syntax = "proto3";
package ga.channel_audio_violation;

import "ga_base.proto";

option go_package = "golang.52tt.com/protocol/app/channel_audio_violation";
option java_package = "com.yiyou.ga.model.proto";
option objc_class_prefix = "RPC";

message IsDetectEnabledRequest {
  ga.BaseReq base_req = 1;
  uint32 cid = 2;
}
message IsDetectEnabledResponse {
  ga.BaseResp base_resp = 1;
  uint32 cid = 2;
  bool enabled = 3;
}

// 违规事件
message ViolationEvent {
  uint64 unix_timestamp = 1;              // 触发违规时间，unix时间戳
  repeated string violation_actions = 2;  // 违规行为，可以有多个
}

message StartJudgmentRequest {
  ga.BaseReq base_req = 1;
  uint32 cid = 2;
  uint32 uid = 3;            // 被审判用户uid
  ViolationEvent event = 4;  // 用户违规事件
}

message StartJudgmentResponse {
  ga.BaseResp base_resp = 1;
  uint32 cid = 2;
  string judgment_uuid = 3;               // 本次审判的uuid
  uint64 server_start_judgment_time = 4;  // 服务端发起审判的unix时间戳
}

enum JudgmentVoteResult {
  JUDGMENT_VOTE_RESULT_UNSPECIFIED = 0;
  JUDGMENT_VOTE_RESULT_YES = 1;              // 有违规
  JUDGMENT_VOTE_RESULT_NO = 2;               // 无违规
  JUDGMENT_VOTE_RESULT_NOT_SURE = 3;         // 不知道
  JUDGMENT_VOTE_RESULT_NOT_PARTICIPATE = 4;  // 不参与投票
}

message JudgmentVoteRequest {
  ga.BaseReq base_req = 1;
  uint32 cid = 2;
  uint32 uid = 3;                 // 投票者uid
  string judgment_uuid = 4;       // 参与审判的uuid
  JudgmentVoteResult result = 5;  // 投票结果
  string reason = 6;              // 不参与投票时填写
}
message JudgmentVoteResponse {
  ga.BaseResp base_resp = 1;
  uint32 cid = 2;
}

message JudgmentAppealRequest {
  ga.BaseReq base_req = 1;
  uint32 cid = 2;
  uint32 uid = 3;
  string judgment_uuid = 4;
  string reason = 5;          // 申诉理由
}
message JudgmentAppealResponse {
  ga.BaseResp base_resp = 1;
  uint32 cid = 2;
}

message ReportAudioFileRequest {
  ga.BaseReq base_req = 1;
  uint32 cid = 2;
  uint32 uid = 3;
  repeated ViolationEvent events = 4;     // 违规事件，一段录音里可能有多个
  string judgment_uuid = 5;               // 审判uuid，上报时可以为空
  string audio_file_url = 6;              // 录音文件url
  uint64 audio_begin_unix_timestamp = 7;  // 开始录音时间，unix时间戳
  uint64 audio_stop_unix_timestamp = 8;   // 停止录音时间，unix时间戳
}
message ReportAudioFileResponse {
  ga.BaseResp base_resp = 1;
  uint32 cid = 2;
}

// 消息推送：房间违规审判投票邀请
message ChannelAudioViolationJudgmentVoteInvitation {
  uint32 cid = 1;
  uint32 uid = 2;                         // 被审判用户uid
  string nickname = 3;                    // 被审判用户名称
  ViolationEvent event = 4;               // 违规事件
  string judgment_uuid = 5;               // 审判uuid
  uint64 server_start_judgment_time = 6;  // 服务端发起审判的unix时间戳
}

enum PunishmentType {
  PUNISHMENT_TYPE_UNSPECIFIED = 0;
  PUNISHMENT_TYPE_NONE = 1;       // 无惩罚,例如投票结果为无违规
  PUNISHMENT_TYPE_KICK_MIC = 2;  // 踢下麦
  PUNISHMENT_TYPE_MUTE = 3;      // 闭麦
}
// 消息推送：房间违规审判结果
message ChannelAudioViolationJudgmentResult  {
  uint32 cid = 1;
  uint32 uid = 2;
  string nickname = 3;             // 违规用户名称
  ViolationEvent event = 4;        // 违规事件
  string judgment_uuid = 5;        // 审判uuid
  bool is_violation = 6;           // 判定结果
  uint32 punishment_duration = 7;  // 惩罚时长，单位秒
  PunishmentType punishment_type = 8; // 惩罚类型
}