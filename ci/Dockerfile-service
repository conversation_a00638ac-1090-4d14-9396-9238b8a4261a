
############################
# build executable binary
############################
FROM registry.ttyuyin.com/quicksilver/go-env as builder

ARG build_path 
ARG output

COPY . /go/src/golang.52tt.com

WORKDIR /go/src/golang.52tt.com

#RUN echo "BUILDING golang.52tt.com/${build_path} --> ${output}"
RUN go build -o /go/bin/${output} ./${build_path}

############################
# build a runtime image
############################
FROM registry.ttyuyin.com/library/ubuntu:16.04

ARG output

ADD cacert.pem /etc/ssl/certs/

COPY --from=builder /go/bin/${output} /${output}

ENV output $output
CMD /$output -v 
