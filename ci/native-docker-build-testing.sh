#!/bin/bash --login

set -e

#source ARTIFACT_CI_UNIT_TEST
source ARTIFACT_CI_JOB
source ci/ci.conf

service_array=${docker_build_services//,/ }

echo "----------------"
echo "docker_build_services:"${docker_build_services}
echo "service_array:"${service_array}
echo `whoami`
echo "----------------"

docker_build_success=""
docker_build_failed=""

docker login ${CI_TESTING_REGISTRY} -u ${CI_TESTING_REGISTRY_USER} -p ${CI_TESTING_REGISTRY_PWD}

echo "docker login ${CI_TESTING_REGISTRY} success"
#docker build -t ${DOCKER_REGISTRY}"/go-env" -f ci/Dockerfile-goenv .

mkdir -p tmp_bin

echo "helm3 repo add ${CHART_REGISTRY_NAME} ${CHART_REGISTRY} --username ${CHART_REGISTRY_USER} --password ${CHART_REGISTRY_PWD}"
helm3 repo add ${CHART_REGISTRY_NAME} ${CHART_REGISTRY} --username ${CHART_REGISTRY_USER} --password ${CHART_REGISTRY_PWD}
helm3 repo update

CHART_REGISTRY_NAME="tt-testing"

function version_gt() { test "$(echo "$@" | tr " " "\n" | sort -V | head -n 1)" != "$1"; }

function select_version() {
  cmd="helm3 search repo ${1} | awk 'tolower(\$1)==tolower(\"${CHART_REGISTRY_NAME}/${1}\"){print \$2}'"
  #echo $cmd
  ver=`eval $cmd`
  if [[ $? -ne 0 ]]; then
      echo "helm3 search repo ${server_name} chart doesn't exist in ${CHART_REGISTRY_NAME}"
      return 1
  fi
  max=""
  if [[ ! -z $ver ]]; then
      max=${ver[0]}
      for v in ${ver[*]}
      do
              if  version_gt $v $max  ;then
                  max=$v
              fi
      done
  fi
  echo $max
  return 0
}

depVersion=`select_version generalServer`
if [[ -z "${depVersion}" ]]; then
    echo "no available generalServer"
    exit 1
fi
echo "generalServer dep version: "$depVersion

function gen_chart_version(){
  year=`date "+%Y"`
  first=`echo $year%2000|bc`
  full="${first}.`date "+%m.%d%H%m"`"
  echo $full
  return 0
}
function build_chart() {
  server_name=${1}
  echo "server_name:"${server_name}
  CHART_VERSION=`gen_chart_version`
  echo "the chart latest version : "$CHART_VERSION
  conf_file="${server_name}.yaml"
  k8s_conf=`find ./kubernetes/testing -name ${conf_file}`
  echo "generating chart ${server_name}:${CHART_VERSION}... "
  if [ -z "${current_version}" ]; then
      current_version=`date "+v%Y%m%d"`
  fi
  echo "./ci/chart-merge.sh ${k8s_conf} ${CHART_VERSION} ${current_version} generalServer ${depVersion}"
  ./ci/chart-merge.sh ${k8s_conf} ${CHART_VERSION} ${current_version} generalServer ${depVersion}
  if [[ $? -ne 0 ]]; then
      exit 1
  fi
}


for name in ${service_array}
do
    set +e
    echo ${unit_test_failed} | grep ${name}","
    if [[ $? -eq 0 ]]
    then
        set -e
        echo ${name}" test failed, skip..."
    else
        set +e
        path=${name//-/_}"_path"
        service_path=`eval echo '$'${path}`
        docker_image_name=${CI_TESTING_REGISTRY}${name}

        cgo_enable=0
        #变量在ci/ci.conf里面，读取到就开启CGO_ENABLE
        array=(${cgo_services//,/ })
        for s in ${array[@]}
        do
           if [[ ${s} == ${name} ]]; then
                export LD_LIBRARY_PATH=~/golib
                cgo_enable=1
                if [[ "${name}" == "tab-notify" ]]; then
                export PKG_CONFIG_PATH=${PWD}"/"${service_path}/pkgconfig
                fi
           fi
        done
        echo "CGO_ENABLED=${cgo_enable} GOOS=linux go build -o ~/tmp_bin/"${name}" ./"${service_path} >> go_build_detail
        CGO_ENABLED=${cgo_enable} GOOS=linux go build -o ./tmp_bin/${name} ./${service_path} >> go_build_detail 2>&1
        if [[ $? -eq 0 ]]
        then
            docker_build_success=${docker_build_success}${name}","
        else
            docker_build_failed=${docker_build_failed}${name}","
            continue
        fi
        set -e

        docker_file_path="ci/Dockerfile-packing"
        if [[ -e ${service_path}/Dockerfile ]]
        then
            docker_file_path=${service_path}/Dockerfile
        fi

        if [[ -e ${service_path}/dockerfile ]]
        then
            docker_file_path=${service_path}/dockerfile
        fi

        echo "docker build -t ${docker_image_name} --build-arg service_path=${service_path} --build-arg source=./tmp_bin/${name} --build-arg output=${name} -f ci/Dockerfile-packing ."
        docker image ls | grep ${docker_image_name} | awk '{print "docker image rm " $3 | "/bin/bash"}'  > /dev/null 2>&1
        if [[ "${name}" == "tab-notify" ]]
        then
            test2=$(pwd)
            echo "${name}, $test2, $docker_file_path"
            docker build -t ${docker_image_name}:${current_version} --build-arg mylibpath=./services/tab-notify/mylib/imagic.conf --build-arg mylib=./services/tab-notify/mylib --build-arg myimg=./services/tab-notify/img --build-arg myfont=./services/tab-notify/font --build-arg source=./tmp_bin/${name} --build-arg output=${name} -f ${docker_file_path} .
        else
            docker build -t ${docker_image_name}:${current_version} --build-arg service_path=${service_path} --build-arg source=./tmp_bin/${name} --build-arg output=${name} -f ${docker_file_path} .
        fi

        docker tag ${docker_image_name}:${current_version} ${docker_image_name}:latest
        docker push ${docker_image_name}:latest
        docker push ${docker_image_name}:${current_version}
        echo "${docker_image_name} build succeed"
        build_chart $name
    fi
done

if [[ ${docker_build_success} != "" ]]
then
    echo "docker build success -> "${docker_build_success}
fi

if [[ ${docker_build_failed} != "" ]]
then
    echo "docker build failed -> "${docker_build_failed}
fi

echo docker_build_job_url=${CI_JOB_URL} > ARTIFACT_CI_DOCKER_BUILD
echo "docker_build_success="${docker_build_success} >> ARTIFACT_CI_DOCKER_BUILD
echo "docker_build_failed="${docker_build_failed} >> ARTIFACT_CI_DOCKER_BUILD