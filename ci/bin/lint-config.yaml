currentWorkDir: ga

lark:
  appID: cli_a48441d3b6b8900e
  appSecret: SHgEThPtAtXXLfqe1FgTqhVpH4H3mjJN
  encryptKey: SG2qSLBNXABdKiOF3kJZ4gKyST0U5mGQ
  verifyToken: lkof60s2jaLoe7eavTAu1e1HWrdKYjMP
  group:
    # chatID: oc_f0dba02f368d5bab07391fe62f628e27
    chatID: oc_584c8b5319461b033e1f62347839e56e
    
ignore:
  ignoreAllRule:
    - grpc_transport_cfg/transport_v2.proto
    - api/extension/extension.proto
    - api/errors/error.proto
    - api/status/status.proto
    - abtest/abtest_.proto
    - activitypush/activity-push/push_.proto
    - activity/activity_.proto
    - ad_center/ad-center-logic_.proto
    - anchor_check_logic/anchor-check-logic_.proto
    - ancient_search/ancient-search_.proto
    - channel-recommend-logic.proto
    - channel_recommend_logic_.proto
    - channel-recommend-logic_.proto
    - channel_recommendlogic.proto
    - app.proto
    - audit/audit/audit_.proto
    - auth/auth.proto
    - avatar_logic/avatar-logic_.proto
    - avatar/avatar.proto
    - backpack/backpack_.proto
    - channel_audio_token/channel-audio-token_.proto
    - channel_cp_game_logic/channel-cp-game-logic_.proto
    - channel_deeplink_recommend_logic/channel-deeplink-recommend-logic_.proto
    - channel_guide_logic/channel-guide-logic_.proto
    - channel_ktv_heartbeat/channel-ktv-heartbeat-logic_.proto
    - channel_ktv/channel-ktv-logic_.proto
    - channel_level/channel-level_.proto
    - channellisteningautoplaylogic/channel-listening-auto-play-logic_.proto
    - channellisteninglogic/channel-listening-logic_.proto
    - channel_live_logic/channel-live-logic_.proto
    - channel_lottery/channel-lottery_.proto
    - channel_mini_game/channel-minigame-go-logic_.proto
    - channel_open_game_controller/channel-open-game-controller-logic_.proto
    - channel_open_game/channel-open-game-logic_.proto
    - channel_performance/channel-performance_.proto
    - channel-play-view_.proto
    - channel-play_.proto
    - channel-quality_.proto
    - channel-red-packet-logic_.proto
    - channel-roleplay-logic_.proto
    - channel_scheme/channel-scheme_.proto
    - channel_scheme/channel-scheme_push_.proto
    - channel-shift-stream-logic_.proto
    - channel-team-logic_.proto
    - channel/channel_.proto
    - channel_background_logic_.proto
    - game/game_.proto
    - channel/channel_opt_.proto
    - channel/channel_personalization_.proto
    - channel/channel_trivia_game_.proto
    - channel/channelguild/guild/guild_.proto
    - chat-card-logic_.proto
    - circle/circle2_.proto
    - circle/circle_.proto
    - client-conf-mgr_.proto
    - client-gen-push_.proto
    - client/client.proto
    - client/client_ipc.proto
    - competition-entrance-logic_.proto
    - concert-logic_.proto
    - contact.proto
    - contract/contract.proto
    - dark-gift-bonus-logic_.proto
    - demo-helloworld-logic.proto
    - emoji_.proto
    - event-report-logic_.proto
    - exchange/exchange.proto
    - face/face.proto
    - feature/feature.proto
    - fellow-logic_.proto
    - find_friends/find_friends_.proto
    - findfriend_matching_.proto
    - online/friendol_.proto
    - ga_base.proto
    - game-screenshot-logic_.proto
    - game_server_v2/game-server-v2_.proto
    - game-tmp-channel_.proto
    - game_.proto
    - game_card_.proto
    - gameradar-logic_.proto
    - giftpkg/giftpkg2_.proto
    - giftpkg/giftpkg_.proto
    - gnobility-logic_.proto
    - group/group_.proto
    - guild-honor-halls-logic_.proto
    - guild/guild2_.proto
    - guild_.proto
    - guild/guildcircle_.proto
    - hctran/hctran.proto
    - hello_.proto
    - helloworld_logic/helloworldlogic_.proto
    - hobby-channel-view_.proto
    - hobby_channel/hobby-channel_.proto
    - hotwordsearch/hotwordsearch_.proto
    - hunt-monster-logic_.proto
    - im-activity-center-logic_.proto
    - im-promote-logic_.proto
    - im.proto
    - interact-proxy-logic_.proto
    - interaction-intimacy-logic_.proto
    - invitelogic_.proto
    - knight-group-logic_.proto
    - knight-privilege-logic_.proto
    - knocklogic_.proto
    - lbs/lbs_.proto
    - levelup-present-logic_.proto
    - magic-spirit-logic_.proto
    - masked-call_.proto
    - masked-pk-logic_.proto
    - master-apprentice-logic_.proto
    - melee_channel/melee-channel-logic_.proto
    - missiongo-logic_.proto
    - molebeat-logic_.proto
    - muse-post-logic_.proto
    - music-nest-logic_.proto
    - music-topic-channel-logic_.proto
    - my_info.proto
    - mystery-box-logic_.proto
    - muse_interest_hub_logic.proto
    - mystery-place-logic_.proto
    - mystery_place_logic/mystery_place_view.proto
    - nameplate_.proto
    - numeric-logic_.proto
    - oauth2-logic_.proto
    - official-live-channel_.proto
    - official_account/official_account.proto
    - one-piece-logic_.proto
    - personal-certification-logic_.proto
    - pgc-channel-pk-logic_.proto
    - pia_.proto
    - playerlogic_.proto
    - present_go_logic/present-go-logic_.proto
    - profile_.proto
    - push-logic_.proto
    - push_.proto
    - push/pushd.proto
    - rap-logic_.proto
    - rcmd_.proto
    - recruit/recruit_.proto
    - redpacket/redpacket_.proto
    - revenue-nameplate-logic_.proto
    - rhythm_.proto
    - rush_.proto
    - sample/sample_.proto
    - session/session_.proto
    - sing-a-round-logic_.proto
    - singing-hall-logic_.proto
    - slip-note-logic_.proto
    - smash-egg-logic_.proto
    - smash-egg-notify-logic_.proto
    - star-trek-logic_.proto
    - super_channel/super-channel-push_.proto
    - super_channel/super-channel_.proto
    - super-player-dress-logic_.proto
    - super-player-logic_.proto
    - sync/sync.proto
    - tab-notify_.proto
    - tbean-logic_.proto
    - team/team_.proto
    - tel-call-logic_.proto
    - group/tgroup_.proto
    - topic_channel_.proto
    - transmission/transmission.proto
    - transport/transport.proto
    - treasure-box-logic_.proto
    - tt-rev-channel-mode-mgr-logic_.proto
    - ttcmdstatictics/statistics.proto
    - udesk-api-logic_.proto
    - ugc_.proto
    - unclaimed_.proto
    - unified_interface/unified-interface_.proto
    - unified_search/unified-search_.proto
    - user_black_list_logic/user-black-list-logic_.proto
    - user-checkin-logic_.proto
    - user-complaint-logic_.proto
    - user-logic_.proto
    - user_music_rank/user-music-rank-logic_.proto
    - user-visitor-record-logic_.proto
    - channel/user_tag_.proto
    - user_tag_v2/user_tag_v2_.proto
    - userblog_.proto
    - userpresent/userpresent_.proto
    - userrecommend/userrecommend_.proto
    - usual-device-logic_.proto
    - revenue/revenue.proto
    - servicepackettail.proto
    - wish-list-logic_.proto
    - you-know-who-logic_.proto
    - pgc-channel-game-logic_.proto
    - grpc_transport_cfg/transport_v2.proto
    - api/extension/extension.proto
    - api/errors/error.proto
    - api/status/status.proto
    - abtest/abtest_.proto
    - activitypush/activity-push_.proto
    - activity/activity_.proto
    - ad_center/ad-center-logic_.proto
    - anchor_check_logic/anchor-check-logic_.proto
    - ancient_search/ancient-search_.proto
    - channel-recommend-logic.proto
    - channel_recommend_logic/channel_recommend_logic_.proto
    - channel-recommend-logic_.proto
    - channel_recommendlogic.proto
    - app/app.proto
    - channel/channel_opt_v2.proto
    - audit/audit_.proto
    - auth/auth.proto
    - avatar_logic/avatar-logic_.proto
    - avatar/avatar.proto
    - backpack/backpack_.proto
    - channel_audio_token/channel-audio-token_.proto
    - channel_cp_game_logic/channel-cp-game-logic_.proto
    - channel/channel_dating_game_.proto
    - channel/channelguild_.proto
    - channel_deeplink_recommend_logic/channel-deeplink-recommend-logic_.proto
    - channel_guide_logic/channel-guide-logic_.proto
    - channel_ktv_heartbeat/channel-ktv-heartbeat-logic_.proto
    - channel_ktv/channel-ktv-logic_.proto
    - channel_level/channel-level_.proto
    - channellisteningautoplaylogic/channel-listening-auto-play-logic_.proto
    - channellisteninglogic/channel-listening-logic_.proto
    - channel_live_logic/channel-live-logic_.proto
    - channel_lottery/channel-lottery_.proto
    - channel_mini_game/channel-minigame-go-logic_.proto
    - channel_open_game_controller/channel-open-game-controller-logic_.proto
    - channel_open_game/channel-open-game-logic_.proto
    - channel_performance/channel-performance_.proto
    - channel_play/channel-play-view_.proto
    - channel_play/channel-play_.proto
    - channel_quality/channel-quality_.proto
    - channel_red_packet_logic/channel-red-packet-logic_.proto
    - channel_roleplay_logic/channel-roleplay-logic_.proto
    - channel_scheme/channel-scheme_.proto
    - channel_scheme/channel-scheme_push_.proto
    - channelshiftstreamlogic/channel-shift-stream-logic_.proto
    - channel_team/channel-team-logic_.proto
    - channel/channel_.proto
    - channelbackgroundlogic/channel_background_logic_.proto
    - channel/channel_opt_.proto
    - channel/channel_personalization_.proto
    - channel/channel_trivia_game_.proto
    - channel/channelguild/guild_.proto
    - chatcardlogic/chat-card-logic_.proto
    - circle/circle2_.proto
    - circle/circle_.proto
    - client_conf_mgr/client-conf-mgr_.proto
    - clientgenpushlogic/client-gen-push_.proto
    - client/client.proto
    - client/client_ipc.proto
    - competition_entrance_logic/competition-entrance-logic_.proto
    - concert_logic/concert-logic_.proto
    - contact/contact.proto
    - contract/contract.proto
    - dark_gift_bonus_logic/dark-gift-bonus-logic_.proto
    - demo_hello_world_logic/demo-helloworld-logic.proto
    - emoji/emoji_.proto
    - eventreportlogic/event-report-logic_.proto
    - exchange/exchange.proto
    - face/face.proto
    - feature/feature.proto
    - fellow_logic/fellow-logic_.proto
    - find_friends/find_friends_.proto
    - find_friends/findfriend_matching_.proto
    - online/friendol_.proto
    - ga_base.proto
    - game_screenshot_logic/game-screenshot-logic_.proto
    - game_server_v2/game-server-v2_.proto
    - game_tmp_channel/game-tmp-channel_.proto
    - game_.proto
    - game_card/game_card_.proto
    - gameradarlogic/gameradar-logic_.proto
    - giftpkg/giftpkg2_.proto
    - giftpkg/giftpkg_.proto
    - gnobilitylogic/gnobility-logic_.proto
    - group/group_.proto
    - guildhonorhallslogic/guild-honor-halls-logic_.proto
    - guild/guild2_.proto
    - guild/guild_.proto
    - guild/guildcircle_.proto
    - hctran/hctran.proto
    - hello/hello_.proto
    - helloworld_logic/helloworldlogic_.proto
    - hobby_channel/hobby-channel-view_.proto
    - hobby_channel/hobby-channel_.proto
    - hotwordsearch/hotwordsearch_.proto
    - huntmonsterlogic/hunt-monster-logic_.proto
    - im_activity_center_logic/im-activity-center-logic_.proto
    - impromotelogic/im-promote-logic_.proto
    - im/im.proto
    - interact_proxy_logic/interact-proxy-logic_.proto
    - interaction_intimacy/interaction-intimacy-logic_.proto
    - invitelogic/invitelogic_.proto
    - knightgrouplogic/knight-group-logic_.proto
    - knightprivilegelogic/knight-privilege-logic_.proto
    - knocklogic/knocklogic_.proto
    - lbs/lbs_.proto
    - levelup_present_logic/levelup-present-logic_.proto
    - magic_spirit_logic/magic-spirit-logic_.proto
    - masked_call/masked-call_.proto
    - masked_pk_logic/masked-pk-logic_.proto
    - masterapprenticelogic/master-apprentice-logic_.proto
    - missiongologic/melee-channel-logic_.proto
    - missiongologic/missiongo-logic_.proto
    - molebeatlogic/molebeat-logic_.proto
    - music_nest_logic/muse-post-logic_.proto
    - music_nest_logic/music-nest-logic_.proto
    - music_topic_channel/music-topic-channel-logic_.proto
    - myinfo/my_info.proto
    - mystery_box_logic/mystery-box-logic_.proto
    - muse_interest_hub_logic/muse_interest_hub_logic.proto
    - mystery_place_logic/mystery-place-logic_.proto
    - mystery_place_view/mystery_place_view.proto
    - oauth2logic/nameplate_.proto
    - numeric_logic/numeric-logic_.proto
    - oauth2logic/oauth2-logic_.proto
    - official_live_channel/official-live-channel_.proto
    - official_account/official_account.proto
    - one_piece_logic/one-piece-logic_.proto
    - personalcertificationlogic/personal-certification-logic_.proto
    - pgc_channel_pk_logic/pgc-channel-pk-logic_.proto
    - pia/pia_.proto
    - playerlogic/playerlogic_.proto
    - present_go_logic/present-go-logic_.proto
    - push_logic/profile_.proto
    - push_logic/push-logic_.proto
    - push/push_.proto
    - pushd.proto
    - rap_logic/rap-logic_.proto
    - rcmd/rcmd_.proto
    - recruit/recruit_.proto
    - redpacket/redpacket_.proto
    - revenuenameplatelogic/revenue-nameplate-logic_.proto
    - rhythm/rhythm_.proto
    - rush/rush_.proto
    - sample/sample_.proto
    - session/session_.proto
    - sing_a_round_logic/sing-a-round-logic_.proto
    - singing_hall_logic/singing-hall-logic_.proto
    - slipnotelogic/slip-note-logic_.proto
    - smash_egg/smash-egg-logic_.proto
    - smash_egg_notify/smash-egg-notify-logic_.proto
    - star_trek_logic/star-trek-logic_.proto
    - super-channel-push/super-channel-push_.proto
    - super-channel/super-channel_.proto
    - super_player_dress_logic/super-player-dress-logic_.proto
    - superplayerlogic/super-player-logic_.proto
    - sync/sync.proto
    - tabnotify/tab-notify_.proto
    - tbeanlogic/tbean-logic_.proto
    - team/team_.proto
    - telcalllogic/tel-call-logic_.proto
    - group/tgroup_.proto
    - topic_channel/topic_channel_.proto
    - transmission/transmission.proto
    - transport/transport.proto
    - treasure_box_logic/treasure-box-logic_.proto
    - tt_rev_channel_mode_mgr_logic/tt-rev-channel-mode-mgr-logic_.proto
    - ttcmdstatictics/statistics.proto
    - udeskapilogic/udesk-api-logic_.proto
    - ugc/ugc_.proto
    - unclaimed/unclaimed_.proto
    - unified_interface/unified-interface_.proto
    - unified_search/unified-search_.proto
    - usercomplaint_logic/user-black-list-logic_.proto
    - usercheckinlogic/user-checkin-logic_.proto
    - usercomplaint_logic/user-complaint-logic_.proto
    - userlogic/user-logic_.proto
    - user_music_rank/user-music-rank-logic_.proto
    - user_visitor_record/user-visitor-record-logic_.proto
    - channel/user_tag_.proto
    - user_tag_v2/user_tag_v2_.proto
    - userblog/userblog_.proto
    - userpresent/userpresent_.proto
    - userrecommend/userrecommend_.proto
    - usual_device_logic/usual-device-logic_.proto
    - revenue/revenue.proto
    - servicepackettail.proto
    - wishlistlogic/wish-list-logic_.proto
    - youknowwhologic/you-know-who-logic_.proto
    - pgc-channel-game-logic/pgc-channel-game-logic_.proto
  ignoreByRule:
    PACKAGE_NO_IMPORT_CYCLE:
      - grpc_transport_cfg/grpc_transport_cfg.proto
      - transport.proto
      - transport/transport.proto
    PACKAGE_DIRECTORY_MATCH:
      - api/channel_game/grpc_channel_game_cpp.proto
      - api/errors/error.proto
      - api/status/status.proto
      - api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto
    PACKAGE_SAME_DIRECTORY:
      - api/auth/grpc_auth.proto
      - api/auth/grpc_auth_cpp.proto
      - api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto
      - mystery_place_logic/mystery_place_view.proto
    PACKAGE_SAME_GO_PACKAGE:
      - api/auth/grpc_auth.proto
      - api/auth/grpc_auth_cpp.proto
      - api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto
    RPC_PASCAL_CASE:
      - api/client_gen_push/grpc_client_gen_push.proto
      - api/channel_ext/grpc_channel_ext.proto
      - api/demo_hello_world/grpc_demo_hello_world.proto
      - api/logic/grpc_logic_cpp.proto
      - api/guild/grpc_guild_cpp.proto
      - api/guild_circle/grpc_guild_circle_cpp.proto
    ENUM_ZERO_VALUE_SUFFIX:
      - muse_social_community_logic/muse_social_community_logic.proto
    RPC_REQUEST_STANDARD_NAME:
      - api/channel_dating_game_logic/grpc_channel_dating_game.proto
      - api/revenue_audio_stream/grpc_revenue_audio_stream.proto
      - api/game_ugc_logic/grpc_game_ugc.proto 
      - api/channel_mic/grpc_channel_mic_go.proto
      - api/guild_go/grpc_guild_go.proto
      - api/group_management/grpc_group_management.proto
      - api/group_management_logic/grpc_group_management_logic.proto
      - api/group-management-logic/grpc_group_management.proto
      - api/backpack_go/grpc_backpack_go.proto
      - api/hotwordsearchlogic_go/grpc_hotwordsearchlogic_go.proto
      - api/muse_social_community_logic/grpc_muse_social_community.proto
      - api/face_recognition/grpc_face_recognition.proto
      - api/channel_rank/grpc_channel_rank.proto
      - api/channel_guild/grpc_channel_guild.proto
      - api/channelguild/grpc_channelguild.proto
      - api/parent_guardian/grpc_parent_guardian.proto
      - api/abtest/grpc_abtest.proto
      - api/account/grpc_account.proto
      - api/activity/grpc_activity_cpp.proto
      - api/ad_center/grpc_ad_center.proto
      - api/anchor_check/grpc_anchor_check.proto
      - api/ancient_search/grpc_ancient_search.proto
      - api/app/grpc_app_cpp.proto
      - api/audit/grpc_audit_cpp.proto
      - api/auth/grpc_auth.proto
      - api/auth/grpc_auth_cpp.proto
      - api/avatar/grpc_avatar.proto
      - api/backpack/grpc_backpack_cpp.proto
      - api/brand/grpc_brand.proto
      - api/channel/grpc_channel_cpp.proto
      - api/channel_audio_token/grpc_channel_audio_token.proto
      - api/channel_background/grpc_channel_background.proto
      - api/channel_convene/grpc_channel_convene_cpp.proto
      - api/channel_core/grpc_channel_core.proto
      - api/channel_cp_game/grpc_channel_cp_game.proto
      - api/channel_deeplink_recommend/grpc_channel_deeplink_recommend.proto
      - api/channel_ext/grpc_channel_ext.proto
      - api/channel_game/grpc_channel_game_cpp.proto
      - api/channel_guide/grpc_channel_guide.proto
      - api/channel_guild/grpc_channel_guild_cpp.proto
      - api/channel_im/grpc_channel_im_cpp.proto
      - api/channel_ktv/grpc_channel_ktv.proto
      - api/channel_ktv_heartbeat/grpc_channel_ktv_heartbeat.proto
      - api/channel_level/grpc_channel_level.proto
      - api/channel_listening/grpc_channel_listening.proto
      - api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto
      - api/channel_live/grpc_channel_live.proto
      - api/channel_live/grpc_channel_live_cpp.proto
      - api/channel_lottery/grpc_channel_lottery.proto
      - api/channel_member/grpc_channel_member.proto
      - api/channel_mic/grpc_channel_mic_cpp.proto
      - api/channel_minigame_go/grpc_channel_minigame_go.proto
      - api/channel_minigames/grpc_channel_minigames_cpp.proto
      - api/channel_mode_mgr/grpc_channel_mode_mgr.proto
      - api/channel_music/grpc_channel_music_cpp.proto
      - api/channel_ol/grpc_channel_ol_cpp.proto
      - api/channel_open_game/grpc_channel_open_game.proto
      - api/channel_open_game_controller/grpc_channel_open_game_controller.proto
      - api/channel_performance/grpc_channel_performance.proto
      - api/channel_play/grpc_channel_play.proto
      - api/channel_quality/grpc_channel_quality.proto
      - api/channel_recommend/grpc_channel_recommend.proto
      - api/channel_red_packet/grpc_channel_red_packet.proto
      - api/channel_roleplay/grpc_channel_roleplay.proto
      - api/channel_scheme/grpc_channel_scheme.proto
      - api/channel_smallgame/grpc_channel_smallgame.proto
      - api/channel_team/grpc_channel_team.proto
      - api/channelext_logic_go/grpc_channelext_logic_go.proto
      - api/chat_card/grpc_chat_card.proto
      - api/circle/grpc_circle_cpp.proto
      - api/client_conf_mgr/grpc_client_conf_mgr.proto
      - api/client_gen_push/grpc_client_gen_push.proto
      - api/collection/grpc_collection.proto
      - api/competition_entrance/grpc_competition_entrance.proto
      - api/concert/grpc_concert.proto
      - api/content/grpc_content.proto
      - api/dark_gift_bonus/grpc_dark_gift_bonus.proto
      - api/demo_hello_world/grpc_demo_hello_world.proto
      - api/download/grpc_download.proto
      - api/emoji/grpc_emoji.proto
      - api/errors/error.proto
      - api/event_report/grpc_event_report.proto
      - api/extension/extension.proto
      - api/fellow/grpc_fellow.proto
      - api/find_friends/grpc_find_friends.proto
      - api/game_card/grpc_game_card.proto
      - api/game_screenshot/grpc_game_screenshot.proto
      - api/game_server_v2/grpc_game_server_v2.proto
      - api/gameradar/grpc_gameradar.proto
      - api/gift_pkg/grpc_gift_pkg_cpp.proto
      - api/gnobility/grpc_gnobility.proto
      - api/group_announcement/grpc_group_announcement.proto
      - api/guild/grpc_guild_cpp.proto
      - api/guild_circle/grpc_guild_circle_cpp.proto
      - api/guild_honor_halls/grpc_guild_honor_halls.proto
      - api/hobby_channel/grpc_hobby_channel.proto
      - api/hotword_search/grpc_hotword_search_cpp.proto
      - api/hunt_monster/grpc_hunt_monster.proto
      - api/im/grpc_im.proto
      - api/im/grpc_im_cpp.proto
      - api/im_activity_center/grpc_im_activity_center.proto
      - api/im_promote/grpc_im_promote.proto
      - api/installed_game/grpc_installed_game.proto
      - api/interact_proxy/grpc_interact_proxy.proto
      - api/interaction_intimacy/grpc_interaction_intimacy.proto
      - api/invite/grpc_invite.proto
      - api/knight_group/grpc_knight_group.proto
      - api/knight_privilege/grpc_knight_privilege.proto
      - api/knock/grpc_knock.proto
      - api/levelup_present/grpc_levelup_present.proto
      - api/logic/grpc_logic_cpp.proto
      - api/magic_spirit/grpc_magic_spirit.proto
      - api/masked_call/grpc_masked_call.proto
      - api/masked_pk/grpc_masked_pk.proto
      - api/master_apprentice_loigc/grpc_master_apprentice_loigc.proto
      - api/melee_channel/grpc_melee_channel.proto
      - api/missiongo/grpc_missiongo.proto
      - api/molebeat/grpc_molebeat.proto
      - api/muse_post/grpc_muse_post.proto
      - api/music_nest/grpc_music_nest.proto
      - api/music_topic_channel/grpc_music_topic_channel.proto
      - api/mystery_box/grpc_mystery_box.proto
      - api/mystery_place/grpc_mystery_place.proto
      - api/nameplate/grpc_nameplate.proto
      - api/numeric/grpc_numeric.proto
      - api/oauth2/grpc_oauth2.proto
      - api/official_live_channel/grpc_official_live_channel.proto
      - api/offline_msg/grpc_offline_msg.proto
      - api/one_piece/grpc_one_piece.proto
      - api/online/grpc_online.proto
      - api/personal_certification/grpc_personal_certification.proto
      - api/pgc_channel_pk/grpc_pgc_channel_pk.proto
      - api/pia/grpc_pia.proto
      - api/player/grpc_player.proto
      - api/present/grpc_present_cpp.proto
      - api/present_go/grpc_present_go.proto
      - api/profile/grpc_profile.proto
      - api/push/grpc_push.proto
      - api/push_event/grpc_push_event.proto
      - api/rap/grpc_rap.proto
      - api/real_name/grpc_real_name_cpp.proto
      - api/realnameauth_go/grpc_realnameauth_go.proto
      - api/revenue_nameplate/grpc_revenue_nameplate.proto
      - api/rush/grpc_rush.proto
      - api/sakura/grpc_sakura.proto
      - api/sing_a_round/grpc_sing_a_round.proto
      - api/singing_hall/grpc_singing_hall.proto
      - api/slip_note/grpc_slip_note.proto
      - api/smash_egg/grpc_smash_egg.proto
      - api/smash_egg_notify/grpc_smash_egg_notify.proto
      - api/star_trek/grpc_star_trek.proto
      - api/super_channel/grpc_super_channel.proto
      - api/super_player/grpc_super_player.proto
      - api/super_player_dress/grpc_super_player_dress.proto
      - api/sync/grpc_sync_cpp.proto
      - api/tab_notify/grpc_tab_notify.proto
      - api/tbean/grpc_tbean.proto
      - api/tel_call/grpc_tel_call.proto
      - api/tgroup/grpc_tgroup_cpp.proto
      - api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto
      - api/topic_channel/grpc_topic_channel.proto
      - api/udesk_api/grpc_udesk_api.proto
      - api/ugc/grpc_ugc_user.proto
      - api/ugc_friendship/grpc_ugc_friendship.proto
      - api/unclaimed/grpc_unclaimed.proto
      - api/unified_interface/grpc_unified_interface.proto
      - api/unified_search/grpc_unified_search.proto
      - api/user/grpc_user.proto
      - api/user_black_list/grpc_user_black_list.proto
      - api/user_checkin/grpc_user_checkin.proto
      - api/user_grow/grpc_user_grow_cpp.proto
      - api/user_music_rank/grpc_user_music_rank.proto
      - api/user_recommend/grpc_user_recommend_cpp.proto
      - api/user_tag/grpc_user_tag_cpp.proto
      - api/user_tag_go/grpc_user_tag_go.proto
      - api/user_visitor_record/grpc_user_visitor_record.proto
      - api/usercomplaint/grpc_usercomplaint.proto
      - api/usual_device/grpc_usual_device.proto
      - api/wish_list/grpc_wish_list.proto
      - api/you_know_who/grpc_you_know_who.proto
      - api/pgc_channel_game/grpc_pgc_channel_game.proto
      - api/rhythm/grpc_micname.proto
      - api/channel_im/grpc_channel_im_go.proto
      - api/channel_recommend_go/grpc_channel_recommend_go.proto
      - api/master_apprentice/grpc_master_apprentice.proto
      - api/sync/grpc_sync.proto
    RPC_RESPONSE_STANDARD_NAME:
      - api/channel_dating_game_logic/grpc_channel_dating_game.proto
      - api/revenue_audio_stream/grpc_revenue_audio_stream.proto
      - api/game_ugc_logic/grpc_game_ugc.proto 
      - api/channel_mic/grpc_channel_mic_go.proto
      - api/guild_go/grpc_guild_go.proto
      - api/group_management/grpc_group_management.proto
      - api/group_management_logic/grpc_group_management_logic.proto
      - api/group-management-logic/grpc_group_management.proto
      - api/backpack_go/grpc_backpack_go.proto
      - api/hotwordsearchlogic_go/grpc_hotwordsearchlogic_go.proto
      - api/muse_social_community_logic/grpc_muse_social_community.proto
      - api/face_recognition/grpc_face_recognition.proto
      - api/channel_rank/grpc_channel_rank.proto
      - api/channelguild/grpc_channelguild.proto
      - api/channel_guild/grpc_channel_guild.proto
      - api/parent_guardian/grpc_parent_guardian.proto
      - api/abtest/grpc_abtest.proto
      - api/account/grpc_account.proto
      - api/activity/grpc_activity_cpp.proto
      - api/ad_center/grpc_ad_center.proto
      - api/anchor_check/grpc_anchor_check.proto
      - api/ancient_search/grpc_ancient_search.proto
      - api/app/grpc_app_cpp.proto
      - api/audit/grpc_audit_cpp.proto
      - api/auth/grpc_auth.proto
      - api/auth/grpc_auth_cpp.proto
      - api/avatar/grpc_avatar.proto
      - api/backpack/grpc_backpack_cpp.proto
      - api/brand/grpc_brand.proto
      - api/channel/grpc_channel_cpp.proto
      - api/channel_audio_token/grpc_channel_audio_token.proto
      - api/channel_background/grpc_channel_background.proto
      - api/channel_convene/grpc_channel_convene_cpp.proto
      - api/channel_core/grpc_channel_core.proto
      - api/channel_cp_game/grpc_channel_cp_game.proto
      - api/channel_deeplink_recommend/grpc_channel_deeplink_recommend.proto
      - api/channel_ext/grpc_channel_ext.proto
      - api/channel_game/grpc_channel_game_cpp.proto
      - api/channel_guide/grpc_channel_guide.proto
      - api/channel_guild/grpc_channel_guild_cpp.proto
      - api/channel_im/grpc_channel_im_cpp.proto
      - api/channel_ktv/grpc_channel_ktv.proto
      - api/channel_ktv_heartbeat/grpc_channel_ktv_heartbeat.proto
      - api/channel_level/grpc_channel_level.proto
      - api/channel_listening/grpc_channel_listening.proto
      - api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto
      - api/channel_live/grpc_channel_live.proto
      - api/channel_live/grpc_channel_live_cpp.proto
      - api/channel_lottery/grpc_channel_lottery.proto
      - api/channel_member/grpc_channel_member.proto
      - api/channel_mic/grpc_channel_mic_cpp.proto
      - api/channel_minigame_go/grpc_channel_minigame_go.proto
      - api/channel_minigames/grpc_channel_minigames_cpp.proto
      - api/channel_mode_mgr/grpc_channel_mode_mgr.proto
      - api/channel_music/grpc_channel_music_cpp.proto
      - api/channel_ol/grpc_channel_ol_cpp.proto
      - api/channel_open_game/grpc_channel_open_game.proto
      - api/channel_open_game_controller/grpc_channel_open_game_controller.proto
      - api/channel_performance/grpc_channel_performance.proto
      - api/channel_play/grpc_channel_play.proto
      - api/channel_quality/grpc_channel_quality.proto
      - api/channel_recommend/grpc_channel_recommend.proto
      - api/channel_red_packet/grpc_channel_red_packet.proto
      - api/channel_roleplay/grpc_channel_roleplay.proto
      - api/channel_scheme/grpc_channel_scheme.proto
      - api/channel_smallgame/grpc_channel_smallgame.proto
      - api/channel_team/grpc_channel_team.proto
      - api/channelext_logic_go/grpc_channelext_logic_go.proto
      - api/chat_card/grpc_chat_card.proto
      - api/circle/grpc_circle_cpp.proto
      - api/client_conf_mgr/grpc_client_conf_mgr.proto
      - api/client_gen_push/grpc_client_gen_push.proto
      - api/collection/grpc_collection.proto
      - api/competition_entrance/grpc_competition_entrance.proto
      - api/concert/grpc_concert.proto
      - api/content/grpc_content.proto
      - api/dark_gift_bonus/grpc_dark_gift_bonus.proto
      - api/demo_hello_world/grpc_demo_hello_world.proto
      - api/download/grpc_download.proto
      - api/emoji/grpc_emoji.proto
      - api/errors/error.proto
      - api/event_report/grpc_event_report.proto
      - api/extension/extension.proto
      - api/fellow/grpc_fellow.proto
      - api/find_friends/grpc_find_friends.proto
      - api/game_card/grpc_game_card.proto
      - api/game_screenshot/grpc_game_screenshot.proto
      - api/game_server_v2/grpc_game_server_v2.proto
      - api/gameradar/grpc_gameradar.proto
      - api/gift_pkg/grpc_gift_pkg_cpp.proto
      - api/gnobility/grpc_gnobility.proto
      - api/group_announcement/grpc_group_announcement.proto
      - api/guild/grpc_guild_cpp.proto
      - api/guild_circle/grpc_guild_circle_cpp.proto
      - api/guild_honor_halls/grpc_guild_honor_halls.proto
      - api/hobby_channel/grpc_hobby_channel.proto
      - api/hotword_search/grpc_hotword_search_cpp.proto
      - api/hunt_monster/grpc_hunt_monster.proto
      - api/im/grpc_im.proto
      - api/im/grpc_im_cpp.proto
      - api/im_activity_center/grpc_im_activity_center.proto
      - api/im_promote/grpc_im_promote.proto
      - api/installed_game/grpc_installed_game.proto
      - api/interact_proxy/grpc_interact_proxy.proto
      - api/interaction_intimacy/grpc_interaction_intimacy.proto
      - api/invite/grpc_invite.proto
      - api/knight_group/grpc_knight_group.proto
      - api/knight_privilege/grpc_knight_privilege.proto
      - api/knock/grpc_knock.proto
      - api/levelup_present/grpc_levelup_present.proto
      - api/logic/grpc_logic_cpp.proto
      - api/magic_spirit/grpc_magic_spirit.proto
      - api/masked_call/grpc_masked_call.proto
      - api/masked_pk/grpc_masked_pk.proto
      - api/master_apprentice_loigc/grpc_master_apprentice_loigc.proto
      - api/melee_channel/grpc_melee_channel.proto
      - api/missiongo/grpc_missiongo.proto
      - api/molebeat/grpc_molebeat.proto
      - api/muse_post/grpc_muse_post.proto
      - api/music_nest/grpc_music_nest.proto
      - api/music_topic_channel/grpc_music_topic_channel.proto
      - api/mystery_box/grpc_mystery_box.proto
      - api/mystery_place/grpc_mystery_place.proto
      - api/nameplate/grpc_nameplate.proto
      - api/numeric/grpc_numeric.proto
      - api/oauth2/grpc_oauth2.proto
      - api/official_live_channel/grpc_official_live_channel.proto
      - api/offline_msg/grpc_offline_msg.proto
      - api/one_piece/grpc_one_piece.proto
      - api/online/grpc_online.proto
      - api/personal_certification/grpc_personal_certification.proto
      - api/pgc_channel_pk/grpc_pgc_channel_pk.proto
      - api/pia/grpc_pia.proto
      - api/player/grpc_player.proto
      - api/present/grpc_present_cpp.proto
      - api/present_go/grpc_present_go.proto
      - api/profile/grpc_profile.proto
      - api/push/grpc_push.proto
      - api/push_event/grpc_push_event.proto
      - api/rap/grpc_rap.proto
      - api/real_name/grpc_real_name_cpp.proto
      - api/realnameauth_go/grpc_realnameauth_go.proto
      - api/revenue_nameplate/grpc_revenue_nameplate.proto
      - api/rush/grpc_rush.proto
      - api/sakura/grpc_sakura.proto
      - api/sing_a_round/grpc_sing_a_round.proto
      - api/singing_hall/grpc_singing_hall.proto
      - api/slip_note/grpc_slip_note.proto
      - api/smash_egg/grpc_smash_egg.proto
      - api/smash_egg_notify/grpc_smash_egg_notify.proto
      - api/star_trek/grpc_star_trek.proto
      - api/super_channel/grpc_super_channel.proto
      - api/super_player/grpc_super_player.proto
      - api/super_player_dress/grpc_super_player_dress.proto
      - api/sync/grpc_sync_cpp.proto
      - api/tab_notify/grpc_tab_notify.proto
      - api/tbean/grpc_tbean.proto
      - api/tel_call/grpc_tel_call.proto
      - api/tgroup/grpc_tgroup_cpp.proto
      - api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto
      - api/topic_channel/grpc_topic_channel.proto
      - api/udesk_api/grpc_udesk_api.proto
      - api/ugc/grpc_ugc_user.proto
      - api/ugc_friendship/grpc_ugc_friendship.proto
      - api/unclaimed/grpc_unclaimed.proto
      - api/unified_interface/grpc_unified_interface.proto
      - api/unified_search/grpc_unified_search.proto
      - api/user/grpc_user.proto
      - api/user_black_list/grpc_user_black_list.proto
      - api/user_checkin/grpc_user_checkin.proto
      - api/user_grow/grpc_user_grow_cpp.proto
      - api/user_music_rank/grpc_user_music_rank.proto
      - api/user_recommend/grpc_user_recommend_cpp.proto
      - api/user_tag/grpc_user_tag_cpp.proto
      - api/user_tag_go/grpc_user_tag_go.proto
      - api/user_visitor_record/grpc_user_visitor_record.proto
      - api/usercomplaint/grpc_usercomplaint.proto
      - api/usual_device/grpc_usual_device.proto
      - api/wish_list/grpc_wish_list.proto
      - api/you_know_who/grpc_you_know_who.proto
      - api/pgc_channel_game/grpc_pgc_channel_game.proto
      - api/rhythm/grpc_micname.proto
      - api/channel_im/grpc_channel_im_go.proto
      - api/channel_recommend_go/grpc_channel_recommend_go.proto
      - api/master_apprentice/grpc_master_apprentice.proto
      - api/sync/grpc_sync.proto
