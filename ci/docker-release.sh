#!/bin/bash

set -e

source ./ARTIFACT_CI_DOCKER_BUILD
source ./ARTIFACT_CI_JOB

array=${docker_build_success//,/ }
#echo "will deploy "${#array[@]}" services..."
echo "build_only_services:"${build_only_services}
echo "prepare deploy:"${docker_build_success}
echo "array:"${array}
echo "branch:"${CI_COMMIT_REF_NAME}

release_failed=""
release_success=""

function version_gt() { test "$(echo "$@" | tr " " "\n" | sort -V | head -n 1)" != "$1"; }

function check_new_service() {
  current_folder=$(
    cd "$(dirname "$0")"
    pwd
  )
  # create tmp chart dir in order to save chart
  if [ ! -d "${current_folder}/tmp_chart" ]; then
    mkdir ${current_folder}/tmp_chart
  fi

  echo "the k8s conf path:"{$2}
  # get chart version
  server_name=$(basename $2 .yaml)
  echo "server_name:"${1}

  # search chart version it will search the all chart about $server_name
  # notice here maybe error when the chart depend on each other it will show all
  ver=$(helm3 search repo $server_name | awk '{if ($2 != "CHART") print $2}')
  if [[ $? -ne 0 ]]; then
    echo "helm search repo ${1} chart doesn't exist in rancher-quicksilver" >&2
    exit 1
  fi

  # init the version
  if [[ "$ver" == "results" ]]; then
    ver="2.0.0"
  fi

  echo "ver str : "$ver

  ver_slice=(${ver//./ })

  curr_latest=${ver_slice[2]}

  curr=$(($curr_latest + 1))

  CHART_VERSION=${ver_slice[0]}"."${ver_slice[1]}"."$curr
  echo "the chart lastest version : "$CHART_VERSION

  k8s_conf=$(dirname "$current_folder")
  echo "helm-util -f ${k8s_conf}/${2} -t ${current_folder}/tmp_chart -vs=${CHART_VERSION}"

  helm-util -f ${k8s_conf}/${2} -t ${current_folder}/tmp_chart -vs=${CHART_VERSION} >&2

  if [[ $? -ne 0 ]]; then
    echo "helm-util download the chart ${1} form {2} fail " >&2
    rm -rf ${current_folder}/tmp_chart/*
    exit 1
  fi

  cd ${current_folder}/tmp_chart

  echo "helm repo update "
  helm3 repo update >&2

  echo "helm dep update"
  helm3 dep update >&2

  echo "helm push . quicksilver"
  helm3 push . quicksilver >&2

  if [[ $? -ne 0 ]]; then
    echo "helm push the lastest chart ${1} version ${curr} fail"
    rm -rf ${current_folder}/tmp_chart/*
    exit 1
  fi

  echo "clear the tmp_chart"
  rm -rf ${current_folder}/tmp_chart/*
  cd ${current_folder}
}
rancher catalog refresh quicksilver
function deploy_to_testing_k8s() {
  # 发布到开发环境集群
  echo ">> deploying [$1] to cluster [testing]"
  version=${current_version:-latest}
  echo "image.tag = $version"
  vs=$(rancher apps st $1)

  if [[ $? -ne 0 ]]; then
    echo -e "skip k8s deploy because ${1} chart doesn't exist in rancher-quicksilver but maybe ""\033[45;37m"$1"\033[0m"" is a new service "
    kubeconfig_file=$(echo kubernetes/develop/${2}/${1}.yaml)
    echo -e "new service find its kubernetes_conf_path: ""\033[45;37m"$kubeconfig_file"\033[0m"
    check_new_service ${1} ${kubeconfig_file}
  fi

  # check again
  if [[ $? -ne 0 ]]; then
    echo "skip k8s deploy because ${1} chart doesn't exist in rancher-quicksilver" >&2
    exit 1
  fi

  if [[ -z "$CHART_VERSION" ]]; then
    echo "choose empty chart_version"
    echo "auto select the latest version in ("$vs")"
    max=${vs[0]}
    for v in ${vs[*]}; do
      if version_gt $v $max; then
        max=$v
      fi
    done
    CHART_VERSION=$max
    echo "selected version [$CHART_VERSION]"
  fi
  appStatus=$(rancher apps sa $1)
  #echo $appStatus
  if [[ $? -eq 0 ]]; then
    echo "$1 exists, upgrade it"
    echo "${appStatus[*]}"
    echo "rancher app upgrade $1 $CHART_VERSION --set general-server.deploy.image.tag=${version} "
    rancher app upgrade $1 $CHART_VERSION --set general-server.deploy.image.tag=${version}
  else
    echo "$1 doesn't exist, install it "
    echo "rancher app install $1 $1 --version $CHART_VERSION --namespace quicksilver --set general-server.deploy.image.tag=${version}"
    rancher app install $1 $1 --version $CHART_VERSION --namespace quicksilver --set general-server.deploy.image.tag=${version}
  fi
  if [ $? -ne 0 ]; then
    echo "rancher failed"
    exit 1
  else
    echo "rancher cmd sucessed"
  fi
}

set +e
python3 ci/pikachu_notify.py $(eval echo ${array})
for name in $(eval echo ${array}); do
  if [[ ${build_only_services} != "" ]]; then
    ignore=0
    build_only=${docker_build_success//,/ }
    for service in $(eval echo ${build_only}); do
      if [[ ${name} == ${service} ]]; then
        ignore=1
        break
      fi
    done

    if [[ ${ignore} -gt 0 ]]; then
      #build only的跳过
      continue
    fi
  fi

  category=${name//-/_}"_category"
  echo "targetEnv: "${targetEnv}
  if [[ ${targetEnv} == "" ]]; then
    if [[ ${CI_COMMIT_REF_NAME} == "master" ]] || [[ ${CI_COMMIT_REF_NAME} =~ ^release.* ]]; then
      #echo  "helm3 --kubeconfig kubernetes/develop/pokemon-kube-config.yaml upgrade --install ${name} kubernetes/charts/general-server -f kubernetes/develop/`eval echo '$'${category}`/${name}.yaml --namespace quicksilver --set deploy.image.tag=${current_version}"
      #helm3 --kubeconfig kubernetes/develop/pokemon-kube-config.yaml upgrade --install ${name} kubernetes/charts/general-server -f kubernetes/develop/`eval echo '$'${category}`/${name}.yaml --namespace quicksilver --set deploy.image.tag=${current_version}
      #echo " rancher app upgrade ${name} 2.0.0 --set general-server.deploy.image.tag=${current_version}"
      #rancher app upgrade ${name} 2.0.0 --set general-server.deploy.image.tag=${current_version}
      yamlPath=$(eval echo '$'${category})
      deploy_to_testing_k8s $name $yamlPath
      CHART_VERSION=""
    elif [[ ${CI_COMMIT_REF_NAME} =~ ^develop.* ]] || [[ ${CI_COMMIT_REF_NAME} =~ ^hotfix.* ]] || [[ ${CI_COMMIT_REF_NAME} =~ ^feature.* ]]; then
      echo "helm3 --kubeconfig kubernetes/develop/develop-kube-config.yaml upgrade --install ${name} kubernetes/charts/general-server -f kubernetes/develop/$(eval echo '$'${category})/${name}.yaml --namespace quicksilver --set deploy.image.tag=${current_version}"
      helm3 --kubeconfig kubernetes/develop/develop-kube-config.yaml upgrade --install ${name} kubernetes/charts/general-server -f kubernetes/develop/$(eval echo '$'${category})/${name}.yaml --namespace quicksilver --set deploy.image.tag=${current_version}
    else
      echo "CI would not deploy ${name} at this branch..."
    fi
  elif [[ ${targetEnv} == "dev" ]]; then
    echo "helm3 --kubeconfig kubernetes/develop/develop-kube-config.yaml upgrade --install ${name} kubernetes/charts/general-server -f kubernetes/develop/$(eval echo '$'${category})/${name}.yaml --namespace quicksilver --set deploy.image.tag=${current_version}"
    helm3 --kubeconfig kubernetes/develop/develop-kube-config.yaml upgrade --install ${name} kubernetes/charts/general-server -f kubernetes/develop/$(eval echo '$'${category})/${name}.yaml --namespace quicksilver --set deploy.image.tag=${current_version}
  elif [[ ${targetEnv} == "testing" ]]; then
    yamlPath=$(eval echo '$'${category})
    deploy_to_testing_k8s $name $yamlPath
    CHART_VERSION=""
  fi

  if [[ $? -eq 0 ]]; then
    release_success=${release_success}${name}","
  else
    release_failed=${release_failed}${name}","
  fi
done

set -e

echo "docker_release_job_url="${CI_JOB_URL} >ARTIFACT_CI_DOCKER_RELEASE
echo "docker_release_success="${release_success} >>ARTIFACT_CI_DOCKER_RELEASE
echo "docker_release_failed="${release_failed} >>ARTIFACT_CI_DOCKER_RELEASE

if [[ ${release_success} != "" ]]; then
  echo "release success -> "${release_success}
fi

if [[ ${release_failed} != "" ]]; then
  echo "release failed -> "${release_failed}
  exit 1
fi
