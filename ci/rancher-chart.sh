#!/bin/bash

set -e
source ./ARTIFACT_CI_JOB
for name in $(eval echo ${array}); do
  category=${name//-/_}"_category"
  kubernetes/develop/$(eval echo '$'${category})/${name}.yaml
done

echo "kubernetes_conf_path_str:"{$kubernetes_conf_path}

current_folder=$(cd "$(dirname "$0")";pwd)
echo "current exec dirname ${current_folder}"
echo "current branch: "${CI_COMMIT_REF_NAME}


if [[ ${CI_COMMIT_REF_NAME} == "develop" ]] || [[ ${CI_COMMIT_REF_NAME} =~ ^hotfix.* ]] || [[ ${CI_COMMIT_REF_NAME} =~ ^feature.* ]]; then
  echo "current branch is ${CI_COMMIT_REF_NAME}"
  exit 0
fi

if [[ -z "$kubernetes_conf_path" ]]; then
    echo "kubernetes conf no change"
    exit 0
fi


# cap the kubernetes_conf_path param and require the k8s config
array=${kubernetes_conf_path//,/ }
echo "array:"${array}

# create tmp chart dir in order to save chart
if [ ! -d "${current_folder}/tmp_chart" ]; then

  mkdir ${current_folder}/tmp_chart
fi

for p in ${array[*]}
do

  echo "the k8s conf path:"{$p}
  # get chart version
  server_name=$(basename $p .yaml)
  echo "server_name:"${server_name}

  # search chart version it will search the all chart about $server_name
  # notice here maybe error when the chart depend on each other it will show all
  ver=`./ci/gen-chart-version.sh`
#  ver=`helm3 search repo $server_name | awk '{if ($2 != "CHART") print $2}'`
#  if [[ $? -ne 0 ]]; then
#      echo "helm search repo ${server_name} chart doesn't exist in rancher-quicksilver" >&2
#      exit 1
#  fi
#
#  # init the version
#  if [[ "$ver" == "results" ]]; then
#      ver="2.0.0"
#  fi
#
#  echo "ver str : "$ver
#
#  ver_slice=(${ver//./ })
#
#  curr_latest=${ver_slice[2]}
#
#  curr=$(($curr_latest+1))

  CHART_VERSION=${ver}
  echo "the chart lastest version : "$CHART_VERSION

  k8s_conf=$(dirname "$current_folder")
  echo "helm-util -f ${k8s_conf}/${p} -t ${current_folder}/tmp_chart -vs=${CHART_VERSION}"

  helm-util -f ${k8s_conf}/${p} -t ${current_folder}/tmp_chart -vs=${CHART_VERSION}  >&2

  if [[ $? -ne 0 ]]; then
      echo "helm-util download the chart ${server_name} form {p} fail " >&2
      rm -rf ${current_folder}/tmp_chart/*
      exit 1
  fi

  cd ${current_folder}/tmp_chart

  echo "helm repo update "
  helm3 repo update >&2

  echo "helm dep update"
  helm3 dep update >&2

  echo "helm push . quicksilver"
  helm3 push . quicksilver >&2

  if [[ $? -ne 0 ]]; then
    echo "helm push the lastest chart ${server_name} version ${curr} fail"
    rm -rf ${current_folder}/tmp_chart/*
    exit 1
  fi

  echo "clear the tmp_chart"
  rm -rf ${current_folder}/tmp_chart/*
  cd ${current_folder}

done

echo "rancher-quicksilver update sucessed"





