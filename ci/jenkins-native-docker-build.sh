#!/bin/bash --login

set -e

#source ARTIFACT_CI_UNIT_TEST
source ARTIFACT_CI_JOB
source ci/ci.conf

service_array=${docker_build_services//,/ }

CI_REGISTRY=registry.ttyuyin.com
CI_REGISTRY_USER=quicksilver
CI_REGISTRY_PASSWORD=Quicksilver0

echo "----------------"
echo "docker_build_services:"${docker_build_services}
echo "service_array:"${service_array}
echo $(whoami)
echo "----------------"

docker_build_success=""
docker_build_failed=""

echo "docker login ${CI_REGISTRY} -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}"
docker login ${CI_REGISTRY} -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}

echo "docker login success"
#docker build -t ${DOCKER_REGISTRY}"/go-env" -f ci/Dockerfile-goenv .

mkdir -p tmp_bin

: >go_build_detail

for name in ${service_array}; do
  set +e
  echo ${unit_test_failed} | grep ${name}","
  if [[ $? -eq 0 ]]; then
    set -e
    echo ${name}" test failed, skip..."
  else
    set +e
    path=${name//-/_}"_path"
    service_path=$(eval echo '$'${path})
    docker_image_name=${CI_REGISTRY}/quicksilver/${name}

    cgo_enable=0
    #变量在ci/ci.conf里面，读取到就开启CGO_ENABLE
    array=(${cgo_services//,/ })
    for s in ${array[@]}; do
      if [[ ${s} == ${name} ]]; then
        export LD_LIBRARY_PATH=~/golib
        cgo_enable=1
        if [[ "${name}" == "tab-notify" ]]; then
          export PKG_CONFIG_PATH=${PWD}"/"${service_path}/pkgconfig
        fi
      fi
    done
    echo "CGO_ENABLED=${cgo_enable} GOOS=linux go build -o ~/tmp_bin/"${name}" ./"${service_path} >>go_build_detail
    #    CGO_ENABLED=${cgo_enable} GOOS=linux go build -o ./tmp_bin/${name} ./${service_path} >>go_build_detail 2>&1
    CGO_ENABLED=${cgo_enable} GOOS=linux go build -o ./tmp_bin/${name} ./${service_path} >>go_build_detail 2>&1

    if [[ $? -eq 0 ]]; then
      docker_build_success=${docker_build_success}${name}","
    else
      docker_build_failed=${docker_build_failed}${name}","
      continue
    fi
    set -e

    docker_file_path="ci/Dockerfile-packing"
    if [[ -e ${service_path}/Dockerfile ]]; then
      docker_file_path=${service_path}/Dockerfile
    fi

    if [[ -e ${service_path}/dockerfile ]]; then
      docker_file_path=${service_path}/dockerfile
    fi

    echo "docker build -t ${docker_image_name}:${current_version} --build-arg service_path=${service_path} --build-arg source=./tmp_bin/${name} --build-arg output=${name} -f ${docker_file_path} ."
    docker image ls | grep ${docker_image_name} | awk '{print "docker image rm " $3 | "/bin/bash"}' >/dev/null 2>&1
    if [[ "${name}" == "tab-notify" ]]; then
      test2=$(pwd)
      echo "${name}, $test2, $docker_file_path"
      docker build -t ${docker_image_name}:${current_version} --build-arg mylibpath=./services/tab-notify/mylib/imagic.conf --build-arg mylib=./services/tab-notify/mylib --build-arg myimg=./services/tab-notify/img --build-arg myfont=./services/tab-notify/font --build-arg source=./tmp_bin/${name} --build-arg output=${name} -f ${docker_file_path} .
    else
      docker build -t ${docker_image_name}:${current_version} --build-arg service_path=${service_path} --build-arg source=./tmp_bin/${name} --build-arg output=${name} -f ${docker_file_path} .
    fi

    docker tag ${docker_image_name}:${current_version} ${docker_image_name}:latest
    docker push ${docker_image_name}:latest
    docker push ${docker_image_name}:${current_version}
  fi

done

if [[ ${docker_build_success} != "" ]]; then
  echo "docker build success -> "${docker_build_success}
fi

rm -f ARTIFACT_CI_FAILED 2> /dev/null

if [[ ${docker_build_failed} != "" ]]; then
  cat go_build_detail
  echo "docker build failed -> "${docker_build_failed}
  echo "docker_build_failed="${docker_build_failed} >ARTIFACT_CI_FAILED
fi

echo "docker_build_success="${docker_build_success} >>ARTIFACT_CI_JOB
echo "docker_build_failed="${docker_build_failed} >>ARTIFACT_CI_JOB
