pipeline {
    agent {
        label 'cicd-test-3'
    }

    environment {
        // 能覆盖的全局变量
        SHOULD_BUILD = false
     }

    options {
        //不允许并行构建
        disableConcurrentBuilds()
        gitLabConnection('jenkins_to_gitlab')
        gitlabBuilds(builds: ['Checkout','fetch'])
    }

    stages {
        stage('Checkout') {
            steps {
                updateGitlabCommitStatus name: 'Checkout', state: 'running'
                checkout changelog: true, poll: true, scm: [
                  $class: 'GitSCM',
                  branches: [[name: "origin/${env.gitlabSourceBranch}"]],
                  doGenerateSubmoduleConfigurations: false,
                  extensions: [[
                    $class: 'PreBuildMerge',
                    options: [
                      fastForwardMode: 'FF',
                      mergeRemote: 'origin',
                      mergeStrategy: 'default',
                      mergeTarget: "${env.gitlabTargetBranch}"
                    ]
                  ]],
                  extensions: [[
                    $class: 'UserIdentity',
                    email: "${env.gitlabUserEmail}",
                    name: "${env.gitlabMergedByUser}"
                  ]],
                  submoduleCfg: [],
                  userRemoteConfigs: [[
//                     credentialsId: "${GIT_CREDENTIALS_ID}",
                    credentialsId: "sys_cicd",
                    name: 'origin',
                    url: "https://gitlab.ttyuyin.com/avengers/quicksilver.git"
                  ]]
                ]
              echo '=============================================='
              sh 'printenv'
              echo '=============================================='
            }
        }

         stage('fetch') {
            steps {
              updateGitlabCommitStatus name: 'Checkout', state: 'success'
              updateGitlabCommitStatus name: 'fetch', state: 'running'
                script{
                    sh "echo iid: ${env.gitlabMergeRequestIid}"
                    def modifiedFiles=sh (script: "python3 ci/get_mr_changes.py ${env.gitlabMergeRequestIid}", returnStdout: true).replace('[','').replace(']','').replace(' ','')
                    echo "modifiedFiles: ${modifiedFiles}"
                    sh "rm -f ARTIFACT_CI_JOB"
                    sh "touch ARTIFACT_CI_JOB"
                    sh "echo pwd = `pwd` "
                    sh "python3 ci/fetchService.py ARTIFACT_CI_JOB ci/inventory.yaml $modifiedFiles"
                    def str=readFile("ARTIFACT_CI_JOB")
                    def sli=str.split("\n")
                    def value = false
                    echo "str = ${sli}"
                    if(sli.length>0 && sli[0]!=""){
                        SHOULD_BUILD = true
                    }
                }
            }
         }

        stage('fetch check'){
          steps{
            updateGitlabCommitStatus name: 'fetch', state: 'success'
          }
        }

        stage('SonarQube Analysis') { // 扫描整个项目的脚本，而不是具体构建的脚本
          when {
             equals expected: true, actual: SHOULD_BUILD
          }
          steps{
            updateGitlabCommitStatus name: 'fetch', state: 'success'
            updateGitlabCommitStatus name: 'SonarQube Analysis', state: 'running'
            script {
              sh "cat ARTIFACT_CI_JOB"
              //sh "source ci/FETCH_CI_JOB"
              def scannerHome = tool 'default-scanner'
              def str=readFile("ARTIFACT_CI_JOB")
              def sli=str.split("\n")
              def value = false
              echo "str = ${sli}"
              if(sli.length>0 && sli[0]!=""){
                  def check_path = ""
                  Map config_map = new HashMap()
                  for (int i = 0; i < sli.length; i++){
                    def tmpLine = sli[i].split("=")
                    if (tmpLine.length>1){
                        echo tmpLine[0]
                        config_map.put(tmpLine[0],tmpLine[1])
                    }
                  }

                  check_path=config_map.get("check_path")
                  echo "check_path: ${check_path}"
                  def pathSlice = []
                  pathSlice=check_path.split(",")
                  echo "pathSlice: ${pathSlice}"
                  withSonarQubeEnv('GlobalSonarQube') {// If you have configured more than one global server connection, you can specify its name
                    echo "WORKSPACE: ${WORKSPACE}"
                    echo "scannerHome: ${scannerHome}"
                    for (cpath in pathSlice){
                        echo "cpath: ${cpath}"
                        def namePath = cpath.split(":")
                        def name = namePath[0]
                        def path = namePath[1]
                        if (namePath.length>1) {
                         sh "${scannerHome}/bin/sonar-scanner \
                                            -Dsonar.projectKey=quicksilver:${name} \
                                            -Dsonar.sources=${path} \
                                            -Dsonar.exclusions=**/*_test.go,**/bin/**,**/ci/**,**/cmd/**,**/doc/**,**/kubernetes/**,**/protocol/**"
                        }
                    }
                  }
              }
            }
          }
        }

        // TODO: 可以根据需要增加其他静态扫描工具

        stage('Unit Test') {  // 执行所有的单元测试，不论需要编译的是哪个目录
          when {
                  equals expected: true, actual: SHOULD_BUILD
          }
          steps {
            updateGitlabCommitStatus name: 'SonarQube Analysis', state: 'success'
            updateGitlabCommitStatus name: 'Unit Test', state: 'running'
            sh 'echo "TODO: unit test~"'
          }
        }

        stage('Build && Build Image') {
          when {
                  equals expected: true, actual: SHOULD_BUILD
          }
          steps {
            updateGitlabCommitStatus name: 'Unit Test', state: 'success'
            updateGitlabCommitStatus name: 'Build && Build Image', state: 'running'
               script {
                    sh "cat ARTIFACT_CI_JOB"
                    def ci_time_st=sh(script: 'git log -1 --pretty=format:"%h,%ad" --date=raw | awk -F, \'{print $2}\'|awk \'{print $1}\'', returnStdout: true).trim()
                    echo "ci_time_st: $ci_time_st"
                    def ci_time=sh(script: "date -d@$ci_time_st \"+%Y%m%d%H%M%S\"", returnStdout: true).trim()
                    echo "ci_time: $ci_time"
                    echo "currentSha: ${env.gitlabMergeRequestLastCommit}"
                    def current_version = (ci_time+"-"+gitlabMergedByUser+"-merge-"+gitlabMergeRequestLastCommit.substring(0,7)).replace('/','-')
                    sh "echo current_version=$current_version >>  ARTIFACT_CI_JOB"
                    sh "echo branch=${env.gitlabSourceBranch} >>  ARTIFACT_CI_JOB"
                    sh "bash ci/jenkins-native-docker-build.sh"
                    sh "if [ -e ARTIFACT_CI_FAILED ];then cat ARTIFACT_CI_FAILED; exit 1 ;fi"
                    sh "sh ci/jenkins-rancher-chart.sh"
              }
          }
        }

        stage('Deploy Test') {
          when {
                  equals expected: true, actual: SHOULD_BUILD
          }
          steps {
            updateGitlabCommitStatus name: 'Build && Build Image', state: 'success'
            updateGitlabCommitStatus name: 'Deploy Test', state: 'running'
            script {
              sh "echo userName=${env.gitlabMergedByUser} >> ARTIFACT_CI_JOB"
              sh "sh ci/jenkins-docker-release.sh"
            }
          }
        }

        stage('Auto Test') {
          when {
                  equals expected: true, actual: SHOULD_BUILD
          }
          steps {
            updateGitlabCommitStatus name: 'Deploy Test', state: 'success'
            updateGitlabCommitStatus name: 'Auto Test', state: 'running'
              sh 'rm -f ci/ARTIFACT_CI_JOB'
              sh 'echo "Auto Test"'
          }
        }

        stage('result') {
            steps{
              updateGitlabCommitStatus name: 'Auto Test', state: 'success'
              updateGitlabCommitStatus name: 'result', state: 'running'
            }
        }
    }

    post {
        failure {
            updateGitlabCommitStatus name: 'result', state: 'failed'
            addGitLabMRComment comment: "Something unexpected happened. Please inspect Jenkins logs."
        }
        success {
            updateGitlabCommitStatus name: 'result', state: 'success'
//             acceptGitLabMR(useMRDescription: true, removeSourceBranch: false)
        }
        aborted {
            updateGitlabCommitStatus name: 'result', state: 'failed'
            addGitLabMRComment comment: "Merge request ${env.gitlabMergeRequestIid} is failured,Please check jenkins logs"

        }
    }
}