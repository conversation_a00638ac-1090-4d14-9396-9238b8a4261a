#!/bin/bash

set -e

#source ARTIFACT_CI_UNIT_TEST
source ARTIFACT_CI_JOB

service_array=${docker_build_services//,/ }

echo "----------------"
echo "docker_build_services:"${docker_build_services}
echo "service_array:"${service_array}
#echo "rebuild_go_mod:"${rebuild_go_mod}
echo "----------------"

#if [[ ${rebuild_go_mod} = "yes" ]]; then
#    echo "go.mod or go.sum had modify, build the new go environment..."
#    /kaniko/executor --context ${CI_PROJECT_DIR} --dockerfile ci/Dockerfile-goenv --destination ${CI_REGISTRY}/quicksilver/go-env:latest --cache
#fi


#declare -A failed_service_map=()
#for name in ${test_failed_service_array} ; do
#    failed_service_map[${name}]="x"
#done


docker_build_success=""
docker_build_failed=""

#docker build -t ${DOCKER_REGISTRY}"/go-env" -f ci/Dockerfile-goenv .

for name in ${service_array}
do
    set +e
    echo ${unit_test_failed} | grep ${name}","
    if [[ $? -eq 0 ]]
    then
        set -e
        echo ${name}" test failed, skip..."
    else
        set -e
        path=${name//-/_}"_path"
        echo "kaniko build name:"${name}",path:"`eval echo '$'${path}`
#        echo "docker build -t ${DOCKER_REGISTRY}${name} --build-arg build_path=${path} --build-arg output=${name} -f ci/Dockerfile-gomods ."
#        docker build -t ${DOCKER_REGISTRY}${name} --build-arg build_path=${path} --build-arg output=${name} -f ci/Dockerfile-gomods .

#        docker run --rm -it -v ${PWD}:/go/src/golang.52tt.com ${DOCKER_REGISTRY}"/go-env" go build -o ${name} .${path}
#        cp ci/Dockerfile-service ${name}"_tmp_file"
#        sed -i s/SERVICE_NAME/${name}/g ${name}"_tmp_file"
#        docker build -t ${DOCKER_REGISTRY}${name} -f ${name}"_tmp_file" .
#        docker push ${DOCKER_REGISTRY}${name}

        /kaniko/executor --context ${CI_PROJECT_DIR} --dockerfile ci/Dockerfile-service --build-arg build_path=`eval echo '$'${path}` --build-arg output=${name} --destination ${CI_REGISTRY}/quicksilver/${name}:latest --destination ${CI_REGISTRY}/quicksilver/${name}:d${CI_COMMIT_SHORT_SHA} --cache=false --snapshotMode=time

        if [[ $? -eq 0 ]]
        then
            docker_build_success=${docker_build_success}${name}","
        else
            docker_build_failed=${docker_build_failed}${name}","
        fi
    fi
done

if [[ ${docker_build_success} != "" ]]
then
    echo "docker build success -> "${docker_build_success}
fi

if [[ ${docker_build_failed} != "" ]]
then
    echo "docker build failed -> "${docker_build_failed}
fi

echo "docker_build_success="${docker_build_success} > ARTIFACT_CI_DOCKER_BUILD
echo "docker_build_failed="${docker_build_failed} >> ARTIFACT_CI_DOCKER_BUILD