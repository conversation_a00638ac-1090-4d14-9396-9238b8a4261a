syntax = "proto3";

package channel_etl;

option go_package = "golang.52tt.com/protocol/services/rcmd/channel/channel_etl";


/*
 * The way to generate golang grpc code
# new version grpc use the following
# protoc --go_out=. --go_opt=paths=source_relative --go-grpc_out=. --go-grpc_opt=paths=source_relative tf-serving.proto

# old version, 2020.01 version, used the following
protoc --go_out=plugins=grpc,paths=source_relative:. tf-serving.proto
*/

message ChannelEventReq {
    uint64 uid  = 1;
    uint64 ctx  = 2;
    string data = 3;
}

message ChannelEventRsp {
    uint64 code = 1;
    string msg  = 2;
    string data = 3;
}

service ChannelETL {

    // grpcurl -plaintext -d '{"uid":435346, "ctx":1,"data": "{\"name\":\"小学生😃勿进\"}"}' localhost:8100 channel_etl.ChannelETL/SendChannelEvent
    // Only for testing
    rpc SendChannelEvent(ChannelEventReq) returns (ChannelEventRsp);

}


message NegativeFeedEvent{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 tag_id = 3;
    string channel_name = 4;
    map<string,int64> channel_name_map=5;
    repeated string channel_name_cut_word=6;
}