syntax = "proto3";

package ugc.interactive;

option go_package = "golang.52tt.com/protocol/services/ugc/interactive";


service Interactive {   
    rpc MarkRead(MarkReadReq) returns (MarkReadResp);
    rpc GetInteractiveUinfo(GetUinfoReq) returns (GetUinfoResp);

    rpc ClearNewFollowCount(ClearNewFollowCountReq) returns (ClearNewFollowCountResp);
    rpc AddFollowCount(AddFollowCountReq) returns (AddFollowCountResp);

    rpc AddUnreadCounts(AddUnreadCountsReq) returns (AddUnreadCountsResp);
    rpc UpdateFollowingStream(UpdateFollowingStreamReq) returns (UpdateFollowingStreamResp);
    
    // DEPRECATED: messages are now stored to activity-stream server
//    rpc GetInteractiveList(GetInteractiveListReq) returns (GetInteractiveListResp);
//    rpc StoreMsg(StoreMsgReq) returns (StoreMsgResp);
//    rpc DeleteMsg(DeleteMsgReq) returns (DeleteMsgResp);
}

enum MsgType {
    NEW_FOLLOW = 0;
    NEW_ATTITUDE = 1;
    NEW_COMMENT = 2;
    NEW_AT = 3;
    NEW_CONCERN = 4;
}

//message StoreMsgReq {
//    uint32 user_id = 1;
//    repeated InteractiveMsg msgs = 2;
//}
//
//message StoreMsgResp {
//
//}

message CommentMsg {
    string post_object_id = 1;
    string comment_object_id = 2;
    string comment_id = 3;
}

message AttitudeMsg {
    string post_object_id = 1;
    string comment_object_id = 2;
    int32 attitude_type = 3;
}

message AtMsg {
    string post_object_id = 1;
    string comment_object_id = 2;
}

message ConcernMsg { // 关心, 催更
}

message InteractiveMsg {
    string id = 1;
    int64 time = 2;
    uint32 user_id = 3;
    uint32 from_user_id = 4;
    MsgType type = 5;
    CommentMsg comment_msg = 6;
    AttitudeMsg attitude_msg = 7;
    AtMsg at_msg = 8;
    ConcernMsg concern_msg = 9;
}

message FollowingStreamUpdateInfo {
    uint64 update_at = 1;                   // 最后更新时间
    repeated uint32 latest_actors = 2;      // 最近活跃的uid列表
}

message Uinfo {
     uint32 user_id = 1;
     string last_read_id = 2;
     int32 new_attitude_count = 3;
     int32 new_comment_count = 4;
     int32 new_follow_count = 5;
     uint32 unread_sequence = 6;    // deprecated
     FollowingStreamUpdateInfo following_stream_update_info = 7;
     int32 new_at_count = 8;
     int32 new_concern_count = 9;

     string last_read_attitude_id = 11;
     int64 last_read_attitude_time = 12;
     string last_read_comment_id = 13;
     int64 last_read_comment_time = 14;
     string last_read_follow_id = 15;
     int64 last_read_follow_time = 16;
     string last_read_at_id = 17;
     int64 last_read_at_time = 18;
     string last_read_concern_id = 19;
     int64 last_read_concern_time = 20;
}

//message GetInteractiveListReq {
//    uint32 user_id = 1;
//    string last_id = 2;
//    int32 limit = 3;
//}
//
//message GetInteractiveListResp {
//    repeated InteractiveMsg info = 1;
//    string last_id = 2;
//}

//message DeleteMsgReq {
//    uint32 user_id = 1;
//    repeated string ids = 2;
//}
//
//message DeleteMsgResp {
//
//}

message MarkReadReq {
    uint32 user_id = 1;
    string id = 2;
    repeated MsgType types = 3;
}

message MarkReadResp {

}

message GetUinfoReq {
    uint32 user_id = 1;
}

message GetUinfoResp {
    Uinfo uinfo = 1;
}

message ClearNewFollowCountReq {
    uint32 user_id = 1;
}

message ClearNewFollowCountResp {

}

message AddFollowCountReq {
    uint32 user_id = 1;
    uint32 count = 2;
}

message AddFollowCountResp {

}

message UpdateFollowingStreamReq {
    repeated uint32 uid_list = 1;
    FollowingStreamUpdateInfo update_info = 2;
}

message UpdateFollowingStreamResp {

}

message AddUnreadCountsReq {
    uint32 user_id = 1;
    
    /* 
        count更新逻辑：
        1. > 0: 增加
        2. < 0: 清0
        3. = 0: 不变
     */
    int32 new_comment_count = 2;
    int32 new_attitude_count = 3;
    int32 new_follower_count = 4;
    int32 new_at_count = 5;
    int32 new_concern_count = 6;
}

message AddUnreadCountsResp {
}
