syntax = "proto3";

package rcmd.rcmd_post_stream.common;

option go_package = "golang.52tt.com/protocol/services/rcmd/rcmd_post_stream/common";

enum Pool{
    PoolUnknown = 0;//不在一个池
    PoolNew = 1;//新池
    PoolNewMid = 2;//新帖中池
    PoolHighInteract = 3;//高互动池
    PoolIndividualize = 4;//个性化池
    PoolHighQuality = 5;//高质池
    PoolIndividualizeHot = 6;//个性化热门兜底池
    PoolIndividualize11 = 7;//个性化池1.1版本
    PoolNewAlgo = 8;//算法版新池，即A池
}

