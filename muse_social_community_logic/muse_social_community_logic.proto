syntax = "proto3";

package ga.muse_social_community_logic;

import "ga_base.proto";
import "music_topic_channel/music-topic-channel-logic_.proto";
import "sync/sync.proto";
import "ugc_non_public/ugc_non_public.proto";


option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/muse-social-community-logic";

message ListMuseSocialCommunityNavBarsRequest{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;//当前房间
  string extra_nav_id = 3;//上一次进入的社群导航Id
  string social_community_id = 4;//social_community_id和group_id之间必须传一个
}

message ListMuseSocialCommunityNavBarsResponse{
  ga.BaseResp base_resp = 1;
  repeated MuseSocialCommunityNavBar bars = 2;
  int64 unread_interact_msg_count = 3;  //未读互动消息总数所有品类圈的都在一起,废弃
  map<uint32, uint32> msg_type_count_map = 4;//0-评论，1-respect&diss
}

// 获取社团助手消息数
message GetSocialCommunityAssistantMsgCountRequest{
  ga.BaseReq base_req = 1;
}
message GetSocialCommunityAssistantMsgCountResponse{
  ga.BaseResp base_resp = 1;
  uint32 msg_count = 2;
}

message ListMuseSocialCommunityNavSecondaryBarsRequest{
  ga.BaseReq base_req = 1;
  string id = 2;
}

message ListMuseSocialCommunityNavSecondaryBarsResponse{
  ga.BaseResp base_resp = 1;
  repeated MuseSocialCommunityNavSecondaryBar secondary_bars = 2;
}

enum MuseSocialCommunityNavBarFlag{
  MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_UNSPECIFIED = 0;
  MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_MINE = 1;
  MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_RECENT = 2;
  MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_TOURIST = 3;
}

message MuseSocialCommunityNavBar{
  string social_community_id = 1;
  string name = 2;
  string logo = 3;
  string bg_logo = 4;   //logo底图
  MuseSocialCommunityNavBarFlag flag = 5;
  string category_type_simple_desc = 6;
  repeated MuseSocialCommunityNavSecondaryBar secondary_bars = 7;//只有当前的bar有值.
  uint32 brand_professionalism = 8; //1-专业，2-非专业
  uint32 level = 9;
  string level_logo = 10;//社团等级logo
  int64  unread_msg_count = 11;   //每个社群的未读消息总数
  Professionalism professionalism_info = 12;
}

message MuseSocialCommunityNavSecondaryBar{
  oneof content{
    MuseSocialCommunityChannel channel = 1;
    MuseSocialCommunityGroup  group = 2;
    MuseSocialCommunityAnnounce  announce = 3;
    MuseSocialCommunityContentStream content_stream = 4;
  }
}

message MuseSocialCommunityContentStream {
  string  content_stream_id = 1;  //流类型的ID 分为干货or讨论or品类圈 指定前缀拼接社群id或品类id拼接讨论或干货
  uint32 stream_type = 2;  //内容流类型    干货or讨论or品类圈 ugc_non_public_.proto SceneStreamType
  string name = 3;
  string logo = 4;
  int64 unread_msg_count = 5;     //未读消息数量
  bool  is_category_circle = 6;  //是否是品类圈  false--不是   ture ----是品类圈（不展示数字）
  uint32 user_permission = 7; // 权限 ugc_non_public.proto ContentStreamPermission 位运算 0001 0010 0100...，0:无权限
  string scene_id = 8;
}

message MuseSocialCommunityAnnounce  {
  string name = 1;
  string desc = 2;
  string logo = 3;
  string lottie = 4;
  string lottie_md5 = 5;
  string bg_color = 6;//"#****"
  int64 unread_msg_count = 7;  //未读通告总数
}

message MuseSocialCommunityChannel {
  uint32 channel_id = 1;
  string name = 2;//不是房间名，是导航栏显示的名称
  string desc = 3;//描述文案
  string logo = 4;
  string lottie = 5;
  string lottie_md5 = 6;
  BrandChannelType channel_type = 7;
  string bg_color = 8;//"#****"
}

message MuseSocialCommunityGroup {
  uint32 group_id = 1;
  string name = 2;//不是群聊名，是导航栏显示的名称
  string desc = 3;//描述文案
  string logo = 4;
  string lottie = 5;
  string lottie_md5 = 6;
  string bg_color = 7;//"#****"
}

enum BrandChannelType {
  BRAND_CHANNEL_TYPE_UNSPECIFIED = 0;
  BRAND_CHANNEL_TYPE_CHAT = 1;
  BRAND_CHANNEL_TYPE_SHOW = 2;
}

message MuseSocialCommunityNavHome{
  string name = 1;
}

message MuseSocialCommunityNavChatChannel{
  uint32 channel_id = 1;
  string name = 2;//不是房间名，是导航栏显示的名称
  string desc = 3;//描述文案
}
message MuseSocialCommunityNavShowChannel{
  uint32 channel_id = 1;
  string name = 2;//不是房间名，是导航栏显示的名称
  bool is_showing = 3;
  string desc = 4;//描述文案
}


/* 用户社群角色 */
enum BrandMemberRoleV2{
  BRAND_MEMBER_ROLE_V2_UNSPECIFIED = 0;
  BRAND_MEMBER_ROLE_V2_CAPTAIN = 1;//主理人
  BRAND_MEMBER_ROLE_V2_KERNEL = 2;//核心成员
  BRAND_MEMBER_ROLE_V2_VICE_CAPTAIN = 3;//副主理人
  BRAND_MEMBER_ROLE_V2_PRODUCER = 4;//制作人
  BRAND_MEMBER_ROLE_V2_FANS = 5;//粉丝
}

message BrandMember{
  uint32 uid = 1;
  string role_text = 2;
  BrandMemberRoleV2 role = 3;
}

/* 获取用户社群角色 */
message BatGetMuseSocialCommunityUsersRoleRequest{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  repeated uint32 uid_list = 3;//不传默认返回社群所有核心成员信息
  string  social_community_id = 4;
}
message BatGetMuseSocialCommunityUsersRoleResponse{
  ga.BaseResp base_resp = 1;
  map<uint32, BrandMember> uid_role_map = 2; /* BrandMemberRoleV2 */
  string category_type_name = 3;    //品类类型短文案
  string social_community_id = 4;

}

message GetSocialCommunityDetailRequest{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  string  social_community_id = 3;
}

message GetSocialCommunityDetailResponse{
  ga.BaseResp base_resp = 1;
  SocialCommunityDetail social_community = 2;
}


message SocialCommunityDetail{
  string social_community_id = 1;       //社群ID
  string social_community_logo = 2;      //社群logo
  string social_community_name = 3;      //社群名称
  string category_name = 4;              //品类名称
  SimpleUserInfo captain = 5;               //主理人名称
  string social_community_intro = 6;     //社群介绍
  uint32 member_count = 7;                //社群所有成员数量
  int32 integral_count = 8;            //社群积分数
  int32 sign_count = 9;                //认证数
  string category_type_simple_desc = 10;
  uint32 brand_professionalism = 11; //1-专业，2-非专业
  string vision = 12;  //愿景
  uint32 level = 13;//社团等级
  string level_logo = 14;//社团等级logo
  string category_id = 15;
  Professionalism professionalism_info = 16;
  int32 respect_count = 17;
  int32 diss_count = 18;
  string respect_toast = 19;
  SocialCommunityBackground social_community_background=20;     //社群背景
}

message JoinSocialCommunityFansRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
  uint32 push_msg_channel_id = 3;   //做推送用的channelID
  uint32 group_id = 4;
  string invite_code = 5; // 邀请码
  bool is_auto_input_invite_code = 6;
}


message JoinSocialCommunityFansResponse{
  ga.BaseResp base_resp = 1;
  repeated SocialSimpleGroup added_groups = 2;
}

/*用户成功加入粉丝团的房间推送*/
message SocialCommunityChannelPush{
  SimpleUserInfo user_info = 1; //用户信息
  string text = 2;        //推送文案
  string social_community_id = 3;//社群Id
  string social_community_name = 4;//社群名称
  string category_type_name = 5;    //品类类型短文案

}

message SimpleUserInfo{
  uint32 uid = 1;
  string account = 2;    // 用户头像
  string nick_name = 3;  // 用户昵称
  uint32 sex = 4;          //用户性别
}

message GetMySocialCommunityRequest{
  ga.BaseReq base_req = 1;
}

message GetMySocialCommunityResponse{
  ga.BaseResp base_resp = 1;
  string  social_community_id = 2;
  string  social_community_name = 3;
  string  social_community_logo = 4;
  uint32  channel_id = 5;
  uint32  member_cnt = 6;
}


message SendWelcomePushRequest{
  ga.BaseReq base_req = 1;
  uint32 to_uid = 2;
  string social_community_id = 3;
}
message SendWelcomePushResponse{
  ga.BaseResp base_resp = 1;
}

message JoinFansWelcomePush{
  SimpleUserInfo from_user_info = 1; //欢迎者
  SimpleUserInfo to_user_info = 2;   //被欢迎者
  string social_community_name = 3;   //社群名称
  string role_name = 4;             //成员角色
  string category_type_name = 5;    //品类类型短文案
}

message GetSocialCommunityProfilePagesRequest{
  ga.BaseReq base_req = 1;
  string     social_community_id = 2;
  uint32 other_user_uid_for_card = 3; //查看别人的社群名片时传入
}

message SocialCommunityMemberInfo{
  SimpleUserInfo user_info = 1;
  string intro = 2; // 成员介绍
  BrandMemberRoleV2  role = 3; /* 角色 BrandMemberRole */
  string member_text = 4;/*角色名*/
}

message SocialCommunityPhotoAlbumKeyURL{
  string key = 1;
  string url = 2;
}

message UserInfoInTheCommunity{
  SimpleUserInfo user_info = 1;
  uint32 joining_duration = 2; //加入时长
  BrandMemberRoleV2  role = 3; /* 角色 BrandMemberRole */
  string member_text = 4;/*角色名*/
  string  count_down_text = 5;/*倒计时天数*/
  string intro = 6; // 成员介绍
  string category_type_simple_desc = 7;// 社团品类类型短文案
  PersonalCertForSocialCommunityUser personal_cert = 8; // 自定义认证标
  string activity_invite_code=9;
}

message PersonalCertForSocialCommunityUser{
  string icon = 1;
  string text = 2;
  repeated string color = 3;
  string text_shadow_color = 4;
}

// 社团成员名片
message SocialCommunityMemberCard{
  SimpleUserInfo user_info = 1;
  string member_text = 2;/*角色名*/
  SocialCommunitySimpleInfo social_community_simple_info = 3; // 社群信息
  MemberIMCardResource resource = 4; // 社团名片的IM卡片资源
}

// 社团名片的IM卡片资源
message MemberIMCardResource{
  string card_bg_small_url = 1; // 名片small背景
  string common_color = 2; // 通用颜色
  string text_color = 3; // 字体颜色
  string text_shadow_color = 4; // 文字投影颜色
}

message SocialCommunitySimpleInfo{
  string social_community_id = 1;       //社群ID
  string social_community_name = 2;      //社群名称
  string category_type_simple_desc = 3;// 社团品类类型短文案
  string social_community_logo=4; //社群logo
}

message InviteMembersDisplay{
  int64 dissolution_countdown = 1;//（社群解散倒计时,秒）
  uint32 invited_members = 2;  //已经邀请的成员数量
  uint32 invite_member_total = 3;//需要邀请的成员总数
}

message GetSocialCommunityProfilePagesResponse{
  ga.BaseResp base_resp = 1;
  repeated MuseSocialCommunityNavSecondaryBar secondary_bars = 2;//二级导航栏
  SocialCommunityDetail  detail_info = 3;  /*社群信息*/
  repeated SocialCommunityMemberInfo  member_list = 4;/*社群核心成员列表*/
  repeated SocialCommunityPhotoAlbumKeyURL photo_list = 5; /*社群相册*/
  string social_community_background_logo = 6;   /*社群logo背景*/ // 如果为空，则取资源包中的 bg_pic_url
  repeated string robot_url = 7;//机器人头像
  string topic_id = 8;//当前厂牌话题的id
  uint32 view_count = 9;//话题热度
  UserInfoInTheCommunity user_info = 10;/*用户自己信息*/
  string in_share_pic_url = 11; /* 社群资料页分享图片url */
  SocialRankHonorSignInfo honor_info = 12; // 榜单（周榜）的荣誉标识
  uint32 social_community_owner_status = 13;//0-没有自己的社团，1-拥有自己的社团，2-自己的社团在审核中
  InviteMembersDisplay invite_member_display = 14;   //邀请成员结构
  uint32 remaining_member_seats = 15; //剩余成员席位
  uint32 system_message_count = 16;
  NewProfileResource resource = 17; // 资源包
  int64 recent_view_count=18;       //最近访问量
  int64 increase_view_count=19;      //新增访问量
  UserInfoInTheCommunity other_user_info_for_card = 20;/*别人的社群名片*/
  MainSubCommunity main_sub_community = 21; // 主分社
}

// 主分社
message MainSubCommunity{
  repeated SimpleSocialCommunityInfo sub_community_list = 1; // 子社群列表
  repeated SimpleSocialCommunityInfo main_community_list = 2; // 主社群列表
}

// 档案页资源包
message NewProfileResource{
  string bg_pic_url = 1; // 社团档案页背景图
  uint32 bg_pic_type = 2;//社团档案页背景图类型 0：image； 1:video
  string bg_pic_first_frame_pic_url = 3;//社团档案页背景图首帧
  string intro_head_pic_url = 4; // 社团介绍头部图
  string photo_title_pic_url = 5; // 社团相册标题图
  string photo_bg_pic_url = 6; // 社团相册底图
  string member_info_bg_pic_url = 7; // 社团身份底图
  string common_color = 8; // 通用颜色
  string text_color = 9; // 字体颜色
  string kernel_member_head_pic_url = 10; // 核心成员墙头部图
  string kernel_member_bg_pic_url = 11; // 核心成员底图
  string user_card_pic_url =12; // 个人卡片底
  string user_card_im_pic_url = 13; // IM个人卡片底
  string text_shadow_color = 14; // 文字投影颜色
}


/* 上榜社群的公演房增加榜单入口 */
message BatGetRankInChannelRequest{
  ga.BaseReq base_req = 1;
  repeated uint32 channel_id_list = 2;
}

enum SocialCommunityRankType{
  SOCIAL_COMMUNITY_RANK_TYPE_UNSPECIFIED = 0;
  SOCIAL_COMMUNITY_RANK_TYPE_WEEK = 1;
  SOCIAL_COMMUNITY_RANK_TYPE_HOUR = 2;
}

message RankInChannelInfo{
  string category_id = 1; // 品类ID
  uint32 rank_type = 2; // SocialCommunityRankType
  string text = 3; // 文案
  string social_community_id = 4; // 社群ID
  string jump_rank_url = 5; // 榜单入口链接
}

message SocialRankHonorSignInfo{
  string icon = 1; /* icon */
  repeated string style_color_list = 2; /* 样式 底色 */
  string text = 3; /* 文案 */
  string category_id = 4; // 品类ID
  uint32 rank_type = 5; // SocialCommunityRankType
  string jump_rank_url = 6; // 榜单入口链接
}

message BatGetRankInChannelResponse{
  ga.BaseResp base_resp = 1;
  map<uint32, RankInChannelInfo> rank_in_channel_info_map = 2;
}

/* 上榜社群的公演房增加榜单入口 推送 */
message RankInChannelNotify{
  uint32 channel_id = 1;
  RankInChannelInfo rank_info = 2;
}


/*社群3.0部分*/

message RemoveSocialCommunityMemberRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
  uint32 target_uid = 3;     //被开除成员
  string reason = 4;      //说明理由
}

message RemoveSocialCommunityMemberResponse{
  ga.BaseResp base_resp = 1;

}

enum MemberStatus{
  MEMBER_STATUS_UNSPECIFIED = 0;
  MEMBER_STATUS_REVOKE = 1;//取消
  MEMBER_STATUS_EXIT = 2;//退出
}

message ExitSocialCommunityRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
  MemberStatus  status = 3;

}

message ExitSocialCommunityResponse{
  ga.BaseResp base_resp = 1;

}

message UpdateMemberRoleRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
  BrandMemberRoleV2 role = 3;
  uint32 target_uid = 4;
}

message UpdateMemberRoleResponse{
  ga.BaseResp base_resp = 1;

}

message GetSocialCommunityRoleLeftNumbersRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
}

message GetSocialCommunityRoleLeftNumbersResponse{
  ga.BaseResp base_resp = 1;
  map<uint32, int32> role_left_numbers_map = 2;    //每种角色剩余数量
  // 0代表已满，-1代表无限制
}

// 获取兴趣讨论区
message GetChannelAssociateSocialCommunityRequest{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  uint32 tab_id = 3;
}

message SimpleSocialCommunityInfo {
  string id = 1;
  string name = 2;
  string logo = 3;
  string category_type_simple_desc=4;
  string category_name=5;
}

message GetChannelAssociateSocialCommunityResponse{
  ga.BaseResp base_resp = 1;
  string icon = 2;
  string text = 3;
  string juml_url = 4;//跳转短链,注意不同app有不同前缀
  repeated SimpleSocialCommunityInfo list = 5; // 合作社群列表
  string partner_prefix_name = 6;  // 合作方（厂牌）前缀名称
bool has_joined_social_community=7;  //判断用户是否加入社群 true已经加入社群
uint32 unread_talk_msg_count=8;        //讨论频道未读消息
string category_type_simple_desc=9;
string social_community_id=10;
}


/*社群5.0*/

message GetMySocialCommunityPageRequest{
  ga.BaseReq base_req = 1;
  string offset_id = 2;
  uint32 limit = 3;
}

message GetMySocialCommunityPageResponse{
  ga.BaseResp base_resp = 1;
  uint32 social_community_owner_status = 2;//0-没有自己的社团，1-拥有自己的社团，2-自己的社团在审核中
  repeated SocialCommunityView channels = 3;
  repeated ga.music_topic_channel.MusicChannel rcmd_channels = 4;//与tt首页结构一致
  string offset_id = 5;
}

enum SocialCommunityOwnerStatus{
  SOCIAL_COMMUNITY_OWNER_STATUS_NONE = 0;
  SOCIAL_COMMUNITY_OWNER_STATUS_HAS = 1;
  SOCIAL_COMMUNITY_OWNER_STATUS_AUDIT = 2;
}

enum BrandProfession{
  BRAND_PROFESSION_NONE = 0;
  BRAND_PROFESSION_PROFESSIONAL = 1;
  BRAND_PROFESSION_AMATEUR = 2;
}

enum SocialCommunityViewStatus{
  SOCIAL_COMMUNITY_VIEW_STATUS_NONE = 0;
  SOCIAL_COMMUNITY_VIEW_STATUS_AUDIT = 1;
  SOCIAL_COMMUNITY_VIEW_STATUS_REJECT = 2;
}

message SocialCommunityView{
  string id = 1;//社区id
  string name = 2;//社群名称
  string logo = 3;//社群logo
  uint32 channel_id = 4;//进入房间id
  string on_show_desc = 5; //文案（xx人正在演出）
  SocialCommunityViewLabel label = 6;//左上角标签
  uint32 brand_professionalism = 7; //1-专业，2-非专业
  uint32 status = 8;//1-审核中，2-审核拒绝
  uint32 group_id = 9;//群聊ID
  bool has_new_message = 10; // 是否要显示小红点
  string on_show_text = 11; // 需要显示文案
  uint32 btn_type = 12; // 按钮类型，see SocialCommunityViewType
  Professionalism professionalism_info = 13;
}

enum SocialCommunityViewType{
  SOCIAL_COMMUNITY_VIEW_TYPE_NONE = 0;
  SOCIAL_COMMUNITY_VIEW_TYPE_AUDIT = 1; // 审核
  SOCIAL_COMMUNITY_VIEW_TYPE_ANNOUNCE = 2; // 通告牌
  SOCIAL_COMMUNITY_VIEW_TYPE_ROOM = 3; // 房间
  SOCIAL_COMMUNITY_VIEW_TYPE_GROUP = 4; // 群聊
}

enum BrandProfessionalism{
  BRAND_PROFESSIONALISM_BRAND_NONE = 0;
  BRAND_PROFESSIONALISM_BRAND_PROFESSIONAL = 1;//专业社团
  BRAND_PROFESSIONALISM_BRAND_AMATEUR = 2;//业余社团
}
message SocialCommunityViewLabel{
  string desc = 1;
  string account = 2;
}

message ListCategoryTypesRequest{
  ga.BaseReq base_req = 1;
}

message ListCategoryTypesResponse{
  ga.BaseResp base_resp = 1;
  repeated SocialCommunityCategoryType category_types = 2;
  string create_social_community_url = 3;
}

message SocialCommunityCategoryType{
  string id = 1;
  string name = 2;
  string intro = 3;
  string logo = 4;
}

message ListCategoriesRequest{
  ga.BaseReq base_req = 1;
  string category_type_id = 2;
}

message ListCategoriesResponse{
  ga.BaseResp base_resp = 1;
  repeated SocialCommunityCategory categories = 2;
}

message SocialCommunityCategory{
  string id = 1;
  string name = 2;
}

message ApplyCreateSocialCommunityRequest{
  ga.BaseReq base_req = 1;
  string name = 2;
  string intro = 3;
  string category_id = 4;
  string logo = 5;
  string vision = 6;
}

message ApplyCreateSocialCommunityResponse{
  ga.BaseResp base_resp = 1;
}

message GetSocialCommunityFloatRequest{
  ga.BaseReq base_req = 1;
}

message GetSocialCommunityFloatResponse{
  ga.BaseResp base_resp = 1;
  string icon = 2;
  string desc = 3;
  string temp_desc = 4;//临时文案，只展示六秒
}

message JoinSocialCommunityRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
}

message JoinSocialCommunityResponse{
  ga.BaseResp base_resp = 1;
  string social_community_id = 2;
}

message ValidateUserHasCreateQualificationRequest{
  ga.BaseReq base_req = 1;
}
message ValidateUserHasCreateQualificationResponse{
  ga.BaseResp base_resp = 1;
}

//message MusicChannel{
//  uint32 channel_id = 1;
//  string channel_name = 2;//房间名
//  uint32 channel_member_count = 3;//在房人数
//
//  string tab_icon = 4;//左上角icon
//  string tab_desc = 5;//左上角文案 e.g. K歌•合唱
//
//  string owner_account = 6;//房主头像
//  int32 owner_sex = 7;//房主性别
//  repeated string accounts = 8;//其他头像
//  string status = 9;//房间状态 e.g. 合唱中
//
//  string song = 10;//当前歌曲
//
//  ga.music_topic_channel.MusicChannelReview review = 11;//重逢
//
//  ga.music_topic_channel.MusicChannelLabel label = 12;//房间标签
//
//  ga.music_topic_channel.KtvGlory glory = 13;//称号
//
//  ga.music_topic_channel.MusicChannelPersonalCert personal_cert = 14;//个人认证标签
//
//  //非业务必须字段，埋点需要
//  string footprint = 15; //推荐trace id
//  uint32 tab_id = 16;//玩法id
//  uint64 region_id = 17;//blockId与elemId的拼接或者你行你唱的tagId,客户端埋点需要
//
//  uint32 channel_level_id = 18;
//
//  //非业务必须字段，埋点需要
//  string tab_name = 19;
//
//  ga.music_topic_channel.MusicPia pia = 20;//pia戏特殊结构，不是pia戏，为空
//  ga.music_topic_channel.MusicInteresting interesting = 21;//兴趣特殊结构，不是兴趣，为空
//
//  string logo = 22;//左边方形图片，优先房主展示
//  ga.music_topic_channel.MusicSocial music_social = 23;//社群
//}
//
//message MusicSocialRankHonorSignInfo{
//  string icon = 1; /* icon */
//  repeated string style_color_list = 2; /* 样式 底色 */
//  string text = 3; /* 文案 */
//}
//
//message ga.music_topic_channel.MusicSocial {
//  string member_label_bg = 2;
//  string member_label_text = 3;
//  ga.music_topic_channel.MusicSocialRankHonorSignInfo rank_sign_info = 4; // 榜单（周榜）的荣誉标识
//}
//
//message ga.music_topic_channel.MusicPia {
//  repeated string label = 1; //剧本标签
//  string name = 2; // 剧本名称
//}
//
//message ga.music_topic_channel.MusicInteresting {
//  string topic = 1; // 话题
//}
//
//enum ga.music_topic_channel.MusicChannelLabel {
//  MusicChannelLabelNone = 0;
//  MusicChannelLabelQuality = 1;//优质
//  MusicChannelLabelHot = 2;//热门
//}
//
////重逢
//message MusicChannelReview{
//  string review_account = 1;//重逢头像
//  string review_desc = 2;//重逢文案
//  int32 review_sex = 3;//重逢用户性别
//}
//
//message ga.music_topic_channel.KtvGlory {
//  string glory_name = 1; // 称号名称
//  string glory_img = 2; // 头标
//  string glory_bg_img = 3; // 背景颜色
//  uint32 glory_rank = 4; // 排行
//}
//
//message MusicChannelPersonalCert{
//  string icon = 1;
//  string text = 2;
//  repeated string color = 3;
//  string text_shadow_color = 4;
//}




/*社群群聊*/



//2.4.1 获取社群基本信息接口
message GetSocialCommunityBaseRequest{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  uint32 group_id = 3;
}

message GetSocialCommunityBaseResponse{
  ga.BaseResp base_resp = 1;
  string social_community_id = 2;
  uint32 level = 3;//社团等级
  string level_logo = 4;//社团等级logo
}


//2.4.2 获取用户当前社群群聊Id
message GetUserSocialGroupIdsRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
  uint32 group_id = 3;//social_community_id,任选其一
}

message GetUserSocialGroupIdsResponse{
  ga.BaseResp base_resp = 1;
  repeated SocialSimpleGroup join_groups = 2;
  SocialSimpleGroup current_group = 3;
}
message SocialSimpleGroup{
  uint32 group_id = 1;
  string group_account = 2;
  string group_name = 3;
  uint32 group_type = 4;
}

// 2.4.3 获取群预览信息
message MuseSocialPreviewGroupMessageRequest{
  ga.BaseReq base_req = 1;
  uint32 group_id = 2;
}

message MuseSocialPreviewGroupMessageResponse{
  ga.BaseResp base_resp = 1;
  repeated ga.sync.NewMessageSync  message = 2;
  uint32 group_id = 3;
  string social_community_id = 4;
  repeated string admin_accounts = 5;
  string group_owner_account = 6;
  repeated BrandMember members = 7;
  string category_type_simple_desc = 8;
}

//移除管理员
message MuseSocialGroupRemoveAdminRequest  {
  ga.BaseReq base_req = 1;
  uint32 group_id = 2;
  uint32 uid = 3;
}

message MuseSocialGroupRemoveAdminResponse  {
  ga.BaseResp base_resp = 1;
  uint32 group_id = 2;
}

//全部禁言
message MuseSocialGroupSetAllMuteRequest {
  ga.BaseReq base_req = 1;
  uint32 group_id = 2;
  bool is_all_mute = 3;
}


message MuseSocialGroupSetAllMuteResponse {
  ga.BaseResp base_resp = 1;
  uint32 group_id = 2;
  bool is_all_mute = 3;
}


//禁言

message MuseSocialGroupMuteMemberRequest{
  ga.BaseReq base_req = 1;
  uint32 group_id = 2;
  repeated uint32 uids = 3;
}

message MuseSocialGroupMuteMemberResponse{
  ga.BaseResp base_resp = 1;
  uint32 group_id = 2;
  repeated uint32 uids = 3;
}


//解除禁言

message MuseSocialGroupUnmuteMemberRequest{
  ga.BaseReq base_req = 1;
  uint32 group_id = 2;
  repeated uint32 uids = 3;
}

message MuseSocialGroupUnmuteMemberResponse{
  ga.BaseResp base_resp = 1;
  uint32 group_id = 2;
  repeated uint32 uids = 3;
}


//禁言列表
message MuseSocialGroupGetMuteListRequest {
  ga.BaseReq base_req = 1;
  uint32 group_id = 2;
  bool get_count = 3; // 总数和详细内容互斥
}

message MuseSocialMuteUserInfo {
  uint32 uid = 1;
  string account = 2;
  string nickname = 3;
}

message MuseSocialGroupGetMuteListResponse {
  ga.BaseResp base_resp = 1;
  uint32 group_id = 2;
  repeated MuseSocialMuteUserInfo mute_list = 3;     // 禁言uid列表
  uint32 count = 4; // 禁言总数
}


//成员列表
//获取群成员列表 BEGIN
message MuseSocialGroupGetMemberListRequest {
  ga.BaseReq base_req = 1;
  uint32 group_id = 2;  //群账号！=群ID
  LoadMoreMembers load_more = 3;
  uint32 count = 4;
  bool admin_only = 5;       // 拉所有管理员
}
message LoadMoreMembers{
  int32 next_position = 1;
}

message MuseSocialGroupGetMemberListResponse {
  ga.BaseResp base_resp = 1;
  string group_account = 2;
  repeated MuseSocialGroupMemberInfo members = 3;
  LoadMoreMembers load_more = 4;
}
message MuseSocialGroupMemberInfo {
  enum Role {
    ROLE_NONE = 0;
    ROLE_OWNER = 1;  // 群主
    ROLE_ADMIN = 2;  // 管理员
    ROLE_COMMON = 3; // 普通成员
  }
  string account = 1;
  uint32 sex = 2;
  string user_nick = 3;
  uint32 role = 4;
  string face_md5 = 5;     // 头像md5
  bool is_muted = 6;    //用户是否被禁言
  uint32 online_status = 7;       // 在线状态 0 离线 1在线
  uint32 online_status_ts = 8;   // 在线状态变化的时间 只有离线用户才会有值 表示最后离线的时间
  BrandMember brand_member = 9; //社群角色
}

//群详情

// 查群组详情
message MuseSocialGroupGetDetailInfoRequest {
  ga.BaseReq base_req = 1;
  uint32 group_id = 2;
}

message MuseSocialGroupGetDetailInfoResponse
{
  ga.BaseResp base_resp = 1;
  MuseSocialGroupDetailInfo detail_info = 2;


}

message MuseSocialGroupDetailInfo {
  uint32 group_id = 2;  //群id
  string group_name = 3;  // 群名称
  uint32 group_mem_count = 4;  // 群成员数
  uint32 group_mem_count_limit = 5;  // 群成员人数上限
  string portrait_md5 = 6;  // 群头像
  uint32 create_time = 7;  // 创建时间
  uint32 group_type = 8; //
  GroupLeader  group_leader = 9;    //群主信息
  Share   share = 10;  //分享信息
  uint32   group_number = 11;//群号
  repeated SimpleUserInfo   users = 12;
}

message Share{
  string social_community_id = 1;
  string social_community_name = 2;
  string social_community_logo = 3;
}

message GroupLeader {
  uint32 uid = 1;
  string account = 2;
  string nick_name = 3;
  string social_community_name = 4;
}


//加入管理员


message MuseSocialGroupAddAdminRequest {
  ga.BaseReq base_req = 1;
  uint32 group_id = 2;
  uint32 uid = 3;
}

message MuseSocialGroupAddAdminResponse {
  ga.BaseResp base_resp = 1;
  uint32 group_id = 2;
}

// 获取群聊在线人数
message GetSocialGroupOnlineMembersRequest{
  ga.BaseReq base_req = 1;
  uint32  group_id = 2;

}


message GetSocialGroupOnlineMembersResponse{
  ga.BaseResp base_resp = 1;
  uint32 member_count = 2;
  uint32  group_id = 3;
  uint32 level = 4;//社团等级
  string level_logo = 5;//社团等级logo

}

message BatGetSocialCommunityKernelMembersRequest{
  ga.BaseReq base_req = 1;
  repeated string social_community_ids = 2;
}

message BatGetSocialCommunityKernelMembersResponse{
  ga.BaseResp base_resp = 1;
  map<string, BrandMembers> kernel_member_map = 2; /* BrandMemberRoleV2 */
}

message BrandMembers{
  repeated BrandMember members = 1;
}



/*获取活跃成员 6.38.0*/

message GetGroupActiveMembersRequest{
  ga.BaseReq base_req = 1;
  uint32 group_id = 2;
}

message MemberDetail{
  uint32 uid = 1;
  string account = 2;    // 用户头像
  string nick_name = 3;  // 用户昵称
  uint32 sex = 4;          //用户性别
  uint32 status = 5;    //在线状态
  string role_text = 6; //角色文案
  uint32 role = 7;//角色
  uint32 channel_id = 8;
  bool channel_is_lock = 9;
}

message SocialCommunityChannelSimple{
  uint32 channel_id = 1;
  string channel_text = 2;
  string member_count_text = 3;
  uint32 channel_type = 4;  //     ---------BrandChannelType

}

message GetGroupActiveMembersResponse{
  ga.BaseResp base_resp = 1;
  repeated MemberDetail member_list = 2;    //成员列表
  repeated SocialCommunityChannelSimple channel = 3; //房间列表
  Share   share = 4;  //分享信息
  uint32 check_in_status = 5;//0-没有打卡状态（不是成员），1-未打卡，2-已打卡
  uint32  check_in_exp = 6;
}

message GetSocialCommunityMemberListRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
  string  offset_id = 3;
  uint32 count = 4;
}

message GetSocialCommunityMemberListResponse{
  ga.BaseResp base_resp = 1;
  repeated MemberDetail kernel_member_list = 2;
  repeated MemberDetail  fans_member_list = 3;
  string  offset_id = 4;
  uint32 user_role = 5;
  int64  kernel_count = 6;
  int64 fans_count = 7;
}

message GetSocialCommunityAnnounceNewsCountRequest{
  ga.BaseReq base_req = 1;
}

message GetSocialCommunityAnnounceNewsCountResponse{
  ga.BaseResp base_resp = 1;
  uint32 num = 2;
}


message ListMuseSocialAnnouncesRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
  uint32 limit = 3;
  string offset_id = 4;
  uint32 list_type = 5;//0-全部，1-自己发布，2-感兴趣的
}

enum ListAnnouncesType{
  LIST_ANNOUNCES_TYPE_ALL = 0;
  LIST_ANNOUNCES_TYPE_MINE = 1;
  LIST_ANNOUNCES_TYPE_INTERESTED = 2;
}

message ListMuseSocialAnnouncesResponse{
  ga.BaseResp base_resp = 1;
  string title = 2;
  repeated MuseSocialAnnounce announces = 3;
  map<uint32, bool> unreaded_list_type_map = 4; //未读list_type
  string offset_id = 5;
}

message MuseSocialAnnounce{
  string id = 1;
  string title = 2;//公告标题
  string content = 3;//内容
  int64 start_time = 4;//开始时间
  int64 end_time = 5;//结束时间
  uint32 interest_count = 6;//感兴趣人数
  bool interested = 7;//用户是否已经感兴趣
  bool is_unreaded = 8;//显示红点用，只有自己发布的未读的为true
  uint32 left_modify_count = 9;//剩余修改次数
  repeated string images = 10;//展示图片
  repeated string image_keys = 11;//图片key
  uint32 creator_uid = 12;
  string creator_account = 13;
  string creator_nick_name = 14;
  uint32 creator_sex = 15;
  BrandMember creator_role = 16;

  MuseAnnounceDestinationChoose destination_choose = 17;

  oneof muse_announce_destination{
    MuseAnnounceNoDestination none_destination = 18;
    MuseAnnounceGroupDestination group_destination = 19;
    MuseAnnounceChatChanelDestination chat_channel_destination = 20;
    MuseAnnounceShowChanelDestination show_channel_destination = 21;
    MuseAnnouncePersonalChanelDestination personal_channel_destination = 22;
  }
}

message MuseAnnounceDestinationChoose{
  string title = 1;
  uint32 destination_type = 2;//0-没有目的地，1-group,2-chat-channel,3-show-channel,4-personal-channel
  uint32 id = 3;//channelId or group id
}

enum MuseAnnounceDestinationType{
  MUSE_ANNOUNCE_DESTINATION_TYPE_NONE = 0;
  MUSE_ANNOUNCE_DESTINATION_TYPE_GROUP = 1;
  MUSE_ANNOUNCE_DESTINATION_TYPE_CHAT_CHANNEL = 2;
  MUSE_ANNOUNCE_DESTINATION_TYPE_SHOW_CHANNEL = 3;
  MUSE_ANNOUNCE_DESTINATION_TYPE_PERSONAL_CHANNEL = 4;
}

message MuseAnnounceNoDestination {

}

message MuseAnnounceShowChanelDestination{
  uint32 channel_id = 1;
  string name = 2;
  string desc = 3;
  repeated string accounts = 4;//小头像
  string channel_md5 = 5;//房间头像md5
}

message MuseAnnounceChatChanelDestination{
  uint32 channel_id = 1;
  string name = 2;
  string desc = 3;
  repeated string accounts = 4;//小头像
  string channel_md5 = 5;//房间头像md5
}

message MuseAnnouncePersonalChanelDestination{
  uint32 channel_id = 1;
  string name = 2;
  string desc = 3;
  repeated string accounts = 4;//小头像
  string channel_md5 = 5;//房间头像md5
}

message MuseAnnounceGroupDestination{
  uint32 group_id = 1;
  string name = 2;
  string desc = 3;
  string logo = 4;
}

message ListAnnounceDestinationsRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
}

message  ListAnnounceDestinationsResponse{
  ga.BaseResp base_resp = 1;
  repeated  MuseAnnounceDestinationGroup   destination_groups = 2;
}

message MuseAnnounceDestinationGroup {
  string title = 1;
  repeated  MuseAnnounceDestinationChoose   destinations = 2;
}

message UpsertMuseSocialAnnounceRequest{
  ga.BaseReq base_req = 1;
  MuseSocialAnnounce announce = 2;
  string social_community_id = 3;
}

message UpsertMuseSocialAnnounceResponse{
  ga.BaseResp base_resp = 1;
}

/*设置感兴趣*/
message SetMuseSocialAnnounceInterestRequest{
  ga.BaseReq base_req = 1;
  string muse_announce_id = 2;
  uint32 interest_type = 3;//0-感兴趣，1-取消感兴趣
}

enum SetInterestType{
  SET_INTEREST_TYPE_INTERESTED = 0;
  SET_INTEREST_TYPE_UNINTERESTED = 1;
}


message SetMuseSocialAnnounceInterestResponse{
  ga.BaseResp base_resp = 1;
}


/*删除通告牌*/
message RemoveMuseSocialAnnounceRequest{
  ga.BaseReq base_req = 1;
  string muse_announce_id = 2;
}

message RemoveMuseSocialAnnounceResponse{
  ga.BaseResp base_resp = 1;
}


/*感兴趣的成员列表*/

message ListMuseSocialAnnounceInterestUsersRequest{
  ga.BaseReq base_req = 1;
  string muse_announce_id = 2;
  uint32 limit = 3;
  string offset_id = 4;
}

message ListMuseSocialAnnounceInterestUsersResponse{
  ga.BaseResp base_resp = 1;
  repeated SimpleUserInfo users = 2;
  string next_offset_id = 3;
}


/*验证用户是否有创建权限*/

message ValidateUserHasCreateAnnouncePermissionsRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
}

message ValidateUserHasCreateAnnouncePermissionsResponse{
  ga.BaseResp base_resp = 1;
}




/*社群加入权限*/


//2.4.1 设置加群模式
message SetCommunityAdditionModeRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
  uint32 addition_mode = 3;
}

message SetCommunityAdditionModeResponse{
  ga.BaseResp base_resp = 1;

}

//主理人获取加群方式
message GetCommunityAdditionModeRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
}
message AdditionMode{
  uint32 addition_mode = 1;
  string text = 2;
}
message GetCommunityAdditionModeResponse{
  ga.BaseResp base_resp = 1;
  uint32 current_addition_mode = 2;
  repeated AdditionMode addition_modes = 3;  /*所有的模式*/
}

//获取通知列表
message ListSocialCommunitySystemMessageRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
  string offset_id = 3;
  uint32 limit = 4;
}

enum SocialCommunitySystemMessageType{
  SOCIAL_COMMUNITY_SYSTEM_MESSAGE_TYPE_NONE = 0;
  SOCIAL_COMMUNITY_SYSTEM_MESSAGE_TYPE_JOIN = 1;
}
message SocialCommunitySystemMessage{
  oneof social_community_message{
    JoinSocialCommunityMessage  join_message = 1;
  }
  uint32 type = 2;
}
message JoinSocialCommunityMessage{
  string id = 1;
  SimpleUserInfo user = 2;
  int64 join_time = 3;
  string reason = 4;
  uint32  status = 5;
}

message ListSocialCommunitySystemMessageResponse{
  ga.BaseResp base_resp = 1;
  repeated SocialCommunitySystemMessage system_message = 2;
  string offset_id = 3;
}


enum AdditionSocialCommunityMode{
  ADDITION_SOCIAL_COMMUNITY_MODE_NONE = 0;
  ADDITION_SOCIAL_COMMUNITY_MODE_APPLY_JOIN = 1;     //成员需要申请加入
  ADDITION_SOCIAL_COMMUNITY_MODE_REJECT_JOIN = 2;    //不允许成员加入
}

enum JoinSocialCommunityMessageStatus{
  JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_NONE = 0;
  JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_REVIEW = 1;  /*未选择状态*/
  JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_PASS = 2;     /*已通过*/
  JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_REJECT = 3;    /*已拒绝*/
  JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_EXPIRE = 4;    /*超时*/
}

//提交加入请求

message SubmitApplicationToJoinCommunityRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
  string reason = 3;
  string invite_code = 4; // 邀请码
  bool is_auto_input_invite_code = 5;
}

message SubmitApplicationToJoinCommunityResponse{
  ga.BaseResp base_resp = 1;
}


//2.4.6 成员申请加入社群的状态变更
message UpsertJoinSocialCommunityMessageStatusRequest{
  ga.BaseReq base_req = 1;
  string id = 2;
  uint32 status = 3;
}

message UpsertJoinSocialCommunityMessageStatusResponse{
  ga.BaseResp base_resp = 1;
}

//获取升级提醒
message GetSocialCommunityUpdateLevelTipRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
}

message GetSocialCommunityUpdateLevelTipResponse{
  ga.BaseResp base_resp = 1;
  string social_community_id = 2;
  string social_community_name = 3;
  string logo = 4;
  SocialCommunityRightGroup right_group = 5;
  string captain_account = 6;
  string captain_nick_name = 7;
  uint32 captain_uid = 8;
  uint32 level = 9;
}

//获取等级详情
message GetSocialCommunityLevelDetailRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
}

message GetSocialCommunityLevelDetailResponse{
  ga.BaseResp base_resp = 1;
  string social_community_id = 2;
  string social_community_name = 3;
  string social_community_logo = 4;
  uint32 professionalism = 5;
  BrandMember member = 6;//用户角色
  SocialCommunityLevelCard level_card = 7;
  SocialCommunityRightGroup right_group = 8;
  repeated SocialCommunityTaskGroup task_groups = 9;
  uint32 member_count = 10;
  Professionalism professionalism_info = 11;
}

message SocialCommunityRightGroup{
  string title = 1;
  repeated SocialCommunityRight social_community_rights = 2;
}

message SocialCommunityLevelCard {
  uint32 level = 1;
  int64 cur_exp = 2;
  int64 next_level_exp = 3;
  string account = 4;
  int64 user_today_exp = 5;
  int64 user_total_exp = 6;
}

message SocialCommunityRight {
  string logo = 1;
  string title = 2;
  string desc = 3;
  uint32 status = 4;//1-生效中，0-上锁中
  bool new_flag = 5;
  string tips = 6;
}

message SocialCommunityTaskGroup{
  string title = 1;
  repeated SocialCommunityTask tasks = 7;
}

message SocialCommunityTask {
  oneof social_community_task_view{
    SocialCommunityNormalTaskView normal_task_view = 1;
    SocialCommunityCheckInTaskView check_in_task_view = 2;
    SocialCommunityStepTaskView step_task_view = 3;
  }

  uint32 status = 5;//0-未完成，1-已完成
  uint32 action_type = 6;//0-不需要点击，1-url,2-check_in
  string action_url = 7;
  repeated string bg_colors = 8;
  uint32 view_type = 9;
}

enum TaskViewType{
  TASK_VIEW_TYPE_NORMAL = 0;
  TASK_VIEW_TYPE_CHECK_IN = 1;
  TASK_VIEW_TYPE_STEP = 2;
}

message SocialCommunityNormalTaskView {
  int64 award_exp = 1;//奖励经验值，用于左侧图标显示
  string title = 2;
  string desc = 3;
  string active_desc = 4;
  string button_text = 5;
}

message SocialCommunityCheckInTaskView {
  int64 award_exp = 1;//奖励经验值，用于左侧图标显示
  string title = 2;
  string desc = 3;
  string active_desc = 4;
  map<uint32, string> status_button_text = 5;//已完成，和未完成文案
}

message SocialCommunityStepTaskView {
  repeated SocialCommunityStep steps = 1;
  string title = 2;
  string button_text = 3;
  int64 score = 4;//目前获得值
  string desc = 5;
  string tips = 6;
}

message SocialCommunityStep{
  int64 award_exp = 1;//奖励经验值
  string desc = 2;
  int64 score = 3;//到这step需要值
}

message SocialCommunityCheckInRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
}

message SocialCommunityCheckInResponse{
  ga.BaseResp base_resp = 1;
}

/* 群聊任务完成公告 */
message GroupMessageTaskDoneNotify{
  ga.sync.NewMessageSync  message = 1;
  string task_id = 2;
  string desc = 3;
}

message GetSocialCommunityContentStreamNewsCountRequest{
  ga.BaseReq base_req = 1;
  bool need_default_community_stream = 2;

}

message GetSocialCommunityContentStreamNewsCountResponse{
  ga.BaseResp base_resp = 1;
  int64 sum = 2;
  bool  has_category_circle_msg = 3;  //是否有品类圈消息  false --没有品类圈消息或其他消息数不为0 --true 有品类圈新消息，且其他消息总数为0
  SocialCommunityContentStream default_content_stream = 4;
}

message SocialCommunityContentStream{
  string social_community_id = 1;
  string scene_id = 2;
  string stream_id = 3;
  uint32 stream_type = 4;
  string social_name = 5;
  string social_logo = 6;
}

message Professionalism{
  uint32 brand_professionalism = 1;
  string picture_url = 2;
  string text = 3;
}

message GetSocialCommunityContentStreamRequest{
  ga.BaseReq base_req = 1;
  string post_id = 2;
  string category_id = 3;
}

message GetSocialCommunityContentStreamResponse{
  ga.BaseResp base_resp = 1;
  SocialCommunityContentStream stream = 2;
}

message ListMuseSocialCommunityCommentMessageRequest{
  ga.BaseReq base_req = 1;
  string offset_id = 2;
  uint32 limit = 3;
}

message ListMuseSocialCommunityCommentMessageResponse{
  ga.BaseResp base_resp = 1;
  repeated SocialCommentMessage msgs = 2;
  map<uint32, uint32> msg_type_count_map = 3;//0-评论，1-respect&diss
  bool is_done = 4;
}

message SocialCommentMessage {
  string comment_id = 1;//自身评论id
  uint32 user_id = 2;
  string account = 3;//头像
  uint32 sex = 4;//性别
  string nick_name = 5;
  string post_id = 6;
  string reply_to_comment_id = 7;
  ga.ugc_non_public.NonPublicCommentInfo comment = 8; // 评论
  ga.ugc_non_public.NonPublicCommentInfo reply_to_comment = 9; // 被评论的评论
  ga.ugc_non_public.NonPublicPostInfo post = 10; // 帖子的富文本内容
  string scene_id = 11;
  MuseUserRole role = 12;
  string offset_id = 13;
  int64 create_at = 14;
  string stream_id = 15;
  uint32 stream_type = 16;
  string social_community_id = 17;
  bool  has_at_msg=18;  //是否有at消息
}

message MuseUserRole{
  string social_community_id = 1;
  string role_text = 2; // 身份名称
  repeated string text_color = 3;
  repeated string background_color = 4;
}

message ListMuseSocialCommunityAttitudeMessageRequest{
  ga.BaseReq base_req = 1;
  string offset_id = 2;
  uint32 limit = 3;
}

message ListMuseSocialCommunityAttitudeMessageResponse{
  ga.BaseResp base_resp = 1;
  repeated SocialAttitudeMessage msgs = 2;
  map<uint32, uint32> msg_type_count_map = 3;//0-评论，1-respect&diss
  bool is_done = 4;
}

message SocialAttitudeMessage {
  string post_id = 1;
  string comment_id = 2;
  uint32 user_id = 3;
  string account = 4;//头像
  uint32 sex = 5;//性别
  string nick_name = 6;
  uint32 attitude_type = 7;
  uint32 target_user_id = 8;
  uint32 step_on_type = 9;
  string scene_id = 10;
  ga.ugc_non_public.NonPublicCommentInfo reply_to_comment = 11; // 被评论的评论
  ga.ugc_non_public.NonPublicPostInfo post = 12; // 帖子
  MuseUserRole role = 13;
  string offset_id = 14;
  int64 create_at = 15;
  string stream_id = 16;
  uint32 stream_type = 17;
  string social_community_id = 18;
}

message GetSocialCommunityNonPublicUserCardRequest{
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  string social_community_id = 3;

}

message GetSocialCommunityNonPublicUserCardResponse{
  ga.BaseResp base_resp = 1;
  ga.ugc_non_public.CommonUserUGCInfo common_user = 2;
  ga.ugc_non_public.SocialCommunityUserUGCInfo social_community_user = 3; // 社群 扩展信息
  int32 respect_count = 4;
  int32 diss_count = 5;
  string respect_toast = 6;
}


message IntroduceSocialCommunityByCategoryIdRequest{
  ga.BaseReq base_req = 1;
  string category_id = 2;
}

message IntroduceSocialCommunityByCategoryIdResponse{
  ga.BaseResp base_resp = 1;
  string social_community_id = 2;
}

message BatchMuseSocialCommunityNavBarsV2Request{
  ga.BaseReq base_req = 1;
  repeated string social_community_ids = 2;
}

message BatchMuseSocialCommunityNavBarsV2Response{
  ga.BaseResp base_resp = 1;
  map<string, MuseSocialCommunityNavBarV2>  bars = 2;
  map<uint32, uint32> msg_type_count_map = 3;//0-评论，1-respect&diss
}

message ListMuseSocialCommunityNavBarsV2Request{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
}

message ListMuseSocialCommunityNavBarsV2Response{
  ga.BaseResp base_resp = 1;
  repeated MuseSocialCommunityNavBarV2 bars = 2;
  map<uint32, uint32> msg_type_count_map = 3;//0-评论，1-respect&diss, 3-社团助手消息 大于0则有红点
}

message MuseSocialCommunityNavBarV2{
  string social_community_id = 1;
  string name = 2;
  string logo = 3;
  string bg_logo = 4;   //logo底图
  string category_type_simple_desc = 5;
  Professionalism professionalism_info = 6;
  uint32 level = 7;
  string level_logo = 8;//社团等级logo
  bool has_msg_red_dot = 9;   //社群红点消息
  string channel_status_logo = 10;   //房间状态图片
  repeated MuseSocialCommunityNavSecondaryBarV2 secondary_bars = 11;//只有当前的bar有值
  BrandMember member = 12;//用户角色
  string category_id = 13; //品类id
}

message MuseSocialCommunityNavSecondaryBarV2{
  uint32 bar_type = 1;//0-品类圈，1-讨论，2-干货，3-房间，4-群聊，5-通告牌
  string text = 2;
  map<uint32, string> status_logo = 3;//0-未选中，1-选中,可能是静态图，可能是动态图
  uint32 un_read_msg_count = 4;
  bool has_msg_red_dot = 5;   //社群红点消息
  MuseSocialCommunityNavSecondaryBarV2ContentExtra extra = 6;//目前只有品类，讨论，干货有额外参数
}

enum SecondaryBarType{
  SECONDARY_BAR_TYPE_CATEGORY = 0;//品类圈
  SECONDARY_BAR_TYPE_TALK = 1;//讨论
  SECONDARY_BAR_TYPE_KNOWLEDGE = 2;//干货
  SECONDARY_BAR_TYPE_CHANNEL = 3;//房间
  SECONDARY_BAR_TYPE_GROUP = 4;//群聊
  SECONDARY_BAR_TYPE_ANNOUNCE = 5;//通告牌
}

message MuseSocialCommunityNavSecondaryBarV2ContentExtra{
  oneof content_extra {
    MuseSocialCommunityStreamContent stream_permission = 1;
  }
}

message MuseSocialCommunityStreamContent{
  uint32 user_permission = 1; // 权限 ugc_non_public.proto ContentStreamPermission 位运算 0001 0010 0100...，0:无权限
  string scene_id = 2;
  string stream_id = 3;
}

message ListMuseSocialCommunityNavSecondaryBarsV2Request{
  ga.BaseReq base_req = 1;
  string id = 2;
}

message ListMuseSocialCommunityNavSecondaryBarsV2Response{
  ga.BaseResp base_resp = 1;
  repeated MuseSocialCommunityNavSecondaryBarV2 secondary_bars = 2;
  string category_id = 3; //品类id
}

message ListMuseSocialCommunityGroupsRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
}

message ListMuseSocialCommunityGroupsResponse{
  ga.BaseResp base_resp = 1;
  repeated MuseSocialGroup groups = 2;
}

message MuseSocialGroup {
  uint32 group_id = 1;
  string logo = 2;
  string name = 3;
  string desc = 4;
}

message UpdateSocialCommunityInfoRequest{
  ga.BaseReq base_req = 1;
  string social_community_id=2;
  repeated SocialCommunityAttribute attribute =3;
}
message SocialCommunityAttribute{
  oneof social_community_attribute{
    string social_community_name=1;       //社群名
    string social_community_logo=2;       //社群logo
    SocialCommunityBackgroundInfo social_community_background=3;     //社群背景
    SocialCommunityVisionInfo  social_community_vision =4;  //社群愿景
  }
}

message SocialCommunityVisionInfo{
  string social_community_vision =1;  //社群愿景
  bool  is_overwrite_update=2;   //true 覆盖操作  false  不做操作
}

message SocialCommunityBackgroundInfo{
    SocialCommunityBackground background =1;
    bool  is_overwrite_update=2;   //true 覆盖操作  false  不做操作
}
message SocialCommunityBackground{
  string social_community_background_url=1;     //社群背景
  uint32 social_community_background_type=2;  //类型
  string social_community_first_frame=3; //首帧图片
}
enum SocialCommunityBackgroundType{
  SOCIAL_COMMUNITY_BACKGROUND_TYPE_NONE=0;
  SOCIAL_COMMUNITY_BACKGROUND_TYPE_IMAGE=1;  //图片背景
  SOCIAL_COMMUNITY_BACKGROUND_TYPE_VIDEO=2;   //视频背景
}
message UpdateSocialCommunityInfoResponse{
  ga.BaseResp base_resp = 1;
}


message GetSocialCommunityEditableInfoRequest{
  ga.BaseReq base_req = 1;
  string social_community_id=2;
}

message GetSocialCommunityEditableInfoResponse{
  ga.BaseResp base_resp = 1;
  string social_community_id=2;
  string social_community_name=3;
  string social_community_logo=4;
  SocialCommunityBackground   social_community_background=5;
  repeated SocialCommunityPhotoAlbumKeyURL photo_list = 6; /* 厂牌相册 */
  string social_community_intro=7;
  string social_community_vision =8;  //社群愿景
  string category_type_simple=9; //社团品类短文案
  Professionalism brand_professionalism = 10;//社群类型（专业社群or普通社群）
  bool has_update_permission=11; //是否有权限编辑名字
  int64   config_update_times=12;      //次数
}

enum PostPushMessageType{
  POST_PUSH_MESSAGE_TYPE_NONE = 0;
  POST_PUSH_MESSAGE_TYPE_NON_PUBLIC_POST = 1; // 讨论帖
}
// 社团助手的讨论帖推送消息
message SocialCommunityAssistantPostPushMessage{
  string social_community_id = 1;
  string post_id = 2;
  uint32 post_msg_type = 3; // PostPushMessageType
  string prefix_text = 4; // [热门讨论]
  string prefix_text_color = 5;
  string content_text = 6; // 帖子标题
  string jump_url = 7; // 跳转短链
  uint32 bar_type = 8;// SecondaryBarType
}

message ReportPersonalChannelViewSocialCommunityRequest{
  ga.BaseReq base_req = 1;
  uint32 channel_id=2;
  string social_community_id=3;

}

message ReportPersonalChannelViewSocialCommunityResponse{
  ga.BaseResp base_resp = 1;
}


message GetMemberStatusInTheSocialCommunityRequest{
  ga.BaseReq base_req = 1;
  uint32 uid=2;                      //要查询的用户的uid
  string social_community_id=3;
}

message GetMemberStatusInTheSocialCommunityResponse{
  ga.BaseResp base_resp = 1;
  uint32 member_status=2; //用户在社群中的状态（0--未定义（未加入社群），1--审核中  2--已加入社群）
}

enum  UserStatusInTheSocialCommunity{
  USER_STATUS_IN_THE_SOCIAL_COMMUNITY_UNSPECIFIED=0;
  USER_STATUS_IN_THE_SOCIAL_COMMUNITY_AUDITING=1;
  USER_STATUS_IN_THE_SOCIAL_COMMUNITY_JOINED=2;
}

message GetSocialCommunityInvitationCodeDetailRequest {
  ga.BaseReq base_req = 1;
  string invitation_code = 2;
  bool is_auto_input_code = 3;
  string social_community_id = 4;
}

message GetSocialCommunityInvitationCodeDetailResponse {
  ga.BaseResp base_resp = 1;
  SimpleUserInfo invite_user = 2;
  SocialCommunitySimpleInfo social_community_info = 3;
}

message GetSocialCommunityInvitationCodeShareTextRequest {
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  string social_community_id = 3;
  string long_url = 4;
  string tt_url = 5;
  string background_url = 6;
  string invitation_code = 7;
  string content = 8;
}

message GetSocialCommunityInvitationCodeShareTextResponse {
  ga.BaseResp base_resp = 1;
  string content = 2; // 整个的文本内容
  string short_url = 3; // 短连接
  string pass_code = 4; // 口令码
}

message SocialCommunitySharePreCheckRequest {
  ga.BaseReq base_req = 1;
  int32 source = 2; // 1 朋友圈 2 微信 3 qq 4 qq空间 5 复制口令 6 复制链接 7 TT好友
  string invitation_code = 3;
}

message SocialCommunitySharePreCheckResponse {
  ga.BaseResp base_resp = 1;
  bool hit_risk = 2;
}

message CheckSocialCommunityInvitationUserRequest {
  ga.BaseReq base_req = 1;
  string invitation_code = 2;
  string social_community_id = 3;
}

message CheckSocialCommunityInvitationUserResponse {
  ga.BaseResp base_resp = 1;
  bool invitation_code_close = 2; // 邀请码活动关闭
  bool is_valid_user = 3; // 是否有效用户
  int32 user_type = 4; // 0 新注册用户，1 老用户
  bool disable_dialog = 5;
}


message SearchSocialCommunityRequest{
  ga.BaseReq base_req = 1;
  string social_community_name = 2;
  int64 offset=3;
  int64 limit=4;
}

message SearchSocialCommunityResponse{
  ga.BaseResp base_resp = 1;
  repeated  SimpleSocialCommunityInfo  social_community_info=2;
}

message GetUserSocialCommunityListRequest{
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
}

message GetUserSocialCommunityListResponse{
  ga.BaseResp base_resp = 1;
  repeated  SimpleSocialCommunityInfo  social_community_info=2;
}

/*扩展麦位*/
message GetExtendMicPermissionRequest{
  ga.BaseReq base_req = 1;
  int32 tab_id=2;
  uint32 channel_id=3;
}

message GetExtendMicPermissionResponse{
  ga.BaseResp base_resp = 1;
  bool has_permission=2;
}


message SetExtendMicNumbersRequest{
  ga.BaseReq base_req = 1;
  int32 tab_id=2;
  uint32 channel_id=3;
  int32 mic_numbers=4;
}

message SetExtendMicNumbersResponse{
  ga.BaseResp base_resp = 1;
}

message GetExtendMicNumbersRequest{
  ga.BaseReq base_req = 1;
  int32 tab_id=2;
  uint32 channel_id=3;

}

message GetExtendMicNumbersResponse{
  ga.BaseResp base_resp = 1;
  repeated uint32 unlock_mic_numbers=2;
  uint32 current_mic_number=3;
  string permission_introduction=4;
}

/*扩展麦位*/


/*获取社群主理人*/

message GetSocialCommunityCaptainRequest{
  ga.BaseReq base_req = 1;
  uint32 channel_id=2;
  string social_community_id=3;

}

message GetSocialCommunityCaptainResponse{
  ga.BaseResp base_resp = 1;
  SimpleUserInfo captain = 2;               //主理人名称
}