syntax = "proto3";

package ga.newbie_page_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/newbie_page_logic";

/* 获取新用户承接页配置 */
message GetNewbiePageConfigRequest {
  ga.BaseReq base_req = 1;
}
message PageTagInfo{
  string id = 1; /* 唯一ID */
  string tag_name_present = 2;  /* 兴趣标签外显名 */
  string icon = 3;
  string home_page_head_id = 4;  /* 金刚区ID */
  uint32 game_tab_id = 5; /* 游戏tab_id */
  bool has_game_card = 6; /* 可以创建游戏卡 true 可以 */
}
message NewbiePageConfig{
  string catalog_name = 1; /* 品类名 */
  string catalog_icon = 2;
  repeated PageTagInfo tag_info = 3; /* 标签 */
}
message GetNewbiePageConfigResponse {
  ga.BaseResp base_resp = 1;
  repeated NewbiePageConfig page_config = 2;
  uint32 max_tag_select_num = 3; /* 最大标签可选个数 */
}

/* 用户选择兴趣标签 */
message SetUserNewbiePageTagRequest{
  ga.BaseReq base_req = 1;
  repeated string tag_id_list = 2; /* 标签 */
}
message SetUserNewbiePageTagResponse{
  ga.BaseResp base_resp = 1;
}

/*用户兴趣内容选择*/
enum PageTagType{
  PAGE_TAG_TYPE_UNSPECIFIED = 0;
  PAGE_TAG_TYPE_NEWBIE_PAGE = 0x01; // 新用户承接页的标签，废弃
  PAGE_TAG_TYPE_UGC_PAGE = 0x10; // 广场标签，废弃

  PAGE_TAG_TYPE_NEWBIE_PAGE_NEW = 10; // 新用户承接页的标签
  PAGE_TAG_TYPE_UGC_PAGE_NEW = 100; // 广场标签：广场独有的兴趣标签，用户层面用作场景
  PAGE_TAG_TYPE_CHANNEL_PAGE = 1000; // 首页标签：首页房间独有的兴趣标签，用户层面用作场景
  PAGE_TAG_TYPE_COMMON_PAGE = 10000; // 广场和首页共用标签：广场兴趣标签与首页房间标签统一共用的部分
}

message GetUserPageTagListRequest{
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  int32 page_tag_type_bit = 3; // PageTagType bit
}

message UserPageTagInfo{
  string id = 1;
  string text = 2;
  string icon_url = 3;
  int32 page_tag_type_bit = 4; // PageTagType bit
}

message GetUserPageTagListResponse{
  ga.BaseResp base_resp = 1;
  repeated UserPageTagInfo page_tag_list = 2;
}

message SetUserPageTagListRequest{
  uint32 uid = 1;
  repeated string page_tag_id_list = 2;
  int32 page_tag_type_bit = 3; // PageTagType bit

}

message SetUserPageTagListResponse{
  ga.BaseResp base_resp = 1;
}

message SetUserPageTagListReq{
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  repeated string page_tag_id_list = 3;
  int32 page_tag_type_bit = 4; // PageTagType bit
}

message SetUserPageTagListResp{
  ga.BaseResp base_resp = 1;
}
