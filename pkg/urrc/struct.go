package urrc

import "golang.52tt.com/pkg/foundation/utils"

// doc: https://q9jvw0u5f5.feishu.cn/docs/doccnqvqx7EvxixrDOJvvaWtY1f#T8VdnE

type GetViolationReq struct {
	AppId        uint32   `json:"appId"`
	ObjTypeId    uint32   `json:"objTypeId"`
	ObjId        []string `json:"objId"`        // ttid(alias)
	OptStartTime int64    `json:"optStartTime"` // ms
	OptEndTime   int64    `json:"optEndTime"`
}

type ViolationRawInfo struct {
	ObjId   string `json:"objId"` // ttid(alias)
	Records []struct {
		LevelKey string                 `json:"levelKey"` // A,B,C
		Total    uint32                 `json:"total"`
		Details  []*ViolationDetialInfo `json:"details"`
	} `json:"records"`
}

type ViolationDetialInfo struct {
	ViolationReason    string `json:"violationReason"`    // 违规原因
	SanctionActionDesc string `json:"sanctionActionDesc"` // 处罚描述
	BizId              string `json:"bizId"`              // 审核业务ID
	BizName            string `json:"bizName"`            // 审核业务名称
	OptTime            int64  `json:"optTime"`            // 处罚时间 ms
}

type ViolationInfo struct {
	ObjId      string // ttid(alias)
	ViolationA []*ViolationDetialInfo
	ViolationB []*ViolationDetialInfo
	ViolationC []*ViolationDetialInfo
}

func (t *ViolationRawInfo) String() string {
	return utils.ToJson(t)
}

func (t *ViolationInfo) GetViolation() (a, b, c []*ViolationDetialInfo) {
	if t == nil {
		return []*ViolationDetialInfo{}, []*ViolationDetialInfo{}, []*ViolationDetialInfo{}
	}
	return t.ViolationA, t.ViolationB, t.ViolationC
}
