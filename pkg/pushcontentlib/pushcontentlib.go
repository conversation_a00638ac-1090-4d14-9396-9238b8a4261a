/*
 * @Description:
 * @Date: 2021-10-14 19:53:26
 * @LastEditors: liang
 * @LastEditTime: 2022-01-17 20:21:17
 */
package pushcontentlib

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math/rand"
	"net/http"
	"strings"
	"sync/atomic"
	"time"
	"unsafe"

	comm_push_placeholder "golang.52tt.com/clients/comm-push-placeholder"
	"golang.52tt.com/pkg/log"
	comm_push_placeholderPb "golang.52tt.com/protocol/services/comm-push-placeholder"
)

type PushContentlib struct {
	PushTitle   string
	PushContent string
	PushJumpURL string
	PushItemId  string
}

//推送文案模板
type PushContentlibTmplCli struct {
	texts          *[]*SPushContentlibResp
	pushLibId      string
	pushLibURL     string
	PushJumpURL    string
	placeHolderCli *comm_push_placeholder.Client
}

type PushContentlibTmpl struct {
	ItemId              string
	pushTitleTmpl       []string
	pushTitleTmplConf   map[string][]int
	pushContentTmpl     []string
	pushContentTmplConf map[string][]int
	PushJumpURLTmpl     []string
	PushJumpURLTmplConf map[string][]int
	//占位符列表配置
	pushConvertConf map[string]bool
	placeHolderCli  *comm_push_placeholder.Client
}

func (s *PushContentlibTmpl) GetPushConvertConf() map[string]bool {
	return s.pushConvertConf

}

func newPushContentlibTmpl(PushTitle string, PushContent string, PushJumpURL string, PushItemId string, placeHolderCli *comm_push_placeholder.Client) *PushContentlibTmpl {
	s := &PushContentlibTmpl{
		placeHolderCli: placeHolderCli,
		ItemId:         PushItemId,
	}
	s.pushConvertConf = map[string]bool{}
	s.pushTitleTmpl, s.pushTitleTmplConf = addTmplContent2Conf(s.pushConvertConf, PushTitle)
	s.pushContentTmpl, s.pushContentTmplConf = addTmplContent2Conf(s.pushConvertConf, PushContent)
	s.PushJumpURLTmpl, s.PushJumpURLTmplConf = addTmplContent2Conf(s.pushConvertConf, PushJumpURL)
	return s
}

//最后一个参数非必填
func NewPushContentlibTmplCli(pushLibId, pushLibURL, PushJumpURL string, cli *comm_push_placeholder.Client) (*PushContentlibTmplCli, error) {
	s := &PushContentlibTmplCli{
		PushJumpURL:    PushJumpURL,
		pushLibId:      pushLibId,
		pushLibURL:     pushLibURL,
		placeHolderCli: cli,
	}
	for {
		//获取texts
		if err := s.checkUpdate(); err != nil {
			time.Sleep(time.Second)
			continue
		}
		break
	}
	go func() {
		ticker := time.NewTicker(time.Second * 600)
		for range ticker.C {
			s.checkUpdate()
		}
	}()
	return s, nil
}

func (s *PushContentlibTmplCli) GetUnUseTexts(arr []string) (*PushContentlibTmpl, bool) {
	filter := map[string]bool{}
	for _, v := range arr {
		filter[v] = true
	}
	texts := *s.texts
	for _, v := range texts {
		for _, v2 := range v.SData.List {
			if !filter[v2.Copywriting.ItemId] {
				textContent := v2.Copywriting
				return newPushContentlibTmpl(textContent.Title, textContent.Content, s.PushJumpURL, textContent.ItemId, s.placeHolderCli), true
			}
		}
	}

	// 都被过滤了， 随机取一个
	if len(texts) > 0 {
		now := int(time.Now().Unix())
		index := now % len(texts)
		v := texts[index]

		if len(v.SData.List) > 0 {
			i := now % len(v.SData.List)
			textContent := v.SData.List[i].Copywriting
			log.Debugf("GetUnUseTexts rand get... textContent is %s, index is %d, i is %d", textContent, index, i)
			return newPushContentlibTmpl(textContent.Title, textContent.Content, s.PushJumpURL, textContent.ItemId, s.placeHolderCli), true
		}
	}

	return nil, false
}

func (s *PushContentlibTmplCli) GenPushContentlibTmpl() (*PushContentlibTmpl, bool) {
	texts := *s.texts
	text := (texts)[rand.Intn(len(texts))]
	if len(text.SData.List) == 0 {
		return nil, false
	}
	textContent := text.SData.List[rand.Intn(len(text.SData.List))].Copywriting
	return newPushContentlibTmpl(textContent.Title, textContent.Content, s.PushJumpURL, textContent.ItemId, s.placeHolderCli), true
}

func (s *PushContentlibTmplCli) checkUpdate() error {
	texts, err := getContentItemsFromRemote(s.pushLibURL, s.pushLibId)
	if err != nil {
		err := fmt.Errorf("getContentItemsFromRemote err %v", err)
		log.Errorf(err.Error())
		return err
	}
	if len(texts) == 0 {
		err := fmt.Errorf("texts empty err ")
		log.Errorf(err.Error())
		return err
	}
	m := &texts
	atomic.StorePointer((*unsafe.Pointer)(unsafe.Pointer(&s.texts)), unsafe.Pointer(m))
	return nil

}

func (s *PushContentlibTmplCli) GetNextPushContentlibTmplByItemId(Id string) (*PushContentlibTmpl, bool) {
	texts := *s.texts
	text := texts[rand.Intn(len(texts))]
	if len(text.SData.List) == 0 {
		return nil, false
	}
	textContent := text.SData.List[0].Copywriting
	for i, item := range text.SData.List {
		if item.Copywriting.ItemId == Id && i+1 < len(text.SData.List) {
			textContent = text.SData.List[i+1].Copywriting
		}
	}
	return newPushContentlibTmpl(textContent.Title, textContent.Content, s.PushJumpURL, textContent.ItemId, s.placeHolderCli), true
}

func addTmplContent2Conf(recv map[string]bool, text string) ([]string, map[string][]int) {
	r := []rune(text)
	dealText := []string{}
	dealTextMap := map[string][]int{}
	beginI := 0
	i := 0
	for i = 0; i < len(r); i++ {
		if r[i] == '{' {
			dealText = append(dealText, string(r[beginI:i]))
			beginI = i
			var item []rune
			for i < len(r) {
				item = append(item, r[i])
				if r[i] == '}' {
					str := string(item)
					recv[str] = true
					dealTextMap[str] = append(dealTextMap[str], len(dealText))
					dealText = append(dealText, str)
					beginI = i + 1
					break
				}
				i++
			}
		}
	}
	if i > beginI {
		dealText = append(dealText, string(r[beginI:i]))
	}
	return dealText, dealTextMap
}

func doPushContentPlaceHold(inContent []string, inContentIdxMap map[string][]int, placeHold2Val map[string]string) string {
	thisContent := make([]string, len(inContent))
	copy(thisContent, inContent)
	index2Valmap := map[int]string{}
	for placeHold, val := range placeHold2Val {
		if idxVals, ok := inContentIdxMap[placeHold]; ok {
			for _, v := range idxVals {
				index2Valmap[v] = val
			}
		}
	}
	for i := range thisContent {
		if v, ok := index2Valmap[i]; ok {
			thisContent[i] = v
		}
	}
	return strings.Join(thisContent, "")
}

func (s *PushContentlibTmpl) GenPushContentlib(req comm_push_placeholderPb.GetPlaceHoldReq, convertMap map[string]string, defaultMaps ...map[string]string) (*PushContentlib, error) {
	retMap := make(map[string]string, 3)
	//当需要处理占位符的时候进行请求
	if len(req.Params) != 0 {
		if s.placeHolderCli == nil {
			return nil, fmt.Errorf("s.placeHolderCli is nil,can't make req")
		}
		ctx, cancel := context.WithTimeout(context.Background(), time.Second)
		defer cancel()
		pushResp, err := s.placeHolderCli.GetPlaceHold(ctx, &req)
		if err != nil {
			log.Errorf("GetPlaceHold %v", err)
			return &PushContentlib{}, err
		}
		for _, v := range pushResp.RetItem {
			if v1, ok := convertMap[v.Val]; ok {
				retMap[v.Name] = v1
			} else {
				retMap[v.Name] = v.Val
			}
		}
	}

	for _, defaultMap := range defaultMaps {
		for key, val := range defaultMap {
			retMap[key] = val
		}
	}
	//fmt.Println(retMap)
	return &PushContentlib{
		PushTitle:   doPushContentPlaceHold(s.pushTitleTmpl, s.pushTitleTmplConf, retMap),
		PushContent: doPushContentPlaceHold(s.pushContentTmpl, s.pushContentTmplConf, retMap),
		PushJumpURL: doPushContentPlaceHold(s.PushJumpURLTmpl, s.PushJumpURLTmplConf, retMap),
	}, nil
}

type SPushContentlibItem struct {
	ItemId  string `json:"_id"`
	Title   string `json:"title"`
	Content string `json:"content"`
}

type SPushContentlibResp struct {
	SData struct {
		List []struct {
			Copywriting SPushContentlibItem `json:"copywriting"`
		} `json:"list"`
	} `json:"data"`
}

func getContentItemsFromRemote(PushContentlibUrl, pkgId string) (out []*SPushContentlibResp, err error) {
	pageSize := 100
	fetchSize := pageSize
	token := "eyJ1aWQiOiJpbnZpdGVVaWQiLCJpYXQiOjE2MTQwNzgxMzYsImV4cCI6MTYxNDA3OTkzNn1"
	for i := 1; i <= 1000; i++ {
		body := fmt.Sprintf(`{ "page": %d, "size": %d, "pkgId": "%s", "svcToken": "%s"}`, i, pageSize, pkgId, token)
		resp, err := http.Post(PushContentlibUrl, "application/json", strings.NewReader(body))
		if nil != err {
			log.Errorf("getContentItemsFromRemote http req failed, url: %d, req body: %s, err: %+v", PushContentlibUrl, body, err)
			return out, err
		}

		bodyStr, err := ioutil.ReadAll(resp.Body)
		if nil != err {
			log.Errorf("getContentItemsFromRemote http resp body not valid, url: %d, req body: %s, err: %+v", PushContentlibUrl, body, err)
			return out, err
		}

		itemList := &SPushContentlibResp{}
		err = json.Unmarshal(bodyStr, itemList)
		if nil != err {
			log.Errorf("getContentItemsFromRemote http resp body not valid, url: %d, req body: %s, err: %+v", PushContentlibUrl, body, err)
			return out, err
		}
		out = append(out, itemList)
		fetchSize = len(itemList.SData.List)
		if fetchSize < pageSize {
			break
		}
	}
	if len(out) != 0 {
		log.Debugf("getContentItemsFromRemote %v", *out[0])
	} else {
		log.Errorf("getContentItemsFromRemote empty")
	}
	return out, nil
}
