package minor_check

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/runtime/v2/filters/pkg"
	"time"
)

var (
	cfgLoader *configLoader
)

func init() {
	confPath := ttConfPath("minor-check-visible.json")
	loader, err := pkg.NewConfigLoaderV2(context.Background(), confPath, &MinorVisibleConfig{}, true, time.Second*30)
	if nil != err {
		log.Errorf("pkg.NewConfigLoaderV2 failed, conf:%s, err:%v", confPath, err)
	}

	cfgLoader = &configLoader{loader: loader}
}
