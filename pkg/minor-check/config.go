package minor_check

import (
	"encoding/json"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/runtime/v2/filters/pkg"
	"os/user"
	"path"
	"runtime"
)

type MinorVisibleConfig struct {
	IsMinorVisible bool `json:"is_minor_visible"`
}

func (cfg *MinorVisibleConfig) UnmarshalBinary(data []byte) error {
	err := json.Unmarshal(data, cfg)
	if err != nil {
		log.Errorf("UnmarshalBinary data(%s) err: %v", data, err)
		return err
	}

	return nil
}

type configLoader struct {
	loader *pkg.ConfigLoader
}

func ttConfPath(name string) string {
	var ttConfDir string
	if runtime.GOOS == "windows" {
		if u, err := user.Current(); err == nil {
			ttConfDir = path.Join(u.HomeDir, "/data/oss/conf-center/tt/")
		}
	} else {
		ttConfDir = "/data/oss/conf-center/tt/"
	}

	return path.Join(ttConfDir, name)
}

func (ld *configLoader) IsMinorVisible() bool {
	if ld.loader == nil {
		return true
	}

	v := ld.loader.Get()
	cfg, _ := v.(*MinorVisibleConfig)

	return cfg.IsMinorVisible
}
