package crontab

import (
	"fmt"
	"runtime/debug"

	"github.com/robfig/cron"

	"golang.52tt.com/pkg/log"
)

type Option interface {
	Notice(msg string)
}

func New(opt Option) (*Cron, error) {
	if opt == nil {
		return nil, fmt.Errorf("未传入配置参数！")
	}

	return &Cron{
		cron:   cron.New(),
		notice: opt.Notice,
	}, nil
}

type Cron struct {
	cron *cron.Cron

	notice func(msg string)
}

func (c *Cron) Start() {
	c.cron.Start()
}

func (c *Cron) Stop() {
	c.cron.Stop()
}

func (c *Cron) AddFunc(spec string, key string, cmd func()) error {
	return c.cron.AddFunc(spec, func() {
		defer func() {
			if e := recover(); e != nil {
				log.Errorf("Cron %+v panic err:[%+v], stack:[%+v]", key, e, string(debug.Stack()))
				c.notice(fmt.Sprintf(noticeInfo, key, e))
				return
			}
		}()
		cmd()
	})
}

var noticeInfo = `
定时任务异常panic：
	任务名称：[%+v]，
	报错信息：[%+v]，
`
