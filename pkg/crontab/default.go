package crontab

import (
	"runtime/debug"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/reporter"
)

func NewOption(noticeUrl string) *DefaultOption {
	return &DefaultOption{
		feishu: reporter.NewFeishuReporter(noticeUrl, ""),
	}
}

type DefaultOption struct {
	feishu *reporter.FeishuReporter
}

func (d *DefaultOption) Notice(msg string) {
	defer func() {
		if e := recover(); e != nil {
			log.Errorf("Cron defaultNoticeFunc panic err:[%+v], stack:[%+v]", e, debug.Stack())
			return
		}
	}()

	// 这里重试三次
	for i := 0; ; {
		err := d.feishu.SendText(msg)
		if err != nil {
			if i < 3 {
				i++
				time.Sleep(time.Second)
				continue
			}
			log.Errorf("Crontab Notice failed, err:[%+v], msg:[%+v]", err, msg)
			return
		} else {
			break
		}
	}

	log.Infof("Crontab Notice successful, msg: %+v", msg)
	return
}
