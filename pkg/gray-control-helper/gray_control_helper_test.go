package gray_control_helper

import "testing"

func TestIsGrayControlEnabledForChannelID(t *testing.T) {
	type args struct {
		businessType string
		channelID    uint32
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "TestIsGrayControlEnabledForChannelID true",
			args: args{
				businessType: "type1",
				channelID:    70,
			},
			want: true,
		},
		{
			name: "TestIsGrayControlEnabledForChannelID false",
			args: args{
				businessType: "type1",
				channelID:    101,
			},
			want: false,
		},
		{
			name: "TestIsGrayControlEnabledForChannelID false",
			args: args{
				businessType: "type1",
				channelID:    0,
			},
			want: false,
		},
		{
			name: "TestIsGrayControlEnabledForChannelID boundary below",
			args: args{
				businessType: "type1",
				channelID:    1,
			},
			want: true,
		},
		{
			name: "TestIsGrayControlEnabledForChannelID boundary high",
			args: args{
				businessType: "type1",
				channelID:    100,
			},
			want: true,
		},
		{
			name: "TestIsGrayControlEnabledForChannelID true",
			args: args{
				businessType: "type2",
				channelID:    70,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsGrayControlEnabledForChannelID(tt.args.businessType, tt.args.channelID); got != tt.want {
				t.Errorf("IsGrayControlEnabledForChannelID() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsGrayControlEnabledForUID(t *testing.T) {
	type args struct {
		businessType string
		uid          uint32
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "TestIsGrayControlEnabledForUID 1",
			args: args{
				businessType: "type1",
				uid:          70,
			},
			want: true,
		},
		{
			name: "TestIsGrayControlEnabledForUID 2",
			args: args{
				businessType: "type1",
				uid:          0,
			},
			want: false,
		},
		{
			name: "TestIsGrayControlEnabledForUID 3",
			args: args{
				businessType: "type1",
				uid:          0,
			},
			want: false,
		},
		{
			name: "TestIsGrayControlEnabledForUID 4",
			args: args{
				businessType: "type1",
				uid:          20001,
			},
			want: true,
		},
		{
			name: "TestIsGrayControlEnabledForUID 5",
			args: args{
				businessType: "type1",
				uid:          30000,
			},
			want: true,
		},
		{
			name: "TestIsGrayControlEnabledForUID 6",
			args: args{
				businessType: "type1",
				uid:          1,
			},
			want: true,
		},
		{
			name: "TestIsGrayControlEnabledForUID 7",
			args: args{
				businessType: "type1",
				uid:          10000,
			},
			want: true,
		},
		{
			name: "TestIsGrayControlEnabledForUID 8",
			args: args{
				businessType: "type2",
				uid:          70,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsGrayControlEnabledForUID(tt.args.businessType, tt.args.uid); got != tt.want {
				t.Errorf("IsGrayControlEnabledForUID() = %v, want %v", got, tt.want)
			}
		})
	}
}
