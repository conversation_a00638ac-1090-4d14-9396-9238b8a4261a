package prometheus

import (
	"context"
	"os"
	"path"
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

var (
	AppName string
	requestDurationBuckets = []float64{0.5, 1, 10, 20, 50, 100, 200, 500, 1000, 2000} // kafka_request_duration buckets
)

var (
	requestsCounter   = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "kafka_consumer_request_total",
		Help: "Total requests handled by `kafka consumer`",
	}, joinLabels(LabelNamesAppName, LabelNamesKafkaAddr, LabelNamesKafkaGroupId, LabelNamesKafkaTopic))

	requestsHistogram = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "kafka_consumer_request_duration",
		Help:    "Duration in seconds that `kafka_consumer` handles requests",
		Buckets: requestDurationBuckets,
	}, joinLabels(LabelNamesAppName, LabelNamesKafkaAddr, LabelNamesKafkaGroupId, LabelNamesKafkaTopic))
)

const (
	LabelNamesAppName      = "process_name"
	LabelNamesKafkaTopic   = "kafka_topic"
	LabelNamesKafkaGroupId = "kafka_group_id"
	LabelNamesKafkaAddr    = "kafka_addr"
)

type KafkaMetricInfo struct {
	KafkaGroupId string
	KafkaAddr    string
	KafkaTopic   string
	Costs        time.Duration
}

func init() {

	AppName = os.Getenv("MY_APP_NAME")
	if AppName == "" {
		_, AppName = path.Split(os.Args[0])
	}

	prometheus.MustRegister(requestsCounter, requestsHistogram)

}

func joinLabels(labels ...string) (ret []string) {
	for _, label := range labels {
		ret = append(ret, label)
	}
	return
}

func ReportMetrics(ctx context.Context, info *KafkaMetricInfo) {

	labels := prometheus.Labels{
		LabelNamesKafkaGroupId: info.KafkaGroupId,
		LabelNamesKafkaAddr:    info.KafkaAddr,
		LabelNamesAppName:      AppName,
		LabelNamesKafkaTopic:   info.KafkaTopic,
	}

	if len(labels) > 0 {
		requestsCounter.With(labels).Inc()
		requestsHistogram.With(labels).Observe(float64(info.Costs.Milliseconds()))
	}
}
