package models

import (
	"context"
	"golang.52tt.com/pkg/active_present/client"
	"golang.52tt.com/pkg/log"
	backpack "golang.52tt.com/protocol/services/backpacksvr"
	chance_game_entry "golang.52tt.com/protocol/services/chance-game-entry"
	smash_egg "golang.52tt.com/protocol/services/smash-egg"
	"time"
)

type SmashEggABCfgHandle struct {
}

// NewSmashEggABCfgHandle 魔力转转
func NewSmashEggABCfgHandle() *SmashEggABCfgHandle {
	return &SmashEggABCfgHandle{}
}

func (s *SmashEggABCfgHandle) Create() *SmashEggABCfgHandle {
	return &SmashEggABCfgHandle{}
}

func (s *SmashEggABCfgHandle) GetCfgItemList() []uint32 {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	itemIdList := make([]uint32, 0)
	totalPool := make([]*smash_egg.Prize, 0)

	if client.SmashEggAbCli == nil {
		log.ErrorWithCtx(ctx, "SmashEggABCfgHandle GetCfgItemList SmashEggABCli is nil")
		return []uint32{}
	}

	cli, ab, _, err := client.SmashEggAbCli.GetUserThemeCli(ctx, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "SmashEggABCfgHandle GetUserThemeCli err %v", err)
		return []uint32{}
	}

	// 检查开关
	if CheckChanceGameSwitch(ctx, uint32(chance_game_entry.NewChanceGameType_NewChanceGameType_SmashEgg)) {
		normalPool, err := cli.GetPrizePool(ctx, &smash_egg.GetPrizePoolReq{
			Mode: smash_egg.Mode_NORMAL_MODE,
			Flag: smash_egg.Flag_NORMAL,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "SmashEggABCfgHandle GetPrizePool Normal ab %s err %v", ab, err)
		}

		morphPool, err := cli.GetPrizePool(ctx, &smash_egg.GetPrizePoolReq{
			Mode: smash_egg.Mode_NORMAL_MODE,
			Flag: smash_egg.Flag_MORPH,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "SmashEggABCfgHandle GetPrizePool Morph ab %s err %v", ab, err)
		}

		totalPool = append(normalPool.GetPrizeList(), morphPool.GetPrizeList()...)
	}

	if CheckChanceGameSwitch(ctx, uint32(chance_game_entry.NewChanceGameType_NewChanceGameType_GoldSmash)) {
		goldPool, err := cli.GetPrizePool(ctx, &smash_egg.GetPrizePoolReq{
			Mode: smash_egg.Mode_GOLD_MODE,
			Flag: smash_egg.Flag_NORMAL,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "SmashEggABCfgHandle GetPrizePool Gold ab %s err %v", ab, err)
		}
		totalPool = append(totalPool, goldPool.GetPrizeList()...)
	}

	pidMap := make(map[uint32]bool)

	for _, item := range totalPool {
		pidMap[item.PackId] = true
	}

	for bgId := range pidMap {
		bgCfg, err := client.BackpackCli.GetPackageItemCfg(ctx, 0, &backpack.GetPackageItemCfgReq{BgId: bgId})
		if err != nil {
			log.ErrorWithCtx(ctx, "SmashEggABCfgHandle GetPackageItemCfg Gold err %v", err)
			continue
		}
		for _, item := range bgCfg.GetItemCfgList() {
			itemIdList = append(itemIdList, item.GetSourceId())
		}
	}

	log.InfoWithCtx(ctx, "SmashEggABCfgHandle itemList :%v", itemIdList)

	return itemIdList
}
