package models

import (
	"context"
	"golang.52tt.com/pkg/active_present/local_cache"
	"golang.52tt.com/pkg/log"
	ga_base "golang.52tt.com/protocol/app"
	"time"
)

type NormalPresentCfgHandle struct {
	presentCache *local_cache.PresentMemCache
}

// NewNormalPresentCfgHandle 普通架上礼物
func NewNormalPresentCfgHandle(presentCache *local_cache.PresentMemCache) *NormalPresentCfgHandle {
	return &NormalPresentCfgHandle{presentCache: presentCache}
}

func (s *NormalPresentCfgHandle) GetCfgItemList() []uint32 {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 过滤出所有上架的普通礼物

	itemIdList := make([]uint32, 0)
	_, presentMap := s.presentCache.GetConfigList()
	for id, cfg := range presentMap {

		// 删除的不管
		if cfg.GetIsDel() || cfg.GetExtend().GetIsTest() {
			continue
		}

		// 如果是入团卷，那直接加入 - 产品确认入团劵不显示
		//if cfg.GetExtend().GetTag() == uint32(ga_base.PresentTagType_PRESENT_TAG_ADD_GROUP) {
		//	itemIdList = append(itemIdList, id)
		//	continue
		//}

		// 下架的不管
		if cfg.EffectBegin > uint32(time.Now().Unix()) || cfg.EffectEnd < uint32(time.Now().Unix()) {
			continue
		}

		// 只处理这几个类型的礼物
		if cfg.GetExtend().GetTag() == uint32(ga_base.PresentTagType_PRESENT_TAG_FELLOW) {
			if cfg.GetExtend().GetIsTest() {
				continue
			}
		} else if cfg.GetExtend().GetTag() == uint32(ga_base.PresentTagType_PRESENT_TAG_NORMAL) || cfg.GetExtend().GetTag() == uint32(ga_base.PresentTagType_PRESENT_TAG_CHANNEL_DATING) ||
			cfg.GetExtend().GetTag() == uint32(ga_base.PresentTagType_PRESENT_TAG_NOBILITY) || cfg.GetExtend().GetTag() == uint32(ga_base.PresentTagType_PERSENT_TAG_DRAW) {
			// 不显示在礼物架的不管
			if cfg.GetExtend().GetUnshowPresentShelf() {
				continue
			}
		} else {
			continue
		}

		itemIdList = append(itemIdList, id)
	}

	log.InfoWithCtx(ctx, "NormalPresentCfgHandle itemList :%v", itemIdList)

	return itemIdList
}
