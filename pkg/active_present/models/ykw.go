package models

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/active_present/client"
	"golang.52tt.com/pkg/active_present/local_cache"
	"golang.52tt.com/pkg/log"
	backpack "golang.52tt.com/protocol/services/backpacksvr"
	"os"
	"time"
)

type YouKnowWhoCfgHandle struct {
	presentCache *local_cache.PresentMemCache
}

func NewYouKnowWhoCfgHandle() *YouKnowWhoCfgHandle {
	return &YouKnowWhoCfgHandle{}
}

type OrderConfig struct {
	OrderTypeList []*OrderType `json:"order_type_list"`
}

// OrderType 订单套餐类型
type OrderType struct {
	PkgId uint32 `json:"pkg_id"`
}

const OrderConfigFile = "/data/oss/conf-center/tt/you-know-who-order.json"

func (s *YouKnowWhoCfgHandle) GetCfgItemList() []uint32 {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 过滤出所有上架的普通礼物

	itemIdList := make([]uint32, 0)

	cfg, err := load(OrderConfigFile)
	if err != nil {
		log.ErrorWithCtx(ctx, "YouKnowWhoCfgHandle load err %v", err)
		return itemIdList
	}

	pkgList := make([]uint32, 0)
	for _, item := range cfg.OrderTypeList {
		pkgList = append(pkgList, item.PkgId)
	}

	resp, err := client.BackpackCli.GetPackageItemCfg(ctx, 0, &backpack.GetPackageItemCfgReq{
		BgIdList: pkgList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "YouKnowWhoCfgHandle GetPackageItemCfg err %v", err)
		return itemIdList
	}

	for _, item := range resp.GetPackageItemCfgList() {
		for _, backpackItem := range item.GetItemCfgList() {
			if backpackItem.GetItemType() != uint32(backpack.PackageItemType_BACKPACK_PRESENT) {
				continue
			}
			itemIdList = append(itemIdList, backpackItem.GetSourceId())
		}
	}

	log.InfoWithCtx(ctx, "YouKnowWhoCfgHandle itemList :%v", itemIdList)

	return itemIdList
}

func load(filename string) (*OrderConfig, error) {
	oc := &OrderConfig{}
	data, err := os.ReadFile(filename)
	if err != nil {
		return oc, err
	}
	err = json.Unmarshal(data, oc)
	if err != nil {
		return oc, err
	}

	return oc, nil
}
