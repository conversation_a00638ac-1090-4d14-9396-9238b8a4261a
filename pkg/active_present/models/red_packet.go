package models

import (
	"context"
	"golang.52tt.com/pkg/active_present/client"
	"golang.52tt.com/pkg/log"
	"time"
)

// RedPacketCfgHandle 房间礼物红包
type RedPacketCfgHandle struct {
}

func NewRedPacketCfgHandle() *RedPacketCfgHandle {
	return &RedPacketCfgHandle{}
}

func (s *RedPacketCfgHandle) GetCfgItemList() []uint32 {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 过滤出所有上架的普通礼物

	itemIdList := make([]uint32, 0)
	RedPacketCfg, err := client.RedPacketCli.GetRedPacketConf(ctx, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "RedPacketCfgHandle GetActivityConfigList err %v", err)
		return itemIdList
	}

	for _, item := range RedPacketCfg.GetList() {
		if item.GetBeginTime() > uint32(time.Now().Unix()) || item.GetEndTime() < uint32(time.Now().Unix()) {
			continue
		}

		itemIdList = append(itemIdList, item.GetGiftIdList()...)
	}

	log.InfoWithCtx(ctx, "RedPacketCfgHandle itemList :%v", itemIdList)

	return itemIdList
}
