package active_present

import (
	"context"
	"golang.52tt.com/pkg/active_present/client"
	"golang.52tt.com/pkg/active_present/local_cache"
	"golang.52tt.com/pkg/active_present/models"
	"golang.52tt.com/pkg/log"
	userpresent "golang.52tt.com/protocol/services/userpresent"
	"sync"
	"time"
)

// 用于获取所有“正在流通”的礼物
// 没有什么特别的办法，一个个业务去查询、过滤，定义参考 https://q9jvw0u5f5.feishu.cn/docx/AWqvdGB5vogM17xES0JcLb5Qn5c

type ActivePresentMgr struct {
	itemIdList   []uint32
	handleMap    map[uint32]ActivePresentCfgHandle
	presentCache *local_cache.PresentMemCache
	rwMutex      sync.RWMutex
	ticker       *time.Ticker
}

type ActivePresentCfgHandle interface {
	GetCfgItemList() []uint32
}

const (
	ActivePresentCfgTypeNone = iota
	ActivePresentCfgTypeSmashEgg
	ActivePresentCfgTypeOnePiece
	ActivePresentCfgTypeStarTrek
	ActivePresentCfgTypeCatCanteen
	ActivePresentCfgTypeCatConversion
	ActivePresentCfgTypeNormalPresent
	ActivePresentCfgTypeCustomPresent
	ActivePresentCfgTypeLevelupPresent
	ActivePresentCfgTypeMagicSpirit
	ActivePresentCfgTypeTreasureHouse
	ActivePresentCfgTypeYouKnowWho
	ActivePresentCfgTypeOfferRoom
	ActivePresentCfgNobilityLevel
	ActivePresentCfgFirstCharge
	ActivePresentCfgRedPacket
	ActivePresentCfgGloryWorld
	ActivePresentCfgConstellationWall
	ActivePresentCfgRevenueExtGame
	ActivePresentCfgMissingItem
	ActivePresentCfgGloryMagic
	ActivePresentCfgWeekCard
	ActivePresentCfgSmashEggAb
	ActivePresentCfgPresentSet = 23
)

func InitActivePresentCfgMgr() (IActivePresentMgr, error) {
	ActivePresentCfg := &ActivePresentMgr{
		itemIdList: make([]uint32, 0),
		handleMap:  make(map[uint32]ActivePresentCfgHandle),
		rwMutex:    sync.RWMutex{},
	}

	// 初始化要用到的client
	err := client.Setup()
	if err != nil {
		log.Errorf("InitActivePresentCfgMgr fail %v", err)
		return nil, err
	}

	presentCache := local_cache.NewPresentMemCache()
	if err != nil {
		log.Errorf("InitActivePresentCfgMgr fail %v", err)
		return ActivePresentCfg, err
	}
	ActivePresentCfg.presentCache = presentCache

	// 这里初始化的时候直接塞进去
	ActivePresentCfg.handleMap[ActivePresentCfgTypeOnePiece] = models.NewOnePieceCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgTypeStarTrek] = models.NewStarTrekCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgTypeCatCanteen] = models.NewCatCanteenCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgTypeCatConversion] = models.NewConversionCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgTypeNormalPresent] = models.NewNormalPresentCfgHandle(presentCache)
	ActivePresentCfg.handleMap[ActivePresentCfgTypeCustomPresent] = models.NewCustomPresentCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgTypeLevelupPresent] = models.NewLevelUpPresentCfgHandle(presentCache)
	ActivePresentCfg.handleMap[ActivePresentCfgTypeMagicSpirit] = models.NewMagicSpiritCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgTypeTreasureHouse] = models.NewTreasureHouseCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgTypeYouKnowWho] = models.NewYouKnowWhoCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgTypeOfferRoom] = models.NewOfferRoomCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgNobilityLevel] = models.NewNobilityLevelCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgFirstCharge] = models.NewFirstChargeCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgRedPacket] = models.NewRedPacketCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgGloryWorld] = models.NewGloryWorldCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgConstellationWall] = models.NewConstellationWallCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgRevenueExtGame] = models.NewRevenueExtGameCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgMissingItem] = models.NewMissingItemCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgGloryMagic] = models.NewGloryMagicCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgWeekCard] = models.NewWeekCardCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgSmashEggAb] = models.NewSmashEggABCfgHandle()
	ActivePresentCfg.handleMap[ActivePresentCfgPresentSet] = models.NewPresentSetCfgHandle()
	ActivePresentCfg.UpdateActivePresentItem()

	ActivePresentCfg.ticker = time.NewTicker(5 * time.Minute)
	go ActivePresentCfg.ConfigUpdateTicker()

	log.Infof("InitActivePresentCfgMgr success %+v", ActivePresentCfg)

	return ActivePresentCfg, nil
}

func (s *ActivePresentMgr) GetActivePresentItemId() []uint32 {
	s.rwMutex.RLock()
	defer s.rwMutex.RUnlock()

	// 过滤一下，只返回t豆礼物
	tmpIdList := make([]uint32, 0)
	for _, item := range s.itemIdList {
		if s.presentCache.GetConfigById(item).GetPriceType() == uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN) &&
			!s.presentCache.GetConfigById(item).GetIsDel() && !s.presentCache.GetConfigById(item).GetExtend().GetIsTest() {
			tmpIdList = append(tmpIdList, item)
		}
	}

	return tmpIdList
}

func (s *ActivePresentMgr) CheckIsActivePresentItem(id uint32) bool {
	s.rwMutex.RLock()
	defer s.rwMutex.RUnlock()
	for _, item := range s.itemIdList {
		if item == id {
			return true
		}
	}
	return false
}

func (s *ActivePresentMgr) UpdateActivePresentItem() {
	// 手动刷新礼物缓存，保证一致
	s.presentCache.UpdateConfig()

	itemMap := make(map[uint32]bool)
	for _, item := range s.handleMap {
		resp := item.GetCfgItemList()
		for _, id := range resp {
			itemMap[id] = true
		}
	}

	s.rwMutex.Lock()
	defer s.rwMutex.Unlock()
	s.itemIdList = make([]uint32, 0)
	for item := range itemMap {
		s.itemIdList = append(s.itemIdList, item)
	}

	log.InfoWithCtx(context.Background(), "UpdateActivePresentItem %v", s.itemIdList)
}

func (s *ActivePresentMgr) ConfigUpdateTicker() {
	defer s.ticker.Stop()

	for range s.ticker.C {
		s.UpdateActivePresentItem()
	}
}
