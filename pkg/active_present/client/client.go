package client

import (
	"context"
	account "golang.52tt.com/clients/account-go"
	"golang.52tt.com/clients/backpack"
	cat_canteen "golang.52tt.com/clients/cat-canteen"
	chance_game_entry "golang.52tt.com/clients/chance-game-entry"
	"golang.52tt.com/clients/channel"
	channel_red_packet "golang.52tt.com/clients/channel-red-packet"
	channelMic "golang.52tt.com/clients/channelmic"
	"golang.52tt.com/clients/conversion"
	exchange "golang.52tt.com/clients/exchange"
	gloryReward "golang.52tt.com/clients/glory-reward"
	"golang.52tt.com/clients/guild"
	levelup_present "golang.52tt.com/clients/levelup-present"
	magic_spirit "golang.52tt.com/clients/magic-spirit"
	one_piece "golang.52tt.com/clients/one-piece"
	present_extra_conf "golang.52tt.com/clients/present-extra-conf"
	present_week_card "golang.52tt.com/clients/present-week-card"
	revenue_ext_game "golang.52tt.com/clients/revenue-ext-game"
	smash_egg "golang.52tt.com/clients/smash-egg"
	star_trek "golang.52tt.com/clients/star-trek"
	treasure_house "golang.52tt.com/clients/treasure-house"
	userPresent "golang.52tt.com/clients/userpresent"
	glory_lottery "golang.52tt.com/protocol/services/glory-lottery"
	glory_magic "golang.52tt.com/protocol/services/glory-magic"
	present_go_logic "golang.52tt.com/protocol/services/logicsvr-go/present-go-logic"
	offer_room "golang.52tt.com/protocol/services/offer-room"
	present_set "golang.52tt.com/protocol/services/present-set"
	present_wall "golang.52tt.com/protocol/services/present-wall"
	smash_egg_ab "golang.52tt.com/services/smash-egg/ab-control"
	"google.golang.org/grpc"
	"time"
)

var (
	PresentCli            userPresent.IClient
	AccountCli            account.IClient
	ChannelCli            channel.IClient
	ChannelMicCli         channelMic.IClient
	GuildCli              guild.IClient
	PresentExtraConfigCli present_extra_conf.IClient
	SmashEggCli           smash_egg.IClient
	BackpackCli           backpack.IClient
	StarTrekCli           star_trek.IClient
	OnePieceCli           one_piece.IClient
	CatCanteenCli         cat_canteen.IClient
	ConversionCli         conversion.IClient
	LevelUpCli            levelup_present.IClient
	MagicSpiritCli        magic_spirit.IClient
	TreasureHouseCli      treasure_house.IClient
	OfferRoomCli          offer_room.OfferingRoomClient
	RedPacketCli          channel_red_packet.IClient
	GloryCli              *exchange.GloryClient
	GloryLotteryCli       *glory_lottery.Client
	ChanceGameEntryCli    chance_game_entry.IClient
	GloryWorldCli         *gloryReward.Client
	RevenueExtCli         revenue_ext_game.IClient
	PresentGoLogicCli     *present_go_logic.Client
	PresentWallCli        present_wall.PresentWallClient
	GloryMagicCli         *glory_magic.Client
	WeekCardCli           present_week_card.IClient
	SmashEggAbCli         *smash_egg_ab.ABControl
	PresentSetCli         present_set.PresentSetClient
)

func Setup() error {
	opts := []grpc.DialOption{}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	AccountCli, _ = account.NewClient(opts...)
	PresentCli = userPresent.NewClient(opts...)
	ChannelCli = channel.NewClient(opts...)
	ChannelMicCli = channelMic.NewClient(opts...)
	GuildCli = guild.NewClient(opts...)
	PresentExtraConfigCli = present_extra_conf.NewIClient(opts...)
	SmashEggCli = smash_egg.NewIClient()
	BackpackCli = backpack.NewIClient(opts...)
	StarTrekCli = star_trek.NewIClient(opts...)
	OnePieceCli = one_piece.NewIClient(opts...)
	CatCanteenCli = cat_canteen.NewIClient(opts...)
	ConversionCli = conversion.NewIClient(opts...)
	LevelUpCli = levelup_present.NewIClient(opts...)
	MagicSpiritCli = magic_spirit.NewIClient(opts...)
	TreasureHouseCli = treasure_house.NewClient(opts...)
	OfferRoomCli = offer_room.MustNewClient(ctx, opts...)
	RedPacketCli = channel_red_packet.NewIClient(opts...)
	GloryCli, _ = exchange.NewGloryClient(opts...)
	GloryWorldCli, _ = gloryReward.NewClient(opts...)
	GloryLotteryCli, _ = glory_lottery.NewClient(ctx, opts...)
	ChanceGameEntryCli = chance_game_entry.NewIClient(opts...)
	RevenueExtCli = revenue_ext_game.NewIClient(opts...)
	PresentGoLogicCli, _ = present_go_logic.NewClient(ctx, opts...)
	GloryMagicCli, _ = glory_magic.NewClient(ctx, opts...)
	// 这个不需要withBlock，否则present-wall自己第一次调用会出问题
	PresentWallCli, _ = present_wall.NewClient(context.Background())
	WeekCardCli = present_week_card.NewIClient(opts...)
	SmashEggAbCli, _ = smash_egg_ab.NewABControl()
	PresentSetCli = present_set.MustNewClient(ctx, opts...)

	return nil
}
