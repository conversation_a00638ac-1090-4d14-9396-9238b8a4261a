// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/pkg/active_present (interfaces: IActivePresentMgr)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIActivePresentMgr is a mock of IActivePresentMgr interface.
type MockIActivePresentMgr struct {
	ctrl     *gomock.Controller
	recorder *MockIActivePresentMgrMockRecorder
}

// MockIActivePresentMgrMockRecorder is the mock recorder for MockIActivePresentMgr.
type MockIActivePresentMgrMockRecorder struct {
	mock *MockIActivePresentMgr
}

// NewMockIActivePresentMgr creates a new mock instance.
func NewMockIActivePresentMgr(ctrl *gomock.Controller) *MockIActivePresentMgr {
	mock := &MockIActivePresentMgr{ctrl: ctrl}
	mock.recorder = &MockIActivePresentMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIActivePresentMgr) EXPECT() *MockIActivePresentMgrMockRecorder {
	return m.recorder
}

// CheckIsActivePresentItem mocks base method.
func (m *MockIActivePresentMgr) CheckIsActivePresentItem(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIsActivePresentItem", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckIsActivePresentItem indicates an expected call of CheckIsActivePresentItem.
func (mr *MockIActivePresentMgrMockRecorder) CheckIsActivePresentItem(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsActivePresentItem", reflect.TypeOf((*MockIActivePresentMgr)(nil).CheckIsActivePresentItem), arg0)
}

// ConfigUpdateTicker mocks base method.
func (m *MockIActivePresentMgr) ConfigUpdateTicker() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ConfigUpdateTicker")
}

// ConfigUpdateTicker indicates an expected call of ConfigUpdateTicker.
func (mr *MockIActivePresentMgrMockRecorder) ConfigUpdateTicker() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigUpdateTicker", reflect.TypeOf((*MockIActivePresentMgr)(nil).ConfigUpdateTicker))
}

// GetActivePresentItemId mocks base method.
func (m *MockIActivePresentMgr) GetActivePresentItemId() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActivePresentItemId")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetActivePresentItemId indicates an expected call of GetActivePresentItemId.
func (mr *MockIActivePresentMgrMockRecorder) GetActivePresentItemId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActivePresentItemId", reflect.TypeOf((*MockIActivePresentMgr)(nil).GetActivePresentItemId))
}

// UpdateActivePresentItem mocks base method.
func (m *MockIActivePresentMgr) UpdateActivePresentItem() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateActivePresentItem")
}

// UpdateActivePresentItem indicates an expected call of UpdateActivePresentItem.
func (mr *MockIActivePresentMgrMockRecorder) UpdateActivePresentItem() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateActivePresentItem", reflect.TypeOf((*MockIActivePresentMgr)(nil).UpdateActivePresentItem))
}
