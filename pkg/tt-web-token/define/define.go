package define

import "errors"

type TokenInfo struct {
	Uid          uint64
	TerminalType uint32
	ExpiresAt    int64
	UserName     string
	Session      string
	Scopes       []string
}

type Validator interface {
	ValidateToke(token string) (info *TokenInfo, isValid bool, err error)
}

var (
	ErrUnsupportedAlg       = errors.New("unsupported algorithm")
	ErrInvalidToken         = errors.New("invalid token")
	ErrTokenExpired         = errors.New("token expired")
	ErrUnknownAppID         = errors.New("unknown app id")
	ErrInvalidKey           = errors.New("token can be decoded using 16-bytes AES key")
	ErrSessionClientNotInit = errors.New("session client not init")
)
