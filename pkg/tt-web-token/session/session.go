package session

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"reflect"
	"sync"
	"time"
	"unsafe"

	"github.com/opentracing/opentracing-go"
	"golang.52tt.com/clients/session"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tt-web-token/define"
	pb "golang.52tt.com/protocol/services/tokensvr"
)

const (
	tokenSize = 68
)

type TokenInfo struct {
	s1   [3]byte
	uid  [4]byte
	seed [32]byte
	s2   [2]byte
	sec  [4]byte
	msec [4]byte
	biz  [4]byte
	s3   [7]byte
}

func (t *TokenInfo) GetUid() uint32 {
	return binary.BigEndian.Uint32(t.uid[:])
}

func (t *TokenInfo) GetBiz() uint32 {
	return binary.BigEndian.Uint32(t.biz[:])
}

func (t *TokenInfo) GetCreateTime() time.Time {
	s, m := binary.BigEndian.Uint32(t.sec[:]), binary.BigEndian.Uint32(t.msec[:])
	return time.Unix(int64(s), int64(m)*1000000)
}

// 68 bytes
type tokenDecoder struct {
	b5 [4]byte
	u3 byte
	b4 [5]byte
	u2 byte
	b2 [13]byte
	u1 byte
	b1 [23]byte
	u4 byte
	b3 [19]byte
}

func bytesPointer(b []byte) unsafe.Pointer {
	return unsafe.Pointer((*reflect.SliceHeader)(unsafe.Pointer(&b)).Data)
}

func newTokenDecoder(b []byte) (*tokenDecoder, error) {
	if len(b) != tokenSize {
		return nil, define.ErrInvalidToken
	}
	p := bytesPointer(b)
	return (*tokenDecoder)(p), nil
}

func (tk *tokenDecoder) userID() uint32 {
	return (uint32(tk.u1) << 24) + (uint32(tk.u2) << 16) + (uint32(tk.u3) << 8) + uint32(tk.u4)
}

func (tk *tokenDecoder) decode(key []byte) (*TokenInfo, error) {
	if len(key) != 16 {
		return nil, define.ErrInvalidKey
	}

	buf := make([]byte, 64)
	copy(buf[0:], tk.b1[:])
	copy(buf[23:], tk.b2[:])
	copy(buf[36:], tk.b3[:])
	copy(buf[55:], tk.b4[:])
	copy(buf[60:], tk.b5[:])

	k := key[:]
	block, err := aes.NewCipher(k)
	if err != nil {
		return nil, err
	}

	iv := k
	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(buf, buf)
	atk := bytesPointer(buf)
	return (*TokenInfo)(atk), nil
}

func DecodeString(ctx context.Context, appID protocol.AppID, tokenStr string) (*TokenInfo, string, error) {
	tokenByte, err := base64.URLEncoding.DecodeString(tokenStr)
	if err != nil {
		return nil, "", err
	}
	return DecodeByte(ctx, appID, tokenByte)
}

func createTokenSeed(session string) []byte {
	seed := fmt.Sprintf("%s@WebTokenSeed", session)
	hash := md5.Sum([]byte(seed))
	hashHex := make([]byte, 32)
	hex.Encode(hashHex, hash[:])
	return hashHex[:16]
}

var (
	sessionClientOnce sync.Once
	sessionCli        *session.Client
)

func getSessionClient() *session.Client {
	sessionClientOnce.Do(func() {
		sessionCli = session.NewTracedClient(opentracing.GlobalTracer())
	})
	return sessionCli
}
func DecodeByte(ctx context.Context, appID protocol.AppID, tokenByte []byte) (*TokenInfo, string, error) {
	var decoder *tokenDecoder
	if d, err := newTokenDecoder(tokenByte); err != nil {
		return nil, "", err
	} else {
		decoder = d
	}

	var key []byte
	var sessionString string
	switch appID {
	case protocol.TT, protocol.ANDROID_TT, protocol.IPHONE_TT:
		// use session key
		sCli := getSessionClient()
		if nil == sCli {
			return nil, "", define.ErrSessionClientNotInit
		}
		if s, err := sCli.GetSession(ctx, decoder.userID(), uint32(protocol.TT)); err != nil {
			return nil, "", err
		} else {
			sessionString = s
			key = createTokenSeed(sessionString)
		}
	default:
		return nil, "", define.ErrUnknownAppID
	}

	var info *TokenInfo
	if i, err := decoder.decode(key); err != nil {
		return nil, "", err
	} else {
		info = i
	}

	if info.GetUid() != decoder.userID() {
		return nil, "", define.ErrInvalidToken
	}

	return info, sessionString, nil
}

type Validator struct {
}

var timeErrorThresholdIn = 90000 * time.Millisecond

func (v *Validator) ValidateToke(tokenStr string) (info *define.TokenInfo, isValid bool, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	//此处hardcode，只有TT客户端支持webtoken
	biz := pb.TokenBizType_TokenBiz_TT_WEB
	appId := protocol.TT

	var tokenInfo *TokenInfo
	var sessionKey string
	if ti, sk, terr := DecodeString(ctx, appId, tokenStr); terr != nil {
		log.Errorf("DecodeString err %+v, str %s", terr, tokenStr)
		err = terr
		return
	} else {
		tokenInfo = ti
		sessionKey = sk
	}

	if tokenInfo.GetBiz() != uint32(biz) {
		err = define.ErrInvalidToken
		return
	}

	if time.Since(tokenInfo.GetCreateTime()) >= timeErrorThresholdIn {
		err = define.ErrTokenExpired
		return
	}

	isValid = true

	info = new(define.TokenInfo)
	info.Uid = uint64(tokenInfo.GetUid())
	info.ExpiresAt = tokenInfo.GetCreateTime().Add(timeErrorThresholdIn).Unix()
	info.Session = sessionKey
	info.TerminalType = protocol.PackTerminalType(protocol.MOBILE, protocol.UNKNOWN_OS, protocol.TT)

	return
}
