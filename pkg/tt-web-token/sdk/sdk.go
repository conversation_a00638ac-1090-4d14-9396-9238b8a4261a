package sdk

import (
	"context"
	"encoding/base64"
	"time"

	"golang.52tt.com/clients/token.v2"
	"golang.52tt.com/pkg/tt-web-token/define"
	pb "golang.52tt.com/protocol/services/tokensvr"
)

type Validator struct {
	// nothing
}

func (v *Validator) ValidateToke(tokenStr string) (*define.TokenInfo, bool, error) {
	tokenByte, err := base64.URLEncoding.DecodeString(tokenStr)
	if err != nil {
		return nil, false, err
	}

	tokenClient, err := token.NewClient()
	if err != nil {
		return nil, false, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	uid, err := tokenClient.DecodeToken(ctx, tokenByte, uint32(pb.TokenBizType_TokenBiz_SDK), false)
	if err != nil {
		return nil, false, err
	}

	return &define.TokenInfo{
		Uid: uint64(uid),
	}, true, nil
}
