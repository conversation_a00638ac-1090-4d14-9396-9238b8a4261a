package conf

import (
	"context"
	"encoding/json"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/dyconfig"
)

type SensitiveArea struct {
	ProvinceCode uint32 `json:"province_code"`
	Province     string `json:"province"`
	CityCode     uint32 `json:"city_code"`
	City         string `json:"city"`
}
type SensitiveAreaConfig struct {
	SensitiveAreaList []*SensitiveArea `json:"sensitive_area_list"`
	VisibleSwitch     bool             `json:"visible_switch"`
}

func (v *SensitiveAreaConfig) UnmarshalBinary(data []byte) error {
	err := json.Unmarshal(data, v)
	if err != nil {
		return err
	}

	return nil
}

type business struct {
	dc *dyconfig.DynamicConfig
}

func newSensitiveAreaConfig(filename string) (*business, error) {
	ctx := context.Background()
	dc, err := dyconfig.NewDynamicConfig(context.Background(), filename, &SensitiveAreaConfig{})
	if nil != err {
		log.ErrorWithCtx(ctx, "new dy cfg failed, err: %v", err)
		return nil, err
	}
	return &business{
		dc: dc,
	}, nil
}

func (s *business) Get() *SensitiveAreaConfig {
	return s.dc.Get().(*SensitiveAreaConfig)
}
