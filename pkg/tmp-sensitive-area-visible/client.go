package tmp_sensitive_area_visible

import (
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/tmp-sensitive-area-visible/conf"
)

var cf conf.IConfig

func init() {
	var err error
	cf, err = conf.New()
	if err != nil {
		log.Errorf("init conf fail, err: %v", err)
		return
	}
}

// CheckAreaIsVisible 敏感地区可见
func CheckAreaIsVisible(province, city string) bool {
	if province == "" && city == "" {
		return false
	}

	areaConf := cf.GetSensitiveAreaConfig()

	if areaConf.SensitiveAreaList == nil || areaConf.VisibleSwitch {
		return true
	}

	list := areaConf.SensitiveAreaList
	for _, area := range list {
		if area.Province != "" && province == area.Province {
			return false
		}
		if area.City != "" && city == area.City {
			return false
		}
	}
	return true
}
