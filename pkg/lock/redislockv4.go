/*
 * @Description:
 * @Date: 2021-03-09 14:36:01
 * @LastEditors: liang
 * @LastEditTime: 2021-11-24 17:44:37
 */
package lock

import (
	"context"
	tyrRedis "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"math/rand"
	"sync/atomic"
	"time"

	"github.com/go-redis/redis"
)

type RedisLockV4 struct {
	store   tyrRedis.Client
	seconds uint32
	key     string
	id      string
}

func init() {
	rand.Seed(time.Now().UnixNano())
}

func NewRedisLockV4(store tyrRedis.Client, key string) *RedisLockV4 {
	return &RedisLockV4{
		store: store,
		key:   key,
		id:    randomStr(randomLen),
	}
}

func (rl *RedisLockV4) Acquire() (bool, error) {
	ctx := context.Background()
	val, _ := rl.store.Get(ctx, rl.key).Result()
	//续期
	if val == rl.id {
		rl.store.Set(ctx, rl.key, rl.id, time.Second*60+tolerance*time.Millisecond)
		return true, nil
	} else {
		//加锁
		bLock, err := rl.store.SetNX(ctx, rl.key, rl.id, time.Second*20+tolerance*time.Millisecond).Result()
		if bLock {
			return true, err
		}
	}
	return false, nil
}

func (rl *RedisLockV4) Release() (bool, error) {
	ctx := context.Background()
	resp, err := rl.store.Get(ctx, rl.key).Result()
	if err != nil {
		if err == redis.Nil {
			return true, nil
		}
		return false, err
	}
	if resp == rl.id {
		err = rl.store.Del(ctx, rl.key).Err()
		if err != nil {
			return false, err
		}
		return true, nil
	} else {
		return false, nil
	}
}

func (rl *RedisLockV4) SetExpire(seconds int) {
	atomic.StoreUint32(&rl.seconds, uint32(seconds))
}
