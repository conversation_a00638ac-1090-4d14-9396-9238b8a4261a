package token

import (
	"testing"
	"time"
)

const secret = "mykeyasdfasdfsad"

func Test_Generate(t *testing.T) {
	st,err:=getHandler().Generate(secret,12,time.Now().Add(time.Minute))
	if err!=nil {
		t.Error(err)
		return
	}
	t.Log(st)
}
func Test_Parser(t *testing.T) {
	tokenstr:=`eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE1NzM2OTg5MDgsInNpZCI6MTJ9.8t0iBw2j9SY5HtF4ArM9A7oHvHjR6efdvniGDE5DatA`
	info,err:=getHandler().Parser(tokenstr)
	if err!=nil{
		t.Error(err)
	}
	t.Log(info)
}

func Test_Valid(t *testing.T) {
	tokenstr:=`eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE1NzM2OTkzMDQsInNpZCI6MTJ9.ZKTuZeA6kK9_-IpKynf59fbza6NK1M9_yepANf5HPj4`
	err:=getHandler().Valid(tokenstr,secret)
	if err!=nil{
		t.Error(err)
	}
	t.Log("ok")
}
