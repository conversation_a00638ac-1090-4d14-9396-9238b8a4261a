package model

import (
	"errors"
	"golang.52tt.com/pkg/log"
	"time"
)

type SecretModel struct {
	Id          uint64            `json:"id"`
	AppId       string            `json:"app_id"`
	Secret      string            `json:"secret"`
	Des         string            `json:"des"`
	Permissions []PermissionModel `json:"permissions"`
	ExpiredAt   time.Time         `json:"expired_at"`
	CreatedAt   time.Time         `json:"created_at"`
}

func (a *SecretModel) GetResourceType() ResourceType {
	return Secret
}

func (a *SecretModel) Valid(act Action) error {
	svc, method := act.method()
	log.Debugf("valid act %s.%s", svc, method)
	for _, v := range a.Permissions {
		psvc, pmtd := v.method()
		if (psvc == svc || psvc == "*") && (pmtd == method || pmtd == "*") {
			log.Debugf("valid method %s=>%s ok:", act, v)
			return nil
		}
	}
	log.Debugf("valid method %s false", method)
	return errors.New("no access")
}
