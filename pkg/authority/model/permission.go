package model

import "strings"

type PermissionModel string

func newPermissionModel(per string) (PermissionModel, error) {
	return PermissionModel(per), nil
}

func (p PermissionModel) method() (string, string) {
	acts := strings.Split(string(p), ".")
	if len(acts) < 2 {
		return "*", strings.ToLower(acts[0])
	}
	return strings.ToLower(acts[0]), strings.ToLower(acts[1])
}

func (p PermissionModel) GetResourceType() ResourceType {
	return Permission
}
