package authority

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/authority/model"
	grpc2 "golang.52tt.com/pkg/authority/storage/grpc"
	"golang.52tt.com/pkg/authority/storage/grpc/client"
	token2 "golang.52tt.com/pkg/authority/token"
	"golang.52tt.com/pkg/log"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

type authority struct {
	appIdStorage *appIdStorage
	appid        string
}

func newAuthorityInstance(appid string, cert *client.GrpcCert, opts ...grpc.DialOption) (*authority, error) {
	cli, err := grpc2.NewGrpcStorage(cert, opts...)
	if err != nil {
		return nil, err
	}
	s, err := newAppIdStorage(appid, cli)
	if err != nil {
		return nil, err
	}
	return &authority{
		appIdStorage: s,
		appid:        appid,
	}, nil
}

func (a *authority) TestValid() {
	fmt.Println(a.findAppId(a.appid))
}

func (a *authority) valid(token string, per string) error {
	m, err := a.findAppId(a.appid)
	if err != nil {
		return err
	}
	if !m.Enable {
		log.Debugf("appid %s auth disable,ignore", m.AppId)
		return nil
	}
	tokenInfo, err := token2.ParserToken(token)
	if err != nil {
		log.Warnf("parse token[appid:%s] error :%s", a.appid, err)
		return err
	}
	secret, err := m.FindSecret(tokenInfo.Sid)
	if err != nil {
		log.Warnf("find secret[appid:%s,sid:%d] error:%s", a.appid, tokenInfo.Sid, err)
		return err
	}
	err = token2.ValidToken(token, secret.Secret)
	if err != nil {
		log.Warnf("valid token[appid:%s,sid:%d] error:%s", a.appid, tokenInfo.Sid, err)
		return err
	}
	err = secret.Valid(model.Action(per))
	if err != nil {
		log.Warnf("valid action[appid:%s,sid:%d,action:%s] error:%s", a.appid, tokenInfo.Sid, per, err)
		return err
	}
	log.Debugf("valid appid:%s sid:%d - ok", a.appid, tokenInfo.Sid)
	return nil
}

func (a *authority) handleAuthority(ctx context.Context, info *grpc.UnaryServerInfo) error {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return status.Errorf(codes.Unauthenticated, "自定义认证 Token 失败")
	}
	var (
		token string
	)
	if value, ok := md["token"]; ok {
		token = value[0]
	}
	return a.valid(token, info.FullMethod)
}

func (a *authority) findAppId(appid string) (m model.AppIdModel, err error) {
	v, err := a.appIdStorage.Find(appid)
	if err != nil {
		return m, err
	}
	m = *v
	return

}
