package utils

import (
	"golang.52tt.com/pkg/authority/model"
	pb "golang.52tt.com/protocol/services/authority-permission"
	"time"
)

func ConvertPbToAppId(id *pb.AppId) *model.AppIdModel {
	secrets := make(map[uint64]model.SecretModel)
	for _, v := range id.Secrets {
		secrets[v.Id] = *ConvertPbToSecret(v)
	}
	return &model.AppIdModel{
		Id:        id.Id,
		AppId:     id.AppId,
		Des:       id.Des,
		Secrets:   secrets,
		Enable:    id.Enable,
		CreatedAt: time.Time{},
	}
}

func ConvertPbToSecret(v *pb.Secret) *model.SecretModel {
	ps := []model.PermissionModel{}
	for _, ss := range v.Permissions {
		ps = append(ps, model.PermissionModel(ss))
	}
	return &model.SecretModel{
		Id:          v.Id,
		AppId:       v.AppId,
		Secret:      v.Secret,
		Des:         v.Des,
		Permissions: ps,
		ExpiredAt:   time.Unix(int64(v.ExpiredAt), 0),
		CreatedAt:   time.Unix(int64(v.CreatedAt), 0),
	}
}

func ConvertAppIdModelToPB(m *model.AppIdModel) *pb.AppId {
	secrets := make([]*pb.Secret, 0)
	for _, v := range m.Secrets {
		secrets = append(secrets, ConvertSecretModelToPB(&v))
	}
	return &pb.AppId{
		Id:        m.Id,
		AppId:     m.AppId,
		Des:       m.Des,
		Secrets:   secrets,
		Enable:    m.Enable,
		CreatedAt: uint64(m.CreatedAt.Unix()),
	}
}
func ConvertSecretModelToPB(v *model.SecretModel) *pb.Secret {
	ps := []string{}
	for _, ss := range v.Permissions {
		ps = append(ps, string(ss))
	}
	return &pb.Secret{
		Id:          v.Id,
		AppId:       v.AppId,
		Secret:      v.Secret,
		Des:         v.Des,
		ExpiredAt:   uint64(v.ExpiredAt.Unix()),
		CreatedAt:   uint64(v.CreatedAt.Unix()),
		Permissions: ps,
	}
}
