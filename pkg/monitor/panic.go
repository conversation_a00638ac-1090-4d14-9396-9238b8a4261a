package monitor

import (
	"fmt"
	"strings"
	"sync"
	"time"
)

var panicMonitor *PanicMonitor

func init() {
	panicMonitor = &PanicMonitor{
		LastTime: time.Now().Unix(),
		errors: map[string]int64{},
	}
	RegisterMonitor(panicMonitor)
}

func RecordPanic(err string) {
	if strings.Contains(err, "shm") && strings.Contains(err, "zapcore") {
		return
	}
	panicMonitor.Record(err)
}

// 记录 PANIC 进行报警
type PanicMonitor struct {
	errors   map[string]int64
	lock     sync.Mutex
	LastTime int64
}

func (this *PanicMonitor) MonitorStatus() ModuleStatus {
	if len(this.errors) == 0 {
		return OKStatus("panic_msg")
	}
	this.lock.Lock()
	defer this.lock.Unlock()

	msg := ""
	for name, times := range this.errors {
		msg = msg + fmt.Sprintf("[error:\n%s ] \n [times : %d] \n =========================\n", name, times)
	}

	now := time.Now().Unix()
	if now-this.LastTime > 60 {
		this.errors = make(map[string]int64)
		this.LastTime = now
	}

	return ModuleStatus{
		Name:   "panic_msg",
		Status: STATUS_CRITICAL,
		Msg:    msg,
		Rule:   rule,
		Events: []string{EVENT_WEIXIN},
	}
}
func (this *PanicMonitor) Record(err string) {
	now := time.Now().Unix()
	this.lock.Lock()
	defer this.lock.Unlock()
	if now-this.LastTime > 30 {
		this.errors = make(map[string]int64)
		this.LastTime = now
	}
	this.errors[err]++
}