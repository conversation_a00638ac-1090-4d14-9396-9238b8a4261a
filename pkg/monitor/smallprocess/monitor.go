package monitor

import (
	"fmt"
	grpclb "golang.52tt.com/pkg/foundation/grpc/load_balance"
	"golang.52tt.com/pkg/monitor"
)

func init() {
	Regist()
}

type VisualMonitor struct{}

func (v VisualMonitor) Text() string {
	smallPocessMonitor := 0
	bigPocessMonitor := 0
	bSmall := grpclb.GetEtcdProcess().IsSmallProcess()
	if bSmall {
		smallPocessMonitor = 1
	} else {
		smallPocessMonitor = 0
	}
	bBig := grpclb.GetEtcdProcess().IsBigProcess()
	if bBig {
		bigPocessMonitor = 1
	} else {
		bigPocessMonitor = 0
	}
	var output = fmt.Sprintf(`b:%d,s:%d`, bigPocessMonitor, smallPocessMonitor)
	return output
}

func Regist() {
	monitor.RegistVisualMonitor(VisualMonitor{})
	return
}
