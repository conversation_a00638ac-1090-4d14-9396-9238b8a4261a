package avatar

import (
	"fmt"
	"os"
	"testing"

	"github.com/marspere/goencrypt"
	"golang.52tt.com/pkg/log"
)

func init() {
	log.Init(&log.Config{Level: log.DebugLevel, Port: 8078})
}
func Test_AvatarHelper(t *testing.T) {

	srcAcc := "tt110318750"
	cryptAcc := EncodeSafeAcc(srcAcc, 3)
	fmt.Printf("src %s cryptAcc %s\n", srcAcc, cryptAcc)

	fmt.Printf("Is safeUrl? %v\n", IsSafeUrl(cryptAcc))
	dstAcc, err := DecodeSafeAcc(cryptAcc)
	fmt.Printf("dstAcc %s err %v\n", dstAcc, err)
}

func Test_ExampleDESEncryptAndDecrypt(t *testing.T) {
	// des encryption
	cipher := goencrypt.NewDESCipher([]byte("12345678"), []byte(""), goencrypt.ECBMode, goencrypt.Pkcs7, goencrypt.PrintBase64)
	cipherText, err := cipher.DESEncrypt([]byte("hello world"))
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	fmt.Println(cipherText)

	// des decryption
	plainText, err := cipher.DESDecrypt(cipherText)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	fmt.Println(plainText)
}

func Test_ExampleTripleDESEncryptAndDecrypt(t *testing.T) {
	// triple des encryption
	cipher := goencrypt.NewDESCipher([]byte("12345678abcdefghijklmnop"), []byte("abcdefgh"), goencrypt.CBCMode, goencrypt.Pkcs7, goencrypt.PrintBase64)
	cipherText, err := cipher.TripleDESEncrypt([]byte("hello world"))
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	fmt.Println(cipherText)

	// triple des decryption
	plainText, err := cipher.TripleDESDecrypt(cipherText)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	fmt.Println(plainText)
}
