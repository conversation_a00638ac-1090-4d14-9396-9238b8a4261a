/*
 * @Description:
 * @Date： 2021-12-01 12:06:06
 * @LastEditors: Evins
 * @LastEditTime: 2021-12-01 12:06:12
 */
package avatar

import (
	"encoding/base64"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/forgoer/openssl"
	"github.com/marspere/goencrypt"
	"golang.52tt.com/pkg/log"
)

var (
	errIsNotSafeAcc   = errors.New("it's not safe account")
	errInvalidSafeAcc = errors.New("invalid safe account")
	DESKEY            = "********"
	DESKEYV2          = "63eb89ad"
	TRIPLEDESKEY      = "63eb89adaf4189fdeac7eba4"
	DESIV             = "a2252d0e"
	SafePre           = "safe"

	dyCfg *AvatarKeyConfigLoader
)

const (
	Base64    = 1
	DES       = 2
	DESV2     = 3
	TripleDES = 4
)

const (
	configFile = "/data/oss/conf-center/tt/avatar-api-key.json"
	// configFile = "/home/<USER>/project/quicksilver/pkg/avatar/avatar-api-key.json"
)

func init() {
	var err error
	dyCfg, err = NewAvatarKeyConfigLoader(configFile)
	if nil != err {
		log.Errorf("dyCfg start failed, err: %s", err)
		return
	}

	log.Infof("dyCfg is %+v", dyCfg.configLoader.Get())
}

func SetDesKey(desKey string) {
	DESKEY = desKey
}

func SetDesKeyV2(desKeyV2 string) {
	DESKEY = desKeyV2
}

func SetTripleDesKey(tripleDesKey string) {
	TRIPLEDESKEY = tripleDesKey
}

func SetSafePrefix(safePrefix string) {
	SafePre = safePrefix
}

func SetDesIV(desIV string) {
	DESIV = desIV
}

func IsSafeUrl(account string) bool {
	if account != "" {
		return strings.HasPrefix(account, fmt.Sprintf("%s_", SafePre))
	}
	return false
}

func EncodeSafeAcc(account string, safeType uint32) string {
	switch safeType {
	case Base64:
		return fmt.Sprintf("%s_%d_%s", SafePre, safeType, base64.StdEncoding.EncodeToString([]byte(account)))
	case DES:
		log.Debugf("DES encode %s %d key %s", account, safeType, DESKEY)
		cipher := goencrypt.NewDESCipher([]byte(DESKEY), []byte(""), goencrypt.ECBMode, goencrypt.Pkcs7, goencrypt.PrintBase64)
		cipherText, err := cipher.DESEncrypt([]byte(account))
		if err != nil {
			log.Errorf("DESEncrypt err %v", err)
		}
		log.Debugf("cipherText is %s", cipherText)
		return fmt.Sprintf("%s_%d_%s", SafePre, safeType, cipherText)
	case DESV2:
		log.Debugf("DES encode %s %d key %s", account, safeType, DESKEY)
		cipherTextTmp, err := openssl.DesCBCEncrypt([]byte(account), []byte(DESKEYV2), []byte(DESIV), openssl.PKCS7_PADDING)
		if err != nil {
			log.Errorf("DESEncrypt err %v", err)
		}
		cipherText := base64.URLEncoding.EncodeToString(cipherTextTmp)
		log.Debugf("cipherText is %s", cipherText)
		return fmt.Sprintf("%s_%d_%s", SafePre, safeType, cipherText)
	case TripleDES:
		log.Debugf("DES encode %s %d key %s", account, safeType, TRIPLEDESKEY)
		cipher := goencrypt.NewDESCipher([]byte(TRIPLEDESKEY), []byte(DESIV), goencrypt.ECBMode, goencrypt.Pkcs7, goencrypt.PrintBase64)
		cipherText, err := cipher.TripleDESEncrypt([]byte(account))
		if err != nil {
			log.Errorf("DESEncrypt err %v", err)
		}
		log.Debugf("cipherText is %s", cipherText)
		return fmt.Sprintf("%s_%d_%s", SafePre, safeType, cipherText)
	default:
		log.Debugf("Without Encrypt use origin acc %s safeType %d", account, safeType)
		return account
	}
}

func DecodeSafeAcc(cryAccount string) (string, error) {
	if !IsSafeUrl(cryAccount) {
		log.Errorf("Acc is not safe url! acc %s", cryAccount)
		return cryAccount, errIsNotSafeAcc
	}

	splitStrs := strings.SplitN(cryAccount, "_", 3)
	if len(splitStrs) != 3 {
		log.Errorf("Inlegal account %s", cryAccount)
		return cryAccount, errInvalidSafeAcc
	}

	safeType, err := strconv.Atoi(splitStrs[1])
	if err != nil {
		log.Errorf("DecodeSafeAcc err! acc %s err %v", cryAccount, err)
		return cryAccount, err
	}

	cryptAcc := splitStrs[2]
	switch safeType {
	case Base64:
		acc, _ := base64.StdEncoding.DecodeString(cryptAcc)
		return string(acc), nil
	case DES:
		cipher := goencrypt.NewDESCipher([]byte(DESKEY), []byte(""), goencrypt.ECBMode, goencrypt.Pkcs7, goencrypt.PrintBase64)
		acc, _ := cipher.DESDecrypt(cryptAcc)
		return acc, nil
	case DESV2:
		cipherText, _ := base64.URLEncoding.DecodeString(cryptAcc)
		acc, _ := openssl.DesCBCDecrypt([]byte(cipherText), []byte(DESKEYV2), []byte(DESIV), openssl.PKCS7_PADDING)
		return string(acc), nil
	case TripleDES:
		cipher := goencrypt.NewDESCipher([]byte(TRIPLEDESKEY), []byte(DESIV), goencrypt.ECBMode, goencrypt.Pkcs7, goencrypt.PrintBase64)
		acc, _ := cipher.TripleDESDecrypt(cryptAcc)
		return acc, nil
	default:
		log.Debugf("Acc %s Without safeType %d not decode", cryAccount, safeType)
		return cryptAcc, nil
	}
}
