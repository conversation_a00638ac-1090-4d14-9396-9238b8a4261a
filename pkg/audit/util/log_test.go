package util

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"strconv"
	"testing"
)

func TestLog(t *testing.T) {
	ctx := context.Background()
	if len(flag.Args()) < 17 {
		fmt.Println("not enough args")
		return
	}

	iarg6, _ := strconv.ParseUint(flag.Arg(6), 10, 64)
	iarg14, _ := strconv.ParseUint(flag.Arg(14), 10, 64)
	iarg15, _ := strconv.ParseUint(flag.Arg(15), 10, 64)
	iarg16, _ := strconv.ParseUint(flag.Arg(16), 10, 64)

	fmt.Println(SyncTextAuditRejectLogStr(ctx, flag.Arg(0), flag.Arg(1), flag.Arg(2), flag.Arg(3), flag.Arg(4), flag.Arg(5), errors.New("dd"), uint32(iarg6)))

	fmt.Println(AsyncSendAuditLogStr(ctx, flag.Arg(0), flag.Arg(1), flag.Arg(2), flag.Arg(3), flag.Arg(4), flag.Arg(5),
		flag.Arg(6), flag.Arg(7), flag.Arg(8), flag.Arg(9), flag.Arg(10), []string{flag.Arg(11)}))

	fmt.Println(AsyncAuditResultLogStr(ctx, flag.Arg(0), flag.Arg(1), flag.Arg(2), flag.Arg(3), flag.Arg(4), flag.Arg(5),
		flag.Arg(6), flag.Arg(7), flag.Arg(8), flag.Arg(9), flag.Arg(10), flag.Arg(11), flag.Arg(12), []string{flag.Arg(13)},
		uint32(iarg14), int32(iarg15), int32(iarg16)))
}
