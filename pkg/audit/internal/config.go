package internal

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/runtime/v2/filters/pkg"
	"time"
)

const (
	AuditResultConfigPath = "/data/oss/conf-center/tt/audit-sdk.json"
	//AuditResultConfigPath = "D:\\TT-go\\quicksilver\\pkg\\audit\\audit-sdk.json"
)

type KafkaConfigLoader struct {
	configLoader *pkg.ConfigLoader
}

var kafkaConfigLoader *KafkaConfigLoader

type ServiceConfig struct {
	ServiceName     string `json:"service_name"`
	ConsumerGroupId string `json:"consumer_group_id"`
}

type AuditResultConfig struct {
	CybrosKafka config.KafkaConfig `json:"cybros_kafka"`
	ServiceList []ServiceConfig    `json:"service_list"`
}

func (c *AuditResultConfig) UnmarshalBinary(data []byte) error {

	ctx := context.Background()
	err := json.Unmarshal(data, c)
	if err != nil {
		log.ErrorWithCtx(ctx, "AuditResultConfig UnmarsharlBinary failed, err:%v, data:%+v", err, data)
		return err
	}

	return nil
}

func init() {

	ctx := context.Background()
	cfg := &AuditResultConfig{}

	configLoader, err := pkg.NewConfigLoaderV2(ctx, AuditResultConfigPath, cfg, true, time.Second*10)
	if err != nil {
		log.ErrorWithCtx(ctx, "AuditResultConfig NewConfigLoaderV2 failed, err:%v, path:%s", err, AuditResultConfigPath)
		return
	}

	kafkaConfigLoader = &KafkaConfigLoader{configLoader: configLoader}
}

func GetAuditResultCfg() *AuditResultConfig {
	v := kafkaConfigLoader.configLoader.Get()
	cfg, _ := v.(*AuditResultConfig)
	if cfg == nil {
		return nil
	}
	return cfg
}

func GetGroupIdByServiceName(cfg *AuditResultConfig, serviceName string) string {
	if cfg == nil {
		return serviceName
	}
	for _, service := range cfg.ServiceList {
		if service.ServiceName == serviceName {
			return service.ConsumerGroupId
		}
	}

	return serviceName
}
