package channel_msg

import (
	"context"
	"os"
	"path/filepath"
	"time"

	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"

	"google.golang.org/grpc"

	channelMsgExpress "golang.52tt.com/clients/channel-msg-express"
	"golang.52tt.com/clients/channelim"
	"golang.52tt.com/pkg/protocol"
	channelPB "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	channelMsgExpressPB "golang.52tt.com/protocol/services/channel-msg-express"
	channelIMPB "golang.52tt.com/protocol/services/channelim"
)

var callerServerName = "unknown"

const maxUidListLength = 10

func init() {
	if len(os.Args) > 0 {
		callerServerName = filepath.Base(os.Args[0])
	}
}

type Sender struct {
	channelMsgExpressClient *channelMsgExpress.Client
	channelIMClient         *channelim.Client
}

func newSenderImpl(opts ...grpc.DialOption) *Sender {
	msgExpressCli, _ := channelMsgExpress.NewClient(opts...)
	imCli := channelim.NewClient(opts...)
	return &Sender{
		channelMsgExpressClient: msgExpressCli,
		channelIMClient:         imCli,
	}
}

func (s *Sender) SingleCast(ctx context.Context,
	msg *PushMessage, uidList []uint32, opt *SingleCastOpt) protocol.ServerError {
	if err := checkSingleCastParam(msg, uidList); err != nil {
		return err
	}
	return s.channelMsgExpressClient.PushToUsers(ctx, buildPushToUsersReq(msg, uidList, opt))
}

func (s *Sender) MultiCast(ctx context.Context,
	msg *PushMessage, needPersistence bool, opt *MultiCastOpt) protocol.ServerError {
	if err := checkMultiCastParam(msg); err != nil {
		return err
	}
	if needPersistence {
		// 只持久化
		seqID, _, err := s.channelIMClient.SendCommonMessage(ctx, msg.OperatorUid, msg.ChannelId,
			createChannelCommonMsg(msg), false, msg.MsgType)
		if err != nil {
			log.ErrorWithCtx(ctx, "channelIMClient.SendCommonMessage failed:%v, opUId:%d, cid:%d",
				err, msg.OperatorUid, msg.ChannelId)
			return err
		}
		if seqID > 0 {
			// 持久化成功，广播推送ChannelMsg
			return s.channelMsgExpressClient.SendChannelMsgV2(ctx, buildSendChannelMsgReq(seqID, msg, opt))
		}
	}
	// 不需要持久化或者持久化返回seqID为零时，发送ChannelBroadcastMsg
	return s.channelMsgExpressClient.SendChannelBroadcastMsgV2(ctx, buildSendChannelBroadcastMsgReq(msg, opt))
}

func createChannelCommonMsg(msg *PushMessage) *channelIMPB.ChannelCommonMsg {
	cMsg := &channelIMPB.ChannelCommonMsg{
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  msg.ChannelId,
		Type:         msg.MsgType,
		Content:      msg.Content,
		PbOptContent: msg.PBContent,
	}
	if msg.Options != nil {
		cMsg.AttContent = msg.Options.AttContent
		if msg.Options.OperatorUserProfile != nil {
			cMsg.FromUid = msg.Options.OperatorUserProfile.Uid
			cMsg.FromAccount = msg.Options.OperatorUserProfile.Account
			cMsg.FromNick = msg.Options.OperatorUserProfile.Nickname
			cMsg.FromSex = int32(msg.Options.OperatorUserProfile.Sex)
			if msg.Options.OperatorUserProfile.Privilege != nil &&
				msg.Options.OperatorUserProfile.Privilege.Account != "" {
				cMsg.FromProfile = &channelIMPB.UserProfile{
					Privilege: &channelIMPB.UserPrivilege{
						Account:  msg.Options.OperatorUserProfile.Privilege.Account,
						Nickname: msg.Options.OperatorUserProfile.Privilege.Nickname,
						Type:     msg.Options.OperatorUserProfile.Privilege.Type,
						Options:  msg.Options.OperatorUserProfile.Privilege.Options,
					},
				}
			}
		}
		if msg.Options.BeOperatedUserProfile != nil {
			cMsg.TargetUid = msg.Options.BeOperatedUserProfile.Uid
			cMsg.TargetAccount = msg.Options.BeOperatedUserProfile.Account
			cMsg.TargetNick = msg.Options.BeOperatedUserProfile.Nickname
			cMsg.TargetSex = int32(msg.Options.BeOperatedUserProfile.Sex)
			if msg.Options.BeOperatedUserProfile.Privilege != nil &&
				msg.Options.BeOperatedUserProfile.Privilege.Account != "" {
				cMsg.TargetProfile = &channelIMPB.UserProfile{
					Privilege: &channelIMPB.UserPrivilege{
						Account:  msg.Options.BeOperatedUserProfile.Privilege.Account,
						Nickname: msg.Options.BeOperatedUserProfile.Privilege.Nickname,
						Type:     msg.Options.BeOperatedUserProfile.Privilege.Type,
						Options:  msg.Options.BeOperatedUserProfile.Privilege.Options,
					},
				}
			}
		}
	}
	log.Debugf("ChannelCommonMsg: %s", utils.ToJson(cMsg))
	return cMsg
}

func createChannelMsg(seqID uint32, msg *PushMessage) *channelPB.ChannelMsg {
	cMsg := &channelPB.ChannelMsg{
		Seq:           seqID,
		Time:          uint64(time.Now().Unix()),
		ToChannelId:   msg.ChannelId,
		ToChannelType: msg.ChannelType,
		Type:          msg.MsgType,
		Content:       msg.Content,
		PbOptContent:  msg.PBContent,
	}
	if msg.Options != nil {
		cMsg.AttContent = msg.Options.AttContent
		cMsg.PrintUpgradeTips = msg.Options.PrintUpgradeTips
		cMsg.ChannelBoxId = msg.Options.ChannelBoxID
		if msg.Options.OperatorUserProfile != nil {
			cMsg.FromUid = msg.Options.OperatorUserProfile.Uid
			cMsg.FromAccount = msg.Options.OperatorUserProfile.Account
			cMsg.FromNick = msg.Options.OperatorUserProfile.Nickname
			cMsg.FromSex = int32(msg.Options.OperatorUserProfile.Sex)
			cMsg.FromUserProfile = msg.Options.OperatorUserProfile
		}
		if msg.Options.BeOperatedUserProfile != nil {
			cMsg.TargetUid = msg.Options.BeOperatedUserProfile.Uid
			cMsg.TargetAccount = msg.Options.BeOperatedUserProfile.Account
			cMsg.TargetNick = msg.Options.BeOperatedUserProfile.Nickname
			cMsg.TargetSex = int32(msg.Options.BeOperatedUserProfile.Sex)
			cMsg.TargetUserProfile = msg.Options.BeOperatedUserProfile
		}
	}
	log.Debugf("ChannelMsg: %s", utils.ToJson(cMsg))
	return cMsg
}

func createChannelBroadcastMsg(msg *PushMessage) *channelPB.ChannelBroadcastMsg {
	cMsg := &channelPB.ChannelBroadcastMsg{
		Time:          uint64(time.Now().Unix()),
		ToChannelId:   msg.ChannelId,
		ToChannelType: msg.ChannelType,
		Type:          msg.MsgType,
		Content:       []byte(msg.Content),
		PbOptContent:  msg.PBContent,
	}
	if msg.Options != nil {
		cMsg.AttContent = msg.Options.AttContent
		cMsg.PrintUpgradeTips = msg.Options.PrintUpgradeTips
		cMsg.ChannelBoxId = msg.Options.ChannelBoxID
		if msg.Options.OperatorUserProfile != nil {
			cMsg.FromUid = msg.Options.OperatorUserProfile.Uid
			cMsg.FromAccount = msg.Options.OperatorUserProfile.Account
			cMsg.FromNick = msg.Options.OperatorUserProfile.Nickname
			cMsg.FromSex = int32(msg.Options.OperatorUserProfile.Sex)
			cMsg.FromUserProfile = msg.Options.OperatorUserProfile
		}
		if msg.Options.BeOperatedUserProfile != nil {
			cMsg.TargetUid = msg.Options.BeOperatedUserProfile.Uid
			cMsg.TargetAccount = msg.Options.BeOperatedUserProfile.Account
			cMsg.TargetNick = msg.Options.BeOperatedUserProfile.Nickname
			cMsg.TargetSex = int32(msg.Options.BeOperatedUserProfile.Sex)
			cMsg.TargetUserProfile = msg.Options.BeOperatedUserProfile
		}
	}
	log.Debugf("ChannelBroadcastMsg: %s", utils.ToJson(cMsg))
	return cMsg
}

func buildSendChannelMsgReq(seqID uint32, msg *PushMessage, opt *MultiCastOpt) *channelMsgExpressPB.SendChannelMsgReq {
	req := &channelMsgExpressPB.SendChannelMsgReq{
		Msg:        createChannelMsg(seqID, msg),
		ServerName: callerServerName,
	}
	if opt != nil {
		req.SkipUidList = opt.SkipUids
		req.PushOpts = &channelMsgExpressPB.PushOption{
			TerminalTypeList:            opt.TerminalTypeList,
			TerminalTypePolicy:          opt.TerminalTypePolicy,
			ProxyNotificationType:       opt.ProxyNotificationType,
			ProxyNotificationPolicy:     opt.ProxyNotificationPolicy,
			ProxyNotificationExpireTime: opt.ProxyNotificationExpireTime,
		}
	}
	return req
}

func buildSendChannelBroadcastMsgReq(msg *PushMessage,
	opt *MultiCastOpt) *channelMsgExpressPB.SendChannelBroadcastMsgReq {
	req := &channelMsgExpressPB.SendChannelBroadcastMsgReq{
		Msg:        createChannelBroadcastMsg(msg),
		ServerName: callerServerName,
	}
	if opt != nil {
		req.SkipUidList = opt.SkipUids
		req.PushOpts = &channelMsgExpressPB.PushOption{
			TerminalTypeList:            opt.TerminalTypeList,
			TerminalTypePolicy:          opt.TerminalTypePolicy,
			ProxyNotificationType:       opt.ProxyNotificationType,
			ProxyNotificationPolicy:     opt.ProxyNotificationPolicy,
			ProxyNotificationExpireTime: opt.ProxyNotificationExpireTime,
		}
	}
	return req
}

func buildPushToUsersReq(msg *PushMessage, uidList []uint32, opt *SingleCastOpt) *channelMsgExpressPB.PushToUsersReq {
	req := &channelMsgExpressPB.PushToUsersReq{
		Msg:        createChannelBroadcastMsg(msg),
		ServerName: callerServerName,
		UidList:    uidList,
	}
	if opt != nil {
		req.PushOpts = &channelMsgExpressPB.PushOption{
			TerminalTypeList:            opt.TerminalTypeList,
			TerminalTypePolicy:          opt.TerminalTypePolicy,
			ProxyNotificationType:       opt.ProxyNotificationType,
			ProxyNotificationPolicy:     opt.ProxyNotificationPolicy,
			ProxyNotificationExpireTime: opt.ProxyNotificationExpireTime,
		}
		req.Sequence = opt.Sequence
	}
	return req
}

func checkSingleCastParam(msg *PushMessage, uidList []uint32) protocol.ServerError {
	if err := checkPushMessage(msg); err != nil {
		return err
	}
	if len(uidList) == 0 || len(uidList) > maxUidListLength {
		return protocol.NewServerError(status.ErrRequestParamInvalid, "uid list invalid")
	}
	return nil
}

func checkMultiCastParam(msg *PushMessage) protocol.ServerError {
	return checkPushMessage(msg)
}

func checkPushMessage(msg *PushMessage) protocol.ServerError {
	if msg == nil || msg.ChannelId == 0 || msg.MsgType == 0 {
		return protocol.NewServerError(status.ErrRequestParamInvalid, "push message invalid")
	}
	return nil
}
