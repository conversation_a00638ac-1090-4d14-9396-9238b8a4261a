/*
 * @Description:
 * @Date: 2022-01-12 19:10:56
 * @LastEditors: liang
 * @LastEditTime: 2022-01-13 15:28:12
 */
package iopother

import (
	"encoding/json"
	"fmt"
	"testing"
)

func GetMiniGameType(url string, id int64, caller string, secretKey string, data interface{}) error {

	type Entity struct {
		FavoriteGameRecentRank   string `json:"favorite_game_recent_rank"`
		IsPlayGameLast7d         string `json:"is_play_game_last_7d"`
		FavoriteGameTag          string `json:"favorite_game_tag"`
		EntityID                 string `json:"entity_id"`
		FavoriteGameRecentResult string `json:"favorite_game_recent_result"`
	}

	cli := NewIopotherClient(caller, secretKey)
	body, err := cli.PostReturnIopotherRespData(id, url, data)
	if err != nil {
		return err
	}
	st := &Entity{}
	json.Unmarshal(body, st)
	fmt.Printf("%v %s", st, string(body))
	return err
}

func TestGetMiniGameType(t *testing.T) {

	type Data struct {
		AppId        string   `json:"appId"`
		EntityDomain string   `json:"entityDomain"`
		EntityId     string   `json:"entityId"`
		LabelNames   []string `json:"labelNames"`
	}

	err := GetMiniGameType("http://job.data.ttyuyin.com:8060/lpm-api/portrait/v1/singleQuery", 1641977220, "tt-deeplink-a-test", "xhpOwDX8MYguOmNPeQMsei6askLBM2So", Data{
		AppId:        "ttvoice",
		EntityDomain: "user",
		EntityId:     "10373",
		LabelNames:   []string{"is_play_game_last_7d", "favorite_game_tag", "favorite_game_recent_result", "favorite_game_recent_rank"},
	})
	if err != nil {
		t.Fatal(err)
	}
}

func GetMiniGameType2(url string, id int64, caller string, secretKey string, data interface{}) error {

	type MiniGameTypeRespData struct {
		FavoriteGameRecentRank   string `json:"favoriteGameRecentRank"`
		FavoriteGameRecentResult string `json:"favoriteGameRecentResult"`
		FavoriteGameTag          string `json:"favoriteGameTag"`
		IsPlayGameLast7D         string `json:"isPlayGameLast7d"`
		ServerTime               string `json:"serverTime"`
	}
	cli := NewIopotherClient(caller, secretKey)
	body, err := cli.PostReturnIopotherRespData(id, url, data)
	if err != nil {
		return err
	}
	st := &MiniGameTypeRespData{}
	json.Unmarshal(body, st)
	//fmt.Printf("%+v %s", st, string(body))
	return err
}

func TestGetMiniGameType2(t *testing.T) {
	var (
		appid       = "ttvoice"
		gameInfoTag = "is_play_game_last_7d,favorite_game_tag,favorite_game_recent_result,favorite_game_recent_rank"
	)

	type MiniGameTypeReqData struct {
		AppID string `json:"appId"`
		Tags  string `json:"tags"`
		UID   string `json:"uid"`
	}

	err := GetMiniGameType2("http://job.data.ttyuyin.com:8253/iop-other/ugc/tags", 1641977220, "tt-deeplink-a-test", "xhpOwDX8MYguOmNPeQMsei6askLBM2So", MiniGameTypeReqData{
		UID:   "10373",
		Tags:  gameInfoTag,
		AppID: appid,
	})
	if err != nil {
		t.Fatal(err)
	}
}
