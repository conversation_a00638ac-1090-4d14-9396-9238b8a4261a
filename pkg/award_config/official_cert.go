package award_config

import (
	"context"
	officialcert_go "golang.52tt.com/clients/officialcert-go"
	"golang.52tt.com/pkg/log"
	"google.golang.org/grpc"
	"strconv"
	"sync"
	"time"
)

// AwardInfo在勋章配置中的实现
// 需要内存缓存

// OfficialCertAwardInfo 麦位框配置中的奖励信息
type OfficialCertAwardInfo struct {
	// 奖励信息的缓存
	awardInfoCache map[string]*CommonAwardInfo
	lock           sync.RWMutex

	// client
	OfficialCertCli officialcert_go.IClient
}

// NewOfficialCertAwardInfo 创建麦位框配置中的奖励信息
func NewOfficialCertAwardInfo() *OfficialCertAwardInfo {
	OfficialCertCli := officialcert_go.NewIClient(grpc.WithBlock())
	awardInfo := &OfficialCertAwardInfo{
		awardInfoCache:  make(map[string]*CommonAwardInfo),
		OfficialCertCli: OfficialCertCli,
	}

	// 初始化奖励信息
	awardInfo.freshAwardInfo()

	// 之后每隔一段时间刷新一次奖励信息
	go func() {
		for {
			awardInfo.freshAwardInfo()
			time.Sleep(10 * time.Minute)
		}
	}()

	return awardInfo
}

// initAwardInfo 初始化奖励信息
func (a *OfficialCertAwardInfo) freshAwardInfo() {
	a.lock.Lock()
	defer a.lock.Unlock()

	// 从麦位框配置中获取奖励信息
	// 1. 获取麦位框配置中的奖励信息
	cfgList, err := a.OfficialCertCli.ListUserOfficialCertV2(context.Background())
	if err != nil {
		log.ErrorWithCtx(context.Background(), "freshAwardInfo GetOfficialCertConfigAll err:%v", err)
		return
	}

	// 2. 将奖励信息放入缓存
	for _, cfg := range cfgList.GetCertList() {
		awardInfo := &CommonAwardInfo{
			Id:           strconv.Itoa(int(cfg.GetId())),
			Name:         cfg.GetCertifySpecialEffectTitle(),
			IconUrl:      cfg.GetCertifySpecialEffectIcon(),
			SourceStruct: cfg,
		}
		a.awardInfoCache[strconv.Itoa(int(cfg.GetId()))] = awardInfo
	}

	log.InfoWithCtx(context.Background(), "freshAwardInfo OfficialCertAwardInfo success len %d", len(a.awardInfoCache))

	return
}

// GetAwardInfoById 通过id获取奖励信息
func (a *OfficialCertAwardInfo) GetAwardInfoById(ctx context.Context, id string) (*CommonAwardInfo, error) {
	a.lock.RLock()
	defer a.lock.RUnlock()

	out := &CommonAwardInfo{}
	if v, ok := a.awardInfoCache[id]; ok {
		out = v
	}
	return out, nil
}

// GetAwardInfoAll 获取所有奖励信息
func (a *OfficialCertAwardInfo) GetAwardInfoAll(ctx context.Context) (map[string]*CommonAwardInfo, error) {
	a.lock.RLock()
	defer a.lock.RUnlock()

	return a.awardInfoCache, nil
}

// GetAwardInfoByIdList 通过id列表获取奖励信息
func (a *OfficialCertAwardInfo) GetAwardInfoByIdList(ctx context.Context, idList []string) (map[string]*CommonAwardInfo, error) {
	a.lock.RLock()
	defer a.lock.RUnlock()

	awardInfoMap := make(map[string]*CommonAwardInfo)
	for _, id := range idList {
		awardInfoMap[id] = a.awardInfoCache[id]
	}
	return awardInfoMap, nil
}
