package award_config

import (
	"context"
	user_decoration "golang.52tt.com/clients/user-decoration"
	"golang.52tt.com/pkg/log"
	user_decoration2 "golang.52tt.com/protocol/services/user-decoration"
	"google.golang.org/grpc"
	"sync"
	"time"
)

// AwardInfo在勋章配置中的实现
// 需要内存缓存

// FloatAwardInfo 麦位框配置中的奖励信息
type FloatAwardInfo struct {
	// 奖励信息的缓存
	awardInfoCache map[string]*CommonAwardInfo
	lock           sync.RWMutex

	// client
	FloatCli user_decoration.IClient
}

// NewFloatAwardInfo 创建麦位框配置中的奖励信息
func NewFloatAwardInfo() *FloatAwardInfo {
	FloatCli := user_decoration.NewIClient(grpc.WithBlock())
	awardInfo := &FloatAwardInfo{
		awardInfoCache: make(map[string]*CommonAwardInfo),
		FloatCli:       FloatCli,
	}

	// 初始化奖励信息
	awardInfo.freshAwardInfo()

	// 之后每隔一段时间刷新一次奖励信息
	go func() {
		for {
			awardInfo.freshAwardInfo()
			time.Sleep(10 * time.Minute)
		}
	}()

	return awardInfo
}

// initAwardInfo 初始化奖励信息
func (a *FloatAwardInfo) freshAwardInfo() {
	a.lock.Lock()
	defer a.lock.Unlock()

	// 从麦位框配置中获取奖励信息
	// 1. 获取麦位框配置中的奖励信息
	cfgList, err := a.FloatCli.Decorations(context.Background(), user_decoration2.Type_FLOAT)
	if err != nil {
		log.ErrorWithCtx(context.Background(), "freshAwardInfo GetFloatConfigAll err:%v", err)
		return
	}

	// 2. 将奖励信息放入缓存
	for _, cfg := range cfgList {
		awardInfo := &CommonAwardInfo{
			Id:           cfg.GetDecoration().GetId(),
			Name:         cfg.GetDecDetail().GetName(),
			IconUrl:      cfg.GetDecDetail().GetUrlPic(),
			SourceStruct: cfg,
		}
		a.awardInfoCache[cfg.GetDecoration().GetId()] = awardInfo
	}

	log.InfoWithCtx(context.Background(), "freshAwardInfo FloatAwardInfo success len %d", len(a.awardInfoCache))

	return
}

// GetAwardInfoById 通过id获取奖励信息
func (a *FloatAwardInfo) GetAwardInfoById(ctx context.Context, id string) (*CommonAwardInfo, error) {
	a.lock.RLock()
	defer a.lock.RUnlock()

	out := &CommonAwardInfo{}
	if v, ok := a.awardInfoCache[id]; ok {
		out = v
	}
	return out, nil
}

// GetAwardInfoAll 获取所有奖励信息
func (a *FloatAwardInfo) GetAwardInfoAll(ctx context.Context) (map[string]*CommonAwardInfo, error) {
	a.lock.RLock()
	defer a.lock.RUnlock()

	return a.awardInfoCache, nil
}

// GetAwardInfoByIdList 通过id列表获取奖励信息
func (a *FloatAwardInfo) GetAwardInfoByIdList(ctx context.Context, idList []string) (map[string]*CommonAwardInfo, error) {
	a.lock.RLock()
	defer a.lock.RUnlock()

	awardInfoMap := make(map[string]*CommonAwardInfo)
	for _, id := range idList {
		awardInfoMap[id] = a.awardInfoCache[id]
	}
	return awardInfoMap, nil
}
