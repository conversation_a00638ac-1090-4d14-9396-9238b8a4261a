package award_config

import (
	"context"
	medal_go "golang.52tt.com/clients/medalgo"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/medalgo"
	"google.golang.org/grpc"
	"strconv"
	"sync"
	"time"
)

// AwardInfo在勋章配置中的实现
// 需要内存缓存

// MedalAwardInfo 麦位框配置中的奖励信息
type MedalAwardInfo struct {
	// 奖励信息的缓存
	awardInfoCache map[string]*CommonAwardInfo
	lock           sync.RWMutex

	// client
	MedalCli medal_go.IClient
}

// NewMedalAwardInfo 创建麦位框配置中的奖励信息
func NewMedalAwardInfo() *MedalAwardInfo {
	MedalCli := medal_go.NewIClient(grpc.WithBlock())
	awardInfo := &MedalAwardInfo{
		awardInfoCache: make(map[string]*CommonAwardInfo),
		MedalCli:       MedalCli,
	}

	// 初始化奖励信息
	awardInfo.freshAwardInfo()

	// 之后每隔一段时间刷新一次奖励信息
	go func() {
		for {
			awardInfo.freshAwardInfo()
			time.Sleep(10 * time.Minute)
		}
	}()

	return awardInfo
}

// initAwardInfo 初始化奖励信息
func (a *MedalAwardInfo) freshAwardInfo() {
	a.lock.Lock()
	defer a.lock.Unlock()

	// 从麦位框配置中获取奖励信息
	// 1. 获取麦位框配置中的奖励信息
	cfgList, err := a.MedalCli.GetMedalConfigList(context.Background(), medalgo.GetMedalConfigListReq{})
	if err != nil {
		log.ErrorWithCtx(context.Background(), "freshAwardInfo GetMedalConfigAll err:%v", err)
		return
	}

	// 2. 将奖励信息放入缓存
	for _, cfg := range cfgList.GetMedalList() {
		awardInfo := &CommonAwardInfo{
			Id:           strconv.Itoa(int(cfg.GetMedalId())),
			Name:         cfg.GetName(),
			IconUrl:      cfg.GetIcon(),
			SourceStruct: cfg,
		}
		a.awardInfoCache[strconv.Itoa(int(cfg.GetMedalId()))] = awardInfo
	}

	log.InfoWithCtx(context.Background(), "freshAwardInfo MedalAwardInfo success len %d", len(a.awardInfoCache))

	return
}

// GetAwardInfoById 通过id获取奖励信息
func (a *MedalAwardInfo) GetAwardInfoById(ctx context.Context, id string) (*CommonAwardInfo, error) {
	a.lock.RLock()
	defer a.lock.RUnlock()

	out := &CommonAwardInfo{}
	if v, ok := a.awardInfoCache[id]; ok {
		out = v
	}
	return out, nil
}

// GetAwardInfoAll 获取所有奖励信息
func (a *MedalAwardInfo) GetAwardInfoAll(ctx context.Context) (map[string]*CommonAwardInfo, error) {
	a.lock.RLock()
	defer a.lock.RUnlock()

	return a.awardInfoCache, nil
}

// GetAwardInfoByIdList 通过id列表获取奖励信息
func (a *MedalAwardInfo) GetAwardInfoByIdList(ctx context.Context, idList []string) (map[string]*CommonAwardInfo, error) {
	a.lock.RLock()
	defer a.lock.RUnlock()

	awardInfoMap := make(map[string]*CommonAwardInfo)
	for _, id := range idList {
		awardInfoMap[id] = a.awardInfoCache[id]
	}
	return awardInfoMap, nil
}
