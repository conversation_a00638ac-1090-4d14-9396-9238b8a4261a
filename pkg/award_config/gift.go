package award_config

import (
	"context"
	userpresent_go "golang.52tt.com/clients/userpresent-go"
	"golang.52tt.com/pkg/log"
	"google.golang.org/grpc"
	"strconv"
)

// AwardInfo在礼物配置中的实现
// 礼物配置本身就有内存缓存，所以不用额外的缓存

// GiftAwardInfo 礼物奖励信息
type GiftAwardInfo struct {
	presentCli userpresent_go.PresentClientWrapper
}

// NewGiftAwardInfo 创建礼物奖励信息
func NewGiftAwardInfo() *GiftAwardInfo {
	return &GiftAwardInfo{
		presentCli: userpresent_go.NewPresentClientWrapper([]grpc.DialOption{grpc.WithBlock()}),
	}
}

// GetAwardInfoById 通过id获取奖励信息
func (s *GiftAwardInfo) GetAwardInfoById(ctx context.Context, id string) (*CommonAwardInfo, error) {
	presentId, err := strconv.Atoi(id)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAwardInfoById strconv.Atoi req:%s err:%v", id, err)
		return &CommonAwardInfo{}, err
	}

	resp, err := s.presentCli.GetPresentConfigById(ctx, uint32(presentId))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAwardInfoById presentCli.GetPresentConfigById req:%s err:%v", id, err)
		return &CommonAwardInfo{}, err
	}

	out := &CommonAwardInfo{
		Id:           id,
		Name:         resp.GetItemConfig().GetName(),
		IconUrl:      resp.GetItemConfig().GetIconUrl(),
		SourceStruct: resp.GetItemConfig(),
	}

	return out, nil
}

// GetAwardInfoAll 获取所有奖励信息
func (s *GiftAwardInfo) GetAwardInfoAll(ctx context.Context) (map[string]*CommonAwardInfo, error) {
	resp, err := s.presentCli.GetPresentConfigListV2ByUpdateTime(ctx, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAwardInfoAll presentCli.GetPresentConfigAll err:%v", err)
		return nil, err
	}

	out := make(map[string]*CommonAwardInfo)
	for _, v := range resp.GetItemList() {
		out[strconv.Itoa(int(v.GetItemId()))] = &CommonAwardInfo{
			Id:           strconv.Itoa(int(v.GetItemId())),
			Name:         v.GetName(),
			IconUrl:      v.GetIconUrl(),
			SourceStruct: v,
		}
	}

	return out, nil
}

// GetAwardInfoByIdList 通过id列表获取奖励信息
func (s *GiftAwardInfo) GetAwardInfoByIdList(ctx context.Context, idList []string) (map[string]*CommonAwardInfo, error) {
	presentIdList := make([]uint32, 0, len(idList))
	for _, id := range idList {
		presentId, err := strconv.Atoi(id)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAwardInfoByIdList strconv.Atoi req:%s err:%v", id, err)
			return nil, err
		}
		presentIdList = append(presentIdList, uint32(presentId))
	}

	resp, err := s.presentCli.GetPresentConfigByIdList(ctx, 0, presentIdList, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAwardInfoByIdList presentCli.GetPresentConfigListByIds req:%v err %v", presentIdList, err)
		return nil, err
	}

	out := make(map[string]*CommonAwardInfo)
	for _, v := range resp.GetItemList() {
		out[strconv.Itoa(int(v.GetItemId()))] = &CommonAwardInfo{
			Id:           strconv.Itoa(int(v.GetItemId())),
			Name:         v.GetName(),
			IconUrl:      v.GetIconUrl(),
			SourceStruct: v,
		}
	}

	return out, nil
}
