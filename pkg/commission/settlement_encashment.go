package commission

import (
	"gitlab.ttyuyin.com/golang/gudetama/log"
	"golang.org/x/net/context"
)

type SettlementAndEncashmentRequest struct {
	AppID        string `json:"appid"`
	Uid          uint32 `json:"uid"`
	Amount       uint64 `json:"amount"`
	Date         string `json:"date"`
	Remark       string `json:"remark"`
	EncashAcType AcType `json:"encashAcType"`
	RemitAmount  uint64 `json:"remitAmount"`
	Remitter     string `json:"remitter"`
	Receiver     string `json:"receiver"`
	CreateTime   string `json:"createTime"`
}

type SettlementAndEncashmentResponse struct {
	OrderId    string `json:"orderId"`
	CreateTime string `json:"createTime"`
}

const (
	settlementAndEncashmentURI = "commision/settlementAndEncashment.do"
)

func (c *client) SettlementAndEncashment(ctx context.Context, req *SettlementAndEncashmentRequest) (*SettlementAndEncashmentResponse, error) {
	req.AppID = c.appID
	resp := &SettlementAndEncashmentResponse{}
	_, err := c.post(ctx, settlementAndEncashmentURI, req, resp)
	if err != nil {
		log.Error(ctx, "SettlementAndEncashment %d %d %s %s FAILED %+v", req.Uid, req.Amount, req.Date, req.Remark, err)
		return resp, err
	}
	log.Debug(ctx, "SettlementAndEncashment %d %d %s %s OK", req.Uid, req.Amount, req.Date, req.Remark)
	return resp, nil
}
