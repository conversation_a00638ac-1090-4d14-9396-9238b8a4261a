package commission

import (
	"context"
	"time"

	"golang.52tt.com/pkg/log"
)

type getSettlementSumByTimeRangeRequest struct {
	AppID     string `json:"appId"`     // 业务类型
	StartTime string `json:"startTime"` // 起始时间（yyyy-MM-dd HH:mm:ss）
	Range     int    `json:"range"`     // 查询时间范围，单位分钟，1-5(默认不传为1)
}

type getSettlementSumByTimeRangeResponse struct {
	CountNum uint32  `json:"countNum"` // 订单数量
	Amount   float64 `json:"amount"`   // 订单总金额(单位：元)
}

const (
	getSettlementSumByTimeRangeURI = "log/getSettlementSumByTimeRange"
	timeFormatLayout               = "2006-01-02 15:04:05"
)

// GetSettlementSumByTimeRange 获取结算订单统计接口
// @startTime 查询开始时间点
// @rangeTime 查询时间范围
// @count 返回订单总数
// @amount 返回订单总价值，单位分
func (c *client) GetSettlementSumByTimeRange(ctx context.Context, startTime time.Time, rangeTime int) (count uint32, amount uint32, err error) {
	req := &getSettlementSumByTimeRangeRequest{
		AppID:     c.appID,
		StartTime: startTime.Local().Format(timeFormatLayout),
		Range:     rangeTime,
	}
	var resp getSettlementSumByTimeRangeResponse
	_, err = c.post(ctx, getSettlementSumByTimeRangeURI, req, &resp)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSettlementSumByTimeRange %s %s %d FAILED %+v", req.AppID, req.StartTime, req.Range, err)
		return 0, 0, err
	}
	count = resp.CountNum
	amount = uint32((resp.Amount + 0.001) * 100) // 转换精度，将元转换成分
	log.DebugWithCtx(ctx, "GetSettlementSumByTimeRange %s %s %d OK", req.AppID, req.StartTime, req.Range)
	return count, amount, nil
}
