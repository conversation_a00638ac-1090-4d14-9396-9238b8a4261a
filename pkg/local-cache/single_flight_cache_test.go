package local_cache

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"
)

func TestSingleFlightCache_Get(t *testing.T) {
	c, err := NewSingleFlightCache(context.Background(), 32, time.Second)
	if err != nil {
		t.Fatalf("NewSingleFlightCache failed: %v", err)
	}
	cnt := 0
	sfunc := func() ([]byte, error) {
		cnt++
		return []byte("every day"), nil
	}
	type args struct {
		key              string
		singleflightFunc func() ([]byte, error)
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
		wantCnt int
	}{
		{"set cache", args{"happy", sfunc}, []byte("every day"), false, 1},
		{"get from cache", args{"happy", sfunc}, []byte("every day"), false, 1},
		{"return nil byte array", args{"nil", func() ([]byte, error) {
			return nil, nil
		}}, nil, false, 1},
		{"return err", args{"err", func() ([]byte, error) {
			return nil, errors.New("some err")
		}}, nil, true, 1},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.Get(tt.args.key, tt.args.singleflightFunc)
			t.Logf("Get() got = %v, err %v", string(got), err)
			if (err != nil) != tt.wantErr {
				t.Errorf("Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Get() got = %v, want %v", got, tt.want)
			}
			if cnt != tt.wantCnt {
				t.Errorf("sg func called cnt not matched, cur = %d, want %d", cnt, tt.wantCnt)
			}
		})
	}
}
