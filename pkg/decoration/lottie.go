package decoration

import (
	"encoding/json"

	"golang.52tt.com/clients/account"
	ga "golang.52tt.com/protocol/app"
)

type LottieConfig interface {
	Config
}

// LottieBase 替换的基本结构
type LottieBase struct {
	Type    ConfigType `json:"type,omitempty"`    //配置类型
	Content string     `json:"content,omitempty"` //可配置文案
	Key     string     `json:"key,omitempty"`     //要替换的资源名
	Ext     string     `json:"ext,omitempty"`     //额外参数
}

type ExtOption func(ext *Ext)

func WithFontSize(fontSize uint32) ExtOption {
	return func(ext *Ext) {
		ext.FontSize = fontSize
	}
}

func WithTextColor(textColor string) ExtOption {
	return func(ext *Ext) {
		ext.TextColor = textColor
	}
}

func WithFontStyle(fontStyle string) ExtOption {
	return func(ext *Ext) {
		ext.FontStyle = fontStyle
	}
}

func WithRadius(radius uint32) ExtOption {
	return func(ext *Ext) {
		ext.Radius = radius
	}
}

func (s *LottieBase) GetSourceType() SourceType {
	return SourceTypeLottie
}

func NewLottieCustomText(key string, text string, color string, limit, fontSize uint32, needNewLine bool) []Config {
	ext, _ := json.Marshal(&Ext{
		TextColor:   color,
		LengthLimit: limit,
		FontSize:    fontSize,
		NeedNewLine: needNewLine,
	})
	return []Config{
		&LottieBase{
			Type:    ConfigTypeText,
			Content: text,
			Key:     key,
			Ext:     string(ext),
		},
	}
}

func NewLottieCustomTextWithOpt(key string, text string, opts ...ExtOption) []Config {
	ext := &Ext{}
	for _, opt := range opts {
		opt(ext)
	}

	extStr, _ := json.Marshal(ext)
	return []Config{
		&LottieBase{
			Type:    ConfigTypeText,
			Content: text,
			Key:     key,
			Ext:     string(extStr),
		},
	}
}

func NewLottieAccount(fromAccountKey, fromNickKey, toAccountKey, toNickKey string, fromUser, toUser *account.User,
	color string) []Config {
	ext, _ := json.Marshal(&Ext{TextColor: color})
	return []Config{
		&LottieBase{
			Type:    ConfigTypeText,
			Content: fromUser.GetNickname(),
			Key:     fromNickKey,
			Ext:     string(ext),
		},
		&LottieBase{
			Type:    ConfigTypeAccount,
			Content: fromUser.GetUsername(),
			Key:     fromAccountKey,
			Ext:     string(ext),
		},
		&LottieBase{
			Type:    ConfigTypeText,
			Content: toUser.GetNickname(),
			Key:     toNickKey,
			Ext:     string(ext),
		},
		&LottieBase{
			Type:    ConfigTypeAccount,
			Content: toUser.GetUsername(),
			Key:     toAccountKey,
			Ext:     string(ext),
		},
	}
}

func NewLottieAccountWithOpt(fromAccountKey, fromNickKey, toAccountKey, toNickKey string, fromUser, toUser *account.User, opts ...ExtOption) []Config {
	extObj := &Ext{}
	for _, opt := range opts {
		opt(extObj)
	}
	ext, _ := json.Marshal(extObj)
	return []Config{
		&LottieBase{
			Type:    ConfigTypeText,
			Content: fromUser.GetNickname(),
			Key:     fromNickKey,
			Ext:     string(ext),
		},
		&LottieBase{
			Type:    ConfigTypeAccount,
			Content: fromUser.GetUsername(),
			Key:     fromAccountKey,
			Ext:     string(ext),
		},
		&LottieBase{
			Type:    ConfigTypeText,
			Content: toUser.GetNickname(),
			Key:     toNickKey,
			Ext:     string(ext),
		},
		&LottieBase{
			Type:    ConfigTypeAccount,
			Content: toUser.GetUsername(),
			Key:     toAccountKey,
			Ext:     string(ext),
		},
	}
}

func NewLottieSingleUserProfile(fromAccountKey, fromNickKey string, fromUser *ga.UserProfile, fontSize uint32,
	color string) []Config {
	ext, _ := json.Marshal(&Ext{
		FontSize:  fontSize,
		TextColor: color,
	})
	return []Config{
		&LottieBase{
			Type:    ConfigTypeText,
			Content: fromUser.GetNickname(),
			Key:     fromNickKey,
			Ext:     string(ext),
		},
		&LottieBase{
			Type:    ConfigTypeAccount,
			Content: fromUser.GetAccount(),
			Key:     fromAccountKey,
			Ext:     string(ext),
		},
	}
}

func NewLottieSingleUserProfileV2(fromAccountKey, fromNickKey string, fromUser *PushUserInfo, fontSize uint32,
	color string) []Config {
	ext, _ := json.Marshal(&Ext{
		FontSize:  fontSize,
		TextColor: color,
	})
	return []Config{
		&LottieBase{
			Type:    ConfigTypeText,
			Content: fromUser.NickName,
			Key:     fromNickKey,
			Ext:     string(ext),
		},
		&LottieBase{
			Type:    ConfigTypeAccount,
			Content: fromUser.Account,
			Key:     fromAccountKey,
			Ext:     string(ext),
		},
	}
}

func NewLottieUserProfile(fromAccountKey, fromNickKey, toAccountKey, toNickKey string, fromUser, toUser *ga.UserProfile,
	fontSize uint32, color string) []Config {
	ext, _ := json.Marshal(&Ext{
		FontSize:  fontSize,
		TextColor: color,
	})
	return []Config{
		&LottieBase{
			Type:    ConfigTypeText,
			Content: fromUser.GetNickname(),
			Key:     fromNickKey,
			Ext:     string(ext),
		},
		&LottieBase{
			Type:    ConfigTypeAccount,
			Content: fromUser.GetAccount(),
			Key:     fromAccountKey,
			Ext:     string(ext),
		},
		&LottieBase{
			Type:    ConfigTypeText,
			Content: toUser.GetNickname(),
			Key:     toNickKey,
			Ext:     string(ext),
		},
		&LottieBase{
			Type:    ConfigTypeAccount,
			Content: toUser.GetAccount(),
			Key:     toAccountKey,
			Ext:     string(ext),
		},
	}
}

func NewLottieUserProfileV2(fromAccountKey, fromNickKey, toAccountKey, toNickKey string, fromUser *PushUserInfo, toUser *PushUserInfo,
	fontSize uint32, color string) []Config {
	ext, _ := json.Marshal(&Ext{
		FontSize:  fontSize,
		TextColor: color,
	})
	return []Config{
		&LottieBase{
			Type:    ConfigTypeText,
			Content: fromUser.NickName,
			Key:     fromNickKey,
			Ext:     string(ext),
		},
		&LottieBase{
			Type:    ConfigTypeAccount,
			Content: fromUser.Account,
			Key:     fromAccountKey,
			Ext:     string(ext),
		},
		&LottieBase{
			Type:    ConfigTypeText,
			Content: toUser.NickName,
			Key:     toNickKey,
			Ext:     string(ext),
		},
		&LottieBase{
			Type:    ConfigTypeAccount,
			Content: toUser.Account,
			Key:     toAccountKey,
			Ext:     string(ext),
		},
	}
}

func NewLottieImg(key string, img string) []Config {
	return []Config{
		&LottieBase{
			Type:    ConfigTypeImg,
			Content: img,
			Key:     key,
		},
	}
}

func NewLottieImgWithOpt(key string, img string, opts ...ExtOption) []Config {
	ext := &Ext{}
	for _, opt := range opts {
		opt(ext)
	}

	extJson, _ := json.Marshal(ext)
	return []Config{
		&LottieBase{
			Type:    ConfigTypeImg,
			Content: img,
			Key:     key,
			Ext:     string(extJson),
		},
	}
}
