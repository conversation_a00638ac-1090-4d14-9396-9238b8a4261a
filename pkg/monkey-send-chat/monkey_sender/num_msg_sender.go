package monkey_sender

import (
	"fmt"
	"golang.52tt.com/clients/bots/monkey"
	"golang.52tt.com/pkg/log"
	"google.golang.org/grpc"
	"sync"
	"time"
)

var numMsgSenderInst *NumMsgSender

func GetNumMsgSender() *NumMsgSender {
	if numMsgSenderInst != nil {
		return numMsgSenderInst
	} else {
		cli, err := monkey.NewClient(grpc.WithAuthority("monkey-robot.52tt.local"))
		if err != nil {
			log.Errorf("monkey.NewClient failed err:%+v", err)
			return nil
		}
		log.Infof("monkey.NewClient suc")
		numMsgSenderInst = &NumMsgSender{
			BaseMsgSender: BaseMsgSender{
				chatID:       defaultChatId,
				monkeyClient: cli,
			},
			defaultDuration: 60, //默认60s
			msgCh:           make(chan *QueueMsgT, 1000),
			closeCh:         make(chan struct{}),
		}
		go numMsgSenderInst.DoLoop()
		return numMsgSenderInst
	}
}

func GetNumMsgSenderByChatId(chatId string, duration int64) *NumMsgSender {
	if numMsgSenderInst != nil {
		return numMsgSenderInst
	} else {
		cli, err := monkey.NewClient(grpc.WithAuthority("monkey-robot.52tt.local"))
		if err != nil {
			log.Errorf("monkey.NewClient failed err:%+v", err)
			return nil
		}
		log.Infof("monkey.NewClient suc")
		numMsgSenderInst = &NumMsgSender{
			BaseMsgSender: BaseMsgSender{
				chatID:       chatId,
				monkeyClient: cli,
			},
			defaultDuration: duration,
			msgCh:           make(chan *QueueMsgT, 1000),
			closeCh:         make(chan struct{}),
		}
		go numMsgSenderInst.DoLoop()
		return numMsgSenderInst
	}
}

type NumMsgT struct {
	QueueMsgT
	beginTime   int64
	num         uint32
	srcDuration int64
}

type NumMsgSender struct {
	BaseMsgSender
	defaultDuration int64
	msgMap          sync.Map
	msgCh           chan *QueueMsgT
	closeCh         chan struct{}
}

func (b *NumMsgSender) SetDefaultDuration(duration int64) {
	b.defaultDuration = duration
}

func (b *NumMsgSender) SetSrcDuration(src string, duration int64) {
	tmpMsg := &NumMsgT{
		srcDuration: duration,
	}
	b.msgMap.Store(src, tmpMsg)
}

func (b *NumMsgSender) SendMsg(src, msg string) error {
	if b == nil {
		log.Warnf("NumMsgSender SendMsg err: sender nil")
		return nil
	}
	baseMsg := b.getBaseMsg(1)
	tmpMsg := &QueueMsgT{
		src: src,
		msg: baseMsg + msg,
	}
	select {
	case b.msgCh <- tmpMsg:
	default:
		log.Warnf("SendMsg err, queue len:%d", len(b.msgCh))
	}
	return nil
}

func (b *NumMsgSender) SendSrcAndMsg(src string, duration int64, msg string) error {
	if b == nil {
		log.Warnf("NumMsgSender SendMsg err: sender nil")
		return nil
	}
	tmpMsg := &NumMsgT{
		srcDuration: duration,
	}
	b.msgMap.Store(src, tmpMsg)

	baseMsg := b.getBaseMsg(1)
	tmpQueueMsg := &QueueMsgT{
		src: src,
		msg: baseMsg + msg,
	}
	select {
	case b.msgCh <- tmpQueueMsg:
	default:
		log.Warnf("SendMsg err, queue len:%d", len(b.msgCh))
	}
	return nil
}

func (b *NumMsgSender) DoLoop() {
	defer func() {
		if e := recover(); e != nil {
			log.Errorf("DoLoop panic: %v", e)
		}
	}()
	for {
		select {
		case <-b.closeCh:
			log.Infof("DoLoop close")
			return
		case queueMsg, ok := <-b.msgCh:
			if ok {
				src := queueMsg.GetSrc()
				msg := queueMsg.GetMsg()

				log.Infof("msg:%v", src)
				if msgNum, ok := b.msgMap.Load(src); ok {
					if tmpMsg, ok := msgNum.(*NumMsgT); ok {
						nowTime := time.Now().Unix()
						var duration int64
						if tmpMsg.srcDuration > 0 {
							duration = tmpMsg.srcDuration
						} else {
							duration = b.defaultDuration
						}
						tmpMsg.QueueMsgT = QueueMsgT{
							src: src,
							msg: msg,
						}
						if tmpMsg.beginTime == 0 {
							tmpMsg.beginTime = time.Now().Unix()
						}
						diff := nowTime - tmpMsg.beginTime
						log.Infof("diff:%d, duration:%d", diff, duration)
						if diff <= duration {
							tmpMsg.num = tmpMsg.num + 1
							b.msgMap.Store(src, tmpMsg)
						} else {
							tmpNewMsg := &NumMsgT{
								QueueMsgT: QueueMsgT{
									src: src,
									msg: msg,
								},
								beginTime:   time.Now().Unix(),
								num:         1,
								srcDuration: duration,
							}
							b.msgMap.Store(src, tmpNewMsg)
							numMsg := fmt.Sprintf("[Num:%d] ", tmpMsg.num)
							msg = numMsg + msg
							_ = b.SendOriginMsg(src, msg)
						}
					}
				} else {
					log.Infof("src:%v", src)
					tmpMsgNum := &NumMsgT{
						QueueMsgT: QueueMsgT{
							src: src,
							msg: msg,
						},
						beginTime: time.Now().Unix(),
						num:       1,
					}
					b.msgMap.Store(src, tmpMsgNum)
				}
			} else {
				log.Errorf("DoLoop queue channel read err")
			}
		}
	}
}

func (b *NumMsgSender) Close() {
	close(b.closeCh)
}
