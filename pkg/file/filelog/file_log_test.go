package filelog

import (
	"fmt"
	"testing"
	"time"
)

func TestGetFile(b *testing.T) {
	writter := NewFileWritter("/home/<USER>/files", "test")
	nowTime := time.Now()
	getFile, err := writter.GetFileByTime("test", nowTime, true)
	fmt.Printf("file:%+v, err:%+v", getFile, err)

	nowTime = time.Now().AddDate(0, 0, 0)
	getFile, err = writter.GetFileByTime("test", nowTime, true)
	fmt.Printf("file:%+v, err:%+v", getFile, err)

	nowTime = time.Now().AddDate(0, -1, 0)
	getFile, err = writter.GetFileByTime("test", nowTime, true)
	fmt.Printf("file:%+v, err:%+v", getFile, err)

	nowTime = time.Now().AddDate(0, -1, 0)
	getFile, err = writter.GetFileByTime("test", nowTime, true)
	fmt.Printf("file:%+v, err:%+v", getFile, err)

	nowTime = time.Now().AddDate(0, -2, 0)
	getFile, err = writter.GetFileByTime("test", nowTime, true)
	fmt.Printf("file:%+v, err:%+v", getFile, err)
}