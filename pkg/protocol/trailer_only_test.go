package protocol

import (
	"context"
	"fmt"
	"google.golang.org/grpc/metadata"
	"os"
	"testing"
)

func TestEnableTrailerOnlyMode(t *testing.T) {
	ctx := context.Background()

	fmt.Println("no env & no enable >>", IsTrailerOnlyMode(ctx))

	_ = os.Setenv("TT_SUPPORT_TRAILER_ONLY", "true")

	ReloadEnv()

	fmt.Println("env & no enable >>", IsTrailerOnlyMode(ctx))

	//ctx = EnableTrailerOnlyMode(ctx)
	ctx = metadata.NewIncomingContext(ctx, metadata.Pairs(clientSupportTrailerOnlyContext, "true"))

	fmt.Println("env & enable >>", IsTrailerOnlyMode(ctx))
}
