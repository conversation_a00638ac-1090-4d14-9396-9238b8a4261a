package protocol

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func Test_ClientVersion(t *testing.T) {
	Convey("client-version-test", t, func() {
		cv := ClientVersion(FormatClientVersion(6, 42, 0))

		So(cv.Major(), Should<PERSON><PERSON><PERSON>, 3)
		So(cv.Minor(), Should<PERSON><PERSON><PERSON>, 4)
		So(cv.Patch(), ShouldEqual, 1)
		So(cv.String(), ShouldEqual, "3.4.1")
	})
}

func Test_ClientVersion_String(t *testing.T) {
	v := ClientVersion(33619973)
	t.Logf("Test_ClientVersion_String %s", v.String())
	v = ClientVersion(17301510)
	t.Logf("Test_ClientVersion_String %s", v.String())
}

func TestPlatform_String(t *testing.T) {
	t.Log(FormatClientVersion(2, 1, 2))
}
