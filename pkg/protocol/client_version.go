package protocol

import (
	"fmt"

	ga "golang.52tt.com/protocol/app"
)

// ClientVersion ...
type ClientVersion uint32

// Major returns major version
func (cv ClientVersion) Major() uint8 { return uint8((cv >> 24) & 0x00FF) }

// Minor returns minor version
func (cv ClientVersion) Minor() uint8 { return uint8((cv >> 16) & 0x00FF) }

// Patch returns patch
func (cv ClientVersion) Patch() uint16 { return uint16(cv & 0xFFFF) }

// String returns the `major`.`minor`.`patch` formatted string
func (cv ClientVersion) String() string {
	return fmt.Sprintf("%d.%d.%d", cv.Major(), cv.Minor(), cv.Patch())
}

// FormatClientVersion returns the 32-bits representation of the specified major-minor-patch version
func FormatClientVersion(major, minor uint8, patch uint16) uint32 {
	return (uint32(major) << 24) + (uint32(minor) << 16) + uint32(patch)
}

func GetAppNameFromMarkitId(market_id uint32) string {
	switch ga.BaseReq_MarketId(market_id) {
	case ga.BaseReq_MARKET_HUANYOU:
		return "huanyou"
	case ga.BaseReq_MARKET_ZAIYA:
		return "zaiya"
	case ga.BaseReq_MARKET_TOP_SPEED:
		return "topspeed"
	case ga.BaseReq_MARKET_MAIKE:
		return "maike"
	default:
		break
	}
	return "ttvoice"
}
