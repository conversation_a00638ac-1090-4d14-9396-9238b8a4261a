package iplocation

import (
	"encoding/json"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/runtime/v2/filters/pkg"
	"os/user"
	"path"
	"runtime"
)

type config struct {
	Domain      string            `json:"domain"`
	DomainV2    string            `json:"domainV2"` // 新连接地址
	ProvinceMap map[string]string `json:"province"`
	CityMap     map[string]string `json:"city"`
}

func (cfg *config) UnmarshalBinary(data []byte) error {
	err := json.Unmarshal(data, cfg)
	if err != nil {
		log.Errorf("UnmarshalBinary data(%s) err: %v", data, err)
		return err
	}

	return nil
}

type configLoader struct {
	loader *pkg.ConfigLoader
}

func ttConfPath(name string) string {
	var ttConfDir string
	if runtime.GOOS == "windows" {
		if u, err := user.Current(); err == nil {
			ttConfDir = path.Join(u.HomeDir, "/data/oss/conf-center/tt/")
		}
	} else {
		ttConfDir = "/data/oss/conf-center/tt/"
	}

	return path.Join(ttConfDir, name)
}

func (ld *configLoader) domain() string {
	if ld.loader == nil {
		return ""
	}

	v := ld.loader.Get()
	cfg, _ := v.(*config)

	if cfg == nil {
		return ""
	}

	return cfg.DomainV2
}

func (ld *configLoader) GetProvince(name string) string {
	if ld.loader == nil {
		return ""
	}

	v := ld.loader.Get()
	cfg, _ := v.(*config)

	return cfg.ProvinceMap[name]
}

func (ld *configLoader) GetCity(name string) string {
	if ld.loader == nil {
		return ""
	}

	v := ld.loader.Get()
	cfg, _ := v.(*config)

	return cfg.CityMap[name]
}
