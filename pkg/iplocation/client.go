package iplocation

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	tmp_sensitive_area_visible "golang.52tt.com/pkg/tmp-sensitive-area-visible"
	"strings"
)

type Country struct {
	Code string `json:"country_code"`
	Name string `json:"country"`
}

type Province struct {
	Code string `json:"province_code"`
	Name string `json:"province"`
}

type City struct {
	Code string `json:"city_code"`
	Name string `json:"city"`
}

type County struct {
	Code string `json:"county_code"`
	Name string `json:"county"`
}

type Location struct {
	Latitude  string `json:"latitude"`  // 纬度
	Longitude string `json:"longitude"` // 经度

	Isp    string `json:"isp"`    // 运营商
	Routes string `json:"routes"` // 运营商路线

	Country  // 国
	Province // 省
	City     // 市
	County   // 区/县
}

// GetIpLocation IP地域查询
func GetIpLocation(ctx context.Context, ip string) (out *Location, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("panic:%+v", e)
		}
	}()

	type response struct {
		Code int32  `json:"code"`
		Msg  string `json:"msg"`

		Location
	}

	url := fmt.Sprintf("http://%s?ip=%s", cfgLoader.domain(), ip)
	log.DebugWithCtx(ctx, "GetIpLocationUrl:%s", url)
	var resp response
	err = httpCli.get(ctx, url, &resp)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIpLocation get url(%s) err: %v", url, err)
		return nil, err
	}

	if resp.Code != 1 {
		log.ErrorWithCtx(ctx, "GetIpLocation code(%d != 1) msg(%s)", resp.Code, resp.Msg)
		return nil, fmt.Errorf("response code %d", resp.Code)
	}

	log.DebugWithCtx(ctx, "GetIpLocation location: %+v", resp.Location)
	out = &resp.Location
	return out, nil
}

const shi = "市"

func GetLocationInfo(ctx context.Context, ip string) (province string, city string, err error) {
	location, err := GetIpLocation(ctx, ip)
	log.DebugWithCtx(ctx, "GetLocationInfo ip:%s err:%s location:%+v", ip, err, location)
	if err != nil {
		return
	}

	if location.Country.Code != "CN" {
		return
	}

	province = cfgLoader.GetProvince(location.Province.Name)

	if strings.HasSuffix(location.City.Name, shi) {
		city = strings.TrimSuffix(location.City.Name, shi)
	} else {
		city = cfgLoader.GetCity(location.City.Name)
	}

	// 四个直辖市从省的字段换成市
	if province == "北京" || province == "天津" || province == "上海" || province == "重庆" {
		city = province
	}

	if !tmp_sensitive_area_visible.CheckAreaIsVisible(province, city) {
		log.WarnWithCtx(ctx, "CheckAreaIsVisible is false prov:%s city:%s", province, city)
		province = ""
		city = ""
	}

	log.InfoWithCtx(ctx, "GetLocationInfo ip:%s location:%+v province:%s city:%s", ip, location, province, city)
	return
}

// GetShortCityName 获取城市或者省份简写
func GetShortCityName(name string) string {
	tmpCityName := cfgLoader.GetProvince(name)
	if tmpCityName == "" {
		tmpCityName = strings.TrimSuffix(cfgLoader.GetCity(name), shi)
	}
	if tmpCityName == "" {
		tmpCityName = strings.TrimSuffix(name, shi)
	}
	return tmpCityName
}
