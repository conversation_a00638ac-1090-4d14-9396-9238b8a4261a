package pulsar

import (
	"context"
	"github.com/apache/pulsar-client-go/pulsar"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol/grpc"
	"sync"
)

type Subscriber struct {
	pulsarClients map[string]pulsar.Client
	lock          *sync.Mutex
	consumers     []*Consumer

	ctx    context.Context
	cancel context.CancelFunc
}

type ConsumeHandler func(ctx context.Context, message pulsar.ConsumerMessage) error

type Consumer struct {
	consumer pulsar.Consumer
	handler  ConsumeHandler
	ctx      context.Context
	channel  chan pulsar.ConsumerMessage
}

func NewSubscriber(ctx context.Context, cfg config.Configer) (*Subscriber, error) {
	sub := new(Subscriber)

	sub.lock = new(sync.Mutex)
	sub.pulsarClients = make(map[string]pulsar.Client)

	sub.ctx, sub.cancel = context.WithCancel(ctx)

	return sub, nil
}

func (s *Subscriber) getClient(url string) (pulsar.Client, error) {
	s.lock.Lock()
	defer s.lock.Unlock()
	client, ok := s.pulsarClients[url]
	if ok {
		return client, nil
	}

	c, err := pulsar.NewClient(pulsar.ClientOptions{
		URL: url,
	})

	if err != nil {
		return nil, err
	}

	s.pulsarClients[url] = c

	return c, nil
}

func (s *Subscriber) Subscribe(cfg *config.PulsarConfig, chanBufSize int, handler ConsumeHandler) error {
	client, err := s.getClient(cfg.Brokers)
	if err != nil {
		return err
	}

	channel := make(chan pulsar.ConsumerMessage, chanBufSize)

	con, err := client.Subscribe(pulsar.ConsumerOptions{
		Topic:            cfg.Topics,
		SubscriptionName: cfg.GroupID,
		MessageChannel:   channel,
		Type:             pulsar.Shared,
	})

	if err != nil {
		return nil
	}

	consumer := &Consumer{
		consumer: con,
		handler:  handler,
		ctx:      s.ctx,
		channel:  channel,
	}

	consumer.Start()

	s.consumers = append(s.consumers, consumer)

	return nil
}

func (s *Subscriber) Stop() {
	s.cancel()

	for _, consumer := range s.consumers {
		consumer.consumer.Close()
		close(consumer.channel)
	}

	for _, client := range s.pulsarClients {
		client.Close()
	}
}

func (c *Consumer) Start() {
	go func() {
		if c.handler == nil {
			return
		}
		for {
			select {
			case <-c.ctx.Done():
				log.Infof("consumer exit")
				return
			case message, ok := <-c.channel:
				if ok {
					c.handler(WithPulsarMsgContext(message), message)
					c.consumer.Ack(message)
				}
			}
		}
	}()
}

func WithPulsarMsgContext(msg pulsar.ConsumerMessage) context.Context {
	ctx := context.Background()
	if msg.Message != nil && len(msg.Message.Properties()) > 0 {
		meta := msg.Message.Properties()
		ctx = grpc.InjectServiceInfoWithMap(ctx, meta)
	}

	return ctx
}
