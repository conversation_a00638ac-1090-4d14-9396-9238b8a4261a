package main

import (
	"context"
	"flag"
	"golang.52tt.com/pkg/log"
	sdk "golang.52tt.com/pkg/pulsar"
	"google.golang.org/grpc/metadata"
)

var (
	topic = flag.String("topics", "imlogic", "topic")
)

func main() {
	flag.Parse()
	ctx := metadata.NewIncomingContext(context.Background(), metadata.New(map[string]string{
		"req_uid":    "222333",
		"req_dye_id": "8",
		"req_seqid":  "alksdjadadfasdfa",
	}))
	sdk.RegisterProducerWithTopic(*topic)
	err := sdk.ProducerSyncSend(ctx, *topic, []byte("hello"))
	log.InfoWithCtx(ctx, "send done, err:%v", err)
	select {}
}
