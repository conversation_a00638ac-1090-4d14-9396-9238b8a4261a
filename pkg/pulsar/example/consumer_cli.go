package main

import (
	"context"
	"flag"
	"github.com/apache/pulsar-client-go/pulsar"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	sdk "golang.52tt.com/pkg/pulsar"
)

var (
	filePath   = flag.String("conf", "conf.json", "json config file")
	configItem = flag.String("item", "imlogic-pulsar", "json config file")
)

func main() {
	flag.Parse()
	var conf config.ServerConfig
	err := conf.InitWithPath("json", *filePath)
	if err != nil {
		log.Errorf("parse conf err:%v", err)
		return
	}
	sub, _ := sdk.NewSubscriber(context.Background(), conf.Configer)
	if err := sub.Subscribe(config.NewPulsarConfigWithSection(conf.Configer, *configItem), 1024, consumeHandler); err != nil {
		log.E<PERSON>rf("subscribe err:%v", err)
		return
	}
	select {}
}

func consumeHandler(ctx context.Context, msg pulsar.ConsumerMessage) error {
	log.InfoWithCtx(ctx, "consume msg id:%v", msg.ID())
	return nil
}
