package config

import (
	"encoding/json"
	"io/ioutil"
	"os"

	"github.com/astaxie/beego/config"
)

type UFileConfig struct {
	BucketName      string
	FileHost        string
	BucketHost      string
	VerifyUploadMD5 bool
	PublicKey       string
	PrivateKey      string
	Env             string
	QueryUrl        string
}

func NewUFileConfig(cfg config.Configer) *UFileConfig {
	return newUFileConfigSection(cfg, "ufile")
}

func newUFileConfigSection(cfg config.Configer, sectionName string) *UFileConfig {
	return &UFileConfig{
		BucketName:      cfg.DefaultString(sectionName+"::bucket_name", ""),
		FileHost:        cfg.DefaultString(sectionName+"::file_host", ""),
		BucketHost:      cfg.DefaultString(sectionName+"::bucket_host", ""),
		VerifyUploadMD5: cfg.DefaultBool(sectionName+"::verify_upload_md5", false),
		PublicKey:       cfg.DefaultString(sectionName+"::public_key", ""),
		PrivateKey:      cfg.DefaultString(sectionName+"::private_key", ""),
		Env:             cfg.DefaultString(sectionName+"::env", "test"),
		QueryUrl:        cfg.DefaultString(sectionName+"::query_url", ""),
	}
}

// LoadByFile 根据指定路径加载
func LoadByFile(filePath string, configOut interface{}) error {
	fd, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer fd.Close()

	b, _ := ioutil.ReadAll(fd)
	err = json.Unmarshal(b, configOut)
	return err
}
