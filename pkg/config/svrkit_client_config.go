package config

import (
	"fmt"
	"os"
	"os/user"
	"path"
)

var (
	configPath       = "/home/<USER>/etc"
	clientConfigPath string
)

func init() {
	if u, err := user.Current(); err == nil {
		configPath = fmt.Sprintf("%s/etc", u.HomeDir)
	}
	clientConfigPath = os.Getenv("CLIENT_CONFIG_PATH")
	if clientConfigPath == "" {
		clientConfigPath = path.Join(configPath, "client")
	}
}

func DefaultSvrkitClientConfig(service string) string {
	return path.Join(clientConfigPath, fmt.Sprintf("%s_cli.conf", service))
}
