package grpc

import (
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"golang.52tt.com/pkg/tracing"
)

type grpcErrorClassifier struct {
}

// ClassifierError implements tracing.ErrorClassifier.ClassifierError
func (c *grpcErrorClassifier) ClassifierError(err error) (tracing.ErrorClass, int32) {
	if s, ok := status.FromError(err); ok {
		if s.Code() >= 0 {
			switch s.Code() {
			// Success or "success"
			case codes.OK, codes.Canceled:
				return tracing.Success, int32(s.Code())

			// Client errors
			case codes.InvalidArgument, codes.NotFound, codes.AlreadyExists,
				codes.PermissionDenied, codes.Unauthenticated, codes.FailedPrecondition,
				codes.OutOfRange:
				return tracing.ClientError, int32(s.Code())

			// Server errors
			case codes.DeadlineExceeded, codes.ResourceExhausted, codes.Aborted,
				codes.Unimplemented, codes.Internal, codes.Unavailable, codes.DataLoss:
				return tracing.ServerError, int32(s.Code())

			// Not sure
			case codes.Unknown:
				fallthrough
			default:
				return tracing.Unknown, int32(s.Code())
			}
		} else {
			// < 0, logic error
			if int(s.Code()) > -100 {
				// system error
				return tracing.ServerError, int32(s.Code())
			}
			return tracing.LogicalError, int32(s.Code())
		}
	}
	return tracing.Unknown, int32(codes.Unknown)
}

var (
	errorClassifier = &grpcErrorClassifier{}
)
