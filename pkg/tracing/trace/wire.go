package trace

import (
	"bytes"
	"encoding/binary"
	"io"
	"strings"

	"github.com/opentracing/opentracing-go"
	"github.com/uber/jaeger-client-go"
)

func readBinary64(r io.Reader) uint64 {
	var v uint64
	binary.Read(r, binary.LittleEndian, &v)
	return v
}

func readBinary32(r io.Reader) uint32 {
	var v uint32
	binary.Read(r, binary.LittleEndian, &v)
	return v
}

func writeBinaryUint32(w io.Writer, val uint32) error {
	return binary.Write(w, binary.LittleEndian, val)
}

func writeBinaryUint64(w io.Writer, val uint64) error {
	return binary.Write(w, binary.LittleEndian, val)
}

func InjectFromString(traceidStr, spanidStr, parentidStr, sampleStr string) []byte {
	value := []string{traceidStr, spanidStr, parentidStr, sampleStr}
	str := strings.Join(value, ":")
	ctx, err := jaeger.ContextFromString(str)
	if err != nil {
		return nil
	}
	return Inject(ctx)
}

func Inject(SpanContext opentracing.SpanContext) []byte {
	if SpanContext == nil {
		return nil
	}
	buf := new(bytes.Buffer)
	baggage := make(map[string]string)
	sc, err := SpanContext.(jaeger.SpanContext)
	if !err {
		// fmt.Printf("change to jaeger.SpanContext failed\n")
		return nil
	}

	writeBinaryUint64(buf, sc.TraceID().High)
	writeBinaryUint64(buf, sc.TraceID().Low)
	writeBinaryUint64(buf, uint64(sc.SpanID()))
	writeBinaryUint64(buf, uint64(sc.ParentID()))
	if sc.IsSampled() {
		buf.WriteByte('1')
	} else {
		buf.WriteByte('0')
	}

	call := func(k, v string) bool {
		baggage[k] = v
		return true
	}
	sc.ForeachBaggageItem(call)
	writeBinaryUint32(buf, uint32(len(baggage)))

	for k, v := range baggage {
		writeBinaryUint32(buf, uint32(len(k)))
		buf.WriteString(k)
		writeBinaryUint32(buf, uint32(len(v)))
		buf.WriteString(v)
	}
	return buf.Bytes()
}

// 将svrkit的jaeger二进制数据转换成opentracing.SpanContext
func Extract(body []byte) (opentracing.SpanContext, bool) {
	if len(body) == 0 {
		return nil, false
	}

	r := bytes.NewBuffer(body)
	high := readBinary64(r)
	low := readBinary64(r)
	spanId := readBinary64(r)
	parentSpanId := readBinary64(r)

	var flags byte
	binary.Read(r, binary.LittleEndian, &flags)
	numBaggageItems := readBinary32(r)

	baggage := make(map[string]string)
	for c := 0; c < int(numBaggageItems); c++ {
		keyLen := readBinary32(r)
		key := r.Next(int(keyLen))
		valueLen := readBinary32(r)
		Value := r.Next(int(valueLen))
		baggage[string(key)] = string(Value)
	}

	traceId := jaeger.TraceID{High: high, Low: low}
	sc := jaeger.NewSpanContext(traceId, jaeger.SpanID(spanId), jaeger.SpanID(parentSpanId), flags != '0', baggage)
	return sc, true
}
