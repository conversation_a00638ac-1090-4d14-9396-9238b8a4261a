package web

import (
	"encoding/json"
	"io/ioutil"
	"net/http"

	"github.com/dgrijalva/jwt-go"
	"gitlab.ttyuyin.com/golang/gudetama/log"

	"golang.52tt.com/protocol/common/status"
)

var tokenSignKey = []byte("GeLAvKxjQiozR9eKBd3H5VBcmh7Ete5E")

type OpenIdAuthVerify struct {
}

func (o *OpenIdAuthVerify) TokenBasedAuth(w http.ResponseWriter, r *http.Request, unauthorized func(w http.ResponseWriter, code int, message ...string) error) (*AuthInfo, bool) {
	var token string

	var querys = r.URL.Query()

	var body []byte
	var err error
	if r.Method == "POST" {
		req := &struct {
			Token       string `json:"token"`
			AccessToken string `json:"access_token"`
		}{}
		body, err = ioutil.ReadAll(r.Body)
		if err != nil {
			ServeBadReq(w)
			return nil, false
		}
		if err = json.Unmarshal(body, req); err == nil {
			if len(req.AccessToken) > 0 {
				token = req.AccessToken
			} else {
				token = req.Token
			}
		}
	} else if r.Method == "GET" {
		if t := querys.Get("access_token"); len(t) > 0 {
			token = t
		} else {
			token = querys.Get("token")
		}
	}

	log.Debugf("TokenBasedAuth query:%+v, token:%s", r.URL.Query(), token)

	if len(token) == 0 {
		unauthorized(w, status.ErrTokenIsRequired)
		return nil, false
	}

	plat, os, app, marketId, cliVersion := GetTerminalInfo(querys)

	openid, username, session, gameid, roomid, err := o.decodeToken(token)
	if err != nil {
		if err == errTokenExpired {
			unauthorized(w, status.ErrTokenWasExpired)
		} else {
			unauthorized(w, status.ErrTokenBadToken)
		}
		return nil, false
	}

	authInfo := &AuthInfo{
		OpenId:     openid,
		Username:   username,
		Session:    session,
		GameId:     gameid,
		RoomId:     roomid,
		OS:         os,
		MarketID:   marketId,
		CliVersion: cliVersion,
		AppID:      app,
		Platform:   plat,
		Body:       body,
	}
	//log.Debugf("decoded auth info: %v", authInfo)
	return authInfo, true
}

func (o *OpenIdAuthVerify) UidBasedAuth(w http.ResponseWriter, r *http.Request) (*AuthInfo, bool) {
	var querys = r.URL.Query()
	var openId = querys.Get("openid")
	var body []byte
	var err error
	if r.Method == "POST" {
		req := &struct {
			OpenID string `json:"openid"`
		}{}
		body, err = ioutil.ReadAll(r.Body)
		if err != nil {
			ServeBadReq(w)
			return nil, false
		}
		if err := json.Unmarshal(body, req); err == nil {
			openId = req.OpenID
		}
	}

	/*if len(openId) == 0 {
		ServeNotAuth(w)
		return nil, false
	}*/

	plat, os, app, marketId, cliVersion := GetTerminalInfo(querys)
	authInfo := &AuthInfo{
		OpenId:     openId,
		OS:         os,
		AppID:      app,
		Platform:   plat,
		Body:       body,
		MarketID:   marketId,
		CliVersion: cliVersion,
	}
	return authInfo, true
}

type tokenType int

func (o *OpenIdAuthVerify) decodeToken(tokenString string) (openid string, username string, session string, gameid, roomid uint32, err error) {
	claims := &struct {
		jwt.StandardClaims
		Type tokenType `json:"typ"` // 1 access_token / 2 refresh_token
		// only in access tokens
		OpenId  string `json:"openid"`
		Session string `json:"sess"`
		RoomId  uint32 `json:"roomid"`
		GameId  uint32 `json:"gameid"`
	}{}

	token, err := jwtParser.ParseWithClaims(
		tokenString,
		claims,
		func(t *jwt.Token) (interface{}, error) {
			switch t.Method.Alg() {
			case "HS256", "HS384", "HS512":
				return tokenSignKey, nil
			default:
				return nil, errUnsupportedAlg
			}
		})

	if err != nil {
		log.Errorf("ParseWithClaims err: %v, token:%s", err, tokenString)
		return "", "", "", 0, 0, err
	}

	if !token.Valid || claims.Type == 2 {
		log.Errorf("Token %v %v is invalid", token.Header, token.Claims)
		return "", "", "", 0, 0, errInvalidToken
	}

	return claims.OpenId, claims.Subject, claims.Session, claims.GameId, claims.RoomId, nil
}
