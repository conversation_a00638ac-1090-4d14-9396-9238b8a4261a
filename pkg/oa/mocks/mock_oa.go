// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/pkg/oa (interfaces: IClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	io "io"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	oa "golang.52tt.com/pkg/oa"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AppendCorpApplicationArchive mocks base method.
func (m *MockIClient) AppendCorpApplicationArchive(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AppendCorpApplicationArchive", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AppendCorpApplicationArchive indicates an expected call of AppendCorpApplicationArchive.
func (mr *MockIClientMockRecorder) AppendCorpApplicationArchive(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AppendCorpApplicationArchive", reflect.TypeOf((*MockIClient)(nil).AppendCorpApplicationArchive), arg0, arg1, arg2, arg3)
}

// CompleteTask mocks base method.
func (m *MockIClient) CompleteTask(arg0 context.Context, arg1 []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompleteTask", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CompleteTask indicates an expected call of CompleteTask.
func (mr *MockIClientMockRecorder) CompleteTask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompleteTask", reflect.TypeOf((*MockIClient)(nil).CompleteTask), arg0, arg1)
}

// GenSeqNo mocks base method.
func (m *MockIClient) GenSeqNo(arg0 context.Context) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenSeqNo", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenSeqNo indicates an expected call of GenSeqNo.
func (mr *MockIClientMockRecorder) GenSeqNo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenSeqNo", reflect.TypeOf((*MockIClient)(nil).GenSeqNo), arg0)
}

// GetAccountInfo mocks base method.
func (m *MockIClient) GetAccountInfo(arg0 context.Context, arg1, arg2 string) ([]oa.AccountInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].([]oa.AccountInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountInfo indicates an expected call of GetAccountInfo.
func (mr *MockIClientMockRecorder) GetAccountInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountInfo", reflect.TypeOf((*MockIClient)(nil).GetAccountInfo), arg0, arg1, arg2)
}

// GetFileUrl mocks base method.
func (m *MockIClient) GetFileUrl(arg0 context.Context, arg1 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFileUrl", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFileUrl indicates an expected call of GetFileUrl.
func (mr *MockIClientMockRecorder) GetFileUrl(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFileUrl", reflect.TypeOf((*MockIClient)(nil).GetFileUrl), arg0, arg1)
}

// GetHost mocks base method.
func (m *MockIClient) GetHost() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHost")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetHost indicates an expected call of GetHost.
func (mr *MockIClientMockRecorder) GetHost() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHost", reflect.TypeOf((*MockIClient)(nil).GetHost))
}

// ProcessNodeInfo mocks base method.
func (m *MockIClient) ProcessNodeInfo(arg0 context.Context, arg1 string) (*oa.NodeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessNodeInfo", arg0, arg1)
	ret0, _ := ret[0].(*oa.NodeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessNodeInfo indicates an expected call of ProcessNodeInfo.
func (mr *MockIClientMockRecorder) ProcessNodeInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessNodeInfo", reflect.TypeOf((*MockIClient)(nil).ProcessNodeInfo), arg0, arg1)
}

// StartCorpApplyProcess mocks base method.
func (m *MockIClient) StartCorpApplyProcess(arg0 context.Context, arg1 *oa.ContractApplyReq) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartCorpApplyProcess", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartCorpApplyProcess indicates an expected call of StartCorpApplyProcess.
func (mr *MockIClientMockRecorder) StartCorpApplyProcess(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartCorpApplyProcess", reflect.TypeOf((*MockIClient)(nil).StartCorpApplyProcess), arg0, arg1)
}

// StartProcess mocks base method.
func (m *MockIClient) StartProcess(arg0 context.Context, arg1 []byte) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartProcess", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartProcess indicates an expected call of StartProcess.
func (mr *MockIClientMockRecorder) StartProcess(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartProcess", reflect.TypeOf((*MockIClient)(nil).StartProcess), arg0, arg1)
}

// StartProcessByAccount mocks base method.
func (m *MockIClient) StartProcessByAccount(arg0 context.Context, arg1 []byte, arg2 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartProcessByAccount", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartProcessByAccount indicates an expected call of StartProcessByAccount.
func (mr *MockIClientMockRecorder) StartProcessByAccount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartProcessByAccount", reflect.TypeOf((*MockIClient)(nil).StartProcessByAccount), arg0, arg1, arg2)
}

// Upload mocks base method.
func (m *MockIClient) Upload(arg0 context.Context, arg1 io.Reader, arg2 string) (*oa.FileInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upload", arg0, arg1, arg2)
	ret0, _ := ret[0].(*oa.FileInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Upload indicates an expected call of Upload.
func (mr *MockIClientMockRecorder) Upload(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upload", reflect.TypeOf((*MockIClient)(nil).Upload), arg0, arg1, arg2)
}

// UploadFileByPath mocks base method.
func (m *MockIClient) UploadFileByPath(arg0 context.Context, arg1 string) (*oa.FileInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadFileByPath", arg0, arg1)
	ret0, _ := ret[0].(*oa.FileInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadFileByPath indicates an expected call of UploadFileByPath.
func (mr *MockIClientMockRecorder) UploadFileByPath(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadFileByPath", reflect.TypeOf((*MockIClient)(nil).UploadFileByPath), arg0, arg1)
}

// UploadInvoiceVerify mocks base method.
func (m *MockIClient) UploadInvoiceVerify(arg0 context.Context, arg1 io.Reader, arg2 string) (*oa.FileInfo, *oa.Invoice, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadInvoiceVerify", arg0, arg1, arg2)
	ret0, _ := ret[0].(*oa.FileInfo)
	ret1, _ := ret[1].(*oa.Invoice)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// UploadInvoiceVerify indicates an expected call of UploadInvoiceVerify.
func (mr *MockIClientMockRecorder) UploadInvoiceVerify(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadInvoiceVerify", reflect.TypeOf((*MockIClient)(nil).UploadInvoiceVerify), arg0, arg1, arg2)
}
