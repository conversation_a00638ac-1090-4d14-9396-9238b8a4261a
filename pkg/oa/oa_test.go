package oa

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"
	"time"
)

var c *Client

func init() {
	c = NewOAClient(
		//"https://test-bpm.52tt.com", // 废弃
		"https://test-union-b-gateway.52tt.com",
		"tt_operation_background",
		"mtkznzawy2etnju0my00zti3lwe2nzatyjqyytfln2yzmtk1",
		"TT_association_mgr",
		//"T0885",
	)
	//c = NewOAClient(
	//	"https://bpm.52tt.com",
	//	"tt_operation_background",
	//	"nteyodqwogutn2nlzs00ymrmlwiwnwytndi5nzm3zji5mdzi",
	//	"TT_association_mgr",
	//)
}

func TestUploadFile(t *testing.T) {
	ctx := context.Background()
	info, err := c.UploadFileByPath(ctx, "c:/1.txt")
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("fileId: %s", info.FileId)
}

func TestGetFileUrl(t *testing.T) {
	ctx := context.Background()
	url, err := c.GetFileUrl(ctx, "**********218644481")
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("url: %s", url)
}

func TestStartProcess(t *testing.T) {
	ctx := context.Background()
	billFileInfosJson, err := json.Marshal([]FileInfo{})
	receiptFileInfosJson, err := json.Marshal([]FileInfo{})
	relInstJson, err := json.Marshal([]ProcessRelInst{})
	bill := ProcessBill{
		GuildMasterBill: GuildMasterBill{
			SeqNo:                    "HZJSD202202091629150001",
			ApplyDate:                "2022-02-18",
			BillFiles:                string(billFileInfosJson),
			MasterUid:                "123456",
			LogicNo:                  "12345678910",
			ApplyName:                "测试",
			MasterRealName:           "测试",
			OpenCompanyName:          "公司",
			RelaInst:                 string(relInstJson),
			InvoiceFiles:             string(receiptFileInfosJson),
			SubReconciliationDetails: "[]",
		},
	}
	billJson, err := json.Marshal(bill)
	if err != nil {
		t.Fatal(err)
	}
	pro := Process{
		DefKey:     "Process_55212166832411",
		SystemHand: true,
		HasPk:      false,
		FormJson:   string(billJson),
	}
	proJson, err := json.Marshal(pro)
	if err != nil {
		t.Fatal(err)
	}
	url, err := c.StartProcess(ctx, proJson)
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("instId: %s", url)
	// instId: 1494559468465496065
}

func TestProcessNodeInfo(t *testing.T) {
	ctx := context.Background()
	info, err := c.ProcessNodeInfo(ctx, "1891731884862730241")
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("info: %+v", info)
}

func TestUpload(t *testing.T) {
	ctx := context.Background()
	f, err := os.Open("c:/1.txt")
	if err != nil {
		t.Fatal(err)
	}
	info, err := c.Upload(ctx, f, "1.txt")
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("key: %s", info.FileId)
}

func Test_UploadInvoiceVerify(t *testing.T) {
	ctx := context.Background()
	f, err := os.Open("c:/1.txt")
	if err != nil {
		t.Fatal(err)
	}
	fileInfo, invoice, err := c.UploadInvoiceVerify(ctx, f, "1.txt")
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("fileInfo: %+v", fileInfo)
	t.Logf("invoice: %+v", invoice)
}

func Test_CompleteTask(t *testing.T) {
	ctx := context.Background()
	err := c.CompleteTask(ctx, []byte(`{"taskId":"1501444296471523329","comment":"同意"}`))
	if err != nil {
		t.Fatal(err)
	}
}

func TestClient_GetAccountInfo(t *testing.T) {
	ctx := context.Background()
	list, err := c.GetAccountInfo(ctx, "", "1535156390906466305")
	//list, err := c.GetAccountInfo(ctx, "广州趣丸网络科技有限公司", "")
	//list, err := c.GetAccountInfo(ctx, "", "")
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(list)
	if len(list) > 0 {
		item := list[0]
		fmt.Println("Id: ", item.Id)
		fmt.Println("Bank: ", item.Bank)
		fmt.Println("SubBank: ", item.SubBank)
		fmt.Println("Account: ", item.Account)
		fmt.Println("CompanyName: ", item.CompanyName)
		fmt.Println("Province: ", item.Province)
		fmt.Println("City: ", item.City)
	}

}

func Test_GenSeqNo(t *testing.T) {
	ctx := context.Background()
	seqNo, err := c.GenSeqNo(ctx)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(seqNo)
}

func Test_StartCorpApplyProcess(t *testing.T) {
	ctx := context.Background()
	instId, err := c.StartCorpApplyProcess(ctx, &ContractApplyReq{
		CompanyName:       "广州趣丸网络科技有限公司",
		ApplyTime:         time.Now(),
		ContractTimeBegin: time.Date(2025, 2, 1, 0, 0, 0, 0, time.Local),
		ContractTimeEnd:   time.Date(2025, 2, 28, 0, 0, 0, 0, time.Local),
		SignDate:          time.Now(),
	})
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(instId)
}

func Test_AppendCorpApplicationArchive(t *testing.T) {
	ctx := context.Background()
	instId := "1892096420168404994"
	u := "https://obs-test-hw-bj-yy-ebc.obs.cn-north-4.myhuaweicloud.com/obsTest/tt_operation_background/202502/**********218644481.pdf?AccessKeyId=ZTGORAQHD9RPCW9SVY0A&Expires=1740032582&Signature=jwZVsPYAtfVxCojwLxE8vTShAJ0%3D"
	err := c.AppendCorpApplicationArchive(ctx, instId, u, "XXX协议.pdf")
	if err != nil {
		t.Fatal(err)
	}
}
