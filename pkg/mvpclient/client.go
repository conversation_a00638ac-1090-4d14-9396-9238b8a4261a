package MvpClient

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
)

type MvpClient struct {
	HttpClient *http.Client
	caller     string
	secret     string
}

func NewMvpClient(cfg config.Configer, caller, secret string) *MvpClient {
	return &MvpClient{
		HttpClient: config.NewHttpConfig(cfg).NewDefaultHttpClient(),
		caller:     caller,
		secret:     secret,
	}
}

type MvpResp struct {
	Data    json.RawMessage
	Error   string `json:"error"`
	ID      int64  `json:"id"`
	Message string `json:"message"`
	Status  int    `json:"statusCode"`
}

func (s *MvpClient) PostReturnMvpRespData(ctx context.Context,
	id int64, url string, data interface{}) (respData []byte, err error) {

	reqBody := GenCommonReqBody(id, s.caller, s.secret, data)
	req, err := http.NewRequestWithContext(ctx, "POST", url, strings.NewReader(reqBody))
	if err != nil {
		log.ErrorWithCtx(ctx, "Error Occured. %+v", err)
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	// use HttpClient to send request
	response, err := s.HttpClient.Do(req)
	if err != nil && response == nil {
		log.ErrorWithCtx(ctx, "Error sending request to API endpoint. %+v", err)
		return nil, err
	} else {
		// Close the connection to reuse it
		defer response.Body.Close()
		// Let's check if the work actually is done
		// We have seen inconsistencies even when we get 200 OK response
		body, err := ioutil.ReadAll(response.Body)
		if err != nil {
			log.ErrorWithCtx(ctx, "Couldn't parse response body. %+v", err)
			return nil, err
		}
		log.DebugWithCtx(ctx, "Response Body:%s", string(body))
		resp := &MvpResp{}
		err = json.Unmarshal(body, resp)
		if err != nil {
			log.ErrorWithCtx(ctx, "Couldn't marshal response body. %+v", err)
			return nil, err
		}
		if resp.Data == nil || len(resp.Data) == 0 {
			log.ErrorWithCtx(ctx, "PostReturnRespData %s resp %v err %v", string(body), resp, err)
			return nil, fmt.Errorf("PostReturnRespData req %s err %+v", reqBody, resp)
		}

		if resp.Status != 200 {
			log.ErrorWithCtx(ctx, "PostReturnRespData resp %v != 200", resp)
			return nil, errors.New(http.StatusText(resp.Status))
		}
		return resp.Data, err
	}
}
