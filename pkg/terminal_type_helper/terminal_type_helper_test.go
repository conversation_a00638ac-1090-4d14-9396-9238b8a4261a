package terminal_type_helper

import (
	"context"
	"flag"
	"fmt"
	"strconv"
	"testing"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
)

func init() {
	log.SetLevel(log.DebugLevel)
}

func TestGetAllTerminalTypeList(t *testing.T) {

	excludeTerm := uint32(0)

	if len(flag.Args()) >= 3 {
		platform, _ := strconv.Atoi(flag.Arg(0))
		os, _ := strconv.Atoi(flag.Arg(1))
		appId, _ := strconv.Atoi(flag.Arg(2))
		excludeTerm = protocol.PackTerminalType(protocol.Platform(platform), protocol.OS(os), protocol.AppID(appId))
	}

	fmt.Println(GetAllTerminalTypeList(context.Background(), excludeTerm))
}
