/*
 * @Description:
 * @Date: 2021-09-18 14:30:28
 * @LastEditors: liang
 * @LastEditTime: 2021-10-14 20:08:54
 */
package tools

import (
	"testing"

	"gopkg.in/stretchr/testify.v1/assert"
)

func TestLowbound(t *testing.T) {
	type TestItems struct {
		arr    []int
		target int
		ans    int
	}
	arr := []TestItems{
		{
			arr:    []int{0, 100, 300, 400, 500, 600},
			target: 1,
			ans:    0,
		},
		{
			arr:    []int{0, 100, 300, 400, 500, 600},
			target: 100,
			ans:    100,
		},
		{
			arr:    []int{0, 100, 300, 400, 500, 600},
			target: 210,
			ans:    100,
		},
		{
			arr:    []int{0, 100, 300, 400, 500, 600},
			target: 550,
			ans:    500,
		},
		{
			arr:    []int{0, 100, 300, 400, 500, 600},
			target: 601,
			ans:    600,
		},
		{
			arr:    []int{0, 0, 0, 0, 100, 100, 300, 400, 500, 600},
			target: 105,
			ans:    100,
		},
		{
			arr:    []int{0, 0, 0, 0, 100, 100, 300, 400, 500, 600},
			target: -2,
			ans:    -1,
		},
		{
			arr:    []int{0, 100, 100, 300, 400, 500, 600},
			target: 0,
			ans:    0,
		},
	}
	for _, v := range arr {
		i, ok := Lowbound(v.arr, v.target, false)
		if ok {
			assert.Equal(t, v.arr[i], v.ans)
		} else {
			assert.Equal(t, i, -1)
		}

	}

}
