package timer

import (
	"os"
	"reflect"
	"testing"
)

func Test_newDefaultRedisConfig(t *testing.T) {
	tests := []struct {
		name     string
		want     *defaultRedisConfig
		wantErr  bool
		initFunc func()
	}{
		{
			name:    "缺少地址环境变量",
			want:    nil,
			wantErr: true,
			initFunc: func() {
				os.Setenv(EnvValueRedisPassword, "123")
			},
		},
		{
			name:    "地址格式错误",
			want:    nil,
			wantErr: true,
			initFunc: func() {
				os.Setenv(EnvValueRedisPassword, "123")
				os.Setenv(EnvValueRedisAddr, "123")
			},
		},
		{
			name:    "端口格式错误",
			want:    nil,
			wantErr: true,
			initFunc: func() {
				os.Setenv(EnvValueRedisPassword, "123")
				os.Setenv(EnvValueRedisAddr, "*************: qwe")
			},
		},
		{
			name:    "正常",
			want:    &defaultRedisConfig{pwd: "123", host: "*************", port: 123},
			wantErr: false,
			initFunc: func() {
				os.Setenv(EnvValueRedisPassword, "123")
				os.Setenv(EnvValueRedisAddr, "*************:123")
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := getDefaultRedisConfig()
			if (err != nil) != tt.wantErr {
				t.Errorf("newDefaultRedisConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("newDefaultRedisConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
	os.Setenv(EnvValueRedisPassword, "")
	os.Setenv(EnvValueRedisAddr, "")
}
