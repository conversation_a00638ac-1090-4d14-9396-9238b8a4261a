package dye

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func Test_manager_readConfig(t *testing.T) {
	newManager := NewManager("test", "./config.json")
	mm := newManager.(*manager)
	config, err := mm.readConfig()
	assert.NoError(t, err)
	assert.Equal(t, "test", config.ServiceName)
	assert.Equal(t, "123", config.Dye[0].TaskName)
	assert.Equal(t, EnvTagSubMain, config.Dye[0].EnvTag)

	newManager = NewManager("test1", "./config.json")
	mm = newManager.(*manager)
	config, err = mm.readConfig()
	assert.NoError(t, err)
	assert.Equal(t, "test1", config.ServiceName)
	assert.Equal(t, "321", config.Dye[0].TaskName)
	assert.Equal(t, EnvTagBase, config.Dye[0].EnvTag)

}

func Test_manager_GetTaskConfig(t *testing.T) {
	type fields struct {
		index map[string]*Dye
	}
	type args struct {
		taskName string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *Dye
	}{
		{
			name: "查询到配置",
			fields: fields{
				index: map[string]*Dye{
					"123": {
						TaskName: "123",
						EnvTag:   EnvTagSubMain,
					},
				},
			},
			args: args{
				taskName: "123",
			},
			want: &Dye{
				TaskName: "123",
				EnvTag:   EnvTagSubMain,
			},
		},
		{
			name: "查询不到配置",
			fields: fields{
				index: map[string]*Dye{
					"1233": {
						TaskName: "1233",
						EnvTag:   EnvTagSubMain,
					},
				},
			},
			args: args{
				taskName: "123",
			},
			want: &Dye{
				TaskName: "123",
				EnvTag:   EnvTagBase,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &manager{
				index: tt.fields.index,
			}
			assert.Equalf(t, tt.want, m.GetTaskConfig(tt.args.taskName), "GetTaskConfig(%v)", tt.args.taskName)
		})
	}
}

func Test_manager_update(t *testing.T) {

	newManager := NewManager("test", "./config.json")

	mm := newManager.(*manager)
	mm.update()

	assert.NotEmpty(t, mm.index)

}
