package cmdable

import (
	"context"
	v8 "github.com/go-redis/redis/v8"
	v2 "golang.52tt.com/pkg/store/redis"
)

type Z = v8.Z

type Cmdable interface {
	Incr(ctx context.Context, key string) (int64, error)
	ZAdd(ctx context.Context, key string, members ...*Z) (int64, error)
	ZRem(ctx context.Context, key string, members ...interface{}) (int64, error)
	ZRangeWithScores(ctx context.Context, key string, start, stop int64) ([]Z, error)
	ZRemRangeByScore(ctx context.Context, key, min, max string) (int64, error)
}

func NewV8Cmdable(cmdable v8.Cmdable) Cmdable {
	return &v8Cmdable{
		Cmdable: cmdable,
	}
}

func NewV2Cmdable(cmdable v2.CmdableV2) Cmdable {
	return &v2Cmdable{
		CmdableV2: cmdable,
	}
}

type v8Cmdable struct {
	v8.Cmdable
}

func (v v8Cmdable) Incr(ctx context.Context, key string) (int64, error) {
	return v.Cmdable.Incr(ctx, key).Result()
}

func (v v8Cmdable) ZAdd(ctx context.Context, key string, members ...*Z) (int64, error) {
	return v.Cmdable.ZAdd(ctx, key, members...).Result()
}

func (v v8Cmdable) ZRem(ctx context.Context, key string, members ...interface{}) (int64, error) {
	return v.Cmdable.ZRem(ctx, key, members...).Result()
}

func (v v8Cmdable) ZRangeWithScores(ctx context.Context, key string, start, stop int64) ([]Z, error) {
	return v.Cmdable.ZRangeWithScores(ctx, key, start, stop).Result()
}

func (v v8Cmdable) ZRemRangeByScore(ctx context.Context, key, min, max string) (int64, error) {
	return v.Cmdable.ZRemRangeByScore(ctx, key, min, max).Result()
}

type v2Cmdable struct {
	v2.CmdableV2
}

func (v v2Cmdable) Incr(ctx context.Context, key string) (int64, error) {
	return v.CmdableV2.Incr(ctx, key).Result()
}

func (v v2Cmdable) ZAdd(ctx context.Context, key string, members ...*Z) (int64, error) {
	targets := make([]v2.Z, len(members))
	for i, member := range members {
		targets[i] = v2.Z{
			Score:  member.Score,
			Member: member.Member,
		}
	}
	return v.CmdableV2.ZAdd(ctx, key, targets...).Result()
}

func (v v2Cmdable) ZRem(ctx context.Context, key string, members ...interface{}) (int64, error) {
	return v.CmdableV2.ZRem(ctx, key, members...).Result()
}

func (v v2Cmdable) ZRangeWithScores(ctx context.Context, key string, start, stop int64) ([]Z, error) {
	members, err := v.CmdableV2.ZRangeWithScores(ctx, key, start, stop).Result()
	if err != nil {
		return nil, err
	}

	targets := make([]v8.Z, len(members))
	for i, member := range members {
		targets[i] = v8.Z{
			Score:  member.Score,
			Member: member.Member,
		}
	}
	return targets, nil
}

func (v v2Cmdable) ZRemRangeByScore(ctx context.Context, key, min, max string) (int64, error) {
	return v.CmdableV2.ZRemRangeByScore(ctx, key, min, max).Result()
}
