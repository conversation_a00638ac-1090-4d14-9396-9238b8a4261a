// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/pkg/timer/node (interfaces: Manager,Store,Pool)

// Package node is a generated GoMock package.
package node

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
)

// MockManager is a mock of Manager interface.
type MockManager struct {
	ctrl     *gomock.Controller
	recorder *MockManagerMockRecorder
}

// MockManagerMockRecorder is the mock recorder for MockManager.
type MockManagerMockRecorder struct {
	mock *MockManager
}

// NewMockManager creates a new mock instance.
func NewMockManager(ctrl *gomock.Controller) *MockManager {
	mock := &MockManager{ctrl: ctrl}
	mock.recorder = &MockManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockManager) EXPECT() *MockManagerMockRecorder {
	return m.recorder
}

// GetAvailableNode mocks base method.
func (m *MockManager) GetAvailableNode(arg0 context.Context) *Node {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvailableNode", arg0)
	ret0, _ := ret[0].(*Node)
	return ret0
}

// GetAvailableNode indicates an expected call of GetAvailableNode.
func (mr *MockManagerMockRecorder) GetAvailableNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableNode", reflect.TypeOf((*MockManager)(nil).GetAvailableNode), arg0)
}

// GetCurNode mocks base method.
func (m *MockManager) GetCurNode() *Node {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurNode")
	ret0, _ := ret[0].(*Node)
	return ret0
}

// GetCurNode indicates an expected call of GetCurNode.
func (mr *MockManagerMockRecorder) GetCurNode() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurNode", reflect.TypeOf((*MockManager)(nil).GetCurNode))
}

// Start mocks base method.
func (m *MockManager) Start(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Start indicates an expected call of Start.
func (mr *MockManagerMockRecorder) Start(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockManager)(nil).Start), arg0)
}

// Stop mocks base method.
func (m *MockManager) Stop(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stop", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Stop indicates an expected call of Stop.
func (mr *MockManagerMockRecorder) Stop(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockManager)(nil).Stop), arg0)
}

// MockStore is a mock of Store interface.
type MockStore struct {
	ctrl     *gomock.Controller
	recorder *MockStoreMockRecorder
}

// MockStoreMockRecorder is the mock recorder for MockStore.
type MockStoreMockRecorder struct {
	mock *MockStore
}

// NewMockStore creates a new mock instance.
func NewMockStore(ctrl *gomock.Controller) *MockStore {
	mock := &MockStore{ctrl: ctrl}
	mock.recorder = &MockStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStore) EXPECT() *MockStoreMockRecorder {
	return m.recorder
}

// Deregister mocks base method.
func (m *MockStore) Deregister(arg0 context.Context, arg1 *Node) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Deregister", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Deregister indicates an expected call of Deregister.
func (mr *MockStoreMockRecorder) Deregister(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Deregister", reflect.TypeOf((*MockStore)(nil).Deregister), arg0, arg1)
}

// GetNodeList mocks base method.
func (m *MockStore) GetNodeList(arg0 context.Context, arg1 string) ([]*Node, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeList", arg0, arg1)
	ret0, _ := ret[0].([]*Node)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeList indicates an expected call of GetNodeList.
func (mr *MockStoreMockRecorder) GetNodeList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeList", reflect.TypeOf((*MockStore)(nil).GetNodeList), arg0, arg1)
}

// Register mocks base method.
func (m *MockStore) Register(arg0 context.Context, arg1 *Node, arg2 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Register", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Register indicates an expected call of Register.
func (mr *MockStoreMockRecorder) Register(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Register", reflect.TypeOf((*MockStore)(nil).Register), arg0, arg1, arg2)
}

// MockPool is a mock of Pool interface.
type MockPool struct {
	ctrl     *gomock.Controller
	recorder *MockPoolMockRecorder
}

// MockPoolMockRecorder is the mock recorder for MockPool.
type MockPoolMockRecorder struct {
	mock *MockPool
}

// NewMockPool creates a new mock instance.
func NewMockPool(ctrl *gomock.Controller) *MockPool {
	mock := &MockPool{ctrl: ctrl}
	mock.recorder = &MockPoolMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPool) EXPECT() *MockPoolMockRecorder {
	return m.recorder
}

// GetMainNode mocks base method.
func (m *MockPool) GetMainNode() *Node {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMainNode")
	ret0, _ := ret[0].(*Node)
	return ret0
}

// GetMainNode indicates an expected call of GetMainNode.
func (mr *MockPoolMockRecorder) GetMainNode() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainNode", reflect.TypeOf((*MockPool)(nil).GetMainNode))
}

// Update mocks base method.
func (m *MockPool) Update(arg0 []*Node) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Update", arg0)
}

// Update indicates an expected call of Update.
func (mr *MockPoolMockRecorder) Update(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockPool)(nil).Update), arg0)
}
