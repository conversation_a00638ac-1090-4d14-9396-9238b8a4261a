package deal_token

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"time"
)

const (
	ProbGameCenter = "prob-game-center"
)

type DealToken struct {
	TradeNo    string `json:"tradeNo"`
	OrderID    string `json:"orderID"`
	Sign       string `json:"sign"`
	Ctime      string `json:"ctime"`
	ServerName string `json:"serverName"`
	BuyerID    int64  `json:"buyerId"`
	TotalPrice int64  `json:"totalPirce"` //tbean那边拼错鸟
	PrevToken  string `json:"prevToken"`  //前一个节点的dealToken的json.Marshaler
	PrevMd5    string `json:"prevMd5"`    //PrevToken的md5
}

func getMD5Encode(data string) string {
	h := md5.New()
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

func getSign(tradeNo string, buyerID int64, totalPrice int64, ctime string) string {
	return getMD5Encode(fmt.Sprintf("%v#%v#%v#%v", tradeNo, buyerID, totalPrice, ctime))
}

func Decode(dealTokenStr string) (*DealToken, error) {
	dtData := &DealToken{}
	err := json.Unmarshal([]byte(dealTokenStr), dtData)
	if err != nil {
		return dtData, err
	}

	if dtData.Sign != getSign(dtData.TradeNo, dtData.BuyerID, dtData.TotalPrice, dtData.Ctime) {
		return dtData, errors.New("sign not match")
	}

	if len(dtData.PrevToken) > 0 {
		if dtData.PrevMd5 != getMD5Encode(dtData.PrevToken) {
			return dtData, errors.New("prevMd5 not match")
		}
	}
	return dtData, nil
}

func Encode(dt *DealToken) (string, error) {
	marshal, err := json.Marshal(*dt)
	return string(marshal), err
}

func Check(dealTokenStr string) error {
	dtd, err := Decode(dealTokenStr)
	if err != nil {
		return err
	}
	if len(dtd.PrevToken) > 0 {
		return Check(dtd.PrevToken)
	}
	return nil
}

func NewDealTokenData(tradeNo string, orderID string, serverName string, buyerID int64, totalPrice int64) *DealToken {
	dt := &DealToken{
		TradeNo:    tradeNo,
		OrderID:    orderID,
		Ctime:      time.Now().Format("2006-01-02 15:04:05"),
		ServerName: serverName,
		BuyerID:    buyerID,
		TotalPrice: totalPrice,
		PrevToken:  "",
		PrevMd5:    "",
	}
	dt.Sign = getSign(dt.TradeNo, dt.BuyerID, dt.TotalPrice, dt.Ctime)

	return dt
}

func AddDealToken(orgDealTokenStr string, newDealToken *DealToken) (string, error) {
	newDealToken.PrevToken = orgDealTokenStr
	newDealToken.PrevMd5 = getMD5Encode(orgDealTokenStr)
	newDealTokenBytes, err := json.Marshal(newDealToken)
	return string(newDealTokenBytes), err
}



