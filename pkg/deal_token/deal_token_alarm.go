package deal_token

import (
	"bytes"
	"encoding/json"
	"errors"
	"golang.52tt.com/pkg/log"
	"net/http"
	"time"
)

type feiShuMsg struct {
	MsgType string      `json:"msg_type"`
	Content interface{} `json:"content"`
}

type textContent struct {
	Text string `json:"text"`
}

func sendFeishu(text string, env string, feishuUrl string) error {

	client := http.Client{
		Timeout: 10 * time.Second,
	}
	text = "<" + env + "> " + text

	msg := feiShuMsg{MsgType: "text", Content: textContent{Text: text}}

	b, _ := json.Marshal(msg)

	resp, err := client.Post(feishuUrl, "application/json", bytes.NewReader(b))
	if err != nil {
		log.Errorf("SendToFeiShu Post fail url : %s err :%v", feishuUrl, err)
		return err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Errorf("SendToFeiShu Post response not ok : %s", resp.Status)
		return errors.New(resp.Status)
	}

	log.Debugln("SendFeishu over")

	return nil
}

func sendFeishuText(text string) error {
	sendAlarm, Evn, feishuUrl := getAlarmConfig()
	if sendAlarm == false {
		return nil
	}
	return sendFeishu(text, Evn, feishuUrl)
}