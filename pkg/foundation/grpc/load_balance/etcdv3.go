package grpclb

import (
	clientv3 "go.etcd.io/etcd/client/v3"
	"golang.52tt.com/pkg/foundation/grpc/naming"
	"golang.org/x/net/context"

	"encoding/json"
	"errors"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"os"
	"path"
	"strconv"
	"sync"
	"time"
)

const (
	ttl = int64(15)
)

var (
	errGRPCResolverNotReady   = errors.New("gRPC resolver is not ready.")
	defaultEtcdv3Resolver     *etcdv3Resolver
	defaultEtcdv3ResolverOnce sync.Once
)

func DefaultEtcdv3Resolver() *etcdv3Resolver {
	defaultEtcdv3ResolverOnce.Do(func() {
		const etcdClientConf = "etcd_client.json"
		clientConfigPath := os.Getenv(config.ClientConfigPathEnvName)
		if len(clientConfigPath) == 0 {
			clientConfigPath = config.DefaultClientConfigPath()
		}
		conf := path.Join(clientConfigPath, etcdClientConf)
		defaultEtcdv3Resolver = NewEtcdv3Resolver(context.Background(), conf)
	})
	return defaultEtcdv3Resolver
}

func GetEtcdv3Endpoints() ([]string, error) {
	const etcdClientConf = "etcd_client.json"
	clientConfigPath := os.Getenv(config.ClientConfigPathEnvName)
	if len(clientConfigPath) == 0 {
		clientConfigPath = config.DefaultClientConfigPath()
	}
	var endPoints []string
	conf := path.Join(clientConfigPath, etcdClientConf)
	f, err := os.Open(conf)
	if err != nil {
		log.Errorf("Failed to open gRPC resolver config: %s, err: %v", conf, err)
		return endPoints, err
	}

	var cfg clientv3.Config
	if err = json.NewDecoder(f).Decode(&cfg); err != nil {
		log.Errorf("Failed to decode gRPC resolver config: %s, err: %v", conf, err)
		f.Close()
		return endPoints, err
	}
	f.Close()

	return cfg.Endpoints, nil
}

type etcdv3Resolver struct {
	gr          *naming.GRPCResolver
	mu          sync.Mutex
	ready       chan struct{}
	serverWatch *ServerWatcherT
	myAddr      string
}

// NewEtcdv3Resolver creates a new resolver implemented with etcd client v3
func NewEtcdv3Resolver(ctx context.Context, conf string) *etcdv3Resolver {
	r := &etcdv3Resolver{}
	go r.initGRPCResolver(ctx, conf)
	return r
}

func (r *etcdv3Resolver) initGRPCResolver(ctx context.Context, conf string) {
	for {
		for {
			var cfg clientv3.Config
			var cli *clientv3.Client
			f, err := os.Open(conf)
			if err != nil {
				log.Errorf("Failed to open gRPC resolver config: %s, err: %v", conf, err)
				break
			}

			if err = json.NewDecoder(f).Decode(&cfg); err != nil {
				log.Errorf("Failed to decode gRPC resolver config: %s, err: %v", conf, err)
				f.Close()
				break
			}
			f.Close()

			//log.Debugf("Initializing gRPC resolver from config: %+v %+v", conf, cfg)
			cli, err = clientv3.New(cfg)
			if err != nil {
				log.Errorf("Failed to create gRPC resolver from config: %+v, err: %v", cfg, err)
				break
			}

			r.mu.Lock()
			r.gr = &naming.GRPCResolver{Client: cli}
			if r.ready != nil {
				close(r.ready)
			}
			r.mu.Unlock()
			//log.Debugf("Initialized gRPC resolver from config: %+v %+v", conf, cfg)
			return
		}

		select {
		case <-ctx.Done():
			return
		case <-time.After(3 * time.Second):
		}
	}
}

// Register a named server to etcd
func (r *etcdv3Resolver) Register(ctx context.Context, name string, serverConfig *config.ServerConfig, isSmallProcess bool) {
	r.myAddr = serverConfig.GetAddr()

	r.serverWatch = &ServerWatcherT{
		name:   name,
		ctxend: make(chan struct{}),
	}
	go func() {
		for {
			gr, err := r.wait(ctx, false)
			if err != nil {
				if ctx.Err() != nil {
					// context was cancelled or deadline exceeded
					break
				}
			}

			strIndex, bDo := r.registerIndex(ctx, gr, name)
			if !bDo {
				continue
			}

			leaseGrant, err := gr.Client.Grant(ctx, ttl)
			if err != nil {
				log.Errorf("Etcd Grant err:%s", err.Error())
				continue
			}

			if isSmallProcess {
				serverConfig.SetAddr(strIndex)
				r.myAddr = strIndex
			}
			err = gr.Update(ctx, name, naming.Update{Op: naming.Add, Addr: serverConfig.GetAddr(), Metadata: strIndex}, clientv3.WithLease(leaseGrant.ID))
			if err != nil {
				log.Errorf("Etcd Update err:%s", err.Error())
				continue
			}

			r.serverWatch.resolver = gr
			go r.serverWatch.StartWatching(ctx)
			// KeepAlive until ctx closed or timeout
			respChan, err := gr.Client.KeepAlive(ctx, leaseGrant.ID)
			if err != nil {
				log.Errorf("KeepAlive err:%s", err.Error())
				continue
			}

			for range respChan {
			}

			if ctx.Err() != nil {
				// context was cancelled or deadline exceeded
				log.Infof("Register End, err:%s", ctx.Err().Error())
				r.serverWatch.closeCtx()
				break
			}
		}
	}()

	return
}

func (r *etcdv3Resolver) registerIndex(ctx context.Context, gr *naming.GRPCResolver, name string) (string, bool) {
	bFirst := false
	nameIndexKey := "services/" + name + "/index"
	var compareValue string
	var nIndex int
	getResp, err := r.gr.Client.Get(ctx, nameIndexKey)
	//fmt.Printf("get key:%s,%+v.%+v", nameIndexKey, getResp, err)
	if err != nil {
		return "", false
	} else {
		if len(getResp.Kvs) == 0 {
			nIndex = 0
			bFirst = true
		} else {
			compareValue = string(getResp.Kvs[0].Value)
			/*if compareValue == "" {
				compareValue = "0"
			}*/
			log.Infof("registerIndex compareValue:%s", compareValue)
			nIndex, err = strconv.Atoi(compareValue)
			if err != nil {
				nIndex = 0
				log.Errorf("strconv.Atoi, err: %s, compareValue:%s", err.Error(), compareValue)
			}
		}
	}
	nIndex++
	if nIndex >= 80000000 {
		nIndex = 0 //数值非常大时重置
	}
	strIndex := strconv.Itoa(nIndex)
	if bFirst {
		_, err := gr.Client.Put(ctx, nameIndexKey, strIndex)
		if err != nil {
			return "", false
		}
	} else {
		txn := gr.Client.Txn(ctx)
		txnResp, err := txn.If(clientv3.Compare(clientv3.Value(nameIndexKey), "=", compareValue)).
			Then(clientv3.OpPut(nameIndexKey, strIndex)).
			Commit()
		if err != nil {
			log.Errorf("Etcd Txn SetIndex Fail, err:%+v, key:%s, value:%d", err, nameIndexKey, nIndex)
			return "", false
		}
		if txnResp.Succeeded {
			log.Infof("Etcd Txn SetIndex Suc, key:%s, value:%d", nameIndexKey, nIndex)
		} else {
			log.Errorf("Etcd Txn SetIndex Fail, err:%+v, key:%s, value:%d", err, nameIndexKey, nIndex)
			return "", false
		}
	}
	return strIndex, true
}

// Unregister a named server from etcd
func (r *etcdv3Resolver) Unregister(ctx context.Context, name string, addr string) error {
	if gr, err := r.wait(ctx, true); err != nil {
		return err
	} else {
		return gr.Update(ctx, name, naming.Update{Op: naming.Delete, Addr: addr})
	}
}

func (r *etcdv3Resolver) Resolve(target string) (naming.Watcher, error) {
	if gr, err := r.wait(context.Background(), false); err != nil {
		return nil, err
	} else {
		return gr.Resolve(target)
	}
}

func (r *etcdv3Resolver) wait(ctx context.Context, failFast bool) (*naming.GRPCResolver, error) {
	for {
		r.mu.Lock()
		// already initialized
		if r.gr != nil {
			r.mu.Unlock()
			return r.gr, nil
		}

		// r.gr == nil
		if failFast {
			r.mu.Unlock()
			return nil, errGRPCResolverNotReady
		}

		ready := r.ready
		if ready == nil {
			ready = make(chan struct{})
			r.ready = ready
		}
		r.mu.Unlock()

		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-ready:
			// continue
		}
	}
}

type ProcessInfo struct {
	addr  string
	index uint32
}

type ProcessIndexT struct {
	index uint32
}

type ServerWatcherT struct {
	name         string
	resolver     naming.Resolver
	processes    []ProcessInfo
	processMutex sync.RWMutex
	ctxend       chan struct{}
}

func (sw *ServerWatcherT) closeCtx() {
	close(sw.ctxend)
}

func (sw *ServerWatcherT) StartWatching(ctx context.Context) {
	for {
		log.Infoln("StartWatching")
		if ctx.Err() != nil {
			// context was cancelled or deadline exceeded
			log.Errorf("StartWatching err:%v", ctx.Err())
			break
		}

		w, err := sw.resolver.Resolve(sw.name)
		log.Infoln("StartWatching resolve:", sw.name)
		if err != nil {
			log.Errorf("[watcher] Failed to resolve %s, err: %v, try later", sw.name, err)
			time.Sleep(time.Second * 3)
			continue
		}

		for {
			updates, err := w.Next()
			//log.Infof("StartWatching Next:%+v", updates)
			if err != nil {
				log.Errorf("[watcher] Next err: %v", err)
				// re-resolve in out loop
				break
			}
			sw.onUpdates(updates)
			select {
			case <-sw.ctxend:
				log.Infof("StartWatching End, ctx err:%+v", ctx.Err())
				return
			case <-time.After(1 * time.Millisecond):
			}
		}
	}
}

// 定时调度，不用主动通知
func (sw *ServerWatcherT) onUpdates(updates []*naming.Update) {
	sw.processMutex.Lock()
	for _, update := range updates {
		switch update.Op {
		case naming.Add:
			// remove old addr
			for i, v := range sw.processes {
				if v.addr == update.Addr {
					copy(sw.processes[i:], sw.processes[i+1:])
					sw.processes = sw.processes[:len(sw.processes)-1]
					log.Infof("[watcher] %s delete existing address addr %s", sw.name, update.Addr)
					break
				}
			}

			//log.Infof("[watcher] %s add new addr %+v", sw.name, update)
			proncessIndex := 0
			//ok := false
			if update.Metadata == nil {
				continue
			} else {
				strIndex, ok := update.Metadata.(string)
				if !ok {
					continue
				}
				var err error
				proncessIndex, err = strconv.Atoi(strIndex)
				if err != nil {
					continue
				}
			}
			sw.processes = append(sw.processes, ProcessInfo{addr: update.Addr, index: uint32(proncessIndex)})
			log.Infof("[watcher] %s add new addr %s, index:%d", sw.name, update.Addr, proncessIndex)
		case naming.Delete:
			for i, v := range sw.processes {
				if v.addr == update.Addr {
					copy(sw.processes[i:], sw.processes[i+1:])
					sw.processes = sw.processes[:len(sw.processes)-1]
					log.Infof("[watcher] %s delete addr %s", sw.name, update.Addr)
					break
				}
			}
		default:
			log.Errorln("[watcher] Unknown update.Op ", update.Op)
		}
	}
	sw.processMutex.Unlock()
}

func (sw *ServerWatcherT) GetProcesses() []ProcessInfo {
	defer sw.processMutex.RUnlock()
	sw.processMutex.RLock()
	nLen := len(sw.processes)
	if nLen == 0 {
		return nil
	}
	newProcessInfo := make([]ProcessInfo, nLen)
	for i := 0; i < nLen; i++ {
		newProcessInfo[i] = sw.processes[i]
	}
	return newProcessInfo
}

/**
func (r *etcdv3Resolver) keyForSkydns(name string, addr string) string {
	ss := strings.Split(name, ".")
	w := bytes.NewBufferString("/skydns")
	for idx := len(ss) - 1; idx >= 0; idx-- {
		fmt.Fprintf(w, "/%s", ss[idx])
	}
	fmt.Fprintf(w, "/%s", addr)
	return w.String()
}

func (r *etcdv3Resolver) valueForSkydns(addr string) string {
	type skydnsValue struct {
		Host string `json:"host"`
		Port int    `json:"port"`
	}

	ss := strings.Split(addr, ":")
	host := ss[0]
	port := 0
	if len(ss) > 1 {
		port, _ = strconv.Atoi(ss[1])
	}
	v, _ := json.Marshal(&skydnsValue{host, port})
	return string(v)
}
**/
