package grpclb

import (
	"errors"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/startup/lifecycle"
	"golang.52tt.com/pkg/log"
	"sort"
	"sync"
)

var (
	processOnce     sync.Once
	etcdProcessInst *EtcdProcessT
)

func GetEtcdProcess() *EtcdProcessT {
	processOnce.Do(func() {
		etcdProcessInst = &EtcdProcessT{}
	})
	return etcdProcessInst
}

type EtcdProcessT struct {
	defaultEtcdv3Resolver *etcdv3Resolver
}

func (e *EtcdProcessT) SetResolve(resolve *etcdv3Resolver) {
	defaultEtcdv3Resolver = resolve
}

func (e *EtcdProcessT) GetIndex() (uint32, bool) {
	if defaultEtcdv3Resolver == nil {
		log.Warnln("GetIndex defaultEtcdv3Resolver nil")
		return 0, false
	} else if defaultEtcdv3Resolver.myAddr == "" {
		log.Warnln("GetIndex defaultEtcdv3Resolver.myAddr nil")
		return 0, false
	}
	processInfos := defaultEtcdv3Resolver.serverWatch.GetProcesses()
	if processInfos == nil {
		log.Errorf("GetIndex GetProcesses cnt 0, name:%s", defaultEtcdv3Resolver.serverWatch.name)
		return 0, false
	}

	for _, info := range processInfos {
		if info.addr == defaultEtcdv3Resolver.myAddr {
			return info.index, true
		}
	}
	return 0, false
}

func (e *EtcdProcessT) GetIndexAndSmall(redisCnt int) (int, bool, error) {
	if defaultEtcdv3Resolver == nil {
		log.Warnln("GetIndexAndSmall defaultEtcdv3Resolver nil")
		return -1, false, errors.New("resolver nil")
	} else if defaultEtcdv3Resolver.myAddr == "" {
		log.Warnln("GetIndexAndSmall defaultEtcdv3Resolver.myAddr nil")
		return -1, false, errors.New("resolve myadd nil")
	}
	processInfos := defaultEtcdv3Resolver.serverWatch.GetProcesses()
	if processInfos == nil {
		log.Errorf("GetIndexAndSmall GetProcesses cnt 0, name:%s", defaultEtcdv3Resolver.serverWatch.name)
		return -1, false, errors.New("GetProcesses nil")
	}

	var myIndex uint32
	var lastSmallIndexs []int
	var smallI int
	var smallIndex uint32
	for i, info := range processInfos {
		if i == 0 {
			smallIndex = info.index
			smallI = i
		} else {
			if info.index < smallIndex {
				smallIndex = info.index
				smallI = i
			}
		}
		lastSmallIndexs = append(lastSmallIndexs, int(info.index))
		if info.addr == defaultEtcdv3Resolver.myAddr {
			myIndex = info.index
		}
	}

	isSmall := false
	processInfo := processInfos[smallI]
	if processInfo.addr == defaultEtcdv3Resolver.myAddr {
		isSmall = true
	}

	sort.Sort(sort.IntSlice(lastSmallIndexs))

	for i, lastSmallIndex := range lastSmallIndexs {
		if i < redisCnt {
			if lastSmallIndex == int(myIndex) {
				log.Infof("IsLastSmallProcess i:%d, redisCnt:%d, index:%d", i, redisCnt, myIndex)
				return i, isSmall, nil
			}
		}
	}
	if isSmall {
		log.Infof("GetIndexAndSmall addr:%s, name:%s", defaultEtcdv3Resolver.myAddr, defaultEtcdv3Resolver.serverWatch.name)
	}
	return -1, isSmall, nil
}

func (e *EtcdProcessT) IsSmallProcess() bool {
	if lifecycle.IsSmallProcess() {
		return true
	}

	if defaultEtcdv3Resolver == nil {
		log.Warnln("IsSmallProcess defaultEtcdv3Resolver nil")
		return false
	} else if defaultEtcdv3Resolver.myAddr == "" {
		log.Warnln("IsSmallProcess defaultEtcdv3Resolver.myAddr nil")
		return false
	}
	processInfos := defaultEtcdv3Resolver.serverWatch.GetProcesses()
	if processInfos == nil {
		log.Errorf("IsSmallProcess GetProcesses cnt 0, name:%s", defaultEtcdv3Resolver.serverWatch.name)
		return false
	}
	var smallI int
	var smallIndex uint32
	for i, info := range processInfos {
		if i == 0 {
			smallIndex = info.index
			smallI = i
		} else {
			if info.index < smallIndex {
				smallIndex = info.index
				smallI = i
			}
		}
	}
	processInfo := processInfos[smallI]
	if processInfo.addr == defaultEtcdv3Resolver.myAddr {
		log.Infof("IsSmallProcess addr:%s, name:%s", defaultEtcdv3Resolver.myAddr, defaultEtcdv3Resolver.serverWatch.name)
		return true
	}
	log.Infof("IsSmallProcess check addr:%s,%s, name:%s", processInfo.addr, defaultEtcdv3Resolver.myAddr, defaultEtcdv3Resolver.serverWatch.name)
	return false
}

func (e *EtcdProcessT) IsBigProcess() bool {
	if lifecycle.IsBigProcess() {
		return true
	}

	if defaultEtcdv3Resolver == nil {
		log.Warnln("IsBigProcess defaultEtcdv3Resolver nil")
		return false
	} else if defaultEtcdv3Resolver.myAddr == "" {
		log.Warnln("IsBigProcess defaultEtcdv3Resolver.myAddr nil")
		return false
	}
	processInfos := defaultEtcdv3Resolver.serverWatch.GetProcesses()
	if processInfos == nil {
		log.Errorf("IsBigProcess GetProcesses cnt 0, name:%s", defaultEtcdv3Resolver.serverWatch.name)
		return false
	}
	var bigI int
	var bigIndex uint32
	for i, info := range processInfos {
		if i == 0 {
			bigIndex = info.index
			bigI = i
		} else {
			if info.index > bigIndex {
				bigIndex = info.index
				bigI = i
			}
		}
	}
	processInfo := processInfos[bigI]
	if processInfo.addr == defaultEtcdv3Resolver.myAddr {
		log.Infof("IsBigProcess addr:%s, name:%s", defaultEtcdv3Resolver.myAddr, defaultEtcdv3Resolver.serverWatch.name)
		return true
	}
	return false
}

func (e *EtcdProcessT) GetNodeId() int {
	if defaultEtcdv3Resolver == nil {
		log.Warnln("IsSmallProcess defaultEtcdv3Resolver nil")
		return 0
	} else if defaultEtcdv3Resolver.myAddr == "" {
		log.Warnln("IsSmallProcess defaultEtcdv3Resolver.myAddr nil")
		return 0
	}

	processInfos := defaultEtcdv3Resolver.serverWatch.GetProcesses()
	if processInfos == nil {
		log.Errorf("IsBigProcess GetProcesses cnt 0, name:%s", defaultEtcdv3Resolver.serverWatch.name)
		return 0
	}

	for i, info := range processInfos {
		if defaultEtcdv3Resolver.myAddr == info.addr {
			return i
		}
	}
	return 0
}
