package grpcClient

import (
	"google.golang.org/grpc/encoding"
	"google.golang.org/grpc/encoding/proto"
)

type rawCodec struct {
	protoCodec encoding.Codec
}

func newRawCodec() *rawCodec {
	return &rawCodec{
		protoCodec: encoding.GetCodec(proto.Name),
	}
}

func (c *rawCodec) Marshal(v interface{}) ([]byte, error) {
	return c.protoCodec.Marshal(v)
}

func (c *rawCodec) Unmarshal(data []byte, v interface{}) error {
	if len(data) > 0 {
		return c.protoCodec.Unmarshal(data, v)
	}
	return nil
}

//func (c *rawCodec) ProtoUnmarshal(v interface{}) error {
//	return c.protoCodec.Unmarshal(c.payload, v)
//}

func (c *rawCodec) Name() string { return "RawCodec" }

func (c *rawCodec) String() string { return c.Name() }
