package utils

import "context"

func GetIntFromContext(ctx context.Context, key string) int {
	if i := ctx.Value(key); nil != i {
		if a, ok := i.(int); ok {
			return a
		}
	}
	return 0
}

func GetUint32FromContext(ctx context.Context, key string) uint32 {
	if i := ctx.Value(key); nil != i {
		if a, ok := i.(uint32); ok {
			return a
		}
	}
	return 0
}

func GetStringFromContext(ctx context.Context, key string) string {
	if i := ctx.Value(key); nil != i {
		if a, ok := i.(string); ok {
			return a
		}
	}
	return ""
}
