package crowdpkg

//人群包api
import (
	"encoding/json"
	"errors"
	"io/ioutil"
	"net"
	"net/http"
	"strings"
	"time"

	"golang.52tt.com/pkg/log"
)

const (
	ProductID_TTyuyin = "ttvoice"
)

type GetUserListReq struct {
	PageNo           int64  `json:"pageNo"`
	PageSize         int64  `json:"pageSize"`
	ProductID        string `json:"productId"`
	UserGroupID      int64  `json:"userGroupId"`
	UserGroupVersion string `json:"userGroupVersion"`
}

type GetUserListResp struct {
	Code    int64           `json:"code"`
	Data    GetUserListData `json:"data"`
	Msg     string          `json:"msg"`
	Success bool            `json:"success"`
}

type GetUserListData struct {
	PageNo           int64    `json:"pageNo"`
	PageSize         int64    `json:"pageSize"`
	ProductID        string   `json:"productId"`
	TotalUserCount   int64    `json:"totalUserCount"`
	UserGroupID      int64    `json:"userGroupId"`
	UserGroupVersion string   `json:"userGroupVersion"`
	UserIDList       []uint32 `json:"userIdList"`
}

type CrowdpkgClient struct {
	httpClient       *http.Client
	Host             string
	ProductID        string
	UserGroupID      int64
	UserGroupVersion string
	LastUpdateTime   string
}

//..................
type GetUserGroupInfoReq struct {
	ProductID   string `json:"productId"`
	UserGroupID int64  `json:"userGroupId"`
}

type GetUserGroupInfoResp struct {
	Code    int64                `json:"code"`
	Data    GetUserGroupInfoData `json:"data"`
	Msg     string               `json:"msg"`
	Success bool                 `json:"success"`
}

type GetUserGroupInfoData struct {
	ProductID        string `json:"productId"`
	Status           int    `json:"status"`
	UserCount        int64  `json:"userCount"`
	UserGroupDesc    string `json:"userGroupDesc"`
	UserGroupID      int64  `json:"userGroupId"`
	UserGroupName    string `json:"userGroupName"`
	LastUpdateTime   string `json:"lastUpdateTime"`
	UserGroupVersion string `json:"userGroupVersion"`
}

//目前用户群可以按小时定时更新，每次更新会生成一个新版本的用户群，在查询uid列表时需要指定要查询哪个版本的用户群，所以在查询第1页uid列表之前，需要先获取用户群版本号
func TempCrowdpkgClient(Host string, UserGroupID int64, ProductID string) (*CrowdpkgClient, int64, bool, error) {
	p := &CrowdpkgClient{}
	p.httpClient = createHTTPClient()
	p.Host = Host
	p.UserGroupID = UserGroupID
	p.ProductID = ProductID
	rsp, err := p.GetUserGroupInfo()
	log.Infof("GetUserGroupInfo,%+v %v", rsp, err)
	if err != nil || !rsp.Success || rsp.Data.Status != 4 || rsp.Data.UserCount == 0 {
		log.Debugf("GetUserGroupInfo not ok,%+v %v", rsp, err)
		return p, 0, false, err
	}
	t, err := time.ParseInLocation("2006-01-02 15:04:05", rsp.Data.LastUpdateTime, time.Local)
	if err != nil {
		return p, 0, false, err
	}
	if t.YearDay() != time.Now().YearDay() {
		log.Infof("not finish get CrowdPkg")
		return p, 0, false, nil
	}

	p.UserGroupVersion = rsp.Data.UserGroupVersion
	p.LastUpdateTime = rsp.Data.LastUpdateTime
	return p, rsp.Data.UserCount, true, nil
}

//NewCrowdPkgClient 离线人群包不用判断最后更新时间
func NewCrowdPkgClient(Host string, UserGroupID int64, ProductID string) (*CrowdpkgClient, int64, error) {
	p := &CrowdpkgClient{}
	p.httpClient = createHTTPClient()
	p.Host = Host
	p.UserGroupID = UserGroupID
	p.ProductID = ProductID
	rsp, err := p.GetUserGroupInfo()
	log.Infof("GetUserGroupInfo,%+v %v", rsp, err)
	if err != nil || !rsp.Success || rsp.Data.Status != 4 || rsp.Data.UserCount == 0 {
		log.Debugf("GetUserGroupInfo not ok,%+v %v", rsp, err)
		return p, 0, err
	}
	p.UserGroupVersion = rsp.Data.UserGroupVersion
	p.LastUpdateTime = rsp.Data.LastUpdateTime
	return p, rsp.Data.UserCount, err
}

func (s *CrowdpkgClient) GetCrowdpkgUidsByPage(pageNo, pageSize int64) (uids []uint32, total int64, err error) {
	if pageNo == 0 {
		pageNo = 1
	}
	if pageSize == 0 {
		pageSize = 1000
	}
	rsp, err := s.GetCrowdpkgItemsByPage(pageNo, pageSize)
	if err != nil || !rsp.Success || rsp.Code != 200 {
		if err != nil {
			return []uint32{}, 0, err
		} else {
			return []uint32{}, 0, errors.New(rsp.Msg)
		}
	}
	return rsp.Data.UserIDList, rsp.Data.TotalUserCount, nil
}

func (s *CrowdpkgClient) GetUserGroupInfo() (resp *GetUserGroupInfoResp, err error) {
	commonReq := GetUserGroupInfoReq{
		ProductID:   s.ProductID,
		UserGroupID: int64(s.UserGroupID),
	}
	resp = &GetUserGroupInfoResp{}
	jsonBody, err := json.Marshal(commonReq)
	if err != nil {
		log.Errorf("Failed to json.Marshal %+v %+v", commonReq, err)
		return resp, err
	}
	req, err := http.NewRequest("POST", s.Host+"/iop/openApi/userGroup/getUserGroupInfo", strings.NewReader(string(jsonBody)))
	if err != nil {
		log.Errorf("Error Occured. %+v", err)
		return resp, err
	}
	req.Header.Set("Content-Type", "application/json;charset=utf-8")

	// use httpClient to send request
	response, err := s.httpClient.Do(req)
	if err != nil && response == nil {
		log.Errorf("Error sending request to API endpoint. %+v", err)
		return resp, err
	} else {
		// Close the connection to reuse it
		defer response.Body.Close()
		// Let's check if the work actually is done
		// We have seen inconsistencies even when we get 200 OK response
		body, err := ioutil.ReadAll(response.Body)
		if err != nil {
			log.Errorf("Couldn't parse response body. %+v", err)
			return resp, err
		}
		log.Debugf("Response Body:%s", string(body))
		err = json.Unmarshal(body, resp)
		if err != nil {
			log.Errorf("Couldn't marshal response body. %+v", err)
			return resp, err
		}
		return resp, err
	}
}

func (s *CrowdpkgClient) GetCrowdpkgItemsByPage(pageNo, pageSize int64) (resp *GetUserListResp, err error) {
	commonReq := GetUserListReq{
		PageNo:           pageNo,
		PageSize:         pageSize,
		ProductID:        s.ProductID,
		UserGroupID:      int64(s.UserGroupID),
		UserGroupVersion: s.UserGroupVersion,
	}
	resp = &GetUserListResp{}
	jsonBody, err := json.Marshal(commonReq)
	if err != nil {
		log.Errorf("Failed to json.Marshal %+v %+v", commonReq, err)
		return resp, err
	}
	req, err := http.NewRequest("POST", s.Host+"/iop/openApi/userGroup/getUserList", strings.NewReader(string(jsonBody)))
	if err != nil {
		log.Errorf("Error Occured. %+v", err)
		return resp, err
	}
	req.Header.Set("Content-Type", "application/json;charset=utf-8")

	// use httpClient to send request
	response, err := s.httpClient.Do(req)
	if err != nil && response == nil {
		log.Errorf("Error sending request to API endpoint. %+v", err)
		return resp, err
	} else {
		// Close the connection to reuse it
		defer response.Body.Close()
		// Let's check if the work actually is done
		// We have seen inconsistencies even when we get 200 OK response
		body, err := ioutil.ReadAll(response.Body)
		if err != nil {
			log.Errorf("Couldn't parse response body. %+v", err)
			return resp, err
		}
		log.Debugf("Response Body:%s", string(body))
		err = json.Unmarshal(body, resp)
		if err != nil {
			log.Errorf("Couldn't marshal response body. %+v", err)
			return resp, err
		}
		return resp, err
	}
}

func createHTTPClient() *http.Client {
	var maxIdleConns = 100
	var maxIdleConnsPerHost = 100
	var maxConnsPerHost = 100
	var idleConnTimeout = 90
	client := &http.Client{
		Transport: &http.Transport{
			Proxy: http.ProxyFromEnvironment,
			DialContext: (&net.Dialer{
				Timeout:   30 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			MaxIdleConns:        maxIdleConns,
			MaxIdleConnsPerHost: maxIdleConnsPerHost,
			MaxConnsPerHost:     maxConnsPerHost,
			IdleConnTimeout:     time.Duration(idleConnTimeout) * time.Second,
		},
		Timeout: 20 * time.Second,
	}
	return client
}
