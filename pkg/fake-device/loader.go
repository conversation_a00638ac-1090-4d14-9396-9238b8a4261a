package fake_device

import (
	"context"
	"encoding/json"
	"os/user"
	"path"
	"regexp"
	"runtime"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/runtime/v2/filters/pkg"
)

const (
	defaultConfigPath = "/data/oss/conf-center/tt/fake-device.json"
)

var (
	loader *fakeDeviceLoader
)

type Config struct {
	BlackDeviceList []string `json:"black_device_list"`

	regexpList []*regexp.Regexp
}

func (s *Config) UnmarshalBinary(data []byte) error {
	err := json.Unmarshal(data, s)
	if err != nil {
		log.Errorf("json.Unmarshal failed, err:%v", err)
		return err
	}

	for _, black := range s.BlackDeviceList {
		re := regexp.MustCompile(black)
		s.regexpList = append(s.regexpList, re)
	}

	return nil
}

type fakeDeviceLoader struct {
	configLoader *pkg.ConfigLoader
}

func newLoader(filename string, interval time.Duration) (*fakeDeviceLoader, error) {
	if filename == "" {
		filename = defaultConfigPath
	}

	configLoader, err := pkg.NewConfigLoaderV2(context.Background(), filename, &Config{}, true, interval)
	if nil != err {
		log.Errorf("pkg.NewConfigLoaderV2 failed, conf:%s, err:%v", filename, err)
		return nil, err
	}

	return &fakeDeviceLoader{
		configLoader: configLoader,
	}, nil
}

func (s *fakeDeviceLoader) get() *Config {
	if s.configLoader == nil {
		return nil
	}

	if v := s.configLoader.Get(); v != nil {
		cfg, _ := v.(*Config)
		return cfg
	}

	log.Warnf("config not found")
	return nil
}

func init() {
	var base string
	if runtime.GOOS == "windows" {
		if u, err := user.Current(); err == nil {
			base = u.HomeDir
		}
	}

	filename := path.Join(base, defaultConfigPath)

	loader, _ = newLoader(filename, time.Second*60)
	if loader == nil {
		loader = &fakeDeviceLoader{}
	}
}
