package metrics

import (
	"runtime"
	"strconv"
	"strings"
	"time"
)

type trackType byte

const (
	track_func trackType = iota + 1
	track_grpc
	track_mysql
	track_redis
	track_kafka
	track_other

	kPath   = "/"
	kRegion = 0
	kArea   = 0
	kGrpc   = "grpc_"
)

var trackType_name = map[int32]string{
	int32(track_func):  "func",
	int32(track_grpc):  "grpc",
	int32(track_mysql): "mysql",
	int32(track_redis): "redis",
	int32(track_kafka): "kafka",
	int32(track_other): "other",
}

func EnumName(m map[int32]string, k int32) string {
	if s, ok := m[k]; ok {
		return s
	}
	return strconv.Itoa(int(k))
}

func (x trackType) String() string {
	return EnumName(trackType_name, int32(x))
}

func metricsTrack(typ trackType, errCode int32, addr, api string, cost time.Duration) {

	LbStatApiReport(trackType_name[int32(typ)], api, errCode, cost)
}

func getPkgAndFuncName() string {
	pc, _, _, _ := runtime.Caller(2)
	bt := runtime.FuncForPC(pc).Name()
	token := strings.Split(bt, kPath)

	return token[len(token)-1]
}

func getPackageName() string {
	pc, _, _, _ := runtime.Caller(2)
	bt := runtime.FuncForPC(pc).Name()
	token := strings.Split(bt, kPath)
	array := strings.Split(token[len(token)-1], kToken)

	return array[len(array)-1]
}
