package bylink

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"time"

	"github.com/spf13/viper"
	sdk "gitlab.ttyuyin.com/bylink_data/bylink-sdk-go"
	"gitlab.ttyuyin.com/bylink_data/bylink-sdk-go/consumer"
	kafkaBylink "gitlab.ttyuyin.com/bylink_data/bylink-sdk-go/consumer/kafka"
	"gitlab.ttyuyin.com/golang/structs"
	"golang.52tt.com/pkg/bylink/mock"
	"golang.52tt.com/pkg/log"
)

const (
	logTrackLongTime = time.Millisecond * 200
)

// Bylink Analytics
type Bylink = sdk.BylinkAnalytics

// New a BylinkAnalytics instance
func NewKfkCollector() (Bylink, error) {
	var (
		appName  string
		timeFree bool
		err      error
	)

	conf, err := loadConfig()
	if err != nil {
		return nil, err
	}

	var c consumer.Consumer
	appName, timeFree = conf.AppName, conf.TimeFree
	switch conf.Consumer.Enable {
	case _kafka:
		log.Infof("init kafka consumer: %v", conf.Consumer.Kafka)
		c, err = kafkaBylink.New(conf.Consumer.Kafka.Brokers, conf.Consumer.Kafka.Topic)
	case _mock:
		return &mock.Mock{}, nil
	// TODO: support other consumers
	default:
		err = fmt.Errorf("unavailable consumer type: %s", conf.Consumer.Enable)
	}
	if err != nil {
		return nil, err
	}

	return sdk.New(c, sdk.AppName(appName), sdk.TimeFree(timeFree), sdk.AddCallerSkip(1)), err
}

func loadConfig() (*Config, error) {
	v := viper.New()
	v.SetConfigName("bylink")
	v.AddConfigPath(".")
	v.AddConfigPath("/data/oss/conf-center/tt")

	if err := v.ReadInConfig(); err != nil {
		return nil, err
	}

	var conf Config
	if err := v.Unmarshal(&conf); err != nil {
		return nil, err
	}
	return &conf, nil
}

var (
	_globalCollector Bylink
)

// InitGlobalCollector sets a custom bylink collector instead of the default one
func InitGlobalCollector(b Bylink) {
	_globalCollector = b
}

func toMap(s interface{}) map[string]interface{} {
	if m, ok := s.(map[string]interface{}); ok {
		return m
	}

	st := structs.New(s)
	st.TagName = "json"
	return st.Map()
}

// Track an event
// 由于历史原因，这里面会给eventName加上前缀“svr_”,使用的时候要注意
func Track(ctx context.Context, userIdentifier uint64, eventName string, properties interface{}, isLoginIdOpt ...bool) error {
	err := track(ctx, userIdentifier, eventName, toMap(properties), isLoginIdOpt...)
	if err != nil {
		log.Errorf("bylink Track uid:%d with error:%s", userIdentifier, err)
	}
	return err
}

// 由于历史原因，这里面会给eventName加上前缀“svr_”,使用的时候要注意
func TrackMap(ctx context.Context, userIdentifier uint64, eventName string, properties map[string]interface{}, isLoginIdOpt ...bool) error {
	err := track(ctx, userIdentifier, eventName, properties, isLoginIdOpt...)
	if err != nil {
		log.Errorf("bylink Track uid:%d with error:%s", userIdentifier, err)
	}
	return err
}

func track(ctx context.Context, userIdentifier uint64, event string, properties map[string]interface{}, isLoginIdOpt ...bool) error {
	var (
		isLoginId  = true
		distinctId = strconv.FormatUint(userIdentifier, 10)
	)
	if len(isLoginIdOpt) > 0 {
		isLoginId = isLoginIdOpt[0]
	}
	var err error
	defer func() {
		logf := log.Debugf
		if err != nil {
			logf = log.Warnf
		}
		logf("track: %s %s %v %t", distinctId, event, properties, isLoginId)
	}()

	err = normalizeProperties(properties)
	if err != nil {
		return err
	}

	appType, platform := ConvertAppNameAndPlatformByCtx(ctx)
	if _, ok := properties["app_type"]; !ok {
		properties["app_type"] = appType
	}

	if _, ok := properties["$platform"]; !ok {
		properties["$platform"] = platform
	}

	// 以下预置属性不是强制上报属性，如想使用上述属性，必须上报；如果上报为false，百灵不做正确性计算，如果上报true,百灵会计算正确性，如果不为真，会修改为false落地；
	// 当前由于服务端没有记录状态，所以无差别上报true，由百灵进行修正
	properties["$is_first_day"] = true
	properties["$is_first_time"] = true
	properties["$is_first_day_event"] = true

	event = _eventPrefix + event

	if _globalCollector != nil {
		err = _globalCollector.Track(distinctId, event, properties, isLoginId)
	} else {
		return errors.New("track failed, bylink global collector is nil")
	}

	return err
}

func TrackSignUp(ctx context.Context, userIdentifier uint64, originId string) error {
	distinctId := strconv.FormatUint(userIdentifier, 10)

	if _globalCollector != nil {
		return _globalCollector.TrackSignup(distinctId, originId)
	}
	return nil
}

// ProfileSet updates the profile of user
func ProfileSet(userIdentifier uint64, profile interface{}, isLoginIdOpt ...bool) error {
	return profileSet(userIdentifier, toMap(profile), isLoginIdOpt...)
}

func profileSet(userIdentifier uint64, properties map[string]interface{}, isLoginIdOpt ...bool) error {
	var (
		err        error
		isLoginId  = true
		distinctId = strconv.FormatUint(userIdentifier, 10)
	)
	if len(isLoginIdOpt) > 0 {
		isLoginId = isLoginIdOpt[0]
	}

	defer func() {
		logf := log.Debugf
		if err != nil {
			logf = log.Warnf
		}
		logf("profileSet %s %v %t: %v", distinctId, properties, isLoginId, err)
	}()

	err = normalizeProperties(properties)
	if err != nil {
		return err
	}

	if _globalCollector != nil {
		err = _globalCollector.ProfileSet(distinctId, properties, isLoginId)
	}
	return err
}

// Flush all queued data
func Flush() {
	if _globalCollector != nil {
		_globalCollector.Flush()
	}
}

func normalizeProperties(m map[string]interface{}) error {
	for k, v := range m {
		nv, err := normalizedValue(v)
		if err != nil {
			return err
		}
		m[k] = nv
	}
	return nil
}

func normalizedValue(v interface{}) (interface{}, error) {
	rv := reflect.ValueOf(v)
	switch rv.Kind() {
	case reflect.Bool, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Uint8, reflect.Uint16, reflect.Uint32:
		return v, nil
	case reflect.Int:
		if rv.Int() > _maxIntegerValue {
			return nil, errOverflowInteger
		}
		return v, nil
	case reflect.Uint:
		if rv.Uint() > _maxIntegerValue {
			return nil, errOverflowInteger
		}
		return v, nil
	case reflect.Int64:
		// 当前对64位支持不好，直接转为字符串
		return strconv.FormatInt(rv.Int(), 10), nil
	case reflect.Uint64:
		// 当前对64位支持不好，直接转为字符串
		return strconv.FormatUint(rv.Uint(), 10), nil
	case reflect.Float32, reflect.Float64:
		return v, nil
	case reflect.String:
		return v, nil
	case reflect.Slice: //value in properties list MUST be string
		switch v.(type) {
		case []string:
			return v, nil
		}
	case reflect.Struct: //only support time.Time
		switch v.(type) {
		case time.Time:
			return v.(time.Time).Format(_timeFormat), nil
		}
	case reflect.Ptr: //only support *time.Time
		switch v.(type) {
		case *time.Time:
			return v.(*time.Time).Format(_timeFormat), nil
		}
	}
	return fmt.Sprintf("%v", v), nil
}

func Close() {
	if _globalCollector != nil {
		_globalCollector.Close()
	}
}

const (
	_timeFormat      = "2006-01-02 15:04:05.999"
	_maxIntegerValue = 1e16 - 1
	_eventPrefix     = "svr_"
)

var (
	errOverflowInteger = errors.New("maxinum of int/uint is 1e16-1, please use int64/uint64 instead")
)
