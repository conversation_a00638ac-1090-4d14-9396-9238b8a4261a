package bylink

/*
import (
    sdk "gitlab.ttyuyin.com/bylink_data/bylink-sdk-go"
    "gitlab.ttyuyin.com/bylink_data/bylink-sdk-go/consumer"
    "gitlab.ttyuyin.com/bylink_data/bylink-sdk-go/model"
    "os"
    "path"
    "strings"
)

func zapDefaultConsumer() consumer.Consumer {
    logDir := os.Getenv("BYLINK_FALLBACK_LOG_DIR")
    logFile := os.Getenv("BYLINK_FALLBACK_LOG_NAME")

    if logDir == "" {
        if u, err := user.Current(); err == nil {
            logDir = path.Join(u.HomeDir, "bylink")
        }
    }
    if logDir == "" {
        logDir = "./bylink"
    }

    if logFile == "" {
        logFile = "tt"
    }

    if !strings.HasSuffix(logFile, ".log") {
        logFile = logFile + ".log"
    }

    c, _ := sdk.NewLoggingZapConsumer(model.SDKConfig{
        DataLogDir:        logDir,
        DataLogMaxSize:    4 * 1024,
        DataLogMaxBackups: 10,
        DataLogMaxAge:     14,
    }, logFile)
    return c
}

var (
    _zapDefaultConsumer        = zapDefaultConsumer()
    _zapDefaultCollector       = sdk.New(_zapDefaultConsumer, sdk.AddCallerSkip(1))

)

 */