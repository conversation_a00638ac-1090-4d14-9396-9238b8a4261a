package bylink

type ConsumerType string

const (
	_kafka  ConsumerType = "kafka"
	_log    ConsumerType = "log"
	_zapLog ConsumerType = "zaplog"
	_mock   ConsumerType = "mock"
)

// Config of BylinkAnalytics
//
// YAML example: ```
//   appName: ttchat
//   timeFree: true
//   consumer:
//     enable: kafka
//     kafka:
//  	   brokers:
//  		 - 10.200.1.129:19092
//  		 - 10.200.1.130:19092
//  		 - 10.200.1.121:19092
//  	   topic: ttchat-bylink
// ```
type Config struct {
	AppName  string
	TimeFree bool
	Consumer ConsumerConfig
}

type ConsumerConfig struct {
	Enable ConsumerType

	Kafka  *KafkaConsumerConfig
	Log    *LogConsumerConfig
	ZapLog *ZapLogConsumerConfig
}

type KafkaConsumerConfig struct {
	Brokers []string
	Topic   string
}

type LogConsumerConfig struct {
	// TODO
}

type ZapLogConsumerConfig struct {
	// TODO
}
