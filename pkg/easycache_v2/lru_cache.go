package easycache

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"reflect"
	"sync"
	"time"

	"gitlab.ttyuyin.com/golang/gudetama/log"
)

var _ CacheInterface = new(LRUCache)

type LRUCacheNode struct {
	Key   string
	Value interface{}
	Next  *LRUCacheNode
	Prev  *LRUCacheNode
	//允许进行更新
	expireSec int64
}

type (
	LRUCache struct {
		mtx           *sync.Mutex
		dummyTail     *LRUCacheNode
		dummyHead     *LRUCacheNode
		size          int64
		lockedCalls   LockedCallsInterface
		lruMap        map[string]*LRUCacheNode
		baseExpireSec int64
	}
)

type LRUCacheOpt func(*LRUCache)

func WithLRUCacheSize(size int64) LRUCacheOpt {
	return func(o *LRUCache) {
		o.size = size
	}
}

func WithLRUCachebaseExpireMillSec(millsec int64) LRUCacheOpt {
	return func(o *LRUCache) {
		o.baseExpireSec = millsec / 1000
	}
}

func NewLRUCache(opts ...LRUCacheOpt) *LRUCache {

	size := int64(10000)
	baseExpireSec := int64(1) //1s过期
	head := &LRUCacheNode{}
	tail := &LRUCacheNode{}
	p := &LRUCache{
		size:          size,
		lruMap:        map[string]*LRUCacheNode{},
		mtx:           &sync.Mutex{},
		baseExpireSec: baseExpireSec,
		lockedCalls:   NewLockedCalls(),
	}
	for _, opt := range opts {
		opt(p)
	}
	p.dummyHead = head
	p.dummyTail = tail

	p.dummyHead.Next = p.dummyTail
	p.dummyTail.Prev = p.dummyHead
	go p.maintain()
	return p
}

func (p *LRUCache) doGet(key string, out interface{}) (error, bool) {
	rv := reflect.ValueOf(out)
	if rv.Kind() != reflect.Ptr || rv.IsNil() {
		return fmt.Errorf("接收者为空或者非指针类型"), false
	}
	p.mtx.Lock()
	defer p.mtx.Unlock()
	if v, ok := p.lruMap[key]; ok {
		if v.expireSec > time.Now().Unix() { //过期时间为秒
			return nil, trySetGoal(v.Value, out)
		} else {
			delete(p.lruMap, key)
			return nil, false
		}
	} else {
		return nil, false
	}
}

func (p *LRUCache) doSet(key string, value interface{}) bool {
	var head *LRUCacheNode
	expireTime := time.Now().Unix() + p.baseExpireSec + int64(rand.Intn(10))

	//移除本身的
	p.mtx.Lock()
	if v, ok := p.lruMap[key]; ok {
		v.Prev.Next = v.Next
		v.Next.Prev = v.Prev
		head = v
		head.Value = value
	} else {
		head = &LRUCacheNode{Value: value, Key: key}
	}
	//设置过期时间
	head.expireSec = expireTime
	//获得头之后 进行头插入
	tmp := p.dummyHead.Next
	p.dummyHead.Next = head
	head.Next = tmp
	tmp.Prev = head
	head.Prev = p.dummyHead
	p.lruMap[key] = head
	//大于大小 进行截断
	if p.size < int64(len(p.lruMap)) {
		node := p.dummyTail.Prev
		p.removeLastNode(node)
	}
	p.mtx.Unlock()
	return true
}

func (p *LRUCache) DebugFmt() {
	fmt.Println("begin...")
	node := p.dummyHead.Next
	for node != p.dummyTail {
		fmt.Println(node)
		node = node.Next
	}
}

func (c *LRUCache) Take(value interface{}, key string, fetch func() (interface{}, error)) error {
	fnCall := func() error {
		//加localcache
		err, ok := c.doGet(key, value)
		if err != nil {
			return err
		}
		if ok {
			log.Debugf("Take local cache key%v ok value:%v", key, value)
			return nil
		}
		v, err := fetch()
		if err != nil {
			log.Errorf("ERROR fetch key:%v error:%v", key, err)
			return err
		}
		b, err := json.Marshal(v)
		if err != nil {
			log.Errorf("Marshal value:%v err:%v", v, err)
			return err
		}
		c.doSet(key, b)
		json.Unmarshal(b, value)
		return nil
	}
	//共享内存操作
	return c.lockedCalls.Do(key, fnCall)
}

func (c *LRUCache) maintain() {
	ticker := time.NewTicker(time.Millisecond * 100) //0.1秒回收一次
	l := 1000
	for range ticker.C {
		nowUnix := time.Now().Unix()
		c.mtx.Lock()
		node := c.dummyTail.Prev
		for i := 0; i < l && node != c.dummyHead && node.expireSec < nowUnix; i++ {
			//不断删除最后一个
			c.removeLastNode(node)
			node = c.dummyTail.Prev
			i++
		}
		c.mtx.Unlock()
	}
}

func (c *LRUCache) removeLastNode(node *LRUCacheNode) {
	delete(c.lruMap, node.Key)
	newTail := node.Prev
	c.dummyTail.Prev = newTail
	newTail.Next = c.dummyTail
	//这个为空了 避免内存泄漏
	// avoid memory leaks
	// avoid memory leaks
	node.Next = nil
	node.Prev = nil
}
