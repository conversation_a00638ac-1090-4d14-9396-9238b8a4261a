package tbean

import (
	"context"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"fmt"
	"time"
	"math"
	"net/http"
	"strconv"
	"golang.org/x/net/context/ctxhttp"
	"io/ioutil"
	"errors"
	"encoding/json"
	"golang.52tt.com/pkg/log"
)

const (
	uriFirstRechargeCheck = "/record/api/firstRechargeCheck.shtml"
	successCode200        = 200
)

type FirstRechargeCheckInfo struct {
	FirstRecharge bool `json:"firstRecharge"`
}

type GetUserFirstRechargeCheckResp struct {
	Code    int32                   `json:"code"`
	Success bool                    `json:"success"`
	Data    *FirstRechargeCheckInfo `json:"data"`
	Msg     string                  `json:"msg"`
}

// GetFirstRechargeCheck 获取用户首充状态
// firsMinPrice : 指定价格商品的首充情况
// payChannel : 指定支付渠道
func (c *client) GetFirstRechargeCheck(ctx context.Context, uid, firsMinPrice uint32, payChannel string) (bool, error) {
	if uid == 0 {
		return false, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "uid is 0")
	}

	urlParam := fmt.Sprintf("uid=%d", uid)
	if firsMinPrice > 0 {
		urlParam = urlParam + "&firsMinPrice=" + strconv.Itoa(int(firsMinPrice))
	}

	if payChannel != "" {
		urlParam = urlParam + "&payChannel=" + payChannel
	}

	path := uriFirstRechargeCheck + "?" + urlParam
	var res GetUserFirstRechargeCheckResp
	err := c.callByGet(ctx, path, &res, uid)

	if err != nil {
		return false, err
	}

	if res.Code != successCode200 || !res.Success || res.Data == nil {
		return false, protocol.NewExactServerError(nil, status.ErrTbeanSystemError, res.Msg)
	}

	return res.Data.FirstRecharge, err
}

func (c *client) callByGet(ctx context.Context, path string, responseBody interface{}, uid uint32) (err error) {
	if responseBody == nil {
		return errors.New("The input `responseBody` must not be nil.")
	}

	reqUrl := fmt.Sprintf("%s%s", c.contextPath, path)

	start := time.Now()
	defer func() {
		elapsed := time.Since(start)
		elapsedMillisecond := uint32(math.Ceil(float64(elapsed / time.Millisecond)))
		if elapsedMillisecond < 1 {
			elapsedMillisecond = 1
		}

		log.InfoWithCtx(ctx, "GET %s tooks %dms ", reqUrl, elapsedMillisecond)
	}()

	httpReq, err := http.NewRequest("GET", reqUrl, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "http.NewRequest %s post error %v", reqUrl, err)
		return
	}

	strUid := strconv.Itoa(int(uid))
	httpReq.Header.Set("uid", strUid)
	resp, err := ctxhttp.Do(ctx, c.httpClient, httpReq)
	//resp, err := ctxhttp.Post(ctx, c.httpClient, url, "application/json", strings.NewReader(req))
	if err != nil {
		log.ErrorWithCtx(ctx, "POST %s post error %v", reqUrl, err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("GET %s status %s", reqUrl, resp.Status)
		log.ErrorWithCtx(ctx, "Response code not OK: %v", err)
		return
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "POST %s read body failed: %+v", reqUrl, err)
		return
	}
	//err = json.NewDecoder(resp.Body).Decode(response)
	err = json.Unmarshal(body, responseBody)
	if err != nil {
		log.ErrorWithCtx(ctx, "POST %s unmarshal body failed: %v", reqUrl, err)
		log.ErrorWithCtx(ctx, "body: %s", string(body))
		return
	}
	log.InfoWithCtx(ctx, "POST uil:%s OK response:%+v body:%v", reqUrl, responseBody, string(body))

	return
}
