package tbean

import (
	"fmt"
	"testing"
	"time"

	"gitlab.ttyuyin.com/golang/gudetama/log"
	"golang.org/x/net/context"
)

var cli = NewClient(sandboxContextPath)
var testUid = uint32(2190173)
var outTradeNo = fmt.Sprintf("TestTradePrepare_%d", time.Now().Unix())
var tradeNo string

func init() {
	log.Init("api_test", log.DebugLevel, "")
}

func TestClient_GetBalance(t *testing.T) {
	balance, err := cli.GetBalance(context.Background(), AppID_TT_HZ, AccountTypeCustomer, testUid)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetBalance %v", err)
	} else {
		t.Logf("GetBalance %d %d", testUid, balance)
	}
}

func TestClient_TradePrepare(t *testing.T) {
	req := &TradeRequest{
		AppId:         AppID_TT_HZ,
		OutTradeNo:    outTradeNo,
		BuyerId:       testUid,
		BuyerName:     "ezreal",
		CommodityId:   "fengbao",
		CommodityName: "God Of War",
		Num:           1,
		UnitPrice:     1,
		Price:         1,
		Note:          "WWW",
		Platform:      PlatformAndroid,
	}

	r, err := cli.Prepare(context.Background(), req)

	if err != nil {
		t.Errorf("Prepare failed: %v", err)
	} else {
		tradeNo = r.TradeNo
		t.Logf("Prepare %v", r)
	}
}

func TestClient_TradePrepare_Illegal_Argument(t *testing.T) {
	_, err := cli.Prepare(context.Background(), &TradeRequest{
		OutTradeNo:    outTradeNo,
		BuyerName:     "ezreal",
		CommodityId:   "fengbao",
		CommodityName: "God Of War",
		Num:           1,
		UnitPrice:     1,
		Price:         1,
		Note:          "WWW",
		Platform:      PlatformAndroid,
	})

	if err != ErrIllegalArgument {
		t.Errorf("TradePrepare_Duplicate_Order expects a ErrIllegalArgument, got '%+v'", err)
	}
}

func TestClient_TradePrepare_Duplicate_Order(t *testing.T) {
	_, err := cli.Prepare(context.Background(), &TradeRequest{
		AppId:         AppID_TT_HZ,
		OutTradeNo:    outTradeNo,
		BuyerId:       testUid,
		BuyerName:     "ezreal",
		CommodityId:   "fengbao",
		CommodityName: "God Of War",
		Num:           1,
		UnitPrice:     1,
		Price:         1,
		Note:          "WWW",
		Platform:      PlatformAndroid,
	})

	if err != ErrDuplicateOrder {
		t.Errorf("TradePrepare_Duplicate_Order expects a ErrDuplicateOrder, got '%+v'", err)
	}
}

func TestClient_TradeNotify_Commit(t *testing.T) {
	t.Logf("TestClient_TradeNotify_Commit begin: %s %s", outTradeNo, tradeNo)
	s, m, err := notify(context.Background(), AppID_TT_HZ, outTradeNo, tradeNo, PlatformAndroid, Commit)
	if err != nil {
		t.Errorf("TestClient_TradeNotify_Commit failed %v", err)
	} else {
		t.Log("TestClient_TradeNotify_Commit:", outTradeNo, tradeNo, s, m)
	}

	_, _, err = notify(context.Background(), AppID_TT_HZ, outTradeNo, tradeNo, PlatformAndroid, Commit)
	if err != ErrIllegalOrderStatus {
		t.Error("TestClient_TradeNotify_Commit expect ErrIllegalOrderStatus, got", err)
	}
}

func TestClient_TradePrepareAndRollback(t *testing.T) {
	r, err := cli.Prepare(context.Background(), &TradeRequest{
		AppId:         AppID_TT_HZ,
		OutTradeNo:    fmt.Sprintf("TestTradePrepareAndRollback_%d", time.Now().Unix()),
		BuyerId:       testUid,
		BuyerName:     "ezreal",
		CommodityId:   "fengbao",
		CommodityName: "God Of War",
		Num:           2,
		UnitPrice:     1,
		Price:         2,
		Note:          "WWW",
		Platform:      PlatformAndroid,
	})

	if err != nil {
		t.Fatalf("TestClient_TradePrepareAndRollback %v", err)
	}

	s, m, err := notify(context.Background(), AppID_TT_HZ, r.OutTradeNo, r.TradeNo, "android", Rollback)
	if err != nil {
		t.Error("TestClient_TradePrepareAndRollback failed:", err)
	} else {
		t.Log("TestClient_TradePrepareAndRollback:", outTradeNo, tradeNo, s, m)
	}

	// rollback again
	s, m, err = notify(context.Background(), AppID_TT_HZ, r.OutTradeNo, r.TradeNo, "android", Rollback)
	if err != ErrIllegalOrderStatus {
		t.Error("TestClient_TradePrepareAndRollback expect ErrIllegalOrderStatus, got", err)
	}
}

func TestClient_TradeConsume(t *testing.T) {
	req := &TradeRequest{
		AppId:         AppID_TT_HZ,
		OutTradeNo:    fmt.Sprintf("TestTradeConsume_%d", time.Now().Unix()),
		BuyerId:       testUid,
		BuyerName:     "",
		CommodityId:   "",
		CommodityName: "",
		Num:           1,
		UnitPrice:     1,
		Price:         1,
		Note:          "",
		Platform:      PlatformIOS,
	}

	r, err := cli.Consume(context.Background(), req)

	if err != nil {
		t.Errorf("TestClient_TradeConsume %v", err)
	}
	t.Logf("TestClient_TradeConsume %v", r)

	r, err = cli.Consume(context.Background(), req)
	if err != ErrDuplicateOrder {
		t.Errorf("TestClient_TradeConsume#2 expected ErrDuplicateOrder, got %v", err)
	}
}

func notify(ctx context.Context, appID, outTradeNo, tradeNo, platform string, command TradeNotifyRequest_Command) (status string, modifyTime string, err error) {
	return cli.Notify(ctx, &TradeNotifyRequest{
		AppId:      appID,
		OutTradeNo: outTradeNo,
		TradeNo:    tradeNo,
		Command:    command,
		Platform:   platform,
	}, 0)
}

func TestClient_CheckBlackUser(t *testing.T) {
	isBlack, err := cli.CheckBlackUser(context.Background(), "2227996", BLACK_TYPE_RECHARGE)
	if err != nil {
		t.Errorf("CheckBlackUser %v", err)
	} else {
		t.Logf("CheckBlackUser %v", isBlack)
	}
}
