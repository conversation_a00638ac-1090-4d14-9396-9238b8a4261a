package tbean

import (
	"gitlab.ttyuyin.com/golang/gudetama/log"
	"golang.org/x/net/context"
	"strconv"
)

const (
	uriTransferI2C        = "/rest/api/transfer-i2c.shtml"
	uriTransferI2CBalance = "/serverApi/getTransferI2CBalance.shtml"
	uriTransferI2CRefund  = "/rest/api/transfer-i2c/refund.shtml"
	uriTransferI2CV2      = "/credit/transfer/i2c.shtml"
)

func (c *client) TransferI2C(ctx context.Context, request *TransferI2CRequest) ([]*TransferI2CResponseBody, error) {
	var body []*TransferI2CResponseBody
	if len(request.To) == 0 {
		return body, ErrParamError
	}
	err := c.call(ctx, uriTransferI2C, request, &body, request.To[0].GetUid())
	if err != nil {
		log.Error(ctx, "TBean client TransferI2C %v err %v", request, err)
	}
	return body, err
}

func (c *client) TransferI2CGetBalance(ctx context.Context, request *TransferI2CBalanceReq) (*TransferI2CBalanceResp, error) {
	var body *TransferI2CBalanceResp
	err := c.call(ctx, uriTransferI2CBalance, request, body, 0)
	if err != nil {
		log.Error(ctx, "TBean client TransferI2CGetBalance %v err %v", request, err)
	}
	return body, err
}

func (c *client) TransferI2CRefund(ctx context.Context, request *TransferI2CRefundRequest) ([]*TransferI2CRefundResponseBody, error) {
	var body []*TransferI2CRefundResponseBody
	if len(request.To) == 0 {
		return body, ErrParamError
	}
	err := c.call(ctx, uriTransferI2CRefund, request, &body, request.To[0].GetUid())
	if err != nil {
		log.Error(ctx, "TBean client TransferI2CRefund %v err %v", request, err)
	}
	return body, err
}

func (c *client) TransferI2CV2(ctx context.Context, request *TransferI2CV2Request) ([]*TransferI2CResponseBody, error) {
	var body []*TransferI2CResponseBody
	uid, _ := strconv.Atoi(request.Data.ToId)
	err := c.call(ctx, uriTransferI2CV2, request, &body, uint32(uid))
	if err != nil {
		log.Error(ctx, "TBean client TransferI2C %v err %v", request, err)
	}
	return body, err
}