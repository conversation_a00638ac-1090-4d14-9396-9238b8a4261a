package tbean

import (
	"context"
	"gitlab.ttyuyin.com/golang/gudetama/log"
	"testing"
	"time"
)




func init() {
	log.Init("TestClient_TransferI2C", log.DebugLevel, log.UseDefaultLogShmConfigPath)
}

func TestClient_PresetFreeze(t *testing.T) {
	resp, err := cli.PresetFreeze(context.Background(), &PresetFreezeReqData{
		AppId:         "TT_TEST",
		BuyerId:       10000,
		FreezeBalance: 100,
		OutTradeNo:    time.Now().Format("2006-01-02 15:04:05"),
		FreezeTitle:   "test"},
	)
	if err != nil {
		t.Fatalf("TestClient_PresetFreeze, %v", err)
	}

	t.Logf("TestClient_PresetFreeze resp:%+v", resp)

	cli.UnfreezeAndConsume(context.Background(), &UnfreezeAndConsumeReqData{
		AppId:         "TT_TEST",
		BuyerId:       10000,
		BuyerName:      "tt",
		CommodityId:    "",
		CommodityName:  "test",
		Num: 1,
		Price: 10,
		UnitPrice: 10,
		Platform: "1",
		Notes: "test",
		OutTradeNo:    time.Now().Format("2006-01-02 15:04:05"),
	},
	)
}


func TestClient_UnFreezeAndRefund(t *testing.T) {
	resp, err := cli.UnFreezeAndRefund(context.Background(), &UnFreezeAndRefundReqData{
		AppId:      "TT_TEST",
		BuyerId:    2202086,
		OutTradeNo: "fellow_2202086_2193983_1096_1636533605",
	},
	)
	if err != nil {
		t.Fatalf("TestClient_UnFreezeAndRefund, %v", err)
	}
	t.Logf("TestClient_UnFreezeAndRefund resp:%+v err:%v", resp, err)

}