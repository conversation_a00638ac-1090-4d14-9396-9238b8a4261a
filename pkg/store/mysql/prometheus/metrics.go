package prometheus

import (
	"os"
	"path"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"golang.52tt.com/pkg/store/mysql/custom"
)

var (
	AppName string
)

var (
	RequestsTotalCounter = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "mysql_sdk_request_total",
		Help: "mysql_sdk_request counter metrics",
	}, []string{"source_workload", "destination_workload", "mysql_addr", "mysql_database", "query", "ret"})
	RequestsDuration = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "mysql_sdk_request_duration",
		Help:    "mysql_sdk_request duration",
		Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.25, 0.5, 1, 5, 10},
	}, []string{"source_workload", "destination_workload", "mysql_addr", "mysql_database", "query"})
	RequestsTransTotalCounter = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "mysql_sdk_transation_request_total",
		Help: "mysql_sdk_transation_request counter metrics",
	}, []string{"source_workload", "destination_workload", "mysql_addr", "mysql_database", "query", "ret"})
	RequestsTransDuration = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "mysql_sdk_transation_request_duration",
		Help:    "mysql_sdk_transation_request duration",
		Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.25, 0.5, 1, 5, 10},
	}, []string{"source_workload", "destination_workload", "mysql_addr", "mysql_database"})
)

func init() {
	AppName = os.Getenv("MY_APP_NAME")
	if AppName == "" {
		_, AppName = path.Split(os.Args[0])
	}

	prometheus.MustRegister(RequestsTotalCounter, RequestsDuration)
}

type MysqlLog struct {
	SQL          string
	QueryType    string
	Args         []interface{}
	RetMsg       string
	Duration     time.Duration
	TransationID string
}

func ReportReqCounter(msgCtx *custom.MysqlContext) {
	retMsg := ""
	if msgCtx.Err == nil {
		retMsg = ""
	} else {
		retMsg = msgCtx.Err.Error()
	}

	counterLabel := prometheus.Labels{
		"source_workload":      AppName,
		"destination_workload": "mysql",
		"mysql_addr":           msgCtx.Host,
		"mysql_database":       msgCtx.Database,
		"query":                msgCtx.QueryType.String(),
		"ret":                  retMsg,
	}
	RequestsTotalCounter.With(counterLabel).Inc()
}

func ReportReqHistogram(msgCtx *custom.MysqlContext) {
	costLabel := prometheus.Labels{
		"source_workload":      AppName,
		"destination_workload": "mysql",
		"mysql_addr":           msgCtx.Host,
		"mysql_database":       msgCtx.Database,
		"query":                msgCtx.QueryType.String(),
	}
	RequestsDuration.With(costLabel).Observe(msgCtx.Duration.Seconds())
}

func ReportTransReqCounter(msgCtx *custom.MysqlContext) {
	retMsg := ""
	if msgCtx.Err == nil {
		retMsg = ""
	} else {
		retMsg = msgCtx.Err.Error()
	}

	counterLabel := prometheus.Labels{
		"source_workload":      AppName,
		"destination_workload": "mysql",
		"mysql_addr":           msgCtx.Host,
		"mysql_database":       msgCtx.Database,
		// "trans_id":             msgCtx.TransationID,
		"query": msgCtx.QueryType.String(),
		"ret":   retMsg,
	}
	RequestsTransTotalCounter.With(counterLabel).Inc()
}

func ReportTransReqHistogram(msgCtx *custom.MysqlContext) {
	costLabel := prometheus.Labels{
		"source_workload":      AppName,
		"destination_workload": "mysql",
		"mysql_addr":           msgCtx.Host,
		"mysql_database":       msgCtx.Database,
		// "trans_id":             msgCtx.TransationID,
	}
	RequestsTransDuration.With(costLabel).Observe(msgCtx.Duration.Seconds())
}
