package example

import (
	"context"
	"os"
	"testing"
	"time"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/store/mysql"
)

var ctx context.Context
var db *mysql.DB

type Schema struct {
	create string
	drop   string
}

var defaultSchema = Schema{
	create: `CREATE TABLE person (
	first_name text,
	last_name text,
	email text,
	added_at timestamp default now()
);`,
	drop: `drop table person;`,
}

type Person struct {
	FirstName string    `db:"first_name"`
	LastName  string    `db:"last_name"`
	Email     string    `db:"email"`
	AddedAt   time.Time `db:"added_at"`
}

func init() {
	var err error
	cfg := &config.MysqlConfig{
		Host:     "*************",
		Port:     3306,
		UserName: "godman",
		Password: "thegodofman",
		Database: "ugc",
		Charset:  "latin1",
		MysqlSDKConfig: config.MysqlSDKConfig{
			SlowlogTimeMs: 1,
		},
	}

	ctx = context.Background()
	db, err = mysql.NewDBWithCtx(ctx, cfg)
	if err != nil {
		panic(err)
	}

	os.Setenv("MY_APP_NAME", "test")
	log.SetLevel(log.DebugLevel)
}

func TestCDemo(t *testing.T) {
	dropSql := "Drop table person"
	defer func() {
		db.ExecContext(ctx, dropSql)
	}()

	createSql := "CREATE TABLE person (first_name text,last_name text,email text,added_at timestamp default now());"
	_, _ = db.ExecContext(ctx, createSql)
}

func TestDDemo(t *testing.T) {
	dropSql := "Drop table person"
	defer func() {
		db.ExecContext(ctx, dropSql)
	}()

	createSql := "CREATE TABLE person (first_name text,last_name text,email text,added_at timestamp default now());"
	_, _ = db.ExecContext(ctx, createSql)

	insertSql := "INSERT INTO person (first_name, last_name, email) VALUES (?, ?, ?)"
	db.ExecContext(ctx, insertSql, "Jason", "Moiron", "<EMAIL>")
	db.ExecContext(ctx, insertSql, "John", "Doe", "<EMAIL>")
	db.ExecContext(ctx, insertSql, "Test", "DWX", "<EMAIL>")

	deleteSql := "delete FROM person where first_name = ?"
	_, err := db.ExecContext(ctx, deleteSql, "Test")
	if err != nil {
		log.ErrorWithCtx(ctx, "err %v", err)
	}

	selectDelSql := "SELECT * FROM person where first_name = ?"
	TestPer := &Person{}
	err = db.GetContext(ctx, TestPer, selectDelSql, "Test")
	log.Debugf("err %v", TestPer, err)
}

func TestUDemo(t *testing.T) {
	dropSql := "Drop table person"
	defer func() {
		db.ExecContext(ctx, dropSql)
	}()

	createSql := "CREATE TABLE person (first_name text,last_name text,email text,added_at timestamp default now());"
	_, _ = db.ExecContext(ctx, createSql)

	insertSql := "INSERT INTO person (first_name, last_name, email) VALUES (?, ?, ?)"
	db.ExecContext(ctx, insertSql, "Jason", "Moiron", "<EMAIL>")
	db.ExecContext(ctx, insertSql, "John", "Doe", "<EMAIL>")
	db.ExecContext(ctx, insertSql, "Test", "DWX", "<EMAIL>")

	updateSql := "update person set last_name=? where first_name = ?"
	_, _ = db.ExecContext(ctx, updateSql, "Evins", "John")

	JohnPer := &Person{}
	selectSpeSqlV2 := "SELECT * FROM person where first_name = ? limit 1"
	err := db.GetContext(ctx, JohnPer, selectSpeSqlV2, "John")
	log.Debugf("ret %v err %v", JohnPer, err)
}

func TestRDemo(t *testing.T) {
	dropSql := "Drop table person"
	defer func() {
		db.ExecContext(ctx, dropSql)
	}()

	createSql := "CREATE TABLE person (first_name text,last_name text,email text,added_at timestamp default now());"
	_, _ = db.ExecContext(ctx, createSql)
	insertSql := "INSERT INTO person (first_name, last_name, email) VALUES (?, ?, ?)"
	db.ExecContext(ctx, insertSql, "Jason", "Moiron", "<EMAIL>")
	db.ExecContext(ctx, insertSql, "John", "Doe", "<EMAIL>")
	db.ExecContext(ctx, insertSql, "Test", "DWX", "<EMAIL>")

	//select *
	pps := []*Person{}
	selectSql := "SELECT * FROM person"
	_ = db.SelectContext(ctx, &pps, selectSql)
	for _, v := range pps {
		log.Debugf("pps v %+v", v)
	}

	//select by condition

	//case1:
	querySql := "SELECT * FROM person where first_name = ?"
	JohnPer := &Person{}
	rows, err := db.QueryxContext(ctx, querySql, "John")
	if err != nil {
		log.ErrorWithCtx(ctx, "err %v", err)
	}
	defer rows.Close()
	if rows.Next() == false {
		log.DebugfWithCtx(ctx, "rows null")
	} else {
		rows.StructScan(JohnPer)
	}
	log.DebugWithCtx(ctx, "JohnPer %+v", JohnPer)

	//case2
	selectSpeSql := "SELECT * FROM person where first_name = ?"
	JasonPer := &Person{}
	err = db.GetContext(ctx, JasonPer, selectSpeSql, "Jason")
	log.Debugf("ret %+v err %v", JasonPer, err)
}
