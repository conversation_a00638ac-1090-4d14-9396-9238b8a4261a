package types

type intCmd interface {
	Val() int64
	Result() (int64, error)
	String() string
}

type IntCmd struct {
	intCmd
	*baseErrCmd
}

func NewIntCmd(base baseCmd, cmd intCmd) *IntCmd {
	return &IntCmd{
		intCmd:     cmd,
		baseErrCmd: newBaseErrCmd(base),
	}
}

func (c *IntCmd) Result() (int64, error) {
	val, err := c.intCmd.Result()
	return val, NewErr(err)
}

//------------------------------------------------------------------------------

type boolIntCmd struct {
	boolCmd
}

func NewBoolIntCmd(base baseCmd, cmd boolCmd) *IntCmd {
	return NewIntCmd(base, &boolIntCmd{
		boolCmd: cmd,
	})
}

func (c *boolIntCmd) Val() int64 {
	val, _ := c.Result()
	return val
}

func (c *boolIntCmd) Result() (int64, error) {
	val, err := c.boolCmd.Result()
	if val {
		return 1, err
	}
	return 0, err
}
