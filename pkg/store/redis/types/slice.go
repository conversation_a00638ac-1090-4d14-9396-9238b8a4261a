package types

type sliceCmd interface {
	Val() []interface{}
	Result() ([]interface{}, error)
	String() string
}

type SliceCmd struct {
	sliceCmd
	*baseErrCmd
}

func NewSliceCmd(base baseCmd, cmd sliceCmd) *SliceCmd {
	return &SliceCmd{
		sliceCmd:   cmd,
		baseErrCmd: newBaseErrCmd(base),
	}
}

func (c *SliceCmd) Result() ([]interface{}, error) {
	val, err := c.sliceCmd.Result()
	return val, NewErr(err)
}

//------------------------------------------------------------------------------

type stringSliceCmd interface {
	Val() []string
	Result() ([]string, error)
	String() string
}

type StringSliceCmd struct {
	stringSliceCmd
	*baseErrCmd
}

func NewStringSliceCmd(base baseCmd, cmd stringSliceCmd) *StringSliceCmd {
	return &StringSliceCmd{
		stringSliceCmd: cmd,
		baseErrCmd:     newBaseErrCmd(base),
	}
}

func (c *StringSliceCmd) Result() ([]string, error) {
	val, err := c.stringSliceCmd.Result()
	return val, NewErr(err)
}

//------------------------------------------------------------------------------

type Z struct {
	Score  float64
	Member interface{}
}

type zSliceCmd interface {
	Val() []Z
	Result() ([]Z, error)
	String() string
}

type ZSliceCmd struct {
	zSliceCmd
	*baseErrCmd
}

func NewZSliceCmd(base baseCmd, cmd zSliceCmd) *ZSliceCmd {
	return &ZSliceCmd{
		zSliceCmd:  cmd,
		baseErrCmd: newBaseErrCmd(base),
	}
}

func (c *ZSliceCmd) Result() ([]Z, error) {
	val, err := c.zSliceCmd.Result()
	return val, NewErr(err)
}
