package types

type boolCmd interface {
	Val() bool
	Result() (bool, error)
	String() string
}

type BoolCmd struct {
	boolCmd
	*baseErrCmd
}

func NewBoolCmd(base baseCmd, cmd boolCmd) *BoolCmd {
	return &BoolCmd{
		boolCmd:    cmd,
		baseErrCmd: newBaseErrCmd(base),
	}
}

func (c *BoolCmd) Result() (bool, error) {
	val, err := c.boolCmd.Result()
	return val, NewErr(err)
}

//------------------------------------------------------------------------------

type statusBoolCmd struct {
	statusCmd
}

func NewStatusBoolCmd(base baseCmd, cmd statusCmd) *BoolCmd {
	return NewBoolCmd(base, &statusBoolCmd{
		statusCmd: cmd,
	})
}

func (c *statusBoolCmd) Val() bool {
	val, _ := c.Result()
	return val
}

func (c *statusBoolCmd) Result() (bool, error) {
	val, err := c.statusCmd.Result()
	if val == "OK" {
		return true, err
	}
	return false, err
}
