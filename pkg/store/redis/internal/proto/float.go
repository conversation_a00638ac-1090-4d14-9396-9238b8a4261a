package proto

import (
	"golang.52tt.com/pkg/store/redis/types"
)

type FloatCmd struct {
	*baseCmd
	val float64
}

func NewFloatCmd(args []interface{}, val float64, err error) *types.FloatCmd {
	cmd := &FloatCmd{
		baseCmd: newBaseCmd(args, err),
		val:     val,
	}
	return types.NewFloatCmd(cmd, cmd)
}

func (cmd *FloatCmd) SetVal(val float64) {
	cmd.val = val
}

func (cmd *FloatCmd) Val() float64 {
	return cmd.val
}

func (cmd *FloatCmd) Result() (float64, error) {
	return cmd.Val(), cmd.Err()
}

func (cmd *FloatCmd) String() string {
	return cmdString(cmd, cmd.val)
}
