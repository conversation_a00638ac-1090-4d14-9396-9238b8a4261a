package proto

import (
	"time"

	"golang.52tt.com/pkg/store/redis/types"
)

type TimeCmd struct {
	*baseCmd
	val time.Time
}

func NewTimeCmd(args []interface{}, val time.Time, err error) *types.TimeCmd {
	cmd := &TimeCmd{
		baseCmd: newBaseCmd(args, err),
		val:     val,
	}
	return types.NewTimeCmd(cmd, cmd)
}

func (cmd *TimeCmd) SetVal(val time.Time) {
	cmd.val = val
}

func (cmd *TimeCmd) Val() time.Time {
	return cmd.val
}

func (cmd *TimeCmd) Result() (time.Time, error) {
	return cmd.val, cmd.err
}

func (cmd *TimeCmd) String() string {
	return cmdString(cmd, cmd.val)
}
