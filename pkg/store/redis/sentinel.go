package redis

import (
	"context"
	"fmt"
	"sync"
	"time"

	"golang.52tt.com/pkg/sentinel"
	"golang.52tt.com/pkg/sentinel/interceptor"
	"golang.52tt.com/pkg/store/redis/internal"
	"golang.52tt.com/pkg/store/redis/internal/conf"
	"golang.52tt.com/pkg/store/redis/internal/log"
)

var (
	sentinelInitOnce sync.Once

	sentinelConfigFile       = conf.Static().SentinelConfigPath
	sentinelCmdRuleName      = conf.Static().SentinelCmdRuleName
	sentinelPipelineRuleName = conf.Static().SentinelPipelineRuleName
)

var cmdSentinelMethod = fmt.Sprintf("redis_cmd_sentinel_%d", time.Now().Unix())
var pipelineSentinelMethod = fmt.Sprintf("redis_pipeline_sentinel_%d", time.Now().Unix())

func sentinelCmdInterceptor(addr string) CmdInterceptor {
	return func(ctx context.Context, invoker CmdInvoker) (cmd Cmder, err error) {
		if _, ok := ctx.Deadline(); !ok {
			// skip if ctx without deadline
			return invoker(ctx)
		}

		rule := interceptor.Match(cmdSentinelMethod)
		if rule == nil {
			return invoker(ctx)
		}

		i, err := sentinel.WrapsWithFallback(rule.Names, func() (interface{}, error) {
			return invoker(ctx)
		}, func(err error) (interface{}, error) {
			log.ErrorWithCtx(ctx, "redis cmd circuit broken, err:%v", err)
			internal.ReportCircuitBreaking(ctx, addr)
			internal.AlarmCircuitBreaking(ctx, "Redis命令熔断", addr)
			return nil, err
		})
		cmd, _ = i.(Cmder)
		return
	}
}

func sentinelPipelineInterceptor(addr string) PipelineInterceptor {
	return func(ctx context.Context, invoker PipelineInvoker) (cmds []Cmder, err error) {
		if _, ok := ctx.Deadline(); !ok {
			// skip if ctx without deadline
			return invoker(ctx)
		}

		rule := interceptor.Match(pipelineSentinelMethod)
		if rule == nil {
			return invoker(ctx)
		}

		i, err := sentinel.WrapsWithFallback(rule.Names, func() (interface{}, error) {
			return invoker(ctx)
		}, func(err error) (interface{}, error) {
			log.ErrorWithCtx(ctx, "redis pipeline circuit broken, err:%v", err)
			internal.ReportCircuitBreaking(ctx, addr)
			internal.AlarmCircuitBreaking(ctx, "RedisPipeline熔断", addr)
			return nil, err
		})
		cmds, _ = i.([]Cmder)
		return
	}
}

func initSentinel() {
	sentinelInitOnce.Do(func() {
		if err := sentinel.Init(context.Background(), internal.AppName, sentinelConfigFile); err != nil {
			log.Error("sentinel.Init failed, err:%v, app:%s", err, internal.AppName)
			return
		}

		interceptor.Register(cmdSentinelMethod, &interceptor.Rule{
			Name: sentinelCmdRuleName,
		})
		interceptor.Register(pipelineSentinelMethod, &interceptor.Rule{
			Name: sentinelPipelineRuleName,
		})
	})
}
