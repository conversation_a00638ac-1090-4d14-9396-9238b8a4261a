package redis

import (
	"golang.52tt.com/pkg/config"
)

type Client struct {
	*baseClient
	Cmdable
}

func NewClient(cfg *config.RedisConfig, opts ...Option) *Client {
	return newClient(cfg, opts)
}

// 兼容 RedisConfig.NewDefualtGoRedisClient
func NewDefaultClient(cfg *config.RedisConfig, opts ...Option) *Client {
	c := *cfg
	c.DialTimeout, c.ReadTimeout, c.WriteTimeout = 10, 10, 10
	return newClient(&c, opts)
}

// 导入原客户端
func NewNativeClient(cfg *config.RedisConfig, t NativeType, c interface{}) *Client {
	return NewClient(cfg, WithNativeType(t), WithNativeClient(c))
}

// 获取 v2 版本的 Client
func (c *Client) V2() *ClientV2 {
	return &ClientV2{
		baseClient: c.baseClient,
		CmdableV2:  c.cmderV2,
	}
}

//------------------------------------------------------------------------------

// 建议使用 v2 版本的 Client;
// 接口支持 context 参数
type ClientV2 struct {
	*baseClient
	CmdableV2
}

func NewClientV2(cfg *config.RedisConfig, opts ...Option) *ClientV2 {
	return newClientV2(cfg, opts)
}

// 获取 v1 版本的 Client
func (c *ClientV2) V1() *Client {
	return &Client{
		baseClient: c.baseClient,
		Cmdable:    c.cmder,
	}
}
