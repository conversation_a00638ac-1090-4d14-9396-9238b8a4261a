package redis

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tyr/x/runtime/rely"
	"golang.52tt.com/pkg/reporter"
	"os"
	"path/filepath"
	"sync"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/store/redis/types"
)

// 由于当前版本自定义了 redis.Nil
// 需要与其他版本互斥，防止出现判断异常
//func init() {
//	registerResource()
//}

var registerResourceOnce sync.Once

func registerResource() {
	// 先用告警来预防
	defer func() {
		r := recover()
		if nil != r {
			app := os.Getenv("MY_APP_NAME")
			if app == "" {
				if exe, err := os.Executable(); err == nil && "" != exe {
					app = filepath.Base(exe)
				}
			}

			_ = reporter.NewFeishuReporter("https://open.feishu.cn/open-apis/bot/v2/hook/c6adf9a8-ba8d-4df9-9491-b5800efa2370", "T0mm8WmlowGSrq7EjwkWef").
				SendText("新旧版本 redis 混用 > " + app)
		}
	}()
	rely.RegisterResource(rely.NewResource(rely.Redis, "v8-custom"))
}

type baseClient struct {
	Helper
	opts    *options
	cmder   Cmdable
	cmderV2 CmdableV2
	native  NativeClient
}

func newBaseClient(cfg *config.RedisConfig, opts []Option) *baseClient {
	loader, err := newConfigLoader(cfg)
	if err != nil {
		return nil
	}
	cfg = loader.Config()

	cli := &baseClient{
		opts: &options{
			loader: loader,
		},
	}

	for _, o := range opts {
		o.apply(cli.opts)
	}

	if !cli.opts.skipRelyCheck {
		registerResourceOnce.Do(func() {
			registerResource()
		})
	}

	WithCmdInterceptors(types.GetCmdInterceptors()...).apply(cli.opts)
	WithPipelineInterceptors(types.GetPipelineInterceptors()...).apply(cli.opts)

	if cfg.EnableCircuitBreaking {
		initSentinel()
		WithCmdInterceptors(sentinelCmdInterceptor(cfg.Addr())).apply(cli.opts)
		WithPipelineInterceptors(sentinelPipelineInterceptor(cfg.Addr())).apply(cli.opts)
	}

	cli.native = newNativeClient(cli.opts)
	proc := newCmdProcessor(cli.native, cli.opts, nil)

	cli.cmder = newCmdable(proc)
	cli.cmderV2 = newCmdableV2(proc)
	cli.Helper = NewHelper(cli.cmderV2)

	return cli
}

func (b *baseClient) WithCtx(ctx context.Context) Cmdable {
	return newCmdableWithCtx(ctx, b.cmderV2)
}

func (b *baseClient) NativeClient() interface{} {
	return b.native.NativeClient()
}

func (b *baseClient) NativeType() NativeType {
	return b.native.NativeType()
}

func (c *baseClient) String() string {
	cfg := c.opts.Config()
	return fmt.Sprintf("Redis<%s db:%d>", cfg.Addr(), cfg.DB)
}

func (c *baseClient) PoolStats() *PoolStats {
	return c.native.PoolStats()
}

func (c *baseClient) Close() error {
	return c.native.Close()
}

func newClient(cfg *config.RedisConfig, opts []Option) *Client {
	c := newBaseClient(cfg, opts)
	if c == nil {
		return nil
	}

	return &Client{
		baseClient: c,
		Cmdable:    c.cmder,
	}
}

func newClientV2(cfg *config.RedisConfig, opts []Option) *ClientV2 {
	c := newBaseClient(cfg, opts)
	if c == nil {
		return nil
	}

	return &ClientV2{
		baseClient: c,
		CmdableV2:  c.cmderV2,
	}
}
