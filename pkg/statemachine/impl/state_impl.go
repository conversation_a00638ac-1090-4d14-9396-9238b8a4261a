package impl

import (
	"fmt"
	statemachinePB "golang.52tt.com/protocol/services/statemachine"

	"golang.52tt.com/pkg/statemachine"
)

type StateImpl struct {
	stateId          statemachinePB.State
	eventTransitions *EventTransitions
}

func NewStateImpl(stateId statemachinePB.State) *StateImpl {
	return &StateImpl{
		stateId:          stateId,
		eventTransitions: NewEventTransitions(),
	}
}

func (s *StateImpl) GetId() statemachinePB.State {
	return s.stateId
}

func (s *StateImpl) AddTransition(event statemachinePB.Event, target statemachine.InnerState, transitionType statemachinePB.TransitionType) statemachine.Transition {
	newTransition := NewTransitionImpl()
	newTransition.SetSource(s)
	newTransition.SetTarget(target)
	newTransition.SetEvent(event)
	newTransition.SetType(transitionType)

	s.eventTransitions.Put(event, newTransition)

	return newTransition
}

func (s *StateImpl) GetEventTransitions(event statemachinePB.Event) []statemachine.Transition {
	return s.eventTransitions.Get(event)
}

func (s *StateImpl) GetAllTransitions() []statemachine.Transition {
	return s.eventTransitions.AllTransitions()
}

func (s *StateImpl) Accept(visitor statemachine.Visitor) string {
	entry := visitor.VisitStateOnEntry(s)
	exit := visitor.VisitStateOnExit(s)

	return entry + exit
}

func (s *StateImpl) String() string {
	return fmt.Sprintf("%v", s.GetId())
}
