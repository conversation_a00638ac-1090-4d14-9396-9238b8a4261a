package impl

import (
	"fmt"
	statemachinePB "golang.52tt.com/protocol/services/statemachine"

	"golang.52tt.com/pkg/statemachine"
)

type EventTransitions struct {
	eventTransitions map[statemachinePB.Event][]statemachine.Transition
}

func NewEventTransitions() *EventTransitions {
	return &EventTransitions{
		eventTransitions: make(map[statemachinePB.Event][]statemachine.Transition),
	}
}

func (e *EventTransitions) Put(event statemachinePB.Event, transition statemachine.Transition) {
	existingTransitions, ok := e.eventTransitions[event]
	if !ok {
		e.eventTransitions[event] = []statemachine.Transition{transition}
	} else {
		if err := e.verify(existingTransitions, transition); err != nil {
			panic(err)
		}
		e.eventTransitions[event] = append(e.eventTransitions[event], transition)
	}

	return
}

func (e *EventTransitions) Get(event statemachinePB.Event) []statemachine.Transition {
	return e.eventTransitions[event]
}

func (e *EventTransitions) AllTransitions() []statemachine.Transition {
	var ts []statemachine.Transition
	for _, transitions := range e.eventTransitions {
		ts = append(ts, transitions...)
	}

	return ts
}

func (e *EventTransitions) verify(existingTransitions []statemachine.Transition, transition statemachine.Transition) error {
	for _, item := range existingTransitions {
		if item.Equal(transition) {
			return fmt.Errorf("%+v already Exist, you can not add another one", transition)
		}
	}

	return nil
}
