package builder

import (
	"golang.52tt.com/pkg/statemachine"
	statemachineImpl "golang.52tt.com/pkg/statemachine/impl"
	"golang.52tt.com/pkg/statemachine/statemachine_factory"
	statemachinePB "golang.52tt.com/protocol/services/statemachine"
)

func NewStateMachineBuilderImpl() *StateMachineBuilderImpl {
	stateMap := make(map[statemachinePB.State]statemachine.InnerState)
	return &StateMachineBuilderImpl{
		stateMap:     stateMap,
		stateMachine: statemachineImpl.NewStateMachineImpl(stateMap),
	}
}

type StateMachineBuilderImpl struct {
	stateMap     map[statemachinePB.State]statemachine.InnerState
	stateMachine *statemachineImpl.StateMachineImpl
}

func (s *StateMachineBuilderImpl) InternalTransition() InternalTransitionBuilder {
	return NewTransitionBuilderImpl(s.stateMap, statemachinePB.TransitionType_INTERNAL)
}

func (s *StateMachineBuilderImpl) ExternalTransition() ExternalTransitionBuilder {
	return NewTransitionBuilderImpl(s.stateMap, statemachinePB.TransitionType_EXTERNAL)
}

func (s *StateMachineBuilderImpl) ExternalTransitions() ExternalTransitionsBuilder {
	return NewTransitionsBuilderImpl(s.stateMap, statemachinePB.TransitionType_EXTERNAL)
}

func (s *StateMachineBuilderImpl) Build(machineId string) (statemachine.StateMachine, error) {
	s.stateMachine.SetMachineId(machineId)
	s.stateMachine.SetReady(true)
	err := statemachine_factory.Register(s.stateMachine)
	if err != nil {
		return nil, err
	}

	return s.stateMachine, nil
}
