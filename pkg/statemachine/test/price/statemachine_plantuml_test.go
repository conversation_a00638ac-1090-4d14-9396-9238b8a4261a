package price

import (
	"fmt"
	"testing"

	statemachinePB "golang.52tt.com/protocol/services/statemachine"

	"github.com/stretchr/testify/require"

	"golang.52tt.com/pkg/statemachine/statemachine_builder_factory"
)

func TestPlantUML(t *testing.T) {
	checkCondition := func(ctx interface{}) bool {
		return true
	}

	doAction := func(from, to statemachinePB.State, event statemachinePB.Event, ctx interface{}) error {
		fmt.Printf("from: %v to: %v event: %v ctx: %v\n", from, to, event, ctx)
		return nil
	}

	builder := statemachine_builder_factory.Create()

	builder.ExternalTransition().
		From(statemachinePB.State_TEST_NONE).
		To(statemachinePB.State_TEST_SUPPLIER_PROCESSING).
		On(statemachinePB.Event_TEST_CREATE).
		When(checkCondition).
		Perform(doAction)

	// 商家调价
	for _, state := range []statemachinePB.State{statemachinePB.State_TEST_SUPPLIER_PROCESSING, statemachinePB.State_TEST_SUPPLIER_MANAGER_PROCESSING, statemachinePB.State_TEST_PRICE_MANAGER_PROCESSING} {
		builder.ExternalTransition().
			From(state).
			To(statemachinePB.State_TEST_CLOSED).
			On(statemachinePB.Event_TEST_SUPPLIER_AGREE).
			When(checkCondition).
			Perform(doAction)
	}

	// 商家 -上升至-> 控商小二
	builder.ExternalTransition().
		From(statemachinePB.State_TEST_SUPPLIER_PROCESSING).
		To(statemachinePB.State_TEST_SUPPLIER_MANAGER_PROCESSING).
		On(statemachinePB.Event_TEST_SUPPLIER_REJECT).
		When(checkCondition).
		Perform(doAction)

	builder.ExternalTransition().
		From(statemachinePB.State_TEST_SUPPLIER_PROCESSING).
		To(statemachinePB.State_TEST_SUPPLIER_MANAGER_PROCESSING).
		On(statemachinePB.Event_TEST_SUPPLIER_TIMEOUT).
		When(checkCondition).
		Perform(doAction)

	// 申请申请高于P0售卖
	builder.ExternalTransition().
		From(statemachinePB.State_TEST_SUPPLIER_MANAGER_PROCESSING).
		To(statemachinePB.State_TEST_PRICE_MANAGER_PROCESSING).
		On(statemachinePB.Event_TEST_APPLY_OVER_P0_SELL).
		When(checkCondition).
		Perform(doAction)

	// 同意高于P0价售卖
	builder.ExternalTransition().
		From(statemachinePB.State_TEST_PRICE_MANAGER_PROCESSING).
		To(statemachinePB.State_TEST_CLOSED).
		On(statemachinePB.Event_TEST_AGREE_OVER_P0_SELL).
		When(checkCondition).
		Perform(doAction)

	// 拒绝高于P0价售卖
	builder.ExternalTransition().
		From(statemachinePB.State_TEST_PRICE_MANAGER_PROCESSING).
		To(statemachinePB.State_TEST_SUPPLIER_MANAGER_PROCESSING).
		On(statemachinePB.Event_TEST_REJECT_OVER_P0_SELL).
		When(checkCondition).
		Perform(doAction)

	// 普通字段更新事件
	for _, state := range []statemachinePB.State{statemachinePB.State_TEST_SUPPLIER_PROCESSING, statemachinePB.State_TEST_SUPPLIER_MANAGER_PROCESSING, statemachinePB.State_TEST_PRICE_MANAGER_PROCESSING} {
		builder.InternalTransition().
			Within(state).
			On(statemachinePB.Event_TEST_NORMAL_UPDATE).
			When(checkCondition).
			Perform(doAction)
	}

	// P0价变更事件、页面价高于合理价事件
	for _, event := range []statemachinePB.Event{statemachinePB.Event_TEST_P0_CHANGED, statemachinePB.Event_TEST_PAGE_PRICE_CHANGED} {
		builder.ExternalTransitions().
			FromAmong([]statemachinePB.State{statemachinePB.State_TEST_SUPPLIER_PROCESSING, statemachinePB.State_TEST_SUPPLIER_MANAGER_PROCESSING, statemachinePB.State_TEST_PRICE_MANAGER_PROCESSING}).
			To(statemachinePB.State_TEST_CLOSED).
			On(event).
			When(checkCondition).
			Perform(doAction)
	}

	stateMachine, err := builder.Build("AdjustPriceTask")
	require.Nil(t, err)
	fmt.Println(stateMachine.GeneratePlantUML())
}
