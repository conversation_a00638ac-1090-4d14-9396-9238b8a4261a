package rdb

import (
	"encoding/json"
	"errors"
	"log"
	"reflect"
	"strconv"
	"time"

	"github.com/go-redis/redis"
)

var (
	REDIS_ERROR_NIL = redis.Nil
)

type MyRedis struct {
	host   string
	port   int
	expire time.Duration
	client *RedisClient
}

func NewMyRedisWithMonitorEx(host string, port string, expire int64) (rule *MyRedis, err error) {
	_port, err := strconv.Atoi(port)
	if err != nil {
		return
	}
	return NewMyRedisWithMonitor(host, _port, expire)
}

func NewMyRedisWithMonitor(host string, port int, expire int64) (rule *MyRedis, err error) {
	return NewMyRedisWithOption(host, port, expire, 0, true, false)
}

//@expire: 多少分钟
func NewMyRedisEx(host string, port string, expire int64) (rule *MyRedis, err error) {
	_port, err := strconv.Atoi(port)
	if err != nil {
		return
	}
	return NewMyRedis(host, _port, expire)
}

func NewMyRedis(host string, port int, expire int64) (rule *MyRedis, err error) {
	return NewMyRedisWithOption(host, port, expire, 0, false, false)
}

func NewMyRedisWithDBEx(host string, port string, expire int64, db int64, monitored bool) (rule *MyRedis, err error) {
	_port, err := strconv.Atoi(port)
	if err != nil {
		return
	}
	return NewMyRedisWithDB(host, _port, expire, db, monitored)
}

func NewMyRedisWithDB(host string, port int, expire int64, db int64, monitored bool) (rule *MyRedis, err error) {
	return NewMyRedisWithOption(host, port, expire, db, monitored, false)
}

func NewMyRedisWithOptionEx(host, port string, expire int64, db int64, monitored, newConn bool) (rule *MyRedis, err error) {
	_port, err := strconv.Atoi(port)
	if err != nil {
		return
	}
	return NewMyRedisWithOption(host, _port, expire, db, monitored, newConn)
}

func NewMyRedisWithOption(host string, port int, expire int64, db int64, monitored, newConn bool) (rule *MyRedis, err error) {
	rule = &MyRedis{
		host:   host,
		port:   port,
		expire: time.Minute * time.Duration(expire),
	}
	rule.client, err = ConnectRedisWithOption("RuleMq", host, port, monitored, newConn, db)
	if err != nil {
		return
	}
	return
}

func (r *MyRedis) Client() *RedisClient {
	return r.client
}

func (r *MyRedis) Addr() string {
	return r.client.Addr()
}

func (r *MyRedis) Kill() {
	r.Close()
}

func (r *MyRedis) Ping() (err error) {
	res := r.client.Ping()
	return res.Err()
}

func (r *MyRedis) Close() (err error) {
	err = r.client.Close()
	r.client.closed = true
	return
}

func (r *MyRedis) GetExpireConfig() (ttl int64) {
	return int64(r.expire.Seconds())
}

func (r *MyRedis) Subscribe(channel ...string) (ps *MyPubsub, err error) {
	if !r.Client().Pong() {
		return ps, errors.New("redis not pong : " + r.Client().Status() + " : " + r.Client().Addr())
	}
	if pubsub := r.client.Subscribe(channel...); pubsub == nil {
		return ps, errors.New("pubsub is nil")
	} else {
		return &MyPubsub{PubSub: pubsub}, nil
	}
	return
}

func (r *MyRedis) Publish(channel string, message string) (err error) {
	if !r.Client().Pong() {
		return errors.New("redis not pong : " + r.Client().Status() + " : " + r.Client().Addr())
	}
	if res := r.client.Publish(channel, message); res == nil {
		return errors.New("publish faild")
	} else {
		return res.Err()
	}
	return
}

func (r *MyRedis) Reconnect() (err error) {
	if r.client != nil {
		r.Close()
	}
	r.client, err = ConnectRedisWithOption("RuleMq", r.host, r.port, r.client.monitored, r.client.newConn, 0)
	return
}

func (r *MyRedis) Del(key ...string) (err error) {
	if len(key) > 0 {
		err = r.client.Del(key...).Err()
	}
	return
}

func (r *MyRedis) GetString(key string) (value string, err error) {
	if !r.Client().Pong() {
		return "", errors.New("redis not pong : " + r.Client().Status() + " : " + r.Client().Addr())
	}
	if res := r.client.Get(key); res == nil {
		return value, errors.New("Get faild")
	} else if err = res.Err(); err != nil {
		return value, err
	} else if res.Val() == "" {
		return value, errors.New("no more")
	} else {
		value = res.Val()
	}
	return value, nil
}

func (r *MyRedis) PopString(key string) (value string, err error) {
	if !r.Client().Pong() {
		return "", errors.New("redis not pong : " + r.Client().Status() + " : " + r.Client().Addr())
	}
	if res := r.client.RPop(key); res == nil {
		return value, errors.New("get value faild")
	} else if err := res.Err(); err != nil {
		return value, err
	} else if res.Val() == "" {
		return value, errors.New("nul value")
	} else {
		value = res.Val()
	}
	return value, nil
}

/*func (r *MyRedis) GetBson(key string, value *bson.M) (err error){*/
/*if v,err := r.GetString(key); err != nil{*/
/*return err*/
/*}else{*/
/*return json.Unmarshal([]byte(v), value)*/
/*}*/
/*return nil*/
/*}*/

/*func (r *MyRedis) GetBsonAll(key string, values *[]bson.M) (err error){*/
/**values = []bson.M{}*/
/*for{*/
/*if v,err := r.PopString(key); err != nil{*/
/*break*/
/*}else{*/
/*value := map[string] interface{}{}*/
/*if err = json.Unmarshal([]byte(v), &value); err != nil{*/
/*log.Print("here",value)*/
/*return err*/
/*}*/
/**values = append(*values, value)*/
/*}*/
/*}*/
/*return nil*/
/*}*/

func (r *MyRedis) NBPop(key string, value interface{}) (err error) {
	valueV := reflect.ValueOf(value)
	if valueV.Kind() != reflect.Ptr {
		return errors.New("value param should be a slice address")
	}
	if valueV.Elem().Kind() == reflect.Struct {
		if v, err := r.GetString(key); err != nil {
			return err
		} else {
			return json.Unmarshal([]byte(v), value)
		}
	} else if valueV.Elem().Kind() == reflect.Slice {
		sliceV := valueV.Elem()
		elemT := sliceV.Type().Elem()
		if elemT.Kind() != reflect.Struct {
			return errors.New("value should be struct,slice or string")
		}
		for {
			if v, err := r.PopString(key); err != nil {
				break
			} else {
				if elemT.Kind() == reflect.Struct {
					newV := reflect.New(elemT)
					/*log.Print(newV)*/
					if err = json.Unmarshal([]byte(v), newV.Interface()); err != nil {
						log.Print("here", v)
						return err
					}
					sliceV = reflect.Append(sliceV, newV.Elem())
				} else {
					sliceV = reflect.Append(sliceV, reflect.ValueOf(v))
				}
			}
		}
		valueV.Elem().Set(sliceV)
	}
	return nil
}

func (r *MyRedis) Touch(key string) (err error) {
	if r.expire == 0 {
		return nil
	}

	return r.client.Expire(key, r.expire).Err()
}

func (r *MyRedis) SetString(key string, value string) (err error) {
	err = r.client.Set(key, value, r.expire).Err()
	return
}

func (r *MyRedis) SetStringAndExpire(key string, value string, expire int64) (err error) {
	err = r.client.Set(key, value, time.Second*time.Duration(expire)).Err()
	return
}

/*func (r *MyRedis) SetBsonBatch(key string,values []bson.M) (err error){*/
/*for _, value := range values{*/
/*v, err := json.Marshal(value)*/
/*if err != nil{*/
/*return err*/
/*}*/
/*if err = r.PushString(key, string(v)); err != nil{*/
/*return err*/
/*}*/
/*}*/
/*return nil*/
/*}*/

/*func (r *MyRedis) SetBson(key string,value bson.M) (err error){*/
/*v, err := json.Marshal(value)*/
/*if err != nil{*/
/*return err*/
/*}*/
/*return r.SetString(key, string(v))*/
/*}*/

func (r *MyRedis) NBPush(key string, value interface{}) (err error) {
	valueV := reflect.ValueOf(value)
	if valueV.Kind() == reflect.Ptr {
		valueV = valueV.Elem()
	}
	if valueV.Kind() == reflect.Struct {
		v, err := json.Marshal(value)
		if err != nil {
			return err
		}
		return r.SetString(key, string(v))
	} else if valueV.Kind() == reflect.Slice {
		if valueV.Type().Elem().Kind() != reflect.Struct {
			return errors.New("value param should be struct slice")
		}
		for i := 0; i < valueV.Len(); i++ {
			v, err := json.Marshal(valueV.Index(i).Interface())
			if err != nil {
				return err
			}
			if err = r.PushString(key, string(v)); err != nil {
				return err
			}
		}
	}
	return nil
}

func (r *MyRedis) LLen(key string) (cnt int64, err error) {
	if !r.Client().Pong() {
		return 0, errors.New("redis not pong : " + r.Client().Status() + " : " + r.Client().Addr())
	}
	res := r.client.LLen(key)
	if res == nil {
		return 0, errors.New("Get faild")
	} else if err = res.Err(); err != nil {
		return 0, err
	}
	return res.Val(), nil
}

func (r *MyRedis) PushBytes(key string, value []byte) (err error) {
	if !r.Client().Pong() {
		return errors.New("redis not pong : " + r.Client().Status() + " : " + r.Client().Addr())
	}
	if res := r.client.LPush(key, value); res == nil {
		return errors.New("Get faild")
	} else if err = res.Err(); err != nil {
		return err
	}
	if r.expire != 0 {
		if res := r.client.Expire(key, r.expire); res == nil {
			return errors.New("Set expire faild")
		} else if err = res.Err(); err != nil {
			return err
		}
	}
	return nil
}

func (r *MyRedis) PushString(key string, value string) (err error) {
	if !r.Client().Pong() {
		return errors.New("redis not pong : " + r.Client().Status() + " : " + r.Client().Addr())
	}
	if res := r.client.LPush(key, value); res == nil {
		return errors.New("Get faild")
	} else if err = res.Err(); err != nil {
		return err
	}
	if r.expire != 0 {
		if res := r.client.Expire(key, r.expire); res == nil {
			return errors.New("Set expire faild")
		} else if err = res.Err(); err != nil {
			return err
		}
	}
	return nil
}

/*func (r *MyRedis) SetBsonBatch(key string,values []bson.M) (err error){*/
/*for _, value := range values{*/
/*v, err := json.Marshal(value)*/
/*if err != nil{*/
/*return err*/
/*}*/
/*if err = r.PushString(key, string(v)); err != nil{*/
/*return err*/
/*}*/
/*}*/
/*return nil*/
/*}*/

/*func (r *MyRedis) SetBson(key string,value bson.M) (err error){*/
/*v, err := json.Marshal(value)*/
/*if err != nil{*/
/*return err*/
/*}*/
/*return r.SetString(key, string(v))*/
/*}*/

func (r *MyRedis) PushRule(key string, value interface{}) (err error) {
	v, err := json.Marshal(value)
	if err != nil {
		return
	}
	if err = r.PushString(key, string(v)); err != nil {
		return
	}
	return
}

func (r *MyRedis) Keys(pattern string) (keys []string, err error) {
	res := r.client.Keys(pattern)
	return res.Val(), res.Err()
}

func (r *MyRedis) DelByPattern(pattern string) (err error) {
	keys, err := r.Keys(pattern)
	if err != nil {
		return
	}

	r.Del(keys...)
	return
}

func (r *MyRedis) Expire(key string, seconds int64) (err error) {
	res := r.client.Expire(key, time.Duration(seconds)*time.Second)
	return res.Err()
}

func (r *MyRedis) PExpireAt(key string, milliseconds int64) (err error) {
	res := r.client.PExpireAt(key, time.Unix(milliseconds/1000, milliseconds%1000*1000000))
	return res.Err()
}

func (r *MyRedis) ExpireAt(key string, seconds int64) (err error) {
	res := r.client.ExpireAt(key, time.Unix(seconds, 0))
	return res.Err()
}

func (r *MyRedis) TTL(key string) (second int64, err error) {
	res := r.client.TTL(key)
	return int64(res.Val().Seconds()), res.Err()
}

func (r *MyRedis) MGet(key ...string) ([]interface{}, error) {
	res := r.client.MGet(key...)
	return res.Val(), res.Err()
}

// 不存在field 返回 “”， nil...
func (r *MyRedis) HGet(key string, field string) (value string, err error) {
	res := r.client.HGet(key, field)
	if res.Err() != nil && res.Err() == redis.Nil {
		return "", nil
	}
	return res.Val(), res.Err()
}

func (r *MyRedis) HSet(key string, field string, value string) (err error) {
	res := r.client.HSet(key, field, value)
	return res.Err()
}

func (r *MyRedis) HDel(key string, field ...string) (val int64, err error) {
	res := r.client.HDel(key, field...)
	return res.Val(), res.Err()
}

func (r *MyRedis) map2slice(mp map[string]string) (sl []string) {
	for k, v := range mp {
		sl = append(sl, k)
		sl = append(sl, v)
	}
	return
}

func (r *MyRedis) HMGetAll(key string) (values []string, err error) {
	res := r.client.HGetAll(key)
	return r.map2slice(res.Val()), res.Err()
}

func (r *MyRedis) HMGetAllWithField(key string, fields ...string) (values []int64, err error) {
	res, err := r.HGetAll(key)
	if err != nil {
		return
	}
	var cont = make(map[string]int64)
	Len := len(res) / 2
	for i := 0; i < Len; i++ {
		field := res[i*2]
		value, err := strconv.ParseInt(res[i*2+1], 10, 64)
		if err != nil {
			continue
		}
		cont[field] = value
	}
	Len = len(fields)
	values = make([]int64, Len)
	for i := 0; i < Len; i++ {
		if value, ok := cont[fields[i]]; ok {
			values[i] = value
		}
	}
	return
}

func (r *MyRedis) HMGetInt(key string, field ...string) (values []int64, err error) {
	res := r.client.HMGet(key, field...)
	if err = res.Err(); err != nil {
		return
	}
	values = make([]int64, len(field))
	for i, value := range res.Val() {
		var valueInt int64
		switch value.(type) {
		case int:
			valueInt = int64(value.(int))
		case int64:
			valueInt = value.(int64)
		case string:
			valueInt, err = strconv.ParseInt(value.(string), 10, 64)
			if err != nil {
				return
			}
		}
		values[i] = valueInt
	}
	return
}

func (r *MyRedis) HGetAll(key string) ([]string, error) {
	res := r.client.HGetAll(key)
	return r.map2slice(res.Val()), res.Err()
}

func (r *MyRedis) HIncBy1(key, field string) error {
	incr := r.client.HIncrBy(key, field, 1)
	return incr.Err()
}

func (r *MyRedis) HIncBy1Ex(key, field string) (int64, error) {
	incr := r.client.HIncrBy(key, field, 1)
	return incr.Val(), incr.Err()
}

func (r *MyRedis) HIncr(key, field string, value int64) error {
	incr := r.client.HIncrBy(key, field, value)
	return incr.Err()
}

func (r *MyRedis) ZIncrBy(key string, field string, increment float64) error {
	return r.client.ZIncrBy(key, increment, field).Err()
}

func (r *MyRedis) ZRageByScores(key string, min, max string, offset, count int64) (values []string, err error) {
	res := r.client.ZRangeByScore(key, redis.ZRangeBy{
		Min:    min,
		Max:    max,
		Offset: offset,
		Count:  count,
	})
	return res.Val(), res.Err()
}

func (r *MyRedis) ZRemRangeByScore(key string, min, max string) error {
	return r.client.ZRemRangeByScore(key, min, max).Err()
}

func (r *MyRedis) PipelineExpire(pipeline *redis.Pipeline, keys []string, seconds int64) (res []*redis.BoolCmd, err error) {
	if pipeline == nil {
		pipeline := r.client.Pipeline()
		defer pipeline.Close()
	}
	for _, key := range keys {
		res = append(res, pipeline.Expire(key, time.Duration(seconds)*time.Second))
	}
	_, err = pipeline.Exec()
	return
}

func (c *MyRedis) Pipelined(do func(redis.Pipeliner) error) ([]redis.Cmder, error) {
	return c.client.Pipelined(do)
}

func (c *MyRedis) Zscore(key, member string) *redis.FloatCmd {
	return c.client.ZScore(key, member)
}

func (c *MyRedis) ZAdd(key string, members ...redis.Z) *redis.IntCmd {
	return c.client.ZAdd(key, members...)
}
