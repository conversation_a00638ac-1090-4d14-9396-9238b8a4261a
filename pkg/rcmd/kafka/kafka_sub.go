// duplicated from services/ugc/common/event/kafka_sub.go

package kafka

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"math/rand"
	"path"
	"runtime/debug"
	"sync"
	"time"

	"github.com/Shopify/sarama"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/monitor"
)

const (
	defaultKafkaConfigPath = "/home/<USER>/etc/client/kafka.conf"

	maxTryTimes = 15
)

var (
	errAlreadyStarted         = errors.New("kafka sub has already started")
	errMessageHandleWasNotSet = errors.New("message handler was not set")
)

func KafkaConfigPath() string {
	return path.Join(config.ClientConfigPath(), "kafka.conf")
}

type kafkaMessage struct {
	*sarama.ConsumerMessage

	triedTimes int
	backoff    time.Duration
}

type MessageProcessor func(msg *sarama.ConsumerMessage) (error, bool)

type KafkaSub struct {
	name string

	addrs   []string
	groupID string
	config  *sarama.Config
	topics  []string

	mu        sync.Mutex
	wg        sync.WaitGroup
	ctx       context.Context
	cancel    context.CancelFunc
	close     chan struct{}
	processor MessageProcessor
}

func NewKafkaSub(name string, addrs []string, groupID string, topics []string, config *sarama.Config) (*KafkaSub, error) {
	// The version of Kafka that Sarama will assume it is running against.
	// Defaults to the oldest supported stable version. Since Kafka provides
	// backwards-compatibility, setting it to a version older than you have
	// will not break anything, although it may prevent you from using the
	// latest features. Setting it to a version greater than you are actually
	// running may lead to random breakage.
	if !config.Version.IsAtLeast(sarama.V0_10_2_0) {
		// kafka consumer group最低版本要求是0.10.2
		config.Version = sarama.V1_1_1_0
	}

	return &KafkaSub{
		name:    name,
		addrs:   addrs,
		groupID: groupID,
		config:  config,
		topics:  topics,
	}, nil
}

func (s *KafkaSub) SetMessageProcessor(p MessageProcessor) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.processor = p
}

func (s *KafkaSub) Setup(session sarama.ConsumerGroupSession) error {
	log.Infof("[%s] setup claims=%v member-id=%s", s.name, session.Claims(), session.MemberID())
	return nil
}

func (s *KafkaSub) Cleanup(session sarama.ConsumerGroupSession) error {
	log.Infof("[%s] cleanup claims=%v member-id=%s", s.name, session.Claims(), session.MemberID())
	return nil
}

func (s *KafkaSub) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	log.Infof("[%s] consume claim: %s-%d", s.name, claim.Topic(), claim.Partition())
	for msg := range claim.Messages() {
		if s.processMessage(msg) {
			session.MarkMessage(msg, "")
		}
	}
	return nil
}

func (s *KafkaSub) processMessage(msg *sarama.ConsumerMessage) (shouldCommitOffset bool) {
	defer func() {
		err := recover()
		if err == nil {
			return
		}

		var stack string
		var buf bytes.Buffer
		buf.Write(debug.Stack())
		stack = buf.String()

		panicInfo := fmt.Sprintf("\npanic:%v\n===============\n%s\n", err, stack)
		monitor.RecordPanic(panicInfo)
		fmt.Printf("%v %s %s", err, "\n", stack)
	}()

	m := &kafkaMessage{ConsumerMessage: msg}
	shouldCommitOffset = true
	for {
		//latency := time.Since(m.Timestamp)
		//log.Infof("[%s] processMessage %s-%d-%d latency: %s", s.name, msg.Topic, msg.Partition, msg.Offset, latency)
		err, retryable := s.processor(m.ConsumerMessage)
		if err == nil {
			return
		}

		if !retryable {
			s.logFailedMessage(m, err)
			return
		}
		m.triedTimes++
		if m.triedTimes >= maxTryTimes {
			s.logFailedMessage(m, err)
			return
		}

		b := backoff(m.triedTimes)
		log.Errorf("[%s] retry(after %v) msg at %d:%d, topic=%s, key=%s. err: %v", s.name, b, msg.Partition, msg.Offset, msg.Topic, string(msg.Key), err)
		select {
		case <-time.After(b):
			continue
		case <-s.close:
			shouldCommitOffset = false
			return
		}
	}
}

func (s *KafkaSub) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	processor := s.processor
	if processor == nil {
		return errMessageHandleWasNotSet
	}

	if s.close != nil {
		return errAlreadyStarted
	}

	s.close = make(chan struct{})
	log.Infof("[%s] Start", s.name)

	s.ctx, s.cancel = context.WithCancel(context.Background())

	s.wg.Add(1)
	go func() {
		defer s.wg.Done()

		for {
			select {
			case <-s.close:
				log.Infof("consumer group closed")
				return
			default:
			}

			cg, err := sarama.NewConsumerGroup(s.addrs, s.groupID, s.config)
			if err != nil {
				<-time.After(time.Second * 3)
				continue
			}

			err = cg.Consume(s.ctx, s.topics, s)
			if err != nil {
				log.Errorf("[%s] consume err: %v, topics:%+v", s.name, err, s.topics)
			}

			cg.Close()
		}
	}()

	// s.wg.Add(3)
	// go func() {
	// 	defer s.wg.Done()

	// 	for {
	// 		select {
	// 		case msg, ok := <-s.c.Messages():
	// 			// multiplex mode
	// 			if !ok {
	// 				return
	// 			}
	// 			if s.processMessage(msg) {
	// 				s.c.MarkOffset(msg, "")
	// 			}

	// 		case part, ok := <-s.c.Partitions():
	// 			// partition mode
	// 			if !ok {
	// 				return
	// 			}

	// 			s.wg.Add(1)
	// 			go func(pc cluster.PartitionConsumer) {
	// 				defer s.wg.Done()
	// 				log.Debugf("[%s] Start consuming partition-%v-%d init-offset=%d", s.name, s.topics, pc.Partition(), pc.InitialOffset())
	// 				defer log.Debugf("[%s] Stop consuming partition-%v-%d", s.name, s.topics, pc.Partition())

	// 				for msg := range pc.Messages() {
	// 					if s.processMessage(msg) {
	// 						pc.MarkOffset(msg.Offset, "")
	// 					}
	// 				}
	// 			}(part)
	// 		case <-s.close:
	// 			return
	// 		}
	// 	}
	// }()

	// go func() {
	// 	defer s.wg.Done()

	// 	for err := range s.c.Errors() {
	// 		log.Errorf("[%s] received error %v", s.name, err)
	// 	}
	// }()

	// go func() {
	// 	defer s.wg.Done()

	// 	for noti := range s.c.Notifications() {
	// 		log.Debugf("[%s] received notification %v", s.name, noti)
	// 	}
	// }()

	return nil
}

func (s *KafkaSub) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.close != nil {
		close(s.close)
	}
	if s.cancel != nil {
		s.cancel()
	}
	s.wg.Wait()

	return
}

func (s *KafkaSub) logFailedMessage(msg *kafkaMessage, err error) {
	log.Errorf("[%s] failed to process msg at %d:%d, topic=%s key=%v value=%v. err: %v", s.name, msg.Partition, msg.Offset, msg.Topic, msg.Key, msg.Value, err)
}

func backoff(retries int) time.Duration {
	const (
		MaxDelay  = 15 * time.Second
		baseDelay = 500 * time.Millisecond
		factor    = 1.1
		jitter    = 0.01
	)

	if retries == 0 {
		return baseDelay
	}
	backoff, max := float64(baseDelay), float64(MaxDelay)
	for backoff < max && retries > 0 {
		backoff *= factor
		retries--
	}
	if backoff > max {
		backoff = max
	}

	backoff *= 1 + jitter*(rand.Float64()*2-1)
	if backoff < 0 {
		return 0
	}
	return time.Duration(backoff)
}
