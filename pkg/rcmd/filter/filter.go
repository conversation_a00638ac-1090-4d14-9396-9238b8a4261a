package filter

import (
	"encoding/binary"
	"fmt"
	"github.com/go-redis/redis"
	"github.com/seiflotfy/cuckoofilter"

	"golang.52tt.com/pkg/log"
	"time"
)

type FilterConfig struct {
	Bytes         int
	ExpireMinutes int
}

type FilterHelper struct {
	filterDao *FilterDao
	config    *FilterConfig
}

///////////////////////////////////////////////////////////////
func NewFilterHelper(filterDao *FilterDao, config *FilterConfig) (filter *FilterHelper) {
	filter = &FilterHelper{
		filterDao: filterDao,
		config:    config,
	}

	return
}

///////////////////////////////////////////////////////////////
func (f *FilterHelper) GetUserFilter(uid uint32) (uf *UserFilter) {
	ufStr, err := f.filterDao.GetUserFilter(uid)
	if err != nil {
		log.Errorf("GetUserFilter|failed to get filter! uid=%v", uid)
		return nil
	}

	if len(ufStr) > 0 {
		filter, err := cuckoo.Decode([]byte(ufStr))
		if err == nil {
			return &UserFilter{
				cf: filter,
			}
		}
	}

	return f.GetNewFilter()
}

func (f *FilterHelper) GetNewFilter() (uf *UserFilter) {
	return &UserFilter{
		cf: cuckoo.NewFilter(uint(f.config.Bytes)), // 注意：修改参数，会与已缓存数据不匹配
	}
}

func (f *FilterHelper) SetUserFilter(uid uint32, uf *UserFilter) bool {
	if uf == nil {
		log.Debugf("SetUserFilter|no user filter! uid=%v", uid)
		return false
	}

	bytes := uf.cf.Encode()
	if len(bytes) == 0 {
		log.Debugf("SetUserFilter|encode empty object: uid=%v", uid)
		return false
	}

	expireTime := time.Duration(f.config.ExpireMinutes) * time.Minute
	err := f.filterDao.SetUserFilter(uid, string(bytes), expireTime)
	if err != nil {
		log.Debugf("SetUserFilter|set failed: uid=%v;err=%v", uid, err)
	}

	return err == nil
}

func (f *FilterHelper) ClearUserFilter(uid uint32) bool {
	ret := f.filterDao.ClearUserFilter(uid) == nil
	return ret
}

////////////////////////////////////////////////////////////////
type UserFilter struct {
	cf          *cuckoo.Filter
	cfShortTerm *cuckoo.Filter
}

func (uf *UserFilter) HasUint(obj uint32) bool {
	return uf.cf.Lookup(uint32ToBytes(obj))
}

func (uf *UserFilter) AddUint(obj uint32) bool {
	return uf.cf.InsertUnique(uint32ToBytes(obj))
}

func (uf *UserFilter) HasString(obj string) bool {
	return uf.cf.Lookup([]byte(obj))
}

func (uf *UserFilter) AddString(obj string) bool {
	return uf.cf.InsertUnique([]byte(obj))
}

func uint32ToBytes(x uint32) (b []byte) {
	b = make([]byte, 4)
	binary.LittleEndian.PutUint32(b, x)
	return
}

////////////////////////////////////////////////////////////////
type FilterDao struct {
	redisCli        *redis.Client
	filterKeyPrefix string
}

func NewFilterDao(redisCli *redis.Client, filterKeyPrefix string) (filterDao *FilterDao) {
	filterDao = &FilterDao{
		redisCli:        redisCli,
		filterKeyPrefix: filterKeyPrefix,
	}
	return
}

func (d *FilterDao) ClearUserFilter(UserId uint32) (err error) {
	_, err = d.redisCli.Del(d.getUserFilterKey(UserId)).Result()
	if err != nil {
		log.Errorf("ClearUserFilter|failed: %v", err)
	}

	return
}

func (d *FilterDao) getUserFilterKey(uid uint32) string {
	return fmt.Sprintf("%v%v", d.filterKeyPrefix, uid)
}

// 获取用户过滤器序列化数据（连接无误没有命中时，err返回空，以result为空判断）
func (d *FilterDao) GetUserFilter(UserId uint32) (result string, err error) {
	result, err = d.redisCli.Get(d.getUserFilterKey(UserId)).Result()

	if err == redis.Nil {
		err = nil
		return
	}

	if err != nil {
		log.Errorf("GetUserFilter|failed: %v", err)
	}

	return
}

func (d *FilterDao) SetUserFilter(UserId uint32, data string, expireTime time.Duration) (err error) {
	_, err = d.redisCli.Set(d.getUserFilterKey(UserId), data, expireTime).Result()
	if err != nil {
		log.Errorf("SetUserFilter|failed: %v", err)
	}

	return
}
