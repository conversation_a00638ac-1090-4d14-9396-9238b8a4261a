package tx_mini

import (
	"golang.52tt.com/pkg/protocol"
)

type Version protocol.ClientVersion

var (
	minVersion = protocol.FormatClientVersion(0, 0, 0)
	maxVersion = protocol.FormatClientVersion(0xff, 0xff, 0xff)
)

func FormatVersion(major, minor uint8, patch uint16) Version {
	return Version(protocol.FormatClientVersion(major, minor, patch))
}

var (
	All  = minVersion
	None = maxVersion

	V0_0_1 = FormatVersion(0, 0, 1)
)
