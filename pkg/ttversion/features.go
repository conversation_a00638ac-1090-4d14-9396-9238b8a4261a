package ttversion

import (
	"golang.52tt.com/pkg/ttversion/android"
	"golang.52tt.com/pkg/ttversion/car"
	"golang.52tt.com/pkg/ttversion/iphone"
	"golang.52tt.com/pkg/ttversion/pc"
	"golang.52tt.com/pkg/ttversion/tx_mini"
)

var (
	// 非常用的 feature 可以定义在服务自己的目录下

	OfficialAccountMinFeature = New("公众号", android.V1_5_0, iphone.V1_0_0, car.V3_4_0, pc.V1_0_0)
	ImSyncV2Feature           = New("IM消息同步V2,群消息分离", android.V1_8_2, iphone.V1_0_2, car.V3_4_0, pc.V1_0_0)
	CheckSyncMarkReconnect    = New("CheckSync标记是否是重连之后的第一个请求", android.V1_8_2, iphone.UNAVAILABLE, car.V3_4_0, pc.V1_0_0)
	SystemOfficialAccount     = New("系统公众号", android.V2_1_0, iphone.V2_0_0, car.V3_4_0, pc.V1_0_0)

	ChannelBaseFeature           = New("房间操作基础", android.V2_5_3, iphone.V2_1_0, car.V3_4_0, pc.V1_0_1, tx_mini.V0_0_1)
	GuildPublicFunChannelFeature = New("公会公开房操作", android.V3_1_2, iphone.V3_1_0, car.V3_4_0, pc.V1_0_1, tx_mini.V0_0_1)
	SyncAdvancedConfigFeature    = New("高级配置同步", android.V3_1_5, iphone.V3_1_3, car.V3_4_0, pc.V1_0_0)
	TempChannelFeature           = New("临时开黑房操作", android.V3_2_4, iphone.V3_3_2, car.V3_4_0, pc.V1_0_1, tx_mini.V0_0_1)
	LiveChannelFeature           = New("语音直播房操作", android.V5_3_0, iphone.V5_3_0, car.None, pc.V1_2_0, tx_mini.V0_0_1)
	GuildHomeChannelFeature      = New("公会房操作", android.V3_3_0, iphone.V3_4_2, car.V3_4_0, pc.V1_0_1, tx_mini.V0_0_1)
	ImPresentFeature             = New("IM可送礼物", android.V5_4_8, iphone.V5_4_14, car.None, pc.V1_2_0, tx_mini.V0_0_1)
	OfficialLiveChannelFeature   = New("官方频道操作", android.V5_5_4, iphone.V5_5_10, car.None, pc.V1_5_2, tx_mini.V0_0_1)
	CPLChannelFeature            = New("CPL频道", android.V5_5_10, iphone.V5_5_20, car.None, pc.V1_5_2, tx_mini.V0_0_1)
	SuperChannelFeature          = New("大房支持版本", android.V5_5_10, iphone.V5_5_20, car.None, pc.V1_5_8, tx_mini.None)

	FreeMicModeFeature           = New("自由麦位模式", android.V2_5_3, iphone.V2_1_0, car.None, pc.V1_2_0, tx_mini.V0_0_1)
	DatingMicModeFeature         = New("相亲麦位模式", android.V3_3_2, iphone.V3_4_5, car.V3_4_0, pc.V1_2_0, tx_mini.V0_0_1)
	SingleMicModeFeature         = New("单麦模式", android.V3_1_0, iphone.V3_0_0, car.None, pc.V1_1_5, tx_mini.V0_0_1)
	HQMicModeFeature             = New("开黑/高音质麦位模式", android.V3_4_2, iphone.V3_6_2, car.V3_4_0, pc.V1_2_0, tx_mini.V0_0_1)
	WereWolvesGameMicModeFeature = New("狼人杀游戏麦位模式", android.V5_0_5, iphone.V5_0_9, car.None, pc.V1_2_0, tx_mini.V0_0_1)
	OpenGameMicModeFeature       = New("小游戏麦位模式", android.V5_0_3, iphone.V5_0_4, car.None, pc.V1_2_0, tx_mini.V0_0_1)
	CPGameMicModeFeature         = New("CP战麦位模式", android.V5_5_18, iphone.V5_5_33, car.None, pc.V1_5_11, tx_mini.V0_0_1)

	PGCWolvesMicModeFeature = New("公开厅狼人杀", android.V6_5_0, iphone.V6_5_0, car.None, pc.V1_6_12, tx_mini.None)

	UgcChannelSingARoundFeature = New("个人房唱歌房麦位模式", android.V5_8_0, iphone.V5_8_0, car.None, pc.V1_6_7, tx_mini.V0_0_1)
	UgcChannelKTVFeature        = New("个人KTV房麦位模式", android.V5_9_0, iphone.V5_9_0, car.None, pc.None, tx_mini.V0_0_1)

	CheckIopGroupInConsumeFeature = New("未成年人帐号触发人脸识别", android.V6_3_0, iphone.V6_3_0, car.None, pc.None, tx_mini.None)

	HQMicTenFeature = New("开黑房支持10号麦", android.V6_4_0, iphone.V6_7_0, pc.V1_7_1)

	UsualDeviceAuthCheckFeature = New("非常用设备验证", android.V6_5_0, iphone.V6_5_0, pc.V1_6_3)

	YouKnowWhoNewFeature   = New("神秘人V2新版本", android.V6_10_0, iphone.V6_10_0, pc.V1_7_9)
	YouKnowWhoRenewFeature = New("神秘人续费修改版本", android.V6_16_0, iphone.V6_16_0, pc.V1_8_6)

	CustomPresentFeature = New("专属定制礼物版本", android.V6_12_0, iphone.V6_12_0, pc.V1_8_1)
	MikeResourceFeature  = New("麦可资源替换", android.V6_26_0, iphone.V6_26_0, pc.None)
	OfferRoomFeature     = New("拍卖房拍卖位", iphone.V6_28_0, android.V6_28_0, pc.V1_8_14)
	GroupCommunity       = New("兴趣群", android.V6_36_0, iphone.V6_36_0, pc.V1_9_1)
	RechargeRiskFeature  = New("充值统一风控", android.V6_40_0, iphone.V6_40_0)
	EmperorSetFeature    = New("帝王套", android.V6_53_5, iphone.V6_53_5, pc.V2_0_2)

	PgcTitleFeature = New("娱乐厅冠名", android.V6_56_0, iphone.V6_56_0, pc.V2_0_5)

	PgcTitleDayFeature       = New("娱乐厅日冠名", android.V6_57_3, iphone.V6_57_3, pc.V2_0_7)
	LiveAwardInfoCardFeature = New("直播资料卡奖励", android.V6_59_5, iphone.V6_59_5, pc.V2_1_5)
	AdDspFeature             = New("TT-DSP广告", android.V6_64_5, iphone.V6_64_5, pc.V2_1_5)
)
