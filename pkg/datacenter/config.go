package datacenter

//const (
//	ttvoice  = "ttvoice"
//	huanyou  = "huanyou"
//	zaiya    = "zaiya"
//	topspeed = "topspeed"
//	maike    = "maike"
//	defaultConfigPath = "/etc/client/datacenter.yaml"
//)

//func init() {
//	filepath := os.Getenv("DATA_CENTER_CONF")
//	log.Debugf("load DATA_CENTER_CONF :%s", filepath)
//	if filepath == "" {
//		filepath = defaultConfigPath
//	}
//	cfg = &appConfig{file: filepath}
//	if cfg.loadConfigFromFile() != nil {
//		cfg.loadDefault()
//	}
//	go cfg.watch()
//}

//func GetTerminalTypeByMarketId(marketId uint32) string {
//	return cfg.Get(marketId)
//}

//var cfg *appConfig
//
//type appConfig struct {
//	cfg    map[string][]uint32
//	groups map[uint32]string
//	file   string
//}
//
//func (a *appConfig) Get(marketId uint32) string {
//	return a.groups[marketId]
//}

//func (a *appConfig) loadDefault() {
//	a.groups = map[uint32]string{
//		0: ttvoice,
//		1: ttvoice,
//		2: huanyou,
//		3: zaiya,
//		4: topspeed,
//		5: maike,
//	}
//}

//func (a *appConfig) loadConfigFromFile() error {
//	if a.file == "" {
//		log.Errorf("empty file path")
//		return fmt.Errorf("empty file path")
//	}
//	data, err := ioutil.ReadFile(a.file)
//	if err != nil {
//		log.Errorf("load app group config from file[%s] error:%s", a.file, err)
//		return err
//	}
//	cfg := make(map[string][]uint32)
//	err = yaml.Unmarshal(data, cfg)
//	if err != nil {
//		log.Errorf("unmarshal app group config error:%s", err)
//		return err
//	}
//	a.cfg = cfg
//	a.load()
//	return nil
//}
//func (a *appConfig) load() {
//	groups := make(map[uint32]string)
//	for k, v := range a.cfg {
//		for _, m := range v {
//			groups[m] = k
//		}
//	}
//	a.groups = groups
//}
//func (a *appConfig) watch() {
//	for {
//		a.loadConfigFromFile()
//		time.Sleep(time.Minute)
//	}
//}
