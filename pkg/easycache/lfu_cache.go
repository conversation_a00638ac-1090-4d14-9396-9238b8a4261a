package easycache

import (
	"container/list"
	"encoding/json"
	"fmt"
	"math/rand"
	"reflect"
	"sync"
	"time"

	"gitlab.ttyuyin.com/golang/gudetama/log"
)

var _ CacheInterface = new(LFUCache)

//最小频率先淘汰策略 模型是 cache带一个列表 列表是 记录所有频率的列表
type LFUCache struct {
	//频率列表
	freqList *list.List // list for freqEntry
	//key数据
	lockedCalls       LockedCallsInterface
	items             map[string]*lfuItem
	mtx               *sync.Mutex
	baseExpireMillSec int64
	size              int64
}

type lfuItem struct {
	Key       string
	Value     interface{}
	expireSec int64
	//反向指向频率列表
	freqEntry *list.Element
}

type freqEntry struct {
	freq  uint
	items map[*lfuItem]struct{}
}

func NewLFUCache(opts ...LFUCacheOpt) *LFUCache {
	size := int64(1000000000)
	baseExpireMillSec := int64(1000) //1s过期
	c := &LFUCache{
		items:             map[string]*lfuItem{},
		freqList:          list.New(),
		mtx:               &sync.Mutex{},
		size:              size,
		lockedCalls:       NewLockedCalls(),
		baseExpireMillSec: baseExpireMillSec,
	}
	for _, opt := range opts {
		opt(c)
	}
	return c
}

type LFUCacheOpt func(*LFUCache)

func WithLFUCacheSize(size int64) LFUCacheOpt {
	return func(o *LFUCache) {
		o.size = size
	}
}

func WithLFUCachebaseExpireMillSec(sec int64) LFUCacheOpt {
	return func(o *LFUCache) {
		o.baseExpireMillSec = sec
	}
}

func (c *LFUCache) doGet(key string, value interface{}) (error, bool) {
	//获取时 进行维护
	rv := reflect.ValueOf(value)
	if rv.Kind() != reflect.Ptr || rv.IsNil() {
		return fmt.Errorf("接收者为空或者非指针类型"), false
	}
	c.mtx.Lock()
	defer c.mtx.Unlock()
	if v, ok := c.items[key]; ok {
		if v.expireSec > time.Now().Unix() {
			//进行+1
			//currFreq将减少
			//nextFreq将增加
			currFreq := v.freqEntry.Value.(*freqEntry)
			var nextFreq *freqEntry
			if v.freqEntry.Next() == nil || (v.freqEntry.Next().Value.(*freqEntry)).freq != currFreq.freq+1 {
				nextFreq = &freqEntry{freq: currFreq.freq + 1, items: map[*lfuItem]struct{}{v: {}}}
			} else {
				nextFreq = v.freqEntry.Next().Value.(*freqEntry)
				nextFreq.items[v] = struct{}{}
			}
			nextFreqElement := c.freqList.InsertAfter(nextFreq, v.freqEntry)
			//删除原本属于的列表的值
			delete(currFreq.items, v)
			if len(currFreq.items) == 0 {
				c.freqList.Remove(v.freqEntry)
			}
			//重新赋值
			v.freqEntry = nextFreqElement
			v.expireSec = time.Now().Unix()*1000 + c.baseExpireMillSec + int64(rand.Intn(100))
			trySetGoal(v.Value, value)
			return nil, true
		} else {
			//过期了 进行删除
			delete(c.items, key)
			currFreq := v.freqEntry.Value.(*freqEntry)
			delete(currFreq.items, v)
			//列表 删除
			if len(currFreq.items) == 0 {
				c.freqList.Remove(v.freqEntry)
			}
			return nil, false
		}
	}
	return nil, false
}

func (c *LFUCache) doSet(key string, value interface{}) bool {
	c.mtx.Lock()
	defer c.mtx.Unlock()
	//看是否存在
	expireTime := time.Now().Unix()*1000 + c.baseExpireMillSec + int64(rand.Intn(100))
	item, ok := c.items[key]
	if ok {
		item.Value = value
	} else {
		if int64(len(c.items)) >= c.size {
			//删除频率最小 随机淘汰
			small := c.freqList.Front().Value.(*freqEntry)
			for val := range small.items {
				delete(c.items, val.Key)
				delete(small.items, val)
				break
			}
			if len(small.items) == 0 {
				c.freqList.Remove(c.freqList.Front())
			}
		}
		//构建
		item = &lfuItem{Value: value, Key: key}
		c.items[key] = item
		//头添加
		head := c.freqList.Front()
		if head == nil || head.Value.(*freqEntry).freq != 1 {
			fv := c.freqList.PushFront(&freqEntry{freq: 1, items: map[*lfuItem]struct{}{item: {}}})
			item.freqEntry = fv
		} else {
			head.Value.(*freqEntry).items[item] = struct{}{}
			item.freqEntry = c.freqList.Front()
		}
	}
	item.expireSec = expireTime
	return true
}

func (c *LFUCache) DebugFmt() {
	fmt.Println("begin...", c.freqList.Len())
	node := c.freqList.Front()

	for node != nil {
		fmt.Println(node.Value.(*freqEntry))
		node = node.Next()
	}
	fmt.Println("map:", c.items)
}

func (c *LFUCache) Take(value interface{}, key string, fetch func() (interface{}, error)) error {
	fnCall := func() error {
		//加localcache
		err, ok := c.doGet(key, value)
		if err != nil {
			return err
		}
		if ok {
			log.Debugf("Take lfu local cache key%v ok value:%v", key, value)
			return nil
		}
		v, err := fetch()
		if err != nil {
			log.Errorf("ERROR fetch key:%v error:%v", key, err)
			return err
		}
		b, err := json.Marshal(v)
		if err != nil {
			log.Errorf("Marshal value:%v err:%v", v, err)
			return err
		}
		c.doSet(key, b)
		json.Unmarshal(b, value)
		return nil
	}
	//共享内存操作
	return c.lockedCalls.Do(key, fnCall)
}
