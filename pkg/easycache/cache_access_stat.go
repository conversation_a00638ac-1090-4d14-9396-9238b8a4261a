package easycache

import (
	"golang.52tt.com/pkg/log"
	"sync/atomic"
	"time"
)

type CacheAccessStat struct {
	accessCnt uint64
	hitCnt    uint64
	statName  string
}

func NewCacheAccessStat(statName string, statPeriodTs ...uint32) *CacheAccessStat {
	statObj := &CacheAccessStat{
		accessCnt: 0,
		hitCnt:    0,
		statName:  statName,
	}

	go func() {
		statPeriod := uint32(60)
		if 0 != len(statPeriodTs) {
			statPeriod = statPeriodTs[0]
		}
		ticker := time.NewTicker(time.Duration(statPeriod) * time.Second)

		for {
			select {
			case <-ticker.C:
				statObj.StatTimerHandler()
			}
		}
	}()

	return statObj
}

func (c *CacheAccessStat) Access() {
	atomic.AddUint64(&c.accessCnt, 1)
}

func (c *CacheAccessStat) HitAndAccess() {
	atomic.AddUint64(&c.hitCnt, 1)
	atomic.AddUint64(&c.accessCnt, 1)
}

func (c *CacheAccessStat) StatTimerHandler() {
	hitRate := uint64(0)
	if 0 != c.accessCnt {
		hitRate = c.hitCnt * 100 / c.accessCnt
	}

	log.Infof("[%s] CacheAccessStat.statTimerHandler hitCnt: %d, accessCnt: %d, hitRate: %d%%",
		c.statName, c.hitCnt, c.accessCnt, hitRate)
}
