/*
 * @Description:
 * @Date: 2021-03-09 14:36:01
 * @LastEditors: liang
 * @LastEditTime: 2021-05-08 13:50:39
 */
package easycache

import (
	"fmt"
	"testing"
	"time"

	"github.com/go-redis/redis"
)

func TestDoSetLRU(t *testing.T) {
	cache := NewLRUCache(WithLRUCachebaseExpireMillSec(100), WithLRUCacheSize(3))
	cache.doSet("1", 1)
	cache.doSet("2", 1)
	cache.doSet("3", 1)
	cache.doSet("4", 1)
	cache.doSet("5", 1)
	cache.doSet("6", 1)
	cache.DebugFmt()
	cache.doSet("2", 1)
	cache.DebugFmt()
	cache.doSet("1", 1)
	cache.DebugFmt()
	cache.doSet("2", 1)
	cache.DebugFmt()
	ans := 0
	fmt.Println(cache.doGet("3", &ans))
	fmt.Println("ans:", ans)
	type test struct {
		Name string
	}

	cache.doSet("name", test{Name: "1111"})
	out := &test{}
	fmt.Println(cache.doGet("name", out))
	fmt.Println("out:", out)
}

func TestDoSetLFU(t *testing.T) {
	cache := NewLFUCache()
	cache.doSet("1", 1)
	cache.doSet("2", 1)
	cache.doSet("3", 1)
	cache.doSet("4", 1)
	cache.doSet("5", 1)
	cache.doSet("6", 1)
	cache.DebugFmt()
	cache.doSet("2", 1)
	cache.DebugFmt()
	cache.doSet("1", 1)
	cache.doSet("2", 1)
	cache.DebugFmt()
	ans := 0
	fmt.Println(cache.doGet("3", &ans))
	fmt.Println(cache.doGet("3", &ans))
	fmt.Println(cache.doGet("3", &ans))
	fmt.Println(cache.doGet("3", &ans))
	fmt.Println(cache.doGet("3", &ans))
	cache.DebugFmt()
	fmt.Println("ans:", ans)
	type test struct {
		Name string
	}

	cache.doSet("name", test{Name: "1111"})
	out := &test{}
	fmt.Println(cache.doGet("name", out))
	fmt.Println("out:", out)
}

func TestTake(t *testing.T) {
	redisClient := redis.NewClient(&redis.Options{
		Addr: "192.168.9.227:9720",
	})
	type Test struct {
		Name string
	}

	s := NewCacheRedis(redisClient)
	for i := 0; i < 10; i++ {
		go func(i int) {
			out := &Test{}
			s.Take(out, fmt.Sprintf("test:%d", i%2), func() (interface{}, error) {
				fmt.Println("do func")
				return &Test{Name: fmt.Sprintf("test %d", i%2)}, nil
			})
			fmt.Println(out)
		}(i)
	}
	var ok bool
	s.Take(&ok, fmt.Sprintf("testbool:%d", 1), func() (interface{}, error) {
		fmt.Println("do func2")
		return true, nil
	})
	fmt.Println(ok)
	s.Take(&ok, fmt.Sprintf("testbool:%d", 1), func() (interface{}, error) {
		fmt.Println("do func2")
		return true, nil
	})
	fmt.Println(ok)
	time.Sleep(time.Second * 5)
}
