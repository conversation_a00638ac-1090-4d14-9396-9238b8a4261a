/*
 * @Description:
 * @Date: 2021-02-03 14:58:14
 * @LastEditors: liang
 * @LastEditTime: 2021-08-23 11:04:38
 */
package easycache

import (
	"encoding/json"

	"gitlab.ttyuyin.com/golang/gudetama/log"
)

type (

	//LockedCallsInterface 更新内存
	LockedCallsInterface interface {
		Do(key string, fn func() error) error
	}

	//通过输入特定的方法 直接获得cache值
	/* 如果A->F B->F在一时刻发生 但A略前 那么B请求F的时候  A与F阻塞时 B也将阻塞 等A->F完成后 更新缓存 B共享A->F缓存结果
	默认加本地缓存100ms过期,可配时间
	要使用加缓存 可以这样构造缓存

	etc.
	s := NewCacheRedis(redisClient, WithCacheRedisLRULocalCache())
	fetch :=func()(interface{},error){
		fmt.Println("rpc(in)")
		return 3,nil
	}
	out := int(0)
	err := s.CacheClient.Take(&out, KeyGetDateDimenIncome+strconv.Itoa(int(in.GetGuildid())), fetch)
	fmt.Println(out,err)
	*/

	CacheInterface interface {
		doGet(key string, value interface{}) (error, bool)
		doSet(key string, value interface{}) bool
		Take(value interface{}, key string, fetch func() (interface{}, error)) error
	}
)

func trySetGoal(v interface{}, out interface{}) bool {
	err := json.Unmarshal(v.([]byte), out)
	if err != nil {
		log.Errorf("%+v,%+v,err", v, out, err)
		return false
	}
	return true
}
