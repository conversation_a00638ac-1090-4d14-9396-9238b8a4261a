package easycache

import (
	"encoding/json"
	"math/rand"
	"time"

	"github.com/go-redis/redis"
	"gitlab.ttyuyin.com/golang/gudetama/log"
)

const defaultTimeOutSec = 1

var (
	//cache interface
	_ CacheInterface = new(CacheRedis)
)

type (
	//配置 目前是redis
	CacheRedisOption func(*CacheRedis)
	CacheRedis       struct {
		redisClient    *redis.Client
		baseExpireTime time.Duration
		r              *rand.Rand
		lockedCalls    LockedCallsInterface
		//本地缓存的提供
		localCache CacheInterface
		//重试次数
		tryCount int
	}
)

func WithNoLocalCacheRedis() CacheRedisOption {
	return func(o *CacheRedis) {
		o.localCache = nil
	}
}

func WithCacheRedis1MonthPeriod() CacheRedisOption {
	return func(o *CacheRedis) {
		o.baseExpireTime = time.Hour * 24 * 30
	}
}

func WithCacheRedisBaseExpireTime(d time.Duration) CacheRedisOption {
	return func(o *CacheRedis) {
		o.baseExpireTime = d
	}
}

func WithCacheRedisTryCount(tryCount int) CacheRedisOption {
	return func(o *CacheRedis) {
		o.tryCount = tryCount
	}
}
func WithCacheRedisPersistencePeriod() CacheRedisOption {
	return func(o *CacheRedis) {
		o.baseExpireTime = 0
	}
}

func WithCacheRedisLRULocalCache(opts ...LRUCacheOpt) CacheRedisOption {
	localCache := NewLRUCache(opts...)
	return func(o *CacheRedis) {
		o.localCache = localCache
		o.lockedCalls = localCache.lockedCalls
	}
}

func (c *CacheRedis) CloneWithOpt(options ...CacheRedisOption) *CacheRedis {
	o := &CacheRedis{
		redisClient:    c.redisClient,
		baseExpireTime: c.baseExpireTime,
		r:              c.r,
		lockedCalls:    c.lockedCalls,
		localCache:     c.localCache,
		tryCount:       c.tryCount,
	}
	for _, opt := range options {
		opt(o)
	}
	return o
}

//默认本身依赖本地缓存
func NewCacheRedis(red *redis.Client, options ...CacheRedisOption) *CacheRedis {
	o := &CacheRedis{
		redisClient:    red,
		baseExpireTime: time.Second * defaultTimeOutSec,
		r:              rand.New(rand.NewSource(time.Now().UnixNano())),
		lockedCalls:    NewLockedCalls(),
		tryCount:       1}
	//默认0.1s
	withOpt := WithCacheRedisLRULocalCache(WithLRUCachebaseExpireMillSec(100))
	withOpt(o)
	for _, opt := range options {
		opt(o)
	}
	return o
}

func (c *CacheRedis) doGet(key string, value interface{}) (error, bool) {
	var data string
	var err error

	retryCount := 0
	//尝试2次
	for retryCount <= c.tryCount {
		data, err = c.redisClient.Get(key).Result()
		if err != nil {
			if err == redis.Nil {
				return nil, false
			}
			log.Errorf("CacheRedis err key %v err:%v", key, err)
			time.Sleep(time.Second * 1)
			retryCount++
		} else {
			break
		}
	}
	if err != nil {
		return err, false
	}

	err = json.Unmarshal([]byte(data), value)
	if err != nil {
		log.Warnf("Unmarshal value in redis")
		return err, false
	}
	//log.Infof("redis get info:%s,%s", key, data)
	return nil, true
}

func (c *CacheRedis) doSet(key string, value interface{}) bool {
	expireTime := time.Duration(c.baseExpireTime)
	if c.baseExpireTime != 0 {
		expireTime = time.Duration((1 + 2*c.r.Float64()) + float64(c.baseExpireTime))
	}
	if _, err := c.redisClient.Set(key, value, time.Duration(expireTime)).Result(); err == nil {
		return true
	} else {
		log.Errorf("redis set %v err:%v", key, err)
		return false
	}
}

func (c *CacheRedis) Take(value interface{}, key string, fetch func() (interface{}, error)) error {
	fnCall := func() error {
		//加localcache
		key := "esyc_" + key
		if c.localCache != nil {
			err, ok := c.localCache.doGet(key, value)
			if err != nil {
				return err
			}
			if ok {
				return nil
			}
		}
		err, ok := c.doGet(key, value)
		if err != nil {
			return err
		}
		if ok {
			return nil
		}
		//加上localcache
		isErr, val := c.tryFetch(fetch, key, value)
		if isErr {
			return val
		}
		return nil
	}
	//共享操作
	return c.lockedCalls.Do(key, fnCall)
}

func (c *CacheRedis) tryFetch(fetch func() (interface{}, error), key string, value interface{}) (bool, error) {
	var err error
	var v interface{}
	var b []byte
	for i := 0; i <= c.tryCount; i++ {
		v, err = fetch()
		if err != nil {
			log.Errorf("ERROR fetch key:%v error:%v", key, err)
			time.Sleep(time.Millisecond * 500)
			continue
		}
		b, err = json.Marshal(v)
		if err != nil {
			log.Errorf("Marshal value:%v err:%v", v, err)
			continue
		}

		if c.localCache != nil {
			c.localCache.doSet(key, b)
		}
		c.doSet(key, string(b))
		json.Unmarshal(b, value)
		return false, nil
	}
	return true, err
}
