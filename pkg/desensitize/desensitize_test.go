package desensitize

import (
	"fmt"
	"testing"
)

func TestDesensitize(t *testing.T) {
	fmt.Println(IdCard(""))
	fmt.Println(IdCard("1"))
	fmt.Println(IdCard("12"))
	fmt.Println(IdCard("123"))
	fmt.Println(IdCard("***********2345678"))
	fmt.Println(IdCard("***********2345678999"))

	fmt.Println(BankCard(""))
	fmt.Println(BankCard("1"))
	fmt.Println(BankCard("1234"))
	fmt.Println(BankCard("***********23456789"))

	fmt.Println(RealName(""))
	fmt.Println(RealName("张"))
	fmt.Println(RealName("张三李"))
	fmt.Println(RealName("张三李四"))

	fmt.Println(PhoneNumber(""))
	fmt.Println(PhoneNumber("1"))
	fmt.Println(PhoneNumber("12"))
	fmt.Println(PhoneNumber("123"))
	fmt.Println(PhoneNumber("***********"))
}
