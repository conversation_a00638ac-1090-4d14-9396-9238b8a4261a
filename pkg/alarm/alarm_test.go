package alarm

import (
	"encoding/json"
	"math/rand"
	"testing"
	"time"
)

func TestJson(t *testing.T) {
	req := ServiceAlarmGroup{
		AlarmTokenConfig: AlarmTokenConfig{
			Url:   "http://testing-yw-alert-proxy-gateway.ttyuyin.com:80",
			Token: AccessTokenReq{"97d9005f-4ec7-4ce5-8e3c-94cd552454fb", "djLqhgNGijBj0qyr"},
		},
		Fallback: "faillback",
		DisableLabel: map[string]struct{}{
			"disxxx": {},
			"aadisxxx": {},
		},
		Dict: map[string]string{
			"abcd": "ddd",
		},
	}
	body, _ := json.Marshal(req)
	t.Logf("body:%s", body)
}

func TestAlarm(t *testing.T) {
	time.Sleep(time.Second * 3)
	rand.Seed(time.Now().Unix())
	//SendAlarm("test_alarm_send_count-vvv6666", "alarm test xxxxxxx id:5555")
	SendWarning("test_warning_send_count-vvv99999", "warning test xxxxxxx id:5555\n xxxx\taaaa\tbbbbb\ncccddd")
	time.Sleep(time.Second * 3)
}
