package log

import "go.uber.org/zap"

type grpcLogger struct {
	zapSugaredLogger *zap.SugaredLogger
}

func newGrpcLogger(zapSugaredLogger *zap.SugaredLogger) *grpcLogger {
	return &grpcLogger{zapSugaredLogger: zapSugaredLogger}
}

// Info logs to INFO log. Arguments are handled in the manner of fmt.Print.
func (l *grpcLogger) Info(args ...interface{}) {
	l.zapSugaredLogger.Info(args...)
}

// Infoln logs to INFO log. Arguments are handled in the manner of fmt.Println.
func (l *grpcLogger) Infoln(args ...interface{}) {
	l.zapSugaredLogger.Info(args...)
}

// Infof logs to INFO log. Arguments are handled in the manner of fmt.Printf.
func (l *grpcLogger) Infof(format string, args ...interface{}) {
	l.zapSugaredLogger.Infof(format, args...)
}

// Warning logs to WARNING log. Arguments are handled in the manner of fmt.Print.
func (l *grpcLogger) Warning(args ...interface{}) {
	l.zapSugaredLogger.Warn(args...)
}

// Warningln logs to WARNING log. Arguments are handled in the manner of fmt.Println.
func (l *grpcLogger) Warningln(args ...interface{}) {
	l.zapSugaredLogger.Warn(args...)
}

// Warningf logs to WARNING log. Arguments are handled in the manner of fmt.Printf.
func (l *grpcLogger) Warningf(format string, args ...interface{}) {
	l.zapSugaredLogger.Warnf(format, args...)
}

// Error logs to ERROR log. Arguments are handled in the manner of fmt.Print.
func (l *grpcLogger) Error(args ...interface{}) {
	l.zapSugaredLogger.Error(args...)
}

// Errorln logs to ERROR log. Arguments are handled in the manner of fmt.Println.
func (l *grpcLogger) Errorln(args ...interface{}) {
	l.zapSugaredLogger.Error(args...)
}

// Errorf logs to ERROR log. Arguments are handled in the manner of fmt.Printf.
func (l *grpcLogger) Errorf(format string, args ...interface{}) {
	l.zapSugaredLogger.Errorf(format, args...)
}

// Fatal logs to ERROR log. Arguments are handled in the manner of fmt.Print.
// gRPC ensures that all Fatal logs will exit with os.Exit(1).
// Implementations may also call os.Exit() with a non-zero exit code.
func (l *grpcLogger) Fatal(args ...interface{}) {
	l.zapSugaredLogger.Fatal(args...)
}

// Fatalln logs to ERROR log. Arguments are handled in the manner of fmt.Println.
// gRPC ensures that all Fatal logs will exit with os.Exit(1).
// Implementations may also call os.Exit() with a non-zero exit code.
func (l *grpcLogger) Fatalln(args ...interface{}) {
	l.zapSugaredLogger.Fatal(args...)
}

// Fatalf logs to ERROR log. Arguments are handled in the manner of fmt.Printf.
// gRPC ensures that all Fatal logs will exit with os.Exit(1).
// Implementations may also call os.Exit() with a non-zero exit code.
func (l *grpcLogger) Fatalf(format string, args ...interface{}) {
	l.zapSugaredLogger.Fatalf(format, args...)
}

// V reports whether verbosity level l is at least the requested verbose level.
func (g *grpcLogger) V(l int) bool {
	return false
}
