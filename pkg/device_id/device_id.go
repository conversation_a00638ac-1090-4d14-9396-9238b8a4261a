package device_id

import (
	"encoding/hex"
	"github.com/google/uuid"
	"strings"
)

func parseHexDeviceId(b64Device string) []byte {
	device, err := hex.DecodeString(b64Device)
	if err != nil {
		return nil
	}

	return device
}

func parseUUIDDeviceId(UUIDDevice string) []byte {
	if deviceUUID, perr := uuid.Parse(UUIDDevice); perr == nil {
		if b, berr := deviceUUID.MarshalBinary(); berr == nil {
			return b
		}
	}

	return nil
}

func ParseStringDeviceId(device string) []byte {
	if len(device) == 0 {
		return nil
	}

	if strings.Contains(device, "-") {
		return parseUUIDDeviceId(device)
	} else {
		return parseHexDeviceId(device)
	}
}

func ToDeviceHexId(id []byte, upper bool) string {
	device := hex.EncodeToString(id)
	if upper {
		return strings.ToUpper(device)
	}
	return device
}
