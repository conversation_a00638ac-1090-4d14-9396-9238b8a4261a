package success_fail_report

import (
	"context"
	"time"
)

var reportObj *reporter

func init() {
	reportObj = newReporter()
	reportObj.Start(context.Background(), time.Second*1)
}

func SuccFailReport(ID string, succ bool) {
	now := time.Now()
	reportObj.ReportRow(ID, now.UnixNano(), now.Unix(), succ)
}
func StopReporters() {
	reportObj.Stop()
}

func SuccFailReportByErr(ID string, err error) {
	if nil != err {
		SuccFailReport(ID, false)
	} else {
		SuccFailReport(ID, true)
	}
}