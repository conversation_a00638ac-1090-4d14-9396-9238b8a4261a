package channel_base

import (
	"os"
	"path/filepath"
)

var CallerServerName = "unknown"

func init() {
	if len(os.Args) > 0 {
		CallerServerName = filepath.Base(os.Args[0])
	}
}

// type ChannelBase struct {
// 	PermissionChecker IChannelPermissionChecker
// }

// func NewChannelBaseApi(opts ...grpc.DialOption) *ChannelBase {
// 	permissionChecker := NewChannelPermissionChecker(opts...)

// 	return &ChannelBase{
// 		PermissionChecker: permissionChecker,
// 	}
// }
