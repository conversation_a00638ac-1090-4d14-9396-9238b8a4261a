package kafka

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"golang.52tt.com/pkg/robot/model"

	"golang.52tt.com/pkg/log"

	"github.com/Shopify/sarama"
)

type kafkaProducer struct {
	producer sarama.AsyncProducer
	topic    string
	wg       sync.WaitGroup
}

func New(addrs []string, topic string) (*kafkaProducer, error) {
	conf := sarama.NewConfig()
	conf.Producer.RequiredAcks = sarama.WaitForAll
	conf.Producer.Return.Successes = true
	conf.Producer.Return.Errors = true
	conf.ChannelBufferSize = 2048

	p, err := sarama.NewAsyncProducer(addrs, conf)
	if err != nil {
		return nil, err
	}

	c := &kafkaProducer{
		producer: p,
		topic:    topic,
	}
	c.handleReturns()
	return c, nil
}

func (c *kafkaProducer) Send(ctx context.Context, data *model.EventData) error {
	b, err := json.<PERSON>(data)
	if err != nil {
		return err
	}

	msg := &sarama.ProducerMessage{
		Topic: c.topic,
		Key:   sarama.StringEncoder(data.BizId),
		Value: sarama.ByteEncoder(b),
	}

	c.producer.Input() <- msg
	return nil
}

func (c *kafkaProducer) Close(ctx context.Context) error {
	log.Infoln("Closing")
	c.producer.AsyncClose()
	c.wg.Wait()
	log.Infoln("Closed")
	return nil
}

func (c *kafkaProducer) handleReturns() {
	c.wg.Add(2)

	go func() {
		defer c.wg.Done()

		for succ := range c.producer.Successes() {
			fmt.Printf("sent message %s %s::%d::%d\n", succ.Key, c.topic, succ.Partition, succ.Offset)
		}
	}()

	go func() {
		defer c.wg.Done()

		for err := range c.producer.Errors() {
			fmt.Printf("send message %v: %v\n", err.Msg, err.Err)
		}
	}()
}
