package robot

import (
	"context"
	"encoding/json"
	"io/ioutil"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/robot/kafka"
	"golang.52tt.com/pkg/robot/redis"

	"golang.52tt.com/pkg/robot/empty"

	"golang.52tt.com/pkg/config"
)

type ProducerType string

const (
	ProducerTypeKafka ProducerType = "kafka"
	ProducerTypeRedis ProducerType = "redis"
	ProducerTypeNone  ProducerType = "none"
)

type Conf struct {
	ProducerType ProducerType        `json:"producer_type"`
	KafkaConfig  *config.KafkaConfig `json:"kafka"`
	RedisConfig  *config.RedisConfig `json:"redis"`
}

func (c *Conf) GetProducerType() ProducerType {
	if c != nil {
		return c.ProducerType
	}
	return ""
}
func (c *Conf) GetKafkaConfig() *config.KafkaConfig {
	if c != nil {
		return c.KafkaConfig
	}
	return nil
}
func (c *Conf) GetRedisConfig() *config.RedisConfig {
	if c != nil {
		return c.RedisConfig
	}
	return nil
}

const ConfigFile = "/data/oss/conf-center/tt/common_robot.json"

var (
	p producer
)

func Start(ctx context.Context) error {
	data, err := ioutil.ReadFile(ConfigFile)
	if err != nil {
		return err
	}

	var c Conf
	err = json.Unmarshal(data, &c)
	if err != nil {
		return err
	}

	switch c.GetProducerType() {
	case ProducerTypeKafka:
		p, err = kafka.New(c.GetKafkaConfig().BrokerList(), c.GetKafkaConfig().Topics)
		if err != nil {
			log.Errorf("Failed to start err:%+v", err)
			return err
		}
	case ProducerTypeRedis:
		p, err = redis.New(c.GetRedisConfig())
		if err != nil {
			log.Errorf("Failed to start err:%+v", err)
			return err
		}
	case ProducerTypeNone:
		p = empty.NewEmptyProducer()
	default:
		p = empty.NewEmptyProducer()
	}
	return nil
}

func Close(ctx context.Context) error {
	return p.Close(ctx)
}
