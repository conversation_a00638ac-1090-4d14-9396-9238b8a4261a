package client

import (
	"context"
	"golang.52tt.com/pkg/sentinel/template/last_succeed"
	"math/rand"
	"strconv"
	"strings"
	"sync"
	"testing"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/sentinel"
	"golang.52tt.com/pkg/sentinel/interceptor"
	"golang.52tt.com/pkg/sentinel/interceptor/example/mail"
	"google.golang.org/grpc"
)

var client *mail.Client
var server *mail.Server

func init() {
	log.SetLevel(log.DebugLevel)

	ctx := context.Background()

	var err error

	go func() {
		// 初始化客户端和拦截器
		err = sentinel.Init(ctx, "mail", "sentinel.yaml")
		if err != nil {
			log.Errorf("Init fail: %v", err)
			return
		}

		theLast := last_succeed.LastSucceedIntercepter{}
		theLast.Init(func(ctx *interceptor.Context) string {
			return ctx.Method
		}, []last_succeed.Option{
			last_succeed.WithMaxWeight(10),
			last_succeed.WithExpireSeconds(100),
			last_succeed.WithRiseFactor(1),
		}...)

		interceptor.Register("/mail.Mail/Send", &interceptor.Rule{
			Name:     "Send",
			Callback: theLast.Callback,
			Fallback: theLast.Fallback,
		})

		// 异步初始化服务端
		server = &mail.Server{}
		err = server.Serve(ctx, 8222, grpc.UnaryInterceptor(interceptor.UnaryServerInterceptor()))
		if err != nil {
			log.Errorf("service Serve fail: %v", err)
			return
		}
	}()

	client, err = mail.NewClient(
		8222,
		grpc.WithInsecure(),
		grpc.WithBlock(),
	)
	if err != nil {
		log.Errorf("New client fail: %v", err)
		return
	}
}

func roundInPeriod(coroutine int, duration time.Duration, do func()) {
	sw := sync.WaitGroup{}
	stop := make(chan interface{})

	if 0 == coroutine {
		coroutine = 1
	}

	if 0 == duration {
		duration = time.Minute
	}

	sw.Add(coroutine)
	for i := 0; i < coroutine; i++ {
		go func() {
			for {
				select {
				case <-stop:
					sw.Done()
					return
				default:
				}
				do()
			}
		}()
	}
	<-time.After(duration)
	close(stop)
	sw.Wait()
}

func TestMailUnaryClientInterceptorByFailRatio(t *testing.T) {
	_, _, err := client.Test(context.Background(), 0, 0.5)
	if err != nil {
		log.Errorf("Test fail, err: %v", err)
		return
	}
	defer func() {
		_, _, _ = client.Test(context.Background(), 0, 0)
	}()

	roundInPeriod(1, time.Minute, func() {
		ctx, cancel := context.WithTimeout(context.Background(), time.Second)

		from := "jack" + "_" + strconv.Itoa(rand.Intn(1000))

		result, err := client.Send(ctx, from, "rose", "good morning")
		cancel()
		if err != nil {
			// log.Errorf("Send fail, err: %v", err)
		} else {
			if !strings.HasPrefix(result, from) {
				log.Debugf("hit fall back, from: %v, got: %v", from, result)
			}
		}

		time.Sleep(time.Millisecond * 300)
	})
}
