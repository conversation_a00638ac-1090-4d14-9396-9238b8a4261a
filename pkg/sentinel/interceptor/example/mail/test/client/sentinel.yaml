rules:
  - resource: "Send"          # 规则名称
    strategy: 0               # 熔断判断策略，0：慢速请求比例，1：出错请求比例，2：出错请求数量
    retry_timeout_ms: 1000    # 资源进入熔断状态后，在配置的熔断时长内，请求都会快速失败。熔断结束后进入探测恢复模式（HALF-OPEN）
    min_request_amount: 5     # 触发熔断的最小请求数目，若当前统计周期内的请求数小于此值，即使达到熔断条件规则也不会触发
    stat_interval_ms: 3000    # 统计窗口长度
    max_allowed_rt_ms: 10     # 慢调用临界值，慢速请求比例策略判断条件
    threshold: 0.3            # 熔断阈值
    disable: false