package client

import (
	"context"
	"sync"
	"testing"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/sentinel"
	"golang.52tt.com/pkg/sentinel/interceptor"
	"golang.52tt.com/pkg/sentinel/interceptor/example/mail"
	pb "golang.52tt.com/pkg/sentinel/interceptor/example/mail/proto"
	"google.golang.org/grpc"
)

var client *mail.Client
var server *mail.Server

func init() {
	log.SetLevel(log.DebugLevel)

	ctx := context.Background()

	var err error

	go func() {
		// 异步初始化服务端
		server = &mail.Server{}
		err = server.Serve(ctx, 8222)
		if err != nil {
			log.Errorf("service Serve fail: %v", err)
			return
		}
	}()

	// 初始化客户端和拦截器
	err = sentinel.Init(ctx, "mail", "sentinel.yaml")
	if err != nil {
		log.Errorf("Init fail: %v", err)
		return
	}
	interceptor.Register("/mail.Mail/Send", &interceptor.Rule{
		Name:     "Send",
		Fallback: sendFallback,
	})

	client, err = mail.NewClient(
		8222,
		grpc.WithInsecure(),
		grpc.WithBlock(),
		grpc.WithUnaryInterceptor(interceptor.UnaryClientInterceptor()))
	if err != nil {
		log.Errorf("New client fail: %v", err)
		return
	}
}

func roundInPeriod(coroutine int, duration time.Duration, do func()) {
	sw := sync.WaitGroup{}
	stop := make(chan interface{})

	if 0 == coroutine {
		coroutine = 1
	}

	if 0 == duration {
		duration = time.Minute
	}

	sw.Add(coroutine)
	for i := 0; i < coroutine; i++ {
		go func() {
			for {
				select {
				case <-stop:
					sw.Done()
					return
				default:
				}
				do()
			}
		}()
	}
	<-time.After(duration)
	close(stop)
	sw.Wait()
}

func sendFallback(ctx *interceptor.Context, err error) error {
	log.Infof("sendFallback >> method:%s, err:%v", ctx.Method, err)

	ctx.Reply = &pb.SendResp{
		Msg: "try again, " + ctx.Req.(*pb.SendReq).SendFrom,
	}
	return nil
}

func TestMailUnaryClientInterceptorByFailRatio(t *testing.T) {
	_, _, err := client.Test(context.Background(), 0, 0.5)
	if err != nil {
		log.Errorf("Test fail, err: %v", err)
		return
	}
	defer func() {
		_, _, _ = client.Test(context.Background(), 0, 0)
	}()

	roundInPeriod(1, time.Minute, func() {
		ctx, cancel := context.WithTimeout(context.Background(), time.Second)
		_, err := client.Send(ctx, "jack", "rose", "good morning")
		cancel()
		if err != nil {
			// log.Errorf("Send fail, err: %v", err)
		}
		time.Sleep(time.Millisecond * 300)
	})
}

func TestMailUnaryClientInterceptorBySlowRatio(t *testing.T) {
	_, _, err := client.Test(context.Background(), 50, 0.5)
	if err != nil {
		log.Errorf("Test fail, err: %v", err)
		return
	}

	defer func() {
		_, _, _ = client.Test(context.Background(), 0, 0)
	}()

	roundInPeriod(1, time.Minute, func() {
		ctx, cancel := context.WithTimeout(context.Background(), time.Second)
		_, err := client.Send(ctx, "jack", "rose", "good morning")
		cancel()
		if err != nil {
			// log.Errorf("Send fail, err: %v", err)
		}
		time.Sleep(time.Millisecond * 300)
	})
}
