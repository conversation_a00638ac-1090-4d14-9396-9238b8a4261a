package sentinel

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"math/rand"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

func TestBreaker(t *testing.T) {
	log.SetLevel(log.DebugLevel)

	Init(context.Background(), "test", "starter_test.yaml")

	start := time.Now()
	duration := time.Second * 20

	for {
		if time.Since(start) > duration {
			return
		}

		Wrap("tt", func() (interface{}, error) {
			interval := (150 + time.Duration(rand.Intn(200))) * time.Millisecond

			time.Sleep(interval)
			return nil, nil
		})
	}
}

func TestFlow(t *testing.T) {
	log.SetLevel(log.DebugLevel)

	Init(context.Background(), "test", "starter_test.yaml")

	start := time.Now()
	duration := time.Second * 20

	workers := 3
	sw := sync.WaitGroup{}
	sw.Add(workers)

	var count int32

	for i := 0; i < workers; i++ {
		go func() {
			for {
				if time.Since(start) > duration {
					sw.Done()
					return
				}

				_, err := Wrap("tt", func() (interface{}, error) {
					time.Sleep(time.Second)
					return nil, nil
				})
				if nil == err {
					atomic.AddInt32(&count, 1)
				}
			}
		}()
	}

	sw.Wait()

	fmt.Println(atomic.LoadInt32(&count))
}
