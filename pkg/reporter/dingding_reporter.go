package reporter

import (
	"errors"
	"fmt"
	"net/url"
	"time"
)

type DingDingReporter struct {
	url    string
	secret string
}

func NewDingDingReporter(url, secret string) *DingDingReporter {
	return &DingDingReporter{
		url:    url,
		secret: secret,
	}
}

func (s *DingDingReporter) Send(title, text string) (err error) {
	timestamp := time.Now().UnixNano() / 1000 / 1000

	sign, err := genSign(s.secret, timestamp)
	if err != nil {
		return err
	}

	body := fmt.Sprintf(`{"msgtype":"markdown","markdown":{"title":"%s","text": "%s"},"at":{"atMobiles": [],"isAtAll": false}}`, title, text)
	link := fmt.Sprintf("%s&timestamp=%d&sign=%s", s.url, timestamp, url.QueryEscape(sign))

	info := make(map[string]interface{})
	err = post(link, body, &info)
	if err != nil {
		return
	}

	errMsg, ok := info["errmsg"]
	if !ok {
		err = errors.New("response not correct")
		return
	}

	if "ok" == errMsg {
		return
	}

	err = errors.New(errMsg.(string))
	return
}