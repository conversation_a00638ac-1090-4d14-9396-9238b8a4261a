package event

import (
	prom "github.com/prometheus/client_golang/prometheus"
	"strings"
)

var (
	defaultKafkaMetrics = newKafkaMetrics()
)

func init() {
	prom.MustRegister(defaultKafkaMetrics.consumerCost)
}

type ServerMetrics struct {
	consumerCost *prom.GaugeVec
}

func newKafkaMetrics() *ServerMetrics {
	return &ServerMetrics{
		consumerCost: prom.NewGaugeVec(
			prom.GaugeOpts{
				Name: "kafka_topic_partitionId_consumer_group_cost",
				Help: "Cost of kafka consumer group per partition.",
			}, []string{"report_topic", "topicPartId", "consumer_group"}),
	}
}
func RecordCost(topic, topicPartId, consumer_group string, cost float64) {
	defaultKafkaMetrics.consumerCost.WithLabelValues(topic, topicPartId, consumer_group).Set(cost)
}

func DelRecordCost(labelList []string) {
	for _, label := range labelList {
		labels := strings.Split(label, placeholder)
		defaultKafkaMetrics.consumerCost.DeleteLabelValues(labels...)
	}
}
