package settlement

import (
	"fmt"
	pb "golang.52tt.com/protocol/services/settlement-bill"
	"testing"
	"time"
)

func TestSettleMoneyCal(t *testing.T) {
	actualIncomePay, taxCompensationRate, compensationPoint :=
		SettleMoneyCal(6, 1459319, 0, 0)
	fmt.Println(uint64(actualIncomePay), taxCompensationRate, compensationPoint)
}

func TestAllowWithdraw(t *testing.T) {
	tt := time.Date(2022, 5, 31, 0, 0, 0, 0, time.Local)

	fmt.Println(AllowWithdraw(123, pb.SettlementBillType_GiftScore, tt, true))
	fmt.Println(AllowWithdraw(123, pb.SettlementBillType_GiftScore, tt, false))
	fmt.Println(AllowWithdraw(13908026, pb.SettlementBillType_GiftScore, tt, true))
	fmt.Println(AllowWithdraw(13908026, pb.SettlementBillType_GiftScore, tt, false))

	fmt.Println(AllowWithdraw(0, pb.SettlementBillType_GiftScore, tt, false))

	tt1 := time.Date(2022, 6, 1, 15, 0, 0, 0, time.Local)
	fmt.Println(AllowWithdraw(0, pb.SettlementBillType_DeepCoop, tt1, false))

	fmt.Println(AllowWithdraw(0, pb.SettlementBillType_AmuseCommission, time.Now(), false))
	fmt.Println(AllowWithdraw(0, pb.SettlementBillType_YuyinBaseCommission, time.Now(), false))
	fmt.Println(AllowWithdraw(0, pb.SettlementBillType_GiftScore, time.Now(), false))
}

func TestGetConfigCenter(t *testing.T) {
	fmt.Println(GetConfigCenter().GetReportEmailTo())
	fmt.Println(GetConfigCenter().GetSpecialWithdrawRange(pb.SettlementBillType_GiftScore))
}

func TestGetCurrentSettleCycleTime(t *testing.T) {
	fmt.Println(GetCurrentSettleCycleTime(7, time.Now()))
	fmt.Println(GetCurrentSettleCycleTime(6, time.Now()))
	fmt.Println(GetCurrentSettleCycleTime(3, time.Now()))
	fmt.Println(GetCurrentSettleCycleTime(1, time.Now()))
}

func TestGetLastSettleCycleTime(t *testing.T) {
	// 2024-04-05
	tt := time.Date(2024, 4, 15, 0, 0, 0, 0, time.Local)
	fmt.Println(GetLastSettleCycleTime(9, tt))
	fmt.Println(GetLastSettleCycleTime(7, time.Now()))
	fmt.Println(GetLastSettleCycleTime(6, time.Now()))
	fmt.Println(GetLastSettleCycleTime(3, time.Now()))
	fmt.Println(GetLastSettleCycleTime(1, time.Now()))
}

func TestGetSettleCycle(t *testing.T) {
	fmt.Println(GetSettleCycle(8))
	fmt.Println(GetSettleCycle(7))
	fmt.Println(GetSettleCycle(6))
	fmt.Println(GetSettleCycle(5))
	fmt.Println(GetSettleCycle(4))
	fmt.Println(GetSettleCycle(3))
	fmt.Println(GetSettleCycle(2))
	fmt.Println(GetSettleCycle(1))
}
