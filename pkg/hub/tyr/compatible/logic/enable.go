package logic

import (
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/foundation/grpc/logic/filter"
	_ "golang.52tt.com/pkg/hub/tyr/compatible/base"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	hubLogic "gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/logic"
)

func init() {
	log.Infof("logic start with hub compatible")

	// 防止 tyr 框架生成代码没有主动添加拦截器
	hubLogic.AddDefaultUnaryServerInterceptor(filter.FilterInterceptor())
}
