package client

import (
	"fmt"
	"os"
	"sync"

	"google.golang.org/grpc"

	_ "google.golang.org/grpc/balancer/grpclb" // To enable `gRPCLB`
	//_ "google.golang.org/grpc/xds/experimental"
)

var (
	invalidConn     *grpc.ClientConn
	invalidConnOnce sync.Once
)

const (
	defaultSC = `{"loadBalancingConfig": [{"grpclb":{}}],"loadBalancingPolicy":"grpclb"}`
)

func invalidGRPCConn() *grpc.ClientConn {
	invalidConnOnce.Do(func() {
		invalidConn, _ = grpc.Dial(fmt.Sprintf("unix:///var/run/mock-grpc-%d", os.Getpid()), grpc.WithInsecure(), grpc.WithDisableRetry())
	})
	return invalidConn
}

type gRPCClient struct {
	BaseClient
}

type gRPCStubCreator func(cc *grpc.ClientConn) interface{}

// NewInsecureGRPCClient instantiate an insecure gRPC client with the given name and stub creator.
func NewInsecureGRPCClient(name string, ctor gRPCStubCreator, dopts ...grpc.DialOption) BaseClient {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithDisableServiceConfig(), grpc.WithDefaultServiceConfig(defaultSC))
	return newGRPCClient(name, ctor, dopts...)
}

func NewSecureGRPCClient(name string, ctor gRPCStubCreator, dopts ...grpc.DialOption) BaseClient {
	dopts = append(dopts, grpc.WithDisableServiceConfig(), grpc.WithDefaultServiceConfig(defaultSC))
	return newGRPCClient(name, ctor, dopts...)
}

// newGRPCClient initializes a new gRPCClient
func newGRPCClient(name string, ctor gRPCStubCreator, dopts ...grpc.DialOption) BaseClient {
	//dopts = append(dopts, grpc.WithBlock())
	return &gRPCClient{
		BaseClient: newClient(name, func(target string) (Conn, interface{}) {
			cc, err := grpc.Dial(target, dopts...)
			if err != nil {
				// happens when grpc.WithBlock() is specified
				cc = invalidGRPCConn()
			}

			return cc, ctor(cc)
		}),
	}
}
