package conf

import (
	"context"
	"strconv"
	"time"

	"gopkg.in/yaml.v2"

	"golang.52tt.com/pkg/dyconfig"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
)

type marketIdType int32
type cmdType uint32

// type sceneCodeType string

const (
	StrictAuditForAllScene = "all"
)

func NewSatoshiConf(ctx context.Context) (ISatoshiConf, error) {
	dc, err := dyconfig.NewDynamicConfig(ctx, "/data/oss/conf-center/tt/satoshi.yaml", &SatoshiConf{})
	if nil != err {
		log.ErrorWithCtx(ctx, "new dy cfg failed, err: %v", err)
		return nil, err
	}

	return &satoshiConf{
		dc: dc,
	}, nil
}

type SatoshiConf struct {
	Enable        bool   `yaml:"enable"`
	BeginTime     string `yaml:"beginTime"`
	EndTime       string `yaml:"endTime"`
	EndTimeLayout string `yaml:"endTimeLayout"`
	beginTimeTs   int64
	endTimeTs     int64
	viewEndTime   string

	TestMode TestMode `yaml:"testMode"`

	StrictAuditList []StrictAudit `yaml:"strictAuditList"`

	BanCmdConf         BanCmdConf         `yaml:"banCmdConf"`
	SensitiveAreaConf  SensitiveAreaConf  `yaml:"sensitiveAreaConf"`
	WhiteUserAuthConf  WhiteUserAuthConf  `yaml:"whiteUserAuthConf"`
	SilentUserAuthConf SilentUserAuthConf `yaml:"silentUserAuthConf"`

	ContentLimit    map[int32][]string `yaml:"contentLimit"` // key:market_id,-1代表所有 value:cmd
	contentLimitMap map[int32]map[string]bool

	Interceptor Interceptor `yaml:"interceptor"`
}

type TestMode struct {
	Enable       bool     `yaml:"enable"`
	UidList      []uint32 `yaml:"uidList"`
	DeviceIdList []string `yaml:"deviceIdList"`
}

type WhiteMarket struct {
	CmdList  []string `yaml:"cmdList"`
	MarketId uint32   `yaml:"marketId"`
}

type BanCmdConf struct {
	CmdList         []string      `yaml:"cmdList"`
	WhiteMarketList []WhiteMarket `yaml:"whiteMarketList"`
}

type StrictAudit struct {
	MarketId      uint32   `yaml:"marketId"`
	SceneCodeList []string `yaml:"sceneCodeList"`
}

type SensitiveAreaConf struct {
	Enable                  bool     `yaml:"enable"`
	BenForeign              bool     `yaml:"benForeign"`
	BanProvinces            []string `yaml:"banProvinces"`
	WhiteUidList            []uint32 `yaml:"whiteUidList"`
	WhiteMarketIdList       []uint32 `yaml:"whiteMarketIdList"`
	EnableSuperviseTypeList []string `yaml:"enableSuperviseTypeList"`
}

type SilentUserAuthConf struct {
	Enable            bool     `yaml:"enable"`
	SilentSeconds     int64    `yaml:"silentSeconds"`
	WhiteMarketIdList []uint32 `yaml:"whiteMarketIdList"`
}

type WhiteUserAuthConf struct {
	MinConsume               uint32            `yaml:"minConsume"`
	MinCharm                 uint32            `yaml:"minCharm"`
	MinNobilityLevel         uint32            `yaml:"minNobilityLevel"`
	ConsumeAuthDeviceVersion map[uint32]uint32 `yaml:"consumeAuthDeviceVersion"`
}

type Interceptor struct {
	IgnoreCmdList []string `yaml:"ignoreCmdList"`
}

type ISatoshiConf interface {
	GetSatoshiConf() *SatoshiConf
}

type satoshiConf struct {
	dc *dyconfig.DynamicConfig
}

func (s *satoshiConf) GetSatoshiConf() *SatoshiConf {
	return s.dc.Get().(*SatoshiConf)
}

func (s *SatoshiConf) UnmarshalBinary(data []byte) (err error) {
	err = yaml.Unmarshal(data, s)
	if err != nil {
		log.Errorf("yaml.Unmarshal failed, err:%v, %s", err, string(data))
		return
	}

	beginTime, err := time.ParseInLocation(timeLayout, s.BeginTime, time.Local)
	if err != nil {
		log.Errorf("time.ParseInLocation failed, %s, err:%v", s.BeginTime, err)
		return
	}
	s.beginTimeTs = beginTime.Unix()

	endTime, err := time.ParseInLocation(timeLayout, s.EndTime, time.Local)
	if err != nil {
		log.Errorf("time.ParseInLocation failed, %s, err:%v", s.EndTime, err)
		return
	}
	s.endTimeTs = endTime.Unix()

	if s.EndTimeLayout == "" {
		s.EndTimeLayout = timeLayout
	}
	s.viewEndTime = endTime.Local().Format(s.EndTimeLayout)

	s.contentLimitMap = make(map[int32]map[string]bool, len(s.ContentLimit))

	for marketId, cmds := range s.ContentLimit {
		m := make(map[string]bool, len(cmds))
		for _, cmd := range cmds {
			m[cmd] = true
		}

		s.contentLimitMap[marketId] = m
	}

	s.WhiteUserAuthConf.CheckDefault()
	s.SilentUserAuthConf.CheckDefault()

	log.Infof("UnmarshalBinary config updated: %+v", s)

	return
}

func (s *SatoshiConf) GetViewEndTime() string {
	return s.viewEndTime
}

func (s *SatoshiConf) IsCheckEnable(ctx context.Context, uid uint32, deviceId string) bool {
	testMode := s.TestMode
	if !testMode.Enable {
		log.DebugWithCtx(ctx, "testMode not enable, uid:%d, deviceId:%s", uid, deviceId)
		return true
	}
	log.InfoWithCtx(ctx, "testMode enable, to check uid:%d, deviceId:%s", uid, deviceId)

	for _, testUid := range testMode.UidList {
		if uid == testUid {
			log.InfoWithCtx(ctx, "IsSupervisionEnable testMode, hit uid:%d", uid)
			return true
		}
	}

	for _, testDeviceId := range testMode.DeviceIdList {
		if deviceId == testDeviceId {
			log.InfoWithCtx(ctx, "IsSupervisionEnable testMode, hit deviceId:%s", deviceId)
			return true
		}
	}
	log.DebugWithCtx(ctx, "IsSupervisionEnable testMode no hit uid:%d, deviceId:%s", uid, deviceId)
	return false
}

func (s *SatoshiConf) IsSupervisionEnable(ctx context.Context, uid uint32, deviceId string) bool {
	if !s.Enable {
		log.DebugWithCtx(ctx, "IsSupervisionEnable disable, uid:%d, deviceId:%s", uid, deviceId)
		return false
	}

	if !s.IsCheckEnable(ctx, uid, deviceId) {
		return false
	}

	nowTs := time.Now().Unix()
	isInPeriod := nowTs >= s.beginTimeTs && nowTs < s.endTimeTs

	log.DebugWithCtx(ctx, "IsSupervisionEnable, isInPeriod:%t, now:%d, begin:%d, end:%d",
		isInPeriod, nowTs, s.beginTimeTs, s.endTimeTs)

	return isInPeriod
}

func (s *SatoshiConf) IsSensitiveAreaWhiteUid(ctx context.Context, uid uint32) bool {
	if uid == 0 {
		log.DebugWithCtx(ctx, "IsSensitiveAreaWhiteUid, ignore empty uid")
		return false
	}

	for _, whiteUid := range s.SensitiveAreaConf.WhiteUidList {
		if uid == whiteUid {
			log.InfoWithCtx(ctx, "hit sensitiveAreaWhiteUidList, uid:%d", uid)
			return true
		}
	}
	return false
}

func (s *SatoshiConf) IsSensitiveAreaWhiteMarketId(ctx context.Context, marketId uint32) bool {
	for _, whiteMarketId := range s.SensitiveAreaConf.WhiteMarketIdList {
		if marketId == whiteMarketId {
			log.InfoWithCtx(ctx, "hit IsSensitiveAreaWhiteMarketId, marketId:%d", marketId)
			return true
		}
	}
	return false
}

func (s *SatoshiConf) IsInterceptorIgnoreCmd(ctx context.Context, requestPath string, cmdId uint32) bool {
	strCmd := strconv.FormatUint(uint64(cmdId), 10)
	for _, ignoreCmdId := range s.Interceptor.IgnoreCmdList {
		if requestPath == ignoreCmdId || strCmd == ignoreCmdId {
			log.DebugWithCtx(ctx, "IsInterceptorIgnoreCmd, hit cmdId:%s", ignoreCmdId)
			return true
		}
	}
	return false
}

func (s *SatoshiConf) IsBanCmd(ctx context.Context, requestPath string, cmdId uint32) bool {
	strCmd := strconv.FormatUint(uint64(cmdId), 10)

	for _, banCmdId := range s.BanCmdConf.CmdList {
		if strCmd == banCmdId || requestPath == banCmdId {
			log.DebugWithCtx(ctx, "IsBanCmd, hit cmdId:%s", banCmdId)
			return true
		}
	}
	return false
}

func (s *SatoshiConf) IsWhiteMarketCmd(ctx context.Context, marketId uint32, requestPath string, cmdId uint32) bool {
	strCmd := strconv.FormatUint(uint64(cmdId), 10)

	for _, market := range s.BanCmdConf.WhiteMarketList {
		whiteMarketId := market.MarketId
		if marketId != whiteMarketId {
			continue
		}

		for _, whiteCmdId := range market.CmdList {
			if strCmd == whiteCmdId || requestPath == whiteCmdId {
				log.DebugWithCtx(ctx, "IsWhiteMarketCmd, hit cmd:%s, marketId:%d", whiteCmdId, marketId)
				return true
			}
		}
		log.DebugWithCtx(ctx, "IsWhiteMarketCmd, hit marketId:%d", marketId)

	}
	return false
}

func (s *SatoshiConf) IsSuperviseTypeSensitiveAreaEnable(ctx context.Context, superviseType string) bool {
	for _, enableSuperviseType := range s.SensitiveAreaConf.EnableSuperviseTypeList {
		if superviseType == enableSuperviseType {
			log.DebugWithCtx(ctx, "IsSuperviseTypeSensitiveAreaEnable %s", superviseType)
			return true
		}
	}
	log.DebugWithCtx(ctx, "IsSuperviseTypeSensitiveAreaEnable false %s", superviseType)
	return false
}

func (s *SatoshiConf) IsBanIpLocation(ctx context.Context, country, province string) bool {
	if !s.SensitiveAreaConf.Enable {
		return false
	}

	if country != "" && country != "中国" && s.SensitiveAreaConf.BenForeign {
		return true
	}

	for _, banProvince := range s.SensitiveAreaConf.BanProvinces {
		if province == banProvince {
			return true
		}
	}
	return false
}

func (s *SatoshiConf) IsStrictAuditScene(ctx context.Context, marketId uint32, sceneCode string) bool {
	for _, strictAudit := range s.StrictAuditList {
		strictMarketId := strictAudit.MarketId
		if marketId != strictMarketId {
			continue
		}

		for _, strictSceneCode := range strictAudit.SceneCodeList {
			if sceneCode == strictSceneCode {
				log.DebugWithCtx(ctx, "IsStrictAuditScene, hit sceneCode:%s, marketId:%d", sceneCode, marketId)
				return true
			}
		}
		log.DebugWithCtx(ctx, "IsStrictAuditScene, hit marketId:%d", marketId)

	}
	return false
}

func (s *SatoshiConf) IsLimit(ctx context.Context, marketId uint32, cmd uint32, requestPath string) bool {
	strCmd := strconv.FormatUint(uint64(cmd), 10)
	m := s.contentLimitMap[-1]
	if m[strCmd] || m[requestPath] {
		log.DebugWithCtx(ctx, "IsLimit cmd:%s, requestPath:%s", strCmd, requestPath)
		return true
	}

	m = s.contentLimitMap[int32(marketId)]
	if m[strCmd] || m[requestPath] {
		log.DebugWithCtx(ctx, "IsLimit marketId:%d, cmd:%s, requestPath:%s", marketId, strCmd, requestPath)
		return true
	}
	return false
}

func (s *SilentUserAuthConf) CheckDefault() {
	if s.SilentSeconds == 0 {
		s.SilentSeconds = 86400 * 180
	}
}

func (s *WhiteUserAuthConf) CheckDefault() {
	if s.MinConsume == 0 {
		s.MinConsume = 10 * 10000
	}

	if s.MinCharm == 0 {
		s.MinCharm = 100 * 10000
	}

	if s.MinNobilityLevel == 0 {
		s.MinNobilityLevel = 1
	}

	if len(s.ConsumeAuthDeviceVersion) == 0 {
		s.ConsumeAuthDeviceVersion = map[uint32]uint32{
			protocol.ClientTypeANDROID: protocol.FormatClientVersion(uint8(5), uint8(6), uint16(0)),            //ANDROID_TT::V5_6_0
			protocol.ClientTypeIOS:     protocol.FormatClientVersion(uint8(5), uint8(6), uint16(0)),            //IPHONE_TT::V5_6_0
			protocol.ClientTypeCAR:     protocol.FormatClientVersion(uint8(0xff), uint8(0xff), uint16(0xffff)), //CAR_TT::UNAVAILABLE
			protocol.ClientTypePcTT:    protocol.FormatClientVersion(uint8(5), uint8(6), uint16(0)),            //PC_TT::V1_6_3
			protocol.ClientTypeTX_MINI: protocol.FormatClientVersion(uint8(0), uint8(0), uint16(1)),            //TX_MINI_TT::V0_0_1
		}
	}
}

// 沉默时间
func (s *SilentUserAuthConf) IsSlientUser(ctx context.Context, uid, marketId uint32, lastLoginAt int64) bool {
	if !s.Enable {
		log.DebugWithCtx(ctx, "IsSilentTime, not enable, uid:%d, marketId:%d, lastLoginAt=0", uid, marketId)
		return false
	}

	now := time.Now().Unix()
	if lastLoginAt == 0 {
		log.DebugWithCtx(ctx, "IsSilentTime, uid:%d, marketId:%d, lastLoginAt=0", uid, marketId)
		return false
	}

	if now-lastLoginAt < s.SilentSeconds {
		log.DebugWithCtx(ctx, "not slient user, uid:%d, marketId:%d, now:%d - lastLoginAt:%d < %d",
			uid, marketId, now, lastLoginAt, s.SilentSeconds)
		return false
	}

	for _, whiteMarketId := range s.WhiteMarketIdList {
		if marketId == whiteMarketId {
			log.DebugWithCtx(ctx, "not slient user, uid:%d, hit white marketId:%d, now:%d - lastLoginAt:%d < %d",
				uid, marketId, now, lastLoginAt, s.SilentSeconds)
			return false
		}
	}

	log.InfoWithCtx(ctx, "is silent user, uid:%d, marketId:%d, now:%d - lastLoginAt:%d >= %d",
		uid, marketId, now, lastLoginAt, s.SilentSeconds)
	return true
}
