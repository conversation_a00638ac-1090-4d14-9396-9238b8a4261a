// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/pkg/abtest (interfaces: IABTestClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	abtest "golang.52tt.com/pkg/abtest"
)

// MockIABTestClient is a mock of IABTestClient interface.
type MockIABTestClient struct {
	ctrl     *gomock.Controller
	recorder *MockIABTestClientMockRecorder
}

// MockIABTestClientMockRecorder is the mock recorder for MockIABTestClient.
type MockIABTestClientMockRecorder struct {
	mock *MockIABTestClient
}

// NewMockIABTestClient creates a new mock instance.
func NewMockIABTestClient(ctrl *gomock.Controller) *MockIABTestClient {
	mock := &MockIABTestClient{ctrl: ctrl}
	mock.recorder = &MockIABTestClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIABTestClient) EXPECT() *MockIABTestClientMockRecorder {
	return m.recorder
}

// GetABTestExptByUidAndLayerTag mocks base method.
func (m *MockIABTestClient) GetABTestExptByUidAndLayerTag(arg0 context.Context, arg1 uint32, arg2 string) (*abtest.ExptList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetABTestExptByUidAndLayerTag", arg0, arg1, arg2)
	ret0, _ := ret[0].(*abtest.ExptList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetABTestExptByUidAndLayerTag indicates an expected call of GetABTestExptByUidAndLayerTag.
func (mr *MockIABTestClientMockRecorder) GetABTestExptByUidAndLayerTag(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetABTestExptByUidAndLayerTag", reflect.TypeOf((*MockIABTestClient)(nil).GetABTestExptByUidAndLayerTag), arg0, arg1, arg2)
}

// GetABTestTypeMapSimpleByUidAndLayerTag mocks base method.
func (m *MockIABTestClient) GetABTestTypeMapSimpleByUidAndLayerTag(arg0 context.Context, arg1 uint32, arg2 string) (*abtest.ExptVer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetABTestTypeMapSimpleByUidAndLayerTag", arg0, arg1, arg2)
	ret0, _ := ret[0].(*abtest.ExptVer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetABTestTypeMapSimpleByUidAndLayerTag indicates an expected call of GetABTestTypeMapSimpleByUidAndLayerTag.
func (mr *MockIABTestClientMockRecorder) GetABTestTypeMapSimpleByUidAndLayerTag(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetABTestTypeMapSimpleByUidAndLayerTag", reflect.TypeOf((*MockIABTestClient)(nil).GetABTestTypeMapSimpleByUidAndLayerTag), arg0, arg1, arg2)
}

// GetUserTestListInLayerTag mocks base method.
func (m *MockIABTestClient) GetUserTestListInLayerTag(arg0 context.Context, arg1 uint32, arg2 string) ([]abtest.UserTestList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserTestListInLayerTag", arg0, arg1, arg2)
	ret0, _ := ret[0].([]abtest.UserTestList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTestListInLayerTag indicates an expected call of GetUserTestListInLayerTag.
func (mr *MockIABTestClientMockRecorder) GetUserTestListInLayerTag(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTestListInLayerTag", reflect.TypeOf((*MockIABTestClient)(nil).GetUserTestListInLayerTag), arg0, arg1, arg2)
}

// PostABTestByUidAndLayerTag mocks base method.
func (m *MockIABTestClient) PostABTestByUidAndLayerTag(arg0 context.Context, arg1 uint32, arg2 string) (*abtest.PS_LogicCommonRsp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PostABTestByUidAndLayerTag", arg0, arg1, arg2)
	ret0, _ := ret[0].(*abtest.PS_LogicCommonRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostABTestByUidAndLayerTag indicates an expected call of PostABTestByUidAndLayerTag.
func (mr *MockIABTestClientMockRecorder) PostABTestByUidAndLayerTag(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostABTestByUidAndLayerTag", reflect.TypeOf((*MockIABTestClient)(nil).PostABTestByUidAndLayerTag), arg0, arg1, arg2)
}

// PostABTestRecUserVirtualAbtest mocks base method.
func (m *MockIABTestClient) PostABTestRecUserVirtualAbtest(arg0 context.Context, arg1 uint32, arg2 string, arg3 abtest.LayerDomain) (*abtest.PS_LogicCommonRsp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PostABTestRecUserVirtualAbtest", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*abtest.PS_LogicCommonRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostABTestRecUserVirtualAbtest indicates an expected call of PostABTestRecUserVirtualAbtest.
func (mr *MockIABTestClientMockRecorder) PostABTestRecUserVirtualAbtest(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostABTestRecUserVirtualAbtest", reflect.TypeOf((*MockIABTestClient)(nil).PostABTestRecUserVirtualAbtest), arg0, arg1, arg2, arg3)
}
