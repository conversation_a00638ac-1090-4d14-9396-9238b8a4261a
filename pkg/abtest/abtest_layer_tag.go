package abtest

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
)

func (s *ABTestClient) GetABTestTypeMapSimpleByUidAndLayerTag(ctx context.Context, uid uint32, layerTag string) (exptVer *ExptVer, err error) {
	resp, err := s.PostABTestByUidAndLayerTag(ctx, uid, layerTag)
	if err != nil {
		return nil, err
	}
	return s.getLabIdFromABTestDataString(strconv.Itoa(int(uid)), resp.GetData())
}

func (s *ABTestClient) GetUserTestListInLayerTag(ctx context.Context, uid uint32, layerTag string) ([]UserTestList, error) {
	resp, err := s.PostABTestByUidAndLayerTag(ctx, uid, layerTag)
	if err != nil {
		return nil, err
	}

	data, err := s.decodeABTestDataString(resp.GetData())
	if err != nil {
		return nil, err
	}

	list := make([]UserTestList, 0, len(data.UserTestList))
	for _, userTest := range data.UserTestList {
		if strconv.Itoa(int(uid)) == userTest.User.ClientID {
			list = append(list, userTest)
		}
	}

	return list, nil
}

func (s *ABTestClient) getLabIdFromABTestDataString(uidStr, dataStr string) (exptVer *ExptVer, err error) {
	respData, err := s.decodeABTestDataString(dataStr)
	if err != nil {
		log.Errorf("ABTestClient getLabIdFromABTestDataString dataStr %s respData %v", dataStr, respData)
		return nil, err
	}
	tmp := s.getLabIdTypeSimple(uidStr, respData)
	return &tmp, nil
}

func (s *ABTestClient) getLabIdTypeSimple(uidStr string, data *RespData) ExptVer {
	for _, v := range data.UserTestList {
		// uidStr一定存在
		if v.User.ClientID == uidStr && len(v.ExptList) > 0 {
			return v.ExptList[0].ExptVer
		}
	}
	return ExptVer{}
}

func (s *ABTestClient) PostABTestByUidAndLayerTag(ctx context.Context, uid uint32, layerTag string) (resp *PS_LogicCommonRsp, err error) {
	resp = &PS_LogicCommonRsp{}
	commonReq := s.buildABTestReqByUidAndLayerTag(uid, layerTag)
	jsonBody, err := json.Marshal(commonReq)
	if err != nil {
		log.Errorf("Failed to json.Marshal %+v %+v", commonReq, err)
		return resp, err
	}
	req, err := http.NewRequest("POST", s.URLABTest, strings.NewReader(string(jsonBody)))
	if err != nil {
		log.Errorf("Error Occured. %+v", err)
		return resp, err
	}
	req = req.WithContext(ctx)
	req.Header.Set("Content-Type", "application/json;charset=utf-8")

	// use httpClient to send request
	response, err := s.httpClient.Do(req)
	if err != nil || response == nil {
		log.Errorf("Error sending request to API endpoint. %+v", err)
		return resp, err
	} else {
		// Close the connection to reuse it
		defer response.Body.Close()

		if response.StatusCode != http.StatusOK {
			log.Errorf("Response StatusCode %d not 200", response.StatusCode)
			return
		}

		// Let's check if the work actually is done
		// We have seen inconsistencies even when we get 200 OK response
		body, err := ioutil.ReadAll(response.Body)
		if err != nil {
			log.Errorf("Couldn't parse response body. %+v", err)
			return resp, err
		}
		log.Debugf("Response Body:%s", string(body))
		err = json.Unmarshal(body, resp)
		if err != nil {
			log.Errorf("Couldn't marshal response body(%s). %+v", body, err)
			return resp, err
		}

		baseRsp := resp.GetBaseRsp()
		if baseRsp != nil && (baseRsp.GetCode() != 0 || baseRsp.GetSubCode() != 0) {
			log.Errorf("Response Code %d Msg %s SubCode %d SubMsg %s",
				baseRsp.GetCode(), baseRsp.GetMsg(), baseRsp.GetSubCode(), baseRsp.GetSubMsg())
			return resp, protocol.NewServerError(status.ErrExternalSystemFailed)
		}

		return resp, err
	}
}

func (s *ABTestClient) buildABTestReqByUidAndLayerTag(uid uint32, layerTag string) *PS_LogicCommonReq {
	data := ReqData{
		LayerTag: layerTag,
		UserList: []UserList{
			{
				ClientID:   fmt.Sprintf("%d", uid),
				ClientType: int64(PS_AbtestClientType_PS_AbtestClientType_UID),
			},
		},
		IsNotNeedExptVer: false,
	}
	b, _ := json.Marshal(data)
	p := &PS_LogicCommonReq{
		BaseReq: &BaseReq{
			AppId: s.AppID,
		},
		Data: string(b),
	}
	// fmt.Println(p)
	return p
}

func (s *ABTestClient) PostABTestRecUserVirtualAbtest(ctx context.Context, uid uint32, node string, domain LayerDomain) (resp *PS_LogicCommonRsp, err error) {
	resp = &PS_LogicCommonRsp{}
	commonReq := s.buildABTestRecUserVirtualAbtestReq(uid, node, domain)
	jsonBody, err := json.Marshal(commonReq)
	if err != nil {
		log.Errorf("Failed to json.Marshal %+v %+v", commonReq, err)
		return resp, err
	}
	req, err := http.NewRequest("POST", s.ABTestBaseUrl+"/AbtestLogicService/RecUserVirtualAbtest", strings.NewReader(string(jsonBody)))
	if err != nil {
		log.Errorf("Error Occured. %+v", err)
		return resp, err
	}
	req = req.WithContext(ctx)
	req.Header.Set("Content-Type", "application/json;charset=utf-8")

	// use httpClient to send request
	response, err := s.httpClient.Do(req)
	if err != nil || response == nil {
		log.Errorf("Error sending request to API endpoint. %+v", err)
		return resp, err
	} else {
		// Close the connection to reuse it
		defer response.Body.Close()

		if response.StatusCode != http.StatusOK {
			log.Errorf("PostABTestRecUserVirtualAbtest Response StatusCode %d not 200, uid:%d, node:%s, domain:%v", response.StatusCode, uid, node, domain)
			return
		}

		// Let's check if the work actually is done
		// We have seen inconsistencies even when we get 200 OK response
		body, err := ioutil.ReadAll(response.Body)
		if err != nil {
			log.Errorf("Couldn't parse response body. %+v", err)
			return resp, err
		}
		log.Debugf("Response Body:%s", string(body))
		err = json.Unmarshal(body, resp)
		if err != nil {
			log.Errorf("Couldn't marshal response body(%s). %+v", body, err)
			return resp, err
		}

		baseRsp := resp.GetBaseRsp()
		if baseRsp != nil && (baseRsp.GetCode() != 0 || baseRsp.GetSubCode() != 0) {
			log.Errorf("Response Code %d Msg %s SubCode %d SubMsg %s",
				baseRsp.GetCode(), baseRsp.GetMsg(), baseRsp.GetSubCode(), baseRsp.GetSubMsg())
			return resp, protocol.NewServerError(status.ErrExternalSystemFailed)
		}

		return resp, err
	}
}

func (s *ABTestClient) buildABTestRecUserVirtualAbtestReq(uid uint32, node string, domain LayerDomain) *PS_LogicCommonReq {
	data := RecUserVirtualAbtestReqData{
		LayerDomain: domain,
		Node:        node,
		UserList: []UserList{
			{
				ClientID:   fmt.Sprintf("%d", uid),
				ClientType: int64(PS_AbtestClientType_PS_AbtestClientType_UID),
			},
		},
		IsNeedRsp:       false,
		IsNotNeedSubmit: false,
	}
	b, _ := json.Marshal(data)
	p := &PS_LogicCommonReq{
		BaseReq: &BaseReq{
			AppId: s.AppID,
		},
		Data: string(b),
	}
	return p
}

func (s *ABTestClient) GetABTestExptByUidAndLayerTag(ctx context.Context, uid uint32, layerTag string) (abTestExpt *ExptList, err error) {
	resp, err := s.PostABTestByUidAndLayerTag(ctx, uid, layerTag)
	if err != nil {
		return nil, err
	}
	return s.getABTestExptFromABTestDataString(strconv.Itoa(int(uid)), resp.GetData())
}

func (s *ABTestClient) getABTestExptFromABTestDataString(uidStr, dataStr string) (abTestExpt *ExptList, err error) {
	respData, err := s.decodeABTestDataString(dataStr)
	if err != nil {
		log.Errorf("ABTestClient getABTestExptFromABTestDataString dataStr %s respData %v", dataStr, respData)
		return nil, err
	}
	tmp := s.getABTestExptSimple(uidStr, respData)
	return &tmp, nil
}

func (s *ABTestClient) getABTestExptSimple(uidStr string, data *RespData) ExptList {
	for _, v := range data.UserTestList {
		// uidStr一定存在
		if v.User.ClientID == uidStr && len(v.ExptList) > 0 {
			return v.ExptList[0]
		}
	}
	return ExptList{}
}
