// Code generated by protoc-gen-go. DO NOT EDIT.
// source: abtest.proto

package abtest

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type PS_Platform int32

const (
	PS_Platform_PS_Platform_Unknown    PS_Platform = 0
	PS_Platform_PS_Platform_AS         PS_Platform = 1
	PS_Platform_PS_Platform_AC_Android PS_Platform = 2
	PS_Platform_PS_Platform_AC_IOS     PS_Platform = 3
	PS_Platform_PS_Platform_AC_PC      PS_Platform = 4
)

var PS_Platform_name = map[int32]string{
	0: "PS_Platform_Unknown",
	1: "PS_Platform_AS",
	2: "PS_Platform_AC_Android",
	3: "PS_Platform_AC_IOS",
	4: "PS_Platform_AC_PC",
}

var PS_Platform_value = map[string]int32{
	"PS_Platform_Unknown":    0,
	"PS_Platform_AS":         1,
	"PS_Platform_AC_Android": 2,
	"PS_Platform_AC_IOS":     3,
	"PS_Platform_AC_PC":      4,
}

func (x PS_Platform) String() string {
	return proto.EnumName(PS_Platform_name, int32(x))
}

func (PS_Platform) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{0}
}

type PS_AppType int32

const (
	PS_AppType_PS_AppType_Unknown    PS_AppType = 0
	PS_AppType_PS_AppType_Native     PS_AppType = 1
	PS_AppType_PS_AppType_MiniApp    PS_AppType = 2
	PS_AppType_PS_AppType_Web        PS_AppType = 3
	PS_AppType_PS_AppType_QQ_MiniApp PS_AppType = 4
)

var PS_AppType_name = map[int32]string{
	0: "PS_AppType_Unknown",
	1: "PS_AppType_Native",
	2: "PS_AppType_MiniApp",
	3: "PS_AppType_Web",
	4: "PS_AppType_QQ_MiniApp",
}

var PS_AppType_value = map[string]int32{
	"PS_AppType_Unknown":    0,
	"PS_AppType_Native":     1,
	"PS_AppType_MiniApp":    2,
	"PS_AppType_Web":        3,
	"PS_AppType_QQ_MiniApp": 4,
}

func (x PS_AppType) String() string {
	return proto.EnumName(PS_AppType_name, int32(x))
}

func (PS_AppType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{1}
}

type PS_ExperimentStatus int32

const (
	PS_ExperimentStatus_PS_ExperimentStatus_Unknown PS_ExperimentStatus = 0
	PS_ExperimentStatus_PS_ExperimentStatus_Init    PS_ExperimentStatus = 1
	PS_ExperimentStatus_PS_ExperimentStatus_Debug   PS_ExperimentStatus = 2
	PS_ExperimentStatus_PS_ExperimentStatus_Release PS_ExperimentStatus = 3
	PS_ExperimentStatus_PS_ExperimentStatus_End     PS_ExperimentStatus = 4
)

var PS_ExperimentStatus_name = map[int32]string{
	0: "PS_ExperimentStatus_Unknown",
	1: "PS_ExperimentStatus_Init",
	2: "PS_ExperimentStatus_Debug",
	3: "PS_ExperimentStatus_Release",
	4: "PS_ExperimentStatus_End",
}

var PS_ExperimentStatus_value = map[string]int32{
	"PS_ExperimentStatus_Unknown": 0,
	"PS_ExperimentStatus_Init":    1,
	"PS_ExperimentStatus_Debug":   2,
	"PS_ExperimentStatus_Release": 3,
	"PS_ExperimentStatus_End":     4,
}

func (x PS_ExperimentStatus) String() string {
	return proto.EnumName(PS_ExperimentStatus_name, int32(x))
}

func (PS_ExperimentStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{2}
}

type PS_AbtestClientType int32

const (
	PS_AbtestClientType_PS_AbtestClientType_UID PS_AbtestClientType = 0
)

var PS_AbtestClientType_name = map[int32]string{
	0: "PS_AbtestClientType_UID",
}

var PS_AbtestClientType_value = map[string]int32{
	"PS_AbtestClientType_UID": 0,
}

func (x PS_AbtestClientType) String() string {
	return proto.EnumName(PS_AbtestClientType_name, int32(x))
}

func (PS_AbtestClientType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{3}
}

//错误码
type PS_Code int32

const (
	// option (gogoproto.goproto_enum_prefix) = false;
	PS_Code_PS_CODE_OK                    PS_Code = 0
	PS_Code_PS_CODE_MYSQL_ERR_DATABASE    PS_Code = 1000
	PS_Code_PS_CODE_NEED_APP_ID           PS_Code = 1001
	PS_Code_PS_CODE_NEED_SIGN             PS_Code = 1002
	PS_Code_PS_CODE_SESSION_KEY_NOT_FOUND PS_Code = 1003
	PS_Code_PS_CODE_SIGN_NOT_MATCH        PS_Code = 1004
	PS_Code_PS_CODE_SERVICE_NOT_FOUND     PS_Code = 1005
	PS_Code_PS_CODE_KEY_EXPIRATION        PS_Code = 1006
	PS_Code_PS_CODE_CALL_SERVICE_FAILED   PS_Code = 1007
	PS_Code_PS_CODE_DATA_FORMAT_ERROR     PS_Code = 1008
	PS_Code_PS_CODE_SESSION_KEY_EXPIRED   PS_Code = 1009
	PS_Code_PS_CODE_APP_INFO_NOT_FOUND    PS_Code = 1010
	PS_Code_PS_CODE_REQUEST_FORMAT_ERROR  PS_Code = 1011
	PS_Code_PS_CODE_MONGO_ERR_DATABASE    PS_Code = 1012
	PS_Code_PS_CODE_ACCESS_TOKEN_EXPIRED  PS_Code = 1013
	PS_Code_PS_CODE_SYSERR                PS_Code = 1024
	PS_Code_PS_CODE_UNKNOWN               PS_Code = 2000
)

var PS_Code_name = map[int32]string{
	0:    "PS_CODE_OK",
	1000: "PS_CODE_MYSQL_ERR_DATABASE",
	1001: "PS_CODE_NEED_APP_ID",
	1002: "PS_CODE_NEED_SIGN",
	1003: "PS_CODE_SESSION_KEY_NOT_FOUND",
	1004: "PS_CODE_SIGN_NOT_MATCH",
	1005: "PS_CODE_SERVICE_NOT_FOUND",
	1006: "PS_CODE_KEY_EXPIRATION",
	1007: "PS_CODE_CALL_SERVICE_FAILED",
	1008: "PS_CODE_DATA_FORMAT_ERROR",
	1009: "PS_CODE_SESSION_KEY_EXPIRED",
	1010: "PS_CODE_APP_INFO_NOT_FOUND",
	1011: "PS_CODE_REQUEST_FORMAT_ERROR",
	1012: "PS_CODE_MONGO_ERR_DATABASE",
	1013: "PS_CODE_ACCESS_TOKEN_EXPIRED",
	1024: "PS_CODE_SYSERR",
	2000: "PS_CODE_UNKNOWN",
}

var PS_Code_value = map[string]int32{
	"PS_CODE_OK":                    0,
	"PS_CODE_MYSQL_ERR_DATABASE":    1000,
	"PS_CODE_NEED_APP_ID":           1001,
	"PS_CODE_NEED_SIGN":             1002,
	"PS_CODE_SESSION_KEY_NOT_FOUND": 1003,
	"PS_CODE_SIGN_NOT_MATCH":        1004,
	"PS_CODE_SERVICE_NOT_FOUND":     1005,
	"PS_CODE_KEY_EXPIRATION":        1006,
	"PS_CODE_CALL_SERVICE_FAILED":   1007,
	"PS_CODE_DATA_FORMAT_ERROR":     1008,
	"PS_CODE_SESSION_KEY_EXPIRED":   1009,
	"PS_CODE_APP_INFO_NOT_FOUND":    1010,
	"PS_CODE_REQUEST_FORMAT_ERROR":  1011,
	"PS_CODE_MONGO_ERR_DATABASE":    1012,
	"PS_CODE_ACCESS_TOKEN_EXPIRED":  1013,
	"PS_CODE_SYSERR":                1024,
	"PS_CODE_UNKNOWN":               2000,
}

func (x PS_Code) String() string {
	return proto.EnumName(PS_Code_name, int32(x))
}

func (PS_Code) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{4}
}

type PS_SubCode int32

const (
	//option (gogoproto.goproto_enum_prefix) = false;
	PS_SubCode_PS_SUBCODE_OK PS_SubCode = 0
	//通用 10001 - 11000
	PS_SubCode_PS_SUBCODE_SYSERR                   PS_SubCode = 10024
	PS_SubCode_PS_SUBCODE_INVALID_ARGUMENT         PS_SubCode = 10028
	PS_SubCode_PS_SUBCODE_REQUEST_JSON_PARSE_FAIL  PS_SubCode = 10029
	PS_SubCode_PS_SUBCODE_RESPONSE_JSON_BUILD_FAIL PS_SubCode = 10030
	PS_SubCode_PS_SUBCODE_CANT_FIND_CONFIG         PS_SubCode = 10031
	PS_SubCode_PS_SUBCODE_JSON_PARSE_FAIL          PS_SubCode = 10032
	PS_SubCode_PS_SUBCODE_JSON_LACK_ARGUMENT       PS_SubCode = 10033
	PS_SubCode_PS_SUBCODE_GET_HTTP_CHANNEL_FAIL    PS_SubCode = 10034
	PS_SubCode_PS_SUBCODE_CALL_HTTP_CHANNEL_FAIL   PS_SubCode = 10035
	PS_SubCode_PS_SUBCODE_BACKSTAGE_TOKEN_INVALID  PS_SubCode = 10036
	PS_SubCode_PS_SUBCODE_BACKSTAGE_APPID_INVALID  PS_SubCode = 10037
	//abtest logic 23001 - 24000
	PS_SubCode_PS_SUBCODE_ABTEST_CONFIG_NOT_FOUND         PS_SubCode = 23001
	PS_SubCode_PS_SUBCODE_ABTEST_USER_LIST_NUM_TOO_MANY   PS_SubCode = 23002
	PS_SubCode_PS_SUBCODE_ABTEST_USER_NOT_IN_CLIENT_WHITE PS_SubCode = 23003
	PS_SubCode_PS_SUBCODE_ABTEST_KAFKA_PRODUCER_INVALID   PS_SubCode = 23004
	//abtest data 24001 - 25000
	PS_SubCode_PS_SUBCODE_ABTEST_DATA_GET_DOMAIN_INFO_LIST_DB_ERR        PS_SubCode = 24001
	PS_SubCode_PS_SUBCODE_ABTEST_DATA_GET_LAYER_INFO_LIST_DB_ERR         PS_SubCode = 24002
	PS_SubCode_PS_SUBCODE_ABTEST_DATA_GET_EXPTS_BY_LAYER_ID_DB_ERR       PS_SubCode = 24003
	PS_SubCode_PS_SUBCODE_ABTEST_DATA_GET_EXPTVS_BY_EXPT_ID_DB_ERR       PS_SubCode = 24004
	PS_SubCode_PS_SUBCODE_ABTEST_DATA_GET_CLIENT_WHITE_BY_EXPT_ID_DB_ERR PS_SubCode = 24005
	PS_SubCode_PS_SUBCODE_ABTEST_DATA_GET_USING_ARG_LIST_DB_ERR          PS_SubCode = 24006
	PS_SubCode_PS_SUBCODE_ABTEST_DATA_BATCH_GET_LAYER_INFO_DB_ERR        PS_SubCode = 24007
	PS_SubCode_PS_SUBCODE_ABTEST_DATA_GET_EXPT_LIST_BY_STATUS_DB_ERR     PS_SubCode = 24008
	PS_SubCode_PS_SUBCODE_UNKOWN                                         PS_SubCode = 99999999
)

var PS_SubCode_name = map[int32]string{
	0:        "PS_SUBCODE_OK",
	10024:    "PS_SUBCODE_SYSERR",
	10028:    "PS_SUBCODE_INVALID_ARGUMENT",
	10029:    "PS_SUBCODE_REQUEST_JSON_PARSE_FAIL",
	10030:    "PS_SUBCODE_RESPONSE_JSON_BUILD_FAIL",
	10031:    "PS_SUBCODE_CANT_FIND_CONFIG",
	10032:    "PS_SUBCODE_JSON_PARSE_FAIL",
	10033:    "PS_SUBCODE_JSON_LACK_ARGUMENT",
	10034:    "PS_SUBCODE_GET_HTTP_CHANNEL_FAIL",
	10035:    "PS_SUBCODE_CALL_HTTP_CHANNEL_FAIL",
	10036:    "PS_SUBCODE_BACKSTAGE_TOKEN_INVALID",
	10037:    "PS_SUBCODE_BACKSTAGE_APPID_INVALID",
	23001:    "PS_SUBCODE_ABTEST_CONFIG_NOT_FOUND",
	23002:    "PS_SUBCODE_ABTEST_USER_LIST_NUM_TOO_MANY",
	23003:    "PS_SUBCODE_ABTEST_USER_NOT_IN_CLIENT_WHITE",
	23004:    "PS_SUBCODE_ABTEST_KAFKA_PRODUCER_INVALID",
	24001:    "PS_SUBCODE_ABTEST_DATA_GET_DOMAIN_INFO_LIST_DB_ERR",
	24002:    "PS_SUBCODE_ABTEST_DATA_GET_LAYER_INFO_LIST_DB_ERR",
	24003:    "PS_SUBCODE_ABTEST_DATA_GET_EXPTS_BY_LAYER_ID_DB_ERR",
	24004:    "PS_SUBCODE_ABTEST_DATA_GET_EXPTVS_BY_EXPT_ID_DB_ERR",
	24005:    "PS_SUBCODE_ABTEST_DATA_GET_CLIENT_WHITE_BY_EXPT_ID_DB_ERR",
	24006:    "PS_SUBCODE_ABTEST_DATA_GET_USING_ARG_LIST_DB_ERR",
	24007:    "PS_SUBCODE_ABTEST_DATA_BATCH_GET_LAYER_INFO_DB_ERR",
	24008:    "PS_SUBCODE_ABTEST_DATA_GET_EXPT_LIST_BY_STATUS_DB_ERR",
	99999999: "PS_SUBCODE_UNKOWN",
}

var PS_SubCode_value = map[string]int32{
	"PS_SUBCODE_OK":                                             0,
	"PS_SUBCODE_SYSERR":                                         10024,
	"PS_SUBCODE_INVALID_ARGUMENT":                               10028,
	"PS_SUBCODE_REQUEST_JSON_PARSE_FAIL":                        10029,
	"PS_SUBCODE_RESPONSE_JSON_BUILD_FAIL":                       10030,
	"PS_SUBCODE_CANT_FIND_CONFIG":                               10031,
	"PS_SUBCODE_JSON_PARSE_FAIL":                                10032,
	"PS_SUBCODE_JSON_LACK_ARGUMENT":                             10033,
	"PS_SUBCODE_GET_HTTP_CHANNEL_FAIL":                          10034,
	"PS_SUBCODE_CALL_HTTP_CHANNEL_FAIL":                         10035,
	"PS_SUBCODE_BACKSTAGE_TOKEN_INVALID":                        10036,
	"PS_SUBCODE_BACKSTAGE_APPID_INVALID":                        10037,
	"PS_SUBCODE_ABTEST_CONFIG_NOT_FOUND":                        23001,
	"PS_SUBCODE_ABTEST_USER_LIST_NUM_TOO_MANY":                  23002,
	"PS_SUBCODE_ABTEST_USER_NOT_IN_CLIENT_WHITE":                23003,
	"PS_SUBCODE_ABTEST_KAFKA_PRODUCER_INVALID":                  23004,
	"PS_SUBCODE_ABTEST_DATA_GET_DOMAIN_INFO_LIST_DB_ERR":        24001,
	"PS_SUBCODE_ABTEST_DATA_GET_LAYER_INFO_LIST_DB_ERR":         24002,
	"PS_SUBCODE_ABTEST_DATA_GET_EXPTS_BY_LAYER_ID_DB_ERR":       24003,
	"PS_SUBCODE_ABTEST_DATA_GET_EXPTVS_BY_EXPT_ID_DB_ERR":       24004,
	"PS_SUBCODE_ABTEST_DATA_GET_CLIENT_WHITE_BY_EXPT_ID_DB_ERR": 24005,
	"PS_SUBCODE_ABTEST_DATA_GET_USING_ARG_LIST_DB_ERR":          24006,
	"PS_SUBCODE_ABTEST_DATA_BATCH_GET_LAYER_INFO_DB_ERR":        24007,
	"PS_SUBCODE_ABTEST_DATA_GET_EXPT_LIST_BY_STATUS_DB_ERR":     24008,
	"PS_SUBCODE_UNKOWN":                                         99999999,
}

func (x PS_SubCode) String() string {
	return proto.EnumName(PS_SubCode_name, int32(x))
}

func (PS_SubCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{5}
}

type PS_LogicCommonReq struct {
	BaseReq              *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Data                 string   `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PS_LogicCommonReq) Reset()         { *m = PS_LogicCommonReq{} }
func (m *PS_LogicCommonReq) String() string { return proto.CompactTextString(m) }
func (*PS_LogicCommonReq) ProtoMessage()    {}
func (*PS_LogicCommonReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{0}
}

func (m *PS_LogicCommonReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_LogicCommonReq.Unmarshal(m, b)
}
func (m *PS_LogicCommonReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_LogicCommonReq.Marshal(b, m, deterministic)
}
func (m *PS_LogicCommonReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_LogicCommonReq.Merge(m, src)
}
func (m *PS_LogicCommonReq) XXX_Size() int {
	return xxx_messageInfo_PS_LogicCommonReq.Size(m)
}
func (m *PS_LogicCommonReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_LogicCommonReq.DiscardUnknown(m)
}

var xxx_messageInfo_PS_LogicCommonReq proto.InternalMessageInfo

func (m *PS_LogicCommonReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PS_LogicCommonReq) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

//resp
type PS_LogicCommonRsp struct {
	BaseRsp              *BaseRsp `protobuf:"bytes,1,opt,name=base_rsp,json=baseRsp,proto3" json:"base_rsp,omitempty"`
	Data                 string   `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PS_LogicCommonRsp) Reset()         { *m = PS_LogicCommonRsp{} }
func (m *PS_LogicCommonRsp) String() string { return proto.CompactTextString(m) }
func (*PS_LogicCommonRsp) ProtoMessage()    {}
func (*PS_LogicCommonRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{1}
}

func (m *PS_LogicCommonRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_LogicCommonRsp.Unmarshal(m, b)
}
func (m *PS_LogicCommonRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_LogicCommonRsp.Marshal(b, m, deterministic)
}
func (m *PS_LogicCommonRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_LogicCommonRsp.Merge(m, src)
}
func (m *PS_LogicCommonRsp) XXX_Size() int {
	return xxx_messageInfo_PS_LogicCommonRsp.Size(m)
}
func (m *PS_LogicCommonRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_LogicCommonRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PS_LogicCommonRsp proto.InternalMessageInfo

func (m *PS_LogicCommonRsp) GetBaseRsp() *BaseRsp {
	if m != nil {
		return m.BaseRsp
	}
	return nil
}

func (m *PS_LogicCommonRsp) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

type BaseReq struct {
	AppId                uint32   `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Platform             uint32   `protobuf:"varint,4,opt,name=platform,proto3" json:"platform,omitempty"`
	AppType              uint32   `protobuf:"varint,5,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`
	ClientIp             string   `protobuf:"bytes,6,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseReq) Reset()         { *m = BaseReq{} }
func (m *BaseReq) String() string { return proto.CompactTextString(m) }
func (*BaseReq) ProtoMessage()    {}
func (*BaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{2}
}

func (m *BaseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseReq.Unmarshal(m, b)
}
func (m *BaseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseReq.Marshal(b, m, deterministic)
}
func (m *BaseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseReq.Merge(m, src)
}
func (m *BaseReq) XXX_Size() int {
	return xxx_messageInfo_BaseReq.Size(m)
}
func (m *BaseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseReq.DiscardUnknown(m)
}

var xxx_messageInfo_BaseReq proto.InternalMessageInfo

func (m *BaseReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *BaseReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *BaseReq) GetAppType() uint32 {
	if m != nil {
		return m.AppType
	}
	return 0
}

func (m *BaseReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

type BaseRsp struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	SubCode              int32    `protobuf:"varint,3,opt,name=sub_code,json=subCode,proto3" json:"sub_code,omitempty"`
	SubMsg               string   `protobuf:"bytes,4,opt,name=sub_msg,json=subMsg,proto3" json:"sub_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseRsp) Reset()         { *m = BaseRsp{} }
func (m *BaseRsp) String() string { return proto.CompactTextString(m) }
func (*BaseRsp) ProtoMessage()    {}
func (*BaseRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{3}
}

func (m *BaseRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRsp.Unmarshal(m, b)
}
func (m *BaseRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRsp.Marshal(b, m, deterministic)
}
func (m *BaseRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRsp.Merge(m, src)
}
func (m *BaseRsp) XXX_Size() int {
	return xxx_messageInfo_BaseRsp.Size(m)
}
func (m *BaseRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRsp proto.InternalMessageInfo

func (m *BaseRsp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *BaseRsp) GetSubCode() int32 {
	if m != nil {
		return m.SubCode
	}
	return 0
}

func (m *BaseRsp) GetSubMsg() string {
	if m != nil {
		return m.SubMsg
	}
	return ""
}

type PS_AbtestDomain struct {
	Did                  uint32   `protobuf:"varint,1,opt,name=did,proto3" json:"did,omitempty"`
	UpLayerId            uint32   `protobuf:"varint,2,opt,name=up_layer_id,json=upLayerId,proto3" json:"up_layer_id,omitempty"`
	LdRatio              uint32   `protobuf:"varint,3,opt,name=ld_ratio,json=ldRatio,proto3" json:"ld_ratio,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PS_AbtestDomain) Reset()         { *m = PS_AbtestDomain{} }
func (m *PS_AbtestDomain) String() string { return proto.CompactTextString(m) }
func (*PS_AbtestDomain) ProtoMessage()    {}
func (*PS_AbtestDomain) Descriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{4}
}

func (m *PS_AbtestDomain) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_AbtestDomain.Unmarshal(m, b)
}
func (m *PS_AbtestDomain) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_AbtestDomain.Marshal(b, m, deterministic)
}
func (m *PS_AbtestDomain) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_AbtestDomain.Merge(m, src)
}
func (m *PS_AbtestDomain) XXX_Size() int {
	return xxx_messageInfo_PS_AbtestDomain.Size(m)
}
func (m *PS_AbtestDomain) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_AbtestDomain.DiscardUnknown(m)
}

var xxx_messageInfo_PS_AbtestDomain proto.InternalMessageInfo

func (m *PS_AbtestDomain) GetDid() uint32 {
	if m != nil {
		return m.Did
	}
	return 0
}

func (m *PS_AbtestDomain) GetUpLayerId() uint32 {
	if m != nil {
		return m.UpLayerId
	}
	return 0
}

func (m *PS_AbtestDomain) GetLdRatio() uint32 {
	if m != nil {
		return m.LdRatio
	}
	return 0
}

type PS_AbtestDomainRoute struct {
	UpDomainList         []*PS_AbtestDomain `protobuf:"bytes,1,rep,name=up_domain_list,json=upDomainList,proto3" json:"up_domain_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PS_AbtestDomainRoute) Reset()         { *m = PS_AbtestDomainRoute{} }
func (m *PS_AbtestDomainRoute) String() string { return proto.CompactTextString(m) }
func (*PS_AbtestDomainRoute) ProtoMessage()    {}
func (*PS_AbtestDomainRoute) Descriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{5}
}

func (m *PS_AbtestDomainRoute) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_AbtestDomainRoute.Unmarshal(m, b)
}
func (m *PS_AbtestDomainRoute) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_AbtestDomainRoute.Marshal(b, m, deterministic)
}
func (m *PS_AbtestDomainRoute) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_AbtestDomainRoute.Merge(m, src)
}
func (m *PS_AbtestDomainRoute) XXX_Size() int {
	return xxx_messageInfo_PS_AbtestDomainRoute.Size(m)
}
func (m *PS_AbtestDomainRoute) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_AbtestDomainRoute.DiscardUnknown(m)
}

var xxx_messageInfo_PS_AbtestDomainRoute proto.InternalMessageInfo

func (m *PS_AbtestDomainRoute) GetUpDomainList() []*PS_AbtestDomain {
	if m != nil {
		return m.UpDomainList
	}
	return nil
}

type PS_AbtestExptVer struct {
	ExptvId              uint32                `protobuf:"varint,1,opt,name=exptv_id,json=exptvId,proto3" json:"exptv_id,omitempty"`
	ExptvRatio           uint32                `protobuf:"varint,2,opt,name=exptv_ratio,json=exptvRatio,proto3" json:"exptv_ratio,omitempty"`
	ExptId               uint32                `protobuf:"varint,3,opt,name=expt_id,json=exptId,proto3" json:"expt_id,omitempty"`
	ExptRatio            uint32                `protobuf:"varint,4,opt,name=expt_ratio,json=exptRatio,proto3" json:"expt_ratio,omitempty"`
	ExptStatus           uint32                `protobuf:"varint,5,opt,name=expt_status,json=exptStatus,proto3" json:"expt_status,omitempty"`
	LayerId              uint32                `protobuf:"varint,6,opt,name=layer_id,json=layerId,proto3" json:"layer_id,omitempty"`
	ExptBegin            uint32                `protobuf:"varint,7,opt,name=expt_begin,json=exptBegin,proto3" json:"expt_begin,omitempty"`
	ExptEnd              uint32                `protobuf:"varint,8,opt,name=expt_end,json=exptEnd,proto3" json:"expt_end,omitempty"`
	DomainRoute          *PS_AbtestDomainRoute `protobuf:"bytes,9,opt,name=domain_route,json=domainRoute,proto3" json:"domain_route,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *PS_AbtestExptVer) Reset()         { *m = PS_AbtestExptVer{} }
func (m *PS_AbtestExptVer) String() string { return proto.CompactTextString(m) }
func (*PS_AbtestExptVer) ProtoMessage()    {}
func (*PS_AbtestExptVer) Descriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{6}
}

func (m *PS_AbtestExptVer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_AbtestExptVer.Unmarshal(m, b)
}
func (m *PS_AbtestExptVer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_AbtestExptVer.Marshal(b, m, deterministic)
}
func (m *PS_AbtestExptVer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_AbtestExptVer.Merge(m, src)
}
func (m *PS_AbtestExptVer) XXX_Size() int {
	return xxx_messageInfo_PS_AbtestExptVer.Size(m)
}
func (m *PS_AbtestExptVer) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_AbtestExptVer.DiscardUnknown(m)
}

var xxx_messageInfo_PS_AbtestExptVer proto.InternalMessageInfo

func (m *PS_AbtestExptVer) GetExptvId() uint32 {
	if m != nil {
		return m.ExptvId
	}
	return 0
}

func (m *PS_AbtestExptVer) GetExptvRatio() uint32 {
	if m != nil {
		return m.ExptvRatio
	}
	return 0
}

func (m *PS_AbtestExptVer) GetExptId() uint32 {
	if m != nil {
		return m.ExptId
	}
	return 0
}

func (m *PS_AbtestExptVer) GetExptRatio() uint32 {
	if m != nil {
		return m.ExptRatio
	}
	return 0
}

func (m *PS_AbtestExptVer) GetExptStatus() uint32 {
	if m != nil {
		return m.ExptStatus
	}
	return 0
}

func (m *PS_AbtestExptVer) GetLayerId() uint32 {
	if m != nil {
		return m.LayerId
	}
	return 0
}

func (m *PS_AbtestExptVer) GetExptBegin() uint32 {
	if m != nil {
		return m.ExptBegin
	}
	return 0
}

func (m *PS_AbtestExptVer) GetExptEnd() uint32 {
	if m != nil {
		return m.ExptEnd
	}
	return 0
}

func (m *PS_AbtestExptVer) GetDomainRoute() *PS_AbtestDomainRoute {
	if m != nil {
		return m.DomainRoute
	}
	return nil
}

type PS_AbtestExpt struct {
	ExptArgv             map[string]string `protobuf:"bytes,1,rep,name=expt_argv,json=exptArgv,proto3" json:"expt_argv,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ExptVer              *PS_AbtestExptVer `protobuf:"bytes,2,opt,name=expt_ver,json=exptVer,proto3" json:"expt_ver,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PS_AbtestExpt) Reset()         { *m = PS_AbtestExpt{} }
func (m *PS_AbtestExpt) String() string { return proto.CompactTextString(m) }
func (*PS_AbtestExpt) ProtoMessage()    {}
func (*PS_AbtestExpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{7}
}

func (m *PS_AbtestExpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_AbtestExpt.Unmarshal(m, b)
}
func (m *PS_AbtestExpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_AbtestExpt.Marshal(b, m, deterministic)
}
func (m *PS_AbtestExpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_AbtestExpt.Merge(m, src)
}
func (m *PS_AbtestExpt) XXX_Size() int {
	return xxx_messageInfo_PS_AbtestExpt.Size(m)
}
func (m *PS_AbtestExpt) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_AbtestExpt.DiscardUnknown(m)
}

var xxx_messageInfo_PS_AbtestExpt proto.InternalMessageInfo

func (m *PS_AbtestExpt) GetExptArgv() map[string]string {
	if m != nil {
		return m.ExptArgv
	}
	return nil
}

func (m *PS_AbtestExpt) GetExptVer() *PS_AbtestExptVer {
	if m != nil {
		return m.ExptVer
	}
	return nil
}

type PS_AbtestUser struct {
	ClientType           uint32   `protobuf:"varint,1,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientId             string   `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PS_AbtestUser) Reset()         { *m = PS_AbtestUser{} }
func (m *PS_AbtestUser) String() string { return proto.CompactTextString(m) }
func (*PS_AbtestUser) ProtoMessage()    {}
func (*PS_AbtestUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{8}
}

func (m *PS_AbtestUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_AbtestUser.Unmarshal(m, b)
}
func (m *PS_AbtestUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_AbtestUser.Marshal(b, m, deterministic)
}
func (m *PS_AbtestUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_AbtestUser.Merge(m, src)
}
func (m *PS_AbtestUser) XXX_Size() int {
	return xxx_messageInfo_PS_AbtestUser.Size(m)
}
func (m *PS_AbtestUser) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_AbtestUser.DiscardUnknown(m)
}

var xxx_messageInfo_PS_AbtestUser proto.InternalMessageInfo

func (m *PS_AbtestUser) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *PS_AbtestUser) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

type PS_UsersAbtest struct {
	User                 *PS_AbtestUser    `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	ExptList             []*PS_AbtestExpt  `protobuf:"bytes,2,rep,name=expt_list,json=exptList,proto3" json:"expt_list,omitempty"`
	DefaultArgv          map[string]string `protobuf:"bytes,3,rep,name=default_argv,json=defaultArgv,proto3" json:"default_argv,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PS_UsersAbtest) Reset()         { *m = PS_UsersAbtest{} }
func (m *PS_UsersAbtest) String() string { return proto.CompactTextString(m) }
func (*PS_UsersAbtest) ProtoMessage()    {}
func (*PS_UsersAbtest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{9}
}

func (m *PS_UsersAbtest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_UsersAbtest.Unmarshal(m, b)
}
func (m *PS_UsersAbtest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_UsersAbtest.Marshal(b, m, deterministic)
}
func (m *PS_UsersAbtest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_UsersAbtest.Merge(m, src)
}
func (m *PS_UsersAbtest) XXX_Size() int {
	return xxx_messageInfo_PS_UsersAbtest.Size(m)
}
func (m *PS_UsersAbtest) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_UsersAbtest.DiscardUnknown(m)
}

var xxx_messageInfo_PS_UsersAbtest proto.InternalMessageInfo

func (m *PS_UsersAbtest) GetUser() *PS_AbtestUser {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *PS_UsersAbtest) GetExptList() []*PS_AbtestExpt {
	if m != nil {
		return m.ExptList
	}
	return nil
}

func (m *PS_UsersAbtest) GetDefaultArgv() map[string]string {
	if m != nil {
		return m.DefaultArgv
	}
	return nil
}

//按层标签搜索用户的实验配置信息
type PS_GetUsersAbtestByTagReq struct {
	LayerTag             string           `protobuf:"bytes,1,opt,name=layer_tag,json=layerTag,proto3" json:"layer_tag,omitempty"`
	UserList             []*PS_AbtestUser `protobuf:"bytes,2,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PS_GetUsersAbtestByTagReq) Reset()         { *m = PS_GetUsersAbtestByTagReq{} }
func (m *PS_GetUsersAbtestByTagReq) String() string { return proto.CompactTextString(m) }
func (*PS_GetUsersAbtestByTagReq) ProtoMessage()    {}
func (*PS_GetUsersAbtestByTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{10}
}

func (m *PS_GetUsersAbtestByTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_GetUsersAbtestByTagReq.Unmarshal(m, b)
}
func (m *PS_GetUsersAbtestByTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_GetUsersAbtestByTagReq.Marshal(b, m, deterministic)
}
func (m *PS_GetUsersAbtestByTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_GetUsersAbtestByTagReq.Merge(m, src)
}
func (m *PS_GetUsersAbtestByTagReq) XXX_Size() int {
	return xxx_messageInfo_PS_GetUsersAbtestByTagReq.Size(m)
}
func (m *PS_GetUsersAbtestByTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_GetUsersAbtestByTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_PS_GetUsersAbtestByTagReq proto.InternalMessageInfo

func (m *PS_GetUsersAbtestByTagReq) GetLayerTag() string {
	if m != nil {
		return m.LayerTag
	}
	return ""
}

func (m *PS_GetUsersAbtestByTagReq) GetUserList() []*PS_AbtestUser {
	if m != nil {
		return m.UserList
	}
	return nil
}

type PS_GetUsersAbtestByTagRsp struct {
	UserTestList         []*PS_UsersAbtest `protobuf:"bytes,1,rep,name=user_test_list,json=userTestList,proto3" json:"user_test_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PS_GetUsersAbtestByTagRsp) Reset()         { *m = PS_GetUsersAbtestByTagRsp{} }
func (m *PS_GetUsersAbtestByTagRsp) String() string { return proto.CompactTextString(m) }
func (*PS_GetUsersAbtestByTagRsp) ProtoMessage()    {}
func (*PS_GetUsersAbtestByTagRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_f2c090ce0097f12a, []int{11}
}

func (m *PS_GetUsersAbtestByTagRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_GetUsersAbtestByTagRsp.Unmarshal(m, b)
}
func (m *PS_GetUsersAbtestByTagRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_GetUsersAbtestByTagRsp.Marshal(b, m, deterministic)
}
func (m *PS_GetUsersAbtestByTagRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_GetUsersAbtestByTagRsp.Merge(m, src)
}
func (m *PS_GetUsersAbtestByTagRsp) XXX_Size() int {
	return xxx_messageInfo_PS_GetUsersAbtestByTagRsp.Size(m)
}
func (m *PS_GetUsersAbtestByTagRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_GetUsersAbtestByTagRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PS_GetUsersAbtestByTagRsp proto.InternalMessageInfo

func (m *PS_GetUsersAbtestByTagRsp) GetUserTestList() []*PS_UsersAbtest {
	if m != nil {
		return m.UserTestList
	}
	return nil
}

func init() {
	proto.RegisterEnum("event.PS_Platform", PS_Platform_name, PS_Platform_value)
	proto.RegisterEnum("event.PS_AppType", PS_AppType_name, PS_AppType_value)
	proto.RegisterEnum("event.PS_ExperimentStatus", PS_ExperimentStatus_name, PS_ExperimentStatus_value)
	proto.RegisterEnum("event.PS_AbtestClientType", PS_AbtestClientType_name, PS_AbtestClientType_value)
	proto.RegisterEnum("event.PS_Code", PS_Code_name, PS_Code_value)
	proto.RegisterEnum("event.PS_SubCode", PS_SubCode_name, PS_SubCode_value)
	proto.RegisterType((*PS_LogicCommonReq)(nil), "event.PS_LogicCommonReq")
	proto.RegisterType((*PS_LogicCommonRsp)(nil), "event.PS_LogicCommonRsp")
	proto.RegisterType((*BaseReq)(nil), "event.BaseReq")
	proto.RegisterType((*BaseRsp)(nil), "event.BaseRsp")
	proto.RegisterType((*PS_AbtestDomain)(nil), "event.PS_AbtestDomain")
	proto.RegisterType((*PS_AbtestDomainRoute)(nil), "event.PS_AbtestDomainRoute")
	proto.RegisterType((*PS_AbtestExptVer)(nil), "event.PS_AbtestExptVer")
	proto.RegisterType((*PS_AbtestExpt)(nil), "event.PS_AbtestExpt")
	proto.RegisterMapType((map[string]string)(nil), "event.PS_AbtestExpt.ExptArgvEntry")
	proto.RegisterType((*PS_AbtestUser)(nil), "event.PS_AbtestUser")
	proto.RegisterType((*PS_UsersAbtest)(nil), "event.PS_UsersAbtest")
	proto.RegisterMapType((map[string]string)(nil), "event.PS_UsersAbtest.DefaultArgvEntry")
	proto.RegisterType((*PS_GetUsersAbtestByTagReq)(nil), "event.PS_GetUsersAbtestByTagReq")
	proto.RegisterType((*PS_GetUsersAbtestByTagRsp)(nil), "event.PS_GetUsersAbtestByTagRsp")
}

func init() { proto.RegisterFile("abtest.proto", fileDescriptor_f2c090ce0097f12a) }

var fileDescriptor_f2c090ce0097f12a = []byte{
	// 1666 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x57, 0x49, 0x73, 0xdb, 0xc8,
	0x15, 0x36, 0xb4, 0x41, 0x6a, 0xc9, 0x9a, 0x9e, 0x1e, 0xd9, 0xa2, 0xa5, 0xf1, 0x58, 0xc3, 0x54,
	0x26, 0x8a, 0x0e, 0xca, 0x8c, 0xa6, 0x92, 0xcc, 0xc4, 0xa9, 0x99, 0x02, 0x01, 0x88, 0x46, 0x48,
	0x36, 0x60, 0x34, 0x28, 0x5b, 0x97, 0x74, 0x81, 0x46, 0x0f, 0x8b, 0x65, 0x0a, 0x84, 0x01, 0x50,
	0xb1, 0x6e, 0x49, 0xaa, 0xf2, 0x5f, 0x72, 0xc8, 0xe6, 0x28, 0x39, 0x29, 0x17, 0x67, 0x3f, 0xe6,
	0x96, 0xca, 0x72, 0xcb, 0x25, 0xfb, 0x9e, 0xaa, 0xdc, 0x92, 0xea, 0x05, 0x24, 0x48, 0x53, 0x4a,
	0xe6, 0xa2, 0xea, 0x7e, 0xfd, 0xbd, 0xef, 0xbd, 0xf7, 0xbd, 0xd7, 0x0d, 0x0a, 0xac, 0x85, 0x9d,
	0x9c, 0x65, 0xf9, 0x7e, 0x92, 0x0e, 0xf2, 0x01, 0x5a, 0x64, 0xa7, 0x2c, 0xce, 0xab, 0x3e, 0x78,
	0xd9, 0x23, 0xb4, 0x39, 0xe8, 0xf6, 0x1e, 0x99, 0x83, 0x93, 0x93, 0x41, 0xec, 0xb3, 0x27, 0xe8,
	0xe3, 0x60, 0xb9, 0x13, 0x66, 0x8c, 0xa6, 0xec, 0x49, 0x45, 0xdb, 0xd1, 0x76, 0x57, 0x0f, 0xd6,
	0xf7, 0x05, 0x7c, 0xbf, 0x16, 0x66, 0xcc, 0x67, 0x4f, 0x7c, 0xbd, 0x23, 0x17, 0x08, 0x81, 0x85,
	0x28, 0xcc, 0xc3, 0xca, 0xdc, 0x8e, 0xb6, 0xbb, 0xe2, 0x8b, 0xf5, 0x0c, 0xce, 0x2c, 0x19, 0x73,
	0x66, 0xc9, 0x2c, 0xce, 0x2c, 0x51, 0x9c, 0x59, 0x32, 0x93, 0x33, 0x07, 0xba, 0x8a, 0x8d, 0x6e,
	0x80, 0xa5, 0x30, 0x49, 0x68, 0x2f, 0x12, 0x3c, 0xd7, 0xfd, 0xc5, 0x30, 0x49, 0x9c, 0x08, 0x6d,
	0x81, 0xe5, 0xa4, 0x1f, 0xe6, 0x1f, 0x0c, 0xd2, 0x93, 0xca, 0x82, 0x38, 0x18, 0xed, 0xd1, 0x2d,
	0xb0, 0xcc, 0x5d, 0xf2, 0xb3, 0x84, 0x55, 0x16, 0xc5, 0x99, 0x1e, 0x26, 0x49, 0x70, 0x96, 0x30,
	0xb4, 0x0d, 0x56, 0x1e, 0xf5, 0x7b, 0x2c, 0xce, 0x69, 0x2f, 0xa9, 0x2c, 0x89, 0x88, 0xcb, 0xd2,
	0xe0, 0x24, 0xd5, 0x47, 0x2a, 0xaa, 0x4c, 0xea, 0xd1, 0x20, 0x62, 0x22, 0xe6, 0xa2, 0x2f, 0xd6,
	0x08, 0x82, 0xf9, 0x93, 0xac, 0xab, 0xf2, 0xe4, 0x4b, 0x1e, 0x28, 0x1b, 0x76, 0xa8, 0x40, 0xce,
	0x0b, 0xa4, 0x9e, 0x0d, 0x3b, 0x26, 0x07, 0x6f, 0x02, 0xbe, 0xa4, 0xdc, 0x61, 0x41, 0x38, 0x2c,
	0x65, 0xc3, 0x4e, 0x2b, 0xeb, 0x56, 0x3f, 0x0f, 0x5e, 0xf2, 0x08, 0x35, 0x44, 0x73, 0xac, 0xc1,
	0x49, 0xd8, 0x8b, 0x39, 0x71, 0x34, 0xaa, 0x8f, 0x2f, 0xd1, 0x6b, 0x60, 0x75, 0x98, 0xd0, 0x7e,
	0x78, 0xc6, 0x52, 0x5e, 0xf9, 0x9c, 0x38, 0x59, 0x19, 0x26, 0x4d, 0x6e, 0x71, 0x22, 0x1e, 0xb8,
	0x1f, 0xd1, 0x34, 0xcc, 0x7b, 0x03, 0x11, 0xf8, 0xba, 0xaf, 0xf7, 0x23, 0x9f, 0x6f, 0xab, 0x01,
	0xd8, 0x98, 0xe2, 0xf7, 0x07, 0xc3, 0x9c, 0xa1, 0xcf, 0x82, 0xf5, 0x61, 0x42, 0x23, 0x61, 0xa1,
	0xfd, 0x5e, 0x96, 0x57, 0xb4, 0x9d, 0xf9, 0xdd, 0xd5, 0x83, 0x9b, 0xaa, 0x2f, 0xd3, 0x4e, 0x6b,
	0xc3, 0x44, 0xae, 0x9a, 0xbd, 0x2c, 0xaf, 0x7e, 0x6f, 0x0e, 0xc0, 0x11, 0xc2, 0x7e, 0x9a, 0xe4,
	0x47, 0x2c, 0xe5, 0x59, 0xb0, 0xa7, 0x49, 0x7e, 0x3a, 0x6e, 0x8e, 0x2e, 0xf6, 0x4e, 0x84, 0xee,
	0x80, 0x55, 0x79, 0x24, 0x73, 0x94, 0x05, 0x00, 0x61, 0x12, 0x69, 0x72, 0x7d, 0xf8, 0x8e, 0xbb,
	0xca, 0x02, 0x96, 0xf8, 0xd6, 0x89, 0xd0, 0x6d, 0x20, 0x60, 0xca, 0x51, 0xb6, 0x76, 0x85, 0x5b,
	0xa4, 0x9f, 0x22, 0xa6, 0x59, 0x1e, 0xe6, 0xc3, 0x4c, 0xb5, 0x57, 0x78, 0x10, 0x61, 0x11, 0xd2,
	0x14, 0xba, 0x2d, 0x29, 0x69, 0x94, 0x6a, 0x05, 0x75, 0x87, 0x75, 0x7b, 0x71, 0x45, 0x1f, 0x53,
	0xd7, 0xb8, 0xa1, 0x28, 0x87, 0xb2, 0x38, 0xaa, 0x2c, 0x8f, 0xcb, 0xb1, 0xe3, 0x08, 0xbd, 0x07,
	0xd6, 0x94, 0x72, 0x29, 0x17, 0xb3, 0xb2, 0x22, 0x46, 0x7a, 0xfb, 0x12, 0xe9, 0x38, 0xc4, 0x5f,
	0x8d, 0xc6, 0x9b, 0xea, 0x73, 0x0d, 0x5c, 0x9f, 0x90, 0x0f, 0xbd, 0x0f, 0x44, 0x64, 0x1a, 0xa6,
	0xdd, 0x53, 0xd5, 0x89, 0xea, 0x34, 0x1d, 0x07, 0xee, 0xf3, 0x3f, 0x46, 0xda, 0x3d, 0xb5, 0xe3,
	0x3c, 0x3d, 0xf3, 0x45, 0x86, 0x7c, 0x8b, 0x0e, 0x54, 0xb6, 0xa7, 0x2c, 0x15, 0xf2, 0xae, 0x1e,
	0x6c, 0xce, 0xf2, 0x3f, 0x62, 0xa9, 0x2c, 0xe3, 0x88, 0xa5, 0x5b, 0x77, 0xc1, 0xf5, 0x09, 0x3a,
	0x3e, 0x79, 0x8f, 0xd9, 0x99, 0x68, 0xde, 0x8a, 0xcf, 0x97, 0x68, 0x03, 0x2c, 0x9e, 0x86, 0xfd,
	0x21, 0x53, 0x63, 0x2e, 0x37, 0x9f, 0x99, 0x7b, 0x47, 0xab, 0xb6, 0x4a, 0x25, 0xb4, 0x33, 0x96,
	0xf2, 0x56, 0xa8, 0xbb, 0x24, 0x6e, 0x9a, 0x9c, 0x00, 0x20, 0x4d, 0xd3, 0x97, 0x2d, 0x52, 0x7c,
	0xc5, 0x65, 0x8b, 0xaa, 0xff, 0xd6, 0xc0, 0xba, 0x47, 0x28, 0x67, 0xca, 0x24, 0x29, 0xda, 0x05,
	0x0b, 0xc3, 0x8c, 0xa5, 0xea, 0xc1, 0xd8, 0x98, 0x2e, 0x87, 0x43, 0x7d, 0x81, 0x40, 0x6f, 0x29,
	0xf5, 0xc4, 0x1c, 0xcf, 0x09, 0xf5, 0x36, 0x66, 0x55, 0x2f, 0xf5, 0xe2, 0x13, 0x8c, 0x1c, 0xb0,
	0x16, 0xb1, 0x0f, 0xc2, 0x61, 0x5f, 0x69, 0x3e, 0x2f, 0xbc, 0xde, 0x18, 0x7b, 0x95, 0x32, 0xd9,
	0xb7, 0x24, 0x72, 0xac, 0xfb, 0x6a, 0x34, 0xb6, 0x6c, 0xbd, 0x07, 0xe0, 0x34, 0xe0, 0x43, 0x29,
	0xf9, 0x18, 0xdc, 0xf2, 0x08, 0xad, 0xb3, 0xbc, 0x14, 0xb2, 0x76, 0x16, 0x84, 0x5d, 0xfe, 0xde,
	0x6d, 0x83, 0x15, 0x39, 0xbf, 0x79, 0xd8, 0x55, 0x74, 0x72, 0xa0, 0x83, 0xb0, 0xcb, 0xeb, 0xe6,
	0xf5, 0x5f, 0x59, 0xb7, 0x90, 0x69, 0x99, 0xc3, 0xc4, 0xcd, 0x7d, 0x78, 0x69, 0xb0, 0x2c, 0x41,
	0x77, 0xc1, 0xba, 0xe0, 0xe3, 0xc6, 0xf2, 0xa3, 0x70, 0x63, 0xa6, 0x2c, 0xfe, 0x1a, 0x07, 0x07,
	0x2c, 0x13, 0x8a, 0xee, 0x7d, 0x45, 0x03, 0xab, 0x1e, 0xa1, 0x5e, 0xf1, 0xec, 0x6e, 0x82, 0x57,
	0x4a, 0x5b, 0xda, 0x8e, 0x1f, 0xc7, 0x83, 0x2f, 0xc4, 0xf0, 0x1a, 0x42, 0xa2, 0xd3, 0xa3, 0x03,
	0x83, 0x40, 0x0d, 0x6d, 0x81, 0x9b, 0x13, 0x36, 0x93, 0x1a, 0x71, 0x94, 0x0e, 0x7a, 0x11, 0x9c,
	0x43, 0x37, 0x01, 0x9a, 0x3a, 0x73, 0x5c, 0x02, 0xe7, 0xd1, 0x0d, 0xf1, 0xa5, 0x29, 0xdb, 0x3d,
	0x13, 0x2e, 0xec, 0x7d, 0x59, 0x03, 0x80, 0x57, 0xaf, 0x9e, 0x78, 0xe9, 0xad, 0x76, 0xa5, 0x2c,
	0xa4, 0x77, 0x61, 0xc7, 0x61, 0xde, 0x3b, 0x65, 0x50, 0x9b, 0x82, 0xb7, 0x7a, 0x71, 0xcf, 0x48,
	0x12, 0x38, 0xa7, 0x92, 0x2e, 0xec, 0x0f, 0x58, 0x07, 0xce, 0xa3, 0x5b, 0xe0, 0x46, 0xc9, 0x76,
	0xff, 0xfe, 0x08, 0xbe, 0xb0, 0xf7, 0x4c, 0x13, 0xd5, 0xdb, 0x4f, 0x13, 0x96, 0xf6, 0x4e, 0x58,
	0x5c, 0x3c, 0x47, 0x77, 0xc0, 0xf6, 0x0c, 0x73, 0x29, 0xad, 0x57, 0x41, 0x65, 0x16, 0xc0, 0x89,
	0x7b, 0x39, 0xd4, 0xd0, 0x6d, 0xd1, 0xbd, 0x17, 0x4e, 0x2d, 0xd6, 0x19, 0x76, 0xe1, 0xdc, 0x65,
	0xec, 0x3e, 0xeb, 0xb3, 0x30, 0x63, 0x70, 0x1e, 0x6d, 0x83, 0xcd, 0x59, 0x00, 0x3b, 0x8e, 0xe0,
	0xc2, 0xde, 0x81, 0x48, 0x59, 0xf6, 0xd6, 0x2c, 0x5f, 0xdb, 0xcd, 0x19, 0x66, 0xda, 0x76, 0x2c,
	0x78, 0x6d, 0xef, 0x4b, 0x0b, 0x40, 0xf7, 0x08, 0x15, 0xdf, 0xb8, 0x75, 0xa1, 0xbb, 0xe9, 0x5a,
	0x36, 0x75, 0x1b, 0xf0, 0x1a, 0xba, 0x03, 0xb6, 0x8a, 0x7d, 0xeb, 0x98, 0xdc, 0x6f, 0x52, 0xdb,
	0xf7, 0xa9, 0x65, 0x04, 0x46, 0xcd, 0x20, 0x36, 0xfc, 0xad, 0x8e, 0x2a, 0x22, 0xa0, 0x00, 0x60,
	0xdb, 0xb6, 0xa8, 0xe1, 0x79, 0xd4, 0xb1, 0xe0, 0xef, 0x74, 0x74, 0x53, 0x34, 0x67, 0x7c, 0x42,
	0x9c, 0x3a, 0x86, 0xbf, 0xd7, 0x51, 0x15, 0xdc, 0x2e, 0xec, 0xc4, 0x26, 0xc4, 0x71, 0x31, 0x6d,
	0xd8, 0xc7, 0x14, 0xbb, 0x01, 0x3d, 0x74, 0xdb, 0xd8, 0x82, 0x7f, 0xd0, 0xd1, 0xb6, 0x18, 0x25,
	0x89, 0x71, 0xea, 0x58, 0x1c, 0xb6, 0x8c, 0xc0, 0xbc, 0x07, 0xff, 0xa8, 0xa3, 0xd7, 0x84, 0x80,
	0x8a, 0xc0, 0x3f, 0x72, 0x4c, 0xbb, 0xe4, 0xfc, 0xa7, 0x09, 0x67, 0x4e, 0x6c, 0x3f, 0xf4, 0x1c,
	0xdf, 0x08, 0x1c, 0x17, 0xc3, 0x3f, 0xeb, 0x68, 0x47, 0xc8, 0x2b, 0x0e, 0x4d, 0xa3, 0xd9, 0x1c,
	0x31, 0x1c, 0x1a, 0x4e, 0xd3, 0xb6, 0xe0, 0x5f, 0x26, 0xe8, 0x79, 0xa1, 0xf4, 0xd0, 0xf5, 0x5b,
	0x46, 0xc0, 0x0b, 0x77, 0x7d, 0xf8, 0xd7, 0x09, 0x86, 0x72, 0xfe, 0x22, 0x8c, 0x6d, 0xc1, 0xbf,
	0xe9, 0x65, 0xd1, 0x84, 0x1c, 0xf8, 0xd0, 0x2d, 0x65, 0xf8, 0x77, 0x1d, 0xbd, 0x0e, 0x5e, 0x2d,
	0x00, 0xbe, 0x7d, 0xbf, 0x6d, 0x93, 0x60, 0x32, 0xca, 0x3f, 0x26, 0x38, 0x5a, 0x2e, 0xae, 0xbb,
	0x93, 0xc2, 0xff, 0x73, 0x82, 0xc3, 0x30, 0x4d, 0x9b, 0x10, 0x1a, 0xb8, 0x0d, 0x1b, 0x8f, 0xf2,
	0xf8, 0x97, 0x8e, 0x5e, 0x11, 0xf3, 0x2e, 0x33, 0x3d, 0x26, 0xb6, 0xef, 0xc3, 0x2f, 0x2e, 0xa3,
	0x0d, 0xf1, 0x63, 0x45, 0x18, 0xdb, 0xb8, 0x81, 0xdd, 0x07, 0x18, 0xfe, 0xec, 0xa5, 0xbd, 0xdf,
	0x2c, 0x8b, 0xc6, 0x13, 0xf5, 0x53, 0xe7, 0x65, 0xf1, 0x61, 0x20, 0xed, 0xda, 0x78, 0x12, 0x64,
	0x3b, 0x0b, 0x93, 0xe2, 0xfb, 0x2a, 0x56, 0x72, 0x14, 0x76, 0x07, 0x1f, 0x19, 0x4d, 0xc7, 0xa2,
	0x86, 0x5f, 0x6f, 0xb7, 0x6c, 0x1c, 0xc0, 0xaf, 0x61, 0xf4, 0x31, 0x50, 0x2d, 0x21, 0x8a, 0x82,
	0x3f, 0x47, 0x5c, 0x4c, 0x3d, 0xc3, 0x27, 0x52, 0x7b, 0xf8, 0x75, 0x8c, 0x76, 0xc1, 0x47, 0x26,
	0x80, 0xc4, 0x73, 0x31, 0xb1, 0x25, 0xb2, 0xd6, 0x76, 0x9a, 0x96, 0x44, 0x7e, 0x63, 0x3a, 0xa8,
	0x69, 0xe0, 0x80, 0x1e, 0x3a, 0xd8, 0xa2, 0xa6, 0x8b, 0x0f, 0x9d, 0x3a, 0xfc, 0x26, 0x56, 0xfa,
	0x15, 0x88, 0xe9, 0x60, 0xdf, 0xc2, 0x6a, 0x0c, 0x27, 0x00, 0x4d, 0xc3, 0x6c, 0x8c, 0x33, 0x7f,
	0x86, 0xd1, 0x47, 0xc1, 0x4e, 0x09, 0x53, 0xb7, 0x03, 0x7a, 0x2f, 0x08, 0x3c, 0x6a, 0xde, 0x33,
	0x30, 0xb6, 0x9b, 0x92, 0xea, 0xdb, 0x18, 0xbd, 0x01, 0x5e, 0x9f, 0xc8, 0xa6, 0xd9, 0x9c, 0x81,
	0x3b, 0x9f, 0x16, 0xa2, 0x66, 0x98, 0x0d, 0x12, 0x18, 0x75, 0x5b, 0x35, 0x4e, 0x49, 0x07, 0xbf,
	0x73, 0x39, 0xd0, 0xf0, 0x3c, 0xc7, 0x1a, 0x01, 0xbf, 0xcb, 0x15, 0x2b, 0x03, 0x8d, 0x5a, 0xc0,
	0x95, 0x95, 0x22, 0x94, 0x26, 0xee, 0x17, 0xe7, 0x1a, 0xda, 0x07, 0xbb, 0x2f, 0x22, 0xdb, 0xc4,
	0xf6, 0x69, 0xd3, 0x21, 0x01, 0xc5, 0xed, 0x16, 0x0d, 0x5c, 0x97, 0xb6, 0x0c, 0x7c, 0x0c, 0x7f,
	0x79, 0xae, 0xa1, 0x37, 0xc1, 0xde, 0x25, 0x78, 0xce, 0xeb, 0x60, 0x6a, 0x36, 0x1d, 0x1b, 0x07,
	0xf4, 0xc1, 0x3d, 0x27, 0xb0, 0xe1, 0xaf, 0x2e, 0x8b, 0xd0, 0x30, 0x0e, 0x1b, 0x06, 0xf5, 0x7c,
	0xd7, 0x6a, 0x9b, 0xb6, 0x3f, 0x4a, 0xfd, 0xd7, 0xe7, 0x1a, 0x7a, 0x07, 0x1c, 0xbc, 0x88, 0x17,
	0x37, 0x8e, 0x0b, 0x6d, 0xb9, 0x2d, 0xc3, 0xc1, 0xf2, 0xee, 0x88, 0x0c, 0xad, 0x1a, 0xbf, 0x02,
	0xf0, 0xf9, 0x85, 0x86, 0x3e, 0x0d, 0xde, 0xba, 0xc2, 0xb3, 0x69, 0x1c, 0x8b, 0x48, 0x53, 0x8e,
	0xdf, 0xbf, 0xd0, 0xd0, 0xbb, 0xe0, 0xed, 0x2b, 0x1c, 0xed, 0x87, 0x5e, 0x40, 0x68, 0xed, 0xb8,
	0x60, 0xb0, 0x0a, 0xd7, 0x1f, 0xfc, 0x5f, 0xae, 0x47, 0xc2, 0x97, 0xaf, 0x4a, 0xae, 0x3f, 0xbc,
	0xd0, 0xd0, 0xfb, 0xe0, 0xdd, 0x2b, 0x5c, 0xcb, 0x3a, 0xce, 0x20, 0xf8, 0xd1, 0x85, 0x86, 0x3e,
	0x05, 0xde, 0xbc, 0x82, 0xa0, 0x4d, 0x1c, 0x5c, 0xe7, 0x63, 0x3b, 0x51, 0xee, 0x8f, 0x2f, 0xae,
	0x52, 0xb8, 0xc6, 0xdf, 0xd2, 0x69, 0xb5, 0x94, 0xe7, 0x4f, 0x2e, 0x34, 0x74, 0x17, 0x7c, 0xf2,
	0x7f, 0x54, 0x2b, 0x83, 0xd5, 0x8e, 0x29, 0x09, 0x8c, 0xa0, 0x4d, 0x0a, 0xe7, 0x9f, 0x5e, 0x68,
	0xa8, 0x32, 0xf1, 0x52, 0xb4, 0x71, 0x83, 0xbf, 0x31, 0xff, 0x79, 0xfe, 0xf3, 0x4f, 0x74, 0x96,
	0xc4, 0x7f, 0xae, 0x6f, 0xff, 0x37, 0x00, 0x00, 0xff, 0xff, 0x4e, 0xc6, 0x6e, 0xe8, 0xc9, 0x0e,
	0x00, 0x00,
}
