package event_link_wrap

import (
	"context"
	"sync"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
	"golang.52tt.com/pkg/log"
)

type KafkaProduce struct {
	//producer sarama.AsyncProducer
	producer publisher.Publisher
	topic    string
	wg       sync.WaitGroup
}

func NewKafkaProduce(addr []string, clientId, topic string, opts ...publisher.Option) (*KafkaProduce, error) {
	log.Infof("NewKafkaProduce topic %s, addr %v, clientId %s", topic, addr, clientId)

	kafka := &KafkaProduce{
		topic: topic,
	}

	err := kafka.Init(addr, clientId, opts...)
	if err != nil {
		log.Errorf("NewKafkaProduce kafka init err %s kafka %v", err.<PERSON><PERSON>r(), kafka)
		return nil, err
	}

	log.Infof("NewKafkaProduce end")
	return kafka, nil
}

func (k *KafkaProduce) Init(addr []string, clientId string, opts ...publisher.Option) error {
	pc := kafka.DefaultConfig()
	pc.ClientID = clientId
	pc.Producer.RequiredAcks = kafka.WaitForLocal
	pc.Producer.Return.Successes = true
	pc.Producer.Return.Errors = true
	pc.ChannelBufferSize = 2048
	options := []publisher.Option{
		publisher.WithTopicRegisterHandler([]string{k.topic}),
	}
	for _, opt := range opts {
		options = append(options, opt)
	}

	producer, err := kafka.NewAsyncPublisher(addr, pc, options...)
	if err != nil {
		log.Errorf("Failed to create kafka producer to brokers %v err %v", addr, err)
		return err
	}

	k.producer = producer
	return nil
}

func (k *KafkaProduce) Close() {
	//k.producer.AsyncClose()
}

func (k *KafkaProduce) ProduceEvent(ctx context.Context, key string, pbm proto.MessageV1) error {

	log.Infof("ProduceEvent key:%s topic:%s event:%v", key, pbm.String())

	var data []byte
	data, err := proto.Marshal(pbm)
	if err != nil {
		log.Errorf("ProduceEvent Marshal failed, err: %v  key:%s event:%s", err, key, pbm.String())
	}

	res := k.producer.Publish(ctx, &publisher.ProducerMessage{
		Topic: k.topic,
		Key:   publisher.StringEncoder(key),
		Value: publisher.ByteEncoder(data),
	})
	if res.Err != nil {
		log.Errorf("ProduceContractEvent Publish failed, err: %v, event:%s", err, pbm.String())
	}

	return nil
}
