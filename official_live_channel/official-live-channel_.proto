syntax = "proto3";

package ga.official_live_channel;

import "ga_base.proto";
import "channel/channel_.proto";
import "channel_live_logic/channel-live-logic_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/official-live-channel";

// -------------------- 官频直播间接口 --------------------

// 获取官频房间信息，进房时调用
message GetOfficialLiveChannelInfoReq {
    ga.BaseReq base_req = 1;

    uint32 official_channel_id = 2; // 官频id
}
message GetOfficialLiveChannelInfoResp {
    ga.BaseResp base_resp = 1;

    OfficialLiveChannelRelay relay = 2; // 当前转播
}

// 获取官频节目单，点击节目单时调用
message GetOfficialLiveChannelRelayScheduleReq {
    ga.BaseReq base_req = 1;

    uint32 official_channel_id = 2; // 官频id
    int64 schedule_update_time = 3; // 排班更新时间，用于客户端缓存管理
}
message GetOfficialLiveChannelRelayScheduleResp {
    ga.BaseResp base_resp = 1;

    // 排班信息
    string desc_title = 2; // 官频标题
    string desc_image = 3; // 官频介绍
    repeated OfficialLiveChannelRelaySection sections = 4; // 排班列表
    int64 schedule_update_time = 5; // 排班更新时间，用于客户端缓存管理

    // 当前转播信息，用于当前节目单列表定位
    uint32 relay_section_id = 6; // 当前转播排班序号
    int64 relay_update_time = 7; // 用于客户端判断转播是否变更

    string desc_small_image = 8; // 官频精简介绍
}

message CancelOfficialLiveChannelRelayReq {
    ga.BaseReq base_req = 1;

    uint32 official_channel_id = 2; // 官频id
    uint32 section_id = 3; // 排班序号，为0代表当前转播
}
message CancelOfficialLiveChannelRelayResp {
    ga.BaseResp base_resp = 1;
}

// -------------------- 排班直播间接口 --------------------
message GetLiveChannelRelayReq {
    ga.BaseReq base_req = 1;

    uint32 channel_id = 2; // 排班直播间id
}
message GetLiveChannelRelayResp {
    ga.BaseResp base_resp = 1;

    LiveChannelRelay relay = 2;

    uint32 official_channel_member_count = 3;
    uint32 increase_follower_count = 4;
    int64 current_server_time = 5; // 用于倒计时
}

message ReportRelayLiveChannelAudioReq {
    ga.BaseReq base_req = 1;

    uint32 channel_id = 2; // 排班直播间id
    uint32 mic_id = 3; // 排班直播间
    string stream_id = 4; // 音频流信息
}
message ReportRelayLiveChannelAudioResp {
    ga.BaseResp base_resp = 1;
}

// -------------------- 推送 --------------------
// 当前转播变更, 音频流变更/转播切换时推送, 推送给官频
message OfficialLiveChannelRelayChangeMsg {
    OfficialLiveChannelRelay relay = 1;

    // current 为空，则推送当前排班计划（后续还有排班）/下一排班计划
    GetOfficialLiveChannelRelayScheduleResp advance = 2;
}

// 转播变更，推送给排班直播间
message LiveChannelRelayChangeMsg {
    LiveChannelRelay relay = 1; // 转播

    uint32 official_channel_member_count = 2;
    uint32 increase_follower_count = 3; // 排班期间关注主播的人数
    int64 current_server_time = 4; // 用于倒计时
}

// 公屏控制同步
message OfficialLiveChannelModifyMsg {
    // 是否打开了房间锁屏开关 (PS:
    // 目前仅在进房请求和拉取详情请求中会返回该字段内容)
    bool is_open_lock_screen_switch = 1;

    // 是否打开了房间禁止发图开关 (PS:
    // 目前仅在进房请求和拉取详情请求中会返回该字段内容)
    bool is_open_disable_attachment_msg_switch = 2;

    // 是否打开了房间发言用户等级限制开关 (PS:
    // 目前仅在进房请求和拉取详情请求中会返回该字段内容)
    bool is_open_disable_level_lmt_switch = 3;

    int64 update_time = 4; // 更新时间，用于控制同步的时序

    bool open_normal_queue_up_mic_switch_flag = 5; //是否开启排麦
}

// 排班直播间麦位/音频变更
message LiveChannelMicChangeMsg {
    uint32 channel_id = 1; // 排班直播间id

    repeated RelayLiveChannelAudio audios = 2; // 转播直播间音频流信息

    uint32 mic_mode = 3; // 当前房间麦模式
    int64 mic_server_time_ms = 4; // 64bit 毫秒级 服务器时间
    repeated ga.channel.MicrSpace all_mic = 5; // 全量麦位信息

    bool open_normal_queue_up_mic_switch_flag = 6; //是否开启排麦
}

// 官频/排班直播间管理员变更
message OfficialLiveChannelOtherChannelAdminChangeMsg {
    uint32 channel_id = 1;
    map<uint32, uint32> admin_list = 2;
    int64 admin_server_time_ms = 3; // 64bit 毫秒级 服务器时间
}

// -------------------- 基础数据结构 --------------------
message RelayLiveChannelAudio {
    uint32 uid = 1;
    string stream_id = 2;
}

message OfficialLiveChannelRelaySchedule {
    repeated OfficialLiveChannelRelaySection sections = 1; // 排班列表

    int64 update_time = 2; // 排班更新时间，用于客户端缓存管理
}

message OfficialLiveChannelRelaySection {
    uint32 section_id = 1; // 排班序号

    uint32 channel_id = 2; // 排班直播间id

    uint32 anchor_id = 3; // 排班主播id
    string anchor_account = 4; // 排班主播账号
    string anchor_nickname = 5; // 排班主播昵称

    string icon = 6; // 排班图标

    string introduction_text = 7; // 排班直播介绍文案
    string introduction_img = 8; // 排班直播介绍图

    int64 start_time = 9; // 开始时间戳
    int64 end_time = 10; // 结束时间戳

    int32 anchor_sex = 11; //主播性别，1男，2女

    uint32 introduction_img_height = 12; // 排班直播介绍图高度
    uint32 introduction_img_width = 13; // 排班直播介绍图宽度
}

enum OfficialLiveChannelRelayStatus {
    UNKNOWN = 0; // 未转播
    WAITING = 1; // 等待中，节目切换时直播间未开播/正在pk触发
    RUNNING = 2; // 转播中
    BROKEN = 3; // 已中断，管理员中断触发
    PAUSE = 4; // 已暂停
}

// 官方频道当前转播
message OfficialLiveChannelRelay {
    // 当前排班信息
    OfficialLiveChannelRelaySection current = 1; // 当前节目，此处返回的结构体不包含显示不需要的信息
    OfficialLiveChannelRelaySection next = 2; // 下一节目，此处返回的结构体不包含显示不需要的信息

    // 当前转播信息
    repeated RelayLiveChannelAudio audios = 3; // 转播直播间音频流信息
    OfficialLiveChannelRelayStatus relay_status = 4; // 转播状态
    int64 update_time = 5; // 当前转播更新时间

    // 麦位信息，只在切换转播和获取房间信息时提供
    uint32 mic_mode = 6; // 当前房间麦模式
    int64 mic_server_time_ms = 7; // 64bit 毫秒级 服务器时间
    repeated ga.channel.MicrSpace all_mic = 8; // 全量麦位信息

    bool open_normal_queue_up_mic_switch_flag = 9; //是否开启排麦

    string relay_status_msg = 10;

    // 推荐信息，未转播/转播等待中时提供
    repeated ga.channel_live_logic.RecommendChannel channel_list = 11; //主播推荐

    // 排版直播间管理员列表
    map<uint32, uint32> live_channel_admin_list = 12;
    int64 admin_server_time_ms = 13; // 64bit 毫秒级 服务器时间
}

// 语音房转播信息
message LiveChannelRelay {
    uint32 official_channel_id = 1; // 官频id

    int64 start_time = 2; // 开始时间戳
    int64 end_time = 3; // 结束时间戳

    OfficialLiveChannelRelayStatus relay_status = 4; // 转播状态

    int64 update_time = 5; // 转播更新时间

    // 官频管理员列表
    map<uint32, uint32> official_live_channel_admin_list = 6;
    int64 admin_server_time_ms = 7; // 64bit 毫秒级 服务器时间

    string official_channel_icon_md5 = 8; // 官频房间头像，切换时推送
}

//官频标识
message GetOfficialChannelDescribeReq {
    ga.BaseReq base_req = 1;
    repeated uint32 official_channel_ids = 2; // 官频id
}
message GetOfficialChannelDescribeResp {
    ga.BaseResp base_resp = 1;
    map<uint32, OfficialChannelDescribe> official_channel_desc = 2 ;   //
}

message OfficialChannelDescribe{
    string identity_text = 1 ; //标识文案，如：音乐官频
    string status_text = 2 ; //状态文案
}