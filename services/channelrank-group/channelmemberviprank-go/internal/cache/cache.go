package cache

import (
	"fmt"
	channelGA "golang.52tt.com/protocol/app/channel"
	"strconv"
	"time"

	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/metrics"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channelmemberviprank-go"
)

const (
	getHourRankScript = `
		local rank = redis.call('ZREVRANK', KEYS[1], ARGV[1])
		local score = redis.call('ZSCORE', KEYS[1], ARGV[1])
		local count = redis.call('ZCARD', KEYS[1])

		local result = {
			[1]= rank,
			[2]= score,
			[3]= count,
		}
		return result
	`

	resetHourRankScoreScript = `
		redis.call('ZADD', KEYS[1], ARGV[1], ARGV[2])
		redis.call('ZADD', KEYS[2], ARGV[1], ARGV[2])
		redis.call('ZADD', KEYS[3], ARGV[1], ARGV[3])
		redis.call('ZADD', KEYS[4], ARGV[1], ARGV[3])
		redis.call('EXPIRE', KEYS[1], ARGV[4])
		redis.call('EXPIRE', KEYS[2], ARGV[4])
		redis.call('EXPIRE', KEYS[3], ARGV[4])
		redis.call('EXPIRE', KEYS[4], ARGV[4])
	`
)

type Cache struct {
	redisCli *redis.Client
	*LocalCache
}

func NewCache(redisCli *redis.Client) (*Cache, error) {
	_, err := redisCli.Ping().Result()
	if err != nil {
		return nil, err
	}

	return &Cache{
		redisCli:   redisCli,
		LocalCache: NewLocalCache(),
	}, nil
}

// 检查小时榜是否推送过, 返回false表示未推送
func (c *Cache) CheckHourRankPushFlag(id uint32) bool {
	key := GenHourRankPushKey(id)
	ok, err := c.redisCli.SetNX(key, "1", time.Second*3600).Result()
	if err != nil {
		log.Errorf("CheckHourRankPushFlag fail %v, id=%d", err, id)
		return true
	}
	return !ok
}

// 检查小时榜是否结算过
func (c *Cache) CheckHourRankSettleFlag(hour uint32) bool {
	key := GenHourRankSettleKey(hour)
	ok, err := c.redisCli.SetNX(key, "1", time.Second*3600*2).Result()
	if err != nil {
		log.Errorf("CheckHourRankSettleFlag fail %v, hour=%d", err, hour)
		return true
	}
	return !ok
}

type IChannelHourInfo interface {
	GetMember() uint32
	GetScore() uint32
	GetRank() uint32
	GetTagId() uint32
}

type ChannelHourInfo struct {
	ChannelId uint32
	Score     uint32
	Rank      uint32
	TagId     uint32
}

func (c *ChannelHourInfo) String() string    { return utils.ToJson(c) }
func (c *ChannelHourInfo) GetMember() uint32 { return c.ChannelId }
func (c *ChannelHourInfo) GetScore() uint32  { return c.Score }
func (c *ChannelHourInfo) GetRank() uint32   { return c.Rank }
func (c *ChannelHourInfo) GetTagId() uint32  { return c.TagId }

type ChannelHourMemberInfo struct {
	Uid   uint32
	Score uint32
	Rank  uint32
	TagId uint32
}

func (c *ChannelHourMemberInfo) GetMember() uint32 { return c.Uid }
func (c *ChannelHourMemberInfo) GetScore() uint32  { return c.Score }
func (c *ChannelHourMemberInfo) GetRank() uint32   { return c.Rank }
func (c *ChannelHourMemberInfo) GetTagId() uint32  { return c.TagId }

// GetChannelHourRankList 获取房间小时榜
func (c *Cache) GetChannelHourRankList(hour, tagId, begin, limit uint32, diemType channelGA.MultiDimChannelRankType) ([]IChannelHourInfo, error) {
	key := GenHourRankKey(hour, tagId)
	if diemType == channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_NEW_STAR {
		key = GenHourNewStarRankKey(hour)
	}
	list := make([]IChannelHourInfo, 0)
	res, err := c.redisCli.ZRevRangeByScoreWithScores(key,
		redis.ZRangeBy{Min: "0", Max: "+inf", Offset: int64(begin), Count: int64(limit)}).Result()
	if err != nil {
		return list, err
	}

	rank := uint32(1)
	for _, info := range res {
		cid, _ := strconv.Atoi(info.Member.(string))

		if cid > 0 && info.Score > 0 {
			list = append(list, &ChannelHourInfo{
				ChannelId: uint32(cid),
				Score:     uint32(info.Score),
				Rank:      rank,
			})
			rank++
		}
	}
	return list, nil
}

func (c *Cache) GetChannelHourRankTopN(hour, tagId uint32, topN int, diemType channelGA.MultiDimChannelRankType) ([]IChannelHourInfo, error) {
	key := GenHourRankKey(hour, tagId)
	if diemType == channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_NEW_STAR {
		key = GenHourNewStarRankKey(hour)
	}
	var list []IChannelHourInfo
	res, err := c.redisCli.ZRevRangeByScoreWithScores(key,
		redis.ZRangeBy{Min: "0", Max: "+inf", Offset: 0, Count: int64(topN)}).Result()
	if err != nil {
		return list, err
	}

	rank := uint32(1)
	for _, info := range res {
		cid, _ := strconv.Atoi(info.Member.(string))
		if cid > 0 && info.Score > 0 {
			list = append(list, &ChannelHourInfo{
				ChannelId: uint32(cid),
				Score:     uint32(info.Score),
				Rank:      rank,
			})
			rank++
		}
	}
	return list, nil
}

// GetChannelHourMemberRankList 获取房间小时成员榜
func (c *Cache) GetChannelHourMemberRankList(hour, tagId, begin, limit uint32) ([]IChannelHourInfo, error) {
	key := GenHourMemberRankKey(hour, tagId)
	list := make([]IChannelHourInfo, 0)
	res, err := c.redisCli.ZRevRangeByScoreWithScores(key,
		redis.ZRangeBy{Min: "0", Max: "+inf", Offset: int64(begin), Count: int64(limit)}).Result()
	if err != nil {
		return list, err
	}

	rank := uint32(1)
	for _, info := range res {
		uid, _ := strconv.Atoi(info.Member.(string))

		if uid > 0 && info.Score > 0 {
			list = append(list, &ChannelHourMemberInfo{
				Uid:   uint32(uid),
				Score: uint32(info.Score),
				Rank:  rank,
			})
			rank++
		}
	}
	return list, nil
}

func (c *Cache) GetChannelHourMemberRankTopN(hour, tagId uint32, topN int) ([]IChannelHourInfo, error) {
	key := GenHourMemberRankKey(hour, tagId)
	var list []IChannelHourInfo
	res, err := c.redisCli.ZRevRangeByScoreWithScores(key,
		redis.ZRangeBy{Min: "0", Max: "+inf", Offset: 0, Count: int64(topN)}).Result()
	if err != nil {
		return list, err
	}

	rank := uint32(1)
	for _, info := range res {
		uid, _ := strconv.Atoi(info.Member.(string))
		if uid > 0 && info.Score > 0 {
			list = append(list, &ChannelHourMemberInfo{
				Uid:   uint32(uid),
				Score: uint32(info.Score),
				Rank:  rank,
			})
			rank++
		}
	}
	return list, nil
}

// 获取该房间的小时榜排名及分数
func (c *Cache) GetChannelHourRankById(channelID, hour, tagId uint32, diemType channelGA.MultiDimChannelRankType) (
	score, rank, count uint32, err error) {
	key := GenHourRankKey(hour, tagId)

	fmt.Println("========> ", diemType)
	if diemType == channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_NEW_STAR {
		key = GenHourNewStarRankKey(hour)
	}
	keys := []string{key}
	args := []interface{}{channelID}
	res, err := c.redisCli.Eval(getHourRankScript, keys, args...).Result()
	if err != nil {
		return
	}
	rx := res.([]interface{})

	if rx[0] != nil {
		rank = uint32(rx[0].(int64)) + 1
	}

	if rx[1] != nil {
		num, _ := strconv.Atoi(rx[1].(string))
		score = uint32(num)
	}

	if rx[2] != nil {
		count = uint32(rx[2].(int64))
	}

	return
}

func (c *Cache) GetChannelHourRankByIdV2(channelID, hour, tagId uint32, diemType channelGA.MultiDimChannelRankType) (
	score, rank, count uint32, err error) {
	key := GenHourRankKey(hour, tagId)
	if diemType == channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_NEW_STAR {
		key = GenHourNewStarRankKey(hour)
	}
	pipeline := c.redisCli.Pipeline()
	retRankCmd := pipeline.ZRevRank(key, strconv.Itoa(int(channelID)))
	retScoreCmd := pipeline.ZScore(key, strconv.Itoa(int(channelID)))
	retCountCmd := pipeline.ZCard(key)
	_, err = pipeline.Exec()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("GetChannelHourRankByIdV2 err %v, channelID=%d, hour=%d, tagId=%d", err, channelID, hour, tagId)
			return
		}
	}
	retRank, err := retRankCmd.Result()
	if err != nil && err == redis.Nil {
		rank = 0
	} else {
		rank = uint32(retRank + 1)
	}
	retScore, _ := retScoreCmd.Result()
	retCount, _ := retCountCmd.Result()

	return uint32(retScore), rank, uint32(retCount), nil
}

func (c *Cache) GetChannelHourMemberRankById(uid, hour, tagId, diemType uint32) (
	score, rank, count uint32, err error) {
	key := GenHourMemberRankKey(hour, tagId)
	if diemType == uint32(channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_NEW_STAR) {
		key = GenHourNewStarRankKey(hour)
	}
	pipeline := c.redisCli.Pipeline()
	retRankCmd := pipeline.ZRevRank(key, strconv.Itoa(int(uid)))
	retScoreCmd := pipeline.ZScore(key, strconv.Itoa(int(uid)))
	retCountCmd := pipeline.ZCard(key)
	_, err = pipeline.Exec()

	if err != nil {
		if err != redis.Nil {
			log.Errorf("GetChannelHourMemberRankById err %v, uid=%d, hour=%d, tagId=%d", err, uid, hour, tagId)
			return
		}
	}
	retRank, err := retRankCmd.Result()
	if err != nil && err == redis.Nil {
		// 注意这里很关键，nil时排名是0
		rank = 0
	} else {
		rank = uint32(retRank + 1)
	}
	retScore, _ := retScoreCmd.Result()
	retCount, _ := retCountCmd.Result()

	log.Debugf("GetChannelHourMemberRankById uid=%d, hour=%d, tagId=%d, rank=%d, score=%d, count=%d", uid, hour, tagId, rank, retScore, retCount)

	return uint32(retScore), rank, uint32(retCount), nil
}

// 获取小时榜中指定名次的房间的分数
func (c *Cache) GetChannelHourScoreByRank(channelID, hour, tagId, rank uint32, diemType channelGA.MultiDimChannelRankType) (score uint32, err error) {
	key := GenHourRankKey(hour, tagId)
	if diemType == channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_NEW_STAR {
		key = GenHourNewStarRankKey(hour)
	}
	res, err := c.redisCli.ZRevRangeWithScores(key, int64(rank)-1, int64(rank)).Result()
	if err != nil {
		return
	}
	if len(res) > 0 {
		score = uint32(res[0].Score)
	}
	return
}

func (c *Cache) GetChannelHourMemberScoreByRank(hour, tagId, rank, diemType uint32) (score uint32, err error) {
	key := GenHourMemberRankKey(hour, tagId)
	if diemType == uint32(channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_NEW_STAR) {
		key = GenHourNewStarRankKey(hour)
	}
	res, err := c.redisCli.ZRevRangeWithScores(key, int64(rank)-1, int64(rank)).Result()
	if err != nil {
		return
	}
	if len(res) > 0 {
		score = uint32(res[0].Score)
	}
	return
}

// AddHourRankScore 增加小时榜分数
func (c *Cache) AddHourRankScore(channelId, tagId, memberUid, hour uint32, totalConsumeValue uint64, isLiveChannel, isMember, isNewStar bool) error {
	hourRankKey := GenHourRankKey(hour, 0)                    // 增加小时总榜分数
	hourTagRankKey := GenHourRankKey(hour, tagId)             // 增加品类小时榜分数
	hourMemberRankKey := GenHourMemberRankKey(hour, 0)        // 增加小时总榜成员榜分数
	hourMemberTagRankKey := GenHourMemberRankKey(hour, tagId) // 增加品类小时成员榜分数
	hourNewStarKey := GenHourNewStarRankKey(hour)             // 新星榜

	score := float64(totalConsumeValue)
	cid := strconv.Itoa(int(channelId))
	uid := strconv.Itoa(int(memberUid))
	expireTime := time.Duration(3600*3) * time.Second

	if _, err := c.redisCli.TxPipelined(func(pipe redis.Pipeliner) error {
		// 房间榜
		pipe.ZIncrBy(hourRankKey, score, cid)
		pipe.ZIncrBy(hourTagRankKey, score, cid)
		pipe.Expire(hourRankKey, expireTime)
		pipe.Expire(hourTagRankKey, expireTime)

		// 直播房-新星榜
		if isLiveChannel && isNewStar {
			pipe.ZIncrBy(hourNewStarKey, score, cid)
			pipe.Expire(hourNewStarKey, expireTime)
		}

		// 非直播房-成员榜
		if isMember {
			pipe.ZIncrBy(hourMemberRankKey, score, uid)
			pipe.ZIncrBy(hourMemberTagRankKey, score, uid)
			pipe.Expire(hourMemberRankKey, expireTime)
			pipe.Expire(hourMemberTagRankKey, expireTime)
		}

		return nil
	}); err != nil {
		log.Errorf("AddHourRankScore err %v, channelId=%d, tagId=%d, uid=%d, hour=%d", err, channelId, tagId, memberUid, hour)
		return err
	}
	return nil
}

// AddHourRankChannelAwardScore 增加小时榜奖励分数
func (c *Cache) AddHourRankChannelAwardScore(channelId, hour, score, tagId uint32, isNewStar bool, lockKey string) error {
	hourRankKey := GenHourRankKey(hour, 0)                            // 增加小时总榜分数
	hourTagRankKey := GenHourRankKey(hour, tagId)                     // 增加分类小时榜分数
	hourRankAwardScoreKey := GenHourRankAwardScoreKey(hour, 0)        // 增加小时总榜奖励分数
	hourTagRankAwardScoreKey := GenHourRankAwardScoreKey(hour, tagId) // 增加分类小时榜奖励分数
	hourNewStarKey := GenHourNewStarRankKey(hour)                     // 新星榜

	cid := strconv.Itoa(int(channelId))
	expireTime := time.Duration(3600*3) * time.Second

	log.Debugf("AddHourRankChannelAwardScore hourRankKey=%s, hourTagRankKey=%s, hourRankAwardScoreKey=%s, hourTagRankAwardScoreKey=%s, hourNewStarKey=%s, channelId=%d, tagId=%d, hour=%d, score=%d, lockKey:%s",
		hourRankKey, hourTagRankKey, hourRankAwardScoreKey, hourTagRankAwardScoreKey, hourNewStarKey, channelId, tagId, hour, score, lockKey)

	if lockKey != "" {
		getLock, err := c.redisCli.SetNX(lockKey, "1", time.Hour*3).Result()
		if err != nil {
			log.Errorf("AddHourRankChannelAwardScore SetNX err %v, channelId=%d, tagId=%d, hour=%d", err, channelId, tagId, hour)
			return err
		}
		if !getLock {
			log.Warnf("AddHourRankChannelAwardScore no lock, channelId=%d, tagId=%d, hour=%d", channelId, tagId, hour)
			return nil
		}
	}

	if _, err := c.redisCli.TxPipelined(func(pipe redis.Pipeliner) error {
		// 总榜-房间榜
		pipe.ZIncrBy(hourRankKey, float64(score), cid)
		pipe.Expire(hourRankKey, expireTime)
		// 总榜-奖励分
		pipe.HIncrBy(hourRankAwardScoreKey, cid, int64(score))
		pipe.Expire(hourRankAwardScoreKey, expireTime)

		if tagId > 0 {
			// 品类榜-房间榜
			pipe.ZIncrBy(hourTagRankKey, float64(score), cid)
			pipe.Expire(hourTagRankKey, expireTime)
			// 品类榜-奖励分
			pipe.HIncrBy(hourTagRankAwardScoreKey, cid, int64(score))
			pipe.Expire(hourTagRankAwardScoreKey, expireTime)
		}
		// 新星榜
		if isNewStar {
			pipe.ZIncrBy(hourNewStarKey, float64(score), cid)
			pipe.Expire(hourNewStarKey, expireTime)
		}
		return nil
	}); err != nil {
		log.Errorf("AddHourRankScore err %v, channelId=%d, tagId=%d, hour=%d", err, channelId, tagId, hour)
		return err
	}
	return nil
}

// AddHourRankMemberAwardScore 增加小时榜成员奖励分数
func (c *Cache) AddHourRankMemberAwardScore(uid, hour, score uint32, tagIds []uint32, lockKey string) error {
	hourMemberRankKey := GenHourMemberRankKey(hour, 0)                     // 增加小时总榜成员榜分数
	hourMemberRankAwardScoreKey := GenHourMemberRankAwardScoreKey(hour, 0) // 增加小时总榜成员榜奖励分数
	expireTime := time.Hour * 3

	uidStr := strconv.Itoa(int(uid))

	log.Debugf("AddHourRankMemberAwardScore hourMemberRankKey=%s, hourMemberRankAwardScoreKey=%s, uid=%d, hour=%d, score=%d",
		hourMemberRankKey, hourMemberRankAwardScoreKey, uid, hour, score)

	if lockKey != "" {
		getLock, err := c.redisCli.SetNX(lockKey, "1", time.Hour*3).Result()
		if err != nil {
			log.Errorf("AddHourRankMemberAwardScore SetNX err %v, uid=%d, tagId=%+v, hour=%d, lockKey=%s", err, uid, tagIds, hour, lockKey)
			return err
		}
		if !getLock {
			log.Warnf("AddHourRankMemberAwardScore no lock, uid=%d, tagId=%+v, hour=%d, lockKey=%s", uid, tagIds, hour, lockKey)
			return nil
		}
	}

	if _, err := c.redisCli.TxPipelined(func(pipe redis.Pipeliner) error {
		for _, tagId := range tagIds {
			if tagId == 0 {
				continue
			}
			hourMemberTagRankKey := GenHourMemberRankKey(hour, tagId)                     // 增加品类小时成员榜分数
			hourMemberTagRankAwardScoreKey := GenHourMemberRankAwardScoreKey(hour, tagId) // 增加品类小时成员榜奖励分数

			log.Debugf("AddHourRankMemberAwardScore hourMemberTagRankKey=%s, hourMemberTagRankAwardScoreKey=%s, uid=%d, tagId=%d, hour=%d, score=%d",
				hourMemberTagRankKey, hourMemberTagRankAwardScoreKey, uid, tagId, hour, score)

			// 品类成员榜
			pipe.ZIncrBy(hourMemberTagRankKey, float64(score), uidStr)
			pipe.Expire(hourMemberTagRankKey, expireTime)
			// 品类奖励分
			pipe.HIncrBy(hourMemberTagRankAwardScoreKey, uidStr, int64(score))
			pipe.Expire(hourMemberTagRankAwardScoreKey, expireTime)
		}
		// 成员榜总榜
		pipe.ZIncrBy(hourMemberRankKey, float64(score), uidStr)
		pipe.Expire(hourMemberRankKey, expireTime)
		// 总榜奖励分
		pipe.HIncrBy(hourMemberRankAwardScoreKey, uidStr, int64(score))
		pipe.Expire(hourMemberRankAwardScoreKey, expireTime)
		return nil
	}); err != nil {
		log.Errorf("AddHourRankMemberAwardScore err %v, uid=%d, hour=%d", err, uid, hour)
		return err
	}
	return nil
}

func (c *Cache) BatchGetHourRankChannelAwardScore(channelIds []uint32, hour, tagId uint32) (map[uint32]uint32, error) {
	channelIdKeys := make([]string, 0)
	m := make(map[uint32]uint32)
	if len(channelIds) == 0 {
		return m, nil
	}

	key := GenHourRankAwardScoreKey(hour, tagId)
	for _, v := range channelIds {
		channelIdKeys = append(channelIdKeys, strconv.Itoa(int(v)))
	}
	ret, err := c.redisCli.HMGet(key, channelIdKeys...).Result()
	if err != nil {
		return nil, err
	}

	for i, v := range ret {
		if v != nil {
			num, _ := strconv.Atoi(v.(string))
			m[channelIds[i]] = uint32(num)
		}
	}
	return m, nil
}

func (c *Cache) BatchGetHourRankMemberAwardScore(uids []uint32, hour, tagId uint32) (map[uint32]uint32, error) {
	uidKeys := make([]string, 0)
	m := make(map[uint32]uint32)
	if len(uids) == 0 {
		return m, nil
	}

	key := GenHourMemberRankAwardScoreKey(hour, tagId)
	for _, v := range uids {
		uidKeys = append(uidKeys, strconv.Itoa(int(v)))
	}
	ret, err := c.redisCli.HMGet(key, uidKeys...).Result()
	if err != nil {
		return nil, err
	}

	for i, v := range ret {
		if v != nil {
			num, _ := strconv.Atoi(v.(string))
			m[uids[i]] = uint32(num)
		}
	}
	return m, nil
}

// 重置小时榜分数
func (c *Cache) ResetHourRankScore(channelID, tagId, hour uint32, totalConsumeValue uint64) error {
	keys := []string{
		GenHourRankKey(hour, 0),           // 增加小时总榜分数
		GenHourRankKey(hour, tagId),       // 增加分类小时榜分数
		GenHourMemberRankKey(hour, 0),     // 增加小时总榜成员榜分数
		GenHourMemberRankKey(hour, tagId), // 增加分类小时成员榜分数
	}
	args := []interface{}{totalConsumeValue, channelID, 3600 * 3}
	_, err := c.redisCli.Eval(resetHourRankScoreScript, keys, args...).Result()
	if err != nil && err == redis.Nil { // 怎么会有redis.nil报错
		return nil
	}
	return err
}

// 取每个分类榜单的TOP1
func (c *Cache) BatchGetHourRankTop1(hour uint32, tagIdList []uint32) ([]*pb.GetChannelHourRankTop1Info, error) {
	defer metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(0, "redis.pipeline.BatchGetHourRankTop1")).End()

	list := []*pb.GetChannelHourRankTop1Info{}
	pipe := c.redisCli.Pipeline()

	for _, tagId := range tagIdList {
		key := GenHourRankKey(hour, tagId)
		members, err := c.redisCli.ZRevRangeWithScores(key, 0, 0).Result() // O(log(N))
		if err != nil {
			return list, err
		}

		info := &pb.GetChannelHourRankTop1Info{TagId: tagId}
		if len(members) > 0 {
			cid, _ := strconv.Atoi(members[0].Member.(string))
			info.ChannelId = uint32(cid)
			info.Score = uint32(members[0].Score)
		}
		list = append(list, info)
	}

	_, err := pipe.Exec()
	return list, err
}

func (c *Cache) DelHourRankScore(tagId, hour uint32) error {
	return c.redisCli.Del(GenHourRankKey(hour, tagId)).Err()
}

func (c *Cache) ResetHourRank(hour uint32, tagId2ChannelRank map[uint32][]*ChannelHourInfo) error {
	pipe := c.redisCli.Pipeline()
	key0 := GenHourRankKey(hour, 0)
	pipe.Del(key0)
	for tagId, list := range tagId2ChannelRank {
		key := GenHourRankKey(hour, tagId)
		memberList := make([]redis.Z, 0)
		for _, info := range list {
			memberList = append(memberList, redis.Z{Score: float64(info.Score), Member: info.ChannelId})
		}
		pipe.Del(key)
		pipe.ZAdd(key, memberList...)
		pipe.ZAdd(key0, memberList...)
		pipe.Expire(key, time.Duration(time.Second)*3600*3)
	}
	pipe.Expire(key0, time.Duration(time.Second)*3600*3)
	_, err := pipe.Exec()
	return err
}

func (c *Cache) ResetHourMemberRank(hour uint32, tagId2MemberUidRank map[uint32][]*ChannelHourMemberInfo) error {
	pipe := c.redisCli.Pipeline()
	key0 := GenHourMemberRankKey(hour, 0)
	pipe.Del(key0)
	for tagId, list := range tagId2MemberUidRank {
		key := GenHourMemberRankKey(hour, tagId)
		memberList := make([]redis.Z, 0)
		for _, info := range list {
			memberList = append(memberList, redis.Z{Score: float64(info.Score), Member: info.Uid})
		}
		pipe.Del(key)
		pipe.ZAdd(key, memberList...)
		pipe.ZAdd(key0, memberList...)
		pipe.Expire(key, time.Duration(time.Second)*3600*3)
	}
	pipe.Expire(key0, time.Duration(time.Second)*3600*3)
	_, err := pipe.Exec()
	return err
}

func (c *Cache) IncrHourRankPresentAwardScoreCount(hour, presentId, count, score, uid, cid uint32, diemType channelGA.MultiDimChannelRankType) (uint32, uint32, error) {
	countKey, totalKey := GenHourPresentAwardScoreKey(hour, presentId, uid, cid, diemType)
	log.Debugf("IncrHourRankPresentAwardScoreCount countKey=%s, totalKey=%s", countKey, totalKey)

	countRet := c.redisCli.IncrBy(countKey, int64(count)).Val()
	totalRet := c.redisCli.IncrBy(totalKey, int64(score)).Val()
	c.redisCli.Expire(countKey, time.Hour*3)
	c.redisCli.Expire(totalKey, time.Hour*3)

	return uint32(countRet), uint32(totalRet), nil
}
