package internal

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/channel"
	channellivemgr "golang.52tt.com/clients/channel-live-mgr"
	channellottery "golang.52tt.com/clients/channel-lottery"
	channelmemberviprank_go "golang.52tt.com/clients/channelmemberviprank-go"
	"golang.52tt.com/clients/guild"
	headimage "golang.52tt.com/clients/headimage"
	maskedpklive "golang.52tt.com/clients/masked-pk-live"
	maskedpksvr "golang.52tt.com/clients/masked-pk-svr"
	mockchannel "golang.52tt.com/clients/mocks/channel"
	mockonlinerank "golang.52tt.com/clients/mocks/channel-online-rank"
	revenue_ext_game "golang.52tt.com/clients/revenue-ext-game"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	ga "golang.52tt.com/protocol/app/channel"
	accountsvr "golang.52tt.com/protocol/services/accountsvr"
	channelonlinerank "golang.52tt.com/protocol/services/channelonlinerank"
	channelonlinerankpb "golang.52tt.com/protocol/services/channelonlinerank"
	channelsvrpb "golang.52tt.com/protocol/services/channelsvr"
	"golang.52tt.com/protocol/services/youknowwho"
	conf2 "golang.52tt.com/services/channelrank-group/channel-rank-logic/internal/conf"
	"golang.52tt.com/services/channelrank-group/channel-rank-logic/internal/rpc"
	"golang.52tt.com/services/channelrank-group/common/dyconf"
)

// go test -timeout 30s -run ^TestChannelRankLogic_ChannelGetMemberList$ golang.52tt.com/services/channelrank-group/channel-rank-logic/internal/server -v -count=1
func TestChannelRankLogic_ChannelGetMemberList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{})
	mockchannelCli := mockchannel.NewMockIClient(ctl)
	mockonrankCli := mockonlinerank.NewMockIClient(ctl)

	channelInfo := &channelsvrpb.ChannelSimpleInfo{}

	// ChannelonlinerankCli

	rankresp := &channelonlinerankpb.GetMemberRankListResp{
		MemberList: []*channelonlinerank.ChannelMemberRank{
			{
				Uid: 1,
			},
		},
	}

	err := protocol.NewExactServerError(nil, -2, "")
	gomock.InOrder(
		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo, nil),
		mockonrankCli.EXPECT().GetMemberRankList(gomock.Any(), gomock.Any()).Return(rankresp, err),
	)

	type fields struct {
		ctx               context.Context
		dyconfig          dyconf.ISDyConfigHandler
		pkdyconfig        conf2.ISDyConfigHandler
		rankCli           channelmemberviprank_go.IClient
		channelCli        channel.IClient
		accountCli        account.IClient
		guildCli          guild.IClient
		channellotteryCli channellottery.IClient
		channellivemgrCli channellivemgr.IClient
		headimageCli      headimage.IClient
		maskedpksvrCli    maskedpksvr.IClient
		maskedpkliveCli   maskedpklive.IClient
		revenueExtGameCli revenue_ext_game.IClient
	}
	type args struct {
		ctx context.Context
		req *ga.ChannelGetMemberListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ga.ChannelGetMemberListResp
		wantErr bool
	}{
		{
			fields: fields{
				channelCli: mockchannelCli,
			},
			args: args{
				ctx: ctx,
				req: &ga.ChannelGetMemberListReq{
					ReqCnt: 100,
				},
			},
			want:    &ga.ChannelGetMemberListResp{},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		rpc.ChannelonlinerankCli = mockonrankCli
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelRankLogic{
				ctx:               tt.fields.ctx,
				dyconfig:          tt.fields.dyconfig,
				pkdyconfig:        tt.fields.pkdyconfig,
				rankCli:           tt.fields.rankCli,
				channelCli:        tt.fields.channelCli,
				accountCli:        tt.fields.accountCli,
				guildCli:          tt.fields.guildCli,
				channellotteryCli: tt.fields.channellotteryCli,
				channellivemgrCli: tt.fields.channellivemgrCli,
				headimageCli:      tt.fields.headimageCli,
				maskedpksvrCli:    tt.fields.maskedpksvrCli,
				maskedpkliveCli:   tt.fields.maskedpkliveCli,
				revenueExtGameCli: tt.fields.revenueExtGameCli,
			}
			got, err := s.ChannelGetMemberList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ChannelRankLogic.ChannelGetMemberList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				//t.Errorf("ChannelRankLogic.ChannelGetMemberList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRankLogic_getChannelOnlineRankMemberList(t *testing.T) {
	type fields struct {
		ctx               context.Context
		dyconfig          dyconf.ISDyConfigHandler
		pkdyconfig        conf2.ISDyConfigHandler
		rankCli           channelmemberviprank_go.IClient
		channelCli        channel.IClient
		accountCli        account.IClient
		guildCli          guild.IClient
		channellotteryCli channellottery.IClient
		channellivemgrCli channellivemgr.IClient
		headimageCli      headimage.IClient
		maskedpksvrCli    maskedpksvr.IClient
		maskedpkliveCli   maskedpklive.IClient
		revenueExtGameCli revenue_ext_game.IClient
	}
	type args struct {
		ctx       context.Context
		opUid     uint32
		channelId uint32
		beginIdx  uint32
		reqLimit  uint32
		resp      *ga.ChannelGetMemberListResp
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelRankLogic{
				ctx:               tt.fields.ctx,
				dyconfig:          tt.fields.dyconfig,
				pkdyconfig:        tt.fields.pkdyconfig,
				rankCli:           tt.fields.rankCli,
				channelCli:        tt.fields.channelCli,
				accountCli:        tt.fields.accountCli,
				guildCli:          tt.fields.guildCli,
				channellotteryCli: tt.fields.channellotteryCli,
				channellivemgrCli: tt.fields.channellivemgrCli,
				headimageCli:      tt.fields.headimageCli,
				maskedpksvrCli:    tt.fields.maskedpksvrCli,
				maskedpkliveCli:   tt.fields.maskedpkliveCli,
				revenueExtGameCli: tt.fields.revenueExtGameCli,
			}
			if err := s.getChannelOnlineRankMemberList(tt.args.ctx, tt.args.opUid, tt.args.channelId, tt.args.beginIdx, tt.args.reqLimit, tt.args.resp); (err != nil) != tt.wantErr {
				t.Errorf("ChannelRankLogic.getChannelOnlineRankMemberList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestChannelRankLogic_fillMemberExtentInfo(t *testing.T) {
	type fields struct {
		ctx               context.Context
		dyconfig          dyconf.ISDyConfigHandler
		pkdyconfig        conf2.ISDyConfigHandler
		rankCli           channelmemberviprank_go.IClient
		channelCli        channel.IClient
		accountCli        account.IClient
		guildCli          guild.IClient
		channellotteryCli channellottery.IClient
		channellivemgrCli channellivemgr.IClient
		headimageCli      headimage.IClient
		maskedpksvrCli    maskedpksvr.IClient
		maskedpkliveCli   maskedpklive.IClient
		revenueExtGameCli revenue_ext_game.IClient
	}
	type args struct {
		ctx         context.Context
		rankUidList []uint32
		operMember  *ga.ChannelMemberInfo
		memberList  []*ga.ChannelMemberInfo
		onlineReq   *channelonlinerank.GetMemberRankListReq
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*ga.ChannelMemberInfo
		want1  uint32
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelRankLogic{
				ctx:               tt.fields.ctx,
				dyconfig:          tt.fields.dyconfig,
				pkdyconfig:        tt.fields.pkdyconfig,
				rankCli:           tt.fields.rankCli,
				channelCli:        tt.fields.channelCli,
				accountCli:        tt.fields.accountCli,
				guildCli:          tt.fields.guildCli,
				channellotteryCli: tt.fields.channellotteryCli,
				channellivemgrCli: tt.fields.channellivemgrCli,
				headimageCli:      tt.fields.headimageCli,
				maskedpksvrCli:    tt.fields.maskedpksvrCli,
				maskedpkliveCli:   tt.fields.maskedpkliveCli,
				revenueExtGameCli: tt.fields.revenueExtGameCli,
			}
			got, got1, _, _ := s.fillMemberExtentInfo(tt.args.ctx, tt.args.rankUidList, tt.args.operMember, tt.args.memberList, tt.args.onlineReq)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ChannelRankLogic.fillMemberExtentInfo() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("ChannelRankLogic.fillMemberExtentInfo() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_isRankUidMatch(t *testing.T) {
	type args struct {
		ctx             context.Context
		rankUid         uint32
		fakeUid2RealUid map[uint32]uint32
		uid2UKWInfo     map[uint32]*youknowwho.UKWPersonInfo
		uid2UserInfo    map[uint32]*accountsvr.UserResp
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isRankUidMatch(tt.args.ctx, tt.args.rankUid, tt.args.fakeUid2RealUid, tt.args.uid2UKWInfo, tt.args.uid2UserInfo); got != tt.want {
				t.Errorf("isRankUidMatch() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRankLogic_getOnlineRankMembers(t *testing.T) {
	type fields struct {
		ctx               context.Context
		dyconfig          dyconf.ISDyConfigHandler
		pkdyconfig        conf2.ISDyConfigHandler
		rankCli           channelmemberviprank_go.IClient
		channelCli        channel.IClient
		accountCli        account.IClient
		guildCli          guild.IClient
		channellotteryCli channellottery.IClient
		channellivemgrCli channellivemgr.IClient
		headimageCli      headimage.IClient
		maskedpksvrCli    maskedpksvr.IClient
		maskedpkliveCli   maskedpklive.IClient
		revenueExtGameCli revenue_ext_game.IClient
	}
	type args struct {
		ctx       context.Context
		onlineReq *channelonlinerank.GetMemberRankListReq
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*ga.ChannelMemberInfo
		want1  *ga.ChannelMemberInfo
		want2  int
		want3  protocol.ServerError
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelRankLogic{
				ctx:               tt.fields.ctx,
				dyconfig:          tt.fields.dyconfig,
				pkdyconfig:        tt.fields.pkdyconfig,
				rankCli:           tt.fields.rankCli,
				channelCli:        tt.fields.channelCli,
				accountCli:        tt.fields.accountCli,
				guildCli:          tt.fields.guildCli,
				channellotteryCli: tt.fields.channellotteryCli,
				channellivemgrCli: tt.fields.channellivemgrCli,
				headimageCli:      tt.fields.headimageCli,
				maskedpksvrCli:    tt.fields.maskedpksvrCli,
				maskedpkliveCli:   tt.fields.maskedpkliveCli,
				revenueExtGameCli: tt.fields.revenueExtGameCli,
			}
			got, got1, got2, got3 := s.getOnlineRankMembers(tt.args.ctx, tt.args.onlineReq)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ChannelRankLogic.getOnlineRankMembers() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("ChannelRankLogic.getOnlineRankMembers() got1 = %v, want %v", got1, tt.want1)
			}
			if got2 != tt.want2 {
				t.Errorf("ChannelRankLogic.getOnlineRankMembers() got2 = %v, want %v", got2, tt.want2)
			}
			if !reflect.DeepEqual(got3, tt.want3) {
				t.Errorf("ChannelRankLogic.getOnlineRankMembers() got3 = %v, want %v", got3, tt.want3)
			}
		})
	}
}

func Test_isNeedHotValue(t *testing.T) {
	type args struct {
		channelType ga.ChannelType
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isNeedHotValue(tt.args.channelType); got != tt.want {
				t.Errorf("isNeedHotValue() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsNeedInflateSize(t *testing.T) {
	type args struct {
		channelType ga.ChannelType
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsNeedInflateSize(tt.args.channelType); got != tt.want {
				t.Errorf("IsNeedInflateSize() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isNeedRobotMember(t *testing.T) {
	type args struct {
		channelType ga.ChannelType
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isNeedRobotMember(tt.args.channelType); got != tt.want {
				t.Errorf("isNeedRobotMember() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getChannelHotValue(t *testing.T) {
	type args struct {
		ctx         context.Context
		channelId   uint32
		channelType uint32
	}
	tests := []struct {
		name  string
		args  args
		want  uint32
		want1 protocol.ServerError
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := getChannelHotValue(tt.args.ctx, tt.args.channelId, tt.args.channelType)
			if got != tt.want {
				t.Errorf("getChannelHotValue() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("getChannelHotValue() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_isPgcChannelEffectiveUKW(t *testing.T) {
	type args struct {
		channelType ga.ChannelType
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isPgcChannelEffectiveUKW(tt.args.channelType); got != tt.want {
				t.Errorf("isPgcChannelEffectiveUKW() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getChannelMemberSize(t *testing.T) {
	type args struct {
		ctx       context.Context
		channelId uint32
	}
	tests := []struct {
		name  string
		args  args
		want  uint32
		want1 protocol.ServerError
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := getChannelMemberSize(tt.args.ctx, tt.args.channelId)
			if got != tt.want {
				t.Errorf("getChannelMemberSize() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("getChannelMemberSize() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestChannelRankLogic_getRankUid(t *testing.T) {
	type fields struct {
		ctx               context.Context
		dyconfig          dyconf.ISDyConfigHandler
		pkdyconfig        conf2.ISDyConfigHandler
		rankCli           channelmemberviprank_go.IClient
		channelCli        channel.IClient
		accountCli        account.IClient
		guildCli          guild.IClient
		channellotteryCli channellottery.IClient
		channellivemgrCli channellivemgr.IClient
		headimageCli      headimage.IClient
		maskedpksvrCli    maskedpksvr.IClient
		maskedpkliveCli   maskedpklive.IClient
		revenueExtGameCli revenue_ext_game.IClient
	}
	type args struct {
		subCtx      context.Context
		opUid       uint32
		channelId   uint32
		channelType ga.ChannelType
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   uint32
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelRankLogic{
				ctx:               tt.fields.ctx,
				dyconfig:          tt.fields.dyconfig,
				pkdyconfig:        tt.fields.pkdyconfig,
				rankCli:           tt.fields.rankCli,
				channelCli:        tt.fields.channelCli,
				accountCli:        tt.fields.accountCli,
				guildCli:          tt.fields.guildCli,
				channellotteryCli: tt.fields.channellotteryCli,
				channellivemgrCli: tt.fields.channellivemgrCli,
				headimageCli:      tt.fields.headimageCli,
				maskedpksvrCli:    tt.fields.maskedpksvrCli,
				maskedpkliveCli:   tt.fields.maskedpkliveCli,
				revenueExtGameCli: tt.fields.revenueExtGameCli,
			}
			if got := s.getRankUid(tt.args.subCtx, tt.args.opUid, tt.args.channelId, tt.args.channelType); got != tt.want {
				t.Errorf("ChannelRankLogic.getRankUid() = %v, want %v", got, tt.want)
			}
		})
	}
}
