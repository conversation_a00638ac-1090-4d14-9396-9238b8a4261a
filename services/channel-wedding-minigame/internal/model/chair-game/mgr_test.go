package chair_game

import (
    "context"
    "github.com/golang/mock/gomock"
    "sync"
    "testing"
    account_mocks "golang.52tt.com/clients/mocks/account"
    unified_pay_mocks "golang.52tt.com/clients/mocks/unified_pay"
    "golang.52tt.com/services/channel-wedding-minigame/internal/model/chair-game/mocks"
    bcMocks "golang.52tt.com/services/channel-wedding-minigame/internal/conf/mocks"
    acLaymocks "golang.52tt.com/services/channel-wedding-minigame/internal/model/anti-corruption-layer/mocks"
    "golang.52tt.com/services/channel-wedding-minigame/internal/conf"
    "reflect"
    pb "golang.52tt.com/protocol/services/channel-wedding-minigame"
    "golang.52tt.com/services/channel-wedding-minigame/internal/model/chair-game/cache"
    chair_game_award_mocks "golang.52tt.com/services/channel-wedding-minigame/internal/model/chair-game-award/mocks"
    "time"
    "errors"
    "golang.52tt.com/services/channel-wedding-minigame/internal/model/chair-game/store"
)

var (
    testMgr *Mgr

    ctx                = context.Background()
    mockStore          *mocks.MockIStore
    mockCache          *mocks.MockICache
    mockBc             *bcMocks.MockIBusinessConfManager
    ayLayMock          *acLaymocks.MockIMgr
    chairGameAwardMock *chair_game_award_mocks.MockIMgr

    accountClient    *account_mocks.MockIClient
    unifiedPayClient *unified_pay_mocks.MockIClient

    testUid       = uint32(1)
    testCid       = uint32(2)
    testGameId    = uint32(1)
    testWeddingId = uint32(3)

    gamePlayers = []*cache.PlayerInfo{
        {
            Uid:   1,
            MicId: 1,
        },
        {
            Uid:   2,
            MicId: 2,
        },
        {
            Uid:   3,
            MicId: 3,
        },
        {
            Uid:   4,
            MicId: 4,
        },
        {
            Uid:   5,
            MicId: 6,
        },
    }
    now = time.Now()

    testPackId = uint32(1)
    reward     = &conf.RewardInfo{
        GiftId:    "1",
        GiftType:  1,
        GiftName:  "11",
        BasePic:   "111",
        Amount:    1,
        Price:     100,
        PriceType: 1,
    }
)

func initTestMgr(t *testing.T) {
    ctrl := gomock.NewController(t)
    mockStore = mocks.NewMockIStore(ctrl)
    mockCache = mocks.NewMockICache(ctrl)
    mockBc = bcMocks.NewMockIBusinessConfManager(ctrl)
    ayLayMock = acLaymocks.NewMockIMgr(ctrl)
    chairGameAwardMock = chair_game_award_mocks.NewMockIMgr(ctrl)

    accountClient = account_mocks.NewMockIClient(ctrl)
    unifiedPayClient = unified_pay_mocks.NewMockIClient(ctrl)

    testMgr = &Mgr{
        store:             mockStore,
        cache:             mockCache,
        bc:                mockBc,
        acLayerMgr:        ayLayMock,
        chairGameAwardMgr: chairGameAwardMock,
        wg:                sync.WaitGroup{},
        shutDown:          make(chan struct{}),

        accountCli:    accountClient,
        unifiedPayCli: unifiedPayClient,
    }
}

//// 获取当前的整秒时间戳，向上取整到下一秒
//func getCeiledSecondTimestamp(t time.Time) int64 {
//    // 使用 Add 方法确保时间戳向上取整到下一秒
//    nextSecond := t.Add(time.Second - time.Duration(t.Nanosecond())%time.Second)
//    return nextSecond.Unix()
//}

//// 模拟 time.Now() 以返回一个固定的当前时间
//func mockNow() time.Time {
//    return time.Date(2023, 10, 5, 14, 30, 45, *********, time.UTC)
//}
//
//// 使用模拟的当前时间测试 getCeiledSecondTimestamp 方法
//func TestGetCeiledSecondTimestamp(t *testing.T) {
//
//    now := time.Now()
//    // 调用被测试的方法
//    result := getCeiledSecondTimestamp(now)
//
//    t.Logf("Current time: %v %d, Ceiled second timestamp: %v", now, now.Unix(), result)
//}

func TestMgr_AddToPlayerOffMicSet(t *testing.T) {
    initTestMgr(t)

    type args struct {
        ctx    context.Context
        cid    uint32
        gameId uint32
        uid    uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "common success",
            initFunc: func() {
                mockCache.EXPECT().AddToPlayerOffMicSet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
            args: args{
                ctx:    ctx,
                cid:    testCid,
                gameId: testGameId,
                uid:    testUid,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if err := m.AddToPlayerOffMicSet(tt.args.ctx, tt.args.cid, tt.args.gameId, tt.args.uid); (err != nil) != tt.wantErr {
                t.Errorf("AddToPlayerOffMicSet() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_ForceEndGame(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx       context.Context
        cid       uint32
        weddingId uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "game nil",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(nil, nil)
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                weddingId: testWeddingId,
            },
            wantErr: false,
        },
        {
            name: "weddingId not match",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(&cache.ChairGameInfo{
                    WeddingId: 1,
                }, nil)
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                weddingId: testWeddingId,
            },
            wantErr: false,
        },
        {
            name: "common success",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(&cache.ChairGameInfo{
                    Cid:          testCid,
                    WeddingId:    testWeddingId,
                    GameId:       testGameId,
                    GameOverTime: 0,
                }, nil)
                mockCache.EXPECT().SetChairGameInfo(gomock.Any(), gomock.Any()).Return(nil)
                mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).Return(nil)
                chairGameAwardMock.EXPECT().ConsumeRollBack(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                weddingId: testWeddingId,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if err := m.ForceEndGame(tt.args.ctx, tt.args.cid, tt.args.weddingId); (err != nil) != tt.wantErr {
                t.Errorf("ForceEndGame() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_GetChannelChairGameInfo(t *testing.T) {
    initTestMgr(t)

    gameInfo := &cache.ChairGameInfo{
        Cid:             testCid,
        WeddingId:       testWeddingId,
        GameId:          testGameId,
        CurRound:        1,
        ChairNum:        4,
        AutoStarTime:    0,
        ButtonEndTs:     now.Add(10 * time.Second).Unix(),
        StartTime:       now.Add(-10 * time.Second).UnixMilli(),
        EndTime:         now.Add(10 * time.Second).UnixMilli(),
        CurRoundPlayers: []uint32{1, 2, 3, 4, 5},
        GamePlayers:     gamePlayers,
        Reward: &conf.RewardInfo{
            GiftId:    "1",
            GiftType:  conf.GiftTypePackage,
            Price:     10,
            PriceType: 1,
        },
        GameOverTime: 0, // 未结束
    }

    winners := []uint32{1, 2, 3}
    type args struct {
        ctx context.Context
        cid uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *pb.ChairGameInfo
        wantErr  bool
    }{
        {
            name: "game nil",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(nil, nil)
            },
            args: args{
                ctx: ctx,
                cid: testCid,
            },
            want:    nil,
            wantErr: false,
        },
        {
            name: "game over",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(&cache.ChairGameInfo{
                    GameId:       1,
                    GameOverTime: 1,
                }, nil)
            },
            args: args{
                ctx: ctx,
                cid: testCid,
            },
            want:    nil,
            wantErr: false,
        },
        {
            name: "common success",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(gameInfo, nil).AnyTimes()
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(winners, nil)
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(now, nil).AnyTimes()

                mockBc.EXPECT().GetHostStartButtonDuration().Return(int64(30)).AnyTimes()
                mockBc.EXPECT().GetRewardInfoListByPackId(gomock.Any()).Return([]*conf.RewardInfo{
                    reward,
                }).AnyTimes()
            },
            args: args{
                ctx: ctx,
                cid: testCid,
            },
            want: &pb.ChairGameInfo{
                GameId:            testGameId,
                ShowGameBeginAnim: false,
                GameProgress: &pb.ChairGameProgress{
                    GameId:               testGameId,
                    CurRound:             1,
                    ChairNum:             4,
                    RoundStatus:          2,
                    ShowRoundTip:         false,
                    RoundPalyerUids:      []uint32{1, 2, 3, 4, 5},
                    RoundWinnerUids:      []uint32{1, 2, 3},
                    NextRoundChairNum:    2,
                    ServerTimeMs:         now.UnixMilli(),
                    HostStartButDuration: 30,
                    HostButtonEndTs:      now.Add(10 * time.Second).Unix(),
                },
                RewardList: []*pb.ChairGameRewardInfo{transformRewardInfo2PB(reward)},
                Players: []*pb.PlayerInfo{
                    {
                        Uid:   1,
                        MicId: 1,
                    },
                    {
                        Uid:   2,
                        MicId: 2,
                    },
                    {
                        Uid:   3,
                        MicId: 3,
                    },
                    {
                        Uid:   4,
                        MicId: 4,
                    },
                    {
                        Uid:   5,
                        MicId: 6,
                    },
                },
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := m.GetChannelChairGameInfo(tt.args.ctx, tt.args.cid)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetChannelChairGameInfo() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetChannelChairGameInfo() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_GetEnrollList(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx       context.Context
        cid       uint32
        weddingId uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     []uint32
        wantErr  bool
    }{
        {
            name: "common success",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameEnrollList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]uint32{1, 2, 3}, nil)
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                weddingId: testWeddingId,
            },
            want:    []uint32{1, 2, 3},
            wantErr: false,
        },
        {
            name: "common fail",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameEnrollList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("test err"))
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                weddingId: testWeddingId,
            },
            want:    nil,
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := m.GetEnrollList(tt.args.ctx, tt.args.cid, tt.args.weddingId)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetEnrollList() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetEnrollList() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_GetEnrollTotalCnt(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx       context.Context
        cid       uint32
        weddingId uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     uint32
        wantErr  bool
    }{
        {
            name: "common success",
            initFunc: func() {
                mockCache.EXPECT().GetEnrollTotalCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(3), nil)
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                weddingId: testWeddingId,
            },
            want:    3,
            wantErr: false,
        },
        {
            name: "common fail",
            initFunc: func() {
                mockCache.EXPECT().GetEnrollTotalCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), errors.New("test err"))
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                weddingId: testWeddingId,
            },
            want:    0,
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := m.GetEnrollTotalCnt(tt.args.ctx, tt.args.cid, tt.args.weddingId)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetEnrollTotalCnt() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if got != tt.want {
                t.Errorf("GetEnrollTotalCnt() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_GetGameLogByGameId(t *testing.T) {
    initTestMgr(t)
    ctime := time.Now().Add(-time.Hour)
    type args struct {
        ctx    context.Context
        gameId uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *conf.ChairGameLog
        wantErr  bool
    }{
        {
            name: "common success",
            initFunc: func() {
                mockStore.EXPECT().GetGameLogByGameId(gomock.Any(), gomock.Any()).Return(&store.GameLog{
                    ID:         1,
                    Cid:        testCid,
                    WeddingId:  testWeddingId,
                    GameStatus: conf.GameLogStatusFail,
                    CreateTime: ctime,
                }, nil)
            },
            args: args{
                ctx:    ctx,
                gameId: testGameId,
            },
            want: &conf.ChairGameLog{
                GameId:       testGameId,
                GameStatus:   conf.GameLogStatusFail,
                CreateTime:   ctime,
                ResultStatus: conf.GameLogStatusFail,
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := m.GetGameLogByGameId(tt.args.ctx, tt.args.gameId)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetGameLogByGameId() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetGameLogByGameId() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_GrabChair(t *testing.T) {
    initTestMgr(t)
    gameInfo := &cache.ChairGameInfo{
        Cid:             testCid,
        WeddingId:       testWeddingId,
        GameId:          testGameId,
        CurRound:        1,
        ChairNum:        4,
        AutoStarTime:    0,
        ButtonEndTs:     now.Add(10 * time.Second).Unix(),
        StartTime:       now.Add(-10 * time.Second).UnixMilli(),
        EndTime:         now.Add(10 * time.Second).UnixMilli(),
        CurRoundPlayers: []uint32{1, 2, 3, 4, 5},
        GamePlayers:     gamePlayers,
        Reward: &conf.RewardInfo{
            GiftId:    "1",
            GiftType:  conf.GiftTypePackage,
            Price:     10,
            PriceType: 1,
        },
        GameOverTime: 0, // 未结束
    }

    winners := []uint32{1, 2, 3}
    fourWinner := []uint32{1, 2, 3, 4}

    type args struct {
        ctx context.Context
        cid uint32
        uid uint32
    }
    tests := []struct {
        name         string
        initFunc     func()
        args         args
        wantNeedPush bool
        wantErr      bool
    }{
        {
            name: "common success",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(gameInfo, nil)
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(winners, nil)
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(now, nil)
                mockCache.EXPECT().AddChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
            args: args{
                ctx: ctx,
                cid: testCid,
                uid: 4, // 前面已有{1，2，3}抢到了
            },
            wantNeedPush: true,
        },
        {
            name: "游戏未开始",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(&cache.ChairGameInfo{
                    GameId:       1,
                    GameOverTime: 1, // 已结束
                }, nil)
            },
            args: args{
                ctx: ctx,
                cid: testCid,
                uid: 4, // 前面已有{1，2，3}抢到了
            },
            wantNeedPush: false,
        },
        {
            name: "本轮已结束",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(&cache.ChairGameInfo{
                    GameId:    1,
                    StartTime: now.UnixMilli() - 1,
                    EndTime:   now.Add(-1 * time.Second).UnixMilli(),
                }, nil)
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(winners, nil)
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(now, nil)
            },
            args: args{
                ctx: ctx,
                cid: testCid,
                uid: 4, // 前面已有{1，2，3}抢到了
            },
            wantNeedPush: false,
        },
        {
            name: "本轮玩家已满",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(&cache.ChairGameInfo{
                    GameId:    1,
                    StartTime: now.UnixMilli() - 1,
                    EndTime:   now.Add(1 * time.Second).UnixMilli(),
                    ChairNum:  4,
                }, nil)
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fourWinner, nil)
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(now, nil)
            },
            args: args{
                ctx: ctx,
                cid: testCid,
                uid: 4, // 前面已有{1，2，3}抢到了
            },
            wantNeedPush: false,
        },
        {
            name: "非本轮玩家",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(&cache.ChairGameInfo{
                    GameId:    1,
                    StartTime: now.UnixMilli() - 1,
                    EndTime:   now.Add(1 * time.Second).UnixMilli(),
                    ChairNum:  4,
                }, nil)
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(winners, nil)
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(now, nil)
            },
            args: args{
                ctx: ctx,
                cid: testCid,
                uid: 100, // 非本轮玩家
            },
            wantNeedPush: false,
            wantErr:      true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            gotNeedPush, err := m.GrabChair(tt.args.ctx, tt.args.cid, tt.args.uid)
            if (err != nil) != tt.wantErr {
                t.Errorf("GrabChair() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if gotNeedPush != tt.wantNeedPush {
                t.Errorf("GrabChair() gotNeedPush = %v, want %v", gotNeedPush, tt.wantNeedPush)
            }
        })
    }
}

func TestMgr_PushCurrGameInfo(t *testing.T) {
    initTestMgr(t)
    gameInfo := &cache.ChairGameInfo{
        Cid:             testCid,
        WeddingId:       testWeddingId,
        GameId:          testGameId,
        CurRound:        1,
        ChairNum:        4,
        AutoStarTime:    0,
        ButtonEndTs:     now.Add(10 * time.Second).Unix(),
        StartTime:       now.Add(-10 * time.Second).UnixMilli(),
        EndTime:         now.Add(10 * time.Second).UnixMilli(),
        CurRoundPlayers: []uint32{1, 2, 3, 4, 5},
        GamePlayers:     gamePlayers,
        Reward: &conf.RewardInfo{
            GiftId:    "1",
            GiftType:  conf.GiftTypePackage,
            Price:     10,
            PriceType: 1,
        },
        GameOverTime: 0, // 未结束
    }

    winners := []uint32{1, 2, 3}
    type args struct {
        ctx context.Context
        cid uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "common success",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(gameInfo, nil).AnyTimes()
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(winners, nil).AnyTimes()
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(now, nil).AnyTimes()
                mockBc.EXPECT().GetHostStartButtonDuration().Return(int64(30)).AnyTimes()
                mockBc.EXPECT().GetRewardInfoListByPackId(gomock.Any()).Return([]*conf.RewardInfo{
                    reward,
                }).AnyTimes()
                mockStore.EXPECT().GetGameLogByGameId(gomock.Any(), gomock.Any()).Return(&store.GameLog{
                    ID:         1,
                    GameStatus: 1,
                }, nil)
                ayLayMock.EXPECT().ChairGameInfoChannelNotify(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
            args: args{
                ctx: ctx,
                cid: testCid,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if err := m.PushCurrGameInfo(tt.args.ctx, tt.args.cid); (err != nil) != tt.wantErr {
                t.Errorf("PushCurrGameInfo() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_StartGrabNow(t *testing.T) {
    initTestMgr(t)
    gameInfo := &cache.ChairGameInfo{
        Cid:             testCid,
        WeddingId:       testWeddingId,
        GameId:          testGameId,
        CurRound:        1,
        ChairNum:        4,
        AutoStarTime:    0,
        ButtonEndTs:     now.Add(10 * time.Second).Unix(),
        CurRoundPlayers: []uint32{1, 2, 3, 4, 5},
        GamePlayers:     gamePlayers,
        Reward: &conf.RewardInfo{
            GiftId:    "1",
            GiftType:  conf.GiftTypePackage,
            Price:     10,
            PriceType: 1,
        },
        GameOverTime: 0, // 未结束
    }

    gameInfo2 := &cache.ChairGameInfo{
        Cid:             testCid,
        WeddingId:       testWeddingId,
        GameId:          testGameId,
        CurRound:        2,
        ChairNum:        3,
        AutoStarTime:    0,
        ButtonEndTs:     now.Add(10 * time.Second).Unix(),
        StartTime:       now.UnixMilli(),
        EndTime:         now.Add(10 * time.Second).UnixMilli(),
        CurRoundPlayers: []uint32{1, 2, 3, 4},
        GamePlayers:     gamePlayers,
        Reward: &conf.RewardInfo{
            GiftId:    "1",
            GiftType:  conf.GiftTypePackage,
            Price:     10,
            PriceType: 1,
        },
        GameOverTime: 0, // 未结束
    }

    winners := []uint32{1, 2, 3, 4}

    newGameInfo := &pb.ChairGameInfo{
        GameId:            gameInfo.GameId,
        ShowGameBeginAnim: false,
        GameProgress: &pb.ChairGameProgress{
            GameId:               gameInfo.GameId,
            CurRound:             2,
            ChairNum:             3,
            RoundStatus:          2,
            RoundPalyerUids:      []uint32{1, 2, 3, 4},
            RoundWinnerUids:      nil,
            NextRoundChairNum:    0,
            ServerTimeMs:         now.UnixMilli(),
            HostStartButDuration: 10,
            HostButtonEndTs:      now.Add(10 * time.Second).Unix(),
        }, RewardList: []*pb.ChairGameRewardInfo{transformRewardInfo2PB(reward)},
        Players: []*pb.PlayerInfo{
            {
                Uid:   1,
                MicId: 1,
            },
            {
                Uid:   2,
                MicId: 2,
            },
            {
                Uid:   3,
                MicId: 3,
            },
            {
                Uid:   4,
                MicId: 4,
            },
            {
                Uid:   5,
                MicId: 6,
            },
        },
    }

    type args struct {
        ctx context.Context
        cid uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *pb.ChairGameInfo
        wantErr  bool
    }{
        {
            name: "common success",
            initFunc: func() {
                mockCache.EXPECT().LockUpdateChairGameInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
                mockCache.EXPECT().UnlockUpdateChairGameInfo(gomock.Any(), gomock.Any()).Return(nil)
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(gameInfo, nil)
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(winners, nil)
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(now, nil)
                mockBc.EXPECT().GetHostStartButtonDuration().Return(int64(10)).AnyTimes()
                mockBc.EXPECT().GetRewardInfoListByPackId(gomock.Any()).Return([]*conf.RewardInfo{
                    reward,
                }).AnyTimes()
                mockBc.EXPECT().GetChairGameGrabDuration().Return(int64(10)).AnyTimes()

                mockCache.EXPECT().SetChairGameInfo(gomock.Any(), gomock.Any()).Return(nil)
                mockCache.EXPECT().AddChairGameTimerQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(gameInfo2, nil)
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(now, nil)
            },
            args: args{
                ctx: ctx,
                cid: testCid,
            },
            want:    newGameInfo,
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := m.StartGrabNow(tt.args.ctx, tt.args.cid)
            if (err != nil) != tt.wantErr {
                t.Errorf("StartGrabNow() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("StartGrabNow() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_StartNewGame(t *testing.T) {
    initTestMgr(t)
    gameInfo := &cache.ChairGameInfo{
        Cid:             testCid,
        WeddingId:       testWeddingId,
        GameId:          1,
        CurRound:        1,
        ChairNum:        4,
        AutoStarTime:    0,
        ButtonEndTs:     now.Add(10 * time.Second).Unix(),
        CurRoundPlayers: []uint32{1, 2, 3, 4, 5},
        GamePlayers:     gamePlayers,
        Reward: &conf.RewardInfo{
            GiftId:    "1",
            GiftType:  conf.GiftTypePackage,
            Price:     10,
            PriceType: 1,
        },
        GameOverTime: 0, // 未结束
    }

    newGameInfo := &pb.ChairGameInfo{
        GameId:            gameInfo.GameId,
        ShowGameBeginAnim: true,
        GameProgress: &pb.ChairGameProgress{
            GameId:               gameInfo.GameId,
            CurRound:             1,
            ChairNum:             4,
            ShowRoundTip:         true,
            RoundStatus:          1,
            RoundPalyerUids:      []uint32{1, 2, 3, 4, 5},
            RoundWinnerUids:      nil,
            NextRoundChairNum:    0,
            ServerTimeMs:         now.UnixMilli(),
            HostStartButDuration: 10,
            HostButtonEndTs:      now.Add(10 * time.Second).Unix(),
        }, RewardList: []*pb.ChairGameRewardInfo{transformRewardInfo2PB(reward)},
        Players: []*pb.PlayerInfo{
            {
                Uid:   1,
                MicId: 1,
            },
            {
                Uid:   2,
                MicId: 2,
            },
            {
                Uid:   3,
                MicId: 3,
            },
            {
                Uid:   4,
                MicId: 4,
            },
            {
                Uid:   5,
                MicId: 6,
            },
        },
    }
    type args struct {
        ctx        context.Context
        cid        uint32
        weddingId  uint32
        playerList []*pb.PlayerInfo
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *pb.ChairGameInfo
        wantErr  bool
    }{
        {
            name: "奖励不存在",
            initFunc: func() {
                chairGameAwardMock.EXPECT().GetCurRewardInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                weddingId: testWeddingId,
            },
            want:    nil,
            wantErr: true,
        },

        {
            name: "加锁失败",
            initFunc: func() {
                chairGameAwardMock.EXPECT().GetCurRewardInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&conf.ChairGameReward{
                    PackId:       testPackId,
                    GiftInfoList: []*conf.RewardInfo{reward},
                }, nil)
                mockCache.EXPECT().LockUpdateChairGameInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                weddingId: testWeddingId,
            },
            want:    nil,
            wantErr: true,
        },

        {
            name: "common success",
            initFunc: func() {
                chairGameAwardMock.EXPECT().GetCurRewardInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&conf.ChairGameReward{
                    PackId:       testPackId,
                    GiftInfoList: []*conf.RewardInfo{reward},
                }, nil)
                mockCache.EXPECT().LockUpdateChairGameInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
                mockCache.EXPECT().UnlockUpdateChairGameInfo(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(nil, nil)
                mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).Return(nil)

                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(now, nil)
                mockCache.EXPECT().SetChairGameInfo(gomock.Any(), gomock.Any()).Return(nil)
                mockCache.EXPECT().AddChairGameTimerQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                mockCache.EXPECT().DelUserFromEnrollList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                mockBc.EXPECT().GetHostStartButtonDuration().Return(int64(10)).AnyTimes()
                mockBc.EXPECT().GetRewardInfoListByPackId(gomock.Any()).Return([]*conf.RewardInfo{
                    reward,
                }).AnyTimes()
                mockBc.EXPECT().GetChairGameGrabDuration().Return(int64(10)).AnyTimes()
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(gameInfo, nil)
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(now, nil)
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                weddingId: testWeddingId,
                playerList: []*pb.PlayerInfo{
                    {
                        Uid:   1,
                        MicId: 1,
                    },
                    {
                        Uid:   2,
                        MicId: 2,
                    },
                    {
                        Uid:   3,
                        MicId: 3,
                    },
                    {
                        Uid:   4,
                        MicId: 4,
                    },
                    {
                        Uid:   5,
                        MicId: 6,
                    },
                },
            },
            want:    newGameInfo,
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := m.StartNewGame(tt.args.ctx, tt.args.cid, tt.args.weddingId, tt.args.playerList)
            if (err != nil) != tt.wantErr {
                t.Errorf("StartNewGame() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("StartNewGame() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_StartNewGamePreCheck(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx        context.Context
        cid        uint32
        weddingId  uint32
        playerList []uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "当前有进行中的游戏",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(&cache.ChairGameInfo{
                    GameId:       1,
                    GameOverTime: 0,
                }, nil)
            },
            args: args{
                ctx: ctx,
            },
            wantErr: true,
        },
        {
            name: "游戏奖励未配置",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(nil, nil)
                chairGameAwardMock.EXPECT().GetCurRewardInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
            },
            args: args{
                ctx: ctx,
            },
            wantErr: true,
        },
        {
            name: "玩家不在报名列表中",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(nil, nil)
                chairGameAwardMock.EXPECT().GetCurRewardInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&conf.ChairGameReward{
                    PackId:       testPackId,
                    GiftInfoList: []*conf.RewardInfo{reward},
                }, nil)
                mockCache.EXPECT().CheckIfNotInChairGameEnrollList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]uint32{1}, nil)
            },
            args: args{
                ctx: ctx,
            },
            wantErr: false,
        },
        {
            name: "common success",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(nil, nil)
                chairGameAwardMock.EXPECT().GetCurRewardInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&conf.ChairGameReward{
                    PackId:       testPackId,
                    GiftInfoList: []*conf.RewardInfo{reward},
                }, nil)
                mockCache.EXPECT().CheckIfNotInChairGameEnrollList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
            },
            args: args{
                ctx: ctx,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if _, err := m.StartNewGamePreCheck(tt.args.ctx, tt.args.cid, tt.args.weddingId, tt.args.playerList); (err != nil) != tt.wantErr {
                t.Errorf("StartNewGamePreCheck() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_StartNextRound(t *testing.T) {
    initTestMgr(t)
    gameInfo := &cache.ChairGameInfo{
        Cid:             testCid,
        WeddingId:       testWeddingId,
        GameId:          1,
        CurRound:        1,
        ChairNum:        3,
        StartTime:       now.Add(-10 * time.Second).UnixMilli(),
        EndTime:         now.Add(-1 * time.Second).UnixMilli(),
        CurRoundPlayers: []uint32{1, 2, 3, 4},
        GamePlayers:     gamePlayers,
        GameOverTime:    0, // 未结束
    }
    newGameInfo := &cache.ChairGameInfo{
        Cid:             testCid,
        WeddingId:       testWeddingId,
        GameId:          1,
        CurRound:        2,
        ChairNum:        1,
        ButtonEndTs:     now.Add(10 * time.Second).Unix(),
        CurRoundPlayers: []uint32{1, 2},
        GamePlayers:     gamePlayers,
        Reward: &conf.RewardInfo{
            GiftId:    "1",
            GiftType:  conf.GiftTypePackage,
            Price:     10,
            PriceType: 1,
        },
        GameOverTime: 0, // 未结束
    }

    gameInfoPb := &pb.ChairGameInfo{
        GameId:            gameInfo.GameId,
        ShowGameBeginAnim: false,
        GameProgress: &pb.ChairGameProgress{
            GameId:               gameInfo.GameId,
            CurRound:             2,
            ChairNum:             1,
            ShowRoundTip:         true,
            RoundStatus:          1,
            RoundPalyerUids:      []uint32{1, 2},
            RoundWinnerUids:      nil,
            ServerTimeMs:         now.UnixMilli(),
            HostStartButDuration: 10,
            HostButtonEndTs:      now.Add(10 * time.Second).Unix(),
        }, RewardList: []*pb.ChairGameRewardInfo{transformRewardInfo2PB(reward)},
        Players: []*pb.PlayerInfo{
            {
                Uid:   1,
                MicId: 1,
            },
            {
                Uid:   2,
                MicId: 2,
            },
            {
                Uid:   3,
                MicId: 3,
            },
            {
                Uid:   4,
                MicId: 4,
            },
            {
                Uid:   5,
                MicId: 6,
            },
        },
    }
    winners := []uint32{1, 2, 3}
    type args struct {
        ctx context.Context
        cid uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *pb.ChairGameInfo
        wantErr  bool
    }{
        {
            name: "上锁失败",
            initFunc: func() {
                mockCache.EXPECT().LockUpdateChairGameInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
            },
            args: args{
                ctx: ctx,
                cid: testCid,
            },
            want:    nil,
            wantErr: true,
        },
        {
            name: "游戏信息不存在/已过期",
            initFunc: func() {
                mockCache.EXPECT().LockUpdateChairGameInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
                mockCache.EXPECT().UnlockUpdateChairGameInfo(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(&cache.ChairGameInfo{
                    GameId:       1,
                    GameOverTime: 1,
                }, nil)
            },
            args: args{
                ctx: ctx,
                cid: testCid,
            },
            want:    nil,
            wantErr: true,
        },
        {
            name: "common success",
            initFunc: func() {
                mockCache.EXPECT().LockUpdateChairGameInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
                mockCache.EXPECT().UnlockUpdateChairGameInfo(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(gameInfo, nil)
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(winners, nil)
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(now, nil)

                mockBc.EXPECT().GetHostStartButtonDuration().Return(int64(10)).AnyTimes()
                mockBc.EXPECT().GetRewardInfoListByPackId(gomock.Any()).Return([]*conf.RewardInfo{
                    reward,
                }).AnyTimes()
                mockCache.EXPECT().SetChairGameInfo(gomock.Any(), gomock.Any()).Return(nil)

                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(newGameInfo, nil)
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(now, nil)

                mockCache.EXPECT().AddChairGameTimerQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

                ayLayMock.EXPECT().KickOutMicSpace(gomock.Any(), gomock.Any(), gomock.Any())
            },
            args: args{
                ctx: ctx,
                cid: testCid,
            },
            want:    gameInfoPb,
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := m.StartNextRound(tt.args.ctx, tt.args.cid)
            if (err != nil) != tt.wantErr {
                t.Errorf("StartNextRound() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("StartNextRound() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_UserCancelEnroll(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx       context.Context
        cid       uint32
        uid       uint32
        weddingId uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "common success",
            initFunc: func() {
                mockCache.EXPECT().DelUserFromEnrollList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                uid:       testUid,
                weddingId: testWeddingId,
            },
            wantErr: false,
        },
        {
            name: "common fail",
            initFunc: func() {
                mockCache.EXPECT().DelUserFromEnrollList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("test"))
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                uid:       testUid,
                weddingId: testWeddingId,
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if err := m.UserCancelEnroll(tt.args.ctx, tt.args.cid, tt.args.uid, tt.args.weddingId); (err != nil) != tt.wantErr {
                t.Errorf("UserCancelEnroll() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_UserEnroll(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx       context.Context
        cid       uint32
        uid       uint32
        weddingId uint32
        enrollTs  int64
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "common success",
            initFunc: func() {
                mockCache.EXPECT().AddChairGameEnrollList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                uid:       testUid,
                weddingId: testWeddingId,
            },
            wantErr: false,
        },
        {
            name: "db err",
            initFunc: func() {
                mockCache.EXPECT().AddChairGameEnrollList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("db err"))
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                uid:       testUid,
                weddingId: testWeddingId,
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if err := m.UserEnroll(tt.args.ctx, tt.args.cid, tt.args.uid, tt.args.weddingId, tt.args.enrollTs); (err != nil) != tt.wantErr {
                t.Errorf("UserEnroll() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_UserLeaveChannelHandle(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx       context.Context
        cid       uint32
        uid       uint32
        weddingId uint32
    }
    tests := []struct {
        name         string
        initFunc     func()
        args         args
        wantTolCnt   uint32
        wantNeedPush bool
        wantErr      bool
    }{
        {
            name: "用户已报名",
            initFunc: func() {
                mockCache.EXPECT().CheckIfNotInChairGameEnrollList(ctx, testCid, testWeddingId, []uint32{testUid}).Return([]uint32{}, nil)
                mockCache.EXPECT().DelUserFromEnrollList(ctx, testCid, testWeddingId, []uint32{testUid}).Return(nil)
                mockCache.EXPECT().GetEnrollTotalCnt(ctx, testCid, testWeddingId).Return(uint32(0), nil)
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                weddingId: testWeddingId,
                uid:       testUid,
            },
            wantTolCnt:   0,
            wantNeedPush: true,
            wantErr:      false,
        },
        {
            name: "用户未报名",
            initFunc: func() {
                mockCache.EXPECT().CheckIfNotInChairGameEnrollList(ctx, testCid, testWeddingId, []uint32{testUid}).Return([]uint32{testUid}, nil)
            },
            args: args{
                ctx:       ctx,
                cid:       testCid,
                weddingId: testWeddingId,
                uid:       testUid,
            },
            wantTolCnt:   0,
            wantNeedPush: false,
            wantErr:      false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            gotTolCnt, gotNeedPush, err := m.UserLeaveChannelHandle(tt.args.ctx, tt.args.cid, tt.args.uid, tt.args.weddingId)
            if (err != nil) != tt.wantErr {
                t.Errorf("UserLeaveChannelHandle() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if gotTolCnt != tt.wantTolCnt {
                t.Errorf("UserLeaveChannelHandle() gotTolCnt = %v, want %v", gotTolCnt, tt.wantTolCnt)
            }
            if gotNeedPush != tt.wantNeedPush {
                t.Errorf("UserLeaveChannelHandle() gotNeedPush = %v, want %v", gotNeedPush, tt.wantNeedPush)
            }
        })
    }
}

func TestMgr_BatGetIfChannelInChairGame(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx     context.Context
        cidList []uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     []uint32
        wantErr  bool
    }{
        {
            name: "common success",
            initFunc: func() {
                mockCache.EXPECT().BatGetChairGameInfo(ctx, []uint32{1, 2, 3}).Return(map[uint32]*cache.ChairGameInfo{
                    1: {
                        Cid:          1,
                        GameOverTime: 0,
                    },
                }, nil)
            },
            args: args{
                ctx:     ctx,
                cidList: []uint32{1, 2, 3},
            },
            want:    []uint32{1},
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := m.BatGetIfChannelInChairGame(tt.args.ctx, tt.args.cidList)
            if (err != nil) != tt.wantErr {
                t.Errorf("BatGetIfChannelInChairGame() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("BatGetIfChannelInChairGame() got = %v, want %v", got, tt.want)
            }
        })
    }
}
