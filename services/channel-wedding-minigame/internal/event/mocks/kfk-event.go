// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-wedding-minigame/internal/event (interfaces: IKafkaEvent)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	sarama "github.com/IBM/sarama"
	gomock "github.com/golang/mock/gomock"
)

// MockIKafkaEvent is a mock of IKafkaEvent interface.
type MockIKafkaEvent struct {
	ctrl     *gomock.Controller
	recorder *MockIKafkaEventMockRecorder
}

// MockIKafkaEventMockRecorder is the mock recorder for MockIKafkaEvent.
type MockIKafkaEventMockRecorder struct {
	mock *MockIKafkaEvent
}

// NewMockIKafkaEvent creates a new mock instance.
func NewMockIKafkaEvent(ctrl *gomock.Controller) *MockIKafkaEvent {
	mock := &MockIKafkaEvent{ctrl: ctrl}
	mock.recorder = &MockIKafkaEventMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIKafkaEvent) EXPECT() *MockIKafkaEventMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockIKafkaEvent) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIKafkaEventMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIKafkaEvent)(nil).Close))
}

// HandleChannelEvent mocks base method.
func (m *MockIKafkaEvent) HandleChannelEvent(arg0 context.Context, arg1 *sarama.ConsumerMessage) (error, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleChannelEvent", arg0, arg1)
	ret0, _ := ret[0].(error)
	ret1, _ := ret[1].(bool)
	return ret0, ret1
}

// HandleChannelEvent indicates an expected call of HandleChannelEvent.
func (mr *MockIKafkaEventMockRecorder) HandleChannelEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleChannelEvent", reflect.TypeOf((*MockIKafkaEvent)(nil).HandleChannelEvent), arg0, arg1)
}

// HandleChannelMicEvent mocks base method.
func (m *MockIKafkaEvent) HandleChannelMicEvent(arg0 context.Context, arg1 *sarama.ConsumerMessage) (error, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleChannelMicEvent", arg0, arg1)
	ret0, _ := ret[0].(error)
	ret1, _ := ret[1].(bool)
	return ret0, ret1
}

// HandleChannelMicEvent indicates an expected call of HandleChannelMicEvent.
func (mr *MockIKafkaEventMockRecorder) HandleChannelMicEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleChannelMicEvent", reflect.TypeOf((*MockIKafkaEvent)(nil).HandleChannelMicEvent), arg0, arg1)
}
