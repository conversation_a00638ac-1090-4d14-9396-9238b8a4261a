package mysql

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/golang/gudetama/log"
	"strings"
	"time"
)

var CreateVerifyYuyinTable = `
CREATE TABLE IF NOT EXISTS yuyin_gold_verify_%s (
	id bigint unsigned NOT NULL AUTO_INCREMENT,
	paid_uid int unsigned NOT NULL COMMENT 'paid user id',
	room_id int unsigned default 0 NOT NULL,
	order_id varchar(100) NOT NULL,
	income bigint  default 0 NOT NULL,
	fee bigint  default 0 NOT NULL,
	descinfo varchar(256) NOT NULL,
	bought_time bigint NOT NULL,
    status int  default 0 NOT NULL,
	update_time timestamp on update current_timestamp not null default current_timestamp,
    PRIMARY KEY (order_id),
	KEY autoid(id),
	KE<PERSON> up_time(update_time),
	KE<PERSON> boughttime(bought_time)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;`

var CreateVerifyGoldTable = `
CREATE TABLE IF NOT EXISTS gold_verify_%s (
	id bigint unsigned NOT NULL AUTO_INCREMENT,
	paid_uid int unsigned NOT NULL COMMENT 'paid user id',
	room_id int unsigned default 0 NOT NULL,
	guild_id int unsigned default 0 NOT NULL,
	order_id varchar(100) NOT NULL,
	income bigint  default 0 NOT NULL,
	fee bigint  default 0 NOT NULL,
	descinfo varchar(256) NOT NULL,
	bought_time bigint NOT NULL,
    status int  default 0 NOT NULL,
	update_time timestamp on update current_timestamp not null default current_timestamp,
    PRIMARY KEY (order_id),
	KEY autoid(id),
	KEY up_time(update_time),
	KEY boughttime(bought_time)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;`

const (
	VerifyStatusInit    = 0
	VerifyStatusFailed  = 1
	VerifyStatusSuccess = 2
)

type GoldVerifyStore struct {
	Id         uint32    `db:"id"`
	PaidUid    uint32    `db:"paid_uid"`
	RoomId     uint32    `db:"room_id"`
	OrderId    string    `db:"order_id"`
	Income     int64     `db:"income"`
	Fee        int64     `db:"fee"`
	Desc       string    `db:"descinfo"`
	Status     uint32    `db:"status"`
	BoughtTime int64     `db:"bought_time"`
	UpdateTime time.Time `db:"update_time"`
}

type GoldVerifyStoreGuild struct {
	Id         uint32    `db:"id"`
	PaidUid    uint32    `db:"paid_uid"`
	RoomId     uint32    `db:"room_id"`
	GuildId    uint32    `db:"guild_id"`
	OrderId    string    `db:"order_id"`
	Income     int64     `db:"income"`
	Fee        int64     `db:"fee"`
	Desc       string    `db:"descinfo"`
	Status     uint32    `db:"status"`
	BoughtTime int64     `db:"bought_time"`
	UpdateTime time.Time `db:"update_time"`
}

func (s *Store) AddVerifyYuyin(ctx context.Context, paidUid, roomId uint32, orderId string, income, boughtTime, fee int64, status uint32, strType string) (err error) {
	var desc = strType
	getTime := time.Unix(boughtTime, 0).Format("200601")
	tableName := fmt.Sprintf("yuyin_gold_verify_%s", getTime)
	insertSQL := fmt.Sprintf(`INSERT INTO %s ( paid_uid, room_id, order_id, income, fee, descinfo, bought_time) VALUES(?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE status=%d`, tableName, status)
	// log.Errorf("insertSQL: %s", insertSQL)
	_, err = s.db.ExecContext(ctx, insertSQL, paidUid, roomId, orderId, income, fee, desc, boughtTime)
	if err != nil {
		if !strings.Contains(err.Error(), "Error 1146") {
			log.Errorf("AddVerifyGold insertSQL err:%v, orderid:%s", err, orderId)
			return err
		}

		tbName := fmt.Sprintf(CreateVerifyYuyinTable, getTime)
		_, err = s.db.Exec(tbName)
		if err != nil {
			return err
		}
		_, err = s.db.ExecContext(ctx, insertSQL, paidUid, roomId, orderId, income, fee, desc, boughtTime)
		return err
	}
	return nil
}

func (s *Store) UpdateVerifyYuyinStatus(ctx context.Context, orderId string, boughtTime int64, status uint32) (err error) {
	getTime := time.Unix(boughtTime, 0).Format("200601")
	tableName := fmt.Sprintf("yuyin_gold_verify_%s", getTime)
	sql := fmt.Sprintf("UPDATE %s SET status = %d WHERE order_id = '%s'", tableName, status, orderId)
	_, err = s.db.ExecContext(ctx, sql)
	if err != nil {
		log.Errorln("UpdateVerifyYuyinStatus update status=1 err:", err.Error())
		return err
	}
	return nil
}

func (s *Store) GetVerifyYuyin(ctx context.Context, beginTime, endTime int64) ([]*GoldVerifyStore, error) {
	getTime := time.Unix(beginTime, 0).Format("200601")
	tableName := fmt.Sprintf("yuyin_gold_verify_%s", getTime)
	getSQL := fmt.Sprintf(`SELECT * FROM %s WHERE bought_time >= ? AND bought_time < ? and status=?`, tableName)
	// log.Errorf("insertSQL: %s", insertSQL)
	items := make([]*GoldVerifyStore, 0, 10)
	err := s.db.SelectContext(ctx, &items, getSQL, beginTime, endTime, VerifyStatusSuccess)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return items, nil
		} else if strings.Contains(err.Error(), "Error 1146") {
			return items, nil
		} else {
			log.Errorf("GetVerifyYuyin db err:%s", err.Error())
			return nil, err
		}
	}
	return items, nil
}

func (s *Store) GetVerifyYuyinCnt(ctx context.Context, beginTime, endTime int64) (uint32, error) {
	getTime := time.Unix(beginTime, 0).Format("200601")
	tableName := fmt.Sprintf("yuyin_gold_verify_%s", getTime)
	getSQL := fmt.Sprintf(`SELECT count(1) FROM %s WHERE bought_time >= ? AND bought_time < ? and status=?`, tableName)
	var totalCnt uint32
	err := s.db.GetContext(ctx, &totalCnt, getSQL, beginTime, endTime, VerifyStatusSuccess)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return 0, nil
		} else if strings.Contains(err.Error(), "Error 1146") {
			return 0, nil
		} else {
			log.Errorf("GetVerifyYuyinCnt db err:%s", err.Error())
			return 0, err
		}
	}
	return totalCnt, nil
}

func (s *Store) GetVerifyYuyinSum(ctx context.Context, beginTime, endTime int64) (uint32, error) {
	getTime := time.Unix(beginTime, 0).Format("200601")
	tableName := fmt.Sprintf("yuyin_gold_verify_%s", getTime)
	getSQL := fmt.Sprintf(`SELECT sum(fee) FROM %s WHERE bought_time >= ? AND bought_time < ? and status=?`, tableName)
	var sumFee uint32
	err := s.db.GetContext(ctx, &sumFee, getSQL, beginTime, endTime, VerifyStatusSuccess)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return 0, nil
		} else if strings.Contains(err.Error(), "Error 1146") {
			return 0, nil
		} else {
			log.Errorf("GetVerifyYuyinSum db err:%s", err.Error())
			return 0, err
		}
	}
	return sumFee, nil
}
