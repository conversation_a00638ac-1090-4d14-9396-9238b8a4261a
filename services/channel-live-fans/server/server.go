package server

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/bylink"
	"time"

	"golang.52tt.com/protocol/app"

	"github.com/go-redis/redis"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"

	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	channelLiveMission "golang.52tt.com/clients/channel-live-mission"
	"golang.52tt.com/clients/entertainmentrecommendback"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/app/sync"
	push_notification "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/channel-live-fans/cache"
	"golang.52tt.com/services/channel-live-fans/manager"
	"golang.52tt.com/services/notify"

	"github.com/globalsign/mgo"

	"golang.52tt.com/pkg/config"
	//"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
	tracing "golang.52tt.com/pkg/tracing/jaeger"

	liveLogicPb "golang.52tt.com/protocol/app/channel-live-logic"
	pushPb "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/common/status"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	pb "golang.52tt.com/protocol/services/channellivefans"
	v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	entertainmentPB "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	knightGroupMemPb "golang.52tt.com/protocol/services/knightgroupmembers"

	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/antispamlogic"
	censoringProxyClient "golang.52tt.com/clients/censoring-proxy"
	liveMgrClient "golang.52tt.com/clients/channel-live-mgr"
	channelmemberviprank "golang.52tt.com/clients/channelmemberVipRank"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/knight-group-members"
	"golang.52tt.com/clients/nobility"
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/user-profile-api"

	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkafansgroup"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	"golang.52tt.com/services/channel-live-fans/conf"
	"golang.52tt.com/services/channel-live-fans/datareport"
	"golang.52tt.com/services/channel-live-fans/event"
	"golang.52tt.com/services/channel-live-fans/mongo"
)

type ChannelLiveFansServer struct {
	sc                   conf.IServiceConfigT
	mDao                 mongo.IMongoDao
	cacheClient          cache.IChannelLiveFansCache
	kafkaSub             *event.AddFansSubscriber
	accountClient        account.IClient
	pushCli              push.IClient
	missionCli           *channelLiveMission.Client
	liveMrgCli           *liveMgrClient.Client
	fansValueKfkProduce  event.IKafkaProduce
	anchorKfkProduce     event.IKafkaProduce
	enterCli             entertainmentrecommendback.IClient
	antiKafkaSub         *event.AntiSpamSubscriber
	antispamLogicCli     *antispamlogic.Client
	fansMgr              manager.IFansMgr
	censoringProxyClient censoringProxyClient.IClient
	anchorLiveKfk        *event.ChLiveStatusKafkaSub
	userProfileCli       user_profile_api.IClient
	knightGroupMemCli    knightgroupmembers.IClient
	apiCli               apicenter.IClient
	nobilityCli          nobility.IClient
	channelOlCli         channelol.IClient
	channelVipMemCli     channelmemberviprank.IClient
	confMgr              conf.IBusinessConfManager
	followingUpdateKfk   *event.UgcFollowingUpdateSub
	timerD               *timer.Timer
}

func NewChannelLiveFansServer(ctx context.Context, cfg config.Configer) (*ChannelLiveFansServer, error) {
	sc := &conf.ServiceConfigT{}

	err := sc.Parse(cfg)
	if err != nil {
		return nil, err
	}

	businessConfMgr, err := conf.NewBusinessConfManager()
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewBusinessConfManager %v", err)
		return nil, err
	}

	mDao, err := mongo.NewMongoDao(sc.GetMongoConfig())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create mongodb %v", err)
		return nil, err
	}

	err = mDao.CreateIndexs()
	if err != nil {
		log.ErrorWithCtx(ctx, "mDao.CreateIndexs() failed err:%v", err)
	}

	redisClient := redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfig().Protocol,
		Addr:               sc.GetRedisConfig().Addr(),
		PoolSize:           sc.GetRedisConfig().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfig().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfig().DB,
	})
	log.DebugWithCtx(ctx, "Initialized redis connection pool to %s://%s/%d", sc.GetRedisConfig().Protocol, sc.GetRedisConfig().Addr(), sc.GetRedisConfig().DB)
	redisTracer := tracing.Init("channel-live-fans-redis")
	cacheClient := cache.NewChannelLiveFansCache(redisClient, redisTracer)

	// 大小进程的定时器，同一个任务只会在一个节点上执行
	timerD, err := timer.NewTimerD(ctx, "channel-live-fans", timer.WithV6RedisCmdable(redisClient))
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewTimerD err:%v", err)
		return nil, err
	}

	fansMgr, _ := manager.NewFansMgr(mDao, cacheClient, businessConfMgr, timerD)

	anchorKfkProduce := event.NewKafkaProduce(sc.GetFansKafkaConfig().BrokerList(), sc.GetFansKafkaConfig().ClientID, sc.GetFansKafkaConfig().Topics, true)
	fansValueKfkProduce := event.NewKafkaProduce(sc.GetFansValueKafkaConfig().BrokerList(), sc.GetFansValueKafkaConfig().ClientID, sc.GetFansValueKafkaConfig().Topics, false)

	subEvent, err := event.NewLiveFansSubscriber(sc.GetKafkaConfig(), mDao, cacheClient, anchorKfkProduce, fansMgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewKafkaSubscriber err %s", err.Error())
		return nil, err
	}

	/*
		err = subEvent.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to Start subEvent.Start() err %s", err.Error())
			return nil, err
		}
	*/

	antiSubEvent, err := event.NewAntiSpamSubscriber(sc.GetAntiKafkaConfig().ClientID, sc.GetAntiKafkaConfig().GroupID, sc.GetAntiKafkaConfig().TopicList(),
		sc.GetAntiKafkaConfig().BrokerList(), mDao, cacheClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewAntiSpamSubscriber err %s", err.Error())
		return nil, err
	}

	/*
		err = antiSubEvent.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to Start antiSubEvent.Start() err %s", err.Error())
			return nil, err
		}
	*/

	anchorLiveKfk, err := event.NewChLiveStatusKafkaSub(sc.GetAnchorLiveKafkaConfig().ClientID, sc.GetAnchorLiveKafkaConfig().GroupID, sc.GetAnchorLiveKafkaConfig().TopicList(),
		sc.GetAnchorLiveKafkaConfig().BrokerList(), fansMgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewChLiveStatusKafkaSub failed err:%v", err)
		return nil, err
	}

	/*
		err = anchorLiveKfk.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to anchorLiveKfk.Start() err:%v", err)
			return nil, err
		}
	*/

	ugcFollowingUpdateKfk, err := event.NewUgcFollowingUpdateSub(sc.GetUgcFollowingUpdateKafkaConfig())
	if err != nil {
		log.ErrorWithCtx(ctx, "NewUgcFollowingUpdateSub failed,err:%v", err)
		return nil, err
	}

	/*
		err = ugcFollowingUpdateKfk.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to ugcFollowingUpdateKfk.Start() err:%v", err)
			return nil, err
		}
	*/

	// 百灵数据统计 初始化
	bylinkCollect, err := bylink.NewKfkCollector()
	if err != nil {
		log.ErrorWithCtx(ctx, "bylink.NewKfkCollector failed err:%v", err)
		return nil, err
	}
	bylink.InitGlobalCollector(bylinkCollect)

	accountClient, err := account.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "New accountClient err %s", err.Error())
		return nil, err
	}

	pushCli, err := push.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "New pushCli err %s", err.Error())
		return nil, err
	}

	missionCli, err := channelLiveMission.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "New missionCli err %s", err.Error())
		return nil, err
	}

	liveMgrCli, err := liveMgrClient.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "New liveMgrCli err %s", err.Error())
		return nil, err
	}

	enterCli := entertainmentrecommendback.NewClient()
	antilogicCli := antispamlogic.NewClient()
	censoringCli := censoringProxyClient.NewClient()

	userProfileCli, err := user_profile_api.NewClient()
	if err != nil {
		log.Errorf("user_profile_api.NewClient() failed err:%v", err)
		return nil, err
	}

	knightGroupMemCli, err := knightgroupmembers.NewClient()
	if err != nil {
		log.Errorf("knightgroupmembers.NewClient() failed err:%v", err)
		return nil, err
	}

	apiCli := apicenter.NewClient()
	nobilityCli, _ := nobility.NewClient()
	channelOlCli := channelol.NewClient()
	channelVipMenCli := channelmemberviprank.NewClient()

	//定时器开启
	timerD.Start()

	return &ChannelLiveFansServer{
		sc:                   sc,
		mDao:                 mDao,
		cacheClient:          cacheClient,
		kafkaSub:             subEvent,
		accountClient:        accountClient,
		pushCli:              pushCli,
		missionCli:           missionCli,
		liveMrgCli:           liveMgrCli,
		fansValueKfkProduce:  fansValueKfkProduce,
		anchorKfkProduce:     anchorKfkProduce,
		enterCli:             enterCli,
		antiKafkaSub:         antiSubEvent,
		antispamLogicCli:     antilogicCli,
		fansMgr:              fansMgr,
		censoringProxyClient: censoringCli,
		anchorLiveKfk:        anchorLiveKfk,
		userProfileCli:       userProfileCli,
		knightGroupMemCli:    knightGroupMemCli,
		apiCli:               apiCli,
		nobilityCli:          nobilityCli,
		channelOlCli:         channelOlCli,
		channelVipMemCli:     channelVipMenCli,
		confMgr:              businessConfMgr,
		followingUpdateKfk:   ugcFollowingUpdateKfk,
		timerD:               timerD,
	}, nil

}

func (s *ChannelLiveFansServer) ShutDown() {
	s.kafkaSub.Close()
	s.antiKafkaSub.Close()
	s.anchorLiveKfk.Close()
	s.followingUpdateKfk.Close()
	s.timerD.Stop()
	bylink.Close()
}

func (s *ChannelLiveFansServer) CreateFansGroup(ctx context.Context, in *pb.CreateFansGroupReq) (out *pb.CreateFansGroupResp, err error) {
	out = &pb.CreateFansGroupResp{}

	//先判断主播是否已经创建了粉丝团
	isExist, err := s.mDao.CheckIsCreateGroup(in.GetAnchorUid())
	if isExist {
		log.InfoWithCtx(ctx, "CreateFansGroup anchorUid already create fansGroup or GetGroupIdByAnchor failed anchorUid %d err %v", in.GetAnchorUid(), err)
		return out, err
	}

	groupId, err := s.mDao.GroupIdIncr()
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateFansGroup Failed to mDao.GroupIdIncr() err %s", err.Error())
		return out, err
	}

	nowTime := time.Now()
	err = s.mDao.SaveFansGroupInfo(groupId, in.GetAnchorUid(), nowTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateFansGroup Failed to mDao.SaveFansGroupInfo() err %s", err.Error())
		return out, err
	}

	out.GroupId = groupId

	s.fansMgr.DelAnchorGroupInfo(ctx, in.GetAnchorUid())

	// 发放默认铭牌
	go s.fansMgr.GrantAnchorPlateV2(ctx, &pb.GrantAnchorPlateReq{
		GrantInfo: &pb.GrantedPlateInfo{
			AnchorUid: in.GetAnchorUid(),
			PlateId:   s.confMgr.GetDefaultPlateId(),
			DayCnt:    9999,
		},
	})

	log.InfoWithCtx(ctx, "ChannelLiveFans CreateFansGroup req:%+v, rsp:%+v", in, out)
	return out, nil
}

/*
// 获取粉丝的有效时间
func (s *ChannelLiveFansServer) getFansActiveTime(ctx context.Context, anchorUid uint32) time.Time {
	now := time.Now()
	beginTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)

	log.DebugWithCtx(ctx, "getFansActiveTime begin anchoruid %d now %v", anchorUid, now)

	liveTsList, err := s.fansMgr.GetAnchorAllLiveTsList(anchorUid, 0, beginTime.Unix(), 365)
	if err != nil {
		log.ErrorWithCtx(ctx, "getFansActiveTime GetAnchorAllLiveTsList failed anchorUid:%d err:%v", anchorUid, err)
		return beginTime.AddDate(0, 0, -conf.FansValidTimeIntervalDays)
	}

	log.DebugWithCtx(ctx, "getFansActiveTime anchoruid %d now %v begin %d len %d", anchorUid, now, len(liveTsList))

	if len(liveTsList) > 0 {
		recordLiveTime := time.Unix(liveTsList[0], 0)
		recordLiveTime = time.Date(recordLiveTime.Year(), recordLiveTime.Month(), recordLiveTime.Day()+1, 0, 0, 0, 0, time.Local)
		activeTime := time.Unix(recordLiveTime.Unix()-conf.FansValidTimeIntervalTs, 0)
		log.Debugf("getFansActiveTime anchoruid %d liveTime %v time %v", anchorUid, recordLiveTime, activeTime)
		return activeTime
	}

	log.Debugf("getFansActiveTime anchoruid %d", anchorUid)

	return beginTime.AddDate(0, 0, -conf.FansValidTimeIntervalDays)
}
*/

func (s *ChannelLiveFansServer) checkIsActiveFans(ctx context.Context, fansInfo mongo.FansGroupMemberInfo) bool {
	now := time.Now()
	now = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	subTs := now.Unix() - fansInfo.ValueUpdateTime.Unix()

	log.Debugf("checkIsActiveFans begin uid %d anchorUid %d now %d time %d gab %d", fansInfo.FansUid, fansInfo.AnchorUid, now.Unix(), fansInfo.ValueUpdateTime.Unix(), subTs)
	if subTs >= conf.FansValidTimeIntervalTs {
		liveTsList, err := s.fansMgr.GetAnchorAllLiveTsList(fansInfo.AnchorUid, 0, now.Unix(), 365)
		if err != nil {
			log.Errorf("checkIsActiveFans GetAnchorAllLiveTsList failed anchorUid:%d err:%v", fansInfo.AnchorUid, err)
			return true
		}

		log.Debugf("checkIsActiveFans record uid %d anchorUid %d len %d", fansInfo.FansUid, fansInfo.AnchorUid, len(liveTsList))

		if len(liveTsList) > 0 {
			recordLiveTime := time.Unix(liveTsList[0], 0)
			recordLiveTime = time.Date(recordLiveTime.Year(), recordLiveTime.Month(), recordLiveTime.Day()+1, 0, 0, 0, 0, time.Local)
			subSec := recordLiveTime.Unix() - fansInfo.ValueUpdateTime.Unix()
			log.Debugf("checkIsActiveFans  uid %d anchorUid %d recordTime %d  gab %d", fansInfo.FansUid, fansInfo.AnchorUid, liveTsList[0], subSec)
			if subSec >= conf.FansValidTimeIntervalTs {
				return false
			}
		} else {
			// 上线一段时间后，没有直播记录就直接返回false
			return false
		}
	}

	return true
}

/*
func (s *ChannelLiveFansServer) checkIsActiveFansV2(ctx context.Context, fansInfo mongo.FansGroupMemberInfo, latestLiveTs int64) bool {
	now := time.Now()
	now = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	subTs := now.Unix() - fansInfo.ValueUpdateTime.Unix()
	log.InfoWithCtx(ctx,"checkIsActiveFansV2 begin uid %d anchorUid %d now %d time %d gab %d ts:%d", fansInfo.FansUid, fansInfo.AnchorUid,
		now.Unix(), fansInfo.ValueUpdateTime.Unix(), subTs, latestLiveTs)
	if subTs >= conf.FansValidTimeIntervalTs {
		log.DebugWithCtx(ctx,"checkIsActiveFans record uid %d anchorUid %d", fansInfo.FansUid, fansInfo.AnchorUid)

		subSec := latestLiveTs - fansInfo.ValueUpdateTime.Unix()
		log.InfoWithCtx(ctx,"checkIsActiveFansV2  uid %d anchorUid %d recordTime %d  gab %d", fansInfo.FansUid, fansInfo.AnchorUid, latestLiveTs, subSec)
		if subSec >= conf.FansValidTimeIntervalTs {
			return false
		}
	}
	return true
}
*/

func (s *ChannelLiveFansServer) procFansLevelUpEv(anchorUid, fansUid, cid, newFansLevel uint32) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()

	log.DebugWithCtx(ctx, "procFansLevelUpEv begin anchor:%d fans:%d newLv:%d", anchorUid, fansUid, newFansLevel)

	nbResp, err := s.nobilityCli.GetNobilityInfo(ctx, fansUid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "procFansLevelUpEv GetNobilityInfo uid:%d err:%v", fansUid, err)
		return
	}

	if nbResp.GetInvisible() {
		vipResp, err := s.channelVipMemCli.GetMemberNobilityInfo(ctx, fansUid, cid)
		if nil != err {
			log.ErrorWithCtx(ctx, "procFansLevelUpEv GetMemberNobilityInfo uid:%d err:%v", fansUid, err)
			return
		}

		if vipResp.GetInvisible() {
			log.DebugWithCtx(ctx, "procFansLevelUpEv invisible uid:%d cid:%d ", fansUid, cid)
			return
		}
	}

	mapUid2Info, err := s.userProfileCli.BatchGetUserProfile(ctx, []uint32{anchorUid, fansUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "procFansLevelUpEv BatchGetUserProfile failed anchor:%d fans:%d err(%v)", anchorUid, fansUid, err)
		return
	}

	groupInfo, mongoErr := s.fansMgr.GetAnchorGroupInfo(ctx, anchorUid)
	if mongoErr != nil {
		log.ErrorWithCtx(ctx, "procFansLevelUpEv GetAnchorGroupInfo uid %d anchorUid %d err %+v", fansUid, anchorUid, mongoErr)
		return
	}

	status, cacheErr := s.cacheClient.GetFansMissionStatus(anchorUid, fansUid)
	if cacheErr != nil {
		log.ErrorWithCtx(ctx, "procFansLevelUpEv GetFansMissionStatus uid %d anchorUid %d err %+v", fansUid, anchorUid, cacheErr)
		return
	}

	var isFinishMission bool
	if status == 1 {
		isFinishMission = true
	}

	knightResp, err := s.knightGroupMemCli.GetKnightInfo(ctx, &knightGroupMemPb.GetKnightInfoReq{
		KnightUid: fansUid,
		ChannelId: cid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "procFansLevelUpEv GetKnightInfo failed uid %d anchorUid %d err:%v", fansUid, anchorUid, err)
	}

	knightType := pb.EnumKnightType_E_NO_KNIGHT
	nowTs := uint32(time.Now().Unix())
	for _, info := range knightResp.GetKnightInfoList() {
		if anchorUid == info.GetAnchorUid() && info.GetBeginTime() <= nowTs && info.GetEndTime() >= nowTs {
			knightType = pb.EnumKnightType_E_COMMON_KNIGHT
			if knightResp.GetFirstChief() {
				knightType = pb.EnumKnightType_E_CHIEF_KNIGHT
			}
			break
		}
	}

	var plateInfo pb.FansPlateInfo
	if groupInfo.GroupName != "" {
		anchorPlateConfig, err := s.fansMgr.GetAnchorPlateConfig(ctx, anchorUid, groupInfo.GroupName)
		if err != nil {
			log.ErrorWithCtx(ctx, "procFansLevelUpEv GetAnchorPlateConfig failed uid %d anchorUid %d err:%v", fansUid, anchorUid, err)
			return
		}
		plateInfo = getNamePlateConfig(fillFansInfo(anchorUid, fansUid, newFansLevel, uint32(knightType), true, isFinishMission),
			groupInfo.GroupName, anchorPlateConfig, s.confMgr.GetDefaultPlateId())
	} else {
		plateInfo = getCommonPlateConfig(newFansLevel, true)
	}

	fansNotifyMsg := &liveLogicPb.FansNotifyOpt{
		EventType: uint32(liveLogicPb.FansNotifyOpt_ENUM_FANS_LEVEL_UP),
		FansLevel: newFansLevel,
		GroupName: groupInfo.GroupName,
		PlateInfo: fillAppPlateConfigMsg(plateInfo),
	}
	//推送粉丝等级升级消息
	s.pushLevelUpMsg(ctx, fansUid, anchorUid, cid, mapUid2Info, fansNotifyMsg)

	log.DebugWithCtx(ctx, "procFansLevelUpEv end anchor:%d fans:%d newLv:%d", anchorUid, fansUid, newFansLevel)
}

func (s *ChannelLiveFansServer) AddFansLoveValue(ctx context.Context, in *pb.AddFansLoveValueReq) (out *pb.AddFansLoveValueResp, err error) {
	out = &pb.AddFansLoveValueResp{}

	log.DebugWithCtx(ctx, "AddFansLoveValue, in %+v", in)

	if in.GetOrderId() != "" {
		isExist, err := s.cacheClient.CheckOrderIsExist(in.GetOrderId())
		if err != nil {
			log.ErrorWithCtx(ctx, "AddFansLoveValue CheckOrderIsExist failed in:%v err:%v", in, err)
			return out, err
		}

		if isExist {
			log.ErrorWithCtx(ctx, "AddFansLoveValue order is exist in:%v", in)
			return out, protocol.NewExactServerError(nil, status.ErrAddFansLoveValueOrderExistLimit)
		}
	}

	fansInfo, err := s.fansMgr.GetFansInfo(ctx, in.GetAnchorUid(), in.GetFansUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddFansLoveValue GetFansInfo failed in:%v err:%v", in, err)
	}

	if fansInfo.GroupId == NoValidGroupId {
		log.InfoWithCtx(ctx, "AddFansLoveValue no fans in:%v", in)
		return
	}

	nowTime := time.Now()
	newLoveValue, err := s.mDao.AddFansLoveValue(in.GetAnchorUid(), in.GetFansUid(), in.GetLoveValue(), nowTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddFansLoveValue Failed anchorUid %d fansUid %d loveValue %d err %s", in.GetAnchorUid(), in.GetFansUid(), in.GetLoveValue(), err.Error())
		return out, err
	}

	// 清除缓存
	err = s.fansMgr.DelFansInfo(ctx, in.GetAnchorUid(), in.GetFansUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddFansLoveValue DelFansInfo failed in:%v err:%v", in, err)
	}

	oldFansLevel, _, _, _ := conf.GetFansLevelInfo(fansInfo.LoveValue)
	newFansLevel, nextLevel, gapNextValue, valueRatio := conf.GetFansLevelInfo(newLoveValue)

	out.FansLevel = newFansLevel
	out.GroupId = fansInfo.GroupId
	out.NewLoveValue = newLoveValue
	if newFansLevel > oldFansLevel {
		log.DebugWithCtx(ctx, "AddFansLoveValue old %d new %d ", newFansLevel, oldFansLevel)

		go s.procFansLevelUpEv(in.GetAnchorUid(), in.GetFansUid(), in.GetChannelId(), newFansLevel)
	}

	isLightPlate := false
	// 推送粉丝铭牌重新亮起消息
	if !s.checkIsActiveFans(ctx, fansInfo) {
		isLightPlate = true

		fansPatePushMsg := &liveLogicPb.FansPlatePushMsg{
			IsValid: true,
		}
		livePushMsgBin, _ := proto.Marshal(fansPatePushMsg)

		var uidList []uint32
		uidList = append(uidList, in.GetFansUid())
		err = s.PushUserMsg(ctx, livePushMsgBin, uidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "AddFansLoveValue PushUserMsg failed %+v ", err)
		}

		// 删除置灰推送标志
		s.fansMgr.DelPushFlag(ctx, in.GetAnchorUid(), in.GetFansUid())
	}

	channelId := in.GetChannelId()
	if in.GetChannelId() == 0 {
		// 先获取直播信息
		liveInfo, err := s.cacheClient.GetAnchorLiveInfo(in.GetAnchorUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "AddFansLoveValue failed to GetAnchorLiveInfo in:%v err:%v", in, err)
		} else {
			channelId = liveInfo.ChannelId
		}

	}
	if channelId != 0 {
		s.fansMgr.PushFansInfoChangeMsg(ctx, in.GetFansUid(), channelId, &liveLogicPb.FansInfo{
			Uid:          in.GetFansUid(),
			IsFans:       true,
			FansLevel:    newFansLevel,
			IsValid:      true,
			LoveValue:    newLoveValue,
			GapNextValue: gapNextValue,
			NextLevel:    nextLevel,
			ValueRatio:   valueRatio,
		})
	}

	// 产生粉丝亲密变化kafka信息
	valueChangeEvent := &kafka_fans_event.FansLoveValueChange{
		FansUid:      in.GetFansUid(),
		AnchorUid:    in.GetAnchorUid(),
		LoveValue:    in.GetLoveValue(),
		ChannelId:    channelId,
		UpdateTs:     uint32(time.Now().Unix()),
		MissionId:    in.GetMissionId(),
		NewLevel:     newFansLevel,
		IsLightPlate: isLightPlate,
		OldLevel:     oldFansLevel,
	}

	s.fansValueKfkProduce.ProduceLoveValueChangeEvent(valueChangeEvent)

	log.InfoWithCtx(ctx, "AddFansLoveValue done in %+v out %+v channelId:%d", in, out, channelId)
	return out, nil
}

func (s *ChannelLiveFansServer) getKnightInfo(ctx context.Context, anchorUid, cid uint32) map[uint32]uint32 {
	subCtx, cancel := context.WithTimeout(context.Background(), time.Second*1)
	defer cancel()

	mapUid2KnightType := make(map[uint32]uint32, 0)

	knightResp, err := s.knightGroupMemCli.GetKnightGroupMember(subCtx, &knightGroupMemPb.GetKnightGroupMemberReq{
		ChannelId:      cid,
		AnchorUid:      anchorUid,
		WithMemberList: true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getKnightInfo GetKnightGroupMember failed anchor:%d cid:%d err:%v", anchorUid, cid, err)
		return mapUid2KnightType
	}

	nowTs := uint32(time.Now().Unix())
	for _, knight := range knightResp.GetKnightMemberList() {
		if knight.GetAnchorUid() == anchorUid && knight.GetBeginTime() <= nowTs && knight.GetEndTime() >= nowTs {
			mapUid2KnightType[knight.GetUid()] = uint32(pb.EnumKnightType_E_COMMON_KNIGHT)
			if knightResp.ChiefUid == knight.GetUid() {
				mapUid2KnightType[knight.GetUid()] = uint32(pb.EnumKnightType_E_CHIEF_KNIGHT)
			}
		}
	}

	log.DebugWithCtx(ctx, "getKnightInfo end map:%v", mapUid2KnightType)
	return mapUid2KnightType
}

func (s *ChannelLiveFansServer) GetFansRankList(ctx context.Context, in *pb.GetFansRankListReq) (out *pb.GetFansRankListResp, err error) {
	out = &pb.GetFansRankListResp{
		FansList:    make([]uint32, 0),
		FansInfoMap: make(map[uint32]*pb.FansInfo, 0),
	}

	// 因为mongo limit 0 offset 0 会取全部数据，所以给个默认值吧
	var limit uint32 = 15
	if in.GetLimit() != 0 {
		limit = in.GetLimit()
	}

	var fanInfos []mongo.FansGroupMemberInfo
	var activeFansCnt, inactiveFansCnt, newFansCnt uint32

	activeTime := s.fansMgr.GetFansActiveTime(ctx, in.GetAnchorUid())

	switch in.GetRankType() {
	case uint32(pb.EnumRankType_E_NEW):
		fanInfos, newFansCnt, err = s.fansMgr.GetNewAddFans(ctx, in.GetAnchorUid(), limit, in.GetOffset())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFansRankList GetNewAddFans failed anchorUid %d err %s", in.GetAnchorUid(), err.Error())
			return out, err
		}
	case uint32(pb.EnumRankType_E_ACTIVE):
		fanInfos, inactiveFansCnt, activeFansCnt, err = s.fansMgr.GetActiveFans(ctx, in.GetAnchorUid(), limit, in.GetOffset(), activeTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFansRankList GetActiveFans failed anchorUid %d err %s", in.GetAnchorUid(), err.Error())
			return out, err
		}
	case uint32(pb.EnumRankType_E_INACTIVE):
		fanInfos, inactiveFansCnt, err = s.fansMgr.GetInActiveFans(ctx, in.GetAnchorUid(), limit, in.GetOffset(), activeTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFansRankList GetInActiveFans failed anchorUid %d err %s", in.GetAnchorUid(), err.Error())
			return out, err
		}
	default:
		log.ErrorWithCtx(ctx, "GetFansRankList not rankType  anchorUid %d rankType %d", in.GetAnchorUid(), in.GetRankType())
		return out, nil
	}

	out.ActiveFansCnt = activeFansCnt
	out.NewFansCnt = newFansCnt
	out.InactivieFansCnt = inactiveFansCnt

	if len(fanInfos) == 0 {
		log.DebugWithCtx(ctx, "GetFansRankList empty in:%v out:%v", in, out)
		return out, nil
	}

	fansUids := make([]uint32, 0)
	for _, info := range fanInfos {
		fansUids = append(fansUids, info.FansUid)
	}

	mapUid2KnightType := make(map[uint32]uint32, 0)
	if in.GetCid() != 0 {
		// 获取骑士信息
		mapUid2KnightType = s.getKnightInfo(ctx, in.GetAnchorUid(), in.GetCid())
	}

	groupInfo, err := s.fansMgr.GetAnchorGroupInfo(ctx, in.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFansRankList mDao.GetGroupInfoByUid failed uid %d err %s", in.GetAnchorUid(), err.Error())
		return out, err
	}

	missionStatusMap, err := s.cacheClient.BatchGetFansMissionStatusByFans(in.GetAnchorUid(), fansUids)
	if err != nil {
		log.Errorf("GetFansRankList BatchGetFansMissionStatusByFans failed uid %d err %s", in.GetAnchorUid(), err.Error())
		return out, err
	}

	var anchorPlateConfig mongo.PlateConfigInfos
	if groupInfo.GroupName != "" {
		anchorPlateConfig, err = s.fansMgr.GetAnchorPlateConfig(ctx, in.GetAnchorUid(), groupInfo.GroupName)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFansRankList GetAnchorPlateConfig failed in:%v err:%v", in, err)
			return out, err
		}
	}

	plateInfo := make(map[uint32]*pb.FansPlateInfo)
	for _, info := range fanInfos {
		isFinishMission := false
		fansLevel, _, _, _ := conf.GetFansLevelInfo(info.LoveValue)
		if missionStatusMap[info.FansUid] == 1 {
			isFinishMission = true
		}
		if groupInfo.GroupName != "" {
			config := getNamePlateConfig(fillFansInfo(info.AnchorUid, info.FansUid, fansLevel, mapUid2KnightType[info.FansUid],
				info.ValueUpdateTime.Unix() >= activeTime.Unix(), isFinishMission), groupInfo.GroupName, anchorPlateConfig, s.confMgr.GetDefaultPlateId())
			plateInfo[info.FansUid] = &config
		} else {
			config := getCommonPlateConfig(fansLevel, info.ValueUpdateTime.Unix() >= activeTime.Unix())
			plateInfo[info.FansUid] = &config
		}
	}

	m := make(map[uint32]*pb.FansInfo)
	for _, info := range fanInfos {
		fansLevel, _, _, _ := conf.GetFansLevelInfo(info.LoveValue)
		fansInfo := &pb.FansInfo{
			Uid:       info.FansUid,
			FansLevel: fansLevel,
			LoveValue: info.LoveValue,
			PlateInfo: plateInfo[info.FansUid],
			GroupName: groupInfo.GroupName,
		}
		m[info.FansUid] = fansInfo
	}

	out.NextOffset = limit + in.GetOffset()
	if uint32(len(fansUids)) < limit {
		out.NextOffset = 0
	}

	out.FansList = fansUids
	out.FansInfoMap = m

	log.Debugf("ChannelLiveFansServer GetFansRankList end req %+v out %+v ", in, out)
	return out, nil
}

func (s *ChannelLiveFansServer) GetFansInfo(ctx context.Context, in *pb.GetFansInfoReq) (out *pb.GetFansInfoResp, err error) {
	out = &pb.GetFansInfoResp{
		FansInfo: &pb.FansInfo{
			Uid: in.GetUid(),
		},
	}

	log.DebugWithCtx(ctx, "GetFansInfo begin in:%v", in)

	fansInfo, err := s.fansMgr.GetFansInfo(ctx, in.GetAnchorUid(), in.GetUid())
	if err != nil {
		log.Debugf("GetFansInfo failed in:%v err:%v", in, err)
		return out, err
	}

	if fansInfo.GroupId == NoValidGroupId {
		// 不是粉丝团成员
		return out, nil
	}

	groupInfo, err := s.fansMgr.GetAnchorGroupInfo(ctx, in.GetAnchorUid())
	if err != nil {
		log.Errorf("GetFansInfo GetAnchorGroupInfo uid %d  failed err %s", in.GetAnchorUid(), err.Error())
		return out, err
	}
	groupName := groupInfo.GroupName

	status, err := s.cacheClient.GetFansMissionStatus(in.GetAnchorUid(), in.GetUid())
	if err != nil {
		log.Errorf("GetFansInfo GetFansMissionStatus uid %d anchorUid %d err %+v", in.GetUid(), in.GetAnchorUid(), err)
		return out, err
	}

	var isFinishMission bool
	if status == 1 {
		isFinishMission = true
	}

	fansLevel, nextLevel, gapNextValue, valueRatio := conf.GetFansLevelInfo(fansInfo.LoveValue)
	isActiveFans := s.checkIsActiveFans(ctx, fansInfo)

	var plateInfo pb.FansPlateInfo
	if groupName != "" {
		anchorPlateInfo, err := s.fansMgr.GetAnchorPlateConfig(ctx, in.GetAnchorUid(), groupName)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFansInfo GetAnchorPlateConfig failed in:%v err:%v", in, err)
			return out, err
		}
		plateInfo = getNamePlateConfig(fillFansInfo(in.GetAnchorUid(), in.GetUid(), fansLevel, in.GetKnightType(), isActiveFans, isFinishMission),
			groupName, anchorPlateInfo, s.confMgr.GetDefaultPlateId())
	} else {
		plateInfo = getCommonPlateConfig(fansLevel, isActiveFans)
	}

	out.FansInfo = &pb.FansInfo{
		Uid:          in.GetUid(),
		IsFans:       true,
		FansLevel:    fansLevel,
		IsValid:      isActiveFans,
		GapNextValue: gapNextValue,
		LoveValue:    fansInfo.LoveValue,
		NextLevel:    nextLevel,
		ValueRatio:   valueRatio,
		PlateInfo:    &plateInfo,
		GroupName:    groupName,
		AddGroupTs:   fansInfo.AddGroupTime.Unix(),
	}

	log.Debugf("ChannelLiveFansServer  GetFansInfo uid %d anchorUid %d out %+v", in.GetUid(), in.GetAnchorUid(), out)
	return out, nil
}

func (s *ChannelLiveFansServer) BatchGetFansInfo(ctx context.Context, in *pb.BatchGetFansInfoReq) (out *pb.BatchGetFansInfoResp, err error) {
	out = &pb.BatchGetFansInfoResp{}

	log.Debugf("BatchGetFansInfo begin in %+v ", in)

	mapId2Info, err := s.fansMgr.BatchGetFansInfo(ctx, in.GetAnchorUid(), in.GetUidList())
	if err != nil {
		log.Errorf("BatchGetFansInfo failed in:%v err:%v", in, err)
		return out, err
	}

	fansList := make([]uint32, 0)
	for _, info := range mapId2Info {
		if info.GroupId != NoValidGroupId {
			fansList = append(fansList, info.FansUid)
		}
	}

	groupInfo, err := s.fansMgr.GetAnchorGroupInfo(ctx, in.GetAnchorUid())
	if err != nil {
		log.Errorf("BatchGetFansInfo GetAnchorGroupInfo uid %d  failed err %s", in.GetAnchorUid(), err.Error())
		return out, err
	}
	groupName := groupInfo.GroupName

	missionStatusMap, err := s.cacheClient.BatchGetFansMissionStatusByFans(in.GetAnchorUid(), fansList)
	if err != nil {
		log.Errorf("BatchGetFansInfo BatchGetFansMissionStatusByFans failed uid %d err %s", in.GetAnchorUid(), err.Error())
		return out, err
	}

	var anchorPlateConfig mongo.PlateConfigInfos
	if groupName != "" {
		anchorPlateConfig, err = s.fansMgr.GetAnchorPlateConfig(ctx, in.GetAnchorUid(), groupName)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetFansInfo GetAnchorPlateConfig failed in:%v err:%v", in, err)
			return out, err
		}
	}

	activeTime := s.fansMgr.GetFansActiveTime(ctx, in.GetAnchorUid())

	plateInfo := make(map[uint32]*pb.FansPlateInfo)
	for _, uid := range fansList {
		isFinishMission := false
		if missionStatusMap[uid] == 1 {
			isFinishMission = true
		}
		fansLevel, _, _, _ := conf.GetFansLevelInfo(mapId2Info[uid].LoveValue)
		if groupName != "" {
			knightType := uint32(pb.EnumKnightType_E_NO_KNIGHT)
			if in.GetKnightTypeMap() != nil {
				knightType = in.GetKnightTypeMap()[uid]
			}
			config := getNamePlateConfig(fillFansInfo(in.GetAnchorUid(), uid, fansLevel, knightType, mapId2Info[uid].ValueUpdateTime.Unix() >= activeTime.Unix(),
				isFinishMission), groupName, anchorPlateConfig, s.confMgr.GetDefaultPlateId())
			plateInfo[uid] = &config
		} else {
			config := getCommonPlateConfig(fansLevel, mapId2Info[uid].ValueUpdateTime.Unix() >= activeTime.Unix())
			plateInfo[uid] = &config
		}
	}

	m := make(map[uint32]*pb.FansInfo)
	for _, uid := range fansList {
		fansLevel, nextLevel, gapNextValue, valueRatio := conf.GetFansLevelInfo(mapId2Info[uid].LoveValue)
		fansInfo := &pb.FansInfo{
			Uid:          uid,
			IsFans:       true,
			FansLevel:    fansLevel,
			IsValid:      mapId2Info[uid].ValueUpdateTime.Unix() >= activeTime.Unix(),
			LoveValue:    mapId2Info[uid].LoveValue,
			GapNextValue: gapNextValue,
			NextLevel:    nextLevel,
			ValueRatio:   valueRatio,
			GroupName:    groupName,
			PlateInfo:    plateInfo[uid],
		}
		m[uid] = fansInfo
	}

	out.FansInfoMap = m

	log.Debugf("ChannelLiveFansServer BatchGetFansInfo in %+v out %+v", in, out)
	return out, nil
}

func (s *ChannelLiveFansServer) GetAnchorFansInfo(ctx context.Context, in *pb.GetAnchorFansInfoReq) (out *pb.GetAnchorFansInfoResp, err error) {
	out = &pb.GetAnchorFansInfoResp{}

	log.Debugf("GetAnchorFansInfo in %+v", in)

	fansNum, err := s.mDao.GetFansCount(in.GetAnchorUid())
	if err != nil {
		log.Errorf("GetAnchorFansInfo mDao.GetFansCount failed anchorUid %d err %s", in.GetAnchorUid(), err.Error())
		return out, err
	}

	var groupName string
	validGroupName := ""
	verifyGroupNameInfo, err := s.mDao.GetNeedVerifyGroupNameByUid(in.GetAnchorUid())
	if err != nil && err != mgo.ErrNotFound {
		log.Errorf("GetAnchorFansInfo::GetNeedVerifyGroupNameByUid failed uid %d err %+v", in.GetAnchorUid(), err)
		return out, err
	}
	if err == mgo.ErrNotFound {
		groupInfo, err := s.fansMgr.GetAnchorGroupInfo(ctx, in.GetAnchorUid())
		if err != nil {
			log.Errorf("GetAnchorFansInfo GetAnchorGroupInfo failed anchorUid %d err %s", in.GetAnchorUid(), err.Error())
			return out, err
		}

		groupName = groupInfo.GroupName
		validGroupName = groupInfo.GroupName
	} else {
		groupName = verifyGroupNameInfo.GroupName
	}

	var anchorPlateConfig mongo.PlateConfigInfos
	if validGroupName != "" {
		anchorPlateConfig, err = s.fansMgr.GetAnchorPlateConfig(ctx, in.GetAnchorUid(), validGroupName)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorFansInfo GetAnchorPlateConfig failed in:%v err:%v", in, err)
			return out, err
		}
	}

	if groupName == "" {
		groupName = DefaultGroupName
	}

	plateInfo := getAnchorNamePlateConfig(in.GetAnchorUid(), groupName, anchorPlateConfig)

	out.GroupName = groupName
	out.FansCnt = fansNum
	out.ItemId = s.sc.GetAddGroupPresentId()
	out.NamePlateInfo = &plateInfo

	log.Debugf("GetAnchorFansInfo in %+v out %+v", in, out)
	return out, nil
}

func (s *ChannelLiveFansServer) pushLevelUpMsg(ctx context.Context, fromUid, targetUid, cid uint32, usersMap map[uint32]*app.UserProfile, pmsg proto.MessageV1) {

	// 判断是否在不推送白名单
	if s.fansMgr.CheckIsInNoPushList(cid) {
		log.InfoWithCtx(ctx, "pushLevelUpMsg no push cid %d", cid)
		return
	}

	// 神秘人直接不推了
	if usersMap[fromUid].GetPrivilege().GetAccount() != "" || usersMap[targetUid].GetPrivilege().GetAccount() != "" {
		log.InfoWithCtx(ctx, "pushLevelUpMsg no need push %d %d %d map:%v", fromUid, targetUid, cid, usersMap)
		return
	}

	var data []byte
	var err error
	if 0 != proto.Size(pmsg) {
		data, err = proto.Marshal(pmsg)
		if err != nil {
			log.DebugWithCtx(ctx, "Marshal failed:%+v", data)
			return
		}
	}

	log.DebugWithCtx(ctx, "pushLevelUpMsg fromUid %d cid %d", fromUid, cid)
	bMsg := channel.ChannelBroadcastMsg{
		FromUid:           fromUid,
		FromAccount:       usersMap[fromUid].GetAccount(),
		FromNick:          usersMap[fromUid].GetNickname(),
		TargetUid:         targetUid,
		TargetAccount:     usersMap[targetUid].GetAccount(),
		TargetNick:        usersMap[targetUid].GetNickname(),
		ToChannelId:       cid,
		Time:              uint64(time.Now().Unix()),
		Type:              uint32(channel.ChannelMsgType_CHANNEL_LIVE_FANS_MSG),
		PbOptContent:      data,
		FromUserProfile:   usersMap[fromUid],
		TargetUserProfile: usersMap[targetUid],
	}

	if usersMap[fromUid].GetPrivilege().GetAccount() != "" {
		// 旧版 神秘人
		bMsg.FromAccount = usersMap[fromUid].GetPrivilege().GetAccount()
		bMsg.FromNick = usersMap[fromUid].GetPrivilege().GetNickname()
	}

	if usersMap[targetUid].GetPrivilege().GetAccount() != "" {
		// 旧版 神秘人
		bMsg.TargetAccount = usersMap[targetUid].GetPrivilege().GetAccount()
		bMsg.TargetNick = usersMap[targetUid].GetPrivilege().GetNickname()
	}

	mb, err := bMsg.Marshal()
	if err != nil {
		log.DebugWithCtx(ctx, "Marshal failed %+v", bMsg)
		return
	}

	pMsg := pushPb.PushMessage{
		Cmd:     uint32(pushPb.PushMessage_CHANNEL_MSG_BRO),
		Content: mb,
		SeqId:   uint32(time.Now().Unix()),
	}

	mp, err := pMsg.Marshal()
	if err != nil {
		log.DebugWithCtx(ctx, "Marshal failed %+v", pMsg)
		return
	}

	notification := &push_notification.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		AppId:              uint32(protocol.TT),
		TerminalTypePolicy: push.DefaultPolicy,
		ProxyNotification: &push_notification.ProxyNotification{
			Type:       uint32(push_notification.ProxyNotification_PUSH),
			Payload:    mp,
			Policy:     push_notification.ProxyNotification_DEFAULT,
			ExpireTime: 60,
		},
	}

	err = s.pushCli.PushMulticast(ctx, uint64(cid), fmt.Sprintf("%d@channel", cid), []uint32{}, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushLevelUpMsg push failed cid %d err %v", cid, err)
	}

	log.InfoWithCtx(ctx, "pushLevelUpMsg done fromUid %d targetUid %d cid %d fnick %s tnick %s data %+v", fromUid,
		targetUid, cid, usersMap[fromUid].GetNickname(), usersMap[targetUid].GetNickname(), data)
}

func (s *ChannelLiveFansServer) PushUserMsg(ctx context.Context, msg []byte, uidList []uint32) error {

	if len(uidList) == 0 {
		log.ErrorWithCtx(ctx, "PushUserMsg uidList empty ")
		return nil
	}

	log.DebugWithCtx(ctx, "PushUserMsg begin %d", uidList[0])

	pushMessage := &pushPb.PushMessage{
		Cmd:     uint32(pushPb.PushMessage_CHANNEL_LIVE_FANS_STATUS_PUSH),
		Content: msg,
	}
	pushMessageBytes, _ := proto.Marshal(pushMessage)

	notification := &push_notification.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: push.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &push_notification.ProxyNotification{
			Type:    uint32(push_notification.ProxyNotification_PUSH),
			Payload: pushMessageBytes,
		},
	}
	log.InfoWithCtx(ctx, "PushUserMsg end %d", uidList[0])
	return s.pushCli.PushToUsers(ctx, uidList, notification)
}

func (s *ChannelLiveFansServer) GetAnchorFansData(ctx context.Context, in *pb.GetAnchorFansDataReq) (out *pb.GetAnchorFansDataResp, err error) {
	out = &pb.GetAnchorFansDataResp{}
	log.DebugWithCtx(ctx, "GetAnchorFansData in %v", in)
	for startTs := in.GetBeginTime(); startTs <= in.GetEndTime(); {
		cnt, _ := s.mDao.GetFansCntByPeriod(in.GetAnchorUid(), startTs, startTs+86399)
		date := time.Unix(int64(startTs), 0)
		data := &pb.FansData{
			Date:    date.Format("20060102"),
			FansCnt: cnt,
		}
		out.FansDataList = append(out.FansDataList, data)
		startTs = startTs + 86400
	}
	log.DebugWithCtx(ctx, "GetAnchorFansData out %v", out)
	return out, err
}

func (s *ChannelLiveFansServer) AddAnchorPopularity(ctx context.Context, in *pb.AddAnchorPopularityReq) (out *pb.AddAnchorPopularityResp, err error) {
	out = &pb.AddAnchorPopularityResp{}
	log.DebugWithCtx(ctx, "AddAnchorPopularity in %+v", in)

	bIsFirst, err := s.cacheClient.CheckIsFirstAddPopularity(in.GetFansUid(), in.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddAnchorPopularity CheckIsFirstAddPopularity failed %d %d err %+v", in.GetFansUid(), in.GetAnchorUid(), err)
		return out, err
	}

	channelId := in.GetChannelId()
	channelTagResp, err := s.enterCli.GetChannelTag(ctx, in.GetFansUid(), &entertainmentPB.GetChannelTagReq{
		ChannelId: &channelId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddAnchorPopularity GetChannelTag failed uid %d targetUid %d channelId %u err %+v", in.GetFansUid(), in.GetChannelId(), channelId, err)
		return out, err
	}

	if bIsFirst {
		s.anchorKfkProduce.ProducePopularityChangeEvent(in.GetFansUid(), in.GetAnchorUid(), in.GetChannelId(), channelTagResp.GetTagInfo().GetTagId(), uint32(time.Now().Unix()))
	}

	log.DebugWithCtx(ctx, "AddAnchorPopularity isFirst %d in %+v out %+v", bIsFirst, in, out)
	return out, err
}

func (s *ChannelLiveFansServer) GetFansAllAnchorsInfo(ctx context.Context, in *pb.GetFansAllAnchorsInfoReq) (out *pb.GetFansAllAnchorsInfoResp, err error) {
	out = &pb.GetFansAllAnchorsInfoResp{}

	log.DebugWithCtx(ctx, "GetFansAllAnchorsInfo in %+v", in)

	var fanInfos []mongo.FansGroupMemberInfo
	fanInfos, err = s.mDao.GetFansAllAnchorsInfo(in.GetFansUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFansAllAnchorsInfo mDao.GetFansAllAnchorsInfo failed fansUid %d err %s", in.GetFansUid(), err.Error())
		return out, err
	}

	for _, info := range fanInfos {
		out.AnchorUids = append(out.AnchorUids, info.AnchorUid)
	}

	log.DebugWithCtx(ctx, "GetFansAllAnchorsInfo in %+v out %+v", in, out)
	return out, nil
}

func (s *ChannelLiveFansServer) GetFansAddedGroupList(ctx context.Context, in *pb.GetFansAddedGroupListReq) (out *pb.GetFansAddedGroupListResp, err error) {
	out = &pb.GetFansAddedGroupListResp{
		AnchorList:    make([]uint32, 0),
		AddedGroupMap: make(map[uint32]*pb.FansAddedGroupInfo, 0),
		AddedGroupCnt: 0,
	}

	log.DebugWithCtx(ctx, "GetFansAddedGroupList begin in %+v", in)

	var addedGroupNum int64 = 0
	fansInfoMap := make(map[uint32]mongo.FansGroupMemberInfo)
	if in.GetOffset() == 0 {
		// 客户端第一次请求，先从mongo加载到redis
		fansInfoMap, addedGroupNum, err = s.fansMgr.LoadFansAddedGroupToCache(ctx, in.GetFansUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFansAddedGroupList::LoadFansAddedGroupToCache failed fansUid %d err %+v", in.GetFansUid(), err)
			return out, err
		}
	}

	uidList, err := s.cacheClient.GetFansAddedGroupList(in.GetFansUid(), in.GetOffset(), in.GetOffset()+in.GetLimit()-1)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFansAddedGroupList::GetFansAddedGroupList failed %d %d %d error %+v", in.GetFansUid(), in.GetOffset(), in.GetLimit(), err)
		return out, err
	}

	if len(uidList) == 0 {
		return out, nil
	}

	if in.GetOffset() > 0 {
		addedGroupNum, err = s.cacheClient.GetFansAddedGroupNum(in.GetFansUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFansAddedGroupList::GetFansAddedGroupNum failed fansUid %d err %+v", in.GetFansUid(), err)
			return out, err
		}

		for _, anchorUid := range uidList {
			fansInfo, err := s.fansMgr.GetFansInfo(ctx, anchorUid, in.GetFansUid())
			if err != nil {
				log.ErrorWithCtx(ctx, "GetFansAddedGroupList GetFansInfo failed in:%v anchorUid:%d err %s", in, anchorUid, err.Error())
				continue
			}
			fansInfoMap[anchorUid] = fansInfo
		}
	}

	missionStatusMap, err := s.cacheClient.BatchGetFansMissionStatusByAnchors(in.GetFansUid(), uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFansAddedGroupList BatchGetFansMissionStatusByAnchors failed fansUid %d err %v", in.GetFansUid(), err)
		return out, err
	}

	groupInfoMap, err := s.fansMgr.BatchGetAnchorGroupInfo(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFansAddedGroupList BatchGetAnchorGroupInfo failed fansUid:%d uidList:%v err %v", in.GetFansUid(), uidList, err)
		return out, err
	}

	plateInfo := make(map[uint32]*pb.FansPlateInfo)
	for _, uid := range uidList {
		isFinishMission := false
		if missionStatusMap[uid] == 1 {
			isFinishMission = true
		}
		fansLevel, _, _, _ := conf.GetFansLevelInfo(fansInfoMap[uid].LoveValue)
		if groupInfoMap[uid].GroupName != "" {
			anchorPlate, err := s.fansMgr.GetAnchorPlateConfig(ctx, uid, groupInfoMap[uid].GroupName)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetFansAddedGroupList GetAnchorPlateConfig failed id:%d in:%v err:%v", uid, in, err)
				continue
			}
			info := getNamePlateConfig(fillFansInfo(fansInfoMap[uid].AnchorUid, fansInfoMap[uid].FansUid, fansLevel, 0, s.checkIsActiveFans(ctx, fansInfoMap[uid]),
				isFinishMission), groupInfoMap[uid].GroupName, anchorPlate, s.confMgr.GetDefaultPlateId())
			plateInfo[uid] = &info
		} else {
			info := getCommonPlateConfig(fansLevel, s.checkIsActiveFans(ctx, fansInfoMap[uid]))
			plateInfo[uid] = &info
		}
	}

	m := make(map[uint32]*pb.FansAddedGroupInfo, 0)
	for _, uid := range uidList {
		fansLevel, _, _, _ := conf.GetFansLevelInfo(fansInfoMap[uid].LoveValue)
		info := &pb.FansAddedGroupInfo{
			GroupName: groupInfoMap[uid].GroupName,
			FansLevel: fansLevel,
			PlateInfo: plateInfo[uid],
			LoveValue: fansInfoMap[uid].LoveValue,
		}
		m[uid] = info
	}

	out.AddedGroupMap = m
	out.AddedGroupCnt = uint32(addedGroupNum)
	out.AnchorList = uidList

	log.DebugWithCtx(ctx, "GetFansAddedGroupList in %+v out %+v", in, out)
	return out, nil
}

func (s *ChannelLiveFansServer) CheckGroupNameExist(ctx context.Context, in *pb.CheckGroupNameExistReq) (out *pb.CheckGroupNameExistResp, err error) {
	out = &pb.CheckGroupNameExistResp{}

	log.DebugWithCtx(ctx, "CheckGroupNameExist in %+v", in)

	isExist, err := s.mDao.CheckGroupNameIsExists(in.GetGroupName())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGroupNameExist CheckGroupNameIsExists failed uid %d err %+v", in.GetAnchorUid(), err)
		return out, err
	}

	if isExist {
		log.InfoWithCtx(ctx, "CheckGroupNameExist group name is exists uid %d groupName %s", in.GetAnchorUid(), in.GetGroupName())
		out.IsExist = true
		return out, nil
	}

	isExist, err = s.mDao.CheckGroupNameIsVerify(in.GetGroupName())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGroupNameExist CheckGroupNameIsVerify failed uid %d err %+v", in.GetAnchorUid(), err)
		return out, err
	}

	if isExist {
		log.InfoWithCtx(ctx, "CheckGroupNameExist group name is verifying uid %d groupName %s", in.GetAnchorUid(), in.GetGroupName())
		out.IsExist = true
		return out, nil
	}
	log.DebugWithCtx(ctx, "CheckGroupNameExist in %+v out %+v", in, out)
	return out, nil
}

func (s *ChannelLiveFansServer) SetFansGroupName(ctx context.Context, in *pb.SetFansGroupNameReq) (out *pb.SetFansGroupNameResp, err error) {
	out = &pb.SetFansGroupNameResp{}

	//查询团名是否存在
	isExist, err := s.mDao.CheckGroupNameIsExists(in.GetGroupName())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGroupNameExist CheckGroupNameIsExists failed uid %d err %+v", in.GetAnchorUid(), err)
		return out, err
	}

	if isExist {
		log.InfoWithCtx(ctx, "CheckGroupNameExist group name is exists uid %d groupName %s", in.GetAnchorUid(), in.GetGroupName())
		return out, protocol.NewExactServerError(nil, status.ErrFansGroupNameIsExist)
	}

	//查询是否有相同的团名在审核
	isExist, err = s.mDao.CheckGroupNameIsVerify(in.GetGroupName())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGroupNameExist CheckGroupNameIsVerify failed uid %d err %+v", in.GetAnchorUid(), err)
		return out, err
	}

	if isExist {
		log.InfoWithCtx(ctx, "CheckGroupNameExist group name is verifying uid %d groupName %s", in.GetAnchorUid(), in.GetGroupName())
		return out, protocol.NewExactServerError(nil, status.ErrFansGroupNameIsExist)
	}

	// 审核
	isPass, smErr := s.TextCheck(ctx, in.GetAnchorUid(), in.GetGroupName(), in.GetIpAddr(), in.GetDeviceId(), in.GetSmdeviceId())
	if smErr != nil {
		return out, protocol.NewExactServerError(nil, status.ErrSys)
	}

	if !isPass {
		return out, protocol.NewExactServerError(nil, status.ErrFansGroupNameNotStanderd)
	}

	//设置用户有团名在审核标记
	err = s.cacheClient.SetGroupNameVerifyInfo(in.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGroupNameExist SetGroupNameVerifyInfo failed uid:%d err:%v", in.GetAnchorUid(), err)
	}

	log.DebugWithCtx(ctx, "SetFansGroupName in %+v out %+v", in, out)
	return out, nil
}

func (s *ChannelLiveFansServer) TextCheck(ctx context.Context, uid uint32, groupName, ip, deviceId, smDeviceId string) (bool, error) {
	userInfo, err := s.accountClient.GetUser(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "TextCheck GetUser failed uid %d err %v", uid, err)
		return false, err
	}

	checkResp, serr := s.censoringProxyClient.Text().SyncScanText(ctx, &v2.SyncTextCheckReq{
		Text: groupName,
		Context: &v2.TaskContext{
			Category: "MODIFY_FANS_GROUP_NAME",
			UserInfo: &v2.User{
				Id:       uint64(userInfo.GetUid()),
				Phone:    userInfo.GetPhone(),
				Nickname: userInfo.GetNickname(),
				Alias:    userInfo.GetAlias(),
			},
			DeviceInfo: &v2.Device{
				Id: deviceId,
				Ip: ip,
			},
		},
	})

	if serr != nil {
		log.ErrorWithCtx(ctx, "TextCheck failed uid %d groupName %s err %v", uid, groupName, serr)
		return false, serr
	}

	if checkResp.GetResult() == uint32(v2.Suggestion_REJECT) {
		log.InfoWithCtx(ctx, "TextCheck no pass uid %d groupName %s result %d", uid, groupName, checkResp.GetResult())
		return false, nil
	}

	log.DebugWithCtx(ctx, "TextCheck uid %d groupName %s result %d ", uid, groupName, checkResp.GetResult())
	return true, nil
}

func (s *ChannelLiveFansServer) GetNeedVerifyGroupNameList(ctx context.Context, in *pb.GetNeedVerifyGroupNameListReq) (out *pb.GetNeedVerifyGroupNameListResp, err error) {
	out = &pb.GetNeedVerifyGroupNameListResp{}

	log.DebugWithCtx(ctx, "GetNeedVerifyGroupNameList in %+v", in)

	var verifyGroupNameList []mongo.GroupNameVerifyInfo
	var totalCnt uint32
	if in.GetUid() != 0 {
		verifyGroupNameInfo, err := s.mDao.GetNeedVerifyGroupNameByUid(in.GetUid())
		if err != nil && err != mgo.ErrNotFound {
			log.ErrorWithCtx(ctx, "GetNeedVerifyGroupNameList::GetNeedVerifyGroupNameByUid failed uid %d err %+v", in.GetUid(), err)
			return out, err
		}

		if err == nil {
			verifyGroupNameList = append(verifyGroupNameList, verifyGroupNameInfo)
		}
		totalCnt = uint32(len(verifyGroupNameList))
	} else {
		verifyGroupNameList, err = s.mDao.GetNeedVerifyGroupNameList(int(in.GetLimit()), int(in.GetOffset()))
		if err != nil {
			log.ErrorWithCtx(ctx, "GetNeedVerifyGroupNameList::GetNeedVerifyGroupNameList failed err %+v", err)
			return out, err
		}

		totalCnt, err = s.mDao.GetNeedVerifyGroupNameCount()
		if err != nil {
			log.ErrorWithCtx(ctx, "GetNeedVerifyGroupNameList::GetNeedVerifyGroupNameCount failed uid %d err %+v", in.GetUid(), err)
			return out, err
		}

		out.NextOffset = in.GetLimit() + in.GetOffset()
		if uint32(len(verifyGroupNameList)) < in.GetLimit() {
			out.NextOffset = 0
		}
	}

	for _, verifyInfo := range verifyGroupNameList {
		info := &pb.VerifyGroupNameInfo{
			AnchorUid: verifyInfo.AnchorUid,
			GroupName: verifyInfo.GroupName,
			SummitTs:  verifyInfo.SummitTs,
		}
		out.InfoList = append(out.InfoList, info)
	}

	out.TotalCnt = totalCnt

	log.DebugWithCtx(ctx, "GetNeedVerifyGroupNameList in %+v out %+v", in, out)
	return out, nil
}

func (s *ChannelLiveFansServer) GroupNameVerifiedInfo(ctx context.Context, in *pb.GroupNameVerifiedInfoReq) (out *pb.GroupNameVerifiedInfoResp, err error) {
	out = &pb.GroupNameVerifiedInfoResp{}

	log.DebugWithCtx(ctx, "GroupNameVerifiedInfo in %+v", in)

	err = s.mDao.DelNeedVerifyGroupName(in.GetAnchorUid(), in.GetGroupName())
	if err != nil {
		log.ErrorWithCtx(ctx, "GroupNameVerifiedInfo::DelNeedVerifyGroupName failed uid %d groupName %s err %+v", in.GetAnchorUid(), in.GetGroupName(), err)
		return out, err
	}

	// 删除主播团名正在审核标记
	err = s.cacheClient.DelGroupNameVerifyInfo(in.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GroupNameVerifiedInfo DelGroupNameVerifyInfo failed uid:%d err:%v", in.GetAnchorUid(), err)
	}

	if in.GetVerifyResult() == uint32(pb.GroupNameVerifiedInfoReq_E_RESUTL_PASS) {
		err = s.mDao.UpdateGroupName(in.GetAnchorUid(), in.GetGroupName())
		if err != nil {
			log.ErrorWithCtx(ctx, "GroupNameVerifiedInfo::UpdateGroupName failed uid %d groupName %s err %+v", in.GetAnchorUid(), in.GetGroupName(), err)
			return out, err
		}

		nowTs := uint32(time.Now().Unix())
		err = s.cacheClient.SetAnchorSetGroupNameTs(in.GetAnchorUid(), nowTs)
		if err != nil {
			log.ErrorWithCtx(ctx, "GroupNameVerifiedInfo SetAnchorSetGroupNameTs failed uid %d groupName %s err %+v", in.GetAnchorUid(), in.GetGroupName(), err)
		}

		err = s.fansMgr.DelAnchorGroupInfo(ctx, in.GetAnchorUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "GroupNameVerifiedInfo DelAnchorGroupInfo failed uid:%d err:%v", in.GetAnchorUid(), err)
		}

		s.fansMgr.DelAnchorPlateCache(ctx, in.GetAnchorUid())

		// 推送设置团名成功助手消息
		msg := "你的粉丝团名称已更新为" + "“" + in.GetGroupName() + "”," + "快去告诉你的小粉丝们吧~"
		s.SendTTMsg(ctx, in.GetAnchorUid(), msg)
	}

	if in.GetVerifyResult() == uint32(pb.GroupNameVerifiedInfoReq_E_RESULT_NOT_PASS) {
		// 推送设置团名失败助手消息
		msg := "你的粉丝团名称" + "“" + in.GetGroupName() + "”" + "由于" + in.GetNotPassReason() + "原因没有过审，请遵循官方规定合理起名。"
		s.SendTTMsg(ctx, in.GetAnchorUid(), msg)
	}

	if in.GetOperateTs() != 0 {
		// 新后台操作，记录审核记录
		err = s.mDao.AddGroupNameVerifyRecord(&mongo.GroupNameVerifyRecord{
			AnchorUid:     in.GetAnchorUid(),
			GroupName:     in.GetGroupName(),
			SummitTs:      int64(in.GetSummitTs()),
			VerifyResult:  in.GetVerifyResult(),
			NotPassReason: in.GetNotPassReason(),
			Operator:      in.GetOperator(),
			OperateTs:     in.GetOperateTs(),
			AnchorTTid:    in.GetAnchorTtid(),
			AnchorNick:    in.GetAnchorNick(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "AddGroupNameVerifyRecord failed info:%v err %+v", in, err)
		}
	}

	log.DebugWithCtx(ctx, "GroupNameVerifiedInfo in %+v out %+v", in, out)
	return out, nil
}

func (s *ChannelLiveFansServer) CheckSetGroupNamePermit(ctx context.Context, in *pb.CheckSetGroupNamePermitReq) (out *pb.CheckSetGroupNamePermitResp, err error) {
	out = &pb.CheckSetGroupNamePermitResp{}

	log.DebugWithCtx(ctx, "CheckSetGroupNamePermit in %+v", in)

	lastSetTs, err := s.cacheClient.GetAnchorSetGroupNameTs(in.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckSetGroupNamePermit  GetAnchorSetGroupNameTs failed anchorUid %d err %s", in.GetAnchorUid(), err.Error())
		return out, err
	}

	nowTm := time.Now()
	lastTm := time.Unix(int64(lastSetTs), 0)

	lastTs := time.Date(lastTm.Year(), lastTm.Month(), lastTm.Day(), 0, 0, 0, 0, time.Local).Unix()
	nowTs := time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day(), 0, 0, 0, 0, time.Local).Unix()

	var dayCnt uint32
	if nowTs > lastTs {
		dayCnt = uint32((nowTs - lastTs) / OneDayTs)
	}

	if dayCnt < s.confMgr.GetGroupNameDayLimit() {
		log.InfoWithCtx(ctx, "CheckSetGroupNamePermit day cnt limit uid:%d lastSetTs:%d dayCnt:%d", in.GetAnchorUid(), lastSetTs, dayCnt)
		return out, protocol.NewExactServerError(nil, status.ErrFansSetGroupNameCntLimit, fmt.Sprintf(SetGroupNameErrMsg, s.confMgr.GetGroupNameDayLimit(), s.confMgr.GetGroupNameDayLimit()-dayCnt))
	}

	fansNum, err := s.mDao.GetFansCount(in.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckSetGroupNamePermit mDao.GetFansCount failed anchorUid %d err %s", in.GetAnchorUid(), err.Error())
		return out, err
	}

	// 粉丝团人数达到10才可以自定义团名
	if fansNum < set_group_name_member_limit {
		log.InfoWithCtx(ctx, "CheckSetGroupNamePermit fans count no reach limit uid %d cnt %d", in.GetAnchorUid(), fansNum)
		return out, protocol.NewExactServerError(nil, status.ErrFansGroupNameMemberCntLimit)
	}

	isVerifing := s.cacheClient.CheckGroupNameIsVerify(in.GetAnchorUid())

	// 正在审核，不能再设置粉丝团名称
	if isVerifing {
		log.InfoWithCtx(ctx, "CheckSetGroupNamePermit groupName is verifing uid %d", in.GetAnchorUid())
		return out, protocol.NewExactServerError(nil, status.ErrFansGroupNameVerifying)
	}

	out.Desc = fmt.Sprintf(SetGroupNameDesc, s.confMgr.GetGroupNameDayLimit(), s.confMgr.GetGroupNameDayLimit())

	log.DebugWithCtx(ctx, "CheckSetGroupNamePermit in %+v out %+v", in, out)
	return out, nil
}

func FillMongoPlateMsg(plateConfigs []*pb.PlateConfig, plateDecoratios []*pb.PlateDecoration) ([]mongo.PlateConfig, []mongo.PlateDecoration) {

	plateConfigList := make([]mongo.PlateConfig, 0)
	for _, info := range plateConfigs {
		var plateImgList []mongo.PlateImgConfig
		for _, imgInfo := range info.GetImgList() {
			plateImg := mongo.PlateImgConfig{
				ImgUrl:         imgInfo.GetImgUrl(),
				NameFontCntMin: imgInfo.GetNameFontCntMin(),
				NameFontCntMax: imgInfo.GetNameFontCntMax(),
				Width:          imgInfo.GetWidth(),
				Height:         imgInfo.GetHeight(),
				LevelWidth:     imgInfo.GetLevelWidth(),
			}
			plateImgList = append(plateImgList, plateImg)
		}
		plateInfo := mongo.PlateConfig{
			PlateImgList:   plateImgList,
			LevelFontColor: info.GetLevelFontColor(),
			MinLevel:       info.GetMinLevel(),
			MaxLevel:       info.GetMaxLevel(),
		}
		plateConfigList = append(plateConfigList, plateInfo)
	}

	plateDecorationList := make([]mongo.PlateDecoration, 0)
	for _, info := range plateDecoratios {
		decorations := mongo.PlateDecoration{
			ImgUrl:         info.GetImgUrl(),
			NameFontCntMin: info.GetNameFontCntMin(),
			NameFontCntMax: info.GetNameFontCntMax(),
			Width:          info.GetWidth(),
			Height:         info.GetHeight(),
		}
		plateDecorationList = append(plateDecorationList, decorations)
	}

	return plateConfigList, plateDecorationList
}

func FillPbPlateMsg(plateConfig mongo.PlateConfigInfos) *pb.PlateConfigInfos {

	var config *pb.PlateConfigInfos

	plateConfigs := make([]*pb.PlateConfig, 0)
	for _, info := range plateConfig.PlateConfigList {
		var plateImgs []*pb.PlateImgConfig
		for _, imgInfo := range info.PlateImgList {
			plateImg := &pb.PlateImgConfig{
				ImgUrl:         imgInfo.ImgUrl,
				NameFontCntMin: imgInfo.NameFontCntMin,
				NameFontCntMax: imgInfo.NameFontCntMax,
				Width:          imgInfo.Width,
				Height:         imgInfo.Height,
				LevelWidth:     imgInfo.LevelWidth,
			}
			plateImgs = append(plateImgs, plateImg)
		}
		plateInfo := &pb.PlateConfig{
			ImgList:        plateImgs,
			LevelFontColor: info.LevelFontColor,
			MinLevel:       info.MinLevel,
			MaxLevel:       info.MaxLevel,
		}
		plateConfigs = append(plateConfigs, plateInfo)
	}

	plateDecorationList := make([]*pb.PlateDecoration, 0)
	for _, info := range plateConfig.PlateDecorationList {
		decoration := &pb.PlateDecoration{
			ImgUrl:         info.ImgUrl,
			NameFontCntMin: info.NameFontCntMin,
			NameFontCntMax: info.NameFontCntMax,
			Width:          info.Width,
			Height:         info.Height,
		}
		plateDecorationList = append(plateDecorationList, decoration)
	}

	config = &pb.PlateConfigInfos{
		PlateId:             plateConfig.PlateId,
		PlateName:           plateConfig.PlateName,
		NameFontColor:       plateConfig.NameFontColor,
		PlateConfigList:     plateConfigs,
		PlateDecorationList: plateDecorationList,
	}

	return config
}

func (s *ChannelLiveFansServer) SendTTMsg(ctx context.Context, uid uint32, content string) error {

	log.DebugWithCtx(ctx, "SendTTMsg begin. uid:%d, content:%s", uid, content)

	msg := &apiPB.ImMsg{
		ImType: &apiPB.ImType{
			SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
			ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
			ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT),
		},
		FromUid: uint32(10000),
		ToIdList: []uint32{
			uid,
		},
		ImContent: &apiPB.ImContent{
			TextNormal: &apiPB.ImTextNormal{
				Content: content,
			},
		},
		Platform:    apiPB.Platform_UNSPECIFIED,
		AppPlatform: "all",
	}

	err := s.apiCli.SendImMsg(ctx, uid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendTTMsg fail. uid:%d err: %s", uid, err.Error())
		return err
	}

	notify.NotifySync(uid, sync.SyncReq_IM_MSG)

	log.DebugWithCtx(ctx, "SendTTMsg done. uid:%d, content:%s, Msg:%+v", uid, content, msg)
	return nil
}

func (s *ChannelLiveFansServer) SetFansMissionFinishStatus(ctx context.Context, in *pb.SetFansMissionFinishStatusReq) (out *pb.SetFansMissionFinishStatusResp, err error) {
	out = &pb.SetFansMissionFinishStatusResp{}

	log.DebugWithCtx(ctx, "SetFansMissionFinishStatus in %v", in)

	err = s.cacheClient.SetFansMissionStatus(in.GetAnchorUid(), in.GetFansUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetFansMissionFinishStatus anchorUid %d fansUid %d err %v", in.GetAnchorUid(), in.GetFansUid(), err)
		return out, err
	}

	log.DebugWithCtx(ctx, "SetFansMissionFinishStatus out %v", out)
	return out, err
}

func (s *ChannelLiveFansServer) CheckUserIsFans(ctx context.Context, in *pb.CheckUserIsFansReq) (out *pb.CheckUserIsFansResp, err error) {
	out = &pb.CheckUserIsFansResp{}
	out.IsFans = false
	out.ItemId = s.sc.GetAddGroupPresentId()

	log.DebugWithCtx(ctx, "CheckUserIsFans begin in:%v out:%v", in, out)

	fansInfo, err := s.fansMgr.GetFansInfo(ctx, in.GetAnchorUid(), in.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckUserIsFans GetFansInfo failed in:%v err:%v", in, err)
		return out, err
	}

	if fansInfo.GroupId == NoValidGroupId {
		log.DebugWithCtx(ctx, "CheckUserIsFans not fans in:%v err:%v", in, err)
		return out, nil
	}

	fansLevel, _, _, _ := conf.GetFansLevelInfo(fansInfo.LoveValue)

	out.IsFans = true
	out.FansLevel = fansLevel

	log.DebugWithCtx(ctx, "ChannelLiveFansServer CheckUserIsFans info %+v err %+v", fansInfo, err)
	return out, nil
}

func (s *ChannelLiveFansServer) GetFansCntByTs(ctx context.Context, in *pb.GetFansCntByTsReq) (*pb.GetFansCntByTsResp, error) {
	out := &pb.GetFansCntByTsResp{}

	log.DebugWithCtx(ctx, "GetFansCntByTs begin in:%v", in)
	tm := time.Unix(int64(in.GetTs()), 0)
	fansNum, err := s.mDao.GetFansCntByTs(in.GetAnchorUid(), tm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFansCntByTs mDao.GetFansCount failed anchorUid %d err %s", in.GetAnchorUid(), err.Error())
		return out, err
	}

	out.FansCnt = fansNum
	log.DebugWithCtx(ctx, "GetFansCntByTs end in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveFansServer) WearAnchorPlate(ctx context.Context, in *pb.WearAnchorPlateReq) (*pb.WearAnchorPlateResp, error) {
	out := &pb.WearAnchorPlateResp{}

	log.DebugWithCtx(ctx, "WearAnchorPlate begin in:%v", in)

	groupInfo, err := s.fansMgr.GetAnchorGroupInfo(ctx, in.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "WearAnchorPlate GetAnchorGroupInfo failed in:%v err:%v", in, err)
		return out, protocol.ToServerError(err)
	}

	if groupInfo.GroupName == "" {
		log.Infof("WearAnchorPlate groupName is empty unable wear in:%v info:%v", in, groupInfo)
		return out, protocol.NewExactServerError(nil, status.ErrFansGroupWearPlateLimit)
	}

	out, err = s.fansMgr.WearAnchorPlate(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "WearAnchorPlate failed in:%v err:%v", in, err)
		return out, protocol.ToServerError(err)
	}

	log.DebugWithCtx(ctx, "WearAnchorPlate end in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveFansServer) GetAnchorValidGrantPlates(ctx context.Context, in *pb.GetAnchorValidGrantPlatesReq) (*pb.GetAnchorValidGrantPlatesResp, error) {
	out := &pb.GetAnchorValidGrantPlatesResp{}

	log.DebugWithCtx(ctx, "GetAnchorValidGrantPlates begin in:%v", in)

	groupName := ""
	validGroupName := ""
	//审核中的团名
	verifyGroupNameInfo, err := s.mDao.GetNeedVerifyGroupNameByUid(in.GetAnchorUid())
	if err != nil && err != mgo.ErrNotFound {
		log.Errorf("GetAnchorValidGrantPlates::GetNeedVerifyGroupNameByUid failed uid %d err %+v", in.GetAnchorUid(), err)
		return out, err
	}
	if err == mgo.ErrNotFound {
		groupInfo, err := s.fansMgr.GetAnchorGroupInfo(ctx, in.GetAnchorUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorValidGrantPlates GetAnchorGroupInfo failed anchorUid %d err %s", in.GetAnchorUid(), err.Error())
			return out, err
		}

		groupName = groupInfo.GroupName
		validGroupName = groupInfo.GroupName
	} else {
		groupName = verifyGroupNameInfo.GroupName
	}

	mapGrantId2Conf, mapGrantId2GrantInfo, err := s.fansMgr.GetAnchorAllValidPlate(ctx, in.GetAnchorUid(), validGroupName)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorValidGrantPlates GetAnchorAllValidPlate failed in:%v err:%v", in, err)
		return out, err
	}

	if groupName == "" {
		groupName = DefaultGroupName
	}

	nowTs := time.Now().Unix()
	for grantId, tmpConf := range mapGrantId2Conf {
		if mapGrantId2GrantInfo[grantId].EndTs < nowTs {
			continue
		}

		namePlateInfo := getAnchorNamePlate(groupName, tmpConf)
		namePlateInfo.IsWear = mapGrantId2GrantInfo[grantId].IsWear
		namePlateInfo.GrantId = grantId
		namePlateInfo.RemainDays = uint32((mapGrantId2GrantInfo[grantId].EndTs-nowTs)/(24*3600) + 1)

		if tmpConf.PlateId == s.confMgr.GetDefaultPlateId() {
			namePlateInfo.IsDefault = true
		}

		out.InfoList = append(out.InfoList, &namePlateInfo)
	}

	log.DebugWithCtx(ctx, "GetAnchorValidGrantPlates end in:%v out:%v", in, out)
	return out, nil
}

// 加入粉丝团API
func (s *ChannelLiveFansServer) JoinFansGroup(ctx context.Context, in *pb.JoinFansGroupReq) (*pb.JoinFansGroupResp, error) {
	rsp := &pb.JoinFansGroupResp{}
	err, _ := s.kafkaSub.HandAddFansEvent(ctx, &kafkapresent.PresentEvent{
		Uid:         in.Uid,
		TargetUid:   in.AnchorUid,
		ChannelId:   in.ChannelId,
		ChannelType: in.ChannelType,
		OrderId:     in.OrderId,
		SendTime:    in.CreateTime,
	}, in.FromDesc)
	if err != nil {
		log.ErrorWithCtx(ctx, "JoinFansGroup in:%s, err:%v", in.String(), err)
		return rsp, err
	}
	log.InfoWithCtx(ctx, "JoinFansGroup ok in:%s", in.String())

	return rsp, nil
}

func (s *ChannelLiveFansServer) LeaveFansGroup(ctx context.Context, req *pb.LeaveFansGroupReq) (*pb.LeaveFansGroupResp, error) {
	resp := &pb.LeaveFansGroupResp{}

	fansInfo, err := s.fansMgr.GetFansInfo(ctx, req.GetAnchorUid(), req.GetFansUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "LeaveFansGroup GetFansInfo failed req:%v err:%v", req, err)
		return resp, err
	}

	if fansInfo.GroupId == NoValidGroupId {
		log.InfoWithCtx(ctx, "LeaveFansGroup not fans req:%v", req, err)
		return resp, nil
	}

	// 清除mongo数据
	err = s.mDao.DelFansInfo(req.GetFansUid(), req.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "LeaveFansGroup DelFansInfo failed req:%v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	// 缓存
	err = s.cacheClient.DelFansInfo(req.GetAnchorUid(), req.GetFansUid())
	if err != nil {
		// 重试
		err = s.cacheClient.DelFansInfo(req.GetAnchorUid(), req.GetFansUid())
		log.ErrorWithCtx(ctx, "LeaveFansGroup DelFansInfo failed req:%v err:%v", req, err)
	}

	s.anchorKfkProduce.ProduceLeaveFansEvent(req.GetFansUid(), req.GetAnchorUid())

	datareport.ReportFansQuitData(req.GetFansUid(), req.GetAnchorUid(), fansInfo.GroupId)

	log.InfoWithCtx(ctx, "LeaveFansGroup end req:%v", req)
	return resp, nil
}

func (s *ChannelLiveFansServer) ResetFansGroup(ctx context.Context, req *pb.ResetFansGroupReq) (*pb.ResetFansGroupResp, error) {
	resp := &pb.ResetFansGroupResp{}

	err := s.cacheClient.DelAnchorSetGroupNameTs(req.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "ResetFansGroup DelAnchorSetGroupNameTs failed req:%v err:%v", req, err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "ResetFansGroup end req:%v resp:%v", req, resp)
	return resp, nil
}

func (s *ChannelLiveFansServer) SendFansGiftCheck(ctx context.Context, req *pb.SendFansGiftCheckReq) (*pb.SendFansGiftCheckResp, error) {
	resp := &pb.SendFansGiftCheckResp{}

	fansInfo, err := s.fansMgr.GetFansInfo(ctx, req.GetAnchorUid(), req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "SendFansGiftCheck GetFansInfo failed req:%v err:%v", req, err)
		return resp, err
	}

	if fansInfo.GroupId == NoValidGroupId {
		log.ErrorWithCtx(ctx, "SendFansGiftCheck no fans req:%v fansInfo:%v", req, fansInfo)
		return resp, protocol.NewExactServerError(nil, status.ErrUserPresentUnableSendUserPresent, "“加入粉丝团解锁专属礼物哦")
	}

	priResp, err := s.fansMgr.GetUserFansGiftPri(ctx, &pb.GetUserFansGiftPriReq{
		Uid:       req.GetUid(),
		AnchorUid: req.GetAnchorUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendFansGiftCheck GetUserFansGiftPri failed req:%v err:%v", req, err)
		return resp, err
	}

	nowTm := time.Now()
	fansLevel, _, _, _ := conf.GetFansLevelInfo(fansInfo.LoveValue)

	if priResp.GetMapGiftidTs()[req.GetGiftId()] < uint32(nowTm.Unix()) && fansLevel < req.GetGiftFansLv() {
		log.ErrorWithCtx(ctx, "SendFansGiftCheck send limit req:%v fansInfo:%v priTs:%d", req, fansInfo, priResp.GetMapGiftidTs()[req.GetGiftId()])
		return resp, protocol.NewExactServerError(nil, status.ErrUserPresentUnableSendUserPresent, "做任务升级粉丝团等级解锁该礼物哦")
	}

	log.DebugWithCtx(ctx, "SendFansGiftCheck end req:%v resp:%v", req, resp)
	return resp, nil
}

func (s *ChannelLiveFansServer) GrantFansGiftPrivilege(ctx context.Context, req *pb.GrantFansGiftPrivilegeReq) (*pb.GrantFansGiftPrivilegeResp, error) {
	return s.fansMgr.GrantFansGiftPrivilege(ctx, req)
}

func (s *ChannelLiveFansServer) GetUserFansGiftPri(ctx context.Context, req *pb.GetUserFansGiftPriReq) (*pb.GetUserFansGiftPriResp, error) {
	return s.fansMgr.GetUserFansGiftPri(ctx, req)
}
