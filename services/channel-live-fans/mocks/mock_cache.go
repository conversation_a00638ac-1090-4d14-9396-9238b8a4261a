// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-live-fans/cache (interfaces: IChannelLiveFansCache)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	data "golang.52tt.com/services/channel-live-fans/data"
	mongo "golang.52tt.com/services/channel-live-fans/mongo"
)

// MockIChannelLiveFansCache is a mock of IChannelLiveFansCache interface.
type MockIChannelLiveFansCache struct {
	ctrl     *gomock.Controller
	recorder *MockIChannelLiveFansCacheMockRecorder
}

// MockIChannelLiveFansCacheMockRecorder is the mock recorder for MockIChannelLiveFansCache.
type MockIChannelLiveFansCacheMockRecorder struct {
	mock *MockIChannelLiveFansCache
}

// NewMockIChannelLiveFansCache creates a new mock instance.
func NewMockIChannelLiveFansCache(ctrl *gomock.Controller) *MockIChannelLiveFansCache {
	mock := &MockIChannelLiveFansCache{ctrl: ctrl}
	mock.recorder = &MockIChannelLiveFansCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIChannelLiveFansCache) EXPECT() *MockIChannelLiveFansCacheMockRecorder {
	return m.recorder
}

// AddNeedPushAnchor mocks base method.
func (m *MockIChannelLiveFansCache) AddNeedPushAnchor(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddNeedPushAnchor", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddNeedPushAnchor indicates an expected call of AddNeedPushAnchor.
func (mr *MockIChannelLiveFansCacheMockRecorder) AddNeedPushAnchor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNeedPushAnchor", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).AddNeedPushAnchor), arg0, arg1)
}

// AnchorSetGroupNameTsKey mocks base method.
func (m *MockIChannelLiveFansCache) AnchorSetGroupNameTsKey(arg0 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnchorSetGroupNameTsKey", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// AnchorSetGroupNameTsKey indicates an expected call of AnchorSetGroupNameTsKey.
func (mr *MockIChannelLiveFansCacheMockRecorder) AnchorSetGroupNameTsKey(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorSetGroupNameTsKey", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).AnchorSetGroupNameTsKey), arg0)
}

// BatDelAnchorGroupInfo mocks base method.
func (m *MockIChannelLiveFansCache) BatDelAnchorGroupInfo(arg0 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatDelAnchorGroupInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatDelAnchorGroupInfo indicates an expected call of BatDelAnchorGroupInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatDelAnchorGroupInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelAnchorGroupInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatDelAnchorGroupInfo), arg0)
}

// BatDelAnchorPlateInfo mocks base method.
func (m *MockIChannelLiveFansCache) BatDelAnchorPlateInfo(arg0 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatDelAnchorPlateInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatDelAnchorPlateInfo indicates an expected call of BatDelAnchorPlateInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatDelAnchorPlateInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelAnchorPlateInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatDelAnchorPlateInfo), arg0)
}

// BatDelGroupNameVerifyInfo mocks base method.
func (m *MockIChannelLiveFansCache) BatDelGroupNameVerifyInfo(arg0 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatDelGroupNameVerifyInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatDelGroupNameVerifyInfo indicates an expected call of BatDelGroupNameVerifyInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatDelGroupNameVerifyInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelGroupNameVerifyInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatDelGroupNameVerifyInfo), arg0)
}

// BatSetUserFansGiftPrivilege mocks base method.
func (m *MockIChannelLiveFansCache) BatSetUserFansGiftPrivilege(arg0, arg1 uint32, arg2 []mongo.FansGiftPrivilege) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatSetUserFansGiftPrivilege", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatSetUserFansGiftPrivilege indicates an expected call of BatSetUserFansGiftPrivilege.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatSetUserFansGiftPrivilege(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatSetUserFansGiftPrivilege", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatSetUserFansGiftPrivilege), arg0, arg1, arg2)
}

// BatchAddFansAddedGroup mocks base method.
func (m *MockIChannelLiveFansCache) BatchAddFansAddedGroup(arg0 uint32, arg1 map[uint32]uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddFansAddedGroup", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchAddFansAddedGroup indicates an expected call of BatchAddFansAddedGroup.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatchAddFansAddedGroup(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddFansAddedGroup", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatchAddFansAddedGroup), arg0, arg1)
}

// BatchGetAnchorGroupInfo mocks base method.
func (m *MockIChannelLiveFansCache) BatchGetAnchorGroupInfo(arg0 []uint32) (map[uint32]mongo.FansGroupInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorGroupInfo", arg0)
	ret0, _ := ret[0].(map[uint32]mongo.FansGroupInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAnchorGroupInfo indicates an expected call of BatchGetAnchorGroupInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatchGetAnchorGroupInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorGroupInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatchGetAnchorGroupInfo), arg0)
}

// BatchGetAnchorPlateInfo mocks base method.
func (m *MockIChannelLiveFansCache) BatchGetAnchorPlateInfo(arg0 []uint32) (map[uint32]mongo.AnchorPlateInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorPlateInfo", arg0)
	ret0, _ := ret[0].(map[uint32]mongo.AnchorPlateInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAnchorPlateInfo indicates an expected call of BatchGetAnchorPlateInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatchGetAnchorPlateInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorPlateInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatchGetAnchorPlateInfo), arg0)
}

// BatchGetFansInfo mocks base method.
func (m *MockIChannelLiveFansCache) BatchGetFansInfo(arg0 uint32, arg1 []uint32) (map[uint32]mongo.FansGroupMemberInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetFansInfo", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]mongo.FansGroupMemberInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetFansInfo indicates an expected call of BatchGetFansInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatchGetFansInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetFansInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatchGetFansInfo), arg0, arg1)
}

// BatchGetFansMissionStatusByAnchors mocks base method.
func (m *MockIChannelLiveFansCache) BatchGetFansMissionStatusByAnchors(arg0 uint32, arg1 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetFansMissionStatusByAnchors", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetFansMissionStatusByAnchors indicates an expected call of BatchGetFansMissionStatusByAnchors.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatchGetFansMissionStatusByAnchors(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetFansMissionStatusByAnchors", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatchGetFansMissionStatusByAnchors), arg0, arg1)
}

// BatchGetFansMissionStatusByFans mocks base method.
func (m *MockIChannelLiveFansCache) BatchGetFansMissionStatusByFans(arg0 uint32, arg1 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetFansMissionStatusByFans", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetFansMissionStatusByFans indicates an expected call of BatchGetFansMissionStatusByFans.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatchGetFansMissionStatusByFans(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetFansMissionStatusByFans", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatchGetFansMissionStatusByFans), arg0, arg1)
}

// BatchGetPlateConf mocks base method.
func (m *MockIChannelLiveFansCache) BatchGetPlateConf(arg0 []uint32) (map[uint32]mongo.PlateConfigInfos, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPlateConf", arg0)
	ret0, _ := ret[0].(map[uint32]mongo.PlateConfigInfos)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPlateConf indicates an expected call of BatchGetPlateConf.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatchGetPlateConf(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPlateConf", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatchGetPlateConf), arg0)
}

// BatchSetAnchorGroupInfo mocks base method.
func (m *MockIChannelLiveFansCache) BatchSetAnchorGroupInfo(arg0 map[uint32]mongo.FansGroupInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetAnchorGroupInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSetAnchorGroupInfo indicates an expected call of BatchSetAnchorGroupInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatchSetAnchorGroupInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetAnchorGroupInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatchSetAnchorGroupInfo), arg0)
}

// BatchSetAnchorPlateInfo mocks base method.
func (m *MockIChannelLiveFansCache) BatchSetAnchorPlateInfo(arg0 map[uint32]mongo.AnchorPlateInfo, arg1 map[uint32]uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetAnchorPlateInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSetAnchorPlateInfo indicates an expected call of BatchSetAnchorPlateInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatchSetAnchorPlateInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetAnchorPlateInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatchSetAnchorPlateInfo), arg0, arg1)
}

// BatchSetFansInfo mocks base method.
func (m *MockIChannelLiveFansCache) BatchSetFansInfo(arg0 uint32, arg1 map[uint32]mongo.FansGroupMemberInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetFansInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSetFansInfo indicates an expected call of BatchSetFansInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatchSetFansInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetFansInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatchSetFansInfo), arg0, arg1)
}

// BatchSetPlateConf mocks base method.
func (m *MockIChannelLiveFansCache) BatchSetPlateConf(arg0 map[uint32]mongo.PlateConfigInfos) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetPlateConf", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSetPlateConf indicates an expected call of BatchSetPlateConf.
func (mr *MockIChannelLiveFansCacheMockRecorder) BatchSetPlateConf(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetPlateConf", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).BatchSetPlateConf), arg0)
}

// CheckAnchorIsInitGroupName mocks base method.
func (m *MockIChannelLiveFansCache) CheckAnchorIsInitGroupName(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAnchorIsInitGroupName", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckAnchorIsInitGroupName indicates an expected call of CheckAnchorIsInitGroupName.
func (mr *MockIChannelLiveFansCacheMockRecorder) CheckAnchorIsInitGroupName(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAnchorIsInitGroupName", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).CheckAnchorIsInitGroupName), arg0)
}

// CheckGroupNameIsVerify mocks base method.
func (m *MockIChannelLiveFansCache) CheckGroupNameIsVerify(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckGroupNameIsVerify", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckGroupNameIsVerify indicates an expected call of CheckGroupNameIsVerify.
func (mr *MockIChannelLiveFansCacheMockRecorder) CheckGroupNameIsVerify(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckGroupNameIsVerify", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).CheckGroupNameIsVerify), arg0)
}

// CheckIsFirstAddPopularity mocks base method.
func (m *MockIChannelLiveFansCache) CheckIsFirstAddPopularity(arg0, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIsFirstAddPopularity", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIsFirstAddPopularity indicates an expected call of CheckIsFirstAddPopularity.
func (mr *MockIChannelLiveFansCacheMockRecorder) CheckIsFirstAddPopularity(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsFirstAddPopularity", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).CheckIsFirstAddPopularity), arg0, arg1)
}

// CheckOrderIsExist mocks base method.
func (m *MockIChannelLiveFansCache) CheckOrderIsExist(arg0 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckOrderIsExist", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckOrderIsExist indicates an expected call of CheckOrderIsExist.
func (mr *MockIChannelLiveFansCacheMockRecorder) CheckOrderIsExist(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckOrderIsExist", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).CheckOrderIsExist), arg0)
}

// DelAnchorGroupInfo mocks base method.
func (m *MockIChannelLiveFansCache) DelAnchorGroupInfo(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelAnchorGroupInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelAnchorGroupInfo indicates an expected call of DelAnchorGroupInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) DelAnchorGroupInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAnchorGroupInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).DelAnchorGroupInfo), arg0)
}

// DelAnchorLiveByTs mocks base method.
func (m *MockIChannelLiveFansCache) DelAnchorLiveByTs(arg0 uint32, arg1 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelAnchorLiveByTs", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelAnchorLiveByTs indicates an expected call of DelAnchorLiveByTs.
func (mr *MockIChannelLiveFansCacheMockRecorder) DelAnchorLiveByTs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAnchorLiveByTs", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).DelAnchorLiveByTs), arg0, arg1)
}

// DelAnchorPlateInfo mocks base method.
func (m *MockIChannelLiveFansCache) DelAnchorPlateInfo(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelAnchorPlateInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelAnchorPlateInfo indicates an expected call of DelAnchorPlateInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) DelAnchorPlateInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAnchorPlateInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).DelAnchorPlateInfo), arg0)
}

// DelAnchorSetGroupNameTs mocks base method.
func (m *MockIChannelLiveFansCache) DelAnchorSetGroupNameTs(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelAnchorSetGroupNameTs", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelAnchorSetGroupNameTs indicates an expected call of DelAnchorSetGroupNameTs.
func (mr *MockIChannelLiveFansCacheMockRecorder) DelAnchorSetGroupNameTs(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAnchorSetGroupNameTs", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).DelAnchorSetGroupNameTs), arg0)
}

// DelFansAddedGroupList mocks base method.
func (m *MockIChannelLiveFansCache) DelFansAddedGroupList(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelFansAddedGroupList", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelFansAddedGroupList indicates an expected call of DelFansAddedGroupList.
func (mr *MockIChannelLiveFansCacheMockRecorder) DelFansAddedGroupList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelFansAddedGroupList", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).DelFansAddedGroupList), arg0)
}

// DelFansInfo mocks base method.
func (m *MockIChannelLiveFansCache) DelFansInfo(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelFansInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelFansInfo indicates an expected call of DelFansInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) DelFansInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelFansInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).DelFansInfo), arg0, arg1)
}

// DelGroupNameVerifyInfo mocks base method.
func (m *MockIChannelLiveFansCache) DelGroupNameVerifyInfo(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelGroupNameVerifyInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelGroupNameVerifyInfo indicates an expected call of DelGroupNameVerifyInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) DelGroupNameVerifyInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelGroupNameVerifyInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).DelGroupNameVerifyInfo), arg0)
}

// DelNeedPushAnchor mocks base method.
func (m *MockIChannelLiveFansCache) DelNeedPushAnchor(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelNeedPushAnchor", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelNeedPushAnchor indicates an expected call of DelNeedPushAnchor.
func (mr *MockIChannelLiveFansCacheMockRecorder) DelNeedPushAnchor(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelNeedPushAnchor", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).DelNeedPushAnchor), arg0)
}

// DelPlateConf mocks base method.
func (m *MockIChannelLiveFansCache) DelPlateConf(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPlateConf", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelPlateConf indicates an expected call of DelPlateConf.
func (mr *MockIChannelLiveFansCacheMockRecorder) DelPlateConf(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPlateConf", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).DelPlateConf), arg0)
}

// DelPushFlag mocks base method.
func (m *MockIChannelLiveFansCache) DelPushFlag(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPushFlag", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelPushFlag indicates an expected call of DelPushFlag.
func (mr *MockIChannelLiveFansCacheMockRecorder) DelPushFlag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPushFlag", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).DelPushFlag), arg0, arg1)
}

// DelUserFansGiftPrivilege mocks base method.
func (m *MockIChannelLiveFansCache) DelUserFansGiftPrivilege(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserFansGiftPrivilege", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserFansGiftPrivilege indicates an expected call of DelUserFansGiftPrivilege.
func (mr *MockIChannelLiveFansCacheMockRecorder) DelUserFansGiftPrivilege(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserFansGiftPrivilege", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).DelUserFansGiftPrivilege), arg0, arg1)
}

// GetAnchorFansKey mocks base method.
func (m *MockIChannelLiveFansCache) GetAnchorFansKey(arg0, arg1 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorFansKey", arg0, arg1)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetAnchorFansKey indicates an expected call of GetAnchorFansKey.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetAnchorFansKey(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorFansKey", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetAnchorFansKey), arg0, arg1)
}

// GetAnchorGroupInfo mocks base method.
func (m *MockIChannelLiveFansCache) GetAnchorGroupInfo(arg0 uint32) (mongo.FansGroupInfo, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorGroupInfo", arg0)
	ret0, _ := ret[0].(mongo.FansGroupInfo)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAnchorGroupInfo indicates an expected call of GetAnchorGroupInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetAnchorGroupInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorGroupInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetAnchorGroupInfo), arg0)
}

// GetAnchorGroupNameKey mocks base method.
func (m *MockIChannelLiveFansCache) GetAnchorGroupNameKey(arg0 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorGroupNameKey", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetAnchorGroupNameKey indicates an expected call of GetAnchorGroupNameKey.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetAnchorGroupNameKey(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorGroupNameKey", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetAnchorGroupNameKey), arg0)
}

// GetAnchorInitGroupNameKey mocks base method.
func (m *MockIChannelLiveFansCache) GetAnchorInitGroupNameKey(arg0 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorInitGroupNameKey", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetAnchorInitGroupNameKey indicates an expected call of GetAnchorInitGroupNameKey.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetAnchorInitGroupNameKey(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorInitGroupNameKey", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetAnchorInitGroupNameKey), arg0)
}

// GetAnchorLiveInfo mocks base method.
func (m *MockIChannelLiveFansCache) GetAnchorLiveInfo(arg0 uint32) (*data.LiveInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorLiveInfo", arg0)
	ret0, _ := ret[0].(*data.LiveInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorLiveInfo indicates an expected call of GetAnchorLiveInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetAnchorLiveInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorLiveInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetAnchorLiveInfo), arg0)
}

// GetAnchorLiveKey mocks base method.
func (m *MockIChannelLiveFansCache) GetAnchorLiveKey(arg0 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorLiveKey", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetAnchorLiveKey indicates an expected call of GetAnchorLiveKey.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetAnchorLiveKey(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorLiveKey", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetAnchorLiveKey), arg0)
}

// GetAnchorLiveTsList mocks base method.
func (m *MockIChannelLiveFansCache) GetAnchorLiveTsList(arg0 uint32, arg1, arg2, arg3 int64) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorLiveTsList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorLiveTsList indicates an expected call of GetAnchorLiveTsList.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetAnchorLiveTsList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorLiveTsList", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetAnchorLiveTsList), arg0, arg1, arg2, arg3)
}

// GetAnchorPlateInfo mocks base method.
func (m *MockIChannelLiveFansCache) GetAnchorPlateInfo(arg0 uint32) (mongo.AnchorPlateInfo, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorPlateInfo", arg0)
	ret0, _ := ret[0].(mongo.AnchorPlateInfo)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAnchorPlateInfo indicates an expected call of GetAnchorPlateInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetAnchorPlateInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorPlateInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetAnchorPlateInfo), arg0)
}

// GetAnchorPopularityKey mocks base method.
func (m *MockIChannelLiveFansCache) GetAnchorPopularityKey(arg0, arg1 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorPopularityKey", arg0, arg1)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetAnchorPopularityKey indicates an expected call of GetAnchorPopularityKey.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetAnchorPopularityKey(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorPopularityKey", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetAnchorPopularityKey), arg0, arg1)
}

// GetAnchorSetGroupNameCnt mocks base method.
func (m *MockIChannelLiveFansCache) GetAnchorSetGroupNameCnt(arg0 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorSetGroupNameCnt", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorSetGroupNameCnt indicates an expected call of GetAnchorSetGroupNameCnt.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetAnchorSetGroupNameCnt(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorSetGroupNameCnt", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetAnchorSetGroupNameCnt), arg0)
}

// GetAnchorSetGroupNameKey mocks base method.
func (m *MockIChannelLiveFansCache) GetAnchorSetGroupNameKey(arg0 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorSetGroupNameKey", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetAnchorSetGroupNameKey indicates an expected call of GetAnchorSetGroupNameKey.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetAnchorSetGroupNameKey(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorSetGroupNameKey", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetAnchorSetGroupNameKey), arg0)
}

// GetAnchorSetGroupNameTs mocks base method.
func (m *MockIChannelLiveFansCache) GetAnchorSetGroupNameTs(arg0 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorSetGroupNameTs", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorSetGroupNameTs indicates an expected call of GetAnchorSetGroupNameTs.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetAnchorSetGroupNameTs(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorSetGroupNameTs", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetAnchorSetGroupNameTs), arg0)
}

// GetFansAddedGroupKey mocks base method.
func (m *MockIChannelLiveFansCache) GetFansAddedGroupKey(arg0 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFansAddedGroupKey", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetFansAddedGroupKey indicates an expected call of GetFansAddedGroupKey.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetFansAddedGroupKey(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFansAddedGroupKey", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetFansAddedGroupKey), arg0)
}

// GetFansAddedGroupList mocks base method.
func (m *MockIChannelLiveFansCache) GetFansAddedGroupList(arg0, arg1, arg2 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFansAddedGroupList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFansAddedGroupList indicates an expected call of GetFansAddedGroupList.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetFansAddedGroupList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFansAddedGroupList", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetFansAddedGroupList), arg0, arg1, arg2)
}

// GetFansAddedGroupNum mocks base method.
func (m *MockIChannelLiveFansCache) GetFansAddedGroupNum(arg0 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFansAddedGroupNum", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFansAddedGroupNum indicates an expected call of GetFansAddedGroupNum.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetFansAddedGroupNum(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFansAddedGroupNum", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetFansAddedGroupNum), arg0)
}

// GetFansInfo mocks base method.
func (m *MockIChannelLiveFansCache) GetFansInfo(arg0, arg1 uint32) (mongo.FansGroupMemberInfo, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFansInfo", arg0, arg1)
	ret0, _ := ret[0].(mongo.FansGroupMemberInfo)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetFansInfo indicates an expected call of GetFansInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetFansInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFansInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetFansInfo), arg0, arg1)
}

// GetFansMissionKey mocks base method.
func (m *MockIChannelLiveFansCache) GetFansMissionKey(arg0, arg1 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFansMissionKey", arg0, arg1)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetFansMissionKey indicates an expected call of GetFansMissionKey.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetFansMissionKey(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFansMissionKey", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetFansMissionKey), arg0, arg1)
}

// GetFansMissionStatus mocks base method.
func (m *MockIChannelLiveFansCache) GetFansMissionStatus(arg0, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFansMissionStatus", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFansMissionStatus indicates an expected call of GetFansMissionStatus.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetFansMissionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFansMissionStatus", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetFansMissionStatus), arg0, arg1)
}

// GetGroupNameVerifyKey mocks base method.
func (m *MockIChannelLiveFansCache) GetGroupNameVerifyKey(arg0 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupNameVerifyKey", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetGroupNameVerifyKey indicates an expected call of GetGroupNameVerifyKey.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetGroupNameVerifyKey(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupNameVerifyKey", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetGroupNameVerifyKey), arg0)
}

// GetNeedPushAnchorList mocks base method.
func (m *MockIChannelLiveFansCache) GetNeedPushAnchorList(arg0, arg1 int64) (map[uint32]uint32, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNeedPushAnchorList", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetNeedPushAnchorList indicates an expected call of GetNeedPushAnchorList.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetNeedPushAnchorList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNeedPushAnchorList", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetNeedPushAnchorList), arg0, arg1)
}

// GetPlateConf mocks base method.
func (m *MockIChannelLiveFansCache) GetPlateConf(arg0 uint32) (mongo.PlateConfigInfos, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlateConf", arg0)
	ret0, _ := ret[0].(mongo.PlateConfigInfos)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPlateConf indicates an expected call of GetPlateConf.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetPlateConf(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlateConf", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetPlateConf), arg0)
}

// GetPushFlag mocks base method.
func (m *MockIChannelLiveFansCache) GetPushFlag(arg0, arg1 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPushFlag", arg0, arg1)
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetPushFlag indicates an expected call of GetPushFlag.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetPushFlag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPushFlag", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetPushFlag), arg0, arg1)
}

// GetUserFansGiftPrivilege mocks base method.
func (m *MockIChannelLiveFansCache) GetUserFansGiftPrivilege(arg0, arg1 uint32) ([]mongo.FansGiftPrivilege, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserFansGiftPrivilege", arg0, arg1)
	ret0, _ := ret[0].([]mongo.FansGiftPrivilege)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserFansGiftPrivilege indicates an expected call of GetUserFansGiftPrivilege.
func (mr *MockIChannelLiveFansCacheMockRecorder) GetUserFansGiftPrivilege(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserFansGiftPrivilege", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).GetUserFansGiftPrivilege), arg0, arg1)
}

// IncrAnchorSetGroupNameCnt mocks base method.
func (m *MockIChannelLiveFansCache) IncrAnchorSetGroupNameCnt(arg0 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrAnchorSetGroupNameCnt", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrAnchorSetGroupNameCnt indicates an expected call of IncrAnchorSetGroupNameCnt.
func (mr *MockIChannelLiveFansCacheMockRecorder) IncrAnchorSetGroupNameCnt(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrAnchorSetGroupNameCnt", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).IncrAnchorSetGroupNameCnt), arg0)
}

// RecordAnchorLiveTs mocks base method.
func (m *MockIChannelLiveFansCache) RecordAnchorLiveTs(arg0 uint32, arg1 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordAnchorLiveTs", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordAnchorLiveTs indicates an expected call of RecordAnchorLiveTs.
func (mr *MockIChannelLiveFansCacheMockRecorder) RecordAnchorLiveTs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordAnchorLiveTs", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).RecordAnchorLiveTs), arg0, arg1)
}

// SetAnchorGroupInfo mocks base method.
func (m *MockIChannelLiveFansCache) SetAnchorGroupInfo(arg0 uint32, arg1 mongo.FansGroupInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAnchorGroupInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetAnchorGroupInfo indicates an expected call of SetAnchorGroupInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) SetAnchorGroupInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAnchorGroupInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).SetAnchorGroupInfo), arg0, arg1)
}

// SetAnchorLiveInfo mocks base method.
func (m *MockIChannelLiveFansCache) SetAnchorLiveInfo(arg0 uint32, arg1 *data.LiveInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAnchorLiveInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetAnchorLiveInfo indicates an expected call of SetAnchorLiveInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) SetAnchorLiveInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAnchorLiveInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).SetAnchorLiveInfo), arg0, arg1)
}

// SetAnchorPlateInfo mocks base method.
func (m *MockIChannelLiveFansCache) SetAnchorPlateInfo(arg0, arg1 uint32, arg2 mongo.AnchorPlateInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAnchorPlateInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetAnchorPlateInfo indicates an expected call of SetAnchorPlateInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) SetAnchorPlateInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAnchorPlateInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).SetAnchorPlateInfo), arg0, arg1, arg2)
}

// SetAnchorSetGroupNameTs mocks base method.
func (m *MockIChannelLiveFansCache) SetAnchorSetGroupNameTs(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAnchorSetGroupNameTs", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetAnchorSetGroupNameTs indicates an expected call of SetAnchorSetGroupNameTs.
func (mr *MockIChannelLiveFansCacheMockRecorder) SetAnchorSetGroupNameTs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAnchorSetGroupNameTs", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).SetAnchorSetGroupNameTs), arg0, arg1)
}

// SetFansInfo mocks base method.
func (m *MockIChannelLiveFansCache) SetFansInfo(arg0, arg1 uint32, arg2 mongo.FansGroupMemberInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFansInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetFansInfo indicates an expected call of SetFansInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) SetFansInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFansInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).SetFansInfo), arg0, arg1, arg2)
}

// SetFansMissionStatus mocks base method.
func (m *MockIChannelLiveFansCache) SetFansMissionStatus(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFansMissionStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetFansMissionStatus indicates an expected call of SetFansMissionStatus.
func (mr *MockIChannelLiveFansCacheMockRecorder) SetFansMissionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFansMissionStatus", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).SetFansMissionStatus), arg0, arg1)
}

// SetGroupNameInitInfo mocks base method.
func (m *MockIChannelLiveFansCache) SetGroupNameInitInfo(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGroupNameInitInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetGroupNameInitInfo indicates an expected call of SetGroupNameInitInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) SetGroupNameInitInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGroupNameInitInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).SetGroupNameInitInfo), arg0)
}

// SetGroupNameVerifyInfo mocks base method.
func (m *MockIChannelLiveFansCache) SetGroupNameVerifyInfo(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGroupNameVerifyInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetGroupNameVerifyInfo indicates an expected call of SetGroupNameVerifyInfo.
func (mr *MockIChannelLiveFansCacheMockRecorder) SetGroupNameVerifyInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGroupNameVerifyInfo", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).SetGroupNameVerifyInfo), arg0)
}

// SetPlateConf mocks base method.
func (m *MockIChannelLiveFansCache) SetPlateConf(arg0 uint32, arg1 mongo.PlateConfigInfos) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPlateConf", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPlateConf indicates an expected call of SetPlateConf.
func (mr *MockIChannelLiveFansCacheMockRecorder) SetPlateConf(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPlateConf", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).SetPlateConf), arg0, arg1)
}

// SetPushFlag mocks base method.
func (m *MockIChannelLiveFansCache) SetPushFlag(arg0, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPushFlag", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPushFlag indicates an expected call of SetPushFlag.
func (mr *MockIChannelLiveFansCacheMockRecorder) SetPushFlag(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPushFlag", reflect.TypeOf((*MockIChannelLiveFansCache)(nil).SetPushFlag), arg0, arg1, arg2)
}
