package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	event_link_wrap "golang.52tt.com/pkg/event-link-wrap"
	"golang.52tt.com/pkg/log"
	mempb "golang.52tt.com/protocol/services/knightgroupmembers"
	"time"
)

const (
	topicTypeKnight = "knight-group-kfk"
)

type KnightSubscriber struct {
	KafkaSub *event_link_wrap.SEventLinkAsyncSub
	handler  func(*mempb.JoinKnightGroupEvent)
}

func NewKnightSubscriberSubscriber(clientId, groupId string, topics, brokers []string, handler func(*mempb.JoinKnightGroupEvent)) (*KnightSubscriber, error) {

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	sub := &KnightSubscriber{
		handler: handler,
	}
	kafkaSub, err := event_link_wrap.NewEventLinkAsyncSub(brokers, topics, groupId, clientId,
		32, sub.handlerEvent, event_link_wrap.WithMaxRetryTimes(5))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewKnightSubscriberSubscriber Failed to create kafka-subscriber kfk conf:(%v, %v, %v, %v) err:%v ", brokers, topics, groupId, clientId, err)
		return nil, err
	}
	sub.KafkaSub = kafkaSub
	sub.KafkaSub.Start()
	log.InfoWithCtx(ctx, "NewKnightSubscriberSubscriber success... kfk conf:(%v, %v, %v, %v)", brokers, topics, groupId, clientId)
	return sub, nil
}

func (s *KnightSubscriber) Close() {
	s.KafkaSub.Stop()
}

func (s *KnightSubscriber) handlerEvent(msg *subscriber.ConsumerMessage) (error, bool) {
	switch msg.Topic {
	case topicTypeKnight:
		return s.handlerKnightEvent(msg)
	default:

	}
	return nil, false
}

func (s *KnightSubscriber) handlerKnightEvent(msg *subscriber.ConsumerMessage) (error, bool) {
	joinGroupEvent := &mempb.JoinKnightGroupEvent{}
	err := proto.Unmarshal(msg.Value, joinGroupEvent)
	if nil != err {
		log.Errorf("handlerKnightEvent Unmarshal err:%v", err)
		return nil, false
	}
	s.handler(joinGroupEvent)
	return nil, false
}
