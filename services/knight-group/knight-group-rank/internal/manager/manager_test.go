package manager

import (
	"context"
	"fmt"
	"github.com/alicebob/miniredis"
	"github.com/go-redis/redis"
	"github.com/golang/mock/gomock"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	accountMockCli "golang.52tt.com/clients/mocks/account"
	"golang.52tt.com/clients/mocks/channel"
	channelmsgexpress "golang.52tt.com/clients/mocks/channel-msg-express"
	knightgroupmembers "golang.52tt.com/clients/mocks/knight-group-members"
	nobilityMockCli "golang.52tt.com/clients/mocks/nobility"
	mockYouKnowWho "golang.52tt.com/clients/mocks/you-know-who"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/protocol"
	channelsvr "golang.52tt.com/protocol/services/channelsvr"
	mempb "golang.52tt.com/protocol/services/knightgroupmembers"
	pb "golang.52tt.com/protocol/services/knightgrouprank"
	kfkenter "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkachannalevent"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkanobility"
	kfkPB "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	Nobility "golang.52tt.com/protocol/services/nobilitysvr"
	ykwpb "golang.52tt.com/protocol/services/youknowwho"
	"golang.52tt.com/services/knight-group/knight-group-logic/client"
	dyconf "golang.52tt.com/services/knight-group/knight-group-logic/common/dyconf"
	"golang.52tt.com/services/knight-group/knight-group-rank/internal/conf"
	event2 "golang.52tt.com/services/knight-group/knight-group-rank/internal/event"
	"math/rand"
	"reflect"
	"strconv"
	"testing"
	"time"
)

var miniRedisClient *redis.Client

func init() {
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}

	// 连接mock的redis server
	miniRedisClient = redis.NewClient(&redis.Options{
		Addr: s.Addr(), // mock redis server的地址
	})
}

func TestManager_AddInRoomCnt(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	mockYKWCli := mockYouKnowWho.NewMockIClient(ctl)
	mockNbiCli := nobilityMockCli.NewMockIClient(ctl)
	clientX.YKWCli, clientX.NobilityCli = mockYKWCli, mockNbiCli

	var channelId, knightUid uint32 = 1024, 1024
	gomock.InOrder(
		mockYKWCli.EXPECT().GetUKWInfo(ctx, knightUid).Return(&ykwpb.UKWInfo{
			Uid:               knightUid,
			UkwPermissionInfo: nil,
			UkwPersonInfo:     nil,
		}, nil),
		mockNbiCli.EXPECT().GetNobilityInfo(ctx, knightUid, false).Return(&Nobility.NobilityInfo{
			Value:                0,
			KeepValue:            0,
			Level:                0,
			CycleTs:              0,
			Uid:                  0,
			Invisible:            false,
			WaitCostValue:        0,
			TotalWaitCostValue:   0,
			LevelName:            "",
			InvestInfoList:       nil,
			TempNobilityRemainTs: 0,
			RealLevel:            0,
		}, nil),
	)

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx             context.Context
		channelId       uint32
		knightUid       uint32
		isEnter         bool
		isTempInVisible bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_AddInRoomCnt",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:             ctx,
				channelId:       channelId,
				knightUid:       knightUid,
				isEnter:         false,
				isTempInVisible: false,
			},
			wantErr: false,
		},
		{
			name: "TestManager_AddInRoomCnt_v2",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:             ctx,
				channelId:       channelId,
				knightUid:       knightUid,
				isEnter:         true,
				isTempInVisible: false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			if err := m.AddInRoomCnt(tt.args.ctx, tt.args.channelId, tt.args.knightUid, tt.args.isEnter, tt.args.isTempInVisible); (err != nil) != tt.wantErr {
				t.Errorf("AddInRoomCnt() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_AddUserOnline(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()
	var channelId, knightUid uint32 = 1024, 1024

	mockYKWCli := mockYouKnowWho.NewMockIClient(ctl)
	mockNbiCli := nobilityMockCli.NewMockIClient(ctl)
	AccountClient := accountMockCli.NewMockIClient(ctl)
	ChannelMsgExpress := channelmsgexpress.NewMockIClient(ctl)
	clientX.YKWCli, clientX.NobilityCli, clientX.AccountClient = mockYKWCli, mockNbiCli, AccountClient
	clientX.ChannelMsgExpress = ChannelMsgExpress
	clientX.ChannelClient = channel.NewMockIClient(ctl)

	/*	gomock.InOrder(
		AccountClient.EXPECT().GetUser(ctx, knightUid).Return(&account.UserResp{
			Uid:                 knightUid,
			Phone:               "1024",
			Username:            "1024",
		},nil),
		)*/

	miniRedisClient.HSet("x", "", "")

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx             context.Context
		channelId       uint32
		knightUid       uint32
		createrUid      uint32
		isEnter         bool
		isTempInVisible bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_AddUserOnline",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:             ctx,
				channelId:       channelId,
				knightUid:       knightUid,
				createrUid:      knightUid,
				isEnter:         false,
				isTempInVisible: false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			if err := m.AddUserOnline(tt.args.ctx, tt.args.channelId, tt.args.knightUid, tt.args.createrUid, tt.args.isEnter, tt.args.isTempInVisible); (err != nil) != tt.wantErr {
				t.Errorf("AddUserOnline() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_CheckIsChief(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()
	var channelId, anchorUid uint32 = 1024, 1024

	KnightMemberClient := knightgroupmembers.NewMockIClient(ctl)
	clientX.KnightMemberClient = KnightMemberClient
	gomock.InOrder(
		KnightMemberClient.EXPECT().SetFirstChief(ctx, &mempb.SetFirstChiefReq{
			ChannelId:    channelId,
			KnightUid:    0,
			AnchorUid:    anchorUid,
			ExpireTime:   getNextWeekDur(),
			ForceSendMsg: false,
		}).Return(nil, nil),
	)

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx       context.Context
		channelId uint32
		anchorUid uint32
		force     bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_CheckIsChief",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:       ctx,
				channelId: channelId,
				anchorUid: anchorUid,
				force:     false,
			},
			want:    0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			m.CheckIsChief(tt.args.ctx, tt.args.channelId, tt.args.anchorUid, tt.args.force)
			/*			if (err != nil) != tt.wantErr {
							t.Errorf("CheckIsChief() error = %v, wantErr %v", err, tt.wantErr)
							return
						}
						if got != tt.want {
							t.Errorf("CheckIsChief() got = %v, want %v", got, tt.want)
						}*/
		})
	}
}

func TestManager_GenChiefForTest(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()
	var channelId, knightUid, anchorUid uint32 = 1024, 1024, 1023
	member := &pb.MemberInfo{
		ChannelId:  channelId,
		AnchorUid:  anchorUid,
		KnightUid:  knightUid,
		BeginTime:  0,
		ExpireTime: uint32(time.Now().Unix() + 10000),
		Value:      0,
	}
	field := strconv.FormatInt(int64(knightUid), 10)
	bin, _ := proto.Marshal(member)

	miniRedisClient.HSet(getMemberHKey(channelId, anchorUid), field, bin)

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx       context.Context
		channelId uint32
		anchorUid uint32
		knightUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_GenChiefForTest",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:       ctx,
				channelId: channelId,
				anchorUid: knightUid,
				knightUid: knightUid,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			if err := m.GenChiefForTest(tt.args.ctx, tt.args.channelId, tt.args.anchorUid, tt.args.knightUid); (err != nil) != tt.wantErr {
				t.Errorf("GenChiefForTest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_GetCampInfo(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()
	var channelId, knightUid uint32 = 1024, 1024

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx       context.Context
		channelId uint32
		anchorUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.RankValueInfo
		want1   uint32
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_GetCampInfo",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:       ctx,
				channelId: channelId,
				anchorUid: knightUid,
			},
			want:    nil,
			want1:   0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			_, _, err := m.GetCampInfo(tt.args.ctx, tt.args.channelId, tt.args.anchorUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCampInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			/*			if !reflect.DeepEqual(got, tt.want) {
							t.Errorf("GetCampInfo() got = %v, want %v", got, tt.want)
						}
						if got1 != tt.want1 {
							t.Errorf("GetCampInfo() got1 = %v, want %v", got1, tt.want1)
						}*/
		})
	}
}

func TestManager_GetLoveRank(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()
	var channelId, knightUid, anchorUid, sendTime uint32 = 1024, 1024, 1024, 1024

	nowTs := time.Now()
	//m.cache.ZRevRangeWithScores(getWeekRankKey(channelId, anchorUid, uint32(sendTime)), 0, limit).Result()
	miniRedisClient.ZAdd(getWeekRankKey(channelId, anchorUid, uint32(sendTime)), redis.Z{
		Score:  10,
		Member: "1024",
	})

	/*
			hres, err := m.cache.HGetAll(getMemberHKey(channelId, anchorUid)).Result()
		if nil != err {
			log.ErrorWithCtx(ctx,"getDayRank HMGet channelId:%v anchorUid:%v err:%v", channelId, anchorUid, err)
			return userDayMap,userLeftDayMap,userBeginMap,err
		}

		for _, m := range hres {
			e := &pb.MemberInfo{}
	*/
	member := &pb.MemberInfo{
		ChannelId:  channelId,
		AnchorUid:  anchorUid,
		KnightUid:  knightUid,
		BeginTime:  0,
		ExpireTime: uint32(nowTs.Unix() + 10000),
		Value:      1,
	}
	bin, _ := proto.Marshal(member)
	miniRedisClient.HSet(getMemberHKey(channelId, anchorUid), "1024", bin)

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx       context.Context
		channelId uint32
		anchorUid uint32
		sendTime  int64
		isCache   bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.RankValueInfo
		want1   uint32
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_GetLoveRank",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:       ctx,
				channelId: channelId,
				anchorUid: knightUid,
				sendTime:  0,
				isCache:   false,
			},
			want:    nil,
			want1:   0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			m.GetLoveRank(tt.args.ctx, tt.args.channelId, tt.args.anchorUid, tt.args.sendTime, tt.args.isCache)
		})
	}
}

func TestManager_HandlerEnterEvent(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	mockYKWCli := mockYouKnowWho.NewMockIClient(ctl)
	mockNbiCli := nobilityMockCli.NewMockIClient(ctl)
	AccountClient := accountMockCli.NewMockIClient(ctl)
	ChannelMsgExpress := channelmsgexpress.NewMockIClient(ctl)
	clientX.YKWCli, clientX.NobilityCli, clientX.AccountClient = mockYKWCli, mockNbiCli, AccountClient
	clientX.ChannelMsgExpress = ChannelMsgExpress
	ChannelClient := channel.NewMockIClient(ctl)
	clientX.ChannelClient = ChannelClient
	var channelId, knightUid uint32 = 1024, 1024

	channelSimple := &channelsvr.ChannelSimpleInfo{
		CreaterUid: &knightUid,
	}
	gomock.InOrder(
		ChannelClient.EXPECT().GetChannelSimpleInfo(ctx, knightUid, channelId).Return(channelSimple, nil),
		//ChannelMsgExpress.EXPECT().SendChannelBroadcastMsg(ctx, )
	)

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx     context.Context
		event   *kfkenter.ChSimpleEvent
		isEnter bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_HandlerEnterEvent",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx: ctx,
				event: &kfkenter.ChSimpleEvent{
					Uid:         knightUid,
					ChId:        channelId,
					EventType:   0,
					ChannelType: 0,
					OptPbInfo:   nil,
				},
				isEnter: false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			if err := m.HandlerEnterEvent(tt.args.ctx, tt.args.event, tt.args.isEnter); (err != nil) != tt.wantErr {
				t.Errorf("HandlerEnterEvent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_HandlerKnightGroupEvent(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	mockYKWCli := mockYouKnowWho.NewMockIClient(ctl)
	mockNbiCli := nobilityMockCli.NewMockIClient(ctl)
	AccountClient := accountMockCli.NewMockIClient(ctl)
	ChannelMsgExpress := channelmsgexpress.NewMockIClient(ctl)
	clientX.YKWCli, clientX.NobilityCli, clientX.AccountClient = mockYKWCli, mockNbiCli, AccountClient
	clientX.ChannelMsgExpress = ChannelMsgExpress
	ChannelClient := channel.NewMockIClient(ctl)
	clientX.ChannelClient = ChannelClient

	var knightUid uint32 = 1024
	ev := &mempb.JoinKnightGroupEvent{
		OrderId:     "",
		AnchorUid:   0,
		KnightUid:   knightUid,
		ChannelId:   0,
		ChannelType: 0,
		GuildId:     0,
		Price:       0,
		CreateTime:  0,
		BeginTime:   0,
		ExpireTime:  0,
		DealToken:   "",
	}

	yresp := &ykwpb.UKWInfo{
		Uid: knightUid,
	}
	gomock.InOrder(
		mockYKWCli.EXPECT().GetUKWInfo(ctx, knightUid).Return(yresp, protocol.NewServerError(0, "x")),
		//ChannelMsgExpress.EXPECT().SendChannelBroadcastMsg(ctx, )
	)

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx   context.Context
		event *mempb.JoinKnightGroupEvent
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_HandlerKnightGroupEvent",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:   ctx,
				event: ev,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			if err := m.HandlerKnightGroupEvent(tt.args.ctx, tt.args.event); (err != nil) != tt.wantErr {
				t.Errorf("HandlerKnightGroupEvent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_HandlerNobilityEvent(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	mockYKWCli := mockYouKnowWho.NewMockIClient(ctl)
	mockNbiCli := nobilityMockCli.NewMockIClient(ctl)
	AccountClient := accountMockCli.NewMockIClient(ctl)
	ChannelMsgExpress := channelmsgexpress.NewMockIClient(ctl)
	clientX.YKWCli, clientX.NobilityCli, clientX.AccountClient = mockYKWCli, mockNbiCli, AccountClient
	clientX.ChannelMsgExpress = ChannelMsgExpress
	ChannelClient := channel.NewMockIClient(ctl)
	clientX.ChannelClient = ChannelClient

	ev := kafkanobility.NobilityInfoChange{
		Uid:       1024,
		ChannelId: 1024,
	}

	chInfo := &channelsvr.ChannelSimpleInfo{}

	gomock.InOrder(
		ChannelClient.EXPECT().GetChannelSimpleInfo(ctx, ev.Uid, ev.ChannelId).Return(chInfo, nil),
		//ChannelMsgExpress.EXPECT().SendChannelBroadcastMsg(ctx, )
	)

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx   context.Context
		event kafkanobility.NobilityInfoChange
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_HandlerNobilityEvent",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           nil,
			},
			args: args{
				ctx:   ctx,
				event: ev,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			if err := m.HandlerNobilityEvent(tt.args.ctx, tt.args.event); (err != nil) != tt.wantErr {
				t.Errorf("HandlerNobilityEvent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_HandlerPresentEvent(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	mockYKWCli := mockYouKnowWho.NewMockIClient(ctl)
	mockNbiCli := nobilityMockCli.NewMockIClient(ctl)
	AccountClient := accountMockCli.NewMockIClient(ctl)
	ChannelMsgExpress := channelmsgexpress.NewMockIClient(ctl)
	clientX.YKWCli, clientX.NobilityCli, clientX.AccountClient = mockYKWCli, mockNbiCli, AccountClient
	clientX.ChannelMsgExpress = ChannelMsgExpress
	ChannelClient := channel.NewMockIClient(ctl)
	clientX.ChannelClient = ChannelClient

	ev := &kfkPB.PresentEvent{
		Uid:         1024,
		TargetUid:   0,
		OrderId:     "",
		ChannelId:   0,
		ChannelType: 7,
		GuildId:     0,
		SendTime:    0,
		ItemId:      0,
		ItemCount:   0,
		Price:       0,
		PriceType:   0,
	}

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx   context.Context
		event *kfkPB.PresentEvent
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_HandlerPresentEvent",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:   ctx,
				event: ev,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			if err := m.HandlerPresentEvent(tt.args.ctx, tt.args.event); (err != nil) != tt.wantErr {
				t.Errorf("HandlerPresentEvent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_PushCampChannelMsg(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	mockYKWCli := mockYouKnowWho.NewMockIClient(ctl)
	mockNbiCli := nobilityMockCli.NewMockIClient(ctl)
	AccountClient := accountMockCli.NewMockIClient(ctl)
	ChannelMsgExpress := channelmsgexpress.NewMockIClient(ctl)
	clientX.YKWCli, clientX.NobilityCli, clientX.AccountClient = mockYKWCli, mockNbiCli, AccountClient
	clientX.ChannelMsgExpress = ChannelMsgExpress
	ChannelClient := channel.NewMockIClient(ctl)
	clientX.ChannelClient = ChannelClient

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx       context.Context
		channelId uint32
		anchorUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_PushCampChannelMsg",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:       ctx,
				channelId: 0,
				anchorUid: 0,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			if err := m.PushCampChannelMsg(tt.args.ctx, tt.args.channelId, tt.args.anchorUid); (err != nil) != tt.wantErr {
				t.Errorf("PushCampChannelMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_Stop(t *testing.T) {
	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			/*			m := &Manager{
						presentSub:      tt.fields.presentSub,
						knightSub:       tt.fields.knightSub,
						channelEnterSub: tt.fields.channelEnterSub,
						cache:           tt.fields.cache,
					}*/
		})
	}
}

// func TestManager_addKeepDay(t *testing.T) {
// 	ctx := context.Background()

// 	newMember := &pb.MemberInfo{
// 		ChannelId:  1024,
// 		AnchorUid:  1024,
// 		KnightUid:  1024,
// 		BeginTime:  1024,
// 		ExpireTime: 1024,
// 		Value:      1024,
// 	}

// 	type fields struct {
// 		presentSub      *event.PresentSubscriber
// 		knightSub       *event.KnightSubscriber
// 		channelEnterSub *event.ChannelEnterSubscriber
// 		cache           *redis.Client
// 	}
// 	type args struct {
// 		ctx       context.Context
// 		newMember *pb.MemberInfo
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantErr bool
// 	}{
// 		// TODO: Add test cases.
// 		{
// 			name: "TestManager_addKeepDay",
// 			fields: fields{
// 				presentSub:      nil,
// 				knightSub:       nil,
// 				channelEnterSub: nil,
// 				cache:           miniRedisClient,
// 			},
// 			args: args{
// 				ctx:       ctx,
// 				newMember: newMember,
// 			},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &Manager{
// 				presentSub:      tt.fields.presentSub,
// 				knightSub:       tt.fields.knightSub,
// 				channelEnterSub: tt.fields.channelEnterSub,
// 				cache:           tt.fields.cache,
// 			}
// 			m.addKeepDay(tt.args.ctx, tt.args.newMember)
// 		})
// 	}
// }

func TestManager_addKnightGroupMember(t *testing.T) {

	ctx := context.Background()
	members := []*pb.MemberInfo{
		{
			ChannelId:  1024,
			AnchorUid:  1024,
			KnightUid:  1024,
			BeginTime:  1024,
			ExpireTime: 1024,
			Value:      1024,
		},
	}

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx     context.Context
		members []*pb.MemberInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_addKnightGroupMember",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:     ctx,
				members: members,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			if err := m.addKnightGroupMember(tt.args.ctx, tt.args.members); (err != nil) != tt.wantErr {
				t.Errorf("addKnightGroupMember() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_addRankWeekVal(t *testing.T) {

	ctx := context.Background()
	members := []*pb.MemberInfo{
		{
			ChannelId:  1024,
			AnchorUid:  1024,
			KnightUid:  1024,
			BeginTime:  1024,
			ExpireTime: 1024,
			Value:      1024,
		},
	}

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx      context.Context
		sendTime uint32
		members  []*pb.MemberInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_addRankWeekVal",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:      ctx,
				sendTime: 10,
				members:  members,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			if err := m.addRankWeekVal(tt.args.ctx, tt.args.sendTime, tt.args.members); (err != nil) != tt.wantErr {
				t.Errorf("addRankWeekVal() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_checkCondition(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	//ctx := context.Background()

	mockYKWCli := mockYouKnowWho.NewMockIClient(ctl)
	mockNbiCli := nobilityMockCli.NewMockIClient(ctl)
	AccountClient := accountMockCli.NewMockIClient(ctl)
	ChannelMsgExpress := channelmsgexpress.NewMockIClient(ctl)
	clientX.YKWCli, clientX.NobilityCli, clientX.AccountClient = mockYKWCli, mockNbiCli, AccountClient
	clientX.ChannelMsgExpress = ChannelMsgExpress
	ChannelClient := channel.NewMockIClient(ctl)
	clientX.ChannelClient = ChannelClient

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx        context.Context
		channelId  uint32
		anchorUid  uint32
		lastWeekTs int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			got, err := m.checkCondition(tt.args.ctx, tt.args.channelId, tt.args.anchorUid, tt.args.lastWeekTs)
			if (err != nil) != tt.wantErr {
				t.Errorf("checkCondition() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("checkCondition() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_genChief(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	KnightMemberClient := knightgroupmembers.NewMockIClient(ctl)

	clientX.KnightMemberClient = KnightMemberClient
	//var channelId,anchorUid  uint32 = 1024,1024
	gomock.InOrder(
	/*		KnightMemberClient.EXPECT().SetFirstChief( ctx, &mempb.SetFirstChiefReq{
			ChannelId:            channelId,
			KnightUid:            0,
			AnchorUid: 			  anchorUid,
			ExpireTime:           getNextWeekDur(),
			ForceSendMsg: false,
		}).Return(nil,nil ),*/
	)

	channelId2AnchorUid := map[uint32]uint32{}

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx                 context.Context
		channelId2AnchorUid map[uint32]uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_genChief",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:                 ctx,
				channelId2AnchorUid: channelId2AnchorUid,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			m.genChief(tt.args.ctx, tt.args.channelId2AnchorUid)
		})
	}
	//
}

func TestManager_genWeekRank(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	KnightMemberClient := knightgroupmembers.NewMockIClient(ctl)
	var Off, Count uint32 = 0, 1024

	req := &mempb.BatchGetMemberReq{
		Index: uint32(0),
		Off:   Off,
		Count: Count,
	}
	rsp := &mempb.BatchGetMemberResp{
		KnightMemberList: []*mempb.KnightMember{{
			Uid:       0,
			ChannelId: 0,
			AnchorUid: 0,
			BeginTime: 0,
			EndTime:   0,
		}},
	}

	gomock.InOrder(
		KnightMemberClient.EXPECT().BatchGetMember(ctx, req).Return(rsp, nil),
		KnightMemberClient.EXPECT().SetFirstChief(ctx, &mempb.SetFirstChiefReq{
			ChannelId:    0,
			KnightUid:    0,
			AnchorUid:    0,
			ExpireTime:   getNextWeekDur(),
			ForceSendMsg: false,
		}).Return(nil, nil),
	)

	dyconf.InitTestData()

	nowTs := time.Now()
	for i := 1; i < 100; i++ {
		dur := time.Minute * time.Duration(5+rand.Intn(10))
		miniRedisClient.SetNX(flushRankLockKey(uint32(i), nowTs), 1, dur).Val()
	}

	clientX.KnightMemberClient = KnightMemberClient

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "TestManager_genWeekRank",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			m.genWeekRank()
		})
	}
}

func TestManager_getChief(t *testing.T) {
	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		channelId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_getChief",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args:    args{},
			want:    0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			m.getChief(tt.args.channelId)
		})
	}
}

func TestManager_getDayRank(t *testing.T) {
	ctx := context.Background()

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx       context.Context
		channelId uint32
		anchorUid uint32
		ts        int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[uint32]int64
		want1   map[uint32]int64
		want2   map[uint32]int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_getDayRank",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:       ctx,
				channelId: 0,
				anchorUid: 0,
				ts:        0,
			},
			want:    nil,
			want1:   nil,
			want2:   nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			_, _, _, err := m.getDayRank(tt.args.ctx, tt.args.channelId, tt.args.anchorUid, tt.args.ts)
			if (err != nil) != tt.wantErr {
				t.Errorf("getDayRank() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			/*			if !reflect.DeepEqual(got, tt.want) {
							t.Errorf("getDayRank() got = %v, want %v", got, tt.want)
						}
						if !reflect.DeepEqual(got1, tt.want1) {
							t.Errorf("getDayRank() got1 = %v, want %v", got1, tt.want1)
						}
						if !reflect.DeepEqual(got2, tt.want2) {
							t.Errorf("getDayRank() got2 = %v, want %v", got2, tt.want2)
						}*/
		})
	}
}

func TestManager_getKnightGropMember(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	mockYKWCli := mockYouKnowWho.NewMockIClient(ctl)
	mockNbiCli := nobilityMockCli.NewMockIClient(ctl)
	AccountClient := accountMockCli.NewMockIClient(ctl)
	ChannelMsgExpress := channelmsgexpress.NewMockIClient(ctl)
	clientX.YKWCli, clientX.NobilityCli, clientX.AccountClient = mockYKWCli, mockNbiCli, AccountClient
	clientX.ChannelMsgExpress = ChannelMsgExpress
	ChannelClient := channel.NewMockIClient(ctl)
	clientX.ChannelClient = ChannelClient

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx       context.Context
		channelId uint32
		anchorUid uint32
		knightUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.MemberInfo
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_getKnightGropMember",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:       ctx,
				channelId: 0,
				anchorUid: 0,
				knightUid: 0,
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			got, err := m.getKnightGropMember(tt.args.ctx, tt.args.channelId, tt.args.anchorUid, tt.args.knightUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("getKnightGropMember() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getKnightGropMember() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_getKnightInRoom(t *testing.T) {
	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}

	res := map[uint32]bool{1024: false}
	type args struct {
		channelId uint32
		uidList   []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[uint32]bool
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_getKnightInRoom",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				channelId: 1024,
				uidList:   []uint32{1024},
			},
			want:    res,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			got, err := m.getKnightInRoom(tt.args.channelId, tt.args.uidList)
			if (err != nil) != tt.wantErr {
				t.Errorf("getKnightInRoom() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getKnightInRoom() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_setFirstChief(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()
	KnightMemberClient := knightgroupmembers.NewMockIClient(ctl)

	clientX.KnightMemberClient = KnightMemberClient

	req := &mempb.SetFirstChiefReq{
		ChannelId:    0,
		KnightUid:    0,
		AnchorUid:    0,
		ExpireTime:   getNextWeekDur(),
		ForceSendMsg: false,
	}

	gomock.InOrder(
		KnightMemberClient.EXPECT().SetFirstChief(ctx, req).Return(nil, nil),
	)

	type fields struct {
		presentSub      *event2.PresentSubscriber
		knightSub       *event2.KnightSubscriber
		channelEnterSub *event2.ChannelEnterSubscriber
		cache           *redis.Client
	}
	type args struct {
		ctx       context.Context
		channelId uint32
		anchorUid uint32
		knightUid uint32
		force     bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestManager_setFirstChief",
			fields: fields{
				presentSub:      nil,
				knightSub:       nil,
				channelEnterSub: nil,
				cache:           miniRedisClient,
			},
			args: args{
				ctx:       ctx,
				channelId: 0,
				anchorUid: 0,
				knightUid: 0,
				force:     false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentSub:      tt.fields.presentSub,
				knightSub:       tt.fields.knightSub,
				channelEnterSub: tt.fields.channelEnterSub,
				cache:           tt.fields.cache,
			}
			if err := m.setFirstChief(tt.args.ctx, tt.args.channelId, tt.args.anchorUid, tt.args.knightUid, tt.args.force); (err != nil) != tt.wantErr {
				t.Errorf("setFirstChief() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestNewMgr(t *testing.T) {

	//ctx := context.Background()

	type args struct {
		sc *conf.ServiceConfigT
	}
	tests := []struct {
		name    string
		args    args
		want    *Manager
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestNewMgr",
			args: args{sc: &conf.ServiceConfigT{
				RedisConfig:              &config.RedisConfig{},
				MysqlConfig:              &config.MysqlConfig{},
				PresentKafkaConfig:       &config.KafkaConfig{},
				KnightKafkaConfig:        &config.KafkaConfig{},
				ChannelSimpleKafkaConfig: &config.KafkaConfig{},
			}},
			want:    nil,
			wantErr: false,
		},
	}
	ctx := context.Background()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewMgr(ctx, tt.args.sc)
			if (err != nil) != tt.wantErr {
				//t.Errorf("NewMgr() error = %v, wantErr %v", err, tt.wantErr)
				//return
			}
			/*			if !reflect.DeepEqual(got, tt.want) {
						t.Errorf("NewMgr() got = %v, want %v", got, tt.want)
					}*/
		})
	}
}

func TestRankValueInfoSlice_Len(t *testing.T) {
	tests := []struct {
		name string
		a    RankValueInfoSlice
		want int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.a.Len(); got != tt.want {
				t.Errorf("Len() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRankValueInfoSlice_Less(t *testing.T) {
	type args struct {
		i int
		j int
	}
	tests := []struct {
		name string
		a    RankValueInfoSlice
		args args
		want bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.a.Less(tt.args.i, tt.args.j); got != tt.want {
				t.Errorf("Less() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRankValueInfoSlice_Swap(t *testing.T) {
	type args struct {
		i int
		j int
	}
	tests := []struct {
		name string
		a    RankValueInfoSlice
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
		})
	}
}

func Test_firstChiefKey(t *testing.T) {
	type args struct {
		channelId uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "Test_firstChiefKey",
			args: args{channelId: 1024},
			want: fmt.Sprintf("knight_chief_%v", 1024),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := firstChiefKey(tt.args.channelId); got != tt.want {
				t.Errorf("firstChiefKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_flushRankLockKey(t *testing.T) {
	type args struct {
		channelId uint32
		ts        time.Time
	}

	nowTs := time.Now()
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "Test_flushRankLockKey",
			args: args{
				channelId: 1024,
				ts:        nowTs,
			},
			want: fmt.Sprintf("flush_rank_lock_%v_%v", nowTs.Format("20060102"), 1024),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := flushRankLockKey(tt.args.channelId, tt.args.ts); got != tt.want {
				t.Errorf("flushRankLockKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

// func Test_getKeepDayKey(t *testing.T) {
// 	type args struct {
// 		channelId uint32
// 		anchorUid uint32
// 	}
// 	tests := []struct {
// 		name string
// 		args args
// 		want string
// 	}{
// 		// TODO: Add test cases.
// 		{
// 			name: "Test_getKeepDayKey",
// 			args: args{
// 				channelId: 0,
// 				anchorUid: 0,
// 			},
// 			want: fmt.Sprintf("knight_group_day_%v_%v", 0, 0),
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if got := getKeepDayKey(tt.args.channelId, tt.args.anchorUid); got != tt.want {
// 				t.Errorf("getKeepDayKey() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

func Test_getKnightInRoomKey(t *testing.T) {
	type args struct {
		channelId uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "Test_getKnightInRoomKey",
			args: args{channelId: 1024},
			want: fmt.Sprintf("knight_in_room_v2_%v", 1024),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getKnightInRoomKey(tt.args.channelId); got != tt.want {
				t.Errorf("getKnightInRoomKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getMemberHKey(t *testing.T) {
	type args struct {
		channelId uint32
		anchorUid uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "Test_getMemberHKey",
			args: args{
				channelId: 0,
				anchorUid: 0,
			},
			want: fmt.Sprintf("knight_group_info_v2_%v_%v", 0, 0),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getMemberHKey(tt.args.channelId, tt.args.anchorUid); got != tt.want {
				t.Errorf("getMemberHKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getNextWeekDur(t *testing.T) {
	tests := []struct {
		name string
		want uint32
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getNextWeekDur(); got != tt.want {
				t.Errorf("getNextWeekDur() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getTotalSendPresentValKey(t *testing.T) {
	type args struct {
		channelId uint32
		anchorUid uint32
		knightUid uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "Test_getTotalSendPresentValKey",
			args: args{
				channelId: 0,
				anchorUid: 0,
				knightUid: 0,
			},
			want: fmt.Sprintf("knight_send_present_total_value_%v_%v_%v", 0, 0, 0),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getTotalSendPresentValKey(tt.args.channelId, tt.args.anchorUid, tt.args.knightUid); got != tt.want {
				t.Errorf("getTotalSendPresentValKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getWeekRankKey(t *testing.T) {
	type args struct {
		channelId uint32
		anchorUid uint32
		sendTime  uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getWeekRankKey(tt.args.channelId, tt.args.anchorUid, tt.args.sendTime); got != tt.want {
				t.Errorf("getWeekRankKey() = %v, want %v", got, tt.want)
			}
		})
	}
}
