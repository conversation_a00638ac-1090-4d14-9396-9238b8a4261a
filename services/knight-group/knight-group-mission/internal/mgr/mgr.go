package mgr

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	channelPb "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/knight-group-mission"
	"golang.52tt.com/protocol/services/knightgroupmembers"
	"golang.52tt.com/protocol/services/knightgroupscore"
	"golang.52tt.com/services/knight-group/knight-group-mission/internal/cache"
	"golang.52tt.com/services/knight-group/knight-group-mission/internal/conf"
	"golang.52tt.com/services/knight-group/knight-group-mission/internal/rpc"
	"golang.52tt.com/services/knight-group/knight-group-mission/internal/store"
	"golang.52tt.com/services/knight-group/knight-group-mission/internal/timer"
	"sort"
	"strconv"
	"time"
)

type KnightMissionMgr struct {
	store store.IStore
	cache cache.ICache
	sc    *conf.StartConfig
}

var weekMap = map[time.Weekday]string{
	time.Monday:    "周一",
	time.Tuesday:   "周二",
	time.Wednesday: "周三",
	time.Thursday:  "周四",
	time.Friday:    "周五",
	time.Saturday:  "周六",
	time.Sunday:    "周日",
}

func NewKnightMissionMgr(ctx context.Context, sc *conf.StartConfig) (*KnightMissionMgr, error) {
	missionStore, err := store.NewMissionStore(ctx, sc.MysqlConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewKnightMissionMgr err :%v", err)
		return nil, err
	}

	missionCache, err := cache.NewMissionCache(ctx, sc.RedisConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewMissionCache err :%v", err)
		return nil, err
	}

	go timer.TimerHandle(ctx, 5*time.Second, sc.TestConfigParse)

	return &KnightMissionMgr{
		store: missionStore,
		cache: missionCache,
		sc:    sc,
	}, nil
}

type uidCreate struct {
	uid    uint32
	create uint32
}

func (m *KnightMissionMgr) Close() {
	m.cache.Close()
}

var NoMission = true

func (m *KnightMissionMgr) GetKnightMission(ctx context.Context, uid uint32, channelId uint32) (*pb.GetKnightMissionResp, error) {
	resp := &pb.GetKnightMissionResp{}
	resp.MemberList = make([]*pb.KnightMissionMember, 0)
	// 填任务文本
	tranCfgToMissionText(m.sc.LiveMissionConfig, resp)

	// 拿周期时间
	timeList, err := m.store.GetAnchorUidKnightDurationTime(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetKnightMission GetKnightDurationTime err , uid :%d , err: %v", uid, err)
		return resp, protocol.NewExactServerError(nil, status.ErrKnightGroupSysErr)
	}

	timeMap := make(map[uint32]time.Time)
	for _, item := range timeList {
		timeMap[item.FromUid] = item.BeginTime
	}

	// 多查一次是否过期
	members, err := rpc.KnightMemberCli.GetKnightGroupMember(ctx, &knightgroupmembers.GetKnightGroupMemberReq{
		ChannelId: channelId,
		AnchorUid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetKnightMission GetKnightGroupMember err , uid :%d , err: %v", uid, err)
		return resp, err
	}

	memberMap := make(map[uint32]*knightgroupmembers.KnightMember)
	for _, item := range members.GetKnightMemberList() {
		memberMap[item.GetUid()] = item
	}

	// 获取日期，填日期相关的文本
	missionInfo, err := m.store.GetKnightMissionsByToUid(ctx, uid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetKnightMission GetKnightMissionsByToUid err , uid :%d , err: %v", uid, err)
		return resp, protocol.NewExactServerError(nil, status.ErrKnightGroupSysErr)
	}

	orderList := make([]string, 0)
	for _, item := range missionInfo {
		for i := 1; i <= 4; i++ {
			orderList = append(orderList, item.OrderId+fmt.Sprintf("_%d", i))
		}
	}

	missionHistoryMap, err := m.store.GetKnightMissionHistoryByOrderId(ctx, orderList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetKnightMission GetKnightMissionsByToUid err , uid :%d , err: %v", uid, err)
		return resp, protocol.NewExactServerError(nil, status.ErrKnightGroupSysErr)
	}
	nowTs := time.Now()

	// 同一天的要合并，然后重新排序
	missionInfoMap := make(map[uidCreate]*store.KnightMission)
	missionInfoList := make([]*store.KnightMission, 0)
	for _, info := range missionInfo {
		if info.EndTime.Before(timeMap[info.FromUid]) {
			continue
		}

		if _, ok := memberMap[info.FromUid]; !ok {
			continue
		}

		begin, _ := GetWeek(info.CreateTime, 0)
		create := uidCreate{
			uid:    info.FromUid,
			create: uint32(begin.Unix()),
		}

		if _, ok := missionInfoMap[create]; ok {
			missionInfoMap[create].TotalPrice = info.TotalPrice/4 + missionInfoMap[create].TotalPrice
		} else {
			missionInfoMap[create] = info
			missionInfoMap[create].TotalPrice = missionInfoMap[create].TotalPrice / 4
		}
	}

	for _, info := range missionInfoMap {
		missionInfoList = append(missionInfoList, info)
	}

	sort.Slice(missionInfoList, func(i, j int) bool {
		return missionInfoList[i].OutsideTime.After(missionInfoList[j].OutsideTime)
	})

	for _, item := range missionInfoList {

		missionList := make([]*pb.KnightMission, 0)
		beginTs := item.CreateTime

		for i := 1; i <= 4; i++ {
			begin, end := GetWeek(beginTs, i-1)
			timeStr := getTimeRangeStr(begin, end)
			isThisWeek := false

			log.InfoWithCtx(ctx, "nowTs: %v , begin:%v , end: %v", nowTs, begin, end)
			if nowTs.After(begin) && nowTs.Before(end) {
				timeStr = "本周期"
				resp.TimeStringNow = getNowTimeRangeStr(begin, end)
				isThisWeek = true
			}

			isFinished := false
			timeCount := begin.Format("2006-01-02")
			orderId := item.OrderId + fmt.Sprintf("_%d", i)
			if record, ok := missionHistoryMap[orderId]; ok {
				fmt.Println(orderId, timeCount, record.Status)
				if record.Status == store.MissStatusSucceed {
					isFinished = true
				}
			}

			missionList = append(missionList, &pb.KnightMission{
				TimeString: timeStr,
				TotalScore: item.TotalPrice,
				IsFinished: isFinished,
				IsThisWeek: isThisWeek,
			})
		}

		resp.MemberList = append(resp.MemberList, &pb.KnightMissionMember{
			Uid:         item.FromUid,
			MissionList: missionList,
		})
	}

	return resp, err
}

func (m *KnightMissionMgr) AddKnightMission(ctx context.Context, event *knightgroupmembers.JoinKnightGroupEvent) error {
	if NoMission {
		return nil
	}
	log.InfoWithCtx(ctx, "AddKnightMission event:%s", event.String())
	createTime := GetNextZeroTime(time.Unix(int64(event.GetCreateTime()), 0).Local())
	endTime := time.Unix(int64(event.GetExpireTime()), 0).Local()
	outsideTime := time.Unix(int64(event.GetCreateTime()), 0).Local()
	beginTime := time.Unix(int64(event.GetBeginTime()), 0).Local()

	missionType := 0
	if event.GetChannelType() == uint32(channelPb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		missionType = store.MissionTypeLive
	} else {
		missionType = store.MissionTypeMulti
	}

	err := m.store.AddKnightMission(ctx, &store.KnightMission{
		OrderId:     event.GetOrderId(),
		ChannelId:   event.GetChannelId(),
		FromUid:     event.GetKnightUid(),
		ToUid:       event.GetAnchorUid(),
		GuildId:     event.GetGuildId(),
		MissionType: uint32(missionType),
		TotalPrice:  event.GetPrice() / 2,
		CreateTime:  createTime,
		EndTime:     endTime,
		OutsideTime: outsideTime,
		SourcePrice: event.GetPrice(),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "AddKnightMission AddKnightMission fail ,event %+v, err %v", event, err)
		return protocol.NewExactServerError(nil, status.ErrKnightGroupSysErr)
	}

	_, _, exist, err := m.store.GetKnightDurationTime(ctx, event.GetKnightUid(), event.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddKnightMission GetKnightDurationTime fail ,event %+v, err %v", event, err)
		return err
	}
	if !exist {
		err := m.store.SetKnightDurationTime(ctx, event.GetKnightUid(), event.GetAnchorUid(), outsideTime, endTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "AddKnightMission SetKnightDurationTime fail ,event %+v, err %v", event, err)
			return protocol.NewExactServerError(nil, status.ErrKnightGroupSysErr)
		}
		return nil
	}

	err = m.store.UpdateKnightDurationTime(ctx, event.GetKnightUid(), event.GetAnchorUid(), beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddKnightMission UpdateKnightDurationTime fail ,event %+v, err %v", event, err)
		return protocol.NewExactServerError(nil, status.ErrKnightGroupSysErr)
	}

	return nil
}

func (m *KnightMissionMgr) GetOrderForSettle(ctx context.Context, nowTs time.Time, week uint32) (begin, weekEnd time.Time, list []*store.KnightMission, err error) {
	begin, weekEnd = GetWeek(nowTs, -int(week))
	dayEnd := begin.AddDate(0, 0, 1)

	list, err = m.store.GetKnightMissionsByCreateTime(ctx, begin, dayEnd)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderForSettle GetKnightMissionsByCreateTime fail ,nowTs %+v, err %v", nowTs, err)
		return begin, weekEnd, list, protocol.NewExactServerError(nil, status.ErrKnightGroupSysErr)
	}

	return begin, weekEnd, list, nil
}

func (m *KnightMissionMgr) GetKnightMissionByTimeRange(ctx context.Context, begin, end time.Time) (list []*store.KnightMission, err error) {
	list, err = m.store.GetKnightMissionByOutsideTime(ctx, begin, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetKnightMissionByTimeRange GetKnightMissionByOutsideTime fail , err %v", err)
		return list, err
	}

	return list, nil
}

func (m *KnightMissionMgr) SettleKnightMission(ctx context.Context, mission *store.KnightMission, nowTs time.Time, week uint32) error {

	isFinished := false

	statusM := store.MissStatusFailed
	begin, end := GetWeek(nowTs, -1)
	score := int32(mission.TotalPrice / 4)
	orderId := fmt.Sprintf("%s_%d", mission.OrderId, week)

	resp, err := rpc.ChannelLiveCli.GetAnchorDailyRecordWithDateList(ctx, 0, mission.ToUid, uint32(begin.Unix()), uint32(end.Add(-12*time.Hour).Unix()))
	if err != nil {
		log.ErrorWithCtx(ctx, "SettleKnightMission GetAnchorDailyRecordWithDateList fail ,uid %d, err %v", mission.ToUid, err)
		return err
	}

	totalTime := uint32(0)
	totalDay := uint32(0)
	for _, date := range resp.GetList() {
		totalTime += date.LiveValidMinutes
		if date.IsValidDay {
			totalDay += 1
		}
	}

	log.InfoWithCtx(ctx, "SettleKnightMission uid:%d , totalTime:%d , totalDay:%d", mission.ToUid, totalTime, totalDay)

	if totalTime >= m.sc.LiveMissionConfig.MissionRequire && totalDay >= m.sc.LiveMissionConfig.MissionDayCount {
		isFinished = true
		statusM = store.MissStatusSucceed
	}

	sErr := m.store.RecordKnightMissionHistory(ctx, &store.KnightMissionHistory{
		OrderId:      mission.OrderId,
		ScoreOrderId: orderId,
		ChannelId:    mission.ChannelId,
		FromUid:      mission.FromUid,
		ToUid:        mission.ToUid,
		GuildId:      mission.GuildId,
		Status:       uint32(statusM),
		TimeCount:    strconv.Itoa(int(week)),
		MissionType:  store.MissionTypeLive,
		TotalPrice:   uint32(score),
		CreateTime:   time.Unix(nowTs.Unix(), 0).Local(),
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "SettleKnightMission RecordKnightMissionHistory fail ,uid %d, err %v", mission.ToUid, sErr)
		return protocol.NewExactServerError(nil, status.ErrKnightGroupSysErr)
	}

	if isFinished {
		_, err := rpc.KnightScoreCli.AddKnightScore(ctx, &knightgroupscore.AddKnightScoreReq{
			OrderId:      orderId,
			ReasonDetail: "完成任务获得骑士积分",
			ReasonType:   knightgroupscore.ReasonType_REASON_TRIVIAGAME_REWARD,
			OwnerId:      mission.ToUid,
			Score:        score,
			ServerTime:   uint32(nowTs.Unix()),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "SettleKnightMission AddKnightScore fail ,uid %d, err %v", mission.ToUid, err)
			return protocol.NewExactServerError(nil, status.ErrKnightGroupSysErr)
		}
	}

	log.InfoWithCtx(ctx, "SettleKnightMission end , begin , end , mission: %+v , isFinished:%v", begin, end, mission, isFinished)

	return nil
}

func tranCfgToMissionText(missionConfig *conf.MissionConfig, resp *pb.GetKnightMissionResp) {
	resp.MissionText = make([]*pb.KnightMissionText, 0)
	for _, item := range missionConfig.MissionText {
		resp.MissionText = append(resp.MissionText, &pb.KnightMissionText{
			Text:    item.Text,
			KeyText: item.KeyText,
		})
	}
	resp.MissionDesc = missionConfig.MissionDesc
}

func getTimeRangeStr(begin, end time.Time) string {
	end = end.AddDate(0, 0, -1)
	return fmt.Sprintf("%d.%d-%d.%d", begin.Month(), begin.Day(), end.Month(), end.Day())
}

func getNowTimeRangeStr(begin, end time.Time) string {
	end = end.AddDate(0, 0, -1)
	return fmt.Sprintf("%s(%d.%d)至%s(%d.%d)", weekMap[begin.Weekday()], begin.Month(), begin.Day(),
		weekMap[end.Weekday()], end.Month(), end.Day())
}

// GetWeek 获取某个周期的日期，offset标识向前/向后偏移几周
func GetWeek(t time.Time, offset int) (time.Time, time.Time) {
	dayObj := GetZeroTime(t)
	beginTs := dayObj.AddDate(0, 0, 7*offset)
	endTs := dayObj.AddDate(0, 0, 7*offset+7)
	return beginTs, endTs
}

// GetZeroTime 获取某一天的0点时间
func GetZeroTime(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// GetNextZeroTime 获取下一天的0点时间
func GetNextZeroTime(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day()+1, 0, 0, 0, 0, t.Location())
}

func (m KnightMissionMgr) SettleKnightMissionHandler() {
	nowTs := time.Now()

	ctx := context.Background()
	if m.sc.TestConfig.NowTime != "" {
		nowTs, _ = time.ParseInLocation("2006-01-02 15:04:05", m.sc.TestConfig.NowTime, time.Local)
	}

	// 每天一点结算，可配置
	if m.sc.LiveMissionConfig.SettleHour != -1 {
		ok, _ := m.cache.CheckDaySettleCount(ctx, nowTs)
		if ok || nowTs.Hour() != int(m.sc.LiveMissionConfig.SettleHour) {
			return
		}
	}

	if len(m.sc.LiveMissionConfig.TestWeek) > 0 {
		nowTs = GetNextZeroTime(nowTs)
	}

	for i := 1; i <= 4; i++ {
		// 模拟除去今天，i*7天后的零点

		for _, week := range m.sc.LiveMissionConfig.TestWeek {
			if week == uint32(i) {
				nowTs = nowTs.AddDate(0, 0, 7)
			}
		}

		log.Infof("nowTs %s", nowTs.String())

		begin, weekEnd, resp, err := m.GetOrderForSettle(ctx, nowTs, uint32(i))
		if err != nil {
			log.ErrorWithCtx(ctx, "SettleKnightMission GetOrderForSettle err , week %d , err: %v", i, err)
			continue
		}

		log.InfoWithCtx(ctx, "SettleKnightMission begin , now , begin ,end , week: %s , %s, %s , %d ", nowTs.String(), begin.String(), weekEnd.String(), i)

		for _, item := range resp {
			_ = m.SettleKnightMission(ctx, item, nowTs, uint32(i))
		}
	}

	_ = m.cache.RecordDaySettleCount(ctx, nowTs)

}
