package main

import (
	"bufio"
	"fmt"
	"github.com/go-redis/redis"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/config"
	"os"
	"regexp"
	"strconv"
	"time"
)

// Record 结构体定义
type Record struct {
	Uid          int
	TargetUid    int
	OrderId      string
	ChannelId    int
	ChannelType  int
	SendTime     int64
	ItemId       int
	ItemCount    int
	Price        int
	PriceType    int
	ItemSource   int
	RankingRatio int
	MsgType      int
	DealToken    string
	BatchType    int
	// 可选字段
	AddRich       int
	AddCharm      int
	Score         int
	GuildId       int
	TagType       int
	ChannelGameId int
}

func main() {
	//ctx := context.Background()
	var err error

	mysqlConfig := &config.MysqlConfig{
		Host:         "************",
		Port:         3306,
		UserName:     "godman",
		Password:     "thegodofman",
		Database:     "channel",
		Charset:      "utf8",
		Protocol:     "tcp",
		PingInterval: 300,
		MaxIdleConns: 25,
		MaxOpenConns: 50,
	}
	mysqlDb, err := gorm.Open("mysql", mysqlConfig.ConnectionString())
	if err != nil {
		fmt.Printf("Failed to Connect mysql %v", err)
		return
	}
	redisCli := redis.NewClient(&redis.Options{
		Network:  "tcp",
		Addr:     "************:6379",
		PoolSize: 2,
		DB:       0,
	})

	rs := GetOrderList()
	fmt.Println(len(rs))

	//sort.Slice(rs, func(i, j int) bool {
	//	return rs[i].Price > rs[j].Price
	//})

	i := 1
	for _, r := range rs {
		if r.OrderId == "magic_103197383_1_1701744941546802767_248601966" {
			continue
		}
		if r.OrderId == "magic_176630584_7_1701744614642409215_200421123_0" {
			continue
		}

		sentTime := time.Unix(r.SendTime, 0)

		fmt.Println(i, r.Uid, r.TargetUid, r.ChannelType, r.ChannelId, r.OrderId, r.SendTime, r.AddRich, r.AddCharm, sentTime)

		tbl := fmt.Sprintf("tbl_channel_consume_%02d", r.ChannelId%100)
		sql := fmt.Sprintf("update %s set reddaimond_cnt = reddaimond_cnt+? where channel_id = ? and uid = ? limit 1;", tbl)
		err = mysqlDb.Exec(sql, r.Price, r.ChannelId, r.Uid).Error
		if err != nil {
			fmt.Println("ERR: ", err)
			continue
		}

		// 从tbl_channel_consume_获取reddaimond_cnt
		type Consume struct {
			ReddaimondCnt int `gorm:"column:reddaimond_cnt"`
		}
		consume := new(Consume)
		err = mysqlDb.Table(tbl).Select("reddaimond_cnt").Where("channel_id = ? and uid = ?", r.ChannelId, r.Uid).Find(&consume).Error
		if err != nil {
			fmt.Println("ERR: ", err)
			continue
		}

		key := fmt.Sprintf("C_CONSUM_TOP_V2_%d", r.ChannelId)
		err = redisCli.ZAdd(key, redis.Z{
			Score:  float64(consume.ReddaimondCnt),
			Member: r.Uid,
		}).Err()
		if err != nil {
			fmt.Println("ERR: ", err)
			continue
		}
		i++
	}
}

func GetOrderList() []*Record {
	rs := make([]*Record, 0)
	// 打开文件
	file, err := os.Open("orderInfo.txt") // 替换为你的文件名
	if err != nil {
		fmt.Println("Error opening file:", err)
		return nil
	}
	defer file.Close()

	// 创建文件的缓冲读取器
	scanner := bufio.NewScanner(file)

	// 定义正则表达式
	re := map[string]*regexp.Regexp{
		"uid":        regexp.MustCompile(`uid: (\d+)`),
		"target_uid": regexp.MustCompile(`target_uid: (\d+)`),
		//"order_id":      regexp.MustCompile(`order_id: "([^"]+)"`),
		"order_id":      regexp.MustCompile(`order_id: \\\"(\w*)\\\"`),
		"channel_id":    regexp.MustCompile(`channel_id: (\d+)`),
		"channel_type":  regexp.MustCompile(`channel_type: (\d+)`),
		"send_time":     regexp.MustCompile(`send_time: (\d+)`),
		"item_id":       regexp.MustCompile(`item_id: (\d+)`),
		"item_count":    regexp.MustCompile(`item_count: (\d+)`),
		"price":         regexp.MustCompile(`price: (\d+)`),
		"price_type":    regexp.MustCompile(`price_type: (\d+)`),
		"item_source":   regexp.MustCompile(`item_source: (\d+)`),
		"ranking_ratio": regexp.MustCompile(`ranking_ratio: (\d+)`),
		"msg_type":      regexp.MustCompile(`msg_type: (\d+)`),
		"deal_token":    regexp.MustCompile(`deal_token: "([^"]+)"`),
		"batch_type":    regexp.MustCompile(`batch_type: (\d+)`),
		// 可选字段
		"add_rich":        regexp.MustCompile(`add_rich: (\d+)`),
		"add_charm":       regexp.MustCompile(`add_charm: (\d+)`),
		"score":           regexp.MustCompile(`score: (\d+)`),
		"guild_id":        regexp.MustCompile(`guild_id: (\d+)`),
		"tag_type":        regexp.MustCompile(`tag_type: (\d+)`),
		"channel_game_id": regexp.MustCompile(`channel_game_id: (\d+)`),
	}

	i := 1
	// 逐行读取并解析
	for scanner.Scan() {
		line := scanner.Text()
		record := Record{}

		// 对每个字段进行匹配
		for key, regex := range re {
			matches := regex.FindStringSubmatch(line)
			if matches != nil {
				switch key {
				case "uid":
					record.Uid, _ = strconv.Atoi(matches[1])
				case "target_uid":
					record.TargetUid, _ = strconv.Atoi(matches[1])
				case "order_id":
					record.OrderId = matches[1]
				case "channel_id":
					record.ChannelId, _ = strconv.Atoi(matches[1])
				case "channel_type":
					record.ChannelType, _ = strconv.Atoi(matches[1])
				case "send_time":
					record.SendTime, _ = strconv.ParseInt(matches[1], 10, 64)
				case "item_id":
					record.ItemId, _ = strconv.Atoi(matches[1])
				case "item_count":
					record.ItemCount, _ = strconv.Atoi(matches[1])
				case "price":
					record.Price, _ = strconv.Atoi(matches[1])
				case "price_type":
					record.PriceType, _ = strconv.Atoi(matches[1])
				case "item_source":
					record.ItemSource, _ = strconv.Atoi(matches[1])
				// 可选字段
				case "add_rich":
					record.AddRich, _ = strconv.Atoi(matches[1])
				case "add_charm":
					record.AddCharm, _ = strconv.Atoi(matches[1])
				}
			}
		}

		if record.PriceType != 2 {
			continue
		}

		rs = append(rs, &record)

		// 打印结构体
		//fmt.Println(i, record)
		//fmt.Println(i, record.Price, record.AddRich)
		i += 1
	}

	// 检查文件读取过程中是否有错误
	if err := scanner.Err(); err != nil {
		panic(err)
	}

	return rs
}
