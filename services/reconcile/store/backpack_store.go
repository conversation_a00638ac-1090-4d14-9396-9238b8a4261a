package store

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/jinzhu/gorm"
	"github.com/jmoiron/sqlx"
	bppb "golang.52tt.com/protocol/services/backpacksvr"
	"golang.52tt.com/services/gift-data-statistics/mysql"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/reconcile/util"
)

// BackpackGainItemInfo 背包获得物品信息
type BackpackGainItemInfo struct {
	UID         uint32 `db:"uid"`           //UID
	UserItemID  uint32 `db:"user_item_id"`  //物品ID
	ItemID      uint32 `db:"user_item_id"`  //物品ID
	UseCount    uint32 `db:"num"`           //获得数量
	ItemType    uint32 `db:"item_type"`     //物品类型
	Source      uint32 `db:"source"`        //获得主渠道
	SourceAppID string `db:"source_app_id"` //获得子渠道
	TotalPrice  uint32 `db:"total_price"`   //总价值
	SourceType  uint32 `db:"source_type"`   //获得主渠道
	CreateTime  string `db:"create_time"`   //日期
	Name        string `db:"name"`          //物品名称
	Desc        string `db:"desc"`          //物品来源
}

// BackpackUseItemInfo 背包消耗物品信息
type BackpackUseItemInfo struct {
	OrderID        string `db:"order_id"`         //OrderID
	UID            uint32 `db:"uid"`              //UID
	UserItemID     uint32 `db:"user_item_id"`     //user_item_id
	ItemID         uint32 `db:"item_id"`          //物品ID
	UseCount       uint32 `db:"use_count"`        //消耗数量
	OperateType    uint32 `db:"operate_type"`     //消耗原因 兑换合成/过期/送礼
	ItemType       uint32 `db:"item_type"`        //物品类型
	TotalPrice     uint32 `db:"total_price"`      //总价值
	SourceType     uint32 `db:"source_type"`      //来源
	SourceID       uint32 `db:"source_id"`        //来源
	OutSideTime    string `db:"outside_time"`     //日期
	CreateTime     string `db:"create_time"`      //日期
	FinalItemCount uint32 `db:"final_item_count"` //
	PriceType      uint32 `db:"price_type"`       //
	Name           string `db:"name"`             //物品名称
	Desc           string `db:"desc"`             //原因
	ItemTypeName   string `db:"type_name"`        //原因

	Date string `db:"date"` //日期 用于统计每日报表
}

// RollbackOrderInfo .
type RollbackOrderInfo struct {
	UID        uint32    `json:"uid"`
	OrderID    string    `json:"order_id"`
	CreateTime time.Time `json:"create_time"`
}

// GetBackpackGainItem 物品获得
// @month 202010
// @idx 01
// @start 2020-11-05 00:00:03
// @end 2020-11-05 23:59:59
func (s *Store) GetBackpackGainItem(db *sqlx.DB, month, idx, start, end string) (map[string]*BackpackGainItemInfo, error) {
	defer util.Trace("GetBackpackGainItem")()

	sqlCommand := fmt.Sprintf("select user_item_id, num, item_type, source_id, source_app_id from user_backpack_gain_item_month_%s_%s where outside_time between '%s' and '%s';", month, idx, start, end)
	rows, err := db.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetBackpackGainItem sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}
	itemsMap := make(map[string]*BackpackGainItemInfo)
	for rows.Next() {
		var item BackpackGainItemInfo
		err = rows.Scan(&item.ItemID, &item.UseCount, &item.ItemType, &item.SourceType, &item.SourceAppID)
		key := fmt.Sprintf("%d_%d", item.ItemID, item.Source)
		v, ok := itemsMap[key]
		if ok {
			itemsMap[key].UseCount = v.UseCount + item.UseCount
		} else {
			itemsMap[key] = &item
		}
	}

	log.Infof("GetBackpackGainItem sql:%s, itemsMap size:%d", sqlCommand, len(itemsMap))
	return itemsMap, err
}

// GetBackpackUsedItems 获得背包使用的物品明细
// @month 202010
// @start 2020-11-05 00:00:03
// @end 2020-11-05 23:59:59
func (s *Store) GetBackpackUsedItems(db *sqlx.DB, month, start, end string) (map[string]*BackpackUseItemInfo, error) {
	defer util.Trace("GetBackpackUsedItems")()

	sqlCommand := fmt.Sprintf("select source_id, item_type, use_count, operate_type,source_type from user_backpack_month_v2_%s where outside_time between '%s' and '%s';", month, start, end)
	rows, err := db.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetBackpackUsedItems failed sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}

	itemsMap := make(map[string]*BackpackUseItemInfo)
	for rows.Next() {
		var item BackpackUseItemInfo
		err = rows.Scan(&item.ItemID, &item.ItemType, &item.UseCount, &item.OperateType, &item.SourceType)
		key := fmt.Sprintf("%d_%d", item.ItemID, item.OperateType)
		v, ok := itemsMap[key]
		if ok {
			itemsMap[key].UseCount = v.UseCount + item.UseCount
		} else {
			itemsMap[key] = &item
		}
	}

	log.Infof("GetBackpackUsedItems sql:%s, itemsMap size:%v", sqlCommand, len(itemsMap))
	return itemsMap, err
}

// GetBackpackGainItemOrders 物品获得
// @month 202010
// @idx 01
// @start 2020-11-05 00:00:00
// @end 2020-11-05  00:00:59
func (s *Store) GetBackpackGainItemOrders(db *sqlx.DB, month, idx, start, end string) (orders []string, err error) {
	defer util.Trace("GetBackpackGainItemOrders")()

	sqlCommand := fmt.Sprintf("select out_order_id from user_backpack_gain_item_month_v2_%s_%s where item_id != %d and outside_time >= '%s' and outside_time < '%s';", month, idx, s.sc.LittleStar, start, end)
	rows, err := db.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetBackpackGainItemOrders sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}

	orders = make([]string, 0, 1000)
	for rows.Next() {
		var order string
		err = rows.Scan(&order)
		if err != nil {
			return orders, err
		}
		orders = append(orders, order)
	}

	log.Infof("GetBackpackGainItemOrders sql:%s, itemsMap size:%d", sqlCommand, len(orders))
	return orders, err
}

// GetBackpackGainItemOrderCount 物品获得订单数量
// @month 202010
// @idx 01
// @start 2020-11-05 00:00:00
// @end 2020-11-05  00:00:59
func (s *Store) GetBackpackGainItemOrderCount(db *sqlx.DB, month, idx, start, end string) (count uint32, err error) {
	defer util.Trace("GetBackpackGainItemOrderCount")()

	sqlCommand := fmt.Sprintf("select count(out_order_id) from user_backpack_gain_item_month_%s_%s where user_item_id != %d and outside_time >= '%s' and outside_time < '%s';", month, idx, s.sc.LittleStar, start, end)
	rows, err := db.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetBackpackGainItemOrderCount sql:%s, err:%v", sqlCommand, err)
		return count, err
	}

	err = rows.Scan(&count)
	if err != nil {
		return count, err
	}
	log.Infof("GetBackpackGainItemOrderCount sql:%s, count:%d", sqlCommand, count)
	return count, err
}

// UpdateUserReconcileUserBackpack 更新对账背包库
func (s *Store) UpdateUserReconcileUserBackpack(db *sqlx.DB, tableName string, uid, userItemID, sourceId, itemType, itemCount, sourceType uint32) (rowEff int64, err error) {
	//tableName := fmt.Sprintf("user_backpack_%02d", uid%100)
	query := fmt.Sprintf(`UPDATE %s set item_count=item_count+%d where uid=%d and source_id=%d and item_type=%d`,
		tableName, itemCount, uid, sourceId, itemType)
	if userItemID > 0 {
		query = fmt.Sprintf("%s AND user_item_id=%d", query, userItemID)
	}
	if sourceType > 0 {
		query = fmt.Sprintf("%s AND source_type=%d", query, sourceType)
	}

	row, err := db.Exec(query)
	if err != nil {
		fmt.Printf("update reconcile backpack failed, query:%s, error:%v", query, err)
		return 0, err
	}

	rowsAffected, err := row.RowsAffected()
	log.Infof("UpdateUserReconcileUserBackpack update reconcile backpack sql:%s, rowEffect:%d", query, rowsAffected)
	return rowsAffected, err
}

// GetBackpackUsedItemList 获得背包使用的物品明细
// @month 202010
// @start 2020-11-05 00:00:03
// @end 2020-11-05 23:59:59
func (s *Store) GetBackpackUsedItemList(db *sqlx.DB, month, start, end string) (items []BackpackUseItemInfo, err error) {
	defer util.Trace("GetBackpackUsedItemList")()

	sqlCommand := fmt.Sprintf("select uid, user_item_id, source_id, item_type, use_count, source_type from user_backpack_month_v2_%s where outside_time between '%s' and '%s';", month, start, end)
	rows, err := db.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetBackpackUsedItemList failed sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}

	items = make([]BackpackUseItemInfo, 0, 1000)
	for rows.Next() {
		var item BackpackUseItemInfo
		err = rows.Scan(&item.UID, &item.UserItemID, &item.ItemID, &item.ItemType, &item.UseCount, &item.SourceType)
		if err == nil {
			items = append(items, item)
		}
	}
	log.Infof("GetBackpackUsedItemList sql:%s, items:%v", sqlCommand, items)
	return items, err
}

func (s *Store) GetConversionCostItemList(db *sqlx.DB, month, start, end string) (items []BackpackUseItemInfo, err error) {
	defer util.Trace("GetConversionCostItemList")()

	sqlCommand := fmt.Sprintf("select uid, user_item_id, item_type, use_count, source from user_backpack_conversion_cost_item_month_%s where outside_time between '%s' and '%s';", month, start, end)
	rows, err := db.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetConversionCostItemList failed sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}

	items = make([]BackpackUseItemInfo, 0, 1000)
	for rows.Next() {
		var item BackpackUseItemInfo
		err = rows.Scan(&item.UID, &item.ItemID, &item.ItemType, &item.UseCount, &item.SourceType)
		if err != nil {
			log.Errorf("GetConversionCostItemList failed scan error:%s, err:%v", sqlCommand, err)
			return items, err
		}
		items = append(items, item)
	}
	log.Infof("GetConversionCostItemList sql:%s, items:%v", sqlCommand, items)
	return items, err
}

// GetBackpackGainItem 物品获得
// @month 202010
// @idx 01
// @start 2020-11-05 00:00:03
// @end 2020-11-05 23:59:59
func (s *Store) GetBackpackGainItemList(db *sqlx.DB, month, idx, start, end string) (items []BackpackGainItemInfo, err error) {
	defer util.Trace("GetBackpackGainItemList")()

	sqlCommand := fmt.Sprintf("select uid, user_item_id, num, item_type, source_id from user_backpack_gain_item_month_%s_%s where outside_time between '%s' and '%s';", month, idx, start, end)
	rows, err := db.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetBackpackGainItemList sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}

	items = make([]BackpackGainItemInfo, 0, 1000)
	for rows.Next() {
		var item BackpackGainItemInfo
		err = rows.Scan(&item.UID, &item.ItemID, &item.UseCount, &item.ItemType, &item.SourceType)
		if err != nil {
			log.Errorf("GetBackpackGainItemList scan:%s, err:%v", sqlCommand, err)
			return items, err
		}
		items = append(items, item)
	}

	log.Infof("GetBackpackGainItemList sql:%s, items:%d", sqlCommand, items)
	return items, err
}

// DecreaseReconcileBackpack
func (s *Store) DecreaseReconcileBackpack(db *sqlx.DB, tableName string, uid, userItemID, itemID, itemType, itemCount, sourceType uint32) (rowEff int64, err error) {
	//tableName := fmt.Sprintf("user_backpack_%02d", uid%100)
	query := fmt.Sprintf(`UPDATE %s set item_count=item_count-%d where uid=%d and source_id=%d and item_type=%d and source_type=%d and item_count>=%d`,
		tableName, itemCount, uid, itemID, itemType, sourceType, itemCount)
	if userItemID > 0 {
		query = fmt.Sprintf("%s and user_item_id=%d", query, userItemID)
	}

	row, err := db.Exec(query)
	if err != nil {
		log.Errorf("update reconcile backpack failed, query:%s, error:%v", query, err)
	}
	rowsAffected, err := row.RowsAffected()
	log.Infof("DecreaseReconcileBackpack update reconcile backpack sql:%s, rowsAffected:%d", query, rowsAffected)
	return rowsAffected, err
}

//计算上个月背包0点剩余物品
func (s *Store) GetReconcileBackpackLastMothRemainWorth(db *sqlx.DB, idx string) (items []BackpackGainItemInfo, err error) {
	defer util.Trace("GetReconcileBackpackLastMothRemainWorth")()

	sqlCommand := fmt.Sprintf("select source_id,item_type, sum(item_count) as item_count, source_type from user_backpack_%s where item_count>0 group by source_id,source_type;", idx)
	rows, err := db.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetBackpackGainItemList sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}

	items = make([]BackpackGainItemInfo, 0, 1000)
	for rows.Next() {
		var item BackpackGainItemInfo
		err = rows.Scan(&item.ItemID, &item.ItemType, &item.UseCount, &item.SourceType)
		if err != nil {
			log.Errorf("GetReconcileBackpackLastMothRemainWorth scan:%s, err:%v", sqlCommand, err)
			return items, err
		}
		items = append(items, item)
	}

	log.Infof("GetReconcileBackpackLastMothRemainWorth sql:%s, items:%d", sqlCommand, items)
	return items, err
}

// GetUserGainItems 物品获得
// @month 202010
// @idx 01
// @start 2020-11-05 00:00:03
// @end 2020-11-05 23:59:59
func (s *Store) GetUserGainItems(uid uint32, month, idx, start, end string) (items []BackpackGainItemInfo, err error) {
	defer util.Trace("GetUserGainItem")()

	sqlCommand := fmt.Sprintf("select user_item_id, num, item_type, source_id, create_time from user_backpack_gain_item_month_%s_%s where uid=%d and outside_time >= '%s' and outside_time <= '%s';", month, idx, uid, start, end)
	rows, err := s.backpackSlaveDB.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetUserGainItem sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}
	items = make([]BackpackGainItemInfo, 0, 100)
	for rows.Next() {
		var item BackpackGainItemInfo
		err = rows.Scan(&item.ItemID, &item.UseCount, &item.ItemType, &item.Source, &item.CreateTime)
		if err != nil {
			log.Errorf("GetUserGainItem scan:%s, err:%v", sqlCommand, err)
			return items, err
		}
		items = append(items, item)
	}

	log.Infof("GetUserGainItem sql:%s, items:%v", sqlCommand, items)
	return items, err
}

// GetUserUseItems 物品消耗
// @uid 01
// @month 202010
// @start 2020-11-05 00:00:03
// @end 2020-11-05 23:59:59
func (s *Store) GetUserUseItems(uid uint32, month, start, end string) (items []BackpackUseItemInfo, err error) {
	defer util.Trace("GetUserUseItems")()

	sqlCommand := fmt.Sprintf("select uid, source_id, use_count, operate_type, create_time from user_backpack_month_v2_%s where uid=%d and create_time >= '%s' and create_time <= '%s';", month, uid, start, end)
	rows, err := s.backpackSlaveDB.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetUserUseItems sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}
	items = make([]BackpackUseItemInfo, 0, 100)
	for rows.Next() {
		var item BackpackUseItemInfo
		err = rows.Scan(&item.UID, &item.ItemID, &item.UseCount, &item.OperateType, &item.CreateTime)
		if err != nil {
			log.Errorf("GetUserUseItems scan:%s, err:%v", sqlCommand, err)
			return items, err
		}
		items = append(items, item)
	}

	log.Infof("GetUserUseItems sql:%s, items:%v", sqlCommand, items)
	return items, err
}

// GetConversionUserUseItems 合成物品消耗
// @month 202010
// @uid 01
// @start 2020-11-05 00:00:03
// @end 2020-11-05 23:59:59
func (s *Store) GetConversionUserUseItems(uid uint32, month, start, end string) (items []BackpackUseItemInfo, err error) {
	defer util.Trace("GetConversionUserUseItems")()

	sqlCommand := fmt.Sprintf("select uid, user_item_id, use_count, create_time from user_backpack_conversion_cost_item_month_%s where uid=%d and create_time >= '%s' and create_time <= '%s';", month, uid, start, end)
	rows, err := s.backpackSlaveDB.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetConversionUserUseItems sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}
	items = make([]BackpackUseItemInfo, 0, 100)
	for rows.Next() {
		var item BackpackUseItemInfo
		err = rows.Scan(&item.UID, &item.ItemID, &item.UseCount, &item.CreateTime)
		if err != nil {
			log.Errorf("GetConversionUserUseItems scan:%s, err:%v", sqlCommand, err)
			return items, err
		}
		items = append(items, item)
	}

	log.Infof("GetConversionUserUseItems sql:%s, items:%v", sqlCommand, items)
	return items, err
}

// GetBackpackUserItemID .
func (s *Store) GetBackpackUserItemID(db *sqlx.DB, uid, itemID, itemType, itemCount, sourceType uint32) (userItemID uint32, err error) {
	tableName := fmt.Sprintf("user_backpack_%02d", uid%100)
	query := fmt.Sprintf(`select max(user_item_id) as user_item_id from %s where uid=%d and source_id=%d and item_type=%d and item_count>=%d`,
		tableName, uid, itemID, itemType, itemCount)
	if sourceType > 0 {
		query = fmt.Sprintf("%s and source_type=%d", query, sourceType)
	}

	userItemID = 0
	row := db.QueryRowxContext(context.Background(), query)
	if err := row.Err(); err != nil {
		log.Errorf("GetBackpackUserItemID query:%s, err:%v \n", query, err)
		return userItemID, err
	}
	if err := row.Scan(&userItemID); err != nil {
		log.Errorf("GetBackpackUserItemID Scan query:%s, err:%v \n", query, err)
		return userItemID, err
	}

	if userItemID == 0 {
		log.Errorf("GetBackpackUserItemID update reconcile backpack sql:%s  count:%d\n", query, userItemID)
	}
	return userItemID, err
}

// GetUserItemsGroupBySourceID 玩家当前物品统计
// @idx 00-99
func (s *Store) GetUserItemsGroupBySourceID(db *sqlx.DB, idx string) (items []BackpackUseItemInfo, err error) {
	defer util.Trace("GetUserItemsGroupBySourceID")()
	sqlCommand := fmt.Sprintf("select source_id,item_type,source_type,sum(item_count) as total_count from user_backpack_%s group by source_id,item_type,source_type;\n", idx)
	rows, err := db.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetUserItemsGroupBySourceID sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}
	items = make([]BackpackUseItemInfo, 0, 100)
	for rows.Next() {
		var item BackpackUseItemInfo
		err = rows.Scan(&item.ItemID, &item.ItemType, &item.SourceType, &item.UseCount)
		if err != nil {
			log.Errorf("GetUserItemsGroupBySourceID scan:%s, err:%v", sqlCommand, err)
			return items, err
		}
		items = append(items, item)
	}
	log.Infof("GetUserItemsGroupBySourceID sql:%s, items:%v", sqlCommand, len(items))
	return items, err
}

// GetConsumeItemsGroupBySourceID 玩家使用物品统计
// @month 202101
func (s *Store) GetConsumeItemsGroupBySourceID(db *sqlx.DB, month string) (items []BackpackUseItemInfo, err error) {
	defer util.Trace("GetUserItemsGroupBySourceID")()
	sqlCommand := fmt.Sprintf("select source_id,item_type,source_type,sum(use_count) as total_count from user_backpack_month_v2_%s group by source_id,item_type,source_type;\n", month)
	rows, err := db.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetConsumeItemsGroupBySourceID sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}
	items = make([]BackpackUseItemInfo, 0, 100)
	for rows.Next() {
		var item BackpackUseItemInfo
		err = rows.Scan(&item.ItemID, &item.ItemType, &item.SourceType, &item.UseCount)
		if err != nil {
			log.Errorf("GetConsumeItemsGroupBySourceID scan:%s, err:%v", sqlCommand, err)
			return items, err
		}
		items = append(items, item)
	}
	log.Infof("GetConsumeItemsGroupBySourceID sql:%s, items:%v", sqlCommand, len(items))
	return items, err
}

// GetBackpackOrderCountAndUseCount 一段时间内背包送礼的物品总数量
func (s *Store) GetBackpackOrderCountAndUseCount(ctx context.Context, month string, start, end, source uint32) (orderCount, itemCount uint32, err error) {
	tableName := fmt.Sprintf("user_backpack_month_v2_%s", month)
	query := fmt.Sprintf(`select count(order_id) as order_count,sum(use_count) as item_count from %s where outside_time>= FROM_UNIXTIME(%d) and outside_time < FROM_UNIXTIME(%d) and operate_type = %d`,
		tableName, start, end, source)
	row := s.backpackSlaveDB.QueryRowxContext(ctx, query)

	if err := row.Err(); err != nil {
		log.Errorf("GetBackpackOrderCountAndUseCount query:%s, err:%v \n", query, err)
		return 0, 0, err
	}

	var order, item sql.NullInt64
	orderCount = 0
	itemCount = 0
	if err := row.Scan(&order, &item); err != nil {
		log.Errorf("GetBackpackOrderCountAndUseCount Scan query:%s, err:%v \n", query, err)
		return 0, 0, err
	}

	if order.Valid {
		orderCount = uint32(order.Int64)
	}
	if item.Valid {
		itemCount = uint32(item.Int64)
	}
	log.Infof("GetBackpackOrderCountAndUseCount sql:%s, orderCount:%d, itemCount:%d", query, orderCount, itemCount)
	return orderCount, itemCount, err
}

func (s *Store) GetExchangeItems(db *sqlx.DB, idx, start string) (items []BackpackUseItemInfo, err error) {
	defer util.Trace("GetExchangeItems")()
	sqlCommand := fmt.Sprintf("select source_id,item_type,sum(use_count) as use_count from user_backpack_log_%s where operate_time >= '%s' and (operate_type!=4 and item_type=4) or source_id=186 group by source_id,item_type", idx, start)
	rows, err := db.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetExchangeItems sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}
	items = make([]BackpackUseItemInfo, 0, 100)
	for rows.Next() {
		var item BackpackUseItemInfo
		item.SourceType = 5
		err = rows.Scan(&item.ItemID, &item.ItemType, &item.UseCount)
		if err != nil {
			log.Errorf("GetExchangeItems scan:%s, err:%v", sqlCommand, err)
			return items, err
		}
		items = append(items, item)
	}
	log.Infof("GetExchangeItems sql:%s, items:%v", sqlCommand, len(items))
	return items, err
}

func (s *Store) GetRollbackItems(db *sqlx.DB, idx, start string) (items []BackpackUseItemInfo, err error) {
	defer util.Trace("GetExchangeItems")()
	sqlCommand := fmt.Sprintf("select source_id,item_type,sum(use_count) as use_count from user_backpack_log_%s where operate_time >= '%s' and operate_type=4  group by source_id,item_type", idx, start)
	rows, err := db.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetExchangeItems sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}
	items = make([]BackpackUseItemInfo, 0, 100)
	for rows.Next() {
		var item BackpackUseItemInfo
		item.SourceType = 5
		err = rows.Scan(&item.ItemID, &item.ItemType, &item.UseCount)
		if err != nil {
			log.Errorf("GetExchangeItems scan:%s, err:%v", sqlCommand, err)
			return items, err
		}
		items = append(items, item)
	}
	log.Infof("GetExchangeItems sql:%s, items:%v", sqlCommand, len(items))
	return items, err
}

// GetBackpackOrdersByItemID 送礼订单列表
// @month 202010
// @start 2020-11-05 00:00:03
// @end 2020-11-05 23:59:59
// @itemID 物品ID
func (s *Store) GetBackpackOrdersByItemID(month, start, end string, itemID uint32) (orders []string, err error) {
	defer util.Trace("GetBackpackOrdersByItemID")()
	sql := fmt.Sprintf("select order_id from user_backpack_month_v2_%s where outside_time between '%s' and '%s' and source_id=%d;", month, start, end, itemID)
	rows, err := s.backpackSlaveDB.Queryx(sql)
	if err != nil {
		log.Errorf("GetBackpackOrdersByItemID sql:%s, err:%v", sql, err)
		return orders, err
	}

	orders = make([]string, 0, 100)
	for rows.Next() {
		var orderID string
		err = rows.Scan(&orderID)
		if err == nil {
			orders = append(orders, orderID)
		}
	}

	log.Infof("GetPresentOrdersByItemID sql:%s, orders size:%d", sql, len(orders))
	return orders, err
}

// GetBackpackRollbackOrders 送礼回退订单列表
// @month 202010
// @start 2020-11-05 00:00:03
// @end 2020-11-05 23:59:59
// @itemID 物品ID
func (s *Store) GetBackpackRollbackOrders(month string, orders []string) (orderInfo []RollbackOrderInfo, err error) {
	defer util.Trace("GetBackpackOrdersByItemID")()

	//组成sql in 里面的格式
	var orderList string
	for _, order := range orders {
		orderList = fmt.Sprintf("%s '%s',", orderList, order)
	}
	orderList = strings.TrimRight(orderList, ",")

	sqlStr := fmt.Sprintf("select uid, order_id, outside_time from user_backpack_month_v2_%s where order_id in(%s);", month, orderList)
	rows, err := s.backpackSlaveDB.Queryx(sqlStr)
	if err != nil {
		log.Errorf("GetBackpackOrdersByItemID sql:%s, err:%v", sqlStr, err)
		return orderInfo, err
	}

	orderInfo = make([]RollbackOrderInfo, 0, 100)
	for rows.Next() {
		var order RollbackOrderInfo
		err = rows.Scan(&order.UID, &order.OrderID, &order.CreateTime)
		if err == nil {
			orderInfo = append(orderInfo, order)
		}
	}

	log.Infof("GetBackpackRollbackOrders sql:%s, orders size:%d", sqlStr, len(orders))
	return orderInfo, err
}

// GetGainItemsGroupBySourceIDV2 玩家获得物品统计
// @month 202101
// @idx 01
func (s *Store) GetGainItemsGroupBySourceIDV2(db *sqlx.DB, month, idx string) (items []BackpackUseItemInfo, err error) {
	defer util.Trace("GetGainItemsGroupBySourceIDV2")()
	var sqlCommand string
	if len(idx) == 0 {
		sqlCommand = fmt.Sprintf("select item_id,item_type,source_type,sum(num) as total_count from user_backpack_gain_item_month_v2_%s group by item_id,item_type,source_type;", month)
	} else {
		sqlCommand = fmt.Sprintf("select item_id,item_type,source_type,sum(num) as total_count from user_backpack_gain_item_month_v2_%s_%s group by item_id,item_type,source_type;", month, idx)
	}

	rows, err := db.Queryx(sqlCommand)
	if err != nil {
		log.Errorf("GetGainItemsGroupBySourceIDV2 sql:%s, err:%v", sqlCommand, err)
		return nil, err
	}
	items = make([]BackpackUseItemInfo, 0, 100)
	for rows.Next() {
		var item BackpackUseItemInfo
		err = rows.Scan(&item.ItemID, &item.ItemType, &item.SourceType, &item.UseCount)
		if err != nil {
			log.Errorf("GetGainItemsGroupBySourceIDV2 scan:%s, err:%v", sqlCommand, err)
			return items, err
		}
		items = append(items, item)
	}
	log.Infof("GetGainItemsGroupBySourceIDV2 sql:%s, items:%v", sqlCommand, len(items))
	return items, err
}

// GetGainFragment 玩家获得碎片统计
// @month 202101
// @idx 01
func (s *Store) GetGainFragment(reconcileRmDB *gorm.DB, monthTs time.Time, idx string) (items []BackpackUseItemInfo, err error) {
	defer util.Trace("GetGainItemsGroupBySourceIDV2")()
	sourceMap := make(map[uint32]uint32)
	items = make([]BackpackUseItemInfo, 0, 100)
	logTb := fmt.Sprintf("user_backpack_log_%s", idx)
	bgTb := fmt.Sprintf("user_backpack_%s", idx)
	mysql.SelectFreezeItemList(reconcileRmDB, bgTb, sourceMap)
	freezeInfos := mysql.SelectFreezeLogList(reconcileRmDB, logTb, monthTs, monthTs.AddDate(0, 1, 0))
	for _, freezeItem := range freezeInfos {
		if freezeItem.OperateType != uint32(bppb.LogType_LOG_TYPE_FRAGMENT_ROLLBACK) {
			continue
		}
		sourceType := sourceMap[freezeItem.UserItemID]
		if sourceMap[freezeItem.UserItemID] == 0 {
			resp := mysql.GetSourceType(reconcileRmDB, bgTb, freezeItem.OrderID)
			sourceType = resp.SourceType
			if sourceType == 0 {
				sourceType = uint32(bppb.PackageSourceType_PACKAGE_SOURCE_SMASHEGG)
			}
		}
		items = append(items, BackpackUseItemInfo{
			OrderID:     freezeItem.OrderID,
			UID:         freezeItem.UID,
			UserItemID:  freezeItem.UserItemID,
			ItemID:      freezeItem.SourceId,
			UseCount:    uint32(freezeItem.UseCount),
			OperateType: uint32(freezeItem.OperateType),
			ItemType:    freezeItem.ItemType,
			SourceType:  sourceType,
			SourceID:    freezeItem.SourceId,
			OutSideTime: freezeItem.OperateTime,
			CreateTime:  freezeItem.OperateTime,
		})
	}
	return items, err
}

// GetGainItemsSummaryGroupBySource
// @month 202101
// @idx 01
func (s *Store) GetGainItemsSummaryGroupBySource(ctx context.Context, db *sqlx.DB, month, start, end string, sourceType uint32) (cnt uint32, err error) {
	defer util.Trace("GetGainItemsSummaryGroupBySource")()
	sqlCommand := fmt.Sprintf("select count(1) as total_count from user_backpack_gain_item_month_v2_%s where source_type = %d and outside_time>= '%s' and  outside_time < '%s';", month, sourceType, start, end)
	row := db.QueryRowxContext(ctx, sqlCommand)
	if err = row.Err(); err != nil {
		log.Errorf("GetGainItemsSummaryGroupBySource query:%s, err:%v \n", sqlCommand, err)
		return 0, err
	}

	var orderCount sql.NullInt64
	if err := row.Scan(&orderCount); err != nil {
		log.Errorf("GetGainItemsSummaryGroupBySource Scan query:%s, err:%v \n", sqlCommand, err)
		return 0, err
	}

	if orderCount.Valid {
		cnt = uint32(orderCount.Int64)
	}
	log.Infof("GetGainItemsSummaryGroupBySource sql:%s, cnt:%v", sqlCommand, cnt)
	return cnt, err
}

func (s *Store) IsBackpackRollbackOrderIdExist(ctx context.Context, orderId string) (bool, error) {
	query := fmt.Sprintf(`select count(order_id) from roll_back_order_tbl where order_id=?`)
	row := s.backpackSlaveDB.QueryRowxContext(ctx, query, orderId)
	var count uint32
	err := row.Scan(&count)
	if err != nil {
		log.Errorf("IsUserScoreOrderIdExist query:%s, err:%v \n", query, err)
	}
	if count > 0 {
		return true, nil
	} else {
		return false, nil
	}
}
