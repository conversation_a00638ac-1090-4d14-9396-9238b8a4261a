package main

import (
	"context"
	"fmt"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/services/channel-stats/server"
	"os"

	"golang.52tt.com/pkg/config"
	grpcEx "golang.52tt.com/pkg/foundation/grpc/server"
	"golang.52tt.com/protocol/services/channelstats"
	"google.golang.org/grpc"
)

func main() {

	flag := grpcEx.ParseServerFlags(os.Args)
	fmt.Println(os.Args[0])

	var (
		svr *server.ChannelStatsServer
		err error
	)
	initializer := func(ctx context.Context, s *grpc.Server, sc *config.ServerConfig) error {
		svr, err = server.NewChannelStatsServer(ctx, sc.Configer)
		if err != nil {
			return err
		}

		channelstats.RegisterChannelStatsServer(s, svr)
		return nil
	}

    var unaryInt grpc.UnaryServerInterceptor
	unaryInt = grpc_middleware.ChainUnaryServer(
		grpcEx.StatusCodeUnaryInterceptor,
		grpcEx.RecoverPanicInterceptorBussniss(true),
	)
	closer := func(ctx context.Context, s *grpc.Server) error {
		if svr != nil {
			svr.ShutDown()
		}
		return nil
	}

	s := grpcEx.NewServer(
		flag,
		grpcEx.WithGRPCServerOptions(grpc.UnaryInterceptor(unaryInt)),
		grpcEx.WithGRPCServerInitializer(initializer),
		grpcEx.WithGRPCServerCloser(closer),
		grpcEx.WithDefaultConfig("channel-stats.json", grpcEx.AdapterJSON),
	)

	s.Serve()

}

 
 
 
