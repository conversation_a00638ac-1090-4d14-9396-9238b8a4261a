package matcher

import (
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/services/channel-live/channel-live-pk/internal/model/conf"
	"golang.52tt.com/services/channel-live/channel-live-pk/internal/model/data"
)

func TestGetTeamMatchScoreRound(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockConfManager := conf.NewMockIBusinessConfManager(ctrl)

	matcher := &MultiPkMatcher{
		config: mockConfManager,
	}

	team := &data.MultiPkTeam{
		MatchTs:    time.Now().Unix() - 100,
		MatchScore: 1000,
	}

	// Test case 1: No rules
	mockConfManager.EXPECT().GetScoringMatchRuleList().Return(nil)
	start, end := matcher.getTeamMatchScoreRound(team)
	if start != 0 || end != 0 {
		t.<PERSON>("Expected (0, 0), got (%d, %d)", start, end)
	}

	// Test case 2: No matching rule
	rules := []*conf.ScoringMatchRule{
		{
			TeamTBeanRangeStart: 2000,
			TeamTBeanRangeEnd:   3000,
		},
	}
	mockConfManager.EXPECT().GetScoringMatchRuleList().Return(rules)
	start, end = matcher.getTeamMatchScoreRound(team)
	if start != 0 || end != 0 {
		t.Errorf("Expected (0, 0), got (%d, %d)", start, end)
	}

	// Test case 3: Matching rule with IncStep = 0
	rules = []*conf.ScoringMatchRule{
		{
			TeamTBeanRangeStart: 500,
			TeamTBeanRangeEnd:   1500,
			MatchTBean: conf.MatchTBeanRange{
				Start: 800,
				End:   1200,
			},
		},
	}
	mockConfManager.EXPECT().GetScoringMatchRuleList().Return(rules)
	start, end = matcher.getTeamMatchScoreRound(team)
	if start != 800 || end != 1200 {
		t.Errorf("Expected (800, 1200), got (%d, %d)", start, end)
	}

	// Test case 4: Matching rule with IncStep != 0
	rules = []*conf.ScoringMatchRule{
		{
			TeamTBeanRangeStart: 500,
			TeamTBeanRangeEnd:   1500,
			MatchTBean: conf.MatchTBeanRange{
				Start:    800,
				End:      1200,
				IncStep:  50,
				IncValue: 10,
			},
		},
	}
	mockConfManager.EXPECT().GetScoringMatchRuleList().Return(rules)
	start, end = matcher.getTeamMatchScoreRound(team)
	if start != 780 || end != 1220 {
		t.Errorf("Expected (780, 1220), got (%d, %d)", start, end)
	}

	// Test case 5: Matching rule with IsRel = true
	rules = []*conf.ScoringMatchRule{
		{
			TeamTBeanRangeStart: 500,
			TeamTBeanRangeEnd:   1500,
			MatchTBean: conf.MatchTBeanRange{
				Start:    200,
				End:      300,
				IncStep:  50,
				IncValue: 10,
				IsRel:    true,
			},
		},
	}
	mockConfManager.EXPECT().GetScoringMatchRuleList().Return(rules)
	start, end = matcher.getTeamMatchScoreRound(team)
	if start != 780 || end != 1320 {
		t.Errorf("Expected (800, 1300), got (%d, %d)", start, end)
	}
}

func TestClassifyTeamV2(t *testing.T) {
	// Create a MultiPkMatcher instance
	matcher := &MultiPkMatcher{}

	// Define test cases
	testCases := []struct {
		name              string
		teams             []*data.MultiPkTeam
		matchScoreStart   int64
		matchScoreEnd     int64
		expectedMatchPool MatchPool
	}{
		{
			name:              "Test Case 1: Empty team list",
			teams:             []*data.MultiPkTeam{},
			matchScoreStart:   0,
			matchScoreEnd:     0,
			expectedMatchPool: MatchPool{},
		},
		{
			name: "Test Case 2: Single team with match score within range",
			teams: []*data.MultiPkTeam{
				{
					TeamId:     1,
					MatchScore: 500,
					MemList: []*data.MultiPkTeamMem{
						{
							Uid: 1,
						},
					},
				},
			},
			matchScoreStart: 0,
			matchScoreEnd:   1000,
			expectedMatchPool: MatchPool{
				1: []*data.MultiPkTeam{
					{
						TeamId:     1,
						MatchScore: 500,
						MemList: []*data.MultiPkTeamMem{
							{
								Uid: 1,
							},
						},
					},
				},
			},
		},
		{
			name: "Test Case 3: Single team with match score out of range",
			teams: []*data.MultiPkTeam{
				{
					TeamId:     1,
					MatchScore: 1500,
					MemList: []*data.MultiPkTeamMem{
						{
							Uid: 1,
						},
					},
				},
			},
			matchScoreStart:   0,
			matchScoreEnd:     1000,
			expectedMatchPool: MatchPool{},
		},
		{
			name: "Test Case 4: Multiple teams with varying match scores",
			teams: []*data.MultiPkTeam{
				{
					TeamId:     1,
					MatchScore: 500,
					MemList: []*data.MultiPkTeamMem{
						{
							Uid: 1,
						},
					},
				},
				{
					TeamId:     2,
					MatchScore: 1500,
					MemList: []*data.MultiPkTeamMem{
						{
							Uid: 2,
						},
					},
				},
				{
					TeamId:     3,
					MatchScore: 750,
					MemList: []*data.MultiPkTeamMem{
						{
							Uid: 3,
						},
					},
				},
			},
			matchScoreStart: 0,
			matchScoreEnd:   1000,
			expectedMatchPool: MatchPool{
				1: []*data.MultiPkTeam{
					{
						TeamId:     1,
						MatchScore: 500,
						MemList: []*data.MultiPkTeamMem{
							{
								Uid: 1,
							},
						},
					},
					{
						TeamId:     3,
						MatchScore: 750,
						MemList: []*data.MultiPkTeamMem{
							{
								Uid: 3,
							},
						},
					},
				},
			},
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Call ClassifyTeamV2
			result := matcher.ClassifyTeamV2(tc.teams, tc.matchScoreStart, tc.matchScoreEnd)

			// Compare the result with the expected result
			if !reflect.DeepEqual(result, tc.expectedMatchPool) {
				t.Errorf("Expected %v, got %v", tc.expectedMatchPool, result)
			}
		})
	}
}
