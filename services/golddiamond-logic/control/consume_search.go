package control

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	gold_commission "golang.52tt.com/protocol/services/gold-commission"
	"golang.52tt.com/services/golddiamond-logic/models"
	api "golang.52tt.com/services/golddiamond-logic/models/gen-go"
	"net/http"
	"time"
)

func YuyinConsumeSearchHandler(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	var req api.YuyinConsumeSearchReq
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, int32(api.ErrorCode_ERROR_CODE_UNMARSHAL), "unmarshal err", &api.YuyinConsumeSearchRsp{IncomeList: []*api.YuyinConsumeDetail{}})
		return
	}
	log.DebugfWithCtx(ctx, "YuyinConsumeSearchHandler %s %+v", string(authInfo.Body), req)

	if req.GetGuildid() == 0 || req.GetPageNum() == 0 || req.GetStartTime() == 0 || req.GetEndTime() == 0 {
		log.ErrorWithCtx(ctx, "YuyinConsumeSearchHandler param err: guildid:%d, pagenum:%d, start_time:%d, end_time:%d", req.GetGuildid(), req.GetPageNum(), req.GetStartTime(), req.GetEndTime())
		_ = web.ServeAPICodeJson(w, int32(api.ErrorCode_ERROR_CODE_PARR), "param err", &api.YuyinConsumeSearchRsp{IncomeList: []*api.YuyinConsumeDetail{}})
		return
	}
	valid, guildUid := models.GetModelServer().CheckGuildId(ctx, req.GetGuildid(), authInfo.UserID)
	if !valid {
		log.ErrorWithCtx(ctx, "CheckGuildId param err:  guildid:%d, req_uid:%d, real_uid:%d", req.GetGuildid(), authInfo.UserID, guildUid)
		web.ServeBadReq(w)
		return
	}

	rsp := new(api.YuyinConsumeSearchRsp)
	rsp.IncomeList = make([]*api.YuyinConsumeDetail, 0)

	var paidUid uint32
	if req.GetAccount() != "" {
		paidUid, _, err = models.GetModelServer().AccountClient.GetUidByName(ctx, req.GetAccount())
		if err != nil {
			log.Warnf("YuyinConsumeSearchHandler.AccountClient.GetUidByName err: guildid:%d, account:%s", req.GetGuildid(), req.GetAccount())
			_ = web.ServeAPIJson(w, rsp)
			return
		}
	}

	goldReq := &gold_commission.SearchYuyinGuildDetailReq{
		GuildId:  req.GetGuildid(),
		Offset:   req.GetPage() * req.GetPageNum(),
		Limit:    req.GetPageNum(),
		Start:    uint64(req.GetStartTime()),
		End:      uint64(req.GetEndTime()),
		PaidUid:  paidUid,
		AnchorId: req.GetAnchorUid(),
	}
	goldRsp, err := models.GetModelServer().GoldCommissionClient.SearchYuyinGuildDetail(ctx, goldReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "YuyinConsumeSearchHandler SearchYuyinGuildDetail err: %s", err.Error())
		web.ServeAPIError(w)
		return
	}

	listLen := len(goldRsp.GetDetailList())
	nextPage := listLen == int(req.GetPageNum())
	if listLen == 0 {
		nextPage = false
	}

	rsp.NextPage = nextPage

	if listLen != 0 {
		var uids []uint32
		for _, info := range goldRsp.GetDetailList() {
			uids = append(uids, info.GetPaidUid())
		}
		userInfoMap, ServerErr := models.GetModelServer().AccountClient.GetUsersMap(ctx, uids)
		if ServerErr != nil {
			log.ErrorWithCtx(ctx, "accountClient.GetUsersMap err:%s, %+v", ServerErr.Error(), uids)
			web.ServeAPIError(w)
			return
		}
		for _, v := range uids {
			if _, ok := userInfoMap[v]; !ok {
				log.ErrorWithCtx(ctx, "userInfoMap[v] v:%d", v)
				web.ServeAPIError(w)
				return
			}
		}
		var anchorUids []uint32
		for _, info := range goldRsp.GetDetailList() {
			anchorUids = append(anchorUids, info.GetAnchorId())
		}

		anchorUserInfoMap, ServerErr := models.GetModelServer().AccountClient.GetUsersMap(ctx, anchorUids)
		if ServerErr != nil {
			log.ErrorWithCtx(ctx, "accountClient.GetUsersMap err:%s, %+v", ServerErr.Error(), anchorUids)
			web.ServeAPIError(w)
			return
		}
		for _, v := range anchorUids {
			if _, ok := anchorUserInfoMap[v]; !ok {
				log.ErrorWithCtx(ctx, "anchorUserInfoMap[v] v:%d,err", v)
				web.ServeAPIError(w)
				return
			}
		}
		for _, info := range goldRsp.GetDetailList() {
			consumeInfo := &api.YuyinConsumeDetail{
				PaidAccount:   userInfoMap[info.GetPaidUid()].GetAlias(),
				PaidName:      userInfoMap[info.GetPaidUid()].GetNickname(),
				Consume:       int64(info.GetFee()),
				ConsumeTime:   int64(info.GetDate()),
				AnchorAccount: anchorUserInfoMap[info.GetAnchorId()].GetAlias(),
				AnchorName:    anchorUserInfoMap[info.GetAnchorId()].GetNickname(),
			}
			if info.GetPaidUkwAccount() != "" {
				consumeInfo.PaidAccount = info.GetPaidUkwAccount()
				consumeInfo.PaidName = "神秘人"
			}
			rsp.IncomeList = append(rsp.IncomeList, consumeInfo)
		}
	}
	log.DebugfWithCtx(ctx, "YuyinConsumeSearchHandler rsp %+v", rsp)
	_ = web.ServeAPIJson(w, rsp)
}

func AmuseConsumeSearchHandler(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	var req api.ConsumeSearchReq
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, int32(api.ErrorCode_ERROR_CODE_UNMARSHAL), "unmarshal err", &api.ConsumeSearchRsp{IncomeList: []*api.ConsumeDetail{}})
		return
	}
	log.DebugfWithCtx(ctx, "AmuseConsumeSearchHandler %s %+v", string(authInfo.Body), req)

	if req.GetGuildid() == 0 || req.GetPageNum() == 0 || req.GetStartTime() == 0 || req.GetEndTime() == 0 {
		log.ErrorWithCtx(ctx, "AmuseConsumeSearchHandler param err: guildid:%d, pagenum:%d, start_time:%d, end_time:%d", req.GetGuildid(), req.GetPageNum(), req.GetStartTime(), req.GetEndTime())
		_ = web.ServeAPICodeJson(w, int32(api.ErrorCode_ERROR_CODE_PARR), "param err", &api.ConsumeSearchRsp{IncomeList: []*api.ConsumeDetail{}})
		return
	}
	valid, guildUid := models.GetModelServer().CheckGuildId(ctx, req.GetGuildid(), authInfo.UserID)
	if !valid {
		log.ErrorWithCtx(ctx, "CheckGuildId param err:  guildid:%d, req_uid:%d, real_uid:%d", req.GetGuildid(), authInfo.UserID, guildUid)
		web.ServeBadReq(w)
		return
	}

	rsp := new(api.ConsumeSearchRsp)
	rsp.IncomeList = make([]*api.ConsumeDetail, 0)

	var paidUid uint32
	if req.GetAccount() != "" {
		paidUid, _, err = models.GetModelServer().AccountClient.GetUidByName(ctx, req.GetAccount())
		if err != nil {
			log.Warnf("AmuseConsumeSearchHandler.AccountClient.GetUidByName err: guildid:%d, account:%s", req.GetGuildid(), req.GetAccount())
			_ = web.ServeAPIJson(w, rsp)
			return
		}
	}

	goldReq := &gold_commission.SearchAmuseGuildDetailReq{
		GuildId:   req.GetGuildid(),
		Offset:    req.GetPage() * req.GetPageNum(),
		Limit:     req.GetPageNum(),
		Start:     uint64(req.GetStartTime()),
		End:       uint64(req.GetEndTime()),
		PaidUid:   paidUid,
		ChannelId: req.GetChannelid(),
	}
	goldRsp, err := models.GetModelServer().GoldCommissionClient.SearchAmuseGuildDetail(ctx, goldReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "AmuseConsumeSearchHandler SearchAmuseGuildDetail err: %s", err.Error())
		web.ServeAPIError(w)
		return
	}

	listLen := len(goldRsp.GetDetailList())
	nextPage := listLen == int(req.GetPageNum())
	if listLen == 0 {
		nextPage = false
	}

	rsp.NextPage = nextPage

	if len(goldRsp.GetDetailList()) != 0 {
		var channelIds, uids []uint32
		for _, info := range goldRsp.GetDetailList() {
			channelIds = append(channelIds, info.GetChannelId())
			uids = append(uids, info.GetPaidUid())
		}
		channelInfos, serverErr := models.GetModelServer().ChannelClient.BatchGetChannelSimpleInfo(ctx, req.GetGuildid(), channelIds)
		if serverErr != nil {
			log.ErrorWithCtx(ctx, "channelClient.GetChannelSimpleInfo err:%s, %+v", serverErr.Error(), goldReq)
			// return out, protocol.NewServerError(status.ErrSys)
		}
		userInfoMap, ServerErr := models.GetModelServer().AccountClient.GetUsersMap(ctx, uids)
		if ServerErr != nil {
			log.ErrorWithCtx(ctx, "accountClient.GetUsersMap err:%s, %+v", serverErr.Error(), uids)
			web.ServeAPIError(w)
			return
		}
		for _, info := range goldRsp.GetDetailList() {
			consumeInfo := &api.ConsumeDetail{
				Account:     userInfoMap[info.GetPaidUid()].GetAlias(),
				Name:        userInfoMap[info.GetPaidUid()].GetNickname(),
				ChannelName: channelInfos[info.GetChannelId()].GetName(),
				Consume:     int64(info.GetFee()),
				ConsumeTime: int64(info.GetDate()),
			}
			if info.GetPaidUkwAccount() != "" {
				consumeInfo.Account = info.GetPaidUkwAccount()
				consumeInfo.Name = "神秘人"
			}
			rsp.IncomeList = append(rsp.IncomeList, consumeInfo)
		}
	}

	log.DebugfWithCtx(ctx, "AmuseConsumeSearchHandler rsp %+v", rsp)
	_ = web.ServeAPIJson(w, rsp)
}
