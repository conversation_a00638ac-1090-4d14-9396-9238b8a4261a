package control

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	gold_commission "golang.52tt.com/protocol/services/gold-commission"
	"golang.52tt.com/services/golddiamond-logic/models"
	api "golang.52tt.com/services/golddiamond-logic/models/gen-go"
)

func InteractGameIncomeDetail(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	log.Infof("InteractGameIncomeDetail  authInfo:%+v, uid:%d", authInfo, authInfo.UserID)
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()
	var req api.GuildInitInfoReq
	_, jsonErr, errcode := checkGuildInitInfoReq(ctx, authInfo, &req)
	if jsonErr != nil {
		log.ErrorWithCtx(ctx, "InteractGameIncomeDetail checkGuildInitInfoReq  req:%+v,  error %v", req, jsonErr)
		web.ServeAPICodeJson(w, errcode, jsonErr.Error(), nil)
		return
	}

	// 校验公会ID是否合法
	valid, guildUid := models.GetModelServer().CheckGuildId(ctx, req.GetGuildid(), authInfo.UserID)
	if !valid {
		log.ErrorWithCtx(ctx, "InteractGameIncomeDetail CheckGuildId param err:  guildId:[%+v], req_uid:[%+v], real_uid:[%+v]", req.GetGuildid(), authInfo.UserID, guildUid)
		web.ServeBadReq(w)
		return
	}

	// check per.
	hasPer, err := models.GetModelServer().GoldCommissionClient.GetGuildInteractGamePer(ctx, req.Guildid)
	if err != nil {
		log.ErrorWithCtx(ctx, "InteractGameIncomeDetail GetInteractGameIncomeDetail err:%s", err.Error())
		web.ServeAPIError(w)
		return
	}
	log.Infof("InteractGameIncomeDetail Guildid=%d GetGuildInteractGamePer=%v", req.Guildid, hasPer)

	goldReq := &gold_commission.GetInteractGameIncomeDetailReq{
		GuildId: req.GetGuildid(),
		Uid:     authInfo.UserID,
	}

	goldRsp, err := models.GetModelServer().GoldCommissionClient.GetInteractGameIncomeDetail(context.Background(), goldReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "InteractGameIncomeDetail GetInteractGameIncomeDetail err:%s", err.Error())
		web.ServeAPIError(w)
		return
	}
	log.Infof("InteractGameIncomeDetail Guildid=%d GetInteractGameIncomeDetail=%+v", req.Guildid, goldRsp)

	// 组装返回体
	rsp := &api.GetGeneralIncomeRsp{
		IsShowInteractGame: hasPer || (!hasPer && goldRsp.GetMonthlyIncome().GetThisMonthIncome() > 0),
	}
	if !rsp.IsShowInteractGame {
		web.ServeAPIJson(w, rsp)
		log.InfoWithCtx(ctx, "InteractGameIncomeDetail not show. uid=%d guildUid=%d", authInfo.UserID, guildUid)
		return
	}

	if daily := goldRsp.GetDailyIncome(); daily != nil {
		rsp.DateDimenIncome = &api.GetDateDimenIncomeRsp{
			TodayIncome:     daily.GetTodayIncome(),
			YesterdayIncome: daily.GetYesterdayIncome(),
			LastdayQoq:      daily.GetDailyQoq(),
		}
	}
	if weekly := goldRsp.GetWeeklyIncome(); weekly != nil {
		rsp.WeekDimenIncome = &api.GetWeekDimenIncomeRsp{
			ThisWeekIncome: weekly.GetThisWeekIncome(),
			LastWeekIncome: weekly.GetLastWeekIncome(),
			CurrentTime:    weekly.GetCurrentTime(),
			WeekStartTime:  weekly.GetWeekStartTime(),
			WeekEndTime:    weekly.GetWeekEndTime(),
		}
	}
	if monthly := goldRsp.GetMonthlyIncome(); monthly != nil {
		rsp.MonthDimenIncome = &api.GetMonthDimenIncomeRsp{
			ThisMonthIncome:         monthly.GetThisMonthIncome(),
			LastMonthIncome:         monthly.GetLastMonthIncome(),
			MonthQoq:                monthly.GetMonthlyQoq(),
			SameLastMonthIncome:     monthly.GetSameLastMonthIncome(),
			LastMonthValidIncomeNum: monthly.GetLastMonthValidIncome(),
			MonthSixIncome:          monthly.GetLastSixMonthIncome(),
		}
	}
	if monthly := goldRsp.GetMonthlyExtraIncome(); monthly != nil {
		rsp.InteractGameExtraIncome = &api.GetMonthDimenIncomeRsp{
			ThisMonthIncome: monthly.GetThisMonthIncome(),
			LastMonthIncome: monthly.GetLastMonthIncome(),
		}
	}

	log.Infof("InteractGameIncomeDetail req:%+v, rsp:%+v ", req, rsp)
	web.ServeAPIJson(w, rsp)
}

func InteractGameTodayIncome(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	var req api.TodayIncomeReq
	rsp := &api.InteractGameIncomeRsp{List: []*api.InteractGameIncomeInfo{}}

	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, int32(api.ErrorCode_ERROR_CODE_UNMARSHAL), "unmarshal err", rsp)
		return
	}
	log.DebugWithCtx(ctx, "InteractGameTodayIncome %s %+v", string(authInfo.Body), req)

	if req.GetGuildid() == 0 {
		log.ErrorWithCtx(ctx, "InteractGameTodayIncome param err: guildid:%d", req.GetGuildid())
		_ = web.ServeAPICodeJson(w, int32(api.ErrorCode_ERROR_CODE_PARR), "param err", rsp)
		return
	}
	valid, guildUid := models.GetModelServer().CheckGuildId(ctx, req.GetGuildid(), authInfo.UserID)
	if !valid {
		log.ErrorWithCtx(ctx, "InteractGameTodayIncome CheckGuildId param err: guildid:%d, req_uid:%d, real_uid:%d", req.GetGuildid(), authInfo.UserID, guildUid)
		web.ServeBadReq(w)
		return
	}

	now := time.Now()

	goldReq := &gold_commission.GetGuildDayIncomeListReq{
		GuildId:  req.GetGuildid(),
		Datetime: uint64(now.Unix()),
		Offset:   0,
		Limit:    100,
	}
	goldResp, sErr := models.GetModelServer().GoldCommissionClient.GetInteractGameDayIncomeList(ctx, goldReq)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "InteractGameTodayIncome GetInteractGameDayIncomeList err: %s", sErr.Error())
		web.ServeAPIError(w)
		return
	}

	uids := make([]uint32, 0)
	for _, info := range goldResp.GetAnchorList() {
		uids = append(uids, info.GetAnchorId())
	}

	if len(uids) == 0 {
		_ = web.ServeAPIJson(w, rsp)
		return
	}

	userInfoMap, ServerErr := models.GetModelServer().AccountClient.GetUsersMap(ctx, uids)
	if ServerErr != nil {
		log.ErrorWithCtx(ctx, "InteractGameTodayIncome accountClient.GetUsersMap err:%s, %+v", ServerErr.Error(), uids)
		web.ServeAPIError(w)
		return
	}
	for _, v := range uids {
		if _, ok := userInfoMap[v]; !ok {
			log.ErrorWithCtx(ctx, "InteractGameTodayIncome userInfoMap[v] v:%d", v)
			web.ServeAPIError(w)
			return
		}
	}
	if len(goldResp.GetAnchorList()) != 0 {
		if goldResp != nil {
			for _, info := range goldResp.GetAnchorList() {
				channelInfo := &api.InteractGameIncomeInfo{
					AnchorName:   userInfoMap[info.GetAnchorId()].GetNickname(),
					Members:      int64(info.GetPaidUidCnt()),
					Fee:          int64(info.GetFee()),
					Income:       int64(info.GetIncome()),
					GameDuration: int64(info.GetInteractGameDur()),
				}
				rsp.List = append(rsp.List, channelInfo)
			}
		}
	}

	log.DebugWithCtx(ctx, "InteractGameTodayIncome rsp %+v", rsp)
	_ = web.ServeAPIJson(w, rsp)
}

func InteractGameMonthIncome(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	var req api.MonthIncomeReq
	rsp := &api.InteractGameIncomeRsp{List: []*api.InteractGameIncomeInfo{}}

	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, int32(api.ErrorCode_ERROR_CODE_UNMARSHAL), "unmarshal err", rsp)
		return
	}
	log.DebugWithCtx(ctx, "InteractGameMonthIncome %s %+v", string(authInfo.Body), req)

	if req.GetGuildid() == 0 || req.GetMonthTime() == 0 {
		log.ErrorWithCtx(ctx, "InteractGameMonthIncome param err: guildid:%d， monthtime:%d", req.GetGuildid(), req.GetMonthTime())
		_ = web.ServeAPICodeJson(w, int32(api.ErrorCode_ERROR_CODE_PARR), "param err", rsp)
		return
	}
	valid, guildUid := models.GetModelServer().CheckGuildId(ctx, req.GetGuildid(), authInfo.UserID)
	if !valid {
		log.ErrorWithCtx(ctx, "InteractGameMonthIncome CheckGuildId param err:  guildid:%d, req_uid:%d, real_uid:%d", req.GetGuildid(), authInfo.UserID, guildUid)
		web.ServeBadReq(w)
		return
	}

	goldReq := &gold_commission.GetGuildMonthIncomeListReq{
		GuildId:  req.GetGuildid(),
		Datetime: uint64(req.GetMonthTime()),
		Offset:   0,
		Limit:    100,
	}
	goldResp, sErr := models.GetModelServer().GoldCommissionClient.GetInteractGameMonthIncomeList(ctx, goldReq)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "InteractGameMonthIncome GetInteractGameMonthIncomeList err: %s", sErr.Error())
		web.ServeAPIError(w)
		return
	}

	uids := make([]uint32, 0)
	for _, info := range goldResp.GetAnchorList() {
		uids = append(uids, info.GetAnchorId())
	}

	if len(uids) == 0 {
		_ = web.ServeAPIJson(w, rsp)
		return
	}

	userInfoMap, ServerErr := models.GetModelServer().AccountClient.GetUsersMap(ctx, uids)
	if ServerErr != nil {
		log.ErrorWithCtx(ctx, "InteractGameMonthIncome accountClient.GetUsersMap err:%s, %+v", ServerErr.Error(), uids)
		web.ServeAPIError(w)
		return
	}
	for _, v := range uids {
		if _, ok := userInfoMap[v]; !ok {
			log.ErrorWithCtx(ctx, "InteractGameMonthIncome userInfoMap[v] v:%d", v)
			web.ServeAPIError(w)
			return
		}
	}

	if len(goldResp.GetAnchorList()) != 0 {
		if goldResp != nil {
			for _, info := range goldResp.GetAnchorList() {
				channelInfo := &api.InteractGameIncomeInfo{
					AnchorName:   userInfoMap[info.GetAnchorId()].GetNickname(),
					Members:      int64(info.GetPaidUidCnt()),
					Fee:          int64(info.GetFee()),
					Income:       int64(info.GetIncome()),
					GameDuration: int64(info.GetInteractGameDur()),
				}
				rsp.List = append(rsp.List, channelInfo)
			}
		}
	}

	log.DebugWithCtx(ctx, "InteractGameMonthIncome rsp %+v", rsp)
	_ = web.ServeAPIJson(w, rsp)
}

func InteractGameMonthExtraIncome(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	var req api.InteractGameExtraIncomeReq
	resp := &api.InteractGameExtraIncomeResp{}

	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "InteractGameMonthExtraIncome Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, int32(api.ErrorCode_ERROR_CODE_UNMARSHAL), "unmarshal err", resp)
		return
	}
	log.DebugWithCtx(ctx, "InteractGameMonthExtraIncome uid=%d req=%+v", authInfo.UserID, req)

	uid := authInfo.UserID
	guildId := req.GuildId

	if req.GetGuildId() == 0 || req.GetMonthTime() == 0 {
		log.ErrorWithCtx(ctx, "InteractGameMonthExtraIncome param err: guildid:%d， monthtime:%d", req.GetGuildId(), req.GetMonthTime())
		_ = web.ServeAPICodeJson(w, int32(api.ErrorCode_ERROR_CODE_PARR), "param err", resp)
		return
	}
	valid, guildUid := models.GetModelServer().CheckGuildId(ctx, req.GetGuildId(), authInfo.UserID)
	if !valid {
		log.ErrorWithCtx(ctx, "InteractGameMonthExtraIncome CheckGuildId param err:  guildid:%d, req_uid:%d, real_uid:%d", req.GetGuildId(), authInfo.UserID, guildUid)
		web.ServeBadReq(w)
		return
	}

	getGameMonthExtraIncomeRsp, err := models.GetModelServer().GoldCommissionClient.GetInteractGameExtraIncome(ctx, &gold_commission.GetInteractGameExtraIncomeReq{
		GuildIds:  []uint32{guildId},
		MonthTime: req.GetMonthTime(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "InteractGameMonthExtraIncome GetInteractGameExtraIncome failed: %+v, guild=%d uid=%d", err, guildId, uid)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}
	resp.GameMonthTotalFee = getGameMonthExtraIncomeRsp.GetInfo().GetGameMonthTotalFee()
	resp.GameMonthExtraIncome = getGameMonthExtraIncomeRsp.GetInfo().GetGameMonthExtraIncome()

	uids := []uint32{}
	for _, item := range getGameMonthExtraIncomeRsp.GetInfo().GetAnchorInfoList() {
		uids = append(uids, item.AnchorUid)
	}
	userInfoMap, ServerErr := models.GetModelServer().AccountClient.GetUsersMap(ctx, uids)
	if ServerErr != nil {
		log.ErrorWithCtx(ctx, "InteractGameMonthExtraIncome accountClient.GetUsersMap err:%s, %+v", ServerErr.Error(), uids)
		web.ServeAPIError(w)
		return
	}

	for _, item := range getGameMonthExtraIncomeRsp.GetInfo().GetAnchorInfoList() {
		userInfo := userInfoMap[item.AnchorUid]
		resp.List = append(resp.List, &api.InteractGameExtraIncomeDetail{
			AnchorUid:      item.AnchorUid,
			AnchorTtid:     userInfo.GetAlias(),
			AnchorAccount:  userInfo.GetUsername(),
			AnchorNickname: userInfo.GetNickname(),
			AnchorSex:      uint32(userInfo.GetSex()),
			GameMonthFee:   item.GameMonthFee,
			GameIncomeRate: item.GameIncomeRate,
			GameIncome:     item.GameIncome,
		})
	}

	log.DebugWithCtx(ctx, "InteractGameMonthExtraIncome uid=%d req=%+v resp=%s", uid, req, resp.String())
	_ = web.ServeAPIJson(w, resp)
}
