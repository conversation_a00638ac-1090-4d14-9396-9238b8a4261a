package server

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/config"
	pb "golang.52tt.com/protocol/services/presentextraconf"
	"golang.52tt.com/services/present-extra-conf/internal/conf"
	manager2 "golang.52tt.com/services/present-extra-conf/internal/manager"
	"reflect"
	"testing"
)

var svr *PresentExtraConfServer
var serverCfg config.Configer

func init() {
	serverCfg, _ = config.NewConfig("json", "present-extra-conf.json")
	svr, _ = NewPresentExtraConfServerForTest(context.Background(), serverCfg)
	fmt.Println(svr)
}

func TestNewPresentExtraConfServer(t *testing.T) {
	type args struct {
		ctx context.Context
		cfg config.Configer
	}
	tests := []struct {
		name    string
		args    args
		want    *PresentExtraConfServer
		wantErr bool
	}{
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				cfg: serverCfg,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewPresentExtraConfServerForTest(tt.args.ctx, tt.args.cfg)
			fmt.Println(got, err)
		})
	}
}

//func TestPresentExtraConfServer_AddCustomizedPresentConfig(t *testing.T) {
//	ctl := gomock.NewController(t)
//	defer ctl.Finish()
//	ctx := context.Background()
//	err := errors.New("")
//	svr.CustomMgr, err = manager2.NewCustomizedPresentManagerForTest(ctx, svr.sc, ctl)
//	fmt.Println(err)
//
//	type fields struct {
//		sc            *conf.ServiceConfigT
//		PopUpConfMgr  *manager2.PopUpConfManager
//		ExtraConfMgr  *manager2.ExtraConfManager
//		EffectTimeMgr *manager2.EffectTimeManager
//		CustomMgr     *manager2.CustomizedPresentManager
//	}
//	type args struct {
//		ctx context.Context
//		req *pb.AddCustomizedPresentConfigReq
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *pb.AddCustomizedPresentConfigResp
//		wantErr bool
//	}{
//		{
//			name: "test",
//			fields: fields{
//				sc:            svr.sc,
//				PopUpConfMgr:  svr.PopUpConfMgr,
//				ExtraConfMgr:  svr.ExtraConfMgr,
//				EffectTimeMgr: svr.EffectTimeMgr,
//				CustomMgr:     svr.CustomMgr,
//			},
//			args: args{
//				ctx: ctx,
//				req: &pb.AddCustomizedPresentConfigReq{
//					Config: &pb.CustomizedPresentConfig{
//						GiftId:       1505,
//						CmdUrl:       "www.baidu.com",
//						MaxLevel:     2,
//						EffectConfig: []*pb.LevelEffectConfig{{Level: 1, EffectBegin: 10000, EffectEnd: 1769184706}},
//						CustomConfig: []*pb.CustomConfig{{
//							CustomId:   1,
//							CustomText: "测试",
//							CustomName: "测试组件1",
//							OptionConfig: []*pb.OptionConfig{{
//								OptionId:   1,
//								OptionName: "测试样式1",
//							}, {
//								OptionId:   2,
//								OptionName: "测试样式2",
//							}},
//						}, {
//							CustomId:   2,
//							CustomText: "测试",
//							CustomName: "测试组件2",
//							OptionConfig: []*pb.OptionConfig{{
//								OptionId:   1,
//								OptionName: "测试样式3",
//							}, {
//								OptionId:   2,
//								OptionName: "测试样式4",
//							}},
//						},
//						},
//						LevelConfig: []*pb.LevelConfig{{Level: 1, CustomOption: []*pb.CustomOptionConfig{
//							{CustomId: 1, OptionId: 1}, {CustomId: 2, OptionId: 1},
//						}}, {Level: 2, CustomOption: []*pb.CustomOptionConfig{
//							{CustomId: 1, OptionId: 2}, {CustomId: 2, OptionId: 2},
//						}}},
//						CustomMethod: []*pb.CustomMethod{{Options: []*pb.CustomOptionConfig{{CustomId: 1, OptionId: 1}, {CustomId: 2, OptionId: 1}}, GiftId: 1505},
//							{Options: []*pb.CustomOptionConfig{{CustomId: 1, OptionId: 2}, {CustomId: 2, OptionId: 1}}, GiftId: 1506},
//							{Options: []*pb.CustomOptionConfig{{CustomId: 1, OptionId: 1}, {CustomId: 2, OptionId: 2}}, GiftId: 1507},
//							{Options: []*pb.CustomOptionConfig{{CustomId: 1, OptionId: 2}, {CustomId: 2, OptionId: 2}}, GiftId: 1508}},
//						LevelText: "测试等级文案",
//					},
//				},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			s := &PresentExtraConfServer{
//				sc:            tt.fields.sc,
//				PopUpConfMgr:  tt.fields.PopUpConfMgr,
//				ExtraConfMgr:  tt.fields.ExtraConfMgr,
//				EffectTimeMgr: tt.fields.EffectTimeMgr,
//				CustomMgr:     tt.fields.CustomMgr,
//			}
//			got, err := s.AddCustomizedPresentConfig(tt.args.ctx, tt.args.req)
//			fmt.Println(got, err)
//		})
//	}
//}

func TestPresentExtraConfServer_AddEffectDelayLevel(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.AddEffectDelayLevelReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.AddEffectDelayLevelResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.AddEffectDelayLevel(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddEffectDelayLevel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddEffectDelayLevel() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_AddFlashEffectConfig(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.AddFlashEffectConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.AddFlashEffectConfigResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.AddFlashEffectConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddFlashEffectConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddFlashEffectConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_AddPresentFloatLayer(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.AddPresentFloatLayerReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.AddPresentFloatLayerResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.AddPresentFloatLayer(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddPresentFloatLayer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddPresentFloatLayer() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_BoundPresentFlashEffect(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.BoundPresentFlashEffectReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.BoundPresentFlashEffectResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.BoundPresentFlashEffect(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BoundPresentFlashEffect() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BoundPresentFlashEffect() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_CheckCustomizedGift(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.CheckCustomizedGiftReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.CheckCustomizedGiftResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.CheckCustomizedGift(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckCustomizedGift() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckCustomizedGift() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_DelCustomizedPresentConfig(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.DelCustomizedPresentConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.DelCustomizedPresentConfigResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.DelCustomizedPresentConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DelCustomizedPresentConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DelCustomizedPresentConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_DelEffectDelayLevel(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.DelEffectDelayLevelReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.DelEffectDelayLevelResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.DelEffectDelayLevel(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DelEffectDelayLevel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DelEffectDelayLevel() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_DelFlashEffectConfig(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.DelFlashEffectConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.DelFlashEffectConfigResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.DelFlashEffectConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DelFlashEffectConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DelFlashEffectConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_DelPresentFloatLayer(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.DelPresentFloatLayerReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.DelPresentFloatLayerResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.DelPresentFloatLayer(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DelPresentFloatLayer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DelPresentFloatLayer() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_GetCustomPresentEffectTime(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetCustomPresentEffectTimeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetCustomPresentEffectTimeResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.GetCustomPresentEffectTime(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCustomPresentEffectTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCustomPresentEffectTime() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_GetCustomizedPresentConfig(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetAllCustomizedPresentConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetAllCustomizedPresentConfigResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.GetCustomizedPresentConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCustomizedPresentConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCustomizedPresentConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_GetEffectDelayLevel(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetEffectDelayLevelReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetEffectDelayLevelResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.GetEffectDelayLevel(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetEffectDelayLevel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetEffectDelayLevel() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_GetFlashEffectConfig(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetFlashEffectConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetFlashEffectConfigResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.GetFlashEffectConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFlashEffectConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFlashEffectConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_GetPopUpPresentList(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		in  *pb.GetPopUpPresentListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.GetPopUpPresentListResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			gotOut, err := s.GetPopUpPresentList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPopUpPresentList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("GetPopUpPresentList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestPresentExtraConfServer_GetPresentEffectTime(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetPresentEffectTimeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPresentEffectTimeResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.GetPresentEffectTime(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentEffectTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentEffectTime() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_GetPresentEffectTimeDetail(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetPresentEffectTimeDetailReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPresentEffectTimeDetailResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.GetPresentEffectTimeDetail(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentEffectTimeDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentEffectTimeDetail() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_GetPresentFlashEffect(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetPresentFlashEffectReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPresentFlashEffectResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.GetPresentFlashEffect(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentFlashEffect() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentFlashEffect() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_GetPresentFloatLayer(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetPresentFloatLayerReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPresentFloatLayerResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.GetPresentFloatLayer(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentFloatLayer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentFloatLayer() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_GetUserCustomizedInfo(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetUserCustomizedInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserCustomizedInfoResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.GetUserCustomizedInfo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserCustomizedInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserCustomizedInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_GetUserCustomizedInfoByGiftId(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetUserCustomizedInfoByGiftIdReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserCustomizedInfoByGiftIdResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.GetUserCustomizedInfoByGiftId(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserCustomizedInfoByGiftId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserCustomizedInfoByGiftId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_NotifyPresentSend(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.NotifyPresentSendReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.NotifyPresentSendResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.NotifyPresentSend(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("NotifyPresentSend() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NotifyPresentSend() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_NotifyPrivilegeLevelChange(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.NotifyPrivilegeLevelChangeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.NotifyPrivilegeLevelChangeResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.NotifyPrivilegeLevelChange(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("NotifyPrivilegeLevelChange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NotifyPrivilegeLevelChange() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_ReportCustomOptionChoose(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.ReportCustomOptionChooseReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ReportCustomOptionChooseResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.ReportCustomOptionChoose(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReportCustomOptionChoose() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ReportCustomOptionChoose() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_SearchPresent(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.SearchPresentReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.SearchPresentResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.SearchPresent(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SearchPresent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SearchPresent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_ShutDown(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			s.ShutDown()
		})
	}
}

func TestPresentExtraConfServer_UpdateCustomizedPresentConfig(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.UpdateCustomizedPresentConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.UpdateCustomizedPresentConfigResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.UpdateCustomizedPresentConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateCustomizedPresentConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateCustomizedPresentConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_UpdateEffectDelayLevel(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.UpdateEffectDelayLevelReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.UpdateEffectDelayLevelResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.UpdateEffectDelayLevel(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateEffectDelayLevel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateEffectDelayLevel() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_UpdateFlashEffectConfig(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.UpdateFlashEffectConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.UpdateFlashEffectConfigResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.UpdateFlashEffectConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateFlashEffectConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateFlashEffectConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentExtraConfServer_UpdatePresentFloatLayer(t *testing.T) {
	type fields struct {
		sc            *conf.ServiceConfigT
		PopUpConfMgr  *manager2.PopUpConfManager
		ExtraConfMgr  *manager2.ExtraConfManager
		EffectTimeMgr *manager2.EffectTimeManager
		CustomMgr     *manager2.CustomizedPresentManager
	}
	type args struct {
		ctx context.Context
		req *pb.UpdatePresentFloatLayerReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.UpdatePresentFloatLayerResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentExtraConfServer{
				sc:            tt.fields.sc,
				PopUpConfMgr:  tt.fields.PopUpConfMgr,
				ExtraConfMgr:  tt.fields.ExtraConfMgr,
				EffectTimeMgr: tt.fields.EffectTimeMgr,
				CustomMgr:     tt.fields.CustomMgr,
			}
			got, err := s.UpdatePresentFloatLayer(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdatePresentFloatLayer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdatePresentFloatLayer() got = %v, want %v", got, tt.want)
			}
		})
	}
}
