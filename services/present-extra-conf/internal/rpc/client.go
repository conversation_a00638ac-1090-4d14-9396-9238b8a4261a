package rpc

import (
	"context"
	"github.com/golang/mock/gomock"
	presentprivilege "golang.52tt.com/clients/mocks/present-privilege"
	sendim2 "golang.52tt.com/clients/mocks/sendim"
	userPresentMock "golang.52tt.com/clients/mocks/userpresent"
	privilege "golang.52tt.com/clients/present-privilege"
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/sendim"
	userPresent "golang.52tt.com/clients/userpresent"
	presentprivilege2 "golang.52tt.com/protocol/services/presentprivilege"
	"golang.52tt.com/protocol/services/userpresent"
	"google.golang.org/grpc"
	"time"
)

var (
	UserPresentClient userPresent.IClient
	PrivilegeClient   privilege.IClient
	SendImCli         sendim.IClient
	PushCli           push.IClient
)

func Setup() error {
	opts := []grpc.DialOption{
		grpc.WithBlock(),
	}

	cli := userPresent.NewIClient(opts...)
	PrivilegeClient = privilege.NewIClient(opts...)
	UserPresentClient = cli
	SendImCli = sendim.NewIClient(opts...)
	PushCli = push.NewIClient(opts...)
	return nil
}

func SetupForGetPresentConfigByIdList(ctl *gomock.Controller) (err error) {

	cli := userPresentMock.NewMockIClient(ctl)
	cli.EXPECT().GetPresentConfigByIdList(context.Background(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
		&userpresent.GetPresentConfigByIdListResp{ItemList: []*userpresent.StPresentItemConfig{
			{
				ItemId: 1,
			},
		}}, nil)
	cli.EXPECT().GetPresentConfigByIdList(context.Background(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
		&userpresent.GetPresentConfigByIdListResp{ItemList: []*userpresent.StPresentItemConfig{
			{
				ItemId: 1,
			},
		}}, nil)
	UserPresentClient = cli

	return nil
}

func SetupForGetPresentFloat(ctl *gomock.Controller) (err error) {

	cli := userPresentMock.NewMockIClient(ctl)
	cli.EXPECT().GetPresentConfigByIdList(context.Background(), gomock.Any(), gomock.Any(), gomock.Any())
	cli.EXPECT().GetPresentConfigByIdList(context.Background(), gomock.Any(), gomock.Any(), gomock.Any())
	UserPresentClient = cli

	return nil
}

func SetupForSearchPresent(ctl *gomock.Controller) (err error) {

	cli := userPresentMock.NewMockIClient(ctl)
	cli.EXPECT().GetPresentConfigList(context.Background())
	cli.EXPECT().GetPresentConfigList(context.Background())
	UserPresentClient = cli

	return nil
}

func SetupMock(ctl *gomock.Controller) error {

	cli := userPresentMock.NewMockIClient(ctl)
	cli.EXPECT().GetPresentConfigList(gomock.Any()).Return(&userpresent.GetPresentConfigListResp{ItemList: []*userpresent.StPresentItemConfig{{
		ItemId:      1505,
		EffectBegin: uint32(time.Now().Add(-1 * time.Hour).Unix()),
		EffectEnd:   uint32(time.Now().Add(1 * time.Hour).Unix()),
		Extend: &userpresent.StPresentItemConfigExtend{
			ItemId:             1505,
			VideoEffectUrl:     nil,
			ShowEffect:         0,
			UnshowBatchOption:  false,
			IsTest:             false,
			FlowId:             0,
			IosExtend:          nil,
			NotifyAll:          false,
			Tag:                7,
			ForceSendable:      false,
			NobilityLevel:      0,
			UnshowPresentShelf: false,
			ShowEffectEnd:      false,
			EffectEndDelay:     false,
			CustomText:         nil,
		},
	},
		{
			ItemId:      144,
			EffectBegin: 1528885200,
			EffectEnd:   1672416000,
			Extend: &userpresent.StPresentItemConfigExtend{
				ItemId:             144,
				VideoEffectUrl:     nil,
				ShowEffect:         0,
				UnshowBatchOption:  false,
				IsTest:             false,
				FlowId:             0,
				IosExtend:          nil,
				NotifyAll:          false,
				Tag:                1,
				ForceSendable:      false,
				NobilityLevel:      0,
				UnshowPresentShelf: false,
				ShowEffectEnd:      true,
				EffectEndDelay:     true,
				CustomText:         nil,
			}},
	}}, nil)
	UserPresentClient = cli

	privilegeCli := presentprivilege.NewMockIClient(ctl)
	privilegeCli.EXPECT().GetGetPrivilegeList(gomock.Any()).Return(&presentprivilege2.GetPrivilegeListResp{
		PrivilegeList: []*presentprivilege2.Privilege{{Id: 1, GiftId: 1505, Condition: []*presentprivilege2.Condition{
			{
				Type: 1,
				Value: &presentprivilege2.ConditionValue{
					Value:     1000,
					Level:     1,
					BeginTime: uint32(time.Now().Add(-1 * time.Hour).Unix()),
					EndTime:   uint32(time.Now().Add(1 * time.Hour).Unix()),
					Seconds:   0,
				},
			},
		}}},
		Total: 1,
	}, nil)
	PrivilegeClient = privilegeCli

	SendImCli = sendim2.NewMockIClient(ctl)
	return nil
}

func SetupTimeApeendMock(ctl *gomock.Controller) error {

	cli := userPresentMock.NewMockIClient(ctl)
	cli.EXPECT().GetPresentConfigList(gomock.Any()).Return(&userpresent.GetPresentConfigListResp{ItemList: []*userpresent.StPresentItemConfig{{
		ItemId:      1505,
		EffectBegin: uint32(time.Now().Add(-1 * time.Hour).Unix()),
		EffectEnd:   uint32(time.Now().Add(1 * time.Hour).Unix()),
		Extend: &userpresent.StPresentItemConfigExtend{
			ItemId:             1505,
			VideoEffectUrl:     nil,
			ShowEffect:         0,
			UnshowBatchOption:  false,
			IsTest:             false,
			FlowId:             0,
			IosExtend:          nil,
			NotifyAll:          false,
			Tag:                7,
			ForceSendable:      false,
			NobilityLevel:      0,
			UnshowPresentShelf: false,
			ShowEffectEnd:      true,
			EffectEndDelay:     true,
			CustomText:         nil,
		},
	}, {
		ItemId:      144,
		EffectBegin: 1528885200,
		EffectEnd:   1672416000,
		Extend: &userpresent.StPresentItemConfigExtend{
			ItemId:             144,
			VideoEffectUrl:     nil,
			ShowEffect:         0,
			UnshowBatchOption:  false,
			IsTest:             false,
			FlowId:             0,
			IosExtend:          nil,
			NotifyAll:          false,
			Tag:                7,
			ForceSendable:      false,
			NobilityLevel:      0,
			UnshowPresentShelf: false,
			ShowEffectEnd:      true,
			EffectEndDelay:     true,
			CustomText:         nil,
		}}, {
		ItemId:      279,
		EffectBegin: 1572510891,
		EffectEnd:   1671379200,
		Extend: &userpresent.StPresentItemConfigExtend{
			ItemId:             279,
			VideoEffectUrl:     nil,
			ShowEffect:         0,
			UnshowBatchOption:  false,
			IsTest:             false,
			FlowId:             0,
			IosExtend:          nil,
			NotifyAll:          false,
			Tag:                7,
			ForceSendable:      false,
			NobilityLevel:      0,
			UnshowPresentShelf: false,
			ShowEffectEnd:      true,
			EffectEndDelay:     true,
			CustomText:         nil,
		}},
	}}, nil)
	UserPresentClient = cli

	privilegeCli := presentprivilege.NewMockIClient(ctl)
	PrivilegeClient = privilegeCli

	SendImCli = sendim2.NewMockIClient(ctl)

	PushCli = push.NewMockIClient(ctl)
	return nil
}
