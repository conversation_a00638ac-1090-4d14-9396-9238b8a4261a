package main

import (
	"context"
	"golang.52tt.com/pkg/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	startup "gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server"
	pb "golang.52tt.com/protocol/services/presentextraconf"
	"golang.52tt.com/services/present-extra-conf/internal/conf"
	"golang.52tt.com/services/present-extra-conf/internal/server"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server" // 兼容tyr公共库
)

func main() {

	var (
		svr *server.PresentExtraConfServer
		cfg = &conf.ServiceConfigT{}
		err error
	)

	if err := startup.NewServer("present-extra-conf", cfg).
		AddGrpcServer(grpc.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				if svr, err = server.NewPresentExtraConfServer(ctx, cfg); err != nil {
					return err
				}
				pb.RegisterPresentExtraConfServer(s, svr)

				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
