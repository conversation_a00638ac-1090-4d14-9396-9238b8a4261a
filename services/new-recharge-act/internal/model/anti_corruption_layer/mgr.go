package anti_corruption_layer

//go:generate quicksilver-cli test interface ../anti_corruption_layer
//go:generate mockgen -destination=./mocks/anti_corruption_layer.go -package=mocks golang.52tt.com/services/new-recharge-act/internal/model/anti_corruption_layer IMgr

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	push "golang.52tt.com/clients/push-notification/v2"
	sendIm "golang.52tt.com/clients/sendim"
	"golang.52tt.com/pkg/protocol"
	pushPb "golang.52tt.com/protocol/app/push"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	"time"
	"golang.52tt.com/services/new-recharge-act/internal/conf"
	sendim "golang.52tt.com/protocol/services/sendimsvr"
	"golang.52tt.com/protocol/app/tt_rev_common_logic"
	pbAward "golang.52tt.com/protocol/services/risk-control/award-center"
	award_center "golang.52tt.com/clients/award-center"
	backpacksender "golang.52tt.com/clients/backpack-sender"
	"golang.52tt.com/protocol/common/status"
	butils "golang.52tt.com/services/risk-control/backpack-sender/utils"
	backpackSenderPB "golang.52tt.com/protocol/services/backpacksender"
)

type Mgr struct {
    pushCli        push.IClient
    sendImCli      sendIm.IClient
	awardCenter    award_center.IClient
	backpackSenderCli backpacksender.IClient

    bc       conf.IBusinessConfManager
    shutDown chan struct{}
}

func NewMgr(bc conf.IBusinessConfManager) (*Mgr, error) {
    pushCli, _ := push.NewClient()
    sendImCli := sendIm.NewClient()
	awardCenter, _ := award_center.NewClient()
	backpackSenderCli, _ := backpacksender.NewClient()
    m := &Mgr{
        bc:             bc,
        pushCli:        pushCli,
        sendImCli:      sendImCli,
		awardCenter:         awardCenter,
		backpackSenderCli:   backpackSenderCli,

        shutDown:       make(chan struct{}),
    }

    return m, nil
}

func (m *Mgr) Stop() {
    close(m.shutDown)
}

func (m *Mgr) PushFirstRechargeFinImMsg(ctx context.Context, uid uint32, content string) error {
	return m.SendIMMsg(ctx, []uint32{uid}, content)
}

func (m *Mgr) PushFirstRechargeFinStatusPush(ctx context.Context, uid uint32) error {
	opt := &tt_rev_common_logic.NewFirstRechargeFinNotify{
		Uid: uid,
	}

	err := m.pushMsgToUsers(ctx, []uint32{uid}, pushPb.PushMessage_NewFirstRechargeFinPush, opt)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushUserPkInfoChangeNotify pushMsgToUsers err: %s", err.Error())
		return err
	}

	log.InfoWithCtx(ctx, "PushUserPkInfoChangeNotify pushMsgToUsers success. uid:%v", uid)
	return nil
}

func (m *Mgr) pushMsgToUsers(ctx context.Context, uidList []uint32, cmdTy pushPb.PushMessage_CMD_TYPE, opt proto.MessageV1) error {
    msg, err := proto.Marshal(opt)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushUserPkInfoChangeNotify marshal err: %s, %+v", err.Error(), opt)
        return err
    }

    pushMsg := pushPb.PushMessage{
        Cmd:     uint32(cmdTy),
        Content: msg,
        SeqId:   uint32(time.Now().UnixNano()),
    }

    mb, err := pushMsg.Marshal()
    if nil != err {
        log.Errorf("PushUserPkInfoChangeNotify Marshal msg failed:%+v", err)
        return err
    }

    err = m.pushCli.PushToUsers(ctx, uidList, &pushPB.CompositiveNotification{
        Sequence:           uint32(time.Now().Unix()),
        TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
        TerminalTypePolicy: push.DefaultPolicy,
        AppId:              0,
        ProxyNotification: &pushPB.ProxyNotification{
            Type:      uint32(pushPB.ProxyNotification_PUSH),
            Payload:   mb,
            PushLabel: fmt.Sprintf("CMD_%s", cmdTy.String()),
        },
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "pushMsgToUsers PushToUsers err: %s", err.Error())
        return err
    }

    return nil
}

// SendIMMsg 发送TT助手消息
func (m *Mgr) SendIMMsg(ctx context.Context, toUidList []uint32, content string) (err error) {
	msg := &sendim.SendSyncReq{
		Sender: &sendim.Sender{
			Type: int32(sendim.Sender_User),
			Id:   10000,
		},
		Receiver: &sendim.Receiver{
			Type:   int32(sendim.Receiver_User),
			IdList: toUidList,
		},
		Msg: &sendim.ImMsg{
			Content: &sendim.Content{
				Type:       int32(sendim.Content_Text),
				TextNormal: &sendim.ImTextNormal{Content: content},
			},
			AppPlatform: "all",
			AppName:     "",
			ExpiredAt:   uint32(time.Now().Unix()) + 86400,
		},
		WithNotify: true,
	}

	_, err = m.sendImCli.SendSync(ctx, msg)
	if err != nil {
		log.Errorf("SendIMMsg err: %s,toUid:%v, content:%s", err.Error(), toUidList, content)
		return err
	}

	log.InfoWithCtx(ctx, "SendIMMsg success. msg:%+v, uid:%v", msg, toUidList)
	return nil
}

// AwardPackage 发放包裹
func (m *Mgr) AwardPackage(ctx context.Context, uid, packId, amount, outsideTs uint32, orderId string) error {
	appId, sign := m.bc.GetBackSenderConf()
	log.Infof("info.orderId:%v, sign:%s", orderId, sign)
	_, err := m.backpackSenderCli.SendBackpackWithRiskControl(ctx, &backpackSenderPB.SendBackpackWithRiskControlReq{
		BusinessId:  appId,
		BackpackId:  packId,
		ReceiveUid:  uid,
		BackpackCnt: amount,
		ServerTime:  int64(outsideTs),
		OrderId:     orderId,
		Ciphertext:  butils.AESEncrypt([]byte(orderId), []byte(sign)),
		SourceAppId: "新首充活动",
	})
	if err != nil {
		if err.Code() == status.ErrRiskControlAwardCenterOrderExist ||
			err.Code() == status.ErrRiskControlBackpackDuplicateOrderid {
			// 订单重复
			log.Debugf("AwardPackage DuplicateOrderId.orderId:%s, uid:%d, packId:%d", orderId, uid, packId)
			return nil
		}
		log.Errorf("AwardPackage fail to SendBackpackWithRiskControl. uid:%d, orderId:%s, packId:%d, err:%v", uid, orderId, packId, err)
		return err
	}

	log.InfoWithCtx(ctx, "AwardPackage orderId:%s, uid:%d, packId:%d, amount:%d, outsideTs:%d", orderId, uid, packId, amount, outsideTs)
	return nil
}

// AwardDress 发放装扮
func (m *Mgr) AwardDress(ctx context.Context, uid, holdingDay, outsideTs, dressType uint32, giftId, orderId string) error {
	req := &pbAward.AwardReq{
		OrderId:    orderId,
		TargetUid:  uid,
		BusinessId: m.bc.GetDressSenderAppId(),
		GiftId:     giftId,
		GiftType:   dressType,
		HoldingDay: holdingDay,
		OutsideTs:  outsideTs,
		UserDecorationExtra: &pbAward.UserDecorationExtra{
			Version: "v1", // 写死版本号
		},
	}

	err := m.awardCenter.Award(ctx, req)
	if err != nil {
		if err.Code() == status.ErrRiskControlAwardCenterOrderExist {
			// 订单重复
			log.WarnWithCtx(ctx, "AwardDress DuplicateOrderId.orderId:%s, uid:%d, holdingDay:%d, outsideTs:%d, giftId:%s, dressType:%d", orderId, uid, holdingDay, outsideTs, giftId, dressType)
			return nil
		}
		log.Errorf("AwardDress fail to Award. uid:%d, holdingDay:%d, outsideTs:%d, giftId:%s, orderId:%s, err:%v", uid, holdingDay, outsideTs, giftId, orderId, err)
		return err
	}

	log.InfoWithCtx(ctx, "AwardDress success. uid:%d,dressType:%d holdingDay:%d, outsideTs:%d, giftId:%s, orderId:%s", uid, dressType, holdingDay, outsideTs, giftId, orderId)
	return nil
}