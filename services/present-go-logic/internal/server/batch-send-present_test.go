package server

import (
	"context"
	"fmt"
	"github.com/golang/mock/gomock"
	account "golang.52tt.com/clients/account-go"
	account2 "golang.52tt.com/clients/mocks/account-go"
	antiMock "golang.52tt.com/clients/mocks/anti-go"
	channel2 "golang.52tt.com/clients/mocks/channel"
	channelMic "golang.52tt.com/clients/mocks/channelmic"
	channelolMock "golang.52tt.com/clients/mocks/channelol"
	cooldownMock "golang.52tt.com/clients/mocks/cooldown"
	entertainmentrecommendbackMock "golang.52tt.com/clients/mocks/entertainmentrecommendback"
	guildMock "golang.52tt.com/clients/mocks/guild"
	iopMock "golang.52tt.com/clients/mocks/iop-proxy"
	nobilityMock "golang.52tt.com/clients/mocks/nobility"
	presentExtraMock "golang.52tt.com/clients/mocks/present-extra-conf"
	pmMock "golang.52tt.com/clients/mocks/present-middleware"
	presentPrivilegeMock "golang.52tt.com/clients/mocks/present-privilege"
	userprofileapiMock "golang.52tt.com/clients/mocks/user-profile-api"
	presentMock "golang.52tt.com/clients/mocks/userpresent"
	usualDeviceMock "golang.52tt.com/clients/mocks/usual-device"
	youknowwho2 "golang.52tt.com/clients/mocks/you-know-who"
	present_extra_conf "golang.52tt.com/clients/present-extra-conf"
	userPresent "golang.52tt.com/clients/userpresent"
	userpresent_go "golang.52tt.com/clients/userpresent-go"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	basePb "golang.52tt.com/protocol/app"
	pb "golang.52tt.com/protocol/app/present-go-logic"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	entertainmentRecommendBack "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	"golang.52tt.com/protocol/services/mocks"
	present_middleware "golang.52tt.com/protocol/services/present-middleware"
	"golang.52tt.com/protocol/services/presentextraconf"
	richer_birthday "golang.52tt.com/protocol/services/richer-birthday"
	"golang.52tt.com/protocol/services/userpresent"
	youknowwho3 "golang.52tt.com/protocol/services/youknowwho"
	"golang.52tt.com/services/present-go-logic/internal/conf"
	"golang.52tt.com/services/present-go-logic/internal/rpc"
	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
	"reflect"
	"sync"
	"testing"
	"time"
)

var (
	businessConfig      *conf.BusinessConfManager
	sendPresentConfig   *conf.SendPresentConfManager
	constellationUrl    string
	presentExtraConfCli present_extra_conf.IClient
	presentFloatCache   []*pb.PresentFloatLayer
	presentFlashCache   []*pb.PresentFlashEffect
	flashConfigCache    []*pb.FlashEffectConfig
	presentConfigCache  map[uint32]*userpresent.StPresentItemConfig
	lastUpdateTime      uint32
	imPresentConfig     *conf.ImPresentConf
	drawPresentConfig   *conf.DrawGameConf
)

func init() {
	sendPresentConfig = conf.NewSendPresentConf(context.Background())
	drawPresentConfig = conf.NewDrawConfigConf(context.Background())
	imPresentConfig = conf.NewImPresentConf(context.Background())
	businessConfig = &conf.BusinessConfManager{}
	businessConfig.Reload(context.Background(), "./test-config.json")
}

//func TestNewPresentGoLogic(t *testing.T) {
//	type args struct {
//		ctx    context.Context
//		conf   config.Configer
//		tracer opentracing.Tracer
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    *PresentGoLogic_
//		wantErr bool
//	}{}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := NewPresentGoLogic(tt.args.ctx, tt.args.conf, tt.args.tracer)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("NewPresentGoLogic() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("NewPresentGoLogic() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestPresentGoLogic__ChannelPresentSend(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	_ = client.SetupForChannelSend(ctl)

	mockBirth := mocks.NewMockRicherBirthdayServiceClient(ctl)
	mockBirth.EXPECT().CheckIfCouldSendRicherBirthdayGift(gomock.Any(), gomock.Any(), gomock.Any()).Return(&richer_birthday.CheckIfCouldSendRicherBirthdayGiftResponse{CouldSend: true}, nil)
	client.RichBirthdayCli = mockBirth

	ctx := context.Background()
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: 2202538})

	type fields struct {
		presentCli          userpresent_go.PresentClientWrapper
		businessConf        *conf.BusinessConfManager
		sendPresentConf     *conf.SendPresentConfManager
		constellationUrl    string
		presentExtraConfCli present_extra_conf.IClient
		presentFloatCache   []*pb.PresentFloatLayer
		presentFlashCache   []*pb.PresentFlashEffect
		flashConfigCache    []*pb.FlashEffectConfig
		lastUpdateTime      uint32
	}
	type args struct {
		ctx context.Context
		in  *presentPB_.SendPresentReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *presentPB_.SendPresentResp
		wantErr bool
	}{
		{
			name: "ChannelPresentSend",
			fields: fields{
				presentCli:          client.PresentCli,
				businessConf:        businessConfig,
				sendPresentConf:     sendPresentConfig,
				constellationUrl:    "",
				presentExtraConfCli: presentExtraConfCli,
				presentFloatCache:   presentFloatCache,
				presentFlashCache:   presentFlashCache,
				flashConfigCache:    flashConfigCache,
				lastUpdateTime:      0,
			}, args: args{
			ctx: ctx,
			in: &presentPB_.SendPresentReq{
				BaseReq: &basePb.BaseReq{
					AppId:          0,
					MarketId:       0,
					AntispamToken:  nil,
					VerifyCodeInfo: nil,
					AntispamInfo:   nil,
					VerifyInfo:     nil,
					RequestId:      []byte("2202538"),
					FaceAuthInfo:   nil,
				},
				TargetUid:        2414206,
				ItemId:           1,
				ChannelId:        0,
				ConfigUpdateTime: 0,
				Count:            1,
				SendSource:       0,
				ItemSource:       0,
				SourceId:         0,
				SendType:         0,
				DrawPresentPic:   nil,
			},
		}, wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentGoLogic_{
				businessConf:        tt.fields.businessConf,
				sendPresentConf:     tt.fields.sendPresentConf,
				constellationUrl:    tt.fields.constellationUrl,
				presentExtraConfCli: tt.fields.presentExtraConfCli,
				presentFlashCache:   tt.fields.presentFlashCache,
				flashConfigCache:    tt.fields.flashConfigCache,
				lastUpdateTime:      tt.fields.lastUpdateTime,
			}
			resp, err := s.ChannelPresentSend(tt.args.ctx, tt.args.in)
			fmt.Println(resp, err)
		})
	}
}

func TestPresentGoLogic__FillExtraConfigCache(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	presentExtraCli := presentExtraMock.NewMockIClient(ctl)
	presentExtraCli.EXPECT().GetPresentFloatLayer(gomock.Any(), gomock.Any()).Return(&presentextraconf.GetPresentFloatLayerResp{
		LayerInfos: []*presentextraconf.PresentFloatInfo{{
			LayerInfo: &presentextraconf.PresentFloatLayer{
				GiftId:        1,
				FloatImageUrl: "test",
				JumpUrl:       "test",
				IsActivityUrl: false,
				EffectBegin:   0,
				EffectEnd:     uint32(time.Now().Add(time.Hour).Unix()),
				Operator:      "",
				EffectStatus:  2,
				UpdateTime:    0,
			},
			PresentConfig: &presentextraconf.PresentBaseConfig{
				GiftId:     1,
				GiftName:   "",
				PriceValue: 1,
				PriceType:  1,
				GiftImage:  "",
			},
		}},
		Total:          1,
		LastUpdateTime: 1,
	}, nil)
	presentExtraCli.EXPECT().GetPresentFlashEffect(gomock.Any(), gomock.Any()).Return(&presentextraconf.GetPresentFlashEffectResp{
		PresentEffects: []*presentextraconf.PresentFlashInfo{
			{
				FlashInfo: &presentextraconf.FlashEffectConfig{
					FlashId:    1,
					FlashName:  "",
					FlashUrl:   "",
					FlashMd5:   "",
					Operator:   "",
					CreateTime: 0,
					UpdateTime: 0,
				},
				PresentConfig: &presentextraconf.PresentBaseConfig{
					GiftId:     1,
					GiftName:   "",
					PriceValue: 1,
					PriceType:  1,
					GiftImage:  "",
				},
				EffectBegin:  lastUpdateTime,
				EffectEnd:    uint32(time.Now().Add(time.Hour).Unix()),
				EffectStatus: 2,
				Operator:     "",
				CreateTime:   uint32(time.Now().Unix()),
			},
		},
		Total:          1,
		LastUpdateTime: 1,
	}, nil)

	presentExtraConfCli = presentExtraCli

	presentCli := presentMock.NewMockIClient(ctl)
	presentCli.EXPECT().GetPresentConfigListV2ByUpdateTime(gomock.Any(), gomock.Any())
	client.PresentCli = presentCli

	type fields struct {
		presentCli          userpresent_go.PresentClientWrapper
		businessConf        *conf.BusinessConfManager
		sendPresentConf     *conf.SendPresentConfManager
		constellationUrl    string
		presentExtraConfCli present_extra_conf.IClient
		presentFloatCache   []*pb.PresentFloatLayer
		presentFlashCache   []*pb.PresentFlashEffect
		flashConfigCache    []*pb.FlashEffectConfig
		lastUpdateTime      uint32
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{name: "test",
			fields: fields{
				presentCli:          client.PresentCli,
				businessConf:        businessConfig,
				sendPresentConf:     sendPresentConfig,
				constellationUrl:    "",
				presentExtraConfCli: presentExtraConfCli,
				presentFloatCache:   presentFloatCache,
				presentFlashCache:   presentFlashCache,
				flashConfigCache:    flashConfigCache,
				lastUpdateTime:      0,
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentGoLogic_{
				businessConf:        tt.fields.businessConf,
				sendPresentConf:     tt.fields.sendPresentConf,
				constellationUrl:    tt.fields.constellationUrl,
				presentExtraConfCli: tt.fields.presentExtraConfCli,
				presentFlashCache:   tt.fields.presentFlashCache,
				flashConfigCache:    tt.fields.flashConfigCache,
				lastUpdateTime:      tt.fields.lastUpdateTime,
				presentConfigCache:  map[uint32]*userpresent.StPresentItemConfig{},
				mapLock:             sync.RWMutex{},
			}
			s.FillExtraConfigCache()
		})
	}
}

func TestPresentGoLogic__GetNeedPopUpPresentList(t *testing.T) {
	type fields struct {
		presentCli          *userPresent.Client
		businessConf        *conf.BusinessConfManager
		sendPresentConf     *conf.SendPresentConfManager
		constellationUrl    string
		presentExtraConfCli *present_extra_conf.Client
		presentFloatCache   []*pb.PresentFloatLayer
		presentFlashCache   []*pb.PresentFlashEffect
		flashConfigCache    []*pb.FlashEffectConfig
		lastUpdateTime      uint32
	}
	type args struct {
		ctx context.Context
		in  *pb.GetNeedPopUpPresentListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.GetNeedPopUpPresentListResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentGoLogic_{
				businessConf:        tt.fields.businessConf,
				sendPresentConf:     tt.fields.sendPresentConf,
				constellationUrl:    tt.fields.constellationUrl,
				presentExtraConfCli: tt.fields.presentExtraConfCli,
				presentFlashCache:   tt.fields.presentFlashCache,
				flashConfigCache:    tt.fields.flashConfigCache,
				lastUpdateTime:      tt.fields.lastUpdateTime,
			}
			gotOut, err := s.GetNeedPopUpPresentList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNeedPopUpPresentList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("GetNeedPopUpPresentList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestPresentGoLogic__GetPresentExtraConfig(t *testing.T) {
	ctx := context.Background()
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: 2202538})

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	presentExtraCli := presentExtraMock.NewMockIClient(ctl)
	presentExtraCli.EXPECT().GetPresentEffectTime(gomock.Any(), gomock.Any()).Return(&presentextraconf.GetPresentEffectTimeResp{
		PresentEffectTimeInfos: []*presentextraconf.PresentEffectTime{{GiftId: 1, EffectEnd: uint32(time.Now().Unix()), EffectInfo: &presentextraconf.PresentEffectTimeInfo{
			NowCount:              1,
			NextLevelSendCount:    0,
			NextLevelDayCount:     0,
			MaxLevelSendCount:     0,
			IsMaxLevel:            false,
			NoLimitExpireDayCount: 0,
			LastSendTs:            0,
			MaxLevelDayCount:      0,
			EffectEndOnShelf:      0,
			NowLevelDayCount:      0,
			NoticeNoLimitExpire:   false,
		}}},
	}, nil)

	client.PresentExtraConfigCli = presentExtraCli

	privilegeMock := presentPrivilegeMock.NewMockIClient(ctl)
	privilegeMock.EXPECT().GetTreasurePrivilege(gomock.Any(), gomock.Any())
	client.PresentPrivilegeCli = privilegeMock

	type fields struct {
		presentCli          *userPresent.Client
		businessConf        *conf.BusinessConfManager
		sendPresentConf     *conf.SendPresentConfManager
		constellationUrl    string
		presentExtraConfCli *present_extra_conf.Client
		presentFloatCache   []*pb.PresentFloatLayer
		presentFlashCache   []*pb.PresentFlashEffect
		flashConfigCache    []*pb.FlashEffectConfig
		lastUpdateTime      uint32
	}
	type args struct {
		c   context.Context
		req *pb.GetPresentExtraConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPresentExtraConfigResp
		wantErr bool
	}{
		{
			name:   "test",
			fields: fields{},
			args: args{
				c:   ctx,
				req: &pb.GetPresentExtraConfigReq{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentGoLogic_{
				businessConf:        tt.fields.businessConf,
				sendPresentConf:     tt.fields.sendPresentConf,
				constellationUrl:    tt.fields.constellationUrl,
				presentExtraConfCli: tt.fields.presentExtraConfCli,
				presentFlashCache:   tt.fields.presentFlashCache,
				flashConfigCache:    tt.fields.flashConfigCache,
				lastUpdateTime:      tt.fields.lastUpdateTime,
			}
			got, err := s.GetPresentExtraConfig(tt.args.c, tt.args.req)
			fmt.Println(got, err)
		})
	}
}

func TestPresentGoLogic__GetUserActPresentArea(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	presentClient := presentMock.NewMockIClient(ctl)
	presentClient.EXPECT().GetUserPresentSummaryByItemList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
		&userpresent.GetUserPresentSummaryByItemListResp{SummaryList: []*userpresent.StUserPresentSummary{{
			Uid:    2202538,
			ItemId: 1,
			Count:  1,
		}}}, nil)
	client.PresentCli = presentClient

	ctx := context.Background()
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: 2202538})

	type fields struct {
		presentCli          userpresent_go.PresentClientWrapper
		businessConf        *conf.BusinessConfManager
		sendPresentConf     *conf.SendPresentConfManager
		constellationUrl    string
		presentExtraConfCli present_extra_conf.IClient
		presentFloatCache   []*pb.PresentFloatLayer
		presentFlashCache   []*pb.PresentFlashEffect
		flashConfigCache    []*pb.FlashEffectConfig
		lastUpdateTime      uint32
	}
	type args struct {
		ctx context.Context
		in  *pb.GetUserActPresentAreaReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.GetUserActPresentAreaResp
		wantErr bool
	}{
		{name: "test",
			fields: fields{
				presentCli:          client.PresentCli,
				businessConf:        businessConfig,
				sendPresentConf:     sendPresentConfig,
				constellationUrl:    "",
				presentExtraConfCli: presentExtraConfCli,
				presentFloatCache:   presentFloatCache,
				presentFlashCache:   presentFlashCache,
				flashConfigCache:    flashConfigCache,
				lastUpdateTime:      0,
			},
			args: args{ctx: ctx, in: &pb.GetUserActPresentAreaReq{
				Uid: 2202538,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentGoLogic_{
				businessConf:        tt.fields.businessConf,
				sendPresentConf:     tt.fields.sendPresentConf,
				constellationUrl:    tt.fields.constellationUrl,
				presentExtraConfCli: tt.fields.presentExtraConfCli,
				presentFlashCache:   tt.fields.presentFlashCache,
				flashConfigCache:    tt.fields.flashConfigCache,
				lastUpdateTime:      tt.fields.lastUpdateTime,
			}
			gotOut, err := s.GetUserActPresentArea(tt.args.ctx, tt.args.in)
			fmt.Println(gotOut, err)
			//if (err != nil) != tt.wantErr {
			//	t.Errorf("GetUserActPresentArea() error = %v, wantErr %v", err, tt.wantErr)
			//	return
			//}
			//if !reflect.DeepEqual(gotOut, tt.wantOut) {
			//	t.Errorf("GetUserActPresentArea() gotOut = %v, want %v", gotOut, tt.wantOut)
			//}
		})
	}
}

func TestPresentGoLogic__InitTimer(t *testing.T) {
	type fields struct {
		presentCli          *userPresent.Client
		businessConf        *conf.BusinessConfManager
		sendPresentConf     *conf.SendPresentConfManager
		constellationUrl    string
		presentExtraConfCli *present_extra_conf.Client
		presentFloatCache   []*pb.PresentFloatLayer
		presentFlashCache   []*pb.PresentFlashEffect
		flashConfigCache    []*pb.FlashEffectConfig
		lastUpdateTime      uint32
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentGoLogic_{
				businessConf:        tt.fields.businessConf,
				sendPresentConf:     tt.fields.sendPresentConf,
				constellationUrl:    tt.fields.constellationUrl,
				presentExtraConfCli: tt.fields.presentExtraConfCli,
				presentFlashCache:   tt.fields.presentFlashCache,
				flashConfigCache:    tt.fields.flashConfigCache,
				lastUpdateTime:      tt.fields.lastUpdateTime,
			}
			s.InitTimer()
		})
	}
}

func TestPresentGoLogic__PresentBatchSend(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	_ = client.SetupForChannelSend(ctl)

	channelMicMock := channelMic.NewMockIClient(ctl)
	channelMicMock.EXPECT().GetMicrList(gomock.Any(), gomock.Any(), gomock.Any())

	client.ChannelMicCli = channelMicMock
	ctx := context.Background()
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: 2202538})

	mockBirth := mocks.NewMockRicherBirthdayServiceClient(ctl)
	mockBirth.EXPECT().CheckIfCouldSendRicherBirthdayGift(gomock.Any(), gomock.Any(), gomock.Any()).Return(&richer_birthday.CheckIfCouldSendRicherBirthdayGiftResponse{CouldSend: true}, nil)
	client.RichBirthdayCli = mockBirth

	type fields struct {
		presentCli          userpresent_go.PresentClientWrapper
		businessConf        *conf.BusinessConfManager
		sendPresentConf     *conf.SendPresentConfManager
		constellationUrl    string
		presentExtraConfCli present_extra_conf.IClient
		presentFloatCache   []*pb.PresentFloatLayer
		presentFlashCache   []*pb.PresentFlashEffect
		flashConfigCache    []*pb.FlashEffectConfig
		lastUpdateTime      uint32
	}
	type args struct {
		ctx context.Context
		in  *presentPB_.BatchSendPresentReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *presentPB_.BatchSendPresentResp
		wantErr bool
	}{
		{
			name: "ChannelPresentSend",
			fields: fields{
				presentCli:          client.PresentCli,
				businessConf:        businessConfig,
				sendPresentConf:     sendPresentConfig,
				constellationUrl:    "",
				presentExtraConfCli: presentExtraConfCli,
				presentFloatCache:   presentFloatCache,
				presentFlashCache:   presentFlashCache,
				flashConfigCache:    flashConfigCache,
				lastUpdateTime:      0,
			}, args: args{
			ctx: ctx,
			in: &presentPB_.BatchSendPresentReq{
				BaseReq: &basePb.BaseReq{
					AppId:          0,
					MarketId:       0,
					AntispamToken:  nil,
					VerifyCodeInfo: nil,
					AntispamInfo:   nil,
					VerifyInfo:     nil,
					RequestId:      []byte("2202538"),
					FaceAuthInfo:   nil,
				},
				ItemId:         1,
				ChannelId:      0,
				Count:          1,
				SendSource:     0,
				ItemSource:     0,
				SourceId:       0,
				SendType:       0,
				DrawPresentPic: nil,
				UidList:        []uint32{2414206},
			},
		}, wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentGoLogic_{
				businessConf:        tt.fields.businessConf,
				sendPresentConf:     tt.fields.sendPresentConf,
				constellationUrl:    tt.fields.constellationUrl,
				presentExtraConfCli: tt.fields.presentExtraConfCli,
				presentFlashCache:   tt.fields.presentFlashCache,
				flashConfigCache:    tt.fields.flashConfigCache,
				lastUpdateTime:      tt.fields.lastUpdateTime,
			}
			gotOut, err := s.PresentBatchSend(tt.args.ctx, tt.args.in)
			fmt.Println(gotOut, err)
			//if (err != nil) != tt.wantErr {
			//	t.Errorf("PresentBatchSend() error = %v, wantErr %v", err, tt.wantErr)
			//	return
			//}
			//if !reflect.DeepEqual(gotOut, tt.wantOut) {
			//	t.Errorf("PresentBatchSend() gotOut = %v, want %v", gotOut, tt.wantOut)
			//}
		})
	}
}

func TestPresentGoLogic__CommonSendPresent(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	_ = client.SetupForChannelSend(ctl)
	ctx := context.Background()
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: 2202538})

	channelTagCli := entertainmentrecommendbackMock.NewMockIClient(ctl)
	channelTagCli.EXPECT().GetChannelTag(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entertainmentRecommendBack.GetChannelTagResp{}, nil)
	client.EntertainmentRecommendBackCli = channelTagCli

	mockBirth := mocks.NewMockRicherBirthdayServiceClient(ctl)
	mockBirth.EXPECT().CheckIfCouldSendRicherBirthdayGift(gomock.Any(), gomock.Any(), gomock.Any()).Return(&richer_birthday.CheckIfCouldSendRicherBirthdayGiftResponse{CouldSend: true}, nil)
	client.RichBirthdayCli = mockBirth

	type fields struct {
		presentCli          userpresent_go.PresentClientWrapper
		businessConf        *conf.BusinessConfManager
		sendPresentConf     *conf.SendPresentConfManager
		constellationUrl    string
		presentExtraConfCli present_extra_conf.IClient
		presentFloatCache   []*pb.PresentFloatLayer
		presentFlashCache   []*pb.PresentFlashEffect
		flashConfigCache    []*pb.FlashEffectConfig
		lastUpdateTime      uint32
	}
	type args struct {
		ctx context.Context
		in  *pb.CommonSendPresentReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.CommonSendPresentResp
		wantErr bool
	}{
		{
			name: "ChannelPresentSend",
			fields: fields{
				presentCli:          client.PresentCli,
				businessConf:        businessConfig,
				sendPresentConf:     sendPresentConfig,
				constellationUrl:    "",
				presentExtraConfCli: presentExtraConfCli,
				presentFloatCache:   presentFloatCache,
				presentFlashCache:   presentFlashCache,
				flashConfigCache:    flashConfigCache,
				lastUpdateTime:      0,
			}, args: args{
			ctx: ctx,
			in: &pb.CommonSendPresentReq{
				BaseReq: &basePb.BaseReq{
					AppId:          0,
					MarketId:       0,
					AntispamToken:  nil,
					VerifyCodeInfo: nil,
					AntispamInfo:   nil,
					VerifyInfo:     nil,
					RequestId:      []byte("2202538"),
					FaceAuthInfo:   nil,
				},
				ItemId:         1,
				ChannelId:      0,
				Count:          1,
				SendSource:     0,
				ItemSource:     0,
				SourceId:       0,
				SendType:       0,
				DrawPresentPic: nil,
				UidList:        []uint32{2414206},
			},
		}, wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentGoLogic_{
				businessConf:        tt.fields.businessConf,
				sendPresentConf:     tt.fields.sendPresentConf,
				constellationUrl:    tt.fields.constellationUrl,
				presentExtraConfCli: tt.fields.presentExtraConfCli,
				presentFlashCache:   tt.fields.presentFlashCache,
				flashConfigCache:    tt.fields.flashConfigCache,
				lastUpdateTime:      tt.fields.lastUpdateTime,
			}
			gotOut, err := s.CommonSendPresent(tt.args.ctx, tt.args.in)
			fmt.Println(gotOut, err)
			//if (err != nil) != tt.wantErr {
			//	t.Errorf("PresentBatchSend() error = %v, wantErr %v", err, tt.wantErr)
			//	return
			//}
			//if !reflect.DeepEqual(gotOut, tt.wantOut) {
			//	t.Errorf("PresentBatchSend() gotOut = %v, want %v", gotOut, tt.wantOut)
			//}
		})
	}
}

func TestPresentGoLogic__CommonSendPresentNobility(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	_ = SetupForChannelSend(ctl)
	ctx := context.Background()
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: 2202538})

	channelTagCli := entertainmentrecommendbackMock.NewMockIClient(ctl)
	channelTagCli.EXPECT().GetChannelTag(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entertainmentRecommendBack.GetChannelTagResp{}, nil)
	client.EntertainmentRecommendBackCli = channelTagCli

	mockBirth := mocks.NewMockRicherBirthdayServiceClient(ctl)
	mockBirth.EXPECT().CheckIfCouldSendRicherBirthdayGift(gomock.Any(), gomock.Any(), gomock.Any()).Return(&richer_birthday.CheckIfCouldSendRicherBirthdayGiftResponse{CouldSend: true}, nil)
	client.RichBirthdayCli = mockBirth

	type fields struct {
		presentCli          userpresent_go.PresentClientWrapper
		businessConf        *conf.BusinessConfManager
		sendPresentConf     *conf.SendPresentConfManager
		constellationUrl    string
		presentExtraConfCli present_extra_conf.IClient
		presentFloatCache   []*pb.PresentFloatLayer
		presentFlashCache   []*pb.PresentFlashEffect
		flashConfigCache    []*pb.FlashEffectConfig
		lastUpdateTime      uint32
	}
	type args struct {
		ctx context.Context
		in  *pb.CommonSendPresentReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.CommonSendPresentResp
		wantErr bool
	}{
		{
			name: "ChannelPresentSend",
			fields: fields{
				presentCli:          client.PresentCli,
				businessConf:        businessConfig,
				sendPresentConf:     sendPresentConfig,
				constellationUrl:    "",
				presentExtraConfCli: presentExtraConfCli,
				presentFloatCache:   presentFloatCache,
				presentFlashCache:   presentFlashCache,
				flashConfigCache:    flashConfigCache,
				lastUpdateTime:      0,
			}, args: args{
			ctx: ctx,
			in: &pb.CommonSendPresentReq{
				BaseReq: &basePb.BaseReq{
					AppId:          0,
					MarketId:       0,
					AntispamToken:  nil,
					VerifyCodeInfo: nil,
					AntispamInfo:   nil,
					VerifyInfo:     nil,
					RequestId:      []byte("2202538"),
					FaceAuthInfo:   nil,
				},
				ItemId:         1,
				ChannelId:      0,
				Count:          1,
				SendSource:     0,
				ItemSource:     0,
				SourceId:       0,
				SendType:       0,
				DrawPresentPic: nil,
				UidList:        []uint32{2414206},
			},
		}, wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentGoLogic_{
				businessConf:        tt.fields.businessConf,
				sendPresentConf:     tt.fields.sendPresentConf,
				constellationUrl:    tt.fields.constellationUrl,
				presentExtraConfCli: tt.fields.presentExtraConfCli,
				presentFlashCache:   tt.fields.presentFlashCache,
				flashConfigCache:    tt.fields.flashConfigCache,
				lastUpdateTime:      tt.fields.lastUpdateTime,
			}
			gotOut, err := s.CommonSendPresent(tt.args.ctx, tt.args.in)
			fmt.Println(gotOut, err)
			//if (err != nil) != tt.wantErr {
			//	t.Errorf("PresentBatchSend() error = %v, wantErr %v", err, tt.wantErr)
			//	return
			//}
			//if !reflect.DeepEqual(gotOut, tt.wantOut) {
			//	t.Errorf("PresentBatchSend() gotOut = %v, want %v", gotOut, tt.wantOut)
			//}
		})
	}
}

func TestPresentGoLogic__ShutDown(t *testing.T) {
	type fields struct {
		presentCli          *userPresent.Client
		businessConf        *conf.BusinessConfManager
		sendPresentConf     *conf.SendPresentConfManager
		constellationUrl    string
		presentExtraConfCli *present_extra_conf.Client
		presentFloatCache   []*pb.PresentFloatLayer
		presentFlashCache   []*pb.PresentFlashEffect
		flashConfigCache    []*pb.FlashEffectConfig
		lastUpdateTime      uint32
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentGoLogic_{
				businessConf:        tt.fields.businessConf,
				sendPresentConf:     tt.fields.sendPresentConf,
				constellationUrl:    tt.fields.constellationUrl,
				presentExtraConfCli: tt.fields.presentExtraConfCli,
				presentFlashCache:   tt.fields.presentFlashCache,
				flashConfigCache:    tt.fields.flashConfigCache,
				lastUpdateTime:      tt.fields.lastUpdateTime,
			}
			s.ShutDown()
		})
	}
}

func Test_fillBatchResp(t *testing.T) {
	type args struct {
		in   *presentPB_.BatchSendPresentReq
		out  *presentPB_.BatchSendPresentResp
		resp *present_middleware.SendPresentResp
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fillBatchResp(tt.args.in, tt.args.out, tt.args.resp)
		})
	}
}

func Test_fillSendResp(t *testing.T) {
	type args struct {
		in   *presentPB_.SendPresentReq
		out  *presentPB_.SendPresentResp
		resp *present_middleware.SendPresentResp
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fillSendResp(tt.args.in, tt.args.out, tt.args.resp)
		})
	}
}

func SetupForChannelSend(ctl *gomock.Controller) (err error) {

	accountCli := account2.NewMockIClient(ctl)
	presentCli := presentMock.NewMockIClient(ctl)
	iopCli := iopMock.NewMockIClient(ctl)
	usualDeviceCli := usualDeviceMock.NewMockIClient(ctl)
	nobilityCli := nobilityMock.NewMockIClient(ctl)
	presentMiddlewareCli := pmMock.NewMockIClient(ctl)
	channelOlCli := channelolMock.NewMockIClient(ctl)
	channelCli := channel2.NewMockIClient(ctl)
	channelMicCli := channelMic.NewMockIClient(ctl)
	userProfileCli := userprofileapiMock.NewMockIClient(ctl)
	guildCli := guildMock.NewMockIClient(ctl)
	antiCli := antiMock.NewMockIClient(ctl)
	cooldownCli := cooldownMock.NewMockIClient(ctl)
	ukwCli := youknowwho2.NewMockIClient(ctl)

	userProfileCli.EXPECT().BatchGetUserProfile(gomock.Any(), gomock.Any())
	presentCli.EXPECT().GetPresentConfigById(gomock.Any(), gomock.Any()).Return(&userpresent.GetPresentConfigByIdResp{ItemConfig: &userpresent.StPresentItemConfig{
		ItemId:      1,
		Name:        "玫瑰",
		IconUrl:     "",
		Price:       0,
		Score:       0,
		Charm:       0,
		Rank:        0,
		EffectBegin: 0,
		EffectEnd:   **********,
		UpdateTime:  0,
		CreateTime:  0,
		IsDel:       false,
		PriceType:   0,
		RichValue:   0,
		Extend:      &userpresent.StPresentItemConfigExtend{Tag: 2},
	}}, nil)
	nobilityCli.EXPECT().GetNobilityInfo(gomock.Any(), gomock.Any(), gomock.Any())
	uid := uint32(2202538)
	toUid := uint32(2414206)

	accountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(map[uint32]*account.User{2202538: {Uid: &uid}, 2414206: {Uid: &toUid}}, nil)
	channelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	channelOlCli.EXPECT().GetUserChannelId(gomock.Any(), gomock.Any(), gomock.Any())

	ukwCli.EXPECT().BatchGetTrueUidByFake(gomock.Any(), gomock.Any()).Return([]*youknowwho3.TrueUidInfo{{Uid: 2202538, ReqUid: 2202538}, {Uid: 2414206, ReqUid: 2414206}}, nil)

	client.AccountCli = accountCli
	client.PresentCli = presentCli
	client.IopCli = iopCli
	client.UsualDeviceCli = usualDeviceCli
	client.NobilityCli = nobilityCli
	client.PresentMiddlewareCli = presentMiddlewareCli
	client.ChannelOlCli = channelOlCli
	client.ChannelCli = channelCli
	client.ChannelMicCli = channelMicCli
	client.UserProfileCli = userProfileCli
	client.GuildCli = guildCli
	client.AntiCli = antiCli
	client.CooldownCli = cooldownCli
	client.UkwCli = ukwCli
	return nil
}
