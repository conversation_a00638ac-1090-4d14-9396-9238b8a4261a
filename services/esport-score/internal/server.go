package internal

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/config"
	context0 "golang.org/x/net/context"
	"time"

	"golang.52tt.com/services/esport-score/internal/common"

	"golang.52tt.com/pkg/reporter"

	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/timer"
	errStatus "golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/demo/echo"
	pb "golang.52tt.com/protocol/services/esport_score"
	reconcilev2 "golang.52tt.com/protocol/services/reconcile-v2"
	dynamicConfig "golang.52tt.com/services/esport-score/internal/config/ttconfig/esport_score"
	"golang.52tt.com/services/esport-score/internal/event"
	"golang.52tt.com/services/esport-score/internal/manager"
	"golang.52tt.com/services/esport-score/internal/rpc"
	"golang.52tt.com/services/esport-score/internal/store"
)

type StartConfig struct {
	// [optional] from startup arguments

	// from config file
	MysqlConfig      *mysqlConnect.MysqlConfig `json:"mysql"`
	OrderKafkaConfig *config.KafkaConfig       `json:"order_kafka_config"`
	Feishu           string                    `json:"feishu"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	s := &Server{}

	store_, err := store.NewStore(ctx, cfg.MysqlConfig)
	if nil != err {
		log.ErrorWithCtx(ctx, "init store fail, err: %v", err)
		return nil, err
	}

	client_ := rpc.NewClient(ctx)

	config_, err := dynamicConfig.InitEsportScoreConfig()
	if nil != err {
		log.ErrorWithCtx(ctx, "init InitEsportScoreConfig fail, err: %v", err)
		return nil, err
	}

	log.DebugWithCtx(ctx, "feishu url: %+v", cfg.Feishu)
	feishu_ := reporter.NewFeishuReporter(cfg.Feishu, "")

	mgr_ := manager.NewManager(store_, client_, config_, feishu_)
	s.mgr = mgr_

	// 开启定时任务
	timerD, err := timer.NewTimerD(ctx, "esport-score", timer.WithTTL(30*time.Second))
	if nil != err {
		log.ErrorWithCtx(ctx, "init NewTimerD fail, err: %v", err)
		return nil, err
	}
	s.timer = timerD
	mgr_.CreateTable(ctx)
	// 定时任务创建表 每天执行一次
	err = timerD.AddTask("0 0 0 * * *", "CreateTable", timer.BuildFromLambda(func(ctx context.Context) {
		mgr_.CreateTable(ctx)
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init CreateTable fail, err: %v", err)
		return nil, err
	}
	// 定时任务初始化月表 每小时执行一次
	err = timerD.AddTask("0 0 * * * *", "InitMonthlyScoreDetail", timer.BuildFromLambda(func(ctx context.Context) {
		newCtx, cancel := context.WithTimeout(ctx, 50*time.Minute) // 这里把定时任务的超时时间重置为五十分钟
		defer cancel()
		err := mgr_.InitMonthlyScoreDetail(newCtx, time.Now())
		if err != nil {
			log.ErrorWithCtx(ctx, "InitMonthlyScoreDetail fail, err: %v", err)
		}
		time.Sleep(1 * time.Second)
	}))
	timerD.Start() // 启动定时器

	// 开启kafka消费
	kafkaEvent_, err := event.NewKafkaEvent(ctx, cfg.OrderKafkaConfig, mgr_)
	if nil != err {
		log.ErrorWithCtx(ctx, "init NewKafkaEvent fail, err: %v", err)
		return nil, err
	}
	s.svrKfk = kafkaEvent_

	return s, nil
}

type Server struct {
	mgr    manager.Manager
	timer  *timer.Timer
	svrKfk *event.KafkaEvent
}

func (s *Server) GetEsportScoreRate(c context0.Context, req *pb.GetEsportScoreRateReq) (*pb.GetEsportScoreRateResp, error) {
	out := &pb.GetEsportScoreRateResp{}
	defer func() {
		log.DebugWithCtx(c, "GetEsportScoreRate in:%+v, out:%+v", req, out)
	}()
	out.Rate = s.mgr.GetEsportScoreRate(req.GetIsGuildTrade(), time.Unix(req.GetOrderTime(), 0))
	return out, nil
}

func (s *Server) CalculateESportScore(c context.Context, req *pb.CalculateESportScoreReq) (*pb.CalculateESportScoreResp, error) {
	resp := &pb.CalculateESportScoreResp{}
	log.DebugWithCtx(c, "CalculateESportScore req: %+v", req)

	orderTime := time.Unix(int64(req.GetOrderTime()), 0)
	score := s.mgr.CalculateEsportScore(req.GetPrice(), orderTime, req.GetIsGuildTrade())
	resp.Score = score

	log.DebugWithCtx(c, "CalculateESportScore resp: %+v", resp)
	return resp, nil
}

func (s *Server) ShutDown() {
	s.svrKfk.Shutdown()
	s.timer.Stop()
	_ = s.mgr.Stop()
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	log.DebugWithCtx(ctx, "Echo req: %+v", req)
	if err := s.mgr.SendText(req.GetValue()); err != nil {
		log.ErrorWithCtx(ctx, "Echo SendText failed, err:%v", err)
	}
	return req, nil
}

// AddEsportScore 为指定用户增加/减少电竞积分（按订单幂等）
func (s *Server) AddEsportScore(ctx context.Context, req *pb.AddEsportScoreReq) (*pb.AddEsportScoreResp, error) {
	resp := &pb.AddEsportScoreResp{}
	log.DebugWithCtx(ctx, "AddEsportScore req: %+v", req)

	// 校验参数
	if req.GetUid() == 0 || req.GetScore() == 0 || req.GetOrderId() == "" || req.GetReasonType() == 0 || req.GetServerTime() == 0 {
		return resp, protocol.NewExactServerError(nil, errStatus.ErrRequestParamInvalid, "参数错误，检查参数！")
	}

	// 增加积分
	orderDetail := &common.EsportOrderDetail{
		Uid:        req.GetUid(),
		TotalPrice: req.GetTotalPrice(),
		Score:      req.GetScore(),
		ReasonType: req.GetReasonType(),
		Reason:     req.GetReasonDetail(),
		OrderId:    req.GetOrderId(),
		ServerTime: time.Unix(req.GetServerTime(), 0),
		GuildId:    0, // 非订单增加都为0
	}
	finalScore, err := s.mgr.AddEsportScore(ctx, orderDetail)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddEsportScore fail, err: %v", err)
		return resp, err
	}

	resp.FinalScore = finalScore
	log.DebugWithCtx(ctx, "AddEsportScore resp: %+v", resp)
	return resp, nil
}

// GetEsportScore 查询指定用户电竞积分
func (s *Server) GetEsportScore(ctx context.Context, req *pb.GetEsportScoreReq) (*pb.GetEsportScoreResp, error) {
	resp := &pb.GetEsportScoreResp{}
	log.DebugWithCtx(ctx, "GetEsportScore req: %+v", req)

	// 校验参数
	if req.GetUid() == 0 {
		return resp, protocol.NewExactServerError(nil, errStatus.ErrRequestParamInvalid, "参数错误，检查参数！")
	}

	// 查询积分
	score, err := s.mgr.GetEsportScore(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEsportScore fail, err: %v", err)
		return resp, err
	}
	resp.Score = score
	log.DebugWithCtx(ctx, "GetEsportScore resp: %+v", resp)
	return resp, nil
}

// parseAndGetReasonTypeList 解析参数并获取原因类型列表
func (s *Server) parseAndGetReasonTypeList(ctx context.Context, reqList []pb.ReasonType) []uint32 {
	result := make([]uint32, 0)
	for _, reasonType := range reqList {
		result = append(result, uint32(reasonType))
	}
	log.DebugWithCtx(ctx, "parseAndGetReasonTypeList result: %+v", result)
	return result
}

// GetEsportScoreDetail 查询指定用户电竞积分明细
func (s *Server) GetEsportScoreDetail(ctx context.Context, req *pb.GetEsportScoreDetailReq) (*pb.GetEsportScoreDetailResp, error) {
	resp := &pb.GetEsportScoreDetailResp{}
	log.DebugWithCtx(ctx, "GetEsportScoreDetail req: %+v", req)

	// 校验参数
	if req.GetUid() == 0 {
		return resp, protocol.NewExactServerError(nil, errStatus.ErrRequestParamInvalid, "参数错误，检查参数！")
	}

	// 修正参数
	if req.GetLimit() == 0 {
		req.Limit = 100
	}
	now := time.Now()
	var start, end time.Time
	if req.GetBeginTime() == 0 {
		start = time.Date(now.Year()-1, now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), 0, now.Location())
	} else {
		start = time.Unix(req.GetBeginTime(), 0)
	}
	if req.GetEndTime() == 0 {
		end = now
	} else {
		end = time.Unix(req.GetEndTime(), 0)
	}

	// 获取原因类型列表
	reasonTypeList := s.parseAndGetReasonTypeList(ctx, req.GetReasonTypeList())

	// 查询积分明细
	details, err := s.mgr.GetEsportScoreDetail(ctx, start, end, req.GetUid(), reasonTypeList, req.GetOffset(), req.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEsportScoreDetail fail, err: %v", err)
		return resp, err
	}
	resp.DetailList = details
	log.DebugWithCtx(ctx, "GetEsportScoreDetail resp: %+v", resp)
	return resp, nil
}

// GetEsportScoreRecordList 查询指定时间范围内的电竞积分记录（管理后台接口）
func (s *Server) GetEsportScoreRecordList(ctx context.Context, req *pb.GetEsportScoreRecordListReq) (*pb.GetEsportScoreRecordListResp, error) {
	resp := &pb.GetEsportScoreRecordListResp{}
	log.DebugWithCtx(ctx, "GetEsportScoreRecordList req: %+v", req)

	// 校验参数
	if req.GetBeginTs() == 0 || req.GetEndTs() == 0 {
		return resp, protocol.NewExactServerError(nil, errStatus.ErrRequestParamInvalid, "参数错误，检查参数！")
	}
	if time.Unix(req.GetBeginTs(), 0).Month() != time.Unix(req.GetEndTs(), 0).Month() {
		return resp, protocol.NewExactServerError(nil, errStatus.ErrRequestParamInvalid, "开始时间和结束时间必须在同一个月！")
	}

	// 修正参数
	if req.GetLimit() == 0 {
		req.Limit = 100
	}

	// 查询积分明细
	details, total, err := s.mgr.GetEsportScoreRecordList(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEsportScoreRecordList fail, err: %v", err)
		return resp, err
	}

	resp.RecordList = details
	resp.Total = total
	log.DebugWithCtx(ctx, "GetEsportScoreRecordList resp: %+v", resp)
	return resp, nil
}

type Reconcile struct {
	ReasonTypeList []uint32 `json:"reason_type_list"`
}

// parseAndGetReasonTypeListWithString 解析参数并获取原因类型列表
func (s *Server) parseAndGetReasonTypeListWithString(ctx context.Context, params string) ([]uint32, error) {
	result := &Reconcile{}
	if err := json.Unmarshal([]byte(params), result); err != nil {
		log.ErrorWithCtx(ctx, "parseAndGetReasonTypeList fail, err: %v", err)
		return nil, err
	}
	return result.ReasonTypeList, nil
}

// TimeRangeScoreOrderInfo 查询指定时间范围内的订单数量（对账接口）
func (s *Server) TimeRangeScoreOrderInfo(ctx context.Context, req *reconcilev2.TimeRangeReq) (*reconcilev2.CountResp, error) {
	resp := &reconcilev2.CountResp{}
	log.DebugWithCtx(ctx, "TimeRangeScoreOrderInfo req: %+v", req)

	if req.GetParams() == "" {
		return resp, protocol.NewExactServerError(nil, errStatus.ErrRequestParamInvalid, "参数错误，检查参数！")
	}

	// 获取原因类型列表
	reasonTypeList, err := s.parseAndGetReasonTypeListWithString(ctx, req.GetParams())
	if err != nil {
		return resp, protocol.NewExactServerError(nil, errStatus.ErrRequestParamInvalid, "参数解析错误，检查参数！")
	}

	// 查询结果
	count, sum, err := s.mgr.GetOrderInfoByPeriod(ctx, time.Unix(req.GetBeginTime(), 0), time.Unix(req.GetEndTime(), 0), reasonTypeList)
	if err != nil {
		log.ErrorWithCtx(ctx, "TimeRangeScoreOrderInfo fail, err: %v", err)
		return resp, protocol.NewExactServerError(nil, errStatus.ErrRepositoryFailed, "查询失败！")
	}

	resp.Count = count
	resp.Value = sum
	log.DebugWithCtx(ctx, "TimeRangeScoreOrderInfo resp: %+v", resp)
	return resp, nil
}

// TimeRangeOrderIds 查询指定时间范围内的订单号（对账接口）
func (s *Server) TimeRangeOrderIds(ctx context.Context, req *reconcilev2.TimeRangeReq) (*reconcilev2.OrderIdsResp, error) {
	resp := &reconcilev2.OrderIdsResp{}
	log.DebugWithCtx(ctx, "TimeRangeOrderIds req: %+v", req)

	if req.GetParams() == "" {
		return resp, protocol.NewExactServerError(nil, errStatus.ErrRequestParamInvalid, "参数错误，检查参数！")
	}

	// 获取原因类型列表
	reasonTypeList, err := s.parseAndGetReasonTypeListWithString(ctx, req.GetParams())
	if err != nil {
		return resp, protocol.NewExactServerError(nil, errStatus.ErrRequestParamInvalid, "参数解析错误，检查参数！")
	}

	// 查询结果
	orderIds, err := s.mgr.GetOrdersByPeriod(ctx, time.Unix(req.GetBeginTime(), 0), time.Unix(req.GetEndTime(), 0), reasonTypeList)
	if err != nil {
		log.ErrorWithCtx(ctx, "TimeRangeOrderIds fail, err: %v", err)
		return resp, protocol.NewExactServerError(nil, errStatus.ErrRepositoryFailed, "查询失败！")
	}

	resp.OrderIds = orderIds
	log.DebugWithCtx(ctx, "TimeRangeOrderIds resp: %+v", resp)
	return resp, nil
}

// ReSendOrder 补单接口（对账接口）
func (s *Server) ReSendOrder(ctx context.Context, req *reconcilev2.ReplaceOrderReq) (*reconcilev2.EmptyResp, error) {
	resp := &reconcilev2.EmptyResp{}
	log.DebugWithCtx(ctx, "ReSendPkg req: %+v", req)

	// 补单接口，目前只补正常完成订单时的漏单
	if err := s.mgr.ReSendOrder(ctx, req.GetOrderId()); err != nil {
		log.ErrorWithCtx(ctx, "ReSendPkg fail, err: %v", err)
		return resp, protocol.NewExactServerError(nil, errStatus.ErrRepositoryFailed, "补单失败！")
	}

	return resp, nil
}
