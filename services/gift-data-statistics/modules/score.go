package mds

import (
	"context"
	"fmt"
	"github.com/go-redis/redis"
	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jmoiron/sqlx"
	"github.com/tealeg/xlsx"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/gift-data-statistics/manager"
	"time"
)

type Score struct {
	Db       *sqlx.DB
	RedisCli *redis.Client
	files    []string
}

type MonthlyScoreRemain struct {
	Uid   uint32 `db:"uid"`
	Score uint32 `db:"score"`
}

type MonthlyScoreDetail struct {
	Uid         uint32    `db:"uid"`
	Score       int64     `db:"change_score"`
	OutsideTime time.Time `db:"outside_time"`
}

var score *Score

func init() {
	fmt.Println("score init")
	mysqlConfig := &config.MysqlConfig{
		Host:     "*************",
		Port:     3306,
		Database: "gift",
		UserName: "readonly",
		Password: "godmanreadonly",
		Protocol: "tcp",
		Charset:  "utf8",
	}
	dao, err := sqlx.Connect("mysql", mysqlConfig.ConnectionString())
	if err != nil {
		log.Errorf("Failed to create mysql %v %v\n", mysqlConfig.ConnectionString(), err)
		return
	}
	redisCli := redis.NewClient(&redis.Options{
		Addr: "************:6379",
	})
	score = &Score{
		Db:       dao,
		RedisCli: redisCli,
	}
	manager.Register("积分数据", score)
	//go score.Tick(true)
}

func (score *Score) GetMonthlyScoreScore(ctx context.Context, beginTs, endTs time.Time, file *xlsx.File) (err error) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("GetMonthlyScoreScore recover r:%v", r)
		}
	}()

	log.Infof("GetMonthlyScoreScore begin \n")
	var rowCount1, rowCount2 uint32 = 0, 0

	slice1 := []string{"uid", "上月获得总积分"}
	slice2 := []string{"uid", "上月消耗总积分"}

	sheet1, err1 := AddNewSheet(rowCount1/SheetRowCount, file, &slice1, "上月获得总积分")
	sheet2, err2 := AddNewSheet(rowCount2/SheetRowCount, file, &slice2, "上月消耗总积分")

	if err1 != nil || err2 != nil {
		fmt.Printf("GetMonthlyScoreScore err1:%v err2:%v", err1, err2)
		return nil
	}

	for i := 0; i < 100; i++ {
		lists := make([]*MonthlyScoreDetail, 0)
		// 这里用的create_time 有索引 但是对账要用outside_time 没索引
		// 折中一下，上下限都扩一个小时 然后用outside_time 二次过滤

		tmpBegin := beginTs.Add(-time.Hour)
		tmpEnd := endTs.Add(time.Hour)

		query := fmt.Sprintf(`select uid,change_score,outside_time from finance_user_score_detail_%02d 
					where create_time >= '%v' and create_time < '%v' group by uid,sign(change_score)`,
			i, tmpBegin.Format("2006-01-02 15:04:05"), tmpEnd.Format("2006-01-02 15:04:05"))

		log.Infof("GetMonthlyScoreScore :%v", query)

		err = score.Db.SelectContext(ctx, &lists, query)
		if err != nil {
			fmt.Printf("%v", err)
			return err
		}
		scoreMap := make(map[uint32]int64)
		for _, item := range lists {
			if item.OutsideTime.After(endTs) || item.OutsideTime.Before(beginTs) {
				continue
			}
			if _, ok := scoreMap[item.Uid]; !ok {
				scoreMap[item.Uid] = item.Score
			} else {
				scoreMap[item.Uid] += item.Score
			}
		}

		for uid, value := range scoreMap {
			if value > 0 {
				row1 := sheet1.AddRow()
				row1.AddCell().SetInt64(int64(uid))
				row1.AddCell().SetInt64(value)
				rowCount1 = rowCount1 + 1

				if rowCount1%SheetRowCount == 0 {
					sheet1, _ = AddNewSheet(rowCount1/SheetRowCount, file, &slice1, "上月获得总积分")
				}
			} else {
				row2 := sheet2.AddRow()
				row2.AddCell().SetInt64(int64(uid))
				row2.AddCell().SetInt64(value)
				rowCount2 = rowCount2 + 1

				if rowCount2%SheetRowCount == 0 {
					sheet2, _ = AddNewSheet(rowCount2/SheetRowCount, file, &slice2, "上月消耗总积分")
				}
			}
		}

		log.Infof("GetMonthlyScoreScore off:%v sz:%v \n", i, len(lists))
	}

	return nil
}

type SumScore struct {
	Score int64 `db:"sum_score"`
}

func (score *Score) GetMonthlyScoreRemain(ctx context.Context, beginTs time.Time, file *xlsx.File) (err error) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("GetMonthlyScoreRemain recover r:%v", r)
		}
	}()

	tableName := fmt.Sprintf("month_user_score_%v%02v", beginTs.Year(), int(beginTs.Month()))
	query := fmt.Sprintf("select sum(score) as sum_score from %v", tableName)

	log.Infof("GetMonthlyScoreRemain begin tableName:%v", tableName)
	res := SumScore{}
	err = score.Db.GetContext(ctx, &res, query)
	if err != nil {
		log.Errorf("GetMonthlyScoreRemain err:%v\n", err)
		return err
	}
	sheet, err := file.AddSheet("本月1号0点普通积分余额")
	if err != nil {
		log.Errorf("GetMonthlyScoreRemain err:%v\n", err)
		return err
	}
	row := sheet.AddRow()
	row.AddCell().SetString("全网积分剩余")
	row.AddCell().SetInt64(res.Score)
	return nil
}

func (score *Score) WriteUserScoreMonthlyExel(ctx context.Context, beginTs, endTs time.Time) (err error) {
	//filePath := manager.GetSubFile(beginTs, "score")

	//return nil
	filePath := manager.GetSubFile(beginTs, "积分数据")
	if IsExist(filePath) {
		score.files = append(score.files, filePath)
		return
	}
	file := xlsx.NewFile()
	now := time.Now().UnixNano()

	err = score.GetMonthlyScoreRemain(ctx, beginTs, file)
	if err != nil {
		//return err
	}

	err = score.GetMonthlyScoreScore(ctx, beginTs, endTs, file)
	if err != nil {
		//return err
	}

	fmt.Println("spend ", (time.Now().UnixNano()-now)/1e9, " s to save all data")

	err = file.Save(filePath)
	if err != nil {
		log.Errorf("WriteUserScoreMonthlyExel save fill err:%v\n", err)
		//return err
	}

	score.files = append(score.files, filePath)

	time.Sleep(time.Second * 30)

	return err
}

func (score *Score) GenExelFile(beginTs, endTs time.Time) {

	log.Infof("user score GenExelFile")
	if score == nil {
		return
	}

	ctx := context.Background()
	//now, _ := strconv.Atoi(beginTs.Format("200601"))
	_ = score.WriteUserScoreMonthlyExel(ctx, beginTs, endTs)

	log.Infof("userscore file has been written\n")
}

func (score *Score) GetFiles(ts time.Time) []string {
	return score.files
}
