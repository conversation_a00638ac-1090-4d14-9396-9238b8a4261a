package main

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/demo/echo"

	pb "golang.52tt.com/protocol/services/user-recall"

	"golang.52tt.com/services/user-recall/internal"

	// use server startup
	"gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server" // 兼容tyr公共库
)

func main() {
	var (
		svr *internal.Server
		cfg = &internal.StartConfig{}
		err error
	)

	// config file support yaml & json, default user-recall.json/yaml
	if err := server.NewServer("user-recall", cfg).
		AddGrpcServer(grpc.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				if svr, err = internal.NewServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
		pb.RegisterUserRecallServer(s, svr)
		pb.RegisterUserRecallServerCompat(s, svr)

				// grpcurl -plaintext -d '{"value":"hello"}' 127.0.0.1:80 demo.echo.EchoService.Echo
				// grpcurl -plaintext 127.0.0.1:80 grpc.health.v1.Health.Check
				echo.RegisterEchoServiceServer(s, svr)
				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
