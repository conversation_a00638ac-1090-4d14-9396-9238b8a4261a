package mgr

import (
	"fmt"
	"testing"
)

func TestMd5(t *testing.T) {
	md5 := getMd5("https://appcdn.52tt.com/web/frontend-web-assist-user-recall-outside/index.html?sign=yMVKOfwwmcZ4FPDm8Y0MJzcQGhdDw29acy1zVKXPlBo=&market_id=2")
	fmt.Println(md5)
}

/*
import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"github.com/jarcoal/httpmock"
	"golang.52tt.com/clients/account"
	im_api "golang.52tt.com/clients/im-api"
	mockaccount "golang.52tt.com/clients/mocks/account"
	mockim "golang.52tt.com/clients/mocks/im-api"
	mocksmssvr "golang.52tt.com/clients/mocks/smssvr"
	mockUgcfriendship "golang.52tt.com/clients/mocks/ugc/friendship"
	mockuserrecallaward "golang.52tt.com/clients/mocks/user-recall-award"
	smsSvr "golang.52tt.com/clients/smssvr"
	"golang.52tt.com/clients/ugc/friendship"
	"golang.52tt.com/clients/verifycode"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	bizbalancekeeperPB "golang.52tt.com/protocol/services/bizbalancekeeper"
	smsPB "golang.52tt.com/protocol/services/smssvr"
	friendshippb "golang.52tt.com/protocol/services/ugc/friendship"
	pb "golang.52tt.com/protocol/services/user-recall"
	user_recall_award "golang.52tt.com/protocol/services/user-recall-award"
	"golang.52tt.com/services/user-recall/internal/cache"
	"golang.52tt.com/services/user-recall/internal/conf"
	"golang.52tt.com/services/user-recall/internal/mocks"
	"golang.52tt.com/services/user-recall/internal/model"
	"golang.52tt.com/services/user-recall/internal/report"
)

var (
	accountCli      account.IClient
	SmsSvrClientCli *smsSvr.Client
)

func init() {

	log.SetLevel(log.DebugLevel)

	accountCli = account.NewIClient()
	SmsSvrClientCli = smsSvr.NewClient()

}

// go test -timeout 30s -run ^TestGetLossUidList1$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestGetLossUidList1(t *testing.T) {

	mgr := &UserRecallManager{}

	list1 := []*pb.LossUserInfo{
		{
			Uid:                1,
			ConsumeTbeanAmtCnt: 1,
		},
		{
			Uid:                2,
			ConsumeTbeanAmtCnt: 2,
		},
	}
	list2 := []*pb.LossUserInfo{
		{
			Uid:                1,
			ConsumeTbeanAmtCnt: 11,
		},
		{
			Uid:                3,
			ConsumeTbeanAmtCnt: 3,
		},
	}
	list1 = mgr.mergeRecallList(list1, list2)

	for _, info := range list1 {
		t.Logf("%+v\n", info)
	}
}

// go test -timeout 30s -run ^TestSMS$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestSMS(t *testing.T) {
	return
	time.Sleep(time.Second * 3)

	uid := uint32(2404178)
	sms(uid)
}

// 测试短信
func sms(uid uint32) {

	time.Sleep(time.Second * 3)

	ctx := context.Background()

	user, err := accountCli.GetUserByUid(ctx, uid)
	if err != nil {
		log.Errorln(err)
		return
	}

	// 1. gen code.
	key := genAnchorVerifyKey(uid)
	code, err := verifycode.StdClient.CreateVerifyCodeByKey(ctx, key, 4, 5*60)
	if err != nil {
		log.ErrorWithCtx(ctx, "verifycode.StdClient.CreateVerifyCodeByKey failed, key=[%s], err=[%v]", key, err)
		//_ = web.ServeAPICodeJson(w, -500, "gen code fail", nil)
		return
	}
	phone := user.GetPhone()

	params := []string{user.GetAlias(), user.GetNickname(), code}
	smsReq := &smsPB.SendSmsReq{
		Phone:           phone,
		SmsType:         83,
		ParamList:       params,
		WithoutCooldown: false,
		VerifyCodeKey:   key,
		VerifyCodeUsage: "auth",
		MarketId:        0,
		BizId:           uint32(bizbalancekeeperPB.BizId_TT),
	}
	log.InfoWithCtx(ctx, "HandleGenVerifyCode SendSmsReq %+v. uid %d  ", smsReq, uid)

	smsErr := SmsSvrClientCli.SendSmsV2(ctx, smsReq)
	if smsErr != nil {
		log.ErrorWithCtx(ctx, "sms.SendSms failed, uid=[%d], phone=[%s], err=[%v]", uid, phone, smsErr)
		if smsErr.Code() == int(smsPB.ERR_SMS_ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED) {
			log.ErrorWithCtx(ctx, "今日验证码发送次数已达上限 uid:%d", uid)
			//_ = web.ServeAPICodeJson(w, -500, "今日验证码发送次数已达上限", nil)
			return
		} else if smsErr.Code() == int(smsPB.ERR_SMS_ERR_SMS_SEND_SMS_FREQ) {
			log.ErrorWithCtx(ctx, "今日验证码发送操作太过频繁 req uid:%d", uid)
			//_ = web.ServeAPICodeJson(w, -500, "操作太过频繁", nil)
			return
		}
		log.ErrorWithCtx(ctx, "验证码发送 uid:%d err:%+v", uid, smsErr)
		//_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}
}

func genAnchorVerifyKey(uid uint32) string {
	return fmt.Sprintf("guild_rescind_%d", uid)
}

// go test -timeout 30s -run ^TestUserRecallManager_GetUserRecallList$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_GetUserRecallList(t *testing.T) {

	return

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockdyconf := mocks.NewMockISDyConfigHandler(ctl)
	mockcachecli := mocks.NewMockIRedisCache(ctl)
	mockuserrecallawardCli := mockuserrecallaward.NewMockUserRecallAwardClient(ctl)
	mockstorecli := mocks.NewMockIStore(ctl)
	mockaccountCli := mockaccount.NewMockIClient(ctl)

	ctx := grpc.WithServiceInfo(context.Background(), &grpc.ServiceInfo{
		UserID: 1,
	})

	// out.GiftVal = mgr.dyconfig.GetMaxGiftVal()
	// out.JumpUrl = mgr.dyconfig.GetInviterWebUrl()

	info := &pb.LossUserInfoList{List: []*pb.LossUserInfo{
		{
			Uid: 2,
		},
	}}
	bin, _ := proto.Marshal(info)
	awardResp := &user_recall_award.BatchGetInviteAwardInfoResp{
		AwardInfos: []*user_recall_award.InviteAwardInfo{
			{
				RecalledUid: 2,
				AwardInfo: &user_recall_award.AwardInfo{
					GiftId:   1,
					GiftName: "1",
					GiftCnt:  1,
					GiftIcon: "1",
					GiftVal:  1,
				},
			},
		},
	}
	//bindlist := []*model.UserRecallBind{{Uid: 100}}

	usermap := map[uint32]*accountPB.UserResp{}

	gomock.InOrder(
		mockdyconf.EXPECT().GetMaxGiftVal().Return(uint32(0)),
		mockdyconf.EXPECT().GetInviterWebUrl().Return(""),
		mockcachecli.EXPECT().GetLossUidList(gomock.Any()).Return(true, bin, nil),
		mockcachecli.EXPECT().GetBindUnfinished(gomock.Any()).Return(true, bin, nil),
		//mockcachecli.EXPECT().GetBindUnfinished(gomock.Any()).Return(true, 1, nil),
		//mockstorecli.EXPECT().GetUserRecallBindUnfinishedList(gomock.Any(), gomock.Any()).Return(bindlist, nil),
		mockuserrecallawardCli.EXPECT().BatchGetInviteAwardInfo(gomock.Any(), gomock.Any()).Return(awardResp, nil),

		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermap, protocol.NewServerError(status.ErrUserRecallParams, "奖励配置错误")),
	)

	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetUserRecallListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserRecallListResp
		wantErr bool
	}{
		{
			fields: fields{
				dyconfig:              mockdyconf,
				rc:                    mockcachecli,
				userRecallAwardClient: mockuserrecallawardCli,
				store:                 mockstorecli,
				AccountCli:            mockaccountCli,
			},
			args: args{
				ctx: ctx,
				req: &pb.GetUserRecallListReq{Uid: 1},
			},
			want:    &pb.GetUserRecallListResp{},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			got, err := mgr.GetUserRecallList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.GetUserRecallList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UserRecallManager.GetUserRecallList() = %v, want %v", got, tt.want)
			}
		})
	}
}

// go test -timeout 30s -run ^TestUserRecallManager_SendRecall$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_SendRecall(t *testing.T) {
	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx context.Context
		req *pb.SendRecallReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.SendRecallResp
		wantErr bool
	}{
		{
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &pb.SendRecallReq{},
			},
			want:    &pb.SendRecallResp{},
			wantErr: true,
		},
		{
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &pb.SendRecallReq{Uid: 1, TargetUid: 1},
			},
			want:    &pb.SendRecallResp{},
			wantErr: true,
		},
		{
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &pb.SendRecallReq{Uid: 1, TargetUid: 2},
			},
			want:    &pb.SendRecallResp{},
			wantErr: true,
		},
		// {
		// 	fields: fields{},
		// 	args: args{
		// 		ctx: context.Background(),
		// 		req: &pb.SendRecallReq{Uid: 1, TargetUid: 1},
		// 	},
		// 	want:    &pb.SendRecallResp{},
		// 	wantErr: true,
		// },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			got, err := mgr.SendRecall(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.SendRecall() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UserRecallManager.SendRecall() = %v, want %v", got, tt.want)
			}
		})
	}
}

// go test -timeout 30s -run ^TestUserRecallManager_SendRecall$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_SendRecall1(t *testing.T) {
	return

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockcachecli := mocks.NewMockIRedisCache(ctl)

	info := &pb.LossUserInfoList{List: []*pb.LossUserInfo{
		{
			Uid: 1,
		},
	}}
	bin, _ := proto.Marshal(info)

	gomock.InOrder(
		mockcachecli.EXPECT().GetLossUidList(gomock.Any()).Return(true, bin, nil),
	)

	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx context.Context
		req *pb.SendRecallReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.SendRecallResp
		wantErr bool
	}{

		{
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &pb.SendRecallReq{Uid: 1, TargetUid: 1, RecallType: 1},
			},
			want:    &pb.SendRecallResp{},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			got, err := mgr.SendRecall(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.SendRecall() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UserRecallManager.SendRecall() = %v, want %v", got, tt.want)
			}
		})
	}
}

// go test -timeout 30s -run ^TestUserRecallManager_GetRecallPrize$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_GetRecallPrize(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockuserrecallawardCli := mockuserrecallaward.NewMockUserRecallAwardClient(ctl)
	mockstorecli := mocks.NewMockIStore(ctl)
	mockcachecli := mocks.NewMockIRedisCache(ctl)
	bindlist := []*model.UserRecallBind{
		{
			Uid: 2,
		},
	}
	awardresp := &user_recall_award.GetInviteAwardResp{}

	gomock.InOrder(
		mockstorecli.EXPECT().GetUserRecallBindUnfinishedList(gomock.Any(), gomock.Any()).Return(bindlist, nil),
		mockuserrecallawardCli.EXPECT().GetInviteAward(gomock.Any(), gomock.Any(), gomock.Any()).Return(awardresp, nil),
		mockstorecli.EXPECT().FinishUserRecallBindStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockcachecli.EXPECT().DelBindUnfinished(gomock.Any()).Return(errors.New("")),
	)

	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetRecallPrizeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetRecallPrizeResp
		wantErr bool
	}{
		{
			fields: fields{
				store:                 mockstorecli,
				rc:                    mockcachecli,
				userRecallAwardClient: mockuserrecallawardCli,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.GetRecallPrizeReq{
					Uid:       1,
					TargetUid: 2,
				},
			},
			want: &pb.GetRecallPrizeResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			got, err := mgr.GetRecallPrize(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.GetRecallPrize() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UserRecallManager.GetRecallPrize() = %v, want %v", got, tt.want)
			}
		})
	}
}

// go test -timeout 30s -run ^TestUserRecallManager_PushImMsg$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_PushImMsg(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockimCli := mockim.NewMockIClient(ctl)
	gomock.InOrder(
		mockimCli.EXPECT().Send1V1ExtMsg(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
	)

	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx       context.Context
		inviteUid uint32
		uid       uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			fields: fields{
				imApiClient: mockimCli,
			},
			args: args{
				ctx: context.Background(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			if err := mgr.PushImMsg(tt.args.ctx, tt.args.inviteUid, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.PushImMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// go test -timeout 30s -run ^TestUserRecallManager_sendSms2$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_sendSms2(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockaccountCli := mockaccount.NewMockIClient(ctl)
	mockcachecli := mocks.NewMockIRedisCache(ctl)

	usermap := map[uint32]*accountPB.UserResp{
		1: {
			Uid: 1,
		},
	}
	usermap2 := map[uint32]*accountPB.UserResp{
		1: {
			Uid: 1,
		},
		2: {
			Uid: 2,
		},
	}
	usermap3 := map[uint32]*accountPB.UserResp{
		1: {
			Uid: 1,
		},
		2: {
			Uid:   2,
			Phone: "1",
		},
	}

	e := protocol.NewServerError(status.ErrRequestParamInvalid, "用户不存在")
	gomock.InOrder(
		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(nil, e),

		//2
		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermap, nil),

		//3
		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermap2, nil),

		// 4
		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermap3, nil),
		mockcachecli.EXPECT().CheckIsSendSms(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil),
	)

	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx       context.Context
		uid       uint32
		targetUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			fields: fields{
				AccountCli: mockaccountCli,
				//UgcfriendshipCli: mockUgcfriendshipCli,
				//SmsSvrClientCli:  mocksmssvrcli,
				//dyconfig:         mockdyconf,
			},
			args: args{
				ctx:       context.Background(),
				uid:       1,
				targetUid: 2,
			},
			wantErr: true,
		},
		{
			fields: fields{
				AccountCli: mockaccountCli,
				//UgcfriendshipCli: mockUgcfriendshipCli,
				//SmsSvrClientCli:  mocksmssvrcli,
				//dyconfig:         mockdyconf,
			},
			args: args{
				ctx:       context.Background(),
				uid:       1,
				targetUid: 2,
			},
			wantErr: true,
		},

		//3
		{
			fields: fields{
				AccountCli: mockaccountCli,
				//UgcfriendshipCli: mockUgcfriendshipCli,
				//SmsSvrClientCli:  mocksmssvrcli,
				//dyconfig:         mockdyconf,
			},
			args: args{
				ctx:       context.Background(),
				uid:       1,
				targetUid: 2,
			},
			wantErr: true,
		},

		//4
		{
			fields: fields{
				AccountCli: mockaccountCli,
				rc:         mockcachecli,
				//UgcfriendshipCli: mockUgcfriendshipCli,
				//SmsSvrClientCli:  mocksmssvrcli,
				//dyconfig:         mockdyconf,
			},
			args: args{
				ctx:       context.Background(),
				uid:       1,
				targetUid: 2,
			},

			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			if err := mgr.sendSms(tt.args.ctx, tt.args.uid, tt.args.targetUid, 0); (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.sendSms() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// go test -timeout 30s -run ^TestUserRecallManager_sendSms$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_sendSms(t *testing.T) {

	return

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockaccountCli := mockaccount.NewMockIClient(ctl)
	mockUgcfriendshipCli := mockUgcfriendship.NewMockIClient(ctl)
	mocksmssvrcli := mocksmssvr.NewMockIClient(ctl)
	mockdyconf := mocks.NewMockISDyConfigHandler(ctl)

	usermap := map[uint32]*accountPB.UserResp{
		1: {
			Uid: 1,
		},
		2: {
			Uid:   2,
			Phone: "1",
		},
	}
	usermap2 := map[uint32]*accountPB.UserResp{
		1: {
			Uid: 1,
		},
	}
	usermap3 := map[uint32]*accountPB.UserResp{
		1: {
			Uid: 1,
		},
		2: {
			Uid: 2,
		},
	}
	targetFriendInfo := &friendshippb.FriendInfo{Remark: "mk"}

	gomock.InOrder(
		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermap, nil),
		mockUgcfriendshipCli.EXPECT().GetOneFriendInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(targetFriendInfo, nil),
		mockdyconf.EXPECT().GetUserRecallSmsType().Return(uint32(1)),
		mockdyconf.EXPECT().GetUserRecallSmsJumpUrl().Return(""),
		mocksmssvrcli.EXPECT().SendSmsV2(gomock.Any(), gomock.Any()).Return(nil),

		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermap2, nil),
		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermap3, nil),

		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermap, nil),
		mockUgcfriendshipCli.EXPECT().GetOneFriendInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(targetFriendInfo, nil),
		mockdyconf.EXPECT().GetUserRecallSmsType().Return(uint32(1)),
		mockdyconf.EXPECT().GetUserRecallSmsJumpUrl().Return(""),
		mocksmssvrcli.EXPECT().SendSmsV2(gomock.Any(), gomock.Any()).Return(protocol.NewServerError(int(smsPB.ERR_SMS_ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED))),

		// ERR_SMS_ERR_SMS_SEND_SMS_FREQ
		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermap, nil),
		mockUgcfriendshipCli.EXPECT().GetOneFriendInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(targetFriendInfo, nil),
		mockdyconf.EXPECT().GetUserRecallSmsType().Return(uint32(1)),
		mockdyconf.EXPECT().GetUserRecallSmsJumpUrl().Return(""),
		mocksmssvrcli.EXPECT().SendSmsV2(gomock.Any(), gomock.Any()).Return(protocol.NewServerError(int(smsPB.ERR_SMS_ERR_SMS_SEND_SMS_FREQ))),
	)

	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx       context.Context
		uid       uint32
		targetUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			fields: fields{
				AccountCli:       mockaccountCli,
				UgcfriendshipCli: mockUgcfriendshipCli,
				SmsSvrClientCli:  mocksmssvrcli,
				dyconfig:         mockdyconf,
			},
			args: args{
				ctx:       context.Background(),
				uid:       1,
				targetUid: 2,
			},
		},
		{
			fields: fields{
				AccountCli:       mockaccountCli,
				UgcfriendshipCli: mockUgcfriendshipCli,
				SmsSvrClientCli:  mocksmssvrcli,
				dyconfig:         mockdyconf,
			},
			args: args{
				ctx:       context.Background(),
				uid:       1,
				targetUid: 2,
			},
			wantErr: true,
		},
		{
			fields: fields{
				AccountCli:       mockaccountCli,
				UgcfriendshipCli: mockUgcfriendshipCli,
				SmsSvrClientCli:  mocksmssvrcli,
				dyconfig:         mockdyconf,
			},
			args: args{
				ctx:       context.Background(),
				uid:       1,
				targetUid: 2,
			},
			wantErr: true,
		},

		{
			fields: fields{
				AccountCli:       mockaccountCli,
				UgcfriendshipCli: mockUgcfriendshipCli,
				SmsSvrClientCli:  mocksmssvrcli,
				dyconfig:         mockdyconf,
			},
			args: args{
				ctx:       context.Background(),
				uid:       1,
				targetUid: 2,
			},
			wantErr: true,
		},

		{
			fields: fields{
				AccountCli:       mockaccountCli,
				UgcfriendshipCli: mockUgcfriendshipCli,
				SmsSvrClientCli:  mocksmssvrcli,
				dyconfig:         mockdyconf,
			},
			args: args{
				ctx:       context.Background(),
				uid:       1,
				targetUid: 2,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			if err := mgr.sendSms(tt.args.ctx, tt.args.uid, tt.args.targetUid, 0); (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.sendSms() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestSms(t *testing.T) {
	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx context.Context
		req *pb.TestSmsReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.SendRecallResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			got, err := mgr.TestSms(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.TestSms() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UserRecallManager.TestSms() = %v, want %v", got, tt.want)
			}
		})
	}
}

// GetLossUidList
// go test -timeout 30s -run ^Test_GetLossUidList$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func Test_GetLossUidList11(t *testing.T) {
	return
	// dyconfig := conf.NewConfigHandler("../conf/user_recall.json")
	// if err := dyconfig.Start(); err != nil {
	// 	log.Errorf("NewUserRecallManager dyconfig.Start fail %v", err)
	// 	return
	// }

	// redisClient := redis.NewClient(&redis.Options{
	// 	Addr: "************:6379",
	// })

	// redisCache := cache.NewRedisCache(redisClient)

	// m := &UserRecallManager{
	// 	dyconfig: dyconfig,
	// 	rc:       redisCache,
	// }

	// list, _ := m.GetLossUidList(context.Background(), 2404178)
	// for _, info := range list {
	// 	t.Logf("%+v\n", info)
	// }

}

// go test -timeout 30s -run ^Test_doHttpPOST$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func Test_doHttpPOST(t *testing.T) {

	// doHttpPOST(ctx context.Context, url string, reqBody []byte)

	//http://dap-yuntest.ttyuyin.com:8197/tt-activity/lossRecallUser/lossUidList
	url := "http://dap-yuntest.ttyuyin.com:8197/tt-activity/lossRecallUser/lossUidList"
	reqBody := []byte(`{"uidList":[2404178],"type": "lossUid"}`)
	body, err := doHttpPOST(context.Background(), url, reqBody)
	t.Log(err)
	t.Logf("%s\n", body)
}

// go test -timeout 30s -run ^TestPARSE$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestPARSE(t *testing.T) {

	list := []*pb.RecallUserInfo{
		{
			//Uid:         1,
			Status:      1,
			LastLoginTs: 10,
			CreateTime:  100,
		},
		{
			//Uid:         2,
			Status:      1,
			LastLoginTs: 10,
			CreateTime:  101,
		},
		{
			//Uid:         3,
			Status:      2,
			LastLoginTs: 10,
			CreateTime:  101,
		},
		{
			//Uid:         4,
			Status:      2,
			LastLoginTs: 10,
			CreateTime:  102,
		},
	}

	sort.Slice(list, func(i, j int) bool {
		if list[i].Status == list[j].Status {
			return list[i].CreateTime > list[j].CreateTime
		}
		return list[i].Status > list[j].Status
	})

	t.Log("\n")
	for _, info := range list {
		t.Logf("%+v\n", info)
	}

	sort.Slice([]uint32{}, func(i, j int) bool { return false })

	return

	logindate, _ := time.Parse("2006-01-02", "2023-10-30")
	t.Log(logindate, logindate.Unix())
	logindate, _ = time.Parse("2006-1-02", "2023-8-24")
	t.Log(logindate, logindate.Unix())
	return

	ConsumeTbeanAmtAcc := "11.22"
	tbeanVal, _ := strconv.ParseFloat(ConsumeTbeanAmtAcc, 32)
	t.Log(tbeanVal)
	t.Log(uint64(tbeanVal * 100))

}

// go test -timeout 30s -run ^TestJSON2$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestJSON2(t *testing.T) {
	// body := []byte(`
	// {
	// 	"code": 200,
	// 	"message": "SUCCESS",
	// 	"serverTime": 1698724199126,
	// 	"data": {
	// 	  "uidList": [
	// 		"2412355",
	// 		"2532765"
	// 	  ]
	// 	}
	//   }
	// `)

	// msg2, err := UnmarshalWelcome(body)
	// if err != nil {
	// 	log.Errorf("GetLossUidList Unmarshal fail %v, uid=%d", err)
	// 	return
	// }

	// t.Logf("%+v\n", msg2)

}

// go test -timeout 30s -run ^TestJSON$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
// func TestJSON(t *testing.T) {
// 	body := []byte(`
// 	{
// 		"code": 200,
// 		"message": "SUCCESS",
// 		"serverTime": 1698732513590,
// 		"data": {
// 		  "userInfos": [
// 			{
// 			  "uid": "",
// 			  "consumeTbeanAmtAcc": "1000",
// 			  "lstLoginDate": "",
// 			  "receiveGiftOrdAmt": ""
// 			},
// 			{
// 			  "uid": "",
// 			  "consumeTbeanAmtAcc": "1000",
// 			  "lstLoginDate": "2023-10-30",
// 			  "receiveGiftOrdAmt": ""
// 			},
// 			{
// 			  "uid": "",
// 			  "consumeTbeanAmtAcc": "1000",
// 			  "lstLoginDate": "2023-10-30",
// 			  "receiveGiftOrdAmt": "22"
// 			},
// 			{
// 			  "uid": "2404178",
// 			  "consumeTbeanAmtAcc": "1000",
// 			  "lstLoginDate": "2023-10-30",
// 			  "receiveGiftOrdAmt": "22"
// 			},
// 			{
// 			  "uid": "",
// 			  "consumeTbeanAmtAcc": "2000",
// 			  "lstLoginDate": "",
// 			  "receiveGiftOrdAmt": ""
// 			},
// 			{
// 			  "uid": "",
// 			  "consumeTbeanAmtAcc": "2000",
// 			  "lstLoginDate": "2023-10-30",
// 			  "receiveGiftOrdAmt": ""
// 			},
// 			{
// 			  "uid": "",
// 			  "consumeTbeanAmtAcc": "2000",
// 			  "lstLoginDate": "2023-10-30",
// 			  "receiveGiftOrdAmt": "22"
// 			},
// 			{
// 			  "uid": "2404178",
// 			  "consumeTbeanAmtAcc": "2000",
// 			  "lstLoginDate": "2023-10-30",
// 			  "receiveGiftOrdAmt": "22"
// 			}
// 		  ]
// 		}
// 	  }

// 	`)
// 	msg2, err := UnmarshalWelcome2(body)
// 	if err != nil {
// 		log.Errorf("GetLossUidList Unmarshal fail %v, uid=%d", err)
// 		return
// 	}

// 	t.Logf("%+v\n", msg2)

// 	uid2info := map[uint32]lossUserInfo{}
// 	for _, info := range msg2.Data.UserInfos {
// 		uid, _ := strconv.Atoi(info.Uid)
// 		tbeanVal, _ := strconv.ParseFloat(info.ConsumeTbeanAmtAcc, 32)
// 		logindate, _ := time.Parse("2006-01-02", info.LstLoginDate)
// 		if uid > 0 && tbeanVal > 0 && logindate.Unix() > 0 {
// 			uid2info[uint32(uid)] = lossUserInfo{
// 				Uid:                uint32(uid),
// 				ConsumeTbeanAmtCnt: uint64(tbeanVal * 100),
// 				LstLoginDate:       uint64(logindate.Unix()),
// 			}
// 		}
// 	}

// 	t.Logf("\n\n\nuid2info=%+v\n", uid2info)
// }

// go test -timeout 30s -run ^TestUserRecallManager_GetLossUidList$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_GetLossUidList1(t *testing.T) {
	// ctl := gomock.NewController(t)
	// defer ctl.Finish()

	// mockcachecli := mocks.NewMockIRedisCache(ctl)
	// mockdyconf := mocks.NewMockISDyConfigHandler(ctl)

	// // "DataCenterGetLossUidListUrl": "http://dap-yuntest.ttyuyin.com:8197/tt-activity/lossRecallUser/lossUidList",
	// // "DataCenterGetLossUserInfoUrl": "http://dap-yuntest.ttyuyin.com:8197/tt-activity/lossRecallUser/lossUserInfo"

	// httpmock.ActivateNonDefault(http.DefaultClient)
	// url := "http://dap-yuntest.ttyuyin.com:8197/tt-activity/lossRecallUser/lossUidList"
	// method := "POST"
	// response := httpmock.NewStringResponder(http.StatusOK, `{
	// 	"code": 200,
	// 	"message": "SUCCESS",
	// 	"serverTime": 1698724199126,
	// 	"data": {
	// 	  "uidList": [

	// 	  ]
	// 	}
	//   }`)
	// httpmock.RegisterResponder(method, url, response)

	// gomock.InOrder(
	// 	mockcachecli.EXPECT().GetLossUidList(gomock.Any()).Return(true, []byte(""), nil),

	// 	mockcachecli.EXPECT().GetLossUidList(gomock.Any()).Return(false, []byte(""), nil),
	// 	//mockdyconf.EXPECT().GetDataCenterGetLossUidListUrl().Return(url),
	// 	mockcachecli.EXPECT().SetLossUidList(gomock.Any(), gomock.Any()).Return(nil),
	// )

	// type fields struct {
	// 	store                 model.IStore
	// 	rc                    cache.IRedisCache
	// 	report                *report.FeishuReporterV2
	// 	dyconfig              conf.ISDyConfigHandler
	// 	imApiClient           im_api.IClient
	// 	SmsSvrClientCli       smsSvr.IClient
	// 	AccountCli            account.IClient
	// 	UgcfriendshipCli      friendship.IClient
	// 	userRecallAwardClient user_recall_award.UserRecallAwardClient
	// }
	// type args struct {
	// 	ctx context.Context
	// 	uid uint32
	// }
	// tests := []struct {
	// 	name    string
	// 	fields  fields
	// 	args    args
	// 	want    []*pb.LossUserInfo
	// 	wantErr bool
	// }{
	// 	{
	// 		fields: fields{
	// 			rc: mockcachecli,
	// 		},
	// 		args: args{
	// 			ctx: context.Background(),
	// 		},
	// 		want: nil,
	// 	},
	// 	{
	// 		fields: fields{
	// 			rc:       mockcachecli,
	// 			dyconfig: mockdyconf,
	// 		},
	// 		args: args{
	// 			ctx: context.Background(),
	// 		},
	// 		want: nil,
	// 	},
	// }
	// for _, tt := range tests {
	// 	t.Run(tt.name, func(t *testing.T) {
	// 		mgr := &UserRecallManager{
	// 			store:                 tt.fields.store,
	// 			rc:                    tt.fields.rc,
	// 			report:                tt.fields.report,
	// 			dyconfig:              tt.fields.dyconfig,
	// 			imApiClient:           tt.fields.imApiClient,
	// 			SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
	// 			AccountCli:            tt.fields.AccountCli,
	// 			UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
	// 			userRecallAwardClient: tt.fields.userRecallAwardClient,
	// 		}
	// 		got, err := mgr.GetLossUidList(tt.args.ctx, tt.args.uid)
	// 		if (err != nil) != tt.wantErr {
	// 			t.Errorf("UserRecallManager.GetLossUidList() error = %v, wantErr %v", err, tt.wantErr)
	// 			return
	// 		}
	// 		if !reflect.DeepEqual(got, tt.want) {
	// 			t.Errorf("UserRecallManager.GetLossUidList() = %v, want %v", got, tt.want)
	// 		}
	// 	})
	// }
}

func TestUserRecallManager_getLossUidList(t *testing.T) {
	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.LossUserInfo
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			_, got, err := mgr.getLossUidList(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.getLossUidList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UserRecallManager.getLossUidList() = %v, want %v", got, tt.want)
			}
		})
	}
}

// go test -timeout 30s -run ^TestUserRecallManager_CleanInviteData$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_CleanInviteData(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockcachecli := mocks.NewMockIRedisCache(ctl)
	mockstorecli := mocks.NewMockIStore(ctl)
	var list []*model.UserRecallInviteRecord

	gomock.InOrder(
		mockstorecli.EXPECT().GetInviterAllRecord(gomock.Any(), gomock.Any()).Return(list, nil),
		mockstorecli.EXPECT().DeleteUserRecallInviteRecord(gomock.Any(), gomock.Any()).Return(nil),
		mockstorecli.EXPECT().DeleteUserRecallImRecord(gomock.Any(), gomock.Any()).Return(nil),
		mockcachecli.EXPECT().DelLossUidList(gomock.Any()).Return(nil),
	)

	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx context.Context
		req *pb.CleanInviteDataReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.CleanInviteDataResp
		wantErr bool
	}{
		{
			fields: fields{
				store: mockstorecli,
				rc:    mockcachecli,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.CleanInviteDataReq{},
			},
			want: &pb.CleanInviteDataResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			got, err := mgr.CleanInviteData(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.CleanInviteData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UserRecallManager.CleanInviteData() = %v, want %v", got, tt.want)
			}
		})
	}
}

// go  test -timeout 30s -run ^TestUserRecallManager_getLossUidListFromCache$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_getLossUidListFromCache(t *testing.T) {
	return

	ctl := gomock.NewController(t)

	defer ctl.Finish()

	mockcachecli := mocks.NewMockIRedisCache(ctl)
	mockdyconf := mocks.NewMockISDyConfigHandler(ctl)

	gomock.InOrder(
		mockcachecli.EXPECT().GetLossUidList(gomock.Any()).Return(false, nil, nil),

		// GetDataCenterUrlPrefix
		mockdyconf.EXPECT().GetDataCenterUrlPrefix().Return("h"),
	)

	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.LossUserInfo
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			got, err := mgr.getLossUidListFromCache(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.getLossUidListFromCache() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UserRecallManager.getLossUidListFromCache() = %v, want %v", got, tt.want)
			}
		})
	}
}

// go  test -timeout 30s -run ^TestUserRecallManager_GetShortLink$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_GetShortLink(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockcachecli := mocks.NewMockIRedisCache(ctl)

	gomock.InOrder(
		mockcachecli.EXPECT().GetShortLinkByUrl(gomock.Any()).Return("1", nil),

		//2
		mockcachecli.EXPECT().GetShortLinkByUrl(gomock.Any()).Return("", nil),
		mockcachecli.EXPECT().GetUrlByShortLink(gomock.Any()).Return("", nil),
		mockcachecli.EXPECT().SetShortLink(gomock.Any(), gomock.Any()).Return(nil),

		// 3
		mockcachecli.EXPECT().GetUrlByShortLink(gomock.Any()).Return("1", nil),
	)

	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetShortLinkReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetShortLinkResp
		wantErr bool
	}{
		{
			fields: fields{
				rc: mockcachecli,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.GetShortLinkReq{
					Url: "1",
				},
			},
			want: &pb.GetShortLinkResp{
				Url:       "1",
				ShortLink: "1",
			},
		},

		//2
		{
			fields: fields{
				rc: mockcachecli,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.GetShortLinkReq{
					Url: "1",
				},
			},
			want: &pb.GetShortLinkResp{
				Url:       "1",
				ShortLink: "Urquma",
			},
		},

		//3
		{
			fields: fields{
				rc: mockcachecli,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.GetShortLinkReq{
					ShortLink: "1",
				},
			},
			want: &pb.GetShortLinkResp{
				Url:       "1",
				ShortLink: "1",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			got, err := mgr.GetShortLink(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.GetShortLink() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UserRecallManager.GetShortLink() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getMd5(t *testing.T) {
	type args struct {
		url string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getMd5(tt.args.url); got != tt.want {
				t.Errorf("getMd5() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_shortUrl(t *testing.T) {
	type args struct {
		url string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := shortUrl(tt.args.url); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("shortUrl() = %v, want %v", got, tt.want)
			}
		})
	}
}

// go  test -timeout 30s -run ^TestUserRecallManager_ERRMSG$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_ERRMSG(t *testing.T) {

	// sendSms SendSmsV2 fail status: -2, message: 系统错误, underlying: rpc error: code = Unavailable desc = no healthy upstream,
	// uid=297846269 targetUid=297586756 phone=17870044307
	err := protocol.NewServerErrorWithUnderlying(errors.New("rpc error: code = Unavailable desc = no healthy upstream"),
		-2, "系统错误")
	t.Logf("%+v\n", err)

	if strings.Contains(err.Underlying().Error(), "no healthy upstream") {
		t.Log("1")
	}

	err = protocol.NewServerErrorWithUnderlying(nil,
		-2, "系统错误")
	t.Logf("%+v\n", err)

	if err.Underlying() != nil && strings.Contains(err.Underlying().Error(), "no healthy upstream") {
		t.Log("1")
	}

}

func TestUserRecallManager_GetLossUidList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockdyconf := mocks.NewMockISDyConfigHandler(ctl)
	gomock.InOrder(
		mockdyconf.EXPECT().GetDataCenterUrlPrefix().Return("http://abc"),
	)

	httpmock.ActivateNonDefault(http.DefaultClient)
	url := "http://abc/lossRecallUser/lossUserInfo"
	method := "POST"
	response := httpmock.NewStringResponder(http.StatusOK, `
	{
		"code": 200,
		"message": "SUCCESS",
		"serverTime": 1698723454189,
		"data": {
		  "userInfos": [
			{
			  "uid": "1",
			  "consumeTbeanAmtAcc": "1000",
			  "lstLoginDate": "2023-10-10",
			  "receiveGiftOrdAmt": ""
			}
		  ]
		}
	  }`)
	httpmock.RegisterResponder(method, url, response)

	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetLossUidListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetLossUidListResp
		wantErr bool
	}{
		{
			fields: fields{
				dyconfig: mockdyconf,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.GetLossUidListReq{
					LossUids: []uint32{1},
				},
			},
			want: &pb.GetLossUidListResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			got, err := mgr.GetLossUidList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.GetLossUidList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				//t.Errorf("UserRecallManager.GetLossUidList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserRecallManager_getValidRecallList(t *testing.T) {
	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx   context.Context
		opUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.LossUserInfo
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			got, err := mgr.getValidRecallList(tt.args.ctx, tt.args.opUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.getValidRecallList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UserRecallManager.getValidRecallList() = %v, want %v", got, tt.want)
			}
		})
	}
}

// go  test -timeout 30s -run ^TestUserRecallManager_getInviteAwardInfo$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_getInviteAwardInfo(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockuserrecallawardCli := mockuserrecallaward.NewMockUserRecallAwardClient(ctl)
	awardResp := &user_recall_award.BatchGetInviteAwardInfoResp{
		AwardInfos: []*user_recall_award.InviteAwardInfo{
			{
				RecalledUid: 2,
				AwardInfo: &user_recall_award.AwardInfo{
					GiftId:   1,
					GiftName: "1",
					GiftCnt:  1,
					GiftIcon: "1",
					GiftVal:  1,
				},
			},
		},
	}

	gomock.InOrder(
		mockuserrecallawardCli.EXPECT().BatchGetInviteAwardInfo(gomock.Any(), gomock.Any()).Return(awardResp, nil),
	)

	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx          context.Context
		opUid        uint32
		lossUserList []*pb.LossUserInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[uint32]*user_recall_award.AwardInfo
		wantErr bool
	}{
		{
			fields: fields{
				userRecallAwardClient: mockuserrecallawardCli,
			},
			args: args{
				ctx: context.Background(),
				lossUserList: []*pb.LossUserInfo{
					{
						Uid: 1,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			got, err := mgr.getInviteAwardInfo(tt.args.ctx, tt.args.opUid, tt.args.lossUserList)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.getInviteAwardInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				//t.Errorf("UserRecallManager.getInviteAwardInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

// go test -timeout 30s -run ^TestUserRecallManager_getBindUnfinishedList$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_getBindUnfinishedList(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockstorecli := mocks.NewMockIStore(ctl)
	mockcachecli := mocks.NewMockIRedisCache(ctl)

	bindList := []*model.UserRecallBind{
		{
			Uid: 1,
		},
	}
	lossinfo := &model.UserRecalledWhiteList{}
	coupe := &model.UserRecallCouple{
		ConsumeTbeanAmtCnt: 1,
	}
	gomock.InOrder(
		// SaveUserRecallWhiteList

		mockcachecli.EXPECT().GetBindUnfinished(gomock.Any()).Return(true, []byte{}, nil),

		mockcachecli.EXPECT().GetBindUnfinished(gomock.Any()).Return(false, []byte{}, nil),
		mockstorecli.EXPECT().GetUserRecallBindUnfinishedList(gomock.Any(), gomock.Any()).Return(bindList, nil),

		mockstorecli.EXPECT().GetUserRecalledWhiteListData(gomock.Any(), gomock.Any()).Return(lossinfo, nil),
		mockstorecli.EXPECT().GetUserRecallCoupleInfo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(coupe, nil),

		mockcachecli.EXPECT().SetBindUnfinished(gomock.Any(), gomock.Any()).Return(errors.New("")),

		//
	)

	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx   context.Context
		opUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.LossUserInfo
		wantErr bool
	}{
		{
			fields: fields{
				rc: mockcachecli,
			},
		},
		{
			fields: fields{
				rc:    mockcachecli,
				store: mockstorecli,
			},
			want: []*pb.LossUserInfo{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			got, err := mgr.getBindUnfinishedList(tt.args.ctx, tt.args.opUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.getBindUnfinishedList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				//t.Errorf("UserRecallManager.getBindUnfinishedList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserRecallManager_SetRecallWhite(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockstorecli := mocks.NewMockIStore(ctl)
	mockcachecli := mocks.NewMockIRedisCache(ctl)

	gomock.InOrder(
		// SaveUserRecallWhiteList
		mockstorecli.EXPECT().SaveUserRecallWhiteList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockcachecli.EXPECT().DelLossUidList(gomock.Any()).Return(nil),
	)

	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx       context.Context
		inviteUid uint32
		uid       uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			fields: fields{
				store: mockstorecli,
				rc:    mockcachecli,
			},
			args: args{
				ctx: context.Background(),
			},
		},
		{
			fields: fields{
				store: mockstorecli,
				rc:    mockcachecli,
			},
			args: args{
				ctx:       context.Background(),
				inviteUid: 1,
				uid:       1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			if err := mgr.SetRecallWhite(tt.args.ctx, tt.args.inviteUid, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.SetRecallWhite() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestUserRecallManager_DelRecallWhite(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockstorecli := mocks.NewMockIStore(ctl)
	mockcachecli := mocks.NewMockIRedisCache(ctl)

	gomock.InOrder(
		// SaveUserRecallWhiteList
		mockstorecli.EXPECT().DelUserRecallWhiteList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockcachecli.EXPECT().DelLossUidList(gomock.Any()).Return(nil),
	)
	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx       context.Context
		inviteUid uint32
		uid       uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			fields: fields{
				store: mockstorecli,
				rc:    mockcachecli,
			},
			args: args{
				ctx: context.Background(),
			},
		},
		{
			fields: fields{
				store: mockstorecli,
				rc:    mockcachecli,
			},
			args: args{
				ctx:       context.Background(),
				inviteUid: 1,
				uid:       1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			if err := mgr.DelRecallWhite(tt.args.ctx, tt.args.inviteUid, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.DelRecallWhite() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// go  test -timeout 30s -run ^TestUserRecallManager_GetRecallWhiteList$ golang.52tt.com/services/user-recall/internal/mgr -v -count=1
func TestUserRecallManager_GetRecallWhiteList(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockstorecli := mocks.NewMockIStore(ctl)

	list := []*model.UserRecallWhiteList{
		{
			Uid: 1,
		},
	}
	info := &model.UserRecalledWhiteList{}

	gomock.InOrder(
		mockstorecli.EXPECT().GetUserRecallWhiteListDataList(gomock.Any(), gomock.Any()).Return(list, nil),
		mockstorecli.EXPECT().GetUserRecalledWhiteListData(gomock.Any(), gomock.Any()).Return(info, nil),
	)

	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		ctx       context.Context
		inviteUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.LossUserInfo
		wantErr bool
	}{
		{
			fields: fields{
				store: mockstorecli,
			},
			args: args{
				ctx: context.Background(),
			},
			want: []*pb.LossUserInfo{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			got, err := mgr.GetRecallWhiteList(tt.args.ctx, tt.args.inviteUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserRecallManager.GetRecallWhiteList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				//t.Errorf("UserRecallManager.GetRecallWhiteList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserRecallManager_mergeRecallList(t *testing.T) {
	type fields struct {
		store                 model.IStore
		rc                    cache.IRedisCache
		report                *report.FeishuReporterV2
		dyconfig              conf.ISDyConfigHandler
		imApiClient           im_api.IClient
		SmsSvrClientCli       smsSvr.IClient
		AccountCli            account.IClient
		UgcfriendshipCli      friendship.IClient
		userRecallAwardClient user_recall_award.UserRecallAwardClient
	}
	type args struct {
		list1 []*pb.LossUserInfo
		list2 []*pb.LossUserInfo
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*pb.LossUserInfo
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &UserRecallManager{
				store:                 tt.fields.store,
				rc:                    tt.fields.rc,
				report:                tt.fields.report,
				dyconfig:              tt.fields.dyconfig,
				imApiClient:           tt.fields.imApiClient,
				SmsSvrClientCli:       tt.fields.SmsSvrClientCli,
				AccountCli:            tt.fields.AccountCli,
				UgcfriendshipCli:      tt.fields.UgcfriendshipCli,
				userRecallAwardClient: tt.fields.userRecallAwardClient,
			}
			if got := mgr.mergeRecallList(tt.args.list1, tt.args.list2); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UserRecallManager.mergeRecallList() = %v, want %v", got, tt.want)
			}
		})
	}
}
*/
