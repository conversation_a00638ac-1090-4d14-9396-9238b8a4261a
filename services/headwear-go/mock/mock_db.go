// Code generated by MockGen. DO NOT EDIT.
// Source: F:\quicksilver\services\headwear-go\mysql\mysql_api.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	gorm "github.com/jinzhu/gorm"
	headwear_go "golang.52tt.com/protocol/services/headwear-go"
	mysql "golang.52tt.com/services/headwear-go/mysql"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddHeadwearAwardOrder mocks base method.
func (m *MockIStore) AddHeadwearAwardOrder(ctx context.Context, tx *gorm.DB, now time.Time, orderId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddHeadwearAwardOrder", ctx, tx, now, orderId)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddHeadwearAwardOrder indicates an expected call of AddHeadwearAwardOrder.
func (mr *MockIStoreMockRecorder) AddHeadwearAwardOrder(ctx, tx, now, orderId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddHeadwearAwardOrder", reflect.TypeOf((*MockIStore)(nil).AddHeadwearAwardOrder), ctx, tx, now, orderId)
}

// AddHeadwearAwardOrderWithDetail mocks base method.
func (m *MockIStore) AddHeadwearAwardOrderWithDetail(ctx context.Context, tx *gorm.DB, now time.Time, orderId string, outsideTime time.Time, sourceType uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddHeadwearAwardOrderWithDetail", ctx, tx, now, orderId, outsideTime, sourceType)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddHeadwearAwardOrderWithDetail indicates an expected call of AddHeadwearAwardOrderWithDetail.
func (mr *MockIStoreMockRecorder) AddHeadwearAwardOrderWithDetail(ctx, tx, now, orderId, outsideTime, sourceType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddHeadwearAwardOrderWithDetail", reflect.TypeOf((*MockIStore)(nil).AddHeadwearAwardOrderWithDetail), ctx, tx, now, orderId, outsideTime, sourceType)
}

// AddHeadwearConfig mocks base method.
func (m *MockIStore) AddHeadwearConfig(info *headwear_go.HeadwearConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddHeadwearConfig", info)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddHeadwearConfig indicates an expected call of AddHeadwearConfig.
func (mr *MockIStoreMockRecorder) AddHeadwearConfig(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddHeadwearConfig", reflect.TypeOf((*MockIStore)(nil).AddHeadwearConfig), info)
}

// AddUserHeadwearExtraTime mocks base method.
func (m *MockIStore) AddUserHeadwearExtraTime(uid, extraTs uint32, suiteIdList []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserHeadwearExtraTime", uid, extraTs, suiteIdList)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUserHeadwearExtraTime indicates an expected call of AddUserHeadwearExtraTime.
func (mr *MockIStoreMockRecorder) AddUserHeadwearExtraTime(uid, extraTs, suiteIdList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserHeadwearExtraTime", reflect.TypeOf((*MockIStore)(nil).AddUserHeadwearExtraTime), uid, extraTs, suiteIdList)
}

// BatchGetHeadwearConfig mocks base method.
func (m *MockIStore) BatchGetHeadwearConfig(ctx context.Context, headwearIdList []uint32) ([]*headwear_go.HeadwearConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetHeadwearConfig", ctx, headwearIdList)
	ret0, _ := ret[0].([]*headwear_go.HeadwearConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetHeadwearConfig indicates an expected call of BatchGetHeadwearConfig.
func (mr *MockIStoreMockRecorder) BatchGetHeadwearConfig(ctx, headwearIdList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetHeadwearConfig", reflect.TypeOf((*MockIStore)(nil).BatchGetHeadwearConfig), ctx, headwearIdList)
}

// BatchGetUserHeadwearCustomText mocks base method.
func (m *MockIStore) BatchGetUserHeadwearCustomText(ctx context.Context, uidList []uint32) ([]*mysql.UserHeadwearCustomText, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserHeadwearCustomText", ctx, uidList)
	ret0, _ := ret[0].([]*mysql.UserHeadwearCustomText)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserHeadwearCustomText indicates an expected call of BatchGetUserHeadwearCustomText.
func (mr *MockIStoreMockRecorder) BatchGetUserHeadwearCustomText(ctx, uidList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserHeadwearCustomText", reflect.TypeOf((*MockIStore)(nil).BatchGetUserHeadwearCustomText), ctx, uidList)
}

// BatchGetUserHeadwearInUse mocks base method.
func (m *MockIStore) BatchGetUserHeadwearInUse(ctx context.Context, uidList []uint32) (map[uint32]*mysql.UserHeadwearInuse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserHeadwearInUse", ctx, uidList)
	ret0, _ := ret[0].(map[uint32]*mysql.UserHeadwearInuse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserHeadwearInUse indicates an expected call of BatchGetUserHeadwearInUse.
func (mr *MockIStoreMockRecorder) BatchGetUserHeadwearInUse(ctx, uidList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserHeadwearInUse", reflect.TypeOf((*MockIStore)(nil).BatchGetUserHeadwearInUse), ctx, uidList)
}

// CheckIfOrderIdExist mocks base method.
func (m *MockIStore) CheckIfOrderIdExist(ctx context.Context, month uint32, orderId string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfOrderIdExist", ctx, month, orderId)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckIfOrderIdExist indicates an expected call of CheckIfOrderIdExist.
func (mr *MockIStoreMockRecorder) CheckIfOrderIdExist(ctx, month, orderId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfOrderIdExist", reflect.TypeOf((*MockIStore)(nil).CheckIfOrderIdExist), ctx, month, orderId)
}

// CreateHeadwearOrderTable mocks base method.
func (m *MockIStore) CreateHeadwearOrderTable(month uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateHeadwearOrderTable", month)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateHeadwearOrderTable indicates an expected call of CreateHeadwearOrderTable.
func (mr *MockIStoreMockRecorder) CreateHeadwearOrderTable(month interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateHeadwearOrderTable", reflect.TypeOf((*MockIStore)(nil).CreateHeadwearOrderTable), month)
}

// DelHeadwearConfig mocks base method.
func (m *MockIStore) DelHeadwearConfig(headwearId uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelHeadwearConfig", headwearId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelHeadwearConfig indicates an expected call of DelHeadwearConfig.
func (mr *MockIStoreMockRecorder) DelHeadwearConfig(headwearId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelHeadwearConfig", reflect.TypeOf((*MockIStore)(nil).DelHeadwearConfig), headwearId)
}

// DelUserHeadwearCustomTextById mocks base method.
func (m *MockIStore) DelUserHeadwearCustomTextById(ctx context.Context, uid, headwaerId, cpUid uint32, customText string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserHeadwearCustomTextById", ctx, uid, headwaerId, cpUid, customText)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserHeadwearCustomTextById indicates an expected call of DelUserHeadwearCustomTextById.
func (mr *MockIStoreMockRecorder) DelUserHeadwearCustomTextById(ctx, uid, headwaerId, cpUid, customText interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserHeadwearCustomTextById", reflect.TypeOf((*MockIStore)(nil).DelUserHeadwearCustomTextById), ctx, uid, headwaerId, cpUid, customText)
}

// DropHeadwearOrderTable mocks base method.
func (m *MockIStore) DropHeadwearOrderTable(month uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DropHeadwearOrderTable", month)
	ret0, _ := ret[0].(error)
	return ret0
}

// DropHeadwearOrderTable indicates an expected call of DropHeadwearOrderTable.
func (mr *MockIStoreMockRecorder) DropHeadwearOrderTable(month interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DropHeadwearOrderTable", reflect.TypeOf((*MockIStore)(nil).DropHeadwearOrderTable), month)
}

// GetHeadwearConfigAll mocks base method.
func (m *MockIStore) GetHeadwearConfigAll(ctx context.Context) ([]*headwear_go.HeadwearConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHeadwearConfigAll", ctx)
	ret0, _ := ret[0].([]*headwear_go.HeadwearConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHeadwearConfigAll indicates an expected call of GetHeadwearConfigAll.
func (mr *MockIStoreMockRecorder) GetHeadwearConfigAll(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeadwearConfigAll", reflect.TypeOf((*MockIStore)(nil).GetHeadwearConfigAll), ctx)
}

// GetHeadwearConfigById mocks base method.
func (m *MockIStore) GetHeadwearConfigById(ctx context.Context, headwearId uint32) (*headwear_go.HeadwearConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHeadwearConfigById", ctx, headwearId)
	ret0, _ := ret[0].(*headwear_go.HeadwearConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHeadwearConfigById indicates an expected call of GetHeadwearConfigById.
func (mr *MockIStoreMockRecorder) GetHeadwearConfigById(ctx, headwearId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeadwearConfigById", reflect.TypeOf((*MockIStore)(nil).GetHeadwearConfigById), ctx, headwearId)
}

// GetHeadwearConfigBySuiteLv mocks base method.
func (m *MockIStore) GetHeadwearConfigBySuiteLv(ctx context.Context, suiteId, level uint32) (*headwear_go.HeadwearConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHeadwearConfigBySuiteLv", ctx, suiteId, level)
	ret0, _ := ret[0].(*headwear_go.HeadwearConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHeadwearConfigBySuiteLv indicates an expected call of GetHeadwearConfigBySuiteLv.
func (mr *MockIStoreMockRecorder) GetHeadwearConfigBySuiteLv(ctx, suiteId, level interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeadwearConfigBySuiteLv", reflect.TypeOf((*MockIStore)(nil).GetHeadwearConfigBySuiteLv), ctx, suiteId, level)
}

// GetLastSuiteId mocks base method.
func (m *MockIStore) GetLastSuiteId(ctx context.Context, min, max uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastSuiteId", ctx, min, max)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastSuiteId indicates an expected call of GetLastSuiteId.
func (mr *MockIStoreMockRecorder) GetLastSuiteId(ctx, min, max interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastSuiteId", reflect.TypeOf((*MockIStore)(nil).GetLastSuiteId), ctx, min, max)
}

// GetTotalOrderCountBySource mocks base method.
func (m *MockIStore) GetTotalOrderCountBySource(ctx context.Context, begin, end time.Time, sourceType uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTotalOrderCountBySource", ctx, begin, end, sourceType)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTotalOrderCountBySource indicates an expected call of GetTotalOrderCountBySource.
func (mr *MockIStoreMockRecorder) GetTotalOrderCountBySource(ctx, begin, end, sourceType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTotalOrderCountBySource", reflect.TypeOf((*MockIStore)(nil).GetTotalOrderCountBySource), ctx, begin, end, sourceType)
}

// GetTotalOrderListBySource mocks base method.
func (m *MockIStore) GetTotalOrderListBySource(ctx context.Context, begin, end time.Time, sourceType uint32) ([]*mysql.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTotalOrderListBySource", ctx, begin, end, sourceType)
	ret0, _ := ret[0].([]*mysql.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTotalOrderListBySource indicates an expected call of GetTotalOrderListBySource.
func (mr *MockIStoreMockRecorder) GetTotalOrderListBySource(ctx, begin, end, sourceType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTotalOrderListBySource", reflect.TypeOf((*MockIStore)(nil).GetTotalOrderListBySource), ctx, begin, end, sourceType)
}

// GetUserCPHeadwearById mocks base method.
func (m *MockIStore) GetUserCPHeadwearById(ctx context.Context, uid, headwearId, cpUid uint32, customText string) (*mysql.UserHeadwearInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserCPHeadwearById", ctx, uid, headwearId, cpUid, customText)
	ret0, _ := ret[0].(*mysql.UserHeadwearInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserCPHeadwearById indicates an expected call of GetUserCPHeadwearById.
func (mr *MockIStoreMockRecorder) GetUserCPHeadwearById(ctx, uid, headwearId, cpUid, customText interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCPHeadwearById", reflect.TypeOf((*MockIStore)(nil).GetUserCPHeadwearById), ctx, uid, headwearId, cpUid, customText)
}

// GetUserCPHeadwearBySuite mocks base method.
func (m *MockIStore) GetUserCPHeadwearBySuite(ctx context.Context, uid, suiteId, cpUid uint32, customText string) (*mysql.UserHeadwearInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserCPHeadwearBySuite", ctx, uid, suiteId, cpUid, customText)
	ret0, _ := ret[0].(*mysql.UserHeadwearInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserCPHeadwearBySuite indicates an expected call of GetUserCPHeadwearBySuite.
func (mr *MockIStoreMockRecorder) GetUserCPHeadwearBySuite(ctx, uid, suiteId, cpUid, customText interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCPHeadwearBySuite", reflect.TypeOf((*MockIStore)(nil).GetUserCPHeadwearBySuite), ctx, uid, suiteId, cpUid, customText)
}

// GetUserCPHeadwearBySuiteForUpdate mocks base method.
func (m *MockIStore) GetUserCPHeadwearBySuiteForUpdate(ctx context.Context, tx *gorm.DB, uid, suiteId, cpUid uint32, customText string) (*mysql.UserHeadwearInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserCPHeadwearBySuiteForUpdate", ctx, tx, uid, suiteId, cpUid, customText)
	ret0, _ := ret[0].(*mysql.UserHeadwearInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserCPHeadwearBySuiteForUpdate indicates an expected call of GetUserCPHeadwearBySuiteForUpdate.
func (mr *MockIStoreMockRecorder) GetUserCPHeadwearBySuiteForUpdate(ctx, tx, uid, suiteId, cpUid, customText interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCPHeadwearBySuiteForUpdate", reflect.TypeOf((*MockIStore)(nil).GetUserCPHeadwearBySuiteForUpdate), ctx, tx, uid, suiteId, cpUid, customText)
}

// GetUserCPHeadwearList mocks base method.
func (m *MockIStore) GetUserCPHeadwearList(ctx context.Context, uid uint32) ([]*mysql.UserHeadwearInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserCPHeadwearList", ctx, uid)
	ret0, _ := ret[0].([]*mysql.UserHeadwearInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserCPHeadwearList indicates an expected call of GetUserCPHeadwearList.
func (mr *MockIStoreMockRecorder) GetUserCPHeadwearList(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCPHeadwearList", reflect.TypeOf((*MockIStore)(nil).GetUserCPHeadwearList), ctx, uid)
}

// GetUserExpiredCPHeadwearByTable mocks base method.
func (m *MockIStore) GetUserExpiredCPHeadwearByTable(ctx context.Context, t time.Time) ([]*mysql.UserHeadwearInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserExpiredCPHeadwearByTable", ctx, t)
	ret0, _ := ret[0].([]*mysql.UserHeadwearInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExpiredCPHeadwearByTable indicates an expected call of GetUserExpiredCPHeadwearByTable.
func (mr *MockIStoreMockRecorder) GetUserExpiredCPHeadwearByTable(ctx, t interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExpiredCPHeadwearByTable", reflect.TypeOf((*MockIStore)(nil).GetUserExpiredCPHeadwearByTable), ctx, t)
}

// GetUserExpiredHeadwearByTable mocks base method.
func (m *MockIStore) GetUserExpiredHeadwearByTable(ctx context.Context, idx uint32, t time.Time) ([]*mysql.UserHeadwearInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserExpiredHeadwearByTable", ctx, idx, t)
	ret0, _ := ret[0].([]*mysql.UserHeadwearInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExpiredHeadwearByTable indicates an expected call of GetUserExpiredHeadwearByTable.
func (mr *MockIStoreMockRecorder) GetUserExpiredHeadwearByTable(ctx, idx, t interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExpiredHeadwearByTable", reflect.TypeOf((*MockIStore)(nil).GetUserExpiredHeadwearByTable), ctx, idx, t)
}

// GetUserHeadwearById mocks base method.
func (m *MockIStore) GetUserHeadwearById(ctx context.Context, uid, headwearId uint32, customText string) (*mysql.UserHeadwearInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserHeadwearById", ctx, uid, headwearId, customText)
	ret0, _ := ret[0].(*mysql.UserHeadwearInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserHeadwearById indicates an expected call of GetUserHeadwearById.
func (mr *MockIStoreMockRecorder) GetUserHeadwearById(ctx, uid, headwearId, customText interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserHeadwearById", reflect.TypeOf((*MockIStore)(nil).GetUserHeadwearById), ctx, uid, headwearId, customText)
}

// GetUserHeadwearBySuite mocks base method.
func (m *MockIStore) GetUserHeadwearBySuite(ctx context.Context, uid, suiteId uint32, customText string) (*mysql.UserHeadwearInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserHeadwearBySuite", ctx, uid, suiteId, customText)
	ret0, _ := ret[0].(*mysql.UserHeadwearInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserHeadwearBySuite indicates an expected call of GetUserHeadwearBySuite.
func (mr *MockIStoreMockRecorder) GetUserHeadwearBySuite(ctx, uid, suiteId, customText interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserHeadwearBySuite", reflect.TypeOf((*MockIStore)(nil).GetUserHeadwearBySuite), ctx, uid, suiteId, customText)
}

// GetUserHeadwearBySuiteForUpdate mocks base method.
func (m *MockIStore) GetUserHeadwearBySuiteForUpdate(ctx context.Context, tx *gorm.DB, uid, suiteId uint32, customText string) (*mysql.UserHeadwearInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserHeadwearBySuiteForUpdate", ctx, tx, uid, suiteId, customText)
	ret0, _ := ret[0].(*mysql.UserHeadwearInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserHeadwearBySuiteForUpdate indicates an expected call of GetUserHeadwearBySuiteForUpdate.
func (mr *MockIStoreMockRecorder) GetUserHeadwearBySuiteForUpdate(ctx, tx, uid, suiteId, customText interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserHeadwearBySuiteForUpdate", reflect.TypeOf((*MockIStore)(nil).GetUserHeadwearBySuiteForUpdate), ctx, tx, uid, suiteId, customText)
}

// GetUserHeadwearCustomText mocks base method.
func (m *MockIStore) GetUserHeadwearCustomText(ctx context.Context, uid uint32) ([]*mysql.UserHeadwearCustomText, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserHeadwearCustomText", ctx, uid)
	ret0, _ := ret[0].([]*mysql.UserHeadwearCustomText)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserHeadwearCustomText indicates an expected call of GetUserHeadwearCustomText.
func (mr *MockIStoreMockRecorder) GetUserHeadwearCustomText(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserHeadwearCustomText", reflect.TypeOf((*MockIStore)(nil).GetUserHeadwearCustomText), ctx, uid)
}

// GetUserHeadwearCustomTextById mocks base method.
func (m *MockIStore) GetUserHeadwearCustomTextById(ctx context.Context, id uint32) (*mysql.UserHeadwearCustomText, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserHeadwearCustomTextById", ctx, id)
	ret0, _ := ret[0].(*mysql.UserHeadwearCustomText)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserHeadwearCustomTextById indicates an expected call of GetUserHeadwearCustomTextById.
func (mr *MockIStoreMockRecorder) GetUserHeadwearCustomTextById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserHeadwearCustomTextById", reflect.TypeOf((*MockIStore)(nil).GetUserHeadwearCustomTextById), ctx, id)
}

// GetUserHeadwearCustomTextByInfo mocks base method.
func (m *MockIStore) GetUserHeadwearCustomTextByInfo(ctx context.Context, uid, headwaerId, cpUid uint32, customText string) (*mysql.UserHeadwearCustomText, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserHeadwearCustomTextByInfo", ctx, uid, headwaerId, cpUid, customText)
	ret0, _ := ret[0].(*mysql.UserHeadwearCustomText)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserHeadwearCustomTextByInfo indicates an expected call of GetUserHeadwearCustomTextByInfo.
func (mr *MockIStoreMockRecorder) GetUserHeadwearCustomTextByInfo(ctx, uid, headwaerId, cpUid, customText interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserHeadwearCustomTextByInfo", reflect.TypeOf((*MockIStore)(nil).GetUserHeadwearCustomTextByInfo), ctx, uid, headwaerId, cpUid, customText)
}

// GetUserHeadwearInUse mocks base method.
func (m *MockIStore) GetUserHeadwearInUse(ctx context.Context, uid uint32) (*mysql.UserHeadwearInuse, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserHeadwearInUse", ctx, uid)
	ret0, _ := ret[0].(*mysql.UserHeadwearInuse)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserHeadwearInUse indicates an expected call of GetUserHeadwearInUse.
func (mr *MockIStoreMockRecorder) GetUserHeadwearInUse(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserHeadwearInUse", reflect.TypeOf((*MockIStore)(nil).GetUserHeadwearInUse), ctx, uid)
}

// GetUserHeadwearList mocks base method.
func (m *MockIStore) GetUserHeadwearList(ctx context.Context, uid uint32) ([]*mysql.UserHeadwearInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserHeadwearList", ctx, uid)
	ret0, _ := ret[0].([]*mysql.UserHeadwearInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserHeadwearList indicates an expected call of GetUserHeadwearList.
func (mr *MockIStoreMockRecorder) GetUserHeadwearList(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserHeadwearList", reflect.TypeOf((*MockIStore)(nil).GetUserHeadwearList), ctx, uid)
}

// GiveCPHeadwearToUser mocks base method.
func (m *MockIStore) GiveCPHeadwearToUser(tx *gorm.DB, uid, cpUid, headwearId, suiteId uint32, expiredTime time.Time, customText string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GiveCPHeadwearToUser", tx, uid, cpUid, headwearId, suiteId, expiredTime, customText)
	ret0, _ := ret[0].(error)
	return ret0
}

// GiveCPHeadwearToUser indicates an expected call of GiveCPHeadwearToUser.
func (mr *MockIStoreMockRecorder) GiveCPHeadwearToUser(tx, uid, cpUid, headwearId, suiteId, expiredTime, customText interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GiveCPHeadwearToUser", reflect.TypeOf((*MockIStore)(nil).GiveCPHeadwearToUser), tx, uid, cpUid, headwearId, suiteId, expiredTime, customText)
}

// GiveHeadwearToUser mocks base method.
func (m *MockIStore) GiveHeadwearToUser(tx *gorm.DB, uid, headwearId, suiteId, useType uint32, expiredTime time.Time, customText string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GiveHeadwearToUser", tx, uid, headwearId, suiteId, useType, expiredTime, customText)
	ret0, _ := ret[0].(error)
	return ret0
}

// GiveHeadwearToUser indicates an expected call of GiveHeadwearToUser.
func (mr *MockIStoreMockRecorder) GiveHeadwearToUser(tx, uid, headwearId, suiteId, useType, expiredTime, customText interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GiveHeadwearToUser", reflect.TypeOf((*MockIStore)(nil).GiveHeadwearToUser), tx, uid, headwearId, suiteId, useType, expiredTime, customText)
}

// InitTable mocks base method.
func (m *MockIStore) InitTable() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "InitTable")
}

// InitTable indicates an expected call of InitTable.
func (mr *MockIStoreMockRecorder) InitTable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitTable", reflect.TypeOf((*MockIStore)(nil).InitTable))
}

// RemoveUserHeadwearInUse mocks base method.
func (m *MockIStore) RemoveUserHeadwearInUse(uid uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveUserHeadwearInUse", uid)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveUserHeadwearInUse indicates an expected call of RemoveUserHeadwearInUse.
func (mr *MockIStoreMockRecorder) RemoveUserHeadwearInUse(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveUserHeadwearInUse", reflect.TypeOf((*MockIStore)(nil).RemoveUserHeadwearInUse), uid)
}

// SaveHeadwearCustomText mocks base method.
func (m *MockIStore) SaveHeadwearCustomText(tx *gorm.DB, customText *mysql.UserHeadwearCustomText) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveHeadwearCustomText", tx, customText)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveHeadwearCustomText indicates an expected call of SaveHeadwearCustomText.
func (mr *MockIStoreMockRecorder) SaveHeadwearCustomText(tx, customText interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveHeadwearCustomText", reflect.TypeOf((*MockIStore)(nil).SaveHeadwearCustomText), tx, customText)
}

// SetUserHeadwearInUse mocks base method.
func (m *MockIStore) SetUserHeadwearInUse(tx *gorm.DB, uid, headwearId, cpUid uint32, customText string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserHeadwearInUse", tx, uid, headwearId, cpUid, customText)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserHeadwearInUse indicates an expected call of SetUserHeadwearInUse.
func (mr *MockIStoreMockRecorder) SetUserHeadwearInUse(tx, uid, headwearId, cpUid, customText interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserHeadwearInUse", reflect.TypeOf((*MockIStore)(nil).SetUserHeadwearInUse), tx, uid, headwearId, cpUid, customText)
}

// Transaction mocks base method.
func (m *MockIStore) Transaction(ctx context.Context, f func(*gorm.DB) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transaction", ctx, f)
	ret0, _ := ret[0].(error)
	return ret0
}

// Transaction indicates an expected call of Transaction.
func (mr *MockIStoreMockRecorder) Transaction(ctx, f interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transaction", reflect.TypeOf((*MockIStore)(nil).Transaction), ctx, f)
}

// UpdateHeadwearConfig mocks base method.
func (m *MockIStore) UpdateHeadwearConfig(ctx context.Context, infos []*headwear_go.HeadwearConfig) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateHeadwearConfig", ctx, infos)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateHeadwearConfig indicates an expected call of UpdateHeadwearConfig.
func (mr *MockIStoreMockRecorder) UpdateHeadwearConfig(ctx, infos interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateHeadwearConfig", reflect.TypeOf((*MockIStore)(nil).UpdateHeadwearConfig), ctx, infos)
}

// UpdateUserHeadwearCustomTextResultById mocks base method.
func (m *MockIStore) UpdateUserHeadwearCustomTextResultById(ctx context.Context, id uint32, ok bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserHeadwearCustomTextResultById", ctx, id, ok)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserHeadwearCustomTextResultById indicates an expected call of UpdateUserHeadwearCustomTextResultById.
func (mr *MockIStoreMockRecorder) UpdateUserHeadwearCustomTextResultById(ctx, id, ok interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserHeadwearCustomTextResultById", reflect.TypeOf((*MockIStore)(nil).UpdateUserHeadwearCustomTextResultById), ctx, id, ok)
}
