package cache

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/opentracing/opentracing-go"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/store/redis"
	"golang.52tt.com/services/youknowwho/common/model"
	"golang.52tt.com/services/youknowwho/common/numberpool"
)

func getMockCache(t *testing.T) (*miniredis.Miniredis, *redis.Client) {
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	return s, rdb
}

func TestCache_DelNumberList(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()

	poolName := "normal"
	now := time.Now()
	key := fmt.Sprintf("%s_%s_%s", RedisNumberPoolKeyPrefix, poolName, now.Format("20060102"))
	_, err := s.Lpush(key, "10086")
	if err != nil {
		t.Fatal(err)
	}

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx      context.Context
		delTime  time.Time
		poolName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "REDIS numberpool删除号码池",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:      ctx,
				delTime:  now,
				poolName: poolName,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if err := c.DelNumberList(tt.args.ctx, tt.args.delTime, tt.args.poolName); (err != nil) != tt.wantErr {
				t.Errorf("DelNumberList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCache_DelExtraNum(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()
	now := time.Now()

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
		t   time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "REDIS 测试Redis去掉额外号码池",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				t:   now,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if err := c.DelExtraNum(tt.args.ctx, tt.args.t); (err != nil) != tt.wantErr {
				t.Errorf("DelExtraNum() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCache_GetNumber(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()

	poolName := "normal"
	now := time.Now()
	key := fmt.Sprintf("%s_%s_%s", RedisNumberPoolKeyPrefix, poolName, now.Format("20060102"))
	_, err := s.Lpush(key, "10086")
	if err != nil {
		t.Fatal(err)
	}

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx      context.Context
		nowTime  time.Time
		poolName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "REDIS测试从redis获取号码池号码",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:      ctx,
				nowTime:  now,
				poolName: poolName,
			},
			want:    10086,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := c.GetNumber(tt.args.ctx, tt.args.nowTime, tt.args.poolName)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNumber() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetNumber() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCache_GetNumberLen(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()

	poolName := "normal"
	now := time.Now()
	key := fmt.Sprintf("%s_%s_%s", RedisNumberPoolKeyPrefix, poolName, now.Format("20060102"))
	_, err := s.Lpush(key, "10086")
	if err != nil {
		t.Fatal(err)
	}

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx      context.Context
		nowTime  time.Time
		poolName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "REDIS 测试从redis获取号码池长度",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:      ctx,
				nowTime:  now,
				poolName: poolName,
			},
			wantErr: false,
			want:    1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := c.GetNumberLen(tt.args.ctx, tt.args.nowTime, tt.args.poolName)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNumberLen() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetNumberLen() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCache_GetExtraNum(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()
	now := time.Now()

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
		t   time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "REDIS 获取扩充号码池号码",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				t:   now,
			},
			wantErr: false,
			want:    1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := c.GetExtraNum(tt.args.ctx, tt.args.t)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExtraNum() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetExtraNum() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_SetNumbers(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()

	// 准备数据
	now := time.Now()

	type fields struct {
		redisClient *redis.Client
	}
	type args struct {
		ctx      context.Context
		nowTime  time.Time
		poolName string
		numbers  []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:      ctx,
				nowTime:  now,
				poolName: numberpool.LEVEL_NORMAL,
				numbers:  []uint32{12345, 23456, 34567, 45678, 56789},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Cache{
				redisClient: tt.fields.redisClient,
			}
			if err := s.SetNumbers(tt.args.ctx, tt.args.nowTime, tt.args.poolName, tt.args.numbers); (err != nil) != tt.wantErr {
				t.Errorf("SetNumbers() error = %v, wantErr %v", err, tt.wantErr)
			}
			key := fmt.Sprintf("%s_%s_%s", RedisNumberPoolKeyPrefix, tt.args.poolName, tt.args.nowTime.Format("20060102"))
			t.Log(key)
			infos, err := s.redisClient.LRange(key, 0, -1).Result()
			if err != nil {
				t.Errorf("SetNumbers() error = %v, wantErr %v", err, tt.wantErr)
			}
			t.Log(infos)
		})
	}
}

func TestCache_SetLock(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx     context.Context
		key     string
		timeout time.Duration
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "REDIS 设置redis锁",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:     ctx,
				key:     "测试",
				timeout: time.Minute,
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := c.SetLock(tt.args.ctx, tt.args.key, tt.args.timeout)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetLock() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("SetLock() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCache_GetLockWithKey(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()
	now := time.Now()

	key := "测试"
	timeStr := now.String()
	err := s.Set(key, timeStr)
	if err != nil {
		t.Fatal(err)
	}

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
		key string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "REDIS 测试Redis获取锁内容",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				key: key,
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := c.GetLockWithKey(tt.args.ctx, tt.args.key)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLockWithKey() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetLockWithKey() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCache_Lock(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx      context.Context
		key      string
		duration time.Duration
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "REDIS 设置锁",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:      ctx,
				key:      "测试",
				duration: time.Minute,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if got := c.Lock(tt.args.ctx, tt.args.key, tt.args.duration); got != tt.want {
				t.Errorf("Lock() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCache_UnLock(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
		key string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "REDIS 删除锁",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				key: "测试",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if err := c.UnLock(tt.args.ctx, tt.args.key); (err != nil) != tt.wantErr {
				t.Errorf("UnLock() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCache_UpdateUKWStatusCacheInfo(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.HSet("You-Know-Who:status", "2208646", "test")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	info := &model.UKWCachePermissionInfo{
		Uid:           2208646,
		FakeUid:       23456,
		Nickname:      "神秘人5922",
		Switch:        1,
		Status:        1,
		EffectiveTime: 2592000,
		RankSwitch:    0,
	}

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx  context.Context
		info *model.UKWCachePermissionInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "REDIS 测试更新神秘人缓存状态",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:  ctx,
				info: info,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if err := c.UpdateUKWStatusCacheInfo(tt.args.ctx, tt.args.info); (err != nil) != tt.wantErr {
				t.Errorf("UpdateUKWStatusCacheInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCache_UpdateUKWFakeUidCacheInfo(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.HSet("You-Know-Who:fake-uid", "23456", "hehe")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	info := &model.UKWCacheFakeUidInfo{
		Uid:     12345,
		FakeUid: 23456,
	}

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx  context.Context
		info *model.UKWCacheFakeUidInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "REDIS 测试redis更新假uid到真uid的映射",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:  ctx,
				info: info,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if err := c.UpdateUKWFakeUidCacheInfo(tt.args.ctx, tt.args.info); (err != nil) != tt.wantErr {
				t.Errorf("UpdateUKWFakeUidCacheInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestYouKnowWhoCache_GetFakeUidUpdateFinishedTime(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_UPDATE_FAKE_UID_KEY, now.Format("20060102"))
	s.Set(key, "12345")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
		now time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "测试redis获取假uid更新完成锁",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				now: now,
			},
			want:    "12345",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := y.GetFakeUidUpdateFinishedTime(tt.args.ctx, tt.args.now)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFakeUidUpdateFinishedTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetFakeUidUpdateFinishedTime() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_SetFakeUidUpdateFinishedTime(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_UPDATE_FAKE_UID_KEY, now.Format("20060102"))

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
		now time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "设置redis神秘人刷新假uid完成锁",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				now: now,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if err := y.SetFakeUidUpdateFinishedTime(tt.args.ctx, tt.args.now); (err != nil) != tt.wantErr {
				t.Errorf("SetFakeUidUpdateFinishedTime() error = %v, wantErr %v", err, tt.wantErr)
			}
			if r, err := y.redisClient.Get(key).Result(); r != now.String() || err != nil {
				t.Errorf("SetFakeUidUpdateFinishedTime failed, want:[%+v], get:[%+v]", now.String(), r)
			}
		})
	}
}

func TestYouKnowWhoCache_GetAllUKWInfoUpdateInfo(t *testing.T) {
	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_UPDATE_INFO_KEY, now.Format("20060102"))
	s.HSet(key, "12345", "12345")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []uint32
		wantErr bool
	}{
		{
			name: "测试redis获取今日已经刷新过假uid和昵称的uid列表",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
			},
			want:    []uint32{12345},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := y.GetAllUKWInfoUpdateInfo(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllUKWInfoUpdateInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllUKWInfoUpdateInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_GetFakeUidPoolLen(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.Lpush("You-Know-Who:fake-uid-pool", "666666")
	s.Lpush("You-Know-Who:fake-uid-pool", "777777")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "测试redis获取假uid号码池长度",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
			},
			want:    2,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := y.GetFakeUidPoolLen(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFakeUidPoolLen() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetFakeUidPoolLen() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_SetFakeUidList(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	uid := uint32(12345)
	s.Lpush("You-Know-Who:fake-uid-pool", "666666")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx         context.Context
		fakeUidList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试redis神秘人假uid池子添加号码",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:         ctx,
				fakeUidList: []uint32{uid},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if err := y.SetFakeUidList(tt.args.ctx, tt.args.fakeUidList); (err != nil) != tt.wantErr {
				t.Errorf("SetFakeUidList() error = %v, wantErr %v", err, tt.wantErr)
			}
			if num, err := y.redisClient.RPop("You-Know-Who:fake-uid-pool").Result(); num != "666666" || err != nil {
				t.Errorf("SetFakeUidList() want = %v, got %v", "666666", num)
			}
			if num, err := y.redisClient.RPop("You-Know-Who:fake-uid-pool").Result(); num != fmt.Sprintf("%+v", uid) || err != nil {
				t.Errorf("SetFakeUidList() want = %v, got %v", uid, num)
			}
		})
	}
}

func TestYouKnowWhoCache_DelUKWFakeUidCacheInfo(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.HSet("You-Know-Who:fake-uid", "23456", "hehe")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx     context.Context
		fakeUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试删除redis假uid到uid映射关系",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:     ctx,
				fakeUid: 23456,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if err := y.DelUKWFakeUidCacheInfo(tt.args.ctx, tt.args.fakeUid); (err != nil) != tt.wantErr {
				t.Errorf("DelUKWFakeUidCacheInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestYouKnowWhoCache_GetAllFakeUidPoolUidList(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.Lpush("You-Know-Who:fake-uid-pool", "666666")
	s.Lpush("You-Know-Who:fake-uid-pool", "777777")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "测试redis获取神秘人假uid池中的所有uid",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
			},
			want:    []string{"777777", "666666"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := y.GetAllFakeUidPoolUidList(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllFakeUidPoolUidList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllFakeUidPoolUidList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_GetFakeUid(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.Lpush("You-Know-Who:fake-uid-pool", "666666")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "测试redis获取神秘人假uid号码池号码",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
			},
			wantErr: false,
			want:    666666,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := y.GetFakeUid(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFakeUid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetFakeUid() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_UpdateUKWInfoUpdate(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_UPDATE_INFO_KEY, now.Format("20060102"))
	s.HSet(key, "12345", "hehe")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试redis更新今日已经更新的uid列表",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				uid: 12345,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if err := y.UpdateUKWInfoUpdate(tt.args.ctx, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("UpdateUKWInfoUpdate() error = %v, wantErr %v", err, tt.wantErr)
			}
			r, _ := y.redisClient.HGet(key, "12345").Result()
			if r != "12345" {
				t.Errorf("UpdateUKWInfoUpdate failed")
			}
		})
	}
}

func TestYouKnowWhoCache_GetUKWInfoUpdateInfo(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_UPDATE_INFO_KEY, now.Format("20060102"))
	s.HSet(key, "12345", "12345")
	s.HSet(key, "12346", "12346")
	s.HSet(key, "12347", "12347")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "判断redis根据指定uid获取是否有更新过假uid和昵称",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				uid: 12347,
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := y.GetUKWInfoUpdateInfo(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUKWInfoUpdateInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetUKWInfoUpdateInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_GetFakePoolUpdateFinishedTime(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_FAKE_POOL_FINISHED_KEY, now.Format("20060102"))
	s.Set(key, "12345")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
		now time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "测试redis获取神秘人假uid号码池刷新完毕时间",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				now: now,
			},
			wantErr: false,
			want:    "12345",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := y.GetFakePoolUpdateFinishedTime(tt.args.ctx, tt.args.now)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFakePoolUpdateFinishedTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetFakePoolUpdateFinishedTime() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_SetFakePoolUpdateFinishedTime(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_FAKE_POOL_FINISHED_KEY, now.Format("20060102"))

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
		now time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试设置redis神秘人假uid号码池刷新完成时间",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				now: now,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if err := y.SetFakePoolUpdateFinishedTime(tt.args.ctx, tt.args.now); (err != nil) != tt.wantErr {
				t.Errorf("SetFakePoolUpdateFinishedTime() error = %v, wantErr %v", err, tt.wantErr)
			}
			if r, err := y.redisClient.Get(key).Result(); r != now.String() || err != nil {
				t.Errorf("SetFakePoolUpdateFinishedTime failed, want:[%+v], get:[%+v]", now.String(), r)
			}
		})
	}
}

func TestYouKnowWhoCache_ClearShowUpMsgList(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx       context.Context
		uid       uint32
		channelId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "REDIS清除好奇信息",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:       ctx,
				uid:       12345,
				channelId: 10000,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if err := c.ClearShowUpMsgList(tt.args.ctx, tt.args.uid, tt.args.channelId); (err != nil) != tt.wantErr {
				t.Errorf("ClearShowUpMsgList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestYouKnowWhoCache_GetRankSwitchLock(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	uid := uint32(12345)
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_RANK_SWITCH_LOCK, uid)
	s.Set(key, "12345")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "测试redis获取神秘人排行榜切换时间锁",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				uid: uid,
			},
			wantErr: false,
			want:    false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := y.GetRankSwitchLock(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRankSwitchLock() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetRankSwitchLock() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_SetRankSwitchLock(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	uid := uint32(12345)
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_RANK_SWITCH_LOCK, uid)

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx        context.Context
		uid        uint32
		expireTime int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试redis设置神秘人切换排行榜开关锁",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:        ctx,
				uid:        uid,
				expireTime: 66666,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &Cache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if err := y.SetRankSwitchLock(tt.args.ctx, tt.args.uid, tt.args.expireTime); (err != nil) != tt.wantErr {
				t.Errorf("SetRankSwitchLock() error = %v, wantErr %v", err, tt.wantErr)
			}
			if r, err := y.redisClient.Get(key).Result(); r != fmt.Sprintf("%+v", uid) || err != nil {
				t.Errorf("SetFakePoolUpdateFinishedTime failed, want:[%+v], get:[%+v]", uid, r)
			}
		})
	}
}
