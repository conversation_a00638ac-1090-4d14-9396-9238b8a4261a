package mgr

import (
	"context"
	"errors"
	"fmt"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	channelPB "golang.52tt.com/protocol/app/channel"
	logicPb "golang.52tt.com/protocol/app/pgc_channel-pk-logic"
	pb "golang.52tt.com/protocol/services/pgc-channel-pk"
	"golang.52tt.com/services/tt-rev/pgc-channel-pk/internal/cache"
	"golang.52tt.com/services/tt-rev/pgc-channel-pk/internal/store"

	"math"
	"time"
)

func (mgr *Mgr) HandlePeakPkPresent(ctx context.Context, pkId, channelId, totalPrice, subPhrase uint32, pkEndTs int64) error {
	// 巅峰对决处理
	err := mgr.BeforePeakPkHandlePresent(ctx, pkId, channelId, subPhrase, totalPrice, pkEndTs)
	if err != nil {
		log.Errorf("HandlePeakPkPresent failed to BeforePeakPkHandlePresent. pkId:%v, channelId:%d, err:%v", pkId, channelId, err)
	}

	if subPhrase == cache.SubPhrasePeak {
		_, _ = mgr.store.RecordPeakPkFee(ctx, pkId, channelId, totalPrice)
	}

	return nil
}

func (mgr *Mgr) GetSubPhrasePbInfo(ctx context.Context, pkId, channelId uint32) (*pb.PgcChannelPKQuickKillInfo, *pb.PgcChannelPKPeakInfo, error) {
	subPhrase, err := mgr.cache.GetPkSubPhrase(ctx, pkId)
	if err != nil {
		log.Errorf("HandlePeakPkPresent failed to GetPkSubPhrase. pkId:%v, channelId:%d, err:%v", pkId, channelId, err)
		return nil, nil, err
	}

	if subPhrase == cache.SubPhrasePeak {
		peakInfo := &pb.PgcChannelPKPeakInfo{
			InPeakPk: true,
		}

		awardInfo, _ := mgr.getPeakPkAwardInfo(ctx, pkId, subPhrase)
		if awardInfo == nil || awardInfo.Cid != channelId {
			return nil, peakInfo, nil
		}

		peakInfo.PeakDesc = genPeakAwardDesc(awardInfo.Score)

		return nil, peakInfo, nil
	}

	condition, triggerCid, endTs, _ := mgr.getPkQuickKillInfo(ctx, pkId, subPhrase)
	_, descPrefix, desc := mgr.genPkQuickKillConditionDesc(subPhrase, channelId, triggerCid, condition)

	qkInfo := &pb.PgcChannelPKQuickKillInfo{
		QuickKillDescPrefix: descPrefix,
		QuickKillDesc:       desc,
		QuickKillEndTs:      endTs,
		EnableMinPkSec:      mgr.bc.GetMinQuickKillAbleSec(),
		EnableMaxPkSec:      mgr.bc.GetMaxQuickKillAbleSec(),
		ConditionValue:      condition,
		InQuickKill:         subPhrase == cache.SubPhraseQuickKill,
	}

	return qkInfo, nil, nil
}

// BeforePeakPkHandlePresent 巅峰对决前的送礼处理
func (mgr *Mgr) BeforePeakPkHandlePresent(ctx context.Context, pkId, channelId, subPhrase, totalPrice uint32, pkEndTs int64) error {
	now := time.Now()

	if subPhrase != cache.SubPhraseCommon {
		return nil
	}

	pkEndTime := time.Unix(pkEndTs, 0)
	if now.Add(time.Duration(mgr.bc.GetBeforePeakSec()) * time.Second).Before(pkEndTime) {
		// 还没到最后n秒
		return nil
	}

	pkInfoList, err := mgr.store.GetPgcChannelPKInfo(ctx, pkId)
	if err != nil {
		log.Errorf("BeforePeakPkHandlePresent failed to GetPgcChannelPKInfo. pkId:%v, channelId:%d, err:%v", pkId, channelId, err)
		return err
	}

	leaderInfo, secondInfo, err := mgr.GetSortPkInfo(pkInfoList)
	if err != nil {
		log.Errorf("BeforePeakPkHandlePresent failed to getSortPkInfo. pkId:%v, channelId:%d, err:%v", pkId, channelId, err)
		return err
	}

	secondCid := secondInfo.ChannelID
	if leaderInfo.Score == secondInfo.Score {
		// 打平就没有落后方
		secondCid = 0
	}

	_, _ = mgr.cache.IncrBeforePeakConditionScore(ctx, pkId, channelId, totalPrice)

	ok, err := mgr.cache.SetSecondChannelBeforePeak(ctx, pkId, secondCid)
	if err != nil {
		log.Errorf("BeforePeakPkHandlePresent failed to SetSecondChannelBeforePeak. pkId:%v, channelId:%d, err:%v", pkId, channelId, err)
		return err
	}

	if ok {
		log.Debugf("BeforePeakPkHandlePresent SetSecondChannelBeforePeak pkId:%v, secondCid:%d", pkId, secondCid)
	}

	return nil
}

func (mgr *Mgr) TryEnterPeakPk(ctx context.Context, pkId uint32, pkInfoList []*store.PgcChannelPkInfo) bool {
	if len(pkInfoList) != 2 {
		return false
	}
	now := time.Now()

	channelA, channelB := pkInfoList[0].ChannelID, pkInfoList[1].ChannelID

	ok, err := mgr.checkIfCanEnterPeakPk(ctx, pkId, channelA, channelB)
	if err != nil {
		log.Errorf("TryEnterPeakPk failed to checkIfCanEnterPeakPk. pkId:%v, channelId:%v %v, err:%v",
			pkId, channelA, channelB, err)
		return false
	}

	if !ok {
		return false
	}

	err = mgr.cache.SetPkSubPhrase(ctx, pkId, cache.SubPhrasePeak, mgr.bc.GetPeakPKSec()+60)
	if err != nil {
		log.Errorf("TryEnterPeakPk failed to BeginPeakPk. pkId:%v, channelId:%v %v, err:%v",
			pkId, channelA, channelB, err)
		return false
	}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	// extra award
	awardCid, awardScore := mgr.peakPkAward(ctx, pkId, pkInfoList)

	// 巅峰对决推送
	err = mgr.pushEntryPeakPkNotify(ctx, []uint32{channelA, channelB}, awardCid, awardScore)
	if err != nil {
		log.Errorf("TryEnterPeakPk failed to pushEntryPeakPkNotify. pkId:%v, channelId:%v %v, err:%v",
			pkId, channelA, channelB, err)
	}

	for _, pkInfo := range pkInfoList {
		awardScoreTmp := uint32(0)
		if pkInfo.ChannelID == awardCid {
			awardScoreTmp = awardScore
		}
		_ = mgr.store.CreatePeakPkRecord(ctx, &store.PeakPkRecord{
			PKId:          pkId,
			ChannelId:     pkInfo.ChannelID,
			BeforePkScore: pkInfo.Score,
			AwardPkScore:  awardScoreTmp,
		})
	}

	err = mgr.appendPkEndTime(ctx, pkId, channelA, channelB)
	if err != nil {
		log.Errorf("TryEnterPeakPk failed to appendPkEndTime. pkId:%v, channelId:%v %v, err:%v",
			pkId, channelA, channelB, err)
		return false
	}

	log.Infof("TryEnterPeakPk success. pkId:%v, channelId:%v %v, awardCid:%v, awardScore:%v, cost:%v",
		pkId, channelA, channelB, awardCid, awardScore, time.Since(now))
	return true
}

// 延长pk
func (mgr *Mgr) appendPkEndTime(ctx context.Context, pkId, channelA, channelB uint32) error {
	now := time.Now()
	newEndTime := now.Add(time.Duration(mgr.bc.GetPeakPKSec()) * time.Second)
	/*_, err := mgr.store.UpdatePgcChannelPKEndTime(0, pkId, newEndTime)
	  if err != nil {
	  	log.Errorf("appendPkEndTime failed to UpdatePgcChannelPKEndTime. pkId:%v, channelId:%v %v, err:%v",
	  		pkId, channelA, channelB, err)
	  	return err
	  }*/

	err := mgr.cache.SetPKEndTime(ctx, pkId, newEndTime.Unix())
	if err != nil {
		log.Errorf("appendPkEndTime failed to SetPKEndTime. pkId:%v, channelId:%v %v, err:%v",
			pkId, channelA, channelB, err)
		return err
	}

	// pk变更推送
	err = mgr.PushChannelPKBattleInfo(channelA, pkId)
	if err != nil {
		log.Errorf("appendPkEndTime failed to pushPKBattleInfo. pkId:%v, channelId:%v %v, err:%v",
			pkId, channelA, channelB, err)
	}

	log.InfoWithCtx(ctx, "appendPkEndTime pkId:%d, channelId:%v %v, newEndTime:%v", pkId, channelA, channelB, newEndTime)
	return nil
}

func genPeakAwardDesc(awardScore uint32) string {
	return fmt.Sprintf("当前我方已获得PK值奖励\n%dPK值", awardScore)
}

func (mgr *Mgr) pushEntryPeakPkNotify(ctx context.Context, channelIdList []uint32, awardCid, awardScore uint32) error {
	opt := &logicPb.PgcChannelPKPeakChangeOpt{
		AwardChannelId: awardCid,
		AwardPkValue:   awardScore,
		DurationDesc:   genPeakPkDurationDesc(mgr.bc.GetPeakPKSec()),
		PeakInfo: &logicPb.PgcChannelPKPeakInfo{
			InPeakPk: true,
		},
	}

	for _, cid := range channelIdList {
		if cid == opt.GetAwardChannelId() {
			opt.GetPeakInfo().PeakDesc = genPeakAwardDesc(awardScore)
		} else {
			opt.GetPeakInfo().PeakDesc = ""
		}

		data, _ := proto.Marshal(opt)
		err := mgr.pushMsgToChannel(ctx, cid, uint32(channelPB.ChannelMsgType_PGC_CHANNEL_PK_PEAK_CHANGE), "", data)
		if err != nil {
			log.ErrorWithCtx(ctx, "pushEntryPeakPkNotify fail to pushMsgToChannel. cid:%v, err:%v", cid, err)
			return err
		}
	}

	return nil
}

func (mgr *Mgr) GetSortPkInfo(list []*store.PgcChannelPkInfo) (leaderInfo, secondInfo *store.PgcChannelPkInfo, err error) {
	if len(list) != 2 {
		return nil, nil, errors.New("len(PKInfoList) != 2")
	}

	if list[0].Score > list[1].Score {
		return list[0], list[1], nil
	}

	return list[1], list[0], nil
}

func (mgr *Mgr) peakPkAward(ctx context.Context, pkId uint32, pkInfoList []*store.PgcChannelPkInfo) (awardCid, awardScore uint32) {
	leaderInfo, secondInfo, err := mgr.GetSortPkInfo(pkInfoList)
	if err != nil {
		log.Errorf("peakPkAward failed to getSortPkInfo. pkInfoList:%+v, err:%v", pkInfoList, err)
		return 0, 0
	}

	differentScore := leaderInfo.Score - secondInfo.Score
	awardRatio := float64(0)

	for _, awardConf := range mgr.bc.GetPeakAwardConfList() {
		if differentScore > awardConf.MinScore {
			awardRatio = awardConf.AwardRatio
			break
		}
	}

	awardScore = uint32(math.Ceil(float64(differentScore) * awardRatio)) // 向上取整

	if awardScore == 0 {
		return 0, 0
	}

	awardCid = leaderInfo.ChannelID

	// 奖励战力
	success, err := mgr.store.UpdatePgcChannelPKScore(ctx, awardCid, pkId, awardScore, time.Now())
	if err != nil {
		log.Errorf("peakPkAward failed to UpdatePgcChannelPKScore. pkId:%v, awardCid:%v, err:%v",
			pkId, awardCid, err)
		return 0, 0
	}

	if success {
		_ = mgr.cache.SetChannelPKChange(ctx, awardCid, pkId)
	}

	// cache award info
	err = mgr.cache.SetPeakAwardInfo(ctx, pkId, &cache.PeakPkAwardInfo{Cid: awardCid, Score: awardScore})
	if err != nil {
		log.Errorf("peakPkAward failed to SetPeakAwardInfo. pkId:%v, awardCid:%v, awardScore:%v, err:%v",
			pkId, awardCid, awardScore, err)
	}

	return
}

func (mgr *Mgr) checkIfCanEnterPeakPk(ctx context.Context, pkId, channelA, channelB uint32) (bool, error) {
	log.Debugf("checkIfCanEnterPeakPk pkId:%v, channelId:%v %v", pkId, channelA, channelB)

	pkSubPhrase, err := mgr.cache.GetPkSubPhrase(ctx, pkId)
	if err != nil {
		log.Errorf("checkIfCanEnterPeakPk failed to GetPkSubPhrase. pkId:%v, channelId:%v %v, err:%v",
			pkId, channelA, channelB, err)
		return false, err
	}

	if pkSubPhrase != cache.SubPhraseCommon {
		log.Debugf("checkIfCanEnterPeakPk skip. pkSubPhrase:%d, pkId:%d,  channelId:%v %v", pkSubPhrase, pkId, channelA, channelB)
		return false, nil
	}

	secondCid, err := mgr.cache.GetSecondChannelBeforePeak(ctx, pkId)
	if err != nil {
		log.Errorf("checkIfCanEnterPeakPk failed to GetSecondChannelBeforePeak. pkId:%v, channelId:%v %v, err:%v",
			pkId, channelA, channelB, err)
		return false, err
	}

	log.Debugf("checkIfCanEnterPeakPk pkId:%v, secondCid:%v, channelId:%v %v", pkId, secondCid, channelA, channelB)

	checkCidList := make([]uint32, 0)
	if secondCid > 0 {
		// 落后方达到条件都需要触发巅峰对决
		checkCidList = append(checkCidList, secondCid)
	} else {
		// secondCid为0说明双方打平，此时哪一方达到条件都需要触发巅峰对决
		checkCidList = append(checkCidList, channelA, channelB)
	}

	for _, checkCid := range checkCidList {
		score, err := mgr.cache.GetBeforePeakConditionScore(ctx, pkId, checkCid)
		if err != nil {
			log.Errorf("checkIfCanEnterPeakPk failed to GetBeforePeakConditionScore. pkId:%v, checkCid:%v, err:%v",
				pkId, checkCid, err)
			continue
		}

		if score >= mgr.bc.GetMinEnterPeakScore() {
			log.Infof("checkIfCanEnterPeakPk success. pkId:%v, checkCid:%v, score:%d", pkId, checkCid, score)
			return true, nil
		}

		log.Debugf("checkIfCanEnterPeakPk pkId:%v, checkCid:%v, score:%d", pkId, checkCid, score)
	}

	return false, nil
}

func genPeakPkDurationDesc(sec uint32) string {
	return fmt.Sprintf("%d分钟后本场PK结束", sec/60)
}

func (mgr *Mgr) getPeakPkAwardInfo(ctx context.Context, pkId, pkSubPhrase uint32) (*cache.PeakPkAwardInfo, string) {
	info := &cache.PeakPkAwardInfo{}
	if pkSubPhrase != cache.SubPhrasePeak {
		return info, ""
	}

	awardInfo, err := mgr.cache.GetPeakAwardInfo(ctx, pkId)
	if err != nil {
		log.Errorf("getPeakPkAwardInfo fail to GetPeakAwardInfo. pkId:%v, err:%v", pkId, err)
		return awardInfo, ""
	}

	return awardInfo, genPeakPkDurationDesc(mgr.bc.GetPeakPKSec())
}
