package award

import (
    "context"
    "github.com/golang/mock/gomock"
    "golang.52tt.com/services/tt-rev/richer-birthday/internal/conf"
    bcMocks "golang.52tt.com/services/tt-rev/richer-birthday/internal/conf/mocks"
    acMocks "golang.52tt.com/services/tt-rev/richer-birthday/internal/model/anti_corruption_layer/mocks"
    "golang.52tt.com/services/tt-rev/richer-birthday/internal/model/award/mocks"
    "testing"
    "time"
)

var testStore *mocks.MockIStore
var testBc *bcMocks.MockIBusinessConfManager
var testAcLayer *acMocks.MockIMgr

func initTestMgr(ctrl *gomock.Controller) *Mgr {
    testStore = mocks.NewMockIStore(ctrl)
    testBc = bcMocks.NewMockIBusinessConfManager(ctrl)
    testAcLayer = acMocks.NewMockIMgr(ctrl)

    return &Mgr{
        store:  testStore,
        bc:     testBc,
        antiCL: testAcLayer,
    }
}

func TestMgr_IsReceivedBirthdayAward(t *testing.T) {
    m := initTestMgr(gomock.NewController(t))

    type args struct {
        ctx       context.Context
        uid       uint32
        queryTime time.Time
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name: "Test IsReceivedBirthdayAward",
            args: args{
                ctx:       context.Background(),
                uid:       1,
                queryTime: time.Now(),
            },
            wantErr: false,
            mockFunc: func() {
                testStore.EXPECT().GetAwardByBirthday(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
            },
        },
    }
    for _, tt := range tests {
        tt.mockFunc()
        t.Run(tt.name, func(t *testing.T) {
            if _, err := m.IsReceivedBirthdayAward(tt.args.ctx, tt.args.uid, tt.args.queryTime); (err != nil) != tt.wantErr {
                t.Errorf("IsReceivedBirthdayAward() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_ReceiveBirthdayAward(t *testing.T) {
    m := initTestMgr(gomock.NewController(t))

    type args struct {
        ctx      context.Context
        uid      uint32
        birthday time.Time
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name: "Test ReceiveBirthdayAward",
            args: args{
                ctx:      context.Background(),
                uid:      1,
                birthday: time.Now(),
            },
            wantErr: false,
            mockFunc: func() {
                testStore.EXPECT().GetAwardByBirthday(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                testBc.EXPECT().GetBirthdayAwards().Return([]*conf.AwardCfg{{GiftId: "1", GiftType: 1}})
                testStore.EXPECT().BatchInsertAward(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
        },
    }
    for _, tt := range tests {
        tt.mockFunc()
        t.Run(tt.name, func(t *testing.T) {
            if err := m.ReceiveBirthdayAward(tt.args.ctx, tt.args.uid, tt.args.birthday); (err != nil) != tt.wantErr {
                t.Errorf("ReceiveBirthdayAward() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}
