// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/pgc-adventure/internal/mgr (interfaces: IMgr)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	pgc_channel_game_logic "golang.52tt.com/protocol/app/pgc_channel_game_logic"
	pgc_adventure "golang.52tt.com/protocol/services/pgc-adventure"
)

// MockIMgr is a mock of IMgr interface.
type MockIMgr struct {
	ctrl     *gomock.Controller
	recorder *MockIMgrMockRecorder
}

// MockIMgrMockRecorder is the mock recorder for MockIMgr.
type MockIMgrMockRecorder struct {
	mock *MockIMgr
}

// NewMockIMgr creates a new mock instance.
func NewMockIMgr(ctrl *gomock.Controller) *MockIMgr {
	mock := &MockIMgr{ctrl: ctrl}
	mock.recorder = &MockIMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIMgr) EXPECT() *MockIMgrMockRecorder {
	return m.recorder
}

// AdventureControlNext mocks base method.
func (m *MockIMgr) AdventureControlNext(arg0 context.Context, arg1, arg2 uint32, arg3 pgc_adventure.AdventureControlNextReq_NextOp) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AdventureControlNext", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AdventureControlNext indicates an expected call of AdventureControlNext.
func (mr *MockIMgrMockRecorder) AdventureControlNext(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdventureControlNext", reflect.TypeOf((*MockIMgr)(nil).AdventureControlNext), arg0, arg1, arg2, arg3)
}

// AutoEnd mocks base method.
func (m *MockIMgr) AutoEnd() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AutoEnd")
}

// AutoEnd indicates an expected call of AutoEnd.
func (mr *MockIMgrMockRecorder) AutoEnd() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AutoEnd", reflect.TypeOf((*MockIMgr)(nil).AutoEnd))
}

// AutoRandomSteps mocks base method.
func (m *MockIMgr) AutoRandomSteps() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AutoRandomSteps")
}

// AutoRandomSteps indicates an expected call of AutoRandomSteps.
func (mr *MockIMgrMockRecorder) AutoRandomSteps() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AutoRandomSteps", reflect.TypeOf((*MockIMgr)(nil).AutoRandomSteps))
}

// BatchGetAdventurePhase mocks base method.
func (m *MockIMgr) BatchGetAdventurePhase(arg0 context.Context, arg1 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAdventurePhase", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAdventurePhase indicates an expected call of BatchGetAdventurePhase.
func (mr *MockIMgrMockRecorder) BatchGetAdventurePhase(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAdventurePhase", reflect.TypeOf((*MockIMgr)(nil).BatchGetAdventurePhase), arg0, arg1)
}

// BeginAdventure mocks base method.
func (m *MockIMgr) BeginAdventure(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BeginAdventure", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BeginAdventure indicates an expected call of BeginAdventure.
func (mr *MockIMgrMockRecorder) BeginAdventure(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BeginAdventure", reflect.TypeOf((*MockIMgr)(nil).BeginAdventure), arg0, arg1, arg2)
}

// CtrlRandomStepsAgain mocks base method.
func (m *MockIMgr) CtrlRandomStepsAgain(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CtrlRandomStepsAgain", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CtrlRandomStepsAgain indicates an expected call of CtrlRandomStepsAgain.
func (mr *MockIMgrMockRecorder) CtrlRandomStepsAgain(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CtrlRandomStepsAgain", reflect.TypeOf((*MockIMgr)(nil).CtrlRandomStepsAgain), arg0, arg1, arg2)
}

// CtrlRandomStepsNext mocks base method.
func (m *MockIMgr) CtrlRandomStepsNext(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CtrlRandomStepsNext", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CtrlRandomStepsNext indicates an expected call of CtrlRandomStepsNext.
func (mr *MockIMgrMockRecorder) CtrlRandomStepsNext(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CtrlRandomStepsNext", reflect.TypeOf((*MockIMgr)(nil).CtrlRandomStepsNext), arg0, arg1, arg2)
}

// EndGame mocks base method.
func (m *MockIMgr) EndGame(arg0 context.Context, arg1, arg2 uint32, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EndGame", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// EndGame indicates an expected call of EndGame.
func (mr *MockIMgrMockRecorder) EndGame(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EndGame", reflect.TypeOf((*MockIMgr)(nil).EndGame), arg0, arg1, arg2, arg3)
}

// GetAdventureGameInfo mocks base method.
func (m *MockIMgr) GetAdventureGameInfo(arg0 context.Context, arg1 uint32) (*pgc_adventure.AdventureGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAdventureGameInfo", arg0, arg1)
	ret0, _ := ret[0].(*pgc_adventure.AdventureGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAdventureGameInfo indicates an expected call of GetAdventureGameInfo.
func (mr *MockIMgrMockRecorder) GetAdventureGameInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAdventureGameInfo", reflect.TypeOf((*MockIMgr)(nil).GetAdventureGameInfo), arg0, arg1)
}

// HandleHostMicOn mocks base method.
func (m *MockIMgr) HandleHostMicOn(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleHostMicOn", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleHostMicOn indicates an expected call of HandleHostMicOn.
func (mr *MockIMgrMockRecorder) HandleHostMicOn(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleHostMicOn", reflect.TypeOf((*MockIMgr)(nil).HandleHostMicOn), arg0, arg1, arg2)
}

// HandleLeaveChannel mocks base method.
func (m *MockIMgr) HandleLeaveChannel(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleLeaveChannel", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleLeaveChannel indicates an expected call of HandleLeaveChannel.
func (mr *MockIMgrMockRecorder) HandleLeaveChannel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleLeaveChannel", reflect.TypeOf((*MockIMgr)(nil).HandleLeaveChannel), arg0, arg1, arg2)
}

// HandleMicRelease mocks base method.
func (m *MockIMgr) HandleMicRelease(arg0 context.Context, arg1, arg2 uint32, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleMicRelease", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleMicRelease indicates an expected call of HandleMicRelease.
func (mr *MockIMgrMockRecorder) HandleMicRelease(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleMicRelease", reflect.TypeOf((*MockIMgr)(nil).HandleMicRelease), arg0, arg1, arg2, arg3)
}

// PushAdventureInfoChannel mocks base method.
func (m *MockIMgr) PushAdventureInfoChannel(arg0 context.Context, arg1 uint32, arg2 *pgc_channel_game_logic.AdventureInfoChangeOpt) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "PushAdventureInfoChannel", arg0, arg1, arg2)
}

// PushAdventureInfoChannel indicates an expected call of PushAdventureInfoChannel.
func (mr *MockIMgrMockRecorder) PushAdventureInfoChannel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushAdventureInfoChannel", reflect.TypeOf((*MockIMgr)(nil).PushAdventureInfoChannel), arg0, arg1, arg2)
}

// RandomSteps mocks base method.
func (m *MockIMgr) RandomSteps(arg0 context.Context, arg1, arg2 uint32, arg3 bool) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RandomSteps", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RandomSteps indicates an expected call of RandomSteps.
func (mr *MockIMgrMockRecorder) RandomSteps(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RandomSteps", reflect.TypeOf((*MockIMgr)(nil).RandomSteps), arg0, arg1, arg2, arg3)
}

// ReportAdventureEvent mocks base method.
func (m *MockIMgr) ReportAdventureEvent(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32, arg4, arg5 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReportAdventureEvent", arg0, arg1, arg2, arg3, arg4, arg5)
}

// ReportAdventureEvent indicates an expected call of ReportAdventureEvent.
func (mr *MockIMgrMockRecorder) ReportAdventureEvent(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportAdventureEvent", reflect.TypeOf((*MockIMgr)(nil).ReportAdventureEvent), arg0, arg1, arg2, arg3, arg4, arg5)
}

// RestartGame mocks base method.
func (m *MockIMgr) RestartGame(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RestartGame", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RestartGame indicates an expected call of RestartGame.
func (mr *MockIMgrMockRecorder) RestartGame(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestartGame", reflect.TypeOf((*MockIMgr)(nil).RestartGame), arg0, arg1, arg2)
}

// SetAdventurePhase mocks base method.
func (m *MockIMgr) SetAdventurePhase(arg0 context.Context, arg1 *pgc_adventure.SetAdventurePhaseReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAdventurePhase", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetAdventurePhase indicates an expected call of SetAdventurePhase.
func (mr *MockIMgrMockRecorder) SetAdventurePhase(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAdventurePhase", reflect.TypeOf((*MockIMgr)(nil).SetAdventurePhase), arg0, arg1)
}

// StartNewRound mocks base method.
func (m *MockIMgr) StartNewRound(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartNewRound", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartNewRound indicates an expected call of StartNewRound.
func (mr *MockIMgrMockRecorder) StartNewRound(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartNewRound", reflect.TypeOf((*MockIMgr)(nil).StartNewRound), arg0, arg1, arg2)
}

// UserEnroll mocks base method.
func (m *MockIMgr) UserEnroll(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserEnroll", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UserEnroll indicates an expected call of UserEnroll.
func (mr *MockIMgrMockRecorder) UserEnroll(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserEnroll", reflect.TypeOf((*MockIMgr)(nil).UserEnroll), arg0, arg1, arg2, arg3)
}

// UserParticipate mocks base method.
func (m *MockIMgr) UserParticipate(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserParticipate", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// UserParticipate indicates an expected call of UserParticipate.
func (mr *MockIMgrMockRecorder) UserParticipate(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserParticipate", reflect.TypeOf((*MockIMgr)(nil).UserParticipate), arg0, arg1, arg2, arg3, arg4)
}
