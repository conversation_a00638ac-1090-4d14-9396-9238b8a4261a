package cache

import (
	"context"
	"golang.52tt.com/pkg/store/redis"
	"time"
)

type RedisCli struct {
	cmd redis.CmdableV2
}

func NewRedisCli(cmd redis.CmdableV2) *RedisCli {
	return &RedisCli{cmd: cmd}
}

func (r *RedisCli) GetServerTime(ctx context.Context) uint64 {
	timeUnix := uint64(time.Now().UnixNano() / 1e6)

	ctxN, cancel := context.WithTimeout(ctx, 100*time.Millisecond)
	defer cancel()

	serverTime, err := r.cmd.Time(ctxN).Result()
	if err != nil {
		return timeUnix
	}

	timeUnix = uint64(serverTime.UnixNano() / 1e6)
	return timeUnix
}
