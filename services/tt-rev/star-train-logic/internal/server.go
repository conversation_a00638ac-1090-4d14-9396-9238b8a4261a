package internal

import (

	"context"
	"golang.52tt.com/protocol/services/demo/echo"
	"sync/atomic"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	chance_game_entry2 "golang.52tt.com/clients/chance-game-entry"
	userprofileapi "golang.52tt.com/clients/user-profile-api"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/star_train_logic"
	"golang.52tt.com/protocol/common/status"
	chance_game_entry "golang.52tt.com/protocol/services/chance-game-entry"
	star_train "golang.52tt.com/protocol/services/star-train"
	"google.golang.org/grpc"
)

var (
    gameType         = uint32(chance_game_entry.NewChanceGameType_NewChanceGameType_StarTrain)
)

type StartConfig struct {
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)
    grpcOpts := []grpc.DialOption{
        //grpc.WithBlock(),
    }
    starTrainCli, err := star_train.NewClient(ctx, grpcOpts...)
    if err != nil {
        log.Errorf("NewClient star_train fail,err:%v", err)
        return nil, err
    }

    gameEntryCli := chance_game_entry2.NewIClient()

    userProfileCli := userprofileapi.NewIClient()

    return &Server{
	    processLocalCache: &atomic.Value{},
        starTrainCli:   starTrainCli,
        gameEntryCli:   gameEntryCli,
        UserProfileCli: userProfileCli,
    }, nil
}

type Server struct {
	processLocalCache *atomic.Value
    starTrainCli star_train.StarTrainClient
    gameEntryCli chance_game_entry2.IClient
    UserProfileCli userprofileapi.IClient
}

func (s *Server) ShutDown() {}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

// GetStarTrainEntry 获取摘星列车入口信息
func (s *Server) GetStarTrainEntry(ctx context.Context, req *star_train_logic.GetStarTrainEntryRequest) (*star_train_logic.GetStarTrainEntryResponse, error) {
    out := &star_train_logic.GetStarTrainEntryResponse{}

    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.Errorf("GetEntryAndNotifyInfo ServiceInfoFromContext fail. in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }
    opUid := svrInfo.UserID
    channelId := req.GetChannelId()
    marketId := svrInfo.MarketID
    clientIp := svrInfo.ClientIP

    // 1.检查入口权限
    entryResp, e := s.gameEntryCli.CheckGameEntryAccess(ctx, opUid, &chance_game_entry.CheckGameEntryAccessReq{
        Uid:       opUid,
        ChannelId: channelId,
        GameType:  []uint32{gameType},
        ClientIp:  clientIp,
        MarketId:  marketId,
    })
    if e != nil || len(entryResp.GetConfList()) == 0 {
        log.ErrorWithCtx(ctx, "GetStarTrainEntry CheckGameEntryAccess fail,req:%+v opUid:%d,err:%v", req, opUid, e)
        return out, e
    }

    out.HaveAccess = entryResp.GetConfList()[0].GetAccess()
    if !out.HaveAccess {
        return out, nil
    }

    // 2.获取玩法简单信息
    infoResp, err := s.starTrainCli.GetSimpleActivityInfo(ctx, &star_train.GetSimpleActivityInfoReq{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetStarTrainEntry GetSimpleActivityInfo fail,req:%+v opUid:%d,err:%v", req, opUid, err)
        return out, err
    }

    out.ActivityBeginTs = infoResp.GetActBeginTime()
    out.ActivityEndTs = infoResp.GetActEndTime()
    activityCfg := &star_train_logic.StarTrainCfg{
        ActGiftDesc:            infoResp.GetActGiftDesc(),
        TicketGiftIdList:       infoResp.GetGiftIdList(),
        SeatUnitPrice:          infoResp.GetSeatUnitPrice(),
        StrategyRuleImg:        infoResp.GetStrategyRuleImg(),
        NotReachedStopDesc:     infoResp.GetNotReachedStopDesc(),
        AlreadyReachedStopDesc: infoResp.GetAlreadyReachedStopDesc(),
        LastStarAwardUserDesc:  infoResp.GetLastStarAwardUserDesc(),
        StarAwardStopDesc:      infoResp.GetStarAwardStopDesc(),
        StarAwardStopJoinDesc:  infoResp.GetStarAwardStopJoinDesc(),
    }
    out.Cfg = activityCfg
    log.DebugWithCtx(ctx, "GetStarTrainEntry success,req:%+v opUid:%d,out:%+v", req, opUid, out)
    return out, nil
}

// GetStarTrainInfo 获取列车玩法信息
func (s *Server) GetStarTrainInfo(ctx context.Context, req *star_train_logic.GetStarTrainInfoRequest) (*star_train_logic.GetStarTrainInfoResponse, error) {
    out := &star_train_logic.GetStarTrainInfoResponse{}

    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.Errorf("GetStarTrainInfo ServiceInfoFromContext fail. in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }
    opUid := svrInfo.UserID

    // 1.获取列车玩法信息
    infoResp, err := s.starTrainCli.GetStarTrainInfo(ctx, &star_train.GetStarTrainInfoReq{
        Uid: opUid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetStarTrainInfo GetStarTrainInfo fail,req:%+v opUid:%d,err:%v", req, opUid, err)
        return out, err
    }

    out = &star_train_logic.GetStarTrainInfoResponse{
        Progress:         s.progressPbTransfer(infoResp.GetProgress()),
        MyJoinInfo:       s.myJoinInfoPbTransfer(infoResp.GetMyJoinInfo()),
        ActEndTime:       infoResp.GetActEndTime(),
        NextRoundBeginTs: infoResp.GetNextRoundBegin(),
    }

    return out, nil
}

// GetStarTrainProgress 获取列车进程信息
func (s *Server) GetStarTrainProgress(ctx context.Context, req *star_train_logic.GetStarTrainProgressRequest) (*star_train_logic.GetStarTrainProgressResponse, error) {
    out := &star_train_logic.GetStarTrainProgressResponse{}

    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.Errorf("GetStarTrainProgress ServiceInfoFromContext fail. in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }
    opUid := svrInfo.UserID

	now := time.Now()
	if info, ok := s.processLocalCache.Load().(*star_train_logic.StarTrainProgress); ok {
		// 本地缓存还未失效
		if info.GetServerTs() > now.Add(-time.Second).UnixMilli() {
			out.Progress = info
			return out, nil
		}
	}

    // 获取列车进程信息
    progressResp, err := s.starTrainCli.GetStarTrainProgress(ctx, &star_train.GetStarTrainProgressReq{
        Uid: opUid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetStarTrainProgress GetStarTrainProgress fail,req:%+v opUid:%d,err:%v", req, opUid, err)
        return out, err
    }

    out.Progress = s.progressPbTransfer(progressResp.GetProgress())

	// 重置本地缓存
	s.processLocalCache.Store(out.Progress)
    return out, nil
}

// GetStarTrainBroadcast 获取列车播报数据
func (s *Server) GetStarTrainBroadcast(ctx context.Context, req *star_train_logic.GetStarTrainBroadcastRequest) (*star_train_logic.GetStarTrainBroadcastResponse, error) {
    out := &star_train_logic.GetStarTrainBroadcastResponse{}

    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.Errorf("GetStarTrainBroadcast ServiceInfoFromContext fail. in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }
    opUid := svrInfo.UserID

    // 1.获取列车播报数据
    broadcastResp, err := s.starTrainCli.GetStarTrainBroadcast(ctx, &star_train.GetStarTrainBroadcastReq{
        Uid: opUid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetStarTrainBroadcast GetStarTrainBroadcast fail,req:%+v opUid:%d,err:%v", req, opUid, err)
        return out, err
    }

    out.BroadcastList = s.broadcastPbTransfer(broadcastResp.GetBroadcastList())
    return out, nil
}

// GetStarTrainSeatList 获取列车座次表
func (s *Server) GetStarTrainSeatList(ctx context.Context, req *star_train_logic.GetStarTrainSeatListRequest) (*star_train_logic.GetStarTrainSeatListResponse, error) {
    out := &star_train_logic.GetStarTrainSeatListResponse{
		AwardList: make([]*star_train_logic.StarTrainAward, 0),
		SeatList: make([]*star_train_logic.StarTrainSeat, 0),
    }

    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.Errorf("GetStarTrainSeatList ServiceInfoFromContext fail. in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }
	opUid := svrInfo.UserID

    resp, err := s.starTrainCli.GetStarTrainSeatList(ctx, &star_train.GetStarTrainSeatListReq{
		Uid:     opUid,
		RoundId: uint32(req.GetRoundId()),
    })
	if err != nil {
		log.ErrorWithCtx(ctx, "GetStarTrainSeatList GetStarTrainSeatList fail,req:%+v opUid:%d,err:%v", req, opUid, err)
		return out, err
	}

	out.AwardRuleDesc = resp.GetHonorAwardDesc()

	mySeat := resp.GetMySeat()
	out.MySeat = &star_train_logic.StarTrainSeat{
		SeatId:     mySeat.GetSeatId(),
		SeatAmount: mySeat.GetSeatAmount(),
	}

	for _, info := range resp.GetAwardList() {
		out.AwardList = append(out.AwardList, s.awardInfoPbTransfer(info))
	}

	uidList := make([]uint32, 0)
	for _, mem := range resp.GetSeatList() {
		uidList = append(uidList, mem.GetUid())
	}

	userMap := s.getUserProfile(uidList)
	for _, mem := range resp.GetSeatList() {
		out.SeatList = append(out.SeatList, &star_train_logic.StarTrainSeat{
			SeatId:     mem.GetSeatId(),
			SeatAmount: mem.GetSeatAmount(),
			User:       userMap[mem.GetUid()],
		})
	}

	return out, nil
}

