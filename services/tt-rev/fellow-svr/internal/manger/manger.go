package manger

import (
	"context"
	"fmt"
	headImage "golang.52tt.com/clients/headimage"
	fellow_level_award "golang.52tt.com/protocol/services/fellow-level-award"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/pingcap/tidb/util/math"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	proto2 "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/golang/gudetama/oss/datacenter"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
	"golang.52tt.com/clients/account"
	apicenterCli "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/clients/channel"
	channelcpgame "golang.52tt.com/clients/channel-cp-game"
	channelMsgExpress "golang.52tt.com/clients/channel-msg-express"
	channelMic "golang.52tt.com/clients/channelmic"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/guild"
	pushclient "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/sendim"
	"golang.52tt.com/clients/seqgen/v2"
	"golang.52tt.com/clients/timeline"
	"golang.52tt.com/clients/ugc/friendship"
	userprofileapi "golang.52tt.com/clients/user-profile-api"
	userPresentClient "golang.52tt.com/clients/userpresent"
	youknowwho "golang.52tt.com/clients/you-know-who"
	yswfukwdelay "golang.52tt.com/clients/yswf-ukw-delay"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelPB "golang.52tt.com/protocol/app/channel"
	fellow_logic "golang.52tt.com/protocol/app/fellow-logic"
	"golang.52tt.com/protocol/app/im"
	pushPb "golang.52tt.com/protocol/app/push"
	syncPB "golang.52tt.com/protocol/app/sync"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	apicenter "golang.52tt.com/protocol/services/apicenter/apiserver"
	pbFellowAward "golang.52tt.com/protocol/services/fellow-level-award"
	pb "golang.52tt.com/protocol/services/fellow-svr"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	timelinesvrPB "golang.52tt.com/protocol/services/timelinesvr"
	presentPb "golang.52tt.com/protocol/services/userpresent"
	yswfukwdelaypb "golang.52tt.com/protocol/services/yswfukwdelay"
	commonKFK "golang.52tt.com/services/common/kafka"
	"golang.52tt.com/services/helper-from-cpp/immsghelper"
	"golang.52tt.com/services/notify"
	"golang.52tt.com/services/tt-rev/fellow-svr/internal/conf"
	"golang.52tt.com/services/tt-rev/fellow-svr/internal/store"
	"golang.org/x/sync/singleflight"
	"google.golang.org/grpc"
)

var (
	AddPointBase        = 1
	AddPointInteraction = 2
	AddPointSendPresent = 3
	AddPointBind        = 4
	SubPointSystem      = 5
)

type Manager struct {
	sc                   *conf.StartConfig
	bc                   *conf.BusinessConfManager
	store                *store.Store
	sendImCli            *sendim.Client
	seqGenCli            *seqgen.Client
	timelineCli          *Timeline.Client
	accountCli           *account.Client
	httpClient           *http.Client
	apicenterCli         *apicenterCli.Client
	expressCli           *channelMsgExpress.Client
	pushCli              *pushclient.Client
	fellowChangeKafka    publisher.Publisher
	presentCli           *userPresentClient.Client
	rwMutex              sync.RWMutex
	PresentCfgCache      map[uint32]*presentPb.StPresentItemConfig
	channelMicCli        *channelMic.Client
	rwFellowPresentMutex sync.RWMutex
	fellowPresentMap     map[uint32]*store.FellowPresentConfig
	friendshipCli        *friendship.Client
	sg                   *singleflight.Group
	ChannelCPCli         *channelcpgame.Client
	ChannelCli           *channel.Client
	GuildCli             *guild.Client
	ukwCli               *youknowwho.Client
	channelOlCli         *channelol.Client
	userProfileApiCli    *userprofileapi.Client
	yswfUkwDelayCli      yswfukwdelay.IClient
	levelAwardCli        *fellow_level_award.Client
	headImageCli         headImage.IClient
}

func NewManager(sc *conf.StartConfig, bc *conf.BusinessConfManager, st *store.Store) *Manager {

	sendImCli := sendim.NewClient()
	seqGenCli, _ := seqgen.NewClient()
	timelineCli := Timeline.NewClient()
	accountCli, _ := account.NewClient()
	apicenterCli := apicenterCli.NewClient()
	expressCli, _ := channelMsgExpress.NewClient()
	pushCli, _ := pushclient.NewClient()
	//fellowKFK, _ := commonKFK.NewKFKProducer(sc.FellowChangeKafka)
	presentCli := userPresentClient.NewClient(grpc.WithBlock())
	channelMicCli := channelMic.NewClient()
	friendshipCli, _ := friendship.NewClient()
	channelCpGame, _ := channelcpgame.NewClient()
	channelCli := channel.NewClient()
	guildCli := guild.NewClient()
	ukwCli, _ := youknowwho.NewClient()
	channelOlCli := channelol.NewClient()
	userProfileApiCli, _ := userprofileapi.NewClient()
	yswfUkwDelayCli, _ := yswfukwdelay.NewClient()
	levelAwardCli, _ := fellow_level_award.NewClient(context.Background())
	headImageCli := headImage.NewIClient()

	// kfk producer event-link 改造
	fellowKFK, _ := commonKFK.NewEventLinkProducer(sc.FellowChangeKafka.BrokerList(), []string{
		"fellow_point_change", "fellow_unbind", "fellow_bind",
	})

	return &Manager{
		sc:                sc,
		bc:                bc,
		store:             st,
		sg:                &singleflight.Group{},
		sendImCli:         sendImCli,
		seqGenCli:         seqGenCli,
		timelineCli:       timelineCli,
		accountCli:        accountCli,
		httpClient:        newHttpClient(),
		apicenterCli:      apicenterCli,
		expressCli:        expressCli,
		pushCli:           pushCli,
		fellowChangeKafka: fellowKFK,
		presentCli:        presentCli,
		PresentCfgCache:   make(map[uint32]*presentPb.StPresentItemConfig),
		channelMicCli:     channelMicCli,
		fellowPresentMap:  make(map[uint32]*store.FellowPresentConfig),
		friendshipCli:     friendshipCli,
		ChannelCPCli:      channelCpGame,
		ChannelCli:        channelCli,
		GuildCli:          guildCli,
		ukwCli:            ukwCli,
		channelOlCli:      channelOlCli,
		userProfileApiCli: userProfileApiCli,
		yswfUkwDelayCli:   yswfUkwDelayCli,
		levelAwardCli:     levelAwardCli,
		headImageCli:      headImageCli,
	}
}

func (mgr *Manager) GetCpHeadWearItem(ctx context.Context, uid, fellowUid uint32, item *pbFellowAward.LevelAwardItem) (out *pbFellowAward.LevelAwardItem) {
	if item.ItemType != uint32(pbFellowAward.LevelAwardItemType_LEVEL_AWARD_ITEM_CP_HeadWear) {
		return nil
	}
	userItem := proto2.Clone(item).(*pbFellowAward.LevelAwardItem)
	itemList := strings.Split(item.ItemId, "_")
	if len(itemList) != 2 {
		log.ErrorWithCtx(ctx, "GetCpHeadWearSuiteId failed, item:%+v", item)
	}

	nameList := strings.Split(item.ItemName, ";")
	iconList := strings.Split(item.ItemIcon, ";")
	if uid < fellowUid {
		userItem.ItemId = itemList[0]
		userItem.ItemName = nameList[0]
		userItem.ItemIcon = iconList[0]
		return userItem
	} else {
		userItem.ItemId = itemList[1]
		userItem.ItemName = nameList[1]
		userItem.ItemIcon = iconList[1]
		return userItem
	}
}

func (mgr *Manager) AddFellowPoint(ctx context.Context, uid, toUid uint32, point float32, reason string, addType int, isRecovery bool) (err error) {
	if uid == toUid {
		return nil
	}

	// 神秘人数据延迟
	isDelay := false
	if !isRecovery { // 如果不为恢复数据, 需要判断是否需要延迟
		userProfiles, err := mgr.userProfileApiCli.BatchGetUserProfile(ctx, []uint32{uid, toUid})
		if err != nil {
			log.ErrorWithCtx(ctx, "AddFellowPoint.BatchGetUserProfile, uid: %d, toUid: %d, err: %v", uid, toUid, err)
			return err
		}
		if userProfiles[uid].GetPrivilege().GetType() == 1 || userProfiles[toUid].GetPrivilege().GetType() == 1 { // 有一方为神秘人则延迟加分
			item := &pb.FellowPointDelayItem{
				Uid:     uid,
				ToUid:   toUid,
				Point:   point,
				Reason:  reason,
				AddType: uint32(addType),
			}
			reqBytes, _ := proto.Marshal(item)
			_, err := mgr.yswfUkwDelayCli.AddYswfUkwDelayData(ctx, &yswfukwdelaypb.AddYswfUkwDelayDataReq{
				UidA:    uid,
				UidB:    toUid,
				BizType: uint32(yswfukwdelaypb.YswfBizType_FellowVal),
				Data:    reqBytes,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "AddFellowPoint.AddYswfUkwDelayData, req; %+v, err: %v", item, err)
			} else {
				isDelay = true
			}
		}
	}
	if isDelay {
		return nil // 如果该数据已经写入缓存服务, 提前返回
	}

	ret, err := mgr.store.AddFellowPoint(uid, toUid, point, reason)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddFellowPoint failed, uid:%d, toUid:%d, reason:%s, err:%v", uid, toUid, reason, err)
		return err
	}

	newLevel := mgr.bc.GetLevelByPoint(ret.Point)
	log.DebugWithCtx(ctx, "AddFellowPoint uid:%d, toUid:%d, ret:%+v , newLevel:%d", uid, toUid, ret, newLevel)
	if ret.Level != newLevel && ret.BindType != uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_UNKNOWN) {
		err = mgr.store.UpgradeFellow(uid, toUid, newLevel)
		if err != nil {
			log.ErrorWithCtx(ctx, "upgrade failed uid:%d, toUid:%d, err:%v", uid, toUid, err)
			return err
		}

		award, err := mgr.levelAwardCli.GetUpgradeLevelAward(ctx, &fellow_level_award.GetUpgradeLevelAwardReq{
			MyUid:      uid,
			FellowUid:  toUid,
			Level:      newLevel,
			FellowType: ret.FellowType,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUpgradeLevelAward failed, uid:%d, toUid:%d, level:%d, err:%v", uid, toUid, newLevel, err)
		}
		log.DebugWithCtx(ctx, "GetUpgradeLevelAward, uid:%d, toUid:%d, level:%d, award:%+v", uid, toUid, newLevel, award)
		var nextAwardMsg im.FellowNextLevelAwardMsg
		var fellowNextAwardMsg im.FellowNextLevelAwardMsg
		if len(award.GetNextAwardMsg()) > 0 {
			nextAwardMsg.NextAwardLevel = award.GetNextAwardMsg()
			fellowNextAwardMsg.NextAwardLevel = award.GetNextAwardMsg()
			for _, item := range award.GetAward().GetAwardList() {
				if item.ItemType == uint32(pbFellowAward.LevelAwardItemType_LEVEL_AWARD_ITEM_CP_HeadWear) {
					userAward := mgr.GetCpHeadWearItem(ctx, uid, toUid, item)
					nextAwardMsg.AwardList = append(nextAwardMsg.AwardList, &im.LevelAwardItem{
						ItemType:      userAward.ItemType,
						ItemName:      userAward.ItemName,
						ItemId:        userAward.ItemId,
						ItemCount:     userAward.ItemCount,
						ItemIcon:      userAward.ItemIcon,
						ItemCountInfo: userAward.ItemCountInfo,
						ItemTypeName:  userAward.ItemTypeName,
					})

					fellowAward := mgr.GetCpHeadWearItem(ctx, toUid, uid, item)
					fellowNextAwardMsg.AwardList = append(fellowNextAwardMsg.AwardList, &im.LevelAwardItem{
						ItemType:      fellowAward.ItemType,
						ItemName:      fellowAward.ItemName,
						ItemId:        fellowAward.ItemId,
						ItemCount:     fellowAward.ItemCount,
						ItemIcon:      fellowAward.ItemIcon,
						ItemCountInfo: fellowAward.ItemCountInfo,
						ItemTypeName:  fellowAward.ItemTypeName,
					})
				} else {
					levelItem := &im.LevelAwardItem{
						ItemType:      item.ItemType,
						ItemName:      item.ItemName,
						ItemId:        item.ItemId,
						ItemCount:     item.ItemCount,
						ItemIcon:      item.ItemIcon,
						ItemCountInfo: item.ItemCountInfo,
						ItemTypeName:  item.ItemTypeName,
					}
					nextAwardMsg.AwardList = append(nextAwardMsg.AwardList, levelItem)
					fellowNextAwardMsg.AwardList = append(fellowNextAwardMsg.AwardList, levelItem)
				}
			}

			if award.GetMyAward() != nil {
				nextAwardMsg.AwardList = mgr.transLevelAwardConf(award.GetMyAward())
				fellowNextAwardMsg.AwardList = mgr.transLevelAwardConf(award.GetFellowAward())
			}
		}

		extInfo := &im.FellowUpgradeMsg{
			FellowBindType: ret.BindType,
			FellowName:     mgr.bc.GetUnbindFellowMapByType(ret.FellowType),
			Level:          newLevel,
			NextAwardMsg:   &nextAwardMsg,
		}

		fellowExtInfo := &im.FellowUpgradeMsg{
			FellowBindType: ret.BindType,
			FellowName:     mgr.bc.GetUnbindFellowMapByType(ret.FellowType),
			Level:          newLevel,
			NextAwardMsg:   &fellowNextAwardMsg,
		}

		var ext []byte
		ext, err = proto.Marshal(extInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "sendUpgradeMsg Marshal proto fail, msg: %v , err: %v", ext, err)
			return err
		}

		toExt, err := proto.Marshal(fellowExtInfo)

		log.InfoWithCtx(ctx, "AddFellowPoint uid:%d, toUid:%d, point:%d, level:%d, new level:%d",
			uid, toUid, point, ret.Level, newLevel)
		if !isRecovery { // 如果为恢复数据, 不推送升级im
			err = mgr.SendExtImMsg(uid, toUid, uint32(im.IM_MSG_TYPE_FELLOW_UPGRADE_MSG), uint32(im.MsgSourceType_MSG_SOURCE_FROM_FELLOW_UPGRADE), ext, toExt)
		}
		mgr.sendUpgradeMsgToDataCenter(uid, toUid, "")
	}
	mgr.store.DelFellowBindListCache(ctx, uid)
	mgr.store.DelFellowBindListCache(ctx, toUid)

	if ret.BindType != uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_UNKNOWN) {
		mgr.SendFellowPointChangeEvent(ctx, ret, uint32(point), newLevel)
		mgr.store.AddFellowPointCache(ctx, uid, toUid, ret.Point)
	} else {
		mgr.store.DelFellowPointCache(ctx, uid, toUid)
	}

	mgr.SendOSS(ctx, uid, toUid, ret.Point, point, addType, reason)

	return err
}

func (mgr *Manager) transLevelAwardConf(cfg *pbFellowAward.LevelAwardConf) []*im.LevelAwardItem {
	if cfg == nil {
		return nil
	}
	ret := make([]*im.LevelAwardItem, 0, len(cfg.GetAwardList()))
	for _, item := range cfg.GetAwardList() {
		ret = append(ret, &im.LevelAwardItem{
			ItemType:      item.ItemType,
			ItemId:        item.ItemId,
			ItemName:      item.ItemName,
			ItemCount:     item.ItemCount,
			ItemIcon:      item.ItemIcon,
			ItemCountInfo: item.ItemCountInfo,
			ItemTypeName:  item.ItemTypeName,
		})
	}
	return ret
}

func (mgr *Manager) SendExtImMsg(uid, toUid, msgType, sourceType uint32, ext, fellowExt []byte) error {
	now := uint32(time.Now().Unix())
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	users := []uint32{uid, toUid}
	usersMap, err := mgr.accountCli.GetUsersMap(ctx, users)
	if err != nil {
		log.ErrorWithCtx(ctx, "get user failed, err:%v", err)
		return err
	}

	if _, ok := usersMap[uid]; !ok {
		log.ErrorWithCtx(ctx, "get user failed, uid:%d", uid)
		return err
	}

	if _, ok := usersMap[toUid]; !ok {
		log.ErrorWithCtx(ctx, "get user failed, toUid:%d", toUid)
		return err
	}

	msg := &timelinesvrPB.ImMsg{
		FromId:        toUid,
		ToId:          uid,
		FromName:      usersMap[toUid].GetUsername(),
		FromNick:      usersMap[toUid].GetNickname(),
		ToName:        usersMap[uid].GetUsername(),
		ToNick:        usersMap[uid].GetNickname(),
		Type:          msgType,
		MsgSourceType: sourceType,
		ClientMsgTime: now,
		ServerMsgTime: now,
		Ext:           ext,
		Status:        uint32(syncPB.NewMessageSync_UN_READ),
		Platform:      uint32(timelinesvrPB.Platform_UNSPECIFIED), // 没有平台限制必须显式指定
	}

	fromMsgID, err := mgr.seqGenCli.GenerateSequence(ctx, uid, seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendExtImMsg failed to generate svr msg id: %v", err)
		return err
	}

	toMsgID, err := mgr.seqGenCli.GenerateSequence(ctx, toUid, seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendExtImMsg failed to generate svr msg id: %v", err)
		return err
	}

	msg.ServerMsgId = uint32(fromMsgID)
	imErr := immsghelper.WriteMsgToUidWithId(ctx, uid, msg, mgr.seqGenCli, mgr.timelineCli)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendExtImMsg - WriteMsgToUidWithId fail , msg: %v , err: %v", msg, imErr)
		return imErr
	}

	msg = &timelinesvrPB.ImMsg{
		FromId:        uid,
		ToId:          toUid,
		FromName:      usersMap[uid].GetUsername(),
		FromNick:      usersMap[uid].GetNickname(),
		ToName:        usersMap[toUid].GetUsername(),
		ToNick:        usersMap[toUid].GetNickname(),
		Type:          msgType,
		MsgSourceType: sourceType,
		ClientMsgTime: now,
		ServerMsgTime: now,
		Ext:           fellowExt,
		Status:        uint32(syncPB.NewMessageSync_UN_READ),
		Platform:      uint32(timelinesvrPB.Platform_UNSPECIFIED), // 没有平台限制必须显式指定
	}

	msg.ServerMsgId = uint32(toMsgID)
	imErr = immsghelper.WriteMsgToUidWithId(ctx, toUid, msg, mgr.seqGenCli, mgr.timelineCli)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendExtImMsg - WriteMsgToUidWithId fail , msg: %v , err: %v", msg, imErr)
		return imErr
	}

	imErr = notify.NotifySyncX(ctx, users, notify.ImMsg)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendExtImMsg - NotifySyncX fail , msg: %v , err: %v", msg, imErr)
	}

	log.DebugWithCtx(ctx, "SendExtImMsg - WriteMsgToUidWithId , msg: %v ", msg)
	return nil
}

func (mgr *Manager) SendOfficialExtImMsg(uid, toUid, msgType, sourceType uint32, content string, ext []byte) error {
	now := uint32(time.Now().Unix())
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	users := []uint32{uid, toUid}
	usersMap, err := mgr.accountCli.GetUsersMap(ctx, users)
	if err != nil {
		log.ErrorWithCtx(ctx, "get user failed, err:%v", err)
		return err
	}

	if _, ok := usersMap[uid]; !ok {
		log.ErrorWithCtx(ctx, "get user failed, uid:%d", uid)
		return err
	}

	if _, ok := usersMap[toUid]; !ok {
		log.ErrorWithCtx(ctx, "get user failed, toUid:%d", toUid)
		return err
	}

	msg := &timelinesvrPB.ImMsg{
		FromId:        uid,
		ToId:          toUid,
		FromName:      usersMap[uid].GetUsername(),
		FromNick:      usersMap[uid].GetNickname(),
		ToName:        usersMap[toUid].GetUsername(),
		ToNick:        usersMap[toUid].GetNickname(),
		Type:          msgType,
		Content:       content,
		MsgSourceType: sourceType,
		ClientMsgTime: now,
		ServerMsgTime: now,
		Ext:           ext,
		Status:        uint32(syncPB.NewMessageSync_UN_READ),
		Platform:      uint32(timelinesvrPB.Platform_UNSPECIFIED), // 没有平台限制必须显式指定
	}

	svrMsgID, err := mgr.seqGenCli.GenerateSequence(ctx, toUid, seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendExtImMsg failed to generate svr msg id: %v", err)
		return err
	}

	msg.ServerMsgId = uint32(svrMsgID)
	imErr := immsghelper.WriteMsgToUidWithId(ctx, toUid, msg, mgr.seqGenCli, mgr.timelineCli)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendExtImMsg - WriteMsgToUidWithId fail , msg: %v , err: %v", msg, imErr)
		return imErr
	}

	imErr = notify.NotifySyncX(ctx, users, notify.ImMsg)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendExtImMsg - NotifySyncX fail , msg: %v , err: %v", msg, imErr)
	}

	log.DebugWithCtx(ctx, "SendExtImMsg - WriteMsgToUidWithId , msg: %v ", msg)
	return nil
}

func (mgr *Manager) SendOSS(ctx context.Context, uid, fellowUid, fellowValue uint32, change float32, addType int, addReason string) {
	ossKVMap := make(map[string]interface{})
	ossKVMap["uid"] = uid
	ossKVMap["fellowUid"] = fellowUid
	ossKVMap["type"] = addType
	ossKVMap["reason"] = addReason
	ossKVMap["change"] = change
	ossKVMap["fellowValue"] = fellowValue
	ossKVMap["eventTime"] = time.Now().Format("2006-01-02 15:04:05")
	ossKVMap["totalDate"] = time.Now().Format("2006-01-02 15:04:05")
	datacenter.StdReportKV("************", ossKVMap)
	log.DebugWithCtx(ctx, "SendOSS oss:%+v", ossKVMap)
}

// sendToDataCenter 上报数据
func (mgr *Manager) sendUpgradeMsgToDataCenter(uid, targetUid uint32, content string) {
	now := time.Now()

	const timeLayout = "2006-01-02 15:04:05"

	data := map[string]interface{}{
		"appId":      "ttvoice",
		"totalDate":  now.Format(timeLayout),
		"sendUid":    uid,
		"targetUid":  targetUid,
		"platform":   "",
		"meType":     im.IM_MSG_TYPE_FELLOW_UPGRADE_MSG,
		"createTime": now.Format(timeLayout),
		"content":    content,
		"source":     im.MsgSourceType_MSG_SOURCE_FROM_FELLOW_UPGRADE,
	}

	datacenter.StdReportKV("************", data)
}

// SendRareInfoToDataCenter 稀缺关系获得上报
func (mgr *Manager) SendRareInfoToDataCenter(ctx context.Context, cpGameId, channelId, uid, cpUid, rareId, subRareId,
	expireTime, createTime uint32, rareName, subRareName string) {
	day := expireTime / 3600 / 24
	data := map[string]interface{}{
		"id":          strconv.FormatUint(uint64(cpGameId), 10),
		"channelID":   channelId,
		"uid":         uid,
		"cpUid":       cpUid,
		"rareId":      rareId,
		"subRareId":   subRareId,
		"rareName":    rareName,
		"subRareName": subRareName,
		"day":         day,
		"eventTime":   time.Unix(int64(createTime), 0).Format("2006-01-02 15:04:05"),
		"totalDate":   time.Unix(int64(createTime), 0).Format("2006-01-02 15:04:05"),
	}

	ok := datacenter.StdReportKV("************", data)
	log.DebugWithCtx(ctx, "SendRareInfoToDataCenter, ok: %v, data:%+v", ok, data)
}

const (
	FellowLevelAwardComingText      = "挚友等级再升1级，即可解锁新的挚友等级奖励！查看奖励详情>"
	FellowLevelAwardComingHighLight = "查看奖励详情>"
	FellowLevelAwardComingUrl       = ""

	FellowLevelAwardGainText      = "已解锁新的挚友等级奖励，太棒啦！查看奖励详情>"
	FellowLevelAwardGainHighLight = "查看奖励详情>"
	FellowLevelAwardGainUrl       = ""

	FellowLevelAwardLostText      = "挚友等级下降，已失去 Lv.%d 的挚友等级奖励 T^T  查看详情> "
	FellowLevelAwardLostHighLight = "查看详情>"
	FellowLevelAwardLostUrl       = ""
)

func (mgr *Manager) SendIMWhenFellowLvChange(ctx context.Context, fellowInfo *store.FellowInfo, subPoint int32) error {
	var err error

	nowLvIdx := 0
	lcl := mgr.bc.GetLevelConfigList()
	for i, lv := range lcl {
		if fellowInfo.Point < lv.Point {
			break
		}
		nowLvIdx = i
	}

	// 降级
	if int32(fellowInfo.Point)+subPoint < int32(lcl[nowLvIdx].Point) && lcl[nowLvIdx].Award != "" {
		err = mgr.sendImMsgWithHighLightUrl(ctx, fellowInfo.Uid, fellowInfo.ToUid, fmt.Sprintf(FellowLevelAwardLostText, lcl[nowLvIdx].Level),
			FellowLevelAwardLostHighLight, FellowLevelAwardLostUrl)
	}

	// 升级且新等级有奖励
	if nowLvIdx+1 < len(lcl) &&
		int32(fellowInfo.Point)+subPoint > int32(lcl[nowLvIdx+1].Point) &&
		lcl[nowLvIdx+1].Award != "" {
		err = mgr.sendImMsgWithHighLightUrl(ctx, fellowInfo.Uid, fellowInfo.ToUid, FellowLevelAwardGainText, FellowLevelAwardGainHighLight, FellowLevelAwardGainUrl)
	}

	// 升级且下一级有奖励
	if nowLvIdx+2 < len(lcl) &&
		int32(fellowInfo.Point)+subPoint > int32(lcl[nowLvIdx+1].Point) &&
		lcl[nowLvIdx+2].Award != "" {
		err = mgr.sendImMsgWithHighLightUrl(ctx, fellowInfo.Uid, fellowInfo.ToUid, FellowLevelAwardComingText, FellowLevelAwardComingHighLight, FellowLevelAwardComingUrl)
	}

	return err
}

func (mgr *Manager) sendImMsgWithHighLightUrl(ctx context.Context, uid, toUid uint32, text, highLight, url string) error {
	msg := &apicenter.ImMsg{
		ImType: &apicenter.ImType{
			SenderType:   uint32(apicenter.IM_SENDER_TYPE_IM_SENDER_NORMAL),
			ReceiverType: uint32(apicenter.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
			ContentType:  uint32(apicenter.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL),
		},
		FromUid: uint32(10000),
		ToIdList: []uint32{
			uid,
			toUid,
		},
		ImContent: &apicenter.ImContent{
			TextHlUrl: &apicenter.ImTextWithHighlightUrl{
				Content:    text,
				Hightlight: highLight,
				Url:        url,
			},
		},
		Platform:    apicenter.Platform_UNSPECIFIED,
		AppPlatform: "all",
	}

	err := mgr.apicenterCli.SendImMsg(context.Background(), uid, protocol.TT, []*apicenter.ImMsg{msg}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendImMsgWithHighLightUrl send im msg error: %v, uid: %d, to_uid: %v", uid, toUid)
		return err
	}

	return nil
}

func (mgr *Manager) SendImMsgText(ctx context.Context, fromUid uint32, toUidList []uint32, text string) error {
	msg := &apicenter.ImMsg{
		ImType: &apicenter.ImType{
			SenderType:   uint32(apicenter.IM_SENDER_TYPE_IM_SENDER_NORMAL),
			ReceiverType: uint32(apicenter.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
			ContentType:  uint32(apicenter.IM_CONTENT_TYPE_IM_CONTENT_TEXT),
		},
		FromUid:  fromUid,
		ToIdList: toUidList,
		ImContent: &apicenter.ImContent{
			TextNormal: &apicenter.ImTextNormal{
				Content: text,
			},
		},
		Platform:    apicenter.Platform_UNSPECIFIED,
		AppPlatform: "all",
	}

	err := mgr.apicenterCli.SendImMsg(context.Background(), fromUid, protocol.TT, []*apicenter.ImMsg{msg}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendImMsgWithHighLightUrl send im msg error: %v, to_uid: %+v", toUidList)
		return err
	}

	return nil
}

func (mgr *Manager) PushNotificationToChannel(ctx context.Context, sendUser, targetUser *accountPB.UserResp, inviteMsg *fellow_logic.ChannelFellowMsg, timeVal time.Time, channelId uint32, isInvite bool) error {
	if 0 == channelId {
		return nil
	}
	sendTime := timeVal.UnixNano() / 1000000
	MsgContent, _ := proto.Marshal(inviteMsg)
	channelMsg := channelPB.ChannelBroadcastMsg{
		FromUid:       sendUser.Uid,
		FromNick:      sendUser.Nickname,
		FromAccount:   sendUser.Username,
		ToChannelId:   channelId,
		Time:          uint64(sendTime),
		Type:          uint32(channelPB.ChannelMsgType_FELLOW_CHANNEL_INVITE_MSG),
		Content:       MsgContent,
		PbOptContent:  MsgContent,
		TargetUid:     targetUser.Uid,
		TargetNick:    targetUser.Nickname,
		TargetAccount: targetUser.Username,
	}
	if !isInvite {
		channelMsg.Type = uint32(channelPB.ChannelMsgType_FELLOW_BOUND_MSG)
	}

	err := mgr.expressCli.SendChannelBroadcastMsg(ctx, &channelMsg)
	//err := s.expressCli.SendChannelBroadcastMsg(ctx, &channelMsg)
	log.InfoWithCtx(ctx, "pushNotificationToChannel -- PushMulticast. uid:%d, channelMsg :%v ,err :%v", sendUser.Uid,
		channelMsg, err)

	return err
}

func (mgr *Manager) PushChannelInviteMsgToUsers(ctx context.Context, channelId uint32, inviteMsg *fellow_logic.FellowInviteInfo, targetUidList []uint32, isInvite bool) error {
	if 0 == channelId {
		return nil
	}

	if nil == targetUidList {
		return nil
	}

	userMsgContent, _ := proto.Marshal(inviteMsg)

	pushMessage := pushPb.PushMessage{Cmd: uint32(pushPb.PushMessage_FELLOW_INVITE_MSG), Content: userMsgContent}
	if !isInvite {
		pushMessage.Cmd = uint32(pushPb.PushMessage_FELLOW_RESULT_MSG)
	}

	pushMessageContent, _ := proto.Marshal(&pushMessage)
	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		AppId:              uint32(protocol.TT),
		TerminalTypePolicy: pushclient.DefaultPolicy,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageContent,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 86400,
		},
	}

	err := mgr.pushCli.PushToUsers(ctx, targetUidList, notification)

	return err
}

func (mgr *Manager) PushFellowChangeMsgToUsers(ctx context.Context, fellowMsg *fellow_logic.FellowInfo, targetUidList []uint32) error {
	log.DebugWithCtx(ctx, "PushFellowChangeMsgToUsers fellowMsg: %+v", fellowMsg)

	if nil == targetUidList {
		return nil
	}

	userMsgContent, _ := proto.Marshal(fellowMsg)

	pushMessage := pushPb.PushMessage{Cmd: uint32(pushPb.PushMessage_FELLOW_CHANGE_MSG), Content: userMsgContent}

	pushMessageContent, _ := proto.Marshal(&pushMessage)
	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		AppId:              uint32(protocol.TT),
		TerminalTypePolicy: pushclient.DefaultPolicy,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageContent,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 86400,
		},
	}

	err := mgr.pushCli.PushToUsers(ctx, targetUidList, notification)

	return err
}

func (mgr *Manager) GetBiggestPresentIcon(ctx context.Context, uid, toUid uint32) (*presentPb.StPresentItemConfig, []uint32, error) {
	giftList, err := mgr.store.GetPresentHistoryCache(ctx, uid, toUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentHistoryCache uid：%d, toUid:%, err:%v", uid, toUid, err)
		return nil, giftList, err
	}
	log.DebugWithCtx(ctx, "GetBiggestPresentIcon from cache ,uid:%d, toUid:%d giftList:%v", uid, toUid, giftList)

	if len(giftList) == 0 {
		dbList, _, err := mgr.store.GetPresentHistoryByIdPair(uid, toUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPresentHistoryByIdPair err ,err: %v", err)
			return nil, giftList, err
		}
		log.InfoWithCtx(ctx, "GetPresentHistoryByIdPair err no cache  get from db, uid:%d toUid:%d, giftList:%verr: %v", uid, toUid, giftList, err)
		if len(dbList) == 0 {
			_ = mgr.store.AddPresentHistoryCache(ctx, uid, toUid, []uint32{0})
			return nil, dbList, nil
		} else {
			giftList = dbList
		}
		_ = mgr.store.AddPresentHistoryCache(ctx, uid, toUid, giftList)
	}
	//log.InfoWithCtx(ctx, "GetBiggestPresentIcon done ,uid:%d, toUid:%d giftList:%v", uid, toUid, giftList)
	present, err := mgr.getMaxPricePresentIcon(ctx, uid, toUid, giftList)
	return present, giftList, err
}

func (mgr *Manager) getMaxPricePresentIcon(ctx context.Context, uid, toUid uint32, presentList []uint32) (*presentPb.StPresentItemConfig, error) {
	if len(presentList) <= 0 {
		log.ErrorWithCtx(ctx, "getMaxPricePresentIcon   ,  giftList:%v", presentList)
		return nil, nil
	}

	if len(presentList) == 1 {
		if presentList[0] == 0 {
			return nil, nil
		}
	}

	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()
	resp, err := mgr.presentCli.GetPresentConfigByIdList(ctx, 0, presentList, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "maxValuePresent GetPresentConfigByIdList fail. in :%v , err:%v", presentList, err)
		return nil, err
	}

	if len(resp.GetItemList()) == 0 {
		return nil, err
	}

	sort.Slice(resp.GetItemList(), func(i, j int) bool {
		if resp.GetItemList()[i].GetPriceType() > resp.GetItemList()[j].GetPriceType() {
			return true
		}

		if resp.GetItemList()[i].GetPriceType() < resp.GetItemList()[j].GetPriceType() {
			return false
		}

		if resp.GetItemList()[i].GetPrice() > resp.GetItemList()[j].GetPrice() {
			return true
		}
		return false
	})
	log.DebugWithCtx(ctx, "uid: %d toUid:%d item_id:%d, item_name:%s", uid, toUid, resp.GetItemList()[0].ItemId, resp.GetItemList()[0].Name)
	return resp.GetItemList()[0], nil

}

func (mgr *Manager) GetOnMicFellowList(ctx context.Context, uid, channelId uint32) (resp []*pb.MicFellowInfo, err error) {
	resp = make([]*pb.MicFellowInfo, 0)
	if channelId == 0 {
		return resp, nil
	}

	micList, err := mgr.channelMicCli.GetMicrList(ctx, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOnMicFellowList fail. uid:%v, channelId:%+v, err:%v", uid, channelId, err)
		return resp, err
	}

	micListSize := len(micList.GetAllMicList())
	if micListSize <= 1 {
		return resp, nil
	}

	onMicUid := make(map[uint32][]uint32)
	for idx, v := range micList.AllMicList {
		if idx == micListSize-1 {
			break
		}

		nextMic := micList.AllMicList[idx+1]
		if math.Abs(int64(v.MicId)-int64(nextMic.MicId)) > 1 {
			continue
		}

		uid := v.GetMicUid()
		toUid := nextMic.GetMicUid()

		if uid == 0 || toUid == 0 {
			continue
		}

		if _, ok := onMicUid[toUid]; ok {
			onMicUid[toUid] = append(onMicUid[toUid], uid)
		} else {
			if _, ok := onMicUid[uid]; ok {
				onMicUid[uid] = append(onMicUid[uid], toUid)
			} else {
				onMicUid[toUid] = append(onMicUid[toUid], uid)
			}
		}

		log.DebugWithCtx(ctx, "GetOnMicFellowList uid：%d, onMicUid:%v", uid, onMicUid)
	}

	isInUKWChannel := mgr.IsInUKWChannel(ctx, channelId)
	for onMicUid, toUid := range onMicUid {
		fellowList, err := mgr.store.GetFellowListByUid(onMicUid, toUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetOnMicFellowList uid：%d, err:%v", onMicUid, err)
			continue
		}
		log.DebugWithCtx(ctx, "GetOnMicFellowList uid：%d, fellowList:%v", onMicUid, fellowList)

		for _, fellowInfo := range fellowList {
			if fellowInfo.BindType == uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_UNKNOWN) {
				continue
			}

			rareInfo, err := mgr.GetBindingRare(ctx, fellowInfo.Uid, fellowInfo.ToUid)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetOnMicFellowList.GetBindingRare err: %v, uid: %d, toUid: %d", err, fellowInfo.Uid, fellowInfo.ToUid)
			}

			awardInfo, err := mgr.levelAwardCli.GetUserCurrentAwardInfo(ctx, &fellow_level_award.GetUserCurrentAwardInfoReq{
				MyUid:      fellowInfo.Uid,
				FellowUid:  fellowInfo.ToUid,
				AwardType:  uint32(fellow_level_award.LevelAwardItemType_LEVEL_AWARD_ITEM_LIGATURE),
				FellowType: fellowInfo.FellowType,
			})

			ligatureUrl := mgr.bc.GetFellowLigature(fellowInfo.BindType, fellowInfo.FellowType)
			if len(awardInfo.GetResource()) > 0 {
				ligatureUrl = awardInfo.GetResource()
			}

			micFellow := &pb.MicFellowInfo{
				Uid:         onMicUid,
				FellowUid:   fellowInfo.ToUid,
				BindType:    fellowInfo.BindType,
				FellowType:  fellowInfo.FellowType,
				FellowLevel: fellowInfo.Level,
				CurrentRare: &pb.RareInfo{
					RareId:     rareInfo.RareId,
					SubRareId:  rareInfo.SubRareId,
					BindStatus: rareInfo.BindStatus == store.BindStatusBinding,
				},
				LigatureUrl: ligatureUrl,
			}

			if isInUKWChannel && (mgr.IsUKW(ctx, fellowInfo.ToUid) || mgr.IsUKW(ctx, uid)) {
				micFellow.BindType = uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_UNKNOWN) // 在神秘人特权房间, 且对方是神秘人, 忽略只有关系
				micFellow.FellowType = uint32(pb.FellowType_ENUM_FELLOW_TYPE_UNKNOWN)
				micFellow.FellowLevel = 0
				micFellow.CurrentRare = &pb.RareInfo{}
			}

			resp = append(resp, micFellow)
		}
	}
	return resp, nil
}

func (mgr *Manager) PushOnMicFellowChange(ctx context.Context, uid, channelId uint32) (err error) {
	if channelId == 0 {
		return nil
	}

	resp, err := mgr.GetOnMicFellowList(ctx, uid, channelId)
	if err != nil {
		return err
	}
	if len(resp) == 0 {
		return nil
	}

	log.DebugWithCtx(ctx, "PushOnMicFellowChange: %+v", resp)
	onMicMsg := &pb.MicFellowInfoChangeInfo{}
	onMicMsg.MicFellow = resp
	content, _ := proto.Marshal(onMicMsg)
	return mgr.PushMicFellowInfoToChannel(ctx, uid, channelId, content)
}

func (mgr *Manager) PushMicFellowInfoToChannel(ctx context.Context, uid uint32, channelId uint32, content []byte) error {
	if 0 == channelId {
		return nil
	}
	sendTime := time.Now().Unix()
	channelMsg := channelPB.ChannelBroadcastMsg{
		FromUid:      uid,
		ToChannelId:  channelId,
		Time:         uint64(sendTime),
		Type:         uint32(channelPB.ChannelMsgType_FELLOW_MIC_CHANGE_MSG),
		Content:      content,
		PbOptContent: content,
	}

	err := mgr.expressCli.SendChannelBroadcastMsg(ctx, &channelMsg)
	log.InfoWithCtx(ctx, "pushNotificationToChannel -- PushMulticast. uid:%d,channelId:%d, channelMsg :%v ,err :%v", uid,
		channelId, channelMsg, err)

	return err
}

const (
	checkPlaymateTimeout = 2 * time.Second
)

func (mgr *Manager) IsPlaymate(uidA, uidB uint32) (bool, error) {
	timeoutCtx, cancel := context.WithTimeout(context.Background(), checkPlaymateTimeout)
	defer cancel()
	a2b, b2a, err := mgr.friendshipCli.GetBiFollowing(timeoutCtx, uidA, uidB, true)
	if err != nil {
		return false, err
	}

	if a2b != nil && !a2b.Dropped && b2a != nil && !b2a.Dropped {
		return true, nil
	}

	return false, nil
}

// BatchCheckPlayMate 批量判断是否玩伴, 返回玩伴id列表
func (mgr *Manager) BatchCheckPlayMate(uid uint32, checkUids []uint32) ([]uint32, error) {
	timeoutCtx, cancel := context.WithTimeout(context.Background(), checkPlaymateTimeout)
	defer cancel()
	isFollowingMap, isFollowerMap, err := mgr.friendshipCli.BatchGetBiFollowingWithCache(timeoutCtx, uid, checkUids, true, true)
	if err != nil {
		return []uint32{}, err
	}

	playmateList := make([]uint32, 0, len(checkUids))
	for _, checkUid := range checkUids {
		if isFollowingMap[checkUid] && isFollowerMap[checkUid] {
			playmateList = append(playmateList, checkUid)
		}
	}

	return playmateList, nil
}

func (mgr *Manager) GetFellowList(ctx context.Context, uid uint32) ([]*store.FellowInfo, error) {
	list, err := mgr.store.GetFellowBindListFromCache(ctx, uid)
	if err != nil {
		list, err = mgr.store.GetFellowBindList(uid)
		if err != nil {
			return list, err
		}
		mgr.store.SetFellowBindListCache(ctx, uid, list)
	}
	return list, err
}

func (mgr *Manager) BatchGetBindInfoOfPairList(ctx context.Context, pairList []*pb.FellowPair) ([]*store.FellowInfo, error) {

	list, err := mgr.store.BatchGetFellowBindInfo(pairList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetBindInfoOfPairList err:%v, pairList:%+v", err, pairList)
		return list, err
	}

	return list, nil
}

func (mgr *Manager) GetBindingRare(ctx context.Context, uid, toUid uint32) (*store.RareInfo, error) {
	rareInfo, err := mgr.store.GetBindingRareIdFromCache(ctx, uid, toUid)
	if err != nil {
		rareInfo, err = mgr.store.GetBindingRare(uid, toUid)
		if err != nil {
			return rareInfo, err
		}

		mgr.store.SetBindingRareIdFormCache(ctx, uid, toUid, rareInfo)
	}

	return rareInfo, nil
}

func (mgr *Manager) GetUserRareInfo(ctx context.Context, uid, toUid uint32) ([]*store.RareInfo, error) {
	rareInfoList, err := mgr.store.GetUserRareInfoFromCache(ctx, uid, toUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserRareInfo.GetUserRareInfoFromCache, err: %v, uid: %d", err, uid)
		rareInfoList, err = mgr.store.GetUserRareInfo(uid, toUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserRareInfo.GetUserRareInfo, err: %v, uid: %d", err, uid)
			return rareInfoList, err
		}

		mgr.store.SetUserRareInfoCache(ctx, uid, toUid, rareInfoList)
	}

	return rareInfoList, nil
}

func (mgr *Manager) getChannelShortID(ctx context.Context, channelId uint32) uint32 {
	channelResp, err := mgr.ChannelCli.GetChannelSimpleInfo(ctx, 0, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getCardDisplayId fail to GetChannelSimpleInfo. channelId:%v, err:%v", channelId, err)
		return 0
	}

	displayId := channelResp.GetDisplayId()

	if channelResp.GetChannelType() == uint32(channelPB.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		guildResp, err := mgr.GuildCli.GetGuild(ctx, channelResp.GetBindId())
		if err != nil {
			log.ErrorWithCtx(ctx, "getCardDisplayId fail to GetGuild. channelId:%v, err:%v", channelId, err)
			return 0
		}

		displayId = channelResp.GetBindId()
		if guildResp.GetShortId() > 0 {
			displayId = guildResp.GetShortId()
		}
	}

	return displayId
}

func (mgr *Manager) GetUnboundList(ctx context.Context, uid uint32) ([]store.FellowUnboundOrder, error) {
	unboundList, err := mgr.store.GetUnboundListFromCache(ctx, uid)
	if err != nil {
		unboundList, err = mgr.store.GetUnboundList(uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUnboundList from db, uid: %v, error: %v", uid, err)
			return unboundList, err
		}

		mgr.store.SetUnboundListCache(ctx, uid, unboundList)
	}

	return unboundList, nil
}

func (mgr *Manager) GetFellowImInviteCountByInviteUid(ctx context.Context, uid uint32, status uint32) (uint32, error) {
	count, err := mgr.store.GetFellowImInviteCountByInviteUidFromCache(ctx, uid, status)
	if err != nil {
		count, err = mgr.store.GetFellowImInviteCountByInviteUid(uid, status)
		mgr.store.SetFellowImInviteCountByInviteUidCache(ctx, uid, status, count)
	}
	return count, err
}

func newHttpClient() *http.Client {
	transport := http.DefaultTransport.(*http.Transport)
	transport.MaxIdleConnsPerHost = 100

	return &http.Client{
		Transport: transport,
		Timeout:   time.Second * 5,
	}
}

func (mgr *Manager) GetPresentCfgCache() map[uint32]*presentPb.StPresentItemConfig {
	mgr.rwMutex.RLock()
	defer mgr.rwMutex.RUnlock()

	return mgr.PresentCfgCache
}

func (mgr *Manager) TestPushIMMgs(ctx context.Context, uid, toUid, newLevel uint32) error {
	newPoint, _ := mgr.bc.GetCurrentAndNextLevPoint(newLevel)
	mgr.store.SetFellowPoint(uid, toUid, 0)
	mgr.store.DelCache(ctx, uid, toUid)
	mgr.AddFellowPoint(ctx, uid, toUid, float32(newPoint), "test im", 0, false)
	return nil
}
