package store

/*
import (
	"context"
	"testing"
	"time"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/services/tt-rev/fellow-svr/internal/conf"
)

const skipTest = true

func TestStore_SaveRelation(t *testing.T) {
	if skipTest {
		t.Skip()
	}
	store, err := NewStore(context.Background(), &conf.StartConfig{
		MongoConfig: &config.MongoConfig{
			Addrs:    "localhost:27017",
			Database: "fellow_db",
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	relation := &Relation{
		CreateTime: time.Now().Unix(),
		UpdateTime: time.Now().Unix(),
		Name:       "test",
		Type:       1,
		AnimationOfSettlement: &Resource{
			ResourceUrl: "test",
			SourceType:  0,
			Md5:         "123",
			Name:        "123",
		},
		RelationshipBox: &RelationBox{
			Background: &Resource{
				ResourceUrl: "fafsfs",
				SourceType:  3,
				Md5:         "111",
			},
			BigBackground: nil,
		},
		ConnectedForMic: &ConnectedForMic{
			Left:  "1212",
			Right: "1212",
		},
		SpaceFlagUrl:          "123123",
		CardFlagColor:         "#ff0000",
		ImFlagUrl:             "123123",
		FriendSpaceBackground: "123123",
		MsgNotifyConfig: &MsgNotifyConfig{
			Origin:    "1221",
			Thumbnail: "1212",
		},
		BizType: 1,
		SubRelationList: RelationList{
			&Relation{ImFlagUrl: "123123", Name: "name1"},
			&Relation{ImFlagUrl: "31312312", Name: "name2"},
		},
	}
	id, err := store.SaveRelation(context.Background(), relation)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(id)
	for _, item := range relation.SubRelationList {
		t.Logf("%s:%d", item.Name, item.Id)
	}
}

func TestStore_UpdateRelation(t *testing.T) {

	store, err := NewStore(context.Background(), &conf.StartConfig{
		MongoConfig: &config.MongoConfig{
			Addrs:    "*************:27017",
			Database: "fellow_db",
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	relation := &Relation{
		Id:         7,
		CreateTime: time.Now().Unix(),
		UpdateTime: time.Now().Unix(),
		Name:       "test123",
		Type:       1,
		AnimationOfSettlement: &Resource{
			ResourceUrl: "test",
			SourceType:  0,
			Md5:         "123",
			Name:        "123",
		},
		RelationshipBox: &RelationBox{
			Background: &Resource{
				ResourceUrl: "fafsfs",
				SourceType:  3,
				Md5:         "111",
			},
			BigBackground: nil,
		},
		ConnectedForMic: &ConnectedForMic{
			Left:  "1212",
			Right: "1212",
		},
		SpaceFlagUrl:          "123123",
		CardFlagColor:         "#ff0000",
		ImFlagUrl:             "123123",
		FriendSpaceBackground: "123123",
		MsgNotifyConfig: &MsgNotifyConfig{
			Origin:    "1221",
			Thumbnail: "1212",
		},
		BizType: 1,
		SubRelationList: RelationList{
			&Relation{Id: 1, ImFlagUrl: "123123", Name: "name1"},
			&Relation{Id: 2, ImFlagUrl: "31312312", Name: "name2"},
		},
	}
	id, err := store.UpdateRelation(context.Background(), relation)
	if err != nil {
		t.Fatal(err)
	}
	if id != 7 {
		t.Fatal("id should equal 7")
	}
}

func TestStore_SearchRelationByCondition(t *testing.T) {
	if skipTest {
		t.Skip()
	}
	store, err := NewStore(context.Background(), &conf.StartConfig{
		MongoConfig: &config.MongoConfig{
			Addrs:    "localhost:27017",
			Database: "fellow_db",
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	name := "test"
	relation := &Relation{
		CreateTime: time.Now().Unix(),
		UpdateTime: time.Now().Unix(),
		Name:       name,
		Type:       1,
		AnimationOfSettlement: &Resource{
			ResourceUrl: "test",
			SourceType:  0,
			Md5:         "123",
			Name:        "123",
		},
		RelationshipBox: &RelationBox{
			Background: &Resource{
				ResourceUrl: "fafsfs",
				SourceType:  3,
				Md5:         "111",
			},
			BigBackground: nil,
		},
		ConnectedForMic: &ConnectedForMic{
			Left:  "1212",
			Right: "1212",
		},
		SpaceFlagUrl:          "123123",
		CardFlagColor:         "#ff0000",
		ImFlagUrl:             "123123",
		FriendSpaceBackground: "123123",
		MsgNotifyConfig: &MsgNotifyConfig{
			Origin:    "1221",
			Thumbnail: "1212",
		},
		BizType: 1,
		SubRelationList: RelationList{
			&Relation{ImFlagUrl: "123123", Name: "name1"},
			&Relation{ImFlagUrl: "31312312", Name: "name2"},
		},
	}
	id, err := store.SaveRelation(context.Background(), relation)
	if err != nil {
		t.Fatal(err)
	}
	pageSize := 10
	pageIndex := 0
	result, err := store.SearchRelationByCondition(context.Background(), WithSkip(pageSize*pageIndex), WithLimit(pageSize), WithRelationId(id))
	if err != nil {
		t.Fatal(err)
	}
	if result.IsEmpty() {
		t.Errorf("result should not be empty")
	}
	result2, err := store.SearchRelationByCondition(context.Background(), WithSkip(pageSize*pageIndex), WithLimit(pageSize), WithRelationName(name))
	if err != nil {
		t.Fatal(err)
	}
	if result2.IsEmpty() {
		t.Errorf("result should not be empty")
	}
	result3, err := store.SearchRelationByCondition(context.Background(), WithSkip(pageSize*(pageIndex+10)), WithLimit(pageSize), WithRelationName(name))
	if err != nil {
		t.Fatal(err)
	}
	if !result3.IsEmpty() {
		t.Errorf("result should be empty")
	}
	result4, err := store.SearchRelationByCondition(context.Background(),
		WithCreateTime(EQUAL|GT, time.Now().Add(-time.Hour*24*20)),
		WithEndTime(EQUAL|LT, time.Now().Add(-time.Hour*24*10)),
	)
	if err != nil {
		t.Fatal(err)
	}
	if !result4.IsEmpty() {
		t.Errorf("result4 should be empty")
	}

	count, err := store.CountRelationByCondition(context.Background(), WithSkip(pageSize*(pageIndex+1)), WithLimit(pageSize), WithRelationId(id))
	if err != nil {
		t.Fatal(err)
	}
	if count != 1 {
		t.Errorf("count should be 1")
	}
	count1, err := store.CountRelationByCondition(context.Background(), WithSkip(pageSize*(pageIndex+1)), WithLimit(pageSize), WithRelationName(name))
	if err != nil {
		t.Fatal(err)
	}
	if count1 <= 1 {
		t.Errorf("count should greater than 1")
	}

}

func TestStore_AddRelation(t *testing.T) {
	if skipTest {
		t.Skip()
	}
	st.SaveRelation(context.Background(), &Relation{
		Id:                    888,
		CreateTime:            0,
		UpdateTime:            0,
		Name:                  "rareName",
		Type:                  0,
		AnimationOfSettlement: nil,
		RelationshipBox:       nil,
		ConnectedForMic:       nil,
		SpaceFlagUrl:          "",
		CardFlagColor:         "",
		ImFlagUrl:             "",
		FriendSpaceBackground: "",
		MsgNotifyConfig:       nil,
		BizType:               0,
		SubRelationList: []*Relation{
			{
				Id:   1,
				Name: "subA",
			},
			{
				Id:   2,
				Name: "subB",
			},
		},
	})
}

func TestStore_GetRelationByIds(t *testing.T) {
	if skipTest {
		t.Skip()
	}
	relations, err := st.GetRelationByIds(context.Background(), 1, 2, 3)
	if err != nil {
		t.Error(err)
	}

	t.Logf("%+v", relations)
}
*/
