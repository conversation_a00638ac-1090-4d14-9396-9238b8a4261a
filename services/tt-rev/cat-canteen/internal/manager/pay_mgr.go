package manager

import (
	"context"
	"errors"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	unifiedPayPB "golang.52tt.com/protocol/services/unified_pay"
	UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
	"golang.52tt.com/services/tt-rev/cat-canteen/internal/mysql"
	"time"
)

func (m *Mgr) freeze(ctx context.Context, orderId string, uid, totalPrice uint32, outsideTime time.Time) (uint32, error) {
	// unified-pay 冻结T豆
	timeStr := outsideTime.Format("2006-01-02 15:04:05")
	reason := fmt.Sprintf("购买「%s」", m.chancePackInfo.GetPackName())

	restBalance, sErr := m.rpcCli.UnifiedPayCli.PresetFreeze(ctx, uid, totalPrice, m.bc.GetPayAppId(), orderId, timeStr, reason)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "freeze fail to PresetFreeze. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, sErr)
		return restBalance, sErr
	}

	_, err := m.store.ChangeConsumeRecordPayInfo(ctx, outsideTime, uid, []uint32{mysql.ConsumeStatusInit}, mysql.ConsumeStatusFreezing, orderId, "")
	if err != nil {
		log.ErrorWithCtx(ctx, "freeze fail to ChangeConsumeRecordPayInfo. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
		return restBalance, err
	}

	log.Infof("freeze uid:%d, orderId:%s, totalPrice:%d, outsideTime:%v, restBalance:%d", uid, orderId, totalPrice, outsideTime, restBalance)
	return restBalance, nil
}

func (m *Mgr) commit(ctx context.Context, orderId string, uid, cnt uint32, outsideTime time.Time) error {
	user, err := m.rpcCli.AccountCli.GetUser(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "commit fail to GetUser. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
		return err
	}

	reason := fmt.Sprintf("购买「%s」", m.chancePackInfo.GetPackName())

	req := &unifiedPayPB.UnfreezeAndConsumeReq{
		AppId:      m.bc.GetPayAppId(),
		Uid:        uid,
		UserName:   user.GetUsername(),
		ItemId:     m.chancePackInfo.GetPackId(),
		ItemName:   reason,
		ItemNum:    cnt,
		ItemPrice:  ChanceTBean,
		TotalPrice: cnt * ChanceTBean,
		Platform:   "0",
		OutTradeNo: orderId,
		Notes:      "cat-canteen",
	}

	// unified-pay 确认扣除T豆
	timeStr, _, err := m.rpcCli.UnifiedPayCli.UnfreezeAndConsume(ctx, req)
	if err != nil {
		// 货币组commit接口在并发时会因锁报错，在这里重试一次
		retryCtx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
		defer cancel()

		timeStr, _, err = m.rpcCli.UnifiedPayCli.UnfreezeAndConsume(retryCtx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "commit fail to UnfreezeAndConsume. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
			return err
		}
	}

	_, _ = m.store.ChangeConsumeRecordPayInfo(ctx, outsideTime, uid, []uint32{mysql.ConsumeStatusAddedChance}, mysql.ConsumeStatusDone, orderId, timeStr)

	log.Infof("commit uid:%d, orderId:%s, outsideTime:%v, cTime:%v", uid, orderId, outsideTime, timeStr)
	return nil
}

func (m *Mgr) rollback(ctx context.Context, orderId string, uid uint32, outsideTime time.Time, needUpdateStatus bool) error {
	var err error
	if needUpdateStatus {
		// 更新订单状态为失败
		sourceStatusList := []uint32{
			mysql.ConsumeStatusInit,
			mysql.ConsumeStatusFreezing,
		}
		ok, err := m.store.ChangeConsumeRecordPayInfo(ctx, outsideTime, uid, sourceStatusList, mysql.ConsumeStatusRollback, orderId, "")
		if err != nil {
			log.ErrorWithCtx(ctx, "rollback fail to ChangeConsumeRecordPayInfo. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
			return err
		}

		if !ok {
			err = errors.New("订单状态更新失败")
			log.ErrorWithCtx(ctx, "rollback fail to ChangeConsumeRecordPayInfo. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
			return err
		}
	}

	// unified-pay 解冻T豆
	err = m.rpcCli.UnifiedPayCli.UnFreezeAndRefund(ctx, uid, m.bc.GetPayAppId(), orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "rollback fail to UnFreezeAndRefund. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
		return err
	}

	log.Infof("rollback uid:%v, orderId:%v, outsideTime:%v", uid, orderId, outsideTime)
	return nil
}

func (m *Mgr) Callback(ctx context.Context, uid uint32, orderId string) (op UnifiedPayCallback.Op, err error) {
	now := time.Now()
	order, exist, err := m.store.GetConsumeRecordByPayId(ctx, uid, now, orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "Callback fail to GetConsumeRecordByPayId. orderId:%v, err:%v", orderId, err)
		return 0, err
	}

	if !exist {
		lastMonthTime := getLastMonthTime(now)
		// 查上月
		order, exist, err = m.store.GetConsumeRecordByPayId(ctx, uid, lastMonthTime, orderId)
		if err != nil {
			log.ErrorWithCtx(ctx, "Callback fail to GetConsumeRecordByPayId. orderId:%v, err:%v", orderId, err)
			return 0, err
		}
	}

	if !exist {
		// 不可能订单不存在的，有问题，返回错误
		log.ErrorWithCtx(ctx, "Callback fail to GetMagicSpiritOrder. orderId:%v, err:%v", orderId, "order not exist")
		return 0, protocol.NewExactServerError(nil, status.ErrCatCanteenOrderNotExist)
	}

	if order.CreateTime.Add(5 * time.Minute).After(now) {
		// 回调时机有问题，返回错误
		log.ErrorWithCtx(ctx, "Callback fail. orderId:%v, err:%v", orderId, "callback too early")
		return 0, protocol.NewExactServerError(nil, status.ErrCatCanteenOrderNotExist, "回调时机有误")
	}

	outsideTime := order.CreateTime
	orderStatus := order.Status
	if orderStatus == mysql.ConsumeStatusDone || orderStatus == mysql.ConsumeStatusAddedChance {
		err := m.commit(ctx, orderId, order.Uid, order.Amount, outsideTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "Callback fail to commit. orderId:%+v, err:%v", orderId, err)
			return 0, err
		}

		op = UnifiedPayCallback.Op_COMMIT

	} else {
		needUpdateStatus := orderStatus != mysql.ConsumeStatusRollback
		err := m.rollback(ctx, orderId, order.Uid, outsideTime, needUpdateStatus)
		if err != nil {
			log.ErrorWithCtx(ctx, "Callback fail to rollback. orderId:%+v, err:%v", orderId, err)
			return 0, err
		}

		op = UnifiedPayCallback.Op_ROLLBACK
	}

	log.Infof("Callback uid:%d, order:%v, orderId:%v", uid, order, orderId)
	return op, nil
}

func getLastMonthTime(now time.Time) time.Time {
	return time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)
}
