package mgr

import (
	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/protocol/app"
	pb "golang.52tt.com/protocol/services/channel-dating-game"
)

func TestMgr_AddDatingUser(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()
	type args struct {
		cid   uint32
		uid   uint32
		micId uint32
	}
	tests := []struct {
		name                string
		args                args
		wantPushEntranceMsg bool
		wantErr             bool
		mock                func()
	}{
		{
			name: "",
			args: args{
				cid:   cid,
				uid:   uidA,
				micId: 2,
			},
			wantPushEntranceMsg: false,
			wantErr:             false,
			mock: func() {
				cacheCli.EXPECT().AddDatingUser(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				cacheCli.EXPECT().GetUserLikeBeatVal(gomock.Any(), gomock.Any()).Return(uint32(1), nil).AnyTimes()
				userProfileCli.EXPECT().GetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(&app.UserProfile{}, nil).AnyTimes()
				pushCli.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			gotPushEntranceMsg, err := m.AddDatingUser(tt.args.cid, tt.args.uid, tt.args.micId)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddDatingUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotPushEntranceMsg != tt.wantPushEntranceMsg {
				t.Errorf("AddDatingUser() gotPushEntranceMsg = %v, want %v", gotPushEntranceMsg, tt.wantPushEntranceMsg)
			}
			time.Sleep(2 * time.Second)
		})
	}
}

func TestMgr_AddUserLikeBeatVal(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		in0 context.Context
		uid uint32
		cid uint32
		val uint32
	}
	tests := []struct {
		name    string
		args    args
		want    uint32
		wantErr bool
		mock    func()
	}{
		{
			name: "",
			args: args{
				in0: ctx,
				uid: uidA,
				cid: cid,
				val: uint32(10000),
			},
			want:    1,
			wantErr: false,
			mock: func() {
				cacheCli.EXPECT().AddUserLikeBeatVal(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil).AnyTimes()
				cacheCli.EXPECT().GetDatingUser(gomock.Any(), gomock.Any()).Return(uint32(2), true, nil).AnyTimes()
				userProfileCli.EXPECT().GetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(userA, nil).AnyTimes()
				pushCli.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			got, err := m.AddUserLikeBeatVal(tt.args.in0, tt.args.uid, tt.args.cid, tt.args.val)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddUserLikeBeatVal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("AddUserLikeBeatVal() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_ClearUserLikeBeatContent(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		cid uint32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		mock    func()
	}{
		{
			name: "",
			args: args{
				cid: cid,
			},
			wantErr: false,
			mock: func() {
				cacheCli.EXPECT().ClearUserLikeBeatVal(gomock.Any()).AnyTimes()
				cacheCli.EXPECT().ClearUserLikeBeatObj(gomock.Any()).AnyTimes()
				cacheCli.EXPECT().ClearOpenLikeUser(gomock.Any()).AnyTimes()
				cacheCli.EXPECT().ClearChannelFollowUserList(gomock.Any()).AnyTimes()
				cacheCli.EXPECT().ClearDatingUserList(gomock.Any()).AnyTimes()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			if err := m.ClearUserLikeBeatContent(tt.args.cid); (err != nil) != tt.wantErr {
				t.Errorf("ClearUserLikeBeatContent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMgr_ClearUserLikeBeatObj(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		cid uint32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		mock    func()
	}{
		{
			name: "",
			args: args{
				cid: cid,
			},
			wantErr: false,
			mock: func() {
				cacheCli.EXPECT().ClearUserLikeBeatObj(gomock.Any()).AnyTimes().AnyTimes()
				cacheCli.EXPECT().ClearChannelFollowUserList(gomock.Any()).AnyTimes()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			if err := m.ClearUserLikeBeatObj(tt.args.cid); (err != nil) != tt.wantErr {
				t.Errorf("ClearUserLikeBeatObj() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMgr_DelDatingUser(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		cid   uint32
		uid   uint32
		micId uint32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		mock    func()
	}{
		{
			name: "",
			args: args{
				cid:   cid,
				uid:   uidA,
				micId: 2,
			},
			wantErr: false,
			mock: func() {
				cacheCli.EXPECT().DelDatingUser(gomock.Any(), gomock.Any())
				cacheCli.EXPECT().DelUserLikeBeatObj(gomock.Any(), gomock.Any()).AnyTimes()
				cacheCli.EXPECT().GetFollowUserList(gomock.Any(), gomock.Any()).Return([]uint32{1, 2}, nil).AnyTimes()
				cacheCli.EXPECT().GetDatingUser(gomock.Any(), gomock.Any()).Return(uidA, true, nil).AnyTimes()
				cacheCli.EXPECT().ClearFollowUser(gomock.Any(), gomock.Any()).AnyTimes()
				userProfileCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*app.UserProfile{uidA: userA, uidB: userB}, nil).AnyTimes()
				pushCli.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				pushCli.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				seqGenCli.EXPECT().GenerateSequence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint64(1), nil).AnyTimes()

			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			if err := m.DelDatingUser(tt.args.cid, tt.args.uid, tt.args.micId); (err != nil) != tt.wantErr {
				t.Errorf("DelDatingUser() error = %v, wantErr %v", err, tt.wantErr)
			}
			time.Sleep(time.Second)
		})
	}
}

func TestMgr_FollowUser(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		ctx     context.Context
		fromUid uint32
		toUid   uint32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		mock    func()
	}{
		{
			name: "",
			args: args{
				ctx:     ctx,
				fromUid: uidA,
				toUid:   uidB,
			},
			wantErr: false,
			mock: func() {
				seqGenCli.EXPECT().GenerateSequence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint64(1), nil).AnyTimes()
				friendshipCli.EXPECT().FollowUser(gomock.Any(), gomock.Any()).Return(true, true, nil).AnyTimes()

			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			if err := m.FollowUser(tt.args.ctx, tt.args.fromUid, tt.args.toUid); (err != nil) != tt.wantErr {
				t.Errorf("FollowUser() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMgr_GetDatingUserList(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		cid uint32
	}
	tests := []struct {
		name    string
		args    args
		want    map[uint32]uint32
		wantErr bool
		mock    func()
	}{
		{
			name:    "",
			args:    args{cid: cid},
			want:    map[uint32]uint32{uidA: uint32(1)},
			wantErr: false,
			mock: func() {
				cacheCli.EXPECT().GetDatingUserList(gomock.Any()).Return(map[uint32]uint32{uidA: uint32(1)}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			got, err := m.GetDatingUserList(tt.args.cid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDatingUserList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetDatingUserList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_GetMicUserLikeBeatVal(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		in0 context.Context
		cid uint32
	}
	tests := []struct {
		name    string
		args    args
		want    map[uint32]uint32
		wantErr bool
		mock    func()
	}{
		{
			name: "",
			args: args{
				in0: ctx,
				cid: cid,
			},
			want:    map[uint32]uint32{uidA: uint32(1), uidB: uint32(2)},
			wantErr: false,
			mock: func() {
				cacheCli.EXPECT().GetDatingUserList(gomock.Any()).Return(map[uint32]uint32{uidA: uint32(1), uidB: uint32(2)}, nil).AnyTimes()
				cacheCli.EXPECT().GetUserLikeBeatValMap(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{uidA: uint32(1), uidB: uint32(2)}, nil).AnyTimes()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			got, err := m.GetMicUserLikeBeatVal(tt.args.in0, tt.args.cid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMicUserLikeBeatVal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetMicUserLikeBeatVal() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_GetMicUserUserLikeBeatInfo(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()
	type args struct {
		in0 context.Context
		cid uint32
	}
	tests := []struct {
		name    string
		args    args
		want    []*pb.UserLikeBeatInfo
		wantErr bool
		mock    func()
	}{
		{
			name: "",
			args: args{
				in0: ctx,
				cid: cid,
			},
			want: []*pb.UserLikeBeatInfo{
				{
					Uid:          uidA,
					LikeBeatVal:  1,
					SelectStatus: true,
				},
			},
			wantErr: false,
			mock: func() {
				cacheCli.EXPECT().GetDatingUserList(gomock.Any()).Return(map[uint32]uint32{uidA: uint32(1)}, nil).AnyTimes()
				cacheCli.EXPECT().GetUserLikeBeatValMap(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{uidA: uint32(1)}, nil)
				cacheCli.EXPECT().GetAllUserLikeBeatObj(gomock.Any()).Return(map[uint32]uint32{uidA: uint32(1)}, nil).AnyTimes()

			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			got, err := m.GetMicUserUserLikeBeatInfo(tt.args.in0, tt.args.cid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMicUserUserLikeBeatInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetMicUserUserLikeBeatInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_GetOpenLikeUserList(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		cid uint32
	}
	tests := []struct {
		name    string
		args    args
		want    []*pb.OpenLikeUserInfo
		wantErr bool
		mock    func()
	}{
		{
			name: "",
			args: args{
				cid: cid,
			},
			want: []*pb.OpenLikeUserInfo{
				{
					OpenUid: uidA,
					LikeUid: 1,
				},
			},
			wantErr: false,
			mock: func() {
				cacheCli.EXPECT().GetOpenLikeUserList(gomock.Any()).Return([]uint32{uidA}, nil).AnyTimes()
				cacheCli.EXPECT().GetAllUserLikeBeatObj(gomock.Any()).Return(map[uint32]uint32{uidA: uint32(1)}, nil).AnyTimes()

			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			got, err := m.GetOpenLikeUserList(tt.args.cid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOpenLikeUserList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOpenLikeUserList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_GetSelectLikeBeatObjUsers(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		cid uint32
	}
	tests := []struct {
		name    string
		args    args
		want    []uint32
		wantErr bool
		mock    func()
	}{
		{
			name: "",
			args: args{
				cid: cid,
			},
			want:    []uint32{uidA},
			wantErr: false,
			mock: func() {
				cacheCli.EXPECT().GetAllUserLikeBeatObj(gomock.Any()).Return(map[uint32]uint32{uidA: uidB}, nil).AnyTimes()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			got, err := m.GetSelectLikeBeatObjUsers(tt.args.cid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSelectLikeBeatObjUsers() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSelectLikeBeatObjUsers() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_GetUserLikeBeatVal(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		in0 context.Context
		uid uint32
		cid uint32
	}
	tests := []struct {
		name    string
		args    args
		want    uint32
		wantErr bool
		mock    func()
	}{
		{
			name: "",
			args: args{
				cid: cid,
			},
			want:    10000,
			wantErr: false,
			mock: func() {
				cacheCli.EXPECT().GetUserLikeBeatVal(gomock.Any(), gomock.Any()).Return(uint32(10000), nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			got, err := m.GetUserLikeBeatVal(tt.args.in0, tt.args.uid, tt.args.cid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserLikeBeatVal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetUserLikeBeatVal() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_GetUserLikeBeatValMap(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		in0     context.Context
		cid     uint32
		uidList []uint32
	}
	tests := []struct {
		name    string
		args    args
		want    map[uint32]uint32
		wantErr bool
		mock    func()
	}{
		{
			name: "",
			args: args{
				in0:     ctx,
				cid:     cid,
				uidList: []uint32{uidA, uidB},
			},
			want:    map[uint32]uint32{uidA: 10, uidB: 20},
			wantErr: false,
			mock: func() {
				cacheCli.EXPECT().GetUserLikeBeatValMap(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{uidA: 10, uidB: 20}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			got, err := m.GetUserLikeBeatValMap(tt.args.in0, tt.args.cid, tt.args.uidList)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserLikeBeatValMap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserLikeBeatValMap() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_GetUserRankUserLikeBeatVals(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		in0 context.Context
		cid uint32
	}
	tests := []struct {
		name    string
		args    args
		want    []*pb.UserLikeBeatInfo
		wantErr bool
		mock    func()
	}{
		{
			name: "",
			args: args{
				in0: ctx,
				cid: cid,
			},
			want: []*pb.UserLikeBeatInfo{
				{
					Uid:          uidA,
					LikeBeatVal:  100,
					SelectStatus: true,
				},
			},
			wantErr: false,
			mock: func() {
				cacheCli.EXPECT().GetUserRankLikeBeatVals(gomock.Any()).Return([]*pb.UserLikeBeatInfo{{Uid: uidA, LikeBeatVal: 100, SelectStatus: true}}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			got, err := m.GetUserRankUserLikeBeatVals(tt.args.in0, tt.args.cid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserRankUserLikeBeatVals() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserRankUserLikeBeatVals() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_OpenUserLikeBeatObj(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		ctx context.Context
		cid uint32
		uid uint32
	}
	tests := []struct {
		name              string
		args              args
		wantLikeEachOther bool
		wantLikeUid       uint32
		wantErr           bool
		mock              func()
	}{
		{
			name: "",
			args: args{
				ctx: ctx,
				cid: cid,
				uid: uidA,
			},
			wantLikeEachOther: false,
			wantLikeUid:       uidB,
			wantErr:           false,
			mock: func() {
				cacheCli.EXPECT().CheckCanOpenLikeUser(gomock.Any()).Return(true, nil).AnyTimes()
				cacheCli.EXPECT().CheckIsOpenUser(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				cacheCli.EXPECT().GetUserLikeBeatObj(gomock.Any(), gomock.Any()).Return(uidB, nil).AnyTimes()
				cacheCli.EXPECT().SetOpenLikeUserTTL(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				cacheCli.EXPECT().AddOpenLikeUser(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				cacheCli.EXPECT().CheckIsOpenUser(gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
				userProfileCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*app.UserProfile{uidA: userA, uidB: userB}, nil).AnyTimes()
				pushCli.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
				seqGenCli.EXPECT().GenerateSequence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint64(1), nil).AnyTimes()
				pushCli.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			gotLikeEachOther, gotLikeUid, err := m.OpenUserLikeBeatObj(tt.args.ctx, tt.args.cid, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("OpenUserLikeBeatObj() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotLikeEachOther != tt.wantLikeEachOther {
				t.Errorf("OpenUserLikeBeatObj() gotLikeEachOther = %v, want %v", gotLikeEachOther, tt.wantLikeEachOther)
			}
			if gotLikeUid != tt.wantLikeUid {
				t.Errorf("OpenUserLikeBeatObj() gotLikeUid = %v, want %v", gotLikeUid, tt.wantLikeUid)
			}
			time.Sleep(time.Second)
		})
	}
}

func TestMgr_SetUserLikeBeatObj(t *testing.T) {
	m, ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		in0     context.Context
		cid     uint32
		uid     uint32
		likeUid uint32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		mock    func()
	}{
		{
			name: "",
			args: args{
				in0:     ctx,
				cid:     cid,
				uid:     uidA,
				likeUid: uidB,
			},
			wantErr: false,
			mock: func() {
				cacheCli.EXPECT().GetUserLikeBeatObj(gomock.Any(), gomock.Any()).Return(uidB, nil)
				cacheCli.EXPECT().GetDatingUser(gomock.Any(), gomock.Any()).Return(uint32(1), true, nil).AnyTimes()
				cacheCli.EXPECT().DelUserLikeBeatObj(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				cacheCli.EXPECT().DelFollowUser(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				cacheCli.EXPECT().SetUserLikeBeatObj(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				cacheCli.EXPECT().AddFollowUser(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			if err := m.SetUserLikeBeatObj(tt.args.in0, tt.args.cid, tt.args.uid, tt.args.likeUid); (err != nil) != tt.wantErr {
				t.Errorf("SetUserLikeBeatObj() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
