package mgr

import (
	"bufio"
	"bytes"
	"context"
	"errors"
	"fmt"
	"image"
	"image/gif"
	"image/jpeg"
	"image/png"
	"io/ioutil"
	"net/http"
	"os"
	"path"
	"time"

	"golang.52tt.com/services/tt-rev/channel-dating-game/internal/conf"

	"github.com/fogleman/gg"
	"golang.52tt.com/clients/obsgateway"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app"
	channelPB "golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/services/channel-dating-game"
	hiPB "golang.52tt.com/protocol/services/headimagesvr"
)

// InitResource 初始化资源
func (m *Mgr) InitResource() error {
	for _, item := range m.bc.GetDrawConfList().SceneList {
		_, err := m.loadImg(item.BackgroundPicName)
		if err != nil {
			log.Errorf("InitResource fail to BackgroundPicName. err:%v", err)
			return err
		}
		_, err = m.loadImg(item.ImBackgroundPicName)
		if err != nil {
			log.Errorf("InitResource fail to BackgroundPicName. err:%v", err)
			return err
		}
	}
	_, err := m.loadFont(m.bc.GetDrawConfList().FontFileName)
	if err != nil {
		log.Errorf("InitResource fail to FontFileName. err:%v", err)
		return err
	}
	_, err = m.loadImg(m.bc.GetDrawConfList().DefaultHeadPic)
	if err != nil {
		log.Errorf("InitResource fail to DefaultHeadPic. err:%v", err)
		return err
	}
	return nil
}

func (m *Mgr) CreateChannelImage(ctx context.Context, cid uint32, userA, userB *app.UserProfile, hatA,
	hatB *pb.DatingGameHatCfg, sceneCfg *conf.SceneInfo) (imageUrl string, err error) {
	uidA, uidB := userA.GetUid(), userB.GetUid()

	defer func() {
		if err := recover(); err != nil {
			log.Errorf("CreateChannelImage panic. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
		}
	}()

	if sceneCfg == nil {
		log.Errorf("CreateChannelImage fail. sceneCfg == nil. uid:%v, cid:%d", []uint32{uidA, uidB}, cid)
		return
	}

	drawConf := m.bc.GetDrawConfList()
	if drawConf == nil {
		log.Errorf("CreateChannelImage fail. drawConf == nil. uid:%v, cid:%d", []uint32{uidA, uidB}, cid)
		return
	}

	param := sceneCfg.BackgroundParam
	// 加载头像
	uidAHead, err := m.loadUserHeadImage(ctx, uidA, param.Radius, userA.GetAccount(), drawConf.DefaultHeadPic)
	if err != nil {
		log.Errorf("CreateChannelImage fail to loadUserHeadImage. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
		return "", err
	}

	uidBHead, err := m.loadUserHeadImage(ctx, uidB, param.Radius, userB.GetAccount(), drawConf.DefaultHeadPic)
	if err != nil {
		log.Errorf("CreateChannelImage fail to loadUserHeadImage. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
		return "", err
	}

	bgPic := sceneCfg.BackgroundPicName
	// 加载背景
	bgImage, err := m.loadImg(bgPic)
	if err != nil {
		log.Errorf("CreateChannelImage fail to loadImg. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
		return "", err
	}

	c := gg.NewContextForImage(bgImage)
	// 将头像画在背景上
	c.DrawImage(uidAHead, int(param.UserA.X), int(param.UserA.Y))
	c.DrawImage(uidBHead, int(param.UserB.X), int(param.UserB.Y))

	// 画帽子
	if hatA != nil {
		err = m.drawHat(uidA, c, sceneCfg, hatA, false, true)
		if err != nil {
			log.Errorf("CreateChannelImage fail to drawHat. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
			return "", err
		}
	}

	if hatB != nil {
		err = m.drawHat(uidB, c, sceneCfg, hatB, false, false)
		if err != nil {
			log.Errorf("CreateChannelImage fail to drawHat. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
			return "", err
		}
	}

	// 字体文件下载
	fontPath, err := m.loadFont(drawConf.FontFileName)
	if err != nil {
		log.Errorf("CreateChannelImage fail to loadFont. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
	}

	// 加载字体,设置字体大小
	err = c.LoadFontFace(fontPath, 45)
	if err != nil {
		log.Errorf("CreateChannelImage fail to LoadFontFace. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
	}

	diplayId := m.getDisplayId(ctx, cid)
	desc := fmt.Sprintf("我们在语音房 ID:%d 甜蜜牵手", diplayId)
	centerDescX := param.Desc.X + param.Desc.Width/2
	centerDescY := param.Desc.Y + param.Desc.Height/2
	c.SetColor(image.Black)
	c.DrawStringAnchored(desc, float64(centerDescX+1), float64(centerDescY+1), 0.5, 0.5) // 居中展示

	c.SetColor(image.White)
	c.DrawStringAnchored(desc, float64(centerDescX), float64(centerDescY), 0.5, 0.5) // 居中展示

	// 加载字体,重新设置字体大小
	c.LoadFontFace(fontPath, 30)

	nameA := truncateText(string(userA.GetNickname()), 8)
	nameB := truncateText(string(userB.GetNickname()), 8)

	centerAX := param.NameA.X + param.NameA.Width/2
	centerAY := param.NameA.Y + param.NameA.Height/2
	centerBX := param.NameB.X + param.NameB.Width/2
	centerBY := param.NameB.Y + param.NameB.Height/2
	c.SetColor(image.Black)
	c.DrawStringAnchored(nameA, float64(centerAX+1), float64(centerAY+1), 0.5, 0.5) // 居中展示
	c.DrawStringAnchored(nameB, float64(centerBX+1), float64(centerBY+1), 0.5, 0.5) // 居中展示

	c.SetColor(image.White)
	c.DrawStringAnchored(nameA, float64(centerAX), float64(centerAY), 0.5, 0.5) // 居中展示
	c.DrawStringAnchored(nameB, float64(centerBX), float64(centerBY), 0.5, 0.5) // 居中展示

	// 加载字体,重新设置字体大小
	c.LoadFontFace(fontPath, 54)

	dateStr := time.Now().Format("2006.01.02")
	c.ClearPath()
	centerDX := param.Date.X + param.Date.Width/2
	centerDY := param.Date.Y + param.Date.Height/2
	c.SetColor(image.Black)
	c.DrawStringAnchored(dateStr, float64(centerDX+1), float64(centerDY+1), 0.5, 0.5) // 居中展示

	c.SetColor(image.White)
	c.DrawStringAnchored(dateStr, float64(centerDX), float64(centerDY), 0.5, 0.5) // 居中展示

	// 加粗文本,增加描边
	c.SetLineWidth(1)
	c.Stroke()

	imageUrl, err = m.uploadImage(ctx, cid, uidA, uidB, 75, c.Image())
	if err != nil {
		log.Errorf("CreateChannelImage fail to uploadImage. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
		return imageUrl, err
	}

	log.Infof("CreateChannelImage uid:%v, cid:%d, sceneLevel:%d, levelScore:%d, imageUrl:%s", []uint32{uidA, uidB}, cid, sceneCfg.Level, sceneCfg.TBean, imageUrl)
	return imageUrl, nil
}

func (m *Mgr) CreateImImage(ctx context.Context, cid uint32, userA, userB *app.UserProfile, hatA, hatB *pb.DatingGameHatCfg,
	sceneCfg *conf.SceneInfo) (imageUrl string, err error) {
	uidA, uidB := userA.GetUid(), userB.GetUid()

	defer func() {
		if err := recover(); err != nil {
			log.Errorf("CreateImImage panic. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
		}
	}()

	if sceneCfg == nil {
		log.Errorf("CreateImImage fail. sceneCfg == nil. uid:%v, cid:%d", []uint32{uidA, uidB}, cid)
		return
	}

	drawConf := m.bc.GetDrawConfList()
	if drawConf == nil {
		log.Errorf("CreateImImage fail. drawConf == nil. uid:%v, cid:%d", []uint32{uidA, uidB}, cid)
		return
	}

	param := sceneCfg.ImBackgroundParam
	// 加载头像
	uidAHead, err := m.loadUserHeadImage(ctx, uidA, param.Radius, userA.GetAccount(), drawConf.DefaultHeadPic)
	if err != nil {
		log.Errorf("CreateImImage fail to loadUserHeadImage. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
		return "", err
	}

	uidBHead, err := m.loadUserHeadImage(ctx, uidB, param.Radius, userB.GetAccount(), drawConf.DefaultHeadPic)
	if err != nil {
		log.Errorf("CreateImImage fail to loadUserHeadImage. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
		return "", err
	}

	bgPic := sceneCfg.ImBackgroundPicName
	// 加载背景
	bgImage, err := m.loadImg(bgPic)
	if err != nil {
		log.Errorf("CreateImImage fail to loadImg. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
		return "", err
	}

	c := gg.NewContextForImage(bgImage)
	// 将头像画在背景上
	c.DrawImage(uidAHead, int(param.UserA.X), int(param.UserA.Y))
	c.DrawImage(uidBHead, int(param.UserB.X), int(param.UserB.Y))

	// 画帽子
	if hatA != nil {
		err = m.drawHat(uidA, c, sceneCfg, hatA, true, true)
		if err != nil {
			log.Errorf("CreateImImage fail to drawHat. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
			return "", err
		}
	}

	if hatB != nil {
		err = m.drawHat(uidB, c, sceneCfg, hatB, true, false)
		if err != nil {
			log.Errorf("CreateImImage fail to drawHat. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
			return "", err
		}
	}

	// 字体文件下载
	fontPath, err := m.loadFont(drawConf.FontFileName)
	if err != nil {
		log.Errorf("CreateImImage fail to loadFont. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
	}

	// 加载字体,重新设置字体大小
	err = c.LoadFontFace(fontPath, 27)
	if err != nil {
		log.Errorf("CreateImImage fail to LoadFontFace. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
	}

	nameA := truncateText(string(userA.GetNickname()), 5)
	nameB := truncateText(string(userB.GetNickname()), 5)

	centerAX := param.NameA.X + param.NameA.Width/2
	centerAY := param.NameA.Y + param.NameA.Height/2
	centerBX := param.NameB.X + param.NameB.Width/2
	centerBY := param.NameB.Y + param.NameB.Height/2
	c.SetColor(image.Black)
	c.DrawStringAnchored(nameA, float64(centerAX+1), float64(centerAY+1), 0.5, 0.5) // 居中展示
	c.DrawStringAnchored(nameB, float64(centerBX+1), float64(centerBY+1), 0.5, 0.5) // 居中展示

	c.SetColor(image.White)
	c.DrawStringAnchored(nameA, float64(centerAX), float64(centerAY), 0.5, 0.5) // 居中展示
	c.DrawStringAnchored(nameB, float64(centerBX), float64(centerBY), 0.5, 0.5) // 居中展示

	imageUrl, err = m.uploadImage(ctx, cid, uidA, uidB, 75, c.Image())
	if err != nil {
		log.Errorf("CreateImImage fail to uploadImage. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
		return imageUrl, err
	}

	log.Infof("CreateImImage uid:%v, cid:%d, sceneLevel:%d, levelScore:%d, imageUrl:%s", []uint32{uidA, uidB}, cid, sceneCfg.Level, sceneCfg.TBean, imageUrl)
	return imageUrl, nil
}

func (m *Mgr) uploadImage(ctx context.Context, cid, uidA, uidB, quality uint32, image image.Image) (string, error) {
	buff := new(bytes.Buffer)
	err := jpeg.Encode(buff, image, &jpeg.Options{Quality: int(quality)})
	if err != nil {
		log.Errorf("uploadImage fail to jpeg.Encode. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
		return "", err
	}

	f, err := os.Create("test_re.jpeg")
	if err != nil {
		log.Errorf("uploadImage fail to Create. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
		return "", err
	}
	defer f.Close() //nolint

	_, err = f.Write(buff.Bytes())
	if err != nil {
		log.Errorf("uploadImage fail to Create. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
		return "", err
	}

	retKey, _, serr := m.obsGatewayCli.Upload(ctx, "tt", "dateroom-couple", buff.Bytes(),
		obsgateway.WithContentType("image/jpeg"))
	if serr != nil {
		log.Errorf("uploadImage fail to PutObject. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
		return "", serr
	}

	urlPrefix := m.bc.GetUploadUrlPrefix()

	return urlPrefix + retKey, nil
}

func (m *Mgr) drawHat(uid uint32, imgCtx *gg.Context, sceneCfg *conf.SceneInfo, hatInfoPb *pb.DatingGameHatCfg, isImScene, isFirstPos bool) error {
	if sceneCfg == nil || hatInfoPb == nil {
		return nil
	}

	hatPic := ""
	hatConf := m.bc.GetHatConfList()
	for _, info := range hatConf.HatList {
		if info.IsMale == hatInfoPb.GetIsMale() && info.Level == hatInfoPb.GetLevel() {
			hatPic = info.PicName
		}
	}

	if hatPic == "" {
		return nil
	}

	bgParam := sceneCfg.BackgroundParam
	if isImScene {
		bgParam = sceneCfg.ImBackgroundParam
	}

	hatImg, err := m.loadImg(hatPic)
	if err != nil {
		log.Errorf("drawHat fail to loadImg. uid:%d, err:%v", uid, err)
		return err
	}

	pos := bgParam.HatA
	if !isFirstPos {
		pos = bgParam.HatB
	}

	c := gg.NewContext(int(pos.Width), int(pos.Height))
	xScale := float64(pos.Width) / float64(hatImg.Bounds().Size().X)
	yScale := float64(pos.Height) / float64(hatImg.Bounds().Size().Y)
	c.Scale(xScale, yScale)
	c.DrawImage(hatImg, 0, 0)
	imgCtx.DrawImage(c.Image(), int(pos.X), int(pos.Y))
	return nil
}

func (m *Mgr) getDisplayId(ctx context.Context, channelId uint32) uint32 {
	channelResp, err := m.channelCli.GetChannelSimpleInfo(ctx, 0, channelId)
	if err != nil {
		log.Errorf("getDisplayId fail to GetChannelSimpleInfo. channelId:%v, err:%v", channelId, err)
		return channelId
	}

	displayId := channelResp.GetDisplayId()

	if channelResp.GetChannelType() == uint32(channelPB.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		guildResp, err := m.guildCli.GetGuild(ctx, channelResp.GetBindId())
		if err != nil {
			log.Errorf("getDisplayId fail to GetGuild. channelId:%v, err:%v", channelId, err)
			return 0
		}

		displayId = channelResp.GetBindId()
		if guildResp.GetShortId() > 0 {
			displayId = guildResp.GetShortId()
		}
	}

	return displayId
}

func (m *Mgr) loadUserHeadImage(ctx context.Context, uid, radius uint32, username, defaultHeadPic string) (headImage image.Image, err error) {
	byteFace, err := m.headImageCli.GetHeadImageSmallFace(ctx, uid, &hiPB.AccountReq{
		Account: username,
	})
	if err != nil {
		log.Errorf("loadUserHeadImage GetHeadImageSmallFace fail. uid:%d, err:%v", uid, err)
		return
	}

	if len(byteFace) == 0 {
		headImage, err = m.loadImg(defaultHeadPic)
		if err != nil {
			log.Errorf("loadUserHeadImage loadImg fail. uid:%d, err:%v", uid, err)
			return
		}

	} else {
		headImage, _, err = image.Decode(bytes.NewReader(byteFace))
		if err != nil {
			log.Errorf("loadUserHeadImage image.Decode fail. uid:%d, err:%v", uid, err)
			headImage, err = gif.Decode(bytes.NewReader(byteFace)) // 尝试解析为gif
			if err != nil {
				log.Errorf("loadUserHeadImage gif.Decode fail.  uid:%d, err:%v", uid, err)
				headImage, _ = m.loadImg(defaultHeadPic) // 默认头像兜底
			}
		}
	}

	side := int(radius * 2) // 画圆半径为边长的1/2
	c := gg.NewContext(side, side)
	c.SetColor(image.White)
	// 画圆
	c.DrawCircle(float64(radius), float64(radius), float64(radius))
	// 裁剪
	c.ClipPreserve()
	xScale := float64(side) / float64(headImage.Bounds().Size().X)
	yScale := float64(side) / float64(headImage.Bounds().Size().Y)
	c.Scale(xScale, yScale)
	c.DrawImage(headImage, 0, 0)

	return c.Image(), nil
}

func (m *Mgr) loadFont(fileName string) (string, error) {
	if fileName == "" {
		return "", errors.New("fileName is empty")
	}
	filePrefix, urlPrefix := m.bc.GetFileAndUrlPrefix()

	filePath := filePrefix + fileName
	file, err := os.Open(filePath)
	if err != nil {
		err = downLoadFont(filePath, urlPrefix)
	}

	if err != nil {
		log.Errorf("loadFont Open file fail. %v", err)
		return filePath, err
	}

	defer file.Close() //nolint

	return filePath, nil
}

func (m *Mgr) loadImg(fileName string) (image.Image, error) {
	if fileName == "" {
		return nil, errors.New("fileName is empty")
	}
	filePrefix, urlPrefix := m.bc.GetFileAndUrlPrefix()

	filePath := filePrefix + fileName
	file, err := os.Open(filePath)
	if err != nil {
		file, err = downLoadImage(filePath, urlPrefix)
	}
	if err != nil {
		log.Errorf("loadImg Open file fail. %v", err)
		return nil, err
	}

	defer file.Close() //nolint

	img, name, err := image.Decode(file)
	if err != nil {
		log.Errorf("image.Decode fail. %v", err)
		return nil, err
	}

	log.Debugf("loadImg type name:%s", name)
	return img, nil
}

func downLoadFont(filePath, urlPrefix string) error {
	// 先创建目录
	dir := path.Dir(filePath)
	_ = os.MkdirAll(dir, 0777)

	fileName := path.Base(filePath)
	url := fmt.Sprintf("%s/%s", urlPrefix, fileName)

	resp, err := http.Get(url) //nolint
	if err != nil {
		log.Errorf("downLoadFont http.Get fail. %s %v", url, err)
		return err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Errorf("downLoadFont http.Get fail. %s %v", url, resp.Status)
		return err
	}

	date, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("downLoadFont ReadAll fail. %s %v", url, err)
		return err
	}

	outFile, err := os.Create(filePath)
	defer outFile.Close() //nolint
	if err != nil {
		log.Errorf("downLoadFont os.Create fail. %v", err)
		return err
	}

	b := bufio.NewWriter(outFile)
	_, err = b.Write(date)
	if err != nil {
		log.Errorf("downLoadFont Write fail. %v", err)
		return err
	}

	err = b.Flush()
	if err != nil {
		log.Errorf("downLoadFont Flush fail. %v", err)
		return err
	}

	return nil
}

func downLoadImage(filePath, urlPrefix string) (*os.File, error) {
	fileName := path.Base(filePath)
	url := urlPrefix + fileName

	resp, err := http.Get(url) //nolint
	if err != nil {
		log.Errorf("downLoadImage http.Get fail. %s %v", url, err)
		return nil, err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Errorf("downLoadImage http.Get fail. %s %v", url, resp.Status)
		return nil, err
	}

	img, name, err := image.Decode(resp.Body)
	if err != nil {
		log.Errorf("downLoadImage image.Decode fail. %s %v", url, err)
		return nil, err
	}

	log.Debugf("downLoadImage name:%s", name)

	err = savePngImage(img, filePath)
	if err != nil {
		log.Errorf("downLoadImage saveImage fail. %s %v", url, err)
		return nil, err
	}

	return os.Open(filePath)
}

func savePngImage(img image.Image, filePath string) error {
	// 先创建目录
	dir := path.Dir(filePath)
	_ = os.MkdirAll(dir, 0777)

	outFile, err := os.Create(filePath)
	defer outFile.Close() //nolint
	if err != nil {
		log.Errorf("savePngImage os.Create fail. %v", err)
		return err
	}

	b := bufio.NewWriter(outFile)
	err = png.Encode(b, img)
	if err != nil {
		log.Errorf("savePngImage png.Encode. %v", err)
		return err
	}
	err = b.Flush()
	if err != nil {
		log.Errorf("savePngImage Flush fail. %v", err)
		return err
	}

	return nil
}

func truncateText(originalText string, maxWordCnt uint32) string {
	out := ""
	i := uint32(0)
	for _, r := range originalText {
		i++
		if i > maxWordCnt {
			out += "..."
			break
		}
		out += string(r)
	}
	return out
}
