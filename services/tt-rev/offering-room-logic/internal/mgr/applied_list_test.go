package mgr

import (
	"context"
	"github.com/golang/mock/gomock"
	pb "golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/offer_room"
	offer_room_svr "golang.52tt.com/protocol/services/offer-room"
	"golang.52tt.com/services/tt-rev/offering-room-logic/internal/mgr/assembler"
	"golang.52tt.com/services/tt-rev/offering-room-logic/internal/mocks"
	"reflect"
	"testing"
)

type applyListMgrHelperForTest struct {
	*applyListMgr
}

func newApplyListMgrHelperForTest(t *testing.T) *applyListMgrHelperForTest {
	controller := gomock.NewController(t)
	return &applyListMgrHelperForTest{
		applyListMgr: &applyListMgr{
			baseMgr: &baseMgr{
				pushService: NewMockPushService(controller),
				converter:   assembler.NewConverter(),
			},
			userProfileCli:  mocks.NewMockIClient(controller),
			offeringRoomCli: mocks.NewMockOfferingRoomClient(controller),
		},
	}
}

func (receiver *applyListMgrHelperForTest) getOfferingRoomCli() *mocks.MockOfferingRoomClient {
	return receiver.offeringRoomCli.(*mocks.MockOfferingRoomClient)
}

func (receiver *applyListMgrHelperForTest) getUserProfileCli() *mocks.MockIClient {
	return receiver.userProfileCli.(*mocks.MockIClient)
}

func Test_applyListMgr_UserApply(t *testing.T) {
	type args struct {
		ctx       context.Context
		channelId uint32
		uid       uint32
	}
	tests := []struct {
		name     string
		args     args
		want     *offer_room.OfferRoomUserApplyResponse
		wantErr  bool
		initFunc func(s *applyListMgrHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				ctx:       context.Background(),
				channelId: 123,
				uid:       123,
			},
			want:    &offer_room.OfferRoomUserApplyResponse{},
			wantErr: false,
			initFunc: func(s *applyListMgrHelperForTest) {
				s.getOfferingRoomCli().EXPECT().AddAppliedUser(gomock.Any(), gomock.Any()).Return(nil, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := newApplyListMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(a)
			}
			got, err := a.UserApply(tt.args.ctx, tt.args.channelId, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserApply() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UserApply() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_applyListMgr_UserCancelApply(t *testing.T) {
	type args struct {
		ctx       context.Context
		channelId uint32
		uid       uint32
	}
	tests := []struct {
		name     string
		args     args
		want     *offer_room.OfferRoomUserCancelApplyResponse
		wantErr  bool
		initFunc func(s *applyListMgrHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				ctx:       context.Background(),
				channelId: 123,
				uid:       123,
			},
			want:    &offer_room.OfferRoomUserCancelApplyResponse{},
			wantErr: false,
			initFunc: func(s *applyListMgrHelperForTest) {
				s.getOfferingRoomCli().EXPECT().DelAppliedUser(gomock.Any(), gomock.Any()).Return(nil, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := newApplyListMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(a)
			}
			got, err := a.UserCancelApply(tt.args.ctx, tt.args.channelId, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserCancelApply() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UserCancelApply() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_applyListMgr_convertList(t *testing.T) {
	type args struct {
		ctx  context.Context
		list *offer_room_svr.ApplyList
	}
	tests := []struct {
		name     string
		args     args
		want     *offer_room.OfferRoomApplyList
		wantErr  bool
		initFunc func(s *applyListMgrHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				ctx: context.Background(),
				list: &offer_room_svr.ApplyList{
					List: []*offer_room_svr.ApplyList_UserInfo{
						{
							UserId: 123,
						},
					},
					Version: 123,
				},
			},
			want: &offer_room.OfferRoomApplyList{
				List: []*pb.UserProfile{
					{
						Uid:          123,
						Account:      "123",
						Nickname:     "123",
						AccountAlias: "123",
						Sex:          0,
						Privilege:    nil,
					},
				},
				Version: 123,
			},
			wantErr: false,
			initFunc: func(s *applyListMgrHelperForTest) {
				s.getUserProfileCli().EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*pb.UserProfile{
					123: {
						Uid:          123,
						Account:      "123",
						Nickname:     "123",
						AccountAlias: "123",
						Sex:          0,
						Privilege:    nil,
					},
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := newApplyListMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(a)
			}
			got, err := a.convertList(tt.args.ctx, tt.args.list)
			if (err != nil) != tt.wantErr {
				t.Errorf("convertList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertList() got = %v, want %v", got, tt.want)
			}
		})
	}
}
