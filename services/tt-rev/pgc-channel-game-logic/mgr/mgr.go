package mgr

import (
	"context"
	"errors"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/pgc_channel_game_logic"
	"golang.52tt.com/protocol/common/status"
	entertainmentRecommendBack "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	pgc_adventure "golang.52tt.com/protocol/services/pgc-adventure"
	pgc_channel_game "golang.52tt.com/protocol/services/pgc-channel-game"
	pgcchannelpkpb "golang.52tt.com/protocol/services/pgc-channel-pk"
	pgcdigitalbomb "golang.52tt.com/protocol/services/pgc-digital-bomb"
	pgc_throw_bomb "golang.52tt.com/protocol/services/pgc-throw-bomb"
	ttrevoperation "golang.52tt.com/protocol/services/tt-rev-operation"
	"golang.52tt.com/services/tt-rev/pgc-channel-game-logic/conf"
	"golang.52tt.com/services/tt-rev/pgc-channel-game-logic/rpc"
	"strings"
	"time"
)

const (
	game                 = "{游戏名称}"
	channelPkLimitFormat = "跨房PK过程中不可开启" + game + "玩法"
	maskedPKLimitFormat  = "蒙面PK过程中不可开启" + game + "玩法"

	throwBombGame   = "甩雷"
	digitalBombGame = "数字炸弹"
	adventureGame   = "大冒险"
)

var (
	ErrNotFound = errors.New("not found")
)

type IMgr interface {
	SetGamePhase(ctx context.Context, req *pgc_channel_game_logic.SetGamePhaseReq, uid uint32) error
	GetChannelGameInfo(ctx context.Context, channelId uint32) (*pgc_channel_game_logic.GetChannelGameInfoResp, error)
}

type Mgr struct {
	cli *rpc.Client
	bc  conf.IBusinessConfManager
}

func NewMgr(cli *rpc.Client, bc conf.IBusinessConfManager) IMgr {
	return &Mgr{
		cli: cli,
		bc:  bc,
	}
}

func (mgr *Mgr) SetGamePhase(ctx context.Context, req *pgc_channel_game_logic.SetGamePhaseReq, uid uint32) error {
	if req.GetTargetPhase() != uint32(pgc_channel_game_logic.SetGamePhaseReq_GAME_PHASE_FIN) {
		if err := mgr.checkCanSet(ctx, req.GetChannelId(), req.GetGameId()); err != nil { // 玩法互斥判断, 关闭不用做
			return err
		}
	}

	// 如果是结束，则删除当前的玩法
	if req.GetTargetPhase() == uint32(pgc_channel_game_logic.SetGamePhaseReq_GAME_PHASE_FIN) {
		// 记录房间当前的玩法
		_, err := mgr.cli.PgcChannelGameCli.DelChannelCurrentGameInfo(ctx, &pgc_channel_game.DelChannelCurrentGameInfoReq{
			ChannelId: req.GetChannelId(),
			GameId:    req.GetGameId(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "DelChannelCurrentGameInfo, channelId; %d,  err: %v", req.GetChannelId(), err)
		}
	} else {
		// 如果是开始，记录房间当前的玩法
		_, err := mgr.cli.PgcChannelGameCli.SetChannelCurrentGameInfo(ctx, &pgc_channel_game.SetChannelCurrentGameInfoReq{
			ChannelId: req.GetChannelId(),
			GameId:    req.GetGameId(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "SetChannelCurrentGameInfo, channelId; %d,  err: %v", req.GetChannelId(), err)
			return err
		}
	}

	var err error
	switch pgc_channel_game_logic.PgcChannelGameId(req.GetGameId()) {
	case pgc_channel_game_logic.PgcChannelGameId_GAME_THROW_BOMB:
		err = mgr.setGameBombPhase(ctx, req.GetChannelId(), req.GetGameId(), req.GetTargetPhase(), req.GetChose())
	case pgc_channel_game_logic.PgcChannelGameId_GAME_DIGITAL_BOMB:
		err = mgr.setDigitalBombPhase(ctx, req.GetChannelId(), req.GetTargetPhase(), req.GetChose())
	case pgc_channel_game_logic.PgcChannelGameId_GAME_ADVENTURE:
		err = mgr.setAdventurePhase(ctx, uid, req.GetChannelId(), req.GetTargetPhase(), req.GetChose())
	default:
		return fmt.Errorf("invalid gameId")
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "SetGamePhase, channelId; %d,  err: %v", req.GetChannelId(), err)
		return err
	}

	if req.GetTargetPhase() == uint32(pgc_channel_game_logic.SetGamePhaseReq_GAME_PHASE_FIN) {
		// 小游戏结束推送一下蒙面PK入口
		err = mgr.cli.MaskedPKCli.PushGameBeginConf(ctx, req.GetChannelId())
		if err != nil {
			log.ErrorWithCtx(ctx, "EndGameBomb.PushGameBeginConf, channelId; %d,  err: %v", req.GetChannelId(), err)
		}
		log.InfoWithCtx(ctx, "handleGameBombEnd.PushGameBeginConf")
	}

	return nil
}

func (mgr *Mgr) checkCanSet(ctx context.Context, channelId, gameId uint32) error {
	if err := mgr.otherBizCompatible(ctx, channelId, gameId); err != nil {
		log.ErrorWithCtx(ctx, "checkCanSet.otherBizCompatible, channelId: %d,  err; %v", channelId, err)
		return err
	}

	gameInfo, err := mgr.GetChannelGameInfo(ctx, channelId) // 后续做缓存 定时更新
	if err != nil {
		log.ErrorWithCtx(ctx, "checkCanSet.GetChannelGameInfo, channelId: %d,  err; %v", channelId, err)
		return err
	}

	// 有游戏在进行 且不是当前游戏
	if gameInfo.GetPhase() != 0 && gameId != gameInfo.CurrentGameId {
		return protocol.NewServerError(status.ErrSys, "请结束当前玩法后再开启")
	}

	// 判断房间是否有该游戏权限, 如果游戏已开始,则本局不用校验
	if gameInfo.GetPhase() == 0 {
		gameIds, err := mgr.getGameSummaryIds(ctx, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetGamePhase.checkGamePermission, channelId: %d, gameId: %d, err: %v", channelId, gameIds, err)
		}
		hasPermission := false
		for _, gid := range gameIds {
			if gid == gameId {
				hasPermission = true
				break
			}
		}
		if !hasPermission {
			log.ErrorWithCtx(ctx, "SetGamePhase, channelId: %d, gameId: %d, err: no game permission")
			return newPgcChannelGameNoStartGamePermissionError(gameId)
		}
	}

	return nil
}

// 玩法兼容
func (mgr *Mgr) otherBizCompatible(ctx context.Context, channelId, gameId uint32) error {
	var channelPkLimit, maskedPKLimit string
	switch pgc_channel_game_logic.PgcChannelGameId(gameId) {
	case pgc_channel_game_logic.PgcChannelGameId_GAME_THROW_BOMB:
		channelPkLimit = strings.Replace(channelPkLimitFormat, game, throwBombGame, -1)
		maskedPKLimit = strings.Replace(maskedPKLimitFormat, game, throwBombGame, -1)
	case pgc_channel_game_logic.PgcChannelGameId_GAME_DIGITAL_BOMB:
		channelPkLimit = strings.Replace(channelPkLimitFormat, game, digitalBombGame, -1)
		maskedPKLimit = strings.Replace(maskedPKLimitFormat, game, digitalBombGame, -1)
	case pgc_channel_game_logic.PgcChannelGameId_GAME_ADVENTURE:
		channelPkLimit = strings.Replace(channelPkLimitFormat, game, adventureGame, -1)
		maskedPKLimit = strings.Replace(maskedPKLimitFormat, game, adventureGame, -1)
	default:
		channelPkLimit = strings.Replace(channelPkLimitFormat, game, "", -1)
		maskedPKLimit = strings.Replace(maskedPKLimitFormat, game, "", -1)
	}

	pkResp, err := mgr.cli.PGCChannelPKCli.GetPgcChannelPKId(ctx, &pgcchannelpkpb.GetPgcChannelPKIdReq{
		ChannelId: channelId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "otherBizCompatible.GetPgcChannelPKId, channelId: %d, gameId: %d, err: %v", channelId, gameId, err)
	}
	if pkResp.GetPkId() > 0 {
		log.ErrorWithCtx(ctx, "otherBizCompatible, channelId: %d, gameId: %d, err: is pking", channelId, gameId)
		return protocol.NewServerError(status.ErrPgcChannelGameGameplayIncompatibility, channelPkLimit)
	}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "otherBizCompatible, channelId: %d, gameId: %d, err: get serviceInfo fail", channelId, gameId)
		return nil
	}
	maskedPkConf, err := mgr.cli.MaskedPKCli.GetChannelMaskedPKCurrConfWithUser(ctx, serviceInfo.UserID, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "otherBizCompatible.GetChannelMaskedPKCurrConf, channelId: %d, gameId: %d, err: %v", channelId, gameId, err)
	}
	nowTs := uint32(time.Now().Unix())
	if !maskedPkConf.GetIsGiveUp() && nowTs >= maskedPkConf.GetConf().GetBeginTs() && nowTs <= maskedPkConf.GetConf().GetEndTs() {
		log.ErrorWithCtx(ctx, "otherBizCompatible, channelId: %d, gameId: %d, err: is maskedPking")
		return protocol.NewServerError(status.ErrPgcChannelGameGameplayIncompatibility, maskedPKLimit)
	}

	return nil
}

// GetChannelGameInfo 查看各游戏信息
func (mgr *Mgr) GetChannelGameInfo(ctx context.Context, channelId uint32) (*pgc_channel_game_logic.GetChannelGameInfoResp, error) {
	gameInfoResp, err := mgr.cli.PgcChannelGameCli.GetChannelCurrentGameInfo(ctx, &pgc_channel_game.GetChannelCurrentGameInfoReq{
		ChannelId: channelId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelGameInfo, channelId: %d, err: %v", channelId, err)
		return &pgc_channel_game_logic.GetChannelGameInfoResp{}, err
	}
	out := &pgc_channel_game_logic.GetChannelGameInfoResp{
		ChannelId:     gameInfoResp.GetChannelId(),
		CurrentGameId: gameInfoResp.GetCurrentGameId(),
	}

	switch pgc_channel_game_logic.PgcChannelGameId(gameInfoResp.GetCurrentGameId()) {
	case pgc_channel_game_logic.PgcChannelGameId_GAME_THROW_BOMB:
		if err := mgr.fillBombGameInfo(ctx, out); err != nil {
			log.ErrorWithCtx(ctx, "GetChannelGameInfo.fillBombGameInfo, channelId: %d, err: %v", channelId, err)
			return out, err
		}
		// 填充惩罚信息
		if err := mgr.fillThrowBombPunishInfo(ctx, out); err != nil {
			log.ErrorWithCtx(ctx, "GetChannelGameInfo.fillThrowBombPunishInfo, channelId: %d, err: %v", channelId, err)
		}
	case pgc_channel_game_logic.PgcChannelGameId_GAME_DIGITAL_BOMB:
		if err := mgr.fillDigitalBombGameInfo(ctx, out); err != nil {
			log.ErrorWithCtx(ctx, "GetChannelGameInfo.fillDigitalBombGameInfo, channelId: %d, err: %v", channelId, err)
			return out, err
		}
	case pgc_channel_game_logic.PgcChannelGameId_GAME_ADVENTURE:
		if err := mgr.fillAdventureGameInfo(ctx, out); err != nil {
			log.ErrorWithCtx(ctx, "GetChannelGameInfo.fillAdventureGameInfo, channelId: %d, err: %v", channelId, err)
			return out, err
		}
	default:
		log.InfoWithCtx(ctx, "GetChannelGameInfo, channelId: %d, err: no game", channelId)
		// 填充惩罚信息
		if err := mgr.fillThrowBombPunishInfo(ctx, out); err != nil {
			log.ErrorWithCtx(ctx, "GetChannelGameInfo.fillThrowBombPunishInfo, channelId: %d, err: %v", channelId, err)
		}
		return out, nil
	}
	return out, nil
}

func digitalBombPhaseMap(phase uint32) uint32 {
	switch pgcdigitalbomb.DigitalBombPhase(phase) {
	case pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_ENROLL,
		pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_SELECTING,
		pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_SELECTED:
		// 第一版甩雷时客户端使用了甩雷的phase的枚举去做游戏面板的状态判断, 这里要把其他有戏的状态映射到旧的枚举
		return uint32(pgc_channel_game_logic.SetGamePhaseReq_GAME_PHASE_START)
	case pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_EXPLODE,
		pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_MANUAL_END,
		pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_SYS_END,
		pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_DIRECT_END:
		return uint32(pgc_channel_game_logic.SetGamePhaseReq_GAME_PHASE_FIN)
	default:
		return uint32(pgc_channel_game_logic.SetGamePhaseReq_GAME_PHASE_NOT_START)
	}
}

func adventurePhaseMap(phase uint32) uint32 {
	switch pgc_channel_game_logic.AdventurePhase(phase) {
	case pgc_channel_game_logic.AdventurePhase_ADVENTURE_PHASE_ENROLL,
		pgc_channel_game_logic.AdventurePhase_ADVENTURE_PHASE_CONTROL_NEXT,
		pgc_channel_game_logic.AdventurePhase_ADVENTURE_PHASE_RANDOM_STEPS:
		// 第一版甩雷时客户端使用了甩雷的phase的枚举去做游戏面板的状态判断, 这里要把其他有戏的状态映射到旧的枚举
		return uint32(pgc_channel_game_logic.SetGamePhaseReq_GAME_PHASE_START)
	case pgc_channel_game_logic.AdventurePhase_ADVENTURE_PHASE_DIRECT_END:
		return uint32(pgc_channel_game_logic.SetGamePhaseReq_GAME_PHASE_FIN)
	default:
		return uint32(pgc_channel_game_logic.SetGamePhaseReq_GAME_PHASE_NOT_START)
	}
}

// 获取房间当前拥有权限的玩法id
func (mgr *Mgr) getGameSummaryIds(ctx context.Context, channelId uint32) ([]uint32, error) {

	// 获取房间当前tag
	tag, err := mgr.cli.EntertainmentRecommendBackCli.GetChannelTag(ctx, 0, &entertainmentRecommendBack.GetChannelTagReq{
		ChannelId: &channelId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getGameSummaryIds.GetChannelTag, channelId: %d, err: %v", channelId, err)
		return []uint32{}, err
	}

	// tt-rev-operation的gameplayId 对应 这边的gameSummaryId
	gameSummaryIds, err := mgr.cli.TTRevOperationCli.GetChannelAllGameplay(ctx, &ttrevoperation.GetChannelAllGameplayReq{
		ChannelId: channelId,
		TagId:     tag.GetTagInfo().GetTagId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getGameSummaryIds.GetChannelAllGameplay, ")
	}
	return gameSummaryIds, err
}

func newPgcChannelGameNoStartGamePermissionError(gameId uint32) protocol.ServerError {
	switch pgc_channel_game_logic.PgcChannelGameId(gameId) {
	case pgc_channel_game_logic.PgcChannelGameId_GAME_THROW_BOMB:
		return protocol.NewServerError(status.ErrPgcChannelGameNoStartGamePermission)
	case pgc_channel_game_logic.PgcChannelGameId_GAME_DIGITAL_BOMB:
		return protocol.NewServerError(status.ErrPgcChannelGameNoStartGamePermission, "您暂无开启数字炸弹玩法的权限～")
	case pgc_channel_game_logic.PgcChannelGameId_GAME_ADVENTURE:
		return protocol.NewServerError(status.ErrPgcChannelGameNoStartGamePermission, "您暂无开启大冒险玩法的权限～")
	default:
		return protocol.NewServerError(status.ErrSys)
	}
}

func (mgr *Mgr) setDigitalBombPhase(ctx context.Context, channelId, phase uint32, choseOperate []*pgc_channel_game_logic.ChoseOperate) error {
	pt := uint32(0)
	if len(choseOperate) == 1 {
		pt = choseOperate[0].ButtonId
	}

	_, err := mgr.cli.PgcDigitalBombCli.SetDigitalBombPhase(ctx, &pgcdigitalbomb.SetDigitalBombPhaseReq{
		ChannelId:       channelId,
		Phase:           phase,
		ParticipateType: pt,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "setDigitalBombPhase, channelId: %d, phase: %d, choseOperate: %+v, err; %d", channelId, phase, choseOperate, err)
		return err
	}
	return nil
}

func (mgr *Mgr) setAdventurePhase(ctx context.Context, uid, channelId, phase uint32, choseOperate []*pgc_channel_game_logic.ChoseOperate) error {
	pt := uint32(0)
	if len(choseOperate) == 1 {
		pt = choseOperate[0].ButtonId
	}

	_, err := mgr.cli.PgcAdventureCli.SetAdventurePhase(ctx, &pgc_adventure.SetAdventurePhaseReq{
		Uid:             uid,
		ChannelId:       channelId,
		Phase:           phase,
		ParticipateType: pt,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "setAdventurePhase, uid:%d, channelId: %d, phase: %d, choseOperate: %+v, err; %d", uid, channelId, phase, choseOperate, err)
		return err
	}
	return nil
}

func (mgr *Mgr) setGameBombPhase(ctx context.Context, channelId, gameId, targetPhase uint32, choseOperate []*pgc_channel_game_logic.ChoseOperate) error {
	_, err := mgr.cli.PgcThrowBombClient.SetPhase(ctx, &pgc_throw_bomb.SetThrowBombPhaseReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "setGameBombPhase, channelId: %d, gameId: %d, targetPhase: %d, choseOperate: %+v, err; %d", channelId, gameId, targetPhase, choseOperate, err)
		return err
	}
	return nil
}

func (mgr *Mgr) fillBombGameInfo(ctx context.Context, resp *pgc_channel_game_logic.GetChannelGameInfoResp) error {

	info, err := mgr.cli.PgcThrowBombClient.GetGameInfo(ctx, &pgc_throw_bomb.GetThrowBombGameInfoReq{
		ChannelId: resp.GetChannelId(),
	})
	if err != nil {
		return fmt.Errorf("获取炸弹信息失败: %w", err)
	}

	// 组装数据
	resp.CurrentGameId = uint32(pgc_channel_game_logic.PgcChannelGameId_GAME_THROW_BOMB)
	resp.Phase = info.GetPhase()
	resp.BombInfo.GameBombType = info.GetGameInfo().GetGameBombType()
	resp.BombInfo.Bomb = &pgc_channel_game_logic.GameBombUserInfo{
		Uid:           info.GetGameInfo().GetBomb().GetUid(),
		EndTime:       info.GetGameInfo().GetBomb().GetEndTime(),
		NeedRescue:    info.GetGameInfo().GetBomb().GetNeedRescue(),
		AutoThrowTime: info.GetGameInfo().GetBomb().GetAutoThrowTime(),
		ServerTime:    info.GetGameInfo().GetBomb().GetServerTime(),
	}

	for _, user := range info.GetGameInfo().GetThrowList() {
		resp.BombInfo.ThrowList = append(resp.BombInfo.ThrowList, &pgc_channel_game_logic.GameThrowInfo{
			Uid:         user.GetUid(),
			GiftVal:     user.GetGiftVal(),
			ThrowStatus: user.GetThrowStatus(),
		})
	}

	return nil
}

func (mgr *Mgr) fillDigitalBombGameInfo(ctx context.Context, resp *pgc_channel_game_logic.GetChannelGameInfoResp) error {
	digitalBombInfo, err := mgr.cli.PgcDigitalBombCli.GetDigitalBombInfo(ctx, &pgcdigitalbomb.GetDigitalBombInfoReq{
		ChannelId: resp.GetChannelId(),
	})
	if err != nil {
		return fmt.Errorf("获取数字炸弹信息失败: %w", err)
	}
	info := tranDigitalBombInfo(digitalBombInfo.GetInfo())

	resp.DigitalBombInfo = info
	resp.Phase = digitalBombPhaseMap(info.GetPhase())
	resp.CurrentGameId = uint32(pgc_channel_game_logic.PgcChannelGameId_GAME_DIGITAL_BOMB)

	return nil
}

func (mgr *Mgr) fillAdventureGameInfo(ctx context.Context, resp *pgc_channel_game_logic.GetChannelGameInfoResp) error {

	adventureInfo, err := mgr.cli.PgcAdventureCli.GetAdventureInfo(ctx, &pgc_adventure.GetAdventureInfoReq{
		ChannelId: resp.GetChannelId(),
	})
	if err != nil {
		return fmt.Errorf("获取大冒险信息失败：%w", err)
	}
	info, err := mgr.transAdventureLogicPbInfo(ctx, adventureInfo.GetInfo())
	if err != nil {
		return fmt.Errorf("转换大冒险信息失败：%w", err)
	}

	resp.GameInfo = &pgc_channel_game_logic.GameInfoSet{
		GameInfo: &pgc_channel_game_logic.GameInfoSet_AdventureInfo{AdventureInfo: info},
	}
	resp.CurrentGameId = uint32(pgc_channel_game_logic.PgcChannelGameId_GAME_ADVENTURE)
	resp.Phase = adventurePhaseMap(adventureInfo.GetInfo().GetPhase())

	return nil
}

// fillThrowBombPunishInfo 填充甩雷惩罚信息
func (mgr *Mgr) fillThrowBombPunishInfo(ctx context.Context, resp *pgc_channel_game_logic.GetChannelGameInfoResp) error {

	if resp.GetBombInfo() == nil {
		resp.BombInfo = &pgc_channel_game_logic.GameThrowBombInfo{
			Punish: make([]*pgc_channel_game_logic.GameBombPunishInfo, 0),
		}
	}

	info, err := mgr.cli.PgcThrowBombClient.GetPunishInfo(ctx, &pgc_throw_bomb.GetGameBombPunishInfoReq{
		ChannelId: resp.GetChannelId(),
	})
	if err != nil {
		return fmt.Errorf("获取甩雷惩罚信息失败: %w", err)
	}

	for _, punish := range info.GetPunishInfoList() {
		resp.BombInfo.Punish = append(resp.BombInfo.Punish, &pgc_channel_game_logic.GameBombPunishInfo{
			UserProfile:    mgr.transUserProfile(punish.GetUserProfile()),
			EndTime:        punish.GetEndTime(),
			RescueGiftId:   punish.GetRescueGiftId(),
			RescueGiftName: punish.GetRescueGiftName(),
			IconUrl:        punish.GetIconUrl(),
			IconMd5:        punish.GetIconMd5(),
			ServerTime:     punish.GetServerTime(),
		})
	}

	return nil
}
