package mgr

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	channelpb "golang.52tt.com/protocol/services/channelsvr"
	guildpb "golang.52tt.com/protocol/services/guildsvr"
	pb "golang.52tt.com/protocol/services/tt-rev-operation"
)

func TestMgr_BatchGetPerfectMatchPerm(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testMgr := newMockTestMgr(ctrl)
	type args struct {
		ctx        context.Context
		viewIdList []string
	}
	tests := []struct {
		name    string
		args    args
		want    []*pb.DoomedMatchChannelPerm
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx:        context.Background(),
				viewIdList: []string{"2181945", "2193307", "110001"},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			channelId := uint32(2255334)
			channelType := uint32(4)
			mockChannelCli.EXPECT().BatchGetChannelSimpleInfoByViewId(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*channelpb.ChannelSimpleInfo{
				2255334: {ChannelViewId: &tt.args.viewIdList[0], ChannelType: &channelType, ChannelId: &channelId},
			}, nil)

			mockStore.EXPECT().BatchGetPerfectMatchPerm(gomock.Any(), gomock.Any()).Return(map[uint32]bool{channelId: true}, nil)
			mockGuildCli.EXPECT().GetGuildBat(gomock.Any(), gomock.Any(), gomock.Any()).Return(&guildpb.GetGuildBatResp{}, nil)

			_, err := testMgr.BatchGetPerfectMatchPerm(tt.args.ctx, tt.args.viewIdList)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetPerfectMatchPerm() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestMgr_BatchOperatePerfectMatchPerm(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testMgr := newMockTestMgr(ctrl)
	type args struct {
		ctx        context.Context
		viewIdList []string
		op         uint32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := testMgr.BatchOperatePerfectMatchPerm(tt.args.ctx, tt.args.viewIdList, tt.args.op); (err != nil) != tt.wantErr {
				t.Errorf("BatchOperatePerfectMatchPerm() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
