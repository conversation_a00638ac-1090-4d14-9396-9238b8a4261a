package server

import (
	"context"
	"reflect"
	"testing"

	"golang.52tt.com/services/tt-rev/pgc-channel-pk-logic/internal/conf"
	"golang.52tt.com/services/tt-rev/pgc-channel-pk-logic/internal/mocks"

	"github.com/golang/mock/gomock"
	channelmemberviprank "golang.52tt.com/clients/channelmemberVipRank"
	mockmemberRank "golang.52tt.com/clients/mocks/channelmemberVipRank"
	mocksnobility "golang.52tt.com/clients/mocks/nobility"
	mocknumeri "golang.52tt.com/clients/mocks/numeric"
	mockspgcchannelpk "golang.52tt.com/clients/mocks/pgc-channel-pk"
	"golang.52tt.com/clients/nobility"
	"golang.52tt.com/clients/numeric"
	pgcchannelpk "golang.52tt.com/clients/pgc-channel-pk"
	superPlayerCli "golang.52tt.com/clients/super-player-svr"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	pgcchannelpklogicpb "golang.52tt.com/protocol/app/pgc_channel-pk-logic"
	channelmemberviprank2 "golang.52tt.com/protocol/services/channelmemberVipRank"
	nobilityPB "golang.52tt.com/protocol/services/nobilitysvr"
	pgcchannelpkpb "golang.52tt.com/protocol/services/pgc-channel-pk"
)

var (
	svr              *Server
	bc               *mocks.MockIBusinessConfManager
	pgcChannelPKCli  *mockspgcchannelpk.MockIClient
	nobilityClient   *mocksnobility.MockIClient
	numericClient    *mocknumeri.MockIClient
	memberRankClient *mockmemberRank.MockIClient
)

func initial(t *testing.T) *gomock.Controller {
	ctrl := gomock.NewController(t)
	bc = mocks.NewMockIBusinessConfManager(ctrl)
	pgcChannelPKCli = mockspgcchannelpk.NewMockIClient(ctrl)
	nobilityClient = mocksnobility.NewMockIClient(ctrl)
	numericClient = mocknumeri.NewMockIClient(ctrl)
	memberRankClient = mockmemberRank.NewMockIClient(ctrl)

	svr = &Server{
		bc:              bc,
		pgcChannelPKCli: pgcChannelPKCli,
		nobilityClient:  nobilityClient,
		numericClient:   numericClient,
		memberRank:      memberRankClient,
	}

	return ctrl
}

var (
	uid     = uint32(2465835)
	ctx     = protogrpc.WithServiceInfo(context.TODO(), &protogrpc.ServiceInfo{UserID: uid})
	cid     = uint32(2255334)
	toCid   = uint32(2048205)
	guildId = uint32(153380)
)

func TestPgcChannelPKLogic_AcceptPgcChannelPK(t *testing.T) {
	ctrl := initial(t)
	defer ctrl.Finish()

	type args struct {
		ctx context.Context
		req *pgcchannelpklogicpb.AcceptPgcChannelPKReq
	}
	tests := []struct {
		name    string
		args    args
		want    *pgcchannelpklogicpb.AcceptPgcChannelPKResp
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx: nil,
				req: nil,
			},
			want:    &pgcchannelpklogicpb.AcceptPgcChannelPKResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pgcChannelPKCli.EXPECT().AcceptPgcChannelPK(gomock.Any(), gomock.Any()).Return(nil, nil)
			got, err := svr.AcceptPgcChannelPK(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AcceptPgcChannelPK() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AcceptPgcChannelPK() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPgcChannelPKLogic_GetPgcChannelPKAudienceRank(t *testing.T) {
	ctrl := initial(t)
	defer ctrl.Finish()
	type fields struct {
		bc                *conf.BusinessConfManager
		pgcChannelPKCli   *pgcchannelpk.Client
		nobilityClient    *nobility.Client
		numericClient     *numeric.Client
		memberRank        *channelmemberviprank.Client
		superPlayerClient *superPlayerCli.Client
	}
	type args struct {
		ctx context.Context
		req *pgcchannelpklogicpb.GetPgcChannelPKAudienceRankReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pgcchannelpklogicpb.GetPgcChannelPKAudienceRankResp
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx: ctx,
				req: &pgcchannelpklogicpb.GetPgcChannelPKAudienceRankReq{},
			},
			want:    &pgcchannelpklogicpb.GetPgcChannelPKAudienceRankResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		pgcChannelPKCli.EXPECT().GetPgcChannelPKAudienceRank(gomock.Any(), gomock.Any(), gomock.Any()).Return(&pgcchannelpkpb.GetPgcChannelPKAudienceRankResp{}, nil).AnyTimes()
		nobilityClient.EXPECT().BatchGetNobilityInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*nobilityPB.NobilityInfo{}, nil).AnyTimes()
		memberRankClient.EXPECT().BatGetUserConsumeInfo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*channelmemberviprank2.ChannelMemberVip{}, nil).AnyTimes()
		numericClient.EXPECT().BatchGetPersonalNumeric(gomock.Any(), gomock.Any()).Return(map[uint32]*numeric.UserNumericValue{}, nil).AnyTimes()
		t.Run(tt.name, func(t *testing.T) {
			got, err := svr.GetPgcChannelPKAudienceRank(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPgcChannelPKAudienceRank() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPgcChannelPKAudienceRank() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPgcChannelPKLogic_GetPgcChannelPKEntry(t *testing.T) {
	ctrl := initial(t)
	defer ctrl.Finish()
	type fields struct {
		bc                *conf.BusinessConfManager
		pgcChannelPKCli   *pgcchannelpk.Client
		nobilityClient    *nobility.Client
		numericClient     *numeric.Client
		memberRank        *channelmemberviprank.Client
		superPlayerClient *superPlayerCli.Client
	}
	type args struct {
		ctx context.Context
		req *pgcchannelpklogicpb.GetPgcChannelPKEntryReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pgcchannelpklogicpb.GetPgcChannelPKEntryResp
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx: ctx,
				req: &pgcchannelpklogicpb.GetPgcChannelPKEntryReq{},
			},
			want:    &pgcchannelpklogicpb.GetPgcChannelPKEntryResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pgcChannelPKCli.EXPECT().GetPgcChannelPKEntry(gomock.Any(), gomock.Any()).Return(&pgcchannelpkpb.GetPgcChannelPKEntryResp{}, nil)
			got, err := svr.GetPgcChannelPKEntry(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPgcChannelPKEntry() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPgcChannelPKEntry() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPgcChannelPKLogic_GetPgcChannelPKInfo(t *testing.T) {
	ctrl := initial(t)
	defer ctrl.Finish()
	type fields struct {
		bc                *conf.BusinessConfManager
		pgcChannelPKCli   *pgcchannelpk.Client
		nobilityClient    *nobility.Client
		numericClient     *numeric.Client
		memberRank        *channelmemberviprank.Client
		superPlayerClient *superPlayerCli.Client
	}
	type args struct {
		ctx context.Context
		req *pgcchannelpklogicpb.GetPgcChannelPKInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pgcchannelpklogicpb.GetPgcChannelPKInfoResp
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx: ctx,
				req: &pgcchannelpklogicpb.GetPgcChannelPKInfoReq{},
			},
			want:    &pgcchannelpklogicpb.GetPgcChannelPKInfoResp{PkBattleInfo: &pgcchannelpklogicpb.PgcChannelPKBattle{}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bc.EXPECT().GetResultSourceConfList().Return([]*conf.ResultSourceConf{}).AnyTimes()
			bc.EXPECT().GetMvpSourceConfList().Return([]*conf.MvpSourceConf{}).AnyTimes()
			bc.EXPECT().GetMvpIconList().Return([]*conf.Source{}).AnyTimes()
			pgcChannelPKCli.EXPECT().GetPgcChannelPKInfo(gomock.Any(), gomock.Any()).Return(&pgcchannelpkpb.GetPgcChannelPKInfoResp{}, nil).AnyTimes()
			_, err := svr.GetPgcChannelPKInfo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPgcChannelPKInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestPgcChannelPKLogic_GetPgcChannelPKSendGiftScore(t *testing.T) {
	ctrl := initial(t)
	defer ctrl.Finish()
	type fields struct {
		bc                *conf.BusinessConfManager
		pgcChannelPKCli   *pgcchannelpk.Client
		nobilityClient    *nobility.Client
		numericClient     *numeric.Client
		memberRank        *channelmemberviprank.Client
		superPlayerClient *superPlayerCli.Client
	}
	type args struct {
		ctx context.Context
		req *pgcchannelpklogicpb.GetPgcChannelPKSendGiftScoreReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pgcchannelpklogicpb.GetPgcChannelPKSendGiftScoreResp
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx: ctx,
				req: &pgcchannelpklogicpb.GetPgcChannelPKSendGiftScoreReq{},
			},
			want:    &pgcchannelpklogicpb.GetPgcChannelPKSendGiftScoreResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pgcChannelPKCli.EXPECT().GetPgcChannelPKSendGiftScore(gomock.Any(), gomock.Any(), gomock.Any()).Return(&pgcchannelpkpb.GetPgcChannelPKSendGiftScoreResp{}, nil)
			got, err := svr.GetPgcChannelPKSendGiftScore(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPgcChannelPKSendGiftScore() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPgcChannelPKSendGiftScore() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPgcChannelPKLogic_PgcChannelPKReportClientIDChange(t *testing.T) {
	ctrl := initial(t)
	defer ctrl.Finish()
	type fields struct {
		bc                *conf.BusinessConfManager
		pgcChannelPKCli   *pgcchannelpk.Client
		nobilityClient    *nobility.Client
		numericClient     *numeric.Client
		memberRank        *channelmemberviprank.Client
		superPlayerClient *superPlayerCli.Client
	}
	type args struct {
		ctx context.Context
		req *pgcchannelpklogicpb.PgcChannelPKReportClientIDChangeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pgcchannelpklogicpb.PgcChannelPKReportClientIDChangeResp
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx: ctx,
				req: &pgcchannelpklogicpb.PgcChannelPKReportClientIDChangeReq{ChannelId: cid},
			},
			want:    &pgcchannelpklogicpb.PgcChannelPKReportClientIDChangeResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pgcChannelPKCli.EXPECT().PgcChannelPKReportClientIDChange(gomock.Any(), gomock.Any()).Return(&pgcchannelpkpb.PgcChannelPKReportClientIDChangeResp{}, nil).AnyTimes()
			got, err := svr.PgcChannelPKReportClientIDChange(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PgcChannelPKReportClientIDChange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PgcChannelPKReportClientIDChange() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPgcChannelPKLogic_SetPgcChannelPKOpponentMicFlag(t *testing.T) {
	ctrl := initial(t)
	defer ctrl.Finish()
	type fields struct {
		bc                *conf.BusinessConfManager
		pgcChannelPKCli   *pgcchannelpk.Client
		nobilityClient    *nobility.Client
		numericClient     *numeric.Client
		memberRank        *channelmemberviprank.Client
		superPlayerClient *superPlayerCli.Client
	}
	type args struct {
		ctx context.Context
		req *pgcchannelpklogicpb.SetPgcChannelPKOpponentMicFlagReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pgcchannelpklogicpb.SetPgcChannelPKOpponentMicFlagResp
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx: ctx,
				req: &pgcchannelpklogicpb.SetPgcChannelPKOpponentMicFlagReq{},
			},
			want:    &pgcchannelpklogicpb.SetPgcChannelPKOpponentMicFlagResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pgcChannelPKCli.EXPECT().SetPgcChannelPKOpponentMicFlag(gomock.Any(), gomock.Any()).Return(&pgcchannelpkpb.SetPgcChannelPKOpponentMicFlagResp{}, nil).AnyTimes()
			got, err := svr.SetPgcChannelPKOpponentMicFlag(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetPgcChannelPKOpponentMicFlag() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SetPgcChannelPKOpponentMicFlag() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPgcChannelPKLogic_SetPgcChannelPKSwitch(t *testing.T) {
	ctrl := initial(t)
	defer ctrl.Finish()
	type fields struct {
		bc                *conf.BusinessConfManager
		pgcChannelPKCli   *pgcchannelpk.Client
		nobilityClient    *nobility.Client
		numericClient     *numeric.Client
		memberRank        *channelmemberviprank.Client
		superPlayerClient *superPlayerCli.Client
	}
	type args struct {
		ctx context.Context
		req *pgcchannelpklogicpb.SetPgcChannelPKSwitchReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pgcchannelpklogicpb.SetPgcChannelPKSwitchResp
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx: ctx,
				req: &pgcchannelpklogicpb.SetPgcChannelPKSwitchReq{},
			},
			want:    &pgcchannelpklogicpb.SetPgcChannelPKSwitchResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pgcChannelPKCli.EXPECT().SetPgcChannelPKSwitch(gomock.Any(), gomock.Any()).Return(&pgcchannelpkpb.SetPgcChannelPKSwitchResp{}, nil)
			got, err := svr.SetPgcChannelPKSwitch(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetPgcChannelPKSwitch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SetPgcChannelPKSwitch() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPgcChannelPKLogic_StartPgcChannelPK(t *testing.T) {
	ctrl := initial(t)
	defer ctrl.Finish()
	type fields struct {
		bc                *conf.BusinessConfManager
		pgcChannelPKCli   *pgcchannelpk.Client
		nobilityClient    *nobility.Client
		numericClient     *numeric.Client
		memberRank        *channelmemberviprank.Client
		superPlayerClient *superPlayerCli.Client
	}
	type args struct {
		ctx context.Context
		req *pgcchannelpklogicpb.StartPgcChannelPKReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pgcchannelpklogicpb.StartPgcChannelPKResp
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx: ctx,
				req: &pgcchannelpklogicpb.StartPgcChannelPKReq{},
			},
			want:    &pgcchannelpklogicpb.StartPgcChannelPKResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pgcChannelPKCli.EXPECT().StartPgcChannelPK(gomock.Any(), gomock.Any()).Return(&pgcchannelpkpb.StartPgcChannelPKResp{}, nil)
			got, err := svr.StartPgcChannelPK(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("StartPgcChannelPK() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("StartPgcChannelPK() got = %v, want %v", got, tt.want)
			}
		})
	}
}

/*
func TestPgcChannelPKLogic_fillPlateConfigMsg(t *testing.T) {
	ctrl := initial(t)
	defer ctrl.Finish()
	type fields struct {
		bc                *conf.BusinessConfManager
		pgcChannelPKCli   *pgcchannelpk.Client
		nobilityClient    *nobility.Client
		numericClient     *numeric.Client
		memberRank        *channelmemberviprank.Client
		superPlayerClient *superPlayerCli.Client
	}
	type args struct {
		fansPlateInfo *liveFansPb.FansPlateInfo
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *app.FansPlateInfo
	}{
		{
			name: "",
			args: args{
				fansPlateInfo: &liveFansPb.FansPlateInfo{},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := svr.fillPlateConfigMsg(tt.args.fansPlateInfo); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("fillPlateConfigMsg() = %v, want %v", got, tt.want)
			}
		})
	}
}*/

func Test_fillMvpSourceConfList(t *testing.T) {
	type args struct {
		list []*conf.MvpSourceConf
	}
	tests := []struct {
		name string
		args args
		want []*pgcchannelpklogicpb.DownloadSourceConf
	}{
		{
			name: "",
			args: args{
				[]*conf.MvpSourceConf{},
			},
			want: []*pgcchannelpklogicpb.DownloadSourceConf{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := fillMvpSourceConfList(tt.args.list); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("fillMvpSourceConfList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_fillPicConfList(t *testing.T) {
	type args struct {
		list []*pgcchannelpkpb.HeadIconSourceConf
	}
	tests := []struct {
		name string
		args args
		want []*pgcchannelpklogicpb.HeadIconSourceConf
	}{
		{
			name: "",
			args: args{
				[]*pgcchannelpkpb.HeadIconSourceConf{
					{
						MinScore: 1,
						MaxScore: 2,
					},
				},
			},
			want: []*pgcchannelpklogicpb.HeadIconSourceConf{
				{
					MinScore: 1,
					MaxScore: 2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := fillPicConfList(tt.args.list); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("fillPicConfList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_fillResultSourceConfList(t *testing.T) {
	type args struct {
		list []*conf.ResultSourceConf
	}
	tests := []struct {
		name string
		args args
		want []*pgcchannelpklogicpb.ResultSourceConf
	}{
		{
			name: "",
			args: args{
				[]*conf.ResultSourceConf{
					{
						Rank:       1,
						MinScore:   2,
						MaxScore:   3,
						SourceType: 4,
						SourceId:   5,
					},
				},
			},
			want: []*pgcchannelpklogicpb.ResultSourceConf{
				{
					MinScore: 2,
					MaxScore: 3,
					Rank:     1,
					SourceWithMvp: &app.DownloadSourceInfo{
						SourceType: 4,
						SourceId:   5,
					},
					SourceWithoutMvp: &app.DownloadSourceInfo{
						SourceType: 4,
						SourceId:   5,
					},
					TitleSource: &app.DownloadSourceInfo{},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := fillResultSourceConfList(tt.args.list); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("fillResultSourceConfList() = %v, wants %v", got, tt.want)
			}
		})
	}
}
