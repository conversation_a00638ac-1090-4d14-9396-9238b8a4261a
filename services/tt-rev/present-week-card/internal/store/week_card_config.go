package store

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"go.opentelemetry.io/otel/codes"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/present-week-card"
	"strings"
	"time"

	"golang.52tt.com/pkg/log"
)

var queryFields = strings.Join([]string{
	"id", "tier_name", "price", "origin_price", "sell_point_text", "min_last_m_consume", "min_history_consume",
	"is_delete", "update_time",
}, ",")

// 周卡配置的数据库建表SQL
var createPresentWeekCardConfigTbl = `
CREATE TABLE IF NOT EXISTS present_week_card_config (  
  id int(10) NOT NULL AUTO_INCREMENT COMMENT '主键',  
  tier_name varchar(128) NOT NULL DEFAULT '' COMMENT '档位名称',
  price int(10) NOT NULL DEFAULT 0 COMMENT '价值',
  origin_price int(10) NOT NULL DEFAULT 0 COMMENT '原价,仅作展示作用', 
  sell_point_text varchar(255) NOT NULL DEFAULT '' COMMENT '卖点描述',
  min_last_m_consume int(10) NOT NULL DEFAULT 0 COMMENT '最小上月消费金额',
  min_history_consume int(10) NOT NULL DEFAULT 0 COMMENT '最小历史消费金额',
    
  is_delete int(10) NOT NULL DEFAULT 0 COMMENT '是否已经删除', 
  update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  INDEX idx_update_time (update_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT "礼物周卡配置表";`

type WeekCardConfig struct {
	ID            uint32 `db:"id"`
	TierName      string `db:"tier_name"`       // 档位名称
	Price         uint32 `db:"price"`           // 价值
	OriginPrice   uint32 `db:"origin_price"`    // 原价,仅作展示作用
	SellPointText string `db:"sell_point_text"` // 卖点描述

	MinLastMConsume   uint64 `db:"min_last_m_consume"`  // 最小上月消费金额
	MinHistoryConsume uint64 `db:"min_history_consume"` // 最小历史消费金额

	IsDelete   bool      `db:"is_delete"`   // 是否已经删除
	UpdateTime time.Time `db:"update_time"` // 更新时间
}

func (s *Store) CreatePresentWeekCardConfigTbl(ctx context.Context) error {
	_, err := s.db.ExecContext(ctx, createPresentWeekCardConfigTbl)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateRecallAwardConfigTable err:%v", err)
		return err
	}
	return nil
}

// AddPresentWeekCardConfig 添加周卡配置
func (s *Store) AddPresentWeekCardConfig(ctx context.Context, info *WeekCardConfig) error {
	query := "INSERT INTO present_week_card_config (tier_name,price,origin_price,sell_point_text,min_last_m_consume,min_history_consume) " +
		"VALUES(?,?,?,?,?,?)"

	_, err := s.db.ExecContext(ctx, query, info.TierName, info.Price, info.OriginPrice, info.SellPointText, info.MinLastMConsume, info.MinHistoryConsume)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentWeekCardConfig err:%v", err)
		return err
	}

	log.InfoWithCtx(ctx, "AddPresentWeekCardConfig:sql:%s info:%+v", query, info)
	return err
}

// GetCardConfigMaxUpdateTime 获取最大的update_time
func (s *Store) GetCardConfigMaxUpdateTime(ctx context.Context) (uint32, error) {
	maxUpdateTime := uint32(0)
	querySql := "SELECT IFNULL(max(UNIX_TIMESTAMP(update_time)), 0) as update_time FROM present_week_card_config"
	err := s.db.GetContext(ctx, &maxUpdateTime, querySql)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			log.WarnWithCtx(ctx, "GetMaxUpdateTime no rows")
			return maxUpdateTime, nil
		}
		log.ErrorWithCtx(ctx, "GetMaxUpdateTime err:%v", err)
		return maxUpdateTime, err
	}

	return maxUpdateTime, nil
}

// GetAllWeekCardConfigEffective 获取所有生效中的周卡配置
func (s *Store) GetAllWeekCardConfigEffective(ctx context.Context) ([]*WeekCardConfig, error) {
	confList := make([]*WeekCardConfig, 0)
	query := fmt.Sprintf("SELECT %s FROM present_week_card_config WHERE is_delete = 0", queryFields)

	err := s.db.SelectContext(ctx, &confList, query)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			log.WarnWithCtx(ctx, "GetAllWeekCardConfigEffective no rows")
			return confList, nil
		}
		log.ErrorWithCtx(ctx, "GetAllWeekCardConfigEffective err:%v", err)
		return nil, err
	}

	return confList, nil
}

// GetWeekCardConfigByCardId 获取周卡配置
func (s *Store) GetWeekCardConfigByCardId(ctx context.Context, cardId uint32) (*WeekCardConfig, error) {
	conf := &WeekCardConfig{}
	querySql := fmt.Sprintf("SELECT %s FROM present_week_card_config WHERE id = ?", queryFields)

	err := s.db.GetContext(ctx, conf, querySql, cardId)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			log.WarnWithCtx(ctx, "GetWeekCardConfigByCardId no rows")
			return conf, nil
		}
		log.ErrorWithCtx(ctx, "GetWeekCardConfigByCardId err:%v", err)
		return nil, err
	}
	return conf, nil
}

// UpdatePresentWeekCardConfig 更新周卡配置
func (s *Store) UpdatePresentWeekCardConfig(ctx context.Context, cardId, updateType, intValue uint32, strValue string) error {
	updateField := ""
	var value interface{}
	switch pb.UpdatePresentWeekCardConfigRequest_UpdateField(updateType) {
	case pb.UpdatePresentWeekCardConfigRequest_UPDATE_FIELD_TIER_NAME:
		updateField = "tier_name"
		value = strValue
	case pb.UpdatePresentWeekCardConfigRequest_UPDATE_FIELD_PRICE:
		updateField = "price"
		value = intValue
	case pb.UpdatePresentWeekCardConfigRequest_UPDATE_FIELD_ORIGIN_PRICE:
		updateField = "origin_price"
		value = intValue
	case pb.UpdatePresentWeekCardConfigRequest_UPDATE_FIELD_MIN_LAST_MONTH_CONSUME:
		updateField = "min_last_m_consume"
		value = intValue
	case pb.UpdatePresentWeekCardConfigRequest_UPDATE_FIELD_MIN_HISTORY_CONSUME:
		updateField = "min_history_consume"
		value = intValue
	case pb.UpdatePresentWeekCardConfigRequest_UPDARE_FIELD_SELLIN_POINT_TEXT:
		updateField = "sell_point_text"
		value = strValue
	default:
		log.ErrorWithCtx(ctx, "UpdatePresentWeekCardConfig invalid updateType:%d", updateType)
		return protocol.NewExactServerError(codes.Ok, status.ErrRequestParamInvalid)
	}

	if updateField == "" {
		log.ErrorWithCtx(ctx, "UpdatePresentWeekCardConfig invalid updateType:%d", updateType)
		return protocol.NewExactServerError(codes.Ok, status.ErrRequestParamInvalid)
	}

	updateValue := fmt.Sprintf("%s = %v", updateField, value)

	query := fmt.Sprintf("UPDATE present_week_card_config SET %s WHERE id = ?", updateValue)

	_, err := s.db.ExecContext(ctx, query, cardId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdatePresentWeekCardConfig err:%v", err)
		return err
	}
	log.InfoWithCtx(ctx, "UpdatePresentWeekCardConfig: %s, %d,%d, %v", query, cardId, updateType, value)
	return err
}

//GetAllWeekCardConfig 获取所有周卡配置
func (s *Store) GetAllWeekCardConfig(ctx context.Context) ([]*WeekCardConfig, error) {
	confList := make([]*WeekCardConfig, 0)
	querySql := fmt.Sprintf("SELECT %s FROM present_week_card_config", queryFields)
	err := s.db.SelectContext(ctx, &confList, querySql)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			log.WarnWithCtx(ctx, "GetAllWeekCardConfig no rows")
			return confList, nil
		}
		log.ErrorWithCtx(ctx, "GetAllWeekCardConfig err:%v", err)
		return nil, err
	}

	return confList, nil
}

// DeletePresentWeekCardConfig 删除周卡配置
func (s *Store) DeletePresentWeekCardConfig(ctx context.Context, id uint32) error {
	_, err := s.db.ExecContext(ctx, `UPDATE present_week_card_config SET is_delete = 1 WHERE id=?`, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeletePresentWeekCardConfig err:%v", err)
		return err
	}
	log.InfoWithCtx(ctx, "DeletePresentWeekCardConfig:%d", id)
	return err
}
