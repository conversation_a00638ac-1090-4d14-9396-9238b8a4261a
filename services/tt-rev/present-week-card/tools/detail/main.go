package main

import (
    "context"
    "crypto/tls"
    "encoding/json"
    "errors"
    "fmt"
    "github.com/go-gomail/gomail"
    "github.com/tealeg/xlsx"
    backpackBase "golang.52tt.com/clients/backpack-base"
    userPresent "golang.52tt.com/clients/userpresent"
    "golang.52tt.com/pkg/config"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/services/tt-rev/common/feishu"
    "golang.52tt.com/services/tt-rev/present-week-card/internal/store"
    //"google.golang.org/grpc"
    "io/ioutil"
    "os"
    "strconv"
    "time"
)

var presentCli *userPresent.Client
var backpackCli *backpackBase.Client

var pack2Gift = make(map[uint32]*Gift)

type Gift struct {
    Id    uint32
    Name  string
    Count uint32
    Worth uint32
    Type  string
}

var cardInfoMap = make(map[uint32]uint32)

type ServiceConfigT struct {
    MysqlConfig         *config.MysqlConfig `json:"mysql"`
    MailsTo             []string            `json:"mails_to"`
    MysqlReadOnlyConfig *config.MysqlConfig `json:"readonly_mysql"`
}

var st *Store

func (sc *ServiceConfigT) Parse(configFile string) (err error) {
    defer func() {
        if e := recover(); e != nil {
            err = fmt.Errorf("Failed to parse config: %v \n", e)
        }
    }()

    data, err := ioutil.ReadFile(configFile)
    if err != nil {
        return err
    }
    err = json.Unmarshal(data, &sc)
    if err != nil {
        return err
    }

    log.Infof("ServiceConfigT:MysqlConfig:%+v\n", sc.MysqlConfig)
    return
}

var (
    nowMonthBegin time.Time
)

func main() {
    sc := &ServiceConfigT{}
    err := sc.Parse("/home/<USER>/present-week-card/present-week-card.json")
    if err != nil {
        log.Errorf("Parse fail. err:%v", err)
        return
    }

    now := time.Now()
    if len(os.Args) >= 2 {
        ts, err := strconv.ParseUint(os.Args[1], 10, 64)
        if err == nil {
            now = time.Unix(int64(ts), 0)
        } else {
            log.Errorf("ParseUint fail. %s, err:%v", os.Args[1], err)
        }
    }
    log.Infof("now:%v", now)

    cardInfoMap[5] = 600
    cardInfoMap[6] = 3600

    nowMonthBegin = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

    monthBegin := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
    monthEnd := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
    defer cancel()

    st, err = NewMysql(sc.MysqlConfig, sc.MysqlReadOnlyConfig)
    if err != nil {
        log.Errorf("NewMysql fail. err:%v", err)
        return
    }

    //presentCli = userPresent.NewClient(grpc.WithBlock())
    //backpackCli, _ = backpackBase.NewClient()

    // 创建报表
    file := xlsx.NewFile()
    err = genSummarySheet(ctx, file, monthBegin, monthEnd)
    if err != nil {
        log.Errorf("genSummarySheet fail. err:%v", err)
        return
    }

    err = genAwardSheet(ctx, file, monthBegin, monthEnd)
    if err != nil {
        log.Errorf("genAwardSheet fail. err:%v", err)
        return
    }

    filePath := fmt.Sprintf("/home/<USER>/present-week-card/周卡财务奖励数据汇总数据_%04d%02d.xlsx", monthBegin.Year(), monthBegin.Month())
    err = file.Save(filePath)
    if err != nil {
        fmt.Printf("file save fail, err: %v\n", err)
        return
    }

    // send email
    subject := fmt.Sprintf("周卡财务数据 %s-%s", monthBegin.Format("2006-01-02 15点"), monthEnd.Format("2006-01-02 15点"))
    err = SendMail(filePath, subject, sc.MailsTo)
    if err != nil {
        fmt.Printf("Failed to SendMail %v\n", err)
        return
    }
}

func genSummarySheet(ctx context.Context, file *xlsx.File, monthBegin, monthEnd time.Time) error {
    tblTime := monthBegin

    lastMonthBegin := monthBegin.AddDate(0, -1, 0)
    lastMonthEnd := monthBegin

    // 本月购买周卡价值
    consumeStatMap, err := st.GetMonthConsumeStat(ctx, monthBegin)
    if err != nil {
        log.Errorf("GetMonthConsumeStat fail. err:%v", err)
        return err
    }

    // 本月购买总可领取奖励价值
    totalAwardMap, err := st.GetMonthAwardStatByStatus(ctx, false, false, tblTime, tblTime, tblTime, []uint32{})
    if err != nil {
        log.Errorf("GetMonthAwardStatByStatus fail. err:%v", err)
        return err
    }

    // 本月已领取奖励价值
    recvedAwardMap, err := st.GetCurMonthAwardStatRecved(ctx, monthEnd, tblTime)
    if err != nil {
        log.Errorf("GetMonthAwardStatByStatus fail. err:%v", err)
        return err
    }

    // 本月未领取
    notRevAwardMap, err := st.GetCurMonthAwardStatNotRecved(ctx, monthEnd, tblTime)
    if err != nil {
        log.Errorf("GetMonthAwardStatByStatus fail. err:%v", err)
        return err
    }

    // 本月未领，待领
    waitToRevMap, err := st.GetCurMonthAwardStatWaitToRecv(ctx, monthEnd, tblTime)
    if err != nil {
        log.Errorf("GetMonthAwardStatByStatus fail. err:%v", err)
        return err
    }

    // 上月的本月可领取奖励价值
    lastMonthAwardStatMap, err := st.GetCurMonthAwardStatWaitToRecv(ctx, lastMonthEnd, lastMonthBegin)
    if err != nil {
        log.Errorf("GetMonthAwardStatByStatus fail. err:%v", err)
        return err
    }

    // 上月的本月已领取奖励价值
    lastMonthRecvedAwardMap, err := st.GetMonthAwardStatRecved(ctx, lastMonthEnd, lastMonthBegin)
    if err != nil {
        log.Errorf("GetMonthAwardStatByStatus fail. err:%v", err)
        return err
    }

    // 上月的本月未领取奖励价值
    lastMonthNotRecvAwardMap, err := st.GetMonthAwardStatNotRecved(ctx, lastMonthEnd, lastMonthBegin)
    if err != nil {
        log.Errorf("GetMonthAwardStatByStatus fail. err:%v", err)
        return err
    }

    sheet, err := file.AddSheet("汇总表")
    if err != nil {
        log.Errorf("genSummarySheet fail to AddSheet. err:%v", err)
        return err
    }

    // 添加标题
    var colNames = []string{"月份", "周卡id", "周卡单价", "本月购买周卡价值", "本月购买总可领取奖励价值", "本月购买已领取奖励价值", "本月购买未领取奖励价值",
        "本月购买剩余可领取奖励价值", "上月购买本月可领取奖励价值", "上月购买本月已领取奖励价值", "上月购买本月未领取奖励价值"}

    row := sheet.AddRow()
    row.WriteSlice(&colNames, -1)

    date := monthBegin.Format("2006-01")

    for _, v := range consumeStatMap {
        row := sheet.AddRow()
        row.AddCell().SetString(date)

        row.AddCell().SetString(fmt.Sprint(v.CardId))
        row.AddCell().SetString(fmt.Sprint(v.Fee))
        row.AddCell().SetString(fmt.Sprint(v.TotalFee))

        // 本月总可领
        row.AddCell().SetString(fmt.Sprint(getTotalFeeFromAwardMap(totalAwardMap, v.CardId)))
        // 本月已领取
        row.AddCell().SetString(fmt.Sprint(getTotalFeeFromAwardMap(recvedAwardMap, v.CardId)))
        // 本月未领取
        row.AddCell().SetString(fmt.Sprint(getTotalFeeFromAwardMap(notRevAwardMap, v.CardId)))
        // 本月剩余可领取
        row.AddCell().SetString(fmt.Sprint(getTotalFeeFromAwardMap(waitToRevMap, v.CardId)))
        // 上月购买本月可领取
        row.AddCell().SetString(fmt.Sprint(getTotalFeeFromAwardMap(lastMonthAwardStatMap, v.CardId)))
        // 上月已领取
        row.AddCell().SetString(fmt.Sprint(getTotalFeeFromAwardMap(lastMonthRecvedAwardMap, v.CardId)))
        // 上月未领取
        row.AddCell().SetString(fmt.Sprint(getTotalFeeFromAwardMap(lastMonthNotRecvAwardMap, v.CardId)))
    }

    return nil
}

func getTotalFeeFromAwardMap(awardMap map[uint32]*AwardStat, cardId uint32) uint32 {
    if stat, ok := awardMap[cardId]; ok {
        return stat.TotalFee
    }

    return 0
}

func FeiShuWarn(url string) {
    lineList := make([][]*feishu.LineMem, 0)
    line := []*feishu.LineMem{
        {Tag: "text", Text: "check 项异常"},
    }
    lineList = append(lineList, line)

    line = []*feishu.LineMem{
        {Tag: "at", UserId: "6788876840648851726"},
    }
    lineList = append(lineList, line)

    feishu.SendFeiShuRichMsg(url, "周卡财务数据对账异常", lineList)
}

func dealWithAwardStatLastMonth(awardRecordList []*AwardRecord) []*SheetData {
    out := make([]*SheetData, 0)

    SheetDataMap := make(map[string]*SheetData)
    for _, v := range awardRecordList {
        buyDate := v.CreateTime.Format("2006-01-02")
        awardDate := v.AwardTime.Format("2006-01-02")
        key := fmt.Sprintf("%s_%d_%d_%s", buyDate, v.CardId, v.SeqId, awardDate)

        if _, ok := SheetDataMap[key]; ok {
            SheetDataMap[key].Amount += v.Amount
            SheetDataMap[key].GiftWorth += v.GiftWorth * v.Amount
            continue
        } else {
            SheetDataMap[key] = &SheetData{
                BuyDate:    buyDate,
                BuyValue:   cardInfoMap[v.CardId],
                CardId:     v.CardId,
                AwardDate:  awardDate,
                GiftId:     v.GiftId,
                SeqId:      v.SeqId,
                Amount:     v.Amount,
                GiftWorth:  v.GiftWorth * v.Amount,
                AwardState: "已领取",
            }
        }
    }

    for _, v := range SheetDataMap {
        out = append(out, v)
    }

    return out
}

func dealWithAwardStatLastMonthV2(awardRecordList []*AwardRecord) []*SheetData {
    out := make([]*SheetData, 0)

    SheetDataMap := make(map[string]*SheetData)
    for _, v := range awardRecordList {
        buyDate := v.CreateTime.Format("2006-01-02")
        awardDate := v.AwardTime.Format("2006-01-02")
        recvFlag, recvStatus := genStatusDescribe(v.ReceivableEnd, v.AwardTime, v.Status) // 1:待领取 2:未领取 3:已领取
        key := fmt.Sprintf("%s_%d_%d_%s_%s_%d", buyDate, v.CardId, v.SeqId, v.GiftId, awardDate, recvFlag)

        if _, ok := SheetDataMap[key]; ok {
            SheetDataMap[key].Amount += v.Amount
        } else {
            SheetDataMap[key] = &SheetData{
                BuyDate:    buyDate,
                BuyValue:   cardInfoMap[v.CardId],
                CardId:     v.CardId,
                AwardDate:  awardDate,
                GiftId:     v.GiftId,
                SeqId:      v.SeqId,
                Amount:     v.Amount,
                UnitPrice:  v.GiftWorth, // 这里先只记录单价
                AwardState: recvStatus,
            }
        }
    }

    for _, v := range SheetDataMap {
        v.GiftWorth = v.UnitPrice * v.Amount
        out = append(out, v)
    }

    return out
}

//SheetData "购买日期", "购买价值", "周卡id", "发奖日期", "奖品id(包裹id)", "奖品序号", "发放数量", "发放价值", "奖励状态"
type SheetData struct {
    BuyDate    string
    BuyValue   uint32
    CardId     uint32
    AwardDate  string
    GiftId     string
    SeqId      uint32
    Amount     uint32
    UnitPrice  uint32
    GiftWorth  uint32
    AwardState string
}

func genAwardSheet(ctx context.Context, file *xlsx.File, monthBegin, monthEnd time.Time) error {

    lastMonthBegin := monthBegin.AddDate(0, -1, 0)

    sheetData := make([]*SheetData, 0)

    // 获取上月购买，本月领奖的订单
    awardRecordList1, err := st.GetlastMonthBuyAwardStat(ctx, lastMonthBegin, monthBegin)
    if err != nil {
        log.Errorf("GetlastMonthBuyAwardStat fail. tblTime:%v monthBegin:%v,err:%v", lastMonthBegin, monthBegin, err)
        return err
    }

    sheetData = append(sheetData, dealWithAwardStatLastMonth(awardRecordList1)...)

    // 获取本月购买的奖励订单状态
    // 按天统计
    dayBegin := monthBegin
    for dayBegin.Before(monthEnd) {
        awardRecordListTmp := make([]*AwardRecord, 0)
        awardRecordListTmp, err = st.GetAwardRecordStat(ctx, monthBegin, dayBegin, dayBegin.AddDate(0, 0, 1))
        if err != nil {
            log.Errorf("GetAwardRecordStat fail. buyBegin:%v, buyEnd:%v, err:%v", dayBegin, dayBegin.AddDate(0, 0, 1), err)
            return err
        }

        sheetData = append(sheetData, dealWithAwardStatLastMonthV2(awardRecordListTmp)...)

        dayBegin = dayBegin.AddDate(0, 0, 1)
    }

    // 生成中奖数据报表
    name := "发奖明细表"
    sheet, err := file.AddSheet(name)
    if err != nil {
        log.Errorf("genAwardSheet fail to AddSheet. err:%v", err)
        return err
    }
    // 添加标题
    var colNames = []string{"购买日期", "购买价值", "周卡id", "发奖日期", "奖品id(包裹id)", "奖品序号", "发放数量", "发放价值", "奖励状态"}

    row := sheet.AddRow()
    row.WriteSlice(&colNames, -1)

    for _, v := range sheetData {
        row := sheet.AddRow()
        row.AddCell().SetString(v.BuyDate)

        // 购买价值
        if card, ok := cardInfoMap[v.CardId]; ok {
            row.AddCell().SetString(fmt.Sprint(card))
        } else {
            log.Errorf("cardInfoMap not found. cardId:%v", v.CardId)
            return errors.New(fmt.Sprintf("cardInfoMap not found,cardId:%v", v.CardId))
        }

        row.AddCell().SetString(fmt.Sprint(v.CardId))
        if v.AwardState != "已领取" {
            row.AddCell().SetString("-")
        } else {
            row.AddCell().SetString(v.AwardDate)
        }
        row.AddCell().SetString(fmt.Sprint(v.GiftId))
        row.AddCell().SetString(fmt.Sprint(v.SeqId))
        row.AddCell().SetString(fmt.Sprint(v.Amount))
        row.AddCell().SetString(fmt.Sprint(v.GiftWorth))
        row.AddCell().SetString(v.AwardState)
    }

    return nil
}

// 1:待领取 2:未领取 3:已领取
func genStatusDescribe(recvEnd, awardTime time.Time, status uint32) (uint32, string) {
    switch status {
    case store.AwardStatusInit:
        if recvEnd.After(nowMonthBegin) {
            return 1, "待领取"
        } else {
            return 2, "未领取"
        }
    case store.AwardStatusReceiveSuccess:
        if awardTime.After(nowMonthBegin) {
            return 1, "待领取"
        } else {
            return 3, "已领取"
        }
    case store.AwardStatusDone:
        if awardTime.After(nowMonthBegin) {
            return 1, "待领取"
        } else {
            return 3, "已领取"
        }

    default:
        return 0, "未知"
    }
    return 0, "未知"
}

func SendMail(filePath, subject string, to []string) error {
    m := gomail.NewMessage()
    m.SetHeader("From", "<EMAIL>")

    m.SetHeader("To", to...)
    m.SetHeader("Subject", subject)

    m.SetBody("text/html", "见附件")
    m.Attach(filePath) //附件

    d := gomail.NewDialer("mail.52tt.com", 465, "<EMAIL>", "V7.D.sTy@%bDB50t#n.r$A4h+-FnXA")

    d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
    if err := d.DialAndSend(m); err != nil {
        fmt.Println(err)
        return err
    }
    return nil
}

//
//func loadGiftInfo(ctx context.Context, packId uint32) (gift *Gift, err error) {
//	cfg, err := backpackCli.GetPackageItemCfg(ctx, packId)
//	if err != nil {
//		return nil, err
//	}
//
//	// 包裹中只能有一种物品
//	if len(cfg.GetItemCfgList()) != 1 {
//		log.Errorf("loadGiftInfo fail.  packId:%d, len(items) != 1 ", packId)
//		return gift, errors.New("invalid package item type")
//	}
//
//	pkgItemCfg := cfg.GetItemCfgList()[0]
//
//	switch backpackPB.PackageItemType(pkgItemCfg.ItemType) {
//	case backpackPB.PackageItemType_BACKPACK_PRESENT:
//		presentCfg, err := presentCli.GetPresentConfigById(ctx, pkgItemCfg.SourceId)
//		if err != nil {
//			return nil, err
//		}
//		giftType := "红钻礼物"
//		if presentCfg.GetItemConfig().GetPriceType() == 2 {
//			giftType = "T豆礼物"
//		}
//		gift = &Gift{
//			Id:    presentCfg.GetItemConfig().GetItemId(),
//			Name:  presentCfg.GetItemConfig().GetName(),
//			Count: pkgItemCfg.GetItemCount(),
//			Worth: presentCfg.GetItemConfig().GetPrice(),
//			Type:  giftType,
//		}
//	//case backpackPB.PackageItemType_BACKPACK_LOTTERY_FRAGMENT:
//	//	itemCfgs, err := backpackCli.GetItemCfg(ctx,  &backpackPB.GetItemCfgReq{
//	//		ItemType:         uint32(backpackPB.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
//	//		ItemSourceIdList: []uint32{pkgItemCfg.GetSourceId()},
//	//		GetAll:           false,
//	//	})
//	//	if err != nil {
//	//		return nil, err
//	//	}
//	//	if nil == itemCfgs.ItemCfgList || 1 != len(itemCfgs.ItemCfgList) {
//	//		return nil, errors.New("invalid fragment cfg")
//	//	}
//	//
//	//	fragment := backpackPB.LotteryFragmentCfg{}
//	//	err = fragment.Unmarshal()
//	//	if err != nil {
//	//		return nil, err
//	//	}
//	//	gift = &Gift{
//	//		Id:    fragment.GetFragmentId(),
//	//		Name:  fragment.GetFragmentName(),
//	//		Count: pkgItemCfg.GetItemCount(),
//	//		Worth: fragment.GetFragmentPrice(),
//	//		Type:  "碎片",
//	//	}
//	default:
//		log.Errorf("loadGiftInfo fail.  packId:%d, invalid package item type", packId)
//		return nil, errors.New("invalid package item type")
//	}
//
//	return gift, nil
//}
