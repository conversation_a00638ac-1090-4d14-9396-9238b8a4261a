package mgr

import (
    "context"
    "reflect"
    "sync"
    "testing"
    "time"

    "github.com/golang/mock/gomock"
    pbApp "golang.52tt.com/protocol/app"
    backpackpb "golang.52tt.com/protocol/services/backpacksvr"
    gnobilitypb "golang.52tt.com/protocol/services/gnobility"
    pb "golang.52tt.com/protocol/services/user-recall-award"
    "golang.52tt.com/services/tt-rev/user-recall-award/internal/cache"
    "golang.52tt.com/services/tt-rev/user-recall-award/internal/conf"
    awardhelper "golang.52tt.com/services/tt-rev/user-recall-award/internal/mgr/award_helper"
    "golang.52tt.com/services/tt-rev/user-recall-award/internal/mocks"
    "golang.52tt.com/services/tt-rev/user-recall-award/internal/rpc"
    "golang.52tt.com/services/tt-rev/user-recall-award/internal/store"
)

func TestMgr_BatchGetInviteAwardInfo(t *testing.T) {
    type args struct {
        ctx context.Context
        req *pb.BatchGetInviteAwardInfoReq
    }
    tests := []struct {
        name    string
        args    args
        want    *pb.BatchGetInviteAwardInfoResp
        wantErr bool
        mock    func()
    }{
        {
            name: "",
            args: args{
                ctx: context.Background(),
                req: &pb.BatchGetInviteAwardInfoReq{
                    RecalledUser: []*pb.RecalledUserInfo{
                        {
                            RecalledUid: 1,
                            ConsumeVal:  999,
                        },
                    },
                },
            },
            wantErr: false,
            mock: func() {
                mocks.MockAwardFactory.EXPECT().GetHelper(gomock.Any()).Return(&awardhelper.DefaultHelper{})
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m, ctrl := NewTestMgr(t)
            defer ctrl.Finish()
            tt.mock()
            _, err := m.BatchGetInviteAwardInfo(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("BatchGetInviteAwardInfo() error = %v, wantErr %v", err, tt.wantErr)
                return
            }

        })
    }
}

func TestMgr_CanGetAward(t *testing.T) {
    type fields struct {
        bc          conf.IBusinessConfManager
        store       store.IStore
        cache       cache.ICache
        rpcCli      *rpc.Client
        awardConfig *pb.GetRecallAwardResp
        mutex       sync.RWMutex
        shutDown    chan struct{}
    }
    type args struct {
        ctx         context.Context
        record      *store.RecallRecord
        recalledUid uint32
        inviteUid   uint32
        awardType   uint32
        consumeLv   uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    bool
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Mgr{
                bc:          tt.fields.bc,
                store:       tt.fields.store,
                cache:       tt.fields.cache,
                rpcCli:      tt.fields.rpcCli,
                awardConfig: tt.fields.awardConfig,
                mutex:       tt.fields.mutex,
                shutDown:    tt.fields.shutDown,
            }
            got, err := m.CanGetAward(tt.args.ctx, tt.args.record, tt.args.awardType, tt.args.consumeLv)
            if (err != nil) != tt.wantErr {
                t.Errorf("CanGetAward() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if got != tt.want {
                t.Errorf("CanGetAward() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_GenAward(t *testing.T) {
    type fields struct {
        bc          conf.IBusinessConfManager
        store       store.IStore
        cache       cache.ICache
        rpcCli      *rpc.Client
        awardConfig *pb.GetRecallAwardResp
        mutex       sync.RWMutex
        shutDown    chan struct{}
    }
    type args struct {
        ctx         context.Context
        record      *store.RecallRecord
        recalledUid uint32
        inviteUid   uint32
        awardType   uint32
        consumeLv   uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    []*store.AwardRecord
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Mgr{
                bc:          tt.fields.bc,
                store:       tt.fields.store,
                cache:       tt.fields.cache,
                rpcCli:      tt.fields.rpcCli,
                awardConfig: tt.fields.awardConfig,
                mutex:       tt.fields.mutex,
                shutDown:    tt.fields.shutDown,
            }
            _, err := m.GenAward(tt.args.ctx, tt.args.record, tt.args.recalledUid, tt.args.inviteUid, tt.args.awardType, tt.args.consumeLv)
            if (err != nil) != tt.wantErr {
                t.Errorf("GenAward() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestMgr_GetAward(t *testing.T) {
    type args struct {
        ctx         context.Context
        recalledUid uint32
        inviteUid   uint32
        awardType   uint32
        consumeLv   uint32
    }
    tests := []struct {
        name    string
        args    args
        want    []*store.AwardRecord
        wantErr bool
        mock    func()
    }{
        {
            name: "",
            args: args{
                ctx:         nil,
                recalledUid: 2465835,
                inviteUid:   2513421,
                awardType:   1,
                consumeLv:   0,
            },
            wantErr: false,
            mock: func() {
                mocks.MockCache.EXPECT().IsUserRecall(gomock.Any(), gomock.Any()).Return(true, nil)
                mocks.MockCache.EXPECT().GetRecallRecordByUids(gomock.Any(), gomock.Any()).Return([]*store.RecallRecord{{}}, []uint32{1}, nil)
                mocks.MockStore.EXPECT().GetRecallRecordByUids(gomock.Any(), gomock.Any()).Return([]*store.RecallRecord{{}}, nil)
                mocks.MockCache.EXPECT().BatchSetRecallRecord(gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockStore.EXPECT().BatchAddAwardRecord(gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockCache.EXPECT().DelRecallRecord(gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockBc.EXPECT().GetBusinessKey().Return("oyanxrravoxlygfj")
                mocks.MockBc.EXPECT().GetBusinessId().Return(uint32(360)).AnyTimes()
                mocks.MockBackpackSenderCli.EXPECT().SendBackpackWithRiskControl(gomock.Any(), gomock.Any()).Return(nil, nil)
                mocks.MockStore.EXPECT().UpdateAwardRecordStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockStore.EXPECT().CntAwardRecordByBatchAndType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil)
                mocks.MockAwardFactory.EXPECT().GetHelper(gomock.Any()).Return(&awardhelper.DefaultHelper{}).AnyTimes()

            },
        },
        {
            name: "",
            args: args{
                ctx:         nil,
                recalledUid: 2465835,
                awardType:   2,
                consumeLv:   0,
            },
            wantErr: false,
            mock: func() {
                mocks.MockCache.EXPECT().IsUserRecall(gomock.Any(), gomock.Any()).Return(true, nil)
                mocks.MockCache.EXPECT().GetRecallRecordByUids(gomock.Any(), gomock.Any()).Return([]*store.RecallRecord{{}}, []uint32{1}, nil)
                mocks.MockStore.EXPECT().GetRecallRecordByUids(gomock.Any(), gomock.Any()).Return([]*store.RecallRecord{{}}, nil)
                mocks.MockCache.EXPECT().BatchSetRecallRecord(gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockStore.EXPECT().BatchAddAwardRecord(gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockCache.EXPECT().DelRecallRecord(gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockBc.EXPECT().GetBusinessKey().Return("oyanxrravoxlygfj")
                mocks.MockBc.EXPECT().GetBusinessId().Return(uint32(360)).AnyTimes()
                mocks.MockBackpackSenderCli.EXPECT().SendBackpackWithRiskControl(gomock.Any(), gomock.Any()).Return(nil, nil)
                mocks.MockStore.EXPECT().UpdateAwardRecordStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mocks.MockStore.EXPECT().MarkGotConsecutiveLoginAward(gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockNobilityCli.EXPECT().AddTempNobility(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
                mocks.MockStore.EXPECT().CntAwardRecordByBatchAndType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil)
                mocks.MockCache.EXPECT().GetConsecutiveLoginDays(gomock.Any(), gomock.Any()).Return(uint32(7), nil)
                mocks.MockAwardFactory.EXPECT().GetHelper(gomock.Any()).Return(&awardhelper.DefaultHelper{})
            },
        },
        {
            name: "",
            args: args{
                ctx:         nil,
                recalledUid: 2465835,
                awardType:   3,
                consumeLv:   0,
            },
            wantErr: false,
            mock: func() {
                mocks.MockCache.EXPECT().IsUserRecall(gomock.Any(), gomock.Any()).Return(true, nil)
                mocks.MockCache.EXPECT().GetRecallRecordByUids(gomock.Any(), gomock.Any()).Return([]*store.RecallRecord{{}}, []uint32{1}, nil)
                mocks.MockStore.EXPECT().GetRecallRecordByUids(gomock.Any(), gomock.Any()).Return([]*store.RecallRecord{{}}, nil)
                mocks.MockCache.EXPECT().BatchSetRecallRecord(gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockStore.EXPECT().BatchAddAwardRecord(gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockCache.EXPECT().DelRecallRecord(gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockBc.EXPECT().GetBusinessKey().Return("oyanxrravoxlygfj")
                mocks.MockBc.EXPECT().GetBusinessId().Return(uint32(360))
                mocks.MockBackpackSenderCli.EXPECT().SendBackpackWithRiskControl(gomock.Any(), gomock.Any()).Return(nil, nil)
                mocks.MockStore.EXPECT().UpdateAwardRecordStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockStore.EXPECT().MarkGotConsumeValAward(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockStore.EXPECT().CntAwardRecordByBatchAndType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil)
                mocks.MockAwardFactory.EXPECT().GetHelper(gomock.Any()).Return(&awardhelper.DefaultHelper{})

            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m, ctrl := NewTestMgr(t)
            ctrl.Finish()
            tt.mock()
            _, err := m.GetAward(tt.args.ctx, tt.args.recalledUid, tt.args.inviteUid, tt.args.awardType, tt.args.consumeLv)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetAward() error = %v, wantErr %v", err, tt.wantErr)
                return
            }

        })
    }
}

func TestMgr_GetMostExpAwardInfo(t *testing.T) {
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name    string
        args    args
        want    *pb.GetMostExpAwardInfoResp
        wantErr bool
        mock    func()
    }{
        {
            name:    "",
            args:    args{},
            wantErr: false,
            mock: func() {
                mocks.MockBackpackCli.EXPECT().GetPackageItemCfg(gomock.Any(), gomock.Any(), gomock.Any()).Return(&backpackpb.GetPackageItemCfgResp{
                    ItemCfgList: []*backpackpb.PackageItemCfg{
                        {
                            BgItemId:       1,
                            BgId:           1,
                            ItemType:       1,
                            SourceId:       1,
                            ItemCount:      1,
                            FinTime:        1,
                            IsDel:          false,
                            Weight:         1,
                            DynamicFinTime: 1,
                            Months:         1,
                        },
                    },
                }, nil).AnyTimes()
                mocks.MockPresentCli.EXPECT().GetPresentConfigById(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
                mocks.MockGNobilityCli.EXPECT().GetNobilityLevelCfg(gomock.Any(), gomock.Any()).Return(&gnobilitypb.GetNobilityLevelCfgResp{}, nil)
                mocks.MockBc.EXPECT().GetNobilityCardExpire().Return(uint32(7))
                mocks.MockAwardFactory.EXPECT().GetHelper(gomock.Any()).Return(&awardhelper.DefaultHelper{})
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m, ctrl := NewTestMgr(t)
            ctrl.Finish()
            tt.mock()
            _, err := m.GetMostExpAwardInfo(tt.args.ctx)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetMostExpAwardInfo() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestMgr_GetRecallLoginAwardInfo(t *testing.T) {

    type args struct {
        ctx context.Context
        req *pb.GetRecallLoginAwardInfoReq
    }
    tests := []struct {
        name    string
        args    args
        want    *pb.GetRecallLoginAwardInfoResp
        wantErr bool
        mock    func()
    }{
        {
            name: "",
            args: args{
                ctx: context.Background(),
                req: &pb.GetRecallLoginAwardInfoReq{
                    RecalledUid:   1,
                    ConsumeVal:    1,
                    LastLoginTime: 1,
                },
            },
            wantErr: false,
            mock: func() {
                mocks.MockCache.EXPECT().IsUserRecall(gomock.Any(), gomock.Any()).Return(true, nil)
                mocks.MockCache.EXPECT().MarkUserRecall(gomock.Any(), gomock.Any()).Return(false, nil)
                mocks.MockCache.EXPECT().GetRecallRecordByUids(gomock.Any(), gomock.Any()).Return([]*store.RecallRecord{{}}, []uint32{1}, nil)
                mocks.MockStore.EXPECT().GetRecallRecordByUids(gomock.Any(), gomock.Any()).Return([]*store.RecallRecord{{}}, nil)
                mocks.MockCache.EXPECT().BatchSetRecallRecord(gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockBc.EXPECT().GetMaxReturnContinueDay().Return(uint32(14))
                mocks.MockStore.EXPECT().CntAwardRecordByBatchAndType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil)
                mocks.MockBc.EXPECT().GetMaxReturnLoginAwardDay().Return(uint32(7))

            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m, ctrl := NewTestMgr(t)
            ctrl.Finish()
            tt.mock()
            _, err := m.GetRecallLoginAwardInfo(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetRecallLoginAwardInfo() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestMgr_GetReturnAwardInfo(t *testing.T) {
    type args struct {
        ctx context.Context
        uid uint32
    }
    tests := []struct {
        name    string
        args    args
        want    *pb.GetReturnAwardInfoResp
        wantErr bool
        mock    func()
    }{
        {
            name: "",
            args: args{
                ctx: context.Background(),
                uid: 1,
            },
            wantErr: false,
            mock: func() {
                mocks.MockCache.EXPECT().IsUserRecall(gomock.Any(), gomock.Any()).Return(true, nil)
                mocks.MockCache.EXPECT().GetRecallRecordByUids(gomock.Any(), gomock.Any()).Return([]*store.RecallRecord{{}}, []uint32{1}, nil)
                mocks.MockStore.EXPECT().GetRecallRecordByUids(gomock.Any(), gomock.Any()).Return([]*store.RecallRecord{{}}, nil)
                mocks.MockCache.EXPECT().BatchSetRecallRecord(gomock.Any(), gomock.Any()).Return(nil)
                mocks.MockCache.EXPECT().GetConsecutiveLoginDays(gomock.Any(), gomock.Any()).Return(uint32(1), nil)
                mocks.MockBackpackCli.EXPECT().GetPackageItemCfg(gomock.Any(), gomock.Any(), gomock.Any()).Return(&backpackpb.GetPackageItemCfgResp{
                    ItemCfgList: []*backpackpb.PackageItemCfg{
                        {},
                    },
                }, nil).AnyTimes()
                mocks.MockGNobilityCli.EXPECT().GetNobilityLevelCfg(gomock.Any(), gomock.Any()).Return(&gnobilitypb.GetNobilityLevelCfgResp{}, nil)
                mocks.MockBc.EXPECT().GetNobilityCardExpire().Return(uint32(7))
                mocks.MockBc.EXPECT().GetMaxReturnContinueDay().Return(uint32(14)).AnyTimes()
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m, ctrl := NewTestMgr(t)
            ctrl.Finish()
            tt.mock()
            _, err := m.GetReturnAwardInfo(tt.args.ctx, tt.args.uid)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetReturnAwardInfo() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestMgr_SendGetAwardTipsTTHelper(t *testing.T) {

    type args struct {
        ctx    context.Context
        uid    uint32
        markId uint32
    }
    tests := []struct {
        name    string
        args    args
        wantErr bool
        mock    func()
    }{
        {
            name: "",
            args: args{
                ctx:    context.Background(),
                uid:    1,
                markId: 0,
            },
            wantErr: false,
            mock: func() {
                mocks.MockImApiClient.EXPECT().Send1V1ExtMsg(gomock.Any(), gomock.Any()).Return(nil, nil)
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m, ctrl := NewTestMgr(t)
            ctrl.Finish()
            tt.mock()
            if err := m.SendGetAwardTipsTTHelper(tt.args.ctx, tt.args.uid, tt.args.markId); (err != nil) != tt.wantErr {
                t.Errorf("SendGetAwardTipsTTHelper() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_SendGetInviteAwardIm(t *testing.T) {
    type args struct {
        ctx         context.Context
        recalledUid uint32
        inviteUid   uint32
        giftType    uint32
        giftId      string
    }
    tests := []struct {
        name    string
        args    args
        wantErr bool
        mock    func()
    }{
        {
            name: "",
            args: args{
                ctx:         context.Background(),
                recalledUid: 1,
                inviteUid:   2,
                giftType:    3,
                giftId:      "4",
            },
            mock: func() {
                mocks.MockImApiClient.EXPECT().Send1V1ExtMsg(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
                mocks.MockUserProfileCli.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&pbApp.UserProfile{}, nil)
                mocks.MockBackpackCli.EXPECT().GetPackageItemCfg(gomock.Any(), gomock.Any(), gomock.Any()).Return(&backpackpb.GetPackageItemCfgResp{
                    ItemCfgList: []*backpackpb.PackageItemCfg{
                        {},
                    },
                }, nil).AnyTimes()
                mocks.MockAwardFactory.EXPECT().GetHelper(gomock.Any()).Return(&awardhelper.DefaultHelper{})
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m, ctrl := NewTestMgr(t)
            ctrl.Finish()
            tt.mock()

            if err := m.SendGetInviteAwardIm(tt.args.ctx, tt.args.recalledUid, tt.args.inviteUid, tt.args.giftType, tt.args.giftId); (err != nil) != tt.wantErr {
                t.Errorf("SendGetInviteAwardIm() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func Test_randUint32(t *testing.T) {
    type args struct {
        max uint32
    }
    tests := []struct {
        name string
        args args
        want uint32
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            if got := randUint32(tt.args.max); got != tt.want {
                t.Errorf("randUint32() = %v, want %v", got, tt.want)
            }
        })
    }
}

func Test_randomAward(t *testing.T) {
    type args struct {
        ctx       context.Context
        awardList []*pb.ReturnedAwardChance
    }
    tests := []struct {
        name string
        args args
        want *pb.ReturnedAwardChance
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            if got := randomAward(tt.args.ctx, tt.args.awardList); !reflect.DeepEqual(got, tt.want) {
                t.Errorf("randomAward() = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_GenReturnAward(t *testing.T) {
    type fields struct {
        bc           conf.IBusinessConfManager
        store        store.IStore
        cache        cache.ICache
        rpcCli       *rpc.Client
        awardConfig  *pb.GetRecallAwardResp
        mutex        sync.RWMutex
        shutDown     chan struct{}
        awardFactory awardhelper.IAwardHelperFactory
    }
    type args struct {
        ctx        context.Context
        uid        uint32
        consumeVal uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    *pb.AwardConfig
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Mgr{
                bc:           tt.fields.bc,
                store:        tt.fields.store,
                cache:        tt.fields.cache,
                rpcCli:       tt.fields.rpcCli,
                awardConfig:  tt.fields.awardConfig,
                mutex:        tt.fields.mutex,
                shutDown:     tt.fields.shutDown,
                awardFactory: tt.fields.awardFactory,
            }
            got, err := m.GenReturnAward(tt.args.ctx, tt.args.uid, tt.args.consumeVal)
            if (err != nil) != tt.wantErr {
                t.Errorf("GenReturnAward() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GenReturnAward() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_GetAwardInfo(t *testing.T) {
    type fields struct {
        bc           conf.IBusinessConfManager
        store        store.IStore
        cache        cache.ICache
        rpcCli       *rpc.Client
        awardConfig  *pb.GetRecallAwardResp
        mutex        sync.RWMutex
        shutDown     chan struct{}
        awardFactory awardhelper.IAwardHelperFactory
    }
    type args struct {
        ctx      context.Context
        giftType uint32
        giftId   string
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    *pb.AwardInfo
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Mgr{
                bc:           tt.fields.bc,
                store:        tt.fields.store,
                cache:        tt.fields.cache,
                rpcCli:       tt.fields.rpcCli,
                awardConfig:  tt.fields.awardConfig,
                mutex:        tt.fields.mutex,
                shutDown:     tt.fields.shutDown,
                awardFactory: tt.fields.awardFactory,
            }
            got, err := m.GetAwardInfo(tt.args.ctx, tt.args.giftType, tt.args.giftId)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetAwardInfo() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetAwardInfo() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_GetIMAwardMsg(t *testing.T) {
    type fields struct {
        bc           conf.IBusinessConfManager
        store        store.IStore
        cache        cache.ICache
        rpcCli       *rpc.Client
        awardConfig  *pb.GetRecallAwardResp
        mutex        sync.RWMutex
        shutDown     chan struct{}
        awardFactory awardhelper.IAwardHelperFactory
    }
    type args struct {
        ctx             context.Context
        awardRecordList []*store.AwardRecord
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    string
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Mgr{
                bc:           tt.fields.bc,
                store:        tt.fields.store,
                cache:        tt.fields.cache,
                rpcCli:       tt.fields.rpcCli,
                awardConfig:  tt.fields.awardConfig,
                mutex:        tt.fields.mutex,
                shutDown:     tt.fields.shutDown,
                awardFactory: tt.fields.awardFactory,
            }
            got, err := m.GetIMAwardMsg(tt.args.ctx, tt.args.awardRecordList)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetIMAwardMsg() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if got != tt.want {
                t.Errorf("GetIMAwardMsg() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_GetInviteAward(t *testing.T) {
    type fields struct {
        bc           conf.IBusinessConfManager
        store        store.IStore
        cache        cache.ICache
        rpcCli       *rpc.Client
        awardConfig  *pb.GetRecallAwardResp
        mutex        sync.RWMutex
        shutDown     chan struct{}
        awardFactory awardhelper.IAwardHelperFactory
    }
    type args struct {
        ctx context.Context
        req *pb.GetInviteAwardReq
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    *pb.GetInviteAwardResp
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Mgr{
                bc:           tt.fields.bc,
                store:        tt.fields.store,
                cache:        tt.fields.cache,
                rpcCli:       tt.fields.rpcCli,
                awardConfig:  tt.fields.awardConfig,
                mutex:        tt.fields.mutex,
                shutDown:     tt.fields.shutDown,
                awardFactory: tt.fields.awardFactory,
            }
            got, err := m.GetInviteAward(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetInviteAward() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetInviteAward() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_GetNobilityName(t *testing.T) {
    type fields struct {
        bc           conf.IBusinessConfManager
        store        store.IStore
        cache        cache.ICache
        rpcCli       *rpc.Client
        awardConfig  *pb.GetRecallAwardResp
        mutex        sync.RWMutex
        shutDown     chan struct{}
        awardFactory awardhelper.IAwardHelperFactory
    }
    type args struct {
        ctx context.Context
        lv  uint32
    }
    tests := []struct {
        name   string
        fields fields
        args   args
        want   string
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Mgr{
                bc:           tt.fields.bc,
                store:        tt.fields.store,
                cache:        tt.fields.cache,
                rpcCli:       tt.fields.rpcCli,
                awardConfig:  tt.fields.awardConfig,
                mutex:        tt.fields.mutex,
                shutDown:     tt.fields.shutDown,
                awardFactory: tt.fields.awardFactory,
            }
            if got := m.GetNobilityName(tt.args.ctx, tt.args.lv); got != tt.want {
                t.Errorf("GetNobilityName() = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_GetReturnAward(t *testing.T) {
    type fields struct {
        bc           conf.IBusinessConfManager
        store        store.IStore
        cache        cache.ICache
        rpcCli       *rpc.Client
        awardConfig  *pb.GetRecallAwardResp
        mutex        sync.RWMutex
        shutDown     chan struct{}
        awardFactory awardhelper.IAwardHelperFactory
    }
    type args struct {
        ctx context.Context
        req *pb.GetReturnAwardReq
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    *pb.GetReturnAwardResp
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Mgr{
                bc:           tt.fields.bc,
                store:        tt.fields.store,
                cache:        tt.fields.cache,
                rpcCli:       tt.fields.rpcCli,
                awardConfig:  tt.fields.awardConfig,
                mutex:        tt.fields.mutex,
                shutDown:     tt.fields.shutDown,
                awardFactory: tt.fields.awardFactory,
            }
            got, err := m.GetReturnAward(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetReturnAward() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetReturnAward() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_SendConsecutiveLoginTaskFinTTHelper(t *testing.T) {

    type args struct {
        ctx             context.Context
        uid             uint32
        awardRecordList []*store.AwardRecord
    }
    tests := []struct {
        name    string
        args    args
        wantErr bool
        mock    func()
    }{
        {
            name: "",
            args: args{
                ctx:             context.Background(),
                uid:             1,
                awardRecordList: []*store.AwardRecord{{}},
            },
            wantErr: false,
            mock: func() {
                mocks.MockAwardFactory.EXPECT().GetHelper(gomock.Any()).Return(&awardhelper.DefaultHelper{}).AnyTimes()
                mocks.MockImApiClient.EXPECT().Send1V1ExtMsg(gomock.Any(), gomock.Any()).Return(nil, nil)
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m, ctrl := NewTestMgr(t)
            defer ctrl.Finish()
            tt.mock()
            if err := m.SendConsecutiveLoginTaskFinTTHelper(tt.args.ctx, tt.args.uid, tt.args.awardRecordList); (err != nil) != tt.wantErr {
                t.Errorf("SendConsecutiveLoginTaskFinTTHelper() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_SendConsumeTaskFinTTHelper(t *testing.T) {
    type fields struct {
        bc           conf.IBusinessConfManager
        store        store.IStore
        cache        cache.ICache
        rpcCli       *rpc.Client
        awardConfig  *pb.GetRecallAwardResp
        mutex        sync.RWMutex
        shutDown     chan struct{}
        awardFactory awardhelper.IAwardHelperFactory
    }
    type args struct {
        ctx             context.Context
        uid             uint32
        consumeLv       uint32
        awardRecordList []*store.AwardRecord
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Mgr{
                bc:           tt.fields.bc,
                store:        tt.fields.store,
                cache:        tt.fields.cache,
                rpcCli:       tt.fields.rpcCli,
                awardConfig:  tt.fields.awardConfig,
                mutex:        tt.fields.mutex,
                shutDown:     tt.fields.shutDown,
                awardFactory: tt.fields.awardFactory,
            }
            if err := m.SendConsumeTaskFinTTHelper(tt.args.ctx, tt.args.uid, tt.args.consumeLv, tt.args.awardRecordList); (err != nil) != tt.wantErr {
                t.Errorf("SendConsumeTaskFinTTHelper() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_SendGetAwardMsgTTHelper(t *testing.T) {
    type fields struct {
        bc           conf.IBusinessConfManager
        store        store.IStore
        cache        cache.ICache
        rpcCli       *rpc.Client
        awardConfig  *pb.GetRecallAwardResp
        mutex        sync.RWMutex
        shutDown     chan struct{}
        awardFactory awardhelper.IAwardHelperFactory
    }
    type args struct {
        ctx      context.Context
        uid      uint32
        giftType uint32
        giftId   string
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Mgr{
                bc:           tt.fields.bc,
                store:        tt.fields.store,
                cache:        tt.fields.cache,
                rpcCli:       tt.fields.rpcCli,
                awardConfig:  tt.fields.awardConfig,
                mutex:        tt.fields.mutex,
                shutDown:     tt.fields.shutDown,
                awardFactory: tt.fields.awardFactory,
            }
            if err := m.SendGetAwardMsgTTHelper(tt.args.ctx, tt.args.uid, tt.args.giftType, tt.args.giftId); (err != nil) != tt.wantErr {
                t.Errorf("SendGetAwardMsgTTHelper() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_getRecallExpireTime(t *testing.T) {
    type fields struct {
        bc           conf.IBusinessConfManager
        store        store.IStore
        cache        cache.ICache
        rpcCli       *rpc.Client
        awardConfig  *pb.GetRecallAwardResp
        mutex        sync.RWMutex
        shutDown     chan struct{}
        awardFactory awardhelper.IAwardHelperFactory
    }
    type args struct {
        cTime time.Time
    }
    tests := []struct {
        name   string
        fields fields
        args   args
        want   time.Time
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Mgr{
                bc:           tt.fields.bc,
                store:        tt.fields.store,
                cache:        tt.fields.cache,
                rpcCli:       tt.fields.rpcCli,
                awardConfig:  tt.fields.awardConfig,
                mutex:        tt.fields.mutex,
                shutDown:     tt.fields.shutDown,
                awardFactory: tt.fields.awardFactory,
            }
            if got := m.getRecallExpireTime(tt.args.cTime); !reflect.DeepEqual(got, tt.want) {
                t.Errorf("getRecallExpireTime() = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_getTaskActiveTimeLimit(t *testing.T) {
    type fields struct {
        bc           conf.IBusinessConfManager
        store        store.IStore
        cache        cache.ICache
        rpcCli       *rpc.Client
        awardConfig  *pb.GetRecallAwardResp
        mutex        sync.RWMutex
        shutDown     chan struct{}
        awardFactory awardhelper.IAwardHelperFactory
    }
    type args struct {
        cTime time.Time
    }
    tests := []struct {
        name   string
        fields fields
        args   args
        want   time.Time
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Mgr{
                bc:           tt.fields.bc,
                store:        tt.fields.store,
                cache:        tt.fields.cache,
                rpcCli:       tt.fields.rpcCli,
                awardConfig:  tt.fields.awardConfig,
                mutex:        tt.fields.mutex,
                shutDown:     tt.fields.shutDown,
                awardFactory: tt.fields.awardFactory,
            }
            if got := m.getTaskActiveTimeLimit(tt.args.cTime); !reflect.DeepEqual(got, tt.want) {
                t.Errorf("getTaskActiveTimeLimit() = %v, want %v", got, tt.want)
            }
        })
    }
}

func Test_randomAward1(t *testing.T) {
    type args struct {
        ctx       context.Context
        awardList []*pb.ReturnedAwardChance
    }
    tests := []struct {
        name string
        args args
        want *pb.ReturnedAwardChance
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            if got := randomAward(tt.args.ctx, tt.args.awardList); !reflect.DeepEqual(got, tt.want) {
                t.Errorf("randomAward() = %v, want %v", got, tt.want)
            }
        })
    }
}
