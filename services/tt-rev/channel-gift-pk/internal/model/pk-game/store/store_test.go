package store

import (
    "context"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "testing"
    "time"
)

var testStore *Store

func init() {
    mysqlConfig := &mysqlConnect.MysqlConfig{
        Host:     "*************", //"*************"
        Port:     3306,
        Database: "appsvr",
        Charset:  "utf8",
        UserName: "godman",
        Password: "thegodofman",
    }

    dbCli, err := mysqlConnect.NewClient(context.Background(), mysqlConfig)
    if err != nil {
        return
    }

    testStore = NewStore(dbCli, dbCli)
}

func TestStore_InsertConsumeRecord(t *testing.T) {
    orderId := time.Now().String()
    record := &ConsumeRecord{
        OrderId:     orderId,
        Uid:         1,
        ChannelId:   1,
        ToUid:       2,
        GiftId:      1,
        Price:       100,
        OrderStatus: ConsumeOrderStatusInit,
        Ctime:       time.Now(),
        TBeanTime:   time.Now(),
    }

    err := testStore.InsertConsumeRecord(context.Background(), record)
    if err != nil {
        t.Fatalf("InsertConsumeRecord failed. err:%v", err)
    }

    ok, err := testStore.ChangeConsumeRecordPayInfo(context.Background(), time.Now(), record.Uid,
        []uint32{ConsumeOrderStatusInit}, ConsumeOrderStatusWaitCommit, orderId, "", "test")
    if err != nil {
        t.Fatalf("ChangeConsumeRecordPayInfo failed. err:%v", err)
    }
    if !ok {
        t.Fatalf("ChangeConsumeRecordPayInfo failed. not found")
    }

    info, ok, err := testStore.GetConsumeRecordByPayOrderId(context.Background(), time.Now(), orderId)
    if err != nil {
        t.Fatalf("GetConsumeRecordByPayOrderId failed. err:%v", err)
    }
    if !ok {
        t.Fatalf("GetConsumeRecordByPayOrderId failed. not found")
    }
    if info.OrderId != orderId {
        t.Fatalf("GetConsumeRecordByPayOrderId failed. orderId not match")
    }

    t.Logf("%v", info)

    countInfo, err := testStore.GetConsumeTotalCountInfo(context.Background(), time.Now(), time.Now().Add(-time.Minute), time.Now().Add(time.Minute))
    if err != nil {
        t.Fatalf("GetConsumeTotalCountInfo failed. err:%v", err)
    }
    t.Logf("%+v", countInfo)

    orderIds, err := testStore.GetConsumeOrderIds(context.Background(), time.Now(), time.Now().Add(-time.Minute), time.Now().Add(time.Minute))
    if err != nil {
        t.Fatalf("GetConsumeOrderIds failed. err:%v", err)
    }
    t.Logf("%+v", orderIds)
}

func TestStore_BatInsertPkInfos(t *testing.T) {
    pkInfos := make([]*PkInfo, 0)
    now := time.Now()
    for i := 0; i < 2; i++ {
        pkInfos = append(pkInfos, &PkInfo{
            OrderId: now.Add(time.Second * time.Duration(i)).String(),
            PkId:    uint64(now.Add(time.Second * time.Duration(i)).Unix()),
            Uid:         1,
            ChannelId:   uint32(i),
            ToUid:       2,
            GiftId:      1,
            Price:       100,
            PkSprite:    1,
            PkResult:    1,
            Status:      1,
            OutsideTime: now,
            EndTime:     now,
        })
    }

    err := testStore.Transaction(context.Background(), func(tx mysql.Txx) error {
        err := testStore.BatInsertPkInfos(context.Background(), tx, now, pkInfos)
        if err != nil {
            t.Fatalf("BatInsertPkInfos failed. err:%v", err)
        }

        pkInfos[0].PkResult = 2
        ok, err := testStore.UpdatePkInfo(context.Background(), tx, 1, pkInfos[0], now)
        if err != nil {
            t.Fatalf("UpdatePkInfo failed. err:%v", err)
        }

        if !ok {
            t.Fatalf("UpdatePkInfo failed. not found")
        }

        return nil
    })
    if err != nil {
        t.Fatalf("BatInsertPkInfos failed. err:%v", err)
    }

    for _, pkInfo := range pkInfos {
        info, ok, err := testStore.GetPkInfoByOrderId(context.Background(), pkInfo.OrderId, now)
        if err != nil {
            t.Fatalf("GetPkInfoByOrderId failed. err:%v", err)
        }
        if !ok {
            t.Fatalf("GetPkInfoByOrderId failed. not found")
        }
        if info.OrderId != pkInfo.OrderId {
            t.Fatalf("GetPkInfoByOrderId failed. orderId not match")
        }
        t.Logf("%+v", info)

        infos, err := testStore.GetPkInfosByPkId(context.Background(), pkInfo.PkId, now)
        if err != nil {
            t.Fatalf("GetPkInfosByPkId failed. err:%v", err)
        }
        if len(infos) == 0 {
            t.Fatalf("GetPkInfosByPkId failed. not found")
        }
        t.Logf("%+v", infos)
    }

    list, err := testStore.GetExpiredPkInfos(context.Background(), now, now)
    if err != nil {
        t.Fatalf("GetExpiredPkInfos failed. err:%v", err)
    }

    for _, info := range list {
        t.Logf("%+v", info)
    }
}

func TestStore_InsertAwards(t *testing.T) {
    awards := make([]*Award, 0)
    now := time.Now()
    for i := 0; i < 2; i++ {
        awards = append(awards, &Award{
            OrderId: now.Add(time.Second * time.Duration(i)).String(),
            PkId:    uint64(now.Add(time.Second * time.Duration(i)).Unix()),
            FromUid:       1,
            FromChannelId: 1,
            ToUid:         2,
            ToChannelId:   1,
            GiftId:        1,
            Price:         100,
            Status:        0,
            OutsideTime:   now,
            AwardTime:     now,
        })
    }

    err := testStore.Transaction(context.Background(), func(tx mysql.Txx) error {
        err := testStore.InsertAwards(context.Background(), tx, now, awards)
        if err != nil {
            t.Fatalf("InsertAwards failed. err:%v", err)
        }
        return nil
    })
    if err != nil {
        t.Fatalf("InsertAwards failed. err:%v", err)
    }

    list, err := testStore.GetAwardsByAwardTime(context.Background(), now, now.Add(-time.Second), now.Add(time.Second), 0)
    if err != nil {
        t.Fatalf("GetAwardsByAwardTime failed. err:%v", err)
    }
    if len(list) == 0 {
        t.Fatalf("GetAwardsByAwardTime failed. not found")
    }
    for _, info := range list {
        t.Logf("%+v", info)
    }

    for _, award := range awards {
        err := testStore.UpdateAwardStatus(context.Background(), award.OrderId, 1, now)
        if err != nil {
            t.Fatalf("UpdateAwardStatus failed. err:%v", err)
        }
    }

    countInfo, err := testStore.GetAwardTotalCountInfo(context.Background(), now, now.Add(-time.Minute), now.Add(time.Minute))
    if err != nil {
        t.Fatalf("GetAwardTotalCountInfo failed. err:%v", err)
    }
    t.Logf("%+v", countInfo)

    orderIds, err := testStore.GetAwardOrderIds(context.Background(), now, now.Add(-time.Minute), now.Add(time.Minute))
    if err != nil {
        t.Fatalf("GetAwardOrderIds failed. err:%v", err)
    }
    t.Logf("%+v", orderIds)

    info, ok, err := testStore.GetAwardByOrderId(context.Background(), now, awards[0].OrderId)
    if err != nil {
        t.Fatalf("GetAwardByOrderId failed. err:%v", err)
    }
    if !ok {
        t.Fatalf("GetAwardByOrderId failed. not found")
    }
    t.Logf("%+v", info)
}

func TestStore_GetChannelPkRecordByPage(t *testing.T) {
    now := time.Now()
    timeAfter := now.Add(-10 * time.Hour)

    offset := ""
    cid := uint32(1)
    limit := uint32(20)

    list, err := testStore.GetChannelPkRecordByPage(context.Background(), offset, cid, limit, timeAfter)
    if err != nil {
        t.Fatalf("GetChannelPkRecordByPage failed. err:%v", err)
    }

    for _, info := range list {
        t.Logf("%+v", info)
    }
}

func TestStore_GetPkInfoByPkIds(t *testing.T) {
    now := time.Now()
    pkId := uint64(now.Unix())

    pkInfoMap, err := testStore.GetPkInfoByPkIds(context.Background(), 1, []uint64{pkId}, now)
    if err != nil {
        t.Fatalf("GetChannelPkRecordByPkId failed. err:%v", err)
    }

    for _, pkInfos := range pkInfoMap {
        for _, info := range pkInfos {
            t.Logf("%+v", info)
        }
    }
}
