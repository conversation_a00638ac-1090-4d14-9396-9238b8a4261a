package mgr

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	mockaccount "golang.52tt.com/clients/mocks/account"
	mockAnchorContractGo "golang.52tt.com/clients/mocks/anchorcontract-go"
	mockimapi "golang.52tt.com/clients/mocks/im-api"
	mockImStrangerGo "golang.52tt.com/clients/mocks/imstranger-go"
	mockpush "golang.52tt.com/clients/mocks/push-notification/v2"
	mockseqgen "golang.52tt.com/clients/mocks/seqgen/v2"
	mocktimeline "golang.52tt.com/clients/mocks/timeline"
	mockUnifiedPay "golang.52tt.com/clients/mocks/unified_pay"
	mockuserprofile "golang.52tt.com/clients/mocks/user-profile-api"
	mockYouKnowWho "golang.52tt.com/clients/mocks/you-know-who"
	pb "golang.52tt.com/protocol/services/esport-trade"
	esportWechatPb "golang.52tt.com/protocol/services/esport-wechat"
	mockEsportWechat "golang.52tt.com/protocol/services/esport-wechat/mocks"
	mockComm "golang.52tt.com/services/tt-rev/esport/common/mocks"
	"golang.52tt.com/services/tt-rev/esport/esport-trade/internal/mocks"
	"golang.52tt.com/services/tt-rev/esport/esport-trade/internal/rpc"
	"golang.52tt.com/services/tt-rev/esport/esport-trade/internal/store"
)

var rpcCli *rpc.Client
var mockBc *mocks.MockIBusinessConfManager
var mockStore *mocks.MockIStore
var mockMongo *mocks.MockIMongoDao
var mockCache *mocks.MockICache
var orderKfk *mocks.MockIOrderKfkProducer
var imNotify *mockComm.MockITradeImNotify

var mgr *Mgr

var PushCli *mockpush.MockIClient
var AccountCli *mockaccount.MockIClient
var SeqGenCli *mockseqgen.MockIClient
var ImApiCli *mockimapi.MockIClient
var YouKnowWhoCli *mockYouKnowWho.MockIClient
var UserProfileCli *mockuserprofile.MockIClient
var UnifyPayCli *mockUnifiedPay.MockIClient
var TimelineCli *mocktimeline.MockIClient
var AnchorContractCli *mockAnchorContractGo.MockIClient
var ImStrangeGoCli *mockImStrangerGo.MockIClient
var EsportSportWechatCli *mockEsportWechat.MockEsportWechatClient

func initRpcCli(t *testing.T) {
	ctl := gomock.NewController(t)

	PushCli = mockpush.NewMockIClient(ctl)
	AccountCli = mockaccount.NewMockIClient(ctl)
	SeqGenCli = mockseqgen.NewMockIClient(ctl)
	ImApiCli = mockimapi.NewMockIClient(ctl)
	YouKnowWhoCli = mockYouKnowWho.NewMockIClient(ctl)
	UserProfileCli = mockuserprofile.NewMockIClient(ctl)
	UnifyPayCli = mockUnifiedPay.NewMockIClient(ctl)
	TimelineCli = mocktimeline.NewMockIClient(ctl)
	AnchorContractCli = mockAnchorContractGo.NewMockIClient(ctl)
	ImStrangeGoCli = mockImStrangerGo.NewMockIClient(ctl)
	EsportSportWechatCli = mockEsportWechat.NewMockEsportWechatClient(ctl)

	rpcCli = &rpc.Client{
		PushCli:           PushCli,
		SeqGenClient:      SeqGenCli,
		ImApiClient:       ImApiCli,
		YouKnowWhoCli:     YouKnowWhoCli,
		UserProfileCli:    UserProfileCli,
		UnifyPayCli:       UnifyPayCli,
		AccountCli:        AccountCli,
		TimelineCli:       TimelineCli,
		AnchorContractCli: AnchorContractCli,
		EsportWechatClint: EsportSportWechatCli,
		ImStrangerGoCli:   ImStrangeGoCli,
	}

	mockBc = mocks.NewMockIBusinessConfManager(ctl)
	mockStore = mocks.NewMockIStore(ctl)
	mockCache = mocks.NewMockICache(ctl)
	mockMongo = mocks.NewMockIMongoDao(ctl)
	orderKfk = mocks.NewMockIOrderKfkProducer(ctl)
	imNotify = mockComm.NewMockITradeImNotify(ctl)

	mgr = &Mgr{
		rpcCli:        rpcCli,
		bc:            mockBc,
		mysqlStore:    mockStore,
		mongoStore:    mockMongo,
		cache:         mockCache,
		orderKfk:      orderKfk,
		tradeImNotify: imNotify,
	}
}

func TestMgr_PlayerPayOrder(t *testing.T) {
	initRpcCli(t)

	cases := []struct {
		Name    string
		Amount  uint32
		wantErr bool
	}{
		{"common", 10, false},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			mockStore.EXPECT().InsertConsumeRecord(gomock.Any(), gomock.Any()).Return(nil),
			mockBc.EXPECT().GetPayAppId().Return("test"),
			UnifyPayCli.EXPECT().PresetFreeze(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil),

			mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, f func(c context.Context, tx mysql.Txx) error) error {
				mockStore.EXPECT().ChangeConsumeRecordStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(true, nil).AnyTimes()
				mockBc.EXPECT().GetWaitReceiveMinutes().Return(uint32(10)).AnyTimes()
				mockStore.EXPECT().InsertOrderRecord(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockStore.EXPECT().InsertOrderStatusChangeLog(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				return nil
			}),

			mockCache.EXPECT().IncrUserOrderCnt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(1), nil),
			mockCache.EXPECT().IncrUserPlaceCoachOrderCnt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			mockCache.EXPECT().DeleteBothOngoingOrderList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			mockCache.EXPECT().DelUserCoupon(gomock.Any(), gomock.Any()).Return(nil),
			PushCli.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			PushCli.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			orderKfk.EXPECT().SendStatusChangeMsg(gomock.Any(), gomock.Any()),

			imNotify.EXPECT().SendOrderImMsg(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint64(0), uint64(0), nil),
			ImStrangeGoCli.EXPECT().IncrStrangerMsgLimit(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
			mockCache.EXPECT().SetImSvrMsgId(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			imNotify.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint64(0), uint64(0), nil),
			EsportSportWechatCli.EXPECT().SendWechatMsg(gomock.Any(), gomock.Any()).Return(&esportWechatPb.SendWechatMsgResponse{}, nil),
			mockCache.EXPECT().ClearUserOrderCnt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			_, err := mgr.PlayerPayOrder(context.Background(), &pb.PlayerPayOrderRequest{
				Uid:    1,
				Remark: "test",
				Order: &pb.ProductOrder{
					Count:      testCase.Amount,
					TotalPrice: 100,
					ProductId:  1,
					PriceInfo: &pb.PriceInfo{
						Price: 100,
					},
				},
			})
			if (err != nil) != testCase.wantErr {
				t.Errorf("PlayerPayOrder error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}

			time.Sleep(time.Second)
		})
	}
}

func TestMgr_changeOrderStatusWithCommitPay(t *testing.T) {
	initRpcCli(t)

	cases := []struct {
		Name    string
		wantErr bool
	}{
		{"common", false},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			//AnchorContractCli.EXPECT().GetUserContractCacheInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
			mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, f func(c context.Context, tx mysql.Txx) error) error {
				mockStore.EXPECT().UpdateOrderStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.OrderRecord{}, true, nil).AnyTimes()
				mockStore.EXPECT().UpdateConsumeCommitInfo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
				return nil
			}),

			mockCache.EXPECT().DeleteBothOngoingOrderList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			mockStore.EXPECT().GetConsumeRecordByOrderId(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ConsumeRecord{
				CommitFee: 1, CommitAmount: 1,
			}, true, nil),
			AccountCli.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(nil, nil),
			mockBc.EXPECT().GetPayAppId().Return("test"),
			UnifyPayCli.EXPECT().UnfreezeAndConsume(gomock.Any(), gomock.Any()).Return("", "", nil),
			orderKfk.EXPECT().SendOrderSettleMsg(gomock.Any(), gomock.Any()),
			mockStore.EXPECT().UpdateConsumeTBeanInfo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),

			PushCli.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			orderKfk.EXPECT().SendStatusChangeMsg(gomock.Any(), gomock.Any()),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			_, err := mgr.changeOrderStatusWithCommitPay(context.Background(), &ChangeStatusReq{
				OrderId:   generateOrderId(1, 2, time.Now()),
				Status:    uint32(2),
				OldStatus: []uint32{1},
			}, &CommitPayInfo{
				Amount: 1,
				Fee:    1,
			}, &ChangeCouponReq{
				CouponIds: nil,
				Uid:       0,
				OldStatus: 0,
				NewStatus: 0,
			})
			if (err != nil) != testCase.wantErr {
				t.Errorf("changeOrderStatusWithCommitPay error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}
		})
	}
}

func TestMgr_changeOrderStatus(t *testing.T) {
	initRpcCli(t)

	cases := []struct {
		Name    string
		wantErr bool
	}{
		{"common", false},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, f func(c context.Context, tx mysql.Txx) error) error {
				mockStore.EXPECT().UpdateOrderStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.OrderRecord{}, true, nil).AnyTimes()
				return nil
			}),

			mockCache.EXPECT().DeleteBothOngoingOrderList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),

			PushCli.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			orderKfk.EXPECT().SendStatusChangeMsg(gomock.Any(), gomock.Any()),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			_, err := mgr.changeOrderStatus(context.Background(), &ChangeStatusReq{
				OrderId:   generateOrderId(1, 2, time.Now()),
				Status:    uint32(2),
				OldStatus: []uint32{1},
			})
			if (err != nil) != testCase.wantErr {
				t.Errorf("changeOrderStatus error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}
		})
	}
}

func TestMgr_Evaluate(t *testing.T) {
	initRpcCli(t)

	cases := []struct {
		Name    string
		wantErr bool
	}{
		{"common", false},
	}

	orderId := "test"

	for _, testCase := range cases {
		gomock.InOrder(
			mockCache.EXPECT().BatGetWaitEvaluateOrder(gomock.Any(), gomock.Any()).Return(map[string]int64{orderId: time.Now().Add(time.Minute).Unix()}, nil),
			mockStore.EXPECT().GetOrderRecord(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.OrderRecord{
				Status:    uint32(pb.OrderStatus_ORDER_STATUS_FINISHED),
				OrderId:   orderId,
				PlayerUid: 1,
				CoachUid:  2,
			}, true, nil),
			mockMongo.EXPECT().CreateEvaluate(gomock.Any(), gomock.Any()).Return(nil),
			mockCache.EXPECT().RemoveWaitEvaluateOrder(gomock.Any(), gomock.Any()).Return(nil),

			PushCli.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			mockCache.EXPECT().GetImSvrMsgId(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]uint64{0: 0}, nil),
			imNotify.EXPECT().SendOrderImSysMsg(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint64(0), uint64(0), nil),
			mockCache.EXPECT().SetImSvrMsgId(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			_, err := mgr.Evaluate(context.Background(), &pb.EvaluateRequest{
				Uid:      1,
				OrderId:  orderId,
				WordList: []string{"test"},
				Content:  "",
				ScoreInfo: &pb.EvaluateScore{
					ServiceScore: 1,
					SkillScore:   2,
					VoiceScore:   3,
				},
				IsAnonymous: true,
			})
			if (err != nil) != testCase.wantErr {
				t.Errorf("Evaluate error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}
		})
	}
}
