// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/esport/esport-trade/internal/mgr (interfaces: IMgr)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	esport_trade "golang.52tt.com/protocol/services/esport-trade"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	store "golang.52tt.com/services/tt-rev/esport/esport-trade/internal/store"
)

// MockIMgr is a mock of IMgr interface.
type MockIMgr struct {
	ctrl     *gomock.Controller
	recorder *MockIMgrMockRecorder
}

// MockIMgrMockRecorder is the mock recorder for MockIMgr.
type MockIMgrMockRecorder struct {
	mock *MockIMgr
}

// NewMockIMgr creates a new mock instance.
func NewMockIMgr(ctrl *gomock.Controller) *MockIMgr {
	mock := &MockIMgr{ctrl: ctrl}
	mock.recorder = &MockIMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIMgr) EXPECT() *MockIMgrMockRecorder {
	return m.recorder
}

// AddManualGrantCouponTask mocks base method.
func (m *MockIMgr) AddManualGrantCouponTask(arg0 context.Context, arg1 *esport_trade.AddManualGrantCouponTaskRequest) (*esport_trade.AddManualGrantCouponTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddManualGrantCouponTask", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.AddManualGrantCouponTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddManualGrantCouponTask indicates an expected call of AddManualGrantCouponTask.
func (mr *MockIMgrMockRecorder) AddManualGrantCouponTask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddManualGrantCouponTask", reflect.TypeOf((*MockIMgr)(nil).AddManualGrantCouponTask), arg0, arg1)
}

// AuditResultCallback mocks base method.
func (m *MockIMgr) AuditResultCallback(arg0 context.Context, arg1 *esport_trade.AuditResultCallbackRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AuditResultCallback", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AuditResultCallback indicates an expected call of AuditResultCallback.
func (mr *MockIMgrMockRecorder) AuditResultCallback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuditResultCallback", reflect.TypeOf((*MockIMgr)(nil).AuditResultCallback), arg0, arg1)
}

// AutoGrantCoupon mocks base method.
func (m *MockIMgr) AutoGrantCoupon(arg0 context.Context, arg1 *esport_trade.AutoGrantCouponRequest) (*esport_trade.AutoGrantCouponResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AutoGrantCoupon", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.AutoGrantCouponResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AutoGrantCoupon indicates an expected call of AutoGrantCoupon.
func (mr *MockIMgrMockRecorder) AutoGrantCoupon(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AutoGrantCoupon", reflect.TypeOf((*MockIMgr)(nil).AutoGrantCoupon), arg0, arg1)
}

// BatGetEvaluateScoreSummary mocks base method.
func (m *MockIMgr) BatGetEvaluateScoreSummary(arg0 context.Context, arg1 *esport_trade.BatGetEvaluateScoreSummaryRequest) (*esport_trade.BatGetEvaluateScoreSummaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetEvaluateScoreSummary", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.BatGetEvaluateScoreSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetEvaluateScoreSummary indicates an expected call of BatGetEvaluateScoreSummary.
func (mr *MockIMgrMockRecorder) BatGetEvaluateScoreSummary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetEvaluateScoreSummary", reflect.TypeOf((*MockIMgr)(nil).BatGetEvaluateScoreSummary), arg0, arg1)
}

// CancelOrder mocks base method.
func (m *MockIMgr) CancelOrder(arg0 context.Context, arg1, arg2 uint32, arg3, arg4 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelOrder", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelOrder indicates an expected call of CancelOrder.
func (mr *MockIMgrMockRecorder) CancelOrder(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelOrder", reflect.TypeOf((*MockIMgr)(nil).CancelOrder), arg0, arg1, arg2, arg3, arg4)
}

// ClearCouponGainCache mocks base method.
func (m *MockIMgr) ClearCouponGainCache(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearCouponGainCache", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearCouponGainCache indicates an expected call of ClearCouponGainCache.
func (mr *MockIMgrMockRecorder) ClearCouponGainCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearCouponGainCache", reflect.TypeOf((*MockIMgr)(nil).ClearCouponGainCache), arg0, arg1)
}

// Close mocks base method.
func (m *MockIMgr) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIMgrMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIMgr)(nil).Close))
}

// CoachAcceptOrderCheck mocks base method.
func (m *MockIMgr) CoachAcceptOrderCheck(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CoachAcceptOrderCheck", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CoachAcceptOrderCheck indicates an expected call of CoachAcceptOrderCheck.
func (mr *MockIMgrMockRecorder) CoachAcceptOrderCheck(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CoachAcceptOrderCheck", reflect.TypeOf((*MockIMgr)(nil).CoachAcceptOrderCheck), arg0, arg1)
}

// CoachNotifyFinishOrder mocks base method.
func (m *MockIMgr) CoachNotifyFinishOrder(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CoachNotifyFinishOrder", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CoachNotifyFinishOrder indicates an expected call of CoachNotifyFinishOrder.
func (mr *MockIMgrMockRecorder) CoachNotifyFinishOrder(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CoachNotifyFinishOrder", reflect.TypeOf((*MockIMgr)(nil).CoachNotifyFinishOrder), arg0, arg1, arg2)
}

// CoachReceiveOrder mocks base method.
func (m *MockIMgr) CoachReceiveOrder(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CoachReceiveOrder", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CoachReceiveOrder indicates an expected call of CoachReceiveOrder.
func (mr *MockIMgrMockRecorder) CoachReceiveOrder(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CoachReceiveOrder", reflect.TypeOf((*MockIMgr)(nil).CoachReceiveOrder), arg0, arg1, arg2)
}

// ContCouponUseTimes mocks base method.
func (m *MockIMgr) ContCouponUseTimes(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContCouponUseTimes", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContCouponUseTimes indicates an expected call of ContCouponUseTimes.
func (mr *MockIMgrMockRecorder) ContCouponUseTimes(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContCouponUseTimes", reflect.TypeOf((*MockIMgr)(nil).ContCouponUseTimes), arg0, arg1)
}

// CreateCouponConfig mocks base method.
func (m *MockIMgr) CreateCouponConfig(arg0 context.Context, arg1 *esport_trade.CreateCouponConfigRequest) (*esport_trade.CreateCouponConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCouponConfig", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.CreateCouponConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCouponConfig indicates an expected call of CreateCouponConfig.
func (mr *MockIMgrMockRecorder) CreateCouponConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCouponConfig", reflect.TypeOf((*MockIMgr)(nil).CreateCouponConfig), arg0, arg1)
}

// DelOrderRecord mocks base method.
func (m *MockIMgr) DelOrderRecord(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelOrderRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelOrderRecord indicates an expected call of DelOrderRecord.
func (mr *MockIMgrMockRecorder) DelOrderRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelOrderRecord", reflect.TypeOf((*MockIMgr)(nil).DelOrderRecord), arg0, arg1, arg2)
}

// EnterRefundStatus mocks base method.
func (m *MockIMgr) EnterRefundStatus(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnterRefundStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// EnterRefundStatus indicates an expected call of EnterRefundStatus.
func (mr *MockIMgrMockRecorder) EnterRefundStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnterRefundStatus", reflect.TypeOf((*MockIMgr)(nil).EnterRefundStatus), arg0, arg1)
}

// Evaluate mocks base method.
func (m *MockIMgr) Evaluate(arg0 context.Context, arg1 *esport_trade.EvaluateRequest) (*esport_trade.EvaluateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Evaluate", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.EvaluateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Evaluate indicates an expected call of Evaluate.
func (mr *MockIMgrMockRecorder) Evaluate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Evaluate", reflect.TypeOf((*MockIMgr)(nil).Evaluate), arg0, arg1)
}

// FinishOrder mocks base method.
func (m *MockIMgr) FinishOrder(arg0 context.Context, arg1 uint32, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FinishOrder", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// FinishOrder indicates an expected call of FinishOrder.
func (mr *MockIMgrMockRecorder) FinishOrder(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FinishOrder", reflect.TypeOf((*MockIMgr)(nil).FinishOrder), arg0, arg1, arg2, arg3)
}

// GetCoachCouponUseTimes mocks base method.
func (m *MockIMgr) GetCoachCouponUseTimes(arg0 context.Context, arg1 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoachCouponUseTimes", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachCouponUseTimes indicates an expected call of GetCoachCouponUseTimes.
func (mr *MockIMgrMockRecorder) GetCoachCouponUseTimes(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachCouponUseTimes", reflect.TypeOf((*MockIMgr)(nil).GetCoachCouponUseTimes), arg0, arg1)
}

// GetCoachOrderStat mocks base method.
func (m *MockIMgr) GetCoachOrderStat(arg0 context.Context, arg1 *esport_trade.GetCoachOrderStatRequest) (*esport_trade.GetCoachOrderStatResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoachOrderStat", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.GetCoachOrderStatResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachOrderStat indicates an expected call of GetCoachOrderStat.
func (mr *MockIMgrMockRecorder) GetCoachOrderStat(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachOrderStat", reflect.TypeOf((*MockIMgr)(nil).GetCoachOrderStat), arg0, arg1)
}

// GetCouponById mocks base method.
func (m *MockIMgr) GetCouponById(arg0 context.Context, arg1 uint32, arg2 string) (*esport_trade.Coupon, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCouponById", arg0, arg1, arg2)
	ret0, _ := ret[0].(*esport_trade.Coupon)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCouponById indicates an expected call of GetCouponById.
func (mr *MockIMgrMockRecorder) GetCouponById(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCouponById", reflect.TypeOf((*MockIMgr)(nil).GetCouponById), arg0, arg1, arg2)
}

// GetCouponByIds mocks base method.
func (m *MockIMgr) GetCouponByIds(arg0 context.Context, arg1 uint32, arg2 ...string) ([]*esport_trade.Coupon, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCouponByIds", varargs...)
	ret0, _ := ret[0].([]*esport_trade.Coupon)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCouponByIds indicates an expected call of GetCouponByIds.
func (mr *MockIMgrMockRecorder) GetCouponByIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCouponByIds", reflect.TypeOf((*MockIMgr)(nil).GetCouponByIds), varargs...)
}

// GetCouponConfigByIds mocks base method.
func (m *MockIMgr) GetCouponConfigByIds(arg0 context.Context, arg1 *esport_trade.GetCouponConfigByIdsRequest) (*esport_trade.GetCouponConfigByIdsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCouponConfigByIds", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.GetCouponConfigByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCouponConfigByIds indicates an expected call of GetCouponConfigByIds.
func (mr *MockIMgrMockRecorder) GetCouponConfigByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCouponConfigByIds", reflect.TypeOf((*MockIMgr)(nil).GetCouponConfigByIds), arg0, arg1)
}

// GetCouponConfigList mocks base method.
func (m *MockIMgr) GetCouponConfigList(arg0 context.Context, arg1 *esport_trade.GetCouponConfigListRequest) (*esport_trade.GetCouponConfigListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCouponConfigList", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.GetCouponConfigListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCouponConfigList indicates an expected call of GetCouponConfigList.
func (mr *MockIMgrMockRecorder) GetCouponConfigList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCouponConfigList", reflect.TypeOf((*MockIMgr)(nil).GetCouponConfigList), arg0, arg1)
}

// GetEvaluateList mocks base method.
func (m *MockIMgr) GetEvaluateList(arg0 context.Context, arg1, arg2, arg3, arg4 uint32, arg5 string) (*esport_trade.GetEvaluateListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEvaluateList", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*esport_trade.GetEvaluateListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEvaluateList indicates an expected call of GetEvaluateList.
func (mr *MockIMgrMockRecorder) GetEvaluateList(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEvaluateList", reflect.TypeOf((*MockIMgr)(nil).GetEvaluateList), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetEvaluateSummary mocks base method.
func (m *MockIMgr) GetEvaluateSummary(arg0 context.Context, arg1, arg2 uint32) (*esport_trade.GetEvaluateSummaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEvaluateSummary", arg0, arg1, arg2)
	ret0, _ := ret[0].(*esport_trade.GetEvaluateSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEvaluateSummary indicates an expected call of GetEvaluateSummary.
func (mr *MockIMgrMockRecorder) GetEvaluateSummary(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEvaluateSummary", reflect.TypeOf((*MockIMgr)(nil).GetEvaluateSummary), arg0, arg1, arg2)
}

// GetImOngoingOrderList mocks base method.
func (m *MockIMgr) GetImOngoingOrderList(arg0 context.Context, arg1, arg2 uint32) ([]*esport_trade.OrderSimpleInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImOngoingOrderList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*esport_trade.OrderSimpleInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImOngoingOrderList indicates an expected call of GetImOngoingOrderList.
func (mr *MockIMgrMockRecorder) GetImOngoingOrderList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImOngoingOrderList", reflect.TypeOf((*MockIMgr)(nil).GetImOngoingOrderList), arg0, arg1, arg2)
}

// GetManualGrantCouponTasks mocks base method.
func (m *MockIMgr) GetManualGrantCouponTasks(arg0 context.Context, arg1 *esport_trade.GetManualGrantCouponTasksRequest) (*esport_trade.GetManualGrantCouponTasksResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetManualGrantCouponTasks", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.GetManualGrantCouponTasksResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManualGrantCouponTasks indicates an expected call of GetManualGrantCouponTasks.
func (mr *MockIMgrMockRecorder) GetManualGrantCouponTasks(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManualGrantCouponTasks", reflect.TypeOf((*MockIMgr)(nil).GetManualGrantCouponTasks), arg0, arg1)
}

// GetOrderConsume mocks base method.
func (m *MockIMgr) GetOrderConsume(arg0 context.Context, arg1 *esport_trade.GetOrderConsumeRequest) (*esport_trade.GetOrderConsumeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderConsume", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.GetOrderConsumeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderConsume indicates an expected call of GetOrderConsume.
func (mr *MockIMgrMockRecorder) GetOrderConsume(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderConsume", reflect.TypeOf((*MockIMgr)(nil).GetOrderConsume), arg0, arg1)
}

// GetOrderDetail mocks base method.
func (m *MockIMgr) GetOrderDetail(arg0 context.Context, arg1 uint32, arg2 string, arg3 bool) (*esport_trade.SkillProductOrderDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderDetail", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*esport_trade.SkillProductOrderDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderDetail indicates an expected call of GetOrderDetail.
func (mr *MockIMgrMockRecorder) GetOrderDetail(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderDetail", reflect.TypeOf((*MockIMgr)(nil).GetOrderDetail), arg0, arg1, arg2, arg3)
}

// GetOrderHint mocks base method.
func (m *MockIMgr) GetOrderHint(arg0 context.Context, arg1 *esport_trade.GetOrderHintRequest) (*esport_trade.GetOrderHintResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderHint", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.GetOrderHintResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderHint indicates an expected call of GetOrderHint.
func (mr *MockIMgrMockRecorder) GetOrderHint(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderHint", reflect.TypeOf((*MockIMgr)(nil).GetOrderHint), arg0, arg1)
}

// GetOrderList mocks base method.
func (m *MockIMgr) GetOrderList(arg0 context.Context, arg1 *esport_trade.GetOrderListRequest) ([]*esport_trade.OrderSimpleInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderList", arg0, arg1)
	ret0, _ := ret[0].([]*esport_trade.OrderSimpleInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderList indicates an expected call of GetOrderList.
func (mr *MockIMgrMockRecorder) GetOrderList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderList", reflect.TypeOf((*MockIMgr)(nil).GetOrderList), arg0, arg1)
}

// GetPayOrderIds mocks base method.
func (m *MockIMgr) GetPayOrderIds(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPayOrderIds", arg0, arg1)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPayOrderIds indicates an expected call of GetPayOrderIds.
func (mr *MockIMgrMockRecorder) GetPayOrderIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayOrderIds", reflect.TypeOf((*MockIMgr)(nil).GetPayOrderIds), arg0, arg1)
}

// GetPayTotalCount mocks base method.
func (m *MockIMgr) GetPayTotalCount(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPayTotalCount", arg0, arg1)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPayTotalCount indicates an expected call of GetPayTotalCount.
func (mr *MockIMgrMockRecorder) GetPayTotalCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayTotalCount", reflect.TypeOf((*MockIMgr)(nil).GetPayTotalCount), arg0, arg1)
}

// GetUnreadManualGrantCoupon mocks base method.
func (m *MockIMgr) GetUnreadManualGrantCoupon(arg0 context.Context, arg1 uint32) ([]*esport_trade.Coupon, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnreadManualGrantCoupon", arg0, arg1)
	ret0, _ := ret[0].([]*esport_trade.Coupon)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUnreadManualGrantCoupon indicates an expected call of GetUnreadManualGrantCoupon.
func (mr *MockIMgrMockRecorder) GetUnreadManualGrantCoupon(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnreadManualGrantCoupon", reflect.TypeOf((*MockIMgr)(nil).GetUnreadManualGrantCoupon), arg0, arg1)
}

// GetUserCoupon mocks base method.
func (m *MockIMgr) GetUserCoupon(arg0 context.Context, arg1 *esport_trade.GetUserCouponRequest) (*esport_trade.GetUserCouponResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserCoupon", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.GetUserCouponResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserCoupon indicates an expected call of GetUserCoupon.
func (mr *MockIMgrMockRecorder) GetUserCoupon(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCoupon", reflect.TypeOf((*MockIMgr)(nil).GetUserCoupon), arg0, arg1)
}

// GetUserCouponByPage mocks base method.
func (m *MockIMgr) GetUserCouponByPage(arg0 context.Context, arg1 *esport_trade.GetUserCouponByPageRequest) (*esport_trade.GetUserCouponByPageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserCouponByPage", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.GetUserCouponByPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserCouponByPage indicates an expected call of GetUserCouponByPage.
func (mr *MockIMgrMockRecorder) GetUserCouponByPage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCouponByPage", reflect.TypeOf((*MockIMgr)(nil).GetUserCouponByPage), arg0, arg1)
}

// GetUserGroupInfo mocks base method.
func (m *MockIMgr) GetUserGroupInfo(arg0 context.Context, arg1 *esport_trade.GetUserGroupInfoRequest) (*esport_trade.GetUserGroupInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserGroupInfo", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.GetUserGroupInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserGroupInfo indicates an expected call of GetUserGroupInfo.
func (mr *MockIMgrMockRecorder) GetUserGroupInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserGroupInfo", reflect.TypeOf((*MockIMgr)(nil).GetUserGroupInfo), arg0, arg1)
}

// GetUserTotalExpenditureAmountMonthly mocks base method.
func (m *MockIMgr) GetUserTotalExpenditureAmountMonthly(arg0 context.Context, arg1 *esport_trade.GetUserTotalExpenditureAmountMonthlyRequest) (*esport_trade.GetUserTotalExpenditureAmountMonthlyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserTotalExpenditureAmountMonthly", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.GetUserTotalExpenditureAmountMonthlyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTotalExpenditureAmountMonthly indicates an expected call of GetUserTotalExpenditureAmountMonthly.
func (mr *MockIMgrMockRecorder) GetUserTotalExpenditureAmountMonthly(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTotalExpenditureAmountMonthly", reflect.TypeOf((*MockIMgr)(nil).GetUserTotalExpenditureAmountMonthly), arg0, arg1)
}

// MarkManualGrantCouponRead mocks base method.
func (m *MockIMgr) MarkManualGrantCouponRead(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkManualGrantCouponRead", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// MarkManualGrantCouponRead indicates an expected call of MarkManualGrantCouponRead.
func (mr *MockIMgrMockRecorder) MarkManualGrantCouponRead(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkManualGrantCouponRead", reflect.TypeOf((*MockIMgr)(nil).MarkManualGrantCouponRead), arg0, arg1)
}

// OrderNotify mocks base method.
func (m *MockIMgr) OrderNotify(arg0 context.Context, arg1 *store.OrderRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OrderNotify", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// OrderNotify indicates an expected call of OrderNotify.
func (mr *MockIMgrMockRecorder) OrderNotify(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OrderNotify", reflect.TypeOf((*MockIMgr)(nil).OrderNotify), arg0, arg1)
}

// PayCallback mocks base method.
func (m *MockIMgr) PayCallback(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayCallback", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayCallback indicates an expected call of PayCallback.
func (mr *MockIMgrMockRecorder) PayCallback(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayCallback", reflect.TypeOf((*MockIMgr)(nil).PayCallback), arg0, arg1, arg2)
}

// PlayerPayOrder mocks base method.
func (m *MockIMgr) PlayerPayOrder(arg0 context.Context, arg1 *esport_trade.PlayerPayOrderRequest) (*esport_trade.PlayerPayOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PlayerPayOrder", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.PlayerPayOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PlayerPayOrder indicates an expected call of PlayerPayOrder.
func (mr *MockIMgrMockRecorder) PlayerPayOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PlayerPayOrder", reflect.TypeOf((*MockIMgr)(nil).PlayerPayOrder), arg0, arg1)
}

// PushOrderChangeNotify mocks base method.
func (m *MockIMgr) PushOrderChangeNotify(arg0 context.Context, arg1 string, arg2 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushOrderChangeNotify", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushOrderChangeNotify indicates an expected call of PushOrderChangeNotify.
func (mr *MockIMgrMockRecorder) PushOrderChangeNotify(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushOrderChangeNotify", reflect.TypeOf((*MockIMgr)(nil).PushOrderChangeNotify), arg0, arg1, arg2)
}

// Refund mocks base method.
func (m *MockIMgr) Refund(arg0 context.Context, arg1 *esport_trade.RefundRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Refund", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Refund indicates an expected call of Refund.
func (mr *MockIMgrMockRecorder) Refund(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockIMgr)(nil).Refund), arg0, arg1)
}

// RevokeManualGrantCouponTask mocks base method.
func (m *MockIMgr) RevokeManualGrantCouponTask(arg0 context.Context, arg1 *esport_trade.RevokeManualGrantCouponTaskRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RevokeManualGrantCouponTask", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RevokeManualGrantCouponTask indicates an expected call of RevokeManualGrantCouponTask.
func (mr *MockIMgrMockRecorder) RevokeManualGrantCouponTask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokeManualGrantCouponTask", reflect.TypeOf((*MockIMgr)(nil).RevokeManualGrantCouponTask), arg0, arg1)
}

// SearchOrder mocks base method.
func (m *MockIMgr) SearchOrder(arg0 context.Context, arg1 *esport_trade.SearchOrderRequest) (*esport_trade.SearchOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchOrder", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade.SearchOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchOrder indicates an expected call of SearchOrder.
func (mr *MockIMgrMockRecorder) SearchOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchOrder", reflect.TypeOf((*MockIMgr)(nil).SearchOrder), arg0, arg1)
}

// StopManualGrantCouponTask mocks base method.
func (m *MockIMgr) StopManualGrantCouponTask(arg0 context.Context, arg1 *esport_trade.StopManualGrantCouponTaskRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopManualGrantCouponTask", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// StopManualGrantCouponTask indicates an expected call of StopManualGrantCouponTask.
func (mr *MockIMgrMockRecorder) StopManualGrantCouponTask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopManualGrantCouponTask", reflect.TypeOf((*MockIMgr)(nil).StopManualGrantCouponTask), arg0, arg1)
}

// UserPlaceOrderCheck mocks base method.
func (m *MockIMgr) UserPlaceOrderCheck(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserPlaceOrderCheck", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UserPlaceOrderCheck indicates an expected call of UserPlaceOrderCheck.
func (mr *MockIMgrMockRecorder) UserPlaceOrderCheck(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserPlaceOrderCheck", reflect.TypeOf((*MockIMgr)(nil).UserPlaceOrderCheck), arg0, arg1, arg2)
}
