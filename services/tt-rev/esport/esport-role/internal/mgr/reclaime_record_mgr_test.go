package mgr

import (
	esportSkillPb "golang.52tt.com/protocol/services/esport-skill"
	"testing"
)

func Test_skillInfoCompressToString(t *testing.T) {

	skillInfo := &esportSkillPb.UserCurrentSkill{
		Uid: uid,
		Skill: []*esportSkillPb.UserSkillInfo{
			{
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			},
			{
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			}, {
				GameId:        1,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			},

			{
				GameId:        2,
				GameName:      "111",
				SkillEvidence: "111",
				Audio:         "111",
				AudioDuration: 1,
				SectionList: []*esportSkillPb.SectionInfo{
					{
						SectionId:   1,
						SectionName: "111",
					},
				},
				TextDesc: "111",
				GameRank: 1,
			},
		},
		AuditType: 0,
	}

	compressStr := skillInfoCompressToString(skillInfo)
	t.Log(compressStr)
	t.Log(len(compressStr))

	// 解压缩
	userSkillInfo := skillStringUmCompressToPb(compressStr)
	t.Log(userSkillInfo)
}
