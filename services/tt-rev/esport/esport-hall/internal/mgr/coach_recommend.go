package mgr

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/esport_logic"
	esportSkill "golang.52tt.com/protocol/services/esport-skill"
	esport_statistics "golang.52tt.com/protocol/services/esport-statistics"
	"golang.52tt.com/protocol/services/esport_godlevel"
	comctx "golang.52tt.com/services/tt-rev/common/ctx"
	"golang.52tt.com/services/tt-rev/esport/common/collection/list"
	"google.golang.org/grpc/metadata"
	"hash/fnv"
	"os"
	"sort"
	"sync"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/esport_hall"
	"golang.52tt.com/services/tt-rev/esport/esport-hall/internal/store"
	"google.golang.org/grpc/codes"
)

const (
	updateProductRecommendValBatchSize = 1000
)

func (m *Mgr) GetGamePriceProperty(ctx context.Context, gameId uint32) (*pb.GetGamePricePropertyResponse, error) {
	resp := &pb.GetGamePricePropertyResponse{}
	priceProperty := m.bc.GetSkillProductPriceSearchCfg(gameId)
	if len(priceProperty) == 0 {
		return resp, nil
	}

	valList := make([]*pb.GamePropertyVal, 0, len(priceProperty))
	for i, item := range priceProperty {
		valList = append(valList, &pb.GamePropertyVal{
			Id:   uint32(i + 1),
			Name: item,
		})
	}

	resp.PriceProperty = &pb.GameProperty{
		Name:    "价格",
		ValList: valList,
	}
	return resp, nil

}

type skillProductSorter struct {
	data     []*store.SkillProduct
	lenLimit int
}

func newSkillProductSorter(lenLimit int) *skillProductSorter {
	return &skillProductSorter{
		data:     make([]*store.SkillProduct, 0, lenLimit),
		lenLimit: lenLimit,
	}
}

func (s *skillProductSorter) add(spList []*store.SkillProduct) {
	s.data = append(s.data, spList...)
	sort.Slice(s.data, func(i, j int) bool {
		if s.data[i].IsQuickReceive != s.data[j].IsQuickReceive {
			return s.data[i].IsQuickReceive > s.data[j].IsQuickReceive // 开启了秒接单的优先进入列表
		}
		return s.data[i].RecommendVal > s.data[j].RecommendVal
	})
	if len(s.data) > s.lenLimit {
		s.data = s.data[:s.lenLimit]
	}
}

type localGodLevel struct {
	data map[uint32]uint32
}

func (l *localGodLevel) update(data map[uint32]uint32) {
	l.data = data
}

func (l *localGodLevel) get(uid uint32) uint32 {
	if l.data == nil {
		return 0
	}
	return l.data[uid]
}

var (
	godLevel                = localGodLevel{data: make(map[uint32]uint32)}
	godLevelUpdateBatchSize = 100
)

func (m *Mgr) UpdateGodLevel() {
	ctx, cancel := comctx.WithTimeout(10 * time.Second)
	defer cancel()

	startTime := time.Now()
	tmpGodLevelMap := map[uint32]uint32{}
	coachUid := m.GetNowCoachUid()
	for i := 0; i < len(coachUid); {
		start := i
		end := i + godLevelUpdateBatchSize
		if end > len(coachUid) {
			end = len(coachUid)
		}
		batchUid := coachUid[start:end]
		glResp, err := m.rpcCli.ESportGodLevelCli.BatchGetGodLevelByUid(ctx, &esport_godlevel.BatchGetGodLevelByUidReq{
			UidList: batchUid,
		})
		if err != nil {
			i = end
			log.WarnWithCtx(ctx, "UpdateGodLevel BatchGetGodLevelByUid, err: %v", err)
			i = end
			continue
		}
		for uid, lv := range glResp.GetUidLvs() {
			tmpGodLevelMap[uid] = lv
		}

		i = end
	}

	godLevel.update(tmpGodLevelMap)

	log.Infof("UpdateGodLevel success, cost: %dms", time.Now().Sub(startTime).Milliseconds())
}

type localFamousPlayer struct {
	data map[uint32]map[uint32]bool
}

func (l *localFamousPlayer) update(data map[uint32]map[uint32]bool) {
	l.data = data
}

func (l *localFamousPlayer) get(gameId, uid uint32) bool {
	if l.data == nil {
		return false
	}
	return l.data[gameId][uid]
}

func (l *localFamousPlayer) getGameFamousPlayerCopy(gameId uint32) map[uint32]bool {
	if l.data == nil {
		return nil
	}
	famousPlayerMapCopy := make(map[uint32]bool)
	for k, v := range l.data[gameId] {
		famousPlayerMapCopy[k] = v
	}
	return famousPlayerMapCopy
}

var (
	famousPlayer                = localFamousPlayer{} // gameId -> map[uid]bool
	famousPlayerUpdateBatchSize = 100
)

func (m *Mgr) UpdateFamousPlayer() {
	ctx, cancel := comctx.WithTimeout(30 * time.Second)
	defer cancel()

	nowCoachGroupByGameIdRW.RLock()
	defer nowCoachGroupByGameIdRW.RUnlock()

	tmpFamousPlayerMap := make(map[uint32]map[uint32]bool)
	for gameId, coachUidMap := range nowCoachGroupByGameIdMap {
		tmpFamousPlayerMap[gameId] = make(map[uint32]bool)
		coachList := make([]uint32, 0, len(coachUidMap))
		for uid := range coachUidMap {
			coachList = append(coachList, uid)
		}

		for i := 0; i < len(coachList); {
			start := i
			end := i + famousPlayerUpdateBatchSize
			if end > len(coachList) {
				end = len(coachList)
			}
			batchUid := coachList[start:end]
			labelResp, err := m.rpcCli.ESportSkillCli.BatchGetCoachLabelsForGame(ctx, &esportSkill.BatchGetCoachLabelsForGameRequest{
				GameId:   gameId,
				CoachIds: batchUid,
			})
			if err != nil {
				i = end
				log.ErrorWithCtx(ctx, "UpdateFamousPlayer BatchGetCoachLabelsForGame, err: %v", err)
				i = end
				continue
			}
			for _, item := range labelResp.GetLabelList() {
				tmpFamousPlayerMap[gameId][item.GetCoachId()] = item.GetIsRenowned()
			}
			i = end
		}
	}

	famousPlayer.update(tmpFamousPlayerMap)
}

func (m *Mgr) UpdateProductRecommendValTimingV2() {
	ctx, cancel := comctx.WithTimeout(5 * time.Minute)
	defer cancel()

	taskSt := time.Now()
	skillIdList := m.store.DistinctSkillId(ctx)
	for _, skillId := range skillIdList {
		if skillId == 156 {
			log.Debugf("debug")
		}
		offset := 0

		// 本地排序列表
		oldSPListSorter := newSkillProductSorter(int(m.bc.GetRecommendValConfig().EachGameTopN))
		newSPListSorter := newSkillProductSorter(int(m.bc.GetRecommendValConfig().EachGameTopN))

		for {
			data, err := m.store.FindSkillProductBySkillIdPaged(ctx, skillId, offset, updateProductRecommendValBatchSize)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateProductRecommendValTiming GetAllSkillProductPaged, err: %v", err)
				return
			}
			if len(data) == 0 {
				break
			}

			// 获取订单数据
			coachOrderSum := m.getCoachOrderSum(ctx, skillId, data)

			oldSpList := make([]*store.SkillProduct, 0, len(data))
			newSPList := make([]*store.SkillProduct, 0, len(data))
			for _, item := range data {
				godLevelVal := uint32(0)
				if int(item.CoachLevel) < len(m.bc.GetRecommendValConfig().CoachLevelVal) {
					godLevelVal = m.bc.GetRecommendValConfig().CoachLevelVal[int(item.CoachLevel)]
				}
				famousPlayerVal := uint32(0)
				if famousPlayer.get(item.SkillId, item.Uid) {
					famousPlayerVal = m.bc.GetRecommendValConfig().FamousPlayerVal
				}
				recommendVal := float64(godLevelVal + famousPlayerVal)

				coachOrderSumDetail, ok := coachOrderSum[item.Uid]

				// 如果有历史订单数据, 套用新的基础分计算公式
				originRcmdVal := recommendVal
				if ok && coachOrderSumDetail.HistoryAllOrderSum > 0 {
					days14BaseRcmdVal := recommendVal * m.bc.GetRecommendValConfig().Days14OldRcmdValFactor * float64(coachOrderSumDetail.Days14GameOrderSum) / float64(coachOrderSumDetail.Days14AllOrderSum)
					historyBaseRcmdVal := recommendVal * m.bc.GetRecommendValConfig().HistoryOldRcmdValFactor * float64(coachOrderSumDetail.HistoryGameOrderSum) / float64(coachOrderSumDetail.HistoryAllOrderSum)
					recommendVal = days14BaseRcmdVal*m.bc.GetRecommendValConfig().Days14BaseRcmdValFactor + historyBaseRcmdVal*m.bc.GetRecommendValConfig().HistoryBaseRcmdValFactor
				}
				log.DebugWithCtx(ctx, "UpdateProductRecommendValTiming, gameId: %d, uid: %d, originRcmdVal: %f, rcmdVal: %f", skillId, item.Uid, originRcmdVal, recommendVal)

				item.RecommendVal = recommendVal
				if m.IsQuickReceiveUser(item.Uid) {
					item.IsQuickReceive = 1 // 标记秒接单用户
				}

				if m.isNewCoach(taskSt, item.CreateTime, item.Uid) {
					item.IsNewCoach = true
					newSPList = append(newSPList, item)
					continue
				}
				oldSpList = append(oldSpList, item)
			}

			oldSPListSorter.add(oldSpList)
			newSPListSorter.add(newSPList)
			// 下一轮起始游标
			offset += updateProductRecommendValBatchSize
		}
		// 更新搜索索引
		// 推荐位用户
		operationRecommendProductList := m.getOperationCoachRecommend(ctx, 0, skillId)
		// 固定展示实验用户
		fixedCoach := fixedCoachSpList[skillId]
		if err := m.updateSearchIndex(ctx, skillId, oldSPListSorter.data, newSPListSorter.data, operationRecommendProductList, fixedCoach); err != nil {
			log.WarnWithCtx(ctx, "UpdateProductRecommendValTiming updateSearchIndex, err: %v", err)
		}
		// 更新缓存
		err := m.cache.DelSkillProductOrderly(ctx, skillId)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateProductRecommendValTiming DelSkillProductOrderly, err: %v", err)
			continue
		}
		if err := m.cache.AddOldSkillProductOrderly(ctx, skillId, oldSPListSorter.data); err != nil {
			log.WarnWithCtx(ctx, "UpdateProductRecommendValTiming AddOldSkillProductOrderly, err: %v", err)
		}
		if err := m.cache.AddNewSkillProductOrderLy(ctx, skillId, newSPListSorter.data); err != nil {
			log.WarnWithCtx(ctx, "UpdateProductRecommendValTiming AddNewSkillProductOrderLy, err: %v", err)
		}
	}
	log.InfoWithCtx(ctx, "UpdateProductRecommendValTiming, cost: %d", time.Now().Sub(taskSt)/time.Millisecond)
}

func (m *Mgr) isNewCoach(taskSt, createTime time.Time, uid uint32) bool {
	return godLevel.get(uid) == 1 && taskSt.Sub(createTime) < time.Hour*24*7
}

func (m *Mgr) getCoachOrderSum(ctx context.Context, skillId uint32, data []*store.SkillProduct) map[uint32]*esport_statistics.CoachOrderSum {
	subEnvTag := os.Getenv("DYEING_ENVIRONMENT_MARK")
	if subEnvTag == "" {
		subEnvTag = os.Getenv("MY_SENV_NAME") // 兼容jetdev部署
	}
	log.DebugWithCtx(ctx, "refreshCoachConversionRate subEnvTag: %s", subEnvTag)
	if subEnvTag != "" {
		ctx = metadata.AppendToOutgoingContext(ctx, "x-qw-traffic-mark", subEnvTag)
	}

	res := make(map[uint32]*esport_statistics.CoachOrderSum)
	// 查询最近大神订单数据
	coachUidList := make([]uint32, 0, len(data))
	for _, item := range data {
		coachUidList = append(coachUidList, item.Uid)
	}
	orderSumResp, err := m.rpcCli.ESportStatCli.BatGetCoachOrderSum(ctx, &esport_statistics.BatGetCoachOrderSumRequest{
		CoachUidList: coachUidList,
		GameId:       skillId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateProductRecommendValTiming.getCoachOrderSum, BatGetCoachOrderSum, err: %v", err)
		return res
	}

	for _, item := range orderSumResp.GetCoachOrderSumList() {
		res[item.GetCoachUid()] = item
	}

	return res
}

func genTagUidKey(skillId, propertyType uint32, propertyName, tag string) string {
	propertyNameHash := hash2Uint32(propertyName)
	tagHash := hash2Uint32(tag)
	return fmt.Sprintf("%d_%d_%d_%d", skillId, propertyType, propertyNameHash, tagHash)
}

func hash2Uint32(str string) uint32 {
	h := fnv.New32a()
	h.Write([]byte(str))
	hash := h.Sum32()
	return hash
}

func (m *Mgr) updateSearchIndex(ctx context.Context, skillId uint32, spList ...[]*store.SkillProduct) error {
	famousPlayMap := famousPlayer.getGameFamousPlayerCopy(skillId)
	sumSpList := make([]*store.SkillProduct, 0)
	for _, item := range spList {
		if item == nil {
			continue
		}
		sumSpList = append(sumSpList, item...)
	}
	uidList := make([]uint32, 0, len(sumSpList))
	for _, item := range sumSpList {
		uidList = append(uidList, item.Uid)
	}
	upm := m.batchGetUserProfile(ctx, uidList)
	// 更新价格
	m.updateSameGamePriceBatch(ctx, sumSpList)

	tagUidSet := make(map[string][]uint32)
	for _, item := range sumSpList {
		// 记录知名选手uid映射
		if famousPlayMap[item.Uid] {
			key := genTagUidKey(item.SkillId, uint32(esport_logic.GameProperty_PROPERTY_TYPE_FAMUOS_PLAYER), m.bc.GetFamousPlayerTitle(), "知名选手")
			tagUidSet[key] = append(tagUidSet[key], item.Uid)
		}

		// 记录性别uid映射
		gender := "女"
		if upm[item.Uid].GetSex() == 1 {
			gender = "男"
		}
		key := genTagUidKey(item.SkillId, uint32(esport_logic.GameProperty_PROPERTY_TYPE_GENDER), "性别", gender)
		tagUidSet[key] = append(tagUidSet[key], item.Uid)

		// 记录价格区间uid映射
		priceSearchList := m.bc.GetSkillProductPriceSearchCfg(item.SkillId)
		for _, ps := range priceSearchList {
			minPrice, maxPrice := m.bc.ParseSkillProductPriceSearch(ps)
			if item.Price >= minPrice && item.Price <= maxPrice {
				key := genTagUidKey(item.SkillId, uint32(esport_logic.GameProperty_PROPERTY_TYPE_PRICE), "价格", ps)
				tagUidSet[key] = append(tagUidSet[key], item.Uid)
				break
			}
		}

		// 记录tag uid映射
		for _, prop := range item.SkillProperty {
			if prop.GetSectionName() == guaranteeWin && !item.GuaranteeWin { // 报应开关没打开, 不建立包赢搜索索引
				continue
			}

			for _, tag := range prop.GetItemList() {
				key := genTagUidKey(item.SkillId, uint32(esport_logic.GameProperty_PROPERTY_TYPE_CUSTOM), prop.GetSectionName(), tag)
				tagUidSet[key] = append(tagUidSet[key], item.Uid)
			}
		}
	}

	// 更新缓存
	cErr := m.cache.UpdateSkillProductSearchIndex(ctx, tagUidSet)
	if cErr != nil {
		log.WarnWithCtx(ctx, "UpdateEachGameTopCoachList UpdateSkillProductSearchIndex, err: %v", cErr)
	}
	return nil
}

func (m *Mgr) batchGetUserProfile(ctx context.Context, uidList []uint32) map[uint32]*app.UserProfile {
	userProfileMap := make(map[uint32]*app.UserProfile)
	batch := 100
	for i := 0; i < len(uidList); {
		batEnd := i + batch
		if batEnd > len(uidList) {
			batEnd = len(uidList)
		}
		thisBatch := uidList[i:batEnd]

		thisBatUp, err := m.rpcCli.UserProfileCli.BatchGetUserProfileV2(ctx, thisBatch, false)
		if err != nil {
			i = batEnd
			log.WarnWithCtx(ctx, "batchGetUserProfile.BatchGetUserProfileV2, batUid: %+v, err: %v", thisBatch, err)
			i = batEnd
			continue
		}
		for k, v := range thisBatUp {
			userProfileMap[k] = v
		}
		i = batEnd
	}

	return userProfileMap
}

var (
	nowCoachMap              = map[uint32]bool{} // 当前平台中的大神, 用于过滤消息消费, 减少处理量
	nowCoachMapRWLock        sync.RWMutex
	nowCoachGroupByGameIdMap = map[uint32]map[uint32]bool{}
	nowCoachGroupByGameIdRW  sync.RWMutex
)

func getGameIdListFromCoachGroupMap() []uint32 {
	gameIdList := make([]uint32, 0, len(nowCoachGroupByGameIdMap))
	nowCoachGroupByGameIdRW.RLock()
	defer nowCoachGroupByGameIdRW.RUnlock()
	for gameId := range nowCoachGroupByGameIdMap {
		gameIdList = append(gameIdList, gameId)
	}
	return gameIdList
}

func getSingleCoachMapFromCoachGroupMap(gameId uint32) map[uint32]bool {
	nowCoachGroupByGameIdRW.RLock()
	defer nowCoachGroupByGameIdRW.RUnlock()
	return nowCoachGroupByGameIdMap[gameId]
}

func (m *Mgr) UpdateNowCoachMapTiming() {
	ctx, cancel := comctx.WithTimeout(5 * time.Second)
	defer cancel()
	uidList := m.store.DistnctUid(ctx)

	tmpCoachMap := make(map[uint32]bool)
	for _, uid := range uidList {
		tmpCoachMap[uid] = true
	}

	nowCoachMapRWLock.Lock()
	defer nowCoachMapRWLock.Unlock()
	nowCoachMap = tmpCoachMap
}

func (m *Mgr) UpdateNowCoachGroupByIdMapTiming() {
	ctx, cancel := comctx.WithTimeout(5 * time.Second)
	defer cancel()
	skillResp, err := m.rpcCli.ESportSkillCli.GetAllGameSimpleInfo(ctx, &esportSkill.GetAllGameSimpleInfoRequest{})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateNowCoachGroupByIdMapTiming GetAllGameSimpleInfo, err: %v", err)
		return
	}
	for _, item := range skillResp.GetGameList() {
		uidList, err := m.store.DistinctUidBySkillId(ctx, item.GetGameId())
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateNowCoachGroupByIdMapTiming DistinctUidBySkillId, gameId: %d, err: %v", item.GetGameId(), err)
			continue
		}
		tmpCoachMap := make(map[uint32]bool)
		for _, uid := range uidList {
			tmpCoachMap[uid] = true
		}
		nowCoachGroupByGameIdRW.Lock()
		nowCoachGroupByGameIdMap[item.GetGameId()] = tmpCoachMap
		nowCoachGroupByGameIdRW.Unlock()

		log.DebugWithCtx(ctx, "UpdateNowCoachGroupByIdMapTiming, gameId: %d, cnt: %d", item.GetGameId(), len(tmpCoachMap))
	}

}

func (m *Mgr) IsNowCoach(uid uint32) bool {
	nowCoachMapRWLock.RLock()
	defer nowCoachMapRWLock.RUnlock()
	return nowCoachMap[uid]
}

func (m *Mgr) GetNowCoachUid() []uint32 {
	nowCoachMapRWLock.RLock()
	defer nowCoachMapRWLock.RUnlock()

	uidList := make([]uint32, 0, len(nowCoachMap))
	for uid := range nowCoachMap {
		uidList = append(uidList, uid)
	}
	return uidList
}

// HandleCoachOnline 记录大神上下线状态, 性别等数据, 便于曝光值计算
func (m *Mgr) HandleCoachOnline(ctx context.Context, uid uint32, online bool) {
	err := m.cache.SetUserOnline(ctx, uid, online)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleCoachOnline SetUserOnline, err: %v", err)
	}

	// 性别缓存即使存在，也要刷新。因为现在端内可以变更性别1次
	up, err := m.rpcCli.UserProfileCli.GetUserProfileV2(ctx, uid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleCoachOnline GetUserProfileV2, err: %v", err)
		return
	}
	cErr := m.cache.SetUserSex(ctx, uid, up.GetSex())
	if cErr != nil {
		log.ErrorWithCtx(ctx, "HandleCoachOnline SetUserSex, err: %v", cErr)
	}
}

func (m *Mgr) HasFamousPlayer(ctx context.Context, request *pb.HasFamousPlayerRequest) (*pb.HasFamousPlayerResponse, error) {
	resp := &pb.HasFamousPlayerResponse{}
	cnt, err := m.cache.CntTagKey(ctx, genTagUidKey(request.GetSkillId(), uint32(esport_logic.GameProperty_PROPERTY_TYPE_FAMUOS_PLAYER), m.bc.GetFamousPlayerTitle(), "知名选手"))
	if err != nil {
		log.ErrorWithCtx(ctx, "HasFamousPlayer, skillId: %d, err: %v", request.GetSkillId(), err)
		return resp, err
	}
	resp.Cnt = cnt
	return resp, nil
}

func (m *Mgr) MarkRecommendTimes(ctx context.Context, spList []*store.SkillProduct) error {
	if len(spList) == 0 {
		return nil
	}
	skillId := spList[0].SkillId
	uidList := make([]uint32, 0, len(spList))
	for _, item := range spList {
		uidList = append(uidList, item.Uid)
	}

	return m.cache.BatchIncrRecommendTimes(ctx, skillId, uidList, m.nowExposeCycleEndSecond())
}

func (m *Mgr) HandleRecommendLimit(ctx context.Context, spList []*store.SkillProduct) error {
	if len(spList) == 0 {
		return nil
	}
	skillId := spList[0].SkillId
	uidList := make([]uint32, 0, len(spList))
	for _, item := range spList {
		uidList = append(uidList, item.Uid)
	}

	timesMap, err := m.cache.BatchGetRecommendTimes(ctx, skillId, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleRecommendLimit.BatchGetRecommendTimes, skillId: %d, err: %v", skillId, err)
		return err
	}

	for _, item := range spList {
		if timesMap[item.Uid] > m.bc.GetRecommendValConfig().MaxExposeTimes {
			item.RecommendVal -= float64(m.bc.GetRecommendValConfig().MaxExposeReduce)
		}
	}

	return nil
}

var (
	userOnlineMap       = map[uint32]bool{}
	userOnlineBatchSize = 100
)

func (m *Mgr) UpdateUserOnline() {
	ctx, cancel := comctx.WithTimeout(10 * time.Second)
	defer cancel()

	startTime := time.Now()
	tmpUserOnlineMap := map[uint32]bool{}
	batchUid := make([]uint32, 0, userOnlineBatchSize)
	nowBatchCnt := 0
	totalCnt := 0
	coachUid := m.GetNowCoachUid()
	for _, uid := range coachUid {
		batchUid = append(batchUid, uint32(uid))
		nowBatchCnt++
		totalCnt++
		if nowBatchCnt == userOnlineBatchSize || totalCnt == len(coachUid) {
			cacheUserOnline, err := m.cache.BatchGetUserOnline(context.Background(), batchUid)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateUserOnline BatchGetUserOnline, err: %v", err)
				continue
			}
			for k, v := range cacheUserOnline {
				tmpUserOnlineMap[k] = v
			}
			batchUid = batchUid[:0]
			nowBatchCnt = 0
		}
	}

	userOnlineMap = tmpUserOnlineMap

	log.Infof("UpdateUserOnline success, cost: %dms", time.Now().Sub(startTime).Milliseconds())
}

func (m *Mgr) IsUserOnline(uid uint32) bool {
	return userOnlineMap[uid]
}

func (m *Mgr) GetOnlineUser() map[uint32]bool {
	userOnlineMapCopy := make(map[uint32]bool)
	for k, v := range userOnlineMap {
		userOnlineMapCopy[k] = v
	}
	return userOnlineMapCopy
}

var (
	userSexMap       = map[uint32]uint32{}
	userSexBatchSize = 100
)

func (m *Mgr) UpdateUserSex() {
	ctx, cancel := comctx.WithTimeout(10 * time.Second)
	defer cancel()

	startTime := time.Now()
	tmpUserSexMap := map[uint32]uint32{}
	batchUid := make([]uint32, 0, userSexBatchSize)
	nowBatchCnt := 0
	totalCnt := 0
	coachUid := m.GetNowCoachUid()
	for _, uid := range coachUid {
		batchUid = append(batchUid, uint32(uid))
		nowBatchCnt++
		totalCnt++
		if nowBatchCnt == userSexBatchSize || totalCnt == len(coachUid) {
			cacheUserSex, err := m.cache.BatchGetUserSex(ctx, batchUid)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateUserSex BatchGetUserSex, err: %v", err)
				continue
			}
			for u, sex := range cacheUserSex {
				tmpUserSexMap[u] = sex
			}
			batchUid = batchUid[:0]
			nowBatchCnt = 0
		}
	}

	userSexMap = tmpUserSexMap

	log.Infof("UpdateUserSex success, cost: %dms", time.Now().Sub(startTime).Milliseconds())
}

func (m *Mgr) GetUserSex(uid uint32) uint32 {
	return userSexMap[uid]
}

func (m *Mgr) ReportExposeCoach(ctx context.Context, request *pb.ReportExposeCoachRequest) (*pb.ReportExposeCoachResponse, error) {
	resp := &pb.ReportExposeCoachResponse{}

	// 防止太大批量, 限制最大100
	if len(request.GetExposeCoachList()) > 100 {
		request.ExposeCoachList = request.ExposeCoachList[:100]
	}

	switch request.GetExposeType() {
	case uint32(esport_logic.ReportExposeCoachRequest_EXPOSE_COACH_TYPE_ESPORT_KING_TAB):
		recommendTabConfig := m.bc.GetRecommendTabMapping()[request.GetGameId()]
		if recommendTabConfig == nil {
			recommendTabConfig = m.bc.GetRecommendTabConfig()
		}
		err := m.cache.AddUserExposedCoach(ctx, request.GetGameId(), request.GetUid(), request.GetExposeType(),
			recommendTabConfig.NoDupRepeatedPeriod, recommendTabConfig.NoDupRepeatedTimes, request.GetExposeCoachList())
		if err != nil {
			log.ErrorWithCtx(ctx, "ReportExposeCoach king tab expose, req: %+v, err: %v", request, err)
			return resp, err
		}
	case uint32(esport_logic.ReportExposeCoachRequest_EXPOSE_COACH_TYPE_ESPORT_UGC_CHANNEL):
		err := m.cache.AddUserExposedCoach(ctx, request.GetGameId(), request.GetUid(), request.GetExposeType(),
			m.bc.GetUgcRecommendConfig().NoDupTime, m.bc.GetUgcRecommendConfig().NoDupTimes, request.GetExposeCoachList())
		if err != nil {
			log.ErrorWithCtx(ctx, "ReportExposeCoach ugc channel expose, req: %+v, err: %v", request, err)
			return resp, err
		}
	case uint32(esport_logic.ReportExposeCoachRequest_EXPOSE_COACH_TYPE_ESPORT_HELPER_ACTIVITY):
		// 记录曝光过的大神
		err := m.cache.AddUserExposedCoach(ctx, request.GetGameId(), request.GetUid(), uint32(esport_logic.ReportExposeCoachRequest_EXPOSE_COACH_TYPE_ESPORT_HELPER_ACTIVITY),
			m.bc.GetRecommendValConfig().UnderlayTime, -1, request.GetExposeCoachList())
		if err != nil {
			log.ErrorWithCtx(ctx, "ReportExposeCoach helper activity expose, req: %+v, err: %v", request, err)
			return resp, err
		}
	case uint32(esport_logic.ReportExposeCoachRequest_EXPOSE_COACH_TYPE_ESPORT_GLOBAL_POPUP):
		recommendTabConfig := m.bc.GetRecommendTabMapping()[request.GetGameId()]
		if recommendTabConfig == nil {
			recommendTabConfig = m.bc.GetRecommendTabConfig()
		}
		err := m.cache.AddUserExposedCoach(ctx, request.GetGameId(), request.GetUid(), request.GetExposeType(),
			recommendTabConfig.NoDupRepeatedPeriod, recommendTabConfig.NoDupRepeatedTimes, request.GetExposeCoachList())
		if err != nil {
			log.ErrorWithCtx(ctx, "ReportExposeCoach global popup expose, req: %+v, err: %v", request, err)
			return resp, err
		}

	default:
		err := m.esportAreaExpose(ctx, request.GetUid(), request.GetGameId(), request.GetExposeCoachList())
		if err != nil {
			log.ErrorWithCtx(ctx, "ReportExposeCoach esportAreaExpose, req: %+v, err: %v", request, err)
			return resp, err
		}
		// 添加运营置顶的曝光记录
		go m.operationTopExpose(ctx, request.GetUid(), request.GetGameId(), request.GetExposeCoachList())
	}

	return resp, nil
}

// 电竞专区曝光
func (m *Mgr) esportAreaExpose(ctx context.Context, uid, gameId uint32, uidList []uint32) error {
	// 记录曝光过的大神
	err := m.cache.AddUserExposedCoach(ctx, gameId, uid, uint32(esport_logic.ReportExposeCoachRequest_EXPOSE_COACH_TYPE_ESPORT_AREA),
		m.bc.GetRecommendValConfig().UnderlayTime, -1, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "esportAreaExpose.AddUserExposedCoach, uid: %d, gameId: %d, uidList: %+v, err: %v", uid, gameId, uidList, err)
		return err
	}

	// 记录用于排序分数计算的曝光次数
	err = m.cache.BatchIncrRecommendTimes(ctx, gameId, uidList, m.nowExposeCycleEndSecond())
	if err != nil {
		log.ErrorWithCtx(ctx, "esportAreaExpose.BatchIncrRecommendTimes, uid: %d, gameId: %d, uidList: %+v, err: %v", uid, gameId, uidList, err)
	}

	// 曝光分级策略曝光标记
	for _, item := range uidList {
		err := m.cache.AddCoachExposeGradingCount(ctx, uid, item, gameId)
		if err != nil {
			log.ErrorWithCtx(ctx, "esportAreaExpose.AddCoachExposeGradingCount, uid: %d, gameId: %d, uidList: %+v, err: %v", uid, gameId, uidList, err)
		}
	}
	return err
}

// operationTopExpose 记录被运营置顶的大神的曝光记录
// 运营置顶的大神每天只能置顶一次（如果当天重新配置置顶，那么当天会再次置顶一次）
// 先查询出 coachUidList 中有哪些是运营置顶的
// 然后保存到 cache 中
func (m *Mgr) operationTopExpose(oldCtx context.Context, uid, gameId uint32, coachUidList []uint32) {
	// 1. 复制context
	ctx, cancelFunc := grpc.NewContextWithInfoTimeout(oldCtx, 10*time.Second)
	defer func() {
		cancelFunc()
		log.DebugWithCtx(ctx, "operationTopExpose, uid: %d, gameId: %d, coachUidList: %+v", uid, gameId, coachUidList)
	}()

	// 2. 查询库中该游戏配置的运营置顶的大神
	topCoachList, err := m.store.GetCoachRecommend(ctx, "", gameId, uint32(pb.RECOMMEND_TYPE_RECOMMEND_TYPE_EFFECT), 0, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "operationTopExpose GetOperationTopCoach, err: %v", err)
		return
	}
	if len(topCoachList) == 0 {
		log.DebugWithCtx(ctx, "operationTopExpose, gameId: %d, no top coach", gameId)
		return
	}

	// 4. 提取出运营置顶的大神uid，然后和coachUidList取交集
	topCoachUidList := make([]uint32, 0, len(topCoachList))
	for _, item := range topCoachList {
		topCoachUidList = append(topCoachUidList, item.Uid)
	}
	targetExposedCoachIds := list.Intersection(topCoachUidList, coachUidList)

	// 5. 保存到cache中
	err = m.cache.AddUserExposedTopCoach(ctx, uid, gameId, targetExposedCoachIds...)
	if err != nil {
		log.ErrorWithCtx(ctx, "operationTopExpose AddUserExposedTopCoach, err: %v", err)
		return
	}
}

func (m *Mgr) AddCoachRecommend(ctx context.Context, req *pb.AddCoachRecommendRequest) (*pb.AddCoachRecommendResponse, error) {
	resp := &pb.AddCoachRecommendResponse{}

	uid, _, err := m.rpcCli.AccountCli.GetUidByName(ctx, req.GetRecommendInfo().GetTtid())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCoachRecommend GetUidByName, req: %+v, err: %v", req, err)
		return resp, err
	}

	productList, tErr := m.findSkillProductByUid(ctx, uid)
	if tErr != nil {
		log.ErrorWithCtx(ctx, "AddCoachRecommend findSkillProductByUid, req: %+v, err: %v", req, tErr)
		return resp, tErr
	}
	hasGame := false
	for _, item := range productList {
		if item.SkillId == req.GetRecommendInfo().GetGameId() {
			hasGame = true
			break
		}
	}
	if !hasGame {
		return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "大神没有选择的游戏的权限")
	}

	sErr := m.store.AddCoachRecommend(ctx, &store.CoachRecommend{
		Uid:        uid,
		Ttid:       req.GetRecommendInfo().GetTtid(),
		GameId:     req.GetRecommendInfo().GetGameId(),
		CoachType:  req.GetRecommendInfo().GetCoachType(),
		Sort:       req.GetRecommendInfo().GetSort(),
		StartTime:  req.GetRecommendInfo().GetStartTime(),
		EndTime:    req.GetRecommendInfo().GetEndTime(),
		Deleted:    false,
		CreateTime: time.Now(),
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "AddCoachRecommend req: %+v, err: %v", req, err)
		return resp, err
	}

	return resp, nil
}

func (m *Mgr) GetCoachRecommend(ctx context.Context, req *pb.GetCoachRecommendRequest) (*pb.GetCoachRecommendResponse, error) {
	resp := &pb.GetCoachRecommendResponse{}

	if req.GetPageNum() > 0 {
		req.PageNum-- // 前端组件pagenum从1开始
	}

	if req.GetPageSize() == 0 {
		req.PageSize = 10
	}

	total, err := m.store.CntCoachRecommend(ctx, req.GetTtid(), req.GetGameId(), req.GetRecommendType(), req.GetStartTime(), req.GetEndTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCoachRecommend req: %+v, err: %v", req, err)
		return resp, err
	}
	resp.Total = uint32(total)

	data, err := m.store.GetCoachRecommend(ctx, req.GetTtid(), req.GetGameId(), req.GetRecommendType(), req.GetStartTime(),
		req.GetEndTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCoachRecommend req: %+v, err: %v", req, err)
		return resp, err
	}

	resp.RecommendList = toPbCoachRecommendList(data)

	sort.Slice(resp.RecommendList, func(i, j int) bool {
		if resp.RecommendList[i].GetRecommendType() == resp.RecommendList[j].GetRecommendType() {
			return resp.RecommendList[i].GetStartTime() < resp.RecommendList[j].GetStartTime()
		}
		return resp.RecommendList[i].GetRecommendType() < resp.RecommendList[j].GetRecommendType()
	})

	start := req.GetPageNum() * req.GetPageSize()
	if start > uint32(len(resp.RecommendList)) {
		start = uint32(len(resp.RecommendList))
	}
	end := start + req.GetPageSize()
	if end > uint32(len(resp.RecommendList)) {
		end = uint32(len(resp.RecommendList))
	}
	resp.RecommendList = resp.RecommendList[start:end]

	uidList := make([]uint32, 0, len(resp.RecommendList))
	for _, item := range resp.RecommendList {
		uidList = append(uidList, item.GetUid())
	}

	// 更新为实时nickname
	userProfileMap, err := m.rpcCli.UserProfileCli.BatchGetUserProfileV2(ctx, uidList, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCoachRecommend BatchGetUserProfileV2, req: %+v, err: %v", req, err)
		return resp, err
	}
	for _, item := range resp.RecommendList {
		item.Nickname = userProfileMap[item.GetUid()].GetNickname()
	}

	return resp, nil
}

func (m *Mgr) UpdateCoachRecommend(ctx context.Context, req *pb.UpdateCoachRecommendRequest) (*pb.UpdateCoachRecommendResponse, error) {
	resp := &pb.UpdateCoachRecommendResponse{}
	uid, _, err := m.rpcCli.AccountCli.GetUidByName(ctx, req.GetRecommendInfo().GetTtid())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateCoachRecommend GetUidByName, req: %+v, err: %v", req, err)
		return resp, err
	}

	sErr := m.store.UpdateCoachRecommend(ctx, req.GetRecommendInfo().GetId(), &store.CoachRecommend{
		Uid:       uid,
		Ttid:      req.GetRecommendInfo().GetTtid(),
		GameId:    req.GetRecommendInfo().GetGameId(),
		CoachType: req.GetRecommendInfo().GetCoachType(),
		Sort:      req.GetRecommendInfo().GetSort(),
		StartTime: req.GetRecommendInfo().GetStartTime(),
		EndTime:   req.GetRecommendInfo().GetEndTime(),
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "UpdateCoachRecommend req: %+v, err: %v", req, sErr)
		return resp, sErr
	}

	return resp, nil
}

func (m *Mgr) DeleteCoachRecommend(ctx context.Context, req *pb.DelCoachRecommendRequest) (*pb.DelCoachRecommendResponse, error) {
	resp := &pb.DelCoachRecommendResponse{}
	err := m.store.DelCoachRecommend(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteCoachRecommend req: %+v, err: %v", req, err)
	}
	return resp, err
}

func toPbCoachRecommendList(data []*store.CoachRecommend) []*pb.CoachRecommendInfo {
	rs := make([]*pb.CoachRecommendInfo, 0, len(data))
	for _, v := range data {
		rs = append(rs, toPbCoachRecommend(v))
	}
	return rs
}

func toPbCoachRecommend(coachRecommend *store.CoachRecommend) *pb.CoachRecommendInfo {
	return &pb.CoachRecommendInfo{
		Id:            coachRecommend.ID.Hex(),
		GameId:        coachRecommend.GameId,
		Ttid:          coachRecommend.Ttid,
		Nickname:      coachRecommend.NickName,
		CoachType:     coachRecommend.CoachType,
		Sort:          coachRecommend.Sort,
		StartTime:     coachRecommend.StartTime,
		EndTime:       coachRecommend.EndTime,
		RecommendType: getRecommendType(coachRecommend.StartTime, coachRecommend.EndTime),
		Uid:           coachRecommend.Uid,
	}
}

func getRecommendType(startTimestamp, endTimestamp uint32) uint32 {
	startTime := time.Unix(int64(startTimestamp), 0)
	endTime := time.Unix(int64(endTimestamp), 0)

	if startTime.Before(time.Now()) && endTime.After(time.Now()) {
		return uint32(pb.RECOMMEND_TYPE_RECOMMEND_TYPE_EFFECT)
	}

	if endTime.Before(time.Now()) {
		return uint32(pb.RECOMMEND_TYPE_RECOMMEND_TYPE_EXPIRE)
	}

	return uint32(pb.RECOMMEND_TYPE_RECOMMEND_TYPE_WAIT)
}

func (m *Mgr) GetOperationCoachRecommendWithoutExposed(ctx context.Context, opUid, gameId uint32) ([]*store.SimpleProduct, error) {
	coachRecommend, err := m.getCoachRecommendWithCache(ctx, gameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getOperationCoachRecommendWithoutExposed getCoachRecommendWithCache, err: %v", err)
		return make([]*store.SimpleProduct, 0), err
	}
	if len(coachRecommend) == 0 {
		log.DebugWithCtx(ctx, "getOperationCoachRecommendWithoutExposed, opUid:%d, gameId: %d, no coachRecommend", opUid, gameId)
		return make([]*store.SimpleProduct, 0), nil
	}

	if opUid != 0 {
		// 过滤单日已经曝光过的
		exposedTopCoach, err := m.cache.GetUserExposedTopCoach(ctx, opUid, gameId)
		if err != nil {
			log.ErrorWithCtx(ctx, "getOperationCoachRecommend GetUserExposedTopCoach, err: %v", err)
		} else {
			log.DebugWithCtx(ctx, "getOperationCoachRecommend, gameId: %d, exposedTopCoach: %+v", gameId, exposedTopCoach)
			validList := make([]*store.CoachRecommend, 0, len(coachRecommend))
			for _, item := range coachRecommend {
				// 判断是否已经曝光过：1、有曝光记录；2、曝光记录的曝光时间比记录创建时间晚 3、自己
				if exposedTopCoach[item.Uid] > item.CreateTime.Unix() || item.Uid == opUid {
					log.DebugWithCtx(ctx, "getOperationCoachRecommend, gameId: %d, uid: %d, exposedTopCoach: %d, createTime: %d", gameId, item.Uid, exposedTopCoach[item.Uid], item.CreateTime.Unix())
					continue
				}
				validList = append(validList, item)

			}
			coachRecommend = validList
		}
	}

	if len(coachRecommend) == 0 {
		log.DebugWithCtx(ctx, "getOperationCoachRecommendWithoutExposed, opUid:%d, gameId: %d, no coachRecommend", opUid, gameId)
		return make([]*store.SimpleProduct, 0), nil
	}

	sort.Slice(coachRecommend, func(i, j int) bool {
		return coachRecommend[i].Sort < coachRecommend[j].Sort
	})

	uidList := make([]uint32, 0, len(coachRecommend))
	for _, item := range coachRecommend {
		uidList = append(uidList, item.Uid)
	}
	isOrderMap, err := m.batchIsReceiveOrderTime(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "getOperationCoachRecommendWithoutExposed batchGetUsersSettingWithCache,optUid: %d, err: %v", opUid, err)
		return nil, err
	}

	ts := time.Now()
	skillProductMap, err := m.batchFindSkillProductByUidWithCache(ctx, uidList, gameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getOperationCoachRecommendWithoutExposed batchFindSkillProductByUidWithCache, err: %v", err)
		return nil, err
	}
	log.DebugWithCtx(ctx, "getOperationCoachRecommendWithoutExposed batchFindSkillProductByUidWithCache, cost: %dms", time.Since(ts).Milliseconds())

	skillProductIdList := make([]*store.SimpleProduct, 0, len(coachRecommend))
	happenedUid := make(map[uint32]bool)
	for _, item := range coachRecommend {
		if happenedUid[item.Uid] { // 按最优先推荐条目确定排序,后续再出现不管
			continue
		}
		if isReceive := isOrderMap[item.Uid]; !isReceive {
			log.InfoWithCtx(ctx, "getOperationCoachRecommendWithoutExposed.isReceiveOrderTime, uid: %d", item.Uid)
			continue
		}

		if product, ok := skillProductMap[item.Uid]; ok {
			happenedUid[item.Uid] = true
			skillProductIdList = append(skillProductIdList, &store.SimpleProduct{
				Uid:       item.Uid,
				ProductId: product.ProductId,
			})
		}

	}
	return skillProductIdList, nil
}

func (m *Mgr) getCoachRecommendWithCache(ctx context.Context, gameId uint32) ([]*store.CoachRecommend, error) {
	// 1、get from cache
	coachRecommend, err := m.cache.GetCoachRecommendInEffect(ctx, gameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getOperationCoachRecommendWithoutExposed cache.GetCoachRecommendInEffect, err: %v", err)
		coachRecommend, err := m.store.GetCoachRecommend(ctx, "", gameId, uint32(pb.RECOMMEND_TYPE_RECOMMEND_TYPE_EFFECT), 0, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "getOperationCoachRecommendWithoutExposed err: %v", err)
			return nil, err
		}
		_ = m.cache.SetCoachRecommendInEffect(ctx, gameId, coachRecommend)
		log.DebugWithCtx(ctx, "getOperationCoachRecommendWithoutExposed get from store, gameId: %d, coachRecommend: %+v", gameId, coachRecommend)
		return coachRecommend, nil
	} else {
		log.DebugWithCtx(ctx, "getOperationCoachRecommendWithoutExposed get from cache, gameId: %d, coachRecommend: %+v", gameId, coachRecommend)
		return coachRecommend, nil
	}

}

func (m *Mgr) getOperationCoachRecommend(ctx context.Context, opUid uint32, gameId uint32) []*store.SkillProduct {
	coachRecommend, err := m.store.GetCoachRecommend(ctx, "", gameId, uint32(pb.RECOMMEND_TYPE_RECOMMEND_TYPE_EFFECT), 0, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "getOperationCoachRecommend err: %v", err)
		return nil
	}
	if len(coachRecommend) == 0 {
		return make([]*store.SkillProduct, 0)
	}
	if opUid != 0 {
		// 过滤单日已经曝光过的
		exposedTopCoach, err := m.cache.GetUserExposedTopCoach(ctx, opUid, gameId)
		if err != nil {
			log.ErrorWithCtx(ctx, "getOperationCoachRecommend GetUserExposedTopCoach, err: %v", err)
		} else {
			log.DebugWithCtx(ctx, "getOperationCoachRecommend, gameId: %d, exposedTopCoach: %+v", gameId, exposedTopCoach)
			validList := make([]*store.CoachRecommend, 0, len(coachRecommend))
			for _, item := range coachRecommend {
				// 判断是否已经曝光过：1、有曝光记录；2、曝光记录的曝光时间比记录创建时间晚
				if exposedTopCoach[item.Uid] > item.CreateTime.Unix() {
					log.DebugWithCtx(ctx, "getOperationCoachRecommend, gameId: %d, uid: %d, exposedTopCoach: %d, createTime: %d", gameId, item.Uid, exposedTopCoach[item.Uid], item.CreateTime.Unix())
					continue
				}
				validList = append(validList, item)
			}
			coachRecommend = validList
		}
	}

	sort.Slice(coachRecommend, func(i, j int) bool {
		return coachRecommend[i].Sort < coachRecommend[j].Sort
	})

	uidList := make([]uint32, 0, len(coachRecommend))
	for _, item := range coachRecommend {
		uidList = append(uidList, item.Uid)
	}
	isReceiveMap, err := m.batchIsReceiveOrderTime(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "getOperationCoachRecommend batchGetUsersSettingWithCache,optUid: %d, err: %v", opUid, err)
		return nil
	}
	happenedUid := make(map[uint32]bool)
	recommendProductList := make([]*store.SkillProduct, 0, len(coachRecommend))
	ts := time.Now()
	for _, item := range coachRecommend {
		if happenedUid[item.Uid] { // 按最优先推荐条目确定排序,后续再出现不管
			continue
		}
		if isReceive := isReceiveMap[item.Uid]; !isReceive {
			log.InfoWithCtx(ctx, "getOperationCoachRecommend.isReceiveOrderTime, uid: %d", item.Uid)
			continue
		}
		productList, err := m.findSkillProductByUid(ctx, item.Uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "getOperationCoachRecommend err: %v", err)
			return nil
		}
		for _, pItem := range productList {
			if pItem.SkillId == gameId && pItem.Switch {
				recommendProductList = append(recommendProductList, pItem)
				happenedUid[item.Uid] = true
				break
			}
		}
	}
	log.DebugWithCtx(ctx, "getOperationCoachRecommend batchFindSkillProductByUidWithCache, cost: %dms", time.Since(ts).Milliseconds())
	m.updateSameGamePriceBatch(ctx, recommendProductList)
	return recommendProductList
}

type recommendOldCoachList struct {
	data map[uint32][]store.SkillProduct
}

var (
	localRecommendOldCoachList = recommendOldCoachList{data: make(map[uint32][]store.SkillProduct)}
)

func (r *recommendOldCoachList) update(data map[uint32][]store.SkillProduct) {
	r.data = data
}

func (r *recommendOldCoachList) get(skillId uint32) []store.SkillProduct {
	dataCopy := make([]store.SkillProduct, 0, len(r.data[skillId]))
	for _, item := range r.data[skillId] {
		dataCopy = append(dataCopy, item)
	}
	return dataCopy
}

func (m *Mgr) UpdateLocalRecommendCoachList() {
	ctx, cancel := comctx.WithTimeout(10 * time.Second)
	defer cancel()

	startTime := time.Now()
	tmpRecommendOldCoachList := make(map[uint32][]store.SkillProduct)
	skillProductList, err := m.store.FindSkillProductByUids(ctx, m.bc.GetRecommendValConfig().RecommendOldCoachUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateLocalRecommendCoachList FindSkillProductByUids, err: %v", err)
		return
	}
	for _, item := range skillProductList {
		tmpRecommendOldCoachList[item.SkillId] = append(tmpRecommendOldCoachList[item.SkillId], *item)
	}

	localRecommendOldCoachList.update(tmpRecommendOldCoachList)

	log.Infof("UpdateLocalRecommendCoachList success, cost: %dms", time.Now().Sub(startTime).Milliseconds())
}
