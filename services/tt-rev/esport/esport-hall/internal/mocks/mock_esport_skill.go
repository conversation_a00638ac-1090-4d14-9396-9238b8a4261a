// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/esport-skill (interfaces: EsportSkillClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	esport_skill "golang.52tt.com/protocol/services/esport-skill"
	grpc "google.golang.org/grpc"
)

// MockEsportSkillClient is a mock of EsportSkillClient interface.
type MockEsportSkillClient struct {
	ctrl     *gomock.Controller
	recorder *MockEsportSkillClientMockRecorder
}

// MockEsportSkillClientMockRecorder is the mock recorder for MockEsportSkillClient.
type MockEsportSkillClientMockRecorder struct {
	mock *MockEsportSkillClient
}

// NewMockEsportSkillClient creates a new mock instance.
func NewMockEsportSkillClient(ctrl *gomock.Controller) *MockEsportSkillClient {
	mock := &MockEsportSkillClient{ctrl: ctrl}
	mock.recorder = &MockEsportSkillClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEsportSkillClient) EXPECT() *MockEsportSkillClientMockRecorder {
	return m.recorder
}

// AddEsportGameConfig mocks base method.
func (m *MockEsportSkillClient) AddEsportGameConfig(arg0 context.Context, arg1 *esport_skill.AddEsportGameConfigRequest, arg2 ...grpc.CallOption) (*esport_skill.AddEsportGameConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddEsportGameConfig", varargs...)
	ret0, _ := ret[0].(*esport_skill.AddEsportGameConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddEsportGameConfig indicates an expected call of AddEsportGameConfig.
func (mr *MockEsportSkillClientMockRecorder) AddEsportGameConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddEsportGameConfig", reflect.TypeOf((*MockEsportSkillClient)(nil).AddEsportGameConfig), varargs...)
}

// AddRenownedPlayer mocks base method.
func (m *MockEsportSkillClient) AddRenownedPlayer(arg0 context.Context, arg1 *esport_skill.AddRenownedPlayerRequest, arg2 ...grpc.CallOption) (*esport_skill.AddRenownedPlayerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddRenownedPlayer", varargs...)
	ret0, _ := ret[0].(*esport_skill.AddRenownedPlayerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddRenownedPlayer indicates an expected call of AddRenownedPlayer.
func (mr *MockEsportSkillClientMockRecorder) AddRenownedPlayer(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRenownedPlayer", reflect.TypeOf((*MockEsportSkillClient)(nil).AddRenownedPlayer), varargs...)
}

// AddUserAuditSkill mocks base method.
func (m *MockEsportSkillClient) AddUserAuditSkill(arg0 context.Context, arg1 *esport_skill.AddUserAuditSkillRequest, arg2 ...grpc.CallOption) (*esport_skill.AddUserAuditSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddUserAuditSkill", varargs...)
	ret0, _ := ret[0].(*esport_skill.AddUserAuditSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserAuditSkill indicates an expected call of AddUserAuditSkill.
func (mr *MockEsportSkillClientMockRecorder) AddUserAuditSkill(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserAuditSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).AddUserAuditSkill), varargs...)
}

// BatGetUserSkillFreezeStatus mocks base method.
func (m *MockEsportSkillClient) BatGetUserSkillFreezeStatus(arg0 context.Context, arg1 *esport_skill.BatGetUserSkillFreezeStatusRequest, arg2 ...grpc.CallOption) (*esport_skill.BatGetUserSkillFreezeStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetUserSkillFreezeStatus", varargs...)
	ret0, _ := ret[0].(*esport_skill.BatGetUserSkillFreezeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetUserSkillFreezeStatus indicates an expected call of BatGetUserSkillFreezeStatus.
func (mr *MockEsportSkillClientMockRecorder) BatGetUserSkillFreezeStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetUserSkillFreezeStatus", reflect.TypeOf((*MockEsportSkillClient)(nil).BatGetUserSkillFreezeStatus), varargs...)
}

// BatchCheckCoachHasGame mocks base method.
func (m *MockEsportSkillClient) BatchCheckCoachHasGame(arg0 context.Context, arg1 *esport_skill.BatchCheckCoachHasGameRequest, arg2 ...grpc.CallOption) (*esport_skill.BatchCheckCoachHasGameResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchCheckCoachHasGame", varargs...)
	ret0, _ := ret[0].(*esport_skill.BatchCheckCoachHasGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckCoachHasGame indicates an expected call of BatchCheckCoachHasGame.
func (mr *MockEsportSkillClientMockRecorder) BatchCheckCoachHasGame(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckCoachHasGame", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchCheckCoachHasGame), varargs...)
}

// BatchGetAuditSkill mocks base method.
func (m *MockEsportSkillClient) BatchGetAuditSkill(arg0 context.Context, arg1 *esport_skill.BatchGetAuditSkillRequest, arg2 ...grpc.CallOption) (*esport_skill.BatchGetAuditSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetAuditSkill", varargs...)
	ret0, _ := ret[0].(*esport_skill.BatchGetAuditSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAuditSkill indicates an expected call of BatchGetAuditSkill.
func (mr *MockEsportSkillClientMockRecorder) BatchGetAuditSkill(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAuditSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchGetAuditSkill), varargs...)
}

// BatchGetCoachLabelsForGame mocks base method.
func (m *MockEsportSkillClient) BatchGetCoachLabelsForGame(arg0 context.Context, arg1 *esport_skill.BatchGetCoachLabelsForGameRequest, arg2 ...grpc.CallOption) (*esport_skill.BatchGetCoachLabelsForGameResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetCoachLabelsForGame", varargs...)
	ret0, _ := ret[0].(*esport_skill.BatchGetCoachLabelsForGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCoachLabelsForGame indicates an expected call of BatchGetCoachLabelsForGame.
func (mr *MockEsportSkillClientMockRecorder) BatchGetCoachLabelsForGame(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCoachLabelsForGame", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchGetCoachLabelsForGame), varargs...)
}

// BatchGetGameInfoByNames mocks base method.
func (m *MockEsportSkillClient) BatchGetGameInfoByNames(arg0 context.Context, arg1 *esport_skill.BatchGetGameInfoByNamesRequest, arg2 ...grpc.CallOption) (*esport_skill.BatchGetGameInfoByNamesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetGameInfoByNames", varargs...)
	ret0, _ := ret[0].(*esport_skill.BatchGetGameInfoByNamesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGameInfoByNames indicates an expected call of BatchGetGameInfoByNames.
func (mr *MockEsportSkillClientMockRecorder) BatchGetGameInfoByNames(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGameInfoByNames", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchGetGameInfoByNames), varargs...)
}

// BatchGetUserCurrentSkill mocks base method.
func (m *MockEsportSkillClient) BatchGetUserCurrentSkill(arg0 context.Context, arg1 *esport_skill.BatchGetUserCurrentSkillRequest, arg2 ...grpc.CallOption) (*esport_skill.BatchGetUserCurrentSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserCurrentSkill", varargs...)
	ret0, _ := ret[0].(*esport_skill.BatchGetUserCurrentSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserCurrentSkill indicates an expected call of BatchGetUserCurrentSkill.
func (mr *MockEsportSkillClientMockRecorder) BatchGetUserCurrentSkill(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserCurrentSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchGetUserCurrentSkill), varargs...)
}

// BatchGetUserRenownedInfo mocks base method.
func (m *MockEsportSkillClient) BatchGetUserRenownedInfo(arg0 context.Context, arg1 *esport_skill.BatchGetUserRenownedInfoRequest, arg2 ...grpc.CallOption) (*esport_skill.BatchGetUserRenownedInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserRenownedInfo", varargs...)
	ret0, _ := ret[0].(*esport_skill.BatchGetUserRenownedInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserRenownedInfo indicates an expected call of BatchGetUserRenownedInfo.
func (mr *MockEsportSkillClientMockRecorder) BatchGetUserRenownedInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserRenownedInfo", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchGetUserRenownedInfo), varargs...)
}

// BatchRemoveRenownedPlayers mocks base method.
func (m *MockEsportSkillClient) BatchRemoveRenownedPlayers(arg0 context.Context, arg1 *esport_skill.BatchRemoveRenownedPlayersRequest, arg2 ...grpc.CallOption) (*esport_skill.BatchRemoveRenownedPlayersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchRemoveRenownedPlayers", varargs...)
	ret0, _ := ret[0].(*esport_skill.BatchRemoveRenownedPlayersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchRemoveRenownedPlayers indicates an expected call of BatchRemoveRenownedPlayers.
func (mr *MockEsportSkillClientMockRecorder) BatchRemoveRenownedPlayers(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRemoveRenownedPlayers", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchRemoveRenownedPlayers), varargs...)
}

// CalculatePrice mocks base method.
func (m *MockEsportSkillClient) CalculatePrice(arg0 context.Context, arg1 *esport_skill.CalculatePriceRequest, arg2 ...grpc.CallOption) (*esport_skill.CalculatePriceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CalculatePrice", varargs...)
	ret0, _ := ret[0].(*esport_skill.CalculatePriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculatePrice indicates an expected call of CalculatePrice.
func (mr *MockEsportSkillClientMockRecorder) CalculatePrice(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePrice", reflect.TypeOf((*MockEsportSkillClient)(nil).CalculatePrice), varargs...)
}

// CheckCoachIdentityAndSkill mocks base method.
func (m *MockEsportSkillClient) CheckCoachIdentityAndSkill(arg0 context.Context, arg1 *esport_skill.CheckCoachIdentityAndSkillRequest, arg2 ...grpc.CallOption) (*esport_skill.CheckCoachIdentityAndSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckCoachIdentityAndSkill", varargs...)
	ret0, _ := ret[0].(*esport_skill.CheckCoachIdentityAndSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCoachIdentityAndSkill indicates an expected call of CheckCoachIdentityAndSkill.
func (mr *MockEsportSkillClientMockRecorder) CheckCoachIdentityAndSkill(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCoachIdentityAndSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).CheckCoachIdentityAndSkill), varargs...)
}

// CreateLabel mocks base method.
func (m *MockEsportSkillClient) CreateLabel(arg0 context.Context, arg1 *esport_skill.CreateLabelRequest, arg2 ...grpc.CallOption) (*esport_skill.CreateLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateLabel", varargs...)
	ret0, _ := ret[0].(*esport_skill.CreateLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLabel indicates an expected call of CreateLabel.
func (mr *MockEsportSkillClientMockRecorder) CreateLabel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLabel", reflect.TypeOf((*MockEsportSkillClient)(nil).CreateLabel), varargs...)
}

// DelUserSkill mocks base method.
func (m *MockEsportSkillClient) DelUserSkill(arg0 context.Context, arg1 *esport_skill.DelUserSkillRequest, arg2 ...grpc.CallOption) (*esport_skill.DelUserSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelUserSkill", varargs...)
	ret0, _ := ret[0].(*esport_skill.DelUserSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelUserSkill indicates an expected call of DelUserSkill.
func (mr *MockEsportSkillClientMockRecorder) DelUserSkill(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).DelUserSkill), varargs...)
}

// DeleteEsportGameConfig mocks base method.
func (m *MockEsportSkillClient) DeleteEsportGameConfig(arg0 context.Context, arg1 *esport_skill.DeleteEsportGameConfigRequest, arg2 ...grpc.CallOption) (*esport_skill.DeleteEsportGameConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteEsportGameConfig", varargs...)
	ret0, _ := ret[0].(*esport_skill.DeleteEsportGameConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteEsportGameConfig indicates an expected call of DeleteEsportGameConfig.
func (mr *MockEsportSkillClientMockRecorder) DeleteEsportGameConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEsportGameConfig", reflect.TypeOf((*MockEsportSkillClient)(nil).DeleteEsportGameConfig), varargs...)
}

// DeleteLabel mocks base method.
func (m *MockEsportSkillClient) DeleteLabel(arg0 context.Context, arg1 *esport_skill.DeleteLabelRequest, arg2 ...grpc.CallOption) (*esport_skill.DeleteLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteLabel", varargs...)
	ret0, _ := ret[0].(*esport_skill.DeleteLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteLabel indicates an expected call of DeleteLabel.
func (mr *MockEsportSkillClientMockRecorder) DeleteLabel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLabel", reflect.TypeOf((*MockEsportSkillClient)(nil).DeleteLabel), varargs...)
}

// EditLabel mocks base method.
func (m *MockEsportSkillClient) EditLabel(arg0 context.Context, arg1 *esport_skill.EditLabelRequest, arg2 ...grpc.CallOption) (*esport_skill.EditLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EditLabel", varargs...)
	ret0, _ := ret[0].(*esport_skill.EditLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EditLabel indicates an expected call of EditLabel.
func (mr *MockEsportSkillClientMockRecorder) EditLabel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditLabel", reflect.TypeOf((*MockEsportSkillClient)(nil).EditLabel), varargs...)
}

// FreezeCoachSkill mocks base method.
func (m *MockEsportSkillClient) FreezeCoachSkill(arg0 context.Context, arg1 *esport_skill.FreezeCoachSkillRequest, arg2 ...grpc.CallOption) (*esport_skill.FreezeCoachSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FreezeCoachSkill", varargs...)
	ret0, _ := ret[0].(*esport_skill.FreezeCoachSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreezeCoachSkill indicates an expected call of FreezeCoachSkill.
func (mr *MockEsportSkillClientMockRecorder) FreezeCoachSkill(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreezeCoachSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).FreezeCoachSkill), varargs...)
}

// GetAllGameCardConfig mocks base method.
func (m *MockEsportSkillClient) GetAllGameCardConfig(arg0 context.Context, arg1 *esport_skill.GetAllGameCardConfigRequest, arg2 ...grpc.CallOption) (*esport_skill.GetAllGameCardConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllGameCardConfig", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetAllGameCardConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllGameCardConfig indicates an expected call of GetAllGameCardConfig.
func (mr *MockEsportSkillClientMockRecorder) GetAllGameCardConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllGameCardConfig", reflect.TypeOf((*MockEsportSkillClient)(nil).GetAllGameCardConfig), varargs...)
}

// GetAllGameSimpleInfo mocks base method.
func (m *MockEsportSkillClient) GetAllGameSimpleInfo(arg0 context.Context, arg1 *esport_skill.GetAllGameSimpleInfoRequest, arg2 ...grpc.CallOption) (*esport_skill.GetAllGameSimpleInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllGameSimpleInfo", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetAllGameSimpleInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllGameSimpleInfo indicates an expected call of GetAllGameSimpleInfo.
func (mr *MockEsportSkillClientMockRecorder) GetAllGameSimpleInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllGameSimpleInfo", reflect.TypeOf((*MockEsportSkillClient)(nil).GetAllGameSimpleInfo), varargs...)
}

// GetBasePriceSetting mocks base method.
func (m *MockEsportSkillClient) GetBasePriceSetting(arg0 context.Context, arg1 *esport_skill.GetBasePriceSettingRequest, arg2 ...grpc.CallOption) (*esport_skill.GetBasePriceSettingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBasePriceSetting", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetBasePriceSettingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBasePriceSetting indicates an expected call of GetBasePriceSetting.
func (mr *MockEsportSkillClientMockRecorder) GetBasePriceSetting(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBasePriceSetting", reflect.TypeOf((*MockEsportSkillClient)(nil).GetBasePriceSetting), varargs...)
}

// GetCoachApplicableLabels mocks base method.
func (m *MockEsportSkillClient) GetCoachApplicableLabels(arg0 context.Context, arg1 *esport_skill.GetCoachApplicableLabelsRequest, arg2 ...grpc.CallOption) (*esport_skill.GetCoachApplicableLabelsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCoachApplicableLabels", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetCoachApplicableLabelsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachApplicableLabels indicates an expected call of GetCoachApplicableLabels.
func (mr *MockEsportSkillClientMockRecorder) GetCoachApplicableLabels(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachApplicableLabels", reflect.TypeOf((*MockEsportSkillClient)(nil).GetCoachApplicableLabels), varargs...)
}

// GetEsportGameConfigListByPage mocks base method.
func (m *MockEsportSkillClient) GetEsportGameConfigListByPage(arg0 context.Context, arg1 *esport_skill.GetEsportGameConfigListByPageRequest, arg2 ...grpc.CallOption) (*esport_skill.GetEsportGameConfigListByPageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEsportGameConfigListByPage", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetEsportGameConfigListByPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEsportGameConfigListByPage indicates an expected call of GetEsportGameConfigListByPage.
func (mr *MockEsportSkillClientMockRecorder) GetEsportGameConfigListByPage(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGameConfigListByPage", reflect.TypeOf((*MockEsportSkillClient)(nil).GetEsportGameConfigListByPage), varargs...)
}

// GetGameDetailById mocks base method.
func (m *MockEsportSkillClient) GetGameDetailById(arg0 context.Context, arg1 *esport_skill.GetGameDetailByIdRequest, arg2 ...grpc.CallOption) (*esport_skill.GetGameDetailByIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameDetailById", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetGameDetailByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameDetailById indicates an expected call of GetGameDetailById.
func (mr *MockEsportSkillClientMockRecorder) GetGameDetailById(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameDetailById", reflect.TypeOf((*MockEsportSkillClient)(nil).GetGameDetailById), varargs...)
}

// GetGameDetailByIds mocks base method.
func (m *MockEsportSkillClient) GetGameDetailByIds(arg0 context.Context, arg1 *esport_skill.GetGameDetailByIdsRequest, arg2 ...grpc.CallOption) (*esport_skill.GetGameDetailByIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameDetailByIds", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetGameDetailByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameDetailByIds indicates an expected call of GetGameDetailByIds.
func (mr *MockEsportSkillClientMockRecorder) GetGameDetailByIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameDetailByIds", reflect.TypeOf((*MockEsportSkillClient)(nil).GetGameDetailByIds), varargs...)
}

// GetGameList mocks base method.
func (m *MockEsportSkillClient) GetGameList(arg0 context.Context, arg1 *esport_skill.GetGameListRequest, arg2 ...grpc.CallOption) (*esport_skill.GetGameListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameList", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetGameListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameList indicates an expected call of GetGameList.
func (mr *MockEsportSkillClientMockRecorder) GetGameList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameList", reflect.TypeOf((*MockEsportSkillClient)(nil).GetGameList), varargs...)
}

// GetMinimumPrice mocks base method.
func (m *MockEsportSkillClient) GetMinimumPrice(arg0 context.Context, arg1 *esport_skill.GetMinimumPriceRequest, arg2 ...grpc.CallOption) (*esport_skill.GetMinimumPriceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMinimumPrice", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetMinimumPriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinimumPrice indicates an expected call of GetMinimumPrice.
func (mr *MockEsportSkillClientMockRecorder) GetMinimumPrice(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinimumPrice", reflect.TypeOf((*MockEsportSkillClient)(nil).GetMinimumPrice), varargs...)
}

// GetSkillFreezeOperationList mocks base method.
func (m *MockEsportSkillClient) GetSkillFreezeOperationList(arg0 context.Context, arg1 *esport_skill.GetSkillFreezeOperationListRequest, arg2 ...grpc.CallOption) (*esport_skill.GetSkillFreezeOperationListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSkillFreezeOperationList", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetSkillFreezeOperationListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSkillFreezeOperationList indicates an expected call of GetSkillFreezeOperationList.
func (mr *MockEsportSkillClientMockRecorder) GetSkillFreezeOperationList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSkillFreezeOperationList", reflect.TypeOf((*MockEsportSkillClient)(nil).GetSkillFreezeOperationList), varargs...)
}

// GetSwitch mocks base method.
func (m *MockEsportSkillClient) GetSwitch(arg0 context.Context, arg1 *esport_skill.GetSwitchRequest, arg2 ...grpc.CallOption) (*esport_skill.GetSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSwitch", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSwitch indicates an expected call of GetSwitch.
func (mr *MockEsportSkillClientMockRecorder) GetSwitch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSwitch", reflect.TypeOf((*MockEsportSkillClient)(nil).GetSwitch), varargs...)
}

// GetTopGameList mocks base method.
func (m *MockEsportSkillClient) GetTopGameList(arg0 context.Context, arg1 *esport_skill.GetTopGameListRequest, arg2 ...grpc.CallOption) (*esport_skill.GetTopGameListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTopGameList", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetTopGameListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTopGameList indicates an expected call of GetTopGameList.
func (mr *MockEsportSkillClientMockRecorder) GetTopGameList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopGameList", reflect.TypeOf((*MockEsportSkillClient)(nil).GetTopGameList), varargs...)
}

// GetUserAuditSkill mocks base method.
func (m *MockEsportSkillClient) GetUserAuditSkill(arg0 context.Context, arg1 *esport_skill.GetUserAuditSkillRequest, arg2 ...grpc.CallOption) (*esport_skill.GetUserAuditSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserAuditSkill", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetUserAuditSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAuditSkill indicates an expected call of GetUserAuditSkill.
func (mr *MockEsportSkillClientMockRecorder) GetUserAuditSkill(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAuditSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).GetUserAuditSkill), varargs...)
}

// GetUserCurrentSkill mocks base method.
func (m *MockEsportSkillClient) GetUserCurrentSkill(arg0 context.Context, arg1 *esport_skill.GetUserCurrentSkillRequest, arg2 ...grpc.CallOption) (*esport_skill.GetUserCurrentSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserCurrentSkill", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetUserCurrentSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserCurrentSkill indicates an expected call of GetUserCurrentSkill.
func (mr *MockEsportSkillClientMockRecorder) GetUserCurrentSkill(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCurrentSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).GetUserCurrentSkill), varargs...)
}

// GetUserSkillByGameId mocks base method.
func (m *MockEsportSkillClient) GetUserSkillByGameId(arg0 context.Context, arg1 *esport_skill.GetUserSkillByGameIdRequest, arg2 ...grpc.CallOption) (*esport_skill.GetUserSkillByGameIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserSkillByGameId", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetUserSkillByGameIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillByGameId indicates an expected call of GetUserSkillByGameId.
func (mr *MockEsportSkillClientMockRecorder) GetUserSkillByGameId(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillByGameId", reflect.TypeOf((*MockEsportSkillClient)(nil).GetUserSkillByGameId), varargs...)
}

// GetUserSkillFreezeStatus mocks base method.
func (m *MockEsportSkillClient) GetUserSkillFreezeStatus(arg0 context.Context, arg1 *esport_skill.GetUserSkillFreezeStatusRequest, arg2 ...grpc.CallOption) (*esport_skill.GetUserSkillFreezeStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserSkillFreezeStatus", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetUserSkillFreezeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillFreezeStatus indicates an expected call of GetUserSkillFreezeStatus.
func (mr *MockEsportSkillClientMockRecorder) GetUserSkillFreezeStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillFreezeStatus", reflect.TypeOf((*MockEsportSkillClient)(nil).GetUserSkillFreezeStatus), varargs...)
}

// GetUserSkillStatus mocks base method.
func (m *MockEsportSkillClient) GetUserSkillStatus(arg0 context.Context, arg1 *esport_skill.GetUserSkillStatusRequest, arg2 ...grpc.CallOption) (*esport_skill.GetUserSkillStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserSkillStatus", varargs...)
	ret0, _ := ret[0].(*esport_skill.GetUserSkillStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillStatus indicates an expected call of GetUserSkillStatus.
func (mr *MockEsportSkillClientMockRecorder) GetUserSkillStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillStatus", reflect.TypeOf((*MockEsportSkillClient)(nil).GetUserSkillStatus), varargs...)
}

// HandleGameUpdate mocks base method.
func (m *MockEsportSkillClient) HandleGameUpdate(arg0 context.Context, arg1 *esport_skill.HandleGameUpdateRequest, arg2 ...grpc.CallOption) (*esport_skill.HandleGameUpdateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleGameUpdate", varargs...)
	ret0, _ := ret[0].(*esport_skill.HandleGameUpdateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleGameUpdate indicates an expected call of HandleGameUpdate.
func (mr *MockEsportSkillClientMockRecorder) HandleGameUpdate(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleGameUpdate", reflect.TypeOf((*MockEsportSkillClient)(nil).HandleGameUpdate), varargs...)
}

// IssueLabel mocks base method.
func (m *MockEsportSkillClient) IssueLabel(arg0 context.Context, arg1 *esport_skill.IssueLabelRequest, arg2 ...grpc.CallOption) (*esport_skill.IssueLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IssueLabel", varargs...)
	ret0, _ := ret[0].(*esport_skill.IssueLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IssueLabel indicates an expected call of IssueLabel.
func (mr *MockEsportSkillClientMockRecorder) IssueLabel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IssueLabel", reflect.TypeOf((*MockEsportSkillClient)(nil).IssueLabel), varargs...)
}

// ListLabels mocks base method.
func (m *MockEsportSkillClient) ListLabels(arg0 context.Context, arg1 *esport_skill.ListLabelsRequest, arg2 ...grpc.CallOption) (*esport_skill.ListLabelsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListLabels", varargs...)
	ret0, _ := ret[0].(*esport_skill.ListLabelsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListLabels indicates an expected call of ListLabels.
func (mr *MockEsportSkillClientMockRecorder) ListLabels(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListLabels", reflect.TypeOf((*MockEsportSkillClient)(nil).ListLabels), varargs...)
}

// ListRenownedPlayers mocks base method.
func (m *MockEsportSkillClient) ListRenownedPlayers(arg0 context.Context, arg1 *esport_skill.ListRenownedPlayersRequest, arg2 ...grpc.CallOption) (*esport_skill.ListRenownedPlayersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListRenownedPlayers", varargs...)
	ret0, _ := ret[0].(*esport_skill.ListRenownedPlayersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRenownedPlayers indicates an expected call of ListRenownedPlayers.
func (mr *MockEsportSkillClientMockRecorder) ListRenownedPlayers(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRenownedPlayers", reflect.TypeOf((*MockEsportSkillClient)(nil).ListRenownedPlayers), varargs...)
}

// ModifyUserSkill mocks base method.
func (m *MockEsportSkillClient) ModifyUserSkill(arg0 context.Context, arg1 *esport_skill.ModifyUserSkillRequest, arg2 ...grpc.CallOption) (*esport_skill.ModifyUserSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ModifyUserSkill", varargs...)
	ret0, _ := ret[0].(*esport_skill.ModifyUserSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyUserSkill indicates an expected call of ModifyUserSkill.
func (mr *MockEsportSkillClientMockRecorder) ModifyUserSkill(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyUserSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).ModifyUserSkill), varargs...)
}

// QueryIssuanceRecords mocks base method.
func (m *MockEsportSkillClient) QueryIssuanceRecords(arg0 context.Context, arg1 *esport_skill.QueryIssuanceRecordsRequest, arg2 ...grpc.CallOption) (*esport_skill.QueryIssuanceRecordsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryIssuanceRecords", varargs...)
	ret0, _ := ret[0].(*esport_skill.QueryIssuanceRecordsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryIssuanceRecords indicates an expected call of QueryIssuanceRecords.
func (mr *MockEsportSkillClientMockRecorder) QueryIssuanceRecords(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryIssuanceRecords", reflect.TypeOf((*MockEsportSkillClient)(nil).QueryIssuanceRecords), varargs...)
}

// RevokeLabel mocks base method.
func (m *MockEsportSkillClient) RevokeLabel(arg0 context.Context, arg1 *esport_skill.RevokeLabelRequest, arg2 ...grpc.CallOption) (*esport_skill.RevokeLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RevokeLabel", varargs...)
	ret0, _ := ret[0].(*esport_skill.RevokeLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RevokeLabel indicates an expected call of RevokeLabel.
func (mr *MockEsportSkillClientMockRecorder) RevokeLabel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokeLabel", reflect.TypeOf((*MockEsportSkillClient)(nil).RevokeLabel), varargs...)
}

// SetBasePriceSetting mocks base method.
func (m *MockEsportSkillClient) SetBasePriceSetting(arg0 context.Context, arg1 *esport_skill.SetBasePriceSettingRequest, arg2 ...grpc.CallOption) (*esport_skill.SetBasePriceSettingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetBasePriceSetting", varargs...)
	ret0, _ := ret[0].(*esport_skill.SetBasePriceSettingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetBasePriceSetting indicates an expected call of SetBasePriceSetting.
func (mr *MockEsportSkillClientMockRecorder) SetBasePriceSetting(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBasePriceSetting", reflect.TypeOf((*MockEsportSkillClient)(nil).SetBasePriceSetting), varargs...)
}

// SetGameGuaranteeStatus mocks base method.
func (m *MockEsportSkillClient) SetGameGuaranteeStatus(arg0 context.Context, arg1 *esport_skill.SetGameGuaranteeStatusRequest, arg2 ...grpc.CallOption) (*esport_skill.SetGameGuaranteeStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetGameGuaranteeStatus", varargs...)
	ret0, _ := ret[0].(*esport_skill.SetGameGuaranteeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetGameGuaranteeStatus indicates an expected call of SetGameGuaranteeStatus.
func (mr *MockEsportSkillClientMockRecorder) SetGameGuaranteeStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGameGuaranteeStatus", reflect.TypeOf((*MockEsportSkillClient)(nil).SetGameGuaranteeStatus), varargs...)
}

// SetSwitch mocks base method.
func (m *MockEsportSkillClient) SetSwitch(arg0 context.Context, arg1 *esport_skill.SetSwitchRequest, arg2 ...grpc.CallOption) (*esport_skill.SetSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetSwitch", varargs...)
	ret0, _ := ret[0].(*esport_skill.SetSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSwitch indicates an expected call of SetSwitch.
func (mr *MockEsportSkillClientMockRecorder) SetSwitch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSwitch", reflect.TypeOf((*MockEsportSkillClient)(nil).SetSwitch), varargs...)
}

// SetUserSkillAuditType mocks base method.
func (m *MockEsportSkillClient) SetUserSkillAuditType(arg0 context.Context, arg1 *esport_skill.SetUserSkillAuditTypeRequest, arg2 ...grpc.CallOption) (*esport_skill.SetUserSkillAuditTypeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserSkillAuditType", varargs...)
	ret0, _ := ret[0].(*esport_skill.SetUserSkillAuditTypeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserSkillAuditType indicates an expected call of SetUserSkillAuditType.
func (mr *MockEsportSkillClientMockRecorder) SetUserSkillAuditType(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSkillAuditType", reflect.TypeOf((*MockEsportSkillClient)(nil).SetUserSkillAuditType), varargs...)
}

// SetUserSkillRiskAuditType mocks base method.
func (m *MockEsportSkillClient) SetUserSkillRiskAuditType(arg0 context.Context, arg1 *esport_skill.SetUserSkillRiskAuditTypeRequest, arg2 ...grpc.CallOption) (*esport_skill.SetUserSkillRiskAuditTypeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserSkillRiskAuditType", varargs...)
	ret0, _ := ret[0].(*esport_skill.SetUserSkillRiskAuditTypeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserSkillRiskAuditType indicates an expected call of SetUserSkillRiskAuditType.
func (mr *MockEsportSkillClientMockRecorder) SetUserSkillRiskAuditType(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSkillRiskAuditType", reflect.TypeOf((*MockEsportSkillClient)(nil).SetUserSkillRiskAuditType), varargs...)
}

// TestAddUserSkill mocks base method.
func (m *MockEsportSkillClient) TestAddUserSkill(arg0 context.Context, arg1 *esport_skill.TestAddUserSkillRequest, arg2 ...grpc.CallOption) (*esport_skill.TestAddUserSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestAddUserSkill", varargs...)
	ret0, _ := ret[0].(*esport_skill.TestAddUserSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestAddUserSkill indicates an expected call of TestAddUserSkill.
func (mr *MockEsportSkillClientMockRecorder) TestAddUserSkill(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestAddUserSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).TestAddUserSkill), varargs...)
}

// UnfreezeCoachSkill mocks base method.
func (m *MockEsportSkillClient) UnfreezeCoachSkill(arg0 context.Context, arg1 *esport_skill.UnfreezeCoachSkillRequest, arg2 ...grpc.CallOption) (*esport_skill.UnfreezeCoachSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnfreezeCoachSkill", varargs...)
	ret0, _ := ret[0].(*esport_skill.UnfreezeCoachSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnfreezeCoachSkill indicates an expected call of UnfreezeCoachSkill.
func (mr *MockEsportSkillClientMockRecorder) UnfreezeCoachSkill(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfreezeCoachSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).UnfreezeCoachSkill), varargs...)
}

// UpdateEsportGameConfig mocks base method.
func (m *MockEsportSkillClient) UpdateEsportGameConfig(arg0 context.Context, arg1 *esport_skill.UpdateEsportGameConfigRequest, arg2 ...grpc.CallOption) (*esport_skill.UpdateEsportGameConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateEsportGameConfig", varargs...)
	ret0, _ := ret[0].(*esport_skill.UpdateEsportGameConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEsportGameConfig indicates an expected call of UpdateEsportGameConfig.
func (mr *MockEsportSkillClientMockRecorder) UpdateEsportGameConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEsportGameConfig", reflect.TypeOf((*MockEsportSkillClient)(nil).UpdateEsportGameConfig), varargs...)
}

// UpdateRenownedPlayer mocks base method.
func (m *MockEsportSkillClient) UpdateRenownedPlayer(arg0 context.Context, arg1 *esport_skill.UpdateRenownedPlayerRequest, arg2 ...grpc.CallOption) (*esport_skill.UpdateRenownedPlayerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRenownedPlayer", varargs...)
	ret0, _ := ret[0].(*esport_skill.UpdateRenownedPlayerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRenownedPlayer indicates an expected call of UpdateRenownedPlayer.
func (mr *MockEsportSkillClientMockRecorder) UpdateRenownedPlayer(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRenownedPlayer", reflect.TypeOf((*MockEsportSkillClient)(nil).UpdateRenownedPlayer), varargs...)
}
