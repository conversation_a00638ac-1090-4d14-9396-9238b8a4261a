package server

import (
    "context"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    "golang.52tt.com/protocol/services/esport-customer"
    "golang.52tt.com/services/tt-rev/esport/esport-customer/internal/mgr"
    "time"
)

// SaveCustomerAccount 保存新的客服账号信息
func (s *Server) SaveCustomerAccount(ctx context.Context, req *esport_customer.SaveCustomerAccountRequest) (*esport_customer.SaveCustomerAccountResponse, error) {
    log.DebugWithCtx(ctx, "[SaveCustomerAccount] Request: %+v", req)
    resp := &esport_customer.SaveCustomerAccountResponse{}

    // Parameter validation
    if req == nil || req.GetCustomerAccount() == nil {
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "request or customer account cannot be nil")
    }
    if req.GetCustomerAccount().GetCustomerUid() == 0 {
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "customer UID cannot be zero")
    }
    if req.GetCustomerAccount().GetGuildId() == 0 {
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "guild ID cannot be zero")
    }
    if req.GetCustomerAccount().GetCreator() == "" {
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "creator cannot be empty")
    }
    if req.GetCustomerAccount().GetPassword() == "" {
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "password cannot be empty")
    }

    customerAccount := &mgr.CustomerAccount{
        CustomerUID: req.GetCustomerAccount().GetCustomerUid(),
        GuildID:     req.GetCustomerAccount().GetGuildId(),
        Creator:     req.GetCustomerAccount().GetCreator(),
        CreateTime:  time.Now(),
        Password:    req.GetCustomerAccount().GetPassword(),
        Status:      mgr.CUSTOMER_ACCOUNT_STATUS_NORMAL,
    }
    success, err := s.customerAccountMgr.SaveCustomerAccount(ctx, customerAccount)
    if err != nil {
        log.ErrorWithCtx(ctx, "[SaveCustomerAccount] SaveCustomerAccount failed: %v", err)
        return resp, err
    }
    resp.Success = success
    log.DebugWithCtx(ctx, "[SaveCustomerAccount] Response: %+v", resp)
    return resp, nil
}

// GetCustomerAccountDetails 获取客服账号的详细信息
func (s *Server) GetCustomerAccountDetails(ctx context.Context, req *esport_customer.GetCustomerAccountDetailsRequest) (*esport_customer.GetCustomerAccountDetailsResponse, error) {
    log.DebugWithCtx(ctx, "[GetCustomerAccountDetails] Request: %+v", req)
    // Parameter validation
    if req == nil || req.GetCustomerUid() == 0 {
        return &esport_customer.GetCustomerAccountDetailsResponse{}, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "request cannot be nil or customer UID cannot be zero")
    }

    customerAccount, err := s.customerAccountMgr.GetCustomerAccountDetails(ctx, req.GetCustomerUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetCustomerAccountDetails] GetCustomerAccountDetails failed: %v", err)
        return &esport_customer.GetCustomerAccountDetailsResponse{}, err
    }
    resp := &esport_customer.GetCustomerAccountDetailsResponse{
        CustomerAccount: &esport_customer.CustomerAccount{
            CustomerUid: customerAccount.CustomerUID,
            GuildId:     customerAccount.GuildID,
            Creator:     customerAccount.Creator,
            CreateTime:  customerAccount.CreateTime.Unix(),
            Password:    customerAccount.Password,
            Status:      esport_customer.CustomerAccountStatus(customerAccount.Status),
        },
    }
    log.DebugWithCtx(ctx, "[GetCustomerAccountDetails] Response: %+v", resp)
    return resp, nil
}

// BanCustomerAccount 封禁客服账号
func (s *Server) BanCustomerAccount(ctx context.Context, req *esport_customer.BanCustomerAccountRequest) (*esport_customer.BanCustomerAccountResponse, error) {
    log.DebugWithCtx(ctx, "[SetCustomerAccountStatus] Request: %+v", req)
    resp := &esport_customer.BanCustomerAccountResponse{}

    // Parameter validation
    if req == nil || req.GetCustomerUid() == 0 {
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "request cannot be nil or customer UID cannot be zero")
    }
    success, err := s.customerAccountMgr.SetCustomerAccountStatus(ctx, req.GetCustomerUid(), mgr.CUSTOMER_ACCOUNT_STATUS_BANNED)
    if err != nil {
        log.ErrorWithCtx(ctx, "[SetCustomerAccountStatus] SetCustomerAccountStatus failed: %v", err)
        return resp, err
    }
    resp.Success = success
    log.DebugWithCtx(ctx, "[SetCustomerAccountStatus] Response: %+v", resp)
    return resp, nil
}

func (s *Server) GetCustomerAccounts(ctx context.Context, req *esport_customer.GetCustomerAccountsRequest) (*esport_customer.GetCustomerAccountsResponse, error) {
    log.DebugWithCtx(ctx, "[GetCustomerAccounts] Request: %+v", req)

    // 页码从1开始，默认是1
    if req.Page < 1 {
        req.Page = 1
    }

    // 页大小默认是10
    if req.Size < 1 {
        req.Size = 10
    }

    customerAccounts, total, err := s.customerAccountMgr.GetCustomerAccounts(ctx, req.GetPage(), req.GetSize(), req.GetGuildId(), req.GetCustomerUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetCustomerAccounts] GetCustomerAccounts failed: %v", err)
        return &esport_customer.GetCustomerAccountsResponse{}, err
    }
    resp := &esport_customer.GetCustomerAccountsResponse{
        CustomerAccounts: make([]*esport_customer.CustomerAccount, len(customerAccounts)),
        Total:            total,
    }
    for i, account := range customerAccounts {
        resp.CustomerAccounts[i] = &esport_customer.CustomerAccount{
            CustomerUid: account.CustomerUID,
            GuildId:     account.GuildID,
            Creator:     account.Creator,
            CreateTime:  account.CreateTime.Unix(),
            Password:    account.Password,
            Status:      esport_customer.CustomerAccountStatus(account.Status),
        }
    }
    log.DebugWithCtx(ctx, "[GetCustomerAccounts] Response: %+v", resp)
    return resp, nil
}

func (s *Server) AddManagedGod(ctx context.Context, req *esport_customer.AddManagedGodRequest) (*esport_customer.AddManagedGodResponse, error) {
    log.DebugWithCtx(ctx, "[AddManagedGod] Request: %+v", req)
    resp := &esport_customer.AddManagedGodResponse{}

    // 参数校验
    if req.GetCustomerUid() <= 0 {
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "invalid CustomerUid")
    }
    if len(req.GetCoachUid()) == 0 {
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "invalid GodUid")
    }
    success, err := s.mangeGodMgr.AddHostingStatus(ctx, req.GetCustomerUid(), req.GetCoachUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "[AddManagedGod] AddManagedGod failed: %v", err)
        return resp, err
    }
    resp.Success = success
    log.DebugWithCtx(ctx, "[AddManagedGod] Response: %+v", resp)
    return resp, nil
}

func (s *Server) RemoveManagedGod(ctx context.Context, req *esport_customer.RemoveManagedGodRequest) (*esport_customer.RemoveManagedGodResponse, error) {
    log.DebugWithCtx(ctx, "[RemoveManagedGod] Request: %+v", req)
    resp := &esport_customer.RemoveManagedGodResponse{}
    // 参数校验
    if req.GetCustomerUid() <= 0 {
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "invalid CustomerUid")
    }
    if len(req.GetCoachUid()) == 0 {
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "invalid GodUid")
    }
    success, err := s.mangeGodMgr.RemoveHostingStatus(ctx, req.GetCustomerUid(), req.GetCoachUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "[RemoveManagedGod] RemoveManagedGod failed: %v", err)
        return resp, err
    }
    resp.Success = success
    log.DebugWithCtx(ctx, "[RemoveManagedGod] Response: %+v", resp)
    return resp, nil
}

func (s *Server) GetManagedGods(ctx context.Context, req *esport_customer.GetManagedGodsRequest) (*esport_customer.GetManagedGodsResponse, error) {
    log.DebugWithCtx(ctx, "[GetManagedGods] Request: %+v", req)
    if len(req.GetCoachUid()) == 0 {
        return &esport_customer.GetManagedGodsResponse{}, nil
    }
    managedGods, err := s.mangeGodMgr.GetHostingStatusList(ctx, req.GetCoachUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetManagedGods] GetManagedGods failed: %v", err)
        return &esport_customer.GetManagedGodsResponse{}, err
    }
    if len(managedGods) == 0 {
        log.DebugWithCtx(ctx, "[GetManagedGods] No managed gods found, req: %v", req)
        return &esport_customer.GetManagedGodsResponse{}, nil
    }
    resp := &esport_customer.GetManagedGodsResponse{
        ManagedGods: make([]*esport_customer.ManagedGod, len(managedGods)),
    }
    // 抽取 客服号id
    customerUids := make([]uint32, 0, len(managedGods))
    for _, item := range managedGods {
        customerUids = append(customerUids, item.CustomerUID)
    }
    // 批量查询客服号信息
    customerAccountMap, err := s.customerAccountMgr.BatchGetCustomerAccountByUids(ctx, customerUids)
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetManagedGods] GetCustomerAccounts failed: %v", err)
    }

    for i, god := range managedGods {
        resp.ManagedGods[i] = &esport_customer.ManagedGod{
            Uid: god.CoachUID,
        }
        if account, ok := customerAccountMap[god.CustomerUID]; ok {
            resp.ManagedGods[i].CustomerAccount = &esport_customer.CustomerAccount{
                CustomerUid: account.CustomerUID,
                GuildId:     account.GuildID,
                Creator:     account.Creator,
                CreateTime:  account.CreateTime.Unix(),
                Password:    account.Password,
                Status:      esport_customer.CustomerAccountStatus(account.Status),
            }
        }
    }
    log.DebugWithCtx(ctx, "[GetManagedGods] Response: %+v", resp)
    return resp, nil
}
