package store

import (
    context "context"
    kfk_esport_trade "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka-esport-trade"
    context1 "golang.org/x/net/context"
)

type IStore interface {
    BatchGetCoachDurationOrderNum(ctx context.Context, uidList []uint32, gameId, dayCnt uint32) (map[uint32]uint32, error)
    BatchGetOrderCntByGameId(ctx context.Context, uidList []uint32, gameId uint32) (map[uint32]uint32, error)
    Close() error
    GetCoachAllTradeData(ctx context.Context, uid uint32) (map[uint32]*TradeData, error)
    GetCoachTradeData(ctx context.Context, uid, gameId uint32) (*TradeData, error)
    GetCoachVisitRecordByPage(ctx context1.Context, coachId, offset, limit uint32) ([]*CoachVisitRecord, error)
    GetCoachVisitorCount(ctx context1.Context, coachId uint32) (uint32, error)
    InsertCoachVisitRecord(ctx context1.Context, r *CoachVisitRecord) error
    InsertTradeRecord(ctx context.Context, event *kfk_esport_trade.EsportTradeStatusChangeEvent) error
    UpdateCoachVisitRecord(ctx context1.Context, r *CoachVisitRecord) (int64, error)
    GetTradeRecordByCoachUidAndUid(ctx context.Context, coachUid, uid uint32) (*TradeRecord, error)
    GetCoachConversionRate(ctx context.Context, pageNum, pageSize int) ([]*CoachConversionRate, error)
    BatGetCoachOrderSum(ctx context.Context, uidList []uint32, gameId, sumType uint32) (map[uint32]uint32, error)
    GetUserOrderCoachList(ctx context.Context, uid uint32, startTime int64, endTime int64) (map[uint32]int64, error)
    // GetTradeRecordByPage 分页获取订单记录，用于重建用户下单记录，专用
    GetTradeRecordByPage(ctx context.Context, tableNum int, lastOrderId string, limit int, startTime int64, endTime int64) ([]*TradeRecord, error)
    GetCoachOrderRunningValue(ctx context.Context, coachUid uint32, startTime, endTime int64) (uint32, error)
    GetCoachCustomerNum(ctx context.Context, coachUid uint32, startTime, endTime int64) (uint32, error)
}
