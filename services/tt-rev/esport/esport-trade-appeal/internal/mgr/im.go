package mgr

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app/esport_logic"
	im "golang.52tt.com/protocol/app/im"
	imPB "golang.52tt.com/protocol/services/im-api"
	trade_im_notify "golang.52tt.com/services/tt-rev/esport/common/trade-im-notify"
	"golang.52tt.com/services/tt-rev/esport/esport-trade-appeal/internal/cache"
	"golang.52tt.com/services/tt-rev/esport/esport-trade-appeal/internal/conf"
	"golang.52tt.com/services/tt-rev/esport/esport-trade-appeal/internal/rpc"
	"golang.52tt.com/services/tt-rev/esport/esport-trade-appeal/internal/store"
	"time"
)

var _ IM = (*ImImpl)(nil)

//go:generate mockgen -destination=trade_im_notify_mock.go -package=mgr golang.52tt.com/services/tt-rev/esport/common/trade-im-notify ITradeImNotify

type IM interface {
	AsyncSendRefundApplyIMMsg(ctx context.Context, refundId string)
	AsyncSendRefundAcceptIMMsg(ctx context.Context, refundId string)
	AsyncSendRefundRejectIMMsg(ctx context.Context, refundId string)
	AsyncSendAppealApplyIMMsg(ctx context.Context, refundId string)
	AsyncSendAppealCoachUploadIMMsg(ctx context.Context, refundId string)
	AsyncSendAppealSuccessIMMsg(ctx context.Context, refundId string)
	AsyncSendAppealFailIMMsg(ctx context.Context, refundId string)
	AsyncSendAppealPendingAuditIMMsg(ctx context.Context, refundId string)
	AsyncSendFastRefundAcceptIMMsg(ctx context.Context, refundId string)
}

type ImImpl struct {
	tradeImService trade_im_notify.ITradeImNotify
	store          store.Store
	IsAsynchronous bool
	cache          cache.Cache
	dyConfig       conf.BusinessConfApi
}

func NewIM(client *rpc.Client, store store.Store, cache cache.Cache, dyConfig conf.BusinessConfApi) *ImImpl {
	return &ImImpl{
		tradeImService: trade_im_notify.NewTradeImNotify(client.AccountCli, client.SeqgenCli, client.TimelineCli, client.ImApiCli),
		store:          store,
		cache:          cache,
		IsAsynchronous: true,
		dyConfig:       dyConfig,
	}
}

// AsyncSendRefundApplyIMMsg 退款申请的时候给用户和电竞指导者发的IM消息
// 首先查询退款记录。如果退款记录不存在，则打印error日志，直接返回
// 然后根据退款记录，组装数据
// 然后调用ITradeImNotify的接口SendOrderImMsg，发送IM消息
func (i *ImImpl) AsyncSendRefundApplyIMMsg(ctx context.Context, refundId string) {
	i.async(func(aCtx context.Context) {
		log.DebugWithCtx(aCtx, "AsyncSendRefundApplyIMMsg refund_id: %s", refundId)
		refund, err := i.store.GetRefund(aCtx, refundId)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundApplyIMMsg get refund err:%v, refund_id: %s", err, refundId)
			return
		}
		status := uint32(refundStatusToPbStatus(store.RefundStatus(refund.Status)))
		ext := &esport_logic.EsportOrderImMsg{
			MsgTitle:    "我发起了退款申请",
			EventType:   uint32(esport_logic.EsportOrderImMsg_EVENT_TYPE_ORDER_REFUND),
			Status:      status,
			Content:     "待你处理，请确认操作",
			OrderId:     refund.OrderID,
			CoachUid:    refund.CoachID,
			PlayerUid:   refund.UserID,
			OrderStatus: uint32(esport_logic.OrderStatus_ORDER_STATUS_IN_REFUNDING),
		}
		user := &trade_im_notify.UserOrderImMsg{
			Uid:         refund.UserID,
			ServerMsgId: 0, // 第一条消息，所以ServerMsgId为0
			Ext:         ext,
		}
		coach := &trade_im_notify.UserOrderImMsg{
			Uid:         refund.CoachID,
			ServerMsgId: 0, // 第一条消息，所以ServerMsgId为0
			Ext:         ext,
		}

		user.ServerMsgId, coach.ServerMsgId, err = i.tradeImService.SendOrderImMsg(aCtx, user, coach)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundApplyIMMsg SendOrderImMsg err:%v, refund_id: %s", err, refundId)
			return
		}
		// 更新退款记录的IM消息ID
		if err := i.cache.SetLeastRefundIMMsg(aCtx, user.Uid, refundId, user); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundApplyIMMsg SetLeastRefundIMMsgID err:%v, refund_id: %s, user: %v", err, refundId, user)
		}
		if err := i.cache.SetLeastRefundIMMsg(aCtx, coach.Uid, refundId, coach); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundApplyIMMsg SetLeastRefundIMMsgID err:%v, refund_id: %s, coach: %v", err, refundId, coach)
		}
		// 推送订单变更的消息
		if err := i.tradeImService.PushOrderChangeNotify(aCtx, refund.OrderID, []uint32{refund.UserID, refund.CoachID}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundApplyIMMsg PushOrderChangeNotify err:%v, refund_id: %s", err, refundId)
		}
	})
}

// AsyncSendRefundAcceptIMMsg 退款同意的时候给用户和电竞指导者发的IM消息
// 首先查询退款记录。如果退款记录不存在，则打印error日志，直接返回
// 然后根据退款记录，组装本次的IM消息数据。给用户的title是 退款成功，给电竞指导者的title是 已退款。
// 然后调用ITradeImNotify的接口SendOrderImSysMsg，发送本次的IM消息
// 然后查询用户和电竞指导的上一条IM消息。并修改对应的状态status。
// 然后调用ITradeImNotify的接口SendOrderImMsg，发送上一条的IM消息
func (i *ImImpl) AsyncSendRefundAcceptIMMsg(ctx context.Context, refundId string) {
	i.async(func(aCtx context.Context) {
		log.DebugWithCtx(aCtx, "AsyncSendRefundAcceptIMMsg refund_id: %s", refundId)
		refund, err := i.store.GetRefund(aCtx, refundId)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundAcceptIMMsg get refund err:%v, refund_id: %s", err, refundId)
			return
		}
		// 组装本次的IM消息
		status := uint32(refundStatusToPbStatus(store.RefundStatus(refund.Status)))
		orderStatus := uint32(esport_logic.OrderStatus_ORDER_STATUS_REFUNDED)
		user := &trade_im_notify.UserOrderImSysMsg{
			Uid: refund.UserID,
			Ext: &esport_logic.EsportImSysMsg{
				EventType:   uint32(esport_logic.EsportImSysMsg_EVENT_TYPE_ORDER_ACCEPT_REFUND),
				MsgTitle:    "退款成功",
				MsgContent:  fmt.Sprintf("退款费用：%d豆，已原路返还", refund.RefundAmount),
				OrderId:     refund.OrderID,
				Status:      status,
				CoachUid:    refund.CoachID,
				PlayerUid:   refund.UserID,
				OrderStatus: orderStatus,
			},
		}

		coach := &trade_im_notify.UserOrderImSysMsg{
			Uid: refund.CoachID,
			Ext: &esport_logic.EsportImSysMsg{
				EventType:   uint32(esport_logic.EsportImSysMsg_EVENT_TYPE_ORDER_ACCEPT_REFUND),
				MsgTitle:    "已退款",
				MsgContent:  fmt.Sprintf("退款费用：%d豆，已原路返还", refund.RefundAmount),
				OrderId:     refund.OrderID,
				Status:      status,
				CoachUid:    refund.CoachID,
				PlayerUid:   refund.UserID,
				OrderStatus: orderStatus,
			},
		}
		// 发送消息
		if _, _, err := i.tradeImService.SendOrderImSysMsg(aCtx, coach, user); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundAcceptIMMsg SendOrderImSysMsg err:%v, refund_id: %s", err, refundId)
			return
		}

		// 推送订单变更的消息
		if err := i.tradeImService.PushOrderChangeNotify(aCtx, refund.OrderID, []uint32{refund.UserID, refund.CoachID}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundAcceptIMMsg PushOrderChangeNotify err:%v, refund_id: %s", err, refundId)
		}

		//获取上一次的退款IM消息
		userLast, err := i.cache.GetLastRefundIMMsg(aCtx, user.Uid, refundId)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundAcceptIMMsg GetLeastRefundIMMsg err:%v, refund_id: %s, user: %v", err, refundId, user)
			return
		}
		coachLast, err := i.cache.GetLastRefundIMMsg(aCtx, coach.Uid, refundId)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundAcceptIMMsg GetLeastRefundIMMsg err:%v, refund_id: %s, coach: %v", err, refundId, coach)
			return
		}
		// 修改上一次的退款IM消息
		userLast.Ext.Status = status
		userLast.Ext.OrderStatus = orderStatus
		coachLast.Ext.Status = status
		coachLast.Ext.OrderStatus = orderStatus
		// 发送上一次的退款IM消息
		if _, _, err := i.tradeImService.SendOrderImMsg(aCtx, userLast, coachLast); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundAcceptIMMsg SendOrderImMsg err:%v, refund_id: %s", err, refundId)
			return
		}
	})
}

// AsyncSendRefundRejectIMMsg 退款拒绝的时候给用户和电竞指导者发的IM消息
// 首先查询退款记录。如果退款记录不存在，则打印error日志，直接返回
// 然后根据退款记录，组装本次的IM消息数据。给用户的title是 我拒绝了退款申请，内容是 您可以发起申诉，平台将介入，根据双方提供的凭证处理。给电竞指导者的title是 我拒绝了退款申请，内容是 若老板发起申诉，平台将介入，根据双方提供的凭证处理。
// 然后调用ITradeImNotify的接口然后调用ITradeImNotify的接口SendOrderImMsg，发送本次的IM消息
// 然后查询用户和电竞指导的上一条IM消息。并修改对应的状态status。
// 然后调用ITradeImNotify的接口SendOrderImMsg，发送上一条的IM消息
func (i *ImImpl) AsyncSendRefundRejectIMMsg(ctx context.Context, refundId string) {
	i.async(func(aCtx context.Context) {
		log.DebugWithCtx(aCtx, "AsyncSendRefundRejectIMMsg refund_id: %s", refundId)
		// 查询退款记录
		refund, err := i.store.GetRefund(aCtx, refundId)
		if err != nil {
			// 如果退款记录不存在，打印error日志并返回
			log.ErrorWithCtx(aCtx, "AsyncSendRefundRejectIMMsg get refund err:%v, refund_id: %s", err, refundId)
			return
		}

		// 根据退款记录，组装本次的IM消息数据
		status := uint32(refundStatusToPbStatus(store.RefundStatus(refund.Status)))

		// 给用户和电竞指导者发送IM消息
		orderStatus := uint32(esport_logic.OrderStatus_ORDER_STATUS_IN_REFUNDING)
		user := &trade_im_notify.UserOrderImMsg{
			Uid:         refund.UserID,
			ServerMsgId: 0, // 第一条消息，所以ServerMsgId为0
			Ext: &esport_logic.EsportOrderImMsg{
				MsgTitle:    "我拒绝了退款申请",
				EventType:   uint32(esport_logic.EsportOrderImMsg_EVENT_TYPE_ORDER_REJECT_REFUND),
				Status:      status,
				Content:     "您可以发起申诉，平台将介入，根据双方提供的凭证处理",
				OrderId:     refund.OrderID,
				CoachUid:    refund.CoachID,
				PlayerUid:   refund.UserID,
				OrderStatus: orderStatus,
			},
		}
		coach := &trade_im_notify.UserOrderImMsg{
			Uid:         refund.CoachID,
			ServerMsgId: 0, // 第一条消息，所以ServerMsgId为0
			Ext: &esport_logic.EsportOrderImMsg{
				MsgTitle:    "我拒绝了退款申请",
				EventType:   uint32(esport_logic.EsportOrderImMsg_EVENT_TYPE_ORDER_REJECT_REFUND),
				Status:      status,
				Content:     "若老板发起申诉，平台将介入，根据双方提供的凭证处理",
				OrderId:     refund.OrderID,
				CoachUid:    refund.CoachID,
				PlayerUid:   refund.UserID,
				OrderStatus: orderStatus,
			},
		}
		// 根据申诉开关来判断双方的content内容
		if !i.dyConfig.Get().IsAppealOpen {
			// 开关没有开启
			user.Ext.Content = "请您确认订单"
			coach.Ext.Content = "等待老板确认订单"
		}

		// 调用ITradeImNotify的接口SendOrderImMsg，发送IM消息
		user.ServerMsgId, coach.ServerMsgId, err = i.tradeImService.SendOrderImMsg(aCtx, coach, user)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundRejectIMMsg SendOrderImMsg err:%v, refund_id: %s", err, refundId)
			return
		}

		// 推送订单变更的消息
		if err := i.tradeImService.PushOrderChangeNotify(aCtx, refund.OrderID, []uint32{refund.UserID, refund.CoachID}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundRejectIMMsg PushOrderChangeNotify err:%v, refund_id: %s", err, refundId)
		}

		// 查询用户和电竞指导的上一条IM消息
		userLast, err := i.cache.GetLastRefundIMMsg(aCtx, user.Uid, refundId)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundRejectIMMsg GetLeastRefundIMMsg err:%v, refund_id: %s, user: %v", err, refundId, user)
			return
		}
		coachLast, err := i.cache.GetLastRefundIMMsg(aCtx, coach.Uid, refundId)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundRejectIMMsg GetLeastRefundIMMsg err:%v, refund_id: %s, coach: %v", err, refundId, coach)
			return
		}

		// 修改上一次的退款IM消息的状态
		userLast.Ext.Status = status
		userLast.Ext.OrderStatus = orderStatus
		coachLast.Ext.Status = status
		coachLast.Ext.OrderStatus = orderStatus

		// 发送上一次的退款IM消息
		if _, _, err := i.tradeImService.SendOrderImMsg(aCtx, userLast, coachLast); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendRefundRejectIMMsg SendOrderImMsg err:%v, refund_id: %s", err, refundId)
			return
		}
	})
}

// AsyncSendAppealApplyIMMsg 申诉发起的时候给用户和电竞指导者发的IM消息
// 首先查询退款记录。如果退款记录不存在，则打印error日志，直接返回
// 然后根据退款记录，组装本次的IM消息数据。给用户的title是 已发起申诉，内容是 等待平台处理中，请随时关注处理情况。给电竞指导者的title是 已发起申诉，内容是 等待平台处理中，请及时上传凭证。
// 然后调用ITradeImNotify的接口然后调用ITradeImNotify的接口SendOrderImMsg，发送本次的IM消息
// AsyncSendAppealApplyIMMsg 申诉发起的时候给用户和电竞指导者发的IM消息
func (i *ImImpl) AsyncSendAppealApplyIMMsg(ctx context.Context, refundId string) {
	i.async(func(aCtx context.Context) {
		log.DebugWithCtx(aCtx, "AsyncSendAppealApplyIMMsg refund_id: %s", refundId)
		// 首先查询退款记录。如果退款记录不存在，则打印error日志，直接返回
		refund, err := i.store.GetRefund(aCtx, refundId)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealApplyIMMsg get refund err:%v, refund_id: %s", err, refundId)
			return
		}

		// 然后根据退款记录，组装本次的IM消息数据。给用户的title是 已发起申诉，内容是 等待平台处理中，请随时关注处理情况。给电竞指导者的title是 已发起申诉，内容是 等待平台处理中，请及时上传凭证。
		status := uint32(refundStatusToPbStatus(store.RefundStatus(refund.Status)))

		user := &trade_im_notify.UserOrderImMsg{
			Uid:         refund.UserID,
			ServerMsgId: 0, // 第一条消息，所以ServerMsgId为0
			Ext: &esport_logic.EsportOrderImMsg{
				MsgTitle:    "已发起申诉",
				EventType:   uint32(esport_logic.EsportOrderImMsg_EVENT_TYPE_ORDER_APPLY_APPEAL),
				Status:      status,
				Content:     "等待平台处理中，请随时关注处理情况",
				OrderId:     refund.OrderID,
				CoachUid:    refund.CoachID,
				PlayerUid:   refund.UserID,
				OrderStatus: uint32(esport_logic.OrderStatus_ORDER_STATUS_IN_REFUNDING),
			},
		}

		coach := &trade_im_notify.UserOrderImMsg{
			Uid:         refund.CoachID,
			ServerMsgId: 0, // 第一条消息，所以ServerMsgId为0
			Ext: &esport_logic.EsportOrderImMsg{
				MsgTitle:    "已发起申诉",
				EventType:   uint32(esport_logic.EsportOrderImMsg_EVENT_TYPE_ORDER_APPLY_APPEAL),
				Status:      status,
				Content:     "等待平台处理中，请及时上传凭证",
				OrderId:     refund.OrderID,
				CoachUid:    refund.CoachID,
				PlayerUid:   refund.UserID,
				OrderStatus: uint32(esport_logic.OrderStatus_ORDER_STATUS_IN_REFUNDING),
			},
		}

		// 然后调用ITradeImNotify的接口SendOrderImMsg，发送本次的IM消息
		user.ServerMsgId, coach.ServerMsgId, err = i.tradeImService.SendOrderImMsg(aCtx, user, coach)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealApplyIMMsg SendOrderImMsg err:%v, refund_id: %s", err, refundId)
		}

		// 推送订单变更的消息
		if err := i.tradeImService.PushOrderChangeNotify(aCtx, refund.OrderID, []uint32{refund.UserID, refund.CoachID}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealApplyIMMsg PushOrderChangeNotify err:%v, refund_id: %s", err, refundId)
		}
	})
}

// AsyncSendAppealCoachUploadIMMsg 电竞指导者申诉上传凭证的时候给用户和电竞指导者发的IM消息
// 组装本次的IM消息数据，给用户的内容是 '大神已上传申诉凭证'，给电竞指导者的内容是 '您已上传申诉凭证'
// 然后调用ITradeImNotify的接口SendImMsg，发送本次的IM消息
func (i *ImImpl) AsyncSendAppealCoachUploadIMMsg(ctx context.Context, refundId string) {
	// 异步执行
	i.async(func(aCtx context.Context) {
		log.ErrorWithCtx(aCtx, "AsyncSendAppealCoachUploadIMMsg refund_id: %s", refundId)
		// 首先查询退款记录
		refund, err := i.store.GetRefund(aCtx, refundId)
		if err != nil {
			// 如果退款记录不存在，打印error日志并返回
			log.ErrorWithCtx(aCtx, "AsyncSendAppealCoachUploadIMMsg get refund err:%v, refund_id: %s", err, refundId)
			return
		}

		// 给用户发送IM消息
		user := &trade_im_notify.UserMsg{
			Uid:     refund.UserID,
			Content: "大神已上传申诉凭证",
		}

		// 给电竞指导者发送IM消息
		coach := &trade_im_notify.UserMsg{
			Uid:     refund.CoachID,
			Content: "您已上传申诉凭证",
		}

		// 调用ITradeImNotify的接口SendOrderImMsg，发送本次的IM消息
		_, _, err = i.tradeImService.SendImMsg(aCtx, uint32(im.IM_MSG_TYPE_SYSTEM_NOTIFY), coach, user)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealCoachUploadIMMsg SendOrderImMsg err:%v, refund_id: %s", err, refundId)
			return
		}
		// 推送订单变更的消息
		if err := i.tradeImService.PushOrderChangeNotify(aCtx, refund.OrderID, []uint32{refund.UserID, refund.CoachID}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealCoachUploadIMMsg PushOrderChangeNotify err:%v, refund_id: %s", err, refundId)
		}
	})
}

// AsyncSendAppealSuccessIMMsg 申诉成功的时候给用户和电竞指导者发的IM消息
// 首先查询退款记录。如果退款记录不存在，则打印error日志，直接返回
// 然后根据退款记录，组装本次的IM消息数据。给用户的title是 '申诉成功，已退款'，内容是 '退款费用：X豆，已原路返还'。给电竞指导者的title是 '已退款'，内容是 '退款费用：X豆，已原路返还'。
// 然后调用ITradeImNotify的接口SendOrderImSysMsg，发送本次的IM消息
// 然后调用ITradeImNotify的接口SendOfficialAccountMsg，发送主体账号消息给用户和电竞指导者
func (i *ImImpl) AsyncSendAppealSuccessIMMsg(ctx context.Context, refundId string) {
	// 使用异步函数处理
	i.async(func(aCtx context.Context) {
		log.ErrorWithCtx(aCtx, "AsyncSendAppealSuccessIMMsg refund_id: %s", refundId)
		// 首先查询退款记录
		refund, err := i.store.GetRefund(aCtx, refundId)
		if err != nil {
			// 如果退款记录不存在，打印error日志并返回
			log.ErrorWithCtx(aCtx, "AsyncSendAppealSuccessIMMsg get refund err:%v, refund_id: %s", err, refundId)
			return
		}

		// 根据退款记录，组装本次的IM消息数据
		status := uint32(refundStatusToPbStatus(store.RefundStatus(refund.Status)))

		// 给用户和电竞指导者发送IM消息
		user := &trade_im_notify.UserOrderImSysMsg{
			Uid: refund.UserID,
			Ext: &esport_logic.EsportImSysMsg{
				EventType:   uint32(esport_logic.EsportImSysMsg_EVENT_TYPE_ORDER_APPEAL_RESULT),
				MsgTitle:    "申诉成功，已退款",
				MsgContent:  fmt.Sprintf("退款费用：%d豆，已原路返还", refund.RefundAmount),
				OrderId:     refund.OrderID,
				Status:      status,
				CoachUid:    refund.CoachID,
				PlayerUid:   refund.UserID,
				OrderStatus: uint32(esport_logic.OrderStatus_ORDER_STATUS_REFUNDED),
			},
		}

		coach := &trade_im_notify.UserOrderImSysMsg{
			Uid: refund.CoachID,
			Ext: &esport_logic.EsportImSysMsg{
				EventType:   uint32(esport_logic.EsportImSysMsg_EVENT_TYPE_ORDER_APPEAL_RESULT),
				MsgTitle:    "已退款",
				MsgContent:  fmt.Sprintf("退款费用：%d豆，已原路返还", refund.RefundAmount),
				OrderId:     refund.OrderID,
				Status:      status,
				CoachUid:    refund.CoachID,
				PlayerUid:   refund.UserID,
				OrderStatus: uint32(esport_logic.OrderStatus_ORDER_STATUS_REFUNDED),
			},
		}

		// 调用ITradeImNotify的接口SendOrderImSysMsg，发送本次的IM消息
		_, _, err = i.tradeImService.SendOrderImSysMsg(aCtx, user, coach)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealSuccessIMMsg SendOrderImSysMsg err:%v, refund_id: %s", err, refundId)
			return
		}
		// 查询申诉记录
		appealReason := ""
		appeal, err := i.store.GetAppeal(aCtx, refundId)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealSuccessIMMsg get appeal err:%v, refund_id: %s", err, refundId)
		} else {
			appealReason = appeal.AuditNote
		}

		// 调用ITradeImNotify的接口SendOfficialAccountMsg，发送主体账号消息
		if err := i.tradeImService.SendOfficialAccountMsg(aCtx, "esport-refund-appeal", user.Uid, &imPB.Text{
			Content: fmt.Sprintf("订单号为“%s”的退款申诉已经通过啦，退款会立即回退至你的T豆帐户，抱歉给你带来了不愉快的服务体验，我们会努力提升服务质量，期待你再来下单哦~", refund.OrderNumber),
		}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealSuccessIMMsg SendOfficialAccountMsg to user err:%v, refund_id: %s, user_id: %d", err, refundId, user.Uid)
		}
		if err := i.tradeImService.SendOfficialAccountMsg(aCtx, "esport-refund-appeal", coach.Uid, &imPB.Text{
			Content: fmt.Sprintf("订单号为“%s”的退款申诉经人工核查，已判定为【通过退款】，通过原因为【%s】，本订单的服务金将会返还给下单用户，后续请努力提升服务质量哦~", refund.OrderNumber, appealReason),
		}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealSuccessIMMsg SendOfficialAccountMsg to coach err:%v, refund_id: %s, coach_id: %d", err, refundId, coach.Uid)
		}

		// 推送订单变更的消息
		if err := i.tradeImService.PushOrderChangeNotify(aCtx, refund.OrderID, []uint32{refund.UserID, refund.CoachID}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealSuccessIMMsg PushOrderChangeNotify err:%v, refund_id: %s", err, refundId)
		}

	})
}

// AsyncSendAppealFailIMMsg 申诉失败的时候给用户和电竞指导者发的IM消息
// 首先查询退款记录。如果退款记录不存在，则打印error日志，直接返回
// 然后根据退款记录，组装本次的IM消息数据。给用户的title是 '申诉失败'，内容是 '平台根据提交的凭证判定不退款，请确认订单'。给电竞指导者的title是 '申诉结果：无需退款'，内容是 '平台根据提交的凭证判定不退款，待老板确认完成'。
// 然后调用ITradeImNotify的接口SendOrderImSysMsg，发送本次的IM消息
// 然后调用ITradeImNotify的接口SendOfficialAccountMsg，发送主体账号消息给用户和电竞指导者
func (i *ImImpl) AsyncSendAppealFailIMMsg(ctx context.Context, refundId string) {
	// 异步执行
	i.async(func(aCtx context.Context) {
		log.ErrorWithCtx(aCtx, "AsyncSendAppealFailIMMsg refund_id: %s", refundId)
		// 首先查询退款记录
		refund, err := i.store.GetRefund(aCtx, refundId)
		if err != nil {
			// 如果退款记录不存在，打印error日志并返回
			log.ErrorWithCtx(aCtx, "AsyncSendAppealFailIMMsg get refund err:%v, refund_id: %s", err, refundId)
			return
		}

		// 根据退款记录，组装本次的IM消息数据
		status := uint32(refundStatusToPbStatus(store.RefundStatus(refund.Status)))

		// 给用户发送IM消息
		user := &trade_im_notify.UserOrderImSysMsg{
			Uid: refund.UserID,
			Ext: &esport_logic.EsportImSysMsg{
				EventType:   uint32(esport_logic.EsportImSysMsg_EVENT_TYPE_ORDER_APPEAL_RESULT),
				MsgTitle:    "申诉失败",
				MsgContent:  "平台根据提交的凭证判定不退款，请确认订单",
				OrderId:     refund.OrderID,
				Status:      status,
				CoachUid:    refund.CoachID,
				PlayerUid:   refund.UserID,
				OrderStatus: uint32(esport_logic.OrderStatus_ORDER_STATUS_IN_REFUNDING),
			},
		}

		// 给电竞指导者发送IM消息
		coach := &trade_im_notify.UserOrderImSysMsg{
			Uid: refund.CoachID,
			Ext: &esport_logic.EsportImSysMsg{
				EventType:   uint32(esport_logic.EsportImSysMsg_EVENT_TYPE_ORDER_APPEAL_RESULT),
				MsgTitle:    "申诉结果：无需退款",
				MsgContent:  "平台根据提交的凭证判定不退款，待老板确认完成",
				OrderId:     refund.OrderID,
				Status:      status,
				CoachUid:    refund.CoachID,
				PlayerUid:   refund.UserID,
				OrderStatus: uint32(esport_logic.OrderStatus_ORDER_STATUS_IN_REFUNDING),
			},
		}

		// 调用ITradeImNotify的接口SendOrderImSysMsg，发送本次的IM消息
		_, _, err = i.tradeImService.SendOrderImSysMsg(aCtx, user, coach)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealFailIMMsg SendOrderImSysMsg err:%v, refund_id: %s", err, refundId)
			return
		}

		// 查询申诉记录
		appealReason := ""
		appeal, err := i.store.GetAppeal(aCtx, refundId)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealFailIMMsg get appeal err:%v, refund_id: %s", err, refundId)
		} else {
			appealReason = appeal.AuditNote
		}

		// 调用ITradeImNotify的接口SendOfficialAccountMsg，发送主体账号消息
		if err := i.tradeImService.SendOfficialAccountMsg(aCtx, "esport-refund-appeal", user.Uid, &imPB.Text{
			Content: fmt.Sprintf("很抱歉，订单号为“%s”的退款申诉经人工核查，已判定为【拒绝退款】，拒绝原因为【%s】，本订单将于24h内自动把服务费用支付给大神，期待你再来下单哦~", refund.OrderNumber, appealReason),
		}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealFailIMMsg SendOfficialAccountMsg to user err:%v, refund_id: %s, user_id: %d", err, refundId, user.Uid)
		}
		if err := i.tradeImService.SendOfficialAccountMsg(aCtx, "esport-refund-appeal", coach.Uid, &imPB.Text{
			Content: fmt.Sprintf("订单号为“%s”的退款申诉经人工核查，已判定为【拒绝退款】，本订单将于24h内自动把服务金支付给你哦~", refund.OrderNumber),
		}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealFailIMMsg SendOfficialAccountMsg to coach err:%v, refund_id: %s, coach_id: %d", err, refundId, coach.Uid)
		}

		// 推送订单变更的消息
		if err := i.tradeImService.PushOrderChangeNotify(aCtx, refund.OrderID, []uint32{refund.UserID, refund.CoachID}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealFailIMMsg PushOrderChangeNotify err:%v, refund_id: %s", err, refundId)
		}
	})
}

// AsyncSendAppealPendingAuditIMMsg 申诉进入待审核状态的时候给用户和电竞指导者发的IM消息
// 首先查询退款记录。如果退款记录不存在，则打印error日志，直接返回
// 然后推送订单变更的消息
func (i *ImImpl) AsyncSendAppealPendingAuditIMMsg(ctx context.Context, refundId string) {
	i.async(func(aCtx context.Context) {
		log.InfoWithCtx(aCtx, "AsyncSendAppealPendingAuditIMMsg refund_id: %s", refundId)
		// Query the refund record
		refund, err := i.store.GetRefund(aCtx, refundId)
		if err != nil {
			// If the refund record does not exist, log an error and return
			log.ErrorWithCtx(aCtx, "AsyncSendAppealPendingAuditIMMsg get refund err:%v, refund_id: %s", err, refundId)
			return
		}

		// Push the order change notification
		if err := i.tradeImService.PushOrderChangeNotify(aCtx, refund.OrderID, []uint32{refund.UserID, refund.CoachID}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendAppealPendingAuditIMMsg PushOrderChangeNotify err:%v, refund_id: %s", err, refundId)
		}
	})
}

// AsyncSendFastRefundAcceptIMMsg 急速退款完后给用户发IM消息
func (i *ImImpl) AsyncSendFastRefundAcceptIMMsg(ctx context.Context, refundId string) {
	i.async(func(aCtx context.Context) {
		log.DebugWithCtx(aCtx, "AsyncSendFastRefundAcceptIMMsg refund_id: %s", refundId)
		refund, err := i.store.GetRefund(aCtx, refundId)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendFastRefundAcceptIMMsg get refund err:%v, refund_id: %s", err, refundId)
			return
		}
		// 发给用户
		if err := i.tradeImService.SendOfficialAccountMsg(aCtx, "esport-refund-appeal", refund.UserID, &imPB.Text{
			Content: fmt.Sprintf("急速退款成功%d豆已返还到你的账户", refund.RefundAmount),
		}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendFastRefundAcceptIMMsg SendOfficialAccountMsg to user err:%v, refund_id: %s, user_id: %d", err, refundId, refund.UserID)
		}
		// 发给大神
		if err := i.tradeImService.SendOfficialAccountMsg(aCtx, "esport-refund-appeal", refund.CoachID, &imPB.Text{
			Content: "您有一笔极速退款订单，请到我的订单中查看",
		}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendFastRefundAcceptIMMsg SendOfficialAccountMsg to coach err:%v, refund_id: %s, user_id: %d", err, refundId, refund.CoachID)
		}

		// 发送 im 卡片
		status := uint32(refundStatusToPbStatus(store.RefundStatus(refund.Status)))
		ext := &esport_logic.EsportOrderImMsg{
			MsgTitle:    "我发起了极速退款申请",
			EventType:   uint32(esport_logic.EsportOrderImMsg_EVENT_TYPE_ORDER_REFUND),
			Status:      status,
			Content:     "正在极速退款，平台处理中",
			OrderId:     refund.OrderID,
			CoachUid:    refund.CoachID,
			PlayerUid:   refund.UserID,
			OrderStatus: uint32(esport_logic.OrderStatus_ORDER_STATUS_REFUNDED),
			CardStyle:   esport_logic.EsportOrderImMsg_CARD_STYLE_FAST_REFUND,
		}
		user := &trade_im_notify.UserOrderImMsg{
			Uid:         refund.UserID,
			ServerMsgId: 0, // 第一条消息，所以ServerMsgId为0
			Ext:         ext,
		}
		coach := &trade_im_notify.UserOrderImMsg{
			Uid:         refund.CoachID,
			ServerMsgId: 0, // 第一条消息，所以ServerMsgId为0
			Ext:         ext,
		}
		log.DebugWithCtx(aCtx, "AsyncSendFastRefundAcceptIMMsg SendOrderImMsg user: %v, coach: %v", user, coach)
		user.ServerMsgId, coach.ServerMsgId, err = i.tradeImService.SendOrderImMsg(aCtx, user, coach)
		if err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendFastRefundAcceptIMMsg SendOrderImMsg err:%v, refund_id: %s", err, refundId)
		}
		time.Sleep(time.Second * 1)
		// 发送第二组卡片消息
		secondImExt := &esport_logic.EsportImSysMsg{
			EventType:   uint32(esport_logic.EsportImSysMsg_EVENT_TYPE_ORDER_ACCEPT_REFUND),
			MsgTitle:    "极速退款成功",
			MsgContent:  fmt.Sprintf("退款费用：%d豆，已原路返还", refund.RefundAmount),
			OrderId:     refund.OrderID,
			Status:      status,
			CoachUid:    refund.CoachID,
			PlayerUid:   refund.UserID,
			OrderStatus: uint32(esport_logic.OrderStatus_ORDER_STATUS_REFUNDED),
		}
		secondUser := &trade_im_notify.UserOrderImSysMsg{
			Uid: refund.UserID,
			Ext: secondImExt,
		}

		secondCoach := &trade_im_notify.UserOrderImSysMsg{
			Uid: refund.CoachID,
			Ext: secondImExt,
		}
		log.DebugWithCtx(aCtx, "AsyncSendFastRefundAcceptIMMsg SendOrderImSysMsg secondUser: %v, secondCoach: %v", secondUser, secondCoach)
		// 发送消息
		if _, _, err := i.tradeImService.SendOrderImSysMsg(aCtx, secondCoach, secondUser); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendFastRefundAcceptIMMsg SendOrderImSysMsg err:%v, refund_id: %s", err, refundId)
		}

		// 推送订单变更的消息
		if err := i.tradeImService.PushOrderChangeNotify(aCtx, refund.OrderID, []uint32{refund.UserID, refund.CoachID}); err != nil {
			log.ErrorWithCtx(aCtx, "AsyncSendFastRefundAcceptIMMsg PushOrderChangeNotify err:%v, refund_id: %s", err, refundId)
		}
	})
}

func (i *ImImpl) async(fn func(aCtx context.Context)) {
	if i.IsAsynchronous {
		go fn(context.Background())
	} else {
		fn(context.Background())
	}
}
