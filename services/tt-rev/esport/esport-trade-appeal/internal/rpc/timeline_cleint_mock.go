// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/timeline (interfaces: IClient)

// Package rpc is a generated GoMock package.
package rpc

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	Timeline "golang.52tt.com/protocol/services/timelinesvr"
)

// MockTimeliveIClient is a mock of IClient interface.
type MockTimeliveIClient struct {
	ctrl     *gomock.Controller
	recorder *MockTimeliveIClientMockRecorder
}

// MockTimeliveIClientMockRecorder is the mock recorder for MockTimeliveIClient.
type MockTimeliveIClientMockRecorder struct {
	mock *MockTimeliveIClient
}

// NewMockTimeliveIClient creates a new mock instance.
func NewMockTimeliveIClient(ctrl *gomock.Controller) *MockTimeliveIClient {
	mock := &MockTimeliveIClient{ctrl: ctrl}
	mock.recorder = &MockTimeliveIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTimeliveIClient) EXPECT() *MockTimeliveIClientMockRecorder {
	return m.recorder
}

// BatchGetPeerReadStatus mocks base method.
func (m *MockTimeliveIClient) BatchGetPeerReadStatus(arg0 context.Context, arg1, arg2 uint32, arg3 []string) (map[string]*Timeline.PeerReadStatus, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPeerReadStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(map[string]*Timeline.PeerReadStatus)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetPeerReadStatus indicates an expected call of BatchGetPeerReadStatus.
func (mr *MockTimeliveIClientMockRecorder) BatchGetPeerReadStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPeerReadStatus", reflect.TypeOf((*MockTimeliveIClient)(nil).BatchGetPeerReadStatus), arg0, arg1, arg2, arg3)
}

// BatchWriteTimelineMsg mocks base method.
func (m *MockTimeliveIClient) BatchWriteTimelineMsg(arg0 context.Context, arg1 uint32, arg2 string, arg3 []*Timeline.TimelineMsg) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchWriteTimelineMsg", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// BatchWriteTimelineMsg indicates an expected call of BatchWriteTimelineMsg.
func (mr *MockTimeliveIClientMockRecorder) BatchWriteTimelineMsg(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchWriteTimelineMsg", reflect.TypeOf((*MockTimeliveIClient)(nil).BatchWriteTimelineMsg), arg0, arg1, arg2, arg3)
}

// CC mocks base method.
func (m *MockTimeliveIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockTimeliveIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockTimeliveIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockTimeliveIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockTimeliveIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockTimeliveIClient)(nil).Close))
}

// GetMessages mocks base method.
func (m *MockTimeliveIClient) GetMessages(arg0 context.Context, arg1 uint32, arg2 string, arg3, arg4 uint32) ([]*Timeline.TimelineMsg, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMessages", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*Timeline.TimelineMsg)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMessages indicates an expected call of GetMessages.
func (mr *MockTimeliveIClientMockRecorder) GetMessages(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMessages", reflect.TypeOf((*MockTimeliveIClient)(nil).GetMessages), arg0, arg1, arg2, arg3, arg4)
}

// GetPeerReadStatus mocks base method.
func (m *MockTimeliveIClient) GetPeerReadStatus(arg0 context.Context, arg1 uint32, arg2 string) (*Timeline.PeerReadStatus, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPeerReadStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Timeline.PeerReadStatus)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPeerReadStatus indicates an expected call of GetPeerReadStatus.
func (mr *MockTimeliveIClientMockRecorder) GetPeerReadStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPeerReadStatus", reflect.TypeOf((*MockTimeliveIClient)(nil).GetPeerReadStatus), arg0, arg1, arg2)
}

// GetReadStatus mocks base method.
func (m *MockTimeliveIClient) GetReadStatus(arg0 context.Context, arg1 uint32, arg2 string) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReadStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetReadStatus indicates an expected call of GetReadStatus.
func (mr *MockTimeliveIClientMockRecorder) GetReadStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReadStatus", reflect.TypeOf((*MockTimeliveIClient)(nil).GetReadStatus), arg0, arg1, arg2)
}

// IsMsgKeyExist mocks base method.
func (m *MockTimeliveIClient) IsMsgKeyExist(arg0 context.Context, arg1 uint32, arg2, arg3 string, arg4 uint32) (bool, []byte, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsMsgKeyExist", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].([]byte)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// IsMsgKeyExist indicates an expected call of IsMsgKeyExist.
func (mr *MockTimeliveIClientMockRecorder) IsMsgKeyExist(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsMsgKeyExist", reflect.TypeOf((*MockTimeliveIClient)(nil).IsMsgKeyExist), arg0, arg1, arg2, arg3, arg4)
}

// MultiGetReadStatus mocks base method.
func (m *MockTimeliveIClient) MultiGetReadStatus(arg0 context.Context, arg1 uint32, arg2 []string) ([]*Timeline.ReadStatus, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MultiGetReadStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*Timeline.ReadStatus)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// MultiGetReadStatus indicates an expected call of MultiGetReadStatus.
func (mr *MockTimeliveIClientMockRecorder) MultiGetReadStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiGetReadStatus", reflect.TypeOf((*MockTimeliveIClient)(nil).MultiGetReadStatus), arg0, arg1, arg2)
}

// MultiGetReadStatusMap mocks base method.
func (m *MockTimeliveIClient) MultiGetReadStatusMap(arg0 context.Context, arg1 uint32, arg2 []string) (map[string]*Timeline.ReadStatus, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MultiGetReadStatusMap", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[string]*Timeline.ReadStatus)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// MultiGetReadStatusMap indicates an expected call of MultiGetReadStatusMap.
func (mr *MockTimeliveIClientMockRecorder) MultiGetReadStatusMap(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiGetReadStatusMap", reflect.TypeOf((*MockTimeliveIClient)(nil).MultiGetReadStatusMap), arg0, arg1, arg2)
}

// SetPeerReadStatus mocks base method.
func (m *MockTimeliveIClient) SetPeerReadStatus(arg0 context.Context, arg1 uint32, arg2 string, arg3 uint32) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPeerReadStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetPeerReadStatus indicates an expected call of SetPeerReadStatus.
func (mr *MockTimeliveIClientMockRecorder) SetPeerReadStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPeerReadStatus", reflect.TypeOf((*MockTimeliveIClient)(nil).SetPeerReadStatus), arg0, arg1, arg2, arg3)
}

// SetReadStatus mocks base method.
func (m *MockTimeliveIClient) SetReadStatus(arg0 context.Context, arg1 uint32, arg2 string, arg3 uint32, arg4 bool) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetReadStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// SetReadStatus indicates an expected call of SetReadStatus.
func (mr *MockTimeliveIClientMockRecorder) SetReadStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetReadStatus", reflect.TypeOf((*MockTimeliveIClient)(nil).SetReadStatus), arg0, arg1, arg2, arg3, arg4)
}

// Stub mocks base method.
func (m *MockTimeliveIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockTimeliveIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockTimeliveIClient)(nil).Stub))
}

// WriteMessage mocks base method.
func (m *MockTimeliveIClient) WriteMessage(arg0 context.Context, arg1 uint32, arg2 string, arg3 *Timeline.TimelineMsg) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteMessage", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// WriteMessage indicates an expected call of WriteMessage.
func (mr *MockTimeliveIClientMockRecorder) WriteMessage(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteMessage", reflect.TypeOf((*MockTimeliveIClient)(nil).WriteMessage), arg0, arg1, arg2, arg3)
}
