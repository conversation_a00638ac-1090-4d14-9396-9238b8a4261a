package internal

import (
    "context"
    "fmt"
    "golang.52tt.com/services/tt-rev/common/goroutineex"
    "golang.52tt.com/services/tt-rev/esport/common/collection/mapz"
    "sync"
    "time"

    "golang.52tt.com/clients/account"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    "golang.52tt.com/protocol/services/esport_godlevel"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/event"

    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/protocol/services/demo/echo"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/cache"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/conf"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/mgr"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/rpc"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/store"
    context0 "golang.org/x/net/context"
    "google.golang.org/grpc/codes"

    pb "golang.52tt.com/protocol/services/esport-skill"
)

const guaranteeSectionName = "包赢承诺"

type Server struct {
    m              *mgr.Manager
    skillMgr       mgr.SkillService
    gameConfigMgr  mgr.GameConfigService
    labelMgr       mgr.LabelManager
    pricingService mgr.PricingService
    accountCli     account.IClient
    godLevelCli    esport_godlevel.ESportGodLevelServiceClient
    imMgr          mgr.ImService
    godLevelKfkSub *event.GodLevelKafkaSub
}

// GetLabelIssueRecord 获取标识列表
func (s *Server) GetLabelIssueRecord(c context.Context, request *pb.GetLabelIssuanceRecordRequest) (*pb.GetLabelIssuanceRecordResponse, error) {
    resp := &pb.GetLabelIssuanceRecordResponse{}
    record, err := s.labelMgr.GetEffectiveIssuanceRecord(c, int64(request.GetPageNum()), int64(request.GetPageSize()))
    if err != nil {
        log.ErrorWithCtx(c, "GetLabelIssueRecord failed, err:%v", err)
        return resp, err
    }
    for _, r := range record {
        resp.RecordList = append(resp.RecordList, &pb.SimpleIssuanceRecord{
            CoachUid: r.CoachID,
            LabelId:  r.LabelID,
        })
    }

    return resp, nil
}

// GetSpecialIssueRecord 获取特殊标识列表
func (s *Server) GetSpecialIssueRecord(c context.Context, request *pb.GetSpecialLabelIssuanceRecordRequest) (*pb.GetSpecialLabelIssuanceRecordResponse, error) {
    resp := &pb.GetSpecialLabelIssuanceRecordResponse{}
    record, err := s.labelMgr.GetSpecialLabelIssuanceRecord(c, int64(request.GetPageNum()), int64(request.GetPageSize()))
    if err != nil {
        log.ErrorWithCtx(c, "GetSpecialIssueRecord failed, err:%v", err)
        return resp, err
    }
    for _, r := range record {
        resp.RecordList = append(resp.RecordList, &pb.SimpleIssuanceRecord{
            CoachUid: r.CoachID,
            GameId:   r.GameId,
            LabelId:  r.LabelID,
        })
    }

    return resp, nil
}

func (s *Server) BatchGetUserGameSpecialLabel(c context0.Context, request *pb.BatchGetUserGameSpecialLabelRequest) (*pb.BatchGetUserGameSpecialLabelResponse, error) {
    out := &pb.BatchGetUserGameSpecialLabelResponse{}
    defer func() {
        log.DebugWithCtx(c, "BatchGetUserGameSpecialLabel, req:%+v, resp:%+v", request, out)
    }()
    if len(request.GetUidList()) == 0 || request.GetGameId() == 0 {
        log.ErrorWithCtx(c, "BatchGetUserGameSpecialLabel failed, request:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "参数错误")
    }
    out, err := s.labelMgr.BatchGetUserGameSpecialLabel(c, request)
    if err != nil {
        log.ErrorWithCtx(c, "BatchGetUserGameSpecialLabel failed, err:%v", err)
        return out, err
    }
    return out, nil
}

func (s *Server) GetSpecialLabelList(c context0.Context, request *pb.GetSpecialLabelListRequest) (*pb.GetSpecialLabelListResponse, error) {
    out := &pb.GetSpecialLabelListResponse{}
    var err error
    var retLabelList []*store.Label
    switch request.GetLabelType() {
    case pb.SpecialLabelType_SPECIAL_LABEL_TYPE_SPECIAL:
        labelListComm, _, err := s.labelMgr.ListLabels(c, 1, 500, 0, store.LabelType_LABEL_TYPE_SPECIAL, 0)
        if err != nil {
            log.ErrorWithCtx(c, "GetSpecialLabelList failed to get special label list: %v", err)
            return out, err
        }
        labelListGame, _, err := s.labelMgr.ListLabels(c, 1, 500, 0, store.LabelType_LABEL_TYPE_SPECIAL, request.GetGameId())
        if err != nil {
            log.ErrorWithCtx(c, "GetSpecialLabelList failed to get special label list: %v", err)
            return out, err
        }
        retLabelList = append(labelListComm, labelListGame...)

    case pb.SpecialLabelType_SPECIAL_LABEL_TYPE_VOICE:
        retLabelList, _, err = s.labelMgr.ListLabels(c, 1, 500, 0, store.LabelType_LABEL_TYPE_VOICE, 0)
        if err != nil {
            log.ErrorWithCtx(c, "GetSpecialLabelList failed to get special label list: %v", err)
            return out, err
        }

    default:
        log.ErrorWithCtx(c, "GetSpecialLabelList failed: invalid label type: %v", request.GetLabelType())
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "标签类型错误")
    }
    for _, label := range retLabelList {
        out.LabelList = append(out.LabelList, &pb.SpecialLabel{
            Id:        label.ID,
            LabelName: label.Name,
        })
    }
    return out, nil
}

func (s *Server) UpdateUserSpecialLabel(c context0.Context, request *pb.UpdateUserSpecialLabelRequest) (*pb.UpdateUserSpecialLabelResponse, error) {
    out := &pb.UpdateUserSpecialLabelResponse{}
    defer func() {
        log.DebugWithCtx(c, "UpdateUserSpecialLabel, req:%+v, resp:%+v", request, out)
    }()
    if request.GetUid() == 0 || len(request.GetGameList()) == 0 {
        log.ErrorWithCtx(c, "UpdateUserSpecialLabel failed, request:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "参数错误")
    }
    return out, s.m.UpdateUserSpecialLabel(c, request)
}

func (s *Server) BatchGetUserSpecialLabel(c context0.Context, request *pb.BatchGetUserSpecialLabelRequest) (*pb.BatchGetUserSpecialLabelResponse, error) {
    out := &pb.BatchGetUserSpecialLabelResponse{}
    defer func() {
        log.DebugWithCtx(c, "BatchGetUserSpecialLabel, req:%+v, resp:%+v", request, out)
    }()
    if len(request.GetUidList()) == 0 {
        log.ErrorWithCtx(c, "BatchGetUserSpecialLabel failed, request:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "参数错误")
    }
    return s.labelMgr.BatchGetUserSpecialLabel(c, request.GetUidList())
}

func (s *Server) CheckLabelOrder(c context0.Context, request *pb.CheckLabelOrderRequest) (*pb.CheckLabelOrderResponse, error) {
    out := &pb.CheckLabelOrderResponse{}
    defer func() {
        log.DebugWithCtx(c, "CheckAddLabelOrder, req:%+v, resp:%+v", request, out)
    }()
    if request.GetCheckType() == pb.CheckLabelOrderRequest_CHECK_TYPE_UNSPECIFIED {
        log.ErrorWithCtx(c, "CheckAddLabelOrder failed, request:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "参数错误")
    }

    return out, s.labelMgr.CheckLabelOrder(c, request.GetLabelType(), request.GetDisplayOrder(), request.GetLabelId(), request.GetCheckType())
}

func (s *Server) GetAllGameCardConfig(ctx context.Context, request *pb.GetAllGameCardConfigRequest) (*pb.GetAllGameCardConfigResponse, error) {
    return s.m.GetAllGameCardConfig(ctx)
}

func (s *Server) HandleGameUpdate(c context0.Context, request *pb.HandleGameUpdateRequest) (*pb.HandleGameUpdateResponse, error) {
    return &pb.HandleGameUpdateResponse{}, s.m.HandleGameUpdate(c, request.GetGameId())
}

func (s *Server) SetGameGuaranteeStatus(c context0.Context, request *pb.SetGameGuaranteeStatusRequest) (*pb.SetGameGuaranteeStatusResponse, error) {
    out := &pb.SetGameGuaranteeStatusResponse{}
    defer func() {
        log.DebugWithCtx(c, "SetGameGuaranteeStatus, req:%+v, resp:%+v", request, out)
    }()
    if request.GetUid() == 0 || request.GetGameId() == 0 {
        log.ErrorWithCtx(c, "SetGameGuaranteeStatus failed, request:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    err := s.m.SetGameGuaranteeWin(c, request.GetUid(), request.GetGameId(), request.GetIsGuaranteeWin(), false)
    if err != nil {
        log.ErrorWithCtx(c, "SetGameGuaranteeStatus failed, err:%v", err)
        return out, err
    }
    return out, nil
}

func (s *Server) DebugCheckGuaranteeWinPermission(ctx context.Context, in *pb.DebugCheckGuaranteeWinPermissionRequest) (*pb.DebugCheckGuaranteeWinPermissionResponse, error) {
    out := &pb.DebugCheckGuaranteeWinPermissionResponse{}
    go s.m.RunTaskCheckGuaranteeWinPermission()
    return out, nil
}

func NewServer(ctx context.Context, cfg *conf.StartConfig) (*Server, error) {
    log.Infof("server startup with cfg: %+v", *cfg)

    s := &Server{}

    cache_, err := cache.NewCache(ctx, cfg.RedisConfig)
    if nil != err {
        log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
        return nil, err
    }

    dao_, err := store.NewStore(ctx, cfg.MongoConfig)
    if nil != err {
        log.ErrorWithCtx(ctx, "init mongo fail, err: %v", err)
        return nil, err
    }

    bc, err := conf.NewBusinessConfManager()
    if err != nil {
        log.Errorf("conf.NewBusinessConfManager fail,err:%v", err)
        return nil, err
    }

    rpc, err := rpc.NewClient()
    m := mgr.NewManager(dao_, cache_, rpc, bc)
    godKfkCfg := cfg.KafkaMap["god_level"]
    godKfk, err := event.NewGodLevelKafkaSubscriber(godKfkCfg.ClientID, godKfkCfg.GroupID, godKfkCfg.TopicList(), godKfkCfg.BrokerList(), m)
    if err != nil {
        log.ErrorWithCtx(ctx, "Failed to NewGodLevelKafkaSubscriber err %s", err.Error())
        return nil, err
    }
    s.m = m

    s.gameConfigMgr = m
    s.skillMgr = m
    s.labelMgr = m
    s.pricingService = m
    s.imMgr = m
    s.accountCli = rpc.AccountCli
    s.godLevelCli = rpc.GodLevelCli
    s.godLevelKfkSub = godKfk

    log.InfoWithCtx(ctx, "NewServer finish, cfg: %+v", cfg)
    return s, nil
}

func (s *Server) BatchCheckCoachHasGame(c context0.Context, request *pb.BatchCheckCoachHasGameRequest) (*pb.BatchCheckCoachHasGameResponse, error) {
    out := &pb.BatchCheckCoachHasGameResponse{}
    defer func() {
        log.DebugWithCtx(c, "BatchCheckCoachHasGame, req:%+v, resp:%+v", request, out)
    }()
    if len(request.GetCoachIds()) == 0 || request.GetGameId() == 0 {
        log.ErrorWithCtx(c, "BatchCheckCoachHasGame failed, request:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    hasMap, err := s.skillMgr.BatchCheckCoachHasGame(c, request.GetCoachIds(), request.GetGameId())
    if err != nil {
        log.ErrorWithCtx(c, "BatchCheckCoachHasGame failed, err:%v", err)
        return out, err
    }
    out.CoachHasGameMap = hasMap
    return out, nil
}

// BatchGetCoachLabelsForGame 批量获取指定游戏的多个教练的标识列表
// 1. 检查request CoachIds是否合法
// 2. 检查request GameId是否合法
// 3. 根据 GameId 和 CoachIds 批量查询多个教练对应的标签列表
// 4. 根据 CoachIds 批量查询多个教练的知名选手信息列表，并建立索引，教练 id 为 key
// 5. 组装数据
func (s *Server) BatchGetCoachLabelsForGame(c context0.Context, request *pb.BatchGetCoachLabelsForGameRequest) (*pb.BatchGetCoachLabelsForGameResponse, error) {
    log.DebugWithCtx(c, "BatchGetCoachLabelsForGame: request: %v", request)

    // Validate the request
    if len(request.CoachIds) == 0 {
        log.ErrorWithCtx(c, "BatchGetCoachLabelsForGame: invalid request: %v", request)
        return &pb.BatchGetCoachLabelsForGameResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "invalid request")
    }

    // 根据 GameId 和 CoachIds 批量查询多个教练对应的标签列表 
    labels, _, err := s.labelMgr.BatchGetCoachLabels(c, request.GetCoachIds(), []uint32{request.GetGameId()})
    if err != nil {
        log.ErrorWithCtx(c, "BatchGetCoachLabelsForGame: failed to get labels: %v", err)
        return &pb.BatchGetCoachLabelsForGameResponse{}, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to get labels")
    }

    //根据 CoachIds 批量查询多个教练的知名选手信息列表，并建立索引，教练 id 为 key
    renownedInfoList, err := s.labelMgr.BatchGetUserRenownedInfoByCoachIdsAndGameId(c, request.CoachIds, request.GameId)
    if err != nil {
        log.ErrorWithCtx(c, "BatchGetCoachLabelsForGame: failed to get renowned players: %v", err)
        return &pb.BatchGetCoachLabelsForGameResponse{}, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to get renowned players")
    }
    renownedInfoMap := make(map[uint32]*store.RenownedPlayer)
    for _, info := range renownedInfoList {
        renownedInfoMap[info.UID] = info
    }

    // 组装数据
    response := &pb.BatchGetCoachLabelsForGameResponse{
        LabelList: make([]*pb.BatchGetCoachLabelsForGameResponse_CoachLabelInfo, 0, len(request.CoachIds)),
    }

    for _, coachId := range request.GetCoachIds() {
        // Get the renowned player info
        _, ok := renownedInfoMap[coachId]
        // Assemble the data
        labelMap := make(map[uint32]*pb.BatchGetCoachLabelsForGameResponse_LabelList)
        for _, item := range labels[coachId] {
            list, ok := labelMap[uint32(pb.LabelType(item.LabelType))]
            if !ok {
                list = &pb.BatchGetCoachLabelsForGameResponse_LabelList{}
                labelMap[uint32(pb.LabelType(item.LabelType))] = list
            }
            list.LabelList = append(list.LabelList, s.labelInfoEnToPb(item))
        }
        response.LabelList = append(response.LabelList, &pb.BatchGetCoachLabelsForGameResponse_CoachLabelInfo{
            LabelMap:   labelMap,
            IsRenowned: ok,
            CoachId:    coachId,
        })
    }

    // Return the response
    return response, nil
}
func (s *Server) CheckCoachIdentityAndSkill(c context0.Context, request *pb.CheckCoachIdentityAndSkillRequest) (*pb.CheckCoachIdentityAndSkillResponse, error) {
    out := &pb.CheckCoachIdentityAndSkillResponse{}
    // Check if the info_list in the request is not empty
    if len(request.GetCheckList()) == 0 {
        out.ErrMsg = "列表为空"
        return out, nil
    }

    uidList := make([]uint32, 0, len(request.GetCheckList()))
    for _, info := range request.GetCheckList() {
        uidList = append(uidList, info.GetCoachId())
    }

    // 检查uid合法性
    accountMap, sErr := s.accountCli.GetUsersMap(c, uidList)
    if sErr != nil {
        log.ErrorWithCtx(c, "CheckCoachIdentityAndSkill failed to get users map: %v", sErr)
        return out, sErr
    }
    for _, info := range request.GetCheckList() {
        if accountMap[info.GetCoachId()] == nil {
            log.ErrorWithCtx(c, "CheckCoachIdentityAndSkill failed to get user info, uid:%d", info.GetCoachId())
            out.ErrMsg = fmt.Sprintf("获取用户信息失败，uid:%d", info.GetCoachId())
            return out, nil
        }
    }

    notCoachList, err := s.m.CheckCoachList(c, uidList)
    if err != nil {
        log.ErrorWithCtx(c, "CheckCoachIdentityAndSkill failed to check coach list: %v", err)
        out.ErrMsg = "检查教练身份失败"
        return out, nil
    }
    if len(notCoachList) > 0 {
        log.ErrorWithCtx(c, "CheckCoachIdentityAndSkill failed, not coach list: %v", notCoachList)
        out.ErrMsg = fmt.Sprintf("存在非教练用户，uid列表：%+v", notCoachList)
        return out, nil
    }

    for _, info := range request.GetCheckList() {
        if info.GetGameName() == "" {
            log.ErrorWithCtx(c, "CheckCoachIdentityAndSkill failed: GameId is empty")
            out.ErrMsg = "存在游戏名称为空的记录"
            return out, nil
        }
        // 检查教练技能
        game, err := s.m.IsCoachHasGame(c, info.GetCoachId(), info.GetGameName())
        if err != nil {
            log.ErrorWithCtx(c, "CheckCoachIdentityAndSkill failed to check coach has game: %v", err)
            return out, err
        }
        if game == nil {
            errMsg := fmt.Sprintf("教练[ttid:%s]不存在技能[%s]", accountMap[info.GetCoachId()].GetAlias(), info.GetGameName())
            log.ErrorWithCtx(c, "CheckCoachIdentityAndSkill failed IsCoachHasGame, coachId:%d, err: %s", info.GetCoachId(), errMsg)
            out.ErrMsg = errMsg
            return out, nil
        }

        // 检查知名选手是否已存在
        exist, err := s.m.IsRenownedPlayerExist(c, info.GetCoachId(), game.GameId)
        if err != nil {
            log.ErrorWithCtx(c, "CheckCoachIdentityAndSkill failed to check renowned player: %v", err)
            out.ErrMsg = "检查知名选手失败"
            return out, nil
        } else if exist {
            log.ErrorWithCtx(c, "CheckCoachIdentityAndSkill failed: renowned player exist")
            out.ErrMsg = fmt.Sprintf("已存在相同的知名选手！教练[ttid:%s], 技能名称:[%s]", accountMap[info.GetCoachId()].GetAlias(), info.GetGameName())
            return out, nil
        } else {
            log.DebugWithCtx(c, "CheckCoachIdentityAndSkill success: uid:%d, gameName:%s", info.GetCoachId(), info.GetGameName())
        }
    }
    return out, nil
}

// GetCoachApplicableLabels 获取教练当前可申请的标识列表
// 1. 检查request CoachId是否合法
// 2. 检查request GameId是否合法
// 3. 查询标识列表（labelMgr GetLabelsByGamesWithCoachType 方法）
// 4. 查询教练拥有的标识（labelMgr GetCoachLabels），并建立索引，标识 id 为 key
// 5. 组装数据返回
func (s *Server) GetCoachApplicableLabels(c context.Context, request *pb.GetCoachApplicableLabelsRequest) (*pb.GetCoachApplicableLabelsResponse, error) {
    // Step 1: Validate the CoachId and GameId
    if request.CoachId == 0 {
        log.ErrorWithCtx(c, "GetCoachApplicableLabels failed: CoachId is empty")
        return &pb.GetCoachApplicableLabelsResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "CoachId is empty")
    }
    if len(request.GetGameId()) == 0 {
        log.ErrorWithCtx(c, "GetCoachApplicableLabels failed: GameId is empty")
        return &pb.GetCoachApplicableLabelsResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "GameId is empty")
    }

    // Step 2: Get the list of labels
    labels, err := s.labelMgr.GetLabelsByGamesWithCoachType(c, request.GetGameId()...)
    if err != nil {
        log.ErrorWithCtx(c, "GetCoachApplicableLabels failed to get labels: %v", err)
        return &pb.GetCoachApplicableLabelsResponse{}, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to get labels")
    }

    // Step 3: Get the labels that the coach already has
    coachLabels, coach2AddPriceLabelsMap, err := s.labelMgr.BatchGetCoachLabels(c, []uint32{request.CoachId}, request.GetGameId())
    if err != nil {
        log.ErrorWithCtx(c, "GetCoachApplicableLabels failed to get coach labels: %v", err)
        return &pb.GetCoachApplicableLabelsResponse{}, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to get coach labels")
    }

    // Step 4: Create a map to index the labels that the coach already has
    coachLabelMap := make(map[uint32]*store.Label)
    if list, ok := coachLabels[request.GetCoachId()]; ok {
        for _, label := range list {
            coachLabelMap[label.ID] = label
        }
    }

    // add2PriceSwitchMap
    add2PriceSwitchMap := make(map[uint32]bool)
    if list, ok := coach2AddPriceLabelsMap[request.GetCoachId()]; ok {
        for _, labelId := range list {
            add2PriceSwitchMap[labelId] = true
        }
    }

    // Step 5: Iterate over the list of labels and check if the coach already has each label
    var applicableLabels []*pb.GetCoachApplicableLabelsResponse_ApplicableLabel
    for _, label := range labels {
        elems := &pb.GetCoachApplicableLabelsResponse_ApplicableLabel{
            Info:      s.labelInfoEnToPb(label),
            IsApplied: false,
        }
        if _, ok := coachLabelMap[label.ID]; ok {
            elems.IsApplied = true
            elems.IsAddToPrice = add2PriceSwitchMap[label.ID]
        }
        applicableLabels = append(applicableLabels, elems)
    }

    // Step 6: Return the response
    return &pb.GetCoachApplicableLabelsResponse{
        LabelList: applicableLabels,
    }, nil
}

// SetLabelPriceSwitch 设置标识是否加价
func (s *Server) SetLabelPriceSwitch(c context0.Context, request *pb.SetLabelPriceSwitchRequest) (*pb.SetLabelPriceSwitchResponse, error) {
    out := &pb.SetLabelPriceSwitchResponse{}

    if request.GetCoachId() == 0 || request.GetLabelId() == 0 {
        log.ErrorWithCtx(c, "SetLabelPriceSwitch failed: CoachId is empty")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "CoachId is empty")
    }

    err := s.labelMgr.SetLabelPriceAdditionalSwitch(c, request.GetCoachId(), request.GetLabelId(), request.GetTargetState())
    if err != nil {
        log.ErrorWithCtx(c, "SetLabelPriceSwitch failed to set label price switch: %v", err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to set label price switch")
    }

    return out, nil
}

// CalculatePrice 计算指定教练的指定技能的价格
// 1. 检查request CoachIds是否合法。判断是为空列表
// 2. 检查request GameId是否合法，根据 game_id 查询技能的信息。（Manager 的 GetGameDetailByIds 方法）
// 3. 批量查询教练的基础定价配置，并建立索引，教练 id 为 key
// 4. 批量查询教练的标识列表，并建立索引，教练 id 为 key
// 5. 批量查询教练的知名选手信息，并建立索引，教练 id 为 key
// 6. 批量查询教练的大神等级。
// 7. 并发调用pricingService 的 CalculatePrice方法计算价格。
// 8. 组装数据返回
func (s *Server) CalculatePrice(c context.Context, request *pb.CalculatePriceRequest) (*pb.CalculatePriceResponse, error) {
    response := &pb.CalculatePriceResponse{
        PriceMap: map[uint32]*pb.Price{},
    }
    if request.WithCache {
        cachePriceMap, missCacheCoachId, err := s.pricingService.BatGetCoachPrice(c, request.GetGameId(), request.GetCoachIds())
        if err != nil {
            log.WarnWithCtx(c, "CalculatePrice failed to get price from cache: %v", err)
        }
        for uid, item := range cachePriceMap {
            response.PriceMap[uid] = item
        }
        if len(missCacheCoachId) == 0 {
            return response, nil
        }
        request.CoachIds = missCacheCoachId
    }

    log.DebugWithCtx(c, "CalculatePrice: request: %v", request)
    // Step 1: Check if the CoachIds in the request is valid
    if len(request.CoachIds) == 0 {
        log.ErrorWithCtx(c, "CalculatePrice failed: CoachIds is empty")
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "CoachIds is empty")
    }

    // Step 2: Check if the GameId in the request is valid
    gameDetails, err := s.gameConfigMgr.GetGameDetailByIds(c, []uint32{request.GameId})
    if err != nil {
        log.ErrorWithCtx(c, "CalculatePrice failed to get game details: %v", err)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, err.Error())
    }
    if len(gameDetails.GetConfigList()) == 0 {
        log.ErrorWithCtx(c, "CalculatePrice failed to get game details: game_id: %d, req: %v", request.GameId, request)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "invalid GameId")
    }
    basePriceConfig := gameDetails.GetConfigList()[0].GetBasePrice()

    // 批量查询教练的基础定价配置，并建立索引，教练 id 为 key
    basePriceSettingMap, err := s.pricingService.BatchGetBasePriceSetting(c, request.CoachIds, []uint32{request.GameId})
    if err != nil {
        log.ErrorWithCtx(c, "CalculatePrice failed to get base price settings: %v", err)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to get base price settings")
    }

    // 批量查询教练的标识列表，并建立索引，教练 id 为 key
    labels, addPriceLabels, err := s.labelMgr.BatchGetCoachLabels(c, request.CoachIds, []uint32{request.GameId})
    if err != nil {
        log.ErrorWithCtx(c, "CalculatePrice failed to get coach labels: %v", err)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to get coach labels")
    }

    // 批量查询教练的知名选手信息，并建立索引，教练 id 为 key
    renownedInfoList, err := s.labelMgr.BatchGetUserRenownedInfoByCoachIdsAndGameId(c, request.CoachIds, request.GameId)
    if err != nil {
        log.ErrorWithCtx(c, "CalculatePrice failed to get renowned players: %v", err)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to get renowned players")
    }
    renownedInfoMap := make(map[uint32]*store.RenownedPlayer)
    for _, info := range renownedInfoList {
        renownedInfoMap[info.UID] = info
    }

    // 批量查询大神等级
    godLevelResp, err := s.godLevelCli.BatchGetGodLevelByUid(c, &esport_godlevel.BatchGetGodLevelByUidReq{
        UidList: request.GetCoachIds(),
    })
    if err != nil {
        log.ErrorWithCtx(c, "CalculatePrice failed to get god level: %v", err)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to get god level")
    }

    //并发调用pricingService 的 CalculatePrice方法计算价格。
    priceMap := make(map[uint32]*pb.Price, len(request.CoachIds))
    var wg sync.WaitGroup
    var mu sync.Mutex
    for _, coachId := range request.CoachIds {
        wg.Add(1)
        go func(coachId uint32) {
            defer wg.Done()
            if basePriceConfig == nil || len(basePriceConfig.GetRankPriceMap()) == 0 {
                log.ErrorWithCtx(c, "CalculatePrice failed to get base price config: %v, uid:%d", basePriceConfig, coachId)
                // 如果没有配置新的定价，则返回0
                mu.Lock()
                priceMap[coachId] = &pb.Price{
                    Price:   0,
                    CoachId: coachId,
                }
                mu.Unlock()
                return
            }
            // 判断应该使用的大神等级
            level := uint32(1)
            if godLevel, ok := godLevelResp.GetUidLvs()[coachId]; ok {
                level = godLevel
            }
            if settingM, ok := basePriceSettingMap[coachId]; ok {
                if setting, ok := settingM[request.GetGameId()]; ok && setting.CoachLevel < level {
                    level = setting.CoachLevel
                }
            }
            price, err := s.pricingService.CalculatePrice(c, coachId, level, request.GetGameId(), basePriceConfig, labels[coachId], addPriceLabels[coachId], renownedInfoMap[coachId])
            if err != nil {
                log.ErrorWithCtx(c, "CalculatePrice failed to calculate price for coach %d: %v", coachId, err)
                return
            }
            mu.Lock()
            priceMap[coachId] = &pb.Price{
                Price:               price,
                GamePricingUnitType: basePriceConfig.GamePricingUnitType,
                GameId:              request.GameId,
                CoachId:             coachId,
            }
            mu.Unlock()
        }(coachId)
    }
    wg.Wait()

    // 建立缓存
    goroutineex.GoroutineWithTimeoutCtx(c, 5*time.Second, func(ctx context.Context) {
        err := s.pricingService.BatSetCoachPrice(ctx, request.GetGameId(), priceMap)
        if err != nil {
            log.WarnWithCtx(ctx, "CalculatePrice failed to set price to cache: %v", err)
        }
    })

    for uid, item := range priceMap {
        response.PriceMap[uid] = item
    }

    return response, nil
}

// CalculatePriceByGames 计算指定教练的多个技能的价格
func (s *Server) CalculatePriceByGames(c context.Context, request *pb.CalculatePriceByGamesRequest) (*pb.CalculatePriceByGamesResponse, error) {
    response := &pb.CalculatePriceByGamesResponse{
        Prices: []*pb.Price{},
    }
    log.DebugWithCtx(c, "CalculatePriceByGames: request: %v", request)
    // Step 1: Check if the CoachIds in the request is valid
    if len(request.GetGameIds()) == 0 {
        log.ErrorWithCtx(c, "CalculatePriceByGames failed: GameIds is empty")
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "GameIds is empty")
    }

    // Step 2: Check if the GameId in the request is valid
    gameDetails, err := s.gameConfigMgr.GetGameDetailByIds(c, request.GetGameIds())
    if err != nil {
        log.ErrorWithCtx(c, "CalculatePriceByGames failed to get game details: %v", err)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, err.Error())
    }
    if len(gameDetails.GetConfigList()) == 0 {
        log.ErrorWithCtx(c, "CalculatePriceByGames failed to get game details: game_ids: %+v, req: %v", request.GetGameIds(), request)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "invalid GameId")
    }
    // 建立索引
    gameDetailMap := mapz.ToMap(gameDetails.GetConfigList(), func(item *pb.EsportGameConfig) uint32 {
        return item.GetGameId()
    })

    // 批量查询教练的基础定价配置，并建立索引，教练 id 为 key
    basePriceSettingMap, err := s.pricingService.BatchGetBasePriceSetting(c, []uint32{request.GetCoachId()}, request.GetGameIds())
    if err != nil {
        log.ErrorWithCtx(c, "CalculatePriceByGames failed to get base price settings: %v", err)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to get base price settings")
    }

    // 批量查询教练的标识列表，并建立索引，教练 id 为 key
    labels, addPriceLabels, err := s.labelMgr.BatchGetCoachLabels(c, []uint32{request.GetCoachId()}, request.GetGameIds())
    if err != nil {
        log.ErrorWithCtx(c, "CalculatePriceByGames failed to get coach labels: %v", err)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to get coach labels")
    }

    // 批量查询教练的知名选手信息，并建立索引，技能 id 为 key
    renownedInfoList, err := s.labelMgr.BatchGetUserRenownedInfoByCoachIdAndGameIds(c, request.GetCoachId(), request.GetGameIds())
    if err != nil {
        log.ErrorWithCtx(c, "CalculatePriceByGames failed to get renowned players: %v", err)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to get renowned players")
    }
    renownedInfoMap := mapz.ToMap(renownedInfoList, func(item *store.RenownedPlayer) uint32 {
        return item.GameID
    })

    // 批量查询大神等级
    godLevelResp, err := s.godLevelCli.BatchGetGodLevelByUid(c, &esport_godlevel.BatchGetGodLevelByUidReq{
        UidList: []uint32{request.GetCoachId()},
    })
    if err != nil {
        log.ErrorWithCtx(c, "CalculatePriceByGames failed to get god level: %v", err)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to get god level")
    }

    //并发调用pricingService 的 CalculatePrice方法计算价格。
    prices := make([]*pb.Price, 0, len(request.GetGameIds()))
    var wg sync.WaitGroup
    var mu sync.Mutex
    for _, gameId := range request.GetGameIds() {
        wg.Add(1)
        go func(gameId uint32) {
            defer wg.Done()

            basePriceConfig := gameDetailMap[gameId].GetBasePrice()
            if basePriceConfig == nil || len(basePriceConfig.GetRankPriceMap()) == 0 {
                log.ErrorWithCtx(c, "CalculatePriceByGames failed to get base price config: %v, uid:%d, gameId: %d", basePriceConfig, request.GetCoachId(), gameId)
                // 如果没有配置新的定价，则返回0
                mu.Lock()
                prices = append(prices, &pb.Price{
                    Price:   0,
                    CoachId: request.GetCoachId(),
                    GameId:  gameId,
                })
                mu.Unlock()
                return
            }
            // 判断应该使用的大神等级
            level := uint32(1)
            if godLevel, ok := godLevelResp.GetUidLvs()[request.GetCoachId()]; ok {
                level = godLevel
            }
            if settingM, ok := basePriceSettingMap[request.GetCoachId()]; ok {
                if setting, ok := settingM[gameId]; ok && setting.CoachLevel < level {
                    level = setting.CoachLevel
                }
            }
            price, err := s.pricingService.CalculatePrice(c, request.GetCoachId(), level, gameId, basePriceConfig, labels[request.GetCoachId()], addPriceLabels[request.GetCoachId()], renownedInfoMap[gameId])
            if err != nil {
                log.ErrorWithCtx(c, "CalculatePriceByGames failed to calculate price for coach %d, gameId: %d, err: %v", request.GetCoachId(), gameId, err)
                return
            }
            mu.Lock()
            prices = append(prices, &pb.Price{
                Price:               price,
                GamePricingUnitType: basePriceConfig.GamePricingUnitType,
                GameId:              gameId,
                CoachId:             request.GetCoachId(),
            })
            mu.Unlock()
        }(gameId)
    }
    wg.Wait()
    response.Prices = prices
    return response, nil
}

// GetBasePriceSetting 获取指定教练的指定技能的基础价格配置
// 1. 检查request CoachId是否合法
// 2. 检查request GameId是否为空
// 3. 调用pricingStrategy GetBasePriceSetting方法获取基础价格配置
func (s *Server) GetBasePriceSetting(c context0.Context, request *pb.GetBasePriceSettingRequest) (*pb.GetBasePriceSettingResponse, error) {
    log.DebugWithCtx(c, "GetBasePriceSetting: request: %v", request)
    out := &pb.GetBasePriceSettingResponse{}
    // Validate CoachId
    if request.GetCoachId() == 0 {
        log.ErrorWithCtx(c, "GetBasePriceSetting param invalid CoachId: %d", request.GetCoachId())
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "invalid CoachId")
    }

    // Validate GameId
    if request.GetGameId() == 0 {
        log.ErrorWithCtx(c, "GetBasePriceSetting param invalid GameId: %d", request.GetGameId())
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "invalid GameId")
    }

    // Call GetBasePriceSetting method from labelMgr
    setting, err := s.pricingService.GetBasePriceSetting(c, request.CoachId, request.GameId)
    if err != nil {
        log.ErrorWithCtx(c, "GetBasePriceSetting: error getting base price setting", err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "error getting base price setting")
    }
    detail, err := s.gameConfigMgr.GetGameDetailById(c, request.GameId)
    if err != nil {
        log.ErrorWithCtx(c, "GetBasePriceSetting: error getting game detail", err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "error getting game detail")
    }
    out.BasePrice = detail.GetConfig().GetBasePrice()
    out.Level = setting.CoachLevel
    return out, nil
}

// SetBasePriceSetting 设置指定教练的指定技能的基础价格配置
// 1. 检查request CoachId是否合法
// 2. 检查request GameId是否为空
// 3. 检查request Level是否为空
// 4. 调用pricingStrategy SetBasePriceSetting方法获取基础价格配置
func (s *Server) SetBasePriceSetting(c context0.Context, request *pb.SetBasePriceSettingRequest) (*pb.SetBasePriceSettingResponse, error) {
    log.DebugWithCtx(c, "SetBasePriceSetting: request: %v", request)
    out := &pb.SetBasePriceSettingResponse{}
    // 检查request CoachId是否合法
    if request.CoachId == 0 {
        log.ErrorWithCtx(c, "SetBasePriceSetting param invalid CoachId: %d", request.CoachId)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "invalid CoachId")
    }

    // 检查request GameId是否为空
    if request.GameId == 0 {
        log.ErrorWithCtx(c, "SetBasePriceSetting param invalid GameId: %d", request.GameId)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "invalid GameId")
    }

    if request.Level == 0 {
        log.ErrorWithCtx(c, "SetBasePriceSetting param invalid Level: %d", request.Level)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "invalid Level")
    }

    // 调用pricingStrategy SetBasePriceSetting方法获取基础价格配置
    err := s.pricingService.SetBasePriceSetting(c, request.GetCoachId(), request.GetGameId(), request.GetLevel())
    if err != nil {
        log.ErrorWithCtx(c, "SetBasePriceSetting: error setting base price setting", err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "error setting base price setting")
    }

    return &pb.SetBasePriceSettingResponse{}, nil
}

// CreateLabel 创建标识
// 1. 检查request LabelType是否合法
// 2. 检查request apply_entry是否为空
// 3. 调用pricingStrategy CreateLabel方法完成标识创建
func (s *Server) CreateLabel(c context0.Context, request *pb.CreateLabelRequest) (*pb.CreateLabelResponse, error) {
    defer func() {
        log.DebugWithCtx(c, "CreateLabel req: %v", request)
    }()
    labelType := request.GetLabelInfo().GetLabelType()
    if labelType == 0 {
        log.ErrorWithCtx(c, "CreateLabel label_type错误, request: %v", request)
        return &pb.CreateLabelResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "label_type错误")
    }
    if (labelType == pb.LabelType_LABEL_TYPE_COACH || labelType == pb.LabelType_LABEL_TYPE_SKILL) && request.GetLabelInfo().GetApplyEntry() == "" {
        log.ErrorWithCtx(c, "CreateLabel apply_entry不能为空, request: %v", request)
        return &pb.CreateLabelResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "apply_entry不能为空")
    }

    // 非声音标签检查顺序
    if labelType != pb.LabelType_LABEL_TYPE_VOICE {
        err := s.labelMgr.CheckLabelOrder(c, request.GetLabelInfo().GetLabelType(), request.GetLabelInfo().GetDisplayOrder(), 0, pb.CheckLabelOrderRequest_CHECK_TYPE_ADD)
        if err != nil {
            log.ErrorWithCtx(c, "CreateLabel CheckAddLabelOrder failed, err:%v", err)
            return &pb.CreateLabelResponse{}, err
        }
    }

    return &pb.CreateLabelResponse{}, s.labelMgr.CreateLabel(c, &store.Label{
        LabelType:       store.LabelType(request.GetLabelInfo().GetLabelType()),
        ImageUrl:        request.GetLabelInfo().GetLabelImage(),
        IsPriced:        request.GetLabelInfo().GetHasPricing(),
        Price:           request.GetLabelInfo().GetPricingAmount(),
        ApplicableLevel: request.GetLabelInfo().GetApplicableLevel(),
        DisplayOrder:    request.GetLabelInfo().GetDisplayOrder(),
        Description:     request.GetLabelInfo().GetLabelDescription(),
        Requirements:    request.GetLabelInfo().GetLabelRequirements(),
        GameID:          request.GetLabelInfo().GetGameId(),
        ApplyEntry:      request.GetLabelInfo().GetApplyEntry(),
        Name:            request.GetLabelInfo().GetLabelName(),
    })
}

// EditLabel 编辑标识
// 1. 检查request LabelType是否合法
// 2. 检查request apply_entry是否为空
// 3. 检查request label_id大于0
// 4. 调用pricingStrategy EditLabel方法完成标识编辑
func (s *Server) EditLabel(c context0.Context, request *pb.EditLabelRequest) (*pb.EditLabelResponse, error) {
    // 1. 检查request LabelType是否合法
    labelType := request.GetLabelInfo().GetLabelType()
    if labelType == pb.LabelType_LABEL_TYPE_UNSPECIFIED {
        return &pb.EditLabelResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "label_type错误")
    }

    // 2. 检查request apply_entry是否为空
    if (labelType == pb.LabelType_LABEL_TYPE_COACH || labelType == pb.LabelType_LABEL_TYPE_SKILL) && request.GetLabelInfo().GetApplyEntry() == "" {
        return &pb.EditLabelResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "apply_entry不能为空")
    }

    // 3. 检查request label_id大于0
    if request.GetLabelInfo().GetId() == 0 {
        return &pb.EditLabelResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "label_id错误")
    }

    err := s.labelMgr.CheckLabelOrder(c, request.GetLabelInfo().GetLabelType(), request.GetLabelInfo().GetDisplayOrder(), request.GetLabelInfo().GetId(), pb.CheckLabelOrderRequest_CHECK_TYPE_EDIT)
    if err != nil {
        log.ErrorWithCtx(c, "EditLabel CheckEditLabelOrder failed, err:%v", err)
        return &pb.EditLabelResponse{}, err
    }

    // 4. 调用pricingStrategy EditLabel方法完成标识编辑
    label := &store.Label{
        ID:              request.GetLabelInfo().GetId(),
        LabelType:       store.LabelType(request.GetLabelInfo().GetLabelType()),
        ImageUrl:        request.GetLabelInfo().GetLabelImage(),
        IsPriced:        request.GetLabelInfo().GetHasPricing(),
        Price:           request.GetLabelInfo().GetPricingAmount(),
        ApplicableLevel: request.GetLabelInfo().GetApplicableLevel(),
        DisplayOrder:    request.GetLabelInfo().GetDisplayOrder(),
        Description:     request.GetLabelInfo().GetLabelDescription(),
        Requirements:    request.GetLabelInfo().GetLabelRequirements(),
        GameID:          request.GetLabelInfo().GetGameId(),
        ApplyEntry:      request.GetLabelInfo().GetApplyEntry(),
        Name:            request.GetLabelInfo().GetLabelName(),
    }
    err = s.labelMgr.EditLabel(c, label)
    if err != nil {
        log.ErrorWithCtx(c, "EditLabel Failed to edit label: %v", err)
        return &pb.EditLabelResponse{}, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to edit label")
    }

    return &pb.EditLabelResponse{}, nil
}

// DeleteLabel 删除标识
// 1. 检查request label_id大于0
// 2. 调用pricingStrategy DeleteLabel方法完成标识删除
func (s *Server) DeleteLabel(c context0.Context, request *pb.DeleteLabelRequest) (*pb.DeleteLabelResponse, error) {
    log.DebugWithCtx(c, "DeleteLabel req: %v", request)
    // Check if label_id is greater than 0
    if request.GetLabelId() <= 0 {
        err := fmt.Errorf("invalid label_id: %d", request.GetLabelId())
        log.ErrorWithCtx(c, "DeleteLabel error: %v", err)
        return &pb.DeleteLabelResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "label_id must be greater than zero")
    }

    // Call DeleteLabel method from labelMgr
    err := s.labelMgr.DeleteLabel(c, request.GetLabelId())
    if err != nil {
        log.ErrorWithCtx(c, "DeleteLabel error: %v", err)
        return &pb.DeleteLabelResponse{}, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to delete label")
    }

    return &pb.DeleteLabelResponse{}, nil
}

// ListLabels 获取标识列表
// 1. 检查request page_size范围大于0小于等于100
// 2. 检查request PageNumber 大于0
// 3. 调用pricingStrategy ListLabels方法完成标识列表获取
// 4. 根据查询到的标识列表，抽取出技能类型的标识的技能 id，然后调用 m 的GetGameDetailByIds方法批量获取技能信息，并转成map。
// 5. 组装数据返回。
func (s *Server) ListLabels(c context0.Context, request *pb.ListLabelsRequest) (*pb.ListLabelsResponse, error) {
    // Validate the pageSize
    if request.PageSize <= 0 || request.PageSize > 100 {
        log.ErrorWithCtx(c, "ListLabels page_size must be within the range (0, 100], page_size: %d", request.PageSize)
        return &pb.ListLabelsResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "page_size must be within the range (0, 100]")
    }
    if request.GetPageNumber() == 0 {
        log.ErrorWithCtx(c, "ListLabels page_number must be greater than zero")
        return &pb.ListLabelsResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "page_number must be greater than zero")
    }

    // Call the ListLabels method from the labelMgr object
    labels, count, err := s.labelMgr.ListLabels(c, int32(request.PageNumber), int32(request.PageSize), request.LabelId, store.LabelType(request.LabelType), request.GameId)
    if err != nil {
        log.ErrorWithCtx(c, "ListLabels ListLabels failed: %v", err)
        return &pb.ListLabelsResponse{}, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to list labels")
    }

    if len(labels) == 0 {
        return &pb.ListLabelsResponse{
            LabelInfoList: make([]*pb.LabelInfo, 0),
            TotalCnt:      count,
        }, nil
    }

    // Get the game detail by ids
    gameIds := make([]uint32, 0, len(labels))
    for _, label := range labels {
        gameIds = append(gameIds, label.GameID)
    }
    gameDetails, err := s.gameConfigMgr.GetGameDetailByIds(c, gameIds)
    if err != nil {
        log.ErrorWithCtx(c, "ListLabels GetGameDetailByIds failed: %v", err)
        return &pb.ListLabelsResponse{}, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to get game detail by ids")
    }
    gameDetailMap := make(map[uint32]*pb.EsportGameConfig)
    for _, gameDetail := range gameDetails.GetConfigList() {
        gameDetailMap[gameDetail.GetGameId()] = gameDetail
    }

    // Create a ListLabelsResponse object and populate it with the labels
    response := &pb.ListLabelsResponse{
        LabelInfoList: make([]*pb.LabelInfo, 0, len(labels)),
        TotalCnt:      count,
    }
    for _, label := range labels {
        elems := s.labelInfoEnToPb(label)
        if gameDetail, ok := gameDetailMap[label.GameID]; ok {
            elems.GameName = gameDetail.GetName()
        }
        response.LabelInfoList = append(response.LabelInfoList, elems)
    }

    // Return the response object and nil error
    return response, nil
}

// IssueLabel 发放标识
// 1. 检查request label_id大于0
// 2. 检查request uid大于0
// 3. 检查effective_start_time小于effective_end_time
// 4. 调用labelMgr IssueLabel方法完成标识发放
// 5. 发送消息
func (s *Server) IssueLabel(c context0.Context, request *pb.IssueLabelRequest) (*pb.IssueLabelResponse, error) {
    log.InfoWithCtx(c, "IssueLabel req: %v", request)
    // Check if label_id is greater than 0
    if request.GetLabelId() <= 0 {
        log.ErrorWithCtx(c, "IssueLabel error: %v", "label_id must be greater than zero")
        return &pb.IssueLabelResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "label_id must be greater than zero")
    }

    // Check if uid is greater than 0
    if len(request.GetUid()) == 0 {
        log.ErrorWithCtx(c, "IssueLabel error: %v", "uid list must be not empty")
        return &pb.IssueLabelResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "uid list must be not empty")
    }

    if request.GetEffectiveStartTime() >= request.GetEffectiveEndTime() {
        log.ErrorWithCtx(c, "IssueLabel error: %v", "effective_start_time must be less than effective_end_time")
        return &pb.IssueLabelResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "effective_start_time must be less than effective_end_time")
    }

    // Call IssueLabel method from labelMgr
    issueIds, err := s.labelMgr.BatchIssueLabel(c, request.GetLabelId(), time.Unix(request.GetEffectiveStartTime(), 0), time.Unix(request.GetEffectiveEndTime(), 0), request.GetUid())
    if err != nil {
        log.ErrorWithCtx(c, "IssueLabel error: %v", err)
        return &pb.IssueLabelResponse{}, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to issue label")
    }
    // 发送消息
    s.imMgr.AsyncSendLabelIssueImMsg(c, issueIds)

    return &pb.IssueLabelResponse{}, nil
}

// RevokeLabel 回收标识
// 1. 检查request label_id大于0
// 2. 调用pricingStrategy RevokeLabel方法完成标识回收
func (s *Server) RevokeLabel(c context0.Context, request *pb.RevokeLabelRequest) (*pb.RevokeLabelResponse, error) {
    // Check if label_id is greater than 0
    if request.GetLabelId() <= 0 {
        log.ErrorWithCtx(c, "RevokeLabel label_id must be greater than zero")
        return &pb.RevokeLabelResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "label_id must be greater than zero")
    }

    // Call RevokeLabel method from labelMgr
    err := s.labelMgr.RevokeLabel(c, request.GetLabelId())
    if err != nil {
        log.ErrorWithCtx(c, "RevokeLabel error: %v", err)
        return &pb.RevokeLabelResponse{}, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to revoke label")
    }

    return &pb.RevokeLabelResponse{}, nil
}

// QueryIssuanceRecords 查询发放记录
// 1. page_size范围大于0小于等于100
// 2. 调用pricingStrategy ListIssuanceRecords方法完成发放记录查询
// 3. 根据查询到的发放记录，批量查询标识信息，然后建立标识id和标识信息的映射
// 4. 根据查询到的发放记录和标识信息，组装数据返回
func (s *Server) QueryIssuanceRecords(c context0.Context, request *pb.QueryIssuanceRecordsRequest) (*pb.QueryIssuanceRecordsResponse, error) {
    log.DebugWithCtx(c, "QueryIssuanceRecords req: %v", request)
    log.DebugWithCtx(c, "QueryIssuanceRecords 2")

    // Check if page_size is within the range (0, 100]
    if request.GetPageSize() <= 0 || request.GetPageSize() > 1000 {
        log.ErrorWithCtx(c, "QueryIssuanceRecords param page_size must be within the range (0, 1000], got %d", request.GetPageSize())
        return &pb.QueryIssuanceRecordsResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "page_size must be within the range (0, 100]")
    }
    // 校验时间段参数，必须同时为 0 或者同时不为 0 且结束时间大于开始时间
    if !((request.GetEffectiveStartTime() == 0 && request.GetEffectiveEndTime() == 0) || (request.GetEffectiveStartTime() != 0 && request.GetEffectiveEndTime() > request.GetEffectiveStartTime())) {
        log.ErrorWithCtx(c, "QueryIssuanceRecords param effective_start_time and effective_end_time must be both 0 or both not 0 and effective_end_time > effective_start_time")
        return &pb.QueryIssuanceRecordsResponse{}, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "effective_start_time and effective_end_time must be both 0 or both not 0 and effective_end_time > effective_start_time")
    }

    // Call ListIssuanceRecords method from labelMgr
    records, total, err := s.labelMgr.ListIssuanceRecords(c, request.GetPageNumber(), request.GetPageSize(), request.GetCoachIds(), request.GetLabelId(), request.GetEffectiveStartTime(), request.GetEffectiveEndTime(),
        store.IssuanceRecordStatus(request.GetStatus()), store.LabelType(request.GetLabelType()))
    if err != nil {
        log.ErrorWithCtx(c, "QueryIssuanceRecords ListIssuanceRecords error: %v", err)
        return &pb.QueryIssuanceRecordsResponse{}, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to list issuance records")
    }

    // Create a map for label id and label information
    labelInfoMap := make(map[uint32]*store.Label)
    labelIds := make([]uint32, 0, len(records))
    for _, record := range records {
        labelIds = append(labelIds, record.LabelID)
    }
    labelInfos, err := s.labelMgr.BatchGetLabel(c, labelIds)
    if err != nil {
        log.ErrorWithCtx(c, "QueryIssuanceRecords BatchGetLabel error: %v", err)
        return &pb.QueryIssuanceRecordsResponse{}, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "failed to batch get label")
    }
    for _, labelInfo := range labelInfos {
        labelInfoMap[labelInfo.ID] = labelInfo
    }

    // Get the game detail by ids
    gameIds := make([]uint32, 0, len(labelInfos))
    gameDetailMap := make(map[uint32]*pb.EsportGameConfig)
    for _, label := range labelInfos {
        gameIds = append(gameIds, label.GameID)
    }
    gameDetails, err := s.gameConfigMgr.GetGameDetailByIds(c, gameIds)
    if err != nil {
        log.ErrorWithCtx(c, "QueryIssuanceRecords GetGameDetailByIds failed: %v", err)
    } else {
        for _, gameDetail := range gameDetails.GetConfigList() {
            gameDetailMap[gameDetail.GetGameId()] = gameDetail
        }
    }

    // Create the response
    response := &pb.QueryIssuanceRecordsResponse{
        IssuanceRecordList: nil,
        TotalCnt:           total,
    }
    for _, record := range records {
        elems := &pb.IssuanceRecord{
            Id:        record.ID,
            Uid:       record.CoachID,
            StartTime: record.EffectiveDuration.StartTime.Unix(),
            EndTime:   record.EffectiveDuration.EndTime.Unix(),
            LabelInfo: &pb.LabelInfo{},
        }
        if labelInfo, ok := labelInfoMap[record.LabelID]; ok {
            elems.LabelInfo = s.labelInfoEnToPb(labelInfo)
            if gameInfo, ok := gameDetailMap[labelInfo.GameID]; ok {
                elems.LabelInfo.GameName = gameInfo.Name
            }
        }
        response.IssuanceRecordList = append(response.IssuanceRecordList, elems)
    }

    return response, nil
}

// AddRenownedPlayer 添加知名玩家
// 1. 检查request info_list非空
// 2. 遍历info_list
// 3. 检查uid大于0，price大于0，
// 4. 调用mgr IsCoachHasGame 判断教练有没有指定的 skill_name
// 3. 调用pricingStrategy 的方法 AddRenownedPlayer方法完成知名玩家添加
func (s *Server) AddRenownedPlayer(c context0.Context, request *pb.AddRenownedPlayerRequest) (*pb.AddRenownedPlayerResponse, error) {
    out := &pb.AddRenownedPlayerResponse{}
    // Check if the info_list in the request is not empty
    if len(request.GetInfoList()) == 0 {
        log.ErrorWithCtx(c, "AddRenownedPlayer failed: info_list is empty")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "列表为空")
    }

    uidList := make([]uint32, 0, len(request.GetInfoList()))
    for _, info := range request.GetInfoList() {
        uidList = append(uidList, info.GetUid())
    }
    // 检查uid合法性
    accountMap, sErr := s.accountCli.GetUsersMap(c, uidList)
    if sErr != nil {
        log.ErrorWithCtx(c, "AddRenownedPlayer failed to get users map: %v", sErr)
        return out, sErr
    }
    for _, info := range request.GetInfoList() {
        if info.GetSkillName() == "" {
            log.ErrorWithCtx(c, "AddRenownedPlayer failed: GetSkillName is empty")
            return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "存在游戏名称为空的记录")
        }

        if accountMap[info.GetUid()] == nil {
            log.ErrorWithCtx(c, "AddRenownedPlayer failed to get user info, uid:%d", info.GetUid())
            return out, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, fmt.Sprintf("获取用户信息失败，uid:%d", info.GetUid()))
        }
    }

    // 检查教练身份
    notCoachList, err := s.m.CheckCoachList(c, uidList)
    if err != nil {
        log.ErrorWithCtx(c, "AddRenownedPlayer failed to check coach list: %v", err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, "检查教练身份失败")
    }
    if len(notCoachList) > 0 {
        log.ErrorWithCtx(c, "AddRenownedPlayer failed, not coach list: %v", notCoachList)
        return out, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, fmt.Sprintf("存在非教练用户，uid列表：%+v", notCoachList))
    }

    playerItemList := make([]*mgr.PlayerItem, 0, len(request.GetInfoList()))
    for _, info := range request.GetInfoList() {
        // uid和price必须大于0
        if info.GetUid() <= 0 || info.GetPrice() <= 0 {
            log.ErrorWithCtx(c, "uid and price must be greater than zero, uid: %d, price: %d", info.GetUid(), info.GetPrice())
            return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "uid/价格必须大于0")
        }

        // 检查教练是否拥有技能
        hasGame, err := s.m.IsCoachHasGame(c, info.GetUid(), info.GetSkillName())
        if err != nil {
            log.ErrorWithCtx(c, "failed to check if coach has game: %v, uid: %d, price: %d", err, info.GetUid(), info.GetPrice())
            return out, err
        }
        if hasGame == nil {
            errMsg := fmt.Sprintf("教练[ttid:%s]不存在技能[%s]", accountMap[info.GetUid()].GetAlias(), info.GetSkillName())
            log.ErrorWithCtx(c, "failed to check if coach has game: %s, uid: %d, price: %d", errMsg, info.GetUid(), info.GetPrice())
            return out, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillCommonErr, errMsg)
        }

        playerItemList = append(playerItemList, &mgr.PlayerItem{
            UID:      info.GetUid(),
            GameID:   hasGame.GameId,
            Price:    uint32(info.GetPrice()),
            GameName: hasGame.GameName,
            TTid:     accountMap[info.GetUid()].GetAlias(),
        })
        log.InfoWithCtx(c, "AddRenownedPlayer: uid:%d, game_id:%d, price:%d", info.GetUid(), hasGame.GameId, info.GetPrice())
    }
    // 批量入库
    err = s.labelMgr.BatchAddRenownedPlayer(c, playerItemList)
    if err != nil {
        log.ErrorWithCtx(c, "failed to add renowned player: %v", err)
        return out, err
    }
    return out, nil
}

// ListRenownedPlayers 获取知名玩家列表
// 1. 检查request page_size范围大于0小于等于100
// 2. 如果request 的GameName不为空，则调用mgr 的BatchGetGameInfoByNames方法获取游戏的 id。
// 2. 调用pricingStrategy ListRenownedPlayers方法完成知名玩家列表获取
func (s *Server) ListRenownedPlayers(c context0.Context, request *pb.ListRenownedPlayersRequest) (*pb.ListRenownedPlayersResponse, error) {
    out := &pb.ListRenownedPlayersResponse{}
    // Check if page_size is within the range (0, 100]
    if request.GetPageSize() <= 0 || request.GetPageSize() > 100 {
        log.ErrorWithCtx(c, "ListRenownedPlayers page_size out of range, it should be in (0, 100]")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "page_size out of range, it should be in (0, 100]")
    }
    if request.GetPageNumber() <= 0 {
        log.ErrorWithCtx(c, "ListRenownedPlayers page_number must be greater than zero")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "page_number must be greater than 0")
    }
    gameID := make([]uint32, 0)
    // If GameName is not empty, get the game id
    if request.GetGameName() != "" {
        gameInfo, err := s.gameConfigMgr.GetGameListByGameNameFuzzy(c, request.GetGameName())
        if err != nil {
            log.ErrorWithCtx(c, "ListRenownedPlayers failed to get game info by name: %v", err)
            return out, err
        }
        if len(gameInfo) <= 0 {
            log.ErrorWithCtx(c, "ListRenownedPlayers game not found: %s", request.GetGameName())
            // 返回空列表
            return out, nil
        }
        for _, game := range gameInfo {
            gameID = append(gameID, game.GetGameId())
        }

    }

    // Get the list of renowned players
    players, total, err := s.labelMgr.ListRenownedPlayers(c, request.GetPageNumber(), request.GetPageSize(), request.GetUid(), gameID)
    if err != nil {
        log.ErrorWithCtx(c, "ListRenownedPlayers failed to list renowned players: %v", err)
        return out, err
    }

    if len(players) == 0 {
        log.DebugWithCtx(c, "ListRenownedPlayers no renowned players found, req:%+v", request)
        return out, nil
    }
    pbPlayerList, err := s.renownedPlayersEnToPbList(c, players)
    if err != nil {
        log.ErrorWithCtx(c, "ListRenownedPlayers failed to convert renowned players to pb: %v", err)
        return out, err
    }
    return &pb.ListRenownedPlayersResponse{
        PlayerInfos: pbPlayerList,
        TotalCnt:    total,
    }, nil
}

// BatchRemoveRenownedPlayers 批量删除知名玩家
// 1. 检查request IdList非空
// 2. 调用mgr BatchRemoveRenownedPlayers方法完成知名玩家批量删除
func (s *Server) BatchRemoveRenownedPlayers(c context0.Context, request *pb.BatchRemoveRenownedPlayersRequest) (*pb.BatchRemoveRenownedPlayersResponse, error) {
    out := &pb.BatchRemoveRenownedPlayersResponse{}
    // Check if the IdList in the request is not empty
    if len(request.GetIdList()) == 0 {
        log.ErrorWithCtx(c, "IdList is empty")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "列表为空")
    }

    // Call the BatchRemoveRenownedPlayers method from the mgr package
    err := s.labelMgr.BatchRemoveRenownedPlayers(c, request.GetIdList())
    if err != nil {
        // Log the error and return it if an error occurs
        log.ErrorWithCtx(c, "failed to remove renowned players: %v", err)
        return out, err
    }

    // Return a new instance of BatchRemoveRenownedPlayersResponse and nil error
    return out, nil
}

// BatchGetUserRenownedInfo 批量获取用户知名选手
// 1. 检查game_id大于0
// 2. 检查uid_list非空
// 3. 调用pricingStrategy BatchGetUserRenownedInfoByCoachIdsAndGameId方法完成用户名人信息批量获取
func (s *Server) BatchGetUserRenownedInfo(c context0.Context, request *pb.BatchGetUserRenownedInfoRequest) (*pb.BatchGetUserRenownedInfoResponse, error) {
    p := &pb.BatchGetUserRenownedInfoResponse{}
    // Check if game_id is greater than 0
    if request.GetGameId() <= 0 {
        log.ErrorWithCtx(c, "game_id must be greater than zero")
        return p, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "game_id must be greater than zero")
    }

    // Check if uid_list is not empty
    if len(request.GetUidList()) == 0 {
        log.ErrorWithCtx(c, "uid_list is empty")
        return p, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "uid_list is empty")
    }

    // Call the BatchGetUserRenownedInfo method from the labelMgr
    renownedPlayers, err := s.labelMgr.BatchGetUserRenownedInfoByCoachIdsAndGameId(c, request.GetUidList(), request.GetGameId())
    if err != nil {
        log.ErrorWithCtx(c, "failed to get renowned players: %v", err)
        return p, err
    }

    // Convert the renownedPlayers to the protobuf message
    p.UserRenownedInfoMap = make(map[uint32]*pb.BatchGetUserRenownedInfoResponse_UserRenownedInfo)
    for _, player := range renownedPlayers {
        p.UserRenownedInfoMap[player.UID] = &pb.BatchGetUserRenownedInfoResponse_UserRenownedInfo{}
    }

    return p, nil
}

// BatchGetGameInfoByNames 批量获取游戏信息
// 1. 检查name_list非空
// 2. 遍历name_list,检查name非空
// 3. 调用mgr BatchGetGameInfoByNames方法完成游戏信息批量获取
func (s *Server) BatchGetGameInfoByNames(c context.Context, request *pb.BatchGetGameInfoByNamesRequest) (*pb.BatchGetGameInfoByNamesResponse, error) {
    return s.m.BatchGetGameInfoByNames(c, request.GetNameList())
}

// UpdateRenownedPlayer 更新知名玩家
// 检查价格大于0
// 检查id大于0
func (s *Server) UpdateRenownedPlayer(c context0.Context, request *pb.UpdateRenownedPlayerRequest) (*pb.UpdateRenownedPlayerResponse, error) {
    out := &pb.UpdateRenownedPlayerResponse{}
    if request.GetOrderPrice() <= 0 {
        log.ErrorWithCtx(c, "UpdateRenownedPlayer price must be greater than zero")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "price must be greater than zero")
    }
    if request.GetId() <= 0 {
        log.ErrorWithCtx(c, "UpdateRenownedPlayer id must be greater than zero")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "id must be greater than zero")
    }
    err := s.labelMgr.UpdateRenownedPlayer(c, request.GetId(), request.GetOrderPrice())
    if err != nil {
        log.ErrorWithCtx(c, "UpdateRenownedPlayer failed, err:%v", err)
        return out, err
    }
    return out, nil
}

func (s *Server) GetUserSkillStatus(ctx context.Context, request *pb.GetUserSkillStatusRequest) (*pb.GetUserSkillStatusResponse, error) {
    return s.m.GetUserSkillStatus(ctx, request.GetUid())
}

func (s *Server) GetMinimumPrice(c context0.Context, request *pb.GetMinimumPriceRequest) (*pb.GetMinimumPriceResponse, error) {
    out := &pb.GetMinimumPriceResponse{
        MinimumPrice: s.m.GetMinimumPrice(),
    }
    return out, nil
}

func (s *Server) GetUserSkillByGameId(ctx context.Context, request *pb.GetUserSkillByGameIdRequest) (*pb.GetUserSkillByGameIdResponse, error) {
    return s.m.GetUserSkillByGameId(ctx, request.GetUid(), request.GetGameId())
}

func (s *Server) GetAllGameSimpleInfo(c context.Context, request *pb.GetAllGameSimpleInfoRequest) (*pb.GetAllGameSimpleInfoResponse, error) {
    return s.m.GetAllGameSimpleInfo(c)
}

func (s *Server) SetUserSkillRiskAuditType(ctx context.Context, request *pb.SetUserSkillRiskAuditTypeRequest) (*pb.SetUserSkillRiskAuditTypeResponse, error) {
    out := &pb.SetUserSkillRiskAuditTypeResponse{}
    return out, s.m.SetSkillRiskAuditType(ctx, request.GetAuditToken(), request.GetSceneCode(), request.GetUid(), request.GetAuditType())
}

func (s *Server) GetGameDetailById(c context.Context, request *pb.GetGameDetailByIdRequest) (*pb.GetGameDetailByIdResponse, error) {
    return s.m.GetGameDetailById(c, request.GetGameId())
}

func (s *Server) BatchGetAuditSkill(ctx context.Context, request *pb.BatchGetAuditSkillRequest) (*pb.BatchGetAuditSkillResponse, error) {
    return s.m.BatchGetAuditSkill(ctx, request)
}

func (s *Server) GetTopGameList(ctx context.Context, request *pb.GetTopGameListRequest) (*pb.GetTopGameListResponse, error) {
    return s.m.GetTopGameList(ctx, request.GetUid(), request.GetGameType())
}

func (s *Server) GetSwitch(ctx context.Context, request *pb.GetSwitchRequest) (*pb.GetSwitchResponse, error) {
    return s.m.GetSwitchStatus(ctx, request.GetUid())
}

// SetSwitch 废弃
func (s *Server) SetSwitch(ctx context.Context, request *pb.SetSwitchRequest) (*pb.SetSwitchResponse, error) {
    return &pb.SetSwitchResponse{}, nil
}

func (s *Server) ShutDown() {
    s.m.Close(context.Background())
    _ = s.godLevelKfkSub.Close()
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
    return req, nil
}

// AddEsportGameConfig ----------------- 运营后台接口 -----------------
func (s *Server) AddEsportGameConfig(ctx context.Context, req *pb.AddEsportGameConfigRequest) (*pb.AddEsportGameConfigResponse, error) {
    out := &pb.AddEsportGameConfigResponse{}
    if req.GetEsportGameConfig().GetBasePrice() == nil {
        log.ErrorWithCtx(ctx, "AddEsportGameConfig failed, req:%+v", req)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "基础定价配置不能为空")
    }
    for _, section := range req.GetEsportGameConfig().GameInformationList {
        if section.SelectType == pb.GameInformation_GAME_INFORMATION_SELECT_TYPE_INVALID {
            log.ErrorWithCtx(ctx, "AddEsportGameConfig failed 请选择是否多选, req:%+v", req)
            return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "请选择是否多选")
        }
        if section.SectionName == guaranteeSectionName {
            if section.SelectType == pb.GameInformation_GAME_INFORMATION_SELECT_TYPE_MULTI {
                log.ErrorWithCtx(ctx, "AddEsportGameConfig failed, 包赢承诺只能单选, req:%+v", req)
                return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "包赢承诺只能单选")
            }
        }
    }
    err := s.gameConfigMgr.AddEsportGameConfig(ctx, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "AddEsportGameConfig failed, req:%+v, err:%v", req, err)
        return out, err
    }
    return out, nil
}

// UpdateEsportGameConfig
func (s *Server) UpdateEsportGameConfig(ctx context.Context, req *pb.UpdateEsportGameConfigRequest) (*pb.UpdateEsportGameConfigResponse, error) {
    out := &pb.UpdateEsportGameConfigResponse{}
    if req.GetConfig().GetBasePrice() == nil {
        log.ErrorWithCtx(ctx, "UpdateEsportGameConfig failed, req:%+v", req)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "基础定价配置不能为空")
    }
    for _, rankPrice := range req.GetConfig().GetBasePrice().GetRankPriceMap() {
        if rankPrice <= 0 {
            log.ErrorWithCtx(ctx, "UpdateEsportGameConfig failed, req:%+v", req)
            return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "等级价格不能为0")
        }
    }

    for _, section := range req.GetConfig().GameInformationList {
        if section.SelectType == pb.GameInformation_GAME_INFORMATION_SELECT_TYPE_INVALID {
            log.ErrorWithCtx(ctx, "UpdateEsportGameConfig failed 请选择是否多选, req:%+v", req)
            return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "请选择是否多选")
        }
        if section.SectionName == guaranteeSectionName {
            if section.SelectType == pb.GameInformation_GAME_INFORMATION_SELECT_TYPE_MULTI {
                log.ErrorWithCtx(ctx, "UpdateEsportGameConfig failed, 包赢承诺只能单选, req:%+v", req)
                return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "包赢承诺只能单选")
            }
        }
    }

    err := s.m.UpdateEsportGameConfig(ctx, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateEsportGameConfig failed, req:%+v, err:%v", req, err)
        return out, err
    }
    return out, nil

}

// DeleteEsportGameConfig
func (s *Server) DeleteEsportGameConfig(ctx context.Context, req *pb.DeleteEsportGameConfigRequest) (*pb.DeleteEsportGameConfigResponse, error) {
    out := &pb.DeleteEsportGameConfigResponse{}
    if err := s.m.DeleteEsportGameConfig(ctx, req); err != nil {
        log.ErrorWithCtx(ctx, "DeleteEsportGameConfig failed, req:%+v, err:%v", req, err)
        return out, err
    }
    return out, nil
}

// GetEsportGameConfigListByPage
// 1. 查询当前的大神等级配置( godLevelCli GetGodLevelConfs )
// 2. 分页查询游戏配置(GetEsportGameConfigListByPage)
// 3. 遍历查询到的游戏列表，根据大神等级配置，校正游戏的 BasePrice 的 RankPriceMap 字段（key 是等级，value 是价格，如果是新配置的等级，则设置为 0）
// 4. 返回结果
func (s *Server) GetEsportGameConfigListByPage(ctx context.Context, req *pb.GetEsportGameConfigListByPageRequest) (*pb.GetEsportGameConfigListByPageResponse, error) {
    out := &pb.GetEsportGameConfigListByPageResponse{}
    // 查询当前的大神等级配置
    godLevelConfs, err := s.godLevelCli.GetGodLevelConfs(ctx, &esport_godlevel.GetGodLevelConfsReq{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetEsportGameConfigListByPage GetGodLevelConfs failed, err:%v", err)
        return out, err
    }

    // 分页查询游戏配置
    gameConfigList, err := s.gameConfigMgr.GetEsportGameConfigListByPage(ctx, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetEsportGameConfigListByPage GetEsportGameConfigListByPage failed, req:%+v, err:%v", req, err)
        return out, err
    }

    // 遍历查询到的游戏列表，根据大神等级配置，校正游戏的 BasePrice 的 RankPriceMap 字段
    for _, gameConfig := range gameConfigList.GetItemList() {
        tempBasePrice := make(map[uint32]uint32)
        if gameConfig.BasePrice == nil || gameConfig.BasePrice.RankPriceMap == nil {
            gameConfig.BasePrice = &pb.BasePrice{
                RankPriceMap: make(map[uint32]uint32),
            }
        }
        for _, godLevelConf := range godLevelConfs.Confs {
            tempBasePrice[godLevelConf.GetLevel()] = gameConfig.BasePrice.RankPriceMap[godLevelConf.Level]
        }
        gameConfig.BasePrice.RankPriceMap = tempBasePrice
    }

    // 返回结果
    return gameConfigList, nil
}

// GetUserAuditSkill
func (s *Server) GetUserAuditSkill(ctx context.Context, req *pb.GetUserAuditSkillRequest) (*pb.GetUserAuditSkillResponse, error) {
    return s.m.GetUserAuditSkill(ctx, req)
}

// SetUserSkillAuditType
func (s *Server) SetUserSkillAuditType(ctx context.Context, req *pb.SetUserSkillAuditTypeRequest) (*pb.SetUserSkillAuditTypeResponse, error) {
    out := &pb.SetUserSkillAuditTypeResponse{}
    err := s.m.SetUserSkillAuditType(ctx, req)
    return out, err
}

// GetUserCurrentSkill 获取用户当前的技能列表
func (s *Server) GetUserCurrentSkill(ctx context.Context, req *pb.GetUserCurrentSkillRequest) (*pb.GetUserCurrentSkillResponse, error) {
    return s.m.GetUserCurrentSkill(ctx, req.GetUid(), req.GetWithUrlPrefix())
}

// ModifyUserSkill
func (s *Server) ModifyUserSkill(ctx context.Context, req *pb.ModifyUserSkillRequest) (*pb.ModifyUserSkillResponse, error) {
    return s.m.ModifyUserSkill(ctx, req)
}

// DelUserSkill
func (s *Server) DelUserSkill(ctx context.Context, req *pb.DelUserSkillRequest) (*pb.DelUserSkillResponse, error) {
    out := &pb.DelUserSkillResponse{}
    return out, s.m.DelUserSkill(ctx, req)
}

// GetGameList
func (s *Server) GetGameList(ctx context.Context, req *pb.GetGameListRequest) (*pb.GetGameListResponse, error) {
    return s.m.GetGameList(ctx, req.GetGameType(), req.GetPageToken())
}

// AddUserAuditSkill
func (s *Server) AddUserAuditSkill(ctx context.Context, req *pb.AddUserAuditSkillRequest) (*pb.AddUserAuditSkillResponse, error) {
    out := &pb.AddUserAuditSkillResponse{}
    _, err := s.m.AddUserAuditSkill(ctx, req, 0)
    return out, err
}

func (s *Server) TestAddUserSkill(ctx context.Context, request *pb.TestAddUserSkillRequest) (*pb.TestAddUserSkillResponse, error) {
    return &pb.TestAddUserSkillResponse{}, s.m.TestAddUserSkill(ctx, request)
}

func (s *Server) BatchGetUserCurrentSkill(ctx context.Context, request *pb.BatchGetUserCurrentSkillRequest) (*pb.BatchGetUserCurrentSkillResponse, error) {
    return s.m.BatchGetUserCurrentSkill(ctx, request.GetUid())
}

func (s *Server) GetGameDetailByIds(ctx context.Context, request *pb.GetGameDetailByIdsRequest) (*pb.GetGameDetailByIdsResponse, error) {
    return s.m.GetGameDetailByIds(ctx, request.GetGameIds())
}

func (s *Server) FreezeCoachSkill(ctx context.Context, req *pb.FreezeCoachSkillRequest) (*pb.FreezeCoachSkillResponse, error) {
    out := &pb.FreezeCoachSkillResponse{}
    return out, s.m.FreezeCoachSkill(ctx, req)
}

func (s *Server) UnfreezeCoachSkill(ctx context.Context, req *pb.UnfreezeCoachSkillRequest) (*pb.UnfreezeCoachSkillResponse, error) {
    out := &pb.UnfreezeCoachSkillResponse{}
    return out, s.m.UnfreezeCoachSkill(ctx, req)
}

func (s *Server) GetSkillFreezeOperationList(ctx context.Context, req *pb.GetSkillFreezeOperationListRequest) (*pb.GetSkillFreezeOperationListResponse, error) {
    return s.m.GetSkillFreezeOperationList(ctx, req)
}

func (s *Server) GetUserSkillFreezeStatus(ctx context.Context, req *pb.GetUserSkillFreezeStatusRequest) (*pb.GetUserSkillFreezeStatusResponse, error) {
    return s.m.GetUserSkillFreezeStatus(ctx, req)
}

func (s *Server) BatGetUserSkillFreezeStatus(ctx context.Context, req *pb.BatGetUserSkillFreezeStatusRequest) (*pb.BatGetUserSkillFreezeStatusResponse, error) {
    return s.m.BatGetUserSkillFreezeStatus(ctx, req)
}

func (s *Server) renownedPlayersEnToPbList(ctx context.Context, in []*store.RenownedPlayer) ([]*pb.RenownedPlayerInfo, error) {
    var uids []uint32
    var gameIds []uint32
    for _, player := range in {
        uids = append(uids, player.UID)
        gameIds = append(gameIds, player.GameID)
    }
    games, err := s.gameConfigMgr.GetGameDetailByIds(ctx, gameIds)
    if err != nil {
        log.ErrorWithCtx(ctx, "renownedPlayersEnToPbList failed, in:%+v, err:%v", in, err)
        return nil, err
    }
    gameMap := make(map[uint32]*pb.EsportGameConfig)
    for _, game := range games.GetConfigList() {
        gameMap[game.GameId] = game
    }
    var out []*pb.RenownedPlayerInfo
    for _, player := range in {
        game := gameMap[player.GameID]
        info := &pb.RenownedPlayerInfo{
            Id:                  player.ID,
            Uid:                 player.UID,
            GameId:              game.GetGameId(),
            OrderPrice:          int64(player.Price),
            GamePricingUnitType: game.GetBasePrice().GetGamePricingUnitType(),
            GameName:            game.GetName(),
            UpdateTime:          player.UpdateTime.Unix(),
            CreateTime:          player.CreateTime.Unix(),
        }
        // 兼容旧游戏
        if info.GamePricingUnitType == pb.GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_INVALID {
            info.GamePricingUnitType = game.GetGamePricing().GetGamePricingUnitType()
        }
        out = append(out, info)
    }

    return out, nil
}

func (s *Server) labelInfoEnToPb(in *store.Label) *pb.LabelInfo {
    return &pb.LabelInfo{
        Id:                in.ID,
        LabelType:         pb.LabelType(in.LabelType),
        LabelImage:        in.ImageUrl,
        HasPricing:        in.IsPriced,
        PricingAmount:     in.Price,
        ApplicableLevel:   in.ApplicableLevel,
        DisplayOrder:      in.DisplayOrder,
        LabelDescription:  in.Description,
        LabelRequirements: in.Requirements,
        LabelName:         in.Name,
        GameId:            in.GameID,
        ApplyEntry:        in.ApplyEntry,
    }
}
