package mocks

import (
	"github.com/golang/mock/gomock"
	mockaccount "golang.52tt.com/clients/mocks/account"
	mockCensoringProxy "golang.52tt.com/clients/mocks/censoring-proxy"
	mockRole "golang.52tt.com/clients/mocks/esport-role"
	mockGuild "golang.52tt.com/clients/mocks/guild"
	mockPush "golang.52tt.com/clients/mocks/push-notification/v2"
	esportHallMock "golang.52tt.com/protocol/services/esport_hall"
	mockIm "golang.52tt.com/services/tt-rev/esport/common/mocks"
	"golang.52tt.com/services/tt-rev/esport/esport-skill/internal/rpc"
)

var (
	MockBc     *MockIBusinessConfManager
	MockCache  *MockICache
	MockStore  *MockIStore
	MockRpcCli *rpc.Client

	MockPushCli        *mockPush.MockIClient
	MockAccountCli     *mockaccount.MockIClient
	MockGuildCli       *mockGuild.MockIClient
	MockCensoringCli   *mockCensoringProxy.MockIClient
	MockTradeImService *mockIm.MockITradeImNotify
	MockEsportRoleCli  *mockRole.MockIClient
	MockEsportHallCli  *esportHallMock.MockEsportHallClient
)

func NewTestMockMgr(ctrl *gomock.Controller) {
	MockBc = NewMockIBusinessConfManager(ctrl)
	MockStore = NewMockIStore(ctrl)
	MockCache = NewMockICache(ctrl)

	MockAccountCli = mockaccount.NewMockIClient(ctrl)
	MockPushCli = mockPush.NewMockIClient(ctrl)
	MockGuildCli = mockGuild.NewMockIClient(ctrl)
	MockCensoringCli = mockCensoringProxy.NewMockIClient(ctrl)
	MockTradeImService = mockIm.NewMockITradeImNotify(ctrl)
	MockEsportRoleCli = mockRole.NewMockIClient(ctrl)
	MockEsportHallCli = esportHallMock.NewMockEsportHallClient(ctrl)

	MockRpcCli = &rpc.Client{
		AccountCli:        MockAccountCli,
		CensoringProxyCli: MockCensoringCli,
		PushCli:           MockPushCli,
		TradeImService:    MockTradeImService,
		EsportHallCli:     MockEsportHallCli,
		GuildCli:          MockGuildCli,
		EsportRoleCli:     MockEsportRoleCli,
	}
}
