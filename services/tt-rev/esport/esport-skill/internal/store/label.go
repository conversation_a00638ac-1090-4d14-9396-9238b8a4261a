package store

import (
	"context"
	"errors"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
	"go.mongodb.org/mongo-driver/bson"
	mongo_driver "go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/esport-skill"
	"golang.52tt.com/services/tt-rev/esport/common/collection/mapz"
	"golang.52tt.com/services/tt-rev/esport/common/collection/transform"
	context0 "golang.org/x/net/context"
	"google.golang.org/grpc/codes"
	"regexp"
	"strconv"
	"time"
)

// Label 代表可以应用于大神或技能的徽章或标识符，可能影响定价。
type Label struct {
	ID              uint32    `bson:"_id"`
	LabelType       LabelType `bson:"label_type"`
	ImageUrl        string    `bson:"image_url"`
	IsPriced        bool      `bson:"is_priced"`
	Price           uint32    `bson:"price"`
	ApplicableLevel uint32    `bson:"applicable_level"`
	DisplayOrder    uint32    `bson:"display_order"`
	Description     string    `bson:"description"`
	Requirements    string    `bson:"requirements"`
	GameID          uint32    `bson:"game_id"`
	ApplyEntry      string    `bson:"apply_entry"`
	Name            string    `bson:"name"`
	CreateTime      time.Time `bson:"create_time"`
	UpdateTime      time.Time `bson:"update_time"`
}

// IssuanceRecord 记录标签发放给大神的记录。
type IssuanceRecord struct {
	ID                uint32    `bson:"_id"`
	CoachID           uint32    `bson:"coach_id"`
	LabelID           uint32    `bson:"label_id"`
	GameId            uint32    `bson:"-"`
	EffectiveDuration *Duration `bson:"effective_duration"`
	CreateTime        time.Time `bson:"create_time,omitempty"`
	UpdateTime        time.Time `bson:"update_time"`

	PriceAdditionalSwitch bool `bson:"price_additional_switch"` // 是否开启加价模式
}

type IssuanceRecordStatus uint32

// 生效中
// 已过期
// 待生效
const (
	IssuanceRecordStatus_ISSUANCE_RECORD_STATUS_UNSPECIFIED IssuanceRecordStatus = 0
	IssuanceRecordStatus_ISSUANCE_RECORD_STATUS_EFFECTIVE   IssuanceRecordStatus = 1
	IssuanceRecordStatus_ISSUANCE_RECORD_STATUS_EXPIRED     IssuanceRecordStatus = 2
	IssuanceRecordStatus_ISSUANCE_RECORD_STATUS_PENDING     IssuanceRecordStatus = 3
)

// RenownedPlayer 代表特殊技能和定价策略的著名玩家。
type RenownedPlayer struct {
	ID         uint32    `bson:"_id"`
	UID        uint32    `bson:"uid,omitempty"`
	TTid       string    `bson:"-"`
	GameID     uint32    `bson:"game_id,omitempty"`
	GameName   string    `bson:"-"`
	Price      uint32    `bson:"price"`
	CreateTime time.Time `bson:"create_time,omitempty"`
	UpdateTime time.Time `bson:"update_time"`
}

// CoachBasePriceSetting 代表大神基础定价的配置。
type CoachBasePriceSetting struct {
	ID         string    `bson:"_id"`
	CoachLevel uint32    `bson:"coach_level"`
	GameID     uint32    `bson:"game_id"`
	CoachID    uint32    `bson:"coach_id"`
	UpdateTime time.Time `bson:"update_time"`
}

// LabelType 标签类型枚举。
type LabelType uint32

const (
	LabelType_LABEL_TYPE_UNSPECIFIED LabelType = 0
	LabelType_LABEL_TYPE_COACH       LabelType = 1
	LabelType_LABEL_TYPE_SKILL       LabelType = 2
	LabelType_LABEL_TYPE_SPECIAL     LabelType = 3
	LabelType_LABEL_TYPE_VOICE       LabelType = 4
)

// Duration 表示时间段，包含开始时间和结束时间。
type Duration struct {
	StartTime time.Time `bson:"start_time"`
	EndTime   time.Time `bson:"end_time"`
}

// UpsertCoachBasePriceSetting 创建大神的基础价格设置
func (s *Store) UpsertCoachBasePriceSetting(ctx context.Context, setting *CoachBasePriceSetting) error {
	setting.ID = fmt.Sprintf("%d_%d", setting.CoachID, setting.GameID)
	// Insert the document into the collection
	setting.UpdateTime = time.Now()
	_, err := s.basePriceSettingCollection.Collection.UpdateOne(ctx, bson.M{"_id": setting.ID}, bson.M{
		"$set": setting,
	}, options.Update().SetUpsert(true))
	if err != nil {
		return fmt.Errorf("UpsertCoachBasePriceSetting failed to UpdateOne into basePriceSettingCollection: %w", err)
	}
	log.InfoWithCtx(ctx, "UpsertCoachBasePriceSetting success, setting:%+v", setting)
	return nil
}

// GetCoachBasePriceSetting 获取大神的基础价格设置
func (s *Store) GetCoachBasePriceSetting(ctx context.Context, coachID, gameID uint32) (*CoachBasePriceSetting, error) {
	id := fmt.Sprintf("%d_%d", coachID, gameID)
	filter := bson.M{"_id": id}
	result := s.basePriceSettingCollection.Collection.FindOne(ctx, filter)
	if result.Err() != nil {
		if errors.Is(result.Err(), mongo_driver.ErrNoDocuments) {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to find in basePriceSettingCollection: %w", result.Err())
	}
	var setting CoachBasePriceSetting
	err := result.Decode(&setting)
	if err != nil {
		return nil, fmt.Errorf("failed to decode result: %w", err)
	}
	return &setting, nil
}

// DeleteCoachBasePriceSetting 删除大神的基础价格设置
func (s *Store) DeleteCoachBasePriceSetting(ctx context.Context, coachID, gameID uint32) error {
	filter := bson.M{"_id": fmt.Sprintf("%d_%d", coachID, gameID)}
	_, err := s.basePriceSettingCollection.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete from basePriceSettingCollection: %w", err)
	}
	log.InfoWithCtx(ctx, "DeleteCoachBasePriceSetting success, coachID:%d, gameID:%d", coachID, gameID)
	return nil
}

// CreateLabel 创建标签
func (s *Store) CreateLabel(ctx context.Context, label *Label) error {
	id, err := s.genIncrId(ctx, idTypeLabel)
	if err != nil {
		return fmt.Errorf("failed to genIncrId: %w", err)
	}
	label.ID = id
	_, err = s.labelCollection.InsertOne(ctx, label)
	if err != nil {
		return fmt.Errorf("failed to insert into labelCollection: %w", err)
	}
	log.InfoWithCtx(ctx, "CreateLabel success, label:%+v", label)
	return nil
}

// UpdateLabel 更新标签信息
func (s *Store) UpdateLabel(ctx context.Context, label *Label) error {
	filter := bson.M{"_id": label.ID}
	update := bson.M{"$set": label}
	_, err := s.labelCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update labelCollection: %w", err)
	}
	log.InfoWithCtx(ctx, "UpdateLabel success, label:%+v", label)
	return nil
}

// GetLabelByID 根据 ID 获取标签
func (s *Store) GetLabelByID(ctx context.Context, id uint32) (*Label, error) {
	filter := bson.M{"_id": id}
	result := s.labelCollection.Collection.FindOne(ctx, filter)
	if result.Err() != nil {
		if errors.Is(result.Err(), mongo_driver.ErrNoDocuments) {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to find in labelCollection: %w", result.Err())
	}
	var label Label
	err := result.Decode(&label)
	if err != nil {
		return nil, fmt.Errorf("failed to decode result: %w", err)
	}
	return &label, nil
}

// GetLabels 获取满足特定条件的所有标签
func (s *Store) GetLabels(ctx context.Context, pageNumber int32, pageSize int32, labelId uint32, labelType LabelType, gameId uint32) ([]*Label, error) {
	// Create a filter based on the provided parameters
	filter := bson.M{}
	if labelId != 0 {
		filter["_id"] = labelId
	}
	if labelType != 0 {
		filter["label_type"] = labelType
	} else {
		// 默认不查出声音标签
		filter["label_type"] = bson.M{"$ne": LabelType_LABEL_TYPE_VOICE}
	}
	// 如果是特殊标签或者是某个游戏的标签，则需要传入 gameId
	if labelType == LabelType_LABEL_TYPE_SPECIAL || gameId != 0 {
		filter["game_id"] = gameId
	}

	// 如果是 page_num 1 page_size 1, 且label_id不为0 则排除特色标签和声音标签
	if pageNumber == 1 && pageSize == 1 && labelId != 0 {
		filter["label_type"] = bson.M{"$nin": []uint32{uint32(LabelType_LABEL_TYPE_SPECIAL), uint32(LabelType_LABEL_TYPE_VOICE)}}
	}

	// Use the Find method to retrieve the documents that match the filter
	opts := options.Find().SetSkip(int64((pageNumber - 1) * pageSize)).SetLimit(int64(pageSize)).SetSort(bson.M{"update_time": -1})
	cursor, err := s.labelCollection.Collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find in labelCollection: %w", err)
	}
	defer cursor.Close(ctx)

	// Decode the returned documents into a slice of Label
	var labels []*Label
	if err := cursor.All(ctx, &labels); err != nil {
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	return labels, nil
}

// CountLabels 统计满足特定条件的所有标签
func (s *Store) CountLabels(ctx context.Context, labelId uint32, labelType LabelType, gameId uint32) (uint32, error) {
	// Create a filter based on the provided parameters
	filter := bson.M{}
	if labelId != 0 {
		filter["_id"] = labelId
	}
	if labelType != 0 {
		filter["label_type"] = labelType
	}
	if gameId != 0 {
		filter["game_id"] = gameId
	}

	// Use the CountDocuments method to count the documents that match the filter
	count, err := s.labelCollection.Collection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to count documents in labelCollection: %w", err)
	}

	return uint32(count), nil
}

// DeleteLabelByID 根据 ID 删除标签
func (s *Store) DeleteLabelByID(ctx context.Context, id uint32) error {
	filter := bson.M{"_id": id}
	_, err := s.labelCollection.DeleteOne(ctx, filter)
	if err != nil {
		if errors.Is(err, mongo.NoDocuments) {
			return nil
		}
		return fmt.Errorf("failed to delete from labelCollection: %w", err)
	}
	log.InfoWithCtx(ctx, "DeleteLabelByID success, id:%d", id)
	return nil
}

// CreateIssuanceRecord 创建发行记录
func (s *Store) CreateIssuanceRecord(ctx context.Context, record *IssuanceRecord) error {
	id, err := s.genIncrId(ctx, idTypeIssuanceRecord)
	if err != nil {
		return fmt.Errorf("failed to genIncrId: %w", err)
	}
	record.ID = id
	ts := time.Now()
	record.CreateTime = ts
	record.UpdateTime = ts
	_, err = s.issuanceRecordCollection.InsertOne(ctx, record)
	if err != nil {
		return fmt.Errorf("failed to insert into issuanceRecordCollection: %w", err)
	}
	log.InfoWithCtx(ctx, "CreateIssuanceRecord success, record:%+v", record)
	return nil
}

// UpdateIssuanceRecord 更新发行记录
func (s *Store) UpdateIssuanceRecord(ctx context.Context, record *IssuanceRecord) error {
	filter := bson.M{"_id": record.ID}
	record.UpdateTime = time.Now()
	update := bson.M{"$set": record}
	_, err := s.issuanceRecordCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update issuanceRecordCollection: %w", err)
	}
	log.InfoWithCtx(ctx, "UpdateIssuanceRecord success, record:%+v", record)
	return nil
}

// GetIssuanceRecordByID 根据 ID 获取发行记录
func (s *Store) GetIssuanceRecordByID(ctx context.Context, id uint32) (*IssuanceRecord, error) {
	filter := bson.M{"_id": id}
	result := s.issuanceRecordCollection.Collection.FindOne(ctx, filter)
	if result.Err() != nil {
		return nil, fmt.Errorf("failed to find in issuanceRecordCollection: %w", result.Err())
	}
	var record IssuanceRecord
	err := result.Decode(&record)
	if err != nil {
		return nil, fmt.Errorf("failed to decode result: %w", err)
	}
	return &record, nil
}

// GetIssuanceRecords 获取满足特定条件的所有发行记录
func (s *Store) GetIssuanceRecords(ctx context.Context, pageNumber int32, pageSize int32, coachIds []uint32, labelId uint32, effectiveStartTime, effectiveEndTime int64, status IssuanceRecordStatus, labelType LabelType) ([]*IssuanceRecord, error) {
	// Calculate the number of documents to skip
	skip := (pageNumber - 1) * pageSize
	filter := bson.M{}
	if len(coachIds) > 0 {
		filter["coach_id"] = bson.M{"$in": coachIds}
	}
	if labelId != 0 {
		filter["label_id"] = labelId
	}
	if effectiveStartTime != 0 && effectiveEndTime != 0 {
		// 开始时间在指定的时间段内或者结束时间在指定的时间段内
		filter["$or"] = []bson.M{
			{"effective_duration.start_time": bson.M{"$gte": time.Unix(effectiveStartTime, 0), "$lte": time.Unix(effectiveEndTime, 0)}},
			{"effective_duration.end_time": bson.M{"$gte": time.Unix(effectiveStartTime, 0), "$lte": time.Unix(effectiveEndTime, 0)}},
		}
	}

	switch status {
	case IssuanceRecordStatus_ISSUANCE_RECORD_STATUS_UNSPECIFIED:
	case IssuanceRecordStatus_ISSUANCE_RECORD_STATUS_EFFECTIVE:
		filter["effective_duration.start_time"] = bson.M{"$lte": time.Now()}
		filter["effective_duration.end_time"] = bson.M{"$gte": time.Now()}
	case IssuanceRecordStatus_ISSUANCE_RECORD_STATUS_EXPIRED:
		filter["effective_duration.end_time"] = bson.M{"$lt": time.Now()}
	case IssuanceRecordStatus_ISSUANCE_RECORD_STATUS_PENDING:
		filter["effective_duration.start_time"] = bson.M{"$gt": time.Now()}
	default:
	}

	// Create a pipeline for the aggregation
	pipeline := mongo_driver.Pipeline{
		{{"$match", filter}},
	}

	// If labelType is not 0, add a $lookup stage to the pipeline
	if labelType != LabelType_LABEL_TYPE_UNSPECIFIED {
		pipeline = append(pipeline, bson.D{{"$lookup", bson.M{
			"from": "label",
			"let":  bson.M{"label_id": "$label_id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{
					"$expr":      bson.M{"$eq": bson.A{"$_id", "$$label_id"}},
					"label_type": labelType,
				}},
			},
			"as": "label",
		}}})
		// 过滤 label 是空的记录
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"label": bson.M{"$ne": bson.A{}}}}})
	}

	// Add the remaining stages to the pipeline
	pipeline = append(pipeline,
		bson.D{{"$sort", bson.M{"update_time": -1}}},
		bson.D{{"$skip", int64(skip)}},
		bson.D{{"$limit", int64(pageSize)}},
	)

	// Execute the aggregation
	cursor, err := s.issuanceRecordCollection.Collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("failed to execute aggregation: %w", err)
	}
	defer cursor.Close(ctx)

	// Decode the documents
	var records []*IssuanceRecord
	if err := cursor.All(ctx, &records); err != nil {
		return nil, fmt.Errorf("failed to decode cursor documents: %w", err)
	}

	return records, nil
}

// CountIssuanceRecords 统计满足特定条件的所有发行记录数量
func (s *Store) CountIssuanceRecords(ctx context.Context, coachIds []uint32, labelId uint32, effectiveStartTime, effectiveEndTime int64, status IssuanceRecordStatus, labelType LabelType) (uint32, error) {
	filter := bson.M{}
	if len(coachIds) > 0 {
		filter["coach_id"] = bson.M{"$in": coachIds}
	}
	if labelId != 0 {
		filter["label_id"] = labelId
	}
	if effectiveStartTime != 0 && effectiveEndTime != 0 {
		filter["$or"] = []bson.M{
			{"effective_duration.start_time": bson.M{"$gte": time.Unix(effectiveStartTime, 0), "$lte": time.Unix(effectiveEndTime, 0)}},
			{"effective_duration.end_time": bson.M{"$gte": time.Unix(effectiveStartTime, 0), "$lte": time.Unix(effectiveEndTime, 0)}},
		}
	}
	switch status {
	case IssuanceRecordStatus_ISSUANCE_RECORD_STATUS_UNSPECIFIED:
	case IssuanceRecordStatus_ISSUANCE_RECORD_STATUS_EFFECTIVE:
		filter["effective_duration.start_time"] = bson.M{"$lte": time.Now()}
		filter["effective_duration.end_time"] = bson.M{"$gte": time.Now()}
	case IssuanceRecordStatus_ISSUANCE_RECORD_STATUS_EXPIRED:
		filter["effective_duration.end_time"] = bson.M{"$lt": time.Now()}
	case IssuanceRecordStatus_ISSUANCE_RECORD_STATUS_PENDING:
		filter["effective_duration.start_time"] = bson.M{"$gt": time.Now()}
	default:
	}

	if labelType != LabelType_LABEL_TYPE_UNSPECIFIED {
		pipeline := mongo_driver.Pipeline{
			{{"$match", filter}},
			{{"$lookup", bson.M{
				"from": "label",
				"let":  bson.M{"label_id": "$label_id"},
				"pipeline": bson.A{
					bson.M{"$match": bson.M{
						"$expr":      bson.M{"$eq": bson.A{"$$label_id", "$_id"}},
						"label_type": labelType,
					}},
				},
				"as": "label",
			}}},
			{{"$match", bson.M{"label": bson.M{"$ne": bson.A{}}}}},
			{{"$count", "count"}},
		}
		cursor, err := s.issuanceRecordCollection.Collection.Aggregate(ctx, pipeline)
		if err != nil {
			return 0, fmt.Errorf("failed to aggregate documents in issuanceRecordCollection: %w", err)
		}
		defer cursor.Close(ctx)

		type result struct {
			Count uint32 `bson:"count"`
		}
		var res result
		if cursor.Next(ctx) {
			if err := cursor.Decode(&res); err != nil {
				return 0, fmt.Errorf("failed to decode aggregation result: %w", err)
			}
		}
		return res.Count, nil
	} else {
		count, err := s.issuanceRecordCollection.Collection.CountDocuments(ctx, filter)
		if err != nil {
			return 0, fmt.Errorf("failed to count documents in issuanceRecordCollection: %w", err)
		}
		return uint32(count), nil
	}
}

// DeleteIssuanceRecordByID 根据 ID 删除发行记录
func (s *Store) DeleteIssuanceRecordByID(ctx context.Context, id uint32) error {
	filter := bson.M{"_id": id}
	_, err := s.issuanceRecordCollection.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete from issuanceRecordCollection: %w", err)
	}
	log.InfoWithCtx(ctx, "DeleteIssuanceRecordByID success, id:%d", id)
	return nil
}

// UpdateRenownedPlayer 更新知名选手信息
func (s *Store) UpdateRenownedPlayer(ctx context.Context, player *RenownedPlayer) error {
	filter := bson.M{"_id": player.ID}
	update := bson.M{"$set": player}
	_, err := s.renownedPlayerCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		if errors.Is(err, mongo.NoDocuments) {
			log.ErrorWithCtx(ctx, "UpdateRenownedPlayer not found, player:%+v", player)
			return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("记录不存在[RecordID:%d]", player.ID))
		}
		return fmt.Errorf("failed to update renownedPlayerCollection: %w", err)
	}
	log.InfoWithCtx(ctx, "UpdateRenownedPlayer success, player:%+v", player)
	return nil
}

// GetRenownedPlayerByID 根据 ID 获取知名选手信息
func (s *Store) GetRenownedPlayerByID(ctx context.Context, id uint32) (*RenownedPlayer, error) {
	filter := bson.M{"_id": id}
	result := s.renownedPlayerCollection.Collection.FindOne(ctx, filter)
	if result.Err() != nil {
		return nil, fmt.Errorf("failed to find in renownedPlayerCollection: %w", result.Err())
	}
	var player RenownedPlayer
	err := result.Decode(&player)
	if err != nil {
		return nil, fmt.Errorf("failed to decode result: %w", err)
	}
	return &player, nil
}

// GetRenownedPlayers 获取满足特定条件的所有知名选手信息
func (s *Store) GetRenownedPlayers(ctx context.Context, pageNumber int32, pageSize int32, coachUid uint32, gameIdList []uint32) ([]*RenownedPlayer, error) {
	// Create a filter based on the provided parameters
	filter := bson.M{}
	if coachUid != 0 {
		filter["uid"] = coachUid
	}
	if len(gameIdList) != 0 {
		filter["game_id"] = bson.M{"$in": gameIdList}
	}

	// Use the Find method to retrieve the documents that match the filter
	opts := options.Find().SetSkip(int64((pageNumber - 1) * pageSize)).SetLimit(int64(pageSize)).SetSort(bson.M{"update_time": -1})
	cursor, err := s.renownedPlayerCollection.Collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find in renownedPlayerCollection: %w", err)
	}
	defer cursor.Close(ctx)

	// Decode the returned documents into a slice of RenownedPlayer
	var players []*RenownedPlayer
	if err := cursor.All(ctx, &players); err != nil {
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	return players, nil
}

// CountRenownedPlayers 统计满足特定条件的所有知名选手数量
func (s *Store) CountRenownedPlayers(ctx context.Context, coachUid uint32, gameIdList []uint32) (uint32, error) {
	// Create a filter based on the provided parameters
	filter := bson.M{}
	if coachUid != 0 {
		filter["uid"] = coachUid
	}
	if len(gameIdList) != 0 {
		filter["game_id"] = bson.M{"$in": gameIdList}
	}

	// Use the CountDocuments method to count the documents that match the filter
	count, err := s.renownedPlayerCollection.Collection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to count documents in renownedPlayerCollection: %w", err)
	}

	return uint32(count), nil
}

// DeleteRenownedPlayerByID 根据 ID 删除知名选手信息
func (s *Store) DeleteRenownedPlayerByID(ctx context.Context, id uint32) error {
	filter := bson.M{"_id": id}
	_, err := s.renownedPlayerCollection.DeleteOne(ctx, filter)
	if err != nil {
		if errors.Is(err, mongo.NoDocuments) {
			log.InfoWithCtx(ctx, "DeleteRenownedPlayerByID not found, default success, id:%d", id)
			return nil
		}
		return fmt.Errorf("failed to delete from renownedPlayerCollection: %w", err)
	}
	log.InfoWithCtx(ctx, "DeleteRenownedPlayerByID success, id:%d", id)
	return nil
}

// DeleteIssuanceRecordByLabelID 根据 标识 id 删除发放记录
func (s *Store) DeleteIssuanceRecordByLabelID(ctx context.Context, labelId uint32) error {
	filter := bson.M{"label_id": labelId}
	_, err := s.issuanceRecordCollection.DeleteMany(ctx, filter)
	if err != nil {
		if errors.Is(err, mongo.NoDocuments) {
			return nil
		}
		return fmt.Errorf("failed to delete from issuanceRecordCollection: %w", err)
	}
	log.InfoWithCtx(ctx, "DeleteIssuanceRecordByLabelID success, labelId:%d", labelId)
	return nil
}

// BatchDeleteRenownedPlayers 批量删除知名选手信息
func (s *Store) BatchDeleteRenownedPlayers(ctx context.Context, ids []uint32) error {
	filter := bson.M{"_id": bson.M{"$in": ids}}
	_, err := s.renownedPlayerCollection.DeleteMany(ctx, filter)
	if err != nil {
		if errors.Is(err, mongo.NoDocuments) {
			log.InfoWithCtx(ctx, "BatchDeleteRenownedPlayers not found, default success,ids:%+v", ids)
			return nil
		}
		return fmt.Errorf("failed to batch delete from renownedPlayerCollection: %w", err)
	}
	log.InfoWithCtx(ctx, "BatchDeleteRenownedPlayers success, ids:%+v", ids)
	return nil
}

// BatchGetUserRenownedInfoByCoachIdsAndGameId 批量获取指定的游戏关联的多个教练的知名玩家信息
func (s *Store) BatchGetUserRenownedInfoByCoachIdsAndGameId(ctx context.Context, coachIds []uint32, gameId uint32) ([]*RenownedPlayer, error) {
	// Create a filter based on the provided parameters
	filter := bson.M{"uid": bson.M{"$in": coachIds}, "game_id": gameId}

	// Use the Find method to retrieve the documents that match the filter
	cursor, err := s.renownedPlayerCollection.Collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find in renownedPlayerCollection: %w", err)
	}
	defer cursor.Close(ctx)

	// Decode the returned documents into a slice of RenownedPlayer
	var players []*RenownedPlayer
	if err := cursor.All(ctx, &players); err != nil {
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	return players, nil
}

// BatchGetUserRenownedInfoByCoachIdAndGameIds 批量获取指定的教练关联的多个游戏的知名玩家信息
func (s *Store) BatchGetUserRenownedInfoByCoachIdAndGameIds(ctx context.Context, coachId uint32, gameIds []uint32) ([]*RenownedPlayer, error) {
	// Create a filter based on the provided parameters
	filter := bson.M{"game_id": bson.M{"$in": gameIds}, "uid": coachId}

	// Use the Find method to retrieve the documents that match the filter
	cursor, err := s.renownedPlayerCollection.Collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find in renownedPlayerCollection: %w", err)
	}
	defer cursor.Close(ctx)

	// Decode the returned documents into a slice of RenownedPlayer
	var players []*RenownedPlayer
	if err := cursor.All(ctx, &players); err != nil {
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	return players, nil
}

// BatchCreateIssuanceRecord 批量创建发放记录
func (s *Store) BatchCreateIssuanceRecord(ctx context.Context, records []*IssuanceRecord) error {
	insertData := make([]interface{}, len(records))
	for i, record := range records {
		id, err := s.genIncrId(ctx, idTypeIssuanceRecord)
		if err != nil {
			return fmt.Errorf("failed to genIncrId: %w", err)
		}
		record.ID = id
		insertData[i] = record
	}
	// Insert the documents into the collection
	_, err := s.issuanceRecordCollection.InsertMany(ctx, insertData)
	if err != nil {
		return fmt.Errorf("failed to insert into issuanceRecordCollection: %w", err)
	}
	return nil
}

// UpdateLabelPriceAdditionalSwitch 设置用户的标签价格加价开关
func (s *Store) UpdateLabelPriceAdditionalSwitch(ctx context.Context, coachId, labelId uint32, priceAdditionalSwitch bool) error {
	filter := bson.M{"coach_id": coachId, "label_id": labelId}
	update := bson.M{"$set": bson.M{"price_additional_switch": priceAdditionalSwitch}}
	_, err := s.issuanceRecordCollection.UpdateMany(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update labelCollection: %w", err)
	}
	log.InfoWithCtx(ctx, "UpdateLabelPriceAdditionalSwitch success,coachId：%d labelId:%d, priceAdditionalSwitch:%t", coachId, labelId, priceAdditionalSwitch)
	return nil
}

// BatchGetLabel 批量获取标签
func (s *Store) BatchGetLabel(ctx context.Context, ids []uint32) ([]*Label, error) {
	if len(ids) == 0 {
		log.WarnWithCtx(ctx, "BatchGetLabel failed, ids is empty")
		return []*Label{}, nil
	}
	// Create a filter based on the provided IDs
	filter := bson.M{"_id": bson.M{"$in": ids}}

	// Use the Find method to retrieve the documents that match the filter
	cursor, err := s.labelCollection.Collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find in labelCollection: %w", err)
	}
	defer cursor.Close(ctx)

	// Decode the returned documents into a slice of Label
	var labels []*Label
	if err := cursor.All(ctx, &labels); err != nil {
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	return labels, nil
}

// GetCoachAvailableIssuanceRecord 获取指定教练的当前生效的发放记录
func (s *Store) GetCoachAvailableIssuanceRecord(ctx context.Context, coachId uint32) ([]*IssuanceRecord, error) {
	// Create a filter based on the provided coachId and current time
	now := time.Now()
	filter := bson.M{"coach_id": coachId, "effective_duration.start_time": bson.M{"$lte": now}, "effective_duration.end_time": bson.M{"$gte": now}}

	// Use the Find method to retrieve the documents that match the filter
	cursor, err := s.issuanceRecordCollection.Collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find in issuanceRecordCollection: %w", err)
	}
	defer cursor.Close(ctx)

	// Decode the returned documents into a slice of IssuanceRecord
	var records []*IssuanceRecord
	if err := cursor.All(ctx, &records); err != nil {
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	return records, nil
}

// GetLabelsByCoachTypeAndGames  获取大神类型的标签，如果 gameIds 不为空，则还会获取和技能绑定的标签
func (s *Store) GetLabelsByCoachTypeAndGames(ctx context.Context, gameIds ...uint32) ([]*Label, error) {
	// Step 1: Create a filter based on the provided game IDs and label types
	filter := bson.M{
		"label_type": LabelType_LABEL_TYPE_COACH,
	}
	if len(gameIds) != 0 {
		filter = bson.M{
			"$or": []bson.M{
				{"game_id": bson.M{"$in": gameIds}},
				{"label_type": LabelType_LABEL_TYPE_COACH},
			},
		}
	}

	// Step 2: Use the Find method to retrieve the documents that match the filter
	cursor, err := s.labelCollection.Collection.Find(ctx, filter)
	if err != nil {
		// Step 4: If an error occurs during any of these operations, wrap it using fmt.Errorf and return it
		return nil, fmt.Errorf("failed to find in labelCollection: %w", err)
	}
	defer cursor.Close(ctx)

	// Step 3: Decode the returned documents into a slice of Label
	var labels []*Label
	if err := cursor.All(ctx, &labels); err != nil {
		// Step 4: If an error occurs during any of these operations, wrap it using fmt.Errorf and return it
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	// Step 5: If everything goes well, return the slice of Label and nil for the error
	return labels, nil
}

// BatchAddRenownedPlayer 将指定教练的指定游戏设置成知名玩家，并设置特殊定价。
func (s *Store) BatchAddRenownedPlayer(ctx context.Context, itemList []*RenownedPlayer) error {
	// Step 1: Create a slice of interface{} to store the documents to insert
	insertData := make([]interface{}, len(itemList))
	for i, item := range itemList {
		id, err := s.genIncrId(ctx, idTypeRenownedPlayer)
		if err != nil {
			return fmt.Errorf("failed to genIncrId: %w", err)
		}
		item.ID = id
		insertData[i] = item
	}

	// Step 2: Insert the documents into the collection
	_, err := s.renownedPlayerCollection.InsertMany(ctx, insertData)
	if err != nil {
		if mongo_driver.IsDuplicateKeyError(err) {
			uid, gameId := extractDuplicateValuesFromErrMsg(err.Error())
			gameMap, userMap := s.getGameAndUserMap(itemList)
			return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("已存在相同的知名选手！教练[ttid:%s], 技能名称:[%s]", userMap[uid].TTid, gameMap[gameId].GameName))
		}
		return fmt.Errorf("failed to insert into renownedPlayerCollection: %w", err)
	}

	// Step 3: If everything goes well, return nil for the error
	return nil
}

// BatchGetIssuanceRecordByCoachIdsAndLabelIds 批量获取指定教练的指定标签的发放记录
func (s *Store) BatchGetIssuanceRecordByCoachIdsAndLabelIds(ctx context.Context, coachIds []uint32, labelIds []uint32) ([]*IssuanceRecord, error) {
	// Step 1: Create a filter based on the provided coach IDs and label IDs and time
	filter := bson.M{
		"coach_id":                      bson.M{"$in": coachIds},
		"label_id":                      bson.M{"$in": labelIds},
		"effective_duration.start_time": bson.M{"$lte": time.Now()},
		"effective_duration.end_time":   bson.M{"$gte": time.Now()},
	}

	// Step 2: Use the Find method to retrieve the documents that match the filter
	cursor, err := s.issuanceRecordCollection.Collection.Find(ctx, filter)
	if err != nil {
		// Step 4: If an error occurs during any of these operations, wrap it using fmt.Errorf and return it
		return nil, fmt.Errorf("failed to find in issuanceRecordCollection: %w", err)
	}
	defer cursor.Close(ctx)

	// Step 3: Decode the returned documents into a slice of IssuanceRecord
	var records []*IssuanceRecord
	if err := cursor.All(ctx, &records); err != nil {
		// Step 4: If an error occurs during any of these operations, wrap it using fmt.Errorf and return it
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	// Step 5: If everything goes well, return the slice of IssuanceRecord and nil for the error
	return records, nil
}

func (s *Store) getGameAndUserMap(itemList []*RenownedPlayer) (map[uint32]*RenownedPlayer, map[uint32]*RenownedPlayer) {
	gameMap := make(map[uint32]*RenownedPlayer)
	userMap := make(map[uint32]*RenownedPlayer)
	for _, item := range itemList {
		gameMap[item.GameID] = item
		userMap[item.UID] = item
	}
	return gameMap, userMap
}

func extractDuplicateValuesFromErrMsg(str string) (uint32, uint32) {
	// str := `bulk write error: [{[{E11000 duplicate key error collection: esport_skill.renowned_player index: uid_1_game_id_1 dup key: { : 2465920, : 117 }}]}, {<nil>}]`
	// 匹配 uid
	reNumber1 := regexp.MustCompile(`:\s*(\d+)`)
	match1 := reNumber1.FindStringSubmatch(str)
	uidStr := ""
	if len(match1) > 1 {
		uidStr = match1[1]
	}
	fmt.Println(match1)

	// 匹配 gameId
	reNumber2 := regexp.MustCompile(`:\s*(\d+)\s*}`)
	match2 := reNumber2.FindStringSubmatch(str)
	gameIdStr := ""
	if len(match2) > 1 {
		gameIdStr = match2[1]
	}

	uid, _ := strconv.Atoi(uidStr)
	gameId, _ := strconv.Atoi(gameIdStr)
	// 返回结果
	return uint32(uid), uint32(gameId)
}

// IsRenownedPlayerExist 判断知名选手是否存在
func (s *Store) IsRenownedPlayerExist(ctx context.Context, uid, gameId uint32) (bool, error) {
	filter := bson.M{"uid": uid, "game_id": gameId}
	result := s.renownedPlayerCollection.Collection.FindOne(ctx, filter)
	if result.Err() != nil {
		if errors.Is(result.Err(), mongo_driver.ErrNoDocuments) {
			return false, nil
		}
		return false, fmt.Errorf("failed to find in renownedPlayerCollection: %w", result.Err())
	}
	return true, nil
}

// BatchGetBasePriceSetting 批量获取特定教练和游戏的当前基础定价配置。
func (s *Store) BatchGetBasePriceSetting(ctx context.Context, coachIDList []uint32, gameIDList []uint32) ([]*CoachBasePriceSetting, error) {
	// Step 1: Create a filter based on the provided coach IDs and game ID
	filter := bson.M{"coach_id": bson.M{"$in": coachIDList}, "game_id": bson.M{"$in": gameIDList}}

	// Step 2: Use the Find method to retrieve the documents that match the filter
	cursor, err := s.basePriceSettingCollection.Collection.Find(ctx, filter)
	if err != nil {
		if errors.Is(err, mongo_driver.ErrNoDocuments) {
			return []*CoachBasePriceSetting{}, nil
		}
		// Step 4: If an error occurs during any of these operations, wrap it using fmt.Errorf and return it
		return nil, fmt.Errorf("failed to find in basePriceSettingCollection: %w", err)
	}
	defer cursor.Close(ctx)

	// Step 3: Decode the returned documents into a slice of CoachBasePriceSetting
	var settings []*CoachBasePriceSetting
	if err := cursor.All(ctx, &settings); err != nil {
		// Step 4: If an error occurs during any of these operations, wrap it using fmt.Errorf and return it
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	// Step 5: If everything goes well, return the slice of CoachBasePriceSetting and nil for the error
	return settings, nil
}

// BatchSetBasePriceSetting 批量设置特定教练和游戏的基础定价配置。
func (s *Store) BatchSetBasePriceSetting(ctx context.Context, setting []*CoachBasePriceSetting) error {
	if len(setting) == 0 {
		log.WarnWithCtx(ctx, "BatchSetBasePriceSetting failed, setting is empty")
		return nil
	}
	// Step 1: Create a slice of mongo_driver.WriteModel to store the operations
	models := make([]mongo_driver.WriteModel, len(setting))
	for i, item := range setting {
		item.ID = fmt.Sprintf("%d_%d", item.CoachID, item.GameID)
		item.UpdateTime = time.Now()
		// Create an UpdateOneModel for each item in the setting slice
		models[i] = mongo_driver.NewUpdateOneModel().SetFilter(bson.M{"_id": item.ID}).SetUpdate(bson.M{"$set": item}).SetUpsert(true)
	}

	// Step 2: Batch Upsert the documents into the collection
	_, err := s.basePriceSettingCollection.Collection.BulkWrite(ctx, models)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchSetBasePriceSetting failed to BulkWrite into basePriceSettingCollection: %v", err)
		return fmt.Errorf("failed to batch upsert into basePriceSettingCollection: %w", err)
	}
	log.InfoWithCtx(ctx, "BatchSetBasePriceSetting success, setting:%+v", setting)
	// Step 3: If everything goes well, return nil for the error
	return nil
}

func (s *Store) CheckAddLabelOrder(c context0.Context, labelType pb.LabelType, displayOrder uint32) error {
	filter := bson.M{"label_type": labelType, "display_order": displayOrder}
	result := s.labelCollection.Collection.FindOne(c, filter)
	if result.Err() != nil {
		if errors.Is(result.Err(), mongo_driver.ErrNoDocuments) {
			log.DebugWithCtx(c, "CheckAddLabelOrder success, labelType:%d, displayOrder:%d", labelType, displayOrder)
			return nil
		}
		log.ErrorWithCtx(c, "CheckAddLabelOrder failed, labelType:%d, displayOrder:%d, err:%v", labelType, displayOrder, result.Err())
		return result.Err()
	}
	log.ErrorWithCtx(c, "CheckAddLabelOrder failed, labelType:%d, displayOrder:%d, 存在相同排序", labelType, displayOrder)
	return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("已存在相同的排序！"))
}

func (s *Store) CheckUpdateLabelOrder(c context0.Context, labelType pb.LabelType, displayOrder uint32, id uint32) error {
	filter := bson.M{"label_type": labelType, "display_order": displayOrder, "_id": bson.M{"$ne": id}}
	result := s.labelCollection.Collection.FindOne(c, filter)
	if result.Err() != nil {
		if errors.Is(result.Err(), mongo_driver.ErrNoDocuments) {
			log.DebugWithCtx(c, "CheckUpdateLabelOrder success, labelType:%d, displayOrder:%d, id:%d", labelType, displayOrder, id)
			return nil
		}
		log.ErrorWithCtx(c, "CheckUpdateLabelOrder failed, labelType:%d, displayOrder:%d, id:%d, err:%v", labelType, displayOrder, id, result.Err())
		return result.Err()
	}
	log.ErrorWithCtx(c, "CheckUpdateLabelOrder failed, labelType:%d, displayOrder:%d, id:%d, 存在相同排序", labelType, displayOrder, id)
	return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("已存在相同的排序！"))
}

type UserSpecialLabelStruct struct {
	GameId      uint32   `bson:"game_id"`
	LabelIdList []uint32 `bson:"label_id"`
}

type UserSpecialGameStruct struct {
	Uid      uint32                    `bson:"uid"`
	GameList []*UserSpecialLabelStruct `bson:"game_list"`
}

func (s *Store) UpdateUserSpecialLabel(c context0.Context, request *pb.UpdateUserSpecialLabelRequest) error {
	// Step 1: Create a filter based on the provided parameters
	filter := bson.M{"uid": request.Uid}
	// Step 2: Create a slice of UserSpecialGameStruct to store the documents to insert
	insertData := &UserSpecialGameStruct{
		Uid: request.Uid,
	}
	for _, game := range request.GameList {
		gameItem := &UserSpecialLabelStruct{
			GameId: game.GameId,
		}
		labelIdList := make([]uint32, 0, len(game.GetLabelList()))
		for _, label := range game.GetLabelList() {
			labelIdList = append(labelIdList, label.GetLabelId())
		}
		gameItem.LabelIdList = labelIdList
		insertData.GameList = append(insertData.GameList, gameItem)
	}

	// upsert
	_, err := s.userSpecialLabelCollection.Collection.UpdateOne(c, filter, bson.M{"$set": insertData}, options.Update().SetUpsert(true))
	if err != nil {
		log.ErrorWithCtx(c, "UpdateUserSpecialLabel failed, err:%v", err)
		return err
	}
	log.InfoWithCtx(c, "UpdateUserSpecialLabel success, request:%+v", request)
	return nil
}

func (s *Store) BatchGetUserSpecialLabel(c context0.Context, uidList []uint32) (map[uint32]map[uint32][]uint32, error) {
	// Step 1: Create a filter based on the provided parameters
	filter := bson.M{"uid": bson.M{"$in": uidList}}

	// Step 2: Use the Find method to retrieve the documents that match the filter
	cursor, err := s.userSpecialLabelCollection.Collection.Find(c, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find in userSpecialLabelCollection: %w", err)
	}
	defer cursor.Close(c)

	// Step 3: Decode the returned documents into a slice of UserSpecialGameStruct
	var userSpecialGameList []*UserSpecialGameStruct
	if err := cursor.All(c, &userSpecialGameList); err != nil {
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	// Step 4: Create a map to store the result
	result := make(map[uint32]map[uint32][]uint32)
	for _, userSpecialGame := range userSpecialGameList {
		gameMap := make(map[uint32][]uint32)
		for _, game := range userSpecialGame.GameList {
			gameMap[game.GameId] = game.LabelIdList
		}
		result[userSpecialGame.Uid] = gameMap
	}

	// Step 5: If everything goes well, return the map and nil for the error
	return result, nil
}

func (s *Store) BatchGetLabelByIds(ctx context.Context, ids []uint32) (map[uint32]*Label, error) {
	labelList, err := s.BatchGetLabel(ctx, ids)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetLabelByIds failed, err:%v", err)
		return nil, err
	}
	result := make(map[uint32]*Label)
	for _, label := range labelList {
		result[label.ID] = label
	}
	return result, nil
}

func (s *Store) BatchGetUserGameSpecialLabel(c context0.Context, gameId uint32, uidList []uint32) (map[uint32][]uint32, error) {
	// Step 1: Create a filter based on the provided parameters
	filter := bson.M{"uid": bson.M{"$in": uidList}}

	// Step 2: Use the Find method to retrieve the documents that match the filter
	cursor, err := s.userSpecialLabelCollection.Collection.Find(c, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find in userSpecialLabelCollection: %w", err)
	}
	defer cursor.Close(c)

	// Step 3: Decode the returned documents into a slice of UserSpecialGameStruct
	var userSpecialGameList []*UserSpecialGameStruct
	if err := cursor.All(c, &userSpecialGameList); err != nil {
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	// Step 4: Create a map to store the result
	result := make(map[uint32][]uint32)
	for _, userSpecialGame := range userSpecialGameList {
		for _, game := range userSpecialGame.GameList {
			if game.GameId == gameId {
				result[userSpecialGame.Uid] = game.LabelIdList
			}
		}
	}

	// Step 5: If everything goes well, return the map and nil for the error
	return result, nil
}

// BatchGetSpecialLabelIssuanceRecords 批量获取用户特色标签发放记录
func (s *Store) BatchGetSpecialLabelIssuanceRecords(c context.Context, coachIds []uint32, labelIds []uint32) ([]*IssuanceRecord, error) {
	// Step 1: Create a filter based on the provided coach IDs and label IDs
	filter := bson.M{"uid": bson.M{"$in": coachIds}}

	// Step 2: Use the Find method to retrieve the documents that match the filter
	cursor, err := s.userSpecialLabelCollection.Collection.Find(c, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find in issuanceRecordCollection: %w", err)
	}
	defer cursor.Close(c)

	// Step 3: Decode the returned documents into a slice of UserSpecialGameStruct
	var userSpecialGameList []*UserSpecialGameStruct
	if err := cursor.All(c, &userSpecialGameList); err != nil {
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	// 转换数据
	labelIndex := mapz.ToMap(labelIds, func(labelId uint32) uint32 {
		return labelId
	})
	records := transform.FlatMap(userSpecialGameList, func(item *UserSpecialGameStruct) []*IssuanceRecord {
		if len(item.GameList) == 0 {
			return []*IssuanceRecord{}
		}
		return transform.FlatMap(item.GameList, func(game *UserSpecialLabelStruct) []*IssuanceRecord {
			if len(game.LabelIdList) == 0 {
				return []*IssuanceRecord{}
			}
			return transform.FlatMap(game.LabelIdList, func(labelId uint32) []*IssuanceRecord {
				if _, ok := labelIndex[labelId]; !ok {
					return []*IssuanceRecord{}
				}
				return []*IssuanceRecord{
					{
						CoachID: item.Uid,
						LabelID: labelId,
					},
				}
			})
		})
	})

	// Step 4: If everything goes well, return the slice of IssuanceRecord and nil for the error
	return records, nil
}

// GetAllEffectiveIssuanceRecord 获取所有生效的发放记录(大神标识/技能标识)
func (s *Store) GetEffectiveIssuanceRecord(c context.Context, pageNum, pageSize int64) ([]*IssuanceRecord, error) {
	filter := bson.M{"effective_duration.start_time": bson.M{"$lte": time.Now()}, "effective_duration.end_time": bson.M{"$gte": time.Now()}}
	cursor, err := s.issuanceRecordCollection.Collection.Find(c, filter, options.Find().SetSkip(pageNum*pageSize).SetLimit(pageSize))
	if err != nil {
		return nil, fmt.Errorf("failed to find in issuanceRecordCollection: %w", err)
	}
	defer cursor.Close(c)

	var records []*IssuanceRecord
	if err := cursor.All(c, &records); err != nil {
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}
	return records, nil
}

// GetAllSpecialLabelIssuanceRecord 获取所有用户特色标签发放记录
func (s *Store) GetSpecialLabelIssuanceRecord(c context.Context, pageNum, pageSize int64) ([]*IssuanceRecord, error) {
	cursor, err := s.userSpecialLabelCollection.Collection.Find(c, bson.M{}, options.Find().SetSkip(pageNum*pageSize).SetLimit(pageSize))
	if err != nil {
		return nil, fmt.Errorf("failed to find in issuanceRecordCollection: %w", err)
	}
	defer cursor.Close(c)

	var userSpecialGameList []*UserSpecialGameStruct
	if err := cursor.All(c, &userSpecialGameList); err != nil {
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	records := transform.FlatMap(userSpecialGameList, func(item *UserSpecialGameStruct) []*IssuanceRecord {
		if len(item.GameList) == 0 {
			return []*IssuanceRecord{}
		}
		return transform.FlatMap(item.GameList, func(game *UserSpecialLabelStruct) []*IssuanceRecord {
			if len(game.LabelIdList) == 0 {
				return []*IssuanceRecord{}
			}
			return transform.FlatMap(game.LabelIdList, func(labelId uint32) []*IssuanceRecord {
				return []*IssuanceRecord{
					{
						CoachID: item.Uid,
						GameId:  game.GameId,
						LabelID: labelId,
					},
				}
			})
		})
	})

	return records, nil
}
