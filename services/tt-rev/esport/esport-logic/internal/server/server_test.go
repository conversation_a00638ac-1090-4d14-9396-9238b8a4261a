package server

import (
    "context"
    "errors"
    "golang.52tt.com/services/tt-rev/esport/esport-logic/internal/mgr"
    "testing"

    "github.com/golang/mock/gomock"
    mocks_censoring_proxy "golang.52tt.com/clients/mocks/censoring-proxy"
    mocks_guild "golang.52tt.com/clients/mocks/guild"
    mocks_head_dy "golang.52tt.com/clients/mocks/head-dynamic-image-logic"
    mockheadimage "golang.52tt.com/clients/mocks/headimage"
    mocks_parent_guardian "golang.52tt.com/clients/mocks/parent-guardian"
    mock_presence "golang.52tt.com/clients/mocks/presence/v2"
    mocks_risk "golang.52tt.com/clients/mocks/risk-mng-api"
    mocks_ttc_proxy "golang.52tt.com/clients/mocks/ttc-proxy"
    mocks_user_black_list "golang.52tt.com/clients/mocks/user-black-list"
    mocks_user_online "golang.52tt.com/clients/mocks/user-online"
    mocks_user_profile_api "golang.52tt.com/clients/mocks/user-profile-api"
    "golang.52tt.com/protocol/app/esport_logic"
    esport_skill "golang.52tt.com/protocol/services/esport-skill"
    esportStatisMock "golang.52tt.com/protocol/services/esport-statistics"
    esport_trade_appeal "golang.52tt.com/protocol/services/esport-trade-appeal"
    mocksClients "golang.52tt.com/protocol/services/mocks"
    esportcommonmocks "golang.52tt.com/services/tt-rev/esport/common/mocks"
    "golang.52tt.com/services/tt-rev/esport/esport-logic/internal/mocks"
)

// go generate ./...
//go:generate mockgen -destination=../mocks/business_conf.go -mock_names IClient=MockIBusinessConfManager -package=mocks golang.52tt.com/services/tt-rev/esport/esport-logic/internal/conf IBusinessConfManager

type serverHelperForTest struct {
    *Server
}

func newServerHelperForTest(t *testing.T) *serverHelperForTest {
    ctrl := gomock.NewController(t)

    return &serverHelperForTest{
        Server: &Server{
            bc:                  mocks.NewMockIBusinessConfManager(ctrl),
            refundService:       mocksClients.NewMockRefundServiceClient(ctrl),
            orderService:        mocksClients.NewMockEsportTradeClient(ctrl),
            eSportRoleCli:       mocksClients.NewMockESportRoleClient(ctrl),
            TTCProxyClient:      mocks_ttc_proxy.NewMockIClient(ctrl),
            parentGuardianCli:   mocks_parent_guardian.NewMockIClient(ctrl),
            eSportRoleService:   mocksClients.NewMockESportRoleClient(ctrl),
            eSportSkillService:  esport_skill.NewMockEsportSkillClient(ctrl),
            eSportHallService:   mocksClients.NewMockEsportHallClient(ctrl),
            userProfileCli:      mocks_user_profile_api.NewMockIClient(ctrl),
            userBlacklistCli:    mocks_user_black_list.NewMockIClient(ctrl),
            guildCli:            mocks_guild.NewMockIClient(ctrl),
            riskCli:             mocks_risk.NewMockIClient(ctrl),
            headDynamicImageCli: mocks_head_dy.NewMockIClient(ctrl),
            userOnlineCli:       mocks_user_online.NewMockIClient(ctrl),
            censoringProxyCli:   mocks_censoring_proxy.NewMockIClient(ctrl),
            eSportStatCli:       esportStatisMock.NewMockEsportStatisticsClient(ctrl),
            presenceV2Cli:       mock_presence.NewMockIClient(ctrl),
            headImageCli:        mockheadimage.NewMockIClient(ctrl),
            extPushCLi:          esportcommonmocks.NewMockIPushCli(ctrl),
            mgr:                 mgr.NewMockMgr(ctrl),
            esportCustomerCli:   mocksClients.NewMockCustomerServiceClient(ctrl),
            esportInternalCli:   mocksClients.NewMockEsportInternalClient(ctrl),
            esportRcmdCli:       mocksClients.NewMockEsportRcmdServiceClient(ctrl),
        },
    }
}

func (s *serverHelperForTest) getEsportCustomerCli() *mocksClients.MockCustomerServiceClient {
    return s.esportCustomerCli.(*mocksClients.MockCustomerServiceClient)
}

func (s *serverHelperForTest) getEsportRcmdCli() *mocksClients.MockEsportRcmdServiceClient {
    return s.esportRcmdCli.(*mocksClients.MockEsportRcmdServiceClient)
}
func (s *serverHelperForTest) getMgr() *mgr.MockMgr {
    return s.mgr.(*mgr.MockMgr)
}

func (s *serverHelperForTest) getBusinessConfManager() *mocks.MockIBusinessConfManager {
    return s.bc.(*mocks.MockIBusinessConfManager)
}

func (s *serverHelperForTest) getRefundService() *mocksClients.MockRefundServiceClient {
    return s.refundService.(*mocksClients.MockRefundServiceClient)
}

func (s *serverHelperForTest) getOrderService() *mocksClients.MockEsportTradeClient {
    return s.orderService.(*mocksClients.MockEsportTradeClient)
}

func (s *serverHelperForTest) getESportSkillService() *esport_skill.MockEsportSkillClient {
    return s.eSportSkillService.(*esport_skill.MockEsportSkillClient)
}

func (s *serverHelperForTest) getESportRoleService() *mocksClients.MockESportRoleClient {
    return s.eSportRoleService.(*mocksClients.MockESportRoleClient)
}

func (s *serverHelperForTest) getESportHallService() *mocksClients.MockEsportHallClient {
    return s.eSportHallService.(*mocksClients.MockEsportHallClient)
}

func (s *serverHelperForTest) getUserProfileCli() *mocks_user_profile_api.MockIClient {
    return s.userProfileCli.(*mocks_user_profile_api.MockIClient)
}

func (s *serverHelperForTest) getExtPushCli() *esportcommonmocks.MockIPushCli {
    return s.extPushCLi.(*esportcommonmocks.MockIPushCli)
}

func (s *serverHelperForTest) getUserBlackListCli() *mocks_user_black_list.MockIClient {
    return s.userBlacklistCli.(*mocks_user_black_list.MockIClient)
}

func (s *serverHelperForTest) getGuildCli() *mocks_guild.MockIClient {
    return s.guildCli.(*mocks_guild.MockIClient)
}

func (s *serverHelperForTest) getRiskCli() *mocks_risk.MockIClient {
    return s.riskCli.(*mocks_risk.MockIClient)
}

func (s *serverHelperForTest) getHeadDynamicImageCli() *mocks_head_dy.MockIClient {
    return s.headDynamicImageCli.(*mocks_head_dy.MockIClient)
}

func (s *serverHelperForTest) getUserOnlineCli() *mocks_user_online.MockIClient {
    return s.userOnlineCli.(*mocks_user_online.MockIClient)
}

func (s *serverHelperForTest) getPresenceV2Cli() *mock_presence.MockIClient {
    return s.presenceV2Cli.(*mock_presence.MockIClient)
}

func (s *serverHelperForTest) getCensoringProxyCli() *mocks_censoring_proxy.MockIClient {
    return s.censoringProxyCli.(*mocks_censoring_proxy.MockIClient)
}

func (s *serverHelperForTest) getESportStatCli() *esportStatisMock.MockEsportStatisticsClient {
    return s.eSportStatCli.(*esportStatisMock.MockEsportStatisticsClient)
}

func (s *serverHelperForTest) getESportRoleCli() *mocksClients.MockESportRoleClient {
    return s.eSportRoleCli.(*mocksClients.MockESportRoleClient)
}

func (s *serverHelperForTest) getESportInternalCli() *mocksClients.MockEsportInternalClient {
    return s.esportInternalCli.(*mocksClients.MockEsportInternalClient)
}

func (s *serverHelperForTest) getTTCProxyCli() *mocks_ttc_proxy.MockIClient {
    return s.TTCProxyClient.(*mocks_ttc_proxy.MockIClient)
}

func (s *serverHelperForTest) getParentGuardianCli() *mocks_parent_guardian.MockIClient {
    return s.parentGuardianCli.(*mocks_parent_guardian.MockIClient)
}

func (s *serverHelperForTest) getHeadImageCli() *mockheadimage.MockIClient {
    return s.headImageCli.(*mockheadimage.MockIClient)
}

func TestServer_AcceptRefund(t *testing.T) {
    type args struct {
        ctx context.Context
        req *esport_logic.AcceptRefundRequest
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        initFunc func(s *serverHelperForTest)
    }{
        {
            name: "Test with valid request",
            args: args{
                ctx: context.Background(),
                req: &esport_logic.AcceptRefundRequest{
                    RefundId: "valid_refund_id",
                },
            },
            wantErr: false,
            initFunc: func(s *serverHelperForTest) {
                s.getRefundService().EXPECT().AcceptRefund(gomock.Any(), gomock.Any()).Return(&esport_trade_appeal.AcceptRefundResponse{}, nil)
            },
        },
        {
            name: "Test with invalid request",
            args: args{
                ctx: context.Background(),
                req: nil,
            },
            wantErr: true,
        },
        {
            name: "Test with refund service error",
            args: args{
                ctx: context.Background(),
                req: &esport_logic.AcceptRefundRequest{
                    RefundId: "valid_refund_id",
                },
            },
            wantErr: true,
            initFunc: func(s *serverHelperForTest) {
                s.getRefundService().EXPECT().AcceptRefund(gomock.Any(), gomock.Any()).Return(nil, errors.New("refund service error"))
            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s := newServerHelperForTest(t)
            if tt.initFunc != nil {
                tt.initFunc(s)
            }
            _, err := s.AcceptRefund(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("Server.AcceptRefund() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestServer_GetGameList(t *testing.T) {
    type args struct {
        ctx context.Context
        req *esport_logic.GetGameListRequest
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        initFunc func(s *serverHelperForTest)
    }{
        {
            name: "Test with valid request",
            args: args{
                ctx: context.Background(),
                req: &esport_logic.GetGameListRequest{
                    GameType:  1,
                    PageToken: "token",
                },
            },
            wantErr: false,
            initFunc: func(s *serverHelperForTest) {
                mockGameList := []*esport_skill.GameItem{
                    {
                        GameId:   1,
                        GameName: "game1",
                        GameIcon: "icon1",
                    },
                    {
                        GameId:   2,
                        GameName: "game2",
                        GameIcon: "icon2",
                    },
                }
                s.getESportSkillService().EXPECT().GetGameList(gomock.Any(), gomock.Any()).Return(&esport_skill.GetGameListResponse{
                    ItemList:      mockGameList,
                    NextPageToken: "next_token",
                }, nil)
            },
        },
        {
            name: "Test with esport skill service error",
            args: args{
                ctx: context.Background(),
                req: &esport_logic.GetGameListRequest{
                    GameType:  1,
                    PageToken: "token",
                },
            },
            wantErr: true,
            initFunc: func(s *serverHelperForTest) {
                s.getESportSkillService().EXPECT().GetGameList(gomock.Any(), gomock.Any()).Return(nil, errors.New("esport skill service error"))
            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s := newServerHelperForTest(t)
            if tt.initFunc != nil {
                tt.initFunc(s)
            }
            _, err := s.GetGameList(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("Server.GetGameList() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

//func TestServer_GetSwitch(t *testing.T) {
//	type args struct {
//		ctx context.Context
//		req *esport_logic.GetSwitchRequest
//	}
//	tests := []struct {
//		name     string
//		args     args
//		wantErr  bool
//		initFunc func(s *serverHelperForTest)
//	}{
//		{
//			name: "Test with valid request",
//			args: args{
//				ctx: context.Background(),
//				req: &esport_logic.GetSwitchRequest{},
//			},
//			wantErr: false,
//			initFunc: func(s *serverHelperForTest) {
//				s.getESportSkillService().EXPECT().GetSwitch(gomock.Any(), gomock.Any()).Return(&esport_skill.GetSwitchResponse{
//					SwitchStatus: &esport_skill.SwitchStatus{
//						MainSwitchStatus:     esport_skill.EsportSwitchStatus_SWITCH_STATUS_ON,
//						HomepageSwitchStatus: esport_skill.EsportSwitchStatus_SWITCH_STATUS_ON,
//					},
//				}, nil)
//			},
//		},
//		{
//			name: "Test with esport skill service error",
//			args: args{
//				ctx: context.Background(),
//				req: &esport_logic.GetSwitchRequest{},
//			},
//			wantErr: true,
//			initFunc: func(s *serverHelperForTest) {
//				s.getESportSkillService().EXPECT().GetSwitch(gomock.Any(), gomock.Any()).Return(nil, errors.New("esport skill service error"))
//			},
//		},
//	}
//
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			s := newServerHelperForTest(t)
//			if tt.initFunc != nil {
//				tt.initFunc(s)
//			}
//			_, err := s.GetSwitch(tt.args.ctx, tt.args.req)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Server.GetSwitch() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//		})
//	}
//}

//func TestServer_EsportGetTopGameList(t *testing.T) {
//	type args struct {
//		ctx context.Contextcd
//		req *esport_logic.GetTopGameListRequest
//	}
//	tests := []struct {
//		name     string
//		args     args
//		wantErr  bool
//		initFunc func(s *serverHelperForTest)
//	}{
//		{
//			name: "Test with valid request",
//			args: args{
//				ctx: context.Background(),
//				req: &esport_logic.GetTopGameListRequest{
//					GameType: 1,
//				},
//			},
//			wantErr: false,
//			initFunc: func(s *serverHelperForTest) {
//				mockGameList := []*esport_skill.GameItem{
//					{
//						GameId:   1,
//						GameName: "Game1",
//						GameIcon: "Icon1",
//					},
//					{
//						GameId:   2,
//						GameName: "Game2",
//						GameIcon: "Icon2",
//					},
//				}
//				s.getESportSkillService().EXPECT().GetTopGameList(gomock.Any(), gomock.Any()).Return(&esport_skill.GetTopGameListResponse{
//					ItemList: mockGameList,
//				}, nil)
//			},
//		},
//		{
//			name: "Test with esport skill service error",
//			args: args{
//				ctx: context.Background(),
//				req: &esport_logic.GetTopGameListRequest{
//					GameType: 1,
//				},
//			},
//			wantErr: true,
//			initFunc: func(s *serverHelperForTest) {
//				s.getESportSkillService().EXPECT().GetTopGameList(gomock.Any(), gomock.Any()).Return(nil, errors.New("esport skill service error"))
//			},
//		},
//	}
//
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			s := newServerHelperForTest(t)
//			if tt.initFunc != nil {
//				tt.initFunc(s)
//			}
//			_, err := s.EsportGetTopGameList(tt.args.ctx, tt.args.req)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Server.EsportGetTopGameList() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//		})
//	}
//}
