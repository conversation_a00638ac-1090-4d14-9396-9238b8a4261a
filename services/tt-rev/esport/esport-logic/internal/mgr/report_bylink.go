package mgr

import (
    "context"
    "encoding/json"
    "gitlab.ttyuyin.com/bizFund/bizFund/pkg/device_id"
    "golang.52tt.com/pkg/bylink"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    esport_trade "golang.52tt.com/protocol/services/esport-trade"
    guild_cooperation "golang.52tt.com/protocol/services/guild-cooperation"
    user_online "golang.52tt.com/protocol/services/user-online"
    "strings"
    "time"
)

// AsyncReportByLinkOrderFinish 上报订单完成给百灵
func (m *MgrImpl) AsyncReportByLinkOrderFinish(oldCtx context.Context, orderId string, source int32) {
    go func() {
        ctx, cancelFunc := protogrpc.NewContextWithInfoTimeout(oldCtx, 2*time.Second)
        defer cancelFunc()
        log.DebugWithCtx(ctx, "AsyncReportByLinkOrderFinish start, orderId: %s, source: %d", orderId, source)

        svrInfo, _ := protogrpc.ServiceInfoFromContext(oldCtx)

        // 查询订单信息
        orderDetail, err := m.orderService.GetOrderDetail(ctx, &esport_trade.GetOrderDetailRequest{
            Uid:               svrInfo.UserID,
            OrderId:           orderId,
            NeedAllStatusTime: false,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "AsyncReportByLinkOrderFinish GetOrderDetail, uid:%d, orderId:%s, err:%v", svrInfo.UserID, orderId, err)
            return
        }

        // 查询公会信息
        var guildId uint32
        guildName := ""
        guildType := make([]string, 0, 3)
        userResp, err := m.accountCli.GetUserByUid(ctx, svrInfo.UserID)
        if err != nil {
            log.ErrorWithCtx(ctx, "AsyncReportByLinkOrderFinish GetUserByUid, uid:%d, orderId:%s, err:%v", svrInfo.UserID, orderId, err)
            return
        } else {
            guildId = userResp.GetCurrentGuildId()
            guild, serverError := m.guildCli.GetGuild(ctx, guildId)
            if serverError != nil {
                log.ErrorWithCtx(ctx, "AsyncReportByLinkOrderFinish GetGuild, uid:%d, orderId:%s, err:%v", svrInfo.UserID, orderId, serverError)
            } else {
                guildName = guild.GetName()
                // 查询公会类型
                guildCooperationInfos, err := m.guildCopeCli.GetGuildCooperationInfos(ctx, &guild_cooperation.GetGuildCooperationInfosReq{
                    GuildId: guildId,
                })
                if err != nil {
                    log.ErrorWithCtx(ctx, "AsyncReportByLinkOrderFinish GetGuildCooperationInfos, uid:%d, orderId:%s, err:%v", svrInfo.UserID, orderId, err)
                    return
                }
                if guildCooperationInfos.GetIsAmuseCoopGuild() {
                    guildType = append(guildType, "1")
                }
                if guildCooperationInfos.GetIsYuyinCoopGuild() {
                    guildType = append(guildType, "2")
                }
                if guildCooperationInfos.GetIsEsportCoopGuild() {
                    guildType = append(guildType, "3")
                }
            }
        }

        // 组装数据
        data := map[string]interface{}{
            "$ip":         svrInfo.ClientIPAddr().String(),
            "uid":         svrInfo.UserID,
            "device_id":   device_id.ToDeviceHexId(svrInfo.DeviceID, false),
            "app_ver":     protocol.ClientVersion(svrInfo.ClientVersion).String(),
            "source":      source,
            "market_id":   svrInfo.MarketID,
            "activity_id": "完成订单",
            "amount":      orderDetail.GetOrder().GetProductOrder().GetTotalPrice(),
            "order_id":    orderId,
            "guild_id":    guildId,
            "guild_name":  guildName,
            "guild_type":  strings.Join(guildType, ","),
        }

        if err := bylink.Track(ctx, uint64(svrInfo.UserID), "risk_esport_complete_order_log", data, true); err != nil {
            log.ErrorWithCtx(ctx, "AsyncReportByLinkOrderFinish track, uid: %d, data: %+v, err: %v", svrInfo.UserID, data, err)
            return
        }
    }()
}

// AsyncReportByLinkIssueCoupon 上报发放优惠券给百灵
func (m *MgrImpl) AsyncReportByLinkIssueCoupon(oldCtx context.Context, uid uint32, couponIds []string, orderId string) {
    go func() {
        ctx, cancelFunc := protogrpc.NewContextWithInfoTimeout(oldCtx, 2*time.Second)
        defer cancelFunc()

        log.DebugWithCtx(ctx, "AsyncReportByLinkIssueCoupon start, uid: %d, couponIds: %+v, orderId: %s", uid, couponIds, orderId)
        if len(couponIds) == 0 {
            return
        }

        svrInfo, _ := protogrpc.ServiceInfoFromContext(oldCtx)

        //  查询优惠券的信息
        coupons, err := m.orderService.GetCouponByIds(ctx, &esport_trade.GetCouponByIdsRequest{Uid: uid, Id: couponIds})
        if err != nil {
            log.ErrorWithCtx(ctx, "AsyncReportByLinkIssueCoupon GetCouponByIds, uid:%d, orderId:%s, err:%v", svrInfo.UserID, orderId, err)
            return
        }

        // 组装数据
        data := map[string]interface{}{
            "$ip":         svrInfo.ClientIPAddr().String(),
            "uid":         svrInfo.UserID,
            "device_id":   device_id.ToDeviceHexId(svrInfo.DeviceID, false),
            "app_ver":     protocol.ClientVersion(svrInfo.ClientVersion).String(),
            "market_id":   svrInfo.MarketID,
            "activity_id": "发放优惠券",
            "ext":         "",
        }
        for _, coupon := range coupons.GetCoupon() {
            ext := map[string]interface{}{
                "coupon_id":              coupon.GetCouponId(),
                "coupon_price":           coupon.GetReducePrice(),
                "coupon_source":          "",
                "coupon_order_id":        orderId,
                "coupon_source_uid":      coupon.GetGainSourceInfo().GetCoachUid(),
                "coupon_source_guild_id": coupon.GetGainSourceInfo().GetCoachGuildId(),
                "coupon_rcv_time":        time.Unix(int64(coupon.GetCreateTime()), 0).Format("2006-01-02 15:04:05"),
                "coupon_exp_time":        time.Unix(int64(coupon.GetExpireTime()), 0).Format("2006-01-02 15:04:05"),
            }
            if coupon.GetGainSource() == uint32(esport_trade.CouponGainSource_GAIN_SOURCE_COMMIT_ORDER) {
                ext["coupon_source"] = 1
            }

            marshal, err := json.Marshal(ext)
            if err != nil {
                log.ErrorWithCtx(ctx, "AsyncReportByLinkIssueCoupon json.Marshal, uid: %d,ext: %+v, err: %v", svrInfo.UserID, ext, err)
                continue
            }
            data["ext"] = string(marshal)
            if err := bylink.Track(ctx, uint64(svrInfo.UserID), "risk_esport_coupon_issue_log", data, true); err != nil {
                log.ErrorWithCtx(ctx, "AsyncReportByLinkIssueCoupon track, uid: %d, data: %+v, err: %v", svrInfo.UserID, data, err)
                return
            }
        }
    }()
}

// AsyncReportByLinkUseCoupon 上报使用优惠券给百灵
func (m *MgrImpl) AsyncReportByLinkUseCoupon(oldCtx context.Context, uid uint32, coachId uint32, orderId string, couponId string) {
    go func() {
        ctx, cancelFunc := protogrpc.NewContextWithInfoTimeout(oldCtx, 2*time.Second)
        defer cancelFunc()
        log.DebugWithCtx(ctx, "AsyncReportByLinkUseCoupon start, uid: %d, coachId: %d, orderId: %s, couponId: %s", uid, coachId, orderId, couponId)
        svrInfo, _ := protogrpc.ServiceInfoFromContext(oldCtx)

        // 查询订单信息
        orderDetail, err := m.orderService.GetOrderDetail(ctx, &esport_trade.GetOrderDetailRequest{
            Uid:               svrInfo.UserID,
            OrderId:           orderId,
            NeedAllStatusTime: false,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "AsyncReportByLinkUseCoupon GetOrderDetail, uid:%d, orderId:%s, err:%v", svrInfo.UserID, orderId, err)
            return
        }

        // 查询优惠券信息
        coupon, err := m.orderService.GetCouponById(ctx, &esport_trade.GetCouponByIdRequest{Id: couponId, Uid: uid})
        if err != nil {
            log.ErrorWithCtx(ctx, "AsyncReportByLinkUseCoupon GetCouponById, uid:%d, orderId:%s, err:%v", svrInfo.UserID, orderId, err)
            return
        }

        // 查询大神coachUid的登陆信息
        info, serverError := m.userOnlineCli.BatchGetLastMultiOnlineInfo(ctx, []uint32{coachId})
        if serverError != nil {
            log.ErrorWithCtx(ctx, "AsyncReportByLinkUseCoupon BatchGetLastMultiOnlineInfo, uid:%d, orderId:%s, err:%v", svrInfo.UserID, orderId, serverError)
            return
        }
        // 挑选最近一次的登陆信息
        getLatestOnlineInfo := func(list []*user_online.OnlineInfo) *user_online.OnlineInfo {
            var latest *user_online.OnlineInfo
            for _, item := range list {
                if latest == nil || latest.GetOnlineAt() < item.GetOnlineAt() {
                    latest = item
                }
            }
            return latest
        }
        var onlineInfo *user_online.OnlineInfo
        if len(info) == 0 {
            log.ErrorWithCtx(ctx, "AsyncReportByLinkUseCoupon BatchGetLastMultiOnlineInfo, uid:%d, orderId:%s, info is empty", svrInfo.UserID, orderId)
        } else {
            onlineInfo = getLatestOnlineInfo(info[0].GetOnlineInfoList())
        }

        // 组装数据
        data := map[string]interface{}{
            "$ip":         svrInfo.ClientIPAddr().String(),
            "uid":         svrInfo.UserID,
            "device_id":   device_id.ToDeviceHexId(svrInfo.DeviceID, false),
            "app_ver":     protocol.ClientVersion(svrInfo.ClientVersion).String(),
            "market_id":   svrInfo.MarketID,
            "activity_id": "使用优惠券",
            "amount":      orderDetail.GetOrder().GetProductOrder().GetTotalPrice(),
            "order_id":    orderId,
            "ext":         "",
        }

        ext := map[string]interface{}{
            "rcv_uid":                onlineInfo.GetUid(),
            "rcv_device_id":          strings.ToLower(onlineInfo.GetDeviceIdHex()),
            "rcv_ip":                 onlineInfo.GetClientIp(),
            "coupon_id":              coupon.GetCoupon().GetCouponId(),
            "coupon_price":           coupon.GetCoupon().GetReducePrice(),
            "coupon_order_id":        coupon.GetCoupon().GetGainSourceInfo().GetOrderId(),
            "coupon_source":          "",
            "coupon_source_guild_id": coupon.GetCoupon().GetGainSourceInfo().GetCoachGuildId(),
            "coupon_source_uid":      coupon.GetCoupon().GetGainSourceInfo().GetCoachUid(),
            "coupon_rcv_time":        time.Unix(int64(coupon.GetCoupon().GetCreateTime()), 0).Format("2006-01-02 15:04:05"),
            "coupon_exp_time":        time.Unix(int64(coupon.GetCoupon().GetExpireTime()), 0).Format("2006-01-02 15:04:05"),
        }
        if coupon.GetCoupon().GetGainSource() == uint32(esport_trade.CouponGainSource_GAIN_SOURCE_COMMIT_ORDER) {
            ext["coupon_source"] = 1
        }

        bytes, err := json.Marshal(ext)
        if err != nil {
            log.ErrorWithCtx(ctx, "AsyncReportByLinkUseCoupon json.Marshal, uid: %d,ext: %+v, err: %v", svrInfo.UserID, ext, err)
            return
        }
        data["ext"] = string(bytes)
        if err := bylink.Track(ctx, uint64(svrInfo.UserID), "risk_esport_coupon_use_log", data, true); err != nil {
            log.ErrorWithCtx(ctx, "AsyncReportByLinkUseCoupon track, uid: %d, data: %+v, err: %v", svrInfo.UserID, data, err)
            return
        }
    }()

}
