package mgr

import (
    "context"
    "fmt"
    "github.com/golang/mock/gomock"
    "golang.52tt.com/clients/account"
    "golang.52tt.com/clients/channel"
    channel_open_game "golang.52tt.com/clients/channel-open-game"
    channel_open_game_auth "golang.52tt.com/clients/channel-open-game-auth"
    channelOpenGameController "golang.52tt.com/clients/channel-open-game-controller"
    channel_scheme "golang.52tt.com/clients/channel-scheme"
    channel_scheme_conf_mgr "golang.52tt.com/clients/channel-scheme-conf-mgr"
    mockAccount "golang.52tt.com/clients/mocks/account"
    mockChannel "golang.52tt.com/clients/mocks/channel"
    mockChannelOpenGame "golang.52tt.com/clients/mocks/channel-open-game"
    mockOpenGameAuth "golang.52tt.com/clients/mocks/channel-open-game-auth"
    mockChannelOpenGameCtl "golang.52tt.com/clients/mocks/channel-open-game-controller"
    mockChannelScheme "golang.52tt.com/clients/mocks/channel-scheme"
    mockChannelSchemeConf "golang.52tt.com/clients/mocks/channel-scheme-conf-mgr"
    mockChannelApi "golang.52tt.com/clients/mocks/channelapi"
    mockChannelMic "golang.52tt.com/clients/mocks/channelmic"
    kafka_produce2 "golang.52tt.com/clients/mocks/kafka_produce"
    mockPush "golang.52tt.com/clients/mocks/push-notification/v2"
    mockUnifiedPay "golang.52tt.com/clients/mocks/unified_pay"
    "golang.52tt.com/pkg/config"
    UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
    "golang.52tt.com/services/ugc/common/kafka_produce"
    "time"

    "golang.52tt.com/clients/channelapi"
    "golang.52tt.com/clients/channelmic"
    PushNotification "golang.52tt.com/clients/push-notification/v2"
    unifiedPay "golang.52tt.com/clients/unified_pay"
    pbGA "golang.52tt.com/protocol/services/channel-open-game-auth"
    "golang.52tt.com/protocol/services/pia"
    pb "golang.52tt.com/protocol/services/tt-rev-channel-mode-mgr"
    "golang.52tt.com/services/tt-rev/tt-rev-channel-mode-mgr/internal/conf"
    "golang.52tt.com/services/tt-rev/tt-rev-channel-mode-mgr/internal/store"
    "reflect"
    "sync"
    "testing"

    "golang.52tt.com/services/tt-rev/tt-rev-channel-mode-mgr/internal/mocks"
)

var (
    sc                     *conf.ServiceConfigT
    channelModeStore       *mocks.MockIStore
    businessCfg            *mocks.MockIBusinessConfManager
    channelCli             *mockChannel.MockIClient
    channelSchemeCli       *mockChannelScheme.MockIClient
    channelSchemeConfCli   *mockChannelSchemeConf.MockIClient
    channelMicCli          *mockChannelMic.MockIClient
    channelApiCli          *mockChannelApi.MockIClient
    channelOpenGameCli     *mockChannelOpenGame.MockIClient
    pushCli                *mockPush.MockIClient
    channelOpenGameAuthCli *mockOpenGameAuth.MockIClient
    unifiedPayCli          *mockUnifiedPay.MockIClient
    accountCli             *mockAccount.MockIClient
    kfkProduce             *kafka_produce2.MockIKafkaProduceClient
    channelOpenGameCtlCli  *mockChannelOpenGameCtl.MockIClient

    serviceConfig = &conf.ServiceConfigT{
        MysqlConfig:          nil,
        GameEventKafkaConfig: nil,
        TbeanConsumeKafkaConfig: &config.KafkaConfig{
            Brokers:      "test",
            GroupID:      "test",
            Topics:       "test",
            ClientID:     "",
            SASLEnable:   false,
            SASLUser:     "",
            SASLPassword: "",
        },
    }
)

func genMockMgr(ctl *gomock.Controller) {
    channelModeStore = mocks.NewMockIStore(ctl)
    businessCfg = mocks.NewMockIBusinessConfManager(ctl)
    channelCli = mockChannel.NewMockIClient(ctl)
    channelSchemeCli = mockChannelScheme.NewMockIClient(ctl)
    channelSchemeConfCli = mockChannelSchemeConf.NewMockIClient(ctl)
    channelMicCli = mockChannelMic.NewMockIClient(ctl)
    channelApiCli = mockChannelApi.NewMockIClient(ctl)
    channelOpenGameCli = mockChannelOpenGame.NewMockIClient(ctl)
    pushCli = mockPush.NewMockIClient(ctl)
    channelOpenGameAuthCli = mockOpenGameAuth.NewMockIClient(ctl)
    unifiedPayCli = mockUnifiedPay.NewMockIClient(ctl)
    accountCli = mockAccount.NewMockIClient(ctl)
    kfkProduce = kafka_produce2.NewMockIKafkaProduceClient(ctl)
    channelOpenGameCtlCli = mockChannelOpenGameCtl.NewMockIClient(ctl)
}

func TestManager_BuyWerwolfItem(t *testing.T) {
    ctl := gomock.NewController(t)
    genMockMgr(ctl)
    defer ctl.Finish()
    uid := 2212647
    cid := 1000
    ctx := context.Background()
    gomock.InOrder(
        channelOpenGameAuthCli.EXPECT().GetUid(ctx, &pbGA.GetUidReq{
            Uid:    0,
            GameId: 3,
            Openid: "********-4da5-40e8-4840-883fe66da4e8",
        }).Return(&pbGA.GetUidResp{Uid: uint32(uid)}, nil),
        businessCfg.EXPECT().GetMaxPrice().Return(uint32(1000)),
        channelOpenGameCtlCli.EXPECT().GetChannelGameHost(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
        channelModeStore.EXPECT().AddWerwolfOrder(gomock.Any()).Return(nil).AnyTimes(),
        businessCfg.EXPECT().GetPayAppId().Return("TT_WERWOLF").AnyTimes(),
        unifiedPayCli.EXPECT().PresetFreeze(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1000), nil).AnyTimes(),
        channelModeStore.EXPECT().UpdateWerwolfOrderStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
    )

    req := &pb.BuyWerwolfItemReq{
        ChannelId: uint32(cid),
        OpenId:    "********-4da5-40e8-4840-883fe66da4e8",
        SetId:     "2255775_1661850770",
        Price:     100,
        ItemId:    1,
        ItemName:  "平民",
        GameId:    3,
    }

    orderId := fmt.Sprintf("werwolf_%d_%d_%d", req.GetChannelId(), uid, time.Now().Unix())

    resp := &pb.BuyWerwolfItemResp{
        OrderId:   orderId,
        ErrorInfo: &pb.ErrorInfo{},
    }

    type fields struct {
        sc                     *conf.ServiceConfigT
        channelModeStore       store.IStore
        BusinessCfg            conf.IBusinessConfManager
        mapScheme              sync.Map
        ChannelCli             channel.IClient
        ChannelSchemeCli       channel_scheme.IClient
        ChannelSchemeConfCli   channel_scheme_conf_mgr.IClient
        ChannelMicCli          channelmic.IClient
        ChannelApiCli          channelapi.IClient
        ChannelOpenGameCli     channel_open_game.IClient
        PushCli                PushNotification.IClient
        ChannelOpenGameAuthCli channel_open_game_auth.IClient
        UnifiedPayCli          unifiedPay.IClient
        AccountCli             account.IClient
        PiaCli                 pia.PiaClient
        TbeanConsumeKFKPub     *kafka_produce.KafkaProduce
        ChannelOpenGameCtlCli  channelOpenGameController.IClient
    }
    type args struct {
        ctx context.Context
        req *pb.BuyWerwolfItemReq
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    *pb.BuyWerwolfItemResp
        wantErr bool
    }{
        // TODO: Add test cases.
        {
            name: "正常购买",
            fields: fields{
                channelModeStore:       channelModeStore,
                BusinessCfg:            businessCfg,
                mapScheme:              sync.Map{},
                ChannelCli:             channelCli,
                ChannelSchemeCli:       channelSchemeCli,
                ChannelSchemeConfCli:   channelSchemeConfCli,
                ChannelMicCli:          channelMicCli,
                ChannelApiCli:          channelApiCli,
                ChannelOpenGameCli:     channelOpenGameCli,
                PushCli:                pushCli,
                ChannelOpenGameAuthCli: channelOpenGameAuthCli,
                UnifiedPayCli:          unifiedPayCli,
                AccountCli:             accountCli,
                ChannelOpenGameCtlCli:  channelOpenGameCtlCli,
            },
            args: args{
                ctx: ctx,
                req: req,
            },
            want:    resp,
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Manager{
                sc:                     tt.fields.sc,
                channelModeStore:       tt.fields.channelModeStore,
                BusinessCfg:            tt.fields.BusinessCfg,
                ChannelCli:             tt.fields.ChannelCli,
                ChannelSchemeCli:       tt.fields.ChannelSchemeCli,
                ChannelSchemeConfCli:   tt.fields.ChannelSchemeConfCli,
                ChannelMicCli:          tt.fields.ChannelMicCli,
                ChannelApiCli:          tt.fields.ChannelApiCli,
                channelOpenGameCli:     tt.fields.ChannelOpenGameCli,
                PushCli:                tt.fields.PushCli,
                ChannelOpenGameAuthCli: tt.fields.ChannelOpenGameAuthCli,
                UnifiedPayCli:          tt.fields.UnifiedPayCli,
                AccountCli:             tt.fields.AccountCli,
                PiaCli:                 tt.fields.PiaCli,
                TbeanConsumeKFKPub:     tt.fields.TbeanConsumeKFKPub,
                ChannelOpenGameCtlCli:  tt.fields.ChannelOpenGameCtlCli,
            }
            got, err := m.BuyWerwolfItem(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("BuyWerwolfItem() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("BuyWerwolfItem() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestManager_HandleGameOverEvent(t *testing.T) {
    ctl := gomock.NewController(t)
    genMockMgr(ctl)
    defer ctl.Finish()

    uid := uint32(2212647)
    channelId := uint32(2255775)
    price := uint32(10)
    now := time.Now()
    orders := []*store.WerwolfOrder{
        {
            Id:          0,
            OrderId:     "tt_01",
            Status:      store.OrderStatusHandling,
            ChannelId:   channelId,
            Uid:         uid,
            ItemId:      1,
            ItemName:    "预言家",
            SetId:       "set_id_1001",
            GameId:      3,
            Num:         1,
            Price:       price,
            OutsideTime: now,
            CreateTime:  now,
            UpdateTime:  now,
            TBeanTime:   now,
        },
    }
    gomock.InOrder(
        businessCfg.EXPECT().GetWerwolfGameId().Return(uint32(3)).AnyTimes(),
        channelModeStore.EXPECT().GetWerwolfOrderByChannel(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(orders, true, nil).AnyTimes(),
        accountCli.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
        businessCfg.EXPECT().GetPayAppId().Return("TT_WERWOLF").AnyTimes(),
        unifiedPayCli.EXPECT().UnfreezeAndConsume(gomock.Any(), gomock.Any()).Return("2006-01-02 15:04:05", "", nil).AnyTimes(),
        channelModeStore.EXPECT().SetWerwolfOrderFinished(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes(),
        channelModeStore.EXPECT().UpdateWerwolfOrderStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes(),
        //channelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
        kfkProduce.EXPECT().Produce(gomock.Any(), gomock.Any(), gomock.Any()).Return().AnyTimes(),
    )

    type fields struct {
        sc                     *conf.ServiceConfigT
        channelModeStore       store.IStore
        BusinessCfg            conf.IBusinessConfManager
        mapScheme              sync.Map
        ChannelCli             channel.IClient
        ChannelSchemeCli       channel_scheme.IClient
        ChannelSchemeConfCli   channel_scheme_conf_mgr.IClient
        ChannelMicCli          channelmic.IClient
        ChannelApiCli          channelapi.IClient
        channelOpenGameCli     channel_open_game.IClient
        PushCli                PushNotification.IClient
        ChannelOpenGameAuthCli channel_open_game_auth.IClient
        UnifiedPayCli          unifiedPay.IClient
        AccountCli             account.IClient
        PiaCli                 pia.PiaClient
        TbeanConsumeKFKPub     kafka_produce.IKafkaProduceClient
    }
    type args struct {
        channelId    uint32
        gameId       uint32
        setId        string
        gameAbnormal bool
        gameUsers    map[uint32]uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        wantErr bool
    }{
        // TODO: Add test cases.
        {
            name: "模拟游戏正常结束",
            fields: fields{
                sc:                     serviceConfig,
                channelModeStore:       channelModeStore,
                BusinessCfg:            businessCfg,
                mapScheme:              sync.Map{},
                ChannelCli:             channelCli,
                ChannelSchemeCli:       channelSchemeCli,
                ChannelSchemeConfCli:   channelSchemeConfCli,
                ChannelMicCli:          channelMicCli,
                ChannelApiCli:          channelApiCli,
                channelOpenGameCli:     channelOpenGameCli,
                PushCli:                pushCli,
                ChannelOpenGameAuthCli: channelOpenGameAuthCli,
                UnifiedPayCli:          unifiedPayCli,
                AccountCli:             accountCli,
                TbeanConsumeKFKPub:     kfkProduce,
            },
            args: args{
                channelId:    100,
                gameId:       3,
                setId:        "tt",
                gameAbnormal: false,
                gameUsers:    map[uint32]uint32{uid: price},
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Manager{
                sc:                     tt.fields.sc,
                channelModeStore:       tt.fields.channelModeStore,
                BusinessCfg:            tt.fields.BusinessCfg,
                ChannelCli:             tt.fields.ChannelCli,
                ChannelSchemeCli:       tt.fields.ChannelSchemeCli,
                ChannelSchemeConfCli:   tt.fields.ChannelSchemeConfCli,
                ChannelMicCli:          tt.fields.ChannelMicCli,
                ChannelApiCli:          tt.fields.ChannelApiCli,
                channelOpenGameCli:     tt.fields.channelOpenGameCli,
                PushCli:                tt.fields.PushCli,
                ChannelOpenGameAuthCli: tt.fields.ChannelOpenGameAuthCli,
                UnifiedPayCli:          tt.fields.UnifiedPayCli,
                AccountCli:             tt.fields.AccountCli,
                PiaCli:                 tt.fields.PiaCli,
                TbeanConsumeKFKPub:     tt.fields.TbeanConsumeKFKPub,
            }
            if err := m.HandleGameOverEvent(tt.args.channelId, tt.args.gameId, tt.args.setId, tt.args.gameAbnormal, tt.args.gameUsers); (err != nil) != tt.wantErr {
                t.Errorf("HandleGameOverEvent() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestManager_Notify(t *testing.T) {
    ctl := gomock.NewController(t)
    genMockMgr(ctl)
    defer ctl.Finish()

    uid := uint32(2212647)
    channelId := uint32(2255775)
    price := uint32(10)
    now := time.Now().Add(-3 * time.Hour)

    rollbackOrder := &store.WerwolfOrder{
        Id:          0,
        OrderId:     "tt",
        Status:      store.OrderStatusHandling,
        ChannelId:   channelId,
        Uid:         uid,
        ItemId:      1,
        ItemName:    "预言家",
        SetId:       "set_id_1001",
        GameId:      3,
        Num:         1,
        Price:       price,
        OutsideTime: now,
        CreateTime:  now,
        UpdateTime:  now,
        TBeanTime:   now,
    }

    gomock.InOrder(
        channelModeStore.EXPECT().GetWerwolfOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(rollbackOrder, true, nil).AnyTimes(),
        accountCli.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
        businessCfg.EXPECT().GetPayAppId().Return("TT_WERWOLF").AnyTimes(),
        unifiedPayCli.EXPECT().UnfreezeAndConsume(gomock.Any(), gomock.Any()).Return("2006-01-02 15:04:05", "", nil).AnyTimes(),
        unifiedPayCli.EXPECT().UnFreezeAndRefund(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
        channelModeStore.EXPECT().SetWerwolfOrderFinished(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes(),
        channelModeStore.EXPECT().UpdateWerwolfOrderStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes(),
    )

    type fields struct {
        sc                     *conf.ServiceConfigT
        channelModeStore       store.IStore
        BusinessCfg            conf.IBusinessConfManager
        mapScheme              sync.Map
        ChannelCli             channel.IClient
        ChannelSchemeCli       channel_scheme.IClient
        ChannelSchemeConfCli   channel_scheme_conf_mgr.IClient
        ChannelMicCli          channelmic.IClient
        ChannelApiCli          channelapi.IClient
        channelOpenGameCli     channel_open_game.IClient
        PushCli                PushNotification.IClient
        ChannelOpenGameAuthCli channel_open_game_auth.IClient
        UnifiedPayCli          unifiedPay.IClient
        AccountCli             account.IClient
        PiaCli                 pia.PiaClient
        TbeanConsumeKFKPub     kafka_produce.IKafkaProduceClient
    }
    type args struct {
        ctx context.Context
        req *UnifiedPayCallback.PayNotify
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    *UnifiedPayCallback.PayNotifyResponse
        wantErr bool
    }{
        // TODO: Add test cases.
        {
            name: "Notify_ROLLBACK",
            fields: fields{
                channelModeStore:       channelModeStore,
                BusinessCfg:            businessCfg,
                mapScheme:              sync.Map{},
                ChannelCli:             channelCli,
                ChannelSchemeCli:       channelSchemeCli,
                ChannelSchemeConfCli:   channelSchemeConfCli,
                ChannelMicCli:          channelMicCli,
                ChannelApiCli:          channelApiCli,
                channelOpenGameCli:     channelOpenGameCli,
                PushCli:                pushCli,
                ChannelOpenGameAuthCli: channelOpenGameAuthCli,
                UnifiedPayCli:          unifiedPayCli,
                AccountCli:             accountCli,
                TbeanConsumeKFKPub:     kfkProduce,
            },
            args: args{
                ctx: context.Background(),
                req: &UnifiedPayCallback.PayNotify{
                    OutTradeNo: "tt",
                },
            },
            want: &UnifiedPayCallback.PayNotifyResponse{
                Confirmed: true,
                Op:        UnifiedPayCallback.Op_ROLLBACK,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Manager{
                sc:                     tt.fields.sc,
                channelModeStore:       tt.fields.channelModeStore,
                BusinessCfg:            tt.fields.BusinessCfg,
                ChannelCli:             tt.fields.ChannelCli,
                ChannelSchemeCli:       tt.fields.ChannelSchemeCli,
                ChannelSchemeConfCli:   tt.fields.ChannelSchemeConfCli,
                ChannelMicCli:          tt.fields.ChannelMicCli,
                ChannelApiCli:          tt.fields.ChannelApiCli,
                channelOpenGameCli:     tt.fields.channelOpenGameCli,
                PushCli:                tt.fields.PushCli,
                ChannelOpenGameAuthCli: tt.fields.ChannelOpenGameAuthCli,
                UnifiedPayCli:          tt.fields.UnifiedPayCli,
                AccountCli:             tt.fields.AccountCli,
                PiaCli:                 tt.fields.PiaCli,
                TbeanConsumeKFKPub:     tt.fields.TbeanConsumeKFKPub,
            }
            got, err := m.Notify(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("Notify() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("Notify() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestManager_NotifyCommit(t *testing.T) {
    ctl := gomock.NewController(t)
    genMockMgr(ctl)
    defer ctl.Finish()

    uid := uint32(2212647)
    channelId := uint32(2255775)
    price := uint32(10)
    now := time.Now().Add(-3 * time.Hour)

    commitOrder := &store.WerwolfOrder{
        Id:          0,
        OrderId:     "tt_2",
        Status:      store.OrderStatusFinish,
        ChannelId:   channelId,
        Uid:         uid,
        ItemId:      1,
        ItemName:    "狼人",
        SetId:       "set_id_1002",
        GameId:      3,
        Num:         1,
        Price:       price,
        OutsideTime: now,
        CreateTime:  now,
        UpdateTime:  now,
        TBeanTime:   now,
    }

    gomock.InOrder(
        channelModeStore.EXPECT().GetWerwolfOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(commitOrder, true, nil).AnyTimes(),
        accountCli.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
        businessCfg.EXPECT().GetPayAppId().Return("TT_WERWOLF").AnyTimes(),
        unifiedPayCli.EXPECT().UnfreezeAndConsume(gomock.Any(), gomock.Any()).Return("2006-01-02 15:04:05", "", nil).AnyTimes(),
        unifiedPayCli.EXPECT().UnFreezeAndRefund(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
        channelModeStore.EXPECT().SetWerwolfOrderFinished(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes(),
        channelModeStore.EXPECT().UpdateWerwolfOrderStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes(),
        //channelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
        kfkProduce.EXPECT().Produce(gomock.Any(), gomock.Any(), gomock.Any()).Return().AnyTimes(),
    )

    type fields struct {
        sc                     *conf.ServiceConfigT
        channelModeStore       store.IStore
        BusinessCfg            conf.IBusinessConfManager
        mapScheme              sync.Map
        ChannelCli             channel.IClient
        ChannelSchemeCli       channel_scheme.IClient
        ChannelSchemeConfCli   channel_scheme_conf_mgr.IClient
        ChannelMicCli          channelmic.IClient
        ChannelApiCli          channelapi.IClient
        channelOpenGameCli     channel_open_game.IClient
        PushCli                PushNotification.IClient
        ChannelOpenGameAuthCli channel_open_game_auth.IClient
        UnifiedPayCli          unifiedPay.IClient
        AccountCli             account.IClient
        PiaCli                 pia.PiaClient
        TbeanConsumeKFKPub     kafka_produce.IKafkaProduceClient
    }

    type args struct {
        ctx context.Context
        req *UnifiedPayCallback.PayNotify
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    *UnifiedPayCallback.PayNotifyResponse
        wantErr bool
    }{
        // TODO: Add test cases.
        {
            name: "Notify_Commit",
            fields: fields{
                sc:                     serviceConfig,
                channelModeStore:       channelModeStore,
                BusinessCfg:            businessCfg,
                mapScheme:              sync.Map{},
                ChannelCli:             channelCli,
                ChannelSchemeCli:       channelSchemeCli,
                ChannelSchemeConfCli:   channelSchemeConfCli,
                ChannelMicCli:          channelMicCli,
                ChannelApiCli:          channelApiCli,
                channelOpenGameCli:     channelOpenGameCli,
                PushCli:                pushCli,
                ChannelOpenGameAuthCli: channelOpenGameAuthCli,
                UnifiedPayCli:          unifiedPayCli,
                AccountCli:             accountCli,
                TbeanConsumeKFKPub:     kfkProduce,
            },
            args: args{
                ctx: context.Background(),
                req: &UnifiedPayCallback.PayNotify{
                    OutTradeNo: "tt",
                },
            },
            want: &UnifiedPayCallback.PayNotifyResponse{
                Confirmed: true,
                Op:        UnifiedPayCallback.Op_COMMIT,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := &Manager{
                sc:                     tt.fields.sc,
                channelModeStore:       tt.fields.channelModeStore,
                BusinessCfg:            tt.fields.BusinessCfg,
                ChannelCli:             tt.fields.ChannelCli,
                ChannelSchemeCli:       tt.fields.ChannelSchemeCli,
                ChannelSchemeConfCli:   tt.fields.ChannelSchemeConfCli,
                ChannelMicCli:          tt.fields.ChannelMicCli,
                ChannelApiCli:          tt.fields.ChannelApiCli,
                channelOpenGameCli:     tt.fields.channelOpenGameCli,
                PushCli:                tt.fields.PushCli,
                ChannelOpenGameAuthCli: tt.fields.ChannelOpenGameAuthCli,
                UnifiedPayCli:          tt.fields.UnifiedPayCli,
                AccountCli:             tt.fields.AccountCli,
                PiaCli:                 tt.fields.PiaCli,
                TbeanConsumeKFKPub:     tt.fields.TbeanConsumeKFKPub,
            }
            got, err := m.Notify(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("Notify() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("Notify() got = %v, want %v", got, tt.want)
            }
        })
    }
}
