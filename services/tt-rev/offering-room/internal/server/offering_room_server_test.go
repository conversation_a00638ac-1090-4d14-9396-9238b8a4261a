package server

import (
	"context"
	"errors"
	"golang.52tt.com/services/tt-rev/offer-room-common/lock"
	"golang.52tt.com/services/tt-rev/offer-room-common/timez"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/protocol/common/status"
	offering_room "golang.52tt.com/protocol/services/offer-room"
	switch_scheme_checker "golang.52tt.com/protocol/services/switch-scheme-checker"
	"golang.52tt.com/services/tt-rev/offering-room/internal/mgr"
	context0 "golang.org/x/net/context"
)

type offeringRoomServerHelperForTest struct {
	*offeringRoomServer
}

func newTestServer(t *testing.T) *offeringRoomServerHelperForTest {
	controller := gomock.NewController(t)
	timez.Register(timez.NewMockService(controller))
	lock.Register(lock.NewMockService(controller))
	return &offeringRoomServerHelperForTest{
		&offeringRoomServer{
			appliedListMgr:    mgr.NewMockApplyListMgr(controller),
			convertor:         mgr.NewConvertor(),
			gameMgr:           mgr.NewMockGameMgr(controller),
			offeringConfigMgr: mgr.NewMockOfferingConfigMgr(controller),
			orderMgr:          mgr.NewMockOrderMgr(controller),
			presentOrder:      mgr.NewMockPresentOrderMgr(controller),
			relationshipMgr:   mgr.NewMockRelationshipMgr(controller),
			transaction:       mgr.NewMockTransaction(controller),
		},
	}
}

func (receiver *offeringRoomServerHelperForTest) getAppliedListMgr() *mgr.MockApplyListMgr {
	return receiver.appliedListMgr.(*mgr.MockApplyListMgr)
}

func (receiver *offeringRoomServerHelperForTest) getGameMgr() *mgr.MockGameMgr {
	return receiver.gameMgr.(*mgr.MockGameMgr)
}

func (receiver *offeringRoomServerHelperForTest) getOfferingConfigMgr() *mgr.MockOfferingConfigMgr {
	return receiver.offeringConfigMgr.(*mgr.MockOfferingConfigMgr)
}

func (receiver *offeringRoomServerHelperForTest) getOrderMgr() *mgr.MockOrderMgr {
	return receiver.orderMgr.(*mgr.MockOrderMgr)
}

func (receiver *offeringRoomServerHelperForTest) getLockService() *lock.MockService {
	return lock.GetService().(*lock.MockService)
}

func (receiver *offeringRoomServerHelperForTest) getTimerService() *timez.MockService {
	return timez.GetService().(*timez.MockService)
}

func (receiver *offeringRoomServerHelperForTest) getPresentOrder() *mgr.MockPresentOrderMgr {
	return receiver.presentOrder.(*mgr.MockPresentOrderMgr)
}

func (receiver *offeringRoomServerHelperForTest) getRelationshipMgr() *mgr.MockRelationshipMgr {
	return receiver.relationshipMgr.(*mgr.MockRelationshipMgr)
}

func (receiver *offeringRoomServerHelperForTest) getTransaction() *mgr.MockTransaction {
	return receiver.transaction.(*mgr.MockTransaction)
}

func Test_offeringRoomServer_SetOfferGeneralConfig(t *testing.T) {
	type args struct {
		c       context.Context
		request *offering_room.SetOfferGeneralConfigRequest
	}
	tests := []struct {
		name     string
		args     args
		want     *offering_room.SetOfferGeneralConfigResponse
		wantErr  bool
		initFunc func(s *offeringRoomServerHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				c:       context.Background(),
				request: &offering_room.SetOfferGeneralConfigRequest{},
			},
			want:    &offering_room.SetOfferGeneralConfigResponse{},
			wantErr: false,
			initFunc: func(s *offeringRoomServerHelperForTest) {
				s.getOfferingConfigMgr().EXPECT().SetOfferGeneralConfig(context.Background(), gomock.Any()).Return(
					nil,
				)
			},
		},
		{
			name: "mgr异常",
			args: args{
				c:       context.Background(),
				request: &offering_room.SetOfferGeneralConfigRequest{},
			},
			want:    &offering_room.SetOfferGeneralConfigResponse{},
			wantErr: true,
			initFunc: func(s *offeringRoomServerHelperForTest) {
				s.getOfferingConfigMgr().EXPECT().SetOfferGeneralConfig(context.Background(), gomock.Any()).Return(
					errors.New("错误"),
				)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := newTestServer(t)
			if tt.initFunc != nil {
				tt.initFunc(o)
			}
			got, err := o.SetOfferGeneralConfig(tt.args.c, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("offeringRoomServer.SetOfferGeneralConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("offeringRoomServer.SetOfferGeneralConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_offeringRoomServer_GetOfferGeneralConfig(t *testing.T) {
	type args struct {
		c       context.Context
		request *offering_room.GetOfferGeneralConfigRequest
	}
	tests := []struct {
		name     string
		args     args
		want     *offering_room.GetOfferGeneralConfigResponse
		wantErr  bool
		initFunc func(s *offeringRoomServerHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				c:       context.Background(),
				request: &offering_room.GetOfferGeneralConfigRequest{},
			},
			want:    &offering_room.GetOfferGeneralConfigResponse{},
			wantErr: false,
			initFunc: func(s *offeringRoomServerHelperForTest) {
				s.getOfferingConfigMgr().EXPECT().GetOfferGeneralConfig(context.Background(), gomock.Any()).Return(
					&offering_room.GetOfferGeneralConfigResponse{}, nil)
			},
		},
		{
			name: "出错",
			args: args{
				c:       context.Background(),
				request: &offering_room.GetOfferGeneralConfigRequest{},
			},
			want:    &offering_room.GetOfferGeneralConfigResponse{},
			wantErr: true,
			initFunc: func(s *offeringRoomServerHelperForTest) {
				s.getOfferingConfigMgr().EXPECT().GetOfferGeneralConfig(context.Background(), gomock.Any()).Return(
					&offering_room.GetOfferGeneralConfigResponse{}, errors.New("错误"))
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := newTestServer(t)
			if tt.initFunc != nil {
				tt.initFunc(o)
			}
			got, err := o.GetOfferGeneralConfig(tt.args.c, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("offeringRoomServer.GetOfferGeneralConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("offeringRoomServer.GetOfferGeneralConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_offeringRoomServer_BatchGetUserConsumptionByGameRoundId(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *offering_room.BatchGetUserConsumptionByGameRoundIdRequest
	}
	tests := []struct {
		name     string
		args     args
		want     *offering_room.BatchGetUserConsumptionByGameRoundIdResponse
		wantErr  bool
		initFunc func(s *offeringRoomServerHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				ctx: context.Background(),
				request: &offering_room.BatchGetUserConsumptionByGameRoundIdRequest{
					GameRoundId: 1,
				},
			},
			want:    &offering_room.BatchGetUserConsumptionByGameRoundIdResponse{},
			wantErr: false,
			initFunc: func(s *offeringRoomServerHelperForTest) {
				ts := time.Now()
				s.getGameMgr().EXPECT().GetGameStartTimeById(context.Background(), int64(1)).Return(ts, nil)
				s.getGameMgr().EXPECT().BatchGetUserConsumptionByGameRoundId(context.Background(), int64(1), ts).Return(
					&offering_room.BatchGetUserConsumptionByGameRoundIdResponse{}, nil,
				)
			},
		},
		{
			name: "获取开始时间失败",
			args: args{
				ctx: context.Background(),
				request: &offering_room.BatchGetUserConsumptionByGameRoundIdRequest{
					GameRoundId: 1,
				},
			},
			want:    &offering_room.BatchGetUserConsumptionByGameRoundIdResponse{},
			wantErr: true,
			initFunc: func(s *offeringRoomServerHelperForTest) {
				s.getGameMgr().EXPECT().GetGameStartTimeById(context.Background(), int64(1)).Return(time.Time{}, errors.New("失败"))
			},
		},
		{
			name: "统计订单失败",
			args: args{
				ctx: context.Background(),
				request: &offering_room.BatchGetUserConsumptionByGameRoundIdRequest{
					GameRoundId: 1,
				},
			},
			want:    &offering_room.BatchGetUserConsumptionByGameRoundIdResponse{},
			wantErr: true,
			initFunc: func(s *offeringRoomServerHelperForTest) {
				ts := time.Now()
				s.getGameMgr().EXPECT().GetGameStartTimeById(context.Background(), int64(1)).Return(ts, nil)
				s.getGameMgr().EXPECT().BatchGetUserConsumptionByGameRoundId(context.Background(), int64(1), ts).Return(
					&offering_room.BatchGetUserConsumptionByGameRoundIdResponse{}, errors.New("失败"),
				)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := newTestServer(t)
			if tt.initFunc != nil {
				tt.initFunc(o)
			}
			got, err := o.BatchGetUserConsumptionByGameRoundId(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("offeringRoomServer.BatchGetUserConsumptionByGameRoundId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("offeringRoomServer.BatchGetUserConsumptionByGameRoundId() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_offeringRoomServer_Check(t *testing.T) {
	type args struct {
		c   context0.Context
		req *switch_scheme_checker.CheckReq
	}
	tests := []struct {
		name     string
		args     args
		want     *switch_scheme_checker.CheckResp
		wantErr  bool
		initFunc func(s *offeringRoomServerHelperForTest)
	}{
		{
			name: "not found",
			args: args{
				c: context.Background(),
				req: &switch_scheme_checker.CheckReq{
					Cid: 123,
				},
			},
			want:    &switch_scheme_checker.CheckResp{},
			wantErr: false,
			initFunc: func(s *offeringRoomServerHelperForTest) {
				s.getGameMgr().EXPECT().GetCurGame(context.Background(), uint32(123)).Return(
					nil, mgr.ErrNotFound,
				)
			},
		},
		{
			name: "出错",
			args: args{
				c: context.Background(),
				req: &switch_scheme_checker.CheckReq{
					Cid: 123,
				},
			},
			want:    &switch_scheme_checker.CheckResp{},
			wantErr: true,
			initFunc: func(s *offeringRoomServerHelperForTest) {
				s.getGameMgr().EXPECT().GetCurGame(context.Background(), uint32(123)).Return(
					nil, errors.New("错误"),
				)
			},
		},
		{
			name: "正常 可切换",
			args: args{
				c: context.Background(),
				req: &switch_scheme_checker.CheckReq{
					Cid: 123,
				},
			},
			want:    &switch_scheme_checker.CheckResp{},
			wantErr: false,
			initFunc: func(s *offeringRoomServerHelperForTest) {
				s.getGameMgr().EXPECT().GetCurGame(context.Background(), uint32(123)).Return(
					&mgr.Game{
						BaseInfo: &mgr.GameBaseInfo{
							Phase: mgr.GamePhaseUnspecified,
						},
					}, nil,
				)
			},
		},
		{
			name: "游戏进行中 不可切换",
			args: args{
				c: context.Background(),
				req: &switch_scheme_checker.CheckReq{
					Cid: 123,
				},
			},
			want: &switch_scheme_checker.CheckResp{
				Code: status.ErrOfferingRoomSwitchSchemeNotAllow,
				Msg:  "拍卖过程中不可切换其他房间模式",
			},
			wantErr: false,
			initFunc: func(s *offeringRoomServerHelperForTest) {
				s.getGameMgr().EXPECT().GetCurGame(context.Background(), uint32(123)).Return(
					&mgr.Game{
						BaseInfo: &mgr.GameBaseInfo{
							Phase: mgr.GamePhaseOffering,
						},
					}, nil,
				)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := newTestServer(t)
			if tt.initFunc != nil {
				tt.initFunc(o)
			}
			got, err := o.Check(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("offeringRoomServer.Check() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("offeringRoomServer.Check() = %v, want %v", got, tt.want)
			}
		})
	}
}
