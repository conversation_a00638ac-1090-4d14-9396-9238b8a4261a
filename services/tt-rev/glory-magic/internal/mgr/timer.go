package mgr

import (
    "context"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/timer"
    "time"
)

func (mgr *Mgr) setupTimer() error {
    // 创建定时器
    ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
    defer cancel()

    // 大小进程的定时器，同一个任务只会在一个节点上执行
    timerD, err := timer.NewTimerD(ctx, "glory-magic", timer.WithV8RedisCmdable(mgr.cache.GetRedisClient()), timer.WithTTL(10*time.Second))
    if err != nil {
        log.Errorf("setupTimer fail to NewTimerD. err:%v", err)
        return err
    }

    err = timerD.AddTask("@every 1m", "AutoUpdateCurrentStockHourly", timer.BuildFromLambda(func(ctx context.Context) {
        //根据库存配置检测更新当前库存
        mgr.AutoUpdateCurrentStockHourly()
    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 1m", "UpdateCurrentStockManually", timer.BuildFromLambda(func(ctx context.Context) {
        //每分钟更新手动库存
        mgr.UpdateCurrentStockManually()

    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 1m", "HandleMonthStock", timer.BuildFromLambda(func(ctx context.Context) {
        //每月更新库存
        mgr.HandleMonthStock()

    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 1m", "ResetStockCyclely", timer.BuildFromLambda(func(ctx context.Context) {
        // 每分钟检测一次库存重置
        mgr.ResetStockCyclely()

    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 1m", "HourReportTiming", timer.BuildFromLambda(func(ctx context.Context) {
        // 每分钟检测一次小时数据推送
        mgr.HourReportTiming()

    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 1m", "DayReportTiming", timer.BuildFromLambda(func(ctx context.Context) {
        // 每分钟检测一次每日数据推送
        mgr.DayReportTiming()

    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 5m", "UpdateLocalCommonChanceConf", timer.BuildFromLambda(func(ctx context.Context) {
        // 每5s检查一次概率配置
        mgr.UpdateLocalCommonChanceConf()
    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 1s", "StockReport", timer.BuildFromLambda(func(ctx context.Context) {
        // 每秒检查一次库存报警
        mgr.StockReport()
    }))
    if err != nil {
        return err
    }


    timerD.Start()

    mgr.timerD = timerD
    return nil
}
