package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/glory-magic"
)

const (
	noFlashCountKey        = "glory_magic:no_flash_count:%d:%d"
	noPrizeCountKey        = "glory_magic:no_prize_count:%d:%d"
	noPrizeCountTTL        = 90 * 24 * time.Hour
	userLotteryRecordKey   = "glory_magic:user_lottery_record:%d:%d" // 用户前50条抽奖记录
	winningCarouselKey     = "glory_magic:winning_carousel:%d"
	winningCarouselLen     = 9
	userConsumeValThisWeek = "glory_magic:user_consumer_val_week:%d"
	userLotteryTimesKey    = "glory_magic:user_lottery_times:%d"
	userBigPrizeMark       = "glory_magic:user_big_prize_mark:%d:%d"
	userLotteryLockKey     = "glory_magic:user_lottery_lock:%d"
)

func (c *Cache) IncrNoPrizeCount(ctx context.Context, pondType, uid uint32) error {
	key := fmt.Sprintf(noPrizeCountKey, pondType, uid)
	_, err := c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		err := pipe.Incr(ctx, key).Err()
		if err != nil {
			return err
		}
		return pipe.Expire(ctx, key, noPrizeCountTTL).Err()
	})
	return err
}

func (c *Cache) SetNoPrizeCount(ctx context.Context, pondType, uid, count uint32) error {
	key := fmt.Sprintf(noPrizeCountKey, pondType, uid)
	return c.cmder.Set(ctx, key, count, noPrizeCountTTL).Err()
}

func (c *Cache) GetNoPrizeCount(ctx context.Context, pondType, uid uint32) (uint32, error) {
	key := fmt.Sprintf(noPrizeCountKey, pondType, uid)
	noPrizeCount, err := c.cmder.Get(ctx, key).Int()
	if err != nil && err.Error() != redis.Nil.Error() {
		return 0, err
	}
	return uint32(noPrizeCount), nil
}

func (c *Cache) DelNoPrizeCount(ctx context.Context, pondType, uid uint32) error {
	return c.cmder.Del(ctx, fmt.Sprintf(noPrizeCountKey, pondType, uid)).Err()
}

func (c *Cache) IncrNoFlashCount(ctx context.Context, pondType, uid uint32) error {
	key := fmt.Sprintf(noFlashCountKey, pondType, uid)
	_, err := c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		err := pipe.Incr(ctx, key).Err()
		if err != nil {
			return err
		}
		return pipe.Expire(ctx, key, noPrizeCountTTL).Err()
	})
	return err
}

func (c *Cache) SetNoFlashCount(ctx context.Context, pondType, uid, count uint32) error {
	key := fmt.Sprintf(noFlashCountKey, pondType, uid)
	return c.cmder.Set(ctx, key, count, noPrizeCountTTL).Err()
}

func (c *Cache) GetNoFlashCount(ctx context.Context, pondType, uid uint32) (uint32, error) {
	key := fmt.Sprintf(noFlashCountKey, pondType, uid)
	noFlashCount, err := c.cmder.Get(ctx, key).Int()
	if err != nil && err.Error() != redis.Nil.Error() {
		return 0, err
	}
	return uint32(noFlashCount), nil
}

func (c *Cache) DelNoFlashCount(ctx context.Context, pondType, uid uint32) error {
	return c.cmder.Del(ctx, fmt.Sprintf(noFlashCountKey, pondType, uid)).Err()
}

func genWinningCarouselKey(pondType uint32) string {
	return fmt.Sprintf(winningCarouselKey, pondType)
}

func (c *Cache) GetWinningCarousel(ctx context.Context, pondType uint32) ([]*pb.WinningInfo, error) {
	rs := make([]*pb.WinningInfo, 0, 10)
	data, err := c.cmder.LRange(ctx, genWinningCarouselKey(pondType), 0, winningCarouselLen).Result()
	if err != nil {
		return rs, err
	}
	for _, item := range data {
		tmp := &pb.WinningInfo{}
		err = json.Unmarshal([]byte(item), tmp)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetWinningCarousel.Unmarshal fail, data: %s err: %v", item, err)
			continue
		}
		rs = append(rs, tmp)
	}

	return rs, nil
}

func (c *Cache) AddWinningCarousel(ctx context.Context, pondType uint32, data *pb.WinningInfo) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}
	_, err = c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		err := c.cmder.LPush(ctx, genWinningCarouselKey(pondType), string(dataBytes)).Err()
		if err != nil {
			return err
		}
		return c.cmder.LTrim(ctx, genWinningCarouselKey(pondType), 0, winningCarouselLen).Err()
	})
	return err
}

func (c *Cache) GetUserConsumeValThisWeek(ctx context.Context, uid uint32) (uint32, error) {
	consumeVal, err := c.cmder.Get(ctx, fmt.Sprintf(userConsumeValThisWeek, uid)).Int64()
	if redis.IsNil(err) {
		return 0, nil
	}
	if err != nil {
		return 0, err
	}

	return uint32(consumeVal), nil
}

func (c *Cache) AddUserConsumeValThisWeek(ctx context.Context, uid, val uint32) error {
	_, err := c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		key := fmt.Sprintf(userConsumeValThisWeek, uid)
		err := pipe.IncrBy(ctx, key, int64(val)).Err()
		if err != nil {
			return err
		}

		return pipe.Expire(ctx, key, c.CalUntilNextMonday()).Err()
	})
	return err
}

func (c *Cache) GetUserLotteryTimes(ctx context.Context, uid, defaultTimes uint32) (uint32, error) {
	key := fmt.Sprintf(userLotteryTimesKey, uid)
	remainTimes, err := c.cmder.Get(ctx, key).Int64()
	if redis.IsNil(err) {
		// 如果当前没有初始化次数
		return defaultTimes, c.cmder.Set(ctx, key, defaultTimes, c.CalUntilNextMonday()).Err()
	}
	if err != nil {
		return 0, err
	}
	return uint32(remainTimes), err
}

func (c *Cache) ReduceUserLotteryTimes(ctx context.Context, times, uid uint32) error {
	nowTimes, err := c.cmder.IncrBy(ctx, fmt.Sprintf(userLotteryTimesKey, uid), -int64(times)).Result()
	if err != nil {
		return err
	}
	if nowTimes < 0 {
		return fmt.Errorf("no times")
	}
	return nil
}

func (c *Cache) AddUserLotteryTimes(ctx context.Context, uid, addTimes uint32) error {
	key := fmt.Sprintf(userLotteryTimesKey, uid)
	_, err := c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		err := pipe.IncrBy(ctx, key, int64(addTimes)).Err()
		if err != nil {
			return err
		}
		return pipe.Expire(ctx, key, c.CalUntilNextMonday()).Err()
	})
	return err
}

func (c *Cache) IsWinningBigPrizeRecent30Day(ctx context.Context, pondType, uid uint32) (bool, error) {
	_, err := c.cmder.Get(ctx, fmt.Sprintf(userBigPrizeMark, pondType, uid)).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

func (c *Cache) DelWinningBigPrizeRecent30Day(ctx context.Context, pondType, uid uint32) error {
	return c.cmder.Del(ctx, fmt.Sprintf(userBigPrizeMark, pondType, uid)).Err()
}

func (c *Cache) MarkWinningBigPrizeRecent30Day(ctx context.Context, bondTYpe, uid uint32) error {
	return c.cmder.SetNX(ctx, fmt.Sprintf(userBigPrizeMark, bondTYpe, uid), "1", 30*24*time.Hour).Err()
}

func (c *Cache) LockUserLottery(ctx context.Context, uid uint32) error {
	return c.cmder.SetNX(ctx, fmt.Sprintf(userLotteryLockKey, uid), "1", 5*time.Second).Err()
}

func (c *Cache) UnlockUserLottery(ctx context.Context, uid uint32) error {
	return c.cmder.Del(ctx, fmt.Sprintf(userLotteryLockKey, uid)).Err()
}
