// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/fellow-house/internal/conf (interfaces: IBusinessConfManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIBusinessConfManager is a mock of IBusinessConfManager interface.
type MockIBusinessConfManager struct {
	ctrl     *gomock.Controller
	recorder *MockIBusinessConfManagerMockRecorder
}

// MockIBusinessConfManagerMockRecorder is the mock recorder for MockIBusinessConfManager.
type MockIBusinessConfManagerMockRecorder struct {
	mock *MockIBusinessConfManager
}

// NewMockIBusinessConfManager creates a new mock instance.
func NewMockIBusinessConfManager(ctrl *gomock.Controller) *MockIBusinessConfManager {
	mock := &MockIBusinessConfManager{ctrl: ctrl}
	mock.recorder = &MockIBusinessConfManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBusinessConfManager) EXPECT() *MockIBusinessConfManagerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockIBusinessConfManager) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIBusinessConfManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIBusinessConfManager)(nil).Close))
}

// GetBuyHouseIdBreakingNewsId mocks base method.
func (m *MockIBusinessConfManager) GetBuyHouseIdBreakingNewsId(arg0 uint32) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBuyHouseIdBreakingNewsId", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetBuyHouseIdBreakingNewsId indicates an expected call of GetBuyHouseIdBreakingNewsId.
func (mr *MockIBusinessConfManagerMockRecorder) GetBuyHouseIdBreakingNewsId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBuyHouseIdBreakingNewsId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBuyHouseIdBreakingNewsId), arg0)
}

// GetPayAppId mocks base method.
func (m *MockIBusinessConfManager) GetPayAppId() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPayAppId")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetPayAppId indicates an expected call of GetPayAppId.
func (mr *MockIBusinessConfManagerMockRecorder) GetPayAppId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayAppId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetPayAppId))
}

// Reload mocks base method.
func (m *MockIBusinessConfManager) Reload(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reload", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reload indicates an expected call of Reload.
func (mr *MockIBusinessConfManagerMockRecorder) Reload(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockIBusinessConfManager)(nil).Reload), arg0)
}

// TestPayCallBackCommitList mocks base method.
func (m *MockIBusinessConfManager) TestPayCallBackCommitList(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestPayCallBackCommitList", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// TestPayCallBackCommitList indicates an expected call of TestPayCallBackCommitList.
func (mr *MockIBusinessConfManagerMockRecorder) TestPayCallBackCommitList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestPayCallBackCommitList", reflect.TypeOf((*MockIBusinessConfManager)(nil).TestPayCallBackCommitList), arg0)
}

// TestPayRollBackList mocks base method.
func (m *MockIBusinessConfManager) TestPayRollBackList(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestPayRollBackList", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// TestPayRollBackList indicates an expected call of TestPayRollBackList.
func (mr *MockIBusinessConfManagerMockRecorder) TestPayRollBackList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestPayRollBackList", reflect.TypeOf((*MockIBusinessConfManager)(nil).TestPayRollBackList), arg0)
}

// Watch mocks base method.
func (m *MockIBusinessConfManager) Watch(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Watch", arg0)
}

// Watch indicates an expected call of Watch.
func (mr *MockIBusinessConfManagerMockRecorder) Watch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockIBusinessConfManager)(nil).Watch), arg0)
}
