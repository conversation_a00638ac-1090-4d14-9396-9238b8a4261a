package cache

import (
	"context"
	"fmt"
	"github.com/go-redis/redis"
	"github.com/opentracing/opentracing-go"
	"golang.52tt.com/pkg/log"
	"strconv"
	"time"
)

type PresentCountCache struct {
	redisCli *redis.Client
	tracer   opentracing.Tracer
}

func NewPresentCountCache(r *redis.Client, tracer opentracing.Tracer) *PresentCountCache {
	return &PresentCountCache{
		redisCli: r,
		tracer:   tracer,
	}
}

type MicPresentCountInfo struct {
	Uid   uint32
	Price uint32
}

func genPresentOrderIdCheckKey(orderId string) string {
	return fmt.Sprintf("PresentCntOrderID-%v", orderId)
}

func genChannelMicEventKey(channelId uint32) string {
	return fmt.Sprintf("MicInfo:%d", channelId)
}

func genChannelPresentCountKey(channelId uint32) string {
	return fmt.Sprintf("Count:%d", channelId)
}

// 神秘人相关缓存
func genUkwFakeUidKey(channelId, uid uint32) string {
	return fmt.Sprintf("present_count_ukw_%d_%d", channelId, uid)
}

// OrderIdCheck order_id check
func (r *PresentCountCache) OrderIdCheck(ctx context.Context, orderId string) (bool, error) {
	var ok bool

	key := genPresentOrderIdCheckKey(orderId)
	ok, err := r.redisCli.SetNX(key, "1", 3600*time.Second*14).Result() // 14hours过期
	if err != nil {
		log.ErrorWithCtx(ctx, "redis CheckOrderId fail, orderId: %v, err: %v.", orderId, err)
		return ok, err
	}

	return ok, nil
}

func (r *PresentCountCache) OrderIdDealFailRollBack(ctx context.Context, orderId string) error {
	key := genPresentOrderIdCheckKey(orderId)
	err := r.redisCli.Del(key).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "redis OrderIdDealFailRollBack fail, orderId: %v, err: %v.", orderId, err)
		return err
	}

	return nil
}

// AddChannelToCountingSet /* 记录所有已开启计数器的房间id，以及开启时间 */
func (r *PresentCountCache) AddChannelToCountingSet(ctx context.Context, channelId uint32) error {
	key := "presentCountChannels"

	now := time.Now()
	nowSec := now.Unix()

	scoreMember := make([]redis.Z, 1)
	scoreMember[0].Member = fmt.Sprintf("%v", channelId)
	scoreMember[0].Score = float64(nowSec)

	err := r.redisCli.ZAdd(key, scoreMember...).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelToCountingSet fail, channelId:%d, err:%v", channelId, err)
		return err
	}

	return nil
}

func (r *PresentCountCache) DelChannelFromCountingSet(ctx context.Context, channelId uint32) error {
	key := "presentCountChannels"

	member := make([]interface{}, 1)
	member = append(member, fmt.Sprintf("%v", channelId)) // nozero
	err := r.redisCli.ZRem(key, member...).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelFromCountingSet fail, channelId:%d, err:%v", channelId, err)
		return err
	}

	return nil
}

// GetExpiredMemberFromSet 寻找超过最长计数时间的 channel member
func (r *PresentCountCache) GetExpiredMemberFromSet(ctx context.Context, ttl, nowSec int64) ([]uint32, error) {
	key := "presentCountChannels"
	channelList := make([]uint32, 0)

	memberS, err := r.redisCli.ZRevRangeByScore(key, redis.ZRangeBy{
		Min:    "-inf",
		Max:    fmt.Sprintf("%d", nowSec-ttl),
		Offset: 0,
		Count:  int64(10000),
	}).Result()

	if err != nil {
		log.ErrorWithCtx(ctx, "GetExpiredMemberFromSet fail ZRevRangeByScore, nowSec(%d),ttl(%d),err(%v)", nowSec, ttl, err)
		return channelList, err
	}

	for _, item := range memberS {
		channelId, err := strconv.ParseUint(item, 10, 32)
		if nil != err {
			log.ErrorWithCtx(ctx, "GetExpiredMemberFromSet ParseUint err:%v,channelId:%s", err, item)
			continue
		}

		channelList = append(channelList, uint32(channelId))
	}

	return channelList, nil
}

// GetChannelStartTimeSec 用于计算计数器的ttl
func (r *PresentCountCache) GetChannelStartTimeSec(ctx context.Context, channelId uint32) (int64, error) {
	key := "presentCountChannels"

	member := fmt.Sprintf("%v", channelId)
	score, err := r.redisCli.ZScore(key, member).Result()
	if err == redis.Nil {
		return 0, nil
	} else if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelStartTimeSec fail, channelId:%d, err:%v", channelId, err)
		return int64(0), err
	}

	return int64(score), nil
}

func (r *PresentCountCache) CheckIfMemberInSet(ctx context.Context, channelId, targetUid uint32) (bool, error) {
	key := genChannelMicEventKey(channelId)
	member := fmt.Sprintf("%v", targetUid)

	ok, err := r.redisCli.SIsMember(key, member).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckIfMemberInSet fail, key:%s, member:%s, err:%v", key, member, err)
		return false, err
	}

	return ok, nil
}

func (r *PresentCountCache) AddToChannelMicSet(ctx context.Context, channelId uint32, uidList []uint32) error {
	key := genChannelMicEventKey(channelId)

	memberS := make([]interface{}, 0, len(uidList))
	for _, uid := range uidList {
		memberS = append(memberS, uid)
	}
	err := r.redisCli.SAdd(key, memberS...).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddToChannelMicSet fail, key:%d, member:%v, err:%v", key, memberS, err)
		return err
	}

	if len(uidList) > 1 {
		_ = r.redisCli.Expire(key, 24*time.Hour).Err()
	}

	return nil
}

func (r *PresentCountCache) DelFromChannelMicSet(ctx context.Context, channelId, uid uint32) error {
	key := genChannelMicEventKey(channelId)

	member := fmt.Sprintf("%v", uid)
	err := r.redisCli.SRem(key, member).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelFromChannelMicSet fail, key:%d, member:%d, err:%v", key, member, err)
		return err
	}

	return nil
}

func (r *PresentCountCache) IncrUserPresentCount(ctx context.Context, channelId, uid, price uint32) (int64, error) {
	key := genChannelPresentCountKey(channelId)

	member := fmt.Sprintf("%v", uid)
	score, err := r.redisCli.ZIncrBy(key, float64(price), member).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "IncrUserPresentCount fail, key:%d, member:%d, err:%v", key, member, err)
		return int64(0), err
	}

	// 设置键过期时间
	_ = r.redisCli.Expire(key, 24*time.Hour).Err()

	return int64(score), nil
}

func (r *PresentCountCache) GetChannelUserPresentCnt(ctx context.Context, channelId, uid uint32) (int64, error) {
	key := genChannelPresentCountKey(channelId)
	member := fmt.Sprintf("%v", uid)
	score, err := r.redisCli.ZScore(key, member).Result()

	if err == redis.Nil {
		return int64(0), nil
	} else if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelUserPresentCnt fail, key:%d, member:%d, err:%v", key, member, err)
		return int64(0), err
	}

	return int64(score), nil
}

func (r *PresentCountCache) ClearChannelUserPresentCnt(ctx context.Context, channelId, uid uint32) error {
	log.Debugf("ClearChannelUserPresentCnt ykw channelId:%d,uid:%d", channelId, uid)
	key := genChannelPresentCountKey(channelId)
	member := fmt.Sprintf("%v", uid)
	return r.redisCli.ZRem(key, member).Err()
}

func (r *PresentCountCache) GetChannelAllPresentCnt(ctx context.Context, channelId uint32) ([]*MicPresentCountInfo, error) {
	key := genChannelPresentCountKey(channelId)
	res := make([]*MicPresentCountInfo, 0)

	z, err := r.redisCli.ZRangeWithScores(key, 0, -1).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelAllPresentCnt fail ZRangeWithScores, key:%d, err:%v", key, err)
		return res, err
	}

	for _, item := range z {
		intVal, err := strconv.ParseUint(item.Member.(string), 10, 32)
		if nil != err {
			log.ErrorWithCtx(ctx, "GetChannelAllPresentCnt ParseUint err:%v", err)
			continue
		}

		res = append(res, &MicPresentCountInfo{
			Uid:   uint32(intVal),
			Price: uint32(item.Score),
		})
	}

	return res, nil
}

// DelPresentCntAndMicList 当房间主动关闭/本场计数超过 设定值 时，自动关闭本场计数，清理对应的key
func (r *PresentCountCache) DelPresentCntAndMicList(ctx context.Context, channelId uint32) error {
	key1 := genChannelPresentCountKey(channelId)
	key2 := genChannelMicEventKey(channelId)

	err := r.redisCli.Del(key1).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelPresentCountKey fail, key:%d, err:%v", key1, err)
		return err
	}

	err = r.redisCli.Del(key2).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelPresentCountKey fail, key:%d, err:%v", key2, err)
		return err
	}
	return nil
}

func (r *PresentCountCache) SetNewUkwFakeUid(channelId, uid, fakeUid uint32) (bool, error) {
	key := genUkwFakeUidKey(channelId, uid)
	result, err := r.redisCli.Get(key).Result()
	if err != nil && err != redis.Nil {
		log.Errorf("SetNewUkwFakeUid fail to Get, channelId:%d, uid:%d, err:%v", channelId, uid, err)
		return false, err
	}

	change := false
	oldFakeUid, _ := strconv.Atoi(result)
	if fakeUid != uint32(oldFakeUid) {
		change = true
	}

	if fakeUid == uid {
		r.redisCli.Del(key)
	} else {
		r.redisCli.Set(key, fakeUid, 12*time.Hour)
	}

	return change, nil
}
