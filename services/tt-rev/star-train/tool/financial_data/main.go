/******** 生成月度剩余表 *******/
package main

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"github.com/go-gomail/gomail"
	"github.com/tealeg/xlsx"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"io/ioutil"
	"time"
)

var st *Store

type ServiceConfigT struct {
	MysqlReadOnlyConfig *config.MysqlConfig `json:"readonly_mysql"`
	MailsTo             []string            `json:"mails_to"`
	FeiShuUrl           string              `json:"fei_shu_url"`
}

func (sc *ServiceConfigT) Parse(configFile string) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, &sc)
	if err != nil {
		return err
	}

	log.Infof("ServiceConfigT:MysqlConfig:%+v\n", sc.MysqlReadOnlyConfig)
	return
}

func main() {
	sc := &ServiceConfigT{}
	err := sc.Parse("/home/<USER>/star-train/star-train.json")
	if err != nil {
		log.Errorf("Parse fail. err:%v", err)
		return
	}

	now := time.Now()
	monthTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	lastMonthTime := monthTime.AddDate(0, -1, 0)

	beginTime, endTime := lastMonthTime, monthTime

	// test
	//dayTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	//beginTime, endTime = monthTime, dayTime

	st, err = NewMysql(sc.MysqlReadOnlyConfig)
	if err != nil {
		log.Errorf("NewMysql fail. err:%v", err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Minute)
	defer cancel()

	// 创建报表
	file := xlsx.NewFile()

	err = genSummarySheet(ctx, file, beginTime, endTime)
	if err != nil {
		fmt.Printf("genSummarySheet fail, err: %v\n", err)
		return
	}

	err = genDetailSheet(ctx, file, beginTime, endTime)
	if err != nil {
		fmt.Printf("genDetailSheet fail, err: %v\n", err)
		return
	}

	filePath := fmt.Sprintf("/home/<USER>/star-train/摘星列车月数据_%04d%02d.xlsx", beginTime.Year(), beginTime.Month())
	err = file.Save(filePath)
	if err != nil {
		fmt.Printf("file save fail, err: %v\n", err)
		return
	}

	// send email
	subject := fmt.Sprintf("摘星列车财务月数据 %s-%s", beginTime.Format("2006-01-02 15点"), endTime.Format("2006-01-02 15点"))
	err = SendMail(filePath, subject, sc.MailsTo)
	if err != nil {
		fmt.Printf("Failed to SendMail %v\n", err)
		return
	}

	log.Infof("cost:%v", time.Since(now))
	return
}

func genDetailSheet(ctx context.Context, file *xlsx.File, begin, end time.Time) error {
	list, err := st.GetDetailList(ctx, begin, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "getDetailData fail. begin:%v, end:%v, err:%v", begin, end, err)
		return err
	}

	sheet, err := file.AddSheet("明细")
	if err != nil {
		log.Errorf("genDetailSheet fail to AddSheet. err:%v", err)
		return err
	}

	sheet.AddRow().WriteSlice(&[]string{
		"日期", "轮次id", "玩法流水", "玩法送礼人数", "发放礼物个数", "发放礼物价值", "玩法获奖人数", "发放礼物占流水比例",
	}, -1)
	for _, v := range list {
		row := sheet.AddRow()
		row.AddCell().SetString(v.RoundTime.Format("2006-01-02"))
		row.AddCell().SetInt(int(v.RoundID))
		row.AddCell().SetInt64(int64(v.JoinFee))
		row.AddCell().SetInt(int(v.JoinUserCnt))
		row.AddCell().SetInt(int(v.AwardCnt))
		row.AddCell().SetInt64(int64(v.AwardFee))
		row.AddCell().SetInt(int(v.AwardUserCnt))

		// 发放礼物占流水比例
		awardRatio := 0.0
		if v.JoinFee != 0 {
			awardRatio = float64(v.AwardFee) / float64(v.JoinFee)
		}
		row.AddCell().SetString(fmt.Sprintf("%.2f%%", awardRatio*100))
	}

	return nil
}

func genSummarySheet(ctx context.Context, file *xlsx.File, begin, end time.Time) error {
	info, err := st.GetSummary(ctx, begin, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "getSummaryData fail. begin:%v, end:%v, err:%v", begin, end, err)
		return err
	}

	sheet, err := file.AddSheet("汇总")
	if err != nil {
		log.Errorf("genSummarySheet fail to AddSheet. err:%v", err)
		return err
	}

	sheet.AddRow().WriteSlice(&[]string{"月份", "轮次数", "玩法送礼人数", "玩法送礼流水", "发放礼物价值", "发放礼物个数", "玩法获奖人数"}, -1)
	row := sheet.AddRow()
	row.AddCell().SetString(begin.Format("2006-01"))
	row.AddCell().SetInt(int(info.RoundCnt))
	row.AddCell().SetInt(int(info.JoinUserCnt))
	row.AddCell().SetInt64(int64(info.JoinFee))
	row.AddCell().SetInt64(int64(info.AwardFee))
	row.AddCell().SetInt(int(info.AwardCnt))
	row.AddCell().SetInt(int(info.AwardUserCnt))

	log.DebugWithCtx(ctx, "info:%+v", info)
	return nil
}

func SendMail(filePath, subject string, to []string) error {
	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")

	m.SetHeader("To", to...)
	m.SetHeader("Subject", subject)

	m.SetBody("text/html", "见附件")
	m.Attach(filePath) //附件

	d := gomail.NewDialer("mail.52tt.com", 465, "<EMAIL>", "V7.D.sTy@%bDB50t#n.r$A4h+-FnXA")

	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}
