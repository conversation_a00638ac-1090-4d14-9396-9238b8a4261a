package internal

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/core/log"
    "golang.52tt.com/clients/channel"
    "golang.52tt.com/clients/realnameauth"
    risk_mng_api "golang.52tt.com/clients/risk-mng-api"
    user_online "golang.52tt.com/clients/user-online"
    userprofileapi "golang.52tt.com/clients/user-profile-api"
    userPresent "golang.52tt.com/clients/userpresent"
    usual_device "golang.52tt.com/clients/usual-device"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    channelPB "golang.52tt.com/protocol/app/channel"
    "golang.52tt.com/protocol/app/channel_gift_pk"
    "golang.52tt.com/protocol/common/status"
    channel_gift_pk_svr "golang.52tt.com/protocol/services/channel_gift_pk"
    "golang.52tt.com/protocol/services/demo/echo"
    riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
    "golang.52tt.com/protocol/services/tt_rev_common"
    "golang.52tt.com/protocol/services/userpresent"
    "golang.52tt.com/services/tt-rev/common/financial_security"
    "google.golang.org/grpc/codes"
    "sort"
    "strings"
)

var (
	errUnimplemented = protocol.NewExactServerError(codes.OK, status.ErrGrpcUnimplemented)
)

type StartConfig struct {
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	riskMngApiCli := risk_mng_api.NewIClient()
	userProfileApi, _ := userprofileapi.NewClient()
	channelGiftPkCli, err := channel_gift_pk_svr.NewClient(ctx)
	if err != nil {
		log.Errorf("channel_gift_pk_svr.NewClient err:%v", err)
	}

	usualDeviceClient := usual_device.NewClient()
	realNameClient := realnameauth.NewClient()
	channelCli := channel.NewClient()
	userPresentCli := userPresent.NewClient()
	userOnlineCli, _ := user_online.NewClient()

	return &Server{
		riskMngApiCli:     riskMngApiCli,
		channelGiftPkCli:  channelGiftPkCli,
		UserProfileCli:    userProfileApi,
		usualDeviceClient: usualDeviceClient,
		realNameClient:    realNameClient,
		channelCli:        channelCli,
		userPresentCli:    userPresentCli,
		userOnlineCli:     userOnlineCli,
	}, nil
}

type Server struct {
	riskMngApiCli     risk_mng_api.IClient
	channelGiftPkCli  channel_gift_pk_svr.ChannelGiftPkClient
	UserProfileCli    userprofileapi.IClient
	usualDeviceClient usual_device.IClient
	realNameClient    realnameauth.IClient
	channelCli        channel.IClient
	userPresentCli    userPresent.IClient
	userOnlineCli     user_online.IClient
}

func (s *Server) ShutDown() {}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

// CheckChannelGiftPkEntry 获取入口状态
func (s *Server) CheckChannelGiftPkEntry(ctx context.Context, req *channel_gift_pk.CheckChannelGiftPkEntryRequest) (*channel_gift_pk.CheckChannelGiftPkEntryResponse, error) {
	out := &channel_gift_pk.CheckChannelGiftPkEntryResponse{}
	if req.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "CheckChannelGiftPkEntry channel_id is empty. req:%+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "CheckChannelGiftPkEntry ServiceInfoFromContext fail. req:%+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	opUid := serviceInfo.UserID

	entryResp, err := s.channelGiftPkCli.CheckChannelGiftPkEntry(ctx, &channel_gift_pk_svr.CheckChannelGiftPkEntryReq{
		Uid: opUid, ChannelId: req.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckChannelGiftPkEntry fail to CheckChannelGiftPkEntry. req:%+v, err:%v", req, err)
		return out, err
	}

	out.HasEntry = entryResp.GetHasEntry()
	if !out.HasEntry {
		return out, nil
	}

	cfg := entryResp.GetGiftPkCfg()
	giftIdList := make([]uint32, 0)
	for _, v := range cfg.GetPkLvGifts() {
		giftIdList = append(giftIdList, v.GetGiftId())
	}

	// 批量获取礼物配置
	giftCfgResp, err := s.userPresentCli.GetPresentConfigByIdList(ctx, opUid, giftIdList, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckChannelGiftPkEntry fail to GetPresentConfigByIdList. req:%+v, err:%v", req, err)
		return out, err
	}

	giftCfgMap := make(map[uint32]*userpresent.StPresentItemConfig)
	for _, v := range giftCfgResp.GetItemList() {
		giftCfgMap[v.GetItemId()] = v
	}

	out.GiftPkCfg = &channel_gift_pk.GiftPkCfg{
		CommonRes: fillChannelGiftPkResource(cfg.GetCommonRes()),
		PkLvGifts: make([]*channel_gift_pk.PkLvGiftCfg, 0),
		PkSprites: make([]*channel_gift_pk.PkSpriteCfg, 0),
	}

	for _, v := range cfg.GetPkLvGifts() {
		giftCfg, ok := giftCfgMap[v.GetGiftId()]
		if !ok {
			continue
		}

		out.GiftPkCfg.PkLvGifts = append(out.GiftPkCfg.PkLvGifts, &channel_gift_pk.PkLvGiftCfg{
			LvCfg:   fillPkLevelCfg(v.GetLvCfg()),
			GiftCfg: fillPkGiftCfg(giftCfg),
		})
	}

	for _, v := range cfg.GetPkSprites() {
		out.GiftPkCfg.PkSprites = append(out.GiftPkCfg.PkSprites, fillPkSpriteCfg(v))
	}

	return out, nil
}

// GetChannelGiftPkInfo 获取对决信息
func (s *Server) GetChannelGiftPkInfo(ctx context.Context, req *channel_gift_pk.GetChannelGiftPkInfoRequest) (*channel_gift_pk.GetChannelGiftPkInfoResponse, error) {
	out := &channel_gift_pk.GetChannelGiftPkInfoResponse{}
	if req.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "GetChannelGiftPkInfo channel_id is empty. req:%+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetChannelGiftPkInfo ServiceInfoFromContext fail. req:%+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	opUid := serviceInfo.UserID

	infoResp, err := s.channelGiftPkCli.GetChannelGiftPkInfo(ctx, &channel_gift_pk_svr.GetChannelGiftPkInfoReq{
		Uid: opUid, ChannelId: req.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelGiftPkInfo fail to GetChannelGiftPkInfo. req:%+v, err:%v", req, err)
		return out, err
	}

	pkInfo := infoResp.GetGiftPkInfo()
	uidList := make([]uint32, 0)
	for _, v := range []uint32{
		pkInfo.GetMyPkInfos().GetSponsorUid(), pkInfo.GetMyPkInfos().GetAnchorUid(),
		pkInfo.GetEnemyPkInfos().GetSponsorUid(), pkInfo.GetEnemyPkInfos().GetAnchorUid(),
	} {
		if v > 0 {
			uidList = append(uidList, v)
		}
	}

	userMap := make(map[uint32]*app.UserProfile)
	if len(uidList) > 0 {
		userMap, err = s.UserProfileCli.BatchGetUserProfileV2(ctx, uidList, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelGiftPkInfo fail to BatchGetUserProfileV2. req:%+v, err:%v", req, err)
			return out, err
		}
	}

	out.GiftPkInfo = &channel_gift_pk.GiftPkInfo{
		PkId:         pkInfo.GetPkId(),
		Status:       pkInfo.GetStatus(),
		MyPkInfos:    fillPkInfo(pkInfo.GetMyPkInfos(), userMap),
		EnemyPkInfos: fillPkInfo(pkInfo.GetEnemyPkInfos(), userMap),
		StartTime:    pkInfo.GetStartTime(),
		EndTime:      pkInfo.GetEndTime(),
		ServerMs:     pkInfo.GetServerMs(),
	}

	if pkInfo.GetPkGift().GetGiftId() > 0 {
		presentResp, err := s.userPresentCli.GetPresentConfigById(ctx, pkInfo.GetPkGift().GetGiftId())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelGiftPkInfo fail to GetPresentConfigById. req:%+v, err:%v", req, err)
			return out, err
		}
		out.GetGiftPkInfo().PkGift = &channel_gift_pk.PkLvGiftCfg{
			GiftCfg: fillPkGiftCfg(presentResp.GetItemConfig()),
			LvCfg:   fillPkLevelCfg(pkInfo.GetPkGift().GetLvCfg()),
		}
	}

	return out, nil
}

// SponsorChannelGiftPk 发起对决
func (s *Server) SponsorChannelGiftPk(ctx context.Context, req *channel_gift_pk.SponsorChannelGiftPkRequest) (*channel_gift_pk.SponsorChannelGiftPkResponse, error) {
	out := &channel_gift_pk.SponsorChannelGiftPkResponse{}
	if req.GetChannelId() == 0 || req.GetGiftId() == 0 || req.GetPrice() == 0 {
		log.ErrorWithCtx(ctx, "SponsorChannelGiftPk fail. req:%+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "SponsorChannelGiftPk ServiceInfoFromContext fail. req:%+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	opUid := serviceInfo.UserID

	channelResp, sErr := s.channelCli.GetChannelSimpleInfo(ctx, opUid, req.GetChannelId())
	if sErr != nil {
		log.ErrorWithCtx(ctx, "SponsorChannelGiftPk fail to GetChannelSimpleInfo. req:%+v, err:%v", req, sErr)
		return out, sErr
	}
	if channelResp.GetChannelType() != uint32(channelPB.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		log.WarnWithCtx(ctx, "SponsorChannelGiftPk channel type is not radio. req:%+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrChannelGiftPkNotEntry)
	}

    toUid := channelResp.GetCreaterUid()
	if opUid == toUid {
		log.WarnWithCtx(ctx, "SponsorChannelGiftPk user is anchor. req:%+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrChannelGiftPkCannotMatch, "不支持达人在本人的听听房参与玩法哦~")
	}

	user, sErr := s.UserProfileCli.GetUserProfileV2(ctx, opUid, true)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "SponsorChannelGiftPk fail to GetUserProfileV2. req:%+v, err:%v", req, sErr)
		return out, sErr
	}
	if user.GetPrivilege().GetType() == uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
		log.ErrorWithCtx(ctx, "SponsorChannelGiftPk user is UKW. req:%+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrChannelGiftPkCannotMatch, "请先关闭神秘人身份再来匹配哦")
	}

	// 资金安全风险检查
	if err := financial_security.CheckInternalFinancialSecurity(ctx, serviceInfo.UserID, toUid); err != nil {
		log.ErrorWithCtx(ctx, "SendFellowInvite CheckInternalFinancialSecurity failed , in : %v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrChannelGiftPkCannotMatch, "账号有风险，请稍后再试~")
	}

	// 风控检查
	var err error
	out.BaseResp, err = s.riskCheckBeforeConsume(ctx, req.GetChannelId(), req.GetPrice(), toUid, req.GetBaseReq())
	if err != nil {
		log.ErrorWithCtx(ctx, "SponsorChannelGiftPk fail to riskCheckBeforeConsume. in:%+v, err:%v", req, err)
		return out, err
	}

	// 首消检查
	err = s.checkUsualDevice(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "SponsorChannelGiftPk fail to checkUsualDevice. in:%+v, err:%v", req, err)
		return out, err
	}

	sponsorResp, err := s.channelGiftPkCli.SponsorChannelGiftPk(ctx, &channel_gift_pk_svr.SponsorChannelGiftPkReq{
		Uid:       opUid,
		ChannelId: req.GetChannelId(),
		GiftId:    req.GetGiftId(),
		Price:     req.GetPrice(),
		ToUid:     toUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SponsorChannelGiftPk fail to SponsorChannelGiftPk. req:%+v, err:%v", req, err)
		return out, err
	}

	out.Balance = sponsorResp.GetBalance()

	log.InfoWithCtx(ctx, "SponsorChannelGiftPk success. req:%+v, resp:%+v", req, out)
	return out, nil
}

func (s *Server) checkUsualDevice(ctx context.Context) error {
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}

	uid := serviceInfo.UserID
	res, sErr := s.usualDeviceClient.CheckUsualDevice(ctx, string(serviceInfo.DeviceID), uid, 1, uint32(serviceInfo.ClientType))
	if sErr != nil {
		log.ErrorWithCtx(ctx, "checkUsualDevice Fail to CheckUsualDevice err(%v)", sErr)
		return sErr
	}
	if !res.GetResult() {
		err := s.usualDeviceClient.GetDeviceAuthError(ctx, uint64(uid), serviceInfo.ClientType, serviceInfo.ClientVersion)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *Server) riskCheckBeforeConsume(ctx context.Context, cid, price, anchorUid uint32, baseReq *app.BaseReq) (*app.BaseResp, error) {
	// 返回给客户端的 BaseResp
	baseResp := &app.BaseResp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return baseResp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	uid := serviceInfo.UserID

	onlineInfo, err := s.userOnlineCli.GetLatestOnlineInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "riskCheckBeforeConsume fail to GetLatestOnlineInfo. err:%v", err)
		return baseResp, nil
	}

	checkReq := &riskMngApiPb.CheckReq{
		Scene: "CONSUME",
		SourceEntity: &riskMngApiPb.Entity{
			// 必选
			Uid: uid,
			// 设备信息，如果 ctx 里面有 servceInfo，可以不填，风控媏自行查 ctx
			DeviceIdRaw:  serviceInfo.DeviceID,
			ClientIp:     serviceInfo.ClientIPAddr().String(),
			TerminalType: serviceInfo.TerminalType,
			// 可选，用户详细信息，若有现成的数据，建议填写，未来可能有用
			Phone: "",
			// 可选， 房间信息，若有现成的数据，建议填写，未来可能有用
			ChannelId:     cid,
			ChannelViewId: "",
		},
		// 通用参数传递
		CustomParams: map[string]string{
			"rcv_uid":      fmt.Sprint(anchorUid),
			"rcv_deviceId": strings.ToLower(onlineInfo.GetDeviceIdHex()),
			"rcv_ip":       onlineInfo.GetClientIp(),
			"consume_type": "2", // 2-"预扣T豆"
			"scene_id":     fmt.Sprintf("%d", tt_rev_common.ConsumeSceneType_CONSUME_SCENE_TYPE_CHANNEL_GIFT_PK),
			"amount":       fmt.Sprintf("%.2f", float64(price)/100.0),
		},
	}
	checkResp, err := s.riskMngApiCli.CheckHelper(ctx, checkReq, baseReq)
	if err != nil {
		// 系统错误，风控非关键路径，可忽略系统错误
		log.ErrorWithCtx(ctx, "riskCheckBeforeConsume risk-mng-api.Check failed, err:%v, req:%+v", err, checkReq)
		return baseResp, nil
	}
	// 命中风控拦截
	if checkResp.GetErrCode() < 0 {
		// 建议打个 info 拦截日志，方便排查，风控拦截日志不会很多
		log.InfoWithCtx(ctx, "riskCheckBeforeConsume risk-mng-api.Check hit, req:%+v, resp:%+v", checkReq, checkResp)
		// 需要返回 ErrInfo 给客户端
		baseResp.ErrInfo = checkResp.GetErrInfo()
		// 返回错误码给客户端，并设置 gRPC 错误码为 OK
		return baseResp, protocol.NewExactServerError(codes.OK, int(checkResp.GetErrCode()), checkResp.GetErrMsg())
	}
	// 无拦截
	return baseResp, nil
}

// CancelChannelGiftPkMatch 取消对决匹配
func (s *Server) CancelChannelGiftPkMatch(ctx context.Context, req *channel_gift_pk.CancelChannelGiftPkMatchRequest) (*channel_gift_pk.CancelChannelGiftPkMatchResponse, error) {
	out := &channel_gift_pk.CancelChannelGiftPkMatchResponse{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "CancelChannelGiftPkMatch ServiceInfoFromContext fail. req:%+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	opUid := serviceInfo.UserID

	_, err := s.channelGiftPkCli.CancelChannelGiftPkMatch(ctx, &channel_gift_pk_svr.CancelChannelGiftPkMatchReq{
		Uid:       opUid,
		ChannelId: req.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelGiftPkMatch fail to CancelChannelGiftPkMatch. req:%+v, err:%v", req, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "CancelChannelGiftPkMatch success. req:%+v, resp:%+v", req, out)
	return out, nil
}

// ChooseChannelGiftPkSprite 选择对决精灵
func (s *Server) ChooseChannelGiftPkSprite(ctx context.Context, req *channel_gift_pk.ChooseChannelGiftPkSpriteRequest) (*channel_gift_pk.ChooseChannelGiftPkSpriteResponse, error) {
	out := &channel_gift_pk.ChooseChannelGiftPkSpriteResponse{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "ChooseChannelGiftPkSprite ServiceInfoFromContext fail. req:%+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	opUid := serviceInfo.UserID

	_, err := s.channelGiftPkCli.ChooseChannelGiftPkSprite(ctx, &channel_gift_pk_svr.ChooseChannelGiftPkSpriteReq{
		Uid:            opUid,
		ChannelId:      req.GetChannelId(),
		SpriteId:       req.GetSpriteId(),
		IsPreselection: req.GetIsPreselection(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChooseChannelGiftPkSprite fail to ChooseChannelGiftPkSprite. req:%+v, err:%v", req, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "ChooseChannelGiftPkSprite success. req:%+v, resp:%+v", req, out)
	return out, nil
}

// GetRecentlyChannelGiftPkLog 获取最新对决记录播报
func (s *Server) GetRecentlyChannelGiftPkLog(ctx context.Context, req *channel_gift_pk.GetRecentlyChannelGiftPkLogRequest) (*channel_gift_pk.GetRecentlyChannelGiftPkLogResponse, error) {
	out := &channel_gift_pk.GetRecentlyChannelGiftPkLogResponse{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetRecentlyChannelGiftPkLog ServiceInfoFromContext fail. req:%+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	opUid := serviceInfo.UserID

	if req.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "GetRecentlyChannelGiftPkLog channel_id is empty. uid:%d req:%+v", opUid, req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}

	logResp, err := s.channelGiftPkCli.GetChannelGiftPkWinRecord(ctx, &channel_gift_pk_svr.GetChannelGiftPkWinRecordReq{
		ChannelId: req.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecentlyChannelGiftPkLog fail to GetChannelGiftPkWinRecord. req:%+v, err:%v", req, err)
		return out, err
	}

	out.Records, err = s.fillChannelPkWinningRecord(ctx, logResp.GetWinRecord(), opUid)
	// 按时间倒序
	sort.SliceStable(out.Records, func(i, j int) bool {
		return out.Records[i].GetEndTime() > out.Records[j].GetEndTime()
	})

	return out, nil
}

// GetChannelGiftPkRecord 获取对决记录
func (s *Server) GetChannelGiftPkRecord(ctx context.Context, req *channel_gift_pk.GetChannelGiftPkRecordRequest) (*channel_gift_pk.GetChannelGiftPkRecordResponse, error) {
	out := &channel_gift_pk.GetChannelGiftPkRecordResponse{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetChannelGiftPkRecord ServiceInfoFromContext fail. req:%+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	opUid := serviceInfo.UserID

	// 参数检查
	if req.GetChannelId() == 0 || req.GetLimit() == 0 {
		log.ErrorWithCtx(ctx, "GetChannelGiftPkRecord channel_id is empty. req:%+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}

	limit := req.GetLimit()
	if limit > 30 {
		limit = 30
	}

	recordResp, err := s.channelGiftPkCli.GetChannelGiftPkRecord(ctx, &channel_gift_pk_svr.GetChannelGiftPkRecordReq{
		ChannelId: req.GetChannelId(),
		Offset:    req.GetOffset(),
		Limit:     limit,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelGiftPkRecord fail to GetChannelGiftPkRecord. req:%+v, err:%v", req, err)
		return out, err
	}

	out.Records, err = s.fillChannelPkRecordList(ctx, recordResp.GetPkRecord(), opUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelGiftPkRecord fail to fillChannelPkRecordList. req:%+v, err:%v", req, err)
		return out, err
	}

	// 按时间倒序排序
	sort.SliceStable(out.Records, func(i, j int) bool {
		return out.Records[i].GetEndTime() > out.Records[j].GetEndTime()
	})

	out.NextOffset = recordResp.GetNextOffset()
	out.FooterText = recordResp.GetFooterText()
	return out, nil
}
