package internal

import (
    "context"
    "golang.52tt.com/clients/account"
    chance_game_entry "golang.52tt.com/clients/chance-game-entry"
    star_trek "golang.52tt.com/clients/star-trek"
    usual_device "golang.52tt.com/clients/usual-device"
    "golang.52tt.com/services/tt-rev/star-trek-logic/internal/conf"
    "reflect"
    "testing"
    pb "golang.52tt.com/protocol/app/star-trek-logic"
    "time"
    "github.com/golang/mock/gomock"
    accountMock "golang.52tt.com/clients/mocks/account"
    usualDevice "golang.52tt.com/clients/mocks/usual-device"
    "golang.52tt.com/services/tt-rev/star-trek-logic/mocks"
)

//
//import (
//	"context"
//	"errors"
//	"github.com/golang/mock/gomock"
//	"github.com/stretchr/testify/assert"
//	accountPb "golang.52tt.com/clients/account"
//	accountMock "golang.52tt.com/clients/mocks/account"
//	entry "golang.52tt.com/clients/mocks/chance-game-entry"
//	starTrek "golang.52tt.com/clients/mocks/star-trek"
//	usualDevice "golang.52tt.com/clients/mocks/usual-device"
//	"golang.52tt.com/pkg/protocol"
//	protogrpc "golang.52tt.com/pkg/protocol/grpc"
//	pb "golang.52tt.com/protocol/app/star-trek-logic"
//	chance_game_entry "golang.52tt.com/protocol/services/chance-game-entry"
//	star_trek "golang.52tt.com/protocol/services/star-trek"
//	usual_device_svr "golang.52tt.com/protocol/services/usual-device-svr"
//	"golang.52tt.com/services/tt-rev/star-trek-logic/internal/conf"
//	"golang.52tt.com/services/tt-rev/star-trek-logic/mocks"
//	"testing"
//	"time"
//)
//
//func TestServer_StarTrekEntryAndNotify(t *testing.T) {
//	_, cancel := context.WithTimeout(context.Background(), 5*time.Second)
//	defer cancel()
//
//	ctl := gomock.NewController(t)
//	defer ctl.Finish()
//
//	bcMock := mocks.NewMockIBusinessConfManager(ctl)
//	entryMock := entry.NewMockIClient(ctl)
//	starTrekMock := starTrek.NewMockIClient(ctl)
//
//	s := &Server{
//		bc:             bcMock,
//		chanceEntryCli: entryMock,
//		starTrekCli:    starTrekMock,
//	}
//
//	type args struct {
//		c   context.Context
//		req *pb.StarTrekEntryAndNotifyReq
//	}
//	userID := uint32(10)
//
//	now := time.Now()
//	tests := []struct {
//		name     string
//		initFunc func()
//		args     args
//		want     *pb.StarTrekEntryAndNotifyResp
//		wantErr  bool
//	}{
//		{
//			name: "正常：不在活动期内，不可见入口",
//			initFunc: func() {
//                bcMock.EXPECT().GetEntryCheckQuickReturn().Return(false)
//				starTrekMock.EXPECT().GetCurAtcTimeAndRoundTime(gomock.Any(), gomock.Any(), gomock.Any()).Return(&star_trek.GetCurAtcTimeAndRoundTimeResp{
//					ActId:      0,
//					DayBeing:   0,
//					DayEnd:     0,
//					RoundId:    0,
//					RoundBegin: 0,
//					RoundEnd:   0,
//				}, nil)
//			},
//			args: args{
//				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
//					UserID: userID,
//				}),
//				req: &pb.StarTrekEntryAndNotifyReq{
//					ChannelId: 123,
//				},
//			},
//			want:    &pb.StarTrekEntryAndNotifyResp{},
//			wantErr: false,
//		},
//		{
//			name: "正常：在活动期间,可见入口",
//			initFunc: func() {
//                bcMock.EXPECT().GetEntryCheckQuickReturn().Return(false)
//				starTrekMock.EXPECT().GetCurAtcTimeAndRoundTime(gomock.Any(), gomock.Any(), gomock.Any()).Return(&star_trek.GetCurAtcTimeAndRoundTimeResp{
//					ActId:      1,
//					DayBeing:   uint32(time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).Unix()),
//					DayEnd:     uint32(time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, time.Local).Unix()),
//					RoundId:    1,
//					RoundBegin: uint32(now.Add(-10 * time.Second).Unix()),
//					RoundEnd:   uint32(now.Add(1 * time.Hour).Unix()),
//				}, nil)
//				//entryMock.EXPECT().CheckIfAccessible(gomock.Any(), gomock.Any(), gomock.Any()).Return(&chance_game_entry.CheckIfAccessibleResp{
//				//	CanSee:        false,
//				//	StarTrekEntry: true,
//				//}, nil)
//				entryMock.EXPECT().CheckGameEntryAccess(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&chance_game_entry.CheckGameEntryAccessResp{
//						ConfList: []*chance_game_entry.GameEntryAccess{
//							&chance_game_entry.GameEntryAccess{
//								Access: false,
//								Switch: true,
//								ConditionList: []*chance_game_entry.AccessCondition{
//									&chance_game_entry.AccessCondition{
//										SubList: []*chance_game_entry.SubAccessCondition{
//											&chance_game_entry.SubAccessCondition{
//												ConditionType: 6,
//												Threshold:     1,
//											},
//										},
//										RelateType: 1,
//									},
//								},
//								RelateType: 2,
//							},
//						},
//					}, nil)
//				starTrekMock.EXPECT().GetStarTrekExemptValue(gomock.Any(), gomock.Any(), gomock.Any()).Return(&star_trek.GetStarTrekExemptValueResp{
//					LastInvestFlag: true,
//				}, nil)
//				bcMock.EXPECT().GetEntryNotifyInfo().Return(&conf.EntryNotify{
//					EntryNotifyOpen: true,
//					EntryNotifyText: "111111",
//					EntryNotifyTs:   1,
//				}).Times(3)
//				bcMock.EXPECT().GetPublicNotifyInfo().Return(&conf.PublicNotify{
//					PublicNotifyOpen: true,
//					PublicText:       "222222",
//					PublicTextColor:  "ffff",
//				}).Times(3)
//
//			},
//			args: args{
//				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
//					UserID: userID,
//				}),
//				req: &pb.StarTrekEntryAndNotifyReq{
//					ChannelId: 123,
//				},
//			},
//			want: &pb.StarTrekEntryAndNotifyResp{
//				BaseResp:         nil,
//				CannotSee:        false,
//				DayActivityBegin: uint32(time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).Unix()),
//				DayActivityEnd:   uint32(time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, time.Local).Unix()),
//				StatTrekNotify: &pb.StarTrekNotify{
//					HasFloating:      true,
//					FloatingText:     "111111",
//					FloatingDuration: 1,
//					HasPublic:        true,
//					PublicText:       "222222",
//					PublicColor:      "ffff",
//				},
//			},
//			wantErr: false,
//		},
//		{
//			name: "下游服务失败",
//			initFunc: func() {
//                bcMock.EXPECT().GetEntryCheckQuickReturn().Return(false)
//				starTrekMock.EXPECT().GetCurAtcTimeAndRoundTime(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&star_trek.GetCurAtcTimeAndRoundTimeResp{}, protocol.ToServerError(errors.New("some errs")))
//			},
//			args: args{
//				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
//					UserID: userID,
//				}),
//				req: &pb.StarTrekEntryAndNotifyReq{
//					ChannelId: 123,
//				},
//			},
//			want:    &pb.StarTrekEntryAndNotifyResp{},
//			wantErr: true,
//		},
//		{
//			name: "下游服务失败2",
//			initFunc: func() {
//                bcMock.EXPECT().GetEntryCheckQuickReturn().Return(false)
//				starTrekMock.EXPECT().GetCurAtcTimeAndRoundTime(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&star_trek.GetCurAtcTimeAndRoundTimeResp{
//						ActId:      1,
//						DayBeing:   uint32(time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).Unix()),
//						DayEnd:     uint32(time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, time.Local).Unix()),
//						RoundId:    1,
//						RoundBegin: uint32(now.Add(-10 * time.Second).Unix()),
//						RoundEnd:   uint32(now.Add(1 * time.Hour).Unix()),
//					}, nil)
//				//entryMock.EXPECT().CheckIfAccessible(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//				//	&chance_game_entry.CheckIfAccessibleResp{}, protocol.ToServerError(errors.New("some errs")))
//				entryMock.EXPECT().CheckGameEntryAccess(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&chance_game_entry.CheckGameEntryAccessResp{}, protocol.ToServerError(errors.New("some errs")))
//			},
//			args: args{
//				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
//					UserID: userID,
//				}),
//				req: &pb.StarTrekEntryAndNotifyReq{
//					ChannelId: 123,
//				},
//			},
//			want: &pb.StarTrekEntryAndNotifyResp{
//				CannotSee:        true,
//				DayActivityBegin: uint32(time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).Unix()),
//				DayActivityEnd:   uint32(time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, time.Local).Unix()),
//			},
//			wantErr: false,
//		},
//		{
//			name:     "缺少服务上下文数据",
//			initFunc: nil,
//			args: args{
//				c: context.Background(),
//				req: &pb.StarTrekEntryAndNotifyReq{
//					ChannelId: 123,
//				},
//			},
//			want:    &pb.StarTrekEntryAndNotifyResp{},
//			wantErr: true,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if tt.initFunc != nil {
//				tt.initFunc()
//			}
//			got, err := s.StarTrekEntryAndNotify(tt.args.c, tt.args.req)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("StarTrekEntryAndNotify() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			assert.Equalf(t, tt.want, got, "StarTrekEntryAndNotify(%v, %v)", tt.args.c, tt.args.req)
//		})
//	}
//}
//
//func TestServer_GetStatTrekInfo(t *testing.T) {
//	_, cancel := context.WithTimeout(context.Background(), 5*time.Second)
//	defer cancel()
//
//	ctl := gomock.NewController(t)
//	defer ctl.Finish()
//
//	bcMock := mocks.NewMockIBusinessConfManager(ctl)
//	entryMock := entry.NewMockIClient(ctl)
//	starTrekMock := starTrek.NewMockIClient(ctl)
//	accountMock := accountMock.NewMockIClient(ctl)
//
//	s := &Server{
//		bc:             bcMock,
//		chanceEntryCli: entryMock,
//		starTrekCli:    starTrekMock,
//		accountCli:     accountMock,
//	}
//
//	type args struct {
//		c   context.Context
//		req *pb.GetStatTrekInfoReq
//	}
//	userID := uint32(10)
//
//	now := time.Now()
//	tests := []struct {
//		name     string
//		initFunc func()
//		args     args
//		want     *pb.GetStatTrekInfoResp
//		wantErr  bool
//	}{
//		{
//			name:     "缺少服务上下文数据",
//			initFunc: nil,
//			args: args{
//				c:   context.Background(),
//				req: &pb.GetStatTrekInfoReq{},
//			},
//			want:    &pb.GetStatTrekInfoResp{},
//			wantErr: true,
//		},
//		{
//			name: "正常",
//			initFunc: func() {
//				starTrekMock.EXPECT().GetStatTrekInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&star_trek.GetStatTrekInfoResp{
//					RoundId:   2,
//					BeginTime: uint32(now.Add(-2 * time.Second).Unix()),
//					EndTime:   uint32(now.Add(1 * time.Hour).Unix()),
//					AwardPack: &star_trek.StarTAwardInfo{
//						PackId:     1,
//						PackName:   "111",
//						PackPic:    "111",
//						PackAmount: 1,
//						UnitPrice:  100,
//						GiftId:     101,
//					},
//					InvestProgress: 0,
//					UserCount:      0,
//					LastS: &star_trek.LastRoundReview{
//						RoundId:    2,
//						RoundTime:  uint32(now.Add(-2 * time.Second).Unix()),
//						TrekResult: false,
//						AwardPack: &star_trek.StarTAwardInfo{
//							PackId:     1,
//							PackName:   "111",
//							PackPic:    "111",
//							PackAmount: 1,
//							UnitPrice:  100,
//							GiftId:     101,
//						},
//						UserInfo: &star_trek.StarTUserInfo{
//							Uid: 1,
//						},
//						UserList:     []uint32{1},
//						BingoUserNum: 1,
//					},
//					NextS: &star_trek.NextRoundForecast{
//						//RoundId: 3,
//						AwardPack: &star_trek.StarTAwardInfo{
//							PackId:     1,
//							PackName:   "111",
//							PackPic:    "111",
//							PackAmount: 1,
//							UnitPrice:  100,
//							GiftId:     101,
//						},
//					},
//					Records: []*star_trek.RollingAwardInfo{
//						&star_trek.RollingAwardInfo{
//							Uid:          1,
//							Nickname:     "111",
//							PackName:     "111",
//							PackPic:      "111",
//							PackAmount:   1,
//							BingoUserNum: 1,
//						},
//					},
//					LatestPartitions: nil,
//					MySupply:         0,
//					RoundSupplyLimit: 100000,
//					MinInvest:        100,
//					UserDailyInvest:  ********,
//				}, nil)
//				accountMock.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(map[uint32]*accountPb.User{
//					1: &accountPb.User{
//						Username: "111",
//						Nickname: "111",
//					},
//				}, nil)
//			},
//			args: args{
//				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
//					UserID: userID,
//				}),
//				req: &pb.GetStatTrekInfoReq{},
//			},
//			want: &pb.GetStatTrekInfoResp{
//				BaseResp:  nil,
//				RoundId:   2,
//				BeginTime: uint32(now.Add(-2 * time.Second).Unix()),
//				EndTime:   uint32(now.Add(1 * time.Hour).Unix()),
//				AwardPack: &pb.StarTAwardInfo{
//					PackId:     101,
//					PackName:   "111",
//					PackPic:    "111",
//					PackAmount: 1,
//					UnitPrice:  100,
//				},
//				InvestProgress: 0,
//				UserCount:      0,
//				LastS: &pb.LastRoundReview{
//					RoundId:    2,
//					RoundTime:  uint32(now.Add(-2 * time.Second).Unix()),
//					TrekResult: false,
//					AwardPack: &pb.StarTAwardInfo{
//						PackId:     101,
//						PackName:   "111",
//						PackPic:    "111",
//						PackAmount: 1,
//						UnitPrice:  100,
//					},
//					UserInfo: &pb.StarTUserInfo{
//						Uid:      1,
//						Nickname: "111",
//						Account:  "111",
//					},
//					UserList: []*pb.StarTUserInfo{
//						&pb.StarTUserInfo{
//							Uid:      1,
//							Nickname: "111",
//							Account:  "111",
//						},
//					},
//					BingoUserNum: 1,
//				},
//				NextS: &pb.NextRoundForecast{
//					//RoundId: 3,
//					AwardPack: &pb.StarTAwardInfo{
//						PackId:     101,
//						PackName:   "111",
//						PackPic:    "111",
//						PackAmount: 1,
//						UnitPrice:  100,
//					},
//				},
//				Records: []*pb.RollingAwardInfo{
//					&pb.RollingAwardInfo{
//						Uid:          1,
//						Nickname:     "111",
//						PackName:     "111",
//						PackPic:      "111",
//						PackAmount:   1,
//						BingoUserNum: 1,
//					},
//				},
//				LatestPartitions: nil,
//				UserSupply:       0,
//				RoundSupplyLimit: 100000,
//				MinInvest:        100,
//				UserDailyInvest:  ********,
//			},
//			wantErr: false,
//		},
//		{
//			name: "下游服务失败",
//			initFunc: func() {
//				starTrekMock.EXPECT().GetStatTrekInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&star_trek.GetStatTrekInfoResp{}, protocol.ToServerError(errors.New("some errs")))
//			},
//			args: args{
//				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
//					UserID: userID,
//				}),
//				req: &pb.GetStatTrekInfoReq{},
//			},
//			want:    &pb.GetStatTrekInfoResp{},
//			wantErr: true,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if tt.initFunc != nil {
//				tt.initFunc()
//			}
//			got, err := s.GetStatTrekInfo(tt.args.c, tt.args.req)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("StarTrekEntryAndNotify() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			assert.Equalf(t, tt.want, got, "StarTrekEntryAndNotify(%v, %v).", tt.args.c, tt.args.req)
//		})
//	}
//}
//
//func TestServer_DoInvest(t *testing.T) {
//	_, cancel := context.WithTimeout(context.Background(), 5*time.Second)
//	defer cancel()
//
//	ctl := gomock.NewController(t)
//	defer ctl.Finish()
//
//	bcMock := mocks.NewMockIBusinessConfManager(ctl)
//	entryMock := entry.NewMockIClient(ctl)
//	starTrekMock := starTrek.NewMockIClient(ctl)
//	usualDeviceMock := usualDevice.NewMockIClient(ctl)
//
//	s := &Server{
//		bc:                bcMock,
//		chanceEntryCli:    entryMock,
//		starTrekCli:       starTrekMock,
//		usualDeviceClient: usualDeviceMock,
//	}
//
//	type args struct {
//		c   context.Context
//		req *pb.DoInvestReq
//	}
//	userID := uint32(10)
//
//	tests := []struct {
//		name     string
//		initFunc func()
//		args     args
//		want     *pb.DoInvestResp
//		wantErr  bool
//	}{
//		{
//			name:     "缺少服务上下文数据",
//			initFunc: nil,
//			args: args{
//				c: context.Background(),
//				req: &pb.DoInvestReq{SupplyList: []*pb.CostSupplyInfo{
//					{
//						GiftId:   1,
//						GiftType: 1,
//						GiftNum:  1,
//					},
//				}},
//			},
//			want:    &pb.DoInvestResp{},
//			wantErr: true,
//		},
//		{
//			name: "正常",
//			initFunc: func() {
//				//entryMock.EXPECT().CheckIfAccessible(gomock.Any(), gomock.Any(), gomock.Any()).Return(&chance_game_entry.CheckIfAccessibleResp{
//				//	CanSee:        false,
//				//	StarTrekEntry: true,
//				//}, nil)
//				entryMock.EXPECT().CheckGameEntryAccess(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&chance_game_entry.CheckGameEntryAccessResp{
//						ConfList: []*chance_game_entry.GameEntryAccess{
//							&chance_game_entry.GameEntryAccess{
//								Access: false,
//								Switch: true,
//								ConditionList: []*chance_game_entry.AccessCondition{
//									&chance_game_entry.AccessCondition{
//										SubList: []*chance_game_entry.SubAccessCondition{
//											&chance_game_entry.SubAccessCondition{
//												ConditionType: 6,
//												Threshold:     1,
//											},
//										},
//										RelateType: 1,
//									},
//								},
//								RelateType: 2,
//							},
//						},
//					}, nil)
//				starTrekMock.EXPECT().GetStarTrekExemptValue(gomock.Any(), gomock.Any(), gomock.Any()).Return(&star_trek.GetStarTrekExemptValueResp{
//					LastInvestFlag: true,
//				}, nil)
//				usualDeviceMock.EXPECT().CheckUsualDevice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&usual_device_svr.CheckUsualDeviceResp{
//						Result: false,
//					}, nil)
//				usualDeviceMock.EXPECT().GetDeviceAuthError(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//				starTrekMock.EXPECT().DoInvest(gomock.Any(), gomock.Any(), gomock.Any()).Return(&star_trek.DoInvestResp{
//					RoundId:          1,
//					MySupply:         100,
//					RoundSupplyLimit: 10000,
//					ServerSupply:     1000,
//					DailyInvest:      1000,
//					GoalServerSupply: 100000,
//				}, nil)
//				bcMock.EXPECT().GetMinInvest().Return(uint32(100))
//			},
//			args: args{
//				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
//					UserID:        userID,
//					DeviceID:      []byte("sadghsag"),
//					ClientType:    protocol.ClientTypeANDROID,
//					ClientVersion: protocol.FormatClientVersion(6, 10, 0),
//				}),
//				req: &pb.DoInvestReq{SupplyList: []*pb.CostSupplyInfo{
//					{
//						GiftId:   1,
//						GiftType: 1,
//						GiftNum:  1,
//					},
//				}},
//			},
//			want: &pb.DoInvestResp{
//				BaseResp:         nil,
//				RoundId:          1,
//				MySupply:         100,
//				RoundSupplyLimit: 10000,
//				MinInvest:        100,
//				ServerSupply:     1000,
//				UserDailyInvest:  1000,
//			},
//			wantErr: false,
//		},
//		{
//			name: "下游服务失败",
//			initFunc: func() {
//				//entryMock.EXPECT().CheckIfAccessible(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//				//	&chance_game_entry.CheckIfAccessibleResp{}, protocol.ToServerError(errors.New("errs")))
//				entryMock.EXPECT().CheckGameEntryAccess(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&chance_game_entry.CheckGameEntryAccessResp{}, protocol.ToServerError(errors.New("errs")))
//			},
//			args: args{
//				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
//					UserID: userID,
//				}),
//				req: &pb.DoInvestReq{},
//			},
//			want:    &pb.DoInvestResp{},
//			wantErr: true,
//		},
//		{
//			name: "下游服务失败2",
//			initFunc: func() {
//				//entryMock.EXPECT().CheckIfAccessible(gomock.Any(), gomock.Any(), gomock.Any()).Return(&chance_game_entry.CheckIfAccessibleResp{
//				//	CanSee:        false,
//				//	StarTrekEntry: true,
//				//}, nil)
//				entryMock.EXPECT().CheckGameEntryAccess(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&chance_game_entry.CheckGameEntryAccessResp{
//						ConfList: []*chance_game_entry.GameEntryAccess{
//							{
//								Access: true,
//								Switch: true,
//							},
//						},
//					}, nil)
//				usualDeviceMock.EXPECT().CheckUsualDevice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&usual_device_svr.CheckUsualDeviceResp{}, protocol.ToServerError(errors.New("errs")))
//			},
//			args: args{
//				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
//					UserID: userID,
//				}),
//				req: &pb.DoInvestReq{},
//			},
//			want:    &pb.DoInvestResp{},
//			wantErr: true,
//		},
//		{
//			name: "下游服务失败3",
//			initFunc: func() {
//				//entryMock.EXPECT().CheckIfAccessible(gomock.Any(), gomock.Any(), gomock.Any()).Return(&chance_game_entry.CheckIfAccessibleResp{
//				//	CanSee:        false,
//				//	StarTrekEntry: true,
//				//}, nil)
//				entryMock.EXPECT().CheckGameEntryAccess(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&chance_game_entry.CheckGameEntryAccessResp{
//						ConfList: []*chance_game_entry.GameEntryAccess{
//							&chance_game_entry.GameEntryAccess{
//								Access: true,
//								Switch: true,
//							},
//						},
//					}, nil)
//				usualDeviceMock.EXPECT().CheckUsualDevice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&usual_device_svr.CheckUsualDeviceResp{
//						Result: false,
//					}, nil)
//				usualDeviceMock.EXPECT().GetDeviceAuthError(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//				starTrekMock.EXPECT().DoInvest(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&star_trek.DoInvestResp{}, protocol.ToServerError(errors.New("errs")))
//			},
//			args: args{
//				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
//					UserID:        userID,
//					DeviceID:      []byte("sadghksag"),
//					ClientType:    protocol.ClientTypeIOS,
//					ClientVersion: protocol.FormatClientVersion(6, 10, 0),
//				}),
//				req: &pb.DoInvestReq{},
//			},
//			want:    &pb.DoInvestResp{},
//			wantErr: true,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if tt.initFunc != nil {
//				tt.initFunc()
//			}
//			got, err := s.DoInvest(tt.args.c, tt.args.req)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("DoInvest() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			assert.Equalf(t, tt.want, got, "DoInvest(%v, %v).", tt.args.c, tt.args.req)
//		})
//	}
//}
//
//func TestServer_GetAllTrekHistory(t *testing.T) {
//	_, cancel := context.WithTimeout(context.Background(), 5*time.Second)
//	defer cancel()
//
//	ctl := gomock.NewController(t)
//	defer ctl.Finish()
//
//	bcMock := mocks.NewMockIBusinessConfManager(ctl)
//	accountMock := accountMock.NewMockIClient(ctl)
//	starTrekMock := starTrek.NewMockIClient(ctl)
//	usualDeviceMock := usualDevice.NewMockIClient(ctl)
//
//	s := &Server{
//		bc:                bcMock,
//		accountCli:        accountMock,
//		starTrekCli:       starTrekMock,
//		usualDeviceClient: usualDeviceMock,
//	}
//
//	type args struct {
//		c   context.Context
//		req *pb.GetAllTrekHistoryReq
//	}
//	userID := uint32(1)
//
//	tests := []struct {
//		name     string
//		initFunc func()
//		args     args
//		want     *pb.GetAllTrekHistoryResp
//		wantErr  bool
//	}{
//		{
//			name:     "缺少服务上下文数据",
//			initFunc: nil,
//			args: args{
//				c:   context.Background(),
//				req: &pb.GetAllTrekHistoryReq{},
//			},
//			want:    &pb.GetAllTrekHistoryResp{},
//			wantErr: true,
//		},
//		{
//			name: "正常",
//			initFunc: func() {
//				bcMock.EXPECT().GetTrekRecordLimit().Return(uint32(100))
//				starTrekMock.EXPECT().GetAllTrekHistory(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&star_trek.GetAllTrekHistoryResp{
//						ReviewList: []*star_trek.LastRoundReview{
//							&star_trek.LastRoundReview{
//								RoundId:    1,
//								RoundTime:  1111111,
//								TrekResult: true,
//								AwardPack: &star_trek.StarTAwardInfo{
//									PackId:     1,
//									PackName:   "111",
//									PackPic:    "111",
//									PackAmount: 1,
//									UnitPrice:  100,
//									GiftId:     101,
//								},
//								UserInfo: &star_trek.StarTUserInfo{
//									Uid: 1,
//								},
//								UserList:     []uint32{1, 2, 3},
//								BingoUserNum: 3,
//							},
//						},
//					}, nil)
//				accountMock.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(map[uint32]*accountPb.User{
//					1: {
//						Username: "111",
//						Nickname: "111",
//					},
//					2: {
//						Username: "222",
//						Nickname: "222",
//					},
//					3: {
//						Username: "333",
//						Nickname: "333",
//					},
//				}, nil)
//			},
//			args: args{
//				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
//					UserID:        userID,
//					DeviceID:      []byte("sadghksag"),
//					ClientType:    protocol.ClientTypeANDROID,
//					ClientVersion: protocol.FormatClientVersion(6, 10, 0),
//				}),
//				req: &pb.GetAllTrekHistoryReq{
//					BaseReq: nil,
//					Offset:  0,
//					Limit:   10,
//				},
//			},
//			want: &pb.GetAllTrekHistoryResp{
//				BaseResp: nil,
//				ReviewList: []*pb.LastRoundReview{
//					{
//						RoundId:    1,
//						RoundTime:  1111111,
//						TrekResult: true,
//						AwardPack: &pb.StarTAwardInfo{
//							PackId:     101,
//							PackName:   "111",
//							PackPic:    "111",
//							PackAmount: 1,
//							UnitPrice:  100,
//						},
//						UserInfo: &pb.StarTUserInfo{
//							Uid:      1,
//							Nickname: "111",
//							Account:  "111",
//						},
//						UserList:     nil,
//						BingoUserNum: 3,
//					},
//				},
//			},
//			wantErr: false,
//		},
//		{
//			name: "下游服务失败",
//			initFunc: func() {
//				bcMock.EXPECT().GetTrekRecordLimit().Return(uint32(100))
//				starTrekMock.EXPECT().GetAllTrekHistory(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&star_trek.GetAllTrekHistoryResp{}, protocol.ToServerError(errors.New("errs")))
//			},
//			args: args{
//				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
//					UserID: userID,
//				}),
//				req: &pb.GetAllTrekHistoryReq{},
//			},
//			want:    &pb.GetAllTrekHistoryResp{},
//			wantErr: true,
//		},
//		{
//			name: "下游服务失败2",
//			initFunc: func() {
//				bcMock.EXPECT().GetTrekRecordLimit().Return(uint32(100))
//				starTrekMock.EXPECT().GetAllTrekHistory(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//					&star_trek.GetAllTrekHistoryResp{
//						ReviewList: []*star_trek.LastRoundReview{
//							{
//								RoundId:    1,
//								RoundTime:  1111111,
//								TrekResult: true,
//								AwardPack: &star_trek.StarTAwardInfo{
//									PackId:     1,
//									PackName:   "111",
//									PackPic:    "111",
//									PackAmount: 1,
//									UnitPrice:  100,
//									GiftId:     101,
//								},
//								UserInfo: &star_trek.StarTUserInfo{
//									Uid: 1,
//								},
//								UserList:     []uint32{1, 2, 3},
//								BingoUserNum: 3,
//							},
//						},
//					}, nil)
//				accountMock.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(map[uint32]*accountPb.User{}, protocol.ToServerError(errors.New("errs")))
//			},
//			args: args{
//				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
//					UserID: userID,
//				}),
//				req: &pb.GetAllTrekHistoryReq{},
//			},
//			want:    &pb.GetAllTrekHistoryResp{},
//			wantErr: true,
//		},
//	}
//
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if tt.initFunc != nil {
//				tt.initFunc()
//			}
//			got, err := s.GetAllTrekHistory(tt.args.c, tt.args.req)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("GetAllTrekHistory() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			assert.Equalf(t, tt.want, got, "GetAllTrekHistory(%v, %v).", tt.args.c, tt.args.req)
//		})
//	}
//}

func TestServer_DoInvest(t *testing.T) {
    _, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    ctl := gomock.NewController(t)
    defer ctl.Finish()

    bcMock := mocks.NewMockIBusinessConfManager(ctl)
    accountMock := accountMock.NewMockIClient(ctl)
    usualDeviceMock := usualDevice.NewMockIClient(ctl)

    s := &Server{
        bc:                bcMock,
        accountCli:        accountMock,
        usualDeviceClient: usualDeviceMock,
    }

    type fields struct {
        bc                conf.IBusinessConfManager
        chanceEntryCli    chance_game_entry.IClient
        accountCli        account.IClient
        starTrekCli       star_trek.IClient
        usualDeviceClient usual_device.IClient
    }
    type args struct {
        ctx context.Context
        req *pb.DoInvestReq
    }
    tests := []struct {
        name     string
        fields   fields
        args     args
        wantResp *pb.DoInvestResp
        wantErr  bool
    }{
        {
            name:     "common",
            fields:   fields{},
            args:     args{},
            wantResp: &pb.DoInvestResp{},
            wantErr:  false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            gotResp, err := s.DoInvest(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("DoInvest() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotResp, tt.wantResp) {
                t.Errorf("DoInvest() gotResp = %v, want %v", gotResp, tt.wantResp)
            }
        })
    }
}
func TestServer_GetAllTrekHistory(t *testing.T) {
    _, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    ctl := gomock.NewController(t)
    defer ctl.Finish()

    bcMock := mocks.NewMockIBusinessConfManager(ctl)
    accountMock := accountMock.NewMockIClient(ctl)
    usualDeviceMock := usualDevice.NewMockIClient(ctl)

    s := &Server{
        bc:                bcMock,
        accountCli:        accountMock,
        usualDeviceClient: usualDeviceMock,
    }
    type fields struct {
        bc                conf.IBusinessConfManager
        chanceEntryCli    chance_game_entry.IClient
        accountCli        account.IClient
        starTrekCli       star_trek.IClient
        usualDeviceClient usual_device.IClient
    }
    type args struct {
        ctx context.Context
        req *pb.GetAllTrekHistoryReq
    }
    tests := []struct {
        name     string
        fields   fields
        args     args
        wantResp *pb.GetAllTrekHistoryResp
        wantErr  bool
    }{
        {
            name:     "common",
            fields:   fields{},
            args:     args{},
            wantResp: &pb.GetAllTrekHistoryResp{},
            wantErr:  false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            gotResp, err := s.GetAllTrekHistory(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetAllTrekHistory() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotResp, tt.wantResp) {
                t.Errorf("GetAllTrekHistory() gotResp = %v, want %v", gotResp, tt.wantResp)
            }
        })
    }
}

func TestServer_GetMyTrekRecord(t *testing.T) {
    _, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    ctl := gomock.NewController(t)
    defer ctl.Finish()

    bcMock := mocks.NewMockIBusinessConfManager(ctl)
    accountMock := accountMock.NewMockIClient(ctl)
    usualDeviceMock := usualDevice.NewMockIClient(ctl)

    s := &Server{
        bc:                bcMock,
        accountCli:        accountMock,
        usualDeviceClient: usualDeviceMock,
    }
    type fields struct {
        bc                conf.IBusinessConfManager
        chanceEntryCli    chance_game_entry.IClient
        accountCli        account.IClient
        starTrekCli       star_trek.IClient
        usualDeviceClient usual_device.IClient
    }
    type args struct {
        ctx context.Context
        in  *pb.GetMyTrekRecordReq
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        wantOut *pb.GetMyTrekRecordResp
        wantErr bool
    }{
        {
            name:    "common",
            fields:  fields{},
            args:    args{},
            wantOut: &pb.GetMyTrekRecordResp{},
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            gotOut, err := s.GetMyTrekRecord(tt.args.ctx, tt.args.in)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetMyTrekRecord() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotOut, tt.wantOut) {
                t.Errorf("GetMyTrekRecord() gotOut = %v, want %v", gotOut, tt.wantOut)
            }
        })
    }
}

func TestServer_GetStatTrekInfo(t *testing.T) {
    _, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    ctl := gomock.NewController(t)
    defer ctl.Finish()

    bcMock := mocks.NewMockIBusinessConfManager(ctl)
    accountMock := accountMock.NewMockIClient(ctl)
    usualDeviceMock := usualDevice.NewMockIClient(ctl)

    s := &Server{
        bc:                bcMock,
        accountCli:        accountMock,
        usualDeviceClient: usualDeviceMock,
    }
    type fields struct {
        bc                conf.IBusinessConfManager
        chanceEntryCli    chance_game_entry.IClient
        accountCli        account.IClient
        starTrekCli       star_trek.IClient
        usualDeviceClient usual_device.IClient
    }
    type args struct {
        ctx context.Context
        req *pb.GetStatTrekInfoReq
    }
    tests := []struct {
        name     string
        fields   fields
        args     args
        wantResp *pb.GetStatTrekInfoResp
        wantErr  bool
    }{
        {
            name:     "common",
            fields:   fields{},
            args:     args{},
            wantResp: &pb.GetStatTrekInfoResp{},
            wantErr:  false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            gotResp, err := s.GetStatTrekInfo(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetStatTrekInfo() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotResp, tt.wantResp) {
                t.Errorf("GetStatTrekInfo() gotResp = %v, want %v", gotResp, tt.wantResp)
            }
        })
    }
}

func TestServer_GetSupplyConf(t *testing.T) {
    _, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    ctl := gomock.NewController(t)
    defer ctl.Finish()

    bcMock := mocks.NewMockIBusinessConfManager(ctl)
    accountMock := accountMock.NewMockIClient(ctl)
    usualDeviceMock := usualDevice.NewMockIClient(ctl)

    s := &Server{
        bc:                bcMock,
        accountCli:        accountMock,
        usualDeviceClient: usualDeviceMock,
    }
    type fields struct {
        bc                conf.IBusinessConfManager
        chanceEntryCli    chance_game_entry.IClient
        accountCli        account.IClient
        starTrekCli       star_trek.IClient
        usualDeviceClient usual_device.IClient
    }
    type args struct {
        ctx context.Context
        req *pb.GetSupplyConfReq
    }
    tests := []struct {
        name     string
        fields   fields
        args     args
        wantResp *pb.GetSupplyConfResp
        wantErr  bool
    }{
        {
            name:     "common",
            fields:   fields{},
            args:     args{},
            wantResp: &pb.GetSupplyConfResp{},
            wantErr:  false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            gotResp, err := s.GetSupplyConf(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetSupplyConf() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotResp, tt.wantResp) {
                t.Errorf("GetSupplyConf() gotResp = %v, want %v", gotResp, tt.wantResp)
            }
        })
    }
}

func TestServer_GetSupplyValueChange(t *testing.T) {
    _, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    ctl := gomock.NewController(t)
    defer ctl.Finish()

    bcMock := mocks.NewMockIBusinessConfManager(ctl)
    accountMock := accountMock.NewMockIClient(ctl)
    usualDeviceMock := usualDevice.NewMockIClient(ctl)

    s := &Server{
        bc:                bcMock,
        accountCli:        accountMock,
        usualDeviceClient: usualDeviceMock,
    }
    type fields struct {
        bc                conf.IBusinessConfManager
        chanceEntryCli    chance_game_entry.IClient
        accountCli        account.IClient
        starTrekCli       star_trek.IClient
        usualDeviceClient usual_device.IClient
    }
    type args struct {
        ctx context.Context
        req *pb.GetSupplyValueChangeReq
    }
    tests := []struct {
        name     string
        fields   fields
        args     args
        wantResp *pb.GetSupplyValueChangeResp
        wantErr  bool
    }{
        {
            name:     "common",
            fields:   fields{},
            args:     args{},
            wantResp: &pb.GetSupplyValueChangeResp{},
            wantErr:  false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            gotResp, err := s.GetSupplyValueChange(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetSupplyValueChange() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotResp, tt.wantResp) {
                t.Errorf("GetSupplyValueChange() gotResp = %v, want %v", gotResp, tt.wantResp)
            }
        })
    }
}

func TestServer_StarTrekEntryAndNotify(t *testing.T) {
    _, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    ctl := gomock.NewController(t)
    defer ctl.Finish()

    bcMock := mocks.NewMockIBusinessConfManager(ctl)
    accountMock := accountMock.NewMockIClient(ctl)
    usualDeviceMock := usualDevice.NewMockIClient(ctl)

    s := &Server{
        bc:                bcMock,
        accountCli:        accountMock,
        usualDeviceClient: usualDeviceMock,
    }
    type fields struct {
        bc                conf.IBusinessConfManager
        chanceEntryCli    chance_game_entry.IClient
        accountCli        account.IClient
        starTrekCli       star_trek.IClient
        usualDeviceClient usual_device.IClient
    }
    type args struct {
        ctx context.Context
        req *pb.StarTrekEntryAndNotifyReq
    }
    tests := []struct {
        name     string
        fields   fields
        args     args
        wantResp *pb.StarTrekEntryAndNotifyResp
        wantErr  bool
    }{
        {
            name:   "common",
            fields: fields{},
            args:   args{},
            wantResp: &pb.StarTrekEntryAndNotifyResp{
                CannotSee: true,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            gotResp, err := s.StarTrekEntryAndNotify(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("StarTrekEntryAndNotify() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotResp, tt.wantResp) {
                t.Errorf("StarTrekEntryAndNotify() gotResp = %v, want %v", gotResp, tt.wantResp)
            }
        })
    }
}