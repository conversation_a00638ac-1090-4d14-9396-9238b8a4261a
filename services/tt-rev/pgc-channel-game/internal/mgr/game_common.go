package mgr

import (
    "context"
    "fmt"
    "strings"
    "time"

    pgc_adventure "golang.52tt.com/protocol/services/pgc-adventure"

    channelGameMutualPb "golang.52tt.com/protocol/services/channel-game-mutual"
    pgcdigitalbomb "golang.52tt.com/protocol/services/pgc-digital-bomb"
    "golang.52tt.com/services/tt-rev/pgc-channel-game/internal/conf"
    "golang.52tt.com/services/tt-rev/pgc-channel-game/internal/game_bomb"

    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    channelpb "golang.52tt.com/protocol/app/channel"
    pbLogic "golang.52tt.com/protocol/app/pgc_channel_game_logic"
    "golang.52tt.com/protocol/common/status"
    channelschemeconfmgr "golang.52tt.com/protocol/services/channel-scheme-conf-mgr"
    entertainmentRecommendBack "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
    pgc_channel_game "golang.52tt.com/protocol/services/pgc-channel-game"
    pgcchannelpkpb "golang.52tt.com/protocol/services/pgc-channel-pk"
    ttrevoperation "golang.52tt.com/protocol/services/tt-rev-operation"
)

const (
    MaskPk       = 5
    PGCChannelPK = 6
)

// GetGameList 获取工具栏玩法列表
func (mgr *Mgr) GetGameList(ctx context.Context, channelId uint32) ([]*pgc_channel_game.GameSummary, error) {
    gameSummary := make([]*pgc_channel_game.GameSummary, 0)

    // 校验当前房间类型
    err := mgr.checkChannelScheme(ctx, channelId)
    if err != nil {
        // 房间模式不支持小游戏的直接返回空
        log.ErrorWithCtx(ctx, "GetGameList.checkChannelScheme, channelId: %d, err: %v", channelId, err)
        return gameSummary, nil
    }

    // 获取有权限的gameId
    gameSummaryIds, err := mgr.getGameSummaryIds(ctx, channelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetGameList.getGameSummaryIds, channelId: %d, err: %v", channelId, err)
        return gameSummary, err
    }

    gameSummaryConf, err := mgr.bc.GetGameSummary(gameSummaryIds)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetGameList.bc.GetGameSummary, channelId: %d, err: %v", channelId, err)
        return gameSummary, err
    }

    serviceInfo, err := getServiceInfo(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetGameList getServiceInfo fail")
        return gameSummary, err
    }

    for _, item := range gameSummaryConf {
        if serviceInfo.ClientVersion < conf.TextVersion2Uint32("6.20.0") && // 6.16版本看不到新版, 后续移除
            (serviceInfo.ClientType == protocol.ClientTypeIOS || serviceInfo.ClientType == protocol.ClientTypeANDROID) &&
            serviceInfo.ClientVersion < conf.TextVersion2Uint32(item.MinimumMobileVersion) {
            continue
        }

        if serviceInfo.ClientVersion < conf.TextVersion2Uint32("1.8.8") && // 甩雷那版看不到数字炸弹
            serviceInfo.ClientType == protocol.ClientTypePcTT &&
            serviceInfo.ClientVersion < conf.TextVersion2Uint32(item.MinimumPCVersion) {
            continue
        }
        gameSummary = append(gameSummary, &pgc_channel_game.GameSummary{
            GameId:   item.GameId,
            GameName: item.GameName,
            GameIcon: item.GameIcon,
            CmsUrl:   item.CmsUrl,
            Info:     item.Info,
        })
    }

    log.InfoWithCtx(ctx, "GetGameList, channelId: %d, gameSummary: %+v", channelId, gameSummary)
    return gameSummary, err
}

// 获取房间当前拥有权限的玩法id
func (mgr *Mgr) getGameSummaryIds(ctx context.Context, channelId uint32) ([]uint32, error) {
    gameIds, err := mgr.cache.GetChannelGameIds(ctx, channelId)
    if err == nil {
        return gameIds, nil
    }

    // 获取房间当前tag
    tag, err := mgr.rpcClient.EntertainmentRecommendBackCli.GetChannelTag(ctx, 0, &entertainmentRecommendBack.GetChannelTagReq{
        ChannelId: &channelId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "getGameSummaryIds.GetChannelTag, channelId: %d, err: %v", channelId, err)
        return []uint32{}, err
    }

    // tt-rev-operation的gameplayId 对应 这边的gameSummaryId
    gameSummaryIds, err := mgr.rpcClient.TTRevOperationCli.GetChannelAllGameplay(ctx, &ttrevoperation.GetChannelAllGameplayReq{
        ChannelId: channelId,
        TagId:     tag.GetTagInfo().GetTagId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "getGameSummaryIds.GetChannelAllGameplay, ")
    }

    err = mgr.cache.SetChannelGameIds(ctx, channelId, gameSummaryIds)
    if err != nil {
        log.ErrorWithCtx(ctx, "getGameSummaryIds, channelId: %d, err: set gameId cache fail")
    }

    return gameSummaryIds, err
}

func (mgr *Mgr) SetGamePhase(ctx context.Context, channelId, gameId, targetPhase uint32, choseOperate []*pgc_channel_game.ChoseOperate) error {
    if targetPhase != uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_FIN) {
        if err := mgr.checkCanSet(ctx, channelId, gameId); err != nil { // 玩法互斥判断, 关闭不用做
            return err
        }
    }

    // 判断版本
    serviceInfo, err := getServiceInfo(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetGamePhase getServiceInfo fail")
        return err
    }
    if serviceInfo.ClientType == protocol.ClientTypePcTT && serviceInfo.ClientVersion < mgr.bc.GetGamePCMinimumVersion(gameId) {
        return protocol.NewExactServerError(nil, status.ErrPgcChannelGameUnderversion)
    }

    if (serviceInfo.ClientType == protocol.ClientTypeIOS || serviceInfo.ClientType == protocol.ClientTypeANDROID) &&
        serviceInfo.ClientVersion < mgr.bc.GetGameMobileMinimumVersion(gameId) {
        return protocol.NewExactServerError(nil, status.ErrPgcChannelGameUnderversion)
    }

    if mgr.bc.GetFeature("game_mutual_register") {
        // 首先先把房间玩法注册到玩法互斥中心,方便后续的接入
        _, err = mgr.rpcClient.ChannelGameMutualCli.Register(ctx, &channelGameMutualPb.RegisterRequest{
            ChannelId: channelId,
            GameId:    uint64(gameId),
        })
        if err != nil {
            log.WarnWithCtx(ctx, "Register, channelId; %d,  err: %v", channelId, err)
            // no return err
        }
    }

    switch pgc_channel_game.PgcChannelGameId(gameId) {
    case pgc_channel_game.PgcChannelGameId_GAME_THROW_BOME:
        err = mgr.SetGameBombPhase(ctx, channelId, gameId, targetPhase, choseOperate)
    case pgc_channel_game.PgcChannelGameId_GAME_DIGITAL_BOMB:
        err = mgr.SetDigitalBombPhase(ctx, channelId, targetPhase, choseOperate)
    case pgc_channel_game.PgcChannelGameId_GAME_ADVENTURE:
        err = mgr.SetAdventurePhase(ctx, serviceInfo.UserID, channelId, targetPhase, choseOperate)

    default:
        return fmt.Errorf("invalid gameId")
    }
    if err != nil {
        log.ErrorWithCtx(ctx, "SetGamePhase, channelId; %d,  err: %v", channelId, err)
        return err
    }

    if targetPhase == uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_FIN) {
        // 小游戏结束推送一下蒙面PK入口
        err = mgr.rpcClient.MaskedPKCli.PushGameBeginConf(ctx, channelId)
        if err != nil {
            log.ErrorWithCtx(ctx, "EndGameBomb.PushGameBeginConf, channelId; %d,  err: %v", channelId, err)
        }
        log.InfoWithCtx(ctx, "handleGameBombEnd.PushGameBeginConf")
    }

    return nil
}

func (mgr *Mgr) SetGamePhaseV3(ctx context.Context, channelId, gameId, targetPhase uint32, choseOperate []*pgc_channel_game.ChoseOperate) error {
    // 判断版本
    serviceInfo, err := getServiceInfo(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetGamePhase getServiceInfo fail")
        return err
    }
    if serviceInfo.ClientType == protocol.ClientTypePcTT && serviceInfo.ClientVersion < mgr.bc.GetGamePCMinimumVersion(gameId) {
        return protocol.NewExactServerError(nil, status.ErrPgcChannelGameUnderversion)
    }

    if (serviceInfo.ClientType == protocol.ClientTypeIOS || serviceInfo.ClientType == protocol.ClientTypeANDROID) &&
        serviceInfo.ClientVersion < mgr.bc.GetGameMobileMinimumVersion(gameId) {
        return protocol.NewExactServerError(nil, status.ErrPgcChannelGameUnderversion)
    }

    // 玩法互斥检查，并尝试注册到玩法互斥中心，切换至关闭阶段不需要判断
    if targetPhase != uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_FIN) {
        if err := mgr.checkCanSetV3(ctx, channelId, gameId); err != nil {
            return err
        }
    }

    switch pgc_channel_game.PgcChannelGameId(gameId) {
    case pgc_channel_game.PgcChannelGameId_GAME_THROW_BOME:
        err = mgr.SetGameBombPhase(ctx, channelId, gameId, targetPhase, choseOperate)
    case pgc_channel_game.PgcChannelGameId_GAME_DIGITAL_BOMB:
        err = mgr.SetDigitalBombPhase(ctx, channelId, targetPhase, choseOperate)
    case pgc_channel_game.PgcChannelGameId_GAME_ADVENTURE:
        err = mgr.SetAdventurePhase(ctx, serviceInfo.UserID, channelId, targetPhase, choseOperate)

    default:
        return fmt.Errorf("invalid gameId")
    }
    if err != nil {
        log.ErrorWithCtx(ctx, "SetGamePhase, channelId; %d,  err: %v", channelId, err)
        return err
    }

    if targetPhase == uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_FIN) {
        // 小游戏结束推送一下蒙面PK入口
        err = mgr.rpcClient.MaskedPKCli.PushGameBeginConf(ctx, channelId)
        if err != nil {
            log.ErrorWithCtx(ctx, "EndGameBomb.PushGameBeginConf, channelId; %d,  err: %v", channelId, err)
        }
        log.InfoWithCtx(ctx, "handleGameBombEnd.PushGameBeginConf")
    }

    return nil
}

// checkCanSetV3 玩法互斥判断V3, 基于玩法互斥中心实现
func (mgr *Mgr) checkCanSetV3(ctx context.Context, channelId, gameId uint32) error {
    err := mgr.otherBizCompatibleV2(ctx, channelId, gameId)
    if err != nil {
        return err
    }

    // 检查房间是否具有该玩法权限
    gameIds, err := mgr.getGameSummaryIds(ctx, channelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkCanSetV2.checkGamePermission, channelId: %d, gameId: %d, err: %v", channelId, gameIds, err)
    }
    hasPermission := false
    for _, gid := range gameIds {
        if gid == gameId {
            hasPermission = true
            break
        }
    }
    if !hasPermission {
        log.ErrorWithCtx(ctx, "checkCanSetV2, channelId: %d, gameId: %d, err: no game permission")
        return newPgcChannelGameNoStartGamePermissionError(gameId)
    }

    // 尝试注册到玩法互斥中心
    _, err = mgr.rpcClient.ChannelGameMutualCli.Register(ctx, &channelGameMutualPb.RegisterRequest{
        ChannelId: channelId,
        GameId:    uint64(gameId),
    })
    if err != nil {
        // 注册失败
        log.ErrorWithCtx(ctx, "checkCanSetV2.Register, channelId: %d,  err: %v", channelId, err)
        return err
    }

    // 注册成功
    return nil
}

func (mgr *Mgr) checkCanSet(ctx context.Context, channelId, gameId uint32) error {
    if err := mgr.otherBizCompatible(ctx, channelId, gameId); err != nil {
        log.ErrorWithCtx(ctx, "checkCanSet.otherBizCompatible, channelId: %d,  err; %v", channelId, err)
        return err
    }

    gameInfo, err := mgr.GetChannelGameInfo(ctx, channelId) // 后续做缓存 定时更新
    if err != nil {
        log.ErrorWithCtx(ctx, "checkCanSet.GetChannelGameInfo, channelId: %d,  err; %v", channelId, err)
        return err
    }

    // 有游戏在进行 且不是当前游戏
    if gameInfo.GetPhase() != 0 && gameId != gameInfo.CurrentGameId {
        return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请结束当前玩法后再开启")
    }

    // 判断房间是否有该游戏权限, 如果游戏已开始,则本局不用校验
    if gameInfo.GetPhase() == 0 {
        gameIds, err := mgr.getGameSummaryIds(ctx, channelId)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetGamePhase.checkGamePermission, channelId: %d, gameId: %d, err: %v", channelId, gameIds, err)
        }
        hasPermission := false
        for _, gid := range gameIds {
            if gid == gameId {
                hasPermission = true
                break
            }
        }
        if !hasPermission {
            log.ErrorWithCtx(ctx, "SetGamePhase, channelId: %d, gameId: %d, err: no game permission")
            return newPgcChannelGameNoStartGamePermissionError(gameId)
        }
    }

    return nil
}

// GetChannelGameInfo 查看各游戏信息
func (mgr *Mgr) GetChannelGameInfo(ctx context.Context, channelId uint32) (*pgc_channel_game.GetChannelGameInfoResp, error) {
    out, err := mgr.gameBomb.GetChannelGameInfo(ctx, channelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelGameInfo, channelId: %d, err: %v", channelId, err)
        out = &pgc_channel_game.GetChannelGameInfoResp{}
    }

    digitalBombInfo, err := mgr.rpcClient.PgcDigitalBombCli.GetDigitalBombInfo(ctx, &pgcdigitalbomb.GetDigitalBombInfoReq{
        ChannelId: channelId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelGameInfo.GetDigitalBombInfo, channelId: %d, err: %v", channelId, err)
        return out, nil
    }
    if digitalBombInfo.GetInfo().GetPhase() > uint32(pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_UNKNOWN) &&
        digitalBombInfo.GetInfo().GetPlayId() != uint32(pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_DIRECT_END) &&
        digitalBombInfo.GetInfo().GetPhase() < uint32(pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_EXPLODE) {
        out.CurrentGameId = uint32(pgc_channel_game.PgcChannelGameId_GAME_DIGITAL_BOMB)
        out.Phase = digitalBombPhaseMap(digitalBombInfo.GetInfo().GetPhase())
        out.DigitalBombInfo = digitalBombInfo.GetInfo()

        return out, nil
    }

    adventureInfo, err := mgr.rpcClient.PgcAdventureCli.GetAdventureInfo(ctx, &pgc_adventure.GetAdventureInfoReq{
        ChannelId: channelId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelGameInfo.GetAdventureInfo, channelId: %d, err: %v", channelId, err)
        return out, nil
    }

    if adventureInfo.GetInfo().GetPlayId() != 0 {
        out.CurrentGameId = uint32(pgc_channel_game.PgcChannelGameId_GAME_ADVENTURE)
        out.Phase = adventurePhaseMap(adventureInfo.GetInfo().GetPhase())
        out.AdventureInfo = adventureInfo.GetInfo()
    }

    return out, nil
}

func digitalBombPhaseMap(phase uint32) uint32 {
    switch pgcdigitalbomb.DigitalBombPhase(phase) {
    case pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_ENROLL,
        pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_SELECTING,
        pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_SELECTED:
        // 第一版甩雷时客户端使用了甩雷的phase的枚举去做游戏面板的状态判断, 这里要把其他有戏的状态映射到旧的枚举
        return uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_START)
    case pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_EXPLODE,
        pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_MANUAL_END,
        pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_SYS_END,
        pgcdigitalbomb.DigitalBombPhase_DIGITAL_BOMB_PHASE_DIRECT_END:
        return uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_FIN)
    default:
        return uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_NOT_START)
    }
}

func adventurePhaseMap(phase uint32) uint32 {
    switch pbLogic.AdventurePhase(phase) {
    case pbLogic.AdventurePhase_ADVENTURE_PHASE_ENROLL,
        pbLogic.AdventurePhase_ADVENTURE_PHASE_CONTROL_NEXT,
        pbLogic.AdventurePhase_ADVENTURE_PHASE_RANDOM_STEPS:
        // 第一版甩雷时客户端使用了甩雷的phase的枚举去做游戏面板的状态判断, 这里要把其他有戏的状态映射到旧的枚举
        return uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_START)
    case pbLogic.AdventurePhase_ADVENTURE_PHASE_DIRECT_END:
        return uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_FIN)
    default:
        return uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_NOT_START)
    }
}

func (mgr *Mgr) SetGameBombPhase(ctx context.Context, channelId, gameId, targetPhase uint32, choseOperate []*pgc_channel_game.ChoseOperate) error {
    if targetPhase == uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_START) {
        err := mgr.StartGameBomb(ctx, channelId, gameId, choseOperate)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetGamePhase.StartGameBomb, channelId: %d, err: %v", channelId, err)
            return err
        }
    } else if targetPhase == uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_FIN) {
        err := mgr.EndGameBomb(ctx, channelId, gameId)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetGamePhase.EndGameBomb, channelId: %d, err: %v", channelId, err)
            return err
        }
    } else {
        log.ErrorWithCtx(ctx, "SetGamePhase not found channelId: %d, targetPhase: %d", channelId, targetPhase)
    }
    return nil
}

func (mgr *Mgr) StartGameBomb(ctx context.Context, channelId, gameId uint32, choseOperate []*pgc_channel_game.ChoseOperate) error {
    serviceInfo, err := getServiceInfo(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "StartGameBomb getServiceInfo fail")
        return err
    }

    if len(choseOperate) < 1 {
        log.ErrorWithCtx(ctx, "StartGame, channelId: %d, gameId: %d, err: invalid choseOperate", channelId, gameId)
        return fmt.Errorf("invalid choseOperation")
    }
    gameBombType := choseOperate[0].GetButtonId()

    // 检查主持是否再麦上 / 人数是否足够
    if err := mgr.checkMic(ctx, channelId, serviceInfo.UserID, gameBombType); err != nil {
        log.ErrorWithCtx(ctx, "StartGameBomb.checkMic, channelId: %d, uid: %d, err: %v", channelId, serviceInfo.UserID, err)
        return err
    }

    err = mgr.gameBomb.InitThrowBombGame(ctx, channelId, serviceInfo.UserID, gameId, gameBombType)
    if err != nil {
        log.ErrorWithCtx(ctx, "StartGame.InitThrowBombGame, channelId: %d, gameId: %d, uid: %d, op: %+v, err: %v", channelId,
            gameId, serviceInfo.UserID, choseOperate, err)
        return err
    }
    return nil
}

func (mgr *Mgr) EndGameBomb(ctx context.Context, channelId, gameId uint32) error {
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        return fmt.Errorf("get serviceInfo fail")
    }

    // 判断有没有人在主持麦
    if err := mgr.checkMic(ctx, channelId, serviceInfo.UserID, 0); err != nil {
        log.ErrorWithCtx(ctx, "EndGameBomb.checkMic, channelId: %d, uid: %d, err: %v", channelId, gameId, err)
        return err
    }

    if gameId == uint32(pbLogic.PgcChannelGameId_GAME_THROW_BOMB) {
        bombUid, found, err := mgr.cache.GetBombUser(ctx, channelId)
        if err != nil {
            log.ErrorWithCtx(ctx, "EndGameBomb channelId:%d, err:%d", channelId, err)
            return err
        }

        if bombUid == 0 || !found {
            log.ErrorWithCtx(ctx, "EndGameBomb channelId:%d, err bombUid not found", channelId)
            return err
        }

        err = mgr.gameBomb.HandleGameBombEnd(ctx, channelId, bombUid, 0)
        if err != nil {
            log.ErrorWithCtx(ctx, "EndGameBomb.handleGameBombEnd, channelId: %d, err: %v", channelId, err)
            return err
        }
    }
    return nil
}

func (mgr *Mgr) SetDigitalBombPhase(ctx context.Context, channelId, phase uint32, choseOperate []*pgc_channel_game.ChoseOperate) error {
    pt := uint32(0)
    if len(choseOperate) == 1 {
        pt = choseOperate[0].ButtonId
    }

    _, err := mgr.rpcClient.PgcDigitalBombCli.SetDigitalBombPhase(ctx, &pgcdigitalbomb.SetDigitalBombPhaseReq{
        ChannelId:       channelId,
        Phase:           phase,
        ParticipateType: pt,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetDigitalBombPhase, channelId: %d, phase: %d, choseOperate: %+v, err; %d", channelId, phase, choseOperate, err)
        return err
    }
    return nil
}

func (mgr *Mgr) SetAdventurePhase(ctx context.Context, uid, channelId, phase uint32, choseOperate []*pgc_channel_game.ChoseOperate) error {
    pt := uint32(0)
    if len(choseOperate) == 1 {
        pt = choseOperate[0].ButtonId
    }

    _, err := mgr.rpcClient.PgcAdventureCli.SetAdventurePhase(ctx, &pgc_adventure.SetAdventurePhaseReq{
        Uid:             uid,
        ChannelId:       channelId,
        Phase:           phase,
        ParticipateType: pt,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetAdventurePhase, uid:%d, channelId: %d, phase: %d, choseOperate: %+v, err; %d", uid, channelId, phase, choseOperate, err)
        return err
    }
    return nil
}

const (
    game                 = "{游戏名称}"
    channelPkLimitFormat = "跨房PK过程中不可开启" + game + "玩法"
    maskedPKLimitFormat  = "蒙面PK过程中不可开启" + game + "玩法"

    throwBombGame   = "甩雷"
    digitalBombGame = "数字炸弹"
    adventureGame   = "大冒险"
)

func (mgr *Mgr) otherBizCompatibleV2(ctx context.Context, channelId, gameId uint32) error {
    // 通过玩法互斥中心服务获取房间正在玩的列表，判断房间是否正在进行跨房pk或者蒙面pk
    var gameName string
    switch pgc_channel_game.PgcChannelGameId(gameId) {
    case pgc_channel_game.PgcChannelGameId_GAME_THROW_BOME:
        gameName = throwBombGame
    case pgc_channel_game.PgcChannelGameId_GAME_DIGITAL_BOMB:
        gameName = digitalBombGame
    case pgc_channel_game.PgcChannelGameId_GAME_ADVENTURE:
        gameName = adventureGame
    default:
        gameName = ""
    }
    channelPkLimit := strings.Replace(channelPkLimitFormat, game, gameName, -1)
    maskedPKLimit := strings.Replace(maskedPKLimitFormat, game, gameName, -1)

    // 获取当前房间进行中的游戏列表
    gameListResp, err := mgr.rpcClient.ChannelGameMutualCli.GetCurGameId(ctx, &channelGameMutualPb.GetCurGameIdRequest{
        ChannelId: channelId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "checkCanSetV2.GetCurGameId, channelId: %d, err: %v", channelId, err)
        return err
    }

    curGameList := gameListResp.GetList().GetGameId()
    for _, game := range curGameList {
        // 判断是否正在进行跨房pk或者蒙面pk
        if game == mgr.bc.GetMaskPkGameId() { // 这里建议做成动态配置
            log.ErrorWithCtx(ctx, "otherBizCompatibleV2, channelId: %d, game: %s, err: is mask pking", channelId, game)
            return protocol.NewExactServerError(nil, status.ErrPgcChannelGameGameplayIncompatibility, maskedPKLimit)
        }
        if game == mgr.bc.GetPgcChannelPkGameId() {
            log.ErrorWithCtx(ctx, "otherBizCompatibleV2, channelId: %d, game: %s, err: is pking", channelId, game)
            return protocol.NewExactServerError(nil, status.ErrPgcChannelGameGameplayIncompatibility, channelPkLimit)
        }
    }

    return nil
}

// 玩法兼容
func (mgr *Mgr) otherBizCompatible(ctx context.Context, channelId, gameId uint32) error {
    var channelPkLimit, maskedPKLimit string
    switch pgc_channel_game.PgcChannelGameId(gameId) {
    case pgc_channel_game.PgcChannelGameId_GAME_THROW_BOME:
        channelPkLimit = strings.Replace(channelPkLimitFormat, game, throwBombGame, -1)
        maskedPKLimit = strings.Replace(maskedPKLimitFormat, game, throwBombGame, -1)
    case pgc_channel_game.PgcChannelGameId_GAME_DIGITAL_BOMB:
        channelPkLimit = strings.Replace(channelPkLimitFormat, game, digitalBombGame, -1)
        maskedPKLimit = strings.Replace(maskedPKLimitFormat, game, digitalBombGame, -1)
    case pgc_channel_game.PgcChannelGameId_GAME_ADVENTURE:
        channelPkLimit = strings.Replace(channelPkLimitFormat, game, adventureGame, -1)
        maskedPKLimit = strings.Replace(maskedPKLimitFormat, game, adventureGame, -1)
    default:
        channelPkLimit = strings.Replace(channelPkLimitFormat, game, "", -1)
        maskedPKLimit = strings.Replace(maskedPKLimitFormat, game, "", -1)
    }

    pkResp, err := mgr.rpcClient.PGCChannelPKCli.GetPgcChannelPKId(ctx, &pgcchannelpkpb.GetPgcChannelPKIdReq{
        ChannelId: channelId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "otherBizCompatible.GetPgcChannelPKId, channelId: %d, gameId: %d, err: %v", channelId, gameId, err)
    }
    if pkResp.GetPkId() > 0 {
        log.ErrorWithCtx(ctx, "otherBizCompatible, channelId: %d, gameId: %d, err: is pking", channelId, gameId)
        return protocol.NewExactServerError(nil, status.ErrPgcChannelGameGameplayIncompatibility, channelPkLimit)
    }

    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "otherBizCompatible, channelId: %d, gameId: %d, err: get serviceInfo fail", channelId, gameId)
        return nil
    }
    maskedPkConf, err := mgr.rpcClient.MaskedPKCli.GetChannelMaskedPKCurrConfWithUser(ctx, serviceInfo.UserID, channelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "otherBizCompatible.GetChannelMaskedPKCurrConf, channelId: %d, gameId: %d, err: %v", channelId, gameId, err)
    }
    nowTs := uint32(time.Now().Unix())
    if !maskedPkConf.GetIsGiveUp() && nowTs >= maskedPkConf.GetConf().GetBeginTs() && nowTs <= maskedPkConf.GetConf().GetEndTs() {
        log.ErrorWithCtx(ctx, "otherBizCompatible, channelId: %d, gameId: %d, err: is maskedPking")
        return protocol.NewExactServerError(nil, status.ErrPgcChannelGameGameplayIncompatibility, maskedPKLimit)
    }

    return nil
}

// checkMic 判断主持人/人数是否足够
func (mgr *Mgr) checkMic(ctx context.Context, channelId, uid, gameBombType uint32) error {
    micList, err := mgr.rpcClient.ChannelMicCli.GetMicrList(ctx, channelId, uid)
    if err != nil {
        return err
    }
    isCompere := false
    onMicUserNum := 0
    for _, mic := range micList.AllMicList {
        if mic.MicId == 1 && mic.MicUid == uid {
            isCompere = true
        }
        if mic.MicUid != 0 {
            onMicUserNum++
        }
    }
    if !isCompere {
        log.ErrorWithCtx(ctx, "checkMic, channelId: %d, uid: %d, err: not compere", channelId, uid)
        return protocol.NewExactServerError(nil, status.ErrPgcChannelGame_RequireOnHostMic)
    }

    if gameBombType == game_bomb.GameBombTypeWithHost && onMicUserNum < 2 ||
        (gameBombType == game_bomb.GameBombTypeWithoutHost && onMicUserNum < 3) {
        return protocol.NewExactServerError(nil, status.ErrPgcChannelGameNotEnoughPlayer)
    }

    return nil
}

/*
func (mgr *Mgr) checkManager(ctx context.Context, channelId, uid uint32) error {
	adminList, err := mgr.rpcClient.ChannelCli.GetChannelAdmin(ctx, uid, channelId)
	if err != nil {
		return err
	}
	hasPerm := false
	for _, admin := range adminList {
		if (admin.GetAdminRole() == uint32(channel.ChannelAdminRole_CHANNEL_OWNER) ||
			admin.GetAdminRole() == uint32(channel.ChannelAdminRole_CHANNEL_ADMIN_SUPER) ||
			admin.GetAdminRole() == uint32(channel.ChannelAdminRole_CHANNEL_ADMIN)) &&
			admin.GetUid() == uid {
			hasPerm = true
			break
		}
	}
	if !hasPerm {
		log.ErrorWithCtx(ctx, "checkOperatorPermission, channelId: %d, uid: %d, err: not manager", channelId, uid)
		return protocol.NewExactServerError(nil, status.ErrPgcChannelGameNoStartGamePermission)
	}
	return nil
}

 checkChannelScheme 判断当前房间是否支持该玩法
func (mgr *Mgr) checkChannelScheme(ctx context.Context, channelId, gameId uint32) error {
	gameIds, err := mgr.checkChannelScheme(ctx, channelId, []uint32{gameId})
	if err != nil {
		return err
	}
	if len(gameIds) == 0 {
		log.ErrorWithCtx(ctx, "checkChannelScheme, channelId: %d, err: nonsupport gameId: %d", channelId, gameId)
		return protocol.NewExactServerError(nil, status.ErrPgcChannelGameNoStartGamePermission)
	}
	return nil
}*/

// checkChannelScheme 过滤当前房间模式能玩的玩法id, 目前都基于公会房默认模式, 不做gameId细分判断
func (mgr *Mgr) checkChannelScheme(ctx context.Context, channelId uint32) error {
    sErr := protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "不支持的房间类型")
    channelSimpleInfo, err := mgr.rpcClient.ChannelCli.GetChannelSimpleInfo(ctx, 0, channelId)
    if err != nil {
        return err
    }
    if channelSimpleInfo.GetChannelType() != uint32(channelpb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
        return sErr
    }

    channelScheme, err := mgr.rpcClient.ChannelSchemeCli.GetCurChannelSchemeInfo(ctx, channelId, uint32(channelpb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE))
    if err != nil {
        return err
    }

    if channelScheme.GetSchemeInfo().SchemeSvrDetailType != uint32(channelschemeconfmgr.SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_FUN) &&
        channelScheme.GetSchemeInfo().SchemeSvrDetailType != uint32(channelschemeconfmgr.SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_PIA_XI) {
        return sErr
    }

    return nil
}

func getServiceInfo(ctx context.Context) (*protogrpc.ServiceInfo, error) {
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        return nil, fmt.Errorf("StartGame getServiceInfo fail")
    }
    return serviceInfo, nil
}

func newPgcChannelGameNoStartGamePermissionError(gameId uint32) protocol.ServerError {
    switch pgc_channel_game.PgcChannelGameId(gameId) {
    case pgc_channel_game.PgcChannelGameId_GAME_THROW_BOME:
        return protocol.NewExactServerError(nil, status.ErrPgcChannelGameNoStartGamePermission)
    case pgc_channel_game.PgcChannelGameId_GAME_DIGITAL_BOMB:
        return protocol.NewExactServerError(nil, status.ErrPgcChannelGameNoStartGamePermission, "您暂无开启数字炸弹玩法的权限～")
    case pgc_channel_game.PgcChannelGameId_GAME_ADVENTURE:
        return protocol.NewExactServerError(nil, status.ErrPgcChannelGameNoStartGamePermission, "您暂无开启大冒险玩法的权限～")
    default:
        return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }
}
