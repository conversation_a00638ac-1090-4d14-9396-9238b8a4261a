package game_bomb

import (
    "context"
    "reflect"
    "testing"
    "time"

    "github.com/golang/mock/gomock"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    pbApp "golang.52tt.com/protocol/app"
    pbLogic "golang.52tt.com/protocol/app/pgc_channel_game_logic"
    pbSvr "golang.52tt.com/protocol/services/pgc-channel-game"
    "golang.52tt.com/services/tt-rev/pgc-channel-game/internal/cache"
    "golang.52tt.com/services/tt-rev/pgc-channel-game/internal/conf"
    "golang.52tt.com/services/tt-rev/pgc-channel-game/internal/rpc"
    "golang.52tt.com/services/tt-rev/pgc-channel-game/internal/store"
)

var (
    uid       = uint32(2465835)
    channelId = uint32(2255334)
    ctx       = protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
        UserID: uid,
    })
)

func TestGameBombMgr_ClearUserBombInfo(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    testMgr := NewTestGameBombMgr(ctrl)

    mockCache.EXPECT().GetBombGamePunishInfo(gomock.Any(), gomock.Any()).Return(&pbLogic.GameBombPunishInfo{
        UserProfile: &pbApp.UserProfile{
            Uid:          uid,
            Account:      "",
            Nickname:     "",
            AccountAlias: "",
            Sex:          0,
            Privilege:    nil,
        },
        EndTime:        0,
        RescueGiftId:   0,
        RescueGiftName: "",
        IconUrl:        "",
        IconMd5:        "",
        ServerTime:     0,
    }, nil).AnyTimes()
    mockCache.EXPECT().DelBombGamePunishInfo(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
    mockCache.EXPECT().GetOffMicUserGift(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(10), false, nil).AnyTimes()
    mockCache.EXPECT().DelOffMicUserGift(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        ctx       context.Context
        channelId uint32
        uid       uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        wantErr bool
    }{
        // TODO: Add test cases.
        {
            name:   "TestGameBombMgr_ClearUserBombInfo",
            fields: fields{},
            args: args{
                ctx:       ctx,
                channelId: channelId,
                uid:       uid,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            if err := testMgr.ClearUserBombInfo(tt.args.ctx, tt.args.channelId, tt.args.uid); (err != nil) != tt.wantErr {
                t.Errorf("ClearUserBombInfo() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestGameBombMgr_GenAutoBomb(t *testing.T) {
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    tests := []struct {
        name   string
        fields fields
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mgr := &GameBombMgr{
                sc:        tt.fields.sc,
                bc:        tt.fields.bc,
                cache:     tt.fields.cache,
                store:     tt.fields.store,
                rpcClient: tt.fields.rpcClient,
            }
            mgr.GenAutoBomb()
        })
    }
}

func TestGameBombMgr_GenAutoThrow(t *testing.T) {
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    tests := []struct {
        name   string
        fields fields
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mgr := &GameBombMgr{
                sc:        tt.fields.sc,
                bc:        tt.fields.bc,
                cache:     tt.fields.cache,
                store:     tt.fields.store,
                rpcClient: tt.fields.rpcClient,
            }
            mgr.GenAutoThrow()
        })
    }
}

func TestGameBombMgr_GetChannelGameInfo(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    testMgr := NewTestGameBombMgr(ctrl)

    mockCache.EXPECT().GetChannelCurrentGameInfo(gomock.Any(), channelId).Return(&cache.GameInfo{
        GameId:    1,
        GameType:  1,
        StartTime: 0,
    }, true, nil)
    mockCache.EXPECT().GetBombUser(gomock.Any(), channelId).Return(uid, true, nil).AnyTimes()
    mockCache.EXPECT().GetBombTime(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil).AnyTimes()
    mockCache.EXPECT().GetGameBombOnMicUidList(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{1: 1, 2: 2}, nil).AnyTimes()

    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        ctx       context.Context
        channelId uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        wantOut *pbSvr.GetChannelGameInfoResp
        wantErr bool
    }{
        // TODO: Add test cases.
        {
            name:   "TestGameBombMgr_GetChannelGameInfo",
            fields: fields{},
            args: args{
                ctx:       ctx,
                channelId: channelId,
            },
            wantOut: &pbSvr.GetChannelGameInfoResp{
                ChannelId:     channelId,
                CurrentGameId: 1,
                Phase:         0,
                Info:          nil,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            gotOut, err := testMgr.GetChannelGameInfo(tt.args.ctx, tt.args.channelId)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetChannelGameInfo() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotOut.CurrentGameId, tt.wantOut.CurrentGameId) {
                t.Errorf("GetChannelGameInfo() gotOut = %v, want %v", gotOut, tt.wantOut)
            }
        })
    }
}

func TestGameBombMgr_HandleGameBombEnd(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    testMgr := NewTestGameBombMgr(ctrl)

    mockCache.EXPECT().GetChannelCurrentGameInfo(gomock.Any(), gomock.Any()).Return(&cache.GameInfo{
        GameId:    1,
        GameType:  1,
        StartTime: uint32(time.Now().Unix()),
    }, true, nil).AnyTimes()
    mockBc.EXPECT().GetBombMinSec().Return(uint32(10)).AnyTimes()
    mockYouKnowWhoCli.EXPECT().GetTrueUidByFake(gomock.Any(), gomock.Any()).Return(uid, nil).AnyTimes()
    mockUserProfileCli.EXPECT().GetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
    mockCache.EXPECT().GetChannelCurrentGameInfo(gomock.Any(), gomock.Any()).Return(&cache.GameInfo{
        GameId:    1,
        GameType:  1,
        StartTime: uint32(time.Now().Unix()),
    }, true, nil).AnyTimes()
    mockPushCli.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
    mockMaskedPKCli.EXPECT().PushGameBeginConf(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
    mockCache.EXPECT().CleanGame(gomock.Any(), gomock.Any()).Return().AnyTimes()
    mockCache.EXPECT().DelBomb(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
    mockPushCli.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        channelId   uint32
        uid         uint32
        targetMicId uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        wantErr bool
    }{
        // TODO: Add test cases.
        {
            name:   "TestGameBombMgr_HandleGameBombEnd",
            fields: fields{},
            args: args{
                channelId:   channelId,
                uid:         uid,
                targetMicId: 0,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            if err := testMgr.HandleGameBombEnd(context.Background(), tt.args.channelId, tt.args.uid, tt.args.targetMicId); (err != nil) != tt.wantErr {
                t.Errorf("HandleGameBombEnd() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestGameBombMgr_InitThrowBombGame(t *testing.T) {
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        ctx       context.Context
        channelId uint32
        uid       uint32
        gameId    uint32
        gameType  uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mgr := &GameBombMgr{
                sc:        tt.fields.sc,
                bc:        tt.fields.bc,
                cache:     tt.fields.cache,
                store:     tt.fields.store,
                rpcClient: tt.fields.rpcClient,
            }
            if err := mgr.InitThrowBombGame(tt.args.ctx, tt.args.channelId, tt.args.uid, tt.args.gameId, tt.args.gameType); (err != nil) != tt.wantErr {
                t.Errorf("InitThrowBombGame() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestGameBombMgr_ThrowBomb(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    testMgr := NewTestGameBombMgr(ctrl)

    mockCache.EXPECT().GetBombUser(gomock.Any(), gomock.Any()).Return(uid, true, nil).AnyTimes()
    mockYouKnowWhoCli.EXPECT().GetTrueUidByFake(gomock.Any(), gomock.Any()).Return(uid, nil).AnyTimes()
    mockBc.EXPECT().GetInitWaitSec().Return(uint32(10)).AnyTimes()
    mockCache.EXPECT().GetGameOnMicUserGift(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(2), true, nil)
    mockCache.EXPECT().GetGameBombThrowUidList(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]uint32{1: 1}, nil)
    mockCache.EXPECT().DelBomb(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
    mockCache.EXPECT().SetBombTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
    mockBc.EXPECT().GetAutoThrowSec().Return(uint32(109)).AnyTimes()
    mockCache.EXPECT().AddAutoThrowTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
    mockPushCli.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

    mockCache.EXPECT().GetGameBombOnMicUidList(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
    mockPushCli.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
    mockCache.EXPECT().GetChannelCurrentGameInfo(gomock.Any(), gomock.Any()).Return(&cache.GameInfo{
        GameId:    1,
        GameType:  1,
        StartTime: uint32(time.Now().Unix()),
    }, true, nil).AnyTimes()
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        ctx       context.Context
        channelId uint32
        throwUid  uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        wantErr bool
    }{
        // TODO: Add test cases.
        {
            name:   "TestGameBombMgr_ThrowBomb",
            fields: fields{},
            args: args{
                ctx:       ctx,
                channelId: channelId,
                throwUid:  1,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            if err := testMgr.ThrowBomb(tt.args.ctx, tt.args.channelId, tt.args.throwUid); (err != nil) != tt.wantErr {
                t.Errorf("ThrowBomb() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestGameBombMgr_checkRescue(t *testing.T) {
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        boomGift         uint32
        onMicUserGiftMap map[uint32]uint32
    }
    tests := []struct {
        name   string
        fields fields
        args   args
        want   bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mgr := &GameBombMgr{
                sc:        tt.fields.sc,
                bc:        tt.fields.bc,
                cache:     tt.fields.cache,
                store:     tt.fields.store,
                rpcClient: tt.fields.rpcClient,
            }
            if got := mgr.checkRescue(tt.args.boomGift, tt.args.onMicUserGiftMap); got != tt.want {
                t.Errorf("checkRescue() = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestGameBombMgr_genNextBombUid(t *testing.T) {
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        channelId uint32
        bombUid   uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    uint32
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mgr := &GameBombMgr{
                sc:        tt.fields.sc,
                bc:        tt.fields.bc,
                cache:     tt.fields.cache,
                store:     tt.fields.store,
                rpcClient: tt.fields.rpcClient,
            }
            got, err := mgr.genNextBombUid(context.Background(), tt.args.channelId, tt.args.bombUid)
            if (err != nil) != tt.wantErr {
                t.Errorf("genNextBombUid() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if got != tt.want {
                t.Errorf("genNextBombUid() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestGameBombMgr_getGameBombEndType_EndWithNoBomb_1(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    testMgr := NewTestGameBombMgr(ctrl)

    mockCache.EXPECT().GetChannelCurrentGameInfo(gomock.Any(), gomock.Any()).Return(&cache.GameInfo{
        GameId:    1,
        GameType:  1,
        StartTime: uint32(time.Now().Unix()),
    }, true, nil).AnyTimes()
    mockBc.EXPECT().GetBombMinSec().Return(uint32(10)).AnyTimes()

    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        channelId   uint32
        targetMicId uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    uint32
        want1   string
        wantErr bool
    }{
        // TODO: Add test cases.
        {
            name:   "TestGameBombMgr_getGameBombEndType_EndWithNoBomb_1",
            fields: fields{},
            args: args{
                channelId:   channelId,
                targetMicId: 0,
            },
            want:    2,
            want1:   "",
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            got, _, err := testMgr.getGameBombEndType(context.Background(), tt.args.channelId, tt.args.targetMicId)
            if (err != nil) != tt.wantErr {
                t.Errorf("getGameBombEndType() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if got != tt.want {
                t.Errorf("getGameBombEndType() got = %v, want %v", got, tt.want)
            }

        })
    }
}

func TestGameBombMgr_getGameBombEndType_EndWithNoBomb(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    testMgr := NewTestGameBombMgr(ctrl)

    mockCache.EXPECT().GetChannelCurrentGameInfo(gomock.Any(), gomock.Any()).Return(&cache.GameInfo{
        GameId:    1,
        GameType:  1,
        StartTime: uint32(0),
    }, true, nil).AnyTimes()
    mockBc.EXPECT().GetBombMinSec().Return(uint32(10)).AnyTimes()
    mockCache.EXPECT().GetGameBombOnMicUidList(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{}, nil).AnyTimes()
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        channelId   uint32
        targetMicId uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    uint32
        want1   string
        wantErr bool
    }{
        // TODO: Add test cases.
        {
            name:   "TestGameBombMgr_getGameBombEndType_EndWithNoBomb",
            fields: fields{},
            args: args{
                channelId:   channelId,
                targetMicId: 0,
            },
            want:    2,
            want1:   "",
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            got, _, err := testMgr.getGameBombEndType(context.Background(), tt.args.channelId, tt.args.targetMicId)
            if (err != nil) != tt.wantErr {
                t.Errorf("getGameBombEndType() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if got != tt.want {
                t.Errorf("getGameBombEndType() got = %v, want %v", got, tt.want)
            }

        })
    }
}

func TestGameBombMgr_getGameBombEndType_Abnormal(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    testMgr := NewTestGameBombMgr(ctrl)

    mockCache.EXPECT().GetChannelCurrentGameInfo(gomock.Any(), gomock.Any()).Return(&cache.GameInfo{
        GameId:    1,
        GameType:  1,
        StartTime: uint32(0),
    }, true, nil).AnyTimes()
    mockBc.EXPECT().GetBombMinSec().Return(uint32(10)).AnyTimes()
    mockCache.EXPECT().GetGameBombOnMicUidList(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{2: 2, 1: 1}, nil).AnyTimes()
    mockBc.EXPECT().GetPunishSec().Return(uint32(120)).AnyTimes()
    mockBc.EXPECT().GetRescueGiftName().Return("test").AnyTimes()
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        channelId   uint32
        targetMicId uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    uint32
        want1   string
        wantErr bool
    }{
        // TODO: Add test cases.
        {
            name:   "TestGameBombMgr_getGameBombEndType_EndWithNoBomb",
            fields: fields{},
            args: args{
                channelId:   channelId,
                targetMicId: 1,
            },
            want:    1,
            want1:   "",
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            got, _, err := testMgr.getGameBombEndType(context.Background(), tt.args.channelId, tt.args.targetMicId)
            if (err != nil) != tt.wantErr {
                t.Errorf("getGameBombEndType() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if got != tt.want {
                t.Errorf("getGameBombEndType() got = %v, want %v", got, tt.want)
            }

        })
    }
}

func TestGameBombMgr_getIsHost(t *testing.T) {
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        ctx       context.Context
        channelId uint32
        uid       uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        want    bool
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mgr := &GameBombMgr{
                sc:        tt.fields.sc,
                bc:        tt.fields.bc,
                cache:     tt.fields.cache,
                store:     tt.fields.store,
                rpcClient: tt.fields.rpcClient,
            }
            got, err := mgr.getIsHost(tt.args.ctx, tt.args.channelId, tt.args.uid)
            if (err != nil) != tt.wantErr {
                t.Errorf("getIsHost() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if got != tt.want {
                t.Errorf("getIsHost() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestGameBombMgr_getRandomBombUid(t *testing.T) {
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        uidList []uint32
    }
    tests := []struct {
        name   string
        fields fields
        args   args
        want   uint32
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mgr := &GameBombMgr{
                sc:        tt.fields.sc,
                bc:        tt.fields.bc,
                cache:     tt.fields.cache,
                store:     tt.fields.store,
                rpcClient: tt.fields.rpcClient,
            }
            if got := mgr.getRandomBombUid(tt.args.uidList); got != tt.want {
                t.Errorf("getRandomBombUid() = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestGameBombMgr_handlePunishInfo(t *testing.T) {
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        ctx        context.Context
        channelId  uint32
        realUid    uint32
        oldFakeUid uint32
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mgr := &GameBombMgr{
                sc:        tt.fields.sc,
                bc:        tt.fields.bc,
                cache:     tt.fields.cache,
                store:     tt.fields.store,
                rpcClient: tt.fields.rpcClient,
            }
            if err := mgr.handlePunishInfo(tt.args.ctx, tt.args.channelId, tt.args.realUid, tt.args.oldFakeUid); (err != nil) != tt.wantErr {
                t.Errorf("handlePunishInfo() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestGameBombMgr_handleRescue(t *testing.T) {
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        channelId uint32
        fromUid   uint32
        uid       uint32
        giftId    uint32
        giftValue uint32
        orderId   string
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mgr := &GameBombMgr{
                sc:        tt.fields.sc,
                bc:        tt.fields.bc,
                cache:     tt.fields.cache,
                store:     tt.fields.store,
                rpcClient: tt.fields.rpcClient,
            }
            if err := mgr.handleRescue(tt.args.channelId, tt.args.fromUid, tt.args.uid, tt.args.giftId, tt.args.giftValue, tt.args.orderId); (err != nil) != tt.wantErr {
                t.Errorf("handleRescue() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestGameBombMgr_transPunishInfo(t *testing.T) {
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        info *pbLogic.GameBombPunishInfo
    }
    tests := []struct {
        name   string
        fields fields
        args   args
        want   *pbSvr.GameBombPunishInfo
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mgr := &GameBombMgr{
                sc:        tt.fields.sc,
                bc:        tt.fields.bc,
                cache:     tt.fields.cache,
                store:     tt.fields.store,
                rpcClient: tt.fields.rpcClient,
            }
            if got := mgr.transPunishInfo(tt.args.info); !reflect.DeepEqual(got, tt.want) {
                t.Errorf("transPunishInfo() = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestGameBombMgr_transUserProfile(t *testing.T) {
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        appProfile *pbApp.UserProfile
    }
    tests := []struct {
        name   string
        fields fields
        args   args
        want   *pbSvr.UserProfile
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mgr := &GameBombMgr{
                sc:        tt.fields.sc,
                bc:        tt.fields.bc,
                cache:     tt.fields.cache,
                store:     tt.fields.store,
                rpcClient: tt.fields.rpcClient,
            }
            if got := mgr.transUserProfile(tt.args.appProfile); !reflect.DeepEqual(got, tt.want) {
                t.Errorf("transUserProfile() = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestGameBombMgr_updateAllThrowUserInfo(t *testing.T) {
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        ctx           context.Context
        channelId     uint32
        bombGift      uint32
        onMicGameUser map[uint32]uint32
    }
    tests := []struct {
        name   string
        fields fields
        args   args
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mgr := &GameBombMgr{
                sc:        tt.fields.sc,
                bc:        tt.fields.bc,
                cache:     tt.fields.cache,
                store:     tt.fields.store,
                rpcClient: tt.fields.rpcClient,
            }
            mgr.updateAllThrowUserInfo(tt.args.ctx, tt.args.channelId, tt.args.bombGift, tt.args.onMicGameUser)
        })
    }
}

func TestGameBombMgr_updateBombUser(t *testing.T) {
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        ctx           context.Context
        channelId     uint32
        bombUid       uint32
        bombTime      uint32
        autoThrowTime uint32
        needRescue    bool
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mgr := &GameBombMgr{
                sc:        tt.fields.sc,
                bc:        tt.fields.bc,
                cache:     tt.fields.cache,
                store:     tt.fields.store,
                rpcClient: tt.fields.rpcClient,
            }
            if err := mgr.updateBombUser(tt.args.ctx, tt.args.channelId, tt.args.bombUid, tt.args.bombTime, tt.args.autoThrowTime, tt.args.needRescue); (err != nil) != tt.wantErr {
                t.Errorf("updateBombUser() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestGameBombMgr_updateSingleThrowUserInfo(t *testing.T) {
    type fields struct {
        sc        conf.IStartConfig
        bc        conf.IBusinessConfManager
        cache     cache.ICache
        store     store.IStore
        rpcClient *rpc.Client
    }
    type args struct {
        ctx       context.Context
        channelId uint32
        uid       uint32
        gift      uint32
        bombGift  uint32
    }
    tests := []struct {
        name   string
        fields fields
        args   args
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mgr := &GameBombMgr{
                sc:        tt.fields.sc,
                bc:        tt.fields.bc,
                cache:     tt.fields.cache,
                store:     tt.fields.store,
                rpcClient: tt.fields.rpcClient,
            }
            mgr.updateSingleThrowUserInfo(tt.args.ctx, tt.args.channelId, tt.args.uid, tt.args.gift, tt.args.bombGift)
        })
    }
}

func Test_getServiceInfo(t *testing.T) {
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name    string
        args    args
        want    *protogrpc.ServiceInfo
        wantErr bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            got, err := getServiceInfo(tt.args.ctx)
            if (err != nil) != tt.wantErr {
                t.Errorf("getServiceInfo() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("getServiceInfo() got = %v, want %v", got, tt.want)
            }
        })
    }
}
