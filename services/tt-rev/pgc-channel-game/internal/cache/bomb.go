package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"golang.52tt.com/pkg/log"
	pbApp "golang.52tt.com/protocol/app"
	pbLogic "golang.52tt.com/protocol/app/pgc_channel_game_logic"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
)

type GameInfo struct {
	GameId    uint32 `json:"game_id"`
	GameType  uint32 `json:"game_type"`
	StartTime uint32 `json:"start_time"`
}

type PunishInfo struct {
	Uid      uint32 `json:"uid"`
	GameType uint32 `json:"game_type"`
}

type GameBombExpired struct {
	ChannelId uint32
	BombUid   uint32
}

var getAutoThrowChannelsScriptSha string
var getAutoThrowChannelsScript = `
    local expire_time = tonumber(ARGV[1])
    local ret = {}
    local key = 'zset_game_bomb_auto_throw'
    local ids = redis.call('ZRANGEBYSCORE', key, '-INF', expire_time, 'LIMIT', 0, 10)
    for ix = 1, #ids do
        table.insert(ret, ids[ix])
        redis.call('ZREM', key,ids[ix])
     end
    return ret
`

var getBoomChannelsScriptSha string
var getBoomChannelsScript = `
    local expire_time = tonumber(ARGV[1])
    local ret = {}
    local key = 'zset_game_bomb_channel'
    local ids = redis.call('ZRANGEBYSCORE', key, '-INF', expire_time, 'LIMIT', 0, 10)
    for ix = 1, #ids do
        table.insert(ret, ids[ix])
        redis.call('ZREM', key, ids[ix])
    end
    return ret
`

func genChannelBombAutoThrowKey() string {
	return "zset_game_bomb_auto_throw"
}

func genChannelBombUserKey() string {
	return "zset_game_bomb_channel"
}

func genBombUserKey(channelId uint32) string {
	return fmt.Sprintf("set_pgc_game_bomb_user_%d", channelId)
}

const gameTypeExtraExpire = 5 * time.Second

func genGameTypeKey(channelId uint32) string {
	return fmt.Sprintf("set_pgc_game_type_%d", channelId)
}

func genGameOnMicUserKey(channelId uint32) string {
	return fmt.Sprintf("zset_pgc_game_on_mic_%d", channelId)
}

func genGameOffMicUserKey(channelId uint32) string {
	return fmt.Sprintf("zset_pgc_game_off_mic_%d", channelId)
}

func genGameBombPunishKey(uid uint32) string {
	return fmt.Sprintf("set_game_bomb_punish_%d", uid)
}

func genChannelPunishKey(channelId uint32) string {
	return fmt.Sprintf("hset_pgc_game_punish_%d", channelId)
}

func genGameBoomMicUserKey(channelId uint32) string {
	return fmt.Sprintf("hset_game_boom_user_%d", channelId)
}

func (cache *Cache) InitThrowBombGame(ctx context.Context, channelId, gameId, gameType, bombUid, bombTime uint32, uidList []uint32) error {
	if len(uidList) == 0 {
		return nil
	}

	//麦上用户
	//设置当前游戏类型
	expired := time.Duration(bombTime-uint32(time.Now().Unix())) * time.Second
	gameKey := genGameTypeKey(channelId)
	gameInfo := &GameInfo{
		GameId:    gameId,
		GameType:  gameType,
		StartTime: uint32(time.Now().Unix()),
	}
	gameStr, _ := json.Marshal(gameInfo)

	pipe := cache.cmder.Pipeline()
	defer pipe.Close()

	err := pipe.Set(ctx, gameKey, gameStr, expired+gameTypeExtraExpire).Err()
	if err != nil {
		log.Errorf("InitThrowBombGame failed channelId:%d, err", channelId, err)
		return err
	}

	key := genGameOnMicUserKey(channelId)
	pipe.Del(ctx, key)
	redisZ := make([]*redis.Z, 0, len(uidList))
	for _, uid := range uidList {
		member := &redis.Z{
			Score:  float64(0),
			Member: strconv.FormatUint(uint64(uid), 10),
		}
		redisZ = append(redisZ, member)
	}
	pipe.ZAdd(ctx, key, redisZ...)
	pipe.Expire(ctx, key, time.Hour*2)

	//设置雷超时
	bombKey := genBombUserKey(channelId)
	pipe.Set(ctx, bombKey, bombUid, expired)

	channelBombKey := genChannelBombUserKey()
	member := &redis.Z{
		Score:  float64(bombTime),
		Member: fmt.Sprintf("%d_%d", channelId, bombUid),
	}
	pipe.ZAdd(ctx, channelBombKey, member)

	//清理惩罚信息
	channelPunishKey := genChannelPunishKey(channelId)
	pipe.Del(ctx, channelPunishKey)

	_, err = pipe.Exec(ctx)
	if nil != err {
		return err
	}
	return nil
}

func (cache *Cache) GetGameBombOnMicUidList(ctx context.Context, channelId uint32) (map[uint32]uint32, error) {
	key := genGameOnMicUserKey(channelId)
	zRange := &redis.ZRangeBy{
		Min:    "-inf",
		Max:    "+inf",
		Offset: 0,
		Count:  -1,
	}

	zs, err := cache.cmder.ZRangeByScoreWithScores(ctx, key, zRange).Result()
	if err != nil {
		log.Errorf("GetGameBombOnMicUidList cid:%d, err:%v", channelId, err)
		return nil, err
	}
	uid2giftMap := make(map[uint32]uint32)
	for _, z := range zs {
		mebStr := z.Member.(string)
		uid, err := strconv.Atoi(mebStr)
		if err != nil {
			log.Errorf("GetGameBombThrowUidList cid:%d, err:%v", channelId, err)
			return uid2giftMap, err
		}
		uid2giftMap[uint32(uid)] = uint32(z.Score)
	}
	return uid2giftMap, err
}

func (cache *Cache) GetBombUser(ctx context.Context, channelId uint32) (uint32, bool, error) {
	key := genBombUserKey(channelId)
	value, err := cache.cmder.Get(ctx, key).Int()
	if err == redis.Nil {
		return 0, false, nil
	}

	if err != nil {
		return 0, false, err
	}

	return uint32(value), true, nil
}

func (cache *Cache) GetChannelGameBombType(ctx context.Context, channelId uint32) (uint32, bool, error) {
	key := genGameTypeKey(channelId)
	value, err := cache.cmder.Get(ctx, key).Bytes()
	if err == redis.Nil {
		return 0, false, nil
	}

	if err != nil {
		return 0, false, err
	}

	var gameInfo GameInfo
	json.Unmarshal(value, &gameInfo)

	return gameInfo.GameType, true, nil
}

func (cache *Cache) GetChannelCurrentGameInfo(ctx context.Context, channelId uint32) (*GameInfo, bool, error) {
	key := genGameTypeKey(channelId)
	value, err := cache.cmder.Get(ctx, key).Bytes()
	var gameInfo GameInfo
	if err == redis.Nil {
		return &gameInfo, false, nil
	}

	if err != nil {
		return &gameInfo, false, err
	}

	json.Unmarshal(value, &gameInfo)

	return &gameInfo, true, nil
}

// BatchGetChannelCurrentGameInfo use MGet
func (cache *Cache) BatchGetChannelCurrentGameInfo(ctx context.Context, channelIds []uint32) (map[uint32]*GameInfo, error) {
	keys := make([]string, 0, len(channelIds))
	for _, channelId := range channelIds {
		keys = append(keys, genGameTypeKey(channelId))
	}
	values, err := cache.cmder.MGet(ctx, keys...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetChannelCurrentGameInfo failed to MGet. err:%v", err)
		return nil, err
	}

	gameInfoMap := make(map[uint32]*GameInfo)
	for i, value := range values {
		if value == nil {
			continue
		}

		var gameInfo GameInfo
		err := json.Unmarshal([]byte(value.(string)), &gameInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetChannelCurrentGameInfo failed to Unmarshal. err:%v", err)
			continue
		}
		gameInfoMap[channelIds[i]] = &gameInfo
	}
	return gameInfoMap, nil
}

func (cache *Cache) GetBombTime(ctx context.Context, channelId, uid uint32) (uint32, error) {
	key := genChannelBombUserKey()
	value, err := cache.cmder.ZScore(ctx, key, fmt.Sprintf("%d_%d", channelId, uid)).Result()
	if err == redis.Nil {
		return 0, nil
	}

	if err != nil {
		return 0, err
	}

	return uint32(value), nil
}

func (cache *Cache) DelBomb(ctx context.Context, channelId, uid uint32) error {
	key := genChannelBombUserKey()
	throwKey := genChannelBombAutoThrowKey()
	cache.cmder.ZRem(ctx, throwKey, fmt.Sprintf("%d_%d", channelId, uid)).Err()
	return cache.cmder.ZRem(ctx, key, fmt.Sprintf("%d_%d", channelId, uid)).Err()
}

func (cache *Cache) DelAutoThrow(ctx context.Context, channelId, uid uint32) error {
	throwKey := genChannelBombAutoThrowKey()
	return cache.cmder.ZRem(ctx, throwKey, fmt.Sprintf("%d_%d", channelId, uid)).Err()
}

func (cache *Cache) SetBombTime(ctx context.Context, channelId, uid, bombTime uint32) error {
	log.Infof("SetBombTime channelId:%d, uid:%d, bombTime:%d", channelId, uid, bombTime)
	//设置雷超时
	expired := time.Duration(bombTime-uint32(time.Now().Unix())) * time.Second

	pipe := cache.cmder.Pipeline()
	gameKey := genGameTypeKey(channelId)
	pipe.Expire(ctx, gameKey, expired+gameTypeExtraExpire)

	bombKey := genBombUserKey(channelId)
	pipe.Set(ctx, bombKey, uid, expired+time.Second)

	channelBombKey := genChannelBombUserKey()
	member := &redis.Z{
		Score:  float64(bombTime),
		Member: fmt.Sprintf("%d_%d", channelId, uid),
	}
	pipe.ZAdd(ctx, channelBombKey, member)
	_, err := pipe.Exec(ctx)
	return err
}

func (cache *Cache) GetAutoThrowTime(ctx context.Context, channelId, uid uint32) (uint32, error) {
	key := genChannelBombAutoThrowKey()
	value, err := cache.cmder.ZScore(ctx, key, fmt.Sprintf("%d_%d", channelId, uid)).Result()
	if err == redis.Nil {
		return 0, nil
	}

	if err != nil {
		return 0, err
	}
	return uint32(value), nil
}

func (cache *Cache) AddAutoThrowTime(ctx context.Context, channelId, uid, expiredTime uint32) error {
	log.Infof("AddAutoThrowTime channelId:%d,uid：%d, expired:%d", channelId, uid, expiredTime)
	key := genChannelBombAutoThrowKey()
	member := &redis.Z{
		Score:  float64(expiredTime),
		Member: fmt.Sprintf("%d_%d", channelId, uid),
	}
	return cache.cmder.ZAdd(ctx, key, member).Err()
}

func (cache *Cache) GetTimeoutAutoThrowChannel(ctx context.Context, endTime int64) ([]*GameBombExpired, error) {
	list := make([]*GameBombExpired, 0)

	if getAutoThrowChannelsScriptSha == "" {
		var err error
		getAutoThrowChannelsScriptSha, err = cache.cmder.ScriptLoad(ctx, getAutoThrowChannelsScript).Result()
		if err != nil {
			return nil, err
		}
	}

	ret, err := cache.cmder.EvalSha(ctx, getAutoThrowChannelsScriptSha, []string{genChannelBombAutoThrowKey()}, endTime).Result()
	if err != nil {
		return nil, err
	}

	if retList, ok := ret.([]interface{}); ok {
		for _, ret := range retList {
			strList := strings.Split(ret.(string), "_")
			cid, _ := strconv.Atoi(strList[0])
			uid, _ := strconv.Atoi(strList[1])
			changeInfo := &GameBombExpired{
				ChannelId: uint32(cid),
				BombUid:   uint32(uid),
			}
			list = append(list, changeInfo)
			log.Infof("GetTimeoutAutoThrowChannel changeInfo:%v", changeInfo)
		}
	}
	return list, nil
}

func (cache *Cache) GetTimeoutBombChannel(ctx context.Context, endTime int64) ([]*GameBombExpired, error) {
	list := make([]*GameBombExpired, 0)
	if getBoomChannelsScriptSha == "" {
		var err error
		getBoomChannelsScriptSha, err = cache.cmder.ScriptLoad(ctx, getBoomChannelsScript).Result()
		if err != nil {
			log.ErrorWithCtx(ctx, "GetTimeoutBombChannel fail to ScriptLoad. err:%v", err)
			return nil, err
		}
	}

	ret, err := cache.cmder.EvalSha(ctx, getBoomChannelsScriptSha, []string{genChannelBombUserKey()}, endTime).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTimeoutBombChannel fail to EvalSha. err:%v", err)
		return nil, err
	}
	if retList, ok := ret.([]interface{}); ok {
		for _, ret := range retList {
			strList := strings.Split(ret.(string), "_")
			cid, _ := strconv.Atoi(strList[0])
			uid, _ := strconv.Atoi(strList[1])
			changeInfo := &GameBombExpired{
				ChannelId: uint32(cid),
				BombUid:   uint32(uid),
			}
			list = append(list, changeInfo)
			log.Infof("GetTimeoutBombChannel changeInfo:%v", changeInfo)
		}
	}
	return list, err
}

func (cache *Cache) GetBombGamePunishInfo(ctx context.Context, uid uint32) (info *pbLogic.GameBombPunishInfo, err error) {
	key := genGameBombPunishKey(uid)

	value, err := cache.cmder.Get(ctx, key).Bytes()
	if err == redis.Nil {
		return nil, nil
	}

	if err != nil {
		return nil, err
	}
	var res pbLogic.GameBombPunishInfo
	proto.Unmarshal(value, &res)

	if res.GetEndTime() < uint32(time.Now().Unix()) {
		return nil, nil
	}

	return &res, nil
}

func (cache *Cache) SetBombGamePunishInfo(ctx context.Context, channelId, targetMicId uint32, info *pbLogic.GameBombPunishInfo) error {
	uid := info.GetUserProfile().GetUid()
	key := genGameBombPunishKey(uid)
	expired := time.Duration(info.GetEndTime()-uint32(time.Now().Unix())) * time.Second
	msg, _ := proto.Marshal(info)
	if targetMicId == 0 {
		channelKey := genChannelPunishKey(channelId)
		cache.cmder.HSet(ctx, channelKey, fmt.Sprintf("%d", uid), msg)
		cache.cmder.Expire(ctx, channelKey, expired)
	}
	return cache.cmder.Set(ctx, key, msg, expired).Err()
}

func (cache *Cache) DelBombGamePunishInfo(ctx context.Context, uids []uint32) error {
	keys := make([]string, 0, len(uids))
	for _, uid := range uids {
		key := genGameBombPunishKey(uid)
		keys = append(keys, key)
	}
	return cache.cmder.Del(ctx, keys...).Err()
}

func (cache *Cache) SetChannelPunishInfo(ctx context.Context, channelId uint32, info *pbLogic.GameBombPunishInfo) error {
	uid := info.GetUserProfile().GetUid()
	channelKey := genChannelPunishKey(channelId)
	msg, _ := proto.Marshal(info)
	cache.cmder.HSet(ctx, channelKey, fmt.Sprintf("%d", uid), msg).Err()
	cache.cmder.Expire(ctx, channelKey, time.Duration(info.GetEndTime()-uint32(time.Now().Unix()))*time.Second)
	return nil
}

func (cache *Cache) GetChannelPunishInfos(ctx context.Context, channelId uint32) (map[uint32]*pbLogic.GameBombPunishInfo, error) {
	channelKey := genChannelPunishKey(channelId)
	uid2Time := make(map[uint32]*pbLogic.GameBombPunishInfo)
	rs, err := cache.cmder.HGetAll(ctx, channelKey).Result()
	if err != nil {
		return uid2Time, err
	}
	needToBeDeletedKey := make([]string, 0, len(rs))
	for k, value := range rs {
		var info pbLogic.GameBombPunishInfo
		proto.Unmarshal([]byte(value), &info)
		if info.GetEndTime() < uint32(time.Now().Unix()) {
			needToBeDeletedKey = append(needToBeDeletedKey, k)
			continue
		}
		uid2Time[info.GetUserProfile().GetUid()] = &info
	}
	if len(needToBeDeletedKey) != 0 {
		cache.cmder.HDel(ctx, channelKey, needToBeDeletedKey...)
	}
	return uid2Time, err
}

func (cache *Cache) DelChannelPunishInfo(ctx context.Context, channelId, uid uint32) error {
	channelKey := genChannelPunishKey(channelId)
	err := cache.cmder.HDel(ctx, channelKey, fmt.Sprintf("%d", uid)).Err()
	log.Infof("DelChannelPunishInfo channelId:%d, uid:%d, err:%v", channelId, uid, err)
	return err
}

func (cache *Cache) DelChannelAllPunishInfo(ctx context.Context, channelId uint32) error {
	channelKey := genChannelPunishKey(channelId)
	return cache.cmder.Del(ctx, channelKey).Err()
}

func (cache *Cache) CleanGame(ctx context.Context, channelId uint32) {
	log.InfoWithCtx(ctx, "CleanGame channelId:%d", channelId)

	gameKey := genGameTypeKey(channelId)
	giftKey := genGameOnMicUserKey(channelId)
	giftKey1 := genGameOffMicUserKey(channelId)
	bombKey := genBombUserKey(channelId)
	cache.cmder.Del(ctx, []string{gameKey, giftKey, giftKey1, bombKey}...)
}

// SetChannelMicPKUser 实际PK的玩家缓存
func (cache *Cache) SetChannelMicPKUser(ctx context.Context, channelId uint32, mic2Uid map[uint32]uint32) error {
	if len(mic2Uid) == 0 {
		return nil
	}

	key := genGameBoomMicUserKey(channelId)
	fields := make(map[string]interface{})

	for micId, uid := range mic2Uid {
		strMicId := fmt.Sprint(micId)
		fields[strMicId] = uid
	}
	log.Debugf("SetChannelMicPKUser cid:%d, midList:%v", channelId, mic2Uid)
	return cache.cmder.HMSet(ctx, key, fields).Err()
}

// 实际PK的玩家缓存
func (cache *Cache) GetChannelMicPKUser(ctx context.Context, channelId uint32, micIdList []uint32) (map[uint32]uint32, error) {
	key := genGameBoomMicUserKey(channelId)
	out := make(map[uint32]uint32)
	if len(micIdList) == 0 {
		return out, nil
	}

	fields := make([]string, 0, len(micIdList))
	for _, micId := range micIdList {
		fields = append(fields, fmt.Sprint(micId))
	}

	resultList, err := cache.cmder.HMGet(ctx, key, fields...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMicPKUser fail to HMGet. channelId:%v, micIdList:%v, err:%v", channelId, micIdList, err)
		return out, nil
	}

	for i, res := range resultList {
		micId := micIdList[i]

		if res == nil {
			out[micId] = 0

		}
		if strUid, ok := res.(string); ok {
			uid, _ := strconv.ParseUint(strUid, 10, 32)
			out[micId] = uint32(uid)
		}
	}
	log.Debugf("GetChannelMicPKUser cid:%d, midList:%v, res:%v, out:%v", channelId, micIdList, resultList, out)

	return out, nil
}

func (cache *Cache) ClearChannelMicPKUser(ctx context.Context, channelId uint32) error {
	return cache.cmder.Del(ctx, genGameBoomMicUserKey(channelId)).Err()
}

func genUserProfileKey(channelId, uid uint32) string {
	return fmt.Sprintf("set_bomb_user_profile_%d_%d", channelId, uid)
}

func (cache *Cache) AddUserProfile(ctx context.Context, channelId, uid uint32, profile *pbApp.UserProfile) error {
	log.Debugf("AddUserProfile cid:%d, profile:%v", channelId, profile)
	key := genUserProfileKey(channelId, uid)
	bytes, _ := proto.Marshal(profile)
	err := cache.cmder.Set(ctx, key, bytes, time.Hour).Err()
	if err != nil {
		log.Errorf("AddGodUserProfile failed channelId:%d, err:%v", channelId, err)
		return err
	}
	return err
}

func (cache *Cache) GetUserProfile(ctx context.Context, channelId, uid uint32) (*pbApp.UserProfile, bool, error) {
	key := genUserProfileKey(channelId, uid)
	profileStr, err := cache.cmder.Get(ctx, key).Result()
	if err != nil {
		if redis.Nil == err {
			return nil, false, nil
		}
		return nil, false, err
	}

	profile := &pbApp.UserProfile{}
	value := []byte(profileStr)
	err = proto.Unmarshal(value, profile)
	return profile, true, err
}

func (cache *Cache) DelUserProfile(ctx context.Context, channelId, uid uint32) error {
	key := genUserProfileKey(channelId, uid)
	return cache.cmder.Del(ctx, key).Err()
}
