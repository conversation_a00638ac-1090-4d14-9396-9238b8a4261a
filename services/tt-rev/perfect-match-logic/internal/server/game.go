package server

import (
    "context"
    "fmt"

    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    protoGrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    channelPb "golang.52tt.com/protocol/app/channel"
    perfectCoupleMatchLogic "golang.52tt.com/protocol/app/perfect-couple-match-logic"
    "golang.52tt.com/protocol/common/status"
    channelschemeconfmgr "golang.52tt.com/protocol/services/channel-scheme-conf-mgr"
    channelapigo "golang.52tt.com/protocol/services/channelapi-go"
    channelmicPB "golang.52tt.com/protocol/services/channelmicsvr"
    perfect_match_game "golang.52tt.com/protocol/services/perfect-match-game"
)

func (svr *Server) GetPrefectCpGameInfo(ctx context.Context,
    req *perfectCoupleMatchLogic.GetPrefectCpGameInfoRequest) (*perfectCoupleMatchLogic.GetPrefectCpGameInfoResponse, error) {
    resp := &perfectCoupleMatchLogic.GetPrefectCpGameInfoResponse{}

    serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetPrefectCpGameInfo fail to ServiceInfoFromContext, req:%+v", req)
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    opUid := serviceInfo.UserID
    channelId := req.GetChannelId()

    if opUid == 0 || channelId == 0 {
        log.WarnWithCtx(ctx, "GetPrefectCpGameInfo req:%v invalid", req)
        return resp, nil
    }

    svrResp, err := svr.perfectGameCli.GetPrefectMatchGameInfo(ctx, &perfect_match_game.GetPrefectMatchGameInfoReq{
        ChannelId: channelId,
        Uid:       opUid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPrefectCpGameInfo GetPrefectMatchGameInfo fail,req:%v,err:%v", req, err)
        return resp, err
    }

    resp, err = svr.GetPerfectMatchGameRespSvr2Logic(ctx, svrResp)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPrefectCpGameInfo GetPerfectMatchGameRespSvr2Logic,req:%v", req)
        return resp, err
    }

    return resp, err
}

func (svr *Server) SetPrefectCpGamePhase(ctx context.Context,
    req *perfectCoupleMatchLogic.SetPrefectCpGamePhaseRequest) (*perfectCoupleMatchLogic.SetPrefectCpGamePhaseResponse, error) {

    resp := &perfectCoupleMatchLogic.SetPrefectCpGamePhaseResponse{}
    serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "SetPrefectCpGamePhase fail to ServiceInfoFromContext, req:%+v", req)
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    opUid := serviceInfo.UserID

    if opUid == 0 || req.GetChannelId() == 0 {
        log.WarnWithCtx(ctx, "SetPrefectCpGamePhase req:%v invalid", req)
        return resp, nil
    }

    // 检查房间主持人
    MicListResp, sErr := svr.channelMicCli.GetMicrList(ctx, req.GetChannelId(), opUid)
    if sErr != nil {
        log.ErrorWithCtx(ctx, "SetPrefectCpGamePhase fail to GetMicrList, req:%v, err:%v", req, sErr)
        return resp, sErr
    }

    micUidMap := make(map[uint32]uint32)
    for _, v := range MicListResp.GetAllMicList() {
        micUidMap[v.GetMicId()] = v.GetMicUid()
    }

    if micUidMap[0] != opUid && micUidMap[1] != opUid {
        // 非主持人无权限
        return resp, protocol.NewExactServerError(nil, status.ErrPerfectMatchGameCannotChangePhase, "只有主持人才能切换阶段噢")
    }

    _, err := svr.perfectGameCli.SetPrefectMatchGamePhase(ctx, &perfect_match_game.SetPrefectMatchGamePhaseReq{
        ChannelId:   req.GetChannelId(),
        GameId:      req.GetGameId(),
        TargetPhase: uint32(req.GetTargetPhase()),
        OpUid:       opUid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetPrefectCpGamePhase SetPrefectMatchGamePhase fail,req:%v,err%v", req, err)
        return resp, err
    }

    return resp, nil
}

func (svr *Server) BlowLight(ctx context.Context,
    req *perfectCoupleMatchLogic.BlowLightRequest) (*perfectCoupleMatchLogic.BlowLightResponse, error) {
    serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "BlowLight fail to ServiceInfoFromContext, req:%+v", req)
        return &perfectCoupleMatchLogic.BlowLightResponse{}, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    opUid := serviceInfo.UserID

    if opUid == 0 || req.GetChannelId() == 0 || req.GetTargetUid() == 0 || req.GetGameId() == 0 {
        log.WarnWithCtx(ctx, "BlowLight req:%v invalid", req)
        return &perfectCoupleMatchLogic.BlowLightResponse{}, nil
    }

    _, err := svr.perfectGameCli.BlowLight(ctx, &perfect_match_game.BlowLightReq{
        ChannelId: req.GetChannelId(),
        GameId:    req.GetGameId(),
        OpUid:     opUid,
        TargetUid: req.GetTargetUid(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BlowLight BlowLight fail,req:%v,err%v", req, err)
        return &perfectCoupleMatchLogic.BlowLightResponse{}, err
    }

    return &perfectCoupleMatchLogic.BlowLightResponse{}, nil
}

func (svr *Server) ChooseTheOne(ctx context.Context,
    req *perfectCoupleMatchLogic.ChooseTheOneRequest) (*perfectCoupleMatchLogic.ChooseTheOneResponse, error) {
    serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "BlowLight fail to ServiceInfoFromContext, req:%+v", req)
        return &perfectCoupleMatchLogic.ChooseTheOneResponse{}, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    opUid := serviceInfo.UserID

    if opUid == 0 || req.GetChannelId() == 0 || req.GetTargetUid() == 0 || req.GetGameId() == 0 {
        log.WarnWithCtx(ctx, "BlowLight req:%v invalid", req)
        return &perfectCoupleMatchLogic.ChooseTheOneResponse{}, nil
    }

    _, err := svr.perfectGameCli.ChooseTheOne(ctx, &perfect_match_game.ChooseTheOneReq{
        ChannelId: req.GetChannelId(),
        GameId:    req.GetGameId(),
        OpUid:     opUid,
        TargetUid: req.GetTargetUid(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BlowLight BlowLight fail,req:%v,err%v", req, err)
        return &perfectCoupleMatchLogic.ChooseTheOneResponse{}, err
    }

    return &perfectCoupleMatchLogic.ChooseTheOneResponse{}, nil
}

func (svr *Server) GetCoupleClues(ctx context.Context,
    req *perfectCoupleMatchLogic.GetCoupleCluesRequest) (*perfectCoupleMatchLogic.GetCoupleCluesResponse, error) {
    resp := &perfectCoupleMatchLogic.GetCoupleCluesResponse{
        CpCluesList: make([]*perfectCoupleMatchLogic.PlayerCluesInfo, 0),
    }

    serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetCoupleClues fail to ServiceInfoFromContext, req:%+v", req)
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    opUid := serviceInfo.UserID
    channelId := req.GetChannelId()
    targetUid := req.GetTargetUid()
    var isHost bool
    var err error

    if targetUid == 0 {
        isHost, err = svr.checkHostPermission(ctx, opUid, channelId)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetCoupleClues fail to checkHostPermission. req:%+v, err:%v", req, err)
            return resp, err
        }

        if !isHost {
            // 获取自己的
            targetUid = opUid
        }
    }

    cluesResp, err := svr.perfectGameCli.GetCoupleClues(ctx, &perfect_match_game.GetCoupleCluesReq{
        Uid:          targetUid,
        ChannelId:    channelId,
        GameId:       req.GetGameId(),
        GetAllPlayer: isHost, // 主持人获取所有的玩家的线索
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetCoupleClues fail to GetCoupleClues. req:%+v, err:%v", req, err)
        return resp, err
    }

    currPubCnt := cluesResp.GetCurrPublishCnt()

    if !isHost {
        resp.CpClues = fillCoupleCluesPb(cluesResp.GetCpClues(), currPubCnt)
    } else {
        // 主持人返回所有的玩家的线索
        for _, info := range cluesResp.GetCpCluesList() {
            resp.CpCluesList = append(resp.CpCluesList, fillCoupleCluesPb(info, currPubCnt))
        }
    }

    return resp, nil
}

func fillCoupleCluesPb(info *perfect_match_game.PlayerCluesInfo, currPubCnt uint32) *perfectCoupleMatchLogic.PlayerCluesInfo {
    list := make([]*perfectCoupleMatchLogic.CoupleClues, 0)
    for i, clues := range info.GetInfoList() {
        cpClues := &perfectCoupleMatchLogic.CoupleClues{
            CluesLen: uint32(len(clues.GetCluesText())), // 返回长度用于打马赛克
        }
        if uint32(i) < currPubCnt {
            // 公布的线索才返回
            cpClues.CluesText = clues.GetCluesText()
        }

        list = append(list, cpClues)
    }

    return &perfectCoupleMatchLogic.PlayerCluesInfo{
        Uid:      info.GetUid(),
        InfoList: list,
    }
}

func (svr *Server) GetMyQuestionnaire(ctx context.Context,
    req *perfectCoupleMatchLogic.GetMyQuestionnaireRequest) (*perfectCoupleMatchLogic.GetMyQuestionnaireResponse, error) {
    resp := &perfectCoupleMatchLogic.GetMyQuestionnaireResponse{
        QuestionnaireList: make([]*perfectCoupleMatchLogic.Questionnaire, 0),
    }

    serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetMyQuestionnaire fail to ServiceInfoFromContext, req:%+v", req)
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    opUid := serviceInfo.UserID
    channelId := req.GetChannelId()

    questionResp, err := svr.perfectGameCli.GetUserQuestionnaire(ctx, &perfect_match_game.GetUserQuestionnaireReq{
        Uid:       opUid,
        GameId:    req.GetGameId(),
        ChannelId: channelId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetMyQuestionnaire fail to GetUserQuestionnaire. req:%+v, err:%v", req, err)
        return resp, err
    }

    for _, info := range questionResp.GetQuestionnaireList() {
        resp.QuestionnaireList = append(resp.QuestionnaireList, &perfectCoupleMatchLogic.Questionnaire{
            Question: info.GetQuestion(),
            Answer:   info.GetAnswer(),
        })
    }

    return resp, nil
}

func (svr *Server) GetMyCluesProp(ctx context.Context,
    req *perfectCoupleMatchLogic.GetMyCluesPropRequest) (*perfectCoupleMatchLogic.GetMyCluesPropResponse, error) {
    resp := &perfectCoupleMatchLogic.GetMyCluesPropResponse{
        PropList: make([]*perfectCoupleMatchLogic.CluesProp, 0),
    }

    serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetMyCluesProp fail to ServiceInfoFromContext, req:%+v", req)
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    opUid := serviceInfo.UserID
    channelId := req.GetChannelId()

    propResp, err := svr.perfectGameCli.GetUserCluesProp(ctx, &perfect_match_game.GetUserCluesPropReq{
        Uid:       opUid,
        GameId:    req.GetGameId(),
        ChannelId: channelId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetMyCluesProp fail to GetUserCluesProp. req:%+v, err:%v", req, err)
        return resp, err
    }

    uidList := make([]uint32, 0)
    for _, info := range propResp.GetPropList() {
        if info.GetExtraClues().GetObjUid() == 0 {
            continue
        }
        uidList = append(uidList, info.GetExtraClues().GetObjUid())
    }

    userMap, err := svr.userProfileCli.BatchGetUserProfileV2(ctx, uidList, false /*取真实信息*/)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetMyCluesProp fail to BatchGetUserProfileV2. req:%+v, err:%v", req, err)
        return resp, err
    }

    for _, info := range propResp.GetPropList() {
        resp.PropList = append(resp.PropList, &perfectCoupleMatchLogic.CluesProp{
            PropId:   info.GetPropId(),
            Type:     perfectCoupleMatchLogic.CluesProp_CluesPropType(info.GetPropType()),
            PropDesc: info.GetPropDesc(),
            GiftId:   info.GetGiftId(),
            Status:   perfectCoupleMatchLogic.CluesProp_PropStatus(info.GetStatus()),
            ExtraClues: &perfectCoupleMatchLogic.ExtraClues{
                CluesText:      info.GetExtraClues().GetCluesText(),
                ObjUserProfile: userMap[info.GetExtraClues().GetObjUid()],
            },
        })
    }

    return resp, nil
}

func (svr *Server) UseCluesProp(ctx context.Context,
    req *perfectCoupleMatchLogic.UseCluesPropRequest) (*perfectCoupleMatchLogic.UseCluesPropResponse, error) {
    resp := &perfectCoupleMatchLogic.UseCluesPropResponse{}

    serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "UseCluesProp fail to ServiceInfoFromContext, req:%+v", req)
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    opUid := serviceInfo.UserID
    channelId := req.GetChannelId()

    // 检查玩家权限
    isPlayer, _, err := svr.checkGamePlayerMicPermission(ctx, opUid, channelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "UseCluesProp fail to checkGamePlayerMicPermission, req:%+v, err:%v", req, err)
        return resp, err
    }

    if !isPlayer {
        return resp, protocol.NewExactServerError(nil, status.ErrAccountPermissionDenied)
    }

    _, err = svr.perfectGameCli.UseCluesProp(ctx, &perfect_match_game.UseCluesPropReq{
        Uid:       opUid,
        ChannelId: channelId,
        GameId:    req.GetGameId(),
        PropId:    req.GetPropId(),
        TargetUid: req.GetTargetUid(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "UseCluesProp fail to UseCluesProp, req:%+v, err:%v", req, err)
        return resp, err
    }

    log.InfoWithCtx(ctx, "UseCluesProp req:%+v", req)
    return resp, nil
}

func (svr *Server) PublishClues(ctx context.Context,
    req *perfectCoupleMatchLogic.PublishCluesRequest) (*perfectCoupleMatchLogic.PublishCluesResponse, error) {
    resp := &perfectCoupleMatchLogic.PublishCluesResponse{}

    serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "PublishClues fail to ServiceInfoFromContext, req:%+v", req)
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    opUid := serviceInfo.UserID
    channelId := req.GetChannelId()

    // 检查主持人的权限
    ok, err := svr.checkHostPermission(ctx, opUid, channelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "PublishClues fail to checkHostPermission, req:%+v, err:%v", req, err)
        return resp, err
    }

    if !ok {
        log.ErrorWithCtx(ctx, "PublishClues fail to checkHostPermission, req:%+v, err:not host", req)
        return resp, protocol.NewExactServerError(nil, status.ErrAccountPermissionDenied)
    }

    _, err = svr.perfectGameCli.PublishClues(ctx, &perfect_match_game.PublishCluesReq{
        Uid:       opUid,
        ChannelId: channelId,
        GameId:    req.GetGameId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "PublishClues fail to PublishClues, req:%+v, err:%v", req, err)
        return resp, err
    }

    log.InfoWithCtx(ctx, "PublishClues req:%+v", req)
    return resp, nil
}

func (svr *Server) checkHostPermission(ctx context.Context, opUid, channelId uint32) (bool, error) {
    resp, err := svr.channelMicCli.GetMicrList(ctx, channelId, opUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkOpPermission GetChannelMicMode fail. opUid:%+v, channelId:%v, err:%v", opUid, channelId, err)
        return false, err
    }

    // 不是主持麦的人没有权限操作
    for _, info := range resp.GetAllMicList() {
        if info.GetMicId() == 1 && info.GetMicUid() == opUid {
            return true, nil
        }
    }

    return false, nil
}

func (svr *Server) checkGamePlayerMicPermission(ctx context.Context, uid, channelId uint32) (isPlayer bool, correctMicId uint32, err error) {
    gameInfoResp, err := svr.perfectGameCli.GetPrefectMatchGameInfo(ctx, &perfect_match_game.GetPrefectMatchGameInfoReq{
        ChannelId: channelId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "checkGamePlayerMicPermission fail to GetPrefectMatchGameInfo, uid:%d, channelId:%d, err:%v", uid, channelId, err)
        return false, 0, err
    }

    gameInfo := gameInfoResp.GetGameInfo()
    if gameInfo.GetPhase() == uint32(perfectCoupleMatchLogic.PerfectCpGamePhaseType_PERFECT_CP_GAME_PHASE_TYPE_CLOSE_UNSPECIFIED) {
        // 游戏未开始
        return false, 0, nil
    }

    for _, info := range gameInfo.GetPlayerList() {
        if uid == info.GetUid() {
            isPlayer = true
            correctMicId = info.GetMicId()
            break
        }
    }

    return
}

func (svr *Server) UserHoldMic(ctx context.Context, opUid, channelId, holdMicUid, micId, sex uint32) error {
    // 查看房间的类型消息
    objSimpleInfo, err := svr.channelCli.GetChannelSimpleInfo(ctx, 0, channelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "UserHoldMic fail to GetChannelSimpleInfo, opUid:%d, channelId:%d, holdMicUid:%d, micId:%d, err:%v",
            opUid, channelId, holdMicUid, micId, err)
        return err
    }

    var needKickMicUid uint32 // 可能需要踢下麦的人
    var MicStatus uint32      // 麦位状态
    var needChangeMicStatus uint32

    // 检查mvp麦位上是否有人
    MicListResp, err := svr.channelMicCli.GetMicrList(ctx, channelId, holdMicUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "ApplyToHoldMic fail to GetMicrList, opUid:%d, channelId:%d, holdMicUid:%d, micId:%d, err:%v",
            opUid, channelId, holdMicUid, micId, err)
        return err
    }

    micListInfo := MicListResp.GetAllMicList()
    for i := 0; i < len(micListInfo); i++ {
        if micListInfo[i].GetMicId() == micId {
            needKickMicUid = micListInfo[i].GetMicUid()
            MicStatus = micListInfo[i].GetMicState()
        }
    }
    if MicStatus == uint32(channelPb.MicrSpace_MIC_SPACE_DISABLE) {
        // 麦位不可锁，如果自动锁上了，那就再次开启麦位
        needChangeMicStatus = uint32(channelPb.MicrSpace_MIC_SPACE_NOMAL)
        _, err := svr.channelMicCli.SetChannelMicSpaceStatus(ctx, 0, holdMicUid, channelId, micId, needChangeMicStatus)
        if err != nil {
            log.ErrorWithCtx(ctx, "ApplyToHoldMic fail to GetChannelSimpleInfo, opUid:%d, channelId:%d, holdMicUid:%d, micId:%d, err:%v",
                opUid, channelId, holdMicUid, micId, err)
        }
    }

    if needKickMicUid != 0 && needKickMicUid == holdMicUid {
        return nil //已在麦上
    }

    _, err = svr.channelMicCli.SimpleHoldMicrSpace(ctx, 0, &channelmicPB.SimpleHoldMicrSpaceReq{
        ChannelId:        channelId,
        Uid:              holdMicUid,
        MicPosId:         micId,
        IsForce:          true, //强制用户上麦，如果麦上有人，强制该用户下麦
        ChannelDisplayId: objSimpleInfo.GetDisplayId(),
        ChannelType:      objSimpleInfo.GetChannelType(),
        UserSex:          sex,
        OpUid:            0,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "ApplyToHoldMic fail to SimpleHoldMicrSpace, opUid:%d, channelId:%d, holdMicUid:%d, micId:%d, err:%v",
            opUid, channelId, holdMicUid, micId, err)
        return err
    }

    // 上麦push
    err = svr.ChannelApiCli.HoldMicPush(ctx, channelId, holdMicUid, uint32(channelapigo.HoldMicPushSource_HoldMicPushSource_Default))
    if err != nil {
        log.ErrorWithCtx(ctx, "ApplyToHoldMic fail to HoldMicPush, opUid:%d, channelId:%d, holdMicUid:%d, micId:%d, err:%v",
            opUid, channelId, holdMicUid, micId, err)
    }

    return nil
}

func (svr *Server) ApplyToHoldMic(ctx context.Context,
    req *perfectCoupleMatchLogic.ApplyToHoldMicRequest) (*perfectCoupleMatchLogic.ApplyToHoldMicResponse, error) {

    resp := &perfectCoupleMatchLogic.ApplyToHoldMicResponse{}
    serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "ApplyToHoldMic fail to ServiceInfoFromContext, req:%+v", req)
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    opUid := serviceInfo.UserID
    channelId := req.GetChannelId()
    micId := req.GetMicId()
    holdMicUid := opUid

    if micId == 1 {
        // 该接口不能上主持麦
        log.ErrorWithCtx(ctx, "ApplyToHoldMic fail, req:%+v, err:cannot hold host mic", req)
        return resp, protocol.NewExactServerError(nil, status.ErrPerfectMatchGameHoldMicFail, "不能上主持麦")
    }

    // 检查房间玩法模板
    SchemeInfoResp, sErr := svr.channelSchemeCli.GetCurChannelSchemeInfo(ctx, channelId, 0)
    if sErr != nil {
        log.ErrorWithCtx(ctx, "ApplyToHoldMic fail to GetCurChannelSchemeInfo, req:%+v, err:%v", req, sErr)
        return resp, sErr
    }

    log.Infof("ApplyToHoldMic, %d, %d", SchemeInfoResp.GetSchemeInfo().GetSchemeSvrDetailType(),
        uint32(channelschemeconfmgr.SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_PERFECT_COUPLE_GAME))
    if SchemeInfoResp.GetSchemeInfo().GetSchemeSvrDetailType() != uint32(channelschemeconfmgr.SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_PERFECT_COUPLE_GAME) {
        // 当前房间玩法模板不是天配房间玩法
        log.ErrorWithCtx(ctx, "ApplyToHoldMic fail, req:%+v, err:SchemeSvrDetailType error", req)
        return resp, protocol.NewExactServerError(nil, status.ErrPerfectMatchGameNotStart)
    }

    // 抱用户上麦
    if req.GetTargetUid() > 0 {
        holdMicUid = req.GetTargetUid()
        // 检查主持人的权限
        ok, err := svr.checkHostPermission(ctx, opUid, channelId)
        if err != nil {
            log.ErrorWithCtx(ctx, "ApplyToHoldMic fail to checkHostPermission, req:%+v, err:%v", req, err)
            return resp, err
        }

        if !ok {
            log.ErrorWithCtx(ctx, "ApplyToHoldMic fail to checkHostPermission, req:%+v, err:not host", req)
            return resp, protocol.NewExactServerError(nil, status.ErrAccountPermissionDenied)
        }
    }

    // 检查是否能上对应玩家麦位
    isPlayer, correctMicId, err := svr.checkGamePlayerMicPermission(ctx, holdMicUid, channelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "ApplyToHoldMic fail to checkGamePlayerMicPermission, req:%+v, err:%v", req, err)
        return resp, err
    }

    if !isPlayer {
        return resp, protocol.NewExactServerError(nil, status.ErrPerfectMatchGameHoldMicFail, "不支持非玩家用户上麦")
    }

    if correctMicId != micId {
        var displayMicId uint32
        if correctMicId > 0 {
            displayMicId = correctMicId - 1
        }

        msg := fmt.Sprintf("请上您自己的%d号麦位继续进行游戏", displayMicId)
        if opUid != holdMicUid {
            msg = fmt.Sprintf("请抱用户上%d号麦位继续进行游戏", displayMicId)
        }
        return resp, protocol.NewExactServerError(nil, status.ErrPerfectMatchGameHoldMicFail, msg)
    }

    // 上麦者基本信息
    objOpUserResp, err := svr.userProfileCli.GetUserProfile(ctx, holdMicUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "ApplyToHoldMic fail to GetUserProfile, req:%+v, err:%v", req, err)
        return resp, err
    }

    if objOpUserResp.GetPrivilege().GetType() == uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
        // 神秘人不准上玩家麦
        log.ErrorWithCtx(ctx, "ApplyToHoldMic fail, req:%+v, err:ukw user cannot hold mic", req)
        return resp, protocol.NewExactServerError(nil, status.ErrPerfectMatchGameHoldMicFail, "神秘人无法参与游戏")
    }

    err2 := svr.UserHoldMic(ctx, opUid, channelId, holdMicUid, micId, objOpUserResp.GetSex())
    if err2 != nil {
        log.ErrorWithCtx(ctx, "ApplyToHoldMic fail to UserHoldMic, req:%+v, err:%v", req, err2)
        return resp, err2
    }

    log.InfoWithCtx(ctx, "ApplyToHoldMic req:%+v", req)
    return resp, nil
}
