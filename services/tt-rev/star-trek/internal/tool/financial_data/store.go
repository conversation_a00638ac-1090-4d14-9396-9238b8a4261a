package main

import (
	"context"
	"fmt"
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/config"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/tt-rev/star-trek/internal/mysql"
	"time"
)

func GenMonthRemainTblIdx(t time.Time) string {
	return fmt.Sprintf("%04d%02d", t.Year(), t.Month())
}

type Store struct {
	readonlyDb *sqlx.DB
}

func NewMysql(rc *config.MysqlConfig) (*Store, error) {
	mysqlReadonlyDb, err := sqlx.Open("mysql", rc.ConnectionString())
	if err != nil {
		log.Errorf("Failed to create mysql %v", err)
		return nil, err
	}

	if rc.MaxIdleConns > 0 {
		mysqlReadonlyDb.SetMaxIdleConns(rc.MaxIdleConns)
	}
	if rc.MaxOpenConns > 0 {
		mysqlReadonlyDb.SetMaxOpenConns(rc.MaxOpenConns)
	}
	mysqlReadonlyDb.SetConnMaxLifetime(time.Minute * 5)

	st := &Store{
		readonlyDb: mysqlReadonlyDb,
	}

	return st, nil
}

func (s *Store) GetRoundRecords(ctx context.Context, beginTime, endTime time.Time) ([]*mysql.RoundInfo, error) {
	list := make([]*mysql.RoundInfo, 0)
	err := s.readonlyDb.SelectContext(ctx, &list,
		"select round_id, status, begin_time, end_time from star_trek_round_info where (begin_time>=? and begin_time<?) or (end_time>=? and end_time<?)",
		beginTime, endTime, beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRoundRecords fail. begin:%v, end:%v, err:%v", beginTime, endTime, err)
		return list, err
	}

	return list, nil
}

type RoundAward struct {
	RoundId      uint32 `db:"round_id"`
	BingoWorth   uint32 `db:"bingo_worth"`    // 开奖礼物价值
	CommAwardCnt uint32 `db:"comm_award_cnt"` // 发放装扮碎片数
}

func (r *RoundAward) Get() *RoundAward {
	if r == nil {
		return &RoundAward{}
	}

	return r
}

func (s *Store) GetRoundAwardData(ctx context.Context, beginTime, endTime time.Time) (map[uint32]*RoundAward, error) {
	list := make([]*RoundAward, 0)
	monthTime := time.Date(beginTime.Year(), beginTime.Month(), 1, 0, 0, 0, 0, time.Local)

	query := "select round_id, sum(if(bingo_type>0, pack_worth, 0)) as bingo_worth, sum(if(bingo_type=0, pack_amount, 0)) as comm_award_cnt " +
		"from %s where award_time>=? and award_time<? group by round_id"

	err := s.readonlyDb.SelectContext(ctx, &list, fmt.Sprintf(query, mysql.GenAwardRecordTblName(monthTime)), beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRoundAwardData fail. begin:%v, end:%v, err:%v", beginTime, endTime, err)
	}

	// 查上月
	monthTime = monthTime.AddDate(0, -1, 0)
	err = s.readonlyDb.SelectContext(ctx, &list, fmt.Sprintf(query, mysql.GenAwardRecordTblName(monthTime)), beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRoundAwardData fail. begin:%v, end:%v, err:%v", beginTime, endTime, err)
	}

	out := make(map[uint32]*RoundAward)
	for _, info := range list {
		if _, ok := out[info.RoundId]; !ok {
			out[info.RoundId] = &RoundAward{RoundId: info.RoundId}
		}

		out[info.RoundId].BingoWorth += info.BingoWorth
		out[info.RoundId].CommAwardCnt += info.CommAwardCnt
	}

	return out, nil
}

type RoundCost struct {
	RoundId uint32 `db:"round_id"`
	CostVal uint32 `db:"cost_val"` // 投入值
}

func (r *RoundCost) Get() *RoundCost {
	if r == nil {
		return &RoundCost{}
	}

	return r
}

func (s *Store) GetRoundCostData(ctx context.Context, beginTime, endTime time.Time) (map[uint32]*RoundCost, error) {
	list := make([]*RoundCost, 0)
	monthTime := time.Date(beginTime.Year(), beginTime.Month(), 1, 0, 0, 0, 0, time.Local)

	query := "select round_id, sum(cost_gift_price*cost_gift_num) as cost_val from %s " +
		"where outside_time>=? and outside_time<? and status>? and status<? group by round_id"
	err := s.readonlyDb.SelectContext(ctx, &list, fmt.Sprintf(query, mysql.GenInvestLogTblName(monthTime)),
		beginTime, endTime, mysql.InvestCostPrepare, mysql.InvestCostFailAndRollback)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRoundCostData fail. begin:%v, end:%v, err:%v", beginTime, endTime, err)
	}

	// 查上月
	monthTime = monthTime.AddDate(0, -1, 0)
	err = s.readonlyDb.SelectContext(ctx, &list, fmt.Sprintf(query, mysql.GenInvestLogTblName(monthTime)),
		beginTime, endTime, mysql.InvestCostPrepare, mysql.InvestCostFailAndRollback)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRoundCostData fail. begin:%v, end:%v, err:%v", beginTime, endTime, err)
	}

	out := make(map[uint32]*RoundCost)
	for _, info := range list {
		if _, ok := out[info.RoundId]; !ok {
			out[info.RoundId] = &RoundCost{RoundId: info.RoundId}
		}

		out[info.RoundId].CostVal += info.CostVal
	}

	return out, nil
}

func (s *Store) GetFailRoundRollbackVal(ctx context.Context, roundId uint32, roundTime time.Time) (int64, error) {
	val := int64(0)

	query := "select sum(cost_gift_price*cost_gift_num) from %s where round_id=? and status>=? and status<=?"
	err := s.readonlyDb.GetContext(ctx, &val, fmt.Sprintf(query, mysql.GenInvestLogTblName(roundTime)),
		roundId, mysql.InvestCostWaitRollback, mysql.InvestCostRollbackDone)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFailRoundRollbackVal fail. roundId:%v, roundTime:%v, err:%v", roundId, roundTime, err)
		return 0, err
	}

	return val, nil
}
