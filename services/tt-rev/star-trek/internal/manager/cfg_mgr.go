package manager

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/star-trek"
	"golang.52tt.com/services/tt-rev/star-trek/internal/mysql"
	"strconv"
	"time"
)

type DayTimeRange struct {
	Selected bool
	DayBegin uint32
	DayEnd   uint32
	WDayList []uint32
}

func (m *Mgr) fillActivityPb(activityTime *mysql.ActivityTime, prize []*mysql.Prize) *pb.StarTrekConf {
	if activityTime == nil || prize == nil {
		return nil
	}

	prizes := make([]*pb.StarTrekPrizeInfo, 0)
	for _, v := range prize {
		prizes = append(prizes, &pb.StarTrekPrizeInfo{
			BgId:      v.BgId,
			Weight:    v.Weight,
			GiftId:    v.GiftId,
			GiftPrice: v.GiftPrice,
			GiftName:  v.GiftName,
			GiftPic:   v.GiftPic,
			GiftNum:   v.GiftNum,
		})
	}

	dayTime := make([]*DayTimeRange, 0)
	err := json.Unmarshal([]byte(activityTime.TimeRange), &dayTime)
	if err != nil {
		log.Errorf("fillActivityPb json.Unmarshal fail,err:%v, TimeRange:%v", err, activityTime.TimeRange)
		return nil
	}

	timeList := make([]*pb.DayTimeRangeInfo, 0)
	for _, v := range dayTime {
		timeList = append(timeList, &pb.DayTimeRangeInfo{
			Selected: v.Selected,
			DayBegin: v.DayBegin,
			DayEnd:   v.DayEnd,
			WDayList: v.WDayList,
		})
	}

	return &pb.StarTrekConf{
		ConfId:        activityTime.Id,
		ActivityName:  activityTime.ActivityName,
		ActivityBegin: uint32(activityTime.ActivityBegin.Unix()),
		ActivityEnd:   uint32(activityTime.ActivityEnd.Unix()),
		TimeList:      timeList,
		PrizeList:     prizes,
	}
}

func (m *Mgr) fillPrizeStore(activityId uint32, prize *pb.StarTrekPrizeInfo) *mysql.Prize {
	if prize == nil {
		return nil
	}

	return &mysql.Prize{
		ActivityId: activityId,
		BgId:       prize.GetBgId(),
		GiftId:     prize.GetGiftId(),
		GiftPrice:  prize.GetGiftPrice(),
		GiftNum:    prize.GetGiftNum(),
		GiftName:   prize.GetGiftName(),
		GiftPic:    prize.GetGiftPic(),
		Weight:     prize.GetWeight(),
	}
}

func (m *Mgr) fillActivityTimeStore(activityTime *pb.StarTrekConf) *mysql.ActivityTime {
	if activityTime == nil {
		return nil
	}

	dayTime := make([]*DayTimeRange, 0)
	for _, v := range activityTime.GetTimeList() {
		dayTime = append(dayTime, &DayTimeRange{
			Selected: v.GetSelected(),
			DayBegin: v.GetDayBegin(),
			DayEnd:   v.GetDayEnd(),
			WDayList: v.GetWDayList(),
		})
	}

	dayTimeMarshal, err := json.Marshal(dayTime)
	if err != nil {
		log.Errorf("fillActivityTimeStore json.Marshal fail,err:%v, dayTime:%v", err, dayTime)
		return nil
	}

	return &mysql.ActivityTime{
		Id:            0,
		ActivityName:  activityTime.GetActivityName(),
		ActivityBegin: time.Unix(int64(activityTime.GetActivityBegin()), 0),
		ActivityEnd:   time.Unix(int64(activityTime.GetActivityEnd()), 0),
		TimeRange:     string(dayTimeMarshal),
	}
}

// 活动时间检查
func (m *Mgr) CheckActTime(ctx context.Context, confId, actBegin, actEnd uint32, delFlag bool) (bool, bool, error) {
	if !m.bc.GetTesting() {
		// 限制进行中的活动不可修改
		curRound, exist, err := m.store.GetRoundInfoByStatus(ctx, mysql.RoundStatusPending)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckActTime GetRoundInfoByStatus fail, err:%v", err)
			return false, false, err
		}

		if exist && curRound.ActId == confId {
			log.ErrorWithCtx(ctx, "CheckActTime cannot edit an ongoing activity, err:%v", err)
			return false, false, nil
		}
	}

	if delFlag {
		return true, false, nil
	}

	// 冲突检查
	actTimes, err := m.store.GetActivityTimeForCheck(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckActTime fail to GetActivityTimeForCheck,err:%v", err)
		return true, false, err
	}
	for _, v := range actTimes {
		if actBegin > uint32(v.ActivityEnd.Unix()) ||
			actEnd < uint32(v.ActivityBegin.Unix()) {
			continue

		} else if v.Id == confId {
			continue

		} else {
			log.ErrorWithCtx(ctx, "CheckActTime, time conflict, err:%v", err)
			return true, false, nil
		}
	}

	return true, true, nil
}

// 开启日时间段检查
func (m *Mgr) CheckDayTimeRange(ctx context.Context, actEnd uint32, timeRange []*pb.DayTimeRangeInfo) (bool, bool, error) {
	dayCheckMap := make(map[uint32]uint32)
	for _, v := range timeRange {
		if !v.Selected {
			continue
		}

		// 开启日结束时间不能大于活动结束时间
		actEnd := time.Unix(int64(actEnd), 0)
		endDay := time.Date(actEnd.Year(), actEnd.Month(), actEnd.Day(), 0, 0, int(v.GetDayEnd()), 0, actEnd.Location())
		if endDay.After(actEnd) {
			return false, false, nil
		}

		for _, day := range v.GetWDayList() {
			// 开启日时间检查
			_, ok := dayCheckMap[day]
			if ok {
				log.ErrorWithCtx(ctx, "CheckDayTimeRange timeRange:%v", timeRange)
				return true, false, nil
			} else {
				dayCheckMap[day] = 1
			}
		}
	}

	return true, true, nil
}

// 新增活动配置
func (m *Mgr) SetStarTrekConf(ctx context.Context, in *pb.SetStarTrekConfReq) (out *pb.SetStarTrekConfResp, err error) {
	out = &pb.SetStarTrekConfResp{}
	confId := uint32(0)
	activityTime := m.fillActivityTimeStore(in.GetConf())

	if in.GetConf().GetActivityBegin() >= in.GetConf().GetActivityEnd() {
		return out, protocol.NewExactServerError(nil, status.ErrParam, "活动时间配置错误！")
	}

	// 星际礼物价值check
	for _, v := range in.GetConf().GetPrizeList() {
		if v.GetGiftPrice() < m.bc.GetAwardPriceFloor() {
			return out, protocol.NewExactServerError(nil, status.ErrParam, fmt.Sprintf("星际礼物奖励价值不能低于%dT豆", m.bc.GetAwardPriceFloor()))
		}
	}

	// 活动时间冲突检查
	ok, noConflict, err := m.CheckActTime(ctx, confId, in.GetConf().GetActivityBegin(), in.GetConf().GetActivityEnd(), false)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetStarTrekConf m.CheckActTime fail, err:%v", err)
		return out, err
	}
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrParam, "活动正在进行中，不可编辑！")
	}
	if !noConflict {
		return out, protocol.NewExactServerError(nil, status.ErrParam, "活动时间冲突！")
	}

	// 开启时间段冲突检查
	ok, noConflict, err = m.CheckDayTimeRange(ctx, in.GetConf().ActivityEnd, in.GetConf().GetTimeList())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetStarTrekConf m.CheckDayTimeRange err:%v", err)
		return out, err
	}
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrParam, "开启日结束时间不可大于活动结束时间！")
	}
	if !noConflict {
		return out, protocol.NewExactServerError(nil, status.ErrParam, "开启日时间段配置重复！")
	}

	if time.Now().After(activityTime.ActivityEnd) {
		activityTime.Status = uint32(pb.StarTrekStatus_Finished)
	}

	err = m.store.Transaction(ctx, func(tx *sql.Tx) error {
		err = m.store.InsertActivityRecord(ctx, tx, activityTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetStarTrekConf fail to InsertActivityRecord, in:%v, err:%v", in, err)
			return err
		}

		confId, err = m.store.GetMaxIdFromActivity(ctx, tx)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetStarTrekConf fail to GetMaxIdFromActivity, in:%v, err:%v", in, err)
			return err
		}

		return nil
	})
	if err != nil {
		return out, err
	}

	// 清奖池
	err = m.store.DelPrizesByActivityId(ctx, nil, confId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetStarTrekConf fail to DelPrizesByActivityId, in:%v, err:%v", in, err)
		return out, err
	}

	prizes := make([]*mysql.Prize, 0)
	for _, v := range in.GetConf().GetPrizeList() {
		if v == nil {
			continue
		}
		prizes = append(prizes, m.fillPrizeStore(confId, v))
	}

	for _, v := range prizes {
		err = m.store.InsertOrUpdatePrizeConf(ctx, nil, v)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetStarTrekConf fail InsertOrUpdatePrizeConf, in:%v, err:%v", in, err)
			return out, err
		}
	}

	err = m.redisCache.DelActivityConfCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetStarTrekConf fail DelActivityConfCache, err:%v", err)
	}

	return out, err
}

func (m *Mgr) UpdateStarTrekConf(ctx context.Context, in *pb.UpdateStarTrekConfReq) (out *pb.UpdateStarTrekConfResp, err error) {
	out = &pb.UpdateStarTrekConfResp{}
	config := in.GetConf()
	activityTime := m.fillActivityTimeStore(config)

	// 活动时间冲突检查
	ok, noConflict, err := m.CheckActTime(ctx, config.GetConfId(), config.GetActivityBegin(), config.GetActivityEnd(), false)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetStarTrekConf m.CheckActTime fail, err:%v", err)
		return out, err
	}
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrParam, "活动正在进行中，不可编辑！")
	}
	if !noConflict {
		return out, protocol.NewExactServerError(nil, status.ErrParam, "活动时间冲突！")
	}

	// 开启时间段冲突检查
	ok, noConflict, err = m.CheckDayTimeRange(ctx, config.ActivityEnd, config.GetTimeList())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetStarTrekConf m.CheckDayTimeRange err:%v", err)
		return out, err
	}
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrParam, "开启日结束时间不可大于活动结束时间！")
	}
	if !noConflict {
		return out, protocol.NewExactServerError(nil, status.ErrParam, "开启日时间段配置重复！")
	}

	if time.Now().After(activityTime.ActivityEnd) {
		activityTime.Status = uint32(pb.StarTrekStatus_Finished)
	}

	// 星际礼物价值check
	for _, v := range config.GetPrizeList() {
		if v.GetGiftPrice() < m.bc.GetAwardPriceFloor() {
			return out, protocol.NewExactServerError(nil, status.ErrParam, fmt.Sprintf("星际礼物奖励价值不能低于%dT豆", m.bc.GetAwardPriceFloor()))
		}
	}

	err = m.store.UpdateActivityTimeById(ctx, activityTime, config.GetConfId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateStarTrekConf fail to UpdateActivityTimeById, in:%v, err:%v", in, err)
		return out, err
	}

	// 更新奖池
	newPrizes := make([]*mysql.Prize, 0)
	for _, v := range config.GetPrizeList() {
		if v == nil {
			continue
		}
		newPrizes = append(newPrizes, m.fillPrizeStore(config.GetConfId(), v))
	}

	err = m.store.Transaction(ctx, func(tx *sql.Tx) error {
		err = m.store.DelPrizesByActivityId(ctx, tx, config.GetConfId())
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateStarTrekConf fail to DelPrizesByActivityId, in:%v, err:%v", in, err)
			return err
		}

		for _, v := range newPrizes {
			err = m.store.InsertOrUpdatePrizeConf(ctx, tx, v)
			if err != nil {
				log.ErrorWithCtx(ctx, "SetStarTrekConf fail InsertOrUpdatePrizeConf, in:%v, err:%v", in, err)
				return err
			}
		}
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateStarTrekConf fail,err:%v", err)
	}

	err = m.redisCache.DelActivityConfCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckActivityUpdate fail DelActivityConfCache, err:%v", err)
	}

	return out, err
}

// 删除活动配置
func (m *Mgr) DelActivityByActivityId(ctx context.Context, id uint32) (out *pb.DelStarTrekConfResp, err error) {
	out = &pb.DelStarTrekConfResp{}
	if id == 0 {
		return out, nil
	}

	// 删除检查
	ok, _, err := m.CheckActTime(ctx, id, 0, 0, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelActivityByActivityId m.CheckActTime fail, err:%v", err)
		return out, err
	}
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrParam, "活动正在进行中，不可删除！")
	}

	err = m.store.DelActivityRecordById(ctx, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelActivityByActivityId fail DelActivityRecordById, id:%d, err:%v", id, err)
		return out, err
	}

	err = m.store.DelPrizesByActivityId(ctx, nil, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelActivityByActivityId fail DelPrizesByActivityId, id:%d, err:%v", id, err)
		return out, err
	}

	err = m.redisCache.DelActivityConfCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckActivityUpdate fail DelActivityConfCache, err:%v", err)
	}

	return out, nil
}

func (m *Mgr) GetStarTrekConf(ctx context.Context, in *pb.GetStarTrekConfReq) (out *pb.GetStarTrekConfResp, err error) {
	out = &pb.GetStarTrekConfResp{}
	mStatus := uint32(in.GetStatus())
	limit := in.GetLimit()
	offset := in.GetOffset()

	cnt, err := m.store.GetActivityCntByStatus(ctx, mStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetStarTrekConf fail GetActivityCntByStatus, in:%v, err:%v", in, err)
		return out, err
	}

	out.Total = cnt

	activityTimes, err := m.store.GetActivityRecordsByStatus(ctx, mStatus, limit, offset)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetStarTrekConf fail GetActivityCntByStatus, in:%v, err:%v", in, err)
		return out, err
	}

	for _, v := range activityTimes {
		if v == nil {
			continue
		}
		prizes, err := m.store.GetPrizesByActivityId(ctx, v.Id)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetStarTrekConf fail GetPrizesByActivityId, in:%v, err:%v", in, err)
			return out, err
		}

		out.ConfList = append(out.ConfList, m.fillActivityPb(v, prizes))
	}

	return out, nil
}

func (m *Mgr) GetActivityById(ctx context.Context, ids []uint32) (out *pb.GetStarTrekConfByIdResp, err error) {
	out = &pb.GetStarTrekConfByIdResp{}
	if len(ids) == 0 {
		return out, nil
	}

	infos, err := m.store.GetActivityRecordsByIds(ctx, ids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetStarTrekConf fail GetActivityRecordsByIds, ids:%v, err:%v", ids, err)
		return out, err
	}

	for _, v := range infos {
		prizes, err := m.store.GetPrizesByActivityId(ctx, v.Id)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetStarTrekConf fail GetPrizesByActivityId, ids:%v, err:%v", ids, err)
			return out, err
		}

		out.ConfList = append(out.ConfList, m.fillActivityPb(v, prizes))
	}

	return out, nil
}

func (m *Mgr) fillSupplyInfoStore(supply *pb.SupplyConf) *mysql.SupplyInfo {
	if supply == nil {
		return nil
	}

	return &mysql.SupplyInfo{
		SupplyId:  supply.GetId(),
		GiftId:    fmt.Sprintf("%d", supply.GetGiftId()),
		GiftType:  supply.GetGiftType(),
		GiftPrice: supply.GetGiftPrice(),
		GiftName:  supply.GetGiftName(),
		GiftPic:   supply.GetGiftPic(),
	}
}

func (m *Mgr) fillSupplyInfoPb(supply *mysql.SupplyInfo, userItemId, itemNum, finTime uint32) *pb.SupplyConf {
	if supply == nil {
		return nil
	}

	giftId, _ := strconv.ParseUint(supply.GiftId, 10, 32)

	return &pb.SupplyConf{
		Id:        supply.SupplyId,
		GiftId:    uint32(giftId),
		GiftType:  supply.GiftType,
		GiftName:  supply.GiftName,
		GiftPrice: supply.GiftPrice,
		GiftPic:   supply.GiftPic,
	}
}

// 批量新增补给配置
func (m *Mgr) SetStarTrekSupply(ctx context.Context, in *pb.SetStarTrekSupplyReq) (out *pb.SetStarTrekSupplyResp, err error) {
	out = &pb.SetStarTrekSupplyResp{}
	if in == nil {
		log.ErrorWithCtx(ctx, "SetStarTrekSupply fail, in:%v, err Param", in)
		return out, protocol.NewExactServerError(nil, status.ErrParam, "配置不能为空")
	}

	// 补给校验
	for _, v := range in.GetSupplyList() {
		if v.GetGiftPrice() <= 0 || v.GetGiftPrice() > m.bc.GetSupplyPriceLimit() {
			return out, protocol.NewExactServerError(nil, status.ErrParam, fmt.Sprintf("补给配置价格不能为0或超过 %d T豆", m.bc.GetSupplyPriceLimit()))
		}
	}

	supplys := in.GetSupplyList()
	supplysStore := make([]*mysql.SupplyInfo, 0)
	for _, v := range supplys {
		if v == nil {
			continue
		}
		supplysStore = append(supplysStore, m.fillSupplyInfoStore(v))
	}

	err = m.store.InsertSupplyConfs(ctx, supplysStore)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetStarTrekSupply fail, in:%v, err:%v", in, err)
		return out, err
	}

	go func() {
		sups := make([]*mysql.SupplyInfo, 0)
		_ = m.SetSupplyConfCache(context.Background(), sups)
	}()

	return out, nil
}

// 获取补给配置
func (m *Mgr) GetSupplys(ctx context.Context, in *pb.GetStarTrekSupplyReq) (out *pb.GetStarTrekSupplyResp, err error) {
	out = &pb.GetStarTrekSupplyResp{}
	supplys := make([]*mysql.SupplyInfo, 0)

	// 从缓存查
	sups, err := m.GetSupplyConfFromCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSupplys fail GetSupplyConfFromCache, err:%v", err)
		return out, err
	}

	for _, v := range sups {
		supplys = append(supplys, &mysql.SupplyInfo{
			SupplyId:  v.Id,
			GiftId:    fmt.Sprintf("%d", v.GetGiftId()),
			GiftType:  v.GetGiftType(),
			GiftPrice: v.GetGiftPrice(),
			GiftName:  v.GetGiftName(),
			GiftPic:   v.GetGiftPic(),
		})
	}

	for _, v := range supplys {
		if v == nil {
			continue
		}
		out.SupplyList = append(out.SupplyList, m.fillSupplyInfoPb(v, 0, 0, 0))
	}
	out.DailyLimit = m.userDailyLimit

	return out, nil
}

// 删除补给配置
func (m *Mgr) DelSupplyBySupplyId(ctx context.Context, supplyId uint32) error {
	err := m.store.DelSupplysBySupplyId(ctx, supplyId)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelSupplyBySupplyId fail DelSupplyBySupplyId, supplyId:%d,err:%v", supplyId, err)
		return err
	}

	go func() {
		sups := make([]*mysql.SupplyInfo, 0)
		_ = m.SetSupplyConfCache(context.Background(), sups)
	}()

	return nil
}

func (m *Mgr) SortSupplyList(sups []*mysql.SupplyInfo) []*mysql.SupplyInfo {
	retSup := make([]*mysql.SupplyInfo, 0)
	frags := make([]*mysql.SupplyInfo, 0)
	presents := make([]*mysql.SupplyInfo, 0)

	// 排序：碎片类型 > 价值更低 > 更先配置
	for _, v := range sups {
		if v.GiftType == uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) {
			frags = append(frags, v)
		} else {
			presents = append(presents, v)
		}
	}
	// 价值排序
	frags = bubbleSortAsc(frags)
	presents = bubbleSortAsc(presents)

	retSup = append(retSup, frags...)
	retSup = append(retSup, presents...)

	return retSup
}

func bubbleSortAsc(arr []*mysql.SupplyInfo) []*mysql.SupplyInfo {
	length := len(arr)
	for i := 0; i < length; i++ {
		for j := 0; j < length-1-i; j++ {
			if arr[j].GiftPrice > arr[j+1].GiftPrice {
				arr[j], arr[j+1] = arr[j+1], arr[j]
			}
		}
	}
	return arr
}

func (m *Mgr) SetSupplyConfCache(ctx context.Context, sups []*mysql.SupplyInfo) error {
	var err error

	if len(sups) == 0 {
		sups, err = m.store.GetAllSupplys(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetSupplyConfCache fail GetAllSupplys err :%v", err)
			return err
		}
	}

	err = m.redisCache.SetSupplyConfCache(ctx, m.SortSupplyList(sups))
	if err != nil {
		log.ErrorWithCtx(ctx, "SetSupplyConfCache fail redisSet err :%v", err)
		return err
	}

	return nil
}

// 补给配置缓存
func (m *Mgr) GetSupplyConfFromCache(ctx context.Context) ([]*pb.SupplyConf, error) {
	sups := make([]*pb.SupplyConf, 0)

	exist, supsCache, err := m.redisCache.GetSupplyConfCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSupplyConfFromCache fail GetSupplyConfCache, err:%v", err)
		return sups, err
	}

	if exist {
		for _, v := range supsCache {
			giftId, err := strconv.ParseUint(v.GiftId, 10, 32)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetSupplyConfFromCache fail ParseUint, err%v", err)
				continue
			}
			sups = append(sups, &pb.SupplyConf{
				Id:        v.SupplyId,
				GiftId:    uint32(giftId),
				GiftType:  v.GiftType,
				GiftPrice: v.GiftPrice,
				GiftName:  v.GiftName,
				GiftPic:   v.GiftPic,
			})
		}

	} else {
		// get from mysql
		supplys, err := m.store.GetAllSupplys(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetSupplys fail GetAllSupplys err :%v", err)
			return sups, err
		}

		supplys = m.SortSupplyList(supplys)

		for _, v := range supplys {
			giftId, err := strconv.ParseUint(v.GiftId, 10, 32)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetSupplyConfFromCache fail ParseUint, err%v", err)
				continue
			}
			sups = append(sups, &pb.SupplyConf{
				Id:        v.SupplyId,
				GiftId:    uint32(giftId),
				GiftType:  v.GiftType,
				GiftPrice: v.GiftPrice,
				GiftName:  v.GiftName,
				GiftPic:   v.GiftPic,
			})
		}

		go func() {
			_ = m.SetSupplyConfCache(context.Background(), supplys)
		}()
	}

	return sups, nil
}

func (m *Mgr) SupplyCheck(ctx context.Context, sups []*pb.CostSupplyInfo) (ok bool, mapSup map[uint32]map[uint32]*pb.SupplyConf, err error) {
	mapSup = make(map[uint32]map[uint32]*pb.SupplyConf)

	reSups, err := m.GetSupplyConfFromCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "SupplyCheck fail GetSupplyConfFromCache, err:%v", err)
		return false, mapSup, err
	}

	mapGift := make(map[uint32]*pb.SupplyConf)
	mapFrag := make(map[uint32]*pb.SupplyConf)

	for _, v := range reSups {
		if v.GetGiftType() == uint32(pb.PackageItemType_BACKPACK_PRESENT) {
			mapGift[v.GetGiftId()] = v
		} else if v.GetGiftType() == uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) {
			mapFrag[v.GetGiftId()] = v
		} else {
			continue
		}
	}

	mapSup[uint32(pb.PackageItemType_BACKPACK_PRESENT)] = mapGift
	mapSup[uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT)] = mapFrag

	for _, v := range sups {
		var ok bool
		if v.GetGiftType() == uint32(pb.PackageItemType_BACKPACK_PRESENT) {
			_, ok = mapGift[v.GetGiftId()]
			if !ok {
				log.ErrorWithCtx(ctx, "SupplyCheck ,result: fail, not in supplyConf")
				return false, mapSup, nil
			}
		} else if v.GetGiftType() == uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) {
			_, ok = mapFrag[v.GetGiftId()]
			if !ok {
				log.ErrorWithCtx(ctx, "SupplyCheck ,result: fail, not in supplyConf")
				return false, mapSup, nil
			}
		} else {
			continue
		}
	}

	return true, mapSup, nil
}

func (m *Mgr) SetUserDailyInvestLimit(ctx context.Context, dailyLimit uint32) error {
	// 先查库
	oldLimit, err := m.store.GetLatestUserDailyLimit(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "mgr SetUserDailyInvestLimit fail,err:%v, in:%v", err, dailyLimit)
		return err
	}

	if dailyLimit == oldLimit {
		return nil
	}

	err = m.store.SetUserDailyLimit(ctx, dailyLimit)

	return err
}

func (m *Mgr) GetUserDailyInvestLimit(ctx context.Context) (uint32, error) {
	dailyLimit, err := m.store.GetLatestUserDailyLimit(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "mgr GetUserDailyInvestLimit fail,err:%v, in:%v", err, dailyLimit)
		return uint32(0), err
	}

	return dailyLimit, nil
}
