// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/virtual-avatar/internal/model/user-virtual-avatar/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	virtual_avatar "golang.52tt.com/protocol/services/virtual-avatar"
	entity "golang.52tt.com/services/tt-rev/virtual-avatar/internal/entity"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// BatchDelUserSwitchStatus mocks base method.
func (m *MockICache) BatchDelUserSwitchStatus(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelUserSwitchStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDelUserSwitchStatus indicates an expected call of BatchDelUserSwitchStatus.
func (mr *MockICacheMockRecorder) BatchDelUserSwitchStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelUserSwitchStatus", reflect.TypeOf((*MockICache)(nil).BatchDelUserSwitchStatus), arg0, arg1)
}

// BatchGetUserInUseCache mocks base method.
func (m *MockICache) BatchGetUserInUseCache(arg0 context.Context, arg1 []uint32) ([]uint32, map[uint32]*virtual_avatar.UserVAStatusInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserInUseCache", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(map[uint32]*virtual_avatar.UserVAStatusInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// BatchGetUserInUseCache indicates an expected call of BatchGetUserInUseCache.
func (mr *MockICacheMockRecorder) BatchGetUserInUseCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserInUseCache", reflect.TypeOf((*MockICache)(nil).BatchGetUserInUseCache), arg0, arg1)
}

// BatchGetUserSwitchStatus mocks base method.
func (m *MockICache) BatchGetUserSwitchStatus(arg0 context.Context, arg1 []uint32) ([]uint32, map[uint32]*entity.UseScopeSwitch, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserSwitchStatus", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(map[uint32]*entity.UseScopeSwitch)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// BatchGetUserSwitchStatus indicates an expected call of BatchGetUserSwitchStatus.
func (mr *MockICacheMockRecorder) BatchGetUserSwitchStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserSwitchStatus", reflect.TypeOf((*MockICache)(nil).BatchGetUserSwitchStatus), arg0, arg1)
}

// BatchGetUserVirtualAvatarList mocks base method.
func (m *MockICache) BatchGetUserVirtualAvatarList(arg0 context.Context, arg1 []uint32) (map[uint32][]*entity.UserVAStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserVirtualAvatarList", arg0, arg1)
	ret0, _ := ret[0].(map[uint32][]*entity.UserVAStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserVirtualAvatarList indicates an expected call of BatchGetUserVirtualAvatarList.
func (mr *MockICacheMockRecorder) BatchGetUserVirtualAvatarList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserVirtualAvatarList", reflect.TypeOf((*MockICache)(nil).BatchGetUserVirtualAvatarList), arg0, arg1)
}

// BatchSetUserSwitchStatus mocks base method.
func (m *MockICache) BatchSetUserSwitchStatus(arg0 context.Context, arg1 map[uint32]*entity.UseScopeSwitch) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetUserSwitchStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSetUserSwitchStatus indicates an expected call of BatchSetUserSwitchStatus.
func (mr *MockICacheMockRecorder) BatchSetUserSwitchStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetUserSwitchStatus", reflect.TypeOf((*MockICache)(nil).BatchSetUserSwitchStatus), arg0, arg1)
}

// ClearUserVirtualAvatarList mocks base method.
func (m *MockICache) ClearUserVirtualAvatarList(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearUserVirtualAvatarList", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearUserVirtualAvatarList indicates an expected call of ClearUserVirtualAvatarList.
func (mr *MockICacheMockRecorder) ClearUserVirtualAvatarList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearUserVirtualAvatarList", reflect.TypeOf((*MockICache)(nil).ClearUserVirtualAvatarList), arg0, arg1)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// DelUserInUseCache mocks base method.
func (m *MockICache) DelUserInUseCache(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserInUseCache", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserInUseCache indicates an expected call of DelUserInUseCache.
func (mr *MockICacheMockRecorder) DelUserInUseCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserInUseCache", reflect.TypeOf((*MockICache)(nil).DelUserInUseCache), arg0, arg1)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetUserInUseCache mocks base method.
func (m *MockICache) GetUserInUseCache(arg0 context.Context, arg1 uint32) (bool, *virtual_avatar.UserVAStatusInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInUseCache", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*virtual_avatar.UserVAStatusInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserInUseCache indicates an expected call of GetUserInUseCache.
func (mr *MockICacheMockRecorder) GetUserInUseCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInUseCache", reflect.TypeOf((*MockICache)(nil).GetUserInUseCache), arg0, arg1)
}

// GetUserVirtualAvatarList mocks base method.
func (m *MockICache) GetUserVirtualAvatarList(arg0 context.Context, arg1 uint32) ([]*entity.UserVAStatus, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserVirtualAvatarList", arg0, arg1)
	ret0, _ := ret[0].([]*entity.UserVAStatus)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserVirtualAvatarList indicates an expected call of GetUserVirtualAvatarList.
func (mr *MockICacheMockRecorder) GetUserVirtualAvatarList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserVirtualAvatarList", reflect.TypeOf((*MockICache)(nil).GetUserVirtualAvatarList), arg0, arg1)
}

// LockExpireVAHandle mocks base method.
func (m *MockICache) LockExpireVAHandle(arg0 context.Context, arg1, arg2 int32, arg3 time.Duration) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LockExpireVAHandle", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LockExpireVAHandle indicates an expected call of LockExpireVAHandle.
func (mr *MockICacheMockRecorder) LockExpireVAHandle(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LockExpireVAHandle", reflect.TypeOf((*MockICache)(nil).LockExpireVAHandle), arg0, arg1, arg2, arg3)
}

// SetUserInUseCache mocks base method.
func (m *MockICache) SetUserInUseCache(arg0 context.Context, arg1 uint32, arg2 *virtual_avatar.UserVAStatusInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserInUseCache", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserInUseCache indicates an expected call of SetUserInUseCache.
func (mr *MockICacheMockRecorder) SetUserInUseCache(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserInUseCache", reflect.TypeOf((*MockICache)(nil).SetUserInUseCache), arg0, arg1, arg2)
}

// SetUserVirtualAvatarList mocks base method.
func (m *MockICache) SetUserVirtualAvatarList(arg0 context.Context, arg1 uint32, arg2 []*entity.UserVAStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserVirtualAvatarList", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserVirtualAvatarList indicates an expected call of SetUserVirtualAvatarList.
func (mr *MockICacheMockRecorder) SetUserVirtualAvatarList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserVirtualAvatarList", reflect.TypeOf((*MockICache)(nil).SetUserVirtualAvatarList), arg0, arg1, arg2)
}

// UnlockExpireVAHandle mocks base method.
func (m *MockICache) UnlockExpireVAHandle(arg0 context.Context, arg1, arg2 int32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnlockExpireVAHandle", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnlockExpireVAHandle indicates an expected call of UnlockExpireVAHandle.
func (mr *MockICacheMockRecorder) UnlockExpireVAHandle(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnlockExpireVAHandle", reflect.TypeOf((*MockICache)(nil).UnlockExpireVAHandle), arg0, arg1, arg2)
}
