package conf

import (
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"time"

	"golang.52tt.com/pkg/log"
)

const (
	BusinessConfPath = "/data/oss/conf-center/tt/"
	BusinessConfFile = "virtual_image_logic.json"
)

var LastConfMd5Sum [md5.Size]byte

type BusinessConf struct {
    GameEnableCfg *GameEnableCfg `json:"game_enable_cfg"` // 功能开关配置
}

type GameEnableCfg struct {
    Enable       bool     `json:"enable"`
    WhiteUidList []uint32 `json:"white_uid_list"`   // 白名单uid
}

func (c *BusinessConf) checkConfValid() bool {

	return true
}

func (c *BusinessConf) parse(configFile string) (isChange bool, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return false, err
	}

	md5Sum := md5.Sum(data)
	if md5Sum == LastConfMd5Sum {
		isChange = false
		return
	}

	err = json.Unmarshal(data, &c)
	if err != nil {
		return false, err
	}

	if !c.checkConfValid() {
		log.Errorf("Parse fail. %+v, err:conf not valid", c)
		return false, errors.New("conf not valid")
	}

	LastConfMd5Sum = md5Sum

	log.Infof("BusinessConf : %+v", c)
	return true, nil
}

type BusinessConfManager struct {
	Done chan interface{}
	conf *BusinessConf
}

type IBusinessConf interface {
	Close()
    GetGameEnable(uid uint32) bool
}

func NewBusinessConfManager() (IBusinessConf, error) {
	businessConf := &BusinessConf{}

	businessConfFilePath := BusinessConfPath + BusinessConfFile
	if devBusinessConfPath := os.Getenv("DEV_BUSINESS_CONF_PATH"); devBusinessConfPath != "" {
		businessConfFilePath = devBusinessConfPath + BusinessConfFile
	}
	_, err := businessConf.parse(businessConfFilePath)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return nil, err
	}

	confMgr := &BusinessConfManager{
		conf: businessConf,
		Done: make(chan interface{}),
	}

	go confMgr.Watch(businessConfFilePath)

	return confMgr, nil
}

func (bm *BusinessConfManager) reload(file string) error {
	businessConf := &BusinessConf{}

	isChange, err := businessConf.parse(file)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return err
	}

	if isChange {
		bm.conf = businessConf
		log.Infof("Reload %+v", businessConf)
	}

	return nil
}

func (bm *BusinessConfManager) Watch(file string) {
	log.Infof("Watch start. file:%s", file)

	for {
		select {
		case _, ok := <-bm.Done:
			if !ok {
				log.Infof("Watch done")
				return
			}

		case <-time.After(30 * time.Second):
			log.Debugf("Watch check change")

			err := bm.reload(file)
			if err != nil {
				log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
			}
		}
	}
}

func (bm *BusinessConfManager) Close() {
	close(bm.Done)
}

func (bm *BusinessConfManager) GetGameEnable(uid uint32) bool {
    if bm == nil || bm.conf == nil || bm.conf.GameEnableCfg == nil {
        return false
    }

    if bm.conf.GameEnableCfg.Enable {
        return true
    }

    for _, v := range bm.conf.GameEnableCfg.WhiteUidList {
        if v == uid {
            // 白名单用户
            return true
        }
    }

    return bm.conf.GameEnableCfg.Enable
}
