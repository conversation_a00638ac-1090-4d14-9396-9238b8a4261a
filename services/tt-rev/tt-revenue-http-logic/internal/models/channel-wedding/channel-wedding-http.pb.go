// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-wedding-http/channel-wedding-http.proto

package channel_wedding_http

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type WeddingGuestType int32

const (
	WeddingGuestType_UNKNOWN    WeddingGuestType = 0
	WeddingGuestType_GROOMSMAN  WeddingGuestType = 1
	WeddingGuestType_BRIDESMAID WeddingGuestType = 2
	WeddingGuestType_FRIENDS    WeddingGuestType = 3
)

var WeddingGuestType_name = map[int32]string{
	0: "UNKNOWN",
	1: "GROOMSMAN",
	2: "BRIDESMAID",
	3: "FRIENDS",
}
var WeddingGuestType_value = map[string]int32{
	"UNKNOWN":    0,
	"GROOMSMAN":  1,
	"BRIDESMAID": 2,
	"FRIENDS":    3,
}

func (x WeddingGuestType) String() string {
	return proto.EnumName(WeddingGuestType_name, int32(x))
}
func (WeddingGuestType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{0}
}

type ThemeType int32

const (
	ThemeType_THEME_TYPE_UNKNOWN ThemeType = 0
	ThemeType_THEME_TYPE_FREE    ThemeType = 1
	ThemeType_THEME_TYPE_PAY     ThemeType = 2
)

var ThemeType_name = map[int32]string{
	0: "THEME_TYPE_UNKNOWN",
	1: "THEME_TYPE_FREE",
	2: "THEME_TYPE_PAY",
}
var ThemeType_value = map[string]int32{
	"THEME_TYPE_UNKNOWN": 0,
	"THEME_TYPE_FREE":    1,
	"THEME_TYPE_PAY":     2,
}

func (x ThemeType) String() string {
	return proto.EnumName(ThemeType_name, int32(x))
}
func (ThemeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{1}
}

// 获取我的预约信息  url: /tt-revenue-http-logic/channel_wedding/get_my_wedding_reserve_info
type GetMyWeddingReserveInfoRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyWeddingReserveInfoRequest) Reset()         { *m = GetMyWeddingReserveInfoRequest{} }
func (m *GetMyWeddingReserveInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetMyWeddingReserveInfoRequest) ProtoMessage()    {}
func (*GetMyWeddingReserveInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{0}
}
func (m *GetMyWeddingReserveInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyWeddingReserveInfoRequest.Unmarshal(m, b)
}
func (m *GetMyWeddingReserveInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyWeddingReserveInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetMyWeddingReserveInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyWeddingReserveInfoRequest.Merge(dst, src)
}
func (m *GetMyWeddingReserveInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetMyWeddingReserveInfoRequest.Size(m)
}
func (m *GetMyWeddingReserveInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyWeddingReserveInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyWeddingReserveInfoRequest proto.InternalMessageInfo

func (m *GetMyWeddingReserveInfoRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type GetMyWeddingReserveInfoResponse struct {
	ReserveDate          uint32   `protobuf:"varint,1,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	ReserveTimeSection   []string `protobuf:"bytes,3,rep,name=reserve_time_section,json=reserveTimeSection,proto3" json:"reserve_time_section"`
	RemainChangeTimes    uint32   `protobuf:"varint,4,opt,name=remain_change_times,json=remainChangeTimes,proto3" json:"remain_change_times"`
	ChangeLimitTime      uint32   `protobuf:"varint,5,opt,name=change_limit_time,json=changeLimitTime,proto3" json:"change_limit_time"`
	InChangeTime         bool     `protobuf:"varint,6,opt,name=in_change_time,json=inChangeTime,proto3" json:"in_change_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyWeddingReserveInfoResponse) Reset()         { *m = GetMyWeddingReserveInfoResponse{} }
func (m *GetMyWeddingReserveInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetMyWeddingReserveInfoResponse) ProtoMessage()    {}
func (*GetMyWeddingReserveInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{1}
}
func (m *GetMyWeddingReserveInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyWeddingReserveInfoResponse.Unmarshal(m, b)
}
func (m *GetMyWeddingReserveInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyWeddingReserveInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetMyWeddingReserveInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyWeddingReserveInfoResponse.Merge(dst, src)
}
func (m *GetMyWeddingReserveInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetMyWeddingReserveInfoResponse.Size(m)
}
func (m *GetMyWeddingReserveInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyWeddingReserveInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyWeddingReserveInfoResponse proto.InternalMessageInfo

func (m *GetMyWeddingReserveInfoResponse) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *GetMyWeddingReserveInfoResponse) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMyWeddingReserveInfoResponse) GetReserveTimeSection() []string {
	if m != nil {
		return m.ReserveTimeSection
	}
	return nil
}

func (m *GetMyWeddingReserveInfoResponse) GetRemainChangeTimes() uint32 {
	if m != nil {
		return m.RemainChangeTimes
	}
	return 0
}

func (m *GetMyWeddingReserveInfoResponse) GetChangeLimitTime() uint32 {
	if m != nil {
		return m.ChangeLimitTime
	}
	return 0
}

func (m *GetMyWeddingReserveInfoResponse) GetInChangeTime() bool {
	if m != nil {
		return m.InChangeTime
	}
	return false
}

// 获取可预约信息 url: /tt-revenue-http-logic/channel_wedding/get_wedding_reserve_info
type GetWeddingReserveInfoRequest struct {
	ThemeType            uint32   `protobuf:"varint,1,opt,name=theme_type,json=themeType,proto3" json:"theme_type"`
	ReserveDate          uint32   `protobuf:"varint,2,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	ThemeId              uint32   `protobuf:"varint,4,opt,name=theme_id,json=themeId,proto3" json:"theme_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingReserveInfoRequest) Reset()         { *m = GetWeddingReserveInfoRequest{} }
func (m *GetWeddingReserveInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingReserveInfoRequest) ProtoMessage()    {}
func (*GetWeddingReserveInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{2}
}
func (m *GetWeddingReserveInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingReserveInfoRequest.Unmarshal(m, b)
}
func (m *GetWeddingReserveInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingReserveInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingReserveInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingReserveInfoRequest.Merge(dst, src)
}
func (m *GetWeddingReserveInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingReserveInfoRequest.Size(m)
}
func (m *GetWeddingReserveInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingReserveInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingReserveInfoRequest proto.InternalMessageInfo

func (m *GetWeddingReserveInfoRequest) GetThemeType() uint32 {
	if m != nil {
		return m.ThemeType
	}
	return 0
}

func (m *GetWeddingReserveInfoRequest) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *GetWeddingReserveInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetWeddingReserveInfoRequest) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

// 预约房间信息
type WeddingChannelInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	ChannelName          string   `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	ManagerTtid          string   `protobuf:"bytes,4,opt,name=manager_ttid,json=managerTtid,proto3" json:"manager_ttid"`
	ManagerNickname      string   `protobuf:"bytes,5,opt,name=manager_nickname,json=managerNickname,proto3" json:"manager_nickname"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingChannelInfo) Reset()         { *m = WeddingChannelInfo{} }
func (m *WeddingChannelInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingChannelInfo) ProtoMessage()    {}
func (*WeddingChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{3}
}
func (m *WeddingChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingChannelInfo.Unmarshal(m, b)
}
func (m *WeddingChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingChannelInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingChannelInfo.Merge(dst, src)
}
func (m *WeddingChannelInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingChannelInfo.Size(m)
}
func (m *WeddingChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingChannelInfo proto.InternalMessageInfo

func (m *WeddingChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WeddingChannelInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *WeddingChannelInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *WeddingChannelInfo) GetManagerTtid() string {
	if m != nil {
		return m.ManagerTtid
	}
	return ""
}

func (m *WeddingChannelInfo) GetManagerNickname() string {
	if m != nil {
		return m.ManagerNickname
	}
	return ""
}

// 预约时段信息
type ReserveTimeInfo struct {
	ReserveTimeSection   string       `protobuf:"bytes,1,opt,name=reserve_time_section,json=reserveTimeSection,proto3" json:"reserve_time_section"`
	IsFullyReserved      bool         `protobuf:"varint,2,opt,name=is_fully_reserved,json=isFullyReserved,proto3" json:"is_fully_reserved"`
	Groom                *UserProfile `protobuf:"bytes,3,opt,name=groom,proto3" json:"groom"`
	Bride                *UserProfile `protobuf:"bytes,4,opt,name=bride,proto3" json:"bride"`
	IsHot                bool         `protobuf:"varint,5,opt,name=is_hot,json=isHot,proto3" json:"is_hot"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ReserveTimeInfo) Reset()         { *m = ReserveTimeInfo{} }
func (m *ReserveTimeInfo) String() string { return proto.CompactTextString(m) }
func (*ReserveTimeInfo) ProtoMessage()    {}
func (*ReserveTimeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{4}
}
func (m *ReserveTimeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReserveTimeInfo.Unmarshal(m, b)
}
func (m *ReserveTimeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReserveTimeInfo.Marshal(b, m, deterministic)
}
func (dst *ReserveTimeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReserveTimeInfo.Merge(dst, src)
}
func (m *ReserveTimeInfo) XXX_Size() int {
	return xxx_messageInfo_ReserveTimeInfo.Size(m)
}
func (m *ReserveTimeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReserveTimeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReserveTimeInfo proto.InternalMessageInfo

func (m *ReserveTimeInfo) GetReserveTimeSection() string {
	if m != nil {
		return m.ReserveTimeSection
	}
	return ""
}

func (m *ReserveTimeInfo) GetIsFullyReserved() bool {
	if m != nil {
		return m.IsFullyReserved
	}
	return false
}

func (m *ReserveTimeInfo) GetGroom() *UserProfile {
	if m != nil {
		return m.Groom
	}
	return nil
}

func (m *ReserveTimeInfo) GetBride() *UserProfile {
	if m != nil {
		return m.Bride
	}
	return nil
}

func (m *ReserveTimeInfo) GetIsHot() bool {
	if m != nil {
		return m.IsHot
	}
	return false
}

type GiftInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id"`
	GiftName             string   `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	GiftDesc             string   `protobuf:"bytes,3,opt,name=gift_desc,json=giftDesc,proto3" json:"gift_desc"`
	GiftIcon             string   `protobuf:"bytes,4,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon"`
	Worth                uint32   `protobuf:"varint,5,opt,name=worth,proto3" json:"worth"`
	BuyPrice             uint32   `protobuf:"varint,6,opt,name=buy_price,json=buyPrice,proto3" json:"buy_price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiftInfo) Reset()         { *m = GiftInfo{} }
func (m *GiftInfo) String() string { return proto.CompactTextString(m) }
func (*GiftInfo) ProtoMessage()    {}
func (*GiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{5}
}
func (m *GiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiftInfo.Unmarshal(m, b)
}
func (m *GiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiftInfo.Marshal(b, m, deterministic)
}
func (dst *GiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftInfo.Merge(dst, src)
}
func (m *GiftInfo) XXX_Size() int {
	return xxx_messageInfo_GiftInfo.Size(m)
}
func (m *GiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GiftInfo proto.InternalMessageInfo

func (m *GiftInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *GiftInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *GiftInfo) GetGiftDesc() string {
	if m != nil {
		return m.GiftDesc
	}
	return ""
}

func (m *GiftInfo) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

func (m *GiftInfo) GetWorth() uint32 {
	if m != nil {
		return m.Worth
	}
	return 0
}

func (m *GiftInfo) GetBuyPrice() uint32 {
	if m != nil {
		return m.BuyPrice
	}
	return 0
}

type GetWeddingReserveInfoResponse struct {
	CurChannelId           uint32                `protobuf:"varint,1,opt,name=cur_channel_id,json=curChannelId,proto3" json:"cur_channel_id"`
	CurReserveDate         uint32                `protobuf:"varint,2,opt,name=cur_reserve_date,json=curReserveDate,proto3" json:"cur_reserve_date"`
	ReserveableChannelList []*WeddingChannelInfo `protobuf:"bytes,3,rep,name=reserveable_channel_list,json=reserveableChannelList,proto3" json:"reserveable_channel_list"`
	ReserveTimeInfoList    []*ReserveTimeInfo    `protobuf:"bytes,4,rep,name=reserve_time_info_list,json=reserveTimeInfoList,proto3" json:"reserve_time_info_list"`
	MinReserveDate         uint32                `protobuf:"varint,5,opt,name=min_reserve_date,json=minReserveDate,proto3" json:"min_reserve_date"`
	MaxReserveDate         uint32                `protobuf:"varint,6,opt,name=max_reserve_date,json=maxReserveDate,proto3" json:"max_reserve_date"`
	MaxReserveNum          uint32                `protobuf:"varint,7,opt,name=max_reserve_num,json=maxReserveNum,proto3" json:"max_reserve_num"`
	HotTimeGiftList        []*GiftInfo           `protobuf:"bytes,8,rep,name=hot_time_gift_list,json=hotTimeGiftList,proto3" json:"hot_time_gift_list"`
	NormalTimeGiftList     []*GiftInfo           `protobuf:"bytes,9,rep,name=normal_time_gift_list,json=normalTimeGiftList,proto3" json:"normal_time_gift_list"`
	HotLabel               *LabelInfo            `protobuf:"bytes,10,opt,name=hot_label,json=hotLabel,proto3" json:"hot_label"`
	XXX_NoUnkeyedLiteral   struct{}              `json:"-"`
	XXX_unrecognized       []byte                `json:"-"`
	XXX_sizecache          int32                 `json:"-"`
}

func (m *GetWeddingReserveInfoResponse) Reset()         { *m = GetWeddingReserveInfoResponse{} }
func (m *GetWeddingReserveInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingReserveInfoResponse) ProtoMessage()    {}
func (*GetWeddingReserveInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{6}
}
func (m *GetWeddingReserveInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingReserveInfoResponse.Unmarshal(m, b)
}
func (m *GetWeddingReserveInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingReserveInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingReserveInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingReserveInfoResponse.Merge(dst, src)
}
func (m *GetWeddingReserveInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingReserveInfoResponse.Size(m)
}
func (m *GetWeddingReserveInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingReserveInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingReserveInfoResponse proto.InternalMessageInfo

func (m *GetWeddingReserveInfoResponse) GetCurChannelId() uint32 {
	if m != nil {
		return m.CurChannelId
	}
	return 0
}

func (m *GetWeddingReserveInfoResponse) GetCurReserveDate() uint32 {
	if m != nil {
		return m.CurReserveDate
	}
	return 0
}

func (m *GetWeddingReserveInfoResponse) GetReserveableChannelList() []*WeddingChannelInfo {
	if m != nil {
		return m.ReserveableChannelList
	}
	return nil
}

func (m *GetWeddingReserveInfoResponse) GetReserveTimeInfoList() []*ReserveTimeInfo {
	if m != nil {
		return m.ReserveTimeInfoList
	}
	return nil
}

func (m *GetWeddingReserveInfoResponse) GetMinReserveDate() uint32 {
	if m != nil {
		return m.MinReserveDate
	}
	return 0
}

func (m *GetWeddingReserveInfoResponse) GetMaxReserveDate() uint32 {
	if m != nil {
		return m.MaxReserveDate
	}
	return 0
}

func (m *GetWeddingReserveInfoResponse) GetMaxReserveNum() uint32 {
	if m != nil {
		return m.MaxReserveNum
	}
	return 0
}

func (m *GetWeddingReserveInfoResponse) GetHotTimeGiftList() []*GiftInfo {
	if m != nil {
		return m.HotTimeGiftList
	}
	return nil
}

func (m *GetWeddingReserveInfoResponse) GetNormalTimeGiftList() []*GiftInfo {
	if m != nil {
		return m.NormalTimeGiftList
	}
	return nil
}

func (m *GetWeddingReserveInfoResponse) GetHotLabel() *LabelInfo {
	if m != nil {
		return m.HotLabel
	}
	return nil
}

type LabelInfo struct {
	LabelName            string   `protobuf:"bytes,1,opt,name=label_name,json=labelName,proto3" json:"label_name"`
	LabelIcon            string   `protobuf:"bytes,2,opt,name=label_icon,json=labelIcon,proto3" json:"label_icon"`
	LabelDesc            string   `protobuf:"bytes,3,opt,name=label_desc,json=labelDesc,proto3" json:"label_desc"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LabelInfo) Reset()         { *m = LabelInfo{} }
func (m *LabelInfo) String() string { return proto.CompactTextString(m) }
func (*LabelInfo) ProtoMessage()    {}
func (*LabelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{7}
}
func (m *LabelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LabelInfo.Unmarshal(m, b)
}
func (m *LabelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LabelInfo.Marshal(b, m, deterministic)
}
func (dst *LabelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelInfo.Merge(dst, src)
}
func (m *LabelInfo) XXX_Size() int {
	return xxx_messageInfo_LabelInfo.Size(m)
}
func (m *LabelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LabelInfo proto.InternalMessageInfo

func (m *LabelInfo) GetLabelName() string {
	if m != nil {
		return m.LabelName
	}
	return ""
}

func (m *LabelInfo) GetLabelIcon() string {
	if m != nil {
		return m.LabelIcon
	}
	return ""
}

func (m *LabelInfo) GetLabelDesc() string {
	if m != nil {
		return m.LabelDesc
	}
	return ""
}

// 保存预约信息 url: /tt-revenue-http-logic/channel_wedding/save_wedding_reserve
type SaveWeddingReserveRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id"`
	ReserveDate          uint32   `protobuf:"varint,2,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	ReserveTime          []string `protobuf:"bytes,4,rep,name=reserve_time,json=reserveTime,proto3" json:"reserve_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveWeddingReserveRequest) Reset()         { *m = SaveWeddingReserveRequest{} }
func (m *SaveWeddingReserveRequest) String() string { return proto.CompactTextString(m) }
func (*SaveWeddingReserveRequest) ProtoMessage()    {}
func (*SaveWeddingReserveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{8}
}
func (m *SaveWeddingReserveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveWeddingReserveRequest.Unmarshal(m, b)
}
func (m *SaveWeddingReserveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveWeddingReserveRequest.Marshal(b, m, deterministic)
}
func (dst *SaveWeddingReserveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveWeddingReserveRequest.Merge(dst, src)
}
func (m *SaveWeddingReserveRequest) XXX_Size() int {
	return xxx_messageInfo_SaveWeddingReserveRequest.Size(m)
}
func (m *SaveWeddingReserveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveWeddingReserveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SaveWeddingReserveRequest proto.InternalMessageInfo

func (m *SaveWeddingReserveRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *SaveWeddingReserveRequest) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *SaveWeddingReserveRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SaveWeddingReserveRequest) GetReserveTime() []string {
	if m != nil {
		return m.ReserveTime
	}
	return nil
}

type SaveWeddingReserveResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveWeddingReserveResponse) Reset()         { *m = SaveWeddingReserveResponse{} }
func (m *SaveWeddingReserveResponse) String() string { return proto.CompactTextString(m) }
func (*SaveWeddingReserveResponse) ProtoMessage()    {}
func (*SaveWeddingReserveResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{9}
}
func (m *SaveWeddingReserveResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveWeddingReserveResponse.Unmarshal(m, b)
}
func (m *SaveWeddingReserveResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveWeddingReserveResponse.Marshal(b, m, deterministic)
}
func (dst *SaveWeddingReserveResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveWeddingReserveResponse.Merge(dst, src)
}
func (m *SaveWeddingReserveResponse) XXX_Size() int {
	return xxx_messageInfo_SaveWeddingReserveResponse.Size(m)
}
func (m *SaveWeddingReserveResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveWeddingReserveResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SaveWeddingReserveResponse proto.InternalMessageInfo

// 修改预约信息 url: /tt-revenue-http-logic/channel_wedding/change_wedding_reserve
type ChangeWeddingReserveRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id"`
	ReserveDate          uint32   `protobuf:"varint,2,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	ReserveTime          []string `protobuf:"bytes,4,rep,name=reserve_time,json=reserveTime,proto3" json:"reserve_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeWeddingReserveRequest) Reset()         { *m = ChangeWeddingReserveRequest{} }
func (m *ChangeWeddingReserveRequest) String() string { return proto.CompactTextString(m) }
func (*ChangeWeddingReserveRequest) ProtoMessage()    {}
func (*ChangeWeddingReserveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{10}
}
func (m *ChangeWeddingReserveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeWeddingReserveRequest.Unmarshal(m, b)
}
func (m *ChangeWeddingReserveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeWeddingReserveRequest.Marshal(b, m, deterministic)
}
func (dst *ChangeWeddingReserveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeWeddingReserveRequest.Merge(dst, src)
}
func (m *ChangeWeddingReserveRequest) XXX_Size() int {
	return xxx_messageInfo_ChangeWeddingReserveRequest.Size(m)
}
func (m *ChangeWeddingReserveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeWeddingReserveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeWeddingReserveRequest proto.InternalMessageInfo

func (m *ChangeWeddingReserveRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *ChangeWeddingReserveRequest) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *ChangeWeddingReserveRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChangeWeddingReserveRequest) GetReserveTime() []string {
	if m != nil {
		return m.ReserveTime
	}
	return nil
}

type ChangeWeddingReserveResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeWeddingReserveResponse) Reset()         { *m = ChangeWeddingReserveResponse{} }
func (m *ChangeWeddingReserveResponse) String() string { return proto.CompactTextString(m) }
func (*ChangeWeddingReserveResponse) ProtoMessage()    {}
func (*ChangeWeddingReserveResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{11}
}
func (m *ChangeWeddingReserveResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeWeddingReserveResponse.Unmarshal(m, b)
}
func (m *ChangeWeddingReserveResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeWeddingReserveResponse.Marshal(b, m, deterministic)
}
func (dst *ChangeWeddingReserveResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeWeddingReserveResponse.Merge(dst, src)
}
func (m *ChangeWeddingReserveResponse) XXX_Size() int {
	return xxx_messageInfo_ChangeWeddingReserveResponse.Size(m)
}
func (m *ChangeWeddingReserveResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeWeddingReserveResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeWeddingReserveResponse proto.InternalMessageInfo

type UserProfile struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname"`
	AccountAlias         string   `protobuf:"bytes,4,opt,name=account_alias,json=accountAlias,proto3" json:"account_alias"`
	Sex                  uint32   `protobuf:"varint,5,opt,name=sex,proto3" json:"sex"`
	HeadImgMd5           string   `protobuf:"bytes,6,opt,name=head_img_md5,json=headImgMd5,proto3" json:"head_img_md5"`
	HeadDyImgMd5         string   `protobuf:"bytes,7,opt,name=head_dy_img_md5,json=headDyImgMd5,proto3" json:"head_dy_img_md5"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserProfile) Reset()         { *m = UserProfile{} }
func (m *UserProfile) String() string { return proto.CompactTextString(m) }
func (*UserProfile) ProtoMessage()    {}
func (*UserProfile) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{12}
}
func (m *UserProfile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserProfile.Unmarshal(m, b)
}
func (m *UserProfile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserProfile.Marshal(b, m, deterministic)
}
func (dst *UserProfile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserProfile.Merge(dst, src)
}
func (m *UserProfile) XXX_Size() int {
	return xxx_messageInfo_UserProfile.Size(m)
}
func (m *UserProfile) XXX_DiscardUnknown() {
	xxx_messageInfo_UserProfile.DiscardUnknown(m)
}

var xxx_messageInfo_UserProfile proto.InternalMessageInfo

func (m *UserProfile) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserProfile) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserProfile) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserProfile) GetAccountAlias() string {
	if m != nil {
		return m.AccountAlias
	}
	return ""
}

func (m *UserProfile) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserProfile) GetHeadImgMd5() string {
	if m != nil {
		return m.HeadImgMd5
	}
	return ""
}

func (m *UserProfile) GetHeadDyImgMd5() string {
	if m != nil {
		return m.HeadDyImgMd5
	}
	return ""
}

type WeddingGuestInfo struct {
	UserProfile          *UserProfile `protobuf:"bytes,1,opt,name=user_profile,json=userProfile,proto3" json:"user_profile"`
	InviteUid            uint32       `protobuf:"varint,2,opt,name=invite_uid,json=inviteUid,proto3" json:"invite_uid"`
	FellowVal            uint32       `protobuf:"varint,3,opt,name=fellow_val,json=fellowVal,proto3" json:"fellow_val"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *WeddingGuestInfo) Reset()         { *m = WeddingGuestInfo{} }
func (m *WeddingGuestInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingGuestInfo) ProtoMessage()    {}
func (*WeddingGuestInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{13}
}
func (m *WeddingGuestInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingGuestInfo.Unmarshal(m, b)
}
func (m *WeddingGuestInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingGuestInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingGuestInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingGuestInfo.Merge(dst, src)
}
func (m *WeddingGuestInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingGuestInfo.Size(m)
}
func (m *WeddingGuestInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingGuestInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingGuestInfo proto.InternalMessageInfo

func (m *WeddingGuestInfo) GetUserProfile() *UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *WeddingGuestInfo) GetInviteUid() uint32 {
	if m != nil {
		return m.InviteUid
	}
	return 0
}

func (m *WeddingGuestInfo) GetFellowVal() uint32 {
	if m != nil {
		return m.FellowVal
	}
	return 0
}

// 获取伴郎伴娘信息 url: /tt-revenue-http-logic/channel_wedding/get_groomsman_and_bridesmaid_info
type GetGroomsmanAndBridesmaidInfoRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroomsmanAndBridesmaidInfoRequest) Reset()         { *m = GetGroomsmanAndBridesmaidInfoRequest{} }
func (m *GetGroomsmanAndBridesmaidInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroomsmanAndBridesmaidInfoRequest) ProtoMessage()    {}
func (*GetGroomsmanAndBridesmaidInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{14}
}
func (m *GetGroomsmanAndBridesmaidInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroomsmanAndBridesmaidInfoRequest.Unmarshal(m, b)
}
func (m *GetGroomsmanAndBridesmaidInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroomsmanAndBridesmaidInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetGroomsmanAndBridesmaidInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroomsmanAndBridesmaidInfoRequest.Merge(dst, src)
}
func (m *GetGroomsmanAndBridesmaidInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroomsmanAndBridesmaidInfoRequest.Size(m)
}
func (m *GetGroomsmanAndBridesmaidInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroomsmanAndBridesmaidInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroomsmanAndBridesmaidInfoRequest proto.InternalMessageInfo

func (m *GetGroomsmanAndBridesmaidInfoRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type GetGroomsmanAndBridesmaidInfoResponse struct {
	BridesmaidList       []*WeddingGuestInfo `protobuf:"bytes,1,rep,name=bridesmaid_list,json=bridesmaidList,proto3" json:"bridesmaid_list"`
	MaxBridesmaidNum     uint32              `protobuf:"varint,2,opt,name=max_bridesmaid_num,json=maxBridesmaidNum,proto3" json:"max_bridesmaid_num"`
	GroomsmanList        []*WeddingGuestInfo `protobuf:"bytes,3,rep,name=groomsman_list,json=groomsmanList,proto3" json:"groomsman_list"`
	MaxGroomsmanNum      uint32              `protobuf:"varint,4,opt,name=max_groomsman_num,json=maxGroomsmanNum,proto3" json:"max_groomsman_num"`
	InvitedList          []*WeddingGuestInfo `protobuf:"bytes,5,rep,name=invited_list,json=invitedList,proto3" json:"invited_list"`
	AgreedList           []*WeddingGuestInfo `protobuf:"bytes,6,rep,name=agreed_list,json=agreedList,proto3" json:"agreed_list"`
	RefusedList          []*WeddingGuestInfo `protobuf:"bytes,7,rep,name=refused_list,json=refusedList,proto3" json:"refused_list"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetGroomsmanAndBridesmaidInfoResponse) Reset()         { *m = GetGroomsmanAndBridesmaidInfoResponse{} }
func (m *GetGroomsmanAndBridesmaidInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroomsmanAndBridesmaidInfoResponse) ProtoMessage()    {}
func (*GetGroomsmanAndBridesmaidInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{15}
}
func (m *GetGroomsmanAndBridesmaidInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroomsmanAndBridesmaidInfoResponse.Unmarshal(m, b)
}
func (m *GetGroomsmanAndBridesmaidInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroomsmanAndBridesmaidInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetGroomsmanAndBridesmaidInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroomsmanAndBridesmaidInfoResponse.Merge(dst, src)
}
func (m *GetGroomsmanAndBridesmaidInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroomsmanAndBridesmaidInfoResponse.Size(m)
}
func (m *GetGroomsmanAndBridesmaidInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroomsmanAndBridesmaidInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroomsmanAndBridesmaidInfoResponse proto.InternalMessageInfo

func (m *GetGroomsmanAndBridesmaidInfoResponse) GetBridesmaidList() []*WeddingGuestInfo {
	if m != nil {
		return m.BridesmaidList
	}
	return nil
}

func (m *GetGroomsmanAndBridesmaidInfoResponse) GetMaxBridesmaidNum() uint32 {
	if m != nil {
		return m.MaxBridesmaidNum
	}
	return 0
}

func (m *GetGroomsmanAndBridesmaidInfoResponse) GetGroomsmanList() []*WeddingGuestInfo {
	if m != nil {
		return m.GroomsmanList
	}
	return nil
}

func (m *GetGroomsmanAndBridesmaidInfoResponse) GetMaxGroomsmanNum() uint32 {
	if m != nil {
		return m.MaxGroomsmanNum
	}
	return 0
}

func (m *GetGroomsmanAndBridesmaidInfoResponse) GetInvitedList() []*WeddingGuestInfo {
	if m != nil {
		return m.InvitedList
	}
	return nil
}

func (m *GetGroomsmanAndBridesmaidInfoResponse) GetAgreedList() []*WeddingGuestInfo {
	if m != nil {
		return m.AgreedList
	}
	return nil
}

func (m *GetGroomsmanAndBridesmaidInfoResponse) GetRefusedList() []*WeddingGuestInfo {
	if m != nil {
		return m.RefusedList
	}
	return nil
}

// 获取亲友团信息 url: /tt-revenue-http-logic/channel_wedding/get_wedding_friend_info
type GetWeddingFriendInfoRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingFriendInfoRequest) Reset()         { *m = GetWeddingFriendInfoRequest{} }
func (m *GetWeddingFriendInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingFriendInfoRequest) ProtoMessage()    {}
func (*GetWeddingFriendInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{16}
}
func (m *GetWeddingFriendInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingFriendInfoRequest.Unmarshal(m, b)
}
func (m *GetWeddingFriendInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingFriendInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingFriendInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingFriendInfoRequest.Merge(dst, src)
}
func (m *GetWeddingFriendInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingFriendInfoRequest.Size(m)
}
func (m *GetWeddingFriendInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingFriendInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingFriendInfoRequest proto.InternalMessageInfo

func (m *GetWeddingFriendInfoRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type GetWeddingFriendInfoResponse struct {
	FriendList           []*WeddingGuestInfo `protobuf:"bytes,1,rep,name=friend_list,json=friendList,proto3" json:"friend_list"`
	InvitedList          []*WeddingGuestInfo `protobuf:"bytes,2,rep,name=invited_list,json=invitedList,proto3" json:"invited_list"`
	AgreedList           []*WeddingGuestInfo `protobuf:"bytes,3,rep,name=agreed_list,json=agreedList,proto3" json:"agreed_list"`
	RefusedList          []*WeddingGuestInfo `protobuf:"bytes,4,rep,name=refused_list,json=refusedList,proto3" json:"refused_list"`
	MaxFriendNum         uint32              `protobuf:"varint,5,opt,name=max_friend_num,json=maxFriendNum,proto3" json:"max_friend_num"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetWeddingFriendInfoResponse) Reset()         { *m = GetWeddingFriendInfoResponse{} }
func (m *GetWeddingFriendInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingFriendInfoResponse) ProtoMessage()    {}
func (*GetWeddingFriendInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{17}
}
func (m *GetWeddingFriendInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingFriendInfoResponse.Unmarshal(m, b)
}
func (m *GetWeddingFriendInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingFriendInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingFriendInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingFriendInfoResponse.Merge(dst, src)
}
func (m *GetWeddingFriendInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingFriendInfoResponse.Size(m)
}
func (m *GetWeddingFriendInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingFriendInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingFriendInfoResponse proto.InternalMessageInfo

func (m *GetWeddingFriendInfoResponse) GetFriendList() []*WeddingGuestInfo {
	if m != nil {
		return m.FriendList
	}
	return nil
}

func (m *GetWeddingFriendInfoResponse) GetInvitedList() []*WeddingGuestInfo {
	if m != nil {
		return m.InvitedList
	}
	return nil
}

func (m *GetWeddingFriendInfoResponse) GetAgreedList() []*WeddingGuestInfo {
	if m != nil {
		return m.AgreedList
	}
	return nil
}

func (m *GetWeddingFriendInfoResponse) GetRefusedList() []*WeddingGuestInfo {
	if m != nil {
		return m.RefusedList
	}
	return nil
}

func (m *GetWeddingFriendInfoResponse) GetMaxFriendNum() uint32 {
	if m != nil {
		return m.MaxFriendNum
	}
	return 0
}

// 获取婚礼邀请列表 url: /tt-revenue-http-logic/channel_wedding/get_play_mate_list
type GetPlaymateListRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id"`
	PageNum              uint32   `protobuf:"varint,2,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Account              string   `protobuf:"bytes,4,opt,name=account,proto3" json:"account"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlaymateListRequest) Reset()         { *m = GetPlaymateListRequest{} }
func (m *GetPlaymateListRequest) String() string { return proto.CompactTextString(m) }
func (*GetPlaymateListRequest) ProtoMessage()    {}
func (*GetPlaymateListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{18}
}
func (m *GetPlaymateListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlaymateListRequest.Unmarshal(m, b)
}
func (m *GetPlaymateListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlaymateListRequest.Marshal(b, m, deterministic)
}
func (dst *GetPlaymateListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlaymateListRequest.Merge(dst, src)
}
func (m *GetPlaymateListRequest) XXX_Size() int {
	return xxx_messageInfo_GetPlaymateListRequest.Size(m)
}
func (m *GetPlaymateListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlaymateListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlaymateListRequest proto.InternalMessageInfo

func (m *GetPlaymateListRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *GetPlaymateListRequest) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetPlaymateListRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetPlaymateListRequest) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type GetPlaymateListResponse struct {
	PlaymateList         []*WeddingPlaymateInfo `protobuf:"bytes,2,rep,name=playmate_list,json=playmateList,proto3" json:"playmate_list"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPlaymateListResponse) Reset()         { *m = GetPlaymateListResponse{} }
func (m *GetPlaymateListResponse) String() string { return proto.CompactTextString(m) }
func (*GetPlaymateListResponse) ProtoMessage()    {}
func (*GetPlaymateListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{19}
}
func (m *GetPlaymateListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlaymateListResponse.Unmarshal(m, b)
}
func (m *GetPlaymateListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlaymateListResponse.Marshal(b, m, deterministic)
}
func (dst *GetPlaymateListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlaymateListResponse.Merge(dst, src)
}
func (m *GetPlaymateListResponse) XXX_Size() int {
	return xxx_messageInfo_GetPlaymateListResponse.Size(m)
}
func (m *GetPlaymateListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlaymateListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlaymateListResponse proto.InternalMessageInfo

func (m *GetPlaymateListResponse) GetPlaymateList() []*WeddingPlaymateInfo {
	if m != nil {
		return m.PlaymateList
	}
	return nil
}

type WeddingPlaymateInfo struct {
	UserProfile          *UserProfile `protobuf:"bytes,1,opt,name=user_profile,json=userProfile,proto3" json:"user_profile"`
	PlaymateStatus       uint32       `protobuf:"varint,2,opt,name=playmate_status,json=playmateStatus,proto3" json:"playmate_status"`
	FellowVal            uint32       `protobuf:"varint,3,opt,name=fellow_val,json=fellowVal,proto3" json:"fellow_val"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *WeddingPlaymateInfo) Reset()         { *m = WeddingPlaymateInfo{} }
func (m *WeddingPlaymateInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingPlaymateInfo) ProtoMessage()    {}
func (*WeddingPlaymateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{20}
}
func (m *WeddingPlaymateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPlaymateInfo.Unmarshal(m, b)
}
func (m *WeddingPlaymateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPlaymateInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingPlaymateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPlaymateInfo.Merge(dst, src)
}
func (m *WeddingPlaymateInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingPlaymateInfo.Size(m)
}
func (m *WeddingPlaymateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPlaymateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPlaymateInfo proto.InternalMessageInfo

func (m *WeddingPlaymateInfo) GetUserProfile() *UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *WeddingPlaymateInfo) GetPlaymateStatus() uint32 {
	if m != nil {
		return m.PlaymateStatus
	}
	return 0
}

func (m *WeddingPlaymateInfo) GetFellowVal() uint32 {
	if m != nil {
		return m.FellowVal
	}
	return 0
}

// 邀请嘉宾 url: /tt-revenue-http-logic/channel_wedding/invite_wedding_guest
type InviteWeddingGuestRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id"`
	WeddingGuestType     uint32   `protobuf:"varint,2,opt,name=wedding_guest_type,json=weddingGuestType,proto3" json:"wedding_guest_type"`
	TargetUid            uint32   `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	IsCancel             bool     `protobuf:"varint,4,opt,name=is_cancel,json=isCancel,proto3" json:"is_cancel"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InviteWeddingGuestRequest) Reset()         { *m = InviteWeddingGuestRequest{} }
func (m *InviteWeddingGuestRequest) String() string { return proto.CompactTextString(m) }
func (*InviteWeddingGuestRequest) ProtoMessage()    {}
func (*InviteWeddingGuestRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{21}
}
func (m *InviteWeddingGuestRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteWeddingGuestRequest.Unmarshal(m, b)
}
func (m *InviteWeddingGuestRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteWeddingGuestRequest.Marshal(b, m, deterministic)
}
func (dst *InviteWeddingGuestRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteWeddingGuestRequest.Merge(dst, src)
}
func (m *InviteWeddingGuestRequest) XXX_Size() int {
	return xxx_messageInfo_InviteWeddingGuestRequest.Size(m)
}
func (m *InviteWeddingGuestRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteWeddingGuestRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InviteWeddingGuestRequest proto.InternalMessageInfo

func (m *InviteWeddingGuestRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *InviteWeddingGuestRequest) GetWeddingGuestType() uint32 {
	if m != nil {
		return m.WeddingGuestType
	}
	return 0
}

func (m *InviteWeddingGuestRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *InviteWeddingGuestRequest) GetIsCancel() bool {
	if m != nil {
		return m.IsCancel
	}
	return false
}

type InviteWeddingGuestResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InviteWeddingGuestResponse) Reset()         { *m = InviteWeddingGuestResponse{} }
func (m *InviteWeddingGuestResponse) String() string { return proto.CompactTextString(m) }
func (*InviteWeddingGuestResponse) ProtoMessage()    {}
func (*InviteWeddingGuestResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{22}
}
func (m *InviteWeddingGuestResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteWeddingGuestResponse.Unmarshal(m, b)
}
func (m *InviteWeddingGuestResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteWeddingGuestResponse.Marshal(b, m, deterministic)
}
func (dst *InviteWeddingGuestResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteWeddingGuestResponse.Merge(dst, src)
}
func (m *InviteWeddingGuestResponse) XXX_Size() int {
	return xxx_messageInfo_InviteWeddingGuestResponse.Size(m)
}
func (m *InviteWeddingGuestResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteWeddingGuestResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InviteWeddingGuestResponse proto.InternalMessageInfo

// 删除嘉宾 url: /tt-revenue-http-logic/channel_wedding/del_wedding_guest
type DelWeddingGuestRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id"`
	WeddingGuestType     uint32   `protobuf:"varint,2,opt,name=wedding_guest_type,json=weddingGuestType,proto3" json:"wedding_guest_type"`
	TargetUid            uint32   `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelWeddingGuestRequest) Reset()         { *m = DelWeddingGuestRequest{} }
func (m *DelWeddingGuestRequest) String() string { return proto.CompactTextString(m) }
func (*DelWeddingGuestRequest) ProtoMessage()    {}
func (*DelWeddingGuestRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{23}
}
func (m *DelWeddingGuestRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelWeddingGuestRequest.Unmarshal(m, b)
}
func (m *DelWeddingGuestRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelWeddingGuestRequest.Marshal(b, m, deterministic)
}
func (dst *DelWeddingGuestRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelWeddingGuestRequest.Merge(dst, src)
}
func (m *DelWeddingGuestRequest) XXX_Size() int {
	return xxx_messageInfo_DelWeddingGuestRequest.Size(m)
}
func (m *DelWeddingGuestRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DelWeddingGuestRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DelWeddingGuestRequest proto.InternalMessageInfo

func (m *DelWeddingGuestRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *DelWeddingGuestRequest) GetWeddingGuestType() uint32 {
	if m != nil {
		return m.WeddingGuestType
	}
	return 0
}

func (m *DelWeddingGuestRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type DelWeddingGuestResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelWeddingGuestResponse) Reset()         { *m = DelWeddingGuestResponse{} }
func (m *DelWeddingGuestResponse) String() string { return proto.CompactTextString(m) }
func (*DelWeddingGuestResponse) ProtoMessage()    {}
func (*DelWeddingGuestResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{24}
}
func (m *DelWeddingGuestResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelWeddingGuestResponse.Unmarshal(m, b)
}
func (m *DelWeddingGuestResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelWeddingGuestResponse.Marshal(b, m, deterministic)
}
func (dst *DelWeddingGuestResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelWeddingGuestResponse.Merge(dst, src)
}
func (m *DelWeddingGuestResponse) XXX_Size() int {
	return xxx_messageInfo_DelWeddingGuestResponse.Size(m)
}
func (m *DelWeddingGuestResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DelWeddingGuestResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DelWeddingGuestResponse proto.InternalMessageInfo

// 获取基础信息 url: /tt-revenue-http-logic/channel_wedding/get_wedding_plan_base_info
type GetWeddingPlanBaseInfoRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingPlanBaseInfoRequest) Reset()         { *m = GetWeddingPlanBaseInfoRequest{} }
func (m *GetWeddingPlanBaseInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingPlanBaseInfoRequest) ProtoMessage()    {}
func (*GetWeddingPlanBaseInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{25}
}
func (m *GetWeddingPlanBaseInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingPlanBaseInfoRequest.Unmarshal(m, b)
}
func (m *GetWeddingPlanBaseInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingPlanBaseInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingPlanBaseInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingPlanBaseInfoRequest.Merge(dst, src)
}
func (m *GetWeddingPlanBaseInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingPlanBaseInfoRequest.Size(m)
}
func (m *GetWeddingPlanBaseInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingPlanBaseInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingPlanBaseInfoRequest proto.InternalMessageInfo

type GetWeddingPlanBaseInfoResponse struct {
	HasMate              bool     `protobuf:"varint,1,opt,name=has_mate,json=hasMate,proto3" json:"has_mate"`
	IsPay                bool     `protobuf:"varint,2,opt,name=is_pay,json=isPay,proto3" json:"is_pay"`
	IsReserve            bool     `protobuf:"varint,3,opt,name=is_reserve,json=isReserve,proto3" json:"is_reserve"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingPlanBaseInfoResponse) Reset()         { *m = GetWeddingPlanBaseInfoResponse{} }
func (m *GetWeddingPlanBaseInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingPlanBaseInfoResponse) ProtoMessage()    {}
func (*GetWeddingPlanBaseInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{26}
}
func (m *GetWeddingPlanBaseInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingPlanBaseInfoResponse.Unmarshal(m, b)
}
func (m *GetWeddingPlanBaseInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingPlanBaseInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingPlanBaseInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingPlanBaseInfoResponse.Merge(dst, src)
}
func (m *GetWeddingPlanBaseInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingPlanBaseInfoResponse.Size(m)
}
func (m *GetWeddingPlanBaseInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingPlanBaseInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingPlanBaseInfoResponse proto.InternalMessageInfo

func (m *GetWeddingPlanBaseInfoResponse) GetHasMate() bool {
	if m != nil {
		return m.HasMate
	}
	return false
}

func (m *GetWeddingPlanBaseInfoResponse) GetIsPay() bool {
	if m != nil {
		return m.IsPay
	}
	return false
}

func (m *GetWeddingPlanBaseInfoResponse) GetIsReserve() bool {
	if m != nil {
		return m.IsReserve
	}
	return false
}

// 房间内获取婚礼预约列表 url: /tt-revenue-http-logic/channel_wedding/get_inner_channel_wedding_reserved_list
type GetInnerChannelWeddingReservedListRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	PageNum              uint32   `protobuf:"varint,2,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInnerChannelWeddingReservedListRequest) Reset() {
	*m = GetInnerChannelWeddingReservedListRequest{}
}
func (m *GetInnerChannelWeddingReservedListRequest) String() string {
	return proto.CompactTextString(m)
}
func (*GetInnerChannelWeddingReservedListRequest) ProtoMessage() {}
func (*GetInnerChannelWeddingReservedListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{27}
}
func (m *GetInnerChannelWeddingReservedListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInnerChannelWeddingReservedListRequest.Unmarshal(m, b)
}
func (m *GetInnerChannelWeddingReservedListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInnerChannelWeddingReservedListRequest.Marshal(b, m, deterministic)
}
func (dst *GetInnerChannelWeddingReservedListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInnerChannelWeddingReservedListRequest.Merge(dst, src)
}
func (m *GetInnerChannelWeddingReservedListRequest) XXX_Size() int {
	return xxx_messageInfo_GetInnerChannelWeddingReservedListRequest.Size(m)
}
func (m *GetInnerChannelWeddingReservedListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInnerChannelWeddingReservedListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetInnerChannelWeddingReservedListRequest proto.InternalMessageInfo

func (m *GetInnerChannelWeddingReservedListRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetInnerChannelWeddingReservedListRequest) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetInnerChannelWeddingReservedListRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type InnerChannelWeddingReservedInfo struct {
	Groom                *UserProfile `protobuf:"bytes,1,opt,name=groom,proto3" json:"groom"`
	Bride                *UserProfile `protobuf:"bytes,2,opt,name=bride,proto3" json:"bride"`
	ThemeName            string       `protobuf:"bytes,3,opt,name=theme_name,json=themeName,proto3" json:"theme_name"`
	ReserveStartTime     uint32       `protobuf:"varint,4,opt,name=reserve_start_time,json=reserveStartTime,proto3" json:"reserve_start_time"`
	ReserveEndTime       uint32       `protobuf:"varint,5,opt,name=reserve_end_time,json=reserveEndTime,proto3" json:"reserve_end_time"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *InnerChannelWeddingReservedInfo) Reset()         { *m = InnerChannelWeddingReservedInfo{} }
func (m *InnerChannelWeddingReservedInfo) String() string { return proto.CompactTextString(m) }
func (*InnerChannelWeddingReservedInfo) ProtoMessage()    {}
func (*InnerChannelWeddingReservedInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{28}
}
func (m *InnerChannelWeddingReservedInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InnerChannelWeddingReservedInfo.Unmarshal(m, b)
}
func (m *InnerChannelWeddingReservedInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InnerChannelWeddingReservedInfo.Marshal(b, m, deterministic)
}
func (dst *InnerChannelWeddingReservedInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InnerChannelWeddingReservedInfo.Merge(dst, src)
}
func (m *InnerChannelWeddingReservedInfo) XXX_Size() int {
	return xxx_messageInfo_InnerChannelWeddingReservedInfo.Size(m)
}
func (m *InnerChannelWeddingReservedInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_InnerChannelWeddingReservedInfo.DiscardUnknown(m)
}

var xxx_messageInfo_InnerChannelWeddingReservedInfo proto.InternalMessageInfo

func (m *InnerChannelWeddingReservedInfo) GetGroom() *UserProfile {
	if m != nil {
		return m.Groom
	}
	return nil
}

func (m *InnerChannelWeddingReservedInfo) GetBride() *UserProfile {
	if m != nil {
		return m.Bride
	}
	return nil
}

func (m *InnerChannelWeddingReservedInfo) GetThemeName() string {
	if m != nil {
		return m.ThemeName
	}
	return ""
}

func (m *InnerChannelWeddingReservedInfo) GetReserveStartTime() uint32 {
	if m != nil {
		return m.ReserveStartTime
	}
	return 0
}

func (m *InnerChannelWeddingReservedInfo) GetReserveEndTime() uint32 {
	if m != nil {
		return m.ReserveEndTime
	}
	return 0
}

type GetInnerChannelWeddingReservedListResponse struct {
	ReservedList         []*InnerChannelWeddingReservedInfo `protobuf:"bytes,1,rep,name=reserved_list,json=reservedList,proto3" json:"reserved_list"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *GetInnerChannelWeddingReservedListResponse) Reset() {
	*m = GetInnerChannelWeddingReservedListResponse{}
}
func (m *GetInnerChannelWeddingReservedListResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetInnerChannelWeddingReservedListResponse) ProtoMessage() {}
func (*GetInnerChannelWeddingReservedListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{29}
}
func (m *GetInnerChannelWeddingReservedListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInnerChannelWeddingReservedListResponse.Unmarshal(m, b)
}
func (m *GetInnerChannelWeddingReservedListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInnerChannelWeddingReservedListResponse.Marshal(b, m, deterministic)
}
func (dst *GetInnerChannelWeddingReservedListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInnerChannelWeddingReservedListResponse.Merge(dst, src)
}
func (m *GetInnerChannelWeddingReservedListResponse) XXX_Size() int {
	return xxx_messageInfo_GetInnerChannelWeddingReservedListResponse.Size(m)
}
func (m *GetInnerChannelWeddingReservedListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInnerChannelWeddingReservedListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetInnerChannelWeddingReservedListResponse proto.InternalMessageInfo

func (m *GetInnerChannelWeddingReservedListResponse) GetReservedList() []*InnerChannelWeddingReservedInfo {
	if m != nil {
		return m.ReservedList
	}
	return nil
}

// 获取婚礼爱侣榜单 url: /tt-revenue-http-logic/channel_wedding/get_wedding_rank
type GetWeddingRankRequest struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingRankRequest) Reset()         { *m = GetWeddingRankRequest{} }
func (m *GetWeddingRankRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingRankRequest) ProtoMessage()    {}
func (*GetWeddingRankRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{30}
}
func (m *GetWeddingRankRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingRankRequest.Unmarshal(m, b)
}
func (m *GetWeddingRankRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingRankRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingRankRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingRankRequest.Merge(dst, src)
}
func (m *GetWeddingRankRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingRankRequest.Size(m)
}
func (m *GetWeddingRankRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingRankRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingRankRequest proto.InternalMessageInfo

func (m *GetWeddingRankRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

type WeddingRankInfo struct {
	Groom                *UserProfile `protobuf:"bytes,1,opt,name=groom,proto3" json:"groom"`
	Bride                *UserProfile `protobuf:"bytes,2,opt,name=bride,proto3" json:"bride"`
	ThemeName            string       `protobuf:"bytes,3,opt,name=theme_name,json=themeName,proto3" json:"theme_name"`
	ThemeBg              string       `protobuf:"bytes,4,opt,name=theme_bg,json=themeBg,proto3" json:"theme_bg"`
	HappinessVal         uint32       `protobuf:"varint,5,opt,name=happiness_val,json=happinessVal,proto3" json:"happiness_val"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *WeddingRankInfo) Reset()         { *m = WeddingRankInfo{} }
func (m *WeddingRankInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingRankInfo) ProtoMessage()    {}
func (*WeddingRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{31}
}
func (m *WeddingRankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingRankInfo.Unmarshal(m, b)
}
func (m *WeddingRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingRankInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingRankInfo.Merge(dst, src)
}
func (m *WeddingRankInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingRankInfo.Size(m)
}
func (m *WeddingRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingRankInfo proto.InternalMessageInfo

func (m *WeddingRankInfo) GetGroom() *UserProfile {
	if m != nil {
		return m.Groom
	}
	return nil
}

func (m *WeddingRankInfo) GetBride() *UserProfile {
	if m != nil {
		return m.Bride
	}
	return nil
}

func (m *WeddingRankInfo) GetThemeName() string {
	if m != nil {
		return m.ThemeName
	}
	return ""
}

func (m *WeddingRankInfo) GetThemeBg() string {
	if m != nil {
		return m.ThemeBg
	}
	return ""
}

func (m *WeddingRankInfo) GetHappinessVal() uint32 {
	if m != nil {
		return m.HappinessVal
	}
	return 0
}

type GetWeddingRankResponse struct {
	RankList             []*WeddingRankInfo `protobuf:"bytes,1,rep,name=rank_list,json=rankList,proto3" json:"rank_list"`
	RankDesc             string             `protobuf:"bytes,2,opt,name=rank_desc,json=rankDesc,proto3" json:"rank_desc"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetWeddingRankResponse) Reset()         { *m = GetWeddingRankResponse{} }
func (m *GetWeddingRankResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingRankResponse) ProtoMessage()    {}
func (*GetWeddingRankResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{32}
}
func (m *GetWeddingRankResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingRankResponse.Unmarshal(m, b)
}
func (m *GetWeddingRankResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingRankResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingRankResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingRankResponse.Merge(dst, src)
}
func (m *GetWeddingRankResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingRankResponse.Size(m)
}
func (m *GetWeddingRankResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingRankResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingRankResponse proto.InternalMessageInfo

func (m *GetWeddingRankResponse) GetRankList() []*WeddingRankInfo {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetWeddingRankResponse) GetRankDesc() string {
	if m != nil {
		return m.RankDesc
	}
	return ""
}

// 客服安排婚礼预约 url: /tt-revenue-http-logic/channel_wedding/arrange_wedding_reserve
type ArrangeWeddingReserveRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	ReserveDate          uint32   `protobuf:"varint,5,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date"`
	ReserveTime          []string `protobuf:"bytes,2,rep,name=reserve_time,json=reserveTime,proto3" json:"reserve_time"`
	IsHot                bool     `protobuf:"varint,3,opt,name=is_hot,json=isHot,proto3" json:"is_hot"`
	GiftId               uint32   `protobuf:"varint,4,opt,name=gift_id,json=giftId,proto3" json:"gift_id"`
	SourceMsgId          uint32   `protobuf:"varint,6,opt,name=source_msg_id,json=sourceMsgId,proto3" json:"source_msg_id"`
	ThemeId              uint32   `protobuf:"varint,7,opt,name=theme_id,json=themeId,proto3" json:"theme_id"`
	TargetUid            uint32   `protobuf:"varint,8,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArrangeWeddingReserveRequest) Reset()         { *m = ArrangeWeddingReserveRequest{} }
func (m *ArrangeWeddingReserveRequest) String() string { return proto.CompactTextString(m) }
func (*ArrangeWeddingReserveRequest) ProtoMessage()    {}
func (*ArrangeWeddingReserveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{33}
}
func (m *ArrangeWeddingReserveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArrangeWeddingReserveRequest.Unmarshal(m, b)
}
func (m *ArrangeWeddingReserveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArrangeWeddingReserveRequest.Marshal(b, m, deterministic)
}
func (dst *ArrangeWeddingReserveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArrangeWeddingReserveRequest.Merge(dst, src)
}
func (m *ArrangeWeddingReserveRequest) XXX_Size() int {
	return xxx_messageInfo_ArrangeWeddingReserveRequest.Size(m)
}
func (m *ArrangeWeddingReserveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ArrangeWeddingReserveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ArrangeWeddingReserveRequest proto.InternalMessageInfo

func (m *ArrangeWeddingReserveRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ArrangeWeddingReserveRequest) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *ArrangeWeddingReserveRequest) GetReserveTime() []string {
	if m != nil {
		return m.ReserveTime
	}
	return nil
}

func (m *ArrangeWeddingReserveRequest) GetIsHot() bool {
	if m != nil {
		return m.IsHot
	}
	return false
}

func (m *ArrangeWeddingReserveRequest) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *ArrangeWeddingReserveRequest) GetSourceMsgId() uint32 {
	if m != nil {
		return m.SourceMsgId
	}
	return 0
}

func (m *ArrangeWeddingReserveRequest) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *ArrangeWeddingReserveRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type ArrangeWeddingReserveResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArrangeWeddingReserveResponse) Reset()         { *m = ArrangeWeddingReserveResponse{} }
func (m *ArrangeWeddingReserveResponse) String() string { return proto.CompactTextString(m) }
func (*ArrangeWeddingReserveResponse) ProtoMessage()    {}
func (*ArrangeWeddingReserveResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{34}
}
func (m *ArrangeWeddingReserveResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArrangeWeddingReserveResponse.Unmarshal(m, b)
}
func (m *ArrangeWeddingReserveResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArrangeWeddingReserveResponse.Marshal(b, m, deterministic)
}
func (dst *ArrangeWeddingReserveResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArrangeWeddingReserveResponse.Merge(dst, src)
}
func (m *ArrangeWeddingReserveResponse) XXX_Size() int {
	return xxx_messageInfo_ArrangeWeddingReserveResponse.Size(m)
}
func (m *ArrangeWeddingReserveResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ArrangeWeddingReserveResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ArrangeWeddingReserveResponse proto.InternalMessageInfo

// 咨询婚礼预约 url: /tt-revenue-http-logic/channel_wedding/consult_wedding_reserve
type ConsultWeddingReserveRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	ReserveDate          uint32   `protobuf:"varint,2,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date"`
	ReserveTime          []string `protobuf:"bytes,3,rep,name=reserve_time,json=reserveTime,proto3" json:"reserve_time"`
	ThemeId              uint32   `protobuf:"varint,4,opt,name=theme_id,json=themeId,proto3" json:"theme_id"`
	ManagerTtid          string   `protobuf:"bytes,5,opt,name=manager_ttid,json=managerTtid,proto3" json:"manager_ttid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsultWeddingReserveRequest) Reset()         { *m = ConsultWeddingReserveRequest{} }
func (m *ConsultWeddingReserveRequest) String() string { return proto.CompactTextString(m) }
func (*ConsultWeddingReserveRequest) ProtoMessage()    {}
func (*ConsultWeddingReserveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{35}
}
func (m *ConsultWeddingReserveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsultWeddingReserveRequest.Unmarshal(m, b)
}
func (m *ConsultWeddingReserveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsultWeddingReserveRequest.Marshal(b, m, deterministic)
}
func (dst *ConsultWeddingReserveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsultWeddingReserveRequest.Merge(dst, src)
}
func (m *ConsultWeddingReserveRequest) XXX_Size() int {
	return xxx_messageInfo_ConsultWeddingReserveRequest.Size(m)
}
func (m *ConsultWeddingReserveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsultWeddingReserveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ConsultWeddingReserveRequest proto.InternalMessageInfo

func (m *ConsultWeddingReserveRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ConsultWeddingReserveRequest) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *ConsultWeddingReserveRequest) GetReserveTime() []string {
	if m != nil {
		return m.ReserveTime
	}
	return nil
}

func (m *ConsultWeddingReserveRequest) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *ConsultWeddingReserveRequest) GetManagerTtid() string {
	if m != nil {
		return m.ManagerTtid
	}
	return ""
}

type ConsultWeddingReserveResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsultWeddingReserveResponse) Reset()         { *m = ConsultWeddingReserveResponse{} }
func (m *ConsultWeddingReserveResponse) String() string { return proto.CompactTextString(m) }
func (*ConsultWeddingReserveResponse) ProtoMessage()    {}
func (*ConsultWeddingReserveResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{36}
}
func (m *ConsultWeddingReserveResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsultWeddingReserveResponse.Unmarshal(m, b)
}
func (m *ConsultWeddingReserveResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsultWeddingReserveResponse.Marshal(b, m, deterministic)
}
func (dst *ConsultWeddingReserveResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsultWeddingReserveResponse.Merge(dst, src)
}
func (m *ConsultWeddingReserveResponse) XXX_Size() int {
	return xxx_messageInfo_ConsultWeddingReserveResponse.Size(m)
}
func (m *ConsultWeddingReserveResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsultWeddingReserveResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ConsultWeddingReserveResponse proto.InternalMessageInfo

// 获取可预约信息 url: /tt-revenue-http-logic/channel_wedding/get_channel_reserved_info
type GetChannelReservedInfoRequest struct {
	ReserveDate          uint32   `protobuf:"varint,1,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	ThemeType            uint32   `protobuf:"varint,3,opt,name=theme_type,json=themeType,proto3" json:"theme_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelReservedInfoRequest) Reset()         { *m = GetChannelReservedInfoRequest{} }
func (m *GetChannelReservedInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetChannelReservedInfoRequest) ProtoMessage()    {}
func (*GetChannelReservedInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{37}
}
func (m *GetChannelReservedInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelReservedInfoRequest.Unmarshal(m, b)
}
func (m *GetChannelReservedInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelReservedInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetChannelReservedInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelReservedInfoRequest.Merge(dst, src)
}
func (m *GetChannelReservedInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetChannelReservedInfoRequest.Size(m)
}
func (m *GetChannelReservedInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelReservedInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelReservedInfoRequest proto.InternalMessageInfo

func (m *GetChannelReservedInfoRequest) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *GetChannelReservedInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelReservedInfoRequest) GetThemeType() uint32 {
	if m != nil {
		return m.ThemeType
	}
	return 0
}

type GetChannelReservedInfoResponse struct {
	ReserveTimeInfoList  []*ChannelReserveTimeInfo `protobuf:"bytes,1,rep,name=reserve_time_info_list,json=reserveTimeInfoList,proto3" json:"reserve_time_info_list"`
	MinReserveDate       uint32                    `protobuf:"varint,2,opt,name=min_reserve_date,json=minReserveDate,proto3" json:"min_reserve_date"`
	MaxReserveDate       uint32                    `protobuf:"varint,3,opt,name=max_reserve_date,json=maxReserveDate,proto3" json:"max_reserve_date"`
	IsGuild              bool                      `protobuf:"varint,4,opt,name=is_guild,json=isGuild,proto3" json:"is_guild"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetChannelReservedInfoResponse) Reset()         { *m = GetChannelReservedInfoResponse{} }
func (m *GetChannelReservedInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetChannelReservedInfoResponse) ProtoMessage()    {}
func (*GetChannelReservedInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{38}
}
func (m *GetChannelReservedInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelReservedInfoResponse.Unmarshal(m, b)
}
func (m *GetChannelReservedInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelReservedInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetChannelReservedInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelReservedInfoResponse.Merge(dst, src)
}
func (m *GetChannelReservedInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetChannelReservedInfoResponse.Size(m)
}
func (m *GetChannelReservedInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelReservedInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelReservedInfoResponse proto.InternalMessageInfo

func (m *GetChannelReservedInfoResponse) GetReserveTimeInfoList() []*ChannelReserveTimeInfo {
	if m != nil {
		return m.ReserveTimeInfoList
	}
	return nil
}

func (m *GetChannelReservedInfoResponse) GetMinReserveDate() uint32 {
	if m != nil {
		return m.MinReserveDate
	}
	return 0
}

func (m *GetChannelReservedInfoResponse) GetMaxReserveDate() uint32 {
	if m != nil {
		return m.MaxReserveDate
	}
	return 0
}

func (m *GetChannelReservedInfoResponse) GetIsGuild() bool {
	if m != nil {
		return m.IsGuild
	}
	return false
}

// 预约时段信息
type ChannelReserveTimeInfo struct {
	ReserveTime          string       `protobuf:"bytes,1,opt,name=reserve_time,json=reserveTime,proto3" json:"reserve_time"`
	Groom                *UserProfile `protobuf:"bytes,2,opt,name=groom,proto3" json:"groom"`
	Bride                *UserProfile `protobuf:"bytes,3,opt,name=bride,proto3" json:"bride"`
	IsHot                bool         `protobuf:"varint,4,opt,name=is_hot,json=isHot,proto3" json:"is_hot"`
	ThemeName            string       `protobuf:"bytes,5,opt,name=theme_name,json=themeName,proto3" json:"theme_name"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChannelReserveTimeInfo) Reset()         { *m = ChannelReserveTimeInfo{} }
func (m *ChannelReserveTimeInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelReserveTimeInfo) ProtoMessage()    {}
func (*ChannelReserveTimeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_http_c35ae72ce3cfa793, []int{39}
}
func (m *ChannelReserveTimeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelReserveTimeInfo.Unmarshal(m, b)
}
func (m *ChannelReserveTimeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelReserveTimeInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelReserveTimeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelReserveTimeInfo.Merge(dst, src)
}
func (m *ChannelReserveTimeInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelReserveTimeInfo.Size(m)
}
func (m *ChannelReserveTimeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelReserveTimeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelReserveTimeInfo proto.InternalMessageInfo

func (m *ChannelReserveTimeInfo) GetReserveTime() string {
	if m != nil {
		return m.ReserveTime
	}
	return ""
}

func (m *ChannelReserveTimeInfo) GetGroom() *UserProfile {
	if m != nil {
		return m.Groom
	}
	return nil
}

func (m *ChannelReserveTimeInfo) GetBride() *UserProfile {
	if m != nil {
		return m.Bride
	}
	return nil
}

func (m *ChannelReserveTimeInfo) GetIsHot() bool {
	if m != nil {
		return m.IsHot
	}
	return false
}

func (m *ChannelReserveTimeInfo) GetThemeName() string {
	if m != nil {
		return m.ThemeName
	}
	return ""
}

func init() {
	proto.RegisterType((*GetMyWeddingReserveInfoRequest)(nil), "channel_wedding_http.GetMyWeddingReserveInfoRequest")
	proto.RegisterType((*GetMyWeddingReserveInfoResponse)(nil), "channel_wedding_http.GetMyWeddingReserveInfoResponse")
	proto.RegisterType((*GetWeddingReserveInfoRequest)(nil), "channel_wedding_http.GetWeddingReserveInfoRequest")
	proto.RegisterType((*WeddingChannelInfo)(nil), "channel_wedding_http.WeddingChannelInfo")
	proto.RegisterType((*ReserveTimeInfo)(nil), "channel_wedding_http.ReserveTimeInfo")
	proto.RegisterType((*GiftInfo)(nil), "channel_wedding_http.GiftInfo")
	proto.RegisterType((*GetWeddingReserveInfoResponse)(nil), "channel_wedding_http.GetWeddingReserveInfoResponse")
	proto.RegisterType((*LabelInfo)(nil), "channel_wedding_http.LabelInfo")
	proto.RegisterType((*SaveWeddingReserveRequest)(nil), "channel_wedding_http.SaveWeddingReserveRequest")
	proto.RegisterType((*SaveWeddingReserveResponse)(nil), "channel_wedding_http.SaveWeddingReserveResponse")
	proto.RegisterType((*ChangeWeddingReserveRequest)(nil), "channel_wedding_http.ChangeWeddingReserveRequest")
	proto.RegisterType((*ChangeWeddingReserveResponse)(nil), "channel_wedding_http.ChangeWeddingReserveResponse")
	proto.RegisterType((*UserProfile)(nil), "channel_wedding_http.UserProfile")
	proto.RegisterType((*WeddingGuestInfo)(nil), "channel_wedding_http.WeddingGuestInfo")
	proto.RegisterType((*GetGroomsmanAndBridesmaidInfoRequest)(nil), "channel_wedding_http.GetGroomsmanAndBridesmaidInfoRequest")
	proto.RegisterType((*GetGroomsmanAndBridesmaidInfoResponse)(nil), "channel_wedding_http.GetGroomsmanAndBridesmaidInfoResponse")
	proto.RegisterType((*GetWeddingFriendInfoRequest)(nil), "channel_wedding_http.GetWeddingFriendInfoRequest")
	proto.RegisterType((*GetWeddingFriendInfoResponse)(nil), "channel_wedding_http.GetWeddingFriendInfoResponse")
	proto.RegisterType((*GetPlaymateListRequest)(nil), "channel_wedding_http.GetPlaymateListRequest")
	proto.RegisterType((*GetPlaymateListResponse)(nil), "channel_wedding_http.GetPlaymateListResponse")
	proto.RegisterType((*WeddingPlaymateInfo)(nil), "channel_wedding_http.WeddingPlaymateInfo")
	proto.RegisterType((*InviteWeddingGuestRequest)(nil), "channel_wedding_http.InviteWeddingGuestRequest")
	proto.RegisterType((*InviteWeddingGuestResponse)(nil), "channel_wedding_http.InviteWeddingGuestResponse")
	proto.RegisterType((*DelWeddingGuestRequest)(nil), "channel_wedding_http.DelWeddingGuestRequest")
	proto.RegisterType((*DelWeddingGuestResponse)(nil), "channel_wedding_http.DelWeddingGuestResponse")
	proto.RegisterType((*GetWeddingPlanBaseInfoRequest)(nil), "channel_wedding_http.GetWeddingPlanBaseInfoRequest")
	proto.RegisterType((*GetWeddingPlanBaseInfoResponse)(nil), "channel_wedding_http.GetWeddingPlanBaseInfoResponse")
	proto.RegisterType((*GetInnerChannelWeddingReservedListRequest)(nil), "channel_wedding_http.GetInnerChannelWeddingReservedListRequest")
	proto.RegisterType((*InnerChannelWeddingReservedInfo)(nil), "channel_wedding_http.InnerChannelWeddingReservedInfo")
	proto.RegisterType((*GetInnerChannelWeddingReservedListResponse)(nil), "channel_wedding_http.GetInnerChannelWeddingReservedListResponse")
	proto.RegisterType((*GetWeddingRankRequest)(nil), "channel_wedding_http.GetWeddingRankRequest")
	proto.RegisterType((*WeddingRankInfo)(nil), "channel_wedding_http.WeddingRankInfo")
	proto.RegisterType((*GetWeddingRankResponse)(nil), "channel_wedding_http.GetWeddingRankResponse")
	proto.RegisterType((*ArrangeWeddingReserveRequest)(nil), "channel_wedding_http.ArrangeWeddingReserveRequest")
	proto.RegisterType((*ArrangeWeddingReserveResponse)(nil), "channel_wedding_http.ArrangeWeddingReserveResponse")
	proto.RegisterType((*ConsultWeddingReserveRequest)(nil), "channel_wedding_http.ConsultWeddingReserveRequest")
	proto.RegisterType((*ConsultWeddingReserveResponse)(nil), "channel_wedding_http.ConsultWeddingReserveResponse")
	proto.RegisterType((*GetChannelReservedInfoRequest)(nil), "channel_wedding_http.GetChannelReservedInfoRequest")
	proto.RegisterType((*GetChannelReservedInfoResponse)(nil), "channel_wedding_http.GetChannelReservedInfoResponse")
	proto.RegisterType((*ChannelReserveTimeInfo)(nil), "channel_wedding_http.ChannelReserveTimeInfo")
	proto.RegisterEnum("channel_wedding_http.WeddingGuestType", WeddingGuestType_name, WeddingGuestType_value)
	proto.RegisterEnum("channel_wedding_http.ThemeType", ThemeType_name, ThemeType_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-wedding-http/channel-wedding-http.proto", fileDescriptor_channel_wedding_http_c35ae72ce3cfa793)
}

var fileDescriptor_channel_wedding_http_c35ae72ce3cfa793 = []byte{
	// 2078 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x19, 0x4d, 0x6f, 0xdb, 0xc8,
	0xb5, 0x94, 0x2c, 0x8b, 0x7a, 0x92, 0x2d, 0x85, 0xc9, 0x3a, 0xf2, 0x3a, 0x89, 0x13, 0x36, 0x49,
	0x1d, 0x23, 0x9b, 0x2d, 0xb6, 0x08, 0x7a, 0x29, 0x0a, 0xf8, 0x2b, 0x8a, 0x90, 0x58, 0x71, 0x69,
	0xa7, 0x8b, 0xcd, 0x85, 0x18, 0x8b, 0x23, 0x69, 0x10, 0x7e, 0x28, 0x1c, 0xd2, 0x89, 0xf6, 0xb4,
	0xe8, 0xa9, 0x2d, 0xd0, 0x43, 0x7b, 0x68, 0x81, 0x5e, 0x7b, 0x29, 0x0a, 0xec, 0x6f, 0x28, 0xfa,
	0x23, 0xfa, 0x07, 0xda, 0x6b, 0x4f, 0x3d, 0xf5, 0x58, 0xcc, 0x9b, 0x21, 0x45, 0x4a, 0x74, 0xfc,
	0x51, 0x1f, 0xb6, 0x37, 0xf1, 0xbd, 0x37, 0x6f, 0xde, 0xf7, 0x9b, 0xf7, 0x04, 0x3f, 0x8d, 0xa2,
	0xcf, 0xdf, 0xc5, 0xac, 0xff, 0x96, 0x33, 0xf7, 0x84, 0x86, 0x9f, 0xf7, 0x47, 0xc4, 0xf7, 0xa9,
	0xfb, 0xd9, 0x7b, 0xea, 0x38, 0xcc, 0x1f, 0x7e, 0x36, 0x8a, 0xa2, 0x71, 0x21, 0xf0, 0xc9, 0x38,
	0x0c, 0xa2, 0xc0, 0xb8, 0xa1, 0x70, 0xb6, 0xc2, 0xd9, 0x02, 0x67, 0x3e, 0x87, 0x3b, 0x1d, 0x1a,
	0xed, 0x4f, 0xbe, 0x94, 0x40, 0x8b, 0x72, 0x1a, 0x9e, 0xd0, 0xae, 0x3f, 0x08, 0x2c, 0xfa, 0x2e,
	0xa6, 0x3c, 0x32, 0x1e, 0x42, 0x33, 0x39, 0x31, 0x76, 0x89, 0x6f, 0x33, 0xa7, 0xad, 0xdd, 0xd5,
	0x36, 0x96, 0xac, 0x25, 0x05, 0x3e, 0x70, 0x89, 0xdf, 0x75, 0xcc, 0x3f, 0x94, 0x60, 0xfd, 0x54,
	0x56, 0x7c, 0x1c, 0xf8, 0x9c, 0x1a, 0xf7, 0xa0, 0x11, 0x4a, 0xb0, 0xed, 0x90, 0x88, 0x2a, 0x46,
	0x75, 0x05, 0xdb, 0x25, 0x11, 0x35, 0x6e, 0x03, 0x24, 0x82, 0x32, 0xa7, 0x5d, 0x42, 0x82, 0x9a,
	0x82, 0x74, 0x1d, 0xe3, 0x87, 0x70, 0x23, 0xe1, 0x10, 0x31, 0x8f, 0xda, 0x9c, 0xf6, 0x23, 0x16,
	0xf8, 0xed, 0xf2, 0xdd, 0xf2, 0x46, 0xcd, 0x32, 0x14, 0xee, 0x88, 0x79, 0xf4, 0x50, 0x62, 0x8c,
	0x27, 0x70, 0x3d, 0xa4, 0x1e, 0x61, 0xbe, 0x2d, 0xb8, 0x0c, 0xe5, 0x39, 0xde, 0x5e, 0x40, 0xce,
	0xd7, 0x24, 0x6a, 0x07, 0x31, 0xe2, 0x14, 0x37, 0x36, 0xe1, 0x9a, 0x22, 0x74, 0x99, 0xc7, 0x22,
	0x24, 0x6f, 0x57, 0x90, 0xba, 0x29, 0x11, 0x2f, 0x05, 0x5c, 0x10, 0x1b, 0xf7, 0x61, 0x39, 0xcf,
	0xb7, 0xbd, 0x78, 0x57, 0xdb, 0xd0, 0xad, 0x46, 0x96, 0xa5, 0xf9, 0x47, 0x0d, 0x6e, 0x75, 0x68,
	0x74, 0xba, 0x89, 0x6f, 0x03, 0x44, 0x23, 0xea, 0x51, 0x3b, 0x9a, 0x8c, 0x13, 0xa3, 0xd4, 0x10,
	0x72, 0x34, 0x19, 0xcf, 0x5b, 0xad, 0x74, 0x96, 0xd5, 0xca, 0xb3, 0x56, 0x5b, 0x05, 0x5d, 0x5e,
	0xc0, 0x1c, 0xa5, 0x78, 0x15, 0xbf, 0xbb, 0x8e, 0xf9, 0x57, 0x0d, 0x0c, 0x25, 0xd9, 0x8e, 0xa2,
	0xf7, 0x07, 0xc1, 0x0c, 0x43, 0x6d, 0x96, 0xe1, 0x3d, 0x68, 0x24, 0x68, 0x9f, 0x78, 0x52, 0xa4,
	0x9a, 0x55, 0x57, 0xb0, 0x1e, 0xf1, 0xa8, 0xb8, 0x73, 0x18, 0x33, 0xd7, 0x99, 0x0a, 0x54, 0xc5,
	0x6f, 0x79, 0xda, 0x23, 0x3e, 0x19, 0xd2, 0xd0, 0x8e, 0x22, 0x25, 0x52, 0xcd, 0xaa, 0x2b, 0xd8,
	0x51, 0xc4, 0x1c, 0xe3, 0x11, 0xb4, 0x12, 0x12, 0x9f, 0xf5, 0xdf, 0xe2, 0x25, 0x15, 0x24, 0x6b,
	0x2a, 0x78, 0x4f, 0x81, 0xcd, 0xff, 0x68, 0xd0, 0xb4, 0xa6, 0x7e, 0x47, 0xf1, 0x4f, 0x0b, 0x13,
	0x0d, 0x59, 0x14, 0x85, 0xc9, 0x26, 0x5c, 0x63, 0xdc, 0x1e, 0xc4, 0xae, 0x3b, 0xb1, 0x15, 0x5a,
	0x86, 0x9f, 0x6e, 0x35, 0x19, 0x7f, 0x26, 0xe0, 0xea, 0x12, 0xc7, 0xf8, 0x31, 0x54, 0x86, 0x61,
	0x10, 0x78, 0xa8, 0x57, 0xfd, 0x8b, 0x7b, 0x4f, 0x8a, 0x52, 0xeb, 0xc9, 0x6b, 0x4e, 0xc3, 0x83,
	0x30, 0x18, 0x30, 0x97, 0x5a, 0x92, 0x5e, 0x1c, 0x3c, 0x0e, 0x99, 0x43, 0x51, 0xe3, 0xf3, 0x1d,
	0x44, 0x7a, 0xe3, 0x13, 0x58, 0x64, 0xdc, 0x1e, 0x05, 0x11, 0x1a, 0x41, 0xb7, 0x2a, 0x8c, 0x3f,
	0x0f, 0x22, 0xf3, 0x5b, 0x0d, 0xf4, 0x0e, 0x1b, 0x44, 0xa8, 0xf3, 0x4d, 0xa8, 0x0e, 0xd9, 0x20,
	0x9a, 0xfa, 0x6b, 0x51, 0x7c, 0x76, 0x1d, 0x63, 0x0d, 0x6a, 0x88, 0xc8, 0x78, 0x4a, 0x17, 0x00,
	0x74, 0x53, 0x82, 0x74, 0x28, 0xef, 0xa3, 0x3e, 0x0a, 0xb9, 0x4b, 0x79, 0x3f, 0x45, 0xb2, 0x7e,
	0xe0, 0x2b, 0x2f, 0x21, 0xb2, 0xdb, 0x0f, 0x7c, 0xe3, 0x06, 0x54, 0xde, 0x07, 0x61, 0x34, 0x52,
	0xc9, 0x21, 0x3f, 0xc4, 0x91, 0xe3, 0x78, 0x62, 0x8f, 0x43, 0xd6, 0x97, 0xd9, 0xb0, 0x64, 0xe9,
	0xc7, 0xf1, 0xe4, 0x40, 0x7c, 0x9b, 0xbf, 0xaa, 0xc0, 0xed, 0x53, 0x32, 0x41, 0x55, 0x88, 0xfb,
	0xb0, 0xdc, 0x8f, 0x43, 0x7b, 0x2e, 0xf6, 0x1a, 0xfd, 0x38, 0xdc, 0x49, 0xc3, 0x6f, 0x03, 0x5a,
	0x82, 0xaa, 0x20, 0x2b, 0xc4, 0x69, 0x2b, 0x93, 0x18, 0xc7, 0xd0, 0x56, 0x54, 0xe4, 0xd8, 0xa5,
	0x29, 0x5f, 0x97, 0xf1, 0x08, 0x6b, 0x46, 0xfd, 0x8b, 0x8d, 0x62, 0x27, 0xcc, 0xe7, 0x84, 0xb5,
	0x92, 0xe1, 0xa4, 0xe0, 0x2f, 0x19, 0x8f, 0x8c, 0x37, 0xb0, 0x92, 0x0b, 0x36, 0xe6, 0x0f, 0x02,
	0x79, 0xc3, 0x02, 0xde, 0xf0, 0xa0, 0xf8, 0x86, 0x99, 0x98, 0xb5, 0xae, 0x87, 0x79, 0x00, 0xf2,
	0xde, 0x80, 0x96, 0xc7, 0xfc, 0xbc, 0xa6, 0xd2, 0xde, 0xcb, 0x1e, 0xf3, 0xb3, 0x9a, 0x0a, 0x4a,
	0xf2, 0x21, 0x4f, 0xb9, 0xa8, 0x28, 0xc9, 0x87, 0x2c, 0xe5, 0x43, 0x68, 0x66, 0x29, 0xfd, 0xd8,
	0x6b, 0x57, 0x65, 0x45, 0x9f, 0x12, 0xf6, 0x62, 0xcf, 0x78, 0x01, 0xc6, 0x28, 0x90, 0x05, 0xd0,
	0xc6, 0x30, 0x40, 0x9d, 0x74, 0xd4, 0xe9, 0x4e, 0xb1, 0x4e, 0x49, 0x30, 0x5a, 0xcd, 0x51, 0x80,
	0x25, 0x52, 0x00, 0x50, 0x91, 0x9f, 0xc1, 0x27, 0x7e, 0x10, 0x7a, 0xc4, 0x9d, 0xe5, 0x57, 0x3b,
	0x17, 0x3f, 0x43, 0x1e, 0xce, 0xb1, 0xfc, 0x09, 0xd4, 0x84, 0x7c, 0x2e, 0x39, 0xa6, 0x6e, 0x1b,
	0x30, 0xa3, 0xd6, 0x8b, 0xd9, 0xbc, 0x14, 0x24, 0xc8, 0x47, 0x1f, 0x05, 0x11, 0x7e, 0x99, 0x23,
	0xa8, 0xa5, 0x60, 0x51, 0xee, 0x90, 0x8d, 0xcc, 0x11, 0x59, 0x25, 0x6a, 0x08, 0xc1, 0x24, 0x49,
	0xd1, 0x98, 0x08, 0xa5, 0x0c, 0x1a, 0x33, 0x21, 0x45, 0x67, 0x92, 0x48, 0xa2, 0x45, 0x16, 0x99,
	0x7f, 0xd6, 0x60, 0xf5, 0x90, 0x9c, 0xd0, 0x7c, 0xd8, 0x5f, 0xb0, 0xbf, 0x5e, 0x41, 0x17, 0xc8,
	0x70, 0xc0, 0x5e, 0xb5, 0x80, 0x3d, 0xb3, 0x9e, 0x09, 0x3b, 0xf3, 0x16, 0x7c, 0x5a, 0x24, 0xa9,
	0x4c, 0x4e, 0xf3, 0x2f, 0x1a, 0xac, 0xc9, 0xbe, 0xf6, 0x7f, 0xa0, 0xca, 0x1d, 0xb8, 0x55, 0x2c,
	0xab, 0x52, 0xe6, 0xef, 0x1a, 0xd4, 0x33, 0x95, 0xd6, 0x68, 0x41, 0x39, 0x4e, 0x05, 0x16, 0x3f,
	0x8d, 0x36, 0x54, 0x49, 0xbf, 0x1f, 0xc4, 0x7e, 0xa4, 0x5c, 0x9e, 0x7c, 0x1a, 0x9f, 0x82, 0x9e,
	0x76, 0x25, 0x55, 0x33, 0x93, 0x6f, 0xe3, 0xfb, 0xb0, 0xa4, 0xc8, 0x6c, 0xe2, 0x32, 0xc2, 0x55,
	0xdd, 0x6c, 0x28, 0xe0, 0x96, 0x80, 0x89, 0xcb, 0x38, 0xfd, 0xa0, 0x32, 0x59, 0xfc, 0x34, 0xee,
	0x42, 0x63, 0x44, 0x89, 0x63, 0x33, 0x6f, 0x68, 0x7b, 0xce, 0x53, 0x4c, 0xdd, 0x9a, 0x05, 0x02,
	0xd6, 0xf5, 0x86, 0xfb, 0xce, 0x53, 0xe3, 0x01, 0x34, 0x91, 0xc2, 0x99, 0xa4, 0x44, 0x55, 0xc9,
	0x5a, 0x80, 0x77, 0x27, 0x92, 0xcc, 0xfc, 0xbd, 0x06, 0x2d, 0xa5, 0x72, 0x47, 0x78, 0x05, 0xe3,
	0x7b, 0x17, 0x1a, 0x31, 0xa7, 0xa1, 0x3d, 0x96, 0xca, 0xa2, 0x96, 0xe7, 0xea, 0x3f, 0xf5, 0x38,
	0x63, 0xa2, 0xdb, 0x00, 0xcc, 0x3f, 0x61, 0x11, 0xb5, 0xe3, 0xe9, 0xdb, 0x4c, 0x42, 0x5e, 0x33,
	0x47, 0xa0, 0x07, 0xd4, 0x75, 0x83, 0xf7, 0xf6, 0x09, 0x71, 0x13, 0x9f, 0x49, 0xc8, 0xcf, 0x89,
	0x6b, 0xf6, 0xe0, 0x7e, 0x87, 0x46, 0x1d, 0xd1, 0x08, 0xb9, 0x47, 0xfc, 0x2d, 0xdf, 0xd9, 0x16,
	0xbd, 0x8d, 0x7b, 0x84, 0x39, 0x97, 0x79, 0x70, 0x7e, 0xb3, 0x00, 0x0f, 0xce, 0x60, 0xa8, 0x9a,
	0xca, 0x2b, 0x68, 0x1e, 0xa7, 0x18, 0x59, 0x75, 0x34, 0xac, 0x3a, 0x0f, 0x3f, 0x5a, 0xfb, 0x53,
	0xf3, 0x59, 0xcb, 0xd3, 0xe3, 0x58, 0x79, 0x1e, 0x83, 0x21, 0x2a, 0x68, 0x86, 0xa9, 0x28, 0xa2,
	0xd2, 0x20, 0xa2, 0x0a, 0x4f, 0xe5, 0x10, 0x75, 0x74, 0x1f, 0x96, 0x87, 0x89, 0x90, 0xd9, 0xce,
	0x73, 0xde, 0xdb, 0x97, 0xd2, 0xd3, 0x78, 0xf9, 0x26, 0x5c, 0x13, 0x97, 0x4f, 0x59, 0x8a, 0xbb,
	0xe5, 0xab, 0x4e, 0xd4, 0xf5, 0xd4, 0x1e, 0xe2, 0xea, 0x2e, 0x34, 0xa4, 0x7f, 0x94, 0xda, 0x95,
	0x0b, 0x5d, 0x5c, 0x57, 0x67, 0xf1, 0xda, 0x0e, 0xd4, 0xc9, 0x30, 0xa4, 0x09, 0xa7, 0xc5, 0x0b,
	0x71, 0x02, 0x79, 0x14, 0x19, 0x75, 0x45, 0xee, 0x0e, 0x62, 0x9e, 0x70, 0xaa, 0x5e, 0x4c, 0x26,
	0x75, 0x56, 0xb0, 0x32, 0xf7, 0x60, 0x6d, 0xfa, 0x9c, 0x78, 0x16, 0x32, 0xea, 0x5f, 0x2a, 0x92,
	0xfe, 0x55, 0xca, 0x3e, 0xd0, 0xb3, 0x7c, 0x54, 0x00, 0x75, 0xa0, 0x3e, 0x40, 0xe8, 0x65, 0x82,
	0x07, 0xe4, 0xd1, 0x44, 0xf7, 0x9c, 0x3f, 0x4a, 0x57, 0xe6, 0x8f, 0xf2, 0x95, 0xf9, 0x63, 0xe1,
	0xd2, 0xfe, 0x10, 0xaf, 0x37, 0x11, 0x9a, 0xca, 0x56, 0x22, 0x2e, 0x65, 0x85, 0x6b, 0x78, 0xe4,
	0x83, 0x34, 0x6b, 0x2f, 0xf6, 0xcc, 0xdf, 0x69, 0xb0, 0xd2, 0xa1, 0xd1, 0x81, 0x4b, 0x26, 0x1e,
	0x89, 0xa8, 0x38, 0x79, 0xd1, 0x0e, 0xb2, 0x0a, 0xfa, 0x98, 0x0c, 0x69, 0x26, 0xed, 0xaa, 0xe2,
	0x5b, 0x84, 0xfc, 0x1a, 0xd4, 0x10, 0xc5, 0xd9, 0xd7, 0x54, 0x15, 0x21, 0xa4, 0x3d, 0x64, 0x5f,
	0xd3, 0x6c, 0x49, 0x5f, 0xc8, 0x95, 0x74, 0x93, 0xc1, 0xcd, 0x39, 0x99, 0x94, 0xf7, 0x7b, 0xb0,
	0x34, 0x56, 0xf0, 0xac, 0xd7, 0x1e, 0x7d, 0xd4, 0x42, 0x09, 0x27, 0x34, 0x52, 0x63, 0x9c, 0xe1,
	0x6b, 0xfe, 0x49, 0x83, 0xeb, 0x05, 0x54, 0x57, 0x54, 0xa4, 0x7f, 0x00, 0xcd, 0x54, 0x5a, 0x1e,
	0x91, 0x28, 0xe6, 0xc9, 0xd3, 0x38, 0x01, 0x1f, 0x22, 0xf4, 0xac, 0x72, 0xfd, 0xad, 0x06, 0xab,
	0x5d, 0x8c, 0xb7, 0xac, 0xcf, 0x2f, 0xea, 0xa8, 0xc7, 0x60, 0x24, 0x74, 0x43, 0x71, 0x50, 0x8e,
	0xb8, 0xaa, 0x52, 0xbe, 0xcf, 0x30, 0xc6, 0x49, 0x57, 0x0c, 0xc2, 0x24, 0x1c, 0xd2, 0x08, 0x1b,
	0x8c, 0x12, 0x49, 0x42, 0x44, 0x83, 0x59, 0x83, 0x1a, 0xe3, 0x76, 0x9f, 0xf8, 0x7d, 0xea, 0xa2,
	0xff, 0x74, 0x4b, 0x67, 0x7c, 0x07, 0xbf, 0xc5, 0xd3, 0xa5, 0x48, 0x5c, 0xd5, 0xed, 0x7f, 0xa3,
	0xc1, 0xca, 0x2e, 0x75, 0xbf, 0x2b, 0xaa, 0x98, 0xab, 0x70, 0x73, 0x4e, 0x1c, 0x25, 0xea, 0x7a,
	0x76, 0x46, 0x12, 0x77, 0x6f, 0x13, 0x9e, 0x5d, 0x17, 0x98, 0xef, 0x70, 0x67, 0x53, 0x48, 0xa0,
	0x22, 0x76, 0x15, 0xf4, 0x11, 0xe1, 0xb6, 0x97, 0xec, 0x58, 0x74, 0xab, 0x3a, 0x22, 0x7c, 0x5f,
	0x3c, 0xac, 0xe4, 0x24, 0x39, 0x26, 0x13, 0x35, 0xdc, 0x56, 0x18, 0x3f, 0x20, 0x13, 0x6c, 0xed,
	0x3c, 0x19, 0x09, 0x50, 0x5c, 0xdd, 0xaa, 0x31, 0xae, 0x1e, 0x4d, 0xe6, 0x2f, 0x34, 0x78, 0xd4,
	0xa1, 0x51, 0xd7, 0xf7, 0x69, 0x32, 0x86, 0xe5, 0x9f, 0x55, 0x4e, 0x36, 0x8b, 0xcf, 0x58, 0x1e,
	0x5c, 0x32, 0x79, 0xcd, 0x5f, 0x97, 0x60, 0xfd, 0x23, 0x12, 0x60, 0x0e, 0xa5, 0xa3, 0xb9, 0x76,
	0xd9, 0xd1, 0xbc, 0x74, 0xc1, 0xd1, 0x3c, 0x5d, 0xde, 0x64, 0x5e, 0x83, 0x72, 0x79, 0x83, 0xa3,
	0xc3, 0x63, 0x48, 0xb6, 0x0d, 0x22, 0x1b, 0xc3, 0x28, 0x79, 0xaf, 0x62, 0xd4, 0x28, 0xcc, 0xa1,
	0x40, 0xe0, 0x42, 0x69, 0x03, 0x12, 0x98, 0x2d, 0x2a, 0x68, 0x66, 0xf7, 0xb4, 0xac, 0xe0, 0x7b,
	0xbe, 0x83, 0xcf, 0xdb, 0x5f, 0x6a, 0xb0, 0x79, 0x1e, 0x8f, 0xa8, 0x88, 0x78, 0x03, 0x4b, 0xc9,
	0x56, 0x23, 0xdb, 0xc3, 0x9e, 0x16, 0xab, 0x79, 0x86, 0x95, 0xad, 0xe4, 0xf1, 0x2d, 0xbb, 0xf0,
	0x23, 0xf8, 0x24, 0x33, 0xd4, 0x13, 0xff, 0x6d, 0x12, 0x07, 0x2d, 0x28, 0xf7, 0xa7, 0x4f, 0xea,
	0x3e, 0x73, 0xcc, 0x7f, 0x68, 0xd0, 0xcc, 0x10, 0x7e, 0x37, 0x5d, 0x96, 0x6e, 0xcb, 0x8e, 0x87,
	0x49, 0x97, 0xc0, 0xef, 0xed, 0xa1, 0x78, 0xdc, 0x8f, 0xc8, 0x78, 0xcc, 0x7c, 0xca, 0x39, 0x96,
	0x4d, 0xd5, 0xdf, 0x52, 0xa0, 0xa8, 0x9c, 0x13, 0x6c, 0x6f, 0x39, 0x7b, 0x28, 0x2f, 0x6c, 0x43,
	0x2d, 0x24, 0xfe, 0xdb, 0xac, 0x07, 0x1e, 0x7c, 0xb4, 0x8b, 0x24, 0x46, 0xb2, 0x74, 0x71, 0x0e,
	0x7b, 0xec, 0x9a, 0xe2, 0x81, 0xb3, 0xa6, 0xda, 0xe6, 0x08, 0x00, 0x8e, 0x9a, 0xbf, 0x2d, 0xc1,
	0xad, 0xad, 0x30, 0x3c, 0x7d, 0x44, 0x3b, 0x7b, 0xaf, 0x57, 0xb0, 0x6a, 0xc8, 0x4d, 0x66, 0xb3,
	0xa3, 0x57, 0x69, 0x6e, 0xf4, 0xca, 0x6c, 0xab, 0xca, 0x99, 0x6d, 0x55, 0x76, 0x41, 0xb5, 0x90,
	0x5b, 0x50, 0x99, 0xb0, 0xc4, 0x83, 0x38, 0xec, 0x53, 0xdb, 0xe3, 0x43, 0x81, 0x96, 0x7b, 0x8b,
	0xba, 0x04, 0xee, 0xf3, 0xe1, 0xcc, 0x0a, 0xb3, 0x9a, 0x5b, 0x61, 0xce, 0x94, 0x5a, 0x7d, 0xb6,
	0xd4, 0xae, 0xc3, 0xed, 0x53, 0x4c, 0xa2, 0x0a, 0xee, 0xdf, 0x34, 0xb8, 0xb5, 0x13, 0xf8, 0x3c,
	0x76, 0xa3, 0x2b, 0x31, 0x5a, 0xe9, 0x6c, 0xa3, 0x95, 0xe7, 0x8d, 0x76, 0xfa, 0x8e, 0x76, 0x6e,
	0x5f, 0x5a, 0x99, 0xdb, 0x97, 0x0a, 0x25, 0x4f, 0x51, 0x41, 0x29, 0xf9, 0x8d, 0x86, 0x6d, 0x45,
	0x25, 0x75, 0x2e, 0x9b, 0x95, 0x96, 0xff, 0xfb, 0x72, 0x3e, 0xbf, 0xc7, 0x2e, 0xcf, 0xec, 0xb1,
	0xcd, 0x7f, 0x6b, 0xd8, 0xb8, 0x0a, 0x45, 0x50, 0x09, 0x42, 0x4e, 0x5d, 0xa5, 0xc9, 0x6c, 0x79,
	0x5c, 0x9c, 0x2d, 0x79, 0x96, 0x17, 0xdf, 0xa8, 0x95, 0xce, 0xbd, 0x51, 0x2b, 0x17, 0x6e, 0xd4,
	0x56, 0x41, 0x67, 0xdc, 0xc6, 0xf5, 0xb6, 0x7a, 0x97, 0x54, 0x19, 0xef, 0x88, 0x4f, 0xf3, 0x9f,
	0x1a, 0xac, 0x14, 0x8b, 0x37, 0x17, 0x14, 0x72, 0xed, 0x94, 0x0b, 0x8a, 0xb4, 0x36, 0x96, 0x2e,
	0x5b, 0x1b, 0xcb, 0x97, 0xde, 0x34, 0x2f, 0x64, 0x73, 0x37, 0x5f, 0x32, 0x2b, 0x33, 0x25, 0x73,
	0xf3, 0x45, 0x7e, 0xe7, 0x80, 0x2f, 0xa0, 0x3a, 0x54, 0x5f, 0xf7, 0x5e, 0xf4, 0x5e, 0x7d, 0xd9,
	0x6b, 0x7d, 0xcf, 0x58, 0x82, 0x5a, 0xc7, 0x7a, 0xf5, 0x6a, 0xff, 0x70, 0x7f, 0xab, 0xd7, 0xd2,
	0x8c, 0x65, 0x80, 0x6d, 0xab, 0xbb, 0xbb, 0x77, 0xb8, 0xbf, 0xd5, 0xdd, 0x6d, 0x95, 0x04, 0xed,
	0x33, 0xab, 0xbb, 0xd7, 0xdb, 0x3d, 0x6c, 0x95, 0x37, 0x5f, 0x42, 0xed, 0x28, 0xfd, 0xf3, 0x63,
	0x05, 0x8c, 0xa3, 0xe7, 0x7b, 0xfb, 0x7b, 0xf6, 0xd1, 0x57, 0x07, 0x7b, 0xf6, 0x94, 0xe1, 0x75,
	0x68, 0x66, 0xe0, 0xcf, 0xac, 0xbd, 0xbd, 0x96, 0x66, 0x18, 0xb0, 0x9c, 0x01, 0x1e, 0x6c, 0x7d,
	0xd5, 0x2a, 0x6d, 0xaf, 0xbc, 0xb9, 0x51, 0xf4, 0xaf, 0xd8, 0xf1, 0x22, 0xfe, 0x2d, 0xf6, 0xa3,
	0xff, 0x06, 0x00, 0x00, 0xff, 0xff, 0x51, 0xf8, 0x56, 0x54, 0x58, 0x1b, 0x00, 0x00,
}
