syntax = "proto3";

option go_package = "api";

package tt_revenue_http_logic;

/********* Pia戏服务相关 ***********/
// 获取剧本详情
message GetDramaDetailReq {
    uint32 drama_id = 1; // 剧本ID
}

message UserProfile {
    uint32 uid = 1;
    string account = 2; //账号
    string nickname = 3; // 昵称
    string account_alias = 4; // 靓号，预留字段，暂时未赋值
    uint32 sex = 5; //用户性别

    string head_img_md5 = 6; //用户头像md5，如果有填该值，客户端必须用该值加上account去获取用户头像，避免获取头像回源到服务端
    string head_dy_img_md5 = 7; // 动态头像
}

message GetDramaDetailResp {
    string title = 1; // 剧本名称
    string cover_url = 2; // 封面url
    string author = 3; // 作者
    uint32 male_cnt = 4; // 男角色数量
    uint32 female_cnt = 5; // 女角色数量
    uint32 word_cnt = 6; // 字数
    repeated string tags = 7; // 标签
    string descr = 8; // 剧本简介
    string content = 9; // 剧本全文
}

// pia戏角色
message PiaRole {
    string id = 1; // 唯一id
    string name = 2;
    uint32 sex = 3; // 性别：1--女 2--男
    string avatar = 4; // 链接
    string introduction = 5; // 简介
    string color = 6; // 角色颜色
    double dialogue_ratio = 7; // 对白占比
}

// 获取剧本展开页
message GetDramaExpansionReq {
    uint32 drama_id = 1; // 剧本ID
}

message GetDramaExpansionResp {
    repeated string tags = 1; // 标签
    string descr = 2; // 剧本简介
    string content = 3; // 剧本全文
    repeated PiaRole roles = 4; // 角色列表
}

// 反馈类型
enum FeedbackType {
    FEEDBACK_TYPE_INVALID = 0; // 无效类型
    FEEDBACK_TYPE_LACK_OF_BGM = 1; // 剧本缺少BGM
    FEEDBACK_TYPE_TYPESETTING_UNCLEAR = 2; // 剧本的排版不够清晰
    FEEDBACK_TYPE_HOPE_OST = 3; // 希望能出个原配OST
    FEEDBACK_TYPE_HOPE_SEQUEL = 4; // 希望这个本能出续集
    FEEDBACK_TYPE_LINES_COLOR_GLARING = 5; // 部分台词颜色刺眼
    FEEDBACK_TYPE_WRONG_SOUND_MARK = 6; // 剧本音效标识错误
    FEEDBACK_TYPE_BGM_SORTING_ERROR = 7; // BGM列表排序错误
    FEEDBACK_TYPE_WRONG_BGM_SOUND_EFFECT = 8; // BGM音效声有问题
    FEEDBACK_TYPE_OTHER = 9; // 其他
}

// 请求
message PiaAddDramaFeedBackReq {
    FeedbackType feedback_type = 1; // 问题类型
    string content = 2; // 问题描述
    repeated string picture_list = 3; // 图片
    uint32 drama_id = 4; // 剧本唯一ID
}

/******************** pia戏 *******************************/

/******************** cp战 ******************************/

message CpGameGodRankMem {
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    uint32 send_val = 4;    // 送礼值
    bool is_mvp = 5;
}

message CpGameGodRankInfo {
    repeated CpGameGodRankMem mem_list = 1;
    uint32 value = 2;   // cp值
    uint32 rank = 3;    // 名次
    uint32 time = 4;    // 结算时间
    string bg_url = 5;  // 背景url
}

message GetChannelCpGameGodRankReq {
    uint32 channel_id = 1;
}

message GetChannelCpGameGodRankResp {
    repeated CpGameGodRankInfo list = 1;
}


message GetH5CPInfoReq {
    uint32 my_uid = 1;  // 我的UID
    uint32 fellow_uid = 2;  //挚友UID
}

message GetH5CPInfoResp {
    uint32 my_uid = 1; // 我的UID
    string my_nick_name = 2; // 我的昵称
    string my_icon = 3; //我的头像
    uint32 fellow_uid = 4; //挚友UID
    string fellow_nick_name = 5; //挚友昵称
    string fellow_icon = 6; //挚友头像
    uint32 current_lv = 7; // 当前等级
    uint32 current_strength = 8; //当前战力值
    uint32 current_lv_strength = 9; // 当前等级战力值
    uint32 next_lv_strength = 10; // 下一等级战力值

}

/******************** cp战 ******************************/

/******************** 礼物合成 **************************/

message CostInfoMaterial
{
    uint32 giftId = 1;
    uint32 giftNum = 2;
    uint32 userItemId = 3; // 礼物在用户背包中的id
}

message ComposeGiftReq
{
    string uid = 1;
    uint32 syntheticId = 2;                 // 合成id
    uint32 num = 3;                         // 合成数量
    repeated CostInfoMaterial mixList = 4;  // 使用的原料列表
    uint32 cost = 5;                        // 消耗的价值

    string os_type = 6;     // 操作系统
    string app = 7;         // appId
    string market_id = 8;
    string platform = 9;
}

message ComposeGiftResp {}

message ComposeGiftConf
{
    uint32 syntheticId = 1;     // 合成id
    uint32 syntheticType = 2;   // 合成礼物类型 0:普通礼物,1:限时礼物
    string syntheticName = 3;   // 合成目标礼物的名称
    uint32 syntheticPrice = 4;  // 合成目标礼物的价值
    string syntheticUrl = 5;    // 合成目标礼物的图片url
    uint32 index = 6;           // 排序编号
    uint32 upTime = 7;          // 上架时间
    uint32 downTime = 8;        // 下架时间
    uint32 finTime = 9;         // 礼物使用截止时间
}

message GetComposeGiftConfListReq
{
    string uid = 1;
}

message GetComposeGiftConfListResp
{
    repeated ComposeGiftConf configs = 1;
}

// 合成记录
message ComposeLog
{
    string orderId = 1;
    uint32 syntheticTime = 2;    // 订单时间
    string syntheticName = 3;    // 合成礼物描述
    uint32 syntheticPrice = 4;   // 合成礼物价值
    uint32 syntheticNum = 5;     // 合成数量
    string syntheticUrl = 6;     // 合成礼物图标url
    string costDesc = 7;         // 合成原料描述
    uint32 cost = 8;             // 合成原料总价值
}

message GetUserComposeLogsReq
{
    string uid = 1;
    uint32 offset = 2;  // 偏移量
    uint32 pageNum = 3; // 每页数量
}

message GetUserComposeLogsResp
{
    repeated ComposeLog logs = 1;
    bool hasNextPage = 2;   // 是否还有下一页
}

message materialInfo
{
    uint32 giftId = 1;
    uint32 giftNum = 2;
    string giftName = 3;
    uint32 giftPrice = 4;
    string giftUrl = 5;
    uint32 index = 6;   // 排序编号
    uint32 userItemId = 7;  // 礼物在用户背包中的id
    uint32 finTime = 8; // 礼物到期时间
}

message GetUserMaterialListReq
{
    string uid = 1;
}

message GetUserMaterialListResp
{
    repeated materialInfo gifts = 1;
}


/******************** 礼物合成 **************************/

// 获取魔幻奖励碎片配置，其中，魔幻奖励中的兑换配置需要根据fragment_id进行过滤展示
message GetMagicFragmentRequest {
}

message GetMagicFragmentResponse {
    uint32 fragment_id = 1;       // 碎片id
    string fragment_name = 2;     // 碎片名称
    string fragment_pic = 3;      // 碎片图标
    uint32 fragment_price = 4;    // 价值，T豆

    // 规则页资源后缀，根据不同马甲包自行拼接
    string composite_rules = 5;      // 合成页规则
    string dark_composite_rules = 6; // 黑暗合成页规则
    string dark_energy_stones = 7;   // 能量石规则页
}


/******************** 黑暗礼物合成 **********************/

// 检查合成入口是否开启
message CheckGiftComposeEntryReq {
}

message CheckGiftComposeEntryResp {
    bool is_open = 1;                // 是否开启入口
    uint32 materials_tol_price = 2;  // 背包材料可用于合成黑暗女神的总T豆值
    uint32 pool_full_value = 3;      // 水池蓄满值，单位T豆
}

message CostMaterialInfo
{
    uint32 gift_id = 1;
    uint32 gift_num = 2;
    uint32 user_item_id = 3; // 礼物在用户背包中的id
    uint32 gift_type = 4; // 原材料类型,see MaterialType
}

message DarkComposeGiftReq
{
    string uid = 1;
    uint32 compose_id = 2;                 // 合成id
    uint32 num = 3;                         // 合成数量
    repeated CostMaterialInfo mix_list = 4;  // 使用的原料列表
    uint32 cost = 5;                        // 消耗的价值

    string os_type = 6;     // 操作系统
    string app = 7;         // appId
    string market_id = 8;
    string platform = 9;
}

message DarkComposeGiftResp {}

// 原料类型
enum MaterialType
{
    MaterialTypeGiftType = 0;     // 礼物类型
    MaterialTypeFragmentType = 1; // 碎片类型
}

message DarkComposeGiftConf
{
    uint32 compose_id = 1;  // 合成id
    uint32 gift_type = 2;   // see GiftType
    string gift_name = 3;   // 合成目标礼物的名称
    uint32 gift_price = 4;  // 合成目标礼物的价值
    string gift_url = 5;    // 合成目标礼物的图片url
    uint32 sort_id = 6;     // 排序编号
    uint32 up_time = 7;     // 上架时间
    uint32 down_time = 8;   // 下架时间
    uint32 fin_time = 9;    // 礼物使用截止时间
    bool is_dark_goddess = 10; // 是否为黑暗女神礼物
}

// 黑暗礼物合成主页信息
message GetGiftComposeHomePageInfoReq {
}

message GetGiftComposeHomePageInfoResp {
    uint32 energy_stone_num = 1;     // 能量石数量
    uint32 materials_tol_price = 2;  // 背包材料可用于合成黑暗女神的总T豆值
    uint32 pool_full_value = 3;      // 水池蓄满值，单位T豆
    uint32 dark_goddess_price = 4;   // 黑暗女神T豆值,

    repeated DarkComposeGiftConf compose_gift_conf_list = 5;  // 合成礼物信息list
    repeated uint32 can_compose_id_list = 6;  //可合成礼物列表
    uint32 goddess_compose_id = 7;   // 黑暗女生compose_id
}

// 合成记录
message DarkComposeLog
{
    string order_id = 1;
    uint32 compose_time = 2; // 订单时间
    string gift_name = 3;    // 合成礼物描述
    uint32 gift_price = 4;   // 合成礼物价值
    uint32 gift_num = 5;     // 合成数量
    string gift_url = 6;     // 合成礼物图标url
    string cost_desc = 7;    // 合成原料描述
    uint32 cost = 8;         // 合成原料总价值
}

message GetUserDarkComposeLogsReq
{
    string uid = 1;
    uint32 offset = 2;  // 偏移量
    uint32 pageNum = 3; // 每页数量
}

message GetUserDarkComposeLogsResp
{
    repeated DarkComposeLog logs = 1;
    bool hasNextPage = 2;   // 是否还有下一页
}

message DarkMaterialInfo
{
    uint32 gift_id = 1;      // 礼物/碎片 id
    uint32 gift_num = 2;
    string gift_name = 3;
    uint32 gift_price = 4;
    string gift_url = 5;
    uint32 sort_id = 6;      // 排序编号
    uint32 user_item_id = 7; // 礼物在用户背包中的id
    uint32 fin_time = 8;     // 礼物到期时间
    uint32 gift_type = 9;    // see MaterialType
}

// 获取用户背包中指定合成礼物对应的合成原料
message GetUserGift2MaterialsListReq
{
    string uid = 1;
    uint32 compose_id = 2; // 待合成礼物compose_id
}

message GetUserGift2MaterialsListResp
{
    repeated DarkMaterialInfo material_list = 1;
    repeated uint32 merge_fragment_id_list = 2; // 需要合并的碎片(能量石)id
    repeated uint32 merge_gift_id_list = 3;     // 需要合并的礼物id
}

// 获取用户拥有的所有原料列表
message GetAllUserMaterialListReq
{
    string uid = 1;
}

message GetAllUserMaterialListResp
{
    repeated DarkMaterialInfo material_list = 1;
    repeated uint32 merge_fragment_id_list = 2; // 需要合并的碎片(能量石)id
    repeated uint32 merge_gift_id_list = 3;     // 需要合并的礼物id
    repeated DarkMaterialInfo present_material_list = 4;//合成礼物材料
}

/******************** 黑暗礼物合成 **********************/

/******************** 黑暗礼物奖励记录 ******************/

message DarkGiftBonusLog {
    string order_id = 1;
    uint32 gift_id = 2;
    uint32 gift_cnt = 3;
    string gift_name = 4;
    string gift_pic = 5;
    string source = 6;
    uint32 create_time = 7;
}

message GetDarkGiftBonusLogsReq {
    string uid = 1;
    uint32 offset = 2;
    uint32 limit = 3;
}

message GetDarkGiftBonusLogsResp {
    repeated DarkGiftBonusLog list = 1;
}

/******************** 黑暗礼物奖励记录 ******************/

/******************** 挚友相关 *************************/

// 获取挚友信息
message GetFellowInfoReq {
    uint32 my_uid = 1;  // 我的UID
    uint32 fellow_uid = 2;   //挚友UID
}

message FellowTask {
    uint32 id = 1;  // 任务ID
    float add_point = 2;  // 完成一次加分
    uint32 max_point = 3;  // 每日最高分
    uint32 max_cnt = 4;  // 每日任务次数上限
    uint32 complete_cnt = 5;  // 当前完成
    string title = 6;  // 标题
    string jump_url = 7;
    string icon = 8;
    string desc = 9;  // 描述
    string button_text = 10;
}


enum FellowType {
    ENUM_FELLOW_TYPE_UNKNOWN = 0;  // 未知
    ENUM_FELLOW_TYPE_BRO = 1;  // 基友
    ENUM_FELLOW_TYPE_LADYBRO = 2;  // 闺蜜
    ENUM_FELLOW_TYPE_INTIMATE = 3;  // 知己
}

enum FellowBindType {
    ENUM_FELLOW_BIND_TYPE_UNKNOWN = 0;  // 没有关系
    ENUM_FELLOW_BIND_TYPE_UNIQUE = 1;  // 唯一
    ENUM_FELLOW_BIND_TYPE_MULTI = 2;  // 非唯一
}

message FellowLevelConfig {
    uint32 level = 1; // 等级
    uint32 point = 2; // 分数
    string desc = 3; // 描述
    string award = 4; // 奖励
    string icon = 5; // 图标 url
}

// 在榜信息
message OnAccompanyRankInfo {
    uint32 rank_type = 1; // 榜单类型 see accompany-rank.proto RANK_TYPE
    uint32 rank = 2; // 排名
}

// 段位信息 （一个等级范围属于一个段位）
message GradingInfo {
    uint32 level = 1; // 等级
    string grading_name = 2; // 等级所处段位名称
    string grading_icon = 3; // 段位图标
    string grading_color = 4; //段位颜色
}

// 获取挚友信息
message GetFellowInfoResp {
    uint32 uid = 1;  // 我的UID
    uint32 fellow_uid = 2;   //挚友UID
    uint32 fellow_point = 3; //当前挚友值
    uint32 bind_type = 4;  //绑定类型 see FellowBindType
    uint32 fellow_type = 5;  //挚友类型 see FellowType
    repeated FellowTask task = 6; // 每日任务
    repeated FellowLevelConfig level_config = 7;  //里程碑配置
    uint32 bind_day = 8;  //绑定天数
    string present_url = 9;  //信物链接
    uint32 unbound_time = 10; // 解绑发起时间，0为未发起
    string present_name = 11; // 信物名称
    uint32 close_change_fellow = 12; //关闭选择关系 1为关闭
    int64 accompany_value = 13; // 陪伴值
    string accompany_value_desc = 14; // 陪伴值描述
    repeated OnAccompanyRankInfo rank_info_list = 15; // 在榜信息
    GradingInfo grading_info = 16; // 段位信息
}


message DatingGameRecord {
  uint32 scene_id = 1;     //场景ID
  string scene_name = 2;   //场景名称
  string nameplate = 3;    //铭牌
  string background = 4;   //背景
  uint32 scene_count = 5;  //牵手次数
  bool is_wear = 6;        //是否在穿戴中
  uint32 sort = 7;         //排序
}

//
message PiecesSummaryData {
    uint32 hand_in_hand_count = 1; //相亲房累计牵手次数
    string hand_in_hand_scenes = 2; //相亲房 牵手场景名称 多个场景用\t隔开
    uint32 cp_count = 3; // CP战次数
    uint32 cp_strength = 4; // CP战总战力值
    string cp_plate_url = 5; // CP战铭牌URL
    string cp_background = 6;  //CP战背景
    string date_background = 7; //相亲房背景
    repeated string rare_relationship_names = 8; // 获得的稀缺关系名称数组, 返回15个
    string rare_relationship_bg = 9; // 稀缺关系场景背景
    repeated DatingGameRecord dating_record = 10;    //牵手场景记录
    WeddingCertificate wedding_certificate = 11; // 结婚证
}

message WeddingCertificate {
    UserProfile groom = 1; // 新郎信息
    UserProfile bride = 2; // 新娘信息
    int64 wedding_time = 3;// 结婚时间
    string pic_url = 4; // 结婚证图片
}

message PiecesData {
    string date = 1; // 日期，字符串“yyyy-mm-dd”
    string desc = 2; // 描述
    string background = 3;  //背景
}

//关系空间 点滴数据
message GetPiecesInfoReq {
    uint32 my_uid = 1;  // 我的UID
    uint32 fellow_uid = 2;   //挚友UID
}

message GetPiecesInfoResp {
    uint32 my_uid = 1;  // 我的UID
    uint32 fellow_uid = 2;   //挚友UID
    PiecesSummaryData summary = 3; // 相亲房 CP战 稀缺关系汇总
    repeated PiecesData pieces = 4;  // 点滴明细
    repeated string rare_relationship_tags = 5; // 稀缺关系列表
    bool is_Wedding = 6; // 是否已婚
    string wedding_element = 7; // 婚礼主题元素
    string wedding_background = 8; // 婚礼主题背景
    string wedding_color = 9; // 婚礼主题颜色
    string fellow_house_wedding_background = 10; // 挚友房间婚礼主题背景
}

//CP战 战力值历史记录
message GetCPStrengthHistoryReq {
    uint32 my_uid = 1;  // 我的UID
    uint32 fellow_uid = 2;   //挚友UID
    uint32 offset = 3; // 分页开始
    uint32 limit = 4; // 分页大小
}

message StrengthHistory {
    string date = 1; // 日期，格式yyyy-mm-dd hh:mi:ss
    int32 strength = 2; // 战力值
    string reason = 3;  // 获得原因
}

message GetCPStrengthHistoryResp {
    uint32 my_uid = 1;  // 我的UID
    uint32 fellow_uid = 2;   //挚友UID
    uint32 offset = 3; // 分页开始
    uint32 limit = 4; // 分页大小
    repeated StrengthHistory history = 5;
}

message UnboundFellowReq {
    uint32 op_uid = 1;
    uint32 target_uid = 2;
}

message UnboundFellowResp {
}

message CancelUnboundFellowReq {
    uint32 op_uid = 1;
    uint32 target_uid = 2;
}

message CancelUnboundFellowResp {
}


message DirectUnboundFellowReq {
    uint32 op_uid = 1;
    uint32 target_uid = 2;
}

message DirectUnboundFellowResp {
}


message SetNameplateInUseReq {
    uint32 op_uid = 1;
    uint32 target_uid = 2;
    uint32 scene_id = 3;
    string scene_name = 4;
}

message SetNameplateInUseResp {
}




message ChangeFellowBindTypeReq {
    uint32 op_uid = 1;
    uint32 target_uid = 2;
    uint32 from_bind_type = 3;  //绑定类型 see FellowBindType
    uint32 from_fellow_type = 4;  //挚友类型 see FellowType
    uint32 to_bind_type = 5;  //绑定类型 see FellowBindType
    uint32 to_fellow_type = 6;  //挚友类型 see FellowType
}

message ChangeFellowBindTypeResp {
}

/******************** 挚友相关 *************************/

/******************** 打龙web页 ***********************/

message GetHuntMonsterPropsInfoReq {
    string uid = 1;
    uint32 channel_id = 2;
}
message GetHuntMonsterPropsInfoResp {
    string username = 1;
    string nickname = 2;
    uint32 props_cnt = 3;   // 道具总数
    uint32 stay_channel_one_cnt = 4;    // 在房间停留1分钟次数
    uint32 stay_channel_five_cnt = 5;   // 在房间停留5分钟次数
    uint32 enter_channel_hour_rank_cnt = 6;   // 从小时榜进房次数
    uint32 send_present_cnt = 7;     // 送任意T豆礼物次数
    uint32 send_one_thousand_present_cnt = 8;  // 单笔送≥1000T豆的礼物次数
    uint32 first_send_tbean_present_cnt = 9;   // 首次送T豆礼物次数
    uint32 add_fans_group_cnt = 10;         // 加入粉丝团次数
    bool is_recommend_ch = 11;        //是否是经营房
}

/******************** 打龙web页 ***********************/

/******************** 蒙面pk相关 **********************/

// 获取蒙面pk积分 resp
message MaskedPkScore {
    uint32 uid = 1;
    uint32 score = 2;
    bool has_withdraw = 3;
}

// 语音直播主播奖励积分流水
message MaskedPkScoreLog {
    string order_id = 1; // order id
    int32 amount = 2; // 增加的积分 可为负数
    uint32 create_at = 3; // 记录的时间
    string desc = 4; // 记录描述
}

message GetMaskedPkScoreLogReq {
    uint32 page = 1;
    uint32 page_size = 2;
}

message GetMaskedPkScoreLogResp {
    repeated MaskedPkScoreLog list = 1;
}

/******************** 蒙面pk相关 **********************/

/******************* 荣耀世界抽奖 ********************/
// 仅维护跟svr侧不同的协议, 避免增减字段同步修改
// 获取我得记录
message GetMyLotteryRecordReq {
    uint32 award_type = 1; // 过滤奖励类型 见 LotteryItemType
    uint32 offset = 2;     // 偏移量, 0为第一页
    uint32 limit = 3;  // 单页条数
    string uid = 4;
}

/******************* TT周边商城 begin ****************/
// TT周边商品信息
message PeripheralProduct {
    uint32 product_id = 1;
    string title = 2;          // 商品标题
    string homepage_url = 3;   // 商品主页图url
    string detail_desc = 4;    // 商品详细介绍
    uint32 price = 5;          // 商品单价
    uint32 stock = 6;          // 库存
    uint32 limit_per_order =7; // 每单限购数量
    repeated string pic_urls = 8; // 商品图片url列表

    uint32 origin_price = 9; // 商品原价（仅展示用）
    uint32 ordered_cnt = 10;    // 已下单数量

}

// 商品简要信息
message PeripheralBriefInfo{
    uint32 product_id = 1;   // 商品id
    string title = 2;        // 商品标题
    string homepage_url = 3; // 主页图
    uint32 price = 4;        // 商品单价
    bool is_stock_out = 5;   // 缺货中

    uint32 origin_price = 6; // 商品原价（仅展示用）
}

// 全量获取首页简略商品信息列表 url:/tt_peripheral/product_list
message GetPeripheralBriefInfoReq{
}

message GetPeripheralBriefInfoResp{
    repeated PeripheralBriefInfo item_list = 1; // 全量返回商品简要信息，服务端已排序
}

// 获取商品详情  url：/tt_peripheral/product_detail
message GetPeripheralProductDetailReq {
    uint32 product_id = 1; //商品id
}

message GetPeripheralProductDetailResp {
    PeripheralProduct item_info = 1;    // 商品详情
    //PeripheralOrderAddr last_addr = 2;  // 最后一次下单地址
}

message SinglePeripheralProductOrder {
    uint32 product_id = 1;
    uint32 amount = 2;
}

message PeripheralPayReq {
    string pay_channel = 1;     //下单的渠道  必填
    string bundle_id = 2;     //IOS的produceID(appstore支付必传)
    string product_id = 3;    //IOS的product_id (appstore支付必传)
    string device_id = 4;     //设备ID
    uint32 client_type = 5;   //客户端类型
}

message PeripheralPayResp{
    string token = 1;
    string pay_order_id = 2; // 支付订单号
    string cli_order_title = 3; // 订单标题
    string tsk = 4; // 加密字符串
    string channel_map = 5; // 唤起支付渠道的参数, 用于安卓、IOS、前端使用
    uint32 order_time  = 6; // 下单时间
    string t_pay_order_no = 7; // 第三方订单号
}

// 支付人脸检查相关
message PeripheralPayFaceCheckReq{
    string FaceAuthToken = 1;
    string face_auth_provider_code = 2;
    string face_auth_provider_result_data = 3;
    string face_auth_result_token = 4;
    string request_id = 5;
}

message PeripheralPayFaceCheckResp{
    string request_id = 1;
    uint32 auth_scene = 2;
    string face_auth_context_json = 3;
}

// 下单 url: /tt_peripheral/order
message OrderPeripheralProductReq {
    repeated SinglePeripheralProductOrder list = 1;
    PeripheralOrderAddr addr = 2;
    PeripheralPayReq pay_info = 3;
    PeripheralPayFaceCheckReq face_info = 4;
}

message OrderPeripheralProductResp {
    PeripheralPayResp pay_info = 1;
    PeripheralPayFaceCheckResp face_info = 2;
}

message PeripheralProductOrderInfo {
    string order_id = 1; // 订单号
    repeated PeripheralProduct product_list = 2;    // 购买商品列表
    PeripheralOrderAddr addr = 3;                   // 下单地址信息
    uint32 total_price = 4;                         // 总价
    int64 timestamp = 5;                                 // 下单时间
}

// 获取用户历史订单 url：/tt_peripheral/get_history_orders
message GetPeripheralProductOrdersReq {
    uint32 offset = 1;
    uint32 limit = 2;
}

message GetPeripheralProductOrdersResp {
    repeated PeripheralProductOrderInfo list = 1;
}

// 下单地址
message PeripheralOrderAddr {
    string name = 1;
    string phone = 2;
    string address = 3;
}

// 获取用户下单地址 url：/tt_peripheral/order_addr/get
message GetUserPeripheralOrderAddrReq {
}

message GetUserPeripheralOrderAddrResp {
    PeripheralOrderAddr addr = 1;
}

// 保存用户下单地址 url：/tt_peripheral/order_addr/set
message SetUserPeripheralOrderAddrReq {
    PeripheralOrderAddr addr = 1;
}

message SetUserPeripheralOrderAddrResp {
}


/******************* TT周边商城 end ****************/

// 用户召回奖励相关

message AwardInfo {
    uint32 gift_id = 1;    // 礼包ID, 卡片等级
    string gift_name = 2; // 礼包名称
    uint32 gift_cnt = 3;  // 礼包数量
    string gift_icon = 4;   // 礼包图标
    uint32 gift_val = 5;  // 礼包价值
    uint32 expire_day = 6; // 过期天数
}


message ReturnConsumeAwardInfo {
    AwardInfo award_info = 1; // 奖励信息
    uint32 receive_val = 2; // 领取条件
    uint32 receive_status = 3; // 领取状态, 见AwardReceiveStatus, 0:不可领取(待完成) 1:待领取 2:已领取
}


message GetReturnAwardInfoResp {
    uint32 task_limited_time = 1;              // 任务限时时间
    uint32 con_login_day = 2;             //  连续登录天数
    uint32 max_login_day = 3;             // 最大登录天数
    repeated AwardInfo login_award = 4;   // 登录奖励
    uint32 login_award_receive_status = 5;  // 登录奖励领取领取状态, 见, AwardReceiveStatus
    uint32 con_consume_val = 6;            // 连续消费
    uint32 max_consume_val = 7;           // 最大消费
    repeated ReturnConsumeAwardInfo consume_award = 8; // 消费奖励
    uint32 award_limited_time = 9;                 // 奖励领取时间
}

// ========================== 荣耀世界祈愿 ==============================
enum PondType {
    POND_TYPE_NONE = 0; // 无
    POND_TYPE_NORMAL = 1; // 普通池
    POND_TYPE_GRAND = 2; // 高级池
}


message CheckEntryReq {}

message CheckEntryResp {
    bool common_is_open = 1; // 普通奖池是否开启
    bool grand_is_open = 2; // 高级奖池是否开启
}

message GetPreInfoReq {
    uint32 pond_type = 1; // 奖池类型 see PondType
}

message GetPreInfoResp {
    uint32 single_lottery_cost = 1; // 单次抽奖消耗
    uint32 remain_lottery_times = 2; // 剩余抽奖次数
    uint32 incr_lottery_tbean = 3; // 增加抽奖次数需要的T豆
    uint32 incr_lottery_times= 4;  // 增加的抽奖次数
}


// 获取奖池预览
message GetAwardPreviewReq {
    uint32 pond_type = 1; // 奖池类型
}

message GetAwardPreviewResp {
    repeated AwardPreviewInfo award_preview_list = 1; // 奖励预览列表
}

message AwardPreviewInfo {
    GloryMagicAwardInfo award_info = 1; // 奖励信息
    uint32 chance = 2;  // 放大1w倍, 0.01%的概率 = 1
}

message GloryMagicAwardInfo {
    string award_name = 1;    // 奖励名称
    string award_icon = 2;    // 奖励图标
    uint32 award_worth = 3;   // 奖励价值
    uint32 award_num = 4;     // 奖励数量
    uint32 award_days = 5;    // 奖励天数
}

// 获取中奖轮播
message GetWinningCarouselReq {
    uint32 pond_type = 1; // 奖池类型
}

message WinningInfo {
    string nickname = 1;      // 昵称
    GloryMagicAwardInfo award_info = 2;
}

message GetWinningCarouselResp {
    repeated WinningInfo winning_info_list = 1; // 轮播列表
}

// 抽奖接口
message LotteryReq {
    uint32 pond_type = 1; // 奖池类型
    uint32 times = 2; // 抽取次数（连抽）
}

message LotteryResp {
    enum WINNING_TYPE {
        WINNING_TYPE_UNKNOWN = 0;
        WINNING_TYPE_BIG = 1; // 大奖
        WINNING_TYPE_FLASH = 2; // 出光
        WINNING_TYPE_NORMAL = 3; // 普通奖
        WINNING_TYPE_NONE = 4; // 未中奖
    }
    uint32 winning_type = 1;
    repeated GloryMagicAwardInfo award_info = 2; // 中奖奖品
}

// 获取抽奖记录
message GetLotteryRecordReq {
    uint32 pond_type = 1; // 奖池类型
    string offset = 2; // 起始游标, 空串代表第一页
    uint32 limit = 3;  // 单页条数
    uint32 exchange_type = 4; // 筛选类型 see LotteryItemType
}

message LotteryRecord {
    GloryMagicAwardInfo award_info = 1;
    uint32 cost = 2;
    uint32 got_time = 3;

}

message GetMyLotteryRecordResp {
    repeated LotteryRecord lottery_record_list = 1;
    string offset = 2; // 下一页起始游标, 空串代表没有下一页
}


// 查询用户周卡入口权限
// url：/tt-revenue-http-logic/present-week-card/entry
message GetPresentWeekCardEntryReq{
    uint32 market_id= 1;       // 马甲包id
    uint32 cli_version = 2;    // 版本号
    uint32 os_type = 3;        // 2-ios or 1-android
}

message GetPresentWeekCardEntryResp{
    bool have_access = 1;           // 是否有入口
}

message GetMyLotteryRecordTypeReq {
    uint32 pond_type = 1;
}

message  GetMyLotteryRecordTypeResp {
    repeated uint32 type_list = 1; // 奖品类型列表 see LotteryItemType
}


// 获取陪伴排行榜
message GetAccompanyRankListReq {
    uint32 index = 1; // 页码
    uint32 count = 2; // 每页数量
    uint32 rank_type = 3; // 榜单类型  see accompany-rank.proto RANK_TYPE
}


enum UserRoomStatus {
    USER_ROOM_STATUS_IN_ROOM = 0; // 房间中
    USER_ROOM_STATUS_IN_LIVING = 1; // 听听中
    USER_ROOM_STATUS_IN_PK = 2; // PK中
    USER_ROOM_STATUS_WATCH_LIVING = 3; // 在听听
}

// 用户信息
message AccompanyRankUserInfo {
    uint32 uid = 1; // 用户ID
    string nickname = 2; // 昵称
    uint32 channel_id = 3; // 房间id
    uint32 room_status = 4; // see UserRoomStatus
    string head_frame = 5; // 麦位框
    string extend_json = 6; // 扩展字段
    string headwear_key = 7; // 头饰
    string account = 8; // 用来跳转个人主页
}
// 陪伴小屋信息
message AccompanyHouse {
    string resource_url = 1; // 资源url
    string md5 = 2; // 资源md5
    bool is_into_room = 3; // 是否入住房子
    string house_name = 4; // 小屋名字
    float radio = 5; // 加成比例
    string static_icon_url = 6; // 静态图标
}

// 排行榜信息
message AccompanyRankItem {
    AccompanyRankUserInfo uidA = 1; // 用户ID A
    AccompanyRankUserInfo uidB = 2; // 用户ID B
    uint32 fellow_level = 3; // 挚友等级
    string fellow_name = 4; // 挚友关系名称
    AccompanyHouse house = 5; // 陪伴小屋信息 没有小屋则为nil
}

// 获取陪伴排行榜
message GetAccompanyRankListResp {
    repeated AccompanyRankItem rank_list = 1;
    uint32 next_page_num = 2; // 下一页页码
    int64 server_time = 3; // 服务器时间
}

// 获取我的陪伴排行榜
message ExpandMyAccompanyRankListReq {
    uint32 rank_type = 1; // 榜单类型  see fellow-accompany.proto RANK_TYPE
}

// 获取我的陪伴排行榜
message  ExpandMyAccompanyRankListResp {
    message ExpandMyAccompanyRankItem {
        AccompanyRankUserInfo uidB = 1; // 对方
        uint32 fellow_level = 2; // 挚友等级
        string fellow_name = 3; //
        AccompanyHouse house = 4; // 陪伴小屋信息 没有小屋则为nil
        uint32 rank_now = 5; // 当前排名(用于预计下周期排名)
        uint32 rank_last = 6; // 上一周期排名
        uint32 gap_to_rise_rank = 7; // 距离上一名/上榜 还需多少
    }
    AccompanyRankUserInfo uidA = 1; // 用户ID A  共用
    repeated ExpandMyAccompanyRankItem rank_list = 2; // 扩展排行榜
}