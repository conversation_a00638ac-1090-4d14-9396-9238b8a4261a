// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/you-know-who (interfaces: IClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	youknowwho "golang.52tt.com/protocol/services/youknowwho"
)

// MockYouKnowWhoIClient is a mock of IClient interface.
type MockYouKnowWhoIClient struct {
	ctrl     *gomock.Controller
	recorder *MockYouKnowWhoIClientMockRecorder
}

// MockYouKnowWhoIClientMockRecorder is the mock recorder for MockYouKnowWhoIClient.
type MockYouKnowWhoIClientMockRecorder struct {
	mock *MockYouKnowWhoIClient
}

// NewMockYouKnowWhoIClient creates a new mock instance.
func NewMockYouKnowWhoIClient(ctrl *gomock.Controller) *MockYouKnowWhoIClient {
	mock := &MockYouKnowWhoIClient{ctrl: ctrl}
	mock.recorder = &MockYouKnowWhoIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockYouKnowWhoIClient) EXPECT() *MockYouKnowWhoIClientMockRecorder {
	return m.recorder
}

// BatchGetMapTrueUidByFake mocks base method.
func (m *MockYouKnowWhoIClient) BatchGetMapTrueUidByFake(arg0 context.Context, arg1 []uint32) map[uint32]uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetMapTrueUidByFake", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	return ret0
}

// BatchGetMapTrueUidByFake indicates an expected call of BatchGetMapTrueUidByFake.
func (mr *MockYouKnowWhoIClientMockRecorder) BatchGetMapTrueUidByFake(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetMapTrueUidByFake", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).BatchGetMapTrueUidByFake), arg0, arg1)
}

// BatchGetNickAndAccountByFakeUid mocks base method.
func (m *MockYouKnowWhoIClient) BatchGetNickAndAccountByFakeUid(arg0 context.Context, arg1 []uint32) (map[uint32]*youknowwho.FakeNickAndAccount, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetNickAndAccountByFakeUid", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*youknowwho.FakeNickAndAccount)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetNickAndAccountByFakeUid indicates an expected call of BatchGetNickAndAccountByFakeUid.
func (mr *MockYouKnowWhoIClientMockRecorder) BatchGetNickAndAccountByFakeUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetNickAndAccountByFakeUid", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).BatchGetNickAndAccountByFakeUid), arg0, arg1)
}

// BatchGetTrueUidByFake mocks base method.
func (m *MockYouKnowWhoIClient) BatchGetTrueUidByFake(arg0 context.Context, arg1 []uint32) ([]*youknowwho.TrueUidInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTrueUidByFake", arg0, arg1)
	ret0, _ := ret[0].([]*youknowwho.TrueUidInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetTrueUidByFake indicates an expected call of BatchGetTrueUidByFake.
func (mr *MockYouKnowWhoIClientMockRecorder) BatchGetTrueUidByFake(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTrueUidByFake", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).BatchGetTrueUidByFake), arg0, arg1)
}

// BatchGetUKWInfo mocks base method.
func (m *MockYouKnowWhoIClient) BatchGetUKWInfo(arg0 context.Context, arg1 []uint32) ([]*youknowwho.UKWInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUKWInfo", arg0, arg1)
	ret0, _ := ret[0].([]*youknowwho.UKWInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUKWInfo indicates an expected call of BatchGetUKWInfo.
func (mr *MockYouKnowWhoIClientMockRecorder) BatchGetUKWInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUKWInfo", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).BatchGetUKWInfo), arg0, arg1)
}

// BatchGetUKWPersonInfoOnly mocks base method.
func (m *MockYouKnowWhoIClient) BatchGetUKWPersonInfoOnly(arg0 context.Context, arg1 []uint32) (map[uint32]*youknowwho.UKWPersonInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUKWPersonInfoOnly", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*youknowwho.UKWPersonInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUKWPersonInfoOnly indicates an expected call of BatchGetUKWPersonInfoOnly.
func (mr *MockYouKnowWhoIClientMockRecorder) BatchGetUKWPersonInfoOnly(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUKWPersonInfoOnly", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).BatchGetUKWPersonInfoOnly), arg0, arg1)
}

// CC mocks base method.
func (m *MockYouKnowWhoIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockYouKnowWhoIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).CC))
}

// ChangeUKWAccountToUid mocks base method.
func (m *MockYouKnowWhoIClient) ChangeUKWAccountToUid(arg0 context.Context, arg1 string) (uint32, uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeUKWAccountToUid", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// ChangeUKWAccountToUid indicates an expected call of ChangeUKWAccountToUid.
func (mr *MockYouKnowWhoIClientMockRecorder) ChangeUKWAccountToUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeUKWAccountToUid", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).ChangeUKWAccountToUid), arg0, arg1)
}

// CheckCurIsUkwAndLatestUid mocks base method.
func (m *MockYouKnowWhoIClient) CheckCurIsUkwAndLatestUid(arg0 context.Context, arg1 uint32) (bool, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckCurIsUkwAndLatestUid", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CheckCurIsUkwAndLatestUid indicates an expected call of CheckCurIsUkwAndLatestUid.
func (mr *MockYouKnowWhoIClientMockRecorder) CheckCurIsUkwAndLatestUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCurIsUkwAndLatestUid", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).CheckCurIsUkwAndLatestUid), arg0, arg1)
}

// Close mocks base method.
func (m *MockYouKnowWhoIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockYouKnowWhoIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).Close))
}

// ExposureUKW mocks base method.
func (m *MockYouKnowWhoIClient) ExposureUKW(arg0 context.Context, arg1 uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExposureUKW", arg0, arg1)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// ExposureUKW indicates an expected call of ExposureUKW.
func (mr *MockYouKnowWhoIClientMockRecorder) ExposureUKW(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExposureUKW", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).ExposureUKW), arg0, arg1)
}

// GetShowUpMsgList mocks base method.
func (m *MockYouKnowWhoIClient) GetShowUpMsgList(arg0 context.Context, arg1 *youknowwho.GetShowUpMsgListReq) (*youknowwho.GetShowUpMsgListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowUpMsgList", arg0, arg1)
	ret0, _ := ret[0].(*youknowwho.GetShowUpMsgListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetShowUpMsgList indicates an expected call of GetShowUpMsgList.
func (mr *MockYouKnowWhoIClientMockRecorder) GetShowUpMsgList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowUpMsgList", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).GetShowUpMsgList), arg0, arg1)
}

// GetTrueUidAndCheckFakeUid mocks base method.
func (m *MockYouKnowWhoIClient) GetTrueUidAndCheckFakeUid(arg0 context.Context, arg1 uint32) (*youknowwho.GetTrueUidAndCheckFakeUidResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTrueUidAndCheckFakeUid", arg0, arg1)
	ret0, _ := ret[0].(*youknowwho.GetTrueUidAndCheckFakeUidResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetTrueUidAndCheckFakeUid indicates an expected call of GetTrueUidAndCheckFakeUid.
func (mr *MockYouKnowWhoIClientMockRecorder) GetTrueUidAndCheckFakeUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTrueUidAndCheckFakeUid", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).GetTrueUidAndCheckFakeUid), arg0, arg1)
}

// GetTrueUidAndCheckYkwCanDo mocks base method.
func (m *MockYouKnowWhoIClient) GetTrueUidAndCheckYkwCanDo(arg0 context.Context, arg1 uint32) (uint32, bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTrueUidAndCheckYkwCanDo", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// GetTrueUidAndCheckYkwCanDo indicates an expected call of GetTrueUidAndCheckYkwCanDo.
func (mr *MockYouKnowWhoIClientMockRecorder) GetTrueUidAndCheckYkwCanDo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTrueUidAndCheckYkwCanDo", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).GetTrueUidAndCheckYkwCanDo), arg0, arg1)
}

// GetTrueUidByFake mocks base method.
func (m *MockYouKnowWhoIClient) GetTrueUidByFake(arg0 context.Context, arg1 uint32) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTrueUidByFake", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetTrueUidByFake indicates an expected call of GetTrueUidByFake.
func (mr *MockYouKnowWhoIClientMockRecorder) GetTrueUidByFake(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTrueUidByFake", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).GetTrueUidByFake), arg0, arg1)
}

// GetUKWConfInfo mocks base method.
func (m *MockYouKnowWhoIClient) GetUKWConfInfo(arg0 context.Context, arg1, arg2 uint32) (*youknowwho.GetUKWConfInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUKWConfInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*youknowwho.GetUKWConfInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUKWConfInfo indicates an expected call of GetUKWConfInfo.
func (mr *MockYouKnowWhoIClientMockRecorder) GetUKWConfInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUKWConfInfo", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).GetUKWConfInfo), arg0, arg1, arg2)
}

// GetUKWInfo mocks base method.
func (m *MockYouKnowWhoIClient) GetUKWInfo(arg0 context.Context, arg1 uint32) (*youknowwho.UKWInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUKWInfo", arg0, arg1)
	ret0, _ := ret[0].(*youknowwho.UKWInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUKWInfo indicates an expected call of GetUKWInfo.
func (mr *MockYouKnowWhoIClientMockRecorder) GetUKWInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUKWInfo", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).GetUKWInfo), arg0, arg1)
}

// GetUKWPersonInfoOnly mocks base method.
func (m *MockYouKnowWhoIClient) GetUKWPersonInfoOnly(arg0 context.Context, arg1 uint32) (*youknowwho.UKWPersonInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUKWPersonInfoOnly", arg0, arg1)
	ret0, _ := ret[0].(*youknowwho.UKWPersonInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUKWPersonInfoOnly indicates an expected call of GetUKWPersonInfoOnly.
func (mr *MockYouKnowWhoIClientMockRecorder) GetUKWPersonInfoOnly(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUKWPersonInfoOnly", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).GetUKWPersonInfoOnly), arg0, arg1)
}

// OpenUKW mocks base method.
func (m *MockYouKnowWhoIClient) OpenUKW(arg0 context.Context, arg1 *youknowwho.OpenUKWReq) (*youknowwho.OpenUKWResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OpenUKW", arg0, arg1)
	ret0, _ := ret[0].(*youknowwho.OpenUKWResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// OpenUKW indicates an expected call of OpenUKW.
func (mr *MockYouKnowWhoIClientMockRecorder) OpenUKW(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OpenUKW", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).OpenUKW), arg0, arg1)
}

// SendShowUpMsg mocks base method.
func (m *MockYouKnowWhoIClient) SendShowUpMsg(arg0 context.Context, arg1 *youknowwho.SendShowUpMsgReq) (*youknowwho.SendShowUpMsgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendShowUpMsg", arg0, arg1)
	ret0, _ := ret[0].(*youknowwho.SendShowUpMsgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SendShowUpMsg indicates an expected call of SendShowUpMsg.
func (mr *MockYouKnowWhoIClientMockRecorder) SendShowUpMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendShowUpMsg", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).SendShowUpMsg), arg0, arg1)
}

// Stub mocks base method.
func (m *MockYouKnowWhoIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockYouKnowWhoIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).Stub))
}

// UserChangeRankSwitch mocks base method.
func (m *MockYouKnowWhoIClient) UserChangeRankSwitch(arg0 context.Context, arg1 uint32, arg2 youknowwho.RankSwitchType) (*youknowwho.UserChangeRankSwitchResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserChangeRankSwitch", arg0, arg1, arg2)
	ret0, _ := ret[0].(*youknowwho.UserChangeRankSwitchResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UserChangeRankSwitch indicates an expected call of UserChangeRankSwitch.
func (mr *MockYouKnowWhoIClientMockRecorder) UserChangeRankSwitch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserChangeRankSwitch", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).UserChangeRankSwitch), arg0, arg1, arg2)
}

// UserChangeUKWEnterNotice mocks base method.
func (m *MockYouKnowWhoIClient) UserChangeUKWEnterNotice(arg0 context.Context, arg1 uint32, arg2 youknowwho.UKWSwitchType) (*youknowwho.UserChangeUKWEnterNoticeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserChangeUKWEnterNotice", arg0, arg1, arg2)
	ret0, _ := ret[0].(*youknowwho.UserChangeUKWEnterNoticeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UserChangeUKWEnterNotice indicates an expected call of UserChangeUKWEnterNotice.
func (mr *MockYouKnowWhoIClientMockRecorder) UserChangeUKWEnterNotice(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserChangeUKWEnterNotice", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).UserChangeUKWEnterNotice), arg0, arg1, arg2)
}

// UserChangeUKWStatus mocks base method.
func (m *MockYouKnowWhoIClient) UserChangeUKWStatus(arg0 context.Context, arg1 uint32, arg2 youknowwho.UKWSwitchType) (*youknowwho.UserChangeUKWStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserChangeUKWStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(*youknowwho.UserChangeUKWStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UserChangeUKWStatus indicates an expected call of UserChangeUKWStatus.
func (mr *MockYouKnowWhoIClientMockRecorder) UserChangeUKWStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserChangeUKWStatus", reflect.TypeOf((*MockYouKnowWhoIClient)(nil).UserChangeUKWStatus), arg0, arg1, arg2)
}
