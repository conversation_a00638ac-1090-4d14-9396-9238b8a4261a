package manager

import (
	"context"
	"errors"
	"fmt"
	channel "golang.52tt.com/protocol/services/channelsvr"
	"strings"
	"time"

	"github.com/go-redis/redis"
	localCache "github.com/patrickmn/go-cache"
	"github.com/robfig/cron"
	channelClient "golang.52tt.com/clients/channel"
	commissionClient "golang.52tt.com/clients/commission"
	exchangeClient "golang.52tt.com/clients/exchange"
	guildClient "golang.52tt.com/clients/guild"
	settleBillClient "golang.52tt.com/clients/settlement-bill"
	"golang.52tt.com/pkg/commission"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	grpc2 "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/settlement"
	"golang.52tt.com/pkg/settlement/alert"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	pb "golang.52tt.com/protocol/services/gold-commission"
	Guild "golang.52tt.com/protocol/services/guildsvr"
	settlementBill "golang.52tt.com/protocol/services/settlement-bill"
	"golang.52tt.com/services/gold/common/model"
	"golang.52tt.com/services/gold/gold-settlement/cache"
	"golang.52tt.com/services/gold/gold-settlement/conf"
	"golang.52tt.com/services/gold/gold-settlement/mysql"
	"google.golang.org/grpc"
)

type Manager struct {
	mysqlStore     mysql.IStore
	cache          *cache.GoldSettlementCache
	sc             conf.IServiceConfigT
	commissionCli  *commissionClient.Client
	cfgCenter      settlement.IConfigCenter
	cron           *cron.Cron
	cacheGuildInfo *localCache.Cache
	alert          alert.IFeishu

	GuildClient         guildClient.IClient
	ChannelClient       channelClient.IClient
	SettlementClient    settleBillClient.IClient
	ExchangeGuildClient exchangeClient.IGuildClient
}

func NewManager(sc *conf.ServiceConfigT) (*Manager, error) {
	mysqlStore, err := mysql.NewMysql(sc.GetMysqlAmuseConfig(), sc.GetMysqlYuyinConfig(), sc.GetMysqlGameConfig(), sc.GetMysqlESportConfig())
	if err != nil {
		return nil, err
	}

	redisCfg := sc.GetRedisAmuseConfig()
	redisClient := redis.NewClient(&redis.Options{
		Network:            redisCfg.Protocol,
		Addr:               redisCfg.Addr(),
		PoolSize:           redisCfg.PoolSize,
		IdleCheckFrequency: redisCfg.IdleCheckFrequency(),
		DB:                 redisCfg.DB,
	})
	redisTracer := tracing.Init("gold-settlement_redis")

	cacheClient := cache.NewGoldSettlementCache(redisClient, redisTracer)

	cfgCenter, err := settlement.NewConfigCenter()
	if err != nil {
		log.Errorf("settlement.NewConfigCenter err:%v", err)
		return nil, err
	}

	exGuildClient, err := exchangeClient.NewGuildClient(grpc.WithBlock())
	if err != nil {
		log.Errorf("exchangeClient.NewGuildClient err:%v", err)
		return nil, err
	}

	return &Manager{
		mysqlStore:     mysqlStore,
		cache:          cacheClient,
		sc:             sc,
		commissionCli:  commissionClient.NewCommissionCli(sc.Commission),
		cfgCenter:      cfgCenter,
		alert:          alert.NewFeishu(cfgCenter.GetFeishuAlertUrl()),
		cron:           cron.New(),
		cacheGuildInfo: localCache.New(60*time.Minute, 60*time.Minute),

		GuildClient:         guildClient.NewClient(grpc.WithBlock()),
		SettlementClient:    settleBillClient.NewClient(grpc.WithBlock()),
		ExchangeGuildClient: exGuildClient,
		ChannelClient:       channelClient.NewClient(grpc.WithBlock()),
	}, nil
}

func (m *Manager) GetStore(goldType pb.GoldType) (mysql.IGoldCommissionMysql, error) {
	return m.mysqlStore.GetStore(goldType)
}

func (m *Manager) GetCache() *cache.GoldSettlementCache {
	return m.cache
}

func (m *Manager) GetAlert() alert.IFeishu {
	return m.alert
}

func (m *Manager) GetCommissionClient(billType settlementBill.SettlementBillType) commission.Client {
	return m.commissionCli.GetClient(billType)
}

func (m *Manager) GetGuildInfoFromCache(ctx context.Context, guildId uint32) (*Guild.GuildResp, protocol.ServerError) {
	key := fmt.Sprintf("guild_id_%d", guildId)
	v, ok := m.cacheGuildInfo.Get(key)
	if ok {
		if guildInfo, ok1 := v.(*Guild.GuildResp); ok1 {
			log.InfoWithCtx(ctx, "GetGuildInfoFromCache hit cache, guild_id:%d", guildId)
			return guildInfo, nil
		}
	}
	guildInfo, guildErr := m.GuildClient.GetGuild(ctx, guildId)
	if guildErr != nil && guildInfo == nil {
		log.ErrorWithCtx(ctx, "GetGuildInfoFromCache GetGuild err:%s", guildErr)
		return guildInfo, guildErr
	}
	m.cacheGuildInfo.Set(key, guildInfo, localCache.DefaultExpiration)
	return guildInfo, guildErr
}

// GetGuildInfo 单测不好写呀🤷‍♂️
func (m *Manager) GetGuildInfo(ctx context.Context, guildId uint32) (
	guildInfo *Guild.GuildResp, dismiss bool, err error) {
	var guildErr protocol.ServerError
	guildInfo, guildErr = m.GetGuildInfoFromCache(ctx, guildId)
	if guildErr != nil && guildErr.Code() != -501 {
		log.ErrorWithCtx(ctx, "GetGuildInfo GetGuild err:%s", guildErr)
		err = guildErr
		return
	} else if guildErr != nil && guildErr.Code() == -501 {
		// 公会不存在，但有流水，需记录
		log.WarnWithCtx(ctx, "GetGuildInfo guild not found, guild_id:%d", guildId)
		dismiss = true
		return
	} else {
		return
	}
}

func (m *Manager) BatGetChannelInfo(ctx context.Context, cidList []uint32) (map[uint32]*channel.ChannelSimpleInfo, error) {
	cResp, err := m.ChannelClient.BatchGetChannelSimpleInfo(ctx, 1, cidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatGetChannelInfo BatchGetChannelSimpleInfo err:%s, cidList:%v", err, cidList)
		return nil, err
	}
	channelMap := make(map[uint32]*channel.ChannelSimpleInfo)
	for _, c := range cResp {
		channelMap[c.GetChannelId()] = c
	}
	return channelMap, nil
}

func (m *Manager) NewFlow(ctx context.Context, req *pb.NewFlowReq) (*pb.NewFlowResp, error) {
	var err error
	resp := new(pb.NewFlowResp)
	if !req.GetCertain() {
		err = fmt.Errorf("not certain")
		log.ErrorWithCtx(ctx, "NewFlow err:%v", err)
		return resp, err
	}
	var flow ISettlementFlow
	var ctxBg = context.Background()
	switch req.GetBillType() {
	case settlementBill.SettlementBillType_AmuseCommission:
		flow = NewSettleAmuseGold(ctxBg, m)
	case settlementBill.SettlementBillType_YuyinBaseCommission:
		flow = NewSettleYuyinGold(ctxBg, m)
	case settlementBill.SettlementBillType_AmuseExtra:
		flow = NewSettleAmuseExtra(ctxBg, m)
	case settlementBill.SettlementBillType_MonthMiddle:
		flow = NewSettleYuyinExtra(ctxBg, m)
	case settlementBill.SettlementBillType_InteractGameCommission:
		flow = NewSettleInteractGameGold(ctxBg, m)
	case settlementBill.SettlementBillType_InteractGameExtraCommission:
		flow = NewSettleInteractGameExtra(ctxBg, m, req.GetGuildIdList())
	case settlementBill.SettlementBillType_ESportCommission:
		flow = NewSettleESportGold(ctxBg, m)
	default:
		err = fmt.Errorf(fmt.Sprintf("not support bill type, %s", req.GetBillType()))
		log.ErrorWithCtx(ctx, "NewFlow err:%v", err)
		return resp, err
	}
	go NewFlow(flow)
	return resp, nil
}

func (m *Manager) NewSpecialFlow(ctx context.Context, req *pb.NewSpecialFlowReq) (*pb.NewSpecialFlowResp, error) {
	var err error
	resp := new(pb.NewSpecialFlowResp)
	if !req.GetCertain() {
		err = fmt.Errorf("not certain")
		log.ErrorWithCtx(ctx, "NewSpecialFlow err:%v", err)
		return resp, err
	}

	go m.SettleSpecialGold(grpc2.NewContextWithInfo(ctx))
	return resp, nil
}

func (m *Manager) SettleGold(ctx context.Context) error {
	amuseGoldSettleFlow := NewSettleAmuseGold(ctx, m)
	yuyinGoldSettleFlow := NewSettleYuyinGold(ctx, m)
	gameGoldSettleFlow := NewSettleInteractGameGold(ctx, m)
	gameExtraGoldSettleFlow := NewSettleInteractGameExtra(ctx, m, nil)
	eSportGoldSettleFlow := NewSettleESportGold(ctx, m)
	NewFlow(amuseGoldSettleFlow, yuyinGoldSettleFlow, gameGoldSettleFlow, gameExtraGoldSettleFlow, eSportGoldSettleFlow)
	return nil
}

func (m *Manager) SettleAmuseExtra(ctx context.Context) error {
	amuseExtraSettleFlow := NewSettleAmuseExtra(ctx, m)
	NewFlow(amuseExtraSettleFlow)
	return nil
}

func (m *Manager) SettleYuyinExtra(ctx context.Context) error {
	yuyinExtraSettleFlow := NewSettleYuyinExtra(ctx, m)
	NewFlow(yuyinExtraSettleFlow)
	return nil
}

func (m *Manager) SettleSpecialGold(ctx context.Context) {
	specialSettlements := m.cfgCenter.GetSpecialSettlements()
	if specialSettlements == nil || len(specialSettlements) == 0 {
		return
	}
	log.InfoWithCtx(ctx, "SettleSpecialGold specialSettlements:%+v", specialSettlements)

	now := time.Now()
	for _, specialSettlement := range specialSettlements {
		d := specialSettlement.Date
		specialDay := time.Date(d.Year(), d.Month(), d.Day(), 0, 0, 0, 0, time.Local)
		today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)

		log.InfoWithCtx(ctx, "SettleSpecialGold specialDay:%s, today:%s, type:%s", specialDay, today, specialSettlement.BillType)

		// 指定时间是今天
		if specialDay.Equal(today) {
			switch specialSettlement.BillType {
			case settlementBill.SettlementBillType_AmuseCommission:
				amuseGoldSettleFlow := NewSettleAmuseGoldSpecial(ctx, m, specialSettlement.Date)
				NewFlow(amuseGoldSettleFlow)
			case settlementBill.SettlementBillType_YuyinBaseCommission:
				yuyinGoldSettleFlow := NewSettleYuyinGoldSpecial(ctx, m, specialSettlement.Date)
				NewFlow(yuyinGoldSettleFlow)
			default:
				log.ErrorWithCtx(ctx, "SettleSpecialGold not support bill type, %s", specialSettlement.BillType)
			}
		}
	}
	return
}

var (
	settledUid map[string]string
)

// ResetSettledUidList 初始化当次结算uid列表，防止重复创建结算单
func ResetSettledUidList() {
	settledUid = make(map[string]string)
}
func SetSettledUidList(uid uint32, tp settlementBill.SettlementBillType, billId string) {
	key := fmt.Sprintf("%d_%d", tp, uid)
	settledUid[key] = billId
}
func IsSettled(uid uint32, tp settlementBill.SettlementBillType) (string, bool) {
	key := fmt.Sprintf("%d_%d", tp, uid)
	bill, ok := settledUid[key]
	if bill == "" {
		return bill, false
	}
	return bill, ok
}

// IsSkipGuild 不结算公会
func (m *Manager) IsSkipGuild(guildId uint32) bool {
	//if guildId != 153390 {
	//	return true
	//}

	return m.cfgCenter.IsGuildBlackList(guildId)
}

func (m *Manager) SendEmail(ctx context.Context, title, html string, attachPath []string) {
	var to []string

	to = m.cfgCenter.GetReportEmailTo()
	// 指定收件人
	if val, ok := ctx.Value(settlement.SpecialEmailTo).(string); ok {
		to = strings.SplitAfter(val, ",")
	}

	settlement.SendEmail(ctx, to, title, html, attachPath)
}

func (m *Manager) CreateEmailTitle(title string, t time.Time) string {
	title = fmt.Sprintf("%s - %s", title, t.Format("2006-01"))
	if m.sc.IsTest() {
		title += " [测试环境]"
	}
	return title
}

func (m *Manager) GetMasterUid(ctx context.Context, guildId uint32) (uint32, error) {
	settleUid, err := m.ExchangeGuildClient.GetMasterUid(ctx, guildId)
	if err != nil {
		return 0, err
	}
	return settleUid.GetUid(), nil
}

func (m *Manager) CreateBill(ctx context.Context, tp settlementBill.SettlementBillType, uid uint32, start, end time.Time) (string, error) {
	// 每个对公会长uid仅创建一次结算单
	existedBillId, ok := IsSettled(uid, tp)
	if !ok {
		billId, err := m.SettlementClient.CreateSettlementBill(ctx, &settlementBill.CreateSettlementBillReq{
			Uid:         uid,
			BillType:    tp,
			SettleStart: uint32(start.Unix()),
			SettleEnd:   uint32(end.Unix()),
		})
		if err == nil {
			SetSettledUidList(uid, tp, billId)
		}
		return billId, err
	}
	return existedBillId, nil
}

// HandleCorporate 处理对公会长
func (m *Manager) HandleCorporate(ctx context.Context, info *model.CommonSettleInfo) (isCorporate bool, settleUid uint32, err error) {
	billType, guildId, guildOwner, start, end := info.BillType, info.GuildId, info.GuildUid, info.Start, info.End
	// 对公会长
	masterUid, err := m.GetMasterUid(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleCorporate err:%s, guild_id = %d", err, guildId)
		return
	}

	if masterUid > 0 {
		isCorporate = true
		settleUid = masterUid
	} else {
		settleUid = guildOwner
	}

	log.InfoWithCtx(ctx, "HandleCorporate type:%s, guild:%d, masterUid:%d, settleUid:%d, isCorporate:%v, time:%s->%s",
		billType, guildId, masterUid, settleUid, isCorporate, start, end)
	return
}

// GenDayKey 创建货币结算DayKey
// 2023.03 由于存在子母公会，单个会长UID可能被结算多次，需加上guildId
func (m *Manager) GenDayKey(monthTime time.Time, guildId uint32) string {
	if m.sc.IsTest() {
		return fmt.Sprintf("%d_%d", time.Now().Unix(), guildId)
	} else {
		return fmt.Sprintf("%s_%d", monthTime.Format("20060102"), guildId)
	}
}

// GetSettleMonthRange 月结时间区间 前开后闭
func (m *Manager) GetSettleMonthRange(nowTime time.Time) (time.Time, time.Time) {
	start := time.Date(nowTime.Year(), nowTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
	end := time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)

	if m.sc.IsTest() {
		start = time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)
		end = time.Date(nowTime.Year(), nowTime.Month()+1, 1, 0, 0, 0, 0, time.Local)
	}

	log.Infof("GetSettleMonthRange Gold month settle time range: %s -> %s, now: %s", start, end, nowTime)
	return start, end
}

// GetSettleMonthLastRange 获取月结的上一个周期
func (m *Manager) GetSettleMonthLastRange(t time.Time) (time.Time, time.Time) {
	start := time.Date(t.Year(), t.Month()-1, 1, 0, 0, 0, 0, time.Local)
	end := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, time.Local)

	log.Infof("GetSettleMonthLastRange Gold month settle last time range: %s -> %s, t: %s", start, end, t)
	return start, end
}

// GetSpecialSettleDayRange 特殊结算时间区间（当月初~传入时间0点）
func (m *Manager) GetSpecialSettleDayRange(specialTime time.Time) (time.Time, time.Time) {
	start := time.Date(specialTime.Year(), specialTime.Month(), 1, 0, 0, 0, 0, time.Local)
	end := time.Date(specialTime.Year(), specialTime.Month(), specialTime.Day(), 0, 0, 0, 0, time.Local)

	// 测试环境包含当日
	if m.sc.IsTest() {
		start = time.Date(specialTime.Year(), specialTime.Month(), 1, 0, 0, 0, 0, time.Local)
		end = time.Date(specialTime.Year(), specialTime.Month(), specialTime.Day()+1, 0, 0, 0, 0, time.Local)
	}

	log.Infof("GetSpecialSettleDayRange Gold special settle time range: %s -> %s, specialTime: %s", start, end, specialTime)
	return start, end
}

// IsSettleFatal 致命错误，需重试
func IsSettleFatal(err error) bool {
	var e commission.APIError
	if errors.As(err, &e) && e.Code() == commission.CodeDataExisted {
		return false
	}
	return true
}
