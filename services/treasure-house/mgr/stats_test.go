package mgr

import (
	"bou.ke/monkey"
	"context"
	"golang.52tt.com/clients/account"
	account_go "golang.52tt.com/clients/account-go"
	"golang.52tt.com/clients/channel"
	channel_msg_express "golang.52tt.com/clients/channel-msg-express"
	"golang.52tt.com/clients/channelol"
	presentprivilege "golang.52tt.com/clients/present-privilege"
	public_notice "golang.52tt.com/clients/public-notice"
	reconcile_present "golang.52tt.com/clients/reconcile-v2-svr/reconcile-present"
	unifiedPay "golang.52tt.com/clients/unified_pay"
	user_profile_api "golang.52tt.com/clients/user-profile-api"
	userPresent "golang.52tt.com/clients/userpresent"
	"golang.52tt.com/pkg/protocol"
	reconcile_present2 "golang.52tt.com/protocol/services/reconcile-v2-svr/reconcile-present"
	"golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/treasure-house/cache"
	"golang.52tt.com/services/treasure-house/conf"
	"golang.52tt.com/services/treasure-house/mysql"
	"reflect"
	"testing"
	"time"
)

func TestManager_GetSendGift(t *testing.T) {
	ctx := context.Background()
	now := time.Now()

	monkey.Patch(time.Now, func() time.Time { return now })

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.PresentLocalCache{}), "GetPresentListCache",
		func(c *cache.PresentLocalCache) []*userpresent.StPresentItemConfig {
			return []*userpresent.StPresentItemConfig{
				{
					ItemId: 1,
				},
			}
		})

	monkey.PatchInstanceMethod(reflect.TypeOf(&reconcile_present.Client{}), "GetUserPresentSumByGiftId",
		func(c *reconcile_present.Client, ctx context.Context, req *reconcile_present2.GetUserPresentSumByGiftIdReq) (*reconcile_present2.GetUserPresentSumByGiftIdResp, protocol.ServerError) {
			return &reconcile_present2.GetUserPresentSumByGiftIdResp{}, nil
		})

	type fields struct {
		sc            *conf.ServiceConfigT
		mysqlStore    mysql.IStore
		cache         cache.ITreasureHouseCache
		treasureStore mysql.ITreasureStore
		presentCache  cache.IPresentLocalCache
		activityCache cache.IActivityLocalCache

		privilegeCli        presentprivilege.IClient
		unifiedPayCli       unifiedPay.IClient
		accountCli          account.IClient
		accountGoCli        account_go.IClient
		presentCli          userPresent.IClient
		expressCli          channel_msg_express.IClient
		userProfileCli      user_profile_api.IClient
		channelOlCli        channelol.IClient
		channelCli          channel.IClient
		publicNoticeCli     public_notice.IClient
		reconcilePresentCli reconcile_present.IClient
		ticker              *time.Ticker
		done                chan bool
	}
	type args struct {
		ctx   context.Context
		nowTs time.Time
	}
	tests := []struct {
		name         string
		fields       fields
		args         args
		wantMonth    []*reconcile_present2.Statistics
		wantMonthSum []*reconcile_present2.Statistics
		wantDay      []*reconcile_present2.Statistics
		wantDaySum   []*reconcile_present2.Statistics
		wantHour     []*reconcile_present2.Statistics
		wantHourSum  []*reconcile_present2.Statistics
	}{
		{
			name: "GetSendGift",
			fields: fields{
				sc:                  &conf.ServiceConfigT{},
				mysqlStore:          &mysql.Store{},
				cache:               &cache.TreasureHouseCache{},
				treasureStore:       &mysql.TreasureStore{},
				presentCache:        &cache.PresentLocalCache{},
				activityCache:       &cache.ActivityLocalCache{},
				privilegeCli:        &presentprivilege.Client{},
				unifiedPayCli:       &unifiedPay.Client{},
				accountGoCli:        &account_go.Client{},
				accountCli:          &account.Client{},
				presentCli:          &userPresent.Client{},
				expressCli:          &channel_msg_express.Client{},
				userProfileCli:      &user_profile_api.Client{},
				channelOlCli:        &channelol.Client{},
				channelCli:          &channel.Client{},
				publicNoticeCli:     &public_notice.Client{},
				reconcilePresentCli: &reconcile_present.Client{},
			},
			args: args{
				ctx:   ctx,
				nowTs: now,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				sc:            tt.fields.sc,
				mysqlStore:    tt.fields.mysqlStore,
				cache:         tt.fields.cache,
				treasureStore: tt.fields.treasureStore,
				presentCache:  tt.fields.presentCache,
				activityCache: tt.fields.activityCache,

				privilegeCli:        tt.fields.privilegeCli,
				unifiedPayCli:       tt.fields.unifiedPayCli,
				accountCli:          tt.fields.accountCli,
				accountGoCli:        tt.fields.accountGoCli,
				presentCli:          tt.fields.presentCli,
				expressCli:          tt.fields.expressCli,
				userProfileCli:      tt.fields.userProfileCli,
				channelOlCli:        tt.fields.channelOlCli,
				channelCli:          tt.fields.channelCli,
				publicNoticeCli:     tt.fields.publicNoticeCli,
				reconcilePresentCli: tt.fields.reconcilePresentCli,
				ticker:              tt.fields.ticker,
				done:                tt.fields.done,
			}
			gotMonth, gotMonthSum, gotDay, gotDaySum, gotHour, gotHourSum := m.GetSendGift(tt.args.ctx, tt.args.nowTs)
			if !reflect.DeepEqual(gotMonth, tt.wantMonth) {
				t.Errorf("GetSendGift() gotMonth = %v, want %v", gotMonth, tt.wantMonth)
			}
			if !reflect.DeepEqual(gotMonthSum, tt.wantMonthSum) {
				t.Errorf("GetSendGift() gotMonthSum = %v, want %v", gotMonthSum, tt.wantMonthSum)
			}
			if !reflect.DeepEqual(gotDay, tt.wantDay) {
				t.Errorf("GetSendGift() gotDay = %v, want %v", gotDay, tt.wantDay)
			}
			if !reflect.DeepEqual(gotDaySum, tt.wantDaySum) {
				t.Errorf("GetSendGift() gotDaySum = %v, want %v", gotDaySum, tt.wantDaySum)
			}
			if !reflect.DeepEqual(gotHour, tt.wantHour) {
				t.Errorf("GetSendGift() gotHour = %v, want %v", gotHour, tt.wantHour)
			}
			if !reflect.DeepEqual(gotHourSum, tt.wantHourSum) {
				t.Errorf("GetSendGift() gotHourSum = %v, want %v", gotHourSum, tt.wantHourSum)
			}
		})
	}
}

func TestManager_RecordSendGift(t *testing.T) {
	ctx := context.Background()
	now := time.Now()
	uid := uint32(123)

	monkey.Patch(time.Now, func() time.Time { return now })

	monkey.PatchInstanceMethod(reflect.TypeOf(&reconcile_present.Client{}), "GetUserPresentSumByGiftId",
		func(c *reconcile_present.Client, ctx context.Context, req *reconcile_present2.GetUserPresentSumByGiftIdReq) (*reconcile_present2.GetUserPresentSumByGiftIdResp, protocol.ServerError) {
			return &reconcile_present2.GetUserPresentSumByGiftIdResp{
				Stats: []*reconcile_present2.Statistics{
					{
						ItemId:      1,
						OrderCount:  1,
						ItemCount:   0,
						TotalScore:  0,
						TotalPrice:  0,
						PeopleCount: 0,
						Uid:         []uint32{uid},
					},
				},
			}, nil
		})

	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.TreasureStore{}), "CreateHourStats",
		func(c *mysql.TreasureStore, ctx context.Context, stats *mysql.PresentSumStats) error {
			return nil
		})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.TreasureHouseCache{}), "AddUidListToSet",
		func(c *cache.TreasureHouseCache, uidList []uint32, giftId uint32, now time.Time) (hourPeople, dayPeople, monthPeople int64, err error) {
			return 0, 0, 0, nil
		})
	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.TreasureHouseCache{}), "GetUidCount",
		func(c *cache.TreasureHouseCache, giftId uint32, now time.Time) (hourPeople, dayPeople, monthPeople int64, err error) {
			return 0, 0, 0, nil
		})

	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.TreasureStore{}), "GetSumPresentList",
		func(c *mysql.TreasureStore, ctx context.Context, begin, end time.Time) ([]*mysql.PresentStats, error) {
			return []*mysql.PresentStats{}, nil
		})

	type fields struct {
		sc            *conf.ServiceConfigT
		mysqlStore    mysql.IStore
		cache         cache.ITreasureHouseCache
		treasureStore mysql.ITreasureStore
		presentCache  cache.IPresentLocalCache
		activityCache cache.IActivityLocalCache

		privilegeCli        presentprivilege.IClient
		unifiedPayCli       unifiedPay.IClient
		accountCli          account.IClient
		accountGoCli        account_go.IClient
		presentCli          userPresent.IClient
		expressCli          channel_msg_express.IClient
		userProfileCli      user_profile_api.IClient
		channelOlCli        channelol.IClient
		channelCli          channel.IClient
		publicNoticeCli     public_notice.IClient
		reconcilePresentCli reconcile_present.IClient
		ticker              *time.Ticker
		done                chan bool
	}
	type args struct {
		ctx   context.Context
		nowTs time.Time
	}
	tests := []struct {
		name         string
		fields       fields
		args         args
		wantMonth    []*reconcile_present2.Statistics
		wantMonthSum []*reconcile_present2.Statistics
		wantDay      []*reconcile_present2.Statistics
		wantDaySum   []*reconcile_present2.Statistics
		wantHour     []*reconcile_present2.Statistics
		wantHourSum  []*reconcile_present2.Statistics
	}{
		{
			name: "RecordSendGift",
			fields: fields{
				sc:                  &conf.ServiceConfigT{},
				mysqlStore:          &mysql.Store{},
				cache:               &cache.TreasureHouseCache{},
				treasureStore:       &mysql.TreasureStore{},
				presentCache:        &cache.PresentLocalCache{},
				activityCache:       &cache.ActivityLocalCache{},
				privilegeCli:        &presentprivilege.Client{},
				unifiedPayCli:       &unifiedPay.Client{},
				accountGoCli:        &account_go.Client{},
				accountCli:          &account.Client{},
				presentCli:          &userPresent.Client{},
				expressCli:          &channel_msg_express.Client{},
				userProfileCli:      &user_profile_api.Client{},
				channelOlCli:        &channelol.Client{},
				channelCli:          &channel.Client{},
				publicNoticeCli:     &public_notice.Client{},
				reconcilePresentCli: &reconcile_present.Client{},
			},
			args: args{
				ctx:   ctx,
				nowTs: now,
			},
			wantMonth:    []*reconcile_present2.Statistics{{}},
			wantMonthSum: []*reconcile_present2.Statistics{},
			wantDay:      []*reconcile_present2.Statistics{{}},
			wantDaySum:   []*reconcile_present2.Statistics{},
			wantHour: []*reconcile_present2.Statistics{{
				Uid:        []uint32{uid},
				OrderCount: 1,
				ItemId:     1,
			}},
			wantHourSum: []*reconcile_present2.Statistics{{
				Uid:        []uint32{uid},
				OrderCount: 1,
				ItemId:     1,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				sc:            tt.fields.sc,
				mysqlStore:    tt.fields.mysqlStore,
				cache:         tt.fields.cache,
				treasureStore: tt.fields.treasureStore,
				presentCache:  tt.fields.presentCache,
				activityCache: tt.fields.activityCache,

				privilegeCli:        tt.fields.privilegeCli,
				unifiedPayCli:       tt.fields.unifiedPayCli,
				accountCli:          tt.fields.accountCli,
				accountGoCli:        tt.fields.accountGoCli,
				presentCli:          tt.fields.presentCli,
				expressCli:          tt.fields.expressCli,
				userProfileCli:      tt.fields.userProfileCli,
				channelOlCli:        tt.fields.channelOlCli,
				channelCli:          tt.fields.channelCli,
				publicNoticeCli:     tt.fields.publicNoticeCli,
				reconcilePresentCli: tt.fields.reconcilePresentCli,
				ticker:              tt.fields.ticker,
				done:                tt.fields.done,
			}
			gotMonth, gotMonthSum, gotDay, gotDaySum, gotHour, gotHourSum := m.RecordSendGift(tt.args.ctx, tt.args.nowTs)
			if !reflect.DeepEqual(gotMonth, tt.wantMonth) {
				t.Errorf("RecordSendGift() gotMonth = %+v, want %+v", gotMonth, tt.wantMonth)
			}
			if !reflect.DeepEqual(gotMonthSum, tt.wantMonthSum) {
				t.Errorf("RecordSendGift() gotMonthSum = %v, want %v", gotMonthSum, tt.wantMonthSum)
			}
			if !reflect.DeepEqual(gotDay, tt.wantDay) {
				t.Errorf("RecordSendGift() gotDay = %v, want %v", gotDay, tt.wantDay)
			}
			if !reflect.DeepEqual(gotDaySum, tt.wantDaySum) {
				t.Errorf("RecordSendGift() gotDaySum = %v, want %v", gotDaySum, tt.wantDaySum)
			}
			if !reflect.DeepEqual(gotHour, tt.wantHour) {
				t.Errorf("RecordSendGift() gotHour = %v, want %v", gotHour, tt.wantHour)
			}
			if !reflect.DeepEqual(gotHourSum, tt.wantHourSum) {
				t.Errorf("RecordSendGift() gotHourSum = %v, want %v", gotHourSum, tt.wantHourSum)
			}
		})
	}
}
