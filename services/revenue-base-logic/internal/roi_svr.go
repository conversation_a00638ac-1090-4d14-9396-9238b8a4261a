package internal

import (
	"context"
	"encoding/hex"
	"golang.52tt.com/pkg/log"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/revenue_base"
	channelRecPb "golang.52tt.com/protocol/services/channel-recommend-svr"
	signStatsPb "golang.52tt.com/protocol/services/sign-anchor-stats"
	"time"
)

func (s *Server) getUserContractGuildId(ctx context.Context, uid uint32) (uint32, error) {
	contractResp, err := s.contractCli.GetUserContractCacheInfo(ctx, uid, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "getUserContractGuildId GetUserContractCacheInfo failed uid:%d err:%v", uid, err)
		return 0, err
	}

	nowTs := uint32(time.Now().Unix())
	if contractResp.GetContract().GetGuildId() != 0 && contractResp.GetContract().GetExpireTime() >= nowTs {
		log.DebugWithCtx(ctx, "getUserContractGuildId sign uid:%d contractResp:%v", uid, contractResp)
		return contractResp.GetContract().GetGuildId(), nil
	}

    log.DebugWithCtx(ctx, "getUserContractGuildId uid:%d contractResp:%v", uid, contractResp)
	return 0, nil
}

func (s *Server) checkIsBindGuild(ctx context.Context, uid, guildA, guildB uint32) bool {
	statsResp, err := s.signAnchorStats.GetBindGuildInfo(ctx, &signStatsPb.GetBindGuildInfoReq{
		GuildA:               guildA,
		GuildB:               guildB,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "checkIsBindGuild GetBindGuildInfo failed uid:%d guildA:%d guildB:%d err:%v", uid, guildA, guildB, err)
		return false
	}

	log.DebugWithCtx(ctx, "checkIsBindGuild end uid:%d guildA:%d guildB:%d statsResp:%v", uid, guildA, guildB, statsResp)
	return statsResp.IsBind
}

func (s *Server) CheckCanSeeRoiUser(ctx context.Context, req *pb.CheckCanSeeRoiUserRequest) (*pb.CheckCanSeeRoiUserResponse, error) {
	resp := &pb.CheckCanSeeRoiUserResponse{}

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

	resp.Can = true
	/*
	signGuildId, err := s.getUserContractGuildId(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanSeeRoiUser getUserContractGuildId failed uid:%d req:%v err:%v", uid, req, err)
		return resp, err
	}

	if signGuildId != 0 {
		// 获取房间信息
		chInfo, err := s.channelCli.GetChannelSimpleInfo(ctx, uid, req.GetChannelId())
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckCanSeeRoiUser GetChannelSimpleInfo failed uid:%d req:%v err:%v", uid, req, err)
			return resp, err
		}

		if chInfo.GetChannelType() != uint32(channel.ChannelType_RADIO_LIVE_CHANNEL_TYPE) &&
			chInfo.GetChannelType() != uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE){
			log.DebugWithCtx(ctx, "CheckCanSeeRoiUser no need proc end uid:%d req:%v chInfo:%v resp:%v", uid, req, chInfo, resp)
			return resp, nil
		}

		var chBindId uint32
		switch  channel.ChannelType(chInfo.GetChannelType()){
		case channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE :
			chBindId = chInfo.GetBindId()
		case channel.ChannelType_RADIO_LIVE_CHANNEL_TYPE:
			anchorSignGuildId, err := s.getUserContractGuildId(ctx, chInfo.GetBindId())
			if err != nil {
				log.ErrorWithCtx(ctx, "CheckCanSeeRoiUser getUserContractGuildId failed uid:%d chInfo:%v err:%v", uid, chInfo, err)
				return resp, err
			}

			chBindId = anchorSignGuildId
		default:
			resp.Can = false
		}

		if signGuildId == chBindId || s.checkIsBindGuild(ctx, uid, chBindId, signGuildId){
			resp.Can = true
		}
	}
	 */

	log.DebugWithCtx(ctx, "CheckCanSeeRoiUser end uid:%d req:%v resp:%v", uid, req, resp)
	return resp, nil
}

func (s *Server) GetUserRoiInfo(ctx context.Context, req *pb.GetUserRoiInfoRequest) (*pb.GetUserRoiInfoResponse, error) {
    resp := &pb.GetUserRoiInfoResponse{}

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

    recResp, err := s.channelRecCli.GetUserRoiInfo(ctx, &channelRecPb.GetUserRoiInfoReq{
		Uid:                  uid,
		DeviceId: hex.EncodeToString(serviceInfo.DeviceID),
	})
    if err != nil {
    	log.ErrorWithCtx(ctx, "GetUserRoiInfo failed uid:%d req:%v err:%v", uid, req, err)
    	return resp, err
	}

	resp.IsRoi = recResp.GetIsRoi()
	resp.RoiHighPopUrl = recResp.GetRoiHighPopUrl()
	log.DebugWithCtx(ctx, "GetUserRoiInfo end uid:%d req:%v resp:%v deviceId:%s", uid, req, resp, hex.EncodeToString(serviceInfo.DeviceID))
    return resp, nil
}

func (s *Server) ConfirmRoiHighPotentail(ctx context.Context, req *pb.ConfirmRoiHighPotentailRequest) (*pb.ConfirmRoiHighPotentailResponse, error) {
	resp := &pb.ConfirmRoiHighPotentailResponse{}

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

	_, err := s.channelRecCli.ConfirmRoiHighPotentail(ctx, &channelRecPb.ConfirmRoiHighPotentailReq{
		Uid:                  uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ConfirmRoiHighPotentail failed uid:%d req:%v err:%v", uid, req, err)
		return resp, err
	}

	log.DebugWithCtx(ctx, "ConfirmRoiHighPotentail end uid:%d req:%v resp:%v", uid, req, resp)
	return resp, nil
}
