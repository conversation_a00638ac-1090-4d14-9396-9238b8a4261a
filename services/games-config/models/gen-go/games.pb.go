// Code generated by protoc-gen-go. DO NOT EDIT.
// source: games.proto

package api

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type GetGameBaseInfo struct {
	Cpid                 uint32   `protobuf:"varint,1,opt,name=cpid,proto3" json:"cpid"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid"`
	Gamename             string   `protobuf:"bytes,3,opt,name=gamename,proto3" json:"gamename"`
	Version              string   `protobuf:"bytes,4,opt,name=version,proto3" json:"version"`
	Gamesecret           string   `protobuf:"bytes,5,opt,name=gamesecret,proto3" json:"gamesecret"`
	Maxlogincnt          uint32   `protobuf:"varint,6,opt,name=maxlogincnt,proto3" json:"maxlogincnt"`
	Maxjoincnt           uint32   `protobuf:"varint,7,opt,name=maxjoincnt,proto3" json:"maxjoincnt"`
	Maxreadycnt          uint32   `protobuf:"varint,8,opt,name=maxreadycnt,proto3" json:"maxreadycnt"`
	Cpurl                string   `protobuf:"bytes,9,opt,name=cpurl,proto3" json:"cpurl"`
	GrayChids            []uint32 `protobuf:"varint,10,rep,packed,name=gray_chids,json=grayChids,proto3" json:"gray_chids"`
	Gameextraproperty    string   `protobuf:"bytes,11,opt,name=gameextraproperty,proto3" json:"gameextraproperty"`
	Gamestatetemplate    string   `protobuf:"bytes,12,opt,name=gamestatetemplate,proto3" json:"gamestatetemplate"`
	Bussid               uint32   `protobuf:"varint,13,opt,name=bussid,proto3" json:"bussid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameBaseInfo) Reset()         { *m = GetGameBaseInfo{} }
func (m *GetGameBaseInfo) String() string { return proto.CompactTextString(m) }
func (*GetGameBaseInfo) ProtoMessage()    {}
func (*GetGameBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_6bdd6d56efbe7573, []int{0}
}

func (m *GetGameBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameBaseInfo.Unmarshal(m, b)
}
func (m *GetGameBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameBaseInfo.Marshal(b, m, deterministic)
}
func (m *GetGameBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameBaseInfo.Merge(m, src)
}
func (m *GetGameBaseInfo) XXX_Size() int {
	return xxx_messageInfo_GetGameBaseInfo.Size(m)
}
func (m *GetGameBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameBaseInfo proto.InternalMessageInfo

func (m *GetGameBaseInfo) GetCpid() uint32 {
	if m != nil {
		return m.Cpid
	}
	return 0
}

func (m *GetGameBaseInfo) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GetGameBaseInfo) GetGamename() string {
	if m != nil {
		return m.Gamename
	}
	return ""
}

func (m *GetGameBaseInfo) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *GetGameBaseInfo) GetGamesecret() string {
	if m != nil {
		return m.Gamesecret
	}
	return ""
}

func (m *GetGameBaseInfo) GetMaxlogincnt() uint32 {
	if m != nil {
		return m.Maxlogincnt
	}
	return 0
}

func (m *GetGameBaseInfo) GetMaxjoincnt() uint32 {
	if m != nil {
		return m.Maxjoincnt
	}
	return 0
}

func (m *GetGameBaseInfo) GetMaxreadycnt() uint32 {
	if m != nil {
		return m.Maxreadycnt
	}
	return 0
}

func (m *GetGameBaseInfo) GetCpurl() string {
	if m != nil {
		return m.Cpurl
	}
	return ""
}

func (m *GetGameBaseInfo) GetGrayChids() []uint32 {
	if m != nil {
		return m.GrayChids
	}
	return nil
}

func (m *GetGameBaseInfo) GetGameextraproperty() string {
	if m != nil {
		return m.Gameextraproperty
	}
	return ""
}

func (m *GetGameBaseInfo) GetGamestatetemplate() string {
	if m != nil {
		return m.Gamestatetemplate
	}
	return ""
}

func (m *GetGameBaseInfo) GetBussid() uint32 {
	if m != nil {
		return m.Bussid
	}
	return 0
}

type GetGameInfoResp struct {
	Info                 []*GetGameBaseInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info"`
	Totalnum             uint32             `protobuf:"varint,2,opt,name=totalnum,proto3" json:"totalnum"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetGameInfoResp) Reset()         { *m = GetGameInfoResp{} }
func (m *GetGameInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGameInfoResp) ProtoMessage()    {}
func (*GetGameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_6bdd6d56efbe7573, []int{1}
}

func (m *GetGameInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameInfoResp.Unmarshal(m, b)
}
func (m *GetGameInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameInfoResp.Marshal(b, m, deterministic)
}
func (m *GetGameInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameInfoResp.Merge(m, src)
}
func (m *GetGameInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGameInfoResp.Size(m)
}
func (m *GetGameInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameInfoResp proto.InternalMessageInfo

func (m *GetGameInfoResp) GetInfo() []*GetGameBaseInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetGameInfoResp) GetTotalnum() uint32 {
	if m != nil {
		return m.Totalnum
	}
	return 0
}

type VersionBaseInfo struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version"`
	Engineversion        uint32   `protobuf:"varint,3,opt,name=engineversion,proto3" json:"engineversion"`
	Enginetype           uint32   `protobuf:"varint,4,opt,name=enginetype,proto3" json:"enginetype"`
	Packagepath          string   `protobuf:"bytes,5,opt,name=packagepath,proto3" json:"packagepath"`
	Picurl               string   `protobuf:"bytes,6,opt,name=picurl,proto3" json:"picurl"`
	Gameurl              string   `protobuf:"bytes,7,opt,name=gameurl,proto3" json:"gameurl"`
	Resurl               string   `protobuf:"bytes,8,opt,name=resurl,proto3" json:"resurl"`
	Extraproperty        string   `protobuf:"bytes,9,opt,name=extraproperty,proto3" json:"extraproperty"`
	Packagemd5           string   `protobuf:"bytes,10,opt,name=packagemd5,proto3" json:"packagemd5"`
	Resmd5               string   `protobuf:"bytes,11,opt,name=resmd5,proto3" json:"resmd5"`
	Statetemplate        string   `protobuf:"bytes,12,opt,name=statetemplate,proto3" json:"statetemplate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VersionBaseInfo) Reset()         { *m = VersionBaseInfo{} }
func (m *VersionBaseInfo) String() string { return proto.CompactTextString(m) }
func (*VersionBaseInfo) ProtoMessage()    {}
func (*VersionBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_6bdd6d56efbe7573, []int{2}
}

func (m *VersionBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VersionBaseInfo.Unmarshal(m, b)
}
func (m *VersionBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VersionBaseInfo.Marshal(b, m, deterministic)
}
func (m *VersionBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VersionBaseInfo.Merge(m, src)
}
func (m *VersionBaseInfo) XXX_Size() int {
	return xxx_messageInfo_VersionBaseInfo.Size(m)
}
func (m *VersionBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VersionBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VersionBaseInfo proto.InternalMessageInfo

func (m *VersionBaseInfo) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *VersionBaseInfo) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *VersionBaseInfo) GetEngineversion() uint32 {
	if m != nil {
		return m.Engineversion
	}
	return 0
}

func (m *VersionBaseInfo) GetEnginetype() uint32 {
	if m != nil {
		return m.Enginetype
	}
	return 0
}

func (m *VersionBaseInfo) GetPackagepath() string {
	if m != nil {
		return m.Packagepath
	}
	return ""
}

func (m *VersionBaseInfo) GetPicurl() string {
	if m != nil {
		return m.Picurl
	}
	return ""
}

func (m *VersionBaseInfo) GetGameurl() string {
	if m != nil {
		return m.Gameurl
	}
	return ""
}

func (m *VersionBaseInfo) GetResurl() string {
	if m != nil {
		return m.Resurl
	}
	return ""
}

func (m *VersionBaseInfo) GetExtraproperty() string {
	if m != nil {
		return m.Extraproperty
	}
	return ""
}

func (m *VersionBaseInfo) GetPackagemd5() string {
	if m != nil {
		return m.Packagemd5
	}
	return ""
}

func (m *VersionBaseInfo) GetResmd5() string {
	if m != nil {
		return m.Resmd5
	}
	return ""
}

func (m *VersionBaseInfo) GetStatetemplate() string {
	if m != nil {
		return m.Statetemplate
	}
	return ""
}

type GetVersionInfoResp struct {
	Info                 []*VersionBaseInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info"`
	Totalnum             uint32             `protobuf:"varint,2,opt,name=totalnum,proto3" json:"totalnum"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetVersionInfoResp) Reset()         { *m = GetVersionInfoResp{} }
func (m *GetVersionInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetVersionInfoResp) ProtoMessage()    {}
func (*GetVersionInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_6bdd6d56efbe7573, []int{3}
}

func (m *GetVersionInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVersionInfoResp.Unmarshal(m, b)
}
func (m *GetVersionInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVersionInfoResp.Marshal(b, m, deterministic)
}
func (m *GetVersionInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVersionInfoResp.Merge(m, src)
}
func (m *GetVersionInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetVersionInfoResp.Size(m)
}
func (m *GetVersionInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVersionInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVersionInfoResp proto.InternalMessageInfo

func (m *GetVersionInfoResp) GetInfo() []*VersionBaseInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetVersionInfoResp) GetTotalnum() uint32 {
	if m != nil {
		return m.Totalnum
	}
	return 0
}

type GetOpenIdByTTidReq struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOpenIdByTTidReq) Reset()         { *m = GetOpenIdByTTidReq{} }
func (m *GetOpenIdByTTidReq) String() string { return proto.CompactTextString(m) }
func (*GetOpenIdByTTidReq) ProtoMessage()    {}
func (*GetOpenIdByTTidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_6bdd6d56efbe7573, []int{4}
}

func (m *GetOpenIdByTTidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpenIdByTTidReq.Unmarshal(m, b)
}
func (m *GetOpenIdByTTidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpenIdByTTidReq.Marshal(b, m, deterministic)
}
func (m *GetOpenIdByTTidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpenIdByTTidReq.Merge(m, src)
}
func (m *GetOpenIdByTTidReq) XXX_Size() int {
	return xxx_messageInfo_GetOpenIdByTTidReq.Size(m)
}
func (m *GetOpenIdByTTidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpenIdByTTidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpenIdByTTidReq proto.InternalMessageInfo

func (m *GetOpenIdByTTidReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

type GetOpenIdByTTidInfo struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOpenIdByTTidInfo) Reset()         { *m = GetOpenIdByTTidInfo{} }
func (m *GetOpenIdByTTidInfo) String() string { return proto.CompactTextString(m) }
func (*GetOpenIdByTTidInfo) ProtoMessage()    {}
func (*GetOpenIdByTTidInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_6bdd6d56efbe7573, []int{5}
}

func (m *GetOpenIdByTTidInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpenIdByTTidInfo.Unmarshal(m, b)
}
func (m *GetOpenIdByTTidInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpenIdByTTidInfo.Marshal(b, m, deterministic)
}
func (m *GetOpenIdByTTidInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpenIdByTTidInfo.Merge(m, src)
}
func (m *GetOpenIdByTTidInfo) XXX_Size() int {
	return xxx_messageInfo_GetOpenIdByTTidInfo.Size(m)
}
func (m *GetOpenIdByTTidInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpenIdByTTidInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpenIdByTTidInfo proto.InternalMessageInfo

func (m *GetOpenIdByTTidInfo) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *GetOpenIdByTTidInfo) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

type GetOpenIdByTTidRsp struct {
	Infos                []*GetOpenIdByTTidInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetOpenIdByTTidRsp) Reset()         { *m = GetOpenIdByTTidRsp{} }
func (m *GetOpenIdByTTidRsp) String() string { return proto.CompactTextString(m) }
func (*GetOpenIdByTTidRsp) ProtoMessage()    {}
func (*GetOpenIdByTTidRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_6bdd6d56efbe7573, []int{6}
}

func (m *GetOpenIdByTTidRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpenIdByTTidRsp.Unmarshal(m, b)
}
func (m *GetOpenIdByTTidRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpenIdByTTidRsp.Marshal(b, m, deterministic)
}
func (m *GetOpenIdByTTidRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpenIdByTTidRsp.Merge(m, src)
}
func (m *GetOpenIdByTTidRsp) XXX_Size() int {
	return xxx_messageInfo_GetOpenIdByTTidRsp.Size(m)
}
func (m *GetOpenIdByTTidRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpenIdByTTidRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpenIdByTTidRsp proto.InternalMessageInfo

func (m *GetOpenIdByTTidRsp) GetInfos() []*GetOpenIdByTTidInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func init() {
	proto.RegisterType((*GetGameBaseInfo)(nil), "api.GetGameBaseInfo")
	proto.RegisterType((*GetGameInfoResp)(nil), "api.GetGameInfoResp")
	proto.RegisterType((*VersionBaseInfo)(nil), "api.VersionBaseInfo")
	proto.RegisterType((*GetVersionInfoResp)(nil), "api.GetVersionInfoResp")
	proto.RegisterType((*GetOpenIdByTTidReq)(nil), "api.GetOpenIdByTTidReq")
	proto.RegisterType((*GetOpenIdByTTidInfo)(nil), "api.GetOpenIdByTTidInfo")
	proto.RegisterType((*GetOpenIdByTTidRsp)(nil), "api.GetOpenIdByTTidRsp")
}

func init() { proto.RegisterFile("games.proto", fileDescriptor_6bdd6d56efbe7573) }

var fileDescriptor_6bdd6d56efbe7573 = []byte{
	// 526 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x94, 0xcf, 0x8e, 0xd3, 0x30,
	0x10, 0xc6, 0xd5, 0x4d, 0xff, 0x4e, 0x89, 0x56, 0x98, 0x15, 0xb2, 0x90, 0x40, 0x55, 0xb4, 0x87,
	0x1c, 0x50, 0x0f, 0x20, 0x5e, 0x60, 0x01, 0x55, 0x7b, 0x42, 0x8a, 0x56, 0x20, 0x71, 0x41, 0xde,
	0x64, 0xb6, 0x6b, 0x68, 0x1c, 0x13, 0xbb, 0xa8, 0x79, 0x1b, 0x5e, 0x93, 0x1b, 0x1a, 0xdb, 0x49,
	0x93, 0x2e, 0x20, 0x6e, 0xfe, 0xbe, 0x19, 0xcf, 0x4c, 0xfd, 0x9b, 0x06, 0x96, 0x5b, 0x51, 0xa2,
	0x59, 0xeb, 0xba, 0xb2, 0x15, 0x8b, 0x84, 0x96, 0xc9, 0xcf, 0x08, 0xce, 0x37, 0x68, 0x37, 0xa2,
	0xc4, 0x2b, 0x61, 0xf0, 0x5a, 0xdd, 0x55, 0x8c, 0xc1, 0x38, 0xd7, 0xb2, 0xe0, 0xa3, 0xd5, 0x28,
	0x8d, 0x33, 0x77, 0x66, 0x4f, 0x61, 0x4a, 0x77, 0x65, 0xc1, 0xcf, 0x9c, 0x1b, 0x14, 0x7b, 0x06,
	0x73, 0x3a, 0x29, 0x51, 0x22, 0x8f, 0x56, 0xa3, 0x74, 0x91, 0x75, 0x9a, 0x71, 0x98, 0xfd, 0xc0,
	0xda, 0xc8, 0x4a, 0xf1, 0xb1, 0x0b, 0xb5, 0x92, 0xbd, 0x00, 0x70, 0x93, 0x60, 0x5e, 0xa3, 0xe5,
	0x13, 0x17, 0xec, 0x39, 0x6c, 0x05, 0xcb, 0x52, 0x1c, 0x76, 0xd5, 0x56, 0xaa, 0x5c, 0x59, 0x3e,
	0x75, 0x2d, 0xfb, 0x16, 0x55, 0x28, 0xc5, 0xe1, 0x6b, 0xe5, 0x13, 0x66, 0x2e, 0xa1, 0xe7, 0x84,
	0x0a, 0x35, 0x8a, 0xa2, 0xa1, 0x84, 0x79, 0x57, 0xa1, 0xb5, 0xd8, 0x05, 0x4c, 0x72, 0xbd, 0xaf,
	0x77, 0x7c, 0xe1, 0xda, 0x7b, 0xc1, 0x9e, 0x03, 0x6c, 0x6b, 0xd1, 0x7c, 0xc9, 0xef, 0x65, 0x61,
	0x38, 0xac, 0xa2, 0x34, 0xce, 0x16, 0xe4, 0xbc, 0x25, 0x83, 0xbd, 0x84, 0xc7, 0x34, 0x26, 0x1e,
	0x6c, 0x2d, 0x74, 0x5d, 0x69, 0xac, 0x6d, 0xc3, 0x97, 0xae, 0xc0, 0xc3, 0x40, 0x9b, 0x6d, 0xac,
	0xb0, 0x68, 0xb1, 0xd4, 0x3b, 0x61, 0x91, 0x3f, 0x3a, 0x66, 0x0f, 0x02, 0xf4, 0xc4, 0xb7, 0x7b,
	0x63, 0x64, 0xc1, 0x63, 0xff, 0xc4, 0x5e, 0x25, 0x9f, 0x3a, 0x42, 0x44, 0x27, 0x43, 0xa3, 0x59,
	0x0a, 0x63, 0xa9, 0xee, 0x2a, 0x3e, 0x5a, 0x45, 0xe9, 0xf2, 0xd5, 0xc5, 0x5a, 0x68, 0xb9, 0x3e,
	0xa1, 0x98, 0xb9, 0x0c, 0xe2, 0x63, 0x2b, 0x2b, 0x76, 0x6a, 0x5f, 0x06, 0x72, 0x9d, 0x4e, 0x7e,
	0x9d, 0xc1, 0xf9, 0x47, 0x4f, 0xa4, 0x63, 0x7f, 0xe4, 0x3c, 0x1a, 0x70, 0xee, 0xb1, 0x3c, 0x1b,
	0xb2, 0xbc, 0x84, 0x18, 0xd5, 0x56, 0x2a, 0x6c, 0xe3, 0x91, 0xbb, 0x38, 0x34, 0x89, 0x97, 0x37,
	0x6c, 0xa3, 0xd1, 0xad, 0x43, 0x9c, 0xf5, 0x1c, 0xe2, 0xa5, 0x45, 0xfe, 0x4d, 0x6c, 0x51, 0x0b,
	0x7b, 0x1f, 0x56, 0xa2, 0x6f, 0xd1, 0x64, 0x5a, 0xe6, 0x04, 0x6c, 0xea, 0x82, 0x41, 0xd1, 0x64,
	0x34, 0x23, 0x05, 0x66, 0x7e, 0xb2, 0x20, 0xe9, 0x46, 0x8d, 0x86, 0x02, 0x73, 0x7f, 0xc3, 0x2b,
	0x37, 0xf1, 0x00, 0xa0, 0xdf, 0x80, 0xa1, 0x49, 0x13, 0x87, 0xf6, 0x65, 0xf1, 0x86, 0x83, 0xdf,
	0xd1, 0xa3, 0x13, 0xaa, 0x53, 0x6c, 0xd9, 0x55, 0x27, 0xff, 0x12, 0xe2, 0x3f, 0x01, 0x1f, 0x9a,
	0xc9, 0x67, 0x60, 0x1b, 0xb4, 0xe1, 0xf5, 0xff, 0xc9, 0xf5, 0x84, 0xd0, 0x7f, 0x70, 0x4d, 0x5d,
	0xed, 0x0f, 0x1a, 0xd5, 0x75, 0x71, 0xd5, 0xdc, 0xdc, 0xc8, 0x22, 0xc3, 0xef, 0xf4, 0xaf, 0xb6,
	0x36, 0x70, 0x5d, 0x64, 0xee, 0x9c, 0xbc, 0x87, 0x27, 0x27, 0x99, 0xed, 0x12, 0x54, 0x1a, 0x55,
	0x97, 0x1c, 0xd4, 0xdf, 0x3e, 0x02, 0xc9, 0xbb, 0x87, 0x0d, 0x8d, 0x66, 0x6b, 0x98, 0xd0, 0xa8,
	0x26, 0xfc, 0x1a, 0xde, 0x6e, 0xe9, 0x69, 0xbb, 0xcc, 0xa7, 0xdd, 0x4e, 0xdd, 0x67, 0xe9, 0xf5,
	0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0xc2, 0x29, 0xe2, 0xa1, 0xa5, 0x04, 0x00, 0x00,
}
