package mgr

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/channelmic"
	accountmock "golang.52tt.com/clients/mocks/account"
	channelmicmock "golang.52tt.com/clients/mocks/channelmic"
	piaMock "golang.52tt.com/clients/mocks/pia"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/pia"
	pb "golang.52tt.com/protocol/app/pia"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	channelmicsvr "golang.52tt.com/protocol/services/channelmicsvr"
	piapb "golang.52tt.com/protocol/services/pia"
	"golang.52tt.com/services/pia-logic/conf"
)

type MainGameHelperForTest struct {
	*MainGame
}

func (receiver *MainGameHelperForTest) GetPiaCli() *piaMock.MockPiaClient {
	return receiver.piaCli.(*piaMock.MockPiaClient)
}

func (receiver *MainGameHelperForTest) GetConverter() *MockIConverter {
	return receiver.converter.(*MockIConverter)
}

func (receiver *MainGameHelperForTest) GetChannelMicCli() *channelmicmock.MockIClient {
	return receiver.channelMicCli.(*channelmicmock.MockIClient)
}

func (receiver *MainGameHelperForTest) GetAccountCli() *accountmock.MockIClient {
	return receiver.accountCli.(*accountmock.MockIClient)
}

func (receiver *MainGameHelperForTest) GetPushService() *MockPushService {
	return receiver.pushService.(*MockPushService)
}

func (receiver *MainGameHelperForTest) GetConfig() *conf.MockIConfig {
	return receiver.config.(*conf.MockIConfig)
}

func testNewMainGameMgr(t *testing.T) *MainGameHelperForTest {
	controller := gomock.NewController(t)
	return &MainGameHelperForTest{
		MainGame: &MainGame{
			piaCli:        piaMock.NewMockPiaClient(controller),
			converter:     NewMockIConverter(controller),
			channelMicCli: channelmicmock.NewMockIClient(controller),
			accountCli:    accountmock.NewMockIClient(controller),
			pushService:   NewMockPushService(controller),
			config:        conf.NewMockIConfig(controller),
		},
	}
}

func TestMainGame_CancelSelectRole(t *testing.T) {
	type args struct {
		c   context.Context
		req *pia.PiaCancelSelectRoleReq
	}
	tests := []struct {
		name     string
		args     args
		want     *pia.PiaCancelSelectRoleResp
		wantErr  bool
		initFunc func(s *MainGameHelperForTest)
	}{
		{
			name: "1",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetPiaCli().EXPECT().PiaCancelSelectRole(gomock.Any(), gomock.Any()).Return(&piapb.PiaCancelSelectRoleResp{}, nil)
				s.GetConverter().EXPECT().MicRoleBindingSvrToLogic(gomock.Any()).Return(nil)
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args:    args{},
			want:    &pia.PiaCancelSelectRoleResp{MicRoleMap: nil},
			wantErr: false,
		},
		{
			name: "error",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetPiaCli().EXPECT().PiaCancelSelectRole(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("error"))
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args:    args{},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.CancelSelectRole(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PiaOperateBgmVol() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.want, got, "CancelSelectRole(%v, %v)", tt.args.c, tt.args.req)
		})
	}
}

func TestMainGame_SelectDramaV2(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pia.SelectDramaV2Req
	}
	tests := []struct {
		name     string
		args     args
		want     *pia.SelectDramaV2Resp
		wantErr  assert.ErrorAssertionFunc
		initFunc func(s *MainGameHelperForTest)
	}{
		{
			name: "正常",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetPiaCli().EXPECT().SelectDramaV2(gomock.Any(), gomock.Any()).Return(&piapb.SelectDramaV2Resp{
					DramaStatus: &piapb.ChannelDramaStatus{
						DramaInfo: &piapb.DramaV2{
							DramaSubInfo: &piapb.DramaSubInfo{
								CreatorUid: 123,
							},
						},
					},
				}, nil)
				s.GetConverter().EXPECT().DramaStatusPBToLogic(gomock.Any()).Return(&pia.ChannelDramaStatus{
					DramaInfo: &pia.DramaV2{
						IsCopyDrama: true,
					},
				})
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
				s.GetAccountCli().EXPECT().GetUserByUid(gomock.Any(), gomock.Any()).Return(&accountPB.UserResp{
					Uid:      123,
					Username: "123",
					Nickname: "123",
				}, nil)
			},
			args: args{
				ctx: context.Background(),
				req: &pia.SelectDramaV2Req{},
			},
			want: &pia.SelectDramaV2Resp{
				DramaStatus: &pia.ChannelDramaStatus{
					DramaInfo: &pia.DramaV2{
						IsCopyDrama: true,
						CreatorInfo: &pia.PiaDramaCreatorInfo{
							Uid:      123,
							Nickname: "123",
							Account:  "123",
						},
					},
				},
			},
			wantErr: assert.NoError,
		},
		{
			name: "error",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetPiaCli().EXPECT().SelectDramaV2(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("error"))
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args: args{
				ctx: context.Background(),
				req: &pia.SelectDramaV2Req{},
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return assert.NotNil(t, err)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.SelectDramaV2(tt.args.ctx, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("PerformDramaV2(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want, got, "PerformDramaV2(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}

func TestMainGame_OperateDrama(t *testing.T) {
	type args struct {
		c   context.Context
		req *pia.PiaOperateDramaReq
	}
	tests := []struct {
		name     string
		initFunc func(s *MainGameHelperForTest)
		args     args
		want     *pia.PiaOperateDramaResp
		wantErr  bool
	}{
		{
			name: "1",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetPiaCli().EXPECT().PiaOperateDrama(gomock.Any(), gomock.Any()).Return(&piapb.PiaOperateDramaResp{}, nil)
				s.GetConverter().EXPECT().DramaStatusPBToLogic(gomock.Any()).Return(nil)
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args: args{
				c:   context.Background(),
				req: &pia.PiaOperateDramaReq{},
			},
			want:    &pia.PiaOperateDramaResp{DramaStatus: nil},
			wantErr: false,
		},
		{
			name: "error",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetPiaCli().EXPECT().PiaOperateDrama(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("error"))
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args: args{
				c:   context.Background(),
				req: &pia.PiaOperateDramaReq{},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.OperateDrama(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PiaOperateBgmVol() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.want, got, "OperateDrama(%v, %v)", tt.args.c, tt.args.req)
		})
	}
}

func TestMainGame_GetDramaStatus(t *testing.T) {
	type args struct {
		c   context.Context
		req *pia.PiaGetDramaStatusReq
	}
	tests := []struct {
		name     string
		initFunc func(s *MainGameHelperForTest)
		args     args
		want     *pia.PiaGetDramaStatusResp
		wantErr  bool
	}{
		{
			name: "1",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetPiaCli().EXPECT().PiaGetDramaStatus(gomock.Any(), gomock.Any()).Return(&piapb.PiaGetDramaStatusResp{}, nil)
				s.GetConverter().EXPECT().ChannelDramaInfoDetailPBToLogic(gomock.Any()).Return(nil)
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args: args{
				c:   context.Background(),
				req: &pia.PiaGetDramaStatusReq{},
			},
			want:    &pia.PiaGetDramaStatusResp{DramaInfoDetail: nil},
			wantErr: false,
		},
		{
			name: "error",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetPiaCli().EXPECT().PiaGetDramaStatus(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("error"))
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args: args{
				c:   context.Background(),
				req: &pia.PiaGetDramaStatusReq{},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.GetDramaStatus(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PiaOperateBgmVol() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.want, got, "GetDramaStatus(%v, %v)", tt.args.c, tt.args.req)
		})
	}
}

func TestMainGame_OperateBgm(t *testing.T) {

	type args struct {
		c   context.Context
		req *pia.PiaOperateBgmReq
	}
	tests := []struct {
		name     string
		initFunc func(s *MainGameHelperForTest)
		args     args
		want     *pia.PiaOperateBgmResp
		wantErr  bool
	}{
		{
			name: "1",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetPiaCli().EXPECT().PiaOperateBgm(gomock.Any(), gomock.Any()).Return(&piapb.PiaOperateBgmResp{}, nil)
				s.GetConverter().EXPECT().DramaBgmStatusPBToLogic(gomock.Any()).Return(nil)
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args: args{
				c:   context.Background(),
				req: &pia.PiaOperateBgmReq{},
			},
			want:    &pia.PiaOperateBgmResp{},
			wantErr: false,
		},
		{
			name: "error",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetPiaCli().EXPECT().PiaOperateBgm(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("error"))
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args: args{
				c:   context.Background(),
				req: &pia.PiaOperateBgmReq{},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.OperateBgm(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PiaOperateBgmVol() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.want, got, "OperateBgm(%v, %v)", tt.args.c, tt.args.req)
		})
	}
}

func TestMainGame_OperateBgmVol(t *testing.T) {
	type args struct {
		c   context.Context
		req *pia.PiaOperateBgmVolReq
	}
	tests := []struct {
		name     string
		initFunc func(s *MainGameHelperForTest)
		args     args
		want     *pia.PiaOperateBgmVolResp
		wantErr  bool
	}{
		{
			name: "1",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetPiaCli().EXPECT().PiaOperateBgmVol(gomock.Any(), gomock.Any()).Return(&piapb.PiaOperateBgmVolResp{}, nil)
				s.GetConverter().EXPECT().DramaBgmVolStatusPBToLogic(gomock.Any()).Return(nil)
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args: args{
				c:   context.Background(),
				req: &pia.PiaOperateBgmVolReq{},
			},
			want:    &pia.PiaOperateBgmVolResp{},
			wantErr: false,
		},
		{
			name: "error",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetPiaCli().EXPECT().PiaOperateBgmVol(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("error"))
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args: args{
				c:   context.Background(),
				req: &pia.PiaOperateBgmVolReq{},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.OperateBgmVol(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PiaOperateBgmVol() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.want, got, "OperateBgmVol(%v, %v)", tt.args.c, tt.args.req)
		})
	}
}

func TestMainGame_SelectRole(t *testing.T) {
	userID := uint32(123)

	type args struct {
		c   context.Context
		req *pia.PiaSelectRoleReq
	}
	tests := []struct {
		name     string
		initFunc func(s *MainGameHelperForTest)
		args     args
		want     *pia.PiaSelectRoleResp
		wantErr  bool
	}{
		{
			name: "正常",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetChannelMicCli().EXPECT().GetMicrList(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelmicsvr.GetMicrListResp{
					AllMicList: []*channelmicsvr.MicrSpaceInfo{
						{
							MicId:  1,
							MicUid: 123,
						},
					},
				}, nil)
				s.GetPiaCli().EXPECT().PiaSelectRole(gomock.Any(), gomock.Any()).Return(&piapb.PiaSelectRoleResp{}, nil)
				s.GetConverter().EXPECT().MicRoleBindingSvrToLogic(gomock.Any()).Return(&pia.MicRoleMap{})
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args: args{
				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
					UserID: userID,
				}),
				req: &pia.PiaSelectRoleReq{
					ChannelId: 123,
					RoleId:    "123",
					MicNumber: 1,
				},
			},
			want: &pia.PiaSelectRoleResp{
				MicRoleMap: &pia.MicRoleMap{},
			},
			wantErr: false,
		},
		{
			name: "请求下游服务失败",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetChannelMicCli().EXPECT().GetMicrList(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelmicsvr.GetMicrListResp{
					AllMicList: []*channelmicsvr.MicrSpaceInfo{
						{
							MicId:  1,
							MicUid: 123,
						},
					},
				}, nil)
				s.GetPiaCli().EXPECT().PiaSelectRole(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("error"))
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args: args{
				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
					UserID: userID,
				}),
				req: &pia.PiaSelectRoleReq{
					ChannelId: 123,
					RoleId:    "123",
					MicNumber: 1,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "请求麦位服务失败",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetChannelMicCli().EXPECT().GetMicrList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, protocol.NewServerError(100, "error"))
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args: args{
				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
					UserID: userID,
				}),
				req: &pia.PiaSelectRoleReq{
					ChannelId: 123,
					RoleId:    "123",
					MicNumber: 1,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "用户不在对应的麦上",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetChannelMicCli().EXPECT().GetMicrList(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelmicsvr.GetMicrListResp{
					AllMicList: []*channelmicsvr.MicrSpaceInfo{
						{
							MicId:  2,
							MicUid: 123,
						},
					},
				}, nil)
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args: args{
				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
					UserID: userID,
				}),
				req: &pia.PiaSelectRoleReq{
					ChannelId: 123,
					RoleId:    "123",
					MicNumber: 1,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "缺少服务上下文信息",
			initFunc: func(s *MainGameHelperForTest) {
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
			args: args{
				c: context.Background(),
				req: &pia.PiaSelectRoleReq{
					ChannelId: 123,
					RoleId:    "123",
					MicNumber: 1,
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.SelectRole(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PiaOperateBgmVol() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.want, got, "SelectRole(%v, %v)", tt.args.c, tt.args.req)
		})
	}
}

func TestMainGame_SendDialogueIndex(t *testing.T) {
	controller := gomock.NewController(t)
	type fields struct {
		piaCli        piapb.PiaClient
		converter     IConverter
		channelMicCli channelmic.IClient
		accountCli    account.IClient
		pushService   PushService
	}
	type args struct {
		ctx     context.Context
		request *pia.PiaSendDialogueIndexRequest
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantErr  assert.ErrorAssertionFunc
		initFunc func(s *MainGame)
	}{
		{
			name: "正常",
			fields: fields{
				piaCli:        piaMock.NewMockPiaClient(controller),
				converter:     NewMockIConverter(controller),
				channelMicCli: channelmicmock.NewMockIClient(controller),
				accountCli:    accountmock.NewMockIClient(controller),
				pushService:   NewMockPushService(controller),
			},
			args: args{
				ctx: context.Background(),
				request: &pia.PiaSendDialogueIndexRequest{
					ChannelId:     123,
					DialogueIndex: 123,
					UidList:       []uint32{123},
					RoundId:       123,
				},
			},
			wantErr: assert.NoError,
			initFunc: func(s *MainGame) {
				s.pushService.(*MockPushService).EXPECT().PushMsgToUsers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MainGame{
				piaCli:        tt.fields.piaCli,
				converter:     tt.fields.converter,
				channelMicCli: tt.fields.channelMicCli,
				accountCli:    tt.fields.accountCli,
				pushService:   tt.fields.pushService,
			}
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			tt.wantErr(t, m.SendDialogueIndex(tt.args.ctx, tt.args.request), fmt.Sprintf("SendDialogueIndex(%v, %v)", tt.args.ctx, tt.args.request))
		})
	}
}

func TestMainGame_ChangePlayingType(t *testing.T) {
	controller := gomock.NewController(t)
	type fields struct {
		piaCli        piapb.PiaClient
		converter     IConverter
		channelMicCli channelmic.IClient
		accountCli    account.IClient
		pushService   PushService
	}
	type args struct {
		c   context.Context
		req *pia.PiaChangePlayTypeReq
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		want     *pia.PiaChangePlayTypeResp
		wantErr  assert.ErrorAssertionFunc
		initFunc func(s *MainGameHelperForTest)
	}{
		{
			name: "正常",
			fields: fields{
				piaCli:        piaMock.NewMockPiaClient(controller),
				converter:     NewMockIConverter(controller),
				channelMicCli: channelmicmock.NewMockIClient(controller),
				accountCli:    accountmock.NewMockIClient(controller),
				pushService:   NewMockPushService(controller),
			},
			args: args{
				c: context.Background(),
				req: &pia.PiaChangePlayTypeReq{
					ChannelDramaPlayingType: &pia.PiaChannelDramaPlayingType{
						CurrentPlayingTypeRole: pia.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_HAS,
						CurrentPlayingTypeTime: pia.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_HAS,
					},
					ChannelId: 123,
					RoundId:   123,
				},
			},
			want:    nil,
			wantErr: assert.NoError,
			initFunc: func(s *MainGameHelperForTest) {
				s.GetPiaCli().EXPECT().PiaChangePlayType(gomock.Any(), gomock.Any()).Return(&piapb.PiaChangePlayTypeResp{
					ChannelDramaPlayingType: &piapb.PiaChannelDramaPlayingType{
						CurrentPlayingTypeRole: piapb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_HAS,
						CurrentPlayingTypeTime: piapb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_HAS,
					},
				}, nil)
				s.GetConverter().EXPECT().ChannelDramaPlayingTypePBToLogic(gomock.Any()).Return(&pia.PiaChannelDramaPlayingType{})
				s.GetConverter().EXPECT().ChannelDramaPlayingTypeLogicToPB(gomock.Any()).Return(&piapb.PiaChannelDramaPlayingType{})
				s.GetConfig().EXPECT().GetFeatureTrigger().Return(&conf.FeatureTrigger{
					FeatureTriggerField: map[string]bool{},
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			_, err := m.ChangePlayingType(tt.args.c, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("ChangePlayingType(%v, %v)", tt.args.c, tt.args.req)) {
				return
			}
		})
	}
}

func TestMainGame_PerformDrama(t *testing.T) {
	controller := gomock.NewController(t)
	type fields struct {
		piaCli        piapb.PiaClient
		converter     IConverter
		channelMicCli channelmic.IClient
		accountCli    account.IClient
		pushService   PushService
	}
	type args struct {
		c       context.Context
		request *pia.PiaPerformDramaRequest
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		want     *pia.PiaPerformDramaResponse
		wantErr  assert.ErrorAssertionFunc
		initFunc func(s *MainGame)
	}{
		{
			name: "正常",
			fields: fields{
				piaCli:        piaMock.NewMockPiaClient(controller),
				converter:     NewMockIConverter(controller),
				channelMicCli: channelmicmock.NewMockIClient(controller),
				accountCli:    accountmock.NewMockIClient(controller),
				pushService:   NewMockPushService(controller),
			},
			args: args{
				c: context.Background(),
				request: &pia.PiaPerformDramaRequest{
					ChannelId:   123,
					DramaId:     123,
					IndexId:     123,
					IsCopyDrama: false,
					RoundId:     0,
				},
			},
			want:    nil,
			wantErr: assert.NoError,
			initFunc: func(s *MainGame) {
				s.piaCli.(*piaMock.MockPiaClient).EXPECT().SelectDramaV2(gomock.Any(), gomock.Any()).Return(&piapb.SelectDramaV2Resp{
					DramaStatus: &piapb.ChannelDramaStatus{
						DramaInfo: &piapb.DramaV2{
							DramaSubInfo: &piapb.DramaSubInfo{
								CreatorUid: 123,
							},
						},
					},
				}, nil)
				s.converter.(*MockIConverter).EXPECT().DramaStatusPBToLogic(gomock.Any()).Return(&pia.ChannelDramaStatus{
					DramaInfo: &pia.DramaV2{
						DramaSubInfo: &pia.DramaSubInfo{},
						IsCopyDrama:  true,
					},
				})
				s.accountCli.(*accountmock.MockIClient).EXPECT().GetUserByUid(gomock.Any(), gomock.Any()).Return(&accountPB.UserResp{}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MainGame{
				piaCli:        tt.fields.piaCli,
				converter:     tt.fields.converter,
				channelMicCli: tt.fields.channelMicCli,
				accountCli:    tt.fields.accountCli,
				pushService:   tt.fields.pushService,
			}
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			_, err := m.PerformDrama(tt.args.c, tt.args.request)
			if !tt.wantErr(t, err, fmt.Sprintf("PerformDrama(%v, %v)", tt.args.c, tt.args.request)) {
				return
			}
		})
	}
}

func TestMainGame_PerformDramaV2(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pia.SelectDramaV2Req
	}
	tests := []struct {
		name     string
		args     args
		want     *pia.SelectDramaV2Resp
		wantErr  assert.ErrorAssertionFunc
		initFunc func(m *MainGameHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				ctx: context.Background(),
				req: &pia.SelectDramaV2Req{},
			},
			want: &pia.SelectDramaV2Resp{
				DramaStatus: &pia.ChannelDramaStatus{
					DramaInfo: &pia.DramaV2{
						CreatorInfo: &pia.PiaDramaCreatorInfo{
							Uid:      123,
							Nickname: "123",
							Account:  "123",
						},
					},
				},
			},
			wantErr: assert.NoError,
			initFunc: func(m *MainGameHelperForTest) {
				m.GetPiaCli().EXPECT().CreateTempDrama(gomock.Any(), gomock.Any()).Return(&piapb.PiaCreateTempDramaResponse{
					TempDramaId: "123",
					Drama: &piapb.DramaV2{
						DramaSubInfo: &piapb.DramaSubInfo{
							Id:         123,
							CreatorUid: 123,
						},
						RoleList: []*piapb.PiaRole{
							{
								Id:           "123",
								Name:         "123",
								Sex:          1,
								Avatar:       "123",
								Introduction: "123",
								Color:        "123",
							},
						},
						ContentList: []*piapb.PiaContent{
							{
								Id:       "123",
								RoleId:   "123",
								Dialogue: "123",
								Duration: &piapb.PiaDuration{
									BeginTime: 123,
									EndTime:   1234,
								},
								RoleName: "123",
								Color:    "123",
							},
						},
						PlayType: piapb.PiaPlayType_PLAY_TYPE_AUTO,
					},
				}, nil)
				m.GetPiaCli().EXPECT().PreformDrama(gomock.Any(), gomock.Any()).Return(&piapb.PerformDramaResponse{
					DramaStatus: &piapb.ChannelDramaStatus{
						DramaPhase: piapb.DramaPhase_DRAMA_PHASE_PLAY,
						ChannelId:  123,
						Version:    123,
						Progress: &piapb.ChannelDramaProgress{
							CurIndex:   12,
							TimeOffset: 123,
						},
						StartTime: 123,
						RoundId:   123,
						PlayingType: &piapb.PiaChannelDramaPlayingType{
							SupportPlayingTypeRoles: []piapb.PiaChannelDramaPlayingType_PlayingTypeRole{
								piapb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE,
								piapb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_HAS,
							},
							SupportPlayingTypeTimes: []piapb.PiaChannelDramaPlayingType_PlayingTypeTime{
								piapb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_HAS,
								piapb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE,
							},
							CurrentPlayingTypeRole: piapb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_HAS,
							CurrentPlayingTypeTime: piapb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_HAS,
						},
						CanChangePlayingType: false,
						TempDramaId:          "123",
					},
				}, nil)
				m.GetConverter().EXPECT().DramaStatusPBToLogic(gomock.Any()).Return(&pia.ChannelDramaStatus{})
				m.GetConverter().EXPECT().DramaV2PBToLogic(gomock.Any()).Return(&pia.DramaV2{})
				m.GetAccountCli().EXPECT().GetUserByUid(gomock.Any(), gomock.Any()).Return(&accountPB.UserResp{
					Uid:      123,
					Username: "123",
					Nickname: "123",
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.PerformDramaV2(tt.args.ctx, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("PerformDramaV2(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want, got, "PerformDramaV2(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}

func TestMainGame_GetMyFollowInfo(t *testing.T) {
	type args struct {
		c       context.Context
		request *pia.PiaGetMyFollowInfoRequest
	}
	tests := []struct {
		name     string
		args     args
		want     *pia.PiaGetMyFollowInfoResponse
		wantErr  assert.ErrorAssertionFunc
		initFunc func(m *MainGameHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
					UserID: uint32(123),
				}),
				request: &pia.PiaGetMyFollowInfoRequest{
					ChannelId: 123,
					RoundId:   123,
				},
			},
			want: &pia.PiaGetMyFollowInfoResponse{
				TargetMic: pia.PiaMicType_PIA_MIC_TYPE_ONE,
			},
			wantErr: assert.NoError,
			initFunc: func(m *MainGameHelperForTest) {
				m.GetPiaCli().EXPECT().PiaGetMyFollowInfo(gomock.Any(), gomock.Any()).Return(&piapb.PiaGetMyFollowInfoResponse{
					TargetMic: uint32(pia.PiaMicType_PIA_MIC_TYPE_ONE),
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.GetMyFollowInfo(tt.args.c, tt.args.request)
			if !tt.wantErr(t, err, fmt.Sprintf("GetMyFollowInfo(%v, %v)", tt.args.c, tt.args.request)) {
				return
			}
			assert.Equalf(t, tt.want, got, "GetMyFollowInfo(%v, %v)", tt.args.c, tt.args.request)
		})
	}
}

func TestMainGame_GetPreviousDialogueIndex(t *testing.T) {
	type args struct {
		c       context.Context
		request *pia.PiaGetPreviousDialogueIndexRequest
	}
	tests := []struct {
		name     string
		args     args
		want     *pia.PiaGetPreviousDialogueIndexResponse
		wantErr  assert.ErrorAssertionFunc
		initFunc func(m *MainGameHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
					UserID: uint32(123),
				}),
				request: &pia.PiaGetPreviousDialogueIndexRequest{
					ChannelId: 123,
					RoundId:   123,
					MyMic:     pia.PiaMicType_PIA_MIC_TYPE_ONE,
				},
			},
			want: &pia.PiaGetPreviousDialogueIndexResponse{
				DialogueIndex: 12,
			},
			wantErr: assert.NoError,
			initFunc: func(m *MainGameHelperForTest) {
				m.GetPiaCli().EXPECT().PiaGetPreviousDialogueIndex(gomock.Any(), gomock.Any()).Return(&piapb.PiaGetPreviousDialogueIndexResponse{
					DialogueIndex: 12,
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.GetPreviousDialogueIndex(tt.args.c, tt.args.request)
			if !tt.wantErr(t, err, fmt.Sprintf("GetPreviousDialogueIndex(%v, %v)", tt.args.c, tt.args.request)) {
				return
			}
			assert.Equalf(t, tt.want, got, "GetPreviousDialogueIndex(%v, %v)", tt.args.c, tt.args.request)
		})
	}
}

func TestMainGame_ReportDialogueIndex(t *testing.T) {
	type args struct {
		c       context.Context
		request *pia.PiaReportDialogueIndexRequest
	}
	tests := []struct {
		name     string
		args     args
		wantErr  assert.ErrorAssertionFunc
		initFunc func(m *MainGameHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
					UserID: uint32(123),
				}),
				request: &pia.PiaReportDialogueIndexRequest{
					ChannelId:     123,
					RoundId:       123,
					DialogueIndex: 12,
					MyMic:         pia.PiaMicType_PIA_MIC_TYPE_EIGHT,
				},
			},
			wantErr: assert.NoError,
			initFunc: func(m *MainGameHelperForTest) {
				m.GetPiaCli().EXPECT().PiaReportDialogueIndex(gomock.Any(), gomock.Any()).Return(&piapb.PiaReportDialogueIndexResponse{}, nil)
			},
		},
		{
			name: "缺少上下文信息",
			args: args{
				c:       context.Background(),
				request: &pia.PiaReportDialogueIndexRequest{},
			},
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return assert.NotNil(t, err)
			},
		},
		{
			name: "缺少请求参数",
			args: args{
				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
					UserID: uint32(123),
				}),
				request: &pia.PiaReportDialogueIndexRequest{},
			},
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return assert.NotNil(t, err)
			},
			initFunc: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			tt.wantErr(t, m.ReportDialogueIndex(tt.args.c, tt.args.request), fmt.Sprintf("ReportDialogueIndex(%v, %v)", tt.args.c, tt.args.request))
		})
	}
}

func TestMainGame_UnFollowMic(t *testing.T) {
	type args struct {
		c       context.Context
		request *pia.PiaUnFollowMicRequest
	}
	tests := []struct {
		name     string
		args     args
		wantErr  assert.ErrorAssertionFunc
		initFunc func(m *MainGameHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
					UserID: uint32(123),
				}),
				request: &pia.PiaUnFollowMicRequest{
					ChannelId: 13,
					RoundId:   123,
					MyMic:     pia.PiaMicType_PIA_MIC_TYPE_ZERO,
				},
			},
			wantErr: assert.NoError,
			initFunc: func(m *MainGameHelperForTest) {
				m.GetPiaCli().EXPECT().PiaUnFollowMic(gomock.Any(), gomock.Any()).Return(&piapb.PiaUnFollowMicResponse{}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			tt.wantErr(t, m.UnFollowMic(tt.args.c, tt.args.request), fmt.Sprintf("UnFollowMic(%v, %v)", tt.args.c, tt.args.request))
		})
	}
}

func TestMainGame_FollowMic(t *testing.T) {
	type args struct {
		c       context.Context
		request *pia.PiaFollowMicRequest
	}
	tests := []struct {
		name     string
		args     args
		want     *pia.PiaFollowMicResponse
		wantErr  assert.ErrorAssertionFunc
		initFunc func(m *MainGameHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
					UserID: uint32(123),
				}),
				request: &pia.PiaFollowMicRequest{
					ChannelId: 123,
					RoundId:   123,
					TargetMic: pia.PiaMicType_PIA_MIC_TYPE_EIGHT,
					MyMic:     pia.PiaMicType_PIA_MIC_TYPE_FOUR,
				},
			},
			want: &pia.PiaFollowMicResponse{
				DialogueIndex: 12,
			},
			wantErr: assert.NoError,
			initFunc: func(m *MainGameHelperForTest) {
				m.GetPiaCli().EXPECT().PiaFollowMic(gomock.Any(), gomock.Any()).Return(&piapb.PiaFollowMicResponse{
					DialogueIndex: 12,
				}, nil)
				m.GetPiaCli().EXPECT().PiaGetMyFollowInfo(gomock.Any(), &piapb.PiaGetMyFollowInfoRequest{
					Uid:       456,
					ChannelId: 123,
					RoundId:   123,
				}).Return(&piapb.PiaGetMyFollowInfoResponse{
					TargetMic: uint32(pb.PiaMicType_PIA_MIC_TYPE_VIRTUAL),
				}, nil)
				m.GetChannelMicCli().EXPECT().GetMicrList(gomock.Any(), uint32(123), uint32(123)).Return(
					&channelmicsvr.GetMicrListResp{
						ChannelId: 123,
						AllMicList: []*channelmicsvr.MicrSpaceInfo{
							{
								MicId:  9,
								MicUid: 456,
							},
						},
					}, nil,
				)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.FollowMic(tt.args.c, tt.args.request)
			if !tt.wantErr(t, err, fmt.Sprintf("FollowMic(%v, %v)", tt.args.c, tt.args.request)) {
				return
			}
			assert.Equalf(t, tt.want, got, "FollowMic(%v, %v)", tt.args.c, tt.args.request)
		})
	}
}

func TestMainGame_PiaGetFollowedStatusOfMicList(t *testing.T) {

	type args struct {
		c   context.Context
		req *pia.PiaGetFollowedStatusOfMicListRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *pia.PiaGetFollowedStatusOfMicListResponse
		wantErr bool
		init    func(m *MainGameHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
					UserID: uint32(2400464),
				}),
				req: &pia.PiaGetFollowedStatusOfMicListRequest{
					ChannelId: 2254093,
					RoundId:   111222333,
				},
			},
			init: func(m2 *MainGameHelperForTest) {
				m2.GetPiaCli().EXPECT().PiaGetFollowedStatusOfMicList(gomock.Any(), &piapb.PiaGetFollowedStatusOfMicListRequest{
					UidList:   []uint32{2400464, 2465920},
					ChannelId: 2254093,
					RoundId:   111222333,
				}).Return(
					&piapb.PiaGetFollowedStatusOfMicListResponse{
						UidStatusMap: map[uint32]bool{
							2400464: true,
							2465920: false,
						},
					}, nil,
				)
				m2.GetChannelMicCli().EXPECT().GetMicrList(gomock.Any(), uint32(2254093), uint32(2400464)).Return(&channelmicsvr.GetMicrListResp{
					ChannelId: 2254093,
					AllMicList: []*channelmicsvr.MicrSpaceInfo{
						{
							MicId:  1,
							MicUid: 0,
						},
						{
							MicId: 2,
						},
						{
							MicId:  3,
							MicUid: 2400464,
						},
						{
							MicId: 4,
						},
						{
							MicId: 5,
						},
						{
							MicId:  6,
							MicUid: 2465920,
						},
						{
							MicId: 7,
						},
						{
							MicId: 8,
						},
						{
							MicId: 9,
						},
					},
				}, nil)
			},
			wantErr: false,
			want: &pia.PiaGetFollowedStatusOfMicListResponse{
				MicStatusList: []*pia.PiaGetFollowedStatusOfMicListResponse_PiaMicStatus{
					{
						MicNum:        1,
						CanBeFollowed: false,
					},
					{
						MicNum:        2,
						CanBeFollowed: false,
					},
					{
						MicNum:        3,
						CanBeFollowed: true,
					},
					{
						MicNum:        4,
						CanBeFollowed: false,
					},
					{
						MicNum:        5,
						CanBeFollowed: false,
					},
					{
						MicNum:        6,
						CanBeFollowed: false,
					},
					{
						MicNum:        7,
						CanBeFollowed: false,
					},
					{
						MicNum:        8,
						CanBeFollowed: false,
					},
					{
						MicNum:        9,
						CanBeFollowed: false,
					},
				},
			},
		},
		{
			name: "roundId为空",
			args: args{
				c: context.Background(),
				req: &pb.PiaGetFollowedStatusOfMicListRequest{
					RoundId: 0,
				},
			},
			want:    &pb.PiaGetFollowedStatusOfMicListResponse{},
			wantErr: true,
		},
		{
			name: "ctx异常",
			args: args{
				c: context.Background(),
				req: &pb.PiaGetFollowedStatusOfMicListRequest{
					RoundId:   1,
					ChannelId: 1,
				},
			},
			want:    &pb.PiaGetFollowedStatusOfMicListResponse{},
			wantErr: true,
		},
		{
			name: "调用下游channelMic服务错误",
			args: args{
				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
					UserID: uint32(2400464),
				}),
				req: &pb.PiaGetFollowedStatusOfMicListRequest{
					RoundId:   1,
					ChannelId: 2254093,
				},
			},
			init: func(m *MainGameHelperForTest) {
				m.GetChannelMicCli().EXPECT().GetMicrList(gomock.Any(), uint32(2254093), uint32(2400464)).Return(
					&channelmicsvr.GetMicrListResp{}, protocol.NewServerError(-2, "channelMic服务出错"),
				)
			},
			want:    &pb.PiaGetFollowedStatusOfMicListResponse{},
			wantErr: true,
		},
		{
			name: "调用下游pia服务错误",
			args: args{
				c: protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
					UserID: uint32(2400464),
				}),
				req: &pb.PiaGetFollowedStatusOfMicListRequest{
					RoundId:   1,
					ChannelId: 2254093,
				},
			},
			init: func(m *MainGameHelperForTest) {
				m.GetChannelMicCli().EXPECT().GetMicrList(gomock.Any(), uint32(2254093), uint32(2400464)).Return(&channelmicsvr.GetMicrListResp{
					ChannelId: 2254093,
					AllMicList: []*channelmicsvr.MicrSpaceInfo{
						{
							MicId:  1,
							MicUid: 0,
						},
						{
							MicId: 2,
						},
						{
							MicId:  3,
							MicUid: 2400464,
						},
						{
							MicId: 4,
						},
						{
							MicId: 5,
						},
						{
							MicId:  6,
							MicUid: 2465920,
						},
						{
							MicId: 7,
						},
						{
							MicId: 8,
						},
						{
							MicId: 9,
						},
					},
				}, nil)
				m.GetPiaCli().EXPECT().PiaGetFollowedStatusOfMicList(gomock.Any(), &piapb.PiaGetFollowedStatusOfMicListRequest{
					ChannelId: 2254093,
					RoundId:   1,
					UidList:   []uint32{2400464, 2465920},
				}).Return(
					&piapb.PiaGetFollowedStatusOfMicListResponse{}, errors.New("调用下游服务错误"),
				)
			},
			want:    &pb.PiaGetFollowedStatusOfMicListResponse{},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m2 := testNewMainGameMgr(t)
			if tt.init != nil {
				tt.init(m2)
			}
			got, err := m2.PiaGetFollowedStatusOfMicList(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("MainGame.PiaGetFollowedStatusOfMicList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MainGame.PiaGetFollowedStatusOfMicList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMainGame_OperateBgmVolV2(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pia.PiaOperateBgmVolReq
	}
	tests := []struct {
		name     string
		args     args
		want     *pia.PiaOperateBgmVolResp
		wantErr  assert.ErrorAssertionFunc
		initFunc func(m *MainGameHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				ctx: context.Background(),
				req: &pia.PiaOperateBgmVolReq{
					ChannelId: 123,
					Vol:       10,
				},
			},
			want: &pia.PiaOperateBgmVolResp{
				BgmVolStatus: &pb.DramaBgmVolStatus{
					Vol: 10,
				},
			},
			wantErr: assert.NoError,
			initFunc: func(m *MainGameHelperForTest) {
				m.GetPiaCli().EXPECT().SetBgmVol(gomock.Any(), gomock.Any()).Return(&piapb.PiaOperateBgmVolResp{
					BgmVolStatus: &piapb.DramaBgmVolStatus{
						Vol: 10,
					},
				}, nil)
				m.GetConverter().EXPECT().DramaBgmVolStatusPBToLogic(gomock.Any()).Return(&pia.DramaBgmVolStatus{
					Vol: 10,
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.OperateBgmVolV2(tt.args.ctx, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("OperateBgmVolV2(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want, got, "OperateBgmVolV2(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}

func TestMainGame_OperateBgmV2(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pia.PiaOperateBgmReq
	}
	tests := []struct {
		name     string
		args     args
		want     *pia.PiaOperateBgmResp
		wantErr  assert.ErrorAssertionFunc
		initFunc func(m *MainGameHelperForTest)
	}{
		{
			name: "播放",
			args: args{
				ctx: context.Background(),
				req: &pia.PiaOperateBgmReq{
					ChannelId:     123,
					OperationType: pia.DramaBGMOperationType_DRAMA_BGM_OPERATION_TYPE_PLAY,
					BgmId:         "123",
					CurProgress:   123,
					NextProgress:  123,
					RoundId:       123,
				},
			},
			want: &pia.PiaOperateBgmResp{
				BgmStatus: &pia.DramaBgmStatus{},
			},
			wantErr: assert.NoError,
			initFunc: func(m *MainGameHelperForTest) {
				m.GetPiaCli().EXPECT().PlayBgm(gomock.Any(), gomock.Any()).Return(&piapb.PiaOperateBgmResp{}, nil)
				m.GetConverter().EXPECT().DramaBgmStatusPBToLogic(gomock.Any()).Return(&pia.DramaBgmStatus{})
			},
		},
		{
			name: "暂停",
			args: args{
				ctx: context.Background(),
				req: &pia.PiaOperateBgmReq{
					ChannelId:     123,
					OperationType: pia.DramaBGMOperationType_DRAMA_BGM_OPERATION_TYPE_PAUSE,
					BgmId:         "123",
					CurProgress:   123,
					NextProgress:  123,
					RoundId:       123,
				},
			},
			want: &pia.PiaOperateBgmResp{
				BgmStatus: &pia.DramaBgmStatus{},
			},
			wantErr: assert.NoError,
			initFunc: func(m *MainGameHelperForTest) {
				m.GetPiaCli().EXPECT().PauseBgm(gomock.Any(), gomock.Any()).Return(&piapb.PiaOperateBgmResp{}, nil)
				m.GetConverter().EXPECT().DramaBgmStatusPBToLogic(gomock.Any()).Return(&pia.DramaBgmStatus{})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.OperateBgmV2(tt.args.ctx, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("OperateBgmV2(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want, got, "OperateBgmV2(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}

func TestMainGame_ChangePlayingTypeV2(t *testing.T) {
	type args struct {
		c   context.Context
		req *pia.PiaChangePlayTypeReq
	}
	tests := []struct {
		name     string
		args     args
		want     *pia.PiaChangePlayTypeResp
		wantErr  assert.ErrorAssertionFunc
		initFunc func(s *MainGameHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				c:   context.Background(),
				req: &pia.PiaChangePlayTypeReq{},
			},
			want: &pia.PiaChangePlayTypeResp{
				ChannelDramaPlayingType: &pia.PiaChannelDramaPlayingType{},
			},
			wantErr: assert.NoError,
			initFunc: func(s *MainGameHelperForTest) {
				s.GetPiaCli().EXPECT().PiaChangePlayTypeV2(gomock.Any(), gomock.Any()).Return(&piapb.PiaChangePlayTypeV2Response{
					ChannelDramaPlayingType: &piapb.PiaChannelDramaPlayingType{
						SupportPlayingTypeRoles: []piapb.PiaChannelDramaPlayingType_PlayingTypeRole{
							piapb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE,
							piapb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_HAS,
						},
						SupportPlayingTypeTimes: []piapb.PiaChannelDramaPlayingType_PlayingTypeTime{
							piapb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE,
							piapb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_HAS,
						},
						CurrentPlayingTypeRole: piapb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE,
						CurrentPlayingTypeTime: piapb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE,
					},
				}, nil)
				s.GetConverter().EXPECT().ChannelDramaPlayingTypePBToLogic(gomock.Any()).Return(&pia.PiaChannelDramaPlayingType{})
				s.GetConverter().EXPECT().ChannelDramaPlayingTypeLogicToPB(gomock.Any()).Return(&piapb.PiaChannelDramaPlayingType{})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testNewMainGameMgr(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.ChangePlayingTypeV2(tt.args.c, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("ChangePlayingTypeV2(%v, %v)", tt.args.c, tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want, got, "ChangePlayingTypeV2(%v, %v)", tt.args.c, tt.args.req)
		})
	}
}
