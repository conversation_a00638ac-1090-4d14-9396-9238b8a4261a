package mgr

import (
	"context"
	"fmt"
	"google.golang.org/grpc/codes"
	"time"

	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protocolgrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/pia"
	gaPush "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/common/status"
	piaPb "golang.52tt.com/protocol/services/pia"
	"golang.52tt.com/services/pia-logic/conf"
)

const NewFeatureRefactorPreformDrama = "new_feature_refactor_preform_drama"

type MainGame struct {
	piaCli        piaPb.PiaClient
	converter     IConverter
	channelMicCli channelmic.IClient
	accountCli    account.IClient
	pushService   PushService
	config        conf.IConfig
}

func NewMainGame(
	config conf.IConfig,
	pushService PushService,
	accountCli account.IClient,
	piaCli piaPb.PiaClient,
	converter IConverter,
	channelMicCli channelmic.IClient,
) IMainGame {
	return &MainGame{
		config:        config,
		pushService:   pushService,
		accountCli:    accountCli,
		piaCli:        piaCli,
		converter:     converter,
		channelMicCli: channelMicCli,
	}
}

func (m *MainGame) SelectRole(c context.Context, req *pia.PiaSelectRoleReq) (*pia.PiaSelectRoleResp, error) {

	// 判断特性开关
	if m.config.GetFeatureTrigger().IsFeatureEnable(NewFeatureRefactorPreformDrama) {
		return m.SelectRoleV2(c, req)
	}

	log.DebugWithCtx(c, "[选择角色]开始处理，req：%v", req)
	// 获取当前请求的用户信息
	serviceInfo, ok := protocolgrpc.ServiceInfoFromContext(c)
	if !ok {
		log.ErrorWithCtx(c, "[选择角色]获取服务信息失败，req: %v", req)
		return nil, fmt.Errorf("获取服务信息失败")
	}
	// 判断用户是否在对应的麦上
	list, serverError := m.channelMicCli.GetMicrList(c, req.GetChannelId(), serviceInfo.UserID)
	if serverError != nil {
		log.ErrorWithCtx(c, "[选择角色]获取麦位列表失败，err：%v，req：%v", serverError, req)
		return nil, serverError
	}
	log.DebugWithCtx(c, "[选择角色]获取麦位列表成功，list：%v，req：%v", list, req)
	for _, item := range list.AllMicList {
		if item.GetMicUid() == serviceInfo.UserID && item.GetMicId() != req.GetMicNumber() {
			return nil, fmt.Errorf("用户不在对应的麦上")
		}
	}
	// 请求下游服务
	resp, err := m.piaCli.PiaSelectRole(c, &piaPb.PiaSelectRoleReq{
		DramaId:   req.GetDramaId(),
		ChannelId: req.GetChannelId(),
		RoleId:    req.GetRoleId(),
		MicNumber: req.GetMicNumber(),
	})
	if err != nil {
		return nil, err
	}
	return &pia.PiaSelectRoleResp{
		MicRoleMap: m.converter.MicRoleBindingSvrToLogic(resp.GetMicRoleMap()),
	}, nil
}

func (m *MainGame) CancelSelectRole(c context.Context, req *pia.PiaCancelSelectRoleReq) (*pia.PiaCancelSelectRoleResp, error) {
	// 判断特性开关
	if m.config.GetFeatureTrigger().IsFeatureEnable(NewFeatureRefactorPreformDrama) {
		return m.CancelSelectRoleV2(c, req)
	}

	// 请求下游服务
	resp, err := m.piaCli.PiaCancelSelectRole(c, &piaPb.PiaCancelSelectRoleReq{
		DramaId:   req.GetDramaId(),
		ChannelId: req.GetChannelId(),
		RoleId:    req.GetRoleId(),
		MicNumber: req.GetMicNumber(),
	})
	if err != nil {
		return nil, err
	}
	return &pia.PiaCancelSelectRoleResp{
		MicRoleMap: m.converter.MicRoleBindingSvrToLogic(resp.GetMicRoleMap()),
	}, nil
}

func (m *MainGame) SelectDramaV2(c context.Context, req *pia.SelectDramaV2Req) (*pia.SelectDramaV2Resp, error) {
	// 检查特性开关
	if m.config.GetFeatureTrigger().IsFeatureEnable(NewFeatureRefactorPreformDrama) {
		return m.PerformDramaV2(c, req)
	}
	resp, err := m.piaCli.SelectDramaV2(c, &piaPb.SelectDramaV2Req{
		DramaId:   req.GetDramaId(),
		ChannelId: req.GetChannelId(),
		IndexId:   req.GetIndexId(),
	})
	if err != nil {
		return nil, err
	}
	p := &pia.SelectDramaV2Resp{
		DramaStatus: m.converter.DramaStatusPBToLogic(resp.GetDramaStatus()),
	}
	// 如果是副本，还需要提供创建者
	if p.GetDramaStatus().GetDramaInfo().GetIsCopyDrama() {
		userInfo, serverError := m.accountCli.GetUserByUid(c, resp.GetDramaStatus().GetDramaInfo().GetDramaSubInfo().GetCreatorUid())
		if err != nil {
			log.ErrorWithCtx(c, "[演绎剧本]获取副本创建者失败，req:%+v, err:%v", req, serverError)
		} else {
			p.GetDramaStatus().GetDramaInfo().CreatorInfo = &pia.PiaDramaCreatorInfo{
				Uid:      userInfo.GetUid(),
				Nickname: userInfo.GetNickname(),
				Account:  userInfo.GetUsername(),
			}
		}
	}
	return p, nil
}

func (m *MainGame) OperateDrama(c context.Context, req *pia.PiaOperateDramaReq) (*pia.PiaOperateDramaResp, error) {
	// 检查特性开关
	if m.config.GetFeatureTrigger().IsFeatureEnable(NewFeatureRefactorPreformDrama) {
		return m.OperateDramaV2(c, req)
	}
	resp, err := m.piaCli.PiaOperateDrama(c, &piaPb.PiaOperateDramaReq{
		ChannelId:         req.GetChannelId(),
		OperationType:     piaPb.DramaOperationType(req.GetOperationType()),
		DramaSectionIndex: req.GetDramaSectionIndex(),
		DelayTime:         req.GetDelayTime(),
	})
	if err != nil {
		return nil, err
	}
	p := &pia.PiaOperateDramaResp{
		DramaStatus: m.converter.DramaStatusPBToLogic(resp.GetDramaStatus()),
	}
	// 如果是副本，还需要提供创建者
	if p.GetDramaStatus().GetDramaInfo().GetIsCopyDrama() {
		userInfo, serverError := m.accountCli.GetUserByUid(c, resp.GetDramaStatus().GetDramaInfo().GetDramaSubInfo().GetCreatorUid())
		if err != nil {
			log.ErrorWithCtx(c, "[走本操作]获取副本创建者失败，req:%+v, err:%v", req, serverError)
		} else {
			p.GetDramaStatus().GetDramaInfo().CreatorInfo = &pia.PiaDramaCreatorInfo{
				Uid:      userInfo.GetUid(),
				Nickname: userInfo.GetNickname(),
				Account:  userInfo.GetUsername(),
			}
		}
	}
	return p, nil
}

func (m *MainGame) GetDramaStatus(c context.Context, req *pia.PiaGetDramaStatusReq) (*pia.PiaGetDramaStatusResp, error) {

	// 检查特性开关
	if m.config.GetFeatureTrigger().IsFeatureEnable(NewFeatureRefactorPreformDrama) {
		return m.GetDramaStatusV2(c, req)
	}

	dramaStatus, err := m.piaCli.PiaGetDramaStatus(c, &piaPb.PiaGetDramaStatusReq{
		ChannelId: req.GetChannelId(),
	})
	if err != nil {
		return nil, err
	}
	return &pia.PiaGetDramaStatusResp{
		DramaInfoDetail: m.converter.ChannelDramaInfoDetailPBToLogic(dramaStatus.GetDramaInfoDetail()),
	}, nil
}

func (m *MainGame) OperateBgm(c context.Context, req *pia.PiaOperateBgmReq) (*pia.PiaOperateBgmResp, error) {

	// 检查特性开关
	if m.config.GetFeatureTrigger().IsFeatureEnable(NewFeatureRefactorPreformDrama) {
		return m.OperateBgmV2(c, req)
	}

	bgm, err := m.piaCli.PiaOperateBgm(c, &piaPb.PiaOperateBgmReq{
		ChannelId:     req.GetChannelId(),
		OperationType: piaPb.DramaBGMOperationType(req.GetOperationType()),
		BgmId:         req.GetBgmId(),
		CurProgress:   req.GetCurProgress(),
		NextProgress:  req.GetNextProgress(),
	})
	if err != nil {
		return nil, err
	}
	return &pia.PiaOperateBgmResp{
		BgmStatus: m.converter.DramaBgmStatusPBToLogic(bgm.GetBgmStatus()),
	}, nil
}

func (m *MainGame) OperateBgmVol(c context.Context, req *pia.PiaOperateBgmVolReq) (*pia.PiaOperateBgmVolResp, error) {
	// 检查特性开关
	if m.config.GetFeatureTrigger().IsFeatureEnable(NewFeatureRefactorPreformDrama) {
		return m.OperateBgmVolV2(c, req)
	}

	vol, err := m.piaCli.PiaOperateBgmVol(c, &piaPb.PiaOperateBgmVolReq{
		ChannelId: req.GetChannelId(),
		Vol:       req.GetVol(),
	})
	if err != nil {
		log.ErrorWithCtx(c, "PiaOperateBgmVol failed to call piaCli, req:%+v, err:%v", req, err)
		return nil, err
	}
	return &pia.PiaOperateBgmVolResp{
		BgmVolStatus: m.converter.DramaBgmVolStatusPBToLogic(vol.GetBgmVolStatus()),
	}, nil
}

func (m *MainGame) ChangePlayingType(c context.Context, req *pia.PiaChangePlayTypeReq) (*pia.PiaChangePlayTypeResp, error) {
	// 检查特性开关
	if m.config.GetFeatureTrigger().IsFeatureEnable(NewFeatureRefactorPreformDrama) {
		log.DebugWithCtx(c, "[切换走本方式]进入v2 req:%+v, trigger: %+v", req, m.config.GetFeatureTrigger())
		return m.ChangePlayingTypeV2(c, req)
	}
	log.DebugWithCtx(c, "[切换走本方式]PiaChangePlayType req:%+v", req)
	playType, err := m.piaCli.PiaChangePlayType(c, &piaPb.PiaChangePlayTypeReq{
		ChannelDramaPlayingType: m.converter.ChannelDramaPlayingTypeLogicToPB(req.GetChannelDramaPlayingType()),
		ChannelId:               req.GetChannelId(),
		RoundId:                 req.GetRoundId(),
	})
	if err != nil {
		log.ErrorWithCtx(c, "[切换走本方式]PiaChangePlayType failed to call piaCli, req:%+v, err:%v", req, err)
		return nil, err
	}
	return &pia.PiaChangePlayTypeResp{
		ChannelDramaPlayingType: m.converter.ChannelDramaPlayingTypePBToLogic(playType.GetChannelDramaPlayingType()),
	}, nil
}

// PerformDrama 选择演绎剧本
// 1、先结束当前的走本，但是不推送消息
// 2、再 select 新的剧本
func (m *MainGame) PerformDrama(c context.Context, request *pia.PiaPerformDramaRequest) (*pia.PiaPerformDramaResponse, error) {
	// 1、先结束当前的走本，但是不推送消息
	if request.GetRoundId() != 0 {
		_, err := m.piaCli.PiaOperateDrama(c, &piaPb.PiaOperateDramaReq{
			ChannelId:     request.GetChannelId(),
			OperationType: piaPb.DramaOperationType_DRAMA_OPERATION_TYPE_END,
			RoundId:       request.GetRoundId(),
			IsIgnorePush:  true,
		})
		// 如果结束出错不需要特别的处理，暂时输出日志记录
		if err != nil {
			log.ErrorWithCtx(c, "[选择演绎剧本]结束当前剧本失败，req:%+v, err:%v", request, err)
		}
	}
	// 2、再 select 新的剧本
	resp, err := m.piaCli.SelectDramaV2(c, &piaPb.SelectDramaV2Req{
		DramaId:   request.GetDramaId(),
		ChannelId: request.GetChannelId(),
		IndexId:   request.GetIndexId(),
	})
	if err != nil {
		log.ErrorWithCtx(c, "[选择演绎剧本]选择剧本失败，req:%+v, err:%v", request, err)
		return &pia.PiaPerformDramaResponse{}, err
	}
	p := &pia.PiaPerformDramaResponse{
		DramaStatus: m.converter.DramaStatusPBToLogic(resp.GetDramaStatus()),
	}
	// 如果是副本，还需要提供创建者
	if p.GetDramaStatus().GetDramaInfo().GetIsCopyDrama() {
		userInfo, serverError := m.accountCli.GetUserByUid(c, resp.GetDramaStatus().GetDramaInfo().GetDramaSubInfo().GetCreatorUid())
		if err != nil {
			log.ErrorWithCtx(c, "[选择演绎剧本]获取副本创建者失败，req:%+v, err:%v", request, serverError)
		} else {
			p.GetDramaStatus().GetDramaInfo().CreatorInfo = &pia.PiaDramaCreatorInfo{
				Uid:      userInfo.GetUid(),
				Nickname: userInfo.GetNickname(),
				Account:  userInfo.GetUsername(),
			}
		}
	}
	return p, nil
}

// SendDialogueIndex 发送剧本段落
func (m *MainGame) SendDialogueIndex(ctx context.Context, request *pia.PiaSendDialogueIndexRequest) error {
	err := m.pushService.PushMsgToUsers(ctx, gaPush.PushMessage_PIA_DIALOGUE_INDEX_LOCATION_PUSH, &pia.PiaDialogueIndexLocationMsg{
		DialogueIndex: request.GetDialogueIndex(),
		ChannelId:     request.GetChannelId(),
		RoundId:       request.GetRoundId(),
		Version:       time.Now().Unix(),
	}, request.GetUidList()...)
	if err != nil {
		return fmt.Errorf("调用推送服务失败：%v", err)
	}
	return nil
}

// PerformDramaV2 演绎剧本
func (m *MainGame) PerformDramaV2(ctx context.Context, req *pia.SelectDramaV2Req) (*pia.SelectDramaV2Resp, error) {
	log.DebugWithCtx(ctx, "[演绎剧本V2]开始演绎剧本，req:%+v", req)
	// 1、生成一个剧本的缓存copy
	tempDramaResp, err := m.piaCli.CreateTempDrama(ctx, &piaPb.PiaCreateTempDramaRequest{
		DramaId: req.GetDramaId(),
	})
	if err != nil {
		return nil, fmt.Errorf("生成一个剧本的缓存copy失败：%v", err)
	}

	// 组装请求体
	in := &piaPb.PerformDramaRequest{
		TempDramaId: tempDramaResp.GetTempDramaId(),
		ChannelId:   req.GetChannelId(),
		DramaInfo:   tempDramaResp.GetDrama(),
	}

	// 2、调用pia的接口，创建演绎数据
	resp, err := m.piaCli.PreformDrama(ctx, in)
	if err != nil {
		return nil, fmt.Errorf("调用pia的接口失败：%v", err)
	}

	// 3、构造返回值
	p := &pia.SelectDramaV2Resp{
		DramaStatus: m.converter.DramaStatusPBToLogic(resp.GetDramaStatus()),
	}
	p.DramaStatus.DramaInfo = m.converter.DramaV2PBToLogic(tempDramaResp.GetDrama())

	// 4、如果是副本，还需要提供创建者
	if tempDramaResp.GetDrama().GetDramaSubInfo().GetCreatorUid() != 0 {
		userInfo, serverError := m.accountCli.GetUserByUid(ctx, tempDramaResp.GetDrama().GetDramaSubInfo().GetCreatorUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "[演绎剧本]获取副本创建者失败，req:%+v, err:%v", req, serverError)
		} else {
			p.GetDramaStatus().GetDramaInfo().CreatorInfo = &pia.PiaDramaCreatorInfo{
				Uid:      userInfo.GetUid(),
				Nickname: userInfo.GetNickname(),
				Account:  userInfo.GetUsername(),
			}
		}
	}

	return p, nil
}

func checkFollowReq(roundId int64, channelId uint32, uid uint32) bool {
	return roundId > 0 && channelId > 0
}

func (m *MainGame) FollowMic(c context.Context, request *pia.PiaFollowMicRequest) (*pia.PiaFollowMicResponse, error) {
	// 获取当前请求的用户信息
	out := &pia.PiaFollowMicResponse{}
	serviceInfo, ok := protocolgrpc.ServiceInfoFromContext(c)
	if !ok {
		log.ErrorWithCtx(c, "[选择角色]获取服务信息失败，req: %v", request)
		return out, fmt.Errorf("获取服务信息失败")
	}
	if !checkFollowReq(request.GetRoundId(), request.GetChannelId(), serviceInfo.UserID) {
		return &pia.PiaFollowMicResponse{}, protocol.NewExactServerError(codes.OK, status.ErrParam)
	}

	// 跟随虚拟麦时，不需要检查麦上用户跟随状态
	if request.GetTargetMic() != pia.PiaMicType_PIA_MIC_TYPE_VIRTUAL {
		// 检查目标麦位是否有用户
		channelMicList, sErr := m.channelMicCli.GetMicrList(c, request.GetChannelId(), serviceInfo.UserID)
		if sErr != nil {
			log.ErrorWithCtx(c, "FollowMic failed to GetMicrList, req:%+v, err:%v", request, sErr)
			return out, sErr
		}
		onMic := false
		targetMicUser := uint32(0)
		for _, mic := range channelMicList.GetAllMicList() {
			if mic.MicId == m.pbMicType2Uint32(request.GetTargetMic()) && mic.GetMicUid() != 0 {
				onMic = true
				targetMicUser = mic.GetMicUid()
			}
		}

		if !onMic {
			err := protocol.NewExactServerError(codes.OK, status.ErrPiaMicFollowFail, "目标用户已下麦")
			log.ErrorWithCtx(c, "FollowMic invalid target mic, req:%+v, err:%v", request, err)
			return out, err
		}

		// 检查目标麦位是否已跟随
		followInfo, err := m.piaCli.PiaGetMyFollowInfo(c, &piaPb.PiaGetMyFollowInfoRequest{
			Uid:       targetMicUser,
			ChannelId: request.GetChannelId(),
			RoundId:   request.GetRoundId(),
		})
		if err != nil {
			log.ErrorWithCtx(c, "FollowMic failed to PiaGetMyFollowInfo, requeset:%v, targetMicUser:%d, err:%v", request, targetMicUser, err)
			return out, err
		}
		if followInfo.GetTargetMic() != uint32(pia.PiaMicType_PIA_MIC_TYPE_NIL) && followInfo.GetTargetMic() != uint32(pia.PiaMicType_PIA_MIC_TYPE_VIRTUAL) {
			err := protocol.NewExactServerError(codes.OK, status.ErrPiaMicFollowFail, "目标用户处于跟随状态，不可跟随")
			log.ErrorWithCtx(c, "FollowMic invalid target mic, req:%+v, err:%v", request, err)
			return out, err
		}
	}

	// 发起跟随请求
	resp, err := m.piaCli.PiaFollowMic(c, &piaPb.PiaFollowMicRequest{
		Uid:       serviceInfo.UserID,
		ChannelId: request.GetChannelId(),
		RoundId:   request.GetRoundId(),
		MyMic:     uint32(request.GetMyMic()),
		TargetMic: uint32(request.GetTargetMic()),
	})
	if err != nil {
		log.ErrorWithCtx(c, "FollowMic failed to call piaSvr, req:%+v,err:%v ", request, err)
		return out, err
	}
	out.DialogueIndex = resp.GetDialogueIndex()
	return out, nil
}

func (m *MainGame) pbMicType2Uint32(micNum pia.PiaMicType) uint32 {
	switch micNum {
	case pia.PiaMicType_PIA_MIC_TYPE_ZERO:
		return 1
	case pia.PiaMicType_PIA_MIC_TYPE_ONE:
		return 2
	case pia.PiaMicType_PIA_MIC_TYPE_TWO:
		return 3
	case pia.PiaMicType_PIA_MIC_TYPE_THREE:
		return 4
	case pia.PiaMicType_PIA_MIC_TYPE_FOUR:
		return 5
	case pia.PiaMicType_PIA_MIC_TYPE_FIVE:
		return 6
	case pia.PiaMicType_PIA_MIC_TYPE_SIX:
		return 7
	case pia.PiaMicType_PIA_MIC_TYPE_SEVEN:
		return 8
	case pia.PiaMicType_PIA_MIC_TYPE_EIGHT:
		return 9
	default:
		return 0
	}
}

func (m *MainGame) UnFollowMic(c context.Context, request *pia.PiaUnFollowMicRequest) error {
	serviceInfo, ok := protocolgrpc.ServiceInfoFromContext(c)
	if !ok {
		log.ErrorWithCtx(c, "[选择角色]获取服务信息失败，req: %v", request)
		return fmt.Errorf("获取服务信息失败")
	}
	if !checkFollowReq(request.GetRoundId(), request.GetChannelId(), serviceInfo.UserID) {
		return protocol.NewExactServerError(codes.OK, status.ErrParam)
	}

	_, err := m.piaCli.PiaUnFollowMic(c, &piaPb.PiaUnFollowMicRequest{
		Uid:       serviceInfo.UserID,
		ChannelId: request.GetChannelId(),
		RoundId:   request.GetRoundId(),
		MyMic:     request.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(c, "UnFollowMic failed to call piaSvr, req:%+v, err:%v", request, err)
		return err
	}
	return nil
}

func (m *MainGame) ReportDialogueIndex(c context.Context, request *pia.PiaReportDialogueIndexRequest) error {
	serviceInfo, ok := protocolgrpc.ServiceInfoFromContext(c)
	if !ok {
		log.ErrorWithCtx(c, "[选择角色]获取服务信息失败，req: %v", request)
		return fmt.Errorf("获取服务信息失败")
	}
	if !checkFollowReq(request.GetRoundId(), request.GetChannelId(), serviceInfo.UserID) || request.GetMyMic() == 0 {
		return protocol.NewExactServerError(codes.OK, status.ErrParam)
	}
	_, err := m.piaCli.PiaReportDialogueIndex(c, &piaPb.PiaReportDialogueIndexRequest{
		Uid:           serviceInfo.UserID,
		RoundId:       request.GetRoundId(),
		ChannelId:     request.GetChannelId(),
		DialogueIndex: request.GetDialogueIndex(),
		MyMic:         uint32(request.GetMyMic()),
	})
	if err != nil {
		log.ErrorWithCtx(c, "ReportDialogueIndex failed to call piaSvr, req:%+v, err:%v", request, err)
		return err
	}
	return nil
}

func (m *MainGame) GetPreviousDialogueIndex(c context.Context, request *pia.PiaGetPreviousDialogueIndexRequest) (*pia.PiaGetPreviousDialogueIndexResponse, error) {
	out := &pia.PiaGetPreviousDialogueIndexResponse{}
	serviceInfo, ok := protocolgrpc.ServiceInfoFromContext(c)
	if !ok {
		log.ErrorWithCtx(c, "[选择角色]获取服务信息失败，req: %v", request)
		return out, fmt.Errorf("获取服务信息失败")
	}
	if !checkFollowReq(request.GetRoundId(), request.GetChannelId(), serviceInfo.UserID) || request.GetMyMic() == 0 {
		return out, protocol.NewExactServerError(codes.OK, status.ErrParam)
	}
	resp, err := m.piaCli.PiaGetPreviousDialogueIndex(c, &piaPb.PiaGetPreviousDialogueIndexRequest{
		Uid:       serviceInfo.UserID,
		ChannelId: request.GetChannelId(),
		RoundId:   request.GetRoundId(),
		MyMic:     uint32(request.GetMyMic()),
	})
	if err != nil {
		log.ErrorWithCtx(c, "GetPreviousDialogueIndex failed to call piaSvr, uid:%d, req:%+v, err:%v", serviceInfo.UserID, request, err)
		return out, err
	}
	out.DialogueIndex = resp.GetDialogueIndex()
	return out, nil
}

func (m *MainGame) GetMyFollowInfo(c context.Context, request *pia.PiaGetMyFollowInfoRequest) (*pia.PiaGetMyFollowInfoResponse, error) {
	out := &pia.PiaGetMyFollowInfoResponse{}
	serviceInfo, ok := protocolgrpc.ServiceInfoFromContext(c)
	if !ok {
		log.ErrorWithCtx(c, "[选择角色]获取服务信息失败，req: %v", request)
		return out, fmt.Errorf("获取服务信息失败")

	}
	if !checkFollowReq(request.GetRoundId(), request.GetChannelId(), serviceInfo.UserID) {
		return out, protocol.NewExactServerError(codes.OK, status.ErrParam)
	}
	resp, err := m.piaCli.PiaGetMyFollowInfo(c, &piaPb.PiaGetMyFollowInfoRequest{
		Uid:       serviceInfo.UserID,
		ChannelId: request.GetChannelId(),
		RoundId:   request.GetRoundId(),
	})
	if err != nil {
		log.ErrorWithCtx(c, "GetMyFollowInfo failed to call piaSvr, req:%+v, err:%v", request, err)
		return out, err
	}

	out.TargetMic = pia.PiaMicType(resp.GetTargetMic())
	return out, nil
}

// 获取当前房间各个麦位的跟随状态
func (m *MainGame) PiaGetFollowedStatusOfMicList(c context.Context, req *pia.PiaGetFollowedStatusOfMicListRequest) (*pia.PiaGetFollowedStatusOfMicListResponse, error) {
	out := &pia.PiaGetFollowedStatusOfMicListResponse{}
	if req.GetChannelId() == 0 || req.GetRoundId() == 0 {
		return out, protocol.NewExactServerError(codes.OK, status.ErrParam)
	}
	// 获取当前请求的用户信息
	serviceInfo, ok := protocolgrpc.ServiceInfoFromContext(c)
	if !ok {
		log.ErrorWithCtx(c, "[获取当前房间各个麦位的跟随状态]获取服务信息失败，req: %v", req)
		return out, fmt.Errorf("获取服务信息失败")
	}

	// 获取麦位信息
	micInfoList, err := m.channelMicCli.GetMicrList(c, req.GetChannelId(), serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(c, "PiaGetFollowedStatusOfMicList failed to GetMicrList, req:%+v, err:%v", req, err)
		return out, err
	}
	uidList := make([]uint32, 0, len(micInfoList.GetAllMicList()))
	for _, mic := range micInfoList.GetAllMicList() {
		if mic.GetMicUid() != 0 {
			uidList = append(uidList, mic.GetMicUid())
		}
	}

	resp, sErr := m.piaCli.PiaGetFollowedStatusOfMicList(c, &piaPb.PiaGetFollowedStatusOfMicListRequest{
		ChannelId: req.GetChannelId(),
		RoundId:   req.GetRoundId(),
		UidList:   uidList,
	})
	if sErr != nil {
		log.ErrorWithCtx(c, "PiaGetFollowedStatusOfMicList failed to call piaSvr, req:%+v, err:%v", req, err)
		return out, sErr
	}

	out.MicStatusList = make([]*pia.PiaGetFollowedStatusOfMicListResponse_PiaMicStatus, 0, len(micInfoList.GetAllMicList()))
	for _, v := range micInfoList.GetAllMicList() {
		out.MicStatusList = append(out.MicStatusList, &pia.PiaGetFollowedStatusOfMicListResponse_PiaMicStatus{
			MicNum:        v.GetMicId(),
			CanBeFollowed: resp.GetUidStatusMap()[v.GetMicUid()],
		})
	}
	return out, nil
}

func (m *MainGame) OperateDramaV2(ctx context.Context, req *pia.PiaOperateDramaReq) (*pia.PiaOperateDramaResp, error) {

	// 判断操作类型
	var resp *piaPb.PiaOperateDramaResp
	var err error
	in := &piaPb.PiaOperateDramaReq{
		ChannelId:         req.GetChannelId(),
		OperationType:     piaPb.DramaOperationType(req.GetOperationType()),
		DramaSectionIndex: req.GetDramaSectionIndex(),
		DelayTime:         req.GetDelayTime(),
	}

	// 调用下游服务
	switch req.GetOperationType() {
	case pia.DramaOperationType_DRAMA_OPERATION_TYPE_PLAY:
		resp, err = m.piaCli.PlayDrama(ctx, in)
	case pia.DramaOperationType_DRAMA_OPERATION_TYPE_PAUSE:
		resp, err = m.piaCli.PauseDrama(ctx, in)
	case pia.DramaOperationType_DRAMA_OPERATION_TYPE_END:
		resp, err = m.piaCli.StopDrama(ctx, in)
	default:
		return nil, fmt.Errorf("操作类型不正确: %v: %w", req.GetOperationType(), ErrMissingParam)

	}
	if err != nil {
		return nil, fmt.Errorf("调用pia服务失败: %w", err)
	}

	// 组装数据
	p := &pia.PiaOperateDramaResp{
		DramaStatus: m.converter.DramaStatusPBToLogic(resp.GetDramaStatus()),
	}

	return p, nil
}

func (m *MainGame) SelectRoleV2(ctx context.Context, req *pia.PiaSelectRoleReq) (*pia.PiaSelectRoleResp, error) {
	log.DebugWithCtx(ctx, "[选择角色]开始处理，req：%v", req)
	// 获取当前请求的用户信息
	serviceInfo, ok := protocolgrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return nil, fmt.Errorf("获取服务信息失败")
	}
	// 判断用户是否在对应的麦上
	list, serverError := m.channelMicCli.GetMicrList(ctx, req.GetChannelId(), serviceInfo.UserID)
	if serverError != nil {
		return nil, fmt.Errorf("获取麦位列表失败: %w", serverError)
	}
	log.DebugWithCtx(ctx, "[选择角色]获取麦位列表成功，list：%v，req：%v", list, req)
	for _, item := range list.AllMicList {
		if item.GetMicUid() == serviceInfo.UserID && item.GetMicId() != req.GetMicNumber() {
			return nil, fmt.Errorf("用户不在对应的麦上")
		}
	}
	// 请求下游服务
	resp, err := m.piaCli.PiaSelectRoleV2(ctx, &piaPb.PiaSelectRoleReq{
		DramaId:   req.GetDramaId(),
		ChannelId: req.GetChannelId(),
		RoleId:    req.GetRoleId(),
		MicNumber: req.GetMicNumber(),
	})
	if err != nil {
		return nil, fmt.Errorf("调用下游服务失败: %w", err)
	}
	p := &pia.PiaSelectRoleResp{
		MicRoleMap: m.converter.MicRoleBindingSvrToLogic(resp.GetMicRoleMap()),
	}
	return p, nil
}

func (m *MainGame) CancelSelectRoleV2(ctx context.Context, req *pia.PiaCancelSelectRoleReq) (*pia.PiaCancelSelectRoleResp, error) {
	// 请求下游服务
	resp, err := m.piaCli.PiaCancelSelectRoleV2(ctx, &piaPb.PiaCancelSelectRoleReq{
		DramaId:   req.GetDramaId(),
		ChannelId: req.GetChannelId(),
		RoleId:    req.GetRoleId(),
		MicNumber: req.GetMicNumber(),
	})
	if err != nil {
		return nil, fmt.Errorf("调用下游服务失败: %w", err)
	}
	p := &pia.PiaCancelSelectRoleResp{
		MicRoleMap: m.converter.MicRoleBindingSvrToLogic(resp.GetMicRoleMap()),
	}
	return p, nil
}

func (m *MainGame) OperateBgmV2(ctx context.Context, req *pia.PiaOperateBgmReq) (*pia.PiaOperateBgmResp, error) {

	var resp *piaPb.PiaOperateBgmResp
	var err error
	in := &piaPb.PiaOperateBgmReq{
		ChannelId:     req.GetChannelId(),
		OperationType: piaPb.DramaBGMOperationType(req.GetOperationType()),
		BgmId:         req.GetBgmId(),
		CurProgress:   req.GetCurProgress(),
		NextProgress:  req.GetNextProgress(),
	}
	// 判断操作类型
	switch req.GetOperationType() {
	case pia.DramaBGMOperationType_DRAMA_BGM_OPERATION_TYPE_PLAY:
		resp, err = m.piaCli.PlayBgm(ctx, in)
	case pia.DramaBGMOperationType_DRAMA_BGM_OPERATION_TYPE_PAUSE:
		resp, err = m.piaCli.PauseBgm(ctx, in)
	default:
		return nil, fmt.Errorf("不支持的操作类型")
	}
	if err != nil {
		return nil, fmt.Errorf("调用下游服务失败: %w", err)
	}
	p := &pia.PiaOperateBgmResp{
		BgmStatus: m.converter.DramaBgmStatusPBToLogic(resp.GetBgmStatus()),
	}

	return p, nil
}

func (m *MainGame) OperateBgmVolV2(ctx context.Context, req *pia.PiaOperateBgmVolReq) (*pia.PiaOperateBgmVolResp, error) {
	vol, err := m.piaCli.SetBgmVol(ctx, &piaPb.PiaOperateBgmVolReq{
		ChannelId: req.GetChannelId(),
		Vol:       req.GetVol(),
	})
	if err != nil {
		return nil, fmt.Errorf("调用pia服务失败: %w", err)
	}
	p := &pia.PiaOperateBgmVolResp{
		BgmVolStatus: m.converter.DramaBgmVolStatusPBToLogic(vol.GetBgmVolStatus()),
	}
	return p, nil
}

func (m *MainGame) GetDramaStatusV2(c context.Context, req *pia.PiaGetDramaStatusReq) (*pia.PiaGetDramaStatusResp, error) {
	log.DebugWithCtx(c, "获取房间走本详细信息 请求参数：%v", req)
	// 请求下游服务
	dramaStatus, err := m.piaCli.PiaGetDramaStatusV2(c, &piaPb.PiaGetDramaStatusReq{
		ChannelId: req.GetChannelId(),
	})
	if err != nil {
		return nil, fmt.Errorf("调用下游服务获取走本信息失败: %w", err)
	}

	resp := &pia.PiaGetDramaStatusResp{
		DramaInfoDetail: m.converter.ChannelDramaInfoDetailPBToLogic(dramaStatus.GetDramaInfoDetail()),
	}

	// 兼容旧数据，如果有临时剧本id，则认为是新数据，需要查询剧本信息
	if len(dramaStatus.GetDramaInfoDetail().GetDramaStatus().GetTempDramaId()) != 0 {
		// 查询剧本信息
		tempDrama, err := m.piaCli.GetTempDrama(c, &piaPb.PiaGetTempDramaRequest{
			TempDramaId: dramaStatus.GetDramaInfoDetail().GetDramaStatus().GetTempDramaId(),
		})
		if err != nil {
			return nil, fmt.Errorf("调用下游服务获取临时剧本信息失败失败: %w", err)
		}
		// 填充原始的剧本信息
		resp.GetDramaInfoDetail().OriginDramaInfo = m.converter.DramaV2PBToLogic(tempDrama.GetDrama())
		resp.GetDramaInfoDetail().GetDramaStatus().DramaInfo = m.converter.DramaV2PBToLogic(tempDrama.GetDrama())
	}

	// 填充修改过走本方式的剧本信息，为了兼容旧版本
	playingType := dramaStatus.GetDramaInfoDetail().GetDramaStatus().GetPlayingType()
	if playingType.GetCurrentPlayingTypeTime() == piaPb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE {
		// 如果是无时间，则修改剧本类型
		resp.GetDramaInfoDetail().GetDramaStatus().GetDramaInfo().PlayType = pia.PiaPlayType_PLAY_TYPE_MANUAL
	}
	if playingType.GetCurrentPlayingTypeRole() == piaPb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE {
		// 如果是没有角色的，则清空角色列表
		resp.GetDramaInfoDetail().GetDramaStatus().GetDramaInfo().RoleList = nil
	}
	// 如果是副本，还需要提供创建者
	if resp.GetDramaInfoDetail().GetDramaStatus().GetDramaInfo().GetIsCopyDrama() {
		userInfo, serverError := m.accountCli.GetUserByUid(c, resp.GetDramaInfoDetail().GetDramaStatus().GetDramaInfo().GetCreatorInfo().GetUid())
		if err != nil {
			log.ErrorWithCtx(c, "[演绎剧本]获取副本创建者失败，req:%+v, err:%v", req, serverError)
		} else {
			resp.GetDramaInfoDetail().GetDramaStatus().GetDramaInfo().CreatorInfo = &pia.PiaDramaCreatorInfo{
				Uid:      userInfo.GetUid(),
				Nickname: userInfo.GetNickname(),
				Account:  userInfo.GetUsername(),
			}
		}
	}

	return resp, nil
}

// ChangePlayingTypeV2 切换走本方式v2
func (m *MainGame) ChangePlayingTypeV2(c context.Context, req *pia.PiaChangePlayTypeReq) (*pia.PiaChangePlayTypeResp, error) {
	log.DebugWithCtx(c, "切换走本方式v2 请求参数：%v", req)
	// 修改走本方式
	playType, err := m.piaCli.PiaChangePlayTypeV2(c, &piaPb.PiaChangePlayTypeV2Request{
		ChannelDramaPlayingType: m.converter.ChannelDramaPlayingTypeLogicToPB(req.GetChannelDramaPlayingType()),
		ChannelId:               req.GetChannelId(),
		RoundId:                 req.GetRoundId(),
	})
	if err != nil {
		return nil, fmt.Errorf("切换走本方式v2 调用下游服务切换走本方式失败: %w", err)
	}
	return &pia.PiaChangePlayTypeResp{
		ChannelDramaPlayingType: m.converter.ChannelDramaPlayingTypePBToLogic(playType.GetChannelDramaPlayingType()),
	}, nil
}
