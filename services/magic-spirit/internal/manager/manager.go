package manager

import (
    "context"
    "errors"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    chance_game_entry "golang.52tt.com/clients/chance-game-entry"
    "golang.52tt.com/clients/channel"
    official_live_channel "golang.52tt.com/clients/official-live-channel"
    present_middleware "golang.52tt.com/clients/present-middleware"
    probgamecenter "golang.52tt.com/clients/prob-game-center"
    userprofileapi "golang.52tt.com/clients/user-profile-api"
    "golang.52tt.com/pkg/deal_token"
    kfk_magic_spirit "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_magic_spirit"
    pmPB "golang.52tt.com/protocol/services/present-middleware"
    probgamecenter2 "golang.52tt.com/protocol/services/probgamecenter"
    publicNoticePB "golang.52tt.com/protocol/services/public-notice"
    reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
    "golang.52tt.com/protocol/services/userpresent"
    "golang.52tt.com/services/magic-spirit/internal/event/producer"
    "strconv"
    "sync"
    "time"

    "golang.52tt.com/clients/exp"
    "golang.52tt.com/clients/numeric"
    "golang.52tt.com/clients/realnameauth"

    "github.com/jinzhu/gorm"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/clients/account"
    apicentergo "golang.52tt.com/clients/apicenter-go"
    apicenter "golang.52tt.com/clients/apicenter/apiserver"
    channelmsgexpress "golang.52tt.com/clients/channel-msg-express"
    PushNotification "golang.52tt.com/clients/push-notification/v2"
    "golang.52tt.com/clients/seqgen/v2"
    unifiedPay "golang.52tt.com/clients/unified_pay"
    userPresent "golang.52tt.com/clients/userpresent"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/magic-spirit"

    "golang.52tt.com/services/magic-spirit/internal/cache"
    "golang.52tt.com/services/magic-spirit/internal/conf"
    "golang.52tt.com/services/magic-spirit/internal/define"
    "golang.52tt.com/services/magic-spirit/internal/mysql"
)

const (
	CONF_VAL_TYPE_INT = 1
)

//go:generate mockgen -destination=../mocks/public_notice_mock.go -package=mocks golang.52tt.com/protocol/services/public-notice PublicNoticeClient
type MagicSpiritMgr struct {
	shutDown    chan interface{}
	wg          sync.WaitGroup
	mysql       mysql.IStore
	cache       cache.ICache
	sc          *conf.ServiceConfigT
	bc          conf.IBusinessConfManager
	lotteryPond *MagicPondLocalCache
    timeD       *timer.Timer

	apiCenterCli        apicenter.IClient
	seqCli              seqgen.IClient
	pushCli             PushNotification.IClient
	accountCli          account.IClient
	channelMsgCli       channelmsgexpress.IClient
	presentCli          userPresent.IClient
	unifiedPayCli       unifiedPay.IClient
	apiCenterGoCli      apicentergo.IClient
	numericCli          numeric.IClient
	expCli              exp.IClient
	realNameCli         realnameauth.IClient
	presentMWCli        present_middleware.IClient
	officialChannelCli  official_live_channel.IClient
	channelCli          channel.IClient
	probGameCli         probgamecenter.IClient
	userProfileCli      userprofileapi.IClient
	chanceGameEntryCli  chance_game_entry.IClient
	publicNoticeCli     publicNoticePB.PublicNoticeClient
	magicSpiritProducer producer.IMagicSpiritEventProducer
}

func NewMagicSpiritMgr(db *mysql.Store, redisCache *cache.Cache, sc *conf.ServiceConfigT, magicSpiritProducer producer.IMagicSpiritEventProducer) (*MagicSpiritMgr, error) {
	lotteryPond, err := NewMagicPondLocalCache(db, redisCache)
	if err != nil {
		log.Errorf("NewMagicSpiritMgr fail to NewMagicPondLocalCache. err:%v", err)
		return &MagicSpiritMgr{}, err
	}

	apiCenterCli := apicenter.NewClient()
	seqCli, _ := seqgen.NewClient()
	pushCli, _ := PushNotification.NewClient()
	accountCli, _ := account.NewClient()
	channelMsgCli, _ := channelmsgexpress.NewClient()
	presentCli := userPresent.NewClient()
	unifiedPayCli, _ := unifiedPay.NewClient()
	//apiCenterGoCli, _ := apicentergo.NewClient(grpc.WithAuthority("apicenter-go.52tt.local"))
    apiCenterGoCli, _ := apicentergo.NewClient()
	numericCli := numeric.NewClient()
	expCli := exp.NewClient()
	realNameCli := realnameauth.NewClient()
	presentMWCli, _ := present_middleware.NewClient()
	officialChannelCli, _ := official_live_channel.NewClient()
	channelCli := channel.NewClient()
	probGameCli, _ := probgamecenter.NewClient()
	userProfileCli, _ := userprofileapi.NewClient()
	chanceGameEntryCli, _ := chance_game_entry.NewClient()
	publicNoticeCli, _ := publicNoticePB.NewClient(context.Background())

	mgr := &MagicSpiritMgr{
		shutDown:    make(chan interface{}),
		mysql:       db,
		cache:       redisCache,
		sc:          sc,
		bc:          conf.NewBusinessConfManager(),
		lotteryPond: lotteryPond,

		apiCenterCli:        apiCenterCli,
		seqCli:              seqCli,
		pushCli:             pushCli,
		accountCli:          accountCli,
		channelMsgCli:       channelMsgCli,
		presentCli:          presentCli,
		unifiedPayCli:       unifiedPayCli,
		apiCenterGoCli:      apiCenterGoCli,
		numericCli:          numericCli,
		expCli:              expCli,
		realNameCli:         realNameCli,
		presentMWCli:        presentMWCli,
		officialChannelCli:  officialChannelCli,
		channelCli:          channelCli,
		probGameCli:         probGameCli,
		userProfileCli:      userProfileCli,
		chanceGameEntryCli:  chanceGameEntryCli,
		publicNoticeCli:     publicNoticeCli,
		magicSpiritProducer: magicSpiritProducer,
	}

	mgr.StartTimer()

	return mgr, nil
}

func (m *MagicSpiritMgr) ShutDown() {
    m.timeD.Stop()
	m.lotteryPond.Stop()

	close(m.shutDown)
	m.wg.Wait()

	m.apiCenterCli.Close()
	//m.seqCli.Close()
	m.pushCli.Close()
	m.accountCli.Close()
	m.channelMsgCli.Close()
	m.presentCli.Close()
	m.unifiedPayCli.Close()
	m.bc.Close()
	m.apiCenterGoCli.Close()
	m.presentMWCli.Close()
	m.officialChannelCli.Close()
	m.channelCli.Close()
	m.probGameCli.Close()
}

func checkTBeanDealToken(dealToken, payOrderId string) error {
	dtd, err := deal_token.Decode(dealToken)
	if err != nil {
		return err
	}
	if dtd.ServerName == "tbean" && dtd.OrderID == payOrderId {
		// 必须经过T豆支付才放行！！！
		return nil
	}
	if len(dtd.PrevToken) > 0 {
		return checkTBeanDealToken(dtd.PrevToken, payOrderId)
	}
	log.Errorf("dealToken校验失败, orderId:%s", payOrderId)
	return protocol.NewExactServerError(nil, status.ErrMagicSpiritNotValid, "dealToken校验失败")
}

// SendMagicWithSource 特殊来源的幸运礼物送礼
// 此流程中不进行支付和“连送”处理，支付由上游调用方处理
// 在此流程中将直接进行送礼（与SendMagicSpirit不同）
func (m *MagicSpiritMgr) SendMagicWithSource(ctx context.Context, in *pb.SendMagicWithSourceReq) (*pb.SendMagicWithSourceResp, error) {
	out := &pb.SendMagicWithSourceResp{}
	now := time.Now()
	outsideTime := time.Unix(in.GetOutsideTime(), 0)

	uid, channelId, magicSpiritId := in.GetUid(), in.GetChannelId(), in.GetMagicSpiritId()
	totalCnt := in.GetAverageCnt() * uint32(len(in.GetTargetUidList()))

	if magicSpiritId == 0 || len(in.GetTargetUidList()) == 0 || in.GetAverageCnt() == 0 ||
		in.GetPayOrderId() == "" || in.GetDealToken() == "" || in.GetSource() == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	inFusing, err := m.cache.CheckIfFusing(ctx)
	if err != nil {
		log.Errorf("SendMagicWithSource fail to CheckIfFusing. in:%+v, err:%v", in, err)
		return out, err
	}
	if inFusing {
		log.Errorf("SendMagicWithSource fail to CheckIfFusing. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrMagicSpiritNotValid)
	}

	// get magic spirit conf
	magicInfo, err := m.GetMagicSpiritById(ctx, magicSpiritId)
	if err != nil {
		log.Errorf("SendMagicWithSource fail to GetMagicSpiritById. in:%+v, err:%v", in, err)
		return out, err
	}
	if magicInfo.GetEffectBegin() > uint32(now.Unix()) || magicInfo.GetEffectEnd() < uint32(now.Unix()) {
		return out, protocol.NewExactServerError(nil, status.ErrMagicSpiritSendLimit, "该礼物还未上架哦~")
	}

	totalPrice := totalCnt * magicInfo.GetPrice()
	if totalPrice == 0 ||
		totalPrice/(magicInfo.GetPrice()*in.GetAverageCnt()) != uint32(len(in.GetTargetUidList())) {
		// 反向校验，防止白嫖
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	// 检查上游token是否经过了T豆支付！！
	err = checkTBeanDealToken(in.GetDealToken(), in.GetPayOrderId())
	if err != nil {
		log.Errorf("SendMagicWithSource fail to checkDealToken. in:%+v, err:%v", in, err)
		return out, err
	}

	channelInfo, err := m.channelCli.GetChannelSimpleInfo(ctx, in.GetUid(), in.GetChannelId())
	if err != nil {
		log.Errorf("SendMagicWithSource fail to GetChannelSimpleInfo. in:%+v, err:%v", in, err)
		return out, err
	}
	sendTime := time.Now().Unix()
	for _, targetUid := range in.GetTargetUidList() {
		if targetUid == uid {
			log.ErrorWithCtx(ctx, "SendMagicWithSource fail to SendMagicWithSource. in:%+v", in)
			return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不能送给自己哦~")
		}
		m.magicSpiritProducer.SendMsg(ctx, &kfk_magic_spirit.MagicSpiritPresentEvent{
			Uid:           in.GetUid(),
			TargetUid:     targetUid,
			OrderId:       fmt.Sprintf("origin_%s_%d", in.GetPayOrderId(), targetUid),
			ChannelId:     in.GetChannelId(),
			ChannelType:   channelInfo.GetChannelType(),
			GuildId:       channelInfo.GetBindId(),
			SendTime:      uint32(sendTime),
			MagicSpiritId: in.GetMagicSpiritId(),
			ItemCount:     in.GetAverageCnt(),
			Price:         magicInfo.GetPrice(),
			PriceType:     uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN),
			ItemSource:    in.GetSource(),
			DealToken:     in.GetDealToken(),
		})
	}

	sendInfo := &define.SendInfo{
		Uid:                in.GetUid(),
		MagicSpiritId:      in.GetMagicSpiritId(),
		TargetUidList:      in.GetTargetUidList(),
		AverageCnt:         in.GetAverageCnt(),
		ChannelId:          in.GetChannelId(),
		SeparateUnpackGift: true,
		Source:             in.GetSource(),
		DealToken:          in.GetDealToken(),
	}

	// 开奖
	pbList, unpackPbList, probTokenMap, err := m.LotteryDraw(ctx, in.GetPayOrderId(), sendInfo, magicInfo, outsideTime)
	if err != nil {
		log.Errorf("SendMagicWithSource fail to LotteryDraw. in:%+v, err:%v", in, err)
		return out, err
	}

	for _, info := range pbList {
		info.DealToken, err = GenMagicDealToken(uid, info.GetTotalPrice(), in.GetDealToken(), probTokenMap[info.GetPresentOrderId()], in.GetPayOrderId(), info.GetPresentOrderId())
		if err != nil {
			log.Errorf("SendMagicWithSource fail to GenMagicDealToken. in:%+v, err:%v", in, err)
			//return out, err
		}
	}

	// 待开箱礼物处理
	go func() {
		uCtx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		err := m.AddUnpackList(uCtx, uid, channelId, unpackPbList)
		if err != nil {
			log.Errorf("SendMagicWithSource fail to AddUnpackList. in:%+v, err:%v", in, err)
		}
	}()

	// send present
	err = m.SendPresent(ctx, in.GetPayOrderId(), in.GetOutsideTime(), magicInfo, pbList, sendInfo)
	if err != nil {
		log.Errorf("SendMagicWithSource fail to SendPresent. in:%+v, err:%v", in, err)
		return out, err
	}

	log.Debugf("SendMagicWithSource in:%+v, out:%v, cost:%v", in, out, time.Since(now))
	return out, nil
}

func (m *MagicSpiritMgr) SendPresent(ctx context.Context, magicOrderId string, outsideTime int64, magicConf *pb.MagicSpirit, list []*pb.PresentSendInfo, sendInfo *define.SendInfo) error {
	if len(list) == 0 {
		return nil
	}

	bindCid, err := m.getOfficialBindCid(ctx, sendInfo.Uid, sendInfo.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresent fail to getOfficialBindCid. orderId:%s, err:%v", magicOrderId, err)
		//return opt, err
	}

	pmReq := &pmPB.MagicSendPresentReq{
		SendUid:         sendInfo.Uid,
		AppId:           1,
		ChannelId:       sendInfo.ChannelId,
		ConsumeOrderId:  magicOrderId,
		IsBatch:         false,
		MagicSpiritId:   magicConf.GetMagicSpiritId(),
		MagicSpiritCnt:  uint32(len(sendInfo.TargetUidList)) * sendInfo.AverageCnt,
		MagicSpiritName: magicConf.GetName(),
		MagicSpiritIcon: magicConf.GetIconUrl(),
		SendTime:        uint32(outsideTime),
		BindChannelId:   bindCid,
		SysAutoSend:     true,
	}

	for _, v := range list {
		item := &pmPB.GiftItem{
			Uid:         v.GetTargetUid(),
			ItemId:      v.GetGiftId(),
			OrderId:     v.GetPresentOrderId(),
			Count:       v.GetCnt(),
			AwardEffect: v.GetAwardEffect(),
			DealToken:   v.GetDealToken(),
		}
		pmReq.GiftItemList = append(pmReq.GiftItemList, item)
	}
	log.Debugf("SendPresent MagicSendPresent req:%+v", pmReq)

	_, err = m.presentMWCli.MagicSendPresent(ctx, pmReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresent fail to MagicSendPresent. uid:%d, err:%v", sendInfo.Uid, err)
		return err
	}

	return nil
}

func (m *MagicSpiritMgr) SendMagicSpirit(ctx context.Context, in *pb.SendMagicSpiritReq) (*pb.SendMagicSpiritResp, error) {
	out := &pb.SendMagicSpiritResp{}
	now := time.Now()
	outsideTime := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), 0, time.Local)

	uid, channelId, magicSpiritId := in.GetUid(), in.GetChannelId(), in.GetMagicSpiritId()
	totalCnt := in.GetAverageCnt() * uint32(len(in.GetTargetUidList()))

	if magicSpiritId == 0 || len(in.GetTargetUidList()) == 0 || in.GetAverageCnt() == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	// get magic spirit conf
	magicInfo, err := m.GetMagicSpiritById(ctx, magicSpiritId)
	if err != nil {
		log.Errorf("SendMagicSpirit fail to GetMagicSpiritById. in:%+v, err:%v", in, err)
		return out, err
	}
	if magicInfo.GetEffectBegin() > uint32(now.Unix()) || magicInfo.GetEffectEnd() < uint32(now.Unix()) {
		return out, protocol.NewExactServerError(nil, status.ErrMagicSpiritSendLimit, "该礼物还未上架哦~")
	}

	totalPrice := totalCnt * magicInfo.GetPrice()
	if totalPrice == 0 ||
		totalPrice/(magicInfo.GetPrice()*in.GetAverageCnt()) != uint32(len(in.GetTargetUidList())) {
		// 反向校验，防止白嫖
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	sendInfo := &define.SendInfo{
		Uid:                in.GetUid(),
		MagicSpiritId:      in.GetMagicSpiritId(),
		TargetUidList:      in.GetTargetUidList(),
		AverageCnt:         in.GetAverageCnt(),
		ChannelId:          in.GetChannelId(),
		SeparateUnpackGift: in.GetSeparateUnpackGift(),
	}

	// check if can send
	err = m.CheckIfSendMagicSpirit(ctx, sendInfo, magicInfo)
	if err != nil {
		log.Errorf("SendMagicSpirit fail to CheckIfSendMagicSpirit. in:%+v, err:%v", in, err)
		return out, err
	}

	orderId := genMagicSpiritOrderId(uid, magicSpiritId)
	err = m.mysql.CreateMagicSpiritOrder(nil, &mysql.MagicSpiritOrder{
		OrderId:      orderId,
		Status:       mysql.OrderStatusPaying,
		ChannelId:    channelId,
		Uid:          uid,
		MagicId:      magicSpiritId,
		Num:          totalCnt,
		Price:        magicInfo.GetPrice(),
		OutsideTime:  outsideTime,
		CreateTime:   now,
		UpdateTime:   now,
		TBeanTimeStr: now.Format("2006-01-02 15:04:05"),
	})
	if err != nil {
		log.Errorf("SendMagicSpirit fail to CreateMagicSpiritOrder. in:%+v, err:%v", in, err)
		return out, err
	}

	// 冻结T豆
	err = m.freeze(ctx, orderId, uid, totalPrice, magicInfo, outsideTime)
	if err != nil {
		log.Errorf("SendMagicSpirit fail to freeze. in:%+v, err:%v", in, err)
		return out, err
	}

	// 开奖
	pbList, unpackPbList, probTokenMap, err := m.LotteryDraw(ctx, orderId, sendInfo, magicInfo, outsideTime)
	if err != nil {
		log.Errorf("SendMagicSpirit fail to LotteryDraw. in:%+v, err:%v", in, err)
		return out, err
	}

	for _, v := range m.bc.GetCallbackTestList() {
		if v == uid {
			log.Errorf("SendMagicSpirit 不commit，测试回调和补单，uid:%d", uid)
			return &pb.SendMagicSpiritResp{}, nil
		}
	}

	// commit
	commitCtx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	commitTime, tBeanDealToken, err := m.commit(commitCtx, orderId, uid, totalCnt, magicInfo, outsideTime)
	if err != nil {
		log.Errorf("SendMagicSpirit fail to commit. in:%+v, err:%v", in, err)
		return out, err
	}

	if tBeanDealToken == "" {
		log.Errorf("SendMagicSpirit fail to commit. in:%+v, err:tBeanDealToken is null", in)
		return out, protocol.NewExactServerError(nil, status.ErrTbeanSystemError)
	}

	channelInfo, err := m.channelCli.GetChannelSimpleInfo(ctx, in.GetUid(), in.GetChannelId())
	if err != nil {
		log.Errorf("SendMagicSpirit fail to GetChannelSimpleInfo. in:%+v, err:%v", in, err)
		return out, err
	}
	for _, targetUid := range in.GetTargetUidList() {
		m.magicSpiritProducer.SendMsg(ctx, &kfk_magic_spirit.MagicSpiritPresentEvent{
			Uid:           in.GetUid(),
			TargetUid:     targetUid,
			OrderId:       fmt.Sprintf("origin_%s_%d", orderId, targetUid),
			ChannelId:     in.GetChannelId(),
			ChannelType:   channelInfo.GetChannelType(),
			GuildId:       channelInfo.GetBindId(),
			SendTime:      uint32(now.Unix()),
			MagicSpiritId: in.GetMagicSpiritId(),
			ItemCount:     in.GetAverageCnt(),
			Price:         magicInfo.GetPrice(),
			PriceType:     uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN),
			ItemSource:    0,
			DealToken:     tBeanDealToken,
		})
	}

	for _, info := range pbList {
		info.DealToken, err = GenMagicDealToken(uid, info.GetTotalPrice(), tBeanDealToken, probTokenMap[info.GetPresentOrderId()], orderId, info.GetPresentOrderId())
		if err != nil {
			log.Errorf("SendMagicSpirit fail to GenMagicDealToken. in:%+v, err:%v", in, err)
			//return out, err
		}
	}

	out.SendInfoList = pbList
	out.SendTime = uint32(outsideTime.Unix())
	out.CombInfo = m.handleCombInfo(ctx, in, magicInfo)
	out.MagicConf = magicInfo

	// 待开箱礼物处理
	go func() {
		uCtx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		err := m.AddUnpackList(uCtx, uid, channelId, unpackPbList)
		if err != nil {
			log.Errorf("SendMagicSpirit fail to AddUnpackList. in:%+v, err:%v", in, err)
		}
	}()

	log.Debugf("SendMagicSpirit in:%+v, out:%v, cost:%v, commitTime:%v", in, out, time.Since(now), commitTime)
	return out, nil
}

func (m *MagicSpiritMgr) checkProbGameCenter(ctx context.Context, list []*probgamecenter2.CheckFuseItem) (map[string]string, error) {
	tokenMap := make(map[string]string)
	if len(list) == 0 {
		return tokenMap, nil
	}

	quickCtx, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
	defer cancel()

	appId := "magic-spirit"
	probReq := &probgamecenter2.CheckFuseReq{
		BusinessId:    appId,
		CheckItemList: list,
	}

	probResp, err := m.probGameCli.CheckFuse(quickCtx, probReq)
	if err != nil {
		if err.Code() == status.ErrRiskControlPlobGameFuse {
			// 熔断了
			log.ErrorWithCtx(ctx, "checkProbGameCenter fail to CheckFuse. %+v, err:%v", probReq, err)
			return tokenMap, protocol.NewExactServerError(nil, status.ErrMagicSpiritNotValid, "系统维护中")
		}
		log.Errorf("checkProbGameCenter fail to CheckFuse. %+v, err:%v", probReq, err)
		return tokenMap, err
	}

	for _, ret := range probResp.GetCheckResultList() {
		if ret.IsFuse {
			// 熔断了
			log.ErrorWithCtx(ctx, "checkProbGameCenter fail to CheckFuse. %+v, err:%v", probReq, err)
			return tokenMap, protocol.NewExactServerError(nil, status.ErrMagicSpiritNotValid, "系统维护中")
		}

		tokenMap[ret.GetOrderId()] = ret.GetDealToken()
	}

	return tokenMap, nil
}

func updateDealTokenOrderId(dtStr, orderId string) string {
	dt, err := deal_token.Decode(dtStr)
	if err != nil {
		return ""
	}

	dt.OrderID = orderId
	var newStr string

	if dt.PrevToken != "" {
		// 递归修改
		newPrev := updateDealTokenOrderId(dt.PrevToken, orderId)
		newStr, _ = deal_token.AddDealToken(newPrev, dt)

	} else {
		newStr, err = deal_token.Encode(dt)
		if err != nil {
			return ""
		}
	}

	return newStr
}

func GenMagicDealToken(uid, totalPrice uint32, tBeanDealToken, probGameToken, baseOrderId, orderId string) (string, error) {
	//因为有全麦的情况，所以要修改deal_token的order_id，防止主键冲突
	dtStr := updateDealTokenOrderId(tBeanDealToken, orderId)

	newDt := deal_token.NewDealTokenData(baseOrderId, orderId, "magic-spirit", int64(uid), int64(totalPrice))
	magicDealToken, err := deal_token.AddDealToken(dtStr, newDt)
	if err != nil {
		log.Errorf("GenMagicDealToken fail to AddDealToken. uid:%d, newDt:%+v, err:%v", uid, newDt, err)
		return magicDealToken, err
	}

	// 概率玩法风控中心token
	probDt, err := deal_token.Decode(probGameToken)
	if err != nil {
		return "", err
	}

	magicDealToken, err = deal_token.AddDealToken(magicDealToken, probDt)
	if err != nil {
		log.Errorf("GenMagicDealToken fail to AddDealToken. uid:%d, newDt:%+v, err:%v", uid, newDt, err)
		return magicDealToken, err
	}

	return magicDealToken, nil
}

func (m *MagicSpiritMgr) CheckIfSendMagicWithSource(ctx context.Context, in *pb.CheckIfSendMagicWithSourceReq) (bool, error) {
	magicConf, err := m.GetMagicSpiritById(ctx, in.GetMagicSpiritId())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckIfSendMagicWithSource fail to GetMagicSpiritById. in:%+v, error: %v", in, err)
		return false, err
	}

	err = m.CheckIfSendMagicSpirit(ctx, &define.SendInfo{
		Uid:                in.GetUid(),
		MagicSpiritId:      in.GetMagicSpiritId(),
		AverageCnt:         in.GetAmount(),
		ChannelId:          in.GetChannelId(),
		SeparateUnpackGift: true,
		Source:             in.GetSource(),
	}, magicConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckIfSendMagicWithSource fail to CheckIfSendMagicSpirit. in:%+v, error: %v", in, err)
		return false, err
	}

	return true, nil
}

func (m *MagicSpiritMgr) CheckIfSendMagicSpirit(ctx context.Context, sendInfo *define.SendInfo, magicInfo *pb.MagicSpirit) error {
	uid, channelId := sendInfo.Uid, sendInfo.ChannelId
	totalCnt := sendInfo.AverageCnt * uint32(len(sendInfo.TargetUidList))
	totalPrice := totalCnt * magicInfo.GetPrice()

	// check 是否熔断
	inFusing, err := m.cache.CheckIfFusing(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckIfSendMagicSpirit fail to CheckIfFusing. uid:%v, channel_id:%v, error: %v", uid, channelId, err)
		return err
	}
	if inFusing {
		log.ErrorWithCtx(ctx, "CheckIfSendMagicSpirit fail to CheckIfFusing. uid:%v, channel_id:%v, error: %v", uid, channelId, err)
		return protocol.NewExactServerError(nil, status.ErrMagicSpiritNotValid)
	}

	if !m.bc.GetOldVersionAppAccessCheck() || m.bc.CheckNewAccessCheckUidList(uid) {
		//走新鉴权逻辑
		_, magicIdAccessMap, err := m.GetAccessResult(ctx, uid, channelId, sendInfo.MagicSpiritId, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckIfSendMagicSpirit fail to GetAccessResult. uid:%v, channel_id:%v, error: %v", uid, channelId, err)
			return err
		}
		if _, ok := magicIdAccessMap[sendInfo.MagicSpiritId]; !ok {
			log.ErrorWithCtx(ctx, "CheckIfSendMagicSpirit fail to GetAccessResult. uid:%v, channel_id:%v, error: %v", uid, channelId, err)
			return protocol.NewExactServerError(nil, status.ErrMagicSpiritNotValid)
		}

	} else {

		access, err := m.getSmashAccessResult(ctx, uid, channelId, 0, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMagicSpiritUsable fail getSmashAccessResult")
			return err
		}
		if !access {
			log.ErrorWithCtx(ctx, "CheckIfSendMagicSpirit fail to getSmashAccessResult. uid:%v, channel_id:%v, error: %v", uid, channelId, err)
			return protocol.NewExactServerError(nil, status.ErrMagicSpiritNotValid)
		}
	}

	mapCommonConf, err := m.GetCommonConfWithCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckIfSendMagicSpirit fail to GetCommonConf. uid:%v, channel_id:%v, error: %v", uid, channelId, err)
		return err
	}

	// 检查单笔送礼限制
	if item, ok := mapCommonConf[uint32(pb.CommonConfType_PER_ORDER_COUNT_LIMIT)]; ok {
		err = m.checkPerSendCnt(totalCnt, item)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckIfSendMagicSpirit fail to checkPerSendCnt. uid:%v, channel_id:%v, error: %v", uid, channelId, err)
			return err
		}
	}

	// 检查每日送出总金额限制
	if item, ok := mapCommonConf[uint32(pb.CommonConfType_DAILY_SEND_MONEY_LIMIT)]; ok {
		err = m.checkDailySendPrice(ctx, uid, channelId, totalPrice, item)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckIfSendMagicSpirit fail to checkDailySendPrice. uid:%v, channel_id:%v, error: %v", uid, channelId, err)
			return err
		}
	}

	// 检查每日送给同账号送出金额限制
	if item, ok := mapCommonConf[uint32(pb.CommonConfType_DAILY_PREVENT_EXCHANGE_LIMIT)]; ok && len(sendInfo.TargetUidList) > 0 {
		err = m.checkDailySendToUidPrice(ctx, uid, channelId, sendInfo.AverageCnt*magicInfo.GetPrice(), sendInfo.TargetUidList, item)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckIfSendMagicSpirit fail to checkDailySendToUidPrice. uid:%v, channel_id:%v, error: %v", uid, channelId, err)
			return err
		}
	}

	return nil
}

func parseLimitUInt(item *mysql.MagicSpiritCommonConf) uint32 {
	if item.ValueType != CONF_VAL_TYPE_INT {
		return 0
	}

	limit, err := strconv.ParseInt(item.Value, 10, 64)
	if err != nil {
		log.Errorf("parseLimitUInt to ParseInt. confId:%v, err:%v", item.ConfId, err)
	}

	return uint32(limit)
}

func (m *MagicSpiritMgr) checkPerSendCnt(incrCnt uint32, item *mysql.MagicSpiritCommonConf) error {
	limit := parseLimitUInt(item)
	if limit == 0 {
		return nil
	}

	if incrCnt > limit {
		return protocol.NewExactServerError(nil, status.ErrMagicSpiritSendPerAmountLimit, "送太快啦~稍后再试试吧~")
	}

	return nil
}

func (m *MagicSpiritMgr) checkDailySendPrice(ctx context.Context, uid, channelId, incrCnt uint32, item *mysql.MagicSpiritCommonConf) error {
	limit := parseLimitUInt(item) * 100
	if limit == 0 {
		return nil
	}

	cnt, err := m.cache.GetSendDailyPrice(ctx, uid, uint32(time.Now().Day()))
	if err != nil {
		log.ErrorWithCtx(ctx, "checkDailySendPrice to GetSendDailyPrice. uid:%v, channelId:%v, confId:%v, err:%v", uid, channelId, item.ConfId, err)
		return err
	}

	if cnt+incrCnt > limit {
		return protocol.NewExactServerError(nil, status.ErrMagicSpiritSendLimit, "送太快啦~明天再试试吧~")
	}

	return nil
}

func (m *MagicSpiritMgr) checkDailySendToUidPrice(ctx context.Context, uid, channelId, incrPrice uint32, targetUidList []uint32, item *mysql.MagicSpiritCommonConf) error {
	limit := parseLimitUInt(item) * 100
	if limit == 0 {
		return nil
	}

	priceList, err := m.cache.GetSendToOtherDailyPriceList(ctx, uid, uint32(time.Now().Day()), targetUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkDailySendPirce to GetSendToOtherDailyPriceList. uid:%v, channelId:%v, confId:%v, err:%v", uid, channelId, item.ConfId, err)
		return err
	}

	for _, price := range priceList {
		if incrPrice+price > limit {
			return protocol.NewExactServerError(nil, status.ErrMagicSpiritSendLimit, "送太快啦~明天再试试吧~")
		}
	}

	return nil
}

func (m *MagicSpiritMgr) handleCombInfo(ctx context.Context, in *pb.SendMagicSpiritReq, magicInfo *pb.MagicSpirit) *pb.CombInfo {
	out := &pb.CombInfo{
		Duration: m.bc.GetCombMaxIntervalSec(),
	}
	incrCnt := in.GetAverageCnt() * uint32(len(in.GetTargetUidList()))
	oldCnt := in.GetCombCnt()

	serCnt, err := m.cache.GetCombCnt(ctx, in.GetChannelId(), in.GetUid(), in.GetMagicSpiritId())
	if err != nil {
		log.Errorf("getCombInfo fail to GetCombCnt. in:%+v, err:%v", in, err)
		return out
	}

	expire := time.Duration(m.bc.GetCombMaxIntervalSec()) * time.Second
	_, err = m.cache.IncrCombCnt(ctx, in.GetChannelId(), in.GetUid(), in.GetMagicSpiritId(), incrCnt, expire)
	if err != nil {
		log.Errorf("getCombInfo fail to IncrCombCnt. in:%+v, err:%v", in, err)
	}

	// 与服务器记录的数量对比一下,如果比服务器数量多以服务器记录为准
	if oldCnt > serCnt {
		oldCnt = serCnt
	}

	newCnt := incrCnt + oldCnt
	combInfo := &conf.CombConf{}

	if newCnt < m.bc.GetCombValidCnt() {
		return out
	}

	combList := m.bc.GetCombLevelConfList()
	for _, info := range combList {
		if newCnt*magicInfo.GetPrice() >= info.Score {
			combInfo = info
		}
	}

	// level up （0 -> 1 不算升级）
	if combInfo.Level > 1 && oldCnt*magicInfo.GetPrice() < combInfo.Score &&
		newCnt*magicInfo.GetPrice() >= combInfo.Score {
		out.LevelUp = true
	}

	out.CombLevel = combInfo.Level
	out.LevelName = combInfo.LevelName

	return out
}

func (m *MagicSpiritMgr) LotteryDraw(ctx context.Context, magicSpiritOrderId string, sendInfo *define.SendInfo,
	magicSpirit *pb.MagicSpirit, outsideTime time.Time) ([]*pb.PresentSendInfo, []*pb.UnpackGiftInfo, map[string]string, error) {

	opUid, magicSpiritId, avgCnt := sendInfo.Uid, sendInfo.MagicSpiritId, sendInfo.AverageCnt
	uidList := sendInfo.TargetUidList
	totalCnt := avgCnt * uint32(len(uidList))
	list := make([]*pb.PresentSendInfo, 0)
	unpackList := make([]*pb.UnpackGiftInfo, 0)
	probTokenMap := make(map[string]string)

	// 抽奖
	giftList, err := m.lotteryPond.LotteryDraw(magicSpiritId, totalCnt)
	if err != nil {
		log.Errorf("LotteryDraw fail to LotteryDraw. opUid:%v, uidList:%+v, err:%v", opUid, uidList, err)
		return list, unpackList, probTokenMap, err
	}

	if uint32(len(giftList)) != totalCnt {
		log.Errorf("LotteryDraw fail to LotteryDraw. opUid:%v, uidList:%+v, err:%v", opUid, uidList, "giftList cnt error")
		return list, unpackList, probTokenMap, protocol.NewExactServerError(nil, status.ErrMagicSpiritNotValid, "异常")
	}

	giftIdx := 0
	mapGift2Conf := make(map[uint32]*mysql.MagicSpiritPond)
	mapUid2Gift2Cnt := make(map[uint32]map[uint32]uint32)
	for _, uid := range uidList {
		for i := uint32(0); i < avgCnt; i++ {
			if _, ok := mapUid2Gift2Cnt[uid]; !ok {
				mapUid2Gift2Cnt[uid] = make(map[uint32]uint32)
			}

			giftInfo := giftList[giftIdx]
			mapGift2Conf[giftInfo.PresentId] = giftInfo
			mapUid2Gift2Cnt[uid][giftInfo.PresentId] += 1
			giftIdx++
		}
	}

	checkList := make([]*probgamecenter2.CheckFuseItem, 0)
	for uid, mapGift2Cnt := range mapUid2Gift2Cnt {
		hasMultiGift := len(mapGift2Cnt) > 1 // 此用户是否中了多种奖励

		for giftId, cnt := range mapGift2Cnt {
			giftConf, ok := mapGift2Conf[giftId]
			if !ok {
				log.Errorf("LotteryDraw fail . opUid:%v, uidList:%+v, giftId:%v, err:giftConf not found", opUid, uidList, giftId)
				continue
			}

			presentOrderId := genPresentOrderId(magicSpiritOrderId, uid, giftId, hasMultiGift)
			checkList = append(checkList, &probgamecenter2.CheckFuseItem{
				ConsumeValue: int64(cnt) * int64(magicSpirit.GetPrice()),
				PrizeValue:   int64(cnt * giftConf.Price),
				OrderId:      presentOrderId,
				Uid:          opUid,
			})

			// 需分离出开箱礼物
			if sendInfo.SeparateUnpackGift &&
				(giftConf.PrizeLevel == uint32(pb.MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV3_MORE_500) ||
					giftConf.PrizeLevel == uint32(pb.MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV4)) {

				for i := uint32(0); i < cnt; i++ {
					unpackList = append(unpackList, &pb.UnpackGiftInfo{
						MagicSpiritId: sendInfo.MagicSpiritId,
						ItemOrderId:   fmt.Sprintf("%s_%d", presentOrderId, i),
						TargetUid:     uid,
						ItemId:        giftId,
						ChannelId:     sendInfo.ChannelId,
						SendUid:       sendInfo.Uid,
						EndTs:         uint32(outsideTime.Add(time.Duration(m.bc.GetUnpackGiftDelaySec()) * time.Second).Unix()),
					})
				}

			} else {
				list = append(list, &pb.PresentSendInfo{
					PresentOrderId: presentOrderId,
					TargetUid:      uid,
					GiftId:         giftId,
					Cnt:            cnt,
					AwardEffect:    giftConf.PrizeLevel,
					TotalPrice:     cnt * giftConf.Price,
				})
			}
		}
	}

	if len(list) == 0 && len(unpackList) == 0 {
		log.Errorf("LotteryDraw fail . opUid:%v, uidList:%+v, err:giftList cnt error", opUid, uidList)
		return list, unpackList, probTokenMap, protocol.NewExactServerError(nil, status.ErrMagicSpiritNotValid, "异常")
	}

	// 概率玩法风控中心检查
	probTokenMap, err = m.checkProbGameCenter(ctx, checkList)
	if err != nil {
		log.Errorf("LotteryDraw fail to checkProbGameCenter. opUid:%v, uidList:%+v, err:%v", opUid, uidList, err)
		return list, unpackList, probTokenMap, err
	}

	err = m.recordMagicSpiritAward(ctx, magicSpiritOrderId, sendInfo, magicSpirit, outsideTime, &GiftPbList{
		SendInfoList: list, UnpackList: unpackList,
	}, mapGift2Conf)
	if err != nil {
		log.Errorf("LotteryDraw fail to recordMagicSpiritAward. opUid:%v, uidList:%+v, err:%v", opUid, uidList, err)
		return list, unpackList, probTokenMap, err
	}

	return list, unpackList, probTokenMap, nil
}

type GiftPbList struct {
	SendInfoList []*pb.PresentSendInfo
	UnpackList   []*pb.UnpackGiftInfo
}

func (m *MagicSpiritMgr) recordMagicSpiritAward(ctx context.Context, magicSpiritOrderId string, sendInfo *define.SendInfo,
	magicSpirit *pb.MagicSpirit, outsideTime time.Time, giftPbList *GiftPbList, mapGift2Conf map[uint32]*mysql.MagicSpiritPond) error {

	opUid, channelId, magicSpiritId, avgCnt := sendInfo.Uid, sendInfo.ChannelId, sendInfo.MagicSpiritId, sendInfo.AverageCnt
	list, unpackList := giftPbList.SendInfoList, giftPbList.UnpackList
	uidList := sendInfo.TargetUidList
	totalCnt := avgCnt * uint32(len(uidList))
	totalPrice := totalCnt * magicSpirit.GetPrice()
	now := time.Now()

	awardTotalPrice := uint32(0)
	mapUid2Price := make(map[uint32]uint32)
	logList := make([]*mysql.MagicSpiritAwardLog, 0, len(list)+len(unpackList))
	// 普通礼物
	for _, pbInfo := range list {
		giftConf, ok := mapGift2Conf[pbInfo.GetGiftId()]
		if !ok {
			log.Errorf("recordMagicSpiritAward magicSpiritOrderId:%v, GiftId:%v. conf not found", magicSpiritOrderId, pbInfo.GetGiftId())
			continue
		}

		mapUid2Price[pbInfo.GetTargetUid()] += pbInfo.GetCnt() * magicSpirit.GetPrice()
		awardTotalPrice += giftConf.Price * pbInfo.GetCnt()

		logList = append(logList, &mysql.MagicSpiritAwardLog{
			OrderId:         pbInfo.GetPresentOrderId(),
			ToUid:           pbInfo.GetTargetUid(),
			GiftId:          pbInfo.GetGiftId(),
			Num:             pbInfo.GetCnt(),
			MagicTotalPrice: magicSpirit.GetPrice() * pbInfo.GetCnt(),
			GiftTotalPrice:  giftConf.Price * pbInfo.GetCnt(),
			MagicOrderId:    magicSpiritOrderId,
			MagicId:         magicSpiritId,
			ChannelId:       channelId,
			FromUid:         opUid,
			OutsideTime:     outsideTime,
			AwardTime:       outsideTime,
			IsDone:          true,
			Source:          sendInfo.Source,
			TBeanTimeStr:    outsideTime.Format("2006-01-02 15:04:05"),
			TBeanDealToken:  sendInfo.DealToken,
		})
	}
	// 开箱礼物
	for _, pbInfo := range unpackList {
		giftConf, ok := mapGift2Conf[pbInfo.GetItemId()]
		if !ok {
			log.Errorf("recordMagicSpiritAward magicSpiritOrderId:%v, GiftId:%v. conf not found", magicSpiritOrderId, pbInfo.GetItemId())
			continue
		}

		num := uint32(1)
		mapUid2Price[pbInfo.GetTargetUid()] += num * magicSpirit.GetPrice()
		awardTotalPrice += num * giftConf.Price

		logList = append(logList, &mysql.MagicSpiritAwardLog{
			OrderId:         pbInfo.GetItemOrderId(),
			ToUid:           pbInfo.GetTargetUid(),
			GiftId:          pbInfo.GetItemId(),
			Num:             num,
			MagicTotalPrice: num * magicSpirit.GetPrice(),
			MagicOrderId:    magicSpiritOrderId,
			MagicId:         magicSpiritId,
			ChannelId:       channelId,
			FromUid:         opUid,
			GiftTotalPrice:  num * giftConf.Price,
			OutsideTime:     outsideTime,
			AwardTime:       time.Unix(int64(pbInfo.GetEndTs()), 0),
			Source:          sendInfo.Source,
			TBeanTimeStr:    outsideTime.Format("2006-01-02 15:04:05"),
			TBeanDealToken:  sendInfo.DealToken,
		})
	}

	currProfit, err := m.cache.IncrHourProfit(ctx, now.Hour(), int64(totalPrice)-int64(awardTotalPrice))
	if err != nil {
		log.Errorf("recordMagicSpiritAward fail to IncrHourProfit. opUid:%v, uidList:%+v, err:%v", opUid, uidList, err)
		return err

	} else if currProfit+m.bc.GetProfitFusingMin() <= 0 {
		// 亏损停服
		err = m.cache.SetFusing(ctx)
		if err != nil {
			log.Errorf("CheckProfit fail to SetFusing. err:%v", err)
			return err
		}

		_ = m.sendFeiShuMsg("停服通知", []string{now.Format("2006年01月02日 15:04:05"), "亏损已高于停服值，停服啦！！！"}, "all")
		log.ErrorWithCtx(ctx, "CheckProfit fail to SetFusing. err:%v", err)
		return protocol.NewExactServerError(nil, status.ErrMagicSpiritNotValid, "系统维护中")
	}

	err = m.mysql.Transaction(ctx, func(tx *gorm.DB) error {
		// 记录订单
		err := m.mysql.RecordMagicSpiritAwardLogs(tx, logList)
		if err != nil {
			log.Errorf("recordMagicSpiritAward fail to RecordMagicSpiritAwardLogs. opUid:%v, uidList:%+v, err:%v", opUid, uidList, err)
			return err
		}

		// 普通幸运礼物送礼流程
		if sendInfo.Source == uint32(pb.SendMagicSource_Common) {
			// 更新订单状态为已完成
			sourceStatusList := []uint32{mysql.OrderStatusHandling}
			ok, err := m.mysql.UpdateMagicSpiritOrderStatus(tx, magicSpiritOrderId, outsideTime, sourceStatusList, mysql.OrderStatusFinish)
			if err != nil {
				log.Errorf("recordMagicSpiritAward fail to UpdateRedPacketOrderStatus. uid:%v, orderId:%v, outsideTime:%v, err:%v",
					opUid, magicSpiritOrderId, outsideTime, err)
				return err
			}

			if !ok {
				err = errors.New("订单状态更新失败")
				log.Errorf("recordMagicSpiritAward fail to UpdateRedPacketOrderStatus. uid:%v, orderId:%v, outsideTime:%v, err:%v",
					opUid, magicSpiritOrderId, outsideTime, err)
				return err
			}
		} else {
			// 特殊来源的送礼，在此处事务中落库，状态直接置为完成
			err = m.mysql.CreateMagicSpiritOrder(tx, &mysql.MagicSpiritOrder{
				OrderId:      magicSpiritOrderId,
				Status:       mysql.OrderStatusFinish,
				ChannelId:    channelId,
				Uid:          opUid,
				MagicId:      magicSpiritId,
				Num:          totalCnt,
				Price:        magicSpirit.GetPrice(),
				OutsideTime:  outsideTime,
				CreateTime:   now,
				UpdateTime:   now,
				TBeanTimeStr: outsideTime.Format("2006-01-02 15:04:05"),
				Source:       sendInfo.Source,
			})
			if err != nil {
				log.Errorf("recordMagicSpiritAward fail to CreateMagicSpiritOrder. uid:%v, orderId:%v, outsideTime:%v, err:%v",
					opUid, magicSpiritOrderId, outsideTime, err)
				return err
			}

			lastMonthTime := getLastMonthTime(outsideTime)
			// 查上月
			_, exist, err := m.mysql.GetMagicSpiritOrder(tx, magicSpiritOrderId, lastMonthTime)
			if err != nil {
				log.Errorf("recordMagicSpiritAward fail to GetMagicSpiritOrder. uid:%v, orderId:%v, outsideTime:%v, err:%v",
					opUid, magicSpiritOrderId, outsideTime, err)
				return err
			}
			if exist {
				// 订单已存在
				return protocol.NewExactServerError(nil, status.ErrMagicSpiritOrderExist)
			}
		}

		return nil
	})
	if err != nil {
		log.Errorf("recordMagicSpiritAward fail to Transaction. opUid:%v, uidList:%+v, err:%v", opUid, uidList, err)
		return err
	}

	//go func() {
	//	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	//	defer cancel()

	m.addLimitCnt(ctx, opUid, magicSpiritId, uint32(now.Day()), totalPrice, mapUid2Price)
	//}()

	return nil
}

func (m *MagicSpiritMgr) addLimitCnt(ctx context.Context, uid, magicId, day, totalPrice uint32, mapUid2Price map[uint32]uint32) {
	_, err := m.cache.IncrSendDailyPrice(ctx, uid, day, totalPrice, 24*time.Hour)
	if err != nil {
		log.Errorf("addLimitCnt fail to IncrSendDailyCnt. opUid:%v,  err:%v", uid, err)
	}

	err = m.cache.IncrSendToOtherDailyPrice(ctx, uid, day, mapUid2Price, 24*time.Hour)
	if err != nil {
		log.Errorf("addLimitCnt fail to IncrSendToOtherDailyPrice. opUid:%v, err:%v", uid, err)
	}

	_ = m.cache.SetUserSendFlagWithExpire(ctx, uid, magicId, time.Duration(m.bc.GetUserInvestFlagExpireDay()*24*3600)*time.Second)
}

func genMagicSpiritOrderId(uid, magicSpiritId uint32) string {
	return fmt.Sprintf("magic_%d_%d_%v", uid, magicSpiritId, time.Now().UnixNano())
}

func genPresentOrderId(magicSpiritOrderId string, targetUid, giftId uint32, hasMultiGift bool) string {
	if hasMultiGift {
		return fmt.Sprintf("%s_%d_%d", magicSpiritOrderId, targetUid, giftId)
	}

	return fmt.Sprintf("%s_%d", magicSpiritOrderId, targetUid)
}

func (m *MagicSpiritMgr) GetMagicOrderTotal(_ context.Context, beginTime, endTime time.Time, source uint32) (orderCnt uint32, totalPrice, totalNum uint64, err error) {
	timeList := make([]time.Time, 0)
	if beginTime.After(endTime) || beginTime.AddDate(0, 0, 7).Before(endTime) {
		return 0, 0, 0, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	timeList = append(timeList, beginTime)
	if beginTime.Month() != endTime.Month() {
		timeList = append(timeList, endTime)
	}

	// 月初半小时内(与T豆系统有时间差，多查一个月表)
	if beginTime.Add(-30*time.Minute).Month() != beginTime.Month() {
		timeList = append(timeList, beginTime.Add(-30*time.Minute))
	}

	for _, t := range timeList {
		info, err := m.mysql.GetOrderTotalInfo(t, beginTime, endTime, mysql.OrderStatusFinish, source)
		if err != nil {
			log.Errorf("GetMagicOrderTotal GetOrderTotalInfo fail. beginTime:%v, endTime:%v err:%v", beginTime, endTime, err)
			return 0, 0, 0, err
		}

		orderCnt += info.OrderCnt
		totalPrice += info.TotalPrice
		totalNum += info.TotalNum
	}

	return
}

func (m *MagicSpiritMgr) GetMagicAwardTotal(_ context.Context, beginTime, endTime time.Time) (orderCnt uint32, totalPrice, totalNum uint64, err error) {
	timeList := make([]time.Time, 0)
	if beginTime.After(endTime) || beginTime.AddDate(0, 0, 7).Before(endTime) {
		return 0, 0, 0, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	timeList = append(timeList, beginTime)
	if beginTime.Month() != endTime.Month() {
		timeList = append(timeList, endTime)
	}

	// 月初半小时内(与T豆系统有时间差，多查一个月表)
	if beginTime.Add(-30*time.Minute).Month() != beginTime.Month() {
		timeList = append(timeList, beginTime.Add(-30*time.Minute))
	}

	for _, t := range timeList {
		info, err := m.mysql.GetAwardTotalInfo(t, beginTime, endTime)
		if err != nil {
			log.Errorf("GetMagicAwardTotal GetAwardTotalInfo fail. beginTime:%v, endTime:%v err:%v", beginTime, endTime, err)
			return 0, 0, 0, err
		}

		orderCnt += info.OrderCnt
		totalPrice += info.TotalPrice
		totalNum += info.TotalNum
	}

	return
}

func (m *MagicSpiritMgr) GetMagicAwardOrderIdList(_ context.Context, beginTime, endTime time.Time) ([]string, error) {
	list := make([]string, 0)
	timeList := make([]time.Time, 0)
	if beginTime.After(endTime) || beginTime.AddDate(0, 0, 7).Before(endTime) {
		return list, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	timeList = append(timeList, beginTime)
	if beginTime.Month() != endTime.Month() {
		timeList = append(timeList, endTime)
	}

	// 月初半小时内(与T豆系统有时间差，多查一个月表)
	if beginTime.Add(-30*time.Minute).Month() != beginTime.Month() {
		timeList = append(timeList, beginTime.Add(-30*time.Minute))
	}

	for _, t := range timeList {
		tmpList, err := m.mysql.GetMagicAwardOrderIdList(t, beginTime, endTime)
		if err != nil {
			log.Errorf("GetMagicAwardOrderIdList GetMagicAwardOrderIdList fail. beginTime:%v, endTime:%v err:%v", beginTime, endTime, err)
			return list, err
		}

		list = append(list, tmpList...)
	}

	return list, nil
}

func (m *MagicSpiritMgr) GetMagicConsumeOrderIdList(_ context.Context, beginTime, endTime time.Time, source uint32) ([]string, error) {
	list := make([]string, 0)
	timeList := make([]time.Time, 0)
	if beginTime.After(endTime) || beginTime.AddDate(0, 0, 7).Before(endTime) {
		return list, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	timeList = append(timeList, beginTime)
	if beginTime.Month() != endTime.Month() {
		timeList = append(timeList, endTime)
	}

	// 月初半小时内(与T豆系统有时间差，多查一个月表)
	if beginTime.Add(-30*time.Minute).Month() != beginTime.Month() {
		timeList = append(timeList, beginTime.Add(-30*time.Minute))
	}

	for _, t := range timeList {
		tmpList, err := m.mysql.GetMagicConsumeOrderIdList(t, beginTime, endTime, mysql.OrderStatusFinish, source)
		if err != nil {
			log.Errorf("GetMagicConsumeOrderIdList GetMagicConsumeOrderIdList fail. beginTime:%v, endTime:%v err:%v", beginTime, endTime, err)
			return list, err
		}

		list = append(list, tmpList...)
	}

	return list, nil
}

func (m *MagicSpiritMgr) getUserExemptCondValAll(ctx context.Context, uid uint32) (bool, error) {
	sendFlag, err := m.cache.GetUserSendFlag(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserExemptCondVal GetUserSendFlag, uid:%d,err:%v", uid, err)
		return false, err
	}

	return sendFlag, nil
}

func (m *MagicSpiritMgr) GetUserExemptCondVal(ctx context.Context, uid uint32, magicIds ...uint32) (map[uint32]bool, error) {
	sendFlagMap, err := m.cache.BatGetUserSendFlag(ctx, uid, magicIds...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserExemptCondVal GetUserSendFlag, uid:%d,err:%v", uid, err)
		return sendFlagMap, err
	}

	return sendFlagMap, nil
}

func (m *MagicSpiritMgr) GenFinancialFile(ctx context.Context, req *reconcile_v2.GenFinancialFileReq) error {
	beginTime := time.Unix(req.GetBeginTime(), 0)
	endTime := time.Unix(req.GetEndTime(), 0)

	if beginTime.After(endTime) || beginTime.AddDate(0, 0, 7).Before(endTime) {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	list, err := m.mysql.GetReconcileSumStats(beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenFinancialFile fail to GetReconcileSumStats, req:%+v, err:%v", req, err)
		return err
	}

	totalBuyPrice := uint64(0)
	for _, info := range list {
		if info.Source > 0 {
			continue
		}
		totalBuyPrice += info.BuyPriceTBeanTime
	}

	if totalBuyPrice != uint64(req.GetTbeanPrice()) {
		log.ErrorWithCtx(ctx, "【昨日对账失败】幸运礼物-T豆，时间：%v-%v, 总价值：%d-%d", beginTime, endTime, totalBuyPrice, req.GetTbeanPrice())
		return nil
	}

	err = m.mysql.RecordReconcileDataLogs(list)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenFinancialFile fail to RecordReconcileDataLogs, req:%+v, err:%v", req, err)
		return err
	}

	log.InfoWithCtx(ctx, "【昨日对账】幸运礼物-T豆，时间：%v-%v, 总价值：%d-%d", beginTime, endTime, totalBuyPrice, req.GetTbeanPrice())
	return nil
}
