// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/magic-spirit/internal/event/producer (interfaces: IMagicSpiritEventProducer)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	kfk_magic_spirit "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_magic_spirit"
)

// MockMagicSpiritEventProducer is a mock of IMagicSpiritEventProducer interface.
type MockMagicSpiritEventProducer struct {
	ctrl     *gomock.Controller
	recorder *MockMagicSpiritEventProducerMockRecorder
}

// MockMagicSpiritEventProducerMockRecorder is the mock recorder for MockMagicSpiritEventProducer.
type MockMagicSpiritEventProducerMockRecorder struct {
	mock *MockMagicSpiritEventProducer
}

// NewMockMagicSpiritEventProducer creates a new mock instance.
func NewMockMagicSpiritEventProducer(ctrl *gomock.Controller) *MockMagicSpiritEventProducer {
	mock := &MockMagicSpiritEventProducer{ctrl: ctrl}
	mock.recorder = &MockMagicSpiritEventProducerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMagicSpiritEventProducer) EXPECT() *MockMagicSpiritEventProducerMockRecorder {
	return m.recorder
}

// SendMsg mocks base method.
func (m *MockMagicSpiritEventProducer) SendMsg(arg0 context.Context, arg1 *kfk_magic_spirit.MagicSpiritPresentEvent) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendMsg", arg0, arg1)
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockMagicSpiritEventProducerMockRecorder) SendMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockMagicSpiritEventProducer)(nil).SendMsg), arg0, arg1)
}
