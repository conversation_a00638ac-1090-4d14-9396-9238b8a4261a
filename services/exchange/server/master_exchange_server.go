/*
 * 真实会长提现
 */
package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	exchangePB "golang.52tt.com/protocol/services/exchange"
	"golang.52tt.com/services/exchange/cache"
	"golang.52tt.com/services/exchange/comm"
	"golang.52tt.com/services/exchange/model"
)

//会长查看提现记录 ok
func (s *guildExchangeServer) GetExchangeHis(ctx context.Context, req *exchangePB.GetExchangeListReq) (*exchangePB.GetExchangeListRes, error) {
	xGuildHis := model.ExchangeGuildHisDB{Db: s.db}
	//count := xGuildHis.GetHisCount(req.Uid)
	list := []model.ExchangeGuildHis{}
	err := xGuildHis.GetHisList(req.Uid, req.Offset, req.Limit, &list)
	if err != nil {
		return nil, protocol.NewServerError(status.ErrExchangeDbErr, "")
	}
	res := &exchangePB.GetExchangeListRes{}

	for _, elem := range list {
		his := &exchangePB.ExchangeGuildData{
			GuildOrderId:  elem.GuildOrderId,
			MasterUid:     elem.MasterUid,
			Score:         elem.Score,
			AnchorScore:   elem.AnchorScore,
			MaskedPkScore: elem.MaskedPkScore,
			KnightScore:   elem.KnightScore,
			EsportScore:   elem.EsportScore,
			UserId:        elem.MasterUid,
			Status:        uint32(elem.Status),
			CreateAt:      elem.CreateAt,
			UpdateAt:      elem.UpdateAt,
		}
		res.ExchangeGuildHis = append(res.ExchangeGuildHis, his)
	}

	return res, nil
}

//查看提现记录的主播贡献详情 ok
func (s *guildExchangeServer) GetExchangeDetail(c context.Context, req *exchangePB.GetExchangeDetailReq) (*exchangePB.GetExchangeListRes, error) {
	res := &exchangePB.GetExchangeListRes{}
	db := model.ExchangeGuildTransactionsDB{Db: s.db}
	transactions := []model.ExchangeGuildTransactions{}
	err := db.GetDetailList(req.GuildOrderId, req.MasterUid, req.Offset, req.Limit, &transactions)
	if err != nil {
		return res, err
	}
	uidList := make([]uint32, 0)
	for _, elem := range transactions {
		temp := exchangePB.ExchangeGuildData{
			GuildOrderId:  elem.GuildOrderId,
			MasterUid:     elem.MasterUid,
			UserId:        elem.UserId,
			GuildId:       elem.GuildId,
			Score:         elem.Score,
			AnchorScore:   elem.AnchorScore,
			MaskedPkScore: elem.MaskedPkScore,
			KnightScore:   elem.KnightScore,
			CreateAt:      elem.CreateAt,
			UpdateAt:      elem.UpdateAt,
			Status:        uint32(elem.Status),
			Reason:        uint32(elem.Reason),
			EsportScore:   elem.EsportScore,
		}
		uidList = append(uidList, elem.UserId)
		res.ExchangeGuildHis = append(res.ExchangeGuildHis, &temp)
	}

	accountMap, err := comm.BatchGetAccountData(uidList)
	if err != nil {
		return res, err
	}

	for i := 0; i < len(res.ExchangeGuildHis); i++ {
		uid := res.ExchangeGuildHis[i].UserId
		elem, ok := accountMap[uid]
		if !ok {
			continue
		}
		res.ExchangeGuildHis[i].Ttid = elem.Alias
		res.ExchangeGuildHis[i].Nickname = elem.Nickname
		res.ExchangeGuildHis[i].Username = elem.Username
	}

	return res, nil
}

//会长查看可提现金额总数和列表	ok
func (s *guildExchangeServer) GetExchangeAmount(ctx context.Context, req *exchangePB.UidReq) (res *exchangePB.UserAllScoreResp, err error) {
	res = &exchangePB.UserAllScoreResp{
		Amount:    &exchangePB.AllScore{},
		UndoAmount: &exchangePB.AllScore{},
	}

	ts := model.ExchangeGuildTSDB{Db: s.db}
	tsScore, tsAnchorScore, tsPkScore, tsKnightScore, tsEsportScore := ts.GetSum(req.Uid)
	res.Amount.Score = tsScore
	res.Amount.AnchorScore = tsAnchorScore
	res.Amount.MaskedPkScore = tsPkScore
	res.Amount.KnightScore = tsKnightScore
	res.Amount.EsportScore = tsEsportScore
	res.InStatistics = false

	xgtDb := model.ExchangeGuildTransactionsDB{Db: s.db}
	undoCount := xgtDb.GetMasterUndoneCount(req.Uid)
	autoC := cache.NewAutoExchangeCache(s.rc)
	if undoCount > 0  || autoC.GetBusy() == 1{	//统计中
		res.InStatistics = true
	}
	//c := cache.NewScoreSumCache(s.rc)
	undoDb := model.ExchangeGuildUndoAmountDB{Db: s.db}
	score, err := undoDb.GetOriginTotal(req.GetUid(), uint32(exchangePB.ExchangeType_PRESENT))
	if err != nil {
		log.ErrorWithCtx(ctx, "undoDb.GetOriginTotal err=%v", err)
		return res, err
	}
	res.UndoAmount.Score = score
	anchorScore, err := undoDb.GetOriginTotal(req.GetUid(), uint32(exchangePB.ExchangeType_ANCHOR))
	if err != nil {
		log.ErrorWithCtx(ctx, "undoDb.GetOriginTotal err=%v", err)
		return res, err
	}
	res.UndoAmount.AnchorScore = anchorScore
	maskedPkScore, err := undoDb.GetOriginTotal(req.GetUid(), uint32(exchangePB.ExchangeType_PK))
	if err != nil {
		log.ErrorWithCtx(ctx, "undoDb.GetOriginTotal err=%v", err)
		return res, err
	}
	res.UndoAmount.MaskedPkScore = maskedPkScore
	knightScore, err := undoDb.GetOriginTotal(req.GetUid(), uint32(exchangePB.ExchangeType_KNIGHT))
	if err != nil {
		log.ErrorWithCtx(ctx, "undoDb.GetOriginTotal err=%v", err)
		return res, err
	}
	res.UndoAmount.KnightScore = knightScore
	esportScore, err := undoDb.GetOriginTotal(req.GetUid(), uint32(exchangePB.ExchangeType_ESPORT))
	if err != nil {
		log.ErrorWithCtx(ctx, "undoDb.GetOriginTotal err=%v", err)
		return res, err
	}
	res.UndoAmount.EsportScore = esportScore

	mainDb := model.ExchangeGuildMainDB{Db: s.db}
	mainDb.GetOne(req.Uid)
	res.IsMaster = false
	if mainDb.MasterUid != 0 {
		res.IsMaster = true
	}

	return
}

func (s *guildExchangeServer) GetExchangeAmountList(ctx context.Context, req *exchangePB.UidOffsetReq) (res *exchangePB.UserAllScoreList, err error) {
	res = &exchangePB.UserAllScoreList{}
	uidList := make([]uint32, 0)

	tsdb := model.ExchangeGuildTSDB{Db: s.db}
	tslist := tsdb.GetGuildAllScoreListOffset(req.Uid, req.Offset, req.Limit)

	for _, elem := range *tslist {
		uid := elem.UserId

		userAllScore := exchangePB.UserAllScore{
			AllScore: &exchangePB.AllScore{},
		}
		userAllScore.Uid = uid
		userAllScore.AllScore.Score = elem.Score
		userAllScore.AllScore.AnchorScore = elem.AnchorScore
		userAllScore.AllScore.MaskedPkScore = elem.MaskedPkScore
		userAllScore.AllScore.KnightScore = elem.KnightScore
		userAllScore.AllScore.EsportScore = elem.EsportScore
		res.UserAllScoreList = append(res.UserAllScoreList, &userAllScore)
		uidList = append(uidList, uid)
	}

	var accountMap map[uint32]*accountPB.UserResp
	accountMap, err = comm.BatchGetAccountData(uidList)
	if err != nil {
		return
	}

	for i := 0; i < len(res.UserAllScoreList); i++ {
		elem, ok := accountMap[res.UserAllScoreList[i].Uid]
		if !ok {
			continue
		}
		res.UserAllScoreList[i].Ttid = elem.Alias
		res.UserAllScoreList[i].Nickname = elem.Nickname
		res.UserAllScoreList[i].Username = elem.Username
	}

	return
}

//会长提现v2版本
func (s *guildExchangeServer) ExchangeV2(ctx context.Context, req *exchangePB.GuildExchangeTypeReq) (res *exchangePB.AllScore, err error) {
	res = &exchangePB.AllScore{}
	masterUid := req.Uid

	mainDb := model.ExchangeGuildMainDB{Db: s.db}
	mainDb.GetOne(masterUid)
	if mainDb.MasterUid == 0 {
		err = protocol.NewServerError(status.ErrExchangeNotMaster)
		return
	}

	//转移到会长后台提现
	err = protocol.NewServerError(status.ErrExchangeTransfer)
	return


	//tNow := time.Now()
	//
	//if tNow.Hour() < 6 || tNow.Hour() > 23 {
	//	err = protocol.NewServerError(status.ErrExchangeTimeErr)
	//	return
	//}
	//
	//isEveryDay := conf.UidInEveryDay(masterUid)
	//
	//if !comm.IsExchangeDate(req.Type) && !isEveryDay {
	//	err = protocol.NewServerError(status.ErrExchangeTimeErr)
	//	return
	//}
	//
	//var guildOrderId string
	//xType := exchangePB.ExchangeType(req.Type)
	//switch xType {
	//case exchangePB.ExchangeType_COMM_ANCHOR:
	//	err = comm.CheckEncashBlockCA(context.Background(), masterUid)
	//	if err != nil {
	//		if e, ok := err.(commission.APIError); ok && e.Code() == commission.CodeErrorBindCard {
	//			err = protocol.NewServerError(status.ErrCommissionUserBankInfoError)
	//			return
	//		}
	//		return
	//	}
	//	if isEveryDay {
	//		guildOrderId = fmt.Sprintf("CA_%d_%s", masterUid, tNow.Format("********"))
	//	} else {
	//		year, week := tNow.ISOWeek()
	//		guildOrderId = fmt.Sprintf("CA_%d_%d_%d", masterUid, year, week)
	//	}
	//case exchangePB.ExchangeType_PK:
	//	err = comm.CheckEncashBlockPK(context.Background(), masterUid)
	//	if err != nil {
	//		if e, ok := err.(commission.APIError); ok && e.Code() == commission.CodeErrorBindCard {
	//			err = protocol.NewServerError(status.ErrCommissionUserBankInfoError)
	//			return
	//		}
	//		return
	//	}
	//	if isEveryDay {
	//		guildOrderId = fmt.Sprintf("PK_%d_%s", masterUid, tNow.Format("********"))
	//	} else {
	//		guildOrderId = fmt.Sprintf("PK_%d_%s", masterUid, tNow.Format("200601"))
	//	}
	//default:
	//	err = protocol.NewServerError(status.ErrExchangeTypeNotSupport)
	//	return
	//}
	//
	//xHis := model.ExchangeGuildHisDB{Db: s.db}
	//xHis.GetData(guildOrderId, masterUid)
	//if xHis.Status == model.X_GUILD_HIS_STATUS_FINISH {
	//	log.Debugln("guild his finished")
	//	err = protocol.NewServerError(status.ErrExchangeOrderFinished)
	//	return
	//}
	//
	//guildTSDB := model.ExchangeGuildTSDB{Db: s.db}
	//tsScore, tsAnchorScore, tsPkScore := guildTSDB.GetSum(req.Uid)
	//
	//var scoreSum uint64
	//if xType == exchangePB.ExchangeType_COMM_ANCHOR {
	//	scoreSum = tsScore + tsAnchorScore
	//} else {
	//	scoreSum = tsPkScore
	//}
	//if scoreSum < GUILD_EXCHANGE_AMOUNT_MIN {
	//	err = protocol.NewServerError(status.ErrExchangeMinScore, fmt.Sprintf("不满足最小积分数%d", GUILD_EXCHANGE_AMOUNT_MIN))
	//	return
	//}
	//
	//locker := cache.NewLocker(s.rc, s.rb)
	//lockValue := locker.GenerateValue()
	//if !locker.LockMasterExchange(masterUid, lockValue) {
	//	err = protocol.NewServerError(status.ErrExchangeIng)
	//	log.Errorln("lock exchange")
	//	return
	//}
	//defer locker.UnlockMasterExchange(masterUid, lockValue)
	//
	//
	//if xHis.GuildOrderId == "" { //完全没有数据
	//	//提现仓库的
	//	tsList := []model.ExchangeGuildTS{}
	//	err = guildTSDB.GetGuildAllScoreList(masterUid, &tsList)
	//	if err != nil {
	//		log.Errorln("ExchangeGuildTSDB GetGuildStatusAllList", err)
	//		err = protocol.NewServerError(status.ErrExchangeDbErr)
	//		return
	//	}
	//
	//	//确保 tsList 里的数据都是积分扣成功的
	//	xDB := model.ExchangeGuildTransactionsDB{Db: s.db}
	//	undoneCount := xDB.GetMasterUndoneCount(masterUid)
	//	if undoneCount > 0 {
	//		err = protocol.NewServerError(status.ErrExchangeUndoneTransaction)
	//		return
	//	}
	//
	//	tx := s.db.Begin()
	//	defer tx.Rollback()
	//	xHisNew := model.ExchangeGuildHisDB{Db: tx}
	//	xHisNew.GuildOrderId = guildOrderId
	//	xHisNew.MasterUid = masterUid
	//	xHisNew.XType = uint8(xType)
	//	xHisNew.CreateAt = uint32(tNow.Unix())
	//	err = xHisNew.Insert()
	//	if err != nil {
	//		log.Errorln(err)
	//		err = protocol.NewServerError(status.ErrExchangeDbErr)
	//		return
	//	}
	//
	//	for _, elem := range tsList {
	//		//都在事务里，失败了，可以让会长下次再提
	//		if xType == exchangePB.ExchangeType_COMM_ANCHOR {
	//			elem.MaskedPkScore = 0
	//		} else {
	//			elem.Score = 0
	//			elem.AnchorScore = 0
	//		}
	//
	//		err = xHisNew.AddScores(guildOrderId, masterUid, elem.Score, elem.AnchorScore, elem.MaskedPkScore)
	//		if err != nil {
	//			log.Errorln("ExchangeGuildHisDB AddScores", err)
	//			if err != nil {
	//				log.Errorln(err)
	//			}
	//			return
	//		}
	//
	//		guildTSDBt := model.ExchangeGuildTSDB{Db: tx}
	//		err = guildTSDBt.Reduce(masterUid, elem.UserId, elem.Score, elem.AnchorScore, elem.MaskedPkScore)
	//		if err != nil {
	//			log.Errorln("ExchangeGuildTSDB Reduce", err)
	//			err = protocol.NewServerError(status.ErrExchangeDbErr)
	//			return
	//		}
	//
	//		xgtDb := model.ExchangeGuildTransactionsDB{Db: tx}
	//		xgtDb.GuildOrderId = guildOrderId
	//		xgtDb.MasterUid = masterUid
	//		xgtDb.UserId = elem.UserId
	//		xgtDb.Source = model.X_SOURCE_TS_EXCHANGE
	//		xgtDb.GuildId = elem.GuildId
	//		xgtDb.Score = elem.Score
	//		xgtDb.AnchorScore = elem.AnchorScore
	//		xgtDb.MaskedPkScore = elem.MaskedPkScore
	//		xgtDb.Status = model.X_GUILD_X_STATUS_DONE
	//		xgtDb.Reason = model.X_GUILD_X_REASON_EXCHANGE
	//		err = xgtDb.Insert()
	//		if err != nil {
	//			log.Errorln("ExchangeGuildTransactionsDB Insert", err)
	//			err = protocol.NewServerError(status.ErrExchangeDbErr)
	//			return
	//		}
	//	}
	//
	//	tx.Commit()
	//}
	//
	//xHis2 := model.ExchangeGuildHisDB{Db: s.db}
	//xHis2.GetData(guildOrderId, masterUid)
	//
	//seDate := tNow.Format("2006-01-02")
	//if xHis2.XType == uint8(exchangePB.ExchangeType_COMM_ANCHOR) {
	//	err = comm.SettlementAndEncashmentCA(ctx, masterUid, xHis2.Score+xHis2.AnchorScore+xHis2.MaskedPkScore, seDate)
	//} else {
	//	err = comm.SettlementAndEncashmentPK(ctx, masterUid, xHis2.Score+xHis2.AnchorScore+xHis2.MaskedPkScore, seDate)
	//}
	//
	//if err != nil {
	//	apiError, ok := err.(commission.APIError)
	//	if ok {
	//		if apiError.Code() == commission.CodeDataExisted {
	//			log.Warnln("SettlementAndEncashment CodeDataExisted ", req.Uid, guildOrderId, seDate)
	//		} else {
	//			err = protocol.NewServerError(status.ErrNetservice, apiError.Message())
	//			warningText := fmt.Sprintf("佣金平台报错 guild_order_id=%s, master_uid=%d, date=%s, err=%+v", guildOrderId, req.Uid, seDate, apiError)
	//			comm.SendWarning(warningText)
	//			log.Errorln("SettlementAndEncashment", err)
	//			return
	//		}
	//
	//	} else {
	//		err = protocol.NewServerError(status.ErrNetservice, err.Error())
	//		log.Errorln("SettlementAndEncashment", err)
	//		return
	//	}
	//}
	//
	//err = xHis2.UpdateStatusWithExchangeAt(guildOrderId, req.Uid, model.X_GUILD_HIS_STATUS_FINISH)
	//if err != nil {
	//	log.Errorln("ExchangeGuildHisDB UpdateStatus", err)
	//	err = protocol.NewServerError(status.ErrExchangeDbErr)
	//	return
	//}
	//
	//res.Score = xHis2.Score
	//res.AnchorScore = xHis2.AnchorScore
	//res.MaskedPkScore = xHis2.MaskedPkScore
	//
	//
	//infoText := fmt.Sprintf("会长提现成功 guild_order_id=%s, master_uid=%d, xtype=%d, score=%d, anchor_score=%d, pk_score=%d",
	//	xHis2.GuildOrderId, xHis2.MasterUid, xHis2.XType, xHis2.Score, xHis2.AnchorScore, xHis2.MaskedPkScore)
	//comm.SendInfo(infoText)
	//
	//return
}

