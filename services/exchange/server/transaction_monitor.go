package server

import (
	"fmt"
	user_risk_api "golang.52tt.com/clients/user-risk-api"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/services/exchange/cache"
	"golang.52tt.com/services/exchange/comm"
	"golang.52tt.com/services/exchange/model"
	"sync"
	"time"

	"golang.org/x/net/context"

	pb "golang.52tt.com/protocol/services/exchange"
)

const (
	retryInterval = 30 * time.Second
)

func (s *exchangeServer) extendTransactionMonitorLock(ctx context.Context) {
	ticker := time.NewTicker(time.Second * 30)
	locker := cache.NewLocker(s.rc, s.rb)
	for {
		select {
		case <-ticker.C:
			locker.ExtendMonitorLock(time.Minute)
		case <-ctx.Done():
			return
		}
	}
}

//重试没有成功的订单
func (s *exchangeServer) transactionMonitor() {
	time.Sleep(time.Second * 3) //防止客户端没有初始化完成就跑任务
	store := &mysqlStore{db: s.db}
	for {
		const maxConcurrency = 50

		log.Debugf("getting retry tasks")
		txList, err := store.getTxList([]transactionStatus{statusInit, statusPending}, maxConcurrency)
		if err != nil {
			log.Errorf("failed go get tx list: %v", err)
			time.Sleep(retryInterval)
			continue
		}

		if len(txList) == 0 {
			log.Warnf("no retry task")
			time.Sleep(retryInterval)
			continue
		}
		log.Infof("%d retry tasks got from database", len(txList))

		func() {
			locker := cache.NewLocker(s.rc, s.rb)
			lockV := locker.GenerateValue()
			if !locker.LockMonitor(lockV) {
				log.Warnln("transactionMonitor locked")
				return
			}
			defer locker.UnlockMonitor(lockV)
			cancel, cancelFunc := context.WithCancel(context.Background())
			go s.extendTransactionMonitorLock(cancel)
			defer cancelFunc()

			wg := &sync.WaitGroup{}
			for _, retryTx := range txList {
				wg.Add(1)
				func(tx *exchangeTransaction) {
					//ctx2, cancel := context.WithTimeout(log.WithLogTags(ctx, "monitor", fmt.Sprintf("Txn_%d", tx.ID)), time.Minute)
					ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
					defer func() {
						cancel()
						wg.Done()
					}()

					if tx.Status == uint32(statusInit) && time.Since(tx.CreateAt) < time.Second*30 {
						// create in last 30 seconds, do not process this transaction
						log.Warnf("task was created in 30 seconds and init status, do not process")
						return
					}

					log.Debugf("retrying %+v", tx)

					if time.Since(tx.CreateAt) > time.Hour*24 {
						warningText := fmt.Sprintf("重试个人兑换 order_id=%s, target_type=%d, uid=%d, date=%s",
							tx.OrderID, tx.TargetType, tx.UserID, tx.CreateAt.Format("2006-01-02 15:04:05"))
						comm.SendWarning(warningText)
					}

					if tx.TargetType == uint32(pb.CurrencyType_COMMISSION) {

						//如果是提现，那么先判断本月是否有额度
						req := &pb.AddPrivateWithdrawLogReq{
							OrderId:    tx.OrderID,
							Uid:        tx.UserID,
							Amount:     uint64(tx.TargetAmount),
							CreateTime: uint32(tx.CreateAt.Unix()),
						}
						_, err = s.AddPrivateWithdrawLog(ctx, req)
						if err != nil {
							serverError := protocol.ToServerError(err)
							if serverError.Code() == status.ErrExchangeWithdrawMonthMax {
								store.deleteTx(tx.ID)
							}
							if serverError.Code() == status.ErrExchangeNotRealName {
								store.deleteTx(tx.ID)
							}
							return
						}
					}

					d, ok := delegates[pb.CurrencyType(tx.SourceType)]
					if !ok {
						// no delegate for this source type?
						log.Errorf("no delegate for source type: %v", tx.SourceType)
						return
					}

					xt := &model.ExchangeTransaction{
						ID:            tx.ID,
						UserID:        tx.UserID,
						OrderID:       tx.OrderID,
						TargetAmount:  tx.TargetAmount,
						TargetType:    tx.TargetType,
						TargetBalance: tx.TargetBalance,
						SourceAmount:  tx.SourceAmount,
						SourceType:    tx.SourceType,
						CreateAt:      tx.CreateAt,
						Status:        tx.Status,
						UpdateAt:      *tx.UpdateAt,
						UserData:      string(tx.UserData),
						BonusAmount:   tx.BonusAmount,
						OrderDesc:     tx.OrderDesc,
						FinishMessage: tx.FinishMessage,
					}
					_, _, targetBalance, err := d.processTx(ctx, xt)
					tx.TargetBalance = targetBalance
					if err != nil {
						log.Errorf("process transaction %s err: %v", tx.OrderID, err)
						if !d.isRetriableError(err) || time.Since(tx.CreateAt) > time.Hour*24*7 {
							// mark error
							tx.Status = uint32(statusError)
						}
					} else {
						tx.Status = uint32(statusDone)
					}

					s := transactionStatus(tx.Status)
					if s != statusInit && s != statusPending {
						// status update
						log.Infof("Updating transaction status: %s => %v", tx.OrderID, tx.Status)
						err := store.updateTxStatus(tx)
						if err != nil {
							log.Warnf("updateTxStatus %s %d failed: %v", tx.OrderID, tx.Status, err)
						}
					}
				}(retryTx)
			}

			log.Debugf("wait %d tasks to finish", len(txList))
			wg.Wait()
			log.Debugf("%d tasks done", len(txList))
		}()
		time.Sleep(time.Minute)
	}
}

//重试没有成功的订单，留着之前版本，防止升级的时候，刚好有需要重试的订单
func (s *exchangeServer) transactionMonitor2() {
	time.Sleep(time.Second * 3) //防止客户端没有初始化完成就跑任务
	xTDb := model.ExchangeTransactionDB{Db: s.db}
	for {
		const maxConcurrency = 50

		log.Debugf("getting retry tasks")
		//txList, err := store.getTxList([]transactionStatus{statusInit, statusPending}, maxConcurrency)
		txList, err := xTDb.GetTxList([]uint32{uint32(statusInit), uint32(statusPending)}, maxConcurrency)
		if err != nil {
			log.Errorf("failed go get tx list: %v", err)
			time.Sleep(retryInterval)
			continue
		}

		if len(txList) == 0 {
			log.Warnf("no retry task")
			time.Sleep(retryInterval)
			continue
		}
		log.Infof("%d retry tasks got from database", len(txList))

		func() {
			locker := cache.NewLocker(s.rc, s.rb)
			lockV := locker.GenerateValue()
			if !locker.LockMonitor(lockV) {
				log.Warnln("transactionMonitor locked")
				return
			}
			defer locker.UnlockMonitor(lockV)
			cancel, cancelFunc := context.WithCancel(context.Background())
			go s.extendTransactionMonitorLock(cancel)
			defer cancelFunc()

			wg := &sync.WaitGroup{}
			for _, retryTx := range txList {
				wg.Add(1)
				func(tx *model.ExchangeTransaction) {
					//ctx2, cancel := context.WithTimeout(log.WithLogTags(ctx, "monitor", fmt.Sprintf("Txn_%d", tx.ID)), time.Minute)
					ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
					defer func() {
						cancel()
						wg.Done()
					}()

					if tx.Status == uint32(statusInit) && time.Since(tx.CreateAt) < time.Second*30 {
						// create in last 30 seconds, do not process this transaction
						log.Warnf("task was created in 30 seconds and init status, do not process")
						return
					}

					log.Debugf("retrying %+v", tx)

					if time.Since(tx.CreateAt) > time.Hour*24 {
						warningText := fmt.Sprintf("重试个人兑换 order_id=%s, target_type=%d, uid=%d, date=%s",
							tx.OrderID, tx.TargetType, tx.UserID, tx.CreateAt.Format("2006-01-02 15:04:05"))
						comm.SendWarning(warningText)
					}

					if tx.TargetType == uint32(pb.CurrencyType_COMMISSION) {
						//判断是否疑似豆商，因为要有提现记录，所以要在这里判断
						var isProvider bool
						isProvider, err = comm.CheckTBeanProvider(ctx, uint64(tx.UserID), user_risk_api.TBeanScene_UserExchange, int64(tx.TargetAmount))
						if err != nil {
							//接口挂了，则放过
							log.ErrorWithCtx(ctx, "comm.CheckTBeanProvider err=%v", err)
						} else {
							if isProvider {
								log.InfoWithCtx(ctx, "comm.CheckTBeanProvider uid=%d is provider", tx.UserID)
								tx.Status = uint32(statusTBeanProvider)
								tmpDb := &model.ExchangeTransactionDB{Db: s.db, ExchangeTransaction: *tx}
								err = tmpDb.UpdateTxStatus()
								if err != nil {
									log.ErrorWithCtx(ctx, "xTDb.UpdateTxStatus err=%v", err)
								}
								err = protocol.NewServerError(status.ErrExchangeStatus, "本次提现失败，提现账号异常，有疑问请联系客服或ttid80513")
								return
							}
						}

						//如果是提现，那么先判断本月是否有额度
						req := &pb.AddPrivateWithdrawLogReq{
							OrderId:    tx.OrderID,
							Uid:        tx.UserID,
							Amount:     uint64(tx.TargetAmount),
							CreateTime: uint32(tx.CreateAt.Unix()),
						}
						_, err = s.AddPrivateWithdrawLog(ctx, req)
						if err != nil {
							serverError := protocol.ToServerError(err)
							if serverError.Code() == status.ErrExchangeWithdrawMonthMax {
								tmpDb := &model.ExchangeTransactionDB{Db: s.db, ExchangeTransaction: *tx}
								err := tmpDb.DeleteTx()
								if err != nil {
									log.Errorf("tmpDb.DeleteTx err=%v", err)
								}
								log.Infof("DeleteTx %d for ")
							}
							if serverError.Code() == status.ErrExchangeNotRealName {
								tmpDb := &model.ExchangeTransactionDB{Db: s.db, ExchangeTransaction: *tx}
								err := tmpDb.DeleteTx()
								if err != nil {
									log.Errorf("tmpDb.DeleteTx err=%v", err)
								}
							}
							return
						}
					}

					d, ok := delegates[pb.CurrencyType(tx.SourceType)]
					if !ok {
						// no delegate for this source type?
						log.Errorf("no delegate for source type: %v", tx.SourceType)
						return
					}

					outsideOrderId, outsideTime, targetBalance, err := d.processTx(ctx, tx)
					tx.TargetBalance = targetBalance
					if err != nil {
						log.Errorf("process transaction %s err: %v", tx.OrderID, err)
						if !d.isRetriableError(err) || time.Since(tx.CreateAt) > time.Hour*24*7 {
							// mark error
							tx.Status = uint32(statusError)
						}
					} else {
						tx.Status = uint32(statusDone)
						tx.OutsideOrderId = outsideOrderId
						if outsideTime.After(tx.CreateAt.Add(-time.Minute)) { //只有货币组那边的时间大于上游exchange的创建时间才更新，防止写到前面，没法对账
							tx.OutsideTime = outsideTime
						}
					}

					//s := transactionStatus(tx.Status)
					if tx.Status != uint32(statusInit) && tx.Status != uint32(statusPending) {
						// status update
						log.Infof("Updating transaction status: %s => %d", tx.OrderID, tx.Status)
						//err := store.updateTxStatus(tx)
						tmpDb := &model.ExchangeTransactionDB{Db: s.db, ExchangeTransaction: *tx}
						err = tmpDb.UpdateTxStatus()
						if err != nil {
							log.Warnf("updateTxStatus %s %d failed: %v", tx.OrderID, tx.Status, err)
						}
					}
				}(retryTx)
			}

			log.Debugf("wait %d tasks to finish", len(txList))
			wg.Wait()
			log.Debugf("%d tasks done", len(txList))
		}()
		time.Sleep(time.Minute)
	}
}