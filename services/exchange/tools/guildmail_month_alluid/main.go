package main

import (
	"context"
	"crypto/tls"
	"fmt"
	"github.com/go-gomail/gomail"
	_ "github.com/go-sql-driver/mysql"
	"github.com/jinzhu/gorm"
	"github.com/tealeg/xlsx"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/realnameauth"
	pb "golang.52tt.com/protocol/services/realNameAuthSvr"
	"golang.52tt.com/services/exchange/model"
	"golang.52tt.com/services/exchange/tools/guildmail_month/config"
	"google.golang.org/grpc"
	"os"
	"time"
)

var sc *config.ServiceConfigT
var mysqlDb *gorm.DB
var accountClient *account.Client
var realnameauthClient *realnameauth.Client
var mock bool

func Init() error {
	sc = &config.ServiceConfigT{}
	path := "/home/<USER>/tools/guild-exchange/guild-exchange-tool.json"
	envPath := os.Getenv("GUILD_CONFIG_PATH")

	mock = false
	isMock := os.Getenv("MOCK")
	if isMock != "" {
		mock = true
	}
	if envPath != "" {
		path = envPath
	}
	err := sc.Parse(path)
	if err != nil {
		fmt.Printf("\nconfig Parse fail err:%v", err)
		return err
	}

	mysqlDb, err = gorm.Open("mysql", sc.GetMysqlConfig().ConnectionString())
	if err != nil {
		fmt.Printf("\nFailed to create scoreMysqlDb %v", err)
		return err
	}

	accountClient, err = account.NewClient(grpc.WithInsecure(), grpc.WithBlock(), grpc.WithAuthority("account.52tt.local"))
	if err != nil {
		fmt.Println(err)
		return err
	}
	realnameauthClient = realnameauth.NewClient(grpc.WithInsecure(), grpc.WithBlock(), grpc.WithAuthority("realnameauth.52tt.local"))

	return nil
}

func getCAList() (list *[]model.ExchangeGuildTransactions) {
	xDB := model.ExchangeGuildTransactionsDB{Db: mysqlDb}
	tNow := time.Now()

	beginTime := time.Date(tNow.Year(), tNow.Month(), tNow.Day(), 0, 0, 0, 0, tNow.Location()).AddDate(0, -1, 0).Unix()
	endTime := time.Date(tNow.Year(), tNow.Month(), tNow.Day(), 0, 0, 0, 0, tNow.Location()).Unix()
	list = xDB.GetCATransactionDuringTime(beginTime, endTime)

	return
}

func getPKList() (list *[]model.ExchangeGuildTransactions) {
	xDB := model.ExchangeGuildTransactionsDB{Db: mysqlDb}
	tNow := time.Now()

	beginTime := time.Date(tNow.Year(), tNow.Month(), tNow.Day(), 0, 0, 0, 0, tNow.Location()).AddDate(0, -1, 0).Unix()
	endTime := time.Date(tNow.Year(), tNow.Month(), tNow.Day(), 0, 0, 0, 0, tNow.Location()).Unix()
	list = xDB.GetCATransactionDuringTime(beginTime, endTime)

	return
}

func getAllList() (list *[]model.ExchangeGuildTransactions) {
	xDB := model.ExchangeGuildTransactionsDB{Db: mysqlDb}
	tNow := time.Now()

	listCa := xDB.GetCATransactionDuringTime(0, tNow.Unix())
	listPk := xDB.GetPKTransactionDuringTime(0, tNow.Unix())

	list = &[]model.ExchangeGuildTransactions{}
	*list = append(*listCa, *listPk...)

	return
}

func SendMail(filePath, subject string) error {
	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")

	m.SetHeader("To", sc.MailsTo...)
	m.SetHeader("Subject", subject)

	m.SetBody("text/html", "见附件")
	m.Attach(filePath) //附件

	d := gomail.NewDialer("mail.52tt.com", 465, "<EMAIL>", "V7.D.sTy@%bDB50t#n.r$A4h+-FnXA")

	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

const SCORE_TYPE_SCORE = 0
const SCORE_TYPE_ANCHOR = 1
const SCORE_TYPE_PK = 2
const SCORE_TYPE_KNIGHT = 3

func generateExcel(isAll bool) (path string) {
	tNow := time.Now()

	file := xlsx.NewFile()
	sheet, err := file.AddSheet("(TT)会长提现记录")
	if err != nil {
		fmt.Println(err)
		return
	}
	title := sheet.AddRow()
	titleList := &[]string{"主播uid", "主播ttid", "主播昵称", "主播身份证", "提现金额", "对公公司名称", "积分类型", "银行账户", "银行名称",
		"银行开户省", "银行开户市", "主播账号绑定的手机号", "申请提现时间", "对公会长uid", "对公会长姓名", "对公会长身份证"}
	title.WriteSlice(titleList, -1)

	var list []model.ExchangeGuildTransactions
	if isAll {
		list = *getAllList()
	} else {
		listCA := getCAList()
		listPk := getPKList()

		if listCA != nil {
			list = append(list, *listCA...)
		}
		if listPk != nil {
			list = append(list, *listPk...)
		}
	}

	listLen := len(list)

	fmt.Println("提现记录条数：", listLen)
	//废弃，转移到结算工单化
	for _, elem := range list {
		ctx := context.Background()
		for i := 0; i < 4; i++ {
			uidInfo, serverError := accountClient.GetUserByUid(ctx, elem.UserId)
			if serverError != nil {
				fmt.Println("GetUserByUid", serverError)
				return
			}

			var anchorRealInfo *pb.GetUserRealNameAuthInfoV2Resp
			if mock {
				anchorRealInfo = &pb.GetUserRealNameAuthInfoV2Resp{}
				anchorRealInfo.AuthPhone = "123"
				anchorRealInfo.IdcardInfo = &pb.AuthIdCardInfo{Name: "abc", IdentityNum: "789"}
			} else {
				anchorRealInfo, serverError = realnameauthClient.GetUserRealNameAuthInfoV2(ctx, elem.UserId, true, true)
				if serverError != nil {
					fmt.Println("GetUserRealNameAuthInfoV2", serverError)
					return
				}
			}

			var masterRealInfo *pb.GetUserRealNameAuthInfoV2Resp
			if mock {
				masterRealInfo = &pb.GetUserRealNameAuthInfoV2Resp{}
				masterRealInfo.AuthPhone = "123"
				masterRealInfo.IdcardInfo = &pb.AuthIdCardInfo{Name: "abc", IdentityNum: "789"}
			} else {
				masterRealInfo, serverError = realnameauthClient.GetUserRealNameAuthInfoV2(ctx, elem.MasterUid, true, true)
				if serverError != nil {
					fmt.Println("GetUserRealNameAuthInfoV2", serverError)
					return
				}
			}

			mainDb := model.ExchangeGuildMainDB{Db: mysqlDb}
			mainDb.GetOne(elem.MasterUid)

			var scoreData float64

			switch i {
			case SCORE_TYPE_SCORE:
				scoreData = float64(elem.Score)
			case SCORE_TYPE_ANCHOR:
				scoreData = float64(elem.AnchorScore)
			case SCORE_TYPE_PK:
				scoreData = float64(elem.MaskedPkScore)
			case SCORE_TYPE_KNIGHT:
				scoreData = float64(elem.KnightScore)
			}

			if scoreData == 0 {
				continue
			}

			scoreData = scoreData / 100

			var scoreTypeStr string
			switch i {
			case SCORE_TYPE_SCORE:
				scoreTypeStr = "礼物积分"
			case SCORE_TYPE_ANCHOR:
				scoreTypeStr = "奖励积分"
			case SCORE_TYPE_PK:
				scoreTypeStr = "蒙面pk积分"
			case SCORE_TYPE_KNIGHT:
				scoreTypeStr = "骑士积分"
			}

			row := sheet.AddRow()

			row.AddCell().SetInt(int(elem.UserId))
			row.AddCell().SetString(uidInfo.Alias)
			row.AddCell().SetString(uidInfo.Nickname)
			row.AddCell().SetString(anchorRealInfo.IdcardInfo.IdentityNum)

			row.AddCell().SetFloat(scoreData)
			row.AddCell().SetString(mainDb.Company)
			row.AddCell().SetString(scoreTypeStr)

			for j := 0; j < 4; j++ { //空4列留给财务填
				row.AddCell().SetString("")
			}
			row.AddCell().SetString(anchorRealInfo.AuthPhone)
			row.AddCell().SetString(time.Unix(int64(elem.CreateAt), 0).Format("2006-01-02 15:04:05"))
			row.AddCell().SetInt(int(elem.MasterUid))
			row.AddCell().SetString(masterRealInfo.IdcardInfo.Name)
			row.AddCell().SetString(masterRealInfo.IdcardInfo.IdentityNum)
		}
	}

	//仓库记录
	sheet2, err := file.AddSheet("(TT)会长仓库记录")
	if err != nil {
		fmt.Println(err)
		return
	}
	title2 := sheet2.AddRow()
	titleList2 := &[]string{"主播uid", "主播ttid", "主播昵称", "主播身份证", "余额", "对公公司名称", "积分类型",
		"主播账号绑定的手机号", "汇总时间", "对公会长uid", "对公会长姓名", "对公会长身份证"}
	title2.WriteSlice(titleList2, -1)

	tsDB := model.ExchangeGuildTSDB{Db: mysqlDb}
	tsAll := tsDB.GetAll0()

	fmt.Println("仓库记录条数：", len(*tsAll))

	for _, elem := range *tsAll {
		ctx := context.Background()
		for i := 0; i < 4; i++ {
			uidInfo, serverError := accountClient.GetUserByUid(ctx, elem.UserId)
			if serverError != nil {
				fmt.Println("GetUserByUid", serverError)
				return
			}

			var anchorRealInfo *pb.GetUserRealNameAuthInfoV2Resp
			if mock {
				anchorRealInfo = &pb.GetUserRealNameAuthInfoV2Resp{}
				anchorRealInfo.AuthPhone = "123"
				anchorRealInfo.IdcardInfo = &pb.AuthIdCardInfo{Name: "abc", IdentityNum: "789"}
			} else {
				anchorRealInfo, serverError = realnameauthClient.GetUserRealNameAuthInfoV2(ctx, elem.UserId, true, true)
				if serverError != nil {
					fmt.Println("GetUserRealNameAuthInfoV2", serverError)
					return
				}
			}

			var masterRealInfo *pb.GetUserRealNameAuthInfoV2Resp
			if mock {
				masterRealInfo = &pb.GetUserRealNameAuthInfoV2Resp{}
				masterRealInfo.AuthPhone = "123"
				masterRealInfo.IdcardInfo = &pb.AuthIdCardInfo{Name: "abc", IdentityNum: "789"}
			} else {
				masterRealInfo, serverError = realnameauthClient.GetUserRealNameAuthInfoV2(ctx, elem.MasterUid, true, true)
				if serverError != nil {
					fmt.Println("GetUserRealNameAuthInfoV2", serverError)
					return
				}
			}

			mainDb := model.ExchangeGuildMainDB{Db: mysqlDb}
			mainDb.GetOne(elem.MasterUid)

			var scoreData float64

			switch i {
			case SCORE_TYPE_SCORE:
				scoreData = float64(elem.Score)
			case SCORE_TYPE_ANCHOR:
				scoreData = float64(elem.AnchorScore)
			case SCORE_TYPE_PK:
				scoreData = float64(elem.MaskedPkScore)
			case SCORE_TYPE_KNIGHT:
				scoreData = float64(elem.KnightScore)
			}

			//if scoreData == 0 {
			//	continue
			//}

			scoreData = scoreData / 100

			var scoreTypeStr string
			switch i {
			case SCORE_TYPE_SCORE:
				scoreTypeStr = "礼物积分"
			case SCORE_TYPE_ANCHOR:
				scoreTypeStr = "奖励积分"
			case SCORE_TYPE_PK:
				scoreTypeStr = "蒙面pk积分"
			case SCORE_TYPE_KNIGHT:
				scoreTypeStr = "骑士积分"
			}

			row := sheet2.AddRow()

			row.AddCell().SetInt(int(elem.UserId))
			row.AddCell().SetString(uidInfo.Alias)
			row.AddCell().SetString(uidInfo.Nickname)
			if anchorRealInfo.IdcardInfo == nil {
				row.AddCell().SetString("")
			} else {
				row.AddCell().SetString(anchorRealInfo.IdcardInfo.IdentityNum)
			}


			row.AddCell().SetFloat(scoreData)
			row.AddCell().SetString(mainDb.Company)
			row.AddCell().SetString(scoreTypeStr)

			row.AddCell().SetString(anchorRealInfo.AuthPhone)
			sumTime := elem.UpdateAt
			if sumTime == 0 {
				sumTime = elem.CreateAt
			}
			row.AddCell().SetString(time.Unix(int64(sumTime), 0).Format("2006-01-02 15:04:05"))
			row.AddCell().SetInt(int(elem.MasterUid))
			if masterRealInfo.IdcardInfo == nil {
				row.AddCell().SetString("")
				row.AddCell().SetString("")
			} else {
				row.AddCell().SetString(masterRealInfo.IdcardInfo.Name)
				row.AddCell().SetString(masterRealInfo.IdcardInfo.IdentityNum)
			}
		}
	}

	path = fmt.Sprintf("(TT)会长提现记录_%s.xlsx", tNow.Format("2006-01-02"))
	err = file.Save(path)
	if err != nil {
		fmt.Println(err)
		return
	}

	return
}

func main() {
	fmt.Println("开始发送", time.Now().Format("2006-01-02 15:04:05"))
	err := Init()
	if err != nil {
		fmt.Println(err)
		return
	}

	isAll := false
	if len(os.Args) == 2 && os.Args[1] == "-a" {
		isAll = true
	}

	generateExcel(isAll)
	//path := generateExcel(isAll)
	//if path != "" {
	//	err = SendMail(path, "(TT)会长提现记录(月结)")
	//	if err != nil {
	//		fmt.Println(err)
	//	}
	//}

	fmt.Println("发送成功", time.Now().Format("2006-01-02 15:04:05"))
}
