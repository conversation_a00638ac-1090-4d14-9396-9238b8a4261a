package model

import "github.com/jinzhu/gorm"

type ExchangeGloryConfig struct {
    Id            uint32 `gorm:"primary_key;auto_increment:true;comment:'id'"`
    FragmentType  uint8  `gorm:"not null;default:0;comment:'0声望，1荣耀'"`
    ExchangeType  uint32 `gorm:"not null;default:0;comment:'兑换类型：0包裹，1坐骑，2麦位框，3房间资料卡，4财富卡，5动态头像，6互动麦位表情，7主页飘，8典藏礼物'"`
    ItemId        string `gorm:"not null;type:varchar(255);default:0;comment:'物品ID'"`
    Intro         string `gorm:"not null;type:varchar(1024);default:'';comment:'说明'"`
    Price         uint32 `gorm:"not null;default:0;comment:'星钻价格'"`
    InventoryType uint8  `gorm:"not null;default:0;comment:'0限制库存，1不限制'"`
    Remain        uint32 `gorm:"not null;default:0;comment:'库存数量'"`
    Tag           string `gorm:"not null;type:varchar(1024);default:'';comment:'标签'"`
    BuyLimitType  uint8  `gorm:"not null;default:0;comment:'0不限购，1每日限购，2每周限购，3每月限购'"`
    BuyLimitCount uint32 `gorm:"not null;default:0;comment:'限购数量'"`
    SortId        uint32 `gorm:"not null;default:0;index:sort_id;comment:'排序id'"`
    BeginTime     int64  `gorm:"not null;default:0;comment:'开始时间'"`
    EndTime       int64  `gorm:"not null;default:0;comment:'结束时间'"`
    CreateTime    int64  `gorm:"not null;default:0;comment:'创建时间'"`
    Remark        string `gorm:"not null;type:varchar(1024);default:'';comment:'备注'"`
    ExpireTime    uint32 `gorm:"not null;default:0;comment:'过期时间'"`
    MaxCount      uint32 `gorm:"not null;default:0"`
    FillType      uint8  `gorm:"not null;default:0"`
    ShowIcon      string `gorm:"not null;type:varchar(1024);default:''"`
    Version       string `gorm:"not null;type:varchar(255);default:''"`
}

func (e *ExchangeGloryConfig) TableName() string {
    return "exchange_glory_config"
}

type ExchangeGloryConfigDB struct {
    ExchangeGloryConfig
    Db *gorm.DB
}

func (xdb *ExchangeGloryConfigDB) AutoMigrate() error {
    return xdb.Db.AutoMigrate(&xdb.ExchangeGloryConfig).Error
}

func (xdb *ExchangeGloryConfigDB) GetAll(fragmentType uint32) ([]ExchangeGloryConfig, error) {
    list := make([]ExchangeGloryConfig, 0)
    err := xdb.Db.Model(&xdb.ExchangeGloryConfig).Where("fragment_type=?", fragmentType).Order("sort_id asc").Find(&list).Error
    return list, err
}

func (xdb *ExchangeGloryConfigDB) Save() error {
    return xdb.Db.Model(&xdb.ExchangeGloryConfig).Save(&xdb.ExchangeGloryConfig).Error
}

func (xdb *ExchangeGloryConfigDB) UpdateAllSort(idList []uint32) error {
    sortId := 1
    for _, id := range idList {
        err := xdb.Db.Model(&xdb.ExchangeGloryConfig).Where("id=?", id).
            Updates(map[string]interface{}{"sort_id": sortId}).Error
        if err != nil {
            return err
        }
        sortId++
    }
    return nil
}

func (xdb *ExchangeGloryConfigDB) Del(id uint32) error {
    return xdb.Db.Model(&xdb.ExchangeGloryConfig).Where("id=?", id).Delete(&xdb.ExchangeGloryConfig).Error
}

func (xdb *ExchangeGloryConfigDB) GetData(id uint32) error {
    err := xdb.Db.Model(&xdb.ExchangeGloryConfig).Where("id=?", id).First(&xdb.ExchangeGloryConfig).Error
    if err == gorm.ErrRecordNotFound {
        return nil
    }
    return err
}

func (xdb *ExchangeGloryConfigDB) UpdateRemain(id uint32) error {
    return xdb.Db.Model(&xdb.ExchangeGloryConfig).Where("id=?", id).
        Updates(map[string]interface{}{"remain": xdb.Remain}).Error
}

func (xdb *ExchangeGloryConfigDB) GetRemainLess(inventoryType uint8, lessRemain uint32) ([]ExchangeGloryConfig, error) {
    list := make([]ExchangeGloryConfig, 0)
    err := xdb.Db.Model(&xdb.ExchangeGloryConfig).Where("inventory_type=? and remain<=?", inventoryType, lessRemain).Find(&list).Error
    return list, err
}

func (xdb *ExchangeGloryConfigDB) GetAllCanFill(timeNow int64, inventoryType uint8) ([]ExchangeGloryConfig, error) {
    list := make([]ExchangeGloryConfig, 0)
    err := xdb.Db.Model(&xdb.ExchangeGloryConfig).Where("max_count>0 and begin_time<? and end_time>? and inventory_type=?", timeNow, timeNow, inventoryType).Find(&list).Error
    return list, err
}

func (xdb *ExchangeGloryConfigDB) GetRemainLessPercent(inventoryType uint8, percent uint32) ([]ExchangeGloryConfig, error) {
    list := make([]ExchangeGloryConfig, 0)
    err := xdb.Db.Model(&xdb.ExchangeGloryConfig).Where("inventory_type=? and floor(remain * 100/max_count)<?", inventoryType, percent).Find(&list).Error
    return list, err
}