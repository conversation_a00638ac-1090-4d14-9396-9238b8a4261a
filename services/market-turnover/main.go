package main

import (
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/market-turnover/conf"
	"golang.52tt.com/services/market-turnover/manager"
	"net/http"
)

func main() {
	sc := conf.GetInstance()
	log.Infof("sc redis:%+v , userscore_mysql:%+v, kafka :%+v", sc.GetRedisConfig(), sc.GetUserScoreMysqlConfig(), sc.GetPresentKafkaConfig())
	err := manager.NewTurnoverServer(*sc)
	if err != nil {
		return
	}
	http.HandleFunc("/market_turnover/market_turnover", TriggerMarketTurnover)
	_ = http.ListenAndServe(fmt.Sprintf(":%v", sc.GetPort()), nil)
}

func TriggerMarketTurnover(w http.ResponseWriter, r *http.Request) {

}
