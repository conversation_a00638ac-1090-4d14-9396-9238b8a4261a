// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/present-privilege/manager (interfaces: IManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	presentprivilege "golang.52tt.com/protocol/services/presentprivilege"
)

// MockIManager is a mock of IManager interface.
type MockIManager struct {
	ctrl     *gomock.Controller
	recorder *MockIManagerMockRecorder
}

// MockIManagerMockRecorder is the mock recorder for MockIManager.
type MockIManagerMockRecorder struct {
	mock *MockIManager
}

// NewMockIManager creates a new mock instance.
func NewMockIManager(ctrl *gomock.Controller) *MockIManager {
	mock := &MockIManager{ctrl: ctrl}
	mock.recorder = &MockIManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIManager) EXPECT() *MockIManagerMockRecorder {
	return m.recorder
}

// AddCondition mocks base method.
func (m *MockIManager) AddCondition(arg0 context.Context, arg1 string, arg2 []uint32, arg3 *presentprivilege.Privilege) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCondition", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCondition indicates an expected call of AddCondition.
func (mr *MockIManagerMockRecorder) AddCondition(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCondition", reflect.TypeOf((*MockIManager)(nil).AddCondition), arg0, arg1, arg2, arg3)
}

// CheckPrivilege mocks base method.
func (m *MockIManager) CheckPrivilege(arg0 context.Context, arg1 *presentprivilege.Privilege) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckPrivilege", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckPrivilege indicates an expected call of CheckPrivilege.
func (mr *MockIManagerMockRecorder) CheckPrivilege(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckPrivilege", reflect.TypeOf((*MockIManager)(nil).CheckPrivilege), arg0, arg1)
}

// DelConditions mocks base method.
func (m *MockIManager) DelConditions(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelConditions", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelConditions indicates an expected call of DelConditions.
func (mr *MockIManagerMockRecorder) DelConditions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelConditions", reflect.TypeOf((*MockIManager)(nil).DelConditions), arg0, arg1)
}

// DelUserTreasureConditionVal mocks base method.
func (m *MockIManager) DelUserTreasureConditionVal(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserTreasureConditionVal", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserTreasureConditionVal indicates an expected call of DelUserTreasureConditionVal.
func (mr *MockIManagerMockRecorder) DelUserTreasureConditionVal(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserTreasureConditionVal", reflect.TypeOf((*MockIManager)(nil).DelUserTreasureConditionVal), arg0, arg1)
}

// GetConditionsListCache mocks base method.
func (m *MockIManager) GetConditionsListCache(arg0 context.Context, arg1, arg2 uint32) ([]*presentprivilege.Privilege, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConditionsListCache", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*presentprivilege.Privilege)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetConditionsListCache indicates an expected call of GetConditionsListCache.
func (mr *MockIManagerMockRecorder) GetConditionsListCache(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConditionsListCache", reflect.TypeOf((*MockIManager)(nil).GetConditionsListCache), arg0, arg1, arg2)
}

// GetPresentPrivilege mocks base method.
func (m *MockIManager) GetPresentPrivilege(arg0 context.Context, arg1 uint32) ([]*presentprivilege.PresentPrivilege, map[string]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentPrivilege", arg0, arg1)
	ret0, _ := ret[0].([]*presentprivilege.PresentPrivilege)
	ret1, _ := ret[1].(map[string]uint64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPresentPrivilege indicates an expected call of GetPresentPrivilege.
func (mr *MockIManagerMockRecorder) GetPresentPrivilege(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentPrivilege", reflect.TypeOf((*MockIManager)(nil).GetPresentPrivilege), arg0, arg1)
}

// HandlerConsumerEvent mocks base method.
func (m *MockIManager) HandlerConsumerEvent(arg0 context.Context, arg1 string, arg2, arg3 uint32, arg4 presentprivilege.ConditionType, arg5 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlerConsumerEvent", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandlerConsumerEvent indicates an expected call of HandlerConsumerEvent.
func (mr *MockIManagerMockRecorder) HandlerConsumerEvent(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlerConsumerEvent", reflect.TypeOf((*MockIManager)(nil).HandlerConsumerEvent), arg0, arg1, arg2, arg3, arg4, arg5)
}

// HandlerNobilityEvent mocks base method.
func (m *MockIManager) HandlerNobilityEvent(arg0 context.Context, arg1, arg2 uint32, arg3 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlerNobilityEvent", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandlerNobilityEvent indicates an expected call of HandlerNobilityEvent.
func (mr *MockIManagerMockRecorder) HandlerNobilityEvent(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlerNobilityEvent", reflect.TypeOf((*MockIManager)(nil).HandlerNobilityEvent), arg0, arg1, arg2, arg3)
}

// HandlerPresentEvent mocks base method.
func (m *MockIManager) HandlerPresentEvent(arg0 context.Context, arg1 string, arg2, arg3 uint32, arg4 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlerPresentEvent", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandlerPresentEvent indicates an expected call of HandlerPresentEvent.
func (mr *MockIManagerMockRecorder) HandlerPresentEvent(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlerPresentEvent", reflect.TypeOf((*MockIManager)(nil).HandlerPresentEvent), arg0, arg1, arg2, arg3, arg4)
}
