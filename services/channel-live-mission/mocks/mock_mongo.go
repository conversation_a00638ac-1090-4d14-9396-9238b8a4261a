// Code generated by MockGen. DO NOT EDIT.
// Source: ../mongo/mongo_api.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	mongo "golang.52tt.com/services/channel-live-mission/mongo"
)

// MockIMongoDao is a mock of IMongoDao interface.
type MockIMongoDao struct {
	ctrl     *gomock.Controller
	recorder *MockIMongoDaoMockRecorder
}

// MockIMongoDaoMockRecorder is the mock recorder for MockIMongoDao.
type MockIMongoDaoMockRecorder struct {
	mock *MockIMongoDao
}

// NewMockIMongoDao creates a new mock instance.
func NewMockIMongoDao(ctrl *gomock.Controller) *MockIMongoDao {
	mock := &MockIMongoDao{ctrl: ctrl}
	mock.recorder = &MockIMongoDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIMongoDao) EXPECT() *MockIMongoDaoMockRecorder {
	return m.recorder
}

// CleanFansMission mocks base method.
func (m *MockIMongoDao) CleanFansMission(ctx context.Context, uid, actorUid uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CleanFansMission", ctx, uid, actorUid)
	ret0, _ := ret[0].(error)
	return ret0
}

// CleanFansMission indicates an expected call of CleanFansMission.
func (mr *MockIMongoDaoMockRecorder) CleanFansMission(ctx, uid, actorUid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanFansMission", reflect.TypeOf((*MockIMongoDao)(nil).CleanFansMission), ctx, uid, actorUid)
}

// ClearLiveRecodeBeforeTs mocks base method.
func (m *MockIMongoDao) ClearLiveRecodeBeforeTs(beforeTs int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearLiveRecodeBeforeTs", beforeTs)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearLiveRecodeBeforeTs indicates an expected call of ClearLiveRecodeBeforeTs.
func (mr *MockIMongoDaoMockRecorder) ClearLiveRecodeBeforeTs(beforeTs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearLiveRecodeBeforeTs", reflect.TypeOf((*MockIMongoDao)(nil).ClearLiveRecodeBeforeTs), beforeTs)
}

// CreateIndexes mocks base method.
func (m *MockIMongoDao) CreateIndexes() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIndexes")
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateIndexes indicates an expected call of CreateIndexes.
func (mr *MockIMongoDaoMockRecorder) CreateIndexes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIndexes", reflect.TypeOf((*MockIMongoDao)(nil).CreateIndexes))
}

// GetFansMissionById mocks base method.
func (m *MockIMongoDao) GetFansMissionById(uid, actorUid, missionId uint32) (*mongo.FansMission, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFansMissionById", uid, actorUid, missionId)
	ret0, _ := ret[0].(*mongo.FansMission)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFansMissionById indicates an expected call of GetFansMissionById.
func (mr *MockIMongoDaoMockRecorder) GetFansMissionById(uid, actorUid, missionId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFansMissionById", reflect.TypeOf((*MockIMongoDao)(nil).GetFansMissionById), uid, actorUid, missionId)
}

// GetFansMissionFinishUserCnt mocks base method.
func (m *MockIMongoDao) GetFansMissionFinishUserCnt(missionId uint32, date string) (*mongo.FansMissionRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFansMissionFinishUserCnt", missionId, date)
	ret0, _ := ret[0].(*mongo.FansMissionRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFansMissionFinishUserCnt indicates an expected call of GetFansMissionFinishUserCnt.
func (mr *MockIMongoDaoMockRecorder) GetFansMissionFinishUserCnt(missionId, date interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFansMissionFinishUserCnt", reflect.TypeOf((*MockIMongoDao)(nil).GetFansMissionFinishUserCnt), missionId, date)
}

// GetFansMissionFinishUserCntByTime mocks base method.
func (m *MockIMongoDao) GetFansMissionFinishUserCntByTime(b, e time.Time) ([]*mongo.FansMissionRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFansMissionFinishUserCntByTime", b, e)
	ret0, _ := ret[0].([]*mongo.FansMissionRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFansMissionFinishUserCntByTime indicates an expected call of GetFansMissionFinishUserCntByTime.
func (mr *MockIMongoDaoMockRecorder) GetFansMissionFinishUserCntByTime(b, e interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFansMissionFinishUserCntByTime", reflect.TypeOf((*MockIMongoDao)(nil).GetFansMissionFinishUserCntByTime), b, e)
}

// GetFansMissionList mocks base method.
func (m *MockIMongoDao) GetFansMissionList(uid, actorUid uint32) (map[uint32]*mongo.FansMission, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFansMissionList", uid, actorUid)
	ret0, _ := ret[0].(map[uint32]*mongo.FansMission)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFansMissionList indicates an expected call of GetFansMissionList.
func (mr *MockIMongoDaoMockRecorder) GetFansMissionList(uid, actorUid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFansMissionList", reflect.TypeOf((*MockIMongoDao)(nil).GetFansMissionList), uid, actorUid)
}

// GetLastDayActorRecordList mocks base method.
func (m *MockIMongoDao) GetLastDayActorRecordList(now time.Time) ([]*mongo.ActorLiveRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastDayActorRecordList", now)
	ret0, _ := ret[0].([]*mongo.ActorLiveRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastDayActorRecordList indicates an expected call of GetLastDayActorRecordList.
func (mr *MockIMongoDaoMockRecorder) GetLastDayActorRecordList(now interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastDayActorRecordList", reflect.TypeOf((*MockIMongoDao)(nil).GetLastDayActorRecordList), now)
}

// GetLastWeekActorRecordList mocks base method.
func (m *MockIMongoDao) GetLastWeekActorRecordList(now time.Time) ([]*mongo.ActorLiveRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastWeekActorRecordList", now)
	ret0, _ := ret[0].([]*mongo.ActorLiveRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastWeekActorRecordList indicates an expected call of GetLastWeekActorRecordList.
func (mr *MockIMongoDaoMockRecorder) GetLastWeekActorRecordList(now interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastWeekActorRecordList", reflect.TypeOf((*MockIMongoDao)(nil).GetLastWeekActorRecordList), now)
}

// GetUserMissionById mocks base method.
func (m *MockIMongoDao) GetUserMissionById(uid, missionId uint32) (*mongo.UserMission, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserMissionById", uid, missionId)
	ret0, _ := ret[0].(*mongo.UserMission)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserMissionById indicates an expected call of GetUserMissionById.
func (mr *MockIMongoDaoMockRecorder) GetUserMissionById(uid, missionId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserMissionById", reflect.TypeOf((*MockIMongoDao)(nil).GetUserMissionById), uid, missionId)
}

// GetUserMissionList mocks base method.
func (m *MockIMongoDao) GetUserMissionList(uid uint32) (map[uint32]*mongo.UserMission, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserMissionList", uid)
	ret0, _ := ret[0].(map[uint32]*mongo.UserMission)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserMissionList indicates an expected call of GetUserMissionList.
func (mr *MockIMongoDaoMockRecorder) GetUserMissionList(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserMissionList", reflect.TypeOf((*MockIMongoDao)(nil).GetUserMissionList), uid)
}

// GetUserThisDayActorRecord mocks base method.
func (m *MockIMongoDao) GetUserThisDayActorRecord(uid uint32) (uint64, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserThisDayActorRecord", uid)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserThisDayActorRecord indicates an expected call of GetUserThisDayActorRecord.
func (mr *MockIMongoDaoMockRecorder) GetUserThisDayActorRecord(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserThisDayActorRecord", reflect.TypeOf((*MockIMongoDao)(nil).GetUserThisDayActorRecord), uid)
}

// GetUserThisWeekActorRecord mocks base method.
func (m *MockIMongoDao) GetUserThisWeekActorRecord(uid uint32) (uint64, uint32, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserThisWeekActorRecord", uid)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(int64)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// GetUserThisWeekActorRecord indicates an expected call of GetUserThisWeekActorRecord.
func (mr *MockIMongoDaoMockRecorder) GetUserThisWeekActorRecord(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserThisWeekActorRecord", reflect.TypeOf((*MockIMongoDao)(nil).GetUserThisWeekActorRecord), uid)
}

// IncrActorIncome mocks base method.
func (m *MockIMongoDao) IncrActorIncome(uid, incr uint32, ts int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrActorIncome", uid, incr, ts)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrActorIncome indicates an expected call of IncrActorIncome.
func (mr *MockIMongoDaoMockRecorder) IncrActorIncome(uid, incr, ts interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrActorIncome", reflect.TypeOf((*MockIMongoDao)(nil).IncrActorIncome), uid, incr, ts)
}

// IncrDayActorLiveTimeCnt mocks base method.
func (m *MockIMongoDao) IncrDayActorLiveTimeCnt(uid, incr uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrDayActorLiveTimeCnt", uid, incr)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrDayActorLiveTimeCnt indicates an expected call of IncrDayActorLiveTimeCnt.
func (mr *MockIMongoDaoMockRecorder) IncrDayActorLiveTimeCnt(uid, incr interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrDayActorLiveTimeCnt", reflect.TypeOf((*MockIMongoDao)(nil).IncrDayActorLiveTimeCnt), uid, incr)
}

// IncrFansMissionFinishCnt mocks base method.
func (m *MockIMongoDao) IncrFansMissionFinishCnt(uid, actorUid, missionId, incrCnt uint32) (*mongo.FansMission, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrFansMissionFinishCnt", uid, actorUid, missionId, incrCnt)
	ret0, _ := ret[0].(*mongo.FansMission)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrFansMissionFinishCnt indicates an expected call of IncrFansMissionFinishCnt.
func (mr *MockIMongoDaoMockRecorder) IncrFansMissionFinishCnt(uid, actorUid, missionId, incrCnt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrFansMissionFinishCnt", reflect.TypeOf((*MockIMongoDao)(nil).IncrFansMissionFinishCnt), uid, actorUid, missionId, incrCnt)
}

// IncrFansMissionFinishUserCnt mocks base method.
func (m *MockIMongoDao) IncrFansMissionFinishUserCnt(missionId uint32, t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrFansMissionFinishUserCnt", missionId, t)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrFansMissionFinishUserCnt indicates an expected call of IncrFansMissionFinishUserCnt.
func (mr *MockIMongoDaoMockRecorder) IncrFansMissionFinishUserCnt(missionId, t interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrFansMissionFinishUserCnt", reflect.TypeOf((*MockIMongoDao)(nil).IncrFansMissionFinishUserCnt), missionId, t)
}

// IncrUserMissionFinishCnt mocks base method.
func (m *MockIMongoDao) IncrUserMissionFinishCnt(uid, missionId, incrCnt uint32) (*mongo.UserMission, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrUserMissionFinishCnt", uid, missionId, incrCnt)
	ret0, _ := ret[0].(*mongo.UserMission)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrUserMissionFinishCnt indicates an expected call of IncrUserMissionFinishCnt.
func (mr *MockIMongoDaoMockRecorder) IncrUserMissionFinishCnt(uid, missionId, incrCnt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrUserMissionFinishCnt", reflect.TypeOf((*MockIMongoDao)(nil).IncrUserMissionFinishCnt), uid, missionId, incrCnt)
}

// IncrWeekActorLiveValidDays mocks base method.
func (m *MockIMongoDao) IncrWeekActorLiveValidDays(uid, incr uint32, now time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrWeekActorLiveValidDays", uid, incr, now)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrWeekActorLiveValidDays indicates an expected call of IncrWeekActorLiveValidDays.
func (mr *MockIMongoDaoMockRecorder) IncrWeekActorLiveValidDays(uid, incr, now interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrWeekActorLiveValidDays", reflect.TypeOf((*MockIMongoDao)(nil).IncrWeekActorLiveValidDays), uid, incr, now)
}

// ResetFansMissionStatus mocks base method.
func (m *MockIMongoDao) ResetFansMissionStatus(uid, actorUid, missionId uint32, updateTime time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetFansMissionStatus", uid, actorUid, missionId, updateTime)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetFansMissionStatus indicates an expected call of ResetFansMissionStatus.
func (mr *MockIMongoDaoMockRecorder) ResetFansMissionStatus(uid, actorUid, missionId, updateTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetFansMissionStatus", reflect.TypeOf((*MockIMongoDao)(nil).ResetFansMissionStatus), uid, actorUid, missionId, updateTime)
}

// ResetUserMissionStatus mocks base method.
func (m *MockIMongoDao) ResetUserMissionStatus(uid, missionId uint32, updateTime time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetUserMissionStatus", uid, missionId, updateTime)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetUserMissionStatus indicates an expected call of ResetUserMissionStatus.
func (mr *MockIMongoDaoMockRecorder) ResetUserMissionStatus(uid, missionId, updateTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetUserMissionStatus", reflect.TypeOf((*MockIMongoDao)(nil).ResetUserMissionStatus), uid, missionId, updateTime)
}

// UpdateFansMissionStatus mocks base method.
func (m *MockIMongoDao) UpdateFansMissionStatus(uid, actorUid, missionId, status uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFansMissionStatus", uid, actorUid, missionId, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateFansMissionStatus indicates an expected call of UpdateFansMissionStatus.
func (mr *MockIMongoDaoMockRecorder) UpdateFansMissionStatus(uid, actorUid, missionId, status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFansMissionStatus", reflect.TypeOf((*MockIMongoDao)(nil).UpdateFansMissionStatus), uid, actorUid, missionId, status)
}

// UpdateFansMissionTime mocks base method.
func (m *MockIMongoDao) UpdateFansMissionTime(ctx context.Context, uid, actorUid, missionId uint32, updateTime time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFansMissionTime", ctx, uid, actorUid, missionId, updateTime)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateFansMissionTime indicates an expected call of UpdateFansMissionTime.
func (mr *MockIMongoDaoMockRecorder) UpdateFansMissionTime(ctx, uid, actorUid, missionId, updateTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFansMissionTime", reflect.TypeOf((*MockIMongoDao)(nil).UpdateFansMissionTime), ctx, uid, actorUid, missionId, updateTime)
}

// UpdateUserMissionStatus mocks base method.
func (m *MockIMongoDao) UpdateUserMissionStatus(uid, missionId, status uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserMissionStatus", uid, missionId, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserMissionStatus indicates an expected call of UpdateUserMissionStatus.
func (mr *MockIMongoDaoMockRecorder) UpdateUserMissionStatus(uid, missionId, status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserMissionStatus", reflect.TypeOf((*MockIMongoDao)(nil).UpdateUserMissionStatus), uid, missionId, status)
}
