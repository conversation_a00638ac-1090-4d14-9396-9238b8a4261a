package server

import (
	"bou.ke/monkey"
	"context"
	"encoding/json"
	"fmt"
	"github.com/golang/mock/gomock"
	apicenter "golang.52tt.com/clients/mocks/apicenter/apiserver"
	backpacksenderMock "golang.52tt.com/clients/mocks/backpack-sender"
	"golang.52tt.com/clients/mocks/channel-live-fans"
	"golang.52tt.com/clients/mocks/channel-live-mgr"
	knightmemberMock "golang.52tt.com/clients/mocks/knight-group-members"
	user_profile_api "golang.52tt.com/clients/mocks/user-profile-api"
	push "golang.52tt.com/clients/push-notification/v2"
	pb "golang.52tt.com/protocol/services/channel-live-mission"
	liveMgrPb "golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/services/channel-live-mission/cache"
	"golang.52tt.com/services/channel-live-mission/conf"
	"golang.52tt.com/services/channel-live-mission/mocks"
	"golang.52tt.com/services/channel-live-mission/mongo"
	"reflect"
	"testing"
	"time"
)

var anchorUid uint32 = 2212797
var updateTm = time.Unix(1670553116, 0)
var fansUid uint32 = 2195821

func TestChannelLiveMissionServer_UpdateUserMissionCache(t *testing.T) {
	type fields struct {
		MongoDao    mongo.IMongoDao
		CacheClient cache.IChannelLiveMissionCache
	}
	type args struct {
		ctx context.Context
		uid uint32
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockMongo := mocks.NewMockIMongoDao(ctl)
	mockCache := mocks.NewMockIChannelLiveMissionCache(ctl)

	ctx := context.Background()

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[uint32]*cache.MissionCacheInfo
		wantErr bool
	}{
		{
			name: "UpdateUserMissionCache",
			fields: fields{
				MongoDao:    mockMongo,
				CacheClient: mockCache,
			},
			args: args{
				ctx: ctx,
				uid: anchorUid,
			},
			want: map[uint32]*cache.MissionCacheInfo{
				203: &cache.MissionCacheInfo{
					MissionId:  203,
					Status:     1,
					FinishCnt:  1,
					UpdateTime: updateTm,
				},
			},
			wantErr: false,
		},
	}

	gomock.InOrder(
		mockMongo.EXPECT().GetUserMissionList(gomock.Any()).Return(map[uint32]*mongo.UserMission{
			203: &mongo.UserMission{
				MissionId:  203,
				Status:     1,
				FinishCnt:  1,
				UpdateTime: updateTm,
			},
		}, nil),
		mockCache.EXPECT().BatchUpdateUserMission(gomock.Any(), gomock.Any()),
	)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelLiveMissionServer{
				MongoDao:    tt.fields.MongoDao,
				CacheClient: tt.fields.CacheClient,
			}
			got, err := s.UpdateUserMissionCache(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateUserMissionCache() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateUserMissionCache() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelLiveMissionServer_GetUserMissionFromCache(t *testing.T) {
	type fields struct {
		MongoDao    mongo.IMongoDao
		CacheClient cache.IChannelLiveMissionCache
	}
	type args struct {
		ctx context.Context
		uid uint32
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockMongo := mocks.NewMockIMongoDao(ctl)
	mockCache := mocks.NewMockIChannelLiveMissionCache(ctl)

	ctx := context.Background()

	gomock.InOrder(
		mockCache.EXPECT().GetUserMissions(gomock.Any()).Return(make(map[uint32]*cache.MissionCacheInfo), nil),
		mockMongo.EXPECT().GetUserMissionList(gomock.Any()).Return(make(map[uint32]*mongo.UserMission), nil),
		mockCache.EXPECT().UpdateUserMission(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[uint32]*cache.MissionCacheInfo
		wantErr bool
	}{
		{
			name:   "GetUserMissionFromCache",
			fields: fields{MongoDao: mockMongo, CacheClient: mockCache},
			args: args{
				ctx: ctx,
				uid: anchorUid,
			},
			want:    make(map[uint32]*cache.MissionCacheInfo),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelLiveMissionServer{
				MongoDao:    tt.fields.MongoDao,
				CacheClient: tt.fields.CacheClient,
			}
			got, err := s.GetUserMissionFromCache(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserMissionFromCache() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserMissionFromCache() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelLiveMissionServer_GetUserMission(t *testing.T) {
	type fields struct {
		MissionConf conf.IMissionConfManager
		MongoDao    mongo.IMongoDao
		CacheClient cache.IChannelLiveMissionCache
	}
	type args struct {
		ctx context.Context
		in  *pb.GetUserMissionReq
	}

	monkey.Patch(time.Now, func() time.Time {
		return updateTm
	})

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockMongo := mocks.NewMockIMongoDao(ctl)
	mockCache := mocks.NewMockIChannelLiveMissionCache(ctl)
	mockConf := mocks.NewMockIMissionConfManager(ctl)

	ctx := context.Background()

	gomock.InOrder(
		mockCache.EXPECT().GetUserMissions(gomock.Any()).Return(map[uint32]*cache.MissionCacheInfo{
			203: {
				MissionId:  203,
				Status:     0,
				FinishCnt:  1,
				UpdateTime: updateTm,
			},
		}, nil),
		mockConf.EXPECT().GetUserMissionConfMap().Return(map[uint32]*conf.UserMissionConf{
			203: {
				MissionId:     203,
				MissionType:   3,
				MissionName:   "ddd",
				MissionDesc:   "ddd",
				IconUrl:       "",
				GoalFinishCnt: 3,
				AwardGiftId:   0,
				AwardNum:      10,
			},
		}),
		mockMongo.EXPECT().ResetUserMissionStatus(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
		mockCache.EXPECT().UpdateUserMission(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserMissionResp
		wantErr bool
	}{
		{
			name: "GetUserMission",
			fields: fields{
				MissionConf: mockConf,
				MongoDao:    mockMongo,
				CacheClient: mockCache,
			},
			args: args{
				ctx: ctx,
				in: &pb.GetUserMissionReq{
					Uid: anchorUid,
				},
			},
			want: &pb.GetUserMissionResp{
				MissionList: []*pb.ChannelLiveMissionInfo{
					&pb.ChannelLiveMissionInfo{
						MissionId:   203,
						MissionDesc: "ddd",
						MissionType: 3,
						MissionName: "ddd",
						UpdateTime:  updateTm.Unix(),
						Status:      0,
						GoalCnt:     3,
						OperType:    0,
						AwardNum:    10,
						FinishCnt:   1,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelLiveMissionServer{
				MissionConf: tt.fields.MissionConf,
				MongoDao:    tt.fields.MongoDao,
				CacheClient: tt.fields.CacheClient,
			}
			got, err := s.GetUserMission(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserMission() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserMission() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelLiveMissionServer_CheckIfCanHandleUserMission(t *testing.T) {
	type fields struct {
		CacheClient cache.IChannelLiveMissionCache
	}
	type args struct {
		ctx                context.Context
		uid                uint32
		missionId          uint32
		antiRepeatedMember uint32
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockIChannelLiveMissionCache(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockCache.EXPECT().AddMemberToSet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name:   "CheckIfCanHandleUserMission",
			fields: fields{CacheClient: mockCache},
			args: args{
				ctx:                ctx,
				uid:                anchorUid,
				missionId:          101,
				antiRepeatedMember: anchorUid,
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelLiveMissionServer{
				CacheClient: tt.fields.CacheClient,
			}
			got, err := s.CheckIfCanHandleUserMission(tt.args.ctx, tt.args.uid, tt.args.missionId, tt.args.antiRepeatedMember)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckIfCanHandleUserMission() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CheckIfCanHandleUserMission() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelLiveMissionServer_HandleUserMission(t *testing.T) {
	type fields struct {
		MissionConf          conf.IMissionConfManager
		MongoDao             mongo.IMongoDao
		CacheClient          cache.IChannelLiveMissionCache
		BackpackSenderClient *backpacksenderMock.MockIClient
		userProfileCLi       *user_profile_api.MockIClient
		PushCli              *push.MockIClient
		apiCli               *apicenter.MockIClient
		Sc                   conf.IServiceConfigT
	}
	type args struct {
		ctx context.Context
		in  *pb.HandleUserMissionReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockMongo := mocks.NewMockIMongoDao(ctl)
	mockCache := mocks.NewMockIChannelLiveMissionCache(ctl)
	mockConf := mocks.NewMockIMissionConfManager(ctl)
	mockBackCli := backpacksenderMock.NewMockIClient(ctl)
	mockPush := push.NewMockIClient(ctl)
	mockUserProfile := user_profile_api.NewMockIClient(ctl)
	mockSc := mocks.NewMockIServiceConfigT(ctl)
	mockApi := apicenter.NewMockIClient(ctl)

	ctx := context.Background()

	gomock.InOrder(
		mockConf.EXPECT().CheckInUserMissionWhiteList(gomock.Any()).Return(true),
		mockCache.EXPECT().AddMemberToSet(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockConf.EXPECT().GetUserMissionConfById(gomock.Any()).Return(&conf.UserMissionConf{
			MissionId:     101,
			MissionType:   3,
			MissionName:   "ddd",
			MissionDesc:   "ddd",
			IconUrl:       "",
			GoalFinishCnt: 3,
			AwardGiftId:   0,
			AwardNum:      10,
		}, true),
		mockCache.EXPECT().GetUserMissionById(gomock.Any(), gomock.Any()).Return(&cache.MissionCacheInfo{
			MissionId:  101,
			Status:     0,
			FinishCnt:  1,
			UpdateTime: updateTm.AddDate(0, 0, -2),
		}, nil),
		mockMongo.EXPECT().ResetUserMissionStatus(gomock.Any(), gomock.Any(), gomock.Any()),
		mockCache.EXPECT().UpdateUserMission(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
		mockConf.EXPECT().GetUserMissionConfById(gomock.Any()).Return(&conf.UserMissionConf{
			MissionId:     101,
			MissionType:   3,
			MissionName:   "ddd",
			MissionDesc:   "ddd",
			IconUrl:       "",
			GoalFinishCnt: 3,
			AwardGiftId:   0,
			AwardNum:      10,
		}, true),
		mockMongo.EXPECT().IncrUserMissionFinishCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mongo.UserMission{
			Uid:        anchorUid,
			MissionId:  101,
			Status:     1,
			FinishCnt:  3,
			UpdateTime: updateTm,
		}, nil),
		mockMongo.EXPECT().UpdateUserMissionStatus(gomock.Any(), gomock.Any(), gomock.Any()),
		mockCache.EXPECT().UpdateUserMission(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
		mockSc.EXPECT().GetBackpackSenderConfig().Return(&conf.BackpackSenderConfig{
			BusinessID: 8,
			SecretKey:  "tzytciintvevmwis",
		}),
		mockBackCli.EXPECT().SendBackpackWithRiskControl(gomock.Any(), gomock.Any()).AnyTimes(),
		mockUserProfile.EXPECT().GetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
		mockPush.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
		mockApi.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.HandleUserMissionResp
		wantErr bool
	}{
		{
			name: "HandleUserMission",
			fields: fields{
				MissionConf:          mockConf,
				MongoDao:             mockMongo,
				CacheClient:          mockCache,
				BackpackSenderClient: mockBackCli,
				userProfileCLi:       mockUserProfile,
				PushCli:              mockPush,
				Sc:                   mockSc,
				apiCli:               mockApi,
			},
			args: args{
				ctx: ctx,
				in: &pb.HandleUserMissionReq{
					Uid:             anchorUid,
					MissionId:       101,
					ChannelId:       111,
					IncrFinishCnt:   1,
					AntiRepeatedMem: 1,
				},
			},
			want:    &pb.HandleUserMissionResp{Status: 1},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelLiveMissionServer{
				MissionConf:          tt.fields.MissionConf,
				MongoDao:             tt.fields.MongoDao,
				CacheClient:          tt.fields.CacheClient,
				BackpackSenderClient: tt.fields.BackpackSenderClient,
				PushCli:              tt.fields.PushCli,
				userProfileCLi:       tt.fields.userProfileCLi,
				Sc:                   tt.fields.Sc,
				ApiCli:               tt.fields.apiCli,
			}
			got, err := s.HandleUserMission(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("HandleUserMission() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("HandleUserMission() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelLiveMissionServer_GrantLiveMissionAward(t *testing.T) {
	type fields struct {
		CacheClient cache.IChannelLiveMissionCache
	}
	type args struct {
		ctx context.Context
		in  *pb.GrantLiveMissionAwardReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockIChannelLiveMissionCache(ctl)

	ctx := context.Background()

	gomock.InOrder(
		mockCache.EXPECT().GrantAnchorMissionAward(gomock.Any(), gomock.Any(), gomock.Any()),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GrantLiveMissionAwardResp
		wantErr bool
	}{
		{
			name:   "GrantLiveMissionAward",
			fields: fields{CacheClient: mockCache},
			args: args{
				ctx: ctx,
				in: &pb.GrantLiveMissionAwardReq{
					AwardInfo: &pb.MissionAwardInfo{
						MissionType: 1,
						AnchorUid:   anchorUid,
						AwardConfId: 1,
						BeginTs:     0,
						EndTs:       10,
					},
				},
			},
			want:    &pb.GrantLiveMissionAwardResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelLiveMissionServer{
				CacheClient: tt.fields.CacheClient,
			}
			got, err := s.GrantLiveMissionAward(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GrantLiveMissionAward() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GrantLiveMissionAward() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelLiveMissionServer_GetFansMissionConfMap(t *testing.T) {
	type fields struct {
		MissionConf conf.IMissionConfManager
		CacheClient cache.IChannelLiveMissionCache
	}
	type args struct {
		anchorUid uint32
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockIChannelLiveMissionCache(ctl)
	mockConf := mocks.NewMockIMissionConfManager(ctl)

	info := &pb.MissionAwardInfo{
		MissionType: 3,
		AnchorUid:   anchorUid,
		AwardConfId: 1,
		BeginTs:     0,
		EndTs:       uint32(time.Now().AddDate(0, 0, 1).Unix()),
	}

	value, _ := json.Marshal(info)

	gomock.InOrder(
		mockConf.EXPECT().GetFansMissionConfByConfId(gomock.Any()).Return(map[uint32]*conf.FansMissionConf{
			101: {
				MissionId:   101,
				MissionType: 3,
				AwardNum:    10,
				ConfId:      1,
			},
		}),
		mockCache.EXPECT().GetAnchorMissionAwardList(gomock.Any()).Return(map[string]string{
			"2212797": string(value),
		}, nil),
	)

	tests := []struct {
		name   string
		fields fields
		args   args
		want   map[uint32]*conf.FansMissionConf
	}{
		{
			name: "GetFansMissionConfMap",
			fields: fields{
				MissionConf: mockConf,
				CacheClient: mockCache,
			},
			args: args{anchorUid: anchorUid},
			want: map[uint32]*conf.FansMissionConf{101: &conf.FansMissionConf{
				ValidTime:     "",
				MissionId:     101,
				MissionType:   3,
				MissionName:   "",
				MissionDesc:   "",
				IconUrl:       "",
				OperType:      0,
				GoalFinishCnt: 0,
				AwardNum:      10,
				PushMsg:       "",
				ConfId:        1,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelLiveMissionServer{
				MissionConf: tt.fields.MissionConf,
				CacheClient: tt.fields.CacheClient,
			}
			if got := s.GetFansMissionConfMap(tt.args.anchorUid); !reflect.DeepEqual(got, tt.want) {
				fmt.Println(got)
				t.Errorf("GetFansMissionConfMap() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelLiveMissionServer_GetFansMission(t *testing.T) {
	type fields struct {
		MissionConf conf.IMissionConfManager
		MongoDao    mongo.IMongoDao
		CacheClient cache.IChannelLiveMissionCache
	}
	type args struct {
		ctx context.Context
		in  *pb.GetFansMissionReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockIChannelLiveMissionCache(ctl)
	mockMongo := mocks.NewMockIMongoDao(ctl)
	mockConf := mocks.NewMockIMissionConfManager(ctl)

	mockKnightCli := knightmemberMock.NewMockIClient(ctl)

	ctx := context.Background()

	info := &pb.MissionAwardInfo{
		MissionType: 3,
		AnchorUid:   anchorUid,
		AwardConfId: 1,
		BeginTs:     0,
		EndTs:       uint32(time.Now().AddDate(0, 0, 1).Unix()),
	}

	value, _ := json.Marshal(info)

	gomock.InOrder(
		mockKnightCli.EXPECT().GetKnightInfo(gomock.Any(), gomock.Any()).Return(nil, nil),
		mockCache.EXPECT().GetFansMissions(gomock.Any(), gomock.Any()).Return(make(map[uint32]*cache.MissionCacheInfo), nil),
		mockMongo.EXPECT().GetFansMissionList(gomock.Any(), gomock.Any()).Return(map[uint32]*mongo.FansMission{
			201: &mongo.FansMission{
				Uid:        fansUid,
				ActorUid:   anchorUid,
				MissionId:  201,
				Status:     1,
				FinishCnt:  1,
				UpdateTime: updateTm,
			},
		}, nil),
		mockCache.EXPECT().BatchUpdateFansMission(gomock.Any(), gomock.Any(), gomock.Any()),
		mockConf.EXPECT().GetFansMissionConfByConfId(gomock.Any()).Return(map[uint32]*conf.FansMissionConf{
			101: {
				MissionId:   101,
				MissionType: 3,
				AwardNum:    10,
				ConfId:      1,
			},
		}),
		mockCache.EXPECT().GetAnchorMissionAwardList(gomock.Any()).Return(map[string]string{
			"2212797": string(value),
		}, nil),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetFansMissionResp
		wantErr bool
	}{
		{
			name: "GetFansMission",
			fields: fields{
				MissionConf: mockConf,
				MongoDao:    mockMongo,
				CacheClient: mockCache,
			},
			args: args{
				ctx: ctx,
				in: &pb.GetFansMissionReq{
					Uid:       fansUid,
					ActorUid:  anchorUid,
					ChannelId: 1,
				},
			},
			want: &pb.GetFansMissionResp{
				MissionList: []*pb.ChannelLiveMissionInfo{
					&pb.ChannelLiveMissionInfo{
						MissionId:   101,
						MissionType: 3,
						ActorUid:    2212797,
						AwardNum:    10,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelLiveMissionServer{
				MissionConf:     tt.fields.MissionConf,
				MongoDao:        tt.fields.MongoDao,
				CacheClient:     tt.fields.CacheClient,
				KnightMemberCli: mockKnightCli,
			}
			got, err := s.GetFansMission(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFansMission() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFansMission() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelLiveMissionServer_HandleFansMission(t *testing.T) {
	type fields struct {
		MissionConf    conf.IMissionConfManager
		MongoDao       mongo.IMongoDao
		CacheClient    cache.IChannelLiveMissionCache
		FansCli        *channellivefans.MockIClient
		PushCli        *push.MockIClient
		userProfileCLi *user_profile_api.MockIClient
		LiveMgrCli     *channellivemgr.MockIClient
	}
	type args struct {
		ctx context.Context
		in  *pb.HandleFansMissionReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockMongo := mocks.NewMockIMongoDao(ctl)
	mockCache := mocks.NewMockIChannelLiveMissionCache(ctl)
	mockConf := mocks.NewMockIMissionConfManager(ctl)
	mockFansCli := channellivefans.NewMockIClient(ctl)
	mockPush := push.NewMockIClient(ctl)
	mockUserProfile := user_profile_api.NewMockIClient(ctl)
	mockMgrCli := channellivemgr.NewMockIClient(ctl)
	mockKnightCli := knightmemberMock.NewMockIClient(ctl)

	ctx := context.Background()

	info := &pb.MissionAwardInfo{
		MissionType: 3,
		AnchorUid:   anchorUid,
		AwardConfId: 1,
		BeginTs:     0,
		EndTs:       uint32(time.Now().AddDate(0, 0, 1).Unix()),
	}

	value, _ := json.Marshal(info)

	gomock.InOrder(
		mockConf.EXPECT().GetFansMissionConfByConfId(gomock.Any()).Return(map[uint32]*conf.FansMissionConf{
			204: {
				MissionId:     204,
				MissionType:   3,
				AwardNum:      10,
				ConfId:        1,
				GoalFinishCnt: 1,
			},
		}),

		mockCache.EXPECT().GetAnchorMissionAwardList(gomock.Any()).Return(map[string]string{
			"2212797": string(value),
		}, nil),
		mockCache.EXPECT().GetFansMissionById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&cache.MissionCacheInfo{
			MissionId:  204,
			Status:     0,
			FinishCnt:  1,
			UpdateTime: time.Now().AddDate(0, 0, -2),
		}, nil),

		mockMgrCli.EXPECT().GetChannelLiveHistoryRecord(gomock.Any(), gomock.Any()).Return(&liveMgrPb.GetChannelLiveHistoryRecordResp{
			RecordList: []*liveMgrPb.ChannelLiveHistoryRecord{
				&liveMgrPb.ChannelLiveHistoryRecord{
					Uid:       anchorUid,
					ChannelId: 111,
					BeginTime: 0,
					EndTime:   10,
				},
			},
		}, nil),
		mockMongo.EXPECT().ResetFansMissionStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
		mockCache.EXPECT().UpdateFansMission(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
		mockConf.EXPECT().GetFansMissionConfByConfId(gomock.Any()).Return(map[uint32]*conf.FansMissionConf{
			204: {
				MissionId:     204,
				MissionType:   3,
				AwardNum:      10,
				ConfId:        1,
				GoalFinishCnt: 1,
			},
		}),
		mockCache.EXPECT().GetAnchorMissionAwardList(gomock.Any()).Return(map[string]string{
			"2212797": string(value),
		}, nil),
		mockKnightCli.EXPECT().GetKnightInfo(gomock.Any(), gomock.Any()).Return(nil, nil),
		mockCache.EXPECT().AddFansMissionDailyWatchMinutes(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(10), nil),
		mockCache.EXPECT().ResetFansMissionDailyWatchMinutes(gomock.Any(), gomock.Any(), gomock.Any()),
		mockMongo.EXPECT().IncrFansMissionFinishCnt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&mongo.FansMission{
			Uid:        fansUid,
			ActorUid:   anchorUid,
			MissionId:  204,
			Status:     1,
			FinishCnt:  4,
			UpdateTime: time.Now(),
		}, nil),
		mockMongo.EXPECT().UpdateFansMissionStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
		mockMongo.EXPECT().IncrFansMissionFinishUserCnt(gomock.Any(), gomock.Any()),
		mockCache.EXPECT().ClearFansMission(gomock.Any(), gomock.Any(), gomock.Any()),
		//mockFansCli.EXPECT().AddFansLoveValue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
		//mockUserProfile.EXPECT().GetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
		//mockPush.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.HandleFansMissionResp
		wantErr bool
	}{
		{
			name: "HandleFansMission",
			fields: fields{
				MissionConf:    mockConf,
				MongoDao:       mockMongo,
				CacheClient:    mockCache,
				FansCli:        mockFansCli,
				PushCli:        mockPush,
				userProfileCLi: mockUserProfile,
				LiveMgrCli:     mockMgrCli,
			},
			args: args{
				ctx: ctx,
				in: &pb.HandleFansMissionReq{
					Uid:           fansUid,
					ActorUid:      anchorUid,
					ChannelId:     111,
					MissionId:     204,
					IncrFinishCnt: 1,
				},
			},
			want:    &pb.HandleFansMissionResp{Status: 1},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelLiveMissionServer{
				MissionConf:     tt.fields.MissionConf,
				MongoDao:        tt.fields.MongoDao,
				CacheClient:     tt.fields.CacheClient,
				FansCli:         tt.fields.FansCli,
				PushCli:         tt.fields.PushCli,
				userProfileCLi:  tt.fields.userProfileCLi,
				LiveMgrCli:      tt.fields.LiveMgrCli,
				KnightMemberCli: mockKnightCli,
			}
			got, err := s.HandleFansMission(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("HandleFansMission() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("HandleFansMission() got = %v, want %v", got, tt.want)
			}
		})
	}

}

/*
func TestChannelLiveMissionServer_IncrActorLiveTimeCnt(t *testing.T) {
	type fields struct {
		MissionConf  conf.IMissionConfManager
		MongoDao     mongo.IMongoDao
		CacheClient  cache.IChannelLiveMissionCache
		BackpackCli  *backpack.Client
		PushCli      *push.MockIClient
		userOlCli    *userol.Client
		LiveStatsCli *channellivestats.MockIClient
	}
	type args struct {
		ctx context.Context
		in  *pb.IncrActorLiveTimeCntReq
	}

	monkey.Patch(time.Now, func() time.Time {
		return updateTm
	})

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockMongo := mocks.NewMockIMongoDao(ctl)
	mockCache := mocks.NewMockIChannelLiveMissionCache(ctl)
	mockConf := mocks.NewMockIMissionConfManager(ctl)
	//mockPush := push.NewMockIClient(ctl)
	mockLiveStats := channellivestats.NewMockIClient(ctl)

	gomock.InOrder(
		mockCache.EXPECT().GetActorLiveRecordTime(gomock.Any(), gomock.Any()).Return(uint32(updateTm.Unix()-10), nil),
		mockMongo.EXPECT().IncrDayActorLiveTimeCnt(gomock.Any(), gomock.Any()),
		mockCache.EXPECT().UpdateActorLiveRecordTime(gomock.Any(), gomock.Any(), gomock.Any()),
		mockMongo.EXPECT().GetUserThisDayActorRecord(gomock.Any()).Return(uint64(10), uint32(4200), nil).AnyTimes(),
		mockMongo.EXPECT().GetUserThisWeekActorRecord(gomock.Any()).Return(uint64(100), uint32(4200), updateTm.Unix()-7200, nil).AnyTimes(),
		mockMongo.EXPECT().IncrWeekActorLiveValidDays(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
		mockConf.EXPECT().GetActorMissionConf().Return(&conf.ActorMissionConf{}).AnyTimes(),
		mockConf.EXPECT().GetActorMissionConf().Return(&conf.ActorMissionConf{}).AnyTimes(),
		mockConf.EXPECT().GetActorMissionConf().Return(&conf.ActorMissionConf{}).AnyTimes(),
		mockCache.EXPECT().GetActorMissionCurrLevel(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
		mockCache.EXPECT().GetActorMissionCurrLevel(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
		mockCache.EXPECT().GetActorMissionCurrLevel(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
	)

	ctx := context.Background()

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.IncrActorLiveTimeCntResp
		wantErr bool
	}{
		{
			name: "IncrActorLiveTimeCnt",
			fields: fields{
				MissionConf:  mockConf,
				MongoDao:     mockMongo,
				CacheClient:  mockCache,
				BackpackCli:  nil,
				PushCli:      nil,
				userOlCli:    nil,
				LiveStatsCli: mockLiveStats,
			},
			args: args{
				ctx: ctx,
				in: &pb.IncrActorLiveTimeCntReq{
					Uid:           anchorUid,
					ChannelId:     123,
					ChannelLiveId: 123,
					IsLiving:      true,
				},
			},
			want:    &pb.IncrActorLiveTimeCntResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelLiveMissionServer{
				MissionConf:  tt.fields.MissionConf,
				MongoDao:     tt.fields.MongoDao,
				CacheClient:  tt.fields.CacheClient,
				BackpackCli:  tt.fields.BackpackCli,
				PushCli:      tt.fields.PushCli,
				UserOlCli:    tt.fields.userOlCli,
				LiveStatsCli: tt.fields.LiveStatsCli,
			}
			got, err := s.IncrActorLiveTimeCnt(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("IncrActorLiveTimeCnt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("IncrActorLiveTimeCnt() got = %v, want %v", got, tt.want)
			}
		})
	}

}

func TestChannelLiveMissionServer_GetActorMission(t *testing.T) {
	type fields struct {
		Sc           conf.IServiceConfigT
		MissionConf  conf.IMissionConfManager
		MongoDao     mongo.IMongoDao
		CacheClient  cache.IChannelLiveMissionCache
		BackpackCli  *backpack.Client
		LiveStatsCli *channellivestats.MockIClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetActorMissionReq
	}

	monkey.Patch(time.Now, func() time.Time {
		return updateTm
	})

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockMongo := mocks.NewMockIMongoDao(ctl)
	mockCache := mocks.NewMockIChannelLiveMissionCache(ctl)
	mockConf := mocks.NewMockIMissionConfManager(ctl)
	mockLiveStats := channellivestats.NewMockIClient(ctl)

	gomock.InOrder(
		mockMongo.EXPECT().GetUserThisDayActorRecord(gomock.Any()).Return(uint64(100), uint32(10), nil),
		mockMongo.EXPECT().GetUserThisWeekActorRecord(gomock.Any()).Return(uint64(100), uint32(10), updateTm.Unix(), nil),
		mockLiveStats.EXPECT().GetAnchorDailyRecordWithDateList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
		mockConf.EXPECT().GetActorMissionConf().Return(&conf.ActorMissionConf{
			DayLiveIncomeList: []*conf.ActorDayMissionConf{&conf.ActorDayMissionConf{
				MissionName:  "111",
				MissionDesc:  "111",
				IncomeTBean:  1,
				LiveHours:    1,
				AwardScore:   1,
				AwardPercent: 1,
			},
			},
			DayLiveTimeLengthList: []*conf.ActorDayMissionConf{&conf.ActorDayMissionConf{
				MissionName:  "111",
				MissionDesc:  "111",
				IncomeTBean:  1,
				LiveHours:    1,
				AwardScore:   1,
				AwardPercent: 1,
			},
			},
			WeekLiveIncomeList: []*conf.ActorWeekMissionConf{
				&conf.ActorWeekMissionConf{
					MissionName:   "111",
					MissionDesc:   "111",
					IncomeTBean:   1,
					LiveValidDays: 1,
					AwardScore:    1,
					AwardPercent:  1,
				},
			},
			WeekLiveTimeLengthList: []*conf.ActorWeekMissionConf{
				&conf.ActorWeekMissionConf{
					MissionName:   "111",
					MissionDesc:   "111",
					IncomeTBean:   1,
					LiveValidDays: 1,
					AwardScore:    1,
					AwardPercent:  1,
				},
			},
		}).AnyTimes(),
		mockMongo.EXPECT().GetUserThisDayActorRecord(gomock.Any()).Return(uint64(10), uint32(4200), nil).AnyTimes(),
		mockMongo.EXPECT().GetUserThisWeekActorRecord(gomock.Any()).Return(uint64(100), uint32(4200), updateTm.Unix()-7200, nil).AnyTimes(),
		mockMongo.EXPECT().IncrWeekActorLiveValidDays(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
		mockConf.EXPECT().GetActorMissionConf().Return(&conf.ActorMissionConf{}).AnyTimes(),
		mockConf.EXPECT().GetActorMissionConf().Return(&conf.ActorMissionConf{}).AnyTimes(),
		mockConf.EXPECT().GetActorMissionConf().Return(&conf.ActorMissionConf{}).AnyTimes(),
		mockCache.EXPECT().GetActorMissionCurrLevel(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
		mockCache.EXPECT().GetActorMissionCurrLevel(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
		mockCache.EXPECT().GetActorMissionCurrLevel(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
	)

	ctx := context.Background()
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetActorMissionResp
		wantErr bool
	}{
		{
			name: "GetActorMission",
			fields: fields{
				MissionConf:  mockConf,
				MongoDao:     mockMongo,
				CacheClient:  mockCache,
				LiveStatsCli: mockLiveStats,
			},
			args: args{
				ctx: ctx,
				in: &pb.GetActorMissionReq{
					Uid:       anchorUid,
					ChannelId: 123,
				},
			},
			want: &pb.GetActorMissionResp{
				MissionList: []*pb.ActorMissionInfo{
					{
						MissionName:  "111",
						MissionLevel: 1,
						AwardDesc:    "111",
						MissionType:  2,
						SubMissions: []*pb.ActorSubMissionInfo{
							{
								SubName:      "",
								ProgressDesc: "",
								Status:       0,
							},
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelLiveMissionServer{
				Sc:           tt.fields.Sc,
				MissionConf:  tt.fields.MissionConf,
				MongoDao:     tt.fields.MongoDao,
				CacheClient:  tt.fields.CacheClient,
				BackpackCli:  tt.fields.BackpackCli,
				LiveStatsCli: tt.fields.LiveStatsCli,
			}
			got, err := s.GetActorMission(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetActorMission() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				//t.Errorf("GetActorMission() got = %v, want %v", got, tt.want)
			}
		})
	}
}
*/

func TestChannelLiveMissionServer_SendActorMissionFinishMsg(t *testing.T) {
	type fields struct {
		Sc          conf.IServiceConfigT
		MissionConf conf.IMissionConfManager
		MongoDao    mongo.IMongoDao
		CacheClient cache.IChannelLiveMissionCache
		PushCli     *push.MockIClient
	}
	type args struct {
		ctx       context.Context
		uid       uint32
		channelId uint32
		text      string
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockPush := push.NewMockIClient(ctl)

	gomock.InOrder(
		mockPush.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()),
	)

	ctx := context.Background()

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "SendActorMissionFinishMsg",
			fields: fields{
				PushCli: mockPush,
			},
			args: args{
				ctx:       ctx,
				uid:       anchorUid,
				channelId: 123,
				text:      "ddd",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelLiveMissionServer{
				PushCli: tt.fields.PushCli,
			}
			if err := s.SendActorMissionFinishMsg(tt.args.ctx, tt.args.uid, tt.args.channelId, tt.args.text); (err != nil) != tt.wantErr {
				t.Errorf("SendActorMissionFinishMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestChannelLiveMissionServer_SwitchTimeUserMissionTs(t *testing.T) {
	type fields struct {
		MongoDao    mongo.IMongoDao
		CacheClient cache.IChannelLiveMissionCache
	}
	type args struct {
		ctx context.Context
		in  *pb.SwitchTimeUserMissionTsReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockMongo := mocks.NewMockIMongoDao(ctl)
	mockCache := mocks.NewMockIChannelLiveMissionCache(ctl)

	gomock.InOrder(
		mockCache.EXPECT().UpdateLiveChannelMemScore(gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().GetUserMissionById(gomock.Any(), gomock.Any()).Return(nil, nil),
		mockMongo.EXPECT().GetUserMissionById(gomock.Any(), gomock.Any()).Return(&mongo.UserMission{
			Id:         "11",
			Uid:        anchorUid,
			MissionId:  1,
			Status:     0,
			FinishCnt:  1,
			UpdateTime: updateTm,
		}, nil),
		mockMongo.EXPECT().ResetUserMissionStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().UpdateUserMission(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
		mockCache.EXPECT().UpdateLiveChannelMemScore(gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().GetUserMissionById(gomock.Any(), gomock.Any()).Return(nil, nil),
		mockMongo.EXPECT().GetUserMissionById(gomock.Any(), gomock.Any()).Return(&mongo.UserMission{
			Id:         "11",
			Uid:        anchorUid,
			MissionId:  1,
			Status:     0,
			FinishCnt:  1,
			UpdateTime: updateTm,
		}, nil),
		mockMongo.EXPECT().ResetUserMissionStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().UpdateUserMission(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
	)

	ctx := context.Background()
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.SwitchTimeUserMissionTsResp
		wantErr bool
	}{
		{
			name: "SwitchTimeUserMissionTs",
			fields: fields{
				MongoDao:    mockMongo,
				CacheClient: mockCache,
			},
			args: args{
				ctx: ctx,
				in: &pb.SwitchTimeUserMissionTsReq{
					Uid:    anchorUid,
					Switch: false,
				},
			},
			want:    &pb.SwitchTimeUserMissionTsResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelLiveMissionServer{
				MongoDao:    tt.fields.MongoDao,
				CacheClient: tt.fields.CacheClient,
			}
			got, err := s.SwitchTimeUserMissionTs(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("SwitchTimeUserMissionTs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SwitchTimeUserMissionTs() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelLiveMissionServer_HandleTimeUserMission(t *testing.T) {
	type fields struct {
		MissionConf conf.IMissionConfManager
		CacheClient cache.IChannelLiveMissionCache
	}
	type args struct {
		idx uint32
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockConf := mocks.NewMockIMissionConfManager(ctl)
	mockCache := mocks.NewMockIChannelLiveMissionCache(ctl)

	gomock.InOrder(
		mockCache.EXPECT().GetMissionLock(gomock.Any(), gomock.Any()).Return(true, nil),
		mockCache.EXPECT().GetLiveChannelMemScoreBeforeSecs(gomock.Any(), gomock.Any()).Return([]cache.MemScore{
			{
				Uid:   anchorUid,
				Score: 100,
			},
		}, nil),
		mockConf.EXPECT().CheckInUserMissionWhiteList(gomock.Any()).Return(false),
		mockConf.EXPECT().CheckInUserMissionWhiteList(gomock.Any()).Return(false),
		mockCache.EXPECT().UpdateLiveChannelMemScore(gomock.Any(), gomock.Any()),
		mockCache.EXPECT().ReleaseMissionLock(gomock.Any()).AnyTimes(),
	)

	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "HandleTimeUserMission",
			fields: fields{
				MissionConf: mockConf,
				CacheClient: mockCache,
			},
			args: args{idx: 1},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelLiveMissionServer{
				MissionConf: tt.fields.MissionConf,
				CacheClient: tt.fields.CacheClient,
			}
			s.HandleTimeUserMission(tt.args.idx)
		})
	}
}
