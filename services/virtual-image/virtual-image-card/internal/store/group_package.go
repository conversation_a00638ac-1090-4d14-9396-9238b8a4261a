package store

import (
    "context"
    "fmt"
    "github.com/jmoiron/sqlx"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    "google.golang.org/grpc/codes"
    "strings"
)

const groupPackageTableName = "tbl_virtual_image_card_group_package"

const createGroupPackageSQL = `CREATE TABLE IF NOT EXISTS %s (
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '分组id',
    name varchar(255) DEFAULT '' COMMENT '套餐名称',
    group_desc varchar(255) DEFAULT '' COMMENT '套餐描述',
    package_type tinyint(4) NOT NULL DEFAULT 0 COMMENT '套餐类型',
    is_enable tinyint(1) unsigned NOT NULL DEFAULT 1 COMMENT '套餐状态',
    original_price int(10) unsigned NOT NULL DEFAULT 0 COMMENT '套餐原价',
    price int(10) unsigned NOT NULL DEFAULT 0 COMMENT '套餐售价',
    days int(10) NOT NULL DEFAULT 0 COMMENT '套餐天数',
    operator varchar(255) NOT NULL DEFAULT '' COMMENT '操作人',
 	deleted  tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    ctime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    mtime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
 ) ENGINE = InnoDB DEFAULT CHARSET = utf8 COMMENT = '分组套餐表'
`

type GroupPackage struct {
    Id            uint32 `db:"id" json:"id"`
    Name          string `db:"name" json:"name"`
    GroupDesc     string `db:"group_desc" json:"group_desc"`
    PackageType   uint32 `db:"package_type" json:"package_type"`
    IsEnable      bool   `db:"is_enable" json:"is_enable"`
    OriginalPrice uint32 `db:"original_price" json:"original_price"`
    Price         uint32 `db:"price" json:"price"`
    Days          uint32 `db:"days" json:"days"`
    Operator      string `db:"operator" json:"operator"`
    Deleted       uint32 `db:"deleted" json:"deleted"`
    Ctime         string `db:"ctime" json:"ctime"`
    Mtime         string `db:"mtime" json:"mtime"`
}

type MarketInfo struct {
    MarketId  uint32 `db:"market_id"`
    ProductId string `db:"product_id"`
    PackageId uint32 `db:"id"`
}

// AddGroupWithNewPackage 批量创建套餐并加入同一新分组
func (s *Store) AddGroupWithNewPackage(ctx context.Context, groupPackage *GroupPackage, marketInfoList []*MarketInfo) error {
    if len(marketInfoList) == 0 {
        log.ErrorWithCtx(ctx, "market_list is empty, groupPackage: %+v", groupPackage)
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "market_list is empty")
    }
    var isSingle bool
    if len(marketInfoList) == 1 {
        isSingle = true
    }

    groupId := uint32(0)
    txErr := s.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
        if !isSingle {
            // 创建分组
            sql := fmt.Sprintf("INSERT INTO %s (name, group_desc, package_type, original_price, price, days, operator) VALUES (?, ?, ?, ?, ?, ?, ?)", groupPackageTableName)
            result, err := tx.ExecContext(ctx, sql, groupPackage.Name, groupPackage.GroupDesc, groupPackage.PackageType, groupPackage.OriginalPrice, groupPackage.Price, groupPackage.Days, groupPackage.Operator)
            if err != nil {
                log.ErrorWithCtx(ctx, "AddGroupWithNewPackage fail err:%v groupPackage:%+v", err, groupPackage)
                return err
            }
            gid, err := result.LastInsertId()
            if err != nil {
                log.ErrorWithCtx(ctx, "AddGroupWithNewPackage fail err:%v groupPackage:%+v", err, groupPackage)
                return err
            }
            groupId = uint32(gid)
        }
        // 无需特判，如果是单个市场id，groupId会等于0
        err := s.batchAddPackageV2WithGroupId(ctx, tx, groupId, groupPackage, marketInfoList)
        if err != nil {
            log.ErrorWithCtx(ctx, "AddGroupWithNewPackage fail err:%v groupPackage:%+v", err, groupPackage)
            return err
        }
        return nil
    })

    if txErr != nil {
        log.ErrorWithCtx(ctx, "AddGroupWithNewPackage fail err:%v groupPackage:%+v", txErr, groupPackage)
        return txErr
    }
    log.InfoWithCtx(ctx, "AddGroupWithNewPackage groupPackage:%+v, groupId:%d", groupPackage, groupId)
    return nil
}

func (s *Store) batchAddPackageV2WithGroupId(ctx context.Context, tx mysql.Txx, groupId uint32, groupPackage *GroupPackage, marketInfoList []*MarketInfo) error {
    sql := fmt.Sprintf("INSERT INTO %s (product_id, name, package_desc, original_price, price, package_type, days, operator, discount_price, market_id, group_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", packageTableName)
    for _, marketInfo := range marketInfoList {
        _, err := tx.ExecContext(ctx, sql, marketInfo.ProductId, groupPackage.Name, groupPackage.GroupDesc, groupPackage.OriginalPrice, groupPackage.Price, groupPackage.PackageType, groupPackage.Days, groupPackage.Operator, groupPackage.Price, marketInfo.MarketId, groupId)
        if err != nil {
            log.ErrorWithCtx(ctx, "addPackageV2 fail err:%v groupPackage:%+v", err, groupPackage)
            return err
        }
    }
    return nil
}

func (s *Store) GetGroupPackage(ctx context.Context, groupId uint32) (*GroupPackage, error) {
    sql := fmt.Sprintf("SELECT id, name, group_desc, package_type, is_enable, original_price, price, days, operator, ctime, mtime FROM %s WHERE id = ? and deleted = 0", groupPackageTableName)
    groupPackage := &GroupPackage{}
    err := s.db.GetContext(ctx, groupPackage, sql, groupId)
    if err != nil {
        if mysql.IsNoRowsError(err) {
            log.DebugWithCtx(ctx, "GetGroupPackage no data groupId:%d", groupId)
            return nil, nil
        }
        log.ErrorWithCtx(ctx, "GetGroupPackage fail err:%v groupId:%+v", err, groupId)
        return nil, err
    }
    log.DebugWithCtx(ctx, "GetGroupPackage success groupId:%d groupPackage:%+v", groupId, groupPackage)
    return groupPackage, nil
}

func (s *Store) SetGroupPackageStatus(ctx context.Context, groupId uint32, isEnable bool, operator string) error {
    txErr := s.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
        sql := fmt.Sprintf("UPDATE %s SET is_enable = ?, operator = ? WHERE id = ?", groupPackageTableName)
        _, err := tx.ExecContext(ctx, sql, isEnable, operator, groupId)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetGroupPackageStatus fail err:%v groupId:%d isEnable:%v", err, groupId, isEnable)
            return err
        }

        sql = fmt.Sprintf("UPDATE %s SET is_enable = ?, operator = ? WHERE group_id = ?", packageTableName)
        _, err = tx.ExecContext(ctx, sql, isEnable, operator, groupId)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetGroupPackageStatus fail err:%v groupId:%d isEnable:%v", err, groupId, isEnable)
            return err
        }
        return nil
    })
    if txErr != nil {
        log.ErrorWithCtx(ctx, "SetGroupPackageStatus fail err:%v groupId:%d isEnable:%v", txErr, groupId, isEnable)
        return txErr
    }
    log.InfoWithCtx(ctx, "SetGroupPackageStatus success groupId:%d isEnable:%v", groupId, isEnable)
    return nil
}

func (s *Store) GetPackageMarketInfosByGroupId(ctx context.Context, groupId uint32) ([]*MarketInfo, error) {
    sql := fmt.Sprintf("select id, market_id, product_id from %s where group_id = ?", packageTableName)
    marketInfos := make([]*MarketInfo, 0)
    err := s.db.SelectContext(ctx, &marketInfos, sql, groupId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPackageMarketInfosByGroupId groupId:%d, error: %v", groupId, err)
        return nil, err
    }
    return marketInfos, nil
}

// AddGroup 创建套餐分组，并把选中套餐添加到该分组
func (s *Store) AddGroup(ctx context.Context, info *GroupPackage, packageIds []uint32, operator string) error {
    if len(packageIds) == 0 {
        log.ErrorWithCtx(ctx, "AddGroup packageIds is empty, groupPackage: %+v", info)
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "packageIds is empty")
    }
    txErr := s.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
        sql := fmt.Sprintf("INSERT INTO %s (name, group_desc, original_price, price, package_type, days, is_enable, operator) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", groupPackageTableName)
        result, err := tx.ExecContext(ctx, sql, info.Name, info.GroupDesc, info.OriginalPrice, info.Price, info.PackageType, info.Days, info.IsEnable, operator)
        if err != nil {
            log.ErrorWithCtx(ctx, "AddGroup fail err:%v groupPackage:%+v", err, info)
            return err
        }
        gid, err := result.LastInsertId()
        if err != nil {
            log.ErrorWithCtx(ctx, "AddGroup fail err:%v groupPackage:%+v", err, info)
            return err
        }
        groupId := uint32(gid)
        sql = fmt.Sprintf("update %s set group_id = ?, operator = ? where id in (?)", packageTableName)
        query, args, err := sqlx.In(sql, groupId, info.Operator, packageIds)
        if err != nil {
            log.ErrorWithCtx(ctx, "AddGroup sql:%s error: %v, packageIds:%v", sql, err, packageIds)
            return err
        }
        _, err = tx.ExecContext(ctx, query, args...)
        if err != nil {
            log.ErrorWithCtx(ctx, "AddGroup fail err:%v groupPackage:%+v, packageIds:%v", err, info, packageIds)
            return err
        }
        info.Id = groupId
        return nil
    })
    if txErr != nil {
        log.ErrorWithCtx(ctx, "AddGroup fail err:%v groupPackage:%+v, packageIds:%v", txErr, info, packageIds)
        return txErr
    }

    log.InfoWithCtx(ctx, "AddGroup success groupPackage:%+v, packageIds:%v", info, packageIds)
    return nil
}

func (s *Store) ModifyGroupPackage(ctx context.Context, info *GroupPackage) error {
    txErr := s.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
        sql := fmt.Sprintf("UPDATE %s SET name = ?, group_desc = ?, original_price = ?, price = ?, operator = ? WHERE id = ?", groupPackageTableName)
        _, err := tx.ExecContext(ctx, sql, info.Name, info.GroupDesc, info.OriginalPrice, info.Price, info.Operator, info.Id)
        if err != nil {
            log.ErrorWithCtx(ctx, "ModifyGroupPackage fail err:%v groupPackage:%+v", err, info)
            return err
        }

        sql = fmt.Sprintf("UPDATE %s SET name = ?, package_desc = ?, original_price = ?, price = ?, operator = ? WHERE group_id = ?", packageTableName)
        _, err = tx.ExecContext(ctx, sql, info.Name, info.GroupDesc, info.OriginalPrice, info.Price, info.Operator, info.Id)
        if err != nil {
            log.ErrorWithCtx(ctx, "ModifyGroupPackage fail err:%v groupPackage:%+v", err, info)
            return err
        }
        return nil
    })

    if txErr != nil {
        log.ErrorWithCtx(ctx, "ModifyGroupPackage fail err:%v groupPackage:%+v", txErr, info)
        return txErr
    }
    log.InfoWithCtx(ctx, "ModifyGroupPackage success groupPackage:%+v", info)
    return nil
}

func (s *Store) BatchGetGroupByKeyword(ctx context.Context, pkgType, status uint32, keyword string) ([]*GroupPackage, error) {
    sql := fmt.Sprintf("select id, name, group_desc, original_price, price, package_type, days, is_enable, operator from %s where package_type = ? and deleted = 0 and is_enable = ?", groupPackageTableName)
    if keyword != "" {
        keyword = strings.TrimSpace(keyword) // 去除首尾空格造成的影响
        sql += " and (name like '%" + keyword + "%' or id like '%" + keyword + "%')"
    }
    groupPackages := make([]*GroupPackage, 0)
    err := s.db.SelectContext(ctx, &groupPackages, sql, pkgType, status)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetGroupByKeyword fail err:%v keyword:%s, pkgType:%d, status:%d", err, keyword, pkgType, status)
        return nil, err
    }
    log.DebugWithCtx(ctx, "BatchGetGroupByKeyword success keyword:%s groupPackages:%+v, pkgType:%d, status:%d", keyword, groupPackages, pkgType, status)
    return groupPackages, nil
}

func (s *Store) EditGroupMember(ctx context.Context, groupId uint32, needAddPkgIds []uint32, needDelPkgIds []uint32, operator string) error {
    if len(needAddPkgIds) == 0 && len(needDelPkgIds) == 0 {
        log.DebugWithCtx(ctx, "EditGroupMember no data groupId:%d, needAddPkgIds:%v, needDelPkgIds:%v", groupId, needAddPkgIds, needDelPkgIds)
        return nil
    }

    txErr := s.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
        if len(needAddPkgIds) > 0 {
            sql := fmt.Sprintf("update %s set group_id = ?, operator = ? where id in (?)", packageTableName)
            query, args, err := sqlx.In(sql, groupId, operator, needAddPkgIds)
            if err != nil {
                log.ErrorWithCtx(ctx, "EditGroupMember sql:%s error: %v, needAddPkgIds:%v", sql, err, needAddPkgIds)
                return err
            }
            _, err = tx.ExecContext(ctx, query, args...)
            if err != nil {
                log.ErrorWithCtx(ctx, "EditGroupMember fail err:%v groupId:%d, needAddPkgIds:%v", err, groupId, needAddPkgIds)
                return err
            }
        }

        if len(needDelPkgIds) > 0 {
            sql := fmt.Sprintf("update %s set group_id = 0, operator = ? where id in (?)", packageTableName)
            query, args, err := sqlx.In(sql, operator, needDelPkgIds)
            if err != nil {
                log.ErrorWithCtx(ctx, "EditGroupMember sql:%s error: %v, needDelPkgIds:%v", sql, err, needDelPkgIds)
                return err
            }
            _, err = tx.ExecContext(ctx, query, args...)
            if err != nil {
                log.ErrorWithCtx(ctx, "EditGroupMember fail err:%v groupId:%d, needDelPkgIds:%v", err, groupId, needDelPkgIds)
                return err
            }
        }
        return nil
    })
    if txErr != nil {
        log.ErrorWithCtx(ctx, "EditGroupMember fail err:%v groupId:%d, needAddPkgIds:%v, needDelPkgIds:%v", txErr, groupId, needAddPkgIds, needDelPkgIds)
        return txErr
    }
    log.InfoWithCtx(ctx, "EditGroupMember success groupId:%d, needAddPkgIds:%v, needDelPkgIds:%v", groupId, needAddPkgIds, needDelPkgIds)
    return nil
}

func (s *Store) GetPackageIdsByGroupIds(ctx context.Context, groupIds []uint32) (map[uint32][]*MarketInfo, error) {
    sql := fmt.Sprintf("select id, group_id, market_id, product_id from %s where group_id in (?)", packageTableName)
    query, args, err := sqlx.In(sql, groupIds)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPackageIdsByGroupIds sql:%s error: %v, groupIds:%v", sql, err, groupIds)
        return nil, err
    }
    marketInfos := make([]*Package, 0)
    err = s.db.SelectContext(ctx, &marketInfos, query, args...)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPackageIdsByGroupIds fail err:%v groupIds:%v", err, groupIds)
        return nil, err
    }
    result := make(map[uint32][]*MarketInfo)
    for _, info := range marketInfos {
        if info.GroupId == 0 {
            continue
        }
        result[info.GroupId] = append(result[info.GroupId], &MarketInfo{
            MarketId:  info.MarketId,
            PackageId: info.Id,
            ProductId: info.ProductId,
        })
    }
    return result, nil
}

func (s *Store) DisbandGroup(ctx context.Context, groupId uint32, operator string) error {
    txErr := s.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
        sql := fmt.Sprintf("update %s set deleted = 1, operator = ? where id = ?", groupPackageTableName)
        _, err := tx.ExecContext(ctx, sql, operator, groupId)
        if err != nil {
            log.ErrorWithCtx(ctx, "DisbandGroup fail err:%v groupId:%v", err, groupId)
            return err
        }

        sql = fmt.Sprintf("update %s set group_id = 0, operator = ? where group_id = ?", packageTableName)
        _, err = tx.ExecContext(ctx, sql, operator, groupId)
        if err != nil {
            log.ErrorWithCtx(ctx, "DisbandGroup fail err:%v groupId:%d", err, groupId)
            return err
        }

        return nil
    })
    if txErr != nil {
        log.ErrorWithCtx(ctx, "DisbandGroup fail err:%v groupId:%d", txErr, groupId)
        return txErr
    }
    log.InfoWithCtx(ctx, "DisbandGroup success groupId:%d", groupId)
    return nil
}
