package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/pkg/reporter"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
	pb "golang.52tt.com/protocol/services/virtual-image-mall"
	dynamicConfig "golang.52tt.com/services/virtual-image/virtual-image-mall/internal/conf"
	"golang.52tt.com/services/virtual-image/virtual-image-mall/internal/mgr"
)

func NewServer(ctx context.Context, cfg *dynamicConfig.StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	s := &Server{}

	// 飞书告警
	feishu_ := reporter.NewFeishuReporter(cfg.WarnFeishuUrl, "")
	s.feishu = feishu_

	// 全局配置文件，模块内使用模块接口
	dyConf_ := dynamicConfig.NewConfigHandler("")

	mgr, err := mgr.NewVirtualImageMallMgr(ctx, cfg, dyConf_)
	if err != nil {
		return nil, err
	}
	s.mgr = mgr

	s.dyConf = dyConf_
	return s, nil
}

type Server struct {
	feishu *reporter.FeishuReporter
	dyConf *dynamicConfig.SDyConfigHandler
	mgr    mgr.IVirtualImageMallMgr
}

func (s *Server) GetConsumeTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	return s.mgr.GetConsumeTotalCount(ctx, req)
}

func (s *Server) GetConsumeOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	return s.mgr.GetConsumeOrderIds(ctx, req)
}

func (s *Server) ShutDown() {
}

func (s *Server) GetCommodityDataList(ctx context.Context, req *pb.GetCommodityDataListRequest) (*pb.GetCommodityDataListResponse, error) {
	log.InfoWithCtx(ctx, "GetCommodityDataList: %v", req)
	out, err := s.mgr.GetCommodityDataList(ctx, req)
	if err != nil {
		return out, err
	}
	log.InfoWithCtx(ctx, "GetCommodityDataList: req:%v, CommodityDataList size:%v", req, len(out.GetCommodityDataList()))
	return out, nil
}

func (s *Server) GetCommodityRecommendList(ctx context.Context, req *pb.GetCommodityRecommendListRequest) (*pb.GetCommodityRecommendListResponse, error) {
	log.DebugWithCtx(ctx, "GetCommodityRecommendList: %v", req)
	out, err := s.mgr.GetCommodityRecommendList(ctx, req)
	if err != nil {
		return out, err
	}
	log.DebugWithCtx(ctx, "GetCommodityRecommendList: req:%v, CommodityId:%v", req, out.GetRecList())
	return out, nil
}

func (s *Server) GetCommodityDataListByPrice(ctx context.Context, req *pb.GetCommodityDataListByPriceRequest) (*pb.GetCommodityDataListByPriceResponse, error) {
	log.DebugWithCtx(ctx, "GetCommodityDataListByPrice: %v", req)
	out, err := s.mgr.GetCommodityDataListByPrice(ctx, req)
	if err != nil {
		return out, err
	}
	log.DebugWithCtx(ctx, "GetCommodityDataListByPrice: req:%v, resp:%v", req, out)
	return out, nil
}

func (s *Server) BatAddCommodity(ctx context.Context, req *pb.BatAddCommodityReq) (*pb.BatAddCommodityResp, error) {
	log.DebugWithCtx(ctx, "BatAddCommodity: %v", req)
	out, err := s.mgr.BatAddCommodity(ctx, req)
	log.DebugWithCtx(ctx, "BatAddCommodity: req:%v, out:%v", req, out)
	return out, err
}
func (s *Server) UpdateCommodity(ctx context.Context, req *pb.UpdateCommodityReq) (*pb.UpdateCommodityResp, error) {
	log.DebugWithCtx(ctx, "UpdateCommodity: %v", req)
	out, err := s.mgr.UpdateCommodity(ctx, req)
	log.DebugWithCtx(ctx, "UpdateCommodity: req:%v, out:%v", req, out)
	return out, err
}

func (s *Server) BatAddCommodityRecommend(ctx context.Context, req *pb.BatAddCommodityRecommendReq) (*pb.BatAddCommodityRecommendResp, error) {
	out, err := s.mgr.BatAddCommodityRecommend(ctx, req)
	return out, err
}

func (s *Server) UpdateCommodityRecommend(ctx context.Context, req *pb.UpdateCommodityRecommendReq) (*pb.UpdateCommodityRecommendResp, error) {
	out, err := s.mgr.UpdateCommodityRecommend(ctx, req)
	return out, err
}

func (s *Server) DelCommodityRecommend(ctx context.Context, req *pb.DelCommodityRecommendReq) (*pb.DelCommodityRecommendResp, error) {
	out, err := s.mgr.DelCommodityRecommend(ctx, req)
	return out, err
}

func (s *Server) BuyCommodityData(ctx context.Context, req *pb.BuyCommodityDataRequest) (*pb.BuyCommodityDataResponse, error) {
	log.DebugWithCtx(ctx, "BuyCommodityData: %v", req)
	//out := &pb.BuyCommodityDataResponse{}
	out, err := s.mgr.BuyCommodityData(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BuyCommodityData req:%v err:%v", req, err)
		return out, err
	}
	log.DebugWithCtx(ctx, "BuyCommodityData: req:%v, out:%v", req, out)
	return out, nil
}

func (s *Server) UpdateCommodityDataOrdersStatus(ctx context.Context, req *pb.UpdateCommodityDataOrdersStatusRequest) (*pb.UpdateCommodityDataOrdersStatusResponse, error) {
	log.DebugWithCtx(ctx, "UpdateCommodityDataOrdersStatus: %v", req)
	//out := &pb.UpdateCommodityDataOrdersStatusResponse{}
	out, err := s.mgr.UpdateCommodityDataOrdersStatus(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateCommodityDataOrdersStatus req:%v err:%v", req, err)
		return out, err
	}
	log.DebugWithCtx(ctx, "UpdateCommodityDataOrdersStatus: req:%v, out:%v", req, out)
	return out, nil
}

func (s *Server) GetCommodityDataOrdersPaying(ctx context.Context, req *pb.GetCommodityDataOrdersPayingRequest) (*pb.GetCommodityDataOrdersPayingResponse, error) {
	log.DebugWithCtx(ctx, "GetCommodityDataOrdersPaying: %v", req)
	//out := &pb.GetCommodityDataOrdersPayingResponse{}
	out, err := s.mgr.GetCommodityDataOrdersPaying(ctx, req)
	if err != nil {
		return out, err
	}
	log.DebugWithCtx(ctx, "GetCommodityDataOrdersPaying: req:%v, out:%v", req, out)
	return out, nil
}

func (s *Server) AddShoppingCar(ctx context.Context, req *pb.AddShoppingCarRequest) (*pb.AddShoppingCarResponse, error) {
	log.DebugWithCtx(ctx, "AddShoppingCar: %v", req)
	out := &pb.AddShoppingCarResponse{}
	log.DebugWithCtx(ctx, "AddShoppingCar: req:%v, out:%v", req, out)
	return out, nil
}

func (s *Server) DelShoppingCar(ctx context.Context, req *pb.DelShoppingCarRequest) (*pb.DelShoppingCarResponse, error) {
	log.DebugWithCtx(ctx, "DelShoppingCar: %v", req)
	out := &pb.DelShoppingCarResponse{}
	log.DebugWithCtx(ctx, "DelShoppingCar: req:%v, out:%v", req, out)
	return out, nil
}

func (s *Server) BathGetShoppingCar(ctx context.Context, req *pb.BathGetShoppingCarRequest) (*pb.BathGetShoppingCarResponse, error) {
	log.DebugWithCtx(ctx, "BathGetShoppingCar: %v", req)
	out := &pb.BathGetShoppingCarResponse{}
	log.DebugWithCtx(ctx, "BathGetShoppingCar: req:%v, out:%v", req, out)
	return out, nil
}

func (s *Server) BatchGetCommodityDataOrders(ctx context.Context, req *pb.BatchGetCommodityDataOrdersRequest) (*pb.BatchGetCommodityDataOrdersResponse, error) {
	log.DebugWithCtx(ctx, "BatchGetCommodityDataOrders: %v", req)
	//out := &pb.BatchGetCommodityDataOrdersResponse{}
	out, err := s.mgr.BatchGetCommodityDataOrders(ctx, req)
	if err != nil {
		return out, err
	}
	log.DebugWithCtx(ctx, "BatchGetCommodityDataOrders: req:%v, out:%v", req, out)
	return out, nil
}

func (s *Server) Notify(ctx context.Context, req *UnifiedPayCallback.PayNotify) (*UnifiedPayCallback.PayNotifyResponse, error) {
	log.InfoWithCtx(ctx, "Notify req=%+v", req)
	out := &UnifiedPayCallback.PayNotifyResponse{}
	op, err := s.mgr.Callback(ctx, req.GetOutTradeNo())
	if err != nil {
		log.ErrorWithCtx(ctx, "Notify fail to Callback. in:%+v, err:%v", req, err)
		return out, err
	}

	out.Confirmed = true
	out.Op = op

	return out, nil
}

func (s *Server) UpdateCommodityResource(ctx context.Context, req *pb.UpdateCommodityResourceReq) (*pb.UpdateCommodityResourceResp, error) {
	log.DebugWithCtx(ctx, "UpdateCommodityResource: %v", req)
	out, err := s.mgr.UpdateCommodityResource(ctx, req)
	if err != nil {
		return out, err
	}
	log.DebugWithCtx(ctx, "UpdateCommodityResource: req:%v, out:%v", req, out)
	return out, nil
}

func (s *Server) UpdateCommodityPackageRedDot(ctx context.Context, req *pb.UpdateCommodityPackageRedDotReq) (*pb.UpdateCommodityPackageRedDotResp, error) {
	log.DebugWithCtx(ctx, "UpdateCommodityPackageRedDot: %v", req)
	out, err := s.mgr.UpdateCommodityPackageRedDot(ctx, req)
	if err != nil {
		return out, err
	}
	log.DebugWithCtx(ctx, "UpdateCommodityPackageRedDot: req:%v, out:%v", req, out)
	return out, nil
}

func (s *Server) GetUnRefreshedRedDotPackageData(ctx context.Context, req *pb.GetUnRefreshedRedDotPackageDataReq) (*pb.GetUnRefreshedRedDotPackageDataResp, error) {
	log.DebugWithCtx(ctx, "GetUnRefreshedRedDotPackageData: %v", req)
	out, err := s.mgr.GetUnRefreshedRedDotPackageData(ctx, req)
	if err != nil {
		return out, err
	}
	log.DebugWithCtx(ctx, "GetUnRefreshedRedDotPackageData: req:%v, out:%v", req, out)
	return out, nil
}

func (s *Server) CommodityUserReadRedDot(ctx context.Context, req *pb.CommodityUserReadRedDotReq) (*pb.CommodityUserReadRedDotResp, error) {
	log.DebugWithCtx(ctx, "CommodityUserReadRedDot: %v", req)
	out, err := s.mgr.CommodityUserReadRedDot(ctx, req)
	if err != nil {
		return out, err
	}
	log.DebugWithCtx(ctx, "CommodityUserReadRedDot: req:%v, out:%v", req, out)
	return out, nil
}

func (s *Server) GetUserUnReadCommodityRedDot(ctx context.Context, req *pb.GetUserUnReadCommodityRedDotReq) (*pb.GetUserUnReadCommodityRedDotResp, error) {
	log.DebugWithCtx(ctx, "GetUserUnReadCommodityRedDot: %v", req)
	out, err := s.mgr.GetUserUnReadCommodityRedDot(ctx, req)
	if err != nil {
		return out, err
	}
	log.DebugWithCtx(ctx, "GetUserUnReadCommodityRedDot: req:%v, out:%v", req, out)
	return out, nil

}

func (s *Server) BatchGetCommodityRedDot(ctx context.Context, req *pb.BatchGetCommodityRedDotReq) (*pb.BatchGetCommodityRedDotResp, error) {
	log.DebugWithCtx(ctx, "BatchGetCommodityRedDot: %v", req)
	out, err := s.mgr.BatchGetCommodityRedDot(ctx, req)
	if err != nil {
		return out, err
	}
	log.DebugWithCtx(ctx, "BatchGetCommodityRedDot: req:%v, out:%v", req, out)
	return out, nil
}

func (s *Server) DelCommodityData(ctx context.Context, req *pb.DelCommodityDataReq) (*pb.DelCommodityDataResp, error) {
	log.DebugWithCtx(ctx, "DelCommodityData: %v", req)
	out, err := s.mgr.DelCommodityData(ctx, req)
	if err != nil {
		return out, err
	}
	log.DebugWithCtx(ctx, "DelCommodityData: req:%v, out:%v", req, out)
	return out, nil
}

func (s *Server) GetOrderDataByTimeRange(c context.Context, request *pb.GetOrderDataByTimeRangeRequest) (*pb.GetOrderDataByTimeRangeResponse, error) {
	log.InfoWithCtx(c, "GetOrderDataByTimeRange: %v", request)

	return s.mgr.GetOrderDataByTimeRange(c, request)
}
