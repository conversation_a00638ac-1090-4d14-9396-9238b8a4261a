package manager

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/clients/account"
	push "golang.52tt.com/clients/push-notification/v2"
	channelMsg "golang.52tt.com/pkg/channel-msg"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/ttversion"
	"golang.52tt.com/protocol/app"
	channelGA "golang.52tt.com/protocol/app/channel"
	numericlogic "golang.52tt.com/protocol/app/numeric-logic"
	gaPush "golang.52tt.com/protocol/app/push"
	pb "golang.52tt.com/protocol/app/virtual_image_logic"
	"golang.52tt.com/protocol/common/status"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
	mallPb "golang.52tt.com/protocol/services/virtual-image-mall"
	virtualimageresource "golang.52tt.com/protocol/services/virtual-image-resource"
	userPb "golang.52tt.com/protocol/services/virtual-image-user"
	"golang.52tt.com/services/tt-rev/common/goroutineex"
	"golang.52tt.com/services/virtual-image/virtual-image-logic/util"
	"math/rand"
	"os"
	"sort"
	"strconv"
	"time"

	"google.golang.org/grpc/codes"
)

var (
	cardRightsCommodityMinVer = ttversion.Parse("无限换装卡类商品最低支持版本", "android-6.66.0", "ios-6.66.0", "pc-2.8.5")
)

type UserEventTime struct {
	Uid       string `json:"uid"`
	EventTime string `json:"event_time"`
}

type UserEventData struct {
	Code uint32         `json:"code"`
	Msg  string         `json:"msg"`
	Data *UserEventTime `json:"data"`
}

func (m *VirtualImageLogicMgr) CheckFaceAuthInfo(ctx context.Context, uid, totalPrice uint32, baseReq *app.BaseReq) (*riskMngApiPb.CheckResp, error) {
	customType := 1
	sceneId := 12
	op := &riskMngApiPb.FaceAuthInfo{}
	if len(baseReq.GetFaceAuthInfo().GetToken()) > 0 {
		op.Token = baseReq.GetFaceAuthInfo().GetToken()
		op.ProviderCode = baseReq.GetFaceAuthInfo().GetProviderCode()
		op.ProviderResultData = baseReq.GetFaceAuthInfo().GetProviderResultData()
	}
	checkReq := &riskMngApiPb.CheckReq{
		Scene: "VIRTUAL_IMAGE_CONSUME",
		SourceEntity: &riskMngApiPb.Entity{
			FaceAuthInfo: op,
			Uid:          uid,
		},
		// 通用参数传递
		CustomParams: map[string]string{
			"consume_type": strconv.Itoa(customType),
			"scene_id":     strconv.Itoa(sceneId),
			"amount":       strconv.FormatFloat(float64(totalPrice), 'f', -1, 64),
		},
	}

	checkResp, err := m.RiskMngCli.CheckHelper(ctx, checkReq, baseReq)
	log.DebugWithCtx(ctx, "risk-mng-api.Check success, req:%+v, resp:%+v", checkReq, checkResp)

	if err != nil {
		// 系统错误，风控非关键路径，可忽略系统错误
		log.ErrorWithCtx(ctx, "risk-mng-api.Check failed, err:%v, req:%+v customType:%v", err, checkResp, customType)
		return checkResp, nil
	}
	log.DebugWithCtx(ctx, "risk-mng-api.Check success, req:%+v, resp:%+v", checkReq, checkResp)

	if checkResp.GetErrCode() != 0 {
		return checkResp, protocol.NewExactServerError(codes.OK, int(checkResp.GetErrCode()), checkResp.GetErrMsg())
	}
	return checkResp, nil

}

func (m *VirtualImageLogicMgr) checkCanBuyCommodity(ctx context.Context, uid uint32, in *pb.BuyCommodityDataRequest, resp []*mallPb.CommodityData) error {

	errMessExpire := ""
	exCount := 0

	shoppingItemsMap := make(map[uint32]*pb.ShoppingItemBasic)
	for _, itemData := range in.GetCommodityItemList() {
		shoppingItemsMap[itemData.GetPackageId()] = itemData
	}

	suitMap, itemMap, err := m.getUserVirtualImageList(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "BuyCommodityData getUserVirtualImageList uid:%v err:%v", uid, err)
		return err
	}

	for _, commodityData := range resp {
		idStr := util.GetResourceIdStr(commodityData.GetResourceIdList())

		//非购买途径可获得商品 不允许购买
		if commodityData.GetGainPath() != uint32(pb.CommodityGainPath_COMMODITY_GAIN_PATH_PURCHASE) {
			log.ErrorWithCtx(ctx, "BuyCommodityData uid:%v commodityData:%v not PURCHASE", uid, commodityData)
			return protocol.NewExactServerError(nil, status.ErrVirtualImageCommodityNotBuy)
		}

		for _, pricePackage := range commodityData.GetPricePackageList() {
			if val, ok := shoppingItemsMap[pricePackage.GetPackageId()]; ok {
				delete(shoppingItemsMap, pricePackage.GetPackageId())
				// 6.61.5，不允许购买已经拥有的商品
				err := util.CheckCommodityIsGet(ctx, uid, val.GetCount(), commodityData.GetCommodityType(), idStr, itemMap, suitMap)
				if err != nil {
					log.ErrorWithCtx(ctx, "BuyCommodityData uid:%v commodityData:%v CheckCommodityIsGet pricePackage:%v", uid, commodityData.GetCommodityId(), pricePackage)
					return err
				}

				//商品已下架 不允许购买 最多展示三个下架商品
				if pricePackage.GetExpireTime() < uint32(time.Now().Unix()) {
					exCount += 1
					if exCount <= util.EXPIRE_COMMODITY_COUNT {
						errMessExpire += fmt.Sprintf("[%v]、", commodityData.GetCommodityName())
						break
					}
				}

				//商品信息发生更新 不允许购买
				if pricePackage.GetEffectiveDay() != val.GetEffectiveDay() ||
					pricePackage.GetDiscountPrice()*val.GetCount() != val.GetTotalPrice() {
					log.ErrorWithCtx(ctx, "BuyCommodityData uid:%v errMessChange pricePackage:%v,val:%v ", uid, pricePackage, val)
					return protocol.NewExactServerError(nil, status.ErrVirtualImageCommodityConfigChange, "商品信息发生更新，请重新核对确认")
				}
			}
		}
	}

	if len(shoppingItemsMap) > 0 {
		for _, val := range shoppingItemsMap {
			for _, commodityData := range resp {
				if val.CommodityId == commodityData.CommodityId && exCount <= util.EXPIRE_COMMODITY_COUNT {
					errMessExpire += fmt.Sprintf("[%v]、", commodityData.GetCommodityName())
					break
				}
			}
		}
	}

	// 商品已下架，无法购买
	if len(errMessExpire) > 0 {
		errMessExpire = errMessExpire[:len(errMessExpire)-3]
		if exCount > util.EXPIRE_COMMODITY_COUNT {
			errMessExpire += "..."
		}
		errMessExpire += " 商品已下架，无法购买"
		log.ErrorWithCtx(ctx, "BuyCommodityData uid:%v, errMessExpire:%s", uid, errMessExpire)
		return protocol.NewExactServerError(nil, status.ErrVirtualImageCommodityNotExist, errMessExpire)
	}

	// 检查婚礼过程中是否可以购买商品
	err = m.checkWeddingBuyLimit(ctx, uid, resp)
	if err != nil {
		log.ErrorWithCtx(ctx, "BuyCommodityData uid:%v checkWeddingBuyLimit err:%v", uid, err)
		return err
	}

	return nil
}

// checkWeddingBuyLimit 检查婚礼过程中是否可以购买商品
func (m *VirtualImageLogicMgr) checkWeddingBuyLimit(ctx context.Context, uid uint32, commodityDataList []*mallPb.CommodityData) error {
	inWedding, err := m.checkIfInWedding(ctx, uid)
	if err != nil {
		log.WarnWithCtx(ctx, "checkWeddingBuyLimit checkIfInWedding uid:%v err:%v", uid, err)
	}

	if !inWedding {
		return nil
	}

	weddingSubCateList := m.dyconfig.GetWeddingAllowChangeSubCategoryList()
	weddingSubCateMap := make(map[uint32]bool)
	for _, subCate := range weddingSubCateList {
		weddingSubCateMap[subCate] = true
	}

	suitResIdList := make([]uint32, 0)
	for _, commodityData := range commodityDataList {
		if commodityData.GetCommodityType() == uint32(mallPb.CommodityType_COMMODITY_TYPE_SUIT) {
			suitResIdList = append(suitResIdList, commodityData.GetResourceIdList()...)
			continue
		}

		subCate := commodityData.GetSubCategory()
		if !weddingSubCateMap[subCate] {
			// 存在婚礼过程中暂不支持更换的组件
			return protocol.NewExactServerError(nil, status.ErrVirtualImageCommodityNotExist, "婚礼过程中暂不支持更换该装扮哦~")
		}
	}

	if len(suitResIdList) == 0 {
		return nil
	}

	resResp, err := m.resourceCli.GetVirtualImageResourcesByIds(ctx, &virtualimageresource.GetVirtualImageResourcesByIdsRequest{
		Ids: suitResIdList,
	})
	if err != nil {
		log.WarnWithCtx(ctx, "checkWeddingBuyLimit GetVirtualImageResourcesByIds uid:%v err:%v", uid, err)
		return err
	}

	for _, res := range resResp.GetResources() {
		subCate := res.GetSubCategory()
		if !weddingSubCateMap[subCate] {
			// 存在婚礼过程中暂不支持更换的组件
			return protocol.NewExactServerError(nil, status.ErrVirtualImageCommodityNotExist, "婚礼过程中暂不支持更换该装扮哦~")
		}
	}

	return nil
}

func (m *VirtualImageLogicMgr) BuyCommodityData(ctx context.Context, in *pb.BuyCommodityDataRequest) (*pb.BuyCommodityDataResponse, error) {
	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID
	log.InfoWithCtx(ctx, "BuyCommodityData uid:%v in:%+v", uid, in)
	out := &pb.BuyCommodityDataResponse{}

	if err := m.checkUsualDevice(ctx); err != nil {
		return out, err
	}

	commodityIdList := make([]uint32, 0)
	shoppingItemsMap := make(map[uint32]*pb.ShoppingItemBasic)
	for _, itemData := range in.GetCommodityItemList() {
		commodityIdList = append(commodityIdList, itemData.GetCommodityId())
		shoppingItemsMap[itemData.GetPackageId()] = itemData
	}

	resp, err := m.mallClient.GetCommodityDataList(ctx, &mallPb.GetCommodityDataListRequest{
		CommodityIdList: commodityIdList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BuyCommodityData GetCommodityDataList uid:%v commodityIdList:%v err:%v", uid, commodityIdList, err)
		return out, err
	}

	// 商品是否可售卖检测
	err = m.checkCanBuyCommodity(ctx, uid, in, resp.GetCommodityDataList())
	if err != nil {
		return out, err
	}

	log.DebugWithCtx(ctx, "BuyCommodityData uid:%v GetCommodityDataList resp:%+v", uid, resp)

	// init order request
	now := time.Unix(time.Now().Unix(), 0)
	totalPrice := uint64(0)
	buy := &mallPb.BuyCommodityDataRequest{}
	var commodityDataStr string
	subCategoryInfoMap := m.localCache.GetAllResourceSubCategoryInfos()
	bigTradeNo := util.GenOrderID(uid, uint32(rand.Intn(10000)), uint32(rand.Intn(10000)), "virtualimage", now)
	for _, commodityData := range resp.GetCommodityDataList() {
		for _, pricePackage := range commodityData.GetPricePackageList() {
			pkg, ok := shoppingItemsMap[pricePackage.GetPackageId()]
			if !ok {
				continue
			}

			category := commodityData.GetCategory()
			// 套装商品
			if commodityData.GetCommodityType() == uint32(mallPb.CommodityType_COMMODITY_TYPE_SUIT) {
				category = util.CommodityCategory_Suit
				levelCfgMap := m.GetLevelIconConfig(ctx)
				if levelCfg, ok := levelCfgMap[commodityData.GetLevel()]; ok {
					commodityData.LevelIcon = levelCfg.GetLevelIcon()
					commodityData.CommodityAnimation = levelCfg.GetLevelWebp()
				}
			}

			if pkg.GetCommodityId() == commodityData.GetCommodityId() {
				if commodityData.GetCommodityType() == uint32(pb.CommodityType_COMMODITY_TYPE_SUIT) {
					commodityDataStr += fmt.Sprintf("[套装-%s]、", commodityData.GetCommodityName())
				} else {
					if categoryInfo, ok := subCategoryInfoMap[commodityData.GetSubCategory()]; ok {
						commodityDataStr += fmt.Sprintf("[%s-%s]、", categoryInfo.GetSubCategoryName(), commodityData.GetCommodityName())
					}
				}
				totalPrice += uint64(pricePackage.GetDiscountPrice()) * uint64(pkg.GetCount())
				buy.Orders = append(buy.GetOrders(), util.InitPbCommodityOrder(uid, pkg.GetCount(), category, uint32(now.Unix()), bigTradeNo, commodityData, pricePackage))
			}
		}
	}

	if totalPrice > uint64(m.cfg.MaxTotalPrice) {
		log.ErrorWithCtx(ctx, "BuyCommodityData uid:%d totalPrice:%d", uid, totalPrice)
		message := fmt.Sprintf("单次购买不能超过 %v 豆哦，请调整后购买", m.cfg.MaxTotalPrice)
		return out, protocol.NewExactServerError(nil, status.ErrVirtualImageTotalPriceLimit, message)
	}

	if totalPrice == 0 {
		log.ErrorWithCtx(ctx, "BuyCommodityData fail. uid:%d totalPrice:%d", uid, totalPrice)
		return out, protocol.NewExactServerError(nil, status.ErrVirtualImageTotalPriceLimit, "购买失败，请重新选择商品")
	}

	// 风控检查
	checkResp, err := m.CheckFaceAuthInfo(ctx, uid, uint32(totalPrice), in.GetBaseReq())
	if err != nil {
		log.ErrorWithCtx(ctx, "BuyCommodityData uid:%v totalPrice:%v CheckFaceAuthInfo checkResp:%v, err:%v", uid, totalPrice, checkResp, err)
		if out.BaseResp == nil {
			out.BaseResp = &app.BaseResp{}
		}
		out.BaseResp.ErrInfo = checkResp.GetErrInfo()
		return out, err
	}

	// 下单
	_, err = m.mallClient.BuyCommodityData(ctx, buy)
	if err != nil {
		log.ErrorWithCtx(ctx, "BuyCommodityData uid:%v err:%v", uid, err)
		return out, err
	}

	// 冻结T豆
	restBalance, serr := m.unifiedPayCli.PresetFreeze(ctx, uid, uint32(totalPrice), m.cfg.AppId, bigTradeNo, now.Format("2006-01-02 15:04:05"), "购买虚拟形象商品")
	if serr != nil {
		log.ErrorWithCtx(ctx, "uid:%v PresetFreeze outTradeNo:%v serr:%v", uid, bigTradeNo, serr)
		if serr.Code() == status.ErrTbeanNoEnoughBalance {
			return out, protocol.NewExactServerError(nil, status.ErrTbeanNoEnoughBalance)
		}
		m.mallClient.UpdateCommodityDataOrdersStatus(ctx, &mallPb.UpdateCommodityDataOrdersStatusRequest{
			BigTradeNo: bigTradeNo,
			PayStatus:  uint32(mallPb.CommodityPayStatus_COMMODITY_PAY_STATUS_FREEZE_FAIL),
		})
		return out, protocol.ToServerError(serr)
	}

	_, err = m.mallClient.UpdateCommodityDataOrdersStatus(ctx, &mallPb.UpdateCommodityDataOrdersStatusRequest{
		BigTradeNo: bigTradeNo,
		PayStatus:  uint32(mallPb.CommodityPayStatus_COMMODITY_PAY_STATUS_FREEZE),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "uid:%v UpdateCommodityDataOrdersStatus outTradeNo:%v err:%v", uid, bigTradeNo, err)
		return out, err
	}
	m.ShippedCommodityData(ctx, uid, buy.GetOrders())

	// 发送IM消息
	goroutineex.GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
		msg := m.anchorCheckDyconfig.GetAnchorCheckImMsg().BuyVirtualImage
		commodityDataStr = commodityDataStr[:len(commodityDataStr)-3]
		commodityDataStr += "，"
		pushMsg := "你已成功购买" + commodityDataStr + "可前往【形象中心页-我的】进行查看和使用。  立即前往＞"
		_, err := m.ImApiCli.SimpleSendTTAssistantText(ctx, uid, pushMsg, msg.Highlight, msg.Url)
		if err != nil {
			log.ErrorWithCtx(ctx, "uid:%v SimpleSendTTAssistantText err:%v", uid, err)
		}

		_ = m.SendChannelMsg(ctx, uid, uint32(totalPrice))
	})

	log.DebugWithCtx(ctx, "uid:%v orderNo:%v PresetFreeze restBalance:%v totalPrice:%v", uid, bigTradeNo, restBalance, totalPrice)

	out.OrderId = bigTradeNo
	log.InfoWithCtx(ctx, "BuyCommodityData uid:%+v out:%v", uid, out)

	return out, nil
}

func (m *VirtualImageLogicMgr) ComputeCommodityPrice(ctx context.Context, in *pb.ComputeCommodityPriceRequest) (*pb.ComputeCommodityPriceResponse, error) {
	log.DebugWithCtx(ctx, "ComputeCommodityPrice in:%+v", in)
	out := &pb.ComputeCommodityPriceResponse{}

	commodityIdList := make([]uint32, 0)
	shoppingItemsMap := make(map[uint32]*pb.ShoppingItemBasic)
	for _, itemData := range in.GetCommodityItemList() {
		commodityIdList = append(commodityIdList, itemData.GetCommodityId())
		shoppingItemsMap[itemData.GetPackageId()] = itemData
	}

	resp, err := m.mallClient.GetCommodityDataList(ctx, &mallPb.GetCommodityDataListRequest{
		CommodityIdList: commodityIdList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataList commodityIdList:%v err:%v", commodityIdList, err)
		return out, err
	}

	now := uint32(time.Now().Unix())
	log.DebugWithCtx(ctx, "ComputeCommodityPrice in:%+v shoppingItemsMap:%v", in, shoppingItemsMap)
	for _, commodityData := range resp.GetCommodityDataList() {
		pricePackageList := commodityData.GetPricePackageList()
		for _, pricePackage := range pricePackageList {
			log.DebugWithCtx(ctx, "ComputeCommodityPrice in:%+v pricePackage:%v", in, pricePackage)
			if pricePackage.GetShelfTime() < now && pricePackage.GetExpireTime() > now {
				if val, ok := shoppingItemsMap[pricePackage.GetPackageId()]; ok {
					if val.GetCommodityId() == commodityData.GetCommodityId() {
						out.CommodityItemList = append(out.GetCommodityItemList(), &pb.ShoppingItemBasic{
							CommodityId:  commodityData.GetCommodityId(),
							PackageId:    pricePackage.GetPackageId(),
							Count:        val.GetCount(),
							TotalPrice:   pricePackage.GetDiscountPrice() * val.GetCount(),
							AvgPrice:     pricePackage.GetDiscountPrice(),
							EffectiveDay: pricePackage.GetEffectiveDay(),
						})
						out.TotalPrice += pricePackage.GetDiscountPrice() * val.GetCount()

					}
					delete(shoppingItemsMap, pricePackage.GetPackageId())
				}
			}
		}
	}

	if len(shoppingItemsMap) > 0 {
		for _, val := range shoppingItemsMap {
			out.ExpireCommodityItemList = append(out.GetExpireCommodityItemList(), val)
		}
		log.DebugWithCtx(ctx, "ComputeCommodityPrice in:%+v shoppingItemsMap:%v", in, shoppingItemsMap)
		return out, protocol.NewExactServerError(nil, status.ErrVirtualImageCommodityNotExist)
	}

	log.DebugWithCtx(ctx, "ComputeCommodityPrice in:%+v out:%v", in, out)
	return out, nil
}

func (m *VirtualImageLogicMgr) cardCommodityDataListFilter(ctx context.Context, list []*mallPb.CommodityData) ([]*mallPb.CommodityData, error) {
	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

	// 查询是否有无限换装卡可见权限
	cardEntryResp, err := m.GetVirtualImageCardEntryStatus(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "cardCommodityDataListFilter fail to GetVirtualImageCardEntryStatus. uid:%d, err:%v", uid, err)
		return nil, err
	}
	hasCardEntry := cardEntryResp.GetSwitch()

	commodityDataList := make([]*mallPb.CommodityData, 0, len(list))
	for _, data := range list {
		if data.GetGainPath() == uint32(mallPb.CommodityGainPath_COMMODITY_GAIN_PATH_INFINITE_CHANGE_CARD) {
			if !cardRightsCommodityMinVer.Atleast(serviceInfo.ClientType, serviceInfo.ClientVersion) {
				// 旧版本不展示无限换装卡商品
				continue
			}

			if !hasCardEntry {
				// 没有无限换装卡可见权限，不展示无限换装卡商品
				continue
			}
		}

		commodityDataList = append(commodityDataList, data)
	}

	return commodityDataList, nil
}

func (m *VirtualImageLogicMgr) GetCommodityDataList(ctx context.Context, in *pb.GetCommodityDataListRequest) (*pb.GetCommodityDataListResponse, error) {
	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID
	log.InfoWithCtx(ctx, "GetCommodityDataList uid:%+v in:%+v", uid, in)
	out := &pb.GetCommodityDataListResponse{}

	userInfo, serr := m.accountCli.GetUserByUid(ctx, uid)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataList uid:%v GetUserByUid err:%v", uid, serr)
	}

	resourceSexList := make([]uint32, 0)
	// 1: 男 2: 女 3: 通用
	resourceSexList = append(resourceSexList, uint32(virtualimageresource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_COMMON))
	if userInfo.GetSex() == account.Male {
		resourceSexList = append(resourceSexList, uint32(virtualimageresource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_MALE))
	}
	if userInfo.GetSex() == account.Female {
		resourceSexList = append(resourceSexList, uint32(virtualimageresource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_FEMALE))
	}

	// 尝试给用户赠送免费商品
	err := m.tryGiveFreeCommodity(ctx, uid, resourceSexList)
	if err != nil {
		log.WarnWithCtx(ctx, "GetCommodityDataList tryGiveFreeCommodity uid:%v err:%v", uid, err)
	}

	resp, err := m.mallClient.GetCommodityDataList(ctx, &mallPb.GetCommodityDataListRequest{
		Category:        in.GetCategory(),
		SubCategory:     in.GetSubCategory(),
		ResourceSexList: resourceSexList,
		ShelfStatus:     uint32(mallPb.ShelfStatus_SHELF_STATUS_NOW),
		ResourceIdList:  in.GetResourceIdList(),
		CommodityType:   in.GetCommodityType(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataList uid:%v err:%v", uid, err)
		return out, err
	}

	// 云测展示未来商品
	var isTrue bool
	if os.Getenv("MY_CLUSTER") == "testing" {
		futureCommodity, err := m.mallClient.GetCommodityDataList(ctx, &mallPb.GetCommodityDataListRequest{
			Category:        in.GetCategory(),
			SubCategory:     in.GetSubCategory(),
			ResourceSexList: resourceSexList,
			ShelfStatus:     uint32(mallPb.ShelfStatus_SHELF_STATUS_FUTURE),
			ResourceIdList:  in.GetResourceIdList(),
			CommodityType:   in.GetCommodityType(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetCommodityDataList uid:%v err:%v", uid, err)
		}
		log.DebugWithCtx(ctx, "GetCommodityDataList uid:%v futureCommodityDataList:%+v-%d", uid, futureCommodity.GetCommodityDataList(), len(resp.CommodityDataList))
		if len(futureCommodity.GetCommodityDataList()) > 0 {
			resp.CommodityDataList = append(resp.CommodityDataList, futureCommodity.GetCommodityDataList()...)
			isTrue = true
		}
		log.DebugWithCtx(ctx, "GetCommodityDataList uid:%v after futureCommodityDataList:%d-%d", uid, len(resp.CommodityDataList))

	}

	commodityDataList, err := m.cardCommodityDataListFilter(ctx, resp.GetCommodityDataList())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataList fail to cardCommodityDataListFilter. uid:%v err:%v", uid, err)
		return out, err
	}

	resourceIdList := make([]uint32, 0)
	for _, data := range commodityDataList {
		log.DebugWithCtx(ctx, "GetCommodityDataList uid:%v CommodityId:%d", uid, data.CommodityId)
		for _, resourceId := range data.GetResourceIdList() {
			resourceIdList = append(resourceIdList, resourceId)
		}
	}

	sResp, err := m.resourceCli.GetVirtualImageResourcesByIds(ctx, &virtualimageresource.GetVirtualImageResourcesByIdsRequest{
		Ids: resourceIdList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataList GetVirtualImageResourcesByIds uid:%v err:%v", uid, err)
		return out, err
	}

	// 获取用户已拥有的物品
	suitMap, itemMap, err := m.getUserVirtualImageList(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataList getUserVirtualImageList uid:%v err:%v", uid, err)
		return out, err
	}

	commodityDataListObtain, commodityDataListUnobtain := m.PaddingCommodityData(ctx, uid, commodityDataList, sResp.GetResources(), suitMap, itemMap, isTrue)
	sort.SliceStable(commodityDataListUnobtain, func(i, j int) bool {
		if commodityDataListUnobtain[i].GetRank() < commodityDataListUnobtain[j].GetRank() {
			return true
		}
		if commodityDataListUnobtain[i].GetRank() > commodityDataListUnobtain[j].GetRank() {
			return false
		}
		return commodityDataListUnobtain[i].GetCommodityId() > commodityDataListUnobtain[j].GetCommodityId()
	})

	out.CommodityDataList = append(commodityDataListUnobtain, commodityDataListObtain...)
	// 新版本隐藏推荐tab开关
	isHideRecommendTab := m.dyconfig.GetHideRecommendTab() && cardRightsCommodityMinVer.Atleast(serviceInfo.ClientType, serviceInfo.ClientVersion)
	if !isHideRecommendTab {
		recommendIdMap := make(map[uint32]bool)
		reResp, _ := m.mallClient.GetCommodityRecommendList(ctx, &mallPb.GetCommodityRecommendListRequest{})
		for _, rec := range reResp.GetRecList() {
			recommendIdMap[rec.GetData().GetCommodityId()] = true
		}
		for _, data := range out.GetCommodityDataList() {
			if _, ok := recommendIdMap[data.GetCommodityId()]; ok {
				data.IsRecommend = true
			}
		}
	}

	log.InfoWithCtx(ctx, "GetCommodityDataList uid:%v in:%+v, len:%v", uid, in, len(out.GetCommodityDataList()))
	return out, nil

}

func (m *VirtualImageLogicMgr) GetRecommendCommodityDataList(ctx context.Context, in *pb.GetRecommendCommodityDataListRequest) (*pb.GetRecommendCommodityDataListResponse, error) {
	log.InfoWithCtx(ctx, "GetRecommendCommodityDataList in:%+v", in)
	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

	userInfo, serr := m.accountCli.GetUserByUid(ctx, uid)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetRecommendCommodityDataList uid:%v GetUserByUid err:%v", uid, serr)
	}

	resourceSexList := make([]uint32, 0)
	// 1: 男 2: 女 3: 通用
	resourceSexList = append(resourceSexList, uint32(virtualimageresource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_COMMON))
	if userInfo.GetSex() == account.Male {
		resourceSexList = append(resourceSexList, uint32(virtualimageresource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_MALE))
	}
	if userInfo.GetSex() == account.Female {
		resourceSexList = append(resourceSexList, uint32(virtualimageresource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_FEMALE))
	}

	// 尝试给用户赠送免费商品
	err := m.tryGiveFreeCommodity(ctx, uid, resourceSexList)
	if err != nil {
		log.WarnWithCtx(ctx, "GetRecommendCommodityDataList tryGiveFreeCommodity uid:%v err:%v", uid, err)
	}

	out := &pb.GetRecommendCommodityDataListResponse{}
	resp, err := m.mallClient.GetCommodityRecommendList(ctx, &mallPb.GetCommodityRecommendListRequest{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendCommodityDataList GetCommodityRecommendList err:%v", err)
		return out, err
	}
	recList := resp.GetRecList()
	sort.SliceStable(recList, func(i, j int) bool {
		if recList[i].RecRank < recList[j].RecRank {
			return true
		}
		if recList[i].RecRank == recList[j].RecRank {
			return recList[i].Data.CommodityId > recList[j].Data.CommodityId
		}
		return false
	})

	commodityDataList := make([]*mallPb.CommodityData, 0)
	resourceIdList := make([]uint32, 0)
	for _, rec := range recList {
		commodityDataList = append(commodityDataList, rec.GetData())
		for _, resourceId := range rec.GetData().ResourceIdList {
			resourceIdList = append(resourceIdList, resourceId)
		}
	}

	commodityDataList, err = m.cardCommodityDataListFilter(ctx, commodityDataList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendCommodityDataList cardCommodityDataListFilter uid:%v err:%v", uid, err)
		return out, err
	}

	sResp, err := m.resourceCli.GetVirtualImageResourcesByIds(ctx, &virtualimageresource.GetVirtualImageResourcesByIdsRequest{
		Ids: resourceIdList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendCommodityDataList GetVirtualImageResourcesByIds err:%v", err)
		return out, err
	}
	suitMap, itemMap, err := m.getUserVirtualImageList(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendCommodityDataList getUserVirtualImageList uid:%v err:%v", uid, err)
		return out, err
	}

	commodityDataListObtain, commodityDataListUnobtain := m.PaddingCommodityData(ctx, uid, commodityDataList, sResp.GetResources(), suitMap, itemMap, false)
	out.CommodityDataList = append(commodityDataListUnobtain, commodityDataListObtain...)

	for _, data := range out.GetCommodityDataList() {
		data.IsRecommend = true
	}

	return out, nil

}

func (m *VirtualImageLogicMgr) getUserVirtualImageList(ctx context.Context, uid uint32) (map[string]*userPb.UserSuitInfo, map[string]*userPb.UserItemInfo, error) {
	vResp, err := m.userCli.GetUserVirtualImageList(ctx, &userPb.GetUserVirtualImageListReq{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BuyCommodityData GetUserVirtualImageList uid:%v err:%v", uid, err)
		return nil, nil, err
	}
	suitMap := make(map[string]*userPb.UserSuitInfo)
	itemMap := make(map[string]*userPb.UserItemInfo)

	for _, item := range vResp.GetItems() {
		idStr := ""
		idStr += fmt.Sprintf("%d", item.GetCfgId())
		itemMap[idStr] = item
	}
	for _, suit := range vResp.GetSuits() {
		resourceIdList := make([]uint32, 0)
		for _, item := range suit.GetItems() {
			resourceIdList = append(resourceIdList, item.GetCfgId())
		}
		idStr := util.GetResourceIdStr(resourceIdList)
		suitMap[idStr] = suit
	}

	return suitMap, itemMap, err
}

func (m *VirtualImageLogicMgr) initPbCommodityData(ctx context.Context, uid uint32, now, etime uint32, data *mallPb.CommodityData,
	resourceInfoMap map[uint32]*virtualimageresource.VirtualImageResourceInfo, itemMap map[string]*userPb.UserItemInfo,
	suitMap map[string]*userPb.UserSuitInfo, pricePackage []*pb.CommodityDataPackage, levelConfMap map[uint32]*virtualimageresource.LevelConfig) *pb.CommodityData {

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	marketID := serviceInfo.MarketID
	clientType := 0
	// 0: 全部 1: 安卓 2: IOS
	if serviceInfo.ClientType == protocol.ClientTypeANDROID {
		clientType = 1
	}
	if serviceInfo.ClientType == protocol.ClientTypeIOS {
		clientType = 2
	}

	sort.SliceStable(pricePackage, func(i, j int) bool {
		return pricePackage[i].GetEffectiveDay() < pricePackage[j].GetEffectiveDay()
	})

	// 商品列表取的是所有套餐中最晚的时间的套餐，这个套餐展示就展示，不展示就不展示
	// 有多个套餐最晚的时间一样，有一个套餐的show_expire_time为true，就展示，否则不展示
	var showExpireTime bool
	for _, pkg := range pricePackage {
		if etime == pkg.GetExpireTime() {
			if pkg.GetShowExpireTime() {
				showExpireTime = true
				break
			}
		}
	}

	if !showExpireTime {
		etime = 0
	}

	customizeIcon := data.GetCustomizeIcon()
	logotype := ""
	if customizeIcon.GetShelfTime() < now && customizeIcon.GetExpireTime() > now {
		logotype = customizeIcon.GetLogotype()
	}

	actUrl := ""
	for _, act := range data.GetActInfo().GetUrlList() {
		if act.GetMarketId() == marketID {
			if act.GetClientType() == 0 {
				actUrl = act.GetUrl()
				break
			}
			if act.GetClientType() == uint32(clientType) {
				actUrl = act.GetUrl()
				break
			}
		}
	}
	log.DebugWithCtx(ctx, "uid:%v InitCommodityData marketID:%v clientType:%v actUrl:%v", uid, marketID, clientType, actUrl)
	actinfo := &pb.VirtualImageActivityInfo{
		Desc:      data.GetActInfo().GetDesc(),
		DescColor: data.GetActInfo().GetDescColor(),
		Url:       actUrl,
	}

	resourceIdList := make([]uint32, 0)
	resourceInfoList := make([]*pb.VirtualImageResourceInfo, 0)
	commodityName := ""
	for _, resourceId := range data.GetResourceIdList() {
		if resourceInfo, ok := resourceInfoMap[resourceId]; ok {
			resourceIdList = append(resourceIdList, resourceId)
			tmpResource := &pb.VirtualImageResourceInfo{
				Id:           resourceInfo.GetId(),
				SkinName:     resourceInfo.GetSkinName(),
				ResourceType: resourceInfo.GetResourceType(),
				ResourceName: resourceInfo.GetResourceName(),
				SubCategory:  resourceInfo.GetSubCategory(),
				Category:     resourceInfo.GetCategory(),
				IconUrl:      resourceInfo.GetIconUrl(),
				DisplayName:  resourceInfo.GetDisplayName(),
			}
			resourceInfoList = append(resourceInfoList, tmpResource)
			commodityName = resourceInfo.GetDisplayName()
		}
	}

	charSubCategory := uint32(virtualimageresource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_CHAIR)
	roomSubCategory := uint32(virtualimageresource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ROOM_ENTRANCE_EFFECTS)
	sort.SliceStable(resourceInfoList, func(r, l int) bool {
		rSubCategory := resourceInfoList[r].GetSubCategory()
		lSubCategory := resourceInfoList[l].GetSubCategory()
		rCategory := resourceInfoList[r].GetCategory()
		lCategory := resourceInfoList[l].GetCategory()

		if rSubCategory == charSubCategory && lSubCategory != charSubCategory {
			return true
		}
		if rSubCategory != charSubCategory && lSubCategory == charSubCategory {
			return false
		}

		if rSubCategory == roomSubCategory && lSubCategory != roomSubCategory {
			return true
		}
		if rSubCategory != roomSubCategory && lSubCategory == roomSubCategory {
			return false
		}

		if rCategory < lCategory {
			return true
		}
		if rCategory > lCategory {
			return false
		}
		return rSubCategory < lSubCategory
	})
	tmpCData := &pb.CommodityData{
		CommodityId:        data.GetCommodityId(),
		CommodityName:      data.GetCommodityName(),
		Level:              data.GetLevel(),
		LevelIcon:          "",
		Logotype:           logotype,
		CommodityIcon:      data.GetCommodityIcon(),
		CommodityAnimation: "",
		GainPath:           data.GetGainPath(),
		ExpireTime:         etime,
		ResourceList:       resourceInfoList,
		Category:           data.GetCategory(),
		SubCategory:        data.GetSubCategory(),
		CommodityType:      data.GetCommodityType(),
		Rank:               data.GetRank(),
		ResourceSex:        data.GetResourceSex(),
		ActInfo:            actinfo,
		PricePackageList:   pricePackage,
		RedDotVersion:      data.GetRedDotVersion(),
		PromotionalVideoId: data.GetPromotionalVideoId(),
	}
	if val, ok := levelConfMap[data.GetLevel()]; ok {
		tmpCData.LevelIcon = val.GetLevelIcon()
		tmpCData.CommodityAnimation = val.GetLevelWebp()
	}
	if tmpCData.GetExpireTime() >= util.PERPETUAL_TIME {
		tmpCData.ExpireTime = 0
	}

	idStr := util.GetResourceIdStr(resourceIdList)
	if tmpCData.GetCommodityType() == uint32(pb.CommodityType_COMMODITY_TYPE_SUIT) {
		tmpCData.Category = util.CommodityCategory_Suit
		tmpCData.SuitUniqueId = util.GetMd5Str(idStr)
	}

	userExpireTime := int64(0)
	if data.GetCommodityType() == uint32(mallPb.CommodityType_COMMODITY_TYPE_SINGLE) {
		tmpCData.CommodityName = commodityName
		if item, ok := itemMap[idStr]; ok {
			userExpireTime = item.GetExpireTime()
			tmpCData.UserBuyTime = uint32(item.GetUpdateTime())
			tmpCData.IsGet = true
		}
	}
	if data.GetCommodityType() == uint32(mallPb.CommodityType_COMMODITY_TYPE_SUIT) {
		if suit, ok := suitMap[idStr]; ok {
			userExpireTime = suit.GetExpireTime()
			tmpCData.UserBuyTime = uint32(suit.GetUpdateTime())
			tmpCData.IsGet = true
		}
	}
	tmpCData.UserExpireTime = uint32(userExpireTime)
	if userExpireTime >= util.PERPETUAL_TIME {
		tmpCData.UserIsPerpetual = true
		tmpCData.UserExpireTime = util.PERPETUAL_TIME
	}
	log.DebugWithCtx(ctx, "uid:%v InitCommodityData idStr:%v tmpCData: %v", uid, idStr, tmpCData)
	return tmpCData
}

func (m *VirtualImageLogicMgr) PaddingCommodityData(ctx context.Context, uid uint32, mallCommodityDataList []*mallPb.CommodityData,
	resourceInfoList []*virtualimageresource.VirtualImageResourceInfo, suitMap map[string]*userPb.UserSuitInfo, itemMap map[string]*userPb.UserItemInfo, isTrue bool) ([]*pb.CommodityData, []*pb.CommodityData) {
	userInfo, err := m.accountCli.GetUserByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "PaddingCommodityData uid:%v GetUserByUid err:%v", uid, err)
	}

	now := uint32(time.Now().Unix())

	tmpCommodityDataListUnobtain := make([]*pb.CommodityData, 0)
	tmpCommodityDataListObtain := make([]*pb.CommodityData, 0)
	resourceInfoMap := make(map[uint32]*virtualimageresource.VirtualImageResourceInfo)
	for _, data := range resourceInfoList {
		resourceInfoMap[data.GetId()] = data
	}
	log.DebugWithCtx(ctx, "uid:%v PaddingCommodityData mallCommodityDataList len:%v", uid, mallCommodityDataList)

	levelConfMap := m.GetLevelIconConfig(ctx)

	userSex := 0
	// 1: 男 2: 女 0: 通用
	if userInfo.GetSex() == account.Male {
		userSex = int(virtualimageresource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_MALE)
	}
	if userInfo.GetSex() == account.Female {
		userSex = int(virtualimageresource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_FEMALE)
	}
	for _, tmpData := range mallCommodityDataList {
		log.DebugWithCtx(ctx, "GetCommodityDataList PaddingCommodityData uid:%v CommodityId:%d", uid, tmpData.GetCommodityId())
		if tmpData.GetResourceSex() != uint32(virtualimageresource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_COMMON) && tmpData.GetResourceSex() != uint32(userSex) {
			continue
		}
		btime := now - 10
		etime := now - 10
		pricePackageList := make([]*pb.CommodityDataPackage, 0)
		tmpPacketDataList := tmpData.GetPricePackageList()
		sort.SliceStable(tmpPacketDataList, func(i, j int) bool {
			return tmpPacketDataList[i].GetPrice() < tmpPacketDataList[j].GetPrice()
		})
		log.DebugWithCtx(ctx, "uid:%v CommodityId:%v PaddingCommodityData mallCommodityDataList time:%v %v %v, tmpPacketDataList:%v", uid, tmpData.GetCommodityId(), btime, etime, now, tmpPacketDataList)
		expirePackageExpireTime := uint32(0)
		expirePackageShelfTime := uint32(0)
		for _, tmpPacketData := range tmpPacketDataList {
			if (tmpPacketData.GetShelfTime() < now && tmpPacketData.GetExpireTime() > now) || isTrue {
				price := tmpPacketData.GetPrice()
				if price == 0 {
					price = tmpPacketData.GetDiscountPrice()
				}
				packetData := &pb.CommodityDataPackage{
					PackageId:      tmpPacketData.GetPackageId(),
					Price:          price,
					DiscountPrice:  tmpPacketData.GetDiscountPrice(),
					EffectiveDay:   tmpPacketData.GetEffectiveDay(),
					ExpireTime:     tmpPacketData.GetExpireTime(),
					ShowExpireTime: tmpPacketData.GetExpireTimeShow(),
				}
				if packetData.GetExpireTime() >= util.PERPETUAL_TIME {
					packetData.ExpireTime = 0
				}
				if packetData.GetEffectiveDay() >= util.PERPETUAL_DAY {
					packetData.IsPerpetual = true
				}

				pricePackageList = append(pricePackageList, packetData)

				if btime > tmpPacketData.GetShelfTime() {
					btime = tmpPacketData.GetShelfTime()
				}
				if etime < tmpPacketData.GetExpireTime() {
					etime = tmpPacketData.GetExpireTime()
				}
			} else {
				if expirePackageExpireTime < tmpPacketData.GetExpireTime() {
					expirePackageExpireTime = tmpPacketData.GetExpireTime()
				}
				if expirePackageShelfTime > tmpPacketData.GetShelfTime() {
					expirePackageShelfTime = tmpPacketData.GetShelfTime()
				}
			}
		}
		log.DebugWithCtx(ctx, "uid:%v CommodityId:%v PaddingCommodityData mallCommodityDataList time:%v %v %v", uid, tmpData.GetCommodityId(), btime, etime, now)
		// 只有在上架状态下才下发客户端
		if (btime < now && etime > now) || isTrue {
			sort.SliceStable(pricePackageList, func(i, j int) bool {
				return pricePackageList[i].GetDiscountPrice() < pricePackageList[j].GetDiscountPrice()
			})

			// 6.61.5 只保留一个最低价的套餐
			price := uint32(0)
			if len(pricePackageList) > 0 {
				pricePackageList = pricePackageList[:1]
				price = pricePackageList[0].GetDiscountPrice()
			}
			log.DebugWithCtx(ctx, "uid:%v CommodityId:%v PaddingCommodityData mallCommodityDataList time:%v %v %v", uid, tmpData.GetCommodityId(), btime, expirePackageShelfTime, expirePackageExpireTime)

			tmpCdata := m.initPbCommodityData(ctx, uid, now, etime, tmpData, resourceInfoMap, itemMap, suitMap, pricePackageList, levelConfMap)

			if expirePackageExpireTime < btime {
				tmpCdata.ShelfTime = btime
			} else {
				tmpCdata.ShelfTime = expirePackageShelfTime
			}
			log.DebugWithCtx(ctx, "uid:%v CommodityId:%v PaddingCommodityData mallCommodityDataList time:%v %v %v", uid, tmpData.GetCommodityId(), btime, etime, tmpCdata)
			if tmpCdata.GetUserExpireTime() > now && price > 0 /* 获得的非0元商品排序 */ {
				if tmpCdata.GetUserIsPerpetual() {
					continue
				}
				tmpCommodityDataListObtain = append(tmpCommodityDataListObtain, tmpCdata)
			} else {
				// 0元商品 和 未获得的商品 排序
				tmpCommodityDataListUnobtain = append(tmpCommodityDataListUnobtain, tmpCdata)
			}
		}
	}

	log.DebugWithCtx(ctx, "uid:%v PaddingCommodityData tmpCommodityDataList len:%v %v", uid, len(tmpCommodityDataListUnobtain), len(tmpCommodityDataListObtain))
	sort.SliceStable(tmpCommodityDataListObtain, func(i, j int) bool {
		return tmpCommodityDataListObtain[i].GetUserBuyTime() > tmpCommodityDataListObtain[j].GetUserBuyTime()
	})

	return tmpCommodityDataListObtain, tmpCommodityDataListUnobtain
}

func (m *VirtualImageLogicMgr) GetCommodityDataListById(ctx context.Context, in *pb.GetCommodityDataListByIdRequest) (*pb.GetCommodityDataListByIdResponse, error) {
	out := &pb.GetCommodityDataListByIdResponse{}
	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID
	log.DebugWithCtx(ctx, "GetCommodityDataListById uid:%v in:%+v", uid, in)
	commodityIdList := make([]uint32, 0)
	shoppingItemsMap := make(map[uint32]*pb.ShoppingItemBasic)
	for _, itemData := range in.GetCommodityItemList() {
		commodityIdList = append(commodityIdList, itemData.GetCommodityId())
		shoppingItemsMap[itemData.GetPackageId()] = itemData
	}
	resp, err := m.mallClient.GetCommodityDataList(ctx, &mallPb.GetCommodityDataListRequest{
		CommodityIdList: commodityIdList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataList uid:%v commodityIdList:%v err:%v", uid, commodityIdList, err)
		return out, err
	}
	resourceIdList := make([]uint32, 0)
	for _, data := range resp.GetCommodityDataList() {
		for _, resourceId := range data.GetResourceIdList() {
			resourceIdList = append(resourceIdList, resourceId)
		}
	}

	sResp, err := m.resourceCli.GetVirtualImageResourcesByIds(ctx, &virtualimageresource.GetVirtualImageResourcesByIdsRequest{
		Ids: resourceIdList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVirtualImageResourcesByIds err:%v", err)
		return out, err
	}

	suitMap, itemMap, err := m.getUserVirtualImageList(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserVirtualImageList err:%v", err)
		return out, err
	}

	commodityDataListObtain, commodityDataListUnobtain := m.PaddingCommodityData(ctx, uid, resp.GetCommodityDataList(), sResp.GetResources(), suitMap, itemMap, true)

	commodityDataList := append(commodityDataListUnobtain, commodityDataListObtain...)

	levelCfgMap := m.GetLevelIconConfig(ctx)

	for _, commodityData := range commodityDataList {
		tmpData := &pb.CommodityData{
			CommodityId:        commodityData.GetCommodityId(),
			CommodityName:      commodityData.GetCommodityName(),
			Level:              commodityData.GetLevel(),
			LevelIcon:          levelCfgMap[commodityData.GetLevel()].GetLevelIcon(),
			CommodityIcon:      commodityData.GetCommodityIcon(),
			CommodityAnimation: levelCfgMap[commodityData.GetLevel()].GetLevelWebp(),
			GainPath:           commodityData.GetGainPath(),
			Category:           commodityData.GetCategory(),
			SubCategory:        commodityData.GetSubCategory(),
			CommodityType:      commodityData.GetCommodityType(),
			Rank:               commodityData.GetRank(),
			ResourceSex:        commodityData.GetResourceSex(),
			ActInfo:            commodityData.GetActInfo(),
			ResourceList:       commodityData.GetResourceList(),
			ShelfTime:          commodityData.GetShelfTime(),
			ExpireTime:         commodityData.GetExpireTime(),
			IsGet:              commodityData.GetIsGet(),
			UserBuyTime:        commodityData.GetUserBuyTime(),
			UserExpireTime:     commodityData.GetUserExpireTime(),
			UserIsPerpetual:    commodityData.GetUserIsPerpetual(),
		}
		pricePackageList := commodityData.GetPricePackageList()
		for _, tmpPricePackage := range pricePackageList {
			if _, ok := shoppingItemsMap[tmpPricePackage.GetPackageId()]; ok {
				tmpData.PricePackageList = append(tmpData.GetPricePackageList(), &pb.CommodityDataPackage{
					PackageId:      tmpPricePackage.GetPackageId(),
					Price:          tmpPricePackage.GetPrice(),
					DiscountPrice:  tmpPricePackage.GetDiscountPrice(),
					EffectiveDay:   tmpPricePackage.GetEffectiveDay(),
					ExpireTime:     tmpPricePackage.GetExpireTime(),
					IsPerpetual:    tmpPricePackage.GetIsPerpetual(),
					ShowExpireTime: tmpPricePackage.GetShowExpireTime(),
				})
			}
		}
		out.CommodityDataList = append(out.GetCommodityDataList(), tmpData)
	}

	return out, nil
}

func (m *VirtualImageLogicMgr) GetVirtualImageCommodityRedDotDetail(ctx context.Context, in *pb.GetVirtualImageCommodityRedDotDetailRequest) (*pb.GetVirtualImageCommodityRedDotDetailResponse, error) {
	log.DebugWithCtx(ctx, "GetVirtualImageCommodityRedDotDetail in:%+v", in)
	out := &pb.GetVirtualImageCommodityRedDotDetailResponse{}
	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

	userInfo, serr := m.accountCli.GetUserByUid(ctx, uid)
	if serr != nil {
		log.ErrorWithCtx(ctx, "PaddingCommodityData uid:%v GetUserByUid err:%v", uid, serr)
		return out, serr
	}

	resourceSexList := make([]uint32, 0)
	// 1: 男 2: 女 3: 通用
	resourceSexList = append(resourceSexList, uint32(virtualimageresource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_COMMON))
	if userInfo.GetSex() == account.Male {
		resourceSexList = append(resourceSexList, uint32(virtualimageresource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_MALE))
	}
	if userInfo.GetSex() == account.Female {
		resourceSexList = append(resourceSexList, uint32(virtualimageresource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_FEMALE))
	}

	resp, err := m.mallClient.GetCommodityDataList(ctx, &mallPb.GetCommodityDataListRequest{
		ResourceSexList: resourceSexList,
		ShelfStatus:     uint32(mallPb.ShelfStatus_SHELF_STATUS_NOW),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataList uid:%v err:%v", uid, err)
		return out, err
	}

	recommendIdMap := make(map[uint32]bool)
	// 新版本隐藏推荐tab开关
	isHideRecommendTab := m.dyconfig.GetHideRecommendTab() && cardRightsCommodityMinVer.Atleast(serviceInfo.ClientType, serviceInfo.ClientVersion)
	if !isHideRecommendTab {
		reResp, err2 := m.mallClient.GetCommodityRecommendList(ctx, &mallPb.GetCommodityRecommendListRequest{
			ResourceSexList: resourceSexList,
		})
		if err2 != nil {
			log.ErrorWithCtx(ctx, "GetCommodityDataList GetCommodityRecommendList fail. uid:%v err:%v", uid, err)
		}
		for _, rec := range reResp.GetRecList() {
			recommendIdMap[rec.GetData().GetCommodityId()] = true
		}
	}

	suitMap, itemMap, err := m.getUserVirtualImageList(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataList getUserVirtualImageList uid:%v err:%v", uid, err)
		return out, err
	}

	for _, commodityData := range resp.GetCommodityDataList() {
		idStr := util.GetResourceIdStr(commodityData.GetResourceIdList())
		var userExpireTime int64
		if commodityData.GetCommodityType() == uint32(mallPb.CommodityType_COMMODITY_TYPE_SINGLE) {
			if item, ok := itemMap[idStr]; ok {
				userExpireTime = item.GetExpireTime()
			}
		}
		if commodityData.GetCommodityType() == uint32(mallPb.CommodityType_COMMODITY_TYPE_SUIT) {
			if suit, ok := suitMap[idStr]; ok {
				userExpireTime = suit.GetExpireTime()
			}
		}

		if userExpireTime >= util.PERPETUAL_TIME {
			continue
		}

		if commodityData.GetCommodityType() == uint32(pb.CommodityType_COMMODITY_TYPE_SUIT) {
			commodityData.Category = util.CommodityCategory_Suit
		}

		out.CommodityRedDotInfos = append(out.GetCommodityRedDotInfos(), &pb.CommodityRedDotInfo{
			CommodityId:   commodityData.GetCommodityId(),
			RedDotVersion: commodityData.GetRedDotVersion(),
			Category:      commodityData.GetCategory(),
			SubCategory:   commodityData.GetSubCategory(),
			CommodityName: commodityData.GetCommodityName(),
			IsRecommend:   recommendIdMap[commodityData.GetCommodityId()],
		})
	}
	log.DebugWithCtx(ctx, "GetVirtualImageCommodityRedDotDetail in:%+v out:%v", in, out)
	return out, nil

}

func (m *VirtualImageLogicMgr) SendChannelMsg(ctx context.Context, uid, totalPrice uint32) error {
	if m.isRecordRich(ctx, uid) {
		log.InfoWithCtx(ctx, "SendChannelMsg uid:%d isRecordRich", uid)
		return nil
	}
	channelId, err := m.channelOLCli.GetUserChannelId(ctx, uid, uid)
	if err != nil {
		log.Errorf("SendChannelMsg GetUserChannelId failed uid %d err %v", uid, err)
		return err
	}

	if channelId == 0 {
		log.DebugWithCtx(ctx, "SendChannelMsg channelId is 0 uid %d", uid)
		return nil
	}

	// push 公屏消息
	bgCtx := context.Background()
	pushMsg := &numericlogic.RichCharmChangeMsg{
		FromType:    numericlogic.FromType_BuyVirtualImage,
		ChangeType:  numericlogic.ChangeType_Rich,
		Value:       totalPrice,
		Uid:         uid,
		OriginValue: totalPrice,
		FromDesc:    fmt.Sprintf("已成功购买虚拟形象商品"),
	}
	log.InfoWithCtx(bgCtx, "SendChannelMsg channel_id:%d, pushMsg:%+v", channelId, pushMsg)

	chnResp, serr := m.channelCli.GetChannelSimpleInfo(ctx, &channel_go.GetChannelSimpleInfoReq{
		ChannelId: channelId,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "SendChannelMsg GetChannelSimpleInfo err:%s", serr)
		return serr
	}

	pushMsgBytes, _ := proto.Marshal(pushMsg)
	pMsg := &channelMsg.PushMessage{
		ChannelId:   channelId,
		ChannelType: chnResp.GetChannelSimple().GetChannelType(),
		OperatorUid: uid,
		MsgType:     uint32(channelGA.ChannelMsgType_RICH_CHARM_CHANGE),
		PBContent:   pushMsgBytes,
	}
	if err = m.channelMsgSender.SingleCast(ctx, pMsg, []uint32{uid}, nil); err != nil {
		log.ErrorWithCtx(ctx, "SendChannelMsg SingleCast err:%s", err)
	}
	log.InfoWithCtx(ctx, "SendChannelMsg uid:%d, channelId:%d, pushMsg:%+v", uid, channelId, pushMsg)
	return nil
}

func (m *VirtualImageLogicMgr) tryGiveFreeCommodity(ctx context.Context, uid uint32, resourceSexList []uint32) error {
	// 获取免费商品
	freeCommodityResp, err := m.mallClient.GetCommodityDataListByPrice(ctx, &mallPb.GetCommodityDataListByPriceRequest{
		ResourceSexList: resourceSexList,
		Price:           0,
	})
	if err != nil {
		log.WarnWithCtx(ctx, "tryGiveFreeCommodity GetCommodityDataListByPrice uid:%v err:%v", uid, err)
	}

	log.DebugWithCtx(ctx, "tryGiveFreeCommodity uid:%v freeCommodityResp:%+v", uid, freeCommodityResp)
	if len(freeCommodityResp.GetCommodityDataList()) == 0 {
		// 没有免费商品
		return nil
	}

	suitCommodityList := make([]*mallPb.CommodityData, 0)
	itemCommodityList := make([]*mallPb.CommodityData, 0)

	// 获取用户已拥有的物品
	suitMap, itemMap, err := m.getUserVirtualImageList(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "tryGiveFreeCommodity getUserVirtualImageList uid:%v err:%v", uid, err)
		return err
	}

	resIdList := make([]uint32, 0)
	for _, data := range freeCommodityResp.GetCommodityDataList() {
		idStr := util.GetResourceIdStr(data.GetResourceIdList())
		if data.GetCommodityType() == uint32(mallPb.CommodityType_COMMODITY_TYPE_SUIT) {
			if _, ok := suitMap[idStr]; ok {
				// 已拥有
				continue
			}
			suitCommodityList = append(suitCommodityList, data)
			for _, resId := range data.GetResourceIdList() {
				resIdList = append(resIdList, resId)
			}
		}

		if data.GetCommodityType() == uint32(mallPb.CommodityType_COMMODITY_TYPE_SINGLE) {
			if _, ok := itemMap[idStr]; ok {
				// 已拥有
				continue
			}
			itemCommodityList = append(itemCommodityList, data)
			for _, resId := range data.GetResourceIdList() {
				resIdList = append(resIdList, resId)
			}
		}
	}

	if len(suitCommodityList) == 0 && len(itemCommodityList) == 0 {
		// 没有未拥有的免费商品
		return nil
	}

	// 发放免费商品
	now := time.Now()
	sResp, err := m.resourceCli.GetVirtualImageResourcesByIds(ctx, &virtualimageresource.GetVirtualImageResourcesByIdsRequest{
		Ids: resIdList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "tryGiveFreeCommodity getUserVirtualImageList uid:%v err:%v", uid, err)
		return err
	}

	resMap := make(map[uint32]*virtualimageresource.VirtualImageResourceInfo)
	for _, res := range sResp.GetResources() {
		resMap[res.GetId()] = res
	}

	giveItems := make([]*userPb.ItemGiveInfo, 0)
	for _, data := range itemCommodityList {
		if len(data.GetPricePackageList()) == 0 || len(data.GetResourceIdList()) != 1 {
			continue
		}
		pricePackage := data.GetPricePackageList()[0]
		if pricePackage.GetDiscountPrice() != 0 {
			continue
		}

		itemId := data.GetResourceIdList()[0]
		subCate := resMap[itemId].GetSubCategory()
		if subCate == 0 {
			continue
		}

		giveItems = append(giveItems, &userPb.ItemGiveInfo{
			OrderId:     util.GenOrderID(uid, pricePackage.GetPackageId(), data.GetCommodityId(), "mall_free", now),
			DurationSec: int32(pricePackage.GetEffectiveDay() * 24 * 3600),
			ItemPrice:   pricePackage.GetDiscountPrice(),
			Item: &userPb.ItemInfo{
				CfgId:       itemId,
				SubCategory: subCate,
			},
		})
	}

	suitItems := make([]*userPb.SuitGiveInfo, 0)
	for _, data := range suitCommodityList {
		if len(data.GetPricePackageList()) == 0 || len(data.GetResourceIdList()) == 0 {
			continue
		}
		pricePackage := data.GetPricePackageList()[0]
		if pricePackage.GetDiscountPrice() != 0 {
			continue
		}

		items := make([]*userPb.ItemInfo, 0)
		for _, itemId := range data.GetResourceIdList() {
			subCate := resMap[itemId].GetSubCategory()
			if subCate == 0 {
				continue
			}
			items = append(items, &userPb.ItemInfo{
				CfgId:       itemId,
				SubCategory: subCate,
			})
		}

		suitItems = append(suitItems, &userPb.SuitGiveInfo{
			OrderId:             util.GenOrderID(uid, pricePackage.GetPackageId(), data.GetCommodityId(), "mall_free", now),
			DurationSec:         int32(pricePackage.GetEffectiveDay() * 24 * 3600),
			SuitPrice:           pricePackage.GetDiscountPrice(),
			Items:               items,
			SuitName:            data.GetCommodityName(),
			SuitIcon:            data.GetCommodityIcon(),
			LevelIcon:           data.GetLevelIcon(),
			PromotionResourceId: data.GetPromotionalVideoId(),
		})
	}

	giveReq := &userPb.GiveVirtualImageToUserReq{
		Uid:         uid,
		OutsideTime: now.Unix(),
		Source:      uint32(userPb.VirtualImageGainSource_VIRTUAL_IMAGE_GAIN_MALL_FREE),
		Items:       giveItems,
		Suits:       suitItems,
	}

	_, err = m.userCli.GiveVirtualImageToUser(ctx, giveReq)
	if err != nil && protocol.ToServerError(err).Code() != status.ErrVirtualAvatarOrderidExist {
		log.ErrorWithCtx(ctx, "tryGiveFreeCommodity uid:%v GiveVirtualImageToUser fail, err:%v", uid, err)
		return err
	}

	go func() {
		newCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Second*3)
		defer cancel()

		// 免费商品获得通知
		err := m.freeCommodityGainNotify(newCtx, uid, suitCommodityList, itemCommodityList, resMap)
		if err != nil {
			log.ErrorWithCtx(ctx, "tryGiveFreeCommodity freeCommodityGainNotify err:%v", err)
			return
		}
	}()

	log.InfoWithCtx(ctx, "tryGiveFreeCommodity success, uid:%v, suits:%v, items:%v", uid, suitCommodityList, itemCommodityList)
	return nil
}

// freeCommodityGainNotify 免费商品获得通知
func (m *VirtualImageLogicMgr) freeCommodityGainNotify(ctx context.Context, uid uint32, suits, items []*mallPb.CommodityData,
	resourceInfoMap map[uint32]*virtualimageresource.VirtualImageResourceInfo) error {
	cnt := len(suits) + len(items)
	if cnt == 0 {
		return nil
	}

	subCate2Data := make(map[uint32]*mallPb.CommodityData)
	if len(suits) > 0 {
		subCate2Data[uint32(virtualimageresource.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SUIT)] = suits[0]
	}

	for _, item := range items {
		subCate2Data[item.GetSubCategory()] = item
	}

	dataList := make([]*mallPb.CommodityData, 0, len(subCate2Data))
	categoryList := make([]*virtualimageresource.VirtualImageResourceCategoryInfo, 0)
	for _, info := range m.localCache.GetAllResourceCategoryInfos() {
		categoryList = append(categoryList, info)
	}

	// 重排categoryList
	sort.SliceStable(categoryList, func(i, j int) bool {
		// 套装＞服饰＞氛围＞捏脸
		rCate := categoryList[i].GetParentCategoryInfo().GetCategory()
		lCate := categoryList[j].GetParentCategoryInfo().GetCategory()

		if rCate == lCate {
			return false
		}

		if rCate == uint32(virtualimageresource.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SUIT) {
			return true
		}
		if lCate == uint32(virtualimageresource.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SUIT) {
			return false
		}

		if rCate == uint32(virtualimageresource.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_DRESS) {
			return true
		}
		if lCate == uint32(virtualimageresource.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_DRESS) {
			return false
		}

		if rCate == uint32(virtualimageresource.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_ATMOSPHERE) {
			return true
		}
		if lCate == uint32(virtualimageresource.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_ATMOSPHERE) {
			return false
		}

		return false
	})

	// 按照categoryList，对subCate2Data排序
	for _, category := range categoryList {
		if len(category.GetSubCategoryInfoList()) == 0 {
			// 没有子品类，按照父品类排序
			ty := category.GetParentCategoryInfo().GetCategory()
			if data, ok := subCate2Data[ty]; ok {
				dataList = append(dataList, data)
			}
			continue
		}

		for _, subCate := range category.GetSubCategoryInfoList() {
			if data, ok := subCate2Data[subCate.GetSubCategory()]; ok {
				dataList = append(dataList, data)
			}
		}
	}

	levelCfgMap := m.GetLevelIconConfig(ctx)
	now := uint32(time.Now().Unix())

	outDataList := make([]*pb.CommodityData, 0, len(dataList))
	for _, data := range dataList {
		cdata := m.initPbCommodityData(ctx, uid, now, 0, data, resourceInfoMap, nil, nil, nil, levelCfgMap)
		outDataList = append(outDataList, cdata)
	}

	opt := &pb.FreeCommodityGainNotify{
		Uid:               uid,
		Cnt:               uint32(cnt),
		CommodityDataList: outDataList,
	}

	msg, err := proto.Marshal(opt)
	if err != nil {
		log.ErrorWithCtx(ctx, "freeCommodityGainNotify Marshal. opt:%+v, err:%v", opt, err)
		return err
	}

	err = m.pushUserMsg(ctx, msg, uint32(gaPush.PushMessage_VIRTUAL_IMAGE_MALL_FREE_COMMODITY_GAIN_PUSH), []uint32{uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "freeCommodityGainNotify pushUserMsg opt:%+v, err:%v", opt, err)
		return err
	}

	log.DebugWithCtx(ctx, "freeCommodityGainNotify uid:%v, opt:%+v", uid, opt)
	return nil
}

// PushUserMsg 推送用户消息
func (m *VirtualImageLogicMgr) pushUserMsg(ctx context.Context, msg []byte, cmd uint32, uidList []uint32) error {
	pushMessage := &gaPush.PushMessage{
		Cmd:     cmd,
		Content: msg,
	}
	pushMessageBytes, _ := proto.Marshal(pushMessage)

	terminalList := []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT, protocol.WindowsTT}

	notification := &pushPB.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypeList:   terminalList,
		TerminalTypePolicy: push.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:    uint32(pushPB.ProxyNotification_PUSH),
			Payload: pushMessageBytes,
		},
	}

	err := m.pushCli.PushToUsers(ctx, uidList, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushUserMsg err:%v cmd:%v uids:%v", err, cmd, uidList)
	}
	return err
}

// 是否记录财富值
func (m *VirtualImageLogicMgr) isRecordRich(ctx context.Context, uid uint32) bool {
	resp, err := m.NumericGoCli.GetUserRichSwitch(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "isRecordRich GetUserRichSwitch err:%+v", err)
		return true
	}
	return resp.GetEnable()
}
