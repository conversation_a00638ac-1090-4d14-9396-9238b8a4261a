// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-relation/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	mysql "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	store "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-relation/store"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddBindInviteLog mocks base method.
func (m *MockIStore) AddBindInviteLog(arg0 context.Context, arg1 *store.BindInviteLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBindInviteLog", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddBindInviteLog indicates an expected call of AddBindInviteLog.
func (mr *MockIStoreMockRecorder) AddBindInviteLog(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBindInviteLog", reflect.TypeOf((*MockIStore)(nil).AddBindInviteLog), arg0, arg1)
}

// AddUnBindLog mocks base method.
func (m *MockIStore) AddUnBindLog(arg0 context.Context, arg1 *store.UnBindLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUnBindLog", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUnBindLog indicates an expected call of AddUnBindLog.
func (mr *MockIStoreMockRecorder) AddUnBindLog(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUnBindLog", reflect.TypeOf((*MockIStore)(nil).AddUnBindLog), arg0, arg1)
}

// AddUserRelation mocks base method.
func (m *MockIStore) AddUserRelation(arg0 context.Context, arg1 mysql.Txx, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserRelation", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUserRelation indicates an expected call of AddUserRelation.
func (mr *MockIStoreMockRecorder) AddUserRelation(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserRelation", reflect.TypeOf((*MockIStore)(nil).AddUserRelation), arg0, arg1, arg2, arg3)
}

// CheckUserRelation mocks base method.
func (m *MockIStore) CheckUserRelation(arg0 context.Context, arg1, arg2 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserRelation", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserRelation indicates an expected call of CheckUserRelation.
func (mr *MockIStoreMockRecorder) CheckUserRelation(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserRelation", reflect.TypeOf((*MockIStore)(nil).CheckUserRelation), arg0, arg1, arg2)
}

// Close mocks base method.
func (m *MockIStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// CreateRelationInUseTable mocks base method.
func (m *MockIStore) CreateRelationInUseTable(arg0 context.Context, arg1 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRelationInUseTable", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateRelationInUseTable indicates an expected call of CreateRelationInUseTable.
func (mr *MockIStoreMockRecorder) CreateRelationInUseTable(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRelationInUseTable", reflect.TypeOf((*MockIStore)(nil).CreateRelationInUseTable), arg0, arg1)
}

// GetBindInviteByUidAndTargetUid mocks base method.
func (m *MockIStore) GetBindInviteByUidAndTargetUid(arg0 context.Context, arg1, arg2 uint32, arg3, arg4 time.Time) (*store.BindInviteLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindInviteByUidAndTargetUid", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*store.BindInviteLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindInviteByUidAndTargetUid indicates an expected call of GetBindInviteByUidAndTargetUid.
func (mr *MockIStoreMockRecorder) GetBindInviteByUidAndTargetUid(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindInviteByUidAndTargetUid", reflect.TypeOf((*MockIStore)(nil).GetBindInviteByUidAndTargetUid), arg0, arg1, arg2, arg3, arg4)
}

// GetBindInviteLogByInviteId mocks base method.
func (m *MockIStore) GetBindInviteLogByInviteId(arg0 context.Context, arg1 string, arg2 uint32, arg3 time.Time) (*store.BindInviteLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindInviteLogByInviteId", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*store.BindInviteLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindInviteLogByInviteId indicates an expected call of GetBindInviteLogByInviteId.
func (mr *MockIStoreMockRecorder) GetBindInviteLogByInviteId(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindInviteLogByInviteId", reflect.TypeOf((*MockIStore)(nil).GetBindInviteLogByInviteId), arg0, arg1, arg2, arg3)
}

// GetBindInviteLogByTargetUid mocks base method.
func (m *MockIStore) GetBindInviteLogByTargetUid(arg0 context.Context, arg1, arg2 uint32, arg3, arg4 time.Time) ([]*store.BindInviteLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindInviteLogByTargetUid", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*store.BindInviteLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindInviteLogByTargetUid indicates an expected call of GetBindInviteLogByTargetUid.
func (mr *MockIStoreMockRecorder) GetBindInviteLogByTargetUid(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindInviteLogByTargetUid", reflect.TypeOf((*MockIStore)(nil).GetBindInviteLogByTargetUid), arg0, arg1, arg2, arg3, arg4)
}

// GetBindInviteLogListByUid mocks base method.
func (m *MockIStore) GetBindInviteLogListByUid(arg0 context.Context, arg1, arg2 uint32, arg3, arg4 time.Time) ([]*store.BindInviteLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindInviteLogListByUid", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*store.BindInviteLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindInviteLogListByUid indicates an expected call of GetBindInviteLogListByUid.
func (mr *MockIStoreMockRecorder) GetBindInviteLogListByUid(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindInviteLogListByUid", reflect.TypeOf((*MockIStore)(nil).GetBindInviteLogListByUid), arg0, arg1, arg2, arg3, arg4)
}

// GetExpireInviteInfo mocks base method.
func (m *MockIStore) GetExpireInviteInfo(arg0 context.Context, arg1, arg2 time.Time) ([]*store.BindInviteLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExpireInviteInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.BindInviteLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExpireInviteInfo indicates an expected call of GetExpireInviteInfo.
func (mr *MockIStoreMockRecorder) GetExpireInviteInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExpireInviteInfo", reflect.TypeOf((*MockIStore)(nil).GetExpireInviteInfo), arg0, arg1, arg2)
}

// GetRelationInUse mocks base method.
func (m *MockIStore) GetRelationInUse(arg0 context.Context, arg1 uint32) (*store.RelationInUse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelationInUse", arg0, arg1)
	ret0, _ := ret[0].(*store.RelationInUse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationInUse indicates an expected call of GetRelationInUse.
func (mr *MockIStoreMockRecorder) GetRelationInUse(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationInUse", reflect.TypeOf((*MockIStore)(nil).GetRelationInUse), arg0, arg1)
}

// GetUserBindCnt mocks base method.
func (m *MockIStore) GetUserBindCnt(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserBindCnt", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserBindCnt indicates an expected call of GetUserBindCnt.
func (mr *MockIStoreMockRecorder) GetUserBindCnt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBindCnt", reflect.TypeOf((*MockIStore)(nil).GetUserBindCnt), arg0, arg1)
}

// GetUserRelationByUid mocks base method.
func (m *MockIStore) GetUserRelationByUid(arg0 context.Context, arg1 uint32) ([]*store.UserRelation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRelationByUid", arg0, arg1)
	ret0, _ := ret[0].([]*store.UserRelation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserRelationByUid indicates an expected call of GetUserRelationByUid.
func (mr *MockIStoreMockRecorder) GetUserRelationByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRelationByUid", reflect.TypeOf((*MockIStore)(nil).GetUserRelationByUid), arg0, arg1)
}

// Transaction mocks base method.
func (m *MockIStore) Transaction(arg0 context.Context, arg1 func(mysql.Txx) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transaction", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Transaction indicates an expected call of Transaction.
func (mr *MockIStoreMockRecorder) Transaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transaction", reflect.TypeOf((*MockIStore)(nil).Transaction), arg0, arg1)
}

// UpdateBindInviteLogStatus mocks base method.
func (m *MockIStore) UpdateBindInviteLogStatus(arg0 context.Context, arg1 mysql.Txx, arg2 string, arg3, arg4 uint32, arg5 time.Time) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBindInviteLogStatus", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBindInviteLogStatus indicates an expected call of UpdateBindInviteLogStatus.
func (mr *MockIStoreMockRecorder) UpdateBindInviteLogStatus(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBindInviteLogStatus", reflect.TypeOf((*MockIStore)(nil).UpdateBindInviteLogStatus), arg0, arg1, arg2, arg3, arg4, arg5)
}

// UpdateRelationInUse mocks base method.
func (m *MockIStore) UpdateRelationInUse(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRelationInUse", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRelationInUse indicates an expected call of UpdateRelationInUse.
func (mr *MockIStoreMockRecorder) UpdateRelationInUse(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRelationInUse", reflect.TypeOf((*MockIStore)(nil).UpdateRelationInUse), arg0, arg1, arg2)
}

// UpdateRelationInUseWithCheck mocks base method.
func (m *MockIStore) UpdateRelationInUseWithCheck(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRelationInUseWithCheck", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRelationInUseWithCheck indicates an expected call of UpdateRelationInUseWithCheck.
func (mr *MockIStoreMockRecorder) UpdateRelationInUseWithCheck(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRelationInUseWithCheck", reflect.TypeOf((*MockIStore)(nil).UpdateRelationInUseWithCheck), arg0, arg1, arg2, arg3)
}

// UpdateUserRelationBindStatus mocks base method.
func (m *MockIStore) UpdateUserRelationBindStatus(arg0 context.Context, arg1 mysql.Txx, arg2, arg3, arg4 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserRelationBindStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserRelationBindStatus indicates an expected call of UpdateUserRelationBindStatus.
func (mr *MockIStoreMockRecorder) UpdateUserRelationBindStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserRelationBindStatus", reflect.TypeOf((*MockIStore)(nil).UpdateUserRelationBindStatus), arg0, arg1, arg2, arg3, arg4)
}
