package reminder

//go:generate quicksilver-cli test interface ../reminder
//go:generate mockgen -destination=./mocks/user_virtual_avatar.go -package=mocks golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/reminder IMgr

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app/virtual_image_logic"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/conf"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/anti-corruption-layer"
    display_switch "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/display-switch"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/reminder/cache"
    user_virtual_avatar "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-virtual-avatar"
    "sync"
    "time"
)

const (
    displayReminderJumpUrl      = "tt://m.52tt.com/show_virtual_image_shop?tab_type=0&from_source=1"
    displayReminderContent      = "当前虚拟形象为默认状态，无法对外展示    "
    displayDelayReminderContent = "当前虚拟形象为默认状态，无法对外展示    "
)

type Mgr struct {
    cache  cache.ICache
    timerD *timer.Timer

    wg       sync.WaitGroup
    shutDown chan struct{}

    bc            conf.IBusinessConfManager
    userVA        user_virtual_avatar.IMgr
    acLayer       anti_corruption_layer.IMgr
    displaySwitch display_switch.IMgr
}

func NewMgr(bc conf.IBusinessConfManager, cacheClient redis.Cmdable,
    userVA user_virtual_avatar.IMgr, acLayer anti_corruption_layer.IMgr, displaySwitch display_switch.IMgr) (*Mgr, error) {
    var err error
    redisCli := cache.NewCache(cacheClient)

    m := &Mgr{
        cache:         redisCli,
        shutDown:      make(chan struct{}),
        bc:            bc,
        acLayer:       acLayer,
        userVA:        userVA,
        displaySwitch: displaySwitch,
    }

    err = m.startTimer()
    if err != nil {
        log.Errorf("NewMgr startTimer err:%v", err)
        return m, err
    }

    return m, nil
}

func (m *Mgr) Stop() {
    m.timerD.Stop()
    _ = m.cache.Close()
    m.shutDown <- struct{}{}
    m.wg.Wait()
}

// CannotDisplayReminder 用户无法外显状态提醒
// 用户虚拟形象从可外显状态变为不可外显状态时（手动关闭外显总开关、主动全部替换成基础组件不算），需要提醒用户
func (m *Mgr) CannotDisplayReminder(ctx context.Context, uid uint32) error {
    inuseMap, err := m.userVA.GetUserInUseMap(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "CannotDisplayReminder fail to GetUserInUseMap. uid:%d, err:%v", uid, err)
        return err
    }

    // 用户还有在佩戴的虚拟形象，不需要提醒
    if len(inuseMap) > 0 {
        return nil
    }

    switchMap, err := m.displaySwitch.GetUserDisplaySwitchMap(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "CannotDisplayReminder fail to GetUserDisplaySwitchMap. uid:%d, err:%v", uid, err)
        return err
    }

    // 用户关闭了外显，不需要提醒
    if info, ok := switchMap[uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MAIN)]; !ok || !info.Switch {
        return nil
    }

    // 推送用户无法外显提醒
    err = m.pushUserCannotDisplayReminderMsg(ctx, uid, true, displayReminderContent)
    if err != nil {
        log.ErrorWithCtx(ctx, "CannotDisplayReminder fail to pushUserCannotDisplayReminderMsg. uid:%d, err:%v", uid, err)
        return err
    }

    // 兼容用户穿戴物品全部过期时，出现了不可外显弹窗但麦上仍能看见虚拟形象问题
    // 推送房间广播通知，更新麦上用户虚拟形象信息，展示头像
    err = m.acLayer.PushChannelUserVIChange(ctx, 0, &virtual_image_user.UserInuseItemInfo{
        Uid: uid,
    }, true)
    if err != nil {
        log.ErrorWithCtx(ctx, "UserInuseVIChangeChannelNotify failed to PushChannelUserVIChange. uid:%v, err:%v", uid, err)
        return err
    }

    log.DebugWithCtx(ctx, "CannotDisplayReminder uid:%d", uid)
    return nil
}

// CannotDisplayDelayReminder 用户无法外显延后提醒
// 只提示曾经外显过并且当前没有关闭外显虚拟形象总开关的用户
func (m *Mgr) CannotDisplayDelayReminder(ctx context.Context, uid uint32) error {
    inuseMap, err := m.userVA.GetUserInUseMap(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "CannotDisplayDelayReminder fail to GetUserInUseMap. uid:%d, err:%v", uid, err)
        return err
    }

    // 用户还有在佩戴的虚拟形象，不需要提醒
    if len(inuseMap) > 0 {
        return nil
    }

    switchMap, err := m.displaySwitch.GetUserDisplaySwitchMap(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "CannotDisplayDelayReminder fail to GetUserDisplaySwitchMap. uid:%d, err:%v", uid, err)
        return err
    }

    // 用户关闭了外显，不需要提醒
    if info, ok := switchMap[uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MAIN)]; !ok || !info.Switch {
        return nil
    }

    now := time.Now()
    // 添加用户延后提醒信息
    err = m.cache.AddUserDelayDisplayReminder(ctx, uid, now.Unix())
    if err != nil {
        log.WarnWithCtx(ctx, "CannotDisplayDelayReminder fail to AddUserDelayDisplayReminder. uid:%d, err:%v", uid, err)
    }

    // 设置用户房间公屏提醒信息
    err = m.cache.SetUserChannelImDisplayReminder(ctx, uid, &cache.UserChannelImReminder{
        LastTime: now.Unix(),
    })
    if err != nil {
        log.WarnWithCtx(ctx, "CannotDisplayDelayReminder fail to SetUserChannelImDisplayReminder. uid:%d, err:%v", uid, err)
    }

    log.DebugWithCtx(ctx, "CannotDisplayDelayReminder uid:%d", uid)
    return nil
}

func (m *Mgr) pushUserCannotDisplayReminderMsg(ctx context.Context, uid uint32, offlineDelayPop bool, reminderContent string) error {
    var err error

    // 推送im消息
    err = m.acLayer.SendIMMsgWithHighLight(ctx, uid, reminderContent+" 前往搭配＞", "前往搭配＞", displayReminderJumpUrl)
    if err != nil {
        log.WarnWithCtx(ctx, "pushUserCannotDisplayReminderMsg SendIMMsgWithHighLight err:%v", err)
    }

    if offlineDelayPop {
        online, err := m.acLayer.CheckUserOnline(ctx, uid)
        if err != nil {
            log.WarnWithCtx(ctx, "pushUserCannotDisplayReminderMsg CheckUserOnline err:%v", err)
        }

        if !online {
            // 下次上线推送
            err = m.cache.SetUserLoginDisplayReminder(ctx, uid)
            if err != nil {
                log.WarnWithCtx(ctx, "pushUserCannotDisplayReminderMsg SetUserLoginDisplayReminder err:%v", err)
            }
            return nil
        }
    }

    // 推送弹窗
    err = m.acLayer.PushUserDisplayStatusReminderPop(ctx, &virtual_image_logic.VirtualImageDisplayStatusPop{
        Uid:              uid,
        Content:          reminderContent,
        HighLightContent: "前往搭配",
        JumpUrl:          displayReminderJumpUrl,
    })
    if err != nil {
        log.WarnWithCtx(ctx, "pushUserCannotDisplayReminderMsg PushUserDisplayStatusReminderPop err:%v", err)
    }

    log.DebugWithCtx(ctx, "pushUserCannotDisplayReminderMsg uid:%d", uid)
    return nil
}

// HandleUserLoginReminder 用户登录提醒
func (m *Mgr) HandleUserLoginReminder(ctx context.Context, uid uint32) error {
    isExist, err := m.cache.GetUserLoginDisplayReminder(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleUserLoginReminder fail to GetUserLoginDisplayReminder. uid:%d, err:%v", uid, err)
        return err
    }

    if !isExist {
        return nil
    }

    // 推送弹窗
    err = m.acLayer.PushUserDisplayStatusReminderPop(ctx, &virtual_image_logic.VirtualImageDisplayStatusPop{
        Uid:              uid,
        Content:          displayReminderContent,
        HighLightContent: "前往搭配",
        JumpUrl:          displayReminderJumpUrl,
    })
    if err != nil {
        log.WarnWithCtx(ctx, "HandleUserLoginReminder PushUserDisplayStatusReminderPop err:%v", err)
    }

    err = m.cache.RemoveUserLoginDisplayReminder(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleUserLoginReminder fail to RemoveUserLoginDisplayReminder. uid:%d, err:%v", uid, err)
        return err
    }

    log.DebugWithCtx(ctx, "HandleUserLoginReminder uid:%d", uid)
    return nil
}

// RemoveUserDisplayReminder 移除用户外显提醒
func (m *Mgr) RemoveUserDisplayReminder(ctx context.Context, uid uint32) {
    err := m.cache.RemoveUserLoginDisplayReminder(ctx, uid)
    if err != nil {
        log.WarnWithCtx(ctx, "RemoveUserDisplayReminder fail to RemoveUserLoginDisplayReminder. uid:%d, err:%v", uid, err)
    }

    err = m.cache.RemoveUserChannelImDisplayReminder(ctx, uid)
    if err != nil {
        log.WarnWithCtx(ctx, "RemoveUserDisplayReminder fail to RemoveUserChannelImDisplayReminder. uid:%d, err:%v", uid, err)
    }

    err = m.cache.RemoveDelayDisplayReminder(ctx, uid)
    if err != nil {
        log.WarnWithCtx(ctx, "RemoveUserDisplayReminder fail to RemoveDelayDisplayReminder. uid:%d, err:%v", uid, err)
    }

    err = m.cache.RemoveUserInChannelPool(ctx, uid)
    if err != nil {
        log.WarnWithCtx(ctx, "RemoveUserDisplayReminder fail to RemoveUserInChannelPool. uid:%d, err:%v", uid, err)
    }

    log.DebugWithCtx(ctx, "RemoveUserDisplayReminder uid:%d", uid)
}

// HandleUserEnterChannel 用户进入房间
func (m *Mgr) HandleUserEnterChannel(ctx context.Context, uid uint32) error {
    _, exist, err := m.cache.GetUserChannelImDisplayReminder(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleUserEnterChannel fail to GetUserChannelImDisplayReminder. uid:%d, err:%v", uid, err)
        return err
    }

    if !exist {
        // 用户无外显提醒信息
        return nil
    }

    // 加入在房池子
    err = m.cache.AddUserInChannelPool(ctx, uid, time.Now().Unix())
    if err != nil {
        log.WarnWithCtx(ctx, "HandleUserEnterChannel fail to AddUserInChannelPool. uid:%d, err:%v", uid, err)
    }

    return nil
}

// HandleUserLeaveChannel 用户离开房间
func (m *Mgr) HandleUserLeaveChannel(ctx context.Context, uid uint32) error {
    err := m.cache.RemoveUserInChannelPool(ctx, uid)
    if err != nil {
        log.WarnWithCtx(ctx, "HandleUserLeaveChannel fail to RemoveUserInChannelPool. uid:%d, err:%v", uid, err)
    }

    return nil
}

// FirstUseReminder 低版本用户首次使用佩戴提醒
func (m *Mgr) FirstUseReminder(ctx context.Context, uid uint32) error {
    serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.DebugWithCtx(ctx, "FirstUseReminder fail to ServiceInfoFromContext. uid:%d", uid)
        return nil
    }
    opUid := serviceInfo.UserID
    if uid != opUid {
        // 非用户操作
        log.DebugWithCtx(ctx, "FirstUseReminder uid:%d != opUid:%d", uid, opUid)
        return nil
    }

    if serviceInfo.ClientType == protocol.ClientTypeANDROID && serviceInfo.ClientVersion >= protocol.FormatClientVersion(6, 61, 5) ||
        serviceInfo.ClientType == protocol.ClientTypeIOS && serviceInfo.ClientVersion >= protocol.FormatClientVersion(6, 61, 5) ||
        serviceInfo.ClientType == protocol.ClientTypePcTT && serviceInfo.ClientVersion >= protocol.FormatClientVersion(2, 3, 5) {

        // 新版本不用提醒
        return nil
    }

    text := m.bc.GetFirstUseReminder()
    if text == "" {
        return nil
    }

    _, exist, err := m.cache.GetUserFirstUseReminderFlag(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "FirstUseReminder fail to GetUserFirstUseReminderFlag. uid:%d, err:%v", uid, err)
        return err
    }

    if exist {
        return nil
    }

    // 推送首次使用佩戴提醒
    err = m.acLayer.SendIMMsgWithHighLight(ctx, uid, text, "", "")
    if err != nil {
        log.ErrorWithCtx(ctx, "FirstUseReminder fail to SendIMMsgWithHighLight. uid:%d, err:%v", uid, err)
        return err
    }

    // 设置首次使用佩戴提醒标记
    err = m.cache.SetUserFirstUseReminderFlag(ctx, uid, time.Now().Unix())
    if err != nil {
        log.ErrorWithCtx(ctx, "FirstUseReminder fail to SetUserFirstUseReminderFlag. uid:%d, err:%v", uid, err)
        return err
    }

    return nil
}