// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-virtual-avatar/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	cache "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-virtual-avatar/cache"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// BatGetUserVirtualImageInUseList mocks base method.
func (m *MockICache) BatGetUserVirtualImageInUseList(arg0 context.Context, arg1 []uint32) (map[uint32][]*cache.UserVAInUse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetUserVirtualImageInUseList", arg0, arg1)
	ret0, _ := ret[0].(map[uint32][]*cache.UserVAInUse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetUserVirtualImageInUseList indicates an expected call of BatGetUserVirtualImageInUseList.
func (mr *MockICacheMockRecorder) BatGetUserVirtualImageInUseList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetUserVirtualImageInUseList", reflect.TypeOf((*MockICache)(nil).BatGetUserVirtualImageInUseList), arg0, arg1)
}

// BatSetUserVirtualImageInUseList mocks base method.
func (m *MockICache) BatSetUserVirtualImageInUseList(arg0 context.Context, arg1 map[uint32][]*cache.UserVAInUse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatSetUserVirtualImageInUseList", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatSetUserVirtualImageInUseList indicates an expected call of BatSetUserVirtualImageInUseList.
func (mr *MockICacheMockRecorder) BatSetUserVirtualImageInUseList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatSetUserVirtualImageInUseList", reflect.TypeOf((*MockICache)(nil).BatSetUserVirtualImageInUseList), arg0, arg1)
}

// BatchGetUserOrientation mocks base method.
func (m *MockICache) BatchGetUserOrientation(arg0 context.Context, arg1 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserOrientation", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserOrientation indicates an expected call of BatchGetUserOrientation.
func (mr *MockICacheMockRecorder) BatchGetUserOrientation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserOrientation", reflect.TypeOf((*MockICache)(nil).BatchGetUserOrientation), arg0, arg1)
}

// BatchGetUserVirtualImageList mocks base method.
func (m *MockICache) BatchGetUserVirtualImageList(arg0 context.Context, arg1 []uint32) (map[uint32][]*cache.UserVirtualImage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserVirtualImageList", arg0, arg1)
	ret0, _ := ret[0].(map[uint32][]*cache.UserVirtualImage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserVirtualImageList indicates an expected call of BatchGetUserVirtualImageList.
func (mr *MockICacheMockRecorder) BatchGetUserVirtualImageList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserVirtualImageList", reflect.TypeOf((*MockICache)(nil).BatchGetUserVirtualImageList), arg0, arg1)
}

// BatchSetUserVirtualImageList mocks base method.
func (m *MockICache) BatchSetUserVirtualImageList(arg0 context.Context, arg1 map[uint32][]*cache.UserVirtualImage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetUserVirtualImageList", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSetUserVirtualImageList indicates an expected call of BatchSetUserVirtualImageList.
func (mr *MockICacheMockRecorder) BatchSetUserVirtualImageList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetUserVirtualImageList", reflect.TypeOf((*MockICache)(nil).BatchSetUserVirtualImageList), arg0, arg1)
}

// ClearUserCustomSuitList mocks base method.
func (m *MockICache) ClearUserCustomSuitList(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearUserCustomSuitList", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearUserCustomSuitList indicates an expected call of ClearUserCustomSuitList.
func (mr *MockICacheMockRecorder) ClearUserCustomSuitList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearUserCustomSuitList", reflect.TypeOf((*MockICache)(nil).ClearUserCustomSuitList), arg0, arg1)
}

// ClearUserOwnSuitList mocks base method.
func (m *MockICache) ClearUserOwnSuitList(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearUserOwnSuitList", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearUserOwnSuitList indicates an expected call of ClearUserOwnSuitList.
func (mr *MockICacheMockRecorder) ClearUserOwnSuitList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearUserOwnSuitList", reflect.TypeOf((*MockICache)(nil).ClearUserOwnSuitList), arg0, arg1)
}

// ClearUserVirtualImageList mocks base method.
func (m *MockICache) ClearUserVirtualImageList(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearUserVirtualImageList", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearUserVirtualImageList indicates an expected call of ClearUserVirtualImageList.
func (mr *MockICacheMockRecorder) ClearUserVirtualImageList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearUserVirtualImageList", reflect.TypeOf((*MockICache)(nil).ClearUserVirtualImageList), arg0, arg1)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// DelUserVirtualImageInUseList mocks base method.
func (m *MockICache) DelUserVirtualImageInUseList(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserVirtualImageInUseList", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserVirtualImageInUseList indicates an expected call of DelUserVirtualImageInUseList.
func (mr *MockICacheMockRecorder) DelUserVirtualImageInUseList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserVirtualImageInUseList", reflect.TypeOf((*MockICache)(nil).DelUserVirtualImageInUseList), arg0, arg1)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetUserCustomSuitList mocks base method.
func (m *MockICache) GetUserCustomSuitList(arg0 context.Context, arg1 uint32) ([]*cache.UserCustomSuit, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserCustomSuitList", arg0, arg1)
	ret0, _ := ret[0].([]*cache.UserCustomSuit)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserCustomSuitList indicates an expected call of GetUserCustomSuitList.
func (mr *MockICacheMockRecorder) GetUserCustomSuitList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCustomSuitList", reflect.TypeOf((*MockICache)(nil).GetUserCustomSuitList), arg0, arg1)
}

// GetUserOrientation mocks base method.
func (m *MockICache) GetUserOrientation(arg0 context.Context, arg1 uint32) (uint32, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserOrientation", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserOrientation indicates an expected call of GetUserOrientation.
func (mr *MockICacheMockRecorder) GetUserOrientation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserOrientation", reflect.TypeOf((*MockICache)(nil).GetUserOrientation), arg0, arg1)
}

// GetUserOwnSuitList mocks base method.
func (m *MockICache) GetUserOwnSuitList(arg0 context.Context, arg1 uint32) ([]*cache.UserOwnSuit, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserOwnSuitList", arg0, arg1)
	ret0, _ := ret[0].([]*cache.UserOwnSuit)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserOwnSuitList indicates an expected call of GetUserOwnSuitList.
func (mr *MockICacheMockRecorder) GetUserOwnSuitList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserOwnSuitList", reflect.TypeOf((*MockICache)(nil).GetUserOwnSuitList), arg0, arg1)
}

// GetUserVirtualImageInUseList mocks base method.
func (m *MockICache) GetUserVirtualImageInUseList(arg0 context.Context, arg1 uint32) ([]*cache.UserVAInUse, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserVirtualImageInUseList", arg0, arg1)
	ret0, _ := ret[0].([]*cache.UserVAInUse)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserVirtualImageInUseList indicates an expected call of GetUserVirtualImageInUseList.
func (mr *MockICacheMockRecorder) GetUserVirtualImageInUseList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserVirtualImageInUseList", reflect.TypeOf((*MockICache)(nil).GetUserVirtualImageInUseList), arg0, arg1)
}

// GetUserVirtualImageList mocks base method.
func (m *MockICache) GetUserVirtualImageList(arg0 context.Context, arg1 uint32) ([]*cache.UserVirtualImage, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserVirtualImageList", arg0, arg1)
	ret0, _ := ret[0].([]*cache.UserVirtualImage)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserVirtualImageList indicates an expected call of GetUserVirtualImageList.
func (mr *MockICacheMockRecorder) GetUserVirtualImageList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserVirtualImageList", reflect.TypeOf((*MockICache)(nil).GetUserVirtualImageList), arg0, arg1)
}

// SetUserCustomSuitList mocks base method.
func (m *MockICache) SetUserCustomSuitList(arg0 context.Context, arg1 uint32, arg2 []*cache.UserCustomSuit) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserCustomSuitList", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserCustomSuitList indicates an expected call of SetUserCustomSuitList.
func (mr *MockICacheMockRecorder) SetUserCustomSuitList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserCustomSuitList", reflect.TypeOf((*MockICache)(nil).SetUserCustomSuitList), arg0, arg1, arg2)
}

// SetUserOrientation mocks base method.
func (m *MockICache) SetUserOrientation(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserOrientation", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserOrientation indicates an expected call of SetUserOrientation.
func (mr *MockICacheMockRecorder) SetUserOrientation(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserOrientation", reflect.TypeOf((*MockICache)(nil).SetUserOrientation), arg0, arg1, arg2)
}

// SetUserOwnSuitList mocks base method.
func (m *MockICache) SetUserOwnSuitList(arg0 context.Context, arg1 uint32, arg2 []*cache.UserOwnSuit) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserOwnSuitList", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserOwnSuitList indicates an expected call of SetUserOwnSuitList.
func (mr *MockICacheMockRecorder) SetUserOwnSuitList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserOwnSuitList", reflect.TypeOf((*MockICache)(nil).SetUserOwnSuitList), arg0, arg1, arg2)
}

// SetUserVirtualImageInUseList mocks base method.
func (m *MockICache) SetUserVirtualImageInUseList(arg0 context.Context, arg1 uint32, arg2 []*cache.UserVAInUse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserVirtualImageInUseList", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserVirtualImageInUseList indicates an expected call of SetUserVirtualImageInUseList.
func (mr *MockICacheMockRecorder) SetUserVirtualImageInUseList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserVirtualImageInUseList", reflect.TypeOf((*MockICache)(nil).SetUserVirtualImageInUseList), arg0, arg1, arg2)
}

// SetUserVirtualImageList mocks base method.
func (m *MockICache) SetUserVirtualImageList(arg0 context.Context, arg1 uint32, arg2 []*cache.UserVirtualImage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserVirtualImageList", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserVirtualImageList indicates an expected call of SetUserVirtualImageList.
func (mr *MockICacheMockRecorder) SetUserVirtualImageList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserVirtualImageList", reflect.TypeOf((*MockICache)(nil).SetUserVirtualImageList), arg0, arg1, arg2)
}
