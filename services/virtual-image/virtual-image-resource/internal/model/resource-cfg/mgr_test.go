package resource_cfg

import (
	"context"
	"fmt"
	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/ttversion"
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/virtual_image_logic"
	virtualImageResourcePb "golang.52tt.com/protocol/services/virtual-image-resource"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/resource-cfg/store"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"io"
	"os"
	"sort"
	"strings"
	"sync"
	"testing"
	"time"

	mocksConf "golang.52tt.com/services/virtual-image/virtual-image-resource/internal/conf/mocks"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/resource-cfg/mocks"

	"archive/zip"
)

var (
	testMgr *Mgr

	ctx       = context.Background()
	mockStore *mocks.MockIStore
	mockConf  *mocksConf.MockIBusinessConfManager

	testUid        = uint32(1)
	testVaId       = uint32(1)
	testUserItemId = uint32(1)
)

func initTestMgr(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockStore = mocks.NewMockIStore(ctrl)
	mockConf = mocksConf.NewMockIBusinessConfManager(ctrl)

	testMgr = &Mgr{
		//store:    mockStore,
		bc:       mockConf,
		wg:       sync.WaitGroup{},
		shutDown: make(chan struct{}),
		cacheList: []*store.VirtualImageResourceInfo{
			{
				ID: 1,
			},
			{
				ID: 2,
			},
		},
	}
}

func TestGetVirtualImageResourceBySuit(t *testing.T) {
	minVersion := ttversion.Parse("", strings.Split(MINI_CLIENT_VERSION, ",")...)

	currentVersion := "ios-6.61.5,pc-2.4.5,android-6.62.5,car-6.61.5,mini-6.61.5"
	currentTTVersion := ttversion.Parse("", strings.Split(currentVersion, ",")...)

	currentTTVersion.Android()
	atleast := minVersion.Atleast(protocol.ClientTypeANDROID, uint32(currentTTVersion.Android()))

	fmt.Println(atleast)

	cacheList := make([]*store.VirtualImageResourceInfo, 0)

	for i := 0; i < 10; i++ {
		cacheList = append(cacheList, &store.VirtualImageResourceInfo{
			ID:         uint32(i),
			UpdateTime: time.Now(),
			Version:    uint32(i),
		})
	}

	for i := 10; i < 20; i++ {
		cacheList = append(cacheList, &store.VirtualImageResourceInfo{
			ID:         uint32(i),
			UpdateTime: time.Now().Add(time.Second * time.Duration(i)),
			Version:    uint32(i),
		})
	}

	for i := 20; i < 30; i++ {
		cacheList = append(cacheList, &store.VirtualImageResourceInfo{
			ID:         uint32(i),
			UpdateTime: time.Now().Add(-time.Second),
			Version:    uint32(i),
		})
	}

	sort.SliceStable(cacheList, func(i, j int) bool {
		return cacheList[i].UpdateTime.Unix() > cacheList[j].UpdateTime.Unix()
	})

	index := BinarySearch(cacheList, time.Now().Add(-time.Second))
	fmt.Println("BinarySearch", index)

	sort.SliceStable(cacheList, func(i, j int) bool {
		return cacheList[i].Version > cacheList[j].Version
	})

	index = BinarySearchByVersion(cacheList, 30)
	fmt.Println("BinarySearch", index)
	for i := 0; i < index; i++ {
		fmt.Println(cacheList[i].ID, cacheList[i].Version)
	}

}

func TestMgr_GetClientListByPage(t *testing.T) {
	ctx = metadata.AppendToOutgoingContext(ctx, "x-qw-traffic-mark", "tt-dev-mars")
	resourceClient := virtualImageResourcePb.MustNewClientTo(ctx, "10.34.4.7:80", grpc.WithBlock(), grpc.WithAuthority("virtual-image-resource.52tt.local"))

	var offset uint32

	var resourceList virtualImageResourcePb.GetClientListByPageResponse
	for {
		resp, err := resourceClient.GetClientListByPage(ctx, &virtualImageResourcePb.GetClientListByPageRequest{
			Offset:   offset,
			Limit:    100,
			LatestId: 0,
		})

		if err != nil {
			fmt.Println("Error:", err)
			break
		}
		resourceList.Resources = append(resourceList.Resources, resp.Resources...)
		resourceList.LatestId = resp.LatestId

		if len(resp.Resources) < 100 {
			//return
			break
		}

		offset += uint32(100)
	}

	var resourceLogic virtual_image_logic.GetResourceListResponse
	resourceLogic.BaseResp = &app.BaseResp{
		Ret:          0,
		ErrMsg:       "成功",
		AppId:        0,
		ErrInfo:      nil,
		SuccessMsg:   "",
		Ip:           0,
		RequestId:    nil,
		FaceAuthInfo: nil,
	}
	resourceLogic.LatestVersion = resourceList.LatestId

	for _, data := range resourceList.Resources {
		resourceLogic.Resources = append(resourceLogic.Resources, &virtual_image_logic.VirtualImageResourceInfo{
			Id:               data.GetId(),
			SkinName:         data.GetSkinName(),
			ResourceType:     data.GetResourceType(),
			ResourceName:     data.GetResourceName(),
			ResourceUrl:      data.GetResourceUrl(),
			Version:          data.GetUpdateTime(),
			Essential:        data.GetEssential(),
			ShelfTime:        data.GetShelfTime(),
			ExpireTime:       data.GetExpireTime(),
			EncryptKey:       data.GetEncryptKey(),
			DisplayName:      data.GetDisplayName(),
			IconUrl:          data.GetIconUrl(),
			Category:         data.GetCategory(),
			SubCategory:      data.GetSubCategory(),
			Md5:              data.GetMd5(),
			Level:            data.GetLevel(),
			LevelIcon:        data.GetLevelIcon(),
			Sex:              data.GetSex(),
			LevelWebp:        data.GetLevelWebp(),
			ScaleAble:        data.GetScaleAble(),
			DefaultAnimation: data.GetDefaultAnimation(),
			ResourcePrefix:   data.GetResourcePrefix(),
			CustomMap:        data.GetCustomMap(),
		})
	}

	// 写入ZIP文件
	zipFileName := fmt.Sprintf("resource_list.zip")
	zipFile, err := os.Create(zipFileName)
	if err != nil {
		fmt.Println("Error creating zip file:", err)
		return
	}

	zipWriter := zip.NewWriter(zipFile)

	// 创建一个新的文件条目在ZIP文件中
	fileInZip, err := zipWriter.Create("resource_list")
	if err != nil {
		fmt.Println("Error creating file in zip:", err)
		return
	}

	// 将资源列表的二进制数据写入ZIP文件
	resourceBytes, err := proto.Marshal(&resourceLogic)
	if err != nil {
		fmt.Println("Error marshaling resource list:", err)
		return
	}

	writeSize, err := fileInZip.Write(resourceBytes)
	if err != nil {
		fmt.Println("Error writing to zip file:", err)
		return
	}
	defer zipFile.Close()
	defer zipWriter.Close()
	fmt.Println("ZIP file created successfully.", writeSize)

}

func TestMgr_GetClientListByPage2(t *testing.T) {

	// 读取ZIP文件的内容
	file, err := os.Open("resource_list.zip")
	if err != nil {
		fmt.Println("uploadImage fail to ReadFile.err", err)
		return
	}

	fileBytes, err := io.ReadAll(file)
	fmt.Println("ReadFile size: ", len(fileBytes), err)

	// 读取ZIP文件以验证写入
	reader, err := zip.OpenReader("resource_list.zip")
	if err != nil {
		fmt.Println("Error reading zip file:", err)
		return
	}

	defer reader.Close()

	// 读取ZIP文件的内容
	for _, file := range reader.File {
		fileReader, err := file.Open()
		if err != nil {
			fmt.Println("Error opening file in zip:", err)
			continue
		}
		defer fileReader.Close()

		fileBytes, err := io.ReadAll(fileReader)
		if err != nil {
			fmt.Println("Error reading file in zip:", err)
			continue
		}

		// 验证写入的内容
		fmt.Println("size:  md5 ", len(fileBytes))

		var resourceLogic virtual_image_logic.GetResourceListResponse
		err = proto.Unmarshal(fileBytes, &resourceLogic)
		if err != nil {
			panic(err)
		}
		sort.SliceStable(resourceLogic.Resources, func(i, j int) bool {
			return resourceLogic.Resources[i].GetId() > resourceLogic.Resources[i].GetId()
		})

		fmt.Println("GetClientListByPage2", len(resourceLogic.Resources))
		for _, data := range resourceLogic.Resources {
			if data.Id == 26880 {
				fmt.Println("GetClientListByPage2", data.GetId(), data.GetSkinMap(), data.GetIosSkinMap())
			}
		}
	}

	os.Remove("resource_list.zip")

}
