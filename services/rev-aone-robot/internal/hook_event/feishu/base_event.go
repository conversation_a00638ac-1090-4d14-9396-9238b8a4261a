package feishu

import (
	"context"
	"fmt"
	"math/rand"

	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkdispatcher "github.com/larksuite/oapi-sdk-go/v3/event/dispatcher"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"golang.52tt.com/pkg/log"
	feishuapi "golang.52tt.com/services/rev-aone-robot/api/feishu"
	eventcommon "golang.52tt.com/services/rev-aone-robot/internal/common"
	"golang.52tt.com/services/rev-aone-robot/internal/config"
	"golang.52tt.com/services/rev-aone-robot/internal/hook_event/feishu/cmdhandler"
	"golang.52tt.com/services/rev-aone-robot/internal/store"
)

type LarkEvent struct {
	LarkEventHandler *larkdispatcher.EventDispatcher
	feishuApi        *feishuapi.FeishuApi
	cmdMgr           *cmdhandler.LarkCmdHandlerMgr
}

func NewEvent(conf *config.LarkConfig, apiConfig *config.GitRestApiConfig, dbMgr *store.AoneStoreMgr) *LarkEvent {

	larkEv := &LarkEvent{}

	// 注册飞书消息处理器
	// NewEventDispatcher的参数token与key都是为了对事件进行签名验证和解密，如果控制台没有设置加密，默认可以传递为空串
	larkEv.LarkEventHandler = larkdispatcher.NewEventDispatcher(conf.VerifyToken, conf.EncryptKey)

	larkEv.LarkEventHandler.OnP2MessageReceiveV1(larkEv.DoP2ImMessageReceiveV1)

	larkEv.LarkEventHandler.OnP2MessageReadV1(func(ctx context.Context, event *larkim.P2MessageReadV1) error {

		if event == nil {
			log.Errorf("event is nil")
			return nil
		}

		// 处理消息 event，这里简单打印消息的内容
		log.Infof("event: %s", larkcore.Prettify(event))
		// fmt.Println(larkcore.Prettify(event))
		// fmt.Println(event.RequestId())
		return nil
	})

	// 飞书API
	larkEv.feishuApi = feishuapi.NewFeishuApi(conf.AppId, conf.AppSecret)
	larkEv.cmdMgr = cmdhandler.NewCmdHandlerMgr(apiConfig, dbMgr, larkEv.feishuApi)

	return larkEv
}

// DoP2ImMessageReceiveV1 处理消息回调f
func (e *LarkEvent) DoP2ImMessageReceiveV1(ctx context.Context, eventData *larkim.P2MessageReceiveV1) error {

	var errFeishu error
	errFeishu = checkP2ImMessageReceiveV1(eventData)
	if nil != errFeishu {
		fmt.Println(larkcore.Prettify(eventData))
		return errFeishu
	}

	msg := eventData.Event.Message

	if "text" == *(msg.MessageType) {

		newEventCtx := context.WithValue(ctx, eventcommon.LarkMsgEventInfoContextKey, eventcommon.NewLarkMsgEventContext(eventData.Event))

		// msg.Content是个json内容，需要先反序列化成LarkTextMessageContent结构体
		textContent, errJson := eventcommon.ParseLarkTextMessageContent(*(msg.Content))
		if errJson != nil {
			log.Warnf(errJson.Error())
			return nil
		}

		isCmd, retMsg := e.cmdMgr.ExecCmd(newEventCtx, textContent.GetPureContentExtMentions())
		if isCmd {
			errFeishu = e.feishuApi.ReplyByFeiShuText(ctx, *(msg.MessageId), retMsg, nil)
			if errFeishu != nil {
				log.Warnf(errFeishu.Error())
			}
			return nil
		}
	}

	defMsg := genWuliaoMsg()
	errFeishu = e.feishuApi.ReplyByFeiShuText(ctx, *(msg.MessageId), defMsg, nil)
	if errFeishu != nil {
		log.Warnf(errFeishu.Error())
	}

	return nil
}

// 随机返回预设的一段文本
func genWuliaoMsg() string {

	// 一个字符串数组
	var wuliaoMsg = []string{
		"今天天气真好",
		"我是一只小黄狗",
		"你说的对",
		"旺旺", "哇哦 好厉害",
	}

	// 随机从数组中取一个字符串
	randIndex := rand.Intn(len(wuliaoMsg))

	// 返回随机字符串
	return wuliaoMsg[randIndex]
}

func checkP2ImMessageReceiveV1(data *larkim.P2MessageReceiveV1) error {

	var err error

	if data == nil {
		err = fmt.Errorf("P2ImMessageReceiveV1 data is nil")
		log.Errorf(err.Error())
		return err
	}
	if data.Event == nil {
		err = fmt.Errorf("P2ImMessageReceiveV1 data.Event is nil")
		log.Errorf(err.Error())
		return err
	}

	err = checkLarkimEventMessage(data.Event.Message)
	if err != nil {
		return err
	}
	err = checkLarkimEventSender(data.Event.Sender)
	if err != nil {
		return err
	}

	return nil
}

func checkLarkimEventMessage(eventMessage *larkim.EventMessage) error {

	var err error

	if nil == eventMessage {
		err = fmt.Errorf("P2ImMessageReceiveV1 EventMessage is nil")
		log.Errorf(err.Error())
		return err
	}

	if nil == eventMessage.MessageType {
		err = fmt.Errorf("P2ImMessageReceiveV1 EventMessage.MessageType is nil")
		log.Errorf(err.Error())
		return err
	}

	if nil == eventMessage.Content {
		err = fmt.Errorf("P2ImMessageReceiveV1 EventMessage.Content is nil")
		log.Errorf(err.Error())
		return err
	}

	if nil == eventMessage.MessageId {
		err = fmt.Errorf("P2ImMessageReceiveV1 EventMessage.MessageId is nil")
		log.Errorf(err.Error())
		return err
	}

	return nil
}
func checkLarkimEventSender(eventSender *larkim.EventSender) error {

	var err error
	if nil == eventSender {
		err = fmt.Errorf("P2ImMessageReceiveV1 EventSender is nil")
		log.Errorf(err.Error())
		return err
	}
	if nil == eventSender.SenderId {
		err = fmt.Errorf("P2ImMessageReceiveV1 EventSender.Sender is nil")
		log.Errorf(err.Error())
		return err
	}

	return nil
}
