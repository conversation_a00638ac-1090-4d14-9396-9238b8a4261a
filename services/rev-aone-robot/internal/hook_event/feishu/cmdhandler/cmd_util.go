package cmdhandler

import (
	"context"
	"sync"
	"time"

	"golang.52tt.com/pkg/log"
	gitlabapi "golang.52tt.com/services/rev-aone-robot/api/gitlab"
	eventcommon "golang.52tt.com/services/rev-aone-robot/internal/common"
	"golang.52tt.com/services/rev-aone-robot/internal/store"
)

// 命令可见性
type CommandVisibleBitmap uint8

const (
	CommandVisibleBitmap_EMPTY           CommandVisibleBitmap = 0x00
	CommandVisibleBitmap_P2P_ONLY        CommandVisibleBitmap = 0x01 // 单聊场景可见
	CommandVisibleBitmap_P2P_ONLY_ADMIN  CommandVisibleBitmap = 0x02 // 仅管理员可见
	CommandVisibleBitmap_P2P_WHITELIST   CommandVisibleBitmap = 0x04 // 仅注册 人的白名单列表 内 可见
	CommandVisibleBitmap_GROUP_ONLY      CommandVisibleBitmap = 0x08 // 仅注册群组可见
	CommandVisibleBitmap_GROUP_WHITELIST CommandVisibleBitmap = 0x10 // 仅注册群组 白名单列表内可见
	CommandVisibleBitmap_MAX             CommandVisibleBitmap = 0x80
)

func (b *CommandVisibleBitmap) IsVisibleAll(checkBitmap CommandVisibleBitmap) bool {
	return (*b & checkBitmap) == checkBitmap
}
func (b *CommandVisibleBitmap) IsVisibleAny(checkBitmap CommandVisibleBitmap) bool {
	return (*b & checkBitmap) != 0
}

// 命令关联的本地缓存
type CommandLocalCacher struct {
	sync.RWMutex
	mapGroupList      map[string]bool
	mapUserId2Role    map[string]eventcommon.TUserRole
	mapUserId2GitOper map[string]*gitlabapi.GitlabOper

	storeMgr *store.AoneStoreMgr
}

func NewCommandLocalCacher(db *store.AoneStoreMgr) *CommandLocalCacher {
	return &CommandLocalCacher{
		mapGroupList:      make(map[string]bool),
		mapUserId2Role:    make(map[string]eventcommon.TUserRole),
		mapUserId2GitOper: make(map[string]*gitlabapi.GitlabOper),
		storeMgr:          db,
	}
}

// Start 定时更新白名单数据
func (wc *CommandLocalCacher) Start(interval time.Duration) {
	ticker := time.NewTicker(interval)
	go func() {
		for range ticker.C {
			wc.update()
		}
	}()
}
func (wc *CommandLocalCacher) IsGroupExist(chatId string) bool {
	wc.RLock()
	defer wc.RUnlock()
	exist, ok := wc.mapGroupList[chatId]
	if ok {
		return exist
	}
	return false
}
func (wc *CommandLocalCacher) GetGitOper(userId string) *gitlabapi.GitlabOper {
	wc.RLock()
	defer wc.RUnlock()
	operator, ok := wc.mapUserId2GitOper[userId]
	if ok {
		return operator
	}
	return nil
}

func (wc *CommandLocalCacher) GetUserRole(userId string) eventcommon.TUserRole {
	wc.RLock()
	defer wc.RUnlock()
	role, ok := wc.mapUserId2Role[userId]
	if ok {
		return role
	}
	return eventcommon.UserRole_NONE
}

// 实现一个whitelistCache 定时更新的逻辑
// Update 更新白名单数据
func (wc *CommandLocalCacher) update() {

	// 更新 mapGroupList
	wc.updateGroup()

	// 更新 mapUserId2Role
	wc.updateUser()

}

func (wc *CommandLocalCacher) updateGroup() {

	dao := wc.storeMgr.GenDao()
	err, groupList := dao.GetAllLarkGroup(context.Background())
	if err != nil {
		log.Errorf("updateGroup GetAllLarkGroup error %v", err)
		return
	}

	var tempMap = make(map[string]bool)
	for _, group := range groupList {
		tempMap[group.LarkChatID] = true
	}

	wc.Lock()
	defer wc.Unlock()
	wc.mapGroupList = tempMap

	return
}

func (wc *CommandLocalCacher) updateUser() {

	dao := wc.storeMgr.GenDao()
	err, userList := dao.GetAllUser(context.Background())
	if err != nil {
		log.Errorf("updateUser GetAllUser error %v", err)
		return
	}

	var tempRoleMap = make(map[string]eventcommon.TUserRole)
	var tempApiMap = make(map[string]*gitlabapi.GitlabOper)
	for _, user := range userList {
		tempRoleMap[user.LarkUserID] = eventcommon.UserRole_NORMAL
		tempApiMap[user.LarkUserID] = &gitlabapi.GitlabOper{
			Token:    user.GitlabToken,
			Username: user.GitlabAccount,
		}
	}

	wc.Lock()
	defer wc.Unlock()
	wc.mapUserId2Role = tempRoleMap
	wc.mapUserId2GitOper = tempApiMap

	return
}

func getLarkMsgInfoCtx(ctx context.Context) *eventcommon.LarkMsgEventContext {
	var larkMsgInfo *eventcommon.LarkMsgEventContext = nil
	ctxLarkValue := ctx.Value(eventcommon.LarkMsgEventInfoContextKey)
	if ctxLarkValue != nil {
		larkMsgInfo = ctxLarkValue.(*eventcommon.LarkMsgEventContext)
	}
	return larkMsgInfo

}
