//AutoGen BY tools Don't Edit it!
package script
var EntertainmentLua = `
-- 娱乐房匹配

local M = {}

-- 返回码定义
local RET_SUCC = 0  -- 成功
local RET_IN_PK = -1  -- 用户正在PK中
local RET_NO_GIFT = -2  -- 用户礼物信息不存在
local RET_NO_CHIP = -3  -- 用户筹码信息不存在
local RET_IN_VIVE = -4  -- 用户正在复活中
local RET_CHIPS_NOT_ENOUGH = -5 -- 筹码池筹码不足

local WHITELIST_MIN_MATCH_COUNT = 3
local CHIP_MIN_MATCH_COUNT = 3
local LIMIT_LAST_PK_TIME = 480  --限制重新匹配时长
local PK_EXPIRE = 60*60*2
local GAME_EXPIRE = 60*60*24*2


local PKStatus = {
    NotParticipating = 0, -- 未参与
    NotMatching = 1;      -- 未开始匹配
    PreMatching = 2;      -- 预匹配（上一场pk结束后发起匹配前的状态）
    AutoInMatching = 3;   -- 自动发起的匹配中
    ActiveInMatching = 4; -- 手动发起的匹配中
    InPKing = 5;          -- 正在pk中
    InReviving = 6;       -- 复活阶段
    IsOut = 7;            -- 已淘汰
}


local rcall = redis.call
local key_zset_channel_in_pk = 'zset:in:pk:'                     --[[正在PK房间 定时器-- key:gameid  member: 'channel_id:dst_channel_id'  score:end_time]]
local key_hash_channel_in_pk = "hset:channel:in:pk:"             --[[正在PK房间--]]
local key_hash_channel_in_revive = "hset:channel:in:revive:"             --[[房间的复活ID--]]
local key_zset_channel_pk_change = 'zset:pk:change:'                     --[[PK房间 状态变化 定时器-- key:gameid  member: 'pk_id'  score:end_time]]
local key_set_pk_channels = 'set:pk:channels:'                     --[[PK房间 状 -- key:gameid  member: 'pk_id'  score:channels]]

local key_zset_pre_ready_match = "zset:channel:pre:ready:match:"             --[[结束后，待匹配前状态房间]]--
local key_zset_ready_match = "zset:channel:ready:match:"                     --[[待匹配房间]]--
local key_zset_revive_channel = "zset:channel:revive:"                     --[[待复活房间]]--

local key_zset_channel_gift_common = "zset:match:gift:common:"           --[[正在匹配房间  非白名单房间礼物值榜单 前30日流水 比赛前计算 领取筹码加入  取消匹配删除]]--
local key_zset_channel_chip_common = "zset:match:chip:common:"           --[[正在匹配房间  非白名单房间筹码值榜单 领取筹码加入 每场更新  取消匹配删除]]--
local key_zset_channel_gift_whitelist = "zset:match:gift:white:"     --[[正在匹配房间  白名单房间礼物值榜单 前30日流水 比赛前计算 领取筹码加入  取消匹配删除]]--
local key_zset_channel_chip_whitelist = "zset:match:chip:white:"     --[[正在匹配房间  白名单房间筹码值榜单 领取筹码加入 每场更新  取消匹配删除]]--

local key_hset_whitelist = "hset:whitelist"  --[[白名单房间]]--
local key_hset_match_begin_time = "hset:match:begin:time:"  --[[匹配开始时间]]--
local key_hset_last_match_begin_time =  "hset:last:match:begin:time:"  --[[上一轮匹配开始时间  数据上报用]]--

local key_hset_channel_gift = "hset:channel:gift"  --[[所有房间对应的礼物值  初始化比赛时生成 ]]--
local key_hset_channel_chip = "hset:channel:chip:"  --[[所有房间对应的筹码值  领取筹码时生成  比赛结束更新 key：gameid  field: channelid value:chip]]--

 local key_set_unique_id = "set:game:unique:id："     --[[生成 唯一id]]--

local key_hset_channel_guild = "hset:game:channel:guild" --[[房间对应的公会  key，filed:channel_id value:guild]]--
local key_set_guild_channel = "set:guild:channel:"  --[[公会对应的房间列表  key + guild  filed:channel_id]]--
local key_channel_pk_status = "set:channel:pk:status:"  --[[ 房间PK状态  key + channelid value:status]]--
local key_last_pk_channel = "set:last:pk:channel:" --[[ 上一次PK的channel  key + gameid:channelid  value:channelid]]--

local key_hash_channel_pk_score = "hset:channel:pk:score:"   --[[PK 分数， key+game_id, field:channel_id:pk_id value:score]]--
local key_hash_channel_pk_end_time = "hset:channel:pk:end:"  --[[PK 结束时间， key+game_id, field:pk_id value:end_time]]--

local key_hash_channel_revive_score = "hset:channel:revive:score:"  --[[复活 分数， key+game_id, field:channel_id:pk_id value:score]]--

local key_chip_pool = "chip:pool:" --[[筹码池剩余筹码 --key:gameId]]

local function get_chip_pool_key(game_id)
    return key_chip_pool..game_id
end

local function get_pk_member_key(game_id, pk_id)
    return key_set_pk_channels..game_id..":"..pk_id
end

local function get_pk_change_key(game_id)
    return key_zset_channel_pk_change..game_id
end

local function get_revive_score_key(game_id)
    return key_hash_channel_revive_score..game_id
end

local function get_pk_score_key(game_id)
    return key_hash_channel_pk_score..game_id
end

local function get_pk_end_time_key(game_id)
    return key_hash_channel_pk_end_time..game_id
end

local function get_pre_ready_key(game_id)
    return key_zset_pre_ready_match..game_id
end

local function get_revive_key(game_id)
    return key_zset_revive_channel..game_id
end

local function get_last_pk_channel_key(game_id, channel_id)
    return key_last_pk_channel..game_id..":"..channel_id
end

local function get_pk_status_key(game_id,channel_id)
    return key_channel_pk_status..game_id..':'..channel_id
end

local function get_unique_id_key(game_id)
    return key_set_unique_id..game_id
end

local function get_channel_gift_key()
    return key_hset_channel_gift
end

local function get_channel_chip_key(game_id)
    return key_hset_channel_chip..game_id
end

local function get_last_match_begin_time_key(game_id)
    return key_hset_last_match_begin_time..game_id
end

local function get_begin_time_key(game_id)
    return key_hset_match_begin_time..game_id
end

local function get_ready_match_key(game_id)
    return key_zset_ready_match..game_id
end

local function get_in_pk_hash_key(game_id)
    return key_hash_channel_in_pk..game_id
end

local function get_in_pk_zset_key(game_id)
    return key_zset_channel_in_pk..game_id
end

--定义一个记录日志的函数
local function log(message)
    rcall('LPUSH', 'mask_pk_svr_log', message)
end

local function get_wait_prefix(wait_sec)
    local wait_prefix = ":wait:"
    if wait_sec <= 30 then
        wait_prefix = wait_prefix.."thirty"
    elseif wait_sec > 30 and wait_sec <=60 then
        wait_prefix = wait_prefix.."sixty"
    else
        wait_prefix = wait_prefix.."over:sixty"
    end
    return wait_prefix
end

local function get_match_gift_key(game_id, channel_id, match_white, wait_sec)
    local key = key_zset_channel_gift_common..game_id
    if match_white == 1 then
        local in_whitelist = rcall('HGET', key_hset_whitelist, channel_id)
        if in_whitelist then
            key = key_zset_channel_gift_whitelist..game_id
        end
    end
    return key
end

local function get_match_chip_key(game_id, channel_id, match_white, wait_sec, match_final)
    --总决赛  不按时间匹配
    if match_final then
        if match_final == 1 then
            wait_sec = 0
        end
    end

    local key = key_zset_channel_chip_common..game_id
    if match_white == 1 then
        local in_whitelist = rcall('HGET', key_hset_whitelist, channel_id)
         if in_whitelist then
            key = key_zset_channel_chip_whitelist..game_id
        end
    end
    local prefix = get_wait_prefix(wait_sec)
    return key..prefix
end

local function get_match_gift_key_common(game_id, wait_sec)
    local key = key_zset_channel_gift_common..game_id
    return key
end

local function get_match_chip_key_common(game_id, wait_sec)
    local key = key_zset_channel_chip_common..game_id
    local prefix = get_wait_prefix(wait_sec)
    return key..prefix
end

--[[
加入待匹配列表
ARGV['add_ready_match', game_id, channel_id, end_time]

返回值
    -1 不在匹配列表
    0  成功
--]]
local function add_ready(game_id, channel_id, end_time, match_white)
    local ready_key = get_ready_match_key(game_id)
    rcall('ZADD', ready_key, end_time, channel_id)

    local gift_key = get_match_gift_key(game_id,channel_id, match_white,0)
    local chip_key = get_match_chip_key(game_id,channel_id, match_white,0)
    rcall('ZREM', gift_key, channel_id)
    rcall('ZREM', chip_key, channel_id)
end


function M.add_ready_match()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local match_white = tonumber(ARGV[3])
    local match_time = tonumber(ARGV[4])
    add_ready(game_id, channel_id, match_time, match_white)
    return 0
end

--[[
准备匹配超时处理
    ARGV['check_ready_match_timeout', gameid, expire_time]
返回值
    list []channel_id
--]]

function M.check_ready_match_timeout()
    local game_id = ARGV[1]
    local expire_time = tonumber(ARGV[2])
    local channels_ret = {}
    local ready_key = get_ready_match_key(game_id)
    local channels = rcall('ZRANGEBYSCORE', ready_key, '-INF', expire_time)
    for ix = 1, #channels do
        table.insert(channels_ret, channels[ix])
        rcall("ZREM", ready_key, channels[ix])
    end
    return channels_ret
end

--[[
加入匹配
    ARGV['add_match',game_id,channel_id,gift,chip,current_time, match_white,pk_count]
返回值
    -1 重复加入
    0  成功
--]]
function M.add_match()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local chip = tonumber(ARGV[3])
    local status = tonumber(ARGV[4])
    local current_time = tonumber(ARGV[5])
    local match_white = tonumber(ARGV[6])  --是否开启白名单
    local pk_count = tonumber(ARGV[7])  --pk次数
    local provide_chip = tonumber(ARGV[8])  --是否提供筹码
    local match_final = tonumber(ARGV[9])  --是否总决赛


    local in_pk_key = get_in_pk_hash_key(game_id)
    local in_pk = rcall('HGET', in_pk_key, channel_id)
    if in_pk~=false then
        return RET_IN_PK
    end

    local status_key = get_pk_status_key(game_id, channel_id)
    local current_status_str =  rcall('GET', status_key)
    if current_status_str ~= false then
        local current_status = tonumber(current_status_str)
        if current_status == PKStatus.InPKing or current_status == PKStatus.IsOut then
            return RET_IN_PK
        end
    end

    --立刻开始，删除等待
    local ready_key = get_ready_match_key(game_id)
    rcall('ZREM', ready_key, channel_id)

    local pre_ready_key = get_pre_ready_key(game_id)
    rcall('ZREM', pre_ready_key, channel_id)

    local channel_gift_key = get_channel_gift_key()
    local my_gift = rcall("HGET", channel_gift_key, channel_id)
    if my_gift == false then
        my_gift = 0
    end

    local channel_chip_key = get_channel_chip_key(game_id)
    rcall("HSET", channel_chip_key, channel_id, chip)

     local gift_key_common = get_match_gift_key_common(game_id,0)
     local chip_key_common = get_match_chip_key_common(game_id,0)
     rcall('ZREM', gift_key_common,channel_id)
     rcall('ZREM', chip_key_common,channel_id)

     local gift_key = get_match_gift_key(game_id, channel_id, match_white, 0)
     local chip_key = get_match_chip_key(game_id, channel_id, match_white, 0, match_final)

    log("add_match channel_id:"..channel_id.." gift_key:"..gift_key.." chip_key:"..chip_key.." provide_chip:"..provide_chip.." pk_count:"..pk_count.." my_gift:"..my_gift.." chip:"..chip)

    if provide_chip == 0 or pk_count < CHIP_MIN_MATCH_COUNT then
        rcall('ZADD', gift_key, my_gift, channel_id)
        rcall('EXPIRE',gift_key, GAME_EXPIRE )
     else
         rcall('ZADD', chip_key, chip, channel_id)
         rcall('ZREM', gift_key, channel_id)
         rcall('EXPIRE',chip_key, GAME_EXPIRE )
     end

    local begin_time_key = get_begin_time_key(game_id)
    rcall("HSET", begin_time_key, channel_id, current_time)
    rcall('SETEX', status_key, PK_EXPIRE , status)
    rcall('EXPIRE',begin_time_key, GAME_EXPIRE )
    return 0
end


--[[
取消匹配
    ARGV['cancel_match', channel_id]
返回值
    -1 不在匹配列表
    0  成功
--]]

local function clear_match_info(game_id, channel_id, match_white, wait_sec)
    local gift_key = get_match_gift_key(game_id, channel_id, match_white, wait_sec)
    local chip_key = get_match_chip_key(game_id, channel_id, match_white, wait_sec)
    local gift_key_common = get_match_gift_key_common(game_id,wait_sec)
    local chip_key_common = get_match_chip_key_common(game_id,wait_sec)
    rcall('ZREM', gift_key,  channel_id)
    rcall('ZREM', chip_key, channel_id)
    rcall('ZREM', gift_key_common,channel_id)
    rcall('ZREM', chip_key_common,channel_id)
    local begin_time_key = get_begin_time_key(game_id)
    local last_begin_time_key = get_last_match_begin_time_key(game_id)
    local begin_sec = rcall('HGET', begin_time_key, channel_id)
    if begin_sec ~= false then
        rcall("HSET", last_begin_time_key, channel_id, begin_sec)
    end
    rcall("HDEL", begin_time_key, channel_id)
    return 0
end

function M.cancel_match()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local current_time = tonumber(ARGV[3])
    local match_white = tonumber(ARGV[4])

    local status_key = get_pk_status_key(game_id,channel_id)
    local last_status = tonumber(rcall('GET', status_key))
    if last_status == PKStatus.InPKing or last_status == PKStatus.IsOut then
        return RET_IN_PK
    end

    local begin_time_key = get_begin_time_key(game_id)
    local begin_sec_str = rcall('HGET', begin_time_key, channel_id)
    if begin_sec_str == false then
        return -1
    end

    local wait_sec = current_time - tonumber(begin_sec_str)
    if wait_sec ~= false then
        clear_match_info(game_id, channel_id, match_white,wait_sec)
    end

    rcall('SETEX', status_key, PK_EXPIRE, PKStatus.NotMatching)
    return RET_SUCC
end


--[[
有筹码的匹配
ARGV['start_chip_match',game_id, channel_id,pk_count,gift,chip,wait_sec]
返回值
    -1 不在匹配列表
    0  成功
--]]

local function get_gift_chip_offset(wait_sec)
    local add_gift, add_chip
    if wait_sec <= 30 then
        add_gift = 5000000000
        add_chip = 500
    elseif wait_sec > 30 and wait_sec <= 60 then
        add_gift = 5000000000
        add_chip = 1000
    elseif wait_sec > 60 then
        add_gift = 5000000000 + (wait_sec - 59)/2*500000000
        add_chip = 1000 + (wait_sec - 59)/2*200
    end
    return add_gift, add_chip
end

local function check_channel_in_match(game_id, channel_id)
    local status_key = get_pk_status_key(game_id, channel_id)
    local status = rcall('GET', status_key)
    if status ~= false then
        local status_enum = tonumber(status)
        if status_enum == PKStatus.AutoInMatching or status_enum == PKStatus.ActiveInMatching then
            return true
        end
    end
    return false
end


local function match_by_gift(game_id, channel_id, gift_key, guild_channels,my_gift, add_gift, current_time,wait_sec,match_white,pk_sec)
    --先删除自己
    local remove_self = 0
    local score = rcall('ZSCORE', gift_key, channel_id)
    if score ~= false then
        rcall('ZREM', gift_key, channel_id)
        remove_self = tonumber(channel_id)
        log("match_by_gift channel_id:"..channel_id.." gift_key:"..gift_key.." remove_self")
    end

    --同公会不能匹配
    local remove_guild_channels = {}
    for i=1,#guild_channels do
        local guild_score = rcall('ZSCORE', gift_key, guild_channels[i])
        if guild_score ~= false then
            local in_match = check_channel_in_match(game_id, guild_channels[i])
            if  in_match then
                rcall('ZREM', gift_key, guild_channels[i])
                table.insert(remove_guild_channels, guild_channels[i])
                log("match_by_gift channel_id:"..channel_id.." gift_key:"..gift_key.." remove_guild_channels "..guild_channels[i])
            end
        end
    end

    --180内 删除上一轮匹配玩家
    local remove_last_pk_channel = 0
    local last_pk_key = get_last_pk_channel_key(game_id, channel_id)
    if wait_sec <= LIMIT_LAST_PK_TIME then
        local last_channel = rcall('GET', last_pk_key)
        if last_channel ~= false then
            local in_match = check_channel_in_match(game_id, last_channel)
            local score=rcall('ZSCORE', gift_key, last_channel)
            if score ~= false and in_match then
                rcall('ZREM', gift_key, last_channel)
                remove_last_pk_channel = tonumber(last_channel)
                log("match_by_gift channel_id:"..channel_id.." gift_key:"..gift_key.." last_channel "..last_channel)
            end
        end
    end

    local match_size = rcall('ZCARD', gift_key)
    log("match_by_gift channel_id:"..channel_id.." gift_key:"..gift_key.." match_size:"..match_size)

    local channel_gift_key = get_channel_gift_key()
    local dst_channel = rcall('ZREVRANGEBYSCORE', gift_key, my_gift+add_gift, my_gift-add_gift,'LIMIT', 0, 1)
    log("match_by_gift channel_id:"..channel_id.." gift_key:"..gift_key.." my_gift+:"..my_gift+add_gift.." my_gift-:"..my_gift-add_gift.." dst_channel size:"..#dst_channel)

    if #dst_channel ~= 0 then
        local ret = M.begin_pk(game_id, channel_id, dst_channel[1], current_time, pk_sec, 0)
        if ret == 0 then
            clear_match_info(game_id, channel_id, match_white, wait_sec)
            clear_match_info(game_id, dst_channel[1],match_white, wait_sec)

            --恢复公会
            for i=1,#remove_guild_channels do
                local gift = rcall("HGET", channel_gift_key, remove_guild_channels[i])
                if gift ~= false then
                    rcall('ZADD', gift_key, gift, remove_guild_channels[i])
                end
            end

            --恢复上一轮对手
            if remove_last_pk_channel > 0 then
                local gift = rcall("HGET", channel_gift_key, remove_last_pk_channel)
                if gift ~= false then
                    rcall('ZADD', gift_key, gift, remove_last_pk_channel)
                end
            end

            local dst_last_pk_key = get_last_pk_channel_key(game_id, dst_channel[1])
            rcall('SETEX', dst_last_pk_key, LIMIT_LAST_PK_TIME, channel_id)
            rcall('SETEX', last_pk_key, LIMIT_LAST_PK_TIME, dst_channel[1])
            rcall('ZREM', gift_key, dst_channel[1])
            return tonumber(dst_channel[1])
        end
    end

    --失败回滚
    if remove_self > 0 then
        rcall('ZADD', gift_key, my_gift, channel_id)
    end

    for i=1,#remove_guild_channels do
        local gift = rcall("HGET", channel_gift_key, remove_guild_channels[i])
        if gift ~= false then
            rcall('ZADD', gift_key, gift, remove_guild_channels[i])
        end
    end

    --恢复上一轮对手
    if remove_last_pk_channel > 0 then
        local gift = rcall("HGET", channel_gift_key, remove_last_pk_channel)
        if gift ~= false then
            rcall('ZADD', gift_key, gift, remove_last_pk_channel)
        end
    end
    return 0
end



local function match_by_chip(game_id, channel_id,chip_key,guild_channels,my_chip,add_chip,current_time, wait_sec, match_white,pk_sec)
    --先删除自己
    local remove_self = 0
    local score = rcall('ZSCORE', chip_key, channel_id)
    if score ~= false then
        rcall('ZREM', chip_key, channel_id)
        remove_self = tonumber(channel_id)
    end

    --同公会不能匹配
    local remove_guild_channels =  {}
    for i=1,#guild_channels do
        local score=rcall('ZSCORE', chip_key, guild_channels[i])
        if score ~= false then
            local in_match = check_channel_in_match(game_id, guild_channels[i])
            if in_match then
                rcall('ZREM', chip_key, guild_channels[i])
                table.insert(remove_guild_channels,guild_channels[i])
            end
        end
    end


    --180内 删除上一轮匹配玩家
    local remove_last_pk_channel = 0
    local last_pk_key = get_last_pk_channel_key(game_id, channel_id)
    if wait_sec <= LIMIT_LAST_PK_TIME then
        local last_channel = rcall('GET', last_pk_key)
        if last_channel ~= false then
            local in_match = check_channel_in_match(game_id, last_channel)
            local score = rcall('ZSCORE', chip_key, last_channel)
            if score ~= false and in_match then
                rcall('ZREM', chip_key, last_channel)
                remove_last_pk_channel = tonumber(last_channel)
            end
        end
    end

    local channel_chip_key = get_channel_chip_key(game_id)
    local dst_channel = rcall('ZREVRANGEBYSCORE', chip_key, my_chip+add_chip, my_chip-add_chip,'LIMIT', 0, 1)
    if #dst_channel ~= 0 then
        local ret = M.begin_pk(game_id, channel_id, dst_channel[1], current_time, pk_sec, 0)
        if ret == 0 then
            clear_match_info(game_id, channel_id, match_white, wait_sec)
            clear_match_info(game_id, dst_channel[1],match_white, wait_sec)

            --恢复公会
            for i=1,#remove_guild_channels do
                local chip = rcall("HGET", channel_chip_key, remove_guild_channels[i])
                rcall('ZADD', chip_key, chip, remove_guild_channels[i])
            end

            --恢复上一轮对手
            if remove_last_pk_channel > 0 then
                local chip = rcall("HGET", channel_chip_key, remove_last_pk_channel)
                if chip ~= false then
                    rcall('ZADD', chip_key, chip, remove_last_pk_channel)
                end
            end

            --设置本轮对手
            local dst_last_pk_key = get_last_pk_channel_key(game_id, dst_channel[1])
            rcall('SETEX', dst_last_pk_key, LIMIT_LAST_PK_TIME, channel_id)
            rcall('SETEX', last_pk_key, LIMIT_LAST_PK_TIME, dst_channel[1])
            rcall('ZREM', chip_key, dst_channel[1])
            return tonumber(dst_channel[1])
        end
    end


    --失败回滚
    if remove_self > 0 then
        rcall('ZADD', chip_key, my_chip, channel_id)
    end

    for i=1,#remove_guild_channels do
        local chip = rcall("HGET", channel_chip_key, remove_guild_channels[i])
        if chip ~= false then
            rcall('ZADD', chip_key, chip, remove_guild_channels[i])
        end
    end

    --恢复上一轮对手
    if remove_last_pk_channel > 0  then
        local chip = rcall("HGET", channel_chip_key, remove_last_pk_channel)
        if chip ~= false then
            rcall('ZADD', chip_key, chip, remove_last_pk_channel)
        end
    end
    return 0
end

function M.start_chip_match()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local pk_count = tonumber(ARGV[3])
    local wait_sec = tonumber(ARGV[4])
    local current_time = tonumber(ARGV[5])
    local match_white = tonumber(ARGV[6])   --是否开启白名单匹配 0 不开启 1 开启
    local match_guild = tonumber(ARGV[7])   --是否同公会的厅允许匹配 0 不允许 1 允许
    local match_chip = tonumber(ARGV[8])   --是否开启筹码匹配  1开启
    local pk_sec = tonumber(ARGV[9])   --PK 单场PK时长
    local match_final = tonumber(ARGV[10])   --是否总决赛
    local prior = tonumber(ARGV[11])   -- 是否优先匹配
    local prior_sec = tonumber(ARGV[12])   -- 是否优先匹配时长


    local my_guild = rcall('HGET', key_hset_channel_guild, channel_id)
    local guild_channels = {}
    if match_guild == 0  and my_guild ~= false then
       if tonumber(my_guild) > 0 then
            guild_channels = rcall("SMEMBERS", key_set_guild_channel..my_guild)
        end
    end

    local hash_key = get_in_pk_hash_key(game_id)
    local in_pk = rcall('HGET', hash_key, channel_id)
    if in_pk ~= false then
        return RET_IN_PK
    end

    local my_chip
    if match_chip == 1 then
        local channel_chip_key = get_channel_chip_key(game_id)
        my_chip = rcall("HGET", channel_chip_key, channel_id)
        if my_chip == false then
            return RET_NO_CHIP
        end
    end

    local gift_key = get_match_gift_key_common(game_id,wait_sec)
    local channel_gift_key = get_channel_gift_key()
    local my_gift = rcall("HGET", channel_gift_key, channel_id) or 0
    local add_gift, add_chip = get_gift_chip_offset(wait_sec)
    local chip_key = get_match_chip_key(game_id,channel_id, match_white, wait_sec, match_final)
    log("channel_id:"..channel_id.." gift_key:"..gift_key)

    --开启优先匹配 30秒内优先匹配白名单
    --当 30s 内在白名单里的娱乐厅没有可以被匹配的对象时，匹配池内的娱乐厅和非匹配池的娱乐厅匹配即可
    if prior == 1 and wait_sec <= prior_sec then
        log("start_chip_match 1 channel_id:"..channel_id.." gift_key:"..gift_key.." use prior： wait sec: "..wait_sec)
        gift_key = get_match_gift_key(game_id,channel_id, 1, wait_sec)
        local dst_channel = match_by_gift(game_id, channel_id, gift_key, guild_channels, my_gift, add_gift, current_time, wait_sec,match_white, pk_sec)
        if dst_channel >  0 then
            return dst_channel
        else
           return 0
        end
    end

    -- 前3场或者无筹码匹配
    if pk_count < WHITELIST_MIN_MATCH_COUNT or match_chip == 0 then
        --根据礼物值匹配
        if pk_count < WHITELIST_MIN_MATCH_COUNT then
            log("start_chip_match 2 channel_id:"..channel_id.." gift_key:"..gift_key.." use common wait sec: "..wait_sec)
            --不足3场
            local dst_channel = match_by_gift(game_id, channel_id, gift_key, guild_channels, my_gift, add_gift, current_time, wait_sec,match_white, pk_sec)
            if dst_channel >  0 then
                return dst_channel
            end

            log("start_chip_match 3 match_by_gift failed channel_id:"..channel_id.." gift_key:"..gift_key.." use common wait sec: "..wait_sec.." match_white:"..match_white)

            --3场内 匹配失败 >180秒 &&  开启白名单&& 在白名单内  取消白名单限制 在白名单内匹配
            if wait_sec >= 180 and match_white == 1 then
                gift_key = get_match_gift_key(game_id, channel_id, match_white,wait_sec)
                log("start_chip_match 4 channel_id:"..channel_id.." gift_key:"..gift_key.." use white list"..wait_sec)
                dst_channel = match_by_gift(game_id, channel_id, gift_key, guild_channels, my_gift, add_gift, current_time, wait_sec,match_white, pk_sec)
                if dst_channel >  0 then
                    return dst_channel
                end
            end
        else
            log("start_chip_match 5 channel_id:"..channel_id.." gift_key:"..gift_key.." use white list"..wait_sec)

            -- 当场次>3场 无筹码局 优先匹配白名单
            gift_key = get_match_gift_key(game_id, channel_id, match_white,wait_sec)
            local dst_channel = match_by_gift(game_id, channel_id, gift_key, guild_channels, my_gift, add_gift, current_time, wait_sec,match_white,pk_sec)
            if dst_channel >  0 then
                return dst_channel
            end

            --当场次>3场；等待180秒后，白名单内人互相匹配不到时，可与白名单外人互相匹配
            if wait_sec >= 180 and match_white == 1 then
                gift_key = get_match_gift_key_common(game_id,wait_sec)
                dst_channel =  match_by_gift(game_id, channel_id, gift_key, guild_channels, my_gift, add_gift, current_time, wait_sec,match_white,pk_sec)
                if dst_channel >  0 then
                    return dst_channel
                end
            end
        end

        --有筹码的匹配 && 前三场等待100秒 匹配失败  放开前三场只按流水限制 按筹码匹配
        if wait_sec >= 100 and match_chip == 1 then
            local ret = match_by_chip(game_id, channel_id,chip_key,guild_channels,my_chip,add_chip,current_time,wait_sec,match_white,pk_sec)
            if ret > 0 then
                return ret
            end
        end
    else
        log("start_chip_match 6 channel_id:"..channel_id.." chip_key:"..chip_key.." use white list"..wait_sec)

        -- >=3场 按筹码优先匹配
        local ret = match_by_chip(game_id, channel_id,chip_key,guild_channels,my_chip,add_chip,current_time,wait_sec,match_white,pk_sec)
        if ret > 0 then
            return ret
        end

        --匹配失败 >300秒 && 开启白名单&& 在白名单内  取消白名单限制 在普通名单内匹配
        if wait_sec >= 300 and match_white == 1 then
            local in_whitelist = rcall('HGET', key_hset_whitelist, channel_id)
            if in_whitelist then
                chip_key = get_match_chip_key_common(game_id, wait_sec)
                ret = match_by_chip(game_id, channel_id,chip_key,guild_channels,my_chip,add_chip,current_time,wait_sec,match_white,pk_sec)
                if ret > 0 then
                    return ret
                end
            end
        end
    end

    --这一轮匹配失败,等待定时器重试
    return 0
end

--[[
开始PK
ARGV['begin_pk', game_id, channel_id, dst_channel_id, current_time, pk_sec, pk_id]
返回值
    -1 重复加入
    0  成功
--]]
function M.begin_pk(game_id, channel_id, dst_channel_id, current_time, pk_sec, pk_id)
    local pk_end_time = current_time + pk_sec
    local key_in_pk = get_in_pk_zset_key(game_id)
    local hash_key = get_in_pk_hash_key(game_id)
    local in_pk = rcall('HGET',hash_key,channel_id)
    local dest_in_pk = rcall('HGET',hash_key,dst_channel_id)
    if in_pk ~= false or dest_in_pk ~= false then
        log("begin_pk failed channel_id:"..channel_id.." dst_channel_id:"..dst_channel_id.." in_pk:"..in_pk.." dest_in_pk:"..dest_in_pk)
        return RET_IN_PK
    end

    if pk_id == 0 then
        pk_id = M.get_new_unique_id(game_id)
    end

    rcall('ZADD', key_in_pk, pk_end_time, pk_id)
    rcall('EXPIRE', key_in_pk, GAME_EXPIRE)

    rcall('HSET', hash_key, channel_id, pk_id)
    rcall('HSET', hash_key, dst_channel_id, pk_id)
    rcall('EXPIRE', hash_key, GAME_EXPIRE)

    local status_key = get_pk_status_key(game_id, channel_id)
    rcall('SETEX', status_key, PK_EXPIRE ,PKStatus.InPKing)
    local dst_status_key = get_pk_status_key(game_id, dst_channel_id)
    rcall('SETEX', dst_status_key, PK_EXPIRE ,PKStatus.InPKing)

    local end_time_key = get_pk_end_time_key(game_id)
    rcall('HSET', end_time_key, pk_id, pk_end_time)
    rcall('EXPIRE', end_time_key, GAME_EXPIRE)

    local member_key = get_pk_member_key(game_id, pk_id)
    rcall('SADD', member_key, channel_id, dst_channel_id)
    rcall('EXPIRE', member_key, PK_EXPIRE)

    return 0
end


local function get_pk_channels_by_id_local(game_id, pk_id)
     local member_key = get_pk_member_key(game_id,pk_id)
     local ret  = {}

     local change_channels = rcall("SMEMBERS", member_key)
     for i=1,#change_channels do
         table.insert(ret, tonumber(change_channels[i]))
     end

     return ret
end

function M.get_pk_channels_by_id()
     local game_id = ARGV[1]
     local pk_id = ARGV[2]

     return get_pk_channels_by_id_local(game_id, pk_id)
end

local function end_pk_by_pk_id(game_id, pk_id)
    --[[
    local game_id = ARGV[1]
    local pk_id = ARGV[2]
    ]]--

    local key_in_pk = get_in_pk_zset_key(game_id)
    local hash_key = get_in_pk_hash_key(game_id)

    local rem_cnt = rcall("ZREM", key_in_pk, pk_id)
    if tonumber(rem_cnt) == 0 then
        return -1
    end

    local res = get_pk_channels_by_id_local(game_id, pk_id)
    for i = 1, #res do
        rcall('HDEL', hash_key, res[i])
        local status_key = get_pk_status_key(game_id, res[i])
        rcall('SETEX', status_key, PK_EXPIRE, PKStatus.NotMatching)
    end

    return RET_SUCC
end

function M.early_end_pk()
    local game_id = ARGV[1]
    local pk_id = ARGV[2]
    local curr_time = ARGV[3]

    local ret = end_pk_by_pk_id(game_id, pk_id)
    if ret < 0 then
        return ret
    end

    local end_time_key = get_pk_end_time_key(game_id)
    rcall('HSET', end_time_key, pk_id, curr_time)
    rcall('EXPIRE', end_time_key, GAME_EXPIRE)

    return RET_SUCC
end

--[[
获取结束PK信息
    ARGV['get_pk_end_channels', game_id, expire_time]
返回值
    list [pk_id]
--]]
function M.get_pk_end_channels()
    local game_id = ARGV[1]
    local expire_time = tonumber(ARGV[2])
    local ret = {}

    local key_in_pk = get_in_pk_zset_key(game_id)
    local pk_ids = rcall('ZRANGEBYSCORE', key_in_pk, '-INF', expire_time, 'LIMIT', 0, 1)
    for ix = 1, #pk_ids do
        end_pk_by_pk_id(game_id, tonumber(pk_ids[ix]))
        table.insert(ret, tonumber(pk_ids[ix]))
    end

    return ret
end

--[[
function M.get_pk_end_channels()
    local game_id = ARGV[1]
    local expire_time = tonumber(ARGV[2])

    local key_in_pk = get_in_pk_zset_key(game_id)
    local channels = rcall('ZRANGEBYSCORE', key_in_pk, '-INF', expire_time, 'LIMIT', 0, 1)
    local hash_key = get_in_pk_hash_key(game_id)
    local ret = {}
    for ix = 1, #channels do
        rcall("ZREM", key_in_pk, channels[ix])
        local res = split(channels[ix],":")
        for i = 1, #res do
            table.insert(ret, res[i])
            if i==#res then
                local pk_id = rcall('HGET', hash_key, res[i])
                if pk_id ~= false then
                    table.insert(ret, pk_id)
                else
                    table.insert(ret, "0")
                end
            end
            rcall('HDEL', hash_key, res[i])
            local status_key = get_pk_status_key(game_id, res[i])
            rcall('SETEX', status_key, PK_EXPIRE, PKStatus.NotMatching)
        end
    end
    return ret
end
--]]

function M.get_in_pk_channels()
    local game_id = ARGV[1]
    local key_in_pk = get_in_pk_zset_key(game_id)
    return rcall('ZRANGE', key_in_pk, 0, -1)
end

function M.get_in_match_channels()
    local game_id = ARGV[1]
    local hash_key = get_begin_time_key(game_id)
    return rcall("HGETALL", hash_key)
end

function M.remove_in_match_channel()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local match_white = tonumber(ARGV[3])
    local wait_sec = tonumber(ARGV[4])
    local hash_key = get_begin_time_key(game_id)
    rcall("HDEL", hash_key, channel_id)
    local status_key = get_pk_status_key(game_id, channel_id)
    rcall('SETEX', status_key, PK_EXPIRE, PKStatus.NotMatching)
    clear_match_info(game_id, channel_id, match_white,wait_sec)
    return RET_SUCC
end

function M.get_in_match_channel()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local hash_key = get_begin_time_key(game_id)
    local begin_time = rcall("HGET", hash_key, channel_id)
    if begin_time == false then
        return 0
    end
    return tonumber(begin_time)
end


function M.add_whitelist()
    local channel_id = ARGV[1]
    local set_time = tonumber(ARGV[2])
    rcall('HSET', key_hset_whitelist, channel_id, set_time)
    return 0
end

function M.del_whitelist()
    local channel_id = ARGV[1]
    rcall('HDEL', key_hset_whitelist, channel_id)
    return 0
end

function M.get_new_unique_id(game_id)
    local args_game_id = ARGV[1]
    if args_game_id ~= nil then
        game_id = args_game_id
    end
    local key = get_unique_id_key(game_id)
    return rcall("INCR", key)
end


function M.set_pk_status()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local status = tonumber(ARGV[3])
    local status_key = get_pk_status_key(game_id, channel_id)
    rcall('SETEX', status_key, PK_EXPIRE, status)
    if status == PKStatus.IsOut then
        clear_match_info(game_id, channel_id,1,0)
        clear_match_info(game_id, channel_id,1,31)
        clear_match_info(game_id, channel_id,1,61)
    end
    return status
end

function M.get_pk_status()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local status_key = get_pk_status_key(game_id, channel_id)
    local status = rcall('GET', status_key)
    if status == false then
        return PKStatus.NotParticipating
    end
    return status
end

function M.set_channel_chip()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local current_chip = ARGV[3]
    local key = get_channel_chip_key(game_id)
    return rcall("HSET", key, channel_id, current_chip)
end

function M.init_channel_gift()
    local channel_id = ARGV[1]
    local gift = ARGV[2]
    local gift_key = get_channel_gift_key()
    rcall("HSET", gift_key, channel_id, gift)
    return RET_SUCC
end

function M.get_channel_gift()
     local channel_id = ARGV[1]
     local gift_key = get_channel_gift_key()
     return tonumber(rcall("HGET", gift_key, channel_id))
end

function M.get_last_begin_time()
     local game_id = ARGV[1]
     local channel_id = ARGV[2]
     local last_begin_time_key = get_last_match_begin_time_key(game_id)
     return tonumber(rcall("HGET", last_begin_time_key, channel_id))
end

function M.get_pk_id()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local hash_key = get_in_pk_hash_key(game_id)
    local pk_id = rcall('HGET', hash_key, channel_id)
    if pk_id~=false then
        return pk_id
    end
    return 0
end

function M.get_revive_id()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local hash_key = key_hash_channel_in_revive..game_id
    local revive_id = rcall('HGET', hash_key, channel_id)
    if revive_id~=false then
        return revive_id
    end
    return 0
end


function M.change_in_match_channel()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local match_white = tonumber(ARGV[3])
    local wait_sec = tonumber(ARGV[4])
    local pk_count = tonumber(ARGV[5])
    local provide_chip = tonumber(ARGV[6])

    if wait_sec <= 30 then
        return -1
    end

    local last_wait_sec = 30
    if wait_sec > 60 then
        last_wait_sec = 60
    end

    local last_gift_key = get_match_gift_key(game_id, channel_id, match_white, last_wait_sec)
    local last_chip_key = get_match_chip_key(game_id, channel_id, match_white, last_wait_sec)
    rcall('ZREM', last_gift_key, channel_id)
    rcall('ZREM', last_chip_key, channel_id)

    local channel_gift_key = get_channel_gift_key()
    local my_gift = rcall("HGET",channel_gift_key, channel_id)
    if my_gift == false then
        my_gift = 0
    end

    local channel_chip_key = get_channel_chip_key(game_id)
    local my_chip = rcall("HGET", channel_chip_key, channel_id)
    if my_chip == false then
        my_chip = 0
    end

    local gift_key = get_match_gift_key(game_id, channel_id, match_white, wait_sec)
    local chip_key = get_match_chip_key(game_id, channel_id, match_white, wait_sec)
    if provide_chip == 0 or pk_count < CHIP_MIN_MATCH_COUNT then
         rcall('ZADD', gift_key, my_gift, channel_id)
    else
      rcall('ZREM', gift_key, channel_id)
      rcall('ZADD', chip_key, my_chip, channel_id)
    end
    return RET_SUCC
end


--[[
增加到复活队列
    ARGV['add_revive', game_id, channel_id，revive_id, ent_time]
返回值
    int
--]]

function M.add_revive()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local ent_time = tonumber(ARGV[3])
    local status_key = get_pk_status_key(game_id, channel_id)
    local revive_id = M.get_new_unique_id(game_id)
    local key = get_revive_key(game_id)
    local hash_key = key_hash_channel_in_revive..game_id
    rcall('HSET', hash_key, channel_id, revive_id)
    rcall('EXPIRE', hash_key, GAME_EXPIRE)
    rcall('ZADD', key, ent_time, channel_id)
    rcall('SETEX', status_key, PK_EXPIRE, PKStatus.InReviving)
    return revive_id
end


--[[
获取结束复活信息
    ARGV['get_revive_end_channel', game_id, expire_time]
返回值
    list [channel_id]
--]]
function M.get_revive_end_channel()
    local game_id = ARGV[1]
    local expire_time = tonumber(ARGV[2])

    local key = get_revive_key(game_id)
    local channels_ret = {}
    local channels = rcall('ZRANGEBYSCORE', key, '-INF', expire_time, 'LIMIT', 0, 1)
    for ix = 1, #channels do
        table.insert(channels_ret, channels[ix])
        rcall("ZREM", key, channels[ix])
        local status_key = get_pk_status_key(game_id, channels[ix])
        rcall('SETEX', status_key, PK_EXPIRE, PKStatus.NotMatching)
    end
    return channels_ret
end

function M.get_in_revive_channel()
    local game_id = ARGV[1]
    local key = get_revive_key(game_id)
    return rcall('ZRANGE', key, 0, -1)
end


function M.get_in_match_zset_channel()
    local game_id = ARGV[1]
    local wait_sec = tonumber(ARGV[2])
    local gift_key = get_match_gift_key_common(game_id, wait_sec)
    return rcall('ZRANGE', gift_key, 0, -1,"WITHSCORES")
end


function M.add_pre_ready()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local end_time = tonumber(ARGV[3])
    local ready_key = get_pre_ready_key(game_id)
    rcall('ZADD', ready_key, end_time, channel_id)
    return 0
end

--[[
获取预准备房间
    ARGV['get_pre_ready_end_channel', game_id, expire_time]
返回值
    list [channel_id]
--]]
function M.get_pre_ready_end_channel()
    local game_id = ARGV[1]
    local expire_time = tonumber(ARGV[2])

    local key = get_pre_ready_key(game_id)
    local channels_ret = {}
    local channels = rcall('ZRANGEBYSCORE', key, '-INF', expire_time, 'LIMIT', 0, 1)
    for ix = 1, #channels do
        table.insert(channels_ret, channels[ix])
        rcall("ZREM", key, channels[ix])
    end
    return channels_ret
end


function M.get_pk_info()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local pk_id = ARGV[3]
    local score_key = get_pk_score_key(game_id)
    local field = channel_id..":"..pk_id
    local ret = {}
    local score = rcall('HGET', score_key, field)
    if score ~= false then
        table.insert(ret, tonumber(score))
    else
        table.insert(ret, 0)
    end
    local end_time_key = get_pk_end_time_key(game_id)
    local end_time = rcall('HGET', end_time_key, pk_id)
    if end_time ~= false then
        table.insert(ret, tonumber(end_time))
    else
        table.insert(ret, 0)
    end
    return ret
end

function M.update_pk_score()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local pk_id = ARGV[3]
    local score = tonumber(ARGV[4])
    local update_time = tonumber(ARGV[5])

    local key = get_pk_score_key(game_id)
    local field = channel_id..":"..pk_id
    rcall('HINCRBY', key, field, score)

    local change_key = get_pk_change_key(game_id)
    rcall('ZADD', change_key, 'NX', update_time, pk_id)
    return RET_SUCC
end

function M.get_pk_change_channel()
    local game_id = ARGV[1]
    local expire_time = tonumber(ARGV[2])

    local change_key = get_pk_change_key(game_id)
    local ret  = {}

    local pk_ids = rcall('ZRANGEBYSCORE', change_key, '-INF', expire_time, 'LIMIT', 0, 50)
    for ix = 1, #pk_ids do
        table.insert(ret, tonumber(pk_ids[ix]))
        rcall("ZREM", change_key, tonumber(pk_ids[ix]))
        local member_key = get_pk_member_key(game_id,pk_ids[ix])
        local change_channels = rcall("SMEMBERS", member_key)
        for i=1,#change_channels do
            table.insert(ret, tonumber(change_channels[i]))
        end
    end
    return ret
end

function M.update_revive_score()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local revive_id = ARGV[3]
    local score = tonumber(ARGV[4])

    local key = get_revive_score_key(game_id)
    local field = channel_id..":"..revive_id
    return rcall('HINCRBY', key, field, score)
end

function M.get_revive_score()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local revive_id = ARGV[3]
    local score = tonumber(ARGV[4])

    local key = get_revive_score_key(game_id)
    local field = channel_id..":"..revive_id
    local score = rcall('HGET', key, field)
    if score ~= false then
        return tonumber(score)
    end
    return score
end

function M.get_revive_info()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local revive_id = ARGV[3]

    local ret = {}
    local key = get_revive_score_key(game_id)
    local field = channel_id..":"..revive_id
    local score = rcall('HGET', key, field)
    if score ~= false then
        table.insert(ret, tonumber(score))
    else
        table.insert(ret, 0)
    end

    local revive_key = get_revive_key(game_id)
    local end_time = rcall('ZSCORE', revive_key, channel_id)
    if end_time ~= false then
        table.insert(ret, tonumber(end_time))
    else
        table.insert(ret, 0)
    end
    return ret
end

function M.set_revive_end()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local end_time = tonumber(ARGV[3])
    local revive_key = get_revive_key(game_id)
    local score = rcall('ZSCORE', revive_key, channel_id)
    if score ~= false then
        rcall('ZADD', revive_key, end_time, channel_id)
    end
    return 0
end

--[[
获取PK信息
    ARGV['get_pre_ready_end_channel', game_id, expire_time]
返回值
    list [pk_score, end_time, dst_channel, dst_channel_score]
--]]

function M.get_pk_infos()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local pk_id = ARGV[3]

    local ret = {}
    local score_key = get_pk_score_key(game_id)
    local field = channel_id..":"..pk_id
    local score = rcall('HGET', score_key, field)
    if score ~= false then
        table.insert(ret, tonumber(score))
    else
        table.insert(ret, 0)
    end

    local end_time_key = get_pk_end_time_key(game_id)
    local end_time = rcall('HGET', end_time_key, pk_id)
    if end_time ~= false then
        table.insert(ret, tonumber(end_time))
    else
        table.insert(ret, 0)
    end

    local last_pk_key = get_last_pk_channel_key(game_id, channel_id)
    local last_channel = rcall('GET', last_pk_key)
    if last_channel ~= false then
        table.insert(ret, tonumber(last_channel))
        local dst_field = last_channel..":"..pk_id
        local dst_score = rcall('HGET', score_key, dst_field)
        if dst_score ~= false then
            table.insert(ret, tonumber(dst_score))
        else
            table.insert(ret, tonumber(0))
        end
    else
        table.insert(ret, tonumber(0))
    end
    return ret
end

function M.decr_game_chips()
    local game_id = ARGV[1]
    local decr_chip = ARGV[2]

    local chip_pool_ket = get_chip_pool_key(game_id)
    local curr_chip = rcall('Get', chip_pool_ket)
    if curr_chip == false then
        return -1
    elseif tonumber(curr_chip) < tonumber(decr_chip) then
        return RET_CHIPS_NOT_ENOUGH
    else
        rcall('INCRBY', chip_pool_ket, -decr_chip)
    end
    return RET_SUCC
end

function M.init_chip_pool()
    local game_id = ARGV[1]
    local total_chip = ARGV[2]

    local chip_pool_ket = get_chip_pool_key(game_id)
    rcall('SET', chip_pool_ket, total_chip, 'EX', GAME_EXPIRE, 'NX')
    return RET_SUCC
end

function M.get_pk_end_time()
    local game_id = ARGV[1]
    local pk_id = ARGV[2]
    local end_time_key = get_pk_end_time_key(game_id)
    local end_time = rcall('HGET', end_time_key, pk_id)
    if end_time~=false then
        return end_time
    end
    return 0
end

local key_set_pk_sub_phrase = "set:pk:sub:phrase:" --[[pk子阶段 --key:gameId：pkId]]
local key_set_quick_kill_id = "set:quick:kill:cnt:" --[[激活斩杀次数 --key:gameId：pkId]]
local key_set_quick_kill_trigger = "set:quick:kill:trigger:" --[[激活斩杀方 --key:gameId：pkId]]
local key_zset_channel_in_quick_kill = "zset:in:quick:kill:"       --[[已激活斩杀队列 定时器-- key:gameId  member: pkId  score:end_time]]

local PKSubPhrase = {
    Common = 0,
    QuickKill = 1;   -- 斩杀
    PeakPk = 2;      -- 巅峰对决
}

local function get_pk_sub_phrase_key(game_id, pk_id)
    return key_set_pk_sub_phrase..game_id..":"..pk_id
end

local function get_quick_kill_id_key(game_id, pk_id)
    return key_set_quick_kill_id..game_id..":"..pk_id
end

local function get_quick_kill_trigger_key(game_id, pk_id)
    return key_set_quick_kill_trigger..game_id..":"..pk_id
end

local function get_in_quick_kill_key(game_id)
    return key_zset_channel_in_quick_kill..game_id
end

function M.get_pk_sub_phrase()
    local game_id = ARGV[1]
    local pk_id = ARGV[2]
    local pk_sub_phrase_key = get_pk_sub_phrase_key(game_id, pk_id)

    local pk_sub_phrase = rcall("GET", pk_sub_phrase_key)
    if pk_sub_phrase ~= false then
        return tonumber(pk_sub_phrase)
    else
        return 0
    end
end


function M.get_quick_kill_end_pk()
    local game_id = ARGV[1]
    local expire_time = tonumber(ARGV[2])
    local limit = tonumber(ARGV[3])

    local ret = {}
    local in_quick_kill_key = get_in_quick_kill_key(game_id)

    local pk_ids = rcall('ZRANGEBYSCORE', in_quick_kill_key, '-INF', expire_time, 'LIMIT', 0, limit)
    for ix = 1, #pk_ids do
        table.insert(ret, tonumber(pk_ids[ix]))
        rcall("ZREM", in_quick_kill_key, pk_ids[ix])
        local pk_sub_phrase_key = get_pk_sub_phrase_key(game_id, pk_ids[ix])
        rcall('SETEX', pk_sub_phrase_key, PK_EXPIRE, PKSubPhrase.Common)
    end

    return ret
end


function M.enter_quick_kill_phrase()
    local game_id = ARGV[1]
    local pk_id = ARGV[2]
    local trigger_channel_id = ARGV[3]
    local end_time = ARGV[4]

    local pk_sub_phrase_key = get_pk_sub_phrase_key(game_id, pk_id)
    local pk_sub_phrase = rcall("GET", pk_sub_phrase_key)
    if pk_sub_phrase ~= false and tonumber(pk_sub_phrase) ~= PKSubPhrase.Common then
        return -1
    end

    --[[ 更新阶段信息 ]]
    local pk_sub_phrase_key = get_pk_sub_phrase_key(game_id, pk_id)
    rcall("SET", pk_sub_phrase_key, PKSubPhrase.QuickKill, 'EX', PK_EXPIRE)

    --[[ 增加激活斩杀次数 ]]
    local quick_kill_id_key = get_quick_kill_id_key(game_id, pk_id)
    local quick_kill_id = rcall("INCR", quick_kill_id_key)
    rcall('EXPIRE', quick_kill_id_key, PK_EXPIRE)

    --[[ 更新触发激活方 ]]
    local quick_kill_trigger_key = get_quick_kill_trigger_key(game_id, pk_id)
    rcall("SET", quick_kill_trigger_key, trigger_channel_id, 'EX', PK_EXPIRE)

    --[[ 加入已激活队列 ]]
    local in_quick_kill_key = get_in_quick_kill_key(game_id)
    rcall('ZADD', in_quick_kill_key, end_time, pk_id)
    rcall('EXPIRE', in_quick_kill_key, PK_EXPIRE)

    return tonumber(quick_kill_id)
end

function M.quit_quick_kill_phrase()
    local game_id = ARGV[1]
    local pk_id = ARGV[2]

    local pk_sub_phrase_key = get_pk_sub_phrase_key(game_id, pk_id)
    local pk_sub_phrase = rcall("GET", pk_sub_phrase_key)
    if tonumber(pk_sub_phrase) ~= PKSubPhrase.QuickKill then
        return -1
    end

    --[[ 更新阶段信息 ]]
    local pk_sub_phrase_key = get_pk_sub_phrase_key(game_id, pk_id)
    rcall("SET", pk_sub_phrase_key, PKSubPhrase.Common, 'EX', PK_EXPIRE)

    --[[ 更新触发激活方 ]]
    local quick_kill_trigger_key = get_quick_kill_trigger_key(game_id, pk_id)
    rcall("DEL", quick_kill_trigger_key)

    --[[ 移出已激活队列 ]]
    local in_quick_kill_key = get_in_quick_kill_key(game_id)
    rcall('ZREM', in_quick_kill_key, pk_id)

    return RET_SUCC
end

--[[
获取斩杀激活信息
    ARGV['get_quick_kill_info', game_id, pk_id]
返回值
    list [quick_kill_id, trigger_channel_id, end_time]
--]]
function M.get_quick_kill_info()
    local game_id = ARGV[1]
    local pk_id = ARGV[2]
    local ret = {}

    --[[ 增加激活斩杀次数 ]]
    local quick_kill_id_key = get_quick_kill_id_key(game_id, pk_id)
    local quick_kill_id = rcall("GET", quick_kill_id_key)
    if quick_kill_id ~= false then
        table.insert(ret, tonumber(quick_kill_id))
    else
        table.insert(ret, 0)
    end

    --[[ 触发激活方 ]]
    local quick_kill_trigger_key = get_quick_kill_trigger_key(game_id, pk_id)
    local trigger_channel_id = rcall("GET", quick_kill_trigger_key)
    if trigger_channel_id ~= false then
        table.insert(ret, tonumber(trigger_channel_id))
    else
        table.insert(ret, 0)
    end

    --[[ 结束时间 ]]
    local in_quick_kill_key = get_in_quick_kill_key(game_id)
    local end_time = rcall('ZSCORE', in_quick_kill_key, pk_id)
    if end_time ~= false then
        table.insert(ret, tonumber(end_time))
    else
        table.insert(ret, 0)
    end

    return ret
end


function M.begin_peak_pk()
    local game_id = ARGV[1]
    local channel_id = ARGV[2]
    local dst_channel_id = ARGV[3]
    local current_time = ARGV[4]
    local pk_sec = ARGV[5]
    local pk_id = ARGV[6]

    local pk_sub_phrase_key = get_pk_sub_phrase_key(game_id, pk_id)
    local pk_sub_phrase = rcall("GET", pk_sub_phrase_key)
    if pk_sub_phrase ~= false and tonumber(pk_sub_phrase) ~= PKSubPhrase.Common then
        return -1
    end

    --[[ 更新阶段信息 ]]
    local pk_sub_phrase_key = get_pk_sub_phrase_key(game_id, pk_id)
    rcall("SET", pk_sub_phrase_key, PKSubPhrase.PeakPk, 'EX', PK_EXPIRE)

    return M.begin_pk(game_id, channel_id, dst_channel_id, current_time, pk_sec, pk_id)
end




local func_name = KEYS[1]
local func = M[func_name];
if func then
    return func();
else
    return "unknown func ..." .. func_name
end

return M`