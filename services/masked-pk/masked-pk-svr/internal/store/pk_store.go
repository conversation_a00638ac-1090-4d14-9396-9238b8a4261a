package store

import (
	"time"

	"golang.52tt.com/pkg/log"

	"github.com/go-sql-driver/mysql"
	"github.com/jinzhu/gorm"
)

//PKInfo PK信息
// alter table pk_info add extra_score INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '额外值'
// alter table pk_info add result INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'pk结果'
type PKInfo struct {
	GameId       uint32    `gorm:"primary_key;" sql:"type:INT(10) NOT NULL COMMENT 'game_id'"`    //
	PKId         uint32    `gorm:"primary_key;" sql:"type:INT(10) NOT NULL COMMENT 'pk_id'"`      //
	ChannelId    uint32    `gorm:"primary_key;" sql:"type:INT(10) NOT NULL COMMENT 'channel_id'"` //房间ID
	Score        uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '礼物值'"`
	StartTime    time.Time `gorm:"index:start_time_index" sql:"DEFAULT:current_timestamp"`  //PK开始时间
	EndTime      time.Time `gorm:"index:end_time_index" sql:"DEFAULT:current_timestamp"`    //PK结束时间
	UpdateTime   time.Time `gorm:"index:update_time_index" sql:"DEFAULT:current_timestamp"` //PK最后更新时间
	BeforePkChip uint32

	ExtraScore uint32 `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '额外值'"`
	Result     uint32 `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'pk结果'"`
}

func (t *PKInfo) TableName() string {
	return "pk_info"
}

//ReviveInfo 复活信息
type ReviveInfo struct {
	GameId       uint32    `gorm:"primary_key;" sql:"type:INT(10) NOT NULL COMMENT 'game_id'"`    //
	ReviveId     uint32    `gorm:"primary_key;" sql:"type:INT(10) NOT NULL COMMENT 'revive_id'"`  //
	ChannelId    uint32    `gorm:"primary_key;" sql:"type:INT(10) NOT NULL COMMENT 'channel_id'"` //房间ID
	Score        uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '礼物值'"`
	StartTime    time.Time `gorm:"index:start_time_index" sql:"DEFAULT:current_timestamp"`  //PK开始时间
	EndTime      time.Time `gorm:"index:end_time_index" sql:"DEFAULT:current_timestamp"`    //PK结束时间
	UpdateTime   time.Time `gorm:"index:update_time_index" sql:"DEFAULT:current_timestamp"` //PK最后更新时间
	BeforePkChip uint32
}

func (t *ReviveInfo) TableName() string {
	return "revive_info"
}

//QuickKillRecord 斩杀记录
type QuickKillRecord struct {
	GameId        uint32    `gorm:"primary_key;" sql:"type:INT(10) NOT NULL COMMENT 'game_id'"`    //
	PKId          uint32    `gorm:"primary_key;" sql:"type:INT(10) NOT NULL COMMENT 'pk_id'"`      //
	ChannelId     uint32    `gorm:"primary_key;" sql:"type:INT(10) NOT NULL COMMENT 'channel_id'"` //房间ID
	QuickKillId   uint32    `gorm:"primary_key;" sql:"type:INT(10) NOT NULL COMMENT '斩杀id'"`       //斩杀id
	BeforePkScore uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '激活斩杀前的pk值'"`
	StartTime     time.Time `sql:"DEFAULT:current_timestamp"` //开始时间
	EndTime       time.Time `sql:"DEFAULT:current_timestamp"` //结束时间
	Fee           uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '流水'"`
	UpdateTime    time.Time `gorm:"index:update_time_index" sql:"DEFAULT:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"` //最后更新时间
	KillSuccess   bool
}

func (t *QuickKillRecord) TableName() string {
	return "quick_kill_record"
}

//PeakPkRecord 巅峰对决记录
type PeakPkRecord struct {
	GameId    uint32 `gorm:"primary_key;" sql:"type:INT(10) NOT NULL COMMENT 'game_id'"`    //
	PKId      uint32 `gorm:"primary_key;" sql:"type:INT(10) NOT NULL COMMENT 'pk_id'"`      //
	ChannelId uint32 `gorm:"primary_key;" sql:"type:INT(10) NOT NULL COMMENT 'channel_id'"` //房间ID
	//StartTime     time.Time `sql:"DEFAULT:current_timestamp"`                                      //开始时间
	//EndTime       time.Time `sql:"DEFAULT:current_timestamp"`                                      //结束时间
	Fee           uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '巅峰对决期间流水'"`
	BeforePkScore uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '进入巅峰对决前的pk值'"`
	AwardPkScore  uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '奖励的pk值'"`
	UpdateTime    time.Time `gorm:"index:update_time_index" sql:"DEFAULT:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"` //最后更新时间
}

func (t *PeakPkRecord) TableName() string {
	return "peak_pk_record"
}

func (st *Store) getPKInfoTable() *gorm.DB {
	return st.GetPKMysql().Table("pk_info")
}

func (st *Store) getReviveTable() *gorm.DB {
	return st.GetPKMysql().Table("revive_info")
}

func (st *Store) getQuickKillRecordTable() *gorm.DB {
	return st.GetPKMysql().Table("quick_kill_record")
}

func (st *Store) CreateQuickKillRecord(gameId, pkId, channelId, quickKillId, beforePkScore uint32, startTime, endTime time.Time) error {
	info := &QuickKillRecord{
		GameId:        gameId,
		ChannelId:     channelId,
		PKId:          pkId,
		QuickKillId:   quickKillId,
		BeforePkScore: beforePkScore,
		StartTime:     startTime,
		EndTime:       endTime,
	}

	err := st.getQuickKillRecordTable().Create(info).Error
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok {
			// Duplicate entry
			if mysqlErr.Number == 1062 {
				return nil
			}
		}
		log.Errorf("CreateQuickKillRecord fail. gameId:%d, channelId:%d, pkId:%d, err:%v", gameId, channelId, pkId, err)
		return err
	}

	return nil
}

func (st *Store) RecordQuickKillFee(gameId, pkId, channelId, quickKillId, fee uint32) (bool, error) {
	r := st.pkMysql.Exec("update quick_kill_record set fee=fee+? where game_id=? and pk_id=? and channel_id=? and quick_kill_id=?",
		fee, gameId, pkId, channelId, quickKillId)

	err := r.Error
	if err != nil {
		log.Errorf("RecordQuickKillFee fail. gameId:%d, channelId:%d, pkId:%d, quickKillId:%d, fee:%d, err:%v",
			gameId, channelId, pkId, quickKillId, fee, err)
		return false, err
	}

	ok := r.RowsAffected == 1
	return ok, nil
}

func (st *Store) UpdateQuickKillSuccess(gameId, pkId, quickKillId uint32, isSuccess bool) (bool, error) {
	r := st.pkMysql.Exec("update quick_kill_record set kill_success=? where game_id=? and pk_id=?  and quick_kill_id=?",
		isSuccess, gameId, pkId, quickKillId)

	err := r.Error
	if err != nil {
		log.Errorf("UpdateQuickKillFee fail. gameId:%d, pkId:%d, quickKillId:%d, isSuccess:%v, err:%v",
			gameId, pkId, quickKillId, isSuccess, err)
		return false, err
	}

	ok := r.RowsAffected == 1
	return ok, nil
}

func (st *Store) CreatePeakPkRecord(info *PeakPkRecord) error {
	err := st.pkMysql.Create(info).Error
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok {
			// Duplicate entry
			if mysqlErr.Number == 1062 {
				return nil
			}
		}
		log.Errorf("CreatePeakPkRecord fail. info:%+v, err:%v", info, err)
		return err
	}

	return nil
}

func (st *Store) RecordPeakPkFee(gameId, pkId, channelId, fee uint32) (bool, error) {
	r := st.pkMysql.Exec("update peak_pk_record set fee=fee+? where game_id=? and pk_id=? and channel_id=? ",
		fee, gameId, pkId, channelId)

	err := r.Error
	if err != nil {
		log.Errorf("RecordPeakPkFee fail. gameId:%d, channelId:%d, pkId:%d, fee:%d, err:%v",
			gameId, channelId, pkId, fee, err)
		return false, err
	}

	ok := r.RowsAffected == 1
	return ok, nil
}

func (st *Store) InitPKScore(gameId, channelId, pkId, chip uint32, startTime, endTime time.Time) error {
	log.Infof("InitPKScore gameID：%d,channel:%d, pk:%d, start:%v, end:%v", gameId, channelId, pkId, startTime, endTime)
	pkInfo := &PKInfo{
		GameId:       gameId,
		ChannelId:    channelId,
		PKId:         pkId,
		Score:        0,
		ExtraScore:   0,
		StartTime:    startTime,
		EndTime:      endTime,
		BeforePkChip: chip,
	}

	err := st.getPKInfoTable().Create(pkInfo).Error
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok {
			// Duplicate entry
			if mysqlErr.Number == 1062 {
				return nil
			}
		}
		log.Errorf("AddPKGift fail. gameId:%d, channelId:%d, pkId:%d, err:%v", gameId, channelId, pkId, err)
		return err
	}

	return nil
}

func (st *Store) UpdatePKScore(gameId, channelId, pkId, score, extraScore uint32) (bool, error) {
	r := st.getPKInfoTable().Exec("update pk_info set score=score+?, extra_score=extra_score+?, update_time=? where game_id=? and channel_id=? and pk_id=?",
		score, extraScore, time.Now(), gameId, channelId, pkId)

	err := r.Error
	if err != nil {
		log.Errorf("UpdatePKScore fail. gameId:%d, channelId:%d, pkId:%d, score:%d, extraScore:%d, err:%v",
			gameId, channelId, pkId, score, extraScore, err)
		return false, err
	}

	ok := r.RowsAffected == 1
	return ok, nil
}

func (st *Store) UpdatePKEndTime(tx *gorm.DB, gameId, pkId uint32, endTime time.Time) (bool, error) {
	db := st.getMysqlDb(tx)
	r := db.Exec("update pk_info set end_time=?, update_time=? where game_id=? and pk_id=?",
		endTime, time.Now(), gameId, pkId)

	err := r.Error
	if err != nil {
		log.Errorf("UpdatePKResult fail. gameId:%d, pkId:%d, endTime:%v, err:%v",
			gameId, pkId, endTime, err)
		return false, err
	}

	ok := r.RowsAffected > 0
	return ok, nil
}

func (st *Store) UpdatePKResult(tx *gorm.DB, gameId, channelId, pkId, result uint32) (bool, error) {
	db := st.getMysqlDb(tx)
	r := db.Exec("update pk_info set result=?, update_time=? where game_id=? and channel_id=? and pk_id=? and result=0",
		result, time.Now(), gameId, channelId, pkId)

	err := r.Error
	if err != nil {
		log.Errorf("UpdatePKResult fail. gameId:%d, channelId:%d, pkId:%d, result:%d, err:%v",
			gameId, channelId, pkId, result, err)
		return false, err
	}

	ok := r.RowsAffected == 1
	return ok, nil
}

func (st *Store) GetPkIdListByResult(gameId, result, limit uint32, beginTime, endTime time.Time) ([]uint32, error) {
	list := make([]uint32, 0, limit)

	rows, err := st.getPKInfoTable().Select("distinct pk_id").
		Where("end_time >= ? and end_time < ? and game_id=? and result=?", beginTime, endTime, gameId, result).
		Limit(limit).Rows()

	if rows.Err() != nil || err != nil {
		log.Errorf("GetPkIdListByResult fail. gameId:%d, result:%d, beginTime:%v, endTime:%v, err:%v",
			gameId, result, beginTime, endTime, err)
		return list, err
	}

	defer rows.Close()
	for rows.Next() {
		pkId := uint32(0)
		err = rows.Scan(&pkId)
		if err == nil {
			list = append(list, pkId)
		}
	}

	return list, nil
}

func (st *Store) GetPKInfo(gameId, channelId, pkId uint32) (*PKInfo, error) {
	out := &PKInfo{}
	err := st.getPKInfoTable().Find(out, "game_id=? and pk_id=? and channel_id=?", gameId, pkId, channelId).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return out, nil
		}
		log.Errorf("GetPKScore fail gameId:%d, channelId:%d, pkId:%d, err:%v", gameId, channelId, pkId, err)
		return out, err
	}

	// 特殊处理
	out.Score = out.Score + out.ExtraScore
	return out, nil
}

func (st *Store) GetPKInfoByPkId(gameId, pkId uint32) ([]*PKInfo, error) {
	out := make([]*PKInfo, 0)
	err := st.getPKInfoTable().Find(&out, "game_id=? and pk_id=?", gameId, pkId).Error
	if err != nil {
		/*if err == gorm.ErrRecordNotFound {
			return out, nil
		}*/
		log.Errorf("GetPKInfoByPkId fail gameId:%d, pkId:%d, err:%v", gameId, pkId, err)
		return out, err
	}

	for _, info := range out {
		// 特殊处理
		info.Score = info.Score + info.ExtraScore
	}
	return out, nil
}

func (st *Store) AddReviveScore(gameId, channelId, reviveId, chip uint32, startTime, endTime time.Time) error {
	log.Infof("AddReviveScore gameID：%d,channel:%d, pk:%d, start:%v, end:%v", gameId, channelId, reviveId, startTime, endTime)
	info := &ReviveInfo{
		GameId:       gameId,
		ChannelId:    channelId,
		ReviveId:     reviveId,
		Score:        0,
		StartTime:    startTime,
		EndTime:      endTime,
		BeforePkChip: chip,
	}

	err := st.getReviveTable().Create(info).Error
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok {
			// Duplicate entry
			if mysqlErr.Number == 1062 {
				return nil
			}
		}
		log.Errorf("AddReviveScore fail. gameId:%d, channelId:%d, pkId:%d, err:%v", gameId, channelId, reviveId, err)
		return err
	}

	return nil
}

func (st *Store) UpdateReviveScore(gameId, channelId, reviveId, score uint32) (bool, error) {
	r := st.getReviveTable().Exec("update revive_info set score=score+?,update_time=? where game_id=? and channel_id=? and revive_id=?",
		score, time.Now(), gameId, channelId, reviveId)

	err := r.Error
	if err != nil {
		log.Errorf("UpdateReviveScore fail. gameId:%d, channelId:%d,pkId:%d, err:%v", gameId, channelId, reviveId, score, err)
		return false, err
	}

	ok := r.RowsAffected == 1
	return ok, nil
}

func (st *Store) GetReviveInfo(gameId, channelId, reviveId uint32) (*ReviveInfo, error) {
	out := &ReviveInfo{}
	err := st.getReviveTable().Find(out, "game_id=? and channel_id=? and revive_id=?", gameId, channelId, reviveId).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return out, nil
		}
		log.Errorf("GetReviveInfo fail gameId:%d, channelId:%d, reviveId:%d, err:%v", gameId, channelId, reviveId, err)
		return out, err
	}
	return out, nil
}

func (st *Store) GetPKInfoByGameId(gameId uint32) ([]*PKInfo, error) {
	out := make([]*PKInfo, 0)
	err := st.getPKInfoTable().Find(&out, "game_id=?", gameId).Error
	if err != nil {
		log.Errorf("GetPKInfoByPkId fail gameId:%d, pkId:%d, err:%v", gameId, err)
		return out, err
	}
	return out, nil
}
