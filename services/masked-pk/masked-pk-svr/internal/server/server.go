package server

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	switch_scheme_checker "golang.52tt.com/protocol/services/switch-scheme-checker"
	"golang.52tt.com/services/masked-pk/masked-pk-svr/internal/cache"
	"golang.52tt.com/services/masked-pk/masked-pk-svr/internal/conf"
	"golang.52tt.com/services/masked-pk/masked-pk-svr/internal/event"
	"golang.52tt.com/services/masked-pk/masked-pk-svr/internal/mgr"
	store2 "golang.52tt.com/services/masked-pk/masked-pk-svr/internal/store"
	"google.golang.org/grpc"
	"io/ioutil"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"github.com/jinzhu/gorm"
	"gitlab.ttyuyin.com/golang/gudetama/oss/datacenter"
	"golang.52tt.com/clients/account"
	anchorcontractgo "golang.52tt.com/clients/anchorcontract-go"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/clients/channel"
	channelmsgexpress "golang.52tt.com/clients/channel-msg-express"
	"golang.52tt.com/clients/channelguild"
	"golang.52tt.com/clients/guild"
	masked_pk_score "golang.52tt.com/clients/masked-pk-score"
	pgcchannelpk "golang.52tt.com/clients/pgc-channel-pk"
	reconcilePresent "golang.52tt.com/clients/reconcile-v2-svr/reconcile-present"
	"golang.52tt.com/clients/taillight"
	userprofileapi "golang.52tt.com/clients/user-profile-api"
	youknowwho "golang.52tt.com/clients/you-know-who"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	channelPB "golang.52tt.com/protocol/app/channel"
	maskedPkLogic "golang.52tt.com/protocol/app/masked-pk-logic"
	"golang.52tt.com/protocol/common/status"
	pbTl "golang.52tt.com/protocol/services/activity-taillight"
	anchorcontract_go "golang.52tt.com/protocol/services/anchorcontract-go"
	Channel "golang.52tt.com/protocol/services/channelsvr"
	Guild "golang.52tt.com/protocol/services/guildsvr"
	pb "golang.52tt.com/protocol/services/masked-pk-svr"
	roomranking "golang.52tt.com/protocol/services/masked-room-rank"
	pgcchannelgame "golang.52tt.com/protocol/services/pgc-channel-game"
	"golang.org/x/net/context/ctxhttp"
)

type MaskedPKSvrServer struct {
	sc       *conf.StartConfig
	bc       *conf.BusinessConfManager
	store    *store2.Store
	cache    *cache.Cache
	mgr      *mgr.Mgr
	event    *event.KafkaEvent
	shutDown chan interface{}

	ChannelMsgCli     *channelmsgexpress.Client
	ApicneterCli      *apicenter.Client
	ChannelCli        *channel.Client
	AccountCli        *account.Client
	ChannelGuildCli   *channelguild.Client
	MaskedPkScoreCli  *masked_pk_score.Client
	guildCli          *guild.Client
	AnchorCli         *anchorcontractgo.Client
	TailLightCli      *taillight.Client
	httpClient        *http.Client
	userProfileCli    *userprofileapi.Client
	youKnowWhoCli     *youknowwho.Client
	rPresentCli       *reconcilePresent.Client
	pgcChannelPkCli   *pgcchannelpk.Client
	pgcChannelGameCli *pgcchannelgame.Client
	offerRoomCli      switch_scheme_checker.SwitchSchemeCheckerClient
	timerD            *timer.Timer
}

// OSSPKInfo 上报OSS信息
type OSSPKInfo struct {
	StartTime      time.Time
	GameId         uint32
	PKId           uint32
	ChannelGame    *store2.PkGameChannelInfo
	ChannelWaiSec  uint32
	DstChannel     uint32
	DstChannelGame *store2.PkGameChannelInfo
}

type ChannelRankData struct {
	Code uint32                        `json:"code"`
	Msg  string                        `json:"msg"`
	Data *roomranking.GetMaskPkRankRes `json:"data"`
}

type ChannelEntryData struct {
	Code uint32                                 `json:"code"`
	Msg  string                                 `json:"msg"`
	Data *roomranking.CheckMaskedPkRankEntryRes `json:"data"`
}

func NewMaskedPKSvrServer(ctx context.Context, sc *conf.StartConfig) (*MaskedPKSvrServer, error) {

	st, err := store2.NewStore(sc)
	if err != nil {
		log.Errorf("NewStore failed, err:%v", err)
		return nil, err
	}

	cache, err := cache.NewCache(sc)
	if err != nil {
		log.Errorf("NewCache failed, err:%v", err)
		return nil, err
	}

	bc, err := conf.NewBusinessConfManager()
	if err != nil {
		return nil, err
	}

	channelMsgCli, _ := channelmsgexpress.NewClient()
	channelCli := channel.NewClient()
	accountCli, _ := account.NewClient()
	channelGuildCli := channelguild.NewClient()
	maskedPkScoreCli, _ := masked_pk_score.NewClient()
	anchorCli, _ := anchorcontractgo.NewClient()
	guildCli := guild.NewClient()
	taillightCli, _ := taillight.NewClient()
	userProfileCli, _ := userprofileapi.NewClient()
	youKnowWhoCli, _ := youknowwho.NewClient()
	rPresentCli, _ := reconcilePresent.NewClient()
	pgcChannelPkCli, _ := pgcchannelpk.NewClient()
	pgcChannelGameCli, _ := pgcchannelgame.NewClient(ctx)

	offerRoomCli := client.NewInsecureGRPCClient(
		"offer-room",
		func(cc *grpc.ClientConn) interface{} {
			return switch_scheme_checker.NewSwitchSchemeCheckerClient(cc)
		},
		grpc.WithInsecure(),
		grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor),
	)

	mgr := mgr.NewMgr(sc, bc, st, cache)
	event, err := event.NewKafkaEvent(sc, st, mgr, cache)
	if err != nil {
		log.Errorf("NewKafkaEvent failed, err:%v", err)
	}

	s := &MaskedPKSvrServer{
		sc:                sc,
		bc:                bc,
		store:             st,
		cache:             cache,
		shutDown:          make(chan interface{}),
		ChannelMsgCli:     channelMsgCli,
		ApicneterCli:      apicenter.NewClient(),
		ChannelCli:        channelCli,
		AccountCli:        accountCli,
		ChannelGuildCli:   channelGuildCli,
		MaskedPkScoreCli:  maskedPkScoreCli,
		guildCli:          guildCli,
		AnchorCli:         anchorCli,
		TailLightCli:      taillightCli,
		httpClient:        &http.Client{Timeout: time.Second * 2},
		mgr:               mgr,
		event:             event,
		userProfileCli:    userProfileCli,
		youKnowWhoCli:     youKnowWhoCli,
		rPresentCli:       rPresentCli,
		pgcChannelPkCli:   pgcChannelPkCli,
		pgcChannelGameCli: pgcChannelGameCli,
		offerRoomCli:      offerRoomCli.Stub().(switch_scheme_checker.SwitchSchemeCheckerClient),
	}

	s.InitTimer()

	return s, nil
}

func (s *MaskedPKSvrServer) GetChannelMaskedPKStatus(ctx context.Context, in *pb.GetChannelMaskedPKStatusReq) (*pb.GetChannelMaskedPKStatusResp, error) {
	out := &pb.GetChannelMaskedPKStatusResp{}

	gameConf, _, err := s.checkMaskedPKConfAndAnchorList(ctx, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKStatus fail to checkMaskedPKConfAndAnchorList. in:%+v, err:%v", in, err)
		return out, nil
	}

	pkStatus, err := s.cache.GetPKStatus(gameConf.GameId, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKStatus fail to GetPKStatus. in:%+v, err:%v", in, err)
		return out, err
	}

	out.Status = pkStatus
	return out, nil
}

func (s *MaskedPKSvrServer) BatchGetChannelMaskedPKStatus(ctx context.Context, in *pb.BatchGetChannelMaskedPKStatusReq) (*pb.BatchGetChannelMaskedPKStatusResp, error) {
	out := &pb.BatchGetChannelMaskedPKStatusResp{}
	var err error
	if len(in.GetChannelIdList()) == 0 {
		return out, nil
	}

	out.ChannelStatusMap, err = s.cache.BatchGetPKStatus(in.GetGameId(), in.GetChannelIdList())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetChannelMaskedPKStatus fail to BatchGetPKStatus. in:%+v, err:%v", in, err)
		return out, err
	}

	return out, nil
}

func (s *MaskedPKSvrServer) CancelChannelMaskedPK(ctx context.Context, in *pb.CancelChannelMaskedPKReq) (*pb.CancelChannelMaskedPKResp, error) {
	out := &pb.CancelChannelMaskedPKResp{}
	now := time.Now()

	// get conf
	gameConf, exist, err := s.GetCurrMaskedGameConfig(ctx, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK fail to GetCurrMaskedGameConfig. in:%+v, err:%v", in, err)
		return out, err
	}

	if !exist || now.Unix() > gameConf.EndTime.Unix() {
		// pk活动不在有效期
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK. in:%+v, err:curr game conf not exist", in)
		return out, protocol.NewExactServerError(nil, status.ErrMaskedPkNotInActivityTime)
	}

	gameId := gameConf.GameId
	channelId := in.ChannelId

	pkStatus, err := s.cache.GetPKStatus(gameId, channelId)
	if err != nil {
		log.Errorf("GetPKStatus fail to  channelId:%+v, err:%v", channelId, err)
		return out, protocol.NewExactServerError(nil, status.ErrMaskedPkNotInActivityTime)
	}

	if pkStatus == uint32(pb.ChannelMaskedPKStatus_InPKing) {
		// 正在PK不能取消
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK. in:%+v, err:in pk cannot cancel", in)
		return out, protocol.NewExactServerError(nil, status.ErrMaskedPkInPkCannotCancel)
	}

	// get game channel info
	gameChannelInfo, exist, err := s.store.GetPkGameChannelInfo(nil, ctx, gameId, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK fail to GetPkGameChannelInfo. in:%+v, err:%v", in, err)
		return out, err
	}

	if !exist {
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK. in:%+v, err:gameChannelInfo not found", in)
		return out, protocol.NewExactServerError(nil, status.ErrMaskedPkNotJoinGame)
	}

	if gameChannelInfo.RestCancelCnt == 0 {
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK. in:%+v, err:cancel cnt limit", in)
		return out, protocol.NewExactServerError(nil, status.ErrMaskedPkCancelCntLimit)
	}

	// cancel pk match
	cancelRet, err := s.cache.CancelMatch(gameId, channelId, gameConf.UseWhiteListOrPrior())
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK fail to CancelMatch. in:%+v, err:%v", in, err)
		return out, err
	}

	if cancelRet != 0 {
		// 正在PK不能取消
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK fail to CancelMatch. in:%+v, ret:%d", in, cancelRet)
		return out, protocol.NewExactServerError(nil, status.ErrMaskedPkInPkCannotCancel)
	}

	// 减少可取消次数
	ok, err := s.store.DecrPkGameChannelCancelCnt(nil, ctx, gameId, in.GetChannelId(), 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK fail to DecrPkGameChannelCancelCnt. in:%+v, err:%v", in, err)
		return out, err
	}

	if !ok {
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK. in:%+v, rest_cancel_cnt is not enough", in)
	} else {
		s.cache.DelGameChannelInfo(gameId, in.GetChannelId())
	}

	// get new game channel info
	gameChannelInfo, _, err = s.store.GetPkGameChannelInfo(nil, ctx, gameId, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK fail to GetPkGameChannelInfo. in:%+v, err:%v", in, err)
		return out, err
	}
	// get new status
	pkStatus, err = s.cache.GetPKStatus(gameId, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK fail to GetPKStatus. in:%+v, err:%v", in, err)
		return out, err
	}

	_ = s.pushPKStatusChange(ctx, gameChannelInfo, gameConf, pkStatus)

	if gameChannelInfo.WinCnt < gameConf.Withdraw ||
		(gameConf.ProvideChip == uint32(pb.ChannelMaskedPKConf_NoChip) && gameConf.ContinueMatch == 1) {

		err = s.cache.AddReadyMatch(gameId, channelId, gameConf.UseWhiteListOrPrior(), time.Now().Add(time.Second*time.Duration(s.bc.GetPauseSec())).Unix())
		if err != nil {
			log.ErrorWithCtx(ctx, "CancelChannelMaskedPK fail to AddReadyMatch. in:%+v, err:%v", in, err)
			//return out, err
		}
	}

	log.InfoWithCtx(ctx, "CancelChannelMaskedPK in:%+v out:%+v", in, out)
	return out, nil
}

func (s *MaskedPKSvrServer) GiveUpChannelMaskedPK(ctx context.Context, in *pb.GiveUpChannelMaskedPKReq) (*pb.GiveUpChannelMaskedPKResp, error) {
	out := &pb.GiveUpChannelMaskedPKResp{}

	// 检查房间参与资格
	anchorList, err := s.getEntertainmentQualificationByChannel(ctx, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GiveUpChannelMaskedPK fail to getEntertainmentQualificationByChannel. in:%+v, err:%v", in, err)
		return out, err
	}

	if len(anchorList) == 0 {
		log.ErrorWithCtx(ctx, "GiveUpChannelMaskedPK fail. in:%+v, err:Not Qualification", in)
		return out, protocol.NewExactServerError(nil, status.ErrMaskedPkNotQualification)
	}

	pkStatus, err := s.cache.GetPKStatus(in.GetConfId(), in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GiveUpChannelMaskedPK fail to GetPKStatus. in:%+v, err:%v", in, err)
		return out, err
	}

	if pkStatus != uint32(pb.ChannelMaskedPKStatus_NotParticipating) {
		log.ErrorWithCtx(ctx, "GiveUpChannelMaskedPK fail. in:%+v, currPkStatus：%d, err: status is not NotParticipating", in, pkStatus)
		return out, protocol.NewExactServerError(nil, status.ErrMaskedPkCannotGivenUp)
	}

	err = s.cache.SetGiveUpGameCache(in.GetConfId(), in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GiveUpChannelMaskedPK fail to SetGiveUpGameCache. in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "GiveUpChannelMaskedPK in:%+v out:%+v", in, out)
	return out, nil
}

func (s *MaskedPKSvrServer) GetChannelMaskedPKCurrConfWithUser(ctx context.Context, in *pb.GetChannelMaskedPKCurrConfWithUserReq) (*pb.GetChannelMaskedPKCurrConfWithUserResp, error) {
	out := &pb.GetChannelMaskedPKCurrConfWithUserResp{}

	// 检查房间参与资格
	anchorList, err := s.getEntertainmentQualificationByChannel(ctx, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKCurrConfWithUser fail to getEntertainmentQualificationByChannel. in:%+v, err:%v", in, err)
		return out, err
	}

	if len(anchorList) == 0 {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKCurrConfWithUser fail. in:%+v, err:Not Qualification", in)
		return out, protocol.NewExactServerError(nil, status.ErrMaskedPkNotQualification)
	}

	gameConf, exist, err := s.GetCurrMaskedGameConfig(ctx, time.Now())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKCurrConfWithUser fail to GetCurrMaskedGameConfig. in:%+v, err:%v", in, err)
		return out, err
	}

	if exist {
		out.Conf = fillChannelMaskedPKConfPb(gameConf)

		chipReceiveEndTs := uint32(0)
		if out.GetConf().GetChipRole() == uint32(pb.ChannelMaskedPKConf_HaveChip) {
			chipReceiveEndTs = out.GetConf().GetBeginTs() + s.bc.GetJoinGameTimeLimitSec()
		}

		out.GetConf().ChipReceiveEndTs = chipReceiveEndTs

		// 是否放弃参与了
		out.IsGiveUp = s.cache.CheckIfGiveUpGame(gameConf.GameId, in.GetChannelId())
	}

	log.DebugfWithCtx(ctx, "GetChannelMaskedPKCurrConfWithUser in:%+v out:%+v", in, out)
	return out, nil
}

func (s *MaskedPKSvrServer) GetChannelMaskedPKCurrConf(ctx context.Context, in *pb.GetChannelMaskedPKCurrConfReq) (*pb.GetChannelMaskedPKCurrConfResp, error) {
	out := &pb.GetChannelMaskedPKCurrConfResp{}

	gameConf, exist, err := s.GetCurrMaskedGameConfig(ctx, time.Now())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKCurrConf fail to GetCurrMaskedGameConfig. in:%+v, err:%v", in, err)
		return out, err
	}

	if exist {
		out.Conf = fillChannelMaskedPKConfPb(gameConf)

		chipReceiveEndTs := uint32(0)
		if out.GetConf().GetChipRole() == uint32(pb.ChannelMaskedPKConf_HaveChip) {
			chipReceiveEndTs = out.GetConf().GetBeginTs() + s.bc.GetJoinGameTimeLimitSec()
		}
		out.GetConf().ChipReceiveEndTs = chipReceiveEndTs
	}

	log.DebugfWithCtx(ctx, "GetChannelMaskedPKCurrConf in:%+v out:%+v", in, out)
	return out, nil
}

func fillChannelMaskedPKConfPb(gameConf *store2.MaskedGameConfig) *pb.ChannelMaskedPKConf {
	return &pb.ChannelMaskedPKConf{
		BeginTs:         uint32(gameConf.BeginTime.Unix()),
		EndTs:           uint32(gameConf.EndTime.Unix()),
		ChipRole:        gameConf.ProvideChip,
		Chip:            gameConf.ChipAvg,
		ContinueMatch:   gameConf.ContinueMatch,
		AutoMatchingCnt: gameConf.Withdraw,
		JumpUrl:         gameConf.ActivityUrl,
		ConfId:          gameConf.GameId,
		ServerNs:        time.Now().UnixNano(),
		DivideType:      gameConf.DivideType,
		UseBackpack:     gameConf.UseBackpack,
	}
}

func (s *MaskedPKSvrServer) GetChannelMaskedPKInfo(ctx context.Context, in *pb.GetChannelMaskedPKInfoReq) (*pb.GetChannelMaskedPKInfoResp, error) {
	out := &pb.GetChannelMaskedPKInfoResp{}
	cid := in.GetChannelId()

	// 检查房间参与资格
	anchorList, err := s.getEntertainmentQualificationByChannel(ctx, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKInfo fail to getEntertainmentQualificationByChannel. in:%+v, err:%v", in, err)
		return out, err
	}

	if len(anchorList) == 0 {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKInfo fail. in:%+v, err:Not Qualification", in)
		return out, protocol.NewExactServerError(nil, status.ErrMaskedPkNotQualification)
	}

	gameConf, exist, err := s.GetCurrMaskedGameConfig(ctx, time.Now())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKInfo fail to GetCurrMaskedGameConfig. in:%+v, err:%v", in, err)
		return out, err
	}
	if !exist || time.Now().Unix() > gameConf.EndTime.Unix() {
		// pk活动不在有效期
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKInfo. in:%+v, err:curr game conf not exist", in)
		return out, protocol.NewExactServerError(nil, status.ErrMaskedPkNotInActivityTime)
	}

	out.StatusInfo, err = s.getPkGameChannelStatusInfo(ctx, gameConf.GameId, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKInfo fail to getPkGameChannelStatusInfo. in:%+v, err:%v", in, err)
		return out, err
	}

	if out.StatusInfo.GetStatus() == uint32(pb.ChannelMaskedPKStatus_InPKing) {
		// get pk战况比分
		out.PkBattleInfo, err = s.getPkBattleInfo(ctx, gameConf, cid, out.GetStatusInfo())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelMaskedPKInfo fail to getPkBattleInfo. in:%+v, err:%v", in, err)
			return out, nil
		}

	} else if out.StatusInfo.GetStatus() == uint32(pb.ChannelMaskedPKStatus_InReviving) {
		// get pk复活进度
		out.ReviveInfo, err = s.getPkReviveInfo(ctx, gameConf, cid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelMaskedPKInfo fail to getPkReviveInfo. in:%+v, err:%v", in, err)
			return out, nil
		}
	}

	log.DebugfWithCtx(ctx, "GetChannelMaskedPKInfo in:%+v out:%+v", in, out)
	return out, nil
}

func (s *MaskedPKSvrServer) getPkReviveInfo(ctx context.Context, gameConf *store2.MaskedGameConfig, cid uint32) (*pb.ChannelMaskedPKRevive, error) {
	out := &pb.ChannelMaskedPKRevive{}
	gameId := gameConf.GameId

	// 获取复活id
	reviveId, err := s.cache.GetReviveId(gameId, cid)
	if err != nil {
		log.Errorf("getPkReviveInfo fail to GetReviveId, channel:%d, err:%v", cid, err)
		return out, err
	}

	//get from cache
	reviveInfo, err := s.cache.GetReviveInfoRedis(gameId, cid, reviveId)
	if err != nil {
		log.Errorf("getPkReviveInfo fail to GetReviveInfo, channel:%d, err:%v", cid, err)
		return out, err
	}

	out = &pb.ChannelMaskedPKRevive{
		ChannelId: cid,
		Goal:      gameConf.ReviveTbean,
		Curr:      reviveInfo.Score,
		EndTs:     uint32(reviveInfo.EndTime.Unix()),
		ServerNs:  time.Now().UnixNano(),
	}

	log.DebugfWithCtx(ctx, "getPkReviveInfo gameId:%v, reviveId:%v, cid:%v, out:%+v", gameId, reviveId, cid, out)
	return out, nil
}

func (s *MaskedPKSvrServer) getPkBattleInfo(ctx context.Context, gameConf *store2.MaskedGameConfig, cid uint32, outStatusInfo *pb.ChannelMaskedPKStatus) (*pb.ChannelMaskedPKBattle, error) {
	out := &pb.ChannelMaskedPKBattle{}
	gameId := gameConf.GameId

	pkId, err := s.cache.GetPKId(gameId, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "getPkBattleInfo fail to GetPKId. gameId:%v, pkId:%v, cid:%v, err:%v",
			gameId, pkId, cid, err)
		return out, nil
	}

	if pkId == 0 {
		return out, nil
	}

	//get from cache
	pkInfoList, err := s.cache.GetPKInfoByPkIdRedis(gameId, cid, pkId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getPkBattleInfo fail to GetPKInfoByPkId. gameId:%v, pkId:%v, cid:%v, err:%v",
			gameId, pkId, cid, err)
		return out, nil
	}

	if len(pkInfoList) == 0 {
		return out, nil
	}

	pkSubPhrase, err := s.cache.GetPkSubPhrase(gameConf.GameId, pkId)
	if err != nil {
		log.Errorf("pushPKBattleInfo fail to GetPkSubPhrase pkId:%+v, err:%v", pkId, err)
		//return err
	}

	out.PkId = pkId
	out.PkEndTs = uint32(pkInfoList[0].EndTime.Unix())
	out.SubPhrase = pkSubPhrase
	validPk := false

	differenceValue, triggerCid, quickKillEndTs, _ := s.getPkQuickKillInfo(gameId, pkId, pkSubPhrase)
	peakAwardInfo, _ := s.getPeakPkAwardInfo(gameId, pkId, pkSubPhrase)

	for _, info := range pkInfoList {

		if info.Score >= s.bc.GetValidPkScore() {
			validPk = true
		}

		mem, err := s.getMemPkInfo(ctx, pkSubPhrase, triggerCid, info, gameConf, outStatusInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "getPkBattleInfo fail to getMemPkInfo.  gameId:%v, pkId:%v, cid:%v, err:%v",
				gameId, pkId, info.ChannelId, err)
			return out, err
		}

		if gameConf.CanEnterQuickKill() && pkSubPhrase != uint32(pb.ChannelMaskedPKBattle_PeakPk) {
			condition, quickKillDescPrefix, quickKillDesc := s.genPkQuickKillConditionDesc(pkSubPhrase, info.ChannelId, triggerCid, differenceValue, gameConf)
			mem.QuickKillInfo = &pb.QuickKillInfo{
				EnableMinPkSec:      s.bc.GetMinQuickKillAbleSec(),
				EnableMaxPkSec:      s.bc.GetMaxQuickKillAbleSec(),
				ConditionValue:      condition,
				QuickKillEndTs:      quickKillEndTs,
				QuickKillDesc:       quickKillDesc,
				QuickKillDescPrefix: quickKillDescPrefix,
			}
		}
		if info.ChannelId == peakAwardInfo.Cid && peakAwardInfo.Score > 0 {
			mem.PeakPkInfo = &pb.PeakPkInfo{
				PeakDesc: fmt.Sprintf("当前我方已获得战力值奖励\n%d战力值", peakAwardInfo.Score),
			}
		}

		out.MemList = append(out.MemList, mem)
	}

	out.ValidPk = validPk
	if !validPk {
		out.ValidPkDesc = fmt.Sprintf("当前胜负无效\n%s", s.bc.GetValidPkDesc())
	}

	log.DebugfWithCtx(ctx, "getPkBattleInfo gameId:%v, pkId:%v, cid:%v, out:%+v", gameId, pkId, cid, out)
	return out, err
}

func (s *MaskedPKSvrServer) getPkQuickKillInfo(gameId, pkId, pkSubPhrase uint32) (condition, triggerCid, endTs uint32, err error) {
	if pkSubPhrase != uint32(pb.ChannelMaskedPKBattle_QuickKill) && pkSubPhrase != uint32(pb.ChannelMaskedPKBattle_Common) {
		return
	}

	condition, err = s.cache.GetQuickKillCondition(gameId, pkId)
	if err != nil {
		log.Errorf("getPkQuickKillInfo fail to GetQuickKillCondition. gameId:%v, pkId:%v, err:%v", gameId, pkId, err)
		return
	}

	if pkSubPhrase == uint32(pb.ChannelMaskedPKBattle_QuickKill) {
		_, triggerCid, endTs, err = s.cache.GetQuickKillInfo(gameId, pkId)
		if err != nil {
			log.Errorf("getPkQuickKillInfo fail to GetQuickKillInfo. gameId:%v, pkId:%v, err:%v", gameId, pkId, err)
			return
		}
	}

	return
}

func (s *MaskedPKSvrServer) genPkQuickKillConditionDesc(pkSubPhrase, cid, triggerCid, differenceValue uint32, gameConf *store2.MaskedGameConfig) (uint32, string, string) {

	if !gameConf.CanEnterQuickKill() {
		return 0, "", ""
	}

	if gameConf.ProvideChip == 1 {
		if pkSubPhrase == uint32(pb.ChannelMaskedPKBattle_Common) {
			return differenceValue, "领先", "战力可激活斩杀，斩杀成功后赢对方全部奖金"
		} else if pkSubPhrase == uint32(pb.ChannelMaskedPKBattle_QuickKill) {
			if cid == triggerCid {
				return differenceValue, "保持领先", "战力值30s将直接获胜，可赢全部奖金"
			}
			return differenceValue, "缩小差距到", "内可阻止斩杀，否则30s后将被淘汰"
		}
	} else {
		if pkSubPhrase == uint32(pb.ChannelMaskedPKBattle_Common) {
			return differenceValue, "领先", "战力可激活斩杀"
		} else if pkSubPhrase == uint32(pb.ChannelMaskedPKBattle_QuickKill) {
			if cid == triggerCid {
				return differenceValue, "保持领先", fmt.Sprintf("战力值%ds将直接获胜", s.bc.GetQuickKillSec())
			}
			return differenceValue, "缩小差距到", "内可阻止斩杀"
		}
	}
	return 0, "", ""
}

func (s *MaskedPKSvrServer) getPeakPkAwardInfo(gameId, pkId, pkSubPhrase uint32) (*cache.PeakPkAwardInfo, string) {
	info := &cache.PeakPkAwardInfo{}
	if pkSubPhrase != uint32(pb.ChannelMaskedPKBattle_PeakPk) {
		return info, ""
	}

	durationDesc := fmt.Sprintf("%d分钟后本场PK结束", s.bc.GetPeakPKSec()/60)

	awardInfo, err := s.cache.GetPeakAwardInfo(gameId, pkId)
	if err != nil {
		log.Errorf("getPeakPkAwardInfo fail to GetPeakAwardInfo. gameId:%v, pkId:%v, err:%v", gameId, pkId, err)
		return awardInfo, durationDesc
	}

	return awardInfo, durationDesc
}

func (s *MaskedPKSvrServer) getMemPkInfo(ctx context.Context, pkSubPhrase, triggerCid uint32, info *store2.PKInfo, gameConf *store2.MaskedGameConfig, outStatusInfo *pb.ChannelMaskedPKStatus) (*pb.ChannelMaskedPKInfo, error) {
	pkId := info.PKId
	gameId := gameConf.GameId
	isQuickKill := pkSubPhrase == uint32(pb.ChannelMaskedPKBattle_QuickKill)
	var err error
	var statusInfo *pb.ChannelMaskedPKStatus
	out := &pb.ChannelMaskedPKInfo{}

	if info.ChannelId == outStatusInfo.GetChannelId() {
		statusInfo = outStatusInfo
	} else {
		statusInfo, err = s.getPkGameChannelStatusInfo(ctx, gameId, info.ChannelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "getMemPkInfo fail to getPkGameChannelStatusInfo.  gameId:%v, pkId:%v, cid:%v, err:%v",
				gameId, pkId, info.ChannelId, err)
			return out, err
		}
	}

	lossChip, prefixLossDesc, lossDesc := s.getLossInfo(gameConf, &store2.PkGameChannelInfo{
		ChannelId:  info.ChannelId,
		LossCnt:    statusInfo.GetLossCnt(),
		RevivedCnt: statusInfo.GetRevivedCnt(),
		ChipCnt:    statusInfo.GetCurrChip(),
	}, triggerCid, isQuickKill)

	out = &pb.ChannelMaskedPKInfo{
		ChannelId:      info.ChannelId,
		Score:          info.Score,
		CurrChip:       statusInfo.GetCurrChip(),
		WinCnt:         statusInfo.GetWinCnt(),
		ReviveCnt:      statusInfo.GetRevivedCnt(),
		LossDesc:       lossDesc,
		LossChip:       lossChip, /*statusInfo.GetLossChip()*/
		PrefixLossDesc: prefixLossDesc,
	}

	anchorList := make([]uint32, 0)
	cacheList, err := s.getPkAnchorContributionRankList(ctx, pkId, info.ChannelId, 0, 3, gameConf, anchorList)
	if err != nil {
		log.Errorf("getMemPkInfo fail to getPkAnchorContributionRankList channelId:%+v, err:%v", info.ChannelId, err)
	}

	uidList := make([]uint32, 0)
	for _, m := range cacheList {
		if m.Uid == 0 || m.Score == 0 {
			continue
		}
		uidList = append(uidList, m.Uid)
	}

	uidList = append(uidList, anchorList...)

	userMap := s.getUserProfile(ctx, gameConf.GameId, info.ChannelId, uidList)
	for _, m := range cacheList {
		if m.Uid == 0 || m.Score == 0 {
			continue
		}

		profile := userMap[m.Uid]
		out.TopAnchorList = append(out.TopAnchorList, &pb.ChannelMaskedPKRankMem{
			Uid:   m.Uid,
			Score: m.Score,
			Profile: &pb.UserProfile{
				Uid:      profile.Uid,
				Account:  profile.GetAccount(),
				Nickname: profile.GetNickname(),
				Sex:      profile.GetSex(),
				Privilege: &pb.UserPrivilege{
					Account:  profile.GetPrivilege().GetAccount(),
					Nickname: profile.GetPrivilege().GetNickname(),
					Type:     profile.GetPrivilege().GetType(),
					Options:  profile.GetPrivilege().GetOptions(),
				},
			},
		})
	}

	return out, nil
}

func (s *MaskedPKSvrServer) BatchAddUserToEntertainmentQualification(ctx context.Context, in *pb.BatchAddUserToEntertainmentQualificationReq) (*pb.BatchAddUserToEntertainmentQualificationResp, error) {
	out := &pb.BatchAddUserToEntertainmentQualificationResp{}
	qualifications := make([]store2.EntertainmentQualificationList, 0)
	userList := make([]uint32, 0)
	channelList := make([]uint32, 0)
	for _, qua := range in.Qualifications {
		userList = append(userList, qua.Uid)
		channelList = append(channelList, qua.ChannelId)
	}

	userMap, uErr := s.AccountCli.GetUsersMap(ctx, userList)
	if uErr != nil {
		log.Errorf("BatchAddQualificationChannelHash GetUsersMap err , qualifications: %v , err : %v", qualifications, uErr)
		return out, uErr
	}

	channelMap, uErr := s.ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, channelList)
	if uErr != nil {
		log.Errorf("BatchAddQualificationChannelHash BatchGetChannelSimpleInfo err , qualifications: %v , err : %v", qualifications, uErr)
		return out, uErr
	}

	contractRes, uErr := s.AnchorCli.BatchGetUserContract(ctx, 0, &anchorcontract_go.BatchGetUserContractReq{UidList: userList})
	if uErr != nil {
		log.Errorf("BatchGetUserContract GetUsersMap err , qualifications: %v , err : %v", qualifications, uErr)
		return out, uErr
	}

	contractMap := make(map[uint32]*anchorcontract_go.ContractInfo, 0)
	for _, item := range contractRes.GetContractList() {
		contractMap[item.ActorUid] = item
	}
	failQua := make([]*pb.Qualification, 0)

	for _, qua := range in.Qualifications {
		if _, ok := channelMap[qua.ChannelId]; !ok {
			log.Debugf("channelMap not exist , uid %v , ")
			continue
		}
		userGuild := contractMap[qua.Uid].GetGuildId()
		if _, ok := guild2BigGuild[userGuild]; ok {
			userGuild = guild2BigGuild[userGuild]
		}

		if channelMap[qua.ChannelId].GetChannelType() != uint32(channel.ChnanelTypeGuildPublicFun) {
			continue
		}
		qualifications = append(qualifications, store2.EntertainmentQualificationList{Uid: qua.Uid, ChannelId: qua.ChannelId, GuildId: userGuild})
	}

	fail, err := s.store.AddUserToEntertainmentQualification(ctx, s.bc.GetPkChannelCntLimit(), s.bc.GetPkAnchorCntLimit(), qualifications)
	if err != nil {
		return out, err
	}

	if len(qualifications) != 0 {
		err = s.cache.BatchAddQualificationChannelHash(qualifications)
		if err != nil {
			log.Errorf("BatchAddQualificationChannelHash err , qualifications: %v , err : %v", qualifications, err)
		}
		err = s.cache.BatchAddQualificationUserHash(qualifications)
		if err != nil {
			log.Errorf("BatchAddQualificationUserHash err , qualifications: %v , err : %v", qualifications, err)
		}
		err = s.cache.BatchAddChannelGuildHash(qualifications)
		if err != nil {
			log.Errorf("BatchAddChannelGuildHash err , qualifications: %v , err : %v", qualifications, err)
		}
	}

	for _, qua := range fail {
		ttid := ""
		displayId := uint32(0)

		if _, ok := userMap[qua.Uid]; ok {
			ttid = userMap[qua.Uid].Alias
		}

		if _, ok := channelMap[qua.ChannelId]; ok {
			displayId = channelMap[qua.ChannelId].GetDisplayId()
		}

		failQua = append(failQua, &pb.Qualification{Uid: qua.Uid, ChannelId: qua.ChannelId, Ttid: ttid, DisplayId: displayId})
	}
	out.FailQualifications = failQua

	return out, nil
}

func (s *MaskedPKSvrServer) IsUserHasEntertainmentQualification(ctx context.Context, in *pb.IsUserHasEntertainmentQualificationReq) (*pb.IsUserHasEntertainmentQualificationResp, error) {
	out := &pb.IsUserHasEntertainmentQualificationResp{}
	exist, err := s.cache.IsUserHasQualificationByCache(store2.EntertainmentQualificationList{ChannelId: in.ChannelId, Uid: in.Uid})
	if err != nil {
		if err == redis.Nil {
			exist, err = s.store.IsUserHasEntertainmentQualification(ctx, in.Uid, in.ChannelId)
			if err != nil {
				return out, err
			}
			if !exist {
				out.Exist = exist
				_ = s.cache.AddNoneQualificationForUser(in.ChannelId, in.Uid)
				return out, err
			}
			out.Exist = exist
			_ = s.cache.AddQualificationChannelHash(in.ChannelId, in.Uid)
		}
		return out, err
	}
	out.Exist = exist
	return out, nil
}

func (s *MaskedPKSvrServer) BatchAddChannelToWhiteList(ctx context.Context, in *pb.BatchAddChannelToWhiteListReq) (*pb.BatchAddChannelToWhiteListResp, error) {
	out := &pb.BatchAddChannelToWhiteListResp{}
	failDisplay := make([]uint32, 0)

	fail, err := s.BatchAddChannelToWhiteListConf(ctx, in.Channel)
	if err != nil {
		log.Errorf("BatchAddChannelToWhiteList err ,req:%v , err : %v", in, err)
	}

	res, err := s.ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, fail)
	if err != nil {
		log.Errorf("BatchAddChannelToWhiteList - BatchGetChannelSimpleInfo err ,req:%v , err : %v", in, err)
		return out, err
	}

	for _, info := range fail {
		failDisplay = append(failDisplay, res[info].GetDisplayId())
	}

	out.FailDisplay = failDisplay
	return out, nil
}

func (s *MaskedPKSvrServer) BatchDelChannelToWhiteList(ctx context.Context, in *pb.BatchDelChannelToWhiteListReq) (*pb.BatchDelChannelToWhiteListResp, error) {
	out := &pb.BatchDelChannelToWhiteListResp{}
	failDisplay := make([]uint32, 0)
	log.InfoWithCtx(ctx, "BatchDelChannelToWhiteList - in:%v", in)
	if in.GetDeleteAll() == 1 {
		err := s.store.DeleteAllWhiteListChannel(ctx)
		if err != nil {
			log.Errorf("BatchDelChannelToWhiteList err ,req:%v , err : %v", in, err)
		}

		err = s.cache.DeleteAllWhiteListChannelByCache()
		if err != nil {
			log.Errorf("BatchDelChannelToWhiteList err ,req:%v , err : %v", in, err)
		}
		log.InfoWithCtx(ctx, "BatchDelChannelToWhiteList - DeleteAllWhiteListChannelByCache:%v", in)
		return out, nil
	}

	fail, err := s.BatchDelChannelFromWhiteListConf(ctx, in.Channel)
	if err != nil {
		log.Errorf("BatchDelChannelToWhiteList err ,req:%v , err : %v", in, err)
	}

	res, err := s.ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, fail)
	if err != nil {
		log.Errorf("BatchDelChannelToWhiteList - BatchGetChannelSimpleInfo err ,req:%v , err : %v", in, err)
		return out, err
	}

	for _, info := range res {
		failDisplay = append(failDisplay, info.GetDisplayId())
	}

	out.FailDisplay = failDisplay
	return out, nil
}

func (s *MaskedPKSvrServer) GetAllWhiteList(ctx context.Context, in *pb.GetAllWhiteListReq) (*pb.GetAllWhiteListResp, error) {
	list := make([]*pb.WhiteListConfig, 0)
	out := &pb.GetAllWhiteListResp{List: list}
	res, err := s.GetAllWhiteListConf(ctx)
	if err != nil {
		log.Errorf("GetAllWhiteList err ,req:%v , err : %v", in, err)
		return out, err
	}

	channelList := make([]uint32, 0)
	for _, cha := range res {
		channelList = append(channelList, cha.ChannelId)
	}
	channelMap, err := s.ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, channelList)
	if err != nil {
		log.Errorf("GetAllWhiteList err ,req:%v , err : %v", in, err)
		return out, err
	}

	guildList := make([]uint32, 0)
	for _, info := range channelMap {
		guildList = append(guildList, info.GetBindId())
	}

	guildResp, err := s.guildCli.GetGuildBat(ctx, 0, &Guild.GetGuildBatReq{GuildIdList: guildList, WithDismissed: false})
	if err != nil {
		log.Errorf("GetAllWhiteList err ,req:%v , err : %v", in, err)
		return out, err
	}
	guildMap := make(map[uint32]string, 0)
	for _, gui := range guildResp.GetGuildList() {
		guildMap[gui.GuildId] = gui.Name
	}

	for _, cid := range res {
		channelName, guildName := "", ""
		displayId := uint32(0)
		if _, ok := channelMap[cid.ChannelId]; ok {
			channelName = channelMap[cid.ChannelId].GetName()
			displayId = channelMap[cid.ChannelId].GetDisplayId()
			if _, ok := guildMap[channelMap[cid.ChannelId].GetBindId()]; ok {
				guildName = guildMap[channelMap[cid.ChannelId].GetBindId()]
			}
		}

		out.List = append(out.List, &pb.WhiteListConfig{
			ChannelId:   cid.ChannelId,
			ChannelName: channelName,
			GuildName:   guildName,
			UpdateTime:  uint32(cid.CreateTime.Unix()),
			DisplayId:   displayId,
		})
	}

	return out, nil
}

func genStoreGameConfig(conf *pb.MaskedGameConfig) *store2.MaskedGameConfig {
	return &store2.MaskedGameConfig{
		BeginTime:       time.Unix(int64(conf.BeginTime), 0),
		EndTime:         time.Unix(int64(conf.EndTime), 0),
		ChipAvg:         conf.ChipAvg,
		ChipCount:       conf.ChipCount,
		Withdraw:        conf.WithDraw,
		ActivityUrl:     conf.ActivityUrl,
		DivideType:      uint32(conf.DivideType),
		DivideCount:     conf.DivideCount,
		ReviveChip:      conf.ReviveChip,
		ProvideChip:     conf.ProvideChip,
		ContinueMatch:   conf.ContinueMatch,
		DieOutCount:     conf.DieOutCount,
		AwardDivideUser: conf.AwardDivideUser,
		ReviveTbean:     conf.ReviveTbean,
		UseBackpack:     conf.UseBackpack,
		QuickKill:       conf.QuickKill,
	}
}

func (s *MaskedPKSvrServer) AddMaskedGameConfig(ctx context.Context, in *pb.AddMaskedGameConfigReq) (*pb.AddMaskedGameConfigResp, error) {
	out := &pb.AddMaskedGameConfigResp{}

	if in.GetConfig().GetWithDraw() <= 0 && in.GetConfig().GetProvideChip() != 0 {
		return out, protocol.NewExactServerError(nil, -6, "提现场次不能为0场")
	}

	gameConfig := genStoreGameConfig(in.GetConfig())

	err := s.AddMaskedGameConfigConf(ctx, gameConfig)
	if err != nil {
		log.Errorf("AddMaskedGameConfig err ,req:%v , err : %v", in, err)
		return out, protocol.ToServerError(err)
	}
	return out, nil
}

func (s *MaskedPKSvrServer) DelMaskedGameConfig(ctx context.Context, in *pb.DelMaskedGameConfigReq) (*pb.DelMaskedGameConfigResp, error) {
	out := &pb.DelMaskedGameConfigResp{}

	err := s.DelMaskedGameConfigById(ctx, in.GameId)
	if err != nil {
		log.Errorf("DelMaskedGameConfig err ,req:%v , err : %v", in, err)
		return out, err
	}
	return out, nil
}

func (s *MaskedPKSvrServer) BatchDelUserFromEntertainmentQualification(ctx context.Context, in *pb.BatchDelUserFromEntertainmentQualificationReq) (*pb.BatchDelUserFromEntertainmentQualificationResp, error) {
	out := &pb.BatchDelUserFromEntertainmentQualificationResp{}
	qualifications := make([]store2.EntertainmentQualificationList, 0)

	if in.GetDeleteAll() == 1 {
		err := s.store.DeleteAllEntertainmentQualification(ctx)
		if err != nil {
			log.Errorf("BatchDelUserFromEntertainmentQualification err ,req:%v , err : %v", in, err)
		}

		_ = s.cache.DeleteAllEntertainmentQualificationCache(ctx)
		return out, nil
	}

	for _, qua := range in.Qualifications {
		qualifications = append(qualifications, store2.EntertainmentQualificationList{ChannelId: qua.ChannelId, Uid: qua.Uid, GuildId: 0})
	}

	fail, err := s.store.DelUserFromEntertainmentQualification(ctx, qualifications)
	if err != nil {
		return out, err
	}

	_ = s.cache.BatchDelQualificationUserHash(qualifications)

	userList := make([]uint32, 0)
	channelList := make([]uint32, 0)
	for _, qua := range fail {
		userList = append(userList, qua.Uid)
		channelList = append(channelList, qua.ChannelId)
	}

	userMap, uErr := s.AccountCli.GetUsersMap(ctx, userList)
	if uErr != nil {
		log.Errorf("BatchAddQualificationChannelHash GetUsersMap err , qualifications: %v , err : %v", qualifications, uErr)
		return out, uErr
	}

	channelMap, uErr := s.ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, channelList)
	if uErr != nil {
		log.Errorf("BatchAddQualificationChannelHash BatchGetChannelSimpleInfo err , qualifications: %v , err : %v", qualifications, uErr)
		return out, uErr
	}

	failQua := make([]*pb.Qualification, 0)
	for _, qua := range fail {
		ttid := ""
		displayId := uint32(0)

		if _, ok := userMap[qua.Uid]; ok {
			ttid = userMap[qua.Uid].Alias
		}

		if _, ok := channelMap[qua.ChannelId]; ok {
			displayId = channelMap[qua.ChannelId].GetDisplayId()
		}

		failQua = append(failQua, &pb.Qualification{Uid: qua.Uid, ChannelId: qua.ChannelId, Ttid: ttid, DisplayId: displayId})
	}

	out.FailQualifications = failQua
	return out, nil
}

func (s *MaskedPKSvrServer) GetUserFromEntertainmentQualification(ctx context.Context, in *pb.GetUserFromEntertainmentQualificationReq) (*pb.GetUserFromEntertainmentQualificationResp, error) {
	out := &pb.GetUserFromEntertainmentQualificationResp{}

	res, total, err := s.store.GetEntertainmentQualification(ctx, in.Uid, 0, in.Page, in.Count)
	if err != nil {
		return out, err
	}

	qualifications := make([]*store2.EntertainmentQualificationList, 0)
	for _, qualification := range res {
		log.DebugWithCtx(ctx, "GetUserFromEntertainmentQualification qualification:%+v", qualification)
		item := &store2.EntertainmentQualificationList{Uid: qualification.Uid, ChannelId: qualification.ChannelId, GuildId: qualification.GuildId, CreateTime: qualification.CreateTime}
		qualifications = append(qualifications, item)
	}

	userList := make([]uint32, 0)
	for _, cha := range res {
		userList = append(userList, cha.Uid)
	}
	userMap, err := s.AccountCli.GetUsersMap(ctx, userList)
	if err != nil {
		log.Errorf("GetUsersMap err ,req:%v , err : %v", in, err)
		return out, err
	}

	channelList := make([]uint32, 0)
	for _, cha := range res {
		channelList = append(channelList, cha.ChannelId)
	}

	channelMap := make(map[uint32]*Channel.ChannelSimpleInfo)
	for i := 0; i <= len(channelList)/100; i++ {
		begin := i * 100
		end := i*100 + 100
		if end > len(channelList) {
			end = len(channelList)
		}
		resp, err := s.ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, channelList[begin:end])
		if err != nil {
			log.Errorf("BatchGetChannelSimpleInfo err ,req:%v , err : %v", in, err)
			return out, err
		}

		for _, item := range resp {
			channelMap[item.GetChannelId()] = item
		}
	}

	guildList := make([]uint32, 0)
	for _, info := range channelMap {
		guildList = append(guildList, info.GetBindId())
	}

	guildMap := make(map[uint32]*Guild.GuildResp, 0)
	for i := 0; i <= len(guildList)/100; i++ {
		begin := i * 100
		end := i*100 + 100
		if end > len(guildList) {
			end = len(guildList)
		}
		guildResp, err := s.guildCli.GetGuildBat(ctx, 0, &Guild.GetGuildBatReq{GuildIdList: guildList[begin:end], WithDismissed: false})
		if err != nil {
			log.Errorf("GetGuildBat err ,req:%v , err : %v", in, err)
			return out, err
		}
		for _, gui := range guildResp.GetGuildList() {
			guildMap[gui.GuildId] = gui
		}
	}

	for _, cid := range qualifications {
		sex, nickName, channelName, guildName, ttid := "", "", "", "", ""
		displayId, guildShortId, guildId := uint32(0), uint32(0), uint32(0)
		if _, ok := userMap[cid.Uid]; ok {
			if userMap[cid.Uid].Sex == account.Female {
				sex = "女"
			} else {
				sex = "男"
			}
			nickName = userMap[cid.Uid].Nickname
			ttid = userMap[cid.Uid].Alias
		}

		if _, ok := channelMap[cid.ChannelId]; ok {
			channelName = channelMap[cid.ChannelId].GetName()
			if _, ok := guildMap[channelMap[cid.ChannelId].GetBindId()]; ok {
				guildName = guildMap[channelMap[cid.ChannelId].GetBindId()].Name
				guildShortId = guildMap[channelMap[cid.ChannelId].GetBindId()].ShortId
				guildId = guildMap[channelMap[cid.ChannelId].GetBindId()].GuildId
			}
			displayId = channelMap[cid.ChannelId].GetDisplayId()
		}

		out.Qualifications = append(out.Qualifications, &pb.Qualification{
			ChannelId:    cid.ChannelId,
			Uid:          cid.Uid,
			UserNickname: nickName,
			UserSex:      sex,
			ChannelName:  channelName,
			GuildName:    guildName,
			UpdateTime:   uint32(cid.CreateTime.Unix()),
			Ttid:         ttid,
			DisplayId:    displayId,
			GuildId:      guildId,
			GuildShortId: guildShortId,
		})
	}

	out.SumCount = total

	return out, nil
}

func (s *MaskedPKSvrServer) GetChannelFromEntertainmentQualification(ctx context.Context, in *pb.GetChannelFromEntertainmentQualificationReq) (*pb.GetChannelFromEntertainmentQualificationResp, error) {
	out := &pb.GetChannelFromEntertainmentQualificationResp{}

	res, _, err := s.store.GetEntertainmentQualification(ctx, 0, in.ChannelId, 0, 0)
	if err != nil {
		return out, err
	}

	channelQuaMap := make(map[uint32][]*store2.EntertainmentQualificationList)
	channelQuaList := make([]uint32, 0)
	for _, qualification := range res {
		_, ok := channelQuaMap[qualification.ChannelId]
		if ok {
			item := qualification
			channelQuaMap[qualification.ChannelId] = append(channelQuaMap[qualification.ChannelId], &item)
		} else {
			item := qualification
			channelQuaMap[qualification.ChannelId] = []*store2.EntertainmentQualificationList{&item}
			channelQuaList = append(channelQuaList, qualification.ChannelId)
		}
	}
	sort.Slice(channelQuaList, func(i, j int) bool {
		return channelQuaList[i] > channelQuaList[j]
	})

	qualifications := make([]*store2.EntertainmentQualificationList, 0)
	if len(channelQuaList) > int((in.Page+1)*in.Count) {
		for _, qualification := range channelQuaList[in.Page*in.Count : (in.Page+1)*in.Count] {
			for _, qua := range channelQuaMap[qualification] {
				item := &store2.EntertainmentQualificationList{Uid: qua.Uid, ChannelId: qua.ChannelId, GuildId: qua.GuildId, CreateTime: qua.CreateTime}
				qualifications = append(qualifications, item)
			}
		}
	} else if len(channelQuaList) > int((in.Page)*in.Count) {
		for _, qualification := range channelQuaList[in.Page*in.Count:] {
			for _, qua := range channelQuaMap[qualification] {
				item := &store2.EntertainmentQualificationList{Uid: qua.Uid, ChannelId: qua.ChannelId, GuildId: qua.GuildId, CreateTime: qua.CreateTime}
				qualifications = append(qualifications, item)
			}
		}
	}

	userList := make([]uint32, 0)
	for _, cha := range qualifications {
		userList = append(userList, cha.Uid)
	}
	userMap, err := s.AccountCli.GetUsersMap(ctx, userList)
	if err != nil {
		log.Errorf("GetUsersMap err ,req:%v , err : %v", in, err)
		return out, err
	}

	channelList := make([]uint32, 0)
	for _, cha := range qualifications {
		channelList = append(channelList, cha.ChannelId)
	}

	channelMap := make(map[uint32]*Channel.ChannelSimpleInfo)
	for i := 0; i <= len(channelList)/100; i++ {
		begin := i * 100
		end := i*100 + 100
		if end > len(channelList) {
			end = len(channelList)
		}
		resp, err := s.ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, channelList[begin:end])
		if err != nil {
			log.Errorf("BatchGetChannelSimpleInfo err ,req:%v , err : %v", in, err)
			return out, err
		}

		for _, item := range resp {
			channelMap[item.GetChannelId()] = item
		}
	}

	guildList := make([]uint32, 0)
	for _, info := range channelMap {
		guildList = append(guildList, info.GetBindId())
	}

	guildMap := make(map[uint32]*Guild.GuildResp, 0)
	for i := 0; i <= len(guildList)/100; i++ {
		begin := i * 100
		end := i*100 + 100
		if end > len(guildList) {
			end = len(guildList)
		}
		guildResp, err := s.guildCli.GetGuildBat(ctx, 0, &Guild.GetGuildBatReq{GuildIdList: guildList[begin:end], WithDismissed: false})
		if err != nil {
			log.Errorf("GetGuildBat err ,req:%v , err : %v", in, err)
			return out, err
		}
		for _, gui := range guildResp.GetGuildList() {
			guildMap[gui.GuildId] = gui
		}
	}

	for _, cid := range qualifications {
		sex, nickName, channelName, guildName, ttid := "", "", "", "", ""
		displayId, guildId, guildShortId := uint32(0), uint32(0), uint32(0)
		if _, ok := userMap[cid.Uid]; ok {
			if userMap[cid.Uid].Sex == account.Female {
				sex = "女"
			} else {
				sex = "男"
			}
			nickName = userMap[cid.Uid].Nickname
			ttid = userMap[cid.Uid].Alias
		}

		if _, ok := channelMap[cid.ChannelId]; ok {
			channelName = channelMap[cid.ChannelId].GetName()
			if _, ok := guildMap[channelMap[cid.ChannelId].GetBindId()]; ok {
				guildName = guildMap[channelMap[cid.ChannelId].GetBindId()].Name
				guildId = guildMap[channelMap[cid.ChannelId].GetBindId()].GuildId
				guildShortId = guildMap[channelMap[cid.ChannelId].GetBindId()].ShortId
			}
			displayId = channelMap[cid.ChannelId].GetDisplayId()
		}

		out.Qualifications = append(out.Qualifications, &pb.Qualification{
			ChannelId:    cid.ChannelId,
			Uid:          cid.Uid,
			UserNickname: nickName,
			UserSex:      sex,
			ChannelName:  channelName,
			GuildName:    guildName,
			UpdateTime:   uint32(cid.CreateTime.Unix()),
			Ttid:         ttid,
			DisplayId:    displayId,
			GuildId:      guildId,
			GuildShortId: guildShortId,
		})
	}
	log.Debugf("%v", out)
	out.SumCount = uint32(len(channelQuaMap))
	return out, err
}

func (s *MaskedPKSvrServer) UpdateWhiteListConfig(ctx context.Context, in *pb.UpdateWhiteListConfigReq) (*pb.UpdateWhiteListConfigResp, error) {
	out := &pb.UpdateWhiteListConfigResp{}

	err := s.store.ChangeWhiteListConfig(ctx, in.UseWhiteList == 1, in.AllowMatchWithSameGuild == 1, in.UsePrior == 1)
	if err != nil {
		log.Errorf("ChangeWhiteListConfig err ,req:%v , err : %v", in, err)
		return out, err
	}

	err = s.cache.DelMaskedGameConfigFromCache()
	if err != nil {
		log.Errorf("DelMaskedGameConfigFromCache err ,req:%v , err : %v", in, err)
		return out, err
	}

	return out, nil
}

func (s *MaskedPKSvrServer) SetMonthGiftValue(ctx context.Context, in *pb.SetMonthGiftValueReq) (*pb.SetMonthGiftValueResp, error) {
	out := &pb.SetMonthGiftValueResp{}

	_, err := s.cache.InitChannelGift(in.ChannelId, in.Value)
	if err != nil {
		log.Errorf("InitChannelGift err ,req:%v , err : %v", in, err)
		return out, err
	}

	return out, nil
}

func (s *MaskedPKSvrServer) UpdateMaskedGameConfig(ctx context.Context, in *pb.UpdateMaskedGameConfigReq) (*pb.UpdateMaskedGameConfigResp, error) {
	out := &pb.UpdateMaskedGameConfigResp{}

	gameConfig := genStoreGameConfig(in.GetConfig())
	err := s.UpdateMaskedGameConfigConf(ctx, gameConfig)
	if err != nil {
		log.Errorf("UpdateMaskedGameConfig err ,req:%v , err : %v", in, err)
		return out, err
	}

	return out, nil
}

func fillMaskedGameConfigPb(gameConfig *store2.MaskedGameConfig) *pb.MaskedGameConfig {
	return &pb.MaskedGameConfig{
		BeginTime:       uint32(gameConfig.BeginTime.Unix()),
		EndTime:         uint32(gameConfig.EndTime.Unix()),
		ChipAvg:         gameConfig.ChipAvg,
		ChipCount:       gameConfig.ChipCount,
		WithDraw:        gameConfig.Withdraw,
		ActivityUrl:     gameConfig.ActivityUrl,
		DivideType:      pb.DivideType(gameConfig.DivideType),
		DivideCount:     gameConfig.DivideCount,
		ReviveChip:      gameConfig.ReviveChip,
		ProvideChip:     gameConfig.ProvideChip,
		ContinueMatch:   gameConfig.ContinueMatch,
		GameId:          gameConfig.GameId,
		DieOutCount:     gameConfig.DieOutCount,
		AwardDivideUser: gameConfig.AwardDivideUser,
		ReviveTbean:     gameConfig.ReviveTbean,
		UseBackpack:     gameConfig.UseBackpack,
		QuickKill:       gameConfig.QuickKill,
	}
}

func (s *MaskedPKSvrServer) GetAllMaskedGameConfig(ctx context.Context, in *pb.GetAllMaskedGameConfigReq) (*pb.GetAllMaskedGameConfigResp, error) {
	out := &pb.GetAllMaskedGameConfigResp{}
	resp, err := s.GetAllMaskedGameConfigConf(ctx, in.GetExpire)
	if err != nil {
		log.Errorf("UpdateMaskedGameConfig err ,req:%v , err : %v", in, err)
		return out, err
	}
	for _, gameConfig := range resp {
		item := fillMaskedGameConfigPb(gameConfig)
		if gameConfig.BeginTime.Before(time.Now()) {
			item.CanBeModify = false
		} else {
			item.CanBeModify = true
		}

		out.Config = append(out.Config, item)
	}

	return out, nil
}

func (s *MaskedPKSvrServer) GetWhiteListConfig(ctx context.Context, req *pb.GetWhiteListConfigReq) (*pb.GetWhiteListConfigResp, error) {
	out := &pb.GetWhiteListConfigResp{}
	item, err := s.store.GetWhiteListConfig(ctx)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			err = s.store.ChangeWhiteListConfig(ctx, false, false, false)
			if err != nil {
				log.Errorf("ChangeWhiteListConfig err ,req:%v , err : %v", req, err)
				return out, err
			}
			return out, nil
		}
		log.Errorf("GetWhiteListConfig err ,req:%v , err : %v", req, err)
		return out, err
	}

	out.UseWhiteList = item.UseWhiteList
	out.AllowMatchWithSameGuild = item.AllowMatchingWithSameGuild
	out.UsePrior = item.UsePrior
	return out, err
}

// 获取房间有资格的主播列表
func (s *MaskedPKSvrServer) GetChannelQualifiedAnchorList(ctx context.Context, in *pb.GetChannelQualifiedAnchorListReq) (*pb.GetChannelQualifiedAnchorListResp, error) {
	out := &pb.GetChannelQualifiedAnchorListResp{}
	var err error

	out.AnchorUidList, err = s.getEntertainmentQualificationByChannel(ctx, in.GetChannelId())
	if err != nil {
		log.Errorf("GetChannelQualifiedAnchorList fail to getEntertainmentQualificationByChannel, in:%v, err:%v", in, err)
		return out, err
	}

	return out, nil
}

// 获取房间有资格的主播列表
func (s *MaskedPKSvrServer) GetLastMaskedGameConfig(ctx context.Context, in *pb.GetLastMaskedGameConfigReq) (*pb.GetLastMaskedGameConfigResp, error) {
	out := &pb.GetLastMaskedGameConfigResp{}
	var err error

	gameConfig, exist, err := s.GetLastMaskedGameConfigConf(ctx, time.Now())
	if err != nil {
		log.Errorf("GetNearlyMaskedGameConfig fail, in:%v, err:%v", in, err)
		return out, err
	}

	if !exist {
		return out, nil
	}

	out.Config = fillMaskedGameConfigPb(gameConfig)

	return out, nil
}

func (s *MaskedPKSvrServer) ShutDown() {
	close(s.shutDown)
	s.store.Close()
	s.ChannelCli.Close()
	s.AccountCli.Close()
	s.MaskedPkScoreCli.Close()
	s.bc.Close()
}

func (s *MaskedPKSvrServer) GetStore() *store2.Store {
	return s.store
}

func (s *MaskedPKSvrServer) GetCache() *cache.Cache {
	return s.cache
}

func (s *MaskedPKSvrServer) TestPushQuickKillChange(ctx context.Context, in *pb.TestPushQuickKillChangeReq) (*pb.TestPushQuickKillChangeResp, error) {
	out := &pb.TestPushQuickKillChangeResp{}
	err := s.mgr.PushPKQuickKillChange(ctx, in.GetKillChannelId(), in.GetTriggerChannelId(), &maskedPkLogic.QuickKillChangeOpt{
		EventType:        in.GetEventType(),
		TriggerChannelId: in.GetTriggerChannelId(),
		KillChannelId:    in.GetKillChannelId(),
	})

	return out, err
}

func (s *MaskedPKSvrServer) NotifyGodLikeTopUsers(ctx context.Context, in *pb.NotifyGodLikeTopUsersReq) (*pb.NotifyGodLikeTopUsersResp, error) {
	out := &pb.NotifyGodLikeTopUsersResp{}
	gameId := in.GetGameId()
	log.Infof("NotifyGodLikeTopUsers req:%+v", in)
	if s.cache.GetGameTailLightBroadcast(in.GetGameId()) {
		log.Errorf("NotifyGodLikeTopUsers already push req:%+v", in)
		return out, nil
	}

	ctx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Minute)
	defer cancel()

	opt := &maskedPkLogic.MaskedPkTailLight{}

	uidList := make([]uint32, 0)
	pushChannelList := make([]uint32, 0)

	//增加房间尾灯
	channelToUid, uidToScore, err := s.addChannelLight(in)
	if err != nil {
		log.Errorf("addChannelLight err:%v", err)
	}

	for cid, uid := range channelToUid {
		pushChannelList = append(pushChannelList, cid)
		uidList = append(uidList, uid...)
	}

	//增加平台尾灯
	s.addPlatformTaillight(in)

	for _, info := range in.GetUsers() {
		opt.PlatformLight = append(opt.PlatformLight, &maskedPkLogic.TailLightInfo{
			Uid:          info.GetUid(),
			SourceId:     uint32(pbTl.TaillightScope_TaillightScopePlatform),
			Contribution: info.GetContribution(),
		})

		uidList = append(uidList, info.GetUid())
	}

	userMap, err := s.AccountCli.BatGetUserByUid(ctx, uidList...)
	if err != nil {
		log.Errorf("GetUsersMap failed: err：%v", err)
	}

	for idx, info := range opt.GetPlatformLight() {
		uid := info.GetUid()
		if user, ok := userMap[uid]; ok {
			opt.PlatformLight[idx].Nickname = user.GetNickname()
			opt.PlatformLight[idx].Account = user.GetUsername()
			opt.PlatformLight[idx].Sex = uint32(user.GetSex())
			opt.PlatformLight[idx].Uid = user.GetUid()
		}
	}

	sort.SliceStable(opt.PlatformLight, func(i, j int) bool {
		if opt.PlatformLight[i].Contribution > opt.PlatformLight[j].Contribution {
			return true
		}
		if opt.PlatformLight[i].Contribution < opt.PlatformLight[j].Contribution {
			return false
		}
		return opt.PlatformLight[i].Uid < opt.PlatformLight[j].Uid
	})

	participateChannels, err := s.store.GetParticipatedChannels(ctx, in.GetGameId())
	if err != nil {
		log.Errorf("GetParticipatedChannels failed err:%v", err)
	}

	for _, channel := range participateChannels {
		if _, ok := channelToUid[channel.ChannelId]; !ok {
			pushChannelList = append(pushChannelList, channel.ChannelId)
		}
	}

	log.Debugf("game_id:%d,GetParticipatedChannels :%v pushChannelList:%v", in.GetGameId(), participateChannels, pushChannelList)
	if len(pushChannelList) == 0 {
		log.Warnf("no user or channel")
		return out, nil
	}

	for _, channelId := range pushChannelList {
		//本房间获得尾灯用户列表
		opt.ChannelLight = make([]*maskedPkLogic.TailLightInfo, 0)
		if channelUid, ok := channelToUid[channelId]; ok {
			for _, uid := range channelUid {
				opt.ChannelLight = append(opt.ChannelLight, &maskedPkLogic.TailLightInfo{
					Uid:          uid,
					SourceId:     uint32(pbTl.TaillightBizId_TaillightBizIdMaskedPKChannel),
					Contribution: uidToScore[uid],
				})
			}
			for idx, info := range opt.ChannelLight {
				uid := info.GetUid()
				user := userMap[uid]
				opt.ChannelLight[idx].Nickname = user.GetNickname()
				opt.ChannelLight[idx].Account = user.GetUsername()
				opt.ChannelLight[idx].Sex = uint32(user.GetSex())
				opt.ChannelLight[idx].Uid = user.GetUid()
			}
		}

		sort.SliceStable(opt.ChannelLight, func(i, j int) bool {
			if opt.ChannelLight[i].Contribution > opt.ChannelLight[j].Contribution {
				return true
			}
			if opt.ChannelLight[i].Contribution < opt.ChannelLight[j].Contribution {
				return false
			}
			return opt.ChannelLight[i].Uid < opt.ChannelLight[j].Uid
		})

		data, _ := proto.Marshal(opt)
		err := s.mgr.PushMsgToChannel(ctx, channelId, uint32(channelPB.ChannelMsgType_ENTERTAINMENT_MASKED_PK_TAIL_LIGHT),
			"语音房蒙面PK尾灯", data)
		if err != nil {
			log.Errorf("push channelId:%v, opt:%v, err:%v", channelId, opt, err)
			continue
		}
		log.Infof("game_id：%d, push channelId:%v, opt:%v", gameId, channelId, opt)
	}
	s.cache.SetGameTailLightBroadcast(in.GetGameId())
	return out, nil
}

func (s *MaskedPKSvrServer) SendOSSTailLightInfo(gameId, uid, channelId, tailLight, contribution uint32) {
	ossKVMap := make(map[string]interface{})
	ossKVMap["eventTime"] = time.Now().Format("2006-01-02 15:04:05")
	ossKVMap["totalDate"] = time.Now().Format("2006-01-02 15:04:05")
	ossKVMap["gameId"] = gameId
	ossKVMap["channelId"] = channelId
	ossKVMap["uid"] = uid
	ossKVMap["contribution"] = contribution
	ossKVMap["type"] = tailLight
	ossKVMap["gameId"] = gameId
	ossKVMap["pkType"] = "entertainment"
	log.Infof("SendOSSTailLightInfo:%+v", ossKVMap)
	datacenter.StdReportKV("************", ossKVMap)
}

func (s *MaskedPKSvrServer) CheckMaskedPkRankEntry(ctx context.Context, req *pb.CheckMaskedPkRankEntryReq) (*pb.CheckMaskedPkRankEntryResp, error) {
	resp := &pb.CheckMaskedPkRankEntryResp{}
	ret := &ChannelEntryData{}

	if s.bc.GetPostActivityUrl() == 0 {
		log.Errorf("CheckMaskedPkRankEntry post activity url is not open")
		return resp, nil
	}

	err := s.PostActivityServer(ctx, s.bc.GetActivityEntryUrl(), &roomranking.CheckMaskedPkRankEntryReq{}, &ret)
	if err != nil {
		log.Errorf("CheckMaskedPkRankEntry err:%v", err)
		return resp, err
	}
	resp.EntryEnable = ret.Data.EntryEnable
	log.Debugf("CheckMaskedPkRankEntry %v", resp)
	return resp, nil
}

func (s *MaskedPKSvrServer) MaskedPkGetConsumeTopN(ctx context.Context, req *pb.MaskedPkGetConsumeTopNReq) (*pb.MaskedPkGetConsumeTopNResp, error) {
	resp := &pb.MaskedPkGetConsumeTopNResp{}
	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.Errorf("SetDressInUse ctx:%+v", ctx)
	}

	if s.bc.GetPostActivityUrl() == 0 {
		log.Errorf("MaskedPkGetConsumeTopN post activity url is not open")
		return resp, nil
	}

	rankInfo := &ChannelRankData{}
	err := s.PostActivityServer(ctx, s.bc.GetActivityRoomRankUrl(), &roomranking.GetMaskPkRankReq{Uid: si.UserID, ChannelId: req.ChannelId}, &rankInfo)
	if err != nil {
		log.Errorf("GetMaskedPkChannelConsumeTopN uid:%d, err:%v", si.UserID, err)
		return resp, err
	}
	log.Infof("GetMaskedPkChannelConsumeTopN uid:%d, resp:%+v", si.UserID, rankInfo)

	for _, member := range rankInfo.Data.List {
		resp.MemberList = append(resp.MemberList, &pb.MaskedPKConsumeInfo{
			Uid:   member.GetUid(),
			Rank:  member.GetRank(),
			Score: member.GetValue(),
		})
	}

	resp.ChannelId = req.GetChannelId()
	resp.MyInfo = &pb.MaskedPKConsumeInfo{
		Uid:   rankInfo.Data.GetMe().GetUid(),
		Rank:  rankInfo.Data.GetMe().GetRank(),
		Score: rankInfo.Data.GetMe().GetValue(),
	}

	if resp.MyInfo.GetUid() == 0 {
		si, ok := protogrpc.ServiceInfoFromContext(ctx)
		if !ok {
			log.Errorf("SetDressInUse ctx:%+v", ctx)
			return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		}
		resp.MyInfo.Uid = si.UserID
	}
	log.Debugf("MaskedPkGetConsumeTopN uid:%d, resp:%v", si.UserID, resp)
	return resp, nil
}

func (s *MaskedPKSvrServer) PostActivityServer(ctx context.Context, url string, req interface{}, resp interface{}) error {
	reqBody, err := json.Marshal(req)
	if err != nil {
		log.Errorf("POST %s %s Marshal failed: %+v", url, req, err)
		return err
	}

	ret, err := ctxhttp.Post(ctx, s.httpClient, url, "application/json", strings.NewReader(string(reqBody)))
	if err != nil {
		log.Errorf("POST url:%s req:%+v post error:%v", url, req, err)
		return err
	}
	defer ret.Body.Close()

	if ret.StatusCode != http.StatusOK {
		err = fmt.Errorf("POST %s %s status %s", url, req, ret.Status)
		log.Errorf("Response code not OK: %v", err)
		return err
	}

	body, err := ioutil.ReadAll(ret.Body)
	if err != nil {
		log.Errorf("POST %s %s read body failed: %+v", url, req, err)
		return err
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		log.Errorf("POST %s %s unmarshal body failed: %v, body:%s", url, req, err, string(body))
		return err
	}
	log.Infof("Post url:%s, body:%s, req:%+v", url, string(body), req)
	return nil
}

// 发送房间尾灯
func (s *MaskedPKSvrServer) addChannelLight(in *pb.NotifyGodLikeTopUsersReq) (channelToUid map[uint32][]uint32, uidToScore map[uint32]uint32, err error) {
	gameId := in.GetGameId()
	channelToUid = make(map[uint32][]uint32)
	uidToScore = make(map[uint32]uint32)

	anchorList, err := s.cache.GetGameContributionByScore(gameId, s.bc.GetTailLightAnchor())
	if err != nil {
		log.Errorf("PkGameSettle fail to GetGameAnchorContributionList gameId:%v info:%+v err:%v", gameId, in, err)
		return channelToUid, uidToScore, err
	}

	log.Infof("NotifyGodLikeTopUsers game_id:%d get anchorList:%+v, err:%v, TailLightAnchor:%d", in.GetGameId(), anchorList, err, s.bc.GetTailLightAnchor())

	ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
	defer cancel()

	uidList := make([]uint32, 0)
	channelToUid = make(map[uint32][]uint32)
	uidToScore = make(map[uint32]uint32)
	for _, z := range anchorList {
		member, _ := z.Member.(string)
		str := strings.Split(member, ":")
		cid, _ := strconv.Atoi(str[1])
		uidInt, _ := strconv.Atoi(str[2])

		if cid == 0 || uidInt == 0 {
			log.Warnf("NotifyGodLikeTopUsers get cid zero member:%v", member)
			continue
		}

		channelId := uint32(cid)
		uid := uint32(uidInt)

		//开启了神秘人  需要增加真实身份
		trueUid, err := s.youKnowWhoCli.GetTrueUidByFake(ctx, uid)
		if err != nil {
			log.Errorf("GetTrueUidByFake uid:%d, err:%v", uid, err)
		}

		if trueUid != uid {
			log.Debugf("RewardTaillight ykw uid:%d", uid)
			continue
		}

		_, err = s.TailLightCli.RewardTaillight(ctx, &pbTl.RewardTaillightReq{
			BizId:      pbTl.TaillightBizId_TaillightBizIdMaskedPKChannel,
			Scope:      pbTl.TaillightScope_TaillightScopeChannel,
			Uid:        trueUid,
			ChannelId:  channelId,
			EndAt:      in.GetActivityEndTime() + s.bc.GetTailLightSec(),
			ActivityId: "masked-pk-svr",
		})

		if err != nil {
			log.Errorf("RewardTaillight failed uid:%d, err:%v", uid, err)
			continue
		}

		uidList = append(uidList, trueUid)
		channelToUid[channelId] = append(channelToUid[channelId], uid)
		uidToScore[uid] = uint32(z.Score)
		s.SendOSSTailLightInfo(in.GameId, uid, channelId, uint32(pbTl.TaillightScope_TaillightScopeChannel), uint32(z.Score))
	}

	if len(uidList) > 0 {
		s.SendIMMsg(ctx, uidList, "恭喜您在蒙面pk活动中获得蒙面pk房间专属尾灯，活动结束3天后到期")
	}

	return channelToUid, uidToScore, err
}

func (s *MaskedPKSvrServer) addPlatformTaillight(in *pb.NotifyGodLikeTopUsersReq) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
	defer cancel()

	platFormUid := make([]uint32, 0)
	for _, info := range in.GetUsers() {
		for i := 1; i <= int(info.GetCount()); i++ {
			_, err := s.TailLightCli.RewardTaillight(ctx, &pbTl.RewardTaillightReq{
				BizId:      pbTl.TaillightBizId_TaillightBizIdMaskedPKPlatform,
				Scope:      pbTl.TaillightScope_TaillightScopePlatform,
				Uid:        info.GetUid(),
				EndAt:      in.GetActivityEndTime() + s.bc.GetTailLightSec(),
				ActivityId: "masked-pk-svr",
			})

			if err != nil {
				log.Errorf("RewardTaillight failed uid:%d, err:%v", info.GetUid(), err)
				continue
			}
		}
		platFormUid = append(platFormUid, info.GetUid())
		s.SendOSSTailLightInfo(in.GameId, info.GetUid(), 0, uint32(pbTl.TaillightScope_TaillightScopePlatform), info.GetContribution())
	}

	if len(platFormUid) > 0 {
		s.SendIMMsg(ctx, platFormUid, "恭喜您在蒙面pk活动中获得蒙面pk平台专属尾灯，活动结束3天后到期")
	}
	return nil
}

func (s *MaskedPKSvrServer) PushGameBeginConf(ctx context.Context, in *pb.PushGameBeginConfReq) (*pb.PushGameBeginConfResp, error) {
	out := &pb.PushGameBeginConfResp{}

	// 检查房间参与资格
	anchorList, err := s.getEntertainmentQualificationByChannel(ctx, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "PushGameBeginConf fail to getEntertainmentQualificationByChannel. in:%+v, err:%v", in, err)
		return out, err
	}

	if len(anchorList) == 0 {
		log.ErrorWithCtx(ctx, "PushGameBeginConf fail. in:%+v, err:Not Qualification", in)
		return out, protocol.NewExactServerError(nil, status.ErrMaskedPkNotQualification)
	}

	gameConf, exist, err := s.GetCurrMaskedGameConfig(ctx, time.Now())
	if err != nil {
		log.ErrorWithCtx(ctx, "PushGameBeginConf fail to GetCurrMaskedGameConfig. in:%+v, err:%v", in, err)
		return out, err
	}
	if !exist {
		log.ErrorWithCtx(ctx, "PushGameBeginConf fail, in:%+v, err: conf not exist", in)
		return out, nil
	}

	isGiveUp := s.cache.CheckIfGiveUpGame(gameConf.GameId, in.GetChannelId())
	if isGiveUp {
		log.ErrorWithCtx(ctx, "PushGameBeginConf fail, in:%+v, err: user give up", in)
		return out, nil
	}

	err = s.pushGameBeginConf(ctx, []uint32{in.GetChannelId()}, gameConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushGameBeginConf fail to push, in: %+v, err: %v", in, err)
	}
	log.InfoWithCtx(ctx, "PushGameBeginConf, cid: %d", in.GetChannelId())
	return out, err
}
