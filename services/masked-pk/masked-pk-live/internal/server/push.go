package server

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	app "golang.52tt.com/protocol/app"
	channelPB "golang.52tt.com/protocol/app/channel"
	maskedPkLogic "golang.52tt.com/protocol/app/masked-pk-logic"
	gaPush "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/masked-pk-live"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	store2 "golang.52tt.com/services/masked-pk/masked-pk-live/internal/store"
	"time"
)

func (s *MaskedPKSvrServer) pushPKQuickKillChange(ctx context.Context, channelIdA, channelIdB uint32, opt *maskedPkLogic.QuickKillChangeOpt) error {
	data, _ := proto.Marshal(opt)
	err := s.pushMsgToChannel(ctx, channelIdA, uint32(channelPB.ChannelMsgType_LIVE_MASKED_PK_QUICK_KILL_CHANGE),
		"斩杀推送", data)
	if err != nil {
		log.Errorf("pushPKQuickKillChange fail to pushMsgToChannel err: %s, %+v", err.Error(), channelIdA)
	}

	err = s.pushMsgToChannel(ctx, channelIdB, uint32(channelPB.ChannelMsgType_LIVE_MASKED_PK_QUICK_KILL_CHANGE),
		"斩杀推送", data)
	if err != nil {
		log.Errorf("pushPKQuickKillChange fail to pushMsgToChannel err: %s, %+v", err.Error(), channelIdB)
	}

	log.Debugf("pushPKQuickKillChange %v %v opt:%+v", channelIdA, channelIdB, opt)
	return nil
}

func (s *MaskedPKSvrServer) pushPKResult(ctx context.Context, channelIdA, channelIdB uint32, opt *maskedPkLogic.ChannelMaskedPKResult) error {
	data, _ := proto.Marshal(opt)
	err := s.pushMsgToChannel(ctx, channelIdA, uint32(channelPB.ChannelMsgType_LIVE_MASKED_PK_SETTLE_V2),
		"pk结果", data)
	if err != nil {
		log.Errorf("pushPKResult fail to pushMsgToChannel err: %s, %+v", err.Error(), channelIdA)
	}

	err = s.pushMsgToChannel(ctx, channelIdB, uint32(channelPB.ChannelMsgType_LIVE_MASKED_PK_SETTLE_V2),
		"pk结果", data)
	if err != nil {
		log.Errorf("pushPKResult fail to pushMsgToChannel err: %s, %+v", err.Error(), channelIdB)
	}

	log.Debugf("pushPKResult opt:%+v", opt)
	return nil
}

func (s *MaskedPKSvrServer) pushGameBeginConf(ctx context.Context, channelIds []uint32, gameCfg *store2.LiveMaskedGameConfig) error {
	chipReceiveEndTs := uint32(0)
	if gameCfg.ProvideChip == uint32(pb.ChannelMaskedPKConf_HaveChip) {
		chipReceiveEndTs = uint32(gameCfg.BeginTime.Unix()) + s.bc.GetJoinGameTimeLimitSec()
	}

	opt := &maskedPkLogic.ChannelMaskedPKConf{
		BeginTs:          uint32(gameCfg.BeginTime.Unix()),
		EndTs:            uint32(gameCfg.EndTime.Unix()),
		ChipRole:         gameCfg.ProvideChip,
		Chip:             gameCfg.ChipAvg,
		AutoMatchingCnt:  gameCfg.Withdraw,
		ContinueMatch:    gameCfg.ContinueMatch,
		JumpUrl:          gameCfg.ActivityUrl,
		ConfId:           gameCfg.GameId,
		DivideType:       gameCfg.DivideType,
		ServerNs:         time.Now().UnixNano(),
		ChipReceiveEndTs: chipReceiveEndTs,
	}

	data, _ := proto.Marshal(opt)
	return s.pushMsgToChannels(ctx, channelIds, uint32(channelPB.ChannelMsgType_LIVE_MASKED_PK_GAME_BEGIN_V2),
		"pk活动开始", data)
}

func (s *MaskedPKSvrServer) pushGameEndConf(ctx context.Context, channelIds []uint32, gameCfg *store2.LiveMaskedGameConfig) error {
	chipReceiveEndTs := uint32(0)
	if gameCfg.ProvideChip == uint32(pb.ChannelMaskedPKConf_HaveChip) {
		chipReceiveEndTs = uint32(gameCfg.BeginTime.Unix()) + s.bc.GetJoinGameTimeLimitSec()
	}

	opt := &maskedPkLogic.ChannelMaskedPKConf{
		BeginTs:          uint32(gameCfg.BeginTime.Unix()),
		EndTs:            uint32(gameCfg.EndTime.Unix()),
		ChipRole:         gameCfg.ProvideChip,
		Chip:             gameCfg.ChipAvg,
		AutoMatchingCnt:  gameCfg.Withdraw,
		ContinueMatch:    gameCfg.ContinueMatch,
		JumpUrl:          gameCfg.ActivityUrl,
		ConfId:           gameCfg.GameId,
		DivideType:       gameCfg.DivideType,
		ServerNs:         time.Now().UnixNano(),
		ChipReceiveEndTs: chipReceiveEndTs,
	}

	data, _ := proto.Marshal(opt)
	return s.pushMsgToChannels(ctx, channelIds, uint32(channelPB.ChannelMsgType_LIVE_MASKED_PK_GAME_END_V2),
		"pk活动结束", data)
}

func (s *MaskedPKSvrServer) pushPKBattleInfo(ctx context.Context, gameConf *store2.LiveMaskedGameConfig, channelId, dstChannelId, pkId uint32) error {
	channelIds := []uint32{channelId, dstChannelId}
	opt := &maskedPkLogic.ChannelMaskedPKBattle{
		ServerNs: time.Now().UnixNano(),
		ChipRole: gameConf.ProvideChip,
		PkId:     pkId,
	}

	pkSubPhrase, err := s.cache.GetPkSubPhrase(gameConf.GameId, pkId)
	if err != nil {
		log.Errorf("pushPKBattleInfo fail to GetPkSubPhrase pkId:%+v, err:%v", pkId, err)
		//return err
	}

	opt.SubPhrase = pkSubPhrase
	isQuickKill := pkSubPhrase == uint32(pb.ChannelMaskedPKBattle_QuickKill)
	differenceValue, triggerCid, quickKillEndTs, _ := s.getPkQuickKillInfo(gameConf.GameId, pkId, pkSubPhrase)
	peakAwardInfo, durationDesc := s.getPeakPkAwardInfo(gameConf.GameId, pkId, pkSubPhrase)

	validPk := false
	uidList := make([]uint32, 0)
	for _, cid := range channelIds {
		//game, found, err := s.cache.GetPkGameChannelInfo(nil, ctx, gameConf.GameId, cid)
		statusInfo, err := s.getPkGameChannelCacheInfo(ctx, gameConf.GameId, cid)
		if err != nil || statusInfo.GetChannelId() != cid {
			log.Errorf("pushPKBattleInfo fail to getPkGameChannelCacheInfo channelId:%+v, err:%v", cid, err)
			return err
		}

		pk, err := s.cache.GetPKInfoRedis(gameConf.GameId, cid, pkId)
		if err != nil {
			log.Errorf("pushPKBattleInfo fail to GetPKInfoRedis channelId:%+v, err:%v", cid, err)
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		}

		if pk.Score >= s.bc.GetValidPkScore() {
			validPk = true
		}

		lossChip, lossDesc := s.getLossInfo(gameConf, &store2.PkGameChannelInfo{
			ChannelId:  cid,
			LossCnt:    statusInfo.GetLossCnt(),
			RevivedCnt: statusInfo.GetRevivedCnt(),
			ChipCnt:    statusInfo.GetCurrChip(),
		}, triggerCid, isQuickKill)

		pkInfo := &maskedPkLogic.ChannelMaskedPKInfo{
			ChannelId:  cid,
			CurrChip:   statusInfo.GetCurrChip(),
			WinCnt:     statusInfo.GetWinCnt(),
			RevivedCnt: statusInfo.GetRevivedCnt(),
			Score:      pk.Score,
			LossDesc:   lossDesc,
			LossChip:   lossChip,
		}

		if gameConf.CanEnterQuickKill() && pkSubPhrase != uint32(pb.ChannelMaskedPKBattle_PeakPk) {
			condition, quickKillDescPrefix, quickKillDesc := s.genPkQuickKillConditionDesc(pkSubPhrase, cid, triggerCid, differenceValue, gameConf)
			pkInfo.QuickKillInfo = &maskedPkLogic.QuickKillInfo{
				EnableMinPkSec:      s.bc.GetMinQuickKillAbleSec(),
				EnableMaxPkSec:      s.bc.GetMaxQuickKillAbleSec(),
				ConditionValue:      condition,
				QuickKillEndTs:      quickKillEndTs,
				QuickKillDesc:       quickKillDesc,
				QuickKillDescPrefix: quickKillDescPrefix,
			}
		}

		if cid == peakAwardInfo.Cid && peakAwardInfo.Score > 0 {
			pkInfo.PeakPkInfo = &maskedPkLogic.PeakPkInfo{
				PeakDesc: fmt.Sprintf("当前我方已获得PK值奖励\n%dPK值", peakAwardInfo.Score),
			}
		}

		opt.MemList = append(opt.MemList, pkInfo)
		opt.PkEndTs = uint32(pk.EndTime.Unix())

		// 获取该场pk观众贡献榜
		topList, err := s.cache.GetPkAudienceRankList(gameConf.GameId, cid, pkId, 0, 3)
		if err != nil {
			log.Errorf("pushPKBattleInfo fail to GetPkAudienceRankList channelId:%+v, err:%v", cid, err)
		}

		for _, mem := range topList {
			uidList = append(uidList, mem.Uid)
		}

		userMap := s.getUserProfile(ctx, gameConf.GameId, cid, uidList)
		log.Debugf("get user topList:%v, userMap:%v", topList, userMap)

		for _, mem := range topList {
			if mem.Uid == 0 {
				continue
			}

			profile := userMap[mem.Uid]
			pkInfo.TopAnchorList = append(pkInfo.TopAnchorList, &maskedPkLogic.ChannelMaskedPKAnchor{
				Uid:      mem.Uid,
				Account:  profile.GetAccount(),
				NickName: profile.GetNickname(),
				Sex:      profile.GetSex(),
				Score:    mem.Score,
				UserProfile: &app.UserProfile{
					Uid:          mem.Uid,
					Account:      profile.GetAccount(),
					Nickname:     profile.GetNickname(),
					AccountAlias: profile.GetAccountAlias(),
					Sex:          profile.GetSex(),
					Privilege: &app.UserPrivilege{
						Account:  profile.GetPrivilege().GetAccount(),
						Nickname: profile.GetPrivilege().GetNickname(),
						Type:     profile.GetPrivilege().GetType(),
						Options:  profile.GetPrivilege().GetOptions(),
					},
				},
			})
		}
	}

	opt.ValidPk = validPk
	if !validPk {
		opt.ValidPkDesc = fmt.Sprintf("当前胜负无效\n%s", s.bc.GetValidPkDesc())
	} else if validPk && gameConf.DivideType == uint32(pb.DivideType_DIVIDE_TYPE_PERCENT) {
		opt.ValidPkDesc = "当前胜负有效"
	}


	opt.PeakPkOpt = &maskedPkLogic.PeakPkChangeOpt{
		AwardChannelId: peakAwardInfo.Cid,
		AwardPkValue:   peakAwardInfo.Score,
		DurationDesc:   durationDesc,
	}


	log.DebugfWithCtx(ctx, "pushPKBattleInfo %+v", opt)

	data, e := proto.Marshal(opt)
	if e != nil {
		log.Errorf("pushPKBattleInfo fail to Marshal channelIds:%+v, err:%v", channelIds, e)
	}

	for _, cid := range channelIds {
		_ = s.pushMsgToChannel(ctx, cid, uint32(channelPB.ChannelMsgType_LIVE_MASKED_PK_BATTLE_V2),
			"PK战况", data)
	}
	return nil
}

func (s *MaskedPKSvrServer) pushPKReviveInfo(ctx context.Context, gameConf *store2.LiveMaskedGameConfig, channelId uint32) error {
	reviveId, err := s.cache.GetReviveId(gameConf.GameId, channelId)
	if err != nil {
		log.Errorf("pushPKReviveInfo push pk revive get revive id failed, channel:%d, err:%v", channelId, err)
		return err
	}

	reviveInfo, err := s.cache.GetReviveInfoRedis(gameConf.GameId, channelId, reviveId)
	if err != nil {
		log.Errorf("pushPKReviveInfo push pk revive get revive info failed, channel:%d, err:%v", channelId, err)
		return err
	}

	opt := &maskedPkLogic.ChannelMaskedPKRevive{
		ChannelId:   channelId,
		Goal:        gameConf.ReviveTbean,
		Curr:        reviveInfo.Score,
		ContinueSec: s.bc.GetReviveSec(),
		EndTs:       uint32(reviveInfo.EndTime.Unix()),
		ServerNs:    time.Now().UnixNano(),
	}
	data, _ := proto.Marshal(opt)

	log.DebugfWithCtx(ctx, "pushPKReviveInfo %+v", opt)
	return s.pushMsgToChannel(ctx, channelId, uint32(channelPB.ChannelMsgType_LIVE_MASKED_PK_REVIVE_V2),
		"复活战况", data)
}

func (s *MaskedPKSvrServer) pushMsgToChannel(ctx context.Context, channelId, msgType uint32, content string, data []byte) error {
	msg := &channelPB.ChannelBroadcastMsg{
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  channelId,
		Type:         msgType,
		Content:      []byte(content),
		PbOptContent: data,
	}

	channelMsgBin, err := msg.Marshal()
	if err != nil {
		log.Errorf("pushMsgToChannel marshal err: %s, %+v", err.Error(), msg)
		return err
	}

	err = s.pushChannelBroMsgToChannels(ctx, []uint32{channelId}, channelMsgBin)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMsgToChannel fail to SendChannelBroadcastMsg. channelId:%d err:%v", channelId, err)
		return err
	}

	log.DebugfWithCtx(ctx, "pushMsgToChannel msgType:%v, channelId:%d, content:%s", msgType, channelId, content)
	return nil
}

func (s *MaskedPKSvrServer) pushMsgToChannels(ctx context.Context, channelIds []uint32, msgType uint32, content string, data []byte) error {
	msg := &channelPB.ChannelBroadcastMsg{
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  0,
		Type:         msgType,
		Content:      []byte(content),
		PbOptContent: data,
	}

	channelMsgBin, err := msg.Marshal()
	if err != nil {
		log.Errorf("pushMsgToChannels marshal err: %s, %+v", err.Error(), msg)
		return err
	}

	err = s.pushChannelBroMsgToChannels(ctx, channelIds, channelMsgBin)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMsgToChannels fail to SendChannelBroadcastMsg. channelIds:%v err:%v", channelIds, err)
		return err
	}

	log.DebugfWithCtx(ctx, "pushMsgToChannels msgType:%v, channelIds:%v, content:%s", msgType, channelIds, content)
	return nil
}

// 房间广播消息
func (s *MaskedPKSvrServer) pushChannelBroMsgToChannels(ctx context.Context, channelIds []uint32, channelMsgBin []byte) error {
	pushMessage := &gaPush.PushMessage{
		Cmd:     uint32(gaPush.PushMessage_CHANNEL_MSG_BRO),
		Content: channelMsgBin,
	}
	pushMessageBytes, e := pushMessage.Marshal()
	if e != nil {
		log.Errorf("pushChannelBroMsgToChannels Marshal channelIds:%v, err: %v", channelIds, e)
		return e
	}

	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              uint32(protocol.TT),
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageBytes,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 60,
		},
	}

	multicastMap := map[uint64]string{}
	for _, channelId := range channelIds {
		if channelId == 0 {
			continue
		}
		multicastMap[uint64(channelId)] = fmt.Sprintf("%d@channel", channelId)
	}

	err := s.PushCli.PushMulticasts(ctx, multicastMap, []uint32{}, notification)
	if err != nil {
		log.Errorf("pushChannelBroMsgToChannels PushMulticasts channelIds:%v, err: %s", channelIds, err.Error())
		return err
	}

	return nil
}
