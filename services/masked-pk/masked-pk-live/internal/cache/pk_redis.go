package cache

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/masked-pk/masked-pk-live/internal/store"
	"strconv"
	"time"
)

func (cache *Cache) AddMatch(gameId, channelId, chip, status, matchWhite, pkCount, provideChip uint32) (int, error) {
	log.Infof("AddMatch gameId:%d, channelId:%d, chip:%d, status:%d", gameId, channelId, chip, status)
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"add_match"}, gameId, channelId, chip, status, time.Now().Unix(), matchWhite, pkCount, provideChip).Int()
}

// ChangeInMatchChannel 根据等待时间调整匹配队列
func (cache *Cache) ChangeInMatchChannel(gameId, channelId, matchWhite, waitSec, pkCnt, provideChip uint32) (int, error) {
	if waitSec <= 30 {
		return 0, nil
	}
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"change_in_match_channel"}, gameId, channelId, matchWhite, waitSec, pkCnt, provideChip).Int()
}

// AddWhiteList 添加白名单
func (cache *Cache) AddWhiteList(channelId uint32) (int, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"add_whitelist"}, channelId, time.Now().Unix()).Int()
}

// DelWhiteList 删除白名单
func (cache *Cache) DelWhiteList(channelId uint32) (int, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"del_whitelist"}, channelId).Int()
}

// StartMatch  开始匹配 成功返回对手channelId 失败返回<=0
func (cache *Cache) StartMatch(gameId, channelId, pKSec, pkCount, waitSec, matchWhite, matchGuild, matchChip, matchFinal, usePrior, priorSec uint32) (uint32, error) {
	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"start_chip_match"}, gameId, channelId, pkCount, waitSec, time.Now().Unix(),
	matchWhite, matchGuild, matchChip, pKSec, matchFinal, usePrior, priorSec).Int()
	if ret < 0 {
		log.Errorf("StartChipMatch failed, gameId:%d, channelId:%d, ret:%d", gameId, channelId, ret)
		return uint32(0), err
	}
	log.Infof("StartChipMatch gameId:%d, channelId:%d, pkCount:%d, waitSec:%d, matchWhite:%d,matchGuild:%d,matchChip:%d, ret:%d", gameId, channelId, pkCount, waitSec, matchWhite, matchGuild, matchChip, ret)
	return uint32(ret), err
}

func (cache *Cache) CheckReadyMatchTimeout(gameId uint32) (interface{}, error) {
	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"check_ready_match_timeout"}, gameId, time.Now().Unix()).Result()
	return ret, err
}

// AddReadyMatch 准备匹配阶段
func (cache *Cache) AddReadyMatch(gameId, channelId, matchWhitelist uint32, readyTime int64) error {
	log.Infof("AddReadyMatch gameId:%d, channelId:%d, matchWhitelist:%d, readyTime:%d", gameId, channelId, matchWhitelist, readyTime)
	_, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"add_ready_match"}, gameId, channelId, matchWhitelist, readyTime).Result()
	return err
}

// AddPreReady 结束后  预准备匹配阶段
func (cache *Cache) AddPreReady(gameId, channelId uint32, readyTime int64) error {
	log.Infof("AddPreReady gameId:%d, channelId:%d, readyTime:%d", gameId, channelId, readyTime)
	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"add_pre_ready"}, gameId, channelId, readyTime).Result()
	fmt.Println("ret", ret, err)
	return err
}

func (cache *Cache) CheckPreReadyEndChannel(gameId uint32) (interface{}, error) {
	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_pre_ready_end_channel"}, gameId, time.Now().Unix()).Result()
	return ret, err
}

// 获取结束的房间列表
func (cache *Cache) GetPKEndChannels(gameId uint32) ([]uint32, error) {
	pkIdList := make([]uint32, 0)
	iter, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_pk_end_channels"}, gameId, time.Now().Unix()).Result()
	if err != nil {
		return pkIdList, err
	}

	if retList, ok := iter.([]interface{}); ok {
		for _, ret := range retList {
			pkIdList = append(pkIdList, uint32(ret.(int64)))
		}
	}

	return pkIdList, nil
}

// GetInPKChannels 正在PK中的房间列表
func (cache *Cache) GetInPKChannels(gameId uint32) (interface{}, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_in_pk_channels"}, gameId).Result()
}

// GetInMatchChannels 正在匹配中的房间列表
func (cache *Cache) GetInMatchChannels(gameId uint32) (interface{}, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_in_match_channels"}, gameId).Result()
}

// GetInMatchZetChannel
func (cache *Cache) GetInMatchZetChannel(gameId, waitSec uint32) (interface{}, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_in_match_zset_channel"}, gameId, waitSec).Result()
}

func (cache *Cache) GetInMatchChannel(gameId, channelId uint32) (int64, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_in_match_channel"}, gameId, channelId).Int64()
}

func (cache *Cache) GetPKStatus(gameId, channelId uint32) (uint32, error) {
	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_pk_status"}, gameId, channelId).Int()
	if err == redis.Nil {
		return uint32(ret), nil
	}
	return uint32(ret), err
}

// 房间pk状态key，生成规则与在lua脚本中的一致
func genChannelPkStatusKey(gameId, channelId uint32) string {
	return fmt.Sprintf("set:live:channel:pk:status:%d:%d", gameId, channelId)
}

func (cache *Cache) BatchGetPKStatus(gameId uint32, channelIdList []uint32) (map[uint32]uint32, error) {
	out := make(map[uint32]uint32)
	if len(channelIdList) == 0 {
		return out, nil
	}

	keyList := make([]string, 0, len(channelIdList))
	for _, cid := range channelIdList {
		keyList = append(keyList, genChannelPkStatusKey(gameId, cid))
	}

	ret, err := cache.redisClient.MGet(keyList...).Result()
	if err != nil {
		log.Errorf("BatchGetPKStatus fail to MGet. gameId:%d, channelIdList：%v, err:%v", gameId, channelIdList, err)
		return out, err
	}

	for k, v := range ret {
		str, ok := v.(string)
		if !ok {
			continue
		}
		status, err := strconv.ParseUint(str, 10, 32)
		if err != nil {
			log.Errorf("BatchGetPKStatus fail to ParseUint. gameId:%d, str：%s, err:%v", gameId, str, err)
			continue
		}

		out[channelIdList[k]] = uint32(status)
	}

	return out, nil
}

func (cache *Cache) SetPKStatus(gameId, channelId, status uint32) (interface{}, error) {
	log.Infof("SetPKStatus gameId:%d, channelId:%d, status:%d", gameId, channelId, status)
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"set_pk_status"}, gameId, channelId, status).Result()
}

func (cache *Cache) GetPKId(gameId, channelId uint32) (uint32, error) {
	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_pk_id"}, gameId, channelId).Int()
	if err == redis.Nil {
		return uint32(ret), nil
	}
	return uint32(ret), err
}

func (cache *Cache) GetReviveId(gameId, channelId uint32) (uint32, error) {
	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_revive_id"}, gameId, channelId).Int()
	return uint32(ret), err
}

func (cache *Cache) SetInRevive(gameId, channelId, endTime uint32) (int, error) {
	log.Infof("SetInRevive gameId:%d, channelId%d, endTime：%d", gameId, channelId, endTime)
	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"add_revive"}, gameId, channelId, endTime).Int()
	return ret, err
}

func (cache *Cache) GetNewReviveId(gameId uint32) (uint32, error) {
	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_new_unique_id"}, gameId).Int()
	return uint32(ret), err
}

// GetReviveEndChannel 获取复活结束的房间
func (cache *Cache) GetReviveEndChannel(gameId uint32) (interface{}, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_revive_end_channel"}, gameId, time.Now().Unix()).Result()
}

// GetInReviveChannel 获取复活结束的房间
func (cache *Cache) GetInReviveChannel(gameId uint32) (interface{}, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_in_revive_channel"}, gameId, time.Now().Unix()).Result()
}

// InitChannelGift 初始化房间流水值
func (cache *Cache) InitChannelGift(channelId, gift uint32) (interface{}, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"init_channel_gift"}, channelId, gift).Result()
}

func (cache *Cache) GetChannelGift(channelId uint32) (int, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_channel_gift"}, channelId).Int()
}

func (cache *Cache) GetChannelLastMatchBeginTime(gameId, channelId uint32) (int, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_last_begin_time"}, gameId, channelId).Int()
}

func (cache *Cache) CancelMatch(gameId, channelId, matchWhite uint32) (int, error) {
	log.Infof("CancelMatch gameId:%d, channelId:%d, matchWhite:%d", gameId, channelId, matchWhite)
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"cancel_match"}, gameId, channelId, time.Now().Unix(), matchWhite).Int()
}

func (cache *Cache) SetChannelChip(gameId, channelId, chipCount uint32) (int, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"set_channel_chip"}, gameId, channelId, chipCount).Int()
}

func (cache *Cache) GetPKChangeChannels(gameId uint32) (interface{}, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_pk_change_channel"}, gameId, time.Now().Unix()).Result()
}

func (cache *Cache) GetPKChannelsById(gameId, pkId uint32) (uint32, uint32, error) {
	pkChannels, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_pk_channels_by_id"}, gameId, pkId).Result()
	if err != nil {
		return 0, 0, err
	}

	rs, ok := pkChannels.([]interface{})
	if !ok || len(rs) != 2 {
		return 0, 0, errors.New("len(rs) != 2")
	}
	channelA, channelB := rs[0].(int64), rs[1].(int64)

	return uint32(channelA), uint32(channelB), nil
}

// 初始化筹码池
func (cache *Cache) InitChipPool(gameId, chips uint32) error {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"init_chip_pool"}, gameId, chips).Err()
}

// 抢筹码
func (cache *Cache) DecrGameChip(gameId, chips uint32) (int, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"decr_game_chips"}, gameId, chips).Int()
}

func (cache *Cache) RemoveInMatchChannel(gameId, channelId, matchWhite, waitSec uint32) (int, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"remove_in_match_channel"}, gameId, channelId, matchWhite, waitSec).Int()
}

func (cache *Cache) GetPKEndTime(gameId, pkId uint32) (uint32, error) {
	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_pk_end_time"}, gameId, pkId).Int()
	if err == redis.Nil {
		return uint32(ret), nil
	}
	return uint32(ret), err
}

func (cache *Cache) BeginPeakPk(gameId, pkId, channelId, dstChannelId, currentTime, pkSec uint32) (int, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"begin_peak_pk"}, gameId, channelId, dstChannelId, currentTime, pkSec, pkId).Int()
}

func (cache *Cache) EarlyEndPkByPkId(gameId, pkId, currentTime uint32) (int, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"early_end_pk"}, gameId, pkId, currentTime).Int()
}

// 斩杀激活条件值
func genQuickKillConditionKey(gameId, pkId uint32) string {
	return fmt.Sprintf("quick:kill:condition:%d:%d", gameId, pkId)
}

func (cache *Cache) SetQuickKillCondition(gameId, pkId, conditionVal uint32) error {
	key := genQuickKillConditionKey(gameId, pkId)
	return cache.redisClient.Set(key, conditionVal, time.Hour).Err()
}

func (cache *Cache) GetQuickKillCondition(gameId, pkId uint32) (uint32, error) {
	key := genQuickKillConditionKey(gameId, pkId)
	str, err := cache.redisClient.Get(key).Result()
	if err != nil && err != redis.Nil {
		return 0, err
	}

	val, _ := strconv.ParseInt(str, 10, 32)
	return uint32(val), nil
}

// 获取pk子阶段状态
func (cache *Cache) GetPkSubPhrase(gameId, pkId uint32) (uint32, error) {
	phrase, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_pk_sub_phrase"}, gameId, pkId).Int()
	if err != nil {
		return 0, err
	}

	return uint32(phrase), nil
}

// 获取斩杀到期pkId
func (cache *Cache) PopQuickKillEndPk(gameId, expireTs, limit uint32) ([]uint32, error) {
	pkIdList := make([]uint32, 0)
	iter, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_quick_kill_end_pk"}, gameId, expireTs, limit).Result()
	if err != nil {
		return pkIdList, err
	}

	if retList, ok := iter.([]interface{}); ok {
		for _, ret := range retList {
			pkIdList = append(pkIdList, uint32(ret.(int64)))
		}
	}

	return pkIdList, nil
}

// 激活斩杀阶段
func (cache *Cache) EnterQuickKill(gameId, pkId, triggerCid, endTs uint32) (uint32, bool, error) {
	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"enter_quick_kill_phrase"}, gameId, pkId, triggerCid, endTs).Int()
	if err != nil {
		return 0, false, err
	}

	if ret < 0 {
		return 0, false, nil
	}

	quickKillId := uint32(ret)
	return quickKillId, true, nil
}

// 退出斩杀阶段
func (cache *Cache) QuitQuickKill(gameId, pkId uint32) (bool, error) {
	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"quit_quick_kill_phrase"}, gameId, pkId).Int()
	return ret == 0, err
}

// 获取斩杀信息
func (cache *Cache) GetQuickKillInfo(gameId, pkId uint32) (quickKillId, triggerCid, endTs uint32, err error) {
	info, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_quick_kill_info"}, gameId, pkId).Result()
	if err != nil {
		return 0, 0, 0, err
	}

	if retList, ok := info.([]interface{}); ok {
		if len(retList) != 3 {
			return 0, 0, 0, errors.New("ret error")
		}

		quickKillId, triggerCid, endTs := retList[0].(int64), retList[1].(int64), retList[2].(int64)
		return uint32(quickKillId), uint32(triggerCid), uint32(endTs), nil
	}

	return 0, 0, 0, nil
}

const popOneMember = `
local channels = redis.call('ZRANGEBYSCORE', KEYS[1], '-INF', ARGV[1], 'LIMIT', 0, 1)
local ret = {}
for ix = 1, #channels do
	redis.call("ZREM", KEYS[1], channels[ix])
	table.insert(ret, channels[ix])
end
return ret`

func genQuickKillCheckQueueKey(gameId uint32) string {
	return fmt.Sprintf("live_quick_kill_check_queue_%d", gameId)
}

func (cache *Cache) AddFirstQuickKillCheckQueue(gameId, pkId, endTs uint32) error {
	key := genQuickKillCheckQueueKey(gameId)
	err := cache.redisClient.ZAddNX(key, redis.Z{Member: pkId, Score: float64(endTs)}).Err()
	if err != nil {
		log.Errorf("AddFirstQuickKillCheckQueue fail to Eval. gameId:%v, pkId：%v, endTs:%v, err:%v", gameId, pkId, endTs, err)
		return err
	}

	_ = cache.redisClient.Expire(key, 6*time.Hour)
	return nil
}

func (cache *Cache) PopFirstQuickKillCheckQueue(gameId, endTs uint32) (uint32, bool, error) {
	key := genQuickKillCheckQueueKey(gameId)
	res, err := cache.redisClient.Eval(popOneMember, []string{key}, endTs).Result()
	if err != nil {
		log.Errorf("PopFirstQuickKillCheckQueue fail to Eval. gameId:%v, endTs:%v, err:%v", gameId, endTs, err)
		return 0, false, err
	}

	if len(res.([]interface{})) == 0 {
		return 0, false, nil
	}

	if rs, ok := res.([]interface{}); ok && len(res.([]interface{})) > 0 {
		pkId, _ := strconv.Atoi(rs[0].(string))
		return uint32(pkId), true, nil
	}

	return 0, false, nil
}

func genBeforePeakSecondChannelKey(gameId, pkId uint32) string {
	return fmt.Sprintf("live_before_peak_channel_%d_%d", gameId, pkId)
}

func (cache *Cache) SetSecondChannelBeforePeak(gameId, pkId, channelId uint32) (bool, error) {
	key := genBeforePeakSecondChannelKey(gameId, pkId)
	return cache.redisClient.SetNX(key, channelId, 15*time.Minute).Result()
}

func (cache *Cache) GetSecondChannelBeforePeak(gameId, pkId uint32) (uint32, error) {
	key := genBeforePeakSecondChannelKey(gameId, pkId)
	strVal, err := cache.redisClient.Get(key).Result()
	if err == redis.Nil {
		return 0, nil
	}
	if err != nil {
		log.Errorf("GetSecondChannelBeforePeak fail. gameId:%d, pkId:%d, err:%v", gameId, pkId, err)
		return 0, err
	}

	val, _ := strconv.Atoi(strVal)
	return uint32(val), nil
}

func genBeforePeakConditionKey(gameId, pkId, channelId uint32) string {
	return fmt.Sprintf("live_before_peak_condition_%d_%d_%d", gameId, pkId, channelId)
}

func (cache *Cache) IncrBeforePeakConditionScore(gameId, pkId, channelId, score uint32) (uint32, error) {
	key := genBeforePeakConditionKey(gameId, pkId, channelId)
	val, err := cache.redisClient.IncrBy(key, int64(score)).Result()
	_ = cache.redisClient.Expire(key, 5*time.Minute)
	return uint32(val), err
}

func (cache *Cache) GetBeforePeakConditionScore(gameId, pkId, channelId uint32) (uint32, error) {
	key := genBeforePeakConditionKey(gameId, pkId, channelId)

	strVal, err := cache.redisClient.Get(key).Result()
	if err == redis.Nil {
		return 0, nil
	}
	if err != nil {
		log.Errorf("GetBeforePeakConditionScore fail. gameId:%d, pkId:%d, channelId:%d, err:%v", gameId, pkId, channelId, err)
		return 0, err
	}

	val, _ := strconv.Atoi(strVal)
	return uint32(val), nil
}

type PeakPkAwardInfo struct {
	Cid   uint32
	Score uint32
}

func genPeakAwardKey(gameId, pkId uint32) string {
	return fmt.Sprintf("live_peak_award_%d_%d", gameId, pkId)
}

func (cache *Cache) SetPeakAwardInfo(gameId, pkId uint32, info *PeakPkAwardInfo) error {
	key := genPeakAwardKey(gameId, pkId)
	b, _ := json.Marshal(info)

	return cache.redisClient.Set(key, string(b), 30*time.Minute).Err()
}

func (cache *Cache) GetPeakAwardInfo(gameId, pkId uint32) (*PeakPkAwardInfo, error) {
	info := &PeakPkAwardInfo{}
	key := genPeakAwardKey(gameId, pkId)

	str, err := cache.redisClient.Get(key).Result()
	if err == redis.Nil {
		return info, nil
	}
	if err != nil {
		log.Errorf("GetPeakAwardInfo fail. gameId:%d, pkId:%d, err:%v", gameId, pkId, err)
		return info, err
	}

	_ = json.Unmarshal([]byte(str), info)
	return info, nil
}

func (cache *Cache) UpdatePKScoreRedis(gameId, channelId, pkId, score uint32, endTs int64) (int, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"update_pk_score"}, gameId, channelId, pkId, score, endTs).Int()
}

func (cache *Cache) GetPKInfoRedis(gameId, channelId, pkId uint32) (*store.PKInfo, error) {
	out := &store.PKInfo{
		GameId:    gameId,
		ChannelId: channelId,
		PKId:      pkId,
	}

	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_pk_info"}, gameId, channelId, pkId).Result()
	if err != nil {
		return out, err
	}

	var endTime int64
	if rs, ok := ret.([]interface{}); ok {
		out.Score = uint32(rs[0].(int64))
		endTime = rs[1].(int64)
		out.EndTime = time.Unix(endTime, 0)
	}
	return out, err
}

func (cache *Cache) GetPKInfoByPkIdRedis(gameId, channelId, pkId uint32) ([]*store.PKInfo, error) {
	out := make([]*store.PKInfo, 0)
	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_pk_infos"}, gameId, channelId, pkId).Result()
	if err != nil {
		return out, err
	}

	if rs, ok := ret.([]interface{}); ok {
		info := &store.PKInfo{
			GameId:    gameId,
			ChannelId: channelId,
			PKId:      pkId,
		}
		info.Score = uint32(rs[0].(int64))
		info.EndTime = time.Unix(rs[1].(int64), 0)
		out = append(out, info)

		dstInfo := &store.PKInfo{
			GameId: gameId,
			PKId:   pkId,
		}

		dstInfo.ChannelId = uint32(rs[2].(int64))
		dstInfo.Score = uint32(rs[3].(int64))
		dstInfo.EndTime = info.EndTime
		out = append(out, dstInfo)
	}
	return out, nil
}

func (cache *Cache) UpdateReviveScoreRedis(gameId, channelId, reviveId, score uint32) (int, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"update_revive_score"}, gameId, channelId, reviveId, score).Int()
}

func (cache *Cache) GetReviveScoreRedis(gameId, channelId, reviveId uint32) (int, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_revive_score"}, gameId, channelId, reviveId).Int()
}

func (cache *Cache) SetReviveEnd(gameId, channelId uint32) (int, error) {
	return cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"set_revive_end"}, gameId, channelId, time.Now().Unix()).Int()
}

func (cache *Cache) GetReviveInfoRedis(gameId, channelId, reviveId uint32) (*store.ReviveInfo, error) {
	out := &store.ReviveInfo{
		GameId:    gameId,
		ChannelId: channelId,
		ReviveId:  reviveId,
	}

	ret, err := cache.redisClient.EvalSha(cache.luaScript.GetSha(), []string{"get_revive_info"}, gameId, channelId, reviveId).Result()
	if err != nil {
		return out, err
	}

	if rs, ok := ret.([]interface{}); ok {
		out.Score = uint32(rs[0].(int64))
		out.EndTime = time.Unix(rs[1].(int64), 0)
	}
	return out, err
}
