package manager

import (
	"context"
	"fmt"
	reconcile_present "golang.52tt.com/clients/reconcile-v2-svr/reconcile-present"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/store/redis"
	"golang.52tt.com/pkg/timer"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	"golang.52tt.com/protocol/services/userpresent"

	channelGA "golang.52tt.com/protocol/app/channel"

	"golang.52tt.com/services/hunt-monster-mission/internal/conf"
	"sync"

	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"

	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/services/notify"

	"golang.52tt.com/clients/appconfig"
	"golang.52tt.com/clients/darkserver"
	"golang.52tt.com/clients/entertainmentrecommendback"
	huntmonster "golang.52tt.com/clients/hunt-monster"
	push "golang.52tt.com/clients/push-notification/v2"
	pushPb "golang.52tt.com/protocol/app/push"
	syncPb "golang.52tt.com/protocol/app/sync"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	enterPb "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	huntMonsterPb "golang.52tt.com/protocol/services/huntmonster"
	pb "golang.52tt.com/protocol/services/huntmonstermission"
	push_notification "golang.52tt.com/protocol/services/push-notification/v2"
)

type UserStayTime struct {
	Uid uint32
	Ts  uint32
}

//go:generate mockgen -destination=./mock_gen.go -package=manager golang.52tt.com/services/hunt-monster-mission/internal/manager IStore,IPropsCache
type IPropsCache interface {
	AddUserDailyMissionValue(ctx context.Context, uid, value uint32, missionType pb.EnumMissionType) (uint32, error)
	AddUserMissionValue(ctx context.Context, uid, value uint32, missionType pb.EnumMissionType) (uint32, error)
	BatchGetGetUserMissionStatus(ctx context.Context, uids []uint32, mType pb.EnumMissionType) (map[uint32]uint32, error)
	GetLock(ctx context.Context, key string, expireTs time.Duration) bool
	GetStayTimeListByScore(ctx context.Context, minTs, maxTs, offset, count int64, index uint32, mType pb.EnumMissionType) ([]UserStayTime, error)
	GetUserDailyMissionValue(ctx context.Context, uid uint32) (map[pb.EnumMissionType]uint32, error)
	GetUserMissionStatus(ctx context.Context, uid uint32, mType pb.EnumMissionType) (uint32, error)
	GetUserMissionValue(ctx context.Context, uid uint32) (map[pb.EnumMissionType]uint32, error)
	ReleaseLock(ctx context.Context, key string) error
	RemoveUserStayTime(ctx context.Context, uid, index uint32, mType pb.EnumMissionType) error
	BatchRemoveUserStayTime(ctx context.Context, uid []uint32, index uint32, mType pb.EnumMissionType) error

	SetUserMissionStatus(ctx context.Context, uid uint32, mType pb.EnumMissionType) error
	UpdateUserStayTime(ctx context.Context, uid, updateTs, index uint32, mType pb.EnumMissionType) error
	UpdateUserStayTimeV2(ctx context.Context, uid, updateTs, index uint32, mType pb.EnumMissionType) error

	// 批量更新在房时间
	BatchUpdateUserStayTime(ctx context.Context, uidList []uint32, updateTs, index uint32, mType pb.EnumMissionType) error
	BatchUpdateUserStayTimeV2(ctx context.Context, uidList []uint32, updateTs, index uint32, mType pb.EnumMissionType) error

	CheckOrderIfExist(ctx context.Context, orderId string) bool
	Close() error
}
type IStore interface {
	Close() error
	CreateMysqlTable()
	RecordUserMissionAwardInfo(ctx context.Context, uid, missionType, propsCnt, ts uint32) error
	AddPresentOrder(ctx context.Context, event *kafkapresent.PresentEvent) (exist bool, err error)
	GetPresentOrderCountAndValueByTime(ctx context.Context, startTime, endTime int64) (uint32, uint32, error)
	GetOrderLogOrderIdsByTime(ctx context.Context, startTime, endTime int64) ([]string, error)
}
type PropsMgr struct {
	cacheCli       IPropsCache
	enterCli       entertainmentrecommendback.IClient
	appconfigCli   appconfig.IClient
	huntMonsterCli huntmonster.IClient
	pushCli        push.IClient
	store          IStore
	darkCli        darkserver.IClient
	reconcileCli   reconcile_present.IClient

	mapId2IsRecommend        map[uint32]bool // 房间是否在推荐库中
	HuntMonsterConfigIsValid bool            // 打龙活动是否生效
	huntMonsterJumpUrl       string          // 打龙活动任务也跳转链接
	apiCenterCli             apicenter.IClient
	stop                     chan interface{}
	sw                       sync.WaitGroup

	timerD *timer.Timer
}

func NewPropsMgr(ctx context.Context,
	cacheClient IPropsCache,
	store IStore,
	enterCli entertainmentrecommendback.IClient,
	appconfigCli appconfig.IClient,
	huntMonsterCli huntmonster.IClient,
	pushCli push.IClient,
	darkCli darkserver.IClient,
	reconcileCli reconcile_present.IClient,
	apiCenterCli apicenter.IClient,
) (*PropsMgr, error) {
	tmpMapId2IsRecommend := make(map[uint32]bool)
	mgr := &PropsMgr{
		cacheCli:          cacheClient,
		store:             store,
		enterCli:          enterCli,
		appconfigCli:      appconfigCli,
		huntMonsterCli:    huntMonsterCli,
		pushCli:           pushCli,
		darkCli:           darkCli,
		reconcileCli:      reconcileCli,
		mapId2IsRecommend: tmpMapId2IsRecommend,
		stop:              make(chan interface{}),
		apiCenterCli:      apiCenterCli,
	}
	return mgr, nil
}

func (p *PropsMgr) Close() {
	_ = p.cacheCli.Close()
	_ = p.store.Close()
	p.timerD.Stop()
	close(p.stop)
	p.sw.Wait()
}

func (p *PropsMgr) CheckIsWhiteUser(uid uint32) bool {
	for _, whiteUid := range WhiteUidList {
		if whiteUid == uid {
			return true
		}
	}
	return false
}

func (p *PropsMgr) GetMissionLimit() map[pb.EnumMissionType]uint32 {
	return mapMission2Limit
}

func (p *PropsMgr) Maintain(redisConf *config.RedisConfig) {

	// 重启pod的时候,重试读取
	for i := 1; i <= 3; i++ {
		isNoTry := p.TimerRefreshChannelRecInfo()
		if isNoTry {
			break
		}
		time.Sleep(2 * time.Second)
	}

	// 重启pod的时候,重试读取
	for i := 1; i <= 3; i++ {
		isNoTry := p.TimerRefreshHuntMonsterConfig()
		if isNoTry {
			break
		}
		time.Sleep(2 * time.Second)
	}

	// 定时查询经营房信息
	p.sw.Add(1)
	go func() {
		for {
			select {
			case <-time.After(10 * time.Minute):
				p.TimerRefreshChannelRecInfo()
			case <-p.stop:
				p.sw.Done()
				return
			}
		}
	}()

	// 定时查询龙的入口配置信息
	p.sw.Add(1)
	go func() {
		for {
			select {
			case <-time.After(time.Minute):
				p.TimerRefreshHuntMonsterConfig()
			case <-p.stop:
				p.sw.Done()
				return
			}
		}
	}()

	ctx := context.Background()
	client := redis.NewClientV2(redisConf)
	err := client.Ping(ctx).Err()
	if err != nil {
		log.Errorf("redis connect fail: %v", err)
		panic(err)
	}

	timerD, err := timer.NewTimerD(ctx, "hunt-monster-mission", timer.WithRedisCli(client))
	if err != nil {
		log.Errorf("setTimer fail to NewTimerD. err:%v", err)
		panic(err)
	}

	everyOneSec := "@every 1s"
	err = timerD.AddTask(everyOneSec, "TimerProcUserChannelStayMissionFive", timer.BuildFromLambda(func(ctx context.Context) {
		for i := 0; i < lockFiveIndex; i++ {
			go func(i int) {
				p.TimerProcUserChannelStayMissionFive(uint32(i))
			}(i)
			// 分散执行
			time.Sleep(time.Millisecond * 50)
		}
	}))
	if err != nil {
		log.Errorf("TimerProcUserChannelStayMissionFive failed to AddTask, err:%v")
		panic(err)
	}

	timerD.Start()
	p.timerD = timerD

}

func (p *PropsMgr) TimerRefreshChannelRecInfo() bool {
	ctx := context.Background()
	beginTime := time.Now()
	log.InfoWithCtx(ctx, "TimerRefreshChannelRecInfo start ts %v", beginTime)
	tmpMapId2IsRec := make(map[uint32]bool)
	channelTypes := []uint32{uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE), uint32(channel.ChannelType_RADIO_LIVE_CHANNEL_TYPE)}
	for _, channelType := range channelTypes {
		offset := uint32(0)
		pageSize := uint32(500)
		var fetchSize uint32
		for {
			//分页拉取pgc房间
			prepareCtx, prepareCancelFunc := context.WithTimeout(context.Background(), 3*time.Second)
			req := &enterPb.GetPrepareChannelListV2Req{
				ChannelTypeList: []uint32{channelType},
				IsEnableCache:   proto.Bool(true),
				Offset:          proto.Uint32(offset),
				Limit:           proto.Uint32(pageSize),
			}
			resp, err := p.enterCli.GetPrepareChannelListV2(prepareCtx, req)
			prepareCancelFunc()
			if nil != err {
				log.ErrorWithCtx(ctx, "TimerRefreshChannelRecInfo GetPrepareChannelListV2 failed, offset: %d, pageSize: %d, err: %+v", offset, pageSize, err)
				return false
			}

			fetchSize = uint32(len(resp.PrepareChannelList))
			if fetchSize <= 0 {
				break
			}

			nowTs := uint32(time.Now().Unix())
			for _, info := range resp.PrepareChannelList {
				tmpMapId2IsRec[info.GetChannelId()] = true
				if info.GetOldStartTime() <= nowTs {
					tmpMapId2IsRec[info.GetChannelId()] = true
				} else {
					tmpMapId2IsRec[info.GetChannelId()] = false
				}
			}

			offset += fetchSize

			log.InfoWithCtx(ctx, "TimerRefreshChannelRecInfo  GetPrepareChannelListV2 len(cids): %d, fetch len(map): %d, offset:%d",
				len(resp.PrepareChannelList), len(tmpMapId2IsRec), offset)
		}
	}

	p.mapId2IsRecommend = tmpMapId2IsRec

	log.InfoWithCtx(ctx, "TimerRefreshChannelRecInfo end ts %v len(map):%d", time.Since(beginTime), len(p.mapId2IsRecommend))
	return true
}

func (p *PropsMgr) CheckIsRecommendChannel(cid uint32) bool {
	return p.mapId2IsRecommend[cid]
}

func (p *PropsMgr) TimerRefreshHuntMonsterConfig() bool {
	ctx := context.Background()
	log.InfoWithCtx(ctx, "TimerRefreshHuntMonsterConfig start ts %v ", time.Now())

	huntMonsterConfigs, err := p.appconfigCli.GetHuntMonsterConfig(context.Background(), 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerRefreshHuntMonsterConfig Failed to GetHuntMonsterConfig %+v", err)
		return false
	}

	hasEffectConfig := false
	huntMonsterJumpUrl := ""
	now := time.Now()
	for _, config := range huntMonsterConfigs {
		if config.GetBeginTime() <= uint64(now.Unix()) && config.GetEndTime() >= uint64(now.Unix()) {
			hasEffectConfig = true
			huntMonsterJumpUrl = config.GetUrl()
			break
		}
	}

	p.HuntMonsterConfigIsValid = hasEffectConfig
	p.huntMonsterJumpUrl = huntMonsterJumpUrl

	log.InfoWithCtx(ctx, "TimerRefreshHuntMonsterConfig end valid %v url %s", p.HuntMonsterConfigIsValid, p.huntMonsterJumpUrl)
	return true
}

func (p *PropsMgr) CheckHuntMonsterIsValid() bool {
	return p.HuntMonsterConfigIsValid
}

func (p *PropsMgr) handleUpdateStayTime(ctx context.Context, infoList []*UserStayTime, now time.Time, index uint32) {

	todayUidList := make([]uint32, 0, len(infoList))
	yesterdayUidList := make([]uint32, 0, len(infoList))
	for _, info := range infoList {
		stayTime := time.Unix(int64(info.Ts), 0)
		// 更新开始进房时间
		if now.Day() == stayTime.Day() {
			todayUidList = append(todayUidList, info.Uid)
		} else { // 跨天
			yesterdayUidList = append(yesterdayUidList, info.Uid)
		}
	}

	// zadd xx 存在才更新
	if len(todayUidList) != 0 {
		err := p.cacheCli.BatchUpdateUserStayTimeV2(ctx, todayUidList, uint32(now.Unix()), index, pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerProcUserChannelStayMissionFive UpdateUserStayTimeV2 failed uidList:%v, err %s", todayUidList, err)
		}
	}

	// zadd
	if len(yesterdayUidList) != 0 {
		err := p.cacheCli.BatchUpdateUserStayTime(ctx, yesterdayUidList, uint32(now.Unix()), index, pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerProcUserChannelStayMissionFive UpdateUserStayTime failed uid:%v, err %s", yesterdayUidList, err)
		}
	}
	log.InfoWithCtx(ctx, "TimerProcUserChannelStayMissionFive handleUpdateStayTime finish, todayUidList len:%d, yesterdayUidList len:%d, nowTs:%s", len(todayUidList), len(yesterdayUidList), now)

}
func (p *PropsMgr) TimerProcUserChannelStayMissionFive(index uint32) {
	ctx := context.Background()
	lockName := mission_lock_name_five + fmt.Sprintf("_%d", index)
	isGetLock := p.cacheCli.GetLock(ctx, lockName, 60*time.Second)
	if !isGetLock {
		log.DebugWithCtx(ctx, "TimerProcUserChannelStayMissionFive no get lock, lockName:%s", lockName)
		return
	}
	defer func() {
		err := p.cacheCli.ReleaseLock(ctx, lockName)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerProcUserChannelStayMissionFive failed to ReleaseLock, lockName:%s, err:%v", lockName, err)
		}
	}()

	// 避免死循环
	for i := 0; i < 10; i++ {
		// 记录开始时间
		beginTs := time.Now()
		// 只拿出五分钟前的，确保都已经达到完成任务门槛
		fiveMinuteAgo := beginTs.Unix() - int64(fiveMinute)
		userStayInfoList, err := p.cacheCli.GetStayTimeListByScore(ctx, 0, fiveMinuteAgo, 0, int64(conf.GetFiveMinuteMissionStep()), index, pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerProcUserChannelStayMissionFive GetStayTimeListByScore failed err %v", err)
			return
		}
		if len(userStayInfoList) == 0 {
			log.DebugWithCtx(ctx, "TimerProcUserChannelStayMissionFive index:%d, no user need to handle", index)
			return
		}

		// 用户任务信息
		type userInfo struct {
			ts    uint32 // 下次计时起点
			value uint32 // 本次完成的任务值
		}
		// 记录用户任务信息，用于后续索引
		userMissionValueMap := make(map[uint32]*userInfo)
		// 需要处理的用户
		needToHandleUidList := make([]uint32, 0)
		// 异常用户 or 任务完成的用户
		removeStayUidList := make([]uint32, 0)

		for _, info := range userStayInfoList {
			keepTs := uint32(beginTs.Unix()) - info.Ts
			// 计算完成的五分钟任务数量
			value := keepTs / (fiveMinute)
			if value == 0 || value > mapMission2Limit[pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE] {
				log.ErrorWithCtx(ctx, "TimerProcUserChannelStayMissionFive value 计算异常, info:%+v, value:%d", info, value)
				// 加入待删除列表
				removeStayUidList = append(removeStayUidList, info.Uid)
				continue
			}
			userMissionValueMap[info.Uid] = &userInfo{value: value, ts: info.Ts}
			needToHandleUidList = append(needToHandleUidList, info.Uid)

		}

		if len(needToHandleUidList) == 0 {
			log.DebugWithCtx(ctx, "TimerProcUserChannelStayMissionFive no user finish mission now, index:%d", index)
			return
		}

		// 批量获取用户任务完成情况
		mapUid2FiveValue, err := p.cacheCli.BatchGetGetUserMissionStatus(ctx, needToHandleUidList, pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerProcUserChannelStayMissionFive failed to BatchGetGetUserMissionStatus, list:%v, err:v", needToHandleUidList, err)
			return
		}
		// 实际完成任务用户列表(达到五分钟，且没有超过今日任务完成次数限制)
		finishUidList := make([]uint32, 0, len(mapUid2FiveValue))
		// 实际完成任务人数,用于日志统计
		var totalCnt uint32 = 0
		// 过滤已完成任务用户
		for _, uid := range needToHandleUidList {
			status := mapUid2FiveValue[uid]
			if status == 0 {
				// 实际完成任务人数加1
				totalCnt += 1
				finishUidList = append(finishUidList, uid)
			} else {
				// 任务完成就直接从时间表删除 (处理异常分支，正常不会走到这)
				log.ErrorWithCtx(ctx, "TimerProcUserChannelStayMissionFive already finish, uid:%d, type:%d, status:%d", uid, pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE, status)
				removeStayUidList = append(removeStayUidList, uid)
			}
		}

		updateUserStayInfoList := make([]*UserStayTime, 0)
		for _, uid := range finishUidList {
			info := userMissionValueMap[uid]
			log.DebugWithCtx(ctx, "TimerProcUserChannelStayMissionFive uid:%d, info:%+v", uid, info)
			isFinish := p.AddUserDailyMissionValue(ctx, uid, info.value, pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE)
			if isFinish {
				// 任务完成就直接从时间表删除
				removeStayUidList = append(removeStayUidList, uid)
			} else {
				updateUserStayInfoList = append(updateUserStayInfoList, &UserStayTime{Uid: uid, Ts: info.ts})
			}
		}

		// 批量删除
		if len(removeStayUidList) != 0 {
			p.BatchRemoveUserStayTime(ctx, removeStayUidList, index, pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE)
		}
		// 批量更新
		if len(updateUserStayInfoList) != 0 {
			p.handleUpdateStayTime(ctx, updateUserStayInfoList, beginTs, index)
		}
		log.InfoWithCtx(ctx, "TimerProcUserChannelStayMissionFive end cnt %d size %d index %d 耗时:%s", totalCnt, len(userStayInfoList), index, time.Since(beginTs))

		// 处理完break
		if len(userStayInfoList) < int(conf.GetFiveMinuteMissionStep()) {
			break
		}
		// 暂停50ms
		time.Sleep(time.Millisecond * 50)
	}

}

func (p *PropsMgr) CheckUserIsInBlackList(uid uint32) bool {
	// 黑产检查
	var hitUid uint32
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	hitUid, err := p.darkCli.UserBehaviorCheck(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckUserIsInBlackList UserBehaviorCheck Failed uid %d err %v", uid, err)
	}
	cancel()

	if hitUid != 0 {
		log.InfoWithCtx(ctx, "CheckUserIsInBlackList uid %d hituid %d is in black list", uid, hitUid)
		return true
	}
	return false
}

func (p *PropsMgr) AddHuntMonsterProps(uid, cnt uint32, missionType pb.EnumMissionType, isFinish bool) {
	ctx := context.Background()
	// 黑产检查
	if p.CheckUserIsInBlackList(uid) {
		log.ErrorWithCtx(ctx, "AddHuntMonsterProps CheckUserIsInBlackList not pass, uid:%d", uid)
		return
	}

	_, err := p.huntMonsterCli.AddHuntMonsterItem(ctx, &huntMonsterPb.AddHuntMonsterItemReq{
		Uid:      uid,
		ItemType: 0,
		IncrCnt:  int64(cnt),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "AddHuntMonsterProps failed uid %d cnt %d type %v err %v", uid, cnt, missionType, err)
		return
	}

	if p.HuntMonsterConfigIsValid && p.huntMonsterJumpUrl != "" {
		popUpMsg := &pushPb.HuntMonsterPropsPopUpMsg{
			Uid:         uid,
			MissionName: mapMission2Name[missionType],
			PropsName:   props_name,
			PropsCnt:    cnt,
			JumpUrl:     p.huntMonsterJumpUrl,
			MissionType: uint32(missionType),
			IsFinish:    isFinish,
			UpdateMs:    time.Now().UnixNano() / 1e6,
		}
		pupUpMsgBin, _ := proto.Marshal(popUpMsg)
		// 推送打龙道具获得 弹窗
		p.pushUserGetPropsPopMsg(ctx, pupUpMsgBin, uid)

		// 推送助手消息
		content := "【打龙任务】你已完成[" + mapMission2Name[missionType] + "]任务，获得打野刀x" + fmt.Sprintf("%d", cnt) + "，可在打龙中使用"
		_ = p.SendTTMsg(ctx, uid, content)

	}

	sqlErr := p.store.RecordUserMissionAwardInfo(ctx, uid, uint32(missionType), cnt, uint32(time.Now().Unix()))
	if sqlErr != nil {
		log.ErrorWithCtx(ctx, "AddHuntMonsterProps RecordUserMissionAwardInfo %d %d %d err %v", uid, missionType, cnt, sqlErr)
		return
	}
	log.InfoWithCtx(ctx, "AddHuntMonsterProps end uid %d cnt %d type %d isFinish %v", uid, cnt, missionType, isFinish)
}

func (p *PropsMgr) AddUserDailyMissionValue(ctx context.Context, uid, value uint32, missionType pb.EnumMissionType) bool {
	log.DebugWithCtx(ctx, "AddUserDailyMissionValue begin uid %d value %d type %d", uid, value, missionType)

	isFinish := false
	newValue, err := p.cacheCli.AddUserDailyMissionValue(ctx, uid, value, missionType)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddUserDailyMissionValue failed uid %d value %d type %d err %v", uid, value, missionType, err)
		return isFinish
	}

	var cnt uint32 = 0
	switch missionType {
	case pb.EnumMissionType_E_STAY_CHANNEL_ONE_MINUTE, pb.EnumMissionType_E_ENTER_CHANNEL_HOUR_RANK, pb.EnumMissionType_E_SEND_PRESENT:
		if newValue == mapMission2Limit[missionType] {
			cnt = mapMission2Award[missionType]
		}
	case pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE, pb.EnumMissionType_E_SEND_ONE_THOUSAND_PRESENT:
		if newValue <= mapMission2Limit[missionType] {
			cnt = value * mapMission2Award[missionType]
		} else {
			oldValue := newValue - value
			if oldValue < mapMission2Limit[missionType] {
				cnt = (mapMission2Limit[missionType] - oldValue) * mapMission2Award[missionType]
			}
		}

	default:
		log.ErrorWithCtx(ctx, "AddUserDailyMissionValue no such daily mission type uid %d value %d type %d", uid, value, missionType)
	}

	if newValue >= mapMission2Limit[missionType] {
		isFinish = true
	}

	if cnt != 0 {
		p.AddHuntMonsterProps(uid, cnt, missionType, isFinish)
	}

	if missionType == pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE || missionType == pb.EnumMissionType_E_STAY_CHANNEL_ONE_MINUTE {
		if newValue >= mapMission2Limit[missionType] {
			if err = p.cacheCli.SetUserMissionStatus(ctx, uid, missionType); err != nil {
				log.ErrorWithCtx(ctx, "AddUserDailyMissionValue failed to SetUserMissionStatus, err:%v", err)
			}
		}
	}

	log.InfoWithCtx(ctx, "AddUserDailyMissionValue end uid %d value %d type %d newValue %d cnt %d isFinish %v", uid, value, missionType, newValue, cnt, isFinish)
	return isFinish
}

func (p *PropsMgr) AddUserMissionValue(ctx context.Context, uid, value uint32, missionType pb.EnumMissionType) {
	log.DebugWithCtx(ctx, "AddUserMissionValue begin uid %d value %d type %d", uid, value, missionType)

	newValue, err := p.cacheCli.AddUserMissionValue(ctx, uid, value, missionType)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddUserMissionValue failed uid %d value %d type %d err %v", uid, value, missionType, err)
		return
	}

	isFinish := false
	var cnt uint32 = 0
	switch missionType {
	case pb.EnumMissionType_E_FIRST_SEND_TBEAN_PRESENT:
		if newValue == mapMission2Limit[missionType] {
			cnt = mapMission2Award[missionType]
		}
	case pb.EnumMissionType_E_ADD_FANS_GROUP:
		if newValue <= mapMission2Limit[missionType] && newValue > 0 {
			cnt = mapMission2Award[missionType]
		}
	default:
		log.ErrorWithCtx(ctx, "AddUserMissionValue no such mission type uid %d value %d type %d", uid, value, missionType)
	}

	if newValue >= mapMission2Limit[missionType] {
		isFinish = true
	}

	if cnt != 0 {
		p.AddHuntMonsterProps(uid, cnt, missionType, isFinish)
	}

	log.InfoWithCtx(ctx, "AddUserMissionValue end uid %d value %d type %d cnt %d newvalue %d", uid, value, missionType, cnt, newValue)
}

// 获取在房5分钟任务index
func (p *PropsMgr) get5MinuteMissionIndex(uid uint32) uint32 {
	return uid % lockFiveIndex
}

func (p *PropsMgr) UpdateUserStayTime(ctx context.Context, uid uint32, updateTs uint32, mType pb.EnumMissionType) {
	index := p.get5MinuteMissionIndex(uid)
	err := p.cacheCli.UpdateUserStayTime(ctx, uid, updateTs, index, mType)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUserStayTime failed uid %d updateTs %d err %v", uid, updateTs, err)
		return
	}

	log.InfoWithCtx(ctx, "UpdateUserStayTime end uid %d updateTs %d", uid, updateTs)
}

func (p *PropsMgr) RemoveUserStayTime(ctx context.Context, uid uint32, mType pb.EnumMissionType) {
	index := p.get5MinuteMissionIndex(uid)
	err := p.cacheCli.RemoveUserStayTime(ctx, uid, index, mType)
	if err != nil {
		log.ErrorWithCtx(ctx, "RemoveUserStayTime failed uid %d", uid)
		return
	}
	log.InfoWithCtx(ctx, "RemoveUserStayTime end uid %d", uid)
}

func (p *PropsMgr) BatchRemoveUserStayTime(ctx context.Context, uidList []uint32, index uint32, mType pb.EnumMissionType) {
	err := p.cacheCli.BatchRemoveUserStayTime(ctx, uidList, index, mType)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchRemoveUserStayTime failed uidList %v, index:%d", uidList, index)
		return
	}
	log.InfoWithCtx(ctx, "BatchRemoveUserStayTime end uidList len:%d, index:%d", len(uidList), index)
}

func (p *PropsMgr) GetUserHuntMonsterMission(ctx context.Context, uid uint32) (*pb.GetUserHuntMonsterMissionResp, error) {
	out := &pb.GetUserHuntMonsterMissionResp{}
	log.DebugWithCtx(ctx, "GetUserHuntMonsterMission begin uid %d", uid)

	mapType2Value, err := p.cacheCli.GetUserMissionValue(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserHuntMonsterMission GetUserMissionValue failed uid %d err %v", uid, err)
		return out, err
	}

	mapType2DailyValue, err := p.cacheCli.GetUserDailyMissionValue(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserHuntMonsterMission GetUserDailyMissionValue failed uid %d err %v", uid, err)
		return out, err
	}

	out.StayChannelOneCnt = mapType2DailyValue[pb.EnumMissionType_E_STAY_CHANNEL_ONE_MINUTE]
	out.StayChannelFiveCnt = mapType2DailyValue[pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE]
	out.EnterChannelHourRankCnt = mapType2DailyValue[pb.EnumMissionType_E_ENTER_CHANNEL_HOUR_RANK]
	out.SendPresentCnt = mapType2DailyValue[pb.EnumMissionType_E_SEND_PRESENT]
	out.SendOneThousandPresentCnt = mapType2DailyValue[pb.EnumMissionType_E_SEND_ONE_THOUSAND_PRESENT]
	out.FirstSendTbeanPresentCnt = mapType2Value[pb.EnumMissionType_E_FIRST_SEND_TBEAN_PRESENT]
	out.AddFansGroupCnt = mapType2Value[pb.EnumMissionType_E_ADD_FANS_GROUP]

	log.InfoWithCtx(ctx, "GetUserHuntMonsterMission end uid %d out %v", uid, out)
	return out, nil
}

func (p *PropsMgr) GetUserHuntMissionInfo(ctx context.Context, uid, channelId uint32, missionType pb.EnumMissionType) (*pb.GetUserHuntMissionInfoResp, error) {
	out := &pb.GetUserHuntMissionInfoResp{}

	log.DebugWithCtx(ctx, "GetUserHuntMissionInfo begin uid %d id %d type %d", uid, channelId, missionType)

	out.PopMsg = "打龙-停留5分钟任务即将完成，可获得打野刀x2喔，是否现在退出"
	out.UpdateMs = time.Now().UnixNano() / 1e6
	out.IsFinish = true

	// 没开启活动，直接返回完成
	if !p.CheckHuntMonsterIsValid() {
		log.DebugWithCtx(ctx, "GetUserHuntMissionInfo hunt monster no start uid %d id %d type %d", uid, channelId, missionType)
		return out, nil
	}

	// 停留房间时长任务，房间必须是经营房才能完成，所以加个判断
	if missionType == pb.EnumMissionType_E_STAY_CHANNEL_ONE_MINUTE || missionType == pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE {
		if !p.CheckIsRecommendChannel(channelId) {
			log.DebugWithCtx(ctx, "GetUserHuntMissionInfo no rec channel uid %d id %d type %d", uid, channelId, missionType)
			return out, nil
		}
	}

	var err error
	mapType2Value := make(map[pb.EnumMissionType]uint32)
	switch missionType {
	case pb.EnumMissionType_E_STAY_CHANNEL_ONE_MINUTE, pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE, pb.EnumMissionType_E_ENTER_CHANNEL_HOUR_RANK,
		pb.EnumMissionType_E_SEND_PRESENT, pb.EnumMissionType_E_SEND_ONE_THOUSAND_PRESENT:
		mapType2Value, err = p.cacheCli.GetUserDailyMissionValue(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserHuntMissionInfo GetUserDailyMissionValue failed uid %d err %v", uid, err)
			return out, err
		}
		log.DebugWithCtx(ctx, "GetUserHuntMissionInfo GetUserDailyMissionValue uid %d value %d", uid, mapType2Value[missionType])
	case pb.EnumMissionType_E_FIRST_SEND_TBEAN_PRESENT, pb.EnumMissionType_E_ADD_FANS_GROUP:
		mapType2Value, err = p.cacheCli.GetUserMissionValue(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserHuntMissionInfo GetUserMissionValue failed uid %d err %v", uid, err)
			return out, err
		}
		log.DebugWithCtx(ctx, "GetUserHuntMissionInfo GetUserMissionValue uid %d value %d", uid, mapType2Value[missionType])
	default:
		log.ErrorWithCtx(ctx, "GetUserHuntMissionInfo default uid %d type %d", uid, missionType)
	}

	log.DebugWithCtx(ctx, "GetUserHuntMissionInfo uid %d value %d limit %d", uid, mapType2Value[missionType], mapMission2Limit[missionType])
	if mapType2Value[missionType] < mapMission2Limit[missionType] {
		out.IsFinish = false
	}

	log.DebugWithCtx(ctx, "GetUserHuntMissionInfo end uid %d out %v", uid, out)
	return out, nil
}

func (p *PropsMgr) GetUserHuntMonsterDailyMission(ctx context.Context, uid uint32) (map[pb.EnumMissionType]uint32, error) {
	log.DebugWithCtx(ctx, "GetUserHuntMonsterDailyMission begin uid %d", uid)

	mapType2DailyValue, err := p.cacheCli.GetUserDailyMissionValue(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserHuntMonsterDailyMission GetUserDailyMissionValue failed uid %d err %v", uid, err)
		return mapType2DailyValue, err
	}

	log.DebugWithCtx(ctx, "GetUserHuntMonsterDailyMission end uid %d value %v", uid, mapType2DailyValue)
	return mapType2DailyValue, nil
}

func (p *PropsMgr) TestTools(uid uint32) {
	ctx := context.Background()
	popUpMsg := &pushPb.HuntMonsterPropsPopUpMsg{
		Uid:         uid,
		MissionName: mapMission2Name[pb.EnumMissionType_E_ENTER_CHANNEL_HOUR_RANK],
		PropsName:   props_name,
		PropsCnt:    10,
		JumpUrl:     "http://app.52tt.com/internal/frontend-web-activity-play-boss-task-202012/index.html",
	}
	pupUpMsgBin, _ := proto.Marshal(popUpMsg)
	p.pushUserGetPropsPopMsg(ctx, pupUpMsgBin, uid)

	content := "【打龙任务】你已完成[xxxx]任务，获得打野刀x2，可在打龙中使用"
	err := p.SendTTMsg(ctx, uid, content)
	if err != nil {
		log.ErrorWithCtx(ctx, "TestTools failed to SendTTMsg, err:%v", err)
	}
}

func (p *PropsMgr) pushUserGetPropsPopMsg(ctx context.Context, msg []byte, uid uint32) {
	if uid == 0 {
		log.ErrorWithCtx(ctx, "PushUserGetPropsPopMsg uid 0")
		return
	}

	pushMessage := &pushPb.PushMessage{
		Cmd:     uint32(pushPb.PushMessage_HUNT_MONSTER_PROPS_POP_UP_MSG_v2),
		Content: msg,
	}
	pushMessageBytes, _ := proto.Marshal(pushMessage)

	notification := &push_notification.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: push.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &push_notification.ProxyNotification{
			Type:    uint32(push_notification.ProxyNotification_PUSH),
			Payload: pushMessageBytes,
		},
	}

	var uidList []uint32
	uidList = append(uidList, uid)

	if err := p.pushCli.PushToUsers(ctx, uidList, notification); err != nil {
		log.ErrorWithCtx(ctx, "PushUserGetPropsPopMsg failed, uid:%d err %v", uidList[0], err)
	} else {
		log.InfoWithCtx(ctx, "PushUserGetPropsPopMsg success uid:%d err", uidList[0])
	}

}

func (s *PropsMgr) SendTTMsg(ctx context.Context, uid uint32, content string) error {
	msg := &apiPB.ImMsg{
		ImType: &apiPB.ImType{
			SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
			ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
			ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT),
		},
		FromUid: uint32(10000),
		ToIdList: []uint32{
			uid,
		},
		ImContent: &apiPB.ImContent{
			TextNormal: &apiPB.ImTextNormal{
				Content: content,
			},
		},
		Platform:    apiPB.Platform_UNSPECIFIED,
		AppPlatform: "all",
	}

	err := s.apiCenterCli.SendImMsg(context.Background(), uid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendTTMsg fail. uid:%d err: %s", uid, err.Error())
		return err
	}

	notify.NotifySync(uid, syncPb.SyncReq_IM_MSG)
	log.InfoWithCtx(ctx, "SendTTMsg done. uid:%d", uid)
	return nil
}

func (p *PropsMgr) HandleHuntMission(ctx context.Context, presentEvent *kafkapresent.PresentEvent) (error, bool) {
	ts := time.Now()
	// 红钻礼物不记录
	if presentEvent.GetPriceType() != uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN) {
		log.DebugWithCtx(ctx, "handlerPresentEvent no handle suid %d tuid %d id %d gift type %d",
			presentEvent.GetUid(), presentEvent.GetTargetUid(), presentEvent.GetItemId(), presentEvent.GetPriceType())
		return nil, false
	}

	exist, err := p.store.AddPresentOrder(ctx, presentEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerPresentEvent AddPresentOrder failed %+v", presentEvent)
		return err, true
	}
	if exist {
		log.DebugWithCtx(ctx, "handlerPresentEvent order exist %+v", presentEvent)
		return nil, false

	}

	if presentEvent.GetChannelType() == uint32(channelGA.ChannelType_CPL_SUPER_CHANNEL_TYPE) {
		return nil, false
	}

	log.DebugWithCtx(ctx, "handlerPresentEvent begin %+v", presentEvent)

	if !p.CheckHuntMonsterIsValid() {
		log.DebugWithCtx(ctx, "handlerPresentEvent hunt monster no begin uid %d id %d cid %d", presentEvent.GetUid(), presentEvent.GetItemId(), presentEvent.GetChannelId())
		return nil, false
	}
	// 处理送任意数量T豆礼物
	p.AddUserDailyMissionValue(ctx, presentEvent.GetUid(), 1, pb.EnumMissionType_E_SEND_PRESENT)

	// 处理活动期间首送T豆礼物 2021春节去掉任务
	//s.propsMgr.AddUserMissionValue(presentEvent.GetUid(), 1, pb.EnumMissionType_E_FIRST_SEND_TBEAN_PRESENT)

	totalPrice := presentEvent.GetPrice() * presentEvent.GetItemCount()
	if totalPrice >= 1000 {
		// 处理单笔送礼超过1000T豆礼物
		p.AddUserDailyMissionValue(ctx, presentEvent.GetUid(), totalPrice/1000, pb.EnumMissionType_E_SEND_ONE_THOUSAND_PRESENT)
	}

	log.InfoWithCtx(ctx, "handlerPresentEvent end uid:%d, event:%+v, 耗时:%s", presentEvent.GetUid(), presentEvent, time.Since(ts))
	return nil, false
}
