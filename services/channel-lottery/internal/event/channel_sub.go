package event

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/config"
	channelPB "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkachannalevent"
	"golang.52tt.com/services/channel-lottery/internal/conf/business"
	channel2 "golang.52tt.com/services/channel-lottery/internal/model/channel"
	"golang.52tt.com/services/channel-lottery/internal/model/record"
	"golang.52tt.com/services/channel-lottery/internal/model/setting"
	comKafka "golang.52tt.com/services/common/kafka"
)

const topicChannelEvent = "simple_channel_ev"

type ChannelEventSub struct {
	//*event.KafkaSub
	KafkaSub subscriber.Subscriber
	settingManager *setting.SettingManager
	channelManager *channel2.ChannelManager
	recordManager  *record.RecordManager
}

func NewChannelEventSub(kfkCfg *config.KafkaConfig,
	settingManager *setting.SettingManager, channelMgr *channel2.ChannelManager, recordManager *record.RecordManager) (*ChannelEventSub, error) {

	//conf := sarama.NewConfig()
	//conf.ClientID = clientId
	//conf.Consumer.Offsets.Initial = sarama.OffsetNewest
	//conf.Consumer.Return.Errors = true
	//
	//kafkaSub, err := event.NewKafkaSub(topicChannelEvent, brokers, groupId, topics, conf)
	//if err != nil {
	//	log.Errorf("Failed to create kafka-subscriber %+v", err)
	//	return nil, err
	//}
	//
	//kafkaSub.SetIsMetrics()

	sub := &ChannelEventSub{
		channelManager: channelMgr,
		settingManager: settingManager,
		recordManager:  recordManager,
	}

	var err error
	sub.KafkaSub, err = comKafka.NewEventLinkSubscriber(kfkCfg, subscriber.ProcessorContextFunc(sub.handlerEvent))
	if err != nil {
		log.Errorf("NewChLiveStatusKafkaSub Failed to create kafka-subscriber %+v", err)
		return nil, err
	}

	//sub.SetMessageProcessor(sub.handlerEvent)
	return sub, nil
}

func (s *ChannelEventSub) Close() {
	s.KafkaSub.Stop()
}

func (s *ChannelEventSub) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	log.Debugf("handlerEvent topic:%s", msg.Topic)

	switch msg.Topic {
	case topicChannelEvent:
		return s.handlerChannelEvent(msg)
	default:
		return nil, false
	}
}

func (s *ChannelEventSub) handlerChannelEvent(msg *subscriber.ConsumerMessage) (error, bool) {
	channelEvent := &kafkachannalevent.ChSimpleEvent{}
	err := proto.Unmarshal(msg.Value, channelEvent)
	if err != nil {
		log.Debugf("ChannelEventSub handlerChannelEvent Unmarshal err:%v", err)
		return nil, false
	}

	// 不是抽奖房，不进行处理
	if channelEvent.GetChannelType() != uint32(channelPB.ChannelType_RADIO_LIVE_CHANNEL_TYPE) &&
		channelEvent.GetChannelType() != uint32(channelPB.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) &&
		channelEvent.GetChannelType() != uint32(channelPB.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		return nil, false
	}

	switch kafkachannalevent.ESIMPLE_EVENT_TYPE(channelEvent.GetEventType()) {
	case kafkachannalevent.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_ENTER:
		return s.handlerChannelEnterEvent(channelEvent)

	case kafkachannalevent.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_LEAVE, kafkachannalevent.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_EXPIRE_QUIT:
		return s.handlerChannelLeaveEvent(channelEvent)

	default:
		return nil, false
	}
}

func (s *ChannelEventSub) handlerChannelEnterEvent(channelEvent *kafkachannalevent.ChSimpleEvent) (error, bool) {
	info, exist, err := s.channelManager.GetChannelInfoOnCache(channelEvent.GetChId())
	if err != nil {
		return err, false
	}

	if !exist {
		return nil, false
	}

	lotteryInfo, exist, err := s.settingManager.GetChannelLottery(info.ChannelId, false)
	if err != nil {
		log.Errorf("ChannelEventSub handlerChannelEnterEvent  fail to GetLottery. channelId:%d, %+v, err:%v", info.ChannelId, channelEvent, err)
		return err, false
	}
	// 不在抽奖中，不需要处理
	if !exist || lotteryInfo.Condition == nil {
		return nil, false
	}

	channelId := channelEvent.ChId
	err = s.recordManager.IncrLotteryChannelEnterCnt(channelId, 1)
	if err != nil {
		log.Errorf("ChannelEventSub handlerChannelEnterEvent fail to IncrLotteryChannelEnterCnt. channelId:%d, err:%v", channelId, err)
		//return err, false
	}

	opt := kafkachannalevent.ChSimpleEnterOpt{}
	err = proto.Unmarshal(channelEvent.GetOptPbInfo(), &opt)
	if err != nil {
		log.Errorf("ChannelEventSub handlerChannelEnterEvent Unmarshal fail. %+v, err:%v", channelEvent, err)
		return err, false
	}

	if _, ok := business.GetConfig().EnterSourceRatioMap[fmt.Sprint(opt.GetSource())]; ok {
		// 记录特殊进房来源，在抽奖结束后清空
		_, _ = s.recordManager.AddUserEnterSpecialSource(channelId, channelEvent.GetUid(), opt.GetSource())
		log.Debugf("ChannelEventSub handlerChannelEnterEvent AddUserEnterSpecialSource. cid:%d, uid:%d, source:%d", channelId, channelEvent.GetUid(), opt.GetSource())
	}

	return nil, false
}

func (s *ChannelEventSub) handlerChannelLeaveEvent(channelEvent *kafkachannalevent.ChSimpleEvent) (error, bool) {
	err := s.recordManager.DelUserEnterSpecialSource(channelEvent.GetChId(), channelEvent.GetUid())
	if err != nil {
		log.Errorf("ChannelEventSub handlerChannelLeaveEvent DelUserEnterSpecialSource fail. %+v, err:%v", channelEvent, err)
		return err, false
	}

	log.Debugf("ChannelEventSub handlerChannelLeaveEvent %+v", channelEvent)
	return nil, false
}
