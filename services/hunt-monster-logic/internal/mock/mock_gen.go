// Code generated by MockGen. DO NOT EDIT.
// Source: ./interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIhuntMonsterTimer is a mock of IhuntMonsterTimer interface.
type MockIhuntMonsterTimer struct {
	ctrl     *gomock.Controller
	recorder *MockIhuntMonsterTimerMockRecorder
}

// MockIhuntMonsterTimerMockRecorder is the mock recorder for MockIhuntMonsterTimer.
type MockIhuntMonsterTimerMockRecorder struct {
	mock *MockIhuntMonsterTimer
}

// NewMockIhuntMonsterTimer creates a new mock instance.
func NewMockIhuntMonsterTimer(ctrl *gomock.Controller) *MockIhuntMonsterTimer {
	mock := &MockIhuntMonsterTimer{ctrl: ctrl}
	mock.recorder = &MockIhuntMonsterTimerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIhuntMonsterTimer) EXPECT() *MockIhuntMonsterTimerMockRecorder {
	return m.recorder
}

// GetHuntMonsterChannel mocks base method.
func (m *MockIhuntMonsterTimer) GetHuntMonsterChannel() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHuntMonsterChannel")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetHuntMonsterChannel indicates an expected call of GetHuntMonsterChannel.
func (mr *MockIhuntMonsterTimerMockRecorder) GetHuntMonsterChannel() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHuntMonsterChannel", reflect.TypeOf((*MockIhuntMonsterTimer)(nil).GetHuntMonsterChannel))
}

// RefreshHuntMonsterChannel mocks base method.
func (m *MockIhuntMonsterTimer) RefreshHuntMonsterChannel() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RefreshHuntMonsterChannel")
}

// RefreshHuntMonsterChannel indicates an expected call of RefreshHuntMonsterChannel.
func (mr *MockIhuntMonsterTimerMockRecorder) RefreshHuntMonsterChannel() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshHuntMonsterChannel", reflect.TypeOf((*MockIhuntMonsterTimer)(nil).RefreshHuntMonsterChannel))
}

// Run mocks base method.
func (m *MockIhuntMonsterTimer) Run() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Run")
}

// Run indicates an expected call of Run.
func (mr *MockIhuntMonsterTimerMockRecorder) Run() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Run", reflect.TypeOf((*MockIhuntMonsterTimer)(nil).Run))
}

// Stop mocks base method.
func (m *MockIhuntMonsterTimer) Stop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Stop")
}

// Stop indicates an expected call of Stop.
func (mr *MockIhuntMonsterTimerMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockIhuntMonsterTimer)(nil).Stop))
}
