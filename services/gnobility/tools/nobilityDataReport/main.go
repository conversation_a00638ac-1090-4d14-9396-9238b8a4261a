package main

import (
	"context"
	"crypto/tls"
	"fmt"
	beego "github.com/astaxie/beego/config"
	"github.com/go-gomail/gomail"
	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jmoiron/sqlx"
	"github.com/tealeg/xlsx"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/protocol"
	"os"

	//"google.golang.org/grpc"

	"strconv"
	"time"
	//"golang.52tt.com/clients/nobility"
)

type NobilityCurInfo struct {
	Uid           uint32 `db:"uid"`
	Level         uint32 `db:"level"`
	IsFirstReach  uint32 `db:"is_first_reach"`
	NobilityValue uint64 `db:"nobility_value"`
	CycleTs       uint32 `db:"cycle_ts"`
	KeepValue     uint64 `db:"keep_value"`
	WaitCostValue uint64 `db:"wait_cost_value"`
	MaxLv         uint32 `db:"max_lv"`
	RemainDays    uint32 `db:"remain_days"`
	UpdateTs      uint32 `db:"update_ts"`
}

type NobilityTotalInfo struct {
	NobilityLevel    uint32 `db:"nobility_level"`
	NobilityCnt      uint32 `db:"nobility_cnt"`
	ConsumeTBeanCnt  uint64 `db:"consume_tbean_cnt"`
	RechargeTBeanCnt uint64 `db:"recharge_tbean_cnt"`
	UpdateTs         uint32 `db:"update_ts"`
}

type NobilityDetailInfo struct {
	Uid                   uint32 `db:"uid"`
	Level                 uint32 `db:"level"`
	NobilityValue         uint64 `db:"nobility_value"`
	WaitCostValue         uint64 `db:"wait_cost_value"`
	CycleTs               uint32 `db:"cycle_ts"`
	KeepValue             uint64 `db:"keep_value"`
	RemainDays            uint32 `db:"remain_days"`
	PresentConsumeTBean   uint64 `db:"present_consume_tbean"`
	MagicBallConsumeTBean uint64 `db:"magicball_consume_tbean"`
	UpdateTs              uint32 `db:"update_ts"`
}

type NobilityConfig struct {
	Level     uint32
	LvName    string
	KeepValue uint64
	MixValue  uint64
	MaxValue  uint64
	FLevel    float32
}

type NobilityMonthRecord struct {
	Uid                uint32 `db:"uid"`
	NobilityValue      uint64 `db:"nobility_value"`
	KeepNobilityValue  uint32 `db:"keep_nobility_value"`
	WaitCostValue      uint32 `db:"wait_cost_value"`
	TotalWaitCostValue uint32 `db:"total_wait_cost_value"`
	CycleBeginTs       uint32 `db:"cycle_begin_ts"`
	NobilityLevel      uint32 `db:"nobility_level"`
	UpdateTs           uint32 `db:"update_ts"`
	OrderId            string `db:"order_id"`
	Source             string `db:"source"`
}

type SendPackageRecord struct {
	Uid  uint32 `db:"uid"`
	BgId uint32 `db:"bg_id"`
	Num  uint32 `db:"num"`
	Ts   uint32 `db:"outside_time"`
}

var NobilityConfList = []*NobilityConfig{
	{10, "少爵", 3000, 10000, 50000, 0.5},
	{1, "男爵", 15000, 50000, 200000, 1},
	{2, "子爵", 60000, 200000, 500000, 2},
	{3, "伯爵", 150000, 500000, 1000000, 3},
	{4, "侯爵", 300000, 1000000, 2000000, 4},
	{5, "公爵", 600000, 2000000, 5000000, 5},
	{6, "郡王", 1500000, 5000000, 10000000, 6},
	{7, "亲王", 3000000, 10000000, 20000000, 7},
	{8, "国王", 6000000, 20000000, 50000000, 8},
	{9, "神王", 2000000000, 50000000, 0, 9},
}

var mapLv2NobilityConf = map[uint32]NobilityConfig{
	10: {10, "少爵", 3000, 10000, 50000, 0.5},
	1:  {1, "男爵", 15000, 50000, 200000, 1},
	2:  {2, "子爵", 60000, 200000, 500000, 2},
	3:  {3, "伯爵", 150000, 500000, 1000000, 3},
	4:  {4, "侯爵", 300000, 1000000, 2000000, 4},
	5:  {5, "公爵", 600000, 2000000, 5000000, 5},
	6:  {6, "郡王", 1500000, 5000000, 10000000, 6},
	7:  {7, "亲王", 3000000, 10000000, 20000000, 7},
	8:  {8, "国王", 6000000, 20000000, 50000000, 8},
	9:  {9, "神王", 2000000000, 50000000, 0, 9},
}

func GetNobilityConfByLv(level uint32) NobilityConfig {
	tmpConf := &NobilityConfig{}
	for _, conf := range NobilityConfList {
		if conf.Level == level {
			tmpConf = conf
			break
		}
	}

	return *tmpConf
}

func CheckIsLess(levelA, levelB uint32) bool {
	confA := GetNobilityConfByLv(levelA)
	confB := GetNobilityConfByLv(levelB)

	return confA.FLevel < confB.FLevel
}

func CheckIsGreater(levelA, levelB uint32) bool {
	confA := GetNobilityConfByLv(levelA)
	confB := GetNobilityConfByLv(levelB)

	return confA.FLevel > confB.FLevel
}

var mapId2Ch = map[uint32]string{
	0: "否",
	1: "是",
}

var mapLv2Name = map[uint32]string{
	0:  "无等级",
	1:  "男爵",
	2:  "子爵",
	3:  "伯爵",
	4:  "侯爵",
	5:  "公爵",
	6:  "郡王",
	7:  "亲王",
	8:  "国王",
	9:  "神王",
	10: "少爵",
}

const (
	SEND_MAIL_TYPE_ONE    uint32 = 1
	SEND_MAIL_TYPE_SECOND uint32 = 2
	SEND_MAIL_TYPE_THIRD  uint32 = 3
	SEND_MAIL_TYPE_FORTH  uint32 = 4
)

func GetNobilityConf(nobilityValue uint64) NobilityConfig {
	tmpConf := &NobilityConfig{}
	for _, conf := range NobilityConfList {
		if nobilityValue >= conf.MixValue && (nobilityValue < conf.MaxValue || conf.MaxValue == 0) {
			tmpConf = conf
			break
		}
	}

	return *tmpConf
}

func InitSqlx(mysqlName string) *sqlx.DB {
	cfg, _ := beego.NewConfig("json", "/home/<USER>/lja/nobility/dataReport/report.json")

	mysqlConfig := new(config.MysqlConfig)
	mysqlConfig.Read(cfg, mysqlName)
	mysqlAddr := mysqlConfig.ConnectionString()
	fmt.Println(mysqlAddr)

	mysqlDB, err := sqlx.Connect("mysql", mysqlAddr)
	if err != nil {
		panic(err)
	}
	return mysqlDB
}

func SendMail(filePath string, sendType uint32) error {
	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")
	switch sendType {
	case SEND_MAIL_TYPE_ONE:
		m.SetHeader("To", "<EMAIL>")
		m.SetHeader("Subject", "贵族数据统计表")
	case SEND_MAIL_TYPE_SECOND:
		m.SetHeader("To", "<EMAIL>", "<EMAIL>", "<EMAIL>")
		m.SetHeader("Subject", "贵族数据统计表")
	case SEND_MAIL_TYPE_THIRD:
		m.SetHeader("To", "<EMAIL>", "<EMAIL>")
		m.SetHeader("Subject", "贵族自动升级每日数据统计表")
	case SEND_MAIL_TYPE_FORTH:
		m.SetHeader("To", "<EMAIL>", "<EMAIL>", "<EMAIL>")
		m.SetHeader("Subject", "贵族自动升级每月数据统计表")
	default:
		return protocol.NewServerError(-2, "send type error")
	}

	//m.SetHeader("To", "<EMAIL>")
	// m.SetAddressHeader("Cc", "<EMAIL>", "Dan") //抄送
	//m.SetHeader("Subject", "贵族数据统计表")
	m.SetBody("text/html", "数据表在附件")
	m.Attach(filePath) //附件

	d := gomail.NewDialer("mail.52tt.com", 465, "<EMAIL>", "V7.D.sTy@%bDB50t#n.r$A4h+-FnXA")

	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func GetNobilityCurInfos(file *xlsx.File) error {
	ctx := context.Background()
	db := InitSqlx("mysql")
	tb := "nobility_cur_info"

	sheet, err := file.AddSheet("降级监控表")
	if err != nil {
		return err
	}

	fmt.Println("GetNobilityCurInfos start")
	startTm := time.Now()

	row := sheet.AddRow()
	slice := []string{"uid", "贵族等级", "是否近期首次达成", "贵族值", "剩余有效期/天", "剩余保级值", "待消费金额/T豆", "历史最高等级"}
	row.WriteSlice(&slice, -1)

	var offset, count uint32 = 0, 1000
	for {
		query := fmt.Sprintf(`SELECT uid,level,is_first_reach,nobility_value,keep_value,wait_cost_value,max_lv,remain_days FROM %s LIMIT ?,?`, tb)
		infos := make([]*NobilityCurInfo, 0, count)
		err := db.SelectContext(ctx, &infos, query, offset, count)
		if err != nil {
			fmt.Println("GetNobilityCurInfos GetNobilityCurInfos sql err : ", err)
			return err
		}

		fmt.Println("GetNobilityCurInfos size:", len(infos), " offset:", offset, " count:", count)

		for _, info := range infos {
			row := sheet.AddRow()
			cell := row.AddCell()
			cell.Value = strconv.Itoa(int(info.Uid))
			cell = row.AddCell()
			cell.Value = mapLv2NobilityConf[info.Level].LvName
			cell = row.AddCell()
			cell.Value = mapId2Ch[info.IsFirstReach]
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.NobilityValue))
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.RemainDays))
			cell = row.AddCell()
			if info.KeepValue >= mapLv2NobilityConf[info.Level].KeepValue {
				cell.Value = "已保级"
			} else {
				cell.Value = strconv.Itoa(int(mapLv2NobilityConf[info.Level].KeepValue - info.KeepValue))
			}
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.WaitCostValue))
			cell = row.AddCell()
			cell.Value = mapLv2NobilityConf[info.MaxLv].LvName
		}

		if len(infos) < int(count) {
			break
		}

		offset = offset + count
	}

	fmt.Println("GetNobilityCurInfos end : ", time.Since(startTm))
	return nil
}

func GetNobilityDetailInfos(file *xlsx.File) error {
	ctx := context.Background()
	db := InitSqlx("mysql")

	sheet, err := file.AddSheet("明细查询表")
	if err != nil {
		return err
	}

	fmt.Println("GetNobilityDetailInfos start")
	startTm := time.Now()
	beginTime := time.Date(startTm.Year(), startTm.Month(), startTm.Day()-1, 0, 0, 0, 0, time.Local)
	beginTs := beginTime.Unix()
	endTs := beginTs + 86400
	tb := fmt.Sprintf("nobility_detail_info_%04d%02d", beginTime.Year(), int(beginTime.Month()))

	row := sheet.AddRow()
	slice := []string{"uid", "贵族等级", "贵族值", "礼物架消费/T豆", "魔力球消费/T豆", "待消费金额/T豆", "剩余有效期/天", "剩余保级值"}
	row.WriteSlice(&slice, -1)

	var offset, count uint32 = 0, 1000
	for {
		query := fmt.Sprintf(`SELECT uid,level,nobility_value,present_consume_tbean, magicball_consume_tbean,wait_cost_value,remain_days,
                                      keep_value FROM %s WHERE update_ts >= FROM_UNIXTIME(?) AND update_ts < FROM_UNIXTIME(?) LIMIT ?,?`, tb)
		infos := make([]*NobilityDetailInfo, 0, count)
		err := db.SelectContext(ctx, &infos, query, beginTs, endTs, offset, count)
		if err != nil {
			fmt.Println("GetNobilityDetailInfos sql failed err:", err)
			return err
		}

		fmt.Println("GetNobilityDetailInfos size:", len(infos), " offset:", offset, " count:", count)

		for _, info := range infos {
			row := sheet.AddRow()
			cell := row.AddCell()
			cell.Value = strconv.Itoa(int(info.Uid))
			cell = row.AddCell()
			cell.Value = mapLv2NobilityConf[info.Level].LvName
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.NobilityValue))
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.PresentConsumeTBean))
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.MagicBallConsumeTBean))
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.WaitCostValue))
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.RemainDays))
			cell = row.AddCell()
			if info.KeepValue >= mapLv2NobilityConf[info.Level].KeepValue {
				cell.Value = "已保级"
			} else {
				cell.Value = strconv.Itoa(int(mapLv2NobilityConf[info.Level].KeepValue - info.KeepValue))
			}
		}

		if len(infos) < int(count) {
			break
		}

		offset = offset + count
	}

	fmt.Println("GetNobilityDetailInfos end : ", time.Since(startTm))
	return nil
}

func GetNobilityTotalInfos(file *xlsx.File) error {
	ctx := context.Background()
	db := InitSqlx("mysql")

	sheet, err := file.AddSheet("整体数据表")
	if err != nil {
		return err
	}

	fmt.Println("GetNobilityTotalInfos start")
	startTm := time.Now()
	beginTime := time.Date(startTm.Year(), startTm.Month()-1, 1, 0, 0, 0, 0, time.Local)
	beginTs := beginTime.Unix()
	endTs := beginTs + 86400
	tb := "nobility_total_info"

	row := sheet.AddRow()
	slice := []string{"月份", "贵族等级", "用户数", "礼物架送礼&g购买魔力球/T豆", "充值金额", "人均消费/T豆"}
	row.WriteSlice(&slice, -1)

	var offset, count uint32 = 0, 50
	for {

		query := fmt.Sprintf(`SELECT uid FROM %s WHERE update_ts >= FROM_UNIXTIME(?) AND update_ts < FROM_UNIXTIME(?) LIMIT ?,?`, tb)
		infos := make([]*NobilityTotalInfo, 0, count)
		err := db.SelectContext(ctx, &infos, query, beginTs, endTs, offset, count)
		if err != nil {
			fmt.Println("GetNobilityDetailInfos sql failed err:", err)
			return err
		}

		fmt.Println("GetNobilityTotalInfos size:", len(infos), " offset:", offset, " count:", count)

		for _, info := range infos {
			row := sheet.AddRow()
			cell := row.AddCell()
			cell.Value = fmt.Sprintf("%04d.%02d", beginTime.Year(), int(beginTime.Month()))
			cell = row.AddCell()
			cell.Value = mapLv2NobilityConf[info.NobilityLevel].LvName
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.NobilityCnt))
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.ConsumeTBeanCnt))
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.RechargeTBeanCnt))
			cell = row.AddCell()
			if info.NobilityCnt == 0 {
				cell.Value = strconv.Itoa(0)
			} else {
				cell.Value = strconv.Itoa(int(info.ConsumeTBeanCnt / uint64(info.NobilityCnt)))
			}

		}

		if len(infos) < int(count) {
			break
		}

		offset = offset + count
	}

	fmt.Println("GetNobilityTotalInfos end : ", time.Since(startTm))
	return nil
}

func GetAutoUpNobilityData(file *xlsx.File) bool {
	ctx := context.Background()

	db := InitSqlx("mysqlNobility")
	bpDb := InitSqlx("mysqlBackpack")

	sheet, err := file.AddSheet("贵族自动升级数据表.")
	if err != nil {
		return false
	}

	startTm := time.Now()
	beginTime := time.Date(startTm.Year(), startTm.Month(), startTm.Day()-1, 0, 0, 0, 0, time.Local)
	beginTs := beginTime.Unix()
	endTs := beginTs + 86400

	row := sheet.AddRow()
	slice := []string{"uid", "升级前等级", "升级后等级", "待消费", "升级时间", "奖励包裹id", "奖励包裹个数", "奖励包裹时间", "是否首次升级", "备注信息"}
	row.WriteSlice(&slice, -1)

	for i := 0; i < 100; i++ {
		nobilityRecordList := make([]NobilityMonthRecord, 0)
		querySql := `select uid, nobility_value, keep_nobility_value, wait_cost_value, total_wait_cost_value, cycle_begin_ts, nobility_level,
                         UNIX_TIMESTAMP(update_time) update_ts from nobility_month_recored_` + fmt.Sprintf("%04d%02d_%02d", beginTime.Year(), beginTime.Month(), i) +
			` where source = ? and update_time >= FROM_UNIXTIME(?) and update_time <= FROM_UNIXTIME(?)`
		err = db.SelectContext(ctx, &nobilityRecordList, querySql, "AutoLevelUpNobility", beginTs, endTs)
		if err != nil {
			fmt.Println("GetAutoUpNobilityData err:", err, "sql:", querySql)
			return false
		}

		for _, record := range nobilityRecordList {
			recordList := make([]NobilityMonthRecord, 0)
			querySql := `select uid, nobility_value, keep_nobility_value, wait_cost_value, total_wait_cost_value, cycle_begin_ts, nobility_level,
                         UNIX_TIMESTAMP(update_time) update_ts from nobility_month_recored_` + fmt.Sprintf("%04d%02d_%02d", beginTime.Year(), beginTime.Month(), i) +
				` where uid = ? and update_time < FROM_UNIXTIME(?) order by update_time desc`
			err = db.SelectContext(ctx, &recordList, querySql, record.Uid, record.UpdateTs)
			if err != nil {
				fmt.Println("GetAutoUpNobilityData err1:", err, "sql1:", querySql)
				return false
			}

			updateTime := time.Unix(int64(record.UpdateTs), 0)
			backpackTb := fmt.Sprintf("user_package_gain_month_v2_%04d%02d", updateTime.Year(), updateTime.Month())
			backpackRecordList := make([]SendPackageRecord, 0)
			bpSql := fmt.Sprintf("select uid, bg_id, num, unix_timestamp(outside_time) as outside_time from %s where uid = ? and source = 11 and "+
				"outside_time >= FROM_UNIXTIME(?) and outside_time <= FROM_UNIXTIME(?)", backpackTb)
			err = bpDb.SelectContext(ctx, &backpackRecordList, bpSql, record.Uid, record.UpdateTs-2, record.UpdateTs+10)
			if err != nil {
				fmt.Println("GetAutoUpNobilityData GetAutoUpNobilityData uid:", record.Uid, "err2:", err, "sql2:", bpSql)
				return false
			}

			var beforeMaxLv uint32 = 0
			{
				var MaxNobilityLevel uint32 = 0
				querySql := `select COALESCE(max(nobility_level),0) max_nobility_level from nobility_level_record_` + fmt.Sprintf("%02d", i) +
					` where uid = ? and cycle_begin_ts < FROM_UNIXTIME(?) and (order_id = ? or order_id = ?)`
				err = db.GetContext(ctx, &MaxNobilityLevel, querySql, record.Uid, record.UpdateTs, "AutoLevelUpNobility", "AddRecharge")
				if err != nil {
					fmt.Println("GetAutoUpNobilityData err2:", err, "sql2:", querySql)
					return false
				}

				fmt.Printf("uid:%d maxLevel:%d", record.Uid, MaxNobilityLevel)
				beforeMaxLv = MaxNobilityLevel

				/*
					                for j := 4; j <= int(beginTime.Month()); j++ {
										recordList := make([]NobilityMonthRecord,0)
										querySql := `select COALESCE(max(nobility_level),0) nobility_level from nobility_month_recored_`+ fmt.Sprintf("%04d%02d_%02d", beginTime.Year(), j, i) +
											` where uid = ? and update_time < FROM_UNIXTIME(?) and source = ?`
										err = db.SelectContext(ctx, &recordList, querySql,  record.Uid, record.UpdateTs, "AutoLevelUpNobility")
										if err != nil {
											fmt.Println("GetAutoUpNobilityData err2:", err, "sql2:", querySql)
											continue
										}

										if len(recordList) >= 1 {
											if recordList[0].NobilityLevel > beforeMaxLv {
												beforeMaxLv = recordList[0].NobilityLevel
											}
										}
									}
				*/
			}

			awardIdStr := ""
			awardNumStr := ""
			awardTsStr := ""

			if len(backpackRecordList) > 1 {
				awardIdStr = "取数异常"
				awardNumStr = "取数异常"
				awardTsStr = "取数异常"
			} else if len(backpackRecordList) == 1 {
				awardIdStr = strconv.Itoa(int(backpackRecordList[0].BgId))
				awardNumStr = strconv.Itoa(int(backpackRecordList[0].Num))
				awardTsStr = time.Unix(int64(backpackRecordList[0].Ts), 0).Format("2006-01-02 15:04:05")
			} else {
				fmt.Println("GetAutoUpNobilityData no award uid:", record.Uid, "size:", len(backpackRecordList))
				awardIdStr = "无"
				awardNumStr = "无"
				awardTsStr = "无"
			}

			var preLv uint32 = 0
			if len(recordList) > 0 {
				preLv = recordList[0].NobilityLevel
				preLvInfo := GetNobilityConf(recordList[0].NobilityValue)
				if CheckIsGreater(preLvInfo.Level, preLv) {
					preLv = preLvInfo.Level
				}
			}

			var upLv uint32 = record.NobilityLevel
			upLvInfo := GetNobilityConf(record.NobilityValue)
			if CheckIsGreater(upLvInfo.Level, upLv) {
				upLv = upLvInfo.Level
			}

			// 是否是首次升级
			isFirstUp := "是"
			if CheckIsGreater(beforeMaxLv, upLv) || beforeMaxLv == upLv {
				isFirstUp = "否"
			}

			// 备注信息
			remarks := ""
			if awardIdStr == "取数异常" {
				remarks = "包裹取数异常，需要核对"
			} else if awardIdStr == "无" {
				remarks = "包裹发放失败，运营同事在运营后台进行补发"
				if CheckIsGreater(beforeMaxLv, upLv) || beforeMaxLv == upLv {
					remarks = "非首次升级，不需要发放包裹奖励"
				}
			}

			row := sheet.AddRow()
			cell := row.AddCell()
			cell.Value = strconv.Itoa(int(record.Uid))
			cell = row.AddCell()
			cell.Value = mapLv2Name[preLv]
			cell = row.AddCell()
			cell.Value = mapLv2Name[upLv]
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(record.WaitCostValue))
			cell = row.AddCell()
			cell.Value = time.Unix(int64(record.UpdateTs), 0).Format("2006-01-02 15:04:05")
			cell = row.AddCell()
			cell.Value = awardIdStr
			cell = row.AddCell()
			cell.Value = awardNumStr
			cell = row.AddCell()
			cell.Value = awardTsStr
			cell = row.AddCell()
			cell.Value = isFirstUp
			cell = row.AddCell()
			cell.Value = remarks

			fmt.Println("uid : ", record.Uid, "prelv : ", preLv, "upLv : ", upLv, "updateTs:", record.UpdateTs, "beforeMaxLv:", beforeMaxLv)
		}
	}

	return true
}

func GetAutoUpNobilityMonthData(file *xlsx.File) bool {
	ctx := context.Background()

	startTm := time.Now()
	if startTm.Day() != 1 {
		fmt.Println("GetAutoUpNobilityMonthData no need reported auto up data year:", startTm.Year(), " month:", startTm.Month(), " day:", startTm.Day())
		return false
	}

	db := InitSqlx("mysqlNobility")
	bpDb := InitSqlx("mysqlBackpack")

	sheet, err := file.AddSheet("贵族自动升级月统计数据表.")
	if err != nil {
		return false
	}

	beginTime := time.Date(startTm.Year(), startTm.Month()-1, 1, 0, 0, 0, 0, time.Local)

	row := sheet.AddRow()
	slice := []string{"uid", "升级前等级", "升级后等级", "待消费", "升级时间", "奖励包裹id", "奖励包裹个数", "奖励包裹时间", "是否首次升级", "备注"}
	row.WriteSlice(&slice, -1)

	for i := 0; i < 100; i++ {
		nobilityRecordList := make([]NobilityMonthRecord, 0)
		querySql := `select uid, nobility_value, keep_nobility_value, wait_cost_value, total_wait_cost_value, cycle_begin_ts, nobility_level,
                         UNIX_TIMESTAMP(update_time) update_ts from nobility_month_recored_` + fmt.Sprintf("%04d%02d_%02d", beginTime.Year(), beginTime.Month(), i) +
			` where source = ?`
		err = db.SelectContext(ctx, &nobilityRecordList, querySql, "AutoLevelUpNobility")
		if err != nil {
			fmt.Println("GetAutoUpNobilityMonthData err:", err, "sql:", querySql)
			return false
		}

		for _, record := range nobilityRecordList {
			recordList := make([]NobilityMonthRecord, 0)
			querySql := `select uid, nobility_value, keep_nobility_value, wait_cost_value, total_wait_cost_value, cycle_begin_ts, nobility_level,
                         UNIX_TIMESTAMP(update_time) update_ts from nobility_month_recored_` + fmt.Sprintf("%04d%02d_%02d", beginTime.Year(), beginTime.Month(), i) +
				` where uid = ? and update_time < FROM_UNIXTIME(?) order by update_time desc`
			err = db.SelectContext(ctx, &recordList, querySql, record.Uid, record.UpdateTs)
			if err != nil {
				fmt.Println("GetAutoUpNobilityMonthData err1:", err, "sql1:", querySql)
				return false
			}

			updateTime := time.Unix(int64(record.UpdateTs), 0)
			backpackTb := fmt.Sprintf("user_package_gain_month_v2_%04d%02d", updateTime.Year(), updateTime.Month())
			backpackRecordList := make([]SendPackageRecord, 0)
			bpSql := fmt.Sprintf("select uid, bg_id, num, unix_timestamp(outside_time) as outside_time from %s where uid = ? and source = 11 and "+
				"outside_time >= FROM_UNIXTIME(?) and outside_time <= FROM_UNIXTIME(?)", backpackTb)
			err = bpDb.SelectContext(ctx, &backpackRecordList, bpSql, record.Uid, record.UpdateTs-2, record.UpdateTs+10)
			if err != nil {
				fmt.Println("GetAutoUpNobilityMonthData uid:", record.Uid, "err2:", err, "sql2:", querySql)
				return false
			}

			var beforeMaxLv uint32 = 0
			{
				var MaxNobilityLevel uint32 = 0
				querySql := `select COALESCE(max(nobility_level),0) max_nobility_level from nobility_level_record_` + fmt.Sprintf("%02d", i) +
					` where uid = ? and cycle_begin_ts < FROM_UNIXTIME(?) and (order_id = ? or order_id = ?)`
				err = db.GetContext(ctx, &MaxNobilityLevel, querySql, record.Uid, record.UpdateTs, "AutoLevelUpNobility", "AddRecharge")
				if err != nil {
					fmt.Println("GetAutoUpNobilityData err2:", err, "sql2:", querySql)
					return false
				}

				fmt.Printf("uid:%d maxLevel:%d", record.Uid, MaxNobilityLevel)
				beforeMaxLv = MaxNobilityLevel

				/*
					for j := 4; j <= int(beginTime.Month()); j++ {
						recordList := make([]NobilityMonthRecord,0)
						querySql := `select  COALESCE(max(nobility_level),0) nobility_level from nobility_month_recored_`+ fmt.Sprintf("%04d%02d_%02d", beginTime.Year(), j, i) +
							` where uid = ? and update_time < FROM_UNIXTIME(?) and source = ?`
						err = db.SelectContext(ctx, &recordList, querySql,  record.Uid, record.UpdateTs, "AutoLevelUpNobility")
						if err != nil {
							fmt.Println("GetAutoUpNobilityMonthData err2:", err, "sql2:", querySql)
							continue
						}

						if len(recordList) >= 1 {
							if recordList[0].NobilityLevel > beforeMaxLv {
								beforeMaxLv = recordList[0].NobilityLevel
							}
						}
					}*/
			}

			awardIdStr := ""
			awardNumStr := ""
			awardTsStr := ""
			if len(backpackRecordList) > 1 {
				awardIdStr = "取数异常"
				awardNumStr = "取数异常"
				awardTsStr = "取数异常"
			} else if len(backpackRecordList) == 1 {
				awardIdStr = strconv.Itoa(int(backpackRecordList[0].BgId))
				awardNumStr = strconv.Itoa(int(backpackRecordList[0].Num))
				awardTsStr = time.Unix(int64(backpackRecordList[0].Ts), 0).Format("2006-01-02 15:04:05")
			} else {
				fmt.Println("noaward uid:", record.Uid, "size:", len(backpackRecordList))
				awardIdStr = "无"
				awardNumStr = "无"
				awardTsStr = "无"
			}

			var preLv uint32 = 0
			if len(recordList) > 0 {
				preLv = recordList[0].NobilityLevel
				preLvInfo := GetNobilityConf(recordList[0].NobilityValue)
				if CheckIsGreater(preLvInfo.Level, preLv) {
					preLv = preLvInfo.Level
				}
			}

			var upLv uint32 = record.NobilityLevel
			upLvInfo := GetNobilityConf(record.NobilityValue)
			if CheckIsGreater(upLvInfo.Level, upLv) {
				upLv = upLvInfo.Level
			}

			// 是否是首次升级
			isFirstUp := "是"
			if CheckIsGreater(beforeMaxLv, upLv) || beforeMaxLv == upLv {
				isFirstUp = "否"
			}

			// 备注信息
			remarks := ""
			if awardIdStr == "取数异常" {
				remarks = "包裹取数异常，需要核对"
			} else if awardIdStr == "无" {
				remarks = "包裹发放失败，运营同事在运营后台进行补发"
				if CheckIsGreater(beforeMaxLv, upLv) || beforeMaxLv == upLv {
					remarks = "非首次升级，不需要发放包裹奖励"
				}
			}

			row := sheet.AddRow()
			cell := row.AddCell()
			cell.Value = strconv.Itoa(int(record.Uid))
			cell = row.AddCell()
			cell.Value = mapLv2Name[preLv]
			cell = row.AddCell()
			cell.Value = mapLv2Name[upLv]
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(record.WaitCostValue))
			cell = row.AddCell()
			cell.Value = time.Unix(int64(record.UpdateTs), 0).Format("2006-01-02 15:04:05")
			cell = row.AddCell()
			cell.Value = awardIdStr
			cell = row.AddCell()
			cell.Value = awardNumStr
			cell = row.AddCell()
			cell.Value = awardTsStr
			cell = row.AddCell()
			cell.Value = isFirstUp
			cell = row.AddCell()
			cell.Value = remarks

			fmt.Println("uid : ", record.Uid, "prelv : ", preLv, "upLv : ", upLv, "updateTs:", record.UpdateTs, "beforeMaxLv:", beforeMaxLv)
		}
	}

	return true
}

func main() {

	startTm := time.Now()
	beginTime := time.Date(startTm.Year(), startTm.Month(), startTm.Day()-1, 0, 0, 0, 0, time.Local)
	beginTime2 := time.Date(startTm.Year(), startTm.Month()-1, 1, 0, 0, 0, 0, time.Local)

	// 降级监控表
	file := xlsx.NewFile()
	err := GetNobilityCurInfos(file)
	if err != nil {
		return
	}
	path := fmt.Sprintf("降级监控表_%04d%02d%02d.xlsx", startTm.Year(), startTm.Month(), startTm.Day())
	file.Save(path)
	for i := 1; i < 3; i++ {
		err := SendMail(path, SEND_MAIL_TYPE_SECOND)
		if err == nil {
			break
		}
	}
	os.Remove(path)

	// 明细表
	file1 := xlsx.NewFile()
	err1 := GetNobilityDetailInfos(file1)
	if err1 != nil {
		return
	}
	path1 := fmt.Sprintf("明细查询表_%04d%02d%02d.xlsx", beginTime.Year(), beginTime.Month(), beginTime.Day())
	file1.Save(path1)
	for i := 1; i < 3; i++ {
		err := SendMail(path1, SEND_MAIL_TYPE_SECOND)
		if err == nil {
			break
		}
	}
	os.Remove(path1)

	//贵族自动升级数据表
	file3 := xlsx.NewFile()
	isNeedSend := GetAutoUpNobilityData(file3)
	if isNeedSend {
		path3 := fmt.Sprintf("贵族自动升级每日数据表_%04d%02d%02d.xlsx", beginTime.Year(), beginTime.Month(), beginTime.Day())
		file3.Save(path3)
		for i := 1; i < 3; i++ {
			err := SendMail(path3, SEND_MAIL_TYPE_THIRD)
			if err == nil {
				break
			}
		}
		os.Remove(path3)
	}

	//贵族自动升级月统计数据表
	file4 := xlsx.NewFile()
	isNeedSend = GetAutoUpNobilityMonthData(file4)
	if isNeedSend {
		path4 := fmt.Sprintf("贵族自动升级每月统计数据表_%04d%02d.xlsx", beginTime2.Year(), beginTime2.Month())
		file4.Save(path4)
		for i := 1; i < 3; i++ {
			err := SendMail(path4, SEND_MAIL_TYPE_FORTH)
			if err == nil {
				break
			}
			fmt.Println("GetAutoUpNobilityMonthData SendMail err : ", err)
		}
		os.Remove(path4)
	}

	/*
		// 整体数据表
		file2 := xlsx.NewFile()
		err2 := GetNobilityDetailInfos(file1)
		if err2 != nil {
			return
		}
		path2 := "整体数据表.xlsx"
		file2.Save(path2)
		for i := 1 ; i < 3; i++ {
			err := SendMail(path2)
			if err == nil {
				break
			}
		}
		os.Remove(path2)
	*/
}
