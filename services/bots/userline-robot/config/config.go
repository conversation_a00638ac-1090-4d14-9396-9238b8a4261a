package config

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/runtime/v2/filters/pkg"
	"io/ioutil"
	"os/user"
	"path"
	"runtime"
)

const (
	Debug      = "debug"
	Testing    = "testing"
	Staging    = "staging"
	Production = "production"
)

var Environment = Testing

type ServiceConfig struct {
	FeiShuConf        *FeiShuConfig `json:"feishu"`
	RefreshTabInfoFre uint32        `json:"refresh_tab_info_fre"`
	Environment       string        `json:"environment"`
}

var UserLineRobotDynamicConfig *UserLineRobotLoader

func (sc *ServiceConfig) Parse(configFile string) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return err
	}

	err = json.Unmarshal(data, &sc)
	if err != nil {
		return err
	}

	if UserLineRobotDynamicConfig, err = NewUserLineRobotLoader(ttConfPath("user_line_robot.json")); err != nil {
		log.ErrorWithCtx(context.Background(), "NewUserLineRobotLoader err %v")
		return err
	}
	Environment = sc.Environment
	return
}

func ttConfPath(name string) string {
	var ttConfDir string
	if runtime.GOOS == "windows" {
		if u, err := user.Current(); err == nil {
			ttConfDir = path.Join(u.HomeDir, "/data/oss/conf-center/tt/")
		}
	} else {
		ttConfDir = "/data/oss/conf-center/tt/"
	}
	return path.Join(ttConfDir, name)
}

type FeiShuConfig struct {
	AppId             string `json:"app_id"`
	AppSecret         string `json:"app_secret"`
	EncryptKey        string `json:"encrypt_key"`
	VerificationToken string `json:"verification_token"`
	RobotOpenId       string `json:"robot_open_id"`
	Port              string `json:"port"`
}

// UserLineRobotDynamicConfig mock使用
func SetUserLineRobotDynamicConfig() {
	UserLineRobotDynamicConfig = &UserLineRobotLoader{}
	UserLineRobotDynamicConfig.configLoader = &pkg.ConfigLoader{}
}
