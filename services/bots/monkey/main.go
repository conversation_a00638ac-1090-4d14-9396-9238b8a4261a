package main

import (
    "context"
    "time"

    "github.com/spf13/viper"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/services/bots/monkey/internal/authz"
    "golang.52tt.com/services/bots/monkey/internal/conf"
    "golang.52tt.com/services/bots/monkey/internal/server"
    "golang.52tt.com/services/bots/monkey/internal/store"
)

const (
    appName = "revenue-robot"
)

func main() {
    viper.SetConfigName(appName)
    viper.AddConfigPath(".")
    viper.AddConfigPath("$HOME/." + appName)
    viper.AddConfigPath("/config")
    viper.AddConfigPath("$HOME/etc")
    viper.SetEnvPrefix(appName)
    viper.AutomaticEnv()

    if err := viper.ReadInConfig(); err != nil {
        log.Fatalf("viper.ReadInConfig: %v", err)
    }

    var cfg conf.Config
    if err := viper.Unmarshal(&cfg); err != nil {
        log.Fatalf("viper.Unmarshal: %v", err)
    }

    conf.InitCfg(&cfg)

    lv, _ := log.ParseLevel(cfg.LogLevel)
    log.SetLevel(lv)
    log.Infof("InitCfg cfg: %+v", cfg)
    log.SetLevel(-1)
    authz.Init(cfg.Authz)
    var ctx, cancel = context.WithTimeout(context.Background(), time.Second*10)
    defer cancel()
    if err := store.SetupMysqlStore(ctx, cfg.RobotMysql); err != nil {
        log.Fatalf("SetupMysqlStore mysql:%s err: %v", cfg.RobotMysql.String(), err)
    }
    //
    server.RunCallbackServer(cfg.Prefix, cfg.Listen)
}
