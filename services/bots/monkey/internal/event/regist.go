package event

import (
    "bitbucket.org/creachadair/shell"
    "context"
    "encoding/json"
    "github.com/larksuite/botframework-go/SDK/protocol"
    larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
    "golang.52tt.com/pkg/foundation/utils"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/services/bots/monkey/internal/conf"
    "golang.52tt.com/services/bots/monkey/internal/event/addwhiteuid"
    applySign "golang.52tt.com/services/bots/monkey/internal/event/applysign"
    "golang.52tt.com/services/bots/monkey/internal/event/bottest"
    channel_ext_game "golang.52tt.com/services/bots/monkey/internal/event/channel-ext-game"
    delWhiteUid "golang.52tt.com/services/bots/monkey/internal/event/delwhiteuid"
    "golang.52tt.com/services/bots/monkey/internal/feishu"
    "golang.52tt.com/services/bots/monkey/internal/model"
    "strings"
)

// If the code is first generated, all code files are generated from the configuration file.
//
// If you modify the configuration file later, and regenerate the code on the original path,
// only the ./handler_event/regist.go will be forced updated, other files are not updated to avoid overwriting user-defined code.
//
// The ./handler_event/regist.go file will be forced update, you should not write your business code in the file.

/*
// RegistHandler: regist handler
func RegistHandler(appID string) {

		// regist open platform event handler
		event.EventRegister(appID, protocol.EventTypeMessage, EventMessage)
		event.EventRegister(appID, protocol.EventTypeP2PChatCreate, EventP2PChatCreate)

		// regist card action handler
		event.CardRegister(appID, "create", ActionCreate)
		event.CardRegister(appID, "delete", ActionDelete)
		event.CardRegister(appID, "update", ActionUpdate)

		event.BotRecvMsgRegister(appID, "default", BotRecvMsgDefault)
		event.BotRecvMsgRegister(appID, "bottest", NewProxy(newDefaultHandlerBotMsg(bottest.NewBotTestCmd)).Handler())
		event.BotRecvMsgRegister(appID, "applySign", NewProxy(newDefaultHandlerBotMsg(applySign.NewApplySignCmd)).Handler())
		event.BotRecvMsgRegister(appID, "addWhiteUid", NewProxy(newDefaultHandlerBotMsg(addWhiteUid.NewAddWhiteUidCmd)).Handler())
		event.BotRecvMsgRegister(appID, "delWhiteUid", NewProxy(newDefaultHandlerBotMsg(delWhiteUid.NewDelWhiteUidCmd)).Handler())
	}
*/
type ProxyManager struct {
    cmdList               map[string]*Proxy
    FeiShu                *feishu.FeiShuServer
    RobotOpenId           string
    CmdGroupMsgReply2User map[string]bool
}

func NewProxyManager() *ProxyManager {
    proxyManager := &ProxyManager{}
    proxyManager.cmdList = make(map[string]*Proxy)
    feishuServer := feishu.NewFeiShuServer(&conf.GlobalCfg().FeiShuConf)
    proxyManager.FeiShu = feishuServer
    proxyManager.RegistHandler()
    dyconfig := conf.NewConfigHandler(conf.DyconfigPath)
    dyconfig.Start()
    proxyManager.RobotOpenId = dyconfig.GetRobotOpenId()
    proxyManager.CmdGroupMsgReply2User = dyconfig.GetCmdGroupMsgReply2UserMap()
    log.Infof("ProxyManager init, proxyManager:%+v, feishuServer:%+v, feishuConf:%+v", proxyManager, feishuServer, feishuServer.Config)
    return proxyManager
}

func (s *ProxyManager) RegistHandler() {
    s.cmdList["bottest"] = NewProxy(newDefaultHandlerBotMsg(bottest.NewBotTestCmd))
    s.cmdList["applySign"] = NewProxy(newDefaultHandlerBotMsg(applySign.NewApplySignCmd))
    s.cmdList["addWhiteUid"] = NewProxy(newDefaultHandlerBotMsg(addwhiteuid.NewAddWhiteUidCmd))
    s.cmdList["delWhiteUid"] = NewProxy(newDefaultHandlerBotMsg(delWhiteUid.NewDelWhiteUidCmd))
    s.cmdList["channelExtGame"] = NewProxy(newDefaultHandlerBotMsg(channel_ext_game.NewChannelExtGameCmd))
}

// 接收信息事件，机器人根据用户输入查询业务数据并返回
func (s *ProxyManager) OnReceiveMessage(ctx context.Context, eventCmd *larkim.P2MessageReceiveV1) error {
    chatId := *eventCmd.Event.Message.ChatId
    content := *eventCmd.Event.Message.Content

    log.DebugWithCtx(ctx, "OnReceiveMessage event receive content: %v from %v", content, chatId)

    // 1. 解析content，获取命令
    msgContent := &model.ReceiveMessageContent{}
    err := json.Unmarshal([]byte(content), msgContent)
    if err != nil {
        log.ErrorWithCtx(ctx, "Unmarshal content err: %v", err)
        return err
    }

    msgText := strings.Trim(msgContent.Text, " ")

    log.InfoWithCtx(ctx, "eventCmd:%+v", utils.ToJson(eventCmd))
    log.InfoWithCtx(ctx, "Message:%+v", utils.ToJson(eventCmd.Event.Message))
    log.InfoWithCtx(ctx, "Sender:%+v", utils.ToJson(eventCmd.Event.Sender))
    if *eventCmd.Event.Message.ChatType == "group" { // 群里@机器人，不支持@多个人
        if len(eventCmd.Event.Message.Mentions) != 1 {
            return nil
        }
        // 判断@的对象是不是机器人
        if *eventCmd.Event.Message.Mentions[0].Id.OpenId != s.RobotOpenId {
            log.Infof("OnReceiveMessage not bot openId: %s, robotOpenId: %s", *eventCmd.Event.Message.Mentions[0].Id.OpenId, s.RobotOpenId)
            return nil
        } else {
            index1 := strings.Index(msgContent.Text, " ")
            index2 := strings.Index(msgContent.Text, "@")
            if index1 > index2 { //@机器人 xxx
                msgText = msgContent.Text[index1+1:]
            } else if index1 == -1 { //xxx@机器人
                msgText = msgContent.Text[:index2]
            } else { //xxx @机器人
                msgText = msgContent.Text[:index1]
            }
        }
    }
    msgText = strings.Trim(msgText, " ")
    args, ok := shell.Split(msgText)
    if !ok {
        log.Infof("OnReceiveMessage shell.Split err")
        return nil
    }
    if len(args) == 0 {
        log.Infof("OnReceiveMessage args is empty")
        return nil
    }
    msgText = strings.Trim(msgText, args[0])
    msgText = strings.Trim(msgText, " ")
    msgContent.Text = msgText

    log.Infof("OnReceiveMessage args: %v, msgText: %v, event:%+v", args, msgText, utils.ToJson(eventCmd.Event))
    var chatType string
    // 历史原因：chatType一直是错误的，这里增加动态配置，判断是否回复到群组里，以免影响旧有用户使用
    if s.CmdGroupMsgReply2User[args[0]] {
        log.Infof("OnReceiveMessage cmd %v reply to user", args[0])
        chatType = *eventCmd.Event.Message.ChatType
    } else {
        log.Infof("OnReceiveMessage cmd %v reply to group", args[0])
        chatType = *eventCmd.Event.Message.ChatId
    }
    msg := &protocol.BotRecvMsg{
        AppID:         eventCmd.EventV2Base.Header.AppID,
        TenantKey:     eventCmd.EventV2Base.Header.TenantKey,
        MsgType:       *eventCmd.Event.Message.MessageType,
        TextParam:     msgText,
        ChatType:      chatType,
        OpenChatID:    *eventCmd.Event.Message.ChatId,
        OpenID:        *eventCmd.Event.Sender.SenderId.OpenId,
        OpenMessageID: *eventCmd.Event.Message.MessageId,
    }
    log.InfoWithCtx(ctx, "receive msg %v from %v, msgText: %v", content, chatId, utils.ToJson(msg))

    defer func() {
        if err := recover(); err != nil {
            log.ErrorWithCtx(ctx, "QueryBusiness and SendMessage panic, err: %v", err)
        }
    }()
    // QueryBusiness 业务处理超时会导致接收消息事件超时，会重复触发同一事件
    go func() {
        log.InfoWithCtx(ctx, "receive msg %v from %v, msgText: %v", content, chatId, utils.ToJson(msg))
        if proxy, ok := s.cmdList[args[0]]; ok {
            proxy.Handler(ctx, msg)
            log.InfoWithCtx(ctx, "response chatId: %v", chatId)
        }

    }()
    return nil
}

// 首次创建会话事件，机器人发送使用介绍给用户
func (s *ProxyManager) OnChatCreated(ctx context.Context, event *larkim.P1P2PChatCreatedV1) error {
    // 发起聊天的用户id
    userId := event.Event.User.UserId
    content := model.IntroduceMsg

    log.DebugWithCtx(ctx, "OnChatCreated event triggered, userId: %v", userId)

    err := s.FeiShu.SendMessage(ctx, feishu.UserType_user_id, userId, feishu.MsgType_interactive, content)
    if err != nil {
        return err
    }
    return nil
}

// 机器人被添加进群聊事件
func (s *ProxyManager) OnGroupAddBot(ctx context.Context, event *larkim.P2ChatMemberBotAddedV1) error {
    chatId := *event.Event.ChatId
    content := model.IntroduceMsg

    log.DebugWithCtx(ctx, "OnGroupAddBot event triggered, chatId: %v", chatId)

    err := s.FeiShu.SendMessage(ctx, feishu.UserType_chat_id, chatId, feishu.MsgType_interactive, content)
    if err != nil {
        return err
    }
    return nil
}
