package bottest

import (
	"fmt"
	"github.com/spf13/cobra"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/bots/monkey/internal/event/utils"
	"time"
)

func NewBotTestCmd() *cobra.Command {
	accountCmd := &cobra.Command{
		Use:          "bottest",
		SilenceUsage: true,
	}
	accountCmd.AddCommand(notify())
	return accountCmd
}

func notify() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "flushed",
		Short: "刷新npc开场白 eg bottest flushed --npcId 123 ",
	}

	//localizer := i18n.NewLocalizerAsync("game")
	time.Sleep(time.Second * 3)

	cmd.PersistentFlags().String("npcId", "", "npcId")
	cmd.Run = func(cmd *cobra.Command, args []string) {
		c := utils.NewStdout(cmd.OutOrStdout())
		defer c.Flush()
		ctx := cmd.Context()
		npcId, _ := cmd.PersistentFlags().GetString("npcId")

		log.InfoWithCtx(ctx, "flushed senderId: %d", npcId)

		cmd.OutOrStdout().Write([]byte(fmt.Sprintf("npc_id: %s", npcId)))

	}
	return cmd
}
