package applySign

import (
    "context"
    "encoding/json"
    "fmt"
    "github.com/jinzhu/gorm"
    "github.com/spf13/cobra"
    "golang.52tt.com/clients/account"
    anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
    "golang.52tt.com/pkg/log"
    anchorcontractpb "golang.52tt.com/protocol/services/anchorcontract-go"
    "golang.52tt.com/services/bots/monkey/internal/conf"
    fscontext "golang.52tt.com/services/bots/monkey/internal/context"
    "golang.52tt.com/services/bots/monkey/internal/event/utils"
    "time"
)

func NewApplySignCmd() *cobra.Command {
    applyCmd := &cobra.Command{
        Use:          "applySign",
        SilenceUsage: true,
    }
    applyCmd.AddCommand(applySign())
    return applyCmd
}

func applySign() *cobra.Command {
    cmd := &cobra.Command{
        Use:   "record",
        Short: "大主播签约 eg applySign record --identity_type 1 --great_anchor true --uid ********* --guild_id 2555076 --identity_num 21021319990907331X ",
    }

    time.Sleep(time.Second * 3)

    cmd.PersistentFlags().Uint32("identity_type", 0, "身份类型")
    cmd.PersistentFlags().Bool("great_anchor", false, "是否大主播")
    cmd.PersistentFlags().Uint32("uid", 0, "用户uid")
    cmd.PersistentFlags().Uint32("guild_id", 0, "签约公会长号id")
    cmd.PersistentFlags().String("identity_num", "", "身份证号")

    cmd.Run = func(cmd *cobra.Command, args []string) {
        out := utils.NewStdout(cmd.OutOrStdout())
        defer out.Flush()
        ctx := cmd.Context()
        tmpUserInfo, _ := fscontext.UserInfoFromCtx(ctx)

        if tmpUserInfo == nil {
            out.Error("获取用户失败")
            return
        }
        if !utils.CheckUserPermission(ctx, tmpUserInfo.OpenId, "applySign") {
            out.Error(fmt.Sprintf("用户 %s 没有 %s 权限", tmpUserInfo.OpenId, "applySign"))
            return
        }
        identityType, _ := cmd.Flags().GetUint32("identity_type")
        greatAnchor, _ := cmd.Flags().GetBool("great_anchor")
        uid, _ := cmd.Flags().GetUint32("uid")
        guildId, _ := cmd.Flags().GetUint32("guild_id")
        identityNum, _ := cmd.Flags().GetString("identity_num")
        log.InfoWithCtx(ctx, "applySign uid:%v identityType: %d greatAnchor:%v guildId:%v identityNum:%v", uid, identityType, greatAnchor, guildId, identityNum)

        if uid == 0 || guildId == 0 || identityNum == "" {
            out.Error("检查参数")
            return
        }

        accountCli, err := account.NewClient()
        if err != nil {
            out.Errorf("new account client fail %v\n", err)
            return
        }

        anchorcontractGoClient, err := anchorcontract_go.NewClient()
        if err != nil {
            out.Errorf("new anchorcontract_go client fail %v\n", err)
            return
        }

        ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
        defer cancel()
        userInfo, err := accountCli.GetUserByUid(ctx, uid)
        if err != nil {
            out.Errorf("获取用户信息失败 uid:%v %v\n", uid, err)
            //fmt.Printf("GetUserByUid fail %v\n", err)
            return
        }
        if userInfo.GetLastLoginAt() < uint32(time.Now().AddDate(0, -1, 0).Unix()) {
            out.Errorf("please check uid if valid, userInfo= %v\n", userInfo)
            //fmt.Printf("please check uid if valid, userInfo=%+v\n", userInfo)
            return
        }

        contract, err := anchorcontractGoClient.GetUserContract(ctx, 0, uid)
        if err != nil {
            out.Errorf("获取签约合同失败 uid:%v %v\n", uid, err)
            //fmt.Printf("GetUserContract fail %v\n", err)
            return
        }
        if contract.GetContract().GetGuildId() > 0 {
            out.Errorf("已经签约. %+v\n", contract)
            //fmt.Printf("already sign. %+v\n", contract)
            return
        }

        mysqlCfg := conf.GlobalCfg().SignContractMysql

        mysqlDb, err := gorm.Open("mysql", mysqlCfg.ConnectionString())
        if err != nil {
            log.ErrorWithCtx(ctx, "Failed to create mysql %v", err)
            return
        }

        mysqlDb = mysqlDb.Debug()

        extra := ""
        if identityType == 1 && greatAnchor {
            extra = `{"great_anchor":true}`
        }

        err = mysqlDb.Exec("insert into tbl_contract_apply_v2(uid,guild_id,identity_num,contract_duration,identity_type,extra) values(?,?,?,?,?,?)",
            uid, guildId, identityNum, uint32(36), identityType, extra).Error
        if err != nil {
            fmt.Printf("insert db fail %v\n", err)
            return
        }

        applySignList, err := anchorcontractGoClient.GetUserApplySignRecord(ctx, 0, &anchorcontractpb.GetUserApplySignRecordReq{
            Uid:        uid,
            PageSize:   2,
            StatusList: []uint32{0, 1},
        })
        if err != nil {
            fmt.Printf("GetUserApplySignRecord fail %v\n", err)
            return
        }

        str, _ := json.MarshalIndent(applySignList.RecordList, "", " ")

        out.Infof("申请签约成功: 用户uid:%v, 签约记录:%v", uid, string(str))

    }
    return cmd
}
