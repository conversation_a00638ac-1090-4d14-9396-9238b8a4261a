package server

import (
    "crypto/aes"
    "crypto/cipher"
    "crypto/sha256"
    "encoding/base64"
    "encoding/json"
    "errors"
    "fmt"
    "io"
    "io/ioutil"
    "os"
    "strings"

    "github.com/gin-gonic/gin"
    "github.com/larksuite/botframework-go/SDK/common"
    "github.com/larksuite/botframework-go/SDK/event"
    "golang.52tt.com/pkg/log"
)

// EventCallback open platform event
func EventCallback(c *gin.Context) {

    body, err := io.ReadAll(c.Request.Body)
    if err != nil || len(body) == 0 {
        common.Logger(c).Errorf("eventReqParamsError: readHttpBodyError err[%v]bodyLen[%d]", err, len(body))
        c.JSON(500, gin.H{"codemsg": common.ErrEventParams.String()})
        return
    }

    log.Infof("body:%v ", string(body))
    content, err := eventDataDecrypter(string(body), os.Getenv("EncryptKey"))
    log.Infof("content:%v ", content)

    appID := appconf.AppID
    challenge, err := event.EventCallback(c, string(body), appID)
    common.Logger(c).Infof("eventInfo: challenge[%s] err[%v]", challenge, err)
    if err != nil {
        c.JSON(500, gin.H{"codemsg": fmt.Sprintf("%v", err)})
    } else if "" != challenge {
        c.JSON(200, gin.H{"challenge": challenge})
    } else {
        c.JSON(200, gin.H{"codemsg": common.Success.String()})
    }
}

// CardCallback card action callback
func CardCallback(c *gin.Context) {

    body, err := ioutil.ReadAll(c.Request.Body)
    if err != nil || len(body) == 0 {
        common.Logger(c).Errorf("eventReqParamsError: readHttpBodyError err[%v]bodyLen[%d]", err, len(body))
        c.JSON(500, gin.H{"codemsg": common.ErrCardParams.String()})
        return
    }

    // for verify signature
    header := map[string]string{
        "X-Lark-Request-Timestamp": c.Request.Header.Get("X-Lark-Request-Timestamp"),
        "X-Lark-Request-Nonce":     c.Request.Header.Get("X-Lark-Request-Nonce"),
        "X-Lark-Signature":         c.Request.Header.Get("X-Lark-Signature"),
    }

    appID := appconf.AppID
    log.Debugf("body: %s", string(body))
    card, challenge, err := event.CardCallBack(c, appID, header, body)
    common.Logger(c).Infof("cardInfo: challenge[%s] err[%v]", challenge, err)
    if err != nil {
        c.JSON(500, gin.H{"codemsg": fmt.Sprintf("%v", err)})
    } else if "" != challenge {
        c.JSON(200, gin.H{"challenge": challenge})
    } else {
        data, err := json.Marshal(card)
        if err != nil {
            c.JSON(500, gin.H{"codemsg": fmt.Sprintf("%v", err)})
        } else {
            c.String(200, string(data))
        }
    }
}

//
//var handler = dispatcher.NewEventDispatcher(os.Getenv("VerifyToken"), os.Getenv("EncryptKey")).OnP2MessageReceiveV1(
//	func(ctx context.Context, event *larkim.P2MessageReceiveV1) error {
//		//机器人接收到用户发送的消息后触发此事件。
//		log.Infof("body:%v", string(event.Body))
//		return nil
//	}).OnP2MessageReadV1(
//	func(ctx context.Context, event *larkim.P2MessageReadV1) error {
//		// 用户阅读机器人发送的单聊消息后触发此事件
//		return nil
//	})

func eventDataDecrypter(encryptData, keyStr string) (string, error) {
    type AESMsg struct {
        Encrypt string `json:"encrypt"`
    }
    var encrypt AESMsg
    err := json.Unmarshal([]byte(encryptData), &encrypt)
    if err != nil {
        return "", fmt.Errorf("dataDecrypter jsonUnmarshalError[%v]", err)
    }

    buf, err := base64.StdEncoding.DecodeString(encrypt.Encrypt)
    if err != nil {
        return "", fmt.Errorf("base64StdEncode Error[%v]", err)
    }

    key := sha256.Sum256([]byte(keyStr))

    block, err := aes.NewCipher(key[:sha256.Size])
    if err != nil {
        return "", fmt.Errorf("AESNewCipher Error[%v]", err)
    }

    if len(buf) < aes.BlockSize {
        return "", errors.New("ciphertext too short")
    }
    iv := buf[:aes.BlockSize]
    buf = buf[aes.BlockSize:]

    // CBC mode always works in whole blocks.
    if len(buf)%aes.BlockSize != 0 {
        return "", errors.New("ciphertext is not a multiple of the block size")
    }

    mode := cipher.NewCBCDecrypter(block, iv)
    mode.CryptBlocks(buf, buf)

    n := strings.Index(string(buf), "{")
    if n == -1 {
        n = 0
    }
    m := strings.LastIndex(string(buf), "}")
    if m == -1 {
        m = len(buf) - 1
    }

    return string(buf[n : m+1]), nil
}
