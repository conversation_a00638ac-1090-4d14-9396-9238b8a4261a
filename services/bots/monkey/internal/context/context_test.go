package context

import (
	"context"
	"testing"

	"github.com/larksuite/botframework-go/SDK/protocol"
)

func TestContext(t *testing.T) {
	ctx := context.Background()
	//ctx = WithRecvMsg(WithFeishuUser(ctx, &protocol.UserInfo{ID: "1"}), &protocol.BotRecvMsg{AppID: "abc"})
	ctx = WithFeishuUser(WithRecvMsg(ctx, &protocol.BotRecvMsg{AppID: "abc"}), &protocol.UserInfo{ID: "1"})
	t.Log(FeishuUser(ctx))
	t.Log(RecvMsg(ctx))
}
