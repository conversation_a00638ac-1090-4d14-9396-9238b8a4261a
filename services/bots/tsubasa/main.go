package main

import (
	"context"
	"golang.52tt.com/pkg/config"
	grpcEx "golang.52tt.com/pkg/foundation/grpc/server"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/bots/tsubasa/app"
	"golang.52tt.com/services/bots/tsubasa/conf"
	"golang.52tt.com/services/bots/tsubasa/mongo"
	"gopkg.in/yaml.v2"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/larksuite/botframework-go/SDK/appconfig"
	"github.com/larksuite/botframework-go/SDK/common"
	"github.com/larksuite/botframework-go/SDK/protocol"
	"golang.52tt.com/services/bots/tsubasa/handler_event"
	tsConf "golang.52tt.com/services/bots/tsubasa/tsubasa-configz"
)

func main() {
	flags := grpcEx.ParseServerFlags(os.Args)

	f, err := os.Open(flags.ConfigPath)
	if err != nil {
		log.Fatalln(err)
	}
	defer f.Close()

	var cfg = &conf.TsConfig{
		Mongo: &conf.MongoConfig{},
		//Info:  map[string]*conf.Clubs{},
	}
	err = yaml.NewDecoder(f).Decode(&cfg)
	if err != nil {
		log.Fatalln(err)
	}
	mongo.MDao, err = mongo.NewMongoDao(&config.MongoConfig{
		Addrs:       cfg.Mongo.Addrs,
		Database:    cfg.Mongo.Database,
		MaxPoolSize: cfg.Mongo.MaxPool,
		UserName:    cfg.Mongo.UserName,
		Password:    cfg.Mongo.Password,
	})
	if err != nil {
		return
	}
	err = mongo.MDao.CreateIndexes()
	if err != nil {
		return
	}
	r := gin.Default()

	d, err := tsConf.NewDynamicOperation("/data/oss/conf-center/tt/tsubasa_conf.yaml")
	if err != nil || d == nil {
		return
	}
	tsConf.TsConfig = d
	//conf.SetConfig(cfg)

	common.InitLogger(common.NewCommonLogger(), common.DefaultOption())
	defer common.FlushLogger()

	err = InitInfo()
	if err != nil {
		common.Logger(context.TODO()).Errorf("InitError[%v]", err)
		return
	}
	app.InitClubName()

	r.POST("/event", EventCallback) //open platform event callback
	r.POST("/card", CardCallback)   //card action callback

	// NOTE your business code
	r.Run(":8000")
}

func InitInfo() error {
	// Initialize app config
	conf := appconfig.AppConfig{
		AppID:       "cli_9f7e92672032500c",
		AppType:     protocol.InternalApp, // Independent Software Vendor App / Internal App
		AppSecret:   os.Getenv("AppSecret"),
		VerifyToken: os.Getenv("VerifyToken"),
		EncryptKey:  os.Getenv("EncryptKey"),
	}

	appconfig.Init(conf)

	// regist handler
	handler_event.RegistHandler(conf.AppID)

	return nil
}
