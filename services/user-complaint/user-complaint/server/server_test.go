package server

//
//import (
//	"context"
//	"encoding/json"
//	"fmt"
//	"testing"
//	"time"
//
//	"golang.52tt.com/pkg/log"
//	pb "golang.52tt.com/protocol/services/user-complaint"
//	"google.golang.org/grpc"
//)
//
//var server *UserComplaintServerImpl
//
//func init() {
//	log.SetLevel(log.DebugLevel)
//	ctx := context.WithValue(context.TODO(), "configfile", "../user-complaint.json")
//	err := error(nil)
//	server, err = NewUserComplaintServerImpl(ctx, nil, grpc.WithBlock())
//	if err != nil {
//		panic(err)
//	}
//
//	time.Sleep(time.Second * 2)
//}
//
//// go test -timeout 30s -run ^TestBatchHandleUserComplaint$ golang.52tt.com/services/user-complaint/user-complaint/server -v -count=1
//func TestBatchHandleUserComplaint(t *testing.T) {
//	/*
//		resp, err := server.BatchHandleUserComplaint(context.TODO(), &pb.BatchHandleUserComplaintReq{
//			HandleType: uint32(pb.HANDLE_TYPE_HANDLE_TYPE_REJECT),
//			RecordIds:  []uint32{1},
//			Handler:    "w",
//		})
//		fmt.Printf("%v,%v", resp, err)
//	*/
//
//	resp, err := server.BatchHandleUserComplaint(context.TODO(), &pb.BatchHandleUserComplaintReq{
//		HandleType: uint32(pb.HANDLE_TYPE_HANDLE_TYPE_AGREE_ACCEPT),
//		RecordIds:  []uint32{2},
//		Handler:    "ch",
//	})
//	fmt.Printf("%v,%v", resp, err)
//}
//
//// go test -timeout 30s -run ^TestBatchApplyUserComplaint$ golang.52tt.com/services/user-complaint/user-complaint/server -v -count=1
//func TestBatchApplyUserComplaint(t *testing.T) {
//	resp, err := server.BatchApplyUserComplaint(context.TODO(), &pb.BatchApplyUserComplaintReq{RecordIds: []uint32{1, 2, 3}})
//	fmt.Printf("%v,%v", resp, err)
//}
//
//// go test -timeout 30s -run ^TestBatchNotifyUsers$ golang.52tt.com/services/user-complaint/user-complaint/server -v -count=1
//func TestBatchNotifyUsers(t *testing.T) {
//	resp, err := server.BatchNotifyUsers(context.TODO(), &pb.BatchNotifyUsersReq{RecordIds: []uint32{1}, OpMsg: "aaaa", Handler: "lisi"})
//	fmt.Printf("%v,%v", resp, err)
//}
//
//// go test -timeout 30s -run ^TestListUserComplaint$ golang.52tt.com/services/user-complaint/user-complaint/server -v -count=1
//func TestListUserComplaint(t *testing.T) {
//
//	resp, _ := server.ListUserComplaint(context.TODO(), &pb.ListUserComplaintReq{
//		BusinessType: uint32(pb.BUSINESS_TYPE_BUSINESS_TYPE_FUN),
//		QuerysType:   uint32(pb.QUERY_TYPE_QUERY_TYPE_TTID),
//		OpStatus:     uint32(pb.COMPLAINT_OPR_COMPLAINT_OPR_NONE),
//		Ids:          []uint64{1, 2, 3},
//		Page:         1,
//		PageNum:      2,
//	})
//	for _, info := range resp.GetList() {
//		fmt.Printf("%+v\n", info)
//	}
//
//	resp, _ = server.ListUserComplaint(context.TODO(), &pb.ListUserComplaintReq{
//		BusinessType: uint32(pb.BUSINESS_TYPE_BUSINESS_TYPE_FUN),
//		QuerysType:   uint32(pb.QUERY_TYPE_QUERY_TYPE_GUILD_SHORTID),
//		OpStatus:     uint32(pb.COMPLAINT_OPR_COMPLAINT_OPR_NONE),
//		Ids:          []uint64{9999},
//		Page:         0,
//		PageNum:      3,
//	})
//	for _, info := range resp.GetList() {
//		fmt.Printf("%+v\n", info)
//	}
//}
//
//// go test -timeout 30s -run ^TestTime$ golang.52tt.com/services/user-complaint/user-complaint/server -v -count=1
//func TestTime(t *testing.T) {
//	now := time.Now()
//	period := int(server.dyConfig.GetTbeanConsumePeriod() - 1)
//
//	beginTs := now.AddDate(0, 0, -period)
//	beginTs = time.Date(beginTs.Year(), beginTs.Month(), beginTs.Day(), 0, 0, 0, 0, time.Local)
//	t.Log(beginTs)
//}
//
//// go test -timeout 30s -run ^TestSendIMMsgWithJumpUrl$ golang.52tt.com/services/user-complaint/user-complaint/server -v -count=1
////func TestSendIMMsgWithJumpUrl(t *testing.T) {
////	time.Sleep(time.Second * 3)
////	ctx, cancel := context.WithTimeout(context.TODO(), time.Second*3)
////	defer cancel()
////
////	SendIMMsgWithJumpUrl(ctx, 2404178, "点击", "点击", "https://app.52tt.com/testing/frontend-web-assist-user-complaint/index.html?immersion=1#!/detail?recordId=5")
////}
//
///*
//INSERT INTO tbl_user_present_send_daily_record (uid,tbean_consume,channel_type,record_date) VALUES(2521733,100000,4,'2022-05-16');
//INSERT INTO tbl_user_present_send_daily_record (uid,tbean_consume,channel_type,record_date) VALUES(2532765, 99999,4,'2022-05-16');
//*/
//// go test -timeout 30s -run ^TestGetUserComplaintEntry$ golang.52tt.com/services/user-complaint/user-complaint/server -v -count=1
//func TestGetUserComplaintEntry(t *testing.T) {
//
//	ctx, cancel := context.WithTimeout(context.TODO(), time.Second*3)
//	defer cancel()
//
//	// case 1 WhiteHighUid
//	req := &pb.GetUserComplaintEntryReq{Uid: 2404178, GuildDisplayId: 666, ChannelName: "xxxxxx"}
//	resp, err := server.GetUserComplaintEntry(ctx, req)
//	t.Logf("case1 req %+v resp %+v err %+v\n", req, resp, err)
//
//	// case 2.1 TbeanConsume > 10w & Freeze
//	req = &pb.GetUserComplaintEntryReq{Uid: 2521733, GuildDisplayId: 666, ChannelName: "xxxxxx"}
//	resp, err = server.GetUserComplaintEntry(ctx, req)
//	t.Logf("case2.1 req %+v resp %+v err %+v\n", req, resp, err)
//
//	// case 3.2 TbeanConsume < 10w & HandleCnt = 1
//	req = &pb.GetUserComplaintEntryReq{Uid: 2532765, GuildDisplayId: 666, ChannelName: "xxxxxx"}
//	resp, err = server.GetUserComplaintEntry(ctx, req)
//	t.Logf("case3.2 req %+v resp %+v err %+v\n", req, resp, err)
//
//	// case 2.2 TbeanConsume > 10w & dailt count < 3
//	req = &pb.GetUserComplaintEntryReq{Uid: 2521733, GuildDisplayId: 666, ChannelName: "xxxxxx"}
//	resp, err = server.GetUserComplaintEntry(ctx, req)
//	t.Logf("case2.2 req %+v resp %+v err %+v\n", req, resp, err)
//
//	// case 2.3 TbeanConsume > 10w & dailt count = 3
//	req = &pb.GetUserComplaintEntryReq{Uid: 2521733, GuildDisplayId: 666, ChannelName: "xxxxxx"}
//	resp, err = server.GetUserComplaintEntry(ctx, req)
//	t.Logf("case2.3 req %+v resp %+v err %+v\n", req, resp, err)
//
//	// case 3.1 TbeanConsume < 10w & HandleCnt = 0
//	req = &pb.GetUserComplaintEntryReq{Uid: 2532765, GuildDisplayId: 666, ChannelName: "xxxxxx"}
//	resp, err = server.GetUserComplaintEntry(ctx, req)
//	t.Logf("case3.1 req %+v resp %+v err %+v\n", req, resp, err)
//
//}
//
//// go test -timeout 30s -run ^TestGetUserComplaintList$ golang.52tt.com/services/user-complaint/user-complaint/server -v -count=1
//func TestGetUserComplaintList(t *testing.T) {
//
//	ctx, cancel := context.WithTimeout(context.TODO(), time.Second*3)
//	defer cancel()
//
//	req := &pb.GetUserComplaintListReq{Uid: 2404178, PageNum: 666}
//	resp, err := server.GetUserComplaintList(ctx, req)
//	if err != nil {
//		t.Log(err)
//		return
//	}
//	for _, info := range resp.GetList() {
//		t.Logf("%s\n", JSON(info))
//	}
//}
//
//// go test -timeout 30s -run ^TestGetGuildComplaintList$ golang.52tt.com/services/user-complaint/user-complaint/server -v -count=1
//func TestGetGuildComplaintList(t *testing.T) {
//
//	ctx, cancel := context.WithTimeout(context.TODO(), time.Second*3)
//	defer cancel()
//
//	req := &pb.GetGuildComplaintListReq{GuildId: 666, Status: 1, PageNum: 10}
//	resp, err := server.GetGuildComplaintList(ctx, req)
//	if err != nil {
//		t.Log(err)
//		return
//	}
//	for _, info := range resp.GetList() {
//		t.Logf("%s\n", JSON(info))
//	}
//
//	t.Logf("---------------------\n")
//	req.Status = 0
//	resp, err = server.GetGuildComplaintList(ctx, req)
//	if err != nil {
//		t.Log(err)
//		return
//	}
//	for _, info := range resp.GetList() {
//		t.Logf("%s\n", JSON(info))
//	}
//}
//
//// go test -timeout 30s -run ^TestCreateComplaint$ golang.52tt.com/services/user-complaint/user-complaint/server -v -count=1
//func TestCreateComplaint(t *testing.T) {
//	ctx, cancel := context.WithTimeout(context.TODO(), time.Second*3)
//	defer cancel()
//
//	req := &pb.CreateComplaintReq{Info: &pb.ComplaintInfo{Uid: 2404178, GuildId: 0, PicList: []string{"https://aaa"}, MediaList: []*pb.MediaInfo{{Name: "aa", Url: "aa1"}}}}
//	resp, err := server.CreateComplaint(ctx, req)
//	t.Logf("req %+v resp %+v err %+v\n", req, resp, err)
//}
//
//// go test -timeout 30s -run ^TestSubmitGuildComplaint$ golang.52tt.com/services/user-complaint/user-complaint/server -v -count=1
//func TestSubmitGuildComplaint(t *testing.T) {
//	ctx, cancel := context.WithTimeout(context.TODO(), time.Second*3)
//	defer cancel()
//
//	req := &pb.SubmitGuildComplaintReq{RecordId: 5, Feedback: "xxxxxx"}
//	_, err := server.SubmitGuildComplaint(ctx, req)
//	t.Logf("req %+v err %+v\n", req, err)
//}
//
//// go test -timeout 30s -run ^TestArchiveUserComplaint$ golang.52tt.com/services/user-complaint/user-complaint/server -v -count=1
//func TestArchiveUserComplaint(t *testing.T) {
//	ctx, cancel := context.WithTimeout(context.TODO(), time.Second*3)
//	defer cancel()
//
//	req := &pb.ArchiveUserComplaintReq{RecordId: 1}
//	_, err := server.ArchiveUserComplaint(ctx, req)
//	t.Logf("req %+v err %+v\n", req, err)
//}
//
//// go test -timeout 30s -run ^TestGetUserComplaintRecordByRecent$ golang.52tt.com/services/user-complaint/user-complaint/server -v -count=1
//func TestGetUserComplaintRecordByRecent(t *testing.T) {
//	ctx, cancel := context.WithTimeout(context.TODO(), time.Second*3)
//	defer cancel()
//
//	req := &pb.GetUserComplaintListReq{Uid: 2404178, Page: 0, PageNum: 1}
//	resp, err := server.GetUserComplaintList(ctx, req)
//	t.Logf("req %+v err %+v resp %+v\n", req, err, resp)
//
//	req = &pb.GetUserComplaintListReq{Uid: 2404178, Page: 1, PageNum: 1}
//	resp, err = server.GetUserComplaintList(ctx, req)
//	t.Logf("req %+v err %+v resp %+v\n", req, err, resp)
//}
//
//func JSON(in interface{}) string {
//	s, _ := json.MarshalIndent(in, "", "\t")
//	return string(s)
//}
