package server

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jmoiron/sqlx"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/sync"
	"golang.52tt.com/protocol/common/status"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	pb "golang.52tt.com/protocol/services/user-decoration"
	"golang.52tt.com/services/notify"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis"
	_ "github.com/go-sql-driver/mysql"
)

type store struct {
	mysql           *sqlx.DB
	redis           *redis.Client
	apiCenterClient *apicenter.Client
}

func (s *store) alterTable() {
	query := "alter table dec_info add is_del tinyint(1) NOT NULL default 0;"
	s.mysql.Exec(query)
	query = "alter table dec_info add resource_md5 varchar(128) not null default '';"
	s.mysql.Exec(query)
}

// 给用户发放主页飘
func (s *store) upsert(ctx context.Context, req *upsertReq) (decName string, effectEnd int64, resp *upsertResp, err error) {

	resp = &upsertResp{}

	// SetNX防止并行发放导致过期时间错误,这里最多等待1s
	err = Retry(ctx, 10, 100*time.Millisecond, func() error {
		res, err := s.redis.SetNX(getUpsertNxKey(req.uid, req.Id), "0", 60*time.Second).Result()
		if err != nil {
			log.ErrorWithCtx(ctx, "upsert SetNX fail req:%+v err:%v", req, err)
			return err
		}
		if !res {
			return errors.New("setNx fail")
		}
		return nil
	})

	if err != nil {
		return decName, effectEnd, resp, err
	}

	defer s.redis.Del(getUpsertNxKey(req.uid, req.Id))

	if req.orderId != "" {
		if s.redis.Incr(req.orderId).Val() != 1 {
			// 订单号重复
			return decName, effectEnd, resp, protocol.NewServerError(status.ErrRiskControlAwardCenterOrderExist)
		} else {
			s.redis.Expire(req.orderId, 6*time.Hour)
		}

		defer func() {
			if err != nil {
				s.redis.Decr(req.orderId)
			}
		}()
	}

	row := s.mysql.QueryRowxContext(ctx, retrieveDecorationId, req.Typ, req.Id, req.Version)
	if err := row.Err(); err != nil {
		log.ErrorWithCtx(ctx, "upsert QueryRowxContext req:%+v err:%v", req, err)
		return decName, effectEnd, resp, err
	} //

	decInfoId := 0
	name := ""
	if err := row.Scan(&decInfoId, &name); err != nil {
		log.ErrorWithCtx(ctx, "upsert Scan req:%+v err:%v", req, err)
		return name, effectEnd, resp, err
	}
	rows, err := s.mysql.QueryxContext(ctx, retrieveUserDecorationInfo, req.uid, decInfoId, req.fusionTtid)
	if err != nil {
		log.ErrorWithCtx(ctx, "upsert QueryRowxContext req:%+v err:%v", req, err)
		return name, effectEnd, resp, err
	}
	defer func() {
		_ = rows.Close()
	}()

	b := 0
	e := 0

	if rows.Next() {
		if err := rows.Scan(&b, &e); err != nil {
			log.ErrorWithCtx(ctx, "upsert Scan req:%+v err:%v", req, err)
			return name, effectEnd, resp, err
		}
	} else {
		if req.Typ == Float {
			_, err = s.mysql.ExecContext(ctx, createUserDecorationInfoWithFusion, req.uid, decInfoId, req.fusionTtid, req.begin, req.dur)
			if err != nil {
				log.ErrorWithCtx(ctx, "createUserDecorationInfoWithFusion ExecContext req:%+v err:%v", req, err)
			}
			//return resp, nil
		} else { //if req.Typ == Wall
			//就先创建再追加吧 反正发放操作不算太频繁 不然这个逻辑不好拧过来  后面有空再改
			_, err = s.mysql.ExecContext(ctx, createUserDecorationInfo, req.uid, decInfoId, req.begin, req.begin)
			if err != nil {
				log.ErrorWithCtx(ctx, "createUserDecorationInfo ExecContext req:%+v err:%v", req, err)
			}

			// 如果是直播奖励类型的房间资料卡，额外添加扩展信息
			if req.Typ == int(pb.Type_CHANNEL_INFO_CARD) && req.cardExtend != nil {
				extend, _ := json.Marshal(req.cardExtend)
				err := s.InsertAwardCardExtend(ctx, req.uid, uint32(decInfoId), string(extend))
				if err != nil {
					log.ErrorWithCtx(ctx, "InsertAwardCardExtend failed, uid %d err:%s", req.uid, err.Error())
				}
			}
		}
	}

	var end int64
	updateSql := updateUserDecorationInfoWithFusion

	if req.typ == TimeAppend {
		if int64(e) < req.begin {
			end = req.begin + req.dur
			_, err = s.mysql.ExecContext(ctx, updateSql, req.begin, end, req.uid, decInfoId, req.FusionTtid)
			if err != nil {
				log.ErrorWithCtx(ctx, "upsert ExecContext req:%+v err:%v", req, err)
				return name, effectEnd, resp, err
			}
		} else {
			end = int64(e) + req.dur
			_, err = s.mysql.ExecContext(ctx, updateSql, b, end, req.uid, decInfoId, req.FusionTtid)
			if err != nil {
				log.ErrorWithCtx(ctx, "upsert ExecContext req:%+v err:%v", req, err)
				return name, effectEnd, resp, err
			}
		}
	} else if req.typ == TimeAlter {
		end = req.dur
		_, err = s.mysql.ExecContext(ctx, updateSql, req.begin, end, req.uid, decInfoId, req.FusionTtid)
		if err != nil {
			log.ErrorWithCtx(ctx, "upsert ExecContext req:%+v err:%v", req, err)
			return name, effectEnd, resp, err
		}
	}

	// 如果是直播奖励类型的房间资料卡，额外更新扩展信息
	if req.Typ == int(pb.Type_CHANNEL_INFO_CARD) && req.cardExtend != nil {
		extend, _ := json.Marshal(req.cardExtend)
		err := s.UpdateAwardCardExtend(ctx, req.uid, uint32(decInfoId), string(extend))
		if err != nil {
			log.ErrorWithCtx(ctx, "InsertAwardCardExtend failed, uid %d err:%s", req.uid, err.Error())
		}
	}

	effectEnd = end

	// 考虑一下减时间的操作
	if end < time.Now().Unix() {
		key := strconv.Itoa(int(req.uid)) + ":" + strconv.Itoa(req.typ)
		if req.FusionTtid != "" {
			key += ":" + req.FusionTtid
		}
		res := s.redis.Del(key)
		if err := res.Err(); err != nil {
			return name, effectEnd, resp, err
		}
	}

	//如果获得了与目前佩戴的id相同，等级更高的光效，则覆盖当前佩戴光效
	if (req.Typ == int(pb.Type_WALL) || req.Typ == int(pb.Type_CHANNEL_INFO_CARD)) && req.dur > 0 {
		key := strconv.Itoa(int(req.uid)) + ":" + strconv.Itoa(req.Typ)
		res := s.redis.Get(key)
		if err := res.Err(); err != redis.Nil && err != nil {
			return name, effectEnd, resp, err
		} else if err != redis.Nil {
			val := strings.Split(res.Val(), ":")
			nowId := val[0]
			nowVersion := val[1]
			if nowId == req.Id && nowVersion < req.Version {
				val := req.Id + ":" + req.Version
				res := s.redis.Set(key, val, time.Until(time.Unix(end, 0)))
				if err := res.Err(); err != nil {
					return name, effectEnd, resp, err
				}
			}
		}
	}
	//礼物墙光效要发推送，主页飘之前没有先不管
	if req.Typ == int(pb.Type_WALL) {
		_ = s.pushImWithURL(ctx, req.uid, name, end)
	}

	return name, effectEnd, resp, nil
}

// 礼物墙光效推送
func (s *store) pushImWithURL(ctx context.Context, uid uint32, name string, end int64) error {
	content := "恭喜你获得【" + name + "】礼物墙光效，有效期至" + time.Unix(end, 0).Format("2006-01-02") + ",快去佩戴吧！\n立即佩戴>"
	msg := &apiPB.ImMsg{
		ImType: &apiPB.ImType{
			SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
			ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
			ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL),
		},
		FromUid: uint32(10000),
		ToIdList: []uint32{
			uid,
		},
		ImContent: &apiPB.ImContent{
			TextHlUrl: &apiPB.ImTextWithHighlightUrl{
				Content:    content,
				Hightlight: "立即佩戴>",
				Url:        "tt://m.52tt.com/userPersonalityDress?select_tab=4",
			},
		},
		Platform:    apiPB.Platform_UNSPECIFIED,
		AppPlatform: "all",
	}
	err := s.apiCenterClient.SendImMsg(ctx, uid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendIMMsgWithJumpUrl fail. uid:%d err: %s", uid, err.Error())
		return err
	}
	notify.NotifySync(uid, sync.SyncReq_IM_MSG)
	return nil
}

// 用户当前拥有的主页飘
func (s *store) userDecorations(ctx context.Context, req *userDecorationsReq) (*userDecorationsResp, error) {

	resp := &userDecorationsResp{
		decs: []*pb.DecInfo{},
	}
	var query string
	var rows *sqlx.Rows
	var err error
	nowTs := time.Now().Unix()

	//礼物墙光效只返回用户拥有的每个光效的最高级,sql比较恶心但是耗时没怎么提升，还好还好
	if req.typ == 2 {
		query = `
		SELECT b.typ,  b.decoration_id, b.version, b.url, b.md5, b.url_pic, b.name, b.begin, b.end, b.extend, b.rank, b.is_show FROM
		(SELECT
			decoration_id, MAX(version) m
		FROM
			dec_info
		JOIN 
			decorations
		ON
			dec_info.id = decorations.dec_info_id
		WHERE
			uid = ?
		AND 
			typ = ?
		AND 
			begin <= ?
		AND 
			end >= ?
		GROUP BY
			decoration_id)t,
		(	
			SELECT typ,  decoration_id, version, url, md5, url_pic, name, begin, end, extend, rank, is_show FROM
				dec_info
			JOIN 
				decorations
			ON
				dec_info.id = decorations.dec_info_id
			WHERE
				uid = ?
			AND 
				typ = ?) b 
		WHERE t.decoration_id = b.decoration_id
		AND t.m = b.version
		`
		rows, err = s.mysql.QueryxContext(ctx, query, req.uid, req.typ, nowTs, nowTs, req.uid, req.typ)
		if err != nil {
			log.ErrorWithCtx(ctx, "mysql err: %v", err)
			return resp, err
		}
	} else {
		query = `
		SELECT
			dec_info_id ,typ, decoration_id, version, url,resource_url, md5, url_pic, name, begin, end, extend, rank, is_show, fusion_ttid, fusion_color , custom_type, source_type, resource_md5 , info_card_type, live_card_detail
		FROM
			dec_info
		JOIN 
			decorations
		ON
			dec_info.id = decorations.dec_info_id
		WHERE
			uid = ?
			AND typ = ?
		`
		rows, err = s.mysql.QueryxContext(ctx, query, req.uid, req.typ)
		if err != nil {
			log.ErrorWithCtx(ctx, "mysql err: %v", err)
			return resp, err
		}
	}
	monthDecoration := make(map[string]decorationInfo, 0)
	maxDecoration := make(map[string]string, 0)
	//monthDecoration：配置为即使没有也会显示的装扮； maxDecoration：每种装扮对应的最高等级
	if req.typ == 2 {
		monthDecoration, maxDecoration, err = s.getMonthAndMaxDecoration(req.typ, ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "getMonthAndMaxDecoration err: %v", err)
			return resp, err
		}
	}

	decs := make([]*pb.DecInfo, 0, 100)
	for rows.Next() {
		decInfo := &pb.DecInfo{
			Decoration: &pb.Decoration{},
			DecDetail:  &pb.DecDetail{},
			Tim:        &pb.Tim{},
		}
		decString := decStore{}
		decInfoId := uint32(0)
		if req.typ == 2 {
			err = rows.Scan(
				&decInfo.Decoration.Typ,
				&decInfo.Decoration.Id,
				&decInfo.Decoration.Version,
				&decString.Url,
				&decString.Md5,
				&decString.UrlPic,
				&decString.Name,
				&decInfo.Tim.Begin,
				&decInfo.Tim.End,
				&decString.Extend,
				&decString.Rank,
				&decString.IsShow,
			)
		} else {
			err = rows.Scan(
				&decInfoId,
				&decInfo.Decoration.Typ,
				&decInfo.Decoration.Id,
				&decInfo.Decoration.Version,
				&decString.Url,
				&decString.ResourceUrl,
				&decString.Md5,
				&decString.UrlPic,
				&decString.Name,
				&decInfo.Tim.Begin,
				&decInfo.Tim.End,
				&decString.Extend,
				&decString.Rank,
				&decString.IsShow,
				&decInfo.Decoration.FusionTtid,
				&decString.FusionColor,
				&decString.Type,
				&decString.SourceType,
				&decString.ResourceMd5,
				&decString.InfoCardType,
				&decString.LiveCardDetail,
			)
		}

		if err != nil {
			log.ErrorWithCtx(ctx, "mysql err: %v", err)
			return resp, err
		}

		// 如果是房间资料卡且是直播奖励类型，获取额外信息
		if req.typ == int(pb.Type_CHANNEL_INFO_CARD) && decString.InfoCardType == uint32(pb.ChannelInfoCardType_CHANNEL_INFO_CARD_TYPE_LIVE_AWARD) {
			extend, err := s.GetAwardCardExtendByUid(ctx, req.uid, decInfoId)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetAwardCardExtendByUid failed, uid %d err:%s", req.uid, err.Error())
			}

			decInfo.Decoration.LiveAwardInfoCardExtend = &pb.LiveAwardInfoCardExtend{}
			err = json.Unmarshal([]byte(extend.CardExtend), &decInfo.Decoration.LiveAwardInfoCardExtend)
			if err != nil {
				log.ErrorWithCtx(ctx, "json Unmarshal failed, uid %d err:%s", req.uid, err.Error())
			}
		}

		//如果是礼物墙光效，则需要解析序列化的扩展信息，并作一些额外判断
		if req.typ == 2 {
			extend := &pb.WallExtend{}
			extendString, err := base64.StdEncoding.DecodeString(decString.Extend)
			if err != nil {
				log.ErrorWithCtx(ctx, "base64 err: %v", err)
				return resp, err
			}

			err = proto.Unmarshal(extendString, extend)
			if err != nil {
				log.ErrorWithCtx(ctx, "Unmarshal err: %v", err)
				return resp, err
			}
			decInfo.DecDetail.WallExtend = extend
			//判断是否是最高等级
			if maxDecoration[decInfo.Decoration.Id] == decInfo.Decoration.Version {
				decInfo.DecDetail.StatusType = pb.StatusType_MAX
			} else {
				decInfo.DecDetail.StatusType = pb.StatusType_NORMAL
			}

			//判断是否是需要展示的特效，如果是，代表用户已拥有此光效，从待展示的列表里去掉
			_, ok := monthDecoration[decInfo.Decoration.Id]
			if ok {
				delete(monthDecoration, decInfo.Decoration.Id)
			}
		}

		decInfo.DecDetail.Name = decString.Name
		decInfo.DecDetail.Url = decString.Url
		decInfo.DecDetail.ResourceUrl = decString.ResourceUrl
		decInfo.DecDetail.Md5 = decString.Md5
		decInfo.DecDetail.UrlPic = decString.UrlPic
		decInfo.DecDetail.Rank = decString.Rank
		decInfo.DecDetail.Begin = decString.Begin
		decInfo.DecDetail.End = decString.End
		decInfo.DecDetail.FusionColor = decString.FusionColor
		decInfo.DecDetail.Type = pb.DecorationCustomType(decString.Type)
		decInfo.DecDetail.SourceType = pb.DecorationSourceType(decString.SourceType)
		decInfo.DecDetail.Name = transDecName(decInfo.DecDetail.Name)
		decInfo.DecDetail.ResourceMd5 = decString.ResourceMd5
		decInfo.DecDetail.ChannelInfoCardType = pb.ChannelInfoCardType(decString.InfoCardType)
		_ = json.Unmarshal([]byte(decString.LiveCardDetail), &decInfo.DecDetail.LiveAwardInfoCardConfig)

		decs = append(decs, decInfo)
	}

	if err := rows.Err(); err != nil {
		log.ErrorWithCtx(ctx, "mysql err: %v", err)
		return resp, err
	}

	if err := rows.Close(); err != nil {
		log.ErrorWithCtx(ctx, "mysql err: %v", err)
		return resp, err
	}

	//如果是礼物墙特效，要补充一下用户未拥有但设置为可显示的特效类型
	if req.typ == 2 {
		for _, j := range monthDecoration {
			dec := &pb.DecInfo{
				Decoration: &pb.Decoration{},
				DecDetail:  &pb.DecDetail{},
				Tim:        &pb.Tim{},
			}
			dec.Tim.Begin = j.Begin
			dec.Tim.End = j.End
			dec.DecDetail.StatusType = pb.StatusType_NONE
			dec.Decoration.Version = j.Version
			dec.Decoration.Id = j.Id
			dec.Decoration.Typ = pb.Type_WALL
			dec.DecDetail.Rank = j.Rank
			dec.DecDetail.UrlPic = j.UrlPic
			dec.DecDetail.Url = j.Url
			dec.DecDetail.Md5 = j.Md5
			dec.DecDetail.Name = j.Name
			j.Name = transDecName(j.Name)

			extend := &pb.WallExtend{}
			extendString, err := base64.StdEncoding.DecodeString(j.Extend)
			if err != nil {
				log.ErrorWithCtx(ctx, "base64 err: %v", err)
				return resp, err
			}

			err = proto.Unmarshal(extendString, extend)
			if err != nil {
				log.ErrorWithCtx(ctx, "Unmarshal err: %v", err)
				return resp, err
			}
			dec.DecDetail.WallExtend = extend
			decs = append(decs, dec)
		}
	}

	// 根据rank进行排序
	sort.Slice(decs, func(i, j int) bool {
		return decs[i].DecDetail.Rank < decs[j].DecDetail.Rank
	})
	resp.decs = decs

	return resp, nil
}

type decorationInfo struct {
	No          int       `db:"id"`
	Type        int       `db:"typ"`
	Id          string    `db:"decoration_id"`
	Version     string    `db:"version"`
	Url         string    `db:"url"`
	Md5         string    `db:"md5"`
	UrlPic      string    `db:"url_pic"`
	Name        string    `db:"name"`
	IsShow      int       `db:"is_show"`
	Begin       int64     `db:"show_begin"`
	End         int64     `db:"show_end"`
	Rank        uint32    `db:"rank"`
	Extend      string    `db:"extend"`
	UpdateUser  string    `db:"update_user"`
	UpdateTime  time.Time `db:"update_time"`
	ResourceUrl string    `db:"resource_url"`
}

func (s *store) getMonthAndMaxDecoration(typ int, ctx context.Context) (monthDecoration map[string]decorationInfo, maxDecoration map[string]string, err error) {
	monthDecoration = make(map[string]decorationInfo, 0)
	maxDecoration = make(map[string]string, 0)
	list := make([]decorationInfo, 0)
	query := `select b.id,b.typ,b.decoration_id,b.version,b.url,b.md5,b.url_pic,b.name,b.is_show,b.show_begin,b.show_end,b.rank,b.extend
			  from (select decoration_id,max(version) m from dec_info where typ = ? GROUP BY decoration_id) t,dec_info b where t.decoration_id=b.decoration_id and t.m=b.version;`
	err = s.mysql.SelectContext(ctx, &list, query, typ)
	if err != nil {
		return monthDecoration, maxDecoration, err
	}
	for _, i := range list {
		if i.IsShow == 1 && time.Now().Unix() > i.Begin && time.Now().Unix() < i.End {
			log.Errorln("is show :", i, time.Now().Unix())
			monthDecoration[i.Id] = i
		}
		maxDecoration[i.Id] = i.Version
	}
	return monthDecoration, maxDecoration, err
}

// 佩戴主页飘
func (s *store) adorn(ctx context.Context, req *adornReq) (decName string, end int64, resp *adornResp, err error) {

	resp = &adornResp{}
	name := ""
	query := `
	SELECT
	  id, name
	FROM
	  dec_info
	WHERE
	  typ = ?
	  AND decoration_id = ?
	  AND version = ?
	`
	row := s.mysql.QueryRowxContext(ctx, query, req.Typ, req.Id, req.Version)
	if err := row.Err(); err != nil {
		log.ErrorWithCtx(ctx, "adorn err %v %v", req, err)
		return name, end, resp, err
	}
	decInfoId := uint32(0)

	if err := row.Scan(&decInfoId, &name); err != nil {
		log.ErrorWithCtx(ctx, "adorn err %v %v", req, err)
		return name, end, resp, err
	}
	query = `
	SELECT
	  end
	FROM
	  decorations
	WHERE
	  uid = ?
	  AND dec_info_id = ?
	  AND fusion_ttid = ?
	`
	row = s.mysql.QueryRowxContext(ctx, query, req.uid, decInfoId, req.FusionTtid)
	if err := row.Err(); err != nil {
		log.ErrorWithCtx(ctx, "adorn err %v %v", req, err)
		return name, end, resp, err
	}

	if err := row.Scan(&end); err != nil {
		log.ErrorWithCtx(ctx, "adorn err %v %v", req, err)
		return name, end, resp, err
	}

	liveExtend := ""
	if req.Typ == int(pb.Type_CHANNEL_INFO_CARD) {
		extend, err := s.GetAwardCardExtendByUid(ctx, req.uid, decInfoId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAwardCardExtendByUid failed, uid %d err:%s", req.uid, err.Error())
			//return name, end, resp, err
		} else {
			liveExtend = extend.CardExtend
		}
	}

	dur := time.Until(time.Unix(end, 0)) //主页飘到期时间

	key := strconv.Itoa(int(req.uid)) + ":" + strconv.Itoa(req.Typ)
	val := req.Id + ":" + req.Version
	if req.FusionTtid != "" {
		val = val + ":" + req.FusionTtid
	}

	if liveExtend != "" {
		baseExtend := base64.StdEncoding.EncodeToString([]byte(liveExtend))
		val = val + ":" + "json" + baseExtend
	}

	res := s.redis.Set(key, val, dur)
	if err := res.Err(); err != nil {
		return name, end, resp, err
	}

	return name, end, resp, nil
}

// 移除当前佩戴主要飘
func (s *store) remove(ctx context.Context, req *removeReq) (*removeResp, error) {

	resp := &removeResp{}

	key := strconv.Itoa(int(req.uid)) + ":" + strconv.Itoa(req.typ)
	res := s.redis.Del(key)
	if err := res.Err(); err != nil {
		return resp, err
	}

	return resp, nil
}

// 当前佩戴的主页飘
func (s *store) current(ctx context.Context, req *currentReq) (*pb.CurrentResp, error) {

	resp := &pb.CurrentResp{Dec: &pb.Decoration{}, Time: &pb.Tim{}}

	key := strconv.Itoa(int(req.uid)) + ":" + strconv.Itoa(req.typ)
	res := s.redis.Get(key)
	if err := res.Err(); err != nil {
		return resp, err
	}

	val := strings.Split(res.Val(), ":")
	resp.Dec.Typ = pb.Type_INVALID

	/*	if req.typ == 2 {
			resp.Dec.Typ = pb.Type_WALL
		} else if req.typ == 1 {
			resp.Dec.Typ = pb.Type_FLOAT
		} else {
			resp.Dec.Typ = pb.Type_INVALID
		}*/
	if req.typ != 0 {
		resp.Dec.Typ = pb.Type(req.typ)
	}

	resp.Dec.Id = val[0]
	resp.Dec.Version = val[1]
	if len(val) > 2 {
		// 如果val[2]是json字符串，则解析
		if strings.HasPrefix(val[2], "json") {
			extend, _ := base64.StdEncoding.DecodeString(val[2][4:])
			resp.Dec.LiveAwardInfoCardExtend = &pb.LiveAwardInfoCardExtend{}
			err := json.Unmarshal(extend, &resp.Dec.LiveAwardInfoCardExtend)
			if err != nil {
				return resp, err
			}
		} else {
			resp.Dec.FusionTtid = val[2]
		}
	}

	expire, err := s.redis.PTTL(key).Result()
	if err != nil {
		return resp, err
	}
	resp.Time.End = time.Now().Add(expire).Unix()

	return resp, nil
}

// 获取主页飘配置列表
func (s *store) decorations(ctx context.Context, req *pb.DecorationsReq, all bool) (*pb.DecorationsResp, error) {
	resp := &pb.DecorationsResp{}

	query := `
	SELECT
		id, typ, decoration_id, version, name, url, md5, url_pic ,extend ,rank ,is_show ,show_begin ,show_end , update_time, update_user , resource_url,
	       custom_type , default_award_time , source_type , fusion_color, notes, resource_md5, is_del , info_card_type , live_card_detail
	FROM
		dec_info
	WHERE
		typ = ?
	`
	rows, err := s.mysql.QueryxContext(ctx, query, req.GetTyp())
	if err != nil {
		return resp, err
	}
	defer func() {
		_ = rows.Close()
	}()

	decs := make([]*pb.DecorationInfo, 0, 100)
	for rows.Next() {
		dec := &pb.DecorationInfo{
			Decoration: &pb.Decoration{},
			DecDetail:  &pb.DecDetail{},
		}
		decString := decStore{}
		err := rows.Scan(
			&dec.Decoration.IdentityId,
			&dec.Decoration.Typ,
			&dec.Decoration.Id,
			&dec.Decoration.Version,
			&decString.Name,
			&decString.Url,
			&decString.Md5,
			&decString.UrlPic,
			&decString.Extend,
			&decString.Rank,
			&decString.IsShow,
			&decString.Begin,
			&decString.End,
			&decString.UpdateTime,
			&decString.UpdateUser,
			&decString.ResourceUrl,
			&decString.Type,
			&decString.DefaultAwardTime,
			&decString.SourceType,
			&decString.FusionColor,
			&decString.Notes,
			&decString.ResourceMd5,
			&decString.IsDel,
			&decString.InfoCardType,
			&decString.LiveCardDetail,
		)

		if req.Typ == pb.Type_CHANNEL_INFO_CARD && decString.IsDel > 0 {
			if !all {
				continue
			}
		}

		if err != nil {
			log.ErrorWithCtx(ctx, "mysql err: %v", err)
			return resp, err
		}

		//如果是礼物墙光效，则需要解析序列化的扩展信息
		if req.Typ == pb.Type_WALL {
			extend := &pb.WallExtend{}
			extendString, err := base64.StdEncoding.DecodeString(decString.Extend)
			if err != nil {
				log.ErrorWithCtx(ctx, "base64 err: %v", err)
				return resp, err
			}

			err = proto.Unmarshal(extendString, extend)
			if err != nil {
				log.ErrorWithCtx(ctx, "Unmarshal err: %v", err)
				return resp, err
			}
			dec.DecDetail.WallExtend = extend
		}

		dec.DecDetail.Name = decString.Name
		dec.DecDetail.Url = decString.Url
		dec.DecDetail.Md5 = decString.Md5
		dec.DecDetail.UrlPic = decString.UrlPic
		dec.DecDetail.Rank = decString.Rank
		dec.DecDetail.Begin = decString.Begin
		dec.DecDetail.End = decString.End
		dec.DecDetail.UpdateTime = decString.UpdateTime.Unix()
		dec.DecDetail.UpdateUser = decString.UpdateUser
		dec.DecDetail.ResourceUrl = decString.ResourceUrl
		dec.GetDecDetail().Type = pb.DecorationCustomType(decString.Type)
		dec.GetDecDetail().DefaultAwardTime = decString.DefaultAwardTime
		dec.GetDecDetail().FusionColor = decString.FusionColor
		dec.GetDecDetail().SourceType = pb.DecorationSourceType(decString.SourceType)
		dec.GetDecDetail().Notes = decString.Notes
		dec.GetDecDetail().ResourceMd5 = decString.ResourceMd5
		dec.GetDecDetail().ChannelInfoCardType = pb.ChannelInfoCardType(decString.InfoCardType)
		_ = json.Unmarshal([]byte(decString.LiveCardDetail), &dec.DecDetail.LiveAwardInfoCardConfig)

		if decString.IsShow == 1 {
			dec.DecDetail.IsShow = true
		} else {
			dec.DecDetail.IsShow = false
		}

		dec.DecDetail.Name = transDecName(dec.DecDetail.Name)
		decs = append(decs, dec)
	}
	if err := rows.Err(); err != nil {
		log.ErrorWithCtx(ctx, "mysql err: %v", err)
		return resp, err
	}
	resp.DecorationInfos = decs
	return resp, nil
}

// 获取单个装扮的最新配置
func (s *store) getDecorationById(ctx context.Context, id string, typ uint32, version string) (*pb.DecorationInfo, error) {
	resp := &pb.DecorationInfo{}

	query := `
	SELECT
		id, typ, decoration_id, version, name, url, md5, url_pic ,extend ,rank ,is_show ,show_begin ,show_end , update_time, update_user , resource_url,
	       custom_type , default_award_time , source_type , fusion_color, notes, resource_md5, is_del, info_card_type , live_card_detail
	FROM
		dec_info
	WHERE
		typ = ? AND decoration_id = ?
	`
	if version != "" {
		query = query + " AND version = ?"
	}
	var rows *sqlx.Rows
	var err error
	if version == "" {
		rows, err = s.mysql.QueryxContext(ctx, query, typ, id)
	} else {
		rows, err = s.mysql.QueryxContext(ctx, query, typ, id, version)
	}

	if err != nil {
		return resp, err
	}
	defer func() {
		_ = rows.Close()
	}()

	extend := ""
	for rows.Next() {
		resp = &pb.DecorationInfo{
			Decoration: &pb.Decoration{},
			DecDetail:  &pb.DecDetail{},
		}
		decString := decStore{}
		err := rows.Scan(
			&resp.Decoration.IdentityId,
			&resp.Decoration.Typ,
			&resp.Decoration.Id,
			&resp.Decoration.Version,
			&decString.Name,
			&decString.Url,
			&decString.Md5,
			&decString.UrlPic,
			&decString.Extend,
			&decString.Rank,
			&decString.IsShow,
			&decString.Begin,
			&decString.End,
			&decString.UpdateTime,
			&decString.UpdateUser,
			&decString.ResourceUrl,
			&decString.Type,
			&decString.DefaultAwardTime,
			&decString.SourceType,
			&decString.FusionColor,
			&decString.Notes,
			&decString.ResourceMd5,
			&decString.IsDel,
			&resp.DecDetail.ChannelInfoCardType,
			&extend,
		)

		_ = json.Unmarshal([]byte(extend), &resp.DecDetail.LiveAwardInfoCardConfig)
		if err != nil {
			log.ErrorWithCtx(ctx, "mysql err: %v", err)
			return resp, err
		}

		resp.DecDetail.Name = decString.Name
		resp.DecDetail.Url = decString.Url
		resp.DecDetail.Md5 = decString.Md5
		resp.DecDetail.UrlPic = decString.UrlPic
		resp.DecDetail.Rank = decString.Rank
		resp.DecDetail.Begin = decString.Begin
		resp.DecDetail.End = decString.End
		resp.DecDetail.FusionColor = decString.FusionColor
		resp.DecDetail.Type = pb.DecorationCustomType(decString.Type)
		resp.DecDetail.SourceType = pb.DecorationSourceType(decString.SourceType)
		resp.DecDetail.UpdateTime = decString.UpdateTime.Unix()
		resp.DecDetail.UpdateUser = decString.UpdateUser
		resp.DecDetail.ResourceUrl = decString.ResourceUrl
		resp.DecDetail.Notes = decString.Notes
		resp.DecDetail.ResourceMd5 = decString.ResourceMd5
		if decString.IsShow == 1 {
			resp.DecDetail.IsShow = true
		} else {
			resp.DecDetail.IsShow = false
		}

		//如果是礼物墙光效，则需要解析序列化的扩展信息
		if typ == uint32(pb.Type_WALL) {
			extend := &pb.WallExtend{}
			extendString, err := base64.StdEncoding.DecodeString(decString.Extend)
			if err != nil {
				log.ErrorWithCtx(ctx, "base64 err: %v", err)
				return resp, err
			}

			err = proto.Unmarshal(extendString, extend)
			if err != nil {
				log.ErrorWithCtx(ctx, "Unmarshal err: %v", err)
				return resp, err
			}
			resp.DecDetail.WallExtend = extend
		}

	}
	if err := rows.Err(); err != nil {
		log.ErrorWithCtx(ctx, "mysql err: %v", err)
		return resp, err
	}

	return resp, nil
}

// 新加一个主要飘配置
func (s *store) insertDecoration(ctx context.Context, req *pb.InsertDecorationReq) (*pb.InsertDecorationResp, error) {
	resp := &pb.InsertDecorationResp{}
	extendText := ""

	var query string
	if req.DecorationInfo.GetDecoration().Typ == pb.Type_WALL {
		extend := req.GetDecorationInfo().GetDecDetail().GetWallExtend()
		extendProto, _ := proto.Marshal(extend)
		extendText = base64.StdEncoding.EncodeToString(extendProto)

		query = `SELECT typ, decoration_id, MAX(version) version, url, md5, url_pic, name, show_begin, show_end, extend, rank, is_show FROM dec_info WHERE decoration_id = ? and version = ? and typ = ? GROUP BY decoration_id`

		rows, err := s.mysql.QueryxContext(ctx, query, req.DecorationInfo.Decoration.Id, req.DecorationInfo.Decoration.Version, req.DecorationInfo.Decoration.Typ)
		if err != nil {
			log.ErrorWithCtx(ctx, " get decoration info err: %v", err)
			return resp, err
		}
		defer func() {
			_ = rows.Close()
		}()
		if rows.Next() {
			query = `
			UPDATE
				dec_info
			SET
				typ = ?, name = ?, url = ?, md5 = ?, url_pic = ?, extend = ?, rank = ?, is_show = ?, show_begin = ?, show_end = ? , update_user = ? , resource_url = ?
			WHERE
				decoration_id = ? AND version = ? AND typ = ?
			`
			var decInfo = req.GetDecorationInfo()
			_, err = s.mysql.ExecContext(ctx, query,
				decInfo.GetDecoration().GetTyp(),
				decInfo.GetDecDetail().GetName(),
				decInfo.GetDecDetail().GetUrl(),
				decInfo.GetDecDetail().GetMd5(),
				decInfo.GetDecDetail().GetUrlPic(),
				extendText,
				decInfo.GetDecDetail().GetRank(),
				decInfo.GetDecDetail().GetIsShow(),
				decInfo.GetDecDetail().GetBegin(),
				decInfo.GetDecDetail().GetEnd(),
				decInfo.GetDecDetail().GetUpdateUser(),
				decInfo.GetDecDetail().GetResourceUrl(),
				decInfo.GetDecoration().GetId(),
				decInfo.GetDecoration().GetVersion(),
				decInfo.GetDecoration().GetTyp(),
			)
			if err != nil {
				log.ErrorWithCtx(ctx, "mysql err: %v", err)
				return resp, err
			}
			return resp, err
		}
	} else {
		// 主页飘也要处理更新
		query = `SELECT typ, decoration_id, MAX(version) version, url, md5, url_pic, name, show_begin, show_end, extend, rank, is_show FROM dec_info WHERE decoration_id = ? and version = ? and typ = ? GROUP BY decoration_id`
		rows, err := s.mysql.QueryxContext(ctx, query, req.DecorationInfo.Decoration.Id, req.DecorationInfo.Decoration.Version, req.DecorationInfo.Decoration.Typ)
		if err != nil {
			log.ErrorWithCtx(ctx, " get decoration info err: %v", err)
			return resp, err
		}
		defer func() {
			_ = rows.Close()
		}()
		if rows.Next() {
			query = `
			UPDATE
				dec_info
			SET
				typ = ?, name = ?, url = ?, md5 = ?, url_pic = ?, extend = ?, rank = ?, is_show = ?, show_begin = ?, show_end = ? , update_user = ? , resource_url = ?,
			    custom_type = ?, default_award_time  = ?, source_type = ? , fusion_color = ?, notes = ?, resource_md5 = ?, info_card_type = ? , live_card_detail = ?
			WHERE
				decoration_id = ? AND version = ? AND typ = ?
			`
			var decInfo = req.GetDecorationInfo()
			extend, _ := json.Marshal(decInfo.GetDecDetail().GetLiveAwardInfoCardConfig())
			_, err = s.mysql.ExecContext(ctx, query,
				decInfo.GetDecoration().GetTyp(),
				decInfo.GetDecDetail().GetName(),
				decInfo.GetDecDetail().GetUrl(),
				decInfo.GetDecDetail().GetMd5(),
				decInfo.GetDecDetail().GetUrlPic(),
				extendText,
				decInfo.GetDecDetail().GetRank(),
				decInfo.GetDecDetail().GetIsShow(),
				decInfo.GetDecDetail().GetBegin(),
				decInfo.GetDecDetail().GetEnd(),
				decInfo.GetDecDetail().GetUpdateUser(),
				decInfo.GetDecDetail().GetResourceUrl(),
				decInfo.GetDecDetail().GetType(),
				decInfo.GetDecDetail().GetDefaultAwardTime(),
				decInfo.GetDecDetail().GetSourceType(),
				decInfo.GetDecDetail().GetFusionColor(),
				decInfo.GetDecDetail().GetNotes(),
				decInfo.GetDecDetail().GetResourceMd5(),
				decInfo.GetDecDetail().GetChannelInfoCardType(),
				string(extend),
				decInfo.GetDecoration().GetId(),
				decInfo.GetDecoration().GetVersion(),
				decInfo.GetDecoration().GetTyp(),
			)

			if err != nil {
				log.ErrorWithCtx(ctx, "mysql err: %v", err)
				return resp, err
			}
			return resp, err
		}
	}

	query = `
	INSERT INTO
		dec_info (typ, decoration_id, version, name, url, md5, url_pic , extend , rank , is_show , show_begin , show_end, update_user , resource_url,
		          custom_type , default_award_time , source_type , fusion_color, notes, resource_md5, info_card_type, live_card_detail)
	VALUES
		(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ,?, ?, ?,?,?,?,?,?,?)
	`
	var decInfo = req.GetDecorationInfo()
	extend, _ := json.Marshal(decInfo.GetDecDetail().GetLiveAwardInfoCardConfig())

	_, err := s.mysql.ExecContext(ctx, query,
		decInfo.GetDecoration().GetTyp(),
		decInfo.GetDecoration().GetId(),
		decInfo.GetDecoration().GetVersion(),
		decInfo.GetDecDetail().GetName(),
		decInfo.GetDecDetail().GetUrl(),
		decInfo.GetDecDetail().GetMd5(),
		decInfo.GetDecDetail().GetUrlPic(),
		extendText,
		decInfo.GetDecDetail().GetRank(),
		decInfo.GetDecDetail().GetIsShow(),
		decInfo.GetDecDetail().GetBegin(),
		decInfo.GetDecDetail().GetEnd(),
		decInfo.GetDecDetail().GetUpdateUser(),
		decInfo.GetDecDetail().GetResourceUrl(),
		decInfo.GetDecDetail().GetType(),
		decInfo.GetDecDetail().GetDefaultAwardTime(),
		decInfo.GetDecDetail().GetSourceType(),
		decInfo.GetDecDetail().GetFusionColor(),
		decInfo.GetDecDetail().GetNotes(),
		decInfo.GetDecDetail().GetResourceMd5(),
		decInfo.GetDecDetail().GetChannelInfoCardType(),
		string(extend),
	)
	if err != nil {
		log.ErrorWithCtx(ctx, "mysql err: %v", err)
		return resp, err
	}
	return resp, err
}

func (s *store) DelDecoration(ctx context.Context, req *pb.DelDecorationReq) (*pb.DelDecorationResp, error) {
	resp := &pb.DelDecorationResp{}
	query := `
	Delete FROM
		dec_info
	WHERE
		typ = ? AND decoration_id = ? AND version = ?
	`

	if req.GetDecoration().GetTyp() == pb.Type_CHANNEL_INFO_CARD {
		query = "update dec_info set is_del = 1 where typ = ? AND decoration_id = ? AND version = ?"
	}

	_, err := s.mysql.ExecContext(ctx, query, req.Decoration.Typ, req.Decoration.Id, req.Decoration.Version)
	if err != nil {
		log.ErrorWithCtx(ctx, "Del Decoration mysql err: %v", err)
		return resp, err
	}
	return resp, nil
}

func getUpsertNxKey(uid uint32, decId string) string {
	return fmt.Sprintf("upsert_nx_%d_%s", uid, decId)
}

func Retry(ctx context.Context, attempts int, sleep time.Duration, fn func() error) error {
	if err := fn(); err != nil {
		if s, ok := err.(stop); ok {
			return s.error
		}

		if attempts--; attempts > 0 {
			log.ErrorWithCtx(ctx, "retry func error: %s. attemps #%d after %s.", err.Error(), attempts, sleep)
			time.Sleep(sleep)
			return Retry(ctx, attempts, sleep, fn)
		}
		return err
	}
	return nil
}

type stop struct {
	error
}

func NoRetryError(err error) stop {
	return stop{err}
}
