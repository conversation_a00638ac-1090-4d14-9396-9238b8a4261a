package conf

import (
	"encoding/json"
	"io/ioutil"
	"os"
	"strings"
	"sync"
	"time"

	"golang.52tt.com/pkg/files"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
)

var env string

const DyconfigPath = "/data/oss/conf-center/tt/head_dynamic_image.json"

func init() {
	var MY_CLUSTER = os.Getenv("MY_CLUSTER")
	if strings.Contains(MY_CLUSTER, "prod") {
		env = "prod"
	}
	if strings.Contains(MY_CLUSTER, "test") {
		env = "test"
	}
	if strings.Contains(MY_CLUSTER, "dev") {
		env = "dev"
	}
}

type sConfObj struct {
	ShortTimeOutMs uint32 `json:"short_time_out_ms"`
	FailUids       []uint32
	FailUidMap     map[uint32]bool
	AutoPassUids   []uint32
	AutoPassUidMap map[uint32]bool

	AvatarHostMap map[string]string

	ViolationnoticeUrl string
}

type SDyConfigHandler struct {
	confCenterPath string

	confStrLck sync.RWMutex
	confObj    *sConfObj
}

func NewConfigHandler(confPath string) *SDyConfigHandler {
	tmpConf := &sConfObj{}
	return &SDyConfigHandler{confCenterPath: confPath, confStrLck: sync.RWMutex{}, confObj: tmpConf}
}

func (s *SDyConfigHandler) Start() (err error) {

	err = s.loadConf(s.confCenterPath)
	if nil != err {
		log.Errorf("load conf failed, confPath: %s, err: %s", s.confCenterPath, err)
		return err
	}

	go files.NewFileModifyWatch(s.confCenterPath, time.Second).Start(func() {
		_ = s.loadConf(s.confCenterPath)
	})

	return nil
}

func (s *SDyConfigHandler) loadConf(path string) (err error) {
	defer func() {
		if sEx := recover(); nil != sEx {
			log.Errorf("load filePath: %s, catch ex: %s", path, sEx)
			err = sEx.(error)
		}
	}()

	buffer, err := ioutil.ReadFile(path)
	if err != nil {
		log.Errorf("genPushConfig read file:%s failed, err: %s", path, err)
		return err
	}

	if 0 != len(buffer) {
		s.confStrLck.Lock()
		defer s.confStrLck.Unlock()

		tmpConf := sConfObj{}
		err = json.Unmarshal(buffer, &tmpConf)
		if nil != err {
			log.Errorf("load conf: %s failed, str: %s", path, string(buffer))
		} else {
			s.confObj = &tmpConf

			s.confObj.FailUidMap = map[uint32]bool{}
			for _, uid := range s.confObj.FailUids {
				s.confObj.FailUidMap[uid] = true
			}
			s.confObj.AutoPassUidMap = map[uint32]bool{}
			for _, uid := range s.confObj.AutoPassUids {
				s.confObj.AutoPassUidMap[uid] = true
			}

			log.Infof("dyconfig change %s", utils.ToJson(s.confObj))
		}
	} else {
		log.Errorf("loadConf read file: %s empty", path)
		return protocol.NewExactServerError(nil, status.ErrKeyNotFound, status.MessageFromCode(status.ErrKeyNotFound))
	}

	return
}

func (s *SDyConfigHandler) GetShortTimeOutMs() time.Duration {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()

	if s.confObj.ShortTimeOutMs == 0 {
		return time.Duration(500) * time.Millisecond
	}
	return time.Duration(s.confObj.ShortTimeOutMs) * time.Millisecond
}

func (s *SDyConfigHandler) CheckIsFailUid(uid uint32) bool {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()
	return s.confObj.FailUidMap[uid]
}

func (s *SDyConfigHandler) CheckIsAutoPassUid(uid uint32) bool {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()
	return s.confObj.AutoPassUidMap[uid]
}

func (s *SDyConfigHandler) GetAvatarHost() string {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()

	if host, ok := s.confObj.AvatarHostMap[env]; ok && host != "" {
		return host
	}
	return "avatar.52tt.com"
}

func (s *SDyConfigHandler) GetViolationnoticeUrl() string {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()

	if s.confObj.ViolationnoticeUrl == "" {
		return "https://testing-avatar.ttyuyin.com/v2/head_dynamic_image/ver-177286d707a5a482/small"
	}

	return s.confObj.ViolationnoticeUrl
}
