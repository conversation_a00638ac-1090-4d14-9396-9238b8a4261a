
CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_00 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_01 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_02 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_03 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_04 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_05 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_06 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_07 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_08 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_09 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_10 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_11 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_12 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_13 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_14 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_15 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_16 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_17 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_18 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_19 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_20 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_21 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_22 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_23 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_24 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_25 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_26 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_27 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_28 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_29 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_30 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_31 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_32 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_33 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_34 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_35 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_36 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_37 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_38 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_39 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_40 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_41 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_42 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_43 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_44 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_45 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_46 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_47 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_48 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_49 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_50 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_51 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_52 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_53 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_54 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_55 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_56 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_57 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_58 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_59 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_60 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_61 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_62 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_63 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_64 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_65 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_66 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_67 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_68 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_69 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_70 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_71 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_72 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_73 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_74 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_75 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_76 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_77 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_78 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_79 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_80 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_81 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_82 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_83 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_84 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_85 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_86 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_87 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_88 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_89 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_90 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_91 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_92 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_93 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_94 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_95 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_96 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_97 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_98 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';


CREATE TABLE IF NOT EXISTS tbl_head_dynamic_account_md5_99 (
	account varchar(64) NOT NULL DEFAULT '' COMMENT 'account',
	md5 varchar(64) NOT NULL DEFAULT '' COMMENT 'md5',
	uid int unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
	audit_status int unsigned NOT NULL DEFAULT '0' COMMENT '审核状态',
	create_time int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
	PRIMARY KEY (uid),
	KEY idx_account (account),
	KEY idx_create_time (create_time)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '动态头像md5表';

