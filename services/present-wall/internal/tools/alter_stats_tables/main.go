package main

import (
	"context"
	"fmt"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"time"
)

func NewMysql() (mysql.DBx, error) {
	//mysqlConf := &mysqlConnect.MysqlConfig{
	//	Host:     "************",
	//	Port:     3306,
	//	Database: "present_wall",
	//	Charset:  "utf8",
	//	UserName: "present_wall_rw",
	//	Password: "2viB8APqj9*O8V6X",
	//}
	mysqlConf := &mysqlConnect.MysqlConfig{
		//Host:     "************", // 线上
		Host:     "************", // 云测
		Port:     3306,
		Database: "appsvr",
		Charset:  "utf8mb4",
		UserName: "godman",
		Password: "thegodofman",
	}

	ctx := context.Background()
	db, err := mysqlConnect.NewClient(ctx, mysqlConf)
	if err != nil {
		panic(err)
	}
	return db, err
}

func main() {
	//db, err := NewMysql()
	//if err != nil {
	//	panic(err)
	//}
	//ctx := context.Background()

	startTime := time.Date(2024, 4, 1, 0, 0, 0, 0, time.Local)
	now := time.Now()
	nextMonth := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, time.Local)
	for startTime.Before(nextMonth) {
		// 增加stats表字段
		for i := 0; i < 100; i++ {
			tableName := getStatsTableName(startTime, uint32(i))
			query := fmt.Sprintf(alterStatsSql, tableName)
			fmt.Println(query)
			//_, err := db.Exec(query)
			//if err != nil {
			//	log.ErrorWithCtx(ctx, "%s err %v", query, err)
			//}
		}
		// 增加ukw_stats表字段
		tempDate := time.Unix(startTime.Unix(), 0)
		temp := time.Date(startTime.Year(), startTime.Month()+1, 1, 0, 0, 0, 0, time.Local)
		nextDate := time.Date(tempDate.Year(), tempDate.Month(), tempDate.Day()+1, 0, 0, 0, 0, time.Local)
		for nextDate.Day() != tempDate.Day() && tempDate.Before(temp) {
			tableName := getUKWStatsTableName(tempDate)
			query := fmt.Sprintf(alterUKWStatsSql, tableName)
			//_, err := db.Exec(query)
			//if err != nil {
			//	log.ErrorWithCtx(ctx, "%s err %v", query, err)
			//}
			fmt.Println(query)
			tempDate = nextDate
			nextDate = time.Date(tempDate.Year(), tempDate.Month(), tempDate.Day()+1, 0, 0, 0, 0, time.Local)
		}
		startTime = temp
	}
}

func getStatsTableName(ts time.Time, uid uint32) string {
	return fmt.Sprintf("present_wall_stats_%d%02d_%02d", ts.Year(), ts.Month(), uid%100)
}

var alterStatsSql = "ALTER TABLE %+v ADD COLUMN `item_type` int(10) unsigned NOT NULL DEFAULT 1 COMMENT '礼物类型：1-普通礼物，2-帝王套', ADD UNIQUE INDEX `uniq_index_new` (`to_uid`, `date_ts`, `item_id`, `item_type`, `from_uid`), DROP INDEX `unique_index`;"

func getUKWStatsTableName(ts time.Time) string {
	return fmt.Sprintf("present_wall_stats_ukw_%d%02d%02d", ts.Year(), ts.Month(), ts.Day())
}

var alterUKWStatsSql = "ALTER TABLE %+v ADD COLUMN `item_type` int(10) unsigned NOT NULL DEFAULT 1 COMMENT '礼物类型：1-普通礼物，2-帝王套';"
