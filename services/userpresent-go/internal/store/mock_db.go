// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/userpresent-go/internal/store (interfaces: IStore,ISceneStore,IDetailStore)

// Package store is a generated GoMock package.
package store

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	mysql "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	userpresent_go "golang.52tt.com/protocol/services/userpresent-go"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddChanceItemSource mocks base method.
func (m *MockIStore) AddChanceItemSource(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChanceItemSource", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddChanceItemSource indicates an expected call of AddChanceItemSource.
func (mr *MockIStoreMockRecorder) AddChanceItemSource(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChanceItemSource", reflect.TypeOf((*MockIStore)(nil).AddChanceItemSource), arg0, arg1, arg2)
}

// AddDynamicEffectTemplate mocks base method.
func (m *MockIStore) AddDynamicEffectTemplate(arg0 context.Context, arg1 *userpresent_go.AddDynamicEffectTemplateReq) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddDynamicEffectTemplate", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddDynamicEffectTemplate indicates an expected call of AddDynamicEffectTemplate.
func (mr *MockIStoreMockRecorder) AddDynamicEffectTemplate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddDynamicEffectTemplate", reflect.TypeOf((*MockIStore)(nil).AddDynamicEffectTemplate), arg0, arg1)
}

// AddMark mocks base method.
func (m *MockIStore) AddMark(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMark", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddMark indicates an expected call of AddMark.
func (mr *MockIStoreMockRecorder) AddMark(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMark", reflect.TypeOf((*MockIStore)(nil).AddMark), arg0, arg1, arg2)
}

// AddPresentBaseConfig mocks base method.
func (m *MockIStore) AddPresentBaseConfig(arg0 context.Context, arg1 mysql.Txx, arg2 *PresentBaseConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentBaseConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddPresentBaseConfig indicates an expected call of AddPresentBaseConfig.
func (mr *MockIStoreMockRecorder) AddPresentBaseConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentBaseConfig", reflect.TypeOf((*MockIStore)(nil).AddPresentBaseConfig), arg0, arg1, arg2)
}

// AddPresentConfig mocks base method.
func (m *MockIStore) AddPresentConfig(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.StPresentItemConfig) (*userpresent_go.StPresentItemConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(*userpresent_go.StPresentItemConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentConfig indicates an expected call of AddPresentConfig.
func (mr *MockIStoreMockRecorder) AddPresentConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentConfig", reflect.TypeOf((*MockIStore)(nil).AddPresentConfig), arg0, arg1, arg2)
}

// AddPresentConfigLog mocks base method.
func (m *MockIStore) AddPresentConfigLog(arg0 context.Context, arg1 mysql.Txx, arg2 *PresentConfigLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentConfigLog", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddPresentConfigLog indicates an expected call of AddPresentConfigLog.
func (mr *MockIStoreMockRecorder) AddPresentConfigLog(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentConfigLog", reflect.TypeOf((*MockIStore)(nil).AddPresentConfigLog), arg0, arg1, arg2)
}

// AddPresentConfigNew mocks base method.
func (m *MockIStore) AddPresentConfigNew(arg0 context.Context, arg1 *userpresent_go.AddPresentConfigReq) (*userpresent_go.StPresentItemConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentConfigNew", arg0, arg1)
	ret0, _ := ret[0].(*userpresent_go.StPresentItemConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentConfigNew indicates an expected call of AddPresentConfigNew.
func (mr *MockIStoreMockRecorder) AddPresentConfigNew(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentConfigNew", reflect.TypeOf((*MockIStore)(nil).AddPresentConfigNew), arg0, arg1)
}

// AddPresentEffectConfig mocks base method.
func (m *MockIStore) AddPresentEffectConfig(arg0 context.Context, arg1 mysql.Txx, arg2 *PresentEffectConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentEffectConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddPresentEffectConfig indicates an expected call of AddPresentEffectConfig.
func (mr *MockIStoreMockRecorder) AddPresentEffectConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentEffectConfig", reflect.TypeOf((*MockIStore)(nil).AddPresentEffectConfig), arg0, arg1, arg2)
}

// AddPresentEffectTemplateConfig mocks base method.
func (m *MockIStore) AddPresentEffectTemplateConfig(arg0 context.Context, arg1 *userpresent_go.AddPresentEffectTemplateConfigReq) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentEffectTemplateConfig", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentEffectTemplateConfig indicates an expected call of AddPresentEffectTemplateConfig.
func (mr *MockIStoreMockRecorder) AddPresentEffectTemplateConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentEffectTemplateConfig", reflect.TypeOf((*MockIStore)(nil).AddPresentEffectTemplateConfig), arg0, arg1)
}

// AddPresentEnterConfig mocks base method.
func (m *MockIStore) AddPresentEnterConfig(arg0 context.Context, arg1 mysql.Txx, arg2 *PresentEnterConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentEnterConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddPresentEnterConfig indicates an expected call of AddPresentEnterConfig.
func (mr *MockIStoreMockRecorder) AddPresentEnterConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentEnterConfig", reflect.TypeOf((*MockIStore)(nil).AddPresentEnterConfig), arg0, arg1, arg2)
}

// AddPresentFlowConfig mocks base method.
func (m *MockIStore) AddPresentFlowConfig(arg0 context.Context, arg1 *userpresent_go.StPresentFlowConfig) (*userpresent_go.StPresentFlowConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentFlowConfig", arg0, arg1)
	ret0, _ := ret[0].(*userpresent_go.StPresentFlowConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentFlowConfig indicates an expected call of AddPresentFlowConfig.
func (mr *MockIStoreMockRecorder) AddPresentFlowConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentFlowConfig", reflect.TypeOf((*MockIStore)(nil).AddPresentFlowConfig), arg0, arg1)
}

// BatchAddMark mocks base method.
func (m *MockIStore) BatchAddMark(arg0 context.Context, arg1 []PresentMark) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddMark", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchAddMark indicates an expected call of BatchAddMark.
func (mr *MockIStoreMockRecorder) BatchAddMark(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddMark", reflect.TypeOf((*MockIStore)(nil).BatchAddMark), arg0, arg1)
}

// BatchGetPresentConfigByIdList mocks base method.
func (m *MockIStore) BatchGetPresentConfigByIdList(arg0 context.Context, arg1 mysql.Txx, arg2 []uint32) ([]*userpresent_go.StPresentItemConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPresentConfigByIdList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*userpresent_go.StPresentItemConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPresentConfigByIdList indicates an expected call of BatchGetPresentConfigByIdList.
func (mr *MockIStoreMockRecorder) BatchGetPresentConfigByIdList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPresentConfigByIdList", reflect.TypeOf((*MockIStore)(nil).BatchGetPresentConfigByIdList), arg0, arg1, arg2)
}

// BatchReceivePresent mocks base method.
func (m *MockIStore) BatchReceivePresent(arg0 context.Context, arg1 mysql.Txx, arg2 []*SendPresentReqWithPrice, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchReceivePresent", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchReceivePresent indicates an expected call of BatchReceivePresent.
func (mr *MockIStoreMockRecorder) BatchReceivePresent(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchReceivePresent", reflect.TypeOf((*MockIStore)(nil).BatchReceivePresent), arg0, arg1, arg2, arg3)
}

// BatchRecordPresentMonthlyHistory mocks base method.
func (m *MockIStore) BatchRecordPresentMonthlyHistory(arg0 context.Context, arg1 mysql.Txx, arg2 []*SendPresentReqWithPrice) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchRecordPresentMonthlyHistory", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchRecordPresentMonthlyHistory indicates an expected call of BatchRecordPresentMonthlyHistory.
func (mr *MockIStoreMockRecorder) BatchRecordPresentMonthlyHistory(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRecordPresentMonthlyHistory", reflect.TypeOf((*MockIStore)(nil).BatchRecordPresentMonthlyHistory), arg0, arg1, arg2)
}

// BatchSendPresent mocks base method.
func (m *MockIStore) BatchSendPresent(arg0 context.Context, arg1 mysql.Txx, arg2 []*SendPresentReqWithPrice, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSendPresent", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSendPresent indicates an expected call of BatchSendPresent.
func (mr *MockIStoreMockRecorder) BatchSendPresent(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSendPresent", reflect.TypeOf((*MockIStore)(nil).BatchSendPresent), arg0, arg1, arg2, arg3)
}

// Close mocks base method.
func (m *MockIStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// CreateChanceItemSourceTable mocks base method.
func (m *MockIStore) CreateChanceItemSourceTable(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChanceItemSourceTable", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateChanceItemSourceTable indicates an expected call of CreateChanceItemSourceTable.
func (mr *MockIStoreMockRecorder) CreateChanceItemSourceTable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChanceItemSourceTable", reflect.TypeOf((*MockIStore)(nil).CreateChanceItemSourceTable), arg0)
}

// CreateMarkTable mocks base method.
func (m *MockIStore) CreateMarkTable(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMarkTable", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateMarkTable indicates an expected call of CreateMarkTable.
func (mr *MockIStoreMockRecorder) CreateMarkTable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMarkTable", reflect.TypeOf((*MockIStore)(nil).CreateMarkTable), arg0)
}

// CreatePresentBaseConfigTable mocks base method.
func (m *MockIStore) CreatePresentBaseConfigTable(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePresentBaseConfigTable", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePresentBaseConfigTable indicates an expected call of CreatePresentBaseConfigTable.
func (mr *MockIStoreMockRecorder) CreatePresentBaseConfigTable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePresentBaseConfigTable", reflect.TypeOf((*MockIStore)(nil).CreatePresentBaseConfigTable), arg0)
}

// CreatePresentConfigLogTable mocks base method.
func (m *MockIStore) CreatePresentConfigLogTable(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePresentConfigLogTable", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePresentConfigLogTable indicates an expected call of CreatePresentConfigLogTable.
func (mr *MockIStoreMockRecorder) CreatePresentConfigLogTable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePresentConfigLogTable", reflect.TypeOf((*MockIStore)(nil).CreatePresentConfigLogTable), arg0)
}

// CreatePresentEffectConfigTable mocks base method.
func (m *MockIStore) CreatePresentEffectConfigTable(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePresentEffectConfigTable", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePresentEffectConfigTable indicates an expected call of CreatePresentEffectConfigTable.
func (mr *MockIStoreMockRecorder) CreatePresentEffectConfigTable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePresentEffectConfigTable", reflect.TypeOf((*MockIStore)(nil).CreatePresentEffectConfigTable), arg0)
}

// CreatePresentEnterConfigTable mocks base method.
func (m *MockIStore) CreatePresentEnterConfigTable(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePresentEnterConfigTable", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePresentEnterConfigTable indicates an expected call of CreatePresentEnterConfigTable.
func (mr *MockIStoreMockRecorder) CreatePresentEnterConfigTable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePresentEnterConfigTable", reflect.TypeOf((*MockIStore)(nil).CreatePresentEnterConfigTable), arg0)
}

// CreatePresentMarkIconTable mocks base method.
func (m *MockIStore) CreatePresentMarkIconTable(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePresentMarkIconTable", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePresentMarkIconTable indicates an expected call of CreatePresentMarkIconTable.
func (mr *MockIStoreMockRecorder) CreatePresentMarkIconTable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePresentMarkIconTable", reflect.TypeOf((*MockIStore)(nil).CreatePresentMarkIconTable), arg0)
}

// CreatePresentMonthlyTable mocks base method.
func (m *MockIStore) CreatePresentMonthlyTable(arg0 context.Context, arg1, arg2 int) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CreatePresentMonthlyTable", arg0, arg1, arg2)
}

// CreatePresentMonthlyTable indicates an expected call of CreatePresentMonthlyTable.
func (mr *MockIStoreMockRecorder) CreatePresentMonthlyTable(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePresentMonthlyTable", reflect.TypeOf((*MockIStore)(nil).CreatePresentMonthlyTable), arg0, arg1, arg2)
}

// CreatePresentTicketMonthlyTable mocks base method.
func (m *MockIStore) CreatePresentTicketMonthlyTable(arg0 context.Context, arg1, arg2 int) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CreatePresentTicketMonthlyTable", arg0, arg1, arg2)
}

// CreatePresentTicketMonthlyTable indicates an expected call of CreatePresentTicketMonthlyTable.
func (mr *MockIStoreMockRecorder) CreatePresentTicketMonthlyTable(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePresentTicketMonthlyTable", reflect.TypeOf((*MockIStore)(nil).CreatePresentTicketMonthlyTable), arg0, arg1, arg2)
}

// DelPresentEffectTemplateByTemplateId mocks base method.
func (m *MockIStore) DelPresentEffectTemplateByTemplateId(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPresentEffectTemplateByTemplateId", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelPresentEffectTemplateByTemplateId indicates an expected call of DelPresentEffectTemplateByTemplateId.
func (mr *MockIStoreMockRecorder) DelPresentEffectTemplateByTemplateId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentEffectTemplateByTemplateId", reflect.TypeOf((*MockIStore)(nil).DelPresentEffectTemplateByTemplateId), arg0, arg1)
}

// DeleteChanceItemSource mocks base method.
func (m *MockIStore) DeleteChanceItemSource(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteChanceItemSource", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteChanceItemSource indicates an expected call of DeleteChanceItemSource.
func (mr *MockIStoreMockRecorder) DeleteChanceItemSource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteChanceItemSource", reflect.TypeOf((*MockIStore)(nil).DeleteChanceItemSource), arg0, arg1)
}

// DeleteDynamicEffectTemplate mocks base method.
func (m *MockIStore) DeleteDynamicEffectTemplate(arg0 context.Context, arg1 *userpresent_go.DelDynamicEffectTemplateReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteDynamicEffectTemplate", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteDynamicEffectTemplate indicates an expected call of DeleteDynamicEffectTemplate.
func (mr *MockIStoreMockRecorder) DeleteDynamicEffectTemplate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteDynamicEffectTemplate", reflect.TypeOf((*MockIStore)(nil).DeleteDynamicEffectTemplate), arg0, arg1)
}

// DeleteMark mocks base method.
func (m *MockIStore) DeleteMark(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteMark", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteMark indicates an expected call of DeleteMark.
func (mr *MockIStoreMockRecorder) DeleteMark(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMark", reflect.TypeOf((*MockIStore)(nil).DeleteMark), arg0, arg1)
}

// DeletePresentBaseConfig mocks base method.
func (m *MockIStore) DeletePresentBaseConfig(arg0 context.Context, arg1 mysql.Txx, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePresentBaseConfig", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePresentBaseConfig indicates an expected call of DeletePresentBaseConfig.
func (mr *MockIStoreMockRecorder) DeletePresentBaseConfig(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePresentBaseConfig", reflect.TypeOf((*MockIStore)(nil).DeletePresentBaseConfig), arg0, arg1, arg2, arg3)
}

// DeletePresentConfig mocks base method.
func (m *MockIStore) DeletePresentConfig(arg0 context.Context, arg1 mysql.Txx, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePresentConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeletePresentConfig indicates an expected call of DeletePresentConfig.
func (mr *MockIStoreMockRecorder) DeletePresentConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePresentConfig", reflect.TypeOf((*MockIStore)(nil).DeletePresentConfig), arg0, arg1, arg2)
}

// DeletePresentConfigNew mocks base method.
func (m *MockIStore) DeletePresentConfigNew(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePresentConfigNew", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePresentConfigNew indicates an expected call of DeletePresentConfigNew.
func (mr *MockIStoreMockRecorder) DeletePresentConfigNew(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePresentConfigNew", reflect.TypeOf((*MockIStore)(nil).DeletePresentConfigNew), arg0, arg1)
}

// DeletePresentEffectConfig mocks base method.
func (m *MockIStore) DeletePresentEffectConfig(arg0 context.Context, arg1 mysql.Txx, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePresentEffectConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePresentEffectConfig indicates an expected call of DeletePresentEffectConfig.
func (mr *MockIStoreMockRecorder) DeletePresentEffectConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePresentEffectConfig", reflect.TypeOf((*MockIStore)(nil).DeletePresentEffectConfig), arg0, arg1, arg2)
}

// DeletePresentEffectTemplateConfig mocks base method.
func (m *MockIStore) DeletePresentEffectTemplateConfig(arg0 context.Context, arg1 *userpresent_go.DelPresentEffectTemplateConfigReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePresentEffectTemplateConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePresentEffectTemplateConfig indicates an expected call of DeletePresentEffectTemplateConfig.
func (mr *MockIStoreMockRecorder) DeletePresentEffectTemplateConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePresentEffectTemplateConfig", reflect.TypeOf((*MockIStore)(nil).DeletePresentEffectTemplateConfig), arg0, arg1)
}

// DeletePresentEnterConfig mocks base method.
func (m *MockIStore) DeletePresentEnterConfig(arg0 context.Context, arg1 mysql.Txx, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePresentEnterConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePresentEnterConfig indicates an expected call of DeletePresentEnterConfig.
func (mr *MockIStoreMockRecorder) DeletePresentEnterConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePresentEnterConfig", reflect.TypeOf((*MockIStore)(nil).DeletePresentEnterConfig), arg0, arg1, arg2)
}

// DeletePresentFlowConfig mocks base method.
func (m *MockIStore) DeletePresentFlowConfig(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePresentFlowConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePresentFlowConfig indicates an expected call of DeletePresentFlowConfig.
func (mr *MockIStoreMockRecorder) DeletePresentFlowConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePresentFlowConfig", reflect.TypeOf((*MockIStore)(nil).DeletePresentFlowConfig), arg0, arg1)
}

// DeletePresentMarkIcon mocks base method.
func (m *MockIStore) DeletePresentMarkIcon(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePresentMarkIcon", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePresentMarkIcon indicates an expected call of DeletePresentMarkIcon.
func (mr *MockIStoreMockRecorder) DeletePresentMarkIcon(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePresentMarkIcon", reflect.TypeOf((*MockIStore)(nil).DeletePresentMarkIcon), arg0, arg1)
}

// GetAllPresentBaseConfig mocks base method.
func (m *MockIStore) GetAllPresentBaseConfig(arg0 context.Context) ([]*PresentBaseConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllPresentBaseConfig", arg0)
	ret0, _ := ret[0].([]*PresentBaseConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllPresentBaseConfig indicates an expected call of GetAllPresentBaseConfig.
func (mr *MockIStoreMockRecorder) GetAllPresentBaseConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllPresentBaseConfig", reflect.TypeOf((*MockIStore)(nil).GetAllPresentBaseConfig), arg0)
}

// GetAllPresentConfig mocks base method.
func (m *MockIStore) GetAllPresentConfig(arg0 context.Context) ([]*userpresent_go.StPresentItemConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllPresentConfig", arg0)
	ret0, _ := ret[0].([]*userpresent_go.StPresentItemConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllPresentConfig indicates an expected call of GetAllPresentConfig.
func (mr *MockIStoreMockRecorder) GetAllPresentConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllPresentConfig", reflect.TypeOf((*MockIStore)(nil).GetAllPresentConfig), arg0)
}

// GetAllPresentConfigLog mocks base method.
func (m *MockIStore) GetAllPresentConfigLog(arg0 context.Context) ([]*PresentConfigLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllPresentConfigLog", arg0)
	ret0, _ := ret[0].([]*PresentConfigLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllPresentConfigLog indicates an expected call of GetAllPresentConfigLog.
func (mr *MockIStoreMockRecorder) GetAllPresentConfigLog(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllPresentConfigLog", reflect.TypeOf((*MockIStore)(nil).GetAllPresentConfigLog), arg0)
}

// GetAllPresentEffectConfig mocks base method.
func (m *MockIStore) GetAllPresentEffectConfig(arg0 context.Context) ([]*PresentEffectConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllPresentEffectConfig", arg0)
	ret0, _ := ret[0].([]*PresentEffectConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllPresentEffectConfig indicates an expected call of GetAllPresentEffectConfig.
func (mr *MockIStoreMockRecorder) GetAllPresentEffectConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllPresentEffectConfig", reflect.TypeOf((*MockIStore)(nil).GetAllPresentEffectConfig), arg0)
}

// GetAllPresentEnterConfig mocks base method.
func (m *MockIStore) GetAllPresentEnterConfig(arg0 context.Context) ([]*PresentEnterConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllPresentEnterConfig", arg0)
	ret0, _ := ret[0].([]*PresentEnterConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllPresentEnterConfig indicates an expected call of GetAllPresentEnterConfig.
func (mr *MockIStoreMockRecorder) GetAllPresentEnterConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllPresentEnterConfig", reflect.TypeOf((*MockIStore)(nil).GetAllPresentEnterConfig), arg0)
}

// GetDynamicEffectTemplateById mocks base method.
func (m *MockIStore) GetDynamicEffectTemplateById(arg0 context.Context, arg1 int64) (*userpresent_go.DynamicEffectTemplate, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDynamicEffectTemplateById", arg0, arg1)
	ret0, _ := ret[0].(*userpresent_go.DynamicEffectTemplate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDynamicEffectTemplateById indicates an expected call of GetDynamicEffectTemplateById.
func (mr *MockIStoreMockRecorder) GetDynamicEffectTemplateById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDynamicEffectTemplateById", reflect.TypeOf((*MockIStore)(nil).GetDynamicEffectTemplateById), arg0, arg1)
}

// GetDynamicEffectTemplateList mocks base method.
func (m *MockIStore) GetDynamicEffectTemplateList(arg0 context.Context, arg1, arg2 uint32, arg3 string) ([]*userpresent_go.DynamicEffectTemplate, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDynamicEffectTemplateList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*userpresent_go.DynamicEffectTemplate)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetDynamicEffectTemplateList indicates an expected call of GetDynamicEffectTemplateList.
func (mr *MockIStoreMockRecorder) GetDynamicEffectTemplateList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDynamicEffectTemplateList", reflect.TypeOf((*MockIStore)(nil).GetDynamicEffectTemplateList), arg0, arg1, arg2, arg3)
}

// GetDynamicEffectTemplateUpdateTime mocks base method.
func (m *MockIStore) GetDynamicEffectTemplateUpdateTime(arg0 context.Context) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDynamicEffectTemplateUpdateTime", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDynamicEffectTemplateUpdateTime indicates an expected call of GetDynamicEffectTemplateUpdateTime.
func (mr *MockIStoreMockRecorder) GetDynamicEffectTemplateUpdateTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDynamicEffectTemplateUpdateTime", reflect.TypeOf((*MockIStore)(nil).GetDynamicEffectTemplateUpdateTime), arg0)
}

// GetLivePresentOrderList mocks base method.
func (m *MockIStore) GetLivePresentOrderList(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) ([]*LivePresentOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLivePresentOrderList", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*LivePresentOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivePresentOrderList indicates an expected call of GetLivePresentOrderList.
func (mr *MockIStoreMockRecorder) GetLivePresentOrderList(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivePresentOrderList", reflect.TypeOf((*MockIStore)(nil).GetLivePresentOrderList), arg0, arg1, arg2, arg3, arg4)
}

// GetMark mocks base method.
func (m *MockIStore) GetMark(arg0 context.Context, arg1 uint32) (PresentMark, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMark", arg0, arg1)
	ret0, _ := ret[0].(PresentMark)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMark indicates an expected call of GetMark.
func (mr *MockIStoreMockRecorder) GetMark(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMark", reflect.TypeOf((*MockIStore)(nil).GetMark), arg0, arg1)
}

// GetMarkList mocks base method.
func (m *MockIStore) GetMarkList(arg0 context.Context) ([]PresentMark, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMarkList", arg0)
	ret0, _ := ret[0].([]PresentMark)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMarkList indicates an expected call of GetMarkList.
func (mr *MockIStoreMockRecorder) GetMarkList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMarkList", reflect.TypeOf((*MockIStore)(nil).GetMarkList), arg0)
}

// GetOrderLogByOrderIds mocks base method.
func (m *MockIStore) GetOrderLogByOrderIds(arg0 context.Context, arg1 []string) ([]*StUserPresentOrderLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderLogByOrderIds", arg0, arg1)
	ret0, _ := ret[0].([]*StUserPresentOrderLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderLogByOrderIds indicates an expected call of GetOrderLogByOrderIds.
func (mr *MockIStoreMockRecorder) GetOrderLogByOrderIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderLogByOrderIds", reflect.TypeOf((*MockIStore)(nil).GetOrderLogByOrderIds), arg0, arg1)
}

// GetPresentBaseConfigByIdList mocks base method.
func (m *MockIStore) GetPresentBaseConfigByIdList(arg0 context.Context, arg1 []uint32) ([]*PresentBaseConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentBaseConfigByIdList", arg0, arg1)
	ret0, _ := ret[0].([]*PresentBaseConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentBaseConfigByIdList indicates an expected call of GetPresentBaseConfigByIdList.
func (mr *MockIStoreMockRecorder) GetPresentBaseConfigByIdList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentBaseConfigByIdList", reflect.TypeOf((*MockIStore)(nil).GetPresentBaseConfigByIdList), arg0, arg1)
}

// GetPresentConfigById mocks base method.
func (m *MockIStore) GetPresentConfigById(arg0 context.Context, arg1 mysql.Txx, arg2 uint32) (*userpresent_go.StPresentItemConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigById", arg0, arg1, arg2)
	ret0, _ := ret[0].(*userpresent_go.StPresentItemConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigById indicates an expected call of GetPresentConfigById.
func (mr *MockIStoreMockRecorder) GetPresentConfigById(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigById", reflect.TypeOf((*MockIStore)(nil).GetPresentConfigById), arg0, arg1, arg2)
}

// GetPresentConfigLogByUpdateTime mocks base method.
func (m *MockIStore) GetPresentConfigLogByUpdateTime(arg0 context.Context, arg1 int64) ([]*PresentConfigLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigLogByUpdateTime", arg0, arg1)
	ret0, _ := ret[0].([]*PresentConfigLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigLogByUpdateTime indicates an expected call of GetPresentConfigLogByUpdateTime.
func (mr *MockIStoreMockRecorder) GetPresentConfigLogByUpdateTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigLogByUpdateTime", reflect.TypeOf((*MockIStore)(nil).GetPresentConfigLogByUpdateTime), arg0, arg1)
}

// GetPresentEffectConfigByIdList mocks base method.
func (m *MockIStore) GetPresentEffectConfigByIdList(arg0 context.Context, arg1 []uint32) ([]*PresentEffectConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentEffectConfigByIdList", arg0, arg1)
	ret0, _ := ret[0].([]*PresentEffectConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentEffectConfigByIdList indicates an expected call of GetPresentEffectConfigByIdList.
func (mr *MockIStoreMockRecorder) GetPresentEffectConfigByIdList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentEffectConfigByIdList", reflect.TypeOf((*MockIStore)(nil).GetPresentEffectConfigByIdList), arg0, arg1)
}

// GetPresentEffectTemplateConfigById mocks base method.
func (m *MockIStore) GetPresentEffectTemplateConfigById(arg0 context.Context, arg1 int64) (*userpresent_go.PresentEffectTemplateConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentEffectTemplateConfigById", arg0, arg1)
	ret0, _ := ret[0].(*userpresent_go.PresentEffectTemplateConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentEffectTemplateConfigById indicates an expected call of GetPresentEffectTemplateConfigById.
func (mr *MockIStoreMockRecorder) GetPresentEffectTemplateConfigById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentEffectTemplateConfigById", reflect.TypeOf((*MockIStore)(nil).GetPresentEffectTemplateConfigById), arg0, arg1)
}

// GetPresentEffectTemplateConfigList mocks base method.
func (m *MockIStore) GetPresentEffectTemplateConfigList(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) ([]*userpresent_go.PresentEffectTemplateConfig, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentEffectTemplateConfigList", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*userpresent_go.PresentEffectTemplateConfig)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPresentEffectTemplateConfigList indicates an expected call of GetPresentEffectTemplateConfigList.
func (mr *MockIStoreMockRecorder) GetPresentEffectTemplateConfigList(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentEffectTemplateConfigList", reflect.TypeOf((*MockIStore)(nil).GetPresentEffectTemplateConfigList), arg0, arg1, arg2, arg3, arg4)
}

// GetPresentEffectTemplateConfigUpdateTime mocks base method.
func (m *MockIStore) GetPresentEffectTemplateConfigUpdateTime(arg0 context.Context) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentEffectTemplateConfigUpdateTime", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentEffectTemplateConfigUpdateTime indicates an expected call of GetPresentEffectTemplateConfigUpdateTime.
func (mr *MockIStoreMockRecorder) GetPresentEffectTemplateConfigUpdateTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentEffectTemplateConfigUpdateTime", reflect.TypeOf((*MockIStore)(nil).GetPresentEffectTemplateConfigUpdateTime), arg0)
}

// GetPresentEnterConfigByIdList mocks base method.
func (m *MockIStore) GetPresentEnterConfigByIdList(arg0 context.Context, arg1 []uint32) ([]*PresentEnterConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentEnterConfigByIdList", arg0, arg1)
	ret0, _ := ret[0].([]*PresentEnterConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentEnterConfigByIdList indicates an expected call of GetPresentEnterConfigByIdList.
func (mr *MockIStoreMockRecorder) GetPresentEnterConfigByIdList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentEnterConfigByIdList", reflect.TypeOf((*MockIStore)(nil).GetPresentEnterConfigByIdList), arg0, arg1)
}

// GetPresentFlowConfigById mocks base method.
func (m *MockIStore) GetPresentFlowConfigById(arg0 context.Context, arg1 uint32) (*userpresent_go.StPresentFlowConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentFlowConfigById", arg0, arg1)
	ret0, _ := ret[0].(*userpresent_go.StPresentFlowConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentFlowConfigById indicates an expected call of GetPresentFlowConfigById.
func (mr *MockIStoreMockRecorder) GetPresentFlowConfigById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigById", reflect.TypeOf((*MockIStore)(nil).GetPresentFlowConfigById), arg0, arg1)
}

// GetPresentFlowConfigList mocks base method.
func (m *MockIStore) GetPresentFlowConfigList(arg0 context.Context) ([]*userpresent_go.StPresentFlowConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentFlowConfigList", arg0)
	ret0, _ := ret[0].([]*userpresent_go.StPresentFlowConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentFlowConfigList indicates an expected call of GetPresentFlowConfigList.
func (mr *MockIStoreMockRecorder) GetPresentFlowConfigList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigList", reflect.TypeOf((*MockIStore)(nil).GetPresentFlowConfigList), arg0)
}

// GetPresentFlowConfigUpdateTime mocks base method.
func (m *MockIStore) GetPresentFlowConfigUpdateTime(arg0 context.Context) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentFlowConfigUpdateTime", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentFlowConfigUpdateTime indicates an expected call of GetPresentFlowConfigUpdateTime.
func (mr *MockIStoreMockRecorder) GetPresentFlowConfigUpdateTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigUpdateTime", reflect.TypeOf((*MockIStore)(nil).GetPresentFlowConfigUpdateTime), arg0)
}

// GetPresentMarkIcon mocks base method.
func (m *MockIStore) GetPresentMarkIcon(arg0 context.Context, arg1 uint32) (PresentMarkIcon, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentMarkIcon", arg0, arg1)
	ret0, _ := ret[0].(PresentMarkIcon)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentMarkIcon indicates an expected call of GetPresentMarkIcon.
func (mr *MockIStoreMockRecorder) GetPresentMarkIcon(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentMarkIcon", reflect.TypeOf((*MockIStore)(nil).GetPresentMarkIcon), arg0, arg1)
}

// GetPresentMarkIconList mocks base method.
func (m *MockIStore) GetPresentMarkIconList(arg0 context.Context) ([]PresentMarkIcon, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentMarkIconList", arg0)
	ret0, _ := ret[0].([]PresentMarkIcon)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentMarkIconList indicates an expected call of GetPresentMarkIconList.
func (mr *MockIStoreMockRecorder) GetPresentMarkIconList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentMarkIconList", reflect.TypeOf((*MockIStore)(nil).GetPresentMarkIconList), arg0)
}

// GetPresentOrderStatus mocks base method.
func (m *MockIStore) GetPresentOrderStatus(arg0 context.Context, arg1 uint32, arg2 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentOrderStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentOrderStatus indicates an expected call of GetPresentOrderStatus.
func (mr *MockIStoreMockRecorder) GetPresentOrderStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentOrderStatus", reflect.TypeOf((*MockIStore)(nil).GetPresentOrderStatus), arg0, arg1, arg2)
}

// GetReadOnlyTx mocks base method.
func (m *MockIStore) GetReadOnlyTx(arg0 context.Context) (mysql.Txx, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReadOnlyTx", arg0)
	ret0, _ := ret[0].(mysql.Txx)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReadOnlyTx indicates an expected call of GetReadOnlyTx.
func (mr *MockIStoreMockRecorder) GetReadOnlyTx(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReadOnlyTx", reflect.TypeOf((*MockIStore)(nil).GetReadOnlyTx), arg0)
}

// GetTx mocks base method.
func (m *MockIStore) GetTx(arg0 context.Context) (mysql.Txx, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTx", arg0)
	ret0, _ := ret[0].(mysql.Txx)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTx indicates an expected call of GetTx.
func (mr *MockIStoreMockRecorder) GetTx(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTx", reflect.TypeOf((*MockIStore)(nil).GetTx), arg0)
}

// GetUserPresentInfo mocks base method.
func (m *MockIStore) GetUserPresentInfo(arg0 context.Context, arg1 mysql.Txx, arg2 uint32) (uint32, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserPresentInfo indicates an expected call of GetUserPresentInfo.
func (mr *MockIStoreMockRecorder) GetUserPresentInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentInfo", reflect.TypeOf((*MockIStore)(nil).GetUserPresentInfo), arg0, arg1, arg2)
}

// GetUserPresentReceive mocks base method.
func (m *MockIStore) GetUserPresentReceive(arg0 context.Context, arg1 *userpresent_go.GetUserPresentReceiveReq) ([]*receivePresent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentReceive", arg0, arg1)
	ret0, _ := ret[0].([]*receivePresent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentReceive indicates an expected call of GetUserPresentReceive.
func (mr *MockIStoreMockRecorder) GetUserPresentReceive(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentReceive", reflect.TypeOf((*MockIStore)(nil).GetUserPresentReceive), arg0, arg1)
}

// GetUserPresentSend mocks base method.
func (m *MockIStore) GetUserPresentSend(arg0 context.Context, arg1 *userpresent_go.GetUserPresentSendReq) ([]*sendPresent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentSend", arg0, arg1)
	ret0, _ := ret[0].([]*sendPresent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSend indicates an expected call of GetUserPresentSend.
func (mr *MockIStoreMockRecorder) GetUserPresentSend(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSend", reflect.TypeOf((*MockIStore)(nil).GetUserPresentSend), arg0, arg1)
}

// GetUserPresentSummary mocks base method.
func (m *MockIStore) GetUserPresentSummary(arg0 context.Context, arg1 mysql.Txx, arg2 uint32, arg3 bool, arg4 []uint32) ([]*userpresent_go.StUserPresentSummary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentSummary", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*userpresent_go.StUserPresentSummary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSummary indicates an expected call of GetUserPresentSummary.
func (mr *MockIStoreMockRecorder) GetUserPresentSummary(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSummary", reflect.TypeOf((*MockIStore)(nil).GetUserPresentSummary), arg0, arg1, arg2, arg3, arg4)
}

// GetUserPresentSummaryById mocks base method.
func (m *MockIStore) GetUserPresentSummaryById(arg0 context.Context, arg1 mysql.Txx, arg2 uint32, arg3 bool, arg4 uint32) (*userpresent_go.StUserPresentSummary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentSummaryById", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*userpresent_go.StUserPresentSummary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSummaryById indicates an expected call of GetUserPresentSummaryById.
func (mr *MockIStoreMockRecorder) GetUserPresentSummaryById(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSummaryById", reflect.TypeOf((*MockIStore)(nil).GetUserPresentSummaryById), arg0, arg1, arg2, arg3, arg4)
}

// ReceivePresent mocks base method.
func (m *MockIStore) ReceivePresent(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.SendPresentReq, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReceivePresent", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReceivePresent indicates an expected call of ReceivePresent.
func (mr *MockIStoreMockRecorder) ReceivePresent(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReceivePresent", reflect.TypeOf((*MockIStore)(nil).ReceivePresent), arg0, arg1, arg2, arg3, arg4)
}

// RecordPresentMonthlyHistory mocks base method.
func (m *MockIStore) RecordPresentMonthlyHistory(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.SendPresentReq, arg3 *userpresent_go.StPresentItemConfig, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordPresentMonthlyHistory", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordPresentMonthlyHistory indicates an expected call of RecordPresentMonthlyHistory.
func (mr *MockIStoreMockRecorder) RecordPresentMonthlyHistory(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordPresentMonthlyHistory", reflect.TypeOf((*MockIStore)(nil).RecordPresentMonthlyHistory), arg0, arg1, arg2, arg3, arg4)
}

// RecordTicketPresentMonthlyHistory mocks base method.
func (m *MockIStore) RecordTicketPresentMonthlyHistory(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.SendPresentReq, arg3 *userpresent_go.StPresentItemConfig, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordTicketPresentMonthlyHistory", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordTicketPresentMonthlyHistory indicates an expected call of RecordTicketPresentMonthlyHistory.
func (mr *MockIStoreMockRecorder) RecordTicketPresentMonthlyHistory(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordTicketPresentMonthlyHistory", reflect.TypeOf((*MockIStore)(nil).RecordTicketPresentMonthlyHistory), arg0, arg1, arg2, arg3, arg4)
}

// SavePresentMarkIcon mocks base method.
func (m *MockIStore) SavePresentMarkIcon(arg0 context.Context, arg1, arg2 uint32, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SavePresentMarkIcon", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SavePresentMarkIcon indicates an expected call of SavePresentMarkIcon.
func (mr *MockIStoreMockRecorder) SavePresentMarkIcon(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SavePresentMarkIcon", reflect.TypeOf((*MockIStore)(nil).SavePresentMarkIcon), arg0, arg1, arg2, arg3)
}

// SendPresent mocks base method.
func (m *MockIStore) SendPresent(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.SendPresentReq, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPresent", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendPresent indicates an expected call of SendPresent.
func (mr *MockIStoreMockRecorder) SendPresent(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPresent", reflect.TypeOf((*MockIStore)(nil).SendPresent), arg0, arg1, arg2, arg3, arg4)
}

// UpdateDynamicEffectTemplate mocks base method.
func (m *MockIStore) UpdateDynamicEffectTemplate(arg0 context.Context, arg1 *userpresent_go.UpdateDynamicEffectTemplateReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDynamicEffectTemplate", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDynamicEffectTemplate indicates an expected call of UpdateDynamicEffectTemplate.
func (mr *MockIStoreMockRecorder) UpdateDynamicEffectTemplate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDynamicEffectTemplate", reflect.TypeOf((*MockIStore)(nil).UpdateDynamicEffectTemplate), arg0, arg1)
}

// UpdatePresentBaseConfig mocks base method.
func (m *MockIStore) UpdatePresentBaseConfig(arg0 context.Context, arg1 mysql.Txx, arg2 *PresentBaseConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresentBaseConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePresentBaseConfig indicates an expected call of UpdatePresentBaseConfig.
func (mr *MockIStoreMockRecorder) UpdatePresentBaseConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentBaseConfig", reflect.TypeOf((*MockIStore)(nil).UpdatePresentBaseConfig), arg0, arg1, arg2)
}

// UpdatePresentConfig mocks base method.
func (m *MockIStore) UpdatePresentConfig(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.StPresentItemConfig) (*userpresent_go.StPresentItemConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresentConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(*userpresent_go.StPresentItemConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentConfig indicates an expected call of UpdatePresentConfig.
func (mr *MockIStoreMockRecorder) UpdatePresentConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentConfig", reflect.TypeOf((*MockIStore)(nil).UpdatePresentConfig), arg0, arg1, arg2)
}

// UpdatePresentConfigNew mocks base method.
func (m *MockIStore) UpdatePresentConfigNew(arg0 context.Context, arg1 *userpresent_go.UpdatePresentConfigReq) (*userpresent_go.StPresentItemConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresentConfigNew", arg0, arg1)
	ret0, _ := ret[0].(*userpresent_go.StPresentItemConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentConfigNew indicates an expected call of UpdatePresentConfigNew.
func (mr *MockIStoreMockRecorder) UpdatePresentConfigNew(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentConfigNew", reflect.TypeOf((*MockIStore)(nil).UpdatePresentConfigNew), arg0, arg1)
}

// UpdatePresentEffectConfig mocks base method.
func (m *MockIStore) UpdatePresentEffectConfig(arg0 context.Context, arg1 mysql.Txx, arg2 *PresentEffectConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresentEffectConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePresentEffectConfig indicates an expected call of UpdatePresentEffectConfig.
func (mr *MockIStoreMockRecorder) UpdatePresentEffectConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentEffectConfig", reflect.TypeOf((*MockIStore)(nil).UpdatePresentEffectConfig), arg0, arg1, arg2)
}

// UpdatePresentEffectTemplateConfig mocks base method.
func (m *MockIStore) UpdatePresentEffectTemplateConfig(arg0 context.Context, arg1 *userpresent_go.UpdatePresentEffectTemplateConfigReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresentEffectTemplateConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePresentEffectTemplateConfig indicates an expected call of UpdatePresentEffectTemplateConfig.
func (mr *MockIStoreMockRecorder) UpdatePresentEffectTemplateConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentEffectTemplateConfig", reflect.TypeOf((*MockIStore)(nil).UpdatePresentEffectTemplateConfig), arg0, arg1)
}

// UpdatePresentEnterConfig mocks base method.
func (m *MockIStore) UpdatePresentEnterConfig(arg0 context.Context, arg1 mysql.Txx, arg2 *PresentEnterConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresentEnterConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePresentEnterConfig indicates an expected call of UpdatePresentEnterConfig.
func (mr *MockIStoreMockRecorder) UpdatePresentEnterConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentEnterConfig", reflect.TypeOf((*MockIStore)(nil).UpdatePresentEnterConfig), arg0, arg1, arg2)
}

// UpdatePresentFlowConfig mocks base method.
func (m *MockIStore) UpdatePresentFlowConfig(arg0 context.Context, arg1 *userpresent_go.StPresentFlowConfig) (*userpresent_go.StPresentFlowConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresentFlowConfig", arg0, arg1)
	ret0, _ := ret[0].(*userpresent_go.StPresentFlowConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentFlowConfig indicates an expected call of UpdatePresentFlowConfig.
func (mr *MockIStoreMockRecorder) UpdatePresentFlowConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentFlowConfig", reflect.TypeOf((*MockIStore)(nil).UpdatePresentFlowConfig), arg0, arg1)
}

// MockISceneStore is a mock of ISceneStore interface.
type MockISceneStore struct {
	ctrl     *gomock.Controller
	recorder *MockISceneStoreMockRecorder
}

// MockISceneStoreMockRecorder is the mock recorder for MockISceneStore.
type MockISceneStoreMockRecorder struct {
	mock *MockISceneStore
}

// NewMockISceneStore creates a new mock instance.
func NewMockISceneStore(ctrl *gomock.Controller) *MockISceneStore {
	mock := &MockISceneStore{ctrl: ctrl}
	mock.recorder = &MockISceneStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISceneStore) EXPECT() *MockISceneStoreMockRecorder {
	return m.recorder
}

// AddNamingPresentInfo mocks base method.
func (m *MockISceneStore) AddNamingPresentInfo(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.NamingPresentInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddNamingPresentInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddNamingPresentInfo indicates an expected call of AddNamingPresentInfo.
func (mr *MockISceneStoreMockRecorder) AddNamingPresentInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNamingPresentInfo", reflect.TypeOf((*MockISceneStore)(nil).AddNamingPresentInfo), arg0, arg1, arg2)
}

// ClearScenePresent mocks base method.
func (m *MockISceneStore) ClearScenePresent(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.StSceneInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearScenePresent", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearScenePresent indicates an expected call of ClearScenePresent.
func (mr *MockISceneStoreMockRecorder) ClearScenePresent(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearScenePresent", reflect.TypeOf((*MockISceneStore)(nil).ClearScenePresent), arg0, arg1, arg2)
}

// Close mocks base method.
func (m *MockISceneStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockISceneStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockISceneStore)(nil).Close))
}

// DelNamingPresentInfo mocks base method.
func (m *MockISceneStore) DelNamingPresentInfo(arg0 context.Context, arg1 mysql.Txx, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelNamingPresentInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelNamingPresentInfo indicates an expected call of DelNamingPresentInfo.
func (mr *MockISceneStoreMockRecorder) DelNamingPresentInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelNamingPresentInfo", reflect.TypeOf((*MockISceneStore)(nil).DelNamingPresentInfo), arg0, arg1, arg2)
}

// GetNamingPresentInfoList mocks base method.
func (m *MockISceneStore) GetNamingPresentInfoList(arg0 context.Context, arg1 mysql.Txx, arg2, arg3, arg4, arg5, arg6, arg7 uint32) ([]*userpresent_go.NamingPresentInfo, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNamingPresentInfoList", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
	ret0, _ := ret[0].([]*userpresent_go.NamingPresentInfo)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetNamingPresentInfoList indicates an expected call of GetNamingPresentInfoList.
func (mr *MockISceneStoreMockRecorder) GetNamingPresentInfoList(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNamingPresentInfoList", reflect.TypeOf((*MockISceneStore)(nil).GetNamingPresentInfoList), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
}

// GetReadOnlyTx mocks base method.
func (m *MockISceneStore) GetReadOnlyTx(arg0 context.Context) (mysql.Txx, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReadOnlyTx", arg0)
	ret0, _ := ret[0].(mysql.Txx)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReadOnlyTx indicates an expected call of GetReadOnlyTx.
func (mr *MockISceneStoreMockRecorder) GetReadOnlyTx(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReadOnlyTx", reflect.TypeOf((*MockISceneStore)(nil).GetReadOnlyTx), arg0)
}

// GetScenePresentInfo mocks base method.
func (m *MockISceneStore) GetScenePresentInfo(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.StSceneInfo) (uint64, uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScenePresentInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(uint64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetScenePresentInfo indicates an expected call of GetScenePresentInfo.
func (mr *MockISceneStoreMockRecorder) GetScenePresentInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScenePresentInfo", reflect.TypeOf((*MockISceneStore)(nil).GetScenePresentInfo), arg0, arg1, arg2)
}

// GetScenePresentSummary mocks base method.
func (m *MockISceneStore) GetScenePresentSummary(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.StSceneInfo) ([]*userpresent_go.StScenePresentSummary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScenePresentSummary", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*userpresent_go.StScenePresentSummary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScenePresentSummary indicates an expected call of GetScenePresentSummary.
func (mr *MockISceneStoreMockRecorder) GetScenePresentSummary(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScenePresentSummary", reflect.TypeOf((*MockISceneStore)(nil).GetScenePresentSummary), arg0, arg1, arg2)
}

// GetTx mocks base method.
func (m *MockISceneStore) GetTx(arg0 context.Context) (mysql.Txx, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTx", arg0)
	ret0, _ := ret[0].(mysql.Txx)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTx indicates an expected call of GetTx.
func (mr *MockISceneStoreMockRecorder) GetTx(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTx", reflect.TypeOf((*MockISceneStore)(nil).GetTx), arg0)
}

// GetValidNamingPresentInfoList mocks base method.
func (m *MockISceneStore) GetValidNamingPresentInfoList(arg0 context.Context) ([]*userpresent_go.NamingPresentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetValidNamingPresentInfoList", arg0)
	ret0, _ := ret[0].([]*userpresent_go.NamingPresentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetValidNamingPresentInfoList indicates an expected call of GetValidNamingPresentInfoList.
func (mr *MockISceneStoreMockRecorder) GetValidNamingPresentInfoList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetValidNamingPresentInfoList", reflect.TypeOf((*MockISceneStore)(nil).GetValidNamingPresentInfoList), arg0)
}

// RecordSceneConsume mocks base method.
func (m *MockISceneStore) RecordSceneConsume(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.StSceneInfo, arg3, arg4, arg5 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordSceneConsume", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordSceneConsume indicates an expected call of RecordSceneConsume.
func (mr *MockISceneStoreMockRecorder) RecordSceneConsume(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordSceneConsume", reflect.TypeOf((*MockISceneStore)(nil).RecordSceneConsume), arg0, arg1, arg2, arg3, arg4, arg5)
}

// RecordSceneSendPresent mocks base method.
func (m *MockISceneStore) RecordSceneSendPresent(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.StSceneInfo, arg3, arg4, arg5 uint32, arg6 *userpresent_go.StPresentItemConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordSceneSendPresent", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordSceneSendPresent indicates an expected call of RecordSceneSendPresent.
func (mr *MockISceneStoreMockRecorder) RecordSceneSendPresent(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordSceneSendPresent", reflect.TypeOf((*MockISceneStore)(nil).RecordSceneSendPresent), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// UpdateNamingPresentInfo mocks base method.
func (m *MockISceneStore) UpdateNamingPresentInfo(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.NamingPresentInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNamingPresentInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateNamingPresentInfo indicates an expected call of UpdateNamingPresentInfo.
func (mr *MockISceneStoreMockRecorder) UpdateNamingPresentInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNamingPresentInfo", reflect.TypeOf((*MockISceneStore)(nil).UpdateNamingPresentInfo), arg0, arg1, arg2)
}

// MockIDetailStore is a mock of IDetailStore interface.
type MockIDetailStore struct {
	ctrl     *gomock.Controller
	recorder *MockIDetailStoreMockRecorder
}

// MockIDetailStoreMockRecorder is the mock recorder for MockIDetailStore.
type MockIDetailStoreMockRecorder struct {
	mock *MockIDetailStore
}

// NewMockIDetailStore creates a new mock instance.
func NewMockIDetailStore(ctrl *gomock.Controller) *MockIDetailStore {
	mock := &MockIDetailStore{ctrl: ctrl}
	mock.recorder = &MockIDetailStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDetailStore) EXPECT() *MockIDetailStoreMockRecorder {
	return m.recorder
}

// BatchGetPresentDealToken mocks base method.
func (m *MockIDetailStore) BatchGetPresentDealToken(arg0 context.Context, arg1 []string) (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPresentDealToken", arg0, arg1)
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPresentDealToken indicates an expected call of BatchGetPresentDealToken.
func (mr *MockIDetailStoreMockRecorder) BatchGetPresentDealToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPresentDealToken", reflect.TypeOf((*MockIDetailStore)(nil).BatchGetPresentDealToken), arg0, arg1)
}

// BatchReceivePresentDetailRecord mocks base method.
func (m *MockIDetailStore) BatchReceivePresentDetailRecord(arg0 context.Context, arg1 mysql.Txx, arg2 []*SendPresentReqWithPrice, arg3 uint32, arg4 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchReceivePresentDetailRecord", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchReceivePresentDetailRecord indicates an expected call of BatchReceivePresentDetailRecord.
func (mr *MockIDetailStoreMockRecorder) BatchReceivePresentDetailRecord(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchReceivePresentDetailRecord", reflect.TypeOf((*MockIDetailStore)(nil).BatchReceivePresentDetailRecord), arg0, arg1, arg2, arg3, arg4)
}

// BatchSendPresentDetailRecord mocks base method.
func (m *MockIDetailStore) BatchSendPresentDetailRecord(arg0 context.Context, arg1 mysql.Txx, arg2 []*SendPresentReqWithPrice, arg3 uint32, arg4 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSendPresentDetailRecord", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSendPresentDetailRecord indicates an expected call of BatchSendPresentDetailRecord.
func (mr *MockIDetailStoreMockRecorder) BatchSendPresentDetailRecord(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSendPresentDetailRecord", reflect.TypeOf((*MockIDetailStore)(nil).BatchSendPresentDetailRecord), arg0, arg1, arg2, arg3, arg4)
}

// Close mocks base method.
func (m *MockIDetailStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIDetailStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIDetailStore)(nil).Close))
}

// CreateDealTokenMonthlyTable mocks base method.
func (m *MockIDetailStore) CreateDealTokenMonthlyTable(arg0 context.Context, arg1, arg2 int) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CreateDealTokenMonthlyTable", arg0, arg1, arg2)
}

// CreateDealTokenMonthlyTable indicates an expected call of CreateDealTokenMonthlyTable.
func (mr *MockIDetailStoreMockRecorder) CreateDealTokenMonthlyTable(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDealTokenMonthlyTable", reflect.TypeOf((*MockIDetailStore)(nil).CreateDealTokenMonthlyTable), arg0, arg1, arg2)
}

// GetReadOnlyTx mocks base method.
func (m *MockIDetailStore) GetReadOnlyTx(arg0 context.Context) (mysql.Txx, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReadOnlyTx", arg0)
	ret0, _ := ret[0].(mysql.Txx)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReadOnlyTx indicates an expected call of GetReadOnlyTx.
func (mr *MockIDetailStoreMockRecorder) GetReadOnlyTx(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReadOnlyTx", reflect.TypeOf((*MockIDetailStore)(nil).GetReadOnlyTx), arg0)
}

// GetTx mocks base method.
func (m *MockIDetailStore) GetTx(arg0 context.Context) (mysql.Txx, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTx", arg0)
	ret0, _ := ret[0].(mysql.Txx)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTx indicates an expected call of GetTx.
func (mr *MockIDetailStoreMockRecorder) GetTx(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTx", reflect.TypeOf((*MockIDetailStore)(nil).GetTx), arg0)
}

// GetUserPresentReceiveDetailList mocks base method.
func (m *MockIDetailStore) GetUserPresentReceiveDetailList(arg0 context.Context, arg1, arg2, arg3 uint32) ([]*userpresent_go.StUserPresentDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentReceiveDetailList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*userpresent_go.StUserPresentDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentReceiveDetailList indicates an expected call of GetUserPresentReceiveDetailList.
func (mr *MockIDetailStoreMockRecorder) GetUserPresentReceiveDetailList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentReceiveDetailList", reflect.TypeOf((*MockIDetailStore)(nil).GetUserPresentReceiveDetailList), arg0, arg1, arg2, arg3)
}

// GetUserPresentSendDetailList mocks base method.
func (m *MockIDetailStore) GetUserPresentSendDetailList(arg0 context.Context, arg1, arg2, arg3 uint32) ([]*userpresent_go.StUserPresentDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentSendDetailList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*userpresent_go.StUserPresentDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSendDetailList indicates an expected call of GetUserPresentSendDetailList.
func (mr *MockIDetailStoreMockRecorder) GetUserPresentSendDetailList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSendDetailList", reflect.TypeOf((*MockIDetailStore)(nil).GetUserPresentSendDetailList), arg0, arg1, arg2, arg3)
}

// ReceivePresentDetailRecord mocks base method.
func (m *MockIDetailStore) ReceivePresentDetailRecord(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.SendPresentReq, arg3 userpresent_go.StPresentItemConfig, arg4 uint32, arg5 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReceivePresentDetailRecord", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReceivePresentDetailRecord indicates an expected call of ReceivePresentDetailRecord.
func (mr *MockIDetailStoreMockRecorder) ReceivePresentDetailRecord(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReceivePresentDetailRecord", reflect.TypeOf((*MockIDetailStore)(nil).ReceivePresentDetailRecord), arg0, arg1, arg2, arg3, arg4, arg5)
}

// RecordPresentDealToken mocks base method.
func (m *MockIDetailStore) RecordPresentDealToken(arg0 context.Context, arg1 mysql.Txx, arg2, arg3 string, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordPresentDealToken", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordPresentDealToken indicates an expected call of RecordPresentDealToken.
func (mr *MockIDetailStoreMockRecorder) RecordPresentDealToken(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordPresentDealToken", reflect.TypeOf((*MockIDetailStore)(nil).RecordPresentDealToken), arg0, arg1, arg2, arg3, arg4)
}

// RecordPresentDealTokens mocks base method.
func (m *MockIDetailStore) RecordPresentDealTokens(arg0 context.Context, arg1 mysql.Txx, arg2 []*DealInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordPresentDealTokens", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordPresentDealTokens indicates an expected call of RecordPresentDealTokens.
func (mr *MockIDetailStoreMockRecorder) RecordPresentDealTokens(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordPresentDealTokens", reflect.TypeOf((*MockIDetailStore)(nil).RecordPresentDealTokens), arg0, arg1, arg2)
}

// SendPresentDetailRecord mocks base method.
func (m *MockIDetailStore) SendPresentDetailRecord(arg0 context.Context, arg1 mysql.Txx, arg2 *userpresent_go.SendPresentReq, arg3 userpresent_go.StPresentItemConfig, arg4 uint32, arg5 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPresentDetailRecord", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendPresentDetailRecord indicates an expected call of SendPresentDetailRecord.
func (mr *MockIDetailStoreMockRecorder) SendPresentDetailRecord(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPresentDetailRecord", reflect.TypeOf((*MockIDetailStore)(nil).SendPresentDetailRecord), arg0, arg1, arg2, arg3, arg4, arg5)
}
