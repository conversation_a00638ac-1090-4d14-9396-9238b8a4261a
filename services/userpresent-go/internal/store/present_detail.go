package store

import (
	"context"
	"fmt"
	"github.com/jmoiron/sqlx"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/log"
	userpresent "golang.52tt.com/protocol/services/userpresent-go"
	"strings"
	"time"
)

func (s *DetailStore) GetReadOnlyTx(ctx context.Context) (tx mysql.Txx, err error) {
	tx, err = s.readDb.Beginx()
	if err != nil {
		log.ErrorWithCtx(ctx, "Transaction Beginx fail err %v", err)
		return
	}

	return
}

func (s *DetailStore) GetTx(ctx context.Context) (tx mysql.Txx, err error) {
	tx, err = s.db.Beginx()
	if err != nil {
		log.ErrorWithCtx(ctx, "Transaction Beginx fail err %v", err)
		return
	}

	return
}

func (s *DetailStore) GetUserPresentReceiveDetailList(ctx context.Context, uid, offset, limit uint32) (list []*userpresent.StUserPresentDetail, err error) {
	list = make([]*userpresent.StUserPresentDetail, 0)

	nowTime := time.Now()
	dateTime := nowTime.Add(-3600 * 24 * 31 * time.Second) // 最多取到31天前的数据

	tmpList := make([]*UserPresentReceiveDetail, 0)
	sql := fmt.Sprintf("SELECT from_uid, item_id, item_count, UNIX_TIMESTAMP(create_time) as create_time, score, add_charm, order_id, send_source, send_method , is_ukw"+
		" FROM user_present_receive_detail_%02d%02d WHERE uid = ? and create_time > ? ORDER BY create_time DESC LIMIT ?, ?", nowTime.Month()-1, uid%100)
	err = s.db.SelectContext(ctx, &tmpList, sql, uid, dateTime, offset, limit)
	if err != nil && !mysql.IsNoRowsError(err) {
		log.ErrorWithCtx(ctx, "GetUserPresentReceiveDetailList err , uid %d, err %v", uid, err)
		return list, err
	}

	for _, item := range tmpList {
		tmp := &userpresent.StUserPresentDetail{
			TargetUid:   uid,
			FromUid:     item.FromUid,
			ItemId:      item.ItemId,
			ItemCount:   item.ItemCount,
			ReceiveTime: item.CreateTime,
			Score:       item.Score,
			AddCharm:    item.AddCharm,
			OrderId:     item.OrderId,
			SendSource:  item.SendSource,
			SendMethod:  item.SendMethod,
			IsUkw:       item.IsUkw,
		}
		list = append(list, tmp)
	}

	// 如果数量够了，直接返回
	if len(list) >= int(limit) {
		return list, nil
	}

	lastMonthLimit := limit - uint32(len(list))
	lastMonthDate := nowTime.AddDate(0, 0, -nowTime.Day()) // 上个月的最后一天
	tmpList = make([]*UserPresentReceiveDetail, 0)
	sql = fmt.Sprintf("SELECT from_uid, item_id, item_count, UNIX_TIMESTAMP(create_time) as create_time, score, add_charm, order_id, send_source, send_method , is_ukw"+
		" FROM user_present_receive_detail_%02d%02d WHERE uid = ? and create_time > ? ORDER BY create_time DESC LIMIT ?, ?", lastMonthDate.Month()-1, uid%100)
	err = s.db.SelectContext(ctx, &tmpList, sql, uid, dateTime, 0, lastMonthLimit)
	if err != nil && !mysql.IsNoRowsError(err) {
		log.ErrorWithCtx(ctx, "GetUserPresentReceiveDetailList err , uid %d, err %v", uid, err)
		return list, err
	}

	for _, item := range tmpList {
		tmp := &userpresent.StUserPresentDetail{
			TargetUid:   uid,
			FromUid:     item.FromUid,
			ItemId:      item.ItemId,
			ItemCount:   item.ItemCount,
			ReceiveTime: item.CreateTime,
			Score:       item.Score,
			AddCharm:    item.AddCharm,
			OrderId:     item.OrderId,
			SendSource:  item.SendSource,
			SendMethod:  item.SendMethod,
			IsUkw:       item.IsUkw,
		}
		list = append(list, tmp)
	}

	return list, nil
}

func (s *DetailStore) GetUserPresentSendDetailList(ctx context.Context, uid, offset, limit uint32) (list []*userpresent.StUserPresentDetail, err error) {
	list = make([]*userpresent.StUserPresentDetail, 0)

	nowTime := time.Now()
	dateTime := nowTime.Add(-3600 * 24 * 31 * time.Second) // 最多取到31天前的数据

	tmpList := make([]*UserPresentSendDetail, 0)
	sql := fmt.Sprintf("SELECT to_uid, item_id, item_count, UNIX_TIMESTAMP(create_time) as create_time , score, add_rich, order_id, send_source, send_method , is_ukw"+
		" FROM user_present_send_detail_%02d%02d WHERE uid = ? and create_time > ? ORDER BY create_time DESC LIMIT ?, ?", nowTime.Month()-1, uid%100)
	err = s.db.SelectContext(ctx, &tmpList, sql, uid, dateTime, offset, limit)
	if err != nil && !mysql.IsNoRowsError(err) {
		log.ErrorWithCtx(ctx, "GetUserPresentSendDetailList err , uid %d, err %v", uid, err)
		return list, err
	}

	for _, item := range tmpList {
		tmp := &userpresent.StUserPresentDetail{
			TargetUid:   item.ToUid,
			FromUid:     uid,
			ItemId:      item.ItemId,
			ItemCount:   item.ItemCount,
			ReceiveTime: item.CreateTime,
			Score:       item.Score,
			AddRich:     item.AddRich,
			OrderId:     item.OrderId,
			SendSource:  item.SendSource,
			SendMethod:  item.SendMethod,
			IsUkw:       item.IsUkw,
		}
		list = append(list, tmp)
	}

	// 如果数量够了，直接返回
	if len(list) >= int(limit) {
		return list, nil
	}

	lastMonthLimit := limit - uint32(len(list))
	lastMonthDate := nowTime.AddDate(0, 0, -nowTime.Day()) // 上个月的最后一天
	tmpList = make([]*UserPresentSendDetail, 0)
	sql = fmt.Sprintf("SELECT to_uid, item_id, item_count, UNIX_TIMESTAMP(create_time) as create_time, score, add_rich, order_id, send_source, send_method , is_ukw"+
		" FROM user_present_send_detail_%02d%02d WHERE uid = ? and create_time > ? ORDER BY create_time DESC LIMIT ?, ?", lastMonthDate.Month()-1, uid%100)
	err = s.db.SelectContext(ctx, &tmpList, sql, uid, dateTime, 0, lastMonthLimit)
	if err != nil && !mysql.IsNoRowsError(err) {
		log.ErrorWithCtx(ctx, "GetUserPresentSendDetailList err , uid %d, err %v", uid, err)
		return list, err
	}

	for _, item := range tmpList {
		tmp := &userpresent.StUserPresentDetail{
			TargetUid:   item.ToUid,
			FromUid:     uid,
			ItemId:      item.ItemId,
			ItemCount:   item.ItemCount,
			ReceiveTime: item.CreateTime,
			Score:       item.Score,
			AddRich:     item.AddRich,
			OrderId:     item.OrderId,
			SendSource:  item.SendSource,
			SendMethod:  item.SendMethod,
			IsUkw:       item.IsUkw,
		}
		list = append(list, tmp)
	}

	return list, nil
}

func (s *DetailStore) SendPresentDetailRecord(ctx context.Context, tx mysql.Txx, req *userpresent.SendPresentReq, cfg userpresent.StPresentItemConfig, cnt uint32, isRecordRich bool) (err error) {
	if tx == nil {
		tx, err = s.db.Beginx()
		if err != nil {
			log.ErrorWithCtx(ctx, "Transaction Beginx fail err %v", err)
			return err
		}

		defer HandleTx(ctx, tx, err)
	}

	addRich := req.GetAddRich()
	if !isRecordRich {
		addRich = 0
	}

	isUkw := false
	if req.GetToUkwAccount() != "" {
		isUkw = true
	}

	sendTime := req.GetSendTime()
	if sendTime == 0 {
		sendTime = uint32(time.Now().Unix())
	}

	timeDate := time.Unix(int64(sendTime), 0).Local()

	sql := fmt.Sprintf("INSERT INTO user_present_send_detail_%02d%02d (uid, to_uid, order_id, item_id, item_count, "+
		"score, add_rich, user_from_ip, channel_id, guild_id, create_time, send_source, send_method , is_ukw) "+
		"VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, FROM_UNIXTIME(?),  ?, ?, ?)", timeDate.Month()-1, req.GetUid()%100)

	_, err = tx.ExecContext(ctx, sql, req.GetUid(), req.GetTargetUid(), req.GetOrderId(), req.GetItemId(), cnt, cfg.GetScore(),
		addRich, req.GetUserFromIp(), req.GetChannelId(), req.GetGuildId(), sendTime, req.GetSendSource(), req.GetSendMethod(), isUkw)

	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresentDetailRecord err , uid %d, err %v", req.GetUid(), err)
		return err
	}
	return nil
}

func (s *DetailStore) ReceivePresentDetailRecord(ctx context.Context, tx mysql.Txx, req *userpresent.SendPresentReq, cfg userpresent.StPresentItemConfig, cnt uint32, isRecordRich bool) (err error) {
	if tx == nil {
		tx, err = s.db.Beginx()
		if err != nil {
			log.ErrorWithCtx(ctx, "Transaction Beginx fail err %v", err)
			return err
		}

		defer HandleTx(ctx, tx, err)
	}

	isUkw := false
	if req.GetFromUkwAccount() != "" {
		isUkw = true
	}

	sendTime := req.GetSendTime()
	if sendTime == 0 {
		sendTime = uint32(time.Now().Unix())
	}

	timeDate := time.Unix(int64(sendTime), 0).Local()

	sql := fmt.Sprintf("INSERT INTO user_present_receive_detail_%02d%02d (uid, from_uid, order_id, item_id, item_count, "+
		"score, add_charm, user_from_ip, channel_id, guild_id, create_time, send_source, send_method , is_ukw) "+
		"VALUES (?, ?, ?, ?, ?,  ?, ?, ?, ?, ?,FROM_UNIXTIME(?), ?, ?, ?)", timeDate.Month()-1, req.GetTargetUid()%100)

	_, err = tx.ExecContext(ctx, sql, req.GetTargetUid(), req.GetUid(), req.GetOrderId(), req.GetItemId(), cnt, cfg.GetScore(),
		req.GetAddCharm(), req.GetUserFromIp(), req.GetChannelId(), req.GetGuildId(), sendTime, req.GetSendSource(), req.GetSendMethod(), isUkw)

	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresentDetailRecord err , uid %d, err %v", req.GetTargetUid(), err)
		return err
	}

	return nil

}

func (s *DetailStore) RecordPresentDealToken(ctx context.Context, tx mysql.Txx, orderId, dealToken string, sendTime uint32) (err error) {
	if tx == nil {
		tx, err = s.db.Beginx()
		if err != nil {
			log.ErrorWithCtx(ctx, "Transaction Beginx fail err %v", err)
			return err
		}

		defer HandleTx(ctx, tx, err)
	}

	sql := fmt.Sprintf("INSERT INTO user_present_monthly_dealtoken_%d%02d (order_id, deal_token) VALUES (?, ?)", time.Unix(int64(sendTime), 0).Local().Year(), time.Unix(int64(sendTime), 0).Local().Month())
	_, err = tx.ExecContext(ctx, sql, orderId, dealToken)
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordPresentDealToken err , orderId %s, err %v", orderId, err)
		return
	}

	return nil
}

type UserPresentDealToken struct {
	OrderId   string `db:"order_id"`
	DealToken string `db:"deal_token"`
}

func (s *DetailStore) BatchGetPresentDealToken(ctx context.Context, orderIds []string) (dealTokens map[string]string, err error) {
	dealTokens = make(map[string]string)

	tmp := make([]*UserPresentDealToken, 0)
	if len(orderIds) == 0 {
		return dealTokens, nil
	}

	nowTime := time.Now()
	sql := fmt.Sprintf("SELECT order_id, deal_token FROM user_present_monthly_dealtoken_%d%02d WHERE order_id IN (?)", nowTime.Year(), nowTime.Month())
	query, args, err := sqlx.In(sql, orderIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetPresentDealToken err , err %v", err)
		return dealTokens, err
	}

	err = s.db.SelectContext(ctx, &tmp, query, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetPresentDealToken err , err %v", err)
		return dealTokens, err
	}

	for _, item := range tmp {
		dealTokens[item.OrderId] = item.DealToken
	}

	return dealTokens, nil
}

func (s *DetailStore) CreateDealTokenMonthlyTable(ctx context.Context, year, month int) {
	sql := fmt.Sprintf(createPresentMonthlyDealTokenTable, year, month)
	_, err := s.db.ExecContext(ctx, sql)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateDealTokenMonthlyTable err , sql %s , err %v", sql, err)
		return
	}
}

func (s *DetailStore) BatchSendPresentDetailRecord(ctx context.Context, tx mysql.Txx, reqs []*SendPresentReqWithPrice, uid uint32, isRecordRich bool) (err error) {
	if tx == nil {
		tx, err = s.db.Beginx()
		if err != nil {
			log.ErrorWithCtx(ctx, "Transaction Beginx fail err %v", err)
			return err
		}

		defer HandleTx(ctx, tx, err)
	}

	sqlValues := make([]string, 0)
	args := make([]interface{}, 0)

	timeDate := time.Now()

	for _, req := range reqs {
		addRich := req.GetAddRich()
		if !isRecordRich {
			addRich = 0
		}

		isUkw := req.GetToUkwAccount() != ""

		sendTime := req.GetSendTime()
		if sendTime == 0 {
			sendTime = uint32(time.Now().Unix())
		}

		timeDate = time.Unix(int64(sendTime), 0).Local()

		sqlValues = append(sqlValues, "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, FROM_UNIXTIME(?), ?, ?, ?)")
		args = append(args, req.GetUid(), req.GetTargetUid(), req.GetOrderId(), req.GetItemId(), req.GetItemCount(), req.Score,
			addRich, req.GetUserFromIp(), req.GetChannelId(), req.GetGuildId(), sendTime, req.GetSendSource(), req.GetSendMethod(), isUkw)
	}

	sql := fmt.Sprintf("INSERT INTO user_present_send_detail_%02d%02d (uid, to_uid, order_id, item_id, item_count, "+
		"score, add_rich, user_from_ip, channel_id, guild_id, create_time, send_source, send_method , is_ukw) "+
		"VALUES %s", timeDate.Month()-1, uid%100, strings.Join(sqlValues, ","))

	_, err = tx.ExecContext(ctx, sql, args...)

	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresentDetailRecord err , uid %d, err %v", uid, err)
		return err
	}
	return nil
}

func (s *DetailStore) BatchReceivePresentDetailRecord(ctx context.Context, tx mysql.Txx, reqs []*SendPresentReqWithPrice, uid uint32, isRecordRich bool) (err error) {
	if tx == nil {
		tx, err = s.db.Beginx()
		if err != nil {
			log.ErrorWithCtx(ctx, "Transaction Beginx fail err %v", err)
			return err
		}

		defer HandleTx(ctx, tx, err)
	}

	sqlValues := make([]string, 0)
	args := make([]interface{}, 0)
	timeDate := time.Now()

	for _, req := range reqs {

		isUkw := req.GetFromUkwAccount() != ""

		sendTime := req.GetSendTime()
		if sendTime == 0 {
			sendTime = uint32(time.Now().Unix())
		}

		timeDate = time.Unix(int64(sendTime), 0).Local()

		sqlValues = append(sqlValues, "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, FROM_UNIXTIME(?), ?, ?, ?)")
		args = append(args, req.GetTargetUid(), req.GetUid(), req.GetOrderId(), req.GetItemId(), req.GetItemCount(), req.Score,
			req.GetAddCharm(), req.GetUserFromIp(), req.GetChannelId(), req.GetGuildId(), sendTime, req.GetSendSource(), req.GetSendMethod(), isUkw)
	}

	sql := fmt.Sprintf("INSERT INTO user_present_receive_detail_%02d%02d (uid, from_uid, order_id, item_id, item_count, "+
		"score, add_charm, user_from_ip, channel_id, guild_id, create_time, send_source, send_method , is_ukw) "+
		"VALUES %s", timeDate.Month()-1, uid%100, strings.Join(sqlValues, ","))

	_, err = tx.ExecContext(ctx, sql, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresentDetailRecord err , uid %d, err %v", uid, err)
		return err
	}
	return nil
}

func (s *DetailStore) RecordPresentDealTokens(ctx context.Context, tx mysql.Txx, deals []*DealInfo) error {
	if tx == nil {
		var err error
		tx, err = s.db.Beginx()
		if err != nil {
			log.ErrorWithCtx(ctx, "Transaction Beginx fail err %v", err)
			return err
		}

		defer HandleTx(ctx, tx, err)
	}

	sqlValues := make([]string, 0)
	args := make([]interface{}, 0)

	for _, deal := range deals {
		sqlValues = append(sqlValues, "(?, ?)")
		args = append(args, deal.OrderID, deal.DealToken)
	}

	// Assuming all deals have the same sendTime, using the first deal's sendTime
	sendTime := deals[0].SendTime
	sql := fmt.Sprintf("INSERT INTO user_present_monthly_dealtoken_%d%02d (order_id, deal_token) VALUES %s",
		time.Unix(int64(sendTime), 0).Local().Year(), time.Unix(int64(sendTime), 0).Local().Month(), strings.Join(sqlValues, ","))

	_, err := tx.ExecContext(ctx, sql, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordPresentDealTokens err, err %v", err)
		return err
	}

	return nil
}

// DealInfo represents the structure holding orderID, dealToken, and sendTime.
type DealInfo struct {
	OrderID   string
	DealToken string
	SendTime  uint32
}
