package manager

import (
	"context"
	"fmt"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	pgc_channel_ticket "golang.52tt.com/clients/pgc-channel-ticket"
	present_extra_conf "golang.52tt.com/clients/present-extra-conf"
	userpresent_go "golang.52tt.com/clients/userpresent-go"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	ga_base "golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/presentextraconf"
	"google.golang.org/grpc"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"

	"golang.52tt.com/clients/account"
	fellowsvr "golang.52tt.com/clients/fellow-svr"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	fellow_svr "golang.52tt.com/protocol/services/fellow-svr"
	pb "golang.52tt.com/protocol/services/userpresent-go"
	presentPb "golang.52tt.com/protocol/services/userpresent-go"
)

const FellowPresentTagType = 5

type PresentMgr struct {
	presentCli         userpresent_go.IClient
	PresentCfgCache    map[uint32]*presentPb.StPresentItemConfig
	presentMysql       *sqlx.DB
	presentSecondMysql *sqlx.DB // 礼物有两个不同的库，这里是第二个库
	accountCli         account.IClient
	fellowCli          *fellowsvr.Client
	ticketCli          *pgc_channel_ticket.Client
	presentExtraCli    *present_extra_conf.Client
}

type sendPresent struct {
	ToUid      uint32 `db:"to_uid"`
	ItemId     uint32 `db:"item_id"`
	Count      uint32 `db:"item_count"`
	TotalPrice uint32
	CreateTime time.Time `db:"create_time"`
}

type receivePresent struct {
	ToUid      uint32    `db:"from_uid"`
	ItemId     uint32    `db:"item_id"`
	Count      uint32    `db:"item_count"`
	TotalPrice uint32    `db:"add_charm"`
	CreateTime time.Time `db:"create_time"`
	Score      uint32    `db:"score"`
}

const CreatePresentIdSyncTable string = `
CREATE TABLE present_id_sync (
	id INT UNSIGNED NOT NULL DEFAULT 0,
	test_id INT UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='礼物ID和测试ID的映射表';`

type presentIdSync struct {
	Id     uint32 `db:"id"`
	TestId uint32 `db:"test_id"`
}

func NewBackendPresentMgr(mysqlConfig *mysqlConnect.MysqlConfig, mysqlSecondConfig *mysqlConnect.MysqlConfig) (*PresentMgr, error) {
	accountCli, _ := account.NewClient(grpc.WithBlock())
	fellowCli, _ := fellowsvr.NewClient(grpc.WithBlock())
	presentExtraCli, _ := present_extra_conf.NewClient(grpc.WithBlock())
	ticketCli, _ := pgc_channel_ticket.NewClient(grpc.WithBlock())
	presentCfgCache := make(map[uint32]*presentPb.StPresentItemConfig, 0)
	presentCli := userpresent_go.NewIClient(grpc.WithBlock())
	presentMysql, err := sqlx.Connect("mysql", mysqlConfig.ConnectionString())
	if err != nil {
		log.Errorf("sqlx.Connect failed err:%v", err)
		return nil, err
	}

	// 礼物有两个不同的库，这里是第二个库
	presentSecondMysql, err := sqlx.Connect("mysql", mysqlSecondConfig.ConnectionString())
	if err != nil {
		log.Errorf("sqlx.Connect failed err:%v", err)
		return nil, err
	}

	presentMgr := &PresentMgr{
		presentCli:         presentCli,
		PresentCfgCache:    presentCfgCache,
		presentMysql:       presentMysql,
		accountCli:         accountCli,
		fellowCli:          fellowCli,
		ticketCli:          ticketCli,
		presentExtraCli:    presentExtraCli,
		presentSecondMysql: presentSecondMysql,
	}

	ctx := context.Background()
	err = presentMgr.CreatePresentIdSyncTable(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewUserPresentGoMgr CreatePresentBaseConfigTable failed,err:%v", err)
	}

	//拿个缓存并启动维护协程
	presentMgr.updateCache()
	presentMgr.maintain()

	return presentMgr, nil

}
func (m *PresentMgr) maintain() {
	go func() {
		//for {
		//	select {
		//	case <-time.After(time.Second * 15):
		//		m.updateCache()
		//	}
		//}
		t := time.NewTicker(time.Second * 15)
		defer t.Stop()

		for range t.C {
			m.updateCache()
		}
	}()
}

func (m *PresentMgr) updateCache() {
	resp, err := m.presentCli.GetPresentConfigList(context.Background(), &presentPb.GetPresentConfigListReq{})
	if err != nil {
		log.Errorf("NewPresentMgr failed err:%v", err)
	}
	for _, item := range resp.GetItemList() {
		m.PresentCfgCache[item.ItemId] = item
	}

	log.Debugf("PresentMgr.maintain finish itemList is %v", resp.GetItemList())
}

/*
func fillPresentConfigRespMsg (in *presentpb.GetPresentConfigByIdBackendResp, out *pb.GetPresentConfigByIdBackendResp) {
	  btyeMsg, _ := in.Marshal()
      proto.Unmarshal(btyeMsg, out)
}
*/

func (m *PresentMgr) GetPresentConfigById(ctx context.Context, itemId uint32) (*pb.GetPresentConfigByIdBackendResp, error) {
	out := &pb.GetPresentConfigByIdBackendResp{}

	log.InfoWithCtx(ctx, "GetPresentConfigById start itemId %d", itemId)
	resp, err := m.presentCli.GetPresentConfigById(ctx, itemId)
	if err != nil {
		log.Errorf("GetPresentConfigById failed id:%d err:%v", itemId, err)
		return out, err
	}

	out.ItemConfig = transPresentConfigMsg(resp.GetItemConfig())

	// 再拿角标
	mark, err := m.presentCli.GetPresentMarkIconByPresentId(ctx, itemId)
	if err != nil {
		log.Errorf("GetPresentMarkIconByPresentId failed id:%d err:%v", itemId, err)
		return out, err
	}

	out.ItemConfig.Extend.OriginIconUrl = mark.GetOriginIconUrl()
	out.ItemConfig.Extend.MarkId = uint64(mark.GetMarkConfig().GetId())
	out.ItemConfig.Extend.MarkName = mark.GetMarkConfig().GetName()
	if out.ItemConfig.Extend.OriginIconUrl == "" {
		out.ItemConfig.Extend.OriginIconUrl = out.ItemConfig.GetIconUrl()
	}

	if out.GetItemConfig().GetExtend().GetTag() == FellowPresentTagType {
		err := m.getFellowConfig(ctx, out)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPresentConfigById getFellowConfig failed id:%d err:%v", itemId, err)
			return out, err
		}
	}

	// 如果是限时延长礼物
	if out.GetItemConfig().GetExtend().GetEffectEndDelay() {
		out.GetItemConfig().DelayInfo = make([]*pb.EffectDelayLevelInfo, 0)
		resp, err := m.presentExtraCli.GetEffectDelayLevel(ctx, &presentextraconf.GetEffectDelayLevelReq{
			ItemId: itemId,
		})

		if err != nil {
			log.ErrorWithCtx(ctx, "GetPresentConfigById GetEffectDelayLevel failed id:%d err:%v", itemId, err)
			return out, err
		}
		for _, item := range resp.GetDelayConfig() {
			if item.GiftId != out.GetItemConfig().GetItemId() || item.EffectBegin != out.GetItemConfig().GetEffectBegin() {
				continue
			}
			for _, info := range item.GetDelayInfo() {
				out.GetItemConfig().DelayInfo = append(out.GetItemConfig().DelayInfo, m.getTimeDelayInfo(info))
			}
		}

		sort.Slice(out.GetItemConfig().DelayInfo, func(i, j int) bool {
			return out.GetItemConfig().DelayInfo[i].GetLevel() <= out.GetItemConfig().DelayInfo[j].GetLevel()
		})
	}

	testPresentId, err := m.GetPresentIdSync(ctx, itemId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentConfigById GetPresentIdSync failed id:%d err:%v", itemId, err)
		//return out, err
	}
	out.TestingPresentId = testPresentId

	log.InfoWithCtx(ctx, "GetPresentConfigById end id %d out %v", itemId, out)
	return out, nil
}

func (m *PresentMgr) GetPresentConfigList(ctx context.Context, in *pb.GetPresentConfigListBackendReq) (*pb.GetPresentConfigListBackendResp, error) {
	out := &pb.GetPresentConfigListBackendResp{}

	log.InfoWithCtx(ctx, "GetPresentConfigList begin in:%v", in)
	resp, err := m.presentCli.GetPresentConfigList(ctx, &presentPb.GetPresentConfigListReq{
		TypeBitmap: in.GetTypeBitmap(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentConfigList failed in:%v err:%v", in, err)
		return out, err
	}

	for _, cfg := range resp.GetItemList() {
		// 挚友信物不显示
		if (cfg.GetExtend().GetTag() == FellowPresentTagType || cfg.GetExtend().GetTag() == uint32(ga_base.PresentTagType_PRESENT_TAG_CUSTOMIZED)) && !in.GetGetAll() {
			continue
		}

		tmpCfg := transPresentConfigMsg(cfg)

		// 如果是限时延长礼物
		if tmpCfg.GetExtend().GetEffectEndDelay() {
			tmpCfg.DelayInfo = make([]*pb.EffectDelayLevelInfo, 0)
			resp, err := m.presentExtraCli.GetEffectDelayLevel(ctx, &presentextraconf.GetEffectDelayLevelReq{
				ItemId: tmpCfg.GetItemId(),
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "GetPresentConfigById GetEffectDelayLevel failed id:%d err:%v", tmpCfg.GetItemId(), err)
				return out, err
			}
			for _, item := range resp.GetDelayConfig() {
				if item.GiftId != tmpCfg.GetItemId() || item.EffectBegin != tmpCfg.GetEffectBegin() {
					continue
				}
				for _, info := range item.GetDelayInfo() {
					tmpCfg.DelayInfo = append(tmpCfg.DelayInfo, m.getTimeDelayInfo(info))
				}
			}
			sort.Slice(tmpCfg.DelayInfo, func(i, j int) bool {
				return tmpCfg.DelayInfo[i].GetLevel() <= tmpCfg.DelayInfo[j].GetLevel()
			})
		}
		out.ItemList = append(out.ItemList, tmpCfg)
	}

	log.InfoWithCtx(ctx, "GetPresentConfigList end in:%v out:%v", in, out)
	return out, nil
}

func (m *PresentMgr) AddPresentConfig(ctx context.Context, in *pb.AddPresentConfigBackendReq) (*pb.AddPresentConfigBackendResp, error) {
	out := &pb.AddPresentConfigBackendResp{}

	// t豆礼物价格必须是双数且不为0，不然会造成对账问题
	if in.GetPrice() <= 1 && in.GetPriceType() == 2 {
		return out, protocol.NewServerError(status.ErrUserPresentConfigParam, "礼物价格必须大于1，否则会造成对账问题")
	}

	customText := make([]*presentPb.CustomText, 0)
	for _, item := range in.GetExtend().GetCustomText() {
		newStrs := make([]string, 0)
		for _, str := range item.GetText() {
			newStr := strings.Replace(str, "\r\n", "\n", -1)
			newStrs = append(newStrs, newStr)
		}
		customText = append(customText, &presentPb.CustomText{
			Key:  item.GetKey(),
			Text: newStrs,
		})
	}

	//如果rank为0，用rank_float取整
	if in.GetRank() == 0 {
		in.Rank = uint32(in.GetRankFloat())
	}

	log.InfoWithCtx(ctx, "AddPresentConfig begin in:%v", in)
	resp, err := m.presentCli.AddPresentConfig(ctx, &presentPb.AddPresentConfigReq{
		Name:        in.GetName(),
		IconUrl:     in.GetIconUrl(),
		Price:       in.GetPrice(),
		EffectBegin: in.GetEffectBegin(),
		EffectEnd:   in.GetEffectEnd(),
		Rank:        in.GetRank(),
		PriceType:   in.GetPriceType(),
		Extend: &presentPb.StPresentItemConfigExtend{
			ItemId:             in.GetExtend().GetItemId(),
			VideoEffectUrl:     in.GetExtend().GetVideoEffectUrl(),
			ShowEffect:         in.GetExtend().GetShowEffect(),
			UnshowBatchOption:  in.GetExtend().GetUnshowBatchOption(),
			IsTest:             in.GetExtend().GetIsTest(),
			FlowId:             in.GetExtend().GetFlowId(),
			IosExtend:          &presentPb.StConfigIosExtend{VideoEffectUrl: in.GetExtend().GetIosExtend().GetVideoEffectUrl()},
			NotifyAll:          in.GetExtend().GetNotifyAll(),
			Tag:                in.GetExtend().GetTag(),
			ForceSendable:      in.GetExtend().GetForceSendable(),
			NobilityLevel:      in.GetExtend().GetNobilityLevel(),
			UnshowPresentShelf: in.GetExtend().GetUnshowPresentShelf(),
			ShowEffectEnd:      in.GetExtend().GetShowEffectEnd(),
			CustomText:         customText,
			EffectEndDelay:     in.GetExtend().GetEffectEndDelay(),
			SmallVapUrl:        in.GetExtend().GetSmallVapEffectUrl(),
			SmallVapMd5:        in.GetExtend().GetSmallVapEffectMd5(),
			MicEffectUrl:       in.GetExtend().GetMicEffectUrl(),
			MicEffectMd5:       in.GetExtend().GetMicEffectMd5(),
			FusionPresent:      in.GetExtend().GetFusionPresent(),
			IsBoxBreaking:      in.GetExtend().GetIsBoxBreaking(),
			FansLevel:          in.GetExtend().GetFansLevel(),
			MarkName:           in.GetExtend().GetMarkName(),
			MarkId:             in.GetExtend().GetMarkId(),
			OriginIconUrl:      in.GetExtend().GetOriginIconUrl(),
		},
		RankFloat: in.GetRankFloat(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentConfig failed in:%v err:%v", in, err)
		return out, err
	}

	out.ItemConfig = transPresentConfigMsg(resp.GetItemConfig())

	// 挚友信物额外添加相关参数
	if out.GetItemConfig().GetExtend().GetTag() == FellowPresentTagType {
		out.ItemConfig.Fellow = in.GetFellow()
		err := m.addFellowConfig(ctx, in, out.ItemConfig.GetItemId())
		if err != nil {
			return out, err
		}
	}

	if in.GetExtend().GetEffectEndDelay() {
		delayInfo := make([]*presentextraconf.EffectDelayLevelInfo, 0)

		for _, item := range in.GetDelayInfo() {
			delayInfo = append(delayInfo, &presentextraconf.EffectDelayLevelInfo{
				Level:          item.GetLevel(),
				SendCount:      item.GetSendCount(),
				DayCount:       item.GetDayCount(),
				ExpireDayCount: item.GetExpireDayCount(),
				NoticeDayCount: item.GetNoticeDayCount(),
			})
		}

		_, err := m.presentExtraCli.AddEffectDelayLevel(ctx, &presentextraconf.AddEffectDelayLevelReq{
			GiftId:      out.GetItemConfig().GetItemId(),
			DelayInfo:   delayInfo,
			EffectBegin: out.GetItemConfig().GetEffectBegin(),
			EffectEnd:   out.GetItemConfig().GetEffectEnd(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "AddPresentConfig AddEffectDelayLevel failed in:%v err:%v", in, err)
			return out, err
		}
	}

	if in.GetTestingPresentId() != 0 {
		err := m.AddPresentIdSync(ctx, resp.GetItemConfig().GetItemId(), in.GetTestingPresentId())
		if err != nil {
			log.ErrorWithCtx(ctx, "AddPresentConfig AddPresentIdSync failed in:%v err:%v", in, err)
		}
	}

	log.InfoWithCtx(ctx, "AddPresentConfig end in:%v out:%v", in, out)
	return out, nil
}

func (m *PresentMgr) UpdatePresentConfig(ctx context.Context, in *pb.UpdatePresentConfigBackendReq) (*pb.UpdatePresentConfigBackendResp, error) {
	out := &pb.UpdatePresentConfigBackendResp{}

	// t豆礼物价格必须是双数且不为0，不然会造成对账问题
	if in.GetItemConfig().GetPrice() <= 1 && in.GetItemConfig().GetPriceType() == 2 {
		return out, protocol.NewServerError(status.ErrUserPresentConfigParam, "礼物价格必须大于1，否则会造成对账问题")
	}

	log.InfoWithCtx(ctx, "UpdatePresentConfig begin in:%v", in)
	conf := reTransPresentConfigMsg(in.GetItemConfig())
	if in.GetItemConfig().GetIsBanned() {
		conf.GetExtend().EffectEndDelay = false
	}

	_, err := m.presentCli.UpdatePresentConfig(ctx, &presentPb.UpdatePresentConfigReq{
		ItemConfig: conf,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdatePresentConfig failed in:%v err:%v", in, err)
		return out, err
	}

	// 挚友信物额外添加相关参数
	if in.GetItemConfig().GetExtend().GetTag() == FellowPresentTagType {
		err := m.updateFellowConfig(ctx, in.ItemConfig)
		if err != nil {
			return out, err
		}
	}

	if conf.GetExtend().GetEffectEndDelay() {
		delayInfo := make([]*presentextraconf.EffectDelayLevelInfo, 0)

		for _, item := range in.GetItemConfig().GetDelayInfo() {
			delayInfo = append(delayInfo, &presentextraconf.EffectDelayLevelInfo{
				Level:          item.GetLevel(),
				SendCount:      item.GetSendCount(),
				DayCount:       item.GetDayCount(),
				ExpireDayCount: item.GetExpireDayCount(),
				NoticeDayCount: item.GetNoticeDayCount(),
			})
		}

		_, err := m.presentExtraCli.UpdateEffectDelayLevel(ctx, &presentextraconf.UpdateEffectDelayLevelReq{
			GiftId:      in.GetItemConfig().GetItemId(),
			DelayInfo:   delayInfo,
			EffectBegin: in.GetItemConfig().GetEffectBegin(),
			EffectEnd:   in.GetItemConfig().GetEffectEnd(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdatePresentConfig UpdateEffectDelayLevel failed in:%v err:%v", in, err)
			return out, err
		}
	}

	if in.GetItemConfig().GetIsBanned() {
		_, err := m.presentExtraCli.DelEffectDelayLevel(ctx, &presentextraconf.DelEffectDelayLevelReq{GiftId: in.GetItemConfig().GetItemId()})
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdatePresentConfig DelEffectDelayLevel failed in:%v err:%v", in, err)
			return out, err
		}
	}

	if in.GetTestingPresentId() != 0 {
		err := m.AddPresentIdSync(ctx, in.GetItemConfig().GetItemId(), in.GetTestingPresentId())
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdatePresentConfig AddPresentIdSync failed in:%v err:%v", in, err)
		}
	}

	log.InfoWithCtx(ctx, "UpdatePresentConfig end in:%v out:%v", in, out)
	return out, nil
}

func (m *PresentMgr) DelPresentConfig(ctx context.Context, in *pb.DelPresentConfigBackendReq) (*pb.DelPresentConfigBackendResp, error) {
	out := &pb.DelPresentConfigBackendResp{}

	log.InfoWithCtx(ctx, "DelPresentConfig begin in:%v", in)
	_, err := m.presentCli.DelPresentConfig(ctx, in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelPresentConfig failed in:%v err:%v", in, err)
		return out, err
	}
	log.InfoWithCtx(ctx, "DelPresentConfig end in:%v", in)
	return out, nil
}

func (m *PresentMgr) batchGetUserInfo(ctx context.Context, uidList []uint32) map[uint32]*accountPB.UserResp {
	toUserMap := make(map[uint32]*accountPB.UserResp, 0)

	tmpReqList := []uint32{}
	//每50个一组 查uid对应的用户信息
	for i, j := range uidList {
		tmpReqList = append(tmpReqList, j)
		if (i+1)%50 == 0 || i+1 == len(uidList) {
			resp, err := m.accountCli.GetUsersMap(ctx, tmpReqList)
			if err != nil {
				log.ErrorWithCtx(ctx, "batchGetUserInfo GetUsersMap failed uidList:%v err:%v", tmpReqList, err)
				tmpReqList = []uint32{}
				continue
			}
			for uid, user := range resp {
				toUserMap[uid] = user
			}
			tmpReqList = []uint32{}
		}
	}

	return toUserMap
}

func (m *PresentMgr) GetUserPresentSend(ctx context.Context, in *pb.GetUserPresentSendReq) (*pb.GetUserPresentSendResp, error) {
	out := &pb.GetUserPresentSendResp{}
	log.InfoWithCtx(ctx, "GetUserPresentSendResp begin in:%v", in)

	//先处理时间，如果跨月，要考虑2个表
	beginTime := time.Unix(int64(in.BeginTs), 0)
	endTime := time.Unix(int64(in.EndTs), 0)
	isNextMonth := false
	if beginTime.Month() != endTime.Month() {
		isNextMonth = true
	}

	sendGift := make([]*sendPresent, 0)
	query := fmt.Sprintf(`select to_uid,item_id,item_count,create_time from user_present_send_detail_%02d%02d where uid = %d and create_time > "%s" and create_time < "%s"`,
		beginTime.Month()-1, in.Uid%100, in.Uid, beginTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))
	err := m.presentSecondMysql.SelectContext(ctx, &sendGift, query)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserPackageReceive failed in:%v err:%v", in, err)
		return out, err
	}

	//如果跨月，查第二个月的，并追加到结果中
	if isNextMonth {
		sendGiftTemp := make([]*sendPresent, 0)
		query := fmt.Sprintf(`select to_uid,item_id,item_count,create_time from user_present_send_detail_%02d%02d where uid = %d and create_time > "%s" and create_time < "%s"`,
			endTime.Month()-1, in.Uid%100, in.Uid, beginTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))
		err := m.presentSecondMysql.SelectContext(ctx, &sendGiftTemp, query)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserPackageReceive failed in:%v err:%v", in, err)
			return out, err
		}
		sendGift = append(sendGift, sendGiftTemp...)
	}

	//先把所有to_uid的用户信息查出来
	toUidList := []uint32{}

	for _, sendLog := range sendGift {
		toUidList = append(toUidList, sendLog.ToUid)
	}

	if len(toUidList) == 0 {
		log.ErrorWithCtx(ctx, "GetUserPackageReceive err no rows \n")
		return out, err
	}

	// 分批获取用户信息
	toUserMap := m.batchGetUserInfo(ctx, toUidList)

	//填resp
	for _, sendLog := range sendGift {
		presentSendDetail := &pb.UserPresentSend{}
		presentSendDetail.ToUid = sendLog.ToUid
		ttid, _ := strconv.Atoi(toUserMap[sendLog.ToUid].GetAlias())
		presentSendDetail.ToTtid = uint32(ttid)
		presentSendDetail.ToNickname = toUserMap[sendLog.ToUid].GetNickname()
		presentSendDetail.ItemId = sendLog.ItemId
		presentSendDetail.ItemName = m.PresentCfgCache[sendLog.ItemId].GetName()
		presentSendDetail.ItemCount = sendLog.Count
		presentSendDetail.AddRich = m.PresentCfgCache[sendLog.ItemId].GetPrice() * sendLog.Count
		presentSendDetail.SendTime = uint32(sendLog.CreateTime.Unix())
		out.PresentSendDetail = append(out.PresentSendDetail, presentSendDetail)
	}

	log.InfoWithCtx(ctx, "GetUserPresentSendResp end in:%v", in)
	return out, nil
}

func (m *PresentMgr) GetUserPresentReceive(ctx context.Context, in *pb.GetUserPresentReceiveReq) (*pb.GetUserPresentReceiveResp, error) {
	out := &pb.GetUserPresentReceiveResp{}
	log.InfoWithCtx(ctx, "GetUserPresentReceive begin in:%v", in)

	//先处理时间，如果跨月，要考虑2个表
	beginTime := time.Unix(int64(in.BeginTs), 0)
	endTime := time.Unix(int64(in.EndTs), 0)
	isNextMonth := false
	if beginTime.Month() != endTime.Month() {
		isNextMonth = true
	}

	receiveGift := make([]*receivePresent, 0)
	query := fmt.Sprintf(`select from_uid,item_id,item_count,create_time,score,add_charm from user_present_receive_detail_%02d%02d where uid = %d and create_time > "%s" and create_time < "%s"`,
		beginTime.Month()-1, in.Uid%100, in.Uid, beginTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))
	err := m.presentSecondMysql.SelectContext(ctx, &receiveGift, query)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserPackageReceive failed in:%v err:%v", in, err)
		return out, err
	}

	//如果跨月，查第二个月的，并追加到结果中
	if isNextMonth {
		receiveGiftTemp := make([]*receivePresent, 0)
		query := fmt.Sprintf(`select from_uid,item_id,item_count,create_time,score,add_charm from user_present_receive_detail_%02d%02d where uid = %d and create_time > "%s" and create_time < "%s"`,
			endTime.Month()-1, in.Uid%100, in.Uid, beginTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))
		err := m.presentSecondMysql.SelectContext(ctx, &receiveGiftTemp, query)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserPackageReceive failed in:%v err:%v", in, err)
			return out, err
		}
		receiveGift = append(receiveGift, receiveGiftTemp...)
	}

	//先把所有to_uid的用户信息查出来
	toUidList := []uint32{}

	for _, sendLog := range receiveGift {
		toUidList = append(toUidList, sendLog.ToUid)
	}

	if len(toUidList) == 0 {
		log.ErrorWithCtx(ctx, "GetUserPackageReceive err no rows \n")
		return out, err
	}

	// 分批获取用户信息
	toUserMap := m.batchGetUserInfo(ctx, toUidList)

	//填resp
	for _, sendLog := range receiveGift {
		presentSendDetail := &pb.UserPresentReceive{}
		presentSendDetail.FromUid = sendLog.ToUid
		ttid, _ := strconv.Atoi(toUserMap[sendLog.ToUid].GetAlias())
		presentSendDetail.FromTtid = uint32(ttid)
		presentSendDetail.FromNickname = toUserMap[sendLog.ToUid].GetNickname()
		presentSendDetail.ItemId = sendLog.ItemId
		presentSendDetail.ItemName = m.PresentCfgCache[sendLog.ItemId].GetName()
		presentSendDetail.ItemCount = sendLog.Count
		presentSendDetail.AddCharm = sendLog.TotalPrice
		presentSendDetail.AddScore = sendLog.Score
		presentSendDetail.ReceiveTime = uint32(sendLog.CreateTime.Unix())
		out.PresentReceiveDetail = append(out.PresentReceiveDetail, presentSendDetail)
	}

	log.InfoWithCtx(ctx, "GetUserPresentSendResp end in:%v", in)
	return out, nil
}

func (m *PresentMgr) GetAllFellowPresent(ctx context.Context, in *pb.GetAllFellowPresentReq) (*pb.GetAllFellowPresentResp, error) {
	out := &pb.GetAllFellowPresentResp{FellowPresentList: make([]*presentPb.StPresentItemConfigBackend, 0)}

	log.InfoWithCtx(ctx, "GetAllFellowPresent begin in:%v", in)
	resp, err := m.fellowCli.GetAllFellowPresentConfig(ctx, &fellow_svr.GetAllFellowPresentConfigReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentConfigList GetAllFellowPresentConfig failed in:%v err:%v", in, err)
		return out, err
	}

	presentList := make([]uint32, 0)
	presentMap := make(map[uint32]*fellow_svr.FellowPresentConfig, 0)
	for _, item := range resp.Config {
		presentList = append(presentList, item.GiftId)
		presentMap[item.GiftId] = item
	}

	itemConfig, err := m.presentCli.GetPresentConfigByIdList(ctx, presentList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentConfigList failed in:%v err:%v", in, err)
		return out, err
	}

	for _, cfg := range itemConfig.GetItemList() {
		config := transPresentConfigMsg(cfg)
		if _, ok := presentMap[cfg.GetItemId()]; ok {
			config.Fellow = &pb.FellowPresentConfig{
				UniqueSourceType:    presentMap[cfg.GetItemId()].UniqueSourceType,
				UniqueMd5:           presentMap[cfg.GetItemId()].UniqueMd5,
				UniqueBackgroundUrl: presentMap[cfg.GetItemId()].UniqueBackgroundUrl,
				MultiSourceType:     presentMap[cfg.GetItemId()].MultiSourceType,
				MultiMd5:            presentMap[cfg.GetItemId()].MultiMd5,
				MultiBackgroundUrl:  presentMap[cfg.GetItemId()].MultiBackgroundUrl,
				UniqueBackgroundImg: presentMap[cfg.GetItemId()].UniqueBackgroundImg,
				MultiBackgroundImg:  presentMap[cfg.GetItemId()].MultiBackgroundImg,
				SourceZip:           presentMap[cfg.GetItemId()].SourceUrl,
			}
		}

		out.FellowPresentList = append(out.FellowPresentList, config)
	}

	log.InfoWithCtx(ctx, "GetPresentConfigList end in:%v out:%v", in, out)
	return out, nil
}

func transPresentConfigMsg(inMsg *presentPb.StPresentItemConfig) *pb.StPresentItemConfigBackend {
	var outMsg pb.StPresentItemConfigBackend
	customText := make([]*pb.CustomText, 0)
	for _, item := range inMsg.GetExtend().GetCustomText() {
		customText = append(customText, &pb.CustomText{
			Key:  item.GetKey(),
			Text: item.GetText(),
		})
	}

	extendConf := &pb.StPresentItemConfigExtendBackend{
		ItemId:            inMsg.GetExtend().GetItemId(),
		VideoEffectUrl:    inMsg.GetExtend().GetVideoEffectUrl(),
		ShowEffect:        inMsg.GetExtend().GetShowEffect(),
		UnshowBatchOption: inMsg.GetExtend().GetUnshowBatchOption(),
		IsTest:            inMsg.GetExtend().GetIsTest(),
		FlowId:            inMsg.GetExtend().GetFlowId(),
		IosExtend: &pb.StConfigIosExtend{
			VideoEffectUrl: inMsg.GetExtend().GetIosExtend().GetVideoEffectUrl(),
		},
		NotifyAll:          inMsg.GetExtend().GetNotifyAll(),
		Tag:                inMsg.GetExtend().GetTag(),
		ForceSendable:      inMsg.GetExtend().GetForceSendable(),
		NobilityLevel:      inMsg.GetExtend().GetNobilityLevel(),
		UnshowPresentShelf: inMsg.GetExtend().GetUnshowPresentShelf(),
		ShowEffectEnd:      inMsg.GetExtend().GetShowEffectEnd(),
		CustomText:         customText,
		EffectEndDelay:     inMsg.GetExtend().GetEffectEndDelay(),
		MicEffectUrl:       inMsg.GetExtend().GetMicEffectUrl(),
		MicEffectMd5:       inMsg.GetExtend().GetMicEffectMd5(),
		SmallVapEffectUrl:  inMsg.GetExtend().GetSmallVapUrl(),
		SmallVapEffectMd5:  inMsg.GetExtend().GetSmallVapMd5(),
		FusionPresent:      inMsg.GetExtend().GetFusionPresent(),
		IsBoxBreaking:      inMsg.GetExtend().GetIsBoxBreaking(),
		FansLevel:          inMsg.GetExtend().GetFansLevel(),
		MarkName:           inMsg.GetExtend().GetMarkName(),
		MarkId:             inMsg.GetExtend().GetMarkId(),
		OriginIconUrl:      inMsg.GetExtend().GetOriginIconUrl(),
	}

	outMsg.ItemId = inMsg.GetItemId()
	outMsg.Name = inMsg.GetName()
	outMsg.IconUrl = inMsg.GetIconUrl()
	outMsg.Price = inMsg.GetPrice()
	outMsg.Score = inMsg.GetScore()
	outMsg.Charm = inMsg.GetCharm()
	outMsg.Rank = inMsg.GetRank()
	outMsg.EffectBegin = inMsg.GetEffectBegin()
	outMsg.EffectEnd = inMsg.GetEffectEnd()
	outMsg.UpdateTime = inMsg.GetUpdateTime()
	outMsg.CreateTime = inMsg.GetCreateTime()
	outMsg.IsDel = inMsg.GetIsDel()
	outMsg.PriceType = inMsg.GetPriceType()
	outMsg.RichValue = inMsg.GetRichValue()
	outMsg.Extend = extendConf
	outMsg.RankFloat = inMsg.GetRankFloat()

	return &outMsg
}

func reTransPresentConfigMsg(inMsg *pb.StPresentItemConfigBackend) *presentPb.StPresentItemConfig {
	var outMsg presentPb.StPresentItemConfig

	customText := make([]*presentPb.CustomText, 0)
	for _, item := range inMsg.GetExtend().GetCustomText() {
		newStrs := make([]string, 0)
		for _, str := range item.GetText() {
			newStr := strings.Replace(str, "\r\n", "\n", -1)
			newStrs = append(newStrs, newStr)
		}

		customText = append(customText, &presentPb.CustomText{
			Key:  item.GetKey(),
			Text: newStrs,
		})
	}

	if inMsg.GetRank() == 0 {
		inMsg.Rank = uint32(inMsg.GetRankFloat())
	}

	extendConf := &presentPb.StPresentItemConfigExtend{
		ItemId:            inMsg.GetExtend().GetItemId(),
		VideoEffectUrl:    inMsg.GetExtend().GetVideoEffectUrl(),
		ShowEffect:        inMsg.GetExtend().GetShowEffect(),
		UnshowBatchOption: inMsg.GetExtend().GetUnshowBatchOption(),
		IsTest:            inMsg.GetExtend().GetIsTest(),
		FlowId:            inMsg.GetExtend().GetFlowId(),
		IosExtend: &presentPb.StConfigIosExtend{
			VideoEffectUrl: inMsg.GetExtend().GetIosExtend().GetVideoEffectUrl(),
		},
		NotifyAll:          inMsg.GetExtend().GetNotifyAll(),
		Tag:                inMsg.GetExtend().GetTag(),
		ForceSendable:      inMsg.GetExtend().GetForceSendable(),
		NobilityLevel:      inMsg.GetExtend().GetNobilityLevel(),
		UnshowPresentShelf: inMsg.GetExtend().GetUnshowPresentShelf(),
		ShowEffectEnd:      inMsg.GetExtend().GetShowEffectEnd(),
		CustomText:         customText,
		EffectEndDelay:     inMsg.GetExtend().GetEffectEndDelay(),
		SmallVapUrl:        inMsg.GetExtend().GetSmallVapEffectUrl(),
		SmallVapMd5:        inMsg.GetExtend().GetSmallVapEffectMd5(),
		MicEffectUrl:       inMsg.GetExtend().GetMicEffectUrl(),
		MicEffectMd5:       inMsg.GetExtend().GetMicEffectMd5(),
		FusionPresent:      inMsg.GetExtend().GetFusionPresent(),
		IsBoxBreaking:      inMsg.GetExtend().GetIsBoxBreaking(),
		FansLevel:          inMsg.GetExtend().GetFansLevel(),
		MarkName:           inMsg.GetExtend().GetMarkName(),
		MarkId:             inMsg.GetExtend().GetMarkId(),
		OriginIconUrl:      inMsg.GetExtend().GetOriginIconUrl(),
	}

	outMsg.ItemId = inMsg.GetItemId()
	outMsg.Name = inMsg.GetName()
	outMsg.IconUrl = inMsg.GetIconUrl()
	outMsg.Price = inMsg.GetPrice()
	outMsg.Score = inMsg.GetScore()
	outMsg.Charm = inMsg.GetCharm()
	outMsg.Rank = inMsg.GetRank()
	outMsg.EffectBegin = inMsg.GetEffectBegin()
	outMsg.EffectEnd = inMsg.GetEffectEnd()
	outMsg.UpdateTime = inMsg.GetUpdateTime()
	outMsg.CreateTime = inMsg.GetCreateTime()
	outMsg.IsDel = inMsg.GetIsDel()
	outMsg.PriceType = inMsg.GetPriceType()
	outMsg.RichValue = inMsg.GetRichValue()
	outMsg.Extend = extendConf
	outMsg.RankFloat = inMsg.GetRankFloat()

	return &outMsg
}

func (m *PresentMgr) getTimeDelayInfo(delayInfo *presentextraconf.EffectDelayLevelInfo) (delayResp *pb.EffectDelayLevelInfo) {
	delayResp = &pb.EffectDelayLevelInfo{
		Level:          delayInfo.GetLevel(),
		SendCount:      delayInfo.GetSendCount(),
		DayCount:       delayInfo.GetDayCount(),
		ExpireDayCount: delayInfo.GetExpireDayCount(),
		NoticeDayCount: delayInfo.GetNoticeDayCount(),
	}
	return delayResp
}

func (m *PresentMgr) updateFellowConfig(ctx context.Context, in *pb.StPresentItemConfigBackend) error {
	_, err := m.fellowCli.UpdateFellowPresentConfig(ctx, &fellow_svr.UpdateFellowPresentConfigReq{
		Config: &fellow_svr.FellowPresentConfig{
			GiftId:              in.ItemId,
			UniqueSourceType:    in.Fellow.UniqueSourceType,
			UniqueMd5:           in.Fellow.UniqueMd5,
			UniqueBackgroundUrl: in.Fellow.UniqueBackgroundUrl,
			MultiSourceType:     in.Fellow.MultiSourceType,
			MultiMd5:            in.Fellow.MultiMd5,
			MultiBackgroundUrl:  in.Fellow.MultiBackgroundUrl,
			UniqueBackgroundImg: in.Fellow.UniqueBackgroundImg,
			MultiBackgroundImg:  in.Fellow.MultiBackgroundImg,
			SourceUrl:           in.Fellow.SourceZip,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "updateFellowConfig failed in:%v err:%v", in, err)
		return err
	}
	return nil
}

func (m *PresentMgr) addFellowConfig(ctx context.Context, in *pb.AddPresentConfigBackendReq, itemId uint32) error {
	_, err := m.fellowCli.AddFellowPresentConfig(ctx, &fellow_svr.AddFellowPresentConfigReq{
		Config: &fellow_svr.FellowPresentConfig{
			GiftId:              itemId,
			UniqueSourceType:    in.Fellow.UniqueSourceType,
			UniqueMd5:           in.Fellow.UniqueMd5,
			UniqueBackgroundUrl: in.Fellow.UniqueBackgroundUrl,
			MultiSourceType:     in.Fellow.MultiSourceType,
			MultiMd5:            in.Fellow.MultiMd5,
			MultiBackgroundUrl:  in.Fellow.MultiBackgroundUrl,
			UniqueBackgroundImg: in.Fellow.UniqueBackgroundImg,
			MultiBackgroundImg:  in.Fellow.MultiBackgroundImg,
			SourceUrl:           in.Fellow.SourceZip,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentConfig failed in:%v err:%v", in, err)
		return err
	}
	return nil
}

func (m *PresentMgr) getFellowConfig(ctx context.Context, out *pb.GetPresentConfigByIdBackendResp) error {
	resp, err := m.fellowCli.GetFellowPresentConfigById(ctx, &fellow_svr.GetFellowPresentConfigByIdReq{
		GiftId: out.GetItemConfig().GetItemId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFellowPresentConfigById failed id:%d err:%v", out.GetItemConfig().GetItemId(), err)
		return err
	}
	out.ItemConfig.Fellow = &pb.FellowPresentConfig{
		MultiBackgroundUrl:  resp.Config.MultiBackgroundUrl,
		MultiMd5:            resp.Config.MultiMd5,
		MultiSourceType:     resp.Config.MultiSourceType,
		UniqueBackgroundUrl: resp.Config.UniqueBackgroundUrl,
		UniqueMd5:           resp.Config.UniqueMd5,
		UniqueSourceType:    resp.Config.UniqueSourceType,
		UniqueBackgroundImg: resp.Config.UniqueBackgroundImg,
		MultiBackgroundImg:  resp.Config.MultiBackgroundImg,
		SourceZip:           resp.Config.SourceUrl,
	}
	return nil
}

func (m *PresentMgr) CreatePresentIdSyncTable(ctx context.Context) (err error) {

	_, err = m.presentMysql.ExecContext(ctx, CreatePresentIdSyncTable)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateMarkTable err , err %v", err)
		return
	}

	return
}

func (m *PresentMgr) AddPresentIdSync(ctx context.Context, itemId, presentId uint32) (err error) {
	sql := "INSERT INTO present_id_sync (id , test_id) VALUES (?,?) on duplicate key update test_id = ?"
	_, err = m.presentMysql.ExecContext(ctx, sql, itemId, presentId, presentId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentIdSync err , err %v", err)
		return
	}

	return
}

func (m *PresentMgr) GetPresentIdSync(ctx context.Context, itemId uint32) (presentId uint32, err error) {
	sql := "SELECT test_id FROM present_id_sync WHERE id = ?"
	err = m.presentMysql.QueryRowContext(ctx, sql, itemId).Scan(&presentId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentIdSync err , err %v", err)
		return
	}

	return
}
