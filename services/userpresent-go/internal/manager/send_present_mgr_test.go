package manager

import (
	"context"
	"github.com/golang/mock/gomock"
	numeric_go "golang.52tt.com/clients/mocks/numeric-go"
	"golang.52tt.com/pkg/timer"
	pb "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/userpresent-go/internal/cache"
	config "golang.52tt.com/services/userpresent-go/internal/config/ttconfig/userpresent_go"
	"golang.52tt.com/services/userpresent-go/internal/mock"
	"golang.52tt.com/services/userpresent-go/internal/producer"
	"golang.52tt.com/services/userpresent-go/internal/rpc"
	"golang.52tt.com/services/userpresent-go/internal/store"
	"reflect"
	"testing"
)

func TestUserPresentGoMgr_CheckDealToken(t *testing.T) {
	ctrl := gomock.NewController(t)
	configMock := mock.NewMockUserPresentGoConf(ctrl)

	configMock.EXPECT().GetIsCheckDealToken().AnyTimes()

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		price   uint32
		sToken  string
		orderId string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "Test_CheckDealToken",
			fields: fields{
				config: configMock,
			},
			args: args{
				price:   100,
				sToken:  "test",
				orderId: "test",
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			if got := m.CheckDealToken(tt.args.price, tt.args.sToken, tt.args.orderId); got != tt.want {
				t.Errorf("CheckDealToken() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_RecordPresentDetail(t *testing.T) {
	ctrl := gomock.NewController(t)
	detailStoreMock := store.NewMockIDetailStore(ctrl)

	detailStoreMock.EXPECT().GetTx(gomock.Any())
	detailStoreMock.EXPECT().ReceivePresentDetailRecord(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	detailStoreMock.EXPECT().SendPresentDetailRecord(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	detailStoreMock.EXPECT().RecordPresentDealToken(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx          context.Context
		req          *pb.SendPresentReq
		itemCount    uint32
		itemConfig   *pb.StPresentItemConfig
		isRecordRich bool
		dealToken    string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "Test_RecordPresentDetail",
			fields: fields{
				detailStore: detailStoreMock,
			},
			args: args{
				ctx:        context.Background(),
				req:        &pb.SendPresentReq{},
				itemConfig: &pb.StPresentItemConfig{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			if err := m.RecordPresentDetail(tt.args.ctx, tt.args.req, tt.args.itemCount, tt.args.itemConfig, tt.args.isRecordRich, tt.args.dealToken); (err != nil) != tt.wantErr {
				t.Errorf("RecordPresentDetail() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestUserPresentGoMgr_RecordSendPresent(t *testing.T) {
	ctrl := gomock.NewController(t)
	storeMock := store.NewMockIStore(ctrl)
	storeMock.EXPECT().GetTx(gomock.Any())
	storeMock.EXPECT().ReceivePresent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	storeMock.EXPECT().SendPresent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	storeMock.EXPECT().RecordPresentMonthlyHistory(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	storeMock.EXPECT().RecordTicketPresentMonthlyHistory(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx        context.Context
		req        *pb.SendPresentReq
		itemCount  uint32
		itemConfig *pb.StPresentItemConfig
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "Test_RecordSendPresent",
			fields: fields{
				store: storeMock,
			},
			args: args{
				ctx:        context.Background(),
				req:        &pb.SendPresentReq{},
				itemConfig: &pb.StPresentItemConfig{PriceType: 2, Extend: &pb.StPresentItemConfigExtend{Tag: 9}},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			if err := m.RecordSendPresent(tt.args.ctx, tt.args.req, tt.args.itemCount, tt.args.itemConfig); (err != nil) != tt.wantErr {
				t.Errorf("RecordSendPresent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestUserPresentGoMgr_produceKafkaMsg(t *testing.T) {
	ctrl := gomock.NewController(t)
	kafkaProducerMock := mock.NewMockIKafkaProduceMgr(ctrl)

	kafkaProducerMock.EXPECT().PublishPresentEvent(gomock.Any(), gomock.Any())
	kafkaProducerMock.EXPECT().PublishPresentEventV2(gomock.Any(), gomock.Any())

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx       context.Context
		req       *pb.SendPresentReq
		dealToken string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "Test_produceKafkaMsg",
			fields: fields{
				kafkaProducer: kafkaProducerMock,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.SendPresentReq{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			m.produceKafkaMsg(tt.args.ctx, tt.args.req, tt.args.dealToken)
		})
	}
}

func TestUserPresentGoMgr_SendPresent(t *testing.T) {
	ctrl := gomock.NewController(t)
	storeMock := store.NewMockIStore(ctrl)
	cacheMock := mock.NewMockICache(ctrl)
	configMock := mock.NewMockUserPresentGoConf(ctrl)
	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)
	detailStoreMock := store.NewMockIDetailStore(ctrl)
	numericMock := numeric_go.NewMockIClient(ctrl)
	rpc.NumericCli = numericMock

	configMock.EXPECT().CheckIgnoreDetailItems(gomock.Any()).Return(false)
	presentCacheMock.EXPECT().GetConfigById(gomock.Any()).Return(&pb.StPresentItemConfig{ItemId: 1, PriceType: 2})
	configMock.EXPECT().GetIsCheckDealToken().Return(false).AnyTimes()
	numericMock.EXPECT().GetUserRichSwitch(gomock.Any(), gomock.Any())

	storeMock.EXPECT().GetTx(gomock.Any())
	storeMock.EXPECT().ReceivePresent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	storeMock.EXPECT().SendPresent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	storeMock.EXPECT().RecordPresentMonthlyHistory(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	//storeMock.EXPECT().RecordTicketPresentMonthlyHistory(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

	detailStoreMock.EXPECT().GetTx(gomock.Any())
	detailStoreMock.EXPECT().ReceivePresentDetailRecord(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	detailStoreMock.EXPECT().SendPresentDetailRecord(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	detailStoreMock.EXPECT().RecordPresentDealToken(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

	cacheMock.EXPECT().ClearUserPresentDetail(gomock.Any(), gomock.Any())
	cacheMock.EXPECT().ClearUserPresentSendDetail(gomock.Any(), gomock.Any())

	kafkaProducerMock := mock.NewMockIKafkaProduceMgr(ctrl)

	kafkaProducerMock.EXPECT().PublishPresentEvent(gomock.Any(), gomock.Any())
	kafkaProducerMock.EXPECT().PublishPresentEventV2(gomock.Any(), gomock.Any())

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
		req *pb.SendPresentReq
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantResp *pb.SendPresentResp
		wantErr  bool
	}{
		{
			name: "Test_SendPresent",
			fields: fields{
				store:         storeMock,
				cache:         cacheMock,
				config:        configMock,
				presentCache:  presentCacheMock,
				detailStore:   detailStoreMock,
				kafkaProducer: kafkaProducerMock,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.SendPresentReq{DealToken: "{\"tradeNo\":\"0_100000318_11_0_20240311031935_413350\",\"orderID\":\"0_100000318_11_0_20240311031935_413350_249503966\",\"sign\":\"a705ce025c7e8a1b696f75e52cb6ac58\",\"ctime\":\"2024-03-11 03:19:35\",\"serverName\":\"backpack-base\",\"buyerId\":100000318,\"totalPirce\":1000,\"prevToken\":\"\",\"prevMd5\":\"\"}"},
			},
			wantResp: &pb.SendPresentResp{},
			wantErr:  false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			gotResp, err := m.SendPresent(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SendPresent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotResp, tt.wantResp) {
				t.Errorf("SendPresent() gotResp = %v, want %v", gotResp, tt.wantResp)
			}
		})
	}
}

func TestUserPresentGoMgr_BatSendPresent(t *testing.T) {
	ctrl := gomock.NewController(t)
	storeMock := store.NewMockIStore(ctrl)
	cacheMock := mock.NewMockICache(ctrl)
	configMock := mock.NewMockUserPresentGoConf(ctrl)
	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)
	detailStoreMock := store.NewMockIDetailStore(ctrl)
	numericMock := numeric_go.NewMockIClient(ctrl)
	rpc.NumericCli = numericMock

	configMock.EXPECT().CheckIgnoreDetailItems(gomock.Any()).Return(false)
	presentCacheMock.EXPECT().GetConfigById(gomock.Any()).Return(&pb.StPresentItemConfig{ItemId: 1, PriceType: 2}).AnyTimes()
	configMock.EXPECT().GetIsCheckDealToken().Return(false).AnyTimes()
	configMock.EXPECT().CheckBanIdForTest(gomock.Any()).Return(false)
	numericMock.EXPECT().GetUserRichSwitch(gomock.Any(), gomock.Any())

	storeMock.EXPECT().GetTx(gomock.Any())
	storeMock.EXPECT().BatchReceivePresent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	storeMock.EXPECT().BatchSendPresent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	storeMock.EXPECT().BatchRecordPresentMonthlyHistory(gomock.Any(), gomock.Any(), gomock.Any())
	//storeMock.EXPECT().RecordTicketPresentMonthlyHistory(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

	detailStoreMock.EXPECT().GetTx(gomock.Any())
	detailStoreMock.EXPECT().BatchReceivePresentDetailRecord(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	detailStoreMock.EXPECT().BatchSendPresentDetailRecord(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	detailStoreMock.EXPECT().RecordPresentDealTokens(gomock.Any(), gomock.Any(), gomock.Any())

	cacheMock.EXPECT().ClearUserPresentDetail(gomock.Any(), gomock.Any())
	cacheMock.EXPECT().ClearUserPresentSendDetail(gomock.Any(), gomock.Any())

	kafkaProducerMock := mock.NewMockIKafkaProduceMgr(ctrl)

	kafkaProducerMock.EXPECT().PublishPresentEvent(gomock.Any(), gomock.Any())
	kafkaProducerMock.EXPECT().PublishPresentEventV2(gomock.Any(), gomock.Any())
	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
		req *pb.BatchSendPresentReq
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantResp *pb.BatchSendPresentResp
		wantErr  bool
	}{
		{
			name: "Test_SendPresent",
			fields: fields{
				store:         storeMock,
				cache:         cacheMock,
				config:        configMock,
				presentCache:  presentCacheMock,
				detailStore:   detailStoreMock,
				kafkaProducer: kafkaProducerMock,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.BatchSendPresentReq{PresentList: []*pb.SendPresentItem{{DealToken: "{\"tradeNo\":\"0_100000318_11_0_20240311031935_413350\",\"orderID\":\"0_100000318_11_0_20240311031935_413350_249503966\",\"sign\":\"a705ce025c7e8a1b696f75e52cb6ac58\",\"ctime\":\"2024-03-11 03:19:35\",\"serverName\":\"backpack-base\",\"buyerId\":100000318,\"totalPirce\":1000,\"prevToken\":\"\",\"prevMd5\":\"\"}"}}},
			},
			wantResp: &pb.BatchSendPresentResp{},
			wantErr:  false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			gotResp, err := m.BatchSendPresent(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SendPresent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotResp, tt.wantResp) {
				t.Errorf("SendPresent() gotResp = %v, want %v", gotResp, tt.wantResp)
			}
		})
	}
}
