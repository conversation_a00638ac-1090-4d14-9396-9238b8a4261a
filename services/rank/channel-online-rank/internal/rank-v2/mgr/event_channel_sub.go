package mgr

import (
	"context"
	"fmt"
	"golang.52tt.com/protocol/services/channelol-go/event"
	"golang.52tt.com/services/rank/channel-online-rank/internal/conf"
	"golang.52tt.com/services/rank/channel-online-rank/internal/rank-v2/cache"
	"golang.52tt.com/services/rank/channel-online-rank/internal/rank-v2/model"
	"golang.52tt.com/services/rank/channel-online-rank/internal/rpc"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/metrics"
	"golang.52tt.com/pkg/protocol"
	ga "golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/services/channelonlinerank"
)

/*
enum ESIMPLE_EVENT_TYPE
{
	ENUM_SIMPLE_ENTER = 1;	//进入房间
	ENUM_SIMPLE_LEAVE = 2;	//离开房间
	ENUM_SIMPLE_EXPIRE_QUIT = 3;	// 用户由于超时事件被处理 已经退出房间

	ENUM_SIMPLE_EXPIRE_NOTIFY = 4;	// 用户超时事件发出 此时还并没有退出

	ENUM_SIMPLE_ADMIN_UPDATE = 5;	// 管理员变更事件

	ENUM_SIMPLE_MUTE = 6;	// 静音
	ENUM_SIMPLE_UNMUTE = 7;	// 解除静音
	ENUM_SIMPLE_PCHELPER_NOTIFY = 8;	// 使用PC助手
}

*/

type ChannelEv struct {
	ChannelId       uint32
	ChannelType     uint32
	OpUid           uint32
	RankUid         uint32
	IsUkw           bool
	IsDelay         bool
	EventType       uint32
	IsEnter         bool
	NobilityLevel   uint32
	NobInvisible    bool // 为true就是开启隐身
	OnlineSec       uint32
	CreateTime      uint32
	RemainMembercnt uint32
}

// 进出房
func (m *RankV2Manager) HandleChannelEventProxy(channelEv *event.ChannelOLEvent) {
	// now := time.Now()
	// defer func() {
	// 	cost := time.Since(now)
	// 	metrics2.ReportMetrics(m.sc.ChannelEventKafkaConfig.Topics, m.sc.ChannelEventKafkaConfig.GroupID, channelEv.ChId%32, cost)
	// }()

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()

	ev := &ChannelEv{
		ChannelId:   channelEv.GetCid(),
		ChannelType: channelEv.ChannelType,
		OpUid:       channelEv.Uid,
		RankUid:     channelEv.Uid,
		IsEnter:     channelEv.GetEnterEvent() != nil,
	}

	var evTime uint64
	// 进房
	if ev.IsEnter {
		opt := channelEv.GetEnterEvent()
		evTime = opt.GetTsMs()
		ev.CreateTime = uint32(opt.GetTsMs() / 1000)
		ev.NobilityLevel = opt.NobilityLevel
		ev.NobInvisible = opt.Invisible
		ev.RemainMembercnt = opt.RemainMemberCnt
	} else {
		channelLeaveOpt := channelEv.GetLeaveEvent()
		evTime = channelLeaveOpt.GetTsMs()
		ev.OnlineSec = uint32(channelLeaveOpt.GetOnlineSecond())
		ev.CreateTime = uint32(channelLeaveOpt.GetTsMs() / 1000)
		ev.RemainMembercnt = channelLeaveOpt.RemainMemberCnt
	}

	// 新旧事件幂等锁
	uniqueKey := fmt.Sprintf("channelev_%d_%d_%d", channelEv.GetCid(), channelEv.GetUid(), evTime)
	getLock, err := m.cache.Lock(uniqueKey, time.Minute)
	if err != nil {
		log.Errorf("HandleChannelEventProxy Lock err:%v, uniqueKey=%s", err, uniqueKey)
		return
	}
	if !getLock {
		log.Infof("HandleChannelEventProxy Lock ignore. uniqueKey=%s", uniqueKey)
		return
	}

	isSuperChannel := rpc.IsSuperChannel(ctx, channelEv.GetCid())

	//活动大房以真实UID展示
	if !isSuperChannel {
		rankUid, isDelay, err := m.GetRankUid(ctx, channelEv.GetUid(), channelEv.GetCid(), ga.ChannelType(channelEv.GetChannelType()))
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleChannelEventProxy GetRankUid uid:%v err:%v", channelEv.GetUid(), err)
			return
		}
		ev.IsUkw = rankUid != ev.OpUid
		ev.IsDelay = isDelay
		ev.RankUid = rankUid
	}

	// 判断是否超时
	timeoutSec := conf.GetChannelEventTimeOut(ev.IsEnter)
	if time.Now().Unix()-int64(ev.CreateTime) >= int64(timeoutSec) && !ev.IsUkw {
		log.Infof("HandleChannelEventProxy timeout ignore. ev=%+v", ev)
		m.report.SendErrorMsgThreshold("HandleChannelEventProxy timeout", "HandleChannelEventProxy", 30, 10)
		return
	}

	m.HandleChannelEvent(ev)
}

// 进出房-更新房间在线状态
func (m *RankV2Manager) HandleChannelEvent(ev *ChannelEv) {
	log.Infof("HandleChannelEvent begin diff=%ds ev=%+v", time.Now().Unix()-int64(ev.CreateTime), ev)
	cid := ev.ChannelId
	opUid := ev.OpUid
	rankUid := ev.RankUid
	channelType := ev.ChannelType
	ts := ev.CreateTime
	nobilityLevel := ev.NobilityLevel
	nobInvisible := ev.NobInvisible
	if channelType == uint32(ga.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		ts = 0 // 直播房按场次区分
	}
	ukwLevel := uint32(0)
	if rankUid != opUid {
		ukwLevel = 1
	}

	// 进房
	if ev.IsEnter {
		defer metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(0, "HandleChannelEvent.enter")).End()

		// 获取贵族隐身状态，避免上游熔断期间隐身失效
		if !nobInvisible {
			nobCtx, nobCancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
			defer nobCancel()
			nobInfo, err := rpc.NobCli.GetNobilityInfo(nobCtx, opUid, false)
			if err != nil {
				log.Errorf("HandleChannelEvent GetNobilityInfo fail %v, ev=%+v", err, ev)
			} else {
				nobInvisible = nobInfo.GetInvisible()
				nobilityLevel = nobInfo.GetLevel()
			}
		}

		// 贵族隐身和神秘人不可同时开启
		// 开启贵族隐身 进入公会房和直播房不展示在在线榜
		if (channelType == uint32(ga.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) ||
			channelType == uint32(ga.ChannelType_RADIO_LIVE_CHANNEL_TYPE)) &&
			nobilityLevel > 0 && nobInvisible {
			log.Infof("HandleChannelEvent nobInvisible ignore. opUid=%d cid=%d nobilityLevel=%d nobInvisible=%v",
				opUid, cid, nobilityLevel, nobInvisible)
			return
		}

		_, todayConsumeRecord, err := m.cache.GetRankDayRecord(cid, rankUid, ts)
		if err != nil {
			log.Errorf("HandleChannelEvent GetRankRecord fail %v, ev=%+v", err, ev)
			return
		}
		log.Debugf("HandleChannelEvent opUid=%d rankUid=%d, cid=%d cache.todayConsumeRecord=%+v ",
			opUid, rankUid, cid, todayConsumeRecord)

		exist, todayConsume, err := m.cache.GetRankTodayConsume(cid, rankUid, ts)
		if err != nil {
			log.Errorf("HandleChannelEvent GetRankRecord fail %v, ev=%+v", err, ev)
			return
		}

		// 可能会重复发进房kafka
		if exist && todayConsume >= todayConsumeRecord {
			log.Infof("HandleChannelEvent exist. opUid=%d rankUid=%d, cid=%d ctype=%d cache.todayConsumeRecord=%+v rank.todayConsume=%d",
				opUid, rankUid, cid, ev.ChannelType, todayConsumeRecord, todayConsume)
			return
		}

		ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
		defer cancel()
		// 进房查总值
		totalConsumeInfo, err := rpc.GetUserConsumeInfo(ctx, opUid, cid)
		if err != nil {
			log.Errorf("HandleChannelEvent GetUserConsumeInfo fail %v, cid=%d,opuid=%d", err, cid, opUid)
			return
		}

		// 加入排行榜
		err = m.cache.AddMember(
			cid,
			rankUid,
			ts,
			&cache.MemberInfo{
				TodayConsume:  int32(todayConsumeRecord),
				UkwLevel:      ukwLevel,
				TotalConsume:  int32(totalConsumeInfo.GetConsumeValue()),
				NobilityLevel: nobilityLevel,
			})
		if err != nil {
			log.Errorf("HandleChannelEvent AddMember fail %v, ev=%+v", err, ev)
			if err = m.report.SendError("HandleChannelEvent AddMember fail"); err != nil {
				log.Errorf("HandleChannelEvent AddMember SendError fail %v, ev=%+v", err, ev)
			}
			return
		}
	} else {

		defer metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(0, "HandleChannelEvent.quit")).End()
		var activityScore uint32
		var err error
		var needAddActivityScore = true

		// 活跃值统计方式是两个身份加起来上限为5分

		if ev.IsUkw {
			_, todayConsume, err := m.cache.GetRankTodayConsume(cid, rankUid, ts)
			if err != nil {
				log.Errorf("HandleChannelEvent GetRankTodayConsume fail %v, cid=%d rankuid=%d opuid=%d", err, cid, rankUid, opUid)
			}
			if todayConsume == 0 {
				needAddActivityScore = false
				log.Infof("HandleChannelEvent ukw left. no consume cid=%d rankuid=%d opuid=%d", cid, rankUid, opUid)
			}
		}

		if needAddActivityScore {
			activityScore, err = m.cache.IncrActivityScore(cid, opUid, ev.CreateTime, ev.OnlineSec)
			if err != nil {
				log.Errorf("HandleChannelEvent GetRankScore fail %v, ev=%+v", err, ev)
				//return
			}

			if activityScore > 0 {
				tm := time.Unix(int64(ev.CreateTime), 0)
				status := pb.ConsumeStatusType_ENUM_COMMIT
				if ukwLevel == 1 {
					if ev.IsDelay {
						status = pb.ConsumeStatusType_ENUM_DELAY // 周榜延迟更新
					} else {
						status = pb.ConsumeStatusType_ENUM_ABANDON
					}
				}

				err := m.model.RecordChannelRankPresentLog(&model.ChannelRankOrderLog{
					OrderId:     fmt.Sprintf("CHANNELOLRANK_ACTIVITY_%d_%d_%s_%d", cid, opUid, tm.Format("20060102"), ev.CreateTime),
					Uid:         opUid,
					RankUid:     opUid,
					ChannelId:   cid,
					ChannelType: uint8(channelType),
					SourceType:  uint8(pb.ChannelOnlineRankValSourceType_ACTIVITY),
					Status:      uint8(status),
					TotalPrice:  activityScore,
					OutsideTime: tm,
				})
				if err != nil {
					log.Errorf("HandleChannelEvent RecordChannelRankPresentLog fail %v, ev=%+v", err, ev)
					//return
				}

				log.Infof("HandleChannelEvent cid=%d opuid=%d rankUid=%d onlineSec=%d IncrActivityScore=%d",
					cid, opUid, rankUid, ev.OnlineSec, activityScore)

				// 更新当天在线榜缓存
				_ = m.cache.IncrRankDayRecord(cid, rankUid, ts, activityScore)

				// 更新周榜
				if !ev.IsUkw {
					_ = m.cache.AddWeekConsume(cid, opUid, ts, activityScore)
				}
			}
		}

		// 移除排行榜
		ok, err := m.cache.DelMember(cid, rankUid, ts)
		if err != nil {
			log.Errorf("HandleChannelEvent DelMember fail %v, ev=%+v", err, ev)
			if err = m.report.SendError("HandleChannelEvent DelMember fail"); err != nil {
				log.Errorf("HandleChannelEvent DelMember SendError fail %v, ev=%+v", err, ev)
			}
			return
		}
		log.Infof("HandleChannelEvent quit. opUid=%d rankUid=%d cid=%d ctype=%d ok=%v", opUid, rankUid, cid, channelType, ok)

		// 退房却不在榜
		if !ok {
			log.Warnf("HandleChannelEvent quit abnormal, uid=%d cid=%d ctype=%d", opUid, cid, channelType)

			// 1.进房事件丢了
			// 2.贵族隐身进房,没有上榜

		}
	}

	if ev.RemainMembercnt > 0 {
		_ = m.CheckAndRefreshTop3(opUid, rankUid, ts, cid, channelType, "进出房")
	}

	// 在线榜人数比实际房间人数多 或者房间人数比较少 触发检查
	rankMemSize, totalConsumeSize, specialSize, _ := m.cache.GetRankMemberSize(cid, ts)
	if rankMemSize > ev.RemainMembercnt ||
		totalConsumeSize > ev.RemainMembercnt ||
		specialSize > ev.RemainMembercnt {
		log.Errorf("HandleChannelEvent check hit. rankMemSize=%d, totalConsumeSize=%d, specialSize=%d RemainMembercnt(%d) cid=%d ctype=%d",
			rankMemSize, totalConsumeSize, specialSize, ev.RemainMembercnt, cid, channelType)
		go m.checkChannelOnlineMember(cid, channelType)
	}

	if ev.RemainMembercnt < conf.GetCheckChannelOlMemMinSize() {
		go m.checkChannelOnlineMember(cid, channelType)
	}
}
