package manager

import (
	"bytes"
	"encoding/json"
	"golang.52tt.com/services/channel-live-mgr/conf"
	"io/ioutil"
	"net/http"
	"time"

	"golang.52tt.com/pkg/log"
)

//从前端接口获取主播排行榜数据
func (m *Manager) GetPkRankScoreFromWeb(uidList []uint32) map[uint32]uint32 {
	log.Debugf("GetPkRankScoreFromWeb begin uidList:%v", uidList)

	mapUid2Score := make(map[uint32]uint32, 0)
	postReq := &struct {
		UidList   []uint32    `json:"uidList"`
	}{}

	postReq.UidList = uidList

	httpCli := &http.Client{
		Timeout:       500 * time.Millisecond,
	}

	postBodeByte, err := json.Marshal(postReq)
	if err != nil {
		log.Errorf("GetPkRankScoreFromWeb json.Marshal failed req:%v err:%v", postReq, err)
		return mapUid2Score
	}

	postResp, err := httpCli.Post(conf.GetWebUrl(), "application/json", bytes.NewReader(postBodeByte))
	if err != nil {
		log.Errorf("GetPkRankScoreFromWeb httpCli.Post failed req:%v err:%v", postReq, err)
		return mapUid2Score
	}

	if postResp.StatusCode != http.StatusOK {
		log.Errorf("GetPkRankScoreFromWeb http code no 200 req:%v byte:%v code:%d", postReq, string(postBodeByte), postResp.StatusCode)
		return mapUid2Score
	}

	defer postResp.Body.Close()
	respBody, err := ioutil.ReadAll(postResp.Body)
	if err != nil {
		log.Errorf("GetPkRankScoreFromWeb ioutil.ReadAll failed req:%v err:%v", postReq, err)
		return mapUid2Score
	}

	respResult := &struct {
		Code   uint32    `json:"code"`
		Msg    string    `json:"msg"`
		Data   struct{
			UserScoreList  []struct{
				Uid   uint32   `json:"uid"`
				Point uint32   `json:"point"`
			} `json:"list"`
		}  `json:"data"`
	}{}

	err = json.Unmarshal(respBody, respResult)
	if err != nil {
		log.Errorf("GetPkRankScoreFromWeb json.Unmarshal(respBody, respResult) failed req:%v err:%+v", postReq, err)
		return mapUid2Score
	}

	log.Debugf("GetPkRankScoreFromWeb mapUid2Score uidList:%v result:%v", uidList, respResult)

	if respResult.Code != 0 {
		log.Errorf("GetPkRankScoreFromWeb failed uidList:%v code:%d msg:%s", uidList, respResult.Code, respResult.Msg)
		return mapUid2Score
	}

	for _, info := range respResult.Data.UserScoreList {
		mapUid2Score[info.Uid] = info.Point
	}

	log.Debugf("GetPkRankScoreFromWeb end uidList:%v result:%v", uidList, mapUid2Score)
	return mapUid2Score
}
