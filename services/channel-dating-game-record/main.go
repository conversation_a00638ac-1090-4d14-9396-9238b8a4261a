package main

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	pb "golang.52tt.com/protocol/services/channel-dating-game-record"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/channel-dating-game-record/internal"
	"golang.52tt.com/services/channel-dating-game-record/internal/conf"

	// use server startup
	"gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server" // 兼容tyr公共库
)

func main() {
	var (
		svr *internal.Server
		cfg = &conf.StartConfig{}
		err error
	)

	// config file support yaml & json, default channel-dating-game-record.json/yaml
	if err := server.NewServer("channel-dating-game-record", cfg).
		AddGrpcServer(grpc.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				kafka.InitEventLinkSubWithGrpcSvr(s)

				if svr, err = internal.NewServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterChannelDatingGameRecordServer(s, svr)

				// grpcurl -plaintext -d '{"value":"hello"}' 127.0.0.1:80 demo.echo.EchoService.Echo
				// grpcurl -plaintext 127.0.0.1:80 grpc.health.v1.Health.Check
				echo.RegisterEchoServiceServer(s, svr)
				log.InfoWithCtx(ctx, "RegisterSvr finish")
				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			//   svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
