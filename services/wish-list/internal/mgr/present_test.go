package mgr

import (
	"context"
	"golang.52tt.com/clients/account"
	channel_msg_express "golang.52tt.com/clients/channel-msg-express"
	magic_spirit "golang.52tt.com/clients/magic-spirit"
	"golang.52tt.com/pkg/active_present"
	"golang.52tt.com/protocol/app/wishlistlogic"
	userpresent "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/wish-list/internal/cache"
	dyConfig "golang.52tt.com/services/wish-list/internal/config/ttconfig/wish_list"
	"golang.52tt.com/services/wish-list/internal/event"
	"golang.52tt.com/services/wish-list/internal/store"
	"testing"
)

var TestPresentConfig = &userpresent.StPresentItemConfig{
	ItemId:      1,
	Name:        "",
	IconUrl:     "",
	Price:       0,
	Score:       0,
	Charm:       0,
	Rank:        0,
	EffectBegin: **********,
	EffectEnd:   **********,
	UpdateTime:  0,
	CreateTime:  0,
	IsDel:       false,
	PriceType:   2,
	RichValue:   0,
	RankFloat:   0,
	Extend: &userpresent.StPresentItemConfigExtend{
		ItemId: 1,
	},
}

func TestManager_checkPresentValid(t *testing.T) {
	presentListCacheMap = map[uint32]*userpresent.StPresentItemConfig{TestPresentConfig.ItemId: TestPresentConfig}
	customPresentListCache = []*userpresent.StPresentItemConfig{TestPresentConfig}

	type fields struct {
		store             store.IStore
		cache             cache.ICache
		dyCfg             *dyConfig.WishListDyConfig
		channelLiveSub    *event.ChannelLiveEventLinkSub
		presentSub        *event.PresentEventLinkSub
		ykwSub            *event.YKWEventLinkSub
		wishListProd      *event.WishListProducer
		magicSpiritCli    magic_spirit.IClient
		channelMsgPreCli  channel_msg_express.IClient
		accountCli        account.IClient
		actorPresentDyCfg active_present.ActivePresentCfgHandle
	}
	type args struct {
		ctx      context.Context
		giftType wishlistlogic.WishGiftType
		giftId   uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "checkPresentValid",
			fields: fields{},
			args: args{
				ctx:      nil,
				giftType: 0,
				giftId:   1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				store:             tt.fields.store,
				cache:             tt.fields.cache,
				dyCfg:             tt.fields.dyCfg,
				channelLiveSub:    tt.fields.channelLiveSub,
				presentSub:        tt.fields.presentSub,
				ykwSub:            tt.fields.ykwSub,
				wishListProd:      tt.fields.wishListProd,
				magicSpiritCli:    tt.fields.magicSpiritCli,
				channelMsgPreCli:  tt.fields.channelMsgPreCli,
				accountCli:        tt.fields.accountCli,
				actorPresentDyCfg: tt.fields.actorPresentDyCfg,
			}
			if err := m.checkPresentValid(tt.args.ctx, tt.args.giftType, tt.args.giftId); (err != nil) != tt.wantErr {
				t.Errorf("checkPresentValid() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_RefreshCustomPresentListCache(t *testing.T) {
	type fields struct {
		store             store.IStore
		cache             cache.ICache
		dyCfg             *dyConfig.WishListDyConfig
		channelLiveSub    *event.ChannelLiveEventLinkSub
		presentSub        *event.PresentEventLinkSub
		ykwSub            *event.YKWEventLinkSub
		wishListProd      *event.WishListProducer
		magicSpiritCli    magic_spirit.IClient
		channelMsgPreCli  channel_msg_express.IClient
		accountCli        account.IClient
		actorPresentDyCfg active_present.ActivePresentCfgHandle
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "RefreshCustomPresentListCache",
			fields: fields{
				store:             nil,
				cache:             nil,
				dyCfg:             nil,
				channelLiveSub:    nil,
				presentSub:        nil,
				ykwSub:            nil,
				wishListProd:      nil,
				magicSpiritCli:    nil,
				channelMsgPreCli:  nil,
				accountCli:        nil,
				actorPresentDyCfg: nil,
			},
			args: args{
				ctx: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				store:             tt.fields.store,
				cache:             tt.fields.cache,
				dyCfg:             tt.fields.dyCfg,
				channelLiveSub:    tt.fields.channelLiveSub,
				presentSub:        tt.fields.presentSub,
				ykwSub:            tt.fields.ykwSub,
				wishListProd:      tt.fields.wishListProd,
				magicSpiritCli:    tt.fields.magicSpiritCli,
				channelMsgPreCli:  tt.fields.channelMsgPreCli,
				accountCli:        tt.fields.accountCli,
				actorPresentDyCfg: tt.fields.actorPresentDyCfg,
			}
			if err := m.RefreshCustomPresentListCache(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("RefreshCustomPresentListCache() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
