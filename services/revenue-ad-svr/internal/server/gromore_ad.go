package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/revenue_ad_svr"
	"golang.52tt.com/services/revenue-ad-svr/internal/rpc"
	"time"
)

func (s *Server) GetGromoreAdInfos(adPosId string) *rpc.GromoreAdUnit {
	s.gromoreAdUnitsLock.RLock()
	defer s.gromoreAdUnitsLock.RUnlock()
	if adUnit, ok := s.gromoreAdUnits[adPosId]; ok {
		return adUnit
	}
	return nil
}

// ReloadGromoreAdUnits 重新同步gromore广告位信息
func (s *Server) ReloadGromoreAdUnits() {
	dycf := s.dyconf.Get()
	cf, ok := dycf.AdPlatormTypesMap[uint32(pb.AdPlatformType_AD_PLATFORM_TYPE_GROMORE)]
	if !ok {
		return
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	if !cf.Enabled {
		log.InfoWithCtx(ctx, "ReloadGromoreAdUnits gromore ad platform is disabled")
		return
	}
	if time.Since(s.gromoreAdUpdateTime) < time.Second*30 {
		log.InfoWithCtx(ctx, "ReloadGromoreAdUnits gromore ad update time is cool down")
		return
	}
	appIds := make([]string, 0)
	appIdMaps := make(map[string]struct{})
	for _, appid := range cf.IosAppIds {
		if _, ok := appIdMaps[appid]; ok {
			continue
		}
		appIds = append(appIds, appid)
		appIdMaps[appid] = struct{}{}
	}
	for _, appid := range cf.AndroidAppIds {
		if _, ok := appIdMaps[appid]; ok {
			continue
		}
		appIds = append(appIds, appid)
		appIdMaps[appid] = struct{}{}
	}

	s.gromoreAdUpdateTime = time.Now()
	adUnits := make(map[string]*rpc.GromoreAdUnit)
	for _, appid := range appIds {
		units, err := s.gromoreApiCli.GetAdUnits(ctx, appid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReloadGromoreAdUnits Failed to GetAdUnits appid:%s err: (%v)", appid, err)
			continue
		}

		for posId, adUnit := range units {
			if dycf.IsSupportAdType(adUnit.GetAdType()) {
				adUnits[posId] = adUnit
			}
		}
	}
	log.InfoWithCtx(ctx, "ReloadGromoreAdUnits ok adUnits:%+v", adUnits)
	s.gromoreAdUnitsLock.Lock()
	s.gromoreAdUnits = adUnits
	s.gromoreAdUnitsLock.Unlock()
}
