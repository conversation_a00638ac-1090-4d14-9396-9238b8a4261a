package mgr

import (
	"context"
	"github.com/golang/mock/gomock"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_layout"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_layout_conf_mgr"
	channellivemgr "golang.52tt.com/clients/channel-live-mgr"
	channel_msg_express "golang.52tt.com/clients/channel-msg-express"
	present_extra_conf "golang.52tt.com/clients/present-extra-conf"
	user_profile_api "golang.52tt.com/clients/user-profile-api"
	userpresent_go "golang.52tt.com/clients/userpresent-go"
	youknowwho "golang.52tt.com/clients/you-know-who"
	ga "golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/time_present"
	channel_live_mgr "golang.52tt.com/protocol/services/channellivemgr"
	pb "golang.52tt.com/protocol/services/time-present"
	"golang.52tt.com/services/time-present/internal/cache"
	"golang.52tt.com/services/time-present/internal/conf"
	"golang.52tt.com/services/time-present/internal/store"
	"golang.52tt.com/services/time-present/internal/utils"
	"math/rand"
	"reflect"
	"testing"
	"time"
)

func Test_manager_AddChannelLiveIntimatePresent(t *testing.T) {
	type fields struct {
		cache                 cache.Cache
		store                 store.Store
		timePresentLocalCache TimePresentLocalCache
		randSource            rand.Source
		dyConfig              conf.TimePresent
		presentCli            userpresent_go.IClient
		presentExtraCli       present_extra_conf.IClient
		userProfileApiCli     user_profile_api.IClient
		micTemplateCli        channel_mic_layout_conf_mgr.ChannelMicLayoutConfMgrClient
		channelMsgExpressCli  channel_msg_express.IClient
		micLayoutCli          channel_mic_layout.ChannelMicLayoutClient
		youKnowWhoCli         youknowwho.IClient
		channelLiveMgrCli     channellivemgr.IClient
		reporter              *utils.FeishuReporterV2
		timerD                *timer.Timer
	}
	type args struct {
		c   context.Context
		req *pb.AddChannelLiveIntimatePresentReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.AddChannelLiveIntimatePresentResp
		wantErr bool
	}{
		{
			name: "Test_AddChannelLiveIntimatePresent_Success",
			args: args{
				c: context.TODO(),
				req: &pb.AddChannelLiveIntimatePresentReq{
					ChannelId: 1000,
					OrderId:   "test",
					ItemId:    1,
					Items: []*pb.AddTimePresentItem{{
						OrderId: "test_1",
						FromUid: 11,
						ToUid:   12,
						MicIdList: []*pb.TimePresentMicReflect{
							{
								MicId: 1,
								Uid:   11,
							},
						},
					}},
				},
			},
			want:    &pb.AddChannelLiveIntimatePresentResp{ /* 填写期望的响应 */ },
			wantErr: false,
		},
	}
	for _, tt := range tests {

		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)
			m.getCacheMock().EXPECT().LockChannelLiveIntimatePresent(gomock.Any(), gomock.Any()).Return(nil)
			m.getCacheMock().EXPECT().CheckIntimateOrderId(gomock.Any(), gomock.Any()).Return(true, nil)
			m.getCacheMock().EXPECT().UnlockChannelLiveIntimatePresent(gomock.Any(), gomock.Any()).Return(nil)
			m.getStoreMock().EXPECT().GetLiveIntimatePresent(gomock.Any(), gomock.Any()).Return(&store.LiveIntimatePresent{ItemID: 1}, nil)
			m.getCacheMock().EXPECT().GetChannelLiveIntimatePresentFromQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			m.getCacheMock().EXPECT().AddChannelLiveIntimatePresentToQueue(gomock.Any(), gomock.Any()).Return(nil)
			m.getDyConfigMock().EXPECT().GetLiveIntimatePresentTransferTime(gomock.Any()).Return(uint32(1)).AnyTimes()
			m.getDyConfigMock().EXPECT().CheckIntimatePresentSwitch(gomock.Any(), gomock.Any()).Return(true).AnyTimes()
			m.getDyConfigMock().EXPECT().GetFirstPresentDelayTime(gomock.Any()).Return(uint32(1)).AnyTimes()
			//m.getChannelMsgExpressCliMock().EXPECT().SendChannelBroadcastMsg(gomock.Any(), gomock.Any()).Return(nil)

			got, err := m.AddChannelLiveIntimatePresent(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddChannelLiveIntimatePresent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddChannelLiveIntimatePresent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_manager_ClearChannelLiveIntimatePresentQueue(t *testing.T) {
	type fields struct {
		cache                 cache.Cache
		store                 store.Store
		timePresentLocalCache TimePresentLocalCache
		randSource            rand.Source
		dyConfig              conf.TimePresent
		presentCli            userpresent_go.IClient
		presentExtraCli       present_extra_conf.IClient
		userProfileApiCli     user_profile_api.IClient
		micTemplateCli        channel_mic_layout_conf_mgr.ChannelMicLayoutConfMgrClient
		channelMsgExpressCli  channel_msg_express.IClient
		micLayoutCli          channel_mic_layout.ChannelMicLayoutClient
		youKnowWhoCli         youknowwho.IClient
		channelLiveMgrCli     channellivemgr.IClient
		reporter              *utils.FeishuReporterV2
		timerD                *timer.Timer
	}
	type args struct {
		ctx       context.Context
		channelId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "Test_ClearChannelLiveIntimatePresentQueue_Success",
			args: args{
				ctx:       context.TODO(),
				channelId: 1000,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)

			ctx := context.TODO()
			channelId := uint32(1000)
			presentList := []*cache.LiveIntimatePresentItem{
				{
					IntimatePresentItem: pb.IntimatePresentItem{
						Id:        1,
						ItemId:    1,
						FromUser:  []byte("fromUser"),
						ToUser:    []byte("toUser"),
						BeginTime: 1234567890,
						EndTime:   1234567890,
					},
					OrderId: "orderId",
				},
			}

			m.getCacheMock().EXPECT().GetChannelLiveIntimatePresentFromQueue(ctx, channelId, gomock.Any(), true).Return(presentList, nil)
			m.getCacheMock().EXPECT().ClearChannelLiveIntimatePresentQueue(ctx, channelId, presentList).Return(nil)

			if err := m.ClearChannelLiveIntimatePresentQueue(tt.args.ctx, tt.args.channelId); (err != nil) != tt.wantErr {
				t.Errorf("ClearChannelLiveIntimatePresentQueue() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_manager_GetChannelLiveIntimatePresentList(t *testing.T) {
	type fields struct {
		cache                 cache.Cache
		store                 store.Store
		timePresentLocalCache TimePresentLocalCache
		randSource            rand.Source
		dyConfig              conf.TimePresent
		presentCli            userpresent_go.IClient
		presentExtraCli       present_extra_conf.IClient
		userProfileApiCli     user_profile_api.IClient
		micTemplateCli        channel_mic_layout_conf_mgr.ChannelMicLayoutConfMgrClient
		channelMsgExpressCli  channel_msg_express.IClient
		micLayoutCli          channel_mic_layout.ChannelMicLayoutClient
		youKnowWhoCli         youknowwho.IClient
		channelLiveMgrCli     channellivemgr.IClient
		reporter              *utils.FeishuReporterV2
		timerD                *timer.Timer
	}
	type args struct {
		c   context.Context
		req *pb.GetChannelLiveIntimatePresentListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetChannelLiveIntimatePresentListResp
		wantErr bool
	}{{
		name: "Test_GetChannelLiveIntimatePresentList_Success",
		args: args{
			c: context.TODO(),
			req: &pb.GetChannelLiveIntimatePresentListReq{
				ChannelId:       1000,
				PkChannelIdList: []uint32{2000, 3000},
			},
		},
		want: &pb.GetChannelLiveIntimatePresentListResp{
			Items: []*pb.IntimatePresentItem{
				{
					Id:         1,
					ItemId:     1,
					FromUser:   []byte("fromUser"),
					ToUser:     []byte("toUser"),
					BeginTime:  1234567890,
					EndTime:    1234567890,
					DuringTime: 60,
				},
			},
			LiveIntimatePresentInfo: &pb.LiveIntimatePresentInfo{
				ChannelInfo: []*pb.LiveIntimatePresentChannelInfo{
					{
						ChannelId:      1000,
						PresentId:      1,
						FromUser:       []byte("fromUser"),
						ToUser:         []byte("toUser"),
						LastChangeTime: 1234567890,
					},
					{
						ChannelId:      2000,
						PresentId:      2,
						FromUser:       []byte("fromUser2"),
						ToUser:         []byte("toUser2"),
						LastChangeTime: 1234567891,
					},
					{
						ChannelId:      3000,
						PresentId:      3,
						FromUser:       []byte("fromUser3"),
						ToUser:         []byte("toUser3"),
						LastChangeTime: 1234567892,
					},
				},
			},
		},
		wantErr: false,
	}}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)

			m.getCacheMock().EXPECT().GetChannelLiveIntimatePresentFromQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*cache.LiveIntimatePresentItem{
				{
					IntimatePresentItem: pb.IntimatePresentItem{
						Id:         1,
						ItemId:     1,
						FromUser:   []byte("fromUser"),
						ToUser:     []byte("toUser"),
						BeginTime:  1234567890,
						EndTime:    1234567890,
						DuringTime: 60,
					},
					OrderId: "orderId",
				},
			}, nil)
			m.getCacheMock().EXPECT().BatchGetIntimateChangeTime(gomock.Any(), gomock.Any()).Return(map[uint32]int64{
				1000: 1234567890,
				2000: 1234567891,
				3000: 1234567892,
			}, nil)
			m.getCacheMock().EXPECT().GetChannelLiveIntimatePresentNow(gomock.Any(), uint32(2000), gomock.Any()).Return(&cache.LiveIntimatePresentItem{
				IntimatePresentItem: pb.IntimatePresentItem{
					Id:         2,
					ItemId:     2,
					FromUser:   []byte("fromUser2"),
					ToUser:     []byte("toUser2"),
					BeginTime:  1234567891,
					EndTime:    1234567891,
					DuringTime: 60,
				},
				OrderId: "orderId2",
			}, nil)

			m.getCacheMock().EXPECT().GetChannelLiveIntimatePresentNow(gomock.Any(), uint32(3000), gomock.Any()).Return(&cache.LiveIntimatePresentItem{
				IntimatePresentItem: pb.IntimatePresentItem{
					Id:         3,
					ItemId:     3,
					FromUser:   []byte("fromUser3"),
					ToUser:     []byte("toUser3"),
					BeginTime:  1234567892,
					EndTime:    1234567892,
					DuringTime: 60,
				},
				OrderId: "orderId3",
			}, nil)
			m.getDyConfigMock().EXPECT().GetFirstPresentDelayTime(gomock.Any()).Return(uint32(1)).AnyTimes()

			got, err := m.GetChannelLiveIntimatePresentList(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChannelLiveIntimatePresentList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetChannelLiveIntimatePresentList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_manager_HandleAllBeginChannelLiveIntimatePresent(t *testing.T) {
	type fields struct {
		cache                 cache.Cache
		store                 store.Store
		timePresentLocalCache TimePresentLocalCache
		randSource            rand.Source
		dyConfig              conf.TimePresent
		presentCli            userpresent_go.IClient
		presentExtraCli       present_extra_conf.IClient
		userProfileApiCli     user_profile_api.IClient
		micTemplateCli        channel_mic_layout_conf_mgr.ChannelMicLayoutConfMgrClient
		channelMsgExpressCli  channel_msg_express.IClient
		micLayoutCli          channel_mic_layout.ChannelMicLayoutClient
		youKnowWhoCli         youknowwho.IClient
		channelLiveMgrCli     channellivemgr.IClient
		reporter              *utils.FeishuReporterV2
		timerD                *timer.Timer
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "Test_HandleAllBeginChannelLiveIntimatePresent_Success",
			args: args{
				ctx: context.TODO(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)
			ctx := context.TODO()
			timestamp := time.Now().Unix()

			liveIntimatePresentList := []*cache.LiveIntimatePresentItem{
				{
					IntimatePresentItem: pb.IntimatePresentItem{
						Id:         1,
						ItemId:     1,
						FromUser:   []byte("fromUser"),
						ToUser:     []byte("toUser"),
						BeginTime:  1234567890,
						EndTime:    1234567890,
						DuringTime: 60,
					},
					OrderId: "orderId",
				},
			}

			m.getCacheMock().EXPECT().GetAllBeginChannelLiveIntimatePresentFromQueue(ctx, timestamp).Return(liveIntimatePresentList, nil)
			m.getCacheMock().EXPECT().RemoveChannelBeginLiveIntimatePresentFromQueue(ctx, gomock.Any()).Return(nil).AnyTimes()
			m.getCacheMock().EXPECT().GetChannelLiveIntimatePresentFromQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(liveIntimatePresentList, nil).AnyTimes()
			m.getUserProfileApiCliMock().EXPECT().BatchGetUserProfile(ctx, gomock.Any()).Return(map[uint32]*ga.UserProfile{}, nil).AnyTimes()
			m.getYouKnowWhoCliMock().EXPECT().BatchGetMapTrueUidByFake(ctx, gomock.Any()).Return(map[uint32]uint32{}).AnyTimes()
			m.getChannelMsgExpressCliMock().EXPECT().SendChannelBroadcastMsg(ctx, gomock.Any()).Return(nil).AnyTimes()
			m.getDyConfigMock().EXPECT().GetIntimateChannelMsgContent().Return(&conf.ChannelMsgContent{}).AnyTimes()

			m.HandleAllBeginChannelLiveIntimatePresent(tt.args.ctx)
		})
	}
}

func Test_manager_HandleAllEndChannelLiveIntimatePresent(t *testing.T) {
	type fields struct {
		cache                 cache.Cache
		store                 store.Store
		timePresentLocalCache TimePresentLocalCache
		randSource            rand.Source
		dyConfig              conf.TimePresent
		presentCli            userpresent_go.IClient
		presentExtraCli       present_extra_conf.IClient
		userProfileApiCli     user_profile_api.IClient
		micTemplateCli        channel_mic_layout_conf_mgr.ChannelMicLayoutConfMgrClient
		channelMsgExpressCli  channel_msg_express.IClient
		micLayoutCli          channel_mic_layout.ChannelMicLayoutClient
		youKnowWhoCli         youknowwho.IClient
		channelLiveMgrCli     channellivemgr.IClient
		reporter              *utils.FeishuReporterV2
		timerD                *timer.Timer
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "Test_HandleAllEndChannelLiveIntimatePresent_Success",
			args: args{
				ctx: context.TODO(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)

			ctx := context.TODO()
			liveIntimatePresent := &cache.LiveIntimatePresentItem{
				IntimatePresentItem: pb.IntimatePresentItem{
					Id:         1,
					ItemId:     1,
					FromUser:   []byte("fromUser"),
					ToUser:     []byte("toUser"),
					BeginTime:  1234567890,
					EndTime:    1234567890,
					DuringTime: 60,
				},
				OrderId: "orderId",
			}

			m.getCacheMock().EXPECT().GetAllEndChannelLiveIntimatePresentFromQueue(ctx, gomock.Any()).Return([]*cache.LiveIntimatePresentItem{liveIntimatePresent}, nil)
			m.getCacheMock().EXPECT().RemoveChannelEndLiveIntimatePresentFromQueue(ctx, liveIntimatePresent).Return(nil)
			m.getCacheMock().EXPECT().GetChannelLiveIntimatePresentFromQueue(gomock.Any(), liveIntimatePresent.GetChannelId(), gomock.Any(), true).Return([]*cache.LiveIntimatePresentItem{liveIntimatePresent}, nil)
			m.getYouKnowWhoCliMock().EXPECT().BatchGetMapTrueUidByFake(ctx, gomock.Any()).Return(map[uint32]uint32{})
			m.getUserProfileApiCliMock().EXPECT().BatchGetUserProfile(ctx, gomock.Any()).Return(map[uint32]*ga.UserProfile{}, nil)
			m.getChannelMsgExpressCliMock().EXPECT().SendChannelBroadcastMsg(ctx, gomock.Any()).Return(nil).AnyTimes()
			m.getDyConfigMock().EXPECT().GetIntimateChannelMsgEndContent().Return(&conf.ChannelMsgContent{}).AnyTimes()

			m.HandleAllEndChannelLiveIntimatePresent(tt.args.ctx)
		})
	}
}

func Test_manager_HandleChannelLiveIntimatePresentBegin(t *testing.T) {
	type fields struct {
		cache                 cache.Cache
		store                 store.Store
		timePresentLocalCache TimePresentLocalCache
		randSource            rand.Source
		dyConfig              conf.TimePresent
		presentCli            userpresent_go.IClient
		presentExtraCli       present_extra_conf.IClient
		userProfileApiCli     user_profile_api.IClient
		micTemplateCli        channel_mic_layout_conf_mgr.ChannelMicLayoutConfMgrClient
		channelMsgExpressCli  channel_msg_express.IClient
		micLayoutCli          channel_mic_layout.ChannelMicLayoutClient
		youKnowWhoCli         youknowwho.IClient
		channelLiveMgrCli     channellivemgr.IClient
		reporter              *utils.FeishuReporterV2
		timerD                *timer.Timer
	}
	type args struct {
		ctx                 context.Context
		LiveIntimatePresent *cache.LiveIntimatePresentItem
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "Test_HandleChannelLiveIntimatePresentBegin_Success",
			args: args{
				ctx: context.TODO(),
				LiveIntimatePresent: &cache.LiveIntimatePresentItem{
					IntimatePresentItem: pb.IntimatePresentItem{
						Id:         1,
						ItemId:     1,
						FromUser:   []byte("fromUser"),
						ToUser:     []byte("toUser"),
						BeginTime:  1234567890,
						EndTime:    1234567890,
						DuringTime: 60,
					},
					OrderId: "orderId",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)

			liveIntimatePresent := &cache.LiveIntimatePresentItem{
				IntimatePresentItem: pb.IntimatePresentItem{
					Id:         1,
					ItemId:     1,
					FromUser:   []byte("fromUser"),
					ToUser:     []byte("toUser"),
					BeginTime:  1234567890,
					EndTime:    1234567890,
					DuringTime: 60,
				},
				OrderId: "orderId",
			}
			m.getCacheMock().EXPECT().GetChannelLiveIntimatePresentFromQueue(gomock.Any(), liveIntimatePresent.GetChannelId(), gomock.Any(), gomock.Any()).Return(nil, nil)
			m.getCacheMock().EXPECT().RemoveChannelBeginLiveIntimatePresentFromQueue(gomock.Any(), liveIntimatePresent).Return(nil)
			m.getYouKnowWhoCliMock().EXPECT().BatchGetMapTrueUidByFake(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{1: 1, 2: 2})
			m.getUserProfileApiCliMock().EXPECT().BatchGetUserProfile(gomock.Any(), gomock.Any()).Return(map[uint32]*ga.UserProfile{1: {Uid: 1}, 2: {Uid: 2}}, nil)
			m.getChannelMsgExpressCliMock().EXPECT().SendChannelBroadcastMsg(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			m.getChannelLiveMgrCliMock().EXPECT().BatchGetChannelLiveStatusSimple(gomock.Any(), gomock.Any()).Return(&channel_live_mgr.BatchGetChannelLiveStatusSimpleResp{}, nil).AnyTimes()
			m.getCacheMock().EXPECT().BatchGetIntimateChangeTime(gomock.Any(), gomock.Any()).Return(map[uint32]int64{1: 1234567890}, nil)
			m.getTimePresentLocalCacheMock().EXPECT().GetIntimatePresentUpdateTime().Return(uint32(1)).AnyTimes()
			m.getDyConfigMock().EXPECT().GetIntimateChannelMsgContent().Return(&conf.ChannelMsgContent{}).AnyTimes()
			if err := m.HandleChannelLiveIntimatePresentBegin(tt.args.ctx, tt.args.LiveIntimatePresent); (err != nil) != tt.wantErr {
				t.Errorf("HandleChannelLiveIntimatePresentBegin() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_manager_HandleChannelLiveIntimatePresentEnd(t *testing.T) {
	type fields struct {
		cache                 cache.Cache
		store                 store.Store
		timePresentLocalCache TimePresentLocalCache
		randSource            rand.Source
		dyConfig              conf.TimePresent
		presentCli            userpresent_go.IClient
		presentExtraCli       present_extra_conf.IClient
		userProfileApiCli     user_profile_api.IClient
		micTemplateCli        channel_mic_layout_conf_mgr.ChannelMicLayoutConfMgrClient
		channelMsgExpressCli  channel_msg_express.IClient
		micLayoutCli          channel_mic_layout.ChannelMicLayoutClient
		youKnowWhoCli         youknowwho.IClient
		channelLiveMgrCli     channellivemgr.IClient
		reporter              *utils.FeishuReporterV2
		timerD                *timer.Timer
	}
	type args struct {
		ctx                 context.Context
		LiveIntimatePresent *cache.LiveIntimatePresentItem
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &manager{
				cache:                 tt.fields.cache,
				store:                 tt.fields.store,
				timePresentLocalCache: tt.fields.timePresentLocalCache,
				randSource:            tt.fields.randSource,
				dyConfig:              tt.fields.dyConfig,
				presentCli:            tt.fields.presentCli,
				presentExtraCli:       tt.fields.presentExtraCli,
				userProfileApiCli:     tt.fields.userProfileApiCli,
				micTemplateCli:        tt.fields.micTemplateCli,
				channelMsgExpressCli:  tt.fields.channelMsgExpressCli,
				micLayoutCli:          tt.fields.micLayoutCli,
				youKnowWhoCli:         tt.fields.youKnowWhoCli,
				channelLiveMgrCli:     tt.fields.channelLiveMgrCli,
				reporter:              tt.fields.reporter,
				timerD:                tt.fields.timerD,
			}
			if err := m.HandleChannelLiveIntimatePresentEnd(tt.args.ctx, tt.args.LiveIntimatePresent); (err != nil) != tt.wantErr {
				t.Errorf("HandleChannelLiveIntimatePresentEnd() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_manager_SendLiveIntimatePresentListUpdateImPush(t *testing.T) {
	type fields struct {
		cache                 cache.Cache
		store                 store.Store
		timePresentLocalCache TimePresentLocalCache
		randSource            rand.Source
		dyConfig              conf.TimePresent
		presentCli            userpresent_go.IClient
		presentExtraCli       present_extra_conf.IClient
		userProfileApiCli     user_profile_api.IClient
		micTemplateCli        channel_mic_layout_conf_mgr.ChannelMicLayoutConfMgrClient
		channelMsgExpressCli  channel_msg_express.IClient
		micLayoutCli          channel_mic_layout.ChannelMicLayoutClient
		youKnowWhoCli         youknowwho.IClient
		channelLiveMgrCli     channellivemgr.IClient
		reporter              *utils.FeishuReporterV2
		timerD                *timer.Timer
	}
	type args struct {
		ctx                 context.Context
		LiveIntimatePresent *cache.LiveIntimatePresentItem
		isBegin             bool
		realtimeFromUid     uint32
		realtimeToUid       uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &manager{
				cache:                 tt.fields.cache,
				store:                 tt.fields.store,
				timePresentLocalCache: tt.fields.timePresentLocalCache,
				randSource:            tt.fields.randSource,
				dyConfig:              tt.fields.dyConfig,
				presentCli:            tt.fields.presentCli,
				presentExtraCli:       tt.fields.presentExtraCli,
				userProfileApiCli:     tt.fields.userProfileApiCli,
				micTemplateCli:        tt.fields.micTemplateCli,
				channelMsgExpressCli:  tt.fields.channelMsgExpressCli,
				micLayoutCli:          tt.fields.micLayoutCli,
				youKnowWhoCli:         tt.fields.youKnowWhoCli,
				channelLiveMgrCli:     tt.fields.channelLiveMgrCli,
				reporter:              tt.fields.reporter,
				timerD:                tt.fields.timerD,
			}
			if err := m.SendLiveIntimatePresentListUpdateImPush(tt.args.ctx, tt.args.LiveIntimatePresent, tt.args.isBegin, tt.args.realtimeFromUid, tt.args.realtimeToUid); (err != nil) != tt.wantErr {
				t.Errorf("SendLiveIntimatePresentListUpdateImPush() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_manager_SendLiveIntimatePresentListUpdatePush(t *testing.T) {
	type fields struct {
		cache                 cache.Cache
		store                 store.Store
		timePresentLocalCache TimePresentLocalCache
		randSource            rand.Source
		dyConfig              conf.TimePresent
		presentCli            userpresent_go.IClient
		presentExtraCli       present_extra_conf.IClient
		userProfileApiCli     user_profile_api.IClient
		micTemplateCli        channel_mic_layout_conf_mgr.ChannelMicLayoutConfMgrClient
		channelMsgExpressCli  channel_msg_express.IClient
		micLayoutCli          channel_mic_layout.ChannelMicLayoutClient
		youKnowWhoCli         youknowwho.IClient
		channelLiveMgrCli     channellivemgr.IClient
		reporter              *utils.FeishuReporterV2
		timerD                *timer.Timer
	}
	type args struct {
		ctx        context.Context
		channelId  uint32
		changeType time_present.LiveIntimatePresentMsg_IntimatePresentChangeType
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &manager{
				cache:                 tt.fields.cache,
				store:                 tt.fields.store,
				timePresentLocalCache: tt.fields.timePresentLocalCache,
				randSource:            tt.fields.randSource,
				dyConfig:              tt.fields.dyConfig,
				presentCli:            tt.fields.presentCli,
				presentExtraCli:       tt.fields.presentExtraCli,
				userProfileApiCli:     tt.fields.userProfileApiCli,
				micTemplateCli:        tt.fields.micTemplateCli,
				channelMsgExpressCli:  tt.fields.channelMsgExpressCli,
				micLayoutCli:          tt.fields.micLayoutCli,
				youKnowWhoCli:         tt.fields.youKnowWhoCli,
				channelLiveMgrCli:     tt.fields.channelLiveMgrCli,
				reporter:              tt.fields.reporter,
				timerD:                tt.fields.timerD,
			}
			if err := m.SendLiveIntimatePresentListUpdatePush(tt.args.ctx, tt.args.channelId, tt.args.changeType); (err != nil) != tt.wantErr {
				t.Errorf("SendLiveIntimatePresentListUpdatePush() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_manager_genLiveIntimatePresentUuid(t *testing.T) {
	type fields struct {
		cache                 cache.Cache
		store                 store.Store
		timePresentLocalCache TimePresentLocalCache
		randSource            rand.Source
		dyConfig              conf.TimePresent
		presentCli            userpresent_go.IClient
		presentExtraCli       present_extra_conf.IClient
		userProfileApiCli     user_profile_api.IClient
		micTemplateCli        channel_mic_layout_conf_mgr.ChannelMicLayoutConfMgrClient
		channelMsgExpressCli  channel_msg_express.IClient
		micLayoutCli          channel_mic_layout.ChannelMicLayoutClient
		youKnowWhoCli         youknowwho.IClient
		channelLiveMgrCli     channellivemgr.IClient
		reporter              *utils.FeishuReporterV2
		timerD                *timer.Timer
	}
	tests := []struct {
		name   string
		fields fields
		want   uint32
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &manager{
				cache:                 tt.fields.cache,
				store:                 tt.fields.store,
				timePresentLocalCache: tt.fields.timePresentLocalCache,
				randSource:            tt.fields.randSource,
				dyConfig:              tt.fields.dyConfig,
				presentCli:            tt.fields.presentCli,
				presentExtraCli:       tt.fields.presentExtraCli,
				userProfileApiCli:     tt.fields.userProfileApiCli,
				micTemplateCli:        tt.fields.micTemplateCli,
				channelMsgExpressCli:  tt.fields.channelMsgExpressCli,
				micLayoutCli:          tt.fields.micLayoutCli,
				youKnowWhoCli:         tt.fields.youKnowWhoCli,
				channelLiveMgrCli:     tt.fields.channelLiveMgrCli,
				reporter:              tt.fields.reporter,
				timerD:                tt.fields.timerD,
			}
			if got := m.genLiveIntimatePresentUuid(); got != tt.want {
				t.Errorf("genLiveIntimatePresentUuid() = %v, want %v", got, tt.want)
			}
		})
	}
}
