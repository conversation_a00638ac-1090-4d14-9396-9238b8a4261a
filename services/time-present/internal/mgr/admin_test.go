package mgr

import (
	"context"
	"github.com/golang/mock/gomock"
	pb "golang.52tt.com/protocol/services/time-present"
	"golang.52tt.com/services/time-present/internal/store"
	"reflect"
	"testing"
	"time"
)

func Test_manager_AddOrUpdateTimePresentConfig(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.StTimePresentItemConfig
	}
	tests := []struct {
		name     string
		args     args
		wantErr  bool
		initFunc func(m *mgrForTest)
	}{
		{
			name: "AddOrUpdateTimePresentConfig",
			args: args{
				ctx: context.Background(),
				req: &pb.StTimePresentItemConfig{
					Type:                1,
					ItemId:              1000,
					TimeSecond:          100,
					MicLayoutTemplateId: 4,
					Operator:            "马富达",
				},
			},
			wantErr: false,
			initFunc: func(m *mgrForTest) {
				m.getStoreMock().EXPECT().InsertOrUpdateTimePresent(gomock.Any(), gomock.Any()).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			if err := m.AddOrUpdateTimePresentConfig(tt.args.ctx, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("AddOrUpdateTimePresentConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_manager_BatchGetTimePresentConfig(t *testing.T) {
	now := time.Now()
	type args struct {
		ctx               context.Context
		timePresentIdList []uint32
	}
	tests := []struct {
		name     string
		args     args
		want     map[uint32]*store.TimePresent
		wantErr  bool
		initFunc func(m *mgrForTest)
	}{
		{
			name: "BatchGetTimePresentConfig",
			args: args{
				ctx:               context.Background(),
				timePresentIdList: []uint32{1, 2},
			},
			want: map[uint32]*store.TimePresent{
				1: {
					ID:                  1,
					ItemID:              1,
					PresentType:         1,
					TimeSecond:          20,
					MicLayoutTemplateID: 3,
					Remark:              "测试",
					Operator:            "马富达",
					CreateTime:          now,
					UpdateTime:          now,
					IsDeleted:           false,
				},
				2: {
					ID:                  2,
					ItemID:              2,
					PresentType:         1,
					TimeSecond:          30,
					MicLayoutTemplateID: 4,
					Remark:              "测试2",
					Operator:            "测试",
					CreateTime:          now,
					UpdateTime:          now,
					IsDeleted:           true,
				},
			},
			wantErr: false,
			initFunc: func(m *mgrForTest) {
				m.getStoreMock().EXPECT().BatchGetTimePresent(gomock.Any(), []uint32{1, 2}).Return(map[uint32]*store.TimePresent{
					1: {
						ID:                  1,
						ItemID:              1,
						PresentType:         1,
						TimeSecond:          20,
						MicLayoutTemplateID: 3,
						Remark:              "测试",
						Operator:            "马富达",
						CreateTime:          now,
						UpdateTime:          now,
						IsDeleted:           false,
					},
					2: {
						ID:                  2,
						ItemID:              2,
						PresentType:         1,
						TimeSecond:          30,
						MicLayoutTemplateID: 4,
						Remark:              "测试2",
						Operator:            "测试",
						CreateTime:          now,
						UpdateTime:          now,
						IsDeleted:           true,
					},
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.BatchGetTimePresentConfig(tt.args.ctx, tt.args.timePresentIdList)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetTimePresentConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetTimePresentConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_manager_GetTimePresentById(t *testing.T) {
	now := time.Now()
	type args struct {
		ctx    context.Context
		itemId uint32
	}
	tests := []struct {
		name     string
		args     args
		want     *store.TimePresent
		wantErr  bool
		initFunc func(m *mgrForTest)
	}{
		{
			name: "GetTimePresentById",
			args: args{
				ctx:    context.Background(),
				itemId: 1,
			},
			want: &store.TimePresent{
				ID:                  1,
				ItemID:              1,
				PresentType:         1,
				TimeSecond:          20,
				MicLayoutTemplateID: 3,
				Remark:              "测试",
				Operator:            "马富达",
				CreateTime:          now,
				UpdateTime:          now,
				IsDeleted:           false,
			},
			wantErr: false,
			initFunc: func(m *mgrForTest) {
				m.getStoreMock().EXPECT().GetTimePresent(gomock.Any(), uint32(1)).Return(&store.TimePresent{
					ID:                  1,
					ItemID:              1,
					PresentType:         1,
					TimeSecond:          20,
					MicLayoutTemplateID: 3,
					Remark:              "测试",
					Operator:            "马富达",
					CreateTime:          now,
					UpdateTime:          now,
					IsDeleted:           false,
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.GetTimePresentById(tt.args.ctx, tt.args.itemId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTimePresentById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetTimePresentById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_manager_DeleteTimePresent(t *testing.T) {
	type args struct {
		ctx      context.Context
		itemId   uint32
		operator string
	}
	tests := []struct {
		name     string
		args     args
		wantErr  bool
		initFunc func(m *mgrForTest)
	}{
		{
			name: "DeleteTimePresent",
			args: args{
				ctx:      context.Background(),
				itemId:   1,
				operator: "test",
			},
			wantErr: false,
			initFunc: func(m *mgrForTest) {
				m.getStoreMock().EXPECT().DeleteTimePresent(gomock.Any(), uint32(1), "test").Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			if err := m.DeleteTimePresent(tt.args.ctx, tt.args.itemId, tt.args.operator); (err != nil) != tt.wantErr {
				t.Errorf("DeleteTimePresent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
