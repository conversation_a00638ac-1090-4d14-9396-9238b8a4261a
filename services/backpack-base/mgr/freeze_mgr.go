package mgr

// 二阶段消耗， 仅用于碎片消耗
// pb.FreeZeItemReq

import (
	"context"
	"errors"
	"fmt"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/backpack-base"
	"golang.52tt.com/services/backpack-base/store"
)

// 拼装物品扣减数据、订单流水数据
func (m *Manager) GenFreezeItemData(ctx context.Context, uid uint32, useItemList []*pb.UseItemInfo, orderId string,
	outsideTime time.Time, logType uint32, allItemsMap map[uint32]*pb.UserBackpackItem) ([]*store.UseItemInfo, []*store.UseItemMonthRecord) {

	updateUserItems := make([]*store.UseItemInfo, 0, len(useItemList))
	useItemRecords := make([]*store.UseItemMonthRecord, 0, len(useItemList))
	for i, item := range useItemList {
		m := &store.UseItemInfo{}
		m.FromPb(item)
		updateUserItems = append(updateUserItems, m)

		r := &store.UseItemMonthRecord{
			Uid:         uid,
			OperateType: logType,
			OutsideTime: outsideTime,
			MainOrderId: orderId,
		}
		subOrderId := ""
		if i > 0 {
			subOrderId = fmt.Sprintf("%s_%d", orderId, i)
		} else {
			subOrderId = orderId
		}
		haveItem := allItemsMap[item.UserItemId] //一定有
		r.CopyBaseFromPb(haveItem, subOrderId, item.UseCount, haveItem.ItemCount-item.UseCount)
		useItemRecords = append(useItemRecords, r)
	}
	return updateUserItems, useItemRecords
}

// 预扣除
func (m *Manager) FreezePrepare(ctx context.Context, uid uint32, useItemList []*pb.UseItemInfo, transInfo *pb.TransactionInfo) error {
	if len(useItemList) == 0 {
		log.ErrorWithCtx(ctx, "FreeZePrepare param uid:%d", uid)
		return protocol.NewExactServerError(nil, status.ErrBackpackInvalidParams)
	}
	for _, item := range useItemList {
		if item.ItemType != uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) || item.UseCount == 0 {
			log.ErrorWithCtx(ctx, "FreeZePrepare param uid:%d", uid)
			return protocol.NewExactServerError(nil, status.ErrBackpackUseFreezeTypeInvalidErr)
		}
	}
	freezeInfo, err := m.store.GetFreezeStatus(ctx, uid, transInfo.OrderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreeZePrepare uid:%d, err:%v", uid, err)
		return protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
	}
	if freezeInfo != nil { //订单已存在
		return protocol.NewExactServerError(nil, status.ErrBackpackUseFreezeOrderConfilctErr)
	}

	//检查数量是否足够
	allBackpackItems, err := m.GetUserBackpackByItemType(ctx, uid, uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT))
	if err != nil {
		log.ErrorWithCtx(ctx, "FreeZePrepare GetUserBackpackByItemType uid:%d, err:%v", uid, err)
		return err
	}
	allItemsMap := make(map[uint32]*pb.UserBackpackItem)
	for _, item := range allBackpackItems {
		allItemsMap[item.UserItemId] = item
	}
	now := uint32(time.Now().Unix())
	for _, item := range useItemList {
		haveCount := uint32(0)
		haveItem, ok := allItemsMap[item.UserItemId]
		if ok {
			haveCount = haveItem.ItemCount
		}
		if haveCount < item.UseCount {
			log.ErrorWithCtx(ctx, "FreeZePrepare num not enougth uid:%d, useItem:%s , haveCount:%d", uid, item.String(), haveCount)
			return protocol.NewExactServerError(nil, status.ErrBackpackUserNotEnoughItem)
		}
		if haveItem.SourceId != item.SourceId {
			log.ErrorWithCtx(ctx, "FreeZePrepare item_id valid uid:%d, useItem:%s , haveItemId:%d", uid, item.String(), haveItem.SourceId)
			return protocol.NewExactServerError(nil, status.ErrBackpackUseSourceIdErr)
		}
		if haveItem.FinTime != 0 && haveItem.FinTime <= now {
			log.ErrorWithCtx(ctx, "FreeZePrepare timeout  uid:%d, useItem:%s , haveItemId:%d", uid, item.String(), haveItem.SourceId)
			return protocol.NewExactServerError(nil, status.ErrBackpackPackageItemTimeout)
		}
	}

	outSideTime := time.Unix(int64(transInfo.OperTime), 0)
	useItemListM, useItemRecords := m.GenFreezeItemData(ctx, uid, useItemList,
		transInfo.OrderId, outSideTime,
		transInfo.Reason,
		allItemsMap,
	)

	err = m.store.FreezePrepare(ctx, uid, useItemListM, useItemRecords, transInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreeZePrepare store uid:%d, orderId :%s err:%v", uid, transInfo.OrderId, err)
		if errors.Is(err, store.ErrNoEnougthItem) {
			return protocol.NewExactServerError(nil, status.ErrBackpackUserNotEnoughItem)
		}
		if errors.Is(err, store.ErrOrderHasDone) { //订单已处理
			log.InfoWithCtx(ctx, "FreeZePrepare store orderId exist uid:%d, orderId:%s", uid, transInfo.OrderId)
			return protocol.NewExactServerError(nil, status.ErrBackpackUseFreezeOrderConfilctErr)
		}
		return protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
	}

	m.CleanUserBackpack(ctx, uid)

	return nil
}

// 新版预扣，不用传物品项，按时间优先使用
func (m *Manager) FreezePrepareV2(ctx context.Context, uid uint32, itemType, itemId uint32, useCount uint32, transInfo *pb.TransactionInfo) error {
	freezeInfo, err := m.store.GetFreezeStatus(ctx, uid, transInfo.OrderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreezePrepareV2 uid:%d, err:%v", uid, err)
		return protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
	}
	if freezeInfo != nil { //订单已存在
		return protocol.NewExactServerError(nil, status.ErrBackpackUseFreezeOrderConfilctErr)
	}

	itemList, totalNum, err := m.CheckCanUseItem(ctx, uid, itemType, itemId, useCount)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanUseItem fail uid:%d, item:%d,%d, total:%d, use:%d, err:%v", uid, itemType, itemId, totalNum, useCount, err)
		m.CleanUserBackpack(ctx, uid)
		return err
	}
	outSideTime := time.Unix(int64(transInfo.OperTime), 0)
	//拼装扣减数据，订单流水数据
	baseRecord := &store.UseItemMonthRecord{
		Uid:         uid,
		OperateType: transInfo.Reason,
		OutsideTime: outSideTime,
		MainOrderId: transInfo.OrderId,
	}
	useItemListM, useItemRecords := m.GenUseBackpackItemData(useCount, useCount, []string{transInfo.OrderId}, itemList, baseRecord)
	err = m.store.FreezePrepare(ctx, uid, useItemListM, useItemRecords, transInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreeZePrepare store uid:%d, orderId :%s err:%v", uid, transInfo.OrderId, err)
		if errors.Is(err, store.ErrNoEnougthItem) {
			return protocol.NewExactServerError(nil, status.ErrBackpackUserNotEnoughItem)
		}
		if errors.Is(err, store.ErrOrderHasDone) { //订单已处理
			log.InfoWithCtx(ctx, "FreeZePrepare store orderId exist uid:%d, orderId:%s", uid, transInfo.OrderId)
			return protocol.NewExactServerError(nil, status.ErrBackpackUseFreezeOrderConfilctErr)
		}
		return protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
	}

	m.CleanUserBackpack(ctx, uid)
	return nil
}

// 确认
func (m *Manager) FreezeCommit(ctx context.Context, uid uint32, transInfo *pb.TransactionInfo) error {
	//获取冻结状态
	orderId := transInfo.OrderId
	freezeInfo, err := m.store.GetFreezeStatus(ctx, uid, orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreezeCommit uid:%d, orderid:%s, err:%v", uid, orderId, err)
		return protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
	}
	if freezeInfo.Uid != uid {
		log.ErrorWithCtx(ctx, "FreezeCommit uid:%d, orderid:%s, freezeUid:%d, err:%v", uid, orderId, freezeInfo.Uid, err)
		return protocol.NewExactServerError(nil, status.ErrBackpackInvalidParams)
	}
	if freezeInfo.FreezeType != uint32(pb.FREEZETYPE_FREEZETYPE_PREPARE) { //订单已处理
		log.InfoWithCtx(ctx, "FreezeCommit uid:%d, freezeNum:%d, freezeType:%d orderid:%s, err:%v", uid, freezeInfo.FreezeNum, freezeInfo.FreezeType, orderId, err)
		return nil
	}

	err = m.store.FreezeCommit(ctx, uid, transInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreezeCommit store uid:%d, err:%v", uid, err)
		return protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
	}

	//异步处理发放包裹后的事情
	m.asyncTaskMgr.AfterFreezeItemCommit(ctx, uid, orderId, freezeInfo.FreezeNum, freezeInfo.OperateTime)

	return nil
}

// 回滚
func (m *Manager) FreezeRollback(ctx context.Context, uid uint32, transInfo *pb.TransactionInfo) error {
	orderId := transInfo.OrderId
	//获取冻结状态
	freezeInfo, err := m.store.GetFreezeStatus(ctx, uid, orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreezeRollback uid:%d, orderid:%s, err:%v", uid, orderId, err)
		return protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
	}
	if freezeInfo == nil || freezeInfo.Uid == 0 { //不存在的订单。
		log.InfoWithCtx(ctx, "FreezeRollback not exist uid:%d, orderid:%s", uid, orderId)
		return nil
	}
	if freezeInfo.Uid != uid {
		log.ErrorWithCtx(ctx, "FreezeRollback uid:%d, orderid:%s, freezeUid:%d, err:%v", uid, orderId, freezeInfo.Uid, err)
		return protocol.NewExactServerError(nil, status.ErrBackpackInvalidParams)
	}
	if freezeInfo.FreezeType != uint32(pb.FREEZETYPE_FREEZETYPE_PREPARE) { //订单已处理
		log.InfoWithCtx(ctx, "FreezeRollback uid:%d, freezeNum:%d, freezeType:%d orderid:%s, err:%v", uid, freezeInfo.FreezeNum, freezeInfo.FreezeType, orderId, err)
		return nil
	}

	err = m.store.FreezeRollback(ctx, uid, freezeInfo.OperateTime, orderId, transInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreezeRollback store uid:%d, orderid:%s, err:%v", uid, orderId, err)
		return protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
	}
	m.CleanUserBackpack(ctx, uid)
	log.InfoWithCtx(ctx, "FreezeRollback ok uid:%d, orderId:%s", uid, orderId)
	return nil
}
