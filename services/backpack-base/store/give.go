package store

import (
	"context"
	"errors"
	"fmt"
	mysql_driver "github.com/go-sql-driver/mysql"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/backpack-base"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"strings"
	"time"
)

//发放背包物品

//用户包裹获取流水表(月表)
const UserPackageGainMonthTbl = `CREATE TABLE IF NOT EXISTS %s (
	id bigint(10) NOT NULL AUTO_INCREMENT COMMENT 'ID',
	out_order_id  varchar(255) NOT NULL COMMENT '订单id',
	uid int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
	bg_id int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '包裹id',
	num int(10) UNSIGNED NOT NULL COMMENT '送出数量',
	source tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '包裹来源',
	source_app_id varchar(64) NOT NULL COMMENT '包裹详细来源',
	total_price int unsigned NOT NULL default 0 COMMENT '包裹总价值',
	outside_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '外部时间时间',
	create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '包裹入库时间',
	PRIMARY KEY (id), 
	UNIQUE KEY idx_order(out_order_id),
	INDEX idx_outside_time (outside_time,source,total_price), 
	INDEX uid_index (uid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户获得包裹记录(月表)';`

type UserPackageGainMonthV2 struct {
	ID          uint64    `gorm:"column:id" db:"id" json:"id"`                                  //  ID
	OutOrderId  string    `gorm:"column:out_order_id" db:"out_order_id" json:"out_order_id"`    //  订单ID
	Uid         uint32    `gorm:"column:uid" db:"uid" json:"uid"`                               //  UID
	BgId        uint32    `gorm:"column:bg_id" db:"bg_id" json:"bg_id"`                         //  包裹ID
	Num         uint32    `gorm:"column:num" db:"num" json:"num"`                               //  数量
	Source      uint32    `gorm:"column:source" db:"source" json:"source"`                      //  来源
	SourceAppId string    `gorm:"column:source_app_id" db:"source_app_id" json:"source_app_id"` //  包裹详细来源
	TotalPrice  uint32    `gorm:"column:total_price" db:"total_price" json:"total_price"`       //  包裹总价值
	OutsideTime time.Time `gorm:"column:outside_time" db:"outside_time" json:"outside_time"`    //  订单发放时间
	CreateTime  time.Time `gorm:"column:create_time" db:"create_time" json:"create_time"`       //  包裹入库时间
}

func (m *UserPackageGainMonthV2) TableName() string {
	return fmt.Sprintf("user_package_gain_month_v2_%s", m.OutsideTime.Format("200601"))
}
func (m *UserPackageGainMonthV2) CreateTable(db *gorm.DB) {
	tb := m.TableName()
	if db.Migrator().HasTable(tb) {
		log.Infof("CreateTable HasTable:%s", tb)
		return
	}
	sql := fmt.Sprintf(UserPackageGainMonthTbl, tb)
	err := db.Exec(sql).Error
	log.Infof("UserPackageGainMonthTbl:%s,  err:%v", tb, err)
}

func (m *UserPackageGainMonthV2) TXAddRecord(tx *gorm.DB) error {
	return tx.Table(m.TableName()).
		Select("out_order_id", "uid", "bg_id", "num", "source", "source_app_id", "total_price", "outside_time").Create(m).Error
}

func (m *UserPackageGainMonthV2) String() string {
	return fmt.Sprintf("uid:%d,bgid:%d,orderid:%s", m.Uid, m.BgId, m.OutOrderId)
}

//用户背包物品获取流水表（月表)
const UserPackageGainItemMonthTbl = `CREATE TABLE IF NOT EXISTS %s (
	log_id int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
	out_order_id  varchar(255) NOT NULL COMMENT '订单id',
	uid int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
	item_id  int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '物品id',
	item_type  tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '物品类型',
	num int(10) UNSIGNED NOT NULL COMMENT '送出数量',
	source_type tinyint unsigned not null COMMENT '包裹来源', 
	source_app_id varchar(64) NOT NULL COMMENT '物品详细来源',
	total_price int unsigned NOT NULL default 0 COMMENT '物品总价值',
	outside_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '外部时间时间',
	create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '物品入库时间',
	fin_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '物品过期时间',
	user_item_id int(10) unsigned not null default 0 comment '用户道具ID',
	final_item_count int(10) unsigned not null default 0 comment '变化后的道具数量',
	PRIMARY KEY (log_id), 
	INDEX item_id_idx (item_id), 
	INDEX order_id_idx (out_order_id), 
	INDEX outside_time_idx (outside_time), 
	INDEX create_time_idx (create_time), 
	INDEX uid_index (uid) 
	) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户获得物品记录';`

//用户获得物品记录
type UserPackageGainItemMonthV2 struct {
	LogId          int64     `gorm:"column:log_id" db:"log_id" json:"log_id"`
	OutOrderId     string    `gorm:"column:out_order_id" db:"out_order_id" json:"out_order_id"`             //  订单id
	Uid            uint32    `gorm:"column:uid" db:"uid" json:"uid"`                                        //  用户id
	ItemId         uint32    `gorm:"column:item_id" db:"item_id" json:"item_id"`                            //  物品id
	ItemType       uint32    `gorm:"column:item_type" db:"item_type" json:"item_type"`                      //  物品类型
	Num            uint32    `gorm:"column:num" db:"num" json:"num"`                                        //  送出数量
	SourceType     uint32    `gorm:"column:source_type" db:"source_type" json:"source_type"`                //  包裹来源
	SourceAppId    string    `gorm:"column:source_app_id" db:"source_app_id" json:"source_app_id"`          //  物品详细来源
	TotalPrice     uint32    `gorm:"column:total_price" db:"total_price" json:"total_price"`                //  物品总价值
	OutsideTime    time.Time `gorm:"column:outside_time" db:"outside_time" json:"outside_time"`             //  外部时间时间
	CreateTime     time.Time `gorm:"column:create_time" db:"create_time" json:"create_time"`                //  物品入库时间
	FinTime        time.Time `gorm:"column:fin_time" db:"fin_time" json:"fin_time"`                         //  物品过期时间
	UserItemId     uint32    `gorm:"column:user_item_id" db:"user_item_id" json:"user_item_id"`             //  用户道具id
	FinalItemCount uint32    `gorm:"column:final_item_count" db:"final_item_count" json:"final_item_count"` //  变化后的道具数量
}

func (m *UserPackageGainItemMonthV2) TableName() string {
	return fmt.Sprintf("user_backpack_gain_item_month_v2_%s", m.OutsideTime.Format("200601"))
}
func (m *UserPackageGainItemMonthV2) CreateTable(db *gorm.DB) {
	tb := m.TableName()
	for i := 0; i < 100; i++ {
		subTb := fmt.Sprintf("%s_%02d", tb, i)
		if db.Migrator().HasTable(subTb) {
			continue
		}
		sql := fmt.Sprintf(UserPackageGainItemMonthTbl, subTb)
		err := db.Exec(sql).Error
		if err != nil {
			log.Errorf("UserPackageGainItemMonthTbl:%s,  err:%v", subTb, err)
		}
	}
	if db.Migrator().HasTable(tb) {
		log.Infof("CreateTable HasTable:%s", tb)
		return
	}
	sql := fmt.Sprintf(UserPackageGainItemMonthTbl, tb)
	err := db.Exec(sql).Error
	log.Infof("UserPackageGainItemMonthTbl:%s,  err:%v", tb, err)
}

// func (m *UserPackageGainItemMonthV2) InsertHeader() string {
// 	return "(out_order_id, uid, item_id, item_type, num, source_type, source_app_id, total_price, outside_time, fin_time, user_item_id, final_item_count)"
// }
// func (m *UserPackageGainItemMonthV2) InsertValue() string {
// 	return fmt.Sprintf("('%s',%d,%d,%d,%d,%d,%d,%d,'%s','%s',%d,%d)",
// 		m.OutOrderId, m.Uid, m.ItemId, m.ItemType, m.Num, m.SourceType, m.SourceAppId, m.TotalPrice,
// 		m.OutsideTime.Format("2006-01-02 15:04:05"),
// 		m.FinTime.Format("2006-01-02 15:04:05"),
// 		m.UserItemId, m.FinalItemCount,
// 	)
// }

func (m *UserPackageGainItemMonthV2) TransformFromItem(item *UserBackpackItem, record *UserPackageGainMonthV2) {
	m.Uid = item.Uid
	m.ItemId = item.SourceId
	m.ItemType = item.ItemType
	m.Num = item.ItemCount
	m.SourceType = item.SourceType
	m.FinTime = item.FinTime
	m.OutOrderId = record.OutOrderId
	m.SourceAppId = record.SourceAppId
	m.TotalPrice = record.TotalPrice
	m.OutsideTime = record.OutsideTime
	m.CreateTime = record.CreateTime
}
func (m *UserPackageGainItemMonthV2) TransformFromUseRecord(item *UseItemMonthRecord, outsideTime time.Time, sourceType, totalPrice uint32, transOrderId, appid string) {
	m.UserItemId = item.UserItemId
	m.Uid = item.Uid
	m.ItemId = item.SourceId
	m.ItemType = item.ItemType
	m.Num = item.UseCount
	m.OutOrderId = transOrderId
	m.OutsideTime = outsideTime
	m.SourceType = sourceType
	m.TotalPrice = totalPrice
	m.SourceAppId = appid
	m.CreateTime = m.OutsideTime
}

//检查发放订单是否存在
func (s *Store) CheckGiveOrderIdExist(ctx context.Context, orderId string, outsideTime uint32) (bool, error) {
	outTime := time.Unix(int64(outsideTime), 0)
	exist, err := s.IsGiveOrderIdExist(ctx, orderId, outTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGiveOrderIdExist fail, orderid:%s, time:%d, err:%v", orderId, outsideTime, err)
		return false, err
	}
	if exist {
		return true, nil
	}

	//检查上个月的
	return s.IsGiveOrderIdExist(ctx, orderId, outTime.AddDate(0, -1, 0))
}
func (s *Store) IsGiveOrderIdExist(ctx context.Context, orderId string, outsideTime time.Time) (bool, error) {
	m := &UserPackageGainMonthV2{
		OutsideTime: outsideTime,
	}
	sql := fmt.Sprintf("SELECT out_order_id FROM %s WHERE out_order_id=?", m.TableName())
	err := s.GetOnlyReadDb().Raw(sql, orderId).Scan(m).Error
	if driverErr, ok := err.(*mysql_driver.MySQLError); ok && driverErr.Number == 1146 { //未创建
		return false, nil
	}
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		log.ErrorWithCtx(ctx, "IsGiveOrderIdExist fail , err:%v", err)
		return false, err
	}
	if len(m.OutOrderId) > 0 {
		return true, nil
	}
	return false, nil
}

func (s *Store) FillUserItemId(finalItemList []*UserBackpackItem, gainItemRecords []*UserPackageGainItemMonthV2) {
	for _, record := range gainItemRecords {
		for _, item := range finalItemList {
			if item.ItemType != record.ItemType || item.SourceId != record.ItemId || item.SourceType != record.SourceType {
				continue
			}
			if item.FinTime != record.FinTime {
				continue
			}
			record.UserItemId = item.UserItemId
			record.FinalItemCount = item.ItemCount
		}
	}
}

//批量添加物品
// func (s *Store) TXAddUserBackpackItem(ctx context.Context, tx *gorm.DB, itemList []*UserBackpackItem) error {
// 	return tx.Clauses(clause.OnConflict{
// 		DoUpdates: clause.Assignments(map[string]interface{}{
// 			"item_count": gorm.Expr("item_count+ values(item_count)"),
// 		}),
// 	}).Table(itemList[0].TableName()).
// 		Select("uid", "item_type", "source_id", "fin_time", "item_count", "weight", "source_type").
// 		CreateInBatches(itemList, len(itemList)).Error
// }
//批量添加物品SQL
func (s *Store) SQLAddUserBackpackItem(ctx context.Context, itemList []*UserBackpackItem) string {
	var build strings.Builder
	build.WriteString(fmt.Sprintf("insert into %s(uid, item_type, source_id, fin_time, item_count, weight, source_type) values", itemList[0].TableName()))
	for i, item := range itemList {
		if i != 0 {
			build.WriteString(",")
		}
		build.WriteString(fmt.Sprintf("(%d, %d, %d,'%s', %d,%d,%d)", item.Uid, item.ItemType, item.SourceId,
			item.FinTime.Format("2006-01-02 15:04:05"), item.ItemCount, item.Weight, item.SourceType))
	}
	build.WriteString("on duplicate key update item_count = item_count + VALUES(item_count)")
	return build.String()
}

//添加物品流水SQL
func (s *Store) TXAddGainItemRecord(ctx context.Context, tx *gorm.DB, gainItemRecords []*UserPackageGainItemMonthV2) error {
	record := gainItemRecords[0]
	tb := record.TableName()
	if record.SourceType == uint32(pb.PackageSourceType_PACKAGE_SOURCE_SMASHEGG) {
		tb += fmt.Sprintf("_%02d", record.Uid%100)
	}
	return tx.Clauses(clause.Insert{
		Modifier: "IGNORE",
	}).Table(tb).
		Select(`out_order_id`, `uid`, `item_id`, `item_type`, `num`, `source_type`, `source_app_id`, `total_price`, `outside_time`, `fin_time`, `user_item_id`, `final_item_count`).
		CreateInBatches(gainItemRecords, len(gainItemRecords)).Error
}

//发放包裹物品
func (s *Store) GiveUserPackage(ctx context.Context, uid uint32, gainMonthRecord *UserPackageGainMonthV2, itemList []*UserBackpackItem) error {
	//获取物品类型列表
	itemTypes := []uint32{}
	itemTypesMap := map[uint32][]uint32{}
	for _, item := range itemList {
		if _, ok := itemTypesMap[item.ItemType]; !ok {
			itemTypesMap[item.ItemType] = []uint32{}
			itemTypes = append(itemTypes, item.ItemType)
		}
		itemTypesMap[item.ItemType] = append(itemTypesMap[item.ItemType], item.SourceId)
	}
	onlyOneItemType := uint32(0)
	onlyOneItemId := uint32(0)
	if len(itemTypes) == 1 && len(itemTypesMap[itemTypes[0]]) == 1 {
		onlyOneItemType = itemTypes[0]
		onlyOneItemId = itemTypesMap[itemTypes[0]][0]
	}

	// 生成背包物品获取流水数组
	gainItemRecords := make([]*UserPackageGainItemMonthV2, len(itemList))
	for i, item := range itemList {
		gainItemRecords[i] = &UserPackageGainItemMonthV2{}
		gainItemRecords[i].TransformFromItem(item, gainMonthRecord)
	}

	addItemSQL := s.SQLAddUserBackpackItem(ctx, itemList)

	e := s.Transaction(ctx, func(tx *gorm.DB) error {
		//1.添加获取包裹流水
		err := gainMonthRecord.TXAddRecord(tx)
		if err != nil {
			return err
		}

		//2.添加物品
		//err = s.TXAddUserBackpackItem(ctx, tx, itemList)
		err = tx.Exec(addItemSQL).Error
		if err != nil {
			return err
		}

		//3.按类型获取背包数据
		var finalItemList []*UserBackpackItem
		if onlyOneItemType == 0 {
			finalItemList, err = s.GetUserBackpackByItemType(ctx, tx, uid, itemTypes)
		} else {
			finalItemList, err = s.GetUserBackpackByItem(ctx, tx, uid, onlyOneItemType, onlyOneItemId)
		}
		if err != nil {
			return err
		}
		s.FillUserItemId(finalItemList, gainItemRecords)

		//4.添加物品获取流水
		return s.TXAddGainItemRecord(ctx, tx, gainItemRecords)
	})

	if e != nil {
		log.ErrorWithCtx(ctx, "GiveUserPackage fail , err:%v, info:%s", e, gainMonthRecord.String())
		if driverErr, ok := e.(*mysql_driver.MySQLError); ok && driverErr.Number == 1062 { //订单已存在
			return ErrOrderHasDone
		}
	}
	return e
}
