package main

import (
	"context"
	"flag"
	"fmt"
	"time"

	"golang.52tt.com/services/gold-commission/conf"
	"golang.52tt.com/services/gold-commission/manager"
	"golang.52tt.com/services/gold/common/model"
)

func main() {
	var err error
	ctx := context.Background()
	sc := &conf.ServiceConfigT{}

	dataType := flag.String("type", "", "type = amuse, yuyin, game")
	flag.Parse() //

	err = sc.Parse("/home/<USER>/etc/server/gold-commission.json")
	if err != nil {
		fmt.Println(err)
		return
	}

	mgr, err := manager.NewManager(sc)
	if err != nil {
		fmt.Println(err)
		return
	}

	now := time.Now()
	initStart := time.Date(2025, 3, 1, 0, 0, 0, 0, time.Local)
	initEnd := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)

	if *dataType == "amuse" {
		amuseStore := mgr.GetMysqlStore().GetAmuseDB()

		start := initStart
		end := initEnd

		for start.Before(end) {
			datyStart := start
			datyEnd := start.AddDate(0, 0, 1)

			tbl := fmt.Sprintf("amuse_gold_%s", datyStart.Format("200601"))

			// 初始化付费人数
			dayGuildIds, err := amuseStore.GetGuildIdsFromDayStat(ctx, datyStart, datyEnd)
			if err != nil {
				fmt.Println(err)
				return
			}

			// 公会-日
			for _, guildId := range dayGuildIds {
				var count int64
				err := amuseStore.GetDb().
					Table(tbl).
					Distinct("paid_uid").
					Where("guild_id = ?", guildId).
					Where("bought_time >= ?", datyStart.Unix()).
					Where("bought_time < ?", datyEnd.Unix()).
					Count(&count).Error
				if err != nil {
					fmt.Println(err)
					return
				}
				amuseStore.UpsertGuildPaidCnt(ctx, model.DimensionGuildDaily, datyStart, guildId, 0, uint32(count))
			}

			// 公会+房间-日
			for _, guildId := range dayGuildIds {
				channelIds, err := amuseStore.GetChannelIdsByGuildId(ctx, []uint32{guildId}, datyStart, datyEnd)
				if err != nil {
					fmt.Println(err)
					return
				}
				for _, channelId := range channelIds {
					var count int64
					err := amuseStore.GetDb().
						Table(tbl).
						Distinct("paid_uid").
						Where("guild_id = ?", guildId).
						Where("room_id = ?", channelId).
						Where("bought_time >= ?", datyStart.Unix()).
						Where("bought_time < ?", datyEnd.Unix()).
						Count(&count).Error
					if err != nil {
						fmt.Println(err)
						return
					}
					amuseStore.UpsertGuildPaidCnt(ctx, model.DimensionGuildIncrDaily, datyStart, guildId, channelId, uint32(count))
				}
			}

			if start.Day() == 1 {
				// 上个月
				monthStart := time.Date(start.Year(), start.Month()-1, 1, 0, 0, 0, 0, time.Local)
				monthEnd := time.Date(start.Year(), start.Month(), 1, 0, 0, 0, 0, time.Local)

				lastMonthTbl := fmt.Sprintf("amuse_gold_%s", monthStart.Format("200601"))

				monthGuildIds, err := amuseStore.GetGuildIdsFromDayStat(ctx, monthStart, monthEnd)
				if err != nil {
					fmt.Println(err)
					return
				}

				// 公会+房间-月
				for _, guildId := range monthGuildIds {
					channelIds, err := amuseStore.GetChannelIdsByGuildId(ctx, []uint32{guildId}, monthStart, monthEnd)
					if err != nil {
						fmt.Println(err)
						return
					}
					for _, channelId := range channelIds {
						var count int64
						err := amuseStore.GetDb().
							Table(lastMonthTbl).
							Distinct("paid_uid").
							Where("guild_id = ?", guildId).
							Where("room_id = ?", channelId).
							Where("bought_time >= ?", monthStart.Unix()).
							Where("bought_time < ?", monthEnd.Unix()).
							Count(&count).Error
						if err != nil {
							fmt.Println(err)
							return
						}
						amuseStore.UpsertGuildPaidCnt(ctx, model.DimensionGuildIncrMonthly, monthStart, guildId, channelId, uint32(count))
					}
				}
			}

			start = start.AddDate(0, 0, 1)
		}
	} else if *dataType == "yuyin" {
		yuyinStore := mgr.GetMysqlStore().GetYuyinDB()

		start := initStart
		end := initEnd

		for start.Before(end) {
			// 每天的付费人数统计
			dayStart := start
			dayEnd := start.AddDate(0, 0, 1)

			tbl := fmt.Sprintf("yuyin_gold_%s", dayStart.Format("200601"))

			dayGuildIds, err := yuyinStore.GetGuildIdsFromDayStat(ctx, dayStart, dayEnd)
			if err != nil {
				fmt.Println(err)
				return
			}

			for _, guildId := range dayGuildIds {
				var count int64
				err := yuyinStore.GetDb().
					Table(tbl).
					Distinct("paid_uid").
					Where("guild_id = ?", guildId).
					Where("bought_time >= ?", dayStart.Unix()).
					Where("bought_time < ?", dayEnd.Unix()).
					Count(&count).Error
				if err != nil {
					fmt.Println(err)
					return
				}
				yuyinStore.UpsertGuildPaidCnt(ctx, model.DimensionGuildDaily, dayStart, guildId, 0, uint32(count))
			}

			// 公会+主播-日
			for _, guildId := range dayGuildIds {
				anchorIds, err := yuyinStore.GetAnchorIdsByGuildId(ctx, []uint32{guildId}, dayStart, dayEnd)
				if err != nil {
					fmt.Println(err)
					return
				}
				for _, anchorId := range anchorIds {
					var count int64
					err := yuyinStore.GetDb().
						Table(tbl).
						Distinct("paid_uid").
						Where("guild_id = ?", guildId).
						Where("anchor_id = ?", anchorId).
						Where("bought_time >= ?", dayStart.Unix()).
						Where("bought_time < ?", dayEnd.Unix()).
						Count(&count).Error
					if err != nil {
						fmt.Println(err)
						return
					}
					yuyinStore.UpsertGuildPaidCnt(ctx, model.DimensionGuildIncrDaily, dayStart, guildId, anchorId, uint32(count))
				}
			}

			if start.Day() == 1 {
				// 上个月
				monthStart := time.Date(start.Year(), start.Month()-1, 1, 0, 0, 0, 0, time.Local)
				monthEnd := time.Date(start.Year(), start.Month(), 1, 0, 0, 0, 0, time.Local)

				lastMonthTbl := fmt.Sprintf("yuyin_gold_%s", monthStart.Format("200601"))

				monthGuildIds, err := yuyinStore.GetGuildIdsFromDayStat(ctx, monthStart, monthEnd)
				if err != nil {
					fmt.Println(err)
					return
				}

				// 公会+主播-月
				for _, guildId := range monthGuildIds {
					anchorIds, err := yuyinStore.GetAnchorIdsByGuildId(ctx, []uint32{guildId}, monthStart, monthEnd)
					if err != nil {
						fmt.Println(err)
						return
					}
					for _, anchorId := range anchorIds {
						var count int64
						err := yuyinStore.GetDb().
							Table(lastMonthTbl).
							Distinct("paid_uid").
							Where("guild_id = ?", guildId).
							Where("anchor_id = ?", anchorId).
							Where("bought_time >= ?", monthStart.Unix()).
							Where("bought_time < ?", monthEnd.Unix()).
							Count(&count).Error
						if err != nil {
							fmt.Println(err)
							return
						}
						yuyinStore.UpsertGuildPaidCnt(ctx, model.DimensionGuildIncrMonthly, monthStart, guildId, anchorId, uint32(count))
					}
				}
			}

			start = start.AddDate(0, 0, 1)
		}
	} else if *dataType == "game" {
		gameStore := mgr.GetMysqlStore().GetGameDB()

		start := initStart
		end := initEnd

		for start.Before(end) {
			// 每天的付费人数统计
			dayStart := start
			dayEnd := start.AddDate(0, 0, 1)

			tbl := fmt.Sprintf("interact_game_gold_%s", dayStart.Format("200601"))

			dayGuildIds, err := gameStore.GetGuildIdsFromDayStat(ctx, dayStart, dayEnd)
			if err != nil {
				fmt.Println(err)
				return
			}

			for _, guildId := range dayGuildIds {
				var count int64
				err := gameStore.GetDb().
					Table(tbl).
					Distinct("paid_uid").
					Where("guild_id = ?", guildId).
					Where("bought_time >= ?", dayStart.Unix()).
					Where("bought_time < ?", dayEnd.Unix()).
					Count(&count).Error
				if err != nil {
					fmt.Println(err)
					return
				}
				gameStore.UpsertGuildPaidCnt(ctx, model.DimensionGuildDaily, dayStart, guildId, 0, uint32(count))
			}

			// 公会+主播-日
			for _, guildId := range dayGuildIds {
				anchorIds, err := gameStore.GetAnchorIdsByGuildId(ctx, []uint32{guildId}, dayStart, dayEnd)
				if err != nil {
					fmt.Println(err)
					return
				}
				for _, anchorId := range anchorIds {
					var count int64
					err := gameStore.GetDb().
						Table(tbl).
						Distinct("paid_uid").
						Where("guild_id = ?", guildId).
						Where("anchor_id = ?", anchorId).
						Where("bought_time >= ?", dayStart.Unix()).
						Where("bought_time < ?", dayEnd.Unix()).
						Count(&count).Error
					if err != nil {
						fmt.Println(err)
						return
					}
					gameStore.UpsertGuildPaidCnt(ctx, model.DimensionGuildIncrDaily, dayStart, guildId, anchorId, uint32(count))
				}
			}

			if start.Day() == 1 {
				// 上个月
				monthStart := time.Date(start.Year(), start.Month()-1, 1, 0, 0, 0, 0, time.Local)
				monthEnd := time.Date(start.Year(), start.Month(), 1, 0, 0, 0, 0, time.Local)

				lastMonthTbl := fmt.Sprintf("interact_game_gold_%s", monthStart.Format("200601"))

				monthGuildIds, err := gameStore.GetGuildIdsFromDayStat(ctx, monthStart, monthEnd)
				if err != nil {
					fmt.Println(err)
					return
				}

				// 公会+主播-月
				for _, guildId := range monthGuildIds {
					anchorIds, err := gameStore.GetAnchorIdsByGuildId(ctx, []uint32{guildId}, monthStart, monthEnd)
					if err != nil {
						fmt.Println(err)
						return
					}
					for _, anchorId := range anchorIds {
						var count int64
						err := gameStore.GetDb().
							Table(lastMonthTbl).
							Distinct("paid_uid").
							Where("guild_id = ?", guildId).
							Where("anchor_id = ?", anchorId).
							Where("bought_time >= ?", monthStart.Unix()).
							Where("bought_time < ?", monthEnd.Unix()).
							Count(&count).Error
						if err != nil {
							fmt.Println(err)
							return
						}
						gameStore.UpsertGuildPaidCnt(ctx, model.DimensionGuildIncrMonthly, monthStart, guildId, anchorId, uint32(count))
					}
				}
			}

			start = start.AddDate(0, 0, 1)
		}
	}
}
