package manager

import (
	"context"
	"errors"
	"fmt"
	"time"

	anchorcontract_go "golang.52tt.com/protocol/services/anchorcontract-go"

	"golang.52tt.com/pkg/deal_token"
	"golang.52tt.com/pkg/log"
	channelPb "golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/services/gold-commission"
	"golang.52tt.com/protocol/services/youknowwho"
	"golang.52tt.com/services/gold-commission/client"
	"golang.52tt.com/services/gold-commission/mysql"
)

func (m *Manager) HandleKnightEvent(ctx context.Context, e *pb.AwardGoldOrder) (error, bool) {
	var err error

	ctxUkw, cancel := context.WithTimeout(ctx, time.Millisecond*300)
	defer cancel()
	// 检查消费用户是否是神秘人
	ykwInfo, err := client.YkwCli.GetUKWInfo(ctxUkw, e.GetPaidUid())
	if err != nil {
		log.ErrorWithCtx(ctxUkw, "handleKnightEvent GetUKWInfo err:%s", err.Error())
		// ignore
	} else {
		if ykwInfo.GetUkwPermissionInfo().GetSwitch() == youknowwho.UKWSwitchType_UKW_SWITCH_ON {
			e.IsUkwPaid = true
			e.UkwPaidAccount = ykwInfo.GetUkwPersonInfo().GetAccount()
		}
	}

	store := m.mysqlStore.GetStoreV2(e.GetGoldType())
	if store == nil {
		log.ErrorWithCtx(ctx, "handleKnightEvent GetStore err, gold_type:%d", e.GetGoldType())
		return errors.New("GetStore gold_type err"), false
	}
	cache := m.cacheStore.GetCacheV2(e.GetGoldType())
	if cache == nil {
		log.ErrorWithCtx(ctx, "handleKnightEvent GetCacheV2 err, gold_type:%d", e.GetGoldType())
		return errors.New("GetCacheV2 gold_type err"), false
	}

	awardGold := func() (err error, ret bool) {
		channelInfo, sErr := client.ChannelCli.GetChannelDetailInfo(ctx, e.GetTargetUid(), e.GetChannelId())
		if sErr != nil {
			log.ErrorWithCtx(ctx, "HandleKnightEvent GetChannelDetailInfo err:%s, anchor_id:%d, order_id:%s", sErr.Error(), e.GetTargetUid(), e.GetOrderId())
			return sErr, true
		}

		var contractGuildId, contractActorId uint32

		contractInfo, sErr := client.AnchorContractCli.GetUserContractCacheInfo(ctx, e.GetTargetUid(), channelInfo.GetCreaterUid())
		if sErr != nil {
			log.ErrorWithCtx(ctx, "handleKnightEvent GetUserContractCacheInfo err:%s, anchor_id:%d, order_id:%s", sErr.Error(), e.GetTargetUid(), e.GetOrderId())
			return sErr, true
		}
		if contractInfo.GetContract().GetGuildId() == 0 {
			log.WarnWithCtx(ctx, "handleKnightEvent err contract guild = 0, anchor_id:%d, order_id:%s", e.GetTargetUid(), e.GetOrderId())
			return nil, false
		}
		if time.Unix(int64(contractInfo.GetContract().GetExpireTime()), 0).Before(time.Unix(int64(e.GetBoughtTime()), 0)) {
			log.ErrorWithCtx(ctx, "handleKnightEvent err contract expired, anchor_id:%d, guild_id:%d, order_id:%s", e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
			return nil, false
		}
		contractGuildId = contractInfo.GetContract().GetGuildId()
		contractActorId = contractInfo.GetContract().GetActorUid()

		if contractGuildId == 0 || contractActorId == 0 {
			log.WarnWithCtx(ctx, "handleKnightEvent err contractGuildId == 0 || contractActorId == 0")
			return nil, false
		}

		if m.IsNoBonusGuild(contractGuildId) {
			log.Warnf("HandleKnightEvent, NotHandle, Guild:%d", contractGuildId)
			return nil, false
		}

		resp, err := client.GuildCooperationCli.GetGuildCooperationInfos(ctx, contractGuildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleKnightEvent GetGuildCooperationInfos err:%s, anchor_id:%d, guild_id:%d, order_id:%s", err.Error(), e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
			return err, false
		}
		if !resp.GetIsYuyinCoopGuild() {
			log.DebugWithCtx(ctx, "HandleKnightEvent GetGuildCooperationInfos Not Exist, anchor_id:%d, guild_id:%d, order_id:%s", e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
			return nil, false
		}

		e.GuildId = contractGuildId
		e.TargetUid = contractActorId

		if deal_token.HandleDealToken(e.GetDealToken()) != 0 {
			log.ErrorWithCtx(ctx, "handleKnightEvent HandleDealToken err, deal_token", e.GetDealToken())
			return errors.New("deal_token err"), false
		}

		err = store.AwardGoldDiamond(ctx, e)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleKnightEvent AwardKnightGoldDiamond err:%s, anchor_id:%d, guild_id:%d, order_id:%s", err.Error(), e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
			return err, false
		}

		// 神秘人不记录消费榜
		if e.IsUkwPaid {
			return nil, true
		}

		boughtTime := time.Unix(int64(e.GetBoughtTime()), 0)

		// 写入月排行榜数据
		if cacheErr := cache.IncrMonthlyRank(
			ctx, e.GetGuildId(), e.GetPaidUid(), int64(e.GetFee()), boughtTime); cacheErr != nil {
			log.ErrorWithCtx(ctx, "HandleKnightEvent AwardKnightGoldDiamond IncrMonthlyRank failed, err:[%+v]", cacheErr)
		}
		// 写入日排行榜数据
		if cacheErr := cache.IncrDailyRank(
			ctx, e.GetGuildId(), e.GetPaidUid(), int64(e.GetFee()), boughtTime); cacheErr != nil {
			log.ErrorWithCtx(ctx, "HandleKnightEvent AwardKnightGoldDiamond IncrDailyRank failed, err:[%+v]", cacheErr)
		}

		// 消费人数
		if cacheErr := cache.IncrGuildChannelPaidCnt(ctx, e.GetGuildId(), e.GetChannelId(), e.GetTargetUid(), e.GetPaidUid(), boughtTime); cacheErr != nil {
			log.ErrorWithCtx(ctx, "HandleKnightEvent AwardKnightGoldDiamond IncrGuildChannelPaidCnt failed, err:[%+v]", cacheErr)
		}

		return nil, true
	}

	err = store.CreateGoldReconcile(ctx, e)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleKnightEvent CreateGoldReconcile err:%s", err)
		return err, false
	}

	err, ret := awardGold()
	if err != nil {
		_ = store.SetGoldReconcileStatus(ctx, e, mysql.ReconcileStatusFailed)
	} else {
		_ = store.SetGoldReconcileStatus(ctx, e, mysql.ReconcileStatusSuccess)
	}

	return err, ret
}

func (m *Manager) HandlePresentEvent(ctx context.Context, e *pb.AwardGoldOrder) (error, bool) {
	var err error

	channelType := e.GetChannelType()

	store := m.mysqlStore.GetStoreV2(e.GetGoldType())
	if store == nil {
		log.ErrorWithCtx(ctx, "HandlePresentEvent GetStore err, gold_type:%d", e.GetGoldType())
		return errors.New("GetStore gold_type err"), false
	}
	cache := m.cacheStore.GetCacheV2(e.GetGoldType())
	if cache == nil {
		log.ErrorWithCtx(ctx, "HandlePresentEvent GetCacheV2 err, gold_type:%d", e.GetGoldType())
		return errors.New("GetCacheV2 gold_type err"), false
	}

	awardGold := func() (err error, ret bool) {
		channelInfo, sErr := client.ChannelCli.GetChannelDetailInfo(ctx, e.GetTargetUid(), e.GetChannelId())
		if sErr != nil {
			log.ErrorWithCtx(ctx, "HandlePresentEvent GetChannelDetailInfo err:%s, target_uid:%d, order_id:%s", sErr.Error(), e.GetTargetUid(), e.GetOrderId())
			return sErr, true
		}

		e.GuildId = channelInfo.GetBindId()

		if e.ChannelType == pb.ChannelType_CTypeYuyin {
			var contractGuildId, contractActorId uint32

			contractInfo, sErr := client.AnchorContractCli.GetUserContractCacheInfo(ctx, e.GetTargetUid(), channelInfo.GetCreaterUid())
			if sErr != nil {
				log.ErrorWithCtx(ctx, "HandlePresentEvent GetUserContractCacheInfo err:%s, target_uid:%d, order_id:%s", sErr.Error(), e.GetTargetUid(), e.GetOrderId())
				return sErr, true
			}
			if contractInfo.GetContract().GetGuildId() == 0 {
				log.WarnWithCtx(ctx, "HandlePresentEvent warm contract guild = 0, anchor_id:%d, order_id:%s", e.GetTargetUid(), e.GetOrderId())
				return nil, false
			}
			if time.Unix(int64(contractInfo.GetContract().GetExpireTime()), 0).Before(time.Unix(int64(e.GetBoughtTime()), 0)) {
				log.ErrorWithCtx(ctx, "HandlePresentEvent err contract expired, target_uid:%d, guild_id:%d, order_id:%s", e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
				return nil, false
			}

			// 检查签约身份为语音直播
			if !isContactIdentity(contractInfo, anchorcontract_go.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
				log.WarnWithCtx(ctx, "HandlePresentEvent err contract identity is not yuyin, anchor_id:%d, order_id:%s", e.GetTargetUid(), e.GetOrderId())
				return nil, false
			}

			contractGuildId = contractInfo.GetContract().GetGuildId()
			contractActorId = contractInfo.GetContract().GetActorUid()

			if contractGuildId == 0 || contractActorId == 0 {
				log.WarnWithCtx(ctx, "HandlePresentEvent err contractGuildId == 0 || contractActorId == 0")
				return nil, false
			}

			e.GuildId = contractGuildId
			e.TargetUid = contractActorId
		}

		if m.IsNoBonusGuild(e.GuildId) {
			log.Warnf("HandlePresentEvent, NotHandle, Guild:%d", e.GuildId)
			return nil, false
		}

		resp, err := client.GuildCooperationCli.GetGuildCooperationInfos(ctx, e.GuildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandlePresentEvent GetGuildCooperationInfos err:%s, uid:%d, guild_id:%d, order_id:%s", err.Error(), e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
			return err, false
		}
		var bExist bool
		switch channelType {
		case pb.ChannelType_CTypeAmuse:
			bExist = resp.GetIsAmuseCoopGuild()
		case pb.ChannelType_CTypeYuyin:
			bExist = resp.GetIsYuyinCoopGuild()
		default:
			return nil, false
		}
		if !bExist {
			log.DebugWithCtx(ctx, "HandlePresentEvent GetAwardGuildRoomType Not Exist, uid:%d, guild_id:%d, order_id:%s", e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
			return nil, false
		}

		if m.sc.EnableDealToken() && deal_token.HandleDealToken(e.GetDealToken()) != 0 {
			log.ErrorWithCtx(ctx, "HandlePresentEvent HandleDealToken err, deal_token", e.GetDealToken())
			return errors.New("deal_token err"), false
		}

		err = store.AwardGoldDiamond(ctx, e)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandlePresentEvent AwardPresentGoldDiamond err:%s, uid:%d, guild_id:%d, order_id:%s", err.Error(), e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
			return err, false
		}

		// 神秘人不记录消费榜
		if e.IsUkwPaid {
			return nil, true
		}

		boughtTime := time.Unix(int64(e.GetBoughtTime()), 0)

		// 写入月排行榜数据
		if cacheErr := cache.IncrMonthlyRank(
			ctx, e.GetGuildId(), e.GetPaidUid(), int64(e.GetFee()), boughtTime); cacheErr != nil {
			log.ErrorWithCtx(ctx, "HandlePresentEvent AwardKnightGoldDiamond IncrMonthlyRank failed, err:[%+v]", cacheErr)
		}
		// 写入日排行榜数据
		if cacheErr := cache.IncrDailyRank(
			ctx, e.GetGuildId(), e.GetPaidUid(), int64(e.GetFee()), boughtTime); cacheErr != nil {
			log.ErrorWithCtx(ctx, "HandlePresentEvent AwardKnightGoldDiamond IncrDailyRank failed, err:[%+v]", cacheErr)
		}

		// 消费人数
		if cacheErr := cache.IncrGuildChannelPaidCnt(ctx, e.GetGuildId(), e.GetChannelId(), e.GetTargetUid(), e.GetPaidUid(), boughtTime); cacheErr != nil {
			log.ErrorWithCtx(ctx, "HandlePresentEvent AwardGoldDiamond IncrGuildChannelPaidCnt failed, err:[%+v]", cacheErr)
		}

		return nil, true
	}

	err = store.CreateGoldReconcile(ctx, e)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlePresentEvent CreateGoldReconcile err:%s", err)
		return err, false
	}

	err, ret := awardGold()
	if err != nil {
		_ = store.SetGoldReconcileStatus(ctx, e, mysql.ReconcileStatusFailed)
	} else {
		_ = store.SetGoldReconcileStatus(ctx, e, mysql.ReconcileStatusSuccess)
	}

	return err, ret
}

func (m *Manager) HandleTBeanEvent(ctx context.Context, e *pb.AwardGoldOrder) (error, bool) {
	var err error

	ctxUkw, cancel := context.WithTimeout(ctx, time.Millisecond*300)
	defer cancel()
	// 检查消费用户是否是神秘人
	ykwInfo, err := client.YkwCli.GetUKWInfo(ctxUkw, e.GetPaidUid())
	if err != nil {
		log.ErrorWithCtx(ctxUkw, "HandleTBeanEvent GetUKWInfo err:%s", err.Error())
		// ignore
	} else {
		if ykwInfo.GetUkwPermissionInfo().GetSwitch() == youknowwho.UKWSwitchType_UKW_SWITCH_ON {
			e.IsUkwPaid = true
			e.UkwPaidAccount = ykwInfo.GetUkwPersonInfo().GetAccount()
		}
	}

	store := m.mysqlStore.GetStoreV2(e.GetGoldType())
	if store == nil {
		log.ErrorWithCtx(ctx, "HandleTBeanEvent GetStore err, gold_type:%d", e.GetGoldType())
		return errors.New("GetStore gold_type err"), false
	}
	cache := m.cacheStore.GetCacheV2(e.GetGoldType())
	if cache == nil {
		log.ErrorWithCtx(ctx, "HandleTBeanEvent GetCacheV2 err, gold_type:%d", e.GetGoldType())
		return errors.New("GetCacheV2 gold_type err"), false
	}

	// 提前获取房间类型
	channelInfo, sErr := client.ChannelCli.GetChannelDetailInfo(ctx, e.GetTargetUid(), e.GetChannelId())
	if sErr != nil {
		log.ErrorWithCtx(ctx, "HandleTBeanEvent GetChannelDetailInfo err:%s, target_uid:%d, order_id:%s", sErr.Error(), e.GetTargetUid(), e.GetOrderId())
		return sErr, true
	}
	e.ChannelType = pb.ChannelType(*channelInfo.ChannelBindType)
	e.GuildId = channelInfo.GetBindId()

	awardGold := func() (err error, ret bool) {
		// 狼人杀检查娱乐房类型
		if e.SourceType == pb.SourceType_SourceTypeWerewolf {
			if uint32(channelPb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) != *channelInfo.ChannelBindType {
				log.Errorf("HandleTBeanEvent type err :%d", *channelInfo.ChannelBindType)
				return fmt.Errorf("type err"), false
			}
		}

		if m.IsNoBonusGuild(e.GuildId) {
			log.Warnf("HandleTBeanEvent, NotHandle, Guild:%d", e.GuildId)
			return nil, false
		}

		resp, err := client.GuildCooperationCli.GetGuildCooperationInfos(ctx, e.GuildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleTBeanEvent GetGuildCooperationInfos err:%s, uid:%d, guild_id:%d, order_id:%s", err.Error(), e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
			return err, false
		}
		if !resp.IsAmuseCoopGuild {
			log.DebugWithCtx(ctx, "HandleTBeanEvent GetGuildCooperationInfos Not Exist, uid:%d, guild_id:%d, order_id:%s", e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
			return nil, false
		}

		if e.GetDealToken() != "" && deal_token.HandleDealToken(e.GetDealToken()) != 0 {
			log.ErrorWithCtx(ctx, "HandleTBeanEvent HandleDealToken err, deal_token", e.GetDealToken())
			return errors.New("deal_token err"), false
		}

		err = store.AwardGoldDiamond(ctx, e)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleTBeanEvent AwardGoldDiamond err:%s, uid:%d, guild_id:%d, order_id:%s", err.Error(), e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
			return err, false
		}

		// 神秘人不记录消费榜
		if e.IsUkwPaid {
			return nil, true
		}

		boughtTime := time.Unix(int64(e.GetBoughtTime()), 0)

		// 月榜
		if cacheErr := cache.IncrMonthlyRank(ctx, e.GetGuildId(), e.GetPaidUid(), int64(e.GetFee()), boughtTime); cacheErr != nil {
			log.ErrorWithCtx(ctx, "HandleTBeanEvent AwardGoldDiamond IncrMonthlyRank failed, err:[%+v]", cacheErr)
		}
		// 日榜
		if cacheErr := cache.IncrDailyRank(ctx, e.GetGuildId(), e.GetPaidUid(), int64(e.GetFee()), boughtTime); cacheErr != nil {
			log.ErrorWithCtx(ctx, "HandleTBeanEvent AwardGoldDiamond IncrDailyRank failed, err:[%+v]", cacheErr)
		}

		// 消费人数
		if cacheErr := cache.IncrGuildChannelPaidCnt(ctx, e.GetGuildId(), e.GetChannelId(), e.GetTargetUid(), e.GetPaidUid(), boughtTime); cacheErr != nil {
			log.ErrorWithCtx(ctx, "HandleTBeanEvent AwardGoldDiamond IncrGuildChannelPaidCnt failed, err:[%+v]", cacheErr)
		}

		return nil, true
	}

	err = store.CreateGoldReconcile(ctx, e)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleTBeanEvent CreateGoldReconcile err:%s", err)
		return err, false
	}

	err, ret := awardGold()
	if err != nil {
		_ = store.SetGoldReconcileStatus(ctx, e, mysql.ReconcileStatusFailed)
	} else {
		_ = store.SetGoldReconcileStatus(ctx, e, mysql.ReconcileStatusSuccess)
	}

	return err, ret
}

func (m *Manager) HandleESportEvent(ctx context.Context, e *pb.AwardGoldOrder) (error, bool) {
	var err error

	store := m.mysqlStore.GetStoreV2(e.GetGoldType())
	if store == nil {
		log.ErrorWithCtx(ctx, "handleESportEvent GetStore err, gold_type:%d", e.GetGoldType())
		return errors.New("GetStore gold_type err"), false
	}

	awardGold := func() (err error, ret bool) {
		var contractGuildId, contractCoachUid uint32

		contractInfo, sErr := client.AnchorContractCli.GetUserContractCacheInfo(ctx, e.GetTargetUid(), e.GetTargetUid())
		if sErr != nil {
			log.ErrorWithCtx(ctx, "handleESportEvent GetUserContractCacheInfo err:%s, target_uid:%d, order_id:%s", sErr.Error(), e.GetTargetUid(), e.GetOrderId())
			return sErr, true
		}
		if contractInfo.GetContract().GetGuildId() == 0 {
			log.WarnWithCtx(ctx, "handleESportEvent warm contract guild = 0, anchor_id:%d, order_id:%s", e.GetTargetUid(), e.GetOrderId())
			return nil, false
		}
		if time.Unix(int64(contractInfo.GetContract().GetExpireTime()), 0).Before(time.Unix(int64(e.GetBoughtTime()), 0)) {
			log.ErrorWithCtx(ctx, "handleESportEvent err contract expired, target_uid:%d, guild_id:%d, order_id:%s", e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
			return nil, false
		}
		contractGuildId = contractInfo.GetContract().GetGuildId()
		contractCoachUid = contractInfo.GetContract().GetActorUid()

		// 检查签约身份为电竞
		if !isContactIdentity(contractInfo, anchorcontract_go.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS) {
			log.WarnWithCtx(ctx, "handleESportEvent err contract identity is not esports, anchor_id:%d, order_id:%s", e.GetTargetUid(), e.GetOrderId())
			return nil, false
		}

		if contractGuildId == 0 || contractCoachUid == 0 {
			log.WarnWithCtx(ctx, "handleESportEvent err contractGuildId == 0 || contractActorId == 0")
			return nil, false
		}

		e.GuildId = contractGuildId
		e.TargetUid = contractCoachUid

		if m.IsNoBonusGuild(e.GuildId) {
			log.Warnf("handleESportEvent, NotHandle, Guild:%d", e.GuildId)
			return nil, false
		}

		resp, err := client.GuildCooperationCli.GetGuildCooperationInfos(ctx, e.GuildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleESportEvent GetGuildCooperationInfos err:%s, uid:%d, guild_id:%d, order_id:%s", err.Error(), e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
			return err, false
		}
		if !resp.GetIsEsportCoopGuild() {
			log.DebugWithCtx(ctx, "handleESportEvent GetGuildCooperationInfos Not Exist, uid:%d, guild_id:%d, order_id:%s", e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
			return nil, false
		}

		//if e.GetDealToken() != "" && deal_token.HandleDealToken(e.GetDealToken()) != 0 {
		//	log.ErrorWithCtx(ctx, "handleESportEvent HandleDealToken err, deal_token", e.GetDealToken())
		//	return errors.New("deal_token err"), false
		//}

		err = store.AwardGoldDiamond(ctx, e)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleESportEvent AwardGoldDiamond err:%s, uid:%d, guild_id:%d, order_id:%s", err.Error(), e.GetTargetUid(), e.GetGuildId(), e.GetOrderId())
			return err, false
		}

		return nil, true
	}

	err = store.CreateGoldReconcile(ctx, e)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleESportEvent CreateGoldReconcile err:%s", err)
		return err, false
	}

	err, ret := awardGold()
	if err != nil {
		_ = store.SetGoldReconcileStatus(ctx, e, mysql.ReconcileStatusFailed)
	} else {
		_ = store.SetGoldReconcileStatus(ctx, e, mysql.ReconcileStatusSuccess)
	}

	return err, ret
}

func isContactIdentity(info *anchorcontract_go.ContractCacheInfo, identity anchorcontract_go.SIGN_ANCHOR_IDENTITY) bool {
	for _, i := range info.GetInfoList() {
		if anchorcontract_go.SIGN_ANCHOR_IDENTITY(i.GetIdentityType()) == identity {
			return true
		}
	}
	return false
}
