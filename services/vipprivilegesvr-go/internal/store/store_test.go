package store

import (
	"context"
	"database/sql"
	"fmt"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	pb "golang.52tt.com/protocol/services/vipprivilegesvr-go"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func getMockDb() (mock sqlmock.Sqlmock, gormDB *gorm.DB, db *sql.DB) {
	var err error
	db, mock, err = sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
	if err != nil {
		panic(err)
	}
	if gormDB, err = gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		DriverName:                "mysql",
		SkipInitializeWithVersion: true, // auto configure based on currently MySQL version
	}), &gorm.Config{}); err != nil {
		fmt.Println(err)
		return
	}

	gormDB = gormDB.Debug()
	if nil != err {
		panic("Init DB with sqlmock failed")
	}
	return
}
func Test_AddVipKefu(t *testing.T) {
	mock, gormDB, db := getMockDb()
	defer db.Close()

	m := &pb.AddVipKefuReq{
		Uid:      1000,
		Username: "Hello",
		Name:     "World",
		Id:       "ID-1",
		Level:    1,
	}
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `tbl_vipkefu` (`uid`,`username`,`name`,`id`,`level`,`status`) VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `username`=VALUES(`username`),`name`=VALUES(`name`),`id`=VALUES(`id`),`level`=VALUES(`level`)").
		WithArgs(m.Uid, m.Username, m.Name, m.Id, m.Level, 0).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `tbl_vipkefu` (`uid`,`username`,`name`,`id`,`level`,`status`) VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `username`=VALUES(`username`),`name`=VALUES(`name`),`id`=VALUES(`id`),`level`=VALUES(`level`)").
		WithArgs(m.Uid, m.Username, m.Name, m.Id, m.Level, 0).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	ctx := context.Background()

	tests := []struct {
		name    string
		pb      *pb.AddVipKefuReq
		wantErr error
	}{
		{
			name: "MYSQL-Test_AddVipKefu-first",
			pb:   m,
		},
		{
			name: "MYSQL-Test_AddVipKefu-onduplicate",
			pb:   m,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db:         gormDB,
				readOnlyDb: gormDB,
			}
			if err := s.AddVipKefu(ctx, m); err != tt.wantErr {
				t.Errorf("store AddVipKefu() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_DeleteVipKefu(t *testing.T) {
	mock, gormDB, db := getMockDb()
	defer db.Close()

	ctx := context.Background()
	type args struct {
		uid uint32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "Test_DeleteVipKefu-1",
			args: args{
				uid: 1000,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db:         gormDB,
				readOnlyDb: gormDB,
			}
			mock.ExpectBegin()
			mock.ExpectExec("DELETE FROM `tbl_vipkefu` WHERE uid=?").
				WithArgs(tt.args.uid).WillReturnResult(sqlmock.NewResult(1, 1))
			mock.ExpectCommit()
			if err := s.DeleteVipKefu(ctx, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("store DeleteVipKefu() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
