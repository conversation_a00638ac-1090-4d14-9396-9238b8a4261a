package server

import (
	"context"
	"errors"
	"google.golang.org/grpc/codes"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	channelpb_ "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/pia"
	"golang.52tt.com/services/pia/mgr"
)

const (
	PiaSwitchDesc = "房间玩法切换为[Pia戏]"
)

func (p *piaServer) isChannelAdmin(c context.Context, channelId, opUid uint32) error {
	// 判断是否有房管权限
	ok, err := p.mainGameMgr.CheckPermission(c, channelId, opUid)
	if err != nil {
		return err
	}
	if !ok {
		return protocol.NewExactServerError(codes.OK, status.ErrNoPiaPermission, status.MessageFromCode(status.ErrNoPiaPermission))
	}

	return nil
}

func (p *piaServer) SelectDrama(c context.Context, req *pb.SelectDramaReq) (*pb.SelectDramaResp, error) {
	log.Debugf("SelectDrama req: %+v", req)
	out := &pb.SelectDramaResp{}

	// 判断是否有房管权限
	if err := p.isChannelAdmin(c, req.GetChannelId(), req.GetOpUid()); err != nil {
		log.Errorf("SelectDrama.isChannelAdmin error: %v, req: %+v", err, req)
		return out, err
	}

	// 判断房间状态
	if p.mainGameMgr.IsPiaRunning(req.GetChannelId()) {
		return out, protocol.NewExactServerError(codes.OK, status.ErrPiaIsRunning, status.MessageFromCode(status.ErrPiaIsRunning))
	}

	drama, err := p.dramaMgr.GetDramaByID(c, req.GetDramaId())
	if err != nil {
		log.Errorf("SelectDrama.GetDramaByID error: %v, req: %+v", err, req)
		return out, err
	}

	err = p.mainGameMgr.SetDramaInfo(req.GetChannelId(), drama)
	if err != nil {
		log.Errorf("SelectDrama.SetDramaInfo error: %v, req: %+v", err)
		return out, err
	}

	go p.broadcastPiaInfo(req.GetChannelId())

	return out, nil
}

func (p *piaServer) GetChannelPiaStatus(c context.Context, req *pb.GetChannelPiaStatusReq) (*pb.GetChannelPiaStatusResp, error) {
	var err error
	out := &pb.GetChannelPiaStatusResp{}
	out.Entry, err = p.channelConfigMgr.GetPiaRoomPermission(c, req.GetChannelId())
	if err != nil {
		log.Errorf("GetChannelPiaStatus.GetPiaRoomPermission error: %v")
	}
	out.IsOpen, err = p.mainGameMgr.GetPiaSwitch(req.ChannelId)
	if err != nil {
		log.Errorf("GetChannelPiaStatus error: %v", err)
	}
	out.IsSwitchedToNew, err = p.playingController.GetChannelSwitchToNewModeFlag(c, req.ChannelId)
	if err != nil {
		log.ErrorWithCtx(c, "GetChannelPiaStatus failed to GetChannelSwitchToNewModeFlag, channelId:%d, err:%v", req.ChannelId, err)
	}
	return out, nil
}

func (p *piaServer) GetDramaDetailByID(c context.Context, req *pb.GetDramaDetailByIDReq) (*pb.GetDramaDetailByIDResp, error) {
	out := &pb.GetDramaDetailByIDResp{}
	data, err := p.dramaMgr.GetDramaByID(c, req.GetDramaId())
	if err != nil {
		log.Errorf("GetDramaDetailByID error: %v, req: %+v", err, req)
		return out, err
	}
	out.DramaDetail = data
	return out, nil
}

func (p *piaServer) GetDrama(ctx context.Context, req *pb.GetDramaReq) (*pb.GetDramaResp, error) {
	for _, opt := range req.GetSearchOption() {
		log.Debugf("GetDrama searchOption: %+v", opt)
	}

	out := &pb.GetDramaResp{}
	list, err := p.dramaMgr.GetDramaList(req.SearchOption, req.GetPageToken(), req.GetPageSize())
	if err != nil {
		log.Errorf("GetDrama.GetDramaList error: %v, req: %+v", err, req)
		return out, err
	}
	for _, item := range list {
		item.HasPlaying = p.mainGameMgr.HasPlayingChannel(item.GetId())
	}
	out.DramaList = list
	return out, nil
}

func (p *piaServer) GetSearchOptionGroup(ctx context.Context, req *pb.GetSearchOptionGroupReq) (*pb.GetSearchOptionGroupResp, error) {
	out := &pb.GetSearchOptionGroupResp{}
	group, err := p.dramaMgr.GetSearchOptionGroup()
	if err != nil {
		log.Errorf("GetSearchOptionGroup mgr error: %v", err)
		return out, err
	}
	out.SearchOptionGroup = group
	return out, nil
}

func (p *piaServer) GetCurrentPiaInfo(ctx context.Context, req *pb.GetCurrentPiaInfoReq) (*pb.GetCurrentPiaInfoResp, error) {
	log.Debugf("GetCurrentPiaInfo req: %+v", req)

	out := &pb.GetCurrentPiaInfoResp{}
	piaInfo, err := p.piaInfoMgr.GetPiaInfo(context.Background(), req.GetChannelId())
	if err != nil {
		log.Errorf("GetCurrentPiaInfo error: %v, req: %+v", err, req)
		return out, err
	}

	// 获取进房提示
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if ok {
		log.Debugf("GetCurrentPiaInfo serviceInfo: %+v", serviceInfo)
		alertText, err := p.mainGameMgr.GetUserEnterPiaAlert(req.GetChannelId(), serviceInfo.UserID)
		if err != nil {
			log.Errorf("GetCurrentPiaInfo.GetUserEnterPiaAlert error: %v, channelID: %d, uid: %d", err, req.GetChannelId(), serviceInfo.UserID)
		}
		out.MsgForScreen = alertText
	}

	out.PiaInfo = piaInfo
	return out, nil
}

func (p *piaServer) SetPiaSwitch(ctx context.Context, req *pb.SetPiaSwitchReq) (*pb.SetPiaSwitchResp, error) {
	log.Debugf("SetPiaSwitch req: %+v", req)
	out := &pb.SetPiaSwitchResp{}

	// 判断是否有房管权限
	sinfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, errors.New("get serviceInfo error")
	}
	if err := p.isChannelAdmin(ctx, req.GetChannelId(), sinfo.UserID); err != nil {
		log.Errorf("SetPiaSwitch.isChannelAdmin error: %v, req: %+v", err, req)
		return out, err
	}

	entry, _ := p.channelConfigMgr.GetPiaRoomPermission(ctx, req.GetChannelId())
	if !entry {
		return out, protocol.NewExactServerError(codes.OK, status.ErrNoPiaPermission, status.MessageFromCode(status.ErrGuildNoPermission))
	}

	// 未结束切模式上报
	if !req.GetIsOpen() {
		piaInfo, err := p.piaInfoMgr.GetPiaInfo(ctx, req.GetChannelId())
		if err != nil {
			log.Errorf("SetPiaSwitch.GetPiaInfo error: %v, req: %+v", err, req)
			return out, err
		}
		if piaInfo.GetPhase() == uint32(pb.PiaPhaseType_PIA_PHASE_ON_PLAY) {
			p.reporter.ReportPiaInfo(ctx, req.GetChannelId(), mgr.ReportPiaInfoStatusWrong)
		}
	}

	err := p.mainGameMgr.SetPiaSwitch(req.GetChannelId(), req.IsOpen)
	if err != nil {
		return out, err
	}

	_ = p.broadcastPiaSwitch(req.GetChannelId(), req.IsOpen)

	return out, nil
}

func (p *piaServer) SetPiaPhase(ctx context.Context, req *pb.SetPiaPhaseReq) (*pb.SetPiaPhaseResp, error) {
	log.Debugf("SetPiaPhase req: %+v", req)
	out := &pb.SetPiaPhaseResp{}

	// 判断是否有房管权限
	sinfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, errors.New("get serviceInfo error")
	}
	if err := p.isChannelAdmin(ctx, req.GetChannelId(), sinfo.UserID); err != nil {
		log.Errorf("SetPiaPhase.isChannelAdmin error: %v, req: %+v", err, req)
		return out, err
	}

	if req.GetPhase() == uint32(pb.PiaPhaseType_PIA_PHASE_CLOSE) {
		p.reporter.ReportPiaInfo(ctx, req.ChannelId, mgr.ReportPiaInfoStatusNormal)
	}

	err := p.mainGameMgr.SetPiaPhase(ctx, req.GetChannelId(), req.GetDramaId(), req.GetPhase())
	if err != nil {
		log.Errorf("SetPiaPhase error: %v, req: %+v", err, req)
		return out, err
	}

	// 判断pia戏权限
	cinfo, err := p.channelCli.GetChannelSimpleInfo(ctx, 0, req.GetChannelId())
	if err != nil {
		log.Errorf("SetPiaPhase.GetChannelSimpleInfo error: %v, ")
		return out, err
	}
	entry, err := p.channelConfigMgr.GetPiaRoomPermission(ctx, req.GetChannelId())
	if err != nil {
		log.Errorf("GetChannelPiaStatus.GetPiaRoomPermission error: %v")
	}
	if req.GetPhase() == uint32(pb.PiaPhaseType_PIA_PHASE_CLOSE) &&
		cinfo.GetChannelType() == uint32(channelpb_.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) &&
		!entry { // 如果工会房权限回收了, 在该局结束时候推送pia戏关闭
		go func() {
			_ = p.broadcastPiaSwitch(req.GetChannelId(), false)
		}()
		return out, nil
	}

	go p.broadcastPiaInfo(req.ChannelId)

	return out, nil
}

func (p *piaServer) SetPiaProgress(ctx context.Context, req *pb.SetPiaProgressReq) (*pb.SetPiaProgressResp, error) {
	log.Debugf("SetPiaProgress req: %+v", req)

	out := &pb.SetPiaProgressResp{}
	err := p.mainGameMgr.SetPiaProgress(req.GetChannelId(), req.GetOpMic(), req.GetProgress())
	if err != nil {
		log.Errorf("SetPiaProgress error: %v, req: %+v", err, req)
		return out, err
	}

	go p.broadcastPiaInfo(req.ChannelId)

	return out, nil
}

func (p *piaServer) SetCompereMic(ctx context.Context, req *pb.SetCompereMicReq) (*pb.SetCompereMicResp, error) {
	out := &pb.SetCompereMicResp{}
	err := p.mainGameMgr.SetCompereMic(req.GetChannelId(), req.GetMic())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetCompereMic error: %s, req: %+v", err.Error(), req)
		return out, err
	}

	go p.broadcastPiaInfo(req.GetChannelId())

	return out, nil
}

// broadcastPiaInfo 广播Pia戏游戏信息
func (p *piaServer) broadcastPiaInfo(channelID uint32) {
	piaInfo, err := p.piaInfoMgr.GetPiaInfo(context.Background(), channelID)
	if err != nil {
		log.Errorf("broadcastPiaInfo.GetPiaInfo error: %v, channelID: %d", err, channelID)
	}
	err = p.pushMgr.PushPiaInfo(context.Background(), channelID, piaInfo)
	if err != nil {
		log.Errorf("broadcastPiaInfo.PushPiaInfo error: %v, channelID: %d", err, channelID)
	}
}

func (p *piaServer) broadcastPiaSwitch(channelID uint32, isOpen bool) error {
	var piaInfo *pb.PiaInfo
	var err error

	pushMsg := &pb.PiaSwitch{
		IsOpen: isOpen,
	}
	if isOpen { // 开启时候推送剧本信息
		piaInfo, err = p.piaInfoMgr.GetPiaInfo(context.Background(), channelID)
		if err != nil {
			log.Errorf("SetPiaSwitch.GetPiaInfo error: %v, channelID: %d", err, channelID)
			return err
		}
		pushMsg.PiaInfo = piaInfo
		pushMsg.Desc = PiaSwitchDesc
		pushMsg.Hint = mgr.PiaEnterAlertWhenStop
	}

	err = p.pushMgr.PushPiaSwitch(context.Background(), channelID, pushMsg)
	if err != nil {
		log.Errorf("SetPiaSwitch.PushPiaSwitch error: %v, req: %d", err, channelID)
		return err
	}

	return nil
}

func (p *piaServer) GetPlayingChannel(ctx context.Context, req *pb.GetPlayingChannelReq) (*pb.GetPlayingChannelResp, error) {
	out := &pb.GetPlayingChannelResp{}
	playingChannels, err := p.mainGameMgr.GetPlayingChannel(ctx, 0, req.GetDramaId())
	if err != nil {
		log.Errorf("GetPlayingChannel error: %v, req: %+v", err, req)
		return out, err
	}
	out.PlayingChannels = playingChannels
	return out, nil
}

func (p *piaServer) SetBgmInfo(c context.Context, req *pb.SetBgmInfoReq) (*pb.SetBgmInfoResp, error) {
	log.Debugf("SetBgmInfo req: %+v", req)
	out := &pb.SetBgmInfoResp{}

	if !p.mainGameMgr.IsPiaRunning(req.GetChannelId()) { // 忽略未读本状态下的bgm设置请求
		log.Debugf("SetBgmInfo can't do when pia is running, req: %+V", req)
		return out, nil
	}

	err := p.mainGameMgr.SetBgmInfo(req.GetChannelId(), req.GetBgmInfo())
	if err != nil {
		log.Errorf("SetBgmInfo error: %v", err)
		return out, err
	}
	return out, nil
}

func (p *piaServer) GetBgmInfo(c context.Context, req *pb.GetBgmInfoReq) (*pb.GetBgmInfoResp, error) {
	log.Debugf("GetBgmInfo req: %+v", req)

	out := &pb.GetBgmInfoResp{}
	bgmInfo, err := p.mainGameMgr.GetBgmInfo(req.GetChannelId())
	if err != nil {
		log.Errorf("GetBgmInfo error: %+v", err)
		return out, err
	}
	out.BgmInfo = bgmInfo
	return out, nil
}
