// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/pia/mgr (interfaces: OrderDramaListRepo,EventBus,IDService,LockService,PushService,IDrama,IPiaInfoCache,OrderDramaService,Handler,Notify,MicRoleBindingRepo,MicRoleBindingService,IChannelPlayRecord,IStickRoom,IPiaRoomPermission,IDramaV2,ChannelRunningDramaInfoRepo,Convertor,TimeService,DramaService,TimeLineService,ChannelPlayingDramaInfoRepo,ReportDramaRecordStore,DramaWebApi,UserRelatedCopiedDramaRepo)

// Package mgr is a generated GoMock package.
package mgr

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	proto "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	primitive "go.mongodb.org/mongo-driver/bson/primitive"
	options "go.mongodb.org/mongo-driver/mongo/options"
	channel "golang.52tt.com/protocol/app/channel"
	pia "golang.52tt.com/protocol/app/pia"
	push "golang.52tt.com/protocol/app/push"
	pia0 "golang.52tt.com/protocol/services/pia"
)

// MockOrderDramaListRepo is a mock of OrderDramaListRepo interface.
type MockOrderDramaListRepo struct {
	ctrl     *gomock.Controller
	recorder *MockOrderDramaListRepoMockRecorder
}

// MockOrderDramaListRepoMockRecorder is the mock recorder for MockOrderDramaListRepo.
type MockOrderDramaListRepoMockRecorder struct {
	mock *MockOrderDramaListRepo
}

// NewMockOrderDramaListRepo creates a new mock instance.
func NewMockOrderDramaListRepo(ctrl *gomock.Controller) *MockOrderDramaListRepo {
	mock := &MockOrderDramaListRepo{ctrl: ctrl}
	mock.recorder = &MockOrderDramaListRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrderDramaListRepo) EXPECT() *MockOrderDramaListRepoMockRecorder {
	return m.recorder
}

// Delete mocks base method.
func (m *MockOrderDramaListRepo) Delete(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockOrderDramaListRepoMockRecorder) Delete(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockOrderDramaListRepo)(nil).Delete), arg0, arg1)
}

// Fetch mocks base method.
func (m *MockOrderDramaListRepo) Fetch(arg0 context.Context, arg1 uint32, arg2 time.Time) (*OrderDramaList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Fetch", arg0, arg1, arg2)
	ret0, _ := ret[0].(*OrderDramaList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Fetch indicates an expected call of Fetch.
func (mr *MockOrderDramaListRepoMockRecorder) Fetch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Fetch", reflect.TypeOf((*MockOrderDramaListRepo)(nil).Fetch), arg0, arg1, arg2)
}

// FetchAllChannelID mocks base method.
func (m *MockOrderDramaListRepo) FetchAllChannelID(arg0 context.Context) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchAllChannelID", arg0)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchAllChannelID indicates an expected call of FetchAllChannelID.
func (mr *MockOrderDramaListRepoMockRecorder) FetchAllChannelID(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchAllChannelID", reflect.TypeOf((*MockOrderDramaListRepo)(nil).FetchAllChannelID), arg0)
}

// FetchAndCreateIfNotPresent mocks base method.
func (m *MockOrderDramaListRepo) FetchAndCreateIfNotPresent(arg0 context.Context, arg1 uint32, arg2 time.Time) (*OrderDramaList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchAndCreateIfNotPresent", arg0, arg1, arg2)
	ret0, _ := ret[0].(*OrderDramaList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchAndCreateIfNotPresent indicates an expected call of FetchAndCreateIfNotPresent.
func (mr *MockOrderDramaListRepoMockRecorder) FetchAndCreateIfNotPresent(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchAndCreateIfNotPresent", reflect.TypeOf((*MockOrderDramaListRepo)(nil).FetchAndCreateIfNotPresent), arg0, arg1, arg2)
}

// RemoveChannelOrderItemByTime mocks base method.
func (m *MockOrderDramaListRepo) RemoveChannelOrderItemByTime(arg0 context.Context, arg1 uint32, arg2, arg3 time.Time) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveChannelOrderItemByTime", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveChannelOrderItemByTime indicates an expected call of RemoveChannelOrderItemByTime.
func (mr *MockOrderDramaListRepoMockRecorder) RemoveChannelOrderItemByTime(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveChannelOrderItemByTime", reflect.TypeOf((*MockOrderDramaListRepo)(nil).RemoveChannelOrderItemByTime), arg0, arg1, arg2, arg3)
}

// Save mocks base method.
func (m *MockOrderDramaListRepo) Save(arg0 context.Context, arg1 *OrderDramaList) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockOrderDramaListRepoMockRecorder) Save(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockOrderDramaListRepo)(nil).Save), arg0, arg1)
}

// MockEventBus is a mock of EventBus interface.
type MockEventBus struct {
	ctrl     *gomock.Controller
	recorder *MockEventBusMockRecorder
}

// MockEventBusMockRecorder is the mock recorder for MockEventBus.
type MockEventBusMockRecorder struct {
	mock *MockEventBus
}

// NewMockEventBus creates a new mock instance.
func NewMockEventBus(ctrl *gomock.Controller) *MockEventBus {
	mock := &MockEventBus{ctrl: ctrl}
	mock.recorder = &MockEventBusMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventBus) EXPECT() *MockEventBusMockRecorder {
	return m.recorder
}

// Publish mocks base method.
func (m *MockEventBus) Publish(arg0 context.Context, arg1 Notify) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Publish", arg0, arg1)
}

// Publish indicates an expected call of Publish.
func (mr *MockEventBusMockRecorder) Publish(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockEventBus)(nil).Publish), arg0, arg1)
}

// Subscribe mocks base method.
func (m *MockEventBus) Subscribe(arg0 string, arg1 Handler) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Subscribe", arg0, arg1)
}

// Subscribe indicates an expected call of Subscribe.
func (mr *MockEventBusMockRecorder) Subscribe(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Subscribe", reflect.TypeOf((*MockEventBus)(nil).Subscribe), arg0, arg1)
}

// MockIDService is a mock of IDService interface.
type MockIDService struct {
	ctrl     *gomock.Controller
	recorder *MockIDServiceMockRecorder
}

// MockIDServiceMockRecorder is the mock recorder for MockIDService.
type MockIDServiceMockRecorder struct {
	mock *MockIDService
}

// NewMockIDService creates a new mock instance.
func NewMockIDService(ctrl *gomock.Controller) *MockIDService {
	mock := &MockIDService{ctrl: ctrl}
	mock.recorder = &MockIDServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDService) EXPECT() *MockIDServiceMockRecorder {
	return m.recorder
}

// GenerateID mocks base method.
func (m *MockIDService) GenerateID() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateID")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GenerateID indicates an expected call of GenerateID.
func (mr *MockIDServiceMockRecorder) GenerateID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateID", reflect.TypeOf((*MockIDService)(nil).GenerateID))
}

// MockLockService is a mock of LockService interface.
type MockLockService struct {
	ctrl     *gomock.Controller
	recorder *MockLockServiceMockRecorder
}

// MockLockServiceMockRecorder is the mock recorder for MockLockService.
type MockLockServiceMockRecorder struct {
	mock *MockLockService
}

// NewMockLockService creates a new mock instance.
func NewMockLockService(ctrl *gomock.Controller) *MockLockService {
	mock := &MockLockService{ctrl: ctrl}
	mock.recorder = &MockLockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLockService) EXPECT() *MockLockServiceMockRecorder {
	return m.recorder
}

// Lock mocks base method.
func (m *MockLockService) Lock(arg0 string, arg1 time.Duration, arg2 ...LockOption) bool {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Lock", varargs...)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Lock indicates an expected call of Lock.
func (mr *MockLockServiceMockRecorder) Lock(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Lock", reflect.TypeOf((*MockLockService)(nil).Lock), varargs...)
}

// TryLock mocks base method.
func (m *MockLockService) TryLock(arg0 string, arg1 time.Duration) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TryLock", arg0, arg1)
	ret0, _ := ret[0].(bool)
	return ret0
}

// TryLock indicates an expected call of TryLock.
func (mr *MockLockServiceMockRecorder) TryLock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TryLock", reflect.TypeOf((*MockLockService)(nil).TryLock), arg0, arg1)
}

// Unlock mocks base method.
func (m *MockLockService) Unlock(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Unlock", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Unlock indicates an expected call of Unlock.
func (mr *MockLockServiceMockRecorder) Unlock(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unlock", reflect.TypeOf((*MockLockService)(nil).Unlock), arg0)
}

// MockPushService is a mock of PushService interface.
type MockPushService struct {
	ctrl     *gomock.Controller
	recorder *MockPushServiceMockRecorder
}

// MockPushServiceMockRecorder is the mock recorder for MockPushService.
type MockPushServiceMockRecorder struct {
	mock *MockPushService
}

// NewMockPushService creates a new mock instance.
func NewMockPushService(ctrl *gomock.Controller) *MockPushService {
	mock := &MockPushService{ctrl: ctrl}
	mock.recorder = &MockPushServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPushService) EXPECT() *MockPushServiceMockRecorder {
	return m.recorder
}

// PushMsgToChannel mocks base method.
func (m *MockPushService) PushMsgToChannel(arg0 context.Context, arg1 uint32, arg2 channel.ChannelMsgType, arg3 string, arg4 []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushMsgToChannel", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushMsgToChannel indicates an expected call of PushMsgToChannel.
func (mr *MockPushServiceMockRecorder) PushMsgToChannel(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushMsgToChannel", reflect.TypeOf((*MockPushService)(nil).PushMsgToChannel), arg0, arg1, arg2, arg3, arg4)
}

// PushMsgToUsers mocks base method.
func (m *MockPushService) PushMsgToUsers(arg0 context.Context, arg1 []uint32, arg2 push.PushMessage_CMD_TYPE, arg3 string, arg4 proto.MessageV1) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushMsgToUsers", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushMsgToUsers indicates an expected call of PushMsgToUsers.
func (mr *MockPushServiceMockRecorder) PushMsgToUsers(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushMsgToUsers", reflect.TypeOf((*MockPushService)(nil).PushMsgToUsers), arg0, arg1, arg2, arg3, arg4)
}

// PushPiaBgmOperationMsg mocks base method.
func (m *MockPushService) PushPiaBgmOperationMsg(arg0 context.Context, arg1 *BgmProgress) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushPiaBgmOperationMsg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushPiaBgmOperationMsg indicates an expected call of PushPiaBgmOperationMsg.
func (mr *MockPushServiceMockRecorder) PushPiaBgmOperationMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushPiaBgmOperationMsg", reflect.TypeOf((*MockPushService)(nil).PushPiaBgmOperationMsg), arg0, arg1)
}

// PushPiaBgmVolMsg mocks base method.
func (m *MockPushService) PushPiaBgmVolMsg(arg0 context.Context, arg1 *BgmVolSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushPiaBgmVolMsg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushPiaBgmVolMsg indicates an expected call of PushPiaBgmVolMsg.
func (mr *MockPushServiceMockRecorder) PushPiaBgmVolMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushPiaBgmVolMsg", reflect.TypeOf((*MockPushService)(nil).PushPiaBgmVolMsg), arg0, arg1)
}

// PushPiaChangePlayTypeMsg mocks base method.
func (m *MockPushService) PushPiaChangePlayTypeMsg(arg0 context.Context, arg1 *ChannelPlayingDramaInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushPiaChangePlayTypeMsg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushPiaChangePlayTypeMsg indicates an expected call of PushPiaChangePlayTypeMsg.
func (mr *MockPushServiceMockRecorder) PushPiaChangePlayTypeMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushPiaChangePlayTypeMsg", reflect.TypeOf((*MockPushService)(nil).PushPiaChangePlayTypeMsg), arg0, arg1)
}

// PushPiaChangePlayTypeMsgCompatibleWithOldVersion mocks base method.
func (m *MockPushService) PushPiaChangePlayTypeMsgCompatibleWithOldVersion(arg0 context.Context, arg1 *ChannelPlayingDramaInfo, arg2 *DramaDomainEntity) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushPiaChangePlayTypeMsgCompatibleWithOldVersion", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushPiaChangePlayTypeMsgCompatibleWithOldVersion indicates an expected call of PushPiaChangePlayTypeMsgCompatibleWithOldVersion.
func (mr *MockPushServiceMockRecorder) PushPiaChangePlayTypeMsgCompatibleWithOldVersion(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushPiaChangePlayTypeMsgCompatibleWithOldVersion", reflect.TypeOf((*MockPushService)(nil).PushPiaChangePlayTypeMsgCompatibleWithOldVersion), arg0, arg1, arg2)
}

// PushPiaDramaOperationMsg mocks base method.
func (m *MockPushService) PushPiaDramaOperationMsg(arg0 context.Context, arg1 *ChannelPlayingDramaInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushPiaDramaOperationMsg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushPiaDramaOperationMsg indicates an expected call of PushPiaDramaOperationMsg.
func (mr *MockPushServiceMockRecorder) PushPiaDramaOperationMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushPiaDramaOperationMsg", reflect.TypeOf((*MockPushService)(nil).PushPiaDramaOperationMsg), arg0, arg1)
}

// PushPiaInfo mocks base method.
func (m *MockPushService) PushPiaInfo(arg0 context.Context, arg1 uint32, arg2 *pia0.PiaInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushPiaInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushPiaInfo indicates an expected call of PushPiaInfo.
func (mr *MockPushServiceMockRecorder) PushPiaInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushPiaInfo", reflect.TypeOf((*MockPushService)(nil).PushPiaInfo), arg0, arg1, arg2)
}

// PushPiaMicRoleMapMsg mocks base method.
func (m *MockPushService) PushPiaMicRoleMapMsg(arg0 context.Context, arg1 *ChannelMicRoleBindingInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushPiaMicRoleMapMsg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushPiaMicRoleMapMsg indicates an expected call of PushPiaMicRoleMapMsg.
func (mr *MockPushServiceMockRecorder) PushPiaMicRoleMapMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushPiaMicRoleMapMsg", reflect.TypeOf((*MockPushService)(nil).PushPiaMicRoleMapMsg), arg0, arg1)
}

// PushPiaPreformMsg mocks base method.
func (m *MockPushService) PushPiaPreformMsg(arg0 context.Context, arg1 *ChannelPlayingDramaInfo, arg2 *pia0.DramaV2) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushPiaPreformMsg", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushPiaPreformMsg indicates an expected call of PushPiaPreformMsg.
func (mr *MockPushServiceMockRecorder) PushPiaPreformMsg(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushPiaPreformMsg", reflect.TypeOf((*MockPushService)(nil).PushPiaPreformMsg), arg0, arg1, arg2)
}

// PushPiaSwitch mocks base method.
func (m *MockPushService) PushPiaSwitch(arg0 context.Context, arg1 uint32, arg2 *pia0.PiaSwitch) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushPiaSwitch", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushPiaSwitch indicates an expected call of PushPiaSwitch.
func (mr *MockPushServiceMockRecorder) PushPiaSwitch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushPiaSwitch", reflect.TypeOf((*MockPushService)(nil).PushPiaSwitch), arg0, arg1, arg2)
}

// MockIDrama is a mock of IDrama interface.
type MockIDrama struct {
	ctrl     *gomock.Controller
	recorder *MockIDramaMockRecorder
}

// MockIDramaMockRecorder is the mock recorder for MockIDrama.
type MockIDramaMockRecorder struct {
	mock *MockIDrama
}

// NewMockIDrama creates a new mock instance.
func NewMockIDrama(ctrl *gomock.Controller) *MockIDrama {
	mock := &MockIDrama{ctrl: ctrl}
	mock.recorder = &MockIDramaMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDrama) EXPECT() *MockIDramaMockRecorder {
	return m.recorder
}

// GetDramaByID mocks base method.
func (m *MockIDrama) GetDramaByID(arg0 context.Context, arg1 uint32) (*DramaEntity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaByID", arg0, arg1)
	ret0, _ := ret[0].(*DramaEntity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaByID indicates an expected call of GetDramaByID.
func (mr *MockIDramaMockRecorder) GetDramaByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaByID", reflect.TypeOf((*MockIDrama)(nil).GetDramaByID), arg0, arg1)
}

// GetDramaListByCondition mocks base method.
func (m *MockIDrama) GetDramaListByCondition(arg0 context.Context, arg1 []*DramaSearchCondition, arg2, arg3 uint32) ([]*DramaEntity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaListByCondition", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*DramaEntity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaListByCondition indicates an expected call of GetDramaListByCondition.
func (mr *MockIDramaMockRecorder) GetDramaListByCondition(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaListByCondition", reflect.TypeOf((*MockIDrama)(nil).GetDramaListByCondition), arg0, arg1, arg2, arg3)
}

// GetMaxDramaId mocks base method.
func (m *MockIDrama) GetMaxDramaId(arg0 context.Context) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxDramaId", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxDramaId indicates an expected call of GetMaxDramaId.
func (mr *MockIDramaMockRecorder) GetMaxDramaId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxDramaId", reflect.TypeOf((*MockIDrama)(nil).GetMaxDramaId), arg0)
}

// UpdateDrama mocks base method.
func (m *MockIDrama) UpdateDrama(arg0 context.Context, arg1 uint32, arg2 interface{}, arg3 *options.UpdateOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDrama", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDrama indicates an expected call of UpdateDrama.
func (mr *MockIDramaMockRecorder) UpdateDrama(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDrama", reflect.TypeOf((*MockIDrama)(nil).UpdateDrama), arg0, arg1, arg2, arg3)
}

// MockIPiaInfoCache is a mock of IPiaInfoCache interface.
type MockIPiaInfoCache struct {
	ctrl     *gomock.Controller
	recorder *MockIPiaInfoCacheMockRecorder
}

// MockIPiaInfoCacheMockRecorder is the mock recorder for MockIPiaInfoCache.
type MockIPiaInfoCacheMockRecorder struct {
	mock *MockIPiaInfoCache
}

// NewMockIPiaInfoCache creates a new mock instance.
func NewMockIPiaInfoCache(ctrl *gomock.Controller) *MockIPiaInfoCache {
	mock := &MockIPiaInfoCache{ctrl: ctrl}
	mock.recorder = &MockIPiaInfoCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPiaInfoCache) EXPECT() *MockIPiaInfoCacheMockRecorder {
	return m.recorder
}

// AddPlayingChannel mocks base method.
func (m *MockIPiaInfoCache) AddPlayingChannel(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPlayingChannel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddPlayingChannel indicates an expected call of AddPlayingChannel.
func (mr *MockIPiaInfoCacheMockRecorder) AddPlayingChannel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPlayingChannel", reflect.TypeOf((*MockIPiaInfoCache)(nil).AddPlayingChannel), arg0, arg1)
}

// BatchGetPiaInfo mocks base method.
func (m *MockIPiaInfoCache) BatchGetPiaInfo(arg0 []uint32) ([]*pia0.PiaInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPiaInfo", arg0)
	ret0, _ := ret[0].([]*pia0.PiaInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPiaInfo indicates an expected call of BatchGetPiaInfo.
func (mr *MockIPiaInfoCacheMockRecorder) BatchGetPiaInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPiaInfo", reflect.TypeOf((*MockIPiaInfoCache)(nil).BatchGetPiaInfo), arg0)
}

// CleanPiaProgress mocks base method.
func (m *MockIPiaInfoCache) CleanPiaProgress(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CleanPiaProgress", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CleanPiaProgress indicates an expected call of CleanPiaProgress.
func (mr *MockIPiaInfoCacheMockRecorder) CleanPiaProgress(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanPiaProgress", reflect.TypeOf((*MockIPiaInfoCache)(nil).CleanPiaProgress), arg0)
}

// ClearPiaInfo mocks base method.
func (m *MockIPiaInfoCache) ClearPiaInfo(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearPiaInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearPiaInfo indicates an expected call of ClearPiaInfo.
func (mr *MockIPiaInfoCacheMockRecorder) ClearPiaInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearPiaInfo", reflect.TypeOf((*MockIPiaInfoCache)(nil).ClearPiaInfo), arg0)
}

// DelPiaInfo mocks base method.
func (m *MockIPiaInfoCache) DelPiaInfo(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPiaInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelPiaInfo indicates an expected call of DelPiaInfo.
func (mr *MockIPiaInfoCacheMockRecorder) DelPiaInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPiaInfo", reflect.TypeOf((*MockIPiaInfoCache)(nil).DelPiaInfo), arg0)
}

// GetBgmInfo mocks base method.
func (m *MockIPiaInfoCache) GetBgmInfo(arg0 uint32) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBgmInfo", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBgmInfo indicates an expected call of GetBgmInfo.
func (mr *MockIPiaInfoCacheMockRecorder) GetBgmInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBgmInfo", reflect.TypeOf((*MockIPiaInfoCache)(nil).GetBgmInfo), arg0)
}

// GetPiaInfo mocks base method.
func (m *MockIPiaInfoCache) GetPiaInfo(arg0 uint32) (*pia0.PiaInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPiaInfo", arg0)
	ret0, _ := ret[0].(*pia0.PiaInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPiaInfo indicates an expected call of GetPiaInfo.
func (mr *MockIPiaInfoCacheMockRecorder) GetPiaInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPiaInfo", reflect.TypeOf((*MockIPiaInfoCache)(nil).GetPiaInfo), arg0)
}

// GetPiaPhase mocks base method.
func (m *MockIPiaInfoCache) GetPiaPhase(arg0 uint32) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPiaPhase", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetPiaPhase indicates an expected call of GetPiaPhase.
func (mr *MockIPiaInfoCacheMockRecorder) GetPiaPhase(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPiaPhase", reflect.TypeOf((*MockIPiaInfoCache)(nil).GetPiaPhase), arg0)
}

// GetPiaStartTime mocks base method.
func (m *MockIPiaInfoCache) GetPiaStartTime(arg0 uint32) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPiaStartTime", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetPiaStartTime indicates an expected call of GetPiaStartTime.
func (mr *MockIPiaInfoCacheMockRecorder) GetPiaStartTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPiaStartTime", reflect.TypeOf((*MockIPiaInfoCache)(nil).GetPiaStartTime), arg0)
}

// GetPiaSwitch mocks base method.
func (m *MockIPiaInfoCache) GetPiaSwitch(arg0 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPiaSwitch", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPiaSwitch indicates an expected call of GetPiaSwitch.
func (mr *MockIPiaInfoCacheMockRecorder) GetPiaSwitch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPiaSwitch", reflect.TypeOf((*MockIPiaInfoCache)(nil).GetPiaSwitch), arg0)
}

// GetPlayingChannel mocks base method.
func (m *MockIPiaInfoCache) GetPlayingChannel(arg0 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlayingChannel", arg0)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlayingChannel indicates an expected call of GetPlayingChannel.
func (mr *MockIPiaInfoCacheMockRecorder) GetPlayingChannel(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlayingChannel", reflect.TypeOf((*MockIPiaInfoCache)(nil).GetPlayingChannel), arg0)
}

// HasPlayingChannel mocks base method.
func (m *MockIPiaInfoCache) HasPlayingChannel(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasPlayingChannel", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// HasPlayingChannel indicates an expected call of HasPlayingChannel.
func (mr *MockIPiaInfoCacheMockRecorder) HasPlayingChannel(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasPlayingChannel", reflect.TypeOf((*MockIPiaInfoCache)(nil).HasPlayingChannel), arg0)
}

// IsRecentlyEntered mocks base method.
func (m *MockIPiaInfoCache) IsRecentlyEntered(arg0, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsRecentlyEntered", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsRecentlyEntered indicates an expected call of IsRecentlyEntered.
func (mr *MockIPiaInfoCacheMockRecorder) IsRecentlyEntered(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsRecentlyEntered", reflect.TypeOf((*MockIPiaInfoCache)(nil).IsRecentlyEntered), arg0, arg1)
}

// RMPlayingChannel mocks base method.
func (m *MockIPiaInfoCache) RMPlayingChannel(arg0 uint32, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RMPlayingChannel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RMPlayingChannel indicates an expected call of RMPlayingChannel.
func (mr *MockIPiaInfoCacheMockRecorder) RMPlayingChannel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RMPlayingChannel", reflect.TypeOf((*MockIPiaInfoCache)(nil).RMPlayingChannel), arg0, arg1)
}

// SetBgmInfo mocks base method.
func (m *MockIPiaInfoCache) SetBgmInfo(arg0 uint32, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBgmInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetBgmInfo indicates an expected call of SetBgmInfo.
func (mr *MockIPiaInfoCacheMockRecorder) SetBgmInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBgmInfo", reflect.TypeOf((*MockIPiaInfoCache)(nil).SetBgmInfo), arg0, arg1)
}

// SetCompereMic mocks base method.
func (m *MockIPiaInfoCache) SetCompereMic(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCompereMic", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetCompereMic indicates an expected call of SetCompereMic.
func (mr *MockIPiaInfoCacheMockRecorder) SetCompereMic(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCompereMic", reflect.TypeOf((*MockIPiaInfoCache)(nil).SetCompereMic), arg0, arg1)
}

// SetDramaInfo mocks base method.
func (m *MockIPiaInfoCache) SetDramaInfo(arg0 uint32, arg1 *pia0.Drama) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDramaInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDramaInfo indicates an expected call of SetDramaInfo.
func (mr *MockIPiaInfoCacheMockRecorder) SetDramaInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDramaInfo", reflect.TypeOf((*MockIPiaInfoCache)(nil).SetDramaInfo), arg0, arg1)
}

// SetPiaPhase mocks base method.
func (m *MockIPiaInfoCache) SetPiaPhase(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPiaPhase", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPiaPhase indicates an expected call of SetPiaPhase.
func (mr *MockIPiaInfoCacheMockRecorder) SetPiaPhase(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPiaPhase", reflect.TypeOf((*MockIPiaInfoCache)(nil).SetPiaPhase), arg0, arg1)
}

// SetPiaProgress mocks base method.
func (m *MockIPiaInfoCache) SetPiaProgress(arg0, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPiaProgress", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPiaProgress indicates an expected call of SetPiaProgress.
func (mr *MockIPiaInfoCacheMockRecorder) SetPiaProgress(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPiaProgress", reflect.TypeOf((*MockIPiaInfoCache)(nil).SetPiaProgress), arg0, arg1, arg2)
}

// SetPiaStartTime mocks base method.
func (m *MockIPiaInfoCache) SetPiaStartTime(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPiaStartTime", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPiaStartTime indicates an expected call of SetPiaStartTime.
func (mr *MockIPiaInfoCacheMockRecorder) SetPiaStartTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPiaStartTime", reflect.TypeOf((*MockIPiaInfoCache)(nil).SetPiaStartTime), arg0, arg1)
}

// SetPiaSwitch mocks base method.
func (m *MockIPiaInfoCache) SetPiaSwitch(arg0 uint32, arg1 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPiaSwitch", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPiaSwitch indicates an expected call of SetPiaSwitch.
func (mr *MockIPiaInfoCacheMockRecorder) SetPiaSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPiaSwitch", reflect.TypeOf((*MockIPiaInfoCache)(nil).SetPiaSwitch), arg0, arg1)
}

// MockOrderDramaService is a mock of OrderDramaService interface.
type MockOrderDramaService struct {
	ctrl     *gomock.Controller
	recorder *MockOrderDramaServiceMockRecorder
}

// MockOrderDramaServiceMockRecorder is the mock recorder for MockOrderDramaService.
type MockOrderDramaServiceMockRecorder struct {
	mock *MockOrderDramaService
}

// NewMockOrderDramaService creates a new mock instance.
func NewMockOrderDramaService(ctrl *gomock.Controller) *MockOrderDramaService {
	mock := &MockOrderDramaService{ctrl: ctrl}
	mock.recorder = &MockOrderDramaServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrderDramaService) EXPECT() *MockOrderDramaServiceMockRecorder {
	return m.recorder
}

// BatchDeleteOrderDrama mocks base method.
func (m *MockOrderDramaService) BatchDeleteOrderDrama(arg0 context.Context, arg1 *BatchDeleteOrderDramaReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteOrderDrama", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteOrderDrama indicates an expected call of BatchDeleteOrderDrama.
func (mr *MockOrderDramaServiceMockRecorder) BatchDeleteOrderDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteOrderDrama", reflect.TypeOf((*MockOrderDramaService)(nil).BatchDeleteOrderDrama), arg0, arg1)
}

// BatchDeleteOrderDramaByDramaIDs mocks base method.
func (m *MockOrderDramaService) BatchDeleteOrderDramaByDramaIDs(arg0 context.Context, arg1 uint32, arg2 ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchDeleteOrderDramaByDramaIDs", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteOrderDramaByDramaIDs indicates an expected call of BatchDeleteOrderDramaByDramaIDs.
func (mr *MockOrderDramaServiceMockRecorder) BatchDeleteOrderDramaByDramaIDs(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteOrderDramaByDramaIDs", reflect.TypeOf((*MockOrderDramaService)(nil).BatchDeleteOrderDramaByDramaIDs), varargs...)
}

// ClearChannelOrderDrama mocks base method.
func (m *MockOrderDramaService) ClearChannelOrderDrama(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearChannelOrderDrama", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearChannelOrderDrama indicates an expected call of ClearChannelOrderDrama.
func (mr *MockOrderDramaServiceMockRecorder) ClearChannelOrderDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearChannelOrderDrama", reflect.TypeOf((*MockOrderDramaService)(nil).ClearChannelOrderDrama), arg0, arg1)
}

// ClearOutDateOrderDrama mocks base method.
func (m *MockOrderDramaService) ClearOutDateOrderDrama(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearOutDateOrderDrama", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearOutDateOrderDrama indicates an expected call of ClearOutDateOrderDrama.
func (mr *MockOrderDramaServiceMockRecorder) ClearOutDateOrderDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearOutDateOrderDrama", reflect.TypeOf((*MockOrderDramaService)(nil).ClearOutDateOrderDrama), arg0, arg1)
}

// GetOrderDramaByChannelIDAndIndexID mocks base method.
func (m *MockOrderDramaService) GetOrderDramaByChannelIDAndIndexID(arg0 context.Context, arg1 uint32, arg2 int64) (*OrderDrama, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderDramaByChannelIDAndIndexID", arg0, arg1, arg2)
	ret0, _ := ret[0].(*OrderDrama)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderDramaByChannelIDAndIndexID indicates an expected call of GetOrderDramaByChannelIDAndIndexID.
func (mr *MockOrderDramaServiceMockRecorder) GetOrderDramaByChannelIDAndIndexID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderDramaByChannelIDAndIndexID", reflect.TypeOf((*MockOrderDramaService)(nil).GetOrderDramaByChannelIDAndIndexID), arg0, arg1, arg2)
}

// ListAllChannelThatOrderDrama mocks base method.
func (m *MockOrderDramaService) ListAllChannelThatOrderDrama(arg0 context.Context) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAllChannelThatOrderDrama", arg0)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAllChannelThatOrderDrama indicates an expected call of ListAllChannelThatOrderDrama.
func (mr *MockOrderDramaServiceMockRecorder) ListAllChannelThatOrderDrama(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllChannelThatOrderDrama", reflect.TypeOf((*MockOrderDramaService)(nil).ListAllChannelThatOrderDrama), arg0)
}

// OrderDrama mocks base method.
func (m *MockOrderDramaService) OrderDrama(arg0 context.Context, arg1 *pia0.OrderDramaReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OrderDrama", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// OrderDrama indicates an expected call of OrderDrama.
func (mr *MockOrderDramaServiceMockRecorder) OrderDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OrderDrama", reflect.TypeOf((*MockOrderDramaService)(nil).OrderDrama), arg0, arg1)
}

// MockHandler is a mock of Handler interface.
type MockHandler struct {
	ctrl     *gomock.Controller
	recorder *MockHandlerMockRecorder
}

// MockHandlerMockRecorder is the mock recorder for MockHandler.
type MockHandlerMockRecorder struct {
	mock *MockHandler
}

// NewMockHandler creates a new mock instance.
func NewMockHandler(ctrl *gomock.Controller) *MockHandler {
	mock := &MockHandler{ctrl: ctrl}
	mock.recorder = &MockHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockHandler) EXPECT() *MockHandlerMockRecorder {
	return m.recorder
}

// HandleMsg mocks base method.
func (m *MockHandler) HandleMsg(arg0 context.Context, arg1 Notify) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleMsg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleMsg indicates an expected call of HandleMsg.
func (mr *MockHandlerMockRecorder) HandleMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleMsg", reflect.TypeOf((*MockHandler)(nil).HandleMsg), arg0, arg1)
}

// MockNotify is a mock of Notify interface.
type MockNotify struct {
	ctrl     *gomock.Controller
	recorder *MockNotifyMockRecorder
}

// MockNotifyMockRecorder is the mock recorder for MockNotify.
type MockNotifyMockRecorder struct {
	mock *MockNotify
}

// NewMockNotify creates a new mock instance.
func NewMockNotify(ctrl *gomock.Controller) *MockNotify {
	mock := &MockNotify{ctrl: ctrl}
	mock.recorder = &MockNotifyMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNotify) EXPECT() *MockNotifyMockRecorder {
	return m.recorder
}

// Topic mocks base method.
func (m *MockNotify) Topic() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Topic")
	ret0, _ := ret[0].(string)
	return ret0
}

// Topic indicates an expected call of Topic.
func (mr *MockNotifyMockRecorder) Topic() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Topic", reflect.TypeOf((*MockNotify)(nil).Topic))
}

// MockMicRoleBindingRepo is a mock of MicRoleBindingRepo interface.
type MockMicRoleBindingRepo struct {
	ctrl     *gomock.Controller
	recorder *MockMicRoleBindingRepoMockRecorder
}

// MockMicRoleBindingRepoMockRecorder is the mock recorder for MockMicRoleBindingRepo.
type MockMicRoleBindingRepoMockRecorder struct {
	mock *MockMicRoleBindingRepo
}

// NewMockMicRoleBindingRepo creates a new mock instance.
func NewMockMicRoleBindingRepo(ctrl *gomock.Controller) *MockMicRoleBindingRepo {
	mock := &MockMicRoleBindingRepo{ctrl: ctrl}
	mock.recorder = &MockMicRoleBindingRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMicRoleBindingRepo) EXPECT() *MockMicRoleBindingRepoMockRecorder {
	return m.recorder
}

// BatchFetch mocks base method.
func (m *MockMicRoleBindingRepo) BatchFetch(arg0 context.Context, arg1 []uint32) ([]*ChannelMicRoleBindingInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchFetch", arg0, arg1)
	ret0, _ := ret[0].([]*ChannelMicRoleBindingInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchFetch indicates an expected call of BatchFetch.
func (mr *MockMicRoleBindingRepoMockRecorder) BatchFetch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchFetch", reflect.TypeOf((*MockMicRoleBindingRepo)(nil).BatchFetch), arg0, arg1)
}

// Delete mocks base method.
func (m *MockMicRoleBindingRepo) Delete(arg0 context.Context, arg1 ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Delete", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockMicRoleBindingRepoMockRecorder) Delete(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockMicRoleBindingRepo)(nil).Delete), varargs...)
}

// FetchByChannelID mocks base method.
func (m *MockMicRoleBindingRepo) FetchByChannelID(arg0 context.Context, arg1 uint32) (*ChannelMicRoleBindingInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchByChannelID", arg0, arg1)
	ret0, _ := ret[0].(*ChannelMicRoleBindingInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchByChannelID indicates an expected call of FetchByChannelID.
func (mr *MockMicRoleBindingRepoMockRecorder) FetchByChannelID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchByChannelID", reflect.TypeOf((*MockMicRoleBindingRepo)(nil).FetchByChannelID), arg0, arg1)
}

// FetchByChannelIDAndCreateIfNotPresent mocks base method.
func (m *MockMicRoleBindingRepo) FetchByChannelIDAndCreateIfNotPresent(arg0 context.Context, arg1 uint32) (*ChannelMicRoleBindingInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchByChannelIDAndCreateIfNotPresent", arg0, arg1)
	ret0, _ := ret[0].(*ChannelMicRoleBindingInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchByChannelIDAndCreateIfNotPresent indicates an expected call of FetchByChannelIDAndCreateIfNotPresent.
func (mr *MockMicRoleBindingRepoMockRecorder) FetchByChannelIDAndCreateIfNotPresent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchByChannelIDAndCreateIfNotPresent", reflect.TypeOf((*MockMicRoleBindingRepo)(nil).FetchByChannelIDAndCreateIfNotPresent), arg0, arg1)
}

// Save mocks base method.
func (m *MockMicRoleBindingRepo) Save(arg0 context.Context, arg1 *ChannelMicRoleBindingInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockMicRoleBindingRepoMockRecorder) Save(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockMicRoleBindingRepo)(nil).Save), arg0, arg1)
}

// MockMicRoleBindingService is a mock of MicRoleBindingService interface.
type MockMicRoleBindingService struct {
	ctrl     *gomock.Controller
	recorder *MockMicRoleBindingServiceMockRecorder
}

// MockMicRoleBindingServiceMockRecorder is the mock recorder for MockMicRoleBindingService.
type MockMicRoleBindingServiceMockRecorder struct {
	mock *MockMicRoleBindingService
}

// NewMockMicRoleBindingService creates a new mock instance.
func NewMockMicRoleBindingService(ctrl *gomock.Controller) *MockMicRoleBindingService {
	mock := &MockMicRoleBindingService{ctrl: ctrl}
	mock.recorder = &MockMicRoleBindingServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMicRoleBindingService) EXPECT() *MockMicRoleBindingServiceMockRecorder {
	return m.recorder
}

// AddMicRoleBinding mocks base method.
func (m *MockMicRoleBindingService) AddMicRoleBinding(arg0 context.Context, arg1 *pia0.PiaSelectRoleReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMicRoleBinding", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddMicRoleBinding indicates an expected call of AddMicRoleBinding.
func (mr *MockMicRoleBindingServiceMockRecorder) AddMicRoleBinding(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMicRoleBinding", reflect.TypeOf((*MockMicRoleBindingService)(nil).AddMicRoleBinding), arg0, arg1)
}

// BatchGetMicRoleBinding mocks base method.
func (m *MockMicRoleBindingService) BatchGetMicRoleBinding(arg0 context.Context, arg1 []uint32) (map[uint32]*ChannelMicRoleBindingInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetMicRoleBinding", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*ChannelMicRoleBindingInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetMicRoleBinding indicates an expected call of BatchGetMicRoleBinding.
func (mr *MockMicRoleBindingServiceMockRecorder) BatchGetMicRoleBinding(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetMicRoleBinding", reflect.TypeOf((*MockMicRoleBindingService)(nil).BatchGetMicRoleBinding), arg0, arg1)
}

// Clear mocks base method.
func (m *MockMicRoleBindingService) Clear(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Clear", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Clear indicates an expected call of Clear.
func (mr *MockMicRoleBindingServiceMockRecorder) Clear(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Clear", reflect.TypeOf((*MockMicRoleBindingService)(nil).Clear), arg0, arg1)
}

// GetMicRoleBinding mocks base method.
func (m *MockMicRoleBindingService) GetMicRoleBinding(arg0 context.Context, arg1 uint32) (*ChannelMicRoleBindingInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMicRoleBinding", arg0, arg1)
	ret0, _ := ret[0].(*ChannelMicRoleBindingInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMicRoleBinding indicates an expected call of GetMicRoleBinding.
func (mr *MockMicRoleBindingServiceMockRecorder) GetMicRoleBinding(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMicRoleBinding", reflect.TypeOf((*MockMicRoleBindingService)(nil).GetMicRoleBinding), arg0, arg1)
}

// RemoveMicRoleBinding mocks base method.
func (m *MockMicRoleBindingService) RemoveMicRoleBinding(arg0 context.Context, arg1 *pia0.PiaCancelSelectRoleReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveMicRoleBinding", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveMicRoleBinding indicates an expected call of RemoveMicRoleBinding.
func (mr *MockMicRoleBindingServiceMockRecorder) RemoveMicRoleBinding(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveMicRoleBinding", reflect.TypeOf((*MockMicRoleBindingService)(nil).RemoveMicRoleBinding), arg0, arg1)
}

// MockIChannelPlayRecord is a mock of IChannelPlayRecord interface.
type MockIChannelPlayRecord struct {
	ctrl     *gomock.Controller
	recorder *MockIChannelPlayRecordMockRecorder
}

// MockIChannelPlayRecordMockRecorder is the mock recorder for MockIChannelPlayRecord.
type MockIChannelPlayRecordMockRecorder struct {
	mock *MockIChannelPlayRecord
}

// NewMockIChannelPlayRecord creates a new mock instance.
func NewMockIChannelPlayRecord(ctrl *gomock.Controller) *MockIChannelPlayRecord {
	mock := &MockIChannelPlayRecord{ctrl: ctrl}
	mock.recorder = &MockIChannelPlayRecordMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIChannelPlayRecord) EXPECT() *MockIChannelPlayRecordMockRecorder {
	return m.recorder
}

// FindLastPlayDramaID mocks base method.
func (m *MockIChannelPlayRecord) FindLastPlayDramaID(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindLastPlayDramaID", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindLastPlayDramaID indicates an expected call of FindLastPlayDramaID.
func (mr *MockIChannelPlayRecordMockRecorder) FindLastPlayDramaID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindLastPlayDramaID", reflect.TypeOf((*MockIChannelPlayRecord)(nil).FindLastPlayDramaID), arg0, arg1)
}

// RecordLastPlayDrama mocks base method.
func (m *MockIChannelPlayRecord) RecordLastPlayDrama(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordLastPlayDrama", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordLastPlayDrama indicates an expected call of RecordLastPlayDrama.
func (mr *MockIChannelPlayRecordMockRecorder) RecordLastPlayDrama(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordLastPlayDrama", reflect.TypeOf((*MockIChannelPlayRecord)(nil).RecordLastPlayDrama), arg0, arg1, arg2)
}

// MockIStickRoom is a mock of IStickRoom interface.
type MockIStickRoom struct {
	ctrl     *gomock.Controller
	recorder *MockIStickRoomMockRecorder
}

// MockIStickRoomMockRecorder is the mock recorder for MockIStickRoom.
type MockIStickRoomMockRecorder struct {
	mock *MockIStickRoom
}

// NewMockIStickRoom creates a new mock instance.
func NewMockIStickRoom(ctrl *gomock.Controller) *MockIStickRoom {
	mock := &MockIStickRoom{ctrl: ctrl}
	mock.recorder = &MockIStickRoomMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStickRoom) EXPECT() *MockIStickRoomMockRecorder {
	return m.recorder
}

// AddStickPiaRoom mocks base method.
func (m *MockIStickRoom) AddStickPiaRoom(arg0 context.Context, arg1 []*pia0.AddStickPiaRoomReq_StickPiaRoomReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddStickPiaRoom", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddStickPiaRoom indicates an expected call of AddStickPiaRoom.
func (mr *MockIStickRoomMockRecorder) AddStickPiaRoom(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddStickPiaRoom", reflect.TypeOf((*MockIStickRoom)(nil).AddStickPiaRoom), arg0, arg1)
}

// DeleteStickPiaRoom mocks base method.
func (m *MockIStickRoom) DeleteStickPiaRoom(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteStickPiaRoom", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteStickPiaRoom indicates an expected call of DeleteStickPiaRoom.
func (mr *MockIStickRoomMockRecorder) DeleteStickPiaRoom(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteStickPiaRoom", reflect.TypeOf((*MockIStickRoom)(nil).DeleteStickPiaRoom), arg0, arg1)
}

// GetStickPiaRoom mocks base method.
func (m *MockIStickRoom) GetStickPiaRoom(arg0 context.Context, arg1 *StickRoomSearchOption) ([]*StickRoomRecord, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStickPiaRoom", arg0, arg1)
	ret0, _ := ret[0].([]*StickRoomRecord)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetStickPiaRoom indicates an expected call of GetStickPiaRoom.
func (mr *MockIStickRoomMockRecorder) GetStickPiaRoom(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStickPiaRoom", reflect.TypeOf((*MockIStickRoom)(nil).GetStickPiaRoom), arg0, arg1)
}

// SearchStickRoom mocks base method.
func (m *MockIStickRoom) SearchStickRoom(arg0 context.Context, arg1 interface{}) ([]*StickRoomRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchStickRoom", arg0, arg1)
	ret0, _ := ret[0].([]*StickRoomRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchStickRoom indicates an expected call of SearchStickRoom.
func (mr *MockIStickRoomMockRecorder) SearchStickRoom(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchStickRoom", reflect.TypeOf((*MockIStickRoom)(nil).SearchStickRoom), arg0, arg1)
}

// UpdateStickPiaRoom mocks base method.
func (m *MockIStickRoom) UpdateStickPiaRoom(arg0 context.Context, arg1 string, arg2, arg3 uint32, arg4, arg5 int64, arg6 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStickPiaRoom", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStickPiaRoom indicates an expected call of UpdateStickPiaRoom.
func (mr *MockIStickRoomMockRecorder) UpdateStickPiaRoom(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStickPiaRoom", reflect.TypeOf((*MockIStickRoom)(nil).UpdateStickPiaRoom), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// MockIPiaRoomPermission is a mock of IPiaRoomPermission interface.
type MockIPiaRoomPermission struct {
	ctrl     *gomock.Controller
	recorder *MockIPiaRoomPermissionMockRecorder
}

// MockIPiaRoomPermissionMockRecorder is the mock recorder for MockIPiaRoomPermission.
type MockIPiaRoomPermissionMockRecorder struct {
	mock *MockIPiaRoomPermission
}

// NewMockIPiaRoomPermission creates a new mock instance.
func NewMockIPiaRoomPermission(ctrl *gomock.Controller) *MockIPiaRoomPermission {
	mock := &MockIPiaRoomPermission{ctrl: ctrl}
	mock.recorder = &MockIPiaRoomPermissionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPiaRoomPermission) EXPECT() *MockIPiaRoomPermissionMockRecorder {
	return m.recorder
}

// AddPiaRoomPermission mocks base method.
func (m *MockIPiaRoomPermission) AddPiaRoomPermission(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPiaRoomPermission", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddPiaRoomPermission indicates an expected call of AddPiaRoomPermission.
func (mr *MockIPiaRoomPermissionMockRecorder) AddPiaRoomPermission(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPiaRoomPermission", reflect.TypeOf((*MockIPiaRoomPermission)(nil).AddPiaRoomPermission), arg0, arg1, arg2)
}

// BatchCancelPiaRoomPermission mocks base method.
func (m *MockIPiaRoomPermission) BatchCancelPiaRoomPermission(arg0 context.Context, arg1 []uint32, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCancelPiaRoomPermission", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCancelPiaRoomPermission indicates an expected call of BatchCancelPiaRoomPermission.
func (mr *MockIPiaRoomPermissionMockRecorder) BatchCancelPiaRoomPermission(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCancelPiaRoomPermission", reflect.TypeOf((*MockIPiaRoomPermission)(nil).BatchCancelPiaRoomPermission), arg0, arg1, arg2)
}

// BatchGetPiaRoomPermission mocks base method.
func (m *MockIPiaRoomPermission) BatchGetPiaRoomPermission(arg0 context.Context, arg1 []uint32, arg2 uint32) (map[uint32]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPiaRoomPermission", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPiaRoomPermission indicates an expected call of BatchGetPiaRoomPermission.
func (mr *MockIPiaRoomPermissionMockRecorder) BatchGetPiaRoomPermission(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPiaRoomPermission", reflect.TypeOf((*MockIPiaRoomPermission)(nil).BatchGetPiaRoomPermission), arg0, arg1, arg2)
}

// BatchGrantPiaRoomPermission mocks base method.
func (m *MockIPiaRoomPermission) BatchGrantPiaRoomPermission(arg0 context.Context, arg1 []uint32, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGrantPiaRoomPermission", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchGrantPiaRoomPermission indicates an expected call of BatchGrantPiaRoomPermission.
func (mr *MockIPiaRoomPermissionMockRecorder) BatchGrantPiaRoomPermission(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGrantPiaRoomPermission", reflect.TypeOf((*MockIPiaRoomPermission)(nil).BatchGrantPiaRoomPermission), arg0, arg1, arg2)
}

// DeletePiaRoomPermission mocks base method.
func (m *MockIPiaRoomPermission) DeletePiaRoomPermission(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePiaRoomPermission", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePiaRoomPermission indicates an expected call of DeletePiaRoomPermission.
func (mr *MockIPiaRoomPermissionMockRecorder) DeletePiaRoomPermission(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePiaRoomPermission", reflect.TypeOf((*MockIPiaRoomPermission)(nil).DeletePiaRoomPermission), arg0, arg1, arg2)
}

// GetPiaRoomPermission mocks base method.
func (m *MockIPiaRoomPermission) GetPiaRoomPermission(arg0 context.Context, arg1, arg2 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPiaRoomPermission", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPiaRoomPermission indicates an expected call of GetPiaRoomPermission.
func (mr *MockIPiaRoomPermissionMockRecorder) GetPiaRoomPermission(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPiaRoomPermission", reflect.TypeOf((*MockIPiaRoomPermission)(nil).GetPiaRoomPermission), arg0, arg1, arg2)
}

// MockIDramaV2 is a mock of IDramaV2 interface.
type MockIDramaV2 struct {
	ctrl     *gomock.Controller
	recorder *MockIDramaV2MockRecorder
}

// MockIDramaV2MockRecorder is the mock recorder for MockIDramaV2.
type MockIDramaV2MockRecorder struct {
	mock *MockIDramaV2
}

// NewMockIDramaV2 creates a new mock instance.
func NewMockIDramaV2(ctrl *gomock.Controller) *MockIDramaV2 {
	mock := &MockIDramaV2{ctrl: ctrl}
	mock.recorder = &MockIDramaV2MockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDramaV2) EXPECT() *MockIDramaV2MockRecorder {
	return m.recorder
}

// BatchDeleteDramaByDramaIds mocks base method.
func (m *MockIDramaV2) BatchDeleteDramaByDramaIds(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteDramaByDramaIds", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteDramaByDramaIds indicates an expected call of BatchDeleteDramaByDramaIds.
func (mr *MockIDramaV2MockRecorder) BatchDeleteDramaByDramaIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteDramaByDramaIds", reflect.TypeOf((*MockIDramaV2)(nil).BatchDeleteDramaByDramaIds), arg0, arg1)
}

// BatchDisableCopiedDramaByOriginalDramaIds mocks base method.
func (m *MockIDramaV2) BatchDisableCopiedDramaByOriginalDramaIds(arg0 context.Context, arg1 ...uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchDisableCopiedDramaByOriginalDramaIds", varargs...)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDisableCopiedDramaByOriginalDramaIds indicates an expected call of BatchDisableCopiedDramaByOriginalDramaIds.
func (mr *MockIDramaV2MockRecorder) BatchDisableCopiedDramaByOriginalDramaIds(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDisableCopiedDramaByOriginalDramaIds", reflect.TypeOf((*MockIDramaV2)(nil).BatchDisableCopiedDramaByOriginalDramaIds), varargs...)
}

// BatchGetDramaByCondition mocks base method.
func (m *MockIDramaV2) BatchGetDramaByCondition(arg0 context.Context, arg1 []*DramaSearchCondition, arg2 ...Mask) ([]*DramaEntityV2, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetDramaByCondition", varargs...)
	ret0, _ := ret[0].([]*DramaEntityV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetDramaByCondition indicates an expected call of BatchGetDramaByCondition.
func (mr *MockIDramaV2MockRecorder) BatchGetDramaByCondition(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetDramaByCondition", reflect.TypeOf((*MockIDramaV2)(nil).BatchGetDramaByCondition), varargs...)
}

// BatchGetDramas mocks base method.
func (m *MockIDramaV2) BatchGetDramas(arg0 context.Context, arg1 primitive.M, arg2 ...Mask) ([]*DramaEntityV2, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetDramas", varargs...)
	ret0, _ := ret[0].([]*DramaEntityV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetDramas indicates an expected call of BatchGetDramas.
func (mr *MockIDramaV2MockRecorder) BatchGetDramas(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetDramas", reflect.TypeOf((*MockIDramaV2)(nil).BatchGetDramas), varargs...)
}

// BatchGetDramasByIDs mocks base method.
func (m *MockIDramaV2) BatchGetDramasByIDs(arg0 context.Context, arg1 []uint32, arg2 ...Mask) ([]*DramaEntityV2, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetDramasByIDs", varargs...)
	ret0, _ := ret[0].([]*DramaEntityV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetDramasByIDs indicates an expected call of BatchGetDramasByIDs.
func (mr *MockIDramaV2MockRecorder) BatchGetDramasByIDs(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetDramasByIDs", reflect.TypeOf((*MockIDramaV2)(nil).BatchGetDramasByIDs), varargs...)
}

// BatchGetDramasByOriginIds mocks base method.
func (m *MockIDramaV2) BatchGetDramasByOriginIds(arg0 context.Context, arg1 []uint32, arg2 ...Mask) (DramaList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetDramasByOriginIds", varargs...)
	ret0, _ := ret[0].(DramaList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetDramasByOriginIds indicates an expected call of BatchGetDramasByOriginIds.
func (mr *MockIDramaV2MockRecorder) BatchGetDramasByOriginIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetDramasByOriginIds", reflect.TypeOf((*MockIDramaV2)(nil).BatchGetDramasByOriginIds), varargs...)
}

// BatchUpdateCopiedDramaInfo mocks base method.
func (m *MockIDramaV2) BatchUpdateCopiedDramaInfo(arg0 context.Context, arg1 *DramaEntityV2, arg2 ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchUpdateCopiedDramaInfo", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateCopiedDramaInfo indicates an expected call of BatchUpdateCopiedDramaInfo.
func (mr *MockIDramaV2MockRecorder) BatchUpdateCopiedDramaInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateCopiedDramaInfo", reflect.TypeOf((*MockIDramaV2)(nil).BatchUpdateCopiedDramaInfo), varargs...)
}

// GetAllCreatorId mocks base method.
func (m *MockIDramaV2) GetAllCreatorId(arg0 context.Context) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllCreatorId", arg0)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllCreatorId indicates an expected call of GetAllCreatorId.
func (mr *MockIDramaV2MockRecorder) GetAllCreatorId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllCreatorId", reflect.TypeOf((*MockIDramaV2)(nil).GetAllCreatorId), arg0)
}

// GetAllSearchInfo mocks base method.
func (m *MockIDramaV2) GetAllSearchInfo(arg0 context.Context) ([]*SearchInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllSearchInfo", arg0)
	ret0, _ := ret[0].([]*SearchInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllSearchInfo indicates an expected call of GetAllSearchInfo.
func (mr *MockIDramaV2MockRecorder) GetAllSearchInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllSearchInfo", reflect.TypeOf((*MockIDramaV2)(nil).GetAllSearchInfo), arg0)
}

// GetAuthorDramaListByPage mocks base method.
func (m *MockIDramaV2) GetAuthorDramaListByPage(arg0 context.Context, arg1 primitive.M, arg2 pia0.PiaAuthorWorksListSortType, arg3 string, arg4 uint32) ([]*DramaEntityV2, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthorDramaListByPage", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*DramaEntityV2)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAuthorDramaListByPage indicates an expected call of GetAuthorDramaListByPage.
func (mr *MockIDramaV2MockRecorder) GetAuthorDramaListByPage(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthorDramaListByPage", reflect.TypeOf((*MockIDramaV2)(nil).GetAuthorDramaListByPage), arg0, arg1, arg2, arg3, arg4)
}

// GetCopiedDramaListByPage mocks base method.
func (m *MockIDramaV2) GetCopiedDramaListByPage(arg0 context.Context, arg1 primitive.M, arg2 string, arg3 uint32) ([]*DramaEntityV2, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCopiedDramaListByPage", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*DramaEntityV2)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetCopiedDramaListByPage indicates an expected call of GetCopiedDramaListByPage.
func (mr *MockIDramaV2MockRecorder) GetCopiedDramaListByPage(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCopiedDramaListByPage", reflect.TypeOf((*MockIDramaV2)(nil).GetCopiedDramaListByPage), arg0, arg1, arg2, arg3)
}

// GetDramaByCondition mocks base method.
func (m *MockIDramaV2) GetDramaByCondition(arg0 context.Context, arg1 []*DramaSearchCondition, arg2 ...Mask) (*DramaEntityV2, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDramaByCondition", varargs...)
	ret0, _ := ret[0].(*DramaEntityV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaByCondition indicates an expected call of GetDramaByCondition.
func (mr *MockIDramaV2MockRecorder) GetDramaByCondition(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaByCondition", reflect.TypeOf((*MockIDramaV2)(nil).GetDramaByCondition), varargs...)
}

// GetDramaByID mocks base method.
func (m *MockIDramaV2) GetDramaByID(arg0 context.Context, arg1 uint32, arg2 ...Mask) (*DramaEntityV2, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDramaByID", varargs...)
	ret0, _ := ret[0].(*DramaEntityV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaByID indicates an expected call of GetDramaByID.
func (mr *MockIDramaV2MockRecorder) GetDramaByID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaByID", reflect.TypeOf((*MockIDramaV2)(nil).GetDramaByID), varargs...)
}

// GetDramaIdByCreatorIdWithoutCopy mocks base method.
func (m *MockIDramaV2) GetDramaIdByCreatorIdWithoutCopy(arg0 context.Context, arg1 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaIdByCreatorIdWithoutCopy", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaIdByCreatorIdWithoutCopy indicates an expected call of GetDramaIdByCreatorIdWithoutCopy.
func (mr *MockIDramaV2MockRecorder) GetDramaIdByCreatorIdWithoutCopy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaIdByCreatorIdWithoutCopy", reflect.TypeOf((*MockIDramaV2)(nil).GetDramaIdByCreatorIdWithoutCopy), arg0, arg1)
}

// GetDramaList mocks base method.
func (m *MockIDramaV2) GetDramaList(arg0 context.Context, arg1 primitive.M, arg2, arg3 uint32) ([]*DramaEntityV2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*DramaEntityV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaList indicates an expected call of GetDramaList.
func (mr *MockIDramaV2MockRecorder) GetDramaList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaList", reflect.TypeOf((*MockIDramaV2)(nil).GetDramaList), arg0, arg1, arg2, arg3)
}

// GetDramaListByPage mocks base method.
func (m *MockIDramaV2) GetDramaListByPage(arg0 context.Context, arg1 primitive.M, arg2 string, arg3 uint32) ([]*DramaEntityV2, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaListByPage", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*DramaEntityV2)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetDramaListByPage indicates an expected call of GetDramaListByPage.
func (mr *MockIDramaV2MockRecorder) GetDramaListByPage(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaListByPage", reflect.TypeOf((*MockIDramaV2)(nil).GetDramaListByPage), arg0, arg1, arg2, arg3)
}

// GetLocalLatestUpdateTime mocks base method.
func (m *MockIDramaV2) GetLocalLatestUpdateTime(arg0 context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLocalLatestUpdateTime", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLocalLatestUpdateTime indicates an expected call of GetLocalLatestUpdateTime.
func (mr *MockIDramaV2MockRecorder) GetLocalLatestUpdateTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLocalLatestUpdateTime", reflect.TypeOf((*MockIDramaV2)(nil).GetLocalLatestUpdateTime), arg0)
}

// GetMyCopiedDramaListByPage mocks base method.
func (m *MockIDramaV2) GetMyCopiedDramaListByPage(arg0 context.Context, arg1 uint32, arg2 primitive.M, arg3 string, arg4 uint32) ([]*DramaEntityV2, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyCopiedDramaListByPage", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*DramaEntityV2)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetMyCopiedDramaListByPage indicates an expected call of GetMyCopiedDramaListByPage.
func (mr *MockIDramaV2MockRecorder) GetMyCopiedDramaListByPage(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyCopiedDramaListByPage", reflect.TypeOf((*MockIDramaV2)(nil).GetMyCopiedDramaListByPage), arg0, arg1, arg2, arg3, arg4)
}

// PageGetDramaByCondition mocks base method.
func (m *MockIDramaV2) PageGetDramaByCondition(arg0 context.Context, arg1 []*DramaSearchCondition, arg2 string, arg3 uint32, arg4 ...Mask) ([]*DramaEntityV2, string, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2, arg3}
	for _, a := range arg4 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PageGetDramaByCondition", varargs...)
	ret0, _ := ret[0].([]*DramaEntityV2)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// PageGetDramaByCondition indicates an expected call of PageGetDramaByCondition.
func (mr *MockIDramaV2MockRecorder) PageGetDramaByCondition(arg0, arg1, arg2, arg3 interface{}, arg4 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2, arg3}, arg4...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PageGetDramaByCondition", reflect.TypeOf((*MockIDramaV2)(nil).PageGetDramaByCondition), varargs...)
}

// PiaGetDramaCopyId mocks base method.
func (m *MockIDramaV2) PiaGetDramaCopyId(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaGetDramaCopyId", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetDramaCopyId indicates an expected call of PiaGetDramaCopyId.
func (mr *MockIDramaV2MockRecorder) PiaGetDramaCopyId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetDramaCopyId", reflect.TypeOf((*MockIDramaV2)(nil).PiaGetDramaCopyId), arg0, arg1, arg2)
}

// Save mocks base method.
func (m *MockIDramaV2) Save(arg0 context.Context, arg1 *DramaEntityV2) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Save indicates an expected call of Save.
func (mr *MockIDramaV2MockRecorder) Save(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockIDramaV2)(nil).Save), arg0, arg1)
}

// UpdateDrama mocks base method.
func (m *MockIDramaV2) UpdateDrama(arg0 context.Context, arg1, arg2 interface{}, arg3 ...*options.UpdateOptions) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateDrama", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDrama indicates an expected call of UpdateDrama.
func (mr *MockIDramaV2MockRecorder) UpdateDrama(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDrama", reflect.TypeOf((*MockIDramaV2)(nil).UpdateDrama), varargs...)
}

// UpsertDramaV2 mocks base method.
func (m *MockIDramaV2) UpsertDramaV2(arg0 context.Context, arg1 *DramaEntityV2) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertDramaV2", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertDramaV2 indicates an expected call of UpsertDramaV2.
func (mr *MockIDramaV2MockRecorder) UpsertDramaV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertDramaV2", reflect.TypeOf((*MockIDramaV2)(nil).UpsertDramaV2), arg0, arg1)
}

// MockChannelRunningDramaInfoRepo is a mock of ChannelRunningDramaInfoRepo interface.
type MockChannelRunningDramaInfoRepo struct {
	ctrl     *gomock.Controller
	recorder *MockChannelRunningDramaInfoRepoMockRecorder
}

// MockChannelRunningDramaInfoRepoMockRecorder is the mock recorder for MockChannelRunningDramaInfoRepo.
type MockChannelRunningDramaInfoRepoMockRecorder struct {
	mock *MockChannelRunningDramaInfoRepo
}

// NewMockChannelRunningDramaInfoRepo creates a new mock instance.
func NewMockChannelRunningDramaInfoRepo(ctrl *gomock.Controller) *MockChannelRunningDramaInfoRepo {
	mock := &MockChannelRunningDramaInfoRepo{ctrl: ctrl}
	mock.recorder = &MockChannelRunningDramaInfoRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelRunningDramaInfoRepo) EXPECT() *MockChannelRunningDramaInfoRepoMockRecorder {
	return m.recorder
}

// Delete mocks base method.
func (m *MockChannelRunningDramaInfoRepo) Delete(arg0 context.Context, arg1 ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Delete", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockChannelRunningDramaInfoRepoMockRecorder) Delete(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockChannelRunningDramaInfoRepo)(nil).Delete), varargs...)
}

// FetchAllPlayingChannel mocks base method.
func (m *MockChannelRunningDramaInfoRepo) FetchAllPlayingChannel(arg0 context.Context) ([]*ChannelRunningDramaInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchAllPlayingChannel", arg0)
	ret0, _ := ret[0].([]*ChannelRunningDramaInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchAllPlayingChannel indicates an expected call of FetchAllPlayingChannel.
func (mr *MockChannelRunningDramaInfoRepoMockRecorder) FetchAllPlayingChannel(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchAllPlayingChannel", reflect.TypeOf((*MockChannelRunningDramaInfoRepo)(nil).FetchAllPlayingChannel), arg0)
}

// FetchByChannelID mocks base method.
func (m *MockChannelRunningDramaInfoRepo) FetchByChannelID(arg0 context.Context, arg1 uint32) (*ChannelRunningDramaInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchByChannelID", arg0, arg1)
	ret0, _ := ret[0].(*ChannelRunningDramaInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchByChannelID indicates an expected call of FetchByChannelID.
func (mr *MockChannelRunningDramaInfoRepoMockRecorder) FetchByChannelID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchByChannelID", reflect.TypeOf((*MockChannelRunningDramaInfoRepo)(nil).FetchByChannelID), arg0, arg1)
}

// FetchByChannelIDAndCreateIfNotPresent mocks base method.
func (m *MockChannelRunningDramaInfoRepo) FetchByChannelIDAndCreateIfNotPresent(arg0 context.Context, arg1 uint32) (*ChannelRunningDramaInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchByChannelIDAndCreateIfNotPresent", arg0, arg1)
	ret0, _ := ret[0].(*ChannelRunningDramaInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchByChannelIDAndCreateIfNotPresent indicates an expected call of FetchByChannelIDAndCreateIfNotPresent.
func (mr *MockChannelRunningDramaInfoRepoMockRecorder) FetchByChannelIDAndCreateIfNotPresent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchByChannelIDAndCreateIfNotPresent", reflect.TypeOf((*MockChannelRunningDramaInfoRepo)(nil).FetchByChannelIDAndCreateIfNotPresent), arg0, arg1)
}

// Save mocks base method.
func (m *MockChannelRunningDramaInfoRepo) Save(arg0 context.Context, arg1 *ChannelRunningDramaInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockChannelRunningDramaInfoRepoMockRecorder) Save(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockChannelRunningDramaInfoRepo)(nil).Save), arg0, arg1)
}

// MockConvertor is a mock of Convertor interface.
type MockConvertor struct {
	ctrl     *gomock.Controller
	recorder *MockConvertorMockRecorder
}

// MockConvertorMockRecorder is the mock recorder for MockConvertor.
type MockConvertorMockRecorder struct {
	mock *MockConvertor
}

// NewMockConvertor creates a new mock instance.
func NewMockConvertor(ctrl *gomock.Controller) *MockConvertor {
	mock := &MockConvertor{ctrl: ctrl}
	mock.recorder = &MockConvertorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConvertor) EXPECT() *MockConvertorMockRecorder {
	return m.recorder
}

// BgmStatusToLogic mocks base method.
func (m *MockConvertor) BgmStatusToLogic(arg0 *BgmProgress) *pia.DramaBgmStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BgmStatusToLogic", arg0)
	ret0, _ := ret[0].(*pia.DramaBgmStatus)
	return ret0
}

// BgmStatusToLogic indicates an expected call of BgmStatusToLogic.
func (mr *MockConvertorMockRecorder) BgmStatusToLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BgmStatusToLogic", reflect.TypeOf((*MockConvertor)(nil).BgmStatusToLogic), arg0)
}

// BgmStatusToPB mocks base method.
func (m *MockConvertor) BgmStatusToPB(arg0 *BgmProgress) *pia0.DramaBgmStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BgmStatusToPB", arg0)
	ret0, _ := ret[0].(*pia0.DramaBgmStatus)
	return ret0
}

// BgmStatusToPB indicates an expected call of BgmStatusToPB.
func (mr *MockConvertorMockRecorder) BgmStatusToPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BgmStatusToPB", reflect.TypeOf((*MockConvertor)(nil).BgmStatusToPB), arg0)
}

// BgmVolStatusToLogic mocks base method.
func (m *MockConvertor) BgmVolStatusToLogic(arg0 *BgmVolSetting) *pia.DramaBgmVolStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BgmVolStatusToLogic", arg0)
	ret0, _ := ret[0].(*pia.DramaBgmVolStatus)
	return ret0
}

// BgmVolStatusToLogic indicates an expected call of BgmVolStatusToLogic.
func (mr *MockConvertorMockRecorder) BgmVolStatusToLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BgmVolStatusToLogic", reflect.TypeOf((*MockConvertor)(nil).BgmVolStatusToLogic), arg0)
}

// BgmVolStatusToPB mocks base method.
func (m *MockConvertor) BgmVolStatusToPB(arg0 *BgmVolSetting) *pia0.DramaBgmVolStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BgmVolStatusToPB", arg0)
	ret0, _ := ret[0].(*pia0.DramaBgmVolStatus)
	return ret0
}

// BgmVolStatusToPB indicates an expected call of BgmVolStatusToPB.
func (mr *MockConvertorMockRecorder) BgmVolStatusToPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BgmVolStatusToPB", reflect.TypeOf((*MockConvertor)(nil).BgmVolStatusToPB), arg0)
}

// ChannelDramaStatusToLogic mocks base method.
func (m *MockConvertor) ChannelDramaStatusToLogic(arg0 *ChannelRunningDramaInfo) *pia.ChannelDramaStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelDramaStatusToLogic", arg0)
	ret0, _ := ret[0].(*pia.ChannelDramaStatus)
	return ret0
}

// ChannelDramaStatusToLogic indicates an expected call of ChannelDramaStatusToLogic.
func (mr *MockConvertorMockRecorder) ChannelDramaStatusToLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelDramaStatusToLogic", reflect.TypeOf((*MockConvertor)(nil).ChannelDramaStatusToLogic), arg0)
}

// ChannelMicRoleBindingInfoToLogic mocks base method.
func (m *MockConvertor) ChannelMicRoleBindingInfoToLogic(arg0 *ChannelMicRoleBindingInfo) *pia.MicRoleMap {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelMicRoleBindingInfoToLogic", arg0)
	ret0, _ := ret[0].(*pia.MicRoleMap)
	return ret0
}

// ChannelMicRoleBindingInfoToLogic indicates an expected call of ChannelMicRoleBindingInfoToLogic.
func (mr *MockConvertorMockRecorder) ChannelMicRoleBindingInfoToLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelMicRoleBindingInfoToLogic", reflect.TypeOf((*MockConvertor)(nil).ChannelMicRoleBindingInfoToLogic), arg0)
}

// ChannelMicRoleBindingInfoToPB mocks base method.
func (m *MockConvertor) ChannelMicRoleBindingInfoToPB(arg0 *ChannelMicRoleBindingInfo) *pia0.MicRoleMap {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelMicRoleBindingInfoToPB", arg0)
	ret0, _ := ret[0].(*pia0.MicRoleMap)
	return ret0
}

// ChannelMicRoleBindingInfoToPB indicates an expected call of ChannelMicRoleBindingInfoToPB.
func (mr *MockConvertorMockRecorder) ChannelMicRoleBindingInfoToPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelMicRoleBindingInfoToPB", reflect.TypeOf((*MockConvertor)(nil).ChannelMicRoleBindingInfoToPB), arg0)
}

// ChannelPlayingDramaInfoToChangePlayTypeLogic mocks base method.
func (m *MockConvertor) ChannelPlayingDramaInfoToChangePlayTypeLogic(arg0 *ChannelPlayingDramaInfo) *pia.PiaChangePlayType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelPlayingDramaInfoToChangePlayTypeLogic", arg0)
	ret0, _ := ret[0].(*pia.PiaChangePlayType)
	return ret0
}

// ChannelPlayingDramaInfoToChangePlayTypeLogic indicates an expected call of ChannelPlayingDramaInfoToChangePlayTypeLogic.
func (mr *MockConvertorMockRecorder) ChannelPlayingDramaInfoToChangePlayTypeLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelPlayingDramaInfoToChangePlayTypeLogic", reflect.TypeOf((*MockConvertor)(nil).ChannelPlayingDramaInfoToChangePlayTypeLogic), arg0)
}

// ChannelPlayingStatusInfoToLogic mocks base method.
func (m *MockConvertor) ChannelPlayingStatusInfoToLogic(arg0 *ChannelPlayingStatusInfo) *pia.ChannelDramaStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelPlayingStatusInfoToLogic", arg0)
	ret0, _ := ret[0].(*pia.ChannelDramaStatus)
	return ret0
}

// ChannelPlayingStatusInfoToLogic indicates an expected call of ChannelPlayingStatusInfoToLogic.
func (mr *MockConvertorMockRecorder) ChannelPlayingStatusInfoToLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelPlayingStatusInfoToLogic", reflect.TypeOf((*MockConvertor)(nil).ChannelPlayingStatusInfoToLogic), arg0)
}

// ChannelPlayingStatusInfoToPB mocks base method.
func (m *MockConvertor) ChannelPlayingStatusInfoToPB(arg0 *ChannelPlayingStatusInfo) *pia0.ChannelDramaStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelPlayingStatusInfoToPB", arg0)
	ret0, _ := ret[0].(*pia0.ChannelDramaStatus)
	return ret0
}

// ChannelPlayingStatusInfoToPB indicates an expected call of ChannelPlayingStatusInfoToPB.
func (mr *MockConvertorMockRecorder) ChannelPlayingStatusInfoToPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelPlayingStatusInfoToPB", reflect.TypeOf((*MockConvertor)(nil).ChannelPlayingStatusInfoToPB), arg0)
}

// ChannelPlayingTypeInfoFromPB mocks base method.
func (m *MockConvertor) ChannelPlayingTypeInfoFromPB(arg0 *pia0.PiaChannelDramaPlayingType) *ChannelPlayingTypeInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelPlayingTypeInfoFromPB", arg0)
	ret0, _ := ret[0].(*ChannelPlayingTypeInfo)
	return ret0
}

// ChannelPlayingTypeInfoFromPB indicates an expected call of ChannelPlayingTypeInfoFromPB.
func (mr *MockConvertorMockRecorder) ChannelPlayingTypeInfoFromPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelPlayingTypeInfoFromPB", reflect.TypeOf((*MockConvertor)(nil).ChannelPlayingTypeInfoFromPB), arg0)
}

// ChannelPlayingTypeInfoToLogic mocks base method.
func (m *MockConvertor) ChannelPlayingTypeInfoToLogic(arg0 *ChannelPlayingTypeInfo) *pia.PiaChannelDramaPlayingType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelPlayingTypeInfoToLogic", arg0)
	ret0, _ := ret[0].(*pia.PiaChannelDramaPlayingType)
	return ret0
}

// ChannelPlayingTypeInfoToLogic indicates an expected call of ChannelPlayingTypeInfoToLogic.
func (mr *MockConvertorMockRecorder) ChannelPlayingTypeInfoToLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelPlayingTypeInfoToLogic", reflect.TypeOf((*MockConvertor)(nil).ChannelPlayingTypeInfoToLogic), arg0)
}

// ChannelPlayingTypeInfoToPB mocks base method.
func (m *MockConvertor) ChannelPlayingTypeInfoToPB(arg0 *ChannelPlayingTypeInfo) *pia0.PiaChannelDramaPlayingType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelPlayingTypeInfoToPB", arg0)
	ret0, _ := ret[0].(*pia0.PiaChannelDramaPlayingType)
	return ret0
}

// ChannelPlayingTypeInfoToPB indicates an expected call of ChannelPlayingTypeInfoToPB.
func (mr *MockConvertorMockRecorder) ChannelPlayingTypeInfoToPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelPlayingTypeInfoToPB", reflect.TypeOf((*MockConvertor)(nil).ChannelPlayingTypeInfoToPB), arg0)
}

// ChannelPlayingTypeRoleInfoFromPB mocks base method.
func (m *MockConvertor) ChannelPlayingTypeRoleInfoFromPB(arg0 pia0.PiaChannelDramaPlayingType_PlayingTypeRole) DramaPlayingType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelPlayingTypeRoleInfoFromPB", arg0)
	ret0, _ := ret[0].(DramaPlayingType)
	return ret0
}

// ChannelPlayingTypeRoleInfoFromPB indicates an expected call of ChannelPlayingTypeRoleInfoFromPB.
func (mr *MockConvertorMockRecorder) ChannelPlayingTypeRoleInfoFromPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelPlayingTypeRoleInfoFromPB", reflect.TypeOf((*MockConvertor)(nil).ChannelPlayingTypeRoleInfoFromPB), arg0)
}

// ChannelPlayingTypeRoleInfoToLogic mocks base method.
func (m *MockConvertor) ChannelPlayingTypeRoleInfoToLogic(arg0 DramaPlayingType) pia.PiaChannelDramaPlayingType_PlayingTypeRole {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelPlayingTypeRoleInfoToLogic", arg0)
	ret0, _ := ret[0].(pia.PiaChannelDramaPlayingType_PlayingTypeRole)
	return ret0
}

// ChannelPlayingTypeRoleInfoToLogic indicates an expected call of ChannelPlayingTypeRoleInfoToLogic.
func (mr *MockConvertorMockRecorder) ChannelPlayingTypeRoleInfoToLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelPlayingTypeRoleInfoToLogic", reflect.TypeOf((*MockConvertor)(nil).ChannelPlayingTypeRoleInfoToLogic), arg0)
}

// ChannelPlayingTypeRoleInfoToPB mocks base method.
func (m *MockConvertor) ChannelPlayingTypeRoleInfoToPB(arg0 DramaPlayingType) pia0.PiaChannelDramaPlayingType_PlayingTypeRole {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelPlayingTypeRoleInfoToPB", arg0)
	ret0, _ := ret[0].(pia0.PiaChannelDramaPlayingType_PlayingTypeRole)
	return ret0
}

// ChannelPlayingTypeRoleInfoToPB indicates an expected call of ChannelPlayingTypeRoleInfoToPB.
func (mr *MockConvertorMockRecorder) ChannelPlayingTypeRoleInfoToPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelPlayingTypeRoleInfoToPB", reflect.TypeOf((*MockConvertor)(nil).ChannelPlayingTypeRoleInfoToPB), arg0)
}

// ChannelPlayingTypeTimeInfoFromPB mocks base method.
func (m *MockConvertor) ChannelPlayingTypeTimeInfoFromPB(arg0 pia0.PiaChannelDramaPlayingType_PlayingTypeTime) DramaPlayingType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelPlayingTypeTimeInfoFromPB", arg0)
	ret0, _ := ret[0].(DramaPlayingType)
	return ret0
}

// ChannelPlayingTypeTimeInfoFromPB indicates an expected call of ChannelPlayingTypeTimeInfoFromPB.
func (mr *MockConvertorMockRecorder) ChannelPlayingTypeTimeInfoFromPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelPlayingTypeTimeInfoFromPB", reflect.TypeOf((*MockConvertor)(nil).ChannelPlayingTypeTimeInfoFromPB), arg0)
}

// ChannelPlayingTypeTimeInfoToLogic mocks base method.
func (m *MockConvertor) ChannelPlayingTypeTimeInfoToLogic(arg0 DramaPlayingType) pia.PiaChannelDramaPlayingType_PlayingTypeTime {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelPlayingTypeTimeInfoToLogic", arg0)
	ret0, _ := ret[0].(pia.PiaChannelDramaPlayingType_PlayingTypeTime)
	return ret0
}

// ChannelPlayingTypeTimeInfoToLogic indicates an expected call of ChannelPlayingTypeTimeInfoToLogic.
func (mr *MockConvertorMockRecorder) ChannelPlayingTypeTimeInfoToLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelPlayingTypeTimeInfoToLogic", reflect.TypeOf((*MockConvertor)(nil).ChannelPlayingTypeTimeInfoToLogic), arg0)
}

// ChannelPlayingTypeTimeInfoToPB mocks base method.
func (m *MockConvertor) ChannelPlayingTypeTimeInfoToPB(arg0 DramaPlayingType) pia0.PiaChannelDramaPlayingType_PlayingTypeTime {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelPlayingTypeTimeInfoToPB", arg0)
	ret0, _ := ret[0].(pia0.PiaChannelDramaPlayingType_PlayingTypeTime)
	return ret0
}

// ChannelPlayingTypeTimeInfoToPB indicates an expected call of ChannelPlayingTypeTimeInfoToPB.
func (mr *MockConvertorMockRecorder) ChannelPlayingTypeTimeInfoToPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelPlayingTypeTimeInfoToPB", reflect.TypeOf((*MockConvertor)(nil).ChannelPlayingTypeTimeInfoToPB), arg0)
}

// ChannelRunningDramaInfoToPB mocks base method.
func (m *MockConvertor) ChannelRunningDramaInfoToPB(arg0 *ChannelPlayingDramaInfo) *pia0.ChannelDramaStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelRunningDramaInfoToPB", arg0)
	ret0, _ := ret[0].(*pia0.ChannelDramaStatus)
	return ret0
}

// ChannelRunningDramaInfoToPB indicates an expected call of ChannelRunningDramaInfoToPB.
func (mr *MockConvertorMockRecorder) ChannelRunningDramaInfoToPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelRunningDramaInfoToPB", reflect.TypeOf((*MockConvertor)(nil).ChannelRunningDramaInfoToPB), arg0)
}

// CopyDramaInfoWithStatusListToPB mocks base method.
func (m *MockConvertor) CopyDramaInfoWithStatusListToPB(arg0 []*DramaEntityV2) []*pia0.PiaCopyDramaInfoWithStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyDramaInfoWithStatusListToPB", arg0)
	ret0, _ := ret[0].([]*pia0.PiaCopyDramaInfoWithStatus)
	return ret0
}

// CopyDramaInfoWithStatusListToPB indicates an expected call of CopyDramaInfoWithStatusListToPB.
func (mr *MockConvertorMockRecorder) CopyDramaInfoWithStatusListToPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyDramaInfoWithStatusListToPB", reflect.TypeOf((*MockConvertor)(nil).CopyDramaInfoWithStatusListToPB), arg0)
}

// CopyDramaInfoWithStatusToPB mocks base method.
func (m *MockConvertor) CopyDramaInfoWithStatusToPB(arg0 *DramaEntityV2) *pia0.PiaCopyDramaInfoWithStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyDramaInfoWithStatusToPB", arg0)
	ret0, _ := ret[0].(*pia0.PiaCopyDramaInfoWithStatus)
	return ret0
}

// CopyDramaInfoWithStatusToPB indicates an expected call of CopyDramaInfoWithStatusToPB.
func (mr *MockConvertorMockRecorder) CopyDramaInfoWithStatusToPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyDramaInfoWithStatusToPB", reflect.TypeOf((*MockConvertor)(nil).CopyDramaInfoWithStatusToPB), arg0)
}

// DramaBgmListPBToLogic mocks base method.
func (m *MockConvertor) DramaBgmListPBToLogic(arg0 []*pia0.PiaBGM) []*pia.PiaBGM {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaBgmListPBToLogic", arg0)
	ret0, _ := ret[0].([]*pia.PiaBGM)
	return ret0
}

// DramaBgmListPBToLogic indicates an expected call of DramaBgmListPBToLogic.
func (mr *MockConvertorMockRecorder) DramaBgmListPBToLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaBgmListPBToLogic", reflect.TypeOf((*MockConvertor)(nil).DramaBgmListPBToLogic), arg0)
}

// DramaContentListPBToLogic mocks base method.
func (m *MockConvertor) DramaContentListPBToLogic(arg0 []*pia0.PiaContent) []*pia.PiaContent {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaContentListPBToLogic", arg0)
	ret0, _ := ret[0].([]*pia.PiaContent)
	return ret0
}

// DramaContentListPBToLogic indicates an expected call of DramaContentListPBToLogic.
func (mr *MockConvertorMockRecorder) DramaContentListPBToLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaContentListPBToLogic", reflect.TypeOf((*MockConvertor)(nil).DramaContentListPBToLogic), arg0)
}

// DramaInfoToLogicBgmList mocks base method.
func (m *MockConvertor) DramaInfoToLogicBgmList(arg0 *DramaEntityV2) []*pia.PiaBGM {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToLogicBgmList", arg0)
	ret0, _ := ret[0].([]*pia.PiaBGM)
	return ret0
}

// DramaInfoToLogicBgmList indicates an expected call of DramaInfoToLogicBgmList.
func (mr *MockConvertorMockRecorder) DramaInfoToLogicBgmList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToLogicBgmList", reflect.TypeOf((*MockConvertor)(nil).DramaInfoToLogicBgmList), arg0)
}

// DramaInfoToLogicContentList mocks base method.
func (m *MockConvertor) DramaInfoToLogicContentList(arg0 *DramaEntityV2) []*pia.PiaContent {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToLogicContentList", arg0)
	ret0, _ := ret[0].([]*pia.PiaContent)
	return ret0
}

// DramaInfoToLogicContentList indicates an expected call of DramaInfoToLogicContentList.
func (mr *MockConvertorMockRecorder) DramaInfoToLogicContentList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToLogicContentList", reflect.TypeOf((*MockConvertor)(nil).DramaInfoToLogicContentList), arg0)
}

// DramaInfoToLogicDrama mocks base method.
func (m *MockConvertor) DramaInfoToLogicDrama(arg0 *DramaEntityV2) *pia.DramaV2 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToLogicDrama", arg0)
	ret0, _ := ret[0].(*pia.DramaV2)
	return ret0
}

// DramaInfoToLogicDrama indicates an expected call of DramaInfoToLogicDrama.
func (mr *MockConvertorMockRecorder) DramaInfoToLogicDrama(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToLogicDrama", reflect.TypeOf((*MockConvertor)(nil).DramaInfoToLogicDrama), arg0)
}

// DramaInfoToLogicDramaWithMaskField mocks base method.
func (m *MockConvertor) DramaInfoToLogicDramaWithMaskField(arg0 *DramaEntityV2, arg1 *ChannelPlayingStatusInfo) *pia.DramaV2 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToLogicDramaWithMaskField", arg0, arg1)
	ret0, _ := ret[0].(*pia.DramaV2)
	return ret0
}

// DramaInfoToLogicDramaWithMaskField indicates an expected call of DramaInfoToLogicDramaWithMaskField.
func (mr *MockConvertorMockRecorder) DramaInfoToLogicDramaWithMaskField(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToLogicDramaWithMaskField", reflect.TypeOf((*MockConvertor)(nil).DramaInfoToLogicDramaWithMaskField), arg0, arg1)
}

// DramaInfoToLogicPictureList mocks base method.
func (m *MockConvertor) DramaInfoToLogicPictureList(arg0 *DramaEntityV2) []*pia.PiaPicture {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToLogicPictureList", arg0)
	ret0, _ := ret[0].([]*pia.PiaPicture)
	return ret0
}

// DramaInfoToLogicPictureList indicates an expected call of DramaInfoToLogicPictureList.
func (mr *MockConvertorMockRecorder) DramaInfoToLogicPictureList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToLogicPictureList", reflect.TypeOf((*MockConvertor)(nil).DramaInfoToLogicPictureList), arg0)
}

// DramaInfoToLogicRoleList mocks base method.
func (m *MockConvertor) DramaInfoToLogicRoleList(arg0 *DramaEntityV2) []*pia.PiaRole {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToLogicRoleList", arg0)
	ret0, _ := ret[0].([]*pia.PiaRole)
	return ret0
}

// DramaInfoToLogicRoleList indicates an expected call of DramaInfoToLogicRoleList.
func (mr *MockConvertorMockRecorder) DramaInfoToLogicRoleList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToLogicRoleList", reflect.TypeOf((*MockConvertor)(nil).DramaInfoToLogicRoleList), arg0)
}

// DramaInfoToLogicSubInfo mocks base method.
func (m *MockConvertor) DramaInfoToLogicSubInfo(arg0 *DramaEntityV2) *pia.DramaSubInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToLogicSubInfo", arg0)
	ret0, _ := ret[0].(*pia.DramaSubInfo)
	return ret0
}

// DramaInfoToLogicSubInfo indicates an expected call of DramaInfoToLogicSubInfo.
func (mr *MockConvertorMockRecorder) DramaInfoToLogicSubInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToLogicSubInfo", reflect.TypeOf((*MockConvertor)(nil).DramaInfoToLogicSubInfo), arg0)
}

// DramaInfoToPBBgmList mocks base method.
func (m *MockConvertor) DramaInfoToPBBgmList(arg0 *DramaEntityV2) []*pia0.PiaBGM {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToPBBgmList", arg0)
	ret0, _ := ret[0].([]*pia0.PiaBGM)
	return ret0
}

// DramaInfoToPBBgmList indicates an expected call of DramaInfoToPBBgmList.
func (mr *MockConvertorMockRecorder) DramaInfoToPBBgmList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToPBBgmList", reflect.TypeOf((*MockConvertor)(nil).DramaInfoToPBBgmList), arg0)
}

// DramaInfoToPBContentList mocks base method.
func (m *MockConvertor) DramaInfoToPBContentList(arg0 *DramaEntityV2) []*pia0.PiaContent {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToPBContentList", arg0)
	ret0, _ := ret[0].([]*pia0.PiaContent)
	return ret0
}

// DramaInfoToPBContentList indicates an expected call of DramaInfoToPBContentList.
func (mr *MockConvertorMockRecorder) DramaInfoToPBContentList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToPBContentList", reflect.TypeOf((*MockConvertor)(nil).DramaInfoToPBContentList), arg0)
}

// DramaInfoToPBDrama mocks base method.
func (m *MockConvertor) DramaInfoToPBDrama(arg0 *DramaEntityV2) *pia0.DramaV2 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToPBDrama", arg0)
	ret0, _ := ret[0].(*pia0.DramaV2)
	return ret0
}

// DramaInfoToPBDrama indicates an expected call of DramaInfoToPBDrama.
func (mr *MockConvertorMockRecorder) DramaInfoToPBDrama(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToPBDrama", reflect.TypeOf((*MockConvertor)(nil).DramaInfoToPBDrama), arg0)
}

// DramaInfoToPBDramaWithMaskField mocks base method.
func (m *MockConvertor) DramaInfoToPBDramaWithMaskField(arg0 *DramaEntityV2, arg1 *ChannelPlayingStatusInfo) *pia0.DramaV2 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToPBDramaWithMaskField", arg0, arg1)
	ret0, _ := ret[0].(*pia0.DramaV2)
	return ret0
}

// DramaInfoToPBDramaWithMaskField indicates an expected call of DramaInfoToPBDramaWithMaskField.
func (mr *MockConvertorMockRecorder) DramaInfoToPBDramaWithMaskField(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToPBDramaWithMaskField", reflect.TypeOf((*MockConvertor)(nil).DramaInfoToPBDramaWithMaskField), arg0, arg1)
}

// DramaInfoToPBPictureList mocks base method.
func (m *MockConvertor) DramaInfoToPBPictureList(arg0 *DramaEntityV2) []*pia0.PiaPicture {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToPBPictureList", arg0)
	ret0, _ := ret[0].([]*pia0.PiaPicture)
	return ret0
}

// DramaInfoToPBPictureList indicates an expected call of DramaInfoToPBPictureList.
func (mr *MockConvertorMockRecorder) DramaInfoToPBPictureList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToPBPictureList", reflect.TypeOf((*MockConvertor)(nil).DramaInfoToPBPictureList), arg0)
}

// DramaInfoToPBRoleList mocks base method.
func (m *MockConvertor) DramaInfoToPBRoleList(arg0 *DramaEntityV2) []*pia0.PiaRole {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToPBRoleList", arg0)
	ret0, _ := ret[0].([]*pia0.PiaRole)
	return ret0
}

// DramaInfoToPBRoleList indicates an expected call of DramaInfoToPBRoleList.
func (mr *MockConvertorMockRecorder) DramaInfoToPBRoleList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToPBRoleList", reflect.TypeOf((*MockConvertor)(nil).DramaInfoToPBRoleList), arg0)
}

// DramaInfoToPBSubInfo mocks base method.
func (m *MockConvertor) DramaInfoToPBSubInfo(arg0 *DramaEntityV2) *pia0.DramaSubInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToPBSubInfo", arg0)
	ret0, _ := ret[0].(*pia0.DramaSubInfo)
	return ret0
}

// DramaInfoToPBSubInfo indicates an expected call of DramaInfoToPBSubInfo.
func (mr *MockConvertorMockRecorder) DramaInfoToPBSubInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToPBSubInfo", reflect.TypeOf((*MockConvertor)(nil).DramaInfoToPBSubInfo), arg0)
}

// DramaPictureListPBToLogic mocks base method.
func (m *MockConvertor) DramaPictureListPBToLogic(arg0 []*pia0.PiaPicture) []*pia.PiaPicture {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaPictureListPBToLogic", arg0)
	ret0, _ := ret[0].([]*pia.PiaPicture)
	return ret0
}

// DramaPictureListPBToLogic indicates an expected call of DramaPictureListPBToLogic.
func (mr *MockConvertorMockRecorder) DramaPictureListPBToLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaPictureListPBToLogic", reflect.TypeOf((*MockConvertor)(nil).DramaPictureListPBToLogic), arg0)
}

// DramaRoleListPBToLogic mocks base method.
func (m *MockConvertor) DramaRoleListPBToLogic(arg0 []*pia0.PiaRole) []*pia.PiaRole {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaRoleListPBToLogic", arg0)
	ret0, _ := ret[0].([]*pia.PiaRole)
	return ret0
}

// DramaRoleListPBToLogic indicates an expected call of DramaRoleListPBToLogic.
func (mr *MockConvertorMockRecorder) DramaRoleListPBToLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaRoleListPBToLogic", reflect.TypeOf((*MockConvertor)(nil).DramaRoleListPBToLogic), arg0)
}

// DramaSubInfoPBToLogic mocks base method.
func (m *MockConvertor) DramaSubInfoPBToLogic(arg0 *pia0.DramaSubInfo) *pia.DramaSubInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaSubInfoPBToLogic", arg0)
	ret0, _ := ret[0].(*pia.DramaSubInfo)
	return ret0
}

// DramaSubInfoPBToLogic indicates an expected call of DramaSubInfoPBToLogic.
func (mr *MockConvertorMockRecorder) DramaSubInfoPBToLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaSubInfoPBToLogic", reflect.TypeOf((*MockConvertor)(nil).DramaSubInfoPBToLogic), arg0)
}

// DramaV2PBToLogic mocks base method.
func (m *MockConvertor) DramaV2PBToLogic(arg0 *pia0.DramaV2) *pia.DramaV2 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaV2PBToLogic", arg0)
	ret0, _ := ret[0].(*pia.DramaV2)
	return ret0
}

// DramaV2PBToLogic indicates an expected call of DramaV2PBToLogic.
func (mr *MockConvertorMockRecorder) DramaV2PBToLogic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaV2PBToLogic", reflect.TypeOf((*MockConvertor)(nil).DramaV2PBToLogic), arg0)
}

// MyPlayingRecordToPB mocks base method.
func (m *MockConvertor) MyPlayingRecordToPB(arg0 *MyPlayingRecord, arg1 *DramaEntityV2) *pia0.PiaDramaPlayingRecord {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MyPlayingRecordToPB", arg0, arg1)
	ret0, _ := ret[0].(*pia0.PiaDramaPlayingRecord)
	return ret0
}

// MyPlayingRecordToPB indicates an expected call of MyPlayingRecordToPB.
func (mr *MockConvertorMockRecorder) MyPlayingRecordToPB(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MyPlayingRecordToPB", reflect.TypeOf((*MockConvertor)(nil).MyPlayingRecordToPB), arg0, arg1)
}

// OrderDramaListBizToPB mocks base method.
func (m *MockConvertor) OrderDramaListBizToPB(arg0 *OrderDramaList) *pia0.DramaOrderList {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OrderDramaListBizToPB", arg0)
	ret0, _ := ret[0].(*pia0.DramaOrderList)
	return ret0
}

// OrderDramaListBizToPB indicates an expected call of OrderDramaListBizToPB.
func (mr *MockConvertorMockRecorder) OrderDramaListBizToPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OrderDramaListBizToPB", reflect.TypeOf((*MockConvertor)(nil).OrderDramaListBizToPB), arg0)
}

// StickDramaInfoToPB mocks base method.
func (m *MockConvertor) StickDramaInfoToPB(arg0 *DramaStickEntity) *pia0.SearchStickDramaResp_StickDramaInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StickDramaInfoToPB", arg0)
	ret0, _ := ret[0].(*pia0.SearchStickDramaResp_StickDramaInfo)
	return ret0
}

// StickDramaInfoToPB indicates an expected call of StickDramaInfoToPB.
func (mr *MockConvertorMockRecorder) StickDramaInfoToPB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StickDramaInfoToPB", reflect.TypeOf((*MockConvertor)(nil).StickDramaInfoToPB), arg0)
}

// MockTimeService is a mock of TimeService interface.
type MockTimeService struct {
	ctrl     *gomock.Controller
	recorder *MockTimeServiceMockRecorder
}

// MockTimeServiceMockRecorder is the mock recorder for MockTimeService.
type MockTimeServiceMockRecorder struct {
	mock *MockTimeService
}

// NewMockTimeService creates a new mock instance.
func NewMockTimeService(ctrl *gomock.Controller) *MockTimeService {
	mock := &MockTimeService{ctrl: ctrl}
	mock.recorder = &MockTimeServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTimeService) EXPECT() *MockTimeServiceMockRecorder {
	return m.recorder
}

// Correction mocks base method.
func (m *MockTimeService) Correction(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Correction", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Correction indicates an expected call of Correction.
func (mr *MockTimeServiceMockRecorder) Correction(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Correction", reflect.TypeOf((*MockTimeService)(nil).Correction), arg0)
}

// CurrentTime mocks base method.
func (m *MockTimeService) CurrentTime(arg0 context.Context) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CurrentTime", arg0)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CurrentTime indicates an expected call of CurrentTime.
func (mr *MockTimeServiceMockRecorder) CurrentTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CurrentTime", reflect.TypeOf((*MockTimeService)(nil).CurrentTime), arg0)
}

// MockDramaService is a mock of DramaService interface.
type MockDramaService struct {
	ctrl     *gomock.Controller
	recorder *MockDramaServiceMockRecorder
}

// MockDramaServiceMockRecorder is the mock recorder for MockDramaService.
type MockDramaServiceMockRecorder struct {
	mock *MockDramaService
}

// NewMockDramaService creates a new mock instance.
func NewMockDramaService(ctrl *gomock.Controller) *MockDramaService {
	mock := &MockDramaService{ctrl: ctrl}
	mock.recorder = &MockDramaServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDramaService) EXPECT() *MockDramaServiceMockRecorder {
	return m.recorder
}

// GetDramaDomainById mocks base method.
func (m *MockDramaService) GetDramaDomainById(arg0 context.Context, arg1 uint32) (*DramaDomainEntity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaDomainById", arg0, arg1)
	ret0, _ := ret[0].(*DramaDomainEntity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaDomainById indicates an expected call of GetDramaDomainById.
func (mr *MockDramaServiceMockRecorder) GetDramaDomainById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaDomainById", reflect.TypeOf((*MockDramaService)(nil).GetDramaDomainById), arg0, arg1)
}

// GetDramaDomainByOriginId mocks base method.
func (m *MockDramaService) GetDramaDomainByOriginId(arg0 context.Context, arg1 uint32) (*DramaDomainEntity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaDomainByOriginId", arg0, arg1)
	ret0, _ := ret[0].(*DramaDomainEntity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaDomainByOriginId indicates an expected call of GetDramaDomainByOriginId.
func (mr *MockDramaServiceMockRecorder) GetDramaDomainByOriginId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaDomainByOriginId", reflect.TypeOf((*MockDramaService)(nil).GetDramaDomainByOriginId), arg0, arg1)
}

// GetDramaListByPage mocks base method.
func (m *MockDramaService) GetDramaListByPage(arg0 context.Context, arg1 *DramaListPageToken, arg2 uint32) (DramaList, *DramaListPageToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaListByPage", arg0, arg1, arg2)
	ret0, _ := ret[0].(DramaList)
	ret1, _ := ret[1].(*DramaListPageToken)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetDramaListByPage indicates an expected call of GetDramaListByPage.
func (mr *MockDramaServiceMockRecorder) GetDramaListByPage(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaListByPage", reflect.TypeOf((*MockDramaService)(nil).GetDramaListByPage), arg0, arg1, arg2)
}

// Save mocks base method.
func (m *MockDramaService) Save(arg0 context.Context, arg1 *DramaDomainEntity) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Save indicates an expected call of Save.
func (mr *MockDramaServiceMockRecorder) Save(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockDramaService)(nil).Save), arg0, arg1)
}

// UpsertDrama mocks base method.
func (m *MockDramaService) UpsertDrama(arg0 context.Context, arg1 *DramaDomainEntity) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertDrama", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertDrama indicates an expected call of UpsertDrama.
func (mr *MockDramaServiceMockRecorder) UpsertDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertDrama", reflect.TypeOf((*MockDramaService)(nil).UpsertDrama), arg0, arg1)
}

// MockTimeLineService is a mock of TimeLineService interface.
type MockTimeLineService struct {
	ctrl     *gomock.Controller
	recorder *MockTimeLineServiceMockRecorder
}

// MockTimeLineServiceMockRecorder is the mock recorder for MockTimeLineService.
type MockTimeLineServiceMockRecorder struct {
	mock *MockTimeLineService
}

// NewMockTimeLineService creates a new mock instance.
func NewMockTimeLineService(ctrl *gomock.Controller) *MockTimeLineService {
	mock := &MockTimeLineService{ctrl: ctrl}
	mock.recorder = &MockTimeLineServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTimeLineService) EXPECT() *MockTimeLineServiceMockRecorder {
	return m.recorder
}

// CreateTimeLine mocks base method.
func (m *MockTimeLineService) CreateTimeLine(arg0 []*TimeLineElement) []*TimeLineElement {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTimeLine", arg0)
	ret0, _ := ret[0].([]*TimeLineElement)
	return ret0
}

// CreateTimeLine indicates an expected call of CreateTimeLine.
func (mr *MockTimeLineServiceMockRecorder) CreateTimeLine(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTimeLine", reflect.TypeOf((*MockTimeLineService)(nil).CreateTimeLine), arg0)
}

// MockChannelPlayingDramaInfoRepo is a mock of ChannelPlayingDramaInfoRepo interface.
type MockChannelPlayingDramaInfoRepo struct {
	ctrl     *gomock.Controller
	recorder *MockChannelPlayingDramaInfoRepoMockRecorder
}

// MockChannelPlayingDramaInfoRepoMockRecorder is the mock recorder for MockChannelPlayingDramaInfoRepo.
type MockChannelPlayingDramaInfoRepoMockRecorder struct {
	mock *MockChannelPlayingDramaInfoRepo
}

// NewMockChannelPlayingDramaInfoRepo creates a new mock instance.
func NewMockChannelPlayingDramaInfoRepo(ctrl *gomock.Controller) *MockChannelPlayingDramaInfoRepo {
	mock := &MockChannelPlayingDramaInfoRepo{ctrl: ctrl}
	mock.recorder = &MockChannelPlayingDramaInfoRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelPlayingDramaInfoRepo) EXPECT() *MockChannelPlayingDramaInfoRepoMockRecorder {
	return m.recorder
}

// BatchFetchByChannelID mocks base method.
func (m *MockChannelPlayingDramaInfoRepo) BatchFetchByChannelID(arg0 context.Context, arg1 []uint32, arg2 ...Mask) ([]*ChannelPlayingDramaInfo, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchFetchByChannelID", varargs...)
	ret0, _ := ret[0].([]*ChannelPlayingDramaInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchFetchByChannelID indicates an expected call of BatchFetchByChannelID.
func (mr *MockChannelPlayingDramaInfoRepoMockRecorder) BatchFetchByChannelID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchFetchByChannelID", reflect.TypeOf((*MockChannelPlayingDramaInfoRepo)(nil).BatchFetchByChannelID), varargs...)
}

// Delete mocks base method.
func (m *MockChannelPlayingDramaInfoRepo) Delete(arg0 context.Context, arg1 ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Delete", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockChannelPlayingDramaInfoRepoMockRecorder) Delete(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockChannelPlayingDramaInfoRepo)(nil).Delete), varargs...)
}

// FetchAllChannelID mocks base method.
func (m *MockChannelPlayingDramaInfoRepo) FetchAllChannelID(arg0 context.Context) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchAllChannelID", arg0)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchAllChannelID indicates an expected call of FetchAllChannelID.
func (mr *MockChannelPlayingDramaInfoRepoMockRecorder) FetchAllChannelID(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchAllChannelID", reflect.TypeOf((*MockChannelPlayingDramaInfoRepo)(nil).FetchAllChannelID), arg0)
}

// FetchByChannelID mocks base method.
func (m *MockChannelPlayingDramaInfoRepo) FetchByChannelID(arg0 context.Context, arg1 uint32, arg2 ...Mask) (*ChannelPlayingDramaInfo, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchByChannelID", varargs...)
	ret0, _ := ret[0].(*ChannelPlayingDramaInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchByChannelID indicates an expected call of FetchByChannelID.
func (mr *MockChannelPlayingDramaInfoRepoMockRecorder) FetchByChannelID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchByChannelID", reflect.TypeOf((*MockChannelPlayingDramaInfoRepo)(nil).FetchByChannelID), varargs...)
}

// Save mocks base method.
func (m *MockChannelPlayingDramaInfoRepo) Save(arg0 context.Context, arg1 *ChannelPlayingDramaInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockChannelPlayingDramaInfoRepoMockRecorder) Save(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockChannelPlayingDramaInfoRepo)(nil).Save), arg0, arg1)
}

// SaveBgmVol mocks base method.
func (m *MockChannelPlayingDramaInfoRepo) SaveBgmVol(arg0 context.Context, arg1 *ChannelPlayingDramaInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveBgmVol", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveBgmVol indicates an expected call of SaveBgmVol.
func (mr *MockChannelPlayingDramaInfoRepoMockRecorder) SaveBgmVol(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveBgmVol", reflect.TypeOf((*MockChannelPlayingDramaInfoRepo)(nil).SaveBgmVol), arg0, arg1)
}

// MockReportDramaRecordStore is a mock of ReportDramaRecordStore interface.
type MockReportDramaRecordStore struct {
	ctrl     *gomock.Controller
	recorder *MockReportDramaRecordStoreMockRecorder
}

// MockReportDramaRecordStoreMockRecorder is the mock recorder for MockReportDramaRecordStore.
type MockReportDramaRecordStoreMockRecorder struct {
	mock *MockReportDramaRecordStore
}

// NewMockReportDramaRecordStore creates a new mock instance.
func NewMockReportDramaRecordStore(ctrl *gomock.Controller) *MockReportDramaRecordStore {
	mock := &MockReportDramaRecordStore{ctrl: ctrl}
	mock.recorder = &MockReportDramaRecordStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReportDramaRecordStore) EXPECT() *MockReportDramaRecordStoreMockRecorder {
	return m.recorder
}

// FetchById mocks base method.
func (m *MockReportDramaRecordStore) FetchById(arg0 context.Context, arg1 string) (*ReportDramaRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchById", arg0, arg1)
	ret0, _ := ret[0].(*ReportDramaRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchById indicates an expected call of FetchById.
func (mr *MockReportDramaRecordStoreMockRecorder) FetchById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchById", reflect.TypeOf((*MockReportDramaRecordStore)(nil).FetchById), arg0, arg1)
}

// FetchList mocks base method.
func (m *MockReportDramaRecordStore) FetchList(arg0 context.Context, arg1 primitive.M) ([]*ReportDramaRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchList", arg0, arg1)
	ret0, _ := ret[0].([]*ReportDramaRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchList indicates an expected call of FetchList.
func (mr *MockReportDramaRecordStoreMockRecorder) FetchList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchList", reflect.TypeOf((*MockReportDramaRecordStore)(nil).FetchList), arg0, arg1)
}

// Save mocks base method.
func (m *MockReportDramaRecordStore) Save(arg0 context.Context, arg1 *ReportDramaRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockReportDramaRecordStoreMockRecorder) Save(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockReportDramaRecordStore)(nil).Save), arg0, arg1)
}

// Update mocks base method.
func (m *MockReportDramaRecordStore) Update(arg0 context.Context, arg1 *ReportDramaRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockReportDramaRecordStoreMockRecorder) Update(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockReportDramaRecordStore)(nil).Update), arg0, arg1)
}

// MockDramaWebApi is a mock of DramaWebApi interface.
type MockDramaWebApi struct {
	ctrl     *gomock.Controller
	recorder *MockDramaWebApiMockRecorder
}

// MockDramaWebApiMockRecorder is the mock recorder for MockDramaWebApi.
type MockDramaWebApiMockRecorder struct {
	mock *MockDramaWebApi
}

// NewMockDramaWebApi creates a new mock instance.
func NewMockDramaWebApi(ctrl *gomock.Controller) *MockDramaWebApi {
	mock := &MockDramaWebApi{ctrl: ctrl}
	mock.recorder = &MockDramaWebApiMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDramaWebApi) EXPECT() *MockDramaWebApiMockRecorder {
	return m.recorder
}

// DramaFeedback mocks base method.
func (m *MockDramaWebApi) DramaFeedback(arg0 context.Context, arg1 *DramaFeedbackReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaFeedback", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DramaFeedback indicates an expected call of DramaFeedback.
func (mr *MockDramaWebApiMockRecorder) DramaFeedback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaFeedback", reflect.TypeOf((*MockDramaWebApi)(nil).DramaFeedback), arg0, arg1)
}

// ReportDrama mocks base method.
func (m *MockDramaWebApi) ReportDrama(arg0 context.Context, arg1 *ReportDramaReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportDrama", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportDrama indicates an expected call of ReportDrama.
func (mr *MockDramaWebApiMockRecorder) ReportDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportDrama", reflect.TypeOf((*MockDramaWebApi)(nil).ReportDrama), arg0, arg1)
}

// MockUserRelatedCopiedDramaRepo is a mock of UserRelatedCopiedDramaRepo interface.
type MockUserRelatedCopiedDramaRepo struct {
	ctrl     *gomock.Controller
	recorder *MockUserRelatedCopiedDramaRepoMockRecorder
}

// MockUserRelatedCopiedDramaRepoMockRecorder is the mock recorder for MockUserRelatedCopiedDramaRepo.
type MockUserRelatedCopiedDramaRepoMockRecorder struct {
	mock *MockUserRelatedCopiedDramaRepo
}

// NewMockUserRelatedCopiedDramaRepo creates a new mock instance.
func NewMockUserRelatedCopiedDramaRepo(ctrl *gomock.Controller) *MockUserRelatedCopiedDramaRepo {
	mock := &MockUserRelatedCopiedDramaRepo{ctrl: ctrl}
	mock.recorder = &MockUserRelatedCopiedDramaRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserRelatedCopiedDramaRepo) EXPECT() *MockUserRelatedCopiedDramaRepoMockRecorder {
	return m.recorder
}

// GetMyCopyCountOfOneDrama mocks base method.
func (m *MockUserRelatedCopiedDramaRepo) GetMyCopyCountOfOneDrama(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyCopyCountOfOneDrama", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyCopyCountOfOneDrama indicates an expected call of GetMyCopyCountOfOneDrama.
func (mr *MockUserRelatedCopiedDramaRepoMockRecorder) GetMyCopyCountOfOneDrama(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyCopyCountOfOneDrama", reflect.TypeOf((*MockUserRelatedCopiedDramaRepo)(nil).GetMyCopyCountOfOneDrama), arg0, arg1, arg2)
}

// GetMyTotalCopyCount mocks base method.
func (m *MockUserRelatedCopiedDramaRepo) GetMyTotalCopyCount(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyTotalCopyCount", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyTotalCopyCount indicates an expected call of GetMyTotalCopyCount.
func (mr *MockUserRelatedCopiedDramaRepoMockRecorder) GetMyTotalCopyCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyTotalCopyCount", reflect.TypeOf((*MockUserRelatedCopiedDramaRepo)(nil).GetMyTotalCopyCount), arg0, arg1)
}
