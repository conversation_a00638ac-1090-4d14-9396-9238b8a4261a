package mgr

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"golang.52tt.com/clients/channel"
	channelMic "golang.52tt.com/clients/channelmic"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/pia"
	"golang.52tt.com/services/pia/conf"
)

var (
	ErrChannelPlayingMicInfoNotFound = errors.New("房间麦位参演记录不存在")
)

type MyPlayingRecordPageToken struct {
	LastPlayingTime int64 `json:"last_playing_time"`
}

func (m *MyPlayingRecordPageToken) Marshal() (string, error) {
	data, err := json.Marshal(m)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(data), nil
}

func (m *MyPlayingRecordPageToken) Unmarshal(token string) error {
	if len(token) == 0 {
		return nil
	}
	decodeString, err := base64.StdEncoding.DecodeString(token)
	if err != nil {
		return fmt.Errorf("base64解析token失败,err:%+v", err)
	}
	err = json.Unmarshal(decodeString, m)
	if err != nil {
		return fmt.Errorf("json解析token失败,err:%+v", err)
	}
	return nil
}

type MyPlayingRecord struct {
	ID       string `json:"id" bson:"_id"`
	PlayTime int64  `json:"play_time" bson:"play_time"` // 参演时间
	Uid      uint32 `json:"uid" bson:"uid"`             // 用户id
	DramaId  uint32 `json:"drama_id" bson:"drama_id"`   // 剧本id
}

type MyPlayingRecordList []*MyPlayingRecord

func (receiver MyPlayingRecordList) GetAllDramaId() []uint32 {
	dramaIds := make([]uint32, len(receiver))
	for i, v := range receiver {
		dramaIds[i] = v.DramaId
	}
	return dramaIds
}

type MyPlayingRecordRepo interface {
	//GetMyPlayingRecordByPage 获取用户参演记录
	GetMyPlayingRecordByPage(ctx context.Context, uid uint32, query bson.M, pageSize uint32, lastPlayingTime int64) (MyPlayingRecordList, error)
	// BatchUpsertRecord 批量更新用户参演记录
	BatchUpsertRecord(ctx context.Context, records []*MyPlayingRecord) error
	// BatchDeleteMyDramaPlayingRecord 批量删除用户参演记录
	BatchDeleteMyDramaPlayingRecord(ctx context.Context, id []string, uid uint32) error
	GetUserPlayingRecordIds(ctx context.Context, uid uint32, searchOpts ...*pb.SearchOption) ([]string, error)
}

type ChannelPlayingMicInfoSnapshot struct {
	ChannelID  uint32            `json:"channel_id"`   // 房间id
	RoundID    int64             `json:"round_id"`     // 轮次id
	DramaID    uint32            `json:"drama_id"`     // 剧本id
	CreateTime int64             `json:"create_time"`  // 创建时间
	MicUserMap map[uint32]uint32 `json:"mic_user_map"` // 麦位id和用户id的映射
}

func (c ChannelPlayingMicInfoSnapshot) GetAllUserId() []uint32 {
	userIds := make([]uint32, 0, len(c.MicUserMap))
	for _, v := range c.MicUserMap {
		userIds = append(userIds, v)
	}
	return userIds
}

type ChannelPlayingMicInfoSnapshotList []*ChannelPlayingMicInfoSnapshot

//GetAllValidUserId 获取所有在指定有效麦位的有效的用户id
// 在麦位上滞留的时间超过了指定的时间的才算有效。
// 并且是累计的，也就是说，如果用户在麦位上滞留了10分钟，然后下麦了，然后又上其他有效麦10分钟了，那么这个用户在麦位上的滞留时间就是20分钟
func (receiver ChannelPlayingMicInfoSnapshotList) GetAllValidUserId() []uint32 {
	if len(receiver) == 0 {
		return []uint32{}
	}
	// 用户的累计时间统计变量
	userIds := make(map[uint32]int64, len(receiver[0].MicUserMap)*3)
	for micId := range receiver[0].MicUserMap {
		uid := uint32(0)
		joinTime := int64(0)
		for _, snapshot := range receiver {
			if uid == 0 {
				uid = snapshot.MicUserMap[micId]
				joinTime = snapshot.CreateTime
				continue
			}
			// 麦位换了人，计算uid在麦位上的时间
			if uid != snapshot.MicUserMap[micId] {
				userIds[uid] = userIds[uid] + snapshot.CreateTime - joinTime
				// 更新uid和joinTime
				uid = snapshot.MicUserMap[micId]
				joinTime = snapshot.CreateTime
			}
		}
	}
	// 提取合格的uid
	uids := make([]uint32, 0, len(userIds))
	for uid, totalTime := range userIds {
		if totalTime >= 60 {
			uids = append(uids, uid)
		}
	}
	return uids
}

type ChannelPlayingMicInfoCacheRepo interface {
	// Save 保存房间麦位信息快照
	Save(ctx context.Context, info *ChannelPlayingMicInfoSnapshot) error
	// GetLastSnapshot 获取最后一次的快照，如果没有，则返回 ErrChannelPlayingMicInfoNotFound
	GetLastSnapshot(ctx context.Context, channelID uint32) (*ChannelPlayingMicInfoSnapshot, error)
	// GetAllSnapshotByChannelID 获取指定房间的所有快照
	GetAllSnapshotByChannelID(ctx context.Context, channelID uint32) (ChannelPlayingMicInfoSnapshotList, error)
	Clear(ctx context.Context, channelID uint32) error
}

type MyPlayingRecordMgr struct {
	channelPlayingMicInfoCacheRepo ChannelPlayingMicInfoCacheRepo
	channelMicCli                  channelMic.IClient
	channelCli                     channel.IClient
	timeService                    TimeService
	MyPlayingRecordRepo            MyPlayingRecordRepo
	convertor                      Convertor
	DramaRepo                      IDramaV2
	channelPlayingService          ChannelPlayingService
	dramaSearchOptionsService      DramaSearchOptionsService
	dramaConfig                    *conf.DramaConfig
	reporter                       *Reporter
}

func NewMyPlayingRecordMgr(
	channelPlayingMicInfoCacheRepo ChannelPlayingMicInfoCacheRepo,
	channelMicCli channelMic.IClient,
	timeService TimeService,
	myPlayingRecordRepo MyPlayingRecordRepo,
	convertor Convertor,
	dramaRepo IDramaV2,
	channelPlayingService ChannelPlayingService,
	dramaSearchOptionsService DramaSearchOptionsService,
	dramaConfig *conf.DramaConfig,
	reporter *Reporter,
	channelCli channel.IClient,
) *MyPlayingRecordMgr {
	return &MyPlayingRecordMgr{
		channelPlayingMicInfoCacheRepo: channelPlayingMicInfoCacheRepo,
		channelMicCli:                  channelMicCli,
		timeService:                    timeService,
		MyPlayingRecordRepo:            myPlayingRecordRepo,
		convertor:                      convertor,
		DramaRepo:                      dramaRepo,
		channelPlayingService:          channelPlayingService,
		dramaSearchOptionsService:      dramaSearchOptionsService,
		dramaConfig:                    dramaConfig,
		reporter:                       reporter,
		channelCli:                     channelCli,
	}
}

//StartPlaying 开始走本时记录房间的参演情况
func (m *MyPlayingRecordMgr) StartPlaying(ctx context.Context, channelID uint32, roundID int64, dramaID uint32) {
	// 先清掉之前的数据
	err := m.channelPlayingMicInfoCacheRepo.Clear(ctx, channelID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[开始参演]StartPlaying,清除缓存失败,err:%+v,channelID:%d,roundID:%d,dramaID:%d", err, channelID, roundID, dramaID)
		return
	}
	// 获取统一时间
	currentTime, err := m.timeService.CurrentTime(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "[开始参演]StartPlaying,获取统一时间失败,err:%+v,channelID:%d,roundID:%d,dramaID:%d", err, channelID, roundID, dramaID)
		return
	}
	// 获取房间的演绎麦位信息
	channelPlayingInfo, err := m.channelPlayingService.FetchByChannelId(ctx, channelID, WithChannelPlayingDramaInfoReadMask().MaskMicRoleMap())
	if err != nil {
		log.ErrorWithCtx(ctx, "[开始参演]StartPlaying,获取房间的演绎麦位信息失败,err:%+v,channelID:%d,roundID:%d,dramaID:%d", err, channelID, roundID, dramaID)
		return
	}
	// 获取房间的麦位信息
	list, serverError := m.channelMicCli.GetMicrList(ctx, channelID, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "[开始参演]StartPlaying,获取麦位信息失败,err:%+v,channelID:%d,roundID:%d,dramaID:%d", serverError, channelID, roundID, dramaID)
		return
	}
	// 保存到缓存
	info := &ChannelPlayingMicInfoSnapshot{
		ChannelID:  channelID,
		RoundID:    roundID,
		DramaID:    dramaID,
		CreateTime: currentTime.Unix(),
		MicUserMap: make(map[uint32]uint32, len(list.GetAllMicList())),
	}
	for _, item := range list.GetAllMicList() {
		// 只有在演绎麦位的麦位才算
		if _, ok := channelPlayingInfo.GetMicRoleMap().BindingList[item.GetMicId()]; ok {
			info.MicUserMap[item.MicId] = item.MicUid
		}
	}
	// 保存
	err = m.channelPlayingMicInfoCacheRepo.Save(ctx, info)
	if err != nil {
		log.ErrorWithCtx(ctx, "[开始参演]StartPlaying,保存缓存失败,err:%+v,info:%+v", err, info)
		return
	}
	// 异步上报到数分
	go m.ReportToByLink(ctx, []*ChannelPlayingMicInfoSnapshot{info})
}

// JoinOrQuitPlaying 上麦参加或者下麦退出参演
func (m *MyPlayingRecordMgr) JoinOrQuitPlaying(ctx context.Context, channelID uint32) {
	// 获取房间最后一个快照，如果没有快照，则认为房间没有开始，统一不处理
	snapshot, err := m.channelPlayingMicInfoCacheRepo.GetLastSnapshot(ctx, channelID)
	if err != nil {
		if err == ErrChannelPlayingMicInfoNotFound {
			log.InfoWithCtx(ctx, "[上麦参加或者下麦退出参演]JoinOrQuitPlaying,没有快照,channelID:%d", channelID)
			return
		}
		log.ErrorWithCtx(ctx, "[上麦参加或者下麦退出参演]JoinOrQuitPlaying,获取最后一次快照失败,err:%+v,channelID:%d", err, channelID)
		return
	}
	// 获取时间
	time, err := m.timeService.CurrentTime(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "[上麦参加或者下麦退出参演]JoinOrQuitPlaying,获取统一时间失败,err:%+v,channelID:%d", err, channelID)
		return
	}
	// 获取房间的麦位信息
	list, serverError := m.channelMicCli.GetMicrList(ctx, channelID, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "[上麦参加或者下麦退出参演]JoinOrQuitPlaying,获取麦位信息失败,err:%+v,channelID:%d", serverError, channelID)
		return
	}
	// 保存到缓存
	info := &ChannelPlayingMicInfoSnapshot{
		ChannelID:  channelID,
		RoundID:    snapshot.RoundID,
		DramaID:    snapshot.DramaID,
		CreateTime: time.Unix(),
		MicUserMap: make(map[uint32]uint32, len(list.GetAllMicList())),
	}
	for _, item := range list.GetAllMicList() {
		// 只有在演绎麦位的麦位才算
		if _, ok := snapshot.MicUserMap[item.GetMicId()]; ok {
			info.MicUserMap[item.MicId] = item.MicUid
		}
	}
	// 保存
	err = m.channelPlayingMicInfoCacheRepo.Save(ctx, info)
	if err != nil {
		log.ErrorWithCtx(ctx, "[上麦参加或者下麦退出参演]JoinOrQuitPlaying,保存缓存失败,err:%+v,info:%+v", err, info)
		return
	}
	// 异步上报到数分
	go m.ReportToByLink(ctx, []*ChannelPlayingMicInfoSnapshot{info})
}

//EndPlaying 结束参演进行结算
func (m *MyPlayingRecordMgr) EndPlaying(ctx context.Context, channelID uint32, roundID int64) {
	log.DebugWithCtx(ctx, "[结束参演]EndPlaying,channelID:%d,roundID:%d", channelID, roundID)
	defer func() {
		err := m.channelPlayingMicInfoCacheRepo.Clear(ctx, channelID)
		if err != nil {
			log.ErrorWithCtx(ctx, "EndPlaying failed to Clear, channelID:%d, err:%v", channelID, err)
		}
	}()
	// 获取房间的全部快照
	snapshotList, err := m.channelPlayingMicInfoCacheRepo.GetAllSnapshotByChannelID(ctx, channelID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[结束参演]EndPlaying,获取全部快照失败,err:%+v,channelID:%d,roundID:%d", err, channelID, roundID)
		return
	}
	log.DebugWithCtx(ctx, "[结束参演]EndPlaying,获取全部快照成功,snapshotList:%#v,channelID:%d,roundID:%d", snapshotList, channelID, roundID)
	if len(snapshotList) == 0 {
		log.InfoWithCtx(ctx, "[结束参演]EndPlaying,没有快照,channelID:%d,roundID:%d", channelID, roundID)
		return
	}
	// 判断是否是同一场走本
	if snapshotList[0].RoundID != roundID {
		log.InfoWithCtx(ctx, "[结束参演]EndPlaying,不是同一场走本,channelID:%d,roundID:%d,snapshotList[0].RoundID:%d", channelID, roundID, snapshotList[0].RoundID)
		return
	}
	// 获取时间
	time, err := m.timeService.CurrentTime(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "[结束参演]EndPlaying,获取统一时间失败,err:%+v,channelID:%d,roundID:%d", err, channelID, roundID)
		return
	}
	// 增加一个停止的快照
	snapshotList = append(snapshotList, &ChannelPlayingMicInfoSnapshot{
		ChannelID:  channelID,
		RoundID:    roundID,
		DramaID:    snapshotList[0].DramaID,
		CreateTime: time.Unix(),
		MicUserMap: make(map[uint32]uint32, len(snapshotList[0].MicUserMap)),
	})
	// 获取有效的用户id
	userIDs := snapshotList.GetAllValidUserId()
	if len(userIDs) == 0 {
		log.InfoWithCtx(ctx, "[结束参演]EndPlaying,没有有效的用户,channelID:%d,roundID:%d,snapshot:%+v", channelID, roundID, snapshotList)
		return
	}
	// 保存用户的参演记录
	userPlayingRecord := make([]*MyPlayingRecord, len(userIDs))
	for i, userID := range userIDs {
		userPlayingRecord[i] = &MyPlayingRecord{
			ID:       fmt.Sprintf("%d_%d", userID, snapshotList[0].DramaID),
			PlayTime: snapshotList[0].CreateTime,
			Uid:      userID,
			DramaId:  snapshotList[0].DramaID,
		}
	}
	log.DebugWithCtx(ctx, "[结束参演]EndPlaying,保存用户的参演记录,userPlayingRecord:%#v", userPlayingRecord)
	err = m.MyPlayingRecordRepo.BatchUpsertRecord(ctx, userPlayingRecord)
	if err != nil {
		log.ErrorWithCtx(ctx, "[结束参演]EndPlaying,保存用户的参演记录失败,err:%+v,channelID:%d,roundID:%d,userPlayingRecord:%#v", err, channelID, roundID, userPlayingRecord)
		return
	}
}

//GetMyDramaPlayingRecord 获取用户参演记录
func (m *MyPlayingRecordMgr) GetMyDramaPlayingRecord(c context.Context, req *pb.GetMyDramaPlayingRecordReq) (*pb.GetMyDramaPlayingRecordResp, error) {
	// 解析查询条件
	query := bson.M{}
	m.dramaSearchOptionsService.ParseToMongo(req.GetSearchOption(), query)
	// 解析token
	token := MyPlayingRecordPageToken{}
	err := token.Unmarshal(req.GetPageToken())
	if err != nil {
		log.ErrorWithCtx(c, "[获取用户参演记录]GetMyDramaPlayingRecord,解析token失败,err:%+v,token:%s", err, req.GetPageToken())
	}
	// 格式化页大小
	pageSize := m.dramaConfig.PlayingRecordPageSize()
	page, err := m.MyPlayingRecordRepo.GetMyPlayingRecordByPage(c, req.GetUid(), query, pageSize, token.LastPlayingTime)
	if err != nil {
		log.ErrorWithCtx(c, "[获取用户参演记录]GetMyDramaPlayingRecord,获取用户参演记录失败,err:%+v,uid:%d", err, req.GetUid())
		return nil, fmt.Errorf("GetMyDramaPlayingRecord,获取用户参演记录失败, uid:%d ,err:%w", req.GetUid(), err)
	}
	// 构造返回结果
	resp := &pb.GetMyDramaPlayingRecordResp{
		DramaPlayingRecordList: make([]*pb.PiaDramaPlayingRecord, len(page)),
		PageToken:              "",
	}
	// 构造下一页token
	if len(page) >= int(pageSize) {
		token.LastPlayingTime = page[len(page)-1].PlayTime
		tokenStr, err := token.Marshal()
		if err != nil {
			log.ErrorWithCtx(c, "[获取用户参演记录]GetMyDramaPlayingRecord,构造token失败,err:%+v,token:%+v", err, token)
		} else {
			resp.PageToken = tokenStr
		}
	}
	// 获取剧本的信息
	dramaList, err := m.DramaRepo.BatchGetDramasByIDs(c, page.GetAllDramaId(), WithDramaInfoFieldMask().MaskContentList().MaskBgmList().MaskRoleList().MaskPictureList())
	if err != nil {
		log.ErrorWithCtx(c, "[获取用户参演记录]GetMyDramaPlayingRecord,获取剧本信息失败,err:%+v,dramaIDs:%+v", err, page.GetAllDramaId())
		return nil, err
	}
	// 获取剧本的信息索引
	dramaMap := make(map[uint32]*DramaEntityV2, len(dramaList))
	for _, drama := range dramaList {
		dramaMap[drama.ID] = drama
	}
	for i, item := range page {
		if drama, ok := dramaMap[item.DramaId]; ok {
			// 副本 对于本人非私密
			if drama.Uid == req.GetUid() {
				drama.IsPrivate = false
			}
			resp.DramaPlayingRecordList[i] = m.convertor.MyPlayingRecordToPB(item, drama)
		}
	}
	return resp, nil
}

//BatchDeleteMyDramaPlayingRecord 批量删除用户参演记录
func (m *MyPlayingRecordMgr) BatchDeleteMyDramaPlayingRecord(c context.Context, req *pb.PiaBatchDeleteMyPlayingRecordReq) (*pb.PiaBatchDeleteMyPlayingRecordResp, error) {
	if req.GetUid() == 0 {
		log.ErrorWithCtx(c, "[批量删除用户参演记录]BatchDeleteMyDramaPlayingRecord,uid为0")
		return nil, ErrMissingUserID
	}
	if len(req.GetRecordIdList()) == 0 {
		return &pb.PiaBatchDeleteMyPlayingRecordResp{}, nil
	}
	err := m.MyPlayingRecordRepo.BatchDeleteMyDramaPlayingRecord(c, req.GetRecordIdList(), req.GetUid())
	if err != nil {
		log.ErrorWithCtx(c, "[批量删除用户参演记录]BatchDeleteMyDramaPlayingRecord,批量删除用户参演记录失败,err:%+v,recordIdList:%+v,uid:%d", err, req.GetRecordIdList(), req.GetUid())
		return nil, err
	}
	return &pb.PiaBatchDeleteMyPlayingRecordResp{}, nil
}

func (m *MyPlayingRecordMgr) GetMyPlayingRecordIdList(c context.Context, req *pb.PiaGetMyPlayingRecordIdListReq) (*pb.PiaGetMyPlayingRecordIdListResp, error) {
	out := &pb.PiaGetMyPlayingRecordIdListResp{}
	if req.GetUid() == 0 {
		log.ErrorWithCtx(c, "GetMyPlayingRecordIdList, uid为0")
		return nil, ErrMissingUserID
	}

	idList, err := m.MyPlayingRecordRepo.GetUserPlayingRecordIds(c, req.GetUid(), req.GetSearchOption()...)
	if err != nil {
		log.ErrorWithCtx(c, "GetMyPlayingRecordIdList failed to GetAllPlayingRecordIdByUid, uid:%d, err:%v", req.GetUid(), err)
		return nil, err
	}
	out.RecordIdList = idList
	return out, nil
}

func (m *MyPlayingRecordMgr) ReportToByLink(c context.Context, list ChannelPlayingMicInfoSnapshotList) {
	if len(list) == 0 {
		return
	}
	// 超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	roundID := strconv.FormatInt(list[0].RoundID, 10)
	channelID := strconv.FormatInt(int64(list[0].ChannelID), 10)
	dramaID := strconv.FormatInt(int64(list[0].DramaID), 10)
	logs := make([]*DeduceMicLog, 0, len(list))
	for _, item := range list {
		UidList := make([]string, 0, len(item.MicUserMap))
		for _, userId := range item.GetAllUserId() {
			if userId > 0 {
				UidList = append(UidList, strconv.FormatInt(int64(userId), 10))
			}
		}
		if len(UidList) == 0 {
			continue
		}
		logs = append(logs, &DeduceMicLog{
			RoundID:  roundID,
			RoomID:   channelID,
			ScriptID: dramaID,
			UidList:  strings.Join(UidList, ","),
		},
		)
	}
	reportErr := m.reporter.ReportMicInfo(ctx, logs)
	if reportErr != nil {
		log.ErrorWithCtx(ctx, "[麦位用户快照]异步上报到数分,上报到数分失败,err:%+v,logs:%v", reportErr, logs)
	}
}
