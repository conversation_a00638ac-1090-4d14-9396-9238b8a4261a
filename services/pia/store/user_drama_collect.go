package store

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/pia/mgr"
)

type UserDramaCollect struct {
	cli *mongo.Collection
}

func (s *UserDramaCollect) ensureIndexes() error {
	uid1CtimeM1 := mongo.IndexModel{
		Keys: bson.D{primitive.E{Key: "uid", Value: 1}, primitive.E{Key: "collect_time", Value: -1}},
	}

	_, err := s.cli.Indexes().CreateOne(context.Background(), uid1CtimeM1)
	if err != nil {
		return ignoreKnownError(err)
	}
	return nil
}

func NewUserDramaCollect(mongoCli *mongo.Client) mgr.IUserDramaCollect {
	s := &UserDramaCollect{
		cli: mongoCli.Database(DB).Collection(UserDramaCollectLogColl),
	}

	if err := s.ensureIndexes(); err != nil {
		panic(err)
	}
	return s
}

// 收藏
func (s *UserDramaCollect) SetCollectInfo(ctx context.Context, uid uint32, info *mgr.UserDramaColl) error {
	_, err := s.cli.InsertOne(ctx, info)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserCollect fail ,err :%v", err)
		return err
	}
	return nil
}

// 取消收藏
func (s *UserDramaCollect) DelUserCollectInfo(ctx context.Context, uid uint32, dramaId uint32) error {
	_, err := s.cli.DeleteOne(ctx, bson.M{"uid": uid, "drama_id": dramaId})
	if err != nil {
		log.ErrorWithCtx(ctx, "DelUserCollectInfo.DeleteOne fail,err:%v", err)
	}

	return err
}

// 判断用户的收藏状态
func (s *UserDramaCollect) GetUserDramaCollByUidDid(ctx context.Context, uid, dramaId uint32) (bool, error) {
	filter := bson.M{
		"uid":      uid,
		"drama_id": dramaId,
	}

	res := s.cli.FindOne(ctx, filter)
	if res.Err() != nil {
		if res.Err() == mongo.ErrNoDocuments {
			return false, nil
		}
		log.ErrorWithCtx(ctx, "GetUserDramaCollByUidDid failed to Find, err:%v", res.Err())
		return false, res.Err()
	}

	return true, nil
}

// 作品搜索信息变化
func (s *UserDramaCollect) UpdateUserCollInfo(ctx context.Context, dramaId uint32, info *mgr.UserDramaColl) error {
	filter := bson.M{
		"drama_id": dramaId,
	}

	update := bson.M{
		"$set": bson.M{
			"type":       info.Type,
			"tags":       append([]string{}, info.Tags...),
			"male_cnt":   info.MaleCnt,
			"female_cnt": info.FemaleCnt,
			"is_freeze":  info.IsFreeze,
			"is_private": info.IsPrivate,
			"deleted":    info.Deleted,
		},
	}

	_, err := s.cli.UpdateMany(ctx, filter, update)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUserDramaCollStatus fail ,err :%v", err)
		return err
	}

	return nil
}

type UserCollectedInfo struct {
	Uid           uint32             `bson:"uid"`
	CollectedTime int64              `bson:"collect_time"`
	DramaID       uint32             `bson:"drama_id"`
	DramaInfo     *mgr.DramaEntityV2 `bson:"drama_info"`
}

func (s *UserDramaCollect) GetUserDramaCollList(ctx context.Context, uid uint32, query bson.M, limit, lastCollectedTime uint32) ([]*mgr.UserDramaCollTime, error) {
	collectedInfoFilter := bson.M{
		"uid": uid,
	}
	if lastCollectedTime > 0 {
		collectedInfoFilter["collect_time"] = bson.M{"$lt": lastCollectedTime}
	}
	if limit >= 30 {
		limit = 30
	}
	matchKeyWord := "$match"
	lookup := mongo.Pipeline{
		bson.D{bson.E{Key: matchKeyWord, Value: collectedInfoFilter}},
		bson.D{bson.E{Key: "$sort", Value: bson.M{"collect_time": -1}}},
		bson.D{
			bson.E{
				Key: "$lookup", Value: bson.D{
					bson.E{Key: "from", Value: DramaCollV2},
					bson.E{Key: "as", Value: "drama_info"},
					bson.E{Key: "let", Value: bson.M{"drama_id": "$drama_id"}},
					bson.E{Key: "pipeline", Value: mongo.Pipeline{
						bson.D{bson.E{Key: matchKeyWord, Value: bson.M{"$expr": bson.M{"$eq": []interface{}{"$_id", "$$drama_id"}}}}},
						bson.D{bson.E{Key: matchKeyWord, Value: query}},
					}},
				},
			},
		},
		bson.D{bson.E{Key: "$unwind", Value: "$drama_info"}},
		bson.D{bson.E{Key: "$project", Value: bson.M{
			"drama_id":     1,
			"collect_time": 1,
		}}},
		bson.D{bson.E{Key: "$limit", Value: limit}},
	}
	aggregate, err := s.cli.Aggregate(ctx, lookup)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetUserDramaCollList]search failed to Aggregate, err:%v", err)
		return nil, err
	}
	defer func() {
		if err := aggregate.Close(ctx); err != nil {
			log.ErrorWithCtx(ctx, "[GetUserDramaCollList]search failed to Close, err:%v", err)
		}
	}()
	var userCollectedInfoList []*UserCollectedInfo
	err = aggregate.All(ctx, &userCollectedInfoList)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetUserDramaCollList]search failed to All, err:%v", err)
		return nil, err
	}
	userDramaCollTimeList := make([]*mgr.UserDramaCollTime, 0, len(userCollectedInfoList))
	for _, userCollectedInfo := range userCollectedInfoList {
		userDramaCollTimeList = append(userDramaCollTimeList, &mgr.UserDramaCollTime{
			CollectTime: userCollectedInfo.CollectedTime,
			DramaId:     userCollectedInfo.DramaID,
		})
	}
	return userDramaCollTimeList, nil
}

func (s *UserDramaCollect) AggregateDramaCollectedCount(ctx context.Context, dramaIds ...uint32) (map[uint32][]uint32, error) {
	pipeline := mongo.Pipeline{}
	if len(dramaIds) > 0 {
		pipeline = append(pipeline, bson.D{
			bson.E{
				Key: "$match", Value: bson.M{
					"drama_id": bson.M{"$in": dramaIds},
				},
			},
		})
	}
	pipeline = append(pipeline, bson.D{
		bson.E{
			Key: "$group",
			Value: bson.M{
				"_id": "$drama_id",
			},
		},
	}, bson.D{
		bson.E{
			Key: "$lookup", Value: bson.D{
				bson.E{Key: "from", Value: UserDramaCollectLogColl},
				bson.E{Key: "let", Value: bson.M{"drama_id": "$_id"}},
				bson.E{Key: "pipeline", Value: mongo.Pipeline{
					bson.D{bson.E{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": []interface{}{"$drama_id", "$$drama_id"}}}}},
					bson.D{bson.E{Key: "$project", Value: bson.M{"_id": 0, "uid": 1}}},
				}},
				bson.E{Key: "as", Value: "uid_array"},
			},
		},
	}, bson.D{bson.E{Key: "$project", Value: bson.M{
		"_id": 1,
		"uid": bson.M{"$concatArrays": []string{"$uid_array.uid"}},
	}}})
	cursor, err := s.cli.Aggregate(ctx, pipeline)
	if err != nil {
		log.ErrorWithCtx(ctx, "[AggregateDramaCollectedCount]search failed to Aggregate, err:%v", err)
		return nil, err
	}
	defer func() {
		if err := cursor.Close(ctx); err != nil {
			log.ErrorWithCtx(ctx, "[AggregateDramaCollectedCount]search failed to Close the cursor, err:%v", err)
		}
	}()
	if cursor.RemainingBatchLength() == 0 {
		return map[uint32][]uint32{}, nil
	}
	result := make(map[uint32][]uint32, cursor.RemainingBatchLength())
	for cursor.Next(ctx) {
		tmp := struct {
			DramaId uint32   `bson:"_id"`
			Uid     []uint32 `bson:"uid"`
		}{}
		err := cursor.Decode(&tmp)
		if err != nil {
			log.ErrorWithCtx(ctx, "[AggregateDramaCollectedCount]search failed to Decode, err:%v", err)
			return nil, err
		}
		result[tmp.DramaId] = tmp.Uid
	}
	return result, nil
}
