package manager

import (
	"context"
	"fmt"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/clients/currency"
	sendIm "golang.52tt.com/clients/sendim"
	"golang.52tt.com/pkg/log"
	currencyPB "golang.52tt.com/protocol/services/currencysvr"
	sendim "golang.52tt.com/protocol/services/sendimsvr"
	"golang.52tt.com/services/operate-platform/cache"
	"time"
)

type AwardType uint32

const (
	AwardTypeUnknown  AwardType = 0
	AwardTypeCurrency AwardType = 1
)

type AuthAwardMgr struct {
	// 直接继承一下
	*NobilityStaMgr
	cache       *cache.OperatePlatformCache
	currencyCli *currency.Client
	apiCli      *apicenter.Client
	sendImCli   *sendIm.Client
}

func NewAuthAwardMgr(mgr *NobilityStaMgr, platformCache *cache.OperatePlatformCache) (*AuthAwardMgr, error) {
	currencyCli := currency.NewClient()
	apiCli := apicenter.NewClient()
	sendImCli := sendIm.NewClient()

	_ = mgr.mysqlSto.CreateAuthAwardTable("1")

	return &AuthAwardMgr{
		NobilityStaMgr: mgr,
		cache:          platformCache,
		currencyCli:    currencyCli,
		apiCli:         apiCli,
		sendImCli:      sendImCli,
	}, nil
}

func (m *AuthAwardMgr) IsAuthAward(ctx context.Context, cfgId string, uid uint32) (ok bool, err error) {
	ok, exist, err := m.cache.GetAuthAward(cfgId, uid)
	if err != nil {
		log.Errorf("IsAuthAward GetAuthAward err , cfgId: %s , uid %d , err %v", cfgId, uid, err)
		return
	}
	if !exist {
		hasAward, err := m.mysqlSto.IsUserHaveAuthAward(ctx, cfgId, uid)
		if err != nil {
			log.Errorf("IsAuthAward IsUserHaveAuthAward err , cfgId: %s , uid %d , err %v", cfgId, uid, err)
			return false, err
		}
		_ = m.cache.RecordAuthAward(cfgId, uid, hasAward)
		log.Debugf("IsAuthAward res: %d , uid: %d ", ok, uid)
		return hasAward, nil
	}
	log.Debugf("IsAuthAward res: %d , uid: %d ", ok, uid)
	return ok, nil
}

func (m *AuthAwardMgr) RecordAuthAward(ctx context.Context, cfgId string, uid uint32) (err error) {
	err = m.mysqlSto.RecordAuthAward(ctx, cfgId, uid, time.Now())
	if err != nil {
		log.Errorf("RecordAuthAward err , cfgId: %s , uid %d , err %v", cfgId, uid, err)
		return
	}

	_ = m.cache.RecordAuthAward(cfgId, uid, true)

	return err
}

func (m *AuthAwardMgr) GiveUserAward(ctx context.Context, uid, value, awardType uint32, cfgId string) (err error) {
	if awardType == uint32(AwardTypeCurrency) {
		orderId := genCurrencyOrderId(uid, cfgId)
		err := m.currencyCli.AddUserCurrency(ctx, uid, int32(value), orderId, "NewVersionAuth", uint32(currencyPB.ADD_CURRENCY_REASON_UNKNOWN))
		if err != nil {
			log.Errorf("GiveUserAward AddUserCurrency err , uid %d , err %v", uid, err)
			return err
		}
		_ = m.apiCli.NotifyGrowInfoSync(ctx, uid)
		_ = m.writeOfficialIMMsgToUser(uid, "恭喜更新至新版本，开启TT新玩法的遨游吧！2000红钻已发放至背包，请查收~")
		log.Debugf("GiveUserAward end, uid %d, value %d, awardType %d", uid, value, awardType)
	}
	return nil
}

func genCurrencyOrderId(uid uint32, cfgId string) string {
	return fmt.Sprintf("auth_award_%d_%s", uid, cfgId)
}

func (m *AuthAwardMgr) writeOfficialIMMsgToUser(toUid uint32, content string) (err error) {
	msg := &sendim.SendSyncReq{
		Sender: &sendim.Sender{
			Type: int32(sendim.Sender_User),
			Id:   10000,
		},
		Receiver: &sendim.Receiver{
			Type:   int32(sendim.Receiver_User),
			IdList: []uint32{toUid},
		},
		Msg: &sendim.ImMsg{
			Content: &sendim.Content{
				Type:       int32(sendim.Content_Text),
				TextNormal: &sendim.ImTextNormal{Content: content},
			},
			AppPlatform: "android",
			AppName:     "ttvoice",
			ExpiredAt:   uint32(time.Now().Unix()) + 86400,
		},
		WithNotify: true,
	}
	log.Debugf("send mission msg: %+v, uid:%d", msg, toUid)
	_, err = m.sendImCli.SendSync(context.Background(), msg)
	if err != nil {
		log.Errorf("SendImMsg err: %s", err.Error())
		return err
	}
	return nil
}
