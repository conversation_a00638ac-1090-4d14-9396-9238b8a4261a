package server

import (
	"context"
	"fmt"
	"github.com/golang/mock/gomock"
	accountCli "golang.52tt.com/clients/account"
	anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
	channel_personalization "golang.52tt.com/clients/channel-personalization"
	headwear_go "golang.52tt.com/clients/headwear-go"
	accountMock "golang.52tt.com/clients/mocks/account"
	anchorcontractGoMock "golang.52tt.com/clients/mocks/anchorcontract-go"
	userDecorationMock "golang.52tt.com/clients/mocks/user-decoration"
	user_decoration "golang.52tt.com/clients/user-decoration"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/profile"
	udpb "golang.52tt.com/protocol/services/user-decoration"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
	"time"

	"gitlab.ttyuyin.com/golang/gudetama/log"
)

var svr *Server

func init() {
	log.Init("", log.DebugLevel)

	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))

	var err error
	svr, err = NewServer(context.TODO(), &StartConfig{})
	if err != nil {
		log.Fatalln(err)
	}

}

func TestServer_UserDecorations1(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	userDecorationCli := userDecorationMock.NewMockIClient(ctl)
	anchorcontractGoCli := anchorcontractGoMock.NewMockIClient(ctl)
	accountMockCli := accountMock.NewMockIClient(ctl)

	userDecorationCli.EXPECT().UserDecorations(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*udpb.DecInfo{{
		Decoration: &udpb.Decoration{
			Typ:        2,
			Id:         "111",
			Version:    "1",
			FusionTtid: "",
		},
		DecDetail: &udpb.DecDetail{
			WallExtend: &udpb.WallExtend{},
		},
		Tim: &udpb.Tim{Begin: time.Now().Add(-time.Second).Unix(), End: time.Now().Add(time.Hour).Unix()},
	}}, nil)

	userDecorationCli.EXPECT().UserDecorations(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*udpb.DecInfo{{
		Decoration: &udpb.Decoration{
			Typ:        1,
			Id:         "",
			Version:    "",
			FusionTtid: "",
		},
		DecDetail: &udpb.DecDetail{
			WallExtend: &udpb.WallExtend{},
		},
		Tim: &udpb.Tim{Begin: time.Now().Add(-time.Second).Unix(), End: time.Now().Add(time.Hour).Unix()},
	}}, nil)

	userDecorationCli.EXPECT().Current(gomock.Any(), gomock.Any(), gomock.Any()).Return(&udpb.CurrentResp{
		Dec: &udpb.Decoration{
			Typ:        2,
			Id:         "111",
			Version:    "1",
			FusionTtid: "",
		}}, nil)

	userDecorationCli.EXPECT().Current(gomock.Any(), gomock.Any(), gomock.Any()).Return(&udpb.CurrentResp{
		Dec: &udpb.Decoration{
			Typ:        1,
			Id:         "",
			Version:    "",
			FusionTtid: "",
		}}, nil)

	accountMockCli.EXPECT().BatchQueryUidListByAccount(gomock.Any(), gomock.Any())
	accountMockCli.EXPECT().BatchQueryUidListByAccount(gomock.Any(), gomock.Any())
	accountMockCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any())
	accountMockCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any())

	ctx := context.Background()

	ctx = grpc.WithServiceInfo(ctx, &grpc.ServiceInfo{
		UserID: 2527884,
	})

	type fields struct {
		userDecorationCli user_decoration.IClient
		anchorCli         anchorcontract_go.IClient
		wallConfigCache   []*udpb.DecorationInfo
		floatConfigCache  []*udpb.DecorationInfo
		accountCli        accountCli.IClient
	}
	type args struct {
		ctx context.Context
		in  *profile.UserDecorationsReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *profile.UserDecorationsResp
		wantErr bool
	}{
		{
			name: "UserDecorations",
			fields: fields{
				userDecorationCli: userDecorationCli,
				anchorCli:         anchorcontractGoCli,
				wallConfigCache: []*udpb.DecorationInfo{{
					Decoration: &udpb.Decoration{
						Typ:        2,
						Id:         "111",
						Version:    "1",
						FusionTtid: "",
					},
					DecDetail: &udpb.DecDetail{
						WallExtend: &udpb.WallExtend{},
					},
				}, {
					Decoration: &udpb.Decoration{
						Typ:        1,
						Id:         "222",
						Version:    "2",
						FusionTtid: "",
					},
					DecDetail: &udpb.DecDetail{
						WallExtend: &udpb.WallExtend{},
					},
				},
				},
				floatConfigCache: nil,
				accountCli:       accountMockCli,
			},
			args: args{
				ctx: ctx,
				in: &profile.UserDecorationsReq{
					Uid: 2527884,
					Typ: 2,
				},
			},
			wantErr: false,
		},
		{
			name: "UserDecorations1",
			fields: fields{
				userDecorationCli: userDecorationCli,
				anchorCli:         anchorcontractGoCli,
				wallConfigCache: []*udpb.DecorationInfo{{
					Decoration: &udpb.Decoration{
						Typ:        1,
						Id:         "",
						Version:    "",
						FusionTtid: "",
					},
					DecDetail: &udpb.DecDetail{
						WallExtend: &udpb.WallExtend{},
					},
				},
				},
				floatConfigCache: nil,
				accountCli:       accountMockCli,
			},
			args: args{
				ctx: ctx,
				in: &profile.UserDecorationsReq{
					Uid: 2527884,
					Typ: 1,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				userDecorationCli: tt.fields.userDecorationCli,
				anchorCli:         tt.fields.anchorCli,
				wallConfigCache:   tt.fields.wallConfigCache,
				floatConfigCache:  tt.fields.floatConfigCache,
				accountCli:        tt.fields.accountCli,
			}
			_, err := s.UserDecorations(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserDecorations() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("UserDecorations() got = %v, want %v", got, tt.want)
			//}
		})
	}
}

func TestServer_UserCurrDecoration(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	userDecorationCli := userDecorationMock.NewMockIClient(ctl)
	anchorcontractGoCli := anchorcontractGoMock.NewMockIClient(ctl)
	accountMockCli := accountMock.NewMockIClient(ctl)

	userDecorationCli.EXPECT().Current(gomock.Any(), gomock.Any(), gomock.Any()).Return(&udpb.CurrentResp{
		Dec: &udpb.Decoration{
			Typ:        2,
			Id:         "111",
			Version:    "111",
			FusionTtid: "",
		}, Time: &udpb.Tim{
			Begin: 0,
			End:   0,
		}}, nil)

	userDecorationCli.EXPECT().Current(gomock.Any(), gomock.Any(), gomock.Any()).Return(&udpb.CurrentResp{
		Dec: &udpb.Decoration{
			Typ:        1,
			Id:         "111",
			Version:    "111",
			FusionTtid: "",
		}, Time: &udpb.Tim{
			Begin: 0,
			End:   0,
		}}, nil)

	userDecorationCli.EXPECT().UserDecorations(gomock.Any(), gomock.Any(), udpb.Type(1)).Return([]*udpb.DecInfo{{
		Decoration: &udpb.Decoration{
			Typ:        1,
			Id:         "111",
			Version:    "111",
			FusionTtid: "",
		},
		DecDetail: &udpb.DecDetail{
			WallExtend: &udpb.WallExtend{},
		},
		Tim: &udpb.Tim{Begin: time.Now().Add(-time.Second).Unix(), End: time.Now().Add(time.Hour).Unix()},
	}}, nil)

	userDecorationCli.EXPECT().Current(gomock.Any(), gomock.Any(), gomock.Any()).Return(&udpb.CurrentResp{
		Dec: &udpb.Decoration{
			Typ:        2,
			Id:         "1111",
			Version:    "1111",
			FusionTtid: "",
		}, Time: &udpb.Tim{
			Begin: 0,
			End:   0,
		}}, nil)

	userDecorationCli.EXPECT().UserDecorations(gomock.Any(), gomock.Any(), udpb.Type(2)).Return([]*udpb.DecInfo{{
		Decoration: &udpb.Decoration{
			Typ:        2,
			Id:         "1111",
			Version:    "1111",
			FusionTtid: "",
		},
		DecDetail: &udpb.DecDetail{
			WallExtend: &udpb.WallExtend{},
		},
		Tim: &udpb.Tim{Begin: time.Now().Add(-time.Second).Unix(), End: time.Now().Add(time.Hour).Unix()},
	}}, nil)

	ctx := context.Background()

	ctx = grpc.WithServiceInfo(ctx, &grpc.ServiceInfo{
		UserID: 2527884,
	})

	type fields struct {
		userDecorationCli user_decoration.IClient
		anchorCli         anchorcontract_go.IClient
		wallConfigCache   []*udpb.DecorationInfo
		floatConfigCache  []*udpb.DecorationInfo
		accountCli        accountCli.IClient
	}
	type args struct {
		ctx context.Context
		in  *profile.UserCurrDecorationReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *profile.UserCurrDecorationResp
		wantErr bool
	}{
		{
			name: "UserDecorations",
			fields: fields{
				userDecorationCli: userDecorationCli,
				anchorCli:         anchorcontractGoCli,
				wallConfigCache: []*udpb.DecorationInfo{{
					Decoration: &udpb.Decoration{
						Typ:        2,
						Id:         "111",
						Version:    "111",
						FusionTtid: "",
					},
					DecDetail: &udpb.DecDetail{
						WallExtend: &udpb.WallExtend{},
					},
				},
				},
				floatConfigCache: nil,
				accountCli:       accountMockCli,
			},
			args: args{
				ctx: ctx,
				in: &profile.UserCurrDecorationReq{
					Uid: 2527884,
					Typ: 2,
				},
			},
			wantErr: false,
		}, {
			name: "UserDecorations1",
			fields: fields{
				userDecorationCli: userDecorationCli,
				anchorCli:         anchorcontractGoCli,
				wallConfigCache: []*udpb.DecorationInfo{{
					Decoration: &udpb.Decoration{
						Typ:        1,
						Id:         "111",
						Version:    "111",
						FusionTtid: "",
					},
					DecDetail: &udpb.DecDetail{
						WallExtend: &udpb.WallExtend{},
					},
				},
				},
				floatConfigCache: nil,
				accountCli:       accountMockCli,
			},
			args: args{
				ctx: ctx,
				in: &profile.UserCurrDecorationReq{
					Uid: 2527884,
					Typ: 1,
				},
			},
			wantErr: false,
		}, {
			name: "UserDecorations2",
			fields: fields{
				userDecorationCli: userDecorationCli,
				anchorCli:         anchorcontractGoCli,
				wallConfigCache: []*udpb.DecorationInfo{{
					Decoration: &udpb.Decoration{
						Typ:        2,
						Id:         "111",
						Version:    "111",
						FusionTtid: "",
					},
					DecDetail: &udpb.DecDetail{
						WallExtend: &udpb.WallExtend{},
					},
				},
				},
				floatConfigCache: nil,
				accountCli:       accountMockCli,
			},
			args: args{
				ctx: ctx,
				in: &profile.UserCurrDecorationReq{
					Uid: 2527884,
					Typ: 2,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				userDecorationCli: tt.fields.userDecorationCli,
				anchorCli:         tt.fields.anchorCli,
				wallConfigCache:   tt.fields.wallConfigCache,
				floatConfigCache:  tt.fields.floatConfigCache,
				accountCli:        tt.fields.accountCli,
			}
			got, err := s.UserCurrDecoration(tt.args.ctx, tt.args.in)
			fmt.Println(got, err)
		})
	}
}

func TestServer_UserAdornDecoration(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	userDecorationCli := userDecorationMock.NewMockIClient(ctl)
	anchorcontractGoCli := anchorcontractGoMock.NewMockIClient(ctl)
	accountMockCli := accountMock.NewMockIClient(ctl)

	userDecorationCli.EXPECT().Adorn(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

	ctx := context.Background()

	ctx = grpc.WithServiceInfo(ctx, &grpc.ServiceInfo{
		UserID: 2527884,
	})

	type fields struct {
		userDecorationCli user_decoration.IClient
		anchorCli         anchorcontract_go.IClient
		wallConfigCache   []*udpb.DecorationInfo
		floatConfigCache  []*udpb.DecorationInfo
		accountCli        accountCli.IClient
	}
	type args struct {
		ctx context.Context
		in  *profile.UserAdornDecorationReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *profile.UserAdornDecorationResp
		wantErr bool
	}{
		{
			name: "UserDecorations",
			fields: fields{
				userDecorationCli: userDecorationCli,
				anchorCli:         anchorcontractGoCli,
				wallConfigCache: []*udpb.DecorationInfo{{
					Decoration: &udpb.Decoration{
						Typ:        2,
						Id:         "111",
						Version:    "111",
						FusionTtid: "",
					},
					DecDetail: &udpb.DecDetail{
						WallExtend: &udpb.WallExtend{},
					},
				},
				},
				floatConfigCache: nil,
				accountCli:       accountMockCli,
			},
			args: args{
				ctx: ctx,
				in: &profile.UserAdornDecorationReq{
					Uid: 2527884,
					Typ: 2,
					Id:  "aa",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				userDecorationCli: tt.fields.userDecorationCli,
				anchorCli:         tt.fields.anchorCli,
				wallConfigCache:   tt.fields.wallConfigCache,
				floatConfigCache:  tt.fields.floatConfigCache,
				accountCli:        tt.fields.accountCli,
			}
			got, err := s.UserAdornDecoration(tt.args.ctx, tt.args.in)
			fmt.Println(got, err)
		})
	}
}

func TestServer_UserRemoveDecoration(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	userDecorationCli := userDecorationMock.NewMockIClient(ctl)
	//anchorcontractGoCli := anchorcontractGoMock.NewMockIClient(ctl)
	//accountMockCli := accountMock.NewMockIClient(ctl)

	userDecorationCli.EXPECT().Remove(gomock.Any(), gomock.Any(), gomock.Any())

	ctx := context.Background()

	ctx = grpc.WithServiceInfo(ctx, &grpc.ServiceInfo{
		UserID: 2527884,
	})

	type fields struct {
		userDecorationCli user_decoration.IClient
		anchorCli         anchorcontract_go.IClient
		wallConfigCache   []*udpb.DecorationInfo
		floatConfigCache  []*udpb.DecorationInfo
		accountCli        accountCli.IClient
	}
	type args struct {
		ctx context.Context
		in  *profile.UserRemoveDecorationReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *profile.UserRemoveDecorationResp
		wantErr bool
	}{
		{
			name: "UserDecorations",
			fields: fields{
				userDecorationCli: userDecorationCli,
				wallConfigCache: []*udpb.DecorationInfo{{
					Decoration: &udpb.Decoration{
						Typ:        2,
						Id:         "111",
						Version:    "111",
						FusionTtid: "",
					},
					DecDetail: &udpb.DecDetail{
						WallExtend: &udpb.WallExtend{},
					},
				},
				},
				floatConfigCache: nil,
			},
			args: args{
				ctx: ctx,
				in: &profile.UserRemoveDecorationReq{
					Uid: 2527884,
					Typ: 2,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				userDecorationCli: tt.fields.userDecorationCli,
				anchorCli:         tt.fields.anchorCli,
				wallConfigCache:   tt.fields.wallConfigCache,
				floatConfigCache:  tt.fields.floatConfigCache,
				accountCli:        tt.fields.accountCli,
			}
			got, err := s.UserRemoveDecoration(tt.args.ctx, tt.args.in)
			fmt.Println(got, err)
		})
	}
}

func TestServer_ChangeDecorationCustomText(t *testing.T) {
	ctx := context.Background()

	ctx = grpc.WithServiceInfo(ctx, &grpc.ServiceInfo{
		UserID: 2527884,
	})

	cpCli, _ := channel_personalization.NewClient()

	type fields struct {
		userDecorationCli user_decoration.IClient
		anchorCli         anchorcontract_go.IClient
		wallConfigCache   []*udpb.DecorationInfo
		floatConfigCache  []*udpb.DecorationInfo
		accountCli        accountCli.IClient
		headwearCli       headwear_go.IClient
		cpCli             channel_personalization.IClient
	}
	type args struct {
		ctx context.Context
		in  *profile.ChangeDecorationCustomTextReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *profile.ChangeDecorationCustomTextResp
		wantErr bool
	}{{
		name: "UserDecorations",
		fields: fields{
			headwearCli: headwear_go.NewClient(),
			cpCli:       cpCli,
		},
		args: args{
			ctx: ctx,
			in:  &profile.ChangeDecorationCustomTextReq{DecorationType: 1},
		},
		wantErr: false,
	}, {
		name: "UserDecorations",
		fields: fields{
			headwearCli: headwear_go.NewClient(),
			cpCli:       cpCli,
		},
		args: args{
			ctx: ctx,
			in:  &profile.ChangeDecorationCustomTextReq{DecorationType: 2},
		},
		wantErr: false,
	}}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				userDecorationCli:         tt.fields.userDecorationCli,
				anchorCli:                 tt.fields.anchorCli,
				wallConfigCache:           tt.fields.wallConfigCache,
				floatConfigCache:          tt.fields.floatConfigCache,
				accountCli:                tt.fields.accountCli,
				headwearCli:               tt.fields.headwearCli,
				channelPersonalizationCli: tt.fields.cpCli,
			}
			got, err := s.ChangeDecorationCustomText(tt.args.ctx, tt.args.in)
			fmt.Println(got, err)
		})
	}
}
