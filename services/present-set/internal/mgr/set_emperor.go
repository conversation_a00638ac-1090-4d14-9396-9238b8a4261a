package mgr

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/present-set"
	userpresent "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/present-set/internal/store"
	"google.golang.org/grpc/codes"
	"sort"
)

func (m *Manager) CreateEmperorSet(ctx context.Context, req *pb.CreateEmperorSetReq) (*pb.CreateEmperorSetResp, error) {
	resp := &pb.CreateEmperorSetResp{}
	eSet := req.GetEmperorSet()
	if eSet == nil || eSet.GetSetName() == "" || eSet.GetIconUrl() == "" || len(eSet.GetPresentList()) == 0 ||
		eSet.GetStartTime() == 0 || eSet.GetEndTime() == 0 || eSet.GetStartTime() > eSet.GetEndTime() {
		return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "参数错误")
	}

	log.InfoWithCtx(ctx, "CreateEmperorSet start req:%+v", req)

	if eSet.GetShowPresentShelf() {
		if eSet.GetRank() == 0 {
			log.ErrorWithCtx(ctx, "CreateEmperorSet rank param err, req:%+v", req)
			return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "礼物架配置错误")
		}
	} else {
		eSet.Rank, eSet.ShowEffectEnd = 0, false // reset
	}

	if eSet.GetBannerJumpUrl() != "" {
		if eSet.GetBannerJumpType() == 0 || eSet.GetBannerJumpType() == pb.EmperorSetBannerJumpType_EmperorSetBannerJumpTypeNone {
			log.ErrorWithCtx(ctx, "CreateEmperorSet banner param err, req:%+v", req)
			return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "banner配置错误")
		}
	} else {
		eSet.BannerJumpUrl, eSet.BannerJumpType = "", 0 // reset
	}

	if eSet.GetEnableBreakingNews() {
		if eSet.GetBreakingNewsId() == 0 {
			log.ErrorWithCtx(ctx, "CreateEmperorSet breaking news param err, req:%+v", req)
			return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "全服ID配置错误")
		}
		valid, err := m.rpc.CheckBreakingNewValid(ctx, eSet.GetBreakingNewsId())
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateEmperorSet CheckBreakingNewValid err:%s, req:%+v", err, req)
			return resp, err
		}
		if !valid {
			log.ErrorWithCtx(ctx, "CreateEmperorSet breaking news not valid, req:%+v", req)
			return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "全服ID不存在")
		}

	} else {
		eSet.BreakingNewsId, eSet.BreakingTimesLimit = 0, 0 // reset
	}

	isNew := eSet.GetSetId() == 0
	includeSetPresent := false // 是否包含套装礼物
	presentList := eSet.GetPresentList()
	isAutoSort := false // 是否自动排序礼物
	presentIdMap := map[uint32]struct{}{}
	presentRankMap := map[float64]struct{}{}

	if !isNew {
		originEmpSet, err := m.getEmperorSetConfigById(ctx, eSet.GetSetId(), false)
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateEmperorSet getEmperorSetConfigById err:%s, req:%+v", err, req)
			return resp, err
		}
		if originEmpSet == nil {
			log.ErrorWithCtx(ctx, "CreateEmperorSet getEmperorSetConfigById not found, req:%+v", req)
			return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "帝王套不存在")
		}
		if originEmpSet.GetSetName() != eSet.GetSetName() || originEmpSet.GetIconUrl() != eSet.GetIconUrl() {
			log.ErrorWithCtx(ctx, "CreateEmperorSet name or icon changed, req:%+v", req)
			return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "帝王套名称或图标不可修改")
		}

		// 礼物无法修改
		eSet.PresentList = originEmpSet.GetPresentList()
		eSet.CreateTime = originEmpSet.GetCreateTime()

		if eSet.GetShowPresentShelf() {
			// 已被套组关联的帝王套不允许展示礼物架
			setIds, err := m.store.GetSetIdListByEmperorId(ctx, eSet.GetSetId())
			if err != nil {
				log.ErrorWithCtx(ctx, "CreateEmperorSet GetSetIdListByEmperorId err:%s, req:%+v", err, req)
				return resp, err
			}
			if len(setIds) > 0 {
				log.ErrorWithCtx(ctx, "CreateEmperorSet setId:%d has been associated, req:%+v, presentSetId:%d", eSet.GetSetId(), req, setIds)
				return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "关联了礼物套装的帝王套不能配置在礼物架展示")
			}
		}
	}

	// check present
	for i, present := range presentList {
		if i == 0 {
			// 未填排序，按T豆从小到大
			isAutoSort = present.GetRank() == 0
		}
		if present.GetPresentId() == 0 {
			log.ErrorWithCtx(ctx, "CreateEmperorSet present:%d param err, req:%+v", present.GetPresentId(), req)
			return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "礼物配置错误")
		}

		if _, ok := presentIdMap[present.GetPresentId()]; ok {
			log.ErrorWithCtx(ctx, "CreateEmperorSet present:%d duplicate, req:%+v", present.GetPresentId(), req)
			return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("礼物不可重复 ID:%d", present.GetPresentId()))
		}
		presentIdMap[present.GetPresentId()] = struct{}{}

		presentCfg := m.cache.PresentConfigCache().GetPresent(ctx, present.GetPresentId())
		if presentCfg == nil {
			log.ErrorWithCtx(ctx, "CreateEmperorSet present:%d not found, req:%+v", present.GetPresentId(), req)
			return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("礼物不存在 ID:%d", present.GetPresentId()))
		}

		if isAutoSort {
			present.PresentPrice = presentCfg.GetPrice() // 填充价格字段用于排序
		} else {
			if _, ok := presentRankMap[present.GetRank()]; ok {
				log.ErrorWithCtx(ctx, "CreateEmperorSet present:%d rank duplicate, req:%+v", present.GetPresentId(), req)
				return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("排序不可重复 ID:%d", present.GetPresentId()))
			}
			presentRankMap[present.GetRank()] = struct{}{}
		}

		isValid, isSetType := checkEmperorSetPresentSupport(ctx, present.GetPresentId(), presentCfg)
		if !isValid {
			log.ErrorWithCtx(ctx, "CreateEmperorSet present:%d not support, req:%+v", present.GetPresentId(), req)
			return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("礼物类型错误 ID:%d", present.GetPresentId()))
		}
		if isSetType {
			includeSetPresent = true
		}
	}

	if isAutoSort {
		// 按T豆排序
		sort.Slice(presentList, func(i, j int) bool {
			return presentList[i].GetPresentPrice() < presentList[j].GetPresentPrice()
		})
		for i, present := range presentList {
			present.Rank = float64(i + 1)
		}
	} else {
		// 按填写的排序
		sort.Slice(presentList, func(i, j int) bool {
			return presentList[i].GetRank() < presentList[j].GetRank()
		})
	}

	eSet.PresentList = presentList

	// 套组礼物不能展示在礼物架
	if includeSetPresent && eSet.GetShowPresentShelf() {
		log.ErrorWithCtx(ctx, "CreateEmperorSet err: include set present cannot show shelf, req:%+v", req)
		return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "子礼物是套装礼物不能直接在礼物架展示")
	}

	if req.GetIsValidate() {
		return resp, nil
	}

	eSetModel, presentListModel := store.NewEmperorSetConfigFromPb(eSet)
	setId, err := m.store.SaveEmperorSetConfig(ctx, eSetModel, presentListModel)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateEmperorSet SaveEmperorSetConfig err:%s, req:%+v", err, req)
		return resp, err
	}
	resp.SetId = setId
	return resp, nil
}

func checkEmperorSetPresentSupport(ctx context.Context, presentId uint32, presentCfg *userpresent.StPresentItemConfig) (bool, bool) {
	tag := app.PresentTagType(presentCfg.GetExtend().GetTag())
	isSetType := tag == app.PresentTagType_PRESENT_TAG_SET

	if presentCfg.GetPriceType() != uint32(app.PRESENT_PRICE_TYPE_PRESENT_PRICE_TBEAN) {
		log.ErrorWithCtx(ctx, "checkEmperorSetPresentSupport present:%d price type err", presentId)
		return false, isSetType
	}

	switch tag {
	case app.PresentTagType_PRESENT_TAG_FELLOW, // 挚友礼物
		app.PresentTagType_PRESENT_TAG_CUSTOMIZED, // 定制礼物
		app.PresentTagType_PRESENT_TAG_FANS_LEVEL, // 粉丝团礼物
		app.PresentTagType_PRESENT_TAG_NOBILITY,   // 贵族礼物
		app.PresentTagType_PRESENT_TAG_PRIVILEGE:  // 权限礼物
		log.ErrorWithCtx(ctx, "checkEmperorSetPresentSupport present:%d tag type err", presentId)
		return false, isSetType
	}

	// 前置特效 暂时去掉这个检测
	//if presentCfg.GetExtend().GetIsBoxBreaking() {
	//	log.ErrorWithCtx(ctx, "checkEmperorSetPresentSupport present:%d box breaking err", presentId)
	//	return false, isSetType
	//}
	return true, isSetType
}

func (m *Manager) getEmperorSetPbAllListNoCache(ctx context.Context) ([]*pb.EmperorSetConfig, error) {
	resp, err := m.GetEmperorSetListNoCache(ctx, &pb.GetEmperorSetListNoCacheReq{Limit: 999, IncludeDel: true})
	if err != nil {
		log.ErrorWithCtx(ctx, "getEmperorSetPbAllListNoCache GetEmperorSetListNoCache err:%s", err)
		return nil, err
	}
	return resp.GetEmperorSetList(), nil
}

func (m *Manager) GetEmperorSetListNoCache(ctx context.Context, req *pb.GetEmperorSetListNoCacheReq) (*pb.GetEmperorSetListNoCacheResp, error) {
	resp := &pb.GetEmperorSetListNoCacheResp{}
	resp.EmperorSetList = make([]*pb.EmperorSetConfig, 0)

	esList, total, err := m.store.GetEmperorSetConfigList(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEmperorSetListNoCache GetEmperorSetConfigList err:%s, req:%+v", err, req)
		return resp, err
	}
	esIdList := make([]uint32, 0)
	for _, es := range esList {
		esIdList = append(esIdList, es.ID)
	}

	esPresentList, err := m.store.GetEmperorSetPresentItemsByIds(ctx, esIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEmperorSetListNoCache GetEmperorSetPresentItemsByIds err:%s, req:%+v", err, req)
		return resp, err
	}

	esPresentListMap := make(map[uint32][]*pb.EmperorSetPresentItem)
	esPresentTotalPriceMap := make(map[uint32]uint32)
	for _, esPresent := range esPresentList {
		presentPb := esPresent.ToPb()
		presentCfg := m.cache.PresentConfigCache().GetPresent(ctx, esPresent.PresentID)
		if presentCfg == nil {
			log.ErrorWithCtx(ctx, "GetEmperorSetListNoCache present:%d not found, req:%+v", esPresent.PresentID, req)
			continue
		}
		presentPb.PresentName = presentCfg.GetName()
		presentPb.PresentIcon = presentCfg.GetIconUrl()
		presentPb.PresentPrice = presentCfg.GetPrice()
		esPresentListMap[esPresent.SetID] = append(esPresentListMap[esPresent.SetID], presentPb)
		esPresentTotalPriceMap[esPresent.SetID] += presentCfg.GetPrice()
	}

	text, url := m.dyConf.GetSendEmperorSetDefaultInfo()
	pbList := make([]*pb.EmperorSetConfig, 0)
	for _, es := range esList {
		esPb := es.ToPb()
		esPb.PresentsTotalPrice = esPresentTotalPriceMap[es.ID]
		esPb.PresentList = esPresentListMap[es.ID]
		esPb.SendEmperorSetText = text
		esPb.SendEmperorSetFrameUrl = url
		pbList = append(pbList, esPb)
	}

	resp.EmperorSetList = pbList
	resp.Total = total
	return resp, nil
}

func (m *Manager) GetEmperorSetInfoNoCache(ctx context.Context, req *pb.GetEmperorSetInfoReq) (*pb.GetEmperorSetInfoResp, error) {
	resp := &pb.GetEmperorSetInfoResp{}

	eSet, err := m.getEmperorSetConfigById(ctx, req.GetSetId(), req.GetIncludeDel())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEmperorSetInfo getEmperorSetConfigById err:%s, req:%+v", err, req)
		return resp, protocol.NewExactServerError(codes.OK, status.ErrPresentSetEmperorNotFound)
	}

	resp.EmperorSet = eSet
	return resp, nil
}

func (m *Manager) DeleteEmperorSet(ctx context.Context, req *pb.DeleteEmperorSetReq) (*pb.DeleteEmperorSetResp, error) {
	resp := &pb.DeleteEmperorSetResp{}
	if req.GetSetId() == 0 {
		return resp, nil
	}

	log.InfoWithCtx(ctx, "DeleteEmperorSet start req:%+v", req)

	// 标记帝王套删除，同时解除关联该帝王套的礼物套组
	err := m.store.SoftDeleteEmperorSetConfig(ctx, req.GetSetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteEmperorSet SoftDeleteEmperorSetConfig err:%s, req:%+v", err, req)
		return resp, err
	}

	return resp, nil
}

func (m *Manager) GetEmperorSetList(ctx context.Context, req *pb.GetEmperorSetListReq) (*pb.GetEmperorSetListResp, error) {
	_ = req
	resp := &pb.GetEmperorSetListResp{}
	resp.EmperorSetList = make([]*pb.EmperorSetConfig, 0)

	eSetList := m.cache.EmpSetConfigCache().GetEmperorSetList(ctx)
	if len(eSetList) == 0 {
		return resp, nil
	}

	if !req.GetIncludeDel() {
		eSetListTmp := make([]*pb.EmperorSetConfig, 0)
		for _, eSet := range eSetList {
			if !eSet.GetIsDel() {
				eSetListTmp = append(eSetListTmp, eSet)
			}
		}
		eSetList = eSetListTmp
	}

	text, url := m.dyConf.GetSendEmperorSetDefaultInfo()
	for _, eSet := range eSetList {
		presentTotalPrice := uint32(0)
		for _, p := range eSet.GetPresentList() {
			presentCfg := m.cache.PresentConfigCache().GetPresent(ctx, p.GetPresentId())
			if presentCfg == nil {
				log.ErrorWithCtx(ctx, "GetEmperorSetList present:%d not found, eSetId:%d", p.GetPresentId(), eSet.GetSetId())
				return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("礼物不存在 ID:%d", p.GetPresentId()))
			}
			presentTotalPrice += presentCfg.GetPrice()
		}
		eSet.PresentsTotalPrice = presentTotalPrice
		eSet.SendEmperorSetText = text
		eSet.SendEmperorSetFrameUrl = url
	}

	resp.EmperorSetList = eSetList
	return resp, nil
}

func (m *Manager) getEmperorSetConfigById(ctx context.Context, eSetId uint32, includeDel bool) (*pb.EmperorSetConfig, error) {
	eSetCfg, err := m.store.GetEmperorSetConfig(ctx, eSetId, includeDel)
	if err != nil {
		log.ErrorWithCtx(ctx, "getEmperorSetConfigById GetEmperorSetConfig err:%s, eSetId:%d", err, eSetId)
		return nil, err
	}

	if eSetCfg == nil {
		log.ErrorWithCtx(ctx, "getEmperorSetConfigById GetEmperorSetConfig err:not found, eSetId:%d", eSetId)
		return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("帝王套不存在 ID:%d", eSetId))
	}

	eSetPresents, err := m.store.GetEmperorSetPresentItemsByIds(ctx, []uint32{eSetId})
	if err != nil {
		log.ErrorWithCtx(ctx, "getEmperorSetConfigById GetEmperorSetPresentItemsByIds err:%s, eSetId:%d", err, eSetId)
		return nil, err
	}

	eSet := eSetCfg.ToPb()
	presentItems := make([]*pb.EmperorSetPresentItem, 0)
	totalPrice := uint32(0)

	for _, p := range eSetPresents {
		presentCfg := m.cache.PresentConfigCache().GetPresent(ctx, p.PresentID)
		if presentCfg == nil {
			log.ErrorWithCtx(ctx, "getEmperorSetConfigById present:%d not found, eSetId:%d", p.PresentID, eSetId)
			return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("礼物不存在 ID:%d", p.PresentID))
		}
		presentItems = append(presentItems, &pb.EmperorSetPresentItem{
			SetId:        eSetId,
			PresentId:    p.PresentID,
			PresentName:  presentCfg.GetName(),
			PresentIcon:  presentCfg.GetIconUrl(),
			PresentPrice: presentCfg.GetPrice(),
			Rank:         p.Ranking,
		})
		totalPrice += presentCfg.GetPrice()
	}

	eSet.PresentsTotalPrice = totalPrice
	eSet.PresentList = presentItems
	text, url := m.dyConf.GetSendEmperorSetDefaultInfo()
	eSet.SendEmperorSetText = text
	eSet.SendEmperorSetFrameUrl = url
	return eSet, nil
}

func (m *Manager) GetEmperorSetInfo(ctx context.Context, req *pb.GetEmperorSetInfoReq) (*pb.GetEmperorSetInfoResp, error) {
	resp := &pb.GetEmperorSetInfoResp{}
	if req.GetSetId() == 0 {
		return resp, nil
	}

	eSet := m.cache.EmpSetConfigCache().GetEmperorSet(ctx, req.GetSetId())
	if eSet == nil {
		log.ErrorWithCtx(ctx, "GetEmperorSetInfo GetEmperorSet err:not found, eSetId:%d", req.GetSetId())
		return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("帝王套不存在 ID:%d", req.GetSetId()))
	}

	if !req.GetIncludeDel() {
		if eSet.GetIsDel() {
			log.ErrorWithCtx(ctx, "GetEmperorSetInfo GetEmperorSet err:deleted, eSetId:%d", req.GetSetId())
			return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("帝王套不存在 ID:%d", req.GetSetId()))
		}
	}

	resp.EmperorSet = eSet
	return resp, nil
}

// 检查套装礼物与帝王套礼物是否一致
func (m *Manager) checkEmperorPresentConsistency(ctx context.Context, emperorSetId uint32, setPresentList []uint32) error {
	eSet, err := m.getEmperorSetConfigById(ctx, emperorSetId, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkEmperorPresentConsistency getEmperorSetConfigById err:%s, emperorSetId:%d", err, emperorSetId)
		return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("帝王套不存在 ID:%d", emperorSetId))
	}

	if eSet.GetShowPresentShelf() {
		log.ErrorWithCtx(ctx, "checkEmperorPresentConsistency showPresentShelf err, emperorSetId:%d", emperorSetId)
		return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "关联礼帝王套不能在礼物架展示")
	}

	if len(eSet.GetPresentList()) != len(setPresentList) {
		log.ErrorWithCtx(ctx, "checkEmperorPresentConsistency presentList len not equal, emperorSetId:%d", emperorSetId)
		return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "套装礼物与关联帝王套礼物不匹配")
	}

	eSetPresentMap := make(map[uint32]*pb.EmperorSetPresentItem)
	for _, p := range eSet.GetPresentList() {
		eSetPresentMap[p.GetPresentId()] = p
	}

	for _, p := range setPresentList {
		if eSetPresentMap[p] == nil {
			log.ErrorWithCtx(ctx, "checkEmperorPresentConsistency present not found, emperorSetId:%d, presentId:%d", emperorSetId, p)
			return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("套装礼物与关联帝王套礼物不匹配 ID:%d", p))
		}
	}
	return nil
}
