package kafka_produce

import (
	pb "golang.52tt.com/protocol/services/highcontent"
)

type IKafkaProduceClient interface {
	GetTopic() string
	Init(addr []string, clientId string) error
	Produce(topic, key string, value []byte)
	ProduceStream(stream pb.AddStreamItemReq)
	Close()
}


func NewIKafkaProduce(addr []string, clientId, username, password string, saslEnable bool, topic string) IKafkaProduceClient {
	cli := NewKafkaProduce(addr ,clientId,username,password,saslEnable,topic)
	return cli
}
