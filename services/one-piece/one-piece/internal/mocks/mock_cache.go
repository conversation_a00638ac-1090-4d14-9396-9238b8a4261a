// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/one-piece/one-piece/internal/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	one_piece "golang.52tt.com/protocol/services/one-piece"
	cache "golang.52tt.com/services/one-piece/one-piece/internal/cache"
	mysql "golang.52tt.com/services/one-piece/one-piece/internal/mysql"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// AddBingoNotify mocks base method.
func (m *MockICache) AddBingoNotify(arg0 context.Context, arg1 uint32, arg2 *one_piece.WinningRecord, arg3 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBingoNotify", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddBingoNotify indicates an expected call of AddBingoNotify.
func (mr *MockICacheMockRecorder) AddBingoNotify(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBingoNotify", reflect.TypeOf((*MockICache)(nil).AddBingoNotify), arg0, arg1, arg2, arg3)
}

// AddMinuteWindVal mocks base method.
func (m *MockICache) AddMinuteWindVal(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMinuteWindVal", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddMinuteWindVal indicates an expected call of AddMinuteWindVal.
func (mr *MockICacheMockRecorder) AddMinuteWindVal(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMinuteWindVal", reflect.TypeOf((*MockICache)(nil).AddMinuteWindVal), arg0, arg1)
}

// CheckIfFusing mocks base method.
func (m *MockICache) CheckIfFusing(arg0 context.Context) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfFusing", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfFusing indicates an expected call of CheckIfFusing.
func (mr *MockICacheMockRecorder) CheckIfFusing(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfFusing", reflect.TypeOf((*MockICache)(nil).CheckIfFusing), arg0)
}

// CheckIfWarning mocks base method.
func (m *MockICache) CheckIfWarning(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfWarning", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfWarning indicates an expected call of CheckIfWarning.
func (mr *MockICacheMockRecorder) CheckIfWarning(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfWarning", reflect.TypeOf((*MockICache)(nil).CheckIfWarning), arg0, arg1)
}

// ClearMinuteWindVal mocks base method.
func (m *MockICache) ClearMinuteWindVal(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearMinuteWindVal", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearMinuteWindVal indicates an expected call of ClearMinuteWindVal.
func (mr *MockICacheMockRecorder) ClearMinuteWindVal(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearMinuteWindVal", reflect.TypeOf((*MockICache)(nil).ClearMinuteWindVal), arg0)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// DelFusing mocks base method.
func (m *MockICache) DelFusing(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelFusing", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelFusing indicates an expected call of DelFusing.
func (mr *MockICacheMockRecorder) DelFusing(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelFusing", reflect.TypeOf((*MockICache)(nil).DelFusing), arg0)
}

// DelHourProfit mocks base method.
func (m *MockICache) DelHourProfit(arg0 context.Context, arg1 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelHourProfit", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelHourProfit indicates an expected call of DelHourProfit.
func (mr *MockICacheMockRecorder) DelHourProfit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelHourProfit", reflect.TypeOf((*MockICache)(nil).DelHourProfit), arg0, arg1)
}

// DelUserMileage mocks base method.
func (m *MockICache) DelUserMileage(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserMileage", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserMileage indicates an expected call of DelUserMileage.
func (mr *MockICacheMockRecorder) DelUserMileage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserMileage", reflect.TypeOf((*MockICache)(nil).DelUserMileage), arg0, arg1)
}

// DelUserRemainChance mocks base method.
func (m *MockICache) DelUserRemainChance(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserRemainChance", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserRemainChance indicates an expected call of DelUserRemainChance.
func (mr *MockICacheMockRecorder) DelUserRemainChance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserRemainChance", reflect.TypeOf((*MockICache)(nil).DelUserRemainChance), arg0, arg1)
}

// DelWarning mocks base method.
func (m *MockICache) DelWarning(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelWarning", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelWarning indicates an expected call of DelWarning.
func (mr *MockICacheMockRecorder) DelWarning(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelWarning", reflect.TypeOf((*MockICache)(nil).DelWarning), arg0, arg1)
}

// GetAllMinuteWindVal mocks base method.
func (m *MockICache) GetAllMinuteWindVal(arg0 context.Context) (map[string]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllMinuteWindVal", arg0)
	ret0, _ := ret[0].(map[string]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllMinuteWindVal indicates an expected call of GetAllMinuteWindVal.
func (mr *MockICacheMockRecorder) GetAllMinuteWindVal(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllMinuteWindVal", reflect.TypeOf((*MockICache)(nil).GetAllMinuteWindVal), arg0)
}

// GetHistoryProfit mocks base method.
func (m *MockICache) GetHistoryProfit(arg0 context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHistoryProfit", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHistoryProfit indicates an expected call of GetHistoryProfit.
func (mr *MockICacheMockRecorder) GetHistoryProfit(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHistoryProfit", reflect.TypeOf((*MockICache)(nil).GetHistoryProfit), arg0)
}

// GetHourProfit mocks base method.
func (m *MockICache) GetHourProfit(arg0 context.Context, arg1 int) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHourProfit", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHourProfit indicates an expected call of GetHourProfit.
func (mr *MockICacheMockRecorder) GetHourProfit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHourProfit", reflect.TypeOf((*MockICache)(nil).GetHourProfit), arg0, arg1)
}

// GetPriceLimitCfg mocks base method.
func (m *MockICache) GetPriceLimitCfg(arg0 context.Context) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPriceLimitCfg", arg0)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPriceLimitCfg indicates an expected call of GetPriceLimitCfg.
func (mr *MockICacheMockRecorder) GetPriceLimitCfg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPriceLimitCfg", reflect.TypeOf((*MockICache)(nil).GetPriceLimitCfg), arg0)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetUserDailyPrice mocks base method.
func (m *MockICache) GetUserDailyPrice(arg0 context.Context, arg1, arg2, arg3 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDailyPrice", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDailyPrice indicates an expected call of GetUserDailyPrice.
func (mr *MockICacheMockRecorder) GetUserDailyPrice(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDailyPrice", reflect.TypeOf((*MockICache)(nil).GetUserDailyPrice), arg0, arg1, arg2, arg3)
}

// GetUserInvestFlag mocks base method.
func (m *MockICache) GetUserInvestFlag(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInvestFlag", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInvestFlag indicates an expected call of GetUserInvestFlag.
func (mr *MockICacheMockRecorder) GetUserInvestFlag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInvestFlag", reflect.TypeOf((*MockICache)(nil).GetUserInvestFlag), arg0, arg1)
}

// GetUserMileage mocks base method.
func (m *MockICache) GetUserMileage(arg0 context.Context, arg1 uint32) (uint32, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserMileage", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserMileage indicates an expected call of GetUserMileage.
func (mr *MockICacheMockRecorder) GetUserMileage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserMileage", reflect.TypeOf((*MockICache)(nil).GetUserMileage), arg0, arg1)
}

// GetUserRemainChance mocks base method.
func (m *MockICache) GetUserRemainChance(arg0 context.Context, arg1 uint32) (uint32, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRemainChance", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserRemainChance indicates an expected call of GetUserRemainChance.
func (mr *MockICacheMockRecorder) GetUserRemainChance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRemainChance", reflect.TypeOf((*MockICache)(nil).GetUserRemainChance), arg0, arg1)
}

// GetWindInfo mocks base method.
func (m *MockICache) GetWindInfo(arg0 context.Context) (*cache.WindInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWindInfo", arg0)
	ret0, _ := ret[0].(*cache.WindInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWindInfo indicates an expected call of GetWindInfo.
func (mr *MockICacheMockRecorder) GetWindInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWindInfo", reflect.TypeOf((*MockICache)(nil).GetWindInfo), arg0)
}

// GetWinningRecord mocks base method.
func (m *MockICache) GetWinningRecord(arg0 context.Context, arg1, arg2, arg3 uint32) ([]*mysql.AwardRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWinningRecord", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*mysql.AwardRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWinningRecord indicates an expected call of GetWinningRecord.
func (mr *MockICacheMockRecorder) GetWinningRecord(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWinningRecord", reflect.TypeOf((*MockICache)(nil).GetWinningRecord), arg0, arg1, arg2, arg3)
}

// IncrHistoryProfit mocks base method.
func (m *MockICache) IncrHistoryProfit(arg0 context.Context, arg1 int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrHistoryProfit", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrHistoryProfit indicates an expected call of IncrHistoryProfit.
func (mr *MockICacheMockRecorder) IncrHistoryProfit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrHistoryProfit", reflect.TypeOf((*MockICache)(nil).IncrHistoryProfit), arg0, arg1)
}

// IncrHourProfit mocks base method.
func (m *MockICache) IncrHourProfit(arg0 context.Context, arg1 int, arg2 int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrHourProfit", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrHourProfit indicates an expected call of IncrHourProfit.
func (mr *MockICacheMockRecorder) IncrHourProfit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrHourProfit", reflect.TypeOf((*MockICache)(nil).IncrHourProfit), arg0, arg1, arg2)
}

// IncrUserDailyPrice mocks base method.
func (m *MockICache) IncrUserDailyPrice(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrUserDailyPrice", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrUserDailyPrice indicates an expected call of IncrUserDailyPrice.
func (mr *MockICacheMockRecorder) IncrUserDailyPrice(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrUserDailyPrice", reflect.TypeOf((*MockICache)(nil).IncrUserDailyPrice), arg0, arg1, arg2, arg3, arg4)
}

// PopExpireBingoNotify mocks base method.
func (m *MockICache) PopExpireBingoNotify(arg0 context.Context) (*one_piece.WinningRecord, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PopExpireBingoNotify", arg0)
	ret0, _ := ret[0].(*one_piece.WinningRecord)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// PopExpireBingoNotify indicates an expected call of PopExpireBingoNotify.
func (mr *MockICacheMockRecorder) PopExpireBingoNotify(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PopExpireBingoNotify", reflect.TypeOf((*MockICache)(nil).PopExpireBingoNotify), arg0)
}

// RecordWinning mocks base method.
func (m *MockICache) RecordWinning(arg0 context.Context, arg1 uint32, arg2 ...*mysql.AwardRecord) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordWinning", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordWinning indicates an expected call of RecordWinning.
func (mr *MockICacheMockRecorder) RecordWinning(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordWinning", reflect.TypeOf((*MockICache)(nil).RecordWinning), varargs...)
}

// ScriptLoad mocks base method.
func (m *MockICache) ScriptLoad(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScriptLoad", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ScriptLoad indicates an expected call of ScriptLoad.
func (mr *MockICacheMockRecorder) ScriptLoad(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScriptLoad", reflect.TypeOf((*MockICache)(nil).ScriptLoad), arg0)
}

// SetFusing mocks base method.
func (m *MockICache) SetFusing(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFusing", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetFusing indicates an expected call of SetFusing.
func (mr *MockICacheMockRecorder) SetFusing(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFusing", reflect.TypeOf((*MockICache)(nil).SetFusing), arg0)
}

// SetPriceLimitCfg mocks base method.
func (m *MockICache) SetPriceLimitCfg(arg0 context.Context, arg1 map[uint32]uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPriceLimitCfg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPriceLimitCfg indicates an expected call of SetPriceLimitCfg.
func (mr *MockICacheMockRecorder) SetPriceLimitCfg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPriceLimitCfg", reflect.TypeOf((*MockICache)(nil).SetPriceLimitCfg), arg0, arg1)
}

// SetUserInvestFlagWithExpire mocks base method.
func (m *MockICache) SetUserInvestFlagWithExpire(arg0 context.Context, arg1 uint32, arg2 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserInvestFlagWithExpire", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserInvestFlagWithExpire indicates an expected call of SetUserInvestFlagWithExpire.
func (mr *MockICacheMockRecorder) SetUserInvestFlagWithExpire(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserInvestFlagWithExpire", reflect.TypeOf((*MockICache)(nil).SetUserInvestFlagWithExpire), arg0, arg1, arg2)
}

// SetUserMileage mocks base method.
func (m *MockICache) SetUserMileage(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserMileage", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserMileage indicates an expected call of SetUserMileage.
func (mr *MockICacheMockRecorder) SetUserMileage(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserMileage", reflect.TypeOf((*MockICache)(nil).SetUserMileage), arg0, arg1, arg2)
}

// SetUserRemainChance mocks base method.
func (m *MockICache) SetUserRemainChance(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserRemainChance", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserRemainChance indicates an expected call of SetUserRemainChance.
func (mr *MockICacheMockRecorder) SetUserRemainChance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRemainChance", reflect.TypeOf((*MockICache)(nil).SetUserRemainChance), arg0, arg1, arg2)
}

// SetWarning mocks base method.
func (m *MockICache) SetWarning(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWarning", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWarning indicates an expected call of SetWarning.
func (mr *MockICacheMockRecorder) SetWarning(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWarning", reflect.TypeOf((*MockICache)(nil).SetWarning), arg0, arg1)
}

// SetWindInfo mocks base method.
func (m *MockICache) SetWindInfo(arg0 context.Context, arg1 *cache.WindInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWindInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWindInfo indicates an expected call of SetWindInfo.
func (mr *MockICacheMockRecorder) SetWindInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWindInfo", reflect.TypeOf((*MockICache)(nil).SetWindInfo), arg0, arg1)
}

// TrimWinningRecord mocks base method.
func (m *MockICache) TrimWinningRecord(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TrimWinningRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// TrimWinningRecord indicates an expected call of TrimWinningRecord.
func (mr *MockICacheMockRecorder) TrimWinningRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TrimWinningRecord", reflect.TypeOf((*MockICache)(nil).TrimWinningRecord), arg0, arg1, arg2)
}
