package server

import (
	"context"
	"github.com/golang/mock/gomock"
	pb "golang.52tt.com/protocol/services/one-piece"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
	"golang.52tt.com/services/one-piece/one-piece/internal/mocks"
	"reflect"
	"testing"
)

func TestOnePiece_BuyChance(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)
	mockEvent := mocks.NewMockIKafkaProduce(ctl)

	s := &OnePiece{
		mgr:             mockMgr,
		tBeanKfkProduce: mockEvent,
	}

	gomock.InOrder(
		mockMgr.EXPECT().BuyChance(gomock.Any(), gomock.Any()).Return(&pb.BuyChanceResp{}, "", nil),
		mockEvent.EXPECT().ProduceTBeanConsumeEvent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
	)

	t.Run("BuyChance", func(t *testing.T) {
		_, err := s.BuyChance(context.Background(), &pb.BuyChanceReq{Uid: 1, Amount: 1, Fee: 1000})
		if err != nil {
			t.Errorf("BuyChance fail err:%v", err)
		}
	})
}

func TestOnePiece_LotteryDraw(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().LotteryDraw(gomock.Any(), gomock.Any()).Return(&pb.LotteryDrawResp{}, nil),
	)

	t.Run("LotteryDraw", func(t *testing.T) {
		_, err := s.LotteryDraw(context.Background(), &pb.LotteryDrawReq{Uid: 1, ChanceAmount: 1})
		if err != nil {
			t.Errorf("LotteryDraw fail err:%v", err)
		}
	})
}

func TestOnePiece_Notify(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().Callback(gomock.Any(), gomock.Any(), gomock.Any()).Return(UnifiedPayCallback.Op_COMMIT, nil),
	)

	t.Run("Notify", func(t *testing.T) {
		_, err := s.Notify(context.Background(), &UnifiedPayCallback.PayNotify{})
		if err != nil {
			t.Errorf("Notify fail err:%v", err)
		}
	})
}

func TestOnePiece_SetBoxCfg(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().SetBoxCfg(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("SetBoxCfg", func(t *testing.T) {
		_, err := s.SetBoxCfg(context.Background(), &pb.SetBoxCfgReq{})
		if err != nil {
			t.Errorf("SetBoxCfg fail err:%v", err)
		}
	})
}

func TestOnePiece_UpdateBoxCfg(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().UpdateBoxCfg(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("UpdateBoxCfg", func(t *testing.T) {
		_, err := s.UpdateBoxCfg(context.Background(), &pb.UpdateBoxCfgReq{})
		if err != nil {
			t.Errorf("UpdateBoxCfg fail err:%v", err)
		}
	})
}

func TestOnePiece_DelBoxCfg(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().DelBoxCfg(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("DelBoxCfgReq", func(t *testing.T) {
		_, err := s.DelBoxCfg(context.Background(), &pb.DelBoxCfgReq{})
		if err != nil {
			t.Errorf("DelBoxCfgReq fail err:%v", err)
		}
	})
}

func TestOnePiece_GetBoxCfgList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetBoxCfgList(gomock.Any()).Return([]*pb.BoxCfg{}, nil),
	)

	t.Run("GetBoxCfgList", func(t *testing.T) {
		_, err := s.GetBoxCfgList(context.Background(), &pb.GetBoxCfgListReq{})
		if err != nil {
			t.Errorf("GetBoxCfgList fail err:%v", err)
		}
	})
}

func TestOnePiece_SetOnePieceLimitConf(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().SetOnePieceLimitConf(gomock.Any(), gomock.Any()).Return(&pb.SetOnePieceLimitConfResp{}, nil),
	)

	t.Run("SetOnePieceLimitConf", func(t *testing.T) {
		_, err := s.SetOnePieceLimitConf(context.Background(), &pb.SetOnePieceLimitConfReq{})
		if err != nil {
			t.Errorf("SetOnePieceLimitConf fail err:%v", err)
		}
	})
}

func TestOnePiece_GetOnePieceLimitConf(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetOnePieceLimitConf(gomock.Any()).Return(&pb.GetOnePieceLimitConfResp{}, nil),
	)

	t.Run("GetOnePieceLimitConf", func(t *testing.T) {
		_, err := s.GetOnePieceLimitConf(context.Background(), &pb.GetOnePieceLimitConfReq{})
		if err != nil {
			t.Errorf("GetOnePieceLimitConf fail err:%v", err)
		}
	})
}

func TestOnePiece_SetOnePiecePrizePool(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().SetOnePiecePrizePool(gomock.Any(), gomock.Any()).Return(&pb.SetOnePiecePrizePoolResp{}, nil),
	)

	t.Run("SetOnePiecePrizePool", func(t *testing.T) {
		_, err := s.SetOnePiecePrizePool(context.Background(), &pb.SetOnePiecePrizePoolReq{})
		if err != nil {
			t.Errorf("SetOnePiecePrizePool fail err:%v", err)
		}
	})
}

func TestOnePiece_GetOnePiecePrizePool(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetOnePiecePrizePool(gomock.Any(), gomock.Any()).Return(&pb.GetOnePiecePrizePoolResp{}, nil),
	)

	t.Run("GetOnePiecePrizePool", func(t *testing.T) {
		_, err := s.GetOnePiecePrizePool(context.Background(), &pb.GetOnePiecePrizePoolReq{})
		if err != nil {
			t.Errorf("GetOnePiecePrizePool fail err:%v", err)
		}
	})
}

func TestOnePiece_GetAwardTotalCount(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetAwardTotalCount(gomock.Any(), gomock.Any()).Return(&reconcile_v2.CountResp{}, nil),
	)

	t.Run("GetAwardTotalCount", func(t *testing.T) {
		_, err := s.GetAwardTotalCount(context.Background(), &reconcile_v2.TimeRangeReq{})
		if err != nil {
			t.Errorf("GetAwardTotalCount fail err:%v", err)
		}
	})
}

func TestOnePiece_GetConsumeTotalCount(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetConsumeTotalCount(gomock.Any(), gomock.Any()).Return(&reconcile_v2.CountResp{}, nil),
	)

	t.Run("GetConsumeTotalCount", func(t *testing.T) {
		_, err := s.GetConsumeTotalCount(context.Background(), &reconcile_v2.TimeRangeReq{})
		if err != nil {
			t.Errorf("GetConsumeTotalCount fail err:%v", err)
		}
	})
}

func TestOnePiece_GetAwardOrderIds(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetAwardOrderIds(gomock.Any(), gomock.Any()).Return(&reconcile_v2.OrderIdsResp{}, nil),
	)

	t.Run("GetAwardOrderIds", func(t *testing.T) {
		_, err := s.GetAwardOrderIds(context.Background(), &reconcile_v2.TimeRangeReq{})
		if err != nil {
			t.Errorf("GetAwardOrderIds fail err:%v", err)
		}
	})
}

func TestOnePiece_GetConsumeOrderIds(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetConsumeOrderIds(gomock.Any(), gomock.Any()).Return(&reconcile_v2.OrderIdsResp{}, nil),
	)

	t.Run("GetConsumeOrderIds", func(t *testing.T) {
		_, err := s.GetConsumeOrderIds(context.Background(), &reconcile_v2.TimeRangeReq{})
		if err != nil {
			t.Errorf("GetConsumeOrderIds fail err:%v", err)
		}
	})
}

func TestOnePiece_GetOnePieceInfo(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetOnePieceInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&pb.GetOnePieceInfoResp{}, nil),
	)

	t.Run("GetOnePieceInfo", func(t *testing.T) {
		_, err := s.GetOnePieceInfo(context.Background(), &pb.GetOnePieceInfoReq{})
		if err != nil {
			t.Errorf("GetOnePieceInfo fail err:%v", err)
		}
	})
}

func TestOnePiece_GetRecentWinningRecords(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetRecentWinningRecords(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&pb.GetRecentWinningRecordsResp{}, nil),
	)

	t.Run("GetRecentWinningRecords", func(t *testing.T) {
		_, err := s.GetRecentWinningRecords(context.Background(), &pb.GetRecentWinningRecordsReq{})
		if err != nil {
			t.Errorf("GetRecentWinningRecords fail err:%v", err)
		}
	})
}

func TestOnePiece_GetWinningRecords(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)

	s := &OnePiece{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetWinningRecords(gomock.Any(), gomock.Any()).Return(&pb.GetWinningRecordsResp{}, nil),
	)

	t.Run("GetWinningRecords", func(t *testing.T) {
		_, err := s.GetWinningRecords(context.Background(), &pb.GetWinningRecordsReq{})
		if err != nil {
			t.Errorf("GetWinningRecords fail err:%v", err)
		}
	})
}

func TestOnePiece_GetOnePieceExemptValue(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMgr(ctl)
	ctx := context.Background()

	s := &OnePiece{
		mgr: mockMgr,
	}
	type args struct {
		ctx context.Context
		in  *pb.GetOnePieceExemptValueReq
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		want     *pb.GetOnePieceExemptValueResp
		wantErr  bool
	}{
		{
			name: "TestOnePiece_GetOnePieceExemptValue",
			initFunc: func() {
				mockMgr.EXPECT().GetOnePieceExemptVal(ctx, uint32(1)).Return(true, uint32(10), nil)
			},
			args: args{
				ctx: context.Background(),
				in: &pb.GetOnePieceExemptValueReq{
					Uid: 1,
				},
			},
			want: &pb.GetOnePieceExemptValueResp{
				InvestFlag: true,
				Milege:     10,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := s
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := s.GetOnePieceExemptValue(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOnePieceExemptValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOnePieceExemptValue() got = %v, want %v", got, tt.want)
			}
		})
	}
}
