/******** 生成月度剩余表 *******/
package main

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-gomail/gomail"
	"github.com/tealeg/xlsx"
	"golang.52tt.com/clients/backpack"
	userPresent "golang.52tt.com/clients/userpresent"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	backpackPB "golang.52tt.com/protocol/services/backpacksvr"
	opMysql "golang.52tt.com/services/one-piece/one-piece/internal/mysql"
	"golang.52tt.com/services/tt-rev/common/feishu"
	"google.golang.org/grpc"
	"io/ioutil"
	"sort"
	"time"
)

var presentCli *userPresent.Client
var backpackCli *backpack.Client

var pack2Gift = make(map[uint32]*Gift)

type ServiceConfigT struct {
	MysqlReadOnlyConfig *config.MysqlConfig `json:"readonly_mysql"`
	MailsTo             []string            `json:"mails_to"`
	FeiShuUrl           string              `json:"fei_shu_url"`
}

type Gift struct {
	Id    uint32
	Name  string
	Count uint32
	Worth uint32
	Type  string
}

func (sc *ServiceConfigT) Parse(configFile string) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, &sc)
	if err != nil {
		return err
	}

	log.Infof("ServiceConfigT:MysqlConfig:%+v\n", sc.MysqlReadOnlyConfig)
	return
}

func main() {
	sc := &ServiceConfigT{}
	err := sc.Parse("/home/<USER>/one-piece/one-piece.json")
	if err != nil {
		log.Errorf("Parse fail. err:%v", err)
		return
	}

	presentCli = userPresent.NewClient(grpc.WithBlock())
	backpackCli = backpack.NewClient(grpc.WithBlock())

	now := time.Now()
	monthTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	lastMonthTime := monthTime.AddDate(0, -1, 0)

	//dayTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	beginTime, endTime := lastMonthTime, monthTime

	st, err := NewMysql(sc.MysqlReadOnlyConfig)
	if err != nil {
		log.Errorf("NewMysql fail. err:%v", err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Minute)
	defer cancel()

	awardLogList, err := st.GetAwardLog(ctx, beginTime, endTime)
	if err != nil {
		log.Errorf("GetAwardLog fail. err:%v", err)
		return
	}

	// 期初剩余
	beginRemain, err := st.GetMonthRemainTotal(ctx, beginTime.AddDate(0, -1, 0))
	if err != nil {
		log.Errorf("GetMonthRemainTotal fail. err:%v", err)
		return
	}
	// 期末剩余
	endRemain, err := st.GetMonthRemainTotal(ctx, beginTime)
	if err != nil {
		log.Errorf("GetMonthRemainTotal fail. err:%v", err)
		return
	}

	// 创建报表
	file := xlsx.NewFile()

	checkPass, err := genSummarySheet(ctx, file, beginTime, awardLogList, beginRemain, endRemain)
	if err != nil {
		log.Errorf("genSummarySheet fail. err:%v", err)
		return
	}

	err = genAwardSheet(ctx, file, beginTime, awardLogList)
	if err != nil {
		log.Errorf("genAwardSheet fail. err:%v", err)
		return
	}

	filePath := fmt.Sprintf("/home/<USER>/one-piece/航海寻宝汇总数据_%04d%02d.xlsx", beginTime.Year(), beginTime.Month())
	err = file.Save(filePath)
	if err != nil {
		fmt.Printf("file save fail, err: %v\n", err)
		return
	}

	if !checkPass {
		fmt.Printf("genSummarySheet checkFail.")
		FeiShuWarn(sc.FeiShuUrl)
		return
	}

	// send email
	subject := fmt.Sprintf("航海寻宝财务数据 %s-%s", beginTime.Format("2006-01-02 15点"), endTime.Format("2006-01-02 15点"))
	err = SendMail(filePath, subject, sc.MailsTo)
	if err != nil {
		fmt.Printf("Failed to SendMail %v\n", err)
		return
	}

	log.Infof("cost:%v", time.Since(now))
	return
}

func loadGiftInfo(ctx context.Context, packId uint32) (gift *Gift, err error) {
	cfg, err := backpackCli.GetPackageItemCfg(ctx, 0, &backpackPB.GetPackageItemCfgReq{
		BgId: packId,
	})
	if err != nil {
		return nil, err
	}

	// 包裹中只能有一种物品
	if len(cfg.GetItemCfgList()) != 1 {
		log.Errorf("loadGiftInfo fail.  packId:%d, len(items) != 1 ", packId)
		return gift, errors.New("invalid package item type")
	}

	pkgItemCfg := cfg.GetItemCfgList()[0]

	switch backpackPB.PackageItemType(pkgItemCfg.ItemType) {
	case backpackPB.PackageItemType_BACKPACK_PRESENT:
		presentCfg, err := presentCli.GetPresentConfigById(ctx, pkgItemCfg.SourceId)
		if err != nil {
			return nil, err
		}
		giftType := "红钻礼物"
		if presentCfg.GetItemConfig().GetPriceType() == 2 {
			giftType = "T豆礼物"
		}
		gift = &Gift{
			Id:    presentCfg.GetItemConfig().GetItemId(),
			Name:  presentCfg.GetItemConfig().GetName(),
			Count: pkgItemCfg.GetItemCount(),
			Worth: presentCfg.GetItemConfig().GetPrice(),
			Type:  giftType,
		}
	case backpackPB.PackageItemType_BACKPACK_LOTTERY_FRAGMENT:
		itemCfgs, err := backpackCli.GetItemCfg(ctx, 0, &backpackPB.GetItemCfgReq{
			ItemType:         uint32(backpackPB.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
			ItemSourceIdList: []uint32{pkgItemCfg.GetSourceId()},
			GetAll:           false,
		})
		if err != nil {
			return nil, err
		}
		if nil == itemCfgs.ItemCfgList || 1 != len(itemCfgs.ItemCfgList) {
			return nil, errors.New("invalid fragment cfg")
		}

		fragment := backpackPB.LotteryFragmentCfg{}
		err = fragment.Unmarshal(itemCfgs.ItemCfgList[0])
		if err != nil {
			return nil, err
		}
		gift = &Gift{
			Id:    fragment.GetFragmentId(),
			Name:  fragment.GetFragmentName(),
			Count: pkgItemCfg.GetItemCount(),
			Worth: fragment.GetFragmentPrice(),
			Type:  "碎片",
		}
	default:
		log.Errorf("loadGiftInfo fail.  packId:%d, invalid package item type", packId)
		return nil, errors.New("invalid package item type")
	}

	return gift, nil
}

const ChanceFee = 1000

func genSummarySheet(ctx context.Context, file *xlsx.File, t time.Time, awardLogList []*AwardLog, beginRemain, endRemain uint64) (bool, error) {
	checkPass := true
	sheet, err := file.AddSheet("数据汇总")
	if err != nil {
		log.Errorf("genSummarySheet fail to AddSheet. err:%v", err)
		return false, err
	}

	head := sheet.AddRow()

	col, _ := head.AddCell(), head.AddCell()
	col.HMerge = 1
	col.Value = t.Format("2006-01") + " / 单位：T豆"

	row := sheet.AddRow()
	row.AddCell().SetString("期初航海船总剩余价值")
	row.AddCell().SetInt64(int64(beginRemain * ChanceFee))

	saleFee, lotteryFee, awardFee := int64(0), int64(0), int64(0)
	commonLotteryFee, commonAwardFee := int64(0), int64(0)
	seniorLotteryFee, seniorAwardFee := int64(0), int64(0)
	for _, l := range awardLogList {
		giftInfo, ok := pack2Gift[l.PackId]
		if !ok {
			giftInfo, err = loadGiftInfo(ctx, l.PackId)
			if err != nil {
				continue
			}
			pack2Gift[l.PackId] = giftInfo
		}

		switch l.Source {

		case opMysql.AwardSourceBuy:
			saleFee += l.Amount * int64(ChanceFee)

		case opMysql.AwardSourceLotteryCommon:
			awardFee += l.Amount * int64(giftInfo.Worth) * int64(giftInfo.Count)
			lotteryFee += l.Amount * int64(ChanceFee)
			commonAwardFee += l.Amount * int64(giftInfo.Worth) * int64(giftInfo.Count)
			commonLotteryFee += l.Amount * int64(ChanceFee)

		case opMysql.AwardSourceLotterySenior:
			awardFee += l.Amount * int64(giftInfo.Worth) * int64(giftInfo.Count)
			lotteryFee += l.Amount * int64(ChanceFee*10)
			seniorAwardFee += l.Amount * int64(giftInfo.Worth) * int64(giftInfo.Count)
			seniorLotteryFee += l.Amount * int64(ChanceFee*10)
		}
	}

	row = sheet.AddRow()
	row.AddCell().SetString("本月航海寻宝累计总销售额")
	row.AddCell().SetInt64(saleFee)

	row = sheet.AddRow()
	row.AddCell().SetString("本月航海寻宝累计总抽奖额")
	row.AddCell().SetInt64(lotteryFee)

	row = sheet.AddRow()
	row.AddCell().SetString("期末航海船总剩余价值")
	row.AddCell().SetInt64(int64(endRemain * ChanceFee))

	check := int64(beginRemain*ChanceFee) + saleFee - lotteryFee - int64(endRemain*ChanceFee)
	if check != 0 {
		checkPass = false
	}

	row = sheet.AddRow()
	row.AddCell().SetString("check")
	row.AddCell().SetInt64(check)

	row = sheet.AddRow()
	row.AddCell().SetString("本月航海寻宝累计发奖额")
	row.AddCell().SetInt64(awardFee)

	profit := lotteryFee - awardFee
	row = sheet.AddRow()
	row.AddCell().SetString("本月航海寻宝累计利润")
	row.AddCell().SetInt64(profit)

	profitRatio := float64(profit) / float64(lotteryFee)
	row = sheet.AddRow()
	row.AddCell().SetString("本月航海寻宝累计利润率")
	row.AddCell().SetString(fmt.Sprintf("%.4f%%", profitRatio*100))

	row = sheet.AddRow()
	row.AddCell().SetString("普通奖池抽奖额")
	row.AddCell().SetInt64(commonLotteryFee)

	row = sheet.AddRow()
	row.AddCell().SetString("普通奖池出奖额")
	row.AddCell().SetInt64(commonAwardFee)

	profit = commonLotteryFee - commonAwardFee
	row = sheet.AddRow()
	row.AddCell().SetString("普通奖池利润")
	row.AddCell().SetInt64(profit)

	profitRatio = float64(profit) / float64(commonLotteryFee)
	row = sheet.AddRow()
	row.AddCell().SetString("普通奖池利润率")
	row.AddCell().SetString(fmt.Sprintf("%.4f%%", profitRatio*100))

	row = sheet.AddRow()
	row.AddCell().SetString("高级奖池抽奖额")
	row.AddCell().SetInt64(seniorLotteryFee)

	row = sheet.AddRow()
	row.AddCell().SetString("高级奖池出奖额")
	row.AddCell().SetInt64(seniorAwardFee)

	profit = seniorLotteryFee - seniorAwardFee
	row = sheet.AddRow()
	row.AddCell().SetString("高级奖池利润")
	row.AddCell().SetInt64(profit)

	profitRatio = float64(profit) / float64(seniorLotteryFee)
	row = sheet.AddRow()
	row.AddCell().SetString("高级奖池利润率")
	row.AddCell().SetString(fmt.Sprintf("%.4f%%", profitRatio*100))

	return checkPass, nil
}

func FeiShuWarn(url string) {
	lineList := make([][]*feishu.LineMem, 0)
	line := []*feishu.LineMem{
		{Tag: "text", Text: "check 项异常"},
	}
	lineList = append(lineList, line)

	line = []*feishu.LineMem{
		{Tag: "at", UserId: "6788876840648851726"},
	}
	lineList = append(lineList, line)

	feishu.SendFeiShuRichMsg(url, "航海寻宝财务数据对账异常", lineList)
}

func genAwardSheet(ctx context.Context, file *xlsx.File, t time.Time, awardLogList []*AwardLog) error {
	// 排序
	sort.Slice(awardLogList, func(right, left int) bool {
		r, l := awardLogList[right], awardLogList[left]
		if r.Source == l.Source {
			return r.PackId > l.PackId
		} else {
			return r.Source > l.Source
		}
	})

	// 生成中奖数据报表
	name := "中奖数据"
	sheet, err := file.AddSheet(name)
	if err != nil {
		log.Errorf("genAwardSheet fail to AddSheet. err:%v", err)
		return err
	}
	// 添加标题
	var colNames = []string{"月份", "航海模式", "包裹id", "礼物id", "发放奖品类型", "礼物名", "礼物总数量", "中奖礼物总价值（T豆）", "抽奖总次数", "抽奖总消耗汽油价值（T豆）"}

	row := sheet.AddRow()
	row.WriteSlice(&colNames, -1)

	date := t.Format("2006-01")
	for _, l := range awardLogList {
		if l.Source == opMysql.AwardSourceBuy {
			continue
		}

		mode := "普通"
		chanceFee := int64(ChanceFee)
		if l.Source == opMysql.AwardSourceLotterySenior {
			mode = "高级"
			chanceFee = 10 * ChanceFee
		}

		giftInfo, ok := pack2Gift[l.PackId]
		if !ok {
			giftInfo, err = loadGiftInfo(ctx, l.PackId)
			if err != nil {
				continue
			}
			pack2Gift[l.PackId] = giftInfo
		}

		row := sheet.AddRow()
		row.AddCell().SetString(date)
		row.AddCell().SetString(mode)
		row.AddCell().SetInt64(int64(l.PackId))
		row.AddCell().SetInt64(int64(giftInfo.Id))
		row.AddCell().SetString(giftInfo.Type)
		row.AddCell().SetString(giftInfo.Name)
		row.AddCell().SetInt64(int64(giftInfo.Count) * l.Amount)
		row.AddCell().SetInt64(int64(giftInfo.Count) * l.Amount * int64(giftInfo.Worth))
		row.AddCell().SetInt64(l.Amount)
		row.AddCell().SetInt64(l.Amount * chanceFee)
	}

	return nil
}

func SendMail(filePath, subject string, to []string) error {
	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")

	m.SetHeader("To", to...)
	m.SetHeader("Subject", subject)

	m.SetBody("text/html", "见附件")
	m.Attach(filePath) //附件

	d := gomail.NewDialer("mail.52tt.com", 465, "<EMAIL>", "V7.D.sTy@%bDB50t#n.r$A4h+-FnXA")

	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}
