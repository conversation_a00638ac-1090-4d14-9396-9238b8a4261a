package store

import (
	"context"
	"time"

	pb "golang.52tt.com/protocol/services/glory-celebrity"
)

type IStore interface {
	CreateTable(bool)
	GetCelebrityWeekRankList(ctx context.Context, t uint32) ([]*CelebrityWeekRank, error)
	GetCelebrityMyRank(ctx context.Context, t uint32, uid uint32) (*CelebrityWeekRank, error)

	AddCelebrityPalaceInfo(ctx context.Context, info *CelebrityPalaceInfo, orderId string, sendTs, price uint32) error
	//获取列表
	GetCelebrityPalaceList(ctx context.Context, periodsNo uint32, giftId uint32, lastCelebrityId uint32, limit uint32) ([]*pb.CelebrityPalaceInfo, error)
	//获取最新/最强/首次
	GetCelebrityPalacePos(ctx context.Context, periodsNo uint32, giftId uint32, pos int32) (*pb.CelebrityPalaceInfo, error)

	//获取某个ID的信息
	GetCelebrityPalaceById(ctx context.Context, periodsNo uint32, celebrityId uint32) (*CelebrityPalaceInfo, error)
	//获取某个UID的信息
	GetCelebrityPlaceByUid(ctx context.Context, periodsNo uint32, giftId uint32, uid uint32) (*pb.CelebrityPalaceInfo, error)

	//获取未统计的星钻获取记录
	GetCelebrityFragmentRecordNoStat(context.Context, time.Time, uint32) ([]*GloryWorldFragmentReward, error)
	//处理一条记录到名流周榜统计
	AddCelebrityWeekOne(ctx context.Context, r *GloryWorldFragmentReward, rank *CelebrityWeekRank) error
	//更新统计状态
	UpdateCelebrityFragementRecordStat(ctx context.Context, Id uint64) error

	//定时检查过滤参数相关
	GetRankFiltersList(ctx context.Context, lastId int64, limit int, weekNo int, periodsNo uint32) ([]*RankFilter, error)
	UpdateRankFilters(ctx context.Context, weekNo int, periodsNo uint32, info *RankFilter) error

	//更新用户是否签约主播过滤属性
	UpdateIsAnchor(ctx context.Context, uid uint32, isAnchor uint32, t uint32) (uint32, error)
	//更新用户公会相关过滤属性
	UpdateGuildFilter(ctx context.Context, info *RankFilter, t uint32) (uint32, error)
	//公会解散，清除公会相关过滤属性
	ClearGuildFilter(ctx context.Context, guildId uint32, t uint32) (uint32, error)
	//更新黑名单用户的过滤属性
	UpdateBlackUidFilter(ctx context.Context, isWeek bool, uid uint32, isBlack uint32) error

	//星钻获取统计
	StatStarFragment(ctx context.Context, beginTime time.Time, endTime time.Time, fragmentType uint32, statPeriods uint32) (*StarFragmentStat, error)
	GetStarFragmentStat(ctx context.Context, t time.Time, fragmentType uint32, statPeriods uint32) (*StarFragmentStat, error)

	GetCelebrityPlaceGiftOrders(ctx context.Context, beginTime, endTime int64) ([]*CelebrityPalaceGiftStat, error)
}
