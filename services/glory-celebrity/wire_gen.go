// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"context"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/services/glory-celebrity/cache"
	"golang.52tt.com/services/glory-celebrity/conf"
	"golang.52tt.com/services/glory-celebrity/event"
	"golang.52tt.com/services/glory-celebrity/mgr"
	"golang.52tt.com/services/glory-celebrity/rpc"
	"golang.52tt.com/services/glory-celebrity/server"
	"golang.52tt.com/services/glory-celebrity/store"
	"google.golang.org/grpc"
)

import (
	_ "golang.52tt.com/pkg/tracing/opentelemetry"
)

// Injectors from wire.go:

func initServer(contextContext context.Context, grpcServer *grpc.Server, serverConfig *config.ServerConfig) (*App, func(), error) {
	iConfDynamic, err := conf.NewGloryCelerityConf()
	if err != nil {
		return nil, nil, err
	}
	iStore, err := store.NewStore(serverConfig, iConfDynamic)
	if err != nil {
		return nil, nil, err
	}
	iCache := cache.NewCache(serverConfig, iConfDynamic)
	rpcClients, err := rpc.NewRpcClients()
	if err != nil {
		return nil, nil, err
	}
	manager, cleanup := mgr.NewManager(iStore, iCache, iConfDynamic, rpcClients)
	serverServer := server.NewServer(grpcServer, manager, rpcClients)
	eventEvent, cleanup2, err := event.NewEvent(serverConfig, manager)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	app, cleanup3, err := newApp(serverServer, eventEvent)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	return app, func() {
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
