package server

import (
	"context"
	channel_game_mutual "golang.52tt.com/protocol/services/channel-game-mutual"
	"google.golang.org/grpc/codes"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/mapreduce"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	pb "golang.52tt.com/protocol/app/channel-live-logic"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	channellivepkPB "golang.52tt.com/protocol/services/channel-live-pk"
	"golang.52tt.com/protocol/services/channellivefans"
	"golang.52tt.com/protocol/services/channellivemgr"
	channelmemberviprank "golang.52tt.com/protocol/services/channelmemberVipRank"
	NobilityPB "golang.52tt.com/protocol/services/nobilitysvr"
	"golang.52tt.com/protocol/services/numericsvr"
	"golang.52tt.com/services/channel-live-logic/conf"
)

// 直播多人PK
func (s *ChannelLiveLogic_) GetChannelLiveMultiPkPermission(ctx context.Context, in *pb.GetChannelLiveMultiPkPermissionRequest) (
	*pb.GetChannelLiveMultiPkPermissionResponse, error) {
	out := &pb.GetChannelLiveMultiPkPermissionResponse{}

	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkPermission fail ctx:%+v in=%s", ctx, in.String())
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	uid := si.UserID

	resp, err := s.channelLivePkCli.GetLiveMultiPkPer(ctx, &channellivepkPB.GetLiveMultiPkPerReq{
		UidList: []uint32{uid},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkPermission GetLiveMultiPkPer fail %v, uid=%d", err, uid)
		return out, err
	}
	hasPer := resp.GetUidEntryMap()[uid]

	checkResp, err := s.channelLivePkCli.BatchCheckIfInBlacklist(ctx, &channellivepkPB.BatchCheckIfInBlacklistReq{
		UidList:   []uint32{uid},
		BlackType: channellivepkPB.BlacklistType_BLACKLIST_TYPE_MULTI_PK,
	})

	var isInBlack bool
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkPermission BatchCheckIfInBlacklist fail %v, uid=%d", err, uid)
	} else {
		isInBlack = checkResp.GetBlackMap()[uid]
	}
	if hasPer && !isInBlack {
		out.IsShow = true
	}
	log.InfoWithCtx(ctx, "GetChannelLiveMultiPkPermission uid=%d %s out=%v, hasPer:%v, isInBlack:%v", uid, si.String(), out.IsShow, hasPer, isInBlack)
	return out, nil
}

// 搜索
func (s *ChannelLiveLogic_) SerarchPkAnchor(ctx context.Context, in *pb.SerarchMultiPkAnchorRequest) (*pb.SerarchMultiPkAnchorResponse, error) {
	out := &pb.SerarchMultiPkAnchorResponse{
		AnchorInfo: &pb.MultiPkAnchorInfo{},
	}
	si, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := si.UserID

	defer func() {
		log.InfoWithCtx(ctx, "SerarchPkAnchor uid=%d %s in=%s out=%s", si.UserID, si.String(), in.String(), out.String())
	}()

	ttid := in.TargetTtid
	if ttid == "" {
		return out, nil
	}

	targetUid, _, serr := s.accountClient.GetUidByAlias(ctx, ttid)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SerarchPkAnchor GetUidByAlias fail %v, in=%s", serr, in.String())
		if serr.Code() == status.ErrAccountNotExist {
			return out, nil
		}
		return out, serr
	}
	if targetUid == uid {
		log.InfoWithCtx(ctx, "SerarchPkAnchor can not pk self. uid=%d", uid)
		return out, nil
	}
	log.InfoWithCtx(ctx, "SerarchPkAnchor GetUidByAlias %s->%d", ttid, targetUid)

	user, sErr := s.accountClient.GetUserByUid(ctx, targetUid)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "SerarchPkAnchor GetChannelLiveInfo fail %v, targetUid=%d", sErr, targetUid)
		return out, sErr
	}

	liveInfo, sErr := s.channelLiveMgrCli.GetChannelLiveInfo(ctx, targetUid, false)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "SerarchPkAnchor GetChannelLiveInfo fail %v, targetUid=%d", sErr, targetUid)
		return out, sErr
	}
	// 没有直播权限
	if liveInfo.GetChannelLiveInfo().GetChannelId() == 0 {
		log.InfoWithCtx(ctx, "SerarchPkAnchor no live per. targetUid=%d liveInfo=%+v", targetUid, liveInfo)
		return out, nil
	}

	out.AnchorInfo.UserProfile = &app.UserProfile{
		Uid:          targetUid,
		Account:      user.GetUsername(),
		Nickname:     user.GetNickname(),
		AccountAlias: user.GetAlias(),
		Sex:          uint32(user.GetSex()),
	}

	// 查开播状态
	targetCid := liveInfo.GetChannelLiveInfo().GetChannelId()
	liveStatus, sErr := s.channelLiveMgrCli.GetChannelLiveStatus(ctx, channellivemgr.GetChannelLiveStatusReq{Uid: targetUid, ChannelId: targetCid})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "SerarchPkAnchor GetChannelLiveStatus fail %v, targetUid=%d", sErr, targetUid)
		return out, sErr
	}
	log.InfoWithCtx(ctx, "SerarchPkAnchor GetChannelLiveStatus opuid=%d targetUid=%d targetCid=%d liveStatus=%s", uid, targetUid, targetCid, liveStatus.String())
	// 不在开播
	if liveStatus.GetChannelLiveInfo().GetChannelLiveStatus().GetStatus() == (channellivemgr.EnumChannelLiveStatus_CLOSE) {
		out.Status = uint32(pb.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_NOT_LIVE)
		out.AnchorInfo.AnchorStatus = out.Status
		return out, nil
	}

	// 在PK
	if liveStatus.GetChannelLiveInfo().GetChannelLiveStatus().GetPkStatus() != channellivemgr.EnumChannelLivePKStatus_IDLE {
		out.Status = uint32(pb.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_PKING)
		out.AnchorInfo.AnchorStatus = out.Status
		return out, nil
	}

	// 查被邀请人状态
	inviteeStatusResp, err := s.channelLivePkCli.GetLiveMultiPkInviteeStatus(ctx, &channellivepkPB.GetLiveMultiPkInviteeStatusReq{
		ApplyUid:      uid,
		TargetUidList: []uint32{targetUid},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SerarchPkAnchor GetLiveMultiPkInviteeStatus fail %v, targetUid=%d", sErr, targetUid)
		return out, err
	}
	log.InfoWithCtx(ctx, "SerarchPkAnchor GetLiveMultiPkInviteeStatus opuid=%d targetUid=%d inviteeStatusResp=%s", uid, targetUid, inviteeStatusResp.String())
	inviteeStatus := inviteeStatusResp.GetTargetUid2Status()[targetUid]
	out.Status = convertApplyStatus(inviteeStatus)

	if out.Status == uint32(pb.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_CAN_INVITE) {
		// 检查下是否在其他人匹配队伍中
		livePkMatchResp, err := s.channelLivePkCli.BatCheckIsInMultiMatch(ctx, &channellivepkPB.BatCheckIsInMultiMatchReq{UidList: []uint32{targetUid}})
		if err != nil {
			log.ErrorWithCtx(ctx, "SerarchPkAnchor BatCheckIsInMultiMatch fail %v, targetUid=%d", err, targetUid)
			return out, err
		}

		if livePkMatchResp.GetMapIdIsMatch()[targetUid] {
			out.Status = uint32(pb.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_PKING)
		}
	}

	out.AnchorInfo.AnchorStatus = out.Status
	return out, nil
}

func convertApplyStatus(inviteeStatus uint32) uint32 {
	status := uint32(0)
	switch inviteeStatus {
	case uint32(channellivepkPB.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_INVITED): // 已邀请
		{
			status = uint32(pb.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_INVITED)
		}
	case uint32(channellivepkPB.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_ACCEPTED): // 已接受
		{
			status = uint32(pb.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_ACCEPTED)
		}
	case uint32(channellivepkPB.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_REFUSED): // 已拒绝
		{
			status = uint32(pb.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_REFUSED)
		}
	default:
		status = uint32(pb.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_CAN_INVITE) // 可邀请
	}
	return status
}

// 发起邀请
func (s *ChannelLiveLogic_) ApplyChannelLiveMultiPk(ctx context.Context, in *pb.ApplyChannelLiveMultiPkRequest) (
	out *pb.ApplyChannelLiveMultiPkResponse, err error) {
	out = &pb.ApplyChannelLiveMultiPkResponse{}

	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk fail ctx:%+v in=%s", ctx, in.String())
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	uid := si.UserID
	targetUid := in.TargetUid

	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk fail uid=%d targetUid=%d, err=%v", uid, targetUid, err)
		}
	}()

	if targetUid == 0 {
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk uid=%d targetUid=0", uid)
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	if targetUid == uid {
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk invalid uid=%d self targetUid=uid", uid)
		return out, protocol.NewServerError(status.ErrRequestParamInvalid, "不能邀请自己PK")
	}

	blackResp, err := s.channelLivePkCli.BatchCheckIfInBlacklist(ctx, &channellivepkPB.BatchCheckIfInBlacklistReq{
		UidList:   []uint32{uid, targetUid},
		BlackType: channellivepkPB.BlacklistType_BLACKLIST_TYPE_MULTI_PK,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk BatchCheckIfInBlacklist fail %v, uid=%d targetUid=%d", err, uid, targetUid)
		return out, err
	}
	if blackResp.GetBlackMap()[uid] {
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk uid=%d in blacklist", uid)
		return out, protocol.NewExactServerError(codes.OK, status.ErrChannelLiveMultiPkApplyFail, "当前帐号不支持开启PK哦")
	}
	if blackResp.GetBlackMap()[targetUid] {
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk targetUid=%d in blacklist", targetUid)
		return out, protocol.NewExactServerError(codes.OK, status.ErrChannelLiveMultiPkApplyFail, "当前主播暂时不支持参与PK哦")
	}

	liveInfo, err := s.channelLiveMgrCli.GetChannelLiveInfo(ctx, targetUid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk GetChannelLiveInfo fail %v, targetUid=%d", err, targetUid)
		return out, err
	}
	// 没有直播权限
	if liveInfo.GetChannelLiveInfo().GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk no live per. targetUid=%d liveInfo=%+v", targetUid, liveInfo)
		return out, protocol.NewServerError(status.ErrChannelLiveMultiPkApplyFail)
	}

	// 查直播状态
	targetCid := liveInfo.GetChannelLiveInfo().GetChannelId()
	liveStatus, err := s.channelLiveMgrCli.GetChannelLiveStatus(ctx, channellivemgr.GetChannelLiveStatusReq{Uid: targetUid, ChannelId: targetCid})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk GetChannelLiveStatus fail %v, targetUid=%d", err, targetUid)
		return out, err
	}
	log.InfoWithCtx(ctx, "ApplyChannelLiveMultiPk targetUid=%d targetCid=%d GetChannelLiveStatus=%s", targetUid, targetCid, liveStatus.String())
	if liveStatus.GetChannelLiveInfo().GetChannelLiveStatus().GetStatus() == channellivemgr.EnumChannelLiveStatus_CLOSE {
		log.InfoWithCtx(ctx, "ApplyChannelLiveMultiPk targetUid=%d not live. GetChannelLiveStatus=%s", targetUid, liveStatus.String())
		return out, protocol.NewServerError(status.ErrChannelLiveMultiPkApplyFail)
	}
	// 在pk中不能邀请
	if liveStatus.GetChannelLiveInfo().GetChannelLiveStatus().GetPkStatus() != channellivemgr.EnumChannelLivePKStatus_IDLE {
		log.InfoWithCtx(ctx, "ApplyChannelLiveMultiPk targetUid=%d in pk. GetChannelLiveStatus=%s", targetUid, liveStatus.String())
		return out, protocol.NewServerError(status.ErrChannelLiveMultiPkApplyFail)
	}

	// 检查下是否在其他人匹配队伍中
	livePkMatchResp, err := s.channelLivePkCli.BatCheckIsInMultiMatch(ctx, &channellivepkPB.BatCheckIsInMultiMatchReq{UidList: []uint32{targetUid}})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk BatCheckIsInMultiMatch fail %v, targetUid=%d", err, targetUid)
		return out, err
	}
	log.InfoWithCtx(ctx, "ApplyChannelLiveMultiPk targetUid=%d BatCheckIsInMultiMatch=%s", targetUid, livePkMatchResp.String())
	if livePkMatchResp.GetMapIdIsMatch()[targetUid] {
		return out, protocol.NewServerError(status.ErrChannelLiveMultiPkApplyFail)
	}

	// 多人pk权限
	resp, err := s.channelLivePkCli.GetLiveMultiPkPer(ctx, &channellivepkPB.GetLiveMultiPkPerReq{
		UidList: []uint32{targetUid},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk GetLiveMultiPkPer fail %v, uid=%d targetUid=%d", err, uid, targetUid)
		return out, err
	}
	log.InfoWithCtx(ctx, "ApplyChannelLiveMultiPk uid=%d targetUid=%d GetLiveMultiPkPer=%v", uid, targetUid, resp.GetUidEntryMap())
	if !resp.GetUidEntryMap()[targetUid] {
		err = protocol.NewServerError(status.ErrChannelLiveMultiPkApplyNoPkPermission)
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk GetLiveMultiPkPer check fail %v, uid=%d targetUid=%d", err, uid, targetUid)
		return out, err
	}

	inviteeStatusResp, err := s.channelLivePkCli.GetLiveMultiPkInviteeStatus(ctx, &channellivepkPB.GetLiveMultiPkInviteeStatusReq{
		ApplyUid:      uid,
		TargetUidList: []uint32{targetUid},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk GetLiveMultiPkInviteeStatus fail %v, targetUid=%d", err, targetUid)
		return out, err
	}
	log.InfoWithCtx(ctx, "ApplyChannelLiveMultiPk GetLiveMultiPkInviteeStatus opuid=%d targetUid=%d inviteeStatusResp=%s", uid, targetUid, inviteeStatusResp.String())
	inviteeStatus := inviteeStatusResp.GetTargetUid2Status()[targetUid]
	if inviteeStatus == uint32(channellivepkPB.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_ACCEPTED) ||
		inviteeStatus == uint32(channellivepkPB.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_REFUSED) {
		log.InfoWithCtx(ctx, "ApplyChannelLiveMultiPk GetLiveMultiPkInviteeStatus inviteeStatus opuid=%d targetUid=%d inviteeStatusResp=%s", uid, targetUid, inviteeStatusResp.String())
		return out, protocol.NewServerError(status.ErrChannelLiveMultiPkApplyFail)
	}
	// 通过玩法注册中心，判断对方是否能开启4人pk
	checkResp, err := s.channelGameMutualCli.CanOpenGame(ctx, &channel_game_mutual.CanOpenGameRequest{
		ChannelId: liveInfo.GetChannelLiveInfo().GetChannelId(),
		GameId:    uint64(channel_game_mutual.GameType_GAME_TYPE_MUL_PK),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk CanOpenGame fail %v, targetUid: %d, cid: %d", err, targetUid, liveInfo.GetChannelLiveInfo().GetChannelId())
	} else if !checkResp.GetResult() {
		log.InfoWithCtx(ctx, "ApplyChannelLiveMultiPk CanOpenGame not pass resp: %+v, targetUid: %d, cid: %d", checkResp, targetUid, liveInfo.GetChannelLiveInfo().GetChannelId())
		return out, protocol.NewServerError(status.ErrChannelLiveMultiPkApplyFail, "对方暂不方便接受四人PK")
	}

	_, err = s.channelLivePkCli.ApplyLiveMultiPk(ctx, &channellivepkPB.ApplyLiveMultiPkReq{
		ApplyUid:  uid,
		TargetUid: targetUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyChannelLiveMultiPk ApplyLiveMultiPk fail %v, uid=%d targetUid=%d", err, uid, targetUid)
		return out, err
	}

	log.InfoWithCtx(ctx, "ApplyChannelLiveMultiPk done uid=%d %s targetUid=%d", uid, si.String(), targetUid)
	return out, nil
}

func (s *ChannelLiveLogic_) MatchMultiPk(ctx context.Context, in *pb.MatchMultiPkRequest) (*pb.MatchMultiPkResponse, error) {
	out := &pb.MatchMultiPkResponse{}

	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "MatchMultiPk fail ctx:%+v in=%s", ctx, in.String())
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	uid := si.UserID

	_, err := s.channelLivePkCli.StartMultiPkMatch(ctx, &channellivepkPB.StartMultiPkMatchReq{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "MatchMultiPk StartMultiPkMatch fail %v, uid=%d", err, uid)
		return out, err
	}

	log.InfoWithCtx(ctx, "MatchMultiPk uid=%d %s", uid, si.String())
	return out, nil
}

func (s *ChannelLiveLogic_) CancelMatchMultiPk(ctx context.Context, in *pb.CancelMatchMultiPkRequest) (*pb.CancelMatchMultiPkResponse, error) {
	out := &pb.CancelMatchMultiPkResponse{}

	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "CancelMatchMultiPk fail ctx:%+v in=%s", ctx, in.String())
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	uid := si.UserID

	_, err := s.channelLivePkCli.CancelMultiPkMatch(ctx, &channellivepkPB.CancelMultiPkMatchReq{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelMatchMultiPk CancelMultiPkMatch fail %v, uid=%d", err, uid)
		return out, err
	}

	log.InfoWithCtx(ctx, "CancelMatchMultiPk uid=%d %s", uid, si.String())
	return out, nil
}

func (s *ChannelLiveLogic_) AcceptChannelLiveMultiPk(ctx context.Context, in *pb.AcceptChannelLiveMultiPkRequest) (out *pb.AcceptChannelLiveMultiPkResponse, err error) {
	out = &pb.AcceptChannelLiveMultiPkResponse{}

	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "AcceptChannelLiveMultiPk fail ctx:%+v in=%s", ctx, in.String())
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}

	uid := si.UserID
	applyUid := in.ApplyUid
	oper := in.Oper
	cid := uint32(0)

	blackResp, err := s.channelLivePkCli.BatchCheckIfInBlacklist(ctx, &channellivepkPB.BatchCheckIfInBlacklistReq{
		UidList:   []uint32{uid},
		BlackType: channellivepkPB.BlacklistType_BLACKLIST_TYPE_MULTI_PK,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptChannelLiveMultiPk BatchCheckIfInBlacklist fail %v, uid=%d", err, uid)
		return out, err
	}
	if blackResp.GetBlackMap()[uid] {
		log.ErrorWithCtx(ctx, "AcceptChannelLiveMultiPk uid=%d in blacklist", uid)
		return out, protocol.NewExactServerError(codes.OK, status.ErrChannelLiveMultiPkApplyFail, "当前帐号不支持开启PK哦")
	}

	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "AcceptChannelLiveMultiPk fail %v, uid=%d applyUid=%d", err, uid, applyUid)
		}
	}()

	if applyUid == uid {
		log.ErrorWithCtx(ctx, "AcceptChannelLiveMultiPk invalid uid=%d self applyUid=uid", uid)
		return out, protocol.NewServerError(status.ErrRequestParamInvalid, "不能邀请自己PK")
	}

	if applyUid == 0 {
		log.ErrorWithCtx(ctx, "AcceptChannelLiveMultiPk invalid applyUid uid=%d", uid)
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}

	if oper == uint32(pb.HandleApplyMultiPkType_HANDLE_APPLY_MULTI_PK_TYPE_ACCEPT) {
		liveInfo, err := s.channelLiveMgrCli.GetChannelLiveInfo(ctx, uid, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "AcceptChannelLiveMultiPk GetChannelLiveInfo fail %v, uid=%d", err, uid)
			return out, err
		}
		cid = liveInfo.GetChannelLiveInfo().GetChannelId()
		liveStatus, err := s.channelLiveMgrCli.GetChannelLiveStatus(ctx, channellivemgr.GetChannelLiveStatusReq{Uid: uid, ChannelId: cid})
		if err != nil {
			log.ErrorWithCtx(ctx, "AcceptChannelLiveMultiPk GetChannelLiveStatus fail %v, cid=%d", err, cid)
			return out, err
		}
		log.InfoWithCtx(ctx, "AcceptChannelLiveMultiPk uid=%d cid=%d GetChannelLiveStatus=%s", uid, cid, liveStatus.String())
		if liveStatus.GetChannelLiveInfo().GetChannelLiveStatus().GetPkMatchState() != channellivemgr.EnumPkMatch_PKM_Match_Close {
			log.InfoWithCtx(ctx, "AcceptChannelLiveMultiPk uid=%d in pk. GetChannelLiveStatus=%s", uid, liveStatus.String())
			return out, protocol.NewServerError(status.ErrChannelLiveMultiPkApplyFail, "加入失败")
		}
		// 尝试注册到玩法互斥中心，如果注册不上，则证明有其他互斥的玩法在进行中
		resp, gameRegErr := s.channelGameMutualCli.Register(ctx, &channel_game_mutual.RegisterRequest{
			ChannelId: cid,
			GameId:    uint64(channel_game_mutual.GameType_GAME_TYPE_MUL_PK),
		})
		if gameRegErr != nil {
			log.ErrorWithCtx(ctx, "AcceptChannelLiveMultiPk invoke Register fail %v, uid=%d", gameRegErr, uid)
			return out, protocol.NewServerError(status.ErrChannelLiveMultiPkApplyFail, "加入失败")
		}
		if !resp.GetResult() {
			// 如果注册失败，则返回具体的提示
			log.ErrorWithCtx(ctx, "AcceptChannelLiveMultiPk Register fail %v, uid=%d", gameRegErr, uid)
			return out, protocol.NewServerError(status.ErrChannelLiveMultiPkApplyFail, "当前暂不可以进行四人PK")
		}
	}

	_, err = s.channelLivePkCli.HandleLiveMultiPkApply(ctx, &channellivepkPB.HandleLiveMultiPkApplyReq{
		Uid:      uid,
		ApplyUid: applyUid,
		Oper:     oper,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptChannelLiveMultiPk HandleLiveMultiPkApply fail %v, uid=%d applyUid=%d", err, uid, applyUid)
		// 如果下游服务发生了错误，则要注销玩法中心的记录
		go func(cid uint32) {
			if _, err := s.channelGameMutualCli.UnRegister(ctx, &channel_game_mutual.UnregisterRequest{
				ChannelId: cid,
				GameId:    uint64(channel_game_mutual.GameType_GAME_TYPE_MUL_PK),
			}); err != nil {
				log.ErrorWithCtx(ctx, "AcceptChannelLiveMultiPk UnRegister fail %v, uid=%d", err, uid)
			}
		}(cid)
		return out, err
	}

	log.InfoWithCtx(ctx, "AcceptChannelLiveMultiPk uid=%d %s ApplyUid=%d Oper=%d", uid, si.String(), applyUid, oper)
	return out, nil
}

func (s *ChannelLiveLogic_) StartChannelLiveMultiPk(ctx context.Context, in *pb.StartChannelLiveMultiPkRequest) (*pb.StartChannelLiveMultiPkResponse, error) {
	out := &pb.StartChannelLiveMultiPkResponse{}

	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "StartChannelLiveMultiPk fail ctx:%+v in=%s", ctx, in.String())
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	uid := si.UserID

	if in.PkType != uint32(pb.MultiPkType_MULTI_PK_TYPE_SINGLE) &&
		in.PkType != uint32(pb.MultiPkType_MULTI_PK_TYPE_TEAM) {
		log.ErrorWithCtx(ctx, "StartChannelLiveMultiPk fail. pkType invalid. uid=%d in=%s", uid, in.String())
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	if len(in.TeamList) != 4 {
		log.ErrorWithCtx(ctx, "StartChannelLiveMultiPk fail. RoomInfoList invalid. uid=%d in=%s", uid, in.String())
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	for i := 0; i < 4; i++ {
		if in.TeamList[i].Uid == 0 {
			log.ErrorWithCtx(ctx, "StartChannelLiveMultiPk fail. TeamList.uid invalid. uid=%d in=%s", uid, in.String())
			return out, protocol.NewServerError(status.ErrRequestParamInvalid)
		}
	}

	req := &channellivepkPB.StartLiveMultiPkReq{
		Uid:    uid,
		PkId:   in.PkId,
		PkType: in.PkType,
	}
	for _, info := range in.TeamList {
		req.TeamList = append(req.TeamList, &channellivepkPB.MultiPkTeamSimpleInfo{
			TeamId: info.TeamId,
			Uid:    info.Uid,
		})
	}
	_, err := s.channelLivePkCli.StartLiveMultiPk(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartChannelLiveMultiPk StartLiveMultiPk fail %v, uid=%d", err, uid)
		return out, err
	}

	log.InfoWithCtx(ctx, "StartChannelLiveMultiPk uid=%d %s in=%s", uid, si.String(), in.String())
	return out, nil
}

func (s *ChannelLiveLogic_) GetChannelLiveMultiPkRank(ctx context.Context, in *pb.GetChannelLiveMultiPkRankRequest) (*pb.GetChannelLiveMultiPkRankResponse, error) {
	out := &pb.GetChannelLiveMultiPkRankResponse{}
	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkRank fail ctx:%+v in=%s", ctx, in.String())
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	uid := si.UserID
	anchorUid := in.AnchorUid

	rankResp, err := s.channelLivePkCli.GetLiveMultiPkRank(ctx, &channellivepkPB.GetLiveMultiPkRankReq{
		Uid:       uid,
		AnchorUid: anchorUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkRank GetLiveMultiPkRank fail %v, uid=%d anchorUid=%d", err, uid, anchorUid)
		return out, err
	}
	log.DebugWithCtx(ctx, "GetChannelLiveMultiPkRank anchorUid=%d GetLiveMultiPkRank=%+v", anchorUid, rankResp.GetRankList())
	if len(rankResp.GetRankList()) == 0 {
		return out, nil
	}

	liveResp, err := s.channelLiveMgrCli.GetChannelLiveInfo(ctx, anchorUid, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkRank GetChannelLiveInfo fail %v, uid=%d", err, uid)
		return out, err
	}
	cid := liveResp.GetChannelLiveInfo().GetChannelId()
	if cid == 0 {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkRank no liveInfo, anchorUid=%d", anchorUid)
		return out, nil
	}

	uidList := make([]uint32, 0, len(rankResp.GetRankList()))
	for _, rank := range rankResp.GetRankList() {
		uidList = append(uidList, rank.Uid)
	}

	subCtx, cancel := context.WithTimeout(ctx, time.Millisecond*800)
	defer cancel()

	mapUser, fansResp, nobilityInfoMap, memberVipMap, mapNumeric, err2 := s.getMultiPkUserExtraInfo(subCtx, anchorUid, cid, uidList)
	if err2 != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkRank getMultiPkUserExtraInfo fail %v, uid=%d", err2, uid)
		return out, err2
	}

	for _, rank := range rankResp.GetRankList() {
		user, ok := mapUser[rank.Uid]
		if !ok {
			continue
		}
		_, _, _, userProfile, ukwInfo := getPkUserAccountInfoFromUidInfo(user, rank.GetUkwInfo())

		var rich, charm uint32 = 0, 0
		if num, ok := mapNumeric[rank.Uid]; ok && ukwInfo.GetLevel() == 0 {
			rich = uint32(int(num.Rich64 / 1000))
			charm = uint32(int(num.Charm64 / 1000))
		}

		var pinfo *app.FansPlateInfo
		groupName := ""
		var fansLv uint32 = 0

		fan, ok := fansResp[rank.Uid]
		if ok && ukwInfo.GetLevel() == 0 {
			groupName = fan.GetGroupName()
			plateInfo := fan.GetPlateInfo()
			fansLv = fan.GetFansLevel()
			pinfo = s.FillPlateConfigMsg(plateInfo)
		}

		nobilityLv := nobilityInfoMap[rank.Uid].GetLevel()
		memLv := memberVipMap[rank.Uid].GetCurrLevelId()

		rankInfo := &pb.MultiPkUserInfo{
			UserProfile:     userProfile,
			PkScore:         rank.GetPkSocre(),
			KnightLevel:     rank.GetKnightLevel(),
			RichLevel:       rich,
			Charm:           charm,
			NobilityLevel:   nobilityLv,
			GroupFansLevel:  fansLv,
			ChannelMemLevel: memLv,
			GroupName:       groupName,
			PlateInfo:       pinfo,
			FirstKill:       rank.GetFirstKill(),
			NobilityInfo: &app.NobilityInfo{
				Level:  nobilityInfoMap[rank.Uid].GetLevel(),
				FLevel: nobilityInfoMap[rank.Uid].GetFLevel(),
			},
		}

		out.RankList = append(out.RankList, rankInfo)

		log.InfoWithCtx(ctx, "GetChannelLiveMultiPkRank uid=%d, userProfile=%+v ukwInfo=%+v rankInfo=%s",
			rank.Uid, userProfile, ukwInfo, utils.ToJson(rankInfo))
	}

	log.InfoWithCtx(ctx, "GetChannelLiveMultiPkRank end anchorUid=%d out=%s", anchorUid, utils.ToJson(out))
	return out, nil
}

func (s *ChannelLiveLogic_) GetChannelLiveMultiPkKnightList(ctx context.Context, in *pb.GetChannelLiveMultiPkKnightListRequest) (*pb.GetChannelLiveMultiPkKnightListResponse, error) {
	out := &pb.GetChannelLiveMultiPkKnightListResponse{}
	si, _ := protogrpc.ServiceInfoFromContext(ctx)
	anchorUid := in.AnchorUid
	resp, serr := s.channelLivePkCli.GetOnlineKnightList(ctx, &channellivepkPB.GetOnlineKnightListReq{
		AnchorUid: anchorUid,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkKnightList GetOnlineKnightList fail %v, anchorUid=%d", serr, anchorUid)
		return out, serr
	}
	log.InfoWithCtx(ctx, "GetChannelLiveMultiPkKnightList opUid=%d %s, anchorUid=%d GetOnlineKnightList=%+v",
		si.UserID, si.String(), anchorUid, resp.GetKnightList())
	if len(resp.GetKnightList()) == 0 {
		return out, nil
	}
	liveResp, serr := s.channelLiveMgrCli.GetChannelLiveInfo(ctx, anchorUid, true)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkKnightList GetChannelLiveInfo fail %v, uid=%d", serr, anchorUid)
		return out, serr
	}
	cid := liveResp.GetChannelLiveInfo().GetChannelId()
	if cid == 0 {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkKnightList no liveInfo, anchorUid=%d", anchorUid)
		return out, nil
	}

	uids := make([]uint32, 0, len(resp.GetKnightList()))
	for _, info := range resp.GetKnightList() {
		uids = append(uids, info.Uid)
	}

	subCtx, cancel := context.WithTimeout(ctx, time.Millisecond*800)
	defer cancel()
	mapUser, fansResp, nobilityInfoMap, memberVipMap, mapNumeric, err := s.getMultiPkUserExtraInfo(subCtx, anchorUid, cid, uids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkRank getMultiPkUserExtraInfo fail %v, uid=%d", err, anchorUid)
		return out, err
	}

	for _, rank := range resp.GetKnightList() {

		user, ok := mapUser[rank.Uid]
		if !ok {
			continue
		}
		_, _, _, userProfile, ukwInfo := getPkUserAccountInfoFromUidInfo(user, rank.GetUkwInfo())

		var rich, charm uint32 = 0, 0
		if num, ok := mapNumeric[rank.Uid]; ok && ukwInfo.GetLevel() == 0 {
			rich = uint32(int(num.Rich64 / 1000))
			charm = uint32(int(num.Charm64 / 1000))
		}

		var pinfo *app.FansPlateInfo
		groupName := ""
		var fansLv uint32 = 0

		fan, ok := fansResp[rank.Uid]
		if ok && ukwInfo.GetLevel() == 0 {
			groupName = fan.GetGroupName()
			plateInfo := fan.GetPlateInfo()
			fansLv = fan.GetFansLevel()
			pinfo = s.FillPlateConfigMsg(plateInfo)
		}

		nobilityLv := nobilityInfoMap[rank.Uid].GetLevel()
		memLv := memberVipMap[rank.Uid].GetCurrLevelId()

		rankInfo := &pb.MultiPkUserInfo{
			UserProfile:     userProfile,
			PkScore:         rank.GetPkSocre(),
			KnightLevel:     rank.GetKnightLevel(),
			RichLevel:       rich,
			Charm:           charm,
			NobilityLevel:   nobilityLv,
			GroupFansLevel:  fansLv,
			ChannelMemLevel: memLv,
			GroupName:       groupName,
			PlateInfo:       pinfo,
			FirstKill:       rank.GetFirstKill(),
			NobilityInfo: &app.NobilityInfo{
				Level:  nobilityInfoMap[rank.Uid].GetLevel(),
				FLevel: nobilityInfoMap[rank.Uid].GetFLevel(),
			},
		}
		out.KnightList = append(out.KnightList, rankInfo)

		log.InfoWithCtx(ctx, "GetChannelLiveMultiPkKnightList uid=%d userProfile=%+v ukwInfo=%+v rankInfo=%s",
			rank.Uid, userProfile, ukwInfo, utils.ToJson(rankInfo))
	}

	log.InfoWithCtx(ctx, "GetChannelLiveMultiPkKnightList anchorUid=%d, out=%s", anchorUid, utils.ToJson(out))
	return out, nil
}

func (s *ChannelLiveLogic_) GetChannelLiveMultiPkRecordList(ctx context.Context, in *pb.GetChannelLiveMultiPkRecordListRequest) (*pb.GetChannelLiveMultiPkRecordListResponse, error) {
	out := &pb.GetChannelLiveMultiPkRecordListResponse{}

	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkRecordList fail ctx:%+v in=%s", ctx, in.String())
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	uid := si.UserID

	resp, err := s.channelLivePkCli.GetLiveMultiPkRecordList(ctx, &channellivepkPB.GetLiveMultiPkRecordListReq{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkRecordList GetLiveMultiPkRecordList fail %v, uid=%d", err, uid)
		return out, err
	}
	log.DebugWithCtx(ctx, "GetChannelLiveMultiPkRecordList uid=%d GetLiveMultiPkRecordList size=%d list=%+v", uid, len(resp.GetUidList()), resp)
	if len(resp.GetUidList()) == 0 {
		return out, nil
	}
	anchorUidList := resp.GetUidList()
	if uint32(len(resp.GetUidList())) > conf.MulitPKRecordMaxCnt {
		anchorUidList = resp.GetUidList()[:conf.MulitPKRecordMaxCnt]
	}

	mapUser, err := s.accountClient.GetUsersMap(ctx, anchorUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkRecordList GetUsersMap anchorUid=%d, err:%v", uid, err)
		return out, err
	}

	// 查直播权限
	batGetChannelLiveInfoResp, err := s.channelLiveMgrCli.BatGetChannelLiveInfo(ctx, &channellivemgr.BatGetChannelLiveInfoReq{
		UidList: anchorUidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkRecordList BatGetChannelLiveInfo anchorUid=%d, err:%v", uid, err)
		return out, err
	}
	uid2LivePer := map[uint32]bool{}
	for _, info := range batGetChannelLiveInfoResp.GetInfoList() {
		if info.GetChannelId() > 0 && info.GetEndTime() > uint32(time.Now().Unix()) {
			uid2LivePer[info.GetUid()] = true
		}
	}

	// 查直播状态
	liveStatsResp, err := s.channelLiveMgrCli.BatchGetChannelLiveStatus(ctx, channellivemgr.BatchGetChannelLiveStatusReq{UidList: anchorUidList})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkRecordList BatchGetChannelLiveStatus anchorUid=%d, err:%v", uid, err)
		return out, err
	}
	uid2LiveStatus := map[uint32]*channellivemgr.ChannelLiveStatus{}
	for _, info := range liveStatsResp.GetChannelLiveInfoList() {
		uid2LiveStatus[info.GetChannelLiveStatus().GetUid()] = info.GetChannelLiveStatus()
	}
	log.InfoWithCtx(ctx, "GetChannelLiveMultiPkRecordList BatchGetChannelLiveStatus uid=%d liveStatsResp=%s", uid, liveStatsResp.String())

	// 查被邀请人状态
	inviteeStatusResp, err := s.channelLivePkCli.GetLiveMultiPkInviteeStatus(ctx, &channellivepkPB.GetLiveMultiPkInviteeStatusReq{
		ApplyUid:      uid,
		TargetUidList: anchorUidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkRecordList GetLiveMultiPkInviteeStatus fail %v, uid=%d", err, uid)
		return out, err
	}
	uid2inviteeStatus := inviteeStatusResp.GetTargetUid2Status()

	// 检查下是否在其他人匹配队伍中
	livePkMatchResp, err := s.channelLivePkCli.BatCheckIsInMultiMatch(ctx, &channellivepkPB.BatCheckIsInMultiMatchReq{UidList: anchorUidList})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveMultiPkRecordList BatCheckIsInMultiMatch fail %v, uid=%d", err, uid)
		return out, err
	}

	log.InfoWithCtx(ctx, "GetChannelLiveMultiPkRecordList GetLiveMultiPkInviteeStatus uid=%d inviteeStatusResp=%s livePkMatchResp:%v",
		uid, inviteeStatusResp.String(), livePkMatchResp)

	for _, anchorUid := range anchorUidList {
		userInfo, ok := mapUser[anchorUid]
		if !ok {
			continue
		}

		status := uint32(0)
		liveStatus := uid2LiveStatus[anchorUid]
		inviteeStatus := uid2inviteeStatus[anchorUid]

		// 没有直播权限
		if !uid2LivePer[anchorUid] {
			log.InfoWithCtx(ctx, "GetChannelLiveMultiPkRecordList no live per. anchorUid=%d", anchorUid)
			status = uint32(pb.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_NOT_LIVE)
			//continue
		}

		// 不在开播
		if liveStatus.GetStatus() == (channellivemgr.EnumChannelLiveStatus_CLOSE) {
			status = uint32(pb.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_NOT_LIVE)
			log.DebugWithCtx(ctx, "GetChannelLiveMultiPkRecordList uid=%d anchorUid=%d not live", uid, anchorUid)
		} else {
			// 在pk
			if liveStatus.GetPkStatus() != channellivemgr.EnumChannelLivePKStatus_IDLE {
				status = uint32(pb.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_PKING)
				log.DebugWithCtx(ctx, "GetChannelLiveMultiPkRecordList uid=%d anchorUid=%d in pk", uid, anchorUid)
			}
		}

		if status == 0 {
			status = convertApplyStatus(inviteeStatus)
			if status == uint32(pb.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_CAN_INVITE) && livePkMatchResp.GetMapIdIsMatch()[anchorUid] {
				// 在匹配中也算pk中
				status = uint32(pb.ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_PKING)
			}
			log.DebugWithCtx(ctx, "GetChannelLiveMultiPkRecordList uid=%d anchorUid=%d apply. status=%d", uid, anchorUid, status)
		}

		anchorInfo := &pb.MultiPkAnchorInfo{
			UserProfile: &app.UserProfile{
				Uid:          anchorUid,
				Account:      userInfo.GetUsername(),
				Nickname:     userInfo.GetNickname(),
				AccountAlias: userInfo.GetAlias(),
				Sex:          uint32(userInfo.GetSex()),
			},
			AnchorStatus: status,
			LiveStatus:   uint32(liveStatus.GetStatus()),
		}
		out.AnchorList = append(out.AnchorList, anchorInfo)

		log.InfoWithCtx(ctx, "GetChannelLiveMultiPkRecordList uid=%d liveStatus=%d inviteeStatus=%d status=%d append=%s",
			uid, liveStatus, inviteeStatus, status, anchorInfo.String())

		if len(out.AnchorList) >= 20 {
			break
		}
	}

	log.InfoWithCtx(ctx, "GetChannelLiveMultiPkRecordList uid=%d out.size=%d", uid, len(out.AnchorList))
	return out, nil
}

func (s *ChannelLiveLogic_) CancelChannelLiveMultiPkTeam(ctx context.Context, in *pb.CancelChannelLiveMultiPkTeamRequest) (*pb.CancelChannelLiveMultiPkTeamResponse, error) {
	out := &pb.CancelChannelLiveMultiPkTeamResponse{}

	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "CancelChannelLiveMultiPkTeam fail ctx:%+v in=%s", ctx, in.String())
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	uid := si.UserID

	_, err := s.channelLivePkCli.CancelLiveMultiPkTeam(ctx, &channellivepkPB.CancelLiveMultiPkTeamReq{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelLiveMultiPkTeam DisinviteChannelLiveMultiPk fail %v, uid=%d", err, uid)
		return out, err
	}

	// 注销玩法中心的记录
	go func() {
		timeOutCtx, cancel := context.WithTimeout(context.Background(), time.Second*3)
		defer cancel()
		// 获取用户当前所在的房间
		cid, serverErr := s.channelOlCli.GetUserChannelId(timeOutCtx, uid, uid)
		if serverErr != nil {
			log.ErrorWithCtx(timeOutCtx, "CancelChannelLiveMultiPkTeam GetUserChannelId failed uid=%d err=%v", uid, err)
			return
		}
		_, err := s.channelGameMutualCli.UnRegister(timeOutCtx, &channel_game_mutual.UnregisterRequest{
			ChannelId: cid,
			GameId:    uint64(channel_game_mutual.GameType_GAME_TYPE_MUL_PK),
		})
		if err != nil {
			log.ErrorWithCtx(timeOutCtx, "CancelChannelLiveMultiPkTeam Unregister failed cid: %d, uid: %d, err:%v", cid, uid, err)
		}
	}()

	log.InfoWithCtx(ctx, "CancelChannelLiveMultiPkTeam uid=%d %s", uid, si.String())
	return out, nil
}

func (s *ChannelLiveLogic_) StopChannelLiveMultiPk(ctx context.Context, in *pb.StopChannelLiveMultiPkRequest) (*pb.StopChannelLiveMultiPkResponse, error) {
	out := &pb.StopChannelLiveMultiPkResponse{}
	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "StopChannelLiveMultiPk fail ctx:%+v in=%s", ctx, in.String())
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	uid := si.UserID

	_, err := s.channelLivePkCli.StopLiveMultiPk(ctx, &channellivepkPB.StopLiveMultiPkReq{
		Uid:  uid,
		PkId: in.PkId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "StopChannelLiveMultiPk DisinviteChannelLiveMultiPk fail %v, uid=%d", err, uid)
		return out, err
	}

	log.InfoWithCtx(ctx, "StopChannelLiveMultiPk uid=%d pk_id=%d %s", uid, in.PkId, si.String())
	return out, nil
}

func (s *ChannelLiveLogic_) DisinviteChannelLiveMultiPk(ctx context.Context, in *pb.DisinviteChannelLiveMultiPkRequest) (*pb.DisinviteChannelLiveMultiPkResponse, error) {
	out := &pb.DisinviteChannelLiveMultiPkResponse{}

	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "DisinviteChannelLiveMultiPk fail ctx:%+v in=%s", ctx, in.String())
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	uid := si.UserID

	if in.TargetUid == 0 {
		log.ErrorWithCtx(ctx, "DisinviteChannelLiveMultiPk invalid TargetUid uid=%d", uid)
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	if in.TargetUid == uid {
		log.ErrorWithCtx(ctx, "DisinviteChannelLiveMultiPk invalid uid=%d self targetUid=uid", uid)
		return out, protocol.NewServerError(status.ErrRequestParamInvalid, "不能邀请自己PK")
	}

	_, err := s.channelLivePkCli.DisinviteChannelLiveMultiPk(ctx, &channellivepkPB.DisinviteChannelLiveMultiPkReq{
		ApplyUid:  uid,
		TargetUid: in.TargetUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "DisinviteChannelLiveMultiPk DisinviteChannelLiveMultiPk fail %v, uid=%d", err, uid)
		return out, err
	}

	log.InfoWithCtx(ctx, "DisinviteChannelLiveMultiPk uid=%d TargetUid=%d %s", uid, in.TargetUid, si.String())
	return out, nil
}

func (s *ChannelLiveLogic_) InitChannelLiveMultiPkTeam(ctx context.Context, in *pb.InitChannelLiveMultiPkTeamRequest) (*pb.InitChannelLiveMultiPkTeamResponse, error) {
	out := &pb.InitChannelLiveMultiPkTeamResponse{}

	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "IniLiveMultiPkTeam fail ctx:%+v in=%s", ctx, in.String())
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	uid := si.UserID

	// 查询当前的用户在哪个房间
	cid, serverError := s.channelOlCli.GetUserChannelId(ctx, uid, uid)
	if serverError != nil {
		log.ErrorWithCtx(ctx, "InitChannelLiveMultiPkTeam GetUserChannelId fail %v, uid=%d", serverError, uid)
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}
	// 尝试注册到玩法互斥中心，如果注册不上，则证明有其他互斥的玩法在进行中
	resp, gameRegErr := s.channelGameMutualCli.Register(ctx, &channel_game_mutual.RegisterRequest{
		ChannelId: cid,
		GameId:    uint64(channel_game_mutual.GameType_GAME_TYPE_MUL_PK),
	})
	if gameRegErr != nil {
		log.ErrorWithCtx(ctx, "IniLiveMultiPkTeam invoke Register fail %v, uid=%d", gameRegErr, uid)
		return out, protocol.NewServerError(status.ErrChannelLiveMultiPkApplyFail, "加入失败")
	}
	if !resp.GetResult() {
		// 如果注册失败，则返回具体的提示
		log.ErrorWithCtx(ctx, "IniLiveMultiPkTeam Register fail %v, uid=%d", gameRegErr, uid)
		return out, protocol.NewServerError(status.ErrChannelLiveMultiPkApplyFail, "当前正在蒙面PK，不可进行四人PK")
	}

	_, err := s.channelLivePkCli.IniLiveMultiPkTeam(ctx, &channellivepkPB.IniLiveMultiPkTeamReq{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "IniLiveMultiPkTeam DisinviteChannelLiveMultiPk fail %v, uid=%d", err, uid)
		// 如果下游服务发生了错误，则要注销玩法中心的记录
		go func(cid uint32) {
			if _, err := s.channelGameMutualCli.UnRegister(ctx, &channel_game_mutual.UnregisterRequest{
				ChannelId: cid,
				GameId:    uint64(channel_game_mutual.GameType_GAME_TYPE_MUL_PK),
			}); err != nil {
				log.ErrorWithCtx(ctx, "IniLiveMultiPkTeam UnRegister fail %v, uid=%d", err, uid)
			}
		}(cid)
		return out, err
	}

	log.InfoWithCtx(ctx, "IniLiveMultiPkTeam uid=%d %s", uid, si.String())
	return out, nil
}

// 废弃
func (s *ChannelLiveLogic_) GetChannelLiveMultiPkTeamInfo(ctx context.Context, in *pb.GetChannelLiveMultiPkTeamInfoRequest) (*pb.GetChannelLiveMultiPkTeamInfoResponse, error) {
	out := &pb.GetChannelLiveMultiPkTeamInfoResponse{}
	return out, nil
}

func genUserProfile(userInfo *account.User, ukwInfo *channellivepkPB.UkwInfo) *app.UserProfile {
	userProfile := &app.UserProfile{
		Uid:          userInfo.Uid,
		Account:      userInfo.GetUsername(),
		Nickname:     userInfo.GetNickname(),
		AccountAlias: userInfo.GetAlias(),
		Sex:          uint32(userInfo.GetSex()),
	}
	if ukwInfo.GetLevel() == 0 {
		return userProfile
	}
	userProfile.Uid = ukwInfo.GetFakeUid()
	userProfile.Account = ukwInfo.GetAccount()
	userProfile.Nickname = ukwInfo.GetNickname()

	userUkwInfo := &app.UserUKWInfo{
		Level:     ukwInfo.GetLevel(),
		Medal:     ukwInfo.GetMedal(),
		HeadFrame: ukwInfo.GetHeadFrame(),
	}
	byteUkwInfo, _ := proto.Marshal(userUkwInfo)

	userProfile.Privilege = &app.UserPrivilege{
		Account:  ukwInfo.GetAccount(),
		Nickname: ukwInfo.GetNickname(),
		Type:     uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW),
		Options:  byteUkwInfo,
	}
	return userProfile
}

func getPkUserAccountInfoFromUidInfo(user *account.User, ukwInfo *channellivepkPB.UkwInfo) (
	uint32, string, string, *app.UserProfile, *app.UserUKWInfo) {
	log.Debugf("getPkUserAccountInfoFromUidInfo begin userInfo:%v ukwInfo:%v", user, ukwInfo)

	uid, account, nickName := user.GetUid(), user.GetUsername(), user.GetNickname()
	if ukwInfo.GetLevel() > 0 {
		account = ukwInfo.GetAccount()
		nickName = ukwInfo.GetNickname()
		uid = ukwInfo.GetFakeUid()
	}

	userProfile := &app.UserProfile{
		Uid:          uid,
		Account:      account,
		Nickname:     nickName,
		AccountAlias: user.GetAlias(),
		Sex:          uint32(user.GetSex()),
	}
	userUkwInfo := &app.UserUKWInfo{}

	if ukwInfo.GetLevel() > 0 {
		userUkwInfo.Level = ukwInfo.GetLevel()
		userUkwInfo.Medal = ukwInfo.GetMedal()
		userUkwInfo.HeadFrame = ukwInfo.GetHeadFrame()

		byteUkwInfo, _ := proto.Marshal(userUkwInfo)

		userProfile.Privilege = &app.UserPrivilege{
			Account:  ukwInfo.GetAccount(),
			Nickname: ukwInfo.GetNickname(),
			Type:     uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW),
			Options:  byteUkwInfo,
		}
	}

	log.Debugf("getPkUserAccountInfoFromUidInfo end user=%s ukwInfo=%s userProfile=%s", user.String(), ukwInfo.String(), userProfile.String())
	return uid, account, nickName, userProfile, userUkwInfo
}

func (s *ChannelLiveLogic_) getMultiPkUserExtraInfo(ctx context.Context, anchorUid, cid uint32, uidList []uint32) (
	mapUser map[uint32]*accountPB.UserResp,
	fansResp map[uint32]*channellivefans.FansInfo,
	nobilityInfoMap map[uint32]*NobilityPB.NobilityInfo,
	memberVipMap map[uint32]*channelmemberviprank.ChannelMemberVip,
	mapNumeric map[uint32]*numericsvr.PersonalNumeric,
	err error,
) {

	mapUser = map[uint32]*accountPB.UserResp{}
	fansResp = map[uint32]*channellivefans.FansInfo{}
	nobilityInfoMap = map[uint32]*NobilityPB.NobilityInfo{}
	memberVipMap = map[uint32]*channelmemberviprank.ChannelMemberVip{}
	mapNumeric = map[uint32]*numericsvr.PersonalNumeric{}

	fillWiths := []func() error{
		func() error {
			mapUser, err = s.GetUsersMap(ctx, uidList)
			if err != nil {
				log.ErrorWithCtx(ctx, "getMultiPkUserExtraInfo GetUsersMap anchorUid=%d, cid=%d err:%v", anchorUid, cid, err)
				return err
			}
			return nil
		},
		func() error {
			fansResp, err = s.BatchGetFansInfo(ctx, anchorUid, uidList)
			if err != nil {
				log.ErrorWithCtx(ctx, "getMultiPkUserExtraInfo BatchGetFansInfo failed anchorUid=%d, cid=%d err:%v", anchorUid, cid, err)
			}
			log.DebugWithCtx(ctx, "getMultiPkUserExtraInfo anchorUid=%d BatchGetFansInfo=%+v", anchorUid, fansResp)
			return nil
		},
		func() error {
			// 贵族信息
			nobilityInfoMap, err = s.BatchGetNobilityInfo(ctx, cid, uidList)
			if err != nil {
				log.ErrorWithCtx(ctx, "getMultiPkUserExtraInfo BatchGetNobilityInfo failed anchorUid=%d, cid=%d err:%v", anchorUid, cid, err)
			}
			log.DebugWithCtx(ctx, "getMultiPkUserExtraInfo anchorUid=%d BatchGetNobilityInfo=%+v", anchorUid, nobilityInfoMap)
			return nil
		},
		func() error {
			//房间成员vip信息
			memberVipMap, err = s.BatGetUserConsumeInfo(ctx, cid, uidList)
			if nil != err {
				log.ErrorWithCtx(ctx, "getMultiPkUserExtraInfo BatGetUserConsumeInfo failed anchorUid=%d, cid=%d err:%v", anchorUid, cid, err)
			}
			log.DebugWithCtx(ctx, "getMultiPkUserExtraInfo anchorUid=%d BatGetUserConsumeInfo=%+v", anchorUid, memberVipMap)
			return nil
		},
		func() error {
			mapNumeric, err = s.BatchGetPersonalNumeric(ctx, uidList)
			if err != nil {
				log.ErrorWithCtx(ctx, "getMultiPkUserExtraInfo BatchGetPersonalNumeric failed anchorUid=%d, cid=%d err:%v", anchorUid, cid, err)
			}
			log.DebugWithCtx(ctx, "getMultiPkUserExtraInfo anchorUid=%d BatchGetPersonalNumeric=%+v", anchorUid, mapNumeric)
			return nil
		},
	}
	err = mapreduce.Finish(fillWiths...)
	if err != nil {
		log.ErrorWithCtx(ctx, "getMultiPkUserExtraInfo mapreduce fail err:%v, anchorUid=%d", err, anchorUid)
		return
	}
	return
}

// =======================================================================================

func (s *ChannelLiveLogic_) GetUsersMap(ctx context.Context, uidList []uint32) (map[uint32]*accountPB.UserResp, error) {
	usermp := map[uint32]*accountPB.UserResp{}
	limit := 100
	for i := 0; i < len(uidList); i += limit {
		end := i + limit
		if end > len(uidList) {
			end = len(uidList)
		}

		uidsx := uidList[i:end]
		log.Debugf("GetUsersMap %d-%d uids=%v", i, end, uidsx)
		if len(uidsx) == 0 {
			break
		}

		mapUser, err := s.accountClient.GetUsersMap(ctx, uidsx)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUsersMap err:%v", err)
			return nil, err
		}
		for _, user := range mapUser {
			usermp[user.GetUid()] = user
		}
	}
	return usermp, nil
}

func (s *ChannelLiveLogic_) BatchGetFansInfo(ctx context.Context, anchorUid uint32, uidList []uint32) (map[uint32]*channellivefans.FansInfo, error) {
	fansResp := map[uint32]*channellivefans.FansInfo{}
	limit := 100
	for i := 0; i < len(uidList); i += limit {
		end := i + limit
		if end > len(uidList) {
			end = len(uidList)
		}

		uidsx := uidList[i:end]
		log.Debugf("BatchGetFansInfo %d-%d uids=%v", i, end, uidsx)
		if len(uidsx) == 0 {
			break
		}

		resp, err := s.fansCli.BatchGetFansInfo(ctx, anchorUid, uidsx)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetFansInfo failed anchorUid=%d, err:%v", anchorUid, err)
			return nil, err
		}
		for _, user := range resp {
			fansResp[user.GetUid()] = user
		}
	}
	return fansResp, nil
}

func (s *ChannelLiveLogic_) BatchGetNobilityInfo(ctx context.Context, cid uint32, uidList []uint32) (map[uint32]*NobilityPB.NobilityInfo, error) {
	nobilityInfoMap := map[uint32]*NobilityPB.NobilityInfo{}
	limit := 100
	for i := 0; i < len(uidList); i += limit {
		end := i + limit
		if end > len(uidList) {
			end = len(uidList)
		}

		uidsx := uidList[i:end]
		log.Debugf("BatchGetNobilityInfo %d-%d uids=%v", i, end, uidsx)
		if len(uidsx) == 0 {
			break
		}

		resp, err := s.nobilityClient.BatchGetNobilityInfo(ctx, cid, uidsx)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetNobilityInfo failed  cid=%d err:%v", cid, err)
			return nil, err
		}
		for _, user := range resp {
			nobilityInfoMap[user.GetUid()] = user
		}
	}
	return nobilityInfoMap, nil
}

func (s *ChannelLiveLogic_) BatGetUserConsumeInfo(ctx context.Context, cid uint32, uidList []uint32) (map[uint32]*channelmemberviprank.ChannelMemberVip, error) {
	memberVipMap := map[uint32]*channelmemberviprank.ChannelMemberVip{}
	limit := 100
	for i := 0; i < len(uidList); i += limit {
		end := i + limit
		if end > len(uidList) {
			end = len(uidList)
		}

		uidsx := uidList[i:end]
		log.Debugf("BatGetUserConsumeInfo %d-%d uids=%v", i, end, uidsx)
		if len(uidsx) == 0 {
			break
		}

		resp, err := s.memberRank.BatGetUserConsumeInfo(ctx, 0, cid, uidsx)
		if nil != err {
			log.ErrorWithCtx(ctx, "BatGetUserConsumeInfo failed cid=%d err:%v", cid, err)
			return nil, err
		}
		for uid, user := range resp {
			memberVipMap[uid] = user
		}
	}
	return memberVipMap, nil
}

func (s *ChannelLiveLogic_) BatchGetPersonalNumeric(ctx context.Context, uidList []uint32) (map[uint32]*numericsvr.PersonalNumeric, error) {
	mapNumeric := map[uint32]*numericsvr.PersonalNumeric{}
	limit := 100
	for i := 0; i < len(uidList); i += limit {
		end := i + limit
		if end > len(uidList) {
			end = len(uidList)
		}

		uidsx := uidList[i:end]
		log.Debugf("BatchGetPersonalNumeric %d-%d uids=%v", i, end, uidsx)
		if len(uidsx) == 0 {
			break
		}

		resp, err := s.numericClient.BatchGetPersonalNumeric(ctx, uidsx)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetPersonalNumeric failed  err:%v", err)
		}
		for uid, user := range resp {
			mapNumeric[uid] = user
		}
	}
	return mapNumeric, nil
}
