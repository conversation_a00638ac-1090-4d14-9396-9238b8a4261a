package numberpool

import (
	"context"
	"fmt"
	"github.com/alicebob/miniredis/v2"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/store/redis"
	mutex "golang.52tt.com/services/you-know-who/cache/redis-mutex"
	"strconv"
	"testing"
	"time"
)

func getMockCache(t *testing.T) (*miniredis.Miniredis, *redis.Client) {
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	return s, rdb
}

func TestStore_SetNumbers(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()

	// 准备数据
	now := time.Now()

	type fields struct {
		redisClient *redis.Client
	}
	type args struct {
		ctx      context.Context
		nowTime  time.Time
		poolName string
		numbers  []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:      ctx,
				nowTime:  now,
				poolName: LEVEL_NORMAL,
				numbers:  []uint32{12345, 23456, 34567, 45678, 56789},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				redisClient: tt.fields.redisClient,
			}
			if err := s.SetNumbers(tt.args.ctx, tt.args.nowTime, tt.args.poolName, tt.args.numbers); (err != nil) != tt.wantErr {
				t.Errorf("SetNumbers() error = %v, wantErr %v", err, tt.wantErr)
			}
			key := fmt.Sprintf("%s_%s_%s", RedisNumberPoolKeyPrefix, tt.args.poolName, tt.args.nowTime.Format("20060102"))
			t.Log(key)
			infos, err := s.redisClient.LRange(key, 0, -1).Result()
			if err != nil {
				t.Errorf("SetNumbers() error = %v, wantErr %v", err, tt.wantErr)
			}
			t.Log(infos)
		})
	}
}

func TestStore_GetNumber(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()

	poolName := "normal"
	now := time.Now()
	key := fmt.Sprintf("%s_%s_%s", RedisNumberPoolKeyPrefix, poolName, now.Format("20060102"))
	_, err := s.Lpush(key, "10086")
	if err != nil {
		t.Fatal(err)
	}

	type fields struct {
		redisClient     *redis.Client
		distributedLock *mutex.DistributedLock
	}
	type args struct {
		ctx      context.Context
		nowTime  time.Time
		poolName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "numberpool测试从redis获取号码",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:      ctx,
				nowTime:  now,
				poolName: poolName,
			},
			want:    10086,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				redisClient:     tt.fields.redisClient,
				distributedLock: tt.fields.distributedLock,
			}
			got, err := s.GetNumber(tt.args.ctx, tt.args.nowTime, tt.args.poolName)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNumber() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetNumber() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_GetNumberLenfunc(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()

	poolName := "normal"
	now := time.Now()
	key := fmt.Sprintf("%s_%s_%s", RedisNumberPoolKeyPrefix, poolName, now.Format("20060102"))
	_, err := s.Lpush(key, "10086")
	if err != nil {
		t.Fatal(err)
	}

	type fields struct {
		redisClient     *redis.Client
		distributedLock *mutex.DistributedLock
	}
	type args struct {
		ctx      context.Context
		nowTime  time.Time
		poolName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "numberpool获取redis号码池剩余号码数量",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:      ctx,
				nowTime:  now,
				poolName: poolName,
			},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				redisClient:     tt.fields.redisClient,
				distributedLock: tt.fields.distributedLock,
			}
			got, err := s.GetNumberLen(tt.args.ctx, tt.args.nowTime, tt.args.poolName)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNumberLen() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetNumberLen() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_DelNumberList(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()

	poolName := "normal"
	now := time.Now()
	key := fmt.Sprintf("%s_%s_%s", RedisNumberPoolKeyPrefix, poolName, now.Format("20060102"))
	_, err := s.Lpush(key, "10086")
	if err != nil {
		t.Fatal(err)
	}

	type fields struct {
		redisClient     *redis.Client
		distributedLock *mutex.DistributedLock
	}
	type args struct {
		ctx      context.Context
		delTime  time.Time
		poolName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "numberpool删除号码池",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:      ctx,
				delTime:  now,
				poolName: poolName,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				redisClient:     tt.fields.redisClient,
				distributedLock: tt.fields.distributedLock,
			}
			if err := s.DelNumberList(tt.args.ctx, tt.args.delTime, tt.args.poolName); (err != nil) != tt.wantErr {
				t.Errorf("DelNumberList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_SetLockWithTime(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()
	now := time.Now()

	type fields struct {
		redisClient     *redis.Client
		distributedLock *mutex.DistributedLock
	}
	type args struct {
		ctx          context.Context
		key          string
		finishedTime int64
		timeout      time.Duration
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "numberpool设置redis锁",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:          ctx,
				key:          "测试",
				finishedTime: now.Unix(),
				timeout:      time.Minute,
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				redisClient:     tt.fields.redisClient,
				distributedLock: tt.fields.distributedLock,
			}
			got, err := s.SetLockWithTime(tt.args.ctx, tt.args.key, tt.args.finishedTime, tt.args.timeout)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetLockWithTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("SetLockWithTime() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_GetLockWithKey(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()
	now := time.Now()

	key := "测试"
	timeStr := now.String()
	err := s.Set(key, timeStr)
	if err != nil {
		t.Fatal(err)
	}

	type fields struct {
		redisClient     *redis.Client
		distributedLock *mutex.DistributedLock
	}
	type args struct {
		ctx context.Context
		key string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "numberpool测试Redis获取锁",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				key: key,
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				redisClient:     tt.fields.redisClient,
				distributedLock: tt.fields.distributedLock,
			}
			got, err := s.GetLockWithKey(tt.args.ctx, tt.args.key)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLockWithKey() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetLockWithKey() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_GetExtraNum(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()
	now := time.Now()

	type fields struct {
		redisClient     *redis.Client
		distributedLock *mutex.DistributedLock
	}
	type args struct {
		ctx context.Context
		t   time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "numberpool获取扩充号码池号码",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				t:   now,
			},
			wantErr: false,
			want:    1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				redisClient:     tt.fields.redisClient,
				distributedLock: tt.fields.distributedLock,
			}
			got, err := s.GetExtraNum(tt.args.ctx, tt.args.t)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExtraNum() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetExtraNum() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_DelExtraNum(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, rdb := getMockCache(t)
	defer s.Close()
	now := time.Now()

	type fields struct {
		redisClient     *redis.Client
		distributedLock *mutex.DistributedLock
	}
	type args struct {
		ctx context.Context
		t   time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "numberpool测试Redis去掉额外号码池",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				t:   now,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				redisClient:     tt.fields.redisClient,
				distributedLock: tt.fields.distributedLock,
			}
			if err := s.DelExtraNum(tt.args.ctx, tt.args.t); (err != nil) != tt.wantErr {
				t.Errorf("DelExtraNum() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
