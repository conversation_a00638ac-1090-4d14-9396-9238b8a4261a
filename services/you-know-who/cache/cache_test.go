package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"sync"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/opentracing/opentracing-go"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/store/redis"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	"golang.52tt.com/services/you-know-who/conf"
	//"testing"
)

var (
	sc          *conf.ServiceConfigT
	cacheClient *YouKnowWhoCache
)

func init() {
	sc := &conf.ServiceConfigT{}
	err := sc.Parse("../you-know-who.json")
	if err != nil {
		return
	}

	redisClient := redis.NewClient(sc.GetRedisConfig())
	redisTracer := tracing.Init("you-know-who_redis")
	cacheClient, _ = NewYouKnowWhoCache(redisClient, redisTracer)
}

/*func TestKnockExist(t *testing.T) {
	var knockUid uint32 = 1
	var followUid uint32 = 3
	cacheClient.IsOneUserExist(knockUid, followUid, sc.GetKnockInfo().ImTimeOut)
}*/

func TestYouKnowWhoCache_UpdateUKWStatusCacheInfo(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.HSet("You-Know-Who:status", "2208646", "test")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	info := &UKWCachePermissionInfo{
		Uid:           2208646,
		FakeUid:       23456,
		Nickname:      "神秘人5922",
		Switch:        1,
		Status:        1,
		EffectiveTime: 2592000,
		RankSwitch:    0,
	}

	type fields struct {
		redisClient         *redis.Client
		tracer              opentracing.Tracer
		localCache          []*sync.Map
		localCacheReadIndex uint8
	}
	type args struct {
		ctx  context.Context
		info *UKWCachePermissionInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:  ctx,
				info: info,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:         tt.fields.redisClient,
				tracer:              tt.fields.tracer,
				localCache:          tt.fields.localCache,
				localCacheReadIndex: tt.fields.localCacheReadIndex,
			}
			if err := y.UpdateUKWStatusCacheInfo(tt.args.ctx, tt.args.info); (err != nil) != tt.wantErr {
				t.Errorf("UpdateUKWStatusCacheInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
			result, _ := y.redisClient.HGet("You-Know-Who:status", "2208646").Result()
			statusInfo := new(UKWCachePermissionInfo)
			err = json.Unmarshal([]byte(result), statusInfo)
			if err != nil {
				t.Errorf("GetUKWStatusCacheInfo Unmarshal failed, err:[%+v]", err)
			}
			if !reflect.DeepEqual(statusInfo, info) {
				t.Errorf("BatchUpdateUKWStatusCacheInfo() update failed. ")
			}
		})
	}
}

func TestYouKnowWhoCache_GetAllUKWStatusCacheInfos(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	info := &UKWCachePermissionInfo{
		Uid:           2208646,
		FakeUid:       23456,
		Nickname:      "神秘人59221",
		Switch:        1,
		Status:        1,
		EffectiveTime: 2592000,
		RankSwitch:    0,
	}
	jsonStr, _ := json.Marshal(info)
	s.HSet("You-Know-Who:status", "2208646", string(jsonStr))
	info = &UKWCachePermissionInfo{
		Uid:           2208647,
		FakeUid:       23457,
		Nickname:      "神秘人59235",
		Switch:        1,
		Status:        1,
		EffectiveTime: 2592000,
		RankSwitch:    0,
	}
	jsonStr, _ = json.Marshal(info)
	s.HSet("You-Know-Who:status", "2208647", string(jsonStr))
	info = &UKWCachePermissionInfo{
		Uid:           2208648,
		FakeUid:       23458,
		Nickname:      "神秘人59239",
		Switch:        1,
		Status:        1,
		EffectiveTime: 2592000,
		RankSwitch:    0,
	}
	jsonStr, _ = json.Marshal(info)
	s.HSet("You-Know-Who:status", "2208648", string(jsonStr))

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient         *redis.Client
		tracer              opentracing.Tracer
		localCache          []*sync.Map
		localCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*UKWCachePermissionInfo
		wantErr bool
	}{
		{
			name: "测试获取所有的信息",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:         tt.fields.redisClient,
				tracer:              tt.fields.tracer,
				localCache:          tt.fields.localCache,
				localCacheReadIndex: tt.fields.localCacheReadIndex,
			}
			_, err := y.GetAllUKWStatusCacheInfos(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllUKWStatusCacheInfos() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestYouKnowWhoCache_GetUKWStatusCacheInfo(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	info := &UKWCachePermissionInfo{
		Uid:           2208646,
		FakeUid:       23456,
		Nickname:      "神秘人5922",
		Switch:        1,
		Status:        1,
		EffectiveTime: 2592000,
		RankSwitch:    0,
	}
	jsonStr, _ := json.Marshal(info)
	s.HSet("You-Know-Who:status", "2208646", string(jsonStr))

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient         *redis.Client
		tracer              opentracing.Tracer
		localCache          []*sync.Map
		localCacheReadIndex uint8
	}
	type args struct {
		ctx    context.Context
		ukwUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *UKWCachePermissionInfo
		wantErr bool
	}{
		{
			name: "获取单个信息",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:    ctx,
				ukwUid: 2208646,
			},
			wantErr: false,
			want:    info,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient: tt.fields.redisClient,
			}
			got, err := y.GetUKWStatusCacheInfo(tt.args.ctx, tt.args.ukwUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUKWStatusCacheInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUKWStatusCacheInfo() want = %v, got %v", tt.want, got)
			}
		})
	}
}

func TestYouKnowWhoCache_BatchUpdateUKWStatusCacheInfo(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.HSet("You-Know-Who:status", "12345", "test")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	info1 := &UKWCachePermissionInfo{
		Uid:           12345,
		FakeUid:       23456,
		Nickname:      "神秘人24690",
		Status:        1,
		Switch:        1,
		EffectiveTime: 666666,
		RankSwitch:    0,
	}
	info2 := &UKWCachePermissionInfo{
		Uid:           12346,
		FakeUid:       23457,
		Nickname:      "神秘人24691",
		Status:        1,
		Switch:        1,
		EffectiveTime: 666666,
		RankSwitch:    0,
	}
	infos := make([]*UKWCachePermissionInfo, 0)
	infos = append(infos, info1, info2)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx   context.Context
		infos []*UKWCachePermissionInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试批量更新神秘人状态信息",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:   ctx,
				infos: infos,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			if err := y.BatchUpdateUKWStatusCacheInfo(tt.args.ctx, tt.args.infos); (err != nil) != tt.wantErr {
				t.Errorf("BatchUpdateUKWStatusCacheInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
			result, _ := y.redisClient.HGet("You-Know-Who:status", "12345").Result()
			statusInfo := new(UKWCachePermissionInfo)
			err = json.Unmarshal([]byte(result), statusInfo)
			if err != nil {
				t.Errorf("GetUKWStatusCacheInfo Unmarshal failed, err:[%+v]", err)
			}
			if !reflect.DeepEqual(statusInfo, info1) {
				t.Errorf("BatchUpdateUKWStatusCacheInfo() update failed. ")
			}
		})
	}
}

func TestYouKnowWhoCache_DelUKWStatusCacheInfo(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.HSet("You-Know-Who:status", "12345", "test")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx    context.Context
		ukwUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试删除指定神秘人状态信息",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:    ctx,
				ukwUid: 12345,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			if err := y.DelUKWStatusCacheInfo(tt.args.ctx, tt.args.ukwUid); (err != nil) != tt.wantErr {
				t.Errorf("DelUKWStatusCacheInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestYouKnowWhoCache_GetUKWFakeUidCacheInfo(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	info := &UKWCacheFakeUidInfo{
		Uid:      12345,
		FakeUid:  23456,
		NickName: "神秘人73925",
	}
	jsonStr, err := json.Marshal(info)
	if err != nil {
		t.Errorf("UpdateUKWFakeUidCacheInfo Marshal failed, err:[%+v]", err)
	}
	s.HSet("You-Know-Who:fake-uid", "23456", string(jsonStr))

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx     context.Context
		fakeUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *UKWCacheFakeUidInfo
		wantErr bool
	}{
		{
			name: "测试从redis根据神秘人假uid获取真uid映射关系",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:     ctx,
				fakeUid: 23456,
			},
			want: &UKWCacheFakeUidInfo{
				Uid:      12345,
				FakeUid:  23456,
				NickName: "神秘人73925",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			got, err := y.GetUKWFakeUidCacheInfo(tt.args.ctx, tt.args.fakeUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUKWFakeUidCacheInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUKWFakeUidCacheInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_GetAllUKWFakeUidCacheInfos(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	info := &UKWCacheFakeUidInfo{
		Uid:     12345,
		FakeUid: 23456,
	}
	jsonStr, err := json.Marshal(info)
	if err != nil {
		t.Errorf("UpdateUKWFakeUidCacheInfo Marshal failed, err:[%+v]", err)
	}
	s.HSet("You-Know-Who:fake-uid", "23456", string(jsonStr))
	info = &UKWCacheFakeUidInfo{
		Uid:     12346,
		FakeUid: 23457,
	}
	jsonStr, err = json.Marshal(info)
	if err != nil {
		t.Errorf("UpdateUKWFakeUidCacheInfo Marshal failed, err:[%+v]", err)
	}
	s.HSet("You-Know-Who:fake-uid", "23457", string(jsonStr))
	info = &UKWCacheFakeUidInfo{
		Uid:     12347,
		FakeUid: 23458,
	}
	jsonStr, err = json.Marshal(info)
	if err != nil {
		t.Errorf("UpdateUKWFakeUidCacheInfo Marshal failed, err:[%+v]", err)
	}
	s.HSet("You-Know-Who:fake-uid", "23458", string(jsonStr))

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*UKWCacheFakeUidInfo
		wantErr bool
	}{
		{
			name: "测试从redis批量获取假uid到真uid映射关系",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			got, err := y.GetAllUKWFakeUidCacheInfos(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllUKWFakeUidCacheInfos() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for _, r := range got {
				if r.FakeUid == uint32(23456) {
					if r.Uid != 12345 {
						t.Errorf("GetAllUKWFakeUidCacheInfos info is wrong")
					}
				}
				if r.FakeUid == uint32(23458) {
					if r.Uid != 12347 {
						t.Errorf("GetAllUKWFakeUidCacheInfos info is wrong")
					}
				}
			}
		})
	}
}

func TestYouKnowWhoCache_UpdateUKWFakeUidCacheInfo(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.HSet("You-Know-Who:fake-uid", "23456", "hehe")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	info := &UKWCacheFakeUidInfo{
		Uid:     12345,
		FakeUid: 23456,
	}

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx  context.Context
		info *UKWCacheFakeUidInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试redis更新假uid到真uid的映射",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:  ctx,
				info: info,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			if err := y.UpdateUKWFakeUidCacheInfo(tt.args.ctx, tt.args.info); (err != nil) != tt.wantErr {
				t.Errorf("UpdateUKWFakeUidCacheInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
			result, _ := y.redisClient.HGet("You-Know-Who:fake-uid", "23456").Result()
			fakeInfo := new(UKWCacheFakeUidInfo)
			err = json.Unmarshal([]byte(result), fakeInfo)
			if err != nil {
				t.Errorf("UpdateUKWFakeUidCacheInfo Unmarshal failed, err:[%+v]", err)
			}
			if !reflect.DeepEqual(fakeInfo, info) {
				t.Errorf("UpdateUKWFakeUidCacheInfo() update failed. ")
			}
		})
	}
}

func TestYouKnowWhoCache_BatchUpdateUKWFakeUidCacheInfo(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.HSet("You-Know-Who:fake-uid", "23456", "hehe")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	info1 := &UKWCacheFakeUidInfo{
		Uid:     12345,
		FakeUid: 23456,
	}
	info2 := &UKWCacheFakeUidInfo{
		Uid:     12346,
		FakeUid: 23457,
	}
	info3 := &UKWCacheFakeUidInfo{
		Uid:     12347,
		FakeUid: 23458,
	}
	infos := make([]*UKWCacheFakeUidInfo, 0)
	infos = append(infos, info1, info2, info3)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx   context.Context
		infos []*UKWCacheFakeUidInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试redis批量更新假uid到uid映射关系",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:   ctx,
				infos: infos,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			if err := y.BatchUpdateUKWFakeUidCacheInfo(tt.args.ctx, tt.args.infos); (err != nil) != tt.wantErr {
				t.Errorf("BatchUpdateUKWFakeUidCacheInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
			result, _ := y.redisClient.HGet("You-Know-Who:fake-uid", "23456").Result()
			fakeInfo := new(UKWCacheFakeUidInfo)
			err = json.Unmarshal([]byte(result), fakeInfo)
			if err != nil {
				t.Errorf("UpdateUKWFakeUidCacheInfo Unmarshal failed, err:[%+v]", err)
			}
			if !reflect.DeepEqual(fakeInfo, info1) {
				t.Errorf("UpdateUKWFakeUidCacheInfo() update failed. ")
			}
		})
	}
}

func TestYouKnowWhoCache_DelUKWFakeUidCacheInfo(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.HSet("You-Know-Who:fake-uid", "23456", "hehe")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx     context.Context
		fakeUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试删除redis假uid到uid映射关系",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:     ctx,
				fakeUid: 23456,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			if err := y.DelUKWFakeUidCacheInfo(tt.args.ctx, tt.args.fakeUid); (err != nil) != tt.wantErr {
				t.Errorf("DelUKWFakeUidCacheInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestYouKnowWhoCache_UpdateUKWInfoUpdate(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_UPDATE_INFO_KEY, now.Format("20060102"))
	s.HSet(key, "12345", "hehe")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试redis更新今日已经更新的uid列表",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				uid: 12345,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			if err := y.UpdateUKWInfoUpdate(tt.args.ctx, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("UpdateUKWInfoUpdate() error = %v, wantErr %v", err, tt.wantErr)
			}
			r, _ := y.redisClient.HGet(key, "12345").Result()
			if r != "12345" {
				t.Errorf("UpdateUKWInfoUpdate failed")
			}
		})
	}
}

func TestYouKnowWhoCache_GetAllUKWInfoUpdateInfo(t *testing.T) {
	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_UPDATE_INFO_KEY, now.Format("20060102"))
	s.HSet(key, "12345", "12345")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []uint32
		wantErr bool
	}{
		{
			name: "测试redis获取今日已经刷新过假uid和昵称的uid列表",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
			},
			want:    []uint32{12345},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			got, err := y.GetAllUKWInfoUpdateInfo(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllUKWInfoUpdateInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllUKWInfoUpdateInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_GetUKWInfoUpdateInfo(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_UPDATE_INFO_KEY, now.Format("20060102"))
	s.HSet(key, "12345", "12345")
	s.HSet(key, "12346", "12346")
	s.HSet(key, "12347", "12347")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "判断redis根据指定uid获取是否有更新过假uid和昵称",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				uid: 12347,
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			got, err := y.GetUKWInfoUpdateInfo(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUKWInfoUpdateInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetUKWInfoUpdateInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_GetReportTaskFinishedTime(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_REPORT_FINISHED_KEY, now.Format("200601"))
	s.Set(key, "12345")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
		now time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "测试redis获取神秘人报表完成统计时间",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				now: now,
			},
			want:    "12345",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			got, err := y.GetReportTaskFinishedTime(tt.args.ctx, tt.args.now)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetReportTaskFinishedTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetReportTaskFinishedTime() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_SetReportTaskFinishedTime(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_REPORT_FINISHED_KEY, now.Format("200601"))

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
		now time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试redis设置神秘人报表完成时间",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				now: now,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			if err := y.SetReportTaskFinishedTime(tt.args.ctx, tt.args.now); (err != nil) != tt.wantErr {
				t.Errorf("SetReportTaskFinishedTime() error = %v, wantErr %v", err, tt.wantErr)
			}
			if r, err := y.redisClient.Get(key).Result(); r != now.String() || err != nil {
				t.Errorf("SetReportTaskFinishedTime failed, want:[%+v], get:[%+v]", now.String(), r)
			}
		})
	}
}

func TestYouKnowWhoCache_GetFakeUidUpdateFinishedTime(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_UPDATE_FAKE_UID_KEY, now.Format("20060102"))
	s.Set(key, "12345")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
		now time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "测试redis获取假uid更新完成锁",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				now: now,
			},
			want:    "12345",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			got, err := y.GetFakeUidUpdateFinishedTime(tt.args.ctx, tt.args.now)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFakeUidUpdateFinishedTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetFakeUidUpdateFinishedTime() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_SetFakeUidUpdateFinishedTime(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_UPDATE_FAKE_UID_KEY, now.Format("20060102"))

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
		now time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "设置redis神秘人刷新假uid完成锁",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				now: now,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			if err := y.SetFakeUidUpdateFinishedTime(tt.args.ctx, tt.args.now); (err != nil) != tt.wantErr {
				t.Errorf("SetFakeUidUpdateFinishedTime() error = %v, wantErr %v", err, tt.wantErr)
			}
			if r, err := y.redisClient.Get(key).Result(); r != now.String() || err != nil {
				t.Errorf("SetFakeUidUpdateFinishedTime failed, want:[%+v], get:[%+v]", now.String(), r)
			}
		})
	}
}

func TestYouKnowWhoCache_GetFakePoolUpdateFinishedTime(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_FAKE_POOL_FINISHED_KEY, now.Format("20060102"))
	s.Set(key, "12345")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
		now time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "测试redis获取神秘人假uid号码池刷新完毕时间",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				now: now,
			},
			wantErr: false,
			want:    "12345",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			got, err := y.GetFakePoolUpdateFinishedTime(tt.args.ctx, tt.args.now)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFakePoolUpdateFinishedTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetFakePoolUpdateFinishedTime() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_SetFakePoolUpdateFinishedTime(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	now := time.Now()
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_FAKE_POOL_FINISHED_KEY, now.Format("20060102"))

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
		now time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试设置redis神秘人假uid号码池刷新完成时间",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				now: now,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			if err := y.SetFakePoolUpdateFinishedTime(tt.args.ctx, tt.args.now); (err != nil) != tt.wantErr {
				t.Errorf("SetFakePoolUpdateFinishedTime() error = %v, wantErr %v", err, tt.wantErr)
			}
			if r, err := y.redisClient.Get(key).Result(); r != now.String() || err != nil {
				t.Errorf("SetFakePoolUpdateFinishedTime failed, want:[%+v], get:[%+v]", now.String(), r)
			}
		})
	}
}

func TestYouKnowWhoCache_GetRankSwitchLock(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	uid := uint32(12345)
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_RANK_SWITCH_LOCK, uid)
	s.Set(key, "12345")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "测试redis获取神秘人排行榜切换时间锁",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
				uid: uid,
			},
			wantErr: false,
			want:    false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			got, err := y.GetRankSwitchLock(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRankSwitchLock() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetRankSwitchLock() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_SetRankSwitchLock(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	uid := uint32(12345)
	key := fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_RANK_SWITCH_LOCK, uid)

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx        context.Context
		uid        uint32
		expireTime int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试redis设置神秘人切换排行榜开关锁",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:        ctx,
				uid:        uid,
				expireTime: 66666,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			if err := y.SetRankSwitchLock(tt.args.ctx, tt.args.uid, tt.args.expireTime); (err != nil) != tt.wantErr {
				t.Errorf("SetRankSwitchLock() error = %v, wantErr %v", err, tt.wantErr)
			}
			if r, err := y.redisClient.Get(key).Result(); r != fmt.Sprintf("%+v", uid) || err != nil {
				t.Errorf("SetFakePoolUpdateFinishedTime failed, want:[%+v], get:[%+v]", uid, r)
			}
		})
	}
}

func TestYouKnowWhoCache_SetFakeUidList(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	uid := uint32(12345)
	s.Lpush("You-Know-Who:fake-uid-pool", "666666")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx         context.Context
		fakeUidList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试redis神秘人假uid池子添加号码",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx:         ctx,
				fakeUidList: []uint32{uid},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			if err := y.SetFakeUidList(tt.args.ctx, tt.args.fakeUidList); (err != nil) != tt.wantErr {
				t.Errorf("SetFakeUidList() error = %v, wantErr %v", err, tt.wantErr)
			}
			if num, err := y.redisClient.RPop("You-Know-Who:fake-uid-pool").Result(); num != "666666" || err != nil {
				t.Errorf("SetFakeUidList() want = %v, got %v", "666666", num)
			}
			if num, err := y.redisClient.RPop("You-Know-Who:fake-uid-pool").Result(); num != fmt.Sprintf("%+v", uid) || err != nil {
				t.Errorf("SetFakeUidList() want = %v, got %v", uid, num)
			}
		})
	}
}

func TestYouKnowWhoCache_GetFakeUid(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.Lpush("You-Know-Who:fake-uid-pool", "666666")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "测试redis获取神秘人假uid号码池号码",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
			},
			wantErr: false,
			want:    666666,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			got, err := y.GetFakeUid(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFakeUid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetFakeUid() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_GetFakeUidPoolLen(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.Lpush("You-Know-Who:fake-uid-pool", "666666")
	s.Lpush("You-Know-Who:fake-uid-pool", "777777")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "测试redis获取假uid号码池长度",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
			},
			want:    2,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			got, err := y.GetFakeUidPoolLen(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFakeUidPoolLen() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetFakeUidPoolLen() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhoCache_GetAllFakeUidPoolUidList(t *testing.T) {

	ctx := context.Background()
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	// 准备数据
	s.Lpush("You-Know-Who:fake-uid-pool", "666666")
	s.Lpush("You-Know-Who:fake-uid-pool", "777777")

	// 连接mock的redis server
	port, _ := strconv.Atoi(s.Port())
	redisConf := &config.RedisConfig{
		Host: s.Host(),
		Port: port,
	}
	rdb := redis.NewClient(redisConf)

	type fields struct {
		redisClient                *redis.Client
		tracer                     opentracing.Tracer
		localCache                 []*sync.Map
		localCacheReadIndex        uint8
		localFakeUidCache          []*sync.Map
		localFakeUidCacheReadIndex uint8
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "测试redis获取神秘人假uid池中的所有uid",
			fields: fields{
				redisClient: rdb,
			},
			args: args{
				ctx: ctx,
			},
			want:    []string{"777777", "666666"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &YouKnowWhoCache{
				redisClient:                tt.fields.redisClient,
				tracer:                     tt.fields.tracer,
				localCache:                 tt.fields.localCache,
				localCacheReadIndex:        tt.fields.localCacheReadIndex,
				localFakeUidCache:          tt.fields.localFakeUidCache,
				localFakeUidCacheReadIndex: tt.fields.localFakeUidCacheReadIndex,
			}
			got, err := y.GetAllFakeUidPoolUidList(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllFakeUidPoolUidList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllFakeUidPoolUidList() got = %v, want %v", got, tt.want)
			}
		})
	}
}
