package server

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/http"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	pb "golang.52tt.com/protocol/services/virtual-plugin-http"
)

// 获取礼物配置
func (s *Server) GetGiftConfs(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	httpReq := &pb.GetGiftConfsReq{}
	if err := http.ReadJSON(r, httpReq); err != nil {
		log.ErrorWithCtx(ctx, "GetGiftConfs err:%v", err)
		w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusBadRequest)
		return
	}
	baseReq := &BaseReq{
		Uri:       r.RequestURI,
		ChnSecret: httpReq.ChnSecret,
		ReqTime:   httpReq.ReqTime,
		Sign:      httpReq.Sign,
	}
	httpRsp := &pb.GetGiftConfsRsp{
		Data: []*pb.GiftConf{},
	}
	httpErr := s.CheckInvalidArg(ctx, baseReq)
	if httpErr != nil {
		log.ErrorWithCtx(ctx, "GetGiftConfs CheckInvalidArg err:%v", httpErr.Error())
		httpRsp.ErrCode = httpErr.ErrCode
		httpRsp.ErrMsg = httpErr.ErrMsg
		_ = http.WriteJSON(w, http.StatusOK, httpRsp)
		return
	}

	anchorUid, cid, httpErr := s.CheckAuth(ctx, baseReq, httpReq)
	if httpErr != nil {
		log.ErrorWithCtx(ctx, "GetGiftConfs CheckAuth err:%v", httpErr.Error())
		httpRsp.ErrCode = httpErr.ErrCode
		httpRsp.ErrMsg = httpErr.ErrMsg
		_ = http.WriteJSON(w, http.StatusOK, httpRsp)
		return
	}

	dy := s.dycf.Get()
	if dy.IsAllGifts { // 全部礼物
		httpRsp.Data = s.pbPresentList
	} else {
		for _, giftId := range s.dycf.Get().GiftIdList {
			info := s.GetGiftInfo(giftId)
			if info == nil {
				continue
			}
			httpRsp.Data = append(httpRsp.Data, info)
		}
	}

	log.InfoWithCtx(ctx, "GetGiftConfs ok secret:%s, anchorUid:%d, cid:%d ", httpReq.ChnSecret, anchorUid, cid)

	_ = http.WriteJSON(w, http.StatusOK, httpRsp)
	return
}

// 获取表情配置
func (s *Server) GetEmojiConfs(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	httpReq := &pb.GetEmojiReq{}
	if err := http.ReadJSON(r, httpReq); err != nil {
		log.ErrorWithCtx(ctx, "GetEmojiConfs err:%v", err)
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	baseReq := &BaseReq{
		Uri:       r.RequestURI,
		ChnSecret: httpReq.ChnSecret,
		ReqTime:   httpReq.ReqTime,
		Sign:      httpReq.Sign,
	}
	httpRsp := &pb.GetEmojiRsp{
		Data: []*pb.EmojiConf{},
	}
	httpErr := s.CheckInvalidArg(ctx, baseReq)
	if httpErr != nil {
		log.ErrorWithCtx(ctx, "GetEmojiConfs CheckInvalidArg err:%v", httpErr.Error())
		httpRsp.ErrCode = httpErr.ErrCode
		httpRsp.ErrMsg = httpErr.ErrMsg
		_ = http.WriteJSON(w, http.StatusOK, httpRsp)
		return
	}

	anchorUid, cid, httpErr := s.CheckAuth(ctx, baseReq, httpReq)
	if httpErr != nil {
		log.ErrorWithCtx(ctx, "GetEmojiConfs CheckAuth err:%v", httpErr.Error())
		httpRsp.ErrCode = httpErr.ErrCode
		httpRsp.ErrMsg = httpErr.ErrMsg
		_ = http.WriteJSON(w, http.StatusOK, httpRsp)
		return
	}

	for _, cf := range s.dycf.Get().EmojiConfList {
		httpRsp.Data = append(httpRsp.Data, &pb.EmojiConf{
			Name: cf.Text,
			Url:  cf.Img,
		})
	}

	log.InfoWithCtx(ctx, "GetEmojiConfs ok secret:%s, anchorUid:%d, cid:%d ", httpReq.ChnSecret, anchorUid, cid)

	_ = http.WriteJSON(w, http.StatusOK, httpRsp)
	return
}
