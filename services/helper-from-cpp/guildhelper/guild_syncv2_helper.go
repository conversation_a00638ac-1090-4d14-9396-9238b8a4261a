package guildhelper

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"

	"gitlab.ttyuyin.com/golang/svrkit/codes"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/guild"
	"golang.52tt.com/clients/guildtimeline2"
	HeadImage "golang.52tt.com/clients/headimage"
	pushV2 "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	"golang.52tt.com/clients/starguild"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	syncPb "golang.52tt.com/protocol/app/sync"
	"golang.52tt.com/protocol/common/status"
	Guild "golang.52tt.com/protocol/services/guildsvr"
	GuildTimeline2 "golang.52tt.com/protocol/services/guildtimeline2"
	starguildv3 "golang.52tt.com/protocol/services/starguildsvr/v3"
	"golang.52tt.com/services/helper-from-cpp/notifyhelper"
)

var (
	GuildSignInTsMap = sync.Map{} // 公会签到 map   guildId --> signInTs     c++ 中的 g_pStGuildCheckTsHashMap
	GuildDonateTsMap = sync.Map{} // 公会捐献 map   guildId --> donateTs     c++ 中的 g_pStGuildDonateTsHashMap
)

type SGuildSyncV2Helper struct {
	guildTimeline2Cli guildtimeline2.IClient
	guildCli          guild.IClient
	seqgenCli         seqgen.IClient
	starGuildV3Cli    starguild.IClient
	headImageCli      HeadImage.IClient
	accountClient     account.IClient
}

func NewGuildSyncV2Helper(guildTimeline2Cli guildtimeline2.IClient, guildCli guild.IClient, seqgenCli seqgen.IClient,
	headImageCli HeadImage.IClient, starGuildV3Cli starguild.IClient, accountClient account.IClient) *SGuildSyncV2Helper {
	return &SGuildSyncV2Helper{
		guildTimeline2Cli: guildTimeline2Cli,
		guildCli:          guildCli,
		seqgenCli:         seqgenCli,
		headImageCli:      headImageCli,
		starGuildV3Cli:    starGuildV3Cli,
		accountClient:     accountClient,
	}
}

func (s *SGuildSyncV2Helper) UpdateGuildAlbumSyncInfo(ctx context.Context, uid uint32, guildId uint32, albumId uint32, isDel bool) error {
	log.DebugWithCtx(ctx, "start call SGuildSyncV2Helper.UpdateGuildAlbumSyncInfo, uid: %d, guildId: %d, albumId: %d, isDel: %d",
		uid, guildId, albumId, isDel)

	bHaveGuildTL := false
	{
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		resp, err := s.guildTimeline2Cli.CheckHaveGuildData(ctx, uid, &GuildTimeline2.CheckGuildDataIsExistReq{GuildId: guildId})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.UpdateGuildAlbumSyncInfo CheckHaveGuildData failed, err: %+v, uid: %d, guildId: %d, albumId: %d, isDel: %d",
				err, uid, guildId, albumId, isDel)
			return err
		}

		bHaveGuildTL = resp.Is_Exist
	}

	if false == bHaveGuildTL {
		err := s.InitGuildBaseSyncInfo(ctx, uid, guildId)
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.UpdateGuildAlbumSyncInfo InitGuildBaseSyncInfo failed, err: %+v, uid: %d, guildId: %d, albumId: %d, isDel: %d",
				err, uid, guildId, albumId, isDel)
		} else {
			err = s.InitGuildExtraSyncInfo(ctx, uid, guildId)
			if nil != err {
				log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.UpdateGuildAlbumSyncInfo InitGuildExtraSyncInfo failed, err: %+v, uid: %d, guildId: %d, albumId: %d, isDel: %d",
					err, uid, guildId, albumId, isDel)
			}

			err = s.InitGuildNumerSyncInfo(ctx, uid, guildId)
			if nil != err {
				log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.UpdateGuildAlbumSyncInfo InitGuildNumerSyncInfo failed, err: %+v, uid: %d, guildId: %d, albumId: %d, isDel: %d",
					err, uid, guildId, albumId, isDel)
			}

			err = s.InitGuildCheckInSyncInfo(ctx, uid, guildId)
			if nil != err {
				log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.UpdateGuildAlbumSyncInfo InitGuildCheckInSyncInfo failed, err: %+v, uid: %d, guildId: %d, albumId: %d, isDel: %d",
					err, uid, guildId, albumId, isDel)
			}

			err = s.InitGuildAdminMemberSyncInfo(ctx, uid, guildId)
			if nil != err {
				log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.UpdateGuildAlbumSyncInfo InitGuildAdminMemberSyncInfo failed, err: %+v, uid: %d, guildId: %d, albumId: %d, isDel: %d",
					err, uid, guildId, albumId, isDel)
			}

			err = s.InitGuildGroupSyncInfo(ctx, uid, guildId)
			if nil != err {
				log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.UpdateGuildAlbumSyncInfo InitGuildGroupSyncInfo failed, err: %+v, uid: %d, guildId: %d, albumId: %d, isDel: %d",
					err, uid, guildId, albumId, isDel)
			}

			err = s.InitGuildGameSyncInfo(ctx, uid, guildId)
			if nil != err {
				log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.UpdateGuildAlbumSyncInfo InitGuildGameSyncInfo failed, err: %+v, uid: %d, guildId: %d, albumId: %d, isDel: %d",
					err, uid, guildId, albumId, isDel)
			}

			err = s.InitGuildDonateSyncInfo(ctx, uid, guildId)
			if nil != err {
				log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.UpdateGuildAlbumSyncInfo InitGuildDonateSyncInfo failed, err: %+v, uid: %d, guildId: %d, albumId: %d, isDel: %d",
					err, uid, guildId, albumId, isDel)
			}
		}
	}

	seqId, err := s.GenerateGuildSyncSeq(ctx, guildId)
	if nil != err {
		log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.UpdateGuildAlbumSyncInfo GenerateGuildSyncSeq failed, err: %+v, uid: %d, guildId: %d, albumId: %d, isDel: %d",
			err, uid, guildId, albumId, isDel)
		return err
	}

	{
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		err = s.guildTimeline2Cli.UpdateCommonIncrTimelineSeq(ctx, uid, &GuildTimeline2.UpdateCommonIncrTimelineSeqReq{
			GuildId: guildId,
			SeqId:   uint32(seqId),
			Data: &GuildTimeline2.StCommonIncrTimelineData{
				KeyPrefix: "albumV2",
				KeyId:     albumId,
				IsDeleted: isDel,
			},
		})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.UpdateGuildAlbumSyncInfo UpdateCommonIncrTimelineSeq failed, err: %+v, uid: %d, guildId: %d, albumId: %d, isDel: %d",
				err, uid, guildId, albumId, isDel)
			return protocol.NewServerError(status.ErrGuildTimelineSvrSystemErr)
		}
	}
	return nil
}

func (s *SGuildSyncV2Helper) InitGuildBaseSyncInfo(ctx context.Context, uid uint32, guildId uint32) error {
	log.Debugf("SGuildSyncV2Helper.InitGuildBaseSyncInfo uid: %d, guildId: %d", uid, guildId)

	//查公会基本信息
	guildInfo := &Guild.GuildResp{}
	{
		ctx, cancelFunc := context.WithTimeout(context.Background(), 3*time.Second)
		guildResp, err := s.guildCli.GetGuild(ctx, guildId)
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildBaseSyncInfo GetGuild failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
		guildInfo = guildResp
	}

	seqId, err := s.GenerateGuildSyncSeq(ctx, guildId)
	if nil != err {
		log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildBaseSyncInfo GenerateGuildSyncSeq failed, err: %+v, uid: %d, guildId: %d",
			err, uid, guildId)
		return err
	}

	//星级公会信息
	starGuildInfo := &starguildv3.StarGuildInfo{}
	{
		ctx, cancelFunc := context.WithTimeout(context.Background(), 3*time.Second)
		resp, err := s.starGuildV3Cli.GetGuildStarInfos(ctx, &starguildv3.GetGuildStarInfosReq{
			GuildIdList: []uint32{guildId},
			QueryToday:  true,
		})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildBaseSyncInfo GetGuildStarInfos failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}

		if 0 == len(resp.StarInfoList) {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildBaseSyncInfo GetGuildStarInfos empty, uid: %d, guildId: %d",
				uid, guildId)
			return protocol.NewServerError(status.ErrSys)
		}

		starGuildInfo = resp.StarInfoList[0]
	}

	//星级公会等级对应的上限
	gameCntCfg := &Guild.GetGuildGameCountConfigResp{}
	{
		ctx, cancelFunc := context.WithTimeout(context.Background(), 3*time.Second)
		resp, err := s.guildCli.GetGuildGameCountConfig(ctx, uid, &Guild.GetGuildGameCountConfigReq{Level: starGuildInfo.StarLevel})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildBaseSyncInfo GetGuildGameCountConfig failed, err: %+v, uid: %d, guildId: %d, starGuild: %+v",
				err, uid, guildId, starGuildInfo)
			return err
		}

		gameCntCfg = resp
	}

	//公会扩展游戏数量
	extraGameCnt := uint32(0)
	{
		ctx, cancelFunc := context.WithTimeout(context.Background(), 3*time.Second)
		resp, err := s.guildCli.GetGuildExtraGameCount(ctx, uid, &Guild.GetGuildExtraGameCountReq{GuildId: guildId})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildBaseSyncInfo GetGuildExtraGameCount failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
		extraGameCnt = resp.Count
	}

	{
		stBaseInfo := &GuildTimeline2.StGuildBaseSyncInfo{
			LastUpdateTime: uint32(time.Now().Unix()),
			LastSeqId:      uint32(seqId),
			GuildId:        guildId,
			GuildDisplayId: guildInfo.ShortId,
			GuildGroupID:   guildInfo.MainGroup,
			GameSizelimit:  gameCntCfg.Count + extraGameCnt,
			CreateDate:     guildInfo.CreatedAt,
			GuildPrefix:    guildInfo.Prefix,
			Name:           guildInfo.Name,
			Desc:           guildInfo.Intro,
			Manifesto:      guildInfo.Manifesto,
			IsNeedverify:   guildInfo.NeedVerify,
		}
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		err = s.guildTimeline2Cli.UpdateGuildBaseInfSeq(ctx, uid, &GuildTimeline2.UpdateGuildBaseInfoSeqReq{
			GuildId:  guildId,
			Baseinfo: stBaseInfo,
		})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildBaseSyncInfo UpdateGuildBaseInfSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
	}

	return nil
}

func (s *SGuildSyncV2Helper) GenerateGuildSyncSeq(ctx context.Context, guildId uint32) (seqId uint64, err error) {
	ctx, cancelFunc := context.WithTimeout(ctx, 1*time.Second)
	seqId, err = s.seqgenCli.GenerateSequence(ctx, guildId, seqgen.NamespaceGuild, seqgen.KeyGuildV2, 1)
	cancelFunc()
	if nil != err {
		log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.GenerateGuildSyncSeq GenerateSequence failed, err: %+v, guild: %d", err, guildId)
		return 0, err
	}

	return seqId, nil
}

func (s *SGuildSyncV2Helper) InitGuildExtraSyncInfo(ctx context.Context, uid uint32, guildId uint32) (err error) {
	log.DebugWithCtx(ctx, "start call SGuildSyncV2Helper.InitGuildExtraSyncInfo, uid: %d, guildId: %d", uid, guildId)

	//查公会图标
	guildImageMd5 := ""
	{
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		guildImageMd5, err = s.headImageCli.GetHeadImageMd5(ctx, uid, strconv.FormatUint(uint64(guildId), 10)+"@guild")
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildExtraSyncInfo GetHeadImageMd5 failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}

		if len(guildImageMd5) == 0 {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildExtraSyncInfo GetHeadImageMd5 empty, uid: %d, guildId: %d", uid, guildId)
			return nil
		}
	}

	seqId := uint64(0)
	{
		ctx, cancelFunc := context.WithTimeout(ctx, 1*time.Second)
		seqId, err = s.GenerateGuildSyncSeq(ctx, guildId)
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildExtraSyncInfo GenerateGuildSyncSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
	}

	{
		ctx, cancelFunc := context.WithTimeout(ctx, 1*time.Second)
		err = s.guildTimeline2Cli.UpdateGuildExtraInfoSeq(ctx, uid, &GuildTimeline2.UpdateGuildExtraInfoSeqReq{
			GuildId: guildId,
			Extrainfo: &GuildTimeline2.StGuildExtraSyncInfo{
				LastUpdateTime: uint32(time.Now().Unix()),
				LastSeqId:      uint32(seqId),
				IconId:         "",
			},
		})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildExtraSyncInfo UpdateGuildExtraInfoSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return protocol.NewServerError(status.ErrGuildTimelineSvrSystemErr)
		}
	}

	return nil
}

func (s *SGuildSyncV2Helper) InitGuildNumberSyncInfo(ctx context.Context, reqUid, guildId uint32) (err error) {
	log.DebugWithCtx(ctx, "InitGuildNumberSyncInfo uid:%d, guildId:%d", reqUid, guildId)

	// 查公会基本信息
	guildInfoRes, err := s.guildCli.GetGuild(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitGuildNumberSyncInfo guild.GetGuild failed, err:%v, uid:%d, guildId:%d", err, reqUid, guildId)
		return err
	}

	currTs := time.Now().Unix()
	// 生成seqid
	seqId, err := s.GenerateGuildSyncSeq(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitGuildNumberSyncInfo GenerateGuildSyncSeq failed, err:%v, uid:%d, guildId:%d", err, reqUid, guildId)
		return err
	}

	stNumberData := &GuildTimeline2.StGuildNumberDataSyncInfo{
		LastSeqId:      uint32(seqId),
		LastUpdateTime: uint32(currTs),
		MemberCount:    guildInfoRes.GetMemberCount(),
	}
	err = s.guildTimeline2Cli.UpdateGuildNumberDataInfoSeq(ctx, reqUid, &GuildTimeline2.UpdateGuildNumberInfoSeqReq{
		GuildId:    guildId,
		NumberInfo: stNumberData,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "InitGuildNumberSyncInfo guildTimline2.UpdateGuildNumberDataInfoSeq failed, err:%v, uid:%d, guildId:%d",
			err, reqUid, guildId)
		return protocol.NewExactServerError(nil, status.ErrGuildTimelineSvrSystemErr)
	}

	return nil
}

func (s *SGuildSyncV2Helper) InitGuildNumerSyncInfo(ctx context.Context, uid uint32, guildId uint32) (err error) {
	log.DebugWithCtx(ctx, "start call SGuildSyncV2Helper.InitGuildNumerSyncInfo, uid: %d, guildId: %d", uid, guildId)

	guildMemCnt := uint32(0)
	{
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		resp, err := s.guildCli.GetGuild(ctx, guildId)
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildNumerSyncInfo get guild info failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}

		guildMemCnt = resp.MemberCount
	}

	seqId := uint64(0)
	{
		ctx, cancelFunc := context.WithTimeout(ctx, 1*time.Second)
		seqId, err = s.GenerateGuildSyncSeq(ctx, guildId)
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildNumerSyncInfo GenerateGuildSyncSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
	}

	{
		ctx, cancelFunc := context.WithTimeout(ctx, 1*time.Second)
		err = s.guildTimeline2Cli.UpdateGuildNumberDataInfoSeq(ctx, uid, &GuildTimeline2.UpdateGuildNumberInfoSeqReq{
			GuildId: guildId,
			NumberInfo: &GuildTimeline2.StGuildNumberDataSyncInfo{
				LastUpdateTime: uint32(time.Now().Unix()),
				LastSeqId:      uint32(seqId),
				MemberCount:    guildMemCnt,
			},
		})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildNumerSyncInfo UpdateGuildNumberDataInfoSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
		}
	}

	log.DebugWithCtx(ctx, "SGuildSyncV2Helper.InitGuildNumerSyncInfo finish... uid is %d, guildId is %d, guildMemCnt is %d, "+
		"seqId is %d", uid, guildId, guildMemCnt, seqId)

	return nil
}

func (s *SGuildSyncV2Helper) InitGuildCheckInSyncInfo(ctx context.Context, uid uint32, guildId uint32) (err error) {
	log.DebugWithCtx(ctx, "start call SGuildSyncV2Helper.InitGuildCheckInSyncInfo, uid: %d, guildId: %d", uid, guildId)

	// 查公会目前的总签到人数
	guildCheckinCnt := uint32(0)
	{
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		resp, err := s.guildCli.GetGuildCheckinCount(ctx, uid, &Guild.GetGuildCheckinCountReq{GuildId: guildId})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildCheckInSyncInfo GetGuildCheckinCount failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
		guildCheckinCnt = resp.CheckinCount
	}

	seqId := uint64(0)
	{
		ctx, cancelFunc := context.WithTimeout(ctx, 1*time.Second)
		seqId, err = s.GenerateGuildSyncSeq(ctx, guildId)
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildCheckInSyncInfo GenerateGuildSyncSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
	}

	{
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		err = s.guildTimeline2Cli.UpdateGuildCheckinNum(ctx, uid, &GuildTimeline2.UpdateGuildCheckinNumSeqReq{
			GuildId: guildId,
			NumInfo: &GuildTimeline2.StGuildCheckinNumSyncInfo{
				LastUpdateTime: uint32(time.Now().Unix()),
				LastSeqId:      uint32(seqId),
				CheckinNum:     guildCheckinCnt,
			},
		})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildCheckInSyncInfo UpdateGuildCheckinNum failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
	}

	//查签到前N个人
	var checkInList []*Guild.GetGuildCheckinResp
	{
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		resp, err := s.guildCli.GetGuildCheckinList(ctx, uid, &Guild.GetGuildCheckinListReq{
			GuildId:  guildId,
			Limit:    ^uint32(0) >> 1,
			Offset:   0,
			RankType: 0,
		})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildCheckInSyncInfo GetGuildCheckinList failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}

		checkInList = append(checkInList, resp.CheckinList...)
	}

	{
		req := &GuildTimeline2.UpdateGuildCheckinTopUserSeqReq{
			GuildId: guildId,
			TopInfo: &GuildTimeline2.StGuildCheckinTopUserSyncInfo{
				LastUpdateTime:  uint32(time.Now().Unix()),
				LastSeqId:       uint32(seqId),
				CheckinTopNList: []*GuildTimeline2.CheckInMem{},
			},
		}

		for _, checkInItem := range checkInList {
			req.TopInfo.CheckinTopNList = append(req.TopInfo.CheckinTopNList, &GuildTimeline2.CheckInMem{
				Uid:  checkInItem.Uid,
				Days: checkInItem.CheckinCount,
			})
		}

		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		err = s.guildTimeline2Cli.UpdateGuildCheckinTopUserSeq(ctx, uid, req)
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildCheckInSyncInfo UpdateGuildCheckinTopUserSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
	}

	log.DebugWithCtx(ctx, "SGuildSyncV2Helper.InitGuildCheckInSyncInfo success... uid: %d, guildId: %d, reqId: %d, checkInList is %v",
		uid, guildId, seqId, checkInList)

	return nil
}

func (s *SGuildSyncV2Helper) InitGuildAdminMemberSyncInfo(ctx context.Context, uid uint32, guildId uint32) (err error) {
	log.DebugWithCtx(ctx, "start call SGuildSyncV2Helper.InitGuildAdminMemberSyncInfo, uid: %d, guildId: %d", uid, guildId)

	//获取公会全部管理员
	mapGuildAdminId := make(map[uint32]struct{})
	{
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		resp, err := s.guildCli.GetGuildMemberList(ctx, uid, guildId, true, false)
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildAdminMemberSyncInfo GetGuildMemberList failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}

		for _, mem := range resp.Members {
			mapGuildAdminId[mem.Uid] = struct{}{}
		}
	}

	//获取公会下所有群
	var guildGroupIdList []uint32
	{
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		resp, err := s.guildCli.GetGuildGroupList(ctx, uid, guildId)
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildAdminMemberSyncInfo GetGuildGroupList failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}

		guildGroupIdList = resp.GroupIdList
	}

	//遍历这些公会群 获取群管理员
	for _, groupId := range guildGroupIdList {
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		resp, err := s.guildCli.GetGroupMemberListAdmin(ctx, uid, groupId, 0, ^uint32(0)>>1)
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildAdminMemberSyncInfo GetGroupMemberListAdmin failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}

		for _, mem := range resp.Members {
			if mem.Role == 1 || mem.Role == 2 {
				mapGuildAdminId[mem.Uid] = struct{}{}
			}
		}
	}

	for adminUid, _ := range mapGuildAdminId {
		seqId, err := s.GenerateGuildSyncSeq(ctx, guildId)
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildAdminMemberSyncInfo GenerateGuildSyncSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}

		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		err = s.guildTimeline2Cli.UpdateCommonIncrTimelineSeq(ctx, uid, &GuildTimeline2.UpdateCommonIncrTimelineSeqReq{
			GuildId: guildId,
			SeqId:   uint32(seqId),
			Data: &GuildTimeline2.StCommonIncrTimelineData{
				KeyPrefix: "managerV2",
				KeyId:     adminUid,
				IsDeleted: false,
			},
		})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildAdminMemberSyncInfo UpdateCommonIncrTimelineSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
	}

	log.DebugWithCtx(ctx, "SGuildSyncV2Helper.InitGuildCheckInSyncInfo success... uid: %d, guildId: %d, mapGuildAdminId: %v, guildGroupIdList is %v",
		uid, guildId, mapGuildAdminId, guildGroupIdList)

	return nil
}

func (s *SGuildSyncV2Helper) InitGuildGroupSyncInfo(ctx context.Context, uid uint32, guildId uint32) (err error) {
	log.DebugWithCtx(ctx, "start call SGuildSyncV2Helper.InitGuildGroupSyncInfo, uid: %d, guildId: %d", uid, guildId)

	//获取公会全部群ID
	var guildGroupIdList []uint32
	{
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		resp, err := s.guildCli.GetGuildGroupList(ctx, uid, guildId)
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildGroupSyncInfo GetGuildGroupList failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}

		guildGroupIdList = resp.GroupIdList
	}

	for _, groudId := range guildGroupIdList {
		seqId, err := s.GenerateGuildSyncSeq(ctx, guildId)
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildGroupSyncInfo GenerateGuildSyncSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}

		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		err = s.guildTimeline2Cli.UpdateCommonIncrTimelineSeq(ctx, uid, &GuildTimeline2.UpdateCommonIncrTimelineSeqReq{
			GuildId: guildId,
			SeqId:   uint32(seqId),
			Data: &GuildTimeline2.StCommonIncrTimelineData{
				KeyPrefix: "groupV2",
				KeyId:     groudId,
				IsDeleted: false,
			},
		})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildGroupSyncInfo UpdateCommonIncrTimelineSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
	}

	log.DebugWithCtx(ctx, "SGuildSyncV2Helper.InitGuildCheckInSyncInfo success... uid: %d, guildId: %d, guildGroupIdList: %v, guildGroupIdList is %v",
		uid, guildId, guildGroupIdList, guildGroupIdList)

	return nil
}

func (s *SGuildSyncV2Helper) InitGuildGameSyncInfo(ctx context.Context, uid uint32, guildId uint32) (err error) {
	log.DebugWithCtx(ctx, "start call SGuildSyncV2Helper.InitGuildGameSyncInfo, uid: %d, guildId: %d", uid, guildId)

	mapGameIds := make(map[uint32]struct{})
	{
		ctx, cancelFunc := context.WithTimeout(context.Background(), 3*time.Second)
		resp, err := s.guildCli.GetGuildGameList(ctx, uid, guildId)
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildGameSyncInfo GetGuildGameList failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}

		for _, game := range resp.Games {
			mapGameIds[game.GameId] = struct{}{}
		}
	}

	for gameId, _ := range mapGameIds {
		seqId, err := s.GenerateGuildSyncSeq(ctx, guildId)
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildGameSyncInfo GenerateGuildSyncSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}

		ctx, cancelFunc := context.WithTimeout(context.Background(), 3*time.Second)
		err = s.guildTimeline2Cli.UpdateCommonIncrTimelineSeq(ctx, uid, &GuildTimeline2.UpdateCommonIncrTimelineSeqReq{
			GuildId: guildId,
			SeqId:   uint32(seqId),
			Data: &GuildTimeline2.StCommonIncrTimelineData{
				KeyPrefix: "gameV2",
				KeyId:     gameId,
				IsDeleted: false,
			},
		})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildGameSyncInfo UpdateCommonIncrTimelineSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
	}

	log.DebugWithCtx(ctx, "SGuildSyncV2Helper.InitGuildGameSyncInfo success... uid: %d, guildId: %d, mapGameIds is %v",
		uid, guildId, mapGameIds)

	return nil
}

func (s *SGuildSyncV2Helper) InitGuildDonateSyncInfo(ctx context.Context, uid uint32, guildId uint32) (err error) {
	log.DebugWithCtx(ctx, "start call SGuildSyncV2Helper.InitGuildDonateSyncInfo, uid: %d, guildId: %d", uid, guildId)

	guildDonateCnt := uint32(0)
	{
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		resp, err := s.guildCli.GetGuildDonateCount(ctx, uid, &Guild.GetGuildDonateCountReq{GuildId: guildId})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildDonateSyncInfo GetGuildDonateCount failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}

		guildDonateCnt = resp.DonateCount
	}

	seqId, err := s.GenerateGuildSyncSeq(ctx, guildId)
	if nil != err {
		log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildDonateSyncInfo GenerateGuildSyncSeq failed, err: %+v, uid: %d, guildId: %d",
			err, uid, guildId)
		return err
	}

	{
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		err = s.guildTimeline2Cli.UpdateGuildDonateNumSeq(ctx, uid, &GuildTimeline2.UpdateGuildDonateNumSeqReq{
			GuildId: guildId,
			NumInfo: &GuildTimeline2.StGuildDonateNumSyncInfo{
				LastUpdateTime: uint32(time.Now().Unix()),
				LastSeqId:      uint32(seqId),
				DonateNum:      guildDonateCnt,
			},
		})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildDonateSyncInfo UpdateGuildDonateNumSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
	}

	var donateList []*Guild.GetGuildDonateResp
	{
		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		resp, err := s.guildCli.GetGuildDonateList(ctx, uid, &Guild.GetGuildDonateListReq{
			GuildId: guildId,
			Limit:   10,
			Offset:  0,
		})
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildDonateSyncInfo GetGuildDonateList failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
		donateList = append(donateList, resp.DonateList...)
	}

	{
		req := &GuildTimeline2.UpdateGuildDonateTopUserSeqReq{
			GuildId: guildId,
			TopInfo: &GuildTimeline2.StGuildDonateTopUserSyncInfo{
				LastUpdateTime: uint32(time.Now().Unix()),
				LastSeqId:      uint32(seqId),
			},
		}
		for _, donateItem := range donateList {
			req.TopInfo.DonateTopNList = append(req.TopInfo.DonateTopNList, &GuildTimeline2.DonateMem{
				Uid:  donateItem.Uid,
				Days: donateItem.DonateCount,
			})
		}

		ctx, cancelFunc := context.WithTimeout(ctx, 3*time.Second)
		err = s.guildTimeline2Cli.UpdateGuildDonateTopUserSeq(ctx, uid, req)
		cancelFunc()
		if nil != err {
			log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.InitGuildDonateSyncInfo UpdateGuildDonateTopUserSeq failed, err: %+v, uid: %d, guildId: %d",
				err, uid, guildId)
			return err
		}
	}

	log.DebugWithCtx(ctx, "SGuildSyncV2Helper.InitGuildDonateSyncInfo, uid: %d, guildId: %d, seqId: %d, donateList is %v",
		uid, guildId, seqId, donateList)

	return nil
}

// 获取用户所在公会的最新序号 seqId, guildId
func (s *SGuildSyncV2Helper) GetUserGuildSyncLatestSeq(ctx context.Context, uid uint32) (uint32, uint32, error) {
	var guildId uint32
	err := s.accountClient.GetUserGuild(ctx, uid, &guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.GetUserGuildSyncLatestSeq GetUserGuild failed... uid is %d, err is %v",
			uid, err)
		return 0, 0, err
	}

	if guildId == 0 {
		log.DebugWithCtx(ctx, "SGuildSyncV2Helper.GetUserGuildSyncLatestSeq user has no guild... uid is %d", uid)
		return 0, 0, nil
	}

	// 检查签到信息是否过期
	s.CheckAndUpdateGuildCheckinInf(ctx, uid, guildId)

	// 检查捐献信息是否过期
	s.CheckAndUpdateGuildDonateInf(ctx, uid, guildId)

	seqId, err := s.seqgenCli.RetrieveSequence(ctx, guildId, seqgen.NamespaceGuild, seqgen.KeyGuildV2)
	if err != nil {
		log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.GetUserGuildSyncLatestSeq RetrieveSequence failed...uid is %d, "+
			"guildId is %d, err is %v", uid, guildId, err)
		return 0, 0, err
	}

	return uint32(seqId), guildId, nil
}

func (s *SGuildSyncV2Helper) CheckIfGuildHaveTimeline(ctx context.Context, uid, guildId uint32) (bool, error) {
	resp, err := s.guildTimeline2Cli.CheckHaveGuildData(ctx, uid, &GuildTimeline2.CheckGuildDataIsExistReq{GuildId: guildId})
	if err != nil {
		log.ErrorWithCtx(ctx, "SGuildSyncV2Helper.CheckIfGuildHaveTimeline failed... uid is %d, guildId is %d, err is %v", uid, guildId, err)
		return false, err
	}

	return resp.GetIs_Exist(), nil
}

// 检查签到信息是否过期
func (s *SGuildSyncV2Helper) CheckAndUpdateGuildCheckinInf(ctx context.Context, uid, guildId uint32) bool {
	bNeedUpdate, bNeedCheckTimeline := false, true

	bNeedCheckTimeline = checkAndUpdateGuildCheckinInf(ctx, guildId)

	if bNeedCheckTimeline {

		// 拉取有更新的签到数的值
		ts, err := s.guildTimeline2Cli.GetGuildCheckInLastTime(ctx, uid, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndUpdateGuildCheckinInf.GetGuildCheckInLastTime failed... uid is %d, guildId is %d, err is %v",
				uid, guildId, err)
			bNeedUpdate = true
		}

		if ts > 0 {
			ut, nowTs := genLongDateByTs(int64(ts)), genLongDateByNow()
			if ut != nowTs {
				log.DebugWithCtx(ctx, "CheckAndUpdateGuildCheckinInf.GetGuildCheckInLastTime now is not update... ut is %s, "+
					"nowTs is %s", ut, nowTs)
				bNeedUpdate = true
			}
		}

		// 存储最新的 updateTs
		GuildSignInTsMap.Store(guildId, time.Now().Unix())
	}

	if bNeedUpdate {

		// 函数内部会进行计算更新当天的签到人数
		err := s.InitGuildCheckInSyncInfo(ctx, uid, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndUpdateGuildCheckinInf.InitGuildCheckInSyncInfo failed... uid: %d, guildId is %d, err is %v",
				uid, guildId, err)
			return false
		}

		log.DebugWithCtx(ctx, "CheckAndUpdateGuildCheckinInf.InitGuildCheckInSyncInfo success... uid is %d, guildId: %d", uid, guildId)
	}

	return true
}

func checkAndUpdateGuildCheckinInf(ctx context.Context, guildId uint32) bool {
	signInTs, ok := GuildSignInTsMap.Load(guildId)
	if !ok { // 不存在，过期，需要 update
		log.DebugWithCtx(ctx, "checkAndUpdateGuildDonateInf not exist... guildId is %d", guildId)
		return true
	}

	if ts, ok := signInTs.(int64); ok {

		if ts == 0 {
			return true
		}

		ut, nowTs := genLongDateByTs(ts), genLongDateByNow()
		if ut != nowTs {
			log.DebugWithCtx(ctx, "checkAndUpdateGuildDonateInf not equal... guildId is %d, ut is %s, nowTs is %s", ut, nowTs)
			return true
		}
	}

	return false

}

// 检查捐献信息是否过期
func (s *SGuildSyncV2Helper) CheckAndUpdateGuildDonateInf(ctx context.Context, uid, guildId uint32) bool {
	bNeedUpdate, bNeedCheckTimeline := false, true

	bNeedCheckTimeline = checkAndUpdateGuildDonateInf(ctx, guildId)

	if bNeedCheckTimeline {

		// 拉取有更新的捐献数的值
		ts, err := s.guildTimeline2Cli.GetGuildDonateLastTime(ctx, uid, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndUpdateGuildCheckinInf.GetGuildDonateLastTime failed... uid is %d, guildId is %d, err is %v",
				uid, guildId, err)
			bNeedUpdate = true
		}

		if ts > 0 {
			ut, nowTs := genLongDateByTs(int64(ts)), genLongDateByNow()
			if ut != nowTs {
				log.DebugWithCtx(ctx, "CheckAndUpdateGuildCheckinInf.GetGuildDonateLastTime now is not update... ut is %s, "+
					"nowTs is %s", ut, nowTs)
				bNeedUpdate = true
			}
		}

		// 存储最新的 updateTs
		GuildDonateTsMap.Store(guildId, time.Now().Unix())
	}

	if bNeedUpdate {

		// 函数内部会进行计算更新当天的捐献人数
		err := s.InitGuildDonateSyncInfo(ctx, uid, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndUpdateGuildCheckinInf.InitGuildDonateSyncInfo failed... uid: %d, guildId is %d, err is %v",
				uid, guildId, err)
			return false
		}
	}

	return true
}

func checkAndUpdateGuildDonateInf(ctx context.Context, guildId uint32) bool {
	signInTs, ok := GuildDonateTsMap.Load(guildId)
	if !ok { // 不存在，过期，需要 update
		return true
	}

	if ts, ok := signInTs.(int64); ok {

		if ts == 0 {
			return true
		}

		ut, nowTs := genLongDateByTs(ts), genLongDateByNow()
		if ut != nowTs {
			log.DebugWithCtx(ctx, "checkAndUpdateGuildDonateInf ut is %s, nowTs is %s", ut, nowTs)
			return true
		}
	}

	return false

}

func genLongDateByTs(ts int64) string {
	return time.Unix(ts, 0).Format("2006-01-02")
}

func genLongDateByNow() string {
	return time.Now().Format("2006-01-02")
}

// 触发更新公会扩展信息同步数据
// 扩展信息更新触发时机包括: 公会图标变化 公会公告 群排序
func (s *SGuildSyncV2Helper) UpdateGuildExtraDataSyncInfo(ctx context.Context, reqUid, guildId uint32) (err error) {
	if ok, _ := s.CheckIfGuildHaveTimeline(ctx, reqUid, guildId); !ok {
		err := s.InitGuildBaseSyncInfo(ctx, reqUid, guildId)
		if err == nil {
			s.InitGuildExtraSyncInfo(ctx, reqUid, guildId)
			s.InitGuildNumberSyncInfo(ctx, reqUid, guildId)
			s.InitGuildCheckInSyncInfo(ctx, reqUid, guildId)
			s.InitGuildAdminMemberSyncInfo(ctx, reqUid, guildId)
			s.InitGuildGroupSyncInfo(ctx, reqUid, guildId)
			s.InitGuildGameSyncInfo(ctx, reqUid, guildId)
			s.InitGuildDonateSyncInfo(ctx, reqUid, guildId)

			log.InfoWithCtx(ctx, "UpdateGuildExtraDataSyncInfo uid:%d, guildId:%d new init ", reqUid, guildId)
		}
	}

	log.DebugWithCtx(ctx, "UpdateGuildExtraDataSyncInfo uid:%d, guildId:%d", reqUid, guildId)

	// 查工会图标
	guildIconMd5, err := s.headImageCli.GetHeadImageMd5(ctx, 0, fmt.Sprintf("%d%s", guildId, protocol.TARGET_SUFFIX_GUILD))
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateGuildExtraDataSyncInfo headimage.GetHeadImageMd5 failed, err:%v, guildId:%d", err, guildId)
	}

	currTs := time.Now().Unix()
	// 生成seqid
	seqId, err := s.GenerateGuildSyncSeq(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateGuildExtraDataSyncInfo GenerateGuildSyncSeq failed, err:%v, guildId:%d", err, guildId)
		return err
	}

	extrainfo := &GuildTimeline2.StGuildExtraSyncInfo{
		LastUpdateTime: uint32(currTs),
		LastSeqId:      uint32(seqId),
		IconId:         guildIconMd5,
	}

	err = s.guildTimeline2Cli.UpdateGuildExtraInfoSeq(ctx, reqUid, &GuildTimeline2.UpdateGuildExtraInfoSeqReq{
		GuildId:   guildId,
		Extrainfo: extrainfo,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateGuildExtraDataSyncInfo guildTimeline.UpdateGuildExtraInfoSeq failed, err:%v, guildId:%d, uid:%d",
			err, guildId, reqUid)
		return protocol.NewExactServerError(nil, status.ErrGuildTimelineSvrSystemErr)
	}

	log.InfoWithCtx(ctx, "UpdateGuildExtraDataSyncInfo, uid:%d, guildId:%d, %+v", reqUid, guildId, extrainfo)
	return nil
}

// wuwei: [Potential performance issue]
// 拉取整个公会的成员列表, 大公会下有性能问题.
// 2017/11/29: 改为直接向公会主群广播, 不再拉取成员列表.
func (s *SGuildSyncV2Helper) NotifyGuildAllMemberSync(ctx context.Context, guildId uint32, pushV2Cli pushV2.IClient,
	pushLabel pushV2.PushLabelType) (err error) {
	log.DebugWithCtx(ctx, "NotifyGuildAllMember guildId:%d", guildId)
	guildInfoRes, err := s.guildCli.GetGuild(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "NotifyGuildAllMember GetGuild failed, err:%v, guildId:%d", err, guildId)
		return err
	}

	err = notifyhelper.NotifyData2GroupWithPushLabel(ctx, guildInfoRes.GetMainGroup(), uint32(syncPb.SyncReq_GUILD_V2), pushV2Cli, pushLabel)
	if err != nil {
		log.ErrorWithCtx(ctx, "NotifyGuildAllMember notifyhelper.NotifyData2Group failed, err:%v, guildId:%d, mainGroup:%d",
			err, guildId, guildInfoRes.GetMainGroup())
		return err
	}

	log.InfoWithCtx(ctx, "NotifyGuildAllMember guildId:%d, notify to main group:%d, mem count:%d done",
		guildId, guildInfoRes.GetMainGroup(), guildInfoRes.GetMemberCount())
	return nil
}

func (s *SGuildSyncV2Helper) CheckUserIsGuildAdmin(ctx context.Context, uid, guildId uint32) (guildRole int, groupRole int, bIsAdmin bool) {
	bIsAdmin = false
	guildRole = 3
	groupRole = 3

	vecCheckUID := []uint32{uid}

	//用户在公会中的角色
	guildMember, err := s.guildCli.GetGuildMember(ctx, uid, guildId)
	if err != nil && err.Code() == status.ErrGuildMemberNotExist {
		log.ErrorWithCtx(ctx, "[guildSyncV2::CheckUserIsGuildAdmin] GetGuildMember ERR_GUILD_MEMBER_NOT_EXIST, uid:%d, guildId:%d", uid, guildId)
		return
	} else if err != nil {
		log.ErrorWithCtx(ctx, "[guildSyncV2::CheckUserIsGuildAdmin] GetGuildMember. err:%v, uid:%d, guildId:%d", err, uid, guildId)
		return
	}

	if guildMember.GetRole() < 3 {
		guildRole = int(guildMember.GetRole())
		bIsAdmin = true
	} else {
		// 在各个公会群的管理员角色
		objCheckAdminResp, err := s.guildCli.CheckUserGroupAdminInfoInGuild(ctx, uid, vecCheckUID, 0, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "[guildSyncV2::CheckUserIsGuildAdmin] fail. CheckUserGroupAdminInfoInGuild err:%v, reqUID:%d guild:%d",
				err, uid, guildId)
			return
		}

		for _, stInfo := range objCheckAdminResp.GetUserGroupAdminInfList() {
			if len(stInfo.GetOwnerGroupList()) > 0 {
				groupRole = 1
				bIsAdmin = true
				break
			} else if len(stInfo.GetAdminGroupList()) > 0 {
				groupRole = 2
			}
		}

		if groupRole < 3 {
			bIsAdmin = true
		}
	}

	log.DebugWithCtx(ctx, "[guildSyncV2::CheckUserIsGuildAdmin] uid:%d, guild:%d, admin:%t", uid, guildId, bIsAdmin)
	return
}

// 触发更新公会成员变化
// 目前只对有权限的公会成员的变化(新增管理员 移除管理员...) 调用该接口
// 权限的公会成员包括: 会长/副会长/公会群主/公会群管理员 4种
func (s *SGuildSyncV2Helper) UpdateGuildMemberSyncInfo(ctx context.Context, guildId uint32, uid uint32, isDel bool) error {
	if ok, _ := s.CheckIfGuildHaveTimeline(ctx, uid, guildId); !ok {
		err := s.InitGuildBaseSyncInfo(ctx, uid, guildId)
		if err == nil {
			s.InitGuildExtraSyncInfo(ctx, uid, guildId)
			s.InitGuildNumberSyncInfo(ctx, uid, guildId)
			s.InitGuildCheckInSyncInfo(ctx, uid, guildId)
			s.InitGuildAdminMemberSyncInfo(ctx, uid, guildId)
			s.InitGuildGroupSyncInfo(ctx, uid, guildId)
			s.InitGuildGameSyncInfo(ctx, uid, guildId)
			s.InitGuildDonateSyncInfo(ctx, uid, guildId)

			log.ErrorWithCtx(ctx, "[guildSyncV2::UpdateGuildMemberSyncInfo] uid:%d guild:%d NEW init ", uid, guildId)
		}
	}

	log.DebugWithCtx(ctx, "[guildSyncV2::UpdateGuildMemberSyncInfo] -- guildId:%d uid:%d isDel:%t", guildId, uid, isDel)

	// 生成seqid
	seqId, err := s.GenerateGuildSyncSeq(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "[guildSyncV2::UpdateGuildMemberSyncInfo] -- guild_id = %d, guildSyncV2::GenerateGuildSyncSeq err:%v",
			guildId, err)
		return err
	}

	err = s.guildTimeline2Cli.UpdateCommonIncrTimelineSeq(ctx, uid, &GuildTimeline2.UpdateCommonIncrTimelineSeqReq{
		GuildId: guildId,
		SeqId:   uint32(seqId),
		Data: &GuildTimeline2.StCommonIncrTimelineData{
			KeyPrefix: "managerV2",
			KeyId:     uid,
			IsDeleted: isDel,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "[guildSyncV2::UpdateGuildMemberSyncInfo] -- guild_id = %d, UpdateManagerIncrTimelineSeq err:%v",
			guildId, err)
		return protocol.NewExactServerError(codes.Internal, status.ErrGuildTimelineSvrSystemErr)
	}

	return nil
}

// 触发更新公会群组变化
func (s *SGuildSyncV2Helper) UpdateGuildGroupSyncInfo(ctx context.Context, guildId uint32, groupID uint32, isDel bool) error {
	if ok, _ := s.CheckIfGuildHaveTimeline(ctx, 0, guildId); !ok {
		err := s.InitGuildBaseSyncInfo(ctx, 0, guildId)
		if err == nil {
			s.InitGuildExtraSyncInfo(ctx, 0, guildId)
			s.InitGuildNumberSyncInfo(ctx, 0, guildId)
			s.InitGuildCheckInSyncInfo(ctx, 0, guildId)
			s.InitGuildAdminMemberSyncInfo(ctx, 0, guildId)
			s.InitGuildGroupSyncInfo(ctx, 0, guildId)
			s.InitGuildGameSyncInfo(ctx, 0, guildId)
			s.InitGuildDonateSyncInfo(ctx, 0, guildId)

			log.ErrorWithCtx(ctx, "[guildSyncV2::UpdateGuildGroupSyncInfo] uid:%d guild:%d NEW init ", 0, guildId)
		}
	}

	log.DebugWithCtx(ctx, "[guildSyncV2::UpdateGuildGroupSyncInfo] -- guildId:%d group:%d isDel:%t", guildId, groupID, isDel)
	// 生成seqid
	seqId, err := s.GenerateGuildSyncSeq(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "[guildSyncV2::UpdateGuildGroupSyncInfo] -- guild_id = %d, guildSyncV2::GenerateGuildSyncSeq err:%v",
			guildId, err)
		return err
	}

	err = s.guildTimeline2Cli.UpdateCommonIncrTimelineSeq(ctx, guildId, &GuildTimeline2.UpdateCommonIncrTimelineSeqReq{
		GuildId: guildId,
		SeqId:   uint32(seqId),
		Data: &GuildTimeline2.StCommonIncrTimelineData{
			KeyPrefix: "groupV2",
			KeyId:     groupID,
			IsDeleted: isDel,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "[guildSyncV2::UpdateGuildGroupSyncInfo] -- guild_id = %d, UpdateGroupIncrTimelineSeq err:%v",
			guildId, err)
		return protocol.NewExactServerError(codes.SystemError, status.ErrGuildTimelineSvrSystemErr)
	}

	log.InfoWithCtx(ctx, "[guildSyncV2::UpdateGuildGroupSyncInfo] -- guild:%d group:%d seq:%d", guildId, groupID, seqId)
	return nil
}
