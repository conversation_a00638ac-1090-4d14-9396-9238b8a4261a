package server

import (
	"context"
	"errors"
	pb "golang.52tt.com/protocol/services/expsvr"
	"golang.52tt.com/services/expsvr/internal/conf"
	"golang.52tt.com/services/expsvr/internal/manager"
)

type ExpServer struct {
	*manager.Manager

	sc *conf.ServiceConfigT
}

func (s *ExpServer) AddUserMedal(ctx context.Context, req *pb.AddUserMedalReq) (*pb.AddUserMedalResp, error) {
	return nil, errors.New("unimplemented")
}

func (s *ExpServer) GetUserMedal(ctx context.Context, req *pb.GetUserMedalReq) (*pb.GetUserMedalResp, error) {
	return nil, errors.New("unimplemented")
}

func (s *ExpServer) BatGetUserMedal(ctx context.Context, req *pb.BatGetUserMedalReq) (*pb.BatGetUserMedalResp, error) {
	return nil, errors.New("unimplemented")
}

func NewExpServer(ctx context.Context, sc *conf.ServiceConfigT) (*ExpServer, error) {
	if err := sc.ParseLevelExp(); err != nil {
		return nil, err
	}
	mgr, err := manager.NewManager(sc)
	if err != nil {
		return nil, err
	}

	s := &ExpServer{
		sc:      sc,
		Manager: mgr,
	}

	// 处理在线增加经验任务
	go s.AsyncTaskHandler(context.Background())
	// 定时清理发放记录
	go s.CleanUserExpLog(context.Background())

	return s, nil
}

func (s *ExpServer) ShutDown() {
	s.Manager.ShutDown()
}
