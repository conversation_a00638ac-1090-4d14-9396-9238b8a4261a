package server

import (
    "context"
    "errors"
    "fmt"
    apicenter "golang.52tt.com/clients/apicenter/apiserver"
    "golang.52tt.com/pkg/hash/rdb"
    "math/rand"
    "time"

    backpacksender "golang.52tt.com/clients/backpack-sender"
    "golang.52tt.com/clients/medalgo"
    "golang.52tt.com/pkg/log"
    limitReport "golang.52tt.com/services/molebeat/tools/limit_report_award"

    "golang.52tt.com/clients/bots/monkey"
    "golang.52tt.com/services/molebeat/mongo"

    "golang.52tt.com/clients/backpack"
    userPresent "golang.52tt.com/clients/userpresent"

    channelolCli "golang.52tt.com/clients/channelol"
    headwear "golang.52tt.com/clients/headwear-go"
    numeri "golang.52tt.com/clients/numeric-go"
    publicnotice "golang.52tt.com/clients/public-notice"

    chPer "golang.52tt.com/clients/channel-personalization"
    missionTL "golang.52tt.com/clients/missiontimeline"
    "golang.52tt.com/clients/seqgen/v2"

    "golang.52tt.com/clients/currency"
    "golang.52tt.com/clients/exp"

    "golang.52tt.com/clients/account"
    "golang.52tt.com/clients/channelol"
    headImageCli "golang.52tt.com/clients/headimage"
    PushNotification "golang.52tt.com/clients/push-notification/v2"
    Timeline "golang.52tt.com/clients/timeline"
    "golang.52tt.com/pkg/hash/hashdb"
    _ "golang.52tt.com/pkg/monitor/smallprocess"
    "golang.52tt.com/services/molebeat/kafka"
    "golang.52tt.com/services/molebeat/lottery"
    "golang.52tt.com/services/molebeat/mq"
    "golang.52tt.com/services/molebeat/timer"
    "golang.52tt.com/services/molebeat/tools/robotUid"

    "golang.52tt.com/clients/channel"
    pb "golang.52tt.com/protocol/services/molebeat"
    "golang.52tt.com/services/molebeat/cache"
    "golang.52tt.com/services/molebeat/conf"
)

type MoleBeatServer struct {
    sc                 *conf.ServiceConfigT
    cacheClient        *cache.MoleBeatCache
    rankListCache      *RankListCache
    reportRankingCache *ReportRankingtCache
    aClient            *account.Client
    channelClient      *channelol.Client
    lotteryHandle      *lottery.MoleLotteryHandle
    mqHandles          *mq.MqHandlesT
    statClient         *cache.StatCache
    monkeyCli          *monkey.Client
    kfkProduce         *kafka.ChannelInfoProduce
    kfkSub             *kafka.ChannelInfoSubscriber
    timer              *timer.TimerHandleT
}

type RankListInfos []*pb.RankListInfo

type RankListCache struct {
    cache          RankListInfos
    totalRecordNum int64
    ts             int64
}

type ReportRankingInfos []*pb.ReportRankingInfo
type ReportRankingtCache struct {
    cache ReportRankingInfos
    ts    int64
}

const (
    RankListCntMax    = 100
    RankListCacheTime = 2
)

func NewMoleBeatServer(ctx context.Context) (*MoleBeatServer, error) {

    sc := &conf.ServiceConfigT{}

    cfgPath := ctx.Value("configfile").(string)
    if cfgPath == "" {
        return nil, errors.New("configfile not exist")
    }
    err := sc.Parse(cfgPath)
    if err != nil {
        return nil, err
    }
    err = sc.CheckConfig()
    if err != nil {
        return nil, err
    }
    // 设置redis的检查周期
    if sc.RedisCheckWait > 0 {
        rdb.REDIS_CHECK_WAIT = sc.RedisCheckWait
    }
    log.InfoWithCtx(ctx, "redis checked internal:%d", rdb.REDIS_CHECK_WAIT)

    //fmt.Println(cfgPath, sc.GetMongoConfig(), sc.GetRedisHashConfig())
    mDao, err := mongo.NewMongoDao(sc.GetMongoConfig())
    if err != nil {
        log.ErrorWithCtx(ctx, "Failed to create mongodb %v", err)
        return nil, err
    }

    _ = mDao.CreateIndexes()

    mDaoFirstType, err := mongo.NewMongoDao(sc.GetMongoFirstTypeConfig())
    if err != nil {
        log.ErrorWithCtx(ctx, "Failed to create mongodb %v", err)
        return nil, err
    }

    _ = mDaoFirstType.CreateIndexes()

    mLotteryRecordDao, err := mongo.NewMongoDao(sc.GetLotteryRecordMongoConfig())
    if err != nil {
        log.ErrorWithCtx(ctx, "Failed to create mongodb %v", err)
        return nil, err
    }
    _ = mLotteryRecordDao.CreateLotteryRecordIndexes()

    mLotteryRecordDaoFirstType, err := mongo.NewMongoDao(sc.GetLotteryRecordFirtTypeMongoConfig())
    if err != nil {
        log.ErrorWithCtx(ctx, "Failed to create mongodb: %v", err)
        return nil, err
    }
    _ = mLotteryRecordDaoFirstType.CreateLotteryRecordIndexes()

    err = sc.GetMoleConfig().Init()
    if err != nil {
        fmt.Println(err)
        return nil, err
    }
    // 读配置中心，覆盖上面的配置
    _ = sc.WatchFile(ctx, "/data/oss/conf-center/tt/molebeat-gameinfo.json")

    limitReport.NewList(sc.GetMoleConfig().RecordGifts)

    redisHashClient, err := hashdb.NewKHashDB(sc.GetRedisHashConfig())
    if err != nil {
        return nil, err
    }
    cacheClient := cache.NewMoleBeatCache(redisHashClient)

    log.InfoWithCtx(ctx, "sc.GetRedisStatConfig():%+v", sc.GetRedisStatConfig())
    redisStatClient, err := hashdb.NewKHashDB(sc.GetRedisStatConfig())
    if err != nil {
        return nil, err
    }
    statClient := cache.NewStatCache(redisStatClient)
    publicNoticeCli, err := publicnotice.NewClient()
    if err != nil {
        log.ErrorWithCtx(ctx, "publicnotice.NewClient, err:%s", err.Error())
    }
    pushClient, err := PushNotification.NewClient()
    if err != nil {
        log.ErrorWithCtx(ctx, "PushNotification.NewClient err: %s", err.Error())
        return nil, err
    }
    accountClient, err := account.NewClient()
    if err != nil {
        log.ErrorWithCtx(ctx, "New accountClient err:%s", err.Error())
        return nil, err
    }

    headImageClient := headImageCli.NewClient()
    if headImageClient == nil {
        log.ErrorWithCtx(ctx, "New trace head image client failed:%+v", err)
        return nil, err
    }
    expCli := exp.NewClient()
    if expCli == nil {
        log.ErrorWithCtx(ctx, "New exp failed")
        return nil, err
    }
    currCli := currency.NewClient()
    if currCli == nil {
        log.ErrorWithCtx(ctx, "New currency failed")
        return nil, err
    }
    tlCli := missionTL.NewClient()
    if tlCli == nil {
        log.ErrorWithCtx(ctx, "New missionTL failed")
        return nil, err
    }
    seqgenCli, err := seqgen.NewClient()
    if err != nil {
        log.ErrorWithCtx(ctx, "New seqgen failed")
        return nil, err
    }
    timelineCli := Timeline.NewClient()
    //timelineCli := Timeline.NewClient()
    if timelineCli == nil {
        log.ErrorWithCtx(ctx, "New Timeline failed")
        return nil, err
    }
    medalCli, err := medalgo.NewClient()
    if err != nil {
        log.ErrorWithCtx(ctx, "New medal failed")
        return nil, err
    }
    hwCli := headwear.NewClient()
    if hwCli == nil {
        log.ErrorWithCtx(ctx, "New headwear failed")
        return nil, err
    }
    pkgCli := backpack.NewClient()
    if pkgCli == nil {
        log.ErrorWithCtx(ctx, "New backpack failed")
        return nil, err
    }

    upresent := userPresent.NewClient()
    if upresent == nil {
        log.ErrorWithCtx(ctx, "New userPresent failed")
        return nil, err
    }
    chPerCli, err := chPer.NewClient()
    if err != nil {
        log.ErrorWithCtx(ctx, "New channel-personalization failed")
        return nil, err
    }
    numeriCli := numeri.NewIClient()
    if numeriCli == nil {
        log.ErrorWithCtx(ctx, "New numeric failed")
        return nil, err
    }
    cholCli := channelolCli.NewClient()
    if cholCli == nil {
        log.ErrorWithCtx(ctx, "New channelolCli failed")
        return nil, err
    }

    monkeyCli, err := monkey.NewClient()
    if err != nil {
        log.ErrorWithCtx(ctx, "monkey.NewClient err")
        //return nil, err
    }

    backpackSenderClient, err := backpacksender.NewClient()
    if err != nil {
        return nil, err
    }

    channelClient := channel.NewClient()
    apiCenterClient := apicenter.NewClient()

    unAwardMsgHandle := mq.NewUnAwardMsgHandle(mq.UnAward, mq.MsgHandleTypeMap[mq.UnAward], pushClient, accountClient)
    allBcHandle := mq.NewAllBroadcastMsgHandle(mq.BroadCastAll, mq.MsgHandleTypeMap[mq.BroadCastAll], sc, accountClient, headImageClient, channelClient, publicNoticeCli)
    awardNotifyMsgHandle := mq.NewAwardNotifyMsgHandle(mq.AwardNotify, mq.MsgHandleTypeMap[mq.AwardNotify], pushClient, accountClient)
    awardMsgHandle2 := mq.NewAwardMsgHandle(mq.LotteryAwardMedal, mq.MsgHandleTypeMap[mq.LotteryAwardMedal], statClient, accountClient, expCli, currCli, tlCli, seqgenCli, timelineCli,
        medalCli, hwCli, pkgCli, upresent, chPerCli, numeriCli, cholCli, pushClient, cacheClient, mLotteryRecordDao, backpackSenderClient, sc, apiCenterClient)
    awardMsgHandle3 := mq.NewAwardMsgHandle(mq.LotteryAwardHeadWare, mq.MsgHandleTypeMap[mq.LotteryAwardHeadWare], statClient, accountClient, expCli, currCli, tlCli, seqgenCli, timelineCli,
        medalCli, hwCli, pkgCli, upresent, chPerCli, numeriCli, cholCli, pushClient, cacheClient, mLotteryRecordDao, backpackSenderClient, sc, apiCenterClient)
    awardMsgHandle4 := mq.NewAwardMsgHandle(mq.LotteryAwardPackage, mq.MsgHandleTypeMap[mq.LotteryAwardPackage], statClient, accountClient, expCli, currCli, tlCli, seqgenCli, timelineCli,
        medalCli, hwCli, pkgCli, upresent, chPerCli, numeriCli, cholCli, pushClient, cacheClient, mLotteryRecordDao, backpackSenderClient, sc, apiCenterClient)
    awardMsgHandle5 := mq.NewAwardMsgHandle(mq.LotteryAwardChannelEffect, mq.MsgHandleTypeMap[mq.LotteryAwardChannelEffect], statClient, accountClient, expCli, currCli, tlCli, seqgenCli, timelineCli,
        medalCli, hwCli, pkgCli, upresent, chPerCli, numeriCli, cholCli, pushClient, cacheClient, mLotteryRecordDao, backpackSenderClient, sc, apiCenterClient)
    handles := []mq.MsgHandleInterface{
        unAwardMsgHandle,
        allBcHandle,
        awardMsgHandle2,
        awardNotifyMsgHandle,
        awardMsgHandle3,
        awardMsgHandle4,
        awardMsgHandle5,
    }

    msgHandles := mq.NewMsgHandlesT(handles)
    mqHandles := mq.NewMqHandlesT(msgHandles, cacheClient)

    lotteryHandle := lottery.NewMoleLotteryHandle(cacheClient, mqHandles, statClient, mDao, mDaoFirstType, mLotteryRecordDao, mLotteryRecordDaoFirstType, monkeyCli, sc)
    lotteryHandle.Reload(ctx)

    kfkProduce := kafka.NewChannelInfoProduce(sc.GetKafkaConfig().BrokerList(), sc.GetKafkaConfig().ClientID, sc.GetKafkaConfig().Topics)

    handleInterface := timer.NewAwardHandle(cacheClient, statClient, sc, mqHandles, kfkProduce, lotteryHandle)
    timerHandle := timer.NewTimerHandle(ctx, sc, handleInterface)
    timerHandle.Start()

    aClient, err := account.NewClient()
    if err != nil {
        return nil, err
    }
    cClient := channelol.NewClient()
    rlInfos := make([]*pb.RankListInfo, 0, 100)
    rankListCache := &RankListCache{
        cache:          rlInfos,
        totalRecordNum: 0,
        ts:             0,
    }
    rpInfos := make([]*pb.ReportRankingInfo, 0, 100)
    reportRankingCache := &ReportRankingtCache{
        cache: rpInfos,
        ts:    0,
    }

    kfkSub, err := kafka.NewChannelInfoSubscriber(sc.GetSubKafkaConfig().ClientID, sc.GetSubKafkaConfig().GroupID, sc.GetSubKafkaConfig().TopicList(), sc.GetSubKafkaConfig().BrokerList(), sc, cacheClient, mqHandles, lotteryHandle)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewChannelInfoSubscriber err %s ", err.Error())
        return nil, err
    }
    _ = kfkSub.Start()

    return &MoleBeatServer{
        sc:                 sc,
        cacheClient:        cacheClient,
        rankListCache:      rankListCache,
        reportRankingCache: reportRankingCache,
        aClient:            aClient,
        channelClient:      cClient,
        mqHandles:          mqHandles,
        lotteryHandle:      lotteryHandle,
        statClient:         statClient,
        monkeyCli:          monkeyCli,
        kfkProduce:         kfkProduce,
        kfkSub:             kfkSub,
        timer:              timerHandle,
    }, nil
}

func (s *MoleBeatServer) Lottery(ctx context.Context, gameid, uid, chid, beatCnt uint32, bFirst bool, gameConfigBeginTime int64) (lotteryInfo *mongo.MoleLotteryInfo, err error) {
    lotteryInfo = &mongo.MoleLotteryInfo{}
    if uidSize, err := s.cacheClient.GetUserBeatStatusCnt(ctx, gameid, chid, cache.Capture, cache.Award); err == nil {
        log.DebugWithCtx(ctx, "Lottery GetUserBeatStatusCnt uidsize:%d, gameid:%d, uid:%d,  chid:%d, beatcnt:%d", uidSize, gameid, uid, chid, beatCnt)
        // 同房间至少2人点中
        if uidSize >= 1 {
            if lotteryInfo, err = s.lotteryHandle.Handle(ctx, gameid, chid, uid, beatCnt, gameConfigBeginTime); err == nil {
                //log.DebugWithCtx(ctx,"Lottery end 279,  uidsize:%d, gameid:%d, uid:%d, chid:%d, beatcnt:%d, lotteryInfo:%+v", uidSize, gameid, uid, chid, beatCnt, lotteryInfo)
            } else {
                log.ErrorWithCtx(ctx, "Lottery GetUserBeatStatusCnt uidsize:%d, gameid:%d, uid:%d,  chid:%d, beatcnt:%d,err:%+v", uidSize, gameid, uid, chid, beatCnt, err)
                return nil, err
            }
        } else {
            err = s.cacheClient.AddChannelUserBeatStatus(ctx, gameid, chid, uid, cache.Capture)
            if err != nil {
                return nil, err
            }

        }
        if bFirst {
            _ = s.cacheClient.ExpireChannelUserBeatStatus(ctx, gameid, chid, uid, 10*3600*24*time.Second)
        }
    } else {
        //log.DebugWithCtx(ctx,"Lottery end 294,  uidsize:%d, gameid:%d, uid:%d, chid:%d, beatcnt:%d", uidSize, gameid, uid, chid, beatCnt)
        return lotteryInfo, err
    }
    return lotteryInfo, nil
}

func (s *MoleBeatServer) ReportBeat(ctx context.Context, in *pb.MoleBeatReportReq) (out *pb.MoleBeatReportRsp, err error) {
    out = &pb.MoleBeatReportRsp{}
    gameid, gameConfigBeginTime := s.sc.GetMoleConfig().GetCurrentGameId(ctx)
    log.DebugWithCtx(ctx, "GetCurrentGameId gameid:%d, in:%+v", gameid, in)
    if gameid == 0 {
        log.DebugWithCtx(ctx, "GetCurrentGameId err,return nil,in:%+v", in)
        return out, nil //errors.New("game not start")
    }
    //in.BeatCnt = 15 /* 临时改15次 */
    if in.GetBeatCnt() > 15 {
        log.WarnWithCtx(ctx, "ReportBeat GetBeatCnt err, cnt:%d, in:%+v", in.GetBeatCnt())
        return out, nil
    }
    //log.DebugWithCtx(ctx,"GetCurrentGameId gameid:%d, in:%+v", gameid, in)
    /*chid, err := s.cacheClient.GetUserPlayChannel(gameid, in.GetUid())
      if err != nil {
      	log.WarnWithCtx(ctx,"GetUserPlayChannel err:", err.Error())
      	return out, err
      }
      if chid != 0 {
      	log.WarnWithCtx(ctx,"already report, gameid:%d, chid:%d, in: %+v", gameid, chid, in)
      	return out, err
      }*/

    cnt, err := s.cacheClient.IncrUserPlayChannel(ctx, gameid, in.GetUid())
    if err != nil {
        log.WarnWithCtx(ctx, "IncrUserPlayChannel err:", err.Error())
        return out, err
    }
    if cnt >= 2 {
        log.WarnWithCtx(ctx, "IncrUserPlayChannel err, cnt:%d, in:%+v", cnt, in)
        return out, err
    }

    nowTime := time.Now().Unix()
    /*	var currentGameTime int64
    	gameEndTime := currentGameTime + 30
    	if nowTime <= gameEndTime {
    		log.WarnWithCtx(ctx,"report time err, gameid:%d, in:%+v", gameid, in)
    		return out, err
    	}*/
    gameBeginTime, err := s.cacheClient.GetEachGameBeginTime(ctx, gameid)
    if err != nil {
        log.WarnWithCtx(ctx, "GetEachGameBeginTime err:", err.Error())
        return out, err
    }
    if gameBeginTime == 0 {
        _ = s.cacheClient.SetEachGameBeginTime(ctx, gameid, nowTime)
    }
    reportTime, err := s.cacheClient.GetChannelReportTime(ctx, gameid, in.GetChid())
    if err != nil {
        log.WarnWithCtx(ctx, "GetChannelReportTime err:", err.Error())
        return out, err
    }
    /*err = s.cacheClient.AddUserPlayChannel(gameid, in.GetUid(), in.GetChid())
      if err != nil {
      	log.WarnWithCtx(ctx,"AddUserPlayChannel err:", err.Error())
      	return out, err
      }*/
    _ = s.statClient.SaveHitCnt(ctx, gameConfigBeginTime, cache.HIT)
    _ = s.statClient.SaveGameRoundHitCnt(ctx, gameConfigBeginTime, cache.HIT, in.GetUid())
    if reportTime == 0 {
        log.DebugWithCtx(ctx, "reportTime = 0, %+v", in)
        _ = s.cacheClient.AddChannelReportTime(ctx, gameid, in.GetChid(), nowTime)
        if err = s.cacheClient.ReportUserBeat(ctx, gameid, in.GetChid(), in.GetUid(), in.GetBeatCnt()); err == nil {
            if in.GetBeatCnt() < s.sc.GetMoleConfig().AttachAtleastCnt {
                log.InfoWithCtx(ctx, "ReportBeat req:%+v, rsp: %+v", in, out)
                return out, nil
            } else {
                lotteryInfo, err := s.Lottery(ctx, gameid, in.GetUid(), in.GetChid(), in.GetBeatCnt(), true, gameConfigBeginTime)
                if err != nil {
                    log.WarnWithCtx(ctx, "Lottery err:", err.Error())
                    return out, err
                }
                out.AwardUrl = lotteryInfo.PoolInfo.ImUrl
                out.AwardName = lotteryInfo.PoolInfo.Name
                log.InfoWithCtx(ctx, "ReportBeat req:%+v,rsp:%+v", in, out)
                return out, nil
            }
        } else {
            log.WarnWithCtx(ctx, "ReportUserBeat err:", err.Error())
            return out, err
        }
    } else {
        if nowTime-reportTime > int64(s.sc.GetMoleConfig().GetDelayTime(ctx)+40) {
            log.ErrorWithCtx(ctx, "reportTime time err:%d, %+v", nowTime-reportTime, in)
            if in.GetBeatCnt() >= s.sc.GetMoleConfig().AttachAtleastCnt {
                err = s.mqHandles.PushUnAward(ctx, in.GetUid(), in.GetChid())
                if err == nil {
                    _ = s.cacheClient.AddChannelUserBeatStatus(ctx, gameid, in.GetChid(), in.GetUid(), cache.NotCapture)
                }
            }
        } else {
            if err = s.cacheClient.ReportUserBeat(ctx, gameid, in.GetChid(), in.GetUid(), in.GetBeatCnt()); err == nil {
                if in.GetBeatCnt() < s.sc.GetMoleConfig().AttachAtleastCnt {
                    log.InfoWithCtx(ctx, "ReportBeat req:%+v, rsp:%+v", in, out)
                    return out, nil
                } else {
                    lotteryInfo, err := s.Lottery(ctx, gameid, in.GetUid(), in.GetChid(), in.GetBeatCnt(), true, gameConfigBeginTime)
                    if err != nil {
                        log.WarnWithCtx(ctx, "Lottery err:", err.Error())
                        return out, err
                    }
                    out.AwardUrl = lotteryInfo.PoolInfo.ImUrl
                    out.AwardName = lotteryInfo.PoolInfo.Name
                    log.InfoWithCtx(ctx, "ReportBeat req:%+v, rsp:%+v", in, out)
                    return out, nil
                }
            } else {
                log.WarnWithCtx(ctx, "ReportUserBeat err:", err.Error())
                return out, err
            }
        }
        return out, nil

    }
}

func (s *MoleBeatServer) StartMoleBeat(ctx context.Context, _ *pb.StartMoleBeatReq) (out *pb.StartMoleBeatResp, err error) {
    out = new(pb.StartMoleBeatResp)
    isOpen, updateTime, err := s.cacheClient.StartMoleBeat(ctx)
    if err != nil {
        return
    }

    out.UpdateTime = updateTime
    out.IsOpen = isOpen

    return
}

func (s *MoleBeatServer) StopMoleBeat(ctx context.Context, _ *pb.StopMoleBeatReq) (out *pb.StopMoleBeatResp, err error) {
    out = new(pb.StopMoleBeatResp)
    isOpen, updateTime, err := s.cacheClient.StopMoleBeat(ctx)
    if err != nil {
        return
    }

    out.UpdateTime = updateTime
    out.IsOpen = isOpen

    return
}

func (s *MoleBeatServer) GetMoleAttackGameConfig(ctx context.Context, in *pb.GetMoleAttackGameConfigReq) (out *pb.GetMoleAttackGameConfigRsp, err error) {
    out = &pb.GetMoleAttackGameConfigRsp{}

    log.DebugWithCtx(ctx, "GetMoleAttackGameConfig in: %+v", in)
    /*if s.sc.GetMoleConfig().WhiteListOpen && len(s.sc.GetMoleConfig().AccessWhiteList) != 0 {
    	gotten := false
    	for _, v := range s.sc.GetMoleConfig().AccessWhiteList {
    		if v == in.GetUid() {
    			gotten = true
    			break
    		}
    	}
    	if !gotten {
    		return out, nil
    	}
    }*/

    // 判断 生产 灰度 环境
    env := s.sc.GetMoleConfig().Env
    log.DebugWithCtx(ctx, "molebeat svr env: %d, in env: %d", env, in.Env)
    if env == uint32(pb.GetMoleAttackGameConfigReq_ENV_STAGING) {
        if in.Env != uint32(pb.GetMoleAttackGameConfigReq_ENV_STAGING) {
            log.ErrorWithCtx(ctx, "GetMoleAttackGameConfig err svr env: %d, in env: %d", env, in.Env)
            return out, nil
        }
    } else if env == uint32(pb.GetMoleAttackGameConfigReq_ENV_PROD) {
        if in.Env != 0 {
            log.ErrorWithCtx(ctx, "GetMoleAttackGameConfig err svr env: %d, in env: %d", env, in.Env)
            return out, nil
        }
    } else if env == uint32(pb.GetMoleAttackGameConfigReq_ENV_TESTING) {
        /*加测试环境*/
    } else {
        return out, nil
    }

    updateTime, err := time.ParseInLocation("2006-01-02 15:04:05", s.sc.GetMoleConfig().UpdateTime, time.Local)
    if err != nil {
        return out, nil
    }
    beginTime, err := time.ParseInLocation("2006-01-02 15:04:05", s.sc.GetMoleConfig().BeginTime, time.Local)
    if err != nil {
        return out, nil
    }
    endTime, err := time.ParseInLocation("2006-01-02 15:04:05", s.sc.GetMoleConfig().EndTime, time.Local)
    if err != nil {
        return out, nil
    }
    newYearTime, err := time.ParseInLocation("2006-01-02 15:04:05", s.sc.GetMoleConfig().NewYearTime, time.Local)
    if err != nil {
        return out, nil
    }

    isOpen, realUpdateTime, err := s.cacheClient.GetMoleBeatUpdateTime(ctx, updateTime.Unix())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetMoleBeatUpdateTime err：%v", err)
    } else {
        updateTime = time.Unix(int64(realUpdateTime), 0)
        if !isOpen {
            out = &pb.GetMoleAttackGameConfigRsp{
                ConfigLastChangeTs: uint32(updateTime.Unix()),
                GameName:           s.sc.GetMoleConfig().GameName,
                PeriodInfoList: []*pb.MoleAttackGamePeriodInfo{{
                    PeriodBeginTs:  realUpdateTime,
                    PeriodFinishTs: realUpdateTime,
                }},
                WhiteUids: s.sc.GetMoleConfig().AccessWhiteList,
                WhiteOpen: s.sc.GetMoleConfig().WhiteListOpen,
            }
            log.InfoWithCtx(ctx, "GetMoleAttackGameConfig, req:%+v, rsp:%+v ", in, out)
            return
        }
    }

    out = &pb.GetMoleAttackGameConfigRsp{
        ConfigLastChangeTs:             uint32(updateTime.Unix()),
        GameName:                       s.sc.GetMoleConfig().GameName,
        GameBeginTs:                    uint32(beginTime.Unix()),
        GameFinishTs:                   uint32(endTime.Unix()),
        RandomGamestartDelaySecond:     s.sc.GetMoleConfig().StartDelay,
        RandomGamefinReportDelaySecond: s.sc.GetMoleConfig().ReportDelay,
        GamePrepareSecond:              s.sc.GetMoleConfig().PrepareSecond,
        GameDurationSecond:             s.sc.GetMoleConfig().DurationSecond,
        MoleAppearCnt:                  s.sc.GetMoleConfig().MoleAppearCnt,
        AttackAtleastCnt:               s.sc.GetMoleConfig().AttachAtleastCnt,
        NewYearTime:                    newYearTime.Unix(),
        NeweYearSecond:                 s.sc.GetMoleConfig().NewYearSecond,
        MoleGameType:                   s.sc.GetMoleConfig().GameType,
        WhiteUids:                      s.sc.GetMoleConfig().AccessWhiteList,
        WhiteOpen:                      s.sc.GetMoleConfig().WhiteListOpen,
        UiRes:                          &pb.UiRes{},
    }
    for _, periodInfo := range s.sc.GetMoleConfig().PeriodList {
        beginTime, err := time.ParseInLocation("2006-01-02 15:04:05", periodInfo.BeginTime, time.Local)
        if err != nil {
            return out, nil
        }
        endTime, err := time.ParseInLocation("2006-01-02 15:04:05", periodInfo.EndTime, time.Local)
        if err != nil {
            return out, nil
        }
        periodInfo := &pb.MoleAttackGamePeriodInfo{
            PeriodBeginTs:    uint32(beginTime.Unix()),
            PeriodFinishTs:   uint32(endTime.Unix()),
            PeriodIntervalTs: periodInfo.Interval,
        }
        out.PeriodInfoList = append(out.PeriodInfoList, periodInfo)
    }

    // 填充ui资源
    if s.sc.GetMoleConfig().UiRes != nil {
        out.UiRes.Url = s.sc.GetMoleConfig().UiRes.Url
        out.UiRes.Md5 = s.sc.GetMoleConfig().UiRes.Md5
    }

    log.InfoWithCtx(ctx, "GetMoleAttackGameConfig, req:%+v, rsp:%+v ", in, out)
    return out, nil
}

func (s *MoleBeatServer) GetMoleBeatRankList(ctx context.Context, in *pb.RequestRankListReq) (out *pb.RequestRankListResp, err error) {
    out = &pb.RequestRankListResp{}
    //return out, nil
    ctx, cancel := context.WithTimeout(ctx, time.Second*10)
    defer cancel()
    count := in.PageNum * in.PageIdx
    if count > RankListCntMax {
        log.ErrorWithCtx(ctx, "GetMoleBeatRankList err, count:%d, RankListCntMax:%d", count, RankListCntMax)
    } else {
        //get curr user info
        sum, err := s.cacheClient.GetUserBeatSum(ctx, in.GetUid())
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUserBeatSum err : %s,uid: %d", err.Error(), in.GetUid())
            //return nil, err
        }
        aSum, err := s.cacheClient.GetSingleUserAwardSum(ctx, in.GetUid())
        if err != nil {
            log.ErrorWithCtx(ctx, "GetSingleUserAwardSum err : %s,uid: %d", err.Error(), in.GetUid())
            //return nil, err
        }
        info, err := s.aClient.GetUserByUid(ctx, in.GetUid())
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUserByUid err : %s,uid: %d", err.Error(), in.GetUid())
            //return nil, err
        }
        mechid, err := s.channelClient.GetUsersChannelId(ctx, in.Uid, in.Uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUsersChannelId err : %s,uid: %d", err.Error(), in.GetUid())
            //return nil, err
        }
        var uacount, nickname string
        if info != nil {
            uacount = info.Username
            nickname = info.Nickname
        }
        var roomid uint32
        if mechid != nil {
            roomid = mechid.ChannelId
        }
        out.Meinfo = &pb.RankListInfo{
            Suminfo: &pb.BeatSumInfo{
                BeatSum:  sum,
                AwardSum: aSum.AwardSum,
                Ranking:  0,
            },
            Userinfo: &pb.UserInfo{
                Uid:      in.GetUid(),
                Nickname: nickname,
                Account:  uacount,
                Roomid:   roomid,
            },
        }

        ts := time.Now().Unix()
        if ts > (s.rankListCache.ts + RankListCacheTime) {
            /*num, err := s.cacheClient.GetTotalBeatRecord()
              if err != nil {
              	return out, nil
              }
              if int64(count-in.PageNum) >= num {
              	return out, nil
              }*/
            beatSum, err := s.cacheClient.GetUserBeatSumSortedList(ctx, RankListCntMax)
            if err != nil {
                log.ErrorWithCtx(ctx, "GetUserBeatSumSortedList err : %s", err.Error())
                return nil, err
            }
            lenv := len(beatSum)
            if lenv == 0 {
                return out, nil
            }
            uids := make([]uint32, 0, lenv)
            for _, u := range beatSum {
                uids = append(uids, u.Uid)
            }

            awardSum, err := s.cacheClient.GetUserAwardSum(ctx, uids)
            if err != nil {
                log.ErrorWithCtx(ctx, "GetUserAwardSum err : %s", err.Error())
                return nil, err
            } else {
                log.DebugWithCtx(ctx, "GetUserAwardSum success ")
            }
            userInfo, err := s.aClient.GetUsersMap(ctx, uids)
            if err != nil {
                log.ErrorWithCtx(ctx, "GetUsersMap err: %s", err.Error())
                return nil, err
            }
            if len(userInfo) == 0 {
                log.ErrorWithCtx(ctx, "len(userInfo) == 0 ")
                return nil, nil
            }
            chids, _, err := s.channelClient.GetUsersChannelOnline(ctx, in.Uid, uids, false)
            if err != nil {
                log.ErrorWithCtx(ctx, "GetUsersChannelOnline err : %s", err.Error())
                return nil, err
            }

            list := make(RankListInfos, 0, RankListCntMax)
            for k, i := range beatSum {
                var nickname, username string
                var chid uint32
                if userItem, ok := userInfo[i.Uid]; ok {
                    if userItem != nil {
                        nickname = userItem.Nickname
                        username = userItem.Username
                    } else {
                        log.ErrorWithCtx(ctx, "userInfo nil, uid: %d", i.Uid)
                    }
                } else {
                    log.ErrorWithCtx(ctx, "nil userInfo[i.Uid] uid: %d", i.Uid)
                }
                if chidinfo, ok := chids[i.Uid]; ok {
                    if chidinfo != nil {
                        chid = chidinfo.ChannelId
                    }
                }
                list = append(list, &pb.RankListInfo{
                    Suminfo: &pb.BeatSumInfo{
                        BeatSum:  i.BeatSum,
                        AwardSum: awardSum[k].AwardSum,
                        Ranking:  i.Ranking,
                    },
                    Userinfo: &pb.UserInfo{
                        Uid:      i.Uid,
                        Nickname: nickname,
                        Account:  username,
                        Roomid:   chid,
                    },
                })
            }

            //listSize := len(list)
            //if listSize < RankListCntMax {
            //	for i := 0; i < RankListCntMax-listSize; i++ {
            //		list = append(list, &pb.RankListInfo{
            //			Suminfo:  &pb.BeatSumInfo{},
            //			Userinfo: &pb.UserInfo{},
            //		})
            //	}
            //}

            s.rankListCache.cache = list
            s.rankListCache.totalRecordNum = int64(len(list))
            s.rankListCache.ts = ts
        }

        //var ranking int
        cachelen := len(s.rankListCache.cache)
        /*	if sum != 0 {
        		ranking = sort.Search(cachelen, func(i int) bool {

        			return s.rankListCache.cache[i].Suminfo.BeatSum <= sum
        		})
        	} else {
        		ranking = -1
        	}*/
        /*新查个人排名*/
        listmap := make(map[uint32]uint32, 0)
        for _, v := range s.rankListCache.cache {
            listmap[v.Userinfo.Uid] = v.Suminfo.Ranking
        }
        if r, ok := listmap[in.GetUid()]; ok {
            out.Meinfo.Suminfo.Ranking = r
        }
        //out.Meinfo.Suminfo.Ranking = uint32(ranking + 1)
        //out.Meinfo = &pb.RankListInfo{
        //	Suminfo: &pb.BeatSumInfo{
        //		BeatSum:  sum,
        //		AwardSum: aSum.AwardSum,
        //		Ranking:  uint32(ranking + 1),
        //	},
        //	Userinfo: &pb.UserInfo{
        //		Uid:      in.Uid,
        //		Nickname: nickname,
        //		Account:  uacount,
        //		Roomid:   roomid,
        //	},
        //}
        //var max int
        max := count
        if int(count) > cachelen {
            max = uint32(cachelen)
        }

        //if len(s.rankListCache.cache) >= int(max) {
        out.Info = s.rankListCache.cache[count-in.PageNum : max]
        //}
        out.TotalNum = uint64(s.rankListCache.totalRecordNum)
    }
    log.InfoWithCtx(ctx, "GetMoleBeatRankList in:%+v,out:%+v", in, out)
    return out, nil
}

func (s *MoleBeatServer) GetReportRankings(ctx context.Context, _ *pb.ReportRankingsReq) (out *pb.ReportRankingsResp, err error) {
    out = &pb.ReportRankingsResp{}
    ctx, cancel := context.WithTimeout(ctx, time.Second*10)
    defer cancel()
    out.Infos = make([]*pb.ReportRankingInfo, 0, 50)
    ts := time.Now().Unix()
    /*活动开始之前不返回*/
    if ts < s.sc.GetMoleConfig().GameFirstStartTs {
        return out, nil
    }
    if ts > s.reportRankingCache.ts+RankListCacheTime {
        infos, err := s.statClient.LoadLimitLotteryHistory(ctx, 50)
        if err != nil {
            log.ErrorWithCtx(ctx, "LoadLimitLotteryHistory err : %s", err.Error())
            return out, err
        }

        // add fake info if < 50
        var fakeTs uint32
        if len(infos) == 0 {
            fakeTs = 0
        } else {
            fakeTs = infos[len(infos)-1].Ts
        }

        fakeCnt := 50 - len(infos)
        uidmap := make(map[uint32]int)
        for i := 0; i < fakeCnt; i++ {
            fakeUid := robotUid.GetRandomUid()
            for {
                if _, ok := uidmap[fakeUid]; ok {
                    fakeUid = robotUid.GetRandomUid()
                } else {
                    uidmap[fakeUid] = i
                    break
                }
            }

            fakeAward := s.getRandomFakeAward()
            infos = append(infos, &cache.UserLotteryResultInfo{
                Item: fakeAward,
                Uid:  fakeUid,
                Ts:   fakeTs,
            })

        }

        uids := make([]uint32, 0, 50)
        for _, v := range infos {
            uids = append(uids, v.Uid)
            out.Infos = append(out.Infos, &pb.ReportRankingInfo{
                Uid:      v.Uid,
                Nickname: "",
                Price:    v.Item.TBeansPrice,
                GiftName: v.Item.Name,
                GiftNum:  0,
                Ts:       v.Ts,
            })
        }

        users, err := s.aClient.GetUsersMap(ctx, uids)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUsersMap err: %s", err.Error())
            return out, err
        }

        for k, v := range out.Infos {
            var nickname, useraccount string
            if users[v.Uid] != nil {
                nickname = users[v.Uid].Nickname
                useraccount = users[v.Uid].Username
            } else {
                log.ErrorWithCtx(ctx, "nil out.Infos uid:%d", v.Uid)
            }
            out.Infos[k].Nickname = nickname
            out.Infos[k].Account = useraccount
        }

        s.reportRankingCache.cache = out.Infos
        s.reportRankingCache.ts = ts

    } else {
        out.Infos = make([]*pb.ReportRankingInfo, 0, 50)
        out.Infos = s.reportRankingCache.cache
    }

    return out, nil
}

/*var FakeAwardResultItem = []*cache.LotteryResultItem{&cache.LotteryResultItem{
	TBeansPrice: 500000,
	Name:        "幸运女神*1"}, &cache.LotteryResultItem{
	TBeansPrice: 131400,
	Name:        "刚好遇见你*1"}, &cache.LotteryResultItem{
	TBeansPrice: 52000,
	Name:        "小美好*1"}, &cache.LotteryResultItem{
	TBeansPrice: 800000,
	Name:        "祥云坐骑1天"}, &cache.LotteryResultItem{
	TBeansPrice: 2400000,
	Name:        "祥云坐骑3天"}, &cache.LotteryResultItem{
	TBeansPrice: 800000,
	Name:        "瑞气坐骑1天"}, &cache.LotteryResultItem{
	TBeansPrice: 2400000,
	Name:        "瑞气坐骑3天"}}

func getRandomFakeAward() *cache.LotteryResultItem {
	rd := rand.New(rand.NewSource(time.Now().UnixNano()))
	idx := rd.Intn(len(FakeAwardResultItem))
	return FakeAwardResultItem[idx]
}*/

/*new*/
func (s *MoleBeatServer) getRandomFakeAward() conf.LotteryResultItem {
    /* #nosec */
    rd := rand.New(rand.NewSource(time.Now().UnixNano()))
    idx := rd.Intn(len(s.sc.GetMoleConfig().LotteryResultItem))
    return s.sc.GetMoleConfig().LotteryResultItem[idx]
}

func (s *MoleBeatServer) ShutDown() {
    s.mqHandles.SetDead()
    s.kfkProduce.Close()
    s.kfkSub.Close()
    s.timer.Close()
}
