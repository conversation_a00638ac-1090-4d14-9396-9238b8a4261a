package manager

import (
	"context"
	"reflect"
	"sort"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/revenuenameplate"
	"golang.52tt.com/services/revenue-nameplate/internal/cache"
	"golang.52tt.com/services/revenue-nameplate/internal/common"
)

// GetUserNameplateInfo 获取用户铭牌配置
// 客户端接口
func (m *Manager) GetUserNameplateInfo(ctx context.Context, in *pb.GetUserNameplateInfoReq) (out *pb.GetUserNameplateInfoResp, err error) {
	out = &pb.GetUserNameplateInfoResp{}
	log.DebugWithCtx(ctx, "GetUserNameplateInfo in:[%+v]", in)
	result := make([]*pb.NameplateDetailInfo, 0)
	// 获取参数
	uid := in.GetUid()
	scene := in.GetScenes()

	// 查询用户配置
	config, err := m.cache.GetNameplateUserConfig(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserNameplateInfo GetNameplateUserConfig failed, in:[%+v], err:[%+v]", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "从缓存获取用户铭牌配置失败，请检查服务状态！")
	}
	// 判断获取到的信息是否为空，为空直接返回
	if config == nil {
		out.Nameplates = result
		return out, nil
	}

	// 配置更新标志
	needUpdate := false

	// 获取铭牌配置
	// 收集铭牌ID
	now := time.Now()
	nameplateIdList := make([]uint32, 0)
	userFinishTimeMap := make(map[uint32]int64)
	for _, u := range config.Wear {
		userConfig := u
		// 判断是否过期，过期跳过
		if userConfig.FinishTime < now.Unix() {
			needUpdate = true
			continue
		}
		id := userConfig.NameplateId
		nameplateIdList = append(nameplateIdList, id)
		userFinishTimeMap[id] = userConfig.FinishTime
	}

	if needUpdate { // 异步刷新用户缓存
		log.DebugWithCtx(ctx, "GetUserNameplateInfo UpdateUserConfigCacheInfo start, uid:[%+v]", uid)
		newCtx := context.Background()
		go func() {
			_ = m.UpdateUserConfigCacheInfo(newCtx, uid, now)
		}()
	}

	// 判断获取到的id是否为空，如果为空则跳过
	if len(nameplateIdList) == 0 {
		out.Nameplates = result
		return out, nil
	}
	// 获取配置
	nameplateConfigs, err := m.cache.BatchGetNameplateConfigs(ctx, nameplateIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserNameplateInfo BatchGetNameplateConfigs failed, nameplateIdList:[%v], err:[%+v]", nameplateIdList, err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "从缓存获取铭牌配置失败，请检查服务状态！")
	}
	// 判断可用的配置并封装返回
	for _, nameplateConfig := range nameplateConfigs {
		if nameplateConfig == nil {
			continue
		}
		// 如果是跟随场景，判断是否需要在跟随显示
		if scene == pb.ScenesType_SCENES_ROOM_FOLLOW_TYPE && nameplateConfig.IsFollowNeed == common.NOT_NEED_SHOW_IN_FOLLOW {
			continue
		}
		// 组装数据
		result = append(result, &pb.NameplateDetailInfo{
			Id:         nameplateConfig.Id,
			Name:       nameplateConfig.Name,
			BaseUrl:    nameplateConfig.BaseUrl,
			DynamicUrl: nameplateConfig.DynamicUrl,
			Type:       pb.NameplateType(nameplateConfig.UrlStatus),
			StartTime:  uint32(now.Unix()),
			FinishTime: uint32(userFinishTimeMap[nameplateConfig.Id]),
			IsUse:      true,
		})
	}
	out.Nameplates = result
	log.DebugWithCtx(ctx, "GetUserNameplateInfo succeed, in:[%+v], out:[%+v]", in, out)
	return out, nil
}

// BatchGetUserNameplates 批量获取用户铭牌配置
// 客户端接口
func (m *Manager) BatchGetUserNameplates(ctx context.Context, in *pb.BatchGetUserNameplatesReq) (out *pb.BatchGetUserNameplatesResp, err error) {
	out = &pb.BatchGetUserNameplatesResp{}
	log.DebugWithCtx(ctx, "BatchGetUserNameplates in:[%+v]", in)
	// 获取参数
	scenes := in.GetScenes()
	uidList := in.GetUidList()
	result := make([]*pb.UserNameplate, 0)
	now := time.Now()

	// 判断获取的uid列表是否为空
	if len(uidList) == 0 {
		out.UserNameplates = result
		return out, nil
	}

	// 查询所有用户的配置信息
	configMap, err := m.cache.BatchGetNameplateUserConfig(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserNameplates BatchGetNameplateUserConfig failed, in:[%+v], err:[%+v]", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "从缓存获取用户铭牌配置失败，请检查服务状态！")
	}
	if configMap == nil {
		out.UserNameplates = result
		return out, nil
	}

	nameplateIds := make([]uint32, 0)
	userConfigs := make([]*cache.UserCacheConfig, 0)
	for _, uid := range uidList {
		config := configMap[uid]
		// 判断获取到的信息是否为空，为空直接跳过
		if config == nil {
			continue
		}
		userConfigs = append(userConfigs, config)
		// 获取铭牌配置
		needUpdate := false // 配置更新标志
		for _, u := range config.Wear {
			userConfig := u
			// 判断是否过期，过期跳过
			if userConfig.FinishTime < now.Unix() {
				needUpdate = true
				continue
			}
			id := userConfig.NameplateId
			nameplateIds = append(nameplateIds, id)
		}

		// 如果需要更新用户配置异步更新
		if needUpdate { // 异步刷新
			newCtx := context.Background()
			go func() {
				_ = m.UpdateUserConfigCacheInfo(newCtx, uid, now)
			}()
		}
	}

	if len(nameplateIds) == 0 {
		return out, nil
	}

	// 批量获取铭牌配置
	nameplateConfigs, err := m.cache.BatchGetNameplateConfigs(ctx, nameplateIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserNameplates BatchGetNameplateConfigs failed, nameplateIdList:[%v], err:[%+v]", nameplateIds, err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "从缓存获取铭牌配置失败，请检查服务状态！")
	}
	// 将配置转换为map
	nameplateConfMap := make(map[uint32]*cache.NameplateCacheConfig)
	for _, config := range nameplateConfigs {
		if config == nil || config.Id == 0 {
			continue
		}
		nameplateConfMap[config.Id] = config
	}

	out.UserNameplates = m.genUserNameplatesResult(userConfigs, nameplateConfMap, now, scenes)
	log.DebugWithCtx(ctx, "BatchGetUserNameplates succeed, in:[%+v], out:[%+v]", in, out)
	return out, nil
}

// genUserNameplatesResult 生成用户铭牌信息返回值
func (m *Manager) genUserNameplatesResult(userConfigs []*cache.UserCacheConfig, nameplateConfMap map[uint32]*cache.NameplateCacheConfig,
	now time.Time, scenes pb.ScenesType) []*pb.UserNameplate {
	result := make([]*pb.UserNameplate, 0)

	// 组装返回信息
	for _, userConf := range userConfigs {
		userNameplates := make([]*pb.NameplateDetailInfo, 0)
		for _, nameplate := range userConf.Wear {
			nameplateConfig := nameplateConfMap[nameplate.NameplateId]
			if nameplateConfig == nil {
				continue
			}
			if scenes == pb.ScenesType_SCENES_ROOM_FOLLOW_TYPE && nameplateConfig.IsFollowNeed == common.NOT_NEED_SHOW_IN_FOLLOW {
				continue
			}
			// 判断是否过期，过期跳过
			if nameplate.FinishTime < now.Unix() {
				continue
			}
			userNameplates = append(userNameplates, &pb.NameplateDetailInfo{
				Id:         nameplate.NameplateId,
				Name:       nameplateConfig.Name,
				BaseUrl:    nameplateConfig.BaseUrl,
				DynamicUrl: nameplateConfig.DynamicUrl,
				Type:       pb.NameplateType(nameplateConfig.UrlStatus),
				StartTime:  uint32(now.Unix()),
				FinishTime: uint32(nameplate.FinishTime),
				IsUse:      true,
			})
		}
		userResult := &pb.UserNameplate{
			Uid:        userConf.Uid,
			Nameplates: userNameplates,
		}
		if len(userResult.Nameplates) != 0 {
			result = append(result, userResult)
		}
	}
	return result
}

// SetUserNameplateInfo 设置用户铭牌配置
// 客户端接口
func (m *Manager) SetUserNameplateInfo(ctx context.Context, in *pb.SetUserNameplateInfoReq) (out *pb.SetUserNameplateInfoResp, err error) {
	out = &pb.SetUserNameplateInfoResp{}
	log.DebugWithCtx(ctx, "SetUserNameplateInfo in:[%+v]", in)

	// 获取参数
	uid := in.GetUid()
	idList := in.GetIdList()
	now := time.Now()

	// 获取用户现在的佩戴信息
	dbIdList, err := m.store.GetUserNameplateConfig(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserNameplateInfo GetUserNameplateConfig failed, uid:[%+v], err:[%+v]", uid, err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "设置用户铭牌配置查询数据库失败，请检查服务状态！")
	}
	if reflect.DeepEqual(dbIdList, idList) { // 如果当前配置和设置配置相同则直接返回
		return out, nil
	}

	// 根据配置文件限制用户可佩戴
	if len(idList) > m.dyConf.CanWearNameplateNum() {
		log.ErrorWithCtx(ctx, "SetUserNameplateInfo err too many nameplate uer set, len(idList):[%+v], CanWearNameplateNum:[%+v]",
			len(idList), m.dyConf.CanWearNameplateNum())
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "期望佩戴的铭牌数量超可佩戴范围，参数错误！")
	}

	// 更新数据库配置
	err = m.store.SetUserNameplateConfig(ctx, uid, idList, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserNameplateInfo SetUserNameplateConfig failed, uid:[%v], idList:[%+v], err:[%+v]", uid, idList, err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "设置用户铭牌配置更新数据库失败，请检查服务状态！")
	}

	// 更新用户铭牌缓存配置
	_ = m.UpdateUserConfigCacheInfo(ctx, uid, now)
	log.DebugWithCtx(ctx, "SetUserNameplateInfo succeed, in:[%+v], out:[%+v]", in, out)
	return out, nil
}

// GetUserAllNameplateList 获取用户所有可配置铭牌
// 客户端接口
func (m *Manager) GetUserAllNameplateList(ctx context.Context, in *pb.GetUserAllNameplateListReq) (out *pb.GetUserAllNameplateListResp, err error) {
	out = &pb.GetUserAllNameplateListResp{}
	uid := in.GetUid()
	now := time.Now()
	log.DebugWithCtx(ctx, "GetUserAllNameplateList, uid:[%+v]", uid)

	// 从数据库获取用户所有铭牌信息
	userNameplateRewards, err := m.store.GetUserNameplateAssignRewards(ctx, uid, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserAllNameplateList GetUserNameplateAssignRewards failed, uid:[%v], err:[%+v]", uid, err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "从数据库获取用户所有可配置铭牌失败，请检查服务状态！")
	}
	if len(userNameplateRewards) == 0 {
		return out, nil
	}

	// 从数据库获取用户所佩戴的铭牌id
	userWearNameplateIds, err := m.store.GetUserNameplateConfig(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserAllNameplateList GetUserNameplateConfig failed, uid:[%v], err:[%+v]", uid, err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "从数据库获取用户所佩戴的铭牌失败，请检查服务状态！")
	}
	// 收集用户佩戴信息
	userWearNameplateIdMap := make(map[uint32]bool)
	for _, userWearNameplateId := range userWearNameplateIds {
		userWearNameplateIdMap[userWearNameplateId] = true
	}

	// 收集所有铭牌id
	idList := make([]uint32, 0)
	userWearMap := make(map[uint32]int64)
	for _, userNameplateReward := range userNameplateRewards {
		// 排除过期的配置
		if userNameplateReward.FinishTime < now.Unix() {
			continue
		}
		idList = append(idList, userNameplateReward.NameplateId)
		userWearMap[userNameplateReward.NameplateId] = userNameplateReward.FinishTime
	}

	// 查询对应铭牌id的铭牌配置
	nameplateConfigs, err := m.store.BatchGetNameplateConfigInfosByIds(ctx, idList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserAllNameplateList BatchGetNameplateConfigInfosByIds failed, idList:[%v], err:[%+v]", idList, err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "从数据库获取用户的铭牌的配置文件失败，请检查服务状态！")
	}

	// 组装返回结果
	result := make([]*pb.NameplateDetailInfo, 0)
	for _, nameplateConfig := range nameplateConfigs {
		// 跳过无效配置
		if userWearMap[nameplateConfig.Id] <= 0 {
			continue
		}
		result = append(result, &pb.NameplateDetailInfo{
			Id:         nameplateConfig.Id,
			Name:       nameplateConfig.Name,
			BaseUrl:    nameplateConfig.BaseUrl,
			DynamicUrl: nameplateConfig.DynamicUrl,
			Type:       pb.NameplateType(nameplateConfig.UrlStatus),
			StartTime:  uint32(now.Unix()),
			FinishTime: uint32(userWearMap[nameplateConfig.Id]),
			IsUse:      userWearNameplateIdMap[nameplateConfig.Id],
		})
	}
	// 按铭牌ID降序排列
	sort.Slice(result, func(i, j int) bool {
		return result[i].Id > result[j].Id
	})
	out.Nameplates = result
	log.DebugWithCtx(ctx, "GetUserAllNameplateList succeed, uid:[%+v], out:[%+v]", uid, out)
	return out, nil
}

// GetNamePlateFromCache 获取指定铭牌配置
// 客户端接口
func (m *Manager) GetNamePlateFromCache(ctx context.Context, in *pb.GetNamePlateReq) (*pb.GetNamePlateResp, error) {
	out := &pb.GetNamePlateResp{}
	log.DebugWithCtx(ctx, "GetNamePlate in:[%+v]", in)

	// 批量获取铭牌配置
	configs, err := m.cache.BatchGetNameplateConfigs(ctx, in.GetIdList())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserNameplates BatchGetNameplateConfigs failed, nameplateIdList:[%v], err:[%+v]", in.GetIdList(), err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "从缓存获取铭牌配置失败，请检查服务状态！")
	}

	out.Nameplates = m.genNameplateRespInfoFromCache(configs)
	log.DebugWithCtx(ctx, "GetNamePlate succeed, in:[%+v], out:[%+v]", in, out)
	return out, nil
}

// genNameplateRespInfoFromCache 组装铭牌配置返回结构
func (m *Manager) genNameplateRespInfoFromCache(configs []*cache.NameplateCacheConfig) []*pb.RevenueNameplateInfo {
	result := make([]*pb.RevenueNameplateInfo, 0)
	for _, config := range configs {
		isFollowNeed := false
		if config.IsFollowNeed == 1 {
			isFollowNeed = true
		}
		result = append(result, &pb.RevenueNameplateInfo{
			Id:           config.Id,
			Name:         config.Name,
			BaseUrl:      config.BaseUrl,
			DynamicUrl:   config.DynamicUrl,
			Type:         pb.NameplateType(config.UrlStatus),
			IsFollowNeed: isFollowNeed,
		})
	}
	return result
}
