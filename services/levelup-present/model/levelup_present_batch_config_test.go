package model

import (
    "context"
    "database/sql"
    sqlmock "github.com/zhashkevych/go-sqlxmock"
    LevelupPresent "golang.52tt.com/protocol/services/levelup-present"
    "reflect"
    "testing"
	"time"
)

func TestStore_DeleteLevelupBatch(t *testing.T) {
    mock, db := getMockDb()

    ctx := context.Background()
    itemId := uint32(1)
    batchCount := uint32(1)

    mock.ExpectExec("delete from levelup_present_batch_config where item_id=? and batch_count=?").
        WithArgs(itemId, batchCount).WillReturnResult(sqlmock.NewResult(0, 1))

	type fields struct {
		db *sql.DB
	}
	type args struct {
		ctx        context.Context
		itemId     uint32
		batchCount uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
        {
            name:    "TestStore_DeleteLevelupBatch",
            fields:  fields{db: db},
            args:    args{ctx: ctx, itemId: itemId, batchCount: batchCount},
            wantErr: false,
        },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.DeleteLevelupBatch(tt.args.ctx, tt.args.itemId, tt.args.batchCount); (err != nil) != tt.wantErr {
				t.Errorf("DeleteLevelupBatch() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_DeleteLevelupPresentBatch(t *testing.T) {
    mock, db := getMockDb()

    ctx := context.Background()
    itemId := uint32(1)

    mock.ExpectExec("delete from levelup_present_batch_config where item_id=?").
        WithArgs(itemId).WillReturnResult(sqlmock.NewResult(0, 1))

	type fields struct {
		db *sql.DB
	}
	type args struct {
		ctx    context.Context
		itemId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
        {
            name:    "TestStore_DeleteLevelupPresentBatch",
            fields:  fields{db: db},
            args:    args{ctx: ctx, itemId: itemId},
            wantErr: false,
        },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.DeleteLevelupPresentBatch(tt.args.ctx, tt.args.itemId); (err != nil) != tt.wantErr {
				t.Errorf("DeleteLevelupPresentBatch() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_GetAllLevelupPresentBatch(t *testing.T) {
	mock, db := getMockDb()

	ctx := context.Background()

	itemId := uint32(1)
	batchCount := uint32(1)
	effectUrl := "a"
	effectMd5 := "b"
	effectDesc := "c"
	createTime := time.Now().Unix()
	updateTime := time.Now().Unix()

	sqlRows := sqlmock.NewRows([]string{"item_id", "batch_count", "effect_url", "effect_md5", "effect_desc", "create_time",
		"update_time"}).AddRow(itemId, batchCount, effectUrl, effectMd5, effectDesc,
		createTime, updateTime)
	mock.ExpectQuery("select item_id, batch_count, effect_url, effect_md5, effect_desc, create_time, update_time " +
		"from levelup_present_batch_config where batch_count>0").
		WithArgs().WillReturnRows(sqlRows)

	type fields struct {
		db *sql.DB
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *[]LevelupPresent.LevelupPresentBatchData
		wantErr bool
	}{
		// Add test cases.
		{
			name:   "TestStore_GetAllLevelupPresentBatch",
			fields: fields{db: db},
			args:   args{ctx: ctx},
			want: &[]LevelupPresent.LevelupPresentBatchData{{
				ItemId:     itemId,
				BatchCount: batchCount,
				EffectUrl:  effectUrl,
				EffectMd5:  effectMd5,
				EffectDesc: effectDesc,
				CreateTime: uint32(createTime),
				UpdateTime: uint32(updateTime),
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			got, err := s.GetAllLevelupPresentBatch(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllLevelupPresentBatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllLevelupPresentBatch() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_InsertLevelupBatch(t *testing.T) {
	mock, db := getMockDb()

	ctx := context.Background()
	itemId := uint32(1)
	batchCount := uint32(1)
	effectUrl := "a"
	effectMd5 := "b"
	effectDesc := "c"
	createTime := time.Now().Unix()

	mock.ExpectExec("insert into levelup_present_batch_config(item_id, batch_count, effect_url, effect_md5, "+
		"effect_desc, create_time) values(?, ?, ?, ?, ?, ?)").
		WithArgs(itemId, batchCount, effectUrl, effectMd5, effectDesc, createTime).WillReturnResult(sqlmock.NewResult(0, 1))

	type fields struct {
		db *sql.DB
	}
	type args struct {
		ctx  context.Context
		data *LevelupPresent.LevelupPresentBatchData
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name:   "TestStore_InsertLevelupBatch",
			fields: fields{db: db},
			args: args{ctx: ctx, data: &LevelupPresent.LevelupPresentBatchData{
				ItemId:     itemId,
				BatchCount: batchCount,
				EffectUrl:  effectUrl,
				EffectMd5:  effectMd5,
				EffectDesc: effectDesc,
				CreateTime: uint32(createTime),
				UpdateTime: 0,
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.InsertLevelupBatch(tt.args.ctx, tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("InsertLevelupBatch() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_UpdateLevelupBatch(t *testing.T) {
	mock, db := getMockDb()

	ctx := context.Background()
	itemId := uint32(1)
	batchCount := uint32(1)
	effectUrl := "a"
	effectMd5 := "b"
	effectDesc := "c"
	updateTime := time.Now().Unix()

	mock.ExpectExec("update levelup_present_batch_config set batch_count=?, effect_url=?, effect_md5=?, effect_desc=?, update_time=? where item_id=?").
		WithArgs(batchCount, effectUrl, effectMd5, effectDesc, updateTime, itemId).WillReturnResult(sqlmock.NewResult(0, 1))

	type fields struct {
		db *sql.DB
	}
	type args struct {
		ctx  context.Context
		data *LevelupPresent.LevelupPresentBatchData
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name:   "TestStore_UpdateLevelupBatch",
			fields: fields{db: db},
			args: args{ctx: ctx, data: &LevelupPresent.LevelupPresentBatchData{
				ItemId:     itemId,
				BatchCount: batchCount,
				EffectUrl:  effectUrl,
				EffectMd5:  effectMd5,
				EffectDesc: effectDesc,
				CreateTime: 0,
				UpdateTime: uint32(updateTime),
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.UpdateLevelupBatch(tt.args.ctx, tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("UpdateLevelupBatch() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_createLevelupPresentBatchConfig(t *testing.T) {
	mock, db := getMockDb()

	sqlstr := "CREATE TABLE IF NOT EXISTS `levelup_present_batch_config` (" +
		"`item_id` int unsigned NOT NULL," +
		"`batch_count` int unsigned NOT NULL," +
		"`effect_url` varchar(256) NOT NULL DEFAULT '' COMMENT '特效url'," +
		"`effect_md5` varchar(256) NOT NULL DEFAULT '' COMMENT '特效url md5'," +
		"`effect_desc` varchar(1024) NOT NULL DEFAULT '' COMMENT '批量送出文案'," +
		"`create_time` int unsigned NOT NULL DEFAULT 0," +
		"`update_time` int unsigned NOT NULL DEFAULT 0," +
		"PRIMARY KEY (`item_id`, `batch_count`)" +
		") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;"

	mock.ExpectExec(sqlstr).
		WithArgs().WillReturnResult(sqlmock.NewResult(0, 1))

	type fields struct {
		db *sql.DB
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// Add test cases.
		{
			name:    "TestStore_createLevelupPresentBatchConfig",
			fields:  fields{db: db},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.createLevelupPresentBatchConfig(); (err != nil) != tt.wantErr {
				t.Errorf("createLevelupPresentBatchConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
