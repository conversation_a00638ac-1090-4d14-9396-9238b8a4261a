package event

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	userpresentPB "golang.52tt.com/protocol/services/userpresent"
	"sync"
	"time"
)

type mergedSceneEventInfo struct {
	ChannelId uint32
	GuildId   uint32
	OrderId   string
	ItemId    uint32
	ItemCount uint32
	Price     uint32
	PriceType uint32
}

//
var gMergedEvMapIdxLock sync.Mutex
var gMergedEvMapIdx = 0
var gMergedEvMap_0 map[uint32][]*mergedSceneEventInfo
var gMergedEvMap_1 map[uint32][]*mergedSceneEventInfo

func (sub *KafkaPresentSubscriber) startMergeSceneEventTimer() error {

	gMergedEvMapIdx = 0
	gMergedEvMap_0 = make(map[uint32][]*mergedSceneEventInfo)
	gMergedEvMap_1 = make(map[uint32][]*mergedSceneEventInfo)

	ticker := time.NewTicker(time.Second * 5)
	//defer ticker.Stop()
	go func() {
		for range ticker.C {
			sub.procMergedScenePresentEvent()
		}
	}()
	return nil
}

// 将红钻送礼的场景请求 进行叠加合并处理
func (sub *KafkaPresentSubscriber) mergeSceneRedDiamondPresentEvent(ev *kafkapresent.PresentEvent) error {

	if ev.GetPriceType() != uint32(userpresentPB.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {

		return fmt.Errorf("price type not red diamond")
	}
	// 加锁 插入当前的map队列中
	var mapPoint *map[uint32][]*mergedSceneEventInfo
	gMergedEvMapIdxLock.Lock()

	if 0 == gMergedEvMapIdx {
		mapPoint = &gMergedEvMap_0
	} else {
		mapPoint = &gMergedEvMap_1
	}

	// 累加 ItemCount 和 Price
	var bIsFindItem = false
	list, ok := (*mapPoint)[ev.ChannelId]
	if ok {

		for _, mergedInfo := range list {
			if mergedInfo.ItemId == ev.ItemId {
				mergedInfo.ItemCount += ev.ItemCount
				mergedInfo.Price += ev.Price
				bIsFindItem = true
				break
			}
		}

	}

	if !bIsFindItem {
		var tmpSceneEventInfo = &mergedSceneEventInfo{
			ChannelId: ev.ChannelId,
			GuildId:   ev.GuildId,
			ItemId:    ev.ItemId,
			ItemCount: ev.ItemCount,
			Price:     ev.Price,
			PriceType: ev.PriceType,
			OrderId:   ev.OrderId,
		}

		(*mapPoint)[ev.ChannelId] = append((*mapPoint)[ev.ChannelId], tmpSceneEventInfo)
	}

	gMergedEvMapIdxLock.Unlock()
	return nil
}

func (sub *KafkaPresentSubscriber) procMergedScenePresentEvent() {

	var mapPoint *map[uint32][]*mergedSceneEventInfo

	// 加锁切换map
	gMergedEvMapIdxLock.Lock()
	if 0 == gMergedEvMapIdx {
		mapPoint = &gMergedEvMap_0
	} else {
		mapPoint = &gMergedEvMap_1
	}
	gMergedEvMapIdx = (gMergedEvMapIdx + 1) % 2
	gMergedEvMapIdxLock.Unlock()

	// 循环发请求
	var cnt uint32 = 0
	for _, list := range *mapPoint {

		for _, info := range list {

			ctx, cancel := context.WithTimeout(context.TODO(), 200*time.Millisecond)

			var spReq userpresentPB.RecordSceneSendPresentReq
			spReq.Uid = 0
			if info.ChannelId > 0 {
				scene := &userpresentPB.StSceneInfo{
					SceneId:   info.ChannelId,
					SceneType: uint32(userpresentPB.PresentSceneType_CHANNEL_PRESENT),
				}
				spReq.SceneList = append(spReq.SceneList, scene)
			}

			if info.GuildId > 0 {

				guildScene := &userpresentPB.StSceneInfo{
					SceneId:    info.GuildId,
					SceneType:  uint32(userpresentPB.PresentSceneType_GUILD_PRESENT),
					SubSceneId: 0,
				}
				spReq.SceneList = append(spReq.SceneList, guildScene)
			}

			if len(spReq.SceneList) > 0 {
				itemCfg := &userpresentPB.StPresentItemConfig{
					ItemId:    info.ItemId,
					Price:     info.Price,
					PriceType: info.PriceType,
				}
				spReq.ItemConfig = itemCfg
				spReq.OrderId = info.OrderId
				spReq.ItemCount = info.ItemCount
				spReq.TargetUid = 0

				terr := sub.userPresentClient.RecordSceneSendPresent(ctx, &spReq)
				if terr != nil {
					log.Errorf("procMergedScene req %v RecordSceneSendPresent err:%v", spReq, terr)
					cancel()
					continue
				}
				cnt++
				log.Infof("procMergedScene send reddaimond Scene cid %d itemId %d itemCnt %d",
					info.ChannelId, info.ItemId, info.ItemCount)
			}
			cancel()
		}

	}
	log.Infof("procMergedScene mapLen %d cnt %d ", len(*mapPoint), cnt)

	// 清空Map
	*mapPoint = make(map[uint32][]*mergedSceneEventInfo)
}
