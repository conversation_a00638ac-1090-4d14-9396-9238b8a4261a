package handler

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/urfave/cli/v2"
	pbBackpack "golang.52tt.com/protocol/services/backpack-base"
	pbBackpackCard "golang.52tt.com/protocol/services/backpack-func-card"
	pbUserpresent "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/muti-cmd-tools/common"
)

func BackpackCommand() []*cli.Command {
	return []*cli.Command{
		{

			Name: "getBackpackInfo",
			Flags: []cli.Flag{
				&cli.Uint64Flag{
					Name:    "id",
					Aliases: []string{"i"},
					Usage:   "包裹ID",
				},
			},
			Usage:  "[礼物]根据包裹ID查询包裹内容",     //命令 介绍
			Action: getBackpackInfoByIdCmd, //命令触发方法
		},
		{

			Name: "getPresentInfo",
			Flags: []cli.Flag{
				&cli.Uint64Flag{
					Name:    "id",
					Aliases: []string{"i"},
					Usage:   "礼物ID",
				},
			},
			Usage:  "[礼物]根据礼物ID查询礼物内容",    //命令 介绍
			Action: getPresentInfoByIdCmd, //命令触发方法
		},
		{

			Name: "getFuncCardInfo",
			Flags: []cli.Flag{
				&cli.Uint64Flag{
					Name:    "id",
					Aliases: []string{"i"},
					Usage:   "功能卡片ID",
				},
			},
			Usage:  "[礼物]根据功能卡ID查询卡片信息",    //命令 介绍
			Action: getFuncCardInfoByIdCmd, //命令触发方法
		},
	}
}

func getFuncCardInfoByIdCmd(c *cli.Context) error {
	var err error
	cardIDParam := c.Uint64("id")

	ctx, _ := context.WithTimeout(context.TODO(), time.Second*3)
	var err2 error

	var tmpCardId []uint32
	tmpCardId = append(tmpCardId, uint32(cardIDParam))
	baseMap, err2 := getFunCardConfigByIDList(ctx, tmpCardId)
	if err2 != nil {
		err = fmt.Errorf("getFunCardConfigByIDList id %d failed=%v", cardIDParam, err2)
		return err
	}

	printCardInfoMsg(baseMap[uint32(cardIDParam)])

	return nil
}
func getPresentInfoByIdCmd(c *cli.Context) error {
	var err error
	presentIDParam := c.Uint64("id")

	ctx, _ := context.WithTimeout(context.TODO(), time.Second*3)
	var err2 error

	var tmpGiftId []uint32
	tmpGiftId = append(tmpGiftId, uint32(presentIDParam))
	baseMap, effMap, enterMap, err2 := getPresentConfigByIDList(ctx, tmpGiftId)
	if err2 != nil {
		err = fmt.Errorf("getPresentConfigByIDList id %d failed=%v", presentIDParam, err2)
		return err
	}

	printPresentConfMsg(baseMap[uint32(presentIDParam)], effMap[uint32(presentIDParam)], enterMap[uint32(presentIDParam)])

	return nil
}

func getBackpackInfoByIdCmd(c *cli.Context) error {
	var err error
	backpackIDParam := c.String("id")

	backpackCli, err1 := common.GetBackpackBaseCli()
	if err1 != nil {
		err = fmt.Errorf("GetBackpackBaseCli failed=%v", err1)
		return err
	}

	// 检测 sourceTTIDParam 是否纯数字
	if !common.IsNumeric(backpackIDParam) {
		err = fmt.Errorf("backpackIDParam %s is not numeric", backpackIDParam)
		return err
	}
	var cfgReq pbBackpack.GetPackageItemCfgReq
	tempBgId, _ := strconv.ParseUint(backpackIDParam, 10, 32)
	cfgReq.BgId = uint32(tempBgId)

	ctx, _ := context.WithTimeout(context.TODO(), time.Second*3)

	cfgResp, errBackpack := backpackCli.GetPackageItemCfgV2(ctx, &cfgReq)
	if errBackpack != nil {
		err = fmt.Errorf("GetPackageItemCfgV2 ID=%d failed=%v", tempBgId, errBackpack)
		return err
	}

	var presentIDList []uint32
	var funcCardIDList []uint32
	itemList := cfgResp.GetItemCfgList()
	// 先过滤出礼物类型的物品，去礼物服务查询
	for _, backpackItem := range itemList {
		if backpackItem.GetItemType() == uint32(pbBackpack.PackageItemType_BACKPACK_PRESENT) {
			// 礼物信息就要去查礼物服务，这里先记录，后面一起查
			presentIDList = append(presentIDList, backpackItem.GetSourceId())
			continue
		} else if backpackItem.GetItemType() == uint32(pbBackpack.PackageItemType_BACKPACK_CARD_RICH_ACCELERATOR) ||
			backpackItem.GetItemType() == uint32(pbBackpack.PackageItemType_BACKPACK_CARD_CHARM_ACCELERATOR) ||
			backpackItem.GetItemType() == uint32(pbBackpack.PackageItemType_BACKPACK_CARD_RICH_INCR) ||
			backpackItem.GetItemType() == uint32(pbBackpack.PackageItemType_BACKPACK_CARD_KNIGHT) {
			// 卡片就要去查卡片服务，这里先记录，后面一起查
			funcCardIDList = append(funcCardIDList, backpackItem.GetSourceId())
			continue
		}
	}
	// 查礼物
	var presentBaseInfoMap map[uint32]*pbUserpresent.PresentBaseConfig
	if len(presentIDList) > 0 {
		var err2 error
		presentBaseInfoMap, _, _, err2 = getPresentConfigByIDList(ctx, presentIDList)
		if err2 != nil {
			err = fmt.Errorf("getPresentBaseConfigByIDList failed=%v", err2)
			return err
		}
	}
	//查卡片
	var cardInfoMap map[uint32]*pbBackpackCard.FuncCardCfg
	if len(funcCardIDList) > 0 {
		var err2 error
		cardInfoMap, err2 = getFunCardConfigByIDList(ctx, funcCardIDList)
		if err2 != nil {
			err = fmt.Errorf("getFunCardConfigByIDList failed=%v", err2)
			return err
		}
	}

	// 重新组合输出
	fmt.Printf("包裹ID %d 包裹物品总数 %d \n", tempBgId, len(itemList))
	for idx, backpackItem := range itemList {
		fmt.Printf(" [%d] 包裹内物品Id %d 物品数量 %d DynamicFinTime %d FinTime %d ",
			idx, backpackItem.BgItemId, backpackItem.ItemCount, backpackItem.DynamicFinTime, backpackItem.FinTime)

		// 打印 礼物物品信息
		if backpackItem.GetItemType() == uint32(pbBackpack.PackageItemType_BACKPACK_PRESENT) {
			if presentBaseInfo, ok := presentBaseInfoMap[backpackItem.GetSourceId()]; ok {
				printPresentConfMsg(presentBaseInfo, nil, nil)
			} else {
				fmt.Printf(" 礼物ID %d 信息未查到 \n", backpackItem.GetSourceId())
			}
			continue
		}

		// 打印 卡片物品信息
		if backpackItem.GetItemType() == uint32(pbBackpack.PackageItemType_BACKPACK_CARD_RICH_ACCELERATOR) ||
			backpackItem.GetItemType() == uint32(pbBackpack.PackageItemType_BACKPACK_CARD_CHARM_ACCELERATOR) ||
			backpackItem.GetItemType() == uint32(pbBackpack.PackageItemType_BACKPACK_CARD_RICH_INCR) ||
			backpackItem.GetItemType() == uint32(pbBackpack.PackageItemType_BACKPACK_CARD_KNIGHT) {
			if cardItemInfo, ok := cardInfoMap[backpackItem.GetSourceId()]; ok {
				printCardInfoMsg(cardItemInfo)
			} else {
				fmt.Printf(" 卡片ID %d（类型%d） 信息未查到 \n",
					backpackItem.GetSourceId(), backpackItem.GetItemType())
			}
			continue
		}

		if backpackItem.GetItemType() == uint32(pbBackpack.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) {
			fmt.Printf(" 碎片ID %d \n", backpackItem.GetSourceId())
		} else {
			fmt.Printf(" 未知类型(%d) ID %d \n", backpackItem.GetItemType(), backpackItem.GetSourceId())
		}

	}

	return nil
}

func printPresentConfMsg(baseCnf *pbUserpresent.PresentBaseConfig,
	effectConf *pbUserpresent.PresentEffectConfig, enterConf *pbUserpresent.PresentEnterConfig) {

	if baseCnf == nil {
		return
	}

	// ShowEffect 映射表
	showEffectMap := map[uint32]string{
		0: "默认",
		1: "全屏",
		2: "流光1(礼物总值小于10W红钻/T豆",
		3: "流光2(礼物总值大于10W红钻/T豆)",
	}

	// Tag 映射表
	tagMap := map[uint32]string{
		0:  "普通",
		1:  "相亲",
		2:  "贵族",
		3:  "语音加团",
		4:  "涂鸦",
		5:  "挚友",
		6:  "升级",
		7:  "定制",
		8:  "弹幕专属",
		9:  "券",
		10: "权限",
		11: "粉丝",
		12: "套组",
	}

	getVal := func(val uint32, m map[uint32]string) string {
		if v, ok := m[val]; ok {
			return v
		}
		return fmt.Sprintf("未知(%d)", val)
	}

	var line1, line2, line3 string

	if baseCnf != nil {
		line1 = fmt.Sprintf("[基础] 礼物ID:%d 名称:%s 价值:%s 可获积分:%d [%s%s%s]",
			baseCnf.GetItemId(),
			baseCnf.GetName(),
			genPresentPriceMsg(baseCnf),
			baseCnf.GetScore(),
			map[bool]string{true: "测试/"}[baseCnf.GetIsTest()],
			map[bool]string{true: "删除/"}[baseCnf.GetIsDel()],
			map[bool]string{true: "强制送"}[baseCnf.GetForceSendable()])
	}

	if effectConf != nil {
		line2 = fmt.Sprintf("[特效] 特效类型:%s 流光ID %d [%s%s%s]",
			getVal(effectConf.GetShowEffect(), showEffectMap),
			effectConf.GetFlowId(),
			map[bool]string{true: "全服,"}[effectConf.GetNotifyAll()],
			map[bool]string{true: "开盒,"}[effectConf.GetIsBoxBreaking()],
			map[bool]string{true: "融合头像"}[effectConf.GetFusionPresent()])
	}

	if enterConf != nil {
		line3 = fmt.Sprintf("[入口] 标签:%s [%s%s%s%s]  排名:%.2f 贵族等级:%d 粉丝等级:%d",
			getVal(enterConf.GetTag(), tagMap),
			map[bool]string{true: "礼物架/"}[enterConf.GetUnshowPresentShelf()],
			map[bool]string{true: "批量送/"}[enterConf.GetUnshowBatchOption()],
			map[bool]string{true: "显示下架时间/"}[enterConf.GetShowEffectEnd()],
			map[bool]string{true: "支持延长上架时间"}[enterConf.GetEffectEndDelay()],
			enterConf.GetRank(),
			enterConf.GetNobilityLevel(),
			enterConf.GetFansLevel())
	}

	fmt.Println(line1)
	if effectConf != nil {
		fmt.Println(line2)
	}

	if enterConf != nil {
		fmt.Println(line3)
	}

	return
}

func printCardInfoMsg(cardInf *pbBackpackCard.FuncCardCfg) {
	if cardInf.GetCardType() == pbBackpack.PackageItemType_BACKPACK_CARD_RICH_ACCELERATOR {
		fmt.Printf(" 财富加成卡 ID %d %s 加成比例 %s 有效时长 %s \n",
			cardInf.GetCardId(), cardInf.GetCardName(),
			translateAccelerateCardTimes(cardInf.GetCardTimes()), translateAccelerateCardDuration(cardInf.GetValidTime()))
	}
	if cardInf.GetCardType() == pbBackpack.PackageItemType_BACKPACK_CARD_CHARM_ACCELERATOR {
		fmt.Printf(" 魅力加成卡 ID %d %s 加成比例 %s 有效时长 %s \n",
			cardInf.GetCardId(), cardInf.GetCardName(),
			translateAccelerateCardTimes(cardInf.GetCardTimes()), translateAccelerateCardDuration(cardInf.GetValidTime()))
	}
	if cardInf.GetCardType() == pbBackpack.PackageItemType_BACKPACK_CARD_RICH_INCR {
		fmt.Printf(" 财富直增卡 ID %d %s 增加值 %d \n",
			cardInf.GetCardId(), cardInf.GetCardName(), cardInf.GetCardValue())
	}
	if cardInf.GetCardType() == pbBackpack.PackageItemType_BACKPACK_CARD_KNIGHT {
		fmt.Printf(" 守护骑士卡 ID %d %s 有效时长 %s \n",
			cardInf.GetCardId(), cardInf.GetCardName(), translateAccelerateCardDuration(cardInf.GetValidTime()))
	}

}

func translateAccelerateCardTimes(times uint32) string {
	timesF := float64(times) / 100
	timesF = float64(int64(timesF*10)) / 10
	return fmt.Sprintf("%.1f", timesF)
}

func translateAccelerateCardDuration(validTime uint32) string {
	var duration string
	// 时间显示逻辑，整数天则显示天数，否则显示整数小时，以此类推
	if validTime%86400 == 0 {
		duration = fmt.Sprintf("%d天", validTime/86400)
	} else if validTime%3600 == 0 {
		duration = fmt.Sprintf("%d小时", validTime/3600)
	} else if validTime%60 == 0 {
		duration = fmt.Sprintf("%d分钟", validTime/60)
	} else {
		duration = fmt.Sprintf("%d秒", validTime)
	}
	return duration
}

// 生成礼物的价格描述
func genPresentPriceMsg(conf *pbUserpresent.PresentBaseConfig) string {
	var presentPriceMsg string
	if conf.GetPriceType() == uint32(pbUserpresent.PresentPriceType_PRESENT_PRICE_TBEAN) {
		presentPriceMsg = fmt.Sprintf("%d个T豆", conf.Price)

	} else if conf.GetPriceType() == uint32(pbUserpresent.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
		presentPriceMsg = fmt.Sprintf("%d个红钻", conf.Price)

	} else {
		presentPriceMsg = fmt.Sprintf("%d个未知(类型%d)单位", conf.Price, conf.GetPriceType())
	}
	return presentPriceMsg
}

func getPresentConfigByIDList(ctx context.Context, presentIDList []uint32) (map[uint32]*pbUserpresent.PresentBaseConfig,
	map[uint32]*pbUserpresent.PresentEffectConfig, map[uint32]*pbUserpresent.PresentEnterConfig,
	error) {
	var err error
	var presentBaseCnfMap = make(map[uint32]*pbUserpresent.PresentBaseConfig)
	var presentEffectCnfMap = make(map[uint32]*pbUserpresent.PresentEffectConfig)
	var presentEnterCnfMap = make(map[uint32]*pbUserpresent.PresentEnterConfig)

	userpresentCli, err2 := common.GetUserpresentCli()
	if err2 != nil {
		err = fmt.Errorf("GetUserpresentCli failed=%v", err2)
		return nil, nil, nil, err
	}
	var presentIDListReq pbUserpresent.GetPresentConfigListByIdListReq
	presentIDListReq.ItemId = presentIDList
	presentResp, errPresent := userpresentCli.GetPresentConfigListByIdListNew(ctx, &presentIDListReq)
	if errPresent != nil {
		err = fmt.Errorf("GetPresentConfigListByIdListNew failed=%v", errPresent)
		return nil, nil, nil, err
	}
	for _, presentItem := range presentResp.GetItemConfig() {
		presentBaseCnfMap[presentItem.BaseConfig.ItemId] = presentItem.BaseConfig
		presentEffectCnfMap[presentItem.BaseConfig.ItemId] = presentItem.EffectConfig
		presentEnterCnfMap[presentItem.BaseConfig.ItemId] = presentItem.EnterConfig
	}
	return presentBaseCnfMap, presentEffectCnfMap, presentEnterCnfMap, nil
}

func getFunCardConfigByIDList(ctx context.Context, cardIDList []uint32) (map[uint32]*pbBackpackCard.FuncCardCfg, error) {
	var err error
	var cardInfoMap = make(map[uint32]*pbBackpackCard.FuncCardCfg)

	bgCardCli, err2 := common.GetBackpackFunCardCli()
	if err2 != nil {
		err = fmt.Errorf("GetBackpackBaseCli failed=%v", err2)
		return nil, err
	}

	cardResp, err3 := bgCardCli.GetFuncCardCfg(ctx, cardIDList)
	if err3 != nil {
		err = fmt.Errorf("GetFuncCardCfg failed=%v", err3)
		return nil, err
	}
	for _, cardItem := range cardResp.GetCardCfgList() {
		cardInfoMap[cardItem.CardId] = cardItem
	}

	return cardInfoMap, nil
}
