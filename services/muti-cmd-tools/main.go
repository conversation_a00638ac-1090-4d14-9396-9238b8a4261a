package main

import (
	"fmt"
	"log"
	"os"

	"github.com/urfave/cli/v2"
	"golang.52tt.com/services/muti-cmd-tools/handler"
)

const version = "1.2.0"

/*
*
初始化
*/
func main() {
	app := &cli.App{
		Name:    "TT Quicksilver CMD Tools ",
		Usage:   "",
		Version: version,
		//Action :  welcomeMsg,
		Commands: getCmdList(),
	}

	err := app.Run(os.Args)
	if err != nil {
		log.Fatal(err)
	}
}

func welcomeMsg(c *cli.Context) error {
	fmt.Println(" TT Quicksilver CMD Tools, -help")
	return nil
}

func getCmdList() []*cli.Command {

	var cmdList []*cli.Command

	cmdList = append(cmdList, handler.DoTestCommand()...)
	cmdList = append(cmdList, handler.ProtoCommand()...)
	cmdList = append(cmdList, handler.NewbieCommand()...)

	cmdList = append(cmdList, handler.AccountPwdCommand()...)
	cmdList = append(cmdList, handler.OnlineCommand()...)
	cmdList = append(cmdList, handler.UserDecoreationCommand()...)
	cmdList = append(cmdList, handler.UserAccountCommand()...)
	cmdList = append(cmdList, handler.UserAccountSQLCommand()...)

	cmdList = append(cmdList, handler.AccountTTIDCommand()...)
	cmdList = append(cmdList, handler.UgcFriendShipCommand()...)

	cmdList = append(cmdList, handler.ChannelInfoCommand()...)
	cmdList = append(cmdList, handler.ChannelLiveInfoCommand()...)
	cmdList = append(cmdList, handler.ChannelTagCommand()...)
	cmdList = append(cmdList, handler.ChannelOlCommand()...)

	cmdList = append(cmdList, handler.BackpackCommand()...)

	return cmdList
}
