package timerManager

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
	"golang.52tt.com/pkg/crontab"
	"golang.52tt.com/pkg/timer"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/conf"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/manager"
	"time"

	"golang.52tt.com/pkg/log"
)

type TimerManager struct {
	//shutDown      chan interface{}
	anchorContractMgr *manager.AnchorContractMgr
	timerD            *timer.Timer
}

func NewTimerManager(ctx context.Context, contractMgr *manager.AnchorContractMgr) *TimerManager {

	log.InfoWithCtx(ctx, "init NewTimerManager begin ")

	dyconfig, err := conf.NewConfigHandler(conf.DyconfigPath)
	if err != nil {
		log.ErrorWithCtx(ctx, "init NewConfigHandler fail, err: %v", err)
		return nil
	}
	if err := dyconfig.Start(); err != nil {
		log.Errorf("dyconfig.Start fail %v", err)
	}
	// 开启定时任务
	cron_, err := crontab.New(crontab.NewOption(dyconfig.GetFeishuNotice()))
	if err != nil {
		log.ErrorWithCtx(ctx, "init NewTimerD fail, err: %v", err)
		return nil
	}
	defer cron_.Start()
	err = cron_.AddFunc("*/60 * * * * *", "UpdateAnchorExtraCertEffect", func() {
		contractMgr.UpdateAnchorExtraCertEffect()
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddFunc ResourceCategoryLoad fail, err: %v", err)
		return nil
	}

	// 定时建后续日期的表
	timerD, err := timer.NewTimerD(ctx, "anchorcontract-go", timer.WithTTL(10*time.Second))
	if nil != err {
		log.ErrorWithCtx(ctx, "init NewTimerD fail, err: %v", err)
		return nil
	}
	mgr := &TimerManager{
		anchorContractMgr: contractMgr,
		timerD:            timerD,
	}

	// 处理过期合约
	err = timerD.AddTask("@every 10s", "ClearTimeOutContractHandle", timer.BuildFromLambda(func(ctx context.Context) {
		mgr.ClearTimeOutContractHandle()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init ClearTimeOutContractHandle fail, err: %v", err)
		return nil
	}

	// 处理过期的解约申请
	err = timerD.AddTask("@every 10s", "ClearTimeOutCancelContractApplyHandle", timer.BuildFromLambda(func(ctx context.Context) {
		contractMgr.ClearTimeOutCancelContractApplyHandle()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init ClearTimeOutCancelContractApplyHandle fail, err: %v", err)
		return nil
	}

	// 更新公会多人合作主播积分缓存
	err = timerD.AddTask("@every 10m", "UpdateGuildScoreCacheHandle", timer.BuildFromLambda(func(ctx context.Context) {
		mgr.UpdateGuildScoreCacheHandle()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init UpdateGuildScoreCacheHandle fail, err: %v", err)
		return nil
	}

	// 处理过期主播考核
	err = timerD.AddTask("@every 5s", "ClearTimeoutLiveAnchorExamine", timer.BuildFromLambda(func(ctx context.Context) {
		contractMgr.ClearTimeoutLiveAnchorExamine()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init ClearTimeoutLiveAnchorExamine fail, err: %v", err)
		return nil
	}

	// 主播标识更新
	err = timerD.AddTask("@every 5s", "NotifyAnchorExamineCert", timer.BuildFromLambda(func(ctx context.Context) {
		contractMgr.NotifyAnchorExamineCert()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init NotifyAnchorExamineCert fail, err: %v", err)
		return nil
	}

	// 定时处理申请提交2小时后的主播签约申请
	err = timerD.AddTask("@every 1m", "AutoPassAnchorApplySign", timer.BuildFromLambda(func(ctx context.Context) {
		contractMgr.AutoPassAnchorApplySign()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init AutoPassAnchorApplySign fail, err: %v", err)
		return nil
	}

	// 定时处理平台达人自动续期
	err = timerD.AddTask("@every 5m", "AutoContinueDoyenSignExpire", timer.BuildFromLambda(func(ctx context.Context) {
		contractMgr.AutoContinueDoyenSignExpire()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init AutoContinueDoyenSignExpire fail, err: %v", err)
		return nil
	}

	// 定时初始化电竞数据
	err = timerD.AddTask("1 0 0 * * *", "AutoInitEsporterTimer", timer.BuildFromLambda(func(ctx context.Context) {
		contractMgr.AutoInitEsporterTimer()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init AutoInitEsporterTimer fail, err: %v", err)
		return nil
	}

	// 主播过期考核通知
	err = timerD.AddTask("1 0 0 * * *", "NotifyOverdueAnchorExamine", timer.BuildFromLambda(func(ctx context.Context) {
		contractMgr.NotifyOverdueAnchorExamine()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init NotifyOverdueAnchorExamine fail, err: %v", err)
		return nil
	}

	// 每天8点通知主播考核
	err = timerD.AddTask("1 0 8 * * *", "NotifyAnchorExamine", timer.BuildFromLambda(func(ctx context.Context) {
		contractMgr.NotifyAnchorExamine()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init NotifyAnchorExamine fail, err: %v", err)
		return nil
	}

	// 定时处理平台达人自动续期
	err = timerD.AddTask("1 0 8 1 * *", "SettleAnchorCertEmotionStoryTimer", timer.BuildFromLambda(func(ctx context.Context) {

		contractMgr.SettleAnchorCertEmotionStoryTimer()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init SettleAnchorCertEmotionStoryTimer fail, err: %v", err)
		return nil
	}

	// 定时处理平台达人自动续期
	err = timerD.AddTask("1 0 8 1 * *", "SettleAnchorCertMusicTimer", timer.BuildFromLambda(func(ctx context.Context) {

		contractMgr.SettleAnchorCertMusicTimer()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init SettleAnchorCertMusicTimer fail, err: %v", err)
		return nil
	}

	// 定时处理平台达人自动续期
	err = timerD.AddTask("1 0 8 1 * *", "SettleAnchorCertTwoDimensionsTimer", timer.BuildFromLambda(func(ctx context.Context) {
		contractMgr.SettleAnchorCertTwoDimensionsTimer()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init SettleAnchorCertTwoDimensionsTimer fail, err: %v", err)
		return nil
	}

	timerD.AddIntervalTask("TimerProcPromoteInvite", time.Second*5, tasks.FuncTask(func(ctx context.Context) {
		contractMgr.TimerProcPromoteInvite()
	}))

	// 定时处理多人互动申请
	err = timerD.AddTask("@every 1m", "AutoPassOrRejectMultiPlayer", timer.BuildFromLambda(func(ctx context.Context) {
		contractMgr.AutoPassOrRejectMultiPlayer()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init AutoPassOrRejectMultiPlayer fail, err: %v", err)
		return nil
	}

	timerD.Start()
	log.InfoWithCtx(ctx, "init NewTimerManager begin timerD:%v", timerD)

	return mgr
}

func (m *TimerManager) ShutDown() {
	m.timerD.Stop()
}

func (m *TimerManager) ClearTimeOutContractHandle() {

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	m.anchorContractMgr.ClearTimeOutContract(ctx)
	//log.Debugf("ClearTimeOutContractHandle done")
}

func (m *TimerManager) UpdateGuildScoreCacheHandle() {
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("UpdateGuildScoreCacheHandle panic err:%v", err)
		}
	}()

	err := m.anchorContractMgr.UpdateAllGuildScoreCache()
	if err != nil {
		log.Errorf("UpdateGuildScoreCacheHandle fail to UpdateAllGuildScoreCache err:%v", err)
		return
	}

	log.Debugf("UpdateGuildScoreCacheHandle done")
}
