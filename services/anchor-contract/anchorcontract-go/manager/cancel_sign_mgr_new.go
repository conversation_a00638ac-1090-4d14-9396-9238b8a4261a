package manager

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/util/mp"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/jinzhu/gorm"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	accountPb "golang.52tt.com/clients/account"
	"golang.52tt.com/clients/obsgateway"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
	"golang.52tt.com/protocol/services/channellivemgr"
	Exchange "golang.52tt.com/protocol/services/exchange"
	guildmanagementpb "golang.52tt.com/protocol/services/guild-management-svr"
	guildPb "golang.52tt.com/protocol/services/guildsvr"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/util"
)

func isCanOperatorAcceptAndReject(cancelType, status uint32) (bool, bool) {
	showAcceptBtn := true
	showRejectBtn := false
	switch cancelType {
	case uint32(pb.CancelContractType_CancelContractType_Negotiate):
		if status == uint32(pb.CancelContractStatus_CancelContractStatus_Apply) {
			showRejectBtn = true
		}
	case uint32(pb.CancelContractType_CancelContractType_Pay):
		if status == uint32(pb.CancelContractStatus_CancelContractStatus_PayOfficeAccepted) {
			showAcceptBtn = true
		} else {
			showAcceptBtn = false
		}
	}
	return showAcceptBtn, showRejectBtn
}

func (m *AnchorContractMgr) canCancelCheckAgent(ctx context.Context, uid, guildId uint32) error {
	//  如果账号是经纪人或者管理不可解约
	agentResp, err := m.GuildManagementSvrCli.GetAgentGuild(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "canCancelCheckAgent GetAgentGuild fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return err
	}
	log.InfoWithCtx(ctx, "canCancelCheckAgent uid:%d, guild:%d, agentGuild:%v", uid, guildId, agentResp)
	agentType := agentResp.GetInfo().GetAgentType()
	if agentResp.GetInfo().GetGuild_Id() == guildId &&
		((agentType&uint32(guildmanagementpb.AgentType_AgentBroker) == uint32(guildmanagementpb.AgentType_AgentBroker)) ||
			(agentType&uint32(guildmanagementpb.AgentType_AgentAdmin) == uint32(guildmanagementpb.AgentType_AgentAdmin))) {
		log.ErrorWithCtx(ctx, "canCancelCheckAgent guild agent limit uid:%d, guild:%d", uid, guildId)
		return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitAgent, "账号属于公会管理人员账号，请与公会协商回收管理权限后再尝试申请")
	}
	return nil
}

func (m *AnchorContractMgr) canCancelCheckExchangeScore(ctx context.Context, uid, guildId uint32) error {
	// 针对对公结算且积分处于冻结中的用户不允许发起解约。
	freezeStatusResp, err := m.ExchangeCli.BatchGetFreezeStatus(ctx, &Exchange.BatchGetFreezeStatusReq{UidList: []uint32{uid}})
	if err != nil {
		log.ErrorWithCtx(ctx, "canCancelCheckExchangeScore BatchGetFreezeStatus fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return err
	}
	guildExchangeDataResp, serr := m.ExchangeGuildClient.GetUidGuildExchangeData(ctx, uid)
	if serr != nil {
		log.ErrorWithCtx(ctx, "canCancelCheckExchangeScore GetUidGuildExchangeData fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return serr
	}
	log.InfoWithCtx(ctx, "canCancelCheckExchangeScore uid:%d, guild:%d, BatchGetFreezeStatus=%+v GetUidGuildExchangeData=%+v", uid, guildId, freezeStatusResp, guildExchangeDataResp)
	if len(freezeStatusResp.GetFrozenUidList()) > 0 && guildExchangeDataResp.GetMasterUid() > 0 {
		log.ErrorWithCtx(ctx, "canCancelCheckExchangeScore exchange limit. uid:%d, guild:%d", uid, guildId)
		return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitExchange, "账号处于积分冻结状态，请联系客服或80513按规定解除冻结后再尝试申请")
	}
	freezeResp, err := m.ExchangeCli.GetScorePartFreezeList(ctx, &Exchange.GetScorePartFreezeListReq{
		Uid:   uid,
		Limit: 10,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "canCancelCheckExchangeScore GetScorePartFreezeList fail id:%d, guild:%d, err:%v", uid, guildId, err)
		return err
	}
	if freezeResp.GetSum().GetStatus() != 0 {
		log.ErrorWithCtx(ctx, "canCancelCheckExchangeScore freeze limit. uid:%d, guild:%d", uid, guildId)
		return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitExchange, "账号处于积分冻结状态，请联系客服或80513按规定解除冻结后再尝试申请")
	}
	return nil
}

// getAnchorIncome 获取主播月收入，总收入
func (m *AnchorContractMgr) getAnchorIncome(ctx context.Context, uid, guildId uint32, monthNum int, isPay bool) ([]int64, int64, error) {
	now := time.Now()
	nowMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	monthIncome := make([]int64, 0)
	totalIncome := int64(0)

	// 付费解约，签约月数小于要求的月份，就算上本月
	if monthNum < int(m.newDyConf.GetContractDyConf().PayCancelContractParam.PayCheckMonth) && isPay {
		nowMonth = nowMonth.AddDate(0, 1, 0)
		monthNum += 1
	}

	for i := 1; i <= monthNum; i++ {
		month := nowMonth.AddDate(0, -i, 0)
		guildIdList := m.dyConfig.GetParentChildGroup(guildId)
		var income int64
		for _, id := range guildIdList {
			valmap, err := m.store.BatchGetGuildUserMonthScore(id, []uint32{uid}, month)
			if err != nil {
				log.ErrorWithCtx(ctx, "getAnchorMonthIncome BatchGetGuildUserMonthScore fail %v, uid=%d, guildId:%d", err, uid, guildId)
				return monthIncome, totalIncome, err
			}
			income += valmap[uid]
		}
		totalIncome += income
		monthIncome = append(monthIncome, income)
		log.InfoWithCtx(ctx, "getAnchorMonthIncome uid=%d guildId=%d month=%s BatchGetGuildUserMonthScore=%d guildIdList:%v", uid, guildId, month, income, guildIdList)
	}
	return monthIncome, totalIncome, nil
}

// getCancelPayAmount 获取付费解约金额
func (m *AnchorContractMgr) getCancelPayAmount(ctx context.Context, info *mysql.ContractInfo) (int64, error) {
	if info.WorkerType == uint32(pb.ContractWorkerType_ContractWorkerType_Manager) {
		if info.PayAmount == 0 {
			log.ErrorWithCtx(ctx, "getCancelPayAmount payAmount empty uid=%d, guildId:%d", info.Uid, info.GuildId)
			return 0, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "未设置解约金额,请联系客服")
		}
		payAmount := int64(0)
		monthDiff := util.MonthsDiff(time.Now(), info.ContractExpireTime)
		if monthDiff <= 3 {
			payAmount = info.PayAmount * 30 / 100
		} else if monthDiff <= 6 {
			payAmount = info.PayAmount * 50 / 100
		} else if monthDiff <= 12 {
			payAmount = info.PayAmount * 70 / 100
		} else {
			payAmount = info.PayAmount
		}

		log.InfoWithCtx(ctx, "getCancelPayAmount uid=%d, guildId=%d, payAmount=%d/%d, monthDiff=%d", info.Uid, info.GuildId, payAmount, info.PayAmount, monthDiff)
		return payAmount, nil
	}
	// - 签约成员过去3个自然月在公会旗下房间月均接档收礼金额*0.15*3个月*倍数
	// - 倍数计算：剩余签约自然月≤6个月的*2倍，剩余签约自然月＞6个月的*3倍
	now := time.Now()
	//now = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	now = now.Add(time.Second * 5)
	dy := m.newDyConf.GetContractDyConf().PayCancelContractParam
	amountParam := dy.CostAmountParam

	payCheckMonth := m.newDyConf.GetContractDyConf().PayCancelContractParam.PayCheckMonth

	// 要往前算几个月的流水
	monthSign := util.MonthsDiff(time.Now(), info.SignTime)
	// 签约满3个月追溯3个月
	if monthSign >= payCheckMonth {
		monthSign = payCheckMonth
	}

	beginTime := now.AddDate(0, -int(monthSign), 0)
	endTime := now.AddDate(0, 0, 0)
	begin := time.Date(beginTime.Year(), beginTime.Month(), 1, 0, 0, 0, 0, beginTime.Location())
	end := time.Date(endTime.Year(), endTime.Month(), 1, 0, 0, 0, 0, endTime.Location())
	// 不满三个月，算上本月
	if monthSign < payCheckMonth {
		end = now
		monthSign += 1
	}

	amount, err := m.store.GetAnchorScoreByTimeRange(ctx, info.Uid, info.GuildId, begin, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "getCancelPayAmount GetAnchorScoreByTimeRange fail %v, uid=%d, guildId:%d", err, info.Uid, info.GuildId)
		return 0, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "获取解约金额失败,请稍后再试")
	}

	monthDiff := util.MonthsDiff(now, info.ContractExpireTime)
	totalAmount := amount
	if monthDiff < amountParam[1] {
		amount = amount * int64(amountParam[2])
	} else {
		amount = amount * int64(amountParam[3])
	}
	if monthDiff == 0 {
		monthDiff = 1
	}

	log.DebugWithCtx(ctx, "getCancelPayAmount MonthsDiff %d", monthDiff)

	// 对应3个月，这里之前写的天数，要除出来
	amount = amount * int64(amountParam[0]/30)
	// 系数，0.15那个
	amount = amount * int64(amountParam[4])
	amount = amount / 100
	// 算月均，这里要注意有几个月的就算几个月
	amount = amount / int64(monthSign) // 改为月均, 有几个月就除以几

	// 比较一下上下限
	oldVal := amount
	if amount < dy.CostAmountLimit[0]*100 {
		amount = dy.CostAmountLimit[0] * 100
	}
	if amount > dy.CostAmountLimit[1]*100 {
		amount = dy.CostAmountLimit[1] * 100
	}
	if amount%100 > 0 { //金额取整
		amount = amount - amount%100 + 100
	}

	log.InfoWithCtx(ctx, "getCancelPayAmount uid=%d, guildId=%d, amount=%d, oldVal=%d, totalAmount:%d, timeRange[%v, %v], monthDiff:%d", info.Uid, info.GuildId, amount, oldVal, totalAmount, beginTime, now, monthDiff)
	return amount, nil
}

func (m *AnchorContractMgr) canCancelCheckTypes(ctx context.Context, uid, guildId, cancelType uint32, proofUrls, videoUrls []string, descTxt string, info *mysql.ContractInfo, paid bool) (uint32, uint32, error) {
	now := time.Now()
	dy := m.newDyConf.GetContractDyConf()
	reason := uint32(0)
	st := uint32(0)
	switch cancelType {
	case uint32(pb.CancelContractType_CancelContractType_NoReason): //无理由解约
		if now.Unix()-info.SignTime.Unix() > int64(dy.NoReasonCancelDays)*24*3600 {
			log.InfoWithCtx(ctx, "canCancelCheckTypes day7 uid=%d, guildId:%d obtainTime=%v", uid, guildId, info.SignTime)
			return reason, st, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "签约超过7天不能使用无理由解约")
		}
		reason = uint32(pb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_DAY7_NOREASON)
		st = uint32(pb.CancelContractStatus_CancelContractStatus_Apply)
	case uint32(pb.CancelContractType_CancelContractType_Quiet): //沉默解约
		monthDiff := util.MonthsDiff(now, info.SignTime)
		if monthDiff < uint32(dy.QuietCancelContractParam.NMonth) {
			log.Errorf("canCancelCheckTypes multiplayers no 2 month vs (monthDiff=%d). uid=%d, obtainTm=%s", monthDiff, uid, info.SignTime)
			return reason, st, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, fmt.Sprintf("签约时长不足%d月，不能使用沉默解约", dy.QuietCancelContractParam.NMonth))
		}
		monthIncome, _, err := m.getAnchorIncome(ctx, uid, guildId, dy.QuietCancelContractParam.NMonth, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "canCancelCheckTypes getAnchorIncome fail %v, uid=%d, guildId:%d", err, uid, guildId)
			return reason, st, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "获取收入失败,请稍后再试")
		}
		for _, income := range monthIncome {
			if income >= int64(dy.QuietCancelContractParam.MonthIncome*100) {
				log.ErrorWithCtx(ctx, "canCancelCheckTypes checkMultiPlayerCancelContract uid=%d, guild:%v, preson_income limit. income=%d ", uid, guildId, income)
				return reason, st, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "未达到沉默解约条件")
			}
		}
		reason = uint32(pb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_PERSON_INCOME)
		st = uint32(pb.CancelContractStatus_CancelContractStatus_Apply)
	case uint32(pb.CancelContractType_CancelContractType_Negotiate): //协商解约
		reason = uint32(pb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_NEGOTIATE)
		st = uint32(pb.CancelContractStatus_CancelContractStatus_Apply)

		// 审核图片
		err := m.SyncCensoringMix(ctx, uid, guildId, descTxt, proofUrls, nil, nil, "", "")
		if err != nil {
			log.ErrorWithCtx(ctx, "canCancelCheckTypes SyncCensoringMix fail %v, uid=%d, guildId:%d", err, uid, guildId)
			if !dy.CensoringIgnoreResult {
				return reason, st, err
			}

		}

	case uint32(pb.CancelContractType_CancelContractType_Pay): //付费解约-
		monthDiff := util.MonthsDiff(now, info.SignTime)
		if monthDiff < dy.PayCancelContractParam.SignMonthLimit {
			log.ErrorWithCtx(ctx, "canCancelCheckTypes signMonthLimit uid=%d, signTime=%v", uid, info.SignTime)
			return reason, st, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, fmt.Sprintf("签约时长不足%d个月，不能使用付费解约", dy.PayCancelContractParam.SignMonthLimit))
		}

		if monthDiff > dy.PayCancelContractParam.PayCheckMonth {
			monthDiff = dy.PayCancelContractParam.PayCheckMonth
		}

		monthIncome, _, err := m.getAnchorIncome(ctx, uid, guildId, int(monthDiff), true)
		if err != nil {
			log.ErrorWithCtx(ctx, "canCancelCheckTypes getAnchorIncome fail %v, uid=%d, guildId:%d", err, uid, guildId)
			return reason, st, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "获取收入失败,请稍后再试")
		}
		ok := false

		monthIncomeOkNum := 0
		for _, income := range monthIncome { //单月收入>2w
			if income > int64(dy.PayCancelContractParam.SingleMonthIncomeLimit*100) {
				log.InfoWithCtx(ctx, "canCancelCheckTypes singleMonthIncomeLimit uid=%d, guild:%v, preson_income limit. income=%v ", uid, guildId, monthIncome)
				ok = true
				break
			}
			if income > int64(dy.PayCancelContractParam.NMonthIncomeLimit*100) {
				monthIncomeOkNum++
			}
		}
		//签约成员满足已于该公会签约满3个自然月，用户在过去3个自然月，2个月房间收礼＞1w或至少1个月房间收礼＞2w ; 已经冻结费用之后不拦截这个；核心管理也不用拦
		if !paid && !ok && monthIncomeOkNum < int(dy.PayCancelContractParam.NMonthLimit) && info.WorkerType != uint32(pb.ContractWorkerType_ContractWorkerType_Manager) {
			log.ErrorWithCtx(ctx, "canCancelCheckTypes singleMonthIncomeLimit uid=%d, guild:%v, preson_income limit. income=%v ", uid, guildId, monthIncome)
			return reason, st, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "未达到付费解约条件")
		}

		// 审核图片
		err = m.SyncCensoringMix(ctx, uid, guildId, descTxt, proofUrls, nil, nil, "", "")
		if err != nil {
			log.ErrorWithCtx(ctx, "canCancelCheckTypes SyncCensoringMix fail %v, uid=%d, guildId:%d", err, uid, guildId)
			if !dy.CensoringIgnoreResult {
				return reason, st, err
			}
		}

		reason = uint32(pb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_PAY)
		st = uint32(pb.CancelContractStatus_CancelContractStatus_Paying)
	}
	return reason, st, nil
}

func (m *AnchorContractMgr) ApplyCancelContractNew(rootCtx context.Context, in *pb.ApplyCancelContractNewReq) error {
	uid := in.Uid
	guildId := in.GuildId
	//上游传递的ctx，过期时间可能不足1分钟，这里重新生成一个ctx，方便审核耗时
	ctx, cancel := grpc.NewContextWithInfoTimeout(rootCtx, time.Minute)
	defer cancel()
	log.InfoWithCtx(ctx, "ApplyCancelContractNew begin %+v, ", in)
	if in.CancelType == uint32(pb.CancelContractType_CancelContractType_Pay) && len(in.ProofUrls) == 0 {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew proofUrls empty uid=%d, guildId:%d", uid, guildId)
		return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "请上传付费解约凭证")
	}
	// 合约
	contract, exist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew GetValidContractWithUid fail uid:%d, guild:%d, err: %v", uid, guildId, err)
		return err
	}
	log.InfoWithCtx(ctx, "ApplyCancelContractNew uid:%d, guild:%d, GetValidContractWithUid=%+v", uid, guildId, contract)
	if !exist {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew fail no contract. uid:%d, guild:%d ", uid, guildId)
		return protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}
	if contract.GuildId != guildId {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew fail no contract. uid:%d, guild:%d contract:%+v", uid, guildId, contract)
		return protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}

	now := time.Now()
	if contract.ContractExpireTime.Sub(now).Hours()/24 < 30 {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew fail contract expireTime. uid:%d, guild:%d contract:%+v", uid, guildId, contract)
		return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "您的签约时长已不足30天，无法发起解约")
	}

	// 看一下是不是在冷却期

	if in.GetCancelType() == uint32(pb.CancelContractType_CancelContractType_Negotiate) {
		coolDown, err := m.store.GetNegotiateCoolDown(ctx, uid, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew GetNegotiateCoolDown fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		}

		endTime := coolDown.BeginTime.Add(time.Duration(m.newDyConf.GetContractDyConf().NegotiateCoolDownTime) * time.Second)
		if endTime.After(now) {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew in coolDown uid:%d, guild:%d, coolDown:%+v", uid, guildId, coolDown)
			return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, fmt.Sprintf("你最近已提交过协商解约申请，请%d天后再继续申请", int(endTime.Sub(now).Hours()/24)+1))
		}
	}

	// 有对应身份=公会在对应合作库
	// 身份
	identityList, err := m.store.GetAnchorIdentity(nil, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew GetAnchorIdentity fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return err
	}
	if len(identityList) == 0 {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew GetAnchorIdentity no identity uid:%d, guild:%d", uid, guildId)
		return protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}
	isNeedDelLockAmount := false
	defer func() {
		if in.CancelType == uint32(pb.CancelContractType_CancelContractType_Pay) && isNeedDelLockAmount {
			_ = m.cache.DelLockPayCancelAmount(ctx, guildId, uid)
		}
	}()
	identity2Info := map[pb.SIGN_ANCHOR_IDENTITY]*mysql.AnchorIdentity{}
	for _, iden := range identityList {
		identity2Info[pb.SIGN_ANCHOR_IDENTITY(iden.IdentityType)] = iden
	}
	//if _, ok := identity2Info[pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER]; !ok {
	//	log.ErrorWithCtx(ctx, "ApplyCancelContractNew only multiplayer uid:%d, guild:%d", uid, guildId)
	//	return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "无娱乐成员身份不能使用该解约方式")
	//}
	if _, ok := identity2Info[pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS]; ok {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew e-sports uid:%d, guild:%d", uid, guildId)
		isNeedDelLockAmount = true
		return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitEsport)
	}

	reason := uint32(0)
	if info, ok := identity2Info[pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE]; ok {
		reasonList, err := m.checkAnchorCancelContract(ctx, uid, guildId, uint32(info.ObtainTime.Unix()))
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew checkAnchorCancelContract fail uid:%d, guild:%d, err:%v", uid, guildId, err)
			return err
		}
		if len(reasonList) == 0 {
			isNeedDelLockAmount = true
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew checkAnchorCancelContract no reason uid:%d, guild:%d", uid, guildId)
			return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "你的语音主播身份不满足解约条件")
		}

		reason = reasonList[0]
	}

	if in.CancelType > 0 { // 检查是否支持该解约类型
		ok, err := m.CheckGuildHasCancelContractType(ctx, guildId, contract.WorkerType, in.CancelType)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew CheckGuildHasCancelContractType fail uid:%d, guild:%d, err:%v", uid, guildId, err)
			return err
		}
		if !ok {
			isNeedDelLockAmount = true
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew CheckGuildHasCancelContractType fail uid:%d, guild:%d", uid, guildId)
			return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "公会不支持该解约类型")
		}
	}

	// 是否已发起申请
	exist, _, err = m.store.GetCancelContractApply(uid, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew GetCancelContractApply fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return err
	}
	if exist {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew has apply uid:%d, guild:%d", uid, guildId)
		return protocol.NewExactServerError(nil, -1062, "已发送过解约申请")
	}

	payAmount := int64(0)
	if in.CancelType == uint32(pb.CancelContractType_CancelContractType_Pay) {
		expireSec := m.newDyConf.GetContractDyConf().PayCancelContractParam.LockAmountExpireS
		_, amount, err := m.cache.GetLockPayCancelAmount(ctx, contract.GuildId, uid, expireSec)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew getCancelPayAmount fail uid:%d, guild:%d, err:%v", uid, guildId, err)
			return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "获取解约金额失败,请稍后再试")
		}
		if amount == 0 {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew getCancelPayAmount amount empty uid=%d, guildId:%d", uid, guildId)
			return protocol.NewExactServerError(nil, status.ErrContractHandleTimeout, "金额锁定时间已过期，请重新申请解约")
		}
		payAmount = amount
	}

	//1.0判断是否是经纪人
	err = m.canCancelCheckAgent(ctx, uid, guildId)
	if err != nil {
		isNeedDelLockAmount = true
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew canCancelCheckAgent fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return err
	}
	//2.0判断是否是对公结算且积分处于冻结中的用户 / 如果已经冻结了解约金额可以不判断
	if payAmount == 0 {
		err = m.canCancelCheckExchangeScore(ctx, uid, guildId)
		if err != nil {
			isNeedDelLockAmount = true
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew canCancelCheckExchangeScore fail uid:%d, guild:%d, err:%v", uid, guildId, err)
			return err
		}
	}

	//3.0 判断解约类型
	imgKeys := in.ProofUrls
	videoKeys := make([]string, 0)
	if in.CancelType == uint32(pb.CancelContractType_CancelContractType_Negotiate) {
		imgKeys = make([]string, 0)
		for _, item := range in.ProofList {
			if item.GetType() == pb.ProofContent_ProofType_Video {
				videoKeys = append(videoKeys, item.GetKey())
			}

			if item.GetType() == pb.ProofContent_ProofType_Image {
				imgKeys = append(imgKeys, item.GetKey())
			}
		}

		censorMap := make(map[string]string)
		if len(videoKeys) > 0 {
			censorMap, err = m.cache.BatchGetCensorKey(ctx, videoKeys)
			if err != nil {
				log.ErrorWithCtx(ctx, "ApplyCancelContractNew BatchGetCensorKey fail uid:%d, guild:%d, err:%v", in.GetUid(), in.GetGuildId(), err)
				return err
			}
		}

		for _, item := range in.ProofList {
			if item.GetType() == pb.ProofContent_ProofType_Video {
				if item.GetCensorKey() != censorMap[item.GetKey()] {
					log.ErrorWithCtx(ctx, "ApplyCancelContractNew video censor fail uid:%d, guild:%d, key:%s, censorKey:%s", uid, guildId, item.GetKey(), item.GetCensorKey())
					return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "视频审核未通过，请重新上传")
				}
			}
		}

		// 可能已经有转码结果了
		videoTranscode, _ := m.cache.BatchGetTranscodeResult(ctx, videoKeys)
		for i, item := range videoKeys {
			if res, ok := videoTranscode[item]; ok {
				videoKeys[i] = res
			}
		}
		log.DebugWithCtx(ctx, "ApplyCancelContractNew videoKeys:%+v, videoTranscode:%+v", videoKeys, videoTranscode)
	}

	imgUrls := make([]string, 0)
	imgUrlMap, err := m.GetObsTempUrl(ctx, imgKeys, 86400)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew GetObsTempUrl fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return err
	}

	for _, key := range imgKeys {
		imgUrls = append(imgUrls, imgUrlMap[key])
	}

	desc := in.PayDesc
	if in.GetCancelType() == uint32(pb.CancelContractType_CancelContractType_Negotiate) {
		desc = in.GetCancelReasonText()
	}
	tmpReason, statu, err := m.canCancelCheckTypes(ctx, uid, guildId, in.CancelType, imgUrls, nil, desc, contract, true)
	if err != nil {
		isNeedDelLockAmount = true

		// 如果是违规，改一下错误码，避免前端直接返回到上一页
		if protocol.ToServerError(err).Code() == status.ErrContractApplySignLimitOther && strings.Contains(protocol.ToServerError(err).Message(), "违规") {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew canCancelCheckTypes fail uid:%d, guild:%d, err:%v", uid, guildId, err)
			return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, protocol.ToServerError(err).Message())
		}

		log.ErrorWithCtx(ctx, "ApplyCancelContractNew canCancelCheckTypes fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return err
	}

	if _, ok := identity2Info[pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER]; ok {
		reason = tmpReason
	}

	identityInfo := &mysql.IdentityInfo{}
	// 补充信息
	cancelApplyLog := &mysql.CancelContractApplyLog{
		Uid:                 in.GetUid(),
		GuildId:             in.GetGuildId(),
		SignTime:            contract.SignTime,
		ContractExpireTime:  contract.ContractExpireTime,
		Status:              statu,
		Reason:              reason,
		ApplyTime:           time.Now(),
		LiveObtainTime:      time.Now(),
		MultiplayObtainTime: time.Now(),
		UpdateTime:          time.Now(),
		CancelType:          in.CancelType,
		ProofUrls:           strings.Join(in.ProofUrls, ","),
		WorkerType:          contract.WorkerType,
		PayAmount:           payAmount,
		IdentityNum:         contract.IdentityNum,
		PayDesc:             in.PayDesc,
	}
	for _, ident := range identityList {
		if ident.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) {
			continue
		}
		identityInfo.AnchorIdentityInfoList = append(identityInfo.AnchorIdentityInfoList, &mysql.AnchorIdentityInfo{
			IdentityType: ident.IdentityType,
			ObtainTime:   uint32(ident.ObtainTime.Unix()),
			AgentUid:     ident.AgentUid,
		})
		if ident.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
			cancelApplyLog.MultiplayIdentityStatus = 1
			cancelApplyLog.MultiplayObtainTime = ident.ObtainTime
		} else if ident.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
			cancelApplyLog.LiveIdentityStatus = 1
			cancelApplyLog.LiveObtainTime = ident.ObtainTime
			cancelApplyLog.AgentUid = ident.AgentUid
		}
		log.InfoWithCtx(ctx, "ApplyCancelContractNew uid:%d, guild:%d, AnchorIdentity=%+v", uid, guildId, ident)
	}

	identityInfo.ReasonList = []uint32{reason}
	identityInfoStr, _ := json.Marshal(identityInfo)
	cancelApplyLog.IdentityInfo = string(identityInfoStr)
	if cancelApplyLog.LiveIdentityStatus == 1 {
		liveAnchorInfo, err := m.liveMgrCli.GetAnchorByUidList(ctx, &channellivemgr.GetAnchorByUidListReq{UidList: []uint32{in.GetUid()}})
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew fail to GetAnchorByUidList in:%+v, err:%v", in, err)
			return err
		}
		if len(liveAnchorInfo.AnchorList) > 0 {
			cancelApplyLog.TagId = liveAnchorInfo.AnchorList[0].TagId
		}
	}
	err = m.store.Transaction(ctx, func(tx *gorm.DB) error {
		err := m.store.AddCancelContractApply(tx, &mysql.CancelContractApply{
			Uid:            in.GetUid(),
			GuildId:        in.GetGuildId(),
			GuildName:      "",
			ApplyTime:      time.Now(),
			CancelType:     in.GetCancelType(),
			Reason:         reason,
			Status:         statu,
			ImageProofList: strings.Join(imgKeys, ","),
			VideoProofList: strings.Join(videoKeys, ","),
			ReasonText:     in.GetCancelReasonText(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew fail to AddCancelContractApply in:%+v, err:%v", in, err)
			return err
		}

		cancelApplyLog.ReasonText = in.GetCancelReasonText()
		cancelApplyLog.VideoProofList = strings.Join(videoKeys, ",")
		cancelApplyLog.ImageProofList = strings.Join(imgKeys, ",")

		err = m.store.AddCancelContractApplyLog(tx, cancelApplyLog)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew fail to AddCancelContractApplyLog in:%+v, err:%v", in, err)
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew fail to Transaction in:%+v, err:%v", in, err)
		return err
	}

	// 到这里提交申请成功了，删除一下冻结解约金产生的临时记录
	_ = m.cache.DelTmpContractLog(ctx, guildId, uid)

	// 如果有视频，记录下转码前的对应关系
	if len(in.GetProofList()) > 0 {
		// 获取刚刚的那条申请
		// 是否已发起申请
		apply, err := m.store.GetCancelContractApplyLog(uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew GetCancelContractApply fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		}

		for _, v := range in.GetProofList() {
			if v.GetType() == pb.ProofContent_ProofType_Video {
				err = m.cache.RecordApplyToKey(ctx, v.Key, apply.ApplyId)
				if err != nil {
					log.ErrorWithCtx(ctx, "RecordApplyToKey fail key %s err %v", v.Key, err)
				}

				err = m.cache.RecordUidGuildIdToKey(ctx, v.Key, apply.Uid, apply.GuildId)
				if err != nil {
					log.ErrorWithCtx(ctx, "RecordUidGuildIdToKey fail key %s err %v", v.Key, err)
				}
			}
		}
	}

	log.InfoWithCtx(ctx, "ApplyCancelContractNew end. uid=%d guildid=%d reasonList=%v", uid, guildId, reason)
	return nil

}

func (m *AnchorContractMgr) GetOfficialCancelSignList(ctx context.Context, req *pb.GetOfficialCancelSignListReq) (*pb.GetOfficialCancelSignListResp, error) {
	resp := &pb.GetOfficialCancelSignListResp{}
	if req.CancelType != uint32(pb.CancelContractType_CancelContractType_Pay) && req.CancelType != uint32(pb.CancelContractType_CancelContractType_Negotiate) {
		log.ErrorWithCtx(ctx, "GetOfficialCancelSignList fail cancelType  req: %v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	uid := uint32(0)
	var err error
	if len(req.Tid) > 0 {
		uid, _, err = m.accountCli.GetUidByName(ctx, req.Tid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetOfficialCancelSignList GetUidByName fail req: %v, err: %v", req, err)
			return resp, err
		}
	}
	guildId := uint32(0)
	if req.GuildId > 0 {
		rsp, err := m.guildCli.GetGuild(ctx, req.GuildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetOfficialCancelSignList GetGuild fail req: %v, err: %v", req, err)
			return resp, err
		}
		if rsp == nil || rsp.GuildId == 0 || rsp.IsDelete == 1 {
			log.ErrorWithCtx(ctx, "GetOfficialCancelSignList guild not exist req: %v, rsp: %v", req, rsp)
			return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "公会不存在")
		}
		guildId = rsp.GuildId
	}

	applyStatus := make([]uint32, 0)
	switch req.CancelType {
	case uint32(pb.CancelContractType_CancelContractType_Pay):
		if req.HandleStatus == uint32(pb.GetOfficialCancelSignListReq_HandleStatus_Running) {
			applyStatus = []uint32{uint32(pb.CancelContractStatus_CancelContractStatus_Paying)}
		} else if req.HandleStatus == uint32(pb.GetOfficialCancelSignListReq_HandleStatus_Done) {
			applyStatus = []uint32{uint32(pb.CancelContractStatus_CancelContractStatus_PayOfficeAccepted),
				uint32(pb.CancelContractStatus_CancelContractStatus_PayOfficeReject),
				uint32(pb.CancelContractStatus_CancelContractStatus_Finish),
			}
		} else {
			applyStatus = []uint32{uint32(pb.CancelContractStatus_CancelContractStatus_Paying),
				uint32(pb.CancelContractStatus_CancelContractStatus_PayOfficeAccepted),
				uint32(pb.CancelContractStatus_CancelContractStatus_PayOfficeReject),
				uint32(pb.CancelContractStatus_CancelContractStatus_Finish),
			}
		}
	case uint32(pb.CancelContractType_CancelContractType_Negotiate):
		if req.HandleStatus == uint32(pb.GetOfficialCancelSignListReq_HandleStatus_Running) {
			applyStatus = []uint32{uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateReject)}
		} else if req.HandleStatus == uint32(pb.GetOfficialCancelSignListReq_HandleStatus_Done) {
			applyStatus = []uint32{uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateOfficeAccepted),
				uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateOfficeReject),
				uint32(pb.CancelContractStatus_CancelContractStatus_NegotinateFinish),
			}
		} else {
			applyStatus = []uint32{uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateReject),
				uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateOfficeAccepted),
				uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateOfficeReject),
				uint32(pb.CancelContractStatus_CancelContractStatus_NegotinateFinish),
			}
		}
	}

	// 获取解约申请列表
	applyList, totalNum, er := m.store.GetOfficialCancelContractApplyLogList(ctx, uid, guildId, req.CancelType, applyStatus, (req.Page-1)*req.PageSize, req.PageSize, req.GetBeginTime(), req.GetEndTime())
	if er != nil {
		log.ErrorWithCtx(ctx, "GetOfficialCancelSignList GetOfficialCancelContractApplyList fail req: %v, err: %v", req, er)
		return resp, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "获取解约申请列表失败")
	}
	resp.TotalCnt = totalNum
	uids := make([]uint32, 0)
	uidMaps := make(map[uint32]struct{})
	guildIds := make([]uint32, 0)
	guildIdMaps := make(map[uint32]struct{})
	for _, apply := range applyList {
		if _, ok := uidMaps[apply.Uid]; !ok {
			uids = append(uids, apply.Uid)
			uidMaps[apply.Uid] = struct{}{}
		}

		if _, ok := guildIdMaps[apply.GuildId]; !ok {
			guildIds = append(guildIds, apply.GuildId)
			guildIdMaps[apply.GuildId] = struct{}{}
		}
	}
	// 获取用户信息
	userMap := make(map[uint32]*accountPb.User)
	if len(uids) > 0 {
		userMap, err = m.accountCli.GetUsersMap(ctx, uids)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetOfficialCancelSignList GetUserInfoBat fail req: %v, uids:%v, err: %v", req, uids, err)
			return resp, err
		}
	}
	// 获取公会信息
	guildMap := make(map[uint32]*guildPb.GuildResp)
	if len(guildIds) > 0 {
		rsp, err := m.guildCli.GetGuildBat(ctx, 0, &guildPb.GetGuildBatReq{
			GuildIdList:   guildIds,
			WithDismissed: true,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetOfficialCancelSignList GetGuildBat fail req: %v, guildIds:%v, err: %v", req, guildIds, err)
			return resp, err
		}
		for _, guild := range rsp.GetGuildList() {
			guildMap[guild.GuildId] = guild
		}
	}

	allUrls := make([]string, 0)
	for _, apply := range applyList {
		if _, ok := userMap[apply.Uid]; !ok {
			log.ErrorWithCtx(ctx, "GetOfficialCancelSignList user not exist req: %v, apply: %v", req, apply)
			continue
		}
		_, ok := guildMap[apply.GuildId]
		if !ok {
			log.ErrorWithCtx(ctx, "GetOfficialCancelSignList guild not exist req: %v, apply: %v", req, apply)
			continue
		}
		if len(apply.ProofUrls) > 0 {
			urls := strings.Split(apply.ProofUrls, ",")
			allUrls = append(allUrls, urls...)
		}
		if len(apply.ProofVideoUrls) > 0 {
			urls := strings.Split(apply.ProofVideoUrls, ",")
			allUrls = append(allUrls, urls...)
		}
	}
	allTargetUrls, err := m.CheckAndTransforUrls(ctx, allUrls)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOfficialCancelSignList CheckAndTransforUrls fail req: %v, err: %v", req, err)
		return resp, err
	}
	for _, apply := range applyList {
		if _, ok := userMap[apply.Uid]; !ok {
			log.ErrorWithCtx(ctx, "GetOfficialCancelSignList user not exist req: %v, apply: %v", req, apply)
			continue
		}
		guildInfo, ok := guildMap[apply.GuildId]
		if !ok {
			log.ErrorWithCtx(ctx, "GetOfficialCancelSignList guild not exist req: %v, apply: %v", req, apply)
			continue
		}
		gid := guildInfo.GuildId
		if guildInfo.ShortId > 0 { //优先展示短号
			gid = guildInfo.ShortId
		}
		proofUrls := make([]string, 0)
		if len(apply.ProofUrls) > 0 {
			urls := strings.Split(apply.ProofUrls, ",")
			for _, url := range urls {
				if _, ok := allTargetUrls[url]; ok {
					proofUrls = append(proofUrls, allTargetUrls[url])
				}
			}
		}
		proofVideoUrls := make([]string, 0)
		if len(apply.ProofVideoUrls) > 0 {
			urls := strings.Split(apply.ProofVideoUrls, ",")
			for _, url := range urls {
				if _, ok := allTargetUrls[url]; ok {
					proofVideoUrls = append(proofVideoUrls, allTargetUrls[url])
				}
			}
		}

		proofContents, err := m.tranProofKeyListToProofUrlList(ctx, apply.ImageProofList, apply.VideoProofList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetOfficialCancelSignList tranProofKeyListToProofUrlList fail req: %v, err: %v", req, err)
		}

		log.DebugWithCtx(ctx, "GetOfficialCancelSignList proofContents:%v", proofContents)

		resp.InfoList = append(resp.InfoList, &pb.OfficialCancelSignInfo{
			ApplyId:       apply.ApplyId,
			GuildId:       gid,
			GuildName:     guildMap[apply.GuildId].Name,
			CancelType:    apply.CancelType,
			ProofUrls:     proofUrls,
			ApplyUid:      apply.Uid,
			ApplyTs:       uint32(apply.ApplyTime.Unix()),
			ApplyNickname: userMap[apply.Uid].Nickname,
			ApplyTid:      userMap[apply.Uid].Alias,
			Status:        apply.Status,
			RejectTxt:     apply.RejectTxt,
			HandleDesc:    apply.OfficialNote,
			WorkType:      apply.WorkerType,
			// 除100然后向上取整
			PayAmount:          (apply.PayAmount + 99) / 100,
			PayDesc:            apply.PayDesc,
			OfficialRemark:     apply.OfficialRemark,
			OfficialRemarkOper: apply.OfficialRemarkOper,
			OfficialRemarkTs:   apply.OfficialRemarkTs,
			ProofVideoUrls:     proofVideoUrls,
			CancelReason:       apply.ReasonText,
			ProofList:          proofContents,
		})
	}
	log.InfoWithCtx(ctx, "GetOfficialCancelSignList end req: %v, resp: %v", req, resp)
	return resp, nil
}

// OfficialHandleCancelSign 官方处理解约申请(协商解约、付费解约)
func (m *AnchorContractMgr) OfficialHandleCancelSign(ctx context.Context, req *pb.OfficialHandleCancelSignReq) (*pb.OfficialHandleCancelSignResp, error) {
	resp := &pb.OfficialHandleCancelSignResp{}
	if req.ApplyId == 0 || req.HandleResult == 0 {
		log.ErrorWithCtx(ctx, "OfficialHandleCancelSign fail req: %v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	// 获取解约申请
	apply, err := m.store.GetCancelContractApplyLogById(req.ApplyId)
	if err != nil {
		log.ErrorWithCtx(ctx, "OfficialHandleCancelSign GetCancelContractApplyLogById fail req: %v, err: %v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "获取解约申请失败")
	}
	if apply == nil {
		log.ErrorWithCtx(ctx, "OfficialHandleCancelSign fail apply not exist req: %v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "解约申请已处理")
	}

	if apply.CancelType != uint32(pb.CancelContractType_CancelContractType_Pay) && apply.CancelType != uint32(pb.CancelContractType_CancelContractType_Negotiate) {
		log.ErrorWithCtx(ctx, "OfficialHandleCancelSign fail cancelType req: %v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	if req.HandleResult == uint32(pb.OfficialHandleCancelSignReq_HandleResult_Agree) {
		isAbort, err := m.checkApplyCancelAbortWhenAccept(ctx, apply.Uid, apply.GuildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "OfficialHandleCancelSign canCancelCheckExchangeScore fail uid:%d, guild:%d, err:%v", apply.Uid, apply.GuildId, err)
			if isAbort { //解约失败, 终止申请
				_ = m.AbortCancelContractApply(ctx, apply.Uid, apply.GuildId, apply.CancelType, req.ApplyId, "解约失败")
			}
			return resp, err
		}
	}
	// 合约
	//contract, exist, err := m.store.GetValidContractWithUid(apply.Uid)
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "OfficialHandleCancelSign GetValidContractWithUid fail uid:%d, guild:%d, err: %v", apply.Uid, apply.GuildId, err)
	//	return resp, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther)
	//}
	//if !exist || contract.GuildId != apply.GuildId {
	//	log.ErrorWithCtx(ctx, "OfficialHandleCancelSign fail no contract. uid:%d, guild:%d ", apply.Uid, apply.GuildId)
	//	return resp, protocol.NewExactServerError(nil, status.ErrContractNonexist, "该用户合约已失效")
	//}

	st := uint32(0)
	userMsg := ""
	if apply.CancelType == uint32(pb.CancelContractType_CancelContractType_Pay) { //付费解约
		if apply.Status != uint32(pb.CancelContractStatus_CancelContractStatus_Paying) {
			log.ErrorWithCtx(ctx, "OfficialHandleCancelSign fail status req: %v", req)
			return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该记录已经被处理")
		}
		if req.HandleResult == uint32(pb.OfficialHandleCancelSignReq_HandleResult_Agree) {
			st = uint32(pb.CancelContractStatus_CancelContractStatus_PayOfficeAccepted)
		} else {
			st = uint32(pb.CancelContractStatus_CancelContractStatus_PayOfficeReject)
			userMsg = "您发起的付费解约申请，官方审核后未通过，请联系官方工作人员，TTid：80119进行处理"
			err := m.cache.DelLockPayCancelAmount(ctx, apply.GuildId, apply.Uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "OfficialHandleCancelSign fail to DelLockPayCancelAmount apply: %v, err: %v", apply, err)
			}
		}
	} else if apply.CancelType == uint32(pb.CancelContractType_CancelContractType_Negotiate) { //协商解约
		if apply.Status != uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateReject) { //只有会长拒绝时才能处理
			log.ErrorWithCtx(ctx, "OfficialHandleCancelSign fail status req: %v", req)
			return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该记录已经被处理")
		}
		if req.HandleResult == uint32(pb.OfficialHandleCancelSignReq_HandleResult_Agree) {
			st = uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateOfficeAccepted)
		} else {
			st = uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateOfficeReject)
			userMsg = "官方拒绝您的协商解约，理由:" + req.HandleDesc
		}
	} else {
		log.ErrorWithCtx(ctx, "OfficialHandleCancelSign fail cancelType req: %v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	apply.Status = st
	apply.OfficialNote = req.HandleDesc
	apply.OfficialRemark = req.OfficialRemark
	apply.OfficialRemarkOper = req.OfficialRemarkOper
	apply.OfficialRemarkTs = uint32(time.Now().Unix())
	err = m.UpdateCancelContractApplyInfo(ctx, apply)
	if err != nil {
		log.ErrorWithCtx(ctx, "OfficialHandleCancelSign fail to Transaction apply: %v, err: %v", apply, err)
		return resp, err
	}

	// 如果是协商解约，被官方拒绝了，15天之内不能再次申请
	if apply.CancelType == uint32(pb.CancelContractType_CancelContractType_Negotiate) && req.HandleResult == uint32(pb.OfficialHandleCancelSignReq_HandleResult_Reject) {
		err = m.store.AddNegotiateCoolDown(ctx, &mysql.NegotiateCoolDown{
			Uid:       apply.Uid,
			GuildId:   apply.GuildId,
			BeginTime: time.Now(),
			EndTime:   time.Now().Add(time.Second * time.Duration(m.newDyConf.GetContractDyConf().NegotiateCoolDownTime)),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateCancelContractApplyInfo fail to UpdateCancelContractApplyTime apply: %v, err: %v", apply, err)
			// 这里可以不报错
		}
	}

	if len(userMsg) > 0 { //发送拒绝TT助手消息
		_ = m.SendIMMsg(ctx, apply.Uid, userMsg)
	}
	log.InfoWithCtx(ctx, "OfficialHandleCancelSign end req: %v, apply: %v", req, apply)
	return resp, nil
}

// OfficialRemarkPayCancelSign 官方备注付费解约
func (m *AnchorContractMgr) OfficialRemarkPayCancelSign(ctx context.Context, req *pb.OfficialRemarkPayCancelSignReq) (*pb.OfficialRemarkPayCancelSignResp, error) {
	resp := &pb.OfficialRemarkPayCancelSignResp{}
	if req.ApplyId == 0 {
		log.ErrorWithCtx(ctx, "OfficialRemarkPayCancelSign fail req: %v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	// 获取解约申请
	apply, err := m.store.GetCancelContractApplyLogById(req.ApplyId)
	if err != nil {
		log.ErrorWithCtx(ctx, "OfficialRemarkPayCancelSign GetCancelContractApplyLogById fail req: %v, err: %v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "获取解约申请失败")
	}
	if apply == nil {
		log.ErrorWithCtx(ctx, "OfficialRemarkPayCancelSign fail apply not exist req: %v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "解约申请已处理")
	}

	err = m.store.UpdateCancelContractRemarkById(ctx, req.ApplyId, req.OfficialRemark, req.OfficialRemarkOper)
	if err != nil {
		log.ErrorWithCtx(ctx, "OfficialRemarkPayCancelSign fail to Transaction apply: %v, err: %v", apply, err)
		return resp, err
	}
	log.InfoWithCtx(ctx, "OfficialRemarkPayCancelSign end req: %v, apply: %v", req, apply)
	return resp, nil
}

func (m *AnchorContractMgr) UpdateCancelContractApplyInfo(ctx context.Context, apply *mysql.CancelContractApplyLog) error {
	return m.store.Transaction(ctx, func(tx *gorm.DB) error {
		err := m.store.UpdateCancelContractApplyLogInfoById(tx, apply)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateCancelContractApplyInfo fail to UpdateCancelContractApplyLogInfoById apply: %v, err: %v", apply, err)
			return err
		}

		if apply.Status == uint32(pb.CancelContractStatus_CancelContractStatus_PayOfficeReject) || apply.Status == uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateOfficeReject) {
			// 官方拒绝时直接删除申请
			err = m.store.DelCancelContractApply(tx, apply.Uid, apply.GuildId)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateCancelContractApplyInfo fail to UpdateContractStatus apply: %v, err: %v", apply, err)
				return err
			}
		} else {
			err = m.store.UpdateCancelContractApply(tx, apply.Uid, apply.GuildId, apply.Status)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateCancelContractApplyInfo fail to UpdateCancelContractApply apply: %v, err: %v", apply, err)
				return err
			}
		}

		return nil
	})
}

func (m *AnchorContractMgr) AbortCancelContractApply(ctx context.Context, uid, guildId, cancelType, applyId uint32, notes string) error {
	txErr := m.store.Transaction(ctx, func(tx *gorm.DB) error {
		err := m.store.DelCancelContractApply(tx, uid, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "AbortCancelContractApply fail to DelCancelContractApply uid:%d, guildId:%d, cancelType: %d, applyId:%d,  err: %v", uid, guildId, cancelType, applyId, err)
			return err
		}

		err = m.store.AbortCancelContractApplyLog(tx, uid, guildId, cancelType, applyId, notes)
		if err != nil {
			log.ErrorWithCtx(ctx, "AbortCancelContractApply fail to AbortCancelContractApplyLog uid:%d, guildId:%d, cancelType: %d, applyId:%d, err: %v", uid, guildId, cancelType, applyId, err)
			return err
		}
		return nil
	})
	if txErr != nil {
		log.ErrorWithCtx(ctx, "AbortCancelContractApply fail to Transaction uid:%d, guildId:%d, cancelType: %d, err: %v", uid, guildId, cancelType, txErr)
		return txErr
	}
	if len(notes) > 0 {
		_ = m.SendIMMsg(ctx, uid, "您发起的娱乐厅解约申请流程已终止，请重新发起")
	}
	log.InfoWithCtx(ctx, "AbortCancelContractApply ok end uid:%d, guildId:%d, cancelType: %d, applyId:%d, notes:%s", uid, guildId, cancelType, applyId, notes)
	return nil
}

func (m *AnchorContractMgr) GetContractWorkerConfigs(ctx context.Context) (*pb.GetContractWorkerConfigsResp, error) {
	resp := &pb.GetContractWorkerConfigsResp{}
	for t, workerConfig := range m.newDyConf.GetContractDyConf().MapType2Worker {
		resp.ConfigList = append(resp.ConfigList, &pb.ContractWorker{
			WorkerType: t,
			WorkerName: workerConfig.Name,
			WorkerDesc: workerConfig.Desc,
		})
	}

	sort.Slice(resp.ConfigList, func(i, j int) bool {
		return resp.ConfigList[i].WorkerType < resp.ConfigList[j].WorkerType
	})
	log.InfoWithCtx(ctx, "GetContractWorkerConfigs end resp: %v", resp)
	return resp, nil
}

// checkApplyCancelAbortWhenAccept 当处理解约申请时检查是否终止
func (m *AnchorContractMgr) checkApplyCancelAbortWhenAccept(ctx context.Context, uid, guildId uint32) (bool, error) {
	contract, exist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkApplyCancelAbortWhenAccept GetValidContractWithUid fail uid:%d, guild:%d, err: %v", uid, guildId, err)
		return false, err
	}
	log.InfoWithCtx(ctx, "checkApplyCancelAbortWhenAccept uid:%d, guild:%d, GetValidContractWithUid=%+v", uid, guildId, contract)
	if !exist {
		log.ErrorWithCtx(ctx, "checkApplyCancelAbortWhenAccept fail no contract. uid:%d, guild:%d ", uid, guildId)
		return true, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}
	if contract.GuildId != guildId {
		log.ErrorWithCtx(ctx, "checkApplyCancelAbortWhenAccept fail no contract. uid:%d, guild:%d contract:%+v", uid, guildId, contract)
		return true, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}
	exist, applyInfo, err := m.store.GetCancelContractApply(uid, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkApplyCancelAbortWhenAccept GetCancelContractApply fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return false, err
	}
	if !exist {
		log.ErrorWithCtx(ctx, "checkApplyCancelAbortWhenAccept has apply uid:%d, guild:%d", uid, guildId)
		return true, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "该申请已经被处理")
	}

	identityList, err := m.store.GetAnchorIdentity(nil, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkApplyCancelAbortWhenAccept GetAnchorIdentity fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return false, err
	}
	if len(identityList) == 0 {
		log.ErrorWithCtx(ctx, "checkApplyCancelAbortWhenAccept GetAnchorIdentity no identity uid:%d, guild:%d", uid, guildId)
		return true, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}
	identityMaps := make(map[uint32]*mysql.AnchorIdentity)
	for _, iden := range identityList {
		identityMaps[iden.IdentityType] = iden
	}
	if _, ok := identityMaps[uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS)]; ok {
		log.ErrorWithCtx(ctx, "checkApplyCancelAbortWhenAccept e-sports uid:%d, guild:%d", uid, guildId)
		return true, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitEsport, "电竞主播不支持解约")
	}
	if _, ok := identityMaps[uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE)]; ok {
		reasons, err := m.checkAnchorCancelContract(ctx, uid, guildId,
			uint32(identityMaps[uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE)].ObtainTime.Unix()))
		if err != nil {
			log.ErrorWithCtx(ctx, "checkApplyCancelAbortWhenAccept checkAnchorCancelContract fail uid:%v, err:%v", uid, err)
			return false, err
		}
		log.InfoWithCtx(ctx, "checkApplyCancelAbortWhenAccept uid=%d checkAnchorCancelContract=%v", uid, reasons)
		if len(reasons) == 0 {
			log.ErrorWithCtx(ctx, "checkApplyCancelAbortWhenAccept anchor limit. uid=%d, guildId:%d", uid, guildId)
			return true, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitEsport, "不满足语音解约条件，无法解约")
		}
	}

	//1.0判断是否是经纪人
	err = m.canCancelCheckAgent(ctx, uid, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkApplyCancelAbortWhenAccept canCancelCheckAgent fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return true, err
	}

	//2.0判断是否是对公结算且积分处于冻结中的用户
	err = m.canCancelCheckExchangeScore(ctx, uid, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkApplyCancelAbortWhenAccept canCancelCheckExchangeScore fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return true, err
	}

	if applyInfo.CancelType > 0 { // 检查是否支持该解约类型
		ok, err := m.CheckGuildHasCancelContractType(ctx, guildId, contract.WorkerType, applyInfo.CancelType)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew CheckGuildHasCancelContractType fail uid:%d, guild:%d, err:%v", uid, guildId, err)
			return false, err
		}
		if !ok {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew CheckGuildHasCancelContractType fail uid:%d, guild:%d", uid, guildId)
			return true, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "解约失败，不支持该解约类型")
		}
	}

	_, _, err = m.canCancelCheckTypes(ctx, uid, guildId, applyInfo.CancelType, nil, nil, "", contract, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkApplyCancelAbortWhenAccept canCancelCheckTypes fail uid:%d, guild:%d, type:%d, err:%v", uid, guildId, applyInfo.CancelType, err)
		return true, err
	}
	log.InfoWithCtx(ctx, "checkApplyCancelAbortWhenAccept ok uid:%d, guildId:%d, cancelType:%d", uid, guildId, applyInfo.CancelType)
	return false, nil
}

// CheckCanApplyCancelContractV2 检查是否可以发起解约申请
func (m *AnchorContractMgr) CheckCanApplyCancelContractV2(ctx context.Context, in *pb.CheckCanApplyCancelContractV2Req) (*pb.CheckCanApplyCancelContractV2Resp, error) {
	resp := &pb.CheckCanApplyCancelContractV2Resp{
		CantReasonMap: make(map[uint32]string),
	}
	uid := in.Uid
	guildId := in.GuildId
	contract, exist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 GetValidContractWithUid fail uid:%d, guild:%d, err: %v", uid, guildId, err)
		return resp, err
	}
	log.InfoWithCtx(ctx, "CheckCanApplyCancelContractV2 uid:%d, guild:%d, GetValidContractWithUid=%+v", uid, guildId, contract)
	if !exist {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 fail no contract. uid:%d, guild:%d ", uid, guildId)
		return resp, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}
	if contract.GuildId != guildId {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 fail no contract. uid:%d, guild:%d contract:%+v", uid, guildId, contract)
		return resp, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}
	now := time.Now()
	if contract.ContractExpireTime.Sub(now).Hours()/24 < 30 {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 fail contract expireTime. uid:%d, guild:%d contract:%+v", uid, guildId, contract)
		return resp, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "您的签约时长已不足30天，无法发起解约")
	}
	// 身份
	identityList, err := m.store.GetAnchorIdentity(nil, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 GetAnchorIdentity fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return resp, err
	}
	if len(identityList) == 0 {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 GetAnchorIdentity no identity uid:%d, guild:%d", uid, guildId)
		return resp, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}
	identityMaps := make(map[uint32]*mysql.AnchorIdentity)
	for _, iden := range identityList {
		identityMaps[iden.IdentityType] = iden
	}

	if !in.OnlyMulti {
		if _, ok := identityMaps[uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS)]; ok {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew e-sports uid:%d, guild:%d", uid, guildId)
			return resp, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitEsport, "电竞主播不支持解约")
		}
		if _, ok := identityMaps[uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE)]; ok {
			reasons, err := m.checkAnchorCancelContract(ctx, uid, guildId,
				uint32(identityMaps[uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE)].ObtainTime.Unix()))
			if err != nil {
				log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 checkAnchorCancelContract fail %v, in=%+v", err, in)
				return resp, err
			}
			log.InfoWithCtx(ctx, "CheckCanApplyCancelContractV2 uid=%d checkAnchorCancelContract=%v", uid, reasons)
			if len(reasons) == 0 {
				log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 anchor limit. uid=%d, guildId:%d", uid, guildId)
				return resp, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitEsport, "你还不满足语音解约条件，无法申请解约")
			}
		}
	}

	// 是否已发起申请
	exist, applyInfo, err := m.store.GetCancelContractApply(uid, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 GetCancelContractApply fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return resp, err
	}

	if exist {
		resp.Status = applyInfo.Status
		resp.StatusEndTime = uint32(applyInfo.ApplyTime.AddDate(0, 0, 2).Unix())
		resp.CancelType = applyInfo.CancelType
		if applyInfo.Status == uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateReject) || applyInfo.Status == uint32(pb.CancelContractStatus_CancelContractStatus_Apply) ||
			applyInfo.Status == uint32(pb.CancelContractStatus_CancelContractStatus_PayOfficeAccepted) || applyInfo.Status == uint32(pb.CancelContractStatus_CancelContractStatus_Paying) ||
			applyInfo.Status == uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateOfficeAccepted) {
			return resp, nil
		}

		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 has apply uid:%d, guild:%d", uid, guildId)
		return resp, protocol.NewExactServerError(nil, -1062, "已发送过解约申请")
	}

	// 有其他签约申请要拦住
	err = m.CheckAnchorApply(ctx, uid, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew ApplySignPreCheckApplyRecord fail uid:%d, guild:%d, err:%v", uid, guildId)
		return resp, err
	}

	// 获取一下是否已经冻结了解约金额
	payAmount := int64(0)
	expireSec := m.newDyConf.GetContractDyConf().PayCancelContractParam.LockAmountExpireS
	_, payAmount, _ = m.cache.GetLockPayCancelAmount(ctx, contract.GuildId, uid, expireSec)

	//1.0判断是否是经纪人
	err = m.canCancelCheckAgent(ctx, uid, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 canCancelCheckAgent fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		return resp, err
	}

	// 如果已经冻结了解约金额，不检查这个条件
	if payAmount != 0 {
		//2.0判断是否是对公结算且积分处于冻结中的用户
		err = m.canCancelCheckExchangeScore(ctx, uid, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 canCancelCheckExchangeScore fail uid:%d, guild:%d, err:%v", uid, guildId, err)
			return resp, err
		}
	}

	if _, ok := identityMaps[uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER)]; ok {
		for t, _ := range pb.CANCEL_REASON_TYPE_name {
			if t == 0 {
				continue
			}
			_, _, err := m.canCancelCheckTypes(ctx, uid, guildId, uint32(t), nil, nil, "", contract, false)
			if err != nil {
				resp.CantReasonMap[uint32(t)] = err.Error()
			}
		}
	}

	resp.ShowButDisableMap = make(map[uint32]string)
	if resp.CantReasonMap[uint32(pb.CancelContractType_CancelContractType_Negotiate)] == "" {
		coolDown, err := m.store.GetNegotiateCoolDown(ctx, uid, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew GetNegotiateCoolDown fail uid:%d, guild:%d, err:%v", uid, guildId, err)
		}

		endTime := coolDown.BeginTime.Add(time.Duration(m.newDyConf.GetContractDyConf().NegotiateCoolDownTime) * time.Second)
		if endTime.After(now) {
			log.ErrorWithCtx(ctx, "ApplyCancelContractNew in coolDown uid:%d, guild:%d, coolDown:%+v", uid, guildId, coolDown)
			resp.ShowButDisableMap[uint32(pb.CancelContractType_CancelContractType_Negotiate)] = protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, fmt.Sprintf("你最近已提交过协商解约申请，请%d天后再继续申请", int(endTime.Sub(now).Hours()/24)+1)).Message()
		}
	}

	if resp.CantReasonMap[uint32(pb.CancelContractType_CancelContractType_Pay)] == "" {
		errMsg := m.CheckPayCancelCancelSignLimit(ctx, uid, guildId)
		if errMsg != "" {
			resp.ShowButDisableMap[uint32(pb.CancelContractType_CancelContractType_Pay)] = errMsg
		}
	}

	log.InfoWithCtx(ctx, "CheckCanApplyCancelContractV2 all end uid:%d, guildId:%d, resp: %v", uid, guildId, resp)
	return resp, nil
}

func (m *AnchorContractMgr) GetCancelPayAmount(ctx context.Context, in *pb.GetCancelPayAmountReq) (*pb.GetCancelPayAmountResp, error) {
	resp := &pb.GetCancelPayAmountResp{}
	contract, exist, err := m.store.GetValidContractWithUid(in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCancelPayAmount GetValidContractWithUid fail uid:%d, err: %v", in.Uid, err)
		return resp, err
	}
	if !exist {
		log.ErrorWithCtx(ctx, "GetCancelPayAmount fail no contract. uid:%d", in.Uid)
		return resp, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}
	if contract.GuildId != in.GuildId {
		log.ErrorWithCtx(ctx, "GetCancelPayAmount fail no contract. uid:%d, guildId:%d contract:%+v", in.Uid, in.GuildId, contract)
		return resp, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}
	amount := int64(0)
	if in.IsUpgrade {
		inviteRsp, err := m.GetUserPromoteInviteInfo(ctx, &pb.GetUserPromoteInviteInfoReq{
			Uid:     in.Uid,
			GuildId: in.GuildId,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetCancelPayAmount GetUserPromoteInviteInfo fail uid:%d, guildId:%d,  err: %v", in.Uid, in.GuildId, err)
			return resp, err
		}
		if inviteRsp.Info != nil {
			amount = int64(inviteRsp.Info.CancelAmount) * 100
		}
	} else {
		amount, err = m.getCancelPayAmount(ctx, contract)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetCancelPayAmount getCancelPayAmount fail uid:%d, err: %v", in.Uid, err)
			return resp, err
		}
	}
	resp.Amount = amount

	expireSec := m.newDyConf.GetContractDyConf().PayCancelContractParam.LockAmountExpireS
	ts, lockAmount, err := m.cache.GetLockPayCancelAmount(ctx, in.GuildId,
		in.Uid,
		expireSec,
	)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew GetLockPayCancelAmount fail uid:%d, guild:%d, err:%v", in.Uid, in.GuildId, err)
		return resp, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "获取解约申请锁定金额失败,请稍后再试")
	}
	resp.LockAmount = lockAmount
	resp.LockExpireTs = ts + expireSec
	resp.LockStartTs = ts

	log.InfoWithCtx(ctx, "GetCancelPayAmount end uid:%d, resp: %v", in.Uid, resp)
	return resp, nil
}

func (m *AnchorContractMgr) CheckPayCancelSignLimit(ctx context.Context, uid, guildId uint32, identityNum string) error {
	//1.该账号付费解约成功后，冻结签约功能3个月，不能签约任何公会（包括本公会）
	//2.账号下的身份证其他的账号，还可以签约其他公会（包含本公会）
	//3.冻结3个月签约到期后，可以签约任何公会
	payCancelApply, err := m.store.GetLastPayCancelContractApplyLog(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckPayCancelSignLimit GetLastPayCancelContractApplyLog fail uid:%d, err: %v", uid, err)
		return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "获取最近一次付费解约申请失败")
	}
	if m.newDyConf.GetContractDyConf().PayCancelContractParam.FreezeSignTimeSec == 0 {
		log.InfoWithCtx(ctx, "CheckPayCancelSignLimit no check uid:%d", uid)
		return nil
	}
	now := time.Now()
	if payCancelApply != nil && int64(now.Sub(payCancelApply.ApplyTime).Seconds()) <= m.newDyConf.GetContractDyConf().PayCancelContractParam.FreezeSignTimeSec {
		log.ErrorWithCtx(ctx, "CheckPayCancelSignLimit fail uid:%d", uid)
		return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "您已成功付费解约，短时间内无法再次申请签约")
	}

	log.InfoWithCtx(ctx, "CheckPayCancelSignLimit ok end uid:%d, guildId:%d", uid, guildId)
	return nil
}

func (m *AnchorContractMgr) CheckPayCancelCancelSignLimit(ctx context.Context, uid, guildId uint32) string {
	//1.该账号付费解约成功后，冻结签约功能3个月，不能签约任何公会（包括本公会）
	//2.账号下的身份证其他的账号，还可以签约其他公会（包含本公会）
	//3.冻结3个月签约到期后，可以签约任何公会
	payCancelApply, err := m.store.GetLastPayCancelContractApplyRejectLog(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckPayCancelSignLimit GetLastPayCancelContractApplyLog fail uid:%d, err: %v", uid, err)
		return ""
	}
	if m.newDyConf.GetContractDyConf().PayCancelContractParam.FreezeCancelSignTimeSec == 0 {
		log.InfoWithCtx(ctx, "CheckPayCancelSignLimit no check uid:%d", uid)
		return ""
	}

	now := time.Now()
	if payCancelApply != nil && int64(now.Sub(payCancelApply.UpdateTime).Seconds()) <= m.newDyConf.GetContractDyConf().PayCancelContractParam.FreezeCancelSignTimeSec &&
		payCancelApply.Status == uint32(pb.CancelContractStatus_CancelContractStatus_PayOfficeReject) {
		log.ErrorWithCtx(ctx, "CheckPayCancelSignLimit fail uid:%d , apply:%+v , sub: %d", uid, payCancelApply, int64(now.Sub(payCancelApply.UpdateTime).Seconds()))
		return fmt.Sprintf("你最近提交的付费解约申请失败，%d天内无法再次提交付费解约", int((m.newDyConf.GetContractDyConf().PayCancelContractParam.FreezeCancelSignTimeSec-int64(now.Sub(payCancelApply.UpdateTime).Seconds()))/86400+1))
	}

	log.InfoWithCtx(ctx, "CheckPayCancelSignLimit ok end uid:%d, guildId:%d, payCancelApply:%+v", uid, guildId, payCancelApply)
	return ""
}

func (m *AnchorContractMgr) LockCancelPayAmount(ctx context.Context, req *pb.LockCancelPayAmountReq) (*pb.LockCancelPayAmountResp, error) {
	resp := &pb.LockCancelPayAmountResp{}
	log.InfoWithCtx(ctx, "LockCancelPayAmount end req: %v", req)

	payNum, err := m.cache.GetGuildPayNumMonth(ctx, m.dyConfig.GetParentGuildId(req.GetGuildId()))
	if err != nil {
		log.ErrorWithCtx(ctx, "canCancelCheckTypes GetGuildPayNumMonth fail %v, req:%v", err, req)
		return resp, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "获取付费解约数量失败,请稍后再试")
	}
	if payNum >= m.newDyConf.GetContractDyConf().PayCancelContractParam.GuildMaxNum {
		log.ErrorWithCtx(ctx, "canCancelCheckTypes guildMaxNum uid=%d, req:%v", req.GetUid(), req)
		return resp, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "公会本月付费解约数量已达上限")
	}

	contract, exist, err := m.store.GetValidContractWithUid(req.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "LockCancelPayAmount GetValidContractWithUid fail uid:%d, err: %v", req.Uid, err)
		return resp, err
	}
	if !exist {
		log.ErrorWithCtx(ctx, "LockCancelPayAmount fail no contract. uid:%d", req.Uid)
		return resp, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}
	if contract.GuildId != req.GuildId {
		log.ErrorWithCtx(ctx, "LockCancelPayAmount fail no contract. uid:%d, guildId:%d contract:%+v", req.Uid, req.GuildId, contract)
		return resp, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}
	amount, err := m.getCancelPayAmount(ctx, contract)
	if err != nil {
		log.ErrorWithCtx(ctx, "LockCancelPayAmount getCancelPayAmount fail uid:%d, err: %v", req.Uid, err)
		return resp, err
	}

	ts, err := m.cache.LockPayCancelAmount(ctx, req.GuildId, req.Uid, amount)
	if err != nil {
		log.ErrorWithCtx(ctx, "LockCancelPayAmount fail to LockPayCancelAmount uid:%d, guildId:%d, err: %v", req.Uid, req.GuildId, err)
		return resp, protocol.NewExactServerError(nil, status.ErrContractHandleInvalid, "锁定解约金额失败, 请稍后再试")
	}
	resp.LockAmount = amount
	resp.LockExpireTs = ts + m.newDyConf.GetContractDyConf().PayCancelContractParam.LockAmountExpireS
	resp.LockStartTs = ts

	err = m.cache.IncrGuildPayNumMonth(ctx, m.dyConfig.GetParentGuildId(req.GetGuildId()))
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractNew fail to IncrGuildPayNumMonth in:%+v, err:%v", req, err)
		return resp, err
	}

	_ = m.RecordTmpCancelContractApplyLog(ctx, contract, req.Uid, req.GuildId)

	log.InfoWithCtx(ctx, "LockCancelPayAmount end uid:%d, guildId:%d,  resp: %v", req.Uid, req.GuildId, resp)
	return resp, nil
}

func (m *AnchorContractMgr) RecordTmpCancelContractApplyLog(ctx context.Context, contract *mysql.ContractInfo, uid, guildId uint32) error {
	// 冻结成功了，写一条临时的申请
	cancelApplyLog := &mysql.CancelContractApplyLog{
		Uid:                 uid,
		GuildId:             guildId,
		SignTime:            contract.SignTime,
		ContractExpireTime:  contract.ContractExpireTime,
		Status:              uint32(pb.CancelContractStatus_CancelContractStatus_PayFreeze),
		Reason:              uint32(pb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_PAY),
		ApplyTime:           time.Now(),
		LiveObtainTime:      time.Now(),
		MultiplayObtainTime: time.Now(),
		UpdateTime:          time.Now(),
		CancelType:          uint32(pb.CancelContractType_CancelContractType_Pay),
		WorkerType:          contract.WorkerType,
		IdentityNum:         contract.IdentityNum,
	}

	identityList, err := m.store.GetAnchorIdentity(nil, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContract fail to GetAnchorIdentity uid:%+v, err:%v", uid, err)
		return err
	}

	identityInfo := &mysql.IdentityInfo{}
	for _, info := range identityList {
		if info.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
			cancelApplyLog.LiveIdentityStatus = 1
			cancelApplyLog.LiveObtainTime = info.ObtainTime
			cancelApplyLog.AgentUid = info.AgentUid
		} else if info.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
			cancelApplyLog.MultiplayIdentityStatus = 1
			cancelApplyLog.MultiplayObtainTime = info.ObtainTime
		} else {
			log.Errorf("ApplyCancelContract invalid type :%v info %+v", info.IdentityType, info)
		}

		identityInfo.AnchorIdentityInfoList = append(identityInfo.AnchorIdentityInfoList, &mysql.AnchorIdentityInfo{
			IdentityType: info.IdentityType,
			ObtainTime:   uint32(info.ObtainTime.Unix()),
			AgentUid:     info.AgentUid,
		})
	}
	identityInfoStr, _ := json.Marshal(identityInfo)
	cancelApplyLog.IdentityInfo = string(identityInfoStr)

	if cancelApplyLog.LiveIdentityStatus == 1 {
		liveAnchorInfo, err := m.liveMgrCli.GetAnchorByUidList(ctx, &channellivemgr.GetAnchorByUidListReq{UidList: []uint32{uid}})
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContract fail to GetAnchorByUidList uid:%+v, err:%v", uid, err)
			return err
		}

		if len(liveAnchorInfo.AnchorList) > 0 {
			cancelApplyLog.TagId = liveAnchorInfo.AnchorList[0].TagId
		}
	}

	err = m.cache.RecordTmpContractLog(ctx, cancelApplyLog)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContract fail to RecordTmpContractLog uid:%+v, err:%v", uid, err)
		return err
	}

	return nil
}

func (m *AnchorContractMgr) CensorVideo(ctx context.Context, in *pb.CensorVideoReq) (*pb.CensorVideoResp, error) {
	out := &pb.CensorVideoResp{}
	//上游传递的ctx，过期时间可能不足1分钟，这里重新生成一个ctx，方便审核耗时
	ctx, cancel := grpc.NewContextWithInfoTimeout(ctx, time.Minute)
	defer cancel()

	tmpUrlMap, err := m.GetObsTempUrl(ctx, in.GetVideoKey(), 86400)
	if err != nil {
		log.ErrorWithCtx(ctx, "CensorVideo GetObsTempUrl error uid:%d, guild:%d, videoKey:%s, err: %v", in.GetUid(), in.GetGuildId(), in.GetVideoKey(), err)
		return &pb.CensorVideoResp{}, err
	}

	log.InfoWithCtx(ctx, "CensorVideo begin uid:%d, guild:%d, videoKey:%s", in.GetUid(), in.GetGuildId(), in.GetVideoKey())
	tmpUrlList := make([]string, 0)
	for _, item := range in.GetVideoKey() {
		log.InfoWithCtx(ctx, "Show CensorVideo tmpUrlMap item:%s, url:%s", item, tmpUrlMap[item])
		tmpUrlList = append(tmpUrlList, tmpUrlMap[item])
	}

	// 将视频送去审核
	wg := sync.WaitGroup{}
	wg.Add(2)

	nowTs := time.Now()
	transcodeMap := make(map[string]string)
	// 每秒钟查一次redis，看看是否转码成功了 ; 30s超时
	go func() {
		timeOut := time.Second * time.Duration(m.newDyConf.GetContractDyConf().TranscodeTimeOut)
		tmpCtx, cancel1 := context.WithTimeout(ctx, timeOut)
		defer cancel1()
		for {

			resp, err := m.cache.BatchGetTranscodeResult(tmpCtx, in.GetVideoKey())
			if err != nil {
				log.ErrorWithCtx(ctx, "CensorVideo BatchGetTranscodeResult fail uid:%d, guild:%d, videoKey:%s, err: %v", in.GetUid(), in.GetGuildId(), in.GetVideoKey(), err)
			} else {
				log.InfoWithCtx(ctx, "CensorVideo BatchGetTranscodeResult uid:%d, guild:%d, videoKey:%s, resp: %v", in.GetUid(), in.GetGuildId(), in.GetVideoKey(), resp)
				for key, trans := range resp {
					transcodeMap[key] = trans
				}

				if len(resp) == len(in.GetVideoKey()) {
					wg.Done()
					return
				}
			}
			if time.Now().Sub(nowTs) > timeOut {
				log.ErrorWithCtx(ctx, "CensorVideo BatchGetTranscodeResult timeout uid:%d, guild:%d, videoKey:%s", in.GetUid(), in.GetGuildId(), in.GetVideoKey())
				wg.Done()
				return
			}

			time.Sleep(time.Second)
		}
	}()

	if !m.newDyConf.GetContractDyConf().CensoringIgnoreResult {
		go func() {
			err = m.SyncCensoringMix(ctx, in.GetUid(), in.GetGuildId(), "", []string{}, tmpUrlList, nil, "", "")
			if err != nil && protocol.ToServerError(err).Code() == status.ErrContractApplySignLimitOther {
				log.ErrorWithCtx(ctx, "CensorVideo SyncCensoringMix fail uid:%d, guild:%d, videoKey:%s, err: %v", in.GetUid(), in.GetGuildId(), in.GetVideoKey(), err)
				err = protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "视频审核违规")
			}
			if err != nil {
				log.ErrorWithCtx(ctx, "CensorVideo fail uid:%d, guild:%d, videoKey:%s, err: %v", in.GetUid(), in.GetGuildId(), in.GetVideoKey(), err)
			}

			wg.Done()
		}()
	} else {
		wg.Done()
	}

	wg.Wait()

	if err != nil {
		return &pb.CensorVideoResp{}, err
	}

	// 如果通过了审核
	transKeyList := make([]string, 0)
	for _, item := range in.GetVideoKey() {
		if transcodeMap[item] != "" {
			transKeyList = append(transKeyList, transcodeMap[item])
		} else {
			transKeyList = append(transKeyList, item)
		}
	}

	urlMap, _ := m.GetObsTempUrl(ctx, transKeyList, 86400)

	out.CensorResult = make([]*pb.CensorResult, 0)
	// 为每一个视频key生成一个对应的随机key并保存到redis中
	for _, item := range in.GetVideoKey() {
		// 生成随机key
		randomKey := m.GenerateRandomKey()
		// 保存到redis中
		err = m.cache.SetCensorKey(ctx, item, randomKey)
		if err != nil {
			log.ErrorWithCtx(ctx, "CensorVideo SetCensorVideoKey fail uid:%d, guild:%d, videoKey:%s, err: %v", in.GetUid(), in.GetGuildId(), item, err)
			return &pb.CensorVideoResp{}, err
		}

		transUrl := ""
		if transcodeMap[item] != "" {
			transUrl = urlMap[transcodeMap[item]]
		}

		out.CensorResult = append(out.CensorResult, &pb.CensorResult{
			VideoKey:     item,
			CensorKey:    randomKey,
			TranscodeUrl: transUrl,
		})
	}

	log.InfoWithCtx(ctx, "CensorVideo end uid:%d, guild:%d, resp:%v", in.GetUid(), in.GetGuildId(), out.CensorResult)
	return out, err
}

func (m *AnchorContractMgr) GenerateRandomKey() string {
	// 生成随机key
	randomKey := strconv.Itoa(int(time.Now().UnixNano())) + strconv.Itoa(rand.Intn(1000))
	return randomKey
}

func (m *AnchorContractMgr) GetObsTempUrl(ctx context.Context, keys []string, expiration uint32) (map[string]string, error) {
	obsConfig := m.newDyConf.GetContractDyConf().ObsConfig

	getUrlByKey := func(key string) (string, error) {
		var claims []interface{}
		var url string
		claims = append(claims, &obsgateway.DownloadTokenReq{
			App:        obsConfig.AppId,
			Scope:      obsConfig.Scope,
			Key:        key,
			Expiration: int32(expiration),
		})
		tokenResp, err := m.ObsCli.ClaimToken(ctx, claims)
		if err != nil {
			log.Errorf("GetObsTempUrl ClaimToken err:%s, key:%s", err, key)
			return "", err
		}
		url = fmt.Sprintf("%s%s/%s/%s?token=%s", obsConfig.Host, obsConfig.AppId, obsConfig.Scope, key, tokenResp[0])
		log.InfoWithCtx(ctx, "GetObsTempUrl key:%s, url:%s", key, url)
		return url, nil
	}

	urlMap := make(map[string]string)
	for _, f := range keys {
		if strings.HasPrefix(f, "http") {
			urlMap[f] = f
			log.DebugWithCtx(ctx, "GetObsTempUrl key:%s, url:%s", f, f)
			continue
		}

		url, err := getUrlByKey(f)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetObsTempUrl getUrlByKey fail err:%v", err)
			continue
		}
		urlMap[f] = url
		log.InfoWithCtx(ctx, "GetObsTempUrl key:%s, url:%s", f, url)
	}

	return urlMap, nil
}

func (m *AnchorContractMgr) ClaimUploadToken(ctx context.Context, expiration uint32) (token string, expireAt uint32, err error) {

	obsConfig := m.newDyConf.GetContractDyConf().ObsConfig

	key := m.GenerateRandomKey()

	if expiration == 0 {
		expiration = 86400
	}

	mp2 := &mp.MediaProcess{
		Action: mp.ACTION_TRANSCODE,
		Transcode: &mp.Transcode{
			Container: &mp.Container{Format: mp.CONTAINER_FORMAT_MP4},
			Video:     &mp.Video{Codec: mp.VIDEO_CODEC_H264},
			Output: mp.Output{
				Key: key + "_transcode",
			},
		},
	}

	mpEn, err := mp2.Encode()
	if err != nil {
		log.Errorf("GetObsTempUrl mp2.Encode err:%s", err)
		return "", 0, err
	}

	claims := &obsgateway.UploadTokenReq{
		App:          obsConfig.AppId,
		Scope:        obsConfig.Scope,
		Expiration:   int32(expiration),
		MediaProcess: string(mpEn),
		CustomId:     key,
	}
	token, err = m.ObsCli.ClaimUploadToken(ctx, claims)
	if err != nil {
		log.Errorf("GetObsTempUrl ClaimToken err:%s, token:%s", err, token)
		return token, 0, err
	}

	expireAt = uint32(time.Now().Add(time.Duration(expiration) * time.Second).Unix())

	log.InfoWithCtx(ctx, "ClaimUploadToken key:%s, token %s, expireAt %d", key, token, expireAt)

	return token, expireAt, nil
}

func (m *AnchorContractMgr) tranProofKeyListToProofUrlList(ctx context.Context, imageProofList, videoProofList string) ([]*pb.ProofShowContent, error) {
	proofUrlList := make([]*pb.ProofShowContent, 0)

	imageUrlList := make([]string, 0)
	for _, item := range strings.Split(imageProofList, ",") {
		if item != "" {
			imageUrlList = append(imageUrlList, item)
		}
	}

	videoUrlList := make([]string, 0)
	for _, item := range strings.Split(videoProofList, ",") {
		if item != "" {
			videoUrlList = append(videoUrlList, item)
		}
	}

	urlMap, err := m.GetObsTempUrl(ctx, append(imageUrlList, videoUrlList...), 86400)
	if err != nil {
		log.ErrorWithCtx(ctx, "tranProofKeyListToProofUrlList GetObsTempUrl fail err: %v", err)
		return proofUrlList, err
	}

	for _, item := range imageUrlList {
		url := urlMap[item]
		if strings.HasPrefix(item, "http") {
			url = item
		}

		proofUrlList = append(proofUrlList, &pb.ProofShowContent{
			Type: pb.ProofShowContent_ProofType_Image,
			Url:  url,
		})
	}

	for _, item := range videoUrlList {
		url := urlMap[item]
		if strings.HasPrefix(item, "http") {
			url = item
		}

		proofUrlList = append(proofUrlList, &pb.ProofShowContent{
			Type: pb.ProofShowContent_ProofType_Video,
			Url:  url,
		})
	}

	return proofUrlList, nil
}

// CheckAnchorApply 付费解约的时候，要判断是不是签约申请，避免解约途中发生变化
func (m *AnchorContractMgr) CheckAnchorApply(ctx context.Context, uid, guildId uint32) error {
	statusList := []uint32{
		uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_HANDLING),
		uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_HANDLING),
	}

	// 检查该用户官方正在审批中的申请记录
	OfficeHandleApplyList, err := m.store.GetContractApplyWithUid(uid, []uint32{uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_HANDLING)})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail to GetContractApplyWithUid uid:%+v, err:%v", uid, err)
		return err
	}

	if len(OfficeHandleApplyList) > 0 {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail. Apply limit uid:%+v", uid)
		return protocol.NewExactServerError(nil, status.ErrContractApplyHasHandling, "已有审批中的签约申请")
	}

	// 检查该用户正在审批中的申请记录
	applyList2, err := m.store.GetContractApplyWithUid(uid, statusList)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail to GetContractApplyWithUid uid:%+v, err:%v", uid, err)
		return err
	}

	for _, apply := range applyList2 {
		if uid == apply.Uid && guildId == apply.GuildId {
			log.ErrorWithCtx(ctx, "ApplySignPreCheck fail. Already apply uid:%+v", uid)
			return protocol.NewExactServerError(nil, status.ErrContractApplyAlready, "已有审批中的签约申请")
		}
	}

	return nil
}

func (m *AnchorContractMgr) HandleMediaProcess(ctx context.Context, result *mp.MediaProcessResult) error {
	log.InfoWithCtx(ctx, "HandleMediaProcess result:%+v", result)

	recordResult := false
	if result.Input.App == m.newDyConf.GetContractDyConf().ObsConfig.AppId && result.Input.Scope == m.newDyConf.GetContractDyConf().ObsConfig.Scope {
		// 找到对应的applyId
		applyId, err := m.cache.GetApplyFromKey(ctx, result.Input.Key)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleMediaProcess GetApplyFromKey error result %+v err %v", result, err)
			recordResult = true
		} else {
			// 获取log
			apply, err := m.store.GetCancelContractApplyLogById(applyId)
			if err != nil {
				log.ErrorWithCtx(ctx, "HandleMediaProcess GetCancelContractApplyLogById error result %+v err %v", result, err)
				recordResult = true
			} else {
				// 更新
				if len(result.Items) > 0 && apply != nil {
					apply.VideoProofList = strings.ReplaceAll(apply.VideoProofList, result.Input.Key, result.Items[0].Output.Key)
					err = m.store.UpdateCancelContractApplyLogInfoById(nil, apply)
					if err != nil {
						log.ErrorWithCtx(ctx, "HandleMediaProcess UpdateCancelContractApplyLogInfoById error result %+v err %v", result, err)
					}
					log.InfoWithCtx(ctx, "HandleMediaProcess UpdateCancelContractApplyLogInfoById success result %+v", result)
				} else {
					log.InfoWithCtx(ctx, "HandleMediaProcess UpdateCancelContractApplyLogInfoById no apply result %+v", result)
					recordResult = true
				}
			}
		}

		// 正在进行的申请也要更新

		uid, guildId, err := m.cache.GetUidGuildIdFromKey(ctx, result.Input.Key)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleMediaProcess GetUidGuildIdFromKey error result %+v err %v", result, err)
			recordResult = true
		} else {
			exist, apply, err := m.store.GetCancelContractApply(uid, guildId)
			if err != nil {
				log.ErrorWithCtx(ctx, "HandleMediaProcess GetApplyFromKey error result %+v err %v", result, err)
				recordResult = true
			} else {
				if exist {
					// 更新
					if len(result.Items) > 0 {
						apply.VideoProofList = strings.ReplaceAll(apply.VideoProofList, result.Input.Key, result.Items[0].Output.Key)
					}

					err = m.store.UpdateCancelContractApplyVideoKey(nil, uid, guildId, apply.VideoProofList)
					if err != nil {
						log.ErrorWithCtx(ctx, "HandleMediaProcess UpdateCancelContractApplyVideoKey error result %+v err %v", result, err)
					}
					log.InfoWithCtx(ctx, "HandleMediaProcess UpdateCancelContractApplyLogInfoById success result %+v", result)
				} else {
					log.ErrorWithCtx(ctx, "HandleMediaProcess GetApplyFromKey no reply result %+v err %v", result, err)
					recordResult = true
				}
			}
		}

		// 有报错，可能是转码结果先到了，存个cache
		if recordResult == true && len(result.Items) > 0 {
			err = m.cache.RecordTranscodeResult(ctx, result.Input.Key, result.Items[0].Output.Key)
			if err != nil {
				log.ErrorWithCtx(ctx, "HandleMediaProcess RecordTranscodeResult error result %+v err %v", result, err)
			}
			log.InfoWithCtx(ctx, "HandleMediaProcess UpdateCancelContractApplyLogInfoById success result %+v", result)
		}
	}

	log.InfoWithCtx(ctx, "HandleMediaProcess end result:%+v , cfg %+v", result, m.newDyConf.GetContractDyConf().ObsConfig)

	return nil
}
