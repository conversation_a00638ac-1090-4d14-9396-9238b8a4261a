package mysql

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-sql-driver/mysql"
	_ "github.com/go-sql-driver/mysql"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/sign-anchor-stats"
	"golang.52tt.com/services/anchor-contract/sign-anchor-stats/utils"
)

const (
	TblHallGuildChannel           = "tbl_hall_guild_channel" // 承接大厅公会房配置
	TblHallGuildLimit             = "tbl_hall_guild_limit"
	TblHallTaskConf               = "tbl_hall_task_conf"      // 任务信息配置
	TblHallTaskConfWeek           = "tbl_hall_task_conf_week" // 任务配置版本记录表，按周
	TblHallTask                   = "tbl_hall_task"
	TblHallTaskChangeLog          = "tbl_hall_task_change_log" // 任务分配历史
	TblHallCostTicketMonthly      = "tbl_hall_cost_ticket_monthly_"
	TblHallTaskAnchorDaily        = "tbl_hall_task_anchor_daily_"         // 签约成员日任务明细表
	TblHallTaskAnchorWeek         = "tbl_hall_task_anchor_week_"          // 签约成员周任务明细表
	TblHallTaskAnchorChannelDaily = "tbl_hall_task_anchor_channel_daily_" // 签约成员日任务明细表-房间维度
	TblHallTaskAnchorChannelWeek  = "tbl_hall_task_anchor_channel_week_"  // 签约成员周任务明细表-房间维度
	TblHallChannelDailyStats      = "tbl_hall_channel_daily_stats"        // 承接大厅房间数据统计日表
	TblHallChannelWeekStats       = "tbl_hall_channel_week_stats"         // 承接大厅房间数据统计周表
	TblHallPresentHistory         = "tbl_hall_present_history_monthly_"
	TblHallTaskValidOpen          = "tbl_hall_task_valid_open_"
)

type HallTaskValidOpen struct {
	Id         uint32
	Uid        uint32
	TargetUid  uint32
	ChannelId  uint32
	StartTime  time.Time
	EndTime    time.Time
	ValidSec   uint32
	CreateTime time.Time
}

type Result struct {
	Cnt uint32
}

// 厅数据统计
type HallChannelStats struct {
	Id              uint32
	GuildId         uint32
	ChannelId       uint32
	Date            time.Time
	TimeKey         string
	EnterChannelCnt uint32
	HasTicketCnt    uint32
	UsedTicketCnt   uint32
	ChannelFee      uint64
	UpdateTime      time.Time
}

// 任务
type HallTask struct {
	TaskId      uint32
	SignGuildId uint32
	GuildId     uint32
	Uid         uint32
	OpUid       uint32
	UpdateTime  time.Time
}

// 任务分配变更
type HallTaskChangeLog struct {
	TaskId      uint32
	SignGuildId uint32
	GuildId     uint32
	Uid         uint32
	OpUid       uint32
	ChangeType  uint32
	CreateTime  time.Time
}

/*
任务配置如果已分配用户，修改要下周一0点生效
*/
// 任务配置
type HallTaskConf struct {
	TaskId        uint32
	GuildId       uint32
	TaskGroupName string
	TaskConfOpt   string
	RewardIntro   string
	IsDel         uint8
	UpdateTime    time.Time
	//NextWeekEffectConf string // HallTaskConfNextWeek
	// next_week_effect_conf
}

// tbl_hall_task_conf_week
type HallTaskConfWeek struct {
	TaskId        uint32
	WeekDate      time.Time
	GuildId       uint32
	TaskGroupName string
	TaskConfOpt   string
	RewardIntro   string
	UpdateTime    time.Time
}

// 承接大厅房间配置
type HallGuildChannel struct {
	GuildId    uint32
	ChannelId  uint32
	IsDel      uint32
	UpdateTime time.Time
}

// 是否可配置非本公会成员
type HallGuildLimit struct {
	GuildId            uint32
	IsLimitNoselfGuild uint32
	UpdateTime         time.Time
}

// 成员日任务
type HallTaskAnchorDaily struct {
	Id           uint32
	Uid          uint32
	TaskId       uint32
	Date         time.Time
	ValidSec     uint32
	ValidOpenCnt uint32
	TicketCnt    uint32
	Income       uint32
	UpdateTime   time.Time
}

// 成员周任务
type HallTaskAnchorWeek struct {
	Uid              uint32
	TaskId           uint32
	Date             time.Time
	ValidHoldDay     uint32
	FinishDayTaskCnt uint32
	TicketCnt        uint32
	ValidOpenCnt     uint32
	Income           uint32
	UpdateTime       time.Time
}

// 成员日任务-房间维度
type HallTaskAnchorChannelDaily struct {
	Id           uint32
	Uid          uint32
	ChannelId    uint32
	Date         time.Time
	TaskId       uint32
	ValidSec     uint32
	ValidOpenCnt uint32
	TicketCnt    uint32
	Income       uint32
	UpdateTime   time.Time
}

// 成员周任务-房间维度
type HallTaskAnchorChannelWeek struct {
	Uid              uint32
	ChannelId        uint32
	Date             time.Time
	TaskId           uint32
	ValidHoldDay     uint32
	FinishDayTaskCnt uint32
	TicketCnt        uint32
	ValidOpenCnt     uint32
	Income           uint32
	UpdateTime       time.Time
}

type HallPresentHistory struct {
	OrderId     string
	FromUid     uint32
	ToUid       uint32
	GuildId     uint32
	ChannelId   uint32
	ItemId      uint32
	ItemCount   uint32
	TotalPrice  uint32
	PriceType   uint32
	TagType     uint32
	OutsideTime time.Time
	UpdateTime  time.Time
}

func (c *HallTaskConfWeek) TableName() string {
	return TblHallTaskConfWeek
}

func (s *HallTaskStore) AddHallTaskConfWeek(tx *gorm.DB, info *HallTaskConfWeek) error {
	db := s.getdb(tx)
	tblName := info.TableName()
	execSql := fmt.Sprintf("INSERT INTO %s(task_id,week_date,guild_id,task_group_name,task_conf_opt,reward_intro)VALUES(?,?,?,?,?,?)"+
		" ON DUPLICATE KEY UPDATE task_group_name=?,task_conf_opt=?,reward_intro=?", tblName)
	err := db.Exec(execSql, info.TaskId, info.WeekDate.Format("2006-01-02"), info.GuildId,
		info.TaskGroupName, info.TaskConfOpt, info.RewardIntro,
		info.TaskGroupName, info.TaskConfOpt, info.RewardIntro).Error
	if err != nil {
		log.Errorf("AddHallTaskConfWeek fail %v info=%+v", err, info)
		return err
	}
	return nil
}

func (s *HallTaskStore) DelHallTaskConfWeek(tx *gorm.DB, info *HallTaskConfWeek) error {
	db := s.getdb(tx)
	tblName := info.TableName()
	execSql := fmt.Sprintf("DELETE FROM %s WHERE task_id=? and week_date=?", tblName)
	return db.Exec(execSql, info.TaskId, info.WeekDate.Format("2006-01-02")).Error
}

// weekDate 周一时间
func (s *HallTaskStore) GetHallTaskConfWeek(taskId uint32, weekDate time.Time) (*HallTaskConfWeek, error) {
	info := HallTaskConfWeek{}
	db := s.db.Table(info.TableName()).Where("task_id=? and week_date=?", taskId, weekDate)
	err := db.Scan(&info).Error
	if err != nil && !checkErrIsRecordNotFound(err) {
		return &info, err
	}
	return &info, nil
}

func (s *HallTaskStore) GetHallTaskConfWeekByDate(weekDate time.Time) ([]*HallTaskConfWeek, error) {
	list := []*HallTaskConfWeek{}
	db := s.db.Table(TblHallTaskConfWeek).Where("week_date=?", weekDate)
	err := db.Find(&list).Error
	if err != nil {
		return list, err
	}
	return list, nil
}

func (s *HallTaskStore) GetHallTaskConfWeekByName(tx *gorm.DB, guildId uint32, weekDate time.Time, taskName string) ([]*HallTaskConfWeek, error) {
	db := s.getdb(tx)
	list := []*HallTaskConfWeek{}
	db = db.Table(TblHallTaskConfWeek).Where("guild_id=? and week_date=? and task_group_name=?", guildId, weekDate, taskName)
	err := db.Find(&list).Error
	if err != nil {
		return list, err
	}
	return list, nil
}

//tbl_hall_task_conf_update
func (s *HallTaskStore) InitHallTaskConfChange(guildId uint32) error {
	execSql := fmt.Sprintf("INSERT INTO tbl_hall_task_conf_update(guild_id)VALUES(?)" +
		" ON DUPLICATE KEY UPDATE update_time=update_time")
	err := s.db.Exec(execSql, guildId).Error
	return err
}

func (s *HallTaskStore) UpdateHallTaskConfChange(tx *gorm.DB, guildId uint32) error {
	now := time.Now()
	db := s.getdb(tx)
	execSql := fmt.Sprintf("UPDATE tbl_hall_task_conf_update SET update_time=?" +
		" WHERE guild_id=?")
	err := db.Exec(execSql, now.Unix(), guildId).Error
	return err
}

func (c *HallPresentHistory) TableName() string {
	return fmt.Sprintf("%s%s", TblHallPresentHistory, c.OutsideTime.Format("200601"))
}

// 记录房间维度日任务 insert..on dup
func (s *HallTaskStore) RecordHallTaskAnchorChannelDaily(tx *gorm.DB, info *HallTaskAnchorChannelDaily) error {
	db := s.getdb(tx)
	tblName := info.TableName()
	execSql := fmt.Sprintf("INSERT INTO %s(channel_id,date,uid,task_id,valid_sec,valid_open_cnt,income,ticket_cnt)VALUES(?,?,?,?,?,?,?,?)"+
		" ON DUPLICATE KEY UPDATE valid_sec=valid_sec+?, valid_open_cnt=valid_open_cnt+?, income=income+?,ticket_cnt=ticket_cnt+?", tblName)
	err := db.Exec(execSql, info.ChannelId, info.Date.Format("2006-01-02"), info.Uid, info.TaskId, info.ValidSec, info.ValidOpenCnt, info.Income, info.TicketCnt,
		info.ValidSec, info.ValidOpenCnt, info.Income, info.TicketCnt).Error
	if checkErrIsTableNotExist(err) {
		err = db.Exec(fmt.Sprintf(CreateTblHallTaskAnchorChannelDailySQL, info.Date.Format("2006"))).Error
		if err != nil {
			return err
		}
		err = db.Exec(execSql, info.ChannelId, info.Date.Format("2006-01-02"), info.Uid, info.TaskId, info.ValidSec, info.ValidOpenCnt, info.Income, info.TicketCnt,
			info.ValidSec, info.ValidOpenCnt, info.Income, info.TicketCnt).Error
	}
	return err
}

// 记录房间维度周任务 insert..on dup
func (s *HallTaskStore) RecordHallTaskAnchorChannelWeek(tx *gorm.DB, info *HallTaskAnchorChannelWeek) error {
	db := s.getdb(tx)
	tblName := info.TableName()
	execSql := fmt.Sprintf("INSERT INTO %s(channel_id,uid,date,task_id,valid_hold_day,finish_day_task_cnt,valid_open_cnt,income,ticket_cnt)VALUES(?,?,?,?,?,?,?,?,?)"+
		" ON DUPLICATE KEY UPDATE valid_hold_day=valid_hold_day+?, finish_day_task_cnt=finish_day_task_cnt+?, valid_open_cnt=valid_open_cnt+?, income=income+?, ticket_cnt=ticket_cnt+?", tblName)
	err := db.Exec(execSql, info.ChannelId, info.Uid, info.Date.Format("2006-01-02"), info.TaskId, info.ValidHoldDay, info.FinishDayTaskCnt, info.ValidOpenCnt, info.Income, info.TicketCnt,
		info.ValidHoldDay, info.FinishDayTaskCnt, info.ValidOpenCnt, info.Income, info.TicketCnt).Error
	if checkErrIsTableNotExist(err) {
		err = db.Exec(fmt.Sprintf(CreateTblHallTaskAnchorChannelWeekSQL, info.Date.Format("2006"))).Error
		if err != nil {
			return err
		}
		err = db.Exec(execSql, info.ChannelId, info.Uid, info.Date.Format("2006-01-02"), info.TaskId, info.ValidHoldDay, info.FinishDayTaskCnt, info.ValidOpenCnt, info.Income, info.TicketCnt,
			info.ValidHoldDay, info.FinishDayTaskCnt, info.ValidOpenCnt, info.Income, info.TicketCnt).Error
	}
	return err
}

// 根据id查询厅数据
func (s *HallTaskStore) GetHallChannelWeekStatsById(id uint32) (*HallChannelStats, error) {
	info := HallChannelStats{}
	db := s.db.Table(TblHallChannelWeekStats).Where("id=?", id)
	err := db.Scan(&info).Error
	if err != nil && !checkErrIsRecordNotFound(err) && !checkErrIsTableNotExist(err) {
		return nil, err
	}
	return &info, nil
}

func (s *HallTaskStore) GetHallTaskAnchorChannelWeekTotal(channelId uint32, tm time.Time) (uint32, error) {
	info := &HallTaskAnchorChannelWeek{Date: tm}
	db := s.db.Table(info.TableName()).Select("count(distinct(uid)) as cnt").Where("channel_id=? and date=?", channelId, tm.Format("2006-01-02 15:04:05"))

	var res Result
	err := db.Scan(&res).Error
	if err != nil {
		if checkErrIsRecordNotFound(err) {
			return 0, nil
		}
		return 0, err
	}
	return res.Cnt, nil
}

// 查询房间维度周任务详情 group by uid
func (s *HallTaskStore) GetHallTaskAnchorChannelWeekList(channelId, uid uint32, tm time.Time) ([]*HallTaskAnchorChannelWeek, error) {
	info := &HallTaskAnchorChannelWeek{Date: tm}
	list := []*HallTaskAnchorChannelWeek{}
	db := s.db.Table(info.TableName()).Select("uid,sum(valid_hold_day)as valid_hold_day,sum(finish_day_task_cnt) as finish_day_task_cnt, "+
		"sum(ticket_cnt)as ticket_cnt,sum(valid_open_cnt)as valid_open_cnt,sum(income)as income").
		Where("channel_id=? and date=?", channelId, tm.Format("2006-01-02 15:04:05"))
	if uid > 0 {
		db = db.Where("uid=?", uid)
	}
	db = db.Group("uid")

	err := db.Find(&list).Error
	return list, err
}

func (s *HallTaskStore) GetHallTaskAnchorChannelWeekTaskOpt(channelId, uid uint32, tm time.Time) (
	map[pb.HALL_TASK_WEEK_TYPE]bool, error) {
	list := []*HallTask{}
	info := &HallTaskAnchorChannelWeek{Date: tm}
	taskMap := map[pb.HALL_TASK_WEEK_TYPE]bool{}

	err := s.db.Table(info.TableName()).Select("task_id").Where("channel_id=? and date=? and uid=?",
		channelId, tm.Format("2006-01-02 15:04:05"), uid).Find(&list).Error
	if err != nil {
		return taskMap, err
	}
	taskIds := []uint32{}
	for _, v := range list {
		taskIds = append(taskIds, v.TaskId)
	}

	mp, err := s.GetHallTaskWeekConfMap(taskIds, tm)
	if err != nil {
		return taskMap, err
	}
	for _, conf := range mp {
		detail, _ := UnarshalHallTaskConfDetialInfo(conf.TaskConfOpt)
		for _, d := range detail.GetWeekTaskList() { // fix
			taskMap[pb.HALL_TASK_WEEK_TYPE(d.TaskType)] = true
		}
	}
	return taskMap, nil
}

func (s *HallTaskStore) GetHallTaskAnchorChannelDailyTotal(channelId uint32, tm time.Time) (uint32, error) {
	info := &HallTaskAnchorChannelDaily{Date: tm}
	db := s.db.Table(info.TableName()).Select("count(distinct(uid)) as cnt").Where("channel_id=? and date=?", channelId, tm.Format("2006-01-02 15:04:05"))
	var res Result
	err := db.Scan(&res).Error
	if err != nil {
		if checkErrIsRecordNotFound(err) {
			return 0, nil
		}
		return 0, err
	}
	return res.Cnt, nil
}

func (s *HallTaskStore) GetHallTaskAnchorChannelDailyTaskOpt(channelId, uid uint32, tm time.Time) (
	map[pb.HALL_TASK_DAY_TYPE]bool, error) {
	taskMap := map[pb.HALL_TASK_DAY_TYPE]bool{}

	list := []*HallTask{}
	info := &HallTaskAnchorChannelDaily{Date: tm}
	err := s.db.Table(info.TableName()).Select("task_id").Where("channel_id=? and date=? and uid=?",
		channelId, tm.Format("2006-01-02"), uid).Find(&list).Error
	if err != nil {
		return taskMap, err
	}
	taskIds := []uint32{}
	for _, v := range list {
		taskIds = append(taskIds, v.TaskId)
	}
	log.Debugf("taskIds=%+v", taskIds)

	mp, err := s.GetHallTaskWeekConfMap(taskIds, tm)
	if err != nil {
		return taskMap, err
	}
	for _, conf := range mp {
		log.Debugf("task conf=%+v", conf)
		detail, _ := UnarshalHallTaskConfDetialInfo(conf.TaskConfOpt)
		for _, d := range detail.DayTaskList {
			taskMap[pb.HALL_TASK_DAY_TYPE(d.TaskType)] = true
		}
	}
	return taskMap, nil
}

func (s *HallTaskStore) GetHallTaskAnchorChannelDailyList(channelId, uid uint32, tm time.Time) ([]*HallTaskAnchorChannelDaily, error) {
	info := &HallTaskAnchorChannelDaily{Uid: uid, ChannelId: channelId, Date: tm}
	list := []*HallTaskAnchorChannelDaily{}

	db := s.db.Table(info.TableName()).Select("uid,sum(valid_sec)as valid_sec, sum(valid_open_cnt)as valid_open_cnt,sum(ticket_cnt)as ticket_cnt,sum(income)as income").
		Where("channel_id=? and date=?", channelId, tm.Format("2006-01-02 15:04:05"))
	if uid > 0 {
		db = db.Where("uid=?", uid)
	}
	db = db.Group("uid")
	err := db.Find(&list).Error
	if err != nil {
		return list, err
	}
	return list, err
}

func (s *HallTaskStore) RecordHallPresentHistory(tx *gorm.DB, info *HallPresentHistory) error {
	db := s.db
	if tx != nil {
		db = tx
	}
	execSql := fmt.Sprintf("INSERT INTO %s(order_id,from_uid,to_uid,guild_id,channel_id,item_id,item_count,total_price,price_type,tag_type,outside_time)"+
		"VALUES(?,?,?,?,?,?,?,?,?,?,?)",
		info.TableName())
	err := db.Exec(execSql, info.OrderId, info.FromUid, info.ToUid, info.GuildId,
		info.ChannelId, info.ItemId, info.ItemCount, info.TotalPrice, info.PriceType, info.TagType, info.OutsideTime.Format("2006-01-02 15:04:05")).Error
	if err != nil {
		if checkErrIsTableNotExist(err) {
			err = db.Exec(fmt.Sprintf(CreateTblHallPresentHistorySQL, info.OutsideTime.Format("200601"))).Error
			if err != nil {
				return err
			}
			err = db.Exec(execSql, info.OrderId, info.FromUid, info.ToUid, info.GuildId,
				info.ChannelId, info.ItemId, info.ItemCount, info.TotalPrice, info.PriceType, info.TagType, info.OutsideTime.Format("2006-01-02 15:04:05")).Error
		} else {
			return err
		}
	}
	return err
}

func (s *HallTaskStore) GetOrderCount(beginTs, endTs, priceType, tagType uint32) (count uint32, value uint32, err error) {
	info := struct {
		Cnt   uint32
		Value uint32
	}{}

	beginTm := time.Unix(int64(beginTs), 0)
	endTm := time.Unix(int64(endTs), 0)

	order := HallPresentHistory{OutsideTime: beginTm}
	db := s.db.Table(order.TableName()).Select("count(order_id) as cnt, sum(total_price) as value").
		Where("outside_time >=? and outside_time<?",
			beginTm.Format("2006-01-02 15:04:05"), endTm.Format("2006-01-02 15:04:05"))

	if priceType > 0 {
		db = db.Where("price_type=?", priceType)
	}
	if tagType > 0 {
		db = db.Where("tag_type=?", tagType)
	}

	err = db.Scan(&info).Error
	if err != nil {
		log.Errorf("GetOrderCount fail %v, beginTs %d, endTs %d", err, beginTs, endTs)
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			err = nil
		}
	}

	count = info.Cnt
	value = info.Value
	return
}

func (s *HallTaskStore) GetOrderList(beginTs, endTs, priceType, tagType uint32) (orderList []*HallPresentHistory, err error) {
	beginTm := time.Unix(int64(beginTs), 0)
	endTm := time.Unix(int64(endTs), 0)

	order := HallPresentHistory{OutsideTime: beginTm}
	db := s.db.Table(order.TableName()).Select("order_id, total_price").
		Where("outside_time >=? and outside_time<?",
			beginTm.Format("2006-01-02 15:04:05"), endTm.Format("2006-01-02 15:04:05"))
	if priceType > 0 {
		db = db.Where("price_type=?", priceType)
	}
	if tagType > 0 {
		db = db.Where("tag_type=?", tagType)
	}

	err = db.Find(&orderList).Error
	if err != nil {
		log.Errorf("GetOrderList fail %v, beginTs %d, endTs %d", err, beginTs, endTs)
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			err = nil
		}
	}
	return
}

func (c *HallTaskAnchorWeek) TableName() string {
	return fmt.Sprintf("%s%s", TblHallTaskAnchorWeek, c.Date.Format("2006"))
}

func (s *HallTaskStore) GetHallTaskAnchorWeek(tx *gorm.DB, uid, taskId uint32, mondayTm time.Time, isForUpdate bool) (*HallTaskAnchorWeek, error) {
	db := s.getdb(tx)
	info := HallTaskAnchorWeek{}
	db = db.Table((&HallTaskAnchorWeek{Date: mondayTm}).TableName()).Where("uid=? and date=?",
		uid, mondayTm.Format("2006-01-02"))
	if taskId > 0 {
		db = db.Where("task_id=?", taskId)
	}
	if isForUpdate {
		db = db.Set("gorm:query_option", "FOR UPDATE")
	}
	err := db.Scan(&info).Error
	if err != nil && !checkErrIsRecordNotFound(err) && !checkErrIsTableNotExist(err) {
		return nil, err
	}
	return &info, nil
}

func (s *HallTaskStore) GetHallTaskAnchorWeekList(uid uint32, mondayTm time.Time) ([]*HallTaskAnchorWeek, error) {
	db := s.db
	list := []*HallTaskAnchorWeek{}
	db = db.Table((&HallTaskAnchorWeek{Date: mondayTm}).TableName()).Where("uid=? and date=?",
		uid, mondayTm.Format("2006-01-02"))

	err := db.Order("update_time desc").Find(&list).Error
	if err != nil && !checkErrIsTableNotExist(err) {
		return nil, err
	}
	return list, nil
}

func (s *HallTaskStore) InitHallTaskAnchorWeek(tx *gorm.DB, info *HallTaskAnchorWeek) error {
	db := s.getdb(tx)
	tblName := info.TableName()
	execSql := fmt.Sprintf("INSERT INTO %s(uid,date,task_id,valid_hold_day,finish_day_task_cnt,valid_open_cnt,income,ticket_cnt)VALUES(?,?,?,?,?,?,?,?)"+
		" ON DUPLICATE KEY UPDATE update_time=update_time", tblName)
	err := db.Exec(execSql, info.Uid, info.Date.Format("2006-01-02"), info.TaskId, 0, 0, 0, 0, 0).Error
	if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
		err = db.Exec(fmt.Sprintf(CreateTblHallTaskAnchorWeekSQL, info.Date.Format("2006"))).Error
		if err != nil {
			return err
		}
		err = db.Exec(execSql, info.Uid, info.Date.Format("2006-01-02"), info.TaskId, 0, 0, 0, 0, 0).Error
	}
	return err
}

func (s *HallTaskStore) UpdateHallTaskAnchorWeek(tx *gorm.DB, info *HallTaskAnchorWeek) error {
	db := s.getdb(tx)
	tblName := info.TableName()
	//execSql := fmt.Sprintf("INSERT INTO %s(uid,task_id,date,valid_hold_day,finish_day_task_cnt,valid_open_cnt,income,ticket_cnt)VALUES(?,?,?,?,?,?,?,?)"+
	//	" ON DUPLICATE KEY UPDATE valid_hold_day=valid_hold_day+?, finish_day_task_cnt=finish_day_task_cnt+?, valid_open_cnt=valid_open_cnt+?, income=income+?, ticket_cnt=ticket_cnt+?", tblName)
	execSql := fmt.Sprintf("UPDATE %s SET"+
		" valid_hold_day=valid_hold_day+?, finish_day_task_cnt=finish_day_task_cnt+?, valid_open_cnt=valid_open_cnt+?, "+
		"income=income+?, ticket_cnt=ticket_cnt+? WHERE uid=? AND date=? AND task_id=?", tblName)
	err := db.Exec(execSql, info.ValidHoldDay, info.FinishDayTaskCnt, info.ValidOpenCnt, info.Income, info.TicketCnt,
		info.Uid, info.Date.Format("2006-01-02"), info.TaskId).Error
	return err
}

func (c *HallTaskAnchorDaily) TableName() string {
	return fmt.Sprintf("%s%s", TblHallTaskAnchorDaily, c.Date.Format("200601"))
}

func (s *HallTaskStore) InitHallTaskAnchorDaily(tx *gorm.DB, info *HallTaskAnchorDaily) error {
	db := s.getdb(tx)
	tblName := info.TableName()
	execSql := fmt.Sprintf("INSERT INTO %s(uid,date,task_id,valid_sec,valid_open_cnt,income,ticket_cnt)VALUES(?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE update_time=update_time", tblName)
	err := db.Exec(execSql, info.Uid, info.Date.Format("2006-01-02"), info.TaskId, 0, 0, 0, 0).Error
	if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
		err = db.Exec(fmt.Sprintf(CreateTblHallTaskAnchorDailySQL, info.Date.Format("200601"))).Error
		if err != nil {
			return err
		}
		err = db.Exec(execSql, info.Uid, info.Date.Format("2006-01-02"), info.TaskId, 0, 0, 0, 0).Error
	}
	return err
}

func (s *HallTaskStore) UpdateHallTaskAnchorDaily(tx *gorm.DB, info *HallTaskAnchorDaily) error {
	db := s.getdb(tx)
	tblName := info.TableName()
	//execSql := fmt.Sprintf("INSERT INTO %s(uid,task_id,date,valid_sec,valid_open_cnt,income,ticket_cnt)VALUES(?,?,?,?,?,?,?)"+
	//	" ON DUPLICATE KEY UPDATE valid_sec=valid_sec+?, valid_open_cnt=valid_open_cnt+?, income=income+?,ticket_cnt=ticket_cnt+?", tblName)
	execSql := fmt.Sprintf("UPDATE %s SET"+
		" valid_sec=valid_sec+?, valid_open_cnt=valid_open_cnt+?, income=income+?,ticket_cnt=ticket_cnt+? WHERE uid=? and date=? and task_id=?", tblName)
	//err := db.Exec(execSql, info.Uid, info.TaskId, info.Date.Format("2006-01-02"), info.ValidSec, info.ValidOpenCnt, info.Income, info.TicketCnt,
	//	info.ValidSec, info.ValidOpenCnt, info.Income, info.TicketCnt).Error
	err := db.Exec(execSql, info.ValidSec, info.ValidOpenCnt, info.Income, info.TicketCnt,
		info.Uid, info.Date.Format("2006-01-02"), info.TaskId).Error

	return err
}

func (s *HallTaskStore) GetHallTaskAnchorDaily(tx *gorm.DB, uid, taskId uint32, ts time.Time, isForUpdate bool) (*HallTaskAnchorDaily, error) {
	db := s.getdb(tx)
	info := HallTaskAnchorDaily{Date: ts}
	db = db.Table((&HallTaskAnchorDaily{Date: ts}).TableName()).Where("uid=? and date=? and task_id=?",
		uid, ts.Format("2006-01-02"), taskId)
	if isForUpdate {
		db = db.Set("gorm:query_option", "FOR UPDATE")
	}

	err := db.Scan(&info).Error
	if err != nil && !checkErrIsRecordNotFound(err) && !checkErrIsTableNotExist(err) {
		return nil, err
	}
	return &info, nil
}

func (s *HallTaskStore) GetHallTaskAnchorDailyTimeRange(uid, taskId uint32, beginTm, endTm time.Time) ([]*HallTaskAnchorDaily, error) {
	list := []*HallTaskAnchorDaily{}
	db := s.db.Table((&HallTaskAnchorDaily{Date: beginTm}).TableName()).Where("uid=? and task_id=? and date>=? and date<=?",
		uid, taskId, beginTm.Format("2006-01-02"), endTm.Format("2006-01-02"))
	err := db.Order("date desc").Find(&list).Error
	if err != nil && !checkErrIsTableNotExist(err) {
		return nil, err
	}
	return list, nil
}

func (s *HallTaskStore) GetHallTaskRecordChangeLog(uid uint32) ([]*HallTaskChangeLog, error) {
	list := []*HallTaskChangeLog{}
	err := s.db.Table(TblHallTaskChangeLog).Where("uid=?", uid).Find(&list).Error
	return list, err
}

type HallTaskRecordChange struct {
	Uid         uint32
	SignGuildId uint32
}

// 追加任务变更记录
func (s *HallTaskStore) AppendHallTaskRecordChangeLog(tx *gorm.DB, guildId, taskId, opUid, createTime, changeType uint32, list []*HallTaskRecordChange) error {
	if len(list) == 0 {
		return nil
	}
	db := s.getdb(tx)
	tm := time.Unix(int64(createTime), 0)
	tmStr := tm.Format("2006-01-02 15:04:05")
	limit := 200
	size := len(list)
	for i := 0; i < size; i += limit {
		end := i + limit
		if end > size {
			end = size
		}

		tmpList := list[i:end]
		queryClauseList := make([]string, 0)
		params := make([]interface{}, 0)
		for _, info := range tmpList {
			queryClauseList = append(queryClauseList, "(?,?,?,?,?,?,?)")
			params = append(params, info.Uid, info.SignGuildId, guildId, taskId, opUid, changeType, tmStr)
		}
		execSql := fmt.Sprintf("INSERT INTO %s(uid,sign_guild_id,guild_id,task_id,op_uid,change_type,create_time)VALUES %s",
			TblHallTaskChangeLog, strings.Join(queryClauseList, ", "))
		err := db.Exec(execSql, params...).Error
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *HallTaskStore) AddHallTask(tx *gorm.DB, info *HallTask) (isDuplicate bool, err error) {
	db := s.getdb(tx)
	execSql := fmt.Sprintf("INSERT INTO %s(uid,guild_id,sign_guild_id,task_id,op_uid)VALUES(?,?,?,?,?)", info.TableName())
	err = db.Exec(execSql, info.Uid, info.GuildId, info.SignGuildId, info.TaskId, info.OpUid).Error
	if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1062 {
		err = nil
		isDuplicate = true
	}
	return
}

func (s *HallTaskStore) ReplaceHallTask(tx *gorm.DB, info *HallTask) error {
	db := s.getdb(tx)
	execSql := fmt.Sprintf("REPLACE INTO %s(uid,guild_id,sign_guild_id,task_id,op_uid)VALUES(?,?,?,?,?)", info.TableName())
	return db.Exec(execSql, info.Uid, info.GuildId, info.SignGuildId, info.TaskId, info.OpUid).Error
}

func (s *HallTaskStore) DelHallTask(tx *gorm.DB, uid uint32) (bool, error) {
	db := s.getdb(tx)
	execSql := fmt.Sprintf("DELETE FROM %s WHERE uid=?", TblHallTask)
	db = db.Exec(execSql, uid)
	return db.RowsAffected == 1, db.Error
}

func (s *HallTaskStore) DelHallTaskByTaskId(tx *gorm.DB, taskId uint32) (int64, error) {
	db := s.getdb(tx)
	execSql := fmt.Sprintf("DELETE FROM %s WHERE task_id=?", TblHallTask)
	db = db.Exec(execSql, taskId)
	return db.RowsAffected, db.Error
}

func (s *HallTaskStore) DelHallTaskByGuildId(tx *gorm.DB, guildIds []uint32) (int64, error) {
	db := s.getdb(tx)
	execSql := fmt.Sprintf("DELETE FROM %s WHERE guild_id in(?)", TblHallTask)
	db = db.Exec(execSql, guildIds)
	return db.RowsAffected, db.Error
}

// 查实时任务
func (s *HallTaskStore) GetHallTask(tx *gorm.DB, uid uint32) (*HallTask, error) {
	db := s.getdb(tx)
	info := HallTask{}
	db = db.Table((&HallTask{}).TableName()).Where("uid=?", uid)
	err := db.Scan(&info).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return &info, nil
}

func (s *HallTaskStore) GetHallTaskByTaskId(tx *gorm.DB, taskId uint32) ([]*HallTask, error) {
	db := s.getdb(tx)
	list := []*HallTask{}
	err := db.Table((&HallTask{}).TableName()).Where("task_id=?", taskId).Find(&list).Error
	return list, err
}

func (s *HallTaskStore) GetHallTaskByGuildId(tx *gorm.DB, guildIds []uint32) ([]*HallTask, error) {
	db := s.getdb(tx)
	list := []*HallTask{}
	db = db.Table((&HallTask{}).TableName())
	db = db.Where("guild_id in(?)", guildIds)
	err := db.Find(&list).Error
	return list, err
}

func (s *HallTaskStore) GetHallTaskList(tx *gorm.DB, guildId, uid, taskId uint32, offset, limit uint32) (uint32, []*HallTask, error) {
	db := s.getdb(tx)
	list := []*HallTask{}
	db = db.Table((&HallTask{}).TableName())
	if guildId > 0 {
		db = db.Where("guild_id=?", guildId)
	}
	if uid > 0 {
		db = db.Where("uid=?", uid)
	}
	if taskId > 0 {
		db = db.Where("task_id=?", taskId)
	}
	var cnt uint32
	err := db.Count(&cnt).Error
	if err != nil {
		return 0, nil, err
	}
	err = db.Order("update_time desc").Offset(offset).Limit(limit).Find(&list).Error
	return cnt, list, err
}

func (s *HallTaskStore) GetAllHallTaskList(offset, limit uint32) ([]*HallTask, error) {
	list := []*HallTask{}
	db := s.db.Table((&HallTask{}).TableName())
	err := db.Order("uid asc").Offset(offset).Limit(limit).Find(&list).Error
	return list, err
}

func (s *HallTaskStore) AddHallTaskConf(tx *gorm.DB, info *HallTaskConf) error {
	db := s.getdb(tx)
	execSql := fmt.Sprintf("INSERT INTO %s(guild_id,task_group_name,task_conf_opt,reward_intro)VALUES(?,?,?,?)", info.TableName())
	return db.Exec(execSql, info.GuildId, info.TaskGroupName, info.TaskConfOpt, info.RewardIntro).Error
}

func (s *HallTaskStore) UpdateHallTaskConf(tx *gorm.DB, info *HallTaskConf) (bool, error) {
	db := s.getdb(tx)
	execSql := fmt.Sprintf("UPDATE %s SET task_group_name=?,task_conf_opt=?,reward_intro=? WHERE task_id=? and is_del=0", info.TableName())
	db = db.Exec(execSql, info.TaskGroupName, info.TaskConfOpt, info.RewardIntro, info.TaskId)
	return db.RowsAffected == 1, db.Error
}

/*
func (s *HallTaskStore) UpdateHallTaskNextWeekConf(info *HallTaskConf) (bool, error) {
	execSql := fmt.Sprintf("UPDATE %s SET next_week_effect_conf=? WHERE task_id=?", info.TableName())
	db := s.db.Exec(execSql, info.NextWeekEffectConf, info.TaskId)
	return db.RowsAffected == 1, db.Error
}
*/

func (s *HallTaskStore) DelHallTaskConf(tx *gorm.DB, taskId uint32) (bool, error) {
	db := s.getdb(tx)
	//execSql := fmt.Sprintf("DELETE FROM %s WHERE task_id=?", (&HallTaskConf{}).TableName())
	execSql := fmt.Sprintf("UPDATE %s SET is_del=1 WHERE task_id=? AND is_del=0", (&HallTaskConf{}).TableName())
	db = db.Exec(execSql, taskId)
	return db.RowsAffected == 1, db.Error
}

func (s *HallTaskStore) GetHallTaskConfByName(tx *gorm.DB, guildId uint32, taskGroupName string) ([]*HallTaskConf, error) {
	db := s.getdb(tx)
	list := []*HallTaskConf{}
	db = db.Table((&HallTaskConf{}).TableName()).Where("guild_id=?", guildId)
	db = db.Where("task_group_name=?", taskGroupName)
	db = db.Where("is_del=0")
	err := db.Find(&list).Error
	return list, err
}

func (s *HallTaskStore) GetHallTaskConf(tx *gorm.DB, guildId, taskId uint32, taskGroupName string, offset, limit uint32) (uint32, []*HallTaskConf, error) {
	db := s.getdb(tx)
	list := []*HallTaskConf{}
	db = db.Table((&HallTaskConf{}).TableName()).Where("guild_id=?", guildId)
	if taskId > 0 {
		db = db.Where("task_id=?", taskId)
	}
	if taskGroupName != "" {
		db = db.Where("task_group_name=?", taskGroupName)
	}
	db = db.Where("is_del=0")
	var cnt uint32
	err := db.Count(&cnt).Error
	if err != nil {
		return 0, nil, err
	}
	err = db.Order("update_time desc").Offset(offset).Limit(limit).Find(&list).Error
	return cnt, list, err
}

func (s *HallTaskStore) GetHallTaskConfMap(taskIds []uint32, isEffect bool) (map[uint32]*HallTaskConf, error) {
	list := []*HallTaskConf{}
	db := s.db.Table((&HallTaskConf{}).TableName()).Where("task_id in(?)", taskIds)
	if isEffect {
		db = db.Where("is_del=0")
	}
	err := db.Find(&list).Error
	if err != nil {
		return nil, err
	}
	taskId2Conf := map[uint32]*HallTaskConf{}
	for _, info := range list {
		taskId2Conf[info.TaskId] = info
	}
	return taskId2Conf, nil
}

// 批量查询周版本任务配置
func (s *HallTaskStore) GetHallTaskWeekConfMap(taskIds []uint32, tm time.Time) (map[uint32]*HallTaskConfWeek, error) {
	weekBegin, _ := utils.CalcWeekDurtion(tm)
	taskId2Conf := map[uint32]*HallTaskConfWeek{}
	if len(taskIds) == 0 {
		return taskId2Conf, nil
	}

	list := []*HallTaskConfWeek{}
	db := s.db.Table((&HallTaskConfWeek{}).TableName()).Where("week_date=? and task_id in(?)", weekBegin.Format("2006-01-02"), taskIds)
	err := db.Find(&list).Error
	if err != nil {
		return taskId2Conf, err
	}

	for _, info := range list {
		taskId2Conf[info.TaskId] = info
	}
	return taskId2Conf, nil
}

// 只返回当前生效的
func (s *HallTaskStore) GetHallTaskConfByTaskId(tx *gorm.DB, taskId uint32) (*HallTaskConf, *pb.HallTaskConfDetialInfo, error) {
	db := s.getdb(tx)
	info := HallTaskConf{}
	detail := &pb.HallTaskConfDetialInfo{}

	db = db.Table((&HallTaskConf{}).TableName()).Where("task_id=? and is_del=0", taskId)
	err := db.Scan(&info).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return &info, detail, err
	}

	if info.TaskConfOpt != "" {
		detail, err = UnarshalHallTaskConfDetialInfo(info.TaskConfOpt)
		if err != nil {
			return &info, detail, err
		}
	}
	return &info, detail, nil
}

// 被删除的任务也会返回数据
func (s *HallTaskStore) GetHallTaskConfInfo(taskId uint32) (*HallTaskConf, *pb.HallTaskConfDetialInfo, error) {
	info := HallTaskConf{}
	db := s.db.Table((&HallTaskConf{}).TableName()).Where("task_id=?", taskId)
	err := db.Scan(&info).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, nil, err
	}
	detail := &pb.HallTaskConfDetialInfo{}
	if info.TaskConfOpt != "" {
		detail, err = UnarshalHallTaskConfDetialInfo(info.TaskConfOpt)
		if err != nil {
			return nil, nil, err
		}
	}
	return &info, detail, nil
}

func MarshalHallTaskConfDetialInfo(c *pb.HallTaskConfDetialInfo) string {
	info := &pb.HallTaskConfDetialInfo{
		DayTaskList:  c.GetDayTaskList(),
		WeekTaskList: c.GetWeekTaskList(),
	}
	s, _ := json.Marshal(info)
	return string(s)
}
func UnarshalHallTaskConfDetialInfo(str string) (*pb.HallTaskConfDetialInfo, error) {
	c := &pb.HallTaskConfDetialInfo{}
	err := json.Unmarshal([]byte(str), c)
	return c, err
}

// IsLimitNoselfGuild为true可以配置非本公会成员
func (s *HallTaskStore) GetHallGuildLimit(guildId uint32) (exist bool, isLimitNoselfGuild bool, err error) {
	info := HallGuildLimit{}
	db := s.db.Table(TblHallGuildLimit).Where("guild_id=?", guildId)
	err2 := db.Scan(&info).Error
	if err2 != nil {
		if err2 == gorm.ErrRecordNotFound {
			return
		}
		err = err2
		return
	}
	exist = true
	isLimitNoselfGuild = info.IsLimitNoselfGuild == 1
	return
}

func (s *HallTaskStore) UpdateHallGuildLimitStatus(guildId uint32, isLimitNoselfGuild bool) error {
	isLimit := 0
	if isLimitNoselfGuild {
		isLimit = 1
	}
	execSql := fmt.Sprintf("INSERT INTO %s(guild_id,is_limit_noself_guild)VALUES(?,?)"+
		" ON DUPLICATE KEY UPDATE is_limit_noself_guild=?", TblHallGuildLimit)
	return s.db.Exec(execSql, guildId, isLimit, isLimit).Error
}

func (s *HallTaskStore) GetHallChannelWeekStats(guildIds []uint32, beginTs, endTs, offset, limit uint32) (uint32, []*HallChannelStats, error) {
	list := []*HallChannelStats{}
	db := s.db.Table(TblHallChannelWeekStats)
	if len(guildIds) == 1 {
		db = db.Where("guild_id=?", guildIds[0])
	} else {
		db = db.Where("guild_id in(?)", guildIds)
	}

	if beginTs > 0 && endTs > 0 {
		beginTm := time.Unix(int64(beginTs), 0).Format("2006-01-02")
		endTm := time.Unix(int64(endTs), 0).Format("2006-01-02")
		db = db.Where("date>=? and date<=?", beginTm, endTm)
	}

	var cnt uint32
	err := db.Count(&cnt).Error
	if err != nil {
		return 0, list, err
	}

	err = db.Order("date desc").Offset(offset).Limit(limit).Find(&list).Error
	return cnt, list, err
}

func (s *HallTaskStore) GetHallChannelDailyStats(guildIds []uint32, beginTs, endTs, offset, limit uint32) (uint32, []*HallChannelStats, error) {
	list := []*HallChannelStats{}
	db := s.db.Table(TblHallChannelDailyStats)
	if len(guildIds) == 1 {
		db = db.Where("guild_id=?", guildIds[0])
	} else {
		db = db.Where("guild_id in(?)", guildIds)
	}

	if beginTs > 0 && endTs > 0 {
		beginTm := time.Unix(int64(beginTs), 0).Format("2006-01-02")
		endTm := time.Unix(int64(endTs), 0).Format("2006-01-02")
		db = db.Where("date>=? and date<=?", beginTm, endTm)
	}

	var cnt uint32
	err := db.Count(&cnt).Error
	if err != nil {
		return 0, list, err
	}

	err = db.Order("date desc").Offset(offset).Limit(limit).Find(&list).Error
	return cnt, list, err
}

func (s *HallTaskStore) GetHallChannelDailyStatsById(id uint32) (*HallChannelStats, error) {
	info := HallChannelStats{}
	db := s.db.Table(TblHallChannelDailyStats).Where("id=?", id)
	err := db.Scan(&info).Error
	if err != nil && !checkErrIsRecordNotFound(err) && !checkErrIsTableNotExist(err) {
		return nil, err
	}
	return &info, nil
}

// 批量新增
func (s *HallTaskStore) AddHallGuildChannel(list []*pb.MultiPlayerHallInfo) error {
	if len(list) == 0 {
		return nil
	}
	//info := &HallGuildChannel{}
	for _, info := range list {
		guildId := info.GuildId
		channelId := info.ChannelId
		execSql := fmt.Sprintf("INSERT INTO %s(guild_id,channel_id)VALUES(?,?) ON DUPLICATE KEY UPDATE is_del=0, update_time=?", TblHallGuildChannel)
		err := s.db.Exec(execSql, guildId, channelId, time.Now().Format("2006-01-02 15:04:05")).Error
		if err != nil {
			log.Errorf("AddHallGuildChannel fail %v, guildId %d channelId %d", err, guildId, channelId)
			if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1062 {
				continue
			}
			return err
		}
	}
	return nil
}

func (s *HallTaskStore) GetHallGuildChannelTotal(guildIds []uint32) (uint32, error) {
	db := s.db.Model(&HallGuildChannel{})
	if len(guildIds) > 0 {
		db = db.Where("guild_id in(?)", guildIds)
	}
	db = db.Where("is_del=0")
	var cnt int64
	err := db.Count(&cnt).Error
	return uint32(cnt), err
}

func (s *HallTaskStore) GetHallGuildChannelList(guildIds []uint32, offset, limit uint32) ([]*HallGuildChannel, error) {
	list := []*HallGuildChannel{}
	db := s.db.Table((&HallGuildChannel{}).TableName())
	if len(guildIds) > 0 {
		db = db.Where("guild_id in(?)", guildIds)
	}
	db = db.Where("is_del=0")
	db = db.Order("update_time desc")
	if limit > 0 {
		db = db.Offset(offset).Limit(limit)
	}
	err := db.Find(&list).Error
	return list, err
}

// 查公会所有的承接大厅，包括已回收的
func (s *HallTaskStore) GetGuildMultiPlayerHall(guildId uint32) ([]*HallGuildChannel, error) {
	list := []*HallGuildChannel{}
	db := s.db.Table((&HallGuildChannel{}).TableName())
	if guildId > 0 {
		db = db.Where("guild_id=?", guildId)
	}
	db = db.Order("update_time desc")
	err := db.Find(&list).Error
	return list, err
}

// 查公会下所有生效的承接大厅
func (s *HallTaskStore) GetHallGuildChannelEffect(tx *gorm.DB, guildIds []uint32) ([]*HallGuildChannel, error) {
	db := s.getdb(tx)
	list := []*HallGuildChannel{}
	db = db.Table((&HallGuildChannel{}).TableName())
	//if guildId > 0 {
	db = db.Where("guild_id in(?)", guildIds)
	db = db.Where("is_del=0")
	//}
	//db = db.Order("update_time desc")
	err := db.Find(&list).Error
	return list, err
}

// 查询当前生效的承接大厅
func (s *HallTaskStore) GetHallGuildChannel(channelId uint32) (guildId uint32, err error) {
	info := HallGuildChannel{}
	db := s.db.Table((&HallGuildChannel{}).TableName()).Select("guild_id").Where("channel_id=? and is_del=0", channelId)
	err = db.Scan(&info).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, err
	}
	return info.GuildId, nil
}

func (s *HallTaskStore) DelHallGuildChannel(tx *gorm.DB, guildId, channelId uint32) (bool, error) {
	db := s.getdb(tx)
	//execSql := fmt.Sprintf("DELETE FROM %s WHERE guild_id=? AND channel_id=?", (&HallGuildChannel{}).TableName())
	execSql := fmt.Sprintf("UPDATE %s SET is_del=1 WHERE channel_id=? AND is_del=0", (&HallGuildChannel{}).TableName())
	db = db.Exec(execSql, channelId)
	return db.RowsAffected == 1, db.Error
}

func (s *HallTaskStore) RecordHallChannelWeekStats(tx *gorm.DB, info *HallChannelStats) error {
	db := s.getdb(tx)
	execSql := fmt.Sprintf("INSERT INTO %s(guild_id,channel_id,date,enter_channel_cnt,has_ticket_cnt,used_ticket_cnt,channel_fee)VALUES(?,?,?,?,?,?,?)"+
		" ON DUPLICATE KEY UPDATE enter_channel_cnt=enter_channel_cnt+?, has_ticket_cnt=has_ticket_cnt+?, used_ticket_cnt=used_ticket_cnt+?, channel_fee=channel_fee+?",
		TblHallChannelWeekStats)
	return db.Exec(execSql, info.GuildId, info.ChannelId, info.Date.Format("2006-01-02"), info.EnterChannelCnt, info.HasTicketCnt, info.UsedTicketCnt, info.ChannelFee,
		info.EnterChannelCnt, info.HasTicketCnt, info.UsedTicketCnt, info.ChannelFee).Error
}

func (s *HallTaskStore) RecordHallChannelDailyStats(tx *gorm.DB, info *HallChannelStats) error {
	db := s.getdb(tx)
	execSql := fmt.Sprintf("INSERT INTO %s(guild_id,channel_id,date,enter_channel_cnt,has_ticket_cnt,used_ticket_cnt,channel_fee)VALUES(?,?,?,?,?,?,?)"+
		" ON DUPLICATE KEY UPDATE enter_channel_cnt=enter_channel_cnt+?, has_ticket_cnt=has_ticket_cnt+?, used_ticket_cnt=used_ticket_cnt+?, channel_fee=channel_fee+?", TblHallChannelDailyStats)
	err := db.Exec(execSql, info.GuildId, info.ChannelId, info.Date.Format("2006-01-02"), info.EnterChannelCnt, info.HasTicketCnt, info.UsedTicketCnt, info.ChannelFee,
		info.EnterChannelCnt, info.HasTicketCnt, info.UsedTicketCnt, info.ChannelFee).Error
	return err
}

func (s *HallTaskStore) RecordHallTaskValidOpen(tx *gorm.DB, record *HallTaskValidOpen) error {
	db := s.getdb(tx)
	execSql := fmt.Sprintf("INSERT INTO %s(uid,target_uid,channel_id,start_time,end_time,valid_sec,create_time)"+
		"VALUES(?,?,?,?,?,?,?)", record.TableName())
	err := db.Exec(execSql, record.Uid, record.TargetUid, record.ChannelId,
		record.StartTime.Format("2006-01-02 15:04:05"),
		record.EndTime.Format("2006-01-02 15:04:05"),
		record.ValidSec,
		record.CreateTime.Format("2006-01-02 15:04:05"),
	).Error
	if checkErrIsTableNotExist(err) {
		err = db.Exec(fmt.Sprintf(CreateTblHallTaskValidOpenSQL, record.CreateTime.Format("2006"))).Error
		if err != nil {
			return err
		}
		err = db.Exec(execSql, record.Uid, record.TargetUid, record.ChannelId,
			record.StartTime.Format("2006-01-02 15:04:05"),
			record.EndTime.Format("2006-01-02 15:04:05"),
			record.ValidSec,
			record.CreateTime.Format("2006-01-02 15:04:05"),
		).Error
	}
	return err
}

func (s *HallTaskStore) getdb(tx *gorm.DB) *gorm.DB {
	db := s.db
	if tx != nil {
		db = tx
	}
	return db
}

// ==========================================================================================

func (c *HallTaskValidOpen) TableName() string {
	return fmt.Sprintf("%s%d", TblHallTaskValidOpen, c.CreateTime.Year())
}

func (c *HallTaskAnchorChannelDaily) TableName() string {
	return fmt.Sprintf("%s%s", TblHallTaskAnchorChannelDaily, c.Date.Format("2006"))
}
func (c *HallTaskAnchorChannelWeek) TableName() string {
	return fmt.Sprintf("%s%s", TblHallTaskAnchorChannelWeek, c.Date.Format("2006"))
}

func (c *HallGuildChannel) TableName() string {
	return TblHallGuildChannel
}

func (c *HallTaskConf) TableName() string {
	return TblHallTaskConf
}

func (c *HallTask) TableName() string {
	return TblHallTask
}

func (c *HallTaskConf) String() string {
	s, _ := json.Marshal(c)
	return string(s)
}

const (
	CreateTblHallGuildChannelSql = `
	CREATE TABLE IF NOT EXISTS tbl_hall_guild_channel (
		id int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
		guild_id int(10) unsigned NOT NULL COMMENT '公会id',
		channel_id int(10) unsigned NOT NULL COMMENT '房间id',
		update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
		PRIMARY KEY (id),
		UNIQUE KEY uniq_guild_channel_id (guild_id, channel_id)
	  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '大厅公会房配置表';`

	CreateTblHallTaskAnchorDailySQL = `
	  CREATE TABLE IF NOT EXISTS tbl_hall_task_anchor_daily_%s (
		id int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
		uid int(10) unsigned NOT NULL COMMENT '签约成员uid',
		task_id int(10) unsigned NOT NULL COMMENT '任务id',
		date datetime NOT NULL COMMENT '日期',
		valid_sec int(10) unsigned NOT NULL DEFAULT '0' COMMENT '接档时间-秒',
		valid_open_cnt int(10) unsigned NOT NULL DEFAULT '0' COMMENT '有效开局数',
		ticket_cnt int(10) unsigned NOT NULL DEFAULT '0' COMMENT '大神带飞券数量',
		income int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'T豆收礼数',
		update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
		PRIMARY KEY (id),
		UNIQUE KEY uniq_task (uid, date, task_id),
		KEY idx_uid (uid),
		KEY idx_task_id (task_id)
	  )ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '签约成员日任务明细表';`

	CreateTblHallTaskAnchorWeekSQL = `
	CREATE TABLE IF NOT EXISTS tbl_hall_task_anchor_week_%s (
		id int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
		uid int(10) unsigned NOT NULL COMMENT '签约成员uid',
		task_id int(10) unsigned NOT NULL COMMENT '任务id',
		date datetime NOT NULL COMMENT '周一日期',
		valid_hold_day int(10) unsigned NOT NULL DEFAULT '0' COMMENT '有效接档天数',
		finish_day_task_cnt int(10) unsigned NOT NULL DEFAULT '0' COMMENT '完成日任务次数',
		ticket_cnt int(10) unsigned NOT NULL DEFAULT '0' COMMENT '大神带飞券数量',
		valid_open_cnt int(10) unsigned NOT NULL DEFAULT '0' COMMENT '有效开局数',
		income int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'T豆收礼数',
		update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
		PRIMARY KEY (id),
		UNIQUE KEY uniq_task (uid, date, task_id),
		KEY idx_uid (uid),
		KEY idx_task_id (task_id)
	  )ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '签约成员周任务明细表';`

	CreateTblHallTaskAnchorChannelDailySQL = `
	CREATE TABLE IF NOT EXISTS tbl_hall_task_anchor_channel_daily_%s (
		id int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
		uid int(10) unsigned NOT NULL DEFAULT '0' COMMENT '签约成员uid',
		channel_id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '房间id',
		date datetime NOT NULL COMMENT '日期',
		task_id int(10) unsigned NOT NULL COMMENT '任务id',
		valid_sec int(10) unsigned NOT NULL DEFAULT '0' COMMENT '接档时间-秒',
		valid_open_cnt int(10) unsigned NOT NULL DEFAULT '0' COMMENT '有效开局数',
		ticket_cnt int(10) unsigned NOT NULL DEFAULT '0' COMMENT '大神带飞券数量',
		income int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'T豆收礼数',
		update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
		PRIMARY KEY (id),
		UNIQUE KEY uniq_task (channel_id,date,uid,task_id),
		KEY idx_uid (uid),
		KEY idx_date (date),
		KEY idx_channel_id (channel_id)
	  )ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '承接大厅房间-接档日任务明细表';`

	CreateTblHallTaskAnchorChannelWeekSQL = `
	CREATE TABLE IF NOT EXISTS tbl_hall_task_anchor_channel_week_%s (
		id int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
		uid int(10) unsigned NOT NULL DEFAULT '0' COMMENT '签约成员uid',
		channel_id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '房间id',
		date datetime NOT NULL COMMENT '周一日期',
		task_id int(10) unsigned NOT NULL COMMENT '任务id',
		valid_hold_day int(10) unsigned NOT NULL DEFAULT '0' COMMENT '有效接档天数',
		finish_day_task_cnt int(10) unsigned NOT NULL DEFAULT '0' COMMENT '完成日任务次数',
		ticket_cnt int(10) unsigned NOT NULL DEFAULT '0' COMMENT '大神带飞券数量',
		valid_open_cnt int(10) unsigned NOT NULL DEFAULT '0' COMMENT '有效开局数',
		income int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'T豆收礼数',
		update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
		PRIMARY KEY (id),
		UNIQUE KEY uniq_task (channel_id,date,uid,task_id),
		KEY idx_uid (uid),
		KEY idx_date (date),
		KEY idx_channel_id (channel_id)
	  )ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '承接大厅房间-接档周任务明细表';`

	CreateTblHallPresentHistorySQL = `
	CREATE TABLE IF NOT EXISTS tbl_hall_present_history_monthly_%s (
		order_id varchar(255) NOT NULL COMMENT '订单号',
		from_uid int(10) unsigned NOT NULL COMMENT '送礼人uid',
		to_uid int(10) unsigned NOT NULL COMMENT '收礼人uid',
		guild_id int(10) unsigned NOT NULL COMMENT '房间绑定公会id',
		channel_id int(10) unsigned NOT NULL COMMENT '房间id',
		item_id int(10) unsigned NOT NULL COMMENT '礼物id',
		item_count int(10) unsigned NOT NULL COMMENT '礼物个数',
		total_price int(10) unsigned NOT NULL COMMENT '总价值',
		price_type int(10) unsigned NOT NULL COMMENT '0默认 1红钻礼物 2-T豆礼物',
		tag_type int(10) unsigned NOT NULL COMMENT '0默认 9-体验券',
		outside_time datetime NOT NULL COMMENT '外部系统时间',
		update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
		PRIMARY KEY (order_id),
		KEY idx_outside_time(outside_time),
		KEY idx_channel_id (channel_id),
		KEY idx_fromuid_itemid (from_uid, item_id),
		KEY idx_touid_itemid (to_uid, item_id)
	  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '承接大厅送礼流水表';
	  `
	CreateTblHallTaskValidOpenSQL = `
	CREATE TABLE IF NOT EXISTS tbl_hall_task_valid_open_%s (
		id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
		uid int(10) unsigned NOT NULL DEFAULT '0' COMMENT '签约成员uid',
		target_uid int(10) unsigned NOT NULL DEFAULT '0' COMMENT '玩家uid',
		channel_id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '房间id',
		start_time datetime NOT NULL COMMENT '有效开局的开始时间',
		end_time datetime NOT NULL COMMENT '有效开局的结束时间',
		valid_sec int(10) unsigned NOT NULL DEFAULT '0' COMMENT '同时在麦时长',
		create_time datetime NOT NULL COMMENT '创建时间',
		PRIMARY KEY (id),
		KEY idx_uid (uid),
		KEY idx_target_uid (target_uid),
		KEY idx_create_time (create_time)
	  )ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '承接大厅房间-有效开局记录';
	`
)

type HallTaskStore struct {
	db *gorm.DB
}

func NewHallTaskStore(cfg *config.MysqlConfig) (*HallTaskStore, error) {
	log.Infof("NewHallTaskStore cfg=%s", cfg.ConnectionString())
	mysqlDb, err := gorm.Open("mysql", cfg.ConnectionString())
	if err != nil {
		log.Errorf("NewHallTaskStore Failed to create mysql %v", err)
		return nil, err
	}
	mysqlDb = mysqlDb.Debug()
	return &HallTaskStore{db: mysqlDb}, nil
}

func (s *HallTaskStore) DB() *gorm.DB {
	return s.db
}

func (s *HallTaskStore) Transaction(ctx context.Context, f func(tx *gorm.DB) error) error {
	tx := s.db.BeginTx(ctx, &sql.TxOptions{})

	err := f(tx)
	if err != nil {
		log.Errorf("Transaction fail err %v", err)
		_ = tx.Rollback()
		return err
	}

	_ = tx.Commit()
	return nil
}

/*
// 判断是否主键或唯一键冲突错误
func checkErrIsDuplicateKey(err error) bool {
	if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1062 {
		return true
	}
	return false
}
*/

// 判断是否表不存在
func checkErrIsTableNotExist(err error) bool {
	if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
		return true
	}
	return false
}

// 判断是否记录不存在
func checkErrIsRecordNotFound(err error) bool {
	return err == gorm.ErrRecordNotFound
}
