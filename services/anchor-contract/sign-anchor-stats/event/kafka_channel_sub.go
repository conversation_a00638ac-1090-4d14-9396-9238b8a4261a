package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	eventlink "golang.52tt.com/pkg/event-link-wrap"
	"golang.52tt.com/pkg/log"
	channelPb "golang.52tt.com/protocol/app/channel"
	channel_go_event "golang.52tt.com/protocol/services/channelol-go/event"
	"golang.52tt.com/services/anchor-contract/sign-anchor-stats/manager"
)

const (
	topicTypeChannel = "channelol_go_ev"
)

type ChannelKafkaSub struct {
	kafkaSub *eventlink.EventLinkAsyncSub
	mgr      *manager.SignAnchorStatsMgr
}

func NewChannelKafkaSub(clientId, groupId string, topics, brokers []string, mgr *manager.SignAnchorStatsMgr) (*ChannelKafkaSub, error) {

	sub := &ChannelKafkaSub{
		mgr: mgr,
	}

	log.Infof("kafka_config Topics:%s, Brokers:%s", topics, brokers)
	//kafkaSub, err := event.NewKafkaSubV2(kafkaConfig, sub.handlerEvent)

	option := []eventlink.Option{
		eventlink.WithMaxRetryTimes(5),
		eventlink.WithProcessWorkerNum(10),
	}

	kafkaSub, err := eventlink.NewEventLinkAsyncSub2(brokers, topics, groupId,
		clientId, sub.handlerEvent, option...)
	if err != nil {
		log.Errorf("Failed to create kafka-subscriber %+v", err)
		return nil, err
	}
	sub.kafkaSub = kafkaSub

	return sub, nil
}

func (s *ChannelKafkaSub) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	switch msg.Topic {
	case topicTypeChannel:
		return s.handlerChannelEvent(ctx, msg)
	}
	return nil, false
}

func (s *ChannelKafkaSub) Close() {
	s.kafkaSub.Stop()
}

func (s *ChannelKafkaSub) handlerChannelEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	channelEvent := &channel_go_event.ChannelOLEvent{}
	err := proto.Unmarshal(msg.Value, channelEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerChannelEvent handlerEvent Unmarshal err:%v", err)
		return nil, false
	}

	log.Debugf("handlerChannelEvent begin %v", channelEvent)

	if channelEvent.GetChannelType() != uint32(channelPb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) &&
		channelEvent.GetChannelType() != uint32(channelPb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) &&
		channelEvent.GetChannelType() != uint32(channelPb.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
		log.DebugWithCtx(ctx, "handlerChannelEvent no handler ev:%+v", channelEvent)
		return nil, false
	}

	switch channelEvent.Event.(type) {
	case *channel_go_event.ChannelOLEvent_EnterEvent:
		s.mgr.HandleChannelEnterEvent(channelEvent)
	case *channel_go_event.ChannelOLEvent_LeaveEvent:
		s.mgr.HandleChannelLeaveEvent(channelEvent)
	}

	log.DebugWithCtx(ctx, "handlerChannelEvent end %v", channelEvent)
	return nil, false
}
