package manager

/*
import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	sign_anchor_stats "golang.52tt.com/protocol/services/sign-anchor-stats"
	"golang.52tt.com/services/anchor-contract/sign-anchor-stats/cache"
	"golang.52tt.com/services/anchor-contract/sign-anchor-stats/mysql"
	"golang.52tt.com/services/anchor-contract/sign-anchor-stats/utils"
)

/*
[33m[2023-08-21 16:17:23][0m  [36;1m[0.63ms][0m  SELECT * FROM `tbl_hall_task_anchor_week_2023`  WHERE (uid=1 and date='2023-08-21') ORDER BY update_time desc
[36;31m[0 rows affected or returned ][0m

[35m(/root/quicksilver/services/anchor-contract/sign-anchor-stats/mysql/hall_task.go:482)[0m
[33m[2023-08-21 16:17:23][0m  [36;1m[0.63ms][0m  SELECT * FROM `tbl_hall_task_anchor_daily_202308`  WHERE (uid=1 and date>='2023-08-21' and date<='2023-08-27') ORDER BY date,task_id desc
[36;31m[0 rows affected or returned ][0m

--
insert tbl_hall_task_anchor_week_2023(uid,date,task_id,valid_hold_day)values();

*/

/*
func init() {
	log.SetLevel(log.DebugLevel)
}

// GetHallTaskConfPeriod
// go test -timeout 30s -run ^TestGetHallTaskConfPeriod$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/manager -v -count=1
func TestGetHallTaskConfPeriod(t *testing.T) {

	m := newMgr()
	m.GetHallTaskConfPeriod(1, time.Now())

}

// go test -timeout 30s -run ^TestX$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/manager -v -count=1
func TestX(t *testing.T) {

	m := newMgr()

	now := time.Now()
	weekBegin, _ := utils.CalcWeekDurtion(now)
	nextWeekBegin := weekBegin.AddDate(0, 0, 7)

	lockkey := fmt.Sprintf("hall_task:push_%s", now.Format("************"))
	t.Log(lockkey)

	t.Log(nextWeekBegin)
	t.Log(nextWeekBegin.Unix())
	return

	m.AddHallTaskConf(context.Background(), &sign_anchor_stats.AddHallTaskConfReq{
		GuildId: 1,
		Info: &sign_anchor_stats.HallTaskConfDetialInfo{

			TaskId:        1,
			TaskGroupName: "覆盖",
			DayTaskList:   []*sign_anchor_stats.HallTaskConfInfo{{TaskType: 1, ValList: []float32{1, 2, 3}}},
			WeekTaskList:  []*sign_anchor_stats.HallTaskConfInfo{{TaskType: 1, ValList: []float32{1, 2}}},
			RewardMsg:     "覆盖re",
		},
	})

	return
	m.GetHallTaskConfById(context.Background(), &sign_anchor_stats.GetHallTaskConfByIdReq{TaskId: 1})
	m.GetHallTaskConfById(context.Background(), &sign_anchor_stats.GetHallTaskConfByIdReq{TaskId: 1, QueryType: 1})
	return

	t.Log(nextWeekBegin)
	t.Log(nextWeekBegin.YearDay())

	tm := time.Time{}
	t.Log(tm.Format("2006-01-02"))
	return

	// parseHoldTimeTaskVal
	// (valList []float32, sec uint32) (taskLevel uint32, taskProgress string, rate float32)
	t.Log(parseHoldTimeTaskVal([]float32{1.5, 2.5, 3.5}, 189))

}

// []*pb.HallTaskDetial

/*
func (m *SignAnchorStatsMgr) GetHallTaskHistory(ctx context.Context, in *pb.GetHallTaskHistoryReq) (*pb.GetHallTaskHistoryResp, error) {
	out := &pb.GetHallTaskHistoryResp{}
	uid := in.Uid
	if uid == 0 {
		return out, nil
	}
	now := time.Now()
	//nowDayZeroTs := utils.GetDayZeroTime(now).Unix()
	beginTm := time.Date(now.Year(), now.Month()-6, now.Day(), 0, 0, 0, 0, time.Local)
	log.DebugfWithCtx(ctx, "GetHallTaskHistory uid=%d beginTm=%v", uid, beginTm)

	// 查日任务
	dayTaskList := []*mysql.HallTaskAnchorDaily{}
	for i := 0; i < 7; i++ {
		tm1 := time.Date(now.Year(), now.Month()-time.Month(i), 1, 0, 0, 0, 0, time.Local)
		tm2 := time.Date(now.Year(), now.Month()-time.Month(i)+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second)
		if i == 0 {
			tm2 = now
		} else if i == 6 {
			tm2 = beginTm
		}

		list, err := m.HallStore.GetHallTaskAnchorDailyTimeRange(uid, tm1, tm2)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetHallTaskHistory GetHallTaskAnchorDailyTimeRange fail %v, uid=%d", err, uid)
			return out, err
		}
		log.DebugfWithCtx(ctx, "GetHallTaskHistory GetHallTaskAnchorDailyTimeRange uid=%d %v %v cnt=%d list=%s",
			uid, tm1, tm2, len(list), utils2.ToJson(list))
		dayTaskList = append(dayTaskList, list...)
	}
	log.DebugfWithCtx(ctx, "GetHallTaskHistory uid=%d dayTaskList.size=%d", uid, len(dayTaskList))

	// 查周任务
	weekBegin, weekEnd := utils.CalcWeekDurtion(now)
	statsWeekBegin, _ := utils.CalcWeekDurtion(beginTm)
	for weekBegin != statsWeekBegin {

		weekInfo, err := m.HallStore.GetHallTaskAnchorWeek(nil, uid, 0, weekBegin, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetHallTaskHistory GetHallTaskAnchorDailyTimeRange fail %v, uid=%d", err, uid)
			return out, err
		}
		log.DebugfWithCtx(ctx, "GetHallTaskHistory uid=%d weekBegin=%v weekEnd=%v weekInfo=%v", uid, weekBegin, weekEnd, weekInfo)

		detail := &pb.HallTaskHistoryDetial{
			Date: fmt.Sprintf("%d.%d.%d-%d.%d", weekBegin.Year(), weekBegin.Month(), weekBegin.Day(), weekEnd.Month(), weekEnd.Day()),
		}

		if weekInfo.TaskId > 0 {
			_, taskOpt, err := m.HallStore.GetHallTaskConfInfo(weekInfo.TaskId)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetHallTaskHistory GetHallTaskConfByTaskId fail %v, uid=%d", err, uid)
				return out, err
			}
			detail.WeekTaskList = fillWeekTaskDetailPB(weekInfo, taskOpt)
		}
		for _, info := range dayTaskList {
			if info.Date.Unix() >= weekBegin.Unix() && info.Date.Unix() <= weekEnd.Unix() {
				_, taskOpt, err := m.HallStore.GetHallTaskConfInfo(info.TaskId)
				if err != nil {
					log.ErrorWithCtx(ctx, "GetHallTaskHistory GetHallTaskConfByTaskId fail %v, uid=%d", err, uid)
					return out, err
				}

				detail.DayTaskList = append(detail.DayTaskList, fillDayTaskDetailPB(info, 0, taskOpt)...)
			}
		}

		if len(detail.DayTaskList) > 0 || len(detail.WeekTaskList) > 0 {
			out.List = append(out.List, detail)
		}

		weekBegin = weekBegin.AddDate(0, 0, -7)
		weekEnd = weekEnd.AddDate(0, 0, -7)
	}

	return out, nil
}

*/

/*
// handleTaskUpgrade
// go test -timeout 30s -run ^TestHhandleTaskUpgrade$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/manager -v -count=1
func TestHhandleTaskUpgrade(t *testing.T) {

	//t.Log(parseHoldTimeTaskVal([]float32{10, 20, 30}, 60))    // 0.1
	//t.Log(parseHoldTimeTaskVal([]float32{10, 20, 30}, 600))   // 0.5
	//t.Log(parseHoldTimeTaskVal([]float32{10, 20, 120}, 5400)) //
	//return

	t.Log(parseTaskVal([]float32{10000000}, 50, "豆"))

	return
	t.Log(parseTaskVal([]float32{100, 200, 300}, 150, "豆"))

	t.Log(parseTaskVal([]float32{1, 2, 3}, 1, "豆"))
	t.Log(parseTaskVal([]float32{1, 2, 3}, 10, "豆"))
	t.Log(parseTaskVal([]float32{1, 2, 3}, 0, "豆"))
	t.Log(parseTaskVal([]float32{1, 2, 3}, 2, "豆"))
	//m := newMgr()

	// handleTaskUpgrade(uid, taskId uint32, tm time.Time, isRecordFinishDayTaskCnt bool, taskvalList []float32, beforeTaskVal, finalTaskVal float32, taskName, unit string)

}

// go test -timeout 30s -run ^TestH$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/manager -v -count=1
func TestH(t *testing.T) {

	t.Log(0 & 8)

	v1 := uint32(0)
	v2 := uint32(8)
	t.Log(v1 - v2)
	return

	ctx := context.Background()
	m := newMgr()
	err := m.HallStore.Transaction(context.Background(), func(tx *gorm.DB) error {
		isDup, err := m.HallStore.AddHallTask(tx, &mysql.HallTask{
			Uid:         1,
			SignGuildId: 1,
			GuildId:     1,
			TaskId:      1,
			OpUid:       1,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "DistributeHallTask AddHallTask fail %v, uid=%d", 1)
			return err
		}
		log.Infof("isDup=%v", isDup)
		return err
	})
	t.Log(err)

}

func test1() {

}

func newMgr() *SignAnchorStatsMgr {
	m := &SignAnchorStatsMgr{}
	hs, err := mysql.NewHallTaskStore(&config.MysqlConfig{
		Host:     "**************",
		Port:     3306,
		Database: "appsvr",
		UserName: "godman",
		Password: "thegodofman",
		Charset:  "utf8",
	})
	if err != nil {
		log.Errorln(err)
		return nil
	}

	redisCli, err := cache.NewHallTaskRedis(&config.RedisConfig{
		Host: "**************",
		Port: 6379,
	})
	if err != nil {
		log.Errorln(err)
		return nil
	}

	m.HallStore = hs
	m.HallRedis = redisCli
	return m
}

/*
// 送礼kafka
func (m *SignAnchorStatsMgr) HandlePresentEventHallTaskBak(presentEvent *kafkapresent.PresentEvent) {

	orderId := presentEvent.OrderId
	sendUid := presentEvent.Uid
	toUid := presentEvent.TargetUid
	channelId := presentEvent.ChannelId
	channelType := presentEvent.ChannelType
	totalPrice := presentEvent.Price * presentEvent.ItemCount
	sendTm := time.Unix(int64(presentEvent.SendTime), 0)
	weekBegin, _ := utils.CalcWeekDurtion(sendTm)
	itemCount := presentEvent.ItemCount

	if orderId == "" || sendUid == 0 || toUid == 0 || channelId == 0 {
		return
	}

	// 过滤非公会房
	if channelType != uint32(channelGA.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		return
	}

	log.Debugf("HandlePresentEventHallTask event=%s", presentEvent.String())

	ok, err := m.HallRedis.CheckPresentOrderId(orderId)
	if err != nil {
		log.Errorf("HandlePresentEventHallTask CheckPresentOrderId fail %v orderId=%s", err, orderId)
		return
	}
	if !ok {
		log.Errorf("HandlePresentEventHallTask orderId exist. orderId=%s", orderId)
		return
	}

	// 过滤非承接大厅房间
	guildId, err := m.CheckIsHallGuildChannel(channelId)
	if err != nil {
		log.Errorf("HandlePresentEventHallTask CheckIsHallGuildChannel fail %v channelId=%d orderId=%s", err, channelId, orderId)
		return
	}
	log.Debugf("HandlePresentEventHallTask CheckIsHallGuildChannel orderId=%s channelId=%d guildId=%d", orderId, channelId, guildId)
	if guildId == 0 {
		return
	}

	// 厅数据 T豆收礼流水
	if presentEvent.PriceType == uint32(presentPb.PresentPriceType_PRESENT_PRICE_TBEAN) {
		err = m.HallStore.Transaction(context.TODO(), func(tx *gorm.DB) error {
			// 厅数据
			err := m.HallStore.RecordHallChannelDailyStats(tx, &mysql.HallChannelStats{
				GuildId:    presentEvent.GuildId,
				ChannelId:  channelId,
				Date:       sendTm,
				ChannelFee: uint64(totalPrice),
			})
			if err != nil {
				log.Errorf("HandlePresentEventHallTask RecordHallChannelDailyStats fail %v", err)
				return err
			}

			err = m.HallStore.RecordHallChannelWeekStats(tx, &mysql.HallChannelStats{
				GuildId:    presentEvent.GuildId,
				ChannelId:  channelId,
				Date:       weekBegin,
				ChannelFee: uint64(totalPrice),
			})
			if err != nil {
				log.Errorf("HandlePresentEventHallTask RecordHallChannelWeekStats fail %v", err)
				return err
			}
			return nil
		})
		if err != nil {
			log.Errorf("HandlePresentEventHallTask 厅数据 T豆收礼流水 fail %v, event=%s", err, presentEvent.String())
		}
	}

	// 查任务
	task, err := m.HallStore.GetHallTask(nil, toUid)
	if err != nil {
		log.Errorf("HandlePresentEventHallTask GetHallTask fail %v channelId=%d orderId=%s", err, channelId, orderId)
		return
	}
	log.Debugf("HandlePresentEventHallTask toUid=%d GetHallTask=%+v", toUid, task)
	if task.TaskId == 0 {
		return
	}
	// 判断是否在对应公会的承接大厅房间
	if !guild_group.GetDyConfHandler().CheckIsGuildGroup(guildId, task.GuildId) {
		log.Infof("HandlePresentEventHallTask not guild group. uid=%d channelId=%d guildId=%d task.GuildId=%d orderId=%s",
			toUid, channelId, guildId, task.GuildId, orderId)
		return
	}

	taskId := task.TaskId
	// 查任务配置信息
	taskInfo, taskOpt, err := m.HallStore.GetHallTaskConfByTaskId(task.TaskId)
	if err != nil {
		log.Errorf("HandlePresentEventHallTask GetHallTaskConfByTaskId fail %v channelId=%d orderId=%s", err, channelId, orderId)
		return
	}
	if taskInfo.TaskId == 0 {
		log.Errorf("HandlePresentEventHallTask no find task %d", task.TaskId)
		return
	}

	// 记录流水
	err = m.HallStore.Transaction(context.Background(), func(tx *gorm.DB) error {
		err := m.HallStore.RecordHallPresentHistory(tx, &mysql.HallPresentHistory{
			OrderId:     orderId,
			FromUid:     sendUid,
			ToUid:       toUid,
			GuildId:     presentEvent.GuildId,
			ChannelId:   channelId,
			ItemId:      presentEvent.ItemId,
			ItemCount:   presentEvent.ItemCount,
			TotalPrice:  totalPrice,
			SourceType:  presentEvent.PriceType,
			OutsideTime: time.Unix(int64(presentEvent.SendTime), 0),
		})
		return err
	})

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()

	dayTaskMap, weekTaskMap := parseTaskOpt(taskOpt)
	dayTaskValList, weekTaskValList := parseTaskValList(taskOpt)
	log.Infof("HandlePresentEventHallTask uid=%d taskId=%d dayTaskMap=%+v, weekTaskMap=%+v",
		toUid, task.TaskId, dayTaskMap, weekTaskMap)

	// T豆
	if presentEvent.PriceType == uint32(presentPb.PresentPriceType_PRESENT_PRICE_TBEAN) {

		// 签约成员在麦收礼
		//if !dayTaskMap[pb.HALL_TASK_DAY_TYPE_HALL_TASK_DAY_TYPE_TBEAN_INCOME] &&
		//	!weekTaskMap[pb.HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_TBEAN_INCOME] {
		//	log.Infof("HandlePresentEventHallTask not record income. uid=%d taskId=%d", toUid, task.TaskId)
		//	return
		//}

		// 反查是否在麦
		isHoldMic, err := m.checkIskHoldMic(ctx, channelId, toUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandlePresentEventHallTask checkIskHoldMic fail %v, event=%s", err, presentEvent.String())
			//return
		}
		log.Infof("HandlePresentEventHallTask uid=%d cid=%d isHoldMic=%v orderId=%s, sendTs=%s",
			toUid, channelId, isHoldMic, orderId, sendTm)
		if 0 == isHoldMic {
			return
		}

		var (
			beforeDailyIncome = uint32(0)
			beforeWeekIncome  = uint32(0)
			finalDailyIncome  = uint32(0)
			finalWeekIncome   = uint32(0)
		)

		// 先insert一条
		err = m.InitHallTaskAnchorRecord(toUid, task.TaskId, sendTm, weekBegin)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandlePresentEventHallTask InitHallTaskAnchorRecord fail %v, event=%s", err, presentEvent.String())
			return
		}

		// 签约成员收礼数据
		err = m.HallStore.Transaction(ctx, func(tx *gorm.DB) error {
			if dayTaskMap[pb.HALL_TASK_DAY_TYPE_HALL_TASK_DAY_TYPE_TBEAN_INCOME] {
				// FOR UPDATE
				taskDay, err := m.HallStore.GetHallTaskAnchorDaily(tx, toUid, task.TaskId, sendTm, true)
				if err != nil {
					log.ErrorWithCtx(ctx, "HandlePresentEventHallTask GetHallTaskAnchorDaily fail %v, event=%s", err, presentEvent.String())
					return err
				}

				err = m.HallStore.UpdateHallTaskAnchorDaily(tx, &mysql.HallTaskAnchorDaily{
					Uid:    toUid,
					TaskId: task.TaskId,
					Date:   sendTm,
					Income: totalPrice,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "HandlePresentEventHallTask UpdateHallTaskAnchorDaily fail %v, event=%s", err, presentEvent.String())
					return err
				}

				err = m.HallStore.RecordHallTaskAnchorChannelDaily(tx, &mysql.HallTaskAnchorChannelDaily{
					Uid:       toUid,
					ChannelId: channelId,
					Date:      sendTm,
					TaskId:    taskId,
					Income:    totalPrice,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "HandlePresentEventHallTask RecordHallTaskAnchorChannelDaily fail %v, event=%s", err, presentEvent.String())
					return err
				}

				beforeDailyIncome = taskDay.Income
				finalDailyIncome = taskDay.Income + totalPrice

			}
			if weekTaskMap[pb.HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_TBEAN_INCOME] {
				// FOR UPDATE
				weekInfo, err := m.HallStore.GetHallTaskAnchorWeek(tx, toUid, task.TaskId, weekBegin, true)
				if err != nil {
					log.ErrorWithCtx(ctx, "HandlePresentEventHallTask GetHallTaskAnchorWeek fail %v, event=%s", err, presentEvent.String())
					return err
				}

				err = m.HallStore.UpdateHallTaskAnchorWeek(tx, &mysql.HallTaskAnchorWeek{
					Uid:    toUid,
					TaskId: task.TaskId,
					Date:   weekBegin,
					Income: totalPrice,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "HandlePresentEventHallTask UpdateHallTaskAnchorWeek fail %v, event=%s", err, presentEvent.String())
					return err
				}

				err = m.HallStore.RecordHallTaskAnchorChannelWeek(tx, &mysql.HallTaskAnchorChannelWeek{
					Uid:       toUid,
					ChannelId: channelId,
					Date:      weekBegin,
					Income:    totalPrice,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "HandlePresentEventHallTask RecordHallTaskAnchorChannelWeek fail %v, event=%s", err, presentEvent.String())
					return err
				}

				beforeWeekIncome = weekInfo.Income
				finalWeekIncome = weekInfo.Income + totalPrice

			}
			return nil
		})
		if err != nil {
			log.Errorf("HandlePresentEventHallTask Transaction fail %v, orderId %s", err, presentEvent.OrderId)
			return
		}

		// 判断收礼任务进度
		if dayTaskMap[pb.HALL_TASK_DAY_TYPE_HALL_TASK_DAY_TYPE_TBEAN_INCOME] {
			valList := dayTaskValList[pb.HALL_TASK_DAY_TYPE_HALL_TASK_DAY_TYPE_TBEAN_INCOME]
			isRecordFinishDayTaskCnt := weekTaskMap[pb.HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_FINISH_DAY_TASK_CNT]
			m.handleTaskUpgrade(toUid, taskId, "日任务", sendTm, isRecordFinishDayTaskCnt, valList, float32(beforeDailyIncome), float32(finalDailyIncome), "流水", "豆")
		}
		if weekTaskMap[pb.HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_TBEAN_INCOME] {
			valList := weekTaskValList[pb.HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_TBEAN_INCOME]
			m.handleTaskUpgrade(toUid, taskId, "周任务", sendTm, false, valList, float32(beforeWeekIncome), float32(finalWeekIncome), "流水", "豆")
		}

		// 最近一次收礼时的大厅
		_ = m.HallRedis.SetLastChannelOl(toUid, channelId)

		// 缓存收礼信息
		_ = m.HallRedis.RecordHallLastSendPresentTime(toUid, sendUid, presentEvent.SendTime)
	}

	// 券
	//if presentEvent.PriceType == uint32(presentPb.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
	// 券可能是T豆礼物，不用只限制红钻
	if presentEvent.TagType == uint32(gaBase.PresentTagType_PRESENT_TAG_TICKET) {

		log.Infof("HandlePresentEventHallTask send ticket. sendUid=%d toUid=%d cid=%d orderId=%s priceType=%d itemId=%d itemCount=%d",
			sendUid, toUid, channelId, orderId, presentEvent.PriceType, presentEvent.ItemId, itemCount)

		// 使用券的人数 在该承接大厅内，送出体验券的用户数。
		dayNewUse, _ := m.HallRedis.SetUseTicketDaily(sendUid, channelId, presentEvent.SendTime)
		weekNewUse, _ := m.HallRedis.SetUseTicketWeek(sendUid, channelId, presentEvent.SendTime)
		log.Infof("HandlePresentEventHallTask sendUid=%d channelId=%d UsedTicketCnt dayNewUse=%d weekNewUse=%d", sendUid, channelId, dayNewUse, weekNewUse)

		// 厅数据
		err = m.HallStore.Transaction(ctx, func(tx *gorm.DB) error {
			err = m.HallStore.RecordHallChannelDailyStats(tx, &mysql.HallChannelStats{
				ChannelId:     channelId,
				Date:          sendTm,
				GuildId:       guildId,
				UsedTicketCnt: dayNewUse,
			})
			if err != nil {
				return err
			}
			err = m.HallStore.RecordHallChannelWeekStats(tx, &mysql.HallChannelStats{
				ChannelId:     channelId,
				Date:          weekBegin,
				GuildId:       guildId,
				UsedTicketCnt: weekNewUse,
			})
			if err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			log.Errorf("HandlePresentEventHallTask UsedTicketCnt fail %v, orderId=%s", err, orderId)
		}

		if !dayTaskMap[pb.HALL_TASK_DAY_TYPE_HALL_TASK_DAY_TYPE_GOD_FLY_TICKET_CNT] &&
			!weekTaskMap[pb.HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_GOD_FLY_TICKET_CNT] {
			log.Infof("HandlePresentEventHallTask not record ticket. uid=%d taskId=%d orderId=%s", toUid, task.TaskId, orderId)
			return
		}

		log.Infof("HandlePresentEventHallTask add ticket. uid=%d fromUid=%d cid=%d orderId=%s",
			toUid, sendUid, channelId, orderId)

		var (
			beforeDailyTicketCnt = uint32(0)
			beforeWeekTicketCnt  = uint32(0)
			finalDailyTicketCnt  = uint32(0)
			finalWeekTicketCnt   = uint32(0)
		)

		// 先insert一条
		err = m.InitHallTaskAnchorRecord(toUid, task.TaskId, sendTm, weekBegin)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandlePresentEventHallTask InitHallTaskAnchorRecord fail %v, event=%s", err, presentEvent.String())
			return
		}

		err = m.HallStore.Transaction(ctx, func(tx *gorm.DB) error {
			if dayTaskMap[(pb.HALL_TASK_DAY_TYPE_HALL_TASK_DAY_TYPE_GOD_FLY_TICKET_CNT)] {
				// FOR UPDATE
				taskDay, err := m.HallStore.GetHallTaskAnchorDaily(tx, toUid, task.TaskId, sendTm, true)
				if err != nil {
					log.ErrorWithCtx(ctx, "HandlePresentEventHallTask GetHallTaskAnchorDaily fail %v, event=%s", err, presentEvent.String())
					return err
				}

				err = m.HallStore.UpdateHallTaskAnchorDaily(tx, &mysql.HallTaskAnchorDaily{
					Uid:       toUid,
					TaskId:    task.TaskId,
					Date:      sendTm,
					TicketCnt: itemCount,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "HandlePresentEventHallTask RecordHallTaskAnchorDaily fail %v, event=%s", err, presentEvent.String())
					return err
				}

				// 记录房间维度的
				err = m.HallStore.RecordHallTaskAnchorChannelDaily(tx, &mysql.HallTaskAnchorChannelDaily{
					Uid:       toUid,
					ChannelId: channelId,
					Date:      sendTm,
					TaskId:    taskId,
					TicketCnt: itemCount,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "HandlePresentEventHallTask RecordHallTaskAnchorChannelDaily fail %v, event=%s", err, presentEvent.String())
					return err
				}

				beforeDailyTicketCnt = taskDay.TicketCnt
				finalDailyTicketCnt = taskDay.TicketCnt + itemCount
			}
			if weekTaskMap[(pb.HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_GOD_FLY_TICKET_CNT)] {
				// FOR UPDATE
				weekInfo, err := m.HallStore.GetHallTaskAnchorWeek(tx, toUid, task.TaskId, weekBegin, true)
				if err != nil {
					log.ErrorWithCtx(ctx, "HandlePresentEventHallTask GetHallTaskAnchorWeek fail %v, event=%s", err, presentEvent.String())
					return err
				}

				err = m.HallStore.UpdateHallTaskAnchorWeek(tx, &mysql.HallTaskAnchorWeek{
					Uid:       toUid,
					TaskId:    task.TaskId,
					Date:      weekBegin,
					TicketCnt: itemCount,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "HandlePresentEventHallTask RecordHallTaskAnchorDaily fail %v, event=%s", err, presentEvent.String())
					return err
				}

				// 记录房间维度的
				err = m.HallStore.RecordHallTaskAnchorChannelWeek(tx, &mysql.HallTaskAnchorChannelWeek{
					Uid:       toUid,
					ChannelId: channelId,
					Date:      weekBegin,
					TicketCnt: itemCount,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "HandlePresentEventHallTask RecordHallTaskAnchorChannelWeek fail %v, event=%s", err, presentEvent.String())
					return err
				}

				beforeWeekTicketCnt = weekInfo.TicketCnt
				finalWeekTicketCnt = weekInfo.TicketCnt + itemCount
			}
			return nil
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "HandlePresentEventHallTask fail %v, event=%s", err, presentEvent.String())
			return
		}

		// 判断下收券任务进度
		if dayTaskMap[pb.HALL_TASK_DAY_TYPE_HALL_TASK_DAY_TYPE_GOD_FLY_TICKET_CNT] {
			valList := dayTaskValList[pb.HALL_TASK_DAY_TYPE_HALL_TASK_DAY_TYPE_GOD_FLY_TICKET_CNT]
			isRecordFinishDayTaskCnt := weekTaskMap[pb.HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_FINISH_DAY_TASK_CNT]
			m.handleTaskUpgrade(toUid, taskId, "日任务", sendTm, isRecordFinishDayTaskCnt, valList, float32(beforeDailyTicketCnt), float32(finalDailyTicketCnt), "大神带飞券", "张")
		}
		if weekTaskMap[pb.HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_GOD_FLY_TICKET_CNT] {
			valList := weekTaskValList[pb.HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_GOD_FLY_TICKET_CNT]
			m.handleTaskUpgrade(toUid, taskId, "周任务", sendTm, false, valList, float32(beforeWeekTicketCnt), float32(finalWeekTicketCnt), "大神带飞券", "张")
		}
	}

}
*/
