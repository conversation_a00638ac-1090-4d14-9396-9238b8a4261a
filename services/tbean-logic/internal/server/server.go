package server

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/clients/account"
	"golang.52tt.com/pkg/log"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/tbeanlogic"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/tbean-logic/internal/conf"
	"golang.52tt.com/services/tbean-logic/internal/group"
	"golang.52tt.com/services/tbean-logic/internal/metrics"
)

type StartConfig struct {
	// [optional] from startup arguments

	// from config file
	*conf.ServiceConfigT
}

type TBeanLogicServer struct {
	serviceConfig *conf.ServiceConfigT
	dyconf        *conf.SDyConfigHandler
	accountClient *account.Client
}

func NewTBeanLogicServe(ctx context.Context, config *StartConfig) (*TBeanLogicServer, error) {
	// sc := &conf.ServiceConfigT{}

	// if cfgPath == "" {
	// 	return nil, errors.New("configfile not exist")
	// }
	// err := sc.Parse(cfgPath)
	// if err != nil {
	// 	return nil, err
	// }

	sc := config.ServiceConfigT
	log.Infof("init cfg %+v", sc)
	if sc == nil || sc.AppId == "" || sc.GroupURL == "" || sc.SecretKey == "" {
		return nil, fmt.Errorf("no find conf")
	}

	group.Url = sc.GroupURL
	group.SecretKey = sc.SecretKey
	group.Caller = sc.Caller
	group.AppId = sc.AppId
	log.Infof("group_config: Url %q, SecretKey %q, Caller %q, AppId %q, RequestTimeout %q", group.Url, group.SecretKey, group.Caller, group.AppId, group.RequestTimeout)

	accountCli, _ := account.NewClient()

	dyconfig := conf.NewConfigHandler(conf.DyCfgFile)
	err := dyconfig.Start()
	if err != nil {
		log.Errorf("dyconfig.Start fail %v", err)
		return nil, err
	}

	return &TBeanLogicServer{
		dyconf:        dyconfig,
		serviceConfig: sc,
		accountClient: accountCli,
	}, nil
}

func (s *TBeanLogicServer) ShutDown() {

}
func (s *TBeanLogicServer) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	log.DebugWithCtx(ctx, "Echo req:[%+v]", req)
	return req, nil
}

// 此cmd预留
func (s *TBeanLogicServer) GetUserTbeanConsume(ctx context.Context, req *tbeanlogic.GetUserTbeanConsumeReq) (resp *tbeanlogic.GetUserTbeanConsumeResp, err error) {
	log.Infof("GetUserTbeanConsume u %d", req.GetUid())
	resp = &tbeanlogic.GetUserTbeanConsumeResp{}

	return
}

func (s *TBeanLogicServer) GetUserGroupInfo(ctx context.Context, req *tbeanlogic.GetUserGroupInfoReq) (resp *tbeanlogic.GetUserGroupInfoResp, err error) {

	resp = &tbeanlogic.GetUserGroupInfoResp{}

	var age uint32
	var registeredAt uint32
	var regDay uint32

	defer func() {

		metrics.ReportMetrics(resp.GetChannelIn(), resp.GetChannelOut())
		log.InfoWithCtx(ctx, "GetUserGroupInfo u %d age %d reg %d regDay %d, ChannelIn %v ChannelOut %v",
			req.GetUid(), age, registeredAt, regDay, resp.GetChannelIn(), resp.GetChannelOut())
	}()

	if req.GetUid() == 0 {
		return
	}

	if s.dyconf.GetGroupInfo(req.Uid) {
		resp.ChannelIn = true
		resp.ChannelOut = true
		log.InfoWithCtx(ctx, "GetUserGroupInfo white. uid=%d", req.Uid)
		return
	}

	ctx2, _ := protogrpc.NewContextWithInfoTimeout(ctx, time.Second)
	userInfo, err := s.accountClient.GetUserByUid(ctx2, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserByUid failed u %d err %s", req.GetUid(), err)
		return
	}

	registeredAt = userInfo.GetRegisteredAt()

	nowTs := uint32(time.Now().Unix())
	regDay = (nowTs - registeredAt) / (3600 * 24)

	// 若注册小于等于3天，房间内外所有全服都不可见
	if regDay <= 3 {
		return
	}

	// 若注册大于3天，则按以下逻辑判断：
	resp.ChannelOut = group.GetGroupFlag(s.serviceConfig.ChannelOutGroupID, req.GetUid())
	resp.ChannelIn = group.GetGroupFlag(s.serviceConfig.ChannelInGroupID, req.GetUid())
	return
}

// func getAge(birthdayStr string) uint32 {
// 	birthday := strings.Split(birthdayStr, "-")

// 	if len(birthday) < 3 {
// 		return 0
// 	}

// 	birYear, _ := strconv.Atoi(birthday[0])
// 	birMonth, _ := strconv.Atoi(birthday[1])

// 	age := time.Now().Year() - birYear

// 	if int(time.Now().Month()) < birMonth {
// 		age--
// 	}

// 	return uint32(age)
// }
