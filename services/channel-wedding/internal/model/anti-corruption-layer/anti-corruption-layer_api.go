package anti_corruption_layer

import (
	context "context"
	app "golang.52tt.com/protocol/app"
	imPB "golang.52tt.com/protocol/app/im"
	channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
	channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
	channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
	fellow_level_award "golang.52tt.com/protocol/services/fellow-level-award"
	fellow_svr "golang.52tt.com/protocol/services/fellow-svr"
	pb "golang.52tt.com/protocol/services/push-notification/v2"
	virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
	virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
	comm "golang.52tt.com/services/channel-wedding/internal/model/comm"
)

type IACLayer interface {
	AwardDress(ctx context.Context, uid, cpUid, holdingDay, outsideTs, dressType uint32, giftId, orderId string) error
	BatchGetUserChannelId(ctx context.Context, uidList []uint32) (map[uint32]uint32,error)
	CheckUserVirtualImageMicDisplay(ctx context.Context, uid uint32) (bool,error)
	CheckUsersInChannel(ctx context.Context, uidList []uint32, channelId uint32) (bool,error)
	EndWeddingChairGame(ctx context.Context, cid, weddingId uint32) error
	GetAllThemeAwardConfig(ctx context.Context) (*channel_wedding_conf.GetThemeFinishedAwardCfgResp,error)
	GetFellowBindByUidPair(ctx context.Context, uidA, uidB uint32) (*fellow_svr.FellowInfo,error)
	GetMyWeddingInfo(ctx context.Context, uid uint32) (*channel_wedding_plan.GetMyWeddingInfoResponse,error)
	GetOnMicUserList(ctx context.Context, cid uint32) (map[uint32]uint32,error)
	GetUserProfile(ctx context.Context, uid uint32) (*app.UserProfile,error)
	GetUserProfileMap(ctx context.Context, uidList []uint32, replace bool) (map[uint32]*app.UserProfile,error)
	GetUserVirtualImageInuseMap(ctx context.Context, uidList []uint32) (map[uint32][]*virtual_image_user.InuseItemInfo,error)
	GetVirtualImageResourceMap(ctx context.Context, ids []uint32) (map[uint32]*virtual_image_resource.VirtualImageResourceInfo,error)
	GetWeddingPlanInfo(ctx context.Context, planId uint32) (*channel_wedding_plan.GetSimpleWeddingPlanInfoResponse,error)
	GetWeddingThemeCfg(ctx context.Context, themeId uint32) (*channel_wedding_conf.ThemeCfg,error)
	KickCommUserOutMicSpace(ctx context.Context, weddingInfo *channel_wedding.WeddingInfo) error
	PushBreakingNews(ctx context.Context, channelId, newsId uint32, weddingInfo *channel_wedding.WeddingInfo) error
	PushToUsers(ctx context.Context, userIDList []uint32, notification *pb.CompositiveNotification) error
	SendCPFellowLine(ctx context.Context, fellowAwardReq *fellow_level_award.AddLevelAwardReq) error
	SendChannelMsg(ctx context.Context, uid, channelId uint32, content string) error
	SendCommonShelfPresent(ctx context.Context, in *SendPresentReq) error
	SendGroupPhotoPosChangeMsg(ctx context.Context, opUid, cid uint32, micIdList []uint32) error
	SendGuestEnterRoomMsg(ctx context.Context, opUid, cid, guestUid, guestType uint32) error
	SendHappinessChangeMsg(ctx context.Context, uid, cid, happiness, nextLevelTipsValue uint32) error
	SendIMCommonXmlMsg(ctx context.Context, fromUid uint32, toUidList []uint32, commStr string, extData *imPB.IMCommonXmlMsg, msgType uint32) error
	SendIMMsgAsync(c context.Context, fromUid uint32, toUidList []uint32, content, highLight, jumpUrl string) (err error)
	SendLevelChangeMsg(ctx context.Context, weddingInfo *channel_wedding.WeddingInfo, level uint32, upgradeCloths map[uint32][]uint32, poseList []*channel_wedding.UserWeddingPose) error
	SendPresentToUser(ctx context.Context, uid, channelId, presentId uint32, weddingId int64) error
	SendSceneNotifyMsg(ctx context.Context, in *SceneNotifyMsgReq) error
	SendStageChangeMsg(ctx context.Context, opUid uint32, info *channel_wedding.WeddingInfo) error
	SendVirtualImageChangeMsg(ctx context.Context, opUid, cid uint32, uidList []uint32, uid2Pose map[uint32]*channel_wedding.UserWeddingPose) error
	SendVirtualImageItems(ctx context.Context, uid, durationSec uint32, giveItemList []*comm.GiveVAItemInfo) error
	SendVirtualImageSuitWithUse(ctx context.Context, uid uint32, suitInfo *comm.UpgradeSuitInfo, allowUseSubCateMap map[uint32]bool) error
	SendWeddingBridesmaidUpdateMsg(ctx context.Context, cid uint32, bridesmaidManList []uint32) error
	SetMicStatus(ctx context.Context, cid, status uint32, micIdList []uint32) error
	SimplePushToChannel(ctx context.Context, fromUid, channelId, channelMsgType uint32, content string, pbOptData []byte) error
	SimplePushToChannelUsers(ctx context.Context, recvUids []uint32, fromUid, channelId, channelMsgType uint32, content string, pbOptData []byte, isReliable bool) error
	SimpleSendTTAssistantText(ctx context.Context, toUid uint32, content, highlight, url string) error
	Stop() 
	UpdateWeddingPlanStatus(ctx context.Context, planId uint32) error
	UseVirtualImageSuit(ctx context.Context, uid uint32, itemIdList []uint32, allowUseSubCateMap map[uint32]bool) error
}

