// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-ext-game-http-logic/channel-ext-game-http.proto

package channel_ext_game_http

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// deprecated
type SessionCheckReq struct {
	Session              string   `protobuf:"bytes,1,opt,name=session,proto3" json:"session"`
	AppId                string   `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id"`
	OpenId               string   `protobuf:"bytes,3,opt,name=open_id,json=openId,proto3" json:"open_id"`
	RoomId               string   `protobuf:"bytes,4,opt,name=room_id,json=roomId,proto3" json:"room_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SessionCheckReq) Reset()         { *m = SessionCheckReq{} }
func (m *SessionCheckReq) String() string { return proto.CompactTextString(m) }
func (*SessionCheckReq) ProtoMessage()    {}
func (*SessionCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_http_70f7bf8120c97b14, []int{0}
}
func (m *SessionCheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SessionCheckReq.Unmarshal(m, b)
}
func (m *SessionCheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SessionCheckReq.Marshal(b, m, deterministic)
}
func (dst *SessionCheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SessionCheckReq.Merge(dst, src)
}
func (m *SessionCheckReq) XXX_Size() int {
	return xxx_messageInfo_SessionCheckReq.Size(m)
}
func (m *SessionCheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SessionCheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_SessionCheckReq proto.InternalMessageInfo

func (m *SessionCheckReq) GetSession() string {
	if m != nil {
		return m.Session
	}
	return ""
}

func (m *SessionCheckReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *SessionCheckReq) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *SessionCheckReq) GetRoomId() string {
	if m != nil {
		return m.RoomId
	}
	return ""
}

type SessionCheckResp struct {
	CheckResult          bool     `protobuf:"varint,1,opt,name=check_result,json=checkResult,proto3" json:"check_result"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SessionCheckResp) Reset()         { *m = SessionCheckResp{} }
func (m *SessionCheckResp) String() string { return proto.CompactTextString(m) }
func (*SessionCheckResp) ProtoMessage()    {}
func (*SessionCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_http_70f7bf8120c97b14, []int{1}
}
func (m *SessionCheckResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SessionCheckResp.Unmarshal(m, b)
}
func (m *SessionCheckResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SessionCheckResp.Marshal(b, m, deterministic)
}
func (dst *SessionCheckResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SessionCheckResp.Merge(dst, src)
}
func (m *SessionCheckResp) XXX_Size() int {
	return xxx_messageInfo_SessionCheckResp.Size(m)
}
func (m *SessionCheckResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SessionCheckResp.DiscardUnknown(m)
}

var xxx_messageInfo_SessionCheckResp proto.InternalMessageInfo

func (m *SessionCheckResp) GetCheckResult() bool {
	if m != nil {
		return m.CheckResult
	}
	return false
}

// deprecated
type GetAccessTokenReq struct {
	AppId                string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id"`
	Secret               string   `protobuf:"bytes,2,opt,name=secret,proto3" json:"secret"` // Deprecated: Do not use.
	OpenId               string   `protobuf:"bytes,3,opt,name=open_id,json=openId,proto3" json:"open_id"`
	JsCode               string   `protobuf:"bytes,4,opt,name=js_code,json=jsCode,proto3" json:"js_code"`
	RoomId               string   `protobuf:"bytes,5,opt,name=room_id,json=roomId,proto3" json:"room_id"`
	AppSecret            string   `protobuf:"bytes,6,opt,name=app_secret,json=appSecret,proto3" json:"app_secret"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAccessTokenReq) Reset()         { *m = GetAccessTokenReq{} }
func (m *GetAccessTokenReq) String() string { return proto.CompactTextString(m) }
func (*GetAccessTokenReq) ProtoMessage()    {}
func (*GetAccessTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_http_70f7bf8120c97b14, []int{2}
}
func (m *GetAccessTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAccessTokenReq.Unmarshal(m, b)
}
func (m *GetAccessTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAccessTokenReq.Marshal(b, m, deterministic)
}
func (dst *GetAccessTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAccessTokenReq.Merge(dst, src)
}
func (m *GetAccessTokenReq) XXX_Size() int {
	return xxx_messageInfo_GetAccessTokenReq.Size(m)
}
func (m *GetAccessTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAccessTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAccessTokenReq proto.InternalMessageInfo

func (m *GetAccessTokenReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

// Deprecated: Do not use.
func (m *GetAccessTokenReq) GetSecret() string {
	if m != nil {
		return m.Secret
	}
	return ""
}

func (m *GetAccessTokenReq) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *GetAccessTokenReq) GetJsCode() string {
	if m != nil {
		return m.JsCode
	}
	return ""
}

func (m *GetAccessTokenReq) GetRoomId() string {
	if m != nil {
		return m.RoomId
	}
	return ""
}

func (m *GetAccessTokenReq) GetAppSecret() string {
	if m != nil {
		return m.AppSecret
	}
	return ""
}

type GetAccessTokenResp struct {
	Session              string   `protobuf:"bytes,1,opt,name=session,proto3" json:"session"`
	SessionExpired       uint32   `protobuf:"varint,2,opt,name=session_expired,json=sessionExpired,proto3" json:"session_expired"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAccessTokenResp) Reset()         { *m = GetAccessTokenResp{} }
func (m *GetAccessTokenResp) String() string { return proto.CompactTextString(m) }
func (*GetAccessTokenResp) ProtoMessage()    {}
func (*GetAccessTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_http_70f7bf8120c97b14, []int{3}
}
func (m *GetAccessTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAccessTokenResp.Unmarshal(m, b)
}
func (m *GetAccessTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAccessTokenResp.Marshal(b, m, deterministic)
}
func (dst *GetAccessTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAccessTokenResp.Merge(dst, src)
}
func (m *GetAccessTokenResp) XXX_Size() int {
	return xxx_messageInfo_GetAccessTokenResp.Size(m)
}
func (m *GetAccessTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAccessTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAccessTokenResp proto.InternalMessageInfo

func (m *GetAccessTokenResp) GetSession() string {
	if m != nil {
		return m.Session
	}
	return ""
}

func (m *GetAccessTokenResp) GetSessionExpired() uint32 {
	if m != nil {
		return m.SessionExpired
	}
	return 0
}

type SessionRenewReq struct {
	Session              string   `protobuf:"bytes,1,opt,name=session,proto3" json:"session"`
	Appid                string   `protobuf:"bytes,2,opt,name=appid,proto3" json:"appid"`
	OpenId               string   `protobuf:"bytes,3,opt,name=open_id,json=openId,proto3" json:"open_id"`
	RoomId               string   `protobuf:"bytes,4,opt,name=room_id,json=roomId,proto3" json:"room_id"`
	AppSecret            string   `protobuf:"bytes,5,opt,name=app_secret,json=appSecret,proto3" json:"app_secret"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SessionRenewReq) Reset()         { *m = SessionRenewReq{} }
func (m *SessionRenewReq) String() string { return proto.CompactTextString(m) }
func (*SessionRenewReq) ProtoMessage()    {}
func (*SessionRenewReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_http_70f7bf8120c97b14, []int{4}
}
func (m *SessionRenewReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SessionRenewReq.Unmarshal(m, b)
}
func (m *SessionRenewReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SessionRenewReq.Marshal(b, m, deterministic)
}
func (dst *SessionRenewReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SessionRenewReq.Merge(dst, src)
}
func (m *SessionRenewReq) XXX_Size() int {
	return xxx_messageInfo_SessionRenewReq.Size(m)
}
func (m *SessionRenewReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SessionRenewReq.DiscardUnknown(m)
}

var xxx_messageInfo_SessionRenewReq proto.InternalMessageInfo

func (m *SessionRenewReq) GetSession() string {
	if m != nil {
		return m.Session
	}
	return ""
}

func (m *SessionRenewReq) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *SessionRenewReq) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *SessionRenewReq) GetRoomId() string {
	if m != nil {
		return m.RoomId
	}
	return ""
}

func (m *SessionRenewReq) GetAppSecret() string {
	if m != nil {
		return m.AppSecret
	}
	return ""
}

type SessionRenewResp struct {
	Session              string   `protobuf:"bytes,1,opt,name=session,proto3" json:"session"`
	SessionExpired       uint32   `protobuf:"varint,2,opt,name=session_expired,json=sessionExpired,proto3" json:"session_expired"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SessionRenewResp) Reset()         { *m = SessionRenewResp{} }
func (m *SessionRenewResp) String() string { return proto.CompactTextString(m) }
func (*SessionRenewResp) ProtoMessage()    {}
func (*SessionRenewResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_http_70f7bf8120c97b14, []int{5}
}
func (m *SessionRenewResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SessionRenewResp.Unmarshal(m, b)
}
func (m *SessionRenewResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SessionRenewResp.Marshal(b, m, deterministic)
}
func (dst *SessionRenewResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SessionRenewResp.Merge(dst, src)
}
func (m *SessionRenewResp) XXX_Size() int {
	return xxx_messageInfo_SessionRenewResp.Size(m)
}
func (m *SessionRenewResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SessionRenewResp.DiscardUnknown(m)
}

var xxx_messageInfo_SessionRenewResp proto.InternalMessageInfo

func (m *SessionRenewResp) GetSession() string {
	if m != nil {
		return m.Session
	}
	return ""
}

func (m *SessionRenewResp) GetSessionExpired() uint32 {
	if m != nil {
		return m.SessionExpired
	}
	return 0
}

type BatchGetUserInfoReq struct {
	Session              string   `protobuf:"bytes,1,opt,name=session,proto3" json:"session"`
	Appid                string   `protobuf:"bytes,2,opt,name=appid,proto3" json:"appid"`
	OpenId               string   `protobuf:"bytes,3,opt,name=open_id,json=openId,proto3" json:"open_id"`
	RoomId               string   `protobuf:"bytes,4,opt,name=room_id,json=roomId,proto3" json:"room_id"`
	OpenIdList           []string `protobuf:"bytes,5,rep,name=open_id_list,json=openIdList,proto3" json:"open_id_list"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserInfoReq) Reset()         { *m = BatchGetUserInfoReq{} }
func (m *BatchGetUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserInfoReq) ProtoMessage()    {}
func (*BatchGetUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_http_70f7bf8120c97b14, []int{6}
}
func (m *BatchGetUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserInfoReq.Unmarshal(m, b)
}
func (m *BatchGetUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserInfoReq.Merge(dst, src)
}
func (m *BatchGetUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserInfoReq.Size(m)
}
func (m *BatchGetUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserInfoReq proto.InternalMessageInfo

func (m *BatchGetUserInfoReq) GetSession() string {
	if m != nil {
		return m.Session
	}
	return ""
}

func (m *BatchGetUserInfoReq) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *BatchGetUserInfoReq) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *BatchGetUserInfoReq) GetRoomId() string {
	if m != nil {
		return m.RoomId
	}
	return ""
}

func (m *BatchGetUserInfoReq) GetOpenIdList() []string {
	if m != nil {
		return m.OpenIdList
	}
	return nil
}

type UserInfo struct {
	Nickname             string   `protobuf:"bytes,1,opt,name=nickname,proto3" json:"nickname"`
	Gender               uint32   `protobuf:"varint,2,opt,name=gender,proto3" json:"gender"`
	Avatar               string   `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar"`
	PossessRoomList      []string `protobuf:"bytes,4,rep,name=possess_room_list,json=possessRoomList,proto3" json:"possess_room_list"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_http_70f7bf8120c97b14, []int{7}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(dst, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserInfo) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *UserInfo) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *UserInfo) GetPossessRoomList() []string {
	if m != nil {
		return m.PossessRoomList
	}
	return nil
}

type BatchGetUserInfoResp struct {
	UserInfos            map[string]*UserInfo `protobuf:"bytes,1,rep,name=user_infos,json=userInfos,proto3" json:"user_infos" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchGetUserInfoResp) Reset()         { *m = BatchGetUserInfoResp{} }
func (m *BatchGetUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserInfoResp) ProtoMessage()    {}
func (*BatchGetUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_http_70f7bf8120c97b14, []int{8}
}
func (m *BatchGetUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserInfoResp.Unmarshal(m, b)
}
func (m *BatchGetUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserInfoResp.Merge(dst, src)
}
func (m *BatchGetUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserInfoResp.Size(m)
}
func (m *BatchGetUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserInfoResp proto.InternalMessageInfo

func (m *BatchGetUserInfoResp) GetUserInfos() map[string]*UserInfo {
	if m != nil {
		return m.UserInfos
	}
	return nil
}

type BatchGetRoomInfoReq struct {
	Session              string   `protobuf:"bytes,1,opt,name=session,proto3" json:"session"`
	Appid                string   `protobuf:"bytes,2,opt,name=appid,proto3" json:"appid"`
	OpenId               string   `protobuf:"bytes,3,opt,name=open_id,json=openId,proto3" json:"open_id"`
	RoomId               string   `protobuf:"bytes,4,opt,name=room_id,json=roomId,proto3" json:"room_id"`
	RoomIdList           []string `protobuf:"bytes,5,rep,name=room_id_list,json=roomIdList,proto3" json:"room_id_list"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetRoomInfoReq) Reset()         { *m = BatchGetRoomInfoReq{} }
func (m *BatchGetRoomInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetRoomInfoReq) ProtoMessage()    {}
func (*BatchGetRoomInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_http_70f7bf8120c97b14, []int{9}
}
func (m *BatchGetRoomInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRoomInfoReq.Unmarshal(m, b)
}
func (m *BatchGetRoomInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRoomInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetRoomInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRoomInfoReq.Merge(dst, src)
}
func (m *BatchGetRoomInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetRoomInfoReq.Size(m)
}
func (m *BatchGetRoomInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRoomInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRoomInfoReq proto.InternalMessageInfo

func (m *BatchGetRoomInfoReq) GetSession() string {
	if m != nil {
		return m.Session
	}
	return ""
}

func (m *BatchGetRoomInfoReq) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *BatchGetRoomInfoReq) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *BatchGetRoomInfoReq) GetRoomId() string {
	if m != nil {
		return m.RoomId
	}
	return ""
}

func (m *BatchGetRoomInfoReq) GetRoomIdList() []string {
	if m != nil {
		return m.RoomIdList
	}
	return nil
}

type RoomInfo struct {
	RoomName             string   `protobuf:"bytes,1,opt,name=room_name,json=roomName,proto3" json:"room_name"`
	Avatar               string   `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoomInfo) Reset()         { *m = RoomInfo{} }
func (m *RoomInfo) String() string { return proto.CompactTextString(m) }
func (*RoomInfo) ProtoMessage()    {}
func (*RoomInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_http_70f7bf8120c97b14, []int{10}
}
func (m *RoomInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoomInfo.Unmarshal(m, b)
}
func (m *RoomInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoomInfo.Marshal(b, m, deterministic)
}
func (dst *RoomInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoomInfo.Merge(dst, src)
}
func (m *RoomInfo) XXX_Size() int {
	return xxx_messageInfo_RoomInfo.Size(m)
}
func (m *RoomInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RoomInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RoomInfo proto.InternalMessageInfo

func (m *RoomInfo) GetRoomName() string {
	if m != nil {
		return m.RoomName
	}
	return ""
}

func (m *RoomInfo) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

type BatchGetRoomInfoResp struct {
	RoomInfos            map[string]*RoomInfo `protobuf:"bytes,1,rep,name=room_infos,json=roomInfos,proto3" json:"room_infos" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchGetRoomInfoResp) Reset()         { *m = BatchGetRoomInfoResp{} }
func (m *BatchGetRoomInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetRoomInfoResp) ProtoMessage()    {}
func (*BatchGetRoomInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_http_70f7bf8120c97b14, []int{11}
}
func (m *BatchGetRoomInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRoomInfoResp.Unmarshal(m, b)
}
func (m *BatchGetRoomInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRoomInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetRoomInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRoomInfoResp.Merge(dst, src)
}
func (m *BatchGetRoomInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetRoomInfoResp.Size(m)
}
func (m *BatchGetRoomInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRoomInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRoomInfoResp proto.InternalMessageInfo

func (m *BatchGetRoomInfoResp) GetRoomInfos() map[string]*RoomInfo {
	if m != nil {
		return m.RoomInfos
	}
	return nil
}

func init() {
	proto.RegisterType((*SessionCheckReq)(nil), "channel_ext_game_http.SessionCheckReq")
	proto.RegisterType((*SessionCheckResp)(nil), "channel_ext_game_http.SessionCheckResp")
	proto.RegisterType((*GetAccessTokenReq)(nil), "channel_ext_game_http.GetAccessTokenReq")
	proto.RegisterType((*GetAccessTokenResp)(nil), "channel_ext_game_http.GetAccessTokenResp")
	proto.RegisterType((*SessionRenewReq)(nil), "channel_ext_game_http.SessionRenewReq")
	proto.RegisterType((*SessionRenewResp)(nil), "channel_ext_game_http.SessionRenewResp")
	proto.RegisterType((*BatchGetUserInfoReq)(nil), "channel_ext_game_http.BatchGetUserInfoReq")
	proto.RegisterType((*UserInfo)(nil), "channel_ext_game_http.UserInfo")
	proto.RegisterType((*BatchGetUserInfoResp)(nil), "channel_ext_game_http.BatchGetUserInfoResp")
	proto.RegisterMapType((map[string]*UserInfo)(nil), "channel_ext_game_http.BatchGetUserInfoResp.UserInfosEntry")
	proto.RegisterType((*BatchGetRoomInfoReq)(nil), "channel_ext_game_http.BatchGetRoomInfoReq")
	proto.RegisterType((*RoomInfo)(nil), "channel_ext_game_http.RoomInfo")
	proto.RegisterType((*BatchGetRoomInfoResp)(nil), "channel_ext_game_http.BatchGetRoomInfoResp")
	proto.RegisterMapType((map[string]*RoomInfo)(nil), "channel_ext_game_http.BatchGetRoomInfoResp.RoomInfosEntry")
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-ext-game-http-logic/channel-ext-game-http.proto", fileDescriptor_channel_ext_game_http_70f7bf8120c97b14)
}

var fileDescriptor_channel_ext_game_http_70f7bf8120c97b14 = []byte{
	// 612 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x55, 0xdf, 0x4a, 0x1b, 0x4f,
	0x18, 0x65, 0x13, 0x13, 0x93, 0x4f, 0x7f, 0xfe, 0x99, 0x9f, 0xd6, 0x60, 0x29, 0xb5, 0xb9, 0xa9,
	0x14, 0x12, 0xc1, 0x22, 0x14, 0x6f, 0x4a, 0x15, 0x2b, 0x42, 0xe9, 0xc5, 0x5a, 0x29, 0x2d, 0x94,
	0x65, 0x3b, 0xfb, 0x69, 0xd6, 0x6c, 0x66, 0xc6, 0x99, 0x59, 0xab, 0xb7, 0x7d, 0x84, 0xde, 0xf5,
	0x2d, 0xfa, 0x28, 0x7d, 0xa4, 0x32, 0x7f, 0x36, 0xee, 0xda, 0x34, 0x50, 0x29, 0xde, 0xed, 0xf9,
	0xce, 0xec, 0x9e, 0x73, 0xbe, 0x03, 0x3b, 0xf0, 0x5a, 0xeb, 0xad, 0x8b, 0x3c, 0xa5, 0x43, 0x95,
	0x66, 0x97, 0x28, 0xb7, 0xe8, 0x20, 0x66, 0x0c, 0xb3, 0x1e, 0x5e, 0xe9, 0xde, 0x59, 0x3c, 0xc2,
	0xde, 0x40, 0x6b, 0xd1, 0xcb, 0xf8, 0x59, 0x4a, 0x27, 0x73, 0x7d, 0x21, 0xb9, 0xe6, 0x64, 0xd5,
	0x93, 0x11, 0x5e, 0xe9, 0xc8, 0x90, 0x91, 0x21, 0xbb, 0x1a, 0x16, 0x8f, 0x51, 0xa9, 0x94, 0xb3,
	0xfd, 0x01, 0xd2, 0x61, 0x88, 0x17, 0xa4, 0x03, 0xb3, 0xca, 0x8d, 0x3a, 0xc1, 0x46, 0xb0, 0xd9,
	0x0e, 0x0b, 0x48, 0x56, 0xa1, 0x19, 0x0b, 0x11, 0xa5, 0x49, 0xa7, 0x66, 0x89, 0x46, 0x2c, 0xc4,
	0x51, 0x42, 0xd6, 0x60, 0x96, 0x0b, 0x64, 0x66, 0x5e, 0xb7, 0xf3, 0xa6, 0x81, 0x8e, 0x90, 0x9c,
	0x8f, 0x0c, 0x31, 0xe3, 0x08, 0x03, 0x8f, 0x92, 0xee, 0x0e, 0x2c, 0x55, 0x55, 0x95, 0x20, 0x4f,
	0x60, 0x9e, 0x1a, 0x10, 0x49, 0x54, 0x79, 0xa6, 0xad, 0x76, 0x2b, 0x9c, 0xa3, 0xfe, 0x40, 0x9e,
	0xe9, 0xee, 0x8f, 0x00, 0x96, 0x0f, 0x51, 0xbf, 0xa2, 0x14, 0x95, 0x7a, 0xc7, 0x87, 0xc8, 0x8c,
	0xdf, 0x1b, 0x57, 0x41, 0xd9, 0xd5, 0x3a, 0x34, 0x15, 0x52, 0x89, 0xda, 0x99, 0xdd, 0xab, 0x75,
	0x82, 0xd0, 0x4f, 0xa6, 0x3a, 0x3e, 0x57, 0x11, 0xe5, 0x09, 0x16, 0x8e, 0xcf, 0xd5, 0x3e, 0x4f,
	0xb0, 0x1c, 0xa5, 0x51, 0x8e, 0x42, 0x1e, 0x01, 0x18, 0x75, 0x2f, 0xd5, 0xb4, 0x5c, 0x3b, 0x16,
	0xe2, 0xd8, 0x0e, 0xba, 0xef, 0x81, 0xdc, 0x76, 0xac, 0xc4, 0x94, 0x15, 0x3f, 0x85, 0x45, 0xff,
	0x18, 0xe1, 0x95, 0x48, 0x25, 0xba, 0x5d, 0xff, 0x17, 0x2e, 0xf8, 0xf1, 0x81, 0x9b, 0x76, 0xbf,
	0x05, 0xe3, 0xe6, 0x42, 0x64, 0xf8, 0x65, 0x7a, 0x73, 0x2b, 0x60, 0xb6, 0x52, 0x29, 0x2e, 0xbd,
	0x43, 0x71, 0xb7, 0xd2, 0x36, 0x6e, 0xa7, 0x3d, 0x19, 0xf7, 0xea, 0x3d, 0xfd, 0x9b, 0xac, 0xdf,
	0x03, 0xf8, 0x7f, 0x2f, 0xd6, 0x74, 0x70, 0x88, 0xfa, 0x44, 0xa1, 0x3c, 0x62, 0xa7, 0xfc, 0x7e,
	0xf2, 0x6e, 0xc0, 0xbc, 0x7f, 0x23, 0xca, 0x52, 0x65, 0x12, 0xd7, 0x37, 0xdb, 0x21, 0xb8, 0xd7,
	0xde, 0xa4, 0x4a, 0x77, 0xbf, 0x06, 0xd0, 0x2a, 0x3c, 0x91, 0x75, 0x68, 0xb1, 0x94, 0x0e, 0x59,
	0x3c, 0x42, 0xef, 0x68, 0x8c, 0xc9, 0x03, 0x68, 0x9e, 0x21, 0x4b, 0x50, 0xfa, 0x90, 0x1e, 0x99,
	0x79, 0x7c, 0x19, 0xeb, 0x58, 0x16, 0x9e, 0x1c, 0x22, 0xcf, 0x60, 0x59, 0x70, 0x65, 0x02, 0x45,
	0xd6, 0x9b, 0xd5, 0x9f, 0xb1, 0xfa, 0x8b, 0x9e, 0x08, 0x39, 0x1f, 0x59, 0x13, 0x3f, 0x03, 0x58,
	0xf9, 0x7d, 0x41, 0x4a, 0x90, 0x0f, 0x00, 0xb9, 0x42, 0x19, 0xa5, 0xec, 0x94, 0xab, 0x4e, 0xb0,
	0x51, 0xdf, 0x9c, 0xdb, 0xde, 0xed, 0x4f, 0xfc, 0x15, 0xf4, 0x27, 0x7d, 0xa0, 0x5f, 0x00, 0x75,
	0xc0, 0xb4, 0xbc, 0x0e, 0xdb, 0x79, 0x81, 0xd7, 0x3f, 0xc1, 0x42, 0x95, 0x24, 0x4b, 0x50, 0x1f,
	0xe2, 0xb5, 0x0f, 0x6e, 0x1e, 0xc9, 0x0e, 0x34, 0x2e, 0xe3, 0x2c, 0x47, 0x1b, 0x79, 0x6e, 0xfb,
	0xf1, 0x1f, 0x94, 0xc7, 0x8a, 0xee, 0xf4, 0x6e, 0xed, 0x45, 0x50, 0xe9, 0xdc, 0xe4, 0xbc, 0xd7,
	0xce, 0x3d, 0x51, 0xe9, 0xdc, 0xb1, 0x76, 0xdd, 0x2f, 0xa1, 0x55, 0x58, 0x22, 0x0f, 0xa1, 0x6d,
	0x4f, 0x97, 0x3b, 0x37, 0x83, 0xb7, 0xbe, 0x73, 0xdf, 0x6d, 0xad, 0xdc, 0x6d, 0xa5, 0xaf, 0x9b,
	0x70, 0xae, 0x2f, 0xa7, 0xfd, 0x17, 0x7d, 0x95, 0x3f, 0xd0, 0x2f, 0x40, 0xd1, 0x97, 0x2c, 0xb0,
	0xe9, 0xab, 0x4a, 0xde, 0xbd, 0xaf, 0xb1, 0xe2, 0x4d, 0x5f, 0x7b, 0x6b, 0x1f, 0x57, 0x27, 0x5e,
	0x3f, 0x9f, 0x9b, 0xf6, 0xfe, 0x79, 0xfe, 0x2b, 0x00, 0x00, 0xff, 0xff, 0xcb, 0x83, 0x73, 0x5f,
	0xc9, 0x06, 0x00, 0x00,
}
