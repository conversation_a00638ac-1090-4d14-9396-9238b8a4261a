package store

import (
	"context"
	"fmt"
	"github.com/go-sql-driver/mysql"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
)

const PropMonthLogTblNum = 10

func GenPropMonthLogTbl(uid uint32, t time.Time) string {
	return fmt.Sprintf("smash_egg_prop_month_log_%04d%02d_%d", t.Year(), t.Month(), uid%PropMonthLogTblNum)
}

const CreatePropMonthLogTbl = `CREATE TABLE %s (
    id int(10) unsigned NOT NULL AUTO_INCREMENT,
    order_id varchar(512) NOT NULL DEFAULT '' COMMENT '订单号',
    out_order_id varchar(512) NOT NULL DEFAULT '' COMMENT '外部订单号',
    uid int(10) unsigned NOT NULL DEFAULT 0,
    prop_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '道具id',
    expire_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '过期时间',
    change_num int(10) NOT NULL DEFAULT 0 COMMENT '变化数量',
    final_num int(10) unsigned NOT NULL DEFAULT 0 COMMENT '余额',
    outside_time timestamp NOT NULL default CURRENT_TIMESTAMP COMMENT '外部时间',
    ctime timestamp NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    mtime timestamp NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',

    primary key (id),
    unique key idx_unique(order_id),
    index idx_out_order_id(out_order_id),
    index idx_outside_time(outside_time),
    index idx_uid_prop_expire(uid, prop_id, expire_time)
)engine=InnoDB default charset=utf8 COMMENT "道具流水记录表";`

type PropLog struct {
	Id          uint32    `db:"id"`
	OrderId     string    `db:"order_id"`
	OutOrderId  string    `db:"out_order_id"`
	Uid         uint32    `db:"uid"`
	PropId      uint32    `db:"prop_id"`
	ExpireTime  time.Time `db:"expire_time"`
	ChangeNum   int32     `db:"change_num"`
	FinalNum    uint32    `db:"final_num"`
	OutsideTime time.Time `db:"outside_time"`
	CTime       time.Time `db:"ctime"`
	MTime       time.Time `db:"mtime"`
}

func (s *Store) CreatPropMonthLogTbl(ctx context.Context, tx *sqlx.Tx, id uint32, t time.Time) error {
	var err error
	createSql := fmt.Sprintf(CreatePropMonthLogTbl, GenPropMonthLogTbl(id, t))

	if tx != nil {
		_, err = tx.ExecContext(ctx, createSql)
	} else {
		_, err = s.db.ExecContext(ctx, createSql)
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "CreatPropMonthLogTbl fail. err:%v", err)
	}
	return err
}

func (s *Store) BatInsertPropLog(ctx context.Context, tx *sqlx.Tx, uid uint32, outsideTime time.Time, list []*PropLog) error {
	if len(list) == 0 {
		return nil
	}

	placeholder := make([]string, 0, len(list))
	params := make([]interface{}, 0)
	for _, r := range list {
		placeholder = append(placeholder, "(?,?,?,?,?,?,?,?)")
		params = append(params, r.OrderId, r.OutOrderId, r.Uid, r.PropId, r.ExpireTime, r.ChangeNum, r.FinalNum, r.OutsideTime)
	}

	tbl := GenPropMonthLogTbl(uid, outsideTime)
	query := fmt.Sprintf("INSERT INTO %s(order_id, out_order_id, uid, prop_id, expire_time, change_num, final_num, outside_time) VALUES %s", tbl, strings.Join(placeholder, ","))
	_, err := tx.ExecContext(ctx, query, params...)
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			// 表不存在，建表
			err = s.CreatPropMonthLogTbl(ctx, tx, uid, outsideTime)
			if err != nil {
				log.ErrorWithCtx(ctx, "BatInsertPropLog fail to CreatPropMonthLogTbl. uid:%d, err:%v", uid, err)
				return err
			}

			_, err = tx.ExecContext(ctx, query, params...)
			if err != nil {
				log.ErrorWithCtx(ctx, "BatInsertPropLog fail to ExecContext. %+v, err:%v", list, err)
				return err
			}

			return nil
		}

		log.ErrorWithCtx(ctx, "BatInsertPropLog fail to ExecContext. %+v, err:%v", list, err)
		return err
	}

	return nil
}

// GetUserPropUseNum 获取用户指定时间段指定道具的使用数量
func (s *Store) GetUserPropUseNum(ctx context.Context, uid, propId uint32, startTime, endTime time.Time) (uint32, error) {
	tbl := GenPropMonthLogTbl(uid, endTime)
	query := fmt.Sprintf("SELECT ifnull(sum(change_num),0) FROM %s WHERE uid=? AND prop_id=? AND outside_time>=? AND outside_time<? AND change_num<0 ", tbl)
	var num int32
	err := s.db.GetContext(ctx, &num, query, uid, propId, startTime, endTime)
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetUserPropUseNum fail. uid:%d, propId:%d, startTime:%v, endTime:%v, err:%v", uid, propId, startTime, endTime, err)
		return 0, err
	}

	return uint32(-num), nil
}
