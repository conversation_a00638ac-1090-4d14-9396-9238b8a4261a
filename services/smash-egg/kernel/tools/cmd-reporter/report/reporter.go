package report

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-redis/redis"
	"github.com/jmoiron/sqlx"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/smash-egg/kernel/utils/common"
	"golang.52tt.com/services/smash-egg/kernel/utils/reporter"
	"sort"
	"sync"
	"time"
)

const (
	timeLayoutEN = "2006-01-02 15:04:05"
	timeLayoutCN = "2006年01月02日 15:04:05"

	layoutHourly = "2006-01-02/15"
	layoutDaily  = "2006-01-02"

	layoutMonthly = "2006-01"

	reportKindHourly = "hourly"
	reportKindDaily  = "daily"

	reportKindMorph     = "morph"
	reportKindMorphOver = "morph_over"

	reportKindDeficitLimit       = "deficit_limit"
	reportKindDeficitWarning     = "deficit_warning"
	reportKindDeficitWarningOver = "deficit_warning_over"
)

type Setting struct {
	Status int `json:"status"`

	DeficitLimit   uint32 `json:"deficit_limit"`
	DeficitWarning uint32 `json:"deficit_warning"`

	MorphDuration uint32 `json:"morph_duration"`
}

func (s *Setting) Disable() bool {
	return s.Status == 0
}

func (s *Setting) Risky() bool {
	return s.Status == 3
}

type Reporter struct {
	store *Store
	cache *Cache

	stop chan interface{}
	wg   sync.WaitGroup

	setting Setting

	reportTarget string
	reportSecret string
}

func NewReporter(redisClient *redis.Client, dbClient *sqlx.DB, reportTarget, reportSecret string) *Reporter {
	s := &Reporter{
		store: &Store{
			db: dbClient,
		},
		cache: NewCache(redisClient),
		stop:  make(chan interface{}),

		reportTarget: reportTarget,
		reportSecret: reportSecret,
	}
	return s
}

func (s *Reporter) Start() error {
	// 初始化配置
	err := s.loadSetting(&s.setting)
	if err != nil {
		log.Errorf("loadSetting fail: %v", err)
		return err
	}

	// 启动扫描
	go s.maintain()
	return nil
}

func (s *Reporter) Stop() {
	close(s.stop)
	s.wg.Wait()
}

func (s *Reporter) loadSetting(setting interface{}) error {
	data, err := s.store.GetConfig(context.Background(), "system_config")
	if err != nil {
		log.Errorf("db GetConfig fail: %v")
		return err
	}

	err = json.Unmarshal([]byte(data), setting)
	if err != nil {
		log.Errorf("Unmarshal config fail : %v", err)
		return err
	}
	return nil
}

func (s *Reporter) maintain() {
	s.wg.Add(4)

	// 变身状态检测
	go func() {
		for {
			select {
			case <-s.stop:
				s.wg.Done()
				return
			case <-time.After(time.Second * 3):
				if s.setting.Disable() {
					continue
				}

				morph, end, err := s.cache.GetMorphing()
				if err != nil {
					log.Errorf("get morphing failed: %v", err)
					continue
				}
				if morph {
					_, _ = s.reportMorph(end)
				} else {
					_, _ = s.reportMorphOver(time.Now())
				}
			}
		}
	}()

	// 数据统计上报
	go func() {
		for {
			select {
			case <-s.stop:
				s.wg.Done()
				return
			case <-time.After(time.Minute):
				if s.setting.Disable() {
					continue
				}

				now := time.Now()
				if now.Minute() < 5 {
					continue
				}
				_, _ = s.reportHourlyStatistics(now, 0)
				_, _ = s.reportDailyStatistics(now, 0)
			}
		}
	}()

	// 亏损统计上报
	go func() {
		for {
			select {
			case <-s.stop:
				s.wg.Done()
				return
			case <-time.After(time.Second * 5):
				if s.setting.Disable() {
					continue
				}

				now := time.Now()
				profit, err := s.cache.GetProfit()
				if err != nil {
					log.Errorf("get profit failed, err: %v", err)
					continue
				}

				if 0 != s.setting.DeficitLimit && int64(s.setting.DeficitLimit)+profit < 0 && s.setting.Risky() {
					_, _ = s.reportDeficitLimit(now)
				} else if 0 != s.setting.DeficitWarning && int64(s.setting.DeficitWarning)+profit < 0 {
					_, _ = s.reportDeficitWarning(now, profit)
				} else {
					_, _ = s.reportDeficitWarningOver(now)
				}
			}
		}
	}()

	// 配置变更上报
	go func() {
		lastUpdates := make(map[string]time.Time)

		for {
			select {
			case <-s.stop:
				s.wg.Done()
				return
			case <-time.After(time.Second * 5):
				//now := time.Now()
				updates, err := s.store.GetAllConfigUpdateTime(context.Background())
				if err != nil {
					log.Errorf("get profit failed, err: %v", err)
					continue
				}

				for k, v := range updates {
					if t, ok := lastUpdates[k]; !ok {
						lastUpdates[k] = v
					} else if v.Unix() != t.Unix() {
						var ok bool

						switch k {
						case "prizes_config_0":
							ok = s.reportPrizeUpdate(0)
						case "prizes_config_1":
							ok = s.reportPrizeUpdate(1)
						case "system_config":
							ok = s.reportConfigUpdate()
						}

						if ok {
							lastUpdates[k] = v
						}
					}
				}
			}
		}
	}()
}

func (s *Reporter) reportConfigUpdate() bool {

	type fullSetting struct {
		Status int `json:"status"`

		DeficitLimit   uint32 `json:"deficit_limit"`
		DeficitWarning uint32 `json:"deficit_warning"`

		MorphHits     uint32 `json:"morph_hits"`
		MorphDuration uint32 `json:"morph_duration"`

		SpeedUp   uint32  `json:"speed_up"`
		SpeedStep uint32  `json:"speed_step"`
		MaxSpeed  float32 `json:"max_speed"`

		DailyLimit uint32 `json:"daily_limit"`

		WealthLimit uint64 `json:"wealth_limit"`
		CharmLimit  uint64 `json:"charm_limit"`
		LevelLimit  uint32 `json:"level_limit"`

		MaxRandN uint32 `json:"max_rand_n"`

		OverallSpeed uint32 `json:"overall_speed"`
	}

	var setting fullSetting
	err := s.loadSetting(&setting)
	if err != nil {
		return false
	}

	s.setting.Status = setting.Status
	s.setting.MorphDuration = setting.MorphDuration
	s.setting.DeficitLimit = setting.DeficitLimit
	s.setting.DeficitWarning = setting.DeficitWarning

	var title, text, stat string
	switch setting.Status {
	case 0:
		stat = "禁用"
	case 1:
		stat = "启用"
	case 2:
		stat = "异常"
	case 3:
		stat = "测试"
	}

	title = "配置更新"
	text = fmt.Sprintf("%s\n服务状态 %s\n变身击打次数 %d 次\n变身持续时间 %d 秒\n亏损警告值 %d 元\n亏损限制值 %d 元\n财富值不低于 %d 或 魅力值不低于 %d 或 等级不低于 %d\n\n变身加速基数 %d\n变身加速条件 %d\n加速度最大值 %.2f\nN值 %d\n最大幸运值 %d\n",
		time.Now().Format(timeLayoutCN), stat, setting.MorphHits, setting.MorphDuration, setting.DeficitWarning/100, setting.DeficitLimit/100, setting.WealthLimit, setting.CharmLimit, setting.LevelLimit, setting.SpeedStep, setting.SpeedUp, setting.MaxSpeed, setting.MaxRandN, setting.OverallSpeed)

	log.Infof("report: %s >> %s", title, text)

	err = reporter.NewFeishuReporter(s.reportTarget, s.reportSecret).SendTexts(title, text)
	if nil != err {
		log.Errorf("SendReport failed: %+v", err)
	}
	return true
}

func (s *Reporter) reportPrizeUpdate(index int) bool {
	var text string
	if 0 == index {
		text = fmt.Sprintf("%s\n普通奖池已更新\n", time.Now().Format(timeLayoutCN))
	} else {
		text = fmt.Sprintf("%s\n变身奖池已更新\n", time.Now().Format(timeLayoutCN))
	}

	title := "配置更新"

	log.Infof("report: %s >> %s", title, text)

	err := reporter.NewFeishuReporter(s.reportTarget, s.reportSecret).SendTexts(title, text)
	if nil != err {
		log.Errorf("SendReport failed: %+v", err)
	}
	return true
}

func (s *Reporter) reportMorph(end time.Time) (reported bool, err error) {
	return s.report(reportKindMorph, end, 0, time.Minute, "", func(last time.Time) (title string, text string, err error) {

		log.Infof("start to report morph at %s", end.Format(timeLayoutEN))

		var records []MorphData
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		records, err = s.store.GetMorphRecord(ctx, common.TheBeginningOfDay(end), common.TheEndingOfDay(end))
		cancel()
		if err != nil {
			log.Errorf("InsertMorphRecord failed:%v", err)
			return
		}

		sort.Slice(records, func(i, j int) bool {
			return records[i].Time.Before(records[j].Time)
		})

		var count int
		if 0 == len(records) {
			count++
		} else {
			count = len(records)

			x := end.Unix()
			y := records[len(records)-1].Time.Unix()
			z := int64(records[len(records)-1].Duration)

			fmt.Println(x, y, z)

			if records[len(records)-1].Time.Unix()+int64(records[len(records)-1].Duration) != end.Unix() {
				count++
			}
		}

		dur, err := time.ParseDuration(fmt.Sprintf("-%ds", s.setting.MorphDuration))
		if err != nil {
			log.Errorf("ParseDuration failed:%v", err)
			return
		}
		begin := end.Add(dur)

		//全局加锁
		title = "变身通知"
		text = fmt.Sprintf("%s\n魔力转转开始今天第 %d 次变身\n", begin.Format(timeLayoutCN), count)
		return
	})
}

func (s *Reporter) reportMorphOver(now time.Time) (reported bool, err error) {
	return s.reportAfter(reportKindMorph, reportKindMorphOver, time.Minute, time.Minute, "", func(last time.Time) (title string, text string, err error) {

		log.Infof("start to report morph over at %s", now.Format(timeLayoutEN))

		//获取变身时间
		var begin, end time.Time
		{
			ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
			defer cancel()
			var records []MorphData
			records, err = s.store.GetMorphRecord(ctx, common.TheBeginningOfDay(now), common.TheEndingOfDay(now))
			if err != nil {
				log.Errorf("InsertMorphRecord failed:%v", err)
				return
			}
			if 0 == len(records) {
				err = errors.New("not found")
				log.Errorf("no morph records")
				return
			}

			begin = records[len(records)-1].Time
			end = begin.Add(time.Duration(records[len(records)-1].Duration) * time.Second)

			//调整结束时间
			end = end.Add(time.Minute)
		}

		var data StatisticsData
		{
			log.Infof("MorphOver from %s to %s", begin.Format(timeLayoutEN), end.Format(timeLayoutEN))

			if data, err = s.store.GetPlayData(begin, end); nil != err {
				log.Errorf("GetMorphData fail: %v", err)
				return
			}
		}

		title = "变身结束提醒"
		text = fmt.Sprintf("%s\n变身结束，上次变身期间转转次数 %d 次\n变身期间利润 %.2f 元\n送出5000元大奖 %d 个\n",
			now.Format(timeLayoutCN), data.PlayInMorphing, data.ProfitInMorphing(), data.ExpensiveGift)
		return
	})
}

func (s *Reporter) reportDeficitWarning(now time.Time, profit int64) (reported bool, err error) {
	return s.report(reportKindDeficitWarning, now, time.Minute*5, time.Minute, "", func(last time.Time) (title string, text string, err error) {
		log.Infof("start to report deficit at %s", now.Format(timeLayoutEN))

		title = "亏损告警"
		text = fmt.Sprintf("%s\n本小时已亏损 %.2f 元\n", now.Format(timeLayoutCN), float64(0-profit)/100)
		return
	})
}

func (s *Reporter) reportDeficitWarningOver(now time.Time) (reported bool, err error) {
	return s.reportAfter(reportKindDeficitWarning, reportKindDeficitWarningOver, time.Minute*5, time.Minute, "", func(last time.Time) (title string, text string, err error) {
		log.Infof("start to report deficit over at %s", now.Format(timeLayoutEN))

		title = "亏损告警解除"
		text = fmt.Sprintf("%s\n亏损已低于告警值，恢复正常\n", now.Format(timeLayoutCN))
		return
	})
}

func (s *Reporter) reportDeficitLimit(now time.Time) (reported bool, err error) {
	return s.report(reportKindDeficitLimit, now, time.Minute*5, time.Minute, layoutHourly, func(last time.Time) (title string, text string, err error) {
		log.Infof("start to report deficit limit at %s", now.Format(timeLayoutEN))

		title = "异常通知"
		text = fmt.Sprintf("%s\n亏损已达限制值， 服务已无法继续使用\n", now.Format(timeLayoutCN))
		return
	})
}

func (s *Reporter) reportHourlyStatistics(now time.Time, interval time.Duration) (reported bool, err error) {
	var timeLayout string
	if 0 == interval {
		timeLayout = layoutHourly
	}

	//return s.report(reportKindHourly, now, time.Minute, time.Minute, "", func(last time.Time) (title string, text string, err error) {
	return s.report(reportKindHourly, now, interval, time.Minute*5, timeLayout, func(last time.Time) (title string, text string, err error) {

		var hourly, daily, monthly, totally, lastHourly, lastDayHourly StatisticsData

		d, _ := time.ParseDuration(fmt.Sprintf("%dh", -1))
		at := now.Add(d)

		log.Infof("start to count hourly data of %s", at.Format(layoutHourly))

		hBegin, hEnd := common.TheBeginningOfHour(at), common.TheEndingOfHour(at)
		{
			hourly, err = s.store.GetAllData(hBegin, hEnd)
			if err != nil {
				log.Errorf("GetAllData fail: %v", err)
				return
			}
		}

		{
			dBegin := common.TheBeginningOfDay(at)
			daily, err = s.store.GetAllData(dBegin, hEnd)
			if err != nil {
				log.Errorf("GetAllData fail: %v", err)
				return
			}
		}

		{
			lastHBegin, lastHEnd := common.TheHourAgo(hBegin, 1), common.TheHourAgo(hEnd, 1)
			lastHourly, err = s.store.GetAllData(lastHBegin, lastHEnd)
			if err != nil {
				log.Errorf("GetAllData fail: %v", err)
				return
			}
		}

		{
			lastDBegin, lastDEnd := common.TheDayAgo(hBegin, 1), common.TheDayAgo(hEnd, 1)
			lastDayHourly, err = s.store.GetAllData(lastDBegin, lastDEnd)
			if err != nil {
				log.Errorf("GetAllData fail: %v", err)
				return
			}
		}

		//获取本月数据
		{
			monthlyKey := "monthly_" + at.Format(layoutMonthly)
			err = s.cache.SafetyGetReportData(monthlyKey, &monthly)
			if err != nil {
				log.Errorf("SafetyGetReportData %s fail: %v", monthlyKey, err)
				return
			}
			monthly.Add(&daily)
		}

		//历史数据获取
		{
			err = s.cache.SafetyGetReportData("history", &totally)
			if err != nil {
				log.Errorf("SafetyGetReportData fail: %v", err)
				return
			}
			totally.Add(&daily)
		}

		title = "小时数据"
		text = (&HourlyReportV2{}).Make(at, hourly, daily, monthly, totally, lastHourly, lastDayHourly)
		return
	})
}

func (s *Reporter) reportDailyStatistics(now time.Time, interval time.Duration) (reported bool, err error) {
	var timeLayout string
	if 0 == interval {
		timeLayout = layoutDaily
	}

	//return s.report(reportKindDaily, now, time.Minute*3, time.Minute, "", func(last time.Time) (title string, text string, err error) {
	return s.report(reportKindDaily, now, interval, time.Minute*5, timeLayout, func(last time.Time) (title string, text string, err error) {

		var daily, monthly, totally, lastDaily, lastMonthDaily StatisticsData

		at := common.TheHourAgo(now, 24)

		log.Infof("start to count daily data of %s", at.Format(layoutDaily))

		dBegin, dEnd := common.TheBeginningOfDay(at), common.TheEndingOfDay(at)
		{
			daily, err = s.store.GetAllData(dBegin, dEnd)
			if err != nil {
				log.Errorf("GetAllData fail: %v", err)
				return
			}
		}

		{
			lastDBegin, lastDEnd := common.TheDayAgo(dBegin, 1), common.TheDayAgo(dEnd, 1)
			lastDaily, err = s.store.GetAllData(lastDBegin, lastDEnd)
			if err != nil {
				log.Errorf("GetAllData fail: %v", err)
				return
			}
		}

		{
			lastMBegin, lastMEnd := dBegin.AddDate(0, -1, 0), dEnd.AddDate(0, -1, 0)
			//5月31减去一个月为5月1
			if dBegin.Day() == lastMBegin.Day() && dEnd.Day() == lastMEnd.Day() {
				lastMonthDaily, err = s.store.GetAllData(lastMBegin, lastMEnd)
				if err != nil {
					log.Errorf("GetAllData fail: %v", err)
					return
				}
			}
		}

		//获取本月数据
		{
			monthlyKey := "monthly_" + at.Format(layoutMonthly)
			err = s.cache.SafetyGetReportData(monthlyKey, &monthly)
			if err != nil {
				log.Errorf("SafetyGetReportData %s fail: %v", monthlyKey, err)
				return
			}

			if monthly.When.Format(layoutDaily) != at.Format(layoutDaily) {
				monthly.When = at
				monthly.Add(&daily)
				_, err = s.cache.SafetySetReportData(monthlyKey, monthly)
				if err != nil {
					log.Errorf("SafetySetReportData fail: %v", err)
					return
				}
			}
		}

		//历史数据获取
		{
			err = s.cache.SafetyGetReportData("history", &totally)
			if err != nil {
				log.Errorf("SafetyGetReportData fail: %v", err)
				return
			}

			if totally.When.Format(layoutDaily) != at.Format(layoutDaily) {
				totally.When = at
				totally.Add(&daily)
				_, err = s.cache.SafetySetReportData("history", totally)
				if err != nil {
					log.Errorf("SafetySetReportData fail: %v", err)
					return
				}
			}
		}

		title = "昨日数据"
		text = (&DailyReportV2{}).Make(at, daily, monthly, totally, lastDaily, lastMonthDaily)
		return
	})
}

func (s *Reporter) report(kind string, when time.Time, interval, timeout time.Duration, timeLayout string, make func(last time.Time) (string, string, error)) (reported bool, err error) {

	//获取上次推送时间
	t, ok, err := s.cache.GetReported(kind)
	if nil != err {
		log.Errorf("GetReported failed:%+v", err)
		return
	}

	//存在推送
	if ok {
		//log.Debugf("check for report %s >> when:%s interval:%v timeout:%v timeLayout:%s last:%s",
		//	kind, when.Format(timeLayout), interval, timeout, timeLayout, t.Format(timeLayout))

		//推送时间与当前一致，则认为以及完成推送
		if when.Equal(t) {
			reported = true
			return
		}

		//推送未达到间隔时间，则返回
		if 0 != interval && t.Add(interval).After(when) {
			reported = true
			return
		}

		//相同的格式化时间不推送
		if 0 != len(timeLayout) && t.Format(timeLayout) == when.Format(timeLayout) {
			reported = true
			return
		}
	} else {
		//不存在推送，且推送存在间隔，则设置上次推送时间为当前时间
		if 0 != interval || 0 != len(timeLayout) {
			_, err = s.cache.SetReported(kind, when)
			reported = true
			return
		}
	}

	//全局加锁
	ok, err = s.cache.StartReport(kind, timeout)
	if nil != err {
		log.Debugf("StartReport fail: %v", err)
		return
	}

	if !ok {
		log.Debugf("StartReport fail: %v", ok)
		return
	}

	at := time.Now()

	//log.Debugf("report %s when %s at %s", kind, when.Format(timeLayoutEN), at.Format(timeLayoutEN))

	//生成报告
	var title, text string
	title, text, err = make(t)
	if nil != err {
		if time.Now().Add(time.Minute).Before(at.Add(timeout)) {
			_ = s.cache.StopReport(kind)
		}
		return
	}

	if 0 == len(title) || 0 == len(text) {
		return
	}

	//推送报告
	log.Infof("report: %s >> %s", title, text)

	err = reporter.NewFeishuReporter(s.reportTarget, s.reportSecret).SendTexts(title, text)
	if nil != err {
		log.Errorf("SendReport fail: %v", err)

		if time.Now().Add(time.Minute).Before(at.Add(timeout)) {
			_ = s.cache.StopReport(kind)
		}
		return
	}

	_, _ = s.cache.MuteV2Report(kind, when)

	//记录报告时间
	ok, err = s.cache.SetReported(kind, when)
	if ok {
		reported = true
	}

	if time.Now().Add(time.Minute).Before(at.Add(timeout)) {
		_ = s.cache.StopReport(kind)
	}
	return
}

func (s *Reporter) reportAfter(kind, after string, interval, timeout time.Duration, timeLayout string, make func(last time.Time) (string, string, error)) (reported bool, err error) {
	t, ok, err := s.cache.GetReported(kind)
	if nil != err {
		log.Errorf("GetReported failed:%+v", err)
		return
	}

	if !ok {
		reported = true
		return
	}

	if t.Add(interval).After(time.Now()) {
		return
	}

	return s.report(after, t, 0, timeout, timeLayout, make)
}
