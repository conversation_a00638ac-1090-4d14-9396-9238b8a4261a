package store

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/go-sql-driver/mysql"
	_ "github.com/go-sql-driver/mysql"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/smash-egg/kernel/entity"
	"sort"
	"strings"
	"time"
)

var CreateConsumeRecord = `
CREATE TABLE IF NOT EXISTS smash_egg_month_consume_record_%02d (
	id bigint unsigned NOT NULL AUTO_INCREMENT,
	uid int unsigned NOT NULL COMMENT 'uid',
	amount int unsigned NOT NULL,
	fee int unsigned NOT NULL,
	order_id varchar(100) NOT NULL,
	status int unsigned NOT NULL,
	create_time timestamp NOT NULL DEFAULT current_timestamp,
	operate_id varchar(64) NOT NULL,

	remain_chance int unsigned NOT NULL,

	PRIMARY KEY (id),
	KEY consume_user(uid),
	KEY consume_time(create_time),
	UNIQUE KEY operate_id(operate_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;`

func (s *Store) CreateConsumeRecordTable(month int) error {
	_, err := s.db.Exec(fmt.Sprintf(CreateConsumeRecord, month))
	if nil != err {
		return err
	}

	return nil
}

func (s *Store) InsertConsumeRecord(ctx context.Context, tx interface{}, tableSuffix string, infos []entity.ConsumeRecord) (err error) {
	if len(infos) == 0 {
		return nil
	}

	size := len(infos)
	allParams := make([]interface{}, 0)
	query := fmt.Sprintf(`INSERT INTO smash_egg_month_consume_record_%s (uid, amount, fee, order_id, operate_id, remain_chance, create_time, status) VALUES `, tableSuffix)
	for i, info := range infos {
		query += `(?, ?, ?, ?, ?, ?, FROM_UNIXTIME(?), ?)`
		if i != size-1 {
			query += `,`
		}
		log.Debugf("info:%+v, info.CreateTime.Unix():%v, info.CreateTime:%v", info, info.CreateTime.Unix(), info.CreateTime)
		params := toInterfaceArray(info.Uid, info.Amount, info.Fee, info.OrderId, info.OperateId, info.RemainChance, info.CreateTime.Unix(), 0)

		allParams = append(allParams, params...)
	}

	_, err = tx.(*sql.Tx).ExecContext(ctx, query, allParams...)
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertConsumeRecord db err:%s", err.Error())
		return err
	}
	return nil
}

func (s *Store) GetConsumeRecord(ctx context.Context, month int, uid uint32, orderId string, offset uint64, limit, beginTime, endTime uint32) ([]entity.ConsumeRecord, error) {
	params := make([]interface{}, 0)
	query := fmt.Sprintf(`SELECT id, uid, amount, fee, order_id, create_time FROM smash_egg_month_consume_record_%02d_%d`, month, uid%10)

	conditions := make([]string, 0)
	if uid != 0 {
		conditions = append(conditions, " uid = ? ")
		params = append(params, uid)
	}

	if len(orderId) != 0 {
		conditions = append(conditions, " order_id = ? ")
		params = append(params, orderId)
	}

	if 0 != offset {
		conditions = append(conditions, " id < ? ")
		params = append(params, offset)
	}

	if 0 != endTime {
		conditions = append(conditions, " create_time between FROM_UNIXTIME(?) AND FROM_UNIXTIME(?) ")
		params = append(params, beginTime, endTime)
	}

	if 0 != len(conditions) {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	query += " order by id desc "

	if 0 != limit {
		if limit > 100 {
			limit = 100
		}

		query += " limit ? "

		params = append(params, limit)
	}

	infos := make([]entity.ConsumeRecord, 0, limit)
	err := s.db.SelectContext(ctx, &infos, query, params...)
	if err != nil {
		if err == sql.ErrNoRows {
			return infos, nil
		} else {
			log.ErrorWithCtx(ctx, "GetWinningRecord db err: %s", err.Error())
			return nil, err
		}
	}

	var sorted entity.ConsumeRecords = infos
	sort.Sort(sorted)
	return sorted, nil
}

func (s *Store) GetConsumeRecordByStatus(ctx context.Context, month uint32, status, beginTime, endTime, limit uint32) ([]entity.ConsumeRecord, error) {
	res := make([]entity.ConsumeRecord, 0)
	for i := 0; i < 10; i++ {
		query := fmt.Sprintf(`SELECT uid, amount, fee, order_id, operate_id, create_time FROM smash_egg_month_consume_record_%02d_%d WHERE create_time between FROM_UNIXTIME(?) and FROM_UNIXTIME(?) and status=? limit ?`, month, i)

		infos := make([]entity.ConsumeRecord, 0)
		err := s.db.SelectContext(ctx, &infos, query, beginTime, endTime, status, limit)
		if err != nil {
			if err == sql.ErrNoRows {
				return infos, nil
			} else {
				log.ErrorWithCtx(ctx, "GetConsumeRecordByStatus db err:%s", err.Error())
				return nil, err
			}
		}
		res = append(res, infos...)
	}

	return res, nil
}

func (s *Store) ChangeConsumeRecordStatus(ctx context.Context, month, uid uint32, operateId string, status uint32) (err error) {
	query := fmt.Sprintf(`UPDATE smash_egg_month_consume_record_%02d_%d SET status=? WHERE operate_id=?`, month, uid%10)
	_, err = s.db.ExecContext(ctx, query, status, operateId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChangeConsumeRecordStatus db err:%s", err.Error())
		return
	}
	return nil
}

type StCount struct {
	Count int64 `db:"count"`
	Worth int64 `db:"worth"`
}

func (s *Store) GetConsumeTotalCountInfo(ctx context.Context, beginTime, endTime time.Time) (*StCount, error) {
	info := &StCount{}

	for i := 0; i < 10; i++ {
		tmp := &StCount{}
		query := fmt.Sprintf("SELECT COUNT(1) AS count, IF(SUM(fee),SUM(fee),0) AS worth from smash_egg_month_consume_record_%02d_%d where create_time >= ? and create_time < ?",
			beginTime.Month(), i)

		err := s.db.GetContext(ctx, tmp, query, beginTime, endTime)
		if err != nil {
			if err == sql.ErrNoRows {
				continue
			}
			log.ErrorWithCtx(ctx, "GetConsumeTotalCountInfo fail. b:%v, e:%v, err:%v", beginTime, endTime, err)
			continue
		}

		info.Count += tmp.Count
		info.Worth += tmp.Worth
	}

	return info, nil
}

func (s *Store) GetConsumeOrderIdList(ctx context.Context, beginTime, endTime time.Time) ([]string, error) {
	list := make([]string, 0)
	var err error

	for i := 0; i < 10; i++ {
		query := fmt.Sprintf("SELECT order_id from smash_egg_month_consume_record_%02d_%d where create_time >= ? and create_time < ?",
			beginTime.Month(), i)

		err = s.db.SelectContext(ctx, &list, query, beginTime, endTime)
		if err != nil {
			mysqlErr, ok := err.(*mysql.MySQLError)
			if !ok || mysqlErr.Number != 1146 {
				log.ErrorWithCtx(ctx, "GetConsumeOrderIdList fail. queryMonthTime:%v, err:%v", beginTime, err)
				return list, err
			}
		}
	}

	return list, nil
}
