package server

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"github.com/go-redis/redis"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	"golang.52tt.com/protocol/common/status"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	pb "golang.52tt.com/protocol/services/risk-control/award-center"
	"golang.52tt.com/services/risk-control/award-center/cache"
	"golang.52tt.com/services/risk-control/award-center/conf"
	"golang.52tt.com/services/risk-control/award-center/manager"
	"golang.52tt.com/services/risk-control/award-center/mysql"
)

type AwardCenterServer struct {
	sc         *conf.ServiceConfigT
	mgr        *manager.AwardCenterManager
	mysqlStore *mysql.Dao
	cache      *cache.Client
}

func NewAwardCenterServer(ctx context.Context, cfg config.Configer) (*AwardCenterServer, error) {
	sc := &conf.ServiceConfigT{}

	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		log.Errorf("config Parse fail err:%v", err)
		return nil, err
	}

	redisClient := redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfig().Protocol,
		Addr:               sc.GetRedisConfig().Addr(),
		PoolSize:           sc.GetRedisConfig().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfig().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfig().DB,
	})
	log.Debugf("Initialized redis connection pool to %s://%s/%d", sc.GetRedisConfig().Protocol, sc.GetRedisConfig().Addr(), sc.GetRedisConfig().DB)
	redisTracer := tracing.Init("award-center-redis")
	cacheClient := cache.NewCacheClient(redisClient, redisTracer)

	mysqlDb, err := gorm.Open("mysql", sc.GetMysqlConnectionString())
	if err != nil {
		log.Errorf("Failed to create mysql %v", err)
		return nil, err
	}
	mysqlDb.DB().SetMaxIdleConns(sc.GetMysqlConfig().MaxIdleConns)
	mysqlDb.DB().SetMaxOpenConns(sc.GetMysqlConfig().MaxOpenConns)
	mysqlStore := mysql.NewMysql(mysqlDb)

	mgr := manager.NewAwardCenterManager(sc, mysqlStore, cacheClient)

	return &AwardCenterServer{
		sc:         sc,
		mgr:        mgr,
		mysqlStore: mysqlStore,
		cache:      cacheClient,
	}, nil
}

func (s *AwardCenterServer) Award(ctx context.Context, in *pb.AwardReq) (*pb.AwardResp, error) {
	out := &pb.AwardResp{}

	if in.GetOrderId() == "" || in.GetTargetUid() == 0 {
		log.Errorf("Award Failed . in %+v err BadRequest", in)
		return out, protocol.NewServerError(status.ErrBadRequest)
	}

	err := s.mgr.CheckOrderIfExist(in.GetOrderId())
	if err != nil {
		log.ErrorWithCtx(ctx, "Award fail to CheckOrderIfExist. in:%+v, err:%v", in, err)
		return out, err
	}

	err = s.mgr.AddAwardOrder(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "Award fail to AddAwardOrder. in:%+v, err:%v", in, err)
		return out, err
	}

	log.DebugfWithCtx(ctx, "Award done. in:%+v", in)
	return out, nil
}

func (s *AwardCenterServer) RollbackAward(ctx context.Context, in *pb.RollbackAwardReq) (*pb.RollbackAwardResp, error) {
	out := &pb.RollbackAwardResp{}

	return out, s.mgr.RollbackAward(ctx, in)
}

func (s *AwardCenterServer) AddBusinessConf(ctx context.Context, in *pb.AddBusinessConfReq) (*pb.AddBusinessConfResp, error) {
	out := &pb.AddBusinessConfResp{}
	businessConf := &mysql.BusinessConf{}

	err := s.mysqlStore.Transaction(ctx, func(tx *gorm.DB) error {
		err := s.mysqlStore.AddBusinessConf(tx, &mysql.BusinessConf{
			Name:         in.GetName(),
			BusinessDesc: in.GetDesc(),
			SourceType:   in.GetSourceType(),
			BeginTime:    time.Unix(int64(in.GetBeginTs()), 0),
			EndTime:      time.Unix(int64(in.GetEndTs()), 0),
			UpdateTime:   time.Now(),
			CreateTime:   time.Now(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "AddBusinessConf fail to AddBusinessConf. in:%+v, err:%v", in, err)
			return err
		}

		businessConf, err = s.mysqlStore.GetBusinessConfByName(tx, in.GetName())
		if err != nil {
			log.ErrorWithCtx(ctx, "AddBusinessConf fail to GetBusinessConfByName. in:%+v, err:%v", in, err)
			return err
		}

		// 增加业务默认配置
		/*err = s.mysqlStore.AddAwardConf(tx, &mysql.AwardConf{
			BusinessId: businessConf.BusinessId,
			GiftId:     manager.DefaultConfGiftId,
			GiftType:   0,
			AwardDesc:  "业务默认配置",
			//DailyNumLimit:  in.GetDefaultDailyLimit(),
			//HourNumLimit:   in.GetDefaultHourLimit(),
			SingleNumLimit: in.GetDefaultSingleLimit(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "AddBusinessConf fail to AddAwardConf. in:%+v, err:%v", in, err)
			return err
		}*/

		return nil
	})

	if err != nil {
		return out, err
	}

	out.BusinessId = businessConf.BusinessId

	log.InfoWithCtx(ctx, "AddBusinessConf in:%+v businessConf:%+v", in, businessConf)
	return out, nil
}

func (s *AwardCenterServer) GetBusinessConf(ctx context.Context, in *pb.GetBusinessConfReq) (*pb.GetBusinessConfResp, error) {
	out := &pb.GetBusinessConfResp{}

	businessConf, err := s.mysqlStore.GetBusinessConfById(in.GetBusinessId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBusinessConf fail to GetBusinessConfById. in:%+v, err:%v", in, err)
		return out, err
	}

	if businessConf.BusinessId == in.GetBusinessId() {
		out.Conf = &pb.BusinessConf{
			BusinessId: businessConf.BusinessId,
			Name:       businessConf.Name,
			Desc:       businessConf.BusinessDesc,
			Invalid:    businessConf.Invalid,
			IsDel:      businessConf.IsDel,
			SourceType: businessConf.SourceType,
			BeginTs:    uint32(businessConf.BeginTime.Unix()),
			EndTs:      uint32(businessConf.EndTime.Unix()),
		}
	}

	return out, nil
}

func (s *AwardCenterServer) UpdateBusinessConf(ctx context.Context, in *pb.UpdateBusinessConfReq) (*pb.UpdateBusinessConfResp, error) {
	out := &pb.UpdateBusinessConfResp{}
	businessConf := in.GetConf()

	err := s.mysqlStore.UpdateBusinessConf(&mysql.BusinessConf{
		BusinessId:   businessConf.GetBusinessId(),
		Name:         businessConf.GetName(),
		BusinessDesc: businessConf.GetDesc(),
		SourceType:   businessConf.GetSourceType(),
		BeginTime:    time.Unix(int64(businessConf.GetBeginTs()), 0),
		EndTime:      time.Unix(int64(businessConf.GetEndTs()), 0),
		UpdateTime:   time.Now(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateBusinessConf fail to UpdateBusinessConf. in:%+v, err:%v", in, err)
		return out, err
	}

	log.Debugf("UpdateBusinessConf in:%+v out:%+v", in, out)
	return out, nil
}

func (s *AwardCenterServer) GetBusinessConfList(ctx context.Context, in *pb.GetBusinessConfListReq) (*pb.GetBusinessConfListResp, error) {
	out := &pb.GetBusinessConfListResp{}
	begin := in.GetPage() * in.GetPageSize()

	total, err := s.mysqlStore.GetBusinessConfListTotal()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBusinessConfList fail to GetBusinessConfListTotal. in:%+v, err:%v", in, err)
		return out, err
	}

	out.Total = total

	if total < begin {
		return out, nil
	}

	list, err := s.mysqlStore.GetBusinessConfList(begin, in.GetPageSize())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBusinessConfList fail to GetBusinessConfList. in:%+v, err:%v", in, err)
		return out, err
	}

	out.ConfList = make([]*pb.BusinessConf, 0, len(list))
	for _, businessConf := range list {
		out.ConfList = append(out.ConfList, &pb.BusinessConf{
			BusinessId: businessConf.BusinessId,
			Name:       businessConf.Name,
			Desc:       businessConf.BusinessDesc,
			Invalid:    businessConf.Invalid,
			IsDel:      businessConf.IsDel,
			SourceType: businessConf.SourceType,
			BeginTs:    uint32(businessConf.BeginTime.Unix()),
			EndTs:      uint32(businessConf.EndTime.Unix()),
		})
	}

	return out, nil
}

func (s *AwardCenterServer) DelBusinessConf(ctx context.Context, in *pb.DelBusinessConfReq) (*pb.DelBusinessConfResp, error) {
	out := &pb.DelBusinessConfResp{}

	err := s.mysqlStore.DelBusinessConf(in.GetBusinessId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelBusinessConf fail to DelBusinessConf. in:%+v, err:%v", in, err)
		return out, err
	}

	log.Debugf("DelBusinessConf in:%+v out:%+v", in, out)
	return out, nil
}

func (s *AwardCenterServer) AddAwardConf(ctx context.Context, in *pb.AddAwardConfReq) (*pb.AddAwardConfResp, error) {
	out := &pb.AddAwardConfResp{}
	awardConf := in.GetConf()
	businessId := awardConf.GetBusinessId()

	businessConf, err := s.mysqlStore.GetBusinessConfById(businessId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddAwardConf fail to GetBusinessConfById. in:%+v, err:%v", in, err)
		return out, err
	}

	if businessConf.BusinessId != businessId || businessConf.IsDel {
		log.ErrorWithCtx(ctx, "AddAwardConf fail. in:%+v, businessConf not found", in)
		return out, protocol.NewServerError(status.ErrRiskControlAwardCenterConfigInvalid, "业务配置不存在")
	}

	err = s.mysqlStore.AddAwardConf(nil, &mysql.AwardConf{
		BusinessId:         businessId,
		GiftId:             awardConf.GetGiftId(),
		GiftType:           awardConf.GetGiftType(),
		AwardDesc:          awardConf.GetItemDesc(),
		SingleNumLimit:     awardConf.GetSingleLimit(),
		DailyAwardCntLimit: awardConf.GetDailyAwardCntLimit(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddAwardConf fail to AddAwardConf. in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "AddAwardConf in:%+v", in)
	return out, nil
}

func (s *AwardCenterServer) GetAwardConf(ctx context.Context, in *pb.GetAwardConfReq) (*pb.GetAwardConfResp, error) {
	out := &pb.GetAwardConfResp{}

	awardConf, exist, err := s.mysqlStore.GetAwardConf(in.GetBusinessId(), in.GetGiftId(), in.GetGiftType())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAwardConf fail to GetAwardConf. in:%+v, err:%v", in, err)
		return out, err
	}

	if exist {
		out.Conf = &pb.AwardConf{
			BusinessId:         awardConf.BusinessId,
			GiftId:             awardConf.GiftId,
			GiftType:           awardConf.GiftType,
			ItemDesc:           awardConf.AwardDesc,
			SingleLimit:        awardConf.SingleNumLimit,
			DailyAwardCntLimit: awardConf.DailyAwardCntLimit,
		}
	}

	return out, nil
}

func (s *AwardCenterServer) UpdateAwardConf(ctx context.Context, in *pb.UpdateAwardConfReq) (*pb.UpdateAwardConfResp, error) {
	out := &pb.UpdateAwardConfResp{}
	awardConf := in.GetConf()

	err := s.mysqlStore.UpdateAwardConf(&mysql.AwardConf{
		BusinessId:         awardConf.GetBusinessId(),
		GiftId:             awardConf.GetGiftId(),
		GiftType:           awardConf.GetGiftType(),
		AwardDesc:          awardConf.GetItemDesc(),
		SingleNumLimit:     awardConf.GetSingleLimit(),
		DailyAwardCntLimit: awardConf.GetDailyAwardCntLimit(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAwardConf fail to UpdateAwardConf. in:%+v, err:%v", in, err)
		return out, err
	}

	log.Debugf("UpdateAwardConf in:%+v out:%+v", in, out)
	return out, nil
}

func (s *AwardCenterServer) GetAwardConfList(ctx context.Context, in *pb.GetAwardConfListReq) (*pb.GetAwardConfListResp, error) {
	out := &pb.GetAwardConfListResp{}
	begin := in.GetPage() * in.GetPageSize()

	total, err := s.mysqlStore.GetAwardConfListTotal(in.GetBusinessId(), in.GetGiftType())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAwardConfList fail to GetAwardConfListTotal. in:%+v, err:%v", in, err)
		return out, err
	}

	out.Total = total

	if total < begin {
		return out, nil
	}

	list, err := s.mysqlStore.GetAwardConfList(in.GetBusinessId(), in.GetGiftType(), begin, in.GetPageSize())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAwardConfList fail to GetAwardConfList. in:%+v, err:%v", in, err)
		return out, err
	}

	out.ConfList = make([]*pb.AwardConf, 0, len(list))
	for _, awardConf := range list {
		out.ConfList = append(out.ConfList, &pb.AwardConf{
			BusinessId:         awardConf.BusinessId,
			GiftId:             awardConf.GiftId,
			GiftType:           awardConf.GiftType,
			ItemDesc:           awardConf.AwardDesc,
			SingleLimit:        awardConf.SingleNumLimit,
			DailyAwardCntLimit: awardConf.DailyAwardCntLimit,
		})
	}

	return out, nil
}

func (s *AwardCenterServer) DelAwardConf(ctx context.Context, in *pb.DelAwardConfReq) (*pb.DelAwardConfResp, error) {
	out := &pb.DelAwardConfResp{}

	err := s.mysqlStore.DelAwardConf(in.GetBusinessId(), in.GetGiftId(), in.GetGiftType())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelAwardConf fail to DelAwardConf. in:%+v, err:%v", in, err)
		return out, err
	}

	return out, nil
}

type awardConf struct {
	GiftType   uint32 `json:"gift_type"`
	SourceType uint32 `json:"source_type"`
}

func (s *AwardCenterServer) GetAwardOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (out *reconcile_v2.CountResp, err error) {
	out = &reconcile_v2.CountResp{}
	param := &awardConf{}
	err = json.Unmarshal([]byte(in.GetParams()), param)
	if err != nil {
		log.Errorf("GetTbeanOrderCount err , in %v, err %v", in, err)
		return out, err
	}

	beginTime := time.Unix(in.GetBeginTime(), 0).Local()
	endTime := time.Unix(in.GetEndTime(), 0).Local()

	resp, err := s.mysqlStore.GetAwardOrderBySource(param.SourceType, param.GiftType, beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAwardOrderBySource fail to GetAwardOrderBySource. in:%+v, err:%v", in, err)
		return out, err
	}
	out.Count = uint32(len(resp))

	return out, err
}

func (s *AwardCenterServer) GetAwardOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq) (out *reconcile_v2.OrderIdsResp, err error) {
	out = &reconcile_v2.OrderIdsResp{}
	out.OrderIds = make([]string, 0)
	param := &awardConf{}
	err = json.Unmarshal([]byte(in.GetParams()), param)
	if err != nil {
		log.Errorf("GetTbeanOrderCount err , in %v, err %v", in, err)
		return out, err
	}

	beginTime := time.Unix(in.GetBeginTime(), 0).Local()
	endTime := time.Unix(in.GetEndTime(), 0).Local()

	resp, err := s.mysqlStore.GetAwardOrderBySource(param.SourceType, param.GiftType, beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAwardOrderBySource fail to GetAwardOrderBySource. in:%+v, err:%v", in, err)
		return out, err
	}
	for _, item := range resp {
		out.OrderIds = append(out.OrderIds, item.OrderId)
	}

	return out, err
}

func (s *AwardCenterServer) ShutDown() {
	s.mgr.ShutDown()
}
