package cache

import (
	"fmt"
	"github.com/go-redis/redis"
	"github.com/opentracing/opentracing-go"
	"golang.52tt.com/pkg/log"
	"strconv"
)

const DiversionCnt = 5
const LivingStatus = "1"

type ChannelDeeplinkRecommendCache struct {
	redisClient *redis.Client
	tracer      opentracing.Tracer
}

func NewChannelDeeplinkRecommendCache(r *redis.Client, tracer opentracing.Tracer) *ChannelDeeplinkRecommendCache {
	return &ChannelDeeplinkRecommendCache{redisClient: r, tracer: tracer}
}

func (r *ChannelDeeplinkRecommendCache) IncrChannelMemCnt(tagId, channelId uint32) error {
	return r.redisClient.ZIncr(genChannelMemCntKey(tagId), redis.Z{Member: channelId, Score: float64(1)}).Err()
}

func (r *ChannelDeeplinkRecommendCache) DecrChannelMemCnt(tagId, channelId uint32) error {
	key := genChannelMemCntKey(tagId)

	score, err := r.redisClient.ZIncr(key, redis.Z{Member: channelId, Score: float64(-1)}).Result()
	if err != nil {
		log.Errorf("DecrChannelMemCnt fail. tagId %d, channelId %d, err %v", tagId, channelId, err)
		return err
	}

	if score <= 0 {
		_ = r.RemoveOnlineChannel(tagId, channelId)
	}

	return nil
}

func (r *ChannelDeeplinkRecommendCache) RemoveOnlineChannel(tagId, channelId uint32) error {
	return r.redisClient.ZRem(genChannelMemCntKey(tagId), channelId).Err()
}

func (r *ChannelDeeplinkRecommendCache) RangeChannelMemCntTopN(tagId, topN uint32) ([]uint32, error) {
	key := genChannelMemCntKey(tagId)
	memList := make([]uint32, 0)

	strList, err := r.redisClient.ZRevRange(key, 0, int64(topN)).Result()
	if err != nil {
		log.Errorf("RangeChannelMemCntTopN fail to ZRevRange. tagId %d, topN %d, err %v", tagId, topN, err)
		return memList, err
	}

	for _, str := range strList {
		mem, err := strconv.ParseUint(str, 10, 32)
		if err != nil {
			log.Errorf("RangeChannelMemCntTopN fail to ParseInt. tagId %d, topN %d, str %v err %v", tagId, topN, str, err)
			continue
		}

		memList = append(memList, uint32(mem))
	}

	return memList, nil
}

func (r *ChannelDeeplinkRecommendCache) SetChannelAnchorSex(channelId, sex uint32) error {
	return r.redisClient.HSet(genLiveChannelAnchorSexKey(), strconv.Itoa(int(channelId)), sex).Err()
}

func (r *ChannelDeeplinkRecommendCache) BatchGetChannelAnchorSex(channelIds []uint32) (map[uint32]uint32, error) {
	mapCid2Sex := make(map[uint32]uint32)

	if len(channelIds) == 0 {
		return mapCid2Sex, nil
	}

	fields := make([]string, 0, len(channelIds))
	for _, cid := range channelIds {
		fields = append(fields, strconv.Itoa(int(cid)))
	}

	list, err := r.redisClient.HMGet(genLiveChannelAnchorSexKey(), fields...).Result()
	if err != nil {
		log.Errorf("BatchGetChannelAnchorSex fail to HMGet. channelId %v, err %v", channelIds, err)
		return mapCid2Sex, err
	}

	for i, iter := range list {
		cid := channelIds[i]

		if iter == nil {
			mapCid2Sex[cid] = 0

		} else if strSex, ok := iter.(string); ok {
			sex, _ := strconv.ParseUint(strSex, 10, 32)
			mapCid2Sex[cid] = uint32(sex)
		}
	}

	return mapCid2Sex, nil
}

func (r *ChannelDeeplinkRecommendCache) SetChannelTagId(channelId, tagId uint32) error {
	return r.redisClient.HSet(genChannelTagIdKey(), strconv.Itoa(int(channelId)), tagId).Err()
}

func (r *ChannelDeeplinkRecommendCache) BatchGetChannelTagId(channelIds []uint32) (map[uint32]uint32, error) {
	mapCid2TagId := make(map[uint32]uint32)

	if len(channelIds) == 0 {
		return mapCid2TagId, nil
	}

	fields := make([]string, 0, len(channelIds))
	for _, cid := range channelIds {
		fields = append(fields, strconv.Itoa(int(cid)))
	}

	list, err := r.redisClient.HMGet(genChannelTagIdKey(), fields...).Result()
	if err != nil {
		log.Errorf("BatchGetChannelTagId fail to HMGet. channelId %v, err %v", channelIds, err)
		return mapCid2TagId, err
	}

	for i, iter := range list {
		cid := channelIds[i]

		if iter == nil {
			mapCid2TagId[cid] = 0

		} else if strTagId, ok := iter.(string); ok {
			tagId, _ := strconv.ParseUint(strTagId, 10, 32)
			mapCid2TagId[cid] = uint32(tagId)
		}
	}

	return mapCid2TagId, nil
}

func (r *ChannelDeeplinkRecommendCache) SetLivingChannel(channelId uint32) error {
	return r.redisClient.HSet(genLivingChannelKey(), strconv.Itoa(int(channelId)), LivingStatus).Err()
}

func (r *ChannelDeeplinkRecommendCache) RemoveLivingChannel(channelId uint32) error {
	return r.redisClient.HDel(genLivingChannelKey(), strconv.Itoa(int(channelId))).Err()
}

func (r *ChannelDeeplinkRecommendCache) BatchGetLivingChannel(channelIds []uint32) (map[uint32]bool, error) {
	mapCid2Status := make(map[uint32]bool)

	if len(channelIds) == 0 {
		return mapCid2Status, nil
	}

	fields := make([]string, 0, len(channelIds))
	for _, cid := range channelIds {
		fields = append(fields, strconv.Itoa(int(cid)))
	}

	list, err := r.redisClient.HMGet(genLivingChannelKey(), fields...).Result()
	if err != nil {
		log.Errorf("BatchGetLivingChannel fail to HMGet. channelId %v, err %v", channelIds, err)
		return mapCid2Status, err
	}

	for i, iter := range list {
		cid := channelIds[i]

		if iter == nil {
			//mapCid2Status[cid] = false
		} else if strVal, ok := iter.(string); ok {
			mapCid2Status[cid] = strVal == LivingStatus
		}
	}

	return mapCid2Status, nil
}

func (r *ChannelDeeplinkRecommendCache) IncrChannelDiversionVersion() (uint32, error) {
	curr, err := r.redisClient.Incr(genChannelDiversionVersionKey()).Result()
	return uint32(curr), err
}

func (r *ChannelDeeplinkRecommendCache) GetChannelDiversionVersion() (uint32, error) {
	str, err := r.redisClient.Get(genChannelDiversionVersionKey()).Result()
	if err != nil {
		if err == redis.Nil {
			return 0, nil
		}

		log.Errorf("GetChannelDiversionVersion fail to Get. err %v", err)
		return 0, err
	}

	curr, _ := strconv.ParseUint(str, 10, 32)
	return uint32(curr), nil
}

type ChannelDiversionRate struct {
	Cid  uint32
	Rate uint32
}

func (r *ChannelDeeplinkRecommendCache) ResetChannelDiversion(list []*ChannelDiversionRate) (uint32, error) {
	currVersion, err := r.IncrChannelDiversionVersion()
	if err != nil {
		log.Errorf("ResetChannelDiversion fail to IncrChannelDiversionVersion. err %v", err)
		return 0, err
	}

	key := genChannelDiversionRuleKey(currVersion)

	memList := make([]redis.Z, 0, len(list))
	for _, info := range list {
		memList = append(memList, redis.Z{
			Member: info.Cid,
			Score:  float64(info.Rate),
		})
	}

	_ = r.redisClient.Del(key)
	err = r.redisClient.ZAdd(key, memList...).Err()
	if err != nil {
		log.Errorf("ResetChannelDiversion fail to ZAdd. err %v", err)
		return 0, err
	}

	return GetDiversionIdx(currVersion), nil
}

func (r *ChannelDeeplinkRecommendCache) GetChannelDiversion(idx uint32) ([]*ChannelDiversionRate, error) {
	key := genChannelDiversionRuleKey(idx)
	memList := make([]*ChannelDiversionRate, 0)

	zList, err := r.redisClient.ZRevRangeWithScores(key, 0, -1).Result()
	if err != nil {
		log.Errorf("GetChannelDiversion fail to ZRevRange. idx %d, err %v", idx, err)
		return memList, err
	}

	for _, z := range zList {
		if strMem, ok := z.Member.(string); ok {
			mem, err := strconv.ParseUint(strMem, 10, 32)
			if err != nil {
				log.Errorf("RangeChannelMemCntTopN fail to ParseInt. idx %d, str %v err %v", idx, mem, err)
				continue
			}

			memList = append(memList, &ChannelDiversionRate{
				Cid:  uint32(mem),
				Rate: uint32(z.Score),
			})
		}
	}

	return memList, nil
}

func GetDiversionIdx(version uint32) uint32 {
	return version % DiversionCnt
}

func genChannelMemCntKey(tagId uint32) string {
	return fmt.Sprintf("zset_channel_mem_cnt_%d", tagId)
}

func genLiveChannelAnchorSexKey() string {
	return "hash_channel_anchor_sex"
}

func genChannelTagIdKey() string {
	return "hash_channel_tag_id"
}

func genLivingChannelKey() string {
	return "hash_living_channel"
}

func genChannelDiversionRuleKey(version uint32) string {
	return fmt.Sprintf("zset_channel_diversion_%d", GetDiversionIdx(version))
}

func genChannelDiversionVersionKey() string {
	return "channel_diversion_version"
}
