package mgr

import (
	"context"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"sort"
	"time"

	"golang.52tt.com/protocol/services/userpresent"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	logicPb "golang.52tt.com/protocol/app/glory_world"
	userpresent2 "golang.52tt.com/protocol/app/userpresent"
	"golang.52tt.com/protocol/common/status"
	glory_reward "golang.52tt.com/protocol/services/glory-reward"
	"golang.52tt.com/services/glory-reward/cache"
	"golang.52tt.com/services/glory-reward/conf"
	"golang.52tt.com/services/glory-reward/rpc"
	"gorm.io/gorm"
)

// GloryWorldGiftFlow 荣耀世界送礼流水表
type GloryWorldGiftFlow struct {
	Id              uint64    `gorm:"column:id" json:"id"`                               //自增主键
	OrderId         string    `gorm:"column:order_id" json:"order_id"`                   //订单ID
	Uid             uint32    `gorm:"column:uid" json:"uid"`                             //送礼人uid
	TargetUid       uint32    `gorm:"column:target_uid" json:"target_uid"`               //收礼人uid
	SendTime        int64     `gorm:"column:send_time" json:"send_time"`                 //送礼时间
	ItemId          uint32    `gorm:"column:item_id" json:"item_id"`                     //礼物ID
	ItemCount       uint32    `gorm:"column:item_count" json:"item_count"`               //礼物数量
	Price           uint32    `gorm:"column:price" json:"price"`                         //礼物价格
	ChannelId       uint32    `gorm:"column:channel_id" json:"channel_id"`               //获取礼物时的房间ID
	ItemSource      uint32    `gorm:"column:item_source" json:"item_source"`             //礼物类型，详见ga::PresentSourceType
	FromUkwAccount  string    `gorm:"column:from_ukw_account" json:"from_ukw_account"`   //送礼人神秘人账号，为空则不是神秘人
	FromUkwNickname string    `gorm:"column:from_ukw_nickname" json:"from_ukw_nickname"` //送礼神秘人昵称，为空则不是神秘人
	ToUkwAccount    string    `gorm:"column:to_ukw_account" json:"to_ukw_account"`       //收礼神秘人账号，为空则不是神秘人
	ToUkwNickname   string    `gorm:"column:to_ukw_nickname" json:"to_ukw_nickname"`     //收礼神秘人昵称，为空则不是神秘人
	UpdateTime      time.Time `gorm:"column:update_time" json:"update_time"`             //记录更新时间
	IsOpen          bool      `gorm:"column:is_open" json:"is_open"`                     //开关是否开启，0：未开启；1：已开启
}

func (g *GloryWorldGiftFlow) TableName(t time.Time) string {
	timeFlow := time.Unix(t.Unix(), 0)
	return fmt.Sprintf("%s_%s", "glory_world_gift_flow", timeFlow.Format("200601"))
}

type GiftFlowStore interface {
	InsertGiftFlow(ctx context.Context, tx *gorm.DB, flow *GloryWorldGiftFlow) error
	GetFlowInfoWithPeriod(ctx context.Context, start, end int64) (count, sum uint32, err error)
	GetOrderListWithPeriod(ctx context.Context, start, end int64) ([]string, error)
	GetFlowByOrderId(ctx context.Context, orderId string, orderTime time.Time) (*GloryWorldGiftFlow, error)
	CreateGiftFlowTbl()
}

type Transaction interface {
	Transaction(ctx context.Context, f func(tx *gorm.DB) error) error
}

type SettlementRewardMgr interface {
	ProcessEventGift(ctx context.Context, flow *GloryWorldGiftFlow, channelId uint32) (err error, needRetry bool)
	ReceiveRewardTask(ctx context.Context, uid uint32, taskList []uint32) error
	ReSendPkg(ctx context.Context, orderId string) error
	AutoResendPkg(ctx context.Context)
	AddFragment(ctx context.Context, uid uint32, fragType glory_reward.GloryFragmentType, num uint32) error
	PushTaskPopWindow(ctx context.Context, uid uint32, channelId uint32, fragType []glory_reward.RewardType) error
	IsValidToAddFragment(ctx context.Context, itemId uint32, priceType uint32, itemSource uint32, tagType uint32, price uint32) bool
	CountPresentOrderNumAndValueByTime(ctx context.Context, startTime, endTime int64) (count uint32, value uint32, err error)
	GetPresentOrderIdsByTime(ctx context.Context, startTime, endTime int64) (orderIds []string, err error)
	PresentReSendPkg(ctx context.Context, orderId string) error
	InsertGiftFlow(ctx context.Context, flow *GloryWorldGiftFlow) error
}

type SettlementRewardMgrWrapper struct {
	taskMgr       TaskMgr
	fragRewardMgr FragmentRewardMgr
	giftFlowStore GiftFlowStore
}

func NewSettlementRewardMgrWrapper(taskMgr TaskMgr, fragRewardMgr FragmentRewardMgr, giftFlowStore GiftFlowStore,
) *SettlementRewardMgrWrapper {
	return &SettlementRewardMgrWrapper{
		taskMgr:       taskMgr,
		fragRewardMgr: fragRewardMgr,
		giftFlowStore: giftFlowStore,
	}
}

type settlementRewardMgr struct {
	transaction     Transaction
	feishu          Feishu
	client          rpc.GloryClient
	dyConf          conf.DynamicConf
	cache           cache.GloryCache
	funcCache       cache.FuncCache
	presentMemCache cache.PresentMemCache
	taskMgr         TaskMgr
	fragRewardMgr   FragmentRewardMgr
	giftFlowStore   GiftFlowStore
	whiteList       WhiteListMgr
}

func NewSettlementRewardMgr(transaction Transaction, feishu Feishu, client rpc.GloryClient, cache cache.GloryCache, dyConf conf.DynamicConf,
	wrapper *SettlementRewardMgrWrapper, funcCache cache.FuncCache, whiteList WhiteListMgr, presentMemCache cache.PresentMemCache) SettlementRewardMgr {
	return &settlementRewardMgr{
		transaction:     transaction,
		feishu:          feishu,
		client:          client,
		dyConf:          dyConf,
		cache:           cache,
		funcCache:       funcCache,
		taskMgr:         wrapper.taskMgr,
		fragRewardMgr:   wrapper.fragRewardMgr,
		giftFlowStore:   wrapper.giftFlowStore,
		whiteList:       whiteList,
		presentMemCache: presentMemCache,
	}
}

// IsValidToAddFragment 校验消息是否可以增加星钻
func (s *settlementRewardMgr) IsValidToAddFragment(ctx context.Context, itemId uint32, priceType uint32, itemSource uint32,
	tagType uint32, price uint32) bool {

	// 如果是礼物套组礼物，判断下是否在激活的套组中
	if tagType == uint32(app.PresentTagType_PRESENT_TAG_SET) {
		isActive, err := s.client.CheckPresentInActiveSet(ctx, itemId)
		if err != nil {
			log.ErrorWithCtx(ctx, "IsValidToAddFragment CheckPresentInActiveSet failed, itemId:[%+v], err:[%+v]", itemId, err)
			return false
		}
		if isActive && price >= s.dyConf.GiftLessPrice() {
			log.DebugWithCtx(ctx, "GiftFlowEventProcessor handle with active set, itemId:[%+v]", itemId)
			return true
		}
		//isActive, err = s.client.CheckPresentInActiveEmperorSet(ctx, itemId)
		//if err != nil {
		//	log.ErrorWithCtx(ctx, "IsValidToAddFragment CheckPresentInActiveEmperorSet failed, itemId:[%+v], err:[%+v]", itemId, err)
		//	return false
		//}
		//if isActive && price >= s.dyConf.GiftLessPrice() {
		//	log.DebugWithCtx(ctx, "GiftFlowEventProcessor handle with active emperor set, itemId:[%+v]", itemId)
		//	return true
		//}
	}

	// 如果是白名单礼物不校验下面信息
	if s.whiteList.GetPresentWhiteListMap()[itemId] {
		log.DebugWithCtx(ctx, "GiftFlowEventProcessor handle with whitelist, itemId:[%+v]", itemId)
		return true
	}

	// 如果不是对应的送礼类型则跳过
	if itemSource != uint32(userpresent2.PresentSourceType_PRESENT_SOURCE_BUY) &&
		itemSource != uint32(userpresent2.PresentSourceType_PRESENT_SOURCE_FELLOW) &&
		itemSource != uint32(userpresent2.PresentSourceType_PRESENT_SOURCE_OFFERING_ROOM) {
		log.DebugWithCtx(ctx, "GiftFlowEventProcessor NotHandle, GetItemSource:[%+v]", itemSource)
		return false
	}

	// 如果是语音直播粉丝团礼物过滤
	if tagType == uint32(app.PresentTagType_PRESENT_TAG_ADD_GROUP) {
		log.DebugWithCtx(ctx, "GiftFlowEventProcessor NotHandle, GetTagType:[%+v]", tagType)
		return false
	}

	// 如果小于最小T豆价格直接过滤
	if price < s.dyConf.GiftLessPrice() {
		log.DebugWithCtx(ctx, "GiftFlowEventProcessor NotHandle, Price:%d", price)
		return false
	}

	return true
}

// ProcessEventGift 处理礼物消息, 返回true则让消息队列进行重试
func (s *settlementRewardMgr) ProcessEventGift(ctx context.Context, flow *GloryWorldGiftFlow, channelId uint32) (err error, needRetry bool) {

	// 增加recover
	defer func() {
		if e := recover(); e != nil {
			log.Errorf("ProcessEventGift panic err:[%+v], stack:[%+v]", e, string(debug.Stack()))
			_ = s.feishu.SendError(fmt.Sprintf("【荣耀星钻获取】处理礼物消息异常panic，错误信息:[%+v]", string(debug.Stack())))
			return
		}
	}()

	// 判断是否要丢单
	if s.dyConf.IsDiscardOrderUser()[flow.Uid] {
		log.InfoWithCtx(ctx, "ProcessEventGift discard order user, uid:[%+v]", flow.Uid)
		return nil, false
	}

	// 判断是否开启(包含白名单逻辑)
	isOpen, _ := s.dyConf.GetEnterInfo(flow.Uid, 0, 0, false)
	// 判断是否关闭
	isCloseReward := s.dyConf.IsCloseReward()
	if !isOpen || isCloseReward {
		log.InfoWithCtx(ctx, "ProcessEventGift glory word not open!")

		// 未开启时记录流水
		flow.IsOpen = false
		if err = s.giftFlowStore.InsertGiftFlow(ctx, nil, flow); err != nil {
			log.ErrorWithCtx(ctx, "ProcessEventGift InsertGiftFlow failed, flow:[%+v], err:[%+v]", flow, err)
			return err, true
		}

		return nil, false
	}

	log.DebugWithCtx(ctx, "ProcessEventGift flow:[%+v]", flow)
	// 用户级分布式锁，保证无并发问题
	key := fmt.Sprintf("glory-reward:process-event-gift:%+v", flow.Uid)
	if lock := s.cache.Lock(ctx, key, 15*time.Second); !lock {
		log.ErrorWithCtx(ctx, "ProcessEventGift Lock failed, key:[%+v]", key)
		return fmt.Errorf("未获取到分布式锁，等待一段时间后操作"), true
	} // 按照uid上锁15秒，保证达标奖励不会重复获取
	defer func() {
		_ = s.cache.UnLock(ctx, key)
	}()

	// 获取背包信息
	pkgIdMap, businessId, businessKey, err := s.getPkgConf(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcessEventGift getPkgConf failed, err:[%+v]", err)
		return err, true
	}

	// 处理基础奖励逻辑
	records, err := s.settlementBaseReward(ctx, flow, pkgIdMap, businessId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcessEventGift settlementBaseReward failed, err:[%+v]", err)
		return err, true // 失败重试
	}

	// 处理任务逻辑
	fee := int64(flow.Price * flow.ItemCount)
	orderTime := time.Unix(flow.SendTime, 0)
	settlementTaskRecords, err := s.taskMgr.SettlementTask(ctx, flow.Uid, flow.ItemId, flow.OrderId, flow.Price, orderTime, fee)
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcessEventGift SettlementTask failed, err:[%+v]", err)
		return err, true // 失败重试
	}

	// 事务入库
	if err = s.transactionSaveRecord(ctx, flow, records, settlementTaskRecords); err != nil {
		log.ErrorWithCtx(ctx, "ProcessEventGift transactionSaveRecord failed, err:[%+v]", err)
		return err, true // 失败重试
	}

	// 异步调用lottery服务，增加流水值
	go s.client.AddLotteryUserFee(grpc.NewContextWithInfo(ctx), flow.Uid, uint32(settlementTaskRecords.fee))

	// 拉起任务异步流程
	go s.sendTaskNotice(grpc.NewContextWithInfo(ctx), channelId, flow.Uid, flow.OrderId)

	// 发放基础任务奖励
	if len(records) != 0 {
		if err = s.sendFragPkg(grpc.NewContextWithInfo(ctx), records, pkgIdMap, businessId, businessKey); err != nil {
			_ = s.feishu.SendWarning(fmt.Sprintf("【荣耀世界】基础星钻包裹发放失败，稍后进行补偿发放，失败原因：%+v", err))
			// 仅抛错误，不进行重试，补偿队列重试
			return err, false
		}
	}

	return nil, false
}

// ReceiveRewardTask 用户领取任务奖励
func (s *settlementRewardMgr) ReceiveRewardTask(ctx context.Context, uid uint32, taskList []uint32) error {

	// 获取背包信息
	pkgIdMap, businessId, businessKey, err := s.getPkgConf(ctx)
	if err != nil {
		return err
	}
	now := time.Now().Unix()

	// 查询对应任务信息
	tasks, err := s.taskMgr.GetNotSendTaskRecordByTaskList(ctx, uid, taskList, businessId, pkgIdMap)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReceiveRewardTask GetTaskRecordByTaskList failed, uid:[%+v], taskList:[%+v], err:[%+v]",
			uid, taskList, err)
		return protocol.NewExactServerError(nil, status.ErrGloryGetTaskInfoFail, "查询挑战任务信息失败！")
	}
	// 如果查询为空则已经发放
	if len(tasks) == 0 {
		return nil
	}

	// 组装碎片发放信息
	records := make([]*GloryWorldFragmentReward, 0)
	for _, task := range tasks {
		records = append(records, s.controlNumberSingleReward(ctx, &GloryWorldFragmentReward{
			OrderId:            task.OrderId,
			Uid:                task.Uid,
			FragmentType:       task.FragmentType,
			FragmentNum:        task.FragmentNum,
			FragmentRewardType: uint16(glory_reward.FragmentRewardType_FragmentRewardTypeTask),
			RewardType:         task.RewardType,
			RewardRemark:       task.RewardRemark,
			GiftItem:           0,
			GiftNum:            0,
			IsSend:             false,
			CreateTime:         time.Unix(now, 0),
		})...)
	}

	// 事务更新数据
	if err = s.transactionReceiveReward(ctx, tasks, records); err != nil {
		log.ErrorWithCtx(ctx, "ReceiveRewardTask transactionReceiveReward failed, err:[%+v]", err)
		return protocol.NewExactServerError(nil, status.ErrGloryRewardTaskFail, "领取奖励失败！")
	}

	// 发放包裹
	go func() {
		if len(records) != 0 {
			if err = s.sendFragPkg(grpc.NewContextWithInfo(ctx), records, pkgIdMap, businessId, businessKey); err != nil {
				_ = s.feishu.SendWarning(fmt.Sprintf("【荣耀世界】任务包裹发放失败，稍后进行补偿发放，失败原因：%+v", err))
			}
		}
	}()

	return nil
}

// GetPkgConf 获取包裹配置
func (s *settlementRewardMgr) getPkgConf(ctx context.Context) (pkgIdMap map[uint32]uint32, pkgBusinessId uint32, pkgBusinessKey string, err error) {
	pkgIdMap = s.dyConf.GetPkgId()
	pkgBusinessId, pkgBusinessKey = s.dyConf.GetPkgBusinessInfo()
	if len(pkgIdMap) != 2 || pkgBusinessId == 0 || pkgBusinessKey == "" {
		log.ErrorWithCtx(ctx, "GetPkgConf failed, pkgIdMap:[%+v], pkgBusinessId:[%+v], pkgBusinessKey:[%+v]", pkgIdMap, pkgBusinessId, pkgBusinessKey)
		_ = s.feishu.SendError(fmt.Sprintf("【荣耀世界】碎片包裹未进行配置，请检查配置文件！pkgIdMap:[%+v], pkgBusinessId:[%+v], pkgBusinessKey:[%+v]",
			pkgIdMap, pkgBusinessId, pkgBusinessKey))
		return nil, 0, "", protocol.NewExactServerError(nil, status.ErrGloryGetTaskInfoFail, "服务配置错误！")
	}
	return pkgIdMap, pkgBusinessId, pkgBusinessKey, nil
}

// transactionReceiveReward 事务更新数据
func (s *settlementRewardMgr) transactionReceiveReward(ctx context.Context, tasks []*GloryWorldTask, records []*GloryWorldFragmentReward) error {
	// 处理逻辑函数
	doTx := func(tx *gorm.DB) error {
		// 更新任务信息
		if len(tasks) != 0 {
			err := s.taskMgr.BatchSendTaskReward(ctx, tx, tasks)
			if err != nil {
				log.ErrorWithCtx(ctx, "transactionReceiveReward BatchSendTaskReward failed, tasks:[%+v], err:[%+v]", tasks, err)
				return err
			}
		}

		// 插入发放记录，这里仅表示已点击领取，但是有概率失败，由定时任务和背包回调保证发放成功
		err := s.fragRewardMgr.BatchAddFragReward(ctx, tx, records)
		if err != nil {
			log.ErrorWithCtx(ctx, "transactionReceiveReward BatchAddFragReward failed, records:[%+v], err:[%+v]", records, err)
			return err
		}
		return nil
	}

	// 事务操作
	if err := s.transaction.Transaction(ctx, doTx); err != nil {
		log.ErrorWithCtx(ctx, "transactionReceiveReward failed, err:[%+v]", err)
		return err
	}
	return nil
}

// SendFragPkg 发放包裹
func (s *settlementRewardMgr) sendFragPkg(ctx context.Context, records []*GloryWorldFragmentReward, pkgIdMap map[uint32]uint32,
	pkgBusinessId uint32, pkgBusinessKey string) error {

	// 循环发放星钻
	for _, record := range records {
		if err := s.client.SendFragment(ctx, record.Uid, pkgIdMap[uint32(record.FragmentType)], record.FragmentNum, record.CreateTime, record.OrderId,
			pkgBusinessId, pkgBusinessKey); err != nil {
			log.ErrorWithCtx(ctx, "sendFragPkg SendFragment failed, record:[%+v], pkgIdMap:[%+v], pkgBusinessId:[%+v],"+
				"pkgBusinessKey:[%+v], err:[%+v]", records, pkgIdMap, pkgBusinessId, pkgBusinessKey, err)
			now := time.Now()
			threeHourAgo := time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-3, now.Minute(), now.Second(), now.Nanosecond(), time.Local)
			if threeHourAgo.After(record.CreateTime) { // 如果尝试发放已经超过三小时，进行告警处理
				_ = s.feishu.SendError(fmt.Sprintf("【荣耀星钻发放】尝试发放订单超过三小时，请检查订单状态！订单号:[%+v], 错误信息:[%+v]", record.OrderId, err))
			}
			return err
		}
	}

	// 更新已发放记录
	if err := s.fragRewardMgr.BatchSendFragReward(ctx, records); err != nil {
		log.ErrorWithCtx(ctx, "sendFragPkg BatchSendFragReward failed, records:[%+v], err:[%+v]", records, err)
		return err
	}

	return nil
}

// SettlementBaseReward 结算基础星钻奖励，基础奖励基本不变，不做复杂逻辑
func (s *settlementRewardMgr) settlementBaseReward(ctx context.Context, flow *GloryWorldGiftFlow, pkgIdMap map[uint32]uint32,
	businessId uint32) ([]*GloryWorldFragmentReward, error) {

	result := make([]*GloryWorldFragmentReward, 0)
	// 结算声望星钻基础奖励
	rewardFame := s.settlementBaseFameFragment(flow, pkgIdMap[uint32(glory_reward.GloryFragmentType_GloryFragmentTypeFame)], businessId)
	result = append(result, s.controlNumberSingleReward(ctx, rewardFame)...)

	// 结算荣耀星钻基础奖励
	rewardGlory, err := s.settlementBaseGloryFragment(ctx, flow, pkgIdMap[uint32(glory_reward.GloryFragmentType_GloryFragmentTypeGlory)], businessId)
	if err != nil {
		log.ErrorWithCtx(ctx, "settlementBaseReward settlementBaseGloryFragment failed, err:[%+v]", err)
		return result, err
	}
	if rewardGlory != nil {
		result = append(result, s.controlNumberSingleReward(ctx, rewardGlory)...)
	}

	return result, nil
}

const BackPackSendMaxNum = int32(10000) // 背包最大单次发放数量

// controlNumberSingleReward 控制单次发放碎片数不超过背包限制上限
// 目前背包单次发放物品上限是10000
func (s *settlementRewardMgr) controlNumberSingleReward(ctx context.Context, reward *GloryWorldFragmentReward) []*GloryWorldFragmentReward {
	result := make([]*GloryWorldFragmentReward, 0)

	// 如果没有超过上限，直接返回
	if reward.FragmentNum < uint32(BackPackSendMaxNum) {
		result = append(result, reward)
		return result
	}
	log.InfoWithCtx(ctx, "controlNumberSingleReward reward fragment num over 10000, reward:[%+v]", reward)

	// 超过上限进行拆单
	fragNum := int32(reward.FragmentNum)
	for i := 1; ; i++ {
		num := BackPackSendMaxNum
		if fragNum <= BackPackSendMaxNum {
			num = fragNum
		}
		// 装入拆分记录
		result = append(result, &GloryWorldFragmentReward{
			OrderId:            fmt.Sprintf("%+v_%+v", reward.OrderId, i),
			Uid:                reward.Uid,
			FragmentType:       reward.FragmentType,
			FragmentNum:        uint32(num),
			FragmentRewardType: reward.FragmentRewardType,
			RewardType:         reward.RewardType,
			RewardRemark:       reward.RewardRemark,
			GiftItem:           reward.GiftItem,
			GiftNum:            reward.GiftNum,
			IsSend:             false,
			CreateTime:         reward.CreateTime,
			UpdateTime:         reward.UpdateTime,
		})
		fragNum = fragNum - BackPackSendMaxNum
		if fragNum <= 0 {
			break
		}
	}
	resultJson, _ := json.Marshal(result)
	log.InfoWithCtx(ctx, "controlNumberSingleReward result:[%+v]", string(resultJson))
	return result
}

const (
	SettlementBaseModulus = uint32(100) // 基础计算系数
)

// GetTaskOrderId 获取任务唯一包裹发放订单ID
// 形如：55_66_TT_GLORY_0_27588682_10_0_20200507163840_947554
func (s *settlementRewardMgr) getBaseOrderId(businessId uint32, pkgId uint32, giftOrderId string) string {
	return fmt.Sprintf("%+v_%+v_TT_GLORY_%+v", businessId, pkgId, giftOrderId)
}

// settlementBaseFameFragment 结算声望星钻基础奖励
func (s *settlementRewardMgr) settlementBaseFameFragment(flow *GloryWorldGiftFlow, pkgId uint32, businessId uint32) *GloryWorldFragmentReward {
	count := flow.Price / SettlementBaseModulus
	fragCount := count * flow.ItemCount
	orderId := s.getBaseOrderId(businessId, pkgId, flow.OrderId)
	return &GloryWorldFragmentReward{
		OrderId:            orderId,
		Uid:                flow.Uid,
		FragmentType:       uint8(glory_reward.GloryFragmentType_GloryFragmentTypeFame),
		FragmentNum:        fragCount,
		FragmentRewardType: uint16(glory_reward.FragmentRewardType_FragmentRewardTypeBase),
		RewardType:         uint16(glory_reward.RewardType_RewardTypeFameBase),
		RewardRemark:       "",
		GiftItem:           flow.ItemId,
		GiftNum:            flow.ItemCount,
		IsSend:             false,
		CreateTime:         time.Unix(flow.SendTime, 0),
	}
}

// settlementBaseGloryFragment 结算荣耀星钻基础奖励
func (s *settlementRewardMgr) settlementBaseGloryFragment(ctx context.Context, flow *GloryWorldGiftFlow, pkgId uint32, businessId uint32) (
	*GloryWorldFragmentReward, error) {

	// 获取周消费值
	userWeekFlow, err := s.taskMgr.GetUserWeekFlow(ctx, flow.Uid, time.Unix(flow.SendTime, 0))
	if err != nil {
		log.ErrorWithCtx(ctx, "settlementBaseGloryFragment GetUserWeekFlow failed, err:[%+v]", err)
		return nil, err
	}

	// 判断值达到多少然后决定系数
	var extraModulus uint32
	if userWeekFlow < SettlementGlory100Thousand { // 如果小于基础准入门槛，则直接返回空
		return nil, nil
	} else if userWeekFlow >= SettlementGlory100Thousand && userWeekFlow < SettlementGloryMillion { // 如果介于10w到100w之间，则系数为1
		extraModulus = 1
	} else { // 剩下为大于100W，系数为2
		extraModulus = 2
	}
	count := flow.Price / SettlementBaseModulus * extraModulus
	fragCount := count * flow.ItemCount
	orderId := s.getBaseOrderId(businessId, pkgId, flow.OrderId)
	return &GloryWorldFragmentReward{
		OrderId:            orderId,
		Uid:                flow.Uid,
		FragmentType:       uint8(glory_reward.GloryFragmentType_GloryFragmentTypeGlory),
		FragmentNum:        fragCount,
		FragmentRewardType: uint16(glory_reward.FragmentRewardType_FragmentRewardTypeBase),
		RewardType:         uint16(glory_reward.RewardType_RewardTypeFameBase),
		RewardRemark:       "",
		GiftItem:           flow.ItemId,
		GiftNum:            flow.ItemCount,
		IsSend:             false,
		CreateTime:         time.Unix(flow.SendTime, 0),
	}, nil
}

// transactionSaveRecord 事务保存记录
func (s *settlementRewardMgr) transactionSaveRecord(ctx context.Context, flow *GloryWorldGiftFlow, records []*GloryWorldFragmentReward,
	settlementTaskRecords *SettlementTaskRecords) error {

	// 处理逻辑函数
	doTx := func(tx *gorm.DB) error {
		// 写入送礼记录
		if err := s.giftFlowStore.InsertGiftFlow(ctx, tx, flow); err != nil {
			log.ErrorWithCtx(ctx, "transactionSaveRecord InsertGiftFlow failed, flow:[%+v], err:[%+v]", flow, err)
			return err
		}

		// 写入星钻获取记录
		if err := s.fragRewardMgr.BatchAddFragReward(ctx, tx, records); err != nil {
			log.ErrorWithCtx(ctx, "transactionSaveRecord BatchAddFragReward failed, flow:[%+v], err:[%+v]", flow, err)
			return err
		}

		// 写入任务相关记录
		if err := s.taskMgr.BatchInsertSettlementRecords(ctx, tx, settlementTaskRecords); err != nil {
			log.ErrorWithCtx(ctx, "transactionSaveRecord BatchAddFragReward failed, flow:[%+v], err:[%+v]", flow, err)
			return err
		}

		return nil
	}

	// 事务处理
	if err := s.transaction.Transaction(ctx, doTx); err != nil {
		log.ErrorWithCtx(ctx, "transactionReceiveReward failed, err:[%+v]", err)
		return err
	}
	return nil
}

const (
	PopWindowFMT             = "挑战成功！完成【%+v】"
	TTHelperFameFragmentFMT  = "恭喜你完成【%+v】任务获得%+v声望星钻，快去荣耀世界查看吧>>"
	TTHelperGloryFragmentFMT = "恭喜你完成【%+v】任务获得%+v荣耀星钻，快去荣耀世界查看吧>>"
	ChannelFameFragmentFMT   = "【提醒】恭喜你完成%+v，获得%+v声望星钻"
	ChannelGloryFragmentFMT  = "【提醒】恭喜你完成%+v，获得%+v荣耀星钻"
)

// sendTaskNotice 推送任务弹窗提醒
func (s *settlementRewardMgr) sendTaskNotice(ctx context.Context, channelId uint32, uid uint32, giftOrderId string) {

	// 清除用户任务相关接口缓存
	go func() {
		if err := s.funcCache.Clean(grpc.NewContextWithInfo(ctx), cache.GetAllUserCacheDelKey(uid)); err != nil {
			log.ErrorWithCtx(ctx, "sendTaskNotice Clean failed, err:[%+v]")
		}
	}()

	// 获取要推送的任务信息
	tasks, giftFirstSendIdMap, err := s.taskMgr.GenPushTasks(ctx, uid, giftOrderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendTaskNotice GenPushTasks failed, err:[%+v]", err)
		return
	}

	// 获取弹窗配置
	pushConfMap := s.dyConf.GetPushConf()
	if len(pushConfMap) != 2 {
		_ = s.feishu.SendError(fmt.Sprintf("【荣耀世界】任务弹窗资源未进行配置，请检查配置文件！pushConfMap:[%+v]", pushConfMap))
	}

	// 获取登陆信息
	marketId, os, err := s.client.GetUserLoginInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendTaskNotice GetUserLoginInfo failed, uid:[%+v] err:[%+v]", uid, err)
		_ = s.feishu.SendError(fmt.Sprintf("【荣耀世界】查询用户登陆信息失败！uid:[%+v], err:[%+v]", uid, err))
		return
	}

	// 查询url
	_, url := s.dyConf.GetEnterInfo(uid, marketId, os, false)
	if url == "" {
		log.ErrorWithCtx(ctx, "sendTaskNotice GetEnterInfo failed, marketId:[%+v] os:[%+v]", marketId, os)
		_ = s.feishu.SendError(fmt.Sprintf("【荣耀世界】根据用户登陆信息获取入口url失败！uid:[%+v], marketId:[%+v], os:[%+v]", uid, marketId, os))
		return
	}

	// 循环生成对应消息
	msgList := make([]rpc.TaskRewardMsg, 0)
	for _, t := range tasks {
		task := t
		pushConf := pushConfMap[uint32(task.FragmentType)]
		banner := fmt.Sprintf(PopWindowFMT, task.RewardRemark)
		notice, helperNotice := s.getTaskMsgNotice(glory_reward.GloryFragmentType(task.FragmentType), task.RewardRemark, task.FragmentNum)
		msg := logicPb.GloryRewardChannelTaskMsg{
			Uid:              task.Uid,
			Type:             logicPb.GloryFragmentType(task.FragmentType),
			Url:              pushConf.UrlField,
			Md5:              pushConf.Md5Field,
			SpecialEffectUrl: pushConf.SpecialEffectUrlField,
			SpecialEffectMd5: pushConf.SpecialEffectMd5Field,
			Banner:           banner,
			Num:              task.FragmentNum,
			DisplayTime:      pushConf.DisplayTimeField,
			TaskList:         []uint32{task.Id},
			Notice:           notice,
			EnterUrl:         url,
			ShineUrl:         pushConf.ShineUrlField,
			CircleLightUrl:   pushConf.CircleLightUrlField,
		}
		// 当为礼物信息弹窗时，填入合并后的礼物信息
		if task.RewardType == uint16(glory_reward.RewardType_RewardTypeTaskGiftFirstSend) {
			isList := make([]uint32, 0)
			var fragNum uint32
			for id, num := range giftFirstSendIdMap {
				isList = append(isList, id)
				fragNum += num
			}
			msg.TaskList = isList
			msg.Num = fragNum
		}
		taskMsg := rpc.TaskRewardMsg{Channel: msg, HelperNotice: helperNotice}
		msgList = append(msgList, taskMsg)
	}
	s.batchPushTaskRewardMsg(ctx, msgList, channelId, uid)
}

// getTaskMsgNotice 获取任务推送提醒文案
func (s *settlementRewardMgr) getTaskMsgNotice(fragType glory_reward.GloryFragmentType, remark string, num uint32) (string, string) {
	var notice, helperNotice string
	if fragType == glory_reward.GloryFragmentType_GloryFragmentTypeFame {
		notice = fmt.Sprintf(ChannelFameFragmentFMT, remark, num)
		helperNotice = fmt.Sprintf(TTHelperFameFragmentFMT, remark, num)
	} else {
		notice = fmt.Sprintf(ChannelGloryFragmentFMT, remark, num)
		helperNotice = fmt.Sprintf(TTHelperGloryFragmentFMT, remark, num)
	}
	return notice, helperNotice
}

// batchPushTaskRewardMsg 批量推送任务消息
func (s *settlementRewardMgr) batchPushTaskRewardMsg(ctx context.Context, msgList []rpc.TaskRewardMsg, channelId uint32, uid uint32) {
	// 排序消息
	sort.Slice(msgList, func(i, j int) bool {
		if msgList[i].Channel.Type == msgList[j].Channel.Type {
			return msgList[i].Channel.Num > msgList[j].Channel.Num // 类型相等时，优先返回价值大的
		}
		return msgList[i].Channel.Type > msgList[j].Channel.Type // 类型不想等，先返回荣耀星钻
	})

	// 循环调用弹窗逻辑
	for _, msg := range msgList {
		m := msg
		err := s.client.PushTaskRewardMsg(ctx, m, channelId, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "sendTaskNotice PushTaskRewardMsg failed, err:[%+v]", err)
			_ = s.feishu.SendWarning(fmt.Sprintf("【荣耀世界】推送任务完成弹窗、公屏、TT助手推送失败，msg:[%+v]，channelId:[%+v]，uid:[%+v]",
				msg, channelId, uid))
		}
	}
}

// ReSendPkg 补发指定订单包裹
func (s *settlementRewardMgr) ReSendPkg(ctx context.Context, orderId string) error {
	log.InfoWithCtx(ctx, "ReSendPkg orderId:[%+v]", orderId)

	// 查到对应的记录
	record, err := s.fragRewardMgr.GetRecordByOrderId(ctx, orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReSendPkg GetRecordByOrderId failed, orderId:[%+v], err:[%+v]", orderId, err)
		return err
	}

	// 如果为空直接返回
	if record == nil || record.OrderId == "" {
		return nil
	}

	// 获取背包信息
	pkgIdMap, businessId, businessKey, err := s.getPkgConf(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReSendPkg getPkgConf failed, err:[%+v]", err)
		return err
	}

	// 根据记录进行补发放
	log.InfoWithCtx(ctx, "ReSendPkg record:[%+v], pkgIdMap:[%+v], businessId:[%+v], businessKey:[%+v]",
		record, pkgIdMap, businessId, businessKey)
	err = s.sendFragPkg(ctx, []*GloryWorldFragmentReward{record}, pkgIdMap, businessId, businessKey)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReSendPkg sendFragPkg failed, record:[%+v], pkgIdMap:[%+v], businessId:[%+v], "+
			"businessKey:[%+v], err:[%+v]", record, pkgIdMap, businessId, businessKey, err)
		return err
	}

	_ = s.feishu.SendInfo(fmt.Sprintf("【荣耀世界】补单接口发放星钻成功，订单ID:[%+v]", record.OrderId))
	return nil
}

// AutoResendPkg 定时任务补发星钻碎片
func (s *settlementRewardMgr) AutoResendPkg(ctx context.Context) {
	log.DebugWithCtx(ctx, "AutoResendPkg start !")

	// 获取五分钟前没有成功发放的星钻获取记录
	now := time.Now()
	fiveMinuteAgoTime := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()-5, now.Second(), 0, time.Local)
	rewards, err := s.fragRewardMgr.GetRecordNotSendWithEndTime(ctx, fiveMinuteAgoTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "AutoResendPkg GetRecordNotSendWithEndTime failed, err:[%+v]", err)
		_ = s.feishu.SendError(fmt.Sprintf("【荣耀世界】自动补发星钻获取记录失败，错误信息:[%+v]", err))
		return
	}

	// 如果没有需要补发的则直接返回
	if len(rewards) == 0 {
		return
	}

	// 获取背包信息
	pkgIdMap, businessId, businessKey, err := s.getPkgConf(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "AutoResendPkg getPkgConf failed, err:[%+v]", err)
		_ = s.feishu.SendError(fmt.Sprintf("【荣耀世界】自动补发星钻获取包裹信息失败，错误信息:[%+v]", err))
		return
	}

	// 根据记录进行补发放
	err = s.sendFragPkg(ctx, rewards, pkgIdMap, businessId, businessKey)
	if err != nil {
		log.ErrorWithCtx(ctx, "AutoResendPkg sendFragPkg failed, rewards:[%+v], pkgIdMap:[%+v], businessId:[%+v], "+
			"businessKey:[%+v], err:[%+v]", rewards, pkgIdMap, businessId, businessKey, err)
		_ = s.feishu.SendError(fmt.Sprintf("【荣耀世界】自动补发星钻发放包裹失败，错误信息:[%+v]", err))
		return
	}
}

// AddFragment 测试接口，直接手动添加星钻
func (s *settlementRewardMgr) AddFragment(ctx context.Context, uid uint32, fragType glory_reward.GloryFragmentType, num uint32) error {
	log.InfoWithCtx(ctx, "AddFragment with uid:[%+v], fragType:[%+v], num:[%+v]", uid, fragType, num)

	if !s.dyConf.IsTestMode() { // 限制接口调用
		log.ErrorWithCtx(ctx, "AddFragment without test mode!")
		return nil
	}

	// 获取背包信息
	pkgIdMap, businessId, businessKey, err := s.getPkgConf(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "AutoResendPkg getPkgConf failed, err:[%+v]", err)
		_ = s.feishu.SendError(fmt.Sprintf("【荣耀世界】自动补发星钻获取包裹信息失败，错误信息:[%+v]", err))
		return err
	}
	// 组装记录
	record := &GloryWorldFragmentReward{
		OrderId:            fmt.Sprintf("%+v_%+v_TEST_%+v_%+v", businessId, pkgIdMap[uint32(fragType)], uid, time.Now().Unix()),
		Uid:                uid,
		FragmentType:       uint8(fragType),
		FragmentNum:        num,
		FragmentRewardType: uint16(glory_reward.FragmentRewardType_FragmentRewardTypeTest),
		RewardType:         uint16(glory_reward.FragmentRewardType_FragmentRewardTypeTest),
		RewardRemark:       "手动添加",
		IsSend:             false,
		CreateTime:         time.Now(),
		UpdateTime:         time.Now(),
	}

	// 记录入库
	records := s.controlNumberSingleReward(ctx, record)
	if err = s.fragRewardMgr.BatchAddFragReward(ctx, nil, records); err != nil {
		log.ErrorWithCtx(ctx, "AddFragment BatchAddFragReward failed, records:[%+v], err:[%+v]", records, err)
		return err
	}

	// 发放
	if err = s.sendFragPkg(ctx, records, pkgIdMap, businessId, businessKey); err != nil {
		log.ErrorWithCtx(ctx, "AddFragment sendFragPkg failed, records:[%+v], err:[%+v]", records, err)
		return err
	}

	return nil
}

// PushTaskPopWindow 测试接口，直接推送任务完成弹窗
func (s *settlementRewardMgr) PushTaskPopWindow(ctx context.Context, uid uint32, channelId uint32, fragType []glory_reward.RewardType) error {
	log.InfoWithCtx(ctx, "PushTaskPopWindow with uid:[%+v], channelId:[%+v], fragType:[%+v]", uid, channelId, fragType)

	if !s.dyConf.IsTestMode() { // 限制接口调用
		log.ErrorWithCtx(ctx, "AddFragment without test mode!")
		return nil
	}

	// 获取弹窗配置
	pushConfMap := s.dyConf.GetPushConf()
	if len(pushConfMap) != 2 {
		_ = s.feishu.SendError(fmt.Sprintf("【荣耀世界】任务弹窗资源未进行配置，请检查配置文件！pushConfMap:[%+v]", pushConfMap))
	}

	// 获取登陆信息
	marketId, os, err := s.client.GetUserLoginInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushTaskPopWindow GetUserLoginInfo failed, uid:[%+v] err:[%+v]", uid, err)
		return err
	}

	// 查询url
	_, url := s.dyConf.GetEnterInfo(uid, marketId, os, false)
	if url == "" {
		log.ErrorWithCtx(ctx, "PushTaskPopWindow GetEnterInfo failed, marketId:[%+v] os:[%+v]", marketId, os)
		return err
	}

	// 根据类型组装推送
	msgList := make([]rpc.TaskRewardMsg, 0)
	for _, rewardType := range fragType {
		var fragmentType uint8
		var fragmentNum uint32
		var rewardRemark string
		switch rewardType {
		case glory_reward.RewardType_RewardTypeTaskFirstSend:
			fragmentType = 1
			fragmentNum = 2
			rewardRemark = "零的突破"
		case glory_reward.RewardType_RewardTypeTaskGiftFirstSend:
			fragmentType = 1
			fragmentNum = 100
			rewardRemark = "首送挑战"
		case glory_reward.RewardType_RewardTypeTaskSevenDays:
			fragmentType = 1
			fragmentNum = 10
			rewardRemark = "七天挑战"
		case glory_reward.RewardType_RewardTypeTaskChallenge:
			fragmentType = 2
			fragmentNum = 1000
			rewardRemark = "十万挑战"
		case glory_reward.RewardType_RewardTypeTaskMillionChallenge:
			fragmentType = 2
			fragmentNum = 10000
			rewardRemark = "百万挑战"
		default:
		}
		notice, helperNotice := s.getTaskMsgNotice(glory_reward.GloryFragmentType(fragmentType), rewardRemark, fragmentNum)
		banner := fmt.Sprintf(PopWindowFMT, rewardRemark)
		popConf := pushConfMap[uint32(fragmentType)]
		msg := logicPb.GloryRewardChannelTaskMsg{
			Uid:              uid,
			Type:             logicPb.GloryFragmentType(fragmentType),
			Url:              popConf.UrlField,
			Md5:              popConf.Md5Field,
			SpecialEffectUrl: popConf.SpecialEffectUrlField,
			SpecialEffectMd5: popConf.SpecialEffectMd5Field,
			Banner:           banner,
			Num:              fragmentNum,
			DisplayTime:      popConf.DisplayTimeField,
			TaskList:         []uint32{},
			Notice:           notice,
			EnterUrl:         url,
			ShineUrl:         popConf.ShineUrlField,
			CircleLightUrl:   popConf.CircleLightUrlField,
		}
		taskMsg := rpc.TaskRewardMsg{Channel: msg, HelperNotice: helperNotice}
		msgList = append(msgList, taskMsg)
	}
	s.batchPushTaskRewardMsg(ctx, msgList, channelId, uid)
	return nil
}

// CountPresentOrderNumAndValueByTime 统计指定时间内的礼物订单数（对账接口）
func (s *settlementRewardMgr) CountPresentOrderNumAndValueByTime(ctx context.Context, startTime, endTime int64) (
	count uint32, value uint32, err error) {

	// 转换为时间打印日志
	start := time.Unix(startTime, 0).Unix()
	end := time.Unix(endTime, 0).Unix()

	log.DebugWithCtx(ctx, "CountPresentOrderNumAndValueByTime with startTime:[%+v], endTime:[%+v]", start, end)
	// 查询详情
	count, value, err = s.giftFlowStore.GetFlowInfoWithPeriod(ctx, startTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "CountPresentOrderNumAndValueByTime GetFlowInfoWithPeriod failed, err:[%+v]", err)
		return count, value, err
	}
	log.DebugWithCtx(ctx, "CountPresentOrderNumAndValueByTime count:[%+v], value:[%+v]", count, value)
	return count, value, nil
}

// GetPresentOrderIdsByTime 获取指定时间内的发放订单号（对账接口）
func (s *settlementRewardMgr) GetPresentOrderIdsByTime(ctx context.Context, startTime, endTime int64) (orderIds []string, err error) {
	result := make([]string, 0)

	// 转换为时间打印日志
	start := time.Unix(startTime, 0).Unix()
	end := time.Unix(endTime, 0).Unix()

	// 查询详情
	log.DebugWithCtx(ctx, "GetPresentOrderIdsByTime with startTime:[%+v], endTime:[%+v]", start, end)
	orders, err := s.giftFlowStore.GetOrderListWithPeriod(ctx, startTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentOrderIdsByTime GetOrderListWithPeriod failed, err:[%+v]", err)
		return result, err
	}

	return orders, nil
}

// PresentReSendPkg 补发指定礼物订单（对账接口）
func (s *settlementRewardMgr) PresentReSendPkg(ctx context.Context, orderId string) error {

	// 获取对应的礼物订单信息
	orderInfo, err := s.client.GetPresentOrderInfoById(ctx, orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "PresentReSendPkg GetPresentOrderInfoById failed, orderId:[%+v], err:[%+v]", orderId, err)
		return err
	}
	if orderInfo == nil || orderInfo.OrderId == "" {
		log.ErrorWithCtx(ctx, "PresentReSendPkg orderInfo is nil, orderId:[%+v], orderInfo:[%+v]", orderId, orderInfo)
		return protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "查询订单信息失败！")
	}

	// 判断当前订单是否已经发放
	orderTime := time.Unix(int64(orderInfo.GetCreateTime()), 0)
	flowInfo, err := s.giftFlowStore.GetFlowByOrderId(ctx, orderId, orderTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "PresentReSendPkg GetFlowByOrderId failed, orderId:[%+v], orderTime:[%+v], err:[%+v]", orderId, orderTime, err)
		return err
	}
	if flowInfo != nil && flowInfo.IsOpen {
		log.ErrorWithCtx(ctx, "PresentReSendPkg flow already send, orderId:[%+v]", orderId)
		return nil
	}

	// 获取礼物配置
	config := s.presentMemCache.GetConfigById(orderInfo.ItemId)
	if config == nil {
		log.ErrorWithCtx(ctx, "PresentReSendPkg config is nil, orderId:[%+v], orderInfo:[%+v]", orderId, orderInfo)
		return protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "查询礼物配置失败！")
	}

	// 组装流水
	flow := &GloryWorldGiftFlow{
		OrderId:    orderInfo.OrderId,
		Uid:        orderInfo.GetFromUid(),
		TargetUid:  orderInfo.GetToUid(),
		SendTime:   int64(orderInfo.GetCreateTime()),
		ItemId:     orderInfo.GetItemId(),
		ItemCount:  orderInfo.GetItemCount(),
		Price:      config.GetPrice(),
		ChannelId:  orderInfo.GetChanelId(),
		ItemSource: orderInfo.GetPresentSource(),
		IsOpen:     true,
		UpdateTime: time.Now(),
	}

	// 校验订单是否需要发放
	if !s.IsValidToAddFragment(ctx, orderInfo.ItemId, uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN), orderInfo.GetPresentSource(),
		config.GetExtend().GetTag(), config.GetPrice()) {
		log.ErrorWithCtx(ctx, "PresentReSendPkg IsValidToAddFragment failed, orderInfo:[%+v]", orderInfo)
		flow.IsOpen = false
		err = s.giftFlowStore.InsertGiftFlow(ctx, nil, flow)
		if err != nil {
			log.ErrorWithCtx(ctx, "PresentReSendPkg InsertGiftFlow failed, flow:[%+v], err:[%+v]", flow, err)
			return err
		}
		return nil
	}

	// 调用星钻处理流程
	err, retry := s.ProcessEventGift(ctx, flow, orderInfo.GetChanelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "PresentReSendPkg ProcessEventGift failed, flow:[%+v], err:[%+v]", flow, err)
		if retry {
			return err
		}
	}

	return nil
}

func (s *settlementRewardMgr) InsertGiftFlow(ctx context.Context, flow *GloryWorldGiftFlow) error {
	return s.giftFlowStore.InsertGiftFlow(ctx, nil, flow)
}
