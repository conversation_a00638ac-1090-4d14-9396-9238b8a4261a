package utils

import "time"

const (
	anchorCheckDay = 7 // 主播考核期限
)

//获取某个开始时间戳的过期时间戳
func GetAnchorCheckExpireTime(timestamp int64) int64 {
	t := time.Unix(timestamp, 0)
	tTodayZero := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)
	tExpireTime := tTodayZero.AddDate(0, 0, anchorCheckDay+1)
	return tExpireTime.Unix()
}

//反向GetAnchorCheckExpireTime，获取当前时间已经过期的最晚创建时间
func GetCreateTimeAtExpireTime(timestamp int64) int64 {
	t := time.Unix(timestamp, 0)
	tTodayZero := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)
	tMinCreateTime := tTodayZero.AddDate(0, 0, -anchorCheckDay)
	return tMinCreateTime.Unix()
}
