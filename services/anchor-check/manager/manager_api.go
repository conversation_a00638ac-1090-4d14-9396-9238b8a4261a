package manager

import (
	context "context"
	pb "golang.52tt.com/protocol/services/anchor-check"
	model "golang.52tt.com/services/anchor-check/model"
)

type IAnchorCheckMgr interface {
	GetAudioLiveCheckResult()
	GetStore() model.IStore
	StartTimer(ctx context.Context)
	CheckInWhite(ctx context.Context, uid uint32) (*pb.WhiteData, error)
	GetCheckList(req *pb.AnchorCheckGetCheckListReq) ([]*model.AnchorCheck, uint32)
	GetExpireListWithTimeRange(beginTime uint32, endTime uint32) []uint32
	GetLastCreateScore(uid uint32) *model.AnchorCheck
	GetMaxRecordTime() int
	Init() error
	SendTTMsg(ctx context.Context, uid uint32, content, hlight, url string) error
	SendWhiteExpireMsg(ctx context.Context)
	SendWillExpireMsg(ctx context.Context)
	SetCheckData(ctx context.Context, info *model.AnchorCheck) error
	SetWhite(ctx context.Context, uid uint32, operator string) error
	ShutDown()
	StartRecord(ctx context.Context, uid uint32, roomId string, streamId string, sourceType uint32) (string, error)
	StopRecord(ctx context.Context, uid uint32, taskId string, submit bool) error
	SubmitWhite(ctx context.Context, uid uint32) error
	SyncZegoMp3()
	GetWhiteList(ctx context.Context, offset uint32, limit uint32, uid uint32, beginTime uint32, endTime uint32, status int32) ([]model.AnchorCheckWhite, uint32)
	GetLastAnchorCheckUpgrade(ctx context.Context, in *pb.UidReq) (*pb.GetLastAnchorCheckUpgradeResp, error)
	ApplyAnchorCheckUpgrade(ctx context.Context, in *pb.UidReq) (*pb.ApplyAnchorCheckUpgradeReqResp, error)
	GetAnchorCheckHistory(ctx context.Context, in *pb.GetAnchorCheckHistoryReq) (*pb.GetAnchorCheckHistoryResp, error)
}
