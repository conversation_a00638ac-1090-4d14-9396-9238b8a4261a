package manager

import (
	"context"
	"errors"
	backpackBase "golang.52tt.com/clients/backpack-base"
	"reflect"
	"testing"
	"time"

	"bou.ke/monkey"
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/clients/account"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	backpackSender "golang.52tt.com/clients/backpack-sender"
	"golang.52tt.com/clients/channel"
	channelmsgexpress "golang.52tt.com/clients/channel-msg-express"
	"golang.52tt.com/clients/darkserver"
	"golang.52tt.com/clients/nobility"
	numeri "golang.52tt.com/clients/numeric"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	unifiedPay "golang.52tt.com/clients/unified_pay"
	userPresent "golang.52tt.com/clients/userpresent"
	ukw "golang.52tt.com/clients/you-know-who"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channel-red-packet"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/cache"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/conf"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/mysql"
)

func init() {
	log.SetLevel(log.DebugLevel)
}

func TestNewRedPacketMgr(t *testing.T) {
	type args struct {
		sc    *conf.ServiceConfigT
		cache *cache.RedPacketCache
		store *mysql.Store
	}
	tests := []struct {
		name string
		args args
		want *RedPacketMgr
	}{
		// TODO: Add test cases.
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got, _ := NewRedPacketMgr(ctx, tt.args.sc, tt.args.cache, tt.args.store); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewRedPacketMgr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRedPacketMgr_GetBusinessConf(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	tests := []struct {
		name   string
		fields fields
		want   conf.IBusinessConfManager
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			if got := m.GetBusinessConf(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RedPacketMgr.GetBusinessConf() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRedPacketMgr_GetCache(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	tests := []struct {
		name   string
		fields fields
		want   cache.IRedPacketCache
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			if got := m.GetCache(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RedPacketMgr.GetCache() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRedPacketMgr_GetRedPacketOrderById(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx     context.Context
		orderId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.RedPacketInfo
		want1   bool
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			got, got1, err := m.GetRedPacketOrderById(tt.args.ctx, tt.args.orderId)
			if (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.GetRedPacketOrderById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RedPacketMgr.GetRedPacketOrderById() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("RedPacketMgr.GetRedPacketOrderById() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestRedPacketMgr_CheckIfCanSendRedPacket(t *testing.T) {

	genMgrMock(t)

	m := map[uint32]bool{1: true}

	whiteSwitch := true
	mapWhiteUid := map[uint32]bool{1: true, 3: true}
	val := uint32(10)
	DailyLimit := uint32(0)
	DailyLimit2 := uint32(110)

	gomock.InOrder(
		mockBusinessConf.EXPECT().GetRPBlackChannelMap().Return(m),

		mockBusinessConf.EXPECT().GetRPBlackChannelMap().Return(m),
		mockBusinessConf.EXPECT().GetEntryDisable().Return(true),

		mockBusinessConf.EXPECT().GetRPBlackChannelMap().Return(m),
		mockBusinessConf.EXPECT().GetEntryDisable().Return(false),
		mockBusinessConf.EXPECT().GetRPWhiteUidMap().Return(whiteSwitch, mapWhiteUid),

		mockBusinessConf.EXPECT().GetRPBlackChannelMap().Return(m),
		mockBusinessConf.EXPECT().GetEntryDisable().Return(false),
		mockBusinessConf.EXPECT().GetRPWhiteUidMap().Return(whiteSwitch, mapWhiteUid),
		mockCache.EXPECT().GetUserDailySendPrice(gomock.Any(), gomock.Any()).Return(val, errors.New("!")),

		mockBusinessConf.EXPECT().GetRPBlackChannelMap().Return(m),
		mockBusinessConf.EXPECT().GetEntryDisable().Return(false),
		mockBusinessConf.EXPECT().GetRPWhiteUidMap().Return(whiteSwitch, mapWhiteUid),
		mockCache.EXPECT().GetUserDailySendPrice(gomock.Any(), gomock.Any()).Return(val, nil),
		mockBusinessConf.EXPECT().GetRPSendPriceDailyLimit().Return(DailyLimit),

		mockBusinessConf.EXPECT().GetRPBlackChannelMap().Return(m),
		mockBusinessConf.EXPECT().GetEntryDisable().Return(false),
		mockBusinessConf.EXPECT().GetRPWhiteUidMap().Return(whiteSwitch, mapWhiteUid),
		mockCache.EXPECT().GetUserDailySendPrice(gomock.Any(), gomock.Any()).Return(val, nil),
		mockBusinessConf.EXPECT().GetRPSendPriceDailyLimit().Return(DailyLimit2),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx       context.Context
		uid       uint32
		channelId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				uid:       1,
				channelId: 1,
			},

			want:    false,
			wantErr: true,
		},
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				uid:       2,
				channelId: 2,
			},

			want:    false,
			wantErr: true,
		},
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				uid:       2,
				channelId: 2,
			},

			want:    false,
			wantErr: true,
		},
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				uid:       3,
				channelId: 2,
			},

			want:    false,
			wantErr: true,
		},
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				uid:       3,
				channelId: 2,
			},

			want:    false,
			wantErr: true,
		},
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				uid:       3,
				channelId: 2,
			},

			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			got, err := m.CheckIfCanSendRedPacket(tt.args.ctx, tt.args.uid, tt.args.channelId)
			if (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.CheckIfCanSendRedPacket() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("RedPacketMgr.CheckIfCanSendRedPacket() = %v, want %v", got, tt.want)
			}
		})
	}
}

// go test -timeout 30s -run ^TestRedPacketMgr_SendRedPacket$ golang.52tt.com/services/channel-red-packet/channel-red-packet/manager -v -count=1
func TestRedPacketMgr_SendRedPacket(t *testing.T) {

	genMgrMock(t)

	in := &pb.SendRedPacketReq{ChannelId: 1, RedPacketId: 1, OpUid: 1}
	now := time.Now()
	rpconf := &pb.RedPacketConf{BeginTime: uint32(now.Unix() - 10), EndTime: uint32(now.Unix()) + 10}
	rplist := []*pb.RedPacketInfo{}
	gomock.InOrder(
		mockCache.EXPECT().GetRedPacketConfById(gomock.Any()).Return(rpconf, true, nil),
		mockCache.EXPECT().GetChannelRedPacketList(gomock.Any()).Return(rplist, true, nil),
		mockStore.EXPECT().CreateRedPacketOrder(gomock.Any(), gomock.Any()).Return(errors.New("")),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.SendRedPacketReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx: context.TODO(),
				in:  in,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			if err := m.SendRedPacket(tt.args.ctx, tt.args.in); (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.SendRedPacket() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRedPacketMgr_ChannelRedPacketUseListChangeNotify(t *testing.T) {

	genMgrMock(t)

	pbList := []*pb.RedPacketInfo{}
	gomock.InOrder(
		mockCache.EXPECT().DelChannelRedPacketList(gomock.Any()).Return(errors.New("!")),
		mockCache.EXPECT().GetChannelRedPacketList(gomock.Any()).Return(pbList, true, errors.New("!")),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx       context.Context
		channelId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				channelId: 2,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			if err := m.ChannelRedPacketUseListChangeNotify(tt.args.ctx, tt.args.channelId); (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.ChannelRedPacketUseListChangeNotify() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRedPacketMgr_getChannelNewestOrderByStatus(t *testing.T) {

	genMgrMock(t)

	order := &mysql.RedPacketOrder{}

	gomock.InOrder(
		mockStore.EXPECT().GetChannelNewestOrderByStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, false, errors.New("!")),
		mockStore.EXPECT().GetChannelNewestOrderByStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(order, true, nil),

		mockStore.EXPECT().GetChannelNewestOrderByStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(order, false, nil),
		mockStore.EXPECT().GetChannelNewestOrderByStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, false, errors.New("!")),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx       context.Context
		tx        *gorm.DB
		channelId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *mysql.RedPacketOrder
		want1   bool
		wantErr bool
	}{
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				channelId: 2,
			},
			//want:    order,
			want1:   false,
			wantErr: true,
		},
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				channelId: 2,
			},
			want:    order,
			want1:   true,
			wantErr: false,
		},
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				channelId: 2,
			},
			//	want:    order,
			want1:   false,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			got, got1, err := m.getChannelNewestOrderByStatus(tt.args.ctx, tt.args.tx, tt.args.channelId)
			if (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.getChannelNewestOrderByStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RedPacketMgr.getChannelNewestOrderByStatus() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("RedPacketMgr.getChannelNewestOrderByStatus() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestRedPacketMgr_AddRedPacketToUse(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx         context.Context
		orderId     string
		channelId   uint32
		uid         uint32
		redPacketId uint32
		outsideTime time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			if err := m.AddRedPacketToUse(tt.args.ctx, tt.args.orderId, tt.args.channelId, tt.args.uid, tt.args.redPacketId, tt.args.outsideTime); (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.AddRedPacketToUse() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCalcGiftAwardRandomCnt(t *testing.T) {
	type args struct {
		ctx        context.Context
		id         uint32
		opt        *mysql.GiftConfOpt
		totalPrice uint32
	}
	tests := []struct {
		name    string
		args    args
		want    map[uint32]uint32
		want1   uint32
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, err := CalcGiftAwardRandomCnt(tt.args.ctx, tt.args.id, tt.args.opt, tt.args.totalPrice)
			if (err != nil) != tt.wantErr {
				t.Errorf("CalcGiftAwardRandomCnt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CalcGiftAwardRandomCnt() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("CalcGiftAwardRandomCnt() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestDivideGiftAwardToUser(t *testing.T) {

	type args struct {
		uidList       []uint32
		mapGiftId2Cnt map[uint32]uint32
	}
	tests := []struct {
		name string
		args args
		want map[uint32]map[uint32]uint32
	}{
		{
			name: "DivideGiftAwardToUser",
			args: args{},
			want: map[uint32]map[uint32]uint32{},
		},
		{
			name: "DivideGiftAwardToUser",
			args: args{
				uidList: []uint32{1, 2},
				mapGiftId2Cnt: map[uint32]uint32{
					1: 10,
				},
			},

			want: map[uint32]map[uint32]uint32{
				1: {
					1: 5,
				},
				2: {
					1: 5,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := DivideGiftAwardToUser(tt.args.uidList, tt.args.mapGiftId2Cnt); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DivideGiftAwardToUser() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRedPacketMgr_GetChannelRedPacketUseList(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx       context.Context
		channelId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.RedPacketInfo
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			got, err := m.GetChannelRedPacketUseList(tt.args.ctx, tt.args.channelId)
			if (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.GetChannelRedPacketUseList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RedPacketMgr.GetChannelRedPacketUseList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRedPacketMgr_reloadChannelRedPacketUseList(t *testing.T) {
	//ts := time.Now().AddDate(1, 1, 1)

	genMgrMock(t)

	monkey.Patch(time.Now, func() time.Time {
		return time.Unix(**********, 0)
	})

	list := []*mysql.RedPacketOrder{}
	list1 := []*mysql.RedPacketOrder{
		{
			RedPacketId: 1,
			EndTime:     time.Unix(1796375464, 0),
		},
	}
	rsp := []*pb.RedPacketInfo{}
	pbList := []*pb.RedPacketConf{
		{
			RedPacketId: 1,
			TotalPrice:  1,
		},
	}

	// red_packet_conf:<red_packet_id:1 total_price:1 > begin_time:2288912640 end_time:1796375464
	info := &pb.RedPacketInfo{
		RedPacketConf: &pb.RedPacketConf{
			RedPacketId: 1,
			TotalPrice:  1,
			BeginTime:   2288912640,
			EndTime:     1796375464,
		},
	}
	rsp2 := make([]*pb.RedPacketInfo, 0)
	rsp2 = append(rsp2, info)

	gomock.InOrder(
		mockStore.EXPECT().GetChannelOrderListByStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("!")),

		mockStore.EXPECT().GetChannelOrderListByStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
		mockStore.EXPECT().GetChannelOrderListByStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("!")),

		mockStore.EXPECT().GetChannelOrderListByStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(list, nil),
		mockStore.EXPECT().GetChannelOrderListByStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(list, nil),
		mockCache.EXPECT().SetChannelRedPacketList(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("!")),

		mockStore.EXPECT().GetChannelOrderListByStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(list, nil),
		mockStore.EXPECT().GetChannelOrderListByStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(list1, nil),
		//mockCache.EXPECT().SetChannelRedPacketList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().GetAllRedPacketConf().Return(pbList, nil),
		mockCache.EXPECT().SetChannelRedPacketList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx       context.Context
		channelId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.RedPacketInfo
		wantErr bool
	}{
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx: context.TODO(),
			},
			want:    rsp,
			wantErr: true,
		},
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx: context.TODO(),
			},
			want:    rsp,
			wantErr: true,
		},
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx: context.TODO(),
			},
			want:    rsp,
			wantErr: true,
		},
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx: context.TODO(),
			},
			want:    rsp2,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			got, err := m.reloadChannelRedPacketUseList(tt.args.ctx, tt.args.channelId)
			if (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.reloadChannelRedPacketUseList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				//t.Errorf("RedPacketMgr.reloadChannelRedPacketUseList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRedPacketMgr_SendPackage(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx     context.Context
		uid     uint32
		bgId    uint32
		num     uint32
		orderId string
		t       time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			if err := m.SendPackage(tt.args.ctx, tt.args.uid, tt.args.bgId, tt.args.num, tt.args.orderId, tt.args.t); (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.SendPackage() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_getLastMonthTime(t *testing.T) {

	now := time.Now()
	ts := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

	type args struct {
		now time.Time
	}
	tests := []struct {
		name string
		args args
		want time.Time
	}{
		{
			args: args{now: ts},
			want: ts.AddDate(0, -1, 0),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getLastMonthTime(tt.args.now); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getLastMonthTime() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_genRedPacketOrderId(t *testing.T) {

	monkey.Patch(time.Now, func() time.Time {
		return time.Unix(**********, 0)
	})

	type args struct {
		channelId   uint32
		uid         uint32
		redPacketId uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{
				channelId:   1,
				uid:         1,
				redPacketId: 1,
			},
			want: "RP_29090B0610C939D4AB1257DFB5F1197B",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := genRedPacketOrderId(tt.args.channelId, tt.args.uid, tt.args.redPacketId); got != tt.want {
				t.Errorf("genRedPacketOrderId() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_genAwardOrderId(t *testing.T) {
	type args struct {
		rpOrderId  string
		businessId uint32
		uid        uint32
		bgId       uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := genAwardOrderId(tt.args.rpOrderId, tt.args.businessId, tt.args.uid, tt.args.bgId); got != tt.want {
				t.Errorf("genAwardOrderId() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRedPacketMgr_GetRedPacketOrderTotal(t *testing.T) {

	genMgrMock(t)

	info := &mysql.TotalInfo{}
	gomock.InOrder(
		mockStore.EXPECT().GetOrderTotalInfo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(info, nil),
		mockStore.EXPECT().GetOrderTotalInfo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(info, nil),

		mockStore.EXPECT().GetOrderTotalInfo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(info, errors.New("1")),
		mockStore.EXPECT().GetOrderTotalInfo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(info, nil),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx       context.Context
		beginTime time.Time
		endTime   time.Time
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		wantCnt        uint32
		wantTotalPrice uint32
		wantErr        bool
	}{
		{
			name: "GetRedPacketOrderTotal",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				beginTime: time.Now().Add(10),
				endTime:   time.Now(),
			},
			wantCnt:        0,
			wantTotalPrice: 0,
			wantErr:        false,
		},
		{
			name: "GetRedPacketOrderTotal",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				beginTime: time.Now(),
				endTime:   time.Now().AddDate(0, 1, 0),
			},
			wantCnt:        0,
			wantTotalPrice: 0,
			wantErr:        false,
		},

		{
			name: "GetRedPacketOrderTotal",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				beginTime: time.Now(),
				endTime:   time.Now().AddDate(0, 1, 0),
			},
			wantCnt:        0,
			wantTotalPrice: 0,
			wantErr:        true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			gotCnt, gotTotalPrice, err := m.GetRedPacketOrderTotal(tt.args.ctx, tt.args.beginTime, tt.args.endTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.GetRedPacketOrderTotal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotCnt != tt.wantCnt {
				t.Errorf("RedPacketMgr.GetRedPacketOrderTotal() gotCnt = %v, want %v", gotCnt, tt.wantCnt)
			}
			if gotTotalPrice != tt.wantTotalPrice {
				t.Errorf("RedPacketMgr.GetRedPacketOrderTotal() gotTotalPrice = %v, want %v", gotTotalPrice, tt.wantTotalPrice)
			}
		})
	}
}

func TestRedPacketMgr_GetRedPacketConsumeOrderIds(t *testing.T) {

	genMgrMock(t)

	list := []string{}
	gomock.InOrder(

		mockStore.EXPECT().GetConsumeOrderIdList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(list, errors.New("1")),

		mockStore.EXPECT().GetConsumeOrderIdList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(list, nil),
		mockStore.EXPECT().GetConsumeOrderIdList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(list, nil),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx       context.Context
		beginTime time.Time
		endTime   time.Time
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantList []string
		wantErr  bool
	}{
		{
			name: "GetRedPacketConsumeOrderIds",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				beginTime: time.Now(),
				endTime:   time.Now().AddDate(0, 1, 0),
			},
			wantList: []string{},
			wantErr:  true,
		},
		{
			name: "GetRedPacketConsumeOrderIds",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				beginTime: time.Now(),
				endTime:   time.Now().AddDate(0, 1, 0),
			},
			wantList: []string{},
			wantErr:  false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			gotList, err := m.GetRedPacketConsumeOrderIds(tt.args.ctx, tt.args.beginTime, tt.args.endTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.GetRedPacketConsumeOrderIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotList, tt.wantList) {
				t.Errorf("RedPacketMgr.GetRedPacketConsumeOrderIds() = %v, want %v", gotList, tt.wantList)
			}
		})
	}
}

func TestRedPacketMgr_GetRedPacketAwardTotal(t *testing.T) {

	genMgrMock(t)

	info := &mysql.TotalInfo{}
	gomock.InOrder(
		mockStore.EXPECT().GetAwardTotalInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(info, nil),
		mockStore.EXPECT().GetAwardTotalInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(info, nil),

		mockStore.EXPECT().GetAwardTotalInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(info, errors.New("1")),
		//mockStore.EXPECT().GetAwardTotalInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(info, nil),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx       context.Context
		beginTime time.Time
		endTime   time.Time
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		wantCnt        uint32
		wantTotalPrice uint32
		wantErr        bool
	}{
		{
			name: "GetRedPacketAwardTotal",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				beginTime: time.Now(),
				endTime:   time.Now().AddDate(0, 1, 0),
			},
			wantCnt:        0,
			wantTotalPrice: 0,
			wantErr:        false,
		},
		{
			name: "GetRedPacketAwardTotal",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				beginTime: time.Now(),
				endTime:   time.Now().AddDate(0, 1, 0),
			},
			wantCnt:        0,
			wantTotalPrice: 0,
			wantErr:        true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			gotCnt, gotTotalPrice, err := m.GetRedPacketAwardTotal(tt.args.ctx, tt.args.beginTime, tt.args.endTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.GetRedPacketAwardTotal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotCnt != tt.wantCnt {
				t.Errorf("RedPacketMgr.GetRedPacketAwardTotal() gotCnt = %v, want %v", gotCnt, tt.wantCnt)
			}
			if gotTotalPrice != tt.wantTotalPrice {
				t.Errorf("RedPacketMgr.GetRedPacketAwardTotal() gotTotalPrice = %v, want %v", gotTotalPrice, tt.wantTotalPrice)
			}
		})
	}
}

func TestRedPacketMgr_GetRedPacketAwardOrderIds(t *testing.T) {

	genMgrMock(t)

	list := []string{}
	gomock.InOrder(

		mockStore.EXPECT().GetAwardOrderIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(list, errors.New("1")),

		mockStore.EXPECT().GetAwardOrderIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(list, nil),
		mockStore.EXPECT().GetAwardOrderIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(list, nil),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx       context.Context
		beginTime time.Time
		endTime   time.Time
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantList []string
		wantErr  bool
	}{
		{
			name: "GetRedPacketAwardOrderIds",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				beginTime: time.Now(),
				endTime:   time.Now().AddDate(0, 1, 0),
			},
			wantList: []string{},
			wantErr:  true,
		},
		{
			name: "GetRedPacketAwardOrderIds",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:       context.TODO(),
				beginTime: time.Now(),
				endTime:   time.Now().AddDate(0, 1, 0),
			},
			wantList: []string{},
			wantErr:  false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			gotList, err := m.GetRedPacketAwardOrderIds(tt.args.ctx, tt.args.beginTime, tt.args.endTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.GetRedPacketAwardOrderIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotList, tt.wantList) {
				t.Errorf("RedPacketMgr.GetRedPacketAwardOrderIds() = %v, want %v", gotList, tt.wantList)
			}
		})
	}
}

func TestRedPacketMgr_ShutDown(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			m.ShutDown()
		})
	}
}

// go  test -timeout 30s -run ^TestRedPacketMgr_BatGetRedPacketChannel2$ golang.52tt.com/services/channel-red-packet/channel-red-packet/manager -v -count=1
func TestRedPacketMgr_BatGetRedPacketChannel2(t *testing.T) {
	return

	redisCfg := &config.RedisConfig{
		Host: "**************",
		Port: 6379,
	}
	cacheClient := cache.NewRedPacketCache(redisCfg)

	mysqlCfg := &config.MysqlConfig{
		Host:     "**************",
		Port:     3306,
		Protocol: "tcp",
		Database: "appsvr",
		UserName: "godman",
		Password: "thegodofman",
		Charset:  "utf8",
	}
	mysqlStore, err := mysql.NewMysql(mysqlCfg, mysqlCfg)
	if err != nil {
		t.Log(err)
		return
	}

	mgr := &RedPacketMgr{
		BusinessConf: conf.NewBusinessConfManager(),
		Cache:        cacheClient,
		Store:        mysqlStore,
	}

	t.Log(mgr.BatGetRedPacketChannel(context.TODO(), []uint32{1, 2}))
	t.Log(mgr.BatGetRedPacketChannel(context.TODO(), []uint32{3}))

}

// go  test -timeout 30s -run ^TestRedPacketMgr_BatGetRedPacketChannel$ golang.52tt.com/services/channel-red-packet/channel-red-packet/manager -v -count=1
func TestRedPacketMgr_BatGetRedPacketChannel(t *testing.T) {

	genMgrMock(t)

	cid2RpInfo := map[uint32][]*pb.RedPacketInfo{
		1: {
			{
				OrderId: "1",
			},
		},
	}
	noExistCids := []uint32{}
	noExistCids2 := []uint32{2}
	out := &pb.BatGetRedPacketChannelResp{ChannelIds: []uint32{1}}
	out2 := &pb.BatGetRedPacketChannelResp{ChannelIds: []uint32{2, 1}}

	genMgrMock(t)

	monkey.Patch(time.Now, func() time.Time {
		return time.Unix(**********, 0)
	})

	list := []*mysql.RedPacketOrder{}
	list1 := []*mysql.RedPacketOrder{
		{
			RedPacketId: 1,
			EndTime:     time.Unix(1796375464, 0),
		},
	}
	pbList := []*pb.RedPacketConf{
		{
			RedPacketId: 1,
			TotalPrice:  1,
		},
	}

	// red_packet_conf:<red_packet_id:1 total_price:1 > begin_time:2288912640 end_time:1796375464
	info := &pb.RedPacketInfo{
		RedPacketConf: &pb.RedPacketConf{
			RedPacketId: 1,
			TotalPrice:  1,
			BeginTime:   2288912640,
			EndTime:     1796375464,
		},
	}
	rsp2 := make([]*pb.RedPacketInfo, 0)
	rsp2 = append(rsp2, info)

	gomock.InOrder(
		mockCache.EXPECT().BatchGetChannelRedPacketList(gomock.Any()).Return(cid2RpInfo, noExistCids, nil),

		mockCache.EXPECT().BatchGetChannelRedPacketList(gomock.Any()).Return(cid2RpInfo, noExistCids2, nil),
		mockStore.EXPECT().GetChannelOrderListByStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(list, nil),
		mockStore.EXPECT().GetChannelOrderListByStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(list1, nil),
		mockCache.EXPECT().GetAllRedPacketConf().Return(pbList, nil),
		mockCache.EXPECT().SetChannelRedPacketList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeri.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx        context.Context
		channelIds []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.BatGetRedPacketChannelResp
		wantErr bool
	}{
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
			},
			args: args{
				ctx: context.TODO(),
			},
			want:    &pb.BatGetRedPacketChannelResp{},
			wantErr: false,
		},
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
			},
			args: args{
				ctx:        context.TODO(),
				channelIds: []uint32{1},
			},
			want:    out,
			wantErr: false,
		},
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
			},
			args: args{
				ctx:        context.TODO(),
				channelIds: []uint32{1, 2},
			},
			want:    out2,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericCli:    tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			got, err := m.BatGetRedPacketChannel(tt.args.ctx, tt.args.channelIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.BatGetRedPacketChannel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RedPacketMgr.BatGetRedPacketChannel() = %v, want %v", got, tt.want)
			}
		})
	}
}
