package manager

import (
	"context"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/clients/account"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app"
	channelPB "golang.52tt.com/protocol/app/channel"
	channelPb "golang.52tt.com/protocol/app/channel"
	channelredpacketlogic "golang.52tt.com/protocol/app/channel-red-packet-logic"
	gaPush "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/app/sync"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	pb "golang.52tt.com/protocol/services/channel-red-packet"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/cache"
	"golang.52tt.com/services/notify"

	uwkPB "golang.52tt.com/protocol/services/youknowwho"
)

/*
push cmd

CHANNEL_RED_PACKET_LIST_CHANGE = 206; // 房间红包列表变更 see channel-red-packet-logic_.proto RedPacketChangeOpt
CHANNEL_RED_PACKET_RAIN_CHANGE = 207; // 房间红包雨变更 see channel-red-packet-logic_.proto RedPacketRainChangeOpt
CHANNEL_RED_PACKET_SETTLE = 208; // 房间红包结算推送 see channel-red-packet-logic_.proto RedPacketSettleOpt

*/

// 红包队列变更push opt
func (m *RedPacketMgr) PushChannelRPListChangeOpt(ctx context.Context, channelId uint32, rpList []*pb.RedPacketInfo) error {
	if channelId == 0 {
		return nil
	}

	orderlist := []cache.UidOrderInfo{}
	uidList := make([]uint32, 0, len(rpList))
	for _, info := range rpList {
		uidList = append(uidList, info.GetSenderUid())
		orderlist = append(orderlist, cache.UidOrderInfo{Uid: info.SenderUid, OrderId: info.OrderId})
	}

	userMap, sErr := m.accountCli.GetUsersMap(ctx, uidList)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "PushChannelRPListChangeOpt fail to GetUsersMap. channelId:%+v, rpList:%+v, err:%v", channelId, rpList, sErr)
		return sErr
	}

	uwkInfos, err := m.Cache.BatchGetUKWCacheInfo(orderlist)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushChannelRPListChangeOpt fail to Cache.BatchGetUKWCacheInfo. channelId:%+v, rpList:%+v, err:%v", channelId, rpList, err)
	}

	channelType := m.getChannelType(channelId)

	opt := &channelredpacketlogic.RedPacketChangeOpt{
		ChannelId:         channelId,
		ServerTs:          uint32(time.Now().Unix()),
		List:              make([]*channelredpacketlogic.RedPacketInfo, 0, len(rpList)),
		SettleDurationSec: m.BusinessConf.GetSettleDurationSec(),
		RuleDesc:          m.GenRuleDesc(),
	}

	for _, info := range rpList {
		opt.List = append(opt.List, fillRedPacketUserInfoLogicPb(info, userMap[info.GetSenderUid()], uwkInfos[info.GetSenderUid()], channelType))
	}

	b, _ := proto.Marshal(opt)
	err = m.pushMsgToChannel(ctx, channelId, uint32(channelPB.ChannelMsgType_CHANNEL_RED_PACKET_LIST_CHANGE), "房间红包列表更新", b, []uint32{})
	if err != nil {
		log.ErrorWithCtx(ctx, "PushChannelRPListChangeOpt fail to pushMsgToChannel. info:%+v, err:%v", opt, err)
		return err
	}

	log.Infof("[房间红包列表变更] PushChannelRPListChangeOpt channelId:%v, opt:%s", channelId, utils.ToJson(opt))
	return nil
}

func (m *RedPacketMgr) GenRuleDesc() string {
	return fmt.Sprintf("须待在房间内才能获得奖励，少于%d人抢红包不瓜分奖励，豆退回至金主", m.BusinessConf.GetRPInvalidAwardUserLimit())
}

// 红包雨变更push opt
func (m *RedPacketMgr) PushChannelRPRainChangeOpt(ctx context.Context, channelId, changeType uint32, info *pb.RedPacketInfo) error {
	if channelId == 0 {
		return nil
	}

	user, sErr := m.accountCli.GetUser(ctx, info.GetSenderUid())
	if sErr != nil {
		log.ErrorWithCtx(ctx, "PushChannelRPRainChangeOpt fail to GetUser. channelId:%+v, changeType:%v, info:%+v, err:%v", channelId, changeType, info, sErr)
		return sErr
	}

	uwkInfo, serr := m.Cache.GetUKWCacheInfo(info.GetSenderUid(), info.GetOrderId())
	if serr != nil {
		log.ErrorWithCtx(ctx, "PushChannelRPRainChangeOpt fail to Cache.GetUKWCacheInfo. channelId:%+v, changeType:%v, info:%+v, err:%v", channelId, changeType, info, serr)
	}

	channelType := m.getChannelType(channelId)

	opt := &channelredpacketlogic.RedPacketRainChangeOpt{
		ChannelId:  channelId,
		ChangeType: changeType,
		ServerTs:   uint32(time.Now().Unix()),
		RedPacket:  fillRedPacketUserInfoLogicPb(info, user, uwkInfo, channelType),
	}

	b, _ := proto.Marshal(opt)
	err := m.pushMsgToChannel(ctx, channelId, uint32(channelPB.ChannelMsgType_CHANNEL_RED_PACKET_RAIN_CHANGE), "房间红包雨变更", b, []uint32{})
	if err != nil {
		log.ErrorWithCtx(ctx, "PushChannelRPRainChangeOpt fail to pushMsgToChannel. info:%+v, err:%v", opt, err)
		return err
	}

	log.InfoWithCtx(ctx, "[房间红包雨变更] PushChannelRPRainChangeOpt channelId:%v, opt:%s", channelId, utils.ToJson(opt))
	return nil
}

// 抢红包结算
func (m *RedPacketMgr) PushRPSettleChannelOpt(ctx context.Context, opt *channelredpacketlogic.RedPacketSettleOpt) error {
	channelId := opt.GetChannelId()
	if channelId == 0 {
		return nil
	}

	b, _ := proto.Marshal(opt)
	err := m.pushMsgToChannel(ctx, channelId, uint32(channelPB.ChannelMsgType_CHANNEL_RED_PACKET_SETTLE), "房间红包结算", b, []uint32{})
	if err != nil {
		log.ErrorWithCtx(ctx, "PushRPSettleChannelOpt fail to pushMsgToChannel. info:%+v, err:%v", opt, err)
		return err
	}

	log.InfoWithCtx(ctx, "[房间红包结算推送] PushRPSettleChannelOpt channelId:%v, opt:%s", channelId, utils.ToJson(opt))
	return nil
}

func (m *RedPacketMgr) PushUserAwardInfo(ctx context.Context, opt *channelredpacketlogic.RedPacketAwardOpt) error {
	uid := opt.GetUid()
	if uid == 0 {
		return nil
	}

	b, _ := proto.Marshal(opt)
	err := m.PushMsgToUser(ctx, uid, uint32(gaPush.PushMessage_CHANNEL_RED_PACKET_AWARD), b)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushUserAwardInfo fail to PushMsgToUser. uid:%v, info:%+v, err:%v", uid, opt, err)
		return err
	}

	log.InfoWithCtx(ctx, "PushUserAwardInfo uid:%v, opt:%s", uid, utils.ToJson(opt))
	return nil
}

func fillRedPacketUserInfoLogicPb(info *pb.RedPacketInfo, user *account.User, uwkInfo *uwkPB.UKWPersonInfo, channelType uint32) *channelredpacketlogic.RedPacketInfo {
	redPacketInfo := &channelredpacketlogic.RedPacketInfo{
		OrderId:   info.GetOrderId(),
		PublicMsg: info.GetPublicMsg(),
		BeginTime: info.GetBeginTime(),
		EndTime:   info.GetEndTime(),
		SenderUser: &channelredpacketlogic.RedPacketUserInfo{
			Uid:      info.GetSenderUid(),
			Account:  user.GetUsername(),
			Nickname: user.GetNickname(),
		},
		RedPacketConf: &channelredpacketlogic.RedPacketConf{
			RedPacketId: info.GetRedPacketConf().GetRedPacketId(),
			GiftIdList:  info.GetRedPacketConf().GetGiftIdList(),
			TotalPrice:  info.GetRedPacketConf().GetTotalPrice(),
		},
	}

	FillUwkInfo(redPacketInfo.SenderUser, uwkInfo, channelType)

	return redPacketInfo
}

var FillUwkInfo = func(redPacketUserInfo *channelredpacketlogic.RedPacketUserInfo, uwkInfo *uwkPB.UKWPersonInfo, channelType uint32) {
	uid := redPacketUserInfo.Uid
	redPacketUserInfo.UserProfile = &app.UserProfile{
		Uid:      redPacketUserInfo.Uid,
		Account:  redPacketUserInfo.Account,
		Nickname: redPacketUserInfo.Nickname,
	}

	// cpl房间 神秘人 不处理
	if channelType == uint32(channelPb.ChannelType_CPL_SUPER_CHANNEL_TYPE) {
		log.Infof("FillUwkInfo uid %d, uwkInfo %+v channelType is cpl, ignore.", uid, uwkInfo)
		return
	}

	if uwkInfo != nil && uwkInfo.Account != "" {
		redPacketUserInfo.Uid = uwkInfo.FakeUid
		redPacketUserInfo.Account = uwkInfo.Account
		redPacketUserInfo.Nickname = uwkInfo.Nickname
		redPacketUserInfo.UserUkwInfo = &app.UserUKWInfo{
			Level:     uwkInfo.Level,
			Medal:     uwkInfo.Medal,
			HeadFrame: uwkInfo.HeadFrame,
		}

		redPacketUserInfo.UserProfile.Uid = uwkInfo.FakeUid
		redPacketUserInfo.UserProfile.Account = uwkInfo.Account
		redPacketUserInfo.UserProfile.Nickname = uwkInfo.Nickname

		redPacketUserInfo.UserProfile.Privilege = &app.UserPrivilege{
			Account:  uwkInfo.Account,
			Nickname: uwkInfo.Nickname,
		}

		// 填充神秘人属性
		options := &app.UserUKWInfo{
			Level:     uwkInfo.GetLevel(),
			Medal:     uwkInfo.GetMedal(),
			HeadFrame: uwkInfo.GetHeadFrame(),
		}
		if pbOptions, err := proto.Marshal(options); err == nil {
			redPacketUserInfo.UserProfile.Privilege.Type = uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW)
			redPacketUserInfo.UserProfile.Privilege.Options = pbOptions
		}

		log.Infof("FillUwkInfo uid %d >> %+v", uid, redPacketUserInfo)
	}
}

func (m *RedPacketMgr) getChannelType(channelId uint32) uint32 {
	ctx, cancel := context.WithTimeout(context.Background(), time.Millisecond*200)
	defer cancel()
	channelInfo, err := m.channelCli.GetChannelSimpleInfo(ctx, 0, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelSimpleInfo fail. channelId:%+v, err:%v", channelId, err)
		return 0
	} else {
		return channelInfo.GetChannelType()
	}
}

func (m *RedPacketMgr) pushMsgToChannel(ctx context.Context, channelId, msgType uint32, content string, data []byte, skipUidList []uint32) error {
	msg := &channelPB.ChannelBroadcastMsg{
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  channelId,
		Type:         msgType,
		Content:      []byte(content),
		PbOptContent: data,
	}

	channelMsgBin, err := msg.Marshal()
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMsgToChannel marshal err: %s, %+v", err.Error(), msg)
		return err
	}

	err = m.pushChannelBroMsgToChannels(ctx, []uint32{channelId}, skipUidList, channelMsgBin)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMsgToChannel fail to SendChannelBroadcastMsg. channelId:%d err:%v", channelId, err)
		return err
	}

	log.InfoWithCtx(ctx, "pushMsgToChannel msgType:%v, channelId:%d, content:%s", msgType, channelId, content)
	return nil
}

// 房间广播消息
func (m *RedPacketMgr) pushChannelBroMsgToChannels(ctx context.Context, channelIds []uint32, skipUidList []uint32, channelMsgBin []byte) error {
	pushMessage := &gaPush.PushMessage{
		Cmd:     uint32(gaPush.PushMessage_CHANNEL_MSG_BRO),
		Content: channelMsgBin,
		SeqId:   uint32(time.Now().Unix()),
	}
	pushMessageBytes, e := pushMessage.Marshal()
	if e != nil {
		log.ErrorWithCtx(ctx, "pushChannelBroMsgToChannels Marshal channelIds:%v, err: %v", channelIds, e)
		return e
	}

	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              uint32(protocol.TT),
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageBytes,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 60,
		},
	}

	multicastMap := map[uint64]string{}
	for _, channelId := range channelIds {
		if channelId == 0 {
			continue
		}
		multicastMap[uint64(channelId)] = fmt.Sprintf("%d@channel", channelId)
	}

	if len(multicastMap) == 0 {
		return nil
	}

	err := m.pushCli.PushMulticasts(ctx, multicastMap, skipUidList, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushChannelBroMsgToChannels fail to PushMulticasts channelIds:%v, err: %s", channelIds, err.Error())
		return err
	}

	return nil
}

// 发送TT助手消息
func (m *RedPacketMgr) SendIMMsg(ctx context.Context, uid uint32, content string) error {
	msg := &apiPB.ImMsg{
		ImType: &apiPB.ImType{
			SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
			ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
			ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT),
		},
		FromUid: uint32(10000),
		ToIdList: []uint32{
			uid,
		},
		ImContent: &apiPB.ImContent{
			TextNormal: &apiPB.ImTextNormal{
				Content: content,
			},
		},
		Platform:    apiPB.Platform_UNSPECIFIED,
		AppPlatform: "all",
	}

	err := m.apiCenterCli.SendImMsg(ctx, uid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImMsg fail. uid:%d err: %s", uid, err.Error())
		return err
	}

	notify.NotifySync(uid, sync.SyncReq_IM_MSG)

	log.Debugf("SendIMMsg done. uid:%d, content:%s, Msg:%+v", uid, content, msg)
	return nil
}

func (m *RedPacketMgr) PushMsgToUser(ctx context.Context, uid, cmd uint32, msg []byte) error {
	pushMessage := &gaPush.PushMessage{
		Cmd:     cmd,
		Content: msg,
	}
	pushMessageBytes, _ := pushMessage.Marshal()

	err := m.pushCli.PushToUsers(ctx, []uint32{uid}, &pushPB.CompositiveNotification{
		Sequence:           uid,
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:    uint32(pushPB.ProxyNotification_PUSH),
			Payload: pushMessageBytes,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PushMsgToUser PushToUsers err: %s", err.Error())
		return err
	}

	return nil
}
