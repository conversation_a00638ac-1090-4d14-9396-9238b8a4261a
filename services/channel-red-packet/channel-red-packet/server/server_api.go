package server

import(
	context "context"
	channel_red_packet "golang.52tt.com/protocol/services/channel-red-packet"
	cb "golang.52tt.com/protocol/services/unified_pay/cb"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
)

type IRedPacket interface {
	AddRedPacketConf(ctx context.Context, req *channel_red_packet.AddRedPacketConfReq) (*channel_red_packet.AddRedPacketConfResp, error)
	CheckIfCanSendRedPacket(ctx context.Context, req *channel_red_packet.CheckIfCanSendRedPacketReq) (*channel_red_packet.CheckIfCanSendRedPacketResp, error)
	DelRedPacketConf(ctx context.Context, req *channel_red_packet.DelRedPacketConfReq) (*channel_red_packet.DelRedPacketConfResp, error)
	GetAwardOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	GetAwardTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	GetConsumeTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetRedPacketAwardTotal(ctx context.Context, req *channel_red_packet.GetRedPacketAwardTotalReq) (*channel_red_packet.GetRedPacketAwardTotalResp, error)
	GetRedPacketConf(ctx context.Context, req *channel_red_packet.GetRedPacketConfReq) (*channel_red_packet.GetRedPacketConfResp, error)
	GetRedPacketConfById(ctx context.Context, req *channel_red_packet.GetRedPacketConfByIdReq) (*channel_red_packet.GetRedPacketConfByIdResp, error)
	GetRedPacketList(ctx context.Context, req *channel_red_packet.GetRedPacketListReq) (*channel_red_packet.GetRedPacketListResp, error)
	GetRedPacketOrderTotal(ctx context.Context, req *channel_red_packet.GetRedPacketOrderTotalReq) (*channel_red_packet.GetRedPacketOrderTotalResp, error)
	Notify(ctx context.Context, req *cb.PayNotify) (*cb.PayNotifyResponse, error)
	ReportRedPacketClickCnt(ctx context.Context, req *channel_red_packet.ReportRedPacketClickCntReq) (*channel_red_packet.ReportRedPacketClickCntResp, error)
	SendRedPacket(ctx context.Context, req *channel_red_packet.SendRedPacketReq) (*channel_red_packet.SendRedPacketResp, error)
	ShutDown() ()
}

