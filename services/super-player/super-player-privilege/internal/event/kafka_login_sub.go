package event

import (
	"context"
	pb "golang.52tt.com/protocol/services/super-player-privilege"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/clients/gnobility"
	presence2 "golang.52tt.com/clients/presence/v2"
	pushNotificationv3 "golang.52tt.com/clients/push-notification/v3/push"
	superplayersvr "golang.52tt.com/clients/super-player-svr"
	"golang.52tt.com/pkg/log"
	gnobilityPB "golang.52tt.com/protocol/services/gnobility"
	kfkPB "golang.52tt.com/protocol/services/minToolkit/kafka/pb/tt_auth_ev"
	"golang.52tt.com/services/super-player/super-player-privilege/internal/cache"
	"golang.52tt.com/services/super-player/super-player-privilege/internal/manager"
	"golang.52tt.com/services/super-player/super-player-privilege/internal/push"
)

const (
	topicTypeNameLoginLogin = "tt_auth_ev"
)

type KafkaLoginSubscriber struct {
	kafkaSub      subscriber.Subscriber
	mgr           *manager.SpecialConcernManager
	pushCliV3     *pushNotificationv3.Client
	spCli         *superplayersvr.Client
	gNobility     *gnobility.Client
	presenceCliV2 *presence2.Client

	batchGetOnlineSwitchFunc func(ctx context.Context, uidList []uint32) (map[uint32]pb.OnlineSwitch, error)
}

func NewLoginKafkaSubscriber(ctx context.Context, clientId, groupId string, topics, brokers []string, mgr *manager.SpecialConcernManager,
	pushCliV3_ *pushNotificationv3.Client, spCli_ *superplayersvr.Client, gNobility_ *gnobility.Client, presenceCliV2_ *presence2.Client,
	batchGetOnlineSwitchFunc func(ctx context.Context, uidList []uint32) (map[uint32]pb.OnlineSwitch, error)) (*KafkaLoginSubscriber, error) {

	conf := kafka.DefaultConfig()
	conf.ClientID = clientId
	conf.Consumer.Offsets.Initial = kafka.OffsetNewest
	conf.Consumer.Return.Errors = true

	kafkaSub, err := kafka.NewSubscriber(brokers, conf, subscriber.WithMaxRetryTimes(3))
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create kafka-subscriber %+v", err)
		return nil, err
	}
	sub := &KafkaLoginSubscriber{
		kafkaSub:                 kafkaSub,
		mgr:                      mgr,
		pushCliV3:                pushCliV3_,
		presenceCliV2:            presenceCliV2_,
		spCli:                    spCli_,
		gNobility:                gNobility_,
		batchGetOnlineSwitchFunc: batchGetOnlineSwitchFunc,
	}
	err = kafkaSub.SubscribeContext(groupId, topics, subscriber.ProcessorContextFunc(sub.handlerEvent))
	if err != nil {
		panic(err)
	}
	return sub, nil
}

func (s *KafkaLoginSubscriber) Close() error {
	if s.kafkaSub == nil {
		return nil
	}
	return s.kafkaSub.Stop()
}

func (s *KafkaLoginSubscriber) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	switch msg.Topic {
	case topicTypeNameLoginLogin:
		return s.handlerLoginEvent(ctx, msg)
	}
	return nil, false
}

func (s *KafkaLoginSubscriber) handlerLoginEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	authEvent := kfkPB.TTAuthEvent{}
	err := proto.Unmarshal(msg.Value, &authEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to proto.Unmarshal %+v", err)
		return err, false
	}
	log.Infof("handlerLoginEvent topic(%s) partition(%d) offset(%d),event:%s", msg.Topic, msg.Partition, msg.Offset, authEvent.String())

	list, err := s.mgr.GetUserBeSpecialConcern(ctx, authEvent.GetUid(), time.Now())
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerLoginEvent GetUserBeSpecialConcern err:%+v", err)
		return nil, false
	}

	log.DebugWithCtx(ctx, "handlerLoginEvent GetUserBeSpecialConcern list:%+v uid:%v", list, authEvent.GetUid())

	if len(list) == 0 {
		return nil, false
	}

	gresp, err := s.gNobility.GetNobilitySwitchFlag(ctx, []uint32{authEvent.GetUid()})
	if nil != err {
		log.ErrorWithCtx(ctx, "handlerLoginEvent GetNobilitySwitchFlag uid:%v err:%v", authEvent.GetUid(), err)
	} else {
		if gresp != nil && len(gresp.SwitchFlagList) == 1 {
			flag := gresp.SwitchFlagList[0]
			if (flag & uint32(gnobilityPB.NobilitySwitch_E_ONLINE)) > 0 {
				log.Infof("handlerLoginEvent GetNobilitySwitchFlag ONLINE uid:%v", authEvent.GetUid())
				return nil, false
			}
		}
	}

	playerInfos, err := s.spCli.BatchGetSuperPlayerInfo(ctx, list)
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerLoginEvent BatchGetSuperPlayerInfo, uid :%v , err: %v", list, err)
		return nil, false
	}
	list = make([]uint32, 0)
	svipList := make([]uint32, 0)
	now := time.Now().Unix()

	for _, v := range playerInfos.GetSuperPlayerInfoList() {
		if v.GetIsSvip() {
			svipList = append(svipList, v.GetSuperPlayerUid())
		}
	}

	stealMap, err := s.batchGetOnlineSwitchFunc(ctx, svipList)
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerLoginEvent batchGetOnlineSwitchFunc err:%+v", err)
	}

	for _, v := range playerInfos.GetSuperPlayerInfoList() {
		u := v.GetSuperPlayerUid()
		if stealMap[u] == pb.OnlineSwitch_ENUM_ONLINE_SWITCH_STEALTH {
			log.DebugWithCtx(ctx, "handlerLoginEvent ENUM_ONLINE_SWITCH_STEALTH uid:%v -> %v", authEvent.GetUid(), u)
			continue
		}
		if v.GetExpireTimestamp() > now {
			list = append(list, u)
		}
	}

	log.DebugWithCtx(ctx, "handlerLoginEvent GetExpireTimestamp list%+v uid:%v", list, authEvent.GetUid())

	userPres := push.CheckPresence(list, s.presenceCliV2, ctx)
	pushuids := make([]uint32, 0)
	for k, v := range userPres {
		log.Infof("handlerLoginEvent CheckPresence k:%+v,v:%+v", k, v)
		if !v {
			pushuids = append(pushuids, k)
		}
	}

	log.DebugWithCtx(ctx, "handlerLoginEvent pushuids%+v uid:%v", pushuids, authEvent.GetUid())

	if len(pushuids) == 0 {
		return nil, false
	}
	ret, err := s.mgr.GetCache().IncrPushWithType(ctx, authEvent.GetUid(), cache.LoginType, pushuids)
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerLoginEvent IncrPushWithType uid:%v err:%+v", authEvent.GetUid(), err)
		return nil, false
	}
	uids := make([]uint32, 0)
	for k, v := range ret {
		if v <= 2 {
			uids = append(uids, k)
		}
	}

	log.DebugWithCtx(ctx, "handlerLoginEvent uids %+v uid:%v", uids, authEvent.GetUid())

	go func() {
		s.pushOnlineMsg(ctx, uids, authEvent)
	}()

	return nil, false
}

func (s *KafkaLoginSubscriber) pushOnlineMsg(ctx context.Context, uids []uint32, event kfkPB.TTAuthEvent) {
	if len(uids) <= 0 {
		return
	}
	limit := 1000
	total := len(uids)
	batch := 0
	ctx2, cancel2 := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel2()
	for i := 0; i < total; i += batch {
		remain := total - i
		batch = limit
		if batch > remain {
			batch = remain
		}
		batUids := uids[i : i+batch]
		log.Infof("in uid: %d push to uid: %+v", event.GetUid(), batUids)
		err := push.SendPushTask(ctx2, "那个ta突然出现", "你特别关心的小可爱上线咯！", "tt://m.52tt.com/home?main_pos=2&vice_pos=0&main_tab=message", batUids, s.pushCliV3)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendPushTask failed event:%s,len uids :%d,err:%+v", event.String(), len(uids), err)
		}
	}
}
