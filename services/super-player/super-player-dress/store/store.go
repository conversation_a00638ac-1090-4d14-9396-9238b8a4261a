package store

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis"
	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/robfig/cron"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/superplayerdress"
	"golang.52tt.com/services/super-player/super-player-dress/conf"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var (
	mapTableSuffix = map[uint32]string{
		1: "room_suit",
		2: "special_concern",
		3: "chat_background",
		4: "chat_bubble",
	}
)

// Store .
type Store struct {
	dressMysql  *gorm.DB            //库
	sc          *conf.ServiceConfig //配置
	redisClient *redis.Client
	memoryCache *MemoryCache // 内存缓存
}

// NewStore .
func NewStore(sc *conf.ServiceConfig) (*Store, error) {
	var err error
	var mysqlDb *gorm.DB
	if mysqlDb, err = gorm.Open(mysql.New(mysql.Config{
		DSN:                       sc.MysqlConfig.ConnectionString(),
		DefaultStringSize:         256,   // default size for string fields
		DisableDatetimePrecision:  true,  // disable datetime precision, which not supported before MySQL 5.6
		DontSupportRenameIndex:    true,  // drop & create when rename index, rename index not supported before MySQL 5.7, MariaDB
		DontSupportRenameColumn:   true,  // `change` when rename column, rename column not supported before MySQL 8, MariaDB
		SkipInitializeWithVersion: false, // auto configure based on currently MySQL version
	}), &gorm.Config{}); err != nil {
		return nil, err
	}

	RedisConfig := sc.RedisConfig
	redisClient := redis.NewClient(&redis.Options{
		Network:            RedisConfig.Protocol,
		Addr:               RedisConfig.Addr(),
		PoolSize:           RedisConfig.PoolSize,
		IdleCheckFrequency: RedisConfig.IdleCheckFrequency(),
		DB:                 RedisConfig.DB,
	})

	st := &Store{
		dressMysql:  mysqlDb,
		sc:          sc,
		redisClient: redisClient,
		memoryCache: NewMemoryCache(),
	}

	st.Init()
	go st.StartCron()
	return st, nil
}

func (st *Store) GetMysql() *gorm.DB {
	return st.dressMysql
}

func (st *Store) GetRedis() *redis.Client {
	return st.redisClient
}

func (st *Store) getDressConfigTable(ctx context.Context, dressType uint32) *gorm.DB {
	tableName := st.getDressConfigTableName(dressType)
	return st.GetMysql().WithContext(ctx).Table(tableName)
}

func (st *Store) getDressInUseTable(ctx context.Context, dressType uint32) *gorm.DB {
	tableName := st.getDressInUseTableName(dressType)
	return st.GetMysql().WithContext(ctx).Table(tableName)
}

func (st *Store) getDressUseHistoryTable(ctx context.Context, dressType uint32) *gorm.DB {
	tableName := st.getDressUseHistoryTableName(dressType)
	return st.GetMysql().WithContext(ctx).Table(tableName)
}

func (st *Store) getChatBgSpecialTable(ctx context.Context, uid uint32) *gorm.DB {
	tableName := fmt.Sprintf("dress_in_use_chat_bg_special_%02d", uid%100)
	return st.GetMysql().WithContext(ctx).Table(tableName)
}

func (st *Store) getDressExperiencePackageConfigTable(ctx context.Context) *gorm.DB {
	config := DressExperiencePackageConfig{}
	return st.GetMysql().WithContext(ctx).Table(config.TableName())
}

func (st *Store) getDressUserExperienceFlowTable(ctx context.Context) *gorm.DB {
	flow := &DressUserExperienceFlow{}
	return st.GetMysql().WithContext(ctx).Table(flow.TableName())
}

type DressConfig struct {
	ID               uint32    `gorm:"primary_key" sql:"type:INT(10) NOT NULL AUTO_INCREMENT COMMENT '装扮ID'"`
	Level            uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'level'"`
	ResourceUrl      string    `sql:"type:VARCHAR(256) NOT NULL COMMENT '资源地址'"`
	ResourceMd5      string    `sql:"type:VARCHAR(256) NOT NULL COMMENT '资源MD5'"`
	Name             string    `sql:"type:VARCHAR(64) NOT NULL COMMENT '装扮名称'"`
	Desc             string    `sql:"type:VARCHAR(256) NOT NULL COMMENT '装扮描述'"`
	MinClientVersion string    `sql:"type:VARCHAR(64) NOT NULL COMMENT '客户端最小版本限制'"`
	CreateTime       time.Time `gorm:"index:create_time_index" sql:"DEFAULT:current_timestamp"`
	WebListUrl       string    `sql:"type:VARCHAR(512) NOT NULL COMMENT 'web列表图片地址'"`
	WebTopUrl        string    `sql:"type:VARCHAR(512) NOT NULL COMMENT 'web顶部图片地址'"`
	WebPreviewUrl    string    `sql:"type:VARCHAR(512) NOT NULL COMMENT 'web预览图地址'"`
	WebAudioUrl      string    `sql:"type:VARCHAR(512) NOT NULL COMMENT 'web音频资源地址'"`
	WebVideoUrl      string    `sql:"type:VARCHAR(512) NOT NULL COMMENT 'web视频资源地址'"`
	WebConfig        string    `sql:"type:VARCHAR(512) NOT NULL COMMENT 'web配置文件地址'"`
	WebStaticUrl     string    `sql:"type:VARCHAR(512) NOT NULL COMMENT 'web静态配件图地址'"`
	DressType        uint32    `gorm:"-"`                                                        // 读写操作均会忽略该字段
	IsDefault        uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否默认装扮'"` //0 不是 1 是
	IsRemoved        uint32    `gorm:"-"`                                                        //0 不是 1 是
	ComDesc          string    `sql:"type:VARCHAR(128) NOT NULL COMMENT '组件文案'"`
	BeginTs          uint64    `sql:"type:BIGINT(10) NOT NULL COMMENT '开始时间'"`
	EndTs            uint64    `sql:"type:BIGINT(10) NOT NULL COMMENT '结束时间'"`
	Operator         string    `sql:"VARCHAR(50) NOT NULL DEFAULT '' COMMENT '操作人'"`
	Idx              uint64    `sql:"type:BIGINT(10) NOT NULL COMMENT '排序'"`
}

func (st *Store) getDressConfigTableName(dressType uint32) string {
	suffix, _ := getTableSuffix(dressType)
	return fmt.Sprintf("dress_config_%s", suffix)
}

func (st *Store) getDressInUseTableName(dressType uint32) string {
	suffix, _ := getTableSuffix(dressType)
	fmt.Println("getDressInUseTableName ", suffix)
	return fmt.Sprintf("dress_in_use_%s", suffix)
}

func (st *Store) getDressUseHistoryTableName(dressType uint32) string {
	suffix, _ := getTableSuffix(dressType)
	return fmt.Sprintf("dress_use_history_%s", suffix)
}

// DressInUse 正在装扮的套装ID
type DressInUse struct {
	ID         uint64    `gorm:"primary_key" sql:"type:INT(10) NOT NULL COMMENT 'id'"`
	DressId    uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'dress_ID'"`
	CreateTime time.Time `gorm:"index:create_time_index" sql:"DEFAULT:current_timestamp"`
	DressType  uint32    `gorm:"-"` // 读写操作均会忽略该字段
	OptDress   uint32    `gorm:"-"` // 读写操作均会忽略该字段
	Platform   uint32    `gorm:"-"` // 读写操作均会忽略该字段
}

// DressInUseHistory 正在装扮的套装ID
type DressInUseHistory struct {
	UID        uint64    `gorm:"primary_key" sql:"type:INT(10) UNSIGNED NOT NULL COMMENT 'uid'"`
	DressId    uint32    `gorm:"primary_key" sql:"type:INT(10) UNSIGNED NOT NULL COMMENT 'dress_ID'"`
	CreateTime time.Time `gorm:"index:create_time_index" sql:"DEFAULT:current_timestamp"`
	DressType  uint32    `gorm:"-"` // 读写操作均会忽略该字段
}

// DressInUseChatBgSpecial 正在装扮的套装ID
type DressInUseChatBgSpecial struct {
	UID        uint32    `gorm:"primary_key" sql:"type:INT(10) UNSIGNED NOT NULL"`
	ToAccount  string    `gorm:"primary_key" sql:"type:VARCHAR(64) NOT NULL"`
	DressId    uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'dress_ID'"`
	CreateTime time.Time `gorm:"index:create_time_index" sql:"DEFAULT:current_timestamp"`
	DressType  uint32    `gorm:"-"` // 读写操作均会忽略该字段
}

func getTableSuffix(dressType uint32) (string, bool) {
	suffix, ok := mapTableSuffix[dressType]
	return suffix, ok
}

func checkDressTypeValid(dressType uint32) bool {
	_, ok := mapTableSuffix[dressType]
	return ok
}

// DressExperiencePackageConfig 临时装扮配置包表
type DressExperiencePackageConfig struct {
	Id         uint64    `gorm:"primary_key" sql:"type:BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键'"`            //自增主键
	Config     string    `sql:"type:text COMMENT '配置信息，为json形式'"`                                                              //配置信息列表，为json形式
	CreateTime time.Time `sql:"type:DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'"`                               //创建时间
	Creator    string    `sql:"type:VARCHAR(512) NOT NULL COMMENT '创建人'"`                                                      //创建人
	UpdateTime time.Time `sql:"type:DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间'"` //记录更新时间
	Updater    string    `sql:"type:VARCHAR (512) NOT NULL COMMENT '修改人'"`                                                     //修改人
}

func (d *DressExperiencePackageConfig) TableName() string {
	return "sp_dress_experience_package_config"
}

var CreateDressExperiencePackageConfigTable = `CREATE TABLE sp_dress_experience_package_config (
  id BIGINT (20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  config text COMMENT '配置信息，为json形式',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  creator VARCHAR (512) NOT NULL COMMENT '创建人',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  updater VARCHAR (512) NOT NULL COMMENT '修改人',
  PRIMARY KEY (id)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '临时装扮配置包表';
`

// DressUserExperienceFlow 用户体验权限发放流水
type DressUserExperienceFlow struct {
	Id         uint64    `gorm:"primary_key" sql:"type:BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键'"`         //自增主键
	Uid        uint32    `gorm:"index:idx_uid_package,idx_uid_dress" sql:"type:BIGINT(20) UNSIGNED NOT NULL COMMENT 'uid'"` //uid
	PackageId  uint64    `gorm:"index:idx_uid_package" sql:"type:BIGINT(20) UNSIGNED NOT NULL COMMENT '体验包ID'"`             //体验包ID
	OrderId    string    `gorm:"unique_index" sql:"type:VARCHAR(255) NOT NULL COMMENT '发放订单ID'"`                            // 订单ID
	Reason     string    `sql:"type:VARCHAR(255) NOT NULL COMMENT '发放理由'"`
	CreateTime time.Time `sql:"type:DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'"`                               //创建时间
	UpdateTime time.Time `sql:"type:DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间'"` //记录更新时间 	// 读写操作均会忽略该字段
}

func (d *DressUserExperienceFlow) TableName() string {
	return "sp_dress_user_experience_flow"
}

var CreateDressUserExperienceFlowTable = `CREATE TABLE sp_dress_user_experience_flow (
  id BIGINT (20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  uid BIGINT (20) UNSIGNED NOT NULL COMMENT 'uid',
  order_id VARCHAR (255) NOT NULL COMMENT '订单id',
  package_id BIGINT (20) UNSIGNED NOT NULL COMMENT '体验包ID',
  reason VARCHAR (255) NOT NULL COMMENT '发放理由',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uniq_idx_order_id ( order_id ),
  KEY idx_uid ( uid )
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '用户体验权限发放流水表';
`

func (st *Store) Init() {
	ctx := context.Background()
	for dressType := range pb.DressType_name {
		if int32(pb.DressType_DRESS_TYPE_UNSPECIFIC) != dressType {
			err := st.getDressConfigTable(ctx, uint32(dressType)).AutoMigrate(&DressConfig{})
			if err != nil {
				log.ErrorWithCtx(ctx, "create table failed, err:%v", err)
			}
			err = st.getDressInUseTable(ctx, uint32(dressType)).AutoMigrate(&DressInUse{})
			if err != nil {
				log.ErrorWithCtx(ctx, "create table failed, err:%v", err)
			}

			err = st.getDressUseHistoryTable(ctx, uint32(dressType)).AutoMigrate(&DressInUseHistory{})
			if err != nil {
				log.ErrorWithCtx(ctx, "create table failed, err:%v", err)
			}
		}
	}

	for i := 0; i < 100; i++ {
		err := st.getChatBgSpecialTable(ctx, uint32(i)).AutoMigrate(&DressInUseChatBgSpecial{})
		if err != nil {
			log.ErrorWithCtx(ctx, "create chat special table failed, err:%v", err)
		}
	}

	err := st.GetMysql().Exec(CreateDressExperiencePackageConfigTable).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "create table failed, err:%v", err)
	}

	err = st.GetMysql().Exec(CreateDressUserExperienceFlowTable).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "create table failed, err:%v", err)
	}
}

// StartCron
func (st *Store) StartCron() {
	c := cron.New()
	spec := "0 */1 * * * ?"
	c.AddFunc(spec, st.GenRedisMaxVersion)
	c.Start()
	select {}
}

func (st *Store) GenRedisMaxVersion() {
	ctx := context.Background()
	for dressType := range pb.DressType_name {
		if int32(pb.DressType_DRESS_TYPE_UNSPECIFIC) != dressType {
			version, err := st.GetMaxDressConfigVersion(ctx, uint32(dressType))
			if err == nil {
				st.SetRedisMaxVersion(uint32(dressType), version)
			}
		}
	}
}

// GetUserAllDressLevelExperiencePkgWithMemoryCache 从本地缓存获取获取用户所有等级体验包
func (st *Store) GetUserAllDressLevelExperiencePkgWithMemoryCache(ctx context.Context, uid uint32) (map[uint64]int64, error) {

	// 先从本地缓存拿
	result, ok := st.memoryCache.Get(st.getUserDressLevelExperienceKey(uid))
	if ok {
		log.DebugWithCtx(ctx, "GetUserAllDressLevelExperiencePkgWithMemoryCache hit cache, uid:%d, result:%v", uid, result)
		return result.(map[uint64]int64), nil
	}

	// 如果本地缓存没有的话，再从redis拿
	cacheResult, err := st.GetUserAllDressLevelExperiencePkg(ctx, uid)
	if err != nil {
		return nil, err
	}

	// 本地缓存一份，缓存2秒钟
	st.memoryCache.Set(st.getUserDressLevelExperienceKey(uid), cacheResult, time.Second*2)
	log.DebugWithCtx(ctx, "GetUserAllDressLevelExperiencePkgWithMemoryCache set cache, uid:%d, result:%v", uid, cacheResult)
	return cacheResult, nil
}
