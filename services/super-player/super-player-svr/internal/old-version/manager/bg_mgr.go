package manager

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/conf"
	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/mysql"
	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/utils"
	"sort"
	"strconv"
	"strings"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/superplayersvr"
	"time"
)

var emptyPackageIdErr = protocol.NewServerError(status.ErrRequestParamInvalid, "套餐id不能为空")
var reduplicatedTimeErr = protocol.NewServerError(status.ErrRequestParamInvalid, "上下架时间跟“已上架”的套餐重叠")

func (m *Manager) AddSuperPlayerEntryAdvConf(ctx context.Context, in *pb.AddSuperPlayerEntryAdvConfReq) (*pb.AddSuperPlayerEntryAdvConfResp, error) {
	out := &pb.AddSuperPlayerEntryAdvConfResp{}

	log.DebugWithCtx(ctx, "AddSuperPlayerEntryAdvConf begin in:%v", in)
	err := m.db.AddSuperPlayerEntryAdvConf(in.GetAdvConf())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddSuperPlayerEntryAdvConf failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	log.DebugWithCtx(ctx, "AddSuperPlayerEntryAdvConf end in:%v out:%v", in, out)
	return out, nil
}

func (m *Manager) UpdateSuperPlayerEntryAdvConf(ctx context.Context, in *pb.UpdateSuperPlayerEntryAdvConfReq) (*pb.UpdateSuperPlayerEntryAdvConfResp, error) {
	out := &pb.UpdateSuperPlayerEntryAdvConfResp{}

	log.DebugWithCtx(ctx, "UpdateSuperPlayerEntryAdvConf begin in:%v", in)
	err := m.db.UpdateSuperPlayerEntryAdvConf(in.GetAdvConf())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateSuperPlayerEntryAdvConf failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	log.DebugWithCtx(ctx, "UpdateSuperPlayerEntryAdvConf end in:%v out:%v", in, out)

	return out, nil
}

func (m *Manager) DelSuperPlayerEntryAdvConf(ctx context.Context, in *pb.DelSuperPlayerEntryAdvConfReq) (*pb.DelSuperPlayerEntryAdvConfResp, error) {
	out := &pb.DelSuperPlayerEntryAdvConfResp{}

	log.DebugWithCtx(ctx, "DelSuperPlayerEntryAdvConf begin in:%v", in)
	err := m.db.DelSuperPlayerEntryAdvConf(in.GetAdvId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelSuperPlayerEntryAdvConf failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	log.DebugWithCtx(ctx, "DelSuperPlayerEntryAdvConf end in:%v out:%v", in, out)
	return out, nil
}

func (m *Manager) GetAllSuperPlayerEntryAdvConf(ctx context.Context, in *pb.GetAllSuperPlayerEntryAdvConfReq) (*pb.GetAllSuperPlayerEntryAdvConfResp, error) {
	out := &pb.GetAllSuperPlayerEntryAdvConfResp{}

	log.DebugWithCtx(ctx, "GetAllSuperPlayerEntryAdvConf begin in:%v", in)
	confList, err := m.db.GetAllSuperPlayerEntryAdvConf()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllSuperPlayerEntryAdvConf failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	for _, conf := range confList {
		msgList := strings.Split(conf.AdvMsg, "-")
		tmpConf := &pb.SuperPlayerEntryAdvConf{
			AdvId: conf.AdvId,
			//ConfType:          conf.ConfType,
			//SuperPlayerStatus: conf.SpStatus,
			AdvMsg:          msgList,
			RedDotFrequency: conf.RdFre,
			BeginTs:         conf.BeginTs,
			EndTs:           conf.EndTs,
			Remarks:         conf.Remarks,
			UpdateTs:        conf.UpdateTs,
		}

		out.ConfList = append(out.ConfList, tmpConf)
	}

	sort.Slice(out.ConfList, func(i, j int) bool { return out.ConfList[i].GetUpdateTs() > out.ConfList[j].GetUpdateTs() })
	log.DebugWithCtx(ctx, "GetAllSuperPlayerEntryAdvConf in:%v out:%v", in, out)
	return out, nil
}

func (m *Manager) AddPackage(ctx context.Context, in *pb.AddPackageReq) (*pb.AddPackageResp, error) {
	out := &pb.AddPackageResp{}

	log.InfoWithCtx(ctx, "AddPackage begin in:%v", in)

	if in.GetPackageInfo().GetPrice() == 0 {
		log.ErrorWithCtx(ctx, "AddPackage price unable be zero in:%v", in)
		return out, protocol.NewServerError(status.ErrSuperPlayerInvalidPara, "售价不能为0")
	}

	if in.GetPackageInfo().GetAuto() && in.GetPackageInfo().GetDays() < 7 {
		log.ErrorWithCtx(ctx, "AddPackage auto package days unable less than 7 in:%v", in)
		return out, protocol.NewServerError(status.ErrSuperPlayerInvalidPara, "自动续费套餐会员天数不能少于7天")
	}

	if in.GetPackageInfo().GetAuto() && in.GetPackageInfo().GetProductId() != "" {
		packConf := conf.GetPackageByProductID(in.GetPackageInfo().GetProductId())
		if nil != packConf {
			log.ErrorWithCtx(ctx, "AddPackage 已经存在相同的produceID套餐 %v", in.GetPackageInfo().ProductId)
			return out, protocol.NewServerError(status.ErrSuperPlayerInvalidPara, fmt.Sprintf("已经存在相同的produceID套餐 %v", in.GetPackageInfo().GetProductId()))
		}
	}

	var payChList []string
	if in.GetPackageInfo().GetProductId() != "" {
		// ios套餐
		payChList = append(payChList, "APPSTORE")
	} else {
		// 非ios套餐
		payChList = append(payChList, "ALIPAY", "WECHAT")
	}

	in.PackageInfo.PayChannelList = payChList

	err := m.db.AddPackage(in.GetPackageInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddPackage failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	log.InfoWithCtx(ctx, "AddPackage end in:%v out:%v", in, out)

	return out, nil
}

func (m *Manager) UpdatePackage(ctx context.Context, in *pb.UpdatePackageReq) (*pb.UpdatePackageResp, error) {
	out := &pb.UpdatePackageResp{}

	log.InfoWithCtx(ctx, "UpdatePackage begin in:%v", in)
	if in.GetPackageInfo().GetId() == "" {
		log.ErrorWithCtx(ctx, "UpdatePackage id unable be empty in:%v", in)
		return out, emptyPackageIdErr
	}

	isUpdateStatus := false
	if in.GetUpdateType() != uint32(pb.UpdatePackageReq_ENUM_UPDATE_BASE) {
		isUpdateStatus = true
	}

	// 当有上架的套餐时，不能停用套餐
	if in.GetUpdateType() == uint32(pb.UpdatePackageReq_ENUM_UPDATE_UNABLE) {
		packageId, err := strconv.Atoi(in.GetPackageInfo().GetId())
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdatePackage strconv.Atoi failed in:%v err:%v", in, err)
			return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
		}

		spList, err := m.db.GetValidSalePackageListByPid(uint32(packageId))
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdatePackage GetValidSalePackageListByPid failed in:%v err:%v", in, err)
			return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
		}

		if len(spList) > 0 {
			log.ErrorWithCtx(ctx, "UpdatePackage package is using in:%v", in)
			return out, protocol.NewServerError(status.ErrSuperPlayerInvalidPara, "该套餐还处于上架状态，不能停用")
		}
	}

	err := m.db.UpdatePackage(in.GetPackageInfo(), isUpdateStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdatePackage failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	log.InfoWithCtx(ctx, "UpdatePackage end in:%v out:%v", in, out)

	return out, nil
}

func (m *Manager) GetPackageListByStatus(ctx context.Context, in *pb.GetPackageListByStatusReq) (*pb.GetPackageListByStatusResp, error) {
	out := &pb.GetPackageListByStatusResp{}

	log.DebugWithCtx(ctx, "GetPackageListByStatus begin in:%v", in)
	packList, err := m.db.GetPackageListByStatus(in.GetPackageStatus())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPackageListByStatus failed in:%v error:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	for _, pack := range packList {
		tmpPack := pack
		tmpConf := dbPack2pbPack(&tmpPack)
		out.PackageList = append(out.PackageList, tmpConf)
	}

	sort.Slice(out.PackageList, func(i, j int) bool { return out.PackageList[i].GetUpdateTs() > out.PackageList[j].GetUpdateTs() })
	log.DebugWithCtx(ctx, "GetPackageListByStatus end in:%v out:%v", in, out)
	return out, nil
}

func (m *Manager) checkTimeRange(ctx context.Context, pid string, saleId uint32, endts, begints uint64) error {
	packageId, err := strconv.Atoi(pid)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddSalePackage strconv.Atoi failed  err:%v", err)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	spList, err := m.db.GetValidSalePackageListByPid(uint32(packageId))
	if err != nil {
		log.ErrorWithCtx(ctx, "AddSalePackage GetValidSalePackageListByPid failed  err:%v", err)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	// 判断时间是否重叠
	for _, spInfo := range spList {

		if saleId == spInfo.SaleId {
			continue
		}

		if (endts <= spInfo.EndTs && endts >= spInfo.BeginTs) ||
			(endts >= spInfo.EndTs && begints <= spInfo.EndTs) {
			return reduplicatedTimeErr
		}
	}
	return nil
}

func (m *Manager) AddSalePackage(ctx context.Context, in *pb.AddSalePackageReq) (*pb.AddSalePackageResp, error) {
	out := &pb.AddSalePackageResp{}

	log.InfoWithCtx(ctx, "AddSalePackage begin in:%v", in)

	if in.GetInfo().GetPackageInfo().GetId() == "" {
		log.ErrorWithCtx(ctx, "AddSalePackage id is empty in:%v", in)
		return out, emptyPackageIdErr
	}

	if in.GetInfo().GetEndTs() >= uint64(time.Now().Unix()) {
		err := m.checkTimeRange(ctx, in.GetInfo().GetPackageInfo().GetId(), 0, in.GetInfo().GetEndTs(), in.GetInfo().GetBeginTs())
		if nil != err {
			log.ErrorWithCtx(ctx, "AddSalePackage checkTimeRange failed in:%v err:%v", in, err)
			return out, err
		}
	}

	// 获取最大权重
	maxWeight, err := m.db.GetSalePackageMaxWeight()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddSalePackage GetSalePackageMaxWeight failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	in.Info.Weight = maxWeight + 1

	err = m.db.AddSalePackage(in.GetInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddSalePackage failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	log.InfoWithCtx(ctx, "AddSalePackage end in:%v out:%v", in, out)

	return out, nil
}

func (m Manager) UpdateSalePackage(ctx context.Context, in *pb.UpdateSalePackageReq) (*pb.UpdateSalePackageResp, error) {
	out := &pb.UpdateSalePackageResp{}

	log.InfoWithCtx(ctx, "UpdateSalePackage begin in:%v", in)

	if in.GetInfo().GetEndTs() >= uint64(time.Now().Unix()) {
		err := m.checkTimeRange(ctx, in.GetInfo().GetPackageInfo().GetId(), in.GetInfo().GetSaleId(), in.GetInfo().GetEndTs(), in.GetInfo().GetBeginTs())
		if nil != err {
			log.ErrorWithCtx(ctx, "UpdateSalePackage checkTimeRange in:%+v err:%v", in, err)
			return out, err
		}
	}

	err := m.db.UpdateSalePackage(in.GetInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateSalePackage failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	log.InfoWithCtx(ctx, "UpdateSalePackage end in:%v err:%v", in, err)
	return out, nil
}

func sdbPack2spbPack(ctx context.Context, sdbPack *mysql.SalePackage, saleStatus uint32, nowTs uint64, tmpPackage *pb.Package) (*pb.SalePackage, error) {

	if saleStatus == uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_SHELF) && sdbPack.BeginTs > nowTs {
		saleStatus = uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_WAITSHELF)
	}

	condition := &pb.DisplayCondition{}
	err := json.Unmarshal([]byte(sdbPack.Condition), condition)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSalePackageListByStatus json.Unmarshal failed condition:%s err:%v", sdbPack.Condition, err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}
	condition.WhiteList = conf.GetPackageWhiteListById(sdbPack.SaleId)

	tmpSalePackage := &pb.SalePackage{
		SaleId:      sdbPack.SaleId,
		PackageInfo: tmpPackage,
		BeginTs:     sdbPack.BeginTs,
		EndTs:       sdbPack.EndTs,
		Condition:   condition,
		Weight:      sdbPack.Weight,
		SaleStatus:  saleStatus,
		Label:       sdbPack.Label,
		Operator:    sdbPack.Operator,
		UpdateTs:    sdbPack.UpdateTs,
		Remarks:     sdbPack.Remarks,
		MarketId:    sdbPack.MarketId,
	}
	return tmpSalePackage, nil
}

func dbPack2pbPack(dbPack *mysql.Package) *pb.Package {
	pbPackage := &pb.Package{
		Id:             strconv.Itoa(int(dbPack.Id)),
		ProductId:      dbPack.ProductId,
		Name:           dbPack.Name,
		Desc:           dbPack.Desc,
		OriginalPrice:  dbPack.OriginalPrice,
		Price:          dbPack.Price,
		Value:          dbPack.Value,
		Auto:           dbPack.Auto == 1,
		PayChannelList: strings.Split(dbPack.PayChList, ","),
		Days:           dbPack.Days,
		PackageStatus:  dbPack.PackageStatus,
		AutoValue:      dbPack.AutoValue,
		Operator:       dbPack.Operator,
		UpdateTs:       dbPack.UpdateTs,
		DiscountPrice:  dbPack.DiscountPrice,
		MarketId:       dbPack.MarketId,
	}
	return pbPackage
}

func (m *Manager) fillPackage(ctx context.Context, mapId2Package map[uint32]mysql.Package, salePackage mysql.SalePackage,
	saleStatus uint32, nowTs uint64, out *pb.GetSalePackageListByStatusResp) {

	if dbPackage, ok := mapId2Package[salePackage.PackageId]; ok {
		tmpPackage := dbPack2pbPack(&dbPackage)

		if saleStatus == uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_SHELF) && salePackage.BeginTs > nowTs {
			saleStatus = uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_WAITSHELF)
		}

		tmpSalePackage, err := sdbPack2spbPack(ctx, &salePackage, saleStatus, nowTs, tmpPackage)
		if nil != err {
			log.ErrorWithCtx(ctx, "fillPackage sdbPack2spbPack err:%v", err)
			return
		}
		out.PackageList = append(out.PackageList, tmpSalePackage)
	}
}

func (m *Manager) fillActivityPackage(ctx context.Context, mapId2Package map[uint32]mysql.Package, salePackage mysql.SalePackage,
	saleStatus uint32, nowTs uint64, out *pb.GetSalePackageListForActivityResp) {

	if dbPackage, ok := mapId2Package[salePackage.PackageId]; ok {
		tmpPackage := dbPack2pbPack(&dbPackage)

		// 判断是否是活动套餐，不是就过滤掉
		if !conf.IsActivityPackage(tmpPackage.GetId()) {
			log.DebugWithCtx(ctx, "fillPackage valid not activity package, packageId:%d", tmpPackage.GetId())
			return
		}

		if saleStatus == uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_SHELF) && salePackage.BeginTs > nowTs {
			saleStatus = uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_WAITSHELF)
		}

		tmpSalePackage, err := sdbPack2spbPack(ctx, &salePackage, saleStatus, nowTs, tmpPackage)
		if nil != err {
			log.ErrorWithCtx(ctx, "fillPackage sdbPack2spbPack err:%v", err)
			return
		}
		out.PackageList = append(out.PackageList, tmpSalePackage)
	}
}

func (m *Manager) GetSalePackageListByStatus(ctx context.Context, in *pb.GetSalePackageListByStatusReq) (*pb.GetSalePackageListByStatusResp, error) {
	out := &pb.GetSalePackageListByStatusResp{}

	log.DebugWithCtx(ctx, "GetSalePackageListByStatus begin in:%v", in)

	memCacheKey := fmt.Sprintf("sale_package_%d", in.GetStatus())
	// 从内存缓存获取
	if in.GetStatus() == uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_SHELF) && in.GetWithCache() {
		out, isExist := localCache.Get(memCacheKey)
		if isExist {
			log.DebugWithCtx(ctx, "GetSalePackageListByStatus get from mem cache in:%v out:%v", in, out.(*pb.GetSalePackageListByStatusResp))
			return out.(*pb.GetSalePackageListByStatusResp), nil
		}
	}

	nowTs := uint64(time.Now().Unix())
	salePackageList, err := m.db.GetSalePackageListByStatus(in.GetStatus(), nowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSalePackageListByStatus failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	var pids = []uint32{}
	for _, salePackage := range salePackageList {
		pids = append(pids, salePackage.PackageId)
		log.DebugWithCtx(ctx, "GetSalePackageListByStatus id:%d salepackage:%v", salePackage.PackageId, salePackage)
	}

	packageList, err := m.db.GetPackageListByPids(pids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSalePackageListByStatus GetPackageListByPids failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	log.DebugWithCtx(ctx, "GetSalePackageListByStatus size:%d", len(packageList))

	mapId2Package := make(map[uint32]mysql.Package)
	for _, Package := range packageList {
		mapId2Package[Package.Id] = Package
		log.DebugWithCtx(ctx, "GetSalePackageListByStatus id:%d package:%v", Package.Id, Package)
	}

	for _, salePackage := range salePackageList {
		m.fillPackage(ctx, mapId2Package, salePackage, in.GetStatus(), nowTs, out)
	}

	if in.GetStatus() == uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_UNSHELF) {
		// 需要按照时间排序
		sort.Slice(out.PackageList, func(i, j int) bool { return out.PackageList[i].GetUpdateTs() > out.PackageList[j].GetUpdateTs() })
	}

	// 内存缓存
	if in.GetStatus() == uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_SHELF) {
		localCache.Set(memCacheKey, out, 60)
	}

	log.DebugWithCtx(ctx, "GetSalePackageListByStatus end in:%v out:%v", in, out)
	return out, nil
}

func (m *Manager) GetSalePackageListForActivity(ctx context.Context, in *pb.GetSalePackageListForActivityReq) (*pb.GetSalePackageListForActivityResp, error) {
	out := &pb.GetSalePackageListForActivityResp{}
	log.DebugWithCtx(ctx, "GetSalePackageListForActivity begin in:%v", in)

	memCacheKey := "sale_package_for_activity"
	// 从内存缓存获取
	if in.GetWithCache() {
		out, isExist := localCache.Get(memCacheKey)
		if isExist {
			log.DebugWithCtx(ctx, "GetSalePackageListForActivity get from mem cache in:%v out:%v", in, out.(*pb.GetSalePackageListForActivityResp))
			return out.(*pb.GetSalePackageListForActivityResp), nil
		}
	}

	nowTs := uint64(time.Now().Unix())
	salePackageList, err := m.db.GetSalePackageListByStatus(uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_UNSHELF), nowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSalePackageListForActivity GetSalePackageListByStatus failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	var pids = []uint32{}
	for _, salePackage := range salePackageList {
		pids = append(pids, salePackage.PackageId)
		log.DebugWithCtx(ctx, "GetSalePackageListForActivity id:%d salepackage:%v", salePackage.PackageId, salePackage)
	}

	packageList, err := m.db.GetPackageListByPids(pids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSalePackageListForActivity GetPackageListByPids failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	log.DebugWithCtx(ctx, "GetSalePackageListForActivity size:%d", len(packageList))

	mapId2Package := make(map[uint32]mysql.Package)
	for _, Package := range packageList {
		mapId2Package[Package.Id] = Package
		log.DebugWithCtx(ctx, "GetSalePackageListForActivity id:%d package:%v", Package.Id, Package)
	}

	for _, salePackage := range salePackageList {
		m.fillActivityPackage(ctx, mapId2Package, salePackage, uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_UNSHELF), nowTs, out)
	}

	// 需要按照时间排序
	sort.Slice(out.PackageList, func(i, j int) bool { return out.PackageList[i].GetUpdateTs() > out.PackageList[j].GetUpdateTs() })

	// 内存缓存
	localCache.Set(memCacheKey, out, 60)
	log.DebugWithCtx(ctx, "GetSalePackageListForActivity end in:%v out:%v", in, out)
	return out, nil
}

func (m *Manager) SalePackageSort(ctx context.Context, in *pb.SalePackageSortReq) (*pb.SalePackageSortResp, error) {
	out := &pb.SalePackageSortResp{}

	log.DebugWithCtx(ctx, "SalePackageSort begin in:%v", in)
	if len(in.GetSaleIdList()) == 0 || len(in.GetSaleIdList()) > 100 {
		log.ErrorWithCtx(ctx, "SalePackageSort id list is empty or more than 200 in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "排序列表为空，或排序列表数据超过100")
	}

	num := uint32(len(in.GetSaleIdList()))

	for _, id := range in.GetSaleIdList() {
		err := m.db.SetSalePackageWeight(id, num)
		if err != nil {
			log.ErrorWithCtx(ctx, "SalePackageSort SetSalePackageWeight failed id:%d err:%s", id, err)
		}
		num--
	}

	log.DebugWithCtx(ctx, "SalePackageSort end in:%v out:%v", in, out)
	return out, nil
}

func (m *Manager) CheckSalePackageInfo(ctx context.Context, in *pb.CheckSalePackageInfoReq) (*pb.CheckSalePackageInfoResp, error) {
	out := &pb.CheckSalePackageInfoResp{}

	log.DebugWithCtx(ctx, "CheckSalePackageInfo begin in:%v", in)

	if in.GetEndTs() >= uint64(time.Now().Unix()) {
		err := m.checkTimeRange(ctx, in.GetPackageId(), in.GetSaleId(), in.GetEndTs(), in.GetBeginTs())
		if nil != err {
			log.ErrorWithCtx(ctx, "CheckSalePackageInfo checkTimeRange in:%v err:%v", in, err)
			return out, err
		}
	}

	log.DebugWithCtx(ctx, "CheckSalePackageInfo end in:%v out:%v", in, out)

	return out, nil
}

func (m *Manager) AddBannerAdvConf(ctx context.Context, in *pb.AddBannerAdvConfReq) (*pb.AddBannerAdvConfResp, error) {
	out := &pb.AddBannerAdvConfResp{}

	log.DebugWithCtx(ctx, "AddBannerAdvConf begin in:%v", in)

	maxWeiht, err := m.db.GetBannerAdvMaxWeight()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddBannerAdvConf failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	in.Conf.Weight = maxWeiht + 1

	err = m.db.AddBannerAdvConf(in.GetConf())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddBannerAdvConf failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	log.DebugWithCtx(ctx, "AddBannerAdvConf end in:%v out:%v", in, out)
	return out, err
}

func (m *Manager) GetBannerAdvConfListByStatus(ctx context.Context, in *pb.GetBannerAdvConfListByStatusReq) (*pb.GetBannerAdvConfListByStatusResp, error) {
	log.DebugWithCtx(ctx, "GetBannerAdvConfListByStatus begin in:%v", in)

	memCacheKey := fmt.Sprintf("banner_adv_%d", in.GetAdvStatus())
	// 从内存缓存获取
	if in.GetAdvStatus() == uint32(pb.BannerAdvStatus_ENUM_BANNER_ADD_USING) && in.GetWithCache() {
		out, isExist := localCache.Get(memCacheKey)
		if isExist {
			log.DebugWithCtx(ctx, "GetBannerAdvConfListByStatus get from mem cache in:%v out:%v", in, out.(*pb.GetBannerAdvConfListByStatusResp))
			return out.(*pb.GetBannerAdvConfListByStatusResp), nil
		}
	}

	out := &pb.GetBannerAdvConfListByStatusResp{}

	nowTs := uint64(time.Now().Unix())
	advList, err := m.db.GetBannerAdvConfListByStatus(in.GetAdvStatus(), nowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBannerAdvConfListByStatus failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	for _, adv := range advList {
		condition := &pb.DisplayCondition{}
		err = json.Unmarshal([]byte(adv.Condition), condition)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetBannerAdvConfListByStatus json.Unmarshal failed condition:%s err:%v", adv.Condition, err)
			continue
		}

		tmpConf := &pb.BannerAdvConf{
			AdvId:        adv.AdvId,
			PlatformType: adv.PlatformType,
			JumpUrl:      adv.JumpUrl,
			BeginTs:      adv.BeginTs,
			EndTs:        adv.EndTs,
			BannerImg:    adv.BannerImg,
			Condition:    condition,
			Remarks:      adv.Remarks,
			Weight:       adv.Weight,
			AdvStatus:    in.GetAdvStatus(),
			Operator:     adv.Operator,
		}

		out.ConfList = append(out.ConfList, tmpConf)
	}

	// 内存缓存
	if in.GetAdvStatus() == uint32(pb.BannerAdvStatus_ENUM_BANNER_ADD_USING) {
		localCache.Set(memCacheKey, out, 60)
	}

	log.DebugWithCtx(ctx, "GetBannerAdvConfListByStatus end in:%v out:%v", in, out)

	return out, nil
}

func (m *Manager) DelBannerAdvConf(ctx context.Context, in *pb.DelBannerAdvConfReq) (*pb.DelBannerAdvConfResp, error) {
	out := &pb.DelBannerAdvConfResp{}

	log.DebugWithCtx(ctx, "DelBannerAdvConf begin in:%v", in)
	err := m.db.DelBannerAdvConf(in.GetAdvId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelBannerAdvConf failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	log.DebugWithCtx(ctx, "DelBannerAdvConf end in:%v out:%v", in, out)
	return out, nil
}

func (m *Manager) UpdateBannerAdvConf(ctx context.Context, in *pb.UpdateBannerAdvConfReq) (*pb.UpdateBannerAdvConfResp, error) {
	out := &pb.UpdateBannerAdvConfResp{}

	log.DebugWithCtx(ctx, "UpdateBannerAdvConf begin in:%v", in)
	err := m.db.UpdateBannerAdvConf(in.GetConf())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateBannerAdvConf failed in:%v err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
	}

	log.DebugWithCtx(ctx, "UpdateBannerAdvConf end in:%v out:%v", in, out)
	return out, nil
}

func (m *Manager) BannerAdvConfSort(ctx context.Context, in *pb.BannerAdvConfSortReq) (*pb.BannerAdvConfSortResp, error) {
	out := &pb.BannerAdvConfSortResp{}

	log.DebugWithCtx(ctx, "BannerAdvConfSort begin in:%v")

	num := len(in.GetAdvIdList())
	if num == 0 || num > 100 {
		log.ErrorWithCtx(ctx, "BannerAdvConfSort failed list is empty or list size more than 100 in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "排序列表为空或者数量超过100")
	}

	for _, id := range in.GetAdvIdList() {
		err := m.db.SetBannerAdvWeight(id, uint32(num))
		if err != nil {
			log.ErrorWithCtx(ctx, "SetBannerAdvWeight failed id:%v err:%v", id, err)
		}
		num--
	}

	log.DebugWithCtx(ctx, "BannerAdvConfSort end in:%v out:%v", in, out)
	return out, nil
}

// 有签约而且有已经生效或者即将生效的非连续套餐
func (m *Manager) getCancelContractPopUpInfo(ctx context.Context, uid uint32) (string, error) {
	intervalKey := fmt.Sprintf("getCancelContractPopUpInfo_%v", uid)
	if val, _ := m.cache.Get(intervalKey); val > 0 {
		log.DebugWithCtx(ctx, "getCancelContractPopUpInfo uid:%v get lack fail", uid)
		return "", nil
	}

	contractList, err := m.getContracts(uid, false)
	if nil != err {
		log.ErrorWithCtx(ctx, "getCancelContractPopUpInfo sendPaySucessNotifyMsg getContracts uid:%v err:%v", uid, err)
		return "", err
	}
	if len(contractList) == 0 {
		log.DebugWithCtx(ctx, "getCancelContractPopUpInfo sendPaySucessNotifyMsg contractList empty uid:%v", uid)
		return "", nil
	}

	payRecList, err := m.GetPayRecordList(ctx, uid, AllMarketId, 0, 1024)
	if nil != err {
		log.DebugWithCtx(ctx, "getCancelContractPopUpInfo GetPayRecordList uid:%v err:%v", uid, err)
		return "", err
	}

	nowTs := time.Now()
	//是否有正在或者将要消耗的非连续套餐
	hasCurrPack := false
	for _, r := range payRecList {
		packConf := conf.GetPackageByID(r.GetPackageId())
		if nil == packConf {
			log.ErrorWithCtx(ctx, "getCancelContractPopUpInfo GetPackageByID fail uid:%v package:%+v", uid, r)
			continue
		}
		if packConf.Auto {
			continue
		}
		if r.GetExpireTime() <= nowTs.Unix() {
			continue
		}
		hasCurrPack = true
		break
	}
	if !hasCurrPack {
		return "", nil
	}

	payChannel := ""
	cnt := 0
	for _, contract := range contractList {
		if payChannel == "" || payChannel != contract.PayChannel {
			payChannel = contract.PayChannel
			cnt++
		}
	}
	both := cnt >= 2
	msg, interval := conf.GetCancelContractMsg(payChannel, both)
	if msg == "" {
		log.ErrorWithCtx(ctx, "getCancelContractPopUpInfo GetCancelContractMsg empty uid:%v paychannel:%v both:%v", uid, payChannel, both)
		return "", nil
	}

	log.DebugWithCtx(ctx, "getCancelContractPopUpInfo uid:%v msg:%v", uid, msg)

	weekHour := int64(time.Date(nowTs.Year(), nowTs.Month(), nowTs.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 1).Sub(nowTs).Seconds())
	if isLock, err := m.cache.Lock(intervalKey, time.Second*time.Duration(interval+weekHour)); !isLock {
		return "", err
	}
	return msg, nil
}

func (m *Manager) GetdeviceBuyRecord(deviceId string) (string, error) {
	return m.cache.GetRedis().Get(utils.DeviceBuyKey(deviceId)).Result()
}
