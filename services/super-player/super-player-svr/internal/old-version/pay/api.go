package pay

import (
	"context"
	"crypto/md5" // #nosec
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"push/pkg/log"
	"strings"
	"time"

	"github.com/golang/protobuf/jsonpb"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/superplayersvr"
	"golang.org/x/net/context/ctxhttp"
)

type ctxKey string

const (
	resultSuccess = "1"
	orderExist    = "-135"

	NotPayTime = "-710"
	NotBalance = "-715"

	CtxUidKey      ctxKey = "uid"
	CtxFmKey       ctxKey = "fm"
	CtxMarketIdKey ctxKey = "market_id"
)

type ApiResponse struct {
	Head pb.ApiResponseHeader `json:"head"`
	Body interface{}          `json:"body"`
}

type Client interface {
	PlaceOrder(ctx context.Context, in proto.MessageV1) (interface{}, error)
	GetContract(ctx context.Context, in proto.MessageV1) (interface{}, error)
	CancelContract(ctx context.Context, in proto.MessageV1) (interface{}, error)
	AutoPay(ctx context.Context, in proto.MessageV1) (interface{}, error)
	DelayPay(ctx context.Context, in proto.MessageV1) (interface{}, error)
}

type client struct {
	contextPath string
	clientID    string
	clientKey   string
	httpClient  *http.Client
	enc         *jsonpb.Marshaler
}

func NewClient(contextPath, clientID, clientKey string) Client {
	c := &client{
		contextPath: contextPath,
		clientID:    clientID,
		clientKey:   clientKey,
		httpClient:  &http.Client{Timeout: time.Second * 32},

		enc: &jsonpb.Marshaler{OrigName: true, EmitDefaults: true},
	}
	return c
}

// 加密
func (c *client) decode(data string) string {
	return data
}

// 签名
func (c *client) sign(data string) string {
	md5Text := fmt.Sprintf("%v%v", data, c.clientKey) //gconf.PayApiClientID
	md5Byte := md5.Sum([]byte(md5Text))               // #nosec
	encoded := base64.StdEncoding.EncodeToString(md5Byte[:])
	encoded = strings.Trim(encoded, "\r\n")
	return encoded
}

func (c *client) call(ctx context.Context, path string, request proto.MessageV1, responseBody interface{}) error {
	if responseBody == nil {
		return protocol.NewServerError(-5851, "The input `responseBody` must not be nil")
	}

	req, err := c.enc.MarshalToString(request)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to marshal request: %v %v", request, err)
		return protocol.NewServerError(-5851, err.Error())
	}

	if nil != err {
		log.ErrorWithCtx(ctx, "Failed to decode request: %v %v", request, err)
		return protocol.NewServerError(-5851, err.Error())
	}

	apiUrl := fmt.Sprintf("%s%s", c.contextPath, path)

	start := time.Now()
	defer func() {
		elapsed := time.Since(start)
		elapsedMillisecond := uint32(elapsed / time.Millisecond)
		if elapsedMillisecond < 1 {
			elapsedMillisecond = 1
		}
		log.InfoWithCtx(ctx, "POST %s %s tooks %d ms", apiUrl, request, elapsedMillisecond)
	}()

	httpReq, err := http.NewRequest("POST", apiUrl, strings.NewReader(c.decode(req)))
	if nil != err {
		log.ErrorWithCtx(ctx, "Failed to NewRequest request: %v %v", request, err)
		return protocol.NewServerError(-5851, err.Error())
	}
	userId, fm, marketId := "", "", ""
	if ctx.Value(CtxUidKey) != nil {
		userId = ctx.Value(CtxUidKey).(string)
	}
	if ctx.Value(CtxFmKey) != nil {
		fm = ctx.Value(CtxFmKey).(string)
	}
	if ctx.Value(CtxMarketIdKey) != nil {
		marketId = ctx.Value(CtxMarketIdKey).(string)
	}

	httpReq.Header.Set("fm", fm)
	httpReq.Header.Set("marketId", marketId)
	httpReq.Header.Set("cliBuyerId", userId)
	httpReq.Header.Set("clientId", c.clientID)
	httpReq.Header.Set("sign", c.sign(req))
	httpReq.Header.Set("Content-Type", "application/json")
	resp, err := ctxhttp.Do(ctx, c.httpClient, httpReq)

	if err != nil {
		log.ErrorWithCtx(ctx, "POST %s %s post error %v", apiUrl, req, err)
		return protocol.NewServerError(-5851, err.Error())
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("POST %s %s status %s", apiUrl, req, resp.Status)
		log.ErrorWithCtx(ctx, "Response code not OK: %v", err)
		return protocol.NewServerError(-5851, err.Error())
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "POST %s %s read body failed: %+v", apiUrl, req, err)
		return protocol.NewServerError(-5851, err.Error())
	}

	response := &ApiResponse{Body: responseBody}
	err = json.Unmarshal(body, response)
	if err != nil {
		log.ErrorWithCtx(ctx, "POST %s %s unmarshal body %v failed: %v", apiUrl, req, string(body), err)
		return protocol.NewServerError(-5851, err.Error())
	}
	if response.Head.Result != resultSuccess {
		log.ErrorWithCtx(ctx, "POST %s %s result error (%s, %s)", apiUrl, req, response.Head.Result, response.Head.Message)
		if response.Head.Result != orderExist { //已经存在的订单
			err = errors.New(response.Head.Result)
			return err
		}
	}
	log.InfoWithCtx(ctx, "POST %s %v %v %v %s OK %+v body:%v", apiUrl, userId, fm, marketId, req, response, string(body))

	return nil
}
