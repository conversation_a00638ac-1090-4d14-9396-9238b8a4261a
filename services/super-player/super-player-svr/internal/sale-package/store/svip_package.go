package store

import (
	"context"
	pb "golang.52tt.com/protocol/services/superplayersvr"
	"time"

	"golang.52tt.com/services/super-player/super-player-svr/internal/sale-package/mgr"

	"github.com/jmoiron/sqlx"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
)

// AddSvipPackage 添加套餐
func (s *Store) AddSvipPackage(ctx context.Context, info *mgr.Package) error {
	log.DebugWithCtx(ctx, "AddSvipPackage info: %v", info)

	sql := "insert into tbl_superplayer_svip_package (`product_id`, `name`, `desc`, `original_price`, `price`, `value`, `auto`, `pay_ch_list`, `days`, `package_status`, `auto_value`, `operator`, `update_ts`, `market_id`, `is_show_coupon`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
	_, err := s.db.ExecContext(ctx, sql, info.ProductId, info.Name, info.Desc, info.OriginalPrice, info.Price, info.Value, info.Auto, info.PayChList, info.Days, info.PackageStatus, info.AutoValue, info.Operator, uint64(time.Now().Unix()), info.MarketId, info.IsShowCoupon)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddSvipPackage error: %v", err)
		return err
	}

	log.DebugWithCtx(ctx, "AddSvipPackage success info: %v", info)
	return nil
}

// UpdateSvipPackage 更新套餐
func (s *Store) UpdateSvipPackage(ctx context.Context, info *mgr.Package, isUpdateStatus bool) error {
	log.DebugWithCtx(ctx, "UpdateSvipPackage info: %v isUpdateStatus: %v", info, isUpdateStatus)

	args := make([]interface{}, 0)
	sql := "update tbl_superplayer_svip_package set `market_id` = ?, `operator` = ?, `update_ts` = ?, `is_show_coupon` = ? "
	args = append(args, info.MarketId, info.Operator, uint64(time.Now().Unix()), info.IsShowCoupon)
	if isUpdateStatus {
		sql += ", `package_status` = ?"
		args = append(args, info.PackageStatus)
	} else {
		sql += ", `name` = ?, `original_price` = ?, `desc` = ?"
		args = append(args, info.Name, info.OriginalPrice, info.Desc)
	}
	if info.Days > 0 {
		sql += ", `days` = ?"
		args = append(args, info.Days)
	}
	sql += " where `id` = ?"
	args = append(args, info.Id)
	_, err := s.db.ExecContext(ctx, sql, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateSvipPackage error: %v", err)
		return err
	}

	log.DebugWithCtx(ctx, "UpdateSvipPackage success info: %v isUpdateStatus: %v", info, isUpdateStatus)
	return nil
}

// GetSvipPackageListByStatus 获取套餐列表
func (s *Store) GetSvipPackageListByStatus(ctx context.Context, packageStatus uint32) ([]*mgr.Package, error) {
	log.DebugWithCtx(ctx, "GetSvipPackageListByStatus packageStatus: %v", packageStatus)

	sql := "select `id`, `product_id`, `name`, `desc`, `original_price`, `price`, `value`, `auto`, `pay_ch_list`, `days`, `package_status`, `auto_value`, `operator`, `update_ts`, `discount_price`, `market_id`, `is_show_coupon`, `group_id` from tbl_superplayer_svip_package where package_status = ? and is_upgrade = 0"
	infoList := make([]*mgr.Package, 0)
	err := s.db.SelectContext(ctx, &infoList, sql, packageStatus)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "GetSvipPackageListByStatus no data packageStatus: %v", packageStatus)
			return infoList, nil
		}
		log.ErrorWithCtx(ctx, "GetSvipPackageListByStatus error: %v", err)
		return nil, err
	}

	for _, item := range infoList {
		item.PackageType = uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP)
	}

	log.DebugWithCtx(ctx, "GetSvipPackageListByStatus success packageStatus: %v infoList: %v", packageStatus, infoList)
	return infoList, nil
}

// GetAllSvipPackageList 获取所有套餐列表
func (s *Store) GetAllSvipPackageList(ctx context.Context) ([]*mgr.Package, error) {
	log.DebugWithCtx(ctx, "GetAllSvipPackageList")

	sql := "select `id`, `product_id`, `name`, `desc`, `original_price`, `price`, `value`, `auto`, `pay_ch_list`, `days`, `package_status`, `auto_value`, `operator`, `update_ts`, `discount_price`, `market_id`, `is_show_coupon`, `group_id` from tbl_superplayer_svip_package where is_upgrade = 0"
	infoList := make([]*mgr.Package, 0)
	err := s.db.SelectContext(ctx, &infoList, sql)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "GetAllSvipPackageList no data")
			return infoList, nil
		}
		log.ErrorWithCtx(ctx, "GetAllSvipPackageList error: %v", err)
		return nil, err
	}

	for _, item := range infoList {
		item.PackageType = uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP)
	}

	log.DebugWithCtx(ctx, "GetAllSvipPackageList success infoList: %v", infoList)
	return infoList, nil
}

// GetSvipPackageListByPids 获取套餐列表
func (s *Store) GetSvipPackageListByPids(ctx context.Context, pidList []uint32) ([]*mgr.Package, error) {
	log.DebugWithCtx(ctx, "GetSvipPackageListByPids pidList: %v", pidList)

	sql := "select `id`, `product_id`, `name`, `desc`, `original_price`, `price`, `value`, `auto`, `pay_ch_list`, `days`, `package_status`, `auto_value`, `operator`, `update_ts`, `discount_price`, `market_id`, `is_show_coupon`, `group_id` from tbl_superplayer_svip_package where `id` in (?) and is_upgrade = 0"
	infoList := make([]*mgr.Package, 0)
	var inQuery string
	args := make([]interface{}, 0)
	inQuery, args, err := sqlx.In(sql, pidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "batchGetUserScoreDailySumWithMinRemainScore In fail, err :%v", err)
		return infoList, nil
	}
	err = s.db.SelectContext(ctx, &infoList, inQuery, args...)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "GetSvipPackageListByPids no data pidList: %v", pidList)
			return infoList, nil
		}
		log.ErrorWithCtx(ctx, "GetSvipPackageListByPids error: %v", err)
		return nil, err
	}

	for _, item := range infoList {
		item.PackageType = uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP)
	}

	log.DebugWithCtx(ctx, "GetSvipPackageListByPids success pidList: %v infoList: %v", pidList, infoList)
	return infoList, nil
}
