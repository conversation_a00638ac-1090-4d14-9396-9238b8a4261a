package conf

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app/channel"
	"io/ioutil"
	"math/rand"
	"strings"
	"sync"
	"time"
)

const (
	BusinessConfEnFilePath = "/data/oss/conf-center/tt/en_recsvr.json"

	ENUM_REC_SCENE_QUICK           = 1 // 娱乐快速推荐场景
	ENUM_REC_SCENE_FIRST           = 2 // 娱乐首页推荐列表场景
	ENUM_REC_SCENE_SECOND          = 3 // 娱乐二级推荐列表场景
	ENUM_REC_SCENE_SECOND_PERSONAL = 4 // 娱乐二级个性标签推荐列表场景
)

var LastConfMd5Sum [md5.Size]byte

// 推送配置
type EnRecBanConf struct {
	BanSceneType uint32 `json:"ban_scene_type"` //see ENUM_REC_SCENE
	BanBeginTs   uint32 `json:"ban_begin_ts"`
	BanEndTs     uint32 `json:"ban_end_ts"`
	MarketId     uint32 `json:"market_id"`
	Desc         string `json:"desc"`
}

// 弹幕游戏标识
type GameCertConf struct {
	GameType    uint32 `json:"game_type"`
	CertUrl     string `json:"cert_url"`
	GameName    string `json:"game_name"`
	IconUrl     string `json:"icon_url"`
	BgColor     string `json:"bg_color"`
	FontColor   string `json:"font_color"`
	TextContent string `json:"text_content"`
}

// 兜底配置
type BottomUpConf struct {
	SceneType      uint32   `json:"scene_type"`
	BottomUpSwitch uint32   `json:"bottom_up_switch" `
	UidList        []uint32 `json:"uid_list"`
	WhiteUidList   []uint32 `json:"white_uid_list"` // 直接走自动兜底用户白名单，用于灰度测试
}

type BusinessConf struct {
	RecBanConfList           []*EnRecBanConf             `json:"en_rec_ban_conf"` //娱乐推荐限制配置
	GameCertConfList         []*GameCertConf             `json:"game_cert_conf"`  // 弹幕游戏标识配置
	BottomUpConfList         []*BottomUpConf             `json:"bottom_up_conf"`
	ChannelCertMap           map[uint32]*ChannelCertInfo `json:"channel_cert_map"`            // 房间标识类型与对应的内容
	HourRankConf             *HourRankConf               `json:"hour_rank_conf"`              // 小时榜配置
	ChannelCertSort          []uint32                    `json:"channel_cert_sort"`           // 房间标识类型排序
	ShowRecommendIcon        bool                        `json:"show_recommend_icon"`         // 是否展示推荐图标
	DefaultAccompanyRankIcon string                      `json:"default_accompany_rank_icon"` // 默认陪伴榜图标
	LastConfMd5Sum           [md5.Size]byte
}

type HourRankConf struct {
	TotalTopN uint32 `json:"total_top_n"` // 总榜前N名
	TopN      uint32 `json:"top_n"`       // 小时榜前N名
}

type ChannelCertInfo struct {
	DetailType      uint32           `json:"detail_type"`       // 详情类型
	DetailContent   []*DetailContent `json:"detail_content"`    // 详情内容
	DetailIcon      string           `json:"detail_icon"`       // 详情图标
	DetailBgColor   string           `json:"detail_bg_color"`   // 详情背景色
	DetailFontColor string           `json:"detail_font_color"` // 详情字体颜色
}

type DetailContentType uint32

const (
	DetailContentTypeNormal       DetailContentType = 1
	DetailContentTypeMicUserSex   DetailContentType = 2
	DetailContentTypeMicTotalUser DetailContentType = 3
	DetailContentTypeRelationship DetailContentType = 4
	DetailContentTypeGift         DetailContentType = 5
	DetailContentTypeGame         DetailContentType = 6
)

type DetailContent struct {
	Status  uint32            `json:"status"`
	Content []string          `json:"content"`
	Type    DetailContentType `json:"type"`
	Ext     *DetailContentExt `json:"ext"`
}

type DetailContentExt struct {
	MicUserQuantifier   string `json:"mic_user_quantifier"`    // 麦上用户量词
	MicFemaleQuantifier string `json:"mic_female_quantifier""` // 麦上女性用户量词
	MicMaleQuantifier   string `json:"mic_male_quantifier"`    // 麦上男性用户量词
}

type BusinessConfManager struct {
	done  chan interface{}
	mutex sync.RWMutex
	conf  *BusinessConf
}

func NewBusinessConfManager() (*BusinessConfManager, error) {
	businessConf := &BusinessConf{}

	_, err := businessConf.Parse(BusinessConfEnFilePath)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return nil, err
	}

	confMgr := &BusinessConfManager{
		conf:  businessConf,
		done:  make(chan interface{}),
		mutex: sync.RWMutex{},
	}

	go confMgr.Watch(BusinessConfEnFilePath)

	return confMgr, nil
}

func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return false, err
	}

	md5Sum := md5.Sum(data)
	if md5Sum == c.LastConfMd5Sum {
		isChange = false
		return
	}

	err = json.Unmarshal(data, &c)
	if err != nil {
		return false, err
	}

	c.LastConfMd5Sum = md5Sum

	log.Infof("BusinessConf:%+v RecBanConfList:%+v ", c, c.RecBanConfList)
	return true, nil
}

func (bm *BusinessConfManager) Reload(file string) error {
	businessConf := &BusinessConf{}

	isChange, err := businessConf.Parse(file)
	if err != nil {
		log.Errorf("NewMissionConfManager fail to Parse MissionConf err:%v", err)
		return err
	}

	if isChange {
		bm.mutex.Lock()
		bm.conf = businessConf
		bm.mutex.Unlock()

		log.Infof("Reload conf:%+v", businessConf)
	}

	return nil
}

func (bm *BusinessConfManager) Watch(file string) {
	log.Infof("Watch start. file:%s", file)

	for {
		select {
		case _, ok := <-bm.done:
			if !ok {
				log.Infof("Watch done")
				return
			}

		case <-time.After(30 * time.Second):
			log.Debugf("Watch check change")

			err := bm.Reload(file)
			if err != nil {
				log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
			}
		}
	}
}

func (bm *BusinessConfManager) CheckRecTypeIsBanned(recSceneType, marketId uint32) bool {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	nowTs := uint32(time.Now().Unix())
	for _, banConf := range bm.conf.RecBanConfList {
		if banConf.BanSceneType == recSceneType && banConf.MarketId == marketId && banConf.BanBeginTs <= nowTs && banConf.BanEndTs >= nowTs {
			return true
		}
	}

	return false
}

func (bm *BusinessConfManager) GetGameCertUrl(gameType uint32) string {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	for _, certConf := range bm.conf.GameCertConfList {
		if certConf.GameType == gameType {
			return certConf.CertUrl
		}
	}

	return ""
}

func (bm *BusinessConfManager) GetGameCertName(gameType uint32) string {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	for _, certConf := range bm.conf.GameCertConfList {
		if certConf.GameType == gameType {
			return certConf.GameName
		}
	}

	return ""
}

func (bm *BusinessConfManager) GetGameConfig(gameType uint32) *GameCertConf {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	for _, certConf := range bm.conf.GameCertConfList {
		if certConf.GameType == gameType {
			return certConf
		}
	}

	return &GameCertConf{}
}

func (bm *BusinessConfManager) CheckIsBottomUp(uid, sceneType uint32) bool {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	for _, conf := range bm.conf.BottomUpConfList {
		if conf.SceneType == sceneType && conf.BottomUpSwitch == 1 {
			if len(conf.UidList) != 0 {
				for _, id := range conf.UidList {
					if id == uid {
						return true
					}
				}
			} else {
				return true
			}
		}
	}

	return false
}

func (bm *BusinessConfManager) CheckIsBottomUpWhiteUid(uid, sceneType uint32) bool {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	for _, conf := range bm.conf.BottomUpConfList {
		for _, id := range conf.WhiteUidList {
			if id == uid {
				return true
			}
		}
	}

	return false
}

func (bm *BusinessConfManager) Close() {
	close(bm.done)
}

type ChannelCertExtInfo struct {
	MainUserSex      uint32
	MicFemaleCount   uint32
	MicMaleCount     uint32
	RelationshipName string
	GiftName         string
	GameName         string
}

func (bm *BusinessConfManager) GetChannelCertInfo(detailType, status uint32, ext *ChannelCertExtInfo) *channel.RealTimeChannelDetail {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	content := ""
	detail := &channel.RealTimeChannelDetail{}
	if channelCertInfo, ok := bm.conf.ChannelCertMap[detailType]; ok {
		for _, item := range channelCertInfo.DetailContent {
			if item.Status == status {
				content = getRandomElement(item.Content)
				if item.Type == DetailContentTypeMicUserSex {
					if ext.MicMaleCount > 0 && ext.MainUserSex == 0 {
						// 用户是女性，推男性代词
						content = strings.ReplaceAll(content, "{sex}", fmt.Sprintf("%d%s", ext.MicMaleCount, item.Ext.MicMaleQuantifier))
					} else if ext.MicFemaleCount > 0 && ext.MainUserSex == 1 {
						// 用户是男性，推女性代词
						content = strings.ReplaceAll(content, "{sex}", fmt.Sprintf("%d%s", ext.MicFemaleCount, item.Ext.MicFemaleQuantifier))
					} else {
						content = strings.ReplaceAll(content, "{sex}", "")
					}
				} else if item.Type == DetailContentTypeMicTotalUser {
					if ext.MicFemaleCount+ext.MicMaleCount > 0 {
						content = strings.ReplaceAll(content, "{total}", fmt.Sprintf("%d%s", ext.MicFemaleCount+ext.MicMaleCount, item.Ext.MicUserQuantifier))
					} else {
						content = strings.ReplaceAll(content, "{total}", "")
					}
				} else if item.Type == DetailContentTypeGift {
					content = strings.ReplaceAll(content, "{gift}", ext.GiftName)
				} else if item.Type == DetailContentTypeRelationship {
					content = strings.ReplaceAll(content, "{relation}", ext.RelationshipName)
				} else if item.Type == DetailContentTypeGame {
					content = strings.ReplaceAll(content, "{game}", ext.GameName)
				}
			}
		}

		detail = &channel.RealTimeChannelDetail{
			DetailType:            detailType,
			DetailContent:         content,
			DetailIcon:            channelCertInfo.DetailIcon,
			DetailBackgroundColor: channelCertInfo.DetailBgColor,
			DetailFontColor:       channelCertInfo.DetailFontColor,
		}
	} else {
		// 如果没有配置这个阶段的配置直接返回nil
		return nil
	}

	return detail
}

func getRandomElement(arr []string) string {
	source := rand.NewSource(time.Now().UnixNano()) // 创建一个随机源
	r := rand.New(source)                           // 创建一个随机数生成器
	randomIndex := r.Intn(len(arr))                 // 生成一个在数组长度范围内的随机索引
	return arr[randomIndex]
}

func (bm *BusinessConfManager) GetHourRankConf() *HourRankConf {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	if bm.conf.HourRankConf == nil {
		return &HourRankConf{}
	}

	return bm.conf.HourRankConf
}

func (bm *BusinessConfManager) GetChannelCertSort() map[uint32]int {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	sortMap := make(map[uint32]int)
	for i, v := range bm.conf.ChannelCertSort {
		sortMap[v] = i
	}

	return sortMap
}

func (bm *BusinessConfManager) GetShowRecommendIcon() bool {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	return bm.conf.ShowRecommendIcon
}

func (bm *BusinessConfManager) GetDefaultAccompanyRankIcon() string {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	if bm.conf.DefaultAccompanyRankIcon == "" {
		return "https://obs-cdn.52tt.com/tt/fe-moss/tt-server/fellow_house/<EMAIL>"
	}
	return bm.conf.DefaultAccompanyRankIcon
}
