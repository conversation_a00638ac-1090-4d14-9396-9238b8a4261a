package main

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"

	pb "golang.52tt.com/protocol/services/channel-recommend-relationship"

	"golang.52tt.com/services/channel-recommend/channel-recommend-relationship/internal"

	// use server startup
	"gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server"
)

func main() {
	var (
		svr *internal.Server
		cfg = &internal.StartConfig{}
		err error
	)

	// config file support yaml & json, default channel-recommend-relationship.json/yaml
	if err := server.NewServer("channel-recommend-relationship", cfg).
		AddGrpcServer(server.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				kafka.InitEventLinkSubWithGrpcSvr(s)
				if svr, err = internal.NewServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterChannelRecommendRelationshipServiceServer(s, svr)
				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
