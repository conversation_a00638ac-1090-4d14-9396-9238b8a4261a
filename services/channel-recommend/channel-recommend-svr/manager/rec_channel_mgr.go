package manager

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channel-recommend-svr"
	enSvrPb "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/model"
	"math/rand"
	"time"
)

func (m *ChannelRecommendManager) BatGetChannelCommonInfo(ctx context.Context, cidList []uint32) (map[uint32]*enSvrPb.ChannelCommonInfo, error) {
	mapId2Info := make(map[uint32]*enSvrPb.ChannelCommonInfo, 0)

	// 先查cache
	tmpCidList := make([]uint32, 0)
	for index, cid := range cidList {
		tmpCidList = append(tmpCidList, cid)
		if len(tmpCidList) == 100 || index == len(cidList)-1 {
			tmpMap, err := m.clientPool.Cache.BatGetChannelCommon(tmpCidList)
			if err != nil {
				log.ErrorWithCtx(ctx, "BatGetChannelCommonInfo BatGetChannelCommon failed tmpCidList:%v err:%v", tmpCidList, err)
				return nil, err
			}

			for k, v := range tmpMap {
				log.DebugWithCtx(ctx, "BatGetChannelCommonInfo cache key:%d info:%v", k, v)
				mapId2Info[k] = v
			}

			tmpCidList = tmpCidList[0:0]
		}
	}

	queryDbList := make([]uint32, 0)
	for _, cid := range cidList {
		if _, ok := mapId2Info[cid]; !ok {
			queryDbList = append(queryDbList, cid)
		}
	}

	// 查db
	for index, cid := range queryDbList {
		tmpCidList = append(tmpCidList, cid)
		if len(tmpCidList) == 100 || index == len(tmpCidList)-1 {
			tmpMap, err := m.clientPool.Store.BatGetChannelCommon(tmpCidList)
			if err != nil {
				log.ErrorWithCtx(ctx, "BatGetChannelCommonInfo BatGetChannelCommon failed tmpCidList:%v err:%v", tmpCidList, err)
				return nil, err
			}

			// 不写缓存，c++服务会写

			for k, v := range tmpMap {
				info := &enSvrPb.ChannelCommonInfo{
					ChannelId: &(v.ChannelId),
					TagId:     &(v.UserTagId),
					SubTag:    &(v.SubTag),
					Sections:  model.ParseTimeSection(v.Sections),
				}

				log.DebugWithCtx(ctx, "BatGetChannelCommonInfo db key:%d info:%v val:%v", k, info, v)
				mapId2Info[k] = info
			}

			tmpCidList = tmpCidList[0:0]
		}
	}

	log.DebugWithCtx(ctx, "BatGetChannelCommonInfo cidList:%v queryDbList:%v mapId2Info:%v", cidList, queryDbList, mapId2Info)
	return mapId2Info, nil
}

func (m *ChannelRecommendManager) getRecommendChannelList(ctx context.Context, index, start, count, tagId, userCategory uint32,
	mapKey2List map[string][]uint32) ([]uint32, bool) {

	cidList := make([]uint32, 0)

	bIsEnd := false                             //是否已经到末尾了
	bIsGetAll := true                           // 是否获取全部了
	mapLevel2LackCnt := make(map[uint32]int, 0) // 每一等级缺少的

	preLevelLackCnt := 0 //需要下当前等级补充的数量
	preLevelNeedCnt := 0 // 前面等级需要的数据
	preLevelRealCnt := 0 // 前面等级总数量

	mapLevel2Ratio := m.clientPool.BusinessConfMgr.GetLevelRatioMap()
	for level := uint32(pb.ChannelLevel_Channel_Level_S); level <= uint32(pb.ChannelLevel_Channel_Level_C); level++ {
		if level > uint32(pb.ChannelLevel_Channel_Level_S) {
			preLevelNeedCnt += int(float32(start) * mapLevel2Ratio[level-1])
			preLevelRealCnt += len(mapKey2List[model.GetLevelTagKey(index, tagId, level-1, userCategory)])
		}

		preLevelLackStart := 0
		if preLevelNeedCnt > preLevelRealCnt {
			preLevelLackStart = preLevelNeedCnt - preLevelRealCnt
		}

		levelKey := model.GetLevelTagKey(index, tagId, level, userCategory)
		var currLevelRealCnt = len(mapKey2List[levelKey])
		var currLevelNeed = int(float32(count)*mapLevel2Ratio[level]) + preLevelLackCnt
		var currLevelStart = int(float32(start)*mapLevel2Ratio[level]) + preLevelLackStart

		levelTakeCnt := currLevelNeed
		if (currLevelRealCnt - currLevelStart) < currLevelNeed {
			levelTakeCnt = currLevelRealCnt - currLevelStart
		}

		if levelTakeCnt < 0 {
			levelTakeCnt = 0
		}

		if level == uint32(pb.ChannelLevel_Channel_Level_C) {
			for tmpLevel := uint32(pb.ChannelLevel_Channel_Level_S); tmpLevel <= uint32(pb.ChannelLevel_Channel_Level_B); tmpLevel++ {
				// 前面等级还没拿完，这个等级数量不够，返回bIsEnd=false，让客户端继续获取
				if mapLevel2LackCnt[tmpLevel] == 0 {
					bIsGetAll = false
				}
			}

			if currLevelStart+levelTakeCnt >= currLevelRealCnt && bIsGetAll {
				bIsEnd = true
			}
		}

		preLevelLackCnt = currLevelNeed - levelTakeCnt
		mapLevel2LackCnt[level] = preLevelLackCnt

		log.DebugWithCtx(ctx, "getRecommendChannelList preLevelNeedCnt:%d preLevelRealCnt:%d preLevelLackStart:%d currLevelRealCnt:%d currLevelNeed:%d"+
			" currLevelStart:%d preLevelLackCnt:%d bIsGetAll:%v level:%d index:%d start:%d", preLevelNeedCnt, preLevelRealCnt, preLevelLackStart, currLevelRealCnt, currLevelNeed,
			currLevelStart, preLevelLackCnt, bIsGetAll, level, index, start)

		if levelTakeCnt <= 0 {
			continue
		}

		if currLevelStart < len(mapKey2List[levelKey]) {
			currLevelEnd := currLevelStart + levelTakeCnt
			if currLevelStart+levelTakeCnt > len(mapKey2List[levelKey]) {
				currLevelEnd = len(mapKey2List[levelKey])
			}
			cidList = append(cidList, mapKey2List[levelKey][currLevelStart:currLevelEnd]...)
		}
	}

	return cidList, bIsEnd
}

func (m *ChannelRecommendManager) GetActChannels(ctx context.Context, uid uint32) map[uint32][]uint32 {
	mapCategory2List := make(map[uint32][]uint32)
	// 活动房间
	m.actChannelRwLock.RLock()
	for k, v := range m.mapType2ActChannelList {
		mapCategory2List[k] = v
	}
	m.actChannelRwLock.RUnlock()
	return mapCategory2List
}

func (m *ChannelRecommendManager) getActAndEnterChannel(ctx context.Context, uid uint32, nowTm time.Time) map[uint32][]uint32 {
	mapCategory2List := make(map[uint32][]uint32, 0)

	// 活动房间
	m.actChannelRwLock.RLock()
	for k, v := range m.mapType2ActChannelList {
		mapCategory2List[k] = v
	}
	m.actChannelRwLock.RUnlock()

	// 获取曾经进入的房间
	cidList, err := m.clientPool.Cache.GetUseEnterChannel(uid, uint32(nowTm.Unix()))
	if err != nil {
		log.ErrorWithCtx(ctx, "getActAndEnterChannel GetUseEnterChannel uid:%d err:%v", uid, err)
	} else {
		rand.Shuffle(len(cidList), func(i, j int) {
			cidList[i], cidList[j] = cidList[j], cidList[i]
		})
		mapCategory2List[uint32(pb.ChannelCategory_Used_Enter)] = cidList
	}

	log.DebugWithCtx(ctx, "getActAndEnterChannel mapCategory2List:%v", mapCategory2List)
	return mapCategory2List
}

func (m *ChannelRecommendManager) getChannelSubTag(ctx context.Context, cid, category uint32, commonInfo *enSvrPb.ChannelCommonInfo, nowTm time.Time) string {
	m.actChannelRwLock.RLock()
	tmpMapId2Act := m.mapId2ActInfo
	m.actChannelRwLock.RUnlock()

	var subTag string
	if category == uint32(pb.ChannelCategory_Activity_BIG) || category == uint32(pb.ChannelCategory_Activity_SAMLL) {
		subTag = tmpMapId2Act[cid].SubTag
	} else {
		for _, sect := range commonInfo.GetSections() {
			if sect.GetBeginTime() <= uint32(nowTm.Unix()) && sect.GetEndTime() >= uint32(nowTm.Unix()) {
				subTag = commonInfo.GetSubTag()
			}
		}
	}

	log.DebugWithCtx(ctx, "getChannelSubTag end cid:%d subTag:%v", cid, subTag)
	return subTag
}

func (m *ChannelRecommendManager) GetRecommendChannel(ctx context.Context, req *pb.GetRecommendChannelReq) (*pb.GetRecommendChannelResp, error) {
	resp := &pb.GetRecommendChannelResp{}

	nowTm := time.Now()

	mapCategory2List := make(map[uint32][]uint32, 0)
	if req.GetStart() == 0 {
		// 获取活动房间和曾经进入房间
		mapCategory2List = m.getActAndEnterChannel(ctx, req.GetUid(), nowTm)
	}

	index, err := m.clientPool.Cache.GetUserRandIndex(req.GetUid(), model.MaxBucketCnt, model.REC_SCENE_FIRST, req.GetStart() == 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendChannel GetUserRandIndex failed req:%v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	mapLevel2List := make(map[string][]uint32, 0)
	m.levelChannelRwLock.RLock()
	for key, list := range m.mapKey2LevelChannel {
		mapLevel2List[key] = list
	}
	m.levelChannelRwLock.RUnlock()

	cidList, isEnd := m.getRecommendChannelList(ctx, index, req.GetStart(), req.GetCount(), 0, req.GetUserCategory(), mapLevel2List)
	mapCategory2List[uint32(pb.ChannelCategory_Normal_Level)] = cidList

	categoryList := []uint32{uint32(pb.ChannelCategory_Activity_BIG), uint32(pb.ChannelCategory_Used_Enter), uint32(pb.ChannelCategory_Activity_SAMLL),
		uint32(pb.ChannelCategory_Normal_Level)}

	for _, category := range categoryList {
		cidList := mapCategory2List[category]

		mapCid2CommonInfo, err := m.BatGetChannelCommonInfo(ctx, cidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetRecommendChannel BatGetChannelCommonInfo failed req:%v len:%d err:%v", req, len(cidList), err)
			return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
		}

		log.DebugWithCtx(ctx, "GetRecommendChannel req:%v cidList:%v", req, cidList)

		for _, cid := range cidList {
			if commonInfo, ok := mapCid2CommonInfo[cid]; ok {
				subTag := m.getChannelSubTag(ctx, cid, category, commonInfo, nowTm)

				resp.ChannelList = append(resp.ChannelList, &pb.ChannelRecommendSimpleInfo{
					ChannelId: cid,
					Category:  category,
					TagId:     commonInfo.GetTagId(),
					SubTag:    subTag,
				})

				log.DebugWithCtx(ctx, "GetRecommendChannel req:%v cid:%d", req, cid)
			}
		}
	}

	resp.IsEnd = isEnd
	log.DebugWithCtx(ctx, "GetRecommendChannel end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) batchGetChLabelInfo(cids []uint32) map[uint32]string {
	if len(cids) == 0 {
		return nil
	}
	m.cidLabelRwLock.RLock()
	defer m.cidLabelRwLock.RUnlock()
	labels := make(map[uint32]string, 0)
	for _, cid := range cids {
		if label, ok := m.mapCid2Label[cid]; !ok {
			labels[cid] = label
		}
	}
	return labels
}

func (m *ChannelRecommendManager) BatGetChannelSoundLabel(ctx context.Context, req *pb.BatGetChannelSoundLabelReq) (*pb.BatGetChannelSoundLabelResp, error) {
	resp := &pb.BatGetChannelSoundLabelResp{
		MapCidLabel: make(map[uint32]string, 0),
	}

	m.cidLabelRwLock.RLock()
	defer m.cidLabelRwLock.RUnlock()

	for _, cid := range req.GetCidList() {
		resp.MapCidLabel[cid] = m.mapCid2Label[cid]
	}

	log.DebugWithCtx(ctx, "BatGetChannelSoundLabel end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) GetTagConfigInfoList(ctx context.Context, req *pb.GetTagConfigInfoListReq) (*pb.GetTagConfigInfoListResp, error) {
	resp := &pb.GetTagConfigInfoListResp{}

	tagList := m.dynamicCfg.GetChannelTagTypes().GetTagConfList()
	for _, tag := range tagList {
		tmpInfo := &pb.TagConfigInfo{
			RootTagName: tag.Name,
			RootTagId:   tag.Tagid,
		}

		for _, subTag := range tag.Subs {
			tmpInfo.SubTagList = append(tmpInfo.SubTagList, &pb.SubTagConfig{
				TagId: subTag.Tagid,
				Name:  subTag.Name,
			})
		}

		resp.TagList = append(resp.TagList, tmpInfo)
	}

	log.DebugWithCtx(ctx, "GetTagConfigInfoList end req:%v resp:%v", req, resp)
	return resp, nil
}
