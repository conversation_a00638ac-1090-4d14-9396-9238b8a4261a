package mysql

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm/clause"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/guild-cooperation"
	"golang.52tt.com/services/guild-cooperation/common"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

const (
	CreateTimeDesc       = "create_time desc"
	UidEqual             = "uid = ?"
	ApplicationIdEqual   = "application_id = ?"
	CooperationTypeEqual = "cooperation_type = ?"
	IdEqual              = "id = ?"
	GuildEqual           = "guild_id = ?"
	GuildIn              = "guild_id in (?)"
)

func NewMysql(cfg *config.MysqlConfig) (*Store, error) {
	var err error
	var db *gorm.DB
	if db, err = gorm.Open(mysql.New(mysql.Config{
		DSN:                       cfg.ConnectionString(),
		DefaultStringSize:         256,   // default size for string fields
		DisableDatetimePrecision:  true,  // disable datetime precision, which not supported before MySQL 5.6
		DontSupportRenameIndex:    true,  // drop & create when rename index, rename index not supported before MySQL 5.7, MariaDB
		DontSupportRenameColumn:   true,  // `change` when rename column, rename column not supported before MySQL 8, MariaDB
		SkipInitializeWithVersion: false, // autoconfigure based on currently MySQL version
	}), &gorm.Config{}); err != nil {
		return nil, err
	}

	db = db.Debug()

	return &Store{
		db: db,
	}, nil
}

type Store struct {
	db *gorm.DB
}

func (s *Store) Transaction(ctx context.Context, f func(tx *gorm.DB) error) error {
	tx := s.db.WithContext(ctx).Begin()
	err := f(tx)
	if err != nil {
		log.ErrorWithCtx(ctx, "Transaction fail err %v", err)
		_ = tx.Rollback()
		return err
	}
	return tx.Commit().Error
}

// CreateApplication 创建申请
func (s *Store) CreateApplication(ctx context.Context, app *CooperationApplication) error {
	if err := s.db.WithContext(ctx).Create(&app).Error; err != nil {
		log.ErrorWithCtx(ctx, "CreateApplication Create err %v", err)
		return err
	}
	return nil
}

// CreateApplications 创建申请
func (s *Store) CreateApplications(ctx context.Context, applications []*CooperationApplication) error {
	if err := s.db.WithContext(ctx).CreateInBatches(&applications, 10).Error; err != nil {
		log.ErrorWithCtx(ctx, "CreateApplications CreateInBatches err %v", err)
		return err
	}
	return nil
}

// GetApplication 获取申请
func (s *Store) GetApplication(ctx context.Context, applicationId string) (*CooperationApplication, error) {
	app := &CooperationApplication{}
	if err := s.db.WithContext(ctx).Where(ApplicationIdEqual, applicationId).
		First(&app).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetApplication First err %v", err)
		return nil, err
	}
	return app, nil
}

func (s *Store) GetLastApplicationByUid(ctx context.Context, uid uint32) (*CooperationApplication, error) {
	app := &CooperationApplication{}
	if err := s.db.WithContext(ctx).Where(UidEqual, uid).
		Order(CreateTimeDesc).
		First(&app).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return app, nil
		}
		log.ErrorWithCtx(ctx, "GetLastApplicationByUid First err %v", err)
		return nil, err
	}
	return app, nil
}

// GetLastApplication 获取最新一个申请
func (s *Store) GetLastApplication(ctx context.Context, uid uint32, ct pb.CooperationType) (*CooperationApplication, error) {
	app := new(CooperationApplication)
	if uid == 0 {
		return app, nil
	}
	q := s.db.WithContext(ctx).Where(UidEqual, uid).Order(CreateTimeDesc)
	if ct != pb.CooperationType_CTypeInvalid {
		q = q.Where(CooperationTypeEqual, ct)
	}
	if err := q.First(&app).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return app, nil
		}
		log.ErrorWithCtx(ctx, "First Find err %v", err)
		return nil, err
	}
	return app, nil
}

// GetLastApplicationByPhone 通过手机号获取最新一个申请
func (s *Store) GetLastApplicationByPhone(ctx context.Context, phone string, ct pb.CooperationType) (*CooperationApplication, error) {
	app := new(CooperationApplication)
	if phone == "" {
		return app, nil
	}
	q := s.db.WithContext(ctx).Where("phone = ?", phone).Order(CreateTimeDesc)
	if ct != pb.CooperationType_CTypeInvalid {
		q = q.Where(CooperationTypeEqual, ct)
	}
	if err := q.First(&app).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return app, nil
		}
		log.ErrorWithCtx(ctx, "First Find err %v", err)
		return nil, err
	}
	return app, nil
}

// GetApplicationList 获取申请列表
func (s *Store) GetApplicationList(ctx context.Context, req *pb.GetApplicationsReq, uid uint32) (
	[]*CooperationApplication, int64, error) {
	var total int64
	applications := make([]*CooperationApplication, 0)
	limit := req.GetLimit()
	if limit == 0 || limit > 100 {
		limit = 10
	}
	q := s.db.WithContext(ctx).Model(&CooperationApplication{})
	if req.GetReqApplyStatus() != pb.ReqApplyStatus_Invalid {
		step, approveStatus := common.ConvStep(req.GetReqApplyStatus())
		if step != pb.ApplicationStep_StepWaitSubmit {
			q = q.Where("step = ?", step)
		}
		q = q.Where("approve_status = ?", approveStatus)
	}
	if uid > 0 {
		q = q.Where(UidEqual, uid)
	}
	if req.GetWechat() != "" {
		q = q.Where("wechat_id like ?", req.GetWechat())
	}
	if req.GetCooperationType() != pb.CooperationType_CTypeInvalid {
		q = q.Where(CooperationTypeEqual, req.GetCooperationType())
	}
	if err := q.Count(&total).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetApplicationList Count err %v", err)
		return nil, 0, err
	}
	if err := q.
		Order(CreateTimeDesc).
		Offset(int(req.GetOffset())).
		Limit(int(limit)).
		Find(&applications).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetApplicationList Find err %v", err)
		return nil, total, err
	}
	return applications, total, nil
}

func (s *Store) SetApplicationStep(ctx context.Context, tx *gorm.DB, app *CooperationApplication,
	newStep pb.ApplicationStep, newOp pb.ApplicationOperation) error {
	q := s.db.WithContext(ctx)
	if tx != nil {
		q = tx
	}
	if app == nil || app.ApplicationID == "" {
		log.ErrorWithCtx(ctx, "SetApplicationStep app is nil")
		return nil
	}
	_, statusDesc := common.ConvReqStatus(newStep, newOp)
	updates := map[string]interface{}{
		"step":           newStep,
		"approve_status": newOp,
		"status_desc":    statusDesc,
	}
	if app.Operator != "" {
		updates["operator"] = app.Operator
	}
	if app.ApproveDesc != "" {
		updates["approve_desc"] = app.ApproveDesc
	}
	if newStep == pb.ApplicationStep_StepCooperation && newOp == pb.ApplicationOperation_OperationApproval {
		updates["finished_time"] = time.Now()
	}
	if newOp == pb.ApplicationOperation_OperationReject {
		updates["rejected_time"] = time.Now()
	}

	if err := q.Model(&CooperationApplication{}).
		Where(ApplicationIdEqual, app.ApplicationID).
		Updates(updates).Error; err != nil {
		log.ErrorWithCtx(ctx, "SetApplicationStep Update err %v", err)
		return err
	}
	return nil
}

func (s *Store) SetApplicationUid(ctx context.Context, applicationId string, uid uint32) error {
	q := s.db.WithContext(ctx)
	if applicationId == "" || uid == 0 {
		log.WarnWithCtx(ctx, "SetApplicationUid applicationId or uid is empty")
		return nil
	}
	updates := map[string]interface{}{
		"uid": uid,
	}
	if err := q.Model(&CooperationApplication{}).
		Where(ApplicationIdEqual, applicationId).
		Updates(updates).Error; err != nil {
		log.ErrorWithCtx(ctx, "SetApplicationUid Updates err %v", err)
		return err
	}
	return nil
}

func (s *Store) ApplicationCooperation(ctx context.Context, req *pb.ApplyCooperationReq) error {
	appId := req.GetApplicationId()
	newStep := pb.ApplicationStep_StepCooperation
	newOp := pb.ApplicationOperation_OperationInvalid
	q := s.db.WithContext(ctx)
	if appId == "" {
		log.ErrorWithCtx(ctx, "ApplicationCooperation app is nil")
		return nil
	}
	_, statusDesc := common.ConvReqStatus(newStep, newOp)
	updates := map[string]interface{}{
		"step":           newStep,
		"approve_status": newOp,
		"status_desc":    statusDesc,
		"guild_id":       req.GetGuildId(),
	}
	if err := q.Model(&CooperationApplication{}).
		Where(ApplicationIdEqual, appId).
		Updates(updates).Error; err != nil {
		log.ErrorWithCtx(ctx, "ApplicationCooperation Update err %v", err)
		return err
	}
	return nil
}

func (s *Store) ApproveApplication(ctx context.Context, app *CooperationApplication,
	newStep pb.ApplicationStep, newOp pb.ApplicationOperation, f func() error) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		if err := s.SetApplicationStep(ctx, tx, app, newStep, newOp); err != nil {
			log.ErrorWithCtx(ctx, "ApproveApplication SetApplicationStep err %v", err)
			return err
		}

		var lastStep pb.ApplicationStep
		var lastOp pb.ApplicationOperation
		lastStep = app.Step // 记录操作前的步骤
		if newOp == pb.ApplicationOperation_OperationReject {
			lastOp = pb.ApplicationOperation_OperationReject
		} else {
			lastOp = pb.ApplicationOperation_OperationApproval
		}
		if err := s.CreateOperationHistory(ctx, tx, app, lastStep, lastOp); err != nil {
			log.ErrorWithCtx(ctx, "ApproveApplication SetApplicationStep err %v", err)
			return err
		}
		if f != nil {
			if err := f(); err != nil {
				log.ErrorWithCtx(ctx, "ApproveApplication f() err %v", err)
				return err
			}
		}
		return nil
	})
}

// CreateOperationHistory 创建操作记录
func (s *Store) CreateOperationHistory(ctx context.Context, tx *gorm.DB, app *CooperationApplication,
	newStep pb.ApplicationStep, newOp pb.ApplicationOperation) error {
	q := s.db.WithContext(ctx)
	if tx != nil {
		q = tx
	}
	history := &CooperationApplicationHistory{
		ApplicationID:   app.ApplicationID,
		UID:             app.UID,
		Operator:        app.Operator,
		CooperationType: app.CooperationType,
		Step:            newStep,
		ApproveStatus:   newOp,
		OperationDesc:   common.CreateOperationDesc(newStep, newOp),
	}
	if err := q.Create(&history).Error; err != nil {
		log.ErrorWithCtx(ctx, "CreateOperationHistory Create err %v", err)
		return err
	}
	return nil
}

// GetOperationHistoryList 获取申请操作历史列表
func (s *Store) GetOperationHistoryList(ctx context.Context, req *pb.GetOperationHistoryReq, uid uint32) (
	[]*CooperationApplicationHistory, int64, error) {
	var total int64
	applications := make([]*CooperationApplicationHistory, 0)
	limit := req.GetLimit()
	if limit == 0 || limit > 100 {
		limit = 10
	}
	q := s.db.WithContext(ctx).Model(&CooperationApplicationHistory{})
	if uid > 0 {
		q = q.Where(UidEqual, uid)
	}
	if req.GetOperator() != "" {
		q = q.Where("operator = ?", req.GetOperator())
	}
	if err := q.Count(&total).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetOperationHistoryList Count err %v", err)
		return nil, 0, err
	}
	if err := q.
		Order(CreateTimeDesc).
		Offset(int(req.GetOffset())).
		Limit(int(limit)).
		Find(&applications).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetOperationHistoryList Find err %v", err)
		return nil, total, err
	}
	return applications, total, nil
}

func (s *Store) CreateOrEditNotification(ctx context.Context, req *pb.EditNotificationReq) error {
	notification := &ApplyNotification{
		ID:       req.GetNotificationId(),
		Title:    req.GetTitle(),
		Content:  req.GetContent(),
		Operator: req.GetOperator(),
		SmsUsage: req.GetUsage(),
		MsgType:  uint32(req.GetMsgType()),
	}
	if notification.ID == 0 {
		if err := s.db.WithContext(ctx).Create(&notification).Error; err != nil {
			log.ErrorWithCtx(ctx, "CreateOrEditNotification Create err %v", err)
			return err
		}
	} else {
		if err := s.db.WithContext(ctx).
			Where(IdEqual, req.GetNotificationId()).
			Updates(&notification).Error; err != nil {
			log.ErrorWithCtx(ctx, "CreateOrEditNotification Updates err %v", err)
			return err
		}
	}
	return nil
}

// GetNotification 获取通知
func (s *Store) GetNotification(ctx context.Context, id uint32) (*ApplyNotification, error) {
	notification := &ApplyNotification{}
	if err := s.db.WithContext(ctx).Where(IdEqual, id).
		First(&notification).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetNotification First err %v", err)
		return nil, err
	}
	return notification, nil
}

// GetNotificationList 获取通知列表
func (s *Store) GetNotificationList(ctx context.Context, msgType pb.MsgType) ([]*ApplyNotification, error) {
	notifications := make([]*ApplyNotification, 0)
	limit := 100
	db := s.db.WithContext(ctx)
	if msgType != 0 {
		db = db.Where("msg_type = ?", msgType)
	}
	if err := db.Limit(limit).
		Order("id desc").
		Find(&notifications).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return notifications, nil
		}
		log.ErrorWithCtx(ctx, "GetNotification Find err %v", err)
		return nil, err
	}
	return notifications, nil
}

// DeleteNotification 删除通知
func (s *Store) DeleteNotification(ctx context.Context, id uint32) error {
	notification := &ApplyNotification{}
	if err := s.db.WithContext(ctx).Where(IdEqual, id).
		Delete(&notification).Error; err != nil {
		log.ErrorWithCtx(ctx, "DeleteNotification Delete err %v", err)
		return err
	}
	return nil
}

func (s *Store) CreateApplyLimit(ctx context.Context, req *pb.CreateApplyLimitReq, uid uint32) error {
	item := &ApplyLimit{
		UID:       uid,
		StartTime: time.Unix(int64(req.GetStartTime()), 0),
		EndTime:   time.Unix(int64(req.GetEndTime()), 0),
		Operator:  req.GetOperator(),
	}
	if req.GetId() == 0 {
		if err := s.db.WithContext(ctx).Create(&item).Error; err != nil {
			log.ErrorWithCtx(ctx, "CreateApplyLimit Create err %v", err)
			return err
		}
	} else {
		if err := s.db.WithContext(ctx).
			Where(IdEqual, req.GetId()).
			Updates(&item).Error; err != nil {
			log.ErrorWithCtx(ctx, "CreateApplyLimit Updates err %v", err)
			return err
		}
	}

	return nil
}

// GetApplyLimitList 获取通知列表
func (s *Store) GetApplyLimitList(ctx context.Context, req *pb.GetApplyLimitListReq, uid uint32) ([]*ApplyLimit, int64, error) {
	applyLimits := make([]*ApplyLimit, 0)
	var count int64
	q := s.db.WithContext(ctx).Model(&ApplyLimit{})
	if uid > 0 {
		q = q.Where(UidEqual, uid)
	}
	if err := q.Count(&count).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetApplyLimitList Count err %v", err)
		return nil, count, err
	}
	if err := q.
		Offset(int(req.GetOffset())).
		Limit(int(req.GetLimit())).
		Order("id desc").
		Find(&applyLimits).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return applyLimits, count, nil
		}
		log.ErrorWithCtx(ctx, "GetApplyLimitList Find err %v", err)
		return nil, count, err
	}
	return applyLimits, count, nil
}

// DeleteApplyLimit 删除限制
func (s *Store) DeleteApplyLimit(ctx context.Context, uid uint32) error {
	applyLimit := &ApplyLimit{}
	if err := s.db.WithContext(ctx).Model(&ApplyLimit{}).Where(UidEqual, uid).
		Delete(&applyLimit).Error; err != nil {
		log.ErrorWithCtx(ctx, "DeleteApplyLimit Delete err %v", err)
		return err
	}
	return nil
}

// GetLastApplyLimit 获取最近的申请限制
func (s *Store) GetLastApplyLimit(ctx context.Context, uid uint32, t time.Time) (*ApplyLimit, error) {
	applyLimit := &ApplyLimit{}
	if err := s.db.WithContext(ctx).Model(&ApplyLimit{}).
		Where(UidEqual, uid).
		Order("start_time asc").
		First(&applyLimit).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return applyLimit, nil
		}
		log.ErrorWithCtx(ctx, "DeleteApplyLimit First err %v", err)
		return nil, err
	}
	return applyLimit, nil
}

// GetApplyLimitByUid 获取申请限制
func (s *Store) GetApplyLimitByUid(ctx context.Context, uid uint32) (*ApplyLimit, error) {
	applyLimit := &ApplyLimit{}
	if err := s.db.WithContext(ctx).Model(&ApplyLimit{}).
		Where(UidEqual, uid).
		First(&applyLimit).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return applyLimit, nil
		}
		log.ErrorWithCtx(ctx, "GetApplyLimitByUid First err %v", err)
		return nil, err
	}
	return applyLimit, nil
}

// JoinGuildCooperation 将公会加入指定合作库
func (s *Store) JoinGuildCooperation(ctx context.Context, info *GuildCooperationInfo) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// 插入公会合作库数据
		if err := s.AddGuildCooperation(ctx, tx, info); err != nil {
			log.ErrorWithCtx(ctx, "JoinGuildCooperation AddGuildCooperation failed, info:[%+v], err:[%+v]", info, err)
			return err
		}

		// 组装流水数据
		history := &GuildCooperationHistory{
			GuildId:         info.GuildId,
			CooperationType: info.CooperationType,
			OptType:         uint32(pb.CoopOptType_Add),
			Operator:        info.Operator,
			OptTime:         time.Now(),
			UpdateTime:      time.Now(),
		}
		// 插入公会合作库操作流水
		if err := s.AddGuildCooperationHistory(ctx, tx, history); err != nil {
			log.ErrorWithCtx(ctx, "JoinGuildCooperation AddGuildCooperationHistory failed, history:[%+v], err:[%+v]", history, err)
			return err
		}
		return nil
	})
}

// RemoveGuildCooperation 将公会移除指定合作库
func (s *Store) RemoveGuildCooperation(ctx context.Context, guildId uint32, cooperationType pb.CooperationType, operator string) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// 插入公会合作库数据
		if err := s.DelGuildCooperation(ctx, tx, guildId, cooperationType); err != nil {
			log.ErrorWithCtx(ctx, "RemoveGuildCooperation RemoveGuildCooperation failed, guildId:[%+v], cooperationType:[%+v], err:[%+v]",
				guildId, cooperationType, err)
			return err
		}

		// 组装流水数据
		history := &GuildCooperationHistory{
			GuildId:         guildId,
			CooperationType: uint32(cooperationType),
			OptType:         uint32(pb.CoopOptType_Del),
			Operator:        operator,
			OptTime:         time.Now(),
			UpdateTime:      time.Now(),
		}
		// 插入公会合作库操作流水
		if err := s.AddGuildCooperationHistory(ctx, tx, history); err != nil {
			log.ErrorWithCtx(ctx, "RemoveGuildCooperation AddGuildCooperationHistory failed, history:[%+v], err:[%+v]", history, err)
			return err
		}
		return nil
	})
}

// AddGuildCooperation 插入公会合作库数据
func (s *Store) AddGuildCooperation(ctx context.Context, tx *gorm.DB, info *GuildCooperationInfo) error {
	q := s.db.WithContext(ctx)
	if tx != nil {
		q = tx
	}
	if err := q.Model(&GuildCooperationInfo{}).Create(&info).Error; err != nil {
		log.ErrorWithCtx(ctx, "AddGuildCooperation Create failed, info:[%+v], err:[%+v]", info, err)
		return err
	}
	return nil
}

// DelGuildCooperation 删除公会合作库数据
func (s *Store) DelGuildCooperation(ctx context.Context, tx *gorm.DB, guildId uint32, cooperationType pb.CooperationType) error {
	q := s.db.WithContext(ctx)
	if tx != nil {
		q = tx
	}
	guildCooperationInfo := &GuildCooperationInfo{}
	if err := q.Model(guildCooperationInfo).
		Where(GuildEqual, guildId).
		Where(CooperationTypeEqual, cooperationType).
		Delete(&guildCooperationInfo).Error; err != nil {
		log.ErrorWithCtx(ctx, "RemoveGuildCooperation Delete failed, guildId:[%+v], CooperationType:[%+v], err:[%+v]",
			guildId, cooperationType, err)
		return err
	}
	return nil
}

// BatchGetGuildCoopInfos 批量获取公会合作信息
func (s *Store) BatchGetGuildCoopInfos(ctx context.Context, limit, offset int, guildIds []uint32, operator string, cooperationType pb.CooperationType) (
	[]*GuildCooperationInfo, int64, error) {
	db := s.db.WithContext(ctx).Model(&GuildCooperationInfo{})
	// 判断是否需要查询
	if len(guildIds) != 0 && operator != "" {
		db = db.Where("guild_id in (?) OR operator like ?", guildIds, "%"+operator+"%")
	} else if len(guildIds) != 0 {
		db = db.Where(GuildIn, guildIds)
	} else if operator != "" {
		db = db.Where("operator like ?", "%"+operator+"%")
	} else {
		log.DebugWithCtx(ctx, "BatchGetGuildCoopInfos len(guildIds) and operator both null, guildIds:[%+v], operator:[%+v]", guildIds, operator)
	}
	if cooperationType != 0 {
		db = db.Where(CooperationTypeEqual, cooperationType)
	}

	result := make([]*GuildCooperationInfo, 0)
	// 查询数量
	var count int64
	if err := db.Count(&count).Error; err != nil {
		log.ErrorWithCtx(ctx, "BatchGetGuildCoopInfos Get Count failed, guildIds:[%+v], operator:[%+v], cooperationType:[%+v], err:[%+v]",
			guildIds, operator, cooperationType, err)
		return result, count, err
	}
	if count == 0 {
		log.DebugWithCtx(ctx, "BatchGetGuildCoopInfos is null... ")
		return result, count, nil
	}

	// 查询结果
	if err := db.Order(CreateTimeDesc).Offset(offset).Limit(limit).Find(&result).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return result, count, nil
		}
		log.ErrorWithCtx(ctx, "BatchGetGuildCoopInfos Get info failed, limit:[%+v], offset:[%+v], guildIds:[%+v],"+
			" operator:[%+v], cooperationType:[%+v], err:[%+v]", limit, offset, guildIds, operator, cooperationType, err)
		return result, count, err
	}

	log.DebugWithCtx(ctx, "BatchGetGuildCoopInfos result:[%+v]", result)
	return result, count, nil
}

// BatchGetALLGuildList 批量获取指定合作类型的所有公会ID列表
func (s *Store) BatchGetALLGuildList(ctx context.Context, cooperationType pb.CooperationType, guildIds []uint32, limit, offset int) (
	[]uint32, int64, error) {
	infos := make([]*GuildCooperationInfo, 0)
	db := s.db.WithContext(ctx).Model(&GuildCooperationInfo{}).
		Where(CooperationTypeEqual, cooperationType)
	if len(guildIds) > 0 {
		db = db.Where(GuildIn, guildIds)
	}

	// 查询数量
	result := make([]uint32, 0)
	var count int64
	if err := db.Count(&count).Error; err != nil {
		log.ErrorWithCtx(ctx, "BatchGetALLGuildListWithCoopType Get Count failed, cooperationType:[%+v], err:[%+v]",
			cooperationType, err)
		return result, count, err
	}
	if count == 0 {
		log.DebugWithCtx(ctx, "BatchGetALLGuildListWithCoopType is null... ")
		return result, count, nil
	}

	// 如果分页参数不为0，则进行分页
	if limit != 0 {
		db = db.Order("guild_id ASC").Offset(offset).Limit(limit)
	}
	if err := db.Find(&infos).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return result, 0, nil
		}
		log.ErrorWithCtx(ctx, "BatchGetALLGuildListWithCoopType failed, cooperationType:[%+v], limit:[%+v], offset:[%+v], err:[%+v]",
			cooperationType, err)
		return result, 0, err
	}
	for _, info := range infos {
		result = append(result, info.GuildId)
	}
	return result, count, nil
}

// GetCooperationWithGuild 获取指定公会的合作关系
func (s *Store) GetCooperationWithGuild(ctx context.Context, guildId uint32) ([]*GuildCooperationInfo, error) {
	infos := make([]*GuildCooperationInfo, 0)
	if err := s.db.WithContext(ctx).Model(&GuildCooperationInfo{}).
		Where(GuildEqual, guildId).
		Find(&infos).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return infos, nil
		}
		log.ErrorWithCtx(ctx, "GetCooperationWithGuild failed, guildId:[%+v], err:[%+v]", guildId, err)
		return nil, err
	}
	return infos, nil
}

// GetGuildCooperationInfo 获取指定公会合作库信息
func (s *Store) GetGuildCooperationInfo(ctx context.Context, guildId uint32, cooperationType pb.CooperationType) (
	*GuildCooperationInfo, error) {
	info := &GuildCooperationInfo{}
	if err := s.db.WithContext(ctx).Model(&GuildCooperationInfo{}).
		Where(GuildEqual, guildId).
		Where(CooperationTypeEqual, cooperationType).
		First(&info).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.DebugWithCtx(ctx, "GetGuildCooperationInfo ErrRecordNotFound.")
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetGuildCooperationInfo failed, guildId:[%+v], cooperationType:[%+v], err:[%+v]",
			guildId, cooperationType, err)
		return nil, err
	}
	return info, nil
}

// AddGuildCooperationHistory 插入公会合作库操作流水
func (s *Store) AddGuildCooperationHistory(ctx context.Context, tx *gorm.DB, history *GuildCooperationHistory) error {
	q := s.db.WithContext(ctx)
	if tx != nil {
		q = tx
	}
	if err := q.Model(&GuildCooperationHistory{}).Create(&history).Error; err != nil {
		log.ErrorWithCtx(ctx, "AddGuildCooperationHistory Create failed, history:[%+v], err:[%+v]", history, err)
		return err
	}
	return nil
}

// BatchGetGuildCoopHistory 批量获取公会合作历史信息
func (s *Store) BatchGetGuildCoopHistory(ctx context.Context, limit, offset int, guildIdList []uint32, cooperationType pb.CooperationType) (
	[]*GuildCooperationHistory, int64, error) {
	db := s.db.WithContext(ctx).Model(&GuildCooperationHistory{})
	// 判断是否需要查询
	if len(guildIdList) != 0 {
		db = db.Where(GuildIn, guildIdList)
	}
	if cooperationType != 0 {
		db = db.Where(CooperationTypeEqual, cooperationType)
	}

	result := make([]*GuildCooperationHistory, 0)
	// 查询数量
	var count int64
	if err := db.Count(&count).Error; err != nil {
		log.ErrorWithCtx(ctx, "BatchGetGuildCoopHistory Get Count failed, guildIdList:[%+v], err:[%+v]", guildIdList, err)
		return result, count, err
	}
	if count == 0 {
		log.DebugWithCtx(ctx, "BatchGetGuildCoopHistory is null... ")
		return result, count, nil
	}

	// 查询结果
	if err := db.Order("opt_time desc").Offset(offset).Limit(limit).Find(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return result, count, nil
		}
		log.ErrorWithCtx(ctx, "BatchGetGuildCoopHistory Get info failed, limit:[%+v], offset:[%+v], guildIdList:[%+v],"+
			" err:[%+v]", limit, offset, guildIdList, err)
		return result, count, err
	}

	log.DebugWithCtx(ctx, "BatchGetGuildCoopHistory result:[%+v]", result)
	return result, count, nil
}

// BatchGetCoopHistoryWithGuild 批量获取指定公会合作类型的历史操作记录
func (s *Store) BatchGetCoopHistoryWithGuild(ctx context.Context, guildId uint32, cooperationType pb.CooperationType) ([]*GuildCooperationHistory, error) {
	result := make([]*GuildCooperationHistory, 0)
	sql := s.db.WithContext(ctx).Model(&GuildCooperationHistory{}).
		Where(GuildEqual, guildId)
	if cooperationType != pb.CooperationType_CTypeInvalid {
		sql = sql.Where(CooperationTypeEqual, cooperationType)
	}
	if err := sql.Order("opt_time desc").Find(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.DebugWithCtx(ctx, "BatchGetCoopHistoryWithGuild ErrRecordNotFound.")
			return result, nil
		}
		log.ErrorWithCtx(ctx, "BatchGetCoopHistoryWithGuild failed, guildId:[%+v], cooperationType:[%+v], err:[%+v]",
			guildId, cooperationType, err)
		return nil, err
	}
	return result, nil
}

// GetAllNoBonusGuild 获取所有无佣金公会信息
func (s *Store) GetAllNoBonusGuild(ctx context.Context) ([]*GuildNoBonus, error) {
	result := make([]*GuildNoBonus, 0)
	if err := s.db.WithContext(ctx).Model(&GuildNoBonus{}).Find(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.DebugWithCtx(ctx, "GetAllNoBonusGuild ErrRecordNotFound.")
			return result, nil
		}
		log.ErrorWithCtx(ctx, "GetAllNoBonusGuild failed, err:[%+v]", err)
		return result, err
	}
	return result, nil
}

// SetNoBonusGuild 设置无佣金公会信息
func (s *Store) SetNoBonusGuild(ctx context.Context, guildId uint32, remark string) error {
	guildNoBonus := &GuildNoBonus{
		GuildId: guildId,
		Remark:  remark,
	}
	if err := s.db.WithContext(ctx).Clauses(clause.OnConflict{ // 当已存在对应信息时，执行更新操作
		DoUpdates: clause.Assignments(map[string]interface{}{
			"remark": remark,
		}),
	}).Model(&GuildNoBonus{}).Create(guildNoBonus).Error; err != nil {
		log.ErrorWithCtx(ctx, "SetNoBonusGuild failed, guildId:[%+v], remark:[%+v], err:%s", guildId, remark, err)
		return err
	}
	return nil
}
