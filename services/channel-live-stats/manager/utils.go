package manager

import (
	"encoding/json"
	"errors"
	"fmt"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channel-live-stats"
	"io/ioutil"
	"net/http"
	"time"
)

const (
	ActiveFans    = "AF"
	SendPresent   = "SP"
	SendPresentV2 = SendPresent + "v2"
	Follow        = "FOLLOW"
	Audience      = "AUD"
	NewFans       = "NewFans"

	Daily   = "daily"
	WeekLy  = "weekly"
	MonthLy = "monthly"

	OneMondayTs = 1601827200 // 2020-10-05 00:00:00 星期一
	WeekSeconds = 7 * 24 * 3600

	RunningChPoorCnt = 10
	LiveTsProcLock   = "live_ts_lock"
)

const (
	//公会营收
	WeekGuildIncomeHtml1  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'> 周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>营收%s，%s；公会旗下所有签约达人中，%s收礼层级达人<span style='font-weight:bold'> %s </span>最明显，%s<span style='font-weight:bold'> %s</span></span></span></div></div></div>"
	WeekGuildIncomeHtml2  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'> 周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>其中新签达人营收%s，对比上周期%s，老达人营收%s，对比上周期%s；公会旗下所有签约达人中，%s收礼层级达人<span style='font-weight:bold'> %s </span>最明显，%s<span style='font-weight:bold'> %s</span></span></span></div></div></div>"
	WeekGuildIncomeHtml3  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'> 周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人营收均等额%s，各%s；公会旗下所有签约达人中，%s收礼层级达人<span style='font-weight:bold'> %s </span>最明显，%s<span style='font-weight:bold'> %s</span></span></span></div></div></div>"
	WeekGuildIncomeHtml4  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'> 周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>营收%s，%s；但公会旗下所有签约达人中，各收礼层级均%s</span></span></div></div></div>"
	WeekGuildIncomeHtml5  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'> 周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人营收均等额%s，各%s；但公会旗下所有签约达人中，各收礼层级均%s</span></span></div></div></div>"
	WeekGuildIncomeHtml6  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'> 周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>营收%s，%s；但公会旗下所有签约达人中，各收礼层级均与上周期持平，未有变化</span></span></div></div></div>"
	WeekGuildIncomeHtml7  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'> 周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人营收均等额%s，各%s；但公会旗下所有签约达人中，各收礼层级均与上周期持平，未有变化</span></span></div></div></div>"
	WeekGuildIncomeHtml8  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'> 周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>营收%s，%s；但公会旗下所有签约达人中，各收礼层级均无%s，其中%s收礼层级达人<span style='font-weight:bold'> %s </span>最明显，%s<span style='font-weight:bold'> %s</span></span></span></div></div></div>"
	WeekGuildIncomeHtml9  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'> 周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>其中新签达人营收%s，对比上周期%s，老达人营收%s，对比上周期%s；公会旗下所有签约达人中，各收礼层级均与上周期持平，未有变化</span></span></div></div></div>"
	WeekGuildIncomeHtml10 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'> 周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人营收均等额%s，各%s；但公会旗下所有签约达人中，各收礼层级均无%s，其中%s收礼层级达人<span style='font-weight:bold'> %s </span>最明显，%s<span style='font-weight:bold'> %s</span></span></span></div></div></div>"

	//新签达人数
	WeekNewSignAnchorHtml1 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>新签达人数</p><div><span style='color:#FF5674'> 周期新签达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人数</span><span style='font-weight:500'> %s人，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期新签达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>有开启听听的新签达人数%s人，听听率%s，对比上周期听听率%s</span><span style='font-weight:bold'> %s</span></div></div></div>"

	//开启听听达人数
	WeekLiveAnchorHtml1 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>开启听听达人数</p><div><span style='color:#FF5674'>周期开启听听达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>开启听听达人数</span><span style='font-weight:500'> %s人，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平，其中周开启听听活跃天≥5天的人数有%s人，占比%s</span><div><span style='color:#FF5674'>周期开启听听达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>开启听听人数%s， %s%s</span></div></div></div>"
	WeekLiveAnchorHtml2 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>开启听听达人数</p><div><span style='color:#FF5674'>周期开启听听达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>开启听听达人数</span><span style='font-weight:500'> %s人，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平，其中周开启听听活跃天≥5天的人数有%s人，占比%s</span><div><span style='color:#FF5674'>周期开启听听达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>其中开启听听新签达人%s人，对比上周期%s%s；开启听听老达人%s人，对比上周期%s%s</span></div></div></div>"
	WeekLiveAnchorHtml3 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>开启听听达人数</p><div><span style='color:#FF5674'>周期开启听听达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>开启听听达人数</span><span style='font-weight:500'> %s人，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平，其中周开启听听活跃天≥5天的人数有%s人，占比%s</span><div><span style='color:#FF5674'>周期开启听听达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人开启听听人数均等额%s，各%s</span></div></div></div>"

	//收礼≥2万豆达人数
	WeekTwoWanAnchorHtml1 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>收礼≥2万豆达人数</p><div><span style='color:#FF5674'>周期收礼≥2万豆达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>收礼≥2万豆达人数</span><span style='font-weight:500'> %s人，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期收礼≥2万豆达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>周收礼≥2万豆达人人数%s， %s%s</span></div></div></div>"
	WeekTwoWanAnchorHtml2 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>收礼≥2万豆达人数</p><div><span style='color:#FF5674'>周期收礼≥2万豆达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>收礼≥2万豆达人数</span><span style='font-weight:500'> %s人，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期收礼≥2万豆达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>周收礼≥2万豆的新签达人%s人，对比上周期%s%s；老达人%s人，对比上周期%s%s</span></div></div></div>"
	WeekTwoWanAnchorHtml3 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>收礼≥2万豆达人数</p><div><span style='color:#FF5674'>周期收礼≥2万豆达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>收礼≥2万豆达人数</span><span style='font-weight:500'> %s人，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期收礼≥2万豆达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人周收礼≥2万豆达人人数均等额%s，各%s</span></div></div></div>"

	//收礼≥10万豆达人数
	WeekTenWanAnchorHtml1 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>收礼≥10万豆达人数</p><div><span style='color:#FF5674'>周期收礼≥10万豆达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>收礼≥10万豆达人数</span><span style='font-weight:500'> %s人，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期收礼≥10万豆达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>周收礼≥10万豆达人人数%s， %s%s</span></div></div></div>"
	WeekTenWanAnchorHtml2 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>收礼≥10万豆达人数</p><div><span style='color:#FF5674'>周期收礼≥10万豆达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>收礼≥10万豆达人数</span><span style='font-weight:500'> %s人，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期收礼≥10万豆达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>收礼≥10万豆的新签达人%s人，对比上周期%s%s；老达人%s人，对比上周期%s%s</span></div></div></div>"
	WeekTenWanAnchorHtml3 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>收礼≥10万豆达人数</p><div><span style='color:#FF5674'>周期收礼≥10万豆达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>收礼≥10万豆达人数</span><span style='font-weight:500'> %s人，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期收礼≥10万豆达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人周收礼≥10万豆达人人数均等额%s，各%s</span></div></div></div>"
)

const (
	//公会营收
	MonthGuildIncomeHtml1  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'>周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>营收%s，%s；公会旗下所有签约达人中，%s收礼层级达人<span style='font-weight:bold'> %s </span>最明显，%s<span style='font-weight:bold'> %s</span></span></span></div></div></div>"
	MonthGuildIncomeHtml2  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'>周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>其中新签达人营收%s，对比上周期%s，老达人营收%s，对比上周期%s；公会旗下所有签约达人中，%s收礼层级达人<span style='font-weight:bold'> %s </span>最明显，%s<span style='font-weight:bold'> %s</span></span></span></div></div></div>"
	MonthGuildIncomeHtml3  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'>周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人营收均等额%s，各%s；公会旗下所有签约达人中，%s收礼层级达人<span style='font-weight:bold'> %s </span>最明显，%s<span style='font-weight:bold'> %s</span></span></span></div></div></div>"
	MonthGuildIncomeHtml4  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'>周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>营收%s，%s；但公会旗下所有签约达人中，各收礼层级均%s</span></span></div></div></div>"
	MonthGuildIncomeHtml5  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'>周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人营收均等额%s，各%s；但公会旗下所有签约达人中，各收礼层级均%s</span></span></div></div></div>"
	MonthGuildIncomeHtml6  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'>周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>营收%s，%s；但公会旗下所有签约达人中，各收礼层级均与上周期持平，未有变化</span></span></div></div></div>"
	MonthGuildIncomeHtml7  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'>周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人营收均等额%s，各%s；但公会旗下所有签约达人中，各收礼层级均与上周期持平，未有变化</span></span></div></div></div>"
	MonthGuildIncomeHtml8  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'>周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>营收%s，%s；但公会旗下所有签约达人中，各收礼层级均无%s，其中%s收礼层级达人<span style='font-weight:bold'> %s </span>最明显，%s<span style='font-weight:bold'> %s</span></span></span></div></div></div>"
	MonthGuildIncomeHtml9  = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'>周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人营收均等额%s，各%s；但公会旗下所有签约达人中，各收礼层级均无%s，其中%s收礼层级达人<span style='font-weight:bold'> %s </span>最明显，%s<span style='font-weight:bold'> %s</span></span></span></div></div></div>"
	MonthGuildIncomeHtml10 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>公会营收</p><div><span style='color:#FF5674'>周期公会营收表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>公会营收</span><span style='font-weight:500'> %s，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期公会营收</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>其中新签达人营收%s，对比上周期%s，老达人营收%s，对比上周期%s；公会旗下所有签约达人中，各收礼层级均与上周期持平，未有变化</span></span></div></div></div>"

	//新签达人数
	MonthNewSignAnchorHtml1 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>新签达人数</p><div><span style='color:#FF5674'>周期新签达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人数</span><span style='font-weight:500'> %s人，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期新签达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>有开启听听的新签达人数%s人，听听率%s，对比上周期听听率%s</span><span style='font-weight:bold'> %s</span></div></div></div>"

	//开启听听达人数
	MonthLiveAnchorHtml1 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>开启听听达人数</p><div><span style='color:#FF5674'>周期开启听听达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>开启听听达人数</span><span style='font-weight:500'> %s人，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平，月听听活跃天数≥10天的人数有%s人，占比%s， 听听活跃天数≥20天的人数有%s人，占比%s</span><div><span style='color:#FF5674'>周期开启听听达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>开启听听人数%s， %s%s</span></div></div></div>"
	MonthLiveAnchorHtml2 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>开启听听达人数</p><div><span style='color:#FF5674'>周期开启听听达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>开启听听达人数</span><span style='font-weight:500'> %s人，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平，月听听活跃天数≥10天的人数有%s人，占比%s， 听听活跃天数≥20天的人数有%s人，占比%s</span><div><span style='color:#FF5674'>周期开启听听达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>其中开启听听新签达人%s人，对比上周期%s%s；开启听听老达人%s人，对比上周期%s%s</span></div></div></div>"
	MonthLiveAnchorHtml3 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>开启听听达人数</p><div><span style='color:#FF5674'>周期开启听听达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>开启听听达人数</span><span style='font-weight:500'> %s人，</span><span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平，月听听活跃天数≥10天的人数有%s人，占比%s， 听听活跃天数≥20天的人数有%s人，占比%s</span><div><span style='color:#FF5674'>周期开启听听达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人开启听听人数均等额%s ，各%s</span></div></div></div>"

	//专业从业者数
	MonthProAnchorHtml1 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>专业从业者数</p><div><span style='color:#FF5674'>周期专业从业者人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期专业从业者人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>专业从业者人数%s，%s；本月新签达人专业从业者%s人，老达人专业从业者%s人；上月为专业从业者的达人中在本月保持的有%s人，次月维持率%s</span></div></div></div>"
	MonthProAnchorHtml2 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>专业从业者数</p><div><span style='color:#FF5674'>周期专业从业者人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期专业从业者人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人专业从业者%s人，对比上周期%s%s；老达人的有%s人，对比上周期%s%s；上月为专业从业者的达人中在本月保持的有%s人，次月维持率%s</span></div></div></div>"
	MonthProAnchorHtml3 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>专业从业者数</p><div><span style='color:#FF5674'>周期专业从业者人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期专业从业者人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人专业从业者人数均等额%s，各%s；本月新签达人专业从业者%s人，老达人专业从业者%s人；上月为专业从业者的达人中在本月保持的有%s人，次月维持率%s</span></div></div></div>"

	//成熟达人数
	MonthMatureAnchorHtml1 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>成熟达人数</p><div><span style='color:#FF5674'>周期成熟达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期成熟达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>成熟达人人数%s，%s；本月新签达人中成熟达人%s人，老达人中成熟达人%s人；上月为成熟达人的达人中在本月保持的有%s人，次月维持率%s</span></div></div></div>"
	MonthMatureAnchorHtml2 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>成熟达人数</p><div><span style='color:#FF5674'>周期成熟达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期成熟达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人中成熟达人%s人，对比上周期%s%s；老达人中成熟达人有%s人，对比上周期%s%s；上月为成熟达人的达人中在本月保持的有%s人，次月维持率%s</span></div></div></div>"
	MonthMatureAnchorHtml3 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>成熟达人数</p><div><span style='color:#FF5674'>周期成熟达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期成熟达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人的成熟达人人数均等额%s，各%s；本月新签达人中成熟达人%s人，老达人中成熟达人%s人；上月为成熟达人的达人中在本月保持的有%s人，次月维持率%s</span></div></div></div>"

	//潜力活跃达人数
	MonthPotActiveAnchorHtml1 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>潜力活跃达人数</p><div><span style='color:#FF5674'>周期潜力活跃达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期潜力活跃达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>主因</span><span style='font-weight:bold'> %s </span><span>潜力活跃达人人数%s，%s；本月新签达人中潜力活跃达人%s人，老达人中潜力活跃达人%s人；上月为潜力活跃达人的达人中在本月保持的有%s人，次月维持率%s</span></div></div></div>"
	MonthPotActiveAnchorHtml2 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>潜力活跃达人数</p><div><span style='color:#FF5674'>周期潜力活跃达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期潜力活跃达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人中潜力活跃达人%s人，对比上周期%s%s；老达人中潜力活跃达人有%s人，对比上周期%s%s；上月为潜力活跃达人的达人中在本月保持的有%s人，次月维持率%s</span></div></div></div>"
	MonthPotActiveAnchorHtml3 = "<div><p style='font-size: 18px;font-weight: 600;color:#616465'>潜力活跃达人数</p><div><span style='color:#FF5674'>周期潜力活跃达人人数表现</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>对比同层级公会处于<span style='color:#FF5674;font-weight:bold'> %s </span>水平</span><div><span style='color:#FF5674'>周期潜力活跃达人人数</span><span style='color:#FF5674;font-weight:bold'> %s</span>，<span>新签达人及老达人的潜力活跃达人人数均等额%s，各%s；本月新签达人中潜力活跃达人%s人，老达人中潜力活跃达人%s人；上月为潜力活跃达人的达人中在本月保持的有%s人，次月维持率%s</span></div></div></div>"
)

const (
	NewAnchor = 1
	OldAnchor = 2
)

const (
	ManifestationExcellent = "较佳"
	ManifestationGood      = "良好"
	ManifestationNormal    = "一般"
	ManifestationBad       = "欠佳"

	LevelHigh = "高"
	LevelMid  = "中"
	LevelLow  = "低"

	ChangeUp          = "增加"
	ChangeDown        = "减少"
	ChangeFluctuation = "波动"
	ChangeNo          = "持平"

	MonthDateTim = "month"
	WeekDateTim  = "week"
)

type Pagination struct {
	Total     uint32 `json:"total"`
	TotalPage uint32 `json:"totalPage"`
	PageSize  uint32 `json:"pageSize"`
	Page      uint32 `json:"page"`
}
type SubData struct {
	Revenue                    string `json:"revenue"`
	MatureAnchorCnt            string `json:"mature_anchor_cnt"`
	NewSignAnchorCnt           string `json:"new_sign_anchor_cnt"`
	DateDim                    string `json:"date_dim"`
	GuildId                    uint32 `json:"guild_id"`
	NewActiveAnchorCnt         string `json:"new_active_anchor_cnt"`
	PotActiveAnchorCnt         string `json:"pot_active_anchor_cnt"`
	IncomeUp10wUserCnt         string `json:"income_up10w_user_cnt"`
	ProAnchorCnt               string `json:"pro_anchor_cnt"`
	DataDate                   string `json:"data_date"`
	EnabledLiveUserCount       string `json:"enabled_live_user_count"`
	EnabledLiveNewUserCount    string `json:"enabled_live_new_user_count"`
	IncomeUp2wUserCnt          string `json:"income_up2w_user_cnt"`
	NewSignProAnchor           string `json:"new_sign_pro_anchor_cnt"`
	RevnLevelUserCnt           string `json:"revn_level_user_cnt"`
	EnabledLiveUserCountNewOld string `json:"enabled_live_user_count_new_old"`
	ProAnchorCntNewOld         string `json:"pro_anchor_cnt_new_old"`
	MatureAnchorCntNewOld      string `json:"mature_anchor_cnt_new_old"`
	PotActiveAnchorCntNewOld   string `json:"pot_active_anchor_cnt_new_old"`
	ProAnchorCntRemain         string `json:"pro_anchor_cnt_remain"`
	MatureAnchorCntRemain      string `json:"mature_anchor_cnt_remain"`
	PotActiveAnchorCntRemain   string `json:"pot_active_anchor_cnt_remain"`
	IncomeUp2wUserCntNewOld    string `json:"income_up2w_user_cnt_new_old"`
	IncomeUp10wUserCntNewOld   string `json:"income_up10w_user_cnt_new_old"`
	EnabledLive10dUserCount    string `json:"enabled_live_10d_user_count"`
	EnabledLive20dUserCount    string `json:"enabled_live_20d_user_count"`
	EnabledLive5dUserCount     string `json:"enabled_live_5d_user_count"`
	RevenueUpdateTime          string `json:"update_time"`
	RevenueNewOld              string `json:"revenue_new_old"`
}
type GuildLiveStats struct {
	Pagination Pagination `json:"pagination"`
	DataList   []SubData  `json:"data"`
}

type GuildAnalysis struct {
	AnalysisList          []*pb.BusinessAnalysis `json:"analysis_list"`
	MapLevel2Income       map[uint32]uint64      `json:"map_level2_income"`
	WeekLive5dCnt         uint32                 `json:"week_live_5d_cnt"`         //周开启活跃天>5天的人数
	MonthLive10dCnt       uint32                 `json:"month_live_10d_cnt"`       // 月开启活跃天>10天的人数
	MonthLive20dCnt       uint32                 `json:"month_live_20d_cnt"`       // 月开启活跃天>20天的人数
	PorAnchorRemain       uint32                 `json:"pro_anchor_remain"`        // 专业从业者数维持人数
	MatureAnchorRemain    uint32                 `json:"mature_anchor_remain"`     // 成熟达人数维持人数
	PotActiveAnchorRemain uint32                 `json:"pot_active_anchor_remain"` // 潜力活跃达人数维持人数
	LevelGuildCnt         uint32                 `json:"level_guild_cnt"`          // 同层级公会数
}

type IncomeLevel struct {
	level     uint32
	minVal    uint64
	minValStr string
	maxVal    uint64
	maxValStr string
}

var WeekIncomeLevelList = []IncomeLevel{
	{
		level:  1,
		minVal: 0,
		maxVal: 10000000,
	},
	{
		level:  2,
		minVal: 10000000,
		maxVal: 30000000,
	},
	{
		level:  3,
		minVal: 30000000,
		maxVal: 50000000,
	},
	{
		level:  4,
		minVal: 50000000,
		maxVal: 100000000,
	},
	{
		level:  5,
		minVal: 100000000,
		maxVal: 150000000,
	},
	{
		level:  6,
		minVal: 150000000,
		maxVal: 250000000,
	},
	{
		level:  7,
		minVal: 250000000,
		maxVal: 0,
	},
}

var MonthIncomeLevelList = []IncomeLevel{
	{
		level:  1,
		minVal: 0,
		maxVal: 10000000,
	},
	{
		level:  2,
		minVal: 10000000,
		maxVal: 30000000,
	},
	{
		level:  3,
		minVal: 30000000,
		maxVal: 50000000,
	},
	{
		level:  4,
		minVal: 50000000,
		maxVal: 100000000,
	},
	{
		level:  5,
		minVal: 100000000,
		maxVal: 200000000,
	},
	{
		level:  6,
		minVal: 200000000,
		maxVal: 300000000,
	},
	{
		level:  7,
		minVal: 300000000,
		maxVal: 500000000,
	},
	{
		level:  8,
		minVal: 500000000,
		maxVal: 1000000000,
	},
	{
		level:  9,
		minVal: 1000000000,
		maxVal: 0,
	},
}

var AnchorIncomeLevelList = []IncomeLevel{
	{
		level:     1,
		minVal:    0,
		maxVal:    100000,
		minValStr: "0",
		maxValStr: "10w豆",
	},
	{
		level:     2,
		minVal:    100000,
		maxVal:    200000,
		minValStr: "10w豆",
		maxValStr: "20w豆",
	},
	{
		level:     3,
		minVal:    200000,
		maxVal:    500000,
		minValStr: "20w豆",
		maxValStr: "50w豆",
	},
	{
		level:     4,
		minVal:    500000,
		maxVal:    1000000,
		minValStr: "50w豆",
		maxValStr: "100w豆",
	},
	{
		level:     5,
		minVal:    1000000,
		maxVal:    3000000,
		minValStr: "100w豆",
		maxValStr: "300w豆",
	},
	{
		level:     6,
		minVal:    3000000,
		maxVal:    5000000,
		minValStr: "300w豆",
		maxValStr: "500w豆",
	},
	{
		level:     7,
		minVal:    5000000,
		maxVal:    0,
		minValStr: "500w豆",
		maxValStr: "∞",
	},
}

func GetUserRedisKey(id, fansUid uint32, dataType, dateType string) string {
	return fmt.Sprintf("%s%s_%d_%d", dataType, dateType, id, fansUid)
}

func GetUserRedisKeyV2(idA, idB uint32, dataType, dateType string, nowTm time.Time) string {
	switch dateType {
	case Daily:
		return fmt.Sprintf("%s%s_%d%d_%04d%02d%02d", dataType, dateType, idA, idB, nowTm.Year(), nowTm.Month(), nowTm.Day())
	case WeekLy:
		weekNum := (nowTm.Unix() - OneMondayTs) / WeekSeconds
		return fmt.Sprintf("%s%s_%d%d_%d", dataType, dateType, idA, idB, weekNum)
	case MonthLy:
		return fmt.Sprintf("%s%s_%d%d_%04d%02d", dataType, dateType, idA, idB, nowTm.Year(), nowTm.Month())
	}
	return "default"
}

func GetUserRedisExpireTs(dateType string, nowTm time.Time) uint32 {
	switch dateType {
	case Daily:
		/*
		 dayBeginTm := time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day(), 0, 0,0,0, time.Local)
		 gabTs := nowTm.Unix() - dayBeginTm.Unix()
		 return uint32(86400 - gabTs)
		*/
		return 2 * 24 * 3600
	case WeekLy:
		/*
					 // 周一算一周的第一天
					 dayBeginTm := time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day(), 0, 0,0,0, time.Local)
			         weekBeginTm := dayBeginTm.AddDate(0, 0, 1-int(dayBeginTm.Weekday()))
			         if dayBeginTm.Weekday() == time.Sunday {
						 weekBeginTm = dayBeginTm.AddDate(0,0, -6)
					 }
					 gabTs := nowTm.Unix() - weekBeginTm.Unix()
					 return uint32(604800 - gabTs)
		*/
		return 8 * 24 * 3600
	case MonthLy:
		/*
		 monthBeginTm := time.Date(nowTm.Year(), nowTm.Month(), 1, 0,0,0,0, time.Local)
		 gabTs := nowTm.Unix() - monthBeginTm.Unix()
		 return uint32(2592000 - gabTs)
		*/
		return 32 * 24 * 3600
	}
	return 0
}

func HttpQueryGuildLiveStats(url string) (data *GuildLiveStats, err error) {
	client := http.Client{
		Timeout: 5 * time.Second,
	}
	resp, err := client.Get(url)
	if err != nil {
		log.Errorf("HttpQueryGuildLiveStats GET fail url:%s err:%v", url, err)
		return
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		log.Errorf("HttpQueryGuildLiveStats GET response not ok:%s url:%s", resp.Status, url)
		return nil, errors.New(resp.Status)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("HttpQueryGuildLiveStats ioutil.ReadAll failed url:%s err:%v", url, err)
		return nil, err
	}

	respData := &struct {
		Code uint32         `json:"code"`
		Data GuildLiveStats `json:"data"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		log.Errorf("HttpQueryGuildLiveStats json.Unmarshal failed url:%s err:%v", url, err)
		return
	}
	for _, data := range respData.Data.DataList {
		log.Infof("HttpQueryGuildLiveStats data:%+v", data)
	}

	log.Debugf("HttpQueryGuildLiveStats end url:%v respData:%+v", url, respData.Data)
	return &respData.Data, nil
}
