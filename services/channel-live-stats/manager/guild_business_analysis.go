package manager

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channel-live-stats"
	guildCooperation "golang.52tt.com/protocol/services/guild-cooperation"
	signAnchorStatsPb "golang.52tt.com/protocol/services/sign-anchor-stats"
	"golang.52tt.com/services/channel-live-stats/conf"
	"golang.52tt.com/services/channel-live-stats/mysql"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"
)

func getAnalysisByType(analysis *GuildAnalysis, kpiType uint32) *pb.BusinessAnalysis {
	for _, ana := range analysis.AnalysisList {
		if ana.KpiType == kpiType {
			return ana
		}
	}

	return &pb.BusinessAnalysis{}
}

func culIncr(valA, valB uint64) (int64, uint64) {
	if valA > valB {
		return int64(valA - valB), valA - valB
	}

	return -int64(valB - valA), valB - valA
}

func getAnchorIncomeLevel(level uint32) IncomeLevel {
	for _, v := range AnchorIncomeLevelList {
		if v.level == level {
			return v
		}
	}

	return IncomeLevel{}
}

func GetIncomeStr(income uint64) string {
	/*
		if income >= 10000 {
			return fmt.Sprintf("%0.3f万豆", float64(income)/float64(10000))
		}
	*/

	return fmt.Sprintf("%d豆", income)
}

func getLevelStr(val, max, min uint64) string {
	levelStr := LevelMid
	if val >= max {
		levelStr = LevelHigh
	} else if val < min {
		levelStr = LevelLow
	}

	return levelStr
}

func GetLevelIncomeStr(incomeList []IncomeLevel) string {
	sort.Slice(incomeList, func(i, j int) bool {
		return incomeList[i].level < incomeList[j].level
	})
	if len(incomeList) > 2 {
		incomeList = incomeList[:2]
	}

	str := ""
	for i, v := range incomeList {
		if i == 0 {
			str = fmt.Sprintf("[%s，%s)", v.minValStr, v.maxValStr)
		} else {
			str = fmt.Sprintf("%s及[%s，%s)", str, v.minValStr, v.maxValStr)
		}
	}

	return str
}

/*
func getIncomeLevelInfo(analysis, lastAnalysis *GuildAnalysis, changeType string) ([]IncomeLevel, int64) {
	switch changeType {
	case ChangeUp:
		var maxIncr int64
		var maxLevelList []IncomeLevel
		for k, v := range analysis.MapLevel2Income {
			signGap, _ := culIncr(v, lastAnalysis.MapLevel2Income[k])
			if signGap > maxIncr {
				maxIncr = signGap
			}
		}

		if maxIncr != 0 {
			for k, v := range analysis.MapLevel2Income {
				signGap, _ := culIncr(v, lastAnalysis.MapLevel2Income[k])
				if signGap == maxIncr {
					maxLevelList = append(maxLevelList, getAnchorIncomeLevel(k))
				}
			}
		}

		if len(maxLevelList) == 0 {
			maxLevelList = append(maxLevelList, getAnchorIncomeLevel(1))
		}

		if len(maxLevelList) > 2 {
			sort.Slice(maxLevelList, func(i, j int) bool {
				return maxLevelList[i].level < maxLevelList[j].level
			})
			maxLevelList = maxLevelList[:2]
		}

		return maxLevelList, maxIncr
	case ChangeDown:
		var minIncr int64
		var minLevelList []IncomeLevel
		for k, v := range analysis.MapLevel2Income {
			signGap, _ := culIncr(v, lastAnalysis.MapLevel2Income[k])
			if signGap < minIncr {
				minIncr = signGap
			}
		}

		if minIncr != 0 {
			for k, v := range analysis.MapLevel2Income {
				signGap, _ := culIncr(v, lastAnalysis.MapLevel2Income[k])
				if signGap == minIncr {
					minLevelList = append(minLevelList, getAnchorIncomeLevel(k))
				}
			}
		}

		if len(minLevelList) == 0 {
			minLevelList = append(minLevelList, getAnchorIncomeLevel(1))
		}

		if len(minLevelList) > 2 {
			sort.Slice(minLevelList, func(i, j int) bool {
				return minLevelList[i].level < minLevelList[j].level
			})
			minLevelList = minLevelList[:2]
		}

		return minLevelList, minIncr
	case ChangeFluctuation:
		var maxLevel uint32
		var maxIncr uint64
		var signIncr int64
		for k, v := range analysis.MapLevel2Income {
			signGap, gap := culIncr(v, lastAnalysis.MapLevel2Income[k])
			if gap > maxIncr || maxIncr == 0 {
				maxLevel = k
				maxIncr = gap
				signIncr = signGap
			}
		}

		if maxLevel == 0 {
			maxLevel = 1
		}

		log.Debugf("getIncomeLevelInfo end maxLevel:%d maxIncr:%d signIncr:%d", maxLevel, maxIncr, signIncr)
		return []IncomeLevel{getAnchorIncomeLevel(maxLevel)}, signIncr
	}

	return []IncomeLevel{getAnchorIncomeLevel(1)}, 0
}
*/

func getIncomeLevelInfo(guildId uint32, analysis, lastAnalysis *GuildAnalysis, changeType string) ([]IncomeLevel, []IncomeLevel, []IncomeLevel, []IncomeLevel, []IncomeLevel, IncomeLevel,
	int64, int64, int64) {
	incrLevelList := make([]IncomeLevel, 0)
	maxIncrLevelList := make([]IncomeLevel, 0)
	noChangeLevelList := make([]IncomeLevel, 0)
	decLevelList := make([]IncomeLevel, 0)
	maxDecLevelList := make([]IncomeLevel, 0)
	maxChangeLevel := IncomeLevel{}
	var maxIncr, maxDec, signMaxChange int64

	switch changeType {
	case ChangeUp, ChangeDown:
		for k, v := range analysis.MapLevel2Income {
			incomeLevel := getAnchorIncomeLevel(k)
			signGap, _ := culIncr(v, lastAnalysis.MapLevel2Income[k])
			if signGap > 0 {
				if signGap > maxIncr {
					maxIncr = signGap
				}
				incrLevelList = append(incrLevelList, incomeLevel)
			} else if signGap < 0 {
				if signGap < maxDec {
					maxDec = signGap
				}
				decLevelList = append(decLevelList, incomeLevel)
			} else {
				noChangeLevelList = append(noChangeLevelList, incomeLevel)
			}
		}

		if maxIncr > 0 {
			for k, v := range analysis.MapLevel2Income {
				signGap, _ := culIncr(v, lastAnalysis.MapLevel2Income[k])
				if signGap == maxIncr {
					maxIncrLevelList = append(maxIncrLevelList, getAnchorIncomeLevel(k))
				}
			}
		}

		if maxDec < 0 {
			for k, v := range analysis.MapLevel2Income {
				signGap, _ := culIncr(v, lastAnalysis.MapLevel2Income[k])
				if signGap == maxDec {
					maxDecLevelList = append(maxDecLevelList, getAnchorIncomeLevel(k))
				}
			}
		}

	case ChangeFluctuation:
		var maxGap uint64
		for k, v := range analysis.MapLevel2Income {
			signGap, gap := culIncr(v, lastAnalysis.MapLevel2Income[k])
			if gap != 0 {
				if gap > maxGap {
					maxGap = gap
					signMaxChange = signGap
					maxChangeLevel = getAnchorIncomeLevel(k)
				}
			} else {
				noChangeLevelList = append(noChangeLevelList, getAnchorIncomeLevel(k))
			}
		}
	}

	log.Debugf("getIncomeLevelInfo end guildId:%d incrLevelList:%v maxIncrLevelList:%v noChangeLevelList:%v decLevelList:%v maxDecLevelList:%v"+
		" maxChangeLevel:%v maxIncr:%d maxDec:%d signMaxChange:%d analysis:%v lastAnalysis:%v changeType:%s",
		guildId, incrLevelList, maxIncrLevelList, noChangeLevelList, decLevelList, maxDecLevelList, maxChangeLevel, maxIncr, maxDec, signMaxChange, analysis, lastAnalysis, changeType)

	return incrLevelList, maxIncrLevelList, noChangeLevelList, decLevelList, maxDecLevelList, maxChangeLevel, maxIncr, maxDec, signMaxChange
}

func getWeekIncomeDia(guildId uint32, analysis, lastAnalysis *GuildAnalysis) *pb.BusinessDiagnosis {
	ana := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Guild_Income))
	lastAna := getAnalysisByType(lastAnalysis, uint32(pb.KpiType_Kpi_Type_Guild_Income))

	var incomeRatio float64 = 1
	if lastAna.Value != 0 {
		incomeRatio = (float64(ana.Value) - float64(lastAna.Value)) / float64(lastAna.Value)
	}

	manifestationStr := ManifestationNormal
	if ana.Value >= ana.LevelAve {
		if incomeRatio > 0.01 {
			manifestationStr = ManifestationExcellent
		} else if incomeRatio > -0.05 {
			manifestationStr = ManifestationGood
		}
	} else if incomeRatio <= 0.01 {
		manifestationStr = ManifestationBad
	}

	levelStr := LevelMid
	if analysis.LevelGuildCnt == 1 {
		if ana.Value >= 100000000 || (ana.Value >= 25000000 && ana.Value < 30000000) || (ana.Value >= 8000000 && ana.Value < 10000000) {
			levelStr = LevelHigh
		}
		if (ana.Value >= 30000000 && ana.Value < 50000000) || (ana.Value >= 10000000 && ana.Value < 15000000) || (ana.Value >= 0 && ana.Value < 5000000) {
			levelStr = LevelLow
		}
	} else {
		if ana.Value > ana.LevelAve {
			levelStr = LevelHigh
		} else if ana.Value < ana.LevelAve {
			levelStr = LevelLow
		}
	}

	businessDiag := &pb.BusinessDiagnosis{
		KpiType: uint32(pb.KpiType_Kpi_Type_Guild_Income),
	}

	if ana.Value > lastAna.Value {
		newIncr, _ := culIncr(ana.NewValue, lastAna.NewValue)
		oldIncr, _ := culIncr(ana.OldValue, lastAna.OldValue)
		incrLevelList, maxIncrLevelList, _, decLevelList, maxDecLevelList, _, maxIncr, maxDec, _ := getIncomeLevelInfo(guildId, analysis, lastAnalysis, ChangeUp)
		addStr := "增加"
		if len(maxIncrLevelList) >= 2 {
			addStr = "均增加"
		}
		if newIncr > oldIncr {
			if len(incrLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml1, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "新签达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))), GetLevelIncomeStr(maxIncrLevelList), "提升", addStr, fmt.Sprintf("%s", GetIncomeStr(uint64(maxIncr))))
			} else if len(decLevelList) == len(analysis.MapLevel2Income) {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml4, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "新签达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))), "下降")
			} else if len(decLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml8, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "新签达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))), "增长", GetLevelIncomeStr(maxDecLevelList), "下降", "减少", fmt.Sprintf("%s", GetIncomeStr(uint64(-maxDec))))
			} else {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml6, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "新签达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))))
			}

		} else if newIncr == oldIncr {
			if len(incrLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml3, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))), GetLevelIncomeStr(maxIncrLevelList), "提升", addStr, fmt.Sprintf("%s", GetIncomeStr(uint64(maxIncr))))
			} else if len(decLevelList) == len(analysis.MapLevel2Income) {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml5, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))), "下降")
			} else if len(decLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml10, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))), "增长", GetLevelIncomeStr(maxDecLevelList), "下降", "减少", fmt.Sprintf("%s", GetIncomeStr(uint64(-maxDec))))
			} else {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml7, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))))
			}
		} else {
			if len(incrLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml1, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "老达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(oldIncr))), GetLevelIncomeStr(maxIncrLevelList), "提升", addStr, GetIncomeStr(uint64(maxIncr)))
			} else if len(decLevelList) == len(analysis.MapLevel2Income) {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml4, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "老达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(oldIncr))), "下降")
			} else if len(decLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml8, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "老达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(oldIncr))), "增长", GetLevelIncomeStr(maxDecLevelList), "下降", "减少", fmt.Sprintf("%s", GetIncomeStr(uint64(-maxDec))))
			} else {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml6, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "老达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(oldIncr))))
			}

		}
	} else if ana.Value < lastAna.Value {
		newIncr, _ := culIncr(ana.NewValue, lastAna.NewValue)
		oldIncr, _ := culIncr(ana.OldValue, lastAna.OldValue)
		incrLevelList, maxIncrLevelList, _, decLevelList, maxDecLevelList, _, maxIncr, maxDec, _ := getIncomeLevelInfo(guildId, analysis, lastAnalysis, ChangeDown)
		delStr := "减少"
		if len(maxDecLevelList) >= 2 {
			delStr = "均减少"
		}
		if newIncr < oldIncr {
			if len(decLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml1, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "新签达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))), GetLevelIncomeStr(maxDecLevelList), "下降", delStr, GetIncomeStr(uint64(-maxDec)))
			} else if len(incrLevelList) == len(analysis.MapLevel2Income) {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml4, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "新签达人", "减少",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))), "增长")
			} else if len(incrLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml8, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "新签达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))), "下降", GetLevelIncomeStr(maxIncrLevelList), "提升", "增长", GetIncomeStr(uint64(maxIncr)))
			} else {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml6, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "新签达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))))
			}
		} else if newIncr == oldIncr {
			if len(decLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml3, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "减少",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))), GetLevelIncomeStr(maxDecLevelList), "下降", delStr, GetIncomeStr(uint64(-maxDec)))
			} else if len(incrLevelList) == len(analysis.MapLevel2Income) {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml5, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))), "增长")
			} else if len(incrLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml10, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))), "下降", GetLevelIncomeStr(maxIncrLevelList), "提升", "增长", GetIncomeStr(uint64(maxIncr)))
			} else {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml7, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))))
			}
		} else {
			if len(decLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml1, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "老达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-oldIncr))), GetLevelIncomeStr(maxDecLevelList), "下降", delStr, GetIncomeStr(uint64(-maxDec)))
			} else if len(incrLevelList) == len(analysis.MapLevel2Income) {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml4, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "老达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-oldIncr))), "增长")
			} else if len(incrLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml8, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "老达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-oldIncr))), "下降", GetLevelIncomeStr(maxIncrLevelList), "提升", "增长", GetIncomeStr(uint64(maxIncr)))
			} else {
				businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml6, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "老达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-oldIncr))))
			}
		}
	} else {
		_, _, _, _, _, maxChangeLevel, _, _, signMaxChange := getIncomeLevelInfo(guildId, analysis, lastAnalysis, ChangeFluctuation)
		newIncr, newGap := culIncr(ana.NewValue, lastAna.NewValue)
		oldIncr, oldGap := culIncr(ana.OldValue, lastAna.OldValue)
		var newIncrStr, oldIncrStr, levelIncrStr, levelIncomeIncrStr string = "增加", "增加", "提升", "增加"
		gapVal := signMaxChange
		if newIncr < 0 {
			newIncrStr = "减少"
		}
		if oldIncr < 0 {
			oldIncrStr = "减少"
		}

		if signMaxChange < 0 {
			levelIncrStr = "下降"
			levelIncomeIncrStr = "减少"
			gapVal = -signMaxChange
		}

		if maxChangeLevel.level > 0 {
			businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml2, manifestationStr, GetIncomeStr(ana.Value), levelStr, "持平", GetIncomeStr(ana.NewValue),
				fmt.Sprintf("%s<span style='font-weight:bold'> %s</span>", newIncrStr, GetIncomeStr(newGap)), GetIncomeStr(ana.OldValue), fmt.Sprintf("%s<span style='font-weight:bold'> %s</span>", oldIncrStr, GetIncomeStr(oldGap)),
				GetLevelIncomeStr([]IncomeLevel{maxChangeLevel}), levelIncrStr, levelIncomeIncrStr, GetIncomeStr(uint64(gapVal)))
		} else {
			businessDiag.Diag = fmt.Sprintf(WeekGuildIncomeHtml9, manifestationStr, GetIncomeStr(ana.Value), levelStr, "持平", GetIncomeStr(ana.NewValue),
				fmt.Sprintf("%s<span style='font-weight:bold'> %s</span>", newIncrStr, GetIncomeStr(newGap)), GetIncomeStr(ana.OldValue), fmt.Sprintf("%s<span style='font-weight:bold'> %s</span>", oldIncrStr, GetIncomeStr(oldGap)))
		}

	}

	log.Debugf("getWeekIncomeDia end guildId:%d ana:%v lastAna:%v analysis:%v businessDiag:%v", guildId, ana, lastAna, analysis, businessDiag)
	return businessDiag
}

func getMonthIncomeDia(guildId uint32, analysis, lastAnalysis *GuildAnalysis) *pb.BusinessDiagnosis {
	ana := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Guild_Income))
	lastAna := getAnalysisByType(lastAnalysis, uint32(pb.KpiType_Kpi_Type_Guild_Income))

	var incomeRatio float64 = 1
	if lastAna.Value != 0 {
		incomeRatio = (float64(ana.Value) - float64(lastAna.Value)) / float64(lastAna.Value)
	}

	manifestationStr := ManifestationNormal
	if ana.Value >= ana.LevelAve {
		if incomeRatio > 0.01 {
			manifestationStr = ManifestationExcellent
		} else if incomeRatio > -0.05 {
			manifestationStr = ManifestationGood
		}
	} else if incomeRatio <= 0.01 {
		manifestationStr = ManifestationBad
	}

	levelStr := LevelMid
	if analysis.LevelGuildCnt == 1 {
		if ana.Value >= 300000000 || (ana.Value >= 80000000 && ana.Value < 100000000) || (ana.Value >= 30000000 && ana.Value < 50000000) {
			levelStr = LevelHigh
		}
		if (ana.Value >= 100000000 && ana.Value < 200000000) || (ana.Value >= 50000000 && ana.Value < 60000000) || (ana.Value >= 0 && ana.Value < 10000000) {
			levelStr = LevelLow
		}
	} else {
		if ana.Value > ana.LevelAve {
			levelStr = LevelHigh
		} else if ana.Value < ana.LevelAve {
			levelStr = LevelLow
		}
	}

	businessDiag := &pb.BusinessDiagnosis{
		KpiType: uint32(pb.KpiType_Kpi_Type_Guild_Income),
	}

	if ana.Value > lastAna.Value {
		newIncr, _ := culIncr(ana.NewValue, lastAna.NewValue)
		oldIncr, _ := culIncr(ana.OldValue, lastAna.OldValue)
		incrLevelList, maxIncrLevelList, _, decLevelList, maxDecLevelList, _, maxIncr, maxDec, _ := getIncomeLevelInfo(guildId, analysis, lastAnalysis, ChangeUp)
		addStr := "增加"
		if len(maxIncrLevelList) >= 2 {
			addStr = "均增加"
		}

		if newIncr > oldIncr {
			if len(incrLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml1, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "新签达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))), GetLevelIncomeStr(maxIncrLevelList), "提升", addStr, GetIncomeStr(uint64(maxIncr)))
			} else if len(decLevelList) == len(analysis.MapLevel2Income) {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml4, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "新签达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))), "下降")
			} else if len(decLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml8, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "新签达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))), "增长", GetLevelIncomeStr(maxDecLevelList), "下降", "减少", GetIncomeStr(uint64(-maxDec)))
			} else {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml6, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "新签达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))))
			}
		} else if newIncr == oldIncr {
			if len(incrLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml3, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))), GetLevelIncomeStr(maxIncrLevelList), "提升", addStr, GetIncomeStr(uint64(maxIncr)))
			} else if len(decLevelList) == len(analysis.MapLevel2Income) {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml5, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))), "下降")
			} else if len(decLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml9, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))), "增长", GetLevelIncomeStr(maxDecLevelList), "下降", "减少", GetIncomeStr(uint64(-maxDec)))
			} else {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml7, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(newIncr))))
			}
		} else {
			if len(incrLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml1, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "老达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(oldIncr))), GetLevelIncomeStr(maxIncrLevelList), "提升", addStr, GetIncomeStr(uint64(maxIncr)))
			} else if len(decLevelList) == len(analysis.MapLevel2Income) {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml4, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "老达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(oldIncr))), "下降")
			} else if len(decLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml8, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "老达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(oldIncr))), "增长", GetLevelIncomeStr(maxDecLevelList), "下降", "减少", GetIncomeStr(uint64(-maxDec)))
			} else {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml6, manifestationStr, GetIncomeStr(ana.Value), levelStr, "增加", "老达人", "增加",
					fmt.Sprintf("增加<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(oldIncr))))
			}
		}
	} else if ana.Value < lastAna.Value {
		newIncr, _ := culIncr(ana.NewValue, lastAna.NewValue)
		oldIncr, _ := culIncr(ana.OldValue, lastAna.OldValue)
		incrLevelList, maxIncrLevelList, _, decLevelList, maxDecLevelList, _, maxIncr, maxDec, _ := getIncomeLevelInfo(guildId, analysis, lastAnalysis, ChangeDown)
		delStr := "减少"
		if len(decLevelList) >= 2 {
			delStr = "均减少"
		}
		if newIncr < oldIncr {
			if len(decLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml1, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "新签达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))), GetLevelIncomeStr(maxDecLevelList), "下降", delStr, GetIncomeStr(uint64(-maxDec)))
			} else if len(incrLevelList) == len(analysis.MapLevel2Income) {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml4, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "新签达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))), "增长")
			} else if len(incrLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml8, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "新签达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))), "下降", GetLevelIncomeStr(maxIncrLevelList), "提升", "增长", GetIncomeStr(uint64(maxIncr)))
			} else {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml6, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "新签达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))))
			}
		} else if newIncr == oldIncr {
			if len(decLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml3, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "减少",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))), GetLevelIncomeStr(maxDecLevelList), "下降", delStr, GetIncomeStr(uint64(-maxDec)))
			} else if len(incrLevelList) == len(analysis.MapLevel2Income) {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml5, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "减少",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))), "增长")
			} else if len(incrLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml9, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))), "下降", GetLevelIncomeStr(maxIncrLevelList), "提升", "增长", GetIncomeStr(uint64(maxIncr)))
			} else {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml7, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-newIncr))))
			}
		} else {
			if len(decLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml1, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "老达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-oldIncr))), GetLevelIncomeStr(maxDecLevelList), "下降", delStr, GetIncomeStr(uint64(-maxDec)))
			} else if len(incrLevelList) == len(analysis.MapLevel2Income) {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml4, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "老达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-oldIncr))), "增长")
			} else if len(incrLevelList) > 0 {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml8, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "老达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-oldIncr))), "下降", GetLevelIncomeStr(maxIncrLevelList), "提升", "增长", GetIncomeStr(uint64(maxIncr)))
			} else {
				businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml6, manifestationStr, GetIncomeStr(ana.Value), levelStr, "减少", "老达人", "下降",
					fmt.Sprintf("减少<span style='font-weight:bold'> %s</span>", GetIncomeStr(uint64(-oldIncr))))
			}
		}
	} else {
		_, _, _, _, _, maxChangeLevel, _, _, signMaxChange := getIncomeLevelInfo(guildId, analysis, lastAnalysis, ChangeFluctuation)
		newIncr, newGap := culIncr(ana.NewValue, lastAna.NewValue)
		oldIncr, oldGap := culIncr(ana.OldValue, lastAna.OldValue)
		var newIncrStr, oldIncrStr, levelIncrStr, levelIncomeIncrStr string = "增加", "增加", "提升", "增加"
		gapVal := signMaxChange
		if newIncr < 0 {
			newIncrStr = "减少"
		}
		if oldIncr < 0 {
			oldIncrStr = "减少"
		}

		if signMaxChange < 0 {
			levelIncrStr = "下降"
			levelIncomeIncrStr = "减少"
			gapVal = -signMaxChange
		}

		if maxChangeLevel.level > 0 {
			businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml2, manifestationStr, GetIncomeStr(ana.Value), levelStr, "持平", GetIncomeStr(ana.NewValue),
				fmt.Sprintf("%s<span style='font-weight:bold'> %s</span>", newIncrStr, GetIncomeStr(newGap)), GetIncomeStr(ana.OldValue), fmt.Sprintf("%s<span style='font-weight:bold'> %s</span>", oldIncrStr, GetIncomeStr(oldGap)),
				GetLevelIncomeStr([]IncomeLevel{maxChangeLevel}), levelIncrStr, levelIncomeIncrStr, GetIncomeStr(uint64(gapVal)))
		} else {
			businessDiag.Diag = fmt.Sprintf(MonthGuildIncomeHtml10, manifestationStr, GetIncomeStr(ana.Value), levelStr, "持平", GetIncomeStr(ana.NewValue),
				fmt.Sprintf("%s<span style='font-weight:bold'> %s</span>", newIncrStr, GetIncomeStr(newGap)), GetIncomeStr(ana.OldValue), fmt.Sprintf("%s<span style='font-weight:bold'> %s</span>", oldIncrStr, GetIncomeStr(oldGap)))
		}

	}

	log.Debugf("getMonthIncomeDia end guildId:%d ana:%v lastAna:%v analysis:%v businessDiag:%v", guildId, ana, lastAna, analysis, businessDiag)
	return businessDiag
}

func getNewSignAnchorDia(guildId uint32, analysis, lastAnalysis *GuildAnalysis) *pb.BusinessDiagnosis {
	ana := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_New_Sign_Anchor))
	lastAna := getAnalysisByType(lastAnalysis, uint32(pb.KpiType_Kpi_Type_New_Sign_Anchor))

	incomeAna := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Guild_Income))
	liveAnchorAna := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Live_Anchor))
	lastLiveAnchorAna := getAnalysisByType(lastAnalysis, uint32(pb.KpiType_Kpi_Type_Live_Anchor))

	manifestationStr := ManifestationNormal
	incr, _ := culIncr(ana.Value, lastAna.Value)
	if incomeAna.Value >= 30000000 {
		if ana.Value >= ana.LevelAve {
			if incr >= 10 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 4 {
				manifestationStr = ManifestationGood
			} else if incr <= -6 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 4 {
				manifestationStr = ManifestationGood
			} else if incr <= -6 {
				manifestationStr = ManifestationBad
			}
		}
	} else {
		if ana.Value >= ana.LevelAve {
			if incr >= 5 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 2 {
				manifestationStr = ManifestationGood
			} else if incr <= -4 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 2 {
				manifestationStr = ManifestationGood
			} else if incr <= -4 {
				manifestationStr = ManifestationBad
			}
		}
	}

	levelStr := LevelMid
	if analysis.LevelGuildCnt == 1 {
		if incomeAna.Value >= 30000000 {
			levelStr = getLevelStr(ana.Value, 100, 60)
		} else if incomeAna.Value >= 10000000 {
			levelStr = getLevelStr(ana.Value, 60, 30)
		} else {
			levelStr = getLevelStr(ana.Value, 30, 15)
		}

	} else {
		if ana.Value > ana.LevelAve {
			levelStr = LevelHigh
		} else if ana.Value < ana.LevelAve {
			levelStr = LevelLow
		}
	}

	changeStr := ChangeUp
	if incr < 0 {
		changeStr = ChangeDown
	} else if incr == 0 {
		changeStr = ChangeNo
	}

	businessDiag := &pb.BusinessDiagnosis{
		KpiType: uint32(pb.KpiType_Kpi_Type_New_Sign_Anchor),
	}

	var liveRatio, lastLiveRatio float32 = 0, 0
	if ana.Value > 0 {
		liveRatio = float32(liveAnchorAna.NewValue) / float32(ana.Value)
	}
	if lastAna.Value > 0 {
		lastLiveRatio = float32(lastLiveAnchorAna.NewValue) / float32(lastAna.Value)
	}

	ratioChangeStr := ChangeNo
	ratioIncrStr := ""
	if liveRatio > lastLiveRatio {
		ratioChangeStr = ChangeUp
		ratioIncrStr = fmt.Sprintf("%0.2f%%", (liveRatio-lastLiveRatio)*100)
	} else if liveRatio < lastLiveRatio {
		ratioChangeStr = ChangeDown
		ratioIncrStr = fmt.Sprintf("%0.2f%%", (lastLiveRatio-liveRatio)*100)
	}

	businessDiag.Diag = fmt.Sprintf(WeekNewSignAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, changeStr, fmt.Sprintf("%d", liveAnchorAna.NewValue),
		fmt.Sprintf("%0.2f%%", liveRatio*100), ratioChangeStr, ratioIncrStr)

	log.Debugf("getNewSignAnchor end guildId:%d ana:%v lastAna:%v analysis:%v businessDiag:%v", guildId, ana, lastAna, analysis, businessDiag)
	return businessDiag
}

func getMonthNewSignAnchorDia(guildId uint32, analysis, lastAnalysis *GuildAnalysis) *pb.BusinessDiagnosis {
	ana := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_New_Sign_Anchor))
	lastAna := getAnalysisByType(lastAnalysis, uint32(pb.KpiType_Kpi_Type_New_Sign_Anchor))

	incomeAna := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Guild_Income))
	liveAnchorAna := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Live_Anchor))
	lastLiveAnchorAna := getAnalysisByType(lastAnalysis, uint32(pb.KpiType_Kpi_Type_Live_Anchor))

	manifestationStr := ManifestationNormal
	incr, _ := culIncr(ana.Value, lastAna.Value)
	if incomeAna.Value >= 50000000 {
		if ana.Value >= ana.LevelAve {
			if incr >= 20 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 10 {
				manifestationStr = ManifestationGood
			} else if incr <= -11 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 10 {
				manifestationStr = ManifestationGood
			} else if incr <= -11 {
				manifestationStr = ManifestationBad
			}
		}
	} else {
		if ana.Value >= ana.LevelAve {
			if incr >= 10 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 5 {
				manifestationStr = ManifestationGood
			} else if incr <= -6 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 5 {
				manifestationStr = ManifestationGood
			} else if incr <= -6 {
				manifestationStr = ManifestationBad
			}
		}
	}

	levelStr := LevelMid
	if analysis.LevelGuildCnt == 1 {
		if incomeAna.Value >= 100000000 {
			levelStr = getLevelStr(ana.Value, 650, 350)
		} else if incomeAna.Value >= 50000000 {
			levelStr = getLevelStr(ana.Value, 200, 130)
		} else {
			levelStr = getLevelStr(ana.Value, 100, 50)
		}

	} else {
		if ana.Value > ana.LevelAve {
			levelStr = LevelHigh
		} else if ana.Value < ana.LevelAve {
			levelStr = LevelLow
		}
	}

	changeStr := ChangeUp
	if incr < 0 {
		changeStr = ChangeDown
	} else if incr == 0 {
		changeStr = ChangeNo
	}

	businessDiag := &pb.BusinessDiagnosis{
		KpiType: uint32(pb.KpiType_Kpi_Type_New_Sign_Anchor),
	}

	var liveRatio, lastLiveRatio float32 = 0, 0
	if ana.Value > 0 {
		liveRatio = float32(liveAnchorAna.NewValue) / float32(ana.Value)
	}
	if lastAna.Value > 0 {
		lastLiveRatio = float32(lastLiveAnchorAna.NewValue) / float32(lastAna.Value)
	}

	ratioChangeStr := ChangeNo
	ratioIncrStr := ""
	if liveRatio > lastLiveRatio {
		ratioChangeStr = ChangeUp
		ratioIncrStr = fmt.Sprintf("%0.2f%%", (liveRatio-lastLiveRatio)*100)
	} else if liveRatio < lastLiveRatio {
		ratioChangeStr = ChangeDown
		ratioIncrStr = fmt.Sprintf("%0.2f%%", (lastLiveRatio-liveRatio)*100)
	}

	businessDiag.Diag = fmt.Sprintf(MonthNewSignAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, changeStr, fmt.Sprintf("%d", liveAnchorAna.NewValue),
		fmt.Sprintf("%0.2f%%", liveRatio*100), ratioChangeStr, ratioIncrStr)

	log.Debugf("getMonthNewSignAnchorDia end guildId:%d ana:%v lastAna:%v analysis:%v businessDiag:%v", guildId, ana, lastAna, analysis, businessDiag)
	return businessDiag
}

func getLiveAnchorDia(guildId uint32, analysis, lastAnalysis *GuildAnalysis) *pb.BusinessDiagnosis {
	ana := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Live_Anchor))
	lastAna := getAnalysisByType(lastAnalysis, uint32(pb.KpiType_Kpi_Type_Live_Anchor))

	incomeAna := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Guild_Income))

	manifestationStr := ManifestationNormal
	incr, _ := culIncr(ana.Value, lastAna.Value)
	if incomeAna.Value >= 30000000 {
		if ana.Value >= ana.LevelAve {
			if incr >= 10 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 5 {
				manifestationStr = ManifestationGood
			} else if incr <= -4 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 5 {
				manifestationStr = ManifestationGood
			} else if incr <= -4 {
				manifestationStr = ManifestationBad
			}
		}
	} else {
		if ana.Value >= ana.LevelAve {
			if incr >= 5 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 3 {
				manifestationStr = ManifestationGood
			} else if incr <= -3 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 3 {
				manifestationStr = ManifestationGood
			} else if incr <= -3 {
				manifestationStr = ManifestationBad
			}
		}
	}

	levelStr := LevelMid
	if analysis.LevelGuildCnt == 1 {
		if incomeAna.Value >= 30000000 {
			levelStr = getLevelStr(ana.Value, 1000, 500)
		} else if incomeAna.Value >= 10000000 {
			levelStr = getLevelStr(ana.Value, 500, 250)
		} else {
			levelStr = getLevelStr(ana.Value, 200, 100)
		}
	} else {
		if ana.Value > ana.LevelAve {
			levelStr = LevelHigh
		} else if ana.Value < ana.LevelAve {
			levelStr = LevelLow
		}
	}

	businessDiag := &pb.BusinessDiagnosis{
		KpiType: uint32(pb.KpiType_Kpi_Type_Live_Anchor),
	}

	var activeLiveRatio float32 = 0
	if ana.Value > 0 {
		activeLiveRatio = float32(analysis.WeekLive5dCnt) / float32(ana.Value)
	}

	newIncr, newGap := culIncr(ana.NewValue, lastAna.NewValue)
	oldIncr, oldGap := culIncr(ana.OldValue, lastAna.OldValue)
	if ana.Value > lastAna.Value {
		if newIncr > oldIncr {
			businessDiag.Diag = fmt.Sprintf(WeekLiveAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, fmt.Sprintf("%d", analysis.WeekLive5dCnt),
				fmt.Sprintf("%0.2f%%", activeLiveRatio*100), "增加", "新签达人", "上升", "增加", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", newIncr))
		} else if newIncr == oldIncr {
			businessDiag.Diag = fmt.Sprintf(WeekLiveAnchorHtml3, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, fmt.Sprintf("%d", analysis.WeekLive5dCnt),
				fmt.Sprintf("%0.2f%%", activeLiveRatio*100), "增加", "上升", fmt.Sprintf("增加<span style='font-weight:bold'> %d人</span>", newIncr))
		} else {
			businessDiag.Diag = fmt.Sprintf(WeekLiveAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, fmt.Sprintf("%d", analysis.WeekLive5dCnt),
				fmt.Sprintf("%0.2f%%", activeLiveRatio*100), "增加", "老达人", "上升", "增加", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", oldIncr))
		}
	} else if ana.Value < lastAna.Value {
		if newIncr < oldIncr {
			businessDiag.Diag = fmt.Sprintf(WeekLiveAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, fmt.Sprintf("%d", analysis.WeekLive5dCnt),
				fmt.Sprintf("%0.2f%%", activeLiveRatio*100), "减少", "新签达人", "下降", "减少", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", -newIncr))
		} else if newIncr == oldIncr {
			businessDiag.Diag = fmt.Sprintf(WeekLiveAnchorHtml3, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, fmt.Sprintf("%d", analysis.WeekLive5dCnt),
				fmt.Sprintf("%0.2f%%", activeLiveRatio*100), "减少", "减少", fmt.Sprintf("减少<span style='font-weight:bold'> %d人</span>", -newIncr))
		} else {
			businessDiag.Diag = fmt.Sprintf(WeekLiveAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, fmt.Sprintf("%d", analysis.WeekLive5dCnt),
				fmt.Sprintf("%0.2f%%", activeLiveRatio*100), "减少", "老达人", "下降", "减少", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", -oldIncr))
		}
	} else {
		newIncrStr := ChangeUp
		if newIncr < 0 {
			newIncrStr = ChangeDown
		}
		oldIncrStr := ChangeUp
		if oldIncr < 0 {
			oldIncrStr = ChangeDown
		}
		businessDiag.Diag = fmt.Sprintf(WeekLiveAnchorHtml2, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, fmt.Sprintf("%d", analysis.WeekLive5dCnt),
			fmt.Sprintf("%0.2f%%", activeLiveRatio*100), "持平", fmt.Sprintf("%d", ana.NewValue), newIncrStr, fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", newGap), fmt.Sprintf("%d", ana.OldValue),
			oldIncrStr, fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", oldGap))
	}

	log.Debugf("getLiveAnchorDia end guildId:%d ana:%v lastAna:%v analysis:%v businessDiag:%v", guildId, ana, lastAna, analysis, businessDiag)
	return businessDiag
}

func getMonthLiveAnchorDia(guildId uint32, analysis, lastAnalysis *GuildAnalysis) *pb.BusinessDiagnosis {
	ana := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Live_Anchor))
	lastAna := getAnalysisByType(lastAnalysis, uint32(pb.KpiType_Kpi_Type_Live_Anchor))

	incomeAna := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Guild_Income))

	manifestationStr := ManifestationNormal
	incr, _ := culIncr(ana.Value, lastAna.Value)
	if incomeAna.Value >= 50000000 {
		if ana.Value >= ana.LevelAve {
			if incr >= 20 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 10 {
				manifestationStr = ManifestationGood
			} else if incr <= -11 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 10 {
				manifestationStr = ManifestationGood
			} else if incr <= -11 {
				manifestationStr = ManifestationBad
			}
		}
	} else {
		if ana.Value >= ana.LevelAve {
			if incr >= 10 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 5 {
				manifestationStr = ManifestationGood
			} else if incr <= -6 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 5 {
				manifestationStr = ManifestationGood
			} else if incr <= -6 {
				manifestationStr = ManifestationBad
			}
		}
	}

	levelStr := LevelMid
	if analysis.LevelGuildCnt == 1 {
		if incomeAna.Value >= 100000000 {
			levelStr = getLevelStr(ana.Value, 2500, 1500)
		} else if incomeAna.Value >= 50000000 {
			levelStr = getLevelStr(ana.Value, 800, 400)
		} else {
			levelStr = getLevelStr(ana.Value, 500, 250)
		}
	} else {
		if ana.Value > ana.LevelAve {
			levelStr = LevelHigh
		} else if ana.Value < ana.LevelAve {
			levelStr = LevelLow
		}
	}

	businessDiag := &pb.BusinessDiagnosis{
		KpiType: uint32(pb.KpiType_Kpi_Type_Live_Anchor),
	}

	var activeLive10dRatio, activeLive20dRatio float32 = 0, 0
	if ana.Value > 0 {
		activeLive10dRatio = float32(analysis.MonthLive10dCnt) / float32(ana.Value)
		activeLive20dRatio = float32(analysis.MonthLive20dCnt) / float32(ana.Value)
	}

	newIncr, newGap := culIncr(ana.NewValue, lastAna.NewValue)
	oldIncr, oldGap := culIncr(ana.OldValue, lastAna.OldValue)
	if ana.Value > lastAna.Value {
		if newIncr > oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthLiveAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, fmt.Sprintf("%d", analysis.MonthLive10dCnt),
				fmt.Sprintf("%0.2f%%", activeLive10dRatio*100), fmt.Sprintf("%d", analysis.MonthLive20dCnt), fmt.Sprintf("%0.2f%%", activeLive20dRatio*100),
				"增加", "新签达人", "上升", "增加", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", newIncr))
		} else if newIncr == oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthLiveAnchorHtml3, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, fmt.Sprintf("%d", analysis.MonthLive10dCnt),
				fmt.Sprintf("%0.2f%%", activeLive10dRatio*100), fmt.Sprintf("%d", analysis.MonthLive20dCnt), fmt.Sprintf("%0.2f%%", activeLive20dRatio*100),
				"增加", "增加", fmt.Sprintf("增加<span style='font-weight:bold'> %d人</span>", newIncr))
		} else {
			businessDiag.Diag = fmt.Sprintf(MonthLiveAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, fmt.Sprintf("%d", analysis.MonthLive10dCnt),
				fmt.Sprintf("%0.2f%%", activeLive10dRatio*100), fmt.Sprintf("%d", analysis.MonthLive20dCnt), fmt.Sprintf("%0.2f%%", activeLive20dRatio*100),
				"增加", "老达人", "上升", "增加", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", oldIncr))
		}
	} else if ana.Value < lastAna.Value {
		if newIncr < oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthLiveAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, fmt.Sprintf("%d", analysis.MonthLive10dCnt),
				fmt.Sprintf("%0.2f%%", activeLive10dRatio*100), fmt.Sprintf("%d", analysis.MonthLive20dCnt), fmt.Sprintf("%0.2f%%", activeLive20dRatio*100),
				"减少", "新签达人", "下降", "减少", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", -newIncr))
		} else if newIncr == oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthLiveAnchorHtml3, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, fmt.Sprintf("%d", analysis.MonthLive10dCnt),
				fmt.Sprintf("%0.2f%%", activeLive10dRatio*100), fmt.Sprintf("%d", analysis.MonthLive20dCnt), fmt.Sprintf("%0.2f%%", activeLive20dRatio*100),
				"减少", "减少", fmt.Sprintf("减少<span style='font-weight:bold'> %d人</span>", -newIncr))
		} else {
			businessDiag.Diag = fmt.Sprintf(MonthLiveAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, fmt.Sprintf("%d", analysis.MonthLive10dCnt),
				fmt.Sprintf("%0.2f%%", activeLive10dRatio*100), fmt.Sprintf("%d", analysis.MonthLive20dCnt), fmt.Sprintf("%0.2f%%", activeLive20dRatio*100),
				"减少", "老达人", "下降", "减少", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", -oldIncr))
		}
	} else {
		newIncrStr := ChangeUp
		if newIncr < 0 {
			newIncrStr = ChangeDown
		}
		oldIncrStr := ChangeUp
		if oldIncr < 0 {
			oldIncrStr = ChangeDown
		}
		businessDiag.Diag = fmt.Sprintf(MonthLiveAnchorHtml2, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, fmt.Sprintf("%d", analysis.MonthLive10dCnt),
			fmt.Sprintf("%0.2f%%", activeLive10dRatio*100), fmt.Sprintf("%d", analysis.MonthLive20dCnt), fmt.Sprintf("%0.2f%%", activeLive20dRatio*100), "持平", fmt.Sprintf("%d", ana.NewValue), newIncrStr, fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", newGap), fmt.Sprintf("%d", ana.OldValue),
			oldIncrStr, fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", oldGap))
	}

	log.Debugf("getMonthLiveAnchorDia end guildId:%d ana:%v lastAna:%v analysis:%v businessDiag:%v", guildId, ana, lastAna, analysis, businessDiag)
	return businessDiag
}

func getTwoWanAnchorDia(guildId uint32, analysis, lastAnalysis *GuildAnalysis) *pb.BusinessDiagnosis {
	ana := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Two_Wan_Anchor))
	lastAna := getAnalysisByType(lastAnalysis, uint32(pb.KpiType_Kpi_Type_Two_Wan_Anchor))

	incomeAna := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Guild_Income))

	manifestationStr := ManifestationNormal
	incr, _ := culIncr(ana.Value, lastAna.Value)
	if incomeAna.Value >= 30000000 {
		if ana.Value >= ana.LevelAve {
			if incr >= 10 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 4 {
				manifestationStr = ManifestationGood
			} else if incr <= -5 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 4 {
				manifestationStr = ManifestationGood
			} else if incr <= -5 {
				manifestationStr = ManifestationBad
			}
		}
	} else {
		if ana.Value >= ana.LevelAve {
			if incr >= 5 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 2 {
				manifestationStr = ManifestationGood
			} else if incr <= -3 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 2 {
				manifestationStr = ManifestationGood
			} else if incr <= -3 {
				manifestationStr = ManifestationBad
			}
		}
	}

	levelStr := LevelMid
	if analysis.LevelGuildCnt == 1 {
		if incomeAna.Value >= 30000000 {
			levelStr = getLevelStr(ana.Value, 300, 150)
		} else if incomeAna.Value >= 10000000 {
			levelStr = getLevelStr(ana.Value, 100, 70)
		} else {
			levelStr = getLevelStr(ana.Value, 35, 15)
		}
	} else {
		if ana.Value > ana.LevelAve {
			levelStr = LevelHigh
		} else if ana.Value < ana.LevelAve {
			levelStr = LevelLow
		}
	}

	businessDiag := &pb.BusinessDiagnosis{
		KpiType: uint32(pb.KpiType_Kpi_Type_Two_Wan_Anchor),
	}

	newIncr, newGap := culIncr(ana.NewValue, lastAna.NewValue)
	oldIncr, oldGap := culIncr(ana.OldValue, lastAna.OldValue)
	if ana.Value > lastAna.Value {
		if newIncr > oldIncr {
			businessDiag.Diag = fmt.Sprintf(WeekTwoWanAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, "增加", "新签达人", "增加", "增加", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", newIncr))
		} else if newIncr == oldIncr {
			businessDiag.Diag = fmt.Sprintf(WeekTwoWanAnchorHtml3, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, "增加", "增加", fmt.Sprintf("增加<span style='font-weight:bold'> %d人</span>", newIncr))
		} else {
			businessDiag.Diag = fmt.Sprintf(WeekTwoWanAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, "增加", "老达人", "增加", "增加", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", oldIncr))
		}
	} else if ana.Value < lastAna.Value {
		if newIncr < oldIncr {
			businessDiag.Diag = fmt.Sprintf(WeekTwoWanAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, "减少", "新签达人", "下降", "减少", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", -newIncr))
		} else if newIncr == oldIncr {
			businessDiag.Diag = fmt.Sprintf(WeekTwoWanAnchorHtml3, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, "减少", "减少", fmt.Sprintf("减少<span style='font-weight:bold'> %d人</span>", -newIncr))
		} else {
			businessDiag.Diag = fmt.Sprintf(WeekTwoWanAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, "减少", "老达人", "下降", "减少", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", -oldIncr))
		}
	} else {
		newIncrStr := ChangeUp
		if newIncr < 0 {
			newIncrStr = ChangeDown
		}
		oldIncrStr := ChangeUp
		if oldIncr < 0 {
			oldIncrStr = ChangeDown
		}
		businessDiag.Diag = fmt.Sprintf(WeekTwoWanAnchorHtml2, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, "持平", fmt.Sprintf("%d", ana.NewValue),
			newIncrStr, fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", newGap), fmt.Sprintf("%d", ana.OldValue), oldIncrStr, fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", oldGap))
	}

	log.Debugf("getTwoWanAnchorDia end guildId:%d ana:%v lastAna:%v analysis:%v businessDiag:%v", guildId, ana, lastAna, analysis, businessDiag)
	return businessDiag
}

func getTenWanAnchorDia(guildId uint32, analysis, lastAnalysis *GuildAnalysis) *pb.BusinessDiagnosis {
	ana := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Ten_Wan_Anchor))
	lastAna := getAnalysisByType(lastAnalysis, uint32(pb.KpiType_Kpi_Type_Ten_Wan_Anchor))

	incomeAna := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Guild_Income))

	manifestationStr := ManifestationNormal
	incr, _ := culIncr(ana.Value, lastAna.Value)
	if incomeAna.Value >= 30000000 {
		if ana.Value >= ana.LevelAve {
			if incr >= 6 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 2 {
				manifestationStr = ManifestationGood
			} else if incr <= -3 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 2 {
				manifestationStr = ManifestationGood
			} else if incr <= -3 {
				manifestationStr = ManifestationBad
			}
		}
	} else {
		if ana.Value >= ana.LevelAve {
			if incr >= 3 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 1 {
				manifestationStr = ManifestationGood
			} else if incr <= -2 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 1 {
				manifestationStr = ManifestationGood
			} else if incr <= -2 {
				manifestationStr = ManifestationBad
			}
		}
	}

	levelStr := LevelMid
	if analysis.LevelGuildCnt == 1 {
		if incomeAna.Value >= 30000000 {
			levelStr = getLevelStr(ana.Value, 150, 80)
		} else if incomeAna.Value >= 10000000 {
			levelStr = getLevelStr(ana.Value, 50, 30)
		} else {
			levelStr = getLevelStr(ana.Value, 20, 10)
		}
	} else {
		if ana.Value > ana.LevelAve {
			levelStr = LevelHigh
		} else if ana.Value < ana.LevelAve {
			levelStr = LevelLow
		}
	}

	businessDiag := &pb.BusinessDiagnosis{
		KpiType: uint32(pb.KpiType_Kpi_Type_Ten_Wan_Anchor),
	}

	newIncr, newGap := culIncr(ana.NewValue, lastAna.NewValue)
	oldIncr, oldGap := culIncr(ana.OldValue, lastAna.OldValue)
	if ana.Value > lastAna.Value {
		if newIncr > oldIncr {
			businessDiag.Diag = fmt.Sprintf(WeekTenWanAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, "增加", "新签达人", "增加", "增加", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", newIncr))
		} else if newIncr == oldIncr {
			businessDiag.Diag = fmt.Sprintf(WeekTenWanAnchorHtml3, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, "增加", "增加", fmt.Sprintf("增加<span style='font-weight:bold'> %d人</span>", newIncr))
		} else {
			businessDiag.Diag = fmt.Sprintf(WeekTenWanAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, "增加", "老达人", "增加", "增加", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", oldIncr))
		}
	} else if ana.Value < lastAna.Value {
		if newIncr < oldIncr {
			businessDiag.Diag = fmt.Sprintf(WeekTenWanAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, "减少", "新签达人", "下降", "减少", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", -newIncr))
		} else if newIncr == oldIncr {
			businessDiag.Diag = fmt.Sprintf(WeekTenWanAnchorHtml3, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, "减少", "减少", fmt.Sprintf("减少<span style='font-weight:bold'> %d人</span>", -newIncr))
		} else {
			businessDiag.Diag = fmt.Sprintf(WeekTenWanAnchorHtml1, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, "减少", "老达人", "下降", "减少", fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", -oldIncr))
		}
	} else {
		newIncrStr := ChangeUp
		if newIncr < 0 {
			newIncrStr = ChangeDown
		}
		oldIncrStr := ChangeUp
		if oldIncr < 0 {
			oldIncrStr = ChangeDown
		}
		businessDiag.Diag = fmt.Sprintf(WeekTenWanAnchorHtml2, manifestationStr, fmt.Sprintf("%d", ana.Value), levelStr, "持平", fmt.Sprintf("%d", ana.NewValue),
			newIncrStr, fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", newGap), fmt.Sprintf("%d", ana.OldValue), oldIncrStr,
			fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", oldGap))
	}

	log.Debugf("getTenWanAnchorDia end guildId:%d ana:%v lastAna:%v analysis:%v businessDiag:%v", guildId, ana, lastAna, analysis, businessDiag)
	return businessDiag
}

func getMonthProAnchorDia(guildId uint32, analysis, lastAnalysis *GuildAnalysis) *pb.BusinessDiagnosis {
	ana := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Pro_Anchor))
	lastAna := getAnalysisByType(lastAnalysis, uint32(pb.KpiType_Kpi_Type_Pro_Anchor))

	incomeAna := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Guild_Income))

	manifestationStr := ManifestationNormal
	incr, _ := culIncr(ana.Value, lastAna.Value)
	if incomeAna.Value >= 50000000 {
		if ana.Value >= ana.LevelAve {
			if incr >= 10 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 3 {
				manifestationStr = ManifestationGood
			} else if incr <= -4 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 3 {
				manifestationStr = ManifestationGood
			} else if incr <= -4 {
				manifestationStr = ManifestationBad
			}
		}
	} else {
		if ana.Value >= ana.LevelAve {
			if incr >= 5 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 2 {
				manifestationStr = ManifestationGood
			} else if incr <= -2 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 2 {
				manifestationStr = ManifestationGood
			} else if incr <= -2 {
				manifestationStr = ManifestationBad
			}
		}
	}

	levelStr := LevelMid
	if analysis.LevelGuildCnt == 1 {
		if incomeAna.Value >= 100000000 {
			levelStr = getLevelStr(ana.Value, 170, 110)
		} else if incomeAna.Value >= 50000000 {
			levelStr = getLevelStr(ana.Value, 60, 30)
		} else {
			levelStr = getLevelStr(ana.Value, 25, 15)
		}
	} else {
		if ana.Value > ana.LevelAve {
			levelStr = LevelHigh
		} else if ana.Value < ana.LevelAve {
			levelStr = LevelLow
		}
	}

	var proRemainRatio float32 = 0
	if lastAna.Value > 0 {
		proRemainRatio = float32(analysis.PorAnchorRemain) / float32(lastAna.Value)
	}

	businessDiag := &pb.BusinessDiagnosis{
		KpiType: uint32(pb.KpiType_Kpi_Type_Pro_Anchor),
	}

	newIncr, newGap := culIncr(ana.NewValue, lastAna.NewValue)
	oldIncr, oldGap := culIncr(ana.OldValue, lastAna.OldValue)
	if ana.Value > lastAna.Value {
		if newIncr > oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthProAnchorHtml1, manifestationStr, levelStr, "增加", "新签达人", "上升", fmt.Sprintf("增加<span style='font-weight:bold'> %d人</span>", newIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.PorAnchorRemain), fmt.Sprintf("%0.2f%%", proRemainRatio*100))
		} else if newIncr == oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthProAnchorHtml3, manifestationStr, levelStr, "增加", "增加", fmt.Sprintf("增加<span style='font-weight:bold'> %d人</span>", newIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.PorAnchorRemain), fmt.Sprintf("%0.2f%%", proRemainRatio*100))
		} else {
			businessDiag.Diag = fmt.Sprintf(MonthProAnchorHtml1, manifestationStr, levelStr, "增加", "老达人", "上升", fmt.Sprintf("增加<span style='font-weight:bold'> %d人</span>", oldIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.PorAnchorRemain), fmt.Sprintf("%0.2f%%", proRemainRatio*100))
		}
	} else if ana.Value < lastAna.Value {
		if newIncr < oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthProAnchorHtml1, manifestationStr, levelStr, "减少", "新签达人", "下降", fmt.Sprintf("减少<span style='font-weight:bold'> %d人</span>", -newIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.PorAnchorRemain), fmt.Sprintf("%0.2f%%", proRemainRatio*100))
		} else if newIncr == oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthProAnchorHtml3, manifestationStr, levelStr, "减少", "下降", fmt.Sprintf("减少<span style='font-weight:bold'> %d人</span>", -newIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.PorAnchorRemain), fmt.Sprintf("%0.2f%%", proRemainRatio*100))
		} else {
			businessDiag.Diag = fmt.Sprintf(MonthProAnchorHtml1, manifestationStr, levelStr, "减少", "老达人", "下降", fmt.Sprintf("减少<span style='font-weight:bold'> %d人</span>", -oldIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.PorAnchorRemain), fmt.Sprintf("%0.2f%%", proRemainRatio*100))
		}
	} else {
		newIncrStr := ChangeUp
		if newIncr < 0 {
			newIncrStr = ChangeDown
		}
		oldIncrStr := ChangeUp
		if oldIncr < 0 {
			oldIncrStr = ChangeDown
		}
		businessDiag.Diag = fmt.Sprintf(MonthProAnchorHtml2, manifestationStr, levelStr, "持平", fmt.Sprintf("%d", ana.NewValue),
			newIncrStr, fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", newGap), fmt.Sprintf("%d", ana.OldValue), oldIncrStr, fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", oldGap),
			fmt.Sprintf("%d", analysis.PorAnchorRemain), fmt.Sprintf("%0.2f%%", proRemainRatio*100))
	}

	log.Debugf("getMonthProAnchorDia end guildId:%d ana:%v lastAna:%v analysis:%v businessDiag:%v", guildId, ana, lastAna, analysis, businessDiag)
	return businessDiag
}

func getMonthMatureAnchorDia(guildId uint32, analysis, lastAnalysis *GuildAnalysis) *pb.BusinessDiagnosis {
	ana := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Mature_Anchor))
	lastAna := getAnalysisByType(lastAnalysis, uint32(pb.KpiType_Kpi_Type_Mature_Anchor))

	incomeAna := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Guild_Income))

	manifestationStr := ManifestationNormal
	incr, _ := culIncr(ana.Value, lastAna.Value)
	if incomeAna.Value >= 50000000 {
		if ana.Value >= ana.LevelAve {
			if incr >= 8 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 2 {
				manifestationStr = ManifestationGood
			} else if incr <= -4 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 2 {
				manifestationStr = ManifestationGood
			} else if incr <= -4 {
				manifestationStr = ManifestationBad
			}
		}
	} else {
		if ana.Value >= ana.LevelAve {
			if incr >= 4 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 2 {
				manifestationStr = ManifestationGood
			} else if incr <= -2 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 2 {
				manifestationStr = ManifestationGood
			} else if incr <= -2 {
				manifestationStr = ManifestationBad
			}
		}
	}

	levelStr := LevelMid
	if analysis.LevelGuildCnt == 1 {
		if incomeAna.Value >= 100000000 {
			levelStr = getLevelStr(ana.Value, 90, 50)
		} else if incomeAna.Value >= 50000000 {
			levelStr = getLevelStr(ana.Value, 25, 15)
		} else {
			levelStr = getLevelStr(ana.Value, 15, 10)
		}
	} else {
		if ana.Value > ana.LevelAve {
			levelStr = LevelHigh
		} else if ana.Value < ana.LevelAve {
			levelStr = LevelLow
		}
	}

	var matureRemainRatio float32 = 0
	if lastAna.Value > 0 {
		matureRemainRatio = float32(analysis.MatureAnchorRemain) / float32(lastAna.Value)
	}

	businessDiag := &pb.BusinessDiagnosis{
		KpiType: uint32(pb.KpiType_Kpi_Type_Mature_Anchor),
	}

	newIncr, newGap := culIncr(ana.NewValue, lastAna.NewValue)
	oldIncr, oldGap := culIncr(ana.OldValue, lastAna.OldValue)
	if ana.Value > lastAna.Value {
		if newIncr > oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthMatureAnchorHtml1, manifestationStr, levelStr, "增加", "新签达人", "上升", fmt.Sprintf("增加<span style='font-weight:bold'> %d人</span>", newIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.MatureAnchorRemain), fmt.Sprintf("%0.2f%%", matureRemainRatio*100))
		} else if newIncr == oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthMatureAnchorHtml3, manifestationStr, levelStr, "增加", "增加", fmt.Sprintf("增加<span style='font-weight:bold'> %d人</span>", newIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.MatureAnchorRemain), fmt.Sprintf("%0.2f%%", matureRemainRatio*100))
		} else {
			businessDiag.Diag = fmt.Sprintf(MonthMatureAnchorHtml1, manifestationStr, levelStr, "增加", "老达人", "上升", fmt.Sprintf("增加<span style='font-weight:bold'> %d人</span>", oldIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.MatureAnchorRemain), fmt.Sprintf("%0.2f%%", matureRemainRatio*100))
		}
	} else if ana.Value < lastAna.Value {
		if newIncr < oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthMatureAnchorHtml1, manifestationStr, levelStr, "减少", "新签达人", "下降", fmt.Sprintf("减少<span style='font-weight:bold'> %d人</span>", -newIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.MatureAnchorRemain), fmt.Sprintf("%0.2f%%", matureRemainRatio*100))
		} else if newIncr == oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthMatureAnchorHtml3, manifestationStr, levelStr, "减少", "下降", fmt.Sprintf("减少<span style='font-weight:bold'> %d人</span>", -newIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.MatureAnchorRemain), fmt.Sprintf("%0.2f%%", matureRemainRatio*100))
		} else {
			businessDiag.Diag = fmt.Sprintf(MonthMatureAnchorHtml1, manifestationStr, levelStr, "减少", "老达人", "下降", fmt.Sprintf("减少<span style='font-weight:bold'> %d人</span>", -oldIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.MatureAnchorRemain), fmt.Sprintf("%0.2f%%", matureRemainRatio*100))
		}
	} else {
		newIncrStr := ChangeUp
		if newIncr < 0 {
			newIncrStr = ChangeDown
		}
		oldIncrStr := ChangeUp
		if oldIncr < 0 {
			oldIncrStr = ChangeDown
		}
		businessDiag.Diag = fmt.Sprintf(MonthMatureAnchorHtml2, manifestationStr, levelStr, "持平", fmt.Sprintf("%d", ana.NewValue),
			newIncrStr, fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", newGap), fmt.Sprintf("%d", ana.OldValue), oldIncrStr, fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", oldGap),
			fmt.Sprintf("%d", analysis.MatureAnchorRemain), fmt.Sprintf("%0.2f%%", matureRemainRatio*100))
	}

	log.Debugf("getMonthMatureAnchorDia end guildId:%d ana:%v lastAna:%v analysis:%v businessDiag:%v", guildId, ana, lastAna, analysis, businessDiag)
	return businessDiag
}

func getPotActiveAnchorDia(guildId uint32, analysis, lastAnalysis *GuildAnalysis) *pb.BusinessDiagnosis {
	ana := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Pot_Active_Anchor))
	lastAna := getAnalysisByType(lastAnalysis, uint32(pb.KpiType_Kpi_Type_Pot_Active_Anchor))

	incomeAna := getAnalysisByType(analysis, uint32(pb.KpiType_Kpi_Type_Guild_Income))

	manifestationStr := ManifestationNormal
	incr, _ := culIncr(ana.Value, lastAna.Value)
	if incomeAna.Value >= 50000000 {
		if ana.Value >= ana.LevelAve {
			if incr >= 6 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 2 {
				manifestationStr = ManifestationGood
			} else if incr <= -4 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 2 {
				manifestationStr = ManifestationGood
			} else if incr <= -4 {
				manifestationStr = ManifestationBad
			}
		}
	} else {
		if ana.Value >= ana.LevelAve {
			if incr >= 3 {
				manifestationStr = ManifestationExcellent
			} else if incr >= 1 {
				manifestationStr = ManifestationGood
			} else if incr <= -2 {
				manifestationStr = ManifestationBad
			}
		} else {
			if incr >= 1 {
				manifestationStr = ManifestationGood
			} else if incr <= -2 {
				manifestationStr = ManifestationBad
			}
		}
	}

	levelStr := LevelMid
	if analysis.LevelGuildCnt == 1 {
		if incomeAna.Value >= 100000000 {
			levelStr = getLevelStr(ana.Value, 60, 30)
		} else if incomeAna.Value >= 50000000 {
			levelStr = getLevelStr(ana.Value, 20, 10)
		} else {
			levelStr = getLevelStr(ana.Value, 10, 5)
		}
	} else {
		if ana.Value > ana.LevelAve {
			levelStr = LevelHigh
		} else if ana.Value < ana.LevelAve {
			levelStr = LevelLow
		}
	}

	var potActiveRemainRatio float32 = 0
	if lastAna.Value > 0 {
		potActiveRemainRatio = float32(analysis.PotActiveAnchorRemain) / float32(lastAna.Value)
	}

	businessDiag := &pb.BusinessDiagnosis{
		KpiType: uint32(pb.KpiType_Kpi_Type_Pot_Active_Anchor),
	}

	newIncr, newGap := culIncr(ana.NewValue, lastAna.NewValue)
	oldIncr, oldGap := culIncr(ana.OldValue, lastAna.OldValue)
	if ana.Value > lastAna.Value {
		if newIncr > oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthPotActiveAnchorHtml1, manifestationStr, levelStr, "增加", "新签达人", "上升", fmt.Sprintf("增加<span style='font-weight:bold'> %d人</span>", newIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.PotActiveAnchorRemain), fmt.Sprintf("%0.2f%%", potActiveRemainRatio*100))
		} else if newIncr == oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthPotActiveAnchorHtml3, manifestationStr, levelStr, "增加", "增加", fmt.Sprintf("增加<span style='font-weight:bold'> %d人</span>", newIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.PotActiveAnchorRemain), fmt.Sprintf("%0.2f%%", potActiveRemainRatio*100))
		} else {
			businessDiag.Diag = fmt.Sprintf(MonthPotActiveAnchorHtml1, manifestationStr, levelStr, "增加", "老达人", "上升", fmt.Sprintf("增加<span style='font-weight:bold'> %d人</span>", oldIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.PotActiveAnchorRemain), fmt.Sprintf("%0.2f%%", potActiveRemainRatio*100))
		}
	} else if ana.Value < lastAna.Value {
		if newIncr < oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthPotActiveAnchorHtml1, manifestationStr, levelStr, "减少", "新签达人", "下降", fmt.Sprintf("减少<span style='font-weight:bold'> %d人</span>", -newIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.PotActiveAnchorRemain), fmt.Sprintf("%0.2f%%", potActiveRemainRatio*100))
		} else if newIncr == oldIncr {
			businessDiag.Diag = fmt.Sprintf(MonthPotActiveAnchorHtml3, manifestationStr, levelStr, "减少", "下降", fmt.Sprintf("减少<span style='font-weight:bold'> %d人</span>", -newIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.PotActiveAnchorRemain), fmt.Sprintf("%0.2f%%", potActiveRemainRatio*100))
		} else {
			businessDiag.Diag = fmt.Sprintf(MonthPotActiveAnchorHtml1, manifestationStr, levelStr, "减少", "老达人", "下降", fmt.Sprintf("减少<span style='font-weight:bold'> %d人</span>", -oldIncr), fmt.Sprintf("%d", ana.NewValue),
				fmt.Sprintf("%d", ana.OldValue), fmt.Sprintf("%d", analysis.PotActiveAnchorRemain), fmt.Sprintf("%0.2f%%", potActiveRemainRatio*100))
		}
	} else {
		newIncrStr := ChangeUp
		if newIncr < 0 {
			newIncrStr = ChangeDown
		}
		oldIncrStr := ChangeUp
		if oldIncr < 0 {
			oldIncrStr = ChangeDown
		}
		businessDiag.Diag = fmt.Sprintf(MonthPotActiveAnchorHtml2, manifestationStr, levelStr, "持平", fmt.Sprintf("%d", ana.NewValue),
			newIncrStr, fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", newGap), fmt.Sprintf("%d", ana.OldValue), oldIncrStr, fmt.Sprintf("<span style='font-weight:bold'> %d人</span>", oldGap),
			fmt.Sprintf("%d", analysis.PotActiveAnchorRemain), fmt.Sprintf("%0.2f%%", potActiveRemainRatio*100))
	}

	log.Debugf("getPotActiveAnchorDia end guildId:%d ana:%v lastAna:%v analysis:%v businessDiag:%v", guildId, ana, lastAna, analysis, businessDiag)
	return businessDiag
}

func (m *ChannelLiveStatsManager) getRatioRank(ctx context.Context, analysis *GuildAnalysis, dateTim string) error {
	guildList, err := m.getAllLiveCoopGuildList(ctx, dateTim, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "getLastRank getAllLiveCoopGuildList failed err:%v", err)
		return err
	}

	for _, ana := range analysis.AnalysisList {
		if uint32(len(guildList)) > ana.AllRank {
			ana.RatioRank = int32(uint32(len(guildList)) - ana.AllRank)
		} else {
			ana.RatioRank = -int32(ana.AllRank - uint32(len(guildList)))
		}
	}

	log.DebugWithCtx(ctx, "getRatioRank end analysis:%v guildList:%d dateTim:%s", analysis, len(guildList), dateTim)
	return nil
}

func (m *ChannelLiveStatsManager) GetWeekBusinessAnalysis(ctx context.Context, req *pb.GetWeekBusinessAnalysisReq) (*pb.GetWeekBusinessAnalysisResp, error) {
	resp := &pb.GetWeekBusinessAnalysisResp{
		AnalysisList:  make([]*pb.BusinessAnalysis, 0),
		DiagnosisList: make([]*pb.BusinessDiagnosis, 0),
	}

	beginTm := time.Unix(int64(req.GetBeginTs()), 0)
	lastBeginTM := beginTm.AddDate(0, 0, -7)

	analysis, isExist, err := m.mysqlStore.GetGuildWeekBusinessAnalysis(ctx, req.GetGuildId(), req.GetBeginTs())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildWeekBusinessAnalysis failed req:%v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	if !isExist {
		log.WarnWithCtx(ctx, "GetGuildWeekBusinessAnalysis not exist req:%v", req)
		return resp, nil
	}

	lastAnalysis, isLastExist, err := m.mysqlStore.GetGuildWeekBusinessAnalysis(ctx, req.GetGuildId(), uint32(lastBeginTM.Unix()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildWeekBusinessAnalysis failed req:%v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	weekAnalysis := &GuildAnalysis{
		AnalysisList: make([]*pb.BusinessAnalysis, 0),
	}
	err = json.Unmarshal(analysis.Analysis, weekAnalysis)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWeekBusinessAnalysis Unmarshal failed req:%v err:%v", req, err)
	}

	lastWeekAnalysis := &GuildAnalysis{
		AnalysisList: make([]*pb.BusinessAnalysis, 0),
	}
	err = json.Unmarshal(lastAnalysis.Analysis, lastWeekAnalysis)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWeekBusinessAnalysis Unmarshal failed req:%v err:%v", req, err)
	}

	if !isLastExist {
		//上周期为空，需要给默认环比排名
		err = m.getRatioRank(ctx, weekAnalysis, WeekDateTim)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetWeekBusinessAnalysis getRatioRank failed req:%v err:%v", req, err)
			return resp, err
		}
	}

	resp.AnalysisList = weekAnalysis.AnalysisList
	resp.DiagnosisList = append(resp.DiagnosisList, getWeekIncomeDia(req.GetGuildId(), weekAnalysis, lastWeekAnalysis))
	resp.DiagnosisList = append(resp.DiagnosisList, getNewSignAnchorDia(req.GetGuildId(), weekAnalysis, lastWeekAnalysis))
	resp.DiagnosisList = append(resp.DiagnosisList, getLiveAnchorDia(req.GetGuildId(), weekAnalysis, lastWeekAnalysis))
	resp.DiagnosisList = append(resp.DiagnosisList, getTwoWanAnchorDia(req.GetGuildId(), weekAnalysis, lastWeekAnalysis))
	resp.DiagnosisList = append(resp.DiagnosisList, getTenWanAnchorDia(req.GetGuildId(), weekAnalysis, lastWeekAnalysis))

	log.DebugWithCtx(ctx, "GetWeekBusinessAnalysis end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelLiveStatsManager) GetMonthBusinessAnalysis(ctx context.Context, req *pb.GetMonthBusinessAnalysisReq) (*pb.GetMonthBusinessAnalysisResp, error) {
	resp := &pb.GetMonthBusinessAnalysisResp{
		KpiList:       make([]*pb.BusinessAnalysis, 0),
		DiagnosisList: make([]*pb.BusinessDiagnosis, 0),
	}

	beginTm := time.Unix(int64(req.GetMonthTs()), 0)
	monthBeginTm := time.Date(beginTm.Year(), beginTm.Month(), 1, 0, 0, 0, 0, time.Local)
	lastBeginTm := monthBeginTm.AddDate(0, 0, -1)

	analysis, isExist, err := m.mysqlStore.GetGuildMonthBusinessAnalysis(ctx, req.GetGuildId(), req.GetMonthTs())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMonthBusinessAnalysis failed req:%v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	if !isExist {
		log.WarnWithCtx(ctx, "GetMonthBusinessAnalysis not exist req:%v", req)
		return resp, nil
	}

	lastAnalysis, isLastExist, err := m.mysqlStore.GetGuildMonthBusinessAnalysis(ctx, req.GetGuildId(), uint32(lastBeginTm.Unix()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMonthBusinessAnalysis failed req:%v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	monthAnalysis := &GuildAnalysis{
		AnalysisList: make([]*pb.BusinessAnalysis, 0),
	}
	err = json.Unmarshal(analysis.Analysis, monthAnalysis)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMonthBusinessAnalysis Unmarshal failed req:%v err:%v", req, err)
	}

	lastMonthAnalysis := &GuildAnalysis{
		AnalysisList: make([]*pb.BusinessAnalysis, 0),
	}
	err = json.Unmarshal(lastAnalysis.Analysis, lastMonthAnalysis)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMonthBusinessAnalysis Unmarshal failed req:%v err:%v", req, err)
	}

	if !isLastExist {
		//上周期为空，需要给默认环比排名
		err = m.getRatioRank(ctx, monthAnalysis, MonthDateTim)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMonthBusinessAnalysis getRatioRank failed req:%v err:%v", req, err)
			return resp, err
		}
	}

	resp.KpiList = monthAnalysis.AnalysisList

	resp.DiagnosisList = append(resp.DiagnosisList, getMonthIncomeDia(req.GetGuildId(), monthAnalysis, lastMonthAnalysis))
	resp.DiagnosisList = append(resp.DiagnosisList, getMonthNewSignAnchorDia(req.GetGuildId(), monthAnalysis, lastMonthAnalysis))
	resp.DiagnosisList = append(resp.DiagnosisList, getMonthLiveAnchorDia(req.GetGuildId(), monthAnalysis, lastMonthAnalysis))
	resp.DiagnosisList = append(resp.DiagnosisList, getMonthProAnchorDia(req.GetGuildId(), monthAnalysis, lastMonthAnalysis))
	resp.DiagnosisList = append(resp.DiagnosisList, getMonthMatureAnchorDia(req.GetGuildId(), monthAnalysis, lastMonthAnalysis))
	resp.DiagnosisList = append(resp.DiagnosisList, getPotActiveAnchorDia(req.GetGuildId(), monthAnalysis, lastMonthAnalysis))

	log.DebugWithCtx(ctx, "GetMonthBusinessAnalysis end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelLiveStatsManager) getAllLiveCoopGuildList(ctx context.Context, dateTim string, isNeedWhite bool) ([]uint32, error) {
	subCtx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()

	analysisConf := conf.DyConfInstance.GetAnalysisConf()
	if isNeedWhite && analysisConf.WhiteConf.IsSwitch {
		log.DebugWithCtx(ctx, "getAllLiveCoopGuildList white mode analysisConf:%v dateTim:%s", analysisConf, dateTim)
		// 白名单模式
		if dateTim == MonthDateTim {
			return analysisConf.WhiteConf.MonthWhite, nil
		}

		return analysisConf.WhiteConf.WeekWhite, nil
	}

	guildList := make([]uint32, 0)
	mapGuild2Is := make(map[uint32]bool)

	coopTypeList := []guildCooperation.CooperationType{
		guildCooperation.CooperationType_CTypeYuyin,
	}

	for _, coopType := range coopTypeList {
		offset := uint32(0)
		limit := uint32(100)
		var totalCnt int

		for {
			resp, err := m.guildCoopCli.GetCooperationGuildIdsV2(subCtx,
				coopType, []uint32{}, offset, limit)
			if err != nil {
				log.ErrorWithCtx(ctx, "getAllLiveCoopGuildList GetCooperationGuildIdsV2 err:%s", err)
				return nil, err
			}

			for _, guildId := range resp.GetGuildList() {
				if !mapGuild2Is[guildId] {
					guildList = append(guildList, guildId)
					mapGuild2Is[guildId] = true
				}
			}

			totalCnt += len(resp.GetGuildList())

			if len(resp.GetGuildList()) < int(limit) {
				break
			}

			offset += limit
		}

		log.InfoWithCtx(ctx, "getAllLiveCoopGuildList coopType:%d totalCnt:%d", coopType, totalCnt)
	}

	log.InfoWithCtx(ctx, "getAllLiveCoopGuildList end len:%d guildList:%v", len(guildList), guildList)
	return guildList, nil
}

func (m *ChannelLiveStatsManager) getBindGuildList(ctx context.Context) (map[uint32][]uint32, map[uint32]uint32, map[uint32]bool, error) {
	subCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	signAnchorResp, err := m.signAnchorStatsCli.GetBindGuildInfoList(subCtx, &signAnchorStatsPb.GetBindGuildInfoListReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "getBindGuildList GetBindGuildInfoList failed err:%v", err)
		return nil, nil, nil, err
	}

	mapId2List := make(map[uint32][]uint32)
	mapId2BindId := make(map[uint32]uint32)
	mapGuild2IsBind := make(map[uint32]bool)
	for _, bindGuild := range signAnchorResp.GetInfoList() {
		if bindGuild.GetGuildId() == bindGuild.GetBindGuildId() {
			continue
		}

		if _, ok := mapId2List[bindGuild.GetBindGuildId()]; !ok {
			mapId2List[bindGuild.GetBindGuildId()] = make([]uint32, 0)
		}

		mapId2List[bindGuild.GetBindGuildId()] = append(mapId2List[bindGuild.GetBindGuildId()], bindGuild.GetGuildId())
		mapId2BindId[bindGuild.GetGuildId()] = bindGuild.GetBindGuildId()
		mapGuild2IsBind[bindGuild.GetGuildId()] = true
		mapGuild2IsBind[bindGuild.GetBindGuildId()] = true
	}

	log.InfoWithCtx(ctx, "getBindGuildList end len:%d mapId2List:%v mapId2BindId:%v mapGuild2IsBind:%v", len(mapId2List), mapId2List, mapId2BindId, mapGuild2IsBind)
	return mapId2List, mapId2BindId, mapGuild2IsBind, nil
}

func (m *ChannelLiveStatsManager) getGuildDataFromDH(ctx context.Context, guildId uint32, beginDay, endDay time.Time, dateDim string) (*GuildLiveStats, error) {
	url := fmt.Sprintf("%s?start_date=%s&end_date=%s&date_dim=%s&guild_id=%s&apiToken=%s",
		m.sc.GuildOperationalCapabilitiesUrl, beginDay.Format("2006-01-02"), endDay.Format("2006-01-02"), dateDim,
		strconv.FormatInt(int64(guildId), 10), m.sc.DWApiToken)

	respData, err := HttpQueryGuildLiveStats(url)
	if err != nil {
		log.ErrorWithCtx(ctx, "getGuildDataFromDH HttpQueryGuildLiveStats failed guildId:%d err:%v", guildId, err)
		return nil, err
	}

	return respData, nil
}

func getGuildIncome(guildId uint32, guildIncomeStrList, guildIncomeNewOldStrList, anchorIncomeStrList []string, analysis, lastAnalysis *GuildAnalysis) error {
	var totalVal, newVal, oldVal uint64 = 0, 0, 0
	mapLevel2Income := make(map[uint32]uint64)

	for _, incomeStr := range guildIncomeStrList {
		revenue, err := strconv.ParseUint(incomeStr, 10, 64)
		if err != nil {
			log.Errorf("getGuildIncome failed guildId:%d incomeStr:%s err:%v", guildId, incomeStr, err)
			return err
		}
		totalVal += revenue
	}

	for _, incomeStr := range guildIncomeNewOldStrList {
		incomeStr = strings.ReplaceAll(incomeStr, "\\", "")
		incomeStr = strings.ReplaceAll(incomeStr, "\"", "")
		incomeStr = strings.ReplaceAll(incomeStr, " ", "")

		strList := strings.Split(incomeStr, "-")
		if len(strList) != 2 {
			log.Errorf("getGuildIncome failed guildId:%d incomeStr:%s", guildId, incomeStr)
			return errors.New("营收数据格式错误")
		}

		tmpNewVal, err := strconv.ParseUint(strList[0], 10, 64)
		if err != nil {
			log.Errorf("getGuildIncome failed guildId:%d str:%s err:%v", guildId, strList[0], err)
		}

		tmpOldVal, err := strconv.ParseUint(strList[1], 10, 64)
		if err != nil {
			log.Errorf("getGuildIncome failed guildId:%d str:%s err:%v", guildId, strList[1], err)
		}

		newVal += tmpNewVal
		oldVal += tmpOldVal
	}

	for _, incomeStr := range anchorIncomeStrList {
		log.Debugf("getGuildIncome guildId:%d incomeStr:%s", guildId, incomeStr)
		incomeStr = strings.ReplaceAll(incomeStr, "[", "")
		incomeStr = strings.ReplaceAll(incomeStr, "]", "")
		incomeStr = strings.ReplaceAll(incomeStr, "\\", "")
		incomeStr = strings.ReplaceAll(incomeStr, "\"", "")
		incomeStr = strings.ReplaceAll(incomeStr, " ", "")

		strList := strings.Split(incomeStr, ",")

		log.Debugf("getGuildIncome guildId:%d strList:%s", guildId, strList)
		for index, str := range strList {
			list := strings.Split(str, "-")
			if len(list) != 2 {
				log.Errorf("getGuildIncome failed guildId:%d str:%s", guildId, str)
				return errors.New("营收数据格式错误")
			}

			tmpNewVal, err := strconv.ParseUint(list[0], 10, 64)
			if err != nil {
				log.Errorf("getGuildIncome failed guildId:%d str:%s err:%v", guildId, list[0], err)
			}

			tmpOldVal, err := strconv.ParseUint(list[1], 10, 64)
			if err != nil {
				log.Errorf("getGuildIncome failed guildId:%d str:%s err:%v", guildId, list[1], err)
			}

			mapLevel2Income[uint32(index+1)] = tmpNewVal + tmpOldVal
		}
	}

	ratioValue := int64(totalVal)
	for _, ana := range lastAnalysis.AnalysisList {
		if ana.KpiType == uint32(pb.KpiType_Kpi_Type_Guild_Income) {
			if ana.Value > totalVal {
				ratioValue = -int64(ana.Value - totalVal)
			} else {
				ratioValue = int64(totalVal - ana.Value)
			}
		}
	}

	analysis.AnalysisList = append(analysis.AnalysisList, &pb.BusinessAnalysis{
		KpiType:    uint32(pb.KpiType_Kpi_Type_Guild_Income),
		Value:      totalVal,
		RatioValue: ratioValue,
		NewValue:   newVal,
		OldValue:   oldVal,
	})
	analysis.MapLevel2Income = mapLevel2Income

	log.Debugf("getGuildIncome end guildId:%d totalVal:%d newVal:%d oldVal:%d lastAnalysis:%v analysis:%v incomeStrList:%v len:%d",
		guildId, totalVal, newVal, oldVal, lastAnalysis, analysis, anchorIncomeStrList, len(anchorIncomeStrList))
	return nil
}

func getNewSignAnchor(guildId uint32, newSignStrList []string, analysis, lastAnalysis *GuildAnalysis) error {
	var totalVal uint64 = 0
	for _, newSignStr := range newSignStrList {
		val, _ := strconv.ParseUint(newSignStr, 10, 64)
		totalVal += val
	}

	ratioValue := int64(totalVal)
	for _, ana := range lastAnalysis.AnalysisList {
		if ana.KpiType == uint32(pb.KpiType_Kpi_Type_New_Sign_Anchor) {
			if ana.Value > totalVal {
				ratioValue = -int64(ana.Value - totalVal)
			} else {
				ratioValue = int64(totalVal - ana.Value)
			}
		}
	}

	analysis.AnalysisList = append(analysis.AnalysisList, &pb.BusinessAnalysis{
		KpiType:    uint32(pb.KpiType_Kpi_Type_New_Sign_Anchor),
		Value:      totalVal,
		RatioValue: ratioValue,
	})

	log.Debugf("getNewSignAnchor end guildId:%d totalVal:%d lastAnalysis:%v analysis:%v newSignStrList:%v", guildId, totalVal, lastAnalysis, analysis, newSignStrList)
	return nil
}

func getLiveAnchor(guildId uint32, liveAnchorStrList []string, analysis, lastAnalysis *GuildAnalysis) error {
	var totalVal, newVal, oldVal uint64 = 0, 0, 0
	for _, liveAnchor := range liveAnchorStrList {
		liveAnchor = strings.ReplaceAll(liveAnchor, "\\", "")
		liveAnchor = strings.ReplaceAll(liveAnchor, "\"", "")
		strList := strings.Split(liveAnchor, "-")

		log.Debugf("getLiveAnchor guildId:%d incomeStr:%s", guildId, liveAnchor)
		if len(strList) != 2 {
			log.Errorf("getLiveAnchor failed guildId:%d liveAnchor:%s", guildId, liveAnchor)
			return errors.New("开启听听达人数格式错误")
		}

		tmpNewVal, _ := strconv.ParseUint(strList[0], 10, 64)
		tmpOldVal, _ := strconv.ParseUint(strList[1], 10, 64)

		totalVal += tmpNewVal + tmpOldVal
		newVal += tmpNewVal
		oldVal += tmpOldVal
	}

	ratioValue := int64(totalVal)
	for _, ana := range lastAnalysis.AnalysisList {
		if ana.KpiType == uint32(pb.KpiType_Kpi_Type_Live_Anchor) {
			if ana.Value > totalVal {
				ratioValue = -int64(ana.Value - totalVal)
			} else {
				ratioValue = int64(totalVal - ana.Value)
			}
		}
	}

	analysis.AnalysisList = append(analysis.AnalysisList, &pb.BusinessAnalysis{
		KpiType:    uint32(pb.KpiType_Kpi_Type_Live_Anchor),
		Value:      totalVal,
		RatioValue: ratioValue,
		NewValue:   newVal,
		OldValue:   oldVal,
	})

	log.Debugf("getLiveAnchor end guildId:%d totalVal:%d newVal:%d oldVal:%d lastAnalysis:%v analysis:%v liveAnchorStrList:%v",
		guildId, totalVal, newVal, oldVal, lastAnalysis, analysis, liveAnchorStrList)
	return nil
}

func getTwoWanAnchor(guildId uint32, twoWanStrList []string, analysis, lastAnalysis *GuildAnalysis) error {
	var totalVal, newVal, oldVal uint64 = 0, 0, 0
	for _, twoWan := range twoWanStrList {
		twoWan = strings.ReplaceAll(twoWan, "\\", "")
		twoWan = strings.ReplaceAll(twoWan, "\"", "")
		strList := strings.Split(twoWan, "-")

		if len(strList) != 2 {
			log.Errorf("getTwoWanAnchor failed guildId:%d twoWan:%s", guildId, twoWan)
			return errors.New("收礼≥2万豆达人数格式错误")
		}

		tmpNewVal, _ := strconv.ParseUint(strList[0], 10, 64)
		tmpOldVal, _ := strconv.ParseUint(strList[1], 10, 64)

		totalVal += tmpNewVal + tmpOldVal
		newVal += tmpNewVal
		oldVal += tmpOldVal
	}

	ratioValue := int64(totalVal)
	for _, ana := range lastAnalysis.AnalysisList {
		if ana.KpiType == uint32(pb.KpiType_Kpi_Type_Two_Wan_Anchor) {
			if ana.Value > totalVal {
				ratioValue = -int64(ana.Value - totalVal)
			} else {
				ratioValue = int64(totalVal - ana.Value)
			}
		}
	}

	analysis.AnalysisList = append(analysis.AnalysisList, &pb.BusinessAnalysis{
		KpiType:    uint32(pb.KpiType_Kpi_Type_Two_Wan_Anchor),
		Value:      totalVal,
		RatioValue: ratioValue,
		NewValue:   newVal,
		OldValue:   oldVal,
	})

	log.Debugf("getTwoWanAnchor end guildId:%d totalVal:%d newVal:%d oldVal:%d lastAnalysis:%v analysis:%v twoWanStrList:%v",
		guildId, totalVal, newVal, oldVal, lastAnalysis, analysis, twoWanStrList)
	return nil
}

func getTenWanAnchor(guildId uint32, tenWanStrList []string, analysis, lastAnalysis *GuildAnalysis) error {
	var totalVal, newVal, oldVal uint64 = 0, 0, 0
	for _, tenWan := range tenWanStrList {
		tenWan = strings.ReplaceAll(tenWan, "\\", "")
		tenWan = strings.ReplaceAll(tenWan, "\"", "")
		strList := strings.Split(tenWan, "-")

		if len(strList) != 2 {
			log.Errorf("getTenWanAnchor failed guildId:%d tenWan:%s", guildId, tenWan)
			return errors.New("收礼≥10万豆达人数格式错误")
		}

		tmpNewVal, _ := strconv.ParseUint(strList[0], 10, 64)
		tmpOldVal, _ := strconv.ParseUint(strList[1], 10, 64)

		totalVal += tmpNewVal + tmpOldVal
		newVal += tmpNewVal
		oldVal += tmpOldVal
	}

	ratioValue := int64(totalVal)
	for _, ana := range lastAnalysis.AnalysisList {
		if ana.KpiType == uint32(pb.KpiType_Kpi_Type_Ten_Wan_Anchor) {
			if ana.Value > totalVal {
				ratioValue = -int64(ana.Value - totalVal)
			} else {
				ratioValue = int64(totalVal - ana.Value)
			}
		}
	}

	analysis.AnalysisList = append(analysis.AnalysisList, &pb.BusinessAnalysis{
		KpiType:    uint32(pb.KpiType_Kpi_Type_Ten_Wan_Anchor),
		Value:      totalVal,
		RatioValue: ratioValue,
		NewValue:   newVal,
		OldValue:   oldVal,
	})

	log.Debugf("getTenWanAnchor end guildId:%d totalVal:%d newVal:%d oldVal:%d lastAnalysis:%v analysis:%v tenWanStrList:%v",
		guildId, totalVal, newVal, oldVal, lastAnalysis, analysis, tenWanStrList)
	return nil
}

func getProAnchor(guildId uint32, proAnchorStrList []string, analysis, lastAnalysis *GuildAnalysis) error {
	var totalVal, newVal, oldVal uint64 = 0, 0, 0
	for _, proAnchor := range proAnchorStrList {
		proAnchor = strings.ReplaceAll(proAnchor, "\\", "")
		proAnchor = strings.ReplaceAll(proAnchor, "\"", "")
		strList := strings.Split(proAnchor, "-")

		if len(strList) != 2 {
			log.Errorf("getProAnchor failed guildId:%d proAnchor:%s", guildId, proAnchor)
			return errors.New("专业从业者数格式错误")
		}

		tmpNewVal, _ := strconv.ParseUint(strList[0], 10, 64)
		tmpOldVal, _ := strconv.ParseUint(strList[1], 10, 64)

		totalVal += tmpNewVal + tmpOldVal
		newVal += tmpNewVal
		oldVal += tmpOldVal
	}

	ratioValue := int64(totalVal)
	for _, ana := range lastAnalysis.AnalysisList {
		if ana.KpiType == uint32(pb.KpiType_Kpi_Type_Pro_Anchor) {
			if ana.Value > totalVal {
				ratioValue = -int64(ana.Value - totalVal)
			} else {
				ratioValue = int64(totalVal - ana.Value)
			}
		}
	}

	analysis.AnalysisList = append(analysis.AnalysisList, &pb.BusinessAnalysis{
		KpiType:    uint32(pb.KpiType_Kpi_Type_Pro_Anchor),
		Value:      totalVal,
		RatioValue: ratioValue,
		NewValue:   newVal,
		OldValue:   oldVal,
	})

	log.Debugf("getProAnchor end guildId:%d totalVal:%d newVal:%d oldVal:%d lastAnalysis:%v analysis:%v proAnchorStrList:%v",
		guildId, totalVal, newVal, oldVal, lastAnalysis, analysis, proAnchorStrList)
	return nil
}

func getMatureAnchor(guildId uint32, proAnchorStrList []string, analysis, lastAnalysis *GuildAnalysis) error {
	var totalVal, newVal, oldVal uint64 = 0, 0, 0
	for _, proAnchor := range proAnchorStrList {
		proAnchor = strings.ReplaceAll(proAnchor, "\\", "")
		proAnchor = strings.ReplaceAll(proAnchor, "\"", "")
		strList := strings.Split(proAnchor, "-")

		if len(strList) != 2 {
			log.Errorf("getMatureAnchor failed guildId:%d proAnchor:%s", guildId, proAnchor)
			return errors.New("成熟达人数格式错误")
		}

		tmpNewVal, _ := strconv.ParseUint(strList[0], 10, 64)
		tmpOldVal, _ := strconv.ParseUint(strList[1], 10, 64)

		totalVal += tmpNewVal + tmpOldVal
		newVal += tmpNewVal
		oldVal += tmpOldVal
	}

	ratioValue := int64(totalVal)
	for _, ana := range lastAnalysis.AnalysisList {
		if ana.KpiType == uint32(pb.KpiType_Kpi_Type_Mature_Anchor) {
			if ana.Value > totalVal {
				ratioValue = -int64(ana.Value - totalVal)
			} else {
				ratioValue = int64(totalVal - ana.Value)
			}
		}
	}

	analysis.AnalysisList = append(analysis.AnalysisList, &pb.BusinessAnalysis{
		KpiType:    uint32(pb.KpiType_Kpi_Type_Mature_Anchor),
		Value:      totalVal,
		RatioValue: ratioValue,
		NewValue:   newVal,
		OldValue:   oldVal,
	})

	log.Debugf("getMatureAnchor end guildId:%d totalVal:%d newVal:%d oldVal:%d lastAnalysis:%v analysis:%v", guildId, totalVal, newVal, oldVal, lastAnalysis, analysis)
	return nil
}

func getPotActiveAnchor(guildId uint32, proAnchorStrList []string, analysis, lastAnalysis *GuildAnalysis) error {
	var totalVal, newVal, oldVal uint64 = 0, 0, 0
	for _, proAnchor := range proAnchorStrList {
		proAnchor = strings.ReplaceAll(proAnchor, "\\", "")
		proAnchor = strings.ReplaceAll(proAnchor, "\"", "")
		strList := strings.Split(proAnchor, "-")

		if len(strList) != 2 {
			log.Errorf("getPotActiveAnchor failed guildId:%d proAnchor:%s", guildId, proAnchor)
			return errors.New("潜力活跃达人数格式错误")
		}

		tmpNewVal, _ := strconv.ParseUint(strList[0], 10, 64)
		tmpOldVal, _ := strconv.ParseUint(strList[1], 10, 64)

		totalVal += tmpNewVal + tmpOldVal
		newVal += tmpNewVal
		oldVal += tmpOldVal
	}

	ratioValue := int64(totalVal)
	for _, ana := range lastAnalysis.AnalysisList {
		if ana.KpiType == uint32(pb.KpiType_Kpi_Type_Pot_Active_Anchor) {
			if ana.Value > totalVal {
				ratioValue = -int64(ana.Value - totalVal)
			} else {
				ratioValue = int64(totalVal - ana.Value)
			}
		}
	}

	analysis.AnalysisList = append(analysis.AnalysisList, &pb.BusinessAnalysis{
		KpiType:    uint32(pb.KpiType_Kpi_Type_Pot_Active_Anchor),
		Value:      totalVal,
		RatioValue: ratioValue,
		NewValue:   newVal,
		OldValue:   oldVal,
	})

	log.Debugf("getPotActiveAnchor end guildId:%d totalVal:%d newVal:%d oldVal:%d lastAnalysis:%v analysis:%v proAnchorStrList:%v",
		guildId, totalVal, newVal, oldVal, lastAnalysis, analysis, proAnchorStrList)
	return nil
}

func getGuildRemain(guildId uint32, proRemainStrList []string, matureRemainStrList []string, potActiveRemainStrList []string, analysis *GuildAnalysis) error {
	for _, proRemain := range proRemainStrList {
		proRemain = strings.ReplaceAll(proRemain, "\\", "")
		proRemain = strings.ReplaceAll(proRemain, "\"", "")
		strList := strings.Split(proRemain, "/")

		if len(strList) != 2 {
			log.Errorf("getGuildRemain failed guildId:%d proRemain:%s", guildId, proRemain)
			return errors.New("留存人数格式错误")
		}

		tmpRemain, _ := strconv.ParseUint(strList[0], 10, 32)

		analysis.PorAnchorRemain += uint32(tmpRemain)
	}

	for _, matureRemain := range matureRemainStrList {
		matureRemain = strings.ReplaceAll(matureRemain, "\\", "")
		matureRemain = strings.ReplaceAll(matureRemain, "\"", "")
		strList := strings.Split(matureRemain, "/")

		if len(strList) != 2 {
			log.Errorf("getGuildRemain failed guildId:%d matureRemain:%s", guildId, matureRemain)
			return errors.New("留存人数格式错误")
		}

		tmpRemain, _ := strconv.ParseUint(strList[0], 10, 32)

		analysis.MatureAnchorRemain += uint32(tmpRemain)
	}

	for _, potActiveRemain := range potActiveRemainStrList {
		potActiveRemain = strings.ReplaceAll(potActiveRemain, "\\", "")
		potActiveRemain = strings.ReplaceAll(potActiveRemain, "\"", "")
		strList := strings.Split(potActiveRemain, "/")

		if len(strList) != 2 {
			log.Errorf("getGuildRemain failed guildId:%d potActiveRemain:%s", guildId, potActiveRemain)
			return errors.New("留存人数格式错误")
		}

		tmpRemain, _ := strconv.ParseUint(strList[0], 10, 32)

		analysis.PotActiveAnchorRemain += uint32(tmpRemain)
	}

	log.Debugf("getGuildRemain end guildId:%d analysis:%v proRemainStrList:%v", guildId, analysis, proRemainStrList)
	return nil
}

func getGuildLevel(guildId uint32, analysis *GuildAnalysis, incomeLevelList []IncomeLevel) uint32 {
	level := uint32(0)
	incomeVal := uint64(0)

	for _, ana := range analysis.AnalysisList {
		if ana.KpiType == uint32(pb.KpiType_Kpi_Type_Guild_Income) {
			incomeVal = ana.Value
			break
		}
	}

	for _, income := range incomeLevelList {
		if incomeVal >= income.minVal && (incomeVal < income.maxVal || income.maxVal == 0) {
			level = income.level
			break
		}
	}

	log.Debugf("getGuildLevel end guildId:%d incomeVal:%d level:%d", guildId, incomeVal, level)
	return level
}

func guildSort(guildList []uint32, kpiTypeList []uint32, mapId2Analysis map[uint32]*GuildAnalysis, isAllRank bool) {
	for _, kpiType := range kpiTypeList {
		sort.Slice(guildList, func(i, j int) bool {
			var iVal, jVal uint64 = 0, 0
			var iIncome, jIncome uint64 = 0, 0
			for _, analysis := range mapId2Analysis[guildList[i]].AnalysisList {
				if analysis.KpiType == kpiType {
					iVal = analysis.Value
				}

				if analysis.KpiType == uint32(pb.KpiType_Kpi_Type_Guild_Income) {
					iIncome = analysis.Value
				}
			}

			for _, analysis := range mapId2Analysis[guildList[j]].AnalysisList {
				if analysis.KpiType == kpiType {
					jVal = analysis.Value
				}
				if analysis.KpiType == uint32(pb.KpiType_Kpi_Type_Guild_Income) {
					jIncome = analysis.Value
				}
			}

			if iVal == jVal {
				return iIncome > jIncome
			}

			return iVal > jVal
		})

		for id, analysis := range mapId2Analysis {
			for i, guildId := range guildList {
				if guildId == id {
					for _, ana := range analysis.AnalysisList {
						if ana.KpiType == kpiType {
							if isAllRank {
								ana.AllRank = uint32(i + 1)
								if ana.Value == 0 {
									ana.AllRank = uint32(len(guildList))
								}
							} else {
								ana.Rank = uint32(i + 1)
								if ana.Value == 0 {
									ana.Rank = uint32(len(guildList))
								}
							}
						}
					}
					break
				}
			}
		}
	}
}

// 计算公会排名
func culGuildRank(allGuildList []uint32, kpiTypeList []uint32, mapLevel2GuildList map[uint32][]uint32, mapId2Analysis, mapId2LastAnalysis map[uint32]*GuildAnalysis) {
	for _, guildList := range mapLevel2GuildList {
		guildSort(guildList, kpiTypeList, mapId2Analysis, false)
	}

	guildSort(allGuildList, kpiTypeList, mapId2Analysis, true)

	for id, analysis := range mapId2Analysis {
		lastAnalysis := mapId2LastAnalysis[id]
		if lastAnalysis == nil {
			continue
		}

		for _, ana := range analysis.AnalysisList {
			for _, lastAna := range lastAnalysis.AnalysisList {
				if ana.KpiType == lastAna.KpiType {
					if lastAna.AllRank > ana.AllRank {
						ana.RatioRank = int32(lastAna.AllRank - ana.AllRank)
					} else {
						ana.RatioRank = -int32(ana.AllRank - lastAna.AllRank)
					}
				}
			}
		}
	}

	log.Debugf("culGuildWeekLevelRank end kpiTypeList:%v mapLevel2GuildList:%v mapId2Analysis:%v", kpiTypeList, mapLevel2GuildList, mapId2Analysis)
}

// 计算公会平均值
func culGuildAve(kpiTypeList []uint32, mapLevel2GuildList map[uint32][]uint32, mapId2Analysis map[uint32]*GuildAnalysis) {
	for _, guildList := range mapLevel2GuildList {
		for _, kpiType := range kpiTypeList {
			guildCnt := len(guildList)
			var totalVal uint64 = 0
			for _, guildId := range guildList {
				for _, ana := range mapId2Analysis[guildId].AnalysisList {
					if ana.KpiType == kpiType {
						if ana.Value > 0 {
							totalVal += ana.Value
						} else {
							guildCnt--
						}

					}
				}
			}

			var aveValFloat float64 = 0
			if guildCnt > 0 {
				aveValFloat = float64(totalVal) / float64(len(guildList))
			}

			for _, guildId := range guildList {
				for _, ana := range mapId2Analysis[guildId].AnalysisList {
					if ana.KpiType == kpiType {
						ana.LevelAve = uint64(math.Round(aveValFloat))
					}
				}
			}
		}
	}

	log.Debugf("culGuildAve end kpiTypeList:%v mapLevel2GuildList:%v mapId2Analysis:%v", kpiTypeList, mapLevel2GuildList, mapId2Analysis)
}

// 获取层级公会数量
func getLevelGuildCnt(mapLevel2GuildList map[uint32][]uint32, mapId2Analysis map[uint32]*GuildAnalysis) {
	for _, guildList := range mapLevel2GuildList {
		for _, guildId := range guildList {
			mapId2Analysis[guildId].LevelGuildCnt = uint32(len(guildList))
		}
	}
}

func (m *ChannelLiveStatsManager) TimerGuildWeekBusinessAnalysis() {
	nowTm := time.Now()
	ctx := context.Background()

	if nowTm.Weekday() != time.Monday || nowTm.Hour() < 8 {
		log.InfoWithCtx(ctx, "TimerGuildWeekBusinessAnalysis not Monday or not 8 o'clock")
		return
	}

	var err error
	lockName := "TimerGuildWeekBusinessAnalysis"
	isGetLock, err := m.statsCache.GetLock(lockName, 24*time.Hour)
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerGuildWeekBusinessAnalysis GetLock failed lockName:%s err:%v", lockName, err)
		return
	}

	if !isGetLock {
		log.DebugWithCtx(ctx, "TimerGuildWeekBusinessAnalysis GetLock failed lockName:%s", lockName)
		return
	}

	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerGuildWeekBusinessAnalysis failed err:%v", err)
			err = m.statsCache.ReleaseLock(lockName)
			if err != nil {
				log.ErrorWithCtx(ctx, "TimerGuildWeekBusinessAnalysis ReleaseLock failed lockName:%s err:%v", lockName, err)
			}

		}
	}()

	err = m.GuildWeekBusinessAnalysis(ctx, nowTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerGuildWeekBusinessAnalysis GuildWeekBusinessAnalysis failed err:%v", err)
		return
	}
}

// 周维度公会经营分析
func (m *ChannelLiveStatsManager) GuildWeekBusinessAnalysis(ctx context.Context, nowTm time.Time) error {
	weekBeginDayTm := nowTm.AddDate(0, 0, -mysql.GetWeekDayOffset(nowTm)+1)

	beginDay := weekBeginDayTm.AddDate(0, 0, -7)
	lastBeginDy := beginDay.AddDate(0, 0, -7)

	log.DebugWithCtx(ctx, "GuildWeekBusinessAnalysis nowTm:%v weekBeginDayTm:%v beginDay:%v lastBeginDy:%v",
		nowTm, weekBeginDayTm, beginDay, lastBeginDy)

	guildIdList, err := m.getAllLiveCoopGuildList(ctx, WeekDateTim, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GuildWeekBusinessAnalysis getAllLiveCoopGuildList failed err:%v", err)
		return err
	}

	mapId2IsCoop := make(map[uint32]bool)
	for _, guildId := range guildIdList {
		mapId2IsCoop[guildId] = true
	}

	mapBindId2List, mapId2BindId, _, err := m.getBindGuildList(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GuildWeekBusinessAnalysis getBindGuildList failed err:%v", err)
		return err
	}

	mapId2DataList := make(map[uint32][]*GuildLiveStats)
	for _, guildId := range guildIdList {
		curData, tmpErr := m.getGuildDataFromDH(ctx, guildId, beginDay, beginDay, "week")
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildWeekBusinessAnalysis getGuildDataFromDH failed guildId:%d err:%v", guildId, tmpErr)
			return err
		}

		tmpGuildId := guildId
		if mapId2BindId[guildId] != 0 && mapId2IsCoop[mapId2BindId[guildId]] {
			tmpGuildId = mapId2BindId[guildId]
		}

		mapId2DataList[tmpGuildId] = append(mapId2DataList[tmpGuildId], curData)
	}

	mapId2Analysis := make(map[uint32]*GuildAnalysis)
	mapId2LastAnalysis := make(map[uint32]*GuildAnalysis)
	mapLevel2GuildList := make(map[uint32][]uint32)
	mapId2Level := make(map[uint32]uint32)
	allGuildList := make([]uint32, 0)

	for guildId, dataList := range mapId2DataList {
		lastBusinessAnalysis, _, tmpErr := m.mysqlStore.GetGuildWeekBusinessAnalysis(ctx, guildId, uint32(lastBeginDy.Unix()))
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildWeekBusinessAnalysis GetGuildWeekBusinessAnalysis failed err:%v", tmpErr)
			return tmpErr
		}

		lastAnalysis := &GuildAnalysis{
			AnalysisList: make([]*pb.BusinessAnalysis, 0),
		}

		tmpErr = json.Unmarshal(lastBusinessAnalysis.Analysis, lastAnalysis)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildWeekBusinessAnalysis Unmarshal failed err:%v", tmpErr)
		}

		analysis := &GuildAnalysis{
			AnalysisList: make([]*pb.BusinessAnalysis, 0),
		}

		revenueCntList := make([]string, 0)
		revenueNewOldCntList := make([]string, 0)
		revnLevelUserCntList := make([]string, 0)
		newSignAnchorCntList := make([]string, 0)
		liveAnchorCntList := make([]string, 0)
		twoWanCntList := make([]string, 0)
		tenWanCntList := make([]string, 0)
		var live5dCnt uint32
		for _, data := range dataList {
			for _, tmpData := range data.DataList {
				revenueCntList = append(revenueCntList, tmpData.Revenue)
				revenueNewOldCntList = append(revenueNewOldCntList, tmpData.RevenueNewOld)
				revnLevelUserCntList = append(revnLevelUserCntList, tmpData.RevnLevelUserCnt)
				newSignAnchorCntList = append(newSignAnchorCntList, tmpData.NewSignAnchorCnt)
				liveAnchorCntList = append(liveAnchorCntList, tmpData.EnabledLiveUserCountNewOld)
				twoWanCntList = append(twoWanCntList, tmpData.IncomeUp2wUserCntNewOld)
				tenWanCntList = append(tenWanCntList, tmpData.IncomeUp10wUserCntNewOld)

				live5dCnt_, _ := strconv.Atoi(tmpData.EnabledLive5dUserCount)
				live5dCnt += uint32(live5dCnt_)
			}
		}

		tmpErr = getGuildIncome(guildId, revenueCntList, revenueNewOldCntList, revnLevelUserCntList, analysis, lastAnalysis)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildWeekBusinessAnalysis getWeekIncome failed guildId:%d err:%v revnLevelUserCntList:%v", guildId, tmpErr, revnLevelUserCntList)
			return tmpErr
		}

		tmpErr = getNewSignAnchor(guildId, newSignAnchorCntList, analysis, lastAnalysis)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildWeekBusinessAnalysis getNewSignAnchor failed err:%v", tmpErr)
			return tmpErr
		}

		tmpErr = getLiveAnchor(guildId, liveAnchorCntList, analysis, lastAnalysis)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildWeekBusinessAnalysis getLiveAnchor failed err:%v", tmpErr)
			return tmpErr
		}

		tmpErr = getTwoWanAnchor(guildId, twoWanCntList, analysis, lastAnalysis)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildWeekBusinessAnalysis getTwoWanAnchor failed err:%v", tmpErr)
			return tmpErr
		}

		tmpErr = getTenWanAnchor(guildId, tenWanCntList, analysis, lastAnalysis)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildWeekBusinessAnalysis getTenWanAnchor failed err:%v", tmpErr)
			return tmpErr
		}

		analysis.WeekLive5dCnt = live5dCnt
		mapId2Analysis[guildId] = analysis
		mapId2LastAnalysis[guildId] = lastAnalysis
		mapId2Level[guildId] = getGuildLevel(guildId, analysis, WeekIncomeLevelList)
		mapLevel2GuildList[mapId2Level[guildId]] = append(mapLevel2GuildList[mapId2Level[guildId]], guildId)
		allGuildList = append(allGuildList, guildId)
	}

	kpiTypeList := []uint32{
		uint32(pb.KpiType_Kpi_Type_Guild_Income),
		uint32(pb.KpiType_Kpi_Type_New_Sign_Anchor),
		uint32(pb.KpiType_Kpi_Type_Live_Anchor),
		uint32(pb.KpiType_Kpi_Type_Two_Wan_Anchor),
		uint32(pb.KpiType_Kpi_Type_Ten_Wan_Anchor),
	}

	culGuildRank(allGuildList, kpiTypeList, mapLevel2GuildList, mapId2Analysis, mapId2LastAnalysis)
	culGuildAve(kpiTypeList, mapLevel2GuildList, mapId2Analysis)
	getLevelGuildCnt(mapLevel2GuildList, mapId2Analysis)

	for guildId, analysis := range mapId2Analysis {
		analysisJson, _ := json.Marshal(analysis)
		tmpErr := m.mysqlStore.UpdateGuildWeekBusinessAnalysis(ctx, &mysql.GuildWeekBusinessAnalysis{
			Week:     mysql.GetWeekKey(uint32(beginDay.Unix())),
			GuildID:  guildId,
			Analysis: analysisJson,
		})
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildWeekBusinessAnalysis SetGuildWeekBusinessAnalysis failed err:%v", tmpErr)
			continue
		}

		if len(mapBindId2List[guildId]) > 0 {
			for _, bindId := range mapBindId2List[guildId] {
				if !mapId2IsCoop[bindId] {
					continue
				}

				tmpErr = m.mysqlStore.UpdateGuildWeekBusinessAnalysis(ctx, &mysql.GuildWeekBusinessAnalysis{
					Week:     mysql.GetWeekKey(uint32(beginDay.Unix())),
					GuildID:  bindId,
					Analysis: analysisJson,
				})
				if tmpErr != nil {
					log.ErrorWithCtx(ctx, "GuildWeekBusinessAnalysis SetGuildWeekBusinessAnalysis failed err:%v", tmpErr)
					continue
				}
			}
		}

	}

	log.InfoWithCtx(ctx, "GuildWeekBusinessAnalysis end weekBeginDayTm:%v mapBindId2List:%v", weekBeginDayTm, mapBindId2List)
	return nil
}

func (m *ChannelLiveStatsManager) TimerGuildMonthBusinessAnalysis() {
	nowTm := time.Now()
	ctx := context.Background()

	if nowTm.Day() != 1 || nowTm.Hour() < 8 {
		log.InfoWithCtx(ctx, "TimerGuildMonthBusinessAnalysis not first day or not 8 o'clock")
		return
	}

	var err error
	lockName := "TimerGuildMonthBusinessAnalysis"
	isGetLock, err := m.statsCache.GetLock(lockName, 24*time.Hour)
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerGuildMonthBusinessAnalysis GetLock failed lockName:%s err:%v", lockName, err)
		return
	}

	if !isGetLock {
		log.DebugWithCtx(ctx, "TimerGuildMonthBusinessAnalysis GetLock failed lockName:%s", lockName)
	}

	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerGuildMonthBusinessAnalysis failed err:%v", err)
			err = m.statsCache.ReleaseLock(lockName)
			if err != nil {
				log.ErrorWithCtx(ctx, "TimerGuildMonthBusinessAnalysis ReleaseLock failed lockName:%s err:%v", lockName, err)
			}

		}
	}()

	err = m.GuildMonthBusinessAnalysis(ctx, nowTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerGuildMonthBusinessAnalysis GuildMonthBusinessAnalysis failed err:%v", err)
		return
	}
}

// 月维度公会经营分析
func (m *ChannelLiveStatsManager) GuildMonthBusinessAnalysis(ctx context.Context, nowTm time.Time) error {
	monthTm := time.Date(nowTm.Year(), nowTm.Month(), 1, 0, 0, 0, 0, time.Local).AddDate(0, 0, -1)
	monthBeginTm := time.Date(monthTm.Year(), monthTm.Month(), 1, 0, 0, 0, 0, time.Local)
	lastMonthTm := monthBeginTm.AddDate(0, 0, -1)
	lastMonthBeginTm := time.Date(lastMonthTm.Year(), lastMonthTm.Month(), 1, 0, 0, 0, 0, time.Local)

	log.DebugWithCtx(ctx, "GuildMonthBusinessAnalysis nowTm:%v monthTm:%v monthBeginTm:%v lastMonthTm:%v lastMonthBeginTm:%v",
		nowTm, monthTm, monthBeginTm, lastMonthTm, lastMonthBeginTm)

	guildIdList, err := m.getAllLiveCoopGuildList(ctx, MonthDateTim, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GuildMonthBusinessAnalysis getAllLiveCoopGuildList failed err:%v", err)
		return err
	}

	mapId2IsCoop := make(map[uint32]bool)
	for _, guildId := range guildIdList {
		mapId2IsCoop[guildId] = true
	}

	mapBindId2List, mapId2BindId, _, err := m.getBindGuildList(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GuildMonthBusinessAnalysis getBindGuildList failed err:%v", err)
		return err
	}

	mapId2DataList := make(map[uint32][]*GuildLiveStats)
	for _, guildId := range guildIdList {
		curData, tmpErr := m.getGuildDataFromDH(ctx, guildId, monthBeginTm, monthBeginTm, "month")
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildMonthBusinessAnalysis getGuildDataFromDH failed guildId:%d err:%v", guildId, tmpErr)
			return tmpErr
		}

		tmpGuildId := guildId
		if mapId2BindId[guildId] != 0 && mapId2IsCoop[mapId2BindId[guildId]] {
			tmpGuildId = mapId2BindId[guildId]
		}

		mapId2DataList[tmpGuildId] = append(mapId2DataList[tmpGuildId], curData)
	}

	mapId2Analysis := make(map[uint32]*GuildAnalysis)
	mapId2LastAnalysis := make(map[uint32]*GuildAnalysis)
	mapLevel2GuildList := make(map[uint32][]uint32)
	mapId2Level := make(map[uint32]uint32)
	allGuildList := make([]uint32, 0)

	for guildId, dataList := range mapId2DataList {
		lastBusinessAnalysis, _, tmpErr := m.mysqlStore.GetGuildMonthBusinessAnalysis(ctx, guildId, uint32(lastMonthBeginTm.Unix()))
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildMonthBusinessAnalysis GetGuildWeekBusinessAnalysis failed err:%v", tmpErr)
			return tmpErr
		}

		lastAnalysis := &GuildAnalysis{
			AnalysisList: make([]*pb.BusinessAnalysis, 0),
		}
		tmpErr = json.Unmarshal(lastBusinessAnalysis.Analysis, lastAnalysis)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildMonthBusinessAnalysis Unmarshal failed err:%v", tmpErr)
		}

		analysis := &GuildAnalysis{
			AnalysisList: make([]*pb.BusinessAnalysis, 0),
		}

		revenueCntList := make([]string, 0)
		revenueNewOldCntList := make([]string, 0)
		revnLevelUserCntList := make([]string, 0)
		newSignAnchorCntList := make([]string, 0)
		liveAnchorCntList := make([]string, 0)
		proAnchorCntList := make([]string, 0)
		matureAnchorCntList := make([]string, 0)
		potActiveAnchorCntList := make([]string, 0)
		proAnchorRemainList := make([]string, 0)
		matureAnchorRemainList := make([]string, 0)
		potActiveAnchorRemainList := make([]string, 0)
		var live10dCnt, live20dCnt uint32

		for _, data := range dataList {
			for _, tmpData := range data.DataList {
				revenueCntList = append(revenueCntList, tmpData.Revenue)
				revenueNewOldCntList = append(revenueNewOldCntList, tmpData.RevenueNewOld)
				revnLevelUserCntList = append(revnLevelUserCntList, tmpData.RevnLevelUserCnt)
				newSignAnchorCntList = append(newSignAnchorCntList, tmpData.NewSignAnchorCnt)
				liveAnchorCntList = append(liveAnchorCntList, tmpData.EnabledLiveUserCountNewOld)
				proAnchorCntList = append(proAnchorCntList, tmpData.ProAnchorCntNewOld)
				matureAnchorCntList = append(matureAnchorCntList, tmpData.MatureAnchorCntNewOld)
				potActiveAnchorCntList = append(potActiveAnchorCntList, tmpData.PotActiveAnchorCntNewOld)
				proAnchorRemainList = append(proAnchorRemainList, tmpData.ProAnchorCntRemain)
				matureAnchorRemainList = append(matureAnchorRemainList, tmpData.MatureAnchorCntRemain)
				potActiveAnchorRemainList = append(potActiveAnchorRemainList, tmpData.PotActiveAnchorCntRemain)

				live10dCnt_, _ := strconv.Atoi(tmpData.EnabledLive10dUserCount)
				live10dCnt += uint32(live10dCnt_)
				live20dCnt_, _ := strconv.Atoi(tmpData.EnabledLive20dUserCount)
				live20dCnt += uint32(live20dCnt_)
			}
		}

		tmpErr = getGuildIncome(guildId, revenueCntList, revenueNewOldCntList, revnLevelUserCntList, analysis, lastAnalysis)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildMonthBusinessAnalysis getWeekIncome failed err:%v", tmpErr)
			return tmpErr
		}

		tmpErr = getNewSignAnchor(guildId, newSignAnchorCntList, analysis, lastAnalysis)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildMonthBusinessAnalysis getNewSignAnchor failed err:%v", tmpErr)
			return tmpErr
		}

		tmpErr = getLiveAnchor(guildId, liveAnchorCntList, analysis, lastAnalysis)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildMonthBusinessAnalysis getLiveAnchor failed err:%v", tmpErr)
			return tmpErr
		}

		tmpErr = getProAnchor(guildId, proAnchorCntList, analysis, lastAnalysis)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildMonthBusinessAnalysis getProAnchor failed err:%v", tmpErr)
			return tmpErr
		}

		tmpErr = getMatureAnchor(guildId, matureAnchorCntList, analysis, lastAnalysis)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildMonthBusinessAnalysis getMatureAnchor failed err:%v", tmpErr)
			return tmpErr
		}

		tmpErr = getPotActiveAnchor(guildId, potActiveAnchorCntList, analysis, lastAnalysis)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildMonthBusinessAnalysis getPotActiveAnchor failed err:%v", tmpErr)
			return tmpErr
		}

		tmpErr = getGuildRemain(guildId, proAnchorRemainList, matureAnchorRemainList, potActiveAnchorRemainList, analysis)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildMonthBusinessAnalysis getGuildRemain failed err:%v", tmpErr)
			return tmpErr
		}

		analysis.MonthLive10dCnt = live10dCnt
		analysis.MonthLive20dCnt = live20dCnt
		mapId2Analysis[guildId] = analysis
		mapId2LastAnalysis[guildId] = lastAnalysis
		mapId2Level[guildId] = getGuildLevel(guildId, analysis, MonthIncomeLevelList)
		mapLevel2GuildList[mapId2Level[guildId]] = append(mapLevel2GuildList[mapId2Level[guildId]], guildId)
		allGuildList = append(allGuildList, guildId)
	}

	kpiTypeList := []uint32{
		uint32(pb.KpiType_Kpi_Type_Guild_Income),
		uint32(pb.KpiType_Kpi_Type_New_Sign_Anchor),
		uint32(pb.KpiType_Kpi_Type_Live_Anchor),
		uint32(pb.KpiType_Kpi_Type_Pro_Anchor),
		uint32(pb.KpiType_Kpi_Type_Mature_Anchor),
		uint32(pb.KpiType_Kpi_Type_Pot_Active_Anchor),
	}

	culGuildRank(allGuildList, kpiTypeList, mapLevel2GuildList, mapId2Analysis, mapId2LastAnalysis)
	culGuildAve(kpiTypeList, mapLevel2GuildList, mapId2Analysis)
	getLevelGuildCnt(mapLevel2GuildList, mapId2Analysis)

	for guildId, analysis := range mapId2Analysis {
		analysisJson, _ := json.Marshal(analysis)
		tmpErr := m.mysqlStore.UpdateGuildMonthBusinessAnalysis(ctx, &mysql.GuildMonthBusinessAnalysis{
			MonthDate: mysql.GetMonthKey(uint32(monthBeginTm.Unix())),
			GuildID:   guildId,
			Analysis:  analysisJson,
		})
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GuildMonthBusinessAnalysis UpdateGuildMonthBusinessAnalysis failed err:%v", tmpErr)
			continue
		}

		if len(mapBindId2List[guildId]) > 0 {
			for _, bindId := range mapBindId2List[guildId] {
				if !mapId2IsCoop[bindId] {
					continue
				}

				tmpErr = m.mysqlStore.UpdateGuildMonthBusinessAnalysis(ctx, &mysql.GuildMonthBusinessAnalysis{
					MonthDate: mysql.GetMonthKey(uint32(monthBeginTm.Unix())),
					GuildID:   bindId,
					Analysis:  analysisJson,
				})
				if tmpErr != nil {
					log.ErrorWithCtx(ctx, "GuildMonthBusinessAnalysis UpdateGuildMonthBusinessAnalysis failed err:%v", tmpErr)
					continue
				}
			}
		}

	}

	log.InfoWithCtx(ctx, "GuildMonthBusinessAnalysis end monthTm:%v mapBindId2List:%v mapId2Analysis:%v", monthTm, mapBindId2List, mapId2Analysis)
	return nil
}

func (m *ChannelLiveStatsManager) TimerClearBusinessAnalysis(ctx context.Context) {
	nowTm := time.Now()
	lastYearTm := nowTm.AddDate(-1, 0, 0)

	err := m.mysqlStore.DeleteGuildWeekBusinessAnalysis(ctx, uint32(lastYearTm.Unix()))
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerClearBusinessAnalysis DeleteGuildWeekBusinessAnalysis failed err:%v", err)
	}

	err = m.mysqlStore.DeleteGuildMonthBusinessAnalysis(ctx, uint32(lastYearTm.Unix()))
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerClearBusinessAnalysis DeleteGuildMonthBusinessAnalysis failed err:%v", err)
	}

	log.InfoWithCtx(ctx, "TimerClearBusinessAnalysis end nowTm:%v lastYearTm:%v", nowTm, lastYearTm)
}
