// manager_test.go
package manager

import (
	"context"
	"errors"
	"github.com/golang/mock/gomock"
	anchor_check "golang.52tt.com/clients/mocks/anchor-check"
	anchorcontractGoMock "golang.52tt.com/clients/mocks/anchorcontract-go"
	apicenter "golang.52tt.com/clients/mocks/apicenter/apiserver"
	channellivemgr "golang.52tt.com/clients/mocks/channel-live-mgr"
	channellivestats "golang.52tt.com/clients/mocks/channel-live-stats"
	channel_recommend_svr "golang.52tt.com/clients/mocks/channel-recommend-svr"
	"golang.52tt.com/clients/mocks/entertainmentrecommendback"
	"golang.52tt.com/clients/mocks/public"
	ttc_proxy "golang.52tt.com/clients/mocks/ttc-proxy"
	userol "golang.52tt.com/clients/mocks/user-online"
	"golang.52tt.com/pkg/protocol"
	anchor_check2 "golang.52tt.com/protocol/services/anchor-check"
	anchorcontract_go "golang.52tt.com/protocol/services/anchorcontract-go"
	channel_live_stats "golang.52tt.com/protocol/services/channel-live-stats"
	channel_live_mgr "golang.52tt.com/protocol/services/channellivemgr"
	PublicAccount "golang.52tt.com/protocol/services/publicsvr"
	userolpb "golang.52tt.com/protocol/services/user-online"
	"golang.52tt.com/services/anchor-level/mocks"
	"golang.52tt.com/services/anchor-level/store"
	"golang.52tt.com/services/anchor-level/utils"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	// ... other necessary imports from your original file
)

type MockMgr struct {
	store                      *mocks.MockIStore
	dyconfig                   *mocks.MockISDyConfigHandler
	channelLiveMgr             *channellivemgr.MockIClient
	channelLiveStats           *channellivestats.MockIClient
	channelRecommendSvr        *channel_recommend_svr.MockIClient
	entertainmentRecommendBack *entertainmentrecommendback.MockIClient
	anchorcontractGoClient     *anchorcontractGoMock.MockIClient
	reporter                   *mocks.MockIFeishuReporterV2
	anchorCheckCli             *anchor_check.MockIClient
	ttcProxyClient             *ttc_proxy.MockIClient
	// 新增的 mock 客户端
	publicCli *public.MockIClient
	userOlCli *userol.MockIClient
	apiCli    *apicenter.MockIClient
}

func Test_getTaskName(t *testing.T) {
	testCases := []struct {
		name     string
		taskType int
		expected string
	}{
		{"New Task", taskTypeNew, "新达人"},
		{"Total New Task", taskTypeTotalNew, "纯新达人"},
		{"Comeback Task", taskTypeComeback, "回归达人"},
		{"Unknown Task", 99, ""},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actual := getTaskName(tc.taskType)
			assert.Equal(t, tc.expected, actual)
		})
	}
}

func Test_getWeekNum(t *testing.T) {
	loc, _ := time.LoadLocation("Asia/Shanghai")                        // Or your relevant timezone
	taskBeginTime := time.Date(2023, time.January, 2, 10, 0, 0, 0, loc) // A Monday 10:00

	testCases := []struct {
		name     string
		tNow     time.Time
		expected int
	}{
		{"First day of task", taskBeginTime, 1},
		{"Same week", taskBeginTime.Add(24 * time.Hour * 2), 1}, // Wednesday of same week
		{"End of first week (Sunday)", taskBeginTime.Add(24*time.Hour*6 + 10*time.Hour), 1},
		{"Start of second week (Monday)", taskBeginTime.Add(24 * time.Hour * 7), 2},
		{"Middle of second week", taskBeginTime.Add(24*time.Hour*7 + 24*time.Hour*3), 2},
		{"Exactly 4 weeks later", taskBeginTime.Add(24 * time.Hour * 7 * 4), 5},
		{"Time before taskBeginTime", taskBeginTime.Add(-24 * time.Hour), 0}, // Test edge case
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actual := getWeekNum(tc.tNow, taskBeginTime)
			assert.Equal(t, tc.expected, actual)
		})
	}
}

func setupAnchorLevelMgr(t *testing.T) (*gomock.Controller, *AnchorLevelMgr, *MockMgr) {
	ctrl := gomock.NewController(t)

	// mockStore := mock_store.NewMockStore(ctrl)
	// mockDyconfig := mock_dyconfig.NewMockDyConfig(ctrl)
	// ... initialize all mocks

	reporter := mocks.NewMockIFeishuReporterV2(ctrl)
	dyconfig := mocks.NewMockISDyConfigHandler(ctrl)
	store := mocks.NewMockIStore(ctrl)
	channelLiveStats := channellivestats.NewMockIClient(ctrl)
	anchorCheckCli := anchor_check.NewMockIClient(ctrl)
	channelLiveMgr := channellivemgr.NewMockIClient(ctrl)
	anchorcontractGoClient := anchorcontractGoMock.NewMockIClient(ctrl)
	entertainmentRecommendBack := entertainmentrecommendback.NewMockIClient(ctrl)
	channelRecommendSvr := channel_recommend_svr.NewMockIClient(ctrl)
	ttcProxyClient := ttc_proxy.NewMockIClient(ctrl)
	// 新增的 mock 客户端
	publicCli := public.NewMockIClient(ctrl)
	userOlCli := userol.NewMockIClient(ctrl)
	apiCli := apicenter.NewMockIClient(ctrl)

	mgr := &AnchorLevelMgr{
		reporter:                   reporter,
		dyconfig:                   dyconfig,
		store:                      store,
		channelLiveStats:           channelLiveStats,
		anchorCheckCli:             anchorCheckCli,
		channelLiveMgr:             channelLiveMgr,
		anchorcontractGoClient:     anchorcontractGoClient,
		entertainmentRecommendBack: entertainmentRecommendBack,
		channelRecommendSvr:        channelRecommendSvr,
		ttcProxyClient:             ttcProxyClient,
		publicCli:                  publicCli,
		userOlCli:                  userOlCli,
		apiCli:                     apiCli,
	}

	mockMgr := &MockMgr{
		reporter:                   reporter,
		dyconfig:                   dyconfig,
		store:                      store,
		channelLiveStats:           channelLiveStats,
		anchorCheckCli:             anchorCheckCli,
		channelLiveMgr:             channelLiveMgr,
		anchorcontractGoClient:     anchorcontractGoClient,
		entertainmentRecommendBack: entertainmentRecommendBack,
		channelRecommendSvr:        channelRecommendSvr,
		ttcProxyClient:             ttcProxyClient,
		publicCli:                  publicCli,
		userOlCli:                  userOlCli,
		apiCli:                     apiCli,
	}

	return ctrl, mgr, mockMgr
}

func TestAnchorLevelMgr_SendNewAnchorTaskReward(t *testing.T) {
	ctrl, mgr, mocks := setupAnchorLevelMgr(t)
	defer ctrl.Finish()

	ctx := context.Background()
	now := time.Date(2023, 8, 14, 10, 0, 0, 0, time.FixedZone("CST", 8*60*60)) // 周一 10:00 CST
	fakeNowTimestamp := now.Unix()
	weekBeginTime := utils.GetThisWeekBeginTime(now) // 本周一 00:00
	lastWeekBeginTime := weekBeginTime.AddDate(0, 0, -7)
	lastWeekEndTime := weekBeginTime.Add(-time.Second)

	// beginTime := weekBeginTime.AddDate(0, 0, -7*8-4)

	mockUID := uint32(123)
	mockCheckListItem := &store.AnchorLevelNewTask{
		Uid:        mockUID,
		TaskType:   taskTypeNew,
		CreateTime: now.AddDate(0, 0, -7*3).Unix(), // 3周前创建
	}

	mockCheckListItemBase := store.AnchorLevelNewTask{ // 基础的检查项
		Uid:      mockUID,
		TaskType: taskTypeNew,
		// CreateTime 会在具体测试场景中设置
	}

	// 公共的 mock 设置 for step2 (如果被调用)
	setupStep2Mocks := func(mocksSub *MockMgr, taskType int, isSecond bool, weekGiftValue uint32) *MockMgr {
		mocksSub.anchorCheckCli.EXPECT().GetLastCreateScore(gomock.Any(), mockUID).Return(&anchor_check2.LastCreateScore{Level: "A"}, nil).AnyTimes()
		mocksSub.channelLiveMgr.EXPECT().GetChannelLiveInfo(gomock.Any(), mockUID, false).Return(&channel_live_mgr.GetChannelLiveInfoResp{
			ChannelLiveInfo: &channel_live_mgr.ChannelLiveInfo{ChannelId: 1, TagId: 1},
		}, nil).AnyTimes()
		mocksSub.anchorcontractGoClient.EXPECT().GetUserContractCacheInfo(gomock.Any(), uint32(0), mockUID).Return(&anchorcontract_go.ContractCacheInfo{
			Contract: &anchorcontract_go.ContractInfo{GuildId: 100},
		}, nil).AnyTimes()
		mocksSub.entertainmentRecommendBack.EXPECT().AddLivePrepareChannel(gomock.Any(), uint32(0), gomock.Any()).Return(nil, nil).AnyTimes()
		mocksSub.store.EXPECT().InsertAnchorLevelNewTaskRecord(gomock.Any()).Return(nil).AnyTimes()
		if taskType == taskTypeNew && !isSecond && weekGiftValue < 200000 && weekGiftValue >= 100000 { // 假设这是会发任务结束消息的场景
			// 这个mock需要根据实际的taskType, isSecond和elemCreateTime来决定是否触发
			// 暂时简化，如果 SendVoiceLiveMsg 被调用，SendImMock会处理
		}

		mocksSub = SendImMock(mocksSub, mockUID) // 假设 handleNewTaskPushMsg 会被调用

		return mocksSub
	}

	// 场景1: 任务完成 - 满足最低条件，进入 step2 (step2 的详细 mock 在其自己的测试或这里简化)
	t.Run("任务完成并发送奖励", func(t *testing.T) {

		gomock.InOrder(
			mocks.reporter.EXPECT().SendInfo("SendNewAnchorTaskReward start").Return(nil),
			mocks.dyconfig.EXPECT().GetFakeNowTime().Return(int64(fakeNowTimestamp)).AnyTimes(),
			mocks.store.EXPECT().GetAnchorLevelNewTaskWithTimeRange(ctx, gomock.Any(), weekBeginTime.Unix()).Return([]*store.AnchorLevelNewTask{mockCheckListItem}, nil),
		)

		mocks.dyconfig.EXPECT().GetFakeLevel(mockUID).Return("").AnyTimes()
		activeDaysResp := &channel_live_stats.GetAnchorDailyRecordWithDateListResp{
			List: []*channel_live_stats.AnchorDailyStats{
				{IsLiveActiveDay: true, AnchorIncome: 20000}, {IsLiveActiveDay: true, AnchorIncome: 20000},
				{IsLiveActiveDay: true, AnchorIncome: 20000}, {IsLiveActiveDay: true, AnchorIncome: 20000},
				{IsLiveActiveDay: true, AnchorIncome: 20000}, // 5 active days, income 100000
			},
		}
		mocks.channelLiveStats.EXPECT().GetAnchorDailyRecordWithDateList(ctx, uint32(0), mockUID, uint32(lastWeekBeginTime.Unix()), uint32(lastWeekEndTime.Unix()-1)).Return(activeDaysResp, nil)

		// Mock for sendNewAnchorTaskRewardStep2 (简化，只期望调用)
		// 实际中，sendNewAnchorTaskRewardStep2 内部的 mock 会更复杂
		mocks.anchorCheckCli.EXPECT().GetLastCreateScore(gomock.Any(), mockUID).Return(&anchor_check2.LastCreateScore{Level: "A"}, nil)
		mocks.channelLiveMgr.EXPECT().GetChannelLiveInfo(gomock.Any(), mockUID, false).Return(&channel_live_mgr.GetChannelLiveInfoResp{
			ChannelLiveInfo: &channel_live_mgr.ChannelLiveInfo{ChannelId: 1, TagId: 1},
		}, nil)
		mocks.anchorcontractGoClient.EXPECT().GetUserContractCacheInfo(gomock.Any(), uint32(0), mockUID).Return(&anchorcontract_go.ContractCacheInfo{
			Contract: &anchorcontract_go.ContractInfo{GuildId: 100},
		}, nil)
		mocks.entertainmentRecommendBack.EXPECT().AddLivePrepareChannel(gomock.Any(), uint32(0), gomock.Any()).Return(nil, nil) // 简化 any req
		mocks.store.EXPECT().InsertAnchorLevelNewTaskRecord(gomock.Any()).Return(nil)

		mocks = SendImMock(mocks, mockUID)
		mocks.reporter.EXPECT().SendInfo("SendNewAnchorTaskReward end").Return(nil).AnyTimes() // Allow multiple calls if run in parallel or sequence

		err := mgr.SendNewAnchorTaskReward()
		assert.NoError(t, err)
	})

	// 场景2: 任务未完成 - 活跃天数不足
	t.Run("任务未完成_活跃天数不足", func(t *testing.T) {
		// Reset relevant mocks for this sub-test if using fresh ctrl, mgr, mocks for each sub-test
		// Or ensure mocks are set for this specific path if sharing mocks
		ctrlSub, mgrSub, mocksSub := setupAnchorLevelMgr(t)
		defer ctrlSub.Finish()

		fakeNowTimestampSub := now.Unix()
		weekBeginTimeSub := utils.GetThisWeekBeginTime(now)
		lastWeekBeginTimeSub := weekBeginTimeSub.AddDate(0, 0, -7)
		lastWeekEndTimeSub := weekBeginTimeSub.Add(-time.Second)

		mockCheckListItemSub := &store.AnchorLevelNewTask{
			Uid: mockUID, TaskType: taskTypeNew, CreateTime: now.AddDate(0, 0, -7*3).Unix(),
		}

		mocksSub.reporter.EXPECT().SendInfo("SendNewAnchorTaskReward start").Return(nil).AnyTimes()
		mocksSub.dyconfig.EXPECT().GetFakeNowTime().Return(fakeNowTimestampSub).AnyTimes()
		mocksSub.store.EXPECT().GetAnchorLevelNewTaskWithTimeRange(ctx, gomock.Any(), weekBeginTimeSub.Unix()).Return([]*store.AnchorLevelNewTask{mockCheckListItemSub}, nil).AnyTimes()
		mocksSub.dyconfig.EXPECT().GetFakeLevel(mockUID).Return("").AnyTimes()

		activeDaysRespFail := &channel_live_stats.GetAnchorDailyRecordWithDateListResp{
			List: []*channel_live_stats.AnchorDailyStats{ // 活跃天数不足 (e.g., 2 days)
				{IsLiveActiveDay: true, AnchorIncome: 60000}, {IsLiveActiveDay: true, AnchorIncome: 60000},
			},
		}
		mocksSub.channelLiveStats.EXPECT().GetAnchorDailyRecordWithDateList(ctx, uint32(0), mockUID, uint32(lastWeekBeginTimeSub.Unix()), uint32(lastWeekEndTimeSub.Unix()-1)).Return(activeDaysRespFail, nil)
		mocksSub.anchorCheckCli.EXPECT().GetLastCreateScore(ctx, mockUID).Return(&anchor_check2.LastCreateScore{Level: "B"}, nil)
		mocksSub.store.EXPECT().InsertAnchorLevelNewTaskRecord(gomock.Any()).DoAndReturn(func(record *store.AnchorLevelNewTaskRecord) error {
			assert.Equal(t, mockUID, record.Uid)
			assert.Equal(t, uint32(0), record.WeekTaskLevel) //未完成
			assert.Equal(t, uint32(2), record.WeekActiveDay)
			assert.Equal(t, uint32(120000), record.WeekIncome)
			return nil
		})
		//mocksSub = SendImMock(mocksSub, mockUID)
		mocksSub.reporter.EXPECT().SendInfo("SendNewAnchorTaskReward end").Return(nil).AnyTimes() // Allow multiple calls if run in parallel or sequence

		err := mgrSub.SendNewAnchorTaskReward()
		assert.NoError(t, err) // 主函数不应报错，错误在内部处理
	})

	// 场景3: 新达人任务，但已超过4周 (isSecond = true)，应该跳过
	t.Run("新达人_超过4周_跳过", func(t *testing.T) {
		ctrlSub, mgrSub, mocksSub := setupAnchorLevelMgr(t)
		defer ctrlSub.Finish()

		checkListItem := mockCheckListItemBase
		// 创建时间在四周前更早 (例如5周前周三创建，到本周一，isSecond会为true)
		// weekBeginTime.AddDate(0, 0, -7*4-4) 是四周前的周四0点
		// 要使 isSecond 为 true, elem.CreateTime < weekBeginTime.AddDate(0,0,-7*4-4).Unix()
		// 例如，创建时间是3周前，那么 isSecond 为 true
		checkListItem.CreateTime = now.AddDate(0, 0, (-7 * 5)).Unix() // 5周前创建，isSecond = true
		checkListItem.TaskType = taskTypeNew                          // 非纯新/回归

		mocksSub.reporter.EXPECT().SendInfo("SendNewAnchorTaskReward start").Return(nil)
		mocksSub.dyconfig.EXPECT().GetFakeNowTime().Return(fakeNowTimestamp).AnyTimes()
		mocksSub.store.EXPECT().GetAnchorLevelNewTaskWithTimeRange(ctx, gomock.Any(), weekBeginTime.Unix()).Return([]*store.AnchorLevelNewTask{&checkListItem}, nil)
		mocksSub.dyconfig.EXPECT().GetFakeLevel(mockUID).Return("").AnyTimes()

		activeDaysResp := &channel_live_stats.GetAnchorDailyRecordWithDateListResp{
			List: []*channel_live_stats.AnchorDailyStats{ // 假设数据满足，但因为类型和时间被跳过
				{IsLiveActiveDay: true, AnchorIncome: 20000}, {IsLiveActiveDay: true, AnchorIncome: 20000},
				{IsLiveActiveDay: true, AnchorIncome: 20000}, {IsLiveActiveDay: true, AnchorIncome: 20000},
				{IsLiveActiveDay: true, AnchorIncome: 20000}, // 5 days, 100k income
			},
		}
		mocksSub.channelLiveStats.EXPECT().GetAnchorDailyRecordWithDateList(ctx, uint32(0), mockUID, uint32(lastWeekBeginTime.Unix()), uint32(lastWeekEndTime.Unix()-1)).Return(activeDaysResp, nil)
		// 不会调用 step2 或失败处理中的 GetLastCreateScore

		mocksSub.reporter.EXPECT().SendInfo("SendNewAnchorTaskReward end").Return(nil)
		err := mgrSub.SendNewAnchorTaskReward()
		assert.NoError(t, err)
	})

	// 场景4: 纯新达人，超过4周 (isSecond = true)，但仍处理
	t.Run("纯新达人_超过4周_仍然处理并发奖", func(t *testing.T) {
		ctrlSub, mgrSub, mocksSub := setupAnchorLevelMgr(t)
		defer ctrlSub.Finish()

		checkListItem := mockCheckListItemBase
		checkListItem.CreateTime = now.AddDate(0, 0, (-7 * 5)).Unix() // 5周前创建 (isSecond = true)
		checkListItem.TaskType = taskTypeTotalNew                     // 纯新达人

		mocksSub.reporter.EXPECT().SendInfo("SendNewAnchorTaskReward start").Return(nil)
		mocksSub.dyconfig.EXPECT().GetFakeNowTime().Return(fakeNowTimestamp).AnyTimes()
		mocksSub.store.EXPECT().GetAnchorLevelNewTaskWithTimeRange(gomock.Any(), gomock.Any(), weekBeginTime.Unix()).Return([]*store.AnchorLevelNewTask{&checkListItem}, nil)

		activeDaysResp := &channel_live_stats.GetAnchorDailyRecordWithDateListResp{
			List: []*channel_live_stats.AnchorDailyStats{
				{IsLiveActiveDay: true, AnchorIncome: 40000}, {IsLiveActiveDay: true, AnchorIncome: 40000},
				{IsLiveActiveDay: true, AnchorIncome: 40000}, {IsLiveActiveDay: true, AnchorIncome: 40000},
				{IsLiveActiveDay: true, AnchorIncome: 40000}, // 5 active days, income 200000
			},
		}
		mocksSub.channelLiveStats.EXPECT().GetAnchorDailyRecordWithDateList(gomock.Any(), uint32(0), mockUID, uint32(lastWeekBeginTime.Unix()), uint32(lastWeekEndTime.Unix()-1)).Return(activeDaysResp, nil)
		mocksSub.dyconfig.EXPECT().GetFakeLevel(mockUID).Return("").AnyTimes()

		mocksSub = setupStep2Mocks(mocksSub, taskTypeTotalNew, true, 200000) // isSecond true

		mocksSub.reporter.EXPECT().SendInfo("SendNewAnchorTaskReward end").Return(nil)
		err := mgrSub.SendNewAnchorTaskReward()
		assert.NoError(t, err)
	})

	// 场景6: 任务完成 - 满足最低条件 (isSecond=false, 周收入 50k-100k)
	t.Run("任务完成_isSecond_false_周收入50k到100k", func(t *testing.T) {
		ctrlSub, mgrSub, mocksSub := setupAnchorLevelMgr(t)
		defer ctrlSub.Finish()

		checkListItem := mockCheckListItemBase
		checkListItem.CreateTime = now.AddDate(0, 0, -7*2).Unix() // 2周前创建 (isSecond = true)
		checkListItem.TaskType = taskTypeComeback                 // 回归达人 (这样不会被 isSecond && taskTypeNew 的逻辑跳过)

		mocksSub.reporter.EXPECT().SendInfo("SendNewAnchorTaskReward start").Return(nil)
		mocksSub.dyconfig.EXPECT().GetFakeNowTime().Return(fakeNowTimestamp).AnyTimes()
		mocksSub.store.EXPECT().GetAnchorLevelNewTaskWithTimeRange(ctx, gomock.Any(), weekBeginTime.Unix()).Return([]*store.AnchorLevelNewTask{&checkListItem}, nil)
		mocksSub.dyconfig.EXPECT().GetFakeLevel(mockUID).Return("").AnyTimes()

		activeDaysResp := &channel_live_stats.GetAnchorDailyRecordWithDateListResp{
			List: []*channel_live_stats.AnchorDailyStats{
				{IsLiveActiveDay: true, AnchorIncome: 15000}, {IsLiveActiveDay: true, AnchorIncome: 15000},
				{IsLiveActiveDay: true, AnchorIncome: 15000}, {IsLiveActiveDay: true, AnchorIncome: 15000},
				{IsLiveActiveDay: true, AnchorIncome: 15000}, // 5 active days, income 75000
			},
		}
		// 最低条件: weekActiveDays >= NeedWeekActiveDays && ((weekGiftValue >= 50000 && isSecond) || weekGiftValue >= 100000)
		// 当前: 5 >= 5 && ((75000 >= 50000 && true) || 75000 >= 100000) => 5 >= 5 && (true || false) => true. 应该发奖
		mocksSub.channelLiveStats.EXPECT().GetAnchorDailyRecordWithDateList(ctx, uint32(0), mockUID, uint32(lastWeekBeginTime.Unix()), uint32(lastWeekEndTime.Unix()-1)).Return(activeDaysResp, nil)

		mocksSub = setupStep2Mocks(mocksSub, taskTypeComeback, true, 75000) // isSecond true, income 75000

		mocksSub.reporter.EXPECT().SendInfo("SendNewAnchorTaskReward end").Return(nil)
		err := mgrSub.SendNewAnchorTaskReward()
		assert.NoError(t, err)
	})

	// 场景7: GetAnchorLevelNewTaskWithTimeRange 失败
	t.Run("GetAnchorLevelNewTaskWithTimeRange失败", func(t *testing.T) {
		ctrlSub, mgrSub, mocksSub := setupAnchorLevelMgr(t)
		defer ctrlSub.Finish()

		expectedErr := errors.New("db error")
		mocksSub.reporter.EXPECT().SendInfo("SendNewAnchorTaskReward start").Return(nil)
		mocksSub.dyconfig.EXPECT().GetFakeNowTime().Return(fakeNowTimestamp).AnyTimes()
		mocksSub.store.EXPECT().GetAnchorLevelNewTaskWithTimeRange(ctx, gomock.Any(), weekBeginTime.Unix()).Return(nil, expectedErr)
		// 没有后续的 reporter.SendInfo("SendNewAnchorTaskReward end") 因为函数提前返回

		err := mgrSub.SendNewAnchorTaskReward()
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
	})
}

func SendImMock(mocksSub *MockMgr, mockUid uint32) *MockMgr {
	basePublicRsp := &PublicAccount.GetPublicAccountsByBindedIdListResp{
		PublicAccountList: []*PublicAccount.StPublicAccount{
			{PublicId: 9001, Name: "达人服务号"},
			{PublicId: 9002, Name: "其他服务号"},
		},
	}
	mocksSub.publicCli.EXPECT().GetPublicAccountsByBindedIdList(gomock.Any(), uint32(0), gomock.Any()).Return(basePublicRsp, nil).AnyTimes()
	mocksSub.userOlCli.EXPECT().GetLastMobileOnlineInfo(gomock.Any(), mockUid).Return(&userolpb.OnlineInfo{
		Uid:          mockUid,
		MarketId:     0,
		TerminalType: 0,
	}, nil).AnyTimes()

	mocksSub.dyconfig.EXPECT().GetAppName(uint32(0)).Return("tt").AnyTimes()
	mocksSub.dyconfig.EXPECT().GetLiveAnchorTaskUrl(uint32(0), uint8(0)).Return("testUrl").AnyTimes()
	mocksSub.apiCli.EXPECT().SendImMsg(gomock.Any(), mockUid, protocol.TT, gomock.Any(), true).Return(nil).AnyTimes()

	return mocksSub
}

func TestAnchorLevelMgr_GetAnchorLevelNewTask(t *testing.T) {
	ctrl, mgr, mocks := setupAnchorLevelMgr(t)
	defer ctrl.Finish()

	ctx := context.Background()
	mockUID := uint32(1001)
	now := time.Date(2023, 8, 14, 10, 0, 0, 0, time.FixedZone("CST", 8*60*60)) // 周一 10:00 CST
	taskCreateTime := now.AddDate(0, 0, -10)                                   // 10天前创建的任务
	taskBeginTime := utils.GetAnchorNewBeginTime(taskCreateTime.Unix())

	defaultStoreData := &store.AnchorLevelNewTask{
		Uid:        mockUID,
		CreateTime: taskCreateTime.Unix(),
		TaskType:   taskTypeNew,
	}

	t.Run("成功获取任务信息_进行中", func(t *testing.T) {
		mocks.store.EXPECT().GetAnchorLevelNewTask(ctx, mockUID).Return(defaultStoreData, nil)
		mocks.dyconfig.EXPECT().GetFakeNowTime().Return(int64(now.Unix())) // No fake time

		weekBeginTime := utils.GetThisWeekBeginTime(now)
		activeDaysResp := &channel_live_stats.GetAnchorDailyRecordWithDateListResp{
			List: []*channel_live_stats.AnchorDailyStats{
				{IsLiveActiveDay: true, AnchorIncome: 1000},
				{IsLiveActiveDay: false, AnchorIncome: 500}, // total 1 day, income 1500
			},
		}

		// 当前是周一10点，weekEndTime 是 now, weekBeginTime 是本周一0点
		mocks.channelLiveStats.EXPECT().GetAnchorDailyRecordWithDateList(ctx, uint32(0), mockUID, uint32(weekBeginTime.Unix()), uint32(now.Unix()-1)).Return(activeDaysResp, nil)
		mocks.anchorCheckCli.EXPECT().GetLastCreateScore(ctx, mockUID).Return(&anchor_check2.LastCreateScore{Level: "B"}, nil)
		mocks.dyconfig.EXPECT().GetFakeLevel(mockUID).Return("")

		resp, err := mgr.GetAnchorLevelNewTask(ctx, mockUID)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, uint32(taskTypeNew), resp.TaskType)
		assert.Equal(t, uint32(1500), resp.WeekGiftValue)
		assert.Equal(t, uint32(NeedWeekActiveDays-1), resp.RemainWeekActiveDays)
		assert.Equal(t, "B", resp.AnchorCheckLevel)
		expectedWeekNum := getWeekNum(now, taskBeginTime)
		assert.Equal(t, uint32(expectedWeekNum), resp.WeekNum)
		expectedRemainTime := uint32(taskBeginTime.Unix() + TotalNewTaskTotalTimeSec - now.Unix())
		assert.Equal(t, expectedRemainTime, resp.RemainTime)
	})

	t.Run("无任务记录", func(t *testing.T) {
		mocks.store.EXPECT().GetAnchorLevelNewTask(ctx, mockUID).Return(&store.AnchorLevelNewTask{}, nil) // Uid = 0

		resp, err := mgr.GetAnchorLevelNewTask(ctx, mockUID)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, uint32(3), resp.TaskType) // 默认值
		assert.Equal(t, uint32(0), resp.WeekNum)
	})

	t.Run("任务已过期", func(t *testing.T) {
		expiredCreateTime := now.Add(-(TotalNewTaskTotalTimeSec + 86400*10) * time.Second) // 很久以前创建
		//expiredTaskBeginTime := utils.GetAnchorNewBeginTime(expiredCreateTime.Unix())
		storeDataExpired := &store.AnchorLevelNewTask{
			Uid:        mockUID,
			CreateTime: expiredCreateTime.Unix(), // 确保 taskBeginTime + TotalNewTaskTotalTimeSec < now
			TaskType:   taskTypeNew,
		}
		mocks.store.EXPECT().GetAnchorLevelNewTask(ctx, mockUID).Return(storeDataExpired, nil)
		mocks.dyconfig.EXPECT().GetFakeNowTime().Return(int64(now.Unix()))

		// 预期日志: GetAnchorLevelNewTask ignore
		resp, err := mgr.GetAnchorLevelNewTask(ctx, mockUID)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, uint32(3), resp.TaskType)
		assert.Equal(t, uint32(0), resp.RemainTime) // 过期后 RemainTime 也是0 （因为提前返回了）
	})

	t.Run("周一8点前_显示上周数据", func(t *testing.T) {
		mondayEarly := time.Date(2023, 8, 14, 7, 0, 0, 0, time.FixedZone("CST", 8*60*60)) // 周一 07:00 CST
		// 任务创建时间需要早于 mondayEarly 至少8小时， чтобы tNow.Sub(taskBeginTime) > time.Hour*8
		taskCreateForMondayEarly := mondayEarly.AddDate(0, 0, -5) // 5天前创建
		taskBeginTimeForMondayEarly := utils.GetAnchorNewBeginTime(taskCreateForMondayEarly.Unix())

		storeDataMonday := &store.AnchorLevelNewTask{
			Uid:        mockUID,
			CreateTime: taskCreateForMondayEarly.Unix(), // 确保 tNow.Sub(taskBeginTime) > time.Hour*8
			TaskType:   taskTypeComeback,
		}
		mocks.store.EXPECT().GetAnchorLevelNewTask(ctx, mockUID).Return(storeDataMonday, nil)
		mocks.dyconfig.EXPECT().GetFakeNowTime().Return(mondayEarly.Unix()) // 使用 fake time

		// 此时 weekBeginTime 应该是上周一0点, weekEndTime 是本周一0点
		expectedWeekBeginTime := utils.GetThisWeekBeginTime(mondayEarly).AddDate(0, 0, -7)
		expectedWeekEndTime := utils.GetThisWeekBeginTime(mondayEarly)

		activeDaysResp := &channel_live_stats.GetAnchorDailyRecordWithDateListResp{List: []*channel_live_stats.AnchorDailyStats{}}
		mocks.channelLiveStats.EXPECT().GetAnchorDailyRecordWithDateList(ctx, uint32(0), mockUID, uint32(expectedWeekBeginTime.Unix()), uint32(expectedWeekEndTime.Unix()-1)).Return(activeDaysResp, nil)
		mocks.anchorCheckCli.EXPECT().GetLastCreateScore(ctx, mockUID).Return(&anchor_check2.LastCreateScore{Level: "A"}, nil)
		mocks.dyconfig.EXPECT().GetFakeLevel(mockUID).Return("S").AnyTimes() // Fake level

		resp, err := mgr.GetAnchorLevelNewTask(ctx, mockUID)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, "S", resp.AnchorCheckLevel)
		expectedWeekNum := getWeekNum(mondayEarly, taskBeginTimeForMondayEarly)
		assert.Equal(t, uint32(expectedWeekNum), resp.WeekNum)
	})

	t.Run("任务尚未开始", func(t *testing.T) {
		futureCreateTime := now.Add(86400 * 5 * time.Second)                        // 未来5天后才“创建”（即考核通过）
		futureTaskBeginTime := utils.GetAnchorNewBeginTime(futureCreateTime.Unix()) // 这会是下周一8点

		storeDataFuture := &store.AnchorLevelNewTask{
			Uid:        mockUID,
			CreateTime: futureCreateTime.Unix(), // 任务将在未来开始
			TaskType:   taskTypeTotalNew,
		}
		mocks.store.EXPECT().GetAnchorLevelNewTask(ctx, mockUID).Return(storeDataFuture, nil)
		mocks.dyconfig.EXPECT().GetFakeNowTime().Return(int64(now.Unix()))

		// GetAnchorDailyRecordWithDateList 和 GetLastCreateScore 仍然会被调用
		weekBeginTimeForFuture := utils.GetThisWeekBeginTime(now)
		activeDaysResp := &channel_live_stats.GetAnchorDailyRecordWithDateListResp{List: []*channel_live_stats.AnchorDailyStats{}}
		mocks.channelLiveStats.EXPECT().GetAnchorDailyRecordWithDateList(ctx, uint32(0), mockUID, uint32(weekBeginTimeForFuture.Unix()), uint32(now.Unix()-1)).Return(activeDaysResp, nil)
		mocks.anchorCheckCli.EXPECT().GetLastCreateScore(ctx, mockUID).Return(&anchor_check2.LastCreateScore{Level: "C"}, nil)
		mocks.dyconfig.EXPECT().GetFakeLevel(mockUID).Return("").AnyTimes()

		resp, err := mgr.GetAnchorLevelNewTask(ctx, mockUID)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, uint32(0), resp.WeekNum) // 尚未开始，WeekNum 为 0
		expectedRemainTime := uint32(futureTaskBeginTime.Unix() + TotalNewTaskTotalTimeSec - now.Unix())
		assert.Equal(t, expectedRemainTime, expectedRemainTime)
	})
}

//
//func TestAnchorLevelMgr_SetAnchorCheckPass(t *testing.T) {
//	ctrl, mgr, mocks := setupAnchorLevelMgr(t)
//	defer ctrl.Finish()
//
//	ctx := context.Background()
//	mockUID := uint32(2001)
//	//now := time.Now() // 固定一个时间点，避免测试中的不确定性
//	fixedNow := time.Date(2023, 8, 15, 14, 0, 0, 0, time.FixedZone("CST", 8*60*60))
//
//	// 辅助函数来 mock GetAnchorBaseInfo
//	mockGetAnchorBaseInfo := func(firstLiveTs, lastLiveAt int64) {
//		mocks.channelLiveStats.EXPECT().GetAnchorBaseInfo(gomock.Any(), uint32(0), mockUID).Return(
//			&channel_live_stats.GetAnchorBaseInfoResp{
//				Info: &channel_live_stats.AnchorBaseInfo{FirstLiveTs: uint32(firstLiveTs), LastLiveAt: uint32(lastLiveAt)},
//			}, nil)
//		mocks.dyconfig.EXPECT().GetFakeNowTime().Return(int64(fixedNow.Unix())).AnyTimes()
//
//		return
//	}
//
//	// 辅助函数来 mock CheckIsTotalNewAnchor (因为这个方法本身有测试，这里可以直接 mock 结果)
//	mockCheckIsTotalNew := func(isTotalNew bool, err error) {
//		// CheckIsTotalNewAnchor 是 mgr 的方法，不能直接 mock。
//		// 需要 mock 其内部依赖 ttcProxyClient 和 anchorcontractGoClient
//		// 为了简化 SetAnchorCheckPass 的测试，这里假设 CheckIsTotalNewAnchor 的行为
//		// 如果 isTotalNew 为 true (即非纯新)，则 ttcProxyClient 会返回导致其为 true 的数据
//		if !isTotalNew { // 意味着其他账号已签约
//			mocks.ttcProxyClient.EXPECT().GetTheSameRealNameUserList(gomock.Any(), uint64(mockUID)).Return(&ttc_proxy2.GetTheSameRealNameUserListResp{Uids: []uint32{100000}}, nil).AnyTimes()
//			mocks.anchorcontractGoClient.EXPECT().GetIdentityChangeHistory(gomock.Any(), gomock.Any()).Return(&anchorcontract_go.GetIdentityChangeHistoryResp{
//				List: []*anchorcontract_go.IdentityChangeInfo{{Uid: 100000, GuildId: 1}}, // 其他账号已签约
//			}, nil).AnyTimes()
//		} else { // 意味着是纯新
//			mocks.ttcProxyClient.EXPECT().GetTheSameRealNameUserList(gomock.Any(), uint64(mockUID)).Return(&ttc_proxy2.GetTheSameRealNameUserListResp{}, nil).AnyTimes()
//			// 不需要 mock GetIdentityChangeHistory 因为列表为空
//		}
//		// 如果 err != nil, 则模拟 ttcProxyClient 出错
//		if err != nil {
//			mocks.ttcProxyClient.EXPECT().GetTheSameRealNameUserList(gomock.Any(), uint64(mockUID)).Return(nil, err).AnyTimes()
//		}
//
//		return
//	}
//
//	mockCommonInfra := func() {
//		mocks.channelLiveMgr.EXPECT().GetChannelLiveInfo(gomock.Any(), mockUID, false).Return(&channel_live_mgr.GetChannelLiveInfoResp{
//			ChannelLiveInfo: &channel_live_mgr.ChannelLiveInfo{ChannelId: 20, TagId: 20},
//		}, nil)
//		mocks.anchorcontractGoClient.EXPECT().GetUserContractCacheInfo(gomock.Any(), uint32(0), mockUID).Return(&anchorcontract_go.ContractCacheInfo{
//			Contract: &anchorcontract_go.ContractInfo{GuildId: 1},
//		}, nil)
//		mocks.store.EXPECT().InsertAnchorLevelNewTask(gomock.Any(), mockUID, gomock.Any()).Return(nil) // taskType 会变化
//	}
//
//	t.Run("老主播_不发任务", func(t *testing.T) {
//		// FirstLiveTs 很久以前, LastLiveAt 最近但不足6个月前
//		firstLiveTs := fixedNow.Add(-4 * MonthSec * time.Second).Unix()
//		lastLiveAt := fixedNow.Add(-3 * MonthSec * time.Second).Unix()
//		mockGetAnchorBaseInfo(firstLiveTs, lastLiveAt)
//		// 不需要 mock CheckIsTotalNewAnchor 因为会提前返回
//
//		err := mgr.SetAnchorCheckPass(ctx, mockUID, "A")
//		assert.NoError(t, err)
//	})
//
//	time.Sleep(time.Second)
//
//	t.Run("纯新主播_考核S级_首次通过", func(t *testing.T) {
//		firstLiveTs := fixedNow.Add(-10 * 86400 * time.Second).Unix() // 10天内首次开播
//		mockGetAnchorBaseInfo(firstLiveTs, firstLiveTs)               // lastLiveAt 不重要
//		mockCheckIsTotalNew(true, nil)                                // 是纯新 (CheckIsTotalNewAnchor 返回 false)
//
//		mockCommonInfra()
//		mocks.store.EXPECT().GetAnchorLevelNewTask(ctx, mockUID).Return(&store.AnchorLevelNewTask{}, nil) // 首次，无旧任务
//
//		// S/A级奖励
//		mocks.channelLiveMgr.EXPECT().SetAuthFlag(gomock.Any(), gomock.Any()).Return(&channel_live_mgr.SetAuthFlagResp{}, nil)
//		// GrantFlowCard (纯新)
//		mocks.channelRecommendSvr.EXPECT().GrantFlowCard(gomock.Any(), gomock.Any()).DoAndReturn(
//			func(ctx context.Context, req *channel_recommend_svr2.GrantFlowCardReq) (*channel_recommend_svr2.GrantFlowCardResp, error) {
//				//assert.Equal(t, mockUID, req.Info.Id)
//				//assert.Equal(t, uint32(2), req.Info.Cnt) // S/A级纯新是2张
//				//assert.Equal(t, uint32(channel_recommend_svr2.RecommendLevel_Recommend_Level_A), req.Info.Level)
//				return nil, nil
//			}).AnyTimes()
//		mocks.entertainmentRecommendBack.EXPECT().AddLivePrepareChannel(gomock.Any(), uint32(0), gomock.Any()).Return(nil, nil)
//
//		SendImMock(mocks, mockUID) // For handleSetAnchorCheckPassPushMsg
//
//		// dyconfig.GetFakeNowTime for SetAnchorCheckPass and subsequent calls
//		mocks.dyconfig.EXPECT().GetFakeNowTime().Return(fixedNow.Unix()).AnyTimes() // For utils.GetAnchorNewBeginTime in push msg
//
//		err := mgr.SetAnchorCheckPass(ctx, mockUID, "S")
//		assert.NoError(t, err)
//	})
//
//	time.Sleep(time.Second)
//	t.Run("回归主播_考核A级_首次通过", func(t *testing.T) {
//		firstLiveTs := fixedNow.Add(-10 * MonthSec * time.Second).Unix() // 很久以前首次开播
//		lastLiveAt := fixedNow.Add(-7 * MonthSec * time.Second).Unix()   // 6个月前以上最后开播
//		mockGetAnchorBaseInfo(firstLiveTs, lastLiveAt)
//		// 回归主播不检查 CheckIsTotalNewAnchor
//
//		mockCommonInfra()
//		mocks.store.EXPECT().GetAnchorLevelNewTask(ctx, mockUID).Return(&store.AnchorLevelNewTask{}, nil)
//
//		mocks.channelLiveMgr.EXPECT().SetAuthFlag(gomock.Any(), gomock.Any()).Return(&channel_live_mgr.SetAuthFlagResp{}, nil)
//		mocks.channelRecommendSvr.EXPECT().GrantFlowCard(gomock.Any(), gomock.Any()).DoAndReturn(
//			func(ctx context.Context, req *channel_recommend_svr2.GrantFlowCardReq) (*channel_recommend_svr2.GrantFlowCardResp, error) {
//				assert.Equal(t, uint32(2), req.Info.Cnt) // A级回归是2张
//				return nil, nil
//			}).AnyTimes()
//		mocks.entertainmentRecommendBack.EXPECT().AddLivePrepareChannel(gomock.Any(), uint32(0), gomock.Any()).Return(nil, nil)
//		SendImMock(mocks, mockUID)
//		mocks.dyconfig.EXPECT().GetFakeNowTime().Return(fixedNow.Unix()).AnyTimes()
//
//		err := mgr.SetAnchorCheckPass(ctx, mockUID, "A")
//		assert.NoError(t, err)
//	})
//
//	time.Sleep(time.Second)
//
//	t.Run("新主播_考核B级_首次通过", func(t *testing.T) {
//		firstLiveTs := fixedNow.Add(-15 * 86400 * time.Second).Unix() // 15天内首次开播
//		mockGetAnchorBaseInfo(firstLiveTs, firstLiveTs)
//		mockCheckIsTotalNew(false, nil) // 是纯新 (CheckIsTotalNewAnchor 返回 false)
//
//		mockCommonInfra()
//		mocks.store.EXPECT().GetAnchorLevelNewTask(ctx, mockUID).Return(&store.AnchorLevelNewTask{}, nil) // 首次
//
//		// B/C级，调用 InitAnchorLevelNewTaskAward
//		mocks.store.EXPECT().InitAnchorLevelNewTaskAward(ctx, mockUID, "B", uint32(taskTypeNew)).Return(nil)
//		// 新主播B级，不发流量卡 (GrantFlowCard 不被调用)
//
//		//SendImMock(mocks, mockUID)
//		mocks.dyconfig.EXPECT().GetFakeNowTime().Return(fixedNow.Unix()).AnyTimes()
//
//		err := mgr.SetAnchorCheckPass(ctx, mockUID, "B")
//		assert.NoError(t, err)
//	})
//
//	time.Sleep(time.Second)
//	t.Run("纯新主播_考核B级_首次通过_发1张B卡", func(t *testing.T) {
//		firstLiveTs := fixedNow.Add(-5 * 86400 * time.Second).Unix()
//		mockGetAnchorBaseInfo(firstLiveTs, firstLiveTs)
//		mockCheckIsTotalNew(true, nil) // 是纯新 (CheckIsTotalNewAnchor 返回 false)
//
//		mockCommonInfra()
//		mocks.store.EXPECT().GetAnchorLevelNewTask(ctx, mockUID).Return(&store.AnchorLevelNewTask{}, nil)
//		mocks.store.EXPECT().InitAnchorLevelNewTaskAward(ctx, mockUID, "B", uint32(taskTypeTotalNew)).Return(nil)
//		mocks.channelRecommendSvr.EXPECT().GrantFlowCard(gomock.Any(), gomock.Any()).DoAndReturn(
//			func(ctx context.Context, req *channel_recommend_svr2.GrantFlowCardReq) (*channel_recommend_svr2.GrantFlowCardResp, error) {
//				//assert.Equal(t, uint32(1), req.Info.Cnt) // B级纯新/回归是1张
//				//assert.Equal(t, uint32(channel_recommend_svr2.RecommendLevel_Recommend_Level_B), req.Info.Level)
//				return nil, nil
//			})
//
//		SendImMock(mocks, mockUID)
//		mocks.dyconfig.EXPECT().GetFakeNowTime().Return(fixedNow.Unix()).AnyTimes()
//		err := mgr.SetAnchorCheckPass(ctx, mockUID, "B")
//		assert.NoError(t, err)
//	})
//
//	time.Sleep(time.Second)
//	t.Run("已存在任务记录_不发推送消息", func(t *testing.T) {
//		firstLiveTs := fixedNow.Add(-10 * 86400 * time.Second).Unix()
//		mockGetAnchorBaseInfo(firstLiveTs, firstLiveTs)
//		mockCheckIsTotalNew(false, nil) // 是纯新 (CheckIsTotalNewAnchor 返回 false)
//
//		mockCommonInfra()
//		mocks.store.EXPECT().GetAnchorLevelNewTask(ctx, mockUID).Return(&store.AnchorLevelNewTask{Uid: mockUID, CreateTime: fixedNow.Add(-86400 * time.Second).Unix()}, nil) // 已有任务
//
//		// S/A级奖励逻辑
//		mocks.channelLiveMgr.EXPECT().SetAuthFlag(gomock.Any(), gomock.Any()).Return(&channel_live_mgr.SetAuthFlagResp{}, nil)
//		mocks.channelRecommendSvr.EXPECT().GrantFlowCard(gomock.Any(), gomock.Any()).Return(nil, nil)
//		mocks.entertainmentRecommendBack.EXPECT().AddLivePrepareChannel(gomock.Any(), uint32(0), gomock.Any()).Return(nil, nil)
//		// SendImMock 不会被调用因为 oldTask.CreateTime != 0
//
//		err := mgr.SetAnchorCheckPass(ctx, mockUID, "S")
//		assert.NoError(t, err)
//	})
//}
