syntax = "proto3";

package AnchorLevel;

service AnchorLevel {
    // 获取新主播任务状态
    rpc GetAnchorLevelNewTask(UidReq) returns (AnchorLevelNewTask) {}
    // 获取麦位任务
    rpc GetAnchorMicPosTask(UidReq) returns (AnchorMicPosTask) {}
    // 设置主播考核通过
    rpc SetAnchorCheckPass(UidCheckLevelReq) returns (Empty) {}
    // 手动触发麦位任务奖励
    rpc ManualSendMicPosReward(Empty) returns (Empty) {}
    // 手动触发新主播任务奖励
    rpc ManualSendNewAnchorTaskReward(Empty) returns (Empty) {}
    // 手动触发麦位任务提醒
    rpc ManualSendAllNotMicPosMsg(Empty) returns (Empty) {}

    // 直播间主播任务入口
    rpc GetLiveAnchorTaskEntry(GetLiveAnchorTaskEntryReq) returns (GetLiveAnchorTaskEntryResp) {}

    rpc GetLiveAnchorLevel (GetLiveAnchorLevelReq) returns (GetLiveAnchorLevelResp) {}
    rpc GetLiveAnchorLevelByUid (GetLiveAnchorLevelByUidReq) returns (GetLiveAnchorLevelByUidResp) {}
    rpc GetAnchorLevelMonthTask(UidReq) returns (GetAnchorLevelMonthTaskResp) {}
    rpc GetAnchorLevel(UidReq) returns (GetAnchorLevelResp) {} // 获取实时主播等级
 
    // 补发主播等级(包含发奖)
    rpc ReplenishAnchorLevel (ReplenishAnchorLevelReq) returns (ReplenishAnchorLevelResp) {}
    // 手动结算主播等级(包含发奖)
    rpc ManualSettleAnchorLevel(ManualSettleAnchorLevelReq) returns (Empty) {}


    rpc SettleNewTaskAward(SettleNewTaskAwardReq) returns (Empty) {}
}

message UidReq {
    uint32 uid = 1;
}
message ManualSettleAnchorLevelReq {
    uint32 settle_month = 1;
    repeated uint32 uids = 2; 
}

message UidCheckLevelReq {
    uint32 uid = 1;
    string check_level = 2;
}

message Empty {
}

message AnchorLevelNewTask {
    uint32 remain_time = 1;
    uint32 need_week_active_days = 2;
    uint32 remain_week_active_days = 3;
    uint32 need_week_platform_days = 4;
    uint32 remain_week_platform_days = 5;
    uint32 week_gift_value = 6;
    string anchor_check_level = 7; // 新主播考核等级
}

message AnchorMicPosTask {
    bool show = 1;
    uint32 need_week_active_days = 2;
    uint32 remain_week_active_days = 3;
    uint32 need_week_platform_days = 4;
    uint32 remain_week_platform_days = 5;
    uint32 week_gift_value = 6; // 本周收礼值
}

message GetLiveAnchorTaskEntryReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
}
message GetLiveAnchorTaskEntryResp{
    string title = 2; // 入口文案 此字段为空 则入口不可见
    string jump_url = 3; // 跳转url
    string base_imgurl = 4;  // 底图
}


enum ANCHOR_LEVEL_TYPE {
    ANCHOR_LEVEL_TYPE_INVALID = 0;
    ANCHOR_LEVEL_TYPE_A = 1; // 见习主播
    ANCHOR_LEVEL_TYPE_B = 2; // 新锐主播
    ANCHOR_LEVEL_TYPE_C = 3; // 精英主播
    ANCHOR_LEVEL_TYPE_D = 4; // 大师主播
    ANCHOR_LEVEL_TYPE_E = 5; // 传奇主播
}

message GetLiveAnchorLevelReq {
    ANCHOR_LEVEL_TYPE level = 1; // 传0取所有
}

message LiveAnchorLevelInfo {
    uint32 uid = 1;
    ANCHOR_LEVEL_TYPE level = 2;
    string item_name = 3;
    string base_imgurl = 4;     // 标识底图
    string shadow_color = 5;    // 配置文字投影颜色
    uint32 start_time = 6;      // 生效时间
    uint32 end_time = 7;        // 过期时间
}

message GetLiveAnchorLevelResp {
    repeated LiveAnchorLevelInfo list = 1;
}


message GetLiveAnchorLevelByUidReq {
    repeated uint32 uids = 1;
}
message GetLiveAnchorLevelByUidResp {
    map<uint32,LiveAnchorLevelInfo> uid2AnchorLevel = 1;
}

message GetAnchorLevelMonthTaskResp {
  string curr_level_name = 1;
  uint32 need_month_active_days = 3;
  uint32 remain_month_active_days = 4;
  uint32 need_month_platform_days = 5;
  uint32 remain_month_platform_days = 6;
  uint32 month_new_fans_cnt = 7;
  uint32 month_income_value = 8;
  uint32 need_month_income_value = 9;
  uint32 month_consumer_cnt = 10;
  bool is_violation = 11;
  uint32 month_active_days = 12; // 当前月直播活跃天数
}

message TestSendImReq {
    uint32 uid=1;
    uint32 level=2;
}


message ReplenishAnchorLevelReq {
    message ReplenishAnchorLevelInfo {
        uint32 anchor_uid = 1;
        uint32 anchor_level = 2; // ANCHOR_LEVEL_TYPE
        uint32 advanced_task_done = 3; // 是否发进阶任务奖励
    }
    uint32 settle_month = 1;
    repeated ReplenishAnchorLevelInfo info_list = 2;
}
message ReplenishAnchorLevelResp {
}

message GetAnchorLevelResp {
    uint32 anchor_uid = 1;
    ANCHOR_LEVEL_TYPE anchor_level = 2; // ANCHOR_LEVEL_TYPE
}

message SettleNewTaskAwardReq {}