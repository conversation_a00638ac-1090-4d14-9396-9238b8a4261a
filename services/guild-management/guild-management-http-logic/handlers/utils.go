package hanlders

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"golang.52tt.com/clients/account"
	utils2 "golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	ga "golang.52tt.com/protocol/app/channel"
	anchorcontractPb "golang.52tt.com/protocol/services/anchorcontract-go"
	channellivestats "golang.52tt.com/protocol/services/channel-live-stats"
	channelscheme "golang.52tt.com/protocol/services/channel-scheme"
	liveMgrPb "golang.52tt.com/protocol/services/channellivemgr"
	Channel "golang.52tt.com/protocol/services/channelsvr"
	enterPb "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	cooperationPB "golang.52tt.com/protocol/services/guild-cooperation"
	guild_management_svr "golang.52tt.com/protocol/services/guild-management-svr"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/models"
	api "golang.52tt.com/services/guild-management/guild-management-http-logic/models/gen-go"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/utils"
	"golang.org/x/sync/errgroup"
)

var NoValidRatio float32 = -2
var GetImMsgTotal uint64 = 20000
var GetImMsgLimit uint64 = 2000

var mapAnchorType2Flag = map[api.AnchorType]uint32{
	api.AnchorType_DeFaultAnchor:        uint32(channellivestats.AnchorFlag_Common), // 签约主播
	api.AnchorType_NewSignAnchor:        uint32(anchorcontractPb.ANCHOR_FLAG_NewSignAnchor),
	api.AnchorType_TodayLiveAnchor:      uint32(channellivestats.AnchorFlag_TodayLive),
	api.AnchorType_BreakLiveAnchor:      uint32(channellivestats.AnchorFlag_BreakLive),
	api.AnchorType_SoonSignExpireAnchor: uint32(anchorcontractPb.ANCHOR_FLAG_SoonSignExpireAnchor),
	api.AnchorType_MonthPotentailAnchor: uint32(channellivestats.AnchorFlag_Potential),
	api.AnchorType_MonthValidAnchor:     uint32(channellivestats.AnchorFlag_Valid),
	api.AnchorType_MonthNewValidAnchor:  uint32(channellivestats.AnchorFlag_NewValid),
	api.AnchorType_MonthLiveAnchor:      uint32(channellivestats.AnchorFlag_Live), // 开播主播
}

var MapTagId2Name = map[uint32]string{
	0:    "",
	2003: "大神带飞",
	2010: "相亲交友",
	2002: "点唱厅",
	2001: "扩列",
	2012: "Pia戏",
	2005: "踢保",
	2007: "小故事",
	2016: "一起玩",
	2015: "大神带飞",
	2014: "CP战",
	2013: "派对",
	3001: "音乐",
	3002: "情感",
	3003: "二次元",
	3004: "故事",
	3005: "脱口秀",
}

var MapIndex2VType = map[int]api.ViolationsType{
	0: api.ViolationsType_Violations_A,
	1: api.ViolationsType_Violations_B,
	2: api.ViolationsType_Violations_C,
}

var MapTimeDimType2QueryStr = map[api.GetMultiPractitionerScheduleDataReq_TimeDimType]string{
	api.GetMultiPractitionerScheduleDataReq_TimeDimType_Hour:  "hour",
	api.GetMultiPractitionerScheduleDataReq_TimeDimType_Day:   "day",
	api.GetMultiPractitionerScheduleDataReq_TimeDimType_Week:  "week",
	api.GetMultiPractitionerScheduleDataReq_TimeDimType_Month: "month",
}

var MapEsportTimeDimType2QueryStr = map[api.EsportTimeDimType]string{
	api.EsportTimeDimType_EsportTimeDimType_Day:   "day",
	api.EsportTimeDimType_EsportTimeDimType_Week:  "week",
	api.EsportTimeDimType_EsportTimeDimType_Month: "month",
}

const (
	GuildAgentMaxCnt     = 130 // 公会经纪人最大数量
	AddAgentAnchorMaxCnt = 100 // 一次增加经纪人主播的最大数量

	QualityMinMonthValidDayCnt  uint32 = 20
	QualityMinMonthIncome       uint32 = 500000
	QualityMinMonthActiveDayCnt uint32 = 20
	QualityMinMonthPayChains    uint32 = 10

	maxMonthAViolationsCnt = 0
	maxMonthBViolationsCnt = 0
	maxMonthCViolationsCnt = 2
)

// 账号角色 移位定义
const (
	AccountRoleChairman = 1 // 会长
	AccountRoleBroker   = 2 // 经纪人
	AccountRoleAdmin    = 4 // 管理员
)

// 达人定义
const (
	PureNewActiveAnchorLiveActive = 4     // 纯新活跃达人听听活跃天
	PureNewActiveAnchorIncome     = 20000 // 纯新活跃达人听听收入

	PureNewProAnchorLiveActive = 10     // 纯新专业达人听听活跃天
	PureNewProAnchorIncome     = 200000 // 纯新专业达人听听收入

	MatureAnchorLiveActive = 20     // 成熟达人听听活跃天
	MatureAnchorIncome     = 500000 // 成熟达人听听收入

	PotentialActiveAnchorLiveActive = 20      // 潜力活跃达人听听活跃天
	PotentialActiveAnchorIncome     = 1000000 // 潜力活跃达人听听收入
)

// 语音直播经营总览需要权限字段
type GuildLiveStatsData struct {
	AnchorIncome       uint64
	ChannelFee         uint64
	ChannelPkgFeeRatio float32
	PureNewAnchorFee   uint64 // 纯新达人流水
}

// 语音直播主播数据需要权限字段
type AnchorData struct {
	ChannelFee           uint64
	AnchorIncome         uint64
	SignTs               uint32
	SignExpireTs         uint32
	ChannelPkgFeeRatio   float32
	AnchorPkgIncomeRatio float32
}

func FloatRound(f float64, n int) float64 {
	format := "%." + strconv.Itoa(n) + "f"
	res, _ := strconv.ParseFloat(fmt.Sprintf(format, f), 64)
	return res
}

func CulRatio(cntA, cntB int64) float32 {
	if cntB != 0 {
		return float32(FloatRound(float64(cntA-cntB)/float64(cntB), 4))
	}
	return NoValidRatio
}

func CulFloatRatio(cntA, cntB float64) float32 {
	if cntB != 0 {
		return float32(FloatRound((cntA-cntB)/cntB, 4))
	}
	return NoValidRatio
}

func CulRatioByN(cntA, cntB uint64, n int) float32 {
	if cntB != 0 {
		return float32(FloatRound(float64(cntA)/float64(cntB), n))
	}

	return 0
}

func CulHour(cntA, cntB int64, n int) float32 {
	if cntB != 0 {
		return float32(FloatRound(float64(cntA)/float64(cntB), n))
	}
	return NoValidRatio
}

func GetWeekDayOffset(tm time.Time) int {
	weekDayOffset := int(tm.Weekday())
	if weekDayOffset == 0 {
		weekDayOffset = 7
	}
	return weekDayOffset
}

// 检查是否是合作库会长
func CheckIsLibraryGuildChairman(ctx context.Context, uid, guildId uint32) (bool, protocol.ServerError) {
	sv, _ := protogrpc.ServiceInfoFromContext(ctx)
	sv.UserID = uid // 公会服务直接使用req_uid 字段判断, ctx提前注入了token的uid,这里重新注入

	isChairman, err := models.GetModelServer().GuildCli.CheckGuildChairman(ctx, uid, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckIsLibraryGuildChairman Failed to CheckGuildChairman uid:%d, guildId:%d, err %+v", uid, guildId, err)
		return false, err
	}

	if isChairman {
		// 还要检查是否是合作库
		resp, err := models.GetModelServer().GuildCoopCli.GetGuildCooperationInfos(ctx, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckIsLibraryGuildChairman Failed to GetGuildTypeInfo uid:%d, guildId:%d, err %+v", uid, guildId, err)
			return false, err
		}
		log.InfoWithCtx(ctx, "CheckIsLibraryGuildChairman uid=%d guildId=%d GetGuildCooperationInfos=%+v", uid, guildId, resp)

		if resp.GetIsAmuseCoopGuild() || resp.GetIsYuyinCoopGuild() || resp.GetIsEsportCoopGuild() {
			log.DebugWithCtx(ctx, "CheckIsLibraryGuildChairman is chairman uid:%d, guildId:%d", uid, guildId)
			return true, nil
		}
	}

	log.DebugWithCtx(ctx, "CheckIsLibraryGuildChairman end uid:%d, guildId:%d", uid, guildId)
	return false, nil
}

// 检查是否是合作库会长
func CheckIsLibraryGuildChairmanV2(ctx context.Context, uid, guildId uint32) (bool, *cooperationPB.GetGuildCooperationInfosResp, protocol.ServerError) {
	sv, _ := protogrpc.ServiceInfoFromContext(ctx)
	sv.UserID = uid // 公会服务直接使用req_uid 字段判断, ctx提前注入了token的uid,这里重新注入

	isChairman, err := models.GetModelServer().GuildCli.CheckGuildChairman(ctx, uid, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckIsLibraryGuildChairmanV2 Failed to CheckGuildChairman uid:%d, guildId:%d, err %+v", uid, guildId, err)
		return false, nil, err
	}
	// 还要检查是否是合作库
	cooperationInfo, err := models.GetModelServer().GuildCoopCli.GetGuildCooperationInfos(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckIsLibraryGuildChairmanV2 Failed to GetGuildTypeInfo uid:%d, guildId:%d, err %+v", uid, guildId, err)
		return false, cooperationInfo, err
	}
	log.InfoWithCtx(ctx, "CheckIsLibraryGuildChairmanV2 uid=%d guildId=%d isChairman=%v GetGuildCooperationInfos=%+v", uid, guildId, isChairman, cooperationInfo)

	if isChairman {
		if cooperationInfo.GetIsAmuseCoopGuild() || cooperationInfo.GetIsYuyinCoopGuild() || cooperationInfo.GetIsEsportCoopGuild() {
			log.DebugWithCtx(ctx, "CheckIsLibraryGuildChairmanV2 is chairman uid:%d, guildId:%d", uid, guildId)
			return true, cooperationInfo, nil
		}
	}

	log.DebugWithCtx(ctx, "CheckIsLibraryGuildChairmanV2 end uid:%d, guildId:%d", uid, guildId)
	return false, cooperationInfo, nil
}

// 获取上周起始时间
func GetLastWeekTime(now time.Time) (begin, end time.Time) {
	weekDay := int(now.Weekday())
	nowDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	thisMonday := nowDate.AddDate(0, 0, 1-weekDay)

	end = thisMonday.Add(-1 * time.Second)
	begin = thisMonday.AddDate(0, 0, -7)

	return
}

func GetTotalQueryType(anchorAccount, agentAccount string) api.TotalQueryType {
	if len(anchorAccount) == 0 && len(agentAccount) == 0 {
		return api.TotalQueryType_QueryAll
	}
	if len(anchorAccount) != 0 {
		return api.TotalQueryType_QueryByAnchor
	}
	if len(anchorAccount) == 0 && len(agentAccount) != 0 {
		return api.TotalQueryType_QueryByAgent
	}
	return api.TotalQueryType_QueryAll
}

func GetOutPermissionMsg(svrPermission guild_management_svr.GuildManagePermission) *api.GuildManagePermission {
	per := &api.GuildManagePermission{
		GuildManageMenuPer: &api.MenuPermission{
			GuildLiveDataMenuPer:  svrPermission.GetGuildManageMenuPer().GetGuildLiveDataMenuPer(),
			GuildAgentMenuPer:     svrPermission.GetGuildManageMenuPer().GetGuildAgentMenuPer(),
			GuildMultiPlayDataPer: svrPermission.GetGuildManageMenuPer().GetMultiPlay(),
			FlowCardMenuPer:       svrPermission.GetGuildManageMenuPer().GetFlowCardMenuPer(),
			AnchorContractMenuPer: svrPermission.GetGuildManageMenuPer().GetContract(),
			GuildMsgMenuPer:       svrPermission.GetGuildManageMenuPer().GetMsg(),
			GuildManageMenuPer:    svrPermission.GetGuildManageMenuPer().GetGuildManage(),
			EsportSkillMenuPer:    svrPermission.GetGuildManageMenuPer().GetEsportMenuPer(),
			HomePageMenuPer:       svrPermission.GetGuildManageMenuPer().GetHomePageMenuPer(),
		},
		LiveDataPermission: &api.LiveDataPermission{
			GuildLiveStatsDataPer: svrPermission.GetLiveDataPermission().GetGuildLiveStatsDataPer(),
			GuildTaskDataPer:      svrPermission.GetLiveDataPermission().GetGuildTaskDataPer(),
			AnchorDataPer:         svrPermission.GetLiveDataPermission().GetAnchorDataPer(),
			AnchorPracDataPer:     svrPermission.GetLiveDataPermission().GetAnchorPracDataPer(),
		},
		DataPermission: &api.DataPermission{
			GuildLiveStatsDataPer:        svrPermission.GetDataPer().GetLiveStats(),
			GuildTaskDataPer:             svrPermission.GetDataPer().GetTaskData(),
			AnchorDataPer:                svrPermission.GetDataPer().GetAnchorData(),
			AnchorPracDataPer:            svrPermission.GetDataPer().GetAnchorPrac(),
			MultiPlayDetailDataPer:       svrPermission.GetDataPer().GetMultiPlayDetail(),
			MultiPlayPracAnalysisDataPer: svrPermission.GetDataPer().GetMultiPlayPracAnalysis(),
		},
		FunctionPermission: &api.FunctionPermission{
			ContractApplySignFuncPer:       svrPermission.GetFunctionPer().GetApplySign(),
			ContractCancelFuncPer:          svrPermission.GetFunctionPer().GetCancelSign(),
			ContractListFuncPer:            svrPermission.GetFunctionPer().GetContractList(),
			ContractVioFuncPer:             svrPermission.GetFunctionPer().GetContractVio(),
			LiveTotalDataFuncPer:           svrPermission.GetFunctionPer().GetLiveTotalData(),
			LiveTaskDataFuncPer:            svrPermission.GetFunctionPer().GetLiveTaskData(),
			AnchorDataFuncPer:              svrPermission.GetFunctionPer().GetAnchorData(),
			AnchorPracAnalysisFuncPer:      svrPermission.GetFunctionPer().GetAnchorPracAnalysis(),
			FlowCardFuncPer:                svrPermission.GetFunctionPer().GetFlowCard(),
			MultiPlayDetailFuncPer:         svrPermission.GetFunctionPer().GetMultiPlayDetail(),
			MultiPlayPracAnalysisFuncPer:   svrPermission.GetFunctionPer().GetMultiPlayPracAnalysis(),
			GuildMsgFuncPer:                svrPermission.GetFunctionPer().GetMsg(),
			MultiPlayOperationFuncPer:      svrPermission.GetFunctionPer().GetMultiPlayOper(),
			MultiPlayHallTaskFuncPer:       svrPermission.GetFunctionPer().GetMultiPlayHallTask(),
			MultiScheduleDataFuncPer:       svrPermission.GetFunctionPer().GetMultiScheduleDataFuncPer(),
			LiveManageFuncPer:              svrPermission.GetFunctionPer().GetLiveManage(),
			EsportManageFuncPer:            svrPermission.GetFunctionPer().GetEsportManageSkillFuncPer(),
			EsportManagePerformanceFuncPer: svrPermission.GetFunctionPer().GetEsportManagePerformanceFuncPer(),
			BusinessDiagnosisFuncPer:       svrPermission.GetFunctionPer().GetBusinessDiag(),
			HomePageMultiFuncPer:           svrPermission.GetFunctionPer().GetHomePageMultiFuncPer(),
			MultiBusinessDiagFunc:          svrPermission.GetFunctionPer().GetMultiBusinessDiagFunc(),
		},
	}

	if per.GetFunctionPermission().GetBusinessDiagnosisFuncPer()&uint32(api.BusinessDiagnosisFuncType_BusinessDiagnosisQuery) != 0 {
		per.FunctionPermission.BusinessDiagnosisFuncPer = uint32(api.BusinessDiagnosisFuncType_BusinessDiagnosisAbility) + uint32(api.BusinessDiagnosisFuncType_BusinessDiagnosisMgr) +
			uint32(api.BusinessDiagnosisFuncType_BusinessDiagnosisRevenue) + uint32(api.BusinessDiagnosisFuncType_BusinessDiagnosisRecruit)
	}

	return per
}

func GetInPermissionMsg(permission api.GuildManagePermission) *guild_management_svr.GuildManagePermission {
	return &guild_management_svr.GuildManagePermission{
		GuildManageMenuPer: &guild_management_svr.MenuPermission{
			GuildLiveDataMenuPer: permission.GetGuildManageMenuPer().GetGuildLiveDataMenuPer(),
			GuildAgentMenuPer:    permission.GetGuildManageMenuPer().GetGuildAgentMenuPer(),
			FlowCardMenuPer:      permission.GetGuildManageMenuPer().GetFlowCardMenuPer(),
			Contract:             permission.GetGuildManageMenuPer().GetAnchorContractMenuPer(),
			MultiPlay:            permission.GetGuildManageMenuPer().GetGuildMultiPlayDataPer(),
			Msg:                  permission.GetGuildManageMenuPer().GetGuildMsgMenuPer(),
			GuildManage:          permission.GetGuildManageMenuPer().GetGuildManageMenuPer(),
			EsportMenuPer:        permission.GetGuildManageMenuPer().GetEsportSkillMenuPer(),
			HomePageMenuPer:      permission.GetGuildManageMenuPer().GetHomePageMenuPer(),
		},
		LiveDataPermission: &guild_management_svr.LiveDataPermission{
			GuildLiveStatsDataPer: permission.GetLiveDataPermission().GetGuildLiveStatsDataPer(),
			GuildTaskDataPer:      permission.GetLiveDataPermission().GetGuildTaskDataPer(),
			AnchorDataPer:         permission.GetLiveDataPermission().GetAnchorDataPer(),
			AnchorPracDataPer:     permission.GetLiveDataPermission().GetAnchorPracDataPer(),
		},
		DataPer: &guild_management_svr.DataPermission{
			LiveStats:             permission.GetDataPermission().GetGuildLiveStatsDataPer(),
			TaskData:              permission.GetDataPermission().GetGuildTaskDataPer(),
			AnchorData:            permission.GetDataPermission().GetAnchorDataPer(),
			AnchorPrac:            permission.GetDataPermission().GetAnchorPracDataPer(),
			MultiPlayDetail:       permission.GetDataPermission().GetMultiPlayDetailDataPer(),
			MultiPlayPracAnalysis: permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer(),
		},
		FunctionPer: &guild_management_svr.FunctionPermission{
			ApplySign:                      permission.GetFunctionPermission().GetContractApplySignFuncPer(),
			CancelSign:                     permission.GetFunctionPermission().GetContractCancelFuncPer(),
			ContractList:                   permission.GetFunctionPermission().GetContractListFuncPer(),
			ContractVio:                    permission.GetFunctionPermission().GetContractVioFuncPer(),
			LiveTotalData:                  permission.GetFunctionPermission().GetLiveTotalDataFuncPer(),
			LiveTaskData:                   permission.GetFunctionPermission().GetLiveTaskDataFuncPer(),
			AnchorData:                     permission.GetFunctionPermission().GetAnchorDataFuncPer(),
			AnchorPracAnalysis:             permission.GetFunctionPermission().GetAnchorPracAnalysisFuncPer(),
			FlowCard:                       permission.GetFunctionPermission().GetFlowCardFuncPer(),
			MultiPlayDetail:                permission.GetFunctionPermission().GetMultiPlayDetailFuncPer(),
			MultiPlayPracAnalysis:          permission.GetFunctionPermission().GetMultiPlayPracAnalysisFuncPer(),
			MultiScheduleDataFuncPer:       permission.GetFunctionPermission().GetMultiScheduleDataFuncPer(),
			Msg:                            permission.GetFunctionPermission().GetGuildMsgFuncPer(),
			MultiPlayOper:                  permission.GetFunctionPermission().GetMultiPlayOperationFuncPer(),
			MultiPlayHallTask:              permission.GetFunctionPermission().GetMultiPlayHallTaskFuncPer(),
			LiveManage:                     permission.GetFunctionPermission().GetLiveManageFuncPer(),
			EsportManageSkillFuncPer:       permission.GetFunctionPermission().GetEsportManageFuncPer(),
			EsportManagePerformanceFuncPer: permission.GetFunctionPermission().GetEsportManagePerformanceFuncPer(),
			BusinessDiag:                   permission.GetFunctionPermission().GetBusinessDiagnosisFuncPer(),
			HomePageMultiFuncPer:           permission.GetFunctionPermission().GetHomePageMultiFuncPer(),
			MultiBusinessDiagFunc:          permission.GetFunctionPermission().GetMultiBusinessDiagFunc(),
		},
	}
}

// 获取会长的权限
func GetChairManPermission(guildId uint32, coopInfo *cooperationPB.GetGuildCooperationInfosResp) *api.GuildManagePermission {
	var guildIncomeMenuPer uint32 = 0
	guildIncomeMenuPer += uint32(api.GuildIncomeMenuType_GuildIncomeManage)

	//parentGuildId, ok := models.GetModelServer().GetSignDyConfig().GetParentGuildId(guildId)
	//if ok && parentGuildId > 0 && parentGuildId != guildId {
	//	// 当前公会是子公会，不展示收益管理
	//} else {
	//	guildIncomeMenuPer += uint32(api.GuildIncomeMenuType_GuildIncomeManage)
	//}
	//
	//log.Debugf("GetChairManPermission guildId=%d parentGuildId=%d guildIncomeMenuPer=%d", guildId, parentGuildId, guildIncomeMenuPer)

	permission := &api.GuildManagePermission{
		GuildManageMenuPer: &api.MenuPermission{
			GuildLiveDataMenuPer: uint32(api.GuildLiveDataMenuType_GuildLiveStatsTotalData + api.GuildLiveDataMenuType_GuildLiveTaskData +
				api.GuildLiveDataMenuType_GuildAnchorData + api.GuildLiveDataMenuType_AnchorPractitionerAnalysis + api.GuildLiveDataMenuType_GuildBusinessDiagnosis +
				api.GuildLiveDataMenuType_AnchorFaceCheckDetail),
			GuildAgentMenuPer:  uint32(api.GuildAgentMenuType_GuildAgentManage),
			GuildIncomeMenuPer: guildIncomeMenuPer,
			GuildMultiPlayDataPer: uint32(api.GuildMultiPlayDataType_MultiPlayOperationData + api.GuildMultiPlayDataType_MultiPlayDetail +
				api.GuildMultiPlayDataType_MultiPlayPractitionerAnalysis +
				api.GuildMultiPlayDataType_MultiPlayHallTask + api.GuildMultiPlayDataType_MultiPlayScheduleData +
				api.GuildMultiPlayDataType_MultiAnchorFaceCheckDetail + api.GuildMultiPlayDataType_MultiPlayWeddingReserved),
			FlowCardMenuPer: uint32(api.FlowCardMenuType_AnchorFlowCard),
			AnchorContractMenuPer: uint32(api.AnchorContractMenuType_AnchorContractApplySign + api.AnchorContractMenuType_AnchorContractCancel +
				api.AnchorContractMenuType_AnchorContractList + api.AnchorContractMenuType_AnchorContractVio),
			GuildMsgMenuPer:          uint32(api.GuildMsgMenuType_FuWuHaoMsg),
			GuildManageMenuPer:       uint32(api.GuildManageMenuType_PersonnelManage) + uint32(api.GuildManageMenuType_LiveManage),
			ViolationMenuPer:         uint32(api.ViolationMenuType_VViolationRecord),
			EsportSkillMenuPer:       uint32(api.EsportMenuType_EsportMenuTypeAudit) + uint32(api.EsportMenuType_EsportMenuTypePerformanceAnalysis),
			GuildSelfActivityMenuPer: uint32(api.GuildSelfActivityMenuType_GuildSelfActivityMenuTypeManage),
		},
		LiveDataPermission: &api.LiveDataPermission{
			GuildLiveStatsDataPer: uint32(api.GuildLiveStatsDataType_AnchorIncome + api.GuildLiveStatsDataType_ChannelFee),
			GuildTaskDataPer:      0,
			AnchorDataPer:         uint32(api.AnchorDataType_ChannelFeeData + api.AnchorDataType_AnchorIncomeData + api.AnchorDataType_SignTsData + api.AnchorDataType_SignExpireTsData),
			AnchorPracDataPer:     uint32(api.AnchorPracDataType_PracChannelFee + api.AnchorPracDataType_PracAnchorIncome),
		},
		DataPermission: &api.DataPermission{
			GuildLiveStatsDataPer: uint32(api.GuildLiveStatsDataType_AnchorIncome + api.GuildLiveStatsDataType_ChannelFee + api.GuildLiveStatsDataType_ChannelPkgFeeRatio +
				api.GuildLiveStatsDataType_PureNewAnchorFee),
			GuildTaskDataPer: 0,
			AnchorDataPer: uint32(api.AnchorDataType_ChannelFeeData + api.AnchorDataType_AnchorIncomeData + api.AnchorDataType_SignTsData +
				api.AnchorDataType_SignExpireTsData + api.AnchorDataType_ChannelPkgFeeRatioData + api.AnchorDataType_AnchorPkgIncomeRatioData),
			AnchorPracDataPer: uint32(api.AnchorPracDataType_PracChannelFee + api.AnchorPracDataType_PracAnchorIncome),
			MultiPlayDetailDataPer: uint32(api.MultiPlayDetailDataType_MultiPlayDetailSignTs + api.MultiPlayDetailDataType_MultiPlayDetailSignExpireTs +
				api.MultiPlayDetailDataType_MultiPlayDetailGuildFee + api.MultiPlayDetailDataType_MultiPlayDetailChGiftFee + api.MultiPlayDetailDataType_MultiPlayDetailChWolfFee +
				api.MultiPlayDetailDataType_MultiPlayDetailChannelFee),
			MultiPlayPracAnalysisDataPer: uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisGiftFee + api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisSignTs + api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisChannelFee +
				api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisActiveFee + api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisQualityFee + api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisQualityFeeRat +
				api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisPracGiftFee + api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisPkgFee + api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisTbeanGiftFee + api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisGuildFee +
				api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisChannelPkgFee + api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisGuildPkgFee),
		},
		FunctionPermission: &api.FunctionPermission{
			ContractApplySignFuncPer: uint32(api.ContractApplySignFuncType_ContractApplySignQuery + api.ContractApplySignFuncType_ContractApplySignApproval),
			ContractCancelFuncPer:    uint32(api.ContractCancelFuncType_ContractCancelQuery + api.ContractCancelFuncType_ContractCancelApproval),
			ContractListFuncPer: uint32(api.ContractListFuncType_ContractListQuery + api.ContractListFuncType_ContractListCancelApproval + api.ContractListFuncType_ContractListRenewApproval +
				api.ContractListFuncType_ContractListExport),
			ContractVioFuncPer:           uint32(api.ContractVioFuncType_ContractVioQuery + api.ContractVioFuncType_ContractVioExport),
			LiveTotalDataFuncPer:         uint32(api.LiveTotalDataFuncType_LiveTotalDataQuery + api.LiveTotalDataFuncType_LiveTotalDataExport),
			LiveTaskDataFuncPer:          uint32(api.LiveTaskDataFuncType_LiveTaskDataQuery),
			AnchorDataFuncPer:            uint32(api.AnchorDataFuncType_AnchorDataQuery + api.AnchorDataFuncType_AnchorDataExport),
			AnchorPracAnalysisFuncPer:    uint32(api.AnchorPracAnalysisFuncType_AnchorPracAnalysisQuery + api.AnchorPracAnalysisFuncType_AnchorPracAnalysisExport),
			FlowCardFuncPer:              uint32(api.FlowCardFuncType_FlowCardQuery + api.FlowCardFuncType_FlowCardUse + api.FlowCardFuncType_FlowCardGrant),
			MultiPlayDetailFuncPer:       uint32(api.MultiPlayDetailFuncType_MultiPlayDetailQuery + api.MultiPlayDetailFuncType_MultiPlayDetailExport),
			MultiPlayPracAnalysisFuncPer: uint32(api.MultiPlayPracAnalysisFuncType_MultiPlayPracAnalysisQuery + api.MultiPlayPracAnalysisFuncType_MultiPlayPracAnalysisExport),
			GuildMsgFuncPer:              uint32(api.GuildMsgFuncType_GuildMsgQuery),
			MultiPlayOperationFuncPer:    uint32(api.MultiPlayOperationFuncType_MultiPlayOperationQuery),
			MultiPlayHallTaskFuncPer: uint32(api.MultiPlayHallTaskFuncType_MultiPlayHallTaskEdit +
				api.MultiPlayHallTaskFuncType_MultiPlayHallTaskQuery +
				api.MultiPlayHallTaskFuncType_MultiPlayHallTaskExport),
			MultiScheduleDataFuncPer:       uint32(api.MultiScheduleDataFuncType_MultiScheduleDataQuery + api.MultiScheduleDataFuncType_MultiScheduleDataExport + api.MultiScheduleDataFuncType_MultiScheduleDataSetAdmin),
			LiveManageFuncPer:              uint32(api.LiveManageFuncType_LiveManageQuery) + uint32(api.LiveManageFuncType_LiveManageExport),
			EsportManageFuncPer:            uint32(api.EsportManageFuncType_EsportManageQuery) + uint32(api.EsportManageFuncType_EsportManageEdit),
			EsportManagePerformanceFuncPer: uint32(api.EsportManagePerformanceFuncType_EsportManagePerformanceQuery) + uint32(api.EsportManagePerformanceFuncType_EsportManagePerformanceExport),
			BusinessDiagnosisFuncPer: uint32(api.BusinessDiagnosisFuncType_BusinessDiagnosisQuery) + uint32(api.BusinessDiagnosisFuncType_BusinessDiagnosisExport) + uint32(api.BusinessDiagnosisFuncType_BusinessDiagnosisAbility) +
				uint32(api.BusinessDiagnosisFuncType_BusinessDiagnosisMgr) + uint32(api.BusinessDiagnosisFuncType_BusinessDiagnosisRevenue) + uint32(api.BusinessDiagnosisFuncType_BusinessDiagnosisRecruit),
			LiveAnchorFaceCheckDetailFuncPer:  uint32(api.LiveAnchorFaceCheckDetailFuncType_LiveAnchorFaceCheckDetailQuery) + uint32(api.LiveAnchorFaceCheckDetailFuncType_LiveAnchorFaceCheckDetailExport),
			MultiAnchorFaceCheckDetailFuncPer: uint32(api.MultiAnchorFaceCheckDetailFuncType_MultiAnchorFaceCheckDetailQuery) + uint32(api.MultiAnchorFaceCheckDetailFuncType_MultiAnchorFaceCheckDetailExport),
		},
	}

	if coopInfo.GetIsAmuseCoopGuild() {
		permission.GuildManageMenuPer.AnchorContractMenuPer |= uint32(api.AnchorContractMenuType_AnchorContracCancelContractType)
		permission.GuildManageMenuPer.AnchorContractMenuPer |= uint32(api.AnchorContractMenuType_AnchorContracSignRight)

		permission.GuildManageMenuPer.HomePageMenuPer |= uint32(api.HomePageMenuType_HomePageMenuTypeMulti)
		permission.FunctionPermission.HomePageMultiFuncPer = uint32(api.HomePageMultiFuncType_HomePageMultiFuncGuildMultiKeyData) + uint32(api.HomePageMultiFuncType_HomePageMultiFuncBanner) + uint32(api.HomePageMultiFuncType_HomePageMultiFuncMultiTopChannel)

		permission.GuildManageMenuPer.GuildMultiPlayDataPer |= uint32(api.GuildMultiPlayDataType_MultiPlayBusinessDiag)
		permission.FunctionPermission.MultiBusinessDiagFunc = uint32(api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncRadarChart) + uint32(api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncRevenue) + uint32(api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncRecruit) +
			uint32(api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncHatch) + uint32(api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncSafety) + uint32(api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncStability) + uint32(api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncRiskResistance)

		permission.FunctionPermission.ContractListFuncPer |= uint32(api.ContractListFuncType_ContractListPromote)

	}

	log.Debugf("GetChairManPermission coopInfo:%v permission=%s", coopInfo, utils2.ToJson(permission))
	return permission
}

// 获取用户权限
func GetUserPermission(ctx context.Context, uid, guildId uint32) (accountRole int, isLibChairman bool, permission *api.GuildManagePermission, err protocol.ServerError) {
	permission = &api.GuildManagePermission{
		GuildManageMenuPer: &api.MenuPermission{},
		LiveDataPermission: &api.LiveDataPermission{},
		FunctionPermission: &api.FunctionPermission{},
		DataPermission:     &api.DataPermission{},
	}

	hasHallChannel, isDel, err := CheckShowHallTask(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserPermission failed to CheckShowHallTask uid:%d guildId:%d err:%v", uid, guildId, err)
		return accountRole, false, permission, err
	}

	m := models.GetModelServer()

	// 检查婚礼房权限
	hasWeddingPer, sErr := checkWeddingPer(ctx, uid, guildId)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetUserPermission failed to checkWeddingPer uid:%d guildId:%d err:%v", uid, guildId, sErr)
	}

	log.InfoWithCtx(ctx, "GetUserPermission CheckShowHallTask uid=%d guildId=%d hasHallChannel=%v isDel=%v hasWeddingPer=%v",
		uid, guildId, hasHallChannel, isDel, hasWeddingPer)

	isLibChairman, cooperationInfo, serr := CheckIsLibraryGuildChairmanV2(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetUserPermission failed to CheckIsLibraryGuildChairmanV2 uid:%d guildId:%d err:%v", uid, guildId, serr)
		return accountRole, false, permission, serr
	}

	defer func() {

		log.InfoWithCtx(ctx, "GetUserPermission before uid=%d guildId=%d permission=%s", uid, guildId, utils2.ToJson(permission))

		// 先加再减
		if !hasHallChannel {
			if permission.GetGuildManageMenuPer().GetGuildMultiPlayDataPer() > 0 &&
				(permission.GetGuildManageMenuPer().GetGuildMultiPlayDataPer()&uint32(api.GuildMultiPlayDataType_MultiPlayHallTask)) == uint32(api.GuildMultiPlayDataType_MultiPlayHallTask) {
				permission.GuildManageMenuPer.GuildMultiPlayDataPer -= uint32(api.GuildMultiPlayDataType_MultiPlayHallTask)
			}
			if permission.GetFunctionPermission().GetMultiPlayHallTaskFuncPer() > 0 &&
				(permission.GetFunctionPermission().GetMultiPlayHallTaskFuncPer()&uint32(api.MultiPlayHallTaskFuncType_MultiPlayHallTaskEdit)) == uint32(api.MultiPlayHallTaskFuncType_MultiPlayHallTaskEdit) {
				permission.FunctionPermission.MultiPlayHallTaskFuncPer -= uint32(api.MultiPlayHallTaskFuncType_MultiPlayHallTaskEdit)
			}
			if permission.GetFunctionPermission().GetMultiPlayHallTaskFuncPer() > 0 &&
				(permission.GetFunctionPermission().GetMultiPlayHallTaskFuncPer()&uint32(api.MultiPlayHallTaskFuncType_MultiPlayHallTaskQuery)) == uint32(api.MultiPlayHallTaskFuncType_MultiPlayHallTaskQuery) {
				permission.FunctionPermission.MultiPlayHallTaskFuncPer -= uint32(api.MultiPlayHallTaskFuncType_MultiPlayHallTaskQuery)
			}
			if permission.GetFunctionPermission().GetMultiPlayHallTaskFuncPer() > 0 &&
				(permission.GetFunctionPermission().GetMultiPlayHallTaskFuncPer()&uint32(api.MultiPlayHallTaskFuncType_MultiPlayHallTaskExport)) == uint32(api.MultiPlayHallTaskFuncType_MultiPlayHallTaskExport) {
				permission.FunctionPermission.MultiPlayHallTaskFuncPer -= uint32(api.MultiPlayHallTaskFuncType_MultiPlayHallTaskExport)
			}

			log.InfoWithCtx(ctx, "GetUserPermission not show hall_task uid=%d guildId=%d GuildMultiPlayDataPer=%d MultiPlayHallTaskFuncPer=%d",
				uid, guildId, permission.GuildManageMenuPer.GuildMultiPlayDataPer, permission.FunctionPermission.MultiPlayHallTaskFuncPer)
		} else {
			//   - 会长经营管理后台的“任务配置”不可见，保留“厅数据”。
			if isDel {
				if permission.GetFunctionPermission().GetMultiPlayHallTaskFuncPer() > 0 &&
					(permission.GetFunctionPermission().GetMultiPlayHallTaskFuncPer()&uint32(api.MultiPlayHallTaskFuncType_MultiPlayHallTaskEdit)) == uint32(api.MultiPlayHallTaskFuncType_MultiPlayHallTaskEdit) {
					permission.FunctionPermission.MultiPlayHallTaskFuncPer -= uint32(api.MultiPlayHallTaskFuncType_MultiPlayHallTaskEdit)
				}
				log.InfoWithCtx(ctx, "GetUserPermission show hall_task isDel uid=%d guildId=%d GuildMultiPlayDataPer=%d MultiPlayHallTaskFuncPer=%d",
					uid, guildId, permission.GuildManageMenuPer.GuildMultiPlayDataPer, permission.FunctionPermission.MultiPlayHallTaskFuncPer)
			}
		}

		if !hasWeddingPer {
			permission.GuildManageMenuPer.GuildMultiPlayDataPer -= uint32(api.GuildMultiPlayDataType_MultiPlayWeddingReserved)
			permission.FunctionPermission.MultiWeddingReservedFuncPer = 0
		}

		if !cooperationInfo.GetIsEsportCoopGuild() {
			if permission.GetGuildManageMenuPer().GetEsportSkillMenuPer() > 0 &&
				(permission.GetGuildManageMenuPer().GetEsportSkillMenuPer()&uint32(api.EsportMenuType_EsportMenuTypeAudit)) == uint32(api.EsportMenuType_EsportMenuTypeAudit) {
				permission.GuildManageMenuPer.EsportSkillMenuPer -= uint32(api.EsportMenuType_EsportMenuTypeAudit)
			}
			if permission.GetGuildManageMenuPer().GetEsportSkillMenuPer() > 0 &&
				(permission.GetGuildManageMenuPer().GetEsportSkillMenuPer()&uint32(api.EsportMenuType_EsportMenuTypePerformanceAnalysis)) == uint32(api.EsportMenuType_EsportMenuTypePerformanceAnalysis) {
				permission.GuildManageMenuPer.EsportSkillMenuPer -= uint32(api.EsportMenuType_EsportMenuTypePerformanceAnalysis)
			}
			if permission.GetFunctionPermission().GetEsportManageFuncPer() > 0 &&
				(permission.GetFunctionPermission().GetEsportManageFuncPer()&uint32(api.EsportManageFuncType_EsportManageQuery)) == uint32(api.EsportManageFuncType_EsportManageQuery) {
				permission.FunctionPermission.EsportManageFuncPer -= uint32(api.EsportManageFuncType_EsportManageQuery)
			}
			if permission.GetFunctionPermission().GetEsportManageFuncPer() > 0 &&
				(permission.GetFunctionPermission().GetEsportManageFuncPer()&uint32(api.EsportManageFuncType_EsportManageEdit)) == uint32(api.EsportManageFuncType_EsportManageEdit) {
				permission.FunctionPermission.EsportManageFuncPer -= uint32(api.EsportManageFuncType_EsportManageEdit)
			}
			if permission.GetFunctionPermission().GetEsportManagePerformanceFuncPer() > 0 &&
				(permission.GetFunctionPermission().GetEsportManagePerformanceFuncPer()&uint32(api.EsportManagePerformanceFuncType_EsportManagePerformanceQuery)) == uint32(api.EsportManagePerformanceFuncType_EsportManagePerformanceQuery) {
				permission.FunctionPermission.EsportManagePerformanceFuncPer -= uint32(api.EsportManagePerformanceFuncType_EsportManagePerformanceQuery)
			}
			if permission.GetFunctionPermission().GetEsportManagePerformanceFuncPer() > 0 &&
				(permission.GetFunctionPermission().GetEsportManagePerformanceFuncPer()&uint32(api.EsportManagePerformanceFuncType_EsportManagePerformanceExport)) == uint32(api.EsportManagePerformanceFuncType_EsportManagePerformanceExport) {
				permission.FunctionPermission.EsportManagePerformanceFuncPer -= uint32(api.EsportManagePerformanceFuncType_EsportManagePerformanceExport)
			}
			log.InfoWithCtx(ctx, "GetUserPermission delete esport per. guildid=%d, uid=%d", guildId, uid)
		}

		if permission.GuildManageMenuPer.GuildSelfActivityMenuPer == uint32(api.GuildSelfActivityMenuType_GuildSelfActivityMenuTypeManage) { //只有娱乐房和语音直播间
			if !cooperationInfo.GetIsYuyinCoopGuild() && !cooperationInfo.GetIsAmuseCoopGuild() {
				permission.GuildManageMenuPer.GuildSelfActivityMenuPer = 0
			}
		}

		log.InfoWithCtx(ctx, "GetUserPermission after uid=%d guildId=%d permission=%s", uid, guildId, utils2.ToJson(permission))
	}()

	// 合作库会长
	if isLibChairman {
		// 获取对公信息
		mainInfo, err := m.ExchangeGuildClientCli.GetMainOnly(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserPermission GetMainData uid:%d guildId:%d err: %v", uid, guildId, err)
			return accountRole, false, permission, protocol.ToServerError(err)
		}

		accountRole |= AccountRoleChairman

		log.DebugWithCtx(ctx, "GetUserPermission is chairman uid:%d guildId:%d placeCount:%d accountRole:%d",
			uid, guildId, mainInfo.GetPlaceCount(), accountRole)
		return accountRole, true, GetChairManPermission(guildId, cooperationInfo), nil
	}

	// 判断是否是公会代理人
	guildAgentResp, serr := m.GuildManagementCli.GetAgentGuild(ctx, uid)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetUserPermission failed to GetAgentGuild uid:%d guildId:%d err:%v", uid, guildId, serr)
		return accountRole, false, permission, serr
	}

	// 代理人
	if guildAgentResp.GetInfo().GetGuild_Id() > 0 && guildAgentResp.GetInfo().GetGuild_Id() == guildId {
		log.DebugWithCtx(ctx, "GetUserPermission is guild agent uid:%d guildId:%d", uid, guildId)

		if guildAgentResp.GetInfo().GetAgentType()&uint32(guild_management_svr.AgentType_AgentBroker) > 0 {
			accountRole |= AccountRoleBroker
		}

		if guildAgentResp.GetInfo().GetAgentType()&uint32(guild_management_svr.AgentType_AgentAdmin) > 0 {
			accountRole |= AccountRoleAdmin
		}

		permission = GetOutPermissionMsg(*guildAgentResp.GetInfo().GetPermission())
		//合作库经纪人
		if cooperationInfo != nil && (accountRole&AccountRoleBroker) > 0 &&
			(cooperationInfo.GetIsAmuseCoopGuild() || cooperationInfo.GetIsYuyinCoopGuild() || cooperationInfo.GetIsEsportCoopGuild()) {
			permission.GuildManageMenuPer.GuildAgentMenuPer = uint32(api.GuildAgentMenuType_GuildAgentManage)
			permission.GuildManageMenuPer.GuildLiveDataMenuPer |= uint32(api.GuildLiveDataMenuType_AnchorFaceCheckDetail)
			permission.GuildManageMenuPer.GuildMultiPlayDataPer |= uint32(api.GuildMultiPlayDataType_MultiAnchorFaceCheckDetail)
			permission.FunctionPermission.LiveAnchorFaceCheckDetailFuncPer = uint32(api.LiveAnchorFaceCheckDetailFuncType_LiveAnchorFaceCheckDetailQuery)
			permission.FunctionPermission.MultiAnchorFaceCheckDetailFuncPer = uint32(api.MultiAnchorFaceCheckDetailFuncType_MultiAnchorFaceCheckDetailQuery)
		}

		if cooperationInfo != nil && (accountRole&AccountRoleAdmin) > 0 { //合作库管理可见
			permission.GuildManageMenuPer.GuildSelfActivityMenuPer = uint32(api.GuildSelfActivityMenuType_GuildSelfActivityMenuTypeManage)
			permission.GuildManageMenuPer.GuildLiveDataMenuPer |= uint32(api.GuildLiveDataMenuType_AnchorFaceCheckDetail)
			permission.GuildManageMenuPer.GuildMultiPlayDataPer |= uint32(api.GuildMultiPlayDataType_MultiAnchorFaceCheckDetail) | uint32(api.GuildMultiPlayDataType_MultiPlayWeddingReserved)
			permission.FunctionPermission.LiveAnchorFaceCheckDetailFuncPer = uint32(api.LiveAnchorFaceCheckDetailFuncType_LiveAnchorFaceCheckDetailQuery) + uint32(api.LiveAnchorFaceCheckDetailFuncType_LiveAnchorFaceCheckDetailExport)
			permission.FunctionPermission.MultiAnchorFaceCheckDetailFuncPer = uint32(api.MultiAnchorFaceCheckDetailFuncType_MultiAnchorFaceCheckDetailQuery) + uint32(api.MultiAnchorFaceCheckDetailFuncType_MultiAnchorFaceCheckDetailExport)
			permission.FunctionPermission.MultiWeddingReservedFuncPer = uint32(api.MultiPlayWeddingReservedFuncType_MultiPlayWeddingReservedQuery) + uint32(api.MultiPlayWeddingReservedFuncType_MultiPlayWeddingReservedExport)
		}

		return accountRole, false, permission, nil
	}

	log.DebugWithCtx(ctx, "GetUserPermission user has no permission uid:%d guildId:%d", uid, guildId)

	return accountRole, false, permission, nil
}

// 检查权限入参
type CheckPerInReq struct {
	ctx         context.Context
	uid         uint32
	guildId     uint32
	menuPerType string //菜单权限类型
	funcPerType string //功能权限类型
}

// 检查权限出参
type CheckPerInResp struct {
	accountRole int                        //角色类型
	hasMenuPer  bool                       //是否有菜单权限
	permission  *api.GuildManagePermission // 用户权限
	hasFuncPer  bool                       //是否功能权限
}

// 检查菜单权限
func checkMenuPer(perType string, permission *api.GuildManagePermission) bool {
	hasPer := false

	if api.GuildMsgMenuType_value[perType] > 0 {
		//会长消息菜单
		hasPer = (permission.GetGuildManageMenuPer().GetGuildMsgMenuPer() & uint32(api.GuildMsgMenuType_value[perType])) > 0
	}

	if api.GuildMultiPlayDataType_value[perType] > 0 {
		// 多人互动经营管理菜单
		hasPer = (permission.GetGuildManageMenuPer().GetGuildMultiPlayDataPer() & uint32(api.GuildMultiPlayDataType_value[perType])) > 0
	}

	if api.GuildLiveDataMenuType_value[perType] > 0 {
		//语音直播经营分析菜单栏
		hasPer = (permission.GetGuildManageMenuPer().GetGuildLiveDataMenuPer() & uint32(api.GuildLiveDataMenuType_value[perType])) > 0
	}

	if api.GuildAgentMenuType_value[perType] > 0 {
		//公会管理菜单栏
		hasPer = (permission.GetGuildManageMenuPer().GetGuildManageMenuPer() & uint32(api.GuildAgentMenuType_value[perType])) > 0
	}

	if api.AnchorContractMenuType_value[perType] > 0 {
		// 签约管理菜单栏
		hasPer = (permission.GetGuildManageMenuPer().GetAnchorContractMenuPer() & uint32(api.AnchorContractMenuType_value[perType])) > 0
	}

	if api.GuildManageMenuType_value[perType] > 0 {
		// 公会管理
		hasPer = (permission.GetGuildManageMenuPer().GetGuildManageMenuPer() & uint32(api.GuildManageMenuType_value[perType])) > 0
	}

	if api.EsportMenuType_value[perType] > 0 {
		// 电竞经营管理
		hasPer = (permission.GetGuildManageMenuPer().GetEsportSkillMenuPer() & uint32(api.EsportMenuType_value[perType])) > 0
	}

	if api.HomePageMenuType_value[perType] > 0 {
		// 首页经营管理
		hasPer = (permission.GetGuildManageMenuPer().GetHomePageMenuPer() & uint32(api.HomePageMenuType_value[perType])) > 0
	}

	return hasPer
}

// 检查功能权限
func checkFuncPer(perType string, permission *api.GuildManagePermission) bool {
	hasPer := false

	if api.HomePageMultiFuncType_value[perType] > 0 {
		///首页管理-多人互动功能权限
		hasPer = (permission.GetFunctionPermission().GetHomePageMultiFuncPer() & uint32(api.HomePageMultiFuncType_value[perType])) > 0
	}

	if api.MultiBusinessDiagFuncType_value[perType] > 0 {
		//多人互动-经营分析功能权限
		hasPer = (permission.GetFunctionPermission().GetMultiBusinessDiagFunc() & uint32(api.MultiBusinessDiagFuncType_value[perType])) > 0
	}

	if api.BusinessDiagnosisFuncType_value[perType] > 0 {
		//直播-经营诊断功能权限
		hasPer = (permission.GetFunctionPermission().GetBusinessDiagnosisFuncPer() & uint32(api.BusinessDiagnosisFuncType_value[perType])) > 0
	}

	return hasPer
}

// 检查用户权限
func CheckUserPermission(req CheckPerInReq) (CheckPerInResp, int32, string) {
	log.DebugWithCtx(req.ctx, "CheckUserPermission begin req:%v", req)

	resp := CheckPerInResp{
		permission: &api.GuildManagePermission{},
	}

	isChairman := false
	var sErr protocol.ServerError
	resp.accountRole, isChairman, resp.permission, sErr = GetUserPermission(req.ctx, req.uid, req.guildId)
	if sErr != nil {
		log.ErrorWithCtx(req.ctx, "CheckUserPermission failed to GetUserPermission err %+v req:%v", sErr, req)
		return resp, int32(sErr.Code()), sErr.Message()
	}

	//菜单权限
	if req.menuPerType != "" {
		resp.hasMenuPer = checkMenuPer(req.menuPerType, resp.permission)

		if !resp.hasMenuPer {
			log.ErrorWithCtx(req.ctx, "CheckUserPermission user no permission req:%v, resp:%v", req, resp)
			return resp, -505, "该用户没有此菜单权限"
		}
	} else {
		resp.hasMenuPer = true
	}

	//功能权限
	if req.funcPerType != "" {
		resp.hasFuncPer = checkFuncPer(req.funcPerType, resp.permission)

		if !resp.hasFuncPer {
			log.ErrorWithCtx(req.ctx, "CheckUserPermission user no permission req:%v, resp:%v", req, resp)
			return resp, -505, "该用户没有此功能权限"
		}
	} else {
		resp.hasFuncPer = true
	}

	log.DebugWithCtx(req.ctx, "CheckUserPermission req:%v isChairman:%v resp:%v", req, isChairman, resp)
	return resp, 0, ""
}

// 不用这个函数检查权限了，使用CheckUserPermission代替
func checkUserHasPer(ctx context.Context, uid, guildId uint32, perType string) (int, bool, int32, string, *api.GuildManagePermission) {
	log.DebugWithCtx(ctx, "checkUserHasPer begin uid:%d guildId:%d perType:%s", uid, guildId, perType)

	hasPer := false
	accountRole, isChairman, permission, serr := GetUserPermission(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "checkUserHasPer failed to GetUserPermission err %+v, uid:%d guild:%d", serr, uid, guildId)
		return accountRole, hasPer, int32(serr.Code()), serr.Message(), permission
	}

	log.DebugWithCtx(ctx, "checkUserHasPer uid:%d guildId:%d perType:%s perr:%v isChairman:%v", uid, guildId, perType, permission, isChairman)

	if api.GuildMsgMenuType_value[perType] > 0 {
		//会长消息菜单
		hasPer = (permission.GetGuildManageMenuPer().GetGuildMsgMenuPer() & uint32(api.GuildMsgMenuType_value[perType])) > 0
	}

	if api.GuildMultiPlayDataType_value[perType] > 0 {
		// 多人互动经营管理菜单
		hasPer = (permission.GetGuildManageMenuPer().GetGuildMultiPlayDataPer() & uint32(api.GuildMultiPlayDataType_value[perType])) > 0
	}

	if api.GuildLiveDataMenuType_value[perType] > 0 {
		//语音直播经营分析菜单栏
		hasPer = (permission.GetGuildManageMenuPer().GetGuildLiveDataMenuPer() & uint32(api.GuildLiveDataMenuType_value[perType])) > 0
	}

	if api.GuildAgentMenuType_value[perType] > 0 {
		//公会管理菜单栏
		hasPer = (permission.GetGuildManageMenuPer().GetGuildManageMenuPer() & uint32(api.GuildAgentMenuType_value[perType])) > 0
	}

	if api.AnchorContractMenuType_value[perType] > 0 {
		// 签约管理菜单栏
		hasPer = (permission.GetGuildManageMenuPer().GetAnchorContractMenuPer() & uint32(api.AnchorContractMenuType_value[perType])) > 0
	}

	if api.GuildManageMenuType_value[perType] > 0 {
		// 公会管理
		hasPer = (permission.GetGuildManageMenuPer().GetGuildManageMenuPer() & uint32(api.GuildManageMenuType_value[perType])) > 0
	}

	if api.EsportMenuType_value[perType] > 0 {
		// 电竞经营管理
		hasPer = (permission.GetGuildManageMenuPer().GetEsportSkillMenuPer() & uint32(api.EsportMenuType_value[perType])) > 0
	}

	if api.HomePageMenuType_value[perType] > 0 {
		// 首页经营管理
		hasPer = (permission.GetGuildManageMenuPer().GetHomePageMenuPer() & uint32(api.HomePageMenuType_value[perType])) > 0
	}

	if !hasPer {
		log.ErrorWithCtx(ctx, "checkUserHasPer user no permission uid:%d guild:%d menuType:%s", uid, guildId, perType)
		return accountRole, hasPer, -505, "该用户没有此菜单权限", permission
	}

	return accountRole, hasPer, 0, "", permission
}

// 根据权限返回经营总览相关数据字段
func GetGuildLiveStatsDataByPermission(data GuildLiveStatsData, permission *api.GuildManagePermission) GuildLiveStatsData {
	if permission.GetDataPermission().GetGuildLiveStatsDataPer()&uint32(api.GuildLiveStatsDataType_AnchorIncome) == 0 {
		data.AnchorIncome = 0
	}
	if permission.GetDataPermission().GetGuildLiveStatsDataPer()&uint32(api.GuildLiveStatsDataType_ChannelFee) == 0 {
		data.ChannelFee = 0
	}
	if permission.GetDataPermission().GetGuildLiveStatsDataPer()&uint32(api.GuildLiveStatsDataType_ChannelPkgFeeRatio) == 0 {
		data.ChannelPkgFeeRatio = 0
	}
	if permission.GetDataPermission().GetGuildLiveStatsDataPer()&uint32(api.GuildLiveStatsDataType_PureNewAnchorFee) == 0 {
		data.PureNewAnchorFee = 0
	}

	return data
}

// 根据权限返回主播数据相关数据字段
func GetAnchorDataByPermission(data AnchorData, permission *api.GuildManagePermission) AnchorData {
	if permission.GetDataPermission().GetAnchorDataPer()&uint32(api.AnchorDataType_ChannelFeeData) == 0 {
		data.ChannelFee = 0
	}
	if permission.GetDataPermission().GetAnchorDataPer()&uint32(api.AnchorDataType_AnchorIncomeData) == 0 {
		data.AnchorIncome = 0
	}
	if permission.GetDataPermission().GetAnchorDataPer()&uint32(api.AnchorDataType_SignTsData) == 0 {
		data.SignTs = 0
	}
	if permission.GetDataPermission().GetAnchorDataPer()&uint32(api.AnchorDataType_SignExpireTsData) == 0 {
		data.SignExpireTs = 0
	}
	if permission.GetDataPermission().GetAnchorDataPer()&uint32(api.AnchorDataType_ChannelPkgFeeRatioData) == 0 {
		data.ChannelPkgFeeRatio = 0
	}
	if permission.GetDataPermission().GetAnchorDataPer()&uint32(api.AnchorDataType_AnchorPkgIncomeRatioData) == 0 {
		data.AnchorPkgIncomeRatio = 0
	}

	return data
}

// 根据权限返回主播详细数据相关数据字段
func GetAnchorDetailDataByPermission(data api.AnchorDetailData, permission *api.GuildManagePermission) *api.AnchorDetailData {
	if permission.GetDataPermission().GetAnchorDataPer()&uint32(api.AnchorDataType_ChannelFeeData) == 0 {
		data.ChannelFee = 0
	}
	if permission.GetDataPermission().GetAnchorDataPer()&uint32(api.AnchorDataType_AnchorIncomeData) == 0 {
		data.AnchorIncome = 0
	}
	if permission.GetDataPermission().GetAnchorDataPer()&uint32(api.AnchorDataType_SignTsData) == 0 {
		data.SignTs = 0
	}
	if permission.GetDataPermission().GetAnchorDataPer()&uint32(api.AnchorDataType_SignExpireTsData) == 0 {
		data.SignExpireTs = 0
	}
	return &data
}

// 根据权限返回主播从业者相关数据字段
func GetAnchorPracDataByPermission(data *api.AnchorPractitionerBaseInfo, permission *api.GuildManagePermission) *api.AnchorPractitionerBaseInfo {
	if permission.GetDataPermission().GetAnchorPracDataPer()&uint32(api.AnchorPracDataType_PracChannelFee) == 0 {
		data.ChannelFee = 0
	}
	if permission.GetDataPermission().GetAnchorPracDataPer()&uint32(api.AnchorPracDataType_PracAnchorIncome) == 0 {
		data.AnchorIncome = 0
	}

	return data
}

// 根据权限返回签约成员个人明细数据字段
func GetContractAnchorDetailByPermission(data *api.ContractAnchorDetail, permission *api.GuildManagePermission) *api.ContractAnchorDetail {
	if permission.GetDataPermission().GetMultiPlayDetailDataPer()&uint32(api.MultiPlayDetailDataType_MultiPlayDetailSignTs) == 0 {
		data.ContractStartTime = 0
	}
	if permission.GetDataPermission().GetMultiPlayDetailDataPer()&uint32(api.MultiPlayDetailDataType_MultiPlayDetailSignExpireTs) == 0 {
		data.ContractEndTime = 0
	}
	if permission.GetDataPermission().GetMultiPlayDetailDataPer()&uint32(api.MultiPlayDetailDataType_MultiPlayDetailGuildFee) == 0 {
		data.MonthFee = 0
	}

	return data
}

// 根据权限返回签约成员房间接档数据字段
func GetContractAnchorChannelStatByPermission(data *api.ContractAnchorChannelStat, permission *api.GuildManagePermission) *api.ContractAnchorChannelStat {
	if permission.GetDataPermission().GetMultiPlayDetailDataPer()&uint32(api.MultiPlayDetailDataType_MultiPlayDetailChGiftFee) == 0 {
		data.PresentFee = 0
	}
	if permission.GetDataPermission().GetMultiPlayDetailDataPer()&uint32(api.MultiPlayDetailDataType_MultiPlayDetailChWolfFee) == 0 {
		data.WerewolfFee = 0
	}
	if permission.GetDataPermission().GetMultiPlayDetailDataPer()&uint32(api.MultiPlayDetailDataType_MultiPlayDetailChannelFee) == 0 {
		data.Fee = 0
	}

	return data
}

// 根据权限返回多人互动从业者数据字段
func GetPractitionerMonthLyInfoByPermission(data *api.PractitionerMonthLyInfo, permission *api.GuildManagePermission) *api.PractitionerMonthLyInfo {
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisGiftFee) == 0 {
		data.BaseInfo.Income = 0
	}
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisSignTs) == 0 {
		data.BaseInfo.SignTs = 0
	}

	return data
}

// 根据权限返回多人互动从业者数据字段
func GetPractitionerDailyInfoByPermission(data *api.PractitionerDailyInfo, permission *api.GuildManagePermission) *api.PractitionerDailyInfo {
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisGiftFee) == 0 {
		data.BaseInfo.Income = 0
	}
	return data
}

// 根据权限返回多人互动房间数据字段
func GetPgcMonthlyInfoByPermission(data *api.PgcMonthlyInfo, permission *api.GuildManagePermission) *api.PgcMonthlyInfo {
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisChannelFee) == 0 {
		data.BaseInfo.ChannelFee = 0
	}
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisActiveFee) == 0 {
		data.ActiveAnchorFee = 0
	}
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisQualityFee) == 0 {
		data.QualityAnchorFee = 0
	}
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisQualityFeeRat) == 0 {
		data.QualityAnchorFeeRatio = 0
	}
	return data
}

// 根据权限返回多人互动房间数据字段
func GetPgcDailyInfoByPermission(data *api.PgcDailyInfo, permission *api.GuildManagePermission) *api.PgcDailyInfo {
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisChannelFee) == 0 {
		data.BaseInfo.ChannelFee = 0
	}
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisPracGiftFee) == 0 {
		data.AnchorFee = 0
	}
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisPkgFee) == 0 {
		data.PkgGiftFee = 0
	}
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisTbeanGiftFee) == 0 {
		data.TbeanGiftFee = 0
	}
	return data
}

// 根据权限返回公会从业者月数据
func GetGuildMonthlyStatsInfoByPermission(data *api.GuildMonthlyStatsInfo, permission *api.GuildManagePermission) *api.GuildMonthlyStatsInfo {
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisActiveFee) == 0 {
		data.ActiveAnchorFee = 0
	}
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisQualityFee) == 0 {
		data.QualityAnchorFee = 0
	}
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisQualityFeeRat) == 0 {
		data.QualityAnchorFeeRatio = 0
	}
	if permission.GetDataPermission().GetMultiPlayPracAnalysisDataPer()&uint32(api.MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisGuildFee) == 0 {
		data.GuildFee = 0
	}

	return data
}

func GetUidGuilds(ctx context.Context, uid uint32) ([]uint32, error) {
	guilds := make([]uint32, 0)
	guildsResp, err := models.GetModelServer().ExchangeGuildClientCli.GetMainGuilds(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUidGuilds GetMainGuilds err:%v", err)
		return nil, err
	}
	for _, guild := range guildsResp.GetList() {
		guilds = append(guilds, guild.GetGuildId())
	}
	log.InfoWithCtx(ctx, "GetUidGuilds guilds: [%v]", guilds)
	return guilds, nil
}

func Tid2Uid(ctx context.Context, tidList []string) ([]uint32, error) {
	uidList := make([]uint32, 0)
	if len(tidList) == 0 {
		return uidList, nil
	}
	resp, err := models.GetModelServer().AccountCli.BatchQueryUidListByAlias(ctx, tidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "Tid2Uid BatchQueryUidListByAlias err:%v", err)
		return nil, err
	}
	for _, r := range resp {
		if r.GetUid() != 0 {
			uidList = append(uidList, r.GetUid())
		}
	}
	return uidList, nil
}

func batGetUidByTid(ctx context.Context, tidList []string) ([]uint32, error) {
	uidList := make([]uint32, 0)
	if len(tidList) == 0 {
		return uidList, nil
	}

	tmpTidList := make([]string, 0)
	for index, tid := range tidList {
		tmpTidList = append(tmpTidList, tid)
		if len(tmpTidList) == 100 || index == len(tidList)-1 {
			resp, err := models.GetModelServer().AccountCli.BatchQueryUidListByAlias(ctx, tmpTidList)
			if err != nil {
				log.ErrorWithCtx(ctx, "batGetUidByTid BatchQueryUidListByAlias err:%v", err)
				return nil, err
			}
			for _, r := range resp {
				if r.GetUid() != 0 {
					uidList = append(uidList, r.GetUid())
				}
			}
		}
	}

	return uidList, nil
}

func batGetUser(ctx context.Context, uidList []uint32) (map[uint32]*account.User, error) {
	mapUid2User := make(map[uint32]*account.User, 0)
	tmpUidList := make([]uint32, 0)

	for index, uid := range uidList {
		tmpUidList = append(tmpUidList, uid)
		if len(tmpUidList) == 100 || index == len(uidList)-1 {
			userMap, err := models.GetModelServer().AccountCli.GetUsersMap(ctx, tmpUidList)
			if err != nil {
				return mapUid2User, err
			}

			for k, v := range userMap {
				mapUid2User[k] = v
			}

			tmpUidList = tmpUidList[0:0]
		}
	}

	return mapUid2User, nil
}

func DisplayId2ChannelId(ctx context.Context, didList []string) ([]uint32, error) {
	channelIds := make([]uint32, 0)
	if len(didList) == 0 {
		return channelIds, nil
	}
	resp, err := models.GetModelServer().ChannelCli.BatchGetChannelSimpleInfoByViewId(ctx, 0, didList)
	if err != nil {
		log.ErrorWithCtx(ctx, "DisplayId2ChannelId BatchGetChannelSimpleInfoByViewId didList:%v err:%v", didList, err)
		return nil, err
	}
	for _, r := range resp {
		channelIds = append(channelIds, r.GetChannelId())
	}
	return channelIds, nil
}

func batGetChannel(ctx context.Context, channelIds []uint32) (map[uint32]*Channel.ChannelSimpleInfo, error) {
	chunks := utils.ChunkUint32(channelIds, 100)

	mapChannelInfoMap := make(map[uint32]*Channel.ChannelSimpleInfo)
	for _, chunk := range chunks {
		mapChannelInfo_, err := models.GetModelServer().ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, chunk)
		if err != nil {
			fmt.Printf("batGetChannel BatchGetChannelSimpleInfo err:%v, ids:%+v", err, chunk)
			return mapChannelInfoMap, nil
		}
		for k, v := range mapChannelInfo_ {
			mapChannelInfoMap[k] = v
		}
	}
	return mapChannelInfoMap, nil
}

func getAnchorContractInfo(ctx context.Context, uidList []uint32) (map[uint32]*anchorcontractPb.ContractInfo, protocol.ServerError) {
	mapUid2AnchorContractInfo := make(map[uint32]*anchorcontractPb.ContractInfo)

	log.DebugWithCtx(ctx, "getAnchorContractInfo begin in:%v", uidList)
	contractResp, err := models.GetModelServer().AnchorcontractCli.BatchGetUserContract(ctx, 0, &anchorcontractPb.BatchGetUserContractReq{
		UidList: uidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getAnchorContractInfo BatchGetUserContract in:%v err:%v", uidList, err)
		return mapUid2AnchorContractInfo, err
	}

	for _, info := range contractResp.GetContractList() {
		mapUid2AnchorContractInfo[info.GetActorUid()] = info
	}

	log.DebugWithCtx(ctx, "getAnchorContractInfo end in:%v out:%v", uidList, mapUid2AnchorContractInfo)
	return mapUid2AnchorContractInfo, nil
}

func getAnchorContractInfoV2(ctx context.Context, uidList []uint32) (map[uint32]*anchorcontractPb.ContractCacheInfo, protocol.ServerError) {
	mapUid2AnchorContractInfo := make(map[uint32]*anchorcontractPb.ContractCacheInfo)

	log.DebugWithCtx(ctx, "getAnchorContractInfo begin in:%v", uidList)
	contractResp, err := models.GetModelServer().AnchorcontractCli.BatchGetContractInfo(ctx, &anchorcontractPb.BatchGetContractInfoReq{
		Uids: uidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getAnchorContractInfo BatchGetUserContract in:%v err:%v", uidList, err)
		return mapUid2AnchorContractInfo, err
	}

	mapUid2AnchorContractInfo = contractResp.GetUid2ContractInfo()
	log.DebugWithCtx(ctx, "getAnchorContractInfo end in:%v out:%v", uidList, mapUid2AnchorContractInfo)
	return mapUid2AnchorContractInfo, nil
}

func getAnchorIndentityInfo(ctx context.Context, uidList []uint32) (map[uint32]*anchorcontractPb.AnchorIdentityInfo, protocol.ServerError) {
	mapUId2AnchorIdentityInfo := make(map[uint32]*anchorcontractPb.AnchorIdentityInfo)

	log.DebugWithCtx(ctx, "getAnchorIndentityInfo begin in:%v", uidList)
	indentityResp, err := models.GetModelServer().AnchorcontractCli.BatchGetAnchorIdentity(ctx, 0, &anchorcontractPb.BatchGetAnchorIdentityReq{
		UidList:      uidList,
		IdentityType: uint32(anchorcontractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getAnchorIndentityInfo BatchGetAnchorIdentity in:%v err:%v", uidList, err)
		return mapUId2AnchorIdentityInfo, err
	}

	for _, info := range indentityResp.GetInfoList() {
		mapUId2AnchorIdentityInfo[info.GetActorUid()] = info
	}

	log.DebugWithCtx(ctx, "getAnchorIndentityInfo end in:%v out:%v", uidList, mapUId2AnchorIdentityInfo)
	return mapUId2AnchorIdentityInfo, nil
}

func getAnchorTagInfo(ctx context.Context, uidList []uint32) (map[uint32]uint32, protocol.ServerError) {
	mapUid2TagId := make(map[uint32]uint32, 0)

	liveResp, sErr := models.GetModelServer().LiveMgrCli.GetAnchorByUidList(ctx, &liveMgrPb.GetAnchorByUidListReq{
		UidList: uidList,
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "getAnchorTagInfo GetAnchorByUidList failed list:%v err:%v", uidList, sErr)
		return mapUid2TagId, sErr
	}

	channelIdList := make([]uint32, 0)
	mapCid2Uid := make(map[uint32]uint32, 0)
	for _, info := range liveResp.GetAnchorList() {
		channelIdList = append(channelIdList, info.GetChannelId())
		mapCid2Uid[info.GetChannelId()] = info.GetUid()
	}

	tagResp, err := models.GetModelServer().EntertainmentCli.BatchGetChannelTag(ctx, 0, &enterPb.BatchGetChannelTagReq{
		ChannelIdList: channelIdList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getAnchorTagInfo BatchGetChannelTag failed list:%v err:%v", channelIdList, sErr)
		return mapUid2TagId, protocol.ToServerError(err)
	}

	if len(channelIdList) != len(tagResp.GetChannelTagList()) {
		log.ErrorWithCtx(ctx, "getAnchorTagInfo fault channel cnt %d %d", len(channelIdList), len(tagResp.GetChannelTagList()))
		return mapUid2TagId, protocol.NewServerError(-2)
	}

	for i, info := range tagResp.GetChannelTagList() {
		mapUid2TagId[mapCid2Uid[channelIdList[i]]] = info.GetTagId()
	}

	log.DebugWithCtx(ctx, "getAnchorTagInfo uidList:%v tag:%v", uidList, mapUid2TagId)
	return mapUid2TagId, nil
}

func getChannelTag(ctx context.Context, cidList []uint32) (map[uint32]uint32, error) {
	mapCid2Tag := make(map[uint32]uint32, 0)

	tagResp, err := models.GetModelServer().EntertainmentCli.BatchGetChannelTag(ctx, 0, &enterPb.BatchGetChannelTagReq{
		ChannelIdList: cidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getChannelTag BatchGetChannelTag failed list:%v err:%v", cidList, err)
		return mapCid2Tag, err
	}

	if len(cidList) != len(tagResp.GetChannelTagList()) {
		log.ErrorWithCtx(ctx, "getChannelTag fault channel cnt %d %d", len(cidList), len(tagResp.GetChannelTagList()))
		return mapCid2Tag, protocol.NewServerError(-2)
	}

	for i, info := range tagResp.GetChannelTagList() {
		mapCid2Tag[cidList[i]] = info.GetTagId()
	}

	log.DebugWithCtx(ctx, "getChannelTag cidList:%v tag:%v", cidList, mapCid2Tag)
	return mapCid2Tag, nil

}

func Tid2UidV2(ctx context.Context, tidList []string) (
	validUids []uint32, uid2ttid map[uint32]string, ttid2Uid map[string]uint32, err error) {

	if len(tidList) == 0 {
		return
	}

	uid2ttid = map[uint32]string{}
	ttid2Uid = map[string]uint32{}

	resp, err := models.GetModelServer().AccountCli.BatchQueryUidListByAlias(ctx, tidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "Tid2Uid BatchQueryUidListByAlias err:%v", err)
		return
	}

	for _, r := range resp {
		if r.GetUid() != 0 {
			validUids = append(validUids, r.GetUid())
			uid2ttid[r.GetUid()] = r.GetKey()
			ttid2Uid[r.GetKey()] = r.GetUid()
		}
	}
	return
}

func setVerifyFlag(ctx context.Context, uid, scene uint32) error {
	_, err := models.GetModelServer().GuildManagementCli.SetVerifyFlag(ctx, &guild_management_svr.SetVerifyFlagReq{
		Scene: scene,
		Uid:   uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "setVerifyFlag SetVerifyFlag failed uid:%d scene:%d", uid, scene)
		return err
	}

	return nil
}

// hasHallChannel是否有配置承接大厅 isDel承接大厅是否都已回收
func CheckShowHallTask(ctx context.Context, guildId uint32) (hasHallChannel bool, isDel bool, serr protocol.ServerError) {
	resp, err := models.GetModelServer().SignAnchorStatsCli.GetGuildMultiPlayerHall(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckShowHallTask failed %+v guildId %d", err, guildId)
		serr = err
		return
	}
	if len(resp.GetList()) == 0 {
		return
	}
	hasHallChannel = true
	isDel = true
	for _, info := range resp.GetList() {
		if !info.IsDel {
			isDel = false
		}
	}
	return
}

func GetAgentInviteList(ctx context.Context, guildId uint32, uidList []uint32, inviteStatus uint32) ([]*guild_management_svr.GuildAgentInvite, protocol.ServerError) {
	inviteList := make([]*guild_management_svr.GuildAgentInvite, 0)

	var page, pageSize uint32 = 1, 100
	for {
		agentInviteResp, err := models.GetModelServer().GuildManagementCli.GetGuildAgentInviteList(ctx, &guild_management_svr.GetGuildAgentInviteListReq{
			GuildId:      guildId,
			UidList:      uidList,
			InviteStatus: inviteStatus,
			Page:         page,
			PageSize:     pageSize,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAgentInviteList failed to GetGuildAgentInviteList guildId:%d uidList:%v inviteStatus:%d err:%v",
				guildId, uidList, inviteStatus, err)
			return nil, err
		}

		for _, invite := range agentInviteResp.GetInviteList() {
			inviteList = append(inviteList, invite)
		}

		if len(agentInviteResp.GetInviteList()) < int(pageSize) {
			break
		}

		page++
	}

	log.DebugWithCtx(ctx, "GetAgentInviteList end guildId:%d uidList:%v inviteStatus:%d inviteList:%v", guildId, uidList, inviteStatus, inviteList)
	return inviteList, nil
}

// 检查婚礼房权限 公会有婚礼房标签或婚礼房玩法其一，则拥有婚礼房菜单权限
func checkWeddingPer(ctx context.Context, uid, guildId uint32) (bool, error) {
	m := models.GetModelServer()
	var hasWeddingPer bool

	// 获取当前公会的房间
	cgList, err := m.ChannelGuildGoCli.BatGetChannelGuildList(ctx, []uint32{guildId}, uint32(ga.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE))
	if err != nil {
		log.ErrorWithCtx(ctx, "checkWeddingPer Failed to BatGetChannelGuildList err %+v, guildId:%d, uid:%d", err, guildId, uid)
		return false, err
	}
	guildGroupCidList := make([]uint32, 0)
	for _, item := range cgList {
		guildGroupCidList = append(guildGroupCidList, item.GetChannelIds()...)
	}

	var g errgroup.Group
	ctx, cancel := context.WithTimeout(ctx, time.Second*3)
	defer cancel()

	g.Go(func() error {
		// 获取房间玩法
		channelSchemeResp, err := m.ChannelSchemeCli.BatGetUgcChannelSchemeInfo(ctx, &channelscheme.BatGetUgcChannelSchemeInfoReq{
			CidList: guildGroupCidList,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "checkWeddingPer Failed to BatGetUgcChannelSchemeInfo err %+v, guildId:%d, uid:%d", err, guildId, uid)
			return err
		}
		for cid, item := range channelSchemeResp.GetSchemeInfoList() {
			if hasWeddingScheme(ctx, item) {
				log.DebugWithCtx(ctx, "checkWeddingPer hasWeddingScheme uid:%d guildId:%d cid:%d, schemeId:%d", uid, guildId, cid, item.GetSchemeId())
				hasWeddingPer = true
				break
			}
		}
		return nil
	})

	g.Go(func() error {
		// 获取房间标签
		channelTagMap, sErr := m.EntertainmentCli.BatchGetChannelTagMap(ctx, guildGroupCidList)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "checkWeddingPer Failed to BatchGetChannelTagMap err %+v, guildId:%d, uid:%d", sErr, guildId, uid)
			return sErr
		}
		for cid, item := range channelTagMap {
			if item.GetTagId() == 2020 {
				log.DebugWithCtx(ctx, "checkWeddingPer hasWeddingChannelTag uid:%d guildId:%d cid:%d, tagId:%d", uid, guildId, cid, item.GetTagId())
				hasWeddingPer = true
				break
			}
		}
		return nil
	})

	if err := g.Wait(); err != nil {
		log.ErrorWithCtx(ctx, "checkWeddingPer Failed during errgroup execution: %+v, guildId:%d, uid:%d", err, guildId, uid)
		return false, err
	}

	return hasWeddingPer, nil
}
