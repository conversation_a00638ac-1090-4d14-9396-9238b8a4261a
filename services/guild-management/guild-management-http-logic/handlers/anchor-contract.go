package hanlders

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/protocol/common/status"
	anchorcontractPB "golang.52tt.com/protocol/services/anchorcontract-go"
	bizbalancekeeperPB "golang.52tt.com/protocol/services/bizbalancekeeper"
	esport_skill "golang.52tt.com/protocol/services/esport-skill"
	smsPB "golang.52tt.com/protocol/services/sms-go"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/models"
	api "golang.52tt.com/services/guild-management/guild-management-http-logic/models/gen-go"
)

// 权限检查
var checkUserPermission = func(ctx context.Context, uid, guildId, menuType uint32, w http.ResponseWriter) (ok bool) {
	_, _, permission, serr := GetUserPermission(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "failed to GetUserPermission err %+v, uid:%d guild:%d", serr, uid, guildId)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if (permission.GetGuildManageMenuPer().GetAnchorContractMenuPer() & uint32(menuType)) == 0 {
		log.ErrorWithCtx(ctx, "user no permission uid:%d guild:%d menuType:%d", uid, guildId, menuType)
		_ = web.ServeAPICodeJson(w, -505, "该用户没有此菜单权限", nil)
		return
	}
	return true
}

// ttid转uid,并校验
var getUids = func(ctx context.Context, ttidList []string, w http.ResponseWriter, resp proto.MessageV1) ([]uint32, error) {
	if len(ttidList) == 0 {
		return []uint32{}, nil
	}

	uidList, err := Tid2Uid(ctx, ttidList)
	if err != nil {
		serr := protocol.ToServerError(err)
		log.ErrorWithCtx(ctx, " failed to Tid2Uid err %+v", serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return nil, err
	}
	if len(ttidList) > 0 && len(uidList) == 0 {
		web.ServeAPIJson(w, resp)
		log.InfoWithCtx(ctx, "invaild ttid, ttidList:%v resp:%v", ttidList, resp)
		return nil, fmt.Errorf("invaild ttidList")
	}

	return uidList, nil
}

// 获取签约申请列表
func GetApplySignList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID
	req := &api.GetGuildApplySignRecordListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetApplySignList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "GetApplySignList begin %s %+v", string(authInfo.Body), req)

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractApplySign), w) {
		return
	}

	uidList, err := Tid2Uid(ctx, req.GetTtidList())
	if err != nil {
		serr := protocol.ToServerError(err)
		log.ErrorWithCtx(ctx, "GetApplySignList failed to Tid2Uid err %+v, uid:%d guild:%d", serr, uid, req.GuildId)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}
	if len(req.GetTtidList()) > 0 && len(uidList) == 0 {
		resp := &api.GetGuildApplySignRecordListResp{}
		web.ServeAPIJson(w, resp)
		log.InfoWithCtx(ctx, "GetApplySignList end invaild ttid, req:%v resp:%v", req, resp)
		return
	}

	applyListresp, serr := models.GetModelServer().AnchorcontractCli.GetGuildApplySignRecordList(ctx, &anchorcontractPB.GetGuildApplySignRecordListReq{
		QueryType: req.QueryType,
		GuildId:   req.GuildId,
		UidList:   uidList,
		Page:      req.Page,
		PageNum:   req.PageNum,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetApplySignList failed to GetGuildApplySignRecordList [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	resp := &api.GetGuildApplySignRecordListResp{
		Total:            applyListresp.GetTotal(),
		AudioLimitStatus: uint32(applyListresp.GetAudioLimitStatus()),
	}

	for _, info := range applyListresp.GetInfoList() {
		resp.InfoList = append(resp.InfoList, &api.ApplySignExtInfo{
			ApplyId:            info.ApplyId,
			AnchorUid:          info.AnchorUid,
			AnchorTtid:         info.AnchorTtid,
			AnchorAccount:      info.AnchorAccount,
			AnchorNickname:     info.AnchorNickname,
			AnchorSex:          info.AnchorSex,
			GuildId:            info.GuildId,
			ApplyIdentity:      info.ApplyIdentity,
			ApplyTime:          info.ApplyTime,
			ContractDuration:   info.ContractDuration,
			ContractExpireTime: info.ContractExpireTime,
			GuildOpStatus:      info.GuildOpStatus,
			OfficialOpStatus:   info.OfficialOpStatus,
			UpdateTime:         info.UpdateTime,
		})
	}

	web.ServeAPIJson(w, resp)
	log.DebugfWithCtx(ctx, "GetApplySignList end req:%+v resp:%+v", req, resp)
}

// 单个处理签约申请
func HandleApplySign(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID
	req := &api.PresidentHandleApplySignReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleApplySign Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "HandleApplySign begin %s %+v", string(authInfo.Body), req)

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractApplySign), w) {
		return
	}

	_, serr := models.GetModelServer().AnchorcontractCli.PresidentHandleApplySign(ctx, &anchorcontractPB.PresidentHandleApplySignReq{
		ApplyId:   req.ApplyId,
		HandleOpr: req.HandleOpr,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "HandleApplySign failed to PresidentHandleApplySign [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil) //-5493  该申请已失效
		return
	}

	resp := &api.PresidentHandleApplySignResp{}

	web.ServeAPIJson(w, resp)
	log.InfoWithCtx(ctx, "HandleApplySign end uid %d req:%v resp:%v", uid, req, resp)
}

// 批量同意签约申请
func BatchAcceptApplySign(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID
	req := &api.BatchPresidentHandleApplySignReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchAcceptApplySign Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "BatchAcceptApplySign begin %s %+v", string(authInfo.Body), req)

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractApplySign), w) {
		return
	}

	failCnt := uint32(0)
	for _, applyId := range req.GetApplyId() {
		_, serr := models.GetModelServer().AnchorcontractCli.PresidentHandleApplySign(ctx, &anchorcontractPB.PresidentHandleApplySignReq{
			ApplyId:   applyId,
			HandleOpr: uint32(anchorcontractPB.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_ACCEPT),
		})
		if serr != nil {
			log.ErrorWithCtx(ctx, "BatchAcceptApplySign failed to PresidentHandleApplySign [%+v], err %+v", req, serr)
			failCnt++
		}
	}

	resp := &api.BatchPresidentHandleApplySignResp{
		Total:   uint32(len(req.GetApplyId())),
		FailCnt: failCnt,
	}

	web.ServeAPIJson(w, resp)
	log.InfoWithCtx(ctx, "BatchAcceptApplySign end uid %d req:%v resp:%v", uid, req, resp)
}

// 获取解约申请列表
func GetApplyCancelContractList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID
	req := &api.GetGuildCancelSignRecordListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetApplyCancelContractList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "GetApplyCancelContractList begin %s %+v", string(authInfo.Body), req)

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractCancel), w) {
		return
	}

	uidList, err := Tid2Uid(ctx, req.GetTtidList())
	if err != nil {
		serr := protocol.ToServerError(err)
		log.ErrorWithCtx(ctx, "GetApplyCancelContractList failed to Tid2Uid err %+v, uid:%d guild:%d", serr, uid, req.GuildId)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}
	if len(req.GetTtidList()) > 0 && len(uidList) == 0 {
		resp := &api.GetGuildApplySignRecordListResp{}
		web.ServeAPIJson(w, resp)
		log.InfoWithCtx(ctx, "GetApplyCancelContractList end invaild ttid, req:%v resp:%v", req, resp)
		return
	}

	applyListresp, serr := models.GetModelServer().AnchorcontractCli.GetGuildCancelSignRecordList(ctx, &anchorcontractPB.GetGuildCancelSignRecordListReq{
		QueryType:  req.QueryType,
		GuildId:    req.GuildId,
		UidList:    uidList,
		Page:       req.Page,
		PageNum:    req.PageNum,
		CancelType: req.CancelType,
		StartTime:  req.StartTime,
		EndTime:    req.EndTime,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetApplyCancelContractList failed to GetGuildCancelSignRecordList [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	resp := &api.GetGuildCancelSignRecordListResp{
		Total: applyListresp.GetTotal(),
	}

	for _, info := range applyListresp.GetInfoList() {
		proofList := make([]*api.ProofShowContent, 0)
		for _, proof := range info.GetProofList() {
			proofList = append(proofList, &api.ProofShowContent{
				Url:  proof.GetUrl(),
				Type: api.ProofShowContent_ProofType(proof.GetType()),
			})
		}

		msg := &api.CancelSignExtInfo{
			ApplyId:            info.ApplyId,
			AnchorUid:          info.AnchorUid,
			AnchorTtid:         info.AnchorTtid,
			AnchorAccount:      info.AnchorAccount,
			AnchorNickname:     info.AnchorNickname,
			AnchorSex:          info.AnchorSex,
			GuildId:            info.GuildId,
			Identity:           info.Identity,
			TagName:            info.TagName,
			AgentUid:           info.AgentUid,
			AgentTtid:          info.AgentTtid,
			AgentNickname:      info.AgentNickname,
			SignTime:           info.SignTime,
			ContractExpireTime: info.ContractExpireTime,
			ApplyTime:          info.ApplyTime,
			Reason:             info.Reason,
			Status:             info.Status,
			CancelTime:         info.CancelTime,
			ReasonList:         info.ReasonList,
			OperTime:           info.UpdateTime,
			RejectTxt:          info.RejectTxt,
			ProofUrls:          info.ProofUrls,
			ProofVideoUrls:     info.ProofVideoUrls,
			OfficialNotes:      info.OfficialNotes,
			PayDesc:            info.PayDesc,
			PayAmount:          info.PayAmount,
			WorkerType:         info.WorkerType,
			ProofList:          proofList,
			CancelReason:       info.GetCancelReason(),
		}
		msg.OperType = uint32(anchorcontractPB.CANCEL_APPLY_OPR_CANCEL_APPLY_OPR_ACCEPT)
		switch info.CancelType {
		case uint32(anchorcontractPB.CancelContractType_CancelContractType_Negotiate):
			if info.Status == uint32(anchorcontractPB.CancelContractStatus_CancelContractStatus_Apply) {
				msg.OperType |= uint32(anchorcontractPB.CANCEL_APPLY_OPR_CANCEL_APPLY_OPR_REJECT)
			}
		case uint32(anchorcontractPB.CancelContractType_CancelContractType_Pay):
			if info.Status != uint32(anchorcontractPB.CancelContractStatus_CancelContractStatus_PayOfficeAccepted) {
				msg.OperType = 0
			}
		}
		switch info.Status {
		case uint32(anchorcontractPB.CancelContractStatus_CancelContractStatus_PayOfficeAccepted):
			msg.ResultDesc = "官方同意"
		case uint32(anchorcontractPB.CancelContractStatus_CancelContractStatus_PayOfficeReject):
			msg.ResultDesc = "官方拒绝"
		case uint32(anchorcontractPB.CancelContractStatus_CancelContractStatus_Apply):
			msg.ResultDesc = "等待会长处理"
		case uint32(anchorcontractPB.CancelContractStatus_CancelContractStatus_NegotiateReject):
			msg.ResultDesc = "等待官方处理"
		case uint32(anchorcontractPB.CancelContractStatus_CancelContractStatus_NegotiateOfficeReject):
			msg.ResultDesc = "官方拒绝"
		case uint32(anchorcontractPB.CancelContractStatus_CancelContractStatus_NegotiateOfficeAccepted):
			msg.ResultDesc = "官方同意,等待会长处理"
		case uint32(anchorcontractPB.CancelContractStatus_CancelContractStatus_Paying):
			msg.ResultDesc = "等待官方处理"
		case uint32(anchorcontractPB.CancelContractStatus_CancelContractStatus_Abort):
			msg.ResultDesc = "解约失败"
		case uint32(anchorcontractPB.CancelContractStatus_CancelContractStatus_Finish):
			msg.ResultDesc = "会长已同意"
		case uint32(anchorcontractPB.CancelContractStatus_CancelContractStatus_NegotinateFinish):
			msg.ResultDesc = "会长同意+官方同意"
		case uint32(anchorcontractPB.CancelContractStatus_CancelContractStatus_PayFreeze):
			msg.ResultDesc = "等待用户上传凭证"
		default:
			msg.ResultDesc = "未知状态"
		}
		for _, identityInfo := range info.GetIdentityInfoList() {
			msg.IdentityInfoList = append(msg.IdentityInfoList, &api.AnchorIdentityInfo{IdentityType: identityInfo.IdentityType, ObtainTime: identityInfo.ObtainTime})
		}

		resp.InfoList = append(resp.InfoList, msg)
	}

	web.ServeAPIJson(w, resp)
	log.DebugfWithCtx(ctx, "GetApplySignList end req:%v resp:%v", req, resp)
}

// 同意解约申请
func AcceptCancelContractApply(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Minute)
	defer cancel()

	uid := authInfo.UserID
	req := &api.AcceptCancelContractApplyReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptCancelContractApply Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.InfoWithCtx(ctx, "AcceptCancelContractApply begin %s %+v", string(authInfo.Body), req)

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractCancel), w) {
		return
	}

	_, serr := models.GetModelServer().AnchorcontractCli.HandlerCancelContractApply(ctx, &anchorcontractPB.HandlerCancelContractApplyReq{
		Uid:            uid,
		GuildId:        req.GuildId,
		TargetUid:      req.AnchorUid,
		HandleOpr:      req.HandleOperType,
		RejectTxt:      req.HandleDesc,
		ProofUrls:      req.HandleProofUrls,
		ProofVideoUrls: req.HandleProofVideoUrls,
		ApplyId:        req.ApplyId,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "AcceptCancelContractApply failed to HandlerCancelContractApply [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	resp := &api.AcceptCancelContractApplyResp{}
	_ = web.ServeAPIJson(w, resp)
	log.InfoWithCtx(ctx, "AcceptCancelContractApply uid %d, req %+v", uid, req)
}

// 获取签约成员列表
func GetAnchorList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	uid := authInfo.UserID
	req := &api.GetGuildAnchorExtInfoListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetAnchorList begin %s %+v", string(authInfo.Body), req)

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractList), w) {
		return
	}

	resp := &api.GetGuildAnchorExtInfoListResp{Total: 0}

	anchorUids, err := getUids(ctx, req.GetAnchorTtidList(), w, resp)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorList fail getUids err %v, uid %d guildId %d", err, uid, req.GetGuildId())
		return
	}
	agentUids, err := getUids(ctx, req.GetAgentTtidList(), w, resp)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorList fail getUids err %v, uid %d guildId %d", err, uid, req.GetGuildId())
		return
	}

	anchorListResp, serr := models.GetModelServer().AnchorcontractCli.GetGuildAnchorExtInfoList(ctx, &anchorcontractPB.GetGuildAnchorExtInfoListReq{
		QueryType:     req.QueryType,
		GuildId:       req.GuildId,
		AnchorUidList: anchorUids,
		AgentUidList:  agentUids,
		Page:          req.Page,
		PageNum:       req.PageNum,
		IsAllAgent:    req.IsAllAgent,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetAnchorList failed to GetGuildAnchorExtInfoList [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}
	log.DebugWithCtx(ctx, "GetAnchorList uid %d, req %+v anchorListResp %s", uid, req, utils.ToJson(anchorListResp))

	resp.Total = anchorListResp.GetTotal()
	for _, info := range anchorListResp.GetInfoList() {

		msg := &api.AnchorExtInfo{
			AnchorUid:          info.AnchorUid,
			AnchorTtid:         info.AnchorTtid,
			AnchorAccount:      info.AnchorAccount,
			AnchorNickname:     info.AnchorNickname,
			AnchorSex:          info.AnchorSex,
			GuildId:            info.GuildId,
			TagName:            info.TagName,
			SignTime:           info.SignTime,
			ContractExpireTime: info.ContractExpireTime,

			AgentUid:      info.AgentUid,
			AgentTtid:     info.AgentTtid,
			AgentNickname: info.AgentNickname,

			LastLiveAt:        info.LastLiveAt,
			Day30LiveHour:     info.Day30LiveHour,
			Day30LiveValidDay: info.Day30LiveValidDay,
			Day30HoldValidDay: info.Day30HoldValidDay,

			IsFocus:             info.IsFocus,
			IsExtensionContract: info.IsExtensionContract,
			IsCancalContract:    info.IsCancalContract,
			Remark:              info.Remark,
			PracType:            info.GetPracType(),
			InviteButton:        info.GetInviteButton(),
			PayAmount:           info.PayAmount,
		}

		for _, identityInfo := range info.GetIdentityInfoList() {
			msg.IdentityInfoList = append(msg.IdentityInfoList, &api.AnchorIdentityInfo{IdentityType: identityInfo.IdentityType, ObtainTime: identityInfo.ObtainTime})
		}

		resp.InfoList = append(resp.InfoList, msg)
	}

	web.ServeAPIJson(w, resp)
	log.DebugWithCtx(ctx, "GetAnchorList uid %d, req %+v, out %s", uid, req, utils.ToJson(resp))
}

// 关注/取消关注 某人
func HandleFocusAnchor(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID
	req := &api.HandleFocusAnchorReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFocusAnchor Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "HandleFocusAnchor begin %s %+v", string(authInfo.Body), req)

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractList), w) {
		return
	}

	_, serr := models.GetModelServer().AnchorcontractCli.HandleFocusAnchor(ctx, &anchorcontractPB.HandleFocusAnchorReq{
		GuildId:   req.GuildId,
		AnchorUid: req.AnchorUid,
		HandleOpr: req.HandleOpr,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "HandleFocusAnchor failed  [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	resp := &api.EmptyResp{}
	web.ServeAPIJson(w, resp)
	log.DebugWithCtx(ctx, "HandleFocusAnchor uid %d, req %+v, out %s", uid, req)
}

// 备注
func UpdateRemark(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID
	req := &api.UpdateRemarkReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateRemark Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "UpdateRemark begin %s %+v", string(authInfo.Body), req)

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractList), w) {
		return
	}

	_, serr := models.GetModelServer().AnchorcontractCli.UpdateRemark(ctx, &anchorcontractPB.UpdateRemarkReq{
		GuildId:   req.GuildId,
		AnchorUid: req.AnchorUid,
		Remark:    req.Remark,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "UpdateRemark failed  [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	resp := &api.EmptyResp{}
	web.ServeAPIJson(w, resp)
	log.DebugWithCtx(ctx, "UpdateRemark uid %d, req %+v", uid, req)
}

func HandleRenew(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID
	req := &api.GuildExtensionContractReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleRenew Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "HandleRenew begin %s %+v", string(authInfo.Body), req)

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractList), w) {
		return
	}

	_, serr := models.GetModelServer().AnchorcontractCli.GuildExtensionContract(ctx, &anchorcontractPB.GuildExtensionContractReq{
		GuildId:   req.GuildId,
		AnchorUid: req.AnchorUid,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "GuildExtensionContract failed  [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	resp := &api.EmptyResp{}
	web.ServeAPIJson(w, resp)
	log.DebugWithCtx(ctx, "HandleRenew uid %d, req %+v", uid, req)
}

func HandleBatchRenew(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	uid := authInfo.UserID
	req := &api.BatchGuildExtensionContractReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleBatchRenew Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "HandleBatchRenew begin %s %+v", string(authInfo.Body), req)

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractList), w) {
		return
	}

	_, serr := models.GetModelServer().AnchorcontractCli.BatchGuildExtensionContract(ctx, &anchorcontractPB.BatchGuildExtensionContractReq{
		GuildId:       req.GuildId,
		AnchorUidList: req.AnchorUidList,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "BatchGuildExtensionContract failed  [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	resp := &api.EmptyResp{}
	web.ServeAPIJson(w, resp)
	log.DebugWithCtx(ctx, "HandleBatchRenew uid %d, req %+v", uid, req)
}

func HandleRescind(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	uid := authInfo.UserID
	req := &api.CancelAnchorContractReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleRescind Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "HandleRescind begin %s %+v", string(authInfo.Body), req)

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractList), w) {
		return
	}

	_, serr := models.GetModelServer().AnchorcontractCli.CancelContractByUid(ctx, &anchorcontractPB.CancelContractByUidReq{
		OpUid:     uid,
		GuildId:   req.GuildId,
		TargetUid: req.AnchorUid,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "CancelContractByUid failed  [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	resp := &api.EmptyResp{}
	web.ServeAPIJson(w, resp)
	log.InfoWithCtx(ctx, "HandleRescind uid %d, req %+v", uid, req)
}

func HandleBatchRescind(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	uid := authInfo.UserID
	req := &api.BatchCancelAnchorContractReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleBatchRescind Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "HandleBatchRescind begin %s %+v", string(authInfo.Body), req)

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractList), w) {
		return
	}

	//
	isErr := false
	errMess := ""
	errCodeMap := make(map[int32]int32, 0)
	errCode := int32(0)

	for _, anchorUid := range req.GetAnchorUidList() {
		_, serr := models.GetModelServer().AnchorcontractCli.CancelContractByUid(ctx, &anchorcontractPB.CancelContractByUidReq{
			OpUid:     uid,
			GuildId:   req.GuildId,
			TargetUid: anchorUid,
		})
		if serr != nil {
			isErr = true
			errCode = int32(serr.Code())
			if _, ok := errCodeMap[int32(serr.Code())]; !ok {
				errMess += serr.Message()
				errCodeMap[int32(serr.Code())] = int32(serr.Code())
			}
			log.ErrorWithCtx(ctx, "CancelContractByUid failed  [%+v], err %+v", req, serr)
			//_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		}
	}

	if isErr {
		_ = web.ServeAPICodeJson(w, errCode, errMess, nil)
		return
	}
	resp := &api.EmptyResp{}
	web.ServeAPIJson(w, resp)

	log.InfoWithCtx(ctx, "HandleBatchRescind uid %d, req %+v", uid, req)
}

func HandleGenVerifyCode(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &struct {
		GuildId uint32 `json:"guild_id"`
	}{}

	//req := &api.GenVerifyCodeReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleGenVerifyCode Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "HandleGenVerifyCode begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractList), w) {
		return
	}

	getIdentifyInfoResp, err := models.GetModelServer().CommissionClient.GetIdentifyInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to GetIdentifyInfo uid(%d) err %+v", uid, err)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	} else if getIdentifyInfoResp.IdentityCard == "" {
		log.ErrorWithCtx(ctx, "Failed to GetIdentifyInfo uid(%d) user identityCard info error", uid)
		_ = web.ServeAPICodeJson(w, -500, "IdentityCard empty", nil)
		return
	} else if !(getIdentifyInfoResp.Status == 1 && getIdentifyInfoResp.Level == 2) {
		log.ErrorWithCtx(ctx, "Failed to GetIdentifyInfo uid(%d) must status=1&level=2 getIdentifyInfoResp: %+v", uid, getIdentifyInfoResp)
		_ = web.ServeAPICodeJson(w, -506, "该账号未完成人脸识别", nil)
		return
	}
	log.DebugfWithCtx(ctx, "GetIdentifyInfo uid(%d) %+v", uid, getIdentifyInfoResp)

	user, err := models.GetModelServer().AccountCli.GetUserByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to GetUserByUid GetIdentifyInfo uid(%d) err %+v", uid, err)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}
	phone := user.GetPhone()
	if phone == "" {
		log.ErrorWithCtx(ctx, "HandleGenVerifyCode get phone is null, uid %d", uid)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}

	// 1. gen code.
	key := genAnchorVerifyKey(uid)
	code, err := verifyCodeClient.CreateVerifyCodeByKey(ctx, key, 4, 5*60)
	if err != nil {
		log.ErrorWithCtx(ctx, "verifyCodeClient.CreateVerifyCodeByKey failed, key=[%s], err=[%v]", key, err)
		_ = web.ServeAPICodeJson(w, -500, "gen code fail", nil)
		return
	}

	log.DebugfWithCtx(ctx, "HandleGenVerifyCode get bind phone %s, uid %d alias %q nickname %q", phone, uid, user.GetAlias(), user.GetNickname())

	params := []string{user.GetAlias(), user.GetNickname(), code}
	smsType := models.GetModelServer().GetDyConfig().GetSmsType("anchor_rescind")
	if smsType == 0 {
		log.ErrorWithCtx(ctx, "HandleGenVerifyCode get smsType is 0, uid %d guildId %d", uid, req.GuildId)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}

	smsReq := &smsPB.SendSmsReq{
		Phone:           phone,
		SmsType:         smsType,
		ParamList:       params,
		WithoutCooldown: false,
		VerifyCodeKey:   key,
		VerifyCodeUsage: "auth",
		MarketId:        0,
		BizId:           uint32(bizbalancekeeperPB.BizId_TT),
	}
	log.InfoWithCtx(ctx, "HandleGenVerifyCode SendSmsReq %+v. uid %d guildId %d", smsReq, uid, req.GuildId)

	smsErr := models.GetModelServer().SmsGoCli.SendSmsV2(ctx, smsReq)
	if smsErr != nil {
		log.ErrorWithCtx(ctx, "sms.SendSms failed, uid=[%d], phone=[%s], err=[%v]", uid, phone, smsErr)
		if smsErr.Code() == int(smsPB.ERR_SMS_ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED) {
			log.ErrorWithCtx(ctx, "今日验证码发送次数已达上限 uid:%d", uid)
			_ = web.ServeAPICodeJson(w, -500, "今日验证码发送次数已达上限", nil)
			return
		} else if smsErr.Code() == int(smsPB.ERR_SMS_ERR_SMS_SEND_SMS_FREQ) {
			log.ErrorWithCtx(ctx, "今日验证码发送操作太过频繁 req uid:%d", uid)
			_ = web.ServeAPICodeJson(w, -500, "操作太过频繁", nil)
			return
		}
		log.ErrorWithCtx(ctx, "验证码发送 uid:%d err:%+v", uid, smsErr)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}

	resp := &api.GenVerifyCodeResp{}
	log.InfoWithCtx(ctx, "HandleGenVerifyCode end uid %d req:%v code %s phone %s resp:%v", uid, req, code, phone, resp)
	web.ServeAPIJson(w, resp)
}

func HandleCheckVerifyCode(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.CheckVerifyCoderReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleCheckVerifyCode Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "HandleCheckVerifyCode begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractList), w) {
		return
	}

	whiteCode := models.GetModelServer().GetDyConfig().GetWhiteVerifyCode(req.GuildId)
	if whiteCode != "" {
		if whiteCode == req.Code {
			resp := &api.EmptyResp{}
			web.ServeAPIJson(w, resp)
		} else {
			resp := &api.ErrResp{ErrMsg: status.CodeMessageMap[status.ErrVerifycodeWrongCode]}
			web.ServeAPICodeJson(w, 0, status.CodeMessageMap[status.ErrVerifycodeWrongCode], resp)
		}
		log.InfoWithCtx(ctx, "HandleCheckVerifyCode white uid %d guildId %d code %s %s", uid, req.GuildId, req.Code, whiteCode)
		return
	}

	// 1. check code.
	key := genAnchorVerifyKey(uid)
	serr := verifyCodeClient.ValidateVerifyCode(ctx, key, req.Code)
	if serr != nil {
		log.ErrorWithCtx(ctx, "HandleBatchRescind ValidateVerifyCode err:%v, uid %d in %+v", serr, uid, req)
		resp := &api.ErrResp{ErrMsg: serr.Message()}
		_ = web.ServeAPICodeJson(w, 0, serr.Message(), resp)
		return
	}
	resp := &api.EmptyResp{}
	web.ServeAPIJson(w, resp)
}

func genAnchorVerifyKey(uid uint32) string {
	return fmt.Sprintf("guild_rescind_%d", uid)
}

func GetGameSkillDetial(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID
	req := &api.GetGameSkillDetialReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameSkillDetial Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetGameSkillDetial begin %s %+v", string(authInfo.Body), req)

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractApplySign), w) {
		return
	}

	var skillList []*esport_skill.UserSkillInfo
	if req.ApplyId > 0 {
		auditToken, err := models.GetModelServer().AnchorcontractCli.GetSignEsportAuditToken(ctx, req.ApplyId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGameSkillDetial GetSignEsportAuditToken fail %v, uid %d guildId %d", err, uid, req.GuildId)
			_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
			return
		}

		skillReq := &esport_skill.GetUserAuditSkillRequest{
			Uid:           req.AnchorUid,
			AuditToken:    auditToken,
			AuditSource:   uint32(esport_skill.AuditSource_AUDIT_SOURCE_NEW_ROLE),
			WithUrlPrefix: true,
		}
		skillResp, err := models.GetModelServer().EsportSkillCli.GetUserAuditSkill(ctx, skillReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGameSkillDetial GetUserAuditSkill fail %v, uid %d guildId %d", err, uid, req.GuildId)
			_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
			return
		}
		skillList = skillResp.GetSkill()
		log.DebugWithCtx(ctx, "GetGameSkillDetial skillReq=%+v GetUserAuditSkill=%s", skillReq, skillResp.String())
	} else {
		skillReq := &esport_skill.GetUserCurrentSkillRequest{
			Uid:           req.AnchorUid,
			WithUrlPrefix: true,
		}
		skillResp, err := models.GetModelServer().EsportSkillCli.GetUserCurrentSkill(ctx, skillReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGameSkillDetial GetUserCurrentSkill fail %v, uid %d guildId %d", err, uid, req.GuildId)
			_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
			return
		}

		skillList = skillResp.GetSkill()
		log.DebugWithCtx(ctx, "GetGameSkillDetial skillReq=%+v GetUserCurrentSkill=%s", skillReq, skillResp.String())
	}

	gameInfo, err := models.GetModelServer().EsportSkillCli.GetAllGameSimpleInfo(ctx, &esport_skill.GetAllGameSimpleInfoRequest{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameSkillDetial GetAllGameSimpleInfo fail %v, uid %d guildId %d", err, uid, req.GuildId)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}
	gameId2Info := map[uint32]*esport_skill.SimpleGameInfo{}
	for _, game := range gameInfo.GetGameList() {
		gameId2Info[game.GameId] = game
	}

	newSkillList := []*esport_skill.UserSkillInfo{}
	mobileSkillList := []*esport_skill.UserSkillInfo{}
	pcSkillList := []*esport_skill.UserSkillInfo{}
	for _, skill := range skillList {
		if gameId2Info[skill.GetGameId()].GetGameType() == esport_skill.GAME_TYPE_GAME_TYPE_MOBILE {
			mobileSkillList = append(mobileSkillList, skill)
		} else if gameId2Info[skill.GetGameId()].GetGameType() == esport_skill.GAME_TYPE_GAME_TYPE_PC {
			pcSkillList = append(pcSkillList, skill)
		}
	}
	newSkillList = append(newSkillList, mobileSkillList...)
	newSkillList = append(newSkillList, pcSkillList...)

	log.DebugWithCtx(ctx, "GetGameSkillDetial uid=%d change [%+v]->[%+v]", req.AnchorUid, skillList, newSkillList)

	resp := &api.GetGameSkillDetialResp{}
	for _, info := range newSkillList {
		skillInfo := &api.UserSkillInfo{
			GameId:        info.GameId,
			GameName:      info.GameName,
			SkillEvidence: info.SkillEvidence,
			Audio:         info.Audio,
			AudioDuration: info.AudioDuration,
			TextDesc:      info.TextDesc,
		}
		for _, sec := range info.SectionList {
			skillInfo.SectionList = append(skillInfo.SectionList, &api.SectionInfo{
				SectionName: sec.SectionName,
				ItemList:    sec.ItemList,
			})
		}
		resp.List = append(resp.List, skillInfo)
	}

	web.ServeAPIJson(w, resp)
	log.DebugWithCtx(ctx, "GetGameSkillDetial uid %d, req=%+v resp=%+v", uid, req, resp)
}

// 获取公会解约方式列表
func GetCancelContractTypeList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	req := &api.GetCancelContractTypeListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCancelContractTypeList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetCancelContractTypeList begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, api.AnchorContractMenuType_AnchorContracCancelContractType.String())
	if !hasPer {
		web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	typeRsp, sErr := models.GetModelServer().AnchorcontractCli.GetCancelContractTypeList(ctx, &anchorcontractPB.GetCancelContractTypeListReq{
		GuildId: req.GuildId,
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetCancelContractTypeList failed to GetCancelContractTypeList [%+v], sErr %+v", req, sErr)
		_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
		return
	}

	resp := &api.GetCancelContractTypeListResp{}
	for _, v := range typeRsp.GetTypeList() {
		pracCancelInfo := &api.PracCancelContractTypeInfo{
			PracType:     v.GetPracType(),
			TimeDesc:     v.GetTimeDesc(),
			PracTypeDesc: v.GetPracTypeDesc(),
			InfoV2List:   make([]*api.CancelContractTypeInfoV2, 0),
			IsCanEdit:    v.GetIsCanEdit(),
		}

		for _, vv := range v.InfoList {
			item := &api.CancelContractTypeInfoV2{
				CancelType: vv.GetCancelType(),
				Tilte:      vv.GetTilte(),
				Desc:       vv.GetDescGuildManage(),
				IsSelect:   vv.GetIsSelect(),
				IsDefault:  vv.GetIsDefault(),
			}
			pracCancelInfo.InfoV2List = append(pracCancelInfo.InfoV2List, item)
		}

		resp.TypeList = append(resp.TypeList, pracCancelInfo)
	}

	log.DebugWithCtx(ctx, "GetCancelContractTypeList uid %d, req %+v resp %s", uid, req, utils.ToJson(resp))
	web.ServeAPIJson(w, resp)
}

// 获取公会解约方式列表
func SetGuildCancelContractType(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	req := &api.SetGuildCancelContractTypeReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetGuildCancelContractType Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "SetGuildCancelContractType begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, api.AnchorContractMenuType_AnchorContracCancelContractType.String())
	if !hasPer {
		web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	_, sErr := models.GetModelServer().AnchorcontractCli.SetGuildCancelContractType(ctx, &anchorcontractPB.SetGuildCancelContractTypeReq{
		GuildId:        req.GetGuildId(),
		PracType:       req.GetPracType(),
		CancelTypeList: req.GetCancelTypeList(),
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "SetGuildCancelContractType failed to GetCancelContractTypeList [%+v], sErr %+v", req, sErr)
		_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
		return
	}

	resp := &api.SetGuildCancelContractTypeResp{}

	log.DebugWithCtx(ctx, "SetGuildCancelContractType uid %d, req %+v resp %s", uid, req, utils.ToJson(resp))
	web.ServeAPIJson(w, resp)
}

// 获取公会签约权益列表
func GetGuildSignRight(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	req := &api.GetGuildSignRightReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildSignRight Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetGuildSignRight begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, api.AnchorContractMenuType_AnchorContracSignRight.String())
	if !hasPer {
		web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	rightResp, sErr := models.GetModelServer().AnchorcontractCli.GetGuildSignRight(ctx, &anchorcontractPB.GetGuildSignRightReq{
		GuildId: req.GuildId,
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetGuildSignRight failed to GetCancelContractTypeList [%+v], sErr %+v", req, sErr)
		_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
		return
	}

	resp := &api.GetGuildSignRightResp{}
	for _, right := range rightResp.GetRightList() {
		pracRight := &api.PracSignRight{
			PracType:  right.GetPracType(),
			RightList: make([]*api.SignRight, 0),
			PracName:  right.GetPracName(),
		}
		for _, group := range right.GetRightList() {
			groupInfo := &api.SignRight{
				Id:           group.GetId(),
				Name:         group.GetName(),
				SubRightList: make([]*api.SignSubRight, 0),
				Icon:         group.GetIcon(),
			}
			for _, subRight := range group.SubRightList {
				groupInfo.SubRightList = append(groupInfo.SubRightList, &api.SignSubRight{
					Id:        subRight.GetId(),
					Name:      subRight.GetName(),
					Icon:      subRight.GetIcon(),
					IsSelect:  subRight.GetIsSelect(),
					IsDefault: subRight.GetIsDefault(),
					Note:      subRight.GetNote(),
				})
			}
			pracRight.RightList = append(pracRight.RightList, groupInfo)
		}

		resp.RightList = append(resp.RightList, pracRight)
	}

	log.DebugWithCtx(ctx, "GetGuildSignRight uid %d, req %+v resp %s", uid, req, utils.ToJson(resp))
	web.ServeAPIJson(w, resp)
}

// 获取公会解约方式列表
func UpdateGuildSignRight(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	req := &api.UpdateGuildSignRightReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateGuildSignRight Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "UpdateGuildSignRight begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, api.AnchorContractMenuType_AnchorContracSignRight.String())
	if !hasPer {
		web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	rightList := make([]*anchorcontractPB.SignRight, 0)
	for _, right := range req.GetRight().GetRightList() {
		tmpRight := &anchorcontractPB.SignRight{
			Id:           right.GetId(),
			SubRightList: make([]*anchorcontractPB.SignSubRight, 0),
		}

		for _, subRight := range right.GetSubRightList() {
			// 只有选中的才会传
			if subRight.GetIsSelect() {
				tmpRight.SubRightList = append(tmpRight.SubRightList, &anchorcontractPB.SignSubRight{
					Id:       subRight.GetId(),
					IsSelect: subRight.GetIsSelect(),
				})
			}
		}

		rightList = append(rightList, tmpRight)
	}

	_, sErr := models.GetModelServer().AnchorcontractCli.UpdateGuildSignRight(ctx, &anchorcontractPB.UpdateGuildSignRightReq{
		GuildId: req.GetGuildId(),
		Right: &anchorcontractPB.PracSignRight{
			PracType:  req.GetRight().GetPracType(),
			RightList: rightList,
		},
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "UpdateGuildSignRight failed to UpdateGuildSignRight [%+v], sErr %+v", req, sErr)
		_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
		return
	}

	resp := &api.SetGuildCancelContractTypeResp{}

	log.DebugWithCtx(ctx, "UpdateGuildSignRight uid %d, req %+v resp %s", uid, req, utils.ToJson(resp))
	web.ServeAPIJson(w, resp)
}

// 邀请晋升
func InvitePromote(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	uid := authInfo.UserID
	req := &api.InvitePromoteReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "InvitePromote Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "InvitePromote begin %s %+v", string(authInfo.Body), req)

	if !checkUserPermission(ctx, uid, req.GuildId, uint32(api.AnchorContractMenuType_AnchorContractList), w) {
		return
	}

	_, serr := models.GetModelServer().AnchorcontractCli.InvitePromote(ctx, &anchorcontractPB.InvitePromoteReq{
		GuildId:      req.GetGuildId(),
		InvitedUid:   req.GetInvitedUid(),
		CancelAmount: req.GetCancelAmount(),
		SignMonths:   req.GetSignMonths(),
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "InvitePromote failed to GetGuildAnchorExtInfoList [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	resp := &api.InvitePromoteResp{}

	web.ServeAPIJson(w, resp)
	log.DebugWithCtx(ctx, "InvitePromote uid %d, req %+v, out %s", uid, req, utils.ToJson(resp))
}
