package main

import (
	"fmt"
	"math"
	"net/http"
	"os"
	"time"

	"github.com/gorilla/mux"
	"github.com/urfave/cli"
	"gitlab.ttyuyin.com/golang/gudetama/log"
	"golang.52tt.com/pkg/admin"
	"golang.52tt.com/pkg/config"
	log2 "golang.52tt.com/pkg/log"
	webmonitor "golang.52tt.com/pkg/monitor/web"
	"golang.52tt.com/pkg/versioning/svn"
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/pkg/web/metrics"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/filter"
	hanlders "golang.52tt.com/services/guild-management/guild-management-http-logic/handlers"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/models"
	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
)

func main() {
	app := cli.NewApp()
	app.Version = svn.CodeRevision
	app.Flags = []cli.Flag{
		cli.StringFlag{
			Name:  "config",
			Value: "",
			Usage: "config path",
		},
	}

	cli.VersionPrinter = func(c *cli.Context) {
		fmt.Fprintf(os.Stdout, "%s\n%s\n", c.App.Name, c.App.Version)
	}

	app.Action = func(c *cli.Context) error {
		log.Init("guild-management-http-logic", log.DebugLevel, log.UseDefaultLogShmConfigPath)
		log2.SetLevel(log.DebugLevel)

		r := mux.NewRouter()

		configPath := c.String("config")
		cfg, err := config.NewConfig("json", configPath)
		if err != nil {
			log.Fatalln("Failed to NewConfig: ", err)
			panic(err)
		}

		err = models.GetModelServer().InitWithConfigerEx(cfg)
		if err != nil {
			log.Fatalln("Failed to LoadModelConfig:", err)
			return err
		}

		auth := web.NewAuth(&web.UidAuthVerify{IsGetTokenFromHeader: true}, models.GetModelServer().GetServiceConfig().GetValidateToken())
		baseHandler := web.NewHandler(auth, models.GetModelServer().GetServiceConfig().GetCors(), true)

		// handlers must init before use
		hanlders.Init()

		p := r.PathPrefix("/guild-management").Subrouter()

		generalRouter := p.PathPrefix("/general").Subrouter()
		generalRouter.Handle("/uploadFile", baseHandler.SetHandler(hanlders.UploadFile))

		guildLiveStats := p.PathPrefix("/guild-live-stats").Subrouter()
		guildLiveStats.Handle("/liveMonthSum", baseHandler.SetHandler(hanlders.GetGuildLiveMonthlyStatsHandler))
		guildLiveStats.Handle("/liveDaySumByMonth", baseHandler.SetHandler(hanlders.GetGuildLiveDailyStatsByMonthHandle))
		guildLiveStats.Handle("/liveDayStatsInfo", baseHandler.SetHandler(hanlders.GetGuildLiveDailyStatsInfoHandler))
		guildLiveStats.Handle("/liveWeekStatsInfo", baseHandler.SetHandler(hanlders.GetGuildLiveWeekStatsInfoHandler))
		guildLiveStats.Handle("/liveMonthStatsInfo", baseHandler.SetHandler(hanlders.GetGuildLiveMonthStatsInfoHandler))
		guildLiveStats.Handle("/getAnchorTotalData", baseHandler.SetHandler(hanlders.GetAnchorTotalDataHandler))
		guildLiveStats.Handle("/getAnchorDayDetailData", baseHandler.SetHandler(hanlders.GetAnchorDayDetailDataHandler))
		guildLiveStats.Handle("/getAnchorWeekDetailData", baseHandler.SetHandler(hanlders.GetAnchorWeekDetailDataHandler))
		guildLiveStats.Handle("/getAnchorMonthDetailData", baseHandler.SetHandler(hanlders.GetAnchorMonthDetailDataHandler))
		guildLiveStats.Handle("/getGuildTaskList", baseHandler.SetHandler(hanlders.GetGuildTaskListHandler))
		guildLiveStats.Handle("/getGuildMonthDetailDataByType", baseHandler.SetHandler(hanlders.GetGuildMonthDetailDataByTypeHandler))
		guildLiveStats.Handle("/getAnchorMonthlyStats", baseHandler.SetHandler(hanlders.GetAnchorMonthlyStatsHandler))
		guildLiveStats.Handle("/getAnchorWeeklyStats", baseHandler.SetHandler(hanlders.GetAnchorWeeklyStatsHandler))

		accountRouter := p.PathPrefix("/account").Subrouter()
		accountRouter.Handle("/getUserInfo", baseHandler.SetHandler(hanlders.GetUserInfo))
		accountRouter.Handle("/getUserGuildManagePermis", baseHandler.SetHandler(hanlders.GetUserGuildManagePermis))
		accountRouter.Handle("/getWhiteWhaleAccessToken", baseHandler.SetHandler(hanlders.GetWhiteWhaleAccessToken))
		accountRouter.Handle("/getGuildCooperationInfo", baseHandler.SetHandler(hanlders.GetGuildCooperationInfo))

		guildAgentRouter := p.PathPrefix("/guild-agent").Subrouter()
		guildAgentRouter.Handle("/getGuildAgentList", baseHandler.SetHandler(hanlders.GetGuildAgentList))
		guildAgentRouter.Handle("/addGuildAgent", baseHandler.SetHandler(hanlders.AddGuildAgent))
		guildAgentRouter.Handle("/delGuildAgent", baseHandler.SetHandler(hanlders.DelGuildAgent))
		guildAgentRouter.Handle("/updateAgentDataPermission", baseHandler.SetHandler(hanlders.UpdateAgentDataPermission))
		guildAgentRouter.Handle("/getAgentAnchorList", baseHandler.SetHandler(hanlders.GetAgentAnchorList))
		guildAgentRouter.Handle("/getAnchorAgentInfo", baseHandler.SetHandler(hanlders.GetAnchorAgentInfo))
		guildAgentRouter.Handle("/addAgentAnchor", baseHandler.SetHandler(hanlders.AddAgentAnchor))
		guildAgentRouter.Handle("/delAgentAnchor", baseHandler.SetHandler(hanlders.DelAgentAnchor))
		guildAgentRouter.Handle("/getGuildAgentInfo", baseHandler.SetHandler(hanlders.GetGuildAgentInfo))
		guildAgentRouter.Handle("/sendGuildAgentInvite", baseHandler.SetHandler(hanlders.SendGuildAgentInvite))
		guildAgentRouter.Handle("/getGuildAgentInviteList", baseHandler.SetHandler(hanlders.GetGuildAgentInviteList))
		guildAgentRouter.Handle("/cancelGuildAgentInvite", baseHandler.SetHandler(hanlders.CancelGuildAgentInvite))
		guildAgentRouter.Handle("/getGuildAdminList", baseHandler.SetHandler(hanlders.GetGuildAdminList)) // 获取公会管理员列表

		// 对公账户
		guildCommission := p.PathPrefix("/guild-commission").Subrouter()
		guildCommission.Handle("/getAccountInfo", baseHandler.SetHandler(hanlders.GetAccountInfo))                             // 获取银行卡账户信息
		guildCommission.Handle("/getGuildOwnerCorpApplyStatus", baseHandler.SetHandler(hanlders.GetGuildOwnerCorpApplyStatus)) // 获取对公账户申请状态
		guildCommission.Handle("/submitGuildOwnerCorpApply", baseHandler.SetHandler(hanlders.SubmitGuildOwnerCorpApply))       // 提交对公账户申请
		guildCommission.Handle("/getGuildOwnerCorpApplyList", baseHandler.SetHandler(hanlders.GetGuildOwnerCorpApplyList))     // 获取对公账户申请列表
		guildCommission.Handle("/updateAccountInfo", baseHandler.SetHandler(hanlders.UpdateAccountInfo))                       // 更新银行卡账户信息
		guildCommission.Handle("/genVerifyCode", baseHandler.SetHandler(hanlders.GenVerifyCode))                               // 生成验证码
		guildCommission.Handle("/getBankList", baseHandler.SetHandler(hanlders.GetBankList))                                   // 获取银行信息列表
		guildCommission.Handle("/checkBankcard", baseHandler.SetHandler(hanlders.CheckBankcard))                               // 检验银行卡信息
		// 结算汇总
		guildCommission.Handle("/getIncomeSummary", baseHandler.SetHandler(hanlders.GetIncomeSummary)) // 未结算数据，各种详情信息
		guildCommission.Handle("/getIncomeStats", baseHandler.SetHandler(hanlders.GetIncomeStats))     // 未结算数据，统计
		// 结算单详情
		guildCommission.Handle("/getScoreBillDetail", baseHandler.SetHandler(hanlders.GetScoreBillDetail))                         // 对公积分结算单详情详情
		guildCommission.Handle("/getAmuseRoomBillDetail", baseHandler.SetHandler(hanlders.GetAmuseRoomBillDetail))                 // 娱乐房结算单详情
		guildCommission.Handle("/getYuyinBaseBillDetail", baseHandler.SetHandler(hanlders.GetYuyinBaseBillDetail))                 // 语音基础结算单详情
		guildCommission.Handle("/getYuyinExtraBillDetail", baseHandler.SetHandler(hanlders.GetYuyinExtraBillDetail))               // 语音奖励结算单奖励详情
		guildCommission.Handle("/getDeepCoopBillDetail", baseHandler.SetHandler(hanlders.GetDeepCoopBillDetail))                   // 多人互动深度合作结算单明细
		guildCommission.Handle("/getYuyinAnchorSubsidyDetail", baseHandler.SetHandler(hanlders.GetYuyinAnchorSubsidyDetail))       // 语音直播补贴结算单明细 - 上部分
		guildCommission.Handle("/getNewGuildSubsidyDetail", baseHandler.SetHandler(hanlders.GetNewGuildSubsidyDetail))             // 语音直播补贴结算单明细 - 下部分
		guildCommission.Handle("/getAmuseExtraBillDetail", baseHandler.SetHandler(hanlders.GetAmuseExtraBillDetail))               // 语音直播补贴结算单明细 - 下部分
		guildCommission.Handle("/getInteractGameBillDetail", baseHandler.SetHandler(hanlders.GetInteractGameBillDetail))           // 语音直播补贴结算单明细 - 下部分
		guildCommission.Handle("/getInteractGameExtraBillDetail", baseHandler.SetHandler(hanlders.GetInteractGameExtraBillDetail)) // 互动游戏额外奖励结算单详情
		guildCommission.Handle("/getEsportBillDetail", baseHandler.SetHandler(hanlders.GetESportBillDetail))                       // 互动游戏额外奖励结算单详情
		guildCommission.Handle("/getFreezeScoreList", baseHandler.SetHandler(hanlders.GetFreezeScoreList))                         // 获取冻结积分列表

		// 提现
		guildCommission.Handle("/doWithdraw", baseHandler.SetHandler(hanlders.DoWithdraw))                       // 对公提现
		guildCommission.Handle("/sumAndSettlement", baseHandler.SetHandler(hanlders.SumAndSettlement))           // 汇总并结算
		guildCommission.Handle("/checkSumAndSettlement", baseHandler.SetHandler(hanlders.CheckSumAndSettlement)) // 检查汇总并结算状态
		// 结算单流转
		settleBillRouter := p.PathPrefix("/settle-bill").Subrouter()
		settleBillRouter.Handle("/getSettlementBillWaitReceipt", baseHandler.SetHandler(hanlders.GetSettlementBillWaitReceipt))
		settleBillRouter.Handle("/getSettlementBillWaitWithdraw", baseHandler.SetHandler(hanlders.GetSettlementBillWaitWithdraw))
		settleBillRouter.Handle("/uploadReceipt", baseHandler.SetHandler(hanlders.UploadReceipt))
		settleBillRouter.Handle("/getReceiptTotalAmount", baseHandler.SetHandler(hanlders.GetReceiptTotalAmountReq))
		settleBillRouter.Handle("/associateReceipts", baseHandler.SetHandler(hanlders.AssociateReceipts))
		settleBillRouter.Handle("/getReceiptList", baseHandler.SetHandler(hanlders.GetReceiptList))
		settleBillRouter.Handle("/getReceiptUrl", baseHandler.SetHandler(hanlders.GetReceiptUrl))
		settleBillRouter.Handle("/getReceiptBillList", baseHandler.SetHandler(hanlders.GetReceiptBillList))
		settleBillRouter.Handle("/getAssociatedBillItems", baseHandler.SetHandler(hanlders.GetAssociatedBillItems))
		settleBillRouter.Handle("/getWaitWithdrawMonths", baseHandler.SetHandler(hanlders.GetWaitWithdrawMonths))
		settleBillRouter.Handle("/getMonthsBySettleBillId", baseHandler.SetHandler(hanlders.GetMonthsBySettleBillId))

		// 多人互动经营数据
		multiPlayRouter := p.PathPrefix("/multi-play").Subrouter()
		multiPlayRouter.Handle("/getChannelOperationData", baseHandler.SetHandler(hanlders.GetChannelOperationData))
		multiPlayRouter.Handle("/getContractAnchorDetail", baseHandler.SetHandler(hanlders.GetContractAnchorDetail))
		multiPlayRouter.Handle("/getContractAnchorChannelStat", baseHandler.SetHandler(hanlders.GetContractAnchorChannelStat))
		multiPlayRouter.Handle("/getMultiPractitionerScheduleData", baseHandler.SetHandler(hanlders.GetMultiPractitionerScheduleData))     // 获取接档数据列表
		multiPlayRouter.Handle("/getMultiChannelEmploymentInfoList", baseHandler.SetHandler(hanlders.GetMultiChannelEmploymentInfoList))   // 获取房间任职信息列表
		multiPlayRouter.Handle("/getMultiChannelEmploymentInfo", baseHandler.SetHandler(hanlders.GetMultiChannelEmploymentInfo))           // 获取房间任职信息
		multiPlayRouter.Handle("/setMultiChannelEmploymentInfo", baseHandler.SetHandler(hanlders.SetMultiChannelEmploymentInfo))           // 设置房间任职成员
		multiPlayRouter.Handle("/setMultiChannelAdminInfo", baseHandler.SetHandler(hanlders.SetMultiChannelAdminInfo))                     // 设置房间管理
		multiPlayRouter.Handle("/getMultiChannelList", baseHandler.SetHandler(hanlders.GetMultiChannelList))                               // 获取房间列表
		multiPlayRouter.Handle("/searchMultiContractMember", baseHandler.SetHandler(hanlders.SearchMultiContractMember))                   // 搜索公会签约成员
		multiPlayRouter.Handle("/batchSetMultiChannelEmploymentInfo", baseHandler.SetHandler(hanlders.BatchSetMultiChannelEmploymentInfo)) // 批量设置房间任职成员
		multiPlayRouter.Handle("/getChannelListByAdminUid", baseHandler.SetHandler(hanlders.GetChannelListByAdminUid))

		// 公会运营能力
		guildOperationalRouter := p.PathPrefix("/guild-operational").Subrouter()
		guildOperationalRouter.Handle("/getGuildOperationalCapabilities", baseHandler.SetHandler(hanlders.GetGuildOperationalCapabilities))
		guildOperationalRouter.Handle("/getCurriculumMessageList", baseHandler.SetHandler(hanlders.GetCurriculumMessageList))
		guildOperationalRouter.Handle("/getGuildOperationalSummary", baseHandler.SetHandler(hanlders.GetGuildOperationalSummary))

		// 流量卡
		flowCardRouter := p.PathPrefix("/flow-card").Subrouter()
		flowCardRouter.Handle("/getGuildFlowCardList", baseHandler.SetHandler(hanlders.GetGuildFlowCardList))
		flowCardRouter.Handle("/batGetAnchorInfo", baseHandler.SetHandler(hanlders.BatGetAnchorInfo))
		flowCardRouter.Handle("/getGuildAnchorFlowCardList", baseHandler.SetHandler(hanlders.GetGuildAnchorFlowCardList))
		flowCardRouter.Handle("/grantAnchorFlowCardByGuild", baseHandler.SetHandler(hanlders.GrantAnchorFlowCardByGuild))
		flowCardRouter.Handle("/getFlowCardHourLimit", baseHandler.SetHandler(hanlders.GetFlowCardHourLimit))
		flowCardRouter.Handle("/useFlowCardByGuild", baseHandler.SetHandler(hanlders.UseFlowCardByGuild))

		// 签约相关
		anchorContractRouter := p.PathPrefix("/anchor-contract").Subrouter()
		anchorContractRouter.Handle("/getApplySignList", baseHandler.SetHandler(hanlders.GetApplySignList))
		anchorContractRouter.Handle("/handleApplySign", baseHandler.SetHandler(hanlders.HandleApplySign))
		anchorContractRouter.Handle("/batchAcceptApplySign", baseHandler.SetHandler(hanlders.BatchAcceptApplySign))
		anchorContractRouter.Handle("/getApplyCancelContractList", baseHandler.SetHandler(hanlders.GetApplyCancelContractList))
		anchorContractRouter.Handle("/acceptCancelContractApply", baseHandler.SetHandler(hanlders.AcceptCancelContractApply))
		anchorContractRouter.Handle("/getAnchorList", baseHandler.SetHandler(hanlders.GetAnchorList))
		anchorContractRouter.Handle("/focusAnchor", baseHandler.SetHandler(hanlders.HandleFocusAnchor))
		anchorContractRouter.Handle("/updateRemark", baseHandler.SetHandler(hanlders.UpdateRemark))
		anchorContractRouter.Handle("/renew", baseHandler.SetHandler(hanlders.HandleRenew))
		anchorContractRouter.Handle("/batchRenew", baseHandler.SetHandler(hanlders.HandleBatchRenew))
		anchorContractRouter.Handle("/rescind", baseHandler.SetHandler(hanlders.HandleRescind))
		anchorContractRouter.Handle("/batchRescind", baseHandler.SetHandler(hanlders.HandleBatchRescind))
		anchorContractRouter.Handle("/genVerifyCode", baseHandler.SetHandler(hanlders.HandleGenVerifyCode))
		anchorContractRouter.Handle("/checkVerifyCode", baseHandler.SetHandler(hanlders.HandleCheckVerifyCode))
		anchorContractRouter.Handle("/GetCancelContractTypeList", baseHandler.SetHandler(hanlders.GetCancelContractTypeList))
		anchorContractRouter.Handle("/SetGuildCancelContractType", baseHandler.SetHandler(hanlders.SetGuildCancelContractType))
		anchorContractRouter.Handle("/GetGuildSignRight", baseHandler.SetHandler(hanlders.GetGuildSignRight))
		anchorContractRouter.Handle("/UpdateGuildSignRight", baseHandler.SetHandler(hanlders.UpdateGuildSignRight))
		anchorContractRouter.Handle("/InvitePromote", baseHandler.SetHandler(hanlders.InvitePromote))

		// 会长消息相关
		imRouter := p.PathPrefix("/im").Subrouter()
		imRouter.Handle("/getFuWuHaoMsgList", baseHandler.SetHandler(hanlders.GetFuWuHaoMsgList))
		imRouter.Handle("/getFuWuHaoMsgListById", baseHandler.SetHandler(hanlders.GetFuWuHaoMsgListById))

		// 从业者分析相关
		pracAnalyRouter := p.PathPrefix("/practitioner-analysis").Subrouter()
		pracAnalyRouter.Handle("/getPractitionerMonthLyInfoList", baseHandler.SetHandler(hanlders.GetPractitionerMonthLyInfoList))
		pracAnalyRouter.Handle("/getPractitionerDailyInfoList", baseHandler.SetHandler(hanlders.GetPractitionerDailyInfoList))
		pracAnalyRouter.Handle("/getPgcMonthlyInfoList", baseHandler.SetHandler(hanlders.GetPgcMonthlyInfoList))
		pracAnalyRouter.Handle("/getPgcDailyInfoList", baseHandler.SetHandler(hanlders.GetPgcDailyInfoList))
		pracAnalyRouter.Handle("/getGuildMonthlyStatsInfoList", baseHandler.SetHandler(hanlders.GetGuildMonthlyStatsInfoList))
		pracAnalyRouter.Handle("/getAnchorPractitionerMonthLyInfoList", baseHandler.SetHandler(hanlders.GetAnchorPractitionerMonthLyInfoList))
		pracAnalyRouter.Handle("/getAnchorPractitionerWeeklyInfoList", baseHandler.SetHandler(hanlders.GetAnchorPractitionerWeeklyInfoList))
		pracAnalyRouter.Handle("/getAnchorPractitionerDailyInfoList", baseHandler.SetHandler(hanlders.GetAnchorPractitionerDailyInfoList))
		pracAnalyRouter.Handle("/getGuildAnchorPracMonthlyInfoList", baseHandler.SetHandler(hanlders.GetGuildAnchorPracMonthlyInfoList))

		// 违规记录
		violationRecordRouter := p.PathPrefix("/violation").Subrouter()
		violationRecordRouter.Handle("/getGuildViolationRecord", baseHandler.SetHandler(hanlders.GetGuildViolationRecord))

		// 违规记录
		verifyRouter := p.PathPrefix("/verify").Subrouter()
		verifyRouter.Handle("/checkIsNeedVerify", baseHandler.SetHandler(hanlders.CheckIsNeedVerify))
		verifyRouter.Handle("/sendVerifyCode", baseHandler.SetHandler(hanlders.SendVerifyCode))
		verifyRouter.Handle("/checkVerifyCode", baseHandler.SetHandler(hanlders.CheckVerifyCode))

		// 多人互动承接大厅
		hallRouter := p.PathPrefix("/hall").Subrouter()
		hallRouter.Handle("/checkPermission", baseHandler.SetHandler(hanlders.CheckPermission))
		hallRouter.Handle("/getTaskConfById", baseHandler.SetHandler(hanlders.GetTaskConfById))
		hallRouter.Handle("/getTaskConfList", baseHandler.SetHandler(hanlders.GetTaskConfList))
		hallRouter.Handle("/addTaskConf", baseHandler.SetHandler(hanlders.AddTaskConf))
		hallRouter.Handle("/delTaskConf", baseHandler.SetHandler(hanlders.DelTaskConf))
		hallRouter.Handle("/getvalidList", baseHandler.SetHandler(hanlders.GetValidList))
		hallRouter.Handle("/distributeHallTask", baseHandler.SetHandler(hanlders.DistributeHallTask))
		hallRouter.Handle("/delHallTask", baseHandler.SetHandler(hanlders.DelHallTask))
		hallRouter.Handle("/getHallTask", baseHandler.SetHandler(hanlders.GetHallTask))
		hallRouter.Handle("/getStatistics", baseHandler.SetHandler(hanlders.GetStatistics))
		hallRouter.Handle("/getStatisticsDetial", baseHandler.SetHandler(hanlders.GetStatisticsDetial))

		// 开播管理
		liveManageRouter := p.PathPrefix("/live-management").Subrouter()
		liveManageRouter.Handle("/GetGuildAnchorList", baseHandler.SetHandler(hanlders.GetGuildAnchorListHandle))
		liveManageRouter.Handle("/GetAnchorMatchList", baseHandler.SetHandler(hanlders.GetAnchorMatchListHandle))

		// 电竞陪玩
		esportRouter := p.PathPrefix("/esports").Subrouter()
		// 电竞陪玩技能管理
		esportRouter.Handle("/getGameSkillDetial", baseHandler.SetHandler(hanlders.GetGameSkillDetial))
		esportRouter.Handle("/getAuditSkill", baseHandler.SetHandler(hanlders.GetAuditSkill))
		esportRouter.Handle("/setSkillAuditType", baseHandler.SetHandler(hanlders.SetSkillAuditType))
		// 电竞陪玩业绩分析
		esportRouter.Handle("/GetEsportPractitionerDaily", baseHandler.SetHandler(hanlders.GetEsportPractitionerDaily))
		esportRouter.Handle("/GetEsportPractitionerMonthly", baseHandler.SetHandler(hanlders.GetEsportPractitionerMonthly))
		esportRouter.Handle("/GetEsportGameMonthlyStat", baseHandler.SetHandler(hanlders.GetEsportGameMonthlyStat))
		esportRouter.Handle("/GetEsportGuildMonthlyStat", baseHandler.SetHandler(hanlders.GetEsportGuildMonthlyStat))
		esportRouter.Handle("/GetEsportPractitionerData", baseHandler.SetHandler(hanlders.GetEsportPractitionerData))
		esportRouter.Handle("/GetEsportGuildData", baseHandler.SetHandler(hanlders.GetEsportGuildData))
		esportRouter.Handle("/GetEsportChannelData", baseHandler.SetHandler(hanlders.GetEsportChannelData))

		businessAnalysisRouter := p.PathPrefix("/business-analysis").Subrouter()
		businessAnalysisRouter.Handle("/GetWeekBusinessAnalysis", baseHandler.SetHandler(hanlders.GetWeekBusinessAnalysis))
		businessAnalysisRouter.Handle("/GetMonthBusinessAnalysis", baseHandler.SetHandler(hanlders.GetMonthBusinessAnalysis))
		businessAnalysisRouter.Handle("/GetRevenueBusinessAnalysis", baseHandler.SetHandler(hanlders.GetRevenueBusinessAnalysis))
		businessAnalysisRouter.Handle("/GetRecruitBusinessAnalysis", baseHandler.SetHandler(hanlders.GetRecruitBusinessAnalysis))

		// 公会主播人脸核验展示相关
		anchorFaceCheckRouter := p.PathPrefix("/anchor-face-check").Subrouter()
		anchorFaceCheckRouter.Handle("/batchGetDailyFaceCheckInfo", baseHandler.SetHandler(hanlders.BatchGetDailyFaceCheckInfo))
		anchorFaceCheckRouter.Handle("/batchGetWeeklyFaceCheckInfo", baseHandler.SetHandler(hanlders.BatchGetWeeklyFaceCheckInfo))
		anchorFaceCheckRouter.Handle("/batchGetGuildWeeklySumFaceCheckInfo", baseHandler.SetHandler(hanlders.BatchGetGuildWeeklySumFaceCheckInfo))
		anchorFaceCheckRouter.Handle("/batchGetGuildDailySumFaceCheckInfo", baseHandler.SetHandler(hanlders.BatchGetGuildDailySumFaceCheckInfo))
		anchorFaceCheckRouter.Handle("/exportFaceCheckInfo", baseHandler.SetHandler(hanlders.ExportFaceCheckInfo))

		// 婚礼房
		weddingRouter := p.PathPrefix("/wedding").Subrouter()
		weddingRouter.Handle("/getReservedWedding", baseHandler.SetHandler(hanlders.GetReservedWedding)) // 获取预约婚礼列表
		weddingRouter.Handle("/setWeddingHost", baseHandler.SetHandler(hanlders.SetWeddingHost))         // 设置婚礼主持人
		weddingRouter.Handle("/searchWeddingHost", baseHandler.SetHandler(hanlders.SearchWeddingHost))   // 搜索婚礼主持人
		weddingRouter.Handle("/cancelWedding", baseHandler.SetHandler(hanlders.CancelWedding))           // 取消婚礼

		//首页
		HomeRouter := p.PathPrefix("/home").Subrouter()
		HomeRouter.Handle("/GetBannerInfo", baseHandler.SetHandler(hanlders.GetBannerInfo))
		HomeRouter.Handle("/GetGuildMultiKeyData", baseHandler.SetHandler(hanlders.GetGuildMultiKeyData))
		HomeRouter.Handle("/GetGuildMultiTopChannel", baseHandler.SetHandler(hanlders.GetGuildMultiTopChannel))

		//多人互动公会经营分析
		multiBusinessAnalysisRouter := p.PathPrefix("/multi-business-analysis").Subrouter()
		multiBusinessAnalysisRouter.Handle("/GetGuildChannelBusinessData", baseHandler.SetHandler(hanlders.GetGuildChannelBusinessData))
		multiBusinessAnalysisRouter.Handle("/GetGuildRadarChart", baseHandler.SetHandler(hanlders.GetGuildRadarChart))
		multiBusinessAnalysisRouter.Handle("/GetGuildAbilityBusinessDiag", baseHandler.SetHandler(hanlders.GetGuildAbilityBusinessDiag))

		muxEx := &HttpRouterInterceptor{Router: r}
		go muxEx.runAdminServer()

		http.Handle("/", muxEx)
		http.ListenAndServe(models.GetModelServer().GetServiceConfig().GetAddr(), nil)

		return nil
	}
	_ = app.Run(os.Args)
}

type HttpRouterInterceptor struct {
	*mux.Router
}

func (s *HttpRouterInterceptor) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	beginTime := time.Now()
	ctx := filter.HttpWithServiceInfo(req)
	req = req.WithContext(ctx)

	defer func() {

		elapsed := time.Since(beginTime)
		elapsedMillisecond := uint32(math.Ceil(float64(elapsed / time.Millisecond)))
		if elapsedMillisecond < 1 {
			elapsedMillisecond = 1
		}

		if elapsedMillisecond >= 30 {
			log2.WarnWithCtx(ctx, "url %s took %d ms", req.URL.Path, elapsedMillisecond)
		} else {
			log2.DebugfWithCtx(ctx, "url %s took %d ms, uid %d", req.URL.Path, elapsedMillisecond)
		}

		metrics.ReportMetrics(
			req.Method,    // HTTP 方法
			req.URL.Path,  // 路径名
			http.StatusOK, // 状态码统一200
			elapsed,       // 耗时
		)
	}()

	s.Router.ServeHTTP(w, req)
}

func (s *HttpRouterInterceptor) runAdminServer() admin.Closer {
	http.HandleFunc(webmonitor.MONITOR_ROUTER, webmonitor.MonitorHandle)
	http.HandleFunc(webmonitor.WARNING_ROUTER, webmonitor.WarningHandle)
	listenPort := ":8078"
	return admin.ListenAndServe(listenPort)
}
