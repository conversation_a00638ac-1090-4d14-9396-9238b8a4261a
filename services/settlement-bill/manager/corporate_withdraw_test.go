package manager

import (
	"bou.ke/monkey"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/account"
	Exchange "golang.52tt.com/clients/exchange"
	mocksExchange "golang.52tt.com/clients/exchange/mocks"
	"golang.52tt.com/clients/guild"
	mocksUserRisk "golang.52tt.com/clients/mocks/user-risk-api"
	"golang.52tt.com/clients/realnameauth"
	userRiskApi "golang.52tt.com/clients/user-risk-api"
	commissionMocks "golang.52tt.com/pkg/commission/mocks"
	"golang.52tt.com/pkg/oa"
	mocksOA "golang.52tt.com/pkg/oa/mocks"
	"golang.52tt.com/pkg/settlement"
	"golang.52tt.com/pkg/settlement/alert"
	"golang.52tt.com/pkg/settlement/pdf"
	mocks2 "golang.52tt.com/pkg/settlement/pdf/mocks"
	"golang.52tt.com/pkg/tbean"
	mocksTBean "golang.52tt.com/pkg/tbean/mocks"
	exchange "golang.52tt.com/protocol/services/exchange"
	pb "golang.52tt.com/protocol/services/settlement-bill"
	"golang.52tt.com/services/settlement-bill/cache"
	"golang.52tt.com/services/settlement-bill/conf"
	"golang.52tt.com/services/settlement-bill/mocks"
	"golang.52tt.com/services/settlement-bill/mysql"
	"golang.52tt.com/services/settlement-bill/utils"
	"golang.org/x/net/context"
	"os"
	"reflect"
	"strconv"
	"testing"
	"time"
)

func TestManager_ConfirmWithdrawLimit(t *testing.T) {

	monkey.PatchInstanceMethod(reflect.TypeOf(&alert.Feishu{}), "SendError", func(c *alert.Feishu,
		text string) {
		return
	})

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	billTypeGiftScore := pb.SettlementBillType_GiftScore
	now := utils.NowTime()
	billId := createBillId(uid, billTypeGiftScore, now)
	settleEnd := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	settleStart := settleEnd.AddDate(0, -1, 0)
	money := uint64(99)

	_, _ = os.Create("/tmp/settle_pdf_tpl.html")

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockConfigCenter := mocks3.NewMockIConfigCenter(ctl)
	mockExchangeGuildCli := mocksExchange.NewMockIGuildClient(ctl)
	mockCommission := commissionMocks.NewMockClient(ctl)
	mockPDF := mocks2.NewMockIGenClient(ctl)
	mockOA := mocksOA.NewMockIClient(ctl)

	commissionCli := &CommissionCli{
		GiftScoreClient: mockCommission,
	}

	resp := &pb.ConfirmWithdrawResp{
		// BillId:        billId,
		// WithdrawMoney: money,
	}

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetSettlementBill(ctx, billId).Return(&mysql.SettlementBill{
			BillId:      billId,
			GuildOwner:  uid,
			SettleStart: settleStart,
			SettleEnd:   settleEnd,
			BillType:    uint8(billTypeGiftScore),
			Status:      uint8(pb.SettlementBillStatus_WaitWithdraw),
		}, nil),
		mockConfig.EXPECT().IsTest().Return(true),
		mockCache.EXPECT().GetConfirmLock(ctx, uid).Return(true, nil),
		mockCommission.EXPECT().GetBalance(ctx, uid).Return(money, uint64(0), nil),
		mockCache.EXPECT().ReleaseConfirmLock(ctx, uid).Return(nil),
	)

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
		PdfGenCli        pdf.IGenClient
		alter            alert.IFeishu
	}
	type args struct {
		ctx context.Context
		req *pb.ConfirmWithdrawReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ConfirmWithdrawResp
		wantErr bool
	}{
		{name: "ConfirmWithdraw",
			fields: fields{mysqlStore: mockStore,
				sc:               mockConfig,
				cacheClient:      mockCache,
				commissionCli:    commissionCli,
				exchangeGuildCli: mockExchangeGuildCli,
				PdfGenCli:        mockPDF,
				oaCli:            mockOA,
				alter:            &alert.Feishu{},
			},
			args: args{ctx: context.Background(), req: &pb.ConfirmWithdrawReq{
				BillId: billId,
			}},
			want:    resp,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
				PdfGenCli:        tt.fields.PdfGenCli,
				alert:            tt.fields.alter,
			}
			got, err := m.ConfirmWithdraw(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ConfirmWithdraw() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ConfirmWithdraw() got = %v, want %v", got, tt.want)
			}
		})
	}

	time.Sleep(1 * time.Second)
}

func TestManager_ConfirmWithdraw(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	billTypeGiftScore := pb.SettlementBillType_GiftScore
	scoreType := transBillType2ScoreType(billTypeGiftScore)
	now := utils.NowTime()
	billId := createBillId(uid, billTypeGiftScore, now)
	settleEnd := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	settleStart := settleEnd.AddDate(0, -1, 0)
	money := uint64(123)
	OAAccountId := "123"
	fileId := "123"

	_, _ = os.Create("/tmp/settle_pdf_tpl.html")

	mockStore := mocks.NewMockIStore(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockConfigCenter := mocks3.NewMockIConfigCenter(ctl)
	mockExchangeGuildCli := mocksExchange.NewMockIGuildClient(ctl)
	mockCommission := commissionMocks.NewMockClient(ctl)
	mockPDF := mocks2.NewMockIGenClient(ctl)
	mockOA := mocksOA.NewMockIClient(ctl)
	mockUseRiskCli := mocksUserRisk.NewMockIClient(ctl)
	mockTBeanCli := mocksTBean.NewMockBlackUserAPI(ctl)

	commissionCli := &CommissionCli{
		GiftScoreClient: mockCommission,
	}

	resp := &pb.ConfirmWithdrawResp{
		// BillId:        billId,
		// WithdrawMoney: money,
	}

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.SettlementBillCache{}), "ReleaseConfirmLock", func(m *cache.SettlementBillCache,
		ctx context.Context, uid uint32) error {
		return nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.SettlementBillCache{}), "GetConfirmLock", func(m *cache.SettlementBillCache,
		ctx context.Context, uid uint32) (bool, error) {
		return true, nil
	})

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetSettlementBill(ctx, billId).Return(&mysql.SettlementBill{
			BillId:      billId,
			GuildOwner:  uid,
			SettleStart: settleStart,
			SettleEnd:   settleEnd,
			BillType:    uint8(billTypeGiftScore),
			Status:      uint8(pb.SettlementBillStatus_WaitWithdraw),
		}, nil),
		mockConfig.EXPECT().IsTest().Return(true),
		mockCommission.EXPECT().GetBalance(ctx, uid).Return(money, uint64(0), nil),
		mockStore.EXPECT().GetGuildTaxRate(ctx, uid).Return(&mysql.TaxRate{
			GuildOwner: uid,
			TaxRate:    3,
		}, nil),
		// m.CheckTBeanRisk
		mockTBeanCli.EXPECT().CheckBlackUser(ctx, strconv.Itoa(int(uid)), tbean.BLACK_TYPE_RECHARGE).Return(false, nil),
		mockUseRiskCli.EXPECT().CheckTBeanProvider(ctx, uint32(0), uint64(uid), userRiskApi.TBeanScene_ChairmanPublicExchange, int64(money)).Return(false, nil),

		mockExchangeGuildCli.EXPECT().GetMainData(ctx, uid).Return(&exchange.GetMain{
			MasterUid:   uid,
			OaAccountId: OAAccountId,
		}, nil),
		mockConfig.EXPECT().IsTest().Return(true),
		mockOA.EXPECT().GetAccountInfo(ctx, "", OAAccountId).Return([]oa.AccountInfo{}, nil),
		mockStore.EXPECT().GetSettleDeductMoneyByBillId(ctx, uid, billId).Return([]*mysql.SettleDeductMoney{
			{GuildOwner: uid, Money: 0},
		}, nil),
		mockPDF.EXPECT().GenPdf(gomock.Any(), gomock.Any()).Return(true, nil),
		mockOA.EXPECT().UploadFileByPath(ctx, gomock.Any()).Return(&oa.FileInfo{
			FileId: fileId,
		}, nil),
		// mockOA.EXPECT().UploadFileByPath(ctx, gomock.Any()).Return(nil, errors.New("err")),
		mockStore.EXPECT().Transaction(ctx, gomock.Any()).Return(nil),
		mockExchangeGuildCli.EXPECT().GetSettlementAttributeV2(ctx, uid, uint32(scoreType), uint32(0), uint32(100), uint64(settleStart.Unix()), uint64(settleEnd.Unix()), Exchange.ReasonTypeSum).
			Return(&exchange.UserAllScoreList{
				UserAllScoreList: []*exchange.UserAllScore{
					{
						AllScore: &exchange.AllScore{
							Score: money,
						},
					},
				},
			}, nil),
		mockStore.EXPECT().SaveAnchorScoresRecords(ctx, gomock.Any()).Return(nil),
		// 返回空，跳出循环
		mockExchangeGuildCli.EXPECT().GetSettlementAttributeV2(ctx, uid, uint32(scoreType), uint32(100), uint32(100), uint64(settleStart.Unix()), uint64(settleEnd.Unix()), Exchange.ReasonTypeSum).
			Return(&exchange.UserAllScoreList{
				UserAllScoreList: []*exchange.UserAllScore{},
			}, nil),
	)

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
		PdfGenCli        pdf.IGenClient
		UseRiskCli       userRiskApi.IClient
		TBeanCli         tbean.BlackUserAPI
	}
	type args struct {
		ctx context.Context
		req *pb.ConfirmWithdrawReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ConfirmWithdrawResp
		wantErr bool
	}{
		{name: "ConfirmWithdraw",
			fields: fields{mysqlStore: mockStore,
				sc:               mockConfig,
				cacheClient:      &cache.SettlementBillCache{},
				commissionCli:    commissionCli,
				exchangeGuildCli: mockExchangeGuildCli,
				PdfGenCli:        mockPDF,
				oaCli:            mockOA,
				UseRiskCli:       mockUseRiskCli,
				TBeanCli:         mockTBeanCli,
			},
			args: args{ctx: context.Background(), req: &pb.ConfirmWithdrawReq{
				BillId: billId,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
				PdfGenCli:        tt.fields.PdfGenCli,
				UseRiskCli:       tt.fields.UseRiskCli,
				TBeanCli:         tt.fields.TBeanCli,
			}
			got, err := m.ConfirmWithdraw(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ConfirmWithdraw() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ConfirmWithdraw() got = %v, want %v", got, tt.want)
			}
		})
	}

	time.Sleep(1 * time.Second)
}

func TestManager_GetAnchorScoreWithdrawRecords(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	billType := pb.SettlementBillType_GiftScore
	now := utils.NowTime()
	billId := createWithdrawBillId(uid, billType, now)
	money := uint64(10000)
	limit := uint32(10)

	resp := &pb.GetAnchorScoreWithdrawRecordsResp{
		AnchorWithdrawList: []*pb.AnchorScoreWithdrawRecord{
			{
				AnchorUid:            uid,
				GuildOwnerWdMoney:    money,
				GuildOwnerWdMoneyCny: settlement.CentToMoney(money),
				AnchorWdMoney:        money,
				AnchorWdMoneyCny:     settlement.CentToMoney(money),
				BillId:               billId,
				SettleStart:          uint64(now.Unix()),
				SettleEnd:            uint64(now.Unix()),
				WithdrawTime:         uint64(now.Unix()),
			},
		},
	}

	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetAnchorScoresRecords(ctx, billType, uid, uint32(0), limit).
			Return([]*mysql.AnchorScoreSumResult{
				{
					AnchorWithdrawScoreRecord: mysql.AnchorWithdrawScoreRecord{
						AnchorUid:         uid,
						AnchorWdMoney:     money,
						GuildOwnerWdMoney: money,
						BillId:            billId,
						BillType:          uint8(billType),
						SettleStart:       now,
						SettleEnd:         now,
						WithdrawTime:      now,
					},
					SumEnd:    now,
					SumStart:  now,
					AnchorSum: money,
				},
			}, nil),
	)

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		exchangeCli      Exchange.IClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
		PdfGenCli        pdf.IGenClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetAnchorScoreWithdrawRecordsReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetAnchorScoreWithdrawRecordsResp
		wantErr bool
	}{
		{name: "GetAnchorScoreWithdrawRecords",
			fields: fields{mysqlStore: mockStore,
				sc:          mockConfig,
				cacheClient: mockCache,
			},
			args: args{ctx: context.Background(), req: &pb.GetAnchorScoreWithdrawRecordsReq{
				BillType:  billType,
				AnchorUid: uid,
				Offset:    0,
				Limit:     limit,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				exchangeCli:      tt.fields.exchangeCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
				PdfGenCli:        tt.fields.PdfGenCli,
			}
			got, err := m.GetAnchorScoreWithdrawRecords(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAnchorScoreWithdrawRecords() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAnchorScoreWithdrawRecords() got = %v, want %v", got, tt.want)
			}
		})
	}
}
