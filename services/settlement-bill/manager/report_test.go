package manager

import (
	"bou.ke/monkey"
	"context"
	"fmt"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/account"
	Exchange "golang.52tt.com/clients/exchange"
	mocksExchange "golang.52tt.com/clients/exchange/mocks"
	"golang.52tt.com/clients/guild"
	mocksAccount "golang.52tt.com/clients/mocks/account"
	mockGuild "golang.52tt.com/clients/mocks/guild"
	mockReal "golang.52tt.com/clients/mocks/realnameauth"
	mockTtcProxy "golang.52tt.com/clients/mocks/ttc-proxy"
	"golang.52tt.com/clients/realnameauth"
	ttc_proxy "golang.52tt.com/clients/ttc-proxy"
	commissionMocks "golang.52tt.com/pkg/commission/mocks"
	"golang.52tt.com/pkg/oa"
	mocksOA "golang.52tt.com/pkg/oa/mocks"
	"golang.52tt.com/pkg/settlement"
	mocks3 "golang.52tt.com/pkg/settlement/mocks"
	"golang.52tt.com/pkg/settlement/pdf"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	ExchangePb "golang.52tt.com/protocol/services/exchange"
	Guild "golang.52tt.com/protocol/services/guildsvr"
	realnameauthPb "golang.52tt.com/protocol/services/realNameAuthSvr"
	pb "golang.52tt.com/protocol/services/settlement-bill"
	ttc_proxy2 "golang.52tt.com/protocol/services/ttc-proxy"
	"golang.52tt.com/services/settlement-bill/cache"
	"golang.52tt.com/services/settlement-bill/conf"
	"golang.52tt.com/services/settlement-bill/mocks"
	"golang.52tt.com/services/settlement-bill/mysql"
	"golang.52tt.com/services/settlement-bill/utils"
	"os"
	"reflect"
	"testing"
	"time"
)

func TestManager_ReportConfirmDeductMoney(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	guildId := uint32(123456)
	guildName := "name"
	now := utils.NowTime()
	date := utils.GetMonthInt(now)
	money := uint64(10000)
	title := fmt.Sprintf("佣金扣款确定名单推送 - %s [测试环境]", now.Format(utils.TimeLayoutMonth))
	html := "见附件"
	attachPath := []string{
		fmt.Sprintf("%s.xlsx", "多人互动会长佣金扣款确定名单推送-"+now.Format(utils.TimeLayoutMonth)),
		fmt.Sprintf("%s.xlsx", "语音直播会长佣金扣款确定名单推送-"+now.Format(utils.TimeLayoutMonth)),
	}
	defer func() {
		for _, a := range attachPath {
			_ = os.Remove(a)
		}
	}()

	start, end := now, now

	mockStore := mocks.NewMockIStore(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockGuildCli := mockGuild.NewMockIClient(ctl)
	mockConfigCenter := mocks3.NewMockIConfigCenter(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetConfirmRecordIncome(ctx, start, end, &mysql.SettleDeductMoney{}).Return([]*mysql.SettleDeductMoney{
			{
				GuildOwner:       uid,
				SettlementDate:   date,
				SettlementStatus: mysql.WithdrawStatusFinished,
				Money:            money,
			},
		}, nil),
		mockAccountCli.EXPECT().GetUser(ctx, uid).Return(&account.User{
			Uid:            uid,
			CurrentGuildId: guildId,
		}, nil),
		mockGuildCli.EXPECT().GetGuild(ctx, guildId).Return(&Guild.GuildResp{
			GuildId: guildId,
			Name:    guildName,
		}, nil),
		mockConfig.EXPECT().IsTest().Return(true),
		mockConfigCenter.EXPECT().SendEmail(ctx, title, html, attachPath).Return(),
	)

	resp := &pb.ReportConfirmResp{}

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		req *pb.ReportConfirmReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ReportConfirmResp
		wantErr bool
	}{
		{name: "ReportConfirmDeductMoney",
			fields: fields{mysqlStore: mockStore,
				accountCli: mockAccountCli,
				guildCli:   mockGuildCli,
				sc:         mockConfig,
				cfgCenter:  mockConfigCenter,
			},
			args: args{ctx: context.Background(), req: &pb.ReportConfirmReq{
				StartTime:        uint64(start.Unix()),
				EndTime:          uint64(end.Unix()),
				ConfirmSendEmail: true,
			}},
			want: resp,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.ReportConfirmDeductMoney(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReportConfirmDeductMoney() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ReportConfirmDeductMoney() got = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func TestManager_ReportConfirmDeepCoop(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	guildId := uint32(123456)
	guildName := "name"
	now := utils.NowTime()
	date := utils.GetMonthInt(now)
	money := uint64(10000)
	title := fmt.Sprintf("深度合作收益推送 - %s [测试环境]", now.Format(utils.TimeLayoutMonth))
	html := "见附件"
	attachPath := []string{
		fmt.Sprintf("%s.xlsx", "深度合作收益推送"),
	}
	defer func() {
		for _, a := range attachPath {
			_ = os.Remove(a)
		}
	}()

	start, end := now, now

	mockStore := mocks.NewMockIStore(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockGuildCli := mockGuild.NewMockIClient(ctl)
	mockConfigCenter := mocks3.NewMockIConfigCenter(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetConfirmRecordIncome(ctx, start, end, &mysql.SettleDeepCooperation{}).Return([]*mysql.SettleDeepCooperation{
			{
				GuildOwner:       uid,
				SettlementDate:   date,
				SettlementStatus: mysql.WithdrawStatusFinished,
				SettlementMoney:  money,
			},
		}, nil),
		mockAccountCli.EXPECT().GetUser(ctx, uid).Return(&account.User{
			Uid:            uid,
			CurrentGuildId: guildId,
		}, nil),
		mockGuildCli.EXPECT().GetGuild(ctx, guildId).Return(&Guild.GuildResp{
			GuildId: guildId,
			Name:    guildName,
		}, nil),
		mockConfig.EXPECT().IsTest().Return(true),
		mockConfigCenter.EXPECT().SendEmail(ctx, title, html, attachPath).Return(),
	)

	resp := &pb.ReportConfirmResp{}

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		req *pb.ReportConfirmReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ReportConfirmResp
		wantErr bool
	}{
		{name: "ReportConfirmDeepCoop",
			fields: fields{mysqlStore: mockStore,
				accountCli: mockAccountCli,
				guildCli:   mockGuildCli,
				sc:         mockConfig,
				cfgCenter:  mockConfigCenter,
			},
			args: args{ctx: context.Background(), req: &pb.ReportConfirmReq{
				StartTime:        uint64(start.Unix()),
				EndTime:          uint64(end.Unix()),
				ConfirmSendEmail: true,
			}},
			want: resp,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.ReportConfirmDeepCoop(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReportConfirmDeepCoop() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ReportConfirmDeepCoop() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_ReportConfirmWith(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	billId := "123"
	now := utils.NowTime()
	guildId := uint32(123456)
	money := uint64(10000)
	OAAccountId := "123"
	title := fmt.Sprintf("结算报表推送 - %s [测试环境]", now.Format(utils.TimeLayoutMonth))
	html := "见附件"
	attachPath := []string{
		fmt.Sprintf("%s.xlsx", "对公礼物积分-"+now.Format(utils.TimeLayout2)),
	}
	defer func() {
		for _, a := range attachPath {
			_ = os.Remove(a)
		}
	}()

	billTypes := []pb.SettlementBillType{
		pb.SettlementBillType_GiftScore,
		// pb.SettlementBillType_AmuseCommission,
		// pb.SettlementBillType_YuyinBaseCommission,
		// pb.SettlementBillType_MonthMiddle,
		// pb.SettlementBillType_DeepCoop,
		// pb.SettlementBillType_YuyinSubsidy,
	}

	monkey.Patch(time.Now, func() time.Time {
		return now
	})

	end := now

	start, _ := getSettleStartByType(pb.SettlementBillType_GiftScore, end)

	mockStore := mocks.NewMockIStore(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockGuildCli := mockGuild.NewMockIClient(ctl)
	mockRealCli := mockReal.NewMockIClient(ctl)
	mockConfigCenter := mocks3.NewMockIConfigCenter(ctl)
	mockExchangeCli := mocksExchange.NewMockIGuildClient(ctl)
	mockCommission := commissionMocks.NewMockClient(ctl)
	mockOA := mocksOA.NewMockIClient(ctl)
	mockTtcProxyCli := mockTtcProxy.NewMockIClient(ctl)

	commissionCli := &CommissionCli{
		GiftScoreClient: mockCommission,
	}

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetCronLock(ctx, createLockLabel(billTypes, now)).Return(true, nil),
		// enter m.ReportConfirmWithByTypes()
		mockStore.EXPECT().GetSettleBillWithdrawn(ctx, pb.SettlementBillType_GiftScore, start, end).Return([]*mysql.SettlementBill{
			{
				BillId:      billId,
				GuildOwner:  uid,
				BillType:    uint8(pb.SettlementBillType_GiftScore),
				SettleStart: start,
				SettleEnd:   end,
			},
		}, nil),
		mockStore.EXPECT().GetSettleBillWithdrawnFormerly(ctx, pb.SettlementBillType_GiftScore, start, end).Return([]*mysql.SettlementBill{}, nil),
		mockStore.EXPECT().GetSettleBillWaitWithdraw(ctx, pb.SettlementBillType_GiftScore).Return([]*mysql.SettlementBill{}, nil),
		// enter m.genBillWithdrawRecord()
		// enter m.getMainDataFromCache()
		mockExchangeCli.EXPECT().GetMainData(ctx, uid).Return(&ExchangePb.GetMain{
			MasterUid:   uid,
			OaAccountId: OAAccountId,
		}, nil),
		// enter m.getUserInfoCache()
		mockAccountCli.EXPECT().GetUser(ctx, uid).Return(&account.User{
			Uid:            uid,
			CurrentGuildId: guildId,
		}, nil),
		// enter m.getRealNameFromCache()
		mockTtcProxyCli.EXPECT().GetUserRealNameAuthInfoV2(ctx, uint64(uid), false, true).Return(&ttc_proxy.GetUserRealNameAuthInfoV2Resp{
			IdcardInfo: &ttc_proxy2.AuthIdCardInfo{
				Name:        "name",
				IdentityNum: "name",
			},
		}, nil),
		mockCommission.EXPECT().GetBalance(ctx, uid).Return(money, uint64(0), nil),
		// enter m.genAnchorWithdrawRecord()
		mockExchangeCli.EXPECT().
			// 第一次循环 offset = 0
			GetSettlementAttributeV2(ctx, uid, uint32(ExchangePb.ExchangeType_PRESENT), uint32(0), uint32(100), uint64(start.Unix()), uint64(end.Unix()), Exchange.ReasonTypeSettle).
			Return(&ExchangePb.UserAllScoreList{
				UserAllScoreList: []*ExchangePb.UserAllScore{
					{
						Uid: uid,
						AllScore: &ExchangePb.AllScore{
							Score: money,
						},
					},
				},
			}, nil),
		mockAccountCli.EXPECT().BatGetUserByUid(ctx, uid).Return(map[uint32]*accountPB.UserResp{
			uid: {Uid: uid, Nickname: "", Phone: ""},
		}, nil),
		mockOA.EXPECT().GetAccountInfo(ctx, "", OAAccountId).Return([]oa.AccountInfo{}, nil),
		mockExchangeCli.EXPECT().
			// 第二次循环 offset = 100
			GetSettlementAttributeV2(ctx, uid, uint32(ExchangePb.ExchangeType_PRESENT), uint32(100), uint32(100), uint64(start.Unix()), uint64(end.Unix()), Exchange.ReasonTypeSettle).
			Return(&ExchangePb.UserAllScoreList{
				// 第二次返回空以跳出循环
				UserAllScoreList: []*ExchangePb.UserAllScore{},
			}, nil),
		mockConfig.EXPECT().IsTest().Return(true),
		mockConfigCenter.EXPECT().SendEmail(ctx, title, html, attachPath).Return(),
	)

	resp := &pb.ReportConfirmWithResp{}

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
		TtcProxyCli      ttc_proxy.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.ReportConfirmWithReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ReportConfirmWithResp
		wantErr bool
	}{
		{name: "ReportConfirmWith",
			fields: fields{mysqlStore: mockStore,
				accountCli:       mockAccountCli,
				guildCli:         mockGuildCli,
				sc:               mockConfig,
				cfgCenter:        mockConfigCenter,
				exchangeGuildCli: mockExchangeCli,
				realNameAuthCli:  mockRealCli,
				commissionCli:    commissionCli,
				oaCli:            mockOA,
				TtcProxyCli:      mockTtcProxyCli,
			},
			args: args{ctx: context.Background(), req: &pb.ReportConfirmWithReq{
				BillType: billTypes,
			}},
			want: resp,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
				TtcProxyCli:      tt.fields.TtcProxyCli,
			}
			got, err := m.ReportConfirmWith(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReportConfirmWith() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ReportConfirmWith() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_ReportConfirmWithByTypes(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx   context.Context
		types []pb.SettlementBillType
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			if err := m.ReportConfirmWithByTypes(tt.args.ctx, tt.args.types); (err != nil) != tt.wantErr {
				t.Errorf("ReportConfirmWithByTypes() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_ReportConfirmYuyinSubsidy(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	guildId := uint32(123456)
	guildName := "name"
	now := utils.NowTime()
	date := utils.GetMonthInt(now)
	money := uint64(10000)
	title := fmt.Sprintf("语音直播新公会补贴&主播补贴推送 - %s [测试环境]", now.Format(utils.TimeLayoutMonth))
	html := "见附件"
	attachPath := []string{
		fmt.Sprintf("%s.xlsx", "语音直播主播补贴推送-"+now.Format(utils.TimeLayoutMonth)),
		fmt.Sprintf("%s.xlsx", "新公会补贴金额-"+now.Format(utils.TimeLayoutMonth)),
	}
	defer func() {
		for _, a := range attachPath {
			_ = os.Remove(a)
		}
	}()

	start, end := now, now

	mockStore := mocks.NewMockIStore(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockGuildCli := mockGuild.NewMockIClient(ctl)
	mockConfigCenter := mocks3.NewMockIConfigCenter(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetConfirmRecordIncome(ctx, start, end, &mysql.SettleAnchorSubsidy{}).Return([]*mysql.SettleAnchorSubsidy{
			{
				GuildOwner:       uid,
				SettlementDate:   date,
				SettlementStatus: mysql.WithdrawStatusFinished,
				SubsidySum:       money,
			},
		}, nil),
		mockStore.EXPECT().GetConfirmRecordIncome(ctx, start, end, &mysql.SettleNewGuildSubsidy{}).Return([]*mysql.SettleNewGuildSubsidy{
			{
				GuildOwner:       uid,
				SettlementDate:   date,
				SettlementStatus: mysql.WithdrawStatusFinished,
				SubsidyMoney:     money,
			},
		}, nil),
		// enter m.getGuildName()
		mockAccountCli.EXPECT().GetUser(ctx, uid).Return(&account.User{
			Uid:            uid,
			CurrentGuildId: guildId,
		}, nil),
		mockGuildCli.EXPECT().GetGuild(ctx, guildId).Return(&Guild.GuildResp{
			GuildId: guildId,
			Name:    guildName,
		}, nil),
		mockAccountCli.EXPECT().GetUser(ctx, uid).Return(&account.User{
			Uid:            uid,
			CurrentGuildId: guildId,
		}, nil),
		mockGuildCli.EXPECT().GetGuild(ctx, guildId).Return(&Guild.GuildResp{
			GuildId: guildId,
			Name:    guildName,
		}, nil),
		mockConfig.EXPECT().IsTest().Return(true),
		mockConfigCenter.EXPECT().SendEmail(ctx, title, html, attachPath).Return(),
	)

	resp := &pb.ReportConfirmResp{}

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		req *pb.ReportConfirmReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ReportConfirmResp
		wantErr bool
	}{
		{name: "ReportConfirmYuyinSubsidy",
			fields: fields{mysqlStore: mockStore,
				accountCli: mockAccountCli,
				guildCli:   mockGuildCli,
				sc:         mockConfig,
				cfgCenter:  mockConfigCenter,
			},
			args: args{ctx: context.Background(), req: &pb.ReportConfirmReq{
				StartTime:        uint64(start.Unix()),
				EndTime:          uint64(end.Unix()),
				ConfirmSendEmail: true,
			}},
			want: resp,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.ReportConfirmYuyinSubsidy(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReportConfirmYuyinSubsidy() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ReportConfirmYuyinSubsidy() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_SendEmail(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx        context.Context
		title      string
		html       string
		attachPath []string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			m.SendEmail(tt.args.ctx, tt.args.title, tt.args.html, tt.args.attachPath)
		})
	}
}

func TestManager_createEmailTitle(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		title string
		t     time.Time
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			if got := m.createEmailTitle(tt.args.title, tt.args.t); got != tt.want {
				t.Errorf("createEmailTitle() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_genAnchorWithdrawRecord(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		b   *mysql.SettlementBill
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   [][]string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			if got := m.genAnchorWithdrawRecord(tt.args.ctx, tt.args.b); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("genAnchorWithdrawRecord() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_genBillWithdrawRecord(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		b   *mysql.SettlementBill
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			if got := m.genBillWithdrawRecord(tt.args.ctx, tt.args.b); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("genBillWithdrawRecord() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_getGuildName(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx     context.Context
		guildId uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			if got := m.getGuildName(tt.args.ctx, tt.args.guildId); got != tt.want {
				t.Errorf("getGuildName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_getMainDataFromCache(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   ExchangePb.GetMain
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			if got := m.getMainDataFromCache(tt.args.ctx, tt.args.uid); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getMainDataFromCache() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_getOAAccountInfoFromCache(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx       context.Context
		accountID string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   oa.AccountInfo
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			if got := m.getOAAccountInfoFromCache(tt.args.ctx, tt.args.accountID); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getOAAccountInfoFromCache() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_getRealNameFromCache(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   realnameauthPb.AuthIdCardInfo
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			if got := m.getRealNameFromCache(tt.args.ctx, tt.args.uid); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getRealNameFromCache() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_getUserInfoCache(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   account.User
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			if got := m.getUserInfoCache(tt.args.ctx, tt.args.uid); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getUserInfoCache() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_UpdateBillLastBalance(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	billType := pb.SettlementBillType_MaskPKScore
	balance := uint64(10000)
	uid := uint32(123456)
	billTypes := []pb.SettlementBillType{
		billType,
	}
	billId := "bill_id"
	settleBills := []*mysql.SettlementBill{
		{
			GuildOwner: uid,
			BillId:     billId,
		},
	}

	mockStore := mocks.NewMockIStore(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockGuildCli := mockGuild.NewMockIClient(ctl)
	mockConfigCenter := mocks3.NewMockIConfigCenter(ctl)
	mockCommission := commissionMocks.NewMockClient(ctl)

	commissionCli := &CommissionCli{
		MaskScoreClient: mockCommission,
	}

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetWaitWithdrawSettlementBillByType(ctx, billType).Return(settleBills, nil),
		mockCommission.EXPECT().GetBalance(ctx, uid).Return(balance, uint64(0), nil),
		mockStore.EXPECT().UpdateSettlementBillLastBalance(ctx, billId, balance).Return(nil),
	)

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		exchangeCli      Exchange.IClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
		PdfGenCli        pdf.IGenClient
	}
	type args struct {
		ctx       context.Context
		billTypes []pb.SettlementBillType
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{name: "UpdateBillLastBalance",
			fields: fields{mysqlStore: mockStore,
				accountCli:    mockAccountCli,
				guildCli:      mockGuildCli,
				sc:            mockConfig,
				cfgCenter:     mockConfigCenter,
				commissionCli: commissionCli,
			},
			args: args{ctx: context.Background(), billTypes: billTypes},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				exchangeCli:      tt.fields.exchangeCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
				PdfGenCli:        tt.fields.PdfGenCli,
			}
			m.UpdateBillLastBalance(tt.args.ctx, tt.args.billTypes)
		})
	}
}
