package mysql

import (
	"encoding/json"
	"strings"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/oa"
	pb "golang.52tt.com/protocol/services/settlement-bill"
	"golang.52tt.com/services/settlement-bill/utils"
)

const (
	IncomeSettleStatusWaiting  = 1 // 未结算
	IncomeSettleStatusFinished = 2 // 已结算

	WithdrawStatusWaiting  = 1 // 未提现
	WithdrawStatusFinished = 2 // 已提现
)

// 暂解决重复字符串异味问题
const (
	WhereStatusIn   = "status in (?)"
	OrderCreateTime = "create_time desc"
	WhereGuildOwner = "guild_owner = ?"
)

// SettlementBill 结算单
type SettlementBill struct {
	BillId              string        `gorm:"primary_key" json:"bill_id"`              // 结算单ID
	GuildOwner          uint32        `json:"guild_owner"`                             // 对公会长uid
	TaxRate             uint32        `json:"tax_rate"`                                // 税点快照
	BillType            uint8         `json:"bill_type"`                               // 结算单类型
	BillData            string        `json:"bill_data"`                               // 结算数据json
	IncomeSum           uint64        `json:"income_sum"`                              // 总金额
	IncomePay           uint64        `json:"income_pay"`                              // 应付金额
	LastBalance         uint64        `json:"last_balance"`                            // 上期余额
	TaxCompensationRate float64       `json:"tax_compensation_rate"`                   // 税费补偿率
	CompensationPoint   float64       `json:"compensation_point"`                      // 补点比例
	Status              uint8         `json:"status"`                                  // 结算状态
	PdfId               string        `json:"pdf_id"`                                  // pdf id
	SettleDate          time.Time     `json:"settle_date"`                             // 对账日期
	SettleStart         time.Time     `json:"settle_start"`                            // 结算周期起始
	SettleEnd           time.Time     `json:"settle_end"`                              // 结算周期结束
	WithdrawTime        time.Time     `json:"withdraw_time"`                           // 提现时间
	FinishedTime        time.Time     `json:"finished_time"`                           // 完成时间
	CreateTime          time.Time     `gorm:"default:current_time" json:"create_time"` // 创建时间
	UpdateTime          time.Time     `gorm:"default:current_time" json:"update_time"` // 更新时间
	RiskStatus          pb.RiskStatus `json:"risk_status"`                             // 风控状态
}

func (m *SettlementBill) TableName() string {
	return "settlement_bill"
}

func (m *SettlementBill) NeedWithdraw() bool {
	return !m.IsRecordType()
}

// IsRecordType 录入类型
func (m *SettlementBill) IsRecordType() bool {
	t := pb.SettlementBillType(m.BillType)
	return utils.SliceSettleTypeExist(t, []pb.SettlementBillType{
		pb.SettlementBillType_DeepCoop, pb.SettlementBillType_YuyinSubsidy,
	})
}

// IsScoreType 积分类型
func (m *SettlementBill) IsScoreType() bool {
	t := pb.SettlementBillType(m.BillType)
	return utils.SliceSettleTypeExist(t, []pb.SettlementBillType{
		pb.SettlementBillType_GiftScore,
		pb.SettlementBillType_AwardScore,
		pb.SettlementBillType_MaskPKScore,
		pb.SettlementBillType_KnightScore,
		pb.SettlementBillType_ESportScore,
	})
}

// IsCommissionType 佣金类型
func (m *SettlementBill) IsCommissionType() bool {
	t := pb.SettlementBillType(m.BillType)
	return utils.SliceSettleTypeExist(t, []pb.SettlementBillType{
		pb.SettlementBillType_AmuseCommission,
		pb.SettlementBillType_YuyinBaseCommission,
		pb.SettlementBillType_MonthMiddle,
		pb.SettlementBillType_AmuseExtra,
		pb.SettlementBillType_InteractGameCommission,
		pb.SettlementBillType_InteractGameExtraCommission,
		pb.SettlementBillType_ESportCommission,
	})
}

func (m *SettlementBill) GetDeductMoney() uint64 {
	if m == nil {
		return 0
	}
	t := pb.SettlementBillType(m.BillType)
	if !utils.SliceSettleTypeExist(t, []pb.SettlementBillType{
		pb.SettlementBillType_AmuseCommission, pb.SettlementBillType_YuyinBaseCommission}) {
		return 0
	}
	d := struct {
		DeductMoney uint64 `json:"deduct_money"`
	}{}
	err := json.Unmarshal([]byte(m.BillData), &d)
	if err != nil {
		return 0
	}
	return d.DeductMoney
}

func (m *SettlementBill) GetPrepaidMoney() uint64 {
	if m == nil {
		return 0
	}
	d := struct {
		PrepaidMoney uint64 `json:"prepaid_money"`
	}{}
	err := json.Unmarshal([]byte(m.BillData), &d)
	if err != nil {
		return 0
	}
	return d.PrepaidMoney
}

func (m *SettlementBill) CreateGeneralBillData(money, deduct, prepaid uint64) {
	jsb, _ := json.Marshal(pb.GeneralBill{
		Money: money, DeductMoney: deduct, PrepaidMoney: prepaid,
	})
	m.BillData = string(jsb)
}

func (m *SettlementBill) DecodeData(data interface{}) (interface{}, error) {
	if m.BillData != "" {
		err := json.Unmarshal([]byte(m.BillData), data)
		if err != nil {
			log.Errorf("decodeData json decode err: %v", err)
			return nil, err
		}
	}
	return data, nil
}

// SettleReceiptsBill 结算发票单表
type SettleReceiptsBill struct {
	BillId                    string               `gorm:"primary_key" json:" - "`                  // 结算发票单ID
	GuildOwner                uint32               `json:"guild_owner"`                             // 会长id
	LastOaStatus              pb.OAAuditBillStatus `json:"last_oa_status"`                          // OA状态
	RefusedMsg                string               `json:"refused_msg"`                             // 驳回信息
	RetryCount                uint32               `json:"retry_count"`                             // 重新发起次数
	CreateTime                time.Time            `gorm:"default:current_time" json:"create_time"` // 创建时间
	UpdateTime                time.Time            `gorm:"default:current_time" json:"update_time"` // 更新时间
	FinishedTime              time.Time            `json:"finished_time"`                           // 完成时间
	AssociatedReceiptIds      string               `json:"associated_receipt_ids"`                  // 关联发票ID，逗号分隔
	AssociatedSettleIds       string               `json:"associated_settle_ids"`                   // 关联结算单ID，逗号分隔
	AssociatedExpressOrderIds string               `json:"associated_express_order_ids"`            // 关联运单ID，逗号分隔
	OAInstId                  string               `json:"oa_inst_id"`                              // 灵犀流程实例ID
	OATaskId                  string               `json:"oa_task_id"`                              // 灵犀流程实例ID
	OASeqNo                   string               `json:"oa_seq_no"`                               // 灵犀请求ID
}

func (m *SettleReceiptsBill) TableName() string {
	return "settle_receipts_bill"
}

func (m *SettleReceiptsBill) GetAssociatedSettleIds() []string {
	if m == nil || m.AssociatedSettleIds == "" {
		return []string{}
	}
	return strings.Split(m.AssociatedSettleIds, ",")
}

// TaxRate 会长税率表
type TaxRate struct {
	GuildOwner uint32    `gorm:"primary_key" json:"guild_owner"`          // 会长id
	StartDate  uint32    `gorm:"primary_key" json:"start_date"`           // 生效时间 月 202201
	TaxRate    uint32    `json:"tax_rate"`                                // 税点
	CreateTime time.Time `gorm:"default:current_time" json:"create_time"` // 创建时间
	UpdateTime time.Time `gorm:"default:current_time" json:"update_time"` // 更新时间
	Operator   string    `json:"operator"`                                // 录入人
}

func (m *TaxRate) TableName() string {
	return "tax_rate"
}

// TaxRateRecord 会长税率上传记录表
type TaxRateRecord struct {
	ID         uint32    `gorm:"primary_key" json:" - "`                  // ID
	Operator   string    `json:"operator"`                                // 录入人
	CreateTime time.Time `gorm:"default:current_time" json:"create_time"` // 创建时间
	FileMD5    string    `json:"file_md5"`                                // 文件md5
	FileName   string    `json:"file_name"`                               // 文件名
}

func (m *TaxRateRecord) TableName() string {
	return "tax_rate_record"
}

// ExtraIncomeRecord 额外收益上传记录
type ExtraIncomeRecord struct {
	ID             uint32    `gorm:"primary_key" json:"id"`
	SettlementDate uint32    `json:"settlement_date"`                         // 结算月份 202201
	Operator       string    `json:"operator"`                                // 录入人
	IncomeType     uint8     `json:"income_type"`                             // 权益类型
	FileName       string    `json:"file_name"`                               // 文件名
	FileMD5        string    `json:"file_md5"`                                // 文件md5
	FileUrl        string    `json:"file_url"`                                // 文件url
	CreateTime     time.Time `gorm:"default:current_time" json:"create_time"` // 创建时间
}

func (m *ExtraIncomeRecord) TableName() string {
	return "extra_income_record"
}

type ISettlementRecordIncome interface {
	TableName() string
}

// SettleNewGuildSubsidy 新公会补贴收益表
type SettleNewGuildSubsidy struct {
	GuildOwner       uint32    `gorm:"primary_key" json:"guild_owner"`          // 会长id
	SettlementDate   uint32    `gorm:"primary_key" json:"settlement_date"`      // 结算月份 202201
	SettlementStatus uint8     `json:"settlement_status"`                       // 结算状态 1 未结算 2 已结算
	SettlementBillID string    `json:"settlement_bill_id"`                      // 归属结算单ID
	WithdrawStatus   uint8     `json:"withdraw_status"`                         // 提现状态 1 未提现 2 已提现
	GuildName        string    `json:"guild_name"`                              // 公会名称
	JoinDate         string    `json:"join_date"`                               // 入驻时间
	FlowThisMonth    uint64    `json:"flow_this_month"`                         // 当月流水
	SubsidyDate      string    `json:"subsidy_date"`                            // 入驻时间
	SubsidyMoney     uint64    `json:"subsidy_money"`                           // 补贴金额
	RecordId         uint32    `json:"record_id"`                               // 上传记录ID
	CreateTime       time.Time `gorm:"default:current_time" json:"create_time"` // 创建时间
	UpdateTime       time.Time `gorm:"default:current_time" json:"update_time"` // 更新时间
	ConfirmTime      time.Time `json:"confirm_time"`                            // 确认时间
	Operator         string    `json:"operator"`                                // 录入人
}

func (m *SettleNewGuildSubsidy) TableName() string {
	return "settle_new_guild_subsidy"
}

// SettleDeepCooperationChannel 深度合作房间收益表
type SettleDeepCooperationChannel struct {
	ID              uint32 `gorm:"primary_key" json:"id"`
	GuildOwner      uint32 `json:"guild_owner"`      // 会长id
	SettlementDate  uint32 `json:"settlement_date"`  // 结算月份 202201
	FlowThisMonth   uint64 `json:"flow_this_month"`  // 本月房间流水
	FlowLastMonth   uint64 `json:"flow_last_month"`  // 上月房间流水
	GrowRate        int32  `json:"grow_rate"`        // 房间流水增长率
	ChannelName     string `json:"channel_name"`     // 房间名
	GuildName       string `json:"guild_name"`       // 公会名
	GuildDisplayId  uint32 `json:"guild_display_id"` // 公会靓号
	ChannelType     string `json:"channel_type"`     // 房间类型
	SettlementRate  uint32 `json:"settlement_rate"`  // 结算比例
	SettlementMoney uint64 `json:"settlement_money"` // 结算金额
}

func (m *SettleDeepCooperationChannel) TableName() string {
	return "settle_deep_cooperation_channel"
}

// SettleDeepCooperation 深度合作收益表
type SettleDeepCooperation struct {
	GuildOwner            uint32    `gorm:"primary_key" json:"guild_owner"`          // 会长id
	SettlementDate        uint32    `gorm:"primary_key" json:"settlement_date"`      // 结算月份 202201
	SettlementStatus      uint8     `json:"settlement_status"`                       // 结算状态 1 未结算 2 已结算
	SettlementBillID      string    `json:"settlement_bill_id"`                      // 归属结算单ID
	WithdrawStatus        uint8     `json:"withdraw_status"`                         // 提现状态 1 未提现 2 已提现
	TotalFlowThisMonth    uint64    `json:"total_flow_this_month"`                   // 本月房间流水
	TotalFlowLastMonth    uint64    `json:"total_flow_last_month"`                   // 上月房间流水
	GrowRate              int32     `json:"grow_rate"`                               // 房间流水增长率
	SettlementMoney       uint64    `json:"settlement_money"`                        // 总结算金额
	PrepaidMoney          uint64    `json:"prepaid_money"`                           // 预付金额
	ActualSettlementMoney uint64    `json:"actual_settlement_money"`                 // 实际结算金额
	Remark                string    `json:"remark"`                                  // 备注
	CreateTime            time.Time `gorm:"default:current_time" json:"create_time"` // 创建时间
	UpdateTime            time.Time `gorm:"default:current_time" json:"update_time"` // 更新时间
	ConfirmTime           time.Time `json:"confirm_time"`                            // 确认时间
	Operator              string    `json:"operator"`                                // 录入人
}

func (m *SettleDeepCooperation) TableName() string {
	return "settle_deep_cooperation"
}

type SettleDeductMoney struct {
	GuildOwner       uint32    `gorm:"primary_key;column:guild_owner" json:"guild_owner"`          // 会长id
	SettlementDate   uint32    `gorm:"primary_key;column:settlement_date" json:"settlement_date"`  // 结算月份 202201
	SettlementStatus uint8     `gorm:"column:settlement_status" json:"settlement_status"`          // 结算状态 1 未结算 2 已结算
	BillType         uint8     `gorm:"primary_key;column:bill_type" json:"bill_type"`              // 结算单类型
	BillID           string    `gorm:"column:bill_id" json:"bill_id"`                              // 归属结算单ID
	Money            uint64    `gorm:"column:money" json:"money"`                                  // 扣款金额
	Remark           string    `gorm:"column:remark" json:"remark"`                                // 备注
	CreateTime       time.Time `gorm:"column:create_time;default:current_time" json:"create_time"` // 创建时间
	UpdateTime       time.Time `gorm:"column:update_time;default:current_time" json:"update_time"` // 更新时间
	Operator         string    `gorm:"column:operator" json:"operator"`                            // 操作人
	WithdrawStatus   uint8     `gorm:"column:withdraw_status" json:"withdraw_status"`              // 提现状态 1 未提现 2 已提现
	ConfirmTime      time.Time `gorm:"column:confirm_time" json:"confirm_time"`                    // 确认提现时间
}

func (m *SettleDeductMoney) TableName() string {
	return "settle_deduct_money"
}

// SettleAnchorSubsidy 语音直播主播补贴表
type SettleAnchorSubsidy struct {
	GuildOwner       uint32    `gorm:"primary_key" json:"guild_owner"`          // 会长id
	SettlementDate   uint32    `gorm:"primary_key" json:"settlement_date"`      // 结算月份 202201
	SettlementStatus uint8     `json:"settlement_status"`                       // 结算状态 1 未结算 2 已结算
	SettlementBillID string    `json:"settlement_bill_id"`                      // 归属结算单ID
	WithdrawStatus   uint8     `json:"withdraw_status"`                         // 提现状态 1 未提现 2 已提现
	SubsidySum       uint64    `json:"subsidy_sum"`                             // 总补贴金额
	CreateTime       time.Time `gorm:"default:current_time" json:"create_time"` // 创建时间
	UpdateTime       time.Time `gorm:"default:current_time" json:"update_time"` // 更新时间
	ConfirmTime      time.Time `json:"confirm_time"`                            // 确认时间
	Operator         string    `json:"operator"`                                // 操作人
}

func (m *SettleAnchorSubsidy) TableName() string {
	return "settle_anchor_subsidy"
}

// SettleAnchorSubsidyDetail 语音直播主播补贴详情表
type SettleAnchorSubsidyDetail struct {
	ID             uint32 `gorm:"primary_key" json:"id"`
	GuildOwner     uint32 `json:"guild_owner"`             // 会长id
	SettlementDate uint32 `json:"settlement_date"`         // 结算月份 202201
	TTId           uint32 `gorm:"column:ttid" json:"ttid"` // TTid
	Nickname       string `json:"nickname"`                // TT昵称
	Tag            string `json:"tag"`                     // 品类
	AnchorFlag     string `json:"anchor_flag"`             // 主播类型
	GuildName      string `json:"guild_name"`              // 所属公会
	PlanDate       string `json:"plan_date"`               // 计划时间
	GiftFlow       uint64 `json:"gift_flow"`               // 主播个人收礼流水
	ValidLiveDays  uint32 `json:"valid_live_days"`         // 主播开播有效天
	SubsidyMoney   uint64 `json:"subsidy_money"`           // 补贴金额
}

func (m *SettleAnchorSubsidyDetail) TableName() string {
	return "settle_anchor_subsidy_detail"
}

type SettleAmuseExtraPrepaid struct {
	GuildOwner       uint32    `gorm:"column:guild_owner;primary_key" json:"guild_owner"`          //  会长id
	SettlementDate   uint32    `gorm:"column:settlement_date;primary_key" json:"settlement_date"`  //  结算月份 202201
	SettlementStatus uint8     `gorm:"column:settlement_status" json:"settlement_status"`          // 结算状态 1 未结算 2 已结算
	SettlementBillID string    `gorm:"column:settlement_bill_id" json:"settlement_bill_id"`        // 归属结算单ID
	PrepaidMoney     uint64    `gorm:"column:prepaid_money" json:"prepaid_money"`                  //  预付金额
	Remark           string    `gorm:"column:remark" json:"remark"`                                //  备注' collate 'utf8mb4_general_ci
	CreateTime       time.Time `gorm:"column:create_time;default:current_time" json:"create_time"` //  创建时间
	UpdateTime       time.Time `gorm:"column:update_time;default:current_time" json:"update_time"` //  更新时间
	Operator         string    `gorm:"column:operator" json:"operator"`                            //  操作人' collate 'utf8mb4_general_ci
	WithdrawStatus   uint8     `gorm:"column:withdraw_status" json:"withdraw_status"`              // 提现状态 1 未提现 2 已提现
	ConfirmTime      time.Time `gorm:"column:confirm_time" json:"confirm_time"`                    // 确认提现时间
}

func (SettleAmuseExtraPrepaid) TableName() string {
	return "settle_amuse_extra_prepaid"
}

type SettleReceipt struct {
	ReceiptId   string    `gorm:"primary_key" json:"receipt_id"`           // 发票ID
	FileName    string    `json:"file_name"`                               // 文件名
	CreateTime  time.Time `gorm:"default:current_time" json:"create_time"` // 创建时间
	GuildOwner  uint32    `json:"guild_owner"`                             // 所属公会
	Size        uint32    `json:"size"`                                    // 文件大小
	ShowSize    string    `json:"show_size"`                               // 可视化文件大小
	FileClass   string    `json:"file_class"`                              // 文件分类
	ReceiptSign string    `json:"receipt_sign"`                            // 发票标识
}

func (m *SettleReceipt) TableName() string {
	return "settle_receipt"
}
func (m *SettleReceipt) ToFileInfo() *oa.FileInfo {
	return &oa.FileInfo{
		FileName:  m.FileName,
		Timestamp: int(m.CreateTime.Unix()),
		Size:      int(m.Size),
		FileId:    m.ReceiptId,
		ShowSize:  m.ShowSize,
		FileClass: m.FileClass,
	}
}

type SettleReceiptInfo struct {
	ReceiptSign    string           `gorm:"primary_key" json:"receipt_sign"`         // 唯一标识 代码+号码
	ReceiptCode    string           `json:"receipt_code"`                            // 发票代码
	ReceiptNo      string           `json:"receipt_no"`                              // 发票号码
	TaxRate        string           `json:"tax_rate"`                                // 发票税率
	TaxAmount      uint64           `json:"tax_amount"`                              // 发票税额
	Amount         uint64           `json:"amount"`                                  // 发票金额
	ExTaxAmount    uint64           `json:"ex_tax_amount"`                           // 发票不含税金额
	VCode          string           `json:"v_code"`                                  // 校验码
	Content        string           `json:"content"`                                 // 内容 项目名称
	SellerName     string           `json:"seller_name"`                             // 销售方名称
	SellerTaxNo    string           `json:"seller_tax_no"`                           // 销售方纳税人识别号
	PurchaserName  string           `json:"purchaser_name"`                          // 购买方名称
	PurchaserTaxNo string           `json:"purchaser_tax_no"`                        // 购买方纳税人识别号
	VerifyResult   string           `json:"verify_result"`                           // 校验结果
	Reason         string           `json:"reason"`                                  // 失败原因
	VerifyStatus   uint8            `json:"verify_status"`                           // 校验状态 1 通过
	AuditStatus    pb.ReceiptStatus `json:"audit_status"`                            // 审核状态 0 待审核 1 已提交 已提交审核的发票无法再提交
	CreateTime     time.Time        `gorm:"default:current_time" json:"create_time"` //  创建时间
	UpdateTime     time.Time        `gorm:"default:current_time" json:"update_time"` //  更新时间
}

func (m *SettleReceiptInfo) TableName() string {
	return "settle_receipt_info"
}

type SettlementBillFile struct {
	FileId     string    `gorm:"primary_key" json:"file_id"`
	FileName   string    `json:"file_name"`
	CreateTime time.Time `gorm:"default:current_time" json:"create_time"`
	GuildOwner uint32    `json:"guild_owner"`
	Size       uint32    `json:"size"`
	ShowSize   string    `json:"show_size"`
	FileClass  string    `json:"file_class"`
}

func (m *SettlementBillFile) TableName() string {
	return "settlement_bill_file"
}
func (m *SettlementBillFile) ToFileInfo() *oa.FileInfo {
	return &oa.FileInfo{
		FileName:  m.FileName,
		Timestamp: int(m.CreateTime.Unix() * 1000),
		Size:      int(m.Size),
		FileId:    m.FileId,
		ShowSize:  m.ShowSize,
		FileClass: m.FileClass,
	}
}

type SettleLock struct {
	TaskSign string `gorm:"primary_key" json:"task_sign"`
}

func (m *SettleLock) TableName() string {
	return "settle_lock"
}

type SettlePrivateWithdrawBill struct {
	BillId       string        `gorm:"primary_key" json:"column:bill_id"` //  提现单号
	GuildOwner   uint32        `json:"column:guild_owner"`                //  会长uid
	Status       uint8         `json:"column:status"`                     //  提现状态
	FinishedTime time.Time     `json:"column:finished_time"`              //  完成时间
	CreateTime   time.Time     `json:"column:create_time"`                //  创建时间
	UpdateTime   time.Time     `json:"column:update_time"`                //  更新时间
	BillType     uint8         `json:"column:bill_type"`                  //  结算单类型
	IncomeSum    uint64        `json:"column:income_sum"`                 //  总金额
	RiskStatus   pb.RiskStatus `json:"risk_status"`                       //  风控状态
}

func (SettlePrivateWithdrawBill) TableName() string {
	return "settle_private_withdraw_bill"
}

// SettleMonthScore  对公积分月余额
type SettleMonthScore struct {
	Uid                int64     `gorm:"primary_key;column:uid" json:"uid"`
	Date               int64     `gorm:"primary_key;column:date" json:"date"`
	ThisMonthBalance   int64     `gorm:"column:this_month_balance" json:"this_month_balance"`        //  本月产生可提现收益金额
	LastMonthBalance   int64     `gorm:"column:last_month_balance" json:"last_month_balance"`        //  上月剩余可提现收益金额
	ThisMonthWithdrawn int64     `gorm:"column:this_month_withdrawn" json:"this_month_withdrawn"`    //  本月已提现收益金额
	CreateTime         time.Time `gorm:"default:current_time;column:create_time" json:"create_time"` //  创建时间
	UpdateTime         time.Time `gorm:"default:current_time;column:update_time" json:"update_time"` //  更新时间
}

func (SettleMonthScore) TableName() string {
	return "settle_month_score"
}

// AnchorWithdrawScoreRecord  主播会长提现记录
type AnchorWithdrawScoreRecord struct {
	ID                uint32    `gorm:"primary_key;column:id" json:"id"`
	AnchorUid         uint32    `gorm:"column:anchor_uid" json:"anchor_uid"`                     //  主播uid
	GuildOwner        uint32    `gorm:"column:guild_owner" json:"guild_owner"`                   //  对公会长uid
	BillType          uint8     `gorm:"column:bill_type" json:"bill_type"`                       //  结算单类型
	BillId            string    `gorm:"column:bill_id" json:"bill_id"`                           //  结算单id
	GuildOwnerWdMoney uint64    `gorm:"column:guild_owner_wd_money" json:"guild_owner_wd_money"` //  会长提现金额
	AnchorWdMoney     uint64    `gorm:"column:anchor_wd_money" json:"anchor_wd_money"`           //  主播提现金额
	SettleStart       time.Time `gorm:"column:settle_start" json:"settle_start"`                 //  起始时间
	SettleEnd         time.Time `gorm:"column:settle_end" json:"settle_end"`                     //  结束时间
	WithdrawTime      time.Time `gorm:"column:withdraw_time" json:"withdraw_time"`               //  发起提现时间
	SumTime           time.Time `gorm:"column:sum_time" json:"sum_time"`
	CreateTime        time.Time `gorm:"default:current_time;column:create_time" json:"create_time"` //  创建时间
	UpdateTime        time.Time `gorm:"default:current_time;column:update_time" json:"update_time"` //  更新时间
}

func (AnchorWithdrawScoreRecord) TableName() string {
	return "anchor_withdraw_score_record"
}

type AnchorScoreSumResult struct {
	AnchorWithdrawScoreRecord
	SumStart  time.Time `json:"sum_start"`  // 汇总最小时间
	SumEnd    time.Time `json:"sum_end"`    // 汇总最大时间
	AnchorSum uint64    `json:"anchor_sum"` // 主播汇总金额
}
