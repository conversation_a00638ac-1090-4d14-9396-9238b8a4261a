package main

import (
	"context"
	"fmt"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/services/settlement-bill/server"
	"os"

	"golang.52tt.com/pkg/config"
	grpcEx "golang.52tt.com/pkg/foundation/grpc/server"
	pb "golang.52tt.com/protocol/services/settlement-bill"
	"google.golang.org/grpc"

	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server" // 兼容tyr公共库
)

func main() {

	// 检查依赖文件，避免提现时生成PDF错误
	CheckDependence()

	flag := grpcEx.ParseServerFlags(os.Args)
	fmt.Println(os.Args[0])

	var (
		svr *server.SettlementBillServer
		err error
	)
	initializer := func(ctx context.Context, s *grpc.Server, sc *config.ServerConfig) error {
		svr, err = server.NewSettlementBillServer(ctx, sc.Configer)
		if err != nil {
			return err
		}

		pb.RegisterSettlementBillServer(s, svr)
		return nil
	}

	var unaryInt grpc.UnaryServerInterceptor
	unaryInt = grpc_middleware.ChainUnaryServer(
		grpcEx.StatusCodeUnaryInterceptor,
		grpcEx.RecoverPanicInterceptorBussniss(true),
	)
	closer := func(ctx context.Context, s *grpc.Server) error {
		if svr != nil {
			svr.ShutDown()
		}
		return nil
	}

	s := grpcEx.NewServer(
		flag,
		grpcEx.WithGRPCServerOptions(grpc.UnaryInterceptor(unaryInt)),
		grpcEx.WithGRPCServerInitializer(initializer),
		grpcEx.WithGRPCServerCloser(closer),
		grpcEx.WithDefaultConfig("settlement-bill.json", grpcEx.AdapterJSON),
	)

	s.Serve()

}

func CheckDependence() {
	// 检查文件存在
	dependFiles := []string{
		"/usr/share/fonts/simsun.ttc",
		"/tmp/settle_pdf_tpl.html",
		"/tmp/settle_pdf_tpl_prepaid.html",
	}
	fileExist := func(filename string) bool {
		info, err := os.Stat(filename)
		if os.IsNotExist(err) {
			return false
		}
		return !info.IsDir()
	}
	for _, file := range dependFiles {
		if !fileExist(file) {
			panic("file not exist: " + file)
		}
	}
}
