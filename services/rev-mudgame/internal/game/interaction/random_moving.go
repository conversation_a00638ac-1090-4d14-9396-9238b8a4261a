package interaction

import (
	"context"

	"github.com/sirupsen/logrus"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/rev-mudgame/internal/game"
	"golang.52tt.com/services/rev-mudgame/internal/world"
)

// 单人在城镇内移动位置
type RandomMovingInteractionHandler struct {
	worldMgr *world.WolrdMgr
}

func NewRandomMovingHandler(w *world.WolrdMgr) *RandomMovingInteractionHandler {
	return &RandomMovingInteractionHandler{
		worldMgr: w,
	}
}

// 描述本行为
func (m *RandomMovingInteractionHandler) Desc() string {
	return "城镇内随机移动"
}

// 判断是否响应这种事件
func (m *RandomMovingInteractionHandler) Check(ctx context.Context, ev game.EventInfo) bool {

	if ev.Type == game.EventTimer {
		return true
	}
	return false
}

// 执行互动行为
func (m *RandomMovingInteractionHandler) DoInteract(ctx context.Context, ev game.EventInfo) (error, bool) {

	channelID := ev.Cid

	var town *world.Town
	if 0 == channelID {
		// 随机选一个town
		town = m.worldMgr.GetRandomTown(ctx)
	} else {
		// 查询town
		town = m.worldMgr.GetTownByCID(ctx, channelID)
	}

	if town == nil {
		log.Errorf("town is nil")
		return nil, false
	}
	location := town.GetRandomLocation()

	// 随机挑选一个用户
	user, userLocationName := m.worldMgr.GetRandomUser(town.ID)
	if user == nil {
		log.Errorf("town %d not found user", town.ID)
		return nil, false
	}
	if userLocationName == location.Name {
		log.Debugf("user %d already in %s", user.ID, location.Name)
		return nil, false
	}

	m.worldMgr.UserMoving(user, town.ID, location.Name)
	logrus.Debugf("user %d moving to %s", user.ID, location.Name)
	return nil, true
}
