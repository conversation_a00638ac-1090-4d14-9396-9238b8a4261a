package server

import (
	"context"
	"errors"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channellivepush"
	"golang.52tt.com/services/channel-live-push/manager"
	"golang.52tt.com/services/risk-control/backpack-sender/utils"
	"time"

	"golang.52tt.com/services/channel-live-push/conf"
	"golang.52tt.com/services/channel-live-push/event"
)

type ChannelLivePushServer struct {
	sc *conf.ServiceConfigT

	//cacheClient *cache.ChannelLivePushCache
	//mysqlStore *mysql.Store

	presentKafkaSub *event.PresentKafkaSubscriber
	channelKfkSub   *event.ChannelKfkSubscriber
	chLiveKfkSub    *event.ChannelLiveKfkSubscriber
	chFansKfkSub    *event.FansValueKfkSubscriber
	followKfkSub    *event.FollowKfkSubscriber
	micKfkSub       *event.MicEventKfkSubscriber
	//onlineKfkSub    *event.OnlineKfkSubscriber
	//authKfkSub *event.AuthKfkSubscriber

	mgr *manager.Manager
}

func (s *ChannelLivePushServer) ClearUserData(ctx context.Context, req *pb.ClearUserDataReq) (*pb.ClearUserDataResp, error) {

	out := &pb.ClearUserDataResp{}

	//s.mgr.Cache.DelUserRecommendData(req.Uid, req.All)

	log.DebugWithCtx(ctx, "ClearUserData uid:%v", req.Uid)

	return out, nil
}

func (s *ChannelLivePushServer) GetChannelLivePushData(ctx context.Context, req *pb.GetChannelLivePushDataReq) (*pb.GetChannelLivePushDataResp, error) {

	log.DebugWithCtx(ctx, "GetChannelLivePushData in:%+v", req)

	out := &pb.GetChannelLivePushDataResp{}
	events, err := s.mgr.Cache.GetRecommendEvent(req.Uid, 0, 100000)
	if nil != err {
		return out, err
	}
	out.Events = events

	log.DebugWithCtx(ctx, "GetChannelLivePushData out:%+v", out)

	return out, nil
}

func NewChannelLivePushServer(ctx context.Context, cfg config.Configer) (*ChannelLivePushServer, error) {

	sc := &conf.ServiceConfigT{}

	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		return nil, err
	}

	mgr, err := manager.NewMgr(sc)

	if nil != err {
		return nil, err
	}

	svr := &ChannelLivePushServer{
		sc:  sc,
		mgr: mgr,
	}

	err = svr.initKfk(sc, mgr)
	if nil != err {
		return nil, err
	}

	//utils.DaemonFunc(mgr.AutoClearEvent, time.Second*60)

	//在线前台推送
	/*	for i := 0; i < 100; i++ {
		idx := i
		f := func() error {
			return mgr.HandlerPushTask(uint32(idx), pb.ChannelLivePushType_ONLINE_FRONT)
		}
		utils.DaemonFunc(f, time.Second*5)
	}*/

	//在线后台推送
	/*	for i := 0; i < 100; i++ {
		idx := i
		f := func() error {
			return mgr.HandlerPushTask(uint32(idx), pb.ChannelLivePushType_ONLINE_BACK)
		}
		utils.DaemonFunc(f, time.Second*5)
	}*/

	//推离线
	for i := 0; i < 100; i++ {
		idx := i
		f := func() error {
			return mgr.HandlerPushTask(uint32(idx), pb.ChannelLivePushType_OFFLINE)
		}
		utils.DaemonFunc(f, time.Second*2)
	}

	return svr, nil
}

func (s *ChannelLivePushServer) initKfk(sc *conf.ServiceConfigT, mgr *manager.Manager) error {

	presentKfk, err := event.NewPresentKafkaSubscriber(sc.PresentKafkaConfig.ClientID, sc.PresentKafkaConfig.GroupID,
		sc.PresentKafkaConfig.TopicList(), sc.PresentKafkaConfig.BrokerList(), mgr)
	if nil != err {
		return err
	}
	s.presentKafkaSub = presentKfk
	presentKfk.Start()

	chLiveKfkSub, err := event.NewKafkaChannelLiveSubscriber(sc.ChLiveKafkaConfig.ClientID, sc.ChLiveKafkaConfig.GroupID,
		sc.ChLiveKafkaConfig.TopicList(), sc.ChLiveKafkaConfig.BrokerList(), mgr)

	if nil != err {
		return err
	}
	s.chLiveKfkSub = chLiveKfkSub
	chLiveKfkSub.Start()

	chFansKfkSub, err := event.NewKafkaFansValueSubscriber(sc.ChLiveFansConfig.ClientID, sc.ChLiveFansConfig.GroupID,
		sc.ChLiveFansConfig.TopicList(), sc.ChLiveFansConfig.BrokerList(), mgr)

	if nil != err {
		return err
	}
	s.chFansKfkSub = chFansKfkSub
	chFansKfkSub.Start()

	channelKfkSub, err := event.NewChannelKafkaSubscriber(sc.EnterChannelConfig.ClientID, sc.EnterChannelConfig.GroupID,
		sc.EnterChannelConfig.TopicList(), sc.EnterChannelConfig.BrokerList(), mgr)

	if nil != err {
		return err
	}
	s.channelKfkSub = channelKfkSub
	channelKfkSub.Start()

	followKfkSub, err := event.NewKafkaFollowSubscriber(sc.FollowKafkaConfig.ClientID, sc.FollowKafkaConfig.GroupID,
		sc.FollowKafkaConfig.TopicList(), sc.FollowKafkaConfig.BrokerList(), mgr)

	if nil != err {
		return err
	}
	s.followKfkSub = followKfkSub
	followKfkSub.Start()

	micKfkSub, err := event.NewMicKafkaSubscriber(sc.MicKafkaConfig.ClientID, sc.MicKafkaConfig.GroupID,
		sc.MicKafkaConfig.TopicList(), sc.MicKafkaConfig.BrokerList(), mgr)

	if nil != err {
		return err
	}
	s.micKfkSub = micKfkSub
	micKfkSub.Start()

	/*	onlineKfkSub, err := event.NewKafkaOnlineSubscriber(sc.OnlineKafkaConfig.ClientID, sc.OnlineKafkaConfig.GroupID,
			sc.OnlineKafkaConfig.TopicList(), sc.OnlineKafkaConfig.BrokerList(), mgr)
		if nil != err {
			return err
		}
		s.onlineKfkSub = onlineKfkSub
		onlineKfkSub.Start()

		authKfkSub, err := event.NewKafkaAuthSubscriber(sc.AuthKafkaConfig.ClientID, sc.AuthKafkaConfig.GroupID, sc.AuthKafkaConfig.TopicList(), sc.AuthKafkaConfig.BrokerList(), mgr)
		if nil != err {
			return err
		}
		s.authKfkSub = authKfkSub
		authKfkSub.Start()*/

	return nil
}

func (s *ChannelLivePushServer) ChannelLiveReport(ctx context.Context, in *pb.ChannelLiveReportReq) (*pb.ChannelLiveReportResp, error) {

	out := &pb.ChannelLiveReportResp{}

	log.DebugWithCtx(ctx, "ChannelLiveReport in:%+v", in)

	if conf.IsCheckFollow() {
		ok, _ := s.mgr.IsFollow(in.AudienceUid, in.AnchorUid, false)
		if !ok {
			return out, nil
		}
	}

	err := s.mgr.HandlerRecommendEvent(&pb.RecommendEvent{
		CreateAt:  uint32(time.Now().Unix()),
		EventType: in.ReportType,
		FansUid:   in.AudienceUid,
		AnchorUid: in.AnchorUid,
		ChannelId: in.ChannelId,
	})

	if nil != err {
		log.ErrorWithCtx(ctx, "ChannelLiveReport err:%v", err)
		return out, err
	}

	log.DebugWithCtx(ctx, "ChannelLiveReport in:%+v out:%+v", in, out)

	return out, nil
}

func (s *ChannelLivePushServer) ShutDown() {
	s.presentKafkaSub.Close()
}
