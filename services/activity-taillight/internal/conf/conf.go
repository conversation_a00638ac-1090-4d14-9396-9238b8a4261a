package conf

import (
	"encoding/json"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"io/ioutil"
)

type TailLightConfig struct {
	BizId    uint32 `json:"biz_id"`
	Name     string `json:"name"`
	Desc     string `json:"desc"`
	Icon     string `json:"icon"`
	Priority uint32 `json:"priority"`
	Interval uint32 `json:"interval"` // 尾灯间隔多久后过期(秒)
}

type ServiceConfigT struct {
	MongoConf *config.MongoConfig `json:"mongo"`
	RedisConf *config.RedisConfig `json:"redis"`

	TailLightConf map[uint32]*TailLightConfig `json:"taillight_conf"`
}

func (sc *ServiceConfigT) Parse(configFile string) error {
	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		log.Errorf("parse config file err: %v", err)
		return err
	}

	err = json.Unmarshal(data, &sc)
	if err != nil {
		log.Errorf("json unmarshal err: %v", err)
		return err
	}

	return nil
}

func (sc *ServiceConfigT) GetTaillightConf(bizId uint32) *TailLightConfig {
	if sc.TailLightConf == nil || sc.TailLightConf[bizId] == nil {
		return new(TailLightConfig)
	}

	return sc.TailLightConf[bizId]
}