syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/game-mission";

package game_mission;

import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service GameMission {
	// 获取登录任务信息
	rpc GetLoginTask(GetLoginTaskReq) returns (GetLoginTaskResp) {}
	// 领取登录任务奖励
	rpc ReceiveLoginTaskAward(ReceiveLoginTaskAwardReq) returns (ReceiveLoginTaskAwardResp) {}
	// 获取用户邀请人数
	rpc GetUserInviteNum(GetUserInviteNumReq) returns (GetUserInviteNumResp) {}
	// 模拟邀请事件
	rpc HandleInviteEvent(HandleInviteEventReq) returns (HandleInviteEventResp) {}
	// 手动补发奖励
	rpc ReissueTaskAward(ReissueTaskAwardReq) returns (ReissueTaskAwardResp) {}
	// 用于内部发放奖励数校对
	rpc GetRewardCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
}

// 任务类型
enum TaskType {
	TaskTypeNone = 0;
	TaskTypeInvite = 1; // 邀请
	TaskTypeLogin = 2; // 登录
}

// 奖励类型
enum AwardType {
	AwardTypeNone = 0;
	AwardTypeGameTicket = 1; // 游戏券
	AwardTypeCouponTicket = 2; // 优惠券
}

// 登录任务
message LoginTask {
	enum State {
		StateNone = 0;
		StateUnreceived = 1; // 未领取
		StateReceived = 2; // 已领取
        StateEnded = 3; // 已结束
	}

	// 任务标题
	string title = 1;
	// 任务状态
	State state = 2;
	// 任务描述
	string desc = 3;
	// 任务背景图
	string background = 4;
	// 弹窗 ICON 图
	string popup_icon = 5;
}

message GetLoginTaskReq {
	uint32 uid = 1;
}

message GetLoginTaskResp {
	LoginTask login_task = 1;
}

message ReceiveLoginTaskAwardReq {
	uint32 uid = 1;
}

message ReceiveLoginTaskAwardResp {
}

message GetUserInviteNumReq {
	uint32 uid = 1;
}

message GetUserInviteNumResp {
	// 邀请总数
	uint32 invite_num = 2;
}

message HandleInviteEventReq {
	// 邀请者uid
	uint32 inviter_uid = 1;
	// 被邀请者uid
	uint32 invitee_uid = 2;
}

message HandleInviteEventResp {}

message ReissueTaskAwardReq {
	uint32 begin_at = 1;
	uint32 end_at = 2;
}

message ReissueTaskAwardResp {
	uint32 reissue_num = 1;
}

// 邀请事件
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message InviteEvent {
	string activityId = 1; // 活动业务ID
	string activityUrl = 2; // 活动链接
	uint32 uid = 3; // 被邀请人UID
	uint32 inviterUid = 4; // 邀请者UID
	uint32 time = 5; // 事件发生时间戳（激活时间）
	string deviceId = 6; // 设备ID
	string appId = 7; // tt语音-ttvoice，在呀-zaiya，欢游-huanyou 声洞-shengdong
	string sourceId = 8; // 来源ID，"s_operate_attribution"为安装激活APP事件，"event_topic"为唤起打开页事件
	string deviceType = 9;// 用户当天登录设备类型 邀请拉新：invite  召回 recall 活跃 active
}
