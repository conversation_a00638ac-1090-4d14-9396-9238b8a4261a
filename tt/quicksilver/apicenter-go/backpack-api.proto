syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/apicentergo";
// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package apicentergo;

enum PackageItemType
{
  UNKNOW_ITEM_TYPE = 0;
  BACKPACK_PRESENT = 1;          // 礼物
  BACKPACK_CARD_RICH_EXP = 2;      // 财富经验加速卡
  BACKPACK_CARD_CHARM_EXP = 3;      // 每日经验加速卡
  BACKPACK_LOTTERY_FRAGMENT = 4;  // 抽奖碎片,砸蛋获得
  BACKPACK_CARD_RICH_INCR = 5;    // 财富卡
  BACKPACK_CARD_KNIGHT    = 6;    // 骑士体验卡
}


// 包裹来源
enum PackageSourceType
{
  UNKNOW_PACKAGE_SOURCE = 0;
  PACKAGE_SOURCE_ACTIVITY_PRESENT = 1;  // 礼物活动
  PACKAGE_SOURCE_DAILY_CHECKIN = 2;      // 每日签到任务
  PACKAGE_SOURCE_FIRST_RECHARGE = 3;      // 首充活动
  PACKAGE_SOURCE_OFFICIAL = 4;        // 内容管理后台中发放
  PACKAGE_SOURCE_SMASHEGG = 5;            // 砸蛋活动
  PACKAGE_SOURCE_CONVERSION = 6;          // 碎片兑换
  PACKAGE_SOURCE_AWARD_CENTER = 7;        // 发奖中心
  PACKAGE_SOURCE_AWARD_YUYIN_LIVE_MISSION = 8; // 语音直播任务奖励
  PACKAGE_SOURCE_INTERACTION_INTIMACY_MISSION = 9; // 亲密度任务
  PACKAGE_SOURCE_ITEM_CONVERSION = 10; // 物品合成
  PACKAGE_SOURCE_NOBILITY = 11;  // 贵族系统发放
  PACKAGE_SOURCE_TBEAN_BUY = 12;  // T豆购买礼物
}

// 包裹配置
message PackageCfg
{
  uint32 bg_id = 1;
  string name = 2;
  string desc = 3;
  bool is_del = 4;
}

// 包裹项配置
message PackageItemCfg
{
  uint32 bg_item_id = 1;
  uint32 bg_id = 2;
  uint32 item_type = 3;         // PackageItemType
  uint32 source_id = 4;         // 礼物id/卡片id/碎片id
  uint32 item_count = 5;        // 数量
  uint32 fin_time = 6;          // 截止时间
  bool is_del = 7;
  uint32 weight = 8;            // 权重
  uint32 dynamic_fin_time = 9;  // 发放后的有效时间(秒)
  string item_name = 10;      // 包裹内项配置名称
  uint32 valid_time = 11;     // 包裹项有效时间
  uint32 card_time = 12;      // 包裹项卡片的有效时间
  string img_url = 13;        // 包裹项图标
  uint32 tbean_value = 14;    // 包裹项T豆单价
  uint32 months = 15;         //发放之后，多少个月之后的1号过期
  uint64 card_value = 16;     // 卡片数值
  uint32 red_diamond_value = 17; //礼物红钻价值
}

message PackageItemCfgList {
  repeated PackageItemCfg package_item_cfg_list=1;
}


message UserBackpackItem
{
  uint32 item_type = 1;
  uint32 user_item_id = 2;
  uint32 item_count = 3;
  uint32 fin_time = 4;
  uint32 source_id = 5;
  uint32 weight = 6;         // 物品权重
  uint32 obtain_time = 7;
  string name = 8;
  uint32 source_type = 9;
}

message UserPackageItem
{
  uint32 bg_id = 1;
  uint32 source_type = 2;
  repeated string bg_content = 3;
  uint32 count = 4;
  uint32 obtain_time = 5;
}

// 添加包裹配置
message AddPackageCfgReq
{
  PackageCfg cfg = 1;
}
message AddPackageCfgResp
{
  PackageCfg cfg = 1;
}

// 删除包裹配置
message DelPackageCfgReq
{
  uint32 bg_id = 1;
}
message DelPackageCfgResp
{
}

// 获取包裹配置
message GetPackageCfgReq
{
    uint32 offset = 1;
    uint32 limit = 2;
}
message GetPackageCfgResp
{
  repeated PackageCfg cfg_list = 1;
  uint32 total_count = 2; //包裹总数量
}

// 根据id列表获取包裹配置
message GetPackageCfgByIdsReq
{
  repeated uint32 id_list = 1;
}
message GetPackageCfgByIdsResp
{
  repeated PackageCfg cfg_list = 1;
}

// 增加包裹项配置
message AddPackageItemCfgReq
{
  PackageItemCfg item_cfg = 1;
}
message AddPackageItemCfgResp
{
  PackageItemCfg item_cfg = 1;
}

// 修改包裹项配置
message ModPackageItemCfgReq
{
  PackageItemCfg item_cfg = 1;
}
message ModPackageItemCfgResp
{
}

// 删除包裹项配置
message DelPackageItemCfgReq
{
  uint32 bg_id = 1;
  uint32 bg_item_id = 2;
}
message DelPackageItemCfgResp
{
}

// 根据包裹id获取包裹项配置
message GetPackageItemCfgReq
{
  uint32 bg_id = 1;             
  repeated uint32 bg_id_list = 2; //bg_id or  bg_id_list 只能用一个
}
message GetPackageItemCfgResp
{
  repeated PackageItemCfg item_cfg_list = 1;
  repeated PackageItemCfgList package_item_cfg_list = 2;
}

// 发放包裹
message GiveUserPackageReq
{
  uint32 uid = 1;
  uint32 bg_id = 2;
  uint32 num = 3;
}
message GiveUserPackageResp
{
}

// 功能卡片配置
message FuncCardCfg
{
  uint32 card_id = 1;
  uint32 card_type = 2; // PackageItemType
  string card_name = 3;
  string card_desc = 4;
  string card_url = 5;
  uint32 card_times = 6; // 倍数（加速卡）
  uint32 valid_time = 7;
  uint32 is_del = 8;
  uint64 card_value = 9; // 数值（财富卡）
}

// 增加功能卡片配置
message AddFuncCardCfgReq
{
  FuncCardCfg card_cfg = 1;
}
message AddFuncCardCfgResp
{
}

// 删除功能卡片配置
message DelFuncCardCfgReq
{
  uint32 card_id = 1;
}
message DelFuncCardCfgResp
{
}

// 根据卡片id获取功能卡片配置
message GetFuncCardCfgReq
{
  repeated uint32 card_id_list = 1;
}
message GetFuncCardCfgResp
{
  repeated FuncCardCfg card_cfg_list = 1;
}

// 根据uid获取用户背包
message GetUserBackpackReq
{
  uint32 uid = 1;
}

message GetUserBackpackResp
{
  repeated UserBackpackItem backpack_item_list = 1;
  uint32 last_obtain_ts = 2;
}

// 包裹发放查询
message GetUserPackageReceiveReq
{
  uint32 uid = 1;
  uint32 begin_ts = 2;
  uint32 end_ts = 3;
}

message GetUserPackageReceiveResp
{
  repeated UserPackageItem package_item_list = 1;
}

//批量回滚相关

message DeductItem{
  uint32 item_type = 1;
  uint32 source_id = 2;
  uint32 count = 3;
}

message DeductDetail {
  uint32 uid = 1;
  repeated DeductItem item_list = 2;
  uint32 count = 3;
  uint32 source_type = 4;
  bool is_all_source = 5;
}

message BatchDeductUserItemReq {
  repeated DeductDetail deduct_list = 1; //要回滚物品的信息
  string operator = 2; // 操作者
  string order_id = 3;   // 订单id，用于防止重复回滚 
}

enum DeductFailType{
  DEDUCTFAILTYPE_UNVALID = 0;               // 未知类型
  DEDUCTFAILTYPE_UID_NOT_EXIST = 1;               // 错误类型：uid不存在
  DEDUCTFAILTYPE_ITEM_NOT_ENOUGH = 2;                // 错误类型：物品数量不足
  DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL = 3;                // 错误类型：扣除物品失败
}

message DeductResult {
  uint32 uid = 1;
  repeated DeductItem success_item_list = 2;
  repeated DeductItem fail_item_list = 3;
  uint32 fail_type = 4; //失败类型DeductFailType
}

message BatchDeductUserItemResp {
  repeated DeductResult deduct_list = 1; //实际回滚物品的信息
}

message RollBackDetail {
  uint32 uid = 1;
  uint32 item_type = 2;
  uint32 source_id = 3;
  uint32 count = 4;
}

message BatchRollBackUserItemReq {
  repeated RollBackDetail rollback_list = 1; //要回滚物品的信息
  uint32 operator = 2; // 操作者
  string order_id = 3;   // 订单id，用于防止重复回滚
}

message BatchRollBackUserItemResp {
  repeated RollBackDetail rollback_list = 1; //实际回滚物品的信息
}