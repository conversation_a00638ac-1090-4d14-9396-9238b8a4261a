syntax = "proto3";

package star_train;
import "tt/quicksilver/extension/options/options.proto";
option go_package = "golang.52tt.com/protocol/services/star-train";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service StarTrain {
    option (service.options.service_ext) = {
        service_name: "star-train"
      };

    // 获取摘星列车玩法信息
    rpc GetStarTrainInfo(GetStarTrainInfoReq) returns (GetStarTrainInfoResp) {}
    // 获取摘星列车进程信息
    rpc GetStarTrainProgress(GetStarTrainProgressReq) returns (GetStarTrainProgressResp) {}
    // 获取摘星列车播报数据
    rpc GetStarTrainBroadcast(GetStarTrainBroadcastReq) returns (GetStarTrainBroadcastResp) {}
    // 获取简单玩法活动信息
    rpc GetSimpleActivityInfo(GetSimpleActivityInfoReq) returns (GetSimpleActivityInfoResp) {}
    // 获取用户记录
    rpc GetStarTrainUserRecord(GetStarTrainUserRecordReq) returns (GetStarTrainUserRecordResp) {}
    // 获取发车记录
    rpc GetTrainDepartureRecord(GetTrainDepartureRecordReq) returns (GetTrainDepartureRecordResp) {}
    // 获取座次表
    rpc GetStarTrainSeatList(GetStarTrainSeatListReq) returns (GetStarTrainSeatListResp) {}

    // 用户参与测试接口
    rpc TestUserJoin(TestUserJoinReq) returns (TestUserJoinResp) {}
    // 即将开启活动提醒推送触发(仅测试环境有效)
    rpc TestActivityBeginNotify(TestActivityBeginNotifyReq) returns (TestActivityBeginNotifyResp) {}

    /******** 运营后台配置接口 *************/
    // 活动配置
    rpc SetStarTrainConf(SetStarTrainConfReq) returns (SetStarTrainConfResp) {}
    // 获取活动配置
    rpc GetStarTrainConf(GetStarTrainConfReq) returns (GetStarTrainConfResp) {}
    rpc GetStarTrainConfById(GetStarTrainConfByIdReq) returns (GetStarTrainConfByIdResp){}
    // 修改活动配置
    rpc UpdateStarTrainConf(UpdateStarTrainConfReq) returns (UpdateStarTrainConfResp) {}
    // 删除活动配置
    rpc DelStarTrainConf(DelStarTrainConfReq) returns (DelStarTrainConfResp) {}
    // 新增活动礼物配置
    rpc SetStarTrainGift(SetStarTrainGiftReq) returns (SetStarTrainGiftResp) {}
    // 获取活动礼物配置（全量）
    rpc GetAllStarTrainGift(GetAllStarTrainGiftReq) returns (GetAllStarTrainGiftResp) {}
    // 删除活动礼物配置
    rpc DelStarTrainGift (DelStarTrainGiftReq) returns (DelStarTrainGiftResp) {}

    /*********对账接口**********/
    // 发放包裹数据对账
    rpc GetAwardTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetAwardOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

    // 送礼订单对账
    rpc GetPresentOrderCount (ReconcileV2.TimeRangeReq ) returns (ReconcileV2.CountResp ) {}
    rpc GetPresentOrderIds (ReconcileV2.TimeRangeReq ) returns (ReconcileV2.OrderIdsResp ) {}
    // 补单
    rpc FixPresentOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}
}

// 奖励类型
enum AwardType {
    AwardTypeNone = 0;
    AwardTypeGoal = 1; // 摘星奖
    AwardTypeHelp = 2; // 助力奖
    AwardTypeConsolation = 3; // 阳光普照
}

enum StationType {
    StationTypeNone = 0;
    StationTypeNormal = 1;  // 普通站点
    StationTypeEnd = 2;     // 终点站点
}

enum WeekDay {
    Sunday = 0;           // 周日 
    Monday = 1;           // 周一
    Tuesday = 2;          // 周二
    Wednesday = 3;        // 周三
    Thursday = 4;         // 周四
    Friday = 5;           // 周五
    Saturday = 6;         // 周六  
    UnkownDay = 7;            
  }

// 开启时间段配置
message DayTimeRangeInfo{
    uint32 day_begin = 1;                   // 时间偏移量，以当天00点作为偏移量起点，如果第一场次在08点开启，则传入 8*60*60
    uint32 day_end = 2;                     // 活动开启日的结束时间，时间偏移量，同字段1
    repeated uint32 w_day_list = 3;         // 每周的活动开启日,see WeekDay
}

message StarTrainBgInfo{
    uint32 bg_id = 1;           // 包裹id
    uint32 gift_id = 2; 
    uint32 gift_price = 3;
    string gift_name = 4;
    string gift_pic = 5;
    uint32 gift_num = 6;

    uint32 stock = 7;  //每轮奖励的库存数量
    uint32 award_type = 8;  //奖励类型 see AwardType

    uint32 fin_time = 9;
    uint32 months = 10;
    uint32 dynamic_fin_time = 11;
}

// 站点信息
message StationInfo{
    string station_id = 1;
    uint32 distance = 2;            // 路程长度
    StarTrainBgInfo goal_prize = 3; // 终点大奖包裹
    StarTrainBgInfo help_prize = 4; // 助力大奖包裹
    repeated StarTrainBgInfo consolation_prize_list = 5; // 阳光普照奖励包裹

    uint32 station_type = 6;        // 站点类型 see StationType
}

// 列车路线信息
message TrainRouteInfo{
    uint32 train_id = 1;  // 路线id
    uint32 weight = 2;    // 权重
    StationInfo goal_station = 3; // 终点站点
    repeated StationInfo station_list = 4; // 经停站点

    uint32 fixed_order = 5;  // 固定次序，填写时在开启日内的第n次开启的列车必是该列车

    uint64 total_reward = 7;    // 总返奖金额
    float reward_ratio = 8;     // 返奖比例 
}

// 玩法活动配置
message StarTrainConf{
    uint32 conf_id = 1;
    string activity_name = 2;                // 活动名称
    int64 activity_begin = 3;               // 活动开启的时间
    int64 activity_end = 4;                 // 活动结束的时间
    repeated TrainRouteInfo train_list = 5;  // 列车路线信息，一次活动可配置多趟不同的路线
    repeated DayTimeRangeInfo time_list = 6; // 开启日时间段
}


// ===================== 运营后台相关begin ======================

// 设置玩法活动配置
message SetStarTrainConfReq{
    StarTrainConf conf = 1;
}

message SetStarTrainConfResp{
}


// 活动状态
enum StarTrainStatus{
    None = 0;           // 无效
    Using = 1;          // 生效中
    Unused = 2;         // 未开始
    Finished = 3;       // 已结束
}

message GetStarTrainConfReq{
    uint32 status = 1;      // 选择待查看活动配置的状态,see StarTrainStatus
    uint32 page = 2;        // 页码,从1开始
    uint32 page_size = 3;
}

message GetStarTrainConfResp{
    repeated StarTrainConf conf_list = 1;    // 活动配置
    uint32 total = 2;
}

message UpdateStarTrainConfReq{
    StarTrainConf conf = 1;
}

message UpdateStarTrainConfResp{
}

message DelStarTrainConfReq{
    uint32 conf_id = 1;
}

message DelStarTrainConfResp{
}

message GetStarTrainConfByIdReq{
    uint32 conf_id = 1;
}

message GetStarTrainConfByIdResp{
    StarTrainConf conf = 1;
}

// ========= 活动礼物配置 =============== 
message SetStarTrainGiftReq{
    uint32 gift_id = 1;
}

message SetStarTrainGiftResp{
}

// 全量接口
message GetAllStarTrainGiftReq{
}

message TicketGiftInfo{
    uint32 gift_id = 1;
    uint32 gift_price = 2;
    string gift_name = 3;
    string gift_pic = 4;
}

message GetAllStarTrainGiftResp{
  repeated TicketGiftInfo gift_list = 1;
}

message DelStarTrainGiftReq{
    uint32 gift_id = 1;
}

message DelStarTrainGiftResp{
}

// ===================== 运营后台相关end ======================

enum PrizeType {
  PRIZE_TYPE_UNSPECIFIED = 0;  // 无效
  PRIZE_TYPE_STAR_AWARD = 1;  // 摘星奖
  PRIZE_TYPE_BOOST_AWARD = 2; // 助推奖
  PRIZE_TYPE_COMFORT_AWARD = 3;// 安慰奖（阳光普照奖）
  PRIZE_TYPE_HONOR_TOP1_AWARD = 4;  // TOP1荣誉奖
  PRIZE_TYPE_HONOR_TOP2_AWARD = 5;  // TOP2荣誉奖
  PRIZE_TYPE_HONOR_TOP3_AWARD = 6;  // TOP3荣誉奖
}

// 列车进程信息
message StarTrainProgress {
  uint32 act_id = 1;                     // 活动id
  uint32 round_id = 2;                     // 轮次id
  uint32 total_distance = 3;              // 总路程
  repeated StarTrainStation stations = 4; // 站点列表
  repeated uint32 seat_uid_list = 5;     // 前三座位用户
  uint32 current_distance = 6;            // 当前位置
  uint32 current_speed = 7;            // 当前时速
  StarTrainBingo last_round_bingo = 8;     // 上一轮摘星奖信息
  uint32 today_train_idx = 9;     // 今天的第几趟列车
  StarTrainAward star_award = 10; // 摘星奖品信息
  int64 server_ts = 11;
}

// 中奖信息
message StarTrainBingo {
  uint32 uid = 1;
  StarTrainAward award = 2;
}

// 站点信息
message StarTrainStation {
  uint32 position = 1;            // 站点位置, 最后一站位置等于总路程
  StarTrainAward award_info = 2;  // 奖品信息
  uint32 award_uid = 3;     // 获得奖品的用户
}

// 奖品信息
message StarTrainAward {
  uint32 id = 1;
  string name = 2;
  string icon = 3;
  uint32 price = 4;
  uint32 amount = 5;
  uint32 gift_type = 6;  
}

// 用户参与信息
message StarTrainUserJoin {
  uint32 act_id = 1;                     // 活动id
  uint32 round_id = 2;       // 轮次id
  uint32 join_distance = 3; // 参与助推的距离（占座分数），为0即为未参与
  uint32 last_join_final_distance = 4;  // 用户最近一次参与后列车的最终距离
  uint32 seat_amount = 5;   // 占座数
  int64 server_ts = 6;
}

// 获取列车信息
message GetStarTrainInfoReq {
  uint32 uid = 1;
}

message GetStarTrainInfoResp {
  StarTrainProgress progress = 1;     // 进程信息
  StarTrainUserJoin my_join_info = 2; // 我的参与信息
  int64 act_end_time = 3;             // 活动结束时间
  int64 next_round_begin = 4;         // 下一轮开始时间
}

// 获取列车进程信息
message GetStarTrainProgressReq {
  uint32 uid = 1;
}

message GetStarTrainProgressResp {
  StarTrainProgress progress = 1;     // 进程信息
}

message StarTrainBroadcast {
  enum BroadcastType {
    BROADCAST_TYPE_UNSPECIFIED = 0;  // 无效
    BROADCAST_TYPE_STAR_AWARD = 1;  // 摘星奖播报
    BROADCAST_TYPE_BOOST_AWARD = 2; // 助推奖播报
    BROADCAST_TYPE_JOIN = 3;  // 上车播报
    BROADCAST_TYPE_SEAT = 4;  // 占座播报
  }
  uint32 broadcast_type = 1;  // 播报类型， see BroadcastType
  uint32 uid = 2;             // 用户信息
  StarTrainAward award = 3;   // 奖品信息
  uint32 incr_distance = 4;   // 距离(占座分数)
}

// 获取列车播报数据
message GetStarTrainBroadcastReq {
  uint32 uid = 1;
}

message GetStarTrainBroadcastResp {
  repeated StarTrainBroadcast broadcast_list = 1; // 播报列表
}

message StarTrainSeat {
  uint32 uid = 1;  // 用户信息
  uint32 seat_id = 2;  // 座位id(排名)
  uint32 seat_amount = 3;  // 占座数
}

// 获取列车座次表
message GetStarTrainSeatListReq {
  uint32 uid = 1;
  uint32 round_id = 2;       // 轮次id
}

message GetStarTrainSeatListResp {
  repeated StarTrainAward award_list = 1;   // 荣誉奖品列表
  repeated StarTrainSeat seat_list = 2;  // 座位列表
  StarTrainSeat my_seat = 3;  // 我的座位
  string honor_award_desc = 4; // 荣耀奖励说明文案
}

message GetSimpleActivityInfoReq{
}

message GetSimpleActivityInfoResp{
    uint32 act_id = 1;
    int64 act_begin_time = 2;
    int64 act_end_time = 3;

    repeated uint32 gift_id_list = 4;
    uint32 seat_unit_price = 5;
    string act_gift_desc = 6;   // 活动礼物描述

    string strategy_rule_img = 7;   // 摘星策略说明图
    string not_reached_stop_desc = 8; // 未抵达的经停站说明文案
    string already_reached_stop_desc = 9; // 已抵达的经停站说明文案
    string last_star_award_user_desc = 10; // 上一轮摘星者弹窗说明文案
    string star_award_stop_desc = 11;    // 摘星奖弹窗说明文案
    string star_award_stop_join_desc = 12; // 摘星奖弹窗如何参与说明文案
}

message StarTrainUserRecord{
    uint32 id = 1;
    StarTrainAward award = 2;
    uint32 gift_type = 3;
    uint32 prize_type = 4;   // 奖品类型，see star_train_logic.proto.StarTrainAwardNotify.PrizeType
    int64 award_ts = 5;
    uint32 price_type = 6;  // 1-红钻 2-T豆
}

// 获取用户记录
message GetStarTrainUserRecordReq{
    uint32 uid = 1;
    string offset = 2;
    uint32 limit = 3;
}

message GetStarTrainUserRecordResp{
    repeated StarTrainUserRecord record_list = 1;
    string next_offset = 2;  // 下一页的请求offset
}

message TrainDepartureRecord{
    message TopUserInfo{
        uint32 uid = 1;
        bool is_bingo = 2;
    }

    string round_id = 1;
    string goal_station = 2;    // 终点站名称
    int64 end_time = 3;         // 列车到站时间
    repeated TopUserInfo user_list = 4;
}

// 获取发车记录
message GetTrainDepartureRecordReq{
    string offset = 2;
    uint32 limit = 3;
}

message GetTrainDepartureRecordResp{
    repeated TrainDepartureRecord record_list = 1;
    string next_offset = 2;  // 下一页的请求offset

    // 荣耀奖励配置
    repeated StarTrainAward honor_prize_list = 3;
    string honor_award_desc = 4; // 荣耀奖励说明文案
}

message TestUserJoinReq {
    uint32 uid = 1;
    uint32 cid = 2;
    uint32 present_id = 3;
    uint32 price = 4;
    string order_id = 5;
}

message TestUserJoinResp {

}

message TestActivityBeginNotifyReq{
    repeated uint32 ch_type_list =1;    // 推送房间类型列表
    repeated uint32 cid_list = 2;   // 推送房间cid列表
}

message TestActivityBeginNotifyResp{
}