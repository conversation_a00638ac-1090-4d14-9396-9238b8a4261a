syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-live-stats";
package channel_live_stats;


service ChannelLiveStats {

  rpc GetAnchorBaseInfo (GetAnchorBaseInfoReq) returns (GetAnchorBaseInfoResp) {
  }

  rpc BatchGetAnchorBaseInfo (BatchGetAnchorBaseInfoReq) returns (BatchGetAnchorBaseInfoResp) {
  }

  rpc GetAnchorBaseInfoList (GetAnchorBaseInfoListReq) returns (GetAnchorBaseInfoListResp) {
  }

  rpc GetGuildAnchorBaseInfo (GetGuildAnchorBaseInfoReq) returns (GetGuildAnchorBaseInfoResp) {
  }

  rpc GetGuildDailyTotalStats (GetGuildDailyTotalStatsReq) returns (GetGuildDailyTotalStatsResp) {
  }

  rpc GetAnchorTotalStatsBetweenDate (GetAnchorTotalStatsBetweenDateReq) returns (GetAnchorTotalStatsBetweenDateResp) {
  }

  rpc GetGuildDailyStatsList (GetGuildDailyStatsListReq) returns (GetGuildDailyStatsListResp) {
  }

  rpc GetAnchorDailyRecordWithDateList (GetAnchorDailyRecordWithDateListReq) returns (GetAnchorDailyRecordWithDateListResp) {
  }

  rpc BatchGetAnchorDailyRecord (BatchGetAnchorDailyRecordReq) returns (BatchGetAnchorDailyRecordResp) {
  }

  rpc GetGuildDailyAnchorList (GetGuildDailyAnchorListReq) returns (GetGuildDailyAnchorListResp) {
  }

  rpc GetAnchorDailyStatsList (GetAnchorDailyStatsListReq) returns (GetAnchorDailyStatsListResp) {
  }

  rpc BatchGetAnchorWeeklyRecord (BatchGetAnchorWeeklyRecordReq) returns (BatchGetAnchorWeeklyRecordResp) {
  }

  rpc GetGuildWeeklyAnchorList (GetGuildWeeklyAnchorListReq) returns (GetGuildWeeklyAnchorListResp) {
  }

  rpc GetAnchorWeeklyStatsList (GetAnchorWeeklyStatsListReq) returns (GetAnchorWeeklyStatsListResp) {
  }

  rpc GetGuildMonthlyStats (GetGuildMonthlyStatsReq) returns (GetGuildMonthlyStatsResp) {
  }

  rpc GetAnchorMonthlyStats (GetAnchorMonthlyStatsReq) returns (GetAnchorMonthlyStatsResp) {
  }

  rpc GetGuildAnchorMonthlyStatsList (GetGuildAnchorMonthlyStatsListReq) returns (GetGuildAnchorMonthlyStatsListResp) {
  }

  rpc GetAnchorMonthlyTotalStats (GetAnchorMonthlyTotalStatsReq) returns (GetAnchorMonthlyTotalStatsResp) {
  }

  rpc BatchGetAnchorMonthlyStats (BatchGetAnchorMonthlyStatsReq) returns (BatchGetAnchorMonthlyStatsResp) {
  }

  rpc BatchGetAnchorMonthlyStatsWithDate (BatchGetAnchorMonthlyStatsWithDateReq) returns (BatchGetAnchorMonthlyStatsWithDateResp) {
  }

  rpc GetAnchorMonthlyStatsList (GetAnchorMonthlyStatsListReq) returns (GetAnchorMonthlyStatsListResp) {
  }

  rpc UpdateGuildAnchorAgentInfo(UpdateGuildAnchorAgentInfoReq) returns (UpdateGuildAnchorAgentInfoResp) {
  }

  rpc BatchGetAnchorMonthlyStatsByUid (BatchGetAnchorMonthlyStatsByUidReq) returns (BatchGetAnchorMonthlyStatsByUidResp) {}

  rpc GetGuildAnchorList (GetGuildAnchorListReq) returns (GetGuildAnchorListResp) {}

  rpc GetAnchorMatchList (GetAnchorMatchListReq) returns (GetAnchorMatchListResp) {}

  // 获取公会运营能力
  rpc GetGuildOperationalCapabilities (GetGuildOperationalCapabilitiesReq) returns (GetGuildOperationalCapabilitiesResp) {}

  // 更新公会运营能力
  rpc UpdateGuildOperationalCapabilities (UpdateGuildOperationalCapabilitiesReq) returns (UpdateGuildOperationalCapabilitiesResp) {}


  // 周维度经营分析     
  rpc GetWeekBusinessAnalysis(GetWeekBusinessAnalysisReq) returns (GetWeekBusinessAnalysisResp) {}

  // 月维度经营分析     
  rpc GetMonthBusinessAnalysis(GetMonthBusinessAnalysisReq) returns (GetMonthBusinessAnalysisResp) {}

  // 获取知识标题课程列表
  rpc GetCurriculumMessageList(GetCurriculumMessageListReq) returns (GetCurriculumMessageListResp) {}

  //触发定时任务
  rpc TriggerTimer(TriggerTimerReq) returns (TriggerTimerResp) {}

}

message AnchorBaseInfo {
  uint32 uid = 1;
  uint32 sign_guild_id = 2;
  uint32 live_room_id = 3;
  uint32 last_live_at = 4;  // 最后一次开播时间
  uint32 agent_uid = 5;  // 经纪人id
  uint32 first_live_ts = 6;  // 首次开播时间
  uint32 channel_live_id = 7; // 场次id，0表示没在开播
}

message GetAnchorBaseInfoReq {
  uint32 uid = 1;
}

message GetAnchorBaseInfoResp {
  AnchorBaseInfo info = 1;
}

message BatchGetAnchorBaseInfoReq {
  repeated uint32 uid_list = 1;
}

message BatchGetAnchorBaseInfoResp {
  repeated AnchorBaseInfo info_list = 1;
}

message GetAnchorBaseInfoListReq {
  uint32 guild_id = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  uint32 anchor_flag = 4;  // 主播类型 see AnchorFlag
  uint32 agent_uid = 5;  // 经纪人id 为0则查询公会的所有主播
  uint32 min_first_live_ts = 6;  // 不为0时查询大于最小第一次开播时间的主播
}
message GetAnchorBaseInfoListResp {
  repeated AnchorBaseInfo info_list = 1;
  uint32 total_cnt = 2;
}

message GetGuildAnchorBaseInfoReq {
  uint32 guild_id = 1;
  uint32 uid = 2;
  uint32 agent_uid = 3;  // 经纪人id 为0时则查全部
}
message GetGuildAnchorBaseInfoResp {
  AnchorBaseInfo info = 1;
}

// 公会汇总数据
message GuildTotalStats {
  uint32 live_anchor_cnt = 1;     // 开播主播数
  uint32 total_anchor_income = 2; // 主播收礼总数
  uint32 total_channel_fee = 3;   // 房间总流水
  uint32 valid_anchor_cnt = 4;  // 有效开播主播数
  uint32 new_add_anchor_cnt = 5;  // 新增主播数
  uint32 anchor_knight_income = 6;  // 骑士流水
  uint32 channel_pkg_fee = 7;  // 直播间背包流水
  uint32 virtual_fee = 8;  // 直播间虚拟流水
}

message GetGuildDailyTotalStatsReq {
  uint32 guild_id = 1;
  uint32 begin_date = 2;
  uint32 end_date = 3;
}

message GetGuildDailyTotalStatsResp {
  GuildTotalStats stats = 1;
}


message GuildDailyStats {
    GuildTotalStats stats = 1;
    uint32 date_time = 2;
}

message GetGuildDailyStatsListReq {
  uint32 guild_id = 1;
  uint32 begin_date = 2;
  uint32 end_date = 3;
  uint32 begin = 4;
  uint32 limit = 5;
}

message GetGuildDailyStatsListResp {
  repeated GuildDailyStats stats_list = 1;
}

// 按日期和指定条件获取记录中的主播列表
message GetGuildDailyAnchorListReq {
  enum Condition {
    Live = 0;   // 开播主播
    Valid = 1;  // 有效天主播
    Invalid = 2;// 非有效天主播
  }

  uint32 guild_id = 1;
  uint32 date = 2;
  uint32 condition = 3;
  uint32 begin = 4;
  uint32 limit = 5;
}

message GetAnchorTotalStatsBetweenDateReq {
  uint32 anchor_uid = 1;
  uint32 guild_id = 2;
  uint32 begin_date = 3;
  uint32 end_date = 4;
}

message GetAnchorTotalStatsBetweenDateResp {
  uint32 total_anchor_income = 1;
  uint32 total_channel_fee = 2;
}

message GetGuildDailyAnchorListResp {
  repeated uint32 anchor_list = 1;
}

message AnchorDailyStats {
  uint32 uid = 1;
  uint32 date = 2;
  uint32 anchor_income = 3;
  uint32 channel_fee = 4;
  uint32 live_valid_minutes = 5;
  bool is_valid_day = 6;
  uint32 day_active_fans = 7;
  uint32 day_sp_fans = 8;
  uint32 fans_send_fee = 9;
  uint32 day_follow_cnt = 10;
  uint32 new_add_fans = 11;
  uint32 live_minutes = 12;
  uint32 agent_uid = 13;  // 经纪人
  uint32 knight_income = 14;  // 主播骑士流水
  bool is_live_active_day = 15;  //是否是直播活跃天
  uint32 consumer_cnt = 16;  // 付费人数
  uint32 audience_cnt = 17;  // 观众人数
  uint32 game_fee = 18;  // 互动游戏流水
  uint32 game_min = 19;  // 互动游戏时长 分钟
  bool is_game_active_day = 20;  // 是否是 互动游戏活跃天
  uint32 game_channel_fee = 21;
  uint32 virtual_fee = 22; // 虚拟直播流水
  uint32 virtual_income = 23; // 虚拟直播收礼
  uint32 virtual_min = 24; // 虚拟直播时长 分钟
  bool is_virtual_active_day = 25; // 是否是 虚拟直播活跃天
  uint32 week_new_add_fans = 26; // 周新增粉丝数
  uint32 week_consumer_cnt = 27;  // 周付费人数
  uint32 week_audience_cnt = 28;  // 周观众人数
  uint32 week_follow_cnt = 29;  // 周关注人数
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorDailyRecordWithDateListReq {
  uint32 uid = 1;
  uint32 beginTime = 2;
  uint32 endTime = 3;
}

message GetAnchorDailyRecordWithDateListResp {
  repeated AnchorDailyStats list = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchGetAnchorDailyRecordReq {
  uint32 guild_id = 1;
  repeated uint32 uid_list = 2;
  uint32 beginTime = 3;
  uint32 endTime = 4;
  uint32 agent_uid = 5;  // 经纪人id， 为0查全部
}
message BatchGetAnchorDailyRecordResp {
  repeated AnchorDailyStats list = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorDailyStatsListReq {
  uint32 guild_id = 1;
  uint32 beginTime = 2;
  uint32 endTime = 3;
  uint32 offset = 4;
  uint32 limit = 5;
  uint32 agent_uid = 6;  // 经纪人id， 为0查询全部
  repeated uint32 uid_list = 7;  // 不为空，指定uid查询
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorDailyStatsListResp {
  repeated AnchorDailyStats list = 1;
  uint32 totalCnt = 2;
}

message AnchorWeeklyStats {
  uint32 uid = 1;
  uint32 anchor_income = 3;
  uint32 channel_fee = 4;
  uint32 live_valid_minutes = 5;
  uint32 valid_days_cnt = 6;
  uint32 week_active_fans = 7;
  uint32 week_sp_fans = 8;
  uint32 fans_send_fee = 9;
  uint32 week_follow_cnt = 10;
  uint32 new_add_fans = 11;
  uint32 live_minutes = 12;
  uint32 agent_uid = 13; // 经纪人id
  uint32 anchor_knight_income = 14;  // 主播骑士流水
  uint32 live_active_days = 15;  // 直播活跃天
  uint32 consumer_cnt = 16;  // 付费人数
  uint32 audience_cnt = 17;  // 观众人数
  uint32 game_fee = 18;  // 互动游戏流水
  uint32 game_min = 19;  // 互动游戏时长 分钟
  uint32 game_active_days = 20;  // 互动游戏活跃天数
  uint32 game_channel_fee = 21;
  uint32 virtual_fee = 22; // 虚拟直播流水
  uint32 virtual_income = 23; // 虚拟直播收礼
  uint32 virtual_min = 24; // 虚拟直播时长 分钟
  uint32 virtual_active_days = 25; // 虚拟直播活跃天
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchGetAnchorWeeklyRecordReq {
  uint32 guild_id = 1; // 不为0时，查指定公会id
  repeated uint32 uid_list = 2;
  uint32 beginTime = 3;
  uint32 endTime = 4;
  uint32 agent_uid = 5;  // 经纪人id， 为0查询全部
}
message BatchGetAnchorWeeklyRecordResp {
  repeated AnchorWeeklyStats list = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorWeeklyStatsListReq {
  uint32 guild_id = 1;
  uint32 beginTime = 2;
  uint32 endTime = 3;
  uint32 offset = 4;
  uint32 limit = 5;
  uint32 agent_uid = 6; // 经纪人id， 为0查询全部
  repeated uint32 anchor_list = 7;  // 不为空,指定查询
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorWeeklyStatsListResp {
  repeated AnchorWeeklyStats list = 1;
  uint32 totalCnt = 2;
}


// 按周和指定条件获取记录中的主播列表
message GetGuildWeeklyAnchorListReq {
  enum Condition {
    Live = 0;   // 开播主播
    Valid = 1;  // 有效天>=5的主播
    Invalid = 2;// 有效天<5的主播
    AppointActiveMinCnt = 3;  // 指定最小直播活跃天数查询 
  }

  uint32 guild_id = 1;  // 公会id不为0时，按公会id查询
  uint32 week_begin_date = 2;
  uint32 week_end_date = 3;
  uint32 condition = 4;
  uint32 begin = 5;
  uint32 limit = 6;
  uint32 active_min_cnt =7;
}

message GetGuildWeeklyAnchorListResp {
  repeated uint32 anchor_list = 1;
}

// 公会直播月汇总数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GuildMonthlyStats {
  uint32 live_anchor_cnt = 1;     // 开播主播数
  uint64 total_anchor_income = 2; // 主播收礼总数
  uint64 total_channel_fee = 3;   // 房间总流水
  uint32 valid_anchor_cnt = 4;    // 有效主播数
  uint32 new_valid_anchor_cnt = 5;// 新增有效主播数
  uint32 potential_anchor_cnt = 6;// 潜力主播数
  uint32 new_add_anchor_cnt = 7;  // 新增主播数
  uint64 anchor_knight_income = 8;  // 骑士流水
  uint32 qualityAnchorCnt = 9;  // 优质主播数
  uint32 activeAnchorCnt = 10;   // 活跃主播数
  uint32 newActiveAnchorCnt = 11;  // 月新增活跃主播数
  uint32 highIncomeAnchorCnt = 12;  // 高收礼主播数
  uint64 channel_pkg_fee = 13;  // 直播间背包流水
  uint32 profession_prac_cnt = 14;  // 专业从业者数
}

message GetGuildMonthlyStatsReq {
  uint32 guild_id = 1;
  uint32 month_time = 2;
}

message GetGuildMonthlyStatsResp {
  GuildMonthlyStats stats = 1;
}

enum AnchorFlag {
  Common = 0;
  Valid = 1;    // 有效主播
  NewValid = 2; // 新增有效主播
  Live = 3;     // 开播主播
  Potential = 4;// 潜力主播
  NewAdd = 5;  // 新增主播
  TodayLive = 6;  // 今日开播主播
  BreakLive = 7;  // 断播主播 指近7天内（含7天）无开播主播 
  QUALITY = 8;    // 优质主播
  ACTIVE = 9;     // 活跃主播
  NEW_ACTIVE = 10;   // 新增活跃主播
  HIGH_INCOME = 11;   // 高收礼
  Profession_Prac = 12; // 专业从业者数
  Is_Living = 13;  // 正在开播的
}

message AnchorMonthlyStats {
  uint32 uid = 1;
  uint32 channel_fee = 2;      // 直播间流水
  uint32 anchor_income = 3;    // 主播收入
  uint32 day_live_valid_cnt = 4; // 直播有效天
  uint32 live_valid_minutes = 5;// 有效直播时长
  uint32 new_flag = 6;  // 新增主播标记
  uint32 last_month_fans_cnt = 7;  // 上个月累计粉丝数
  uint32 new_fans_cnt = 8;  // 新增粉丝数
  uint32 active_fans = 9;
  uint32 sp_fans = 10;
  uint32 fans_send_fee = 11;
  uint32 follow_cnt = 12;
  uint32 date_ts = 13;
  uint32 live_minutes = 14;  // 直播时长
  uint32 agent_uid = 15;  // 经纪人
  uint32 anchor_knight_income = 16;  // 主播骑士流水
  uint32 live_active_days = 17;  // 直播活跃天数
  bool   is_quality_anchor = 18;  // 是否是优质主播
  bool   is_active_anchor = 19;  // 是否是活跃主播
  bool   is_new_active_anchor = 20;  // 是否是新增活跃主播
  uint32 channel_pkg_fee = 21;  // 直播间背包流水
  uint32 anchor_pkg_income = 22; // 主播收礼背包流水
  bool   is_profession_prac = 23;  // 是否是专业从业者
  uint32 game_fee = 24; // 互动游戏流水
  uint32 game_min = 25; // 互动游戏时长 分钟
  uint32 game_active_days = 26; // 互动游戏活跃天
  uint32 game_channel_fee = 27;
  uint32 virtual_fee = 28; // 虚拟直播流水
  uint32 virtual_income = 29; // 虚拟直播收礼
  uint32 virtual_min = 30; // 虚拟直播时长 分钟
  uint32 virtual_active_days = 31; // 虚拟直播活跃天
  uint32 consumer_cnt = 32;  // 付费人数
}

message GetAnchorMonthlyStatsReq {
  uint32 guild_id = 1;
  uint32 anchor_uid = 2;
  uint32 month_time = 3;
}

message GetAnchorMonthlyStatsResp {
  AnchorMonthlyStats stats = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorMonthlyStatsListReq {
  uint32 guild_id = 1;
  uint32 beginMonth = 2;
  uint32 endMonth =3;
  uint32 offset = 4;
  uint32 limit = 5;
  uint32 agent_uid = 6;  // 经纪人id 为0则查询全部
  repeated uint32 anchor_list = 7;  //不为空 指定主播查询
}
message GetAnchorMonthlyStatsListResp {
  repeated AnchorMonthlyStats list = 1;
  uint32 total_cnt = 2;
}

message GetGuildAnchorMonthlyStatsListReq {
  uint32 guild_id = 1;
  uint32 condition = 2; // 筛选条件 see AnchorFlag
  uint32 begin = 3;
  uint32 limit = 4;
  uint32 month_time = 5;
  uint32 agent_uid = 6;  // 经纪人id 为0则查询全部
}

message GetGuildAnchorMonthlyStatsListResp {
  repeated AnchorMonthlyStats stats_list = 1;
  uint32 total_cnt = 2;
}

message BatchGetAnchorMonthlyStatsReq {
  repeated uint32 uid_list = 1;
  uint32 month_ts = 2;
  uint32 guild_id = 3;
}
message BatchGetAnchorMonthlyStatsResp {
  repeated AnchorMonthlyStats stats_list = 1;
}

message BatchGetAnchorMonthlyStatsWithDateReq {
  uint32 guild_id = 1;
  repeated uint32 uid_list = 2;
  uint32 begin_ts = 3;
  uint32 end_ts = 4;
  uint32 agent_uid = 5;  // 经纪人id 为0则查询全部
}
message BatchGetAnchorMonthlyStatsWithDateResp {
  repeated AnchorMonthlyStats stats_list = 1;
}


message MonthlyTotalStats {
  uint32 month_time = 1;
  uint32 anchor_income = 2;       // 主播收入
  uint32 day_live_valid_cnt = 3;  // 直播有效天
}

message GetAnchorMonthlyTotalStatsReq {
  uint32 anchor_uid = 1;
  repeated uint32 month_time_list = 2;
}

message GetAnchorMonthlyTotalStatsResp {
  repeated MonthlyTotalStats stats = 1;
}

//更新公会主播的经纪人信息
message UpdateGuildAnchorAgentInfoReq {
  uint32  guild_id = 1;
  uint32  agent_uid = 2;
  repeated uint32 anchor_list = 3;  // 为空时将该经纪人旗下的所有主播经纪人id置0
}
message UpdateGuildAnchorAgentInfoResp {
}

message BatchGetAnchorMonthlyStatsByUidReq {
  repeated uint32 anchor_uids = 1;
  uint32 month_time = 2;
}
message BatchGetAnchorMonthlyStatsByUidResp {
  // buf:lint:ignore MESSAGE_PASCAL_CASE
  message monthStats {
    uint32 anchor_uid = 1;
    string yearmonth = 2;
    uint32 anchor_income = 3; // 收礼流水
    uint32 new_fans_cnt = 4;
    uint32 live_active_cnt = 5;
    uint32 consumer_cnt = 6; // 送礼人数
    uint32 knight_income = 7; // 骑士流水
    uint32 audience_cnt = 8;  // 观众人数
    uint32 live_valid_minutes = 9;// 有效直播时长
    uint32 day_live_valid_cnt = 10; // 直播有效天
    uint32 channel_fee = 11;      // 直播间流水
    uint32 follow_cnt = 12;
    uint32 game_fee = 13; // 互动游戏流水
    uint32 game_min = 14; // 互动游戏时长 分钟
    uint32 game_active_days = 15; // 互动游戏活跃天
    uint32 game_channel_fee = 16;
    uint32 virtual_fee = 17; // 虚拟直播流水
    uint32 virtual_income = 18; // 虚拟直播收礼
    uint32 virtual_min = 19; // 虚拟直播时长 分钟
    uint32 virtual_active_days = 20; // 虚拟直播活跃天
  }
  repeated monthStats list = 1;
}


//场次数据
message MatchData {
   uint32 channel_fee = 1; // 直播间流水
   uint32 anchor_income = 2;  // 主播总收礼
   uint32 live_min = 3;  //开播总时长
   float pkg_gift_ratio = 4;  // 背包流水占比
   uint32 live_ts = 5;  // 开播时间
}

message AnchorMatchData {
   AnchorBaseInfo base_info = 1;
   MatchData match_data = 2;
}

message GetGuildAnchorListReq {
   enum SortType {
     Sort_Type_Invalid = 0; // 无效
     Sort_Type_Fee = 1; // 直播间流水排序
     Sort_Type_Income = 2; // 主播收入排序
     Sort_Type_Min = 3;  // 直播时长排序
     Sort_Type_Pkg_Ratio = 4; // 背包流水占比排序
   }
   uint32 anchor_uid = 1;  // 指定主播查询
   uint32 agent_uid = 2;  // 指定经纪人查询
   uint32 live_type = 3;  // 0:全部 1:开播
   uint32 sort_type = 4;  // 排序类型 
   uint32 sort = 5;  //  1：降序, 2：升序
   uint32 page = 6; // 从1开始
   uint32 page_size = 7; 
   uint32 guild_id = 8;  
   uint32 uid = 9; 
}

message GetGuildAnchorListResp {
   repeated AnchorMatchData anchor_list = 1;
   uint32 total_cnt = 2;  // 总数
   uint32 live_anchor_cnt = 3; // 开播主播数量
   uint32 guild_anchor_cnt = 4;  // 公会签约主播数量
}


// 获取主播的场次数据 
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorMatchListReq {
   uint32 anchor_uid = 1; // 主播uid
   uint32 begin_ts = 2; 
   uint32 end_ts = 3; 
   uint32 page = 4; // 从1开始
   uint32 page_size = 5; 
   uint32 guild_Id = 6;  
}
message GetAnchorMatchListResp {
   AnchorBaseInfo anchor_info = 1;
   repeated MatchData data_list = 2; 
   uint32 total_cnt = 3;
}

// 公会运营能力
message GuildOperationalData {
    uint32 guild_id = 1;  // 公会id
    uint32 revenue_level = 2;  // 收入等级
    uint32 new_sign_anchor_cnt = 4;  // 新签约主播数量
    uint32 enabled_live_user_count = 5;  // 开播用户数
    uint32 enabled_live_new_user_count = 8;  // 新开播用户数
    uint32 new_active_anchor_cnt = 9;  // 新活跃主播数量
    uint32 pro_anchor_cnt = 10;  // 专业主播数量
    uint32 mature_anchor_cnt = 11;  // 成熟主播数
    uint32 pot_active_anchor_cnt = 12;  // 潜力主播数量
    uint32 new_sign_pro_anchor = 13;  // 新签约专业主播数量
    uint64 revenue = 14;  // 收入
    uint32 revenue_score = 15;  // 收入分数
    uint32 pro_anchor_score = 16;  // 专业主播分数
    uint32 new_active_anchor_score = 17;  // 新活跃主播分数
    uint32 mature_anchor_score = 18;  // 成熟主播分数
    uint32 pot_active_anchor_score = 19;  // 潜力主播分数
    uint32 total_score = 20;  // 总的分数
  uint32 revenue_time = 21;  // 收入时间

}

// 获取公会的运营能力
message GetGuildOperationalCapabilitiesReq {
  uint32 guild_id = 1;  // 公会id
  uint32 revenue_level = 2;  // 收入等级
  int64 end_date = 3;  // 结束日期

}

message GetGuildOperationalCapabilitiesResp {
  repeated GuildOperationalData data_list = 1;
}

message UpdateGuildOperationalCapabilitiesReq {
  uint32 update_time = 22;  // 更新时间
}

message UpdateGuildOperationalCapabilitiesResp {
}

//关键指标类型
enum KpiType {
    Kpi_Type_Invalid = 0;
    Kpi_Type_Guild_Income = 1;  //公会营收
    Kpi_Type_New_Sign_Anchor = 2; // 新签达人数
    Kpi_Type_Live_Anchor = 3;   // 开启听听达人数
    Kpi_Type_Two_Wan_Anchor = 4;  // 收礼≥2万豆达人数
    Kpi_Type_Ten_Wan_Anchor= 5;  // 收礼≥10万豆达人数
    Kpi_Type_Pro_Anchor = 6;  // 专业从业者数
    Kpi_Type_Mature_Anchor = 7;  // 成熟达人数
    Kpi_Type_Pot_Active_Anchor = 8;  //潜力活跃达人数
}

//经营分析
message BusinessAnalysis {
    uint32 kpi_type = 1;  // see KpiType
    uint64 value = 2;   // 数值
    int64 ratio_value = 3;   // 值环比 0表示持平
    uint32 rank = 4;   // 同层级排行
    int32 ratio_rank = 5;   // 排名环比     0表示持平
    uint32 all_rank = 6;  // 全部公会排名
    uint64 level_ave = 7;  // 层级平均值
    uint64 new_value = 8;  // 新达人值
    uint64 old_value = 9;  // 老达人值
}

// 经营诊断
message BusinessDiagnosis {
   uint32 kpi_type = 1;  // see KpiType
   string diag = 2;  // 诊断结果
}

// 周维度经营分析     
message GetWeekBusinessAnalysisReq {
    uint32 guild_id = 1; // 公会id
    uint32 begin_ts = 2;  // 开始时间
    uint32 end_ts = 3;   // 结束时间
}
message GetWeekBusinessAnalysisResp {
    repeated BusinessAnalysis analysis_list = 1;  // 指标分析列表
    repeated BusinessDiagnosis diagnosis_list = 2;  // 指标诊断列表
}


// 月维度经营分析     
message GetMonthBusinessAnalysisReq {
    uint32 guild_id = 1; // 公会id
    uint32 month_ts = 2;  // 月时间
}
message GetMonthBusinessAnalysisResp {
    repeated BusinessAnalysis kpi_list = 1;  // 关键指标列表
    repeated BusinessDiagnosis diagnosis_list = 2;  // 指标诊断列表
}

// 知识标题信息
message CurriculumMessage {
  string curriculum_title = 1;  //知识标题
  string curriculum_desc = 2;   //只是简介
  string file_type = 3;        //文件类型
  string file_url = 4;           //资源链接
}

// 获取知识标题课程列表
message GetCurriculumMessageListReq {
  uint32 type = 1; // 课程类型 CurriculumType
}

message GetCurriculumMessageListResp {
  repeated CurriculumMessage curriculum_list = 1; //课题知识列表
}

//触发定时任务
message TriggerTimerReq {
   // 定时任务类型
   enum TimerType
   {
      Timer_Type_Invalid = 0;  // 无效
      Timer_Type_GuildWeekBusinessAnalysis = 1;  // 周维度经营分析
      Timer_Type_GuildMonthBusinessAnalysis = 2;  // 月维度经营分析
   }

   TimerType timer_type = 1; // 定时任务类型
   uint32  ts = 2;  //时间戳
}
message TriggerTimerResp {
}

