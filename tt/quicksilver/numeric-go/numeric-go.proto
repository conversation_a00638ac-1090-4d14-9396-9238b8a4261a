syntax = "proto3";

import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";
package numeric_go;

// 变更类型
enum NumericT {
  UnknownT = 0;
  Rich = 1;
  Charm = 2;
}

// 来源
enum SourceT {
  SourceUnknownT = 0;
  SourceTPresentTBean = 1; // T豆送礼变更
  SourceTPresentRedDiamond = 2; // 红钻送礼变更
  SourceTRichCard = 3; // 财富卡变更
  SourceTESport = 4; // 电竞变更
  SourceTBean = 5; // T豆消费变更
  SourceTKnight = 6; // 开通骑士团变更
  SourceTManual = 7; // 手动变更
  SourceTCleanRich = 8; // 后台清除财富值
}

message GetPersonalNumericReq {
  uint32 uid = 1;
}
message GetPersonalNumericResp {
  uint64 rich = 1; // 财富值
  uint64 charm = 2;   // 魅力值
}

message GetPersonalNumericV2Req {
  uint32 uid = 1;
}
message GetPersonalNumericV2Resp {
  uint64 rich = 1; // 财富值
  uint64 charm = 2;   // 魅力值
  Level rich_level = 3; // 财富等级
  Level charm_level = 4; // 魅力等级
  Level vip_level = 5; // VIP等级
}

message Level {
  uint32 main_level = 1; // 主等级
  uint32 sub_level = 2; // 子等级1
  uint32 sub_level2 = 6; // 子等级2
  string level_text = 3; // 三级等级 如 12345 对应等级 2-1-2
  string level_name = 4; // 等级名称
  uint64 server_level = 5; // 服务器等级
}

message GetVipSinceReq {
  uint32 uid = 1;
}
message GetVipSinceResp {
  Level vip_level = 1; // VIP等级
  uint64 be_vip_time = 2; // 成为VIP的时间
  uint32 be_vip_days = 3; // 成为VIP的天数
}

message BatchGetPersonalNumericReq {
  repeated uint32 uid_list = 1;
}

message PersonalNumeric{
  uint32 uid = 1;
  uint64 charm = 2;
  uint64 rich = 3;
  Level rich_level = 5; // 财富等级
  Level charm_level = 6; // 魅力等级
}

message BatchGetPersonalNumericResp {
  repeated PersonalNumeric numeric_list = 1;
}

message AddUserNumericReq
{
  uint32 uid = 1;
  uint64 rich_value = 3;
  uint64 charm_value = 4;
  string order_id = 5;
}

message AddUserNumericResp
{
  uint64 final_rich_value = 1;
  uint64 final_charm_value = 2;
}

message GetUserRichSwitchReq
{
  uint32 uid = 1;
}

message GetUserRichSwitchResp
{
  bool enable = 1;
}

message UserSwitchStatus
{
  uint32 uid = 1;
  bool enable = 2;
}

message SetUserRichSwitchReq
{
  uint32 uid = 1;
  bool enable = 2;
}

message SetUserRichSwitchResp {}

message UserGiftEventInfo{
  uint32 uid = 1;
  uint32 guild_id = 2;
  uint64 add_value = 3; // 变更财富值
  uint64 final_value = 4; // 变更后的财富值
  bool level_change = 5;
  uint64 before_value = 6;
}

message RecordSendGiftEventReq {
  uint32 giver_uid = 1;
  uint32 receiver_uid = 2;
  uint64 rich_value = 3;

  uint32 giver_guild = 4;    // 一定是发礼物者的公会
  uint32 receiver_guild = 5; // 一定是收礼物者的公会
  uint64 charm_value = 6;
  string order_id = 7;

  uint32 channel_id = 8;
  uint32 channel_guild = 9;
  uint32 price_type = 10;  // 货币类型 ga.PRESENT_PRICE_TYPE
  uint32 gift_id = 11; //礼物ID
}

message RecordSendGiftEventResp
{
  repeated uint32 level_change_uid_list = 1; // 用户土豪魅力值等级 如果有变化才会在列表中
  uint64 real_charm = 2; // 收礼者 实际增加的魅力值

  uint64 giver_curr_all_rich = 3; // 送礼者 在送礼后的 完整的土豪值
  uint64 receiver_curr_all_charm = 4; // 收礼者 在送礼后的 完整的魅力值

  uint64 real_rich = 5; // 送礼者 实际增加的土豪值
  uint64 before_rich = 6; //送礼前的土豪值
}

// 用户批量送礼物的行为
// 仅记录红钻类型的行为 T豆行为通过MQ消费记录
message BatchRecordSendGiftEventReq {
  UserGiftEventInfo giver_user_info = 1;
  repeated UserGiftEventInfo receiver_user_info_list = 2;
  string order_id = 3;
  uint32 channel_id = 4;     // 如果发礼物是在房间内 这里是房间ID
  uint32 channel_guild = 5;  // 如果发礼物是在房间内 且是公会房 这里是房间对应的公会ID
  uint32 price_type = 6;  // 货币类型 ga.PRESENT_PRICE_TYPE
  uint32 gift_id = 7; // 礼物id
}

message BatchRecordSendGiftEventResp
{
  UserGiftEventInfo giver_user_info = 1; // 送礼者 在送礼后的 土豪值信息
  repeated UserGiftEventInfo receiver_user_info_list = 2; // 收礼者 在收礼后的 魅力值信息
}

message GetCleanUserRichRecordsReq {
  uint32 uid = 1;
  uint32 op_time_start = 2;
  uint32 op_time_end = 3;
  uint32 offset = 4;
  uint32 limit = 5;
}

// 签约身份类型
// from anchorcontract-go.proto.SIGN_ANCHOR_IDENTITY
enum ContractIdentity {
  CONTRACT_MULTIPLAYER = 0;
  CONTRACT_RADIO_LIVE = 1;
}

message GetCleanUserRichRecordsResp {
  message CleanUserRichRecord {
    uint32 uid = 1;
    string tid = 2;
    string nickname = 3;
    uint32 contract_guild_id = 4; // 签约公会ID
    repeated ContractIdentity contract_identity = 5; // 签约身份
    uint32 contract_time = 6; // 签约时间
    uint64 curr_rich = 7; // 当前财富值
    Level curr_rich_level = 8; // 当前财富等级
    uint64 before_rich = 9; // 清理前财富值
    Level before_rich_level = 10; // 清理前财富等级
    uint64 clean_rich = 11; // 清理财富值
    uint32 op_time = 12; // 操作时间
    string operator = 13; // 操作人
    uint32 order_id = 14; // 任务号
    string filename = 15; // 文件名
    string file_url = 16; // 文件url
    uint32 contract_guild_short_id = 17; // 签约公会靓号
  }
  repeated CleanUserRichRecord records = 1;
  uint32 total = 2;
}
message CreateCleanUserRichTaskReq {
  message Item {
    uint32 uid = 1;
    uint64 clean_rich = 2; // 清理财富值
    bool clean_all = 3; // 是否清理全部 (忽略clean_rich)
  }
  repeated Item items = 1;
  string operator = 2; // 操作人
  string filename = 3; // 文件名
  string file_url = 4; // 文件url
}

message CreateCleanUserRichTaskResp {
  message Item {
    uint32 uid = 1;
    uint64 clean_rich = 2; // 清理财富值
    uint32 contract_guild_id = 4; // 签约公会ID
    repeated ContractIdentity contract_identity = 5; // 签约身份
    uint32 contract_time = 6; // 签约时间
    uint32 contract_guild_short_id = 7; // 签约公会靓号
  }
  uint32 order_id = 1; // 任务号
  repeated Item items = 2;
}

message CleanUserRichTaskReq {
  enum Operation {
    OPERATION_APPROVAL = 0; // 审批
    OPERATION_CANCEL = 1; // 取消
  }
  uint32 order_id = 1; // 任务号
  Operation opt = 2; // 审批操作
}
message CleanUserRichTaskResp {

}

message UseRichCardReq {
  enum Opt {
    OPT_PREPROCESS = 0; // 预执行（提交后必须处理成功，除非回滚）
    OPT_PROCESS = 1; // 确认执行
    OPT_ROLLBACK = 2; // 回滚操作
  }
  string order_id = 1; // 订单号
  uint32 uid = 2; // 用户ID
  uint64 add_rich = 3; // 总共增加的财富值
  Opt opt = 4; // 操作类型
  uint32 card_id = 5; // 卡片ID
  uint32 use_count = 6; // 使用的卡片数量
  string card_name = 7; // 卡片名称
}
message UseRichCardResp {
  uint64 add_rich = 1; // 增加的财富值
  uint64 final_rich = 2; // 最终的财富值
}

enum RichSwitchChangeType {
  RICH_SWITCH_CHANGE_TYPE_UNKNOWN = 0; // 未知
  RICH_SWITCH_CHANGE_TYPE_MANUAL = 1; // 手动打开
  RICH_SWITCH_CHANGE_TYPE_CONTRACT_CANCEL = 2; // 解约
  RICH_SWITCH_CHANGE_TYPE_CONTRACT_RENEW = 3; // 签约恢复
}

service NumericGo {
  // 获取财富值魅力值
  rpc GetPersonalNumeric (GetPersonalNumericReq) returns (GetPersonalNumericResp) {}
  // 获取财富值、魅力值、财富等级、魅力等级、VIP等级
  rpc GetPersonalNumericV2 (GetPersonalNumericV2Req) returns (GetPersonalNumericV2Resp) {}
  // 批量获取财富值魅力值
  rpc BatchGetPersonalNumeric (BatchGetPersonalNumericReq) returns (BatchGetPersonalNumericResp) {}
  // 增加财富值魅力值
  rpc AddUserNumeric (AddUserNumericReq) returns (AddUserNumericResp){}
  // 获取成为VIP的时间
  rpc GetVipSince (GetVipSinceReq) returns (GetVipSinceResp) {}

  // 获取财富值控制开关状态
  rpc GetUserRichSwitch (GetUserRichSwitchReq) returns (GetUserRichSwitchResp) {}
  // 设置是否打开关闭财富值累加
  rpc SetUserRichSwitch (SetUserRichSwitchReq) returns (SetUserRichSwitchResp) {}

  // 送礼增加财富值
  rpc RecordSendGiftEvent (RecordSendGiftEventReq) returns (RecordSendGiftEventResp) {}
  // 批量送礼增加财富值
  rpc BatchRecordSendGiftEvent (BatchRecordSendGiftEventReq) returns (BatchRecordSendGiftEventResp) {}

  // 获取清理财富值记录
  rpc GetCleanUserRichRecords (GetCleanUserRichRecordsReq) returns (GetCleanUserRichRecordsResp) {}
  // 创建清除财富值任务
  rpc CreateCleanUserRichTask (CreateCleanUserRichTaskReq) returns (CreateCleanUserRichTaskResp) {}
  // 执行清理财富值
  rpc CleanUserRichTask (CleanUserRichTaskReq) returns (CleanUserRichTaskResp) {}

  // 背包使用财富卡
  rpc UseRichCard (UseRichCardReq) returns (UseRichCardResp) {}

  rpc GetPresentRichOrderCount (ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetPresentRichOrderList (ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  rpc ReplacePresentRichOrder (ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}

}
