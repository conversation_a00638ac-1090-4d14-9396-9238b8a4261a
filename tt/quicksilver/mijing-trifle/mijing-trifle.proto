syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/mijing-trifle";

package mijing_trifle;


service MijingTrifle {

  // 获取谜之小事任务列表
  rpc GetTrifleTask (GetTrifleTaskReq) returns (GetTrifleTaskResp) {}
  // 更新谜之小事任务
  rpc UpdateTrifleTask (UpdateTrifleTaskReq) returns (UpdateTrifleTaskResp) {}
  // 获取谜之小事任务记录
  rpc GetTrifleRecord (GetTrifleRecordReq) returns (GetTrifleRecordResp) {}
  // 修改已读状态
  rpc MarkRecordRead (MarkRecordReadReq) returns (MarkRecordReadResp) {}
  // 标记完成任务
  rpc FinishTrifleTask (FinishTrifleTaskReq) returns (FinishTrifleTaskResp) {}

  // 新增 or 更新谜之小事视觉风格配置
  rpc UpdateTrifleVisualStyle (UpdateTrifleVisualStyleRequest) returns (UpdateTrifleVisualStyleResponse) {}
  // 删除谜之小事视觉风格配置
  rpc DeleteTrifleVisualStyle (DeleteTrifleVisualStyleRequest) returns (DeleteTrifleVisualStyleResponse) {}
  // 获取谜之小事视觉风格配置列表
  rpc GetTrifleVisualStyleList (GetTrifleVisualStyleListRequest) returns (GetTrifleVisualStyleListResponse) {}

  //---------------------------------------------------------------------------------------
  // 手动领取谜之小事奖励
  //  rpc ManualAcceptRecord (ManualAcceptRecordReq) returns (ManualAcceptRecordResp) {}
  // 更新谜之小事奖励配置
//  rpc UpdateAwardConfig (UpdateAwardConfigReq) returns (UpdateAwardConfigResp) {}
  // 获取谜之小事奖励配置列表
//  rpc GetAwardConfig (GetAwardConfigReq) returns (GetAwardConfigResp) {}
  // 完成谜之小事任务上报
    rpc DebugFinishTrifleTask (DebugFinishTrifleTaskReq) returns (FinishTrifleTaskResp) {}
}



enum FinishMode {
  FINISH_MODE_UNKNOWN = 0;
  FINISH_MODE_ONLY_ONCE_FOREVER = 1;      // 永久单次
  FINISH_MODE_ONLY_ONCE_DAILY = 2;        // 每天一次
  FINISH_MODE_MULTI_TIME_DAILY = 3;       // 可每天多次
}

enum DisplayMode {
  DISPLAY_MODE_UNKNOWN = 0;
  DISPLAY_MODE_ALWAYS = 1;         // 常驻
  DISPLAY_MODE_WHEN_FINISHED = 2;  // 神秘小事，完成时才展示
  DISPLAY_MODE_NEVER_SHOW = 3;     // 不展示出来的小事
}

message TrifleTask {
  string id = 1;                          // 小事任务id，唯一，建议用个小事名的拼音写吧，一眼能看出来
  string title = 2;                       // 小事标题
//  string desc = 3;                        // 小事介绍
  DisplayMode display_mode = 4;           // 是否显示
  FinishMode finish_mode = 5;             // 完成次数的类型
  float sequence = 6;                     // 顺序 (小事的顺序)
  repeated TrifleAward awards = 8;        // 奖励id
  string homepage_text = 9;               // 首页小事文案内容
  string task_content = 10;               // 任务内容
  string task_rule = 11;                  // 任务规则文案
  string image_uri = 13;                  // 小事展示页图片(已完成)
  int64 last_modify_at = 16;              // 最后修改时间
  int32 status = 17;                      // 任务状态
  repeated uint32 scenario_ids = 18;      // 关联剧本id
  string unfinished_image_uri = 19;       // 小事展示页图片(未完成)
  string channel_share_image_uri = 21;    // 房间分享图片

  EventType event_type = 22;              // 触发任务的事件类型
  oneof event_cfg {
    PassTaskEventCfg pass_task_event_cfg = 23; // 完成剧本任务事件的配置
  }
}

// 触发任务的事件类型
enum EventType {
  EVENT_TYPE_COMMON = 0; //通用类型，自行将事件关联到任务ID
  EVENT_TYPE_REGISTER = 1; //注册事件类型
  EVENT_TYPE_PASS_TASK = 2; //完成剧本任务事件类型
}
// 完成剧本任务事件的配置
message PassTaskEventCfg {
  repeated string shared_task_ids = 1;   // 第三方上报的完成任务id，同玩的人都算完成了小事
  repeated string single_task_ids = 2;   // 第三方上报的完成任务id，只有玩家算完成了小事
}

message TrifleAward {
  uint32 id = 1;      //奖励配置id
  uint32 count = 2;   //数量
}

message AwardConfig {
  uint32 id = 1;          //奖励配置id
  string name = 2;        //奖励名称
  string desc = 3;        //奖励描述
  uint32 award_type = 4;  //奖励类别 1：密室券，2：优惠券，3：T豆 (下面数量和时间视这里的类型而定把，不搞oneof了，后面要搞头像框、礼物等要再加关联id)
  uint32 count = 5;       //数量
  int64 duration = 6;     //持续时间
  int32 status = 7;       //状态
}


enum TrifleStatus {
  TRIFLE_STATUS_UNSPECIFIED = 0;
  TRIFLE_STATUS_LOCKED = 1;             // 未解锁
  TRIFLE_STATUS_WAITING_ACCEPT = 2;     // 等待领取
  TRIFLE_STATUS_LIGHTED = 3;            // 已点亮
}


message TrifleRecord {
  TrifleTask task = 1;
  string record_id = 2;
  TrifleStatus trifle_status = 3;     //小事状态
  bool already_read = 4;              //是否已读
  int64 last_finish_at = 5;           //最后完成时间
  int64 first_finish_at = 6;          //第一次完成时间
  repeated uint32 related_uids = 7;   //记录相关联的用户id
}


message FinishTrifleTaskReq {
  uint32 uid = 1;
  string task_id = 2;         // 第三方上报的完成任务id
  string unique_key = 3;      // 唯一标识,用来做幂等,eg1：完成游戏能多次完成/发奖，finishgame-{gameid}-{eventid}   eg2：首次通关奖励能多次完成/发奖，finishgame-1st-{gameid}
  int64 finish_at = 4;        // 完成时间, ms为单位！ms为单位！ms为单位！
  bool expect_send_push = 5;         // 是否期望发送推送，⚠️⚠️里面还有一些逻辑的限制，最终可以由于限制原因不进行推送⚠️⚠️，期望发送不代表最后一定发送
  repeated uint32 related_uids = 10; // 完成任务相关联的用户id
}

message FinishTrifleTaskResp {
  bool finished = 1; //是否完成,重复操作第一次才为true
}

message GetTrifleTaskReq {
  bool without_cache = 11;   //不要缓存，对接后台才需要用
  bool return_all_task = 12; //是否返回所有任务，删除的也会返回

  // 分页暂不考虑了，先默认最多100个就是了
}

message GetTrifleTaskResp {
  repeated TrifleTask tasks = 1;
}

message GetTrifleRecordReq {
  uint32 uid = 1;
  bool include_unfinished = 11;   //返回包括未完成的任务
}

message GetTrifleRecordResp {
  repeated TrifleRecord records = 1;
}

message UpdateTrifleTaskReq {
  TrifleTask task = 1;
}

message UpdateTrifleTaskResp {

}

message UpdateAwardConfigReq {
  AwardConfig award_config = 1;
}

message UpdateAwardConfigResp {

}

message GetAwardConfigReq {
  bool without_cache = 11;   //不要缓存，对接后台才需要用
}

message GetAwardConfigResp {
  repeated AwardConfig award_configs = 1;
}

message ManualAcceptRecordReq {
  uint32 uid = 1;
  string record_id = 2;
  repeated uint64 detail_ids = 3;
}

message ManualAcceptRecordResp {

}

message MarkRecordReadReq {
  uint32 uid = 1;
  repeated string record_ids = 2;
}

message MarkRecordReadResp {

}

message DebugFinishTrifleTaskReq {
  uint32 uid = 1;
  string task_id = 2;         // 第三方上报的完成任务id
  string unique_key = 3;      // 唯一标识,用来做幂等,eg1：完成游戏能多次完成/发奖，finishgame-{gameid}-{eventid}   eg2：首次通关奖励能多次完成/发奖，finishgame-1st-{gameid}
  repeated uint32 related_uids = 10; // 完成任务相关联的用户id
  uint32 random_finish_count = 11;
  bool delete_all = 12;
}

// 谜之小事视觉风格配置
message TrifleVisualStyle {
  enum Platform {
    PLATFORM_UNSPECIFIED = 0;
    PLATFORM_IOS = 1;
    PLATFORM_ANDROID = 2;
  }

  // 入口动画（有好友）
  message EntranceHasFriends {
    string bubble_body_url = 1;  // 气泡框主体，格式：.jpg、.jpeg、.png
    string bubble_border_url = 2;  // 气泡框边框，格式：.jpg、.jpeg、.png
  }

  // p.s 格式不会进行校验，只是用来做文档说明
  string id = 1;  // 唯一 ID（新增时填空，更新时传递）
  string name = 2;  // 图标名称
  Platform platform = 3;  // 平台
  string background_pic_url = 4;  // 背景图片 URL，格式：.jpg、.jpeg、.png
  string entrance_pic_url = 5;  // 入口图片 URL，格式：.jpg、.jpeg、.png
  string seat_pic_url = 6;  // 队伍座位图片 URL，格式：.jpg、.jpeg、.png
  string invite_pic_url = 7;  // 邀请图片 URL，格式：.jpg、.jpeg、.png
  string entrance_no_friends_url = 8;  // 入口动画（无好友），格式：.zip
  repeated EntranceHasFriends entrance_has_friends_list = 9;  // 入口动画（有好友）列表
  int64 start_at = 10;  // 展示时段开始时间戳，格式：unix 时间戳，e.x：1697009793
  int64 end_at = 11;  // 展示时段结束时间戳，格式：unix 时间戳，e.x：1697009793
  string all_friend_entrance_url = 12;  // 全部好友入口图片 URL，格式：.jpg、.jpeg、.png

  int64 created_at = 101;  // 创建时间，格式：unix 时间戳，e.x：1697009793
  int64 updated_at = 102;  // 更新时间，格式：unix 时间戳，e.x：1697009793
}

// 新增 or 更新谜之小事视觉风格配置
message UpdateTrifleVisualStyleRequest {
  TrifleVisualStyle trifle_visual_style = 1;  // 谜之小事视觉风格配置
}

message UpdateTrifleVisualStyleResponse {

}

// 删除谜之小事视觉风格配置
message DeleteTrifleVisualStyleRequest {
  repeated string id_list = 1;  // TrifleVisualStyle.Id 列表
}

message DeleteTrifleVisualStyleResponse {

}

// 获取谜之小事视觉风格配置列表
message GetTrifleVisualStyleListRequest {
  // 数据源
  enum DataSource {
    DATA_SOURCE_UNSPECIFIED = 0;
    DATA_SOURCE_LOCAL_CACHE = 1;  // 本地缓存
    DATA_SOURCE_MONGODB = 2;  // MongoDB（运营后台请求用这个枚举类型）
  }

  // 生效状态
  enum ActiveStatus {
    ACTIVE_STATUS_UNSPECIFIED = 0;  // 没有指定（获取所有状态的数据）
    ACTIVE_STATUS_ACTIVE = 1;  // 有效
  }

  DataSource data_source = 1;  // 数据源
  ActiveStatus active_status = 2;  // 生效状态
  TrifleVisualStyle.Platform platform = 3;  // 平台
}

message GetTrifleVisualStyleListResponse {
  repeated TrifleVisualStyle trifle_visual_style_list = 1;  // 谜之小事视觉风格配置列表
}
