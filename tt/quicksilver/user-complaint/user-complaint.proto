syntax = "proto3";

package user_complaint;

option go_package = "golang.52tt.com/protocol/services/user-complaint";

service UserComplaint {
  rpc GetUserComplaintEntry (GetUserComplaintEntryReq) returns (GetUserComplaintEntryResp) {}
  //1. 查看我的投诉
  rpc GetUserComplaintList (GetUserComplaintListReq) returns (GetUserComplaintListResp) {}
  //2. 用户提交投诉表单
  rpc CreateComplaint (CreateComplaintReq) returns (CreateComplaintResp) {}
  //3. 公会侧获取投诉列表
  rpc GetGuildComplaintList (GetGuildComplaintListReq) returns (GetGuildComplaintListResp) {}
  //4. 公会侧提交反馈表单
  rpc SubmitGuildComplaint (SubmitGuildComplaintReq) returns (SubmitGuildComplaintResp) {}

  rpc GetUserComplaintByRecordId (GetUserComplaintByRecordIdReq) returns (GetUserComplaintByRecordIdResp) {}

  // 归档 、废弃
  rpc ArchiveUserComplaint (ArchiveUserComplaintReq) returns (ArchiveUserComplaintResp) {}



  /*运营后台接口*/
  // 查询投诉单
  rpc ListUserComplaint (ListUserComplaintReq) returns (ListUserComplaintResp) {}

  // 获取投诉单详情
  rpc GetUserComplaintInfo (GetUserComplaintInfoReq) returns (GetUserComplaintInfoResp) {}

  // 批量通知用户
  rpc BatchNotifyUsers (BatchNotifyUsersReq) returns (BatchNotifyUsersResp) {}

  // 批量发起归档
  rpc BatchApplyUserComplaint (BatchApplyUserComplaintReq) returns (BatchApplyUserComplaintResp) {}

  // 批量处理归档
  rpc BatchHandleUserComplaint (BatchHandleUserComplaintReq) returns (BatchHandleUserComplaintResp) {}

  // 编辑 业务运营处理说明
  rpc UpdateOpMsg (UpdateOpMsgReq) returns (UpdateOpMsgResp) {}

  // 编辑 备注
  rpc UpdateRemark (UpdateRemarkReq) returns (UpdateRemarkResp) {}
}


// 投诉单归档处理
// buf:lint:ignore ENUM_PASCAL_CASE
enum HANDLE_TYPE {
  HANDLE_TYPE_AGREE_ACCEPT  = 0; // 同意
  HANDLE_TYPE_REJECT        = 1; // 退回
}

// 业务类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum BUSINESS_TYPE {
  BUSINESS_TYPE_FUN     = 0; //娱乐 【多人互动】
  BUSINESS_TYPE_LIVE    = 1; //语音直播
  BUSINESS_TYPE_OTHER   = 2; //其他
}

// 工单状态
// buf:lint:ignore ENUM_PASCAL_CASE
enum COMPLAINT_OPR {
  COMPLAINT_OPR_NONE    = 0; // 未归档
  COMPLAINT_OPR_HANDING = 1; // 审批中
  COMPLAINT_OPR_DONE    = 2; // 已归档
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum QUERY_TYPE {
  QUERY_TYPE_TTID = 0;          // TTID
  QUERY_TYPE_GUILD_SHORTID = 1; // 公会靓号
  QUERY_TYPE_GUILD_ID = 2;      // 公会长号
  QUERY_TYPE_ORDER_ID = 3;      // 编号
}

// 用户投诉类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum COMPLAINT_TYPE {
  COMPLAINT_TYPE_EMPTY    = 0;
  COMPLAINT_TYPE_SPITE    = 1; //1 恶意滋事
  COMPLAINT_TYPE_TRADE    = 2; //2 交易纠纷
  COMPLAINT_TYPE_VIOLATE  = 3; //3 内容违规 
  COMPLAINT_TYPE_HANGUP   = 4; //4 管理行为违规
  COMPLAINT_TYPE_OTHER    = 5; //5 其他
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum PAY_TYPE {
  PAY_TYPE_EMPTY  = 0;
  PAY_TYPE_GIVE   = 1; //1 礼物打赏
  PAY_TYPE_WECHAT = 2; //2 微信
  PAY_TYPE_QQ     = 3; //3 QQ
  PAY_TYPE_ALIPAY = 4; //4 支付宝Alipay
}

// 违规内容
// buf:lint:ignore ENUM_PASCAL_CASE
enum ILLEGAL_TYPE {
  ILLEGAL_TYPE_EMPTY     = 0;
  ILLEGAL_TYPE_PERFORM   = 1; //1 表演违规内容
  ILLEGAL_TYPE_TOPIC     = 2; //2 播放平台禁歌
  ILLEGAL_TYPE_OTHER     = 3; //3 其他
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum COMPLAINT_STATUS_TYPE {
  COMPLAINT_STATUS_TYPE_UNDO = 0;     // 0-公会待处理
  COMPLAINT_STATUS_TYPE_DONE = 1;     // 1-公会已处理
  COMPLAINT_STATUS_TYPE_ARCHIVE = 2;  // 2-已归档 、废弃
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetUserComplaintEntryReq {
    uint32 uid = 1;
    uint32 guild_display_id = 2;
    uint32 anchor_uid = 3;
    string channel_name = 4; // 房间名称
    uint32 terminalType = 5;
}

message GetUserComplaintEntryResp {
  string jump_url = 1;
}

message ComplaintInfo {
  uint32 record_id            = 1; // 投诉单 id
  string order_id             = 2; // 编号
  uint32 type                 = 3; // 投诉类型 COMPLAINT_TYPE
  uint32 business_type        = 4; // 业务类型 BUSINESS_TYPE
  uint32 op_status            = 5; // 工单状态 COMPLAINT_OPR
  uint32 guild_status         = 6; // 公会处理状态 COMPLAINT_STATUS_TYPE

  uint32 uid                  = 7;  // 投诉人uid
  string ttid                 = 8;  // 投诉人ttid
  string nickname             = 9;  // 投诉人昵称
  string account              = 10; // 投诉人账号
  
  uint32 guild_display_id     = 11; // 公会靓号id 
  uint32 guild_id             = 12; // 公会长号id 
  string channel_name         = 13; // 房间名称
  uint32 occurrence_time      = 14; // 发生时间
  string target_ttid          = 15; // 对方ttid
  string target_nickname      = 16; // 对方昵称
  uint32 money                = 17; // 金额
  uint32 illegal_type         = 18; // 违规内容 ILLEGAL_TYPE
  string phone                = 19; // 联系方式
  string tips                 = 20; // 问题描述
  repeated string pic_list    = 21; // 相关截图、音频、视频。。。。url

  uint32 create_time          = 22; // 投诉单 创建时间


  string feedback             = 23; // 公会反馈
  uint32 handler_time         = 24; // 公会处理时间

  bool  is_notify             = 25; // 是否已通知用户

  string              op_msg            = 26; // 业务运营处理说明
  string              remark            = 27; // 备注
  repeated RecordInfo record_info_list  = 28; // 操作记录 按时间降序

  string              archive_handler   = 29; // 归档操作人 
  uint32              archive_time      = 30; // 归档时间

  uint32 update_time = 31; 

  repeated MediaInfo media_list    = 32; // 
}


message MediaInfo {
  string name = 1;
  string url = 2;
}

// 操作类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum OP_TYPE {
  OP_TYPE_NOTIFY        = 0; //通知用户
  OP_TYPE_REMARK        = 1; //填写备注
  OP_TYPE_APPLY_ARCHIVE = 2; //提交归档
  OP_TYPE_REJECT_ARCHIVE = 3; //退回归档
}
message RecordInfo {
  uint32 op_type = 1; // OP_TYPE
  string handler = 2; // 操作人 账号
  uint32 op_time = 3; 
}


message GetUserComplaintListReq {
  uint32 uid = 1;
  uint32 page = 2;
  uint32 page_num = 3;
  bool all_status = 4;
}
message GetUserComplaintListResp {
  uint32 total = 1;
  repeated ComplaintInfo list = 2;
}

message CreateComplaintReq {
  ComplaintInfo info = 1;
}
message CreateComplaintResp {}

message GetGuildComplaintListReq {
  uint32 guild_id = 1;
  uint32 page = 2;
  uint32 page_num = 3;
  uint32 status = 4;
}

message GetGuildComplaintListResp {
  uint32 total = 1;
  repeated ComplaintInfo list = 2;
}

message SubmitGuildComplaintReq {
  uint32 record_id = 1;
  string feedback = 2;
}

message SubmitGuildComplaintResp {}


message ArchiveUserComplaintReq {
  uint32 record_id = 1;
  uint32 create_time = 2; 
  string ttid = 3;
}

message ArchiveUserComplaintResp {
}

message GetUserComplaintByRecordIdReq {
  uint32 record_id = 1;
}
message GetUserComplaintByRecordIdResp {
  ComplaintInfo info = 1;
}

/*运营后台接口*/

message ListUserComplaintReq {
  uint32          business_type   = 1; // 业务类型 BUSINESS_TYPE
  uint32          querys_type     = 2; // 查询方式 QUERY_TYPE
  uint32          op_status       = 3; // 工单状态 COMPLAINT_OPR
  repeated uint64 ids             = 4;
  uint32          page            = 5; // 第一页从0开始
  uint32          page_num        = 6;

  repeated string order_ids       = 7;
}
message ListUserComplaintResp {
  uint32    total               = 1;
  repeated  ComplaintInfo list  = 2;
}

message GetUserComplaintInfoReq{
  uint32 record_id            = 1; // 投诉单 id
}
message GetUserComplaintInfoResp {
  ComplaintInfo info = 1;
}

// 批量处理归档
message BatchHandleUserComplaintReq {
  uint32          handle_type = 1; // HANDLE_TYPE
  repeated uint32 record_ids  = 2; // 投诉单 id
  string          handler     = 3; // 操作人
}
message BatchHandleUserComplaintResp {
  uint32            total           = 1;
  uint32            fail_cnt        = 2;
  repeated  string  fail_order_ids  = 3;
}

// 批量发起归档
message BatchApplyUserComplaintReq {
  repeated uint32 record_ids  = 1; // 投诉单 id
  string          handler     = 2; // 操作人
}
message BatchApplyUserComplaintResp {
  uint32            total           = 1;
  uint32            fail_cnt        = 2;
  repeated  string  fail_order_ids  = 3;
}

// 批量通知用户
message BatchNotifyUsersReq {
  repeated uint32 record_ids  = 1; // 投诉单 id
  string          op_msg      = 2;
  string          handler     = 3; // 操作人
}
message BatchNotifyUsersResp {
  uint32            total           = 1;
  uint32            fail_cnt        = 2;
  repeated  string  fail_order_ids  = 3;
}


// 编辑 业务运营处理说明
message UpdateOpMsgReq {
  uint32 record_id  = 1; // 投诉单 id
  string op_msg     = 2; // 业务运营处理说明
}
message UpdateOpMsgResp {
}


// 编辑 备注
message UpdateRemarkReq {
  uint32 record_id  = 1; // 投诉单 id
  string handler    = 2; // 操作人 账号
  string remark     = 3; // 备注
}
message UpdateRemarkResp {
}