syntax = "proto3";

// 新版礼物墙
package ga.present_wall_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/present_wall_logic";

// 获取用户的礼物墙信息
message GetUserPresentWallRequest
{
  BaseReq base_req = 1;
  uint32 target_uid = 2;
  WallType wall_type = 3;
}

// 礼物墙信息类型
enum WallType{
  WALL_TYPE_UNSPECIFIED = 0;
  WALL_TYPE_90DAY = 1;
  WALL_TYPE_ALL = 2;
}

// 礼物墙项目类型
enum WallItemType{
  WALL_ITEM_TYPE_UNSPECIFIED = 0;
  WALL_ITEM_TYPE_GIFT = 1; // 礼物
  WALL_ITEM_TYPE_EMPEROR_SET = 2; // 帝王套
}

message GetUserPresentWallResponse
{
  BaseResp base_resp = 1;
  repeated PresentWallItem present_wall_item_list = 2;
  bool present_wall_switch = 3; // true - 正常显示新版礼物墙 ； false - 请求旧版礼物墙
}

message PresentWallItem{
  uint32 item_id = 1;
  uint32 item_count = 2;
  PresentNamingUser naming_user = 3;
  WallItemType item_type = 4; // 礼物墙项目类型
}

// 获取用户的礼物墙信息
message GetUserPresentWallMissingItemsRequest
{
  BaseReq base_req = 1;
  uint32 target_uid = 2;
  MissingWallType wall_type = 3;
}

// 礼物墙信息类型
enum MissingWallType{
  MISSING_WALL_TYPE_UNSPECIFIED = 0;
  MISSING_WALL_TYPE_90DAY = 1;
  MISSING_WALL_TYPE_ALL = 2;
}

message GetUserPresentWallMissingItemsResponse
{
  BaseResp base_resp = 1;
  repeated MissingPresentWallItem present_wall_item_list = 2;
}

message MissingPresentWallItem{
  uint32 item_id = 1;
  string source_text = 2;
  WallItemType item_type = 3; // 礼物墙项目类型
}

// 礼物冠名用户信息
message PresentNamingUser {
  UserProfile naming_user = 1; // 若无冠名主，返回为空，若为拉黑、封禁、注销，则昵称返回“--”，除昵称之外返回空，其余情况正常返回
  uint32 send_num = 2; // 送礼物数量
  bool is_black_user = 3; // 是否是黑名单用户
}

message GetPresentNamingInfoRequest {
  BaseReq base_req = 1;
  uint32 target_uid = 2; // 查询用户uid
  uint32 item_id = 3; // 礼物id
  WallItemType item_type = 4; // 礼物墙项目类型
}

message GetPresentNamingInfoResponse {
  BaseResp base_resp = 1;
  PresentNamingUser naming_present_user = 2; // 礼物冠名用户信息
  string how_to_naming_line_one = 3; // 如何冠名文案说明第一行，不可换行，可为空
  string how_to_naming_line_two = 4; // 如何冠名文案说明第二行，不可换行，可为空
  bool is_naming_user = 5; // 是否是冠名礼物的用户
  string giving_details = 6; // 赠送详情文案，该字段为带有HTML元素的字符串，客户端需要解析
}

// 冠名礼物房间推送信息
message PresentNamingMsg {
  string present_name = 1; // 礼物名称/帝王套名称
  string present_img = 2; // 礼物图片/帝王套图片
  UserProfile naming_user = 3; // 冠名用户信息
  UserProfile present_recipient = 4; // 被送礼用户信息
  bool only_master = 5; // 是否只有主态可以展示
}

message GetPresentWallSwitchRequest{
  BaseReq base_req = 1;
}

message GetPresentWallSwitchResponse {
  BaseResp base_resp = 1;
  bool present_wall_switch = 2; // true - 正常显示新版礼物墙 ； false - 请求旧版礼物墙
  bool present_illustration_switch = 3; // true -正常显示礼物图鉴; false -不展示礼物图鉴
}

message PresentWallSwitchChange {
  bool present_wall_switch = 1; // true - 正常显示新版礼物墙 ； false - 请求旧版礼物墙
}

message SetUserNamingSwitchRequest {
  BaseReq base_req = 1;
  bool is_close = 2; // true - 关闭冠名功能 ； false - 开启冠名功能
}

message SetUserNamingSwitchResponse {
  BaseResp base_resp = 1;
}

message GetUserNamingSwitchRequest {
  BaseReq base_req = 1;
}

message GetUserNamingSwitchResponse {
  BaseResp base_resp = 1;
  bool is_close = 2; // true - 关闭冠名功能 ； false - 开启冠名功能
}