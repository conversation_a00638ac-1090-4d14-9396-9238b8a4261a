syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";		

package session;									

// 专用错误码
enum ERR_CODE
{
    ERR_OK = 0;
    ERR_SYSTEM = -2;
    ERR_SESSION_NOT_EXIST = -1801;
    ERR_SESSION_EXPIRED = -1802;
}

message SessionInfo {
    required uint32 uid = 1;        // uid
    required uint32 app_id = 2;
    required uint32 expired_at = 3; // expired time
    required string session_key = 4;// session key. 32bytes.
}

message CreateSessionReq {
    required uint32 uid = 1;
    required uint32 app_id = 2;
}

message CreateSessionResp {
    optional SessionInfo session = 1;
}

message GetSessionReq {
    required uint32 uid = 1;
    required uint32 app_id = 2;
}

message GetSessionResp {
    optional int32 error_code = 1; //ERR_CODE
    optional SessionInfo session = 2;
}

message RemoveSessionReq {
    required uint32 uid = 1;
    required uint32 app_id = 2;
}

message RemoveSessionResp {
}

message RemoveMultiSessionReq {
    required uint32 uid = 1;
    // include 和 exclude 二选一, 如果都为空，删除所有
    repeated uint32 include_app_id = 2; 
    repeated uint32 exclude_app_id = 3;
}
message RemoveMultiSessionResp {
}


service Session {
    option( tlvpickle.Magic ) = 12006;		// 服务监听端口号

    rpc CreateSession(CreateSessionReq) returns(CreateSessionResp){
    	option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:p:";
        option( tlvpickle.Usage ) = "-u <uid> [-p <app_id>]";	
    }
    rpc GetSession( GetSessionReq ) returns(GetSessionResp) {
    	option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:p:";
        option( tlvpickle.Usage ) = "-u <uid> [-p <app_id>]";	
    }
    rpc RemoveSession(RemoveSessionReq) returns(RemoveSessionResp) {
    	option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:p:";
        option( tlvpickle.Usage ) = "-u <uid> [-p <app_id>]";	
    }
    rpc RemoveMultiSession(RemoveMultiSessionReq) returns (RemoveMultiSessionResp) {
    	option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "u:i:x:";
        option( tlvpickle.Usage ) = "-u <uid> [-i <include_app_id>] [-x <exclude_app_id>]";	
    }

}

//内部存储
message SessionModel{
    required uint32 uid = 1;
    required uint32 app_id = 2;
    required uint32 expired_at = 3;	// expired time
    required string session_key = 4;		// session key. 32bytes.    
}
