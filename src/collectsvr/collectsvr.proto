syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package collectsvr;


message CollectDeviceIdReq {
	enum StatisDeviceIDType{
	INSTALL_TT = 1;
	LOGIN_TT = 2;
	}
	required bytes device_id = 1;
	required uint32 statis_type = 2;
}

message CollectDeviceIdResp {
}

message StatisticDeviceIdReq {
}

message StatisticDeviceIdResp {
	required uint32 device_count = 1;
	required uint32 login_count = 2;
}


service CollectSvr{
    option( tlvpickle.Magic ) = 15621;

  
	rpc CollectDeviceId( CollectDeviceIdReq ) returns( CollectDeviceIdResp ) {
		option( tlvpickle.CmdID ) = 1;								// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";								// 测试工具的命令号帮助
	}

	rpc StatisticDeviceId( StatisticDeviceIdReq ) returns( StatisticDeviceIdResp ) {
		option( tlvpickle.CmdID ) = 2;								// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";								// 测试工具的命令号帮助
	}
}
