syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";


package newbie;



message CheckDevRegCountReq
{
	required string dev_info_hex = 1;
}

message CheckDevRegCountResp			
{
	required uint32 count=1;           
}


message CheckUserRegDevReq
{
	required uint32 uid=1; 
}

message CheckUserRegDevResp			
{
	required string dev_hex = 1;  // 用户注册的设备id
	required uint32 dev_count=2;  // 用户注册的时候 该设备ID被注册过几次
}

message UpdateUserRegDevReq
{
	required uint32 uid=1; 

	optional string dev_hex = 4;  // 用户注册的设备id
	optional uint32 dev_count=5;  // 用户注册的时候 该设备ID被注册过几次
}




message UpdateUserRegDevResp			
{


}
message UserFirstLoginInfo
{
	required uint32 uid = 1; 

	optional uint32 app_id    = 2; 
	optional uint32 market_id = 3; 
	optional string dev_hex   = 4;       // 用户注册的设备id
	optional uint32 dev_count = 5;       // 用户注册的时候 该设备ID被注册过几次
	optional uint32 first_login_ts = 6;  // 用户注册的时候 该设备ID被注册过几次
}
message CheckUserFirstLoginTimeReq
{
	required uint32 uid=1; 
}
message CheckUserFirstLoginTimeResp
{
	repeated UserFirstLoginInfo first_login_list = 1;  
}

//用户注册时的渠道包信息
message UserPkgInfo
{
    required uint32 uid = 1;
    required string channel_pkg = 2;
}

//用户注册时选的游戏标签信息
message UserGameInfo
{
    required uint32 uid = 1;
    required string game_name = 2;
}

//批量获取用户注册渠道包和标签信息
message BatchGetUserRegPkgGameInfoReq 
{
    repeated uint32 uid_list = 1;
}

message BatchGetUserRegPkgGameInfoResp
{ 
    repeated UserPkgInfo channel_pkg_list = 1;
    repeated UserGameInfo game_name_list = 2;
}


service newbie
{
	option( tlvpickle.Magic ) = 15231;

	// 查询设备信息
	rpc CheckDevRegCount(CheckDevRegCountReq)returns( CheckDevRegCountResp){
		option( tlvpickle.CmdID ) = 1;						
	    option( tlvpickle.OptString ) = "x:";				
	    option( tlvpickle.Usage ) = "-x<dev_info_hex> ";	
	}
	
	// 用户注册信息
	rpc CheckUserRegDev(CheckUserRegDevReq)returns( CheckUserRegDevResp){
		option( tlvpickle.CmdID ) = 2;						
	    option( tlvpickle.OptString ) = "u:";				
	    option( tlvpickle.Usage ) = "-u<uid> ";	
	}

	// 用户首次登录信息
	rpc CheckUserFirstLoginTime(CheckUserFirstLoginTimeReq)returns( CheckUserFirstLoginTimeResp){
		option( tlvpickle.CmdID ) = 3;						
	    option( tlvpickle.OptString ) = "u:";				
	    option( tlvpickle.Usage ) = "-u<uid> ";	
	}

	// 修改用户注册的设备信息
	rpc UpdateUserRegDev(UpdateUserRegDevReq)returns( UpdateUserRegDevResp){
		option( tlvpickle.CmdID ) = 10;						
	    option( tlvpickle.OptString ) = "u:x:n:";				
	    option( tlvpickle.Usage ) = "-u<uid> -x<dev_info_hex> -n<dev count> ";	
	}

	// 批量获取用户注册时的渠道包和游戏标签信息
	rpc BatchGetUserRegPkgGameInfo( BatchGetUserRegPkgGameInfoReq )returns( BatchGetUserRegPkgGameInfoResp ){
		option( tlvpickle.CmdID ) = 12;						
	    option( tlvpickle.OptString ) = "x:";				
	    option( tlvpickle.Usage ) = "-x<uidlist uid1 uid2>";	
	}
}
