syntax = "proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package HeadImage;

// 头像上传来源
enum UplaodSource {
  UPLOAD_SOURCE_DEFAULT = 0;
  UPLOAD_SOURCE_CMD = 1;              // CMD_UserUploadFace
  UPLOAD_SOURCE_HTTP = 2;
  UPLOAD_SOURCE_THIRD_PARTY_REG = 3;
  UPLOAD_SOURCE_ANCHOR_LIVE_BEGIN = 4; // 主播开播头像送审
  UPLOAD_SOURCE_SILENT_USER = 5;
}

message SaveHeadImageReq {
  required string account = 2;
  required bytes big_face = 3;
  required bytes small_face = 4;
  required string md5 = 5;
}

message AccountReq {
  required string account = 1;
}

message GetHeadImageMd5Resp {
  required string md5 = 1;
}

message GetHeadImageBigFaceResp {
  required bytes big_face = 1;
}

message GetHeadImageSmallFaceResp {
  required bytes small_face = 1;
}

message GetBatchHeadImageMd5Req {
  repeated string accounts = 1;
}

message HeadImageMd5 {
  required string account = 1;
  required string md5 = 2;
}

message GetBatchHeadImageMd5Resp {
  repeated HeadImageMd5 md5_list = 1;
}

message UpdateHeadImageMd5Resp {

}

service HeadImage {
  option (tlvpickle.Magic) = 14003;  // 服务监听端口号


  rpc GetHeadImageMd5(AccountReq) returns (GetHeadImageMd5Resp) {
    option (tlvpickle.CmdID) = 2;  
    option (tlvpickle.OptString) =
        "a:";  
    option (tlvpickle.Usage) = "-a <account>";
  }

  rpc GetBatchHeadImageMd5(GetBatchHeadImageMd5Req)
      returns (GetBatchHeadImageMd5Resp) {
    option (tlvpickle.CmdID) = 5;  
    option (tlvpickle.OptString) =
        "a:m:";  
    option (tlvpickle.Usage) = "-a <account> -m <md5>";
  }

  rpc UpdateHeadImageMd5(HeadImageMd5) returns (UpdateHeadImageMd5Resp) {
      option (tlvpickle.CmdID) = 6; 
      option (tlvpickle.OptString) =
          "a:"; 
      option (tlvpickle.Usage) = "-a <account>";
  }

  // deprecated, @see avatar.proto
  rpc SaveHeadImage(SaveHeadImageReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 1;  
    option (tlvpickle.OptString) =
        "a:i:";  
    option (tlvpickle.Usage) =
        "-a <account> -i <file path>";  
  }

  // deprecated, @see avatar.proto
  rpc GetHeadImageBigFace(AccountReq) returns (GetHeadImageBigFaceResp) {
    option (tlvpickle.CmdID) = 3;  
    option (tlvpickle.OptString) =
        "a:";  
    option (tlvpickle.Usage) = "-a <account>";
  }

  // deprecated, @see avatar.proto
  rpc GetHeadImageSmallFace(AccountReq) returns (GetHeadImageSmallFaceResp) {
    option (tlvpickle.CmdID) = 4;  
    option (tlvpickle.OptString) =
        "a:";  
    option (tlvpickle.Usage) = "-a <account>";
  }
}

