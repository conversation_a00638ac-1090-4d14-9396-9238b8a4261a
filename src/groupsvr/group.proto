syntax="proto2";

// import
import "common/tlvpickle/skbuiltintype.proto";
package Group;

message AddBulletinReq{
	required string title = 1;
	required string content = 2;
	required uint32 group_id = 3;
	required uint32 seq_id = 4;
	required string author = 5;
}

message AddBulletinResp {
	required uint32 bulletin_id = 1;
	required uint32 op_time = 2;
}

message DeleteBulletinReq{
	required uint32 group_id = 1;
	required uint32 bulletin_id = 2;
}


message BatchDelBulletinReq {
	required uint32 group_id = 1;
	repeated uint32 bulletin_ids = 2;
}

message GetBulletinReq {
	required uint32 group_id = 1;
}

message GroupBulletinInfo {
	required string title = 1;
	required string content = 2;
	required uint32 op_time = 3;
	required uint32 bulletin_id = 4;
	required uint32 seq_id = 5;
	required string author = 6;
}

message GetBulletinResp {
	repeated GroupBulletinInfo bulletin_info_list = 1;
}

message UpdateBulletinSeqReq {
	required uint32 data_type = 1;
	required uint32 group_id = 2;
	required bytes data = 3;
}

message GetBulletinSeqReq {
	required uint32 data_type = 1;
	required uint32 group_id = 2;
}

message GetBulletinSeqResp {
	required string data = 1;
}

message GroupMemberKickedRecord {
    required uint32 kicked_user_id = 1;
    required uint32 group_id = 2;
    required string group_account = 3;
    required uint32 operator_user_id = 4;
}

message RecordUserKickedReq {
    required GroupMemberKickedRecord record = 1;
}

message GetUserKickedRecordsReq {
    required uint32 uid = 1;
}

message GetUserKickedRecordsResp {
    repeated GroupMemberKickedRecord group_kick_record_list = 1;
}

message GroupAdminChangeInfo{
    required uint32 uid         = 1;
    required uint32 is_admin    = 2;
    optional string account     = 3;
}

message UpdateGroupAdminChangeTimelineReq
{
    required uint32 group_id    = 1;
    required uint32 seq_id      = 2;
    required GroupAdminChangeInfo  data = 3;
}

message UpdateGroupAdminChangeTimelineResp
{
}

message GetGroupAdminChangeTimelineReq
{
    required uint32 group_id        = 1;
    required uint32 seq_id_start    = 2;
    required uint32 seq_size        = 3;
}

message GetGroupAdminChangeTimelineResp
{
    repeated uint32 seq_id_list             = 1;
    repeated GroupAdminChangeInfo data_list = 2;
}


//.
service Group{
    option( tlvpickle.Magic ) = 15290;

    rpc AddBulletin(AddBulletinReq)returns(AddBulletinResp) {
        option( tlvpickle.CmdID ) = 1;
	    option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

	rpc DeleteBulletin ( DeleteBulletinReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 2;
	    option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

	rpc GetBulletin ( GetBulletinReq ) returns ( GetBulletinResp ) {
        option( tlvpickle.CmdID ) = 3;
	    option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

	rpc UpdateBulletinSeq ( UpdateBulletinSeqReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 4;
	    option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

	rpc GetBulletinSeq ( GetBulletinSeqReq ) returns ( GetBulletinSeqResp ) {
        option( tlvpickle.CmdID ) = 5;
	    option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc RecordUserKicked ( RecordUserKickedReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "u:g:a:o:";
        option( tlvpickle.Usage ) = "-u <uid_kicked> -g <group_id> -a <group_account> -o <uid_operator>";
    }

    rpc GetUserKickedRecords ( GetUserKickedRecordsReq ) returns ( GetUserKickedRecordsResp ) {
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc UpdateGroupAdminChangeTimeline(UpdateGroupAdminChangeTimelineReq) returns (UpdateGroupAdminChangeTimelineResp) {
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "g:s:u:m:";
        option( tlvpickle.Usage ) = "-g <group_id> -s <seq_id> -u <uid> -m <is_admin>";        
    }

    rpc GetGroupAdminChangeTimeline(GetGroupAdminChangeTimelineReq) returns (GetGroupAdminChangeTimelineResp) {
            option( tlvpickle.CmdID ) = 13;
            option( tlvpickle.OptString ) = "g:s:";
            option( tlvpickle.Usage ) = "-g <group_id> -s <seq_id>";        
    }    

	rpc BatchDelBulletin ( BatchDelBulletinReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 14;
	    option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
}
