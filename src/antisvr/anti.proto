syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Anti;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list


enum LOGIN_OP_TYPE {
  LOGIN_OP_Nil = 0;
  LOGIN_OP_Reg = 1;		     // 注册
  LOGIN_OP_Manual = 2;		     // 手工登录
  LOGIN_OP_AutoLogin = 3;	     // 自动登录
  LOGIN_OP_SdkActivate = 4;    // SDK激活
  LOGIN_OP_IOSAvtivate = 5;   // tt ios 激活
}

message UserLoginInfo {
  enum OP_TYPE {  //废弃，改为使用 LOGIN_OP_TYPE
    REG = 1;		     // 注册
    MANUAL = 2;		     // 手工登录
    AUTO_LOGIN = 3;	     // 自动登录
    SDK_ACTIVATE = 4;    // SDK激活
  }
  required uint32 uid = 1;
  required uint32 op_type = 2;    //改为使用 LOGIN_OP_TYPE
  required int32 result = 3;					// 操作结果
  required string phone = 4;					// 手机号
  optional uint32 third_party_type = 5;		// 第三方账号类型
  optional string openid = 6;					// openid

  optional string imei = 10;
  optional string os_ver = 11;				// 操作系统版本
  optional string os_type = 12;				// 操作系统类型
  optional string device_model = 13;			// 机器型号
  optional string signature = 14;				// 签名
  optional string device_info = 15;			// 设备信息
  optional uint32 is_emulator = 16;			// 是否模拟器
  optional string device_id = 17;				// 设备号
  optional uint32 client_ver = 18;			// 客户端版本类型
  optional string clientIp = 19;				// 客户端ip
  optional string client_channel_id = 20;     // 客户端渠道号

  optional uint32 client_type = 21;           // 客户端类型(仅限TT -- 0: Android, 1: iOS)
  optional uint32 terminal_type = 22;         // 终端类型(包含平台/操作系统/APPID)

  optional bool is_quick_login_from_sdk = 23; // 是否从SDK快速登录
  optional string login_time = 24; //没用?
  optional int32 client_port = 25;
  optional string idfa = 26;  //IOS IDFA
  optional string login_account = 27; //登录用账号:phone/ttid
  optional string username = 28;
  optional string alias = 29;
  optional string nickname = 30;
  optional string user_signature = 31; //个性签名
  optional string proxy_ip = 32;
  optional uint32 proxy_port = 33;
  optional uint32 client_id = 34;
}

message RecordUserLoginReq {
  required UserLoginInfo info = 1;
  optional uint32 invalid = 2;				// 是否同个机器注册的。如果是则invalid = 1
}

message RecordUserLoginResp {
  optional bool is_new_usual_device = 1;	// 新的常用设备
}

//message UpdateUserLoginReq {
//	required string device_id = 1;				// 设备号
//}
//
//message UpdateUserLoginResp {
//}
//

message RegUserInfo {
  required uint32 uid = 1;
  required string phone = 2;
  required string reg_at = 3;
}

message GetDeviceIdInfoReq {
  required string device_id = 1;				// hex
}

message GetDeviceIdInfoResp {
  required uint32 reg_user_count = 1;			// 注册用户的数量
  repeated RegUserInfo reg_info_list = 2;		// 注册用户列表
}

message GetUserLastLoginInfoReq {
  required uint32 uid = 1;
}

message BatchGetUserLastLoginInfoReq {
  repeated uint32 uid_list = 1;
}

message BatchGetUserLastLoginInfoResp {
  repeated UserLoginInfo info_list = 1;
}

//登录设备信息
message LoginDeviceInfo {
  required string device_id = 1;				// 设备号
  required uint32 last_login_time = 2;			// 上次登录时间
}

message RecentLoginDeviceList {
  required uint32 uid = 1;
  repeated LoginDeviceInfo device_list = 2;
}

//登录用户信息
message LoginUserInfo {
  required uint32 uid = 1;
  required uint32 last_login_time = 2;			// 上次登录时间
}

message RecentLoginUserList {
  required string device_id = 1;
  repeated LoginUserInfo user_list = 2;
}

// 图片验证成功
message VerifyCAPTCHASuccessReq {
  required uint32 uid	= 1;
  required uint32 verify_reason = 2;	//验证原因：1登录
  required string device_id = 3;	//设备号
}

message VerifyCAPTCHASuccessResp {
}

// 获取上次验证成功的信息
message GetLastVerifySuccessInfoReq {
  required uint32 uid	= 1;
  required uint32 verify_reason = 2;	//验证原因：1登录
}

message GetLastVerifySuccessInfoResp {
  required string device_id = 1;	//设备号
  required uint32 timestamp = 2;
}

enum UnusualDeviceCheckType {
  Unual_Device_Check_Captcha = 0;	// 图片验证
  Unual_Device_Check_Sms = 1;		// 短信验证
}

// 检验是否用户常用设备
message CheckUsualDeviceReq {
  required uint32 uid	= 1;
  required string device_id = 2;	//设备号
  optional uint32 type = 3;		// UnusualDeviceCheckType
}

message CheckUsualDeviceResp {
  required bool is_usual_device = 1;
}

message RecordUsualDeviceReq {
    optional uint32 uid = 1;
    optional string device_id = 2;
}

message RecordUsualDeviceResp {
}

// 检验用户是否是合法注册
message CheckUserIsValidReq
{
  required uint32 uid	= 1;
}

message CheckUserIsValidResp {
  required bool is_valid = 1; // 是否合法
}

message GetUserLoginHistoryReq{
  required uint32 uid = 1;
  optional int64 begin_time = 2;
  optional int64 end_time = 3;
}

message GetUserLoginHistoryResp{
  repeated UserLoginInfo login_history = 1;
}

// 用户profile

enum USER_PROFILE{
  USER_PROFILE_NORMAL = 0; //大号
  USER_PROFILE_FAKE = 1;   //小号
  USER_PROFILE_SUSPICOUS = 2;  //可疑账号
};


message SetUserProfileReq{
  required uint32 uid	= 1;
  required int32 profile	= 2;
  required uint32 reason_code	= 3;
  required string reason	= 4;
}

message SetUserProfileRsp{
}

message GetUserProfileReq{
  required uint32 uid	= 1;
  optional bool with_footmark = 2; //废弃
  optional bool with_detail = 3;
}

message UserProfileDetail{
  required int32 profile = 1;
  required string mark_time = 2;
  required uint32 reason_code = 3;
  required string reason = 4;
  required uint32 status = 5;
}

message GetUserProfileRsp{
  optional uint32 uid	= 1;
  optional int32 profile	= 2;
  repeated UserProfileDetail profile_list = 3; //废弃
  optional uint32 reason_code = 4;
  optional string reason = 5;
}

// 记录 login 信息

message TrackUserLoginReq{
  required uint32 uid	= 1;
  required uint32 login_at	= 2;
  optional string device_id	= 3;
  optional string imei = 4;
  optional string ip	= 5;
}

message TrackUserLoginRsp{
}

//用户登录设备
message GetUserLoginDeviceReq{
  required uint32 uid	= 1;
}

message UserLoginDevice{
  required string device_id = 1;
  required uint32 login_at = 2;
}
message GetUserLoginDeviceRsp{
  optional uint32 uid	= 1;
  repeated UserLoginDevice login_devices = 2;
}

//使用同一设备登陆的用户

message UserLoginHit{
  required uint32 uid = 1;
  required uint32 login_at = 2;
}

message GetUserLoginWithDeviceReq{
  required string device_id = 1;
}

message GetUserLoginWithDeviceRsp{
  optional string device_id = 1;
  repeated UserLoginHit login_users = 2;
}

//
message GetUserUsualDeviceReq{
  required uint32 uid	= 1;
  optional uint32 begin_time = 2;
  optional uint32 end_time = 3;
  optional int32 limit = 4;
}

message GetUserUsualDeviceResp{
  optional uint32 uid	= 1;
  repeated UserLoginDevice login_devices = 2;
}


//用户的登陆ip
message GetUserLoginIpReq{
  required uint32 uid	= 1;
}
message UserLoginIp{
  required string ip = 1;
  required uint32 login_at = 2;
}
message GetUserLoginIpRsp{
  optional uint32 uid	= 1;
  repeated UserLoginIp login_ips = 2;
}

//使用同IP登陆的用户
message GetUserLoginWithIPReq{
  required string ip = 1;
}

message GetUserLoginWithIPRsp{
  required string ip = 1;
  repeated UserLoginHit login_users = 2;
}

//imei
message GetUserLoginWithImeiReq{
  required string imei = 1;
}
message GetUserLoginWithImeiRsp{
  required string imei = 1;
  repeated UserLoginHit login_users = 2;
}

message GetUserLoginImeiReq{
  required uint32 uid	= 1;
}

message UserLoginImei{
  required string imei = 1;
  required uint32 login_at = 2;
}

message GetUserLoginImeiRsp{
  optional uint32 uid	= 1;
  repeated UserLoginImei login_imeis = 2;
}

//批量操作
message BatchSetUserProfileReq{
  repeated uint32 uid_list = 1;
  required int32 profile	= 2;
  required uint32 reason_code	= 3;
  required string reason	= 4;
}
message BatchSetUserProfileRsp{
}

message BatchGetUserProfileReq{
  repeated uint32 uid_list	= 1;
  optional bool with_footmark = 2; //reserved
}
message BatchGetUserProfileRsp{
  repeated GetUserProfileRsp user_profile_list = 3;
}

message GetUserLoginByIdfaReq {
  required string idfa = 1;
}
message UserLoginWithIdfa {
  required string idfa = 1;
  required uint32 uid = 2;
  required uint32 at = 3;
  required uint32 op_type = 4;    //LOGIN_OP_TYPE
}
message GetUserLoginByIdfaResp {
  optional UserLoginWithIdfa rec = 1;
}

message RecordIdfaReq {
  required uint32 uid = 1;
  required uint32 op_type = 2;    //LOGIN_OP_TYPE
  required string idfa = 3;
  optional uint32 at = 4;
}
message RecordIdfaResp {
}

message GetUserRegInfoReq {
  required uint32 uid = 1;
}

message GetUserRegInfoRsp {
  optional UserLoginInfo info = 1;
}

message BatchGetUserLoginDeviceReq {
  repeated uint32 uids = 1;
}

message BatchUserLoginDevice {
  required uint32 uid	= 1;
  required string device_id = 2;
  required uint32 login_at = 3;
}

message BatchGetUserLoginDeviceRsp{
  repeated BatchUserLoginDevice login_devices = 2;
}

message BatchGetUserLoginWithDeviceReq {
  repeated string device_ids = 1;
}

message BatchGetUserLoginWithDeviceRsp {
  repeated BatchUserLoginDevice login_users = 1;
}

// Anti服务
service Anti {
  option( tlvpickle.Magic ) = 15061;		// 服务监听端口号

  rpc RecordUserLogin( RecordUserLoginReq ) returns( RecordUserLoginResp ) {
    option( tlvpickle.CmdID ) = 1;              // 命令号
    option( tlvpickle.OptString ) = "u:d:";   // 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-u <uid> -d<device_id>";    // 测试工具的命令号帮助
  }

  rpc GetDeviceIdInfo( GetDeviceIdInfoReq ) returns( GetDeviceIdInfoResp ) {
    option( tlvpickle.CmdID ) = 2;		// 命令号
    option( tlvpickle.OptString ) = ""; // 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
  }

  rpc GetUserLastLoginInfo( GetUserLastLoginInfoReq ) returns( UserLoginInfo ) {
    option( tlvpickle.CmdID ) = 3;			// 命令号
    option( tlvpickle.OptString ) = "u:";   	// 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-u <uid>";    		// 测试工具的命令号帮助
  }

  rpc VerifyCAPTCHASuccess( VerifyCAPTCHASuccessReq ) returns( VerifyCAPTCHASuccessResp ) {
    option( tlvpickle.CmdID ) = 4;			// 命令号
    option( tlvpickle.OptString ) = "u:r:d:";   	// 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-u <uid> -r <verify_reason> -d <device_id>";    		// 测试工具的命令号帮助
  }

  rpc GetLastVerifySuccessInfo( GetLastVerifySuccessInfoReq ) returns( GetLastVerifySuccessInfoResp ) {
    option( tlvpickle.CmdID ) = 5;			// 命令号
    option( tlvpickle.OptString ) = "u:r:";   	// 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-u <uid> -r <verify_reason>";    		// 测试工具的命令号帮助
  }

  rpc CheckUsualDevice( CheckUsualDeviceReq ) returns( CheckUsualDeviceResp ) {
    option( tlvpickle.CmdID ) = 6;			// 命令号
    option( tlvpickle.OptString ) = "u:d:t:";   	// 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-u <uid> -d <device_id> -t <0.captcha 1.sms>";    		// 测试工具的命令号帮助
  }

  rpc CheckUserIsValid( CheckUserIsValidReq ) returns( CheckUserIsValidResp ) {
    option( tlvpickle.CmdID ) = 7;			// 命令号
    option( tlvpickle.OptString ) = "u:";   	// 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-u <uid> ";    		// 测试工具的命令号帮助
  }
  rpc GetUserLoginHistory( GetUserLoginHistoryReq ) returns( GetUserLoginHistoryResp ) {
    option( tlvpickle.CmdID ) = 8;
    option( tlvpickle.OptString ) = "u:b:e:";
    option( tlvpickle.Usage ) = "-u <uid> [-b <begin time>] [-e <end time>]";
  }

  rpc GetUserUsualDevice( GetUserUsualDeviceReq ) returns( GetUserUsualDeviceResp ) {
    option( tlvpickle.CmdID ) = 9;
    option( tlvpickle.OptString ) = "u:b:e:l:";
    option( tlvpickle.Usage ) = "-u <uid> [-b <begin time>] [-e <end time>] [-l <limit>]";
  }

  //
  //2016/10, 2.9.0增加防刷机制
  //
  rpc SetUserProfile ( SetUserProfileReq ) returns ( SetUserProfileRsp ) {
    option( tlvpickle.CmdID ) = 11;
    option( tlvpickle.OptString ) = "u:p:r:s:";
    option( tlvpickle.Usage ) = "-u <uid> -p <profile> [-r <reason_code, default=2> -s <reason_str,default=admin>]";
  }

  rpc GetUserProfile ( GetUserProfileReq ) returns ( GetUserProfileRsp ) {
    option( tlvpickle.CmdID ) = 12;
    option( tlvpickle.OptString ) = "u:d";
    option( tlvpickle.Usage ) = "-u <uid> [-d , with detail]";
  }

  rpc TrackUserLogin ( TrackUserLoginReq ) returns ( TrackUserLoginRsp ) {
    option( tlvpickle.CmdID ) = 13;
    option( tlvpickle.OptString ) = "u:d:h:i:t:";
    option( tlvpickle.Usage ) = "-u <uid> -d <device id> -h <client ip> -i <imei> -t <login_at/date>";
  }

  rpc GetUserLoginDevice ( GetUserLoginDeviceReq ) returns ( GetUserLoginDeviceRsp ) {
    option( tlvpickle.CmdID ) = 14;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc GetUserLoginWithDevice ( GetUserLoginWithDeviceReq ) returns ( GetUserLoginWithDeviceRsp ) {
    option( tlvpickle.CmdID ) = 15;
    option( tlvpickle.OptString ) = "d:";
    option( tlvpickle.Usage ) = "-d <device_id>";
  }

  rpc GetUserLoginWithIP ( GetUserLoginWithIPReq ) returns ( GetUserLoginWithIPRsp ) {
    option( tlvpickle.CmdID ) = 16;
    option( tlvpickle.OptString ) = "h:";
    option( tlvpickle.Usage ) = "-h <ip>";
  }
  rpc GetUserLoginWithImei ( GetUserLoginWithImeiReq ) returns ( GetUserLoginWithImeiRsp ) {
    option( tlvpickle.CmdID ) = 17;
    option( tlvpickle.OptString ) = "i:";
    option( tlvpickle.Usage ) = "-i <imei>";
  }

  rpc GetUserLoginImei ( GetUserLoginImeiReq ) returns ( GetUserLoginImeiRsp ) {
    option( tlvpickle.CmdID ) = 18;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc BatchSetUserProfile ( BatchSetUserProfileReq ) returns ( BatchSetUserProfileRsp ) {
    option( tlvpickle.CmdID ) = 19;
    option( tlvpickle.OptString ) = "u:p:r:s:";
    option( tlvpickle.Usage ) = "-u <uid1,uid2,uid3,...> -p <profile> -r <reason_code> -s <reason_str>";
  }
  rpc BatchGetUserProfile ( BatchGetUserProfileReq ) returns ( BatchGetUserProfileRsp ) {
    option( tlvpickle.CmdID ) = 20;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid1,uid2,uid3,...>";
  }

  // rpc UpdateUserLogin( UpdateUserLoginReq ) returns( UpdateUserLoginResp) {
  //     option( tlvpickle.CmdID ) = 21;
  // 	option( tlvpickle.OptString ) = "u:";
  // 	option( tlvpickle.Usage ) = "-u <uid>";
  // }

  rpc GetUserLoginIp ( GetUserLoginIpReq ) returns ( GetUserLoginIpRsp ) {
    option( tlvpickle.CmdID ) = 22;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }
  rpc GetUserLoginByIdfa( GetUserLoginByIdfaReq ) returns ( GetUserLoginByIdfaResp ) {
    option( tlvpickle.CmdID ) = 23;
    option( tlvpickle.OptString ) = "i:";
    option( tlvpickle.Usage ) = "-i <idfa>";
  }
  rpc RecordIdfa( RecordIdfaReq ) returns ( RecordIdfaResp ) {
    option( tlvpickle.CmdID ) = 24;
    option( tlvpickle.OptString ) = "u:t:i:";
    option( tlvpickle.Usage ) = "-u <uid> -t <op_type:1-reg,2-manual,3-autologin,4-sdkactivate,5-iosactivate> -i <idfa>";
  }

  rpc GetUserRegInfo( GetUserRegInfoReq ) returns ( GetUserRegInfoRsp ) {
    option( tlvpickle.CmdID ) = 25;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc BatchGetUserLastLoginInfo ( BatchGetUserLastLoginInfoReq ) returns ( BatchGetUserLastLoginInfoResp ) {
    option( tlvpickle.CmdID ) = 26;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc BatchGetUserLoginDevice ( BatchGetUserLoginDeviceReq ) returns ( BatchGetUserLoginDeviceRsp ) {
    option( tlvpickle.CmdID ) = 27;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc BatchGetUserLoginWithDevice ( BatchGetUserLoginWithDeviceReq ) returns ( BatchGetUserLoginWithDeviceRsp ) {
    option( tlvpickle.CmdID ) = 28;
    option( tlvpickle.OptString ) = "d:";
    option( tlvpickle.Usage ) = "-d <device_id>";
  }

  rpc RecordUsualDevice ( RecordUsualDeviceReq ) returns ( RecordUsualDeviceResp ) {
    option( tlvpickle.CmdID ) = 29;
    option( tlvpickle.OptString ) = "u:d:";
    option( tlvpickle.Usage ) = "-u <uid> -d <device_id>";
  }
}
