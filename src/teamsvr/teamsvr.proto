syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package teamsvr;


enum TeamType {
	CF_NORMAL = 1;
	CF_STAR = 2;
}

enum CfZone {
	WeChat = 1;
	QQ = 2;
}

enum OsPlatform{
	ANDROID = 1;
	IOS = 2;
}

enum MemberType {
	Captain = 1;		//队长
	ViceCaptain = 2;	//副队长
	Member = 3;			//队员
	bench = 4;			//替补
}

message CFTeamInfo {
	required OsPlatform os_platform = 1;
	required uint32 cf_zone = 2;
}

message MemberInfo {
	required uint32 uid = 1;
	required string account = 2;
	required string game_nickname = 3;
	required MemberType member_type = 4;
}

message TeamInfo {
	required TeamType team_type = 1;
	required string phone = 2;
	required string qq_number = 3;
	required uint32 is_approved = 4;	//是否报名明星战队
	required string team_name = 5;
	optional bytes detail = 6;  //Analysis base on team_type could be CFTeamInfo or something else
	optional uint32 leader_uid = 7; // uid
	optional uint32 sign_up_time = 8;
}

message TeamSumInfo {
	repeated MemberInfo memberinfo_list = 1;
	required TeamInfo team_info = 2;
	required uint32 team_id = 3;
}

message CreateTeamReq {
	required uint32 uid = 1;
	required string account = 2;
	required string game_nickname = 3;
	required TeamInfo team_info = 4;
}

message CreateTeamResp {
	required uint32 team_id = 1;
}

message GetTeamInfoReq {
	required uint32 uid = 1;
}

message GetTeamInfoResp {
	repeated MemberInfo memberinfo_list = 1;
	required TeamInfo team_info = 2;
	required uint32 team_id = 3;
}


// 
message GetTeamInfoByTeamIdReq {
	required uint32 team_id = 1;
}


// 根据队伍人数批量获取战队信息
message GetTeamInfoByMemberCountReq {
	required uint32 member_count = 1;
}

message GetTeamInfoByMemberCountResp {
	repeated TeamSumInfo team_sum_list = 1;
}

message DismissTeamReq {
	required uint32 team_id = 1;
}

message ModifyTeamInfoReq {
	required uint32 uid = 1;
	required string account = 2;
	required string game_nickname = 3;
	required TeamInfo team_info = 4;
}


message AddTeamMemberReq {
	required uint32 team_id = 1;
	required MemberInfo member_info = 2;
}

message RemoveTeamMemberReq {
	required uint32 team_id = 1;
	required uint32 member_uid = 2;
}


message ReplaceMemberReq {
	required uint32 team_id = 1;
	repeated MemberInfo member_list = 2;
}

message ReplaceMemberResp {
	repeated MemberInfo success_account_list = 1;
	repeated MemberInfo failed_account_list = 2;
}

message ApproveTeamReq {
	required uint32 team_id = 1;
}

message GetTeamApproveStatusResp{
	required uint32 status = 1;
}

message GetTeamCountResp{
	required uint32 count = 1;
}

message GetMemberCountResp{
	required uint32 count = 1;
}

message GetTeamInfoByIdListReq {
	repeated uint32 team_id_list = 1;
}

message GetTeamInfoByIdListResp{
	repeated TeamSumInfo team_sum_list = 1;
}

message MatchColumnInfo{
	optional uint32 id 				= 1;
	optional string title 			= 2;
	optional string title_image 	= 3;
	optional string content 		= 4;
	optional string content_image 	= 5;
	optional string link 			= 6;
	optional uint32 circle_id 		= 7;
	optional uint32 topic_id 		= 8;
}

message MatchTopicInfo{
	optional uint32 circle_id = 1;
	optional uint32 topic_id = 2;
	optional string title = 3;
}

message MatchVideoInfo{
	optional uint32 id 			= 1;
	optional string title 		= 2;
	optional string title_image = 3;
	optional string link 		= 4;
	optional uint32 circle_id 	= 5;
	optional uint32 topic_id 	= 6;
}

message MatchTopicList{
	repeated MatchTopicInfo list = 1;
}

message MatchColumnList{
	repeated  MatchColumnInfo list = 1;
}

message MatchVideoList{
	repeated MatchVideoInfo list = 1;
}

message MatchConfig{
	optional uint32	match_stage = 1;
	repeated MatchTopicInfo topic_list = 2;
	repeated MatchVideoInfo video_list = 3;
	repeated MatchColumnInfo column_list = 4;
}

message DelMatchColumnReq{
	required uint32 id = 1;
}

message DelMatchVideoReq{
	required uint32 id = 1;
}

message SetMatchStageReq{
	required uint32 stage_id = 1;
}

message GetMatchStageResp{
	required uint32 stage_id = 1;
}

message MatchFollowReq{
	required uint32 uid = 1;
}

message GetMatchFollowResp{
	required bool is_follow = 1;
}

// 投票
message VoteTeamReq {
    required string user_id = 1;             // 用户唯一标识 openID或者UID
    required uint32 team_id = 2;
    required uint32 votes = 3;
	required uint32 vote_source_type = 4;    // see vote/TeamVoteDef.h 中 E_VOTE_USER_SOURCE_TYPE定义 投票用户的来源 TT为1 微信为2
	required uint32 page_source_type = 5;    // 投票WEB页面的来源 用于统计数据 
}

message VoteTeamResp {
    required uint32 remain_vote_chances = 1; // 用户进行过投票后剩余的投票机会
}

// 额外加票 测试接口
message ExtraVoteTeamReq {
    required string user_id = 1;             // 用户唯一标识 openID或者UID
    required uint32 team_id = 2;
    required uint32 votes = 3;
}

message ExtraVoteTeamResp {
}


enum TeamRankingType {
	DAILY = 1;      // 日榜
	WEEKLY = 2;     // 周榜
	FINALLY = 3;    // 总榜
	VOTED = 4;      // 用户投票过的榜
}

message TeamRankResult {
    required uint32 team_id = 1;        // 战队ID
    required uint32 votes = 2;          // 总票数(包含额外加的票 不会为负数 一旦与额外票数相加为负数那么总票数为0)
    required int32 extra_votes = 3;     // 额外票数(运营操作,可能为负数)
	required uint32 rank = 4;           // 排名
}


// 获取战队排行榜
message GetTeamRankingReq {
    required uint32 ranking_type = 1; // 榜单类型 see TeamRankingType
    required uint32 uid = 2;          // 操作者

    optional uint32 start_index = 3;  // 从榜单的第几名开始获取 第一名为0
    optional uint32 limit = 4;        // 本次获取的数量
}

message GetTeamRankingResp {
    repeated TeamRankResult team_ranking_list = 1;
}

// 获取战队的排名
message GetTeamRankReq {
    required uint32 ranking_type = 1; // 榜单类型 see TeamRankingType
    required uint32 team_id = 2;
	optional bool is_need_context = 3; // 是否需要获取该队在排行榜的上下队伍的信息
}

message GetTeamRankResp {
    required TeamRankResult team_rank = 1; 
	optional TeamRankResult above_context_rank = 2;
	optional TeamRankResult below_context_rank = 3;
}

// 获取 全部 队伍列表
message GetTeamListReq {

}

message GetTeamListResp {
	repeated TeamSumInfo team_sum_list = 1;
}

// 根据TamID 批量获取 报名成功的战队信息
message BatchGetSignupPassTeamByIDsReq 
{
	repeated uint32 teamid_list = 1;
}

message BatchGetSignupPassTeamByIDsResp 
{
	repeated TeamSumInfo teamsuminfo_list = 1;
}

message GetSignupPassTeamIdsResp{
	repeated uint32 team_id_list = 1;
}

message VerifyTeamInfoReq {
	required uint32 uid = 1;
	required string team_name = 2;
}

service TeamSvr{
    option( tlvpickle.Magic ) = 15340;

    rpc CreateTeam ( CreateTeamReq ) returns ( CreateTeamResp ) {
        option( tlvpickle.CmdID ) = 1;										// 命令号
	    option( tlvpickle.OptString ) = "u:p:q:a:n:d:g:o:r:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <phone> -q <qq> -a <account> -n <team_name> -o <os> -r <area> -g <game_nickname>";	// 测试工具的命令号帮助	
    }

	rpc GetTeamInfo ( GetTeamInfoReq ) returns ( GetTeamInfoResp ) {
        option( tlvpickle.CmdID ) = 2;										// 命令号
	    option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";	// 测试工具的命令号帮助	
    }

	rpc DismissTeam ( DismissTeamReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 3;										// 命令号
	    option( tlvpickle.OptString ) = "t";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <team_id>";	// 测试工具的命令号帮助	
    }

	rpc ModifyTeamInfo ( ModifyTeamInfoReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 5;										// 命令号
	    option( tlvpickle.OptString ) = "u:t:p:q:a:n:g:o:r:e:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<uid> -t <team_type> -p <phone> -q <qq> -a <account> -n <team_name> -o <os_platfrom -r <area> -g <game_nickname> -e <is_approved";	// 测试工具的命令号帮助	
    }

	rpc AddTeamMember ( AddTeamMemberReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 6;										// 命令号
	    option( tlvpickle.OptString ) = "a:n:t:m:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-a <account> -n <game_nickname> -t <team_id> -m <member_type>";	// 测试工具的命令号帮助	
    }

	rpc RemoveTeamMember ( RemoveTeamMemberReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 7;										// 命令号
	    option( tlvpickle.OptString ) = "t:u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <team_id> -u <uid>";	// 测试工具的命令号帮助	
    }

	rpc GetMatchTopic ( tlvpickle.SKBuiltinEmpty_PB ) returns ( MatchTopicList ) {
        option( tlvpickle.CmdID ) = 8;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
    }

	rpc SetMatchTopic ( MatchTopicList ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 9;										// 命令号
	    option( tlvpickle.OptString ) = "i:t:l:o:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <index,from 0> -t <title> -l <circle_id> -o <topic_id>";	// 测试工具的命令号帮助	
    }      

	rpc GetMatchColumn ( tlvpickle.SKBuiltinEmpty_PB ) returns ( MatchColumnList ) {
        option( tlvpickle.CmdID ) = 10;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
    }

	rpc SetMatchColumn ( MatchColumnList ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 11;										// 命令号
	    option( tlvpickle.OptString ) = "i:e:m:o:a:n:l:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <index> -e <title> -m <title_image> -o <content> -a <content_image> -n <link> -l <circle_id> -t <topic_id>";	// 测试工具的命令号帮助	
    }

	rpc GetMatchFollow ( MatchFollowReq ) returns ( GetMatchFollowResp ) {
        option( tlvpickle.CmdID ) = 12;										// 命令号
	    option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";	// 测试工具的命令号帮助	
    }

	rpc AddMatchFollow ( MatchFollowReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 13;										// 命令号
	    option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";	// 测试工具的命令号帮助	
    }    
	
	rpc GetTeamInfoByTeamId ( GetTeamInfoByTeamIdReq ) returns ( GetTeamInfoResp ) {
        option( tlvpickle.CmdID ) = 14;										// 命令号
	    option( tlvpickle.OptString ) = "t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <team_id>";	// 测试工具的命令号帮助	
    }
	
	// 投票
	rpc VoteTeam ( VoteTeamReq ) returns ( VoteTeamResp ) {
        option( tlvpickle.CmdID ) = 15;										
	    option( tlvpickle.OptString ) = "u:t:s:x:";							
        option( tlvpickle.Usage ) = "-u <uid> -t <team_id> -s <votes> -x <vote_souce_type 1:tt 2:wx >";	
    }
	
    // 获取排行榜
	rpc GetTeamRanking ( GetTeamRankingReq ) returns ( GetTeamRankingResp ) {
        option( tlvpickle.CmdID ) = 16;										
	    option( tlvpickle.OptString ) = "u:x:";							
        option( tlvpickle.Usage ) = "-u <uid> -x <ranking type 1:daily 2:weekly 3:finally>";	
    }
	
	// 获取排名
	rpc GetTeamRank ( GetTeamRankReq ) returns ( GetTeamRankResp ) {
        option( tlvpickle.CmdID ) = 17;										
	    option( tlvpickle.OptString ) = "u:t:x:";							
        option( tlvpickle.Usage ) = "-u <uid> -t <team_id> -x <ranking type 1:daily 2:weekly 3:finally>";	
    }

	rpc DelMatchColumn ( DelMatchColumnReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
	    option( tlvpickle.CmdID ) = 18;										
		option( tlvpickle.OptString ) = "i:";							
	    option( tlvpickle.Usage ) = "-i <index from 0>";
	}    
	
	rpc SetMatchStage (SetMatchStageReq) returns(tlvpickle.SKBuiltinEmpty_PB) {
	    option( tlvpickle.CmdID ) = 19;
		option( tlvpickle.OptString ) = "s:";
	    option( tlvpickle.Usage ) = "-s <stage_code>";	
	}

	rpc GetMatchStage (tlvpickle.SKBuiltinEmpty_PB) returns(GetMatchStageResp) {
	    option( tlvpickle.CmdID ) = 20;
		option( tlvpickle.OptString ) = "";
	    option( tlvpickle.Usage ) = "";	
	}		


	rpc ReplaceMember ( ReplaceMemberReq ) returns ( ReplaceMemberResp ) {
        option( tlvpickle.CmdID ) = 21;										// 命令号
	    option( tlvpickle.OptString ) = "u:a:n:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -a <account> -n <game_nickname> -t <team_id>";	// 测试工具的命令号帮助	
    }
	
	rpc GetTeamInfoByMemberCount ( GetTeamInfoByMemberCountReq ) returns ( GetTeamInfoByMemberCountResp ) {
        option( tlvpickle.CmdID ) = 22;										// 命令号
	    option( tlvpickle.OptString ) = "m:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <member_count>";	// 测试工具的命令号帮助	
    }
	
	rpc ApproveTeam ( ApproveTeamReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 23;										// 命令号
	    option( tlvpickle.OptString ) = "t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <team_id>";	// 测试工具的命令号帮助	
    }

    rpc GetTeamApproveStatus( ApproveTeamReq ) returns (GetTeamApproveStatusResp) {
        option( tlvpickle.CmdID ) = 24;										// 命令号
	    option( tlvpickle.OptString ) = "t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <team_id>";	// 测试工具的命令号帮助	    
    }

	rpc GetTeamCount ( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetTeamCountResp ) {
        option( tlvpickle.CmdID ) = 25;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
    }

	rpc GetMemberCount ( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetMemberCountResp ) {
        option( tlvpickle.CmdID ) = 26;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
    }    
	
	// 额外加票 测试接口
	rpc ExtraVoteTeam ( ExtraVoteTeamReq ) returns ( ExtraVoteTeamResp ) {
        option( tlvpickle.CmdID ) = 27;									
	    option( tlvpickle.OptString ) = "t:s:";
        option( tlvpickle.Usage ) = "-t <team_id> -s <votes>";
    }
	
	rpc GetTeamList ( GetTeamListReq ) returns ( GetTeamListResp ) {
        option( tlvpickle.CmdID ) = 28;									
	    option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
	
	// 

	rpc GetMatchVideo ( tlvpickle.SKBuiltinEmpty_PB ) returns ( MatchVideoList ) {
        option( tlvpickle.CmdID ) = 29;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
    }

	rpc SetMatchVideo ( MatchVideoList ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 30;										// 命令号
	    option( tlvpickle.OptString ) = "i:e:m:o:a:n:l:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <index> -e <title> -m <title_image> -n <link> -l <circle_id> -t <topic_id>";	// 测试工具的命令号帮助	
    }

	rpc DelMatchVideo ( DelMatchVideoReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
	    option( tlvpickle.CmdID ) = 31;										
		option( tlvpickle.OptString ) = "i:";							
	    option( tlvpickle.Usage ) = "-i <index from 0>";
	} 

	// 根据TamID 批量获取 报名成功的战队信息
	rpc BatchGetSignupPassTeamByIDs ( BatchGetSignupPassTeamByIDsReq ) returns ( BatchGetSignupPassTeamByIDsResp ) {
	    option( tlvpickle.CmdID ) = 32;										
		option( tlvpickle.OptString ) = "i:t:";							
	    option( tlvpickle.Usage ) = "-i <team id 1> -t <team id 2>";
	} 
	
	rpc GetTeamInfoByIdList ( GetTeamInfoByIdListReq ) returns ( GetTeamInfoByIdListResp ) {
	    option( tlvpickle.CmdID ) = 33;										
		option( tlvpickle.OptString ) = "t:";							
	    option( tlvpickle.Usage ) = "-t <team_id split with ,>";
	}

	rpc GetSignupPassTeamIds ( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetSignupPassTeamIdsResp ) {
	    option( tlvpickle.CmdID ) = 34;										
		option( tlvpickle.OptString ) = "";							
	    option( tlvpickle.Usage ) = "";
	}	
	
	rpc VerifyTeamInfo ( VerifyTeamInfoReq ) returns (  tlvpickle.SKBuiltinEmpty_PB ) {
	    option( tlvpickle.CmdID ) = 50;										
		option( tlvpickle.OptString ) = "";							
	    option( tlvpickle.Usage ) = "";
	}	
	
}
