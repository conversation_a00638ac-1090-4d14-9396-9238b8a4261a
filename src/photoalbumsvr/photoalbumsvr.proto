syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package photoalbumsvr;


//用户相册
message UpdatePhotoAlbumReq {
	required uint32 uid = 1;
	required string img_keys = 2;
}

message GetPhotoAlbumReq {
	required uint32 uid = 2;
}

message GetPhotoAlbumResp {
	required string img_keys = 1;
}


 

service PhotoAlbumSvr{
    option( tlvpickle.Magic ) = 15600;

    rpc UpdatePhotoAlbum(UpdatePhotoAlbumReq) returns (tlvpickle.SKBuiltinEmpty_PB)
    {
     	option ( tlvpickle.CmdID ) = 1;
    	option ( tlvpickle.OptString ) = "u:l:";
    	option ( tlvpickle.Usage ) = "-u <uid> -l <img_list>";   
    }


	rpc GetPhotoAlbum(GetPhotoAlbumReq) returns (GetPhotoAlbumResp)
    {
     	option ( tlvpickle.CmdID ) = 2;
    	option ( tlvpickle.OptString ) = "u:";
    	option ( tlvpickle.Usage ) = "-u <uid>";   
    }
}
 