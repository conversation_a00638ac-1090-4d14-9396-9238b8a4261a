syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package searchguildsvr;

message SearchGuildByKeyNameReq 
{
	required string key_name = 1;
}

message SearchGuildByKeyNameResp
{
	repeated uint32 guild_id_list = 1;
}

service searchguildsvr {
	option( tlvpickle.Magic ) = 15390;		// 服务监听端口号
	
	rpc SearchGuildByKeyName ( SearchGuildByKeyNameReq) returns ( SearchGuildByKeyNameResp ){
		option( tlvpickle.CmdID ) = 1;
		option( tlvpickle.OptString ) = "u:x:";
		option( tlvpickle.Usage ) = "-u <uid> -x <key>";
	}

}
