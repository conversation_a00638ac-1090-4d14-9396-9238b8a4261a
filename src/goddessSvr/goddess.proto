syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package goddess;

enum SIGNUP_STATUS_TYPE {
	SIGNUP_STAT_NOT = 1;              // 未报名
	SIGNUP_STAT_WAIT_AUDIT = 2;       // 待审核
	SIGNUP_STAT_AUDIT_WAITMODIFY = 3; // 已经审核待修改 (等待客服联系用户确认如何修改) (即处于待定状态)
	SIGNUP_STAT_AUDIT_NOTPASS = 4;    // 审核不通过 (执行删除操作) 所以实际上没有该状态
    SIGNUP_STAT_OK = 5;               // 报名成功
}

// 女神基本信息
message GoddessBaseInfo {
    required uint32 uid = 1;              // uid
    optional uint32 guild_id = 2;         // 公会ID
    optional uint32 classify_type = 3;    // 分类
    optional uint32 signup_stat = 4;      // SIGNUP_STATUS_TYPE 报名状态 1=未报名 2=已报名待审核 3=已报名已审核待修改 5=通过审核(报名成功)
    optional uint32 signup_ts = 5;        // 报名时间戳
    optional uint32 signup_update_ts = 6; // 报名数据最后更新时间戳
}

// 报名扩展信息
message GoddessExtendInfo
{
    optional string intro_info = 1;  // 个人简介
    repeated string photo_list = 2;  // 图片url列表
    repeated string game_list = 3;   // 游戏Name 列表

    optional string job = 4;         // 职业
    optional string zodiac_sign = 5; // 星座
    optional uint32 height = 6;      // 身高cm
    optional string bust = 7;        // 胸围(35D 大写)
}

// 审核信息
message GoddessSignupAuditInfo
{
	optional string qq_code = 1;     // qq  只在报名录入的时候填写 用于后台审核 不在页面返回
	optional string phone = 2;       // 电话 只在报名录入的时候填写 用于后台审核 不在页面返回
	optional string audit_info = 3;  // 记录的审核信息
}

message GoddessDetailInfo {
    required GoddessBaseInfo base_info = 1;         // 基本信息
    optional GoddessExtendInfo extend_info = 2;     // 报名扩展信息
    optional GoddessSignupAuditInfo audit_info = 3; // 只在报名录入的时候填写 用于后台审核 不在页面返回
}

// 报名
message SignupReq
{
    required GoddessDetailInfo info = 1;
}

// 修改报名数据
message ModifySignupInfoReq
{
	required GoddessDetailInfo info = 1;   // uid
	required string op_user = 2;           // op info
}

// 删除报名(可以对所有状态的报名信息操作)
message DelSignupInfoReq
{
	required uint32 signup_uid = 1;       // uid
	required string op_user    = 2;       // op user info
	required string op_reason  = 3;       // op reason
}

message DelSignupInfoResp
{
	required uint32 before_del_signupstate  = 1;       // 删除前用户的报名状态
}

// 审核报名(不能对审核通过的用户进行操作)
message AuditSignupInfoReq
{
	required uint32 signup_uid = 1;       // uid
	required string op_user = 2;          // op info
	required uint32 signup_stat = 3;      // SIGNUP_STATUS_TYPE 报名状态  报名状态 3=已报名已审核待修改 4=审核不通过(删除) 5=通过审核(报名成功)
	optional string audit_msg = 4;        // 待审核信息 对于 "已报名已审核待修改" 这种状态 用于记录审核修改建议
}



// 获取用户的基本信息
message GetUserBaseInfoReq
{
	required uint32 op_uid = 1;
	required uint32 target_uid = 2;
}

message GetUserBaseInfoResp
{
	required GoddessBaseInfo base_info = 1;
}

// 获取用户的公开的报名信息
message GetUserExtendInfoReq
{
	required uint32 op_uid = 1;
	required uint32 target_uid = 2;
}

message GetUserExtendInfoResp
{
	required GoddessExtendInfo signup_info = 1;
}

// 获取用户的详细信息
message GetUserDetailInfoReq
{
	required uint32 op_uid = 1;
	required uint32 target_uid = 2;
}

message GetUserDetailInfoResp
{
	required GoddessDetailInfo detail_info = 1;
}

// 女神类型
message GoddessClassifyInfo
{
	required uint32 classify_id  = 1;    // 分类ID 不能为0
	required string classify_name = 2;   // 分类名称 譬如 "气质女生" "邻家妹子" "二次元妹子"等
	required bool is_vaild_signup = 3;   // 是否允许选择该值去报名
	optional uint32 signup_cnt = 4;      // 该分类下已经报名的人数
}

message GetAllGoddessClassifyInfoReq
{
	required bool is_need_signup_cnt  = 1;    // 是否需要携带报名人数数据
}

message GetAllGoddessClassifyInfoResp
{
	repeated GoddessClassifyInfo classify_list  = 1;
	optional uint32 all_signup_cnt = 2;  // 所有分类下已经报名的人数
}

// 根据报名状态 获取报名列表(用于后台审核)
message GetUserDetailListBySignupStatusReq
{
	required uint32 signup_status = 1;  // SIGNUP_STATUS_TYPE 报名状态 2=已报名待审核 3=已报名已审核待修改 5=通过审核(报名成功)
	required bool is_need_signup_info = 2; // 是否返回GoddessExtendInfo信息
}

message GetUserDetailListBySignupStatusResp
{
	required uint32 signup_status = 1;  // SIGNUP_STATUS_TYPE 报名状态 2=已报名待审核 3=已报名已审核待修改 5=通过审核(报名成功)
	repeated GoddessDetailInfo detail_list = 2;
}

// 根据女神类型 获取报名列表(用于动态展示)
message GetUserBaseListByGoddessClassifyReq
{
	required uint32 goddess_classify_id = 1;  // 女神类型 0=表示全部类型 由 GoddessClassifyInfo 获取
}

message GetUserBaseListByGoddessClassifyResp
{
	required uint32 goddess_classify_id = 1;  // 女神类型 0=表示全部类型 由 GoddessClassifyInfo 获取
	repeated GoddessBaseInfo base_list = 2;
}

// 根据UID批量获取女神详情
message BatGetGoddessDetailListReq
{
	repeated uint32 uid_list= 1;   // 女神uid列表
	optional bool is_need_auditinfo = 2;   // 是否需要携带Audit数据(QQ号手机审核标记等)
}

message BatGetGoddessDetailListResp
{
	repeated GoddessDetailInfo detail_list = 2;
}

// 修改女神的公会ID 只有在女神报名时没有填写公会信息时有效
message ModifyGoddessSignupGuildInfoReq
{
	required uint32 goddess_uid = 1;   // 女神uid
	required uint32 guildid = 2;       // 公会ID
}


////////////////////////////////////////////////////////////////
// APIs for goddess' trends
////////////////////////////////////////////////////////////////

// 女神动态, 实际上就是游戏圈的数据
message GoddessTrend {
    required uint32 topic_id = 1;		                // 主题id
    required string title = 2;			                // 标题
    required string content = 3;                        // 内容
    required uint32 creator = 4;                        // 女神uid
    required uint32 create_time = 5;	                // 发布时间
    required uint32 like_count = 6;		                // 点赞数
    required uint32 comment_count = 7;	                // 评论数
    repeated string image_list = 8;		                // 图片列表
    required uint32 tag = 9;			                // 帖子状态
    required uint32 last_comment_time = 10;             // 最后评论时间
    optional GoddessBaseInfo goddess_base_info = 11;    // 女神基本资料
	optional uint32 tease_count = 12;                   // 被撩的次数
}

// 根据女神分类获取动态列表
message GetGoddessTrendsByGoddessClassifyReq {
    optional uint32 classify_id = 1;        // 分类, 如果不填, 则表示取所有分类
    required uint32 start_topic_id = 2;     // 分页起始
    required uint32 trends_count = 3;       // 拉取的动态数量
}

message GetGoddessTrendsByGoddessUidReq {
    repeated uint32 goddess_uid_list = 1;   // 女神uid列表
    required uint32 start_topic_id = 2;     // 分页起始
    required uint32 trends_count = 3;       // 拉取的动态数量
}

message GoddessTrendsResp {
    repeated GoddessTrend trend_list = 1;   // 动态列表
    required uint32 trends_total = 2;       // 动态总数
    required bool has_more = 3;             // 是否还有更多
}

// 撩妹 对帖子撩一下
message TeaseGoddessReq
{
	required uint32 op_uid = 1;      // 撩妹者的UID
	required uint32 goddess_uid = 2; // 女神UID 也就是主题的creator
    optional uint32 topic_id = 3;    // 女神的动态的主题ID
}

message TeaseGoddessResp {
}

////////////////////////////////////////////////////////////////
// APIs for vote
////////////////////////////////////////////////////////////////

message BrushVoteCheckOpt
{
	optional string ip_info = 1;
	optional string device_info = 2;
	optional uint32 user_level = 3;
}	

message GoddessVote {
    required uint32 uid = 1;
    required uint32 votes = 2;          // 总票数(包含额外加的票 不会为负数 一旦与额外票数相加为负数那么总票数为0)
    required int32 extra_votes = 3;     // 额外票数(运营操作,可能为负数)
}

message VoteGoddessReq {
    required uint32 uid = 1;
    required uint32 goddess_uid = 2;
    required uint32 votes = 3;
	optional uint32 page_source_type = 4;    // 投票页的来源 用于统计数据
	optional BrushVoteCheckOpt check_opt = 5; 
}

message VoteGoddessResp {
    required uint32 remain_vote_chances = 1;
}

enum RankingType {
	HOURLY = 1;     // 小时榜
	DAILY = 2;      // 日榜
	FINALLY = 3;    // 总榜
	VOTED = 4;      // 用户投票过的榜
}

message GetGoddessRankingReq {
    required uint32 ranking_type = 1;
    required uint32 uid = 2;
    required uint32 classify_id = 3;
    optional uint32 start_index = 4;
    optional uint32 limit = 5;
}

message GetGoddessRankingResp {
    repeated GoddessVote goddess_vote_list = 1;
}

message GetGoddessFinalRankingReq {
    required uint32 uid = 1;
}

message GetGoddessRankInRankingReq {
	required uint32 goddess_uid = 1;
	required uint32 ranking_type = 2;
}

message GetGoddessRankInRankingResp {
	required uint32 rank = 2;
}

message GetGoddessVotesReq {
    repeated uint32 goddess_uid_list = 1;
}

message GetGoddessVotesResp {
    repeated GoddessVote goddess_vote_list = 1;
}

message GetUserVoteChancesReq {
    required uint32 uid = 1;
	optional BrushVoteCheckOpt check_opt = 2; 
}

message GetUserVoteChancesResp {
    required uint32 chances = 1;
}

message GetUserToGoddessVotesReq {
	required uint32 uid = 1;
	required uint32 goddess_uid = 2;
}

message GetUserToGoddessVotesResp {
	required uint32 votes = 1;
}

// 官方后台加票器
message AddExtraVotesReq
{
	required string op_user = 1;          // op info
	required uint32 goddess_uid = 2;      // goddess
	required int32 extra_votes = 3;       // 加票数 可以为负数
}

message AddExtraVotesResp {
    required GoddessVote current_vote = 1;
}

// 非TT第三方(联运SDK,微信,QQ等) 投票接口
message ThirdPartyVoteGoddessReq
{
	required uint32 third_party_type = 1; // goddessDef.h E_GODDESS_VOTE_SOURCE_TYPE 来源 联运SDK为2 微信为11 QQ为12
	required string identifier = 2;       // 投票者的唯一标示, 第三方平台的的OpenID或者uid
	required uint32 goddess_uid = 3;      // goddess
	required uint32 vote_cnt  = 4;        // 为该女神投多少票
	optional uint32 page_source_type = 5; // 投票页的来源 用于统计数据
}

message ThirdPartyVoteGoddessResp {
    required bool is_vote = 1;           // 是否进行了投票 false一般是没有投票资格了
}

message ThirdPartyCheckVoteChancesReq
{
	required uint32 third_party_type = 1; // goddessDef.h E_GODDESS_VOTE_SOURCE_TYPE 来源 联运SDK为2 微信为11 QQ为12
	required string identifier = 2;       // 投票者的唯一标示, 第三方平台的的OpenID或者uid
}

message ThirdPartyCheckVoteChancesResp {
    required uint32 remain_chances = 1;   // 第三方剩余的投票机会 一般只有0和1两个值
}

//
message VoteID {
    required bool is_in_tt = 1;         // 是否在TT内
    required string identifier = 2;     // 投票者的唯一标示, 第三方平台的的OpenID或者uid
    required uint32 goddess_uid = 3;    // 女神的Uid
    required uint32 timestamp = 4;      // 生成该vote_id的时间戳
    required uint32 salt = 10;
}

message ShareID {
    required uint32 uid = 1;            // 分享人的uid
    required uint32 timestamp = 2;      // 生成该share_id的时间戳
    required uint32 salt = 10;
}

message CreateVoteIDReq {
    required bool is_in_tt = 1;                 // 是否在TT内
    required string identifier = 2;             // 投票者的唯一标示, 第三方平台的的OpenID或者uid
    repeated uint32 goddess_uid_list = 3;       // 女神的Uid列表
}

message CreateVoteIDResp {
    repeated string vote_id_list = 1;    // 严格按照女神uid的数量、顺序返回
}

message CreateShareIDReq {
    required uint32 uid = 1;            // 分享人的uid
    required uint32 timestamp = 2;      // timestamp
}

message CreateShareIDResp {
    required string share_id = 1;
}

message GetCurrentStageReq {

}

enum Stage {
    None = 0;
    Signup = 1;
    Vote = 2;
    Finally = 3;
    End = 4;
}

message GetCurrentStageResp {
    required uint32 current_stage = 1;
}

// 触发 给目标用户 奖励
enum RewardType {
	REWARD_TYPE_DAILY = 1;     // 日常奖励
    REWARD_TYPE_SHARE = 2;     // 分享奖励
}

message NotifyRewardUserVoteChancesReq {

    required uint32 target_uid = 1;       // 源分享人的uid
    required uint32 ori_ts  = 2;          // 源分享的时间
	required uint32 type    = 3;          // 奖励的类型

	optional string trigger_info = 4;    // 触发该奖励的信息
	optional uint32 reward_num = 5;      // 奖励的票数
	optional BrushVoteCheckOpt check_opt = 6; 
}

message NotifyRewardUserVoteChancesResp {
	required bool is_reward = 1;       // 有没有给与奖励
}


////////////////////////////////////////////////////////////////
// massage for statistic
////////////////////////////////////////////////////////////////
message VoteStatisticsAsyncData
{
    required uint32 target_goddess = 1;
    required uint32 ori_uid  = 2;       // 非TT来源的话 UID为0
	required uint32 source_type  = 3;   // goddessDef.h E_GODDESS_VOTE_SOURCE_TYPE 来源 TT为1 联运SDK为2 微信为11 QQ为12
	optional string ex_user_info = 4;   // 第三方投票者的唯一标示, 第三方平台的的OpenID或者uid
	optional uint32 vote_cnt = 5;       // 投票数量
}

message RewardStatisticsAsyncData
{
    required uint32 target_uid  = 1;
    required uint32 reward_type = 2;     // RewardType
	required uint32 reward_num = 3;      // 奖励的票数
}

message TeaseStatisticsAsyncData
{
    required uint32 op_uid  = 1;
    required uint32 godess_uid = 2;    // 撩妹的对象 女神ID
	required uint32 topic_id = 3;      // 撩妹对应的女神动态ID
}

service goddess {
    option( tlvpickle.Magic ) = 15281;

	// 报名
    rpc Signup( SignupReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:g:c:";
        option( tlvpickle.Usage ) = "-u <sign uid> -g <guild> -c <classify>";
	}

	// 修改报名信息
	rpc ModifySignupInfo( ModifySignupInfoReq ) returns( tlvpickle.SKBuiltinEmpty_PB  ){
		option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:g:c:";
        option( tlvpickle.Usage ) = "-u <sign uid> -g <guild> -c <classify>";
	}

	// 删除报名
	rpc DelSignupInfo( DelSignupInfoReq ) returns( DelSignupInfoResp  ){
		option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <sign uid>";
	}

	// 审核报名
	rpc AuditSignupInfo( AuditSignupInfoReq ) returns( tlvpickle.SKBuiltinEmpty_PB  ){
		option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "u:s:";
        option( tlvpickle.Usage ) = "-u <sign uid> -s <audit stat>";
	}

	// 获取用户的基本信息
	rpc GetUserBaseInfo( GetUserBaseInfoReq ) returns( GetUserBaseInfoResp  ){
		option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <sign uid>";
	}

	// 获取用户的公开的扩展报名信息
	rpc GetUserExtendInfo( GetUserExtendInfoReq ) returns( GetUserExtendInfoResp  ){
		option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <sign uid>";
	}

	// 获取用户的详细信息
	rpc GetUserDetailInfo( GetUserDetailInfoReq ) returns( GetUserDetailInfoResp  ){
		option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <sign uid>";
	}

	// 获取女神类型的配置列表
	rpc GetAllGoddessClassifyInfo( GetAllGoddessClassifyInfoReq ) returns( GetAllGoddessClassifyInfoResp ){
		option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <sign uid>";
	}

	// 根据报名状态 获取详细报名列表(用于后台审核)
	rpc GetUserDetailListBySignupStatus( GetUserDetailListBySignupStatusReq ) returns( GetUserDetailListBySignupStatusResp  ){
		option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "u:s:";
        option( tlvpickle.Usage ) = "-u <sign uid> -s <signup status>";
	}

	// 根据女神类型 获取已经成功报名的女神基本信息列表
	rpc GetUserBaseListByGoddessClassify( GetUserBaseListByGoddessClassifyReq ) returns( GetUserBaseListByGoddessClassifyResp  ){
		option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "u:c:";
        option( tlvpickle.Usage ) = "-u <sign uid> -c <goddess classify>";
	}

	// 根据UID列表批量获取女神详细信息
	rpc BatGetGoddessDetailList( BatGetGoddessDetailListReq ) returns( BatGetGoddessDetailListResp  ){
		option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "u:s:";
        option( tlvpickle.Usage ) = "-u <goddess uid1> -s <goddess uid2>";
	}


    rpc GetGoddessTrendsByGoddessClassify( GetGoddessTrendsByGoddessClassifyReq ) returns ( GoddessTrendsResp ) {
        option( tlvpickle.CmdID ) = 100;
        option( tlvpickle.OptString ) = "k:s:n:";
        option( tlvpickle.Usage ) = "-k <goddess classify> -s <start_topic_id> -n <count>";
    }

    rpc GetGoddessTrendsByGoddessUid( GetGoddessTrendsByGoddessUidReq ) returns ( GoddessTrendsResp ) {
        option( tlvpickle.CmdID ) = 101;
        option( tlvpickle.OptString ) = "u:s:n:";
        option( tlvpickle.Usage ) = "-u <uid> -s <start_topic_id> -n <count>";
    }

	//撩一下女神
	rpc TeaseGoddess( TeaseGoddessReq ) returns ( TeaseGoddessResp ) {
        option( tlvpickle.CmdID ) = 102;
        option( tlvpickle.OptString ) = "u:t:g:";
        option( tlvpickle.Usage ) = "-u <opuid> -t <topic_id> -g <goddess_id >";
    }

    rpc VoteGoddess( VoteGoddessReq ) returns ( VoteGoddessResp ) {
        option( tlvpickle.CmdID ) = 200;
        option( tlvpickle.OptString ) = "u:g:n:";
        option( tlvpickle.Usage ) = "-u <uid> -g <goddess_uid> -n <votes>";
    }

    rpc GetGoddessRanking( GetGoddessRankingReq ) returns( GetGoddessRankingResp ) {
        option( tlvpickle.CmdID ) = 201;
        option( tlvpickle.OptString ) = "u:t:k:";
        option( tlvpickle.Usage ) = "-u <uid> -t <ranking_type> -k <classify_id>";
    }

    rpc GetGoddessVotes( GetGoddessVotesReq ) returns ( GetGoddessVotesResp ) {
        option( tlvpickle.CmdID ) = 202;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid_list>";
    }

    rpc GetUserVoteChances( GetUserVoteChancesReq ) returns ( GetUserVoteChancesResp ) {
        option( tlvpickle.CmdID ) = 203;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc AddExtraVotes( AddExtraVotesReq ) returns ( AddExtraVotesResp ) {
        option( tlvpickle.CmdID ) = 204;
        option( tlvpickle.OptString ) = "g:t:";
        option( tlvpickle.Usage ) = "-g <goddess_uid> -t <votes>";
    }

	// 触发 增加 奖励
	rpc NotifyRewardUserVoteChances( NotifyRewardUserVoteChancesReq ) returns ( NotifyRewardUserVoteChancesResp ) {
        option( tlvpickle.CmdID ) = 205;
        option( tlvpickle.OptString ) = "u:t:x:";
        option( tlvpickle.Usage ) = "-u <target_uid> -t <timestamp> -x <reward type>";
    }

	// 第三方投票接口
	rpc ThirdPartyVoteGoddess( ThirdPartyVoteGoddessReq ) returns ( ThirdPartyVoteGoddessResp ) {
        option( tlvpickle.CmdID ) = 206;
        option( tlvpickle.OptString ) = "t:x:g:";
        option( tlvpickle.Usage ) = "-t <ThirdParty Type> -x <ThirdParty user id> -g <goddess uid>";
    }

	// 第三方查询投票机会接口
	rpc ThirdPartyCheckVoteChances( ThirdPartyCheckVoteChancesReq ) returns ( ThirdPartyCheckVoteChancesResp ) {
        option( tlvpickle.CmdID ) = 207;
        option( tlvpickle.OptString ) = "t:x:";
        option( tlvpickle.Usage ) = "-t <ThirdParty Type> -x <ThirdParty user id>";
    }

	rpc GetUserToGoddessVotes( GetUserToGoddessVotesReq ) returns( GetUserToGoddessVotesResp ) {
		option( tlvpickle.CmdID ) = 208;
		option( tlvpickle.OptString ) = "u:g:";
		option( tlvpickle.Usage ) = "-u <uid> -g <goddess_uid>";
	}

	rpc GetGoddessRankInRanking( GetGoddessRankInRankingReq ) returns( GetGoddessRankInRankingResp ) {
		option( tlvpickle.CmdID ) = 209;
		option( tlvpickle.OptString ) = "g:r:";
		option( tlvpickle.Usage ) = "-g <goddess_uid> -r <ranking_type>";
	}

    // 获取当前阶段
    rpc GetCurrentStage( GetCurrentStageReq ) returns ( GetCurrentStageResp ) {
        option( tlvpickle.CmdID ) = 300;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetGoddessFinalRanking( GetGoddessFinalRankingReq ) returns( GetGoddessRankingResp ) {
        option( tlvpickle.CmdID ) = 301;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }
}
