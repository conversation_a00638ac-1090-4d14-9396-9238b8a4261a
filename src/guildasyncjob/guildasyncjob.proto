syntax="proto2";

package guildasyncjob;

message Commands {
  enum Values {
    PublishGuildJoinEvent = 1;
    NotifyGuildChange = 2;
    NotifyGroupMeberChange = 3;
    PublishGuildQuitEvent = 4;
    NotifyGroupChange = 5;
  }
}

message PublishGuildJoinEventJobData {
  required uint32 uid = 1;
  required uint32 guild_id = 2;
  optional uint32 ts = 3;
}

message PublishGuildQuitEventJobData {
  required uint32 uid = 1;
  required uint32 guild_id = 2;
  optional uint32 ts = 3;
}


message NotifyGuildChangeData{
  enum ChangeType{
    GUILD_CREATE = 1;
    GUILD_DISMISS = 2;
    GUILD_NAME_MODIFY = 3;
  }
  required uint32 change_type = 1;
  required uint32 guild_id = 2;
  optional string name = 3;
  optional uint32 ts = 4;
  optional uint32 uid = 5;
}

message UserGroupChange{
  required uint32 uid = 1;
  repeated uint32 group_ids = 2;    //uid_list
}

message NotifyGroupMeberChangeData {
  enum ChangeType {
    JOIN_GROUP = 1;
    QUIT_GROUP = 2;
    DISMISS_GROUP = 3;          //group_id
  }

  required uint32 change_type = 1;
  optional UserGroupChange user_group_change = 2;
  repeated uint32 dismissed_groups = 3;

  optional uint32 uid = 4;
}

message NotifyGroupChangeData {
  enum ChangeType {
    CREATE_GROUP = 1;
    DISMISS_GROUP = 2;
  }
  required uint32 change_type = 1;
  required uint32 group_id = 2;
  required uint32 uid = 3;
  required uint32 at = 4;
  optional uint32 group_type = 5;
  optional uint32 display_id = 6;
  optional string name = 7;
}