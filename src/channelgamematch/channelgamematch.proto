syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package channelgamematch;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

message AddChannelReq
{
	required uint32 channel_id = 1;
	required uint32 max_member_count = 2;	
	required uint32 tag_id = 3;
	required uint32 cur_member_count = 4;
}

message AddChannelResp
{
	
}

message AddNoviceRecommendChannelReq
{
  required uint32 channel_id = 1;
}

message AddNoviceRecommendChannelResp
{
}

message RemoveNoviceRecommendChannelReq
{
  required uint32 channel_id = 1;
}

message RemoveNoviceRecommendChannelResp
{
}

message GetNoviceRecommendChannelReq
{
}

message GetNoviceRecommendChannelResp
{
  repeated uint32 channel_list = 1;
}

message GetChannelInfoReq
{
	repeated uint32 channel_id = 1;
}
message GetChannelInfoResp
{
	required uint32 channel_id = 1;
}

message UserEnterChannelReq
{
	required uint32 channel_id = 1;
	required uint32 member_count = 2;
}
message UserEnterChannelResp
{
	required bool is_full = 1;
}

enum EChannelEvent
{
	ECE_CREATOR_QUIT = 1;
	ECE_MIC_MODE_CHANGED = 2;
	ECE_PASSWORD_SWITCH = 3;
	ECE_TAG_ID_CHANGED = 4;
}

message HandleChannelEventReq
{
	message ChannelEvent
	{
		required uint32 event_type = 1;
		optional bool mic_mode_ok = 2;
		optional bool has_pwd = 3;
		optional bool tag_is_game = 4;
		optional uint32 new_tag_id = 5;
	}
	required uint32 channel_id = 1;
	required ChannelEvent channel_event = 2;
}
message HandleChannelEventResp
{

}

message GetChannelListReq{
	required uint32 tag_id = 1;
	required uint32 start = 2;      // statrt=0, 则应该填充 is_novice
	required uint32 count = 3;
	required bool game_info = 4;
    optional bool is_novice = 5;    // 发起查询的用户是否为注册时间小于5天的新用户
}

message GameChannelInfo {
	required uint32 channel_id = 1;
	required uint32 max_member_count = 2;
	optional string game_name = 3;
	optional string icon_url = 4;
	required uint32 create_ts = 5;
}
message GetChannelListResp{
	repeated GameChannelInfo game_channel_list = 1;
	required bool reach_end = 2;
	required uint32 user_count = 3;
    repeated GameChannelInfo novice_game_channel_list = 4;     // 注册时间小于5天的用户的专属推荐房，在该列表中的房间，不应该再出现在 game_channel_list 中
}


message GetGameMatchOptionsReq {
	required uint32 version = 1;
}
message GameMatchOptions {	
	required uint32 game_id = 1;
	required string game_name = 2;
	required string icon_url = 3;
	repeated uint32 member_count = 4;
	repeated string ann_list = 5;
	required uint32 tag_id = 6;
}
message GetGameMatchOptionsResp {	
	repeated GameMatchOptions game_match_opt_list = 1;
	required uint32 version = 2;
}

message GetGameMatchUserCountReq {
}
message GameMatchUserCount{
	required uint32 tag_id = 1;
	required uint32 user_count = 2;
}
message GetGameMatchUserCountResp {
	repeated GameMatchUserCount user_count_list = 1;
	required uint32 total = 2;
}


// 新人推荐房的展示状态
enum NOVICE_RECOMMEND_CHANNEL_STATUS
{
  ENUM_NOT_RECOMMEND = 0;            // 不是新人推荐房
  ENUM_RECOMMEND_ACTIVATE = 1;      // 在推荐位展示
  ENUM_RECOMMEND_SHUT = 2;          // 不在推荐位展示
}

message UpdateNoviceRecommendChannelStatusReq
{
  required uint32 uid = 1;
  required uint32 cid = 2;
  required uint32 recommend_status = 3;     //see NOVICE_RECOMMEND_CHANNEL_STATUS
}

message UpdateNoviceRecommendChannelStatusResp
{
}

message GetRecommendChannelStatusReq
{
  required uint32 uid = 1;
  required uint32 cid = 2;
}

message GetRecommendChannelStatusResp{
  required uint32 recommend_status = 1;  // see NOVICE_RECOMMEND_CHANNEL_STATUS
}


service ChannelGameMatch {
	option( tlvpickle.Magic ) = 15594;		// 服务监听端口号

    rpc AddChannel ( AddChannelReq ) returns( AddChannelResp ) {
        option( tlvpickle.CmdID ) = 1;              // 命令号
        option( tlvpickle.OptString ) = "c:u:m:t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-c <channel_id> -u<cur_member_count> -m<max_member_count> -t<tag_id>";    // 测试工具的命令号帮助
    }	

	rpc UserEnterChannel ( UserEnterChannelReq ) returns( UserEnterChannelResp ) {
        option( tlvpickle.CmdID ) = 2;              // 命令号
        option( tlvpickle.OptString ) = "c:m:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-c <channel_id> -m<member_count>";    // 测试工具的命令号帮助
    }	

	rpc HandleChannelEvent ( HandleChannelEventReq ) returns( HandleChannelEventResp ) {
        option( tlvpickle.CmdID ) = 3;              // 命令号
        option( tlvpickle.OptString ) = "c:e:m:t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-c <channel_id> -e<event_type> -m<mic_mode_ok> -t<new_tag_id>";    // 测试工具的命令号帮助
    }	

	rpc GetChannelList ( GetChannelListReq ) returns( GetChannelListResp ) {
        option( tlvpickle.CmdID ) = 4;              // 命令号
        option( tlvpickle.OptString ) = "t:s:n:g:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <tag_id> -s<start> -n<count> -g<game_info>";    // 测试工具的命令号帮助
    }	

	rpc GetGameMatchOptions ( GetGameMatchOptionsReq ) returns( GetGameMatchOptionsResp ) {
        option( tlvpickle.CmdID ) = 5;              // 命令号
        option( tlvpickle.OptString ) = "n:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n<version>";    // 测试工具的命令号帮助
    }	

	rpc GetGameMatchUserCount ( GetGameMatchUserCountReq ) returns( GetGameMatchUserCountResp ) {
        option( tlvpickle.CmdID ) = 6;              // 命令号
        option( tlvpickle.OptString ) = "";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
    }	

	rpc AddNoviceRecommendChannel ( AddNoviceRecommendChannelReq ) returns( AddNoviceRecommendChannelResp ) {
        option( tlvpickle.CmdID ) = 7;              // 命令号
        option( tlvpickle.OptString ) = "u:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <channel_id>";    // 测试工具的命令号帮助
    }	

	rpc RemoveNoviceRecommendChannel ( RemoveNoviceRecommendChannelReq ) returns( RemoveNoviceRecommendChannelResp ) {
        option( tlvpickle.CmdID ) = 8;              // 命令号
        option( tlvpickle.OptString ) = "u:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <channel_id>";    // 测试工具的命令号帮助
    }	

	rpc GetNoviceRecommendChannel ( GetNoviceRecommendChannelReq ) returns( GetNoviceRecommendChannelResp ) {
        option( tlvpickle.CmdID ) = 9;              // 命令号
        option( tlvpickle.OptString ) = "";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
    }	

	rpc UpdateNoviceRecommendChannelStatus ( UpdateNoviceRecommendChannelStatusReq ) returns( UpdateNoviceRecommendChannelStatusResp ) {
        option( tlvpickle.CmdID ) = 10;              // 命令号
        option( tlvpickle.OptString ) = "";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
    }	

	rpc GetRecommendChannelStatus ( GetRecommendChannelStatusReq ) returns( GetRecommendChannelStatusResp ) {
        option( tlvpickle.CmdID ) = 11;              // 命令号
        option( tlvpickle.OptString ) = "";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
    }	
	
}
