syntax="proto2";

package channelol.asyncjob;

enum Type{
    ET_ADD_CHANNEL_MEMER = 1;       /* 用户进入频道 */
    ET_REMOVE_CHANNEL_MEMER = 2;    /* 用户退出频道 */
    ET_MEMBER_RANK_CHANGE = 3;
	ET_EXPIRED_MEMBER_LIST = 4;     /*  超时用户列表 */
    ET_REMOVE_EXPIRE_MEMBER = 5;    /*  用户超时 */
    ET_CHANNEL_DISMISS = 6;         /*  频道 解散 */
}

// 房间有人进入
message AddMemberNotify
{
	required uint32 channel_id = 1;
	required uint32 uid = 2;
	optional uint32 app_id = 3;
	optional uint32 channel_type = 4;     // 房间类型
    optional uint32 channel_display_id = 5; 
    optional uint32 at = 6; 
    optional uint64 at_ms = 7; 
    optional bool is_admin = 8;
	optional uint32 remain_online_member = 9;    // 房间剩余人数
	optional uint32 remain_online_admin = 10;    // 房间剩余管理员人数
	optional bool is_pwd = 11;                   // 房间是否上锁
}
message RemoveMemberNotify
{
	required uint32 channel_id = 1;
	required uint32 uid = 2;
	required uint32 app_id = 3;
	required uint32 channel_type = 4;            // 房间类型
    required uint32 duration = 5;                //seconds, 在线时长
	optional uint32 remain_online_member = 6;    // 房间剩余人数
	optional uint32 remain_online_admin = 7;     // 房间剩余管理员人数
    optional bool is_admin = 8;
    optional uint32 channel_display_id = 9; 
    optional uint32 at = 10; 
    optional uint64 at_ms = 11; 
    optional uint32 channel_creater_uid = 12; 

}

message ExpiredMembersNotify {
    repeated uint32 uid_list = 1;
}

message RemoveExpiredMemberNotify {
    required RemoveMemberNotify notify = 1;
}

message ChannelDismissNotify {
    required uint32 channel_id = 1;
	required uint32 app_id = 2;
	required uint32 channel_type = 3;     // 房间类型
    repeated uint32 uid_list = 4;
}


// 废弃
message MemberRankChangeNotify
{
	required uint32 channel_id = 1;
	required uint32 app_id = 2;
    required uint32 member_count = 3;
}