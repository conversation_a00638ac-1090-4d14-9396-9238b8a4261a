syntax = "proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package channelOL;



// 房间成员
enum ChannelOlMemberFlagBitmap
{
	CHANNELOL_IS_ADMIN    = 0x1;
	CHANNELOL_IS_HOLDMIC  = 0x2;
	CHANNELOL_IS_MUTE     = 0x4;
	CHANNELOL_IS_PCHELPER = 0x8;
}

message ChannelMember {
	required uint32 uid  = 1;     // 成员uid

	optional bool is_holdmic = 2; // 废弃
	optional bool is_mute = 3;    // 废弃
	
	optional uint32 cost_value   = 4;    // 没有使用
	optional uint32 ts   = 5;            // 没有使用
	
	optional bool is_have_pc_helper = 6;// 废弃
	optional bool is_robot = 7;         // 废弃
	
	optional uint32 flag = 8;   // ChannelOlMemberFlagBitmap
}



// 获取用户当前房间 （ 废弃接口 请使用 GetUserChannelIdReq ）
message GetUserChannelReq
{
	required uint32 uid = 1;
}

message GetUserChannelResp
{
	required uint32 channel_id = 1;
	optional uint32 app_id = 2; // 废弃

	// 房间成员属性 内容数值仅供参考
	optional ChannelMember member_info = 3;// 废弃
}


// 获取房间成员列表
message GetChannelMemberListReq
{
	required uint32 channel_id = 1;
	required uint32 start_idx = 2;
	required uint32 limit = 3;
}

message GetChannelMemberListResp
{
	repeated ChannelMember member_list = 1;
	required uint32 all_size = 2;
}

// 获取房间成员uid列表
message GetChannelMemberUidListReq
{
    required uint32 channel_id = 1;
    required uint32 start_idx = 2;
    required uint32 limit = 3;
}

message GetChannelMemberUidListResp
{
    repeated uint32 member_uid_list = 1;
}

// 批量获取一组房间的指定个数成员列表
message ChannelMemberListInfo
{
	required uint32 channel_id = 1;
	repeated ChannelMember member_list = 2;
	required uint32 all_size = 3;
}
message BatchGetChannelMemberListReq
{
	repeated uint32 channel_id_list = 1;
	required uint32 member_count = 2; 
}

message BatchGetChannelMemberListResp
{
	repeated ChannelMemberListInfo channelmemberinfo_list = 1;
}

// PC助手加入房间(前提是该用户的手机版本已经进入了房间)
message NotifyPcHelperJoinReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
}

message NotifyPcHelperJoinResp
{
}

//贵族相关信息
message NobilityInfo{
    optional uint32 level = 1; //贵族等级
    optional uint64 value = 2; //贵族值
    optional uint64 keep_value = 3; //贵族保级值
    optional uint32 cycle_ts = 4; //贵族等级获得时间戳
    optional bool invisible = 5; //是否隐身状态
}

//
//move from channelsvr, 2018.06    
//
message AddChannelMemberReq{
    optional uint32 app_id = 1;
    required uint32 channel_id = 2;
    required uint32 channel_type = 3;
    required uint32 uid = 4;

    // 下面都是进房携带的附加信息 基本用来做push
    optional uint32 history_gift_cost = 5;
    optional bool is_mute = 6;
    optional bool is_robot = 7;
    optional bool is_pchelper = 8; //reserved
    optional bool is_admin = 9;
    optional uint32 channel_display_id = 10;
    optional bool is_room_has_pwd = 11;
    optional uint32 market_id = 12;
    optional NobilityInfo nobility_info = 13; //贵族相关信息
    optional uint32 source = 14;            //进房来源
    optional uint32 follow_friend_uid = 15; //跟随进房uid

    optional bool hide_footprint = 16; //隐藏足迹
    optional uint32 last_channel_id = 17; // 从其他房间进房的情况，携带上一个房间id
    optional string channel_view_id = 18;
}

message AddChannelMemberResp{
    optional bool is_already_in = 1; 
    optional uint32 member_size = 2;
    optional uint32 admin_member_size = 3;
    optional uint64 server_ms_ts = 4; // 2018-11-30 新加字段
}
message RemoveChannelMemberReq{
    required uint32 app_id = 1; 
    required uint32 channel_id = 2;
    required uint32 channel_type = 3;
    required uint32 uid = 4;
    
    // 下面都是退房携带的附加信息 基本用来做push
    optional bool is_admin = 5;
    optional bool is_change_channel = 6;
    optional uint32 channel_display_id = 7;
    optional uint32 channel_create_uid = 8;

    //用来区分是被踢出房还是自己退房
    optional uint32 op_uid         = 9;
    optional string channel_view_id = 10;
}
message RemoveChannelMemberResp{
    optional bool exist = 1; 
    optional uint32 duration = 2;     // 用户在房时长 seconds
    optional uint32 left_member_count = 3;
    optional uint32 left_admin_count = 4;
    optional uint64 server_ms_ts = 5; 
}
message RemoveChannelReq{
    optional uint32 app_id = 1;//reserved
    required uint32 channel_id = 2;
    optional bool return_members = 3;
}
message RemoveChannelResp{
    repeated uint32 members = 1;
}
message MemberFootprint{
    required uint32 uid = 1;
    required uint32 timestamp = 2;
}
message GetChannelMemberFootprintReq{
    optional uint32 app_id = 1;//reserved
    required uint32 channel_id = 2;
}
message GetChannelMemberFootprintResp{
    repeated MemberFootprint footprints = 1;
}
message GetChannelMemberReq{
    optional uint32 app_id = 1;//reserved
    required uint32 channel_id = 2;
    required uint32 uid = 3;
}
message GetChannelMemberResp{
    optional ChannelMember member = 1;
}


message GetChannelMemberSizeReq{
    optional uint32 app_id = 1;//reserved
    required uint32 channel_id = 2;
}
message GetChannelMemberSizeResp{
    optional uint32 size = 1;
}

message BatchGetChannelMemberSizeReq
{
	repeated uint32 channel_id_list = 1;
}

message BatchGetChannelMemberSizeResp
{
	repeated uint32 membersize_list = 1;
}

message GetUserChannelIdReq{
    required uint32 uid = 1;
}
message UserChannelId{
    required uint32 uid = 1;
    optional uint32 app_id = 2; //  reserved 无用
    required uint32 channel_id = 3;
    optional uint32 flag = 4;   // ChannelOlMemberFlagBitmap
};
message GetUserChannelIdResp{
    optional UserChannelId result = 1;
}
message BatchGetUserChannelIdReq{
    repeated uint32 uid_list = 1;
} 
message BatchGetUserChannelIdResp{
    repeated UserChannelId results = 1;
}
message AddChannelAdminReq{
    optional uint32 app_id = 1;//reserved
    required uint32 channel_id = 2;
    required uint32 uid = 3;
}
message AddChannelAdminResp{
}
message RemoveChannelAdminReq{
    optional uint32 app_id = 1;//reserved
    required uint32 channel_id = 2;
    required uint32 uid = 3;
}
message RemoveChannelAdminResp{
}
message GetChannelAdminSizeReq{
    optional uint32 app_id = 1;//reserved
    required uint32 channel_id = 2;
}
message GetChannelAdminSizeResp {
    optional uint32 size = 1;
}
message CleanChannelAdminReq{
    optional uint32 app_id = 1;//reserved
    required uint32 channel_id = 2;
}
message CleanChannelAdminResp{
}

message ChannelStat{
	required uint32 online_peak = 1;
	required uint32 online_peak_at = 2;
}
message GetChannelStatReq{
    optional uint32 app_id = 1;//reserved
    required uint32 channel_id = 2;
}
message GetChannelStatResp{
    optional ChannelStat stat = 1;
}
message HoldMicReq{
    required uint32 uid = 1;
    optional uint32 app_id = 2;//reserved
    required uint32 channel_id = 3;
}
message HoldMicResp{
}
message ReleaseMicReq{
    required uint32 uid = 1;
    optional uint32 app_id = 2;//reserved
    required uint32 channel_id = 3;
}
message ReleaseMicResp{
}
message MuteChannelMemberReq{
    optional uint32 app_id = 1;//reserved
    required uint32 channel_id = 2;
    required uint32 uid = 3;
}
message MuteChannelMemberResp{
}
message UnmuteChannelMemberReq{
    optional uint32 app_id = 1;//reserved
    required uint32 channel_id = 2;
    required uint32 uid = 3;
}
message UnmuteChannelMemberResp{
}

message GetAllChannelOnlineMemberCountReq {
}

message GetAllChannelOnlineMemberCountResp {
    required uint32 online_count = 1;
}

message GetLiveChannelListReq {
}

message GetLiveChannelListResp {
    repeated uint32 channel_id_list = 1;
}

message CheckUserIsChannelAdminReq {
    optional uint32 app_id = 1;
    required uint32 channel_id = 2;
    repeated uint32 uid_list = 3;
}
message UserAdminRet{
    required uint32 uid = 1;
    required bool is_admin = 2;
}
message CheckUserIsChannelAdminResp {
    repeated UserAdminRet ret_list = 1;
}

message ExpireChannelMemberReq {
  repeated uint32 uid_list = 1;
}
message ExpireChannelMemberResp {
}

service channelOL {
	option( tlvpickle.Magic ) = 15205;		// 服务监听端口号
	
    // deprecated, @see channelheartbeat.proto
    //rpc ChannelHeartbeat( ChannelHeartbeatReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
	//	option( tlvpickle.CmdID ) = 1;										
    //    option( tlvpickle.OptString ) = "u:x:";						
    //    option( tlvpickle.Usage ) = "-u <uid> -x <channel_id>";
	//}

	rpc GetUserChannel( GetUserChannelReq ) returns( GetUserChannelResp ){
        option( tlvpickle.CmdID ) = 2;										
        option( tlvpickle.OptString ) = "u:";						
        option( tlvpickle.Usage ) = "-u <uid>";
	}
	
	rpc GetChannelMemberList( GetChannelMemberListReq ) returns( GetChannelMemberListResp ){
        option( tlvpickle.CmdID ) = 3;										
        option( tlvpickle.OptString ) = "x:b:l:";						
        option( tlvpickle.Usage ) = "-x <channel id> [-b <begin idx>] [-l <limit >]";
	}
    
	rpc NotifyPcHelperJoin( NotifyPcHelperJoinReq ) returns( NotifyPcHelperJoinResp ){
        option( tlvpickle.CmdID ) = 4;										
        option( tlvpickle.OptString ) = "u:x:";						
        option( tlvpickle.Usage ) = "-u <uid> -x <channel id>";
	}
	
	// 批量获取一组房间的指定个数成员列表 deprecated @see channelolmember.proto
	rpc BatchGetChannelMemberList( BatchGetChannelMemberListReq ) returns( BatchGetChannelMemberListResp ){
        option( tlvpickle.CmdID ) = 5;										
        option( tlvpickle.OptString ) = "x:l:";						
        option( tlvpickle.Usage ) = "-x <channel_id> -l <member count limit>";
	}
    

    //
    //move from channelsvr, 2018.06    
    //
    rpc AddChannelMember( AddChannelMemberReq ) returns ( AddChannelMemberResp ){
        option( tlvpickle.CmdID ) = 11;										
        option( tlvpickle.OptString ) = "x:u:t:s:mrpa";
        option( tlvpickle.Usage ) = "-x <channel_id> -u <uid> -t <channel_type> [-s <gift coSt>] [-m, mute?] [-r, robot?] [-p, pchelper?] [-a, admin?]";
    }
    rpc RemoveChannelMember( RemoveChannelMemberReq ) returns ( RemoveChannelMemberResp ){
        option( tlvpickle.CmdID ) = 12;										
        option( tlvpickle.OptString ) = "u:x:t:as";						
        option( tlvpickle.Usage ) = "-x <channel_id> -u <uid> -t <channelType> [-a,admin?] [-s,change channel?] ";
    }
    rpc RemoveChannel( RemoveChannelReq ) returns ( RemoveChannelResp ) {
        option( tlvpickle.CmdID ) = 13;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel_id>";
    }
    rpc GetChannelMemberFootprint ( GetChannelMemberFootprintReq ) returns ( GetChannelMemberFootprintResp ) {
        option( tlvpickle.CmdID ) = 14;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel_id> ";
    }
    rpc GetChannelMember ( GetChannelMemberReq ) returns ( GetChannelMemberResp ) {
        option( tlvpickle.CmdID ) = 15;										
        option( tlvpickle.OptString ) = "x:u:";						
        option( tlvpickle.Usage ) = "-x <channel_id> -u <uid> ";
    }
    rpc GetChannelMemberSize( GetChannelMemberSizeReq ) returns ( GetChannelMemberSizeResp ) {
        option( tlvpickle.CmdID ) = 16;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel_id> ";
    }
    rpc GetUserChannelId ( GetUserChannelIdReq ) returns ( GetUserChannelIdResp ) {
        option( tlvpickle.CmdID ) = 17;										
        option( tlvpickle.OptString ) = "u:";						
        option( tlvpickle.Usage ) = "-u <uid> ";
    }
    rpc BatchGetUserChannelId ( BatchGetUserChannelIdReq ) returns ( BatchGetUserChannelIdResp ) {
        option( tlvpickle.CmdID ) = 18;										
        option( tlvpickle.OptString ) = "u:";						
        option( tlvpickle.Usage ) = "-u <uid1, uid2, ...> ";
    }


    // 在channelol在线信息中 记录房间当前在线的管理员信息
    rpc AddChannelAdmin ( AddChannelAdminReq ) returns ( AddChannelAdminResp ) {
        option( tlvpickle.CmdID ) = 19;										
        option( tlvpickle.OptString ) = "x:u:";						
        option( tlvpickle.Usage ) = "-x <channel_id> -u <uid> ";
    }
    rpc RemoveChannelAdmin ( RemoveChannelAdminReq ) returns ( RemoveChannelAdminResp ) {
        option( tlvpickle.CmdID ) = 20;										
        option( tlvpickle.OptString ) = "x:u:";
        option( tlvpickle.Usage ) = "-x <channel_id> -u <uid> ";
    }
    rpc GetChannelAdminSize ( GetChannelAdminSizeReq ) returns ( GetChannelAdminSizeResp ) {
        option( tlvpickle.CmdID ) = 21;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel_id> ";
    }
    rpc CleanChannelAdmin ( CleanChannelAdminReq ) returns ( CleanChannelAdminResp ) {
        option( tlvpickle.CmdID ) = 22;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel_id> ";
    }
    
    rpc GetChannelStat( GetChannelStatReq ) returns ( GetChannelStatResp ) {
        option( tlvpickle.CmdID ) = 23;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel_id> ";
    }
    
    // 在channelol在线信息中 记录用户的是否上麦
    rpc HoldMic ( HoldMicReq ) returns ( HoldMicResp ) {
        option( tlvpickle.CmdID ) = 24;										
        option( tlvpickle.OptString ) = "x:u:";						
        option( tlvpickle.Usage ) = "-x <channel_id> -u <uid> ";
    }
    rpc ReleaseMic ( ReleaseMicReq ) returns ( ReleaseMicResp ) {
        option( tlvpickle.CmdID ) = 25;										
        option( tlvpickle.OptString ) = "x:u:";						
        option( tlvpickle.Usage ) = "-x <channel_id> -u <uid>";
    }
    
    // 在channelol在线信息中 记录用户的是否被禁言
    rpc MuteChannelMember ( MuteChannelMemberReq ) returns ( MuteChannelMemberResp ) {
        option( tlvpickle.CmdID ) = 26;										
        option( tlvpickle.OptString ) = "x:u:";						
        option( tlvpickle.Usage ) = "-x <channel_id> -u <uid> ";
    }
    rpc UnmuteChannelMember ( UnmuteChannelMemberReq ) returns ( UnmuteChannelMemberResp ) {
        option( tlvpickle.CmdID ) = 27;										
        option( tlvpickle.OptString ) = "x:u:";						
        option( tlvpickle.Usage ) = "-x <channel_id> -u <uid> ";
    }
    //end, 2018.06

	
    rpc BatchGetChannelMemberSize ( BatchGetChannelMemberSizeReq ) returns ( BatchGetChannelMemberSizeResp ) {
        option( tlvpickle.CmdID ) = 29;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel_id> ";
    }
	

    rpc GetChannelMemberUidList ( GetChannelMemberUidListReq ) returns ( GetChannelMemberUidListResp ) {
        option( tlvpickle.CmdID ) = 31;
        option( tlvpickle.OptString ) = "x:b:l:";
        option( tlvpickle.Usage ) = "-x <channel id> [-b <begin idx>] [-l <limit >]";
    }

    rpc CheckUserIsChannelAdmin( CheckUserIsChannelAdminReq ) returns ( CheckUserIsChannelAdminResp ) {
        option( tlvpickle.CmdID ) = 32;
        option( tlvpickle.OptString ) = "x:u:";
        option( tlvpickle.Usage ) = "-x <channel_id> -u <uid1, uid2,...>";
    }

    // deprecated, @see channelheartbeat.proto
    //rpc RemoveUserHeartbeat( RemoveUserHeartbeatReq ) returns (RemoveUserHeartbeatResp) {
    //    option( tlvpickle.CmdID ) = 33;
    //    option( tlvpickle.OptString ) = "u:";
    //    option( tlvpickle.Usage ) = "-u <uid1>";
    //}
    
    rpc ExpireChannelMember( ExpireChannelMemberReq ) returns (ExpireChannelMemberResp) {
        option( tlvpickle.CmdID ) = 34;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid1>";
    }
}

