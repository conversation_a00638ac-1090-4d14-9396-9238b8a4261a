syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package authlogger;

enum AuthOpType {
    AUTH_OP_NIL = 0;
    AUTH_OP_REG = 1;                // 注册
    AUTH_OP_MANUAL = 2;             // 手工登录
    AUTH_OP_AUTO = 3;               // 自动登录
    AUTH_OP_SDK_ACTIVATE= 4;        // SDK激活
}

message AuthInfo {
    uint32 uid = 1;
    uint32 op_type = 2;             // AuthOpType 
    uint32 op_time = 3;              
    int32 result = 4;               // 操作结果

    string login_account = 5;
    uint32 third_party_type = 6;
    string third_party_openid = 7;

    string imei = 8;
    string idfa = 9;                // IOS IDFA
    string os_ver = 10;             // 操作系统版本
    string os_type = 11;            // 操作系统类型
    string device_id = 12;          // 设备号
    string device_model = 13;       // 机器型号

    uint32 client_ver = 14;         // 客户端版本类型
    uint32 client_type = 15;        // 客户端类型(仅限TT -- 0: Android, 1: iOS)
    string client_channel_id = 16;  // 客户端渠道号
    string pkg_signature = 17;      // 包签名
    uint32 terminal_type = 18;      // 终端类型(包含平台/操作系统/APPID)

    string client_ip = 19;
    uint32 client_port = 20;
    string proxy_ip = 21;
    uint32 proxy_port = 22;
    uint32 client_id = 23;

    string phone = 24;
}

message AppendReq {
    AuthInfo info = 1;
}

message AppendResp {
}

message Match {
    repeated uint32 uid_list = 1;
    repeated string imei_list = 2;
    repeated string idfa_list = 3;
    repeated string device_id_list = 4;
    repeated string client_ip_list = 5;
}

message FindReq {
    Match match = 1;
    uint32 begin_at = 2;
    uint32 end_at = 3;
    uint32 limit = 4;
}

message FindResp {
    repeated AuthInfo info_list = 1;
}

service AuthLogger {
    option( tlvpickle.Magic ) = 15065;

    rpc Append ( AppendReq ) returns ( AppendResp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:o:";
        option( tlvpickle.Usage ) = "-u <uid> -o <op_type>";
    }

    rpc Find ( FindReq ) returns ( FindResp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:m:a:d:i:b:e:l:o:h";
        option( tlvpickle.Usage ) = "-h <help>";
    }

}
