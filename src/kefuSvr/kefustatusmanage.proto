syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";


package KefuStatusManage;

enum KEFU_STATUS
{
	OFF=0;	//客服休息
	ON=1;	//客服上班
}

message kefuStatus
{
	required uint32 cms_id=1;		//客服CMS_ID,与uid11对应，唯一的
	required uint32 uid=2;			// TT内部uid
	required string ttid=3;			// TT数字帐号 Aliens 
	required string service_name=4;	// 工单客服号
	required string name=5;			// 使用者
	required uint32 status=6;		// 值班状态
	optional string ttusername=7;	// TT username
	
}

message GetAllKefuStatusResp
{
	repeated kefuStatus kefu_status_list=1;
}

message SetKefuStatusReq				//客服状态输入
{
	required kefuStatus status=1;	
	required uint32 tag=2;              //标志，加入，删除 		
}

message Msg
{
	required uint32 from_id=1;
	required uint32 to_id=2;
	required bytes content=3;
	optional uint32 ser_db_msgId=4;
	
}

message AddMsgReq      //每次消息发送将其存在数据库中
{
	required Msg msg=1;
}

message GetMsgReq		//通过id去获取消息记录
{
	required uint32 client_id=1;
	required uint32 ser_db_msgId=2;
	required uint32 get_msg_size=3;
}
message GetMsgResp
{
	repeated Msg msg_list=1;
}


message GetRelationUserToKefuReq
{
	required uint32 uid=1;
	optional uint32 kefu_uid=2;
}

message GetRelationUserToKefuResp
{
	required uint32 uid=1;
	required uint32 kefu_uid=2;
	required string kefu_username=3;	// TT username
}

service KefuStatusManage
{
	option( tlvpickle.Magic ) = 14088;

	// 设置客服状态
	rpc SetKefuStatus(SetKefuStatusReq)returns( tlvpickle.SKBuiltinEmpty_PB){
	option( tlvpickle.CmdID ) = 1;						
    option( tlvpickle.OptString ) = "m:u:i:k:n:s:t:";				
    option( tlvpickle.Usage ) = "-m<cms_id> -u<uid>-i<ttid> -k<service_name> -n<name> -s <status> -t<tag>";	
	}

	// 获取全量客服状态列表
	rpc GetAllKefuStatus( tlvpickle.SKBuiltinEmpty_PB)returns(GetAllKefuStatusResp){
	option( tlvpickle.CmdID ) = 2;						
    option( tlvpickle.OptString ) = "";				
    option( tlvpickle.Usage ) = "";				
	}

	// 客服消息全存储
	rpc AddMsg2Db(AddMsgReq)returns(tlvpickle.SKBuiltinEmpty_PB){
	option( tlvpickle.CmdID ) = 3;						
    option( tlvpickle.OptString ) = "t:k:m:o:h:y:s:a:";	
    option( tlvpickle.Usage ) = "-t<client> -k<kefu> -m<from_name> -o<to_name> -h<content>-y<type>-s<status>-a<attach>";	
	}

	rpc GetMsgFromDb(GetMsgReq)returns(GetMsgResp){
	option( tlvpickle.CmdID ) = 4;						
    option( tlvpickle.OptString ) = "t:g:s:";				
    option( tlvpickle.Usage ) = "-t<client> -g<get_msg_size> -s<ser_db_msgID>";			
	}

	// 获取用户与客服的绑定关系
	rpc GetRelationUserToKefu (GetRelationUserToKefuReq)returns(GetRelationUserToKefuResp)
	{
		option( tlvpickle.CmdID ) = 5;						
    	option( tlvpickle.OptString ) = "u:";			
    	option( tlvpickle.Usage ) = "-u <uid> ";				
	}
}
