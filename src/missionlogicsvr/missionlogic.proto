syntax="proto2";

// missionlogic.proto

option go_package = "golang.52tt.com/protocol/services/missionlogicsvr";
import "common/tlvpickle/skbuiltintype.proto";

package MissionLogic;

// 任务类型
enum MISSION_TYPE {
    GREENER = 1;		// 新手任务
    DAILY = 2;			// 日常任务
    ELITE = 3;			// 精英任务
	TIME_LIMIT = 4;		// 限时任务
}

// 限时任务子类型
enum TIME_LIMIT_MISSION_SUB_TYPE {
	TIME_LIMIT_GAME_CONTINUOUS_LOGIN = 1;		// 游戏连续登录
	TIME_LIMIT_GAME_ACCUM_LOGIN = 2;			// 游戏累计登录
	TIME_LIMIT_GAME_ACCUM_RECHARGE = 3;			// 游戏累计充值
	TIME_LIMIT_CONTINOUS_CHECK_IN = 4;			// 公会连续签到
	TIME_LIMIT_HAPPY_CENTER_LEVEL = 5;			// 欢城直播等级
	TIME_LIMIT_TT_CONTINUOUS_LOGIN = 6;			// TT连续登录
	TIME_LIMIT_TT_ACCUM_LOGIN = 7;				// TT累计登录
	TIME_LIMIT_ACCUM_CHECK_IN = 8;				// 公会累计签到
	TIME_LIMIT_GAME_RECHARGE = 9;				// 游戏单笔充值
}

// 事件类型
enum EVENT_TYPE {
	GAME_LOGIN = 1;				// 游戏登陆事件
	GAME_RECHARGE = 2;			// 游戏充值事件
	GUILD_CHECK_IN = 3;			// 公会签到事件
	HAPPY_CENTER_LEVEL = 4;		// 欢城直播等级变化事件
	TT_LOGIN = 5;				// TT登录事件
}

// 任务状态
enum MISSION_STATUS {
    IN_PROGRESS = 1;    // 进行中
    FINISHED = 2;       // 任务已经完成, 奖励未领取
    COLLECTED = 3;      // 已领取奖励
}

// 通知类型(MASK, 各枚举字段需要用位移计算)
enum NOTIFY_MASK {
	NONE = 0;					// 无通知(如任务还未解锁时通过其他方式完成了)
	RED_POINT = 1;				// 任务入口显示红点
	ALTER = 2;					// 弹框提示
	// NEW = 4;					// 任务入口显示NEW(Reserved)
	// NOTIFICATION_CENTER = 8;	// 通知中心显示(Reserved)
}

// 重置进行中的任务的进度的原因
enum RESET_MISSION_REASON {
	GUILD_QUIT = 1;
}

// 任务奖励
message MissionBonus {
	optional uint32 experience = 1;
	optional uint32 red_diamonds = 2;
	optional uint32 medal = 3;
}

message HandleMissionReq {
	required uint32 uid = 1;
	required uint32 cmd_id = 2;
	required bytes cmd_body = 3;
	optional uint32 client_type = 4;
	optional uint32 client_ver = 5;
}

message HandleMissionResp {
}

message HandleGuildJoinMissionReq {
	required uint32 uid = 1;
	required uint32 guild_id = 2;
}

message HandleGuildJoinMissionResp {
}

message HandleBindPhoneMissionReq {
	required uint32 uid = 1;
}

message HandleChannelOnlineMissionReq {
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	optional uint32 channel_type = 3;	// ga::ChannelType
}

message HandleChannelOnlineMissionResp {
}

message MissionGroup {
	required uint32 group_id = 1;	// 任务组id
	required string name = 2;		// 任务组名
	required uint32 type = 3;		// 任务组类型
	required bool visible = 4;		// 任务组是否可见
	required bool unlocked = 5;		// 任务组是否解锁
}

message Mission {
	required uint32 mission_id = 1;
	required string name = 2;
	required uint32 start_time = 3;
	required uint32 expire_time = 4;
	required MissionBonus bonus = 5;		// 奖励
	required uint32 status = 6;				// 状态
	required uint32 finish_count = 7;		// 完成次数
	required uint32 required_count = 8;		// 需要次数
	required string description = 9;		// 描述
	required string strategy = 10;			// 攻略
	required string identifier = 11;		// 需要执行的次数(非重复任务为1)
	optional string client_url = 12;		// for js nav
	optional uint32 reward_count = 13;		// 已领取奖励的进度
	optional uint32 effect_begin = 14;		// 任务生效起始时间
	optional uint32 effect_end = 15;		// 任务生效结束时间
	optional uint32 is_accept = 16;			// 是否已接受任务
	optional string sub_name = 17;			// 子名称（副标题）
	optional string special_bonus_name = 18;	// 完成任务后的特殊奖励名称
	optional MissionBonus special_bonus = 19;	// 完成任务后的特殊奖励
	optional uint32 base_reward_count = 20;		// 阶段领奖需要的完成次数（配置项，与reward_count无关）
	optional uint32 sub_name_type = 21;			// 子名称样式（副标题样式） 0.红字 1.灰字
	optional uint32 max_collect_count = 22;		// 领奖次数上限
}

message MissionGroupDetail {
	required MissionGroup group = 1;
	repeated Mission mission_list = 2;
}

message Platform {
    enum Values {
        Android = 1;
        iOS = 2;
    }
}

message GetUserMissionsReq {
	required uint32 uid = 1;
    optional uint32 platform = 2;
	optional uint32 client_version = 3;
	optional bool show_game_center = 4;
}

message GetUserMissionsResp {
	repeated MissionGroupDetail mission_group_list = 1;
}

message GetLatestUserMissionReq {
	required uint32 uid = 1;
	required uint32 mission_id = 2;
	optional uint32 platform = 3;
}

message GetLatestUserMissionResp {
	optional MissionGroupDetail mission_group = 1;
}

message CollectMissionBonusReq {
	required uint32 uid = 1;
	required string mission_identifier = 2;
    optional uint32 platform = 3;
	optional bool show_game_center = 4;
}

message CollectMissionBonusResp {
	required GrowInfo grow_info			= 1;	// 领取后的成长信息
	required MissionBonus base_bonus	= 2;	// 基础奖励
	repeated MedalBuff medal_buff_list	= 3;	// 勋章buff
	optional uint32 new_medal_id 		= 4;	// 如果有获取到新的勋章, 则会填入勋章ID
	optional uint32 mission_id			= 5;	// 任务id
	optional MissionBonus final_bonus	= 6;	// 实际奖励
	optional MissionBonus no_buff_bonus	= 7; 	// 不算加成部分的最终奖励
}

// 成长信息
message GrowInfo {
	required int32 exp 					= 1;		// 最新经验值
	required uint32 level				= 2;		// 最新等级
	required uint32 current_level_exp_min = 3;		// 本等级所需的最小经验
	required uint32 current_level_exp_max = 4;		// 本等级可获取的最大经验
	required int32 currency				= 5;		// 最新红钻
}

message GetUserMissionGuideReq {
	required uint32 uid = 1;
    optional uint32 platform = 2;
	optional bool show_game_center = 3;
	optional uint32 app_id = 4;
	optional uint32 market_id = 5;
}

message GetUserMissionGuideResp {
	required string guide = 1;
}

// 加成比例, 需要除10000
message BuffRatio {
	required uint32 experience = 1;
	required uint32 red_diamonds = 2;
}

// 加成数量
message Buff {
	required uint32 experience = 1;
	required uint32 red_diamonds = 2;
}

message MedalBuffRatio {
	required uint32 	medal_id = 1;
	required BuffRatio 	buff_ratio = 2;
}

message MedalBuff {
	required uint32	medal_id = 1;	// 勋章id
	required Buff	buff = 2;		// that is buff!
}


////////////////////////////////////////////////
// Static Mission Configuration
////////////////////////////////////////////////

message StaticMission {
    required uint32 mission_id = 1;
    required string name = 2;
    required string description = 3;
    required uint32 effect_begin = 4;
    required uint32 effect_end = 5;
    required uint32 min_client_version = 6;
    required uint32 add_time = 7;
    required MissionBonus bonus = 8;
    optional uint32 reset_time = 9;
	optional uint32 event_type = 10;	// EVENT_TYPE
	optional uint32 sub_type = 11;		// TIME_LIMIT_MISSION_SUB_TYPE
	repeated uint64 ly_game_id_list = 12;			// 联运游戏ID
	optional string sub_name = 13;		// 子名称（副标题）
	optional uint32 recharge_count = 14;	// 充值金额
	optional string special_bonus_name = 15;	// 完成任务后的特殊奖励名称
	optional MissionBonus special_bonus = 16;	// 完成任务后的特殊奖励
	optional uint32 sub_name_type = 17;			// 子名称样式（副标题样式） 0.红字 1.灰字
	optional uint32 platform = 18;				// Platform(0.都可见)
}

message StaticMissionGroup {
    required uint32 group_id = 1;
    required string name = 2;
    required uint32 type = 3;
    repeated StaticMission mission_list = 4;
}

message StaticMissionConfig {
    required uint32 update_time = 1;
    repeated StaticMissionGroup mission_group_list = 2;
}

message GetStaticMissionReq {
	required uint32 mission_id = 1;
}

message GetStaticMissionResp {
	required StaticMission mission = 1;
}

message IncreaseMissionFinishCountReq {
    required uint32 uid = 1;
    required uint32 mission_id = 2;
    required uint32 count = 3;
}

message ResetInProgressMissionReq {
	required uint32 uid = 1;
	optional uint32 mission_id = 2;
	optional uint32 type = 3;	// RESET_MISSION_REASON
}

// 限时任务相关的东东，已废弃
message HandleTimeLimitMissionReq {
	required uint32 uid = 1;
	required uint32 mission_id = 2;
	optional string extend = 3;
}
message HandleTimeLimitMissionResp {
}
message AcceptMissionReq {
	required uint32 uid = 1;
	required uint32 mission_id = 2;
	optional uint32 platform = 3;
}
message GetStaticTimeLimitGameMissionReq {
	optional uint32 last_update_time = 1;	// 0.强制获取限时游戏任务
	optional uint32 event_type = 2;	//EVENT_TYPE
}
message GetStaticTimeLimitGameMissionResp {
	required uint32 update_time = 1;
	repeated StaticMission mission_list = 2;	//update_time与请求的last_update_time一致时，不会返回任务数据
}
message HandleEventReq {
	required uint32 uid = 1;
	required uint32 event_type = 2;	// EVENT_TYPE
	optional uint64 ly_game_id = 3;	// 游戏id
	optional uint32 recharge_penny = 4;	//总充值金额，单位：分
	optional uint32 level = 5;		// 等级（游戏、欢城、tt）
	optional uint32 cash_recharge_penny = 6;	// 现金充值金额，单位：分
}

message HandleEnterChannelFromRankMissionReq {
    required uint32 uid = 1;
}

message HandleEnterChannelFromRankMissionResp {
}

service MissionLogic {

	option( tlvpickle.Magic ) = 15050;		// 服务监听端口号

	rpc HandleMission( HandleMissionReq ) returns( HandleMissionResp ) {
		option( tlvpickle.CmdID ) = 1;			// 命令号
        option( tlvpickle.OptString ) = "";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    		// 测试工具的命令号帮助
	}

	rpc GetUserMissions( GetUserMissionsReq ) returns( GetUserMissionsResp ) {
		option( tlvpickle.CmdID ) = 2;			// 命令号
        option( tlvpickle.OptString ) = "u:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";	// 测试工具的命令号帮助
	}

	rpc CollectMissionBonus( CollectMissionBonusReq ) returns( CollectMissionBonusResp ) {
		option( tlvpickle.CmdID ) = 3;			// 命令号
        option( tlvpickle.OptString ) = "";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    		// 测试工具的命令号帮助
	}

	rpc GetUserMissionGuide( GetUserMissionGuideReq ) returns( GetUserMissionGuideResp ) {
		option( tlvpickle.CmdID ) = 4;			// 命令号
        option( tlvpickle.OptString ) = "u:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";    		// 测试工具的命令号帮助
	}

    rpc GetStaticMissionConfig( tlvpickle.SKBuiltinEmpty_PB ) returns( StaticMissionConfig ) {
        option( tlvpickle.CmdID ) = 5;              // 命令号
        option( tlvpickle.OptString ) = "";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";     		// 测试工具的命令号帮助
    }

    rpc GetStaticMission( GetStaticMissionReq ) returns( GetStaticMissionResp ) {
    	option( tlvpickle.CmdID ) = 6;					// 命令号
        option( tlvpickle.OptString ) = "m:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <missionId>";	// 测试工具的命令号帮助
    }

    rpc HandleGuildJoinMission( HandleGuildJoinMissionReq ) returns( HandleGuildJoinMissionResp ) {
    	option( tlvpickle.CmdID ) = 7;					// 命令号
        option( tlvpickle.OptString ) = "u:g:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id>";	// 测试工具的命令号帮助
    }

    rpc IncreaseMissionFinishCount( IncreaseMissionFinishCountReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 8;					// 命令号
        option( tlvpickle.OptString ) = "u:m:n:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <mission_id> -n <count>";	// 测试工具的命令号帮助
    }
	
	rpc HandleBindPhoneMission( HandleBindPhoneMissionReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 9;					// 命令号
        option( tlvpickle.OptString ) = "u:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";	// 测试工具的命令号帮助
    }
	
	rpc GetLatestUserMission( GetLatestUserMissionReq ) returns ( GetLatestUserMissionResp ) {
        option( tlvpickle.CmdID ) = 10;					// 命令号
        option( tlvpickle.OptString ) = "u:m:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <mission_id>";	// 测试工具的命令号帮助
    }
	
		rpc AcceptMission( AcceptMissionReq ) returns ( GetLatestUserMissionResp ) {
        option( tlvpickle.CmdID ) = 11;					// 命令号
        option( tlvpickle.OptString ) = "u:m:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <mission_id>";	// 测试工具的命令号帮助
    }
	
	rpc HandleTimeLimitMission( HandleTimeLimitMissionReq ) returns ( HandleTimeLimitMissionResp ) {
        option( tlvpickle.CmdID ) = 12;					// 命令号
        option( tlvpickle.OptString ) = "u:m:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <mission_id>";	// 测试工具的命令号帮助
    }
	
	rpc GetStaticTimeLimitGameMission( GetStaticTimeLimitGameMissionReq ) returns ( GetStaticTimeLimitGameMissionResp ) {
        option( tlvpickle.CmdID ) = 13;					// 命令号
        option( tlvpickle.OptString ) = "t:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <event_type>";	// 测试工具的命令号帮助
    }
	
	rpc HandleEvent( HandleEventReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 14;					// 命令号
        option( tlvpickle.OptString ) = "u:t:g:n:l:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t <event_type> [-g <game_id> -n <recharge_penny> -m <cash_recharge_penny> -l <level>]";	// 测试工具的命令号帮助
    }
		
	rpc ResetInProgressMission( ResetInProgressMissionReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 15;					// 命令号
        option( tlvpickle.OptString ) = "u:m:t:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> [-t <type 1.guild>  -m <mission_id>]";	// 测试工具的命令号帮助
    }
	
	rpc HandleChannelOnlineMission( HandleChannelOnlineMissionReq ) returns ( HandleChannelOnlineMissionResp ) {
        option( tlvpickle.CmdID ) = 16;					// 命令号
        option( tlvpickle.OptString ) = "u:x:t:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -x <channel_id> -t <channel_type>";	// 测试工具的命令号帮助
    }

    rpc HandleEnterChannelFromRankMission( HandleEnterChannelFromRankMissionReq ) returns ( HandleEnterChannelFromRankMissionResp ) {
        option( tlvpickle.CmdID ) = 17;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid> ";
    }
}
