syntax="proto2";

package MissionConfig;

import "missionlogic.proto";

//////////////////////////////////////////////////
// 内部类型, 不对外提供接口
//////////////////////////////////////////////////

// 任务定义
message PMission {
	required uint32 mission_id = 1;			// id
	required uint32 type = 2;				// 类型, SEE MISSION_TYPE
	required string name = 3;				// 名称
	required MissionLogic.MissionBonus bonus = 4;	// 奖励
	required string description = 5;		// 描述
	required string strategy = 6;			// 攻略
	required uint32 repeat_count = 7;		// 需要执行的次数(非重复任务为1)
	required string start_time = 8;			// 起始时间(为空或按照此格式: MM-DD-YY HH:mm:ss)
	optional uint32 expire = 9;				// 0:永不过期, 相对于start_time的持续时间(秒), 当前时间超过start_time + expire的任务无法继续执行
	optional uint32 bonus_expire = 10;		// 0:永不过期, 相对于start_time的奖励领取时(秒), 当前时间超过start_time + bonus_expire的任务无法领取奖励
	optional uint32 min_client_version = 11;    // 该任务要求的客户端最低版本
	optional string client_url = 12;            // 客户端跳转url
	required uint32 add_time = 13;              // 任务添加的时间
	required uint32 notify_mask = 14;           // 通知方式
	required uint32 effect_begin = 15;          // 任务生效起始时间
	required uint32 effect_end = 16;            // 任务生效结束时间
	required uint32 group_id = 17;              // 对应的group_id
	required uint32 show_milestone_mask = 18;	// 控制任务可见性: 0 or no-assigned means always show
	required uint32 unlock_milestone_mask = 19;	// 控制任务锁定: 0 or no-assigned means always unlock
	required uint32 base_reward_count = 20;		// 阶段领奖需要的完成次数
	optional uint32 event_type = 21;			// 关联的事件类型
	optional uint32 sub_type = 22;				// 任务子类型
	repeated uint64 ly_game_id_list = 23;		// 联运游戏ID
	optional string sub_name = 24;				// 子名称（副标题）
	optional uint32 recharge_count = 25;		// 充值金额
	optional string special_bonus_name = 26;	// 完成任务后的特殊奖励名称
	optional MissionLogic.MissionBonus special_bonus = 27;	// 完成任务后的特殊奖励
	optional uint32 sub_name_type = 28;			// 子名称样式（副标题样式） 0.红字 1.灰字
	optional uint32 platform = 29;				// missionlogic::Platform(0.都可见)
	optional uint32 max_collect_count = 30;		// 领奖次数上限
}

message PMissionGroup {
	required uint32 group_id = 1;
	required string name = 2;
	required uint32 type = 3;
	repeated PMission mission_list = 4;
	required uint32 show_milestone_mask = 5;    // 控制任务组可见性: 0 or no-assigned means always show
	required uint32 unlock_milestone_mask = 6;  // 控制任务组锁定: 0 or no-assigned means always unlock
}

// 任务配置
message PMissionConfig {
	required uint32 update_time = 1;
	repeated PMissionGroup mission_group_list = 2;
}

