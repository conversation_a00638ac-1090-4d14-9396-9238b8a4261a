syntax="proto3";

import "common/tlvpickle/skbuiltintype.proto";

package anchorcontract;


enum ENUM_CONTRACT_CHANGE_TYPE
{
  ENUM_CONTRACT_CHANGE_ACCEPT = 0;      // 会长同意签约
  ENUM_CONTRACT_CHANGE_DEL = 1;         // 删除合约
  ENUM_CONTRACT_CHANGE_EXTENSION =2;  // 续约
  ENUM_CONTRACT_CHANGE_REJECT =3;     // 会长拒绝签约
  ENUM_CONTRACT_CHANGE_TIMEOUT =4;    // 合约过期
  ENUM_CONTRACT_CHANGE_USERLOGOFF =5;   // 用户注销，清理合约
};

enum CONTRACTOPTTYPE
{
  CONTRACTOPTENUM_UNVALID = 0;
  CONTRACTOPTENUM_SIGN = 1;
  CONTRACTOPTENUM_CANCEL = 2;
  CONTRACTOPTENUM_EXTENSION = 3;
}

enum HANDLE_SIGN_APPLY_OPR
{
  HANDLE_SIGN_APPLY_OPR_INVALID = 0;
  HANDLE_SIGN_APPLY_OPR_ACCEPT = 1;
  HANDLE_SIGN_APPLY_OPR_REJECT = 2;
  HANDLE_SIGN_APPLY_OPR_UNHANDLE = 3;
  HANDLE_SIGN_APPLY_OPR_FAILED = 4;             // 已签约其它公会
}

enum EXTENSION_CONTRACT_HANLE_OPR
{
  EXTENSION_CONTRACT_HANDLE_OPR_INVALID = 0;
  EXTENSION_CONTRACT_HANDLE_OPR_ACCEPT = 1;
  EXTENSION_CONTRACT_HANDLE_OPR_REJECT = 2;
  EXTENSION_CONTRACT_HANDLE_OPR_UNHANDLE = 3;
}

enum EXTENSION_STATUS
{
  EXTENSION_STATUS_CANNOT_EXTENSION = 0;      // 未达到续约条件
  EXTENSION_STATUS_CAN_EXTENSION = 1;        // 达到续约条件
  EXTENSION_STATUS_HAVE_EXTENSION = 2;        // 已发送
};

enum CANCEL_STATUS
{
  CANCEL_STATUS_FALSE = 0;                 // 不可以申请解约
  CANCEL_STATUS_TRUE  = 1;                 // 可以申请解约
};

enum CANCEL_APPLY_OPR
{
  CANCEL_APPLY_OPR_NORMAL = 0;
  CANCEL_APPLY_OPR_REJECT = 1;
  CANCEL_APPLY_OPR_ACCEPT = 2;
}

// 废弃
message CancelContractReq
{
  uint32 uid = 1;
  uint32 guild_id = 2;
}

message CancelContractResp
{
}

message CancelContractApply
{
  uint32 uid = 1;
  uint32 guild_id = 2;
  uint32 apply_timestamp = 3;
  uint32 status = 4;
}

message GetCancelContractApplyListReq
{
  uint32 uid = 1;
  uint32 guild_id = 2;
  uint32 status = 3;
  uint32 page = 4;
  uint32 page_num = 5;
}

message GetCancelContractApplyListResp
{
  repeated CancelContractApply apply_list = 1;
  uint32 total = 2;
}

message HandlerContractApplyReq
{
  uint32 uid = 1;
  uint32 guild_id = 2;
  uint32 target_uid = 3;
  uint32 status = 4;
  string guild_name = 5;
}

message HandlerContractApplyResp
{
}

message ApplyCancelContractReq
{
  uint32 uid = 1;
  uint32 guild_id = 2;
  string guild_name = 3;
}

message ApplyCancelContractResp
{

}

// 废弃
message RecallCancleContractReq
{
  uint32 uid = 1;
  uint32 guild_id = 2;
}

message RecallCancleContractResp
{

}

message CancelContractByUidReq
{
  uint32 target_uid = 1;
  uint32 guild_id = 2;
  string guild_name = 3;
}

message CancelContractByUidResp
{
}

// 签约/续约
message ApplySignRecord
{
  uint32 uid = 1;
  uint32 guild_id = 2;
  uint32 apply_time = 3;
  /*uint32 end_time = 4;*/
  uint32 contract_duration = 5;
  string identity_num = 6;
  bool is_live_actor = 7; // 是否语音直播主播
};

message ApplySignContractReq
{
  uint32 actor_uid = 1;
  uint32 guild_id = 2;
  string identity_num = 3;
  uint32 contract_duration = 4;
  bool is_live_actor = 5; // 是否语音直播主播
}

message ApplySignContractResp
{
}

message GetUserApplySignListReq
{
  uint32 handle_flag = 1;
  uint32 limit = 2;
}

message GetUserApplySignListResp
{
  repeated ApplySignRecord apply_list=1;
}

message GetGuildApplySignListReq
{
  uint32 guild_id = 1;
  uint32 begin = 3;
  uint32 limit = 4;             // limit == 0 get all
}

message GetGuildApplySignListResp
{
  repeated ApplySignRecord apply_list=1;
  uint32 apply_total = 2;
}


message ContractInfo
{
  uint32 actor_uid = 1;
  uint32 guild_id = 2;
  uint32 sign_time = 3;
  uint32 contract_duration = 4;
  uint32 renew = 5;                 // see EXTENSION_STATUS
  uint32 revocable = 6;             // see CANCEL_STATUS
  uint32 expire_time = 7;
  bool is_live_actor = 8; // 是否语音直播主播
}

message GetUserContractInfoReq
{
  uint32 actor_uid = 1;
}

message GetUserContractInfoResp
{
  uint32 contract_status = 1;  // see ga::ENUM_ContractStatus
  string status_tip = 2;
  ContractInfo contract = 3;
}

message GetUserContractInfoSimpleReq
{
  uint32 actor_uid = 1;
}

message GetUserContractInfoSimpleResp
{
  ContractInfo contract = 1;
}

message BatchGetGuildContractListReq
{
  uint32 guild_id = 1;
  repeated uint32 actor_uid_list = 2;
}

message BatchGetGuildContractListResp
{
  repeated ContractInfo contract_list = 1;
}

message HandleApplySignReq
{
  uint32 handle_flag = 1;
  uint32 actor_uid = 2;
  uint32 guild_id = 3;
}

message HandleApplySignResp
{
}


enum GuildExtensionFlag
{
  GuildExtensionFlag_INVALID = 0;
  GuildExtensionFlag_ALL = 1;               // 邀请所有
}


message GuildExtensionContractReq
{
  uint32 actor_uid = 1;
  uint32 guild_id = 2;
  uint32 contract_duration = 3;
  uint32 all = 4;               // see GuildExtensionFlag
}

message GuildExtensionContractResp
{
}

message ActorHandleExtensionContractReq
{
  uint32 actor_uid = 1;
  uint32 guild_id = 2;
  uint32 handle_flag = 3;       //see EXTENSION_CONTRACT_HANLE_OPR
  uint32 contract_duration = 4;
}

message ActorHandleExtensionContractResp
{
  uint32 end_time =1;
}

message GetContractWithIdentityReq
{
  string identity_num = 1;
}


message GetContractWithIdentityResp
{
  ContractInfo contract = 1;
}

message GetCancelContractLimitReq
{
}

message GetCancelContractLimitResp
{
  uint32 anchor_limit = 1;
  uint32 guild_limit = 2;
  uint32 yuyin_anchor_limit = 3;
}

message GetGuildContractSumReq
{
  uint32 guild_id = 1;
}

message GetGuildContractSumResp
{
  uint32 apply_sign_count = 1;            // 向该公会申请签约的人数
  uint32 apply_cancel_count = 2;          // 向该公会申请解约的人数
  uint32 actor_count = 3;                 // 公会签约主播人数
  uint32 renewable_count = 4;              // 可发续约的数量
  uint32 expiring = 5;                    // 是否有即将到期的合约

  uint32 large_score_count = 6;            // 公会中收入积分大于15w的人数
  uint32 live_actor_count = 7;             // 公会中语音直播主播的人数
}


message GuildContractInfo
{
  uint32 guild_id = 1;
  uint32 actor_count = 2;                 // 公会签约主播人数
}

message GetAllGuildReq
{
  uint32 begin = 1;
  uint32 limit = 2;
}

message GetAllGuildResp
{
  repeated GuildContractInfo guild_info_list = 1;
  uint32 contract_guild_cnt = 2;            // 有合约的公会总数
}

message GetCandidateApplyGuildListReq
{
}

message GetCandidateApplyGuildListResp
{
  repeated uint32 guild_list= 1;
}

message ActorInfo
{
  ContractInfo contract = 1;
  uint64 last_month_score = 2;      // 上个月收入积分
  uint32 score_goal = 3;            // 目标积分

  uint64 this_month_score = 4;      // 这个月当前收入积分
}

message SearchActorReq
{
  uint32 guild_id = 1;
  string keyword = 2;
}


message SearchActorResp
{
  repeated ActorInfo actor_list = 1;
}

message SearchGuildReq
{
  string keyword = 1;
}

message SearchGuildResp
{
  repeated uint32 guild_list = 1;
}

// 仅获取多人合作库的签约成员
message GetGuildActorReq
{
  uint32 guild_id = 1;
  uint32 begin = 2;
  uint32 limit = 3;

  bool is_asc = 4;  // 是否升序排列
}

message GetGuildActorResp
{
  repeated ActorInfo actor_list=1;
  uint32 actor_sum = 2;             // 主播总数

  bool has_next_page = 3;   // 是否还有下一页
}

// 获取语音直播合作库的签约成员
message GetGuildLiveActorReq
{
  uint32 guild_id = 1;
}

message GetGuildLiveActorResp
{
  repeated ActorInfo actor_list=1;
}

message GetGuildLiveActorUidListReq
{
  uint32 guild_id = 1;
  uint32 begin = 2;
  uint32 limit = 3;
}

message GetGuildLiveActorUidListResp
{
  repeated uint32 actor_list = 1;
}

message GetUserInGuildMonthScoreReq
{
  uint32 uid = 1;
  uint32 guild_id = 2;
  uint32 timestamp = 3;
}

message GetUserInGuildMonthScoreResp
{
  uint32 score = 1;
}

message GetUserMonthScoreReq
{
  uint32 uid = 1;
  uint32 timestamp = 2;
}

message GetUserMonthScoreResp
{
  uint32 score = 1;
}

message GetGuildChannelsMonthScoreReq
{
  uint32 guild_id = 1;
  uint32 timestamp = 2;
}

message GetGuildChannelsMonthScoreResp
{
  repeated uint32 channel_list = 1;
  repeated uint32 score_list = 2;
  uint32 total_score = 3;
}

message GuildHandleAllApplySignReq
{
  uint32 guild_id = 1;
  uint32 handle_flag = 2;
}

message GuildHandleAllApplySignResp
{
  uint32 handle_cnt = 1;          // 总共处理数量
  uint32 conflict_cnt = 2;          // 已经被签约的 申请数
  uint32 ok_cnt = 3;            // 成功数量

}

message GetGuildRenewAbleListReq
{
  uint32 guild_id = 1;
  uint32 begin = 2;
  uint32 limit = 3;
}

message GetGuildRenewAbleListResp
{
  repeated ActorInfo actor_list=1;
  uint32 actor_sum = 2;             // 主播总数
}

// 账号注销后清除合约信息
message ClearAnchorcontractReq
{
  uint32 clear_type =1; //see ENUM_CONTRACT_CHANGE_TYPE
  uint32 actor_uid = 2;
}

message ClearAnchorcontractResp
{
}

message LiveActorGuildId
{
  uint32 actor_uid = 1;
  uint32 guild_id = 2;
}

message BatchGetLiveActorUid2GuildIdReq
{
  repeated uint32 live_actor_uid_list = 1;
}

message BatchGetLiveActorUid2GuildIdResp
{
  repeated LiveActorGuildId list = 1;
}

message SetContractLiveActorFlagReq
{
  uint32 uid = 1;
  uint32 flag = 2; // 0:add live_actor flag 1:del live_actor flag
}

message SetContractLiveActorFlagResp{}

message BatchGetCancelContractCntReq
{
  repeated uint32 uids = 1;
}

message BatchGetCancelContractCntResp
{
  map<uint32, uint32> map_uid_to_cnt = 1;
}

// 解约公会下所有合约,慎用！！！
message CancelGuildAllContractReq
{
  uint32 guild_id = 1;
}

message CancelGuildAllContractResp{}


message ContractChangeLog
{
  uint32 uid = 1;
  uint32 guild_id = 2;
  uint32 sign_time = 3;   // 签约时间
  uint32 cancel_contract_time = 4;  // 解约时间
}

// 获取用户签约解约记录
message GetSignContractChangeLogReq
{
  uint32 uid = 1;
}

message GetSignContractChangeLogResp
{
  repeated ContractChangeLog log_list = 1;
}

/** 投诉相关 **/
enum ENUM_COMPLAIN_CONTRACT_STATUS
{
  Pending = 0;  // 处理中
  Pass = 1;     // 通过
  Fail = 2;     // 失败
}

enum ENUM_COMPLAIN_TYPE
{
  MULTI_PERSON_COOP = 0;  // 多人合作
  YUYIN_LIVE_COOP = 1;    // 语音直播
}

message CancelContractComplainInfo
{
  uint32 id = 1;  // 投诉记录id 唯一
  uint32 uid = 2;
  uint32 guild_id = 3;
  uint32 create_time = 4;   // 创建时间
  uint32 status = 5;        // 状态 see ENUM_COMPLAIN_CONTRACT_STATUS
  string identity_num = 6;  // 身份证号
  string reason = 7; // 原因
  repeated string evidence_pic_url_list = 8;  // 证据截图url
  string handler = 9; // 操作人
  uint32 handle_time = 10; // 操作时间
  string remarks = 11; // 备注
  uint32 type = 12;   // 投诉业务类型  see ENUM_COMPLAIN_TYPE
}

message ApplyCancelContractComplainReq
{
  CancelContractComplainInfo info = 1;
}

message ApplyCancelContractComplainResp{}

message GetLastCancelContractComplainReq
{
  uint32 uid = 1;
  uint32 guild_id = 2;
}

message GetLastCancelContractComplainResp
{
  CancelContractComplainInfo info = 1;
}

message HandleCancelContractComplainReq
{
  uint32 id = 1;
  uint32 status = 2; //see ENUM_COMPLAIN_CONTRACT_STATUS
  string handler = 3; // 操作人
  string remarks = 4; // 备注
}

message HandleCancelContractComplainResp{}

message GetCancelContractComplainListReq
{
  uint32 uid = 1;     // 为0即查所有uid
  uint32 guild_id = 2;// 为0即查所有guild_id
  uint32 begin_time = 3;
  uint32 end_time = 4;
  uint32 begin_idx = 5;
  uint32 limit = 6;
  bool get_handle_record = 7; // 获取操作记录？
  uint32 type = 8;   // 投诉业务类型
}

message GetCancelContractComplainListResp
{
  repeated CancelContractComplainInfo info_list = 1;
  uint32 total_cnt = 2; // 总数
}

// 成员权限
enum USER_PERMISSIONS
{
  NONE_PERMISSIONS = 0;  // 无权限
  REPORT_PERMISSIONS = 1;// 举报权限
}

message BatchGiveUserPermissionsReq {
  uint32 guild_id = 1;
  repeated uint32 uid_list = 2;
  uint32 permissions = 3; // see USER_PERMISSIONS
}

message BatchGiveUserPermissionsResp {}

message BatchReclaimUserPermissionsReq {
  uint32 guild_id = 1;
  repeated uint32 uid_list = 2;
  uint32 permissions = 3; // see USER_PERMISSIONS
}

message BatchReclaimUserPermissionsResp {}


message GetUsersByPermissionsReq {
  uint32 guild_id = 1;
  uint32 permissions = 2; // see USER_PERMISSIONS
  uint32 begin = 3;
  uint32 limit = 4;
}

message GetUsersByPermissionsResp {
  repeated uint32 uid_list = 1;
}

message CheckUserHasPermissionsReq {
  uint32 guild_id = 1;
  uint32 uid = 2;
  uint32 permissions = 3; // see USER_PERMISSIONS
}

message CheckUserHasPermissionsResp {
  bool has_permissions = 1;
}

message NoticeInfo
{
  string title = 1;
  string content = 2;
}

message VerifyingNoticeInfo
{
  uint32 uid = 1;
  uint32 guild_id = 2;
  NoticeInfo info = 3;
  uint32 summit_ts = 4;
}

// 获取正在审核的公告
message GetVerifyingNoticeListReq
{
  uint32 offset = 1;
  uint32 limit = 2;
}

message GetVerifyingNoticeListResp
{
  repeated VerifyingNoticeInfo info_list = 1;
  uint32 total_cnt = 2;
  uint32 next_offset = 3;
}


message GetVerifyingNotieByIdReq
{
  uint32 uid = 1;
  uint32 guild_id = 2;
}

message GetVerifyingNotieByIdResp
{
  VerifyingNoticeInfo notice_info = 1;
}


// 公告审核结果
message NoticeVerifyResultReq
{
  enum EnumVerifyResult {
    ENUM_NOT_PASS = 0;
    ENUM_PASS = 1;
  }
  uint32 verify_result = 1; //see EnumVerifyResult
  VerifyingNoticeInfo notice_info = 2;
  string remarks = 3;  // 备注
}

message NoticeVerifyResultResp
{

}

message AddVerifyNoticeReq
{
  VerifyingNoticeInfo notice_info = 1;
}
message AddVerifyNoticeResp
{
}


message GuildNoticeInfo
{
  uint32 uid = 1;
  uint32 guild_id = 2;
  NoticeInfo info = 3;
  uint32 update_ts = 4;
}

message GetGuildNoticeInfoReq
{
  enum EnumUserRoleType {
    ENUM_ROLE_COMMON = 0; // 普通签约成员
    ENUM_ROLE_ACTOR = 1;   // 公会会长
  }
  uint32 role_type = 1;
  uint32 guild_id = 2;
  uint32 uid = 3;
}

message GetGuildNoticeInfoResp
{
  GuildNoticeInfo notice_info = 1;
  uint32 total_modify_cnt = 2;
  uint32 reset_modify_cnt = 3;
  bool is_verifying = 4;   // 是否存在正在审核的公告
}

message CheckGuildHasNewNoticeReq {
  uint32 guild_id = 1;
  uint32 uid = 2;
}
message CheckGuildHasNewNoticeResp {
  bool has_new_notice = 1;
}

service  anchorcontract{
  option( tlvpickle.Magic ) = 15677;

  rpc ApplySignContract ( ApplySignContractReq ) returns (ApplySignContractResp) {
    option( tlvpickle.CmdID ) = 1;
    option( tlvpickle.OptString ) = "u:g:t:s:a:";
    option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -t <identity_num> -s <contract_duration> -a <is_live_actor>";
  }

  rpc CancelContract ( CancelContractReq ) returns (CancelContractResp) {
    option( tlvpickle.CmdID ) = 2;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc GetCancelContractApplyList ( GetCancelContractApplyListReq ) returns ( GetCancelContractApplyListResp )
  {
    option( tlvpickle.CmdID ) = 3;
    option( tlvpickle.OptString ) = "u:g:s:";
    option( tlvpickle.Usage ) = "-u <uid> -g <guildid> -s <status>";
  }

  rpc HandlerContractApply ( HandlerContractApplyReq ) returns ( HandlerContractApplyResp )
  {
    option( tlvpickle.CmdID ) = 4;
    option( tlvpickle.OptString ) = "u:g:t:s:n:";
    option( tlvpickle.Usage ) = "-u <uid> -g <guildid> -s <status> -t <target_uid> -n <guild_name> ";
  }

  rpc ApplyCancelContract ( ApplyCancelContractReq ) returns ( ApplyCancelContractResp )
  {
    option( tlvpickle.CmdID ) = 5;
    option( tlvpickle.OptString ) = "u:g:n:";
    option( tlvpickle.Usage ) = "-u <uid>  -g <guildid> -n <guild_name>";
  }

  rpc CancelContractByUid ( CancelContractByUidReq ) returns ( CancelContractByUidResp )
  {
    option( tlvpickle.CmdID ) = 6;
    option( tlvpickle.OptString ) = "u:g:n:";
    option( tlvpickle.Usage ) = "-u <uid>  -g <guildid> -n <guild_name>";
  }

  rpc RecallCancleContract ( RecallCancleContractReq ) returns ( RecallCancleContractResp )
  {
    option( tlvpickle.CmdID ) = 7;
    option( tlvpickle.OptString ) = "u:g:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc GetUserContractInfo ( GetUserContractInfoReq ) returns (GetUserContractInfoResp)
  {
    option( tlvpickle.CmdID ) = 8;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc HandleApplySign ( HandleApplySignReq ) returns (HandleApplySignResp)
  {
    option( tlvpickle.CmdID ) = 9;
    option( tlvpickle.OptString ) = "u:g:s:";
    option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -s <handle_flag> ";
  }

  rpc GetUserApplySignList ( GetUserApplySignListReq ) returns (GetUserApplySignListResp)
  {
    option( tlvpickle.CmdID ) = 10;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc GetGuildApplySignList ( GetGuildApplySignListReq ) returns (GetGuildApplySignListResp)
  {
    option( tlvpickle.CmdID ) = 11;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc GuildExtensionContract ( GuildExtensionContractReq ) returns (GuildExtensionContractResp)
  {
    option( tlvpickle.CmdID ) = 12;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc ActorHandleExtensionContract ( ActorHandleExtensionContractReq ) returns (ActorHandleExtensionContractResp)
  {
    option( tlvpickle.CmdID ) = 13;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc GetContractWithIdentity ( GetContractWithIdentityReq ) returns (GetContractWithIdentityResp)
  {
    option( tlvpickle.CmdID ) = 14;
    option( tlvpickle.OptString ) = "u:n:";
    option( tlvpickle.Usage ) = "-u <uid> -n <identity_num>";
  }

  rpc GetCancelContractLimit ( GetCancelContractLimitReq ) returns (GetCancelContractLimitResp)
  {
    option( tlvpickle.CmdID ) = 15;
    option( tlvpickle.OptString ) = "u:t:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc GetGuildContractSum ( GetGuildContractSumReq ) returns (GetGuildContractSumResp)
  {
    option( tlvpickle.CmdID ) = 16;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc GetCandidateApplyGuildList ( GetCandidateApplyGuildListReq ) returns (GetCandidateApplyGuildListResp)
  {
    option( tlvpickle.CmdID ) = 17;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc SearchActor ( SearchActorReq ) returns (SearchActorResp)
  {
    option( tlvpickle.CmdID ) = 18;
    option( tlvpickle.OptString ) = "g:";
    option( tlvpickle.Usage ) = "-g <guild_id> ";
  }

  rpc SearchGuild ( SearchGuildReq ) returns (SearchGuildResp)
  {
    option( tlvpickle.CmdID ) = 19;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc GetGuildActor ( GetGuildActorReq ) returns (GetGuildActorResp)
  {
    option( tlvpickle.CmdID ) = 20;
    option( tlvpickle.OptString ) = "g:b:l:s:";
    option( tlvpickle.Usage ) = "-g <guild_id> -b<begin> -l<limit> -s<is_asc>";
  }

  rpc GetUserInGuildMonthScore ( GetUserInGuildMonthScoreReq ) returns (GetUserInGuildMonthScoreResp)
  {
    option( tlvpickle.CmdID ) = 21;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc GetGuildChannelsMonthScore ( GetGuildChannelsMonthScoreReq ) returns (GetGuildChannelsMonthScoreResp)
  {
    option( tlvpickle.CmdID ) = 22;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc GetAllGuild (GetAllGuildReq) returns ( GetAllGuildResp )
  {
    option( tlvpickle.CmdID ) = 23;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc GuildHandleAllApplySign (GuildHandleAllApplySignReq) returns ( GuildHandleAllApplySignResp )
  {
    option( tlvpickle.CmdID ) = 24;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc GetGuildRenewAbleList ( GetGuildRenewAbleListReq ) returns ( GetGuildRenewAbleListResp )
  {
    option( tlvpickle.CmdID ) = 25;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc ClearAnchorcontract( ClearAnchorcontractReq ) returns (ClearAnchorcontractResp)
  {
    option( tlvpickle.CmdID ) = 26;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc GetUserMonthScore ( GetUserMonthScoreReq ) returns (GetUserMonthScoreResp)
  {
    option( tlvpickle.CmdID ) = 27;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc GetGuildLiveActor ( GetGuildLiveActorReq ) returns (GetGuildLiveActorResp)
  {
    option( tlvpickle.CmdID ) = 28;
    option( tlvpickle.OptString ) = "g:";
    option( tlvpickle.Usage ) = "-g <guild_id>";
  }

  rpc BatchGetLiveActorUid2GuildId ( BatchGetLiveActorUid2GuildIdReq ) returns (BatchGetLiveActorUid2GuildIdResp)
  {
    option( tlvpickle.CmdID ) = 29;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc SetContractLiveActorFlag ( SetContractLiveActorFlagReq ) returns (SetContractLiveActorFlagResp)
  {
    option( tlvpickle.CmdID ) = 30;
    option( tlvpickle.OptString ) = "u:g:";
    option( tlvpickle.Usage ) = "-u <uid> -g <flag>";
  }

  rpc CancelGuildAllContract ( CancelGuildAllContractReq ) returns (CancelGuildAllContractResp) {
    option( tlvpickle.CmdID ) = 31;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc ApplyCancelContractComplain ( ApplyCancelContractComplainReq ) returns (ApplyCancelContractComplainResp) {
    option( tlvpickle.CmdID ) = 32;
    option( tlvpickle.OptString ) = "u:g:t:";
    option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -t <type>";
  }

  rpc GetLastCancelContractComplain ( GetLastCancelContractComplainReq ) returns (GetLastCancelContractComplainResp) {
    option( tlvpickle.CmdID ) = 33;
    option( tlvpickle.OptString ) = "u:g:";
    option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> ";
  }

  rpc HandleCancelContractComplain( HandleCancelContractComplainReq ) returns (HandleCancelContractComplainResp) {
    option( tlvpickle.CmdID ) = 34;
    option( tlvpickle.OptString ) = "a:s:h:r:";
    option( tlvpickle.Usage ) = "-a <appeal_id> -s <status> -h <handler> -r <remarks>";
  }

  rpc GetCancelContractComplainList ( GetCancelContractComplainListReq ) returns (GetCancelContractComplainListResp) {
    option( tlvpickle.CmdID ) = 35;
    option( tlvpickle.OptString ) = "u:g:b:e:p:l:r:t:";
    option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -b <begin_time> -e <end_time> -p <begin_idx> -l <limit> -r <get_handle_record> -t <type>";
  }

  rpc BatchGetCancelContractCnt ( BatchGetCancelContractCntReq ) returns (BatchGetCancelContractCntResp) {
    option( tlvpickle.CmdID ) = 36;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc BatchGiveUserPermissions ( BatchGiveUserPermissionsReq ) returns (BatchGiveUserPermissionsResp) {
    option( tlvpickle.CmdID ) = 37;
    option( tlvpickle.OptString ) = "u:g:p:";
    option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -p <permissions>";
  }

  rpc BatchReclaimUserPermissions ( BatchReclaimUserPermissionsReq ) returns (BatchReclaimUserPermissionsResp) {
    option( tlvpickle.CmdID ) = 38;
    option( tlvpickle.OptString ) = "u:g:p:";
    option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -p <permissions>";
  }

  rpc GetUsersByPermissions ( GetUsersByPermissionsReq ) returns (GetUsersByPermissionsResp) {
    option( tlvpickle.CmdID ) = 39;
    option( tlvpickle.OptString ) = "g:p:b:l:";
    option( tlvpickle.Usage ) = "-g <guild_id> -p <permissions> -b <begin> -l <limit>";
  }

  rpc CheckUserHasPermissions ( CheckUserHasPermissionsReq ) returns (CheckUserHasPermissionsResp) {
    option( tlvpickle.CmdID ) = 40;
    option( tlvpickle.OptString ) = "u:g:p:";
    option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -p <permissions>";
  }

  rpc GetVerifyingNoticeList ( GetVerifyingNoticeListReq ) returns (GetVerifyingNoticeListResp) {
    option( tlvpickle.CmdID ) = 41;
    option( tlvpickle.OptString ) = "u:o:l:";
    option( tlvpickle.Usage ) = "-u <uid> -o <offset> -l <limit>";
  }

  rpc NoticeVerifyResult ( NoticeVerifyResultReq ) returns (NoticeVerifyResultResp) {
    option( tlvpickle.CmdID ) = 42;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc AddVerifyNotice ( AddVerifyNoticeReq ) returns (AddVerifyNoticeResp) {
    option( tlvpickle.CmdID ) = 43;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc GetVerifyingNotieById (  GetVerifyingNotieByIdReq ) returns ( GetVerifyingNotieByIdResp) {
    option( tlvpickle.CmdID ) = 44;
    option( tlvpickle.OptString ) = "u:g:";
    option( tlvpickle.Usage ) = "-u <uid> -g <guildId>";
  }

  rpc GetSignContractChangeLog (  GetSignContractChangeLogReq ) returns ( GetSignContractChangeLogResp) {
    option( tlvpickle.CmdID ) = 45;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc GetGuildNoticeInfo (  GetGuildNoticeInfoReq ) returns ( GetGuildNoticeInfoResp) {
    option( tlvpickle.CmdID ) = 46;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc CheckGuildHasNewNotice (  CheckGuildHasNewNoticeReq ) returns ( CheckGuildHasNewNoticeResp) {
    option( tlvpickle.CmdID ) = 47;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc BatchGetGuildContractList (  BatchGetGuildContractListReq ) returns ( BatchGetGuildContractListResp) {
    option( tlvpickle.CmdID ) = 48;
    option( tlvpickle.OptString ) = "g:u:";
    option( tlvpickle.Usage ) = "-g <guild_id> -u <uid>";
  }

  rpc GetGuildLiveActorUidList ( GetGuildLiveActorUidListReq ) returns ( GetGuildLiveActorUidListResp ) {
    option( tlvpickle.CmdID ) = 49;
    option( tlvpickle.OptString ) = "g:b:l:";
    option( tlvpickle.Usage ) = "-g <guild_id> -b<begin> -l<limit>";
  }

  rpc GetUserContractInfoSimple ( GetUserContractInfoSimpleReq ) returns (GetUserContractInfoSimpleResp)
  {
    option( tlvpickle.CmdID ) = 50;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

}


