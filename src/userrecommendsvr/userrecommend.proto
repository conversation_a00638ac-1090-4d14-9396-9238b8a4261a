syntax="proto2";
import "common/tlvpickle/skbuiltintype.proto";

package userrecommend;

message RecommendFromContacts
{
	required string phone=1;			//手机号
	required uint32 phone_uid=2;		//是否在TT上注册
}

//全量数据
message AddOrUpdateFullContactsReq
{
     required uint32 uid=1;				
     repeated string phone_list=2;
	 required uint32 contact_version=3;//通讯录版本
}
message AddOrUpdateFullContactsResp
{
}

//增加增量数据
message AddIncrementContactsReq
{
	 required uint32 uid=1;				
     repeated string phone_list=2;
}
message AddIncrementContactsResp
{
}

//删除增量数据
message DeleteIncrementContactsReq
{
	 required uint32 uid=1;				
     repeated string phone_list=2;
}

message DeleteIncrementContactsResp
{
}


//不再推荐别人
message SetRejectRecommendReq
{
     required uint32 uid=1;
     required string phone=2;
     required uint32 bRecommend=3;
}
message SetRejectRecommendResp
{
}

//改变推荐状态（是否向别人推荐自己）
message ChangeRecommendStatusReq
{
	required uint32 uid=1;
	required uint32 status=2;
}
message ChangeRecommendStatusResp
{
}

//获取推荐状态
message GetRecommendStatusReq
{
	required uint32 uid=1;
}

message GetRecommendStatusResp
{
	required uint32 status=2;
}

//推荐已经在TT上注册
message GetRecommendFromContactsReq
{
     required uint32 uid=1;
}

message GetRecommendFromContactsResp
{
	repeated RecommendFromContacts recommend_list=1;
	optional uint32 contact_version=2;
}

//推荐不是好友的用户
message GetNoFriendRecommendReq
{
	required uint32 uid=1;
}
message GetNoFriendRecommendResp
{
	repeated RecommendFromContacts recommend_list=1;
	optional uint32 contact_version=2;
}


//全量添加
message AddFullReportGameReq 
{
	required uint32 uid=1;
	repeated uint32 game_id_list=2;						
}
message AddFullReportGameResp
{
}

//增量更新
message UpdateIncreReportGameReq
{
	required uint32 uid=1;
	repeated uint32 add_game_id_list=2;				 //用户新装的游戏
	repeated uint32 delete_game_id_list=3;			//用户卸载的游戏
}

message UpdateIncreReportGameResp
{
}

//推荐全部信息
message RecommendFromAll
{
	enum RecommendType
	{
		RECOMMEND_FROM_CONTACTS=1;
		RECOMMEND_FROM_GAME=2;
	}
	required uint32 recommend_type=1;
	optional string phone=2;				//手机号
	optional uint32	phone_uid=3;			//是否注册
	optional uint32 game_id=4;				//游戏ID
	optional uint32 game_uid=5;				//游戏推荐用户
}

//获取全部推荐类型用户
message GetRecommendFromAllReq
{
	required uint32 uid=1;
	required uint32 count=2;				//客户端要拉取的数量
	repeated uint32 ex_uid_list=3;			//排除游戏好友
	optional uint32 priRecommend=4;			//优先游戏推荐
	optional uint32 sex=5;					//性别
}
message GetRecommendFromAllResp
{
	repeated RecommendFromAll recommend_all_list=1;
	optional uint32 contact_version=2;
}

//获取在指定游戏集合中用户安装的游戏
message GetUserInstallGameReq
{
	required uint32 uid=1;
}

message GetUserInstallGameResp
{
	repeated uint32 game_id_list=1;
}

//异步队列
message UpdateUserRecommendAsync
{
	required uint32 uid=1;	
	required bool is_full_report=2;
}


service UserRecommend 
{
	option( tlvpickle.Magic ) = 15568;														 // 服务监听端口号

	rpc  AddOrUpdateFullContacts( AddOrUpdateFullContactsReq) returns(  AddOrUpdateFullContactsResp )
	{
		option( tlvpickle.CmdID ) = 1;														 // 命令号
        option( tlvpickle.OptString ) = "u:t:p:";											 // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid>-t<contact_version>-p<phone1,phone2,...> ";    // 测试工具的命令号帮助
	}

	rpc AddIncrementContacts(AddIncrementContactsReq) returns( AddIncrementContactsResp )
	{
		option( tlvpickle.CmdID ) = 2;														 // 命令号
        option( tlvpickle.OptString ) = "u:p:";											 // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid> -p<phone1,phone2,...> ";    // 测试工具的命令号帮助
	}

	rpc DeleteIncrementContacts(DeleteIncrementContactsReq) returns( DeleteIncrementContactsResp )
	{
		option( tlvpickle.CmdID ) = 3;														 // 命令号
        option( tlvpickle.OptString ) = "u:p:";											 // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid> -p<phone1,phone2,...> ";    // 测试工具的命令号帮助
	}

	rpc  SetRejectRecommend(SetRejectRecommendReq) returns( SetRejectRecommendResp )
	{
		option( tlvpickle.CmdID ) = 4;														  // 命令号
        option( tlvpickle.OptString ) = "u:p:b:";												  // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid>-p<phone>-b<bRecommend>";										  // 测试工具的命令号帮助
	}

	rpc  GetRecommendFromContacts(GetRecommendFromContactsReq) returns( GetRecommendFromContactsResp )
	{
		option( tlvpickle.CmdID ) = 5;														  // 命令号
        option( tlvpickle.OptString ) = "u:";												  // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid> ";										  // 测试工具的命令号帮助
	}

	rpc  AddFullReportGame(AddFullReportGameReq) returns( AddFullReportGameResp )
	{
		option( tlvpickle.CmdID ) = 6;														  // 命令号
        option( tlvpickle.OptString ) = "u:g:";												  // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid> -g<gameId1,gameId2,...>";										  // 测试工具的命令号帮助
	}
	rpc  UpdateIncreReportGame(UpdateIncreReportGameReq) returns( UpdateIncreReportGameResp )
	{
		option( tlvpickle.CmdID ) = 7;														  // 命令号
        option( tlvpickle.OptString ) = "u:a:g:";												  // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid> -a<addgameId1,addgamId2,...> -g<deletegameId1,deletegamId2,...>";										  // 测试工具的命令号帮助
	}
	rpc  GetRecommendFromAll(GetRecommendFromAllReq) returns( GetRecommendFromAllResp )
	{
		option( tlvpickle.CmdID ) = 8;														  // 命令号
        option( tlvpickle.OptString ) = "u:n:p:s:";												  // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid> -n<count> -p<priRecommend> -s<sex>";										  // 测试工具的命令号帮助
	}
	rpc  ChangeRecommendStatus(ChangeRecommendStatusReq) returns( ChangeRecommendStatusResp )
	{
		option( tlvpickle.CmdID ) = 9;														  // 命令号
        option( tlvpickle.OptString ) = "u:s:";												  // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid> -s<status>";										  // 测试工具的命令号帮助
	}

	rpc  GetRecommendStatus(GetRecommendStatusReq) returns( GetRecommendStatusResp )
	{
		option( tlvpickle.CmdID ) = 10;														  // 命令号
        option( tlvpickle.OptString ) = "u:";												  // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid>";										  // 测试工具的命令号帮助
	}

	rpc  GetUserInstallGame(GetUserInstallGameReq) returns( GetUserInstallGameResp )
	{
		option( tlvpickle.CmdID ) = 11;														  // 命令号
        option( tlvpickle.OptString ) = "u:";												  // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid>";										  // 测试工具的命令号帮助
	}
}


