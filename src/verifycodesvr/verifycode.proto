syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package verifycode;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

message CreateVerifyCodeReq
{
	required uint32 uid = 1;
	required uint32 type = 2;
	required string session = 3;
	required uint32 code_len = 4;
	optional uint32 ttl = 5;      // 产生的验证码 多长时间有效
}
message CreateVerifyCodeResp
{
	required string code = 1;
}

// 检查验证码的有效性
message CheckValidReq
{
	required uint32 uid = 1;
	required uint32 type = 2;
	required string session = 3;
	optional string code = 4;
}

enum CheckResult
{
	ok = 0;
	not_exists = 1;       // 验证码信息不存在
	session_changed = 2;  // 验证码信息存在 但是 session 不匹配
	incorrect_code = 3;   // 验证码信息存在 但是 code 不匹配
	not_verified = 4;     // 验证码信息存在 而且 还没有验证
	pass = 5;             // 验证码信息存在 而且 已经完成验证
}

message CheckValidResp
{
	required uint32 result = 1; // see CheckResult
	required uint32 timestamp = 2;
	optional uint32 cooldown = 3;
}


// 获取是否开启验证码功能 全局开关
message CheckVerifyCodeGlobalSwitchReq
{
}
message CheckVerifyCodeGlobalSwitchResp
{
	required bool is_open = 1;
}

// 检查是否能需要进行验证操作（比如 每种TYPE 每次session 只需要验证一次）
message CheckVerifyCodeStatusReq
{
	required uint32 uid = 1;
	required uint32 type = 2;
	required string session = 3;
}
message CheckVerifyCodeStatusResp
{
	required uint32 result = 1; // see CheckResult
	optional uint32 timestamp = 2;
	optional uint32 cooldown = 3;
}

// 直接设置通过验证
message PassVerifyCodeCheckReq
{
	enum EPASS_OP_SOURCE_TYPE
	{
		ENUM_SMS_CHECKER = 1;              // 由短信验证通过
		ENUM_THIRDPARTY_TOKEN_CHECKER = 2; // 由三方验证 （QQ/WX）的token验证通过
	}
	
	required uint32 uid = 1;
	required uint32 type = 2;
	required string session = 3;
	required uint32 ttl = 5;      // 产生的验证码 多长时间有效
	required uint32 pass_source_type = 6; // see EPASS_OP_SOURCE_TYPE
}
message PassVerifyCodeCheckResp
{
}

// 没有UID参数情况下使用，原来smsvr服务的接口CreateVerifyCode
message CreateVerifyCodeByKeyReq
{
	required string key = 1;
	required uint32 len = 2;
	optional uint32 ttl = 3;      // 产生的验证码 多长时间有效 默认不设置的话是30分钟
}

message CreateVerifyCodeByKeyResp
{
	required string verify_code = 2;
}

// 验证CreateVerifyCodeByKey产生的接口, 员smsvr的ValidateVerifyCode
message ValidateVerifyCodeReq
{
	required string key  = 1;
	required string verify_code = 2;
	optional bool del_if_success = 3; // 验证成功后是否直接删除
}

service VerifyCode {
	option( tlvpickle.Magic ) = 15309;		// 服务监听端口号
	
	// 创建验证码
    rpc CreateVerifyCode( CreateVerifyCodeReq ) returns( CreateVerifyCodeResp ) {
        option( tlvpickle.CmdID ) = 1;             
        option( tlvpickle.OptString ) = "u:t:s:l:";  
        option( tlvpickle.Usage ) = "-u <uid> -t<type> -s<session> -l<code-len>";   
    }
	
	// 验证验证码
	rpc CheckValid( CheckValidReq ) returns( CheckValidResp ) {
        option( tlvpickle.CmdID ) = 2;              
        option( tlvpickle.OptString ) = "u:t:s:c:";   
        option( tlvpickle.Usage ) = "-u<uid> -t<type> -s<session> -c<code>";  
    }
	
	// 获取是否开启验证码功能 全局开关
	rpc CheckVerifyCodeGlobalSwitch( CheckVerifyCodeGlobalSwitchReq ) returns( CheckVerifyCodeGlobalSwitchResp ) {
        option( tlvpickle.CmdID ) = 3;             
        option( tlvpickle.OptString ) = "u:";   
        option( tlvpickle.Usage ) = "-u<uid> ";    
    }
	
	// 检查是否能需要进行验证操作（比如 每种TYPE 每次session 只需要验证一次）
	rpc CheckVerifyCodeStatus( CheckVerifyCodeStatusReq ) returns( CheckVerifyCodeStatusResp ) {
        option( tlvpickle.CmdID ) = 4;             
        option( tlvpickle.OptString ) = "u:t:s:";   
        option( tlvpickle.Usage ) = "-u<uid> -t<type> -s<session>";    
    }
	
	// 直接设置通过验证
	rpc PassVerifyCodeCheck( PassVerifyCodeCheckReq ) returns( PassVerifyCodeCheckResp ) {
        option( tlvpickle.CmdID ) = 5;             
        option( tlvpickle.OptString ) = "u:t:s:o:";   
        option( tlvpickle.Usage ) = "-u<uid> -t<type> -s<session> -o<source op type>";    
    }

	// 没有UID的情况下使用的产生验证码接口，原smsvr服务的CreateVerifyCode接口
	rpc CreateVerifyCodeByKey ( CreateVerifyCodeByKeyReq ) returns( CreateVerifyCodeByKeyResp ) {
		option( tlvpickle.CmdID ) = 6;             
		option( tlvpickle.OptString ) = "k:l:";   
		option( tlvpickle.Usage ) = "-k<key> -l<len>";
	}

	// 没有UID的情况下使用的验证接口，原来smsvr服的ValidateVerifyCode接口
	rpc ValidateVerifyCode( ValidateVerifyCodeReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 7;             
		option( tlvpickle.OptString ) = "k:c:";
		option( tlvpickle.Usage ) = "-k<key> -c<code>";
	}
}

