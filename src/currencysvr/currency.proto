syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Currency;


// =================================================================
//
// 获取用户红钻
//
// =================================================================
message GetUserCurrencyReq {
	required uint32 uid = 1;
}

message GetUserCurrencyResp {
	required int32 currency = 1;
    optional int32 valuable_currency = 2;
    optional int32 valueless_currency = 3;
}

message UserCurrency {
	required uint32 uid = 1;
	required int32 currency = 2;
    optional int32 valuable_currency = 3;
    optional int32 valueless_currency = 4;
}

message BatGetUserCurrencyReq {
	repeated uint32 uid_list = 1;
}

message BatGetUserCurrencyResp {
	repeated UserCurrency user_currency_list = 1;
}

message GetUserCurrencyLogReq {
	required uint32 uid = 1;
	optional uint32 begin_time = 2;
	optional uint32 end_time = 3;
	optional string desc_like = 4;
}

message UserCurrencyLogInfo {
	required uint32 c_value = 1;
	required string op_time = 2;
	required string desc = 3;
}

message GetUserCurrencyLogResp {
	repeated UserCurrencyLogInfo log_infos = 1;
}


// ====================================================
//
// 添加用户红钻
//
// ====================================================

enum ADD_CURRENCY_REASON{
    UNKNOWN = 0;  //其他
    LOGIN_REWARD = 1; //登录领取红钻奖励
    USER_MISSION = 2; //任务奖励
    SEND_PRESENT = 3; //送礼消耗
    INTERACTION_INTIMACY = 4; // 互动亲密任务送礼
    USER_TAG_GAME_CARD = 5; // 用户标签游戏卡片
    TT_ZHUANGYUAN = 6;      // tt庄园
    CHANNEL_LOTTERY = 7;    // 房间抽奖
    CHANNEL_INVITE_NON_ENTER_CHANNEL_USER = 8; // 邀请非进房的好友用户进房
      SOCIAL_COMMUNITY_LIKE_ACTIVITY_PUSH=9; //社团点赞活动
    BUY_CHANNEL_WEDDING = 10; // 购买婚礼
}

message AddUserCurrencyReq {
    required uint32 uid = 1;
    required int32  currency = 2;
    required string mission_key = 3;
    required string mission_desc = 4;
    required uint32 op_uid = 5;
    optional bool with_stock = 6;
    optional uint32 appid = 7;
    optional bool is_valuable = 8; // currency must be positive
    optional string order_id = 9;  // 
    optional uint32 reason = 10; //  enum ADD_CURRENCY_REASON
}

message AddUserCurrencyResp {

}


// =======================================================
//
// 判断是否已经为用户在对应任务上添加相应红钻
//
// =======================================================

message HasAddedUserCurrencyReq {
	required uint32 uid = 1;
	required string mission_key =2 ;
}

message HasAddedUserCurrencyResp {
	required bool added = 1;
}


// =================================================================
// app 红钻池
// =================================================================

message GetAppCurrencyReq {
    required uint32 appid = 1;
}
message GetAppCurrencyResp {
    optional uint64 amount = 1;
}

message AppCurrencyUpdate{
    enum OP{
        NOP = 0;
        SET = 1;
        ADD = 2;
        SUB = 3;
    }
    required uint32 appid = 1;
    required uint32 op = 2;
    required uint64 amount = 3;
    optional uint32 op_uid = 4;
    optional string op_desc = 5;
    optional uint32 op_at = 6;
}

message UpdateAppCurrencyReq{
    required AppCurrencyUpdate update = 1;
}
message UpdateAppCurrencyResp{
}

message GetAppCurrencyUpdateLogReq{
    required uint32 appid = 1;
    optional uint32 begin_time = 2;
    optional uint32 end_time = 3;
}

message GetAppCurrencyUpdateLogResp{
    repeated AppCurrencyUpdate update_list = 1;
}

message FreezeUserCurrencyReq {
    required uint32 uid = 1;
    required uint32 amount = 2;
    required string app_id = 3;
    required string order_id = 4;
    required string order_desc = 5;
    required uint32 op_uid = 6;
}

message FreezeUserCurrencyResp {
}

message ConfirmFrozenOrderReq {
    enum OP {
        NOP = 0;
        COMMIT = 1;
        ROLLBACK = 2;
    }
    required uint32 uid = 1;
    required uint32 amount = 2;
    required string app_id = 3;
    required string order_id = 4;
    required OP op = 5;
}

message ConfirmFrozenOrderResp {

}

service Currency {
	option( tlvpickle.Magic ) = 15030;		// 服务监听端口号

	rpc GetUserCurrency( GetUserCurrencyReq ) returns ( GetUserCurrencyResp ){
		option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>	";	// 测试工具的命令号帮助
	}

	rpc AddUserCurrency( AddUserCurrencyReq ) returns ( AddUserCurrencyResp ){
		option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "u:r:k:d:o:a:s";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -r <currency> -k <order id> -d <order desc> [-o <op_uid>] [-a <appid>] [-s, with_stock]";
	}

	rpc HasAddedUserCurrency( HasAddedUserCurrencyReq ) returns ( HasAddedUserCurrencyResp ){
		option( tlvpickle.CmdID ) = 3;										// 命令号
        option( tlvpickle.OptString ) = "u:k:a:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -k <order id> [-a <appid>]";
	}

	rpc BatGetUserCurrency( BatGetUserCurrencyReq ) returns ( BatGetUserCurrencyResp ){
		option( tlvpickle.CmdID ) = 4;										// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid list>";
	}

    //红钻池
    rpc GetAppCurrency( GetAppCurrencyReq ) returns ( GetAppCurrencyResp ){
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "a:";
        option( tlvpickle.Usage ) = "-a <appid>";
    }
    rpc UpdateAppCurrency( UpdateAppCurrencyReq ) returns (UpdateAppCurrencyResp) {
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "a:o:r:u:d:t:";
        option( tlvpickle.Usage ) = "-a <appid> -o <operator:1=set,2=add,3=sub> -r <currency> [-u <op uid>] [-d <op desc>] [-t <op at>]";
    }
    rpc GetAppCurrencyUpdateLog( GetAppCurrencyUpdateLogReq ) returns ( GetAppCurrencyUpdateLogResp ) {
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "a:b:e:";
        option( tlvpickle.Usage ) = "-a <appid> [-b <begin time>] [-e <end time>]";
    }

    // 红钻冻结消费
    rpc FreezeUserCurrency( FreezeUserCurrencyReq ) returns ( FreezeUserCurrencyResp ) {
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "u:a:o:";
        option( tlvpickle.Usage ) = "-u <uid> -a <app_id> -o <order_id>";
    }

    rpc ConfirmFrozenOrder( ConfirmFrozenOrderReq ) returns ( ConfirmFrozenOrderResp ) {
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "u:a:o:";
        option( tlvpickle.Usage ) = "-u <uid> -a <app_id> -o <order_id>";
    }

	// 红钻记录
	rpc GetUserCurrencyLog( GetUserCurrencyLogReq ) returns ( GetUserCurrencyLogResp ) {
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "u:b:e:d:";
        option( tlvpickle.Usage ) = "-u <uid> -b <begin_time> -e <end_time> -d <desc_like>";
    }
}