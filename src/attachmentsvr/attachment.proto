syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Attachment;									

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

message stImMsgCache {
	optional uint32 target_uid = 1;
	optional uint32 target_group = 2;
	required uint32 svr_msg_id = 3;
	required bytes msg = 4;				// Timeline::ImMsg
}

message stFileProperty {
	optional uint32 svr_time = 1;		// 服务器时间
	optional bytes client_prop = 2;	// 客户端带上来的文件属性
	optional uint32 status = 3;			// 状态
	repeated stImMsgCache cache_msg_list = 4;			// 等待附件上传完需要写的消息
	required uint32 type = 5;			// 附件类型
	required string target_account = 6;	// 发附件的目标对象，用户account或者群
	required string from_account = 7;	// 上传附件的人的账号
	optional MMBufLocation_t new_file_location = 8;		// 新的文件存储的位置
}

message stFile {
	required bytes data = 1;			// 文件内容
	required stFileProperty prop = 2;	// 文件属性
}

message AddFileReq {
	required string key = 1;
	required stFile file = 2;
	required uint32 ttl	= 3;
}

message GetFileReq {
	required string key = 1;
}

message GetFileResp {
	optional stFile file = 1;
}

message MMAttribute_t {
	required uint32 iDataLen = 1;
};

message MMBufData_t {
	required MMAttribute_t tAttribute = 1;
	required tlvpickle.SKBuiltinBuffer_PB tData = 2;
}

message MMBufLocation_t {
	required uint32 iOffset = 1;
	required uint32 iFileIndex = 2;
};



///////////////
// 低层次版本的 上传下载文件接口
///////////////

message LowLevelAddFileReq {
	required bytes data = 1;			// 文件内容
}
message LowLevelAddFileResp {
	required MMBufLocation_t file_location = 1; // 文件保存的位置
}

message LowLevelGetFileReq {
	required MMBufLocation_t file_location = 1; // 文件保存的位置
}

message LowLevelGetFileResp {
	required bytes data = 1;			// 文件内容
}


message DelFileReq{
    required string key = 1;
}

message DelFileResp{
}

//////////////////
service Attachment {
    option( tlvpickle.Magic ) = 14600;		// 服务监听端口号

	// 高版本接口的 上传文件
	// 高版本接口负责文件存取 同时 管理索引
    rpc AddFile(AddFileReq) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 1;												// 命令号
        option( tlvpickle.OptString ) = "k:d:t:";										// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-k <key_name> -d <file_dir> -t <ttl>";					// 测试工具的命令号帮助
    }

    rpc GetFile(GetFileReq) returns(GetFileResp) {
        option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "k:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-k <key_name>";						// 测试工具的命令号帮助
    }
	
	// 低版本接口的 上传与获取文件
	// 低版本接口只负责文件存取 不管理索引
    rpc LowLevelAddFile (LowLevelAddFileReq) returns(LowLevelAddFileResp) {
        option( tlvpickle.CmdID ) = 3;												
        option( tlvpickle.OptString ) = "d:";										
        option( tlvpickle.Usage ) = "-d <file_dir>";					
    }

    rpc LowLevelGetFile(LowLevelGetFileReq) returns(LowLevelGetFileResp) {
        option( tlvpickle.CmdID ) = 4;										
        option( tlvpickle.OptString ) = "i:o:";								
        option( tlvpickle.Usage ) = "-i <file index> -o <file offset>";		
    }

    rpc DelFile ( DelFileReq ) returns ( DelFileResp ){
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "k:";
        option( tlvpickle.Usage ) = "-k <key>";
    }
}


