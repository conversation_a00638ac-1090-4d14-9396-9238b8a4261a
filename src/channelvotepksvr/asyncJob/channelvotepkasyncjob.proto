syntax="proto2";


package channelvotepk.async;

message PkUserRank
{
	required uint32 uid = 1;
	required uint32 vote = 2;
	required uint32 rank = 3;
}

message PkInfo
{
	required uint32 uid = 1;              //发起玩家
	required uint32 channel_id = 2; 
	required uint32 type = 3;             // SEE channel_.proto  ga::ChannelVotePkType
	required uint32 start_timestamp  = 4; // 开始时间戳
	required uint32 duration_min = 5;     // 持续时间 单位分钟
	optional uint32 vote_cnt = 6;         // 如果是投票类型的情况，每个观众的票数
	optional uint32 person_type = 7;      // SEE channel_.proto  ga::ChannelVotePKNumType
	optional string pk_name = 8;          //PK名字
}

message PkRankChangeEvent
{
	required uint32 channel_id = 1;
	required PkInfo pk_info = 2;
	repeated PkUserRank rank_list = 3;
}

message PkStartEvent
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
	
	required PkInfo pk_info = 3;
	repeated PkUserRank rank_list = 4;
}

message PkFinishEvent
{
	required uint32 channel_id = 1;

	optional bool is_cancel = 2;
	optional uint32 cancel_op_uid = 3;
	
	optional PkInfo pk_info = 4;
	repeated PkUserRank rank_list = 5;
}

