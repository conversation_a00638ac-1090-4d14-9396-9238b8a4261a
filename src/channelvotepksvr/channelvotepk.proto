syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";  

// namespace
package channelvotepk;

enum ChannelVotePkType
{
	INVALID_TYPE = 0; //无效
	VOTE_PK_TYPE = 1; //投票类型
	DIAMOND_PK_TYPE = 2; //红钻
    TBEAN_PK_TYPE = 3;  // T豆
}

message ChannelVotePkCompetitor
{
	required uint32 uid = 1;
	required uint32 vote = 2;
	required uint32 rank = 3;
}

message ChannelPkInfo
{
	required uint32 uid = 1; //发起玩家
	required uint32 channel_id = 2; 
	required uint32 type = 3; //enum ChannelVotePkType PK类型
	required uint32 duration_min = 4; 
	required uint32 start_timestamp  = 5; //开始时间戳
	required uint32 person_type = 6; //ChannelVotePKNumType
	optional uint32 vote_cnt = 7; //如果是投票类型的情况，每个观众的票数
	optional string pk_name = 8; //PK 名字
}

message ChannelVotePKRankInfo
{
	required ChannelPkInfo info = 1;
	repeated ChannelVotePkCompetitor competiter_list = 2;
}

message ChannelVotePkVoteReq
{
	required uint32 uid = 1; //投票玩家
	required uint32 to_uid = 2; //目标玩家
	required uint32 vote_cnt = 3; //投票数量
	required uint32 channel_id = 4; 
	required uint32 start_timestamp  = 5; //开始时间戳
	required uint32 send_timestamp = 6; //送礼时间
	required uint32 pk_type = 7 ; // PK类型，投票0, 礼物1
}

message ChannelVotePkVoteResp
{
	required uint32 code = 1; //错误码
	required uint32 left_vote = 2; //剩余票数
}

message ChannelVotePkStartReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required uint32 duration_min = 3; //持续分钟
	required uint32 type = 4; //  enum ChannelVotePkType PK类型
	repeated uint32 uid_list = 5; //参与UID
	optional uint32 vote_cnt = 6; //每个观众的票数
	optional string pk_name = 7; //PK 名
}

message ChannelVotePkStartResp
{
	required uint32 code = 1;
	required uint32 channel_id = 2;
	required uint32 duration_min = 3; //持续分钟
	required uint32 start_timestamp = 4; //服务端时间，PK开始时间戳  channel_id和start_timestamp唯一确定一个PK对象
	required uint32 left_vote = 5; //自己剩余票数
}

message ChannelPkCancelReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required uint32 start_timestamp = 3;
}

message ChannelPKCancelResp
{
	required uint32 code = 1;
}

message GetChannelVotePKInfoReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
}

message GetChannelVotePKInfoResp
{
	required uint32 code = 1;
	required uint32 left_vote = 2; //剩余票数
	required ChannelVotePKRankInfo pk_info = 3; //PK全量信息
}

service channelvotepk 
{
	option( tlvpickle.Magic ) = 15658;		// 服务监听端口号

	//开始PK
	rpc ChannelVotePKStart( ChannelVotePkStartReq ) returns( ChannelVotePkStartResp ){
		option( tlvpickle.CmdID ) = 1;										
        option( tlvpickle.OptString ) = "u:a:p:d:x:";		
        option( tlvpickle.Usage ) = "-u <uid> -a <channel_id> -p <pk_type> -d <duration_min> -x <uidlist>";
	} 

	//取消PK
	rpc ChannelVotePKCancel ( ChannelPkCancelReq ) returns ( ChannelPKCancelResp )
	{
		option( tlvpickle.CmdID ) = 2;										
        option( tlvpickle.OptString ) = "u:a:s:";		
        option( tlvpickle.Usage ) = "-u <uid> -a <channel_id> -s <start_timestamp>";
	}


	//获取PK信息
	rpc ChannelVotePkGetInfo ( GetChannelVotePKInfoReq ) returns ( GetChannelVotePKInfoResp )
	{
		option( tlvpickle.CmdID ) = 3;										
        option( tlvpickle.OptString ) = "u:a:s:";		
        option( tlvpickle.Usage ) = "-u <uid> -a <channel_id>";
	}

	//投票
	rpc ChannelVotePKVote ( ChannelVotePkVoteReq ) returns ( ChannelVotePkVoteResp )
	{
		option( tlvpickle.CmdID ) = 4;										
        option( tlvpickle.OptString ) = "u:a:s:t:k:";		
        option( tlvpickle.Usage ) = "-u <uid> -a <channel_id> -s <start_timestamp> -t <to_uid> -k <vote_cnt>";
	}
}

