syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package circletimeline;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

//------------------------------------------
// timeline的通用存储结构，所有消息都是包含在这个结构体里面。
//------------------------------------------
message CircleTimelineMsg {
	enum TYPE {
		NEW_MSG_COMMENT = 1;			// 评论或者赞
		MY_TOPIC = 2;
		DELETE_NEW_MSG_COMMENT = 3;
		DELETE_NEW_MSG_TOPIC = 4;
        ACT_BEGIN = 5;                  // 活动开始
        ACT_END = 6;                    // 活动结束
		GUILD_CIRCLE_NEW_MSG_COMMENT = 7; // 公会圈子
		GUILD_CIRCLE_DELETE_NEW_MSG_TOPIC = 8; // 公会圈子
		GUILD_CIRCLE_MY_TOPIC = 9;
	}
	required uint32 type = 1;
	required uint32 seqid = 2;
	required bytes msg_bin = 3;
}

// 1、新消息  评论
message NewMsgComment {
	enum MSG_TYPE {
		COMMENT = 1;           //评论
		LIKE = 2;              //赞
	}
	enum SYSTEM_MSG_TYPE {
		NONE = 0;
		SYSTEM_MSG = 1;
    }
    required uint32 comment_id = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;                 //评论所属的topic
    required uint32 type = 4;					  // 类型，见上
    required string content = 5;                  //评论内容
    required string sender_account = 6;           //发送者
    required uint32 sender_uid = 7;
    required uint32 send_time = 8;                //发送时间
    required uint32 replied_comment_id = 9;       //如果是回复评论，则为评论的id，如果是直接回复主题，为0
    required string replied_account = 10;          //被回复的评论者账号，replied_comment_id不为0时有效
    required uint32 replied_uid = 11;            //被回复的评论者的uid，replied_comment_id不为0时有效
    required string replied_content = 12;         //被回复的评论内容，replied_comment_id不为0时有效
    required uint32 msg_id = 13;					// 消息id
    optional uint32 system_msg_type = 14;			// 系统消息类型
	repeated string comment_img_key_list    	= 15;	//评论图片列表
	repeated string replied_img_key_list    	= 16;	//被回复的评论的图片列表
	optional uint32 parent_comment_id = 17;		// 如果这是一条回复的消息 parentid 为最上级的评论ID
}

// 2、我发表的主题
message MyTopic {
    enum TOPIC_TYPE {
        NORMAL = 0;          //普通类型
        TOP = 1;             //置顶消息
    }
    required uint32 circle_id = 1;
    required uint32 topic_id = 2;
    required uint32 type = 3;                       //类型，见上
    required string sender_account = 4;             //发送者帐号
    required uint32 sender_uid = 5;					//发送者uid
    required uint32 send_time = 6;                  //发送时间
    required string title = 7;                      //标题
    required string content = 8;                    //内容
    repeated string img_key_list = 9;				// 图片key
}

// 3、删除评论/赞
message DeleteNewMsgComment {
    required uint32 circle_id = 1;
    required uint32 topic_id = 2;                	// 评论所属的topic
    required uint32 comment_id = 3;					// 评论id
    required uint32 type = 4;						// 评论/赞
    required uint32 sender_uid = 5;					// 发送者
}

// 4、删除主题
message DeleteNewMsgTopic {
    required uint32 circle_id = 1;
    required uint32 topic_id = 2;
}

// 5、活动上线
message ActBegin {
    required uint32 circle_id = 1;
    required uint32 topic_id = 2;
    required string act_desc = 3;
    required uint64 expire_time = 4;
}

// 6、活动下线
message ActEnd {
    required uint32 circle_id = 1;
    required uint32 topic_id = 2;
}

//------------------------------------------
// 读写协议
//------------------------------------------
message WriteTimelineMsgReq {
	required uint32 id = 1;
	required string suffix = 2;
	required CircleTimelineMsg msg = 3;
}

message WriteTimelineMsgResp {
}

//------------------------------------------
// 根据seq删除消息
//------------------------------------------
message BatchDeleteTimelineReq {
	required uint32 id = 1;
	required string suffix = 2;
	repeated uint32 seq_id_list = 3;
}

message BatchDeleteTimelineResp {
}

//------------------------------------------
// 拉取timeline
//------------------------------------------
message PullTimelineMsgReq {
	required uint32 id = 1;
	required string suffix = 2;
	required uint32 start_seqid = 3;
	required uint32 limit = 4;
	optional uint32 is_rev = 5;			// 反向拉取
	optional uint32 end_seqid = 6;
}

message PullTimelineMsgResp {
	repeated CircleTimelineMsg msg_list = 1;
}

//------------------------------------------
// 标记已读
//------------------------------------------
message MarkReadedReq {
	required uint32 id = 1;
	required string suffix = 2;
	required uint32 seqid = 3;
}

message MarkReadedResp {
}

//------------------------------------------
// 查已读
//------------------------------------------
message GetReadedReq {
	required uint32 id = 1;
	required string suffix = 2;
}

message GetReadedResp {
	required uint32 seqid = 1;
}

//------------------------------------------
// 活动消息
//------------------------------------------
message GetActMessagesReq
{
	required uint32 start_seq_id = 1;
	optional uint32 max_msg_count = 2;
}

message GetActMessagesResp
{
	repeated CircleTimelineMsg msg_list= 1;
	required uint32 max_seq = 2;
}

service CircleTimeline {
	option( tlvpickle.Magic ) = 14990;		// 服务监听端口号

    rpc WriteTimelineMsg(WriteTimelineMsgReq) returns (WriteTimelineMsgResp) {
        option( tlvpickle.CmdID ) = 1;																		// 命令号
        option( tlvpickle.OptString ) = "i:s:";														// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <id> -s <suffix>";			// 测试工具的命令号帮助
    }

    rpc BatchDeleteTimeline(BatchDeleteTimelineReq) returns (BatchDeleteTimelineResp) {
        option( tlvpickle.CmdID ) = 2;																		// 命令号
        option( tlvpickle.OptString ) = "i:s:l:";														// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <id> -s <suffix> -l <seq_id_1, seq_id_2>";			// 测试工具的命令号帮助
    }

    rpc PullTimelineMsg(PullTimelineMsgReq) returns (PullTimelineMsgResp) {
        option( tlvpickle.CmdID ) = 3;																		// 命令号
        option( tlvpickle.OptString ) = "i:s:b:l:";														// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <id> -s <suffix> -b <begin_seq> -l <limit>";			// 测试工具的命令号帮助
    }

    rpc MarkReaded(MarkReadedReq) returns (MarkReadedResp) {
        option( tlvpickle.CmdID ) = 4;											// 命令号
        option( tlvpickle.OptString ) = "i:s:d:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <id> -s <suffix> -d <seqid>";			// 测试工具的命令号帮助
    }

    rpc GetReaded(GetReadedReq) returns (GetReadedResp) {
        option( tlvpickle.CmdID ) = 5;										// 命令号
        option( tlvpickle.OptString ) = "i:s:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <id> -s <suffix>";					// 测试工具的命令号帮助
    }

	rpc WriteActBeginMessage( ActBegin ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 6;											// 命令号
		option( tlvpickle.OptString ) = "i:t:";								// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-i <circle_id> -t <topic_id>";	// 测试工具的命令号帮助
	}

	rpc WriteActEndMessage( ActEnd ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 7;										// 命令号
		option( tlvpickle.OptString ) = "i:t:";								// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-i <circle_id> -t <topic_id>";			// 测试工具的命令号帮助
	}

	rpc GetActMessages( GetActMessagesReq ) returns ( GetActMessagesResp ) {
		option( tlvpickle.CmdID ) = 8;											// 命令号
		option( tlvpickle.OptString ) = "s:l:";								// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-s <start> -l <limit>";	// 测试工具的命令号帮助
	}
}
