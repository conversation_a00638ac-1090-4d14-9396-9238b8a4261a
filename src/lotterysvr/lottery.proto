syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package lottery;

message Product {
    required uint32 id = 1;
    required string name = 2;
    required string icon = 3;
}

message GetLotteryChanceReq{
    required uint32 uid = 1;
}

message GetLotteryChanceResp{
    required uint32 chance = 2;
}

message ProductItem{
    required uint32 product_id = 1;
    repeated string serialnumber_list = 2;
}

message AddProductItemReq{
    repeated ProductItem product_item_list = 1;
}

message UserLotteryReq{
    required uint32 uid = 1;
}

message UserLotteryResp{
    enum Product_Representation_Type {
        SerialNumber = 1;
        Description = 2;
    }

    required uint32 product_id = 1;
    required string serialnumber = 2;
    required string product_name = 3;
    required uint32 representation_type = 4;
}

message GetUserWinningReq{
    required uint32 uid = 1;
}

message UserWinning{
    required uint32 product_id = 1;
    required string serialnumber = 2;
    required uint32 winning_time = 3;
    required uint32 uid = 4;
    required string product_name = 5;
}

message GetUserWinningResp{
    repeated UserWinning user_winning_list = 1;
}

enum RouletteType {
    kRouletteType_Normal = 1;
    kRouletteType_Luxury = 2;
}

message GetUserLotteryInfoReq {
    required uint32 uid = 1;
}

message UserLotteryInfo {
    repeated Product product_list = 1;
    required uint32 lottery_chance = 2;
    required uint32 total_invite = 3;
    required uint32 today_invite = 4;
    required uint32 roulette_type = 5;
}

message GetUserLotteryInfoResp {
    required UserLotteryInfo info = 1;
}

message GetRecentWinningReq {
    required uint32 limit = 1;
}

service Lottery{
    option( tlvpickle.Magic ) = 15240;

    rpc GetLotteryChance ( GetLotteryChanceReq ) returns ( GetLotteryChanceResp ) {
        option( tlvpickle.CmdID ) = 1;
	    option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc AddProductItem ( AddProductItemReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 2;
	    option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc UserLottery ( UserLotteryReq ) returns ( UserLotteryResp ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc GetUserWinning ( GetUserWinningReq ) returns ( GetUserWinningResp ) {
        option( tlvpickle.CmdID ) = 4;
	    option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetUserLotteryInfo ( GetUserLotteryInfoReq ) returns ( GetUserLotteryInfoResp ) {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc GetRecentWinning ( GetRecentWinningReq ) returns ( GetUserWinningResp ) {
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "l:";
        option( tlvpickle.Usage ) = "-l <limit>";
    }
}
