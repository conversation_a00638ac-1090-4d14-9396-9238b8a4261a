syntax = "proto2";

import "antispamlogic.proto";

package antispamlogic.async;

enum TASK_TYPE {
    TEXTCHECK_EVENT = 1;
}

message EventInfo {
	optional string data_id = 1;
	optional uint32 recv_time = 2;
	optional string from_tokenid = 3;
	optional string to_tokenid = 4;
}

message TextCheckEvent
{
	required TextCheckReq req = 1;
	required TextCheckResp resp = 2;
	required EventInfo content = 3;
}



