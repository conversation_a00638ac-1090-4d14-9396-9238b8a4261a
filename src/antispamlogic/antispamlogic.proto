syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

option go_package="golang.52tt.com/protocol/services/src/antispamlogic";

// namespace
package antispamlogic;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

message UserBehaviorCheckReq {
  enum BehaviorType {
    friend_match = 1;
    ugc_follow = 2;
    im_stranger = 3;
    im_send_msg = 4;       // IM发消息
    im_send_text = 5;      // IM发文本消息

    ugc_been_followed = 6;  // 被关注
    ugc_post = 7;           // 广场：发帖
    ugc_comment_text = 8;   // 广场：发文字评论
    ugc_comment_image = 9;  // 广场：发图片评论
    ugc_attitude = 10;      // 广场：点赞

    game_match   = 11;      // 开黑匹配
    hold_mic     = 12; 		// 上麦
    guild_create = 13; 		// 创建公会
    guild_join   = 14;      // 加入公会
    near_user    = 15; 		// 附近的人

    room_text_msg = 16;  // 房间文本消息
    room_atta_msg = 17;  // 房间附件消息
    draw_game     = 18;  // 房间画布
    apply_mic     = 19;  //申请排麦
    usertag_match = 20;  // 标签匹配
    black_list = 21;     //检查是否黑产
  }
  required uint32 uid = 1;
  repeated uint32 behavior_type_list = 2;
  optional uint32 to_uid = 3;
}

message UserBehaviorCheckResp {
  enum ResultType {
    NORMAL = 0;
    SUSPICION = 1;
    BLACK = 2;
  }

  optional uint32 hit_uid = 1;
  optional uint32 hit_behavior_type = 2;
  optional uint32 result_type = 3;
}

message ShumeiDeviceInfo {
  optional string sm_device_id = 1;
}

message AntispamUserInfo {
  required uint32 uid = 1;		// required
  optional string username = 2;
  optional string alias = 3;
  optional string nickname = 4;
  optional string tokenid = 5;
  optional uint32 gender = 6;
  optional string phone_number = 7;
}

message AntispamChanInfo {
  required uint32 channel_id = 1;		// required
  optional uint32 display_id = 2;
  optional string name = 3;
}

message AntispamGroupInfo {
  required uint32 group_id = 1;	// required
}

enum RiskType {
  RESULT_DEFAULT = 0;
  RESULT_HIGH_RISK_ACCOUNT = 1;
}

message SubTextInfo {
  optional string text = 1;          //文本内容
  optional bytes sub_text_check_context = 2;     //业务的上下文
  optional uint32 data_type = 3;             //数据类型区分（业务场景）
  optional string data_sub_scene = 4;        // 业务子场景
}

message TextCheckReq {
  required string text = 1;           			// 主文本信息: 待检测的文本, required
  optional AntispamUserInfo user_info = 2;
  optional AntispamUserInfo to_user_info = 3;
  optional string ip = 4;
  optional string device_id_base64 = 5;
  optional uint32 data_type = 6;      			// 数据类型区分（业务场景）
  optional string data_sub_scene = 7;				// 业务子场景
  optional ShumeiDeviceInfo sm_device_info = 8;
  optional AntispamChanInfo channel_info = 9;
  optional AntispamGroupInfo group_info = 10;
  optional bytes text_check_context = 11;
  optional bool async_status = 12;
  optional uint32 produce_time = 13;
  repeated SubTextInfo sub_text_info_list = 14;    //副文本信息，一定是走异步的
}

message TextCheckResp {
  enum RESULT{
    PASS = 0;           // 0:通过
    SUSPICION = 1;      // 1:嫌疑
    NOT_PASS = 2;       // 2:不通过
  }
  required uint32 result = 1;     // 文本检测结果
  optional string label_info = 2;     // 判定结果分类信息
  optional string requestId = 3;
  optional uint32 risk_type = 4;			//
}

message DVCommCheckReq {
  optional AntispamUserInfo user_info = 1;
  optional AntispamUserInfo to_user_info = 2;
  optional string event_type = 3;
  optional string event_time = 4;
  optional string ext_info = 5;
}

message DVLoginCheckReq {
  optional AntispamUserInfo user_info = 1;
  optional string event_type = 2;
  optional string ip = 3;
  optional uint32 client_version = 4;
  optional string event_time = 5;
  optional string device_id = 6;
  optional string os_ver = 7;
  optional string token =8;
}

message DVCheckResp {
  optional string response_type = 1;
  optional string cust_no = 2;
  optional string apply_no = 3;
  optional double score = 4;
  optional string error_type = 5;
  optional string error_detail = 6;
}

message antispamMissionInfo
{
  optional int32 get_coupon = 1;
  optional string event_name = 2;
  optional int64 task_cost_time = 3;
  optional int64 task_amount = 4;
}

message antispamExchangeInfo
{
  optional int64 interval = 1;
  optional string price = 2;
  optional string discount = 3;
  optional string account = 4;
  optional string discount_type = 5;
}

enum ShumeiOSType {
  none = 0;
  android = 1;
  ios = 2;
  weapp = 3;
  web = 4;
}

// 数美做任务检测
message AntispamTaskEventCheckReq {
  optional string app_id = 1;      // 应用ID，用于区分相同公司的不同应用,现后台默认给default
  required uint32 uid = 2;         //uid
  required string client_ip = 3;
  optional int32  user_level = 4;  //用户等级，有就直接提供，没有就填0
  optional string task_id = 5;     //任务id
  optional string deviceid = 6;
  optional string phone = 7;
  optional int32 os = 8;          //枚举ShumeiOSType
  optional int32 app_version = 9;
  optional string role_id = 10;    //角色职业,暂时用不到
  optional antispamMissionInfo mission_info = 11;      //任务信息
  optional string nickname = 12;
}

// 数美兑换检测
message AntispamExchangeEventCheckReq {
  optional string app_id = 1;      //应用ID，用于区分相同公司的不同应用,现后台默认给default
  required uint32 uid = 2;         //uid
  required string client_ip = 3;
  optional int32  user_level = 4;  //用户等级，有就直接提供，没有就填0
  optional string product = 5;     //兑换的礼物信息
  optional string deviceid = 6;
  optional string phone = 7;
  optional int32 os = 8;           //枚举ShumeiOSType
  optional int32 app_version = 9;
  optional string role_id = 10;    //角色职业,暂时用不到
  optional antispamExchangeInfo exchange_info = 11;    //兑换的信息
  optional string nickname = 12;
}

message AntispamEventCheckResp {
  optional string request_id = 1;
  optional int32 code = 2;
  optional string message = 3;
  optional string risk_level = 4;
  optional int32 score = 5;
}

message UserBehaviorResultReq {
  enum BehaviorResult{
    PASS = 0;           // 0:通过
    NOT_PASS = 1;       // 1:不通过
  }

  required uint32 uid = 1;         //uid
  required uint32 result = 2;
}

message UserBehaviorResultResp {

}
message BatchUserBehaviorCheckReq {
  repeated uint32 uid_list = 1;
}

message UserBehaviorInfo {
  required uint32 uid = 1;
  required bool status = 2;
}

message BatchUserBehaviorCheckResp {
  repeated UserBehaviorInfo uid_behavior_list = 1;
}

//数美检测事件请求：通用
message AntispamEventCheckReq {
  optional string app_id = 1;      //应用ID，用于区分相同公司的不同应用,现后台默认给default
  required uint32 uid = 2;         //uid
  required string ip = 3;
  enum checkEventType
  {
    undefined = 0;
    fission = 1;         //邀请
    coupon = 2;          //领券
    like = 3;            //点赞
  }
  required checkEventType event_type = 4;
  optional int32  token_seperate = 5;      //默认为0
  optional string nickname = 6;
  optional string deviceid = 7;
  optional int32  user_level = 8;  //用户等级，有就直接提供，没有就填0
  optional string phone = 9;

  //fission事件
  optional uint32 fission_inviteuid = 10;

  //点赞事件新增
  optional ShumeiOSType os = 11;
  optional int32 app_version = 12;
  optional string content_id = 13;
}

// AntispamLogic服务
service AntispamLogic {
  option( tlvpickle.Magic ) = 15076;		// 服务监听端口号

  rpc UserBehaviorCheck( UserBehaviorCheckReq ) returns( UserBehaviorCheckResp ) {
    option( tlvpickle.CmdID ) = 1;                  // 命令号
    option( tlvpickle.OptString ) = "u:";   	    // 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-u <uid>";    	// 测试工具的命令号帮助
  }

  rpc TextCheck( TextCheckReq ) returns ( TextCheckResp ) {
    option( tlvpickle.CmdID ) = 2;                  // 命令号
    option( tlvpickle.OptString ) = "u:";   	    // 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-u <uid>";    	// 测试工具的命令号帮助
  }

  rpc DVLoginCheck( DVLoginCheckReq ) returns ( DVCheckResp ) {
    option( tlvpickle.CmdID ) = 3;                  // 命令号
    option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
  }

  rpc DVCommCheck( DVCommCheckReq ) returns ( DVCheckResp ) {
    option( tlvpickle.CmdID ) = 4;                  // 命令号
    option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
  }

  rpc TaskEventCheck( AntispamTaskEventCheckReq ) returns ( AntispamEventCheckResp ) {
    option( tlvpickle.CmdID ) = 5;                  // 命令号
    option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
  }

  rpc ExchangeEventCheck( AntispamExchangeEventCheckReq ) returns ( AntispamEventCheckResp ) {
    option( tlvpickle.CmdID ) = 6;                  // 命令号
    option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
  }

  rpc UpdateUserBehaviorResult( UserBehaviorResultReq ) returns ( UserBehaviorResultResp ) {
    option( tlvpickle.CmdID ) = 7;                  // 命令号
    option( tlvpickle.OptString ) = "u:b:";   	    // 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-u <uid> -b <not black 0 | black 1>";    	// 测试工具的命令号帮助
  }
  rpc BathUserBehaviorCheck( BatchUserBehaviorCheckReq ) returns ( BatchUserBehaviorCheckResp ) {
    option( tlvpickle.CmdID ) = 8;                  // 命令号
    option( tlvpickle.OptString ) = "u:";   	    // 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-u <uid,uid,uid,uid>";    	// 测试工具的命令号帮助
  }

  rpc SMCommEventCheck( AntispamEventCheckReq ) returns ( AntispamEventCheckResp ) {
    option( tlvpickle.CmdID ) = 9;                  // 命令号
    option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
  }
}

