syntax="proto2";


import "common/tlvpickle/skbuiltintype.proto";


package AppDoctor;

enum APP_ID {
	TT_ANDROID = 1;
    TT_IPHONE_APPSTORE = 2;
    TT_IPHONE_ENTERPRISE = 3;
}

message StCrashReport {
	required uint32 uid = 1;
	required uint32 app_id = 2;
	required uint32 app_ver = 3;
	required string crash_time = 4;
	required string crash_key = 5;
}

message StoreCrashReportReq {
	required StCrashReport crash_report = 1;
}

message StoreCrashReportResp {
}


message GetCrashReportsReq {
	required uint32 uid = 1;
	optional uint32 app_id = 2;
	optional uint32 app_ver = 3;
	optional string min_crash_time = 4;
	optional string max_crash_time = 5;
}

message GetCrashReportsResp {
	repeated StCrashReport crash_report_list = 1;
}

message Version {
    required string version_name = 1;
    required uint32 version_major = 2;
    required uint32 version_minor = 3;
    required uint32 release = 4;
    required uint32 version_code = 5;
}

message AppPackageInfo {
    required uint32 package_id = 1;
    required Version version = 2;
    required string package_url = 3;
    required string install_url = 4;
    optional string file_md5 = 5;
    optional string head_md5 = 6;
}

message AppUpgradeStrategy {
    required uint32 strategy_id = 1;
    required uint32 app_id = 2;
    required AppPackageInfo package_info = 3;
    required string upgrade_title = 4;
    required string upgrade_information = 5;
}

message SetActiveAppUpgradeStrategyReq {
    required uint32 app_id = 1;
    required uint32 strategy_id = 2;
}

message GetActiveAppUpgradeStrategyReq {
    required uint32 app_id = 1;
}

service AppDoctor {
	option ( tlvpickle.Magic ) = 15000;

	rpc StoreCrashReport( StoreCrashReportReq ) returns( StoreCrashReportResp ) {
		option( tlvpickle.CmdID ) = 1;
		option( tlvpickle.OptString ) = "u:a:v:t:k:";
		option( tlvpickle.Usage ) = "-u <uid> -a <appid> -v <version> -t <crash_time> -k <crash_key>";
	}

	rpc GetCrashReports( GetCrashReportsReq ) returns( GetCrashReportsResp ) {
		option( tlvpickle.CmdID ) = 2;
		option( tlvpickle.OptString ) = "u:";
		option( tlvpickle.Usage ) = "-u <uid>";
	}

    rpc CreateAppPackage( AppPackageInfo ) returns( AppPackageInfo ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc CreateAppUpgradeStrategy( AppUpgradeStrategy ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc SetActiveAppUpgradeStrategy( SetActiveAppUpgradeStrategyReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "a:s:";
        option( tlvpickle.Usage ) = "-a <app_id> -s <strategy_id>";
    }

    rpc GetActiveAppUpgradeStrategy( GetActiveAppUpgradeStrategyReq ) returns ( AppUpgradeStrategy ) {
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "a:";
        option( tlvpickle.Usage ) = "-a <app_id>";
    }
}
