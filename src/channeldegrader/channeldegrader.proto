syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package channeldegrader;


message CheckCmdDegradationReq {
    required uint32 uid = 1;
    optional uint32 cmd = 2;

    optional uint32 channel_id = 3; //带上才判断
    optional uint32 member_size = 4; // 房间人数，如果带上就判断
}

message CheckCmdDegradationResp {
    optional bool degradation = 1;  //ture-- 降级  false--不降级
}

service  channeldegrader{
    option( tlvpickle.Magic ) = 15650;		// 服务监听端口号

    //
    rpc CheckCmdDegradation( CheckCmdDegradationReq ) returns (  CheckCmdDegradationResp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:p:";  
        option( tlvpickle.Usage ) = "-u <uid> [-p <cmd>]";
    }
}


message UserChannel {
    required uint32 channel_id = 1;
    required uint32 enter_at = 2;
}

