syntax = "proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package bizbalancekeeper;

enum BizId {
  TT = 0;
  TT_GAME = 1;
}

message BalanceInfo {
  uint32 biz_id = 1;  // 业务id
  uint32 ext_biz_id = 2; // 业务方内部自定义id
  string service = 3; // 业务方的自定义服务
  string session = 4; // 业务方定义的session（可选）
  string detail = 5;  // 业务方自定义的详情信息（可选）
  uint64 create_at = 6;
}

message AddReq {
  BalanceInfo info = 1;
}

message AddResp {

}

service BizBalanceKeeper {
  option(tlvpickle.Magic) = 15710;

  rpc Add (AddReq) returns (AddResp) {
    option(tlvpickle.CmdID) = 1;
    option(tlvpickle.OptString) = ":b:s:";
    option(tlvpickle.Usage) = "-b <bizid> -s <server name>";
  }
}