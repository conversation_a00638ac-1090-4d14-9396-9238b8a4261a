syntax ="proto2";

import "common/tlvpickle/skbuiltintype.proto";
package switchGiftCount;

message SwitchGiftCountReq
{
	required uint32 channel_id = 1;//房间id
	required uint32 is_off = 2;// 1 打开 0 关闭
    repeated uint32 micr_users = 3;
}
message SwitchGiftCountResp
{
}

service switchGiftCount
{
	option( tlvpickle.Magic ) = 20000;		// 服务监听端口号
	rpc SwitchCount( SwitchGiftCountReq ) returns( SwitchGiftCountResp ){
		option( tlvpickle.CmdID ) = 1;								// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";								// 测试工具的命令号帮助
	}
}
