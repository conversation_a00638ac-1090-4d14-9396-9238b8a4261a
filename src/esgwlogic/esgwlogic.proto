syntax="proto2";

package esgwlogic;
// 必须import
import "common/tlvpickle/skbuiltintype.proto";

enum IndexType
{
    CHANNEL_INDEX = 0;
    GUILD_INDEX = 1;
}

enum IndexOperType
{
    ADD_SEARCH_INDEX = 0;
    DEL_SEARCH_INDEX = 1;
    MOD_SEARCH_INDEX = 2;

    UNSUPPORT_OPER_INDEX = 255;
}

message IndexData
{
    required uint32 index_type = 1;     // IndexType
    required uint32 oper_type = 2;      // IndexOperType
    required uint32 id = 3;             // 索引数据 id
    optional string name = 4;           // 索引数据 名称
    optional uint32 remain_member = 5;  // 索引数据 人数
}

service EsgwLogic 
{
    option( tlvpickle.Magic ) = 15616;
}

