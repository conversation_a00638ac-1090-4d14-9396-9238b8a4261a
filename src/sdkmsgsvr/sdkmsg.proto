syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

//namespace
package Sdkmsg;

message SdkMsgInfo{
    //消息内容
    required uint32 msg_id      = 1;    //auto increase in database
    required string title       = 2;    //标题
    required string url         = 3;    //内容url
    required string begin_time  = 4;    //开始时间
    required string end_time    = 5;    //结束时间
    required uint32 msg_style   = 6;    //文本框样式：1，2和3
    required uint32 limit_times = 7;    //显示次数
    required uint32 limit_type  = 8;    //显示类型：1，每天显示次数；2，总共显示次数

    //匹配条件
    required uint32 pull_type       = 9;    //匹配条件bitmap,bit0:game_id, bit1:channel_id, bit2:user_id
    required string game_id_list    = 10;    //game id
    required string channel_id_list = 11;   //渠道号列表，逗号分隔
    required string user_id_list    = 12;   //tt user id list, 逗号分隔

    //数据库状态
    required uint32 rank            = 13;   //排序用
    required uint32 status          = 14;   //1表示消息有效

    optional string content_text    = 15;   //消息内容
}

message GetSdkMsgsReq{
    required uint32 type = 1;   // 0:all msg; 1:status=1 msg
}

message GetSdkMsgsResp{
    repeated SdkMsgInfo msg_list = 1;
}

message DelSdkMsgReq{
    required uint32 msg_id = 1;
}

message DelSdkMsgResp{
}

message UpdateSdkMsgReq{
    required SdkMsgInfo msg = 1;
}

message UpdateSdkMsgResp{
    required uint32 msg_id = 1;
}

message GetSdkMsgByIdReq{
    required uint32 msg_id = 1;
}

message GetSdkMsgByIdResp{
    required SdkMsgInfo msg =  2;
}

service Sdkmsg
{
    option( tlvpickle.Magic ) = 15470;		// 服务监听端口号

    rpc GetSdkMsgs( GetSdkMsgsReq ) returns( GetSdkMsgsResp ) {
        option( tlvpickle.CmdID ) = 1;              // 命令号
        option( tlvpickle.OptString ) = "";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
    }

    rpc DelSdkMsg( DelSdkMsgReq ) returns( DelSdkMsgResp ) {
        option( tlvpickle.CmdID ) = 2;              // 命令号
        option( tlvpickle.OptString ) = "m:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <msg_id>";    // 测试工具的命令号帮助
    }

    rpc UpdateSdkMsg( UpdateSdkMsgReq ) returns( UpdateSdkMsgResp ) {
        option( tlvpickle.CmdID ) = 3;              // 命令号
        option( tlvpickle.OptString ) = "m:t:g:s:l:u:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <msg_id> -u <user_id>";    // 测试工具的命令号帮助
    }

    rpc GetSdkMsgById (GetSdkMsgByIdReq) returns(GetSdkMsgByIdResp) {
        option( tlvpickle.CmdID ) = 4;              // 命令号
        option( tlvpickle.OptString ) = "m:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <msg_id>";    // 测试工具的命令号帮助
    }
}
