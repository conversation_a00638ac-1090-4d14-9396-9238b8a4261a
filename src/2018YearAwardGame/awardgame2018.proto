syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package awardgame2018;

enum EStageType
{
	ENUM_STAGE_TYPE_INVALID = 0;       // 无效值
	ENUM_STAGE_TYPE_SIGNUP = 1;        // 报名阶段
	ENUM_STAGE_TYPE_GOLD_GAME = 2;     // 黄金赛
	ENUM_STAGE_TYPE_PT_GAME   = 3;     // 铂金赛
	ENUM_STAGE_TYPE_DIAMOND_GAME = 4;  // 钻石赛
	ENUM_STAGE_TYPE_KING_GAME    = 5;  // 王者赛
	ENUM_STAGE_TYPE_SUPER_KING_GAME = 6;  // 荣耀王者赛
	ENUM_STAGE_TYPE_FIN= 7;            // 结束
}


// 单人榜 报名
enum ESingleSignUpType
{
	ENUM_SINGLE_SIGNUP_INVALID = 0;     // 错误
	ENUM_SINGLE_SIGNUP_MAN   = 1;        // 男生
	ENUM_SINGLE_SIGNUP_WOMAN = 2;	     // 女生
	ENUM_SINGLE_SIGNUP_PRESENTER    = 3; // 主持
	ENUM_SINGLE_SIGNUP_MAN_SINGER   = 4; // 男歌手
	ENUM_SINGLE_SIGNUP_WOMAN_SINGER = 5; // 女歌手
	ENUM_SINGLE_SIGNUP_SKILL        = 6; // 才艺
}

message SingleTypeSignUpInfo
{
	uint32 uid = 1;
	bool is_pay_ticket = 2;
	uint32 single_type = 3; // ESingleSignUpType
	uint32 join_ts = 4;
}

// 多人榜 报名
enum EMutiSignUpType
{
	ENUM_MUTI_SIGNUP_INVALID = 0; // 错误
	ENUM_MUTI_SIGNUP_CP= 1;      // cp 榜
	ENUM_MUTI_SIGNUP_TEAM = 2;	 // 战队榜
}

enum EMutiSignUpQuitReasonType
{
	ENUM_MUTI_SIGNUPQUIT_REASON_INVALID = 0;   // 错误
	ENUM_MUTI_SIGNUPQUIT_REASON_SELF = 1;      // 自己退出
	ENUM_MUTI_SIGNUPQUIT_REASON_KICK = 2;	   // 踢出
	ENUM_MUTI_SIGNUPQUIT_REASON_DISSMISS = 3;  // 解散
	ENUM_MUTI_SIGNUPQUIT_REASON_CLEANUP = 4;   // 清空队伍 与解散不一样 这里保留队长
}
message MutiTypeSignUpMember
{
	uint32 uid = 1;
	uint32 join_ts = 2;
	bool is_pay_ticket = 3; // 我是否买了门票
	bool is_creator = 4;
	uint32 muti_type = 5;  // 我的报名类型
	
}
message MutiTypeTeamInfo
{
	uint32 muti_team_id = 1;    // 队伍ID
	uint32 muti_team_type = 2;  // 队伍的报名类型(就是该队队长自己的报名类型) EMutiSignUpType
	repeated MutiTypeSignUpMember member_full_list = 3; // 全部成员列表
	uint32 creater_uid = 4;     // 队长UID
}

message MutiTypeSignUpInfo
{
	uint32 muti_type = 1;       // 我自己的报名类型 EMutiSignUpType
	
	uint32 muti_team_id = 2;    // 我所在的队伍ID
	uint32 muti_team_type = 3;  // 我所在的队伍的报名类型(就是该队队长自己的报名类型) EMutiSignUpType
	uint32 join_ts = 4;         // 我加入队伍的时间

	// 
	bool is_pay_ticket = 5;
	bool is_creator = 6;
	repeated MutiTypeSignUpMember member_full_list = 7; // 全部成员列表
	
	
	// 我上次退出队伍的原因
	uint32 last_team_quit_reason  = 10; // EMutiSignUpQuitReasonType
	uint32 last_team_quit_id = 11;
	uint32 last_team_quit_op_uid  = 12;
	bool is_need_notify_team_quit = 13;
	uint32 last_team_quit_type  = 14;   // 退出的队伍的队伍类型 EMutiSignUpType
}

// 神豪榜 报名信息
message RichManSignUpInfo
{
	uint32 uid = 1;
	bool is_pay_ticket = 2;
	uint32 join_ts = 3;
}

// 获取用户的报名信息
message GetUserSignUpInfoReq
{
    uint32 uid=1;
}

message GetUserSignUpInfoResp
{
	SingleTypeSignUpInfo single_signup = 1;
	MutiTypeSignUpInfo muti_signup = 2;
	RichManSignUpInfo richman_signup = 3;
}

// 获取用户的多人榜报名信息
message GetUserMutiSignUpInfoReq
{
    uint32 uid=1;
}

message GetUserMutiSignUpInfoResp
{
	MutiTypeSignUpInfo muti_signup = 1;
}

// 设置报名信息
message SetUserSignUpInfoReq
{
    uint32 uid=1;
	bool is_pay_ticket = 2;
	
	// 到底是报名哪个榜单 每次只能报一个
	uint32 single_signup_type = 3;     // ESingleSignUpType
	uint32 muti_signup_type = 4;       // EMutiSignUpType
	bool is_signup_richman = 5; //
}

message SetUserSignUpInfoResp
{
	SingleTypeSignUpInfo single_signup = 1;
	MutiTypeSignUpInfo muti_signup = 2;
	RichManSignUpInfo richman_signup = 3;
}



// 加入 某个多人榜报名 队伍
message JoinMutiTypeTeamReq
{
    uint32 uid=1;
	uint32 muti_team_id = 2;  // 多人报名的ID(队伍ID)
}

message JoinMutiTypeTeamResp
{
	
}

// 退出 多人榜报名 队伍
message QuitMutiTypeTeamReq
{
    uint32 op_uid = 1;
	uint32 target_uid = 2;    // 如果是踢人 那么这里填被踢的成员UID
	uint32 reason_type = 3;   // 退出队伍的原因
	uint32 muti_team_id = 4;  // 多人报名的ID(队伍ID)
}

message QuitMutiTypeTeamResp
{
	uint32 targetuser_new_team_id = 1;  
	uint32 targetuser_muti_type = 2; 
}

// 创建队伍
message CreateMutiTypeTeamReq
{
    uint32 op_uid = 1;
}
message CreateMutiTypeTeamResp
{
    uint32 muti_team_id = 1;  // 多人报名的ID(队伍ID)
}
// 设置用户队伍提醒
message UpdateMutiTypeTeamNotifyReq
{
	uint32 op_uid = 1;
	bool is_open = 2;
}
message UpdateMutiTypeTeamNotifyResp
{
}

// 获取队伍信息
message GetMutiTypeTeamInfoReq
{
	uint32 team_id = 1;
}
message GetMutiTypeTeamInfoResp
{
	MutiTypeTeamInfo muti_info = 1;
}


// 获取报名人数
message SingleTypeSignUpCount
{
	uint32 single_type = 1; // ESingleSignUpType
	uint32 count = 2; 
}
message MutiTypeSignUpCount
{
	uint32 muti_type = 1;       // EMutiSignUpType
	uint32 count = 2; 
}
	
message GetSignupCountReq
{
}
message GetSignupCountResp
{
	repeated SingleTypeSignUpCount single_signup_cnt_list = 1;
	repeated MutiTypeSignUpCount muti_signup_cnt_list = 2;
	uint32 richman_signup_cnt = 3;
}

// 获取当前 年度盛典的阶段
message GetCurrStageReq
{
}
message GetCurrStageResp
{
	uint32 curr_stage = 1; // EStageType
	uint32 curr_stage_begin_ts = 2;
	
	uint32 next_stage = 3; // EStageType
	uint32 next_stage_begin_ts = 4;
}

message NotifyDateChangeReq
{
	uint32 date_int = 1;	// 指定的结算日期，为0时会自动置为昨天
}

// 多人榜
// 排行数据
message MultiTypeTeamMemberRankInfo
{
	uint32 team_id = 1;
	uint32 uid = 2;
	uint32 rank_value = 3;
	bool is_creator = 4;
}

message MultiTypeTeamRankBrief
{
	uint32 team_id = 1;
	uint32 list_type = 2;
	uint32 rank_value = 3;
	uint32 ranking = 4;
	uint32 bonus_value = 5;
}

message MultiTypeTeamRankInfo
{
	MultiTypeTeamRankBrief brief_info = 1;
	repeated MultiTypeTeamMemberRankInfo member_list = 2;
}

// 获取多人榜的队伍榜单
message GetMultiTypeTeamRankListReq
{
	uint32 multi_team_type = 1;       // EMutiSignUpType
	uint32 date_int = 2;	// 0表示总榜
	uint32 stage = 3;		// EStageType
	uint32 offset = 4;
	uint32 count = 5;
}

message GetMultiTypeTeamRankListResp
{
	repeated MultiTypeTeamRankInfo team_list = 1;
	bool is_valid_stage = 2;
	uint32 total_team_count = 3;
}

// 获取多人榜的队伍排行信息
message GetMultiTypeTeamRankInfoReq
{
	uint32 uid = 1;
	uint32 date_int = 2;	// 0表示总榜
	uint32 stage = 3;		// EStageType
	uint32 multi_team_type = 4;
}

message GetMultiTypeTeamRankInfoResp
{
	MultiTypeTeamRankInfo team_info = 1;
	bool is_signed_up = 2;
	uint32 diff_value = 3;	// 距上一名/晋级还差的礼物值
	bool to_be_promoted = 4;	// 此排名是否在晋级范围中
}

// 神豪榜 榜单
message RichmanRankItem
{
	uint32 uid = 1;
	uint32 value = 2;
	uint32 addtion_value = 3;
}

message RichmanMyRankValue
{
	uint32 uid = 1;
	uint32 value = 2;
	uint32 rank = 3;
	bool is_qualified = 4;          // 是否有晋级到本阶段的资格
	uint32 upgrade_dif_value = 5;   // 距离上一级 还差多少 upgrade_dif_value / joinrank_dif_value 两个字段 只有一个有值
	uint32 joinrank_dif_value = 6 ; // 距离进榜 还差多少
}

message GetRichmanRankListReq
{
	uint32 uid = 1;
	uint32 offset = 2;
	uint32 count = 3;
	uint32 stage_id = 4;
	string day = 5;     // day 为空表示获取 总榜数据
	
	bool is_need_my_rank = 6;
}

message GetRichmanRankListResp
{
	repeated RichmanRankItem rank_list = 1;
	RichmanMyRankValue my_rank = 2;
	uint32 total_rank_count = 3;
}

// 个人榜
message SingleRankItem
{
	uint32 uid = 1;
	uint32 value = 2;
	uint32 addtion_value = 3;
}

message SingleMyRankValue
{
	uint32 uid = 1;
	uint32 single_signup_type = 2;  // ESingleSignUpType 如果为0 表示没有报名
	uint32 value = 3;
	uint32 rank = 4;
	uint32 upgrade_dif_value = 5;   // 距离上一级 还差多少 upgrade_dif_value / joinrank_dif_value 两个字段 只有一个有值
	uint32 joinrank_dif_value = 6 ; // 距离进榜 还差多少
	bool is_qualified = 7;          // 是否有晋级的资格
}

message GetSingleRankListReq
{
	uint32 uid = 1;
	uint32 single_type = 2;  // ESingleSignUpType 
	
	uint32 offset = 3;
	uint32 count = 4;
	uint32 stage_id = 5;
	string day = 6;     // day 为空表示获取 总榜数据
	
	bool is_need_my_rank = 7;
}

message GetSingleRankListResp
{
	repeated SingleRankItem rank_list = 1;
	SingleMyRankValue my_rank = 2;
	uint32 total_rank_count = 3;
}

// 房间榜
message ChannelRankItem
{
	uint32 cid = 1;
	uint32 value = 2;
	uint32 addtion_value = 3;
}

message GetChannelRankListReq
{
	uint32 uid = 1;

	uint32 offset = 2;
	uint32 count = 3;
	uint32 stage_id = 4;
	string day = 5;     // day 为空表示获取 总榜数据
}

message GetChannelRankListResp
{
	repeated ChannelRankItem rank_list = 1;
	uint32 total_rank_count = 2;
}

// 战况
message RichmanBattleProgress
{
	uint32 time_ts = 1;
	string msg = 2;
}

message GetRichmanBattleProgressListReq
{

}
message GetRichmanBattleProgressListResp
{
	repeated RichmanBattleProgress progress_list= 1;
}

//
message DoTestReq
{
	uint32 uid = 1; 
	uint32 test_type = 2; // 1 测试活动战况 2 测试神豪TOP3 3 测试礼物事件
	uint32 item_id = 3;
	uint32 target_uid = 4;
	uint32 total_price = 5;
	uint32 send_time = 6;
	uint32 channel_id = 7;
}
message DoTestResp
{
	uint32 cnt = 1;
}

message ShorttimeComsumeInfo
{
	uint32 uid = 1;    
	uint32 price = 2; 
	uint32 ts = 3;   
	string order_id = 4;
}

service awardgame2018 {
	option( tlvpickle.Magic ) = 15235;		// 服务监听端口号

	// 获取当前 年度盛典的阶段
	rpc GetCurrStage( GetCurrStageReq ) returns( GetCurrStageResp ){
		option( tlvpickle.CmdID ) = 1;										
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	} 
	
	// 获取用户的报名信息
    rpc GetUserSignUpInfo( GetUserSignUpInfoReq ) returns( GetUserSignUpInfoResp ){
		option( tlvpickle.CmdID ) = 2;										
        option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid>";
	}
	
    // 设置报名信息
    rpc SetUserSignUpInfo( SetUserSignUpInfoReq ) returns( SetUserSignUpInfoResp ){
		option( tlvpickle.CmdID ) = 3;										
        option( tlvpickle.OptString ) = "u:s:m:r:x:";							
        option( tlvpickle.Usage ) = "-u <uid> -s <single type> -m <muti type> -r <richman> -x <is pay>";
	} 
	
	// 加入 某个多人榜报名 队伍
	rpc JoinMutiTypeTeam( JoinMutiTypeTeamReq ) returns( JoinMutiTypeTeamResp ){
		option( tlvpickle.CmdID ) = 4;										
        option( tlvpickle.OptString ) = "u:x:";							
        option( tlvpickle.Usage ) = "-u <uid> -x <muti signup id>";
	} 
	
	// 退出 某个多人榜报名 队伍 （队长还可以解散队伍 踢人等操作）
	rpc QuitMutiTypeTeam( QuitMutiTypeTeamReq ) returns( QuitMutiTypeTeamResp ){
		option( tlvpickle.CmdID ) = 5;										
        option( tlvpickle.OptString ) = "u:t:";							
        option( tlvpickle.Usage ) = "-u <uid> -t <target uid>";
	} 
	
	// 创建队伍
	rpc CreateMutiTypeTeam( CreateMutiTypeTeamReq ) returns( CreateMutiTypeTeamResp ){
		option( tlvpickle.CmdID ) = 6;										
        option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid> ";
	} 
	
	// 设置用户队伍提醒
	rpc UpdateMutiTypeTeamNotify( UpdateMutiTypeTeamNotifyReq ) returns( UpdateMutiTypeTeamNotifyResp ){
		option( tlvpickle.CmdID ) = 7;										
        option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid> ";
	} 
	
	// 获取队伍信息
	rpc GetMutiTypeTeamInfo( GetMutiTypeTeamInfoReq ) returns( GetMutiTypeTeamInfoResp ){
		option( tlvpickle.CmdID ) = 8;										
        option( tlvpickle.OptString ) = "x:";							
        option( tlvpickle.Usage ) = "-x <team id> ";
	} 
	// 获取报名人数
	rpc GetSignupCount( GetSignupCountReq ) returns( GetSignupCountResp ){
		option( tlvpickle.CmdID ) = 9;										
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	} 

	// 神豪榜
	rpc GetRichmanBattleProgressList( GetRichmanBattleProgressListReq ) returns( GetRichmanBattleProgressListResp ){
		option( tlvpickle.CmdID ) = 11;										
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	} 
	rpc GetRichmanRankList( GetRichmanRankListReq ) returns( GetRichmanRankListResp ){
		option( tlvpickle.CmdID ) = 12;										
        option( tlvpickle.OptString ) = "u:o:s:d:";							
        option( tlvpickle.Usage ) = "-u <uid> -o <offset> -s <size> -d <day>";
	} 
	
	// 个人榜
	rpc GetSingleRankList( GetSingleRankListReq ) returns( GetSingleRankListResp ){
		option( tlvpickle.CmdID ) = 21;										
        option( tlvpickle.OptString ) = "u:t:o:s:d:";							
        option( tlvpickle.Usage ) = "-u <uid> -t <type> -o <offset> -s <size> -d <day>";
	} 
	

	
	// 战队
	rpc GetMultiTypeTeamRankList( GetMultiTypeTeamRankListReq ) returns( GetMultiTypeTeamRankListResp ){
		option( tlvpickle.CmdID ) = 31;										
        option( tlvpickle.OptString ) = "t:d:s:o:n:";							
        option( tlvpickle.Usage ) = "-t <multi_team_type> -d <date_int> -s <stage> -o <offset> -n <count>";
	}
	
	rpc GetMultiTypeTeamRankInfo( GetMultiTypeTeamRankInfoReq ) returns( GetMultiTypeTeamRankInfoResp ){
		option( tlvpickle.CmdID ) = 32;										
        option( tlvpickle.OptString ) = "u:d:s:t:";							
        option( tlvpickle.Usage ) = "-u <uid> -d <date_int> -s <stage> -t <multi_team_type>";
	}
	
	rpc GetUserMutiSignUpInfo( GetUserMutiSignUpInfoReq ) returns( GetUserMutiSignUpInfoResp ){
		option( tlvpickle.CmdID ) = 33;									
        option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid>";
	}
	
	rpc NotifyDateChange( NotifyDateChangeReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 34;									
        option( tlvpickle.OptString ) = "d:";							
        option( tlvpickle.Usage ) = "-d <date_int>";
	}
	
	// 房间榜
	rpc GetChannelRankList( GetChannelRankListReq ) returns( GetChannelRankListResp ){
		option( tlvpickle.CmdID ) = 41;										
        option( tlvpickle.OptString ) = "u:o:s:d:";							
        option( tlvpickle.Usage ) = "-u <uid> -o <offset> -s <size> -d <day>";
	} 
	
	// Test
	rpc DoTest( DoTestReq ) returns( DoTestResp ){
		option( tlvpickle.CmdID ) = 50;										
        option( tlvpickle.OptString ) = "u:t:i:n:r:s:x:";							
        option( tlvpickle.Usage ) = "-u <uid> -t <test type 1=actmsg 2=TOP3 3=send_present> [-i <item_id> -n <total_price> -r <recv_uid> -s <send_time> -x <channel_id>]";
	}
}
