syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";  

// namespace
package checkinasync;

message UserDelayAwardInfo{
    uint32 uid = 1;
    uint32 check_in_type = 2;
    uint32 award_id = 3;
	string device_id = 4;
	uint32 uniq_id = 5;
	uint32 delay_fetch_time = 6;
    uint32 tbean_num = 7;
}

message CheckInAsyncJobDelayAwardNotify
{
	UserDelayAwardInfo delay_award = 1;
	uint32 gift_id = 2;
	uint32 ex_gift_id = 3;
    uint32 async_type = 4; //see ENUM_CHECK_IN_ASYNC_AWARD_TYPE
}

message CheckInAsyncJobDailyCheckInAwardNotify
{
	uint32 uid = 1;
	bytes cfg_info = 2;
	uint32 check_in_type = 3;
}
