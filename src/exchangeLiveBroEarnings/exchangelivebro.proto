syntax="proto3";

import "common/tlvpickle/skbuiltintype.proto";

package exchangelivebro;

// HC 直播 遗产转移

enum ExchangeType
{
  ENUM_EXCHANGE_TBEAN = 0;               // t 豆兑换成礼物
  ENUM_EXCHANGE_USER_EXP = 1;           // 个人等级经验兑换 为 TT 财富等级经验
  ENUM_EXCHANGE_ANCHOR_EXP = 2;        // 主播等级经验兑换 为 TT 魅力等级经验
}

// 操作类型
enum OperType
{
  ENUM_USER_EXCHANGE = 0;                   // 直播用户主动发起 兑换
  ENUM_ERROR_EXCHANGE_ADD = 1;              // 后台补发失败的 兑换
}

/*enum UserType*/
/*{*/
  /*ENUM_USER_INVALID = 0;                // 未知的用户类型*/
  /*ENUM_USER_NO_LIVE = 1;                // 不是直播用户*/
  /*ENUM_USER_LIVE = 2;                   // 直播用户*/
  /*ENUM_USER_LIVE_EX_DONE = 3;           // 完成兑换的用户*/
  /*ENUM_USER_LIVE_NO_EX = 4;            //  未完成兑换的用户*/
/*}*/

enum ExChangeFlag
{
  ENUM_FLAG_NO_EXCHANGE = 0;                    // 未兑换
  ENUM_FLAG_ALREADY_EXCHANGE = 1;              // 已兑换
}

message LiveBroEarnInfo
{
  uint64 tbean = 1;              //   t 豆
  uint64 user_exp = 2;           //   个人等级经验 to TT 财富等级经验
  uint64 anchor_exp = 3;        //    主播等级经验 to TT 魅力等级经验
  uint32 exchange_flag = 4;     //    see ExchangeFlag
}

message PresentPackage
{
  uint32 item_id = 1;               // 礼物 id
  string item_name = 2;             // 礼物 名字
  uint32 item_price = 3;            // t豆价值
  uint32 item_num = 4;              // 兑换数量
  string item_url = 5;             // 礼物图片地址
  uint32 package_id = 6;            // 包裹id
}

message ExchangeScheme
{
  repeated PresentPackage packages = 1;    // 兑换礼物
  uint64 rich_val = 2;                  // 兑换的财富值 from 直播个人等级经验
  uint64 charm_val = 3;                 // 兑换的魅力值 from 主播等级经验
}

message GetLiveUserExchangeInfoReq
{
}

message GetLiveUserExchangeInfoResp
{
  LiveBroEarnInfo live_info = 1;
  ExchangeScheme exchange_scheme = 2;
}


message ExchangeLiveEarnReq
{
  /*uint32 exchange_time = 1;*/
}

message ExchangeLiveEarnResp
{
  uint64 exchanged_tbean = 1;              // 成功兑换的 t 豆
  uint64 exchanged_user_exp = 2;          // 成功兑换的 个人等级经验
  uint64 exchanged_anchor_exp = 3;        // 成功兑换的 直播等级经验
}

message GetLiveUserExchangeLogReq
{
}

message GetLiveUserExchangeLogResp
{
  bool have_log = 1;            // true 有兑换日志，false 无日志
  uint64 tbean = 2;
  uint64 user_exp = 3;
  uint64 anchor_exp = 4;
  uint32 update_time = 5;
}

message AddLiveUserReq
{
  uint64 tbean = 1;              //   t 豆
  uint64 user_exp = 2;           //   个人等级经验 to TT 财富等级经验
  uint64 anchor_exp = 3;        //    主播等级经验 to TT 魅力等级经验
}

message AddLiveUserResp
{
}

service exchangelivebro {
	option( tlvpickle.Magic ) = 15665;

    rpc GetLiveUserExchangeInfo ( GetLiveUserExchangeInfoReq ) returns (GetLiveUserExchangeInfoResp){
      option (tlvpickle.CmdID) = 1;
      option (tlvpickle.OptString) = "u:";
      option (tlvpickle.Usage) = "-u <uid>";
    }

    rpc ExchangeLiveEarn (ExchangeLiveEarnReq) returns (ExchangeLiveEarnResp)
    {
      option (tlvpickle.CmdID) = 2;
      option (tlvpickle.OptString) = "u:";
      option (tlvpickle.Usage) = "-u <uid>";
    }

    rpc GetLiveUserExchangeLog (GetLiveUserExchangeLogReq) returns (GetLiveUserExchangeLogResp)
    {
      option (tlvpickle.CmdID) = 3;
      option (tlvpickle.OptString) = "u:";
      option (tlvpickle.Usage) = "-u <uid>";
    }

    rpc AddLiveUser (AddLiveUserReq) returns (AddLiveUserResp)
    {
      option (tlvpickle.CmdID) = 4;
      option (tlvpickle.OptString) = "u:t:e:a:f:";
      option (tlvpickle.Usage) = "-u <uid> -t <Tbean> -e <user_exp> -a<anchor> -f<exchange_flag>";
    }
}
