syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package GuildTimeline;									

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list


//公会游戏
message stGuildGame {
    required uint32 game_id = 1; // 游戏id
    required string name = 2;
    required string icon_id = 3;    //标识头像资源id
}

message stGuildDetail {
	required uint32 seq_id = 1;			// 序列号
    required uint32 guild_id = 2;             //内部使用的id，不可改
    required uint32 guild_display_id =3;     //给用户看到的公会id
    required string name = 4;
    required string desc = 5;
    required stGuildMember chair_man = 6;		//会长
    required uint32 member_count = 7;		//人数
    required string icon_id = 8;			//头像id
    required uint32 create_date = 9;		//创建时间
    repeated stGuildGame games = 10;			//公会游戏
    repeated stGuildMember index_mem_list = 11;	// 工会首页前几个会员
    repeated stGuildPhoto index_photo_list = 12;	// 工会首页前几张缩略图
}

message CheckInMem {
	required uint32 uid = 1;		// uid
	required uint32 days = 2;		// 连续签到次数
}

message stGuildCheckinStamp {
	required uint32 last_update_time = 1;			// 最后更新签到的时间
	required uint32 last_seq_id = 2;				// 最后更新签到的seq
	required uint32 checkin_num = 3;				// 签到人数
	repeated CheckInMem checkin_top_n_list = 4;		// 签到的前几个人的uid
}

// 
message stGuildPhoto {
    required string thumb_id = 1;   //缩略图id
    required string photo_id = 2;   //图片id
}

//-----------------------------------------
// UpdateGuildInfo
//-----------------------------------------
message UpdateGuildInfoReq {
	required uint32 guild_id = 1;		// 工会ID
	required stGuildDetail guild_info = 2;	// 工会资料
}

message UpdateGuildInfoResp {
	// nothing
}

message stGuildMember {
    required uint32 uid = 1;
    required string name = 2;
	required uint32 role = 3;
	required string remark = 4;		// 勋章（资深玩家）
	required uint32 seq_id = 5;		// 序列号ID
	required string account = 6;	// 用户唯一账号
}

//-----------------------------------------
// UpdateGuildMemberInfo
//-----------------------------------------
message UpdateGuildMemberInfoReq {
	required uint32 guild_id = 1;		// 工会ID
	required uint32 uid = 2;			// 成员uid
	required stGuildMember mem_info = 3;		// 成员资料
}

message UpdateGuildMemberInfoResp {
}

//-----------------------------------------
// 更新工会签到seq
//-----------------------------------------
message UpdateGuildCheckinSeqReq {
	required uint32 guild_id = 1;						// 工会ID
	required stGuildCheckinStamp checkin_info = 2;			// 工会checkin信息
}

message UpdateGuildCheckinSeqResp {
}

//-----------------------------------------
// GetUpdateInfoBySeq
//-----------------------------------------
message GetUpdateInfoBySeqReq {
	required uint32 guild_id = 1;	// 工会ID
	required uint32 seq_id = 2;		// seq_id
}

message GetUpdateInfoBySeqResp {
	optional stGuildDetail guild_detail = 1;			// 工会信息，如果有需要更新的话
	repeated stGuildMember guild_mem_list = 2;			// 成员信息
	optional stGuildCheckinStamp check_in_stamp = 3;	// 如果有的话，表示需要更新checkin
}

message stTimelineData_Giftpkg
{
	required uint32 game_id = 1;
}

message stTimelineData {
	required string key_prefix = 1;		// 如"detail","group","mem","checkin","game","album","giftpkg"
	required uint32 key_id = 2;			// 如[群ID],[成员uid],[游戏id],[相册id],[礼包id]
	required bool is_deleted = 3;		// 是否删除
	required uint32 seq_id = 4;			// 序列号
	optional bytes bin_value = 5;		// timeline内容
}

message GetTimelineBySeqReq {
	required uint32 guild_id = 1;
	required uint32 seq_id = 2;
}

message GetTimelineBySeqResp {
	repeated uint32 seq_id_list = 1;			// 序列号
	repeated stTimelineData data_list = 2;		// seq
}

message UpdateTimelineReq {
	required uint32 guild_id = 1;
	required uint32 seq_id = 2;
	required stTimelineData data = 3;
}

message UpdateTimelineResp {				
}

message BatchDeleteTimelineReq {
	required uint32 guild_id = 1;
	repeated uint32 seq_id_list = 2;
}

message BatchDeleteTimelineResp {
}

//////////////////
service GuildTimeline {
    option( tlvpickle.Magic ) = 14800;		// 服务监听端口号

    rpc UpdateGuildInfo(UpdateGuildInfoReq) returns(UpdateGuildInfoResp) {
        option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "g:s:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id>";			// 测试工具的命令号帮助
    }
    
    rpc UpdateGuildMemberInfo(UpdateGuildMemberInfoReq) returns (UpdateGuildMemberInfoResp) {
        option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "g:u:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -u <uid> -s <seq_id>";			// 测试工具的命令号帮助
    }
    
    rpc GetUpdateInfoBySeq(GetUpdateInfoBySeqReq) returns(GetUpdateInfoBySeqResp) {
        option( tlvpickle.CmdID ) = 3;										// 命令号
        option( tlvpickle.OptString ) = "g:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id>";			// 测试工具的命令号帮助
    }
    
    rpc UpdateGuildCheckinSeq(UpdateGuildCheckinSeqReq) returns(UpdateGuildCheckinSeqResp) {
        option( tlvpickle.CmdID ) = 4;										// 命令号
        option( tlvpickle.OptString ) = "g:s:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id>";			// 测试工具的命令号帮助
    }
    
    rpc GetTimelineBySeq(GetTimelineBySeqReq) returns (GetTimelineBySeqResp) {
        option( tlvpickle.CmdID ) = 5;										// 命令号
        option( tlvpickle.OptString ) = "g:s:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id>";			// 测试工具的命令号帮助
    }
    
    rpc UpdateTimeline(UpdateTimelineReq) returns (UpdateTimelineResp) {
        option( tlvpickle.CmdID ) = 6;																		// 命令号
        option( tlvpickle.OptString ) = "g:s:p:u:d:";														// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id> -p <prefix> -u <id> -d <is_delete>";			// 测试工具的命令号帮助
    }
    
    rpc BatchDeleteTimeline(BatchDeleteTimelineReq) returns (BatchDeleteTimelineResp) {
        option( tlvpickle.CmdID ) = 7;																		// 命令号
        option( tlvpickle.OptString ) = "g:s:";																// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id>";											// 测试工具的命令号帮助
    }
}
