syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package eventcenter;

enum PUNISH_TYPE {
	CHANNEL_EVENT = 1;
    GUILD_EVENT = 2;
    LOGIN_EVENT = 3;
}

enum EVENT_TYPE {
	CHANNEL_USER_ENTER = 1;
	CHANNEL_USER_QUIT = 2;
    GUILD_USER_JOIN = 3;
    USER_LOGIN = 4;
}

//---------------------------
// 事件结构体
//---------------------------
message Event
{
	required uint32 event_type = 1;
	required bytes event_bin = 2;
	required uint32 seq_id = 3;			// 一种PublishType对应独立的一个seq序列（暂时用不上）
	required uint32 event_sec = 4;		// 消息发布时间，用于检测是否延迟，秒
	required uint32 event_usec = 5;		// 微秒
}

//---------------------------
// 事件定义
//---------------------------
message Event_ChannelUserEnter
{
	required uint32 uid = 1;
	required uint32 chid = 2;
}

message Event_ChannelUserQuit
{
	required uint32 uid = 1;
	required uint32 chid = 2;
}

message Event_GuildUserJoin
{
    required uint32 uid = 1;
    required uint32 guild_id = 2;
}

message Event_UserLogin
{
	enum LOGIN_TYPE {
		REG = 1;		// 注册
		MANUAL = 2;		// 手工登录
		AUTO_LOGIN = 3;	// 自动登录
	}
    required uint32 uid = 1;                // 用户ID
    required uint32 client_type = 2;        // 客户端类型
    required uint32 client_version = 3;     // 客户端版本
    required string device_id_hex = 4;      // 设备ID的十六进制表示
    optional string channel = 5;            // 渠道号
    optional bool is_auto_login = 6;        // 是否手动登录
    optional uint32 login_time = 7;			// 登录时间
    optional string imei = 8;				// imei
    optional uint32 login_type = 9;			// 登录类型
	optional string os_ver = 10;			// 操作系统版本
	optional string os_type = 11;			// 操作系统类型
	optional string device_model = 12;		// 机器型号
	optional uint32 current_guild_id = 13;	// 用户当前所在guildid
	optional uint32 join_guild_at = 14;		// 加入公会的时间
	optional uint32 last_quit_guild_id = 15;// 上次退出的公会
	optional bool is_activate_login = 16;	// 是否激活登录（SDK）
	optional uint32 source = 17;			// 用户来源（TT用户，还是SDK用户）
	optional string user_source = 18;		// 来源（代替原来的channel渠道）
	optional string directto = 19;			// 跳转至

    optional string client_ip = 20;
    optional string device_info = 21;
}

//---------------------------
// 发布事件
//---------------------------
message PublishReq {
	required uint32 publish_type = 1;
	required uint32 event_type = 2;
	required bytes event_bin = 3;
}

message PublishResp {
}

//---------------------------
// 同步事件，请求  (begin_seq_id, max_seq_id] 的事件列表
//---------------------------
message SyncReq {
	required uint32 publish_type = 1;
	required uint32 begin_seq_id = 2;
}

message SyncResp {
	required uint32 max_seq_id = 1;
	repeated Event event_list = 2;
}


service EventCenter {
	option( tlvpickle.Magic ) = 14004;		// 服务监听端口号

	rpc Publish( PublishReq ) returns ( PublishResp ){
		option( tlvpickle.CmdID ) = 1;									// 命令号
        option( tlvpickle.OptString ) = "p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <publish_type>";				// 测试工具的命令号帮助
	}

	rpc Sync( SyncReq ) returns ( SyncResp ){
		option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "p:b:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <publish_type> -b <begin_seq_id>";	// 测试工具的命令号帮助
	}
}
