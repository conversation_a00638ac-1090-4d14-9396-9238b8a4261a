syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package PublicAccount;

//////////////////////////////////////////////////////////////////
//  公众号相关 类型/Req/Resp 定义
//////////////////////////////////////////////////////////////////

enum PublicAccountType {
	NOT_BINDED = 1;			// 未绑定的公众号
    SYSTEM = 50;            // 系统公众号, 所有账号默认关注, 不能取消关注

	BIZ_TYPE_BEGIN = 100;	// 业务公众号类型由此开始
	BIZ_TYPE_CIRCLE = 101;	// 圈子公众号
    BIZ_TYPE_UGC_TOPIC = 102;//异步内容 主题公众号
}

// 公众号类型
message StPublicAccount
{
	required uint32 public_id = 1;		// 公众号ID
	required string account = 2;		// 帐号，用于头像服务
	required string name = 3;			// 名称
	required uint32 type = 4;			// 类型
	required uint64 binded_id = 5;		// 公众号绑定的ID，由业务决定ID的具体意义
    optional string authority = 6;      // 认证主体
    optional string authority_url = 7;  // 认证主体url
    required string intro = 8;          // 简介
    optional uint32 update_time = 9;    // 更新时间戳
}

message CreatePublicAccountReq
{
    required string name = 1;			// 名称
    required uint32 type = 2;			// 类型
    required uint64 binded_id = 3;		// 绑定的ID
    optional string authority = 4;      // 认证主体
    optional string authority_url = 5;  // 认证主体url
    optional string intro = 6;          // 简介
}

message CreatePublicAccountResp
{
    required uint32 public_id = 1;		// 创建成功: 公众号ID
    required string account = 2;		// 创建成功: 帐号
}

message GetPublicAccountReq
{
    required uint32 public_id = 1;
}

message GetPublicAccountResp
{
    required StPublicAccount public_account = 1;
}

message GetPublicAccountByBindedIdReq
{
    required uint32 type = 1;
    required uint64 binded_id = 2;
}

message GetPublicAccountByBindedIdResp
{
    required StPublicAccount public_account = 1;
}

message GetPublicAccountsByIdListReq {
	repeated uint32 public_id_list = 1;
}

message GetPublicAccountsByIdListResp {
	repeated StPublicAccount public_account_list = 1;
}

message GetPublicAccountsByBindedIdListReq {
	required uint32 type = 1;
	repeated uint64 binded_id_list = 2;
}

message GetPublicAccountsByBindedIdListResp {
	repeated StPublicAccount public_account_list = 1;
}

message SetPublicAccountAutoReplyReq
{
    required uint32 public_id = 1;
    required string auto_reply = 2;
}

message SetPublicAccountAutoReplyResp
{
}

message GetPublicAccountAutoReplyReq
{
    required uint32 public_id = 1;
}

message GetPublicAccountAutoReplyResp
{
    required string auto_reply = 2;
}

message UpdatePublicAccountReq
{
    required uint32 public_id = 1;      // 公众号ID
    optional string name = 2;           // 名称
    optional string authority = 3;      // 认证主体
    optional string authority_url = 4;  // 认证主体url
    optional string intro = 5;          // 简介
    optional uint32 update_time = 6;    // 更新`更新时间`
}

message UpdatePublicAccountResp
{
}


//////////////////////////////////////////////////////////////////
//  公众号配置相关 类型/Req/Resp 定义
//////////////////////////////////////////////////////////////////

// 账号配置(工具栏)
message PublicAccountConfig {
    required uint32 public_id = 1;
    required bytes config_binary = 2;
}

message UpdatePublicAccountConfigReq {
    required PublicAccountConfig public_config = 1;
}

message BatchGetPublicAccountConfigReq {
    repeated uint32 public_id_list = 1;
}

message BatchGetPublicAccountConfigResp {
    repeated PublicAccountConfig public_config_list = 1;
}

// 默认消息配置
message PublicAccountDefaultMessage {
    required uint32 message_id = 1;
    required uint32 public_id = 2;
    required bytes message_binary = 3;
    optional bool is_test = 4;
    repeated uint32 test_uid_list = 5;
}

message AddOrUpdatePublicAccountDefaultMessageReq {
    required uint32 public_id = 1;
    required PublicAccountDefaultMessage message = 2;
}

message DeletePublicAccountDefaultMessageReq {
    required uint32 public_id = 1;
    required uint32 message_id = 2;
}

message GetPublicAccountDefaultMessagesReq {
    required uint32 public_id = 1;
}

message GetPublicAccountDefaultMessagesResp {
    repeated PublicAccountDefaultMessage message_list = 1;
}

service Public {
	option( tlvpickle.Magic ) = 15090;		// 服务监听端口号

	rpc CreatePublicAccount( CreatePublicAccountReq ) returns( CreatePublicAccountResp ) {
        option( tlvpickle.CmdID ) = 1;              // 命令号
        option( tlvpickle.OptString ) = "n:t:b:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <name> -t <type> -b <binded_id>";    // 测试工具的命令号帮助
	}

	rpc GetPublicAccount( GetPublicAccountReq ) returns( GetPublicAccountResp ) {
        option( tlvpickle.CmdID ) = 2;              // 命令号
        option( tlvpickle.OptString ) = "p:";       // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <public_id>";    // 测试工具的命令号帮助
	}

	rpc GetPublicAccountByBindedId( GetPublicAccountByBindedIdReq ) returns( GetPublicAccountByBindedIdResp ) {
        option( tlvpickle.CmdID ) = 3;              // 命令号
        option( tlvpickle.OptString ) = "t:b:";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <type> -b <binded_id>";    // 测试工具的命令号帮助
	}

    rpc SetPublicAccountAutoReply( SetPublicAccountAutoReplyReq ) returns( SetPublicAccountAutoReplyResp ) {
        option( tlvpickle.CmdID ) = 4;              // 命令号
        option( tlvpickle.OptString ) = "p:r:";       // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <public_id> -r <reply>";    // 测试工具的命令号帮助
    }

    rpc GetPublicAccountAutoReply( GetPublicAccountAutoReplyReq ) returns( GetPublicAccountAutoReplyResp ) {
        option( tlvpickle.CmdID ) = 5;              // 命令号
        option( tlvpickle.OptString ) = "p:";       // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <public_id>";    // 测试工具的命令号帮助
    }

    rpc UpdatePublicAccount( UpdatePublicAccountReq ) returns( UpdatePublicAccountResp ) {
        option( tlvpickle.CmdID ) = 6;              // 命令号
        option( tlvpickle.OptString ) = "p:n:a:u:"; // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <public_id> -n <name> -a <authority> -u <authority_url>";   // 测试工具的命令号帮助
    }

	rpc GetPublicAccountsByIdList( GetPublicAccountsByIdListReq ) returns( GetPublicAccountsByIdListResp ) {
        option( tlvpickle.CmdID ) = 7;              // 命令号
        option( tlvpickle.OptString ) = "p:";       // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <public_id_list_separate_by_comma>";    // 测试工具的命令号帮助
	}

	rpc GetPublicAccountsByBindedIdList( GetPublicAccountsByBindedIdListReq ) returns( GetPublicAccountsByBindedIdListResp ) {
        option( tlvpickle.CmdID ) = 8;              // 命令号
        option( tlvpickle.OptString ) = "t:b:";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <type> -b <binded_id_list_separate_by_comma>";    // 测试工具的命令号帮助
	}

    rpc UpdatePublicAccountConfig( UpdatePublicAccountConfigReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc BatchGetPublicAccountConfig( BatchGetPublicAccountConfigReq ) returns( BatchGetPublicAccountConfigResp ) {
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "p:";
        option( tlvpickle.Usage ) = "-p <public_id>";
    }

    rpc AddOrUpdatePublicAccountDefaultMessage ( AddOrUpdatePublicAccountDefaultMessageReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 20;
        option( tlvpickle.OptString ) = "p:n:m:t:u:";
        option( tlvpickle.Usage ) = "-p <public_id> -n <message_id> -m <message> -t <is_test> -u <test_uid_list>";
    }

    rpc DeletePublicAccountDefaultMessage ( DeletePublicAccountDefaultMessageReq ) returns (tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 21;
        option( tlvpickle.OptString ) = "p:m:";
        option( tlvpickle.Usage ) = "-p <public_id> -m <message_id>";
    }

    rpc GetPublicAccountDefaultMessages ( GetPublicAccountDefaultMessagesReq ) returns ( GetPublicAccountDefaultMessagesResp ) {
        option( tlvpickle.CmdID ) = 22;
        option( tlvpickle.OptString ) = "p:";
        option( tlvpickle.Usage ) = "-p <public_id>";
    }
}

