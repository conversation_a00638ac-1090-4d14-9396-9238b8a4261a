syntax = "proto3";

import "proxy_notify.proto";
import "usergroup_relation_register.proto";

package PushNotification;

option go_package = "golang.52tt.com/protocol/services/push-notification/v2;push_notification";

message PushMessage {
    oneof request_type {
        notify.PushWithUserList push_with_uid_list                  = 1;
        notify.PushWithMulticastAccount push_with_multicast_account = 2;
        RelationEvent relation_event                                = 3;
    }

    string request_id = 32;
    string dye_id = 33;
}

message RelationEvent {
    repeated UserGroupRelationReg.UserGroupRelationRegistEvent events = 1;
    uint64 server_received_at                                         = 2;
}

message PushLimitingMessage {
    repeated string groups = 1;
    bytes data             = 2;
    uint32 priority        = 3;  //推送优先级 仅限限流广播消息
    uint32 payload_length  = 4;
}