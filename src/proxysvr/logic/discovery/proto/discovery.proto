syntax="proto3";

package proxy.logic;

message Address {
  enum Protocol {
    TCP = 0;
    UDP = 1;  // reserved 
  }

  Protocol protocol = 1;

  string address = 2;
  uint32 port = 3;

  string tag = 4; // deprecated, use tag_selectors instead
  uint32 weight = 5;
  string description = 6;
  repeated string tags = 7; 
}

message Service {
  string name = 1;
  repeated Address addresses = 2;
}

message RouteRule {
  string namespace = 1;
  uint32 command = 2;
  repeated string backends = 3;
  string tag_selector = 4;  // deprecated, use tag_selectors instead
  string description = 5;
  repeated string tag_selectors = 6;
}

message NamedCommand {
  uint32 command = 1;
  string name = 2;
}