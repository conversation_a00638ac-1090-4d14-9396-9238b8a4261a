syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package numericsvr;

message GetGuildNumericListReq {
	required uint32 guild_id = 1;
	required uint32 index = 2;
	required uint32 count = 3;
}

message GuildMemerNumeric {
	required uint32 uid = 1;
	required uint32 numeric = 2;
    optional uint64 numeric64 = 3;
}

message GetGuildNumericListResp {
	repeated GuildMemerNumeric numeric_list = 1;
}


message GetPersonalNumericReq {
	required uint32 uid = 1;
}

message GetPersonalNumericResp {
	required uint32 consume_numeric = 1; // 土豪值
	required uint32 charm_numeric = 2;   // 魅力值
    optional uint64 consume_numeric64 = 3;
    optional uint64 charm_numeric64 = 4;
}


message BatchGetPersonalNumericReq {
	repeated uint32 uid_list = 1;
}

message PersonalNumeric{
	required uint32 uid = 1;
	required uint32 charm = 2;
	required uint32 rich = 3;
    optional uint64 charm64 = 4;
    optional uint64 rich64 = 5;
}

message BatchGetPersonalNumericResp {
	repeated PersonalNumeric numeric_list = 1;
}

// 记录 用户送礼物的行为
// 导致更新Numeric 可能引起公会土豪魅力值变化 也可能引起用户的土豪魅力值变化
message RecordSendGiftEventReq {

	required uint32 giver_uid = 1;
	required uint32 receiver_uid = 2;
	required uint32 rich_value = 3;
	
	required uint32 giver_guild = 4;    // 一定是发礼物者的公会
	required uint32 receiver_guild = 5; // 一定是收礼物者的公会
	required uint32 charm_value = 6;
	required string order_id = 7;
	
	optional uint32 channel_id = 8;     // 如果发礼物是在房间内 这里是房间ID
	optional uint32 channel_guild = 9;  // 如果发礼物是在房间内 且是公会房 这里是房间对应的公会ID
	optional uint32 price_type = 10;	// 货币类型
    optional uint32 gift_id = 11; //礼物ID
}

message RecordSendGiftEventResp 
{
	repeated uint32 level_chanage_uid_list = 1; // 用户土豪魅力值等级 如果有变化才会在列表中
	optional uint32 real_charm = 2; // 收礼者 实际增加的魅力值
	
	optional uint32 giver_curr_all_rich  = 3; // 送礼者 在送礼后的 完整的土豪值
	optional uint32 receiver_curr_all_charm = 4; // 收礼者 在送礼后的 完整的魅力值

	optional uint32 real_rich = 5; // 送礼者 实际增加的土豪值
    optional uint32 before_rich = 6; //送礼前的土豪值
    optional uint64 before_rich64 = 7; //送礼前的土豪值

    optional uint64 giver_curr_all_rich64  = 8; // 送礼者 在送礼后的 完整的土豪值
    optional uint64 receiver_curr_all_charm64 = 9; // 收礼者 在送礼后的 完整的魅力值
}

message UserGiftEventInfo{
	required uint32 uid = 1;
	optional uint32 guild_id = 2;
	optional uint32 add_value = 3;
	optional uint32 final_value = 4;
	optional bool level_change = 5;
    optional uint64 final_value64 = 6;
}

// 记录 用户批量送礼物的行为
// 导致更新Numeric 可能引起公会土豪魅力值变化 也可能引起用户的土豪魅力值变化
message BatchRecordSendGiftEventReq {

	required UserGiftEventInfo giver_user_info = 1;
	repeated UserGiftEventInfo receiver_user_info_list = 2;
	required string order_id = 3;
	optional uint32 channel_id = 4;     // 如果发礼物是在房间内 这里是房间ID
	optional uint32 channel_guild = 5;  // 如果发礼物是在房间内 且是公会房 这里是房间对应的公会ID
	optional uint32 price_type = 6;	// 货币类型
    optional uint32 gift_id = 7; // 货币类型
}

message BatchRecordSendGiftEventResp 
{
	optional UserGiftEventInfo giver_user_info  = 1; // 送礼者 在送礼后的 土豪值信息
	repeated UserGiftEventInfo receiver_user_info_list = 2; // 收礼者 在收礼后的 魅力值信息
}

// 记录 用户消费行为
// 导致更新Numeric 只可能引起公会土豪魅力值变化
enum CONSUME_TYPE
{
	ENUM_CONSUME_TT_GAME = 1;
	ENUM_CONSUME_HAPPYCITY = 2;
}

message RecordConsumeEventReq {
	required uint32 uid = 1;
	required uint32 guild_id = 2;
	required uint32 rich_value = 3;
	required uint32 consume_type = 4;
	required string order_info = 5;
}

message RecordConsumeEventResp {
}


message NotifyGuildChangeReq {
	required uint32 uid = 1;
	required uint32 guild_id = 2;
}

message GetGuildGiftTotalValueReq {
	required uint32 guild_id = 1;
}

message GetGuildGiftTotalValueResp {
	required uint32 gift_value = 1;
	optional int64  rich_value64 = 2; // 64位表示的 gift_value
    optional int64  gift_value64 = 3; // 64位表示的 gift_value
}

message GetPersonalRankingReq{
	required uint32 uid = 1;
}

message GetPersonalRankingResp {
	required uint32 week_charm_ranking = 1;
	required uint32 day_charm_ranking = 2;
	required uint32 week_rich_ranking = 3;
	required uint32 day_rich_ranking = 4;

    optional uint32 month_charm_ranking = 5;
    optional uint32 month_rich_ranking = 6;
}

message GetRankListReq {
	enum RANK_TYPE{
		WEEK_RICH = 1;
		DAY_RICH = 2;
		WEEK_CHARM = 3;
		DAY_CHARM = 4;

        MONTH_RICH = 5;
        MONTH_CHARM = 6;
	}
	required uint32 index = 1;
	required uint32 count = 2;
	required uint32 rank_type = 3;
	optional uint32 uid = 4;
}

message GetRankListResp {
	repeated uint32 rank_list = 1;
	optional uint32 rank_last = 2; // 上一周期
	optional uint32 rank_now = 3; // 当前周期（实时）
	optional uint32 gap_to_rise_rank = 4; // 距离上一名/上榜 还需多少
}


enum AddUserNumericType
{
  ENUM_ADD_TYPE_INVALID = 0;
  ENUM_ADD_TYPE_LIVE_TO_TT = 1;             // 直播业务调整，将直播经验转换成 财富值魅力值
}

message AddUserNumericReq
{
  required uint32 uid = 1;
  required uint32 add_type = 2;             // see AddUserNumericType
  optional uint64 rich_value= 3;
  optional uint64 charm_value= 4;

}

message AddUserNumericResp
{
  required uint64 final_rich_value = 1;
  required uint64 final_charm_value = 2;
}



service NumericSvr{
    option( tlvpickle.Magic ) = 15570;

    rpc GetGuildConsumeList ( GetGuildNumericListReq ) returns ( GetGuildNumericListResp ) {
        option( tlvpickle.CmdID ) = 1;				
	    option( tlvpickle.OptString ) = "g:";							
        option( tlvpickle.Usage ) = "-g <guild_id>";	
    }

	rpc GetGuildCharmList ( GetGuildNumericListReq ) returns ( GetGuildNumericListResp ) {
        option( tlvpickle.CmdID ) = 2;					
	    option( tlvpickle.OptString ) = "g:";		
        option( tlvpickle.Usage ) = "-g <guild_id>";
    }

	rpc GetPersonalNumeric ( GetPersonalNumericReq ) returns ( GetPersonalNumericResp) {
        option( tlvpickle.CmdID ) = 3;									
	    option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid>";	
    }
	
	// 由送礼物 导致更新Numeric
	rpc RecordSendGiftEvent ( RecordSendGiftEventReq ) returns (RecordSendGiftEventResp ) {
        option( tlvpickle.CmdID ) = 4;										
	    option( tlvpickle.OptString ) = "u:g:r:t:m:a:o:";							
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -r <rich> -t <target_id> -m <target_guild> -a <charm> -o <order_id>";	
    }

	rpc NotifyGuildQuit ( NotifyGuildChangeReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 6;								
	    option( tlvpickle.OptString ) = "g:u:";							
        option( tlvpickle.Usage ) = "-g <guild_id> -u <uid>";
    }

	rpc NotifyGuildJoin ( NotifyGuildChangeReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 7;		
	    option( tlvpickle.OptString ) = "g:u:";						
        option( tlvpickle.Usage ) = "-g <guild_id> -u <uid>";	
    }


	rpc GetGuildGiftTotalValue ( GetGuildGiftTotalValueReq ) returns ( GetGuildGiftTotalValueResp ) {
        option( tlvpickle.CmdID ) = 8;								
	    option( tlvpickle.OptString ) = "g:";			
        option( tlvpickle.Usage ) = "-g <guild_id>";	
    }
	
	// 由现金消费 导致更新Numeric
	rpc RecordConsumeEvent ( RecordConsumeEventReq ) returns ( RecordConsumeEventResp) {
        option( tlvpickle.CmdID ) = 9;										
	    option( tlvpickle.OptString ) = "u:g:r:o:";							
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -r <rich> -o <order_id>";	
    }
	
	rpc GetPersonalRanking ( GetPersonalRankingReq ) returns ( GetPersonalRankingResp) {
        option( tlvpickle.CmdID ) = 10;										
	    option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid>";	
    }
		
	rpc GetRankList ( GetRankListReq ) returns ( GetRankListResp) {
        option( tlvpickle.CmdID ) = 11;										
	    option( tlvpickle.OptString ) = "u:n:t:";							
        option( tlvpickle.Usage ) = "-u <uid> -n <topN> -t <rank_type>";	
    }
	
	rpc BatchGetPersonalNumeric ( BatchGetPersonalNumericReq ) returns ( BatchGetPersonalNumericResp) {
        option( tlvpickle.CmdID ) = 12;										
	    option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid;uid;uid;>";	
    }
	
	// 由批量送礼物 导致更新Numeric
	rpc BatchRecordSendGiftEvent ( BatchRecordSendGiftEventReq ) returns (BatchRecordSendGiftEventResp ) {
        option( tlvpickle.CmdID ) = 13;										
	    option( tlvpickle.OptString ) = "u:g:r:t:m:a:o:p:";							
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -r <rich> -t <target_id> -m <target_guild> -a <charm> -o <order_id> -p <price_type>";	
    }

    rpc AddUserNumeric (AddUserNumericReq) returns (AddUserNumericResp)
    {
        option( tlvpickle.CmdID ) = 14;										
	    option( tlvpickle.OptString ) = "u:r:x:";							
        option( tlvpickle.Usage ) = "-u <uid> -r <add rich_value> -x <add charm_value> ";	
    }

    rpc GetPersonalRankingFromLocalCache ( GetPersonalRankingReq ) returns ( GetPersonalRankingResp) {
            option( tlvpickle.CmdID ) = 15;
    	    option( tlvpickle.OptString ) = "u:";
            option( tlvpickle.Usage ) = "-u <uid>";
    }
}
 
