syntax = "proto2";

// namespace
package HotWordSearch;
option go_package = "golang.52tt.com/protocol/services/hotwordsearch;hotwordsearch";

// basic - 基本数据元     frontend - 后台配置相关 
// client - 客户端相关    test - 测试相关

// ======================================= basic ==============================================

// 展示样式
enum HotWordType {
    ENUM_DEFAULT_TYPE = 1;                     // 普通
    ENUM_HIGHLIGHT_TYPE = 2;                   // 强调
}

// 数据来源
enum SourceType {
    ENUM_SEARCH_RECORD = 1;                    // 搜索关键词 
    ENUM_PRESS_RECORD = 2;                     // 发布房间名
}

// 测试指令
enum TestType {
    ENUM_LIST_ALLKEY = 1;                      // 列出所有RedisKey
    ENUM_DELETE_ALLKEY = 2;                    // 删除所有RedisKey
    ENUM_DELETE_ONEKEY = 3;                    // 删除指定RedisKey
}

// 热词数据元
message HotWord {
    required string word = 1;                  // 内容
    required HotWordType type = 2;             // 样式
}

// 热词组
message HotWordGroup {
    required uint32 begin_point_second = 1;    // 起始展示时间 UTC时间 自1970/1/1起的秒数
    required uint32 end_point_second = 2;      // 终止展示时间 UTC时间 自1970/1/1起的秒数
    required uint32 group_idx = 3;             // 热词组Idx，允许非连续的组序号
    repeated HotWord hot_word_list = 4;        // 热词组列表
    optional uint32 version = 5;               // version 1为猜你想搜
}

// 敏感词操作记录
message BlackListsOp {
    required string opt_name = 1;              // 登陆管理员用户名
    required bool addOp = 2;                   // true：增加 false：删除
    repeated string word_list = 3;             // 内容列表
    required uint32 time_stamp = 4;            // 登陆时间
}

// 排名数据元
message RankAndScore {
    required string word = 1;                  // 关键词
    required int64 count = 2;                  // 计数
}

// ======================================= frontend ==============================================

message HotWordSearchGetGroupsReq {
    required uint32 start_idx = 1;             // 分页起始idx，从0开始
    required uint32 required_size = 2;         // 分页大小
    optional uint32 version = 3;//版本大小 1
}
message HotWordSearchGetGroupsResp {
    repeated HotWordGroup group_list = 1;      // 热词组列表
    required uint32 total = 2;                 // 后台存储的热词组总数
}

message HotWordSearchSetGroupsReq {
    required HotWordGroup group = 1;           // 热词组
    optional uint32 mode = 2;//0  覆盖 1 新增
    optional uint32 group_idx = 3;
    optional uint32 version = 4;//版本大小 1
}
message HotWordSearchSetGroupsResp {
    // nothing
}

message HotWordSearchDelGroupsReq {
    required uint32 group_idx = 1;             // 热词组idx
    optional uint32 version = 2;//版本大小 1
}
message HotWordSearchDelGroupsResp {
    // nothing
}

message HotWordSearchAddBlackListsOpReq {
    required string opt_name = 1;              // 登陆管理员用户名
    required bool addOp = 2;                   // true：增加 false：删除
    repeated string word_list = 3;             // 内容列表   
    required uint32 time_stamp = 4;            // 登陆时间
}
message HotWordSearchAddBlackListsOpResp {
    // nothing           
}

message HotWordSearchGetBlackListsOpReq {
    required uint32 start_idx = 1;             // 分页起始idx，从0开始
    required uint32 required_size = 2;         // 分页大小
}
message HotWordSearchGetBlackListsOpResp {
    repeated BlackListsOp record_list = 1;     // 操作记录
    required uint32 total = 2;                 // 操作记录总数
}

message HotWordSearchGetSearchRankReq {
    required uint32 begin_point_second = 1;    // 起始时间 UTC时间 自1970/1/1起的秒数
    required uint32 end_point_second = 2;      // 终止时间 UTC时间 自1970/1/1起的秒数
    required uint32 start_idx = 3;             // 分页起始idx，从0开始
    required uint32 required_size = 4;         // 分页大小
}
message HotWordSearchGetSearchRankResp {
    repeated RankAndScore word_list = 1;       // 搜索关键词排名列表，从大到小排序
    required uint32 total = 2;                 // Rank记录总数
}

message HotWordSearchGetPressRankReq {
    required uint32 begin_point_second = 1;    // 起始时间 UTC时间 自1970/1/1起的秒数
    required uint32 end_point_second = 2;      // 终止时间 UTC时间 自1970/1/1起的秒数
    required uint32 start_idx = 3;             // 分页起始idx，从0开始
    required uint32 required_size = 4;         // 分页大小
}
message HotWordSearchGetPressRankResp {
    repeated RankAndScore word_list = 1;       // 发布房间名排名列表，从大到小排序
    required uint32 total = 2;                 // Rank记录总数
}

// ======================================= client ==============================================

message HotWordSearchFetchReq {
    required uint32 start_grp_idx = 1;         // 分页起始idx，从0开始
    required uint32 required_size = 2;         // 分页大小
    optional uint32 version = 3;//版本大小 1
}
message HotWordSearchFetchResp {
    required uint32 next_grp_idx = 1;          // 下次请求拉取的热词组的提示idx
    repeated HotWordGroup group_list = 2;      // 本次请求的热词组列表
}

// ======================================= logic ==============================================

message HotWordSearchAddRecordReq {
    repeated string word_list = 1;             // 搜索内容
    required uint32 time_stamp = 2;            // 搜索时间戳
    required SourceType type = 3;              // 数据来源类型
    optional uint32 version = 4;//版本大小 1
}
message HotWordSearchAddRecordResp {
    optional string debug = 1;                 // 该记录写入的key
}

// ======================================= test ==============================================

message HotWordSearchHandleKeyReq {
    required TestType type = 1;                // 测试操作类型 1->打印 2->删除 3->删除指定
    optional string key = 2;                   // op3 删除的key
}
message HotWordSearchHandleKeyResp {
    repeated string key_list = 1;              // 所有存在的key
}

// ======================================= abandoned ==============================================

// 已停用 调用数美HTTP API实现
message HotWordSearchGetBlackListsReq {
    // nothing
}
message HotWordSearchGetBlackListsResp {
    repeated string word_list = 1;             // 敏感词
}
// 已停用 调用数美HTTP API实现
message HotWordSearchAddBlackListsReq {
    repeated string word_list = 1;             // 敏感词
    required string opt_name = 2;              // 操作人
}
message HotWordSearchAddBlackListsResp {
    // nothing
}
// 已停用 调用数美HTTP API实现
message HotWordSearchDelBlackListsReq {
    repeated string word_list = 1;             // 敏感词
    required string opt_name = 2;              // 操作人
}
message HotWordSearchDelBlackListsResp {
    // nothing
}
// 已停用 调用数美HTTP API实现
message HotWordSearchCheckBListsReq {
    repeated string word_list = 1;             // 待过滤内容列表
}
message HotWordSearchCheckBListsResp {
    repeated string word_list = 1;             // 过滤后内容列表
}

// ===============================================================================================


service HotWordSearch {

  // ======================================= frontend ==============================================

  rpc GetHotWordGroups(HotWordSearchGetGroupsReq)returns(HotWordSearchGetGroupsResp) {}

  rpc SetHotWordGroup(HotWordSearchSetGroupsReq)returns(HotWordSearchSetGroupsResp) {}

  rpc DelHotWordGroup(HotWordSearchDelGroupsReq)returns(HotWordSearchDelGroupsResp) {}

  rpc GetBlackListsOp(HotWordSearchGetBlackListsOpReq)returns(HotWordSearchGetBlackListsOpResp) {}

  rpc AddBlackListsOp(HotWordSearchAddBlackListsOpReq)returns(HotWordSearchAddBlackListsOpResp) {}

  rpc GetSearchRecordRank(HotWordSearchGetSearchRankReq)returns(HotWordSearchGetSearchRankResp) {}

  rpc GetPressRecordRank(HotWordSearchGetPressRankReq)returns(HotWordSearchGetPressRankResp) {}

  // ======================================= client ==============================================

  rpc FetchHotWordGroups(HotWordSearchFetchReq)returns(HotWordSearchFetchResp) {}

  // ======================================= logic ==============================================

  rpc AddRecord(HotWordSearchAddRecordReq)returns(HotWordSearchAddRecordResp) {}

  // ======================================= test ==============================================

  rpc HandleKey(HotWordSearchHandleKeyReq)returns(HotWordSearchHandleKeyResp) {}

  // ======================================= abandoned ==============================================

  // 已停用 调用数美HTTP API实现
  rpc GetHotWordBlackLists(HotWordSearchGetBlackListsReq)returns(HotWordSearchGetBlackListsResp) {}

  // 已停用 调用数美HTTP API实现
  rpc AddHotWordBlackLists(HotWordSearchAddBlackListsReq)returns(HotWordSearchAddBlackListsResp) {}

  // 已停用 调用数美HTTP API实现
  rpc DelHotWordBlackLists(HotWordSearchDelBlackListsReq)returns(HotWordSearchDelBlackListsResp) {}
  
  // 已停用 调用数美HTTP API实现
  rpc CheckWithBlackLists(HotWordSearchCheckBListsReq)returns(HotWordSearchCheckBListsResp) {}

}