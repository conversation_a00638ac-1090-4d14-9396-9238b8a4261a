syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package Preorder;

message PreorderInfo
{
	required uint32 game_id = 1;
	required string img_url = 2;
	required string url = 3;
	required uint64 start_time = 4;
	required uint64 end_time = 5;
	required uint32 rank = 6;
	optional uint32 sdk_game_id = 7;
}

message CreatePreorderReq
{
	required PreorderInfo info = 1;	
}

message DelPreorderReq
{
	required uint32 game_id = 1;
}

message ModifyPreorderReq
{
	required PreorderInfo info = 1;
}

message GetPreorderReq
{
	required uint32 game_id = 1;
}

message GetPreorderResp
{
	required PreorderInfo info = 1;
}

message GetPreorderListResp
{
	repeated PreorderInfo preorder_list = 1;
}

//有效时间范围内的预约活动
message ValidPreorderInfo
{
	required uint32 game_id = 1;
	required string img_url = 2;
	required string url = 3;
	required uint32 count = 4;	//预约人数
}

message GetPreorderValidListResp
{
	repeated ValidPreorderInfo preorder_list = 1;
}

message UserPreorderReq{
	required uint32 uid = 1;
	required uint32 game_id = 2;
	required string device_id = 3;
	optional uint32 guild_id = 4;
}

message UserPreorderInfo
{
	required uint32 game_id = 1;
	required uint64 start_time = 2;
	required uint64 end_time = 3;
	required uint32 public_id = 4;	//游戏id对应的公众号id
	required uint32 is_install = 5;	//是否安装游戏
}

message GetUserPreorderReq{
	required uint32 uid = 1;
}

message GetUserPreorderResp{
	repeated UserPreorderInfo preorder_list = 1;
}

message GetPreorderCountReq{
	required uint32 game_id = 1;
}

message GetPreorderCountResp{
	required uint32 count = 1;
}

message PreorderAwardInfo
{
	required uint32 game_id = 1;
	required string name = 2;
	required string icon = 3;
	required uint32 count = 4;
	required uint32 award_id = 5;
}

message AddPreorderAwardReq
{
	required PreorderAwardInfo award = 1;
}

message ModifyPreorderAwardReq
{
	required PreorderAwardInfo award = 1;
}

message DelPreorderAwardReq
{
	required uint32 game_id = 1;
	required uint32 award_id = 2;
}

message GetPreorderAwardReq
{
	required uint32 game_id = 1;
}

message GetPreorderAwardResp
{
	repeated PreorderAwardInfo award_list = 1;	
}

message GetPreorderMembersReq{
	required uint32 game_id = 1;
}

message GetPreorderMembersResp{
	repeated uint32 uid_list = 1;
}

message UpdateUserInstallStatusReq{
	required uint32 uid = 1;
	required uint32 game_id = 2;
	required uint32 is_install = 3;
}

message GameTabAdInfo{
    required uint32 act_id = 1;
    required uint32 game_id = 2;
    required string act_url = 3;
    required string game_tab_img = 4;       //游戏标签页浮层图片
    required string guild_mgroup_img = 5;   //公会总群浮层图片
    required uint64 begin_time      = 6;    // 活动开始时间(UNIX TIMESTAMP)
    required uint64 end_time        = 7;    // 活动结束时间
    required bool is_valid          = 8;    // 是否生效
}

message GetGameTabAdListResp{
	repeated GameTabAdInfo ad_list = 1;
}

message GetGameTabAdReq{
	required uint32 act_id = 1;
}

message DelGameTabAdReq
{
	required uint32 act_id = 1;
}

message GetGameTabAdByGameIdReq
{
	required uint32 game_id = 1;
}

message GetGuildPreorderInfoReq
{
	required uint32 act_id = 1;
	required uint32 guild_id = 2;
	optional uint32 game_id = 3;	//不知道act_id时基于game_id查询
}

message GetGuildPreorderInfoResp
{
	required uint32 count = 1;
}

service Preorder {
	option( tlvpickle.Magic ) = 15346;		// 服务监听端口号
	
	rpc CreatePreorder( CreatePreorderReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "g:u:p:s:e:r:a:";
        option( tlvpickle.Usage ) = "-g<game_id> -u<url> -p<image_url> -s<start_time> -e<end_time> -r<rank> -a<sdk_game_id>";
	}

	rpc DelPreorder( DelPreorderReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g<game_id>";
	}

	rpc ModifyPreorder( ModifyPreorderReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "g:u:p:s:e:r:a:";
        option( tlvpickle.Usage ) = "-g<game_id> -u<url> -p<image_url> -s<start_time> -e<end_time> -r<rank> -a<sdk_game_id>";
	}

	rpc GetPreorder( GetPreorderReq ) returns( GetPreorderResp ){
		option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g<game_id>";
	}

	rpc GetPreorderList( tlvpickle.SKBuiltinEmpty_PB ) returns( GetPreorderListResp ){
		option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
	}

	rpc UserPreorder( UserPreorderReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "u:g:";
        option( tlvpickle.Usage ) = "-u<uid> -g<game_id>";
	}

	rpc GetUserPreorder( GetUserPreorderReq ) returns( GetUserPreorderResp ){
		option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u<uid>";
	}

	rpc GetPreorderCount( GetPreorderCountReq ) returns( GetPreorderCountResp ){
		option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g<game_id>";
	}

	rpc AddPreorderAward( AddPreorderAwardReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "g:n:i:o:";
        option( tlvpickle.Usage ) = "-g<game_id> -n<name> -i<img_url> -o<count>";
	}

	rpc DelPreorderAward( DelPreorderAwardReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "g:a:";
        option( tlvpickle.Usage ) = "-g<game_id> -a<award_id>";
	}

	rpc GetPreorderAward( GetPreorderAwardReq ) returns( GetPreorderAwardResp ){
		option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g<game_id>";
	}

	rpc ModifyPreorderAward( ModifyPreorderAwardReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "g:n:i:a:o:";
        option( tlvpickle.Usage ) = "-g<game_id> -a<award_id> -n<name> -i<img_url> -o<count>";
	}

	rpc GetPreorderValidList( tlvpickle.SKBuiltinEmpty_PB ) returns( GetPreorderValidListResp ){
		option( tlvpickle.CmdID ) = 13;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
	}

	rpc GetPreorderMembers ( GetPreorderMembersReq ) returns( GetPreorderMembersResp ){
		option( tlvpickle.CmdID ) = 14;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g<game_id>";
	}

	rpc UpdateUserInstallStatus( UpdateUserInstallStatusReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 15;
        option( tlvpickle.OptString ) = "g:u:";
        option( tlvpickle.Usage ) = "-g<game_id> -u<uid>";
	}

	rpc CreateGameTabAd( GameTabAdInfo ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 16;
        option( tlvpickle.OptString ) = "a:g:u:m:n:s:e:l:";
        option( tlvpickle.Usage ) = "-a<act_id> -g<game_id> -u<url> -m<game_tab_img> -n<guild_mgroup_img> -s<start_time> -e<end_time> -l<valid>";
	}

	rpc GetGameTabAdList( tlvpickle.SKBuiltinEmpty_PB ) returns( GetGameTabAdListResp ){
		option( tlvpickle.CmdID ) = 17;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
	}

	rpc GetGameTabAd( GetGameTabAdReq ) returns( GameTabAdInfo ){
		option( tlvpickle.CmdID ) = 18;
        option( tlvpickle.OptString ) = "a:";
        option( tlvpickle.Usage ) = "-a<act_id>";
	}

	rpc DelGameTabAd( DelGameTabAdReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 19;
        option( tlvpickle.OptString ) = "a:";
        option( tlvpickle.Usage ) = "-a<act_id>";
	}

	rpc GetGameTabAdByGameId( GetGameTabAdByGameIdReq ) returns( GameTabAdInfo ){
		option( tlvpickle.CmdID ) = 20;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g<game_id>";
	}	

	rpc GetGuildPreorderInfo( GetGuildPreorderInfoReq ) returns( GetGuildPreorderInfoResp ){
		option( tlvpickle.CmdID ) = 21;
        option( tlvpickle.OptString ) = "a:g:";
        option( tlvpickle.Usage ) = "-a<act_id> -g<guild_id>";
	}	
}