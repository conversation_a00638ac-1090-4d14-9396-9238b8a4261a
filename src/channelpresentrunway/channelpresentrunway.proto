syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package channelpresentrunway;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

message RunwayConfig
{
	uint32 level = 1;
	uint32 begin_value = 2;
	uint32 end_value = 3;
}

message RunwayEffect
{
	uint32 runway_present_base = 1;	// 单次每送出指定值的礼物，可额外增加跑道时间
	uint32 runway_add_seconds = 2;	// 跑道在礼物的播放时间基础上额外再增加的时间（秒）
	uint32 max_runway_add_seconds = 3; // 跑道最多可增加的时间（秒）
}

message UserRunwayBrief
{
	uint32 uid = 1;
	uint32 cur_value = 2;
	uint32 runway_level = 3;
	uint32 expired_time = 4;
	uint32 start_time = 5;
	uint32 cont_launch_times = 6;	// 连续火箭冲天次数
}

message UserRunwayInfo
{
	UserRunwayBrief brief_info = 1;
	RunwayConfig runway_cfg = 2;
}

// 获取房间的火箭跑道信息
message GetChannelRunwayListReq
{
	uint32 channel_id = 1;
}

message GetChannelRunwayListResp
{
	repeated UserRunwayInfo runway_list = 1;
	RunwayEffect runway_effect = 2;
}

// 获取用户在房间的火箭跑道信息
message GetUserRunwayInfoReq
{
	uint32 uid = 1;
	bool query_ready_info = 2;	// 未出现火箭跑道时，查询用户的当前累计送礼值
}

message GetUserRunwayInfoResp
{
	UserRunwayInfo runway_info = 1;
}

message TestChannelEventReq
{
	uint32 uid = 1;
	uint32 channel_id = 2;
	uint32 type = 3;
}

message TestPresentEventReq
{
	uint32 uid = 1;
	uint32 channel_id = 2;
	uint32 total_price = 3;
	uint32 item_id = 4;
	uint32 item_count = 5;
}

message UserPresentRecord
{
	uint32 uid = 1;
	uint32 present_value = 2;
	uint64 micro_ts = 3;
}

service ChannelPresentRunway {
	option( tlvpickle.Magic ) = 15644;		// 服务监听端口号
	
    rpc GetChannelRunwayList ( GetChannelRunwayListReq ) returns( GetChannelRunwayListResp ) {
        option( tlvpickle.CmdID ) = 1;             
        option( tlvpickle.OptString ) = "x:";  
        option( tlvpickle.Usage ) = "-x <channel_id> ";    
    }	
	rpc GetUserRunwayInfo ( GetUserRunwayInfoReq ) returns( GetUserRunwayInfoResp ) {
        option( tlvpickle.CmdID ) = 2;             
        option( tlvpickle.OptString ) = "u:t:";  
        option( tlvpickle.Usage ) = "-u <uid> -t <query_ready_info>";    
    }	
	
    rpc TestChannelEvent ( TestChannelEventReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 3;           
        option( tlvpickle.OptString ) = "u:x:t:";  
        option( tlvpickle.Usage ) = "-u <uid> -x <channel_id> -t <type>";    
    }	
    rpc TestPresentEvent ( TestPresentEventReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 4;           
        option( tlvpickle.OptString ) = "u:x:n:a:t:";  
        option( tlvpickle.Usage ) = "-u <uid> -x <channel_id> -t <total_price> -a <item_id> -n <item_count>";    
    }	

}
