syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package golddiamondsvr;

enum IncomeType {
	GAME_RECHARGE_REBATE = 1; 			// 通过游戏充值返利 获得金钻
	GUILD_SYS_TASK = 2;       			// 通过完成公会系统任务 获得金钻
	GUILD_OPE_TASK = 3;       			// 通过完成公会运营任务 获得金钻
	SETTLEMENT = 4;           			// 通过 系统结算 一般是扣除金钻
	GUILD_WELFARE_FIRST_JOIN = 5;    	// 通过参加会长福利活动——新增有效成员 获得金钻
	CHANNEL_SEND_GIFT = 6;				// 开黑房间送礼物 获得金钻
	CHANNEL_SETTLEMENT = 7;				// 房间收益金钻结算
}


message AwardDiamondReq {
	required uint32 guild_id = 1;
	required uint32 paid_uid = 2;
	required uint64 bought_time = 3;
	required uint32 source_type = 4;
	required int64 income = 5;
	required string order_id = 6;
	required string uniq_sign = 7;
	required string desc = 8;
	required string extand = 9;
	
}


message GetIncomeReq {
	repeated uint32 guild_id_list = 1;
}

message IncomeAmount {
	required uint32 guild_id = 1;
	required int64 day_income = 2;
	required int64 month_income = 3;
}

message GetIncomeResp {
	repeated IncomeAmount income_list = 1;
}

message GetIncomeSingleReq {
	required uint32 guild_id = 1;
	required uint32 star_level = 2;
	optional uint32 source_type = 3;
}

message GetIncomeSingleResp {
	required int64 day_income = 1;         // 今日收入
	required int64 month_income = 2;       // 本月没有结算的金钻收入
	required double income_estimate = 3;   // 本月预估收入
	optional float coefficient = 4;        // 系数
	optional int64 last_month_income = 5;            // 上月没有结算的金钻收入
	optional double last_month_estimate_income = 6;  // 上月没有结算的金钻预估收入
}

message IncomeDetail {
	required uint32 paid_uid = 1;
	required uint64 bought_time = 2;
	required uint32 source_type = 3;
	required int64 income = 4;
	required string desc = 5;
	required string order_id = 6;
	required string extand = 7;
	required int64 income_balance = 8;
}

message GetIncomeDetailByTimeReq {
	required uint32 guild_id = 1;
	required uint32 start_time = 2;
	required uint32 end_time = 3;
}

message GetIncomeDetailReq {
	required uint32 guild_id = 1;
	required uint32 index = 2;
	required uint32 count = 3;
}

message GetIncomeDetailResp {
	repeated IncomeDetail income_detail_list = 1;
	required uint32 total = 2;
}


message SettlementBalanceReq {
	required uint32 guild_id = 1;
	required uint32 settlement_time = 2;
	required uint32 star_level = 3;
}

message SettlementBalanceResp
{
	required GetIncomeDetailResp detail = 1;
	required uint64 amount = 2;
	required double rmb_income = 3;
}


message ReportDetail {
	required uint32 paid_uid = 1;
	required uint32 bought_time = 2;
	required uint32 guild_id = 3;
	required int64  income = 4;
	required string extand = 5;
}


message ReportDetailReq{
	required uint32 start_time = 1;
	required uint32 end_time = 2;	
}

message ReportDetailResp{
	repeated ReportDetail detail_info = 1;
}


message GetCommissionInfoReq {
	required uint32 guild_id = 1;
	required uint32 start_time = 2;
	required uint32 end_time = 3;
	required bool just_unsettlement = 4;
	optional uint32 limit_start = 5;
	optional uint32 limit_end = 6;
	optional uint32 game_id = 7;
	optional uint32 uid = 8;
	optional uint32 source_type = 9;
}


message GetRoomCommissionInfoReq {
	required uint32 guild_id = 1;
	required uint32 start_time = 2;
	required uint32 end_time = 3;
	required bool just_unsettlement = 4;
	optional uint32 limit_start = 5;
	optional uint32 limit_end = 6;
	optional uint32 room_id = 7;
	optional uint32 uid = 8;
	optional uint32 source_type = 9;
}

message GetCommissionInfoResp {
	required uint32 income = 1;
	required uint32 payer_count = 2;
	required uint32 cash_amount = 3;
}


message GetCommissionDetailReq {
	required uint32 guild_id = 1;
	required uint32 start_time = 2;
	required uint32 end_time = 3;
	optional uint32 uid	= 4;
	optional uint32 game_id	= 5;
	required uint32 limit_start = 6;
	required uint32 limit_end = 7;
}

message CommissionInfo{
	required uint32 uid = 1;
	required uint32 fee = 2;
	required uint32 bought_time = 3;
	optional uint32 game_id	= 4;
	required uint32 id = 5;
}

message GetCommissionDetailResp {
	repeated CommissionInfo commission_list = 1;
	required uint32 total = 2;
}


message GetRoomCommissionDetailReq {
	required uint32 guild_id = 1;
	required uint32 start_time = 2;
	required uint32 end_time = 3;
	optional uint32 uid	= 4;
	optional uint32 room_id	= 5;
	required uint32 limit_start = 6;
	required uint32 limit_end = 7;
}

message RoomCommissionInfo{
	required uint32 uid = 1;
	required uint32 fee = 2;
	required uint32 bought_time = 3;
	required uint32 id = 4;
	optional uint32 room_id	= 5;
}

message GetRoomCommissionDetailResp {
	repeated RoomCommissionInfo commission_list = 1;
	required uint32 total = 2;
}


message MemberDetailInfo{
	required uint32 uid = 1;
	required uint32 fee = 2;
	required uint32 income = 3;
	required uint32 id = 4;
}

message GetMemberDetailResp{
	repeated MemberDetailInfo member_detail_list = 1;
	required uint32 total = 2;
}

message GameDetailInfo{
	optional uint32 game_id	= 1;
	required uint32 fee = 2;
	required uint32 income = 3;
	required uint32 id = 4;
}


message GetGameDetailResp{
	repeated GameDetailInfo game_detail_list = 1;
	required uint32 total = 2;
}

message GetRechargeGameReq {
	required uint32 guild_id = 1;
	required uint32 limit_start = 2;
	required uint32 limit_end = 3;
}

message GetRechargeGameResp {
	repeated uint32 game_id_list = 1;
	required uint32 total = 2;
}

message GetConsumeRoomReq {
	required uint32 guild_id = 1;
	required uint32 limit_start = 2;
	required uint32 limit_end = 3;
}

message GetConsumeRoomResp {
	repeated uint32 room_id_list = 1;
	required uint32 total = 2;
}


message RoomDetailInfo{
	optional uint32 room_id	= 1;
	required uint32 fee = 2;
	required uint32 income = 3;
	required uint32 id = 4;
}


message GetRoomDetailResp{
	repeated RoomDetailInfo room_detail_list = 1;
	required uint32 total = 2;
}


service GoldDiamondSvr{
    option( tlvpickle.Magic ) = 15310;

    rpc AwardDiamond ( AwardDiamondReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 1;										// 命令号
	    option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id>";	// 测试工具的命令号帮助	
    }

    rpc GetIncome ( GetIncomeReq ) returns ( GetIncomeResp ) {
        option( tlvpickle.CmdID ) = 2;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
    }

	rpc GetIncomeDetail ( GetIncomeDetailReq ) returns ( GetIncomeDetailResp ) {
        option( tlvpickle.CmdID ) = 3;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
    }

	rpc GetIncomeDetailByTime ( GetIncomeDetailByTimeReq ) returns ( GetIncomeDetailResp ) {
        option( tlvpickle.CmdID ) = 4;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
    }

	rpc SettlementBalance ( SettlementBalanceReq ) returns ( SettlementBalanceResp ) {
        option( tlvpickle.CmdID ) = 5;										// 命令号
	    option( tlvpickle.OptString ) = "g:t:l";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id -t <time> -l <star_level>>";	// 测试工具的命令号帮助	
    }

	rpc GetIncomeSingle ( GetIncomeSingleReq ) returns ( GetIncomeSingleResp ) {
        option( tlvpickle.CmdID ) = 6;										// 命令号
	    option( tlvpickle.OptString ) = "g:s";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <star_level>";	// 测试工具的命令号帮助	
    }

    rpc UpdateDiamondDesc ( AwardDiamondReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 7;			// 命令号
	    option( tlvpickle.OptString ) = "";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
    }
	
	rpc ReportDetail ( ReportDetailReq ) returns ( ReportDetailResp ) {
        option( tlvpickle.CmdID ) = 8;			// 命令号
	    option( tlvpickle.OptString ) = "";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
    }


	rpc GetCommissionInfo ( GetCommissionInfoReq ) returns ( GetCommissionInfoResp ) {
		option( tlvpickle.CmdID ) = 30;										// 命令号
		option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
	}


	rpc GetCommissionDetail ( GetCommissionDetailReq ) returns ( GetCommissionDetailResp ) {
		option( tlvpickle.CmdID ) = 31;										// 命令号
		option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
	}

	rpc GetMemberDetail ( GetCommissionInfoReq ) returns ( GetMemberDetailResp ) {
		option( tlvpickle.CmdID ) = 32;										// 命令号
		option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
	}

	rpc GetGameDetail ( GetCommissionInfoReq ) returns ( GetGameDetailResp ) {
		option( tlvpickle.CmdID ) = 33;										// 命令号
		option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
	}

	rpc GetRechargeGame ( GetRechargeGameReq ) returns ( GetRechargeGameResp ) {
		option( tlvpickle.CmdID ) = 34;										// 命令号
		option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
	}

	rpc GetRoomCommissionInfo ( GetRoomCommissionInfoReq ) returns ( GetCommissionInfoResp ) {
		option( tlvpickle.CmdID ) = 35;										// 命令号
		option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
	}


	rpc GetRoomCommissionDetail ( GetRoomCommissionDetailReq ) returns ( GetRoomCommissionDetailResp ) {
		option( tlvpickle.CmdID ) = 36;										// 命令号
		option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
	}

	rpc GetConsumeRoom ( GetConsumeRoomReq ) returns ( GetConsumeRoomResp ) {
		option( tlvpickle.CmdID ) = 37;										// 命令号
		option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
	}


	rpc GetRoomMemberDetail ( GetRoomCommissionInfoReq ) returns ( GetMemberDetailResp ) {
		option( tlvpickle.CmdID ) = 38;										// 命令号
		option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
	}


	rpc GetRoomDetail ( GetRoomCommissionInfoReq ) returns ( GetRoomDetailResp ) {
		option( tlvpickle.CmdID ) = 39;										// 命令号
		option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
	}
}
