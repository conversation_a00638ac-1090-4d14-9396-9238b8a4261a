syntax="proto2";


package group_online.async;

message AsyncCommands {
    enum Values {
        REBUILD_USER_GROUPRELATION = 1;  // 用户的群关系列表
        REBUILD_GROUP_MEMBERLIST = 2;    // 群的成员列表
        REPAIR_USER_GROUP = 3;           // 修复
        REPAIR_BAT_GROUP_MEMBER = 4;         // 群成员批量修复
    }
}

message BuildUserGroupRelationTaskData {
    required uint32 uid = 1;                // 用户ID
}

message BuildGroupMemberListTaskData {

    required uint32 group_id = 1;          //
    optional uint32 uid = 2;              // 
}

message RepairUserGroupTaskData {

    required uint32 group_id = 1;          //
    required uint32 uid = 2;              // 
}

message CheckRepairMemberTaskData {

    required uint32 group_id = 1;          //
    repeated uint32 uid_list = 2;              // 
}