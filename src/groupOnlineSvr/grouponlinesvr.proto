syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package grouponlinesvr;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

// 
enum GROUP_OL_STATUS 
{
	GROUP_OL_OFFLINE = 0;				// 离线
	GROUP_OL_ONLINE  = 1;				// 在线
}

enum GROUP_MEMBER_EVENT
{
	GROUP_MEMBER_EVENT_JOIN = 1;				// 加入群
	GROUP_MEMBER_EVENT_QUIT  = 2;				// 退出群
	GROUP_MEMBER_EVENT_DISMISS  = 3;		    // 解散群
}

// 群在线
message stGroupOL
{
	required uint32 group_id = 1;
	required uint32 online_cnt = 2;
	required uint32 offline_cnt = 3;
	optional uint32 group_type = 4; // 该值不一定有 取值可以参考 guilddef.h GroupType 0=公会主群 1=游戏主群 2=游戏群 3=临时群 4=兴趣群
}

message stGroupMemberOL
{
	required uint32 uid = 1;
	required uint32 state_update_ts = 2;
	required uint32 state = 3;          // see GROUP_OL_STATUS
}

// 更新用户在线状态
message UpdateUserOnlineStateReq
{
	required uint32 uid = 1;
	required uint32 state = 2; // see GROUP_OL_STATUS
}
message UpdateUserOnlineStateResp
{
}

// 通知群成员变化事件信息
message NotifyGroupMemberEventReq
{
	required uint32 uid = 1;
	required uint32 group_id = 2;
	required uint32 event = 3; // see GROUP_MEMBER_EVENT
}
message NotifyGroupMemberEventResp
{
}

// 获取用户的群列表在线数量信息
message GetUserGroupListOverviewReq
{
	required uint32 uid = 1;
}
message GetUserGroupListOverviewResp
{
	repeated stGroupOL group_ol_list = 1;
}

// 获取指定群的在线数量信息
message GetGroupOnlineOverviewReq
{
	required uint32 group_id = 1;
	required uint32 uid = 2;
}
message GetGroupOnlineOverviewResp
{
	required stGroupOL group_ol = 1;
}

// 获取指定群的成员列表在线信息
message GetGroupMemberListOnlineInfoReq
{
	required uint32 group_id = 1;
	required uint32 uid = 2;
	required uint32 begin_idx = 3;
	required uint32 count = 4;
}
message GetGroupMemberListOnlineInfoResp
{
	repeated stGroupMemberOL member_ol_list = 1;
}


// 批量获取指定群的指定成员的在线信息
message BatGetGroupMemberOnlineInfoReq
{
	required uint32 group_id = 1;
	required uint32 req_uid = 2;
	repeated uint32 target_uid_list = 3;
}
message BatGetGroupMemberOnlineInfoResp
{
	repeated stGroupMemberOL member_ol_list = 1;
}


// 检查和修正
message CheckAndRepairReq
{
	required uint32 group_id = 1;       // 出错的目标群ID
	repeated uint32 error_uid_list = 2; // 出错的用户列表
}
message CheckAndRepairResp
{
}


//////////////////
service grouponlinesvr { 
    option( tlvpickle.Magic ) = 15410;		// 服务监听端口号
	
	// 更新用户在线状态, deprecated
    rpc UpdateUserOnlineState(UpdateUserOnlineStateReq) returns(UpdateUserOnlineStateResp) {
        option( tlvpickle.CmdID ) = 1;												
        option( tlvpickle.OptString ) = "u:s:";									
        option( tlvpickle.Usage ) = "-u <uid> -s <online state 0=offline 1=online> ";			
    }
	
	// 通知群成员变化事件信息, deprecated
    rpc NotifyGroupMemberEvent(NotifyGroupMemberEventReq) returns(NotifyGroupMemberEventResp) {
        option( tlvpickle.CmdID ) = 2;										
        option( tlvpickle.OptString ) = "u:g:e:";								
        option( tlvpickle.Usage ) = "-u <uid> -g <group id> -e <event type>";
    }
	
	// 获取指定用户的群列表在线数量信息
    rpc GetUserGroupListOverview(GetUserGroupListOverviewReq) returns(GetUserGroupListOverviewResp) {
        option( tlvpickle.CmdID ) = 3;										
        option( tlvpickle.OptString ) = "u:";									
        option( tlvpickle.Usage ) = "-u <uid> ";	
    }
	
	// 获取指定群的在线数量信息
    rpc GetGroupOnlineOverview(GetGroupOnlineOverviewReq) returns(GetGroupOnlineOverviewResp) {
        option( tlvpickle.CmdID ) = 4;										
        option( tlvpickle.OptString ) = "u:g:";									
        option( tlvpickle.Usage ) = "-u <uid> -g <group id> ";			
    }
	
	// 获取指定群的成员列表在线信息
    rpc GetGroupMemberListOnlineInfo(GetGroupMemberListOnlineInfoReq) returns(GetGroupMemberListOnlineInfoResp) {
        option( tlvpickle.CmdID ) = 5;										
        option( tlvpickle.OptString ) = "u:g:s:x:";								
        option( tlvpickle.Usage ) = "-u <uid> -g <group id> -s <start idx> -x <count limit>";		
    }
	
	// 获取指定群的指定成员的在线信息
    rpc BatGetGroupMemberOnlineInfo(BatGetGroupMemberOnlineInfoReq) returns(BatGetGroupMemberOnlineInfoResp) {
        option( tlvpickle.CmdID ) = 6;										
        option( tlvpickle.OptString ) = "u:g:t:";								
        option( tlvpickle.Usage ) = "-u <uid> -g <group id> -t <target uid>";		
    }

	// 检查和修正
    rpc CheckAndRepair(CheckAndRepairReq) returns(CheckAndRepairResp) {
        option( tlvpickle.CmdID ) = 7;										
        option( tlvpickle.OptString ) = "u:g:t:";								
        option( tlvpickle.Usage ) = "-u <uid> -g <group id> -t <error uid>";		
    }
}
