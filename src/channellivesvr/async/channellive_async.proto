syntax="proto2";

package channellive.async;



// 直播连麦申请超时事件
message ConnectMicApplyExpireAsyncJobNotify
{
	required uint32 channel_id =1;
	required uint32 remain_apply_cnt =2;
	repeated uint32 expire_uid_list =3;
	optional uint64 server_time_ms  = 4;          // 64bit 毫秒级 服务器时间
}

// 直播结束事件
message LiveFinishedAsyncJobNotify
{
	required uint32 channel_id =1;
	required uint32 uid =2;
	optional uint64 server_time_ms  = 3;          // 64bit 毫秒级 服务器时间
}
