syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package channellive;

// 普通房间的排麦申请
message QueueUpMicApplyReq
{
	uint32 channel_id = 1;
	uint32 op_uid = 2;
	bool is_cancel = 3;
}

message QueueUpMicApplyResp
{
	uint64 server_time_ms  = 1;       // 64bit 毫秒级 服务器时间
	uint32 ramin_apply_cnt = 2;       // 当前剩余的申请连麦的人数
	uint32 success_uid = 3;
}

// 普通房间的排麦处理
message QueueUpMicHandleReq
{
	uint32 channel_id = 1;
	uint32 target_uid = 2;
	uint32 op_uid = 3;
	bool is_allow = 4;
}

message QueueUpMicHandleResp
{
	uint64 server_time_ms  = 1;       // 64bit 毫秒级 服务器时间
	uint32 ramin_apply_cnt = 2;       // 当前剩余的申请连麦的人数
	uint32 success_uid = 3;
}

// 普通房间的排麦列表获取
message GetQueueUpMicApplyListReq
{
	uint32 channel_id = 1;
	uint32 offset = 2;
	uint32 limit_cnt = 3;
}

message GetQueueUpMicApplyListResp
{
	repeated uint32 uid_list = 1;  
	uint32 all_apply_cnt = 2;
}


message CleanQueueUpMicApplyListReq
{
	uint32 channel_id = 1;
	uint32 op_uid = 2;
}

message CleanQueueUpMicApplyListResp
{
}

service ChannelLive {
	option( tlvpickle.Magic ) = 15221;		// 服务监听端口号

	// 普通房间的排麦功能
	rpc QueueUpMicApply( QueueUpMicApplyReq ) returns( QueueUpMicApplyResp ){
		option( tlvpickle.CmdID ) = 30;
        option( tlvpickle.OptString ) = "x:o:p:";							
        option( tlvpickle.Usage ) = "-x <channelID> -o <op_uid> -p <is cancel>";
	}
	
	rpc QueueUpMicHandle( QueueUpMicHandleReq ) returns( QueueUpMicHandleResp ){
		option( tlvpickle.CmdID ) = 31;
        option( tlvpickle.OptString ) = "x:o:t:p:";							
        option( tlvpickle.Usage ) = "-x <channelID> -o <op_uid> -t <target uid>  -p <is allow>";
	}
	
	rpc GetQueueUpMicApplyList( GetQueueUpMicApplyListReq ) returns( GetQueueUpMicApplyListResp ){
		option( tlvpickle.CmdID ) = 32;
        option( tlvpickle.OptString ) = "x:o:l:";							
        option( tlvpickle.Usage ) = "-x <channelID> -o <offset> -l <limit>";
	}

    rpc CleanQueueUpMicApplyList( CleanQueueUpMicApplyListReq ) returns( CleanQueueUpMicApplyListResp ){
		option( tlvpickle.CmdID ) = 33;
        option( tlvpickle.OptString ) = "x:";							
        option( tlvpickle.Usage ) = "-x <channelID>";
	}
}
