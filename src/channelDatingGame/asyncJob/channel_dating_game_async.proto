syntax="proto2";

package channel_dating_game.async;

// 帽子变化
message ChannelDatingGameAsyncJobHatNotify
{
	required uint32 channel_id = 1;
	required uint32 new_hat_uid = 2;
	required uint32 pre_hat_uid = 3;	// 之前获得帽子的uid
	optional bytes hat_cfg = 4;	// 帽子配置
	required uint32 op_uid = 5;
	optional bool is_male = 6;
}

// vip用户变化
message ChannelDatingGameAsyncJobVipNotify
{
	required uint32 channel_id = 1;
	required uint32 new_vip_uid = 2;
	required uint32 pre_vip_uid = 3;	// 之前的vip用户uid
	required uint32 op_uid = 4;
}

// 麦上用户心动值
message  ChannelDatingMicUserLikeBeatValNotify
{
	required uint32 channel_id = 1;
	required uint32 uid = 2;
	required uint32 like_beat_val = 3;
}

// 麦上用户选择状态
message ChannelDatingSelectStatusNotify
{
	required uint32 channel_id = 1;
	repeated SelectStatusInfo status_info_list = 2;
	required uint32 preside_uid = 3;
}
message SelectStatusInfo
{
	required uint32 uid = 1;
	required bool select_status = 2;				// 0 未选择 1 选择
}

// 心动对象
message ChannelDatingLikeBeatObjNotify
{
	required uint32 channel_id = 1;
	required uint32 open_uid = 2;
	required uint32 like_uid = 3;
	required bool add_friend = 4;
}

message ChannelDatingApplyMicNotify
{
	required uint32 channel_id = 1;
	required uint32 push_uid = 2;			// 主持麦用户uid，推送uid
	required uint32 apply_uid = 3;			// 申请用户uid
	required uint32 entrance_uid = 4;		// 最新申请用户uid
	required uint32 apply_user_count = 5;	// 申请人数
	required uint32 is_cancel = 6;			// 
}

