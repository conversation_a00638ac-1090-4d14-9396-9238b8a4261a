syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package channeldatinggame;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list
// 房间相亲

message HatUser
{
  optional uint32 uid = 1;
  optional DatingGameHatCfg hat_cfg = 2;
}

// 获取相亲房信息
// 用于进房的时候拉取初始信息（比如麦上用户的心动值 / 当前帽子用户 / 当前土豪位用户 / 当前阶段 ）
message GetDatingGameCurInfoReq
{
  required uint32 channel_id = 1;
}

message GetDatingGameCurInfoResp
{
  required uint32 phase = 1;      // 房间的当前阶段 DatingGamePhaseType
  required uint32 vip_uid = 2;	// vip麦的用户
  repeated HatUser hat_user_list = 3;	// 获得帽子的麦上用户
  repeated UserLikeBeatInfo like_beat_list = 4; // 麦上用户心动值和心动状态
  repeated OpenLikeUserInfo open_like_user_list = 5;	// 已经公布心动对象的用户
  optional uint32 apply_mic_len = 6; // 申请连麦人数
  // TODO。。。
}

// 开启相亲游戏入口
message OpenDatingGameEntryReq
{
  required uint32 channel_id = 1;
  optional uint32 level = 2;
}
message OpenDatingGameEntryResp
{

}
message CloseDatingGameEntryReq
{
  required uint32 channel_id = 1;
}
message CloseDatingGameEntryResp
{

}

message CheckDatingGameEntryReq
{
  required uint32 channel_id = 1;
}
message CheckDatingGameEntryResp
{
  required bool is_open = 1;
  optional uint32 level = 2;
}


// 设置当前阶段
message SetGamePhaseReq
{
  required uint32 channel_id = 1;
  required uint32 target_phase = 2;	// ga::DatingGamePhase
  optional uint32 op_uid = 3;

}
message SetGamePhaseResp
{
  required uint32 from_phase = 1;	// ga::DatingGamePhase
}


message GetGamePhaseReq
{
  required uint32 channel_id = 1;
}
message GetGamePhaseResp
{
  required uint32 curr_phase = 1;	// ga::DatingGamePhase
}

// 初始化相亲游戏的成员(用于初始化 游戏开始时 已经在麦上的)
message DatingMember
{
  required uint32 uid = 1;
  required uint32 mic_id = 2;
  required uint32 sex = 3;
}

message InitDatingMemberReq
{
  required uint32 channel_id = 1;
  repeated DatingMember member_list = 2;
}
message InitDatingMemberResp
{

}


// 获取心动值
message GetUserLikeBeatValsReq
{
  required uint32 channel_id = 1;
  repeated uint32 uid_list = 2;
}
message GetUserLikeBeatValsResp
{
  repeated UserLikeBeatInfo like_beat_info_list = 2;
}
message GetUserRankLikeBeatValsReq
{
  required uint32 channel_id = 1;
}

message GetUserRankLikeBeatValsResp
{
  repeated UserLikeBeatInfo like_beat_info_list = 2;
}

message UserLikeBeatInfo
{
  required uint32 uid = 1;
  required uint32 like_beat_val = 2;
  optional bool select_status = 3;		// 选择状态 0 未选（需根据阶段处理） 1 已选择
}

// 设置心动对象
message SetUserLikeBeatObjReq
{
  required uint32 channel_id = 1;
  required uint32 select_uid = 2;
  required uint32 like_uid = 3;
}
message SetUserLikeBeatObjResp
{

}

// 获取心动对象
message GetUserLikeBeatObjReq
{
  required uint32 channel_id = 1;
  required uint32 open_uid = 2;
}

message GetUserLikeBeatObjResp
{
  required uint32 like_uid = 1;
}

message GetSelectLikeBeatObjUserReq
{
  required uint32 channel_id = 1;
  required uint32 uid = 3;
}

message GetSelectLikeBeatObjUserResp
{
  repeated uint32 select_uid_list = 1;
}
// 相互心动信息
message MatchLikeBeatInfo
{
  required uint32 uid_a = 1;
  required uint32 uid_b = 2;
  required uint32 like_beat_val_a = 3;
  required uint32 like_beat_val_b = 4;
}

// 获取已公布的用户
message GetOpenLikeUserListReq
{
  required uint32 channel_id = 1;
  required uint32 uid = 2;
}
message GetOpenLikeUserListResp
{
  repeated OpenLikeUserInfo open_info_list = 1;
}
message OpenLikeUserInfo
{
  required uint32 open_uid = 1;
  required uint32 like_uid = 2;
}

// 排麦
message UserApplyMicReq
{
  required uint32 channel_id = 1;
  required uint32 uid = 2;
  optional bool is_cancel = 3;
}

message UserApplyMicResp
{

}

// 获取排麦用户列表
message GetApplyMicUserListReq
{
  required uint32 channel_id = 1;
  required uint32 uid = 2;
}

message GetApplyMicUserListResp
{
  repeated uint32 uid_list = 2;
}

// 帽子信息
message DatingGameHatCfg
{
  required uint32 hat_id = 1;
  required string url = 2;
  required string	md5 = 3;
  required uint32 tbean_limit = 4;
  required bool is_male = 5;
  required uint32 level = 6;
}

// 获取vip用户
message GetVipMicUserReq
{
  required uint32 channel_id = 1;
}

message GetVipMicUserResp
{
  required uint32 vip_uid = 1;
}

// vip用户上麦确认
message ConfirmVipHoldMicReq
{
  required uint32 uid = 1;
  required uint32 channel_id = 2;
}

message ConfirmVipHoldMicResp
{
  required uint32 pre_vip_uid = 1;
}

message TestDrawImageReq {
  required uint32 cid = 1;
  required uint32 uid_a = 2;
  required uint32 uid_b = 3;
  required uint32 v_a = 4;
  required uint32 v_b = 5;
}

service channelDatingGame {
  option( tlvpickle.Magic ) = 15638;		// 服务监听端口号


  rpc OpenDatingGameEntry ( OpenDatingGameEntryReq ) returns( OpenDatingGameEntryResp ) {
    option( tlvpickle.CmdID ) = 1;
    option( tlvpickle.OptString ) = "x:e:";
    option( tlvpickle.Usage ) = "-x <channel_id> -e <level>";
  }
  rpc CheckDatingGameEntry ( CheckDatingGameEntryReq ) returns( CheckDatingGameEntryResp ) {
    option( tlvpickle.CmdID ) = 2;
    option( tlvpickle.OptString ) = "x:";
    option( tlvpickle.Usage ) = "-x <channel_id> ";
  }

  rpc SetGamePhase ( SetGamePhaseReq ) returns( SetGamePhaseResp ) {
    option( tlvpickle.CmdID ) = 3;
    option( tlvpickle.OptString ) = "x:t:";
    option( tlvpickle.Usage ) = "-x <channel_id> -t <target phase>";
  }
  rpc GetGamePhase ( GetGamePhaseReq ) returns( GetGamePhaseResp ) {
    option( tlvpickle.CmdID ) = 4;
    option( tlvpickle.OptString ) = "x:";
    option( tlvpickle.Usage ) = "-x <channel_id> ";
  }

  rpc GetUserLikeBeatVals ( GetUserLikeBeatValsReq ) returns( GetUserLikeBeatValsResp ) {
    option( tlvpickle.CmdID ) = 5;
    option( tlvpickle.OptString ) = "x:u:";
    option( tlvpickle.Usage ) = "-x <channel_id> -u<uid_list>";
  }
  rpc GetUserRankLikeBeatVals ( GetUserRankLikeBeatValsReq ) returns( GetUserRankLikeBeatValsResp ) {
    option( tlvpickle.CmdID ) = 6;
    option( tlvpickle.OptString ) = "x:";
    option( tlvpickle.Usage ) = "-x <channel_id>";
  }
  rpc SetUserLikeBeatObj ( SetUserLikeBeatObjReq ) returns( SetUserLikeBeatObjResp ) {
    option( tlvpickle.CmdID ) = 7;
    option( tlvpickle.OptString ) = "x:a:b:";
    option( tlvpickle.Usage ) = "-x <channel_id> -a<uid_a> -b<uid_b>";
  }
  rpc GetUserLikeBeatObj ( GetUserLikeBeatObjReq ) returns( GetUserLikeBeatObjResp ) {
    option( tlvpickle.CmdID ) = 8;
    option( tlvpickle.OptString ) = "x:u:";
    option( tlvpickle.Usage ) = "-x <channel_id> -u<uid>";
  }
  rpc UserApplyMic ( UserApplyMicReq ) returns( UserApplyMicResp ) {
    option( tlvpickle.CmdID ) = 9;
    option( tlvpickle.OptString ) = "x:u:n:";
    option( tlvpickle.Usage ) = "-x <channel_id> -u<uid> -n<is_cancel>";
  }
  rpc GetApplyMicUserList ( GetApplyMicUserListReq ) returns( GetApplyMicUserListResp ) {
    option( tlvpickle.CmdID ) = 10;
    option( tlvpickle.OptString ) = "x:";
    option( tlvpickle.Usage ) = "-x <channel_id>";
  }

  rpc InitDatingMember ( InitDatingMemberReq ) returns( InitDatingMemberResp ) {
    option( tlvpickle.CmdID ) = 11;
    option( tlvpickle.OptString ) = "x:u:";
    option( tlvpickle.Usage ) = "-x <channel_id> -u <uid>";
  }


  rpc GetVipMicUser ( GetVipMicUserReq ) returns( GetVipMicUserResp ) {
    option( tlvpickle.CmdID ) = 12;
    option( tlvpickle.OptString ) = "x:";
    option( tlvpickle.Usage ) = "-x <channel_id>";
  }

  rpc GetSelectLikeBeatObjUser ( GetSelectLikeBeatObjUserReq ) returns( GetSelectLikeBeatObjUserResp ) {
    option( tlvpickle.CmdID ) = 13;
    option( tlvpickle.OptString ) = "x:";
    option( tlvpickle.Usage ) = "-x <channel_id>";
  }

  rpc ConfirmVipHoldMic ( ConfirmVipHoldMicReq ) returns( ConfirmVipHoldMicResp ) {
    option( tlvpickle.CmdID ) = 14;
    option( tlvpickle.OptString ) = "x:u:";
    option( tlvpickle.Usage ) = "-x <channel_id> -u<uid>";
  }

  rpc GetDatingGameCurInfo ( GetDatingGameCurInfoReq ) returns( GetDatingGameCurInfoResp ) {
    option( tlvpickle.CmdID ) = 15;
    option( tlvpickle.OptString ) = "x:";
    option( tlvpickle.Usage ) = "-x <channel_id>";
  }

  rpc GetOpenLikeUserList ( GetOpenLikeUserListReq ) returns( GetOpenLikeUserListResp ) {
    option( tlvpickle.CmdID ) = 16;
    option( tlvpickle.OptString ) = "x:";
    option( tlvpickle.Usage ) = "-x <channel_id>";
  }

  rpc CloseDatingGameEntry( CloseDatingGameEntryReq ) returns( CloseDatingGameEntryResp ) {
    option( tlvpickle.CmdID ) = 17;
    option( tlvpickle.OptString ) = "x:";
    option( tlvpickle.Usage ) = "-x <channel_id>";
  }

  rpc TestDrawImage ( TestDrawImageReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
    option( tlvpickle.CmdID ) = 101;
    option( tlvpickle.OptString ) = "i:a:b:m:n:";
    option( tlvpickle.Usage ) = "-i <channel_id> -a <uida> -b <uidb> -m <value a> -n <value b>";
  }
}
