syntax="proto2";

// svrkit support
import "common/tlvpickle/skbuiltintype.proto";

package investGuildStatistics;

message TargetGuildInfo {
    required uint32 guild_nomal_id          = 1;   // 公会长号
	required uint32 join_statistic_ts       = 2;   // 加入统计的时间
	required uint32 invest_begin_ts         = 3;  // 被投资开始的时间
}


// 增加统计的目标公会
message AddGuildReq {
    required uint32 guild_id           = 1;  
	required uint32 invest_begin_ts    = 2;  // 被投资开始的时间
	required string desc    = 3;  // 描述信息
}

message AddGuildResp {
    required TargetGuildInfo guild_info = 1;    
}

// 获取全部目标公会
message GetAllGuildListReq
{
}
message GetAllGuildListResp
{
	repeated TargetGuildInfo guild_list = 1;   
}


// 获取公会
message GetGuildReq
{
	required uint32 guild_id            = 1;   // 
}
message GetGuildResp
{
	required TargetGuildInfo guild_info = 1;   
}

message ImportUserInfo {
    required uint32 uid            = 1;   // 
	optional uint32 reg_ts         = 2;   // 注册时间
	optional uint32 join_guild_ts  = 3;   // 加入目标公会的时间
	optional uint32 target_guild_nomal_id = 4;   // 目标公会的长号
	optional uint32 last_alive_ts = 5;   // 最后活跃时间
}

// 增加一个用户给目标公会
// 即该用户是由该公会导来的
message AddUserToGuildReq
{
	required ImportUserInfo user_info = 1;
}
message AddUserToGuildResp
{
	 
}

// 检测用户归属
message CheckUserBelongGuildReq
{
	required uint32 uid            = 1;   // 
}
message CheckUserBelongGuildResp
{
	required ImportUserInfo user_info = 1;
}

// 记录用户消费
enum SonsumeStatType
{
	SonsumeStat_GAME = 1;
	SonsumeStat_HC = 2;
	SonsumeStat_GAME_MIGRATE = 3;  // 游戏历史数据迁移
	SonsumeStat_HC_MIGRATE = 4;    // 欢城历史数据迁移
}

message RecordUserSonsumeStatReq
{
	required uint32 uid            = 1;   // 
	required uint32 sonsume_type   = 2;   //  SonsumeStatType
	required string order_id   = 3;       // 
	required uint32 total   = 4;   //  总金额
	required uint32 cash    = 5;   //  总现金
	required uint32 voucher_real = 6; //  总代金券有效值
	
}
message RecordUserSonsumeStatResp
{
	
}

// 记录用户活跃
message RecordUserAliveStatReq
{
	required uint32 uid            = 1;   // 
	optional uint32 last_alive_ts  = 2;   // 最后活跃时间 如果该字段不为空 已该字段数据为准 否则为当前时间
}
message RecordUserAliveStatResp
{
	
}

service investGuildStatistics {
	option( tlvpickle.Magic ) = 15420;		// 服务监听端口号
	
	// 增加统计的目标公会
    rpc AddGuild( AddGuildReq ) returns ( AddGuildResp ) {
		option( tlvpickle.CmdID ) = 1;									
        option( tlvpickle.OptString ) = "u:g:s:d:";							
        option( tlvpickle.Usage ) = "-u <uid> -g <guildId> -s <ts> -d <desc>";	
    }
	
	// 获取全部目标公会
    rpc GetAllGuildList( GetAllGuildListReq ) returns ( GetAllGuildListResp ) {
		option( tlvpickle.CmdID ) = 2;									
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";	
    }
	
	// 检测公会归属
    rpc GetGuild( GetGuildReq ) returns ( GetGuildResp ) {
		option( tlvpickle.CmdID ) = 3 ;									
        option( tlvpickle.OptString ) = "u:g:";							
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> ";
    }
	
	// 永久增加一个用户给目标公会 表明用户是由该公会导来的
    rpc AddUserToGuild( AddUserToGuildReq ) returns ( AddUserToGuildResp ) {
		option( tlvpickle.CmdID ) = 4 ;									
        option( tlvpickle.OptString ) = "u:r:j:g:t:";							
        option( tlvpickle.Usage ) = "-u <uid> -r <reg ts> -j <join guild ts> -g <guild_id> -t <lastlogints>";
    }
	
	// 检测用户归属
    rpc CheckUserBelongGuild( CheckUserBelongGuildReq ) returns ( CheckUserBelongGuildResp ) {
		option( tlvpickle.CmdID ) = 5 ;									
        option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid> ";
    }
	
	// 记录用户消费
    rpc RecordUserSonsumeStat( RecordUserSonsumeStatReq ) returns ( RecordUserSonsumeStatResp ) {
		option( tlvpickle.CmdID ) = 6 ;									
        option( tlvpickle.OptString ) = "u:o:t:a:x:r:";							
        option( tlvpickle.Usage ) = "-u <uid> -o <orderid> -t <type> -a <total fee> -x <cash fee> -r <voucher real>";
    }
	
	// 记录用户活跃
    rpc RecordUserAliveStat( RecordUserAliveStatReq ) returns ( RecordUserAliveStatResp ) {
		option( tlvpickle.CmdID ) = 7 ;									
        option( tlvpickle.OptString ) = "u:t:";							
        option( tlvpickle.Usage ) = "-u <uid> -t <lastlogints>";
    }
}
