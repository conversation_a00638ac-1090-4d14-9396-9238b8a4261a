syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package channelmusic;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

// 添加歌曲
message MusicInfo
{
	required string client_key = 1;
	required string name = 2;
	required string author = 3;
	required uint32 uid = 4; // 上传者id
	optional uint32 volume = 5; // 服务器的音量
	optional int64 key = 6; // zset中的score
	optional uint32 is_local = 8; // 是否是客户端本地歌曲 1-是 0-否
	optional uint32 status = 9; // 播放状态
	optional uint32 music_type = 10; //音乐类型 1原唱、0伴奏
	optional uint32 start_time = 11; //开始播放时间(百灵上报)
}

message AddChannelMusicReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	repeated MusicInfo music_info_list = 3;
}

message AddChannelMusicResp
{
	repeated MusicInfo music_added_list = 1;
	optional uint32 max_music_count = 2;
	optional uint32 current_music_count = 3;
}

message RemoveChannelMusicReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	repeated int64 music_id_list = 3;
	optional bool remove_self = 4;// true:只能自己的 false:uid有权限删其他人的
}

message RemoveChannelMusicResp
{
	repeated int64 music_removed_list = 1;
}

message RemoveChannelMusicByUidReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
}

message RemoveChannelMusicByUidResp
{

}

//从房间播放列表中删除被举报成功的音乐
message RemoveChannelReportedMusicReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required string music_name = 3;
	required string author = 4;
}

message RemoveChannelReportedMusicResp
{
	required int64 music_id = 1;    //-1:删除失败 0:歌曲不在播放列表中 >0:删除成功，是被删除歌曲的music_id
}

message GetReportedMusicListReq
{
	required uint32 uid = 1;
}

message GetReportedMusicListResp
{
	repeated string reported_music_list = 1;
}


message GetChannelMusicListReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
}
message GetChannelMusicListResp
{
	repeated MusicInfo music_list = 1;
}

// 控制
message ChannelMusicCtrlReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required uint32 channel_ctrl = 3;
}
message ChannelMusicCtrlResp
{
	optional MusicInfo next_music = 1;
}


// 心跳

message ChannelMusicHeartBeatReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required int64 music_key = 3;
	required uint32 volume = 4;
	required uint32 percent = 5; // 播放进度,最大100

	optional uint32 client_event = 6;  //客户端发过来的事件信息
}
message ChannelMusicHeartBeatResp
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	optional int64 music_id = 3;
	optional uint32 volume = 4;
}

message SetChannelMusicPlayModeReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required uint32 play_mode = 3;
}
message SetChannelMusicPlayModeResp
{

}


message SetChannelMusicVolumeReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required uint32 volume = 3;
}
message SetChannelMusicVolumeResp
{
}

message SetChannelMusicCanShareReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required bool can_share = 3;
}
message SetChannelMusicCanShareResp
{
}

message SetChannelMusicFreeModeReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required bool free_mode = 3;
}
message SetChannelMusicFreeModeResp
{
}

// 设置下一首
message SetChannelMusicNextReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required int64 music_id = 3;
}

message SetChannelMusicNextResp
{
}

// 播放器状态
message GetChannelMusicStatusReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
}
message GetChannelMusicStatusResp
{
	required uint32 play_mode = 1;
	required uint32 volume = 2;
	required bool can_share = 3;
	required bool is_playing = 4;
	optional int64 current_playing = 5;
	optional bool is_free_mode = 6;
	optional string music_name = 7;
	optional string music_author = 8;
}

message SetChannelMusicCurrentReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required uint32 music_id = 3;
}
message SetChannelMusicCurrentResp
{

}

enum ChangeType
{
	MUSIC_LIST_CHANGED = 1;
	PLAY_MODE_CHANGED = 2;
	FREE_MODE_CHANGED = 3;
}
// 推送相关
message ChannelMusicListChangedNotify
{
	required uint32 channel_id = 1;
	required uint32 change_type = 2;  //ChangeType
	optional uint32 change_value = 3;
}
message ChannelMusicVolumeChangedNotify
{
	required uint32 channel_id = 1;
	required uint32 volume = 2;
}
message ChannelMusicPlayTheMusic
{
	required uint32 channel_id = 1;
	required uint32 uid = 2;
	required int64 music_id = 3;
	required uint32 volume = 4;
	required bool start = 5;
	required uint32 percent = 6;
	optional string music_name = 7;
	optional uint32 music_changed_type = 8;
	optional uint32 switcher_uid = 9;
}

message ChannelMusicCanShareNotify
{
	required uint32 channel_id = 1;
	required bool can_share = 2;
}

//获取所有正在播放歌曲的房间id
message GetPlayingMusicChannelListReq
{
	required uint32 uid = 1;
}
message GetPlayingMusicChannelListResp
{
	repeated uint32 channel_id_list = 1;
}

// 房间正在播放歌曲信息
message PlayingMusicInfo
{
	optional uint32 channel_id = 1;
	optional int64  music_id = 2;
	optional string music_name = 3;
	optional string music_author = 4;
}

//批量获取房间正在播放的歌曲信息
message BatGetChannelPlayingMusicInfoReq
{
	repeated uint32 channel_id_list = 1;
}

message BatGetChannelPlayingMusicInfoResp
{
	repeated PlayingMusicInfo music_list = 1;
}

service ChannelMusic {
	option( tlvpickle.Magic ) = 15566;		// 服务监听端口号

	rpc AddChannelMusic( AddChannelMusicReq ) returns( AddChannelMusicResp ) {
		option( tlvpickle.CmdID ) = 1;              // 命令号
		option( tlvpickle.OptString ) = "u:i:k:n:a:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u <uid> -i<channel_id> -k<client_key> -n<music_name> -a<music_author>";    // 测试工具的命令号帮助
	}

	rpc RemoveChannelMusic( RemoveChannelMusicReq ) returns( RemoveChannelMusicResp ) {
		option( tlvpickle.CmdID ) = 2;              // 命令号
		option( tlvpickle.OptString ) = "u:i:m:r:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u <uid> -i<channel_id> -m<music_id> -r<force_remove>";    // 测试工具的命令号帮助
	}

	rpc ChannelMusicCtrl( ChannelMusicCtrlReq ) returns( ChannelMusicCtrlResp ) {
		option( tlvpickle.CmdID ) = 3;              // 命令号
		option( tlvpickle.OptString ) = "u:i:t:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -i<channel_id> -t<ctrl_type 1-start 2-stop 3-next 4-prev> ";    // 测试工具的命令号帮助
	}

	rpc ChannelMusicHeartBeat( ChannelMusicHeartBeatReq ) returns( ChannelMusicHeartBeatResp ) {
		option( tlvpickle.CmdID ) = 4;              // 命令号
		option( tlvpickle.OptString ) = "u:i:m:p:e:h:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -i<channel_id> -m<music_id> -p<percent> -e<volume> -h<client_event>";    // 测试工具的命令号帮助
	}

	rpc SetChannelMusicPlayMode( SetChannelMusicPlayModeReq ) returns( SetChannelMusicPlayModeResp ) {
		option( tlvpickle.CmdID ) = 5;              // 命令号
		option( tlvpickle.OptString ) = "u:i:p:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -i<channel_id> -p<play_mode> ";    // 测试工具的命令号帮助
	}

	rpc SetChannelMusicVolume( SetChannelMusicVolumeReq ) returns( SetChannelMusicVolumeResp ) {
		option( tlvpickle.CmdID ) = 6;              // 命令号
		option( tlvpickle.OptString ) = "u:i:e:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -i<channel_id> -e<volume> ";    // 测试工具的命令号帮助
	}
	rpc GetChannelMusicList( GetChannelMusicListReq ) returns( GetChannelMusicListResp ) {
		option( tlvpickle.CmdID ) = 7;              // 命令号
		option( tlvpickle.OptString ) = "u:i:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -i<channel_id> ";    // 测试工具的命令号帮助
	}
	rpc SetChannelMusicNext( SetChannelMusicNextReq ) returns( SetChannelMusicNextResp ) {
		option( tlvpickle.CmdID ) = 8;              // 命令号
		option( tlvpickle.OptString ) = "u:i:m:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -i<channel_id> -m<music_id>";    // 测试工具的命令号帮助
	}

	rpc SetChannelMusicCanShare ( SetChannelMusicCanShareReq ) returns ( SetChannelMusicCanShareResp ){
		option( tlvpickle.CmdID ) = 9;              // 命令号
		option( tlvpickle.OptString ) = "u:i:s:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -i<channel_id> -s<can_share>";    // 测试工具的命令号帮助
	}
	rpc GetChannelMusicStatus ( GetChannelMusicStatusReq ) returns ( GetChannelMusicStatusResp ){
		option( tlvpickle.CmdID ) = 10;              // 命令号
		option( tlvpickle.OptString ) = "u:i:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -i<channel_id> ";    // 测试工具的命令号帮助
	}
	rpc SetChannelMusicCurrent( SetChannelMusicCurrentReq ) returns ( SetChannelMusicCurrentResp ){
		option( tlvpickle.CmdID ) = 11;              // 命令号
		option( tlvpickle.OptString ) = "u:i:m:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -i<channel_id> -m<music_id>";    // 测试工具的命令号帮助
	}
	rpc RemoveChannelMusicByUid( RemoveChannelMusicByUidReq ) returns( RemoveChannelMusicByUidResp ) {
		option( tlvpickle.CmdID ) = 12;              // 命令号
		option( tlvpickle.OptString ) = "u:i:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u <uid> -i<channel_id>";    // 测试工具的命令号帮助
	}
	rpc SetChannelMusicFreeMode( SetChannelMusicFreeModeReq ) returns( SetChannelMusicFreeModeResp ) {
		option( tlvpickle.CmdID ) = 13;              // 命令号
		option( tlvpickle.OptString ) = "u:i:x:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u <uid> -i<channel_id> -x<is_free_mode>";    // 测试工具的命令号帮助
	}
	rpc GetPlayingMusicChannelList( GetPlayingMusicChannelListReq ) returns( GetPlayingMusicChannelListResp ) {
		option( tlvpickle.CmdID ) = 14;              // 命令号
		option( tlvpickle.OptString ) = "u:";       // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u <uid>";    // 测试工具的命令号帮助
	}
	rpc RemoveChannelReportedMusic( RemoveChannelReportedMusicReq ) returns( RemoveChannelReportedMusicResp ) {
		option( tlvpickle.CmdID ) = 15;              // 命令号
		option( tlvpickle.OptString ) = "u:i:n:a:";       // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -i<channel_id> -n<music_name> -a<author>";    // 测试工具的命令号帮助
	}
	rpc GetReportedMusicList( GetReportedMusicListReq ) returns( GetReportedMusicListResp ) {
		option( tlvpickle.CmdID ) = 16;              // 命令号
		option( tlvpickle.OptString ) = "u:";       // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u <uid>";    // 测试工具的命令号帮助
	}
	rpc BatGetChannelPlayingMusicInfo( BatGetChannelPlayingMusicInfoReq ) returns( BatGetChannelPlayingMusicInfoResp ) {
		option( tlvpickle.CmdID ) = 17;              // 命令号
		option( tlvpickle.OptString ) = "u:x:";       // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u <uid> -x <channel_id_list like channel_id1 channel_id2>";    // 测试工具的命令号帮助
	}

}
