syntax="proto2";

// svrkit support
import "common/tlvpickle/skbuiltintype.proto";

package ChannelRobot;


enum ChannelStatus {
	EnterChannel = 1;
	LeaveChannel = 2;
}

message AddChannelRobotAccountReq {
	repeated uint32 uid_list = 1;
}

message JoinChannelNotifyReq {
	required uint32 channel_id = 1;
	required uint32 join_amount = 2;
	optional uint32 remain_user_cnt = 3;
}

message LeaveChannelNotifyReq {
	required uint32 channel_id = 1;
	required uint32 leave_amount = 2;
	optional uint32 remain_user_cnt = 3;
	optional bool is_robot_leave = 4;
}


message ControlRobotInfo{
	required uint32 base_num = 1;
	required uint32 into_channel_time = 2;
	required uint32 left = 3;
	required uint32 user_count = 4;
}

message CreateRobotUserReq{
	required uint32 num = 1;
	optional uint32 sex = 2;
}

message AddSpecifyChannelReq {
	required uint32 channel_id = 1;
}

message DelSpecifyChannelReq {
	required uint32 channel_id = 1;
}

message GetSpecifyChannelResp {
	repeated uint32 channel_id_list = 1;
}

message GetChannelRobotListReq {
	required uint32 channel_id = 1;
}


message GetChannelRobotListResp {
	repeated uint32 uid_list = 1;
}

message GetChannelRobotSizeReq {
	required uint32 channel_id = 1;
}


message GetChannelRobotSizeResp {
	required uint32 robot_size = 1;
}

message BatGetChannelRobotSizeReq {
	repeated uint32 channel_id_list = 1;
}

message BatGetChannelRobotSizeResp {
	repeated uint32 robot_size_list = 1;
}


message RobotCntInfo {
	required string cid = 1;
	required int32 robot_cnt = 2;
}

// 获取所有房间的机器人数量
message GetAllChannelRobotCntReq {

}

message GetAllChannelRobotCntResp {
	repeated RobotCntInfo cnt_info = 1;
}



service ChannelRobot {
	/**
	 * Server listening port
	 */
	option( tlvpickle.Magic ) = 15630;

	// 0 - 99, product related

	rpc AddChannelRobotAccount( AddChannelRobotAccountReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 1;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	rpc JoinChannelNotify( JoinChannelNotifyReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 2;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc LeaveChannelNotify( LeaveChannelNotifyReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 3;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc CreateRobotUser( CreateRobotUserReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 4;
		option( tlvpickle.OptString ) = "n:";
		option( tlvpickle.Usage ) = "-n <count>";
	}

	rpc AddSpecifyChannel( AddSpecifyChannelReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 5;
		option( tlvpickle.OptString ) = "x:";
		option( tlvpickle.Usage ) = "-x <channel_id>";
	}
		
	rpc DelSpecifyChannel( DelSpecifyChannelReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 6;
		option( tlvpickle.OptString ) = "x:";
		option( tlvpickle.Usage ) = "-x <channel_id>";
	}
	
	rpc GetSpecifyChannel( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetSpecifyChannelResp ) {
		option( tlvpickle.CmdID ) = 7;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc GetChannelRobotList( GetChannelRobotListReq ) returns ( GetChannelRobotListResp ) {
		option( tlvpickle.CmdID ) = 8;
		option( tlvpickle.OptString ) = "x:";
		option( tlvpickle.Usage ) = "-x <channel_id>";
	}

	rpc GetChannelRobotSize( GetChannelRobotSizeReq ) returns ( GetChannelRobotSizeResp ) {
		option( tlvpickle.CmdID ) = 9;
		option( tlvpickle.OptString ) = "x:";
		option( tlvpickle.Usage ) = "-x <channel_id>";
	}
	
	rpc BatGetChannelRobotSize( BatGetChannelRobotSizeReq ) returns ( BatGetChannelRobotSizeResp ) {
		option( tlvpickle.CmdID ) = 10;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	rpc GetAllChannelRobotCnt( GetAllChannelRobotCntReq ) returns ( GetAllChannelRobotCntResp ) {
		option( tlvpickle.CmdID ) = 11;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

}
