syntax = "proto2";

message ControlRule
{
	required uint32 percent = 1;		// [0-100], default 0
}

message LogicCmdControlRule
{
	map<string, ControlRule> logic_cmd_control_rule = 1;	// <logic_cmd_rule, rule>
}

message GrayReleaseControlRules
{
	map<string, LogicCmdControlRule> scene_rules_map = 1;	// <scene, rule>
}

message SpecialPeriod
{
	optional bool enable = 1;
	optional string begin_time = 2;
	optional string end_time = 3;
	optional string kickout_message = 4;
	optional uint32 charm_numeric = 5;
	optional uint32 consume_numeric = 6;
}

message ConfigFile
{
	optional GrayReleaseControlRules gray_rule = 1;
	optional SpecialPeriod special_period = 2;
	optional bool is_test_env = 3;
}

