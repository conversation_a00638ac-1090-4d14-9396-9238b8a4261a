syntax="proto2";

// namespace
package banuser;

// 必须import
import "common/tlvpickle/skbuiltintype.proto";


enum BAN_OP_TYPE{
    //封 操作
    BAN_OP_BAN_USER = 0x0001;       // 封用户
    BAN_OP_BAN_DEVICE = 0x0002;     // 封设备
    BAN_OP_BAN_CLIENT_IP = 0x0004;  //  封 客户端ip, reserved
    BAN_OP_BAN_PHONE = 0x0008;  //  封 手机号, reserved

    //解封 操作
    BAN_OP_UNBAN_USER = 0x0100;     //解封 用户
    BAN_OP_UNBAN_DEVICE = 0x0200;   //解封 设备
    BAN_OP_UNBAN_CLIENT_IP = 0x0400;//解封 客户端ip, reserved   
    BAN_OP_UNBAN_PHONE = 0x0800;    // 解封 手机号, reserved
}

enum BANNED_TYPE{
    BAN_NOBANNED = 0;
    BAN_BY_USER = 1;
    BAN_BY_DEVICE = 2;
    BAN_BY_CLIENT_IP = 3;
    BAN_BY_PHONE = 4;
}

enum BAN_LEVEL_TYPE {
    // 默认为普通封禁
    BAN_LEVEL_TYPE_UNSPECIFIED = 0x00;
    // 普通封禁
    BAN_LEVEL_TYPE_COMMON = 0x01;
    // 黑色运营后台封禁
    BAN_LEVEL_TYPE_BACKSTAGE = 0x02;
}

message UpdateBanStatusReq {
    required uint32 op_type = 1;    //BAN_OP_TYPE
    optional uint32 uid = 2;
    optional string device_id = 3;
    optional string client_ip = 4;
    optional uint32 at = 5;
    optional uint32 recovery_at = 6;
    optional string reason = 7;
    optional string operator_id = 8;
    optional string proof_pic = 9;
    optional string ext_info = 10;
    optional bool  no_log = 11; //不记日志
    optional string reason_detail = 12;
    // 封禁级别 see BAN_LEVEL_TYPE
    optional uint32 ban_level = 13;
}

message UpdateBanStatusResp{
}

enum BAN_STATUS{
    BAN_ST_NORMAL = 0;
    BAN_ST_BANNED = 1;
}

message GetBannedStatusReq{
    optional uint32 uid = 1;
    optional string device_id = 2;
    optional string client_ip = 3;
    optional string phone = 4;  
}

enum REASON_CODE {
    REASON_CODE_NORMAL = 0;
    REASON_CODE_BAN = 1;
    REASON_CODE_ACCOUNT_RECYCLE = 2;
    REASON_CODE_ACCOUNT_BLACK = 4;
}

message BannedStatus {
    required uint32 status = 1;    //BAN_STATUS
    optional uint32 banned_type = 2;    //BANNED_TYPE
    optional uint32 uid = 3;
    optional uint32 banned_at = 4;
    optional uint32 recovery_at = 5;
    optional string reason = 6; // 面对用户的原因提示
    optional uint32 reason_code = 7;
    optional string reason_detail = 8; // 原因的详情内容,运营后台使用
}

message GetBannedStatusResp {
    required BannedStatus banned_status = 1;
}

message BatchGetUserBannedStatusReq {
    repeated uint32 uid_list = 1;
}

message BatchGetUserBannedStatusResp {
    repeated BannedStatus status_list = 1;
}


message GetBannedHistoryReq {
    optional uint32 uid = 1;
    optional string device_id = 2;
    optional string client_ip = 3;  //reserved
    optional uint32 op_type = 4;    //reserved
    optional uint32 offset = 5;
    optional uint32 limit = 6;
    optional uint32 begin_time = 7;  //根据封禁时间查询
    optional uint32 end_time = 8;
    optional string operator_id = 9; // 操作人
    optional string reason = 10;     // 封禁原因
}

message BannedRecord {
    required uint32 op = 1;    //BAN_OP_TYPE
    required uint32 at = 2;
    required uint32 recovery_at = 3;
    optional string reason = 4;
    optional string operator_id = 5;
    optional uint32 uid = 6;
    optional string device_id = 7;
    optional string client_ip= 8;  //reserved
    optional string proof_pic = 9;
    optional string ext_info = 10;
    optional string reason_detail = 11;
}

message GetBannedHistoryResp {
    repeated BannedRecord  records = 1; 
    required uint32 total = 2;
}

message DeviceBannedStatus {
    required uint32 status = 1;    //BAN_STATUS
    optional uint32 banned_type = 2;    //BANNED_TYPE
    optional string device_id = 3;
    optional uint32 banned_at = 4;
    optional uint32 recovery_at = 5;
    optional string reason = 6;
    optional string reason_detail = 7;
}

message BatchGetDeviceBannedStatusReq {
    repeated string device_ids = 1;
}

message BatchGetDeviceBannedStatusResp {
    repeated DeviceBannedStatus status_list = 1;
}

// 封禁申诉
enum BannedAppealState {
    BANNED_APPEAL_STATE_NONE = 0;
    BANNED_APPEAL_STATE_PEND = 1;    // 待处理
    BANNED_APPEAL_STATE_PASS = 2;    // 已处理：通过
    BANNED_APPEAL_STATE_DENY = 4;    // 已处理：拒绝
    BANNED_APPEAL_STATE_BLACK = 5;   // 已处理：打黑
}

message BannedAppealRecord {
    optional uint32 id = 1;
    optional uint32 uid = 2;
    optional uint32 state = 3;
    optional uint32 reason_code = 4;
    optional int64 banned_begin_at = 5;   // 被封禁时间段
    optional int64 banned_end_at = 6;
    optional int64 create_at = 7;
    optional int64 update_at = 8;
    optional string create_desc = 9;
    optional string update_desc = 10;
    optional string operator = 11;
    optional uint32 market_id = 12;
}

message GetBannedAppealRecordReq {
    repeated uint32 uid_list = 1;
    repeated uint32 state_list = 2;
    repeated uint32 reason_code_list = 3; // BannedReasonCode
    optional int64 create_begin_at = 4;
    optional int64 create_end_at = 5;
    optional uint32 offset = 6;
    optional uint32 limit = 7;
    optional bool resp_total = 8;
}

message GetBannedAppealRecordResp {
    repeated BannedAppealRecord record_list = 1;
    optional uint32 total = 2;
}

message SetBannedAppealRecordReq {
    repeated BannedAppealRecord record_list = 1;
}

message SetBannedAppealRecordResp {
}

message UpdateBannedAppealRecordReq {
    optional uint32 id = 1;             // 约束条件
    optional uint32 uid = 2;            // 约束条件
    optional uint32 state = 3;
    optional string update_desc = 4;
    optional string operator = 5;
}

message UpdateBannedAppealRecordResp {
    optional bool updated = 1;
}

// 获取封禁操作人
message GetBannedOperatorReq
{
   optional string operator_name = 1;
   optional uint32 limit = 2;
}

message BannedOperator
{
   optional string operator_name = 1;
   optional uint32 last_op_at = 2;
   optional uint32 last_op_type = 3;
}

message GetBannedOperatorResp
{
   repeated BannedOperator operator_list = 1;
}

message UpdateBanedStatus {
    required uint32 op_type = 1;    //BAN_OP_TYPE
    optional uint32 uid = 2;
    optional string device_id = 3;
    optional string client_ip = 4;
    optional uint32 at = 5;
    optional uint32 recovery_at = 6;
    optional string reason = 7;
    optional string operator_id = 8;
    optional string proof_pic = 9;
    optional string ext_info = 10;
    optional bool  no_log = 11; //不记日志
    optional string reason_detail = 12;
    // 封禁级别 see BAN_LEVEL_TYPE
    optional uint32 ban_level = 13;
}

message BatchUpdateBanedStatusReq
{
    repeated UpdateBanedStatus list = 1;
}

message BatchUpdateBanedStatusResp
{
    optional int32 success_size = 1;
}

service BanUser {
	option( tlvpickle.Magic ) = 15064;		// 服务监听端口号

    rpc UpdateBanedStatus ( UpdateBanStatusReq ) returns ( UpdateBanStatusResp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "o:u:d:t:r:a:x:Lb:";
        option( tlvpickle.Usage ) = "-o <op,1-ban,2-unban> -u <uid> [-d <device_id>] [-t <recovery time>] [-r <reason>] [-a <operator>] [-x <proof_pic>] [-L, no log] [-b ban_at]";
    }

    rpc GetBannedStatus ( GetBannedStatusReq ) returns ( GetBannedStatusResp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:d:h:t:";
        option( tlvpickle.Usage ) = "-u <uid> -d <device_id> -h <client ip> -t <phone>";
    }

    rpc BatchGetUserBannedStatus ( BatchGetUserBannedStatusReq ) returns ( BatchGetUserBannedStatusResp ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid1,uid2,...>";
    }

    rpc GetBannedHistory ( GetBannedHistoryReq ) returns ( GetBannedHistoryResp ) {
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "u:d:o:l:b:e:p:r:";
        option( tlvpickle.Usage ) = "-u <uid> -d <device_id> -o <offset> -l <limit> -b <begin_time> -e <end_time> -p <operator> -r <reason>";
    }

    rpc BatchGetDeviceBannedStatus ( BatchGetDeviceBannedStatusReq ) returns ( BatchGetDeviceBannedStatusResp ) {
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "d:";
        option( tlvpickle.Usage ) = "-d <device_id,device_id,...>";
    }

    rpc GetBannedAppealRecord ( GetBannedAppealRecordReq ) returns ( GetBannedAppealRecordResp ) {
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "u:s:r:b:e:o:l:";
        option( tlvpickle.Usage ) = "-u <uid> -s <state> -r <reasonCode> -b <beginAt> -e <endAt> -o <offset> -l <limit>";
    }

    rpc SetBannedAppealRecord ( SetBannedAppealRecordReq ) returns ( SetBannedAppealRecordResp ) {
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "u:s:r:m:n:o:";
        option( tlvpickle.Usage ) = "-u <uid> -s <state> -r <reasonCode> -m <createDesc> -n <updateDsec> -o <opeartor>";
    }

    rpc UpdateBannedAppealRecord ( UpdateBannedAppealRecordReq ) returns ( UpdateBannedAppealRecordResp ) {
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "i:u:n:s:o:";
        option( tlvpickle.Usage ) = "-i <id> -u <uid> -s <state> -n <updateDsec> -o <opeartor>";
    }

    rpc GetBannedOperator ( GetBannedOperatorReq ) returns ( GetBannedOperatorResp ) {
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc BatchUpdateBanedStatus ( BatchUpdateBanedStatusReq ) returns ( BatchUpdateBanedStatusResp ) {
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

}


//cache 对应 ban_log 表
message BannedCacheData {
    required uint32 status = 1;    //BAN_STATUS
    optional uint32 banned_at = 2;
    optional uint32 recovery_at = 3;
    optional string reason = 4;
    optional uint32 ref_uid = 5;
    optional string ref_device = 6; 
    optional string ref_phone = 7;
    optional uint32 reason_code = 8;
    optional string reason_detail = 9;
}



