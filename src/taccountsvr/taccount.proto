syntax="proto2";

//注意，此proto文件仅作为生成测试框架用


// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package taccount;



message GetPackageNameMsg {
	required uint32 game_id = 1;
	required string url = 2;
}

service TAccount {
	option( tlvpickle.Magic ) = 9090;		// 服务监听端口号

	rpc login( tlvpickle.SKBuiltinEmpty_PB ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "u:m:p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <username> -m <mobile> -p <password>";	// 测试工具的命令号帮助
	}

	rpc SendMessage( tlvpickle.SKBuiltinEmpty_PB ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "t:m:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <type> -m <mobile>";	// 测试工具的命令号帮助
	}

	rpc VerifyMessage( tlvpickle.SKBuiltinEmpty_PB ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 3;										// 命令号
        option( tlvpickle.OptString ) = "t:m:n:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <type> -m <mobile> -n <number>";	// 测试工具的命令号帮助
	}

	rpc RegisterByMobilePhone ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 4;										// 命令号
        option( tlvpickle.OptString ) = "m:p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <mobile> -p <password>";	// 测试工具的命令号帮助
	}

	rpc RegisterByLoginName ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 5;										// 命令号
        option( tlvpickle.OptString ) = "n:p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <login name> -p <password>";	// 测试工具的命令号帮助
	}

	rpc RegisterAuto ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 6;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
	}

	rpc ResetPassword ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 7;										// 命令号
        option( tlvpickle.OptString ) = "m:p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <mobile> -p <password>";
	}

	rpc ModifyPassword ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 8;										// 命令号
        option( tlvpickle.OptString ) = "u:o:n:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -o <old password> -n <new password>";
	}


	rpc SearchGameList ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 9;										// 命令号
        option( tlvpickle.OptString ) = "k:o:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-k <keyword> -o <offset> -s <size>";	// 测试工具的命令号帮助
	}

	rpc UpdateGamePartnerStatus ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 10;										// 命令号
        option( tlvpickle.OptString ) = "g:p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game id> -p <is partner>";
	}

	rpc TypeGiftPkg( tlvpickle.SKBuiltinEmpty_PB ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 11;										// 命令号
        option( tlvpickle.OptString ) = "g:n:i:c:u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game id> -n <gift package name> -i <intro> -c <content> -u <usage>";	// 测试工具的命令号帮助
	}

	rpc GetGiftPkgList( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 12;										// 命令号
        option( tlvpickle.OptString ) = "o:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-o <offset> -s <size>";	// 测试工具的命令号帮助
	}

	rpc PassGiftPkgApply( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 13;										// 命令号
        option( tlvpickle.OptString ) = "g:a:o:u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild id> -a <apply id> -o <offer number> -u <op user>";
	}

	rpc RejectGiftPkgApply( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 14;										// 命令号
        option( tlvpickle.OptString ) = "g:a:r:u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild id> -a <apply id> -r <reject reason> -u <op uid>";
	}

	rpc TypeGiftPkgSerial( tlvpickle.SKBuiltinEmpty_PB) returns (tlvpickle.SKBuiltinEmpty_PB){
		option( tlvpickle.CmdID ) = 15;
		option( tlvpickle.OptString ) = "g:s:";
		option( tlvpickle.Usage ) = "-g <gift package id> -s <serial> -u <user account>";

	}

	rpc EditGiftPkg( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 16;										// 命令号
        option( tlvpickle.OptString ) = "p:g:n:i:c:u:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <gift pkg id> -g <game id> -n <gift package name> -i <intro> -c <content> -u <usage> -s <is show>";
	}

	rpc GetGiftPkgApplyList ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 17;										// 命令号
        option( tlvpickle.OptString ) = "o:s:g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-o <offset> -s <size> -g <guild id>";
	}

	rpc UpdateGiftPkgShowStatus ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 18;										// 命令号
        option( tlvpickle.OptString ) = "p:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <gift pkg id> -s <is show>";
	}

	rpc GiveGiftPkgToGuild ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 19;										// 命令号
        option( tlvpickle.OptString ) = "p:g:n:u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <gift pkg id> -g <guild id> -n <offer number> -u <op uid>";
	}

	rpc GetPartnerGameList ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 20;										// 命令号
        option( tlvpickle.OptString ) = "o:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-o <offset> -s <size>";
	}

	rpc GetGiftPkgListByGame ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 21;										// 命令号
        option( tlvpickle.OptString ) = "g:o:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game id> -o <offset> -s <size>";
	}

	rpc GetApplyGiftPkgGuildList ( tlvpickle.SKBuiltinEmpty_PB  ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 22;										// 命令号
        option( tlvpickle.OptString ) = "g:o:s:r:d:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild id> -o <offset> -s <size> -r <order by> -d < is desc> -t <status>";
	}

	rpc GetUserGuildInfoByUsername ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 23;										// 命令号
        option( tlvpickle.OptString ) = "n:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <username>";
	}

	rpc GetSmallFaceByAccount ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 24;										// 命令号
        option( tlvpickle.OptString ) = "n:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <username>";
	}

    rpc GetGiftpkgOpHistory( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
    	option( tlvpickle.CmdID ) = 25;								// 命令号
        option( tlvpickle.OptString ) = "b:e:o:s:a:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-b <begintime> -e <endtime> -a <account> -o <offset> -s <size>";
    }


    rpc collectMissionBonus( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
    	option( tlvpickle.CmdID ) = 26;										// 命令号
        option( tlvpickle.OptString ) = "u:k:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -k <mission key>";
    }


    rpc GetUserByUid( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
    	option( tlvpickle.CmdID ) = 27;										// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";
    }



}
