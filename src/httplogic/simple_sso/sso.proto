syntax="proto2";

package sso;

message AppLoginContext {
    required uint32 app_id = 1;
    required uint32 uid = 2;
    required string account = 3;
    required string alias = 4;
}

message WechatLoginContext {
    required string app_id = 1;
    required string open_id = 2;
    optional string union_id = 3;
    optional uint32 uid = 4;
    optional string account = 5;
    optional string alias = 6;
}

message LoginContext {
    enum Type {
        APP_LOGIN = 1;
        WECHAT_LOGIN = 2;
    }

    required Type login_type = 1;
    optional AppLoginContext app_login_context = 2;
    optional WechatLoginContext wechat_login_context = 3;
}

message SSOCookie {
    required LoginContext login_context = 1;
    required uint32 expiry = 2;
    required uint32 salt = 3;
}
