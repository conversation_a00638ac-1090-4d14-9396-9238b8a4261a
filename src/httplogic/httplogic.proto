syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package httplogic;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

enum HTTP_CMD{
    ECHO = 1;
}

message CommHttpErr{
	required uint32 code = 1;
	optional string errmsg  = 2;
}

message CommHttpReq{
}

message CommHttpResp{
}

// httplogic服务
service HttpLogic{
	option( tlvpickle.Magic ) = 8080;		// 服务监听端口号
    rpc CommHttp( CommHttpReq ) returns( CommHttpResp );
}
