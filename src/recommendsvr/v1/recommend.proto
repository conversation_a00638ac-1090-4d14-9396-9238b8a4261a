syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package recommend;

message SGroup2UserNum 
{
    required uint32 group_id = 1;
    required uint32 usernum   = 2; // 做过某种动作的群成员人数
	optional uint32 game_id  = 3;  // 
	optional uint32 guild_id = 4;
}

message SRecommendGuildList 
{
    repeated uint32 guild_list = 1;
}

message SRecommendGroupIDList 
{
    repeated uint32 groupid_list = 1;
}

message SUser2Cnt 
{
    required uint32 uid = 1;
    required uint32 cnt = 2;
}

message SGroup2User2CntList 
{
    required uint32 group_id = 1;
    repeated SUser2Cnt usercnt_list = 2;
}

// 上报最近周期内 工会群组ID下发言的用户的累计发言条数
message ReportGuildGroupRecentSendMsgUserCntReq
{
	required uint32 timestamp = 1; // 上报数据对应的起始周期时间戳
	repeated SGroup2User2CntList group2usermsgcnt_list = 2;
}


// 上报工会群组ID下的最近周期内累计登录的用户数目
message ReportGuildGroupRecentLoginUserCntReq
{
	required uint32 timestamp = 1;	
	repeated SGroup2UserNum group2usernum_list = 2;
}

// 上报最近周期内 主题群组ID下发言的用户的累计发言条数
message ReportThemeGroupRecentSendMsgUserCntReq
{
	required uint32 timestamp = 1; // 上报数据对应的起始周期时间戳
	repeated SGroup2User2CntList group2usermsgcnt_list = 2;
}

// 获取游戏ID对应的满足推荐条件的工会列表
message GetRecommendGuildListByGameIDReq
{
	required uint32 uid = 1;	
	required uint32 gameid = 2;	
	optional uint32 timestamp = 3; // 可选用户请求的时间
	optional uint32 guildSize = 4; // 需要拉取的工会数目(默认50)
}

// 获取满足条件的主题群组列表
message GetRecommendThemeGroupListReq
{
	required uint32 uid = 1;	
	optional uint32 timestamp = 2; // 可选用户请求的时间
	optional uint32 begin_id = 3;  // 起始序号 默认为0
	optional uint32 getSize  = 4;  // 需要拉取的群组数目(默认50)
}

service recommend {
	option( tlvpickle.Magic ) = 15150;		// 服务监听端口号
	
		
	rpc ReportGuildGroupRecentLoginUserCnt ( ReportGuildGroupRecentLoginUserCntReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 1;
		option( tlvpickle.OptString ) = "g:n:";
		option( tlvpickle.Usage ) = "-g <group_id> -n <user num>";
	}
		
	rpc ReportGuildGroupRecentSendMsgUserCnt ( ReportGuildGroupRecentSendMsgUserCntReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 2;
		option( tlvpickle.OptString ) = "g:u:n:";
		option( tlvpickle.Usage ) = "-g <group_id> -u <uid> -n <user count>";
	}
	
	rpc ReportThemeGroupRecentSendMsgUserCnt ( ReportThemeGroupRecentSendMsgUserCntReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 3;
		option( tlvpickle.OptString ) = "g:u:n:";
		option( tlvpickle.Usage ) = "-g <group_id> -u <uid> -n <user count>";
	}
	
	rpc GetRecommendGuildListByGameID ( GetRecommendGuildListByGameIDReq ) returns ( SRecommendGuildList ){
		option( tlvpickle.CmdID ) = 4;
		option( tlvpickle.OptString ) = "u:g:";
		option( tlvpickle.Usage ) = "-u <uid> -g <game id>";
	}

	rpc GetRecommendThemeGroupList( GetRecommendThemeGroupListReq ) returns ( SRecommendGroupIDList ){
		option( tlvpickle.CmdID ) = 5;
		option( tlvpickle.OptString ) = "u:";
		option( tlvpickle.Usage ) = "-u <uid> ";
	}	
	
}
