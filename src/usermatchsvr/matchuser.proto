syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package matchuser;

// 触发进匹配池的操作类型
enum MatchBeginType
{
  ENUM_MATCHBEGIN_UNVALID = 0;                  // 无效的类型值
  ENUM_MATCHBEGIN_MATCH = 1;                    // 匹配操作
  ENUM_MATCHBEGIN_LBS = 2;                      // 附近的人
  ENUM_MATCHBEGIN_QUICKMATCH = 3;               // 快速匹配
  ENUM_MATCHBEGIN_SUPLLE = 4;                   // 快速补位
  ENUM_MATCHBEGIN_OPENAPP = 5;                  // 打开app
  ENUM_MATCHBEGIN_SYS_RECOMMEND = 6;            // 系统推荐
};

message SimpleTagInfo
{
  required uint32 tag_id=1;
  required uint32 tag_type=2;
};

message UserMatchInfo
{
  required uint32 uid = 1;
  required uint32 sex = 2;
  required uint32 pool_level = 3;
  required uint32 friends_num = 4;
  /*repeated SimpleTagInfo tag_info = 5;*/
  repeated TagInfo tag_info = 5;
  optional uint32 match_time = 6;       // 主动/被动 发起匹配，进入匹配池的时间
};

message TagInfo
{
  required string tag_name = 1;
  required uint32 tag_id = 2;
  required uint32 tag_type = 3;
  optional bytes tag_ext_info = 4;
};

message MatchedUser
{
  required uint32 uid = 1;
  repeated TagInfo tag_info = 2;
};

message GetMatchUsersReq
{
  required uint32 uid = 1;
  required uint32 start = 2;
  required uint32 count = 3;
  optional uint32 get_match_type = 4;  // see MatchBeginType
}

message GetMatchUsersResp
{
  repeated uint32 users = 1;
  required bool reach_end = 2;
  repeated MatchedUser users_info = 3;
}

// 触发match,进入 match pool
message MatchBeginReq
{
  required uint32 uid = 1;
  required uint32 sex = 2;          // 用户性别
  optional uint32 friends_num = 3;
  optional uint32 match_begin_type = 4;  // see MatchBeginType
}

message UidSex
{
  required uint32 uid = 1;
  required uint32 sex = 2;          // 用户性别
}

message MatchBeginNReq
{
  repeated UidSex users = 1;
  optional uint32 match_begin_type = 2;
}

message MatchBeginResp
{
}

// 触发 退出 match pool,不想被匹配
message MatchEndReq
{
  required uint32 uid = 1;
}

message MatchEndResp
{
}

message GetMatchScoreReq
{
  required uint32 op_uid = 1;
  required uint32 matched_uid = 2;
}

message GetMatchScoreResp
{
  required uint32 match_score = 1;
}

service matchuser {
	option( tlvpickle.Magic ) = 15663;		// 服务监听端口号

    rpc GetMatchUsers( GetMatchUsersReq ) returns( GetMatchUsersResp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "-u <uid>:";
        option( tlvpickle.Usage ) = "";
    }

    rpc MatchBegin ( MatchBeginReq ) returns ( MatchBeginResp ){
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "-u <uid>:";
        option( tlvpickle.Usage ) = "";
    }

    rpc MatchEnd ( MatchEndReq ) returns ( MatchEndResp ){
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "-u <uid>:";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetMatchScore ( GetMatchScoreReq ) returns ( GetMatchScoreResp )
    {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "-u1 <uid> -u2<uid>:";
        option( tlvpickle.Usage ) = "";
    }

    rpc MatchBeginN ( MatchBeginNReq ) returns ( MatchBeginResp ){
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "-u <uid>:";
        option( tlvpickle.Usage ) = "";
    }

}
