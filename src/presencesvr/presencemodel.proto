syntax="proto2";


package Presence;									

//
// 数据存储, 内部使用
//

//用户在线数据
message ProxyAddrModel
{
    required uint32 ip = 1;
    required uint32 port = 2;

}
message PresItemModel
{
    required uint32 uid = 1;
    required ProxyAddrModel proxy_addr= 2;
	required uint32 client_id = 3; 		// clientId
	required uint32 client_ip = 4;		// ip
	required uint64 online_time = 5;		// 上线时间点, 单位:微秒
	required bytes device_id = 6;
	required uint32 status = 7; 		// 在线状态 
    required uint32 terminal_type = 8;      //Terminal type
    optional uint64 offline_time  = 9;      // 下线时间
}
message UserPresModel {
    repeated PresItemModel pres_list = 1; 
}

message ProxyModel
{
    required ProxyAddrModel proxy_addr= 1;
    required uint32 boot_at = 2;  // 启动时间
}

message ProxyStatModel
{
    required ProxyAddrModel proxy_addr= 1;
	required uint32 max_online_count = 2;
	required uint32 max_online_at = 3;

}
