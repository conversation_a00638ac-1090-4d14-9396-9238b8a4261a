syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";		
//import "presencemodel.proto";

// namespace
package Presence;									

// 专用错误码从-20开始
//enum ERR_PRES 
//{
	//ERR_PRES_EXIST = -1800;	
    //ERR_SESSION_NOT_FOUND = -1801;
    //ERR_SESSION_EXPIRED = -1802;
//}

enum PRES_TYPE
{
	PRES_TYPE_ANDROID = 0;
	PRES_TYPE_IOS = 1;
	PRES_TYPE_PC = 2;
}
enum PRES_STATUS {
    PRES_OFFLINE = 0;  /* 下线 */
    PRES_ONLINE = 1;   /* 在线 */
}


message Pres {
    required uint32 proxy_ip = 1;
    required uint32 proxy_port = 2;
	required uint32 uid = 3;			// uid
	required bytes device_id = 4;		// 设备ID, len = 16
	required uint32 status = 5; 		// 在线状态 
	required uint32 client_id = 6; 		// clientId
	required uint32 client_ip = 7;		// ip
	required uint32 online_time = 8;		// 上线时间点, 单位:秒
    optional uint32 terminal_type = 9;      //Terminal type
    optional uint32 online_time_ms = 10;    // 上线时间点, 微妙 部分
    optional uint64 offline_time = 11;   //查登录历史时用
}

message Proxy {
    required uint32 proxy_ip = 1;
    required uint32 proxy_port = 2;
    required uint32 boot_time = 3;
}

message UpdatePresReq {
	//required uint32 uid = 1;
	// required uint32 prestype = 2;
    required Proxy proxy = 1;
	repeated Pres pres_list = 4;
}

message UpdatePresResp {
}

//////////////////
message GetPresReq { 
	required uint32 uid = 1;	
}

message GetPresResp {
	repeated Pres pres_list = 3;
}

//////////////////
message PresStat {
	required uint32 proxy_ip = 1;
	required uint32 proxy_port = 2;
	required uint32 online_count = 3;	
	required uint32 max_online_count = 4;
	required uint32 max_online_at_time = 5;
    optional uint32 boot_at = 6;
}

message BatchGetPresReq {
	repeated uint32 uid_list = 1;
}

message ClientInfo {
	required int64 uid = 1;			// uid
	required uint32 client_id = 2; 		// clientId
}

message ProxyInfo {
	required uint32 proxy_ip = 1;
	required int32 proxy_port = 2;
}

message SubscribeChannelReq {
	required int64 channel_id = 1;
    required ProxyInfo proxy_info = 2; 
	optional int64 timestamp = 3;
	optional int64 version = 4;
	repeated ClientInfo client_info = 5;
}

message BatchSubscribeChannelReq {
	required int64 seq = 1;
    repeated SubscribeChannelReq sub = 2; 
}

message GetChannelProxyInfoReq {
	required int64 seq = 1;
	required int64 channel_id = 2;
}

message GetChannelProxyInfoResp {
	required int64 seq = 1;
	required int64 channel_id = 2;
    repeated ProxyInfo proxy_info = 3; 
}

message BatchGetPresResp {
	repeated Pres pres_list = 3;
}

message StatPresReq {
	
}

message StatPresResp {
	repeated PresStat pres_stat_list = 2;
}

service Presence {
    option( tlvpickle.Magic ) = 12001;		// 服务监听端口号

    rpc UpdatePres(UpdatePresReq) returns(UpdatePresResp) {
        option( tlvpickle.CmdID ) = 1;										
        option( tlvpickle.OptString ) = "u:s:d:i:p:";							
        option( tlvpickle.Usage ) = "-u <uid> -s <status> -d <deviceid> -i <clientID> -p <proxy+client ip>";	
    }   

    rpc GetPres(GetPresReq) returns(GetPresResp) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:n:";
        option( tlvpickle.Usage ) = "[-u <uid>] [-n <username>]";	
    }
    rpc BatchGetPres(BatchGetPresReq) returns(BatchGetPresResp) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:s:";
        option( tlvpickle.Usage ) = "-u <uid> -s <uid2>";	
    }
    rpc StatPres(StatPresReq) returns(StatPresResp) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";	
    }

    rpc SubscribeChannel(SubscribeChannelReq) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";	
    }

    rpc BatchSubscribeChannel(BatchSubscribeChannelReq) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";	
    }
    rpc GetChannelSubscriber(GetChannelProxyInfoReq) returns(GetChannelProxyInfoResp) {
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";	
    }
} 

