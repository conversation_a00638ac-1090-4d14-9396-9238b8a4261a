syntax="proto2";

package oauth;

import "common/tlvpickle/skbuiltintype.proto";


//enum OAUTH2_AUTH_SESSION_STEP{
//    OAUTH2_AUTH_SESSION_INIT = 1;
//    OAUTH2_AUTH_SESSION_CODE_ISSUED = 2;
//}

message AuthorizationReq{
    required string appid = 1;
    required string redirect_uri = 2;
    required uint32 uid = 3;
    required string response_type = 4;
    required string scope = 5;
    optional string state = 6;
}

//内部使用
message Oauth2AuthCtrlBlock{
    required AuthorizationReq auth_req = 1;
    optional string uuid = 2;
    optional string authcode = 3;
}


//message Oauth2AuthKey{
//    required uint32 internal_appid = 1;
//    required uint32 uid = 2;
//}


//message Oauth2AuthValue{
//    repeated Oauth2AuthCtrlBlock auth_cb_list = 1;
//}


//message Oauth2TokenCtrlBlock{
//}

//message RegisterAppReq{
//    required string app_name = 1;
//}

message AppItem{   
   required string appid = 1;
   required string app_secret = 2;
   optional string app_name = 3;   
}

//message RegisterAppRsp{
//    required AppItem app = 1;
//}

message GetAppReq{
    required string appid = 1;
}

message GetAppRsp {
    optional int32 error_code = 1;
    optional AppItem app = 2;
}

message GetAppListReq{
    required uint32 offset   = 1;
    required uint32 limit    = 2;
}

message GetAppListRsp{
    repeated AppItem app_list = 1;
}

message ConstructAuthorizationReq{
    required AuthorizationReq auth_req = 1;    
}

message ConstructAuthorizationRsp{   
    optional int32 error_code = 1;
    optional string uuid = 2;
}

message DestructAuthorizationReq{
    required string uuid = 1;
}

message DestructAuthorizationRsp{
    optional int32 error_code = 1;
}

message GetAuthorizationCodeReq{
    required string uuid = 1;
}

message GetAuthorizationCodeRsp{
    required int32 error_code = 1;
    optional string redirect_uri = 2;
    optional string state = 3;
    optional string code = 4; 
}

message GetAccessTokenReq{
    required string appid = 1;
    required string secret = 2;
    required string code = 3;
}

message GetAccessTokenRsp {
    optional int32 error_code = 1;
    optional string access_token = 2;
    optional uint32 expires_in = 3;
    optional string refresh_token = 4;
    optional string scope = 5;
    optional string openid = 6;
}

message RefreshTokenReq{
    required string appid = 1;
    required string refresh_token = 2;
}

message RefreshTokenRsp{
    optional int32 error_code = 1;
    optional string access_token = 2;
    optional uint32 expires_in = 3;
    optional string refresh_token = 4;
}

message ValidateAccessTokenReq{
    required string access_token = 1;
    required string openid = 2;
}

message ValidateAccessTokenRsp{
    optional int32 error_code = 1;
    optional uint32 uid = 2;
}

service oauth{
    
    //rpc RegisterApp ( RegisterAppReq ) returns ( RegisterAppRsp ) {
    //    option( tlvpickle.CmdID ) = 1;
    //    option( tlvpickle.OptString ) = "";
    //    option( tlvpickle.Usage ) = "";
    //}
    rpc GetApp ( GetAppReq ) returns ( GetAppRsp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "a:";
        option( tlvpickle.Usage ) = "-a <3rd_appid str>";
    }

    rpc GetAppList ( GetAppListReq ) returns ( GetAppListRsp ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    
    rpc ConstructAuthorization( ConstructAuthorizationReq ) returns ( ConstructAuthorizationRsp ){
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "a:l:";
        option( tlvpickle.Usage ) = "-a <3rd_appid str>";
    }
    
    rpc DestructAuthorization( DestructAuthorizationReq ) returns (DestructAuthorizationRsp){
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    
    rpc GetAuthorizationCode( GetAuthorizationCodeReq ) returns ( GetAuthorizationCodeRsp ){
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetAccessToken( GetAccessTokenReq ) returns ( GetAccessTokenRsp ){
        option( tlvpickle.CmdID ) = 13;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";

    }

    rpc ValidateAccessToken ( ValidateAccessTokenReq ) returns ( ValidateAccessTokenRsp ) {
        option( tlvpickle.CmdID ) = 14;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc RefreshToken ( RefreshTokenReq ) returns ( RefreshTokenRsp ) {
        option( tlvpickle.CmdID ) = 15;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
}
