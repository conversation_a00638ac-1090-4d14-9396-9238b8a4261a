syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Helper;

message VoiceBallHelperInfo{
	required string title = 1; //机型
	optional string url = 2;   //url
    optional int32 code = 3;   //内部编码
    optional string icon = 4;  //路径
}

message GetVoiceBallHelperInfoResp{
	repeated VoiceBallHelperInfo infos = 1;	
}

message SetVoiceBallHelperInfoReq{
	repeated VoiceBallHelperInfo infos = 1;
}

message RemoveVoiceBallHelperReq{
	required int32 code = 1; //内部编码
}

enum EHelperOSType
{
	ENUM_HELPER_OS_DEFAUT = 0;
	ENUM_HELPER_OS_ANDRIOD = 1;
	ENUM_HELPER_OS_IOS = 2;
}

// 帮助问题
message HelperQuestion
{
	required int32 quesId = 1;
	required string title = 2;
	required string url = 3;
	required string icon = 4;
	required int32 highline = 5;
	optional string model = 6;
	optional uint32 modelId = 7;
	optional uint32 is_delete = 8;
	optional uint32 os_type = 9; // EHelperOSType 问题的平台类型
	
}

// 帮助项（专栏）一个专栏包括很多个问题列表
message HelperColumn
{
	required int32 columnId = 1;
	required string name = 2;
	required string icon = 3;
	required string des = 4;
	repeated HelperQuestion list = 5;
	optional uint32 is_delete = 6;
	optional uint32 os_type = 7; // EHelperOSType 专题的平台类型
}

message HelperColumnList
{
	repeated HelperColumn info = 1;
}

// 获取专栏列表
message GetHelperColumnListReq
{
	optional uint32 os_type = 1; // EHelperOSType 专题的平台类型
}

// 仅获取专栏列表
message GetHelperColumnListOnlyReq
{
	optional uint32 os_type = 1; // EHelperOSType 专题的平台类型
}
// 删除指定专栏
message RemoveHelperColumnReq
{
	required int32 columnId = 1;
}

message RemoveHelperQuestionReq
{
	required int32 columnId = 1;
	repeated int32 ids = 2;
}

// 
message SetHelperQuestionReq
{
	required int32 columnId = 1;
	repeated HelperQuestion questions = 2;
}

message GetHelperQuestionReq
{
	required int32 columnId = 1;
	optional uint32 os_type = 2;
}

message GetHelperQuestionResp
{
	repeated HelperQuestion questions = 1;
}

// 获取置顶(热门)问题列表
message GetHelperHighLineQuestionListReq
{
	optional uint32 os_type = 1;
}

//恢复帮助栏目
message RecoverHelperColumnReq
{
	repeated uint32 column_id_list = 1;
}

//获取已经删除的栏目
message GetRemoveColumnResp
{
	repeated HelperColumn column_list = 1;
}


service Helper{
	option( tlvpickle.Magic ) = 15260;		// 服务监听端口号

	rpc GetVoiceBallHelperInfo( tlvpickle.SKBuiltinEmpty_PB  ) returns( GetVoiceBallHelperInfoResp ){
		option( tlvpickle.CmdID ) = 1;											// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
	}

	rpc SetVoiceBallHelperInfo( SetVoiceBallHelperInfoReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 2;											// 命令号
        option( tlvpickle.OptString ) = "d:t:m:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-d <code> -t <helperType> -m <mobileType>";	// 测试工具的命令号帮助
	}

	rpc RemoveVoiceBallHelper( RemoveVoiceBallHelperReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 3;											// 命令号
        option( tlvpickle.OptString ) = "l:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <code>";	// 测试工具的命令号帮助
	}
	
	// 获取专栏列表（不包括专栏 里面的问题列表）
	rpc GetHelperColumnList( GetHelperColumnListReq ) returns ( HelperColumnList ){
		option( tlvpickle.CmdID ) = 4;											// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
	}
	
	// 全量覆盖更新专栏信息列表 （不包括专栏里面的问题列表）
	rpc SetHelperColumnList( HelperColumnList ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 5;											// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
	}
	
	// 删除指定专栏
	rpc RemoveHelperColumn( RemoveHelperColumnReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 6;											// 命令号
        option( tlvpickle.OptString ) = "d:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-d <columnId>";	// 测试工具的命令号帮助
	}
	
	// 删除 指定专栏下的 问题
	rpc RemoveHelperQuestion( RemoveHelperQuestionReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 7;											// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
	}
	
	// 设置 指定专栏下的 问题
	rpc SetHelperQuestion( SetHelperQuestionReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 8;											// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
	}
	
	// 获取 指定专栏下的 问题列表
	rpc GetHelperQuestion( GetHelperQuestionReq ) returns ( GetHelperQuestionResp ){
		option( tlvpickle.CmdID ) = 9;											// 命令号
        option( tlvpickle.OptString ) = "d:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-d <columnId>";	// 测试工具的命令号帮助
	}
	
	// 仅获取专栏列表
	rpc GetHelperColumnListOnly( GetHelperColumnListOnlyReq ) returns ( HelperColumnList ){
		option( tlvpickle.CmdID ) = 10;											// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
	}
	
	// 获取置顶(热门)问题列表
	rpc GetHelperHighLineQuestionList( GetHelperHighLineQuestionListReq ) returns ( GetHelperQuestionResp ){
		option( tlvpickle.CmdID ) = 11;											// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
	}
	
	// 恢复专栏 
	rpc RecoverHelperColumn( RecoverHelperColumnReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 12;											// 命令号
        option( tlvpickle.OptString ) = "d:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-d <columnId>";	// 测试工具的命令号帮助
	}
	
	//  获取已经删除的专栏
	rpc GetRemoveColumn( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetRemoveColumnResp ){
		option( tlvpickle.CmdID ) = 13;											// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
	}


}
