syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package vipPrivilegeSvr;

//是否开启了VIP
message GetVipPrivilegeScoreLimitReq
{
   // required uint32 uid = 1;
}

message GetVipPrivilegeScoreLimitResp
{
    required uint32 score = 1;
}

//财富值变化
message OnRichChangeReq
{
    //required uint32 uid = 1;
    required uint32 old_rich = 1;
    required uint32 new_rich = 2;
}

message OnRichChangeResp
{

}

message VipLevelConfig
{
    required uint32 index = 1;
    required uint32 min_score = 2;
    required uint32 max_score = 3;
    //required bool status = 3;
}

message GetVipLevelConfigsReq
{
    //required uint32 uid = 1;
}

message GetVipLevelConfigsResp
{
    repeated VipLevelConfig vip_level_configs = 1;
}

//vip服务体系的特权
message Privilege
{
    required string id = 1;
    required string name = 2;
    required string status = 3;
    required string desc = 4;
    repeated string url_list = 5;
}

message GetVipPrivilegeListReq
{
}

message GetVipPrivilegeListResp 
{
    repeated Privilege privilege_list = 1;
}

enum VipKefuLevel
{
    VIP1 = 0;
    VIP2 = 1;
    VIP3 = 2;
}

enum VipOnlineStatus
{
    OFF = 0; //离线
    ON  = 1; //在线
}

message AddVipKefuReq
{
    required uint32 uid = 1;
    required uint32 level = 2; //enum VipKefuLevel
}

message AddVipKefuResp
{
}

message SetKefuOnlineStatusReq
{
    required uint32 uid = 1;
    required uint32 status = 2; //enum VipOnlineStatus
}

message SetKefuOnlineStatusResp
{
    
}

message GetKefuByUidReq
{
    //required uint32 uid = 1;
}

message GetKefuByUidResp
{
    required uint32 kefu_uid = 1;
}

message SetKefu2UidReq
{
    required uint32 uid = 1;
    required uint32 kefu_uid = 2;
}

message SetKefu2UidResp
{
    
}

service vipPrivilegeSvr {
    option( tlvpickle.Magic ) = 15679;      // 服务监听端口号

    rpc GetVipPrivilegeScoreLimit ( GetVipPrivilegeScoreLimitReq ) returns ( GetVipPrivilegeScoreLimitResp )
    {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc OnRichChange ( OnRichChangeReq ) returns ( OnRichChangeResp )
    {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:r:n";
        option( tlvpickle.Usage ) = "-u <uid> -r<rich> -n<new>";
    }

    rpc GetVipLevelConfigs ( GetVipLevelConfigsReq ) returns ( GetVipLevelConfigsResp )
    {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }
    
    rpc GetVipPrivilegeList ( GetVipPrivilegeListReq ) returns ( GetVipPrivilegeListResp )
    {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc AddVipKefu ( AddVipKefuReq ) returns ( AddVipKefuResp )
    {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "u:x:";
        option( tlvpickle.Usage ) = "-t <ttid> -x<level>";
    }

    rpc SetKefuOnlineStatus ( SetKefuOnlineStatusReq ) returns ( SetKefuOnlineStatusResp )
    {
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "u:x:";
        option( tlvpickle.Usage ) = "-t <ttid> -x<status>";
    }

    rpc SetKefu2Uid ( SetKefu2UidReq ) returns ( SetKefu2UidResp )
    {
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "u:x:";
        option( tlvpickle.Usage ) = "-u <uid> -x<kefu>";
    }

    rpc GetKefuByUid ( GetKefuByUidReq ) returns ( GetKefuByUidResp )
    {
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "u:x:";
        option( tlvpickle.Usage ) = "-u <uid> -x<kefu>";
    }
}