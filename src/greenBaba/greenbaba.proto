syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";


package greenBaba;

enum ENUM_TARGET_TYPE
{
	E_TARGET_TYPE_CHANNEL = 1;	// 对房间 
	E_TARGET_TYPE_USER = 2;	    // 对用户
	E_TARGET_TYPE_MUSIC = 3;    //音乐
	E_TARGET_TYPE_GROUP = 4;    //群组
	E_TARGET_TYPE_CHAT  = 5;    //聊天
    E_TARGET_TYPE_DRAW_GAME = 6; // 涂鸦
}

// 制裁类型
enum ENUM_SANCTION_OP_TYPE 
{
	E_SANCTION_OP_TYPE_NONE = 0;	            // 没有处理

	E_SANCTION_OP_TYPE_WARNING = 3;	    // 警告
	E_SANCTION_OP_TYPE_BANNED  = 4;	    // 封禁
	E_SANCTION_OP_TYPE_IGNORE  = 25;	// 忽略
}

// 制裁类型中 封禁情况下 的封禁类型
enum ENUM_BANNED_TYPE 
{
	E_BANNED_NONE  = 0;	            // 没有封禁
	E_BANNED_LOGIN = 1;	            // 禁止登录
	E_BANNED_CHANNEL = 2;	        // 禁止与房间相关的操作
}


message GreenBabaSanctionInfo
{
	required uint32 target_type = 1; // 制裁的目标类型 ENUM_TARGET_TYPE
	required uint32 id          = 2; // 根据 target_type 可以是 uid 或者 房间ID
	
	
	optional uint32 banned_type  = 3;    // 封禁的类型 ENUM_BANNED_TYPE 。 当且仅当 sanction_type = E_SANCTION_OP_TYPE_BANNED 时才有值
	optional uint32 remain_second   = 4; // 剩余制裁的时间 0 表示取消封禁
	
	optional uint32 sanction_type   = 5; // 制裁操作的类型 ENUM_SANCTION_OP_TYPE
	optional string sanction_reason = 6; // 制裁原因
}

// 设置制裁
message SetSanctionReq
{
	repeated GreenBabaSanctionInfo sanction_list = 1;	// 
	optional uint32 op_uid = 2;	// 
	optional string op_userinfo = 3;	// 
	optional uint32 sanction_obj = 4;					// 制裁对象是否 恶意举报		
}

message SetSanctionResp
{
}

// 获取制裁列表
// 获取指定ID 当前的封禁内容

message GetCurrBannedStatByIdReq
{
	required uint32 sanction_target_type = 1; // 制裁的目标类型 ENUM_TARGET_TYPE
	required uint32 req_id = 2;               // 根据 sanction_target_type 可以是 uid 或者 房间ID
}

message GetCurrBannedStatByIdResp
{
	repeated GreenBabaSanctionInfo banned_list = 1;	// 
}

message BatchGetCurrBannedStatByIdReq
{
	required uint32 sanction_target_type = 1; // 制裁的目标类型 ENUM_TARGET_TYPE
	repeated uint32 req_id_list = 2;               // 根据 sanction_target_type 可以是 uid 或者 房间ID
}

message CurrBannedStatInfo
{
    required uint32 id = 1;
    required uint32 sanction_target_type = 2;
    repeated GreenBabaSanctionInfo banned_list = 3;
}

message BatchGetCurrBannedStatByIdResp
{
	repeated CurrBannedStatInfo currbanned_list = 1;	// 
}


// 根据目标类型 获取当前的封禁列表
message GetBannedListByTargetTypeReq
{
	required uint32 sanction_target_type = 1; // 制裁的目标类型 ENUM_TARGET_TYPE

	required uint32 begin_idx  = 2;            // 
	required uint32 limit      = 3;            // 
}

message GetBannedListByTargetTypeResp
{
	repeated GreenBabaSanctionInfo banned_list = 1;	// 
	required uint32 total_size      = 2;            // 
}


enum ENUM_REPORT_TYPE {
  E_REPORT_PRIVACY = 0;         // 泄露隐私
  E_REPORT_PERSONAL_ATTACK = 1; // 人身攻击
  E_REPORT_PORNOGRAPHY = 2;     // 淫秽色情
  E_REPORT_AD = 3;              // 广告垃圾
  E_REPORT_SENSITIVE_INFO = 4;  // 敏感话题
  
  E_REPORT_TYPE_ALL  = 255;     // 全部类型
}

// 发举报
message SendReportReq
{
	required string op_tt_acc = 1;	// 
	optional uint32 target_channel =2;	// 
	optional uint32 target_uid = 3;	    // 
	optional uint32 report_type  = 4;	// ENUM_REPORT_TYPE
	optional string report_reason = 5;	// 
	optional string pic_url = 6;
	optional uint32 report_uid = 7;
}

message SendReportResp
{
}

// 举报
message BaseReportInfo
{
	required string op_tt_acc      = 1;	// 
	optional uint32 report_type    = 2; // ENUM_REPORT_TYPE
	optional string report_reason  = 3; // 
	optional uint32 report_uid	   = 4;
	optional string pic_url		   = 5;
	optional uint32 report_ts	   = 6;
	optional uint32 target_uid	   = 7;
}

// 组合的制裁信息
message ComplexSanctionInfo
{
	required uint32 target_id = 1;                 // 房间ID 或者 用户ID
	repeated BaseReportInfo report_base_list  = 2; // 举报信息的合集摘要 最多N条
	optional uint32 reprot_real_cnt           = 3; // 真正有多少举报条数
	optional uint32 sanction_op_type          = 4; // 如果这些举报被处理了，那么这里是处理的操作类型 ENUM_SANCTION_OP_TYPE , 0 表示未操作/未处理
	optional uint32 banned_op_type            = 5; // 如果sanction_op_type==E_SANCTION_OP_TYPE_BANNED 封禁操作的类型 ENUM_BANNED_TYPE
	optional uint32 banned_second             = 6; // 封禁的时间
	optional uint32 last_update_ts            = 7; // 最后更新时间 如果这些举报被处理了，这里是处理时间，如果是未处理的那就是最后添加时间
	optional string sanction_op_user          = 8; // 如果有封禁操作 那么这里是操作人
	optional string sanction_reason			  = 9; // 制裁原因
	
}

// 根据目标类型 遍历 举报处理的历史列表
message ScanAllReportPorcHistoryListReq
{
	required uint32 target_type = 1;    // 目标类型 ENUM_TARGET_TYPE
	required bool is_scan_waitproc = 2; // 是否是待处理的
	
	required uint32 page_id  = 3;  // 
	required uint32 page_cnt = 4;  // 
    optional uint32 begin_ts = 5;
    optional uint32 end_ts = 6; 
}

message ScanAllReportPorcHistoryListResp
{
	repeated ComplexSanctionInfo sanction_list = 1;
	optional uint32 all_count = 2;  // 
}


// 根据目标类型 和 目标ID 获取指定ID的 举报和处理统计
message GetSanctionStatByIdReq
{
	required uint32 target_type = 1; // 制裁的目标类型 ENUM_TARGET_TYPE
	required uint32 req_id = 2;      // 根据 target_type 可以是 uid 或者 房间ID
}
message GetSanctionStatByIdResp
{
	required uint32 curr_banned_type = 1;     	// 目标当前的封禁类型 ENUM_BANNED_TYPE
	optional uint32 remain_banned_second = 2; 	// 剩余的封禁时间
	
	optional uint32 report_stat_cnt = 3;  		// 被举报的总次数
	optional uint32 banned_stat_cnt = 4;  		// 被封禁的总次数
	optional uint32 warning_stat_cnt = 5; 		// 被警告的总次数
	optional uint32 false_report_cnt = 6; 		// 恶意举报次数

	// report user
	optional uint32 channel_stat_cnt = 7;		// 举报房间次数
	optional uint32 user_stat_cnt = 8;			// 举报用户次数

	optional uint32 dis_channel_stat_cnt = 9;	// 举报房间个数
	optional uint32 dis_user_stat_cnt = 10;		// 举报用户个数
}

// 根据目标类型 和 目标ID 遍历获取指定ID的 举报的历史列表
message  ScanSanctionHistoryListByIdReq
{
	required uint32 target_type = 1; // 制裁的目标类型 ENUM_TARGET_TYPE
	required uint32 req_id = 2;      // 根据 target_type 可以是 uid 或者 房间ID
	required bool is_scan_waitproc = 3; // 是否是待处理的
	
	required uint32 page_id  = 4;  // 
	required uint32 page_cnt = 5;  // 
}
message  ScanSanctionHistoryListByIdResp
{
	repeated ComplexSanctionInfo sanction_list = 1;
	optional uint32 all_count = 2;  // 
}

// 清理举报的历史列表
message  CleanHistoryRecordByIdReq
{
	repeated uint32 id_list = 1;        //
	required uint32 target_type = 2;    // 目标类型 ENUM_TARGET_TYPE	

}
message  CleanHistoryRecordByIdResp
{
}

// 房间白名单
message AddChannelWhiteListReq
{
	required uint32 channel_id=1;
}
message AddChannelWhiteListResp
{

}

message DelChannelFromWhiteListReq
{
	required uint32 channel_id=1;
}

message DelChannelFromWhiteListResp
{
}

message GetChannelWhiteListReq
{
}

message GetChannelWhiteListResp
{
	repeated uint32 channel_id_list=1;
}

//////  用户举报记录

message GetUserReportHistoryReq
{
	required uint32 uid = 1;
	required uint32 page_idx = 2;
	required uint32 page_limit = 3;
}
message GetUserReportHistoryResp
{
	repeated UserReportInfo user_report_list = 1;
}

message UserReportInfo
{
	required uint32 target_id = 1;			// 举报对象ID
	required uint32 target_type = 2;		// 举报对象类型 ENUM_TARGET_TYPE
	required string report_reason = 3;		// 举报原因
	required uint32 report_ts = 4;			// 举报时间
	optional string sanction_op_user = 5;	// 制裁人员
	optional uint32 oper_ts = 6;			// 制裁时间		
}
message UserReportList
{
	repeated UserReportInfo user_report_list =  1;
}

// 恶意举报
message MaliciousReportInfo
{
	required uint32 uid = 1;				// 恶意举报用户
	optional uint32 type = 2;				// 举报用户或房间
	optional uint32 sanction_type = 3;		// 制裁类型
	optional string sanction_op_user = 4;	// 制裁者
	optional uint32 banned_type = 5;		// 封禁类型
	optional uint32 banned_second = 6;		// 封禁时间
}
message MaliciousReportInfoList
{
	repeated MaliciousReportInfo info_list = 1;
}

message SendCommonReportReq
{
	required bytes report_event_bin = 1;
}

message SendCommonReportResp
{
}

message CheckUserIsImLimitReq
{
    required uint32 uid = 1;
}
message CheckUserIsImLimitResp
{
    required bool is_limit = 2;
}

// 频道禁言
message MuteChannelMemberReq {
	required uint32 channel_id = 1;
	repeated uint32 target_uids = 2;
}
message MuteChannelMemberResp {
	required uint32 channel_id = 1;
	repeated uint32 muted_uids = 2;
}
message UnmuteChannelMemberReq {
	required uint32 channel_id = 1;
	repeated uint32 target_uids = 2;
}
message UnmuteChannelMemberResp {
}

// 获取频道禁言成员列表
message GetChannelMuteListReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
}

// 房间成员
message ChannelMemberBaseInfo {
    required uint32 uid  = 1;         // 成员uid
    required uint32 ts   = 2;         // 时间
}

message GetChannelMuteListResp
{
	required uint32 channel_id = 1;
	repeated ChannelMemberBaseInfo mute_list = 2;
}

// 检查用户是否被禁言
message CheckUserIsMuteReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
}
message CheckUserIsMuteResp
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required bool is_mute = 3;
}

message CleanChannelMuteListReq
{
	required uint32 channel_id = 1;
}
message CleanChannelMuteListResp
{

}

message CheckUserKickoutFromChannelReq{
    required uint32 channel_id = 1;
    required uint32 uid = 2;
}
message CheckUserKickoutFromChannelResp{
    optional bool is_kicked = 1;
}

message KickoutChannelMemberReq {
	required uint32 channel_id = 1;
	repeated uint32 target_uids = 2;
	optional uint32 ban_duration = 3;
}
message KickoutChannelMemberResp {
	required uint32 channel_id = 1;
	repeated uint32 kickouted_uids = 2;
}

service greenbaba
{
	option( tlvpickle.Magic ) = 15592;

	// 对目标实施制裁
	rpc SetSanction(SetSanctionReq)returns( SetSanctionResp){
	option( tlvpickle.CmdID ) = 1;						
    option( tlvpickle.OptString ) = "t:x:o:b:s:j:";				
    option( tlvpickle.Usage ) = "-t <target type, 1channel 2user> -x <id> -o <sanction op type> -b <banned type> -s <second,0=del> -j <sanction_obj>";	
	}

	// 根据目标ID 获取 当前封禁状态列表
	rpc GetCurrBannedStatById( GetCurrBannedStatByIdReq)returns(GetCurrBannedStatByIdResp){
	option( tlvpickle.CmdID ) = 2;						
    option( tlvpickle.OptString ) = "t:x:";				
    option( tlvpickle.Usage ) = "-t <target type, 1channel 2user> -x <id>";				
	}

	
	// 根据制裁目标类型 获取 当前被封禁的列表
	rpc GetBannedListByTargetType(GetBannedListByTargetTypeReq)returns(GetBannedListByTargetTypeResp){
	option( tlvpickle.CmdID ) = 4;						
    option( tlvpickle.OptString ) = "t:p:l:";				
    option( tlvpickle.Usage ) = "-t <target type, 1channel 2user> -p <page idx> -l <count>";				
	}
	
	// 举报
	rpc SendReport( SendReportReq)returns(SendReportResp){
		option( tlvpickle.CmdID ) = 5;						
		option( tlvpickle.OptString ) = "x:s:u:t:r:p:";				
		option( tlvpickle.Usage ) = "-x <target channel ID > -s <report_uid> -u <target UID> -t <type> -r <reason> -p <pic_url>";				
	}
	
	// 根据目标类型 遍历 已经处理的历史列表(按时间排序)
	rpc ScanAllReportPorcHistoryList( ScanAllReportPorcHistoryListReq)returns(ScanAllReportPorcHistoryListResp){
		option( tlvpickle.CmdID ) = 6;						
		option( tlvpickle.OptString ) = "x:b:l:";				
		option( tlvpickle.Usage ) = "-x <target type > -b <page_idx> -l <page_cnt_limit>";				
	}

	// 根据目标类型 和 目标ID 获取指定ID的 举报和处理统计
	rpc GetSanctionStatById( GetSanctionStatByIdReq)returns(GetSanctionStatByIdResp){
		option( tlvpickle.CmdID ) = 8;						
		option( tlvpickle.OptString ) = "t:x:";				
		option( tlvpickle.Usage ) = "-t <target type, 1channel 2user> -x <id>";			
	}
	
	// 根据目标类型 和 目标ID 遍历获取指定ID的 历史处理列表
	rpc ScanSanctionHistoryListById( ScanSanctionHistoryListByIdReq)returns(ScanSanctionHistoryListByIdResp){
		option( tlvpickle.CmdID ) = 9;						
		option( tlvpickle.OptString ) = "t:x:b:l:";				
		option( tlvpickle.Usage ) = "-t <target type, 1channel 2user> -x <id>  -b <page_idx> -l <page_cnt_limit>";			
	}

	// 清除记录
	rpc CleanHistoryRecordById( CleanHistoryRecordByIdReq)returns(CleanHistoryRecordByIdResp){
		option( tlvpickle.CmdID ) = 10;						
		option( tlvpickle.OptString ) = "x:";				
		option( tlvpickle.Usage ) = "-x <id>";			
	}

	// 加白名单
	rpc AddChannelWhiteList( AddChannelWhiteListReq)returns(AddChannelWhiteListResp){
		option( tlvpickle.CmdID ) = 11;						
		option( tlvpickle.OptString ) = "x:";				
		option( tlvpickle.Usage ) = "-x <id>";			
	}

	// 删白名单
	rpc DelChannelFromWhiteList( DelChannelFromWhiteListReq)returns(DelChannelFromWhiteListResp){
		option( tlvpickle.CmdID ) = 12;						
		option( tlvpickle.OptString ) = "x:";				
		option( tlvpickle.Usage ) = "-x <id>";			
	}

	// 获取白名单
	rpc GetChannelWhiteList( GetChannelWhiteListReq)returns(GetChannelWhiteListResp){
		option( tlvpickle.CmdID ) = 13;						
		option( tlvpickle.OptString ) = "";				
		option( tlvpickle.Usage ) = "";			
	}

	// 获取用户举报记录
	rpc GetUserReportHistory( GetUserReportHistoryReq)returns(GetUserReportHistoryResp){
		option( tlvpickle.CmdID ) = 14;						
		option( tlvpickle.OptString ) = "u:";				
		option( tlvpickle.Usage ) = "-u <uid>";			
	}

	// 获取对话
	rpc SendCommonReport( SendCommonReportReq)returns(SendCommonReportResp){
		option( tlvpickle.CmdID ) = 18;						
		option( tlvpickle.OptString ) = "u:";				
		option( tlvpickle.Usage ) = "-u <uid>";
	}

	rpc BatchGetCurrBannedStatById( BatchGetCurrBannedStatByIdReq)returns(BatchGetCurrBannedStatByIdResp){
      option( tlvpickle.CmdID ) = 19;						
      option( tlvpickle.OptString ) = "t:x:";				
      option( tlvpickle.Usage ) = "-t <target type, 1channel 2user> -x <id,id2>";				
	}

	rpc CheckUserIsImLimit ( CheckUserIsImLimitReq )returns( CheckUserIsImLimitResp ){
      option( tlvpickle.CmdID ) = 20;						
      option( tlvpickle.OptString ) = "u:";				
      option( tlvpickle.Usage ) = "-u <uid>";				
	}

	// 新的简单禁言接口
    rpc MuteChannelMember( MuteChannelMemberReq ) returns ( MuteChannelMemberResp ){
		option( tlvpickle.CmdID ) = 21;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
    }
    rpc UnmuteChannelMember( UnmuteChannelMemberReq ) returns ( UnmuteChannelMemberResp ) {
		option( tlvpickle.CmdID ) = 22;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	rpc CheckUserIsMute( CheckUserIsMuteReq ) returns( CheckUserIsMuteResp ){
		option( tlvpickle.CmdID ) = 23;										
        option( tlvpickle.OptString ) = "u:x:";						
        option( tlvpickle.Usage ) = "-u <uid> -x <channel id>";
	}

	rpc GetChannelMuteList( GetChannelMuteListReq ) returns( GetChannelMuteListResp ){
		option( tlvpickle.CmdID ) = 24;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel id>";
	}

	rpc CleanChannelMuteList( CleanChannelMuteListReq ) returns( CleanChannelMuteListResp ){
		option( tlvpickle.CmdID ) = 25;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel id>";
	}

	//检测用户是否被踢
    rpc CheckUserKickoutFromChannel( CheckUserKickoutFromChannelReq ) returns ( CheckUserKickoutFromChannelResp ){
		option( tlvpickle.CmdID ) = 26;										
        option( tlvpickle.OptString ) = "x:u:";						
        option( tlvpickle.Usage ) = "-x <channel id> -u <target uid> ";
	}

	rpc KickoutChannelMember( KickoutChannelMemberReq ) returns ( KickoutChannelMemberResp ) {
		option( tlvpickle.CmdID ) = 27;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
    }

}
