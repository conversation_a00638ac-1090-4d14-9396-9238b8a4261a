syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Giftpkg;

enum CHANNEL {
	UNSPECIFIED = 0;	// 未指定
	UNIVERSAL = 1;		// 通用
	BAIDU = 2;			// 百度
	QIHOO = 3;			// 360
	SY8	= 4;			// 手游吧
	UC = 5;				// 九游
	XIAOMI = 6;			// 小米
}

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

//================================================================
// 用户获取礼包
//================================================================
message userFetchGiftPkgReq {
	required uint32 guild_id = 1;			// 公会id
	required uint32 gift_pkg_id = 2;		// 礼包id
	required uint32 fetch_time = 3;			// 领取时间
	required string device_id = 4;			// 设备号
}

message userFetchGiftPkgResp {
	required string gift_pkg_serial = 1;
}

// 获取活动礼包
message UserFetchActivityGiftPkgReq {
    required uint32 uid = 1;
    required uint32 gift_pkg_id = 2;
    required uint32 fetch_time = 3;
    required string device_id_hex = 4;
}

message UserFetchActivityGiftPkgResp {
	required string gift_pkg_serial = 1;
}

message CheckActivityGiftPkgFetchStatusReq {
    required uint32 uid = 1;
    required uint32 gift_pkg_id = 2;
    required string device_id_hex = 3;
}

message CheckActivityGiftPkgFetchStatusResp {
    required bool uid_fetched = 1;
    required bool device_fetched = 2;
    optional uint32 fetch_time = 3;
}

//================================================================
// 用户查看已经获取到的礼包序列号
//================================================================
message GetUserGiftPkgSerialReq {
	required uint32 gift_pkg_id = 1;
}

message GetUserGiftPkgSerialResp {
	required uint32 status = 1;				// 领取状态
	required uint32 op_time = 2;			// 操作时间
	required string serial = 3;				// 序列号
	required uint32 guild_id = 4;			// 对应的公会id
}

// 礼包结构
message stGiftPkg {
	required uint32 gift_pkg_id = 1;		// 礼包id
	required uint32 exchange_begintime = 2;	// 礼包兑换开始时间
	required uint32 exchange_endtime = 3;	// 礼包过期时间
	required uint32 game_id = 4;			// 游戏id
	required string intro = 5;				// 礼包介绍
	required string name = 6;				// 礼包名称
	required string content = 7;			// 礼包内容
	required string usage = 8;				// 使用方法
	required uint32 serial_total_num = 9;	// 礼包数
	required uint32 offered_num	= 10;		// 已提供礼包数
	required bool is_show	= 11;			// 是否显示
	optional uint32 source_guild = 12;		// 添加源(0:运营 >0: 公会)
	optional uint32 channel_id = 13;		// 渠道ID
}



// 礼包序列码结构
message stGiftPkgSerial {
	enum STATUS {
		NONE = 0;		// 未被领取
		FETCH = 1;		// 领取的礼包
		RAND = 2;		// 随机淘号
	}
	required stGiftPkg gift_pkg = 1;		// 对应的礼包
	required uint32 status = 2;				// 领取状态
	required uint32 op_time = 3;			// 操作时间
	required string serial = 4;				// 序列号
	required uint32 guild_id = 5;			// 对应的公会id
}

//================================================================
//查看礼包列表
//================================================================
message GetGiftPkgListReq {
	required uint32 offset = 1;				// 分页起始
	required uint32 size = 2;				// 分页大小
    optional uint32 source_guild = 3;		// 指定取公会礼包
    repeated uint32 channel_id_list = 4;	// 指定取渠道ID
	optional uint32 game_id = 5;			// 指定取某游戏的礼包
	optional bool only_visible = 6;			// 只查可见的礼包
}

message GetGiftPkgListResp {
	repeated stGiftPkg pkg_list = 1;
	required uint32 total = 2;
}

//================================================================
// 单查某个礼包
//================================================================
message GetGiftPkgReq {
	required uint32 gift_pkg_id = 1;
}

message GetGiftPkgResp {
	required stGiftPkg pkg = 1;
}



//================================================================
// 用户查看自己的礼包列表
//================================================================
message GetMyGiftPkgSerialListReq {
	required uint32 offset = 1;
	required uint32 size = 2;
}

message GetMyGiftPkgSerialListResp {
	repeated stGiftPkgSerial pkg_list = 1;
}

//================================================================
// 公会申请礼包
//================================================================
message GuildApplyGiftPkgReq {
	required uint32 guild_id = 1;
	required uint32 pkg_id = 2;
	required uint32 required_num = 3;
	required string apply_msg = 4;
}

message GuildApplyGiftPkgResp {
}

//================================================================
// 查礼包申请列表
//================================================================
message GetGiftPkgApplyListReq {
	required uint32 offset = 1;
	required uint32 size = 2;
}

message stGiftPkgApply {
	enum STATUS {
		APPLY = 0;		// 申请中
		ACCEPT = 1;		// 通过申请
		REJECT = 2;		// 拒绝
	}
	required uint32 guild_id = 1;
	required uint32 apply_id = 2;
	required uint32 pkg_id = 3;
	required uint32 apply_time = 4;
	required uint32 status = 5;
	required string apply_msg = 6;
	required uint32 offical_op_time = 7;
	required string offical_msg = 8;
	required uint32 want_num = 9;
	required uint32 offer_num = 10;
	required uint32 op_uid = 11;
}

message GetGiftPkgApplyListResp {
	repeated stGiftPkgApply apply_list = 1;
	required uint32 total = 2;
}

//================================================================
// 查礼包申请列表
//================================================================
message GetGuildApplyingListReq {
	required uint32 guild_id = 1;
}

message GetGuildApplyingListResp {
	repeated stGiftPkgApply apply_list = 1;
}

//================================================================
// 录入礼包
//================================================================
message TypeGiftPkgReq {
	required stGiftPkg pkg = 1;
	required string op_account = 2;
}

message TypeGiftPkgResp {
	required uint32 gift_pkg_id = 1;
}

//===============================================================
// 更新礼包
//===============================================================
message UpdateGiftPkgReq{
	required stGiftPkg pkg = 1;
	required string op_account = 2;
}

//===============================================================
// 上/下架礼包
//===============================================================
message UpdateGiftPkgShowStatusReq{
	required uint32 gift_pkg_id = 1;
	required bool is_show = 2;
	required string op_account = 3;
}

//================================================================
// 录入序列号
//================================================================
message TypeGiftPkgSerialReq {
	required uint32 gift_pkg_id = 1;
	repeated string serial_list = 2;
	required string op_account = 3;
}

message TypeGiftPkgSerialResp {
	required uint32 add_count = 1;
}

// 录入序列号并直接发给公会, 要求礼包的source必须是同一公会
message TypeGiftPkgSerialToGuildReq {
	required uint32 gift_pkg_id = 1;
	required uint32 guild_id = 2;
	repeated string serial_list = 3;
	required string op_account = 4;
}

message TypeGiftPkgSerialToGuildResp {
	required uint32 add_count = 1;
}

//================================================================
// 批量查礼包信息
//================================================================
message BatchGetGiftPkgReq {
	repeated uint32 gift_pkg_id = 1;
}

message BatchGetGiftPkgResp {
	repeated stGiftPkg pkg = 1;
}

// 红包领取流水
message RedPkgFetchRecord {
	required uint32 id = 1;
	required uint32 guild_id = 2;
	required uint32 red_pkg_id = 3;
	required uint32 fetch_uid = 4;
	required uint32 fetch_time = 5;
}





// 公会礼包结构
message stGuildGiftPkg {
	required stGiftPkg gift_pkg = 1;	// 对应的礼包
	required uint32 total_fetch_num = 2;	// 公会礼包领取数
	required uint32 total_num = 3;			// 公会礼包总数
	required uint32 storage_fetch_num = 4;	// 仓库已领取数
	required uint32 storage_total_num = 5;	// 仓库礼包总数
	required uint32 public_fetch_num = 6;	// 公共礼包获取数
	required uint32 public_total_num = 7;	// 公共礼包总数
	required uint32 taohao_count = 8;		// 淘号次数
	required uint32 apply_pass_time = 9;	// 最后一次申请通过的时间
	optional uint32 red_diamond_price = 10;			// 当前红钻价格
}

//================================================================
// 查看公会礼包列表
//================================================================
message GetGuildGiftPkgListReq {
	required uint32 guild_id = 1;
	required uint32 offset = 2;
	required uint32 size = 3;
	optional uint32 order_type = 4;
}

message GetGuildGiftPkgListResp {
	repeated stGuildGiftPkg pkg_list = 1;
	required uint32 total = 2;
}


//================================================================
// 根据游戏id查看公会礼包列表
//================================================================
message GetGuildPkgListByGameIdReq {
	required uint32 guild_id = 1;
	required uint32 game_id = 2;
	required uint32 offset = 3;
	required uint32 size = 4;
}

message GetGuildPkgListByGameIdResp {
	repeated stGuildGiftPkg pkg_list = 1;
	required uint32 total = 2;
}


//================================================================
// 运营通过申请
//================================================================
message PassGiftPkgApplyReq {
	required uint32 guild_id = 1;
	required uint32 apply_id = 2;
	required uint32 offer_num = 3;
	required string op_account = 4;
	optional string guild_name = 5;
}

message PassGiftPkgApplyResp {
}


//================================================================
// 运营拒绝申请
//================================================================
message RejectGiftPkgApplyReq {
	required uint32 guild_id = 1;
	required uint32 apply_id = 2;
	required string reject_reason = 3;
	required string op_account = 4;
	optional string guild_name = 5;
}

message RejectGiftPkgApplyResp {
}

//================================================================
// 查公会某一种礼包
//================================================================
message GetGuildPkgStatusReq {
	required uint32 guild_id = 1;
	required uint32 pkg_id = 2;
}

message GetGuildPkgStatusResp {
	required stGuildGiftPkg pkg = 1;
}

// 批量接口
message BatchGetGuildPkgStatusReq {
	required uint32 guild_id = 1;
	repeated uint32 pkg_id_list = 2;
}

message BatchGetGuildPkgStatusResp {
	repeated stGuildGiftPkg pkg_list = 1;
}

//================================================================
// 判断公会是否正在申请某种礼包
//================================================================
message CheckGuildPkgApplyingReq {
	required uint32 guild_id = 1;
	required uint32 gift_pkg_id = 2;
}

message CheckGuildPkgApplyingResp {
	required uint32 is_applying = 1;	// 0 : 未申请， 1 : 申请中
}

//================================================================
// 判断公会是否正在申请某种礼包
//================================================================
message GetGuildApplyHistoryReq {
	required uint32 guild_id = 1;
	required uint32 offset = 2;
	required uint32 size = 3;
}

message GetGuildApplyHistoryResp {
	repeated stGiftPkgApply apply_list = 1;
	required uint32 total = 2;
}

//================================================================
// 淘号
//================================================================
message TaohaoReq {
	required uint32 gift_pkg_id = 1;
	required uint32 guild_id = 2;
}

message TaohaoResp {
	required string serial_num = 1;
}

//================================================================
// 增加淘号次数
//================================================================
message TaohaoAddUsedCountReq {
	required uint32 gift_pkg_id = 1;
	required uint32 guild_id = 2;
	required string serial_num = 3;
}

message TaohaoAddUsedCountResp {
}

//================================================================
// 生成红包
//================================================================
message CreateRedpkgReq {
	required uint32 guild_id = 1;
	required uint32 gift_pkg_id = 2;
	required uint32 serial_total_num = 3;
}

message CreateRedpkgResp {
	required uint32 redpkg_id = 1;
}

//================================================================
// 领取红包
//================================================================
message FetchRedPkgReq {
	required uint32 guild_id = 1;
	required uint32 red_pkg_id = 2;
	required uint32 gift_pkg_id = 3;
}

message FetchRedPkgResp {
	required string serial_num = 1;
}

//================================================================
// 查红包领取记录
//================================================================
message GetRedPkgHistoryReq {
	required uint32 guild_id = 1;
	required uint32 red_pkg_id = 2;
	required uint32 offset = 3;
	required uint32 size = 4;
}

message GetRedPkgHistoryResp {
	repeated RedPkgFetchRecord record_list = 1;
}

//================================================================
// 查红包明细
//================================================================
message GetRedPkgDetailReq {
	required uint32 guild_id = 1;
	required uint32 red_pkg_id = 2;
}

message RedPkgDetail {
	required uint32 red_pkg_id = 1;
	required uint32 guild_id = 2;
	required uint32 gift_pkg_id = 3;
	required uint32 creator_uid = 4;
	required uint32 serial_fetch_num = 5;
	required uint32 serial_total_num = 6;
	required uint32 create_time = 7;
	required uint32 update_time = 8;
}

message GetRedPkgDetailResp {
	required RedPkgDetail red_pkg_detail = 1;
}

//======================================================================
// 给会公分发礼包
//======================================================================
message GiveGiftPkgToGuildReq{
	required uint32 gift_pkg_id = 1;
	required uint32 guild_id = 2;
	required uint32 offer_number = 3;
	required string op_account = 4;
	required string guild_name = 5;
}

//================================================================
// 查用户是否领取过某个红包
//================================================================
message CheckIfFetchRedpkgReq {
	required uint32 guild_id = 1;
	required uint32 red_pkg_id = 2;
}

message CheckIfFetchRedpkgResp {
	required bool is_fetch = 1;
}

//==================================================================
// 申请礼包包含未处理记录的公会列表
//==================================================================
message stGuildApplyTimePkg{
	required uint32 guild_id = 1;
	required uint32 last_apply_time = 2;
}

message GetApplyingGuildListReq {
	required uint32 guild_id = 1;
}

message GetApplyingGuildListResp {
	repeated stGuildApplyTimePkg guild_apply_time_list = 1 ;
}

message GetApplyedGuildListReq {
	required uint32 guild_id = 1;
}

message GetApplyedGuildListResp {
	repeated stGuildApplyTimePkg guild_apply_time_list = 1;
}

//================================================================
// 查询某个公会的礼包统计信息
//================================================================

message stGuildGiftPkgStatInfo {
    required uint32 total_count = 1;            // 礼包总数
    required uint32 total_fetched_count = 2;    // 已领取总数
    required uint32 storage_fetch_num = 3;		// 仓库已领取数
    required uint32 storage_total_num = 4;		// 仓库礼包总数
    required uint32 public_fetch_num = 5;		// 公共部分，已领取数
    required uint32 public_total_num = 6;		// 公共部分，礼包总数
}

// 某个公会的礼包统计数据(按游戏进行分组)
message stGuildGameGiftPkgStatInfo {
	required uint32 game_id = 1;				// 游戏id
	required uint32 total_count = 2;			// 礼包总数
	required uint32 total_fetched_count = 3;	// 已领取总数
	optional uint32 public_fetch_num = 4;		// 公共部分，已领取数
    optional uint32 public_total_num = 5;		// 公共部分，礼包总数

}

message GetGuildGiftPkgStatInfoReq {
    required uint32 guild_id = 1;
}

message GetGuildGiftPkgStatInfoResp {
    required stGuildGiftPkgStatInfo stat_info = 1;		// 总计
    repeated stGuildGameGiftPkgStatInfo game_stat_list = 2;	// 按游戏分类统计
}


//=================================================================
// 未分配给公会的礼包序列号数量
//=================================================================
message CountRestGiftPkgSerialReq{
	required uint32 game_id = 1;
}

message CountRestGiftPkgSerialResp{
	required uint32 total = 2;
}

//=================================================================
// 根据申请ID，查申请记录
//=================================================================
message GetApplyReq {
	required uint32 apply_id = 1;
}

message GetApplyResp {
	required stGiftPkgApply apply = 1;
}

//=================================================================
// 查后台操作历史记录
//=================================================================
message GetGiftpkgOpHistoryReq {
	required uint32 record_begintime = 1;
	required uint32 record_endtime = 2;
	required uint32 offset = 3;
	required uint32 size = 4;
	required string op_account = 5;
}

// 礼包操作记录
message GiftpkgOpRecord {
	required uint32 op_time = 1;	// 操作时间
	required string op_account = 2;	// 操作者帐号
	required string op_content = 3;	// 操作内容
	required uint32 op_type = 4;	// 操作类型
}

message GetGiftpkgOpHistoryResp {
	repeated GiftpkgOpRecord records = 1;	// 记录
	required uint32 total = 2;				// 总数
}

//==================================================================
// 申请礼包包含未处理记录的公会列表
//==================================================================
message GetDeviceLastFetchReq {
	required string device_id = 1;
}

message GetDeviceLastFetchResp {
	required uint32 uid = 1;
	required uint32 last_fetch_time = 2;
	required uint32 gift_pkg_id = 3;
}

//==================================================================
// 查看公会最后申请某个礼包官方的处理时间
//==================================================================
message GetOfficalLastOpTimeReq {
	required uint32 guild_id = 1;
	required uint32 giftpkg_id = 2;
}

message GetOfficalLastOpTimeResp {
	required uint32 last_op_time = 1;
}

//==================================================================
// 礼包删除
//==================================================================
message DeleteGuildSourceGiftPkgReq {
	required uint32 giftpkg_id = 1;
	required uint32 guild_id = 2;
	required string op_account = 3;
}

message DeleteGuildSourceGiftPkgResp {
}

//==================================================================
// 锁定分配礼包序列号给用户
//==================================================================
message LockSerialForUserToPayReq {
	required uint32 guild_id = 1;
	required uint32 giftpkg_id = 2;
	required string orderid = 3;
}

message LockSerialForUserToPayResp {
}

//==================================================================
// 扣费成功，分配序列号给用户
//==================================================================
message RealFetchSerialForUserReq {
	required uint32 guild_id = 1;
	required uint32 giftpkg_id = 2;
	required string orderid = 3;
}

message RealFetchSerialForUserResp {
	required string serial_num = 1;
}

//==================================================================
// 扣费失败，释放序列号，回复成公会可领取的状态
//==================================================================
message ReleaseSerialForGuildReq {
	required uint32 guild_id = 1;
	required uint32 giftpkg_id = 2;
	required string orderid = 3;
}

message ReleaseSerialForGuildResp {
}

//==================================================================
// 设置公会礼包的价格
//==================================================================
message SetGuildPkgRedDiamondPriceReq {
	required uint32 guild_id = 1;
	required uint32 giftpkg_id = 2;
	required uint32 red_diamond_cost = 3;
}

message SetGuildPkgRedDiamondPriceResp {
}


//================================================================
// 用户查看已经获取到的礼包序列号
//================================================================
message GetUserPkgSerialListNewByIdReq {
	required uint32 gift_pkg_id = 1;
}

message GetUserPkgSerialListNewByIdResp {
	repeated stGiftPkgSerial serial_list = 1;
}

//================================================================
// 根据 公会(可同时查多个) 查 某些游戏或所有游戏 的礼包总数
//================================================================
message BatchGetGiftPackageStatInfoReq {
	repeated uint32 guild_id_list = 1;
	repeated uint32 game_id_list = 2;	// 为空表示统计所有游戏的礼包总量
}

message stGuildGameGiftPackageStatInfo {
	required uint32 guild_id = 1;
	required uint32 game_id = 2;	// 0表示统计所有游戏的礼包总量
	required stGuildGiftPkgStatInfo stat_info = 3;
}

message BatchGetGiftPackageStatInfoResp {
	repeated stGuildGameGiftPackageStatInfo stat_list = 1;
}

message FetchActGiftPkgCollectionReq{
	required uint32 uid = 1;
	required uint32 game_id = 2;
	required string device_id = 3;
}

message FetchActGiftPkgCollectionResp{
	repeated string gift_pkg_serial = 1;
}

message GetActGiftCollcetionFetStatusReq{
	required uint32 uid = 1;
	required uint32 game_id = 2;
	required string device_id = 3;
}

message GetActGiftCollcetionFetStatusResp{
	required uint32 fetch_status = 1;	//0:no fetch; 1:has fetched
}

message GetActGiftPkgCollectionConfReq
{
	required uint32 game_id = 1;
}

message GetActGiftPkgCollectionConfResp
{
	required string pkg_id_list =1;
	required string pic_url = 2;
}

service Giftpkg {
	option( tlvpickle.Magic ) = 14970;		// 服务监听端口号

	rpc userFetchGiftPkg( userFetchGiftPkgReq ) returns( userFetchGiftPkgResp ){
		option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "u:g:p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guildid> -p<pkg_id>";		// 测试工具的命令号帮助
	}
	rpc GetMyGiftPkgSerialList( GetMyGiftPkgSerialListReq ) returns( GetMyGiftPkgSerialListResp ){
		option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";		// 测试工具的命令号帮助
	}
	rpc GuildApplyGiftPkg( GuildApplyGiftPkgReq ) returns( GuildApplyGiftPkgResp ){
		option( tlvpickle.CmdID ) = 3;										// 命令号
        option( tlvpickle.OptString ) = "u:g:p:r:a:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guildid> -p <pkg_id> -r <required_num> -a <apply_msg>";		// 测试工具的命令号帮助
	}
	rpc GetGiftPkgApplyList( GetGiftPkgApplyListReq ) returns( GetGiftPkgApplyListResp ){
		option( tlvpickle.CmdID ) = 4;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";		// 测试工具的命令号帮助
	}
	rpc TypeGiftPkg( TypeGiftPkgReq ) returns ( TypeGiftPkgResp ) {
		option( tlvpickle.CmdID ) = 5;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";		// 测试工具的命令号帮助
	}
	rpc GetGuildGiftPkgList( GetGuildGiftPkgListReq ) returns ( GetGuildGiftPkgListResp ) {
		option( tlvpickle.CmdID ) = 6;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";		// 测试工具的命令号帮助
	}
	rpc PassGiftPkgApply( PassGiftPkgApplyReq ) returns ( PassGiftPkgApplyResp ) {
		option( tlvpickle.CmdID ) = 7;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";		// 测试工具的命令号帮助
	}
	rpc RejectGiftPkgApply( RejectGiftPkgApplyReq ) returns ( RejectGiftPkgApplyResp ) {
		option( tlvpickle.CmdID ) = 8;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";		// 测试工具的命令号帮助
	}
	rpc TypeGiftPkgSerial( TypeGiftPkgSerialReq ) returns ( TypeGiftPkgSerialResp ) {
		option( tlvpickle.CmdID ) = 9;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";		// 测试工具的命令号帮助
	}

	rpc GetGiftPkgList(GetGiftPkgListReq) returns (GetGiftPkgListResp){
		option( tlvpickle.CmdID ) = 10;										// 命令号
        option( tlvpickle.OptString ) = "g:o:l:s:e:";
        option( tlvpickle.Usage ) = "-g <game id> -o <offset> -l <limit> -s <source_guild_id> -e <channel_id_list>";
	}

	rpc UpdateGiftPkg(UpdateGiftPkgReq) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 11;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
	}

	rpc GetGuildApplyingList(GetGuildApplyingListReq) returns ( GetGuildApplyingListResp ) {
		option( tlvpickle.CmdID ) = 12;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id>";
	}

	rpc GetGiftPkg( GetGiftPkgReq ) returns ( GetGiftPkgResp ) {
		option( tlvpickle.CmdID ) = 13;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <gift_pkg_id>";
	}

	rpc GetGuildPkgStatus( GetGuildPkgStatusReq ) returns ( GetGuildPkgStatusResp ) {
		option( tlvpickle.CmdID ) = 14;										// 命令号
        option( tlvpickle.OptString ) = "g:p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -p <pkg_id>";
	}

	rpc GetUserGiftPkgSerial( GetUserGiftPkgSerialReq ) returns ( GetUserGiftPkgSerialResp ) {
		option( tlvpickle.CmdID ) = 15;										// 命令号
        option( tlvpickle.OptString ) = "p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <pkg_id>";
	}

	rpc CheckGuildPkgApplying( CheckGuildPkgApplyingReq ) returns ( CheckGuildPkgApplyingResp ) {
		option( tlvpickle.CmdID ) = 16;										// 命令号
        option( tlvpickle.OptString ) = "g:p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -p <pkg_id>";
	}

	rpc GetGuildApplyHistory( GetGuildApplyHistoryReq ) returns ( GetGuildApplyHistoryResp ) {
		option( tlvpickle.CmdID ) = 17;										// 命令号
        option( tlvpickle.OptString ) = "g:o:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -o <offset> -s <size>";
	}

	rpc BatchGetGiftPkg( BatchGetGiftPkgReq ) returns ( BatchGetGiftPkgResp ) {
		option( tlvpickle.CmdID ) = 18;								// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
	}

	rpc Taohao( TaohaoReq ) returns ( TaohaoResp ) {
		option( tlvpickle.CmdID ) = 19;								// 命令号
        option( tlvpickle.OptString ) = "g:p:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -p <pkg_id>";
	}

	rpc CreateRedpkg( CreateRedpkgReq ) returns ( CreateRedpkgResp ) {
		option( tlvpickle.CmdID ) = 20;								// 命令号
        option( tlvpickle.OptString ) = "g:p:s:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -p <pkg_id> -s <serial_total_num>";
	}

	rpc FetchRedPkg( FetchRedPkgReq ) returns ( FetchRedPkgResp ) {
		option( tlvpickle.CmdID ) = 21;								// 命令号
        option( tlvpickle.OptString ) = "g:p:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -p <pkg_id>";
	}

	rpc GetRedPkgHistory( GetRedPkgHistoryReq ) returns ( GetRedPkgHistoryResp ) {
		option( tlvpickle.CmdID ) = 22;								// 命令号
        option( tlvpickle.OptString ) = "g:r:o:s:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -r <red_pkg_id> -o <offset> -s <size>";
	}

	rpc TaohaoAddUsedCount( TaohaoAddUsedCountReq ) returns ( TaohaoAddUsedCountResp ) {
		option( tlvpickle.CmdID ) = 23;								// 命令号
        option( tlvpickle.OptString ) = "p:g:s:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -p <pkg_id> -s <serial_num>";
	}

	rpc GetRedPkgDetail( GetRedPkgDetailReq ) returns ( GetRedPkgDetailResp ) {
		option( tlvpickle.CmdID ) = 24;								// 命令号
        option( tlvpickle.OptString ) = "p:g:s:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -r <red_pkg_id>";
	}

	rpc UpdateGiftPkgShowStatus( UpdateGiftPkgShowStatusReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 25;								// 命令号
        option( tlvpickle.OptString ) = "p:s:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <gift package id> -s <is show>";

	}

	rpc GiveGiftPkgToGuild( GiveGiftPkgToGuildReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 26;								// 命令号
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
	}

	rpc CheckIfFetchRedpkg( CheckIfFetchRedpkgReq ) returns ( CheckIfFetchRedpkgResp ) {
		option( tlvpickle.CmdID ) = 28;								// 命令号
        option( tlvpickle.OptString ) = "u:g:r:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -r <red_pkg_id>";
	}

	rpc GetApplyingGuildList ( GetApplyingGuildListReq ) returns ( GetApplyingGuildListResp){
		option( tlvpickle.CmdID ) = 29;								// 命令号
        option( tlvpickle.OptString ) = "";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
	}

	rpc GetApplyedGuildList ( GetApplyedGuildListReq ) returns ( GetApplyedGuildListResp){
		option( tlvpickle.CmdID ) = 30;								// 命令号
        option( tlvpickle.OptString ) = "g:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id>";
	}

    rpc GetGuildGiftPkgStatInfo ( GetGuildGiftPkgStatInfoReq ) returns ( GetGuildGiftPkgStatInfoResp ) {
        option( tlvpickle.CmdID ) = 31;								// 命令号
        option( tlvpickle.OptString ) = "g:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id>";
    }

    rpc CountRestGiftPkgSerial ( CountRestGiftPkgSerialReq ) returns ( CountRestGiftPkgSerialResp ){
    	option( tlvpickle.CmdID ) = 32;								// 命令号
        option( tlvpickle.OptString ) = "g:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game id>";
    }

    rpc GetApply( GetApplyReq ) returns ( GetApplyResp ) {
    	option( tlvpickle.CmdID ) = 33;								// 命令号
        option( tlvpickle.OptString ) = "a:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-a <apply id>";
    }

    rpc GetGiftpkgOpHistory( GetGiftpkgOpHistoryReq ) returns ( GetGiftpkgOpHistoryResp ) {
    	option( tlvpickle.CmdID ) = 34;								// 命令号
        option( tlvpickle.OptString ) = "a:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-b <begintime> -e <endtime> -o <offset> -s <size>";
    }

    rpc GetDeviceLastFetch( GetDeviceLastFetchReq ) returns ( GetDeviceLastFetchResp ) {
    	option( tlvpickle.CmdID ) = 35;								// 命令号
        option( tlvpickle.OptString ) = "d:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-d <device_id>";
    }

    rpc GetOfficalLastOpTime( GetOfficalLastOpTimeReq ) returns ( GetOfficalLastOpTimeResp ) {
    	option( tlvpickle.CmdID ) = 36;								// 命令号
        option( tlvpickle.OptString ) = "g:p:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -p <pkg_id>";
    }

    rpc TypeGiftPkgSerialToGuild( TypeGiftPkgSerialToGuildReq ) returns( TypeGiftPkgSerialToGuildResp ) {
    	option( tlvpickle.CmdID ) = 37;								        // 命令号
        option( tlvpickle.OptString ) = "g:p:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild> -p <pkg_id> -s <cdk_file>";
    }

    rpc BatchGetGuildPkgStatus( BatchGetGuildPkgStatusReq ) returns( BatchGetGuildPkgStatusResp ) {
    	option( tlvpickle.CmdID ) = 38;
    	option( tlvpickle.OptString ) = "g:p:";
    	option( tlvpickle.Usage ) = "-g <guild_id> -p <pkg_id1,pkg_id2,...>";
    }

    rpc DeleteGuildSourceGiftPkg( DeleteGuildSourceGiftPkgReq ) returns( DeleteGuildSourceGiftPkgResp ) {
    	option( tlvpickle.CmdID ) = 39;
    	option( tlvpickle.OptString ) = "g:p:";
    	option( tlvpickle.Usage ) = "-g <guild_id> -p <pkg_id>";
    }

    rpc LockSerialForUserToPay( LockSerialForUserToPayReq ) returns ( LockSerialForUserToPayResp ) {
    	option( tlvpickle.CmdID ) = 40;
    	option( tlvpickle.OptString ) = "";
    	option( tlvpickle.Usage ) = "";
    }

    rpc RealFetchSerialForUser( RealFetchSerialForUserReq ) returns ( RealFetchSerialForUserResp ) {
    	option( tlvpickle.CmdID ) = 41;
    	option( tlvpickle.OptString ) = "";
    	option( tlvpickle.Usage ) = "";
    }

    rpc ReleaseSerialForGuild( ReleaseSerialForGuildReq ) returns ( ReleaseSerialForGuildResp ) {
    	option( tlvpickle.CmdID ) = 42;
    	option( tlvpickle.OptString ) = "";
    	option( tlvpickle.Usage ) = "";
    }

    rpc SetGuildPkgRedDiamondPrice( SetGuildPkgRedDiamondPriceReq ) returns ( SetGuildPkgRedDiamondPriceResp ) {
    	option( tlvpickle.CmdID ) = 43;
    	option( tlvpickle.OptString ) = "";
    	option( tlvpickle.Usage ) = "";
    }

    rpc GetUserPkgSerialListNewById( GetUserPkgSerialListNewByIdReq ) returns ( GetUserPkgSerialListNewByIdResp ) {
    	option( tlvpickle.CmdID ) = 44;
    	option( tlvpickle.OptString ) = "";
    	option( tlvpickle.Usage ) = "";
    }

    rpc GetGuildPkgListByGameId( GetGuildPkgListByGameIdReq ) returns ( GetGuildPkgListByGameIdResp ) {
    	option( tlvpickle.CmdID ) = 45;
    	option( tlvpickle.OptString ) = "g:a:o:s:";
    	option( tlvpickle.Usage ) = "-g <guild_id> -a <game_id> -o <offset> -s <size>";
    }

 	rpc BatchGetGiftPackageStatInfo( BatchGetGiftPackageStatInfoReq ) returns ( BatchGetGiftPackageStatInfoResp ) {
 		option( tlvpickle.CmdID ) = 46;
    	option( tlvpickle.OptString ) = "g:a:";
    	option( tlvpickle.Usage ) = "-g <guild_id_list> -a <game_id_list>";
 	}

 	rpc UserFetchActivityGiftPkg( UserFetchActivityGiftPkgReq ) returns ( UserFetchActivityGiftPkgResp ) {
 		option( tlvpickle.CmdID ) = 47;
    	option( tlvpickle.OptString ) = "u:g:d:";
    	option( tlvpickle.Usage ) = "-u <uid> -g <giftpkg_id> -d <device_id>";
 	}

    rpc CheckActivityGiftPkgFetchStatus( CheckActivityGiftPkgFetchStatusReq ) returns ( CheckActivityGiftPkgFetchStatusResp ) {
        option( tlvpickle.CmdID ) = 48;
        option( tlvpickle.OptString ) = "u:g:d:";
        option( tlvpickle.Usage ) = "-u <uid> -g <giftpkg_id> -d <device_id>";
    }
    
 	rpc FetchActGiftPkgCollection( FetchActGiftPkgCollectionReq ) returns ( FetchActGiftPkgCollectionResp ) {
 		option( tlvpickle.CmdID ) = 49;
    	option( tlvpickle.OptString ) = "u:g:d:";
    	option( tlvpickle.Usage ) = "-u <uid> -g <game_id> -d <device_id>";
    }
    
 	rpc GetActGiftCollcetionFetStatus( GetActGiftCollcetionFetStatusReq ) returns ( GetActGiftCollcetionFetStatusResp ) {
 		option( tlvpickle.CmdID ) = 50;
    	option( tlvpickle.OptString ) = "u:g:d:";
    	option( tlvpickle.Usage ) = "-u <uid> -g <game_id> -d <device_id>";
    }

  	rpc GetActGiftPkgCollectionConf( GetActGiftPkgCollectionConfReq ) returns ( GetActGiftPkgCollectionConfResp ) {
 		option( tlvpickle.CmdID ) = 51;
    	option( tlvpickle.OptString ) = "g:";
    	option( tlvpickle.Usage ) = "-g <game_id>";
    }

 	rpc UserFetchActivityGiftPkgNoLimit( UserFetchActivityGiftPkgReq ) returns ( UserFetchActivityGiftPkgResp ) {
 		option( tlvpickle.CmdID ) = 52;
    	option( tlvpickle.OptString ) = "u:g:d:";
    	option( tlvpickle.Usage ) = "-u <uid> -g <giftpkg_id> -d <device_id>";
 	}

 	rpc FetchActGiftPkgCollectionNoLimit( FetchActGiftPkgCollectionReq ) returns ( FetchActGiftPkgCollectionResp ) {
 		option( tlvpickle.CmdID ) = 53;
    	option( tlvpickle.OptString ) = "u:g:d:";
    	option( tlvpickle.Usage ) = "-u <uid> -g <game_id> -d <device_id>";
    } 	       
}
