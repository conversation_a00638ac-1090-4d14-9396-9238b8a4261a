syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package backlistsvr;

message OperationRiskInfo
{
	optional string riskLevel = 1;
	optional string message = 2;
	optional uint32 score = 3;
}

message AccountRiskInfo
{
	required uint32 uid = 1;
	optional uint32 score = 2; //分越高越危险
	optional string riskGrade = 3;
	optional string riskType = 4;
	optional string riskReason = 5;
	optional string detail = 6;
	optional string requestId = 7; 
	optional string description = 8;
	optional string model = 9;
	optional string hits = 10; //
	optional string groupId = 11;//团伙
	optional uint32 groupSize = 12; //团伙成员数量
}

message BatchGetAccountRiskInfoByTypeReq
{
	required uint32 count = 1;
	required uint32 sex   = 2;
	//required string backType = 3;
	//optional uint32 minScore = 4;
	//optional uint32 maxScore = 5;
}

message BatchGetAccountRiskInfoByTypeResp
{
	repeated AccountRiskInfo back_list = 1;
}

message IsBackUserReq
{
}

message IsBackUserResp
{
	optional bool is_back_user = 1;
}

message BackUserInfo
{
	required uint32 uid = 1;
	required uint32 sex = 2;
}

message AddBackUserReq
{
	repeated BackUserInfo back_user_list = 1;
}

message AddBackUserResp
{
}

service backlistsvr {
	option( tlvpickle.Magic ) = 15661;		// 服务监听端口号

	rpc BatchGetAccountRiskInfoList ( BatchGetAccountRiskInfoByTypeReq ) returns ( BatchGetAccountRiskInfoByTypeResp )
	{
		option( tlvpickle.CmdID ) = 1;
		option( tlvpickle.OptString ) = "u:b:l:r:";
		option( tlvpickle.Usage ) = "-b <backType> -l <minScore> -r<maxScore>";
	}

	rpc IsBackUser ( IsBackUserReq ) returns ( IsBackUserResp )
	{
		option( tlvpickle.CmdID ) = 2;
		option( tlvpickle.OptString ) = "u:x:s:r:";
		option( tlvpickle.Usage ) = "-u <uid>";
	}

	rpc AddBackUser ( AddBackUserReq ) returns ( AddBackUserResp )
	{
		option( tlvpickle.CmdID ) = 3;
		option( tlvpickle.OptString ) = "u:s:";
		option( tlvpickle.Usage ) = "-u <uid> -s<sex>";
	}
}