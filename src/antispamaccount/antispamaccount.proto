syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package AntispamAccount;


// get tokenid by uid. create tokenid for uid if it not exist
message GetTokenidByUidReq
{
	required uint32 uid = 1;
	optional bool auto_create = 2;
}

message GetTokenidByUidResp
{
	required string tokenid = 1;
}

// get tokenid by uid. create tokenid for uid if it not exist
message GetUidByTokenidReq
{
	required string tokenid = 1;
}

message GetUidByTokenidResp
{
	required uint32 uid = 1;
}

message UserTokenidInfo
{
	required uint32 uid = 1;
	optional string tokenid = 2;
}

message GetTokenidInfoByUidsReq
{
	repeated uint32 uid_list = 1;
}

message GetTokenidInfoByUidsResp
{
	repeated UserTokenidInfo tokenid_info_list = 1;
}

enum BannedFeatureType
{
	NONE = 0; 
	TT_PUBLISH_ROOM = 1;
	TT_ROOM_PUBLISH_MSG = 2; // 用户在房间内禁止发公屏消息
	TT_ROOM_CLICK_MIC = 3;   // 用户在房间内禁止上麦
	TT_ENTER_ENTERTAINMENT_ROOM = 4; //禁止进入娱乐房(包括 公会公开房、语音直播房)
	TT_ENTER_NOT_ENTERTAINMENT_ROOM = 5; //禁止进入约玩房(包括 除了 公会公开房、语音直播房 之外的其他房间)
	TT_ROOM_CLICK_MIC_V2 = 6; // 用户在房间内禁止上麦
    TT_IM_NOT_SEEN  = 7; // IM场景下违规内容自见
}

message BannedFeature
{
	required uint32 banned_feature_type = 1;
	required uint32 expire_at = 2;
}

message GetUserBannedFeatureListReq
{
	required uint32 uid = 1;
}

message GetUserBannedFeatureListResp
{
	repeated BannedFeature banned_feature_list = 1;
}

message BanUserFeatureReq
{
	required uint32 uid = 1;
	required uint32 banned_feature_type = 2;
	optional uint32 expire_at = 3;
}

message BanUserFeatureResp
{
}

message UnBanUserFeatureReq
{
	required uint32 uid = 1;
	required uint32 banned_feature_type = 2;
}

message UnBanUserFeatureResp
{
}

message AddShumeiBlackUserReq
{
	required uint32 uid = 1;
	optional string reason = 2;
}

message AddShumeiBlackUserResp
{
}

service AntispamAccount {
	option( tlvpickle.Magic ) = 15077;		// 服务监听端口号

	rpc GetTokenidByUid( GetTokenidByUidReq ) returns( GetTokenidByUidResp ){
		option( tlvpickle.CmdID ) = 1;											// 命令号
        option( tlvpickle.OptString ) = "u:b:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -b <b_auto_create(0/1)>";	// 测试工具的命令号帮助
	}

	rpc GetUidByTokenid( GetUidByTokenidReq ) returns( GetUidByTokenidResp ){
		option( tlvpickle.CmdID ) = 2;											// 命令号
        option( tlvpickle.OptString ) = "s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-s <tokenid>";	// 测试工具的命令号帮助
	}
	
	rpc GetTokenidInfoByUids( GetTokenidInfoByUidsReq ) returns ( GetTokenidInfoByUidsResp ) {
		option( tlvpickle.CmdID ) = 3;											// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid1,uid2>";	// 测试工具的命令号帮助
	}
	
	rpc GetUserBannedFeatureList( GetUserBannedFeatureListReq ) returns ( GetUserBannedFeatureListResp ) {
		option( tlvpickle.CmdID ) = 4;											// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";	// 测试工具的命令号帮助
	}
	
	rpc BanUserFeature( BanUserFeatureReq ) returns ( BanUserFeatureResp ) {
		option( tlvpickle.CmdID ) = 5;											// 命令号
        option( tlvpickle.OptString ) = "u:b:e:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -b <ban_type> -e <expire_at>";	// 测试工具的命令号帮助
	}
	
	rpc UnBanUserFeature( UnBanUserFeatureReq ) returns ( UnBanUserFeatureResp ) {
		option( tlvpickle.CmdID ) = 6;											// 命令号
        option( tlvpickle.OptString ) = "u:b:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -b <ban_type>";	// 测试工具的命令号帮助
	}
	
	rpc AddShumeiBlackUser( AddShumeiBlackUserReq ) returns ( AddShumeiBlackUserResp ) {
		option( tlvpickle.CmdID ) = 7;											// 命令号
        option( tlvpickle.OptString ) = "u:b:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> ";	// 测试工具的命令号帮助
	}
	
}
