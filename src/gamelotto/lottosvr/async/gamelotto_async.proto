syntax="proto2";


package gamelotto.async;

message AsyncCommands {
    enum Values {
        GrantVoucher = 1;
        SendAward    = 2;    //通用发奖事件
        NewUserAward  = 3;    
    }
}

message GrantVoucherTaskData {
    required string order_id = 1;           // 订单号
    required uint32 uid = 2;                // 用户ID
    required uint32 lianyun_game_id = 3;    // 联运游戏ID
    required uint32 amount = 4;             // 金额
    required string effective_date = 5;     // 生效时间, yyyy-MM-dd, 忽略, 由handler发奖时生成
    required string expiry_date = 6;        // 过期时间, yyyy-MM-dd, 忽略, 由handler发奖时生成
    optional bytes device_id = 7;           // 领取的设备号
    optional string tt_channel_id = 8;      // 领取的渠道号
    optional string game_channel_id = 9;    // 游戏渠道ID
    optional string game_cp_id = 10;        // 游戏包ID
    optional string lotto_name = 11;        // 抽奖名字(类型)
    optional uint32 game_classify = 12;     // 游戏类型 1可任意充值 2不可任意充值
}

message SendAwardData{
    required uint32 uid = 1;
    required uint32 item_type = 2;
    required uint32 amount = 3;
    optional string order_id = 4;
    optional string order_desc = 5;
    optional bytes device_id = 6;
}

message AwardNewUserChanceData{
    required uint32 uid = 1;
    required uint32 type = 2;
    required uint32 gameId = 3;
    required string lotto_name = 4;
    required uint32 chance = 5;
}