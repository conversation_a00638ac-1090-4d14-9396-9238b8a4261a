syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package gamelotto;

enum LottoItemType {
	voucher        = 1;   //代金券
    giftpkg        = 2;   // 礼包
    reddiamond     = 3;  //红钻
    rechargcard    = 4;   //充值卡
    medal          = 5;   //勋章
    precharge      = 6;     //首冲号
    activecode     = 7;     //激活码
    combine        = 8;     //奖品合集
    virtualitem    = 9;     //非TT虚拟物品
    realityitem    = 10;    //实体物品
    fragment_wangzhe = 11;  //王者荣耀碎片
    empty_item     = 12;   //无奖品

    updateitem     = 101;   //更新mission sync
}

enum LottoChanceType {
    follow_cle        = 1;   //关注游戏圈
    share_act         = 2;   //分享活动
    visit_act         = 3;   //访问活动页面
}


message ReportGameLoginReq {
    required uint32 uid = 1;
    required uint32 lianyun_game_id = 2;
}

message LottoChance {
	required uint32 lotto_id = 1;
    required string lotto_name = 2;
    required uint32 chances = 3;
	optional string cp_id = 4;
	optional uint32 chance_used = 5;
	optional uint32 free_chances = 6;       // 剩余的抽奖次数
	optional uint32 reddiamond_chances = 7; // 剩余的红钻抽奖次数
    optional uint32 update_time = 8;        //抽奖次数更新时间
}

message GetUserLottoChancesReq {
    required uint32 uid = 1;
    required uint32 lianyun_game_id = 2;
	optional string lotto_name = 3;
	optional string device_id = 4;
}

message GetUserLottoChancesResp {
    repeated LottoChance chance_list = 1;
    optional bool is_lotto_active = 2; // 如果传了lotto_name, 这里会返回对应的lotto是否开启
}

message UserDoLottryReq {
    required uint32 uid = 1;
    optional uint32 lotto_id = 2;
    optional uint32 ly_game_id = 3;
    optional string lotto_name = 4;
    required uint32 lotto_count = 5;
    optional string device_id = 6;
    optional string tt_channel_id = 7;     // TT的渠道包ID
    optional bool pay_chance = 8; // 支付红钻抽奖品
    optional uint32 ip_addr = 9;
    optional uint32 pay_uid = 10; // 扣红钻的uid
	optional string sdk_device_id = 11;
}
message LottoResult {
	required uint32 item_id = 1;
	required uint32 item_count = 2;
	required uint32 item_type = 3;
	required uint32 detail = 4;
    optional string item_name = 5;
    optional string pic_url = 6;
    optional uint32 lotto_time = 7; //中奖时间
}
message UserDoLottryResp {
	repeated LottoResult lotto_result_list = 1;
	optional uint32 chances = 2;
	optional uint32 need_red_diamond = 3;
	optional uint32 reddiamond_chances = 4;
}

message AddLottryReq {
	optional uint32 lotto_id = 1;
	required uint32 item_id = 2;
	required uint32 item_type = 3;
	optional int32 item_count = 4;
	optional double hit_rate = 5;
	optional uint32 amount = 6;

	optional uint32 ly_game_id = 7;
	optional string lotto_name = 8;

    optional string item_name = 9;
    optional string pic_url = 10;
}

message GetLottryItemReq {
	required uint32 lianyun_game_id = 1;
	optional string lotto_name = 2;
	optional string cp_id = 3;
}

message LottoItemExtend{
    optional string item_name   = 1;
    optional string pic_url     = 2;
}

message LottoItem {
	required uint32 item_id = 1;
	required int32 item_count = 2;
	required int32 item_type = 3;
	required uint32 item_detail = 4;
    optional string item_name   = 5;
    optional string pic_url     = 6;
    optional double hit_rate = 7;
    optional uint32 valid = 8;
}

message LottoItemSet {
	required uint32 lotto_id = 1;
	required string lotto_name = 2;
	repeated LottoItem lotto_item_list = 3;
}

message GetLottryItemResp {
	repeated LottoItemSet lottom_item_set_list = 1;
}

message GetContinousLoginDaysReq {
	required uint32 uid = 1;
	required uint32 ly_game_id = 2;
}

message GetContinousLoginDaysResp {
	required uint32 continous_login_days = 1;
}

message BatchGetLottoChanceReq {
	repeated uint32 uid_list = 1;
    repeated string lotto_names = 2;    // 如果填空, 则查询所有的
}

message LottoGameChance {
	required uint32 ly_game_id = 1;
	repeated LottoChance lotto_chance_list = 2;
}

message LottoChanceSet {
	required uint32 uid = 1;
	repeated LottoGameChance lotto_game_chance_list = 2;
}

message BatchGetLottoChanceResp {
	repeated LottoChanceSet lotto_chance_set_list = 1;
}

message GetUserContinuousLoginDaysReq {
    required uint32 uid = 1;
    required uint32 ly_game_id = 2;
}

message GetUserContinuousLoginDaysResp {
    required uint32 days = 1;
}

message LoginLottoConfig {
	optional uint32 max_accumulate_chance = 1; // 累积次数
	optional uint32 default_chance = 2; // 每日免费抽几次
	optional uint32 pay_chance = 3; // 每日红钻额外抽几次
	optional uint32 red_diamond_consume = 4; // 额外抽奖消耗多少红钻
}

message AddLoginLotttoConfigReq {
	required uint32 ly_game_id = 1;
	required string cp_id = 2;
	required LoginLottoConfig config = 3;
}

message GetLoginLotttoConfigReq {
	required uint32 ly_game_id = 1;
	required string cp_id = 2;
}
message GetLoginLotttoConfigResp {
	required LoginLottoConfig config = 1;
}

message GetLottoIdReq {
    required uint32 ly_game_id = 1;
    optional string channel_id = 2;
    required string lotto_name = 3;
}

message GetLottoIdResp {
    optional uint32 lotto_id = 1;
}

message AddVoucherReq {
	required string voucher_id = 1;
	required uint32 uid = 2;
	required uint32 ly_game_id = 3;
	required uint32 send_time = 4;
}

message UpdateVoucherReq{
	required string voucher_id = 1;
	required uint32 use_time = 2;
	required string order_id = 3;
}

message LottoPreorderActInfo{
    required uint32 game_id = 1;
    required string lotto_name = 2;
    required string banner_url = 3;
    required string rules = 4;
    optional string roulette = 5;
    optional string btn_follow = 6;
    optional string bg_color = 7;  //ex.“#13277e”, “#060219”
    optional string award_intro = 8;
}

message GetLottoPreorderActReq{
    required uint32 game_id = 1;
    required string lotto_name = 2;
}

message VoucherItem{
	required uint32 amount = 1;
	required uint32 count = 2;
}

message CreateLottoReq{
    required uint32 game_id = 1;
    required string lotto_name = 2;
	optional string cp_id = 3;
	optional uint32 classic = 4;//游戏类型 1-可任意充值/2-不可
	
	/* 充值抽奖才需要 */
	optional uint32 recharge_fee_threshold = 5;		// 现金充值多少就可以有一次抽奖机会 单位分
	optional uint32 rebate_ratio_thousandth = 6;	// 现金充值中可以用来做抽奖返利的比例 千分比
	optional uint32 min_lottery_gift_value = 7;		// 最小必中中奖值 单位分
}

message CreateLottoResp{
    required uint32 lotto_id = 1;
}

message EnableLottoReq{
    required uint32 game_id = 1;
	required bool enable = 2;
    optional string lotto_name = 3;
	optional string cp_id = 4;
}

message AddFirstLottoItemReq{
	required uint32 ly_game_id = 1;
	required string cp_id = 2;
	repeated VoucherItem voucher_list = 3;
}

message GetLottoInfoByNameReq {
	required string lotto_name = 1;
	required uint32 classic = 2;
}

message BaseLottoInfo {
	required uint32 ly_game_id = 1;
	required string cp_id = 2;
	required uint32 create_time = 3;
	required bool active = 4;
	optional string game_name = 5;
}

message GetLottoInfoByNameResp {
	repeated BaseLottoInfo lotto_info_list = 1;
}

message GetHasFirstLoginLottoReq {
	required uint32 uid = 1;
	required uint32 ly_game_id = 2;
	required uint32 ip_addr = 3;
	required string device_id = 4;
	optional string sdk_device_id = 5;
}

message GetHasFirstLoginLottoResp {
	required bool has_chance = 1;
	repeated VoucherItem voucher_list = 2;
}


message SetLottoTypeChanceReq{
    required uint32 uid         = 1;
    required uint32 game_id     = 2;
    required string lotto_name  = 3; 
    required uint32 type        = 4;
    required uint32 chance      = 5;
}

message GetLottoTypeChanceReq{
    required uint32 uid         = 1;
    required uint32 game_id     = 2;
    required string lotto_name  = 3; 
    required uint32 type        = 4;
}

message GetLottoTypeChanceResp{
    required uint32 chance = 1;
    optional uint32 update_time = 2;
}

message AddLottoChanceReq{
    required uint32 uid = 1;
    required uint32 game_id = 2;
    required string lotto_name = 3;
    required uint32 chance = 4;
    optional bool force = 5;    //true(default)-强制更新，false-每天只更新一次
}

//***************新用户抽奖 start****************//

// 设置用户剩余抽奖次数
message SetLottoTypeChanceV2Req{
    required uint32 uid         = 1;
    required uint32 game_id     = 2;
    required string lotto_name  = 3; 
    required uint32 type        = 4;
    required uint32 chance      = 5;
    optional bool   isReset     = 6;
}

message GetLottoTypeChanceV2Req{
    required uint32 uid         = 1;
    required uint32 game_id     = 2;
    required string lotto_name  = 3; 
}

message LottoTypeWithChance
{
    required uint32 type = 1;
    required uint32 chance = 2;
}

message LottoTypeWithStep
{
    required uint32 type=1;
    required uint32 step=2;
}

message GetLottoTypeChanceV2Resp{
    repeated LottoTypeWithChance type_chance_list = 1;  
    repeated LottoTypeWithStep type_step_list = 2;
    optional uint32 update_time = 3;
}

// 给特定类型加抽奖次数
message AwardLottoTypeChanceReq{
    required uint32 uid         = 1;
    required uint32 game_id     = 2;
    required string lotto_name  = 3; 
    required uint32 type        = 4;
    required uint32 chance      = 5;
}

//// config
enum DailyLimitType{
    EVERYDAY_TYPE = 0;      // 每天默认抽奖奖励次数类型
    KH_CHANNEL_TYPE = 1;    // 开黑房抽奖奖励限制次数类型
    SHARE_TYPE = 2;         // 分享抽奖奖励限制次数类型
    INVITE_FRIEND = 3;      // 邀请好友抽奖奖励限制次数类型
    ONLINE_TIME = 4;        // 在线抽奖奖励限制次数类型
}

message SetTypeChanceConfigReq{
    required uint32 game_id = 1;
    required string lotto_name = 2;
    required uint32 type = 3;           // DailyLimitType
    required uint32 limitChance = 4;
}
//***************新用户抽奖 end*****************//


message AddUserLottoItemReq{
    required uint32 uid = 1;
    required uint32 game_id = 2;
    required string lotto_name = 3;
    required string device_id = 4;
    required uint32 item_id = 5;
    optional bool just_add = 6;//无视库存和次数直接发奖
}

message GetUserLottoItemByUidReq{
    required uint32 uid = 1;
    required uint32 game_id = 2;
    required string lotto_name = 3;
    optional uint32 desc = 4;   //倒序结果
}

message GetUserLottoItemByDeviceReq{
    required string device_id = 1;
    required uint32 game_id = 2;
    required string lotto_name = 3;
}

message GetUserLottoItemResp{
    repeated LottoResult lotto_result_list = 1;
}

message GetLottoItemHistoryReq{
    required uint32 game_id = 1;
    required string lotto_name = 2;
    required uint32 limit = 3;   
}

message LottoHistoryItem{
    required uint32 uid = 1;
    required uint32 item_id = 2;
    required string item_name = 3;
}

message GetLottoItemHistoryResp{
    repeated LottoHistoryItem item_list = 1;
}

message GetLottoActiveStatusReq{
	required uint32 ly_game_id = 1;
	required string cp_id = 2;
	required string lotto_name = 3;
}

message GetLottoActiveStatusResp{
	required uint32 active_status = 1;	// 0.not active 	1.active
	optional uint32 active_ts = 2;	    // if active==true 返回激活的时间
}

message GetUserCpIdByLyGameIdReq{
	required uint32 uid = 1;
	required uint32 ly_game_id = 2;
}

message GetUserCpIdByLyGameIdResp{
	required string cp_id = 1;
}

/************充值抽奖V2************/

message LottoChanceV2 {
	required uint32 ly_game_id = 1;
    required string pkt_channel = 2;
    repeated uint32 overdue_ts_list = 3;
	optional uint32 accumulate_fee = 4;			// 没有转换为抽奖机会的累计充值额 单位分
	optional uint32 recharge_fee_threshold = 5;	// 现金充值多少就可以有一次抽奖机会 单位分
	required bool 	active_status = 6;
	optional uint32 max_voucher_value = 7;		// 可抽奖的代金券最大值 单位分
}

message UserLottoChanceV2 {
	required uint32 uid = 1;
    repeated LottoChanceV2 lotto_chance_list = 2;
}

message BatchGetUserRechargeLottoChancesReq {
    repeated uint32 uid_list = 1;
	optional bool all_recharge_chances = 2; // 是否需要返回所有充值抽奖数据 包括曾经充过值但是当前没有抽奖机会的数据 如果为false则只返回充值机会大于0的数据
}

message BatchGetUserRechargeLottoChancesResp {
    repeated UserLottoChanceV2 chance_list = 1;
}

message GetUserRechargeLottoChanceReq {
    required uint32 uid = 1;
	required uint32 ly_game_id = 2;
}

message GetUserRechargeLottoChanceResp {
    required LottoChanceV2 lotto_chance = 1;
}

//清理mysql的旧数据
message CleanExpiredRecordsReq {
    required uint32 before_timestamp = 1;
}

message RechargeLottoParam {
	required uint32 ly_game_id = 1;
	required string pkt_channel = 2;
	optional uint32 recharge_fee_threshold = 3;		// 现金充值多少就可以有一次抽奖机会 单位分
	optional uint32 rebate_ratio_thousandth = 4;	// 现金充值中可以用来做抽奖返利的比例 千分比
	optional uint32 min_lottery_gift_value = 5;		// 最小必中中奖值 单位分
}

message UpdateRechargeLottoParamReq {
	required uint32 ly_game_id = 1;
	required string pkt_channel = 2;
	optional uint32 recharge_fee_threshold = 3;		// 现金充值多少就可以有一次抽奖机会 单位分
	optional uint32 rebate_ratio_thousandth = 4;	// 现金充值中可以用来做抽奖返利的比例 千分比
	optional uint32 min_lottery_gift_value = 5;		// 最小必中中奖值 单位分
}

message GetRechargeLottoParamReq {
	required uint32 ly_game_id = 1;	// 0.获取所有游戏
	optional string pkt_channel = 2;
}

message GetRechargeLottoParamResp {
	repeated RechargeLottoParam param_list = 1;
}

message BatchGetRechargeLottoParamReq {
	repeated uint32 ly_game_id_list = 1;
}

message BatchGetRechargeLottoParamResp {
	repeated RechargeLottoParam param_list = 1;
}

message AddLottoRechargeFeeReq {
	required uint32 uid = 1;
	required uint32 ly_game_id = 2;
	required string pkt_channel = 3;
	required string order_id = 4;       // 充值订单ID 用于去重
	required uint32 recharge_value = 5; // 有效充值金额 单位分
}

message GetPublicLottoRecordReq {
	required uint32 limit = 1;
}

message RewardRecord {
	required uint32 uid = 1;
	required uint32 amount = 2;
	optional uint32 timestamp = 3;
}

message GetPublicLottoRecordResp {
	repeated RewardRecord record_list = 1;
}

/************获取中奖纪录************/
message GetWinningListReq {
	required int32 lotto_id = 1;
	required uint32 limit = 2;
	required bool fill_by_other = 3;
}

message GetWinningListResp{
	required uint32 winner = 1;
	required string item_name = 2;
}
/************获取中奖纪录************/


/************公共奖池************/
message DelPublicLottryReq{
	required int32 lotto_id = 1;
	required int32 item_id = 2;
}


message LottryInfo {
	required uint32 item_id = 1;
	required uint32 item_type = 2;
	optional int32 item_count = 3;
	optional double hit_rate = 4;
	optional uint32 amount = 5;

	optional uint32 ly_game_id = 6;
	optional string lotto_name = 7;

    optional string item_name = 8;
    optional string pic_url = 9;

	optional string channel_id = 10;
}


message GetPublicLottryResp{
	repeated LottryInfo lottry_info_list = 1;
}

/************公共奖池************/

message SubmitPreLottoItemReq{
    required uint32 uid = 1;
    required uint32 game_id = 2;
    required string lotto_name = 3;
    required uint32 item_id = 4;
    required string device_id = 5;
    required uint32 valid = 6;  // 1-valid, 0-invalid
}

service gamelotto {

    option( tlvpickle.Magic ) = 15380;

    rpc ReportGameLogin ( ReportGameLoginReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 1;
 		option( tlvpickle.OptString ) = "u:g:";
 		option( tlvpickle.Usage ) = "-u <uid> -g <lianyun_game_id>";
 	}

    rpc GetUserLottoChances( GetUserLottoChancesReq ) returns ( GetUserLottoChancesResp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:g:n:";
        option( tlvpickle.Usage ) = "-u <uid> -g <lianyun_game_id> -n <lotto_name>";
    }

	rpc UserDoLottry( UserDoLottryReq ) returns ( UserDoLottryResp ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:g:n:e:";
        option( tlvpickle.Usage ) = "-u <uid> -g <ly_game_id> -n <lotto_name> -e <device_id>";
    }

	rpc AddLottry( AddLottryReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "l:i:t:o:r:m:g:n:d:p:";
        option( tlvpickle.Usage ) = "-l <lotto_id> -i <item_id> -t<item_type> -o<item_count> -r<rate> -m<amount> -g<ly_game_id> -n<lotto_name> -d<item_desc> -p<item_pic>";
    }

	rpc GetLottryItem( GetLottryItemReq ) returns ( GetLottryItemResp ) {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "g:n:x:";
        option( tlvpickle.Usage ) = "-g<ly_game_id> -n<lotto_name> -x<cp_id>";
    }

	rpc GetContinousLoginDays( GetContinousLoginDaysReq ) returns ( GetContinousLoginDaysResp ) {
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "u:g:";
        option( tlvpickle.Usage ) = "-u <uid> -g <lianyun_game_id>";
    }

	rpc BatchGetLottoChance( BatchGetLottoChanceReq ) returns ( BatchGetLottoChanceResp ) {
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc GetLottoId( GetLottoIdReq ) returns ( GetLottoIdResp ) {
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "g:k:n:";
        option( tlvpickle.Usage ) = "-g <game_id> -k <channel_id> -n <lotto_name>";
    }

	rpc AddVoucher( AddVoucherReq ) returns ( tlvpickle.SKBuiltinEmpty_PB  ) {
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "v:u:g:t:";
        option( tlvpickle.Usage ) = "-v<voucher_id> -u<uid> -g <game_id> -t <send_time>";
    }

	rpc UpdateVoucher( UpdateVoucherReq ) returns ( tlvpickle.SKBuiltinEmpty_PB  ) {
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "v:t:o:";
        option( tlvpickle.Usage ) = "-v<voucher_id> -t<use_time> -o<order_id>";
    }

    rpc AddLottoPreorderAct( LottoPreorderActInfo ) returns ( tlvpickle.SKBuiltinEmpty_PB  ) {
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "g:n:b:r:w:l:a:";
        option( tlvpickle.Usage ) = "-g<game_id> -n<lotto_name> -b<banner_url> -r<rules_url> -w<btn_follow> -l<color> -a<award_intro>";
    }

    rpc GetLottoPreorderAct( GetLottoPreorderActReq ) returns ( LottoPreorderActInfo  ) {
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "g:n:";
        option( tlvpickle.Usage ) = "-g <game_id> -n <lotto_name>";
    }

    rpc CreateLotto(CreateLottoReq) returns(
        CreateLottoResp) {
        option( tlvpickle.CmdID ) = 13;
        option( tlvpickle.OptString ) = "g:n:x:t:";
        option( tlvpickle.Usage ) = "-g <game_id> -n <lotto_name> -x <cp_id> -t <classic>";
    }

    rpc SetLottoTypeChance( SetLottoTypeChanceReq ) returns ( tlvpickle.SKBuiltinEmpty_PB  ) {
        option( tlvpickle.CmdID ) = 14;
        option( tlvpickle.OptString ) = "u:g:n:t:a:";
        option( tlvpickle.Usage ) = "-u<uid> -g<game_id> -n<lotto_name> -t<type> -a<chance>";
    }

    rpc GetLottoTypeChance( GetLottoTypeChanceReq ) returns ( GetLottoTypeChanceResp  ) {
        option( tlvpickle.CmdID ) = 15;
        option( tlvpickle.OptString ) = "u:g:n:t:";
        option( tlvpickle.Usage ) = "-u<uid> -g<game_id> -n<lotto_name> -t<type>";
    }

    rpc AddLottoChance( AddLottoChanceReq ) returns ( tlvpickle.SKBuiltinEmpty_PB  ) {
        option( tlvpickle.CmdID ) = 16;
        option( tlvpickle.OptString ) = "u:g:n:a:";
        option( tlvpickle.Usage ) = "-u<uid> -g<game_id> -n<lotto_name> -a<chance>";
    }

    rpc UserDoPreorderLottry( UserDoLottryReq ) returns ( UserDoLottryResp ) {
        option( tlvpickle.CmdID ) = 17;
        option( tlvpickle.OptString ) = "u:g:n:e:";
        option( tlvpickle.Usage ) = "-u <uid> -g<game_id> -n<lotto_name> -e <device_id>";
    }

    rpc AddUserLottoItem( AddUserLottoItemReq ) returns ( tlvpickle.SKBuiltinEmpty_PB  ) {
        option( tlvpickle.CmdID ) = 18;
        option( tlvpickle.OptString ) = "u:g:n:i:d:";
        option( tlvpickle.Usage ) = "-u<uid> -g<game_id> -n<lotto_name> -i<itme_id> -d<device_id>";
    }

    rpc GetUserLottoItemByUid( GetUserLottoItemByUidReq ) returns ( GetUserLottoItemResp  ) {
        option( tlvpickle.CmdID ) = 19;
        option( tlvpickle.OptString ) = "u:g:n:";
        option( tlvpickle.Usage ) = "-u<uid> -g<game_id> -n<lotto_name>";
    }

    rpc GetUserLottoItemByDevice( GetUserLottoItemByDeviceReq ) returns ( GetUserLottoItemResp  ) {
        option( tlvpickle.CmdID ) = 20;
        option( tlvpickle.OptString ) = "d:g:n:";
        option( tlvpickle.Usage ) = "-d<device_id> -g<game_id> -n<lotto_name>";
    }
          
    rpc GetLottoItemHistory( GetLottoItemHistoryReq ) returns ( GetLottoItemHistoryResp  ) {
            option( tlvpickle.CmdID ) = 21;
            option( tlvpickle.OptString ) = "g:n:l:";
            option( tlvpickle.Usage ) = "-g<game_id> -n<lotto_name> -l<limit>";
    }

    rpc GetLottryAllItem( GetLottryItemReq ) returns ( GetLottryItemResp ) {
        option( tlvpickle.CmdID ) = 22;
        option( tlvpickle.OptString ) = "g:n:";
        option( tlvpickle.Usage ) = "-g<ly_game_id> -n<lotto_name>";
    }    
	
	rpc BatchGetUserRechargeLottoChances( BatchGetUserRechargeLottoChancesReq ) returns ( BatchGetUserRechargeLottoChancesResp ) {
        option( tlvpickle.CmdID ) = 23;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u<uid>";
    }
	
	rpc CleanExpiredRecords( CleanExpiredRecordsReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 24;
        option( tlvpickle.OptString ) = "t";
        option( tlvpickle.Usage ) = "-t <timestamp, 0 means current_timestamp>";
    }

	rpc AddLoginLotttoConfig( AddLoginLotttoConfigReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 25;
        option( tlvpickle.OptString ) = "g:x:m:d:p:r:";
        option( tlvpickle.Usage ) = "-g <ly_game_id> -x <cp_id> -m <max_chance> -d <default_chance> -p <pay_chance> -r <red_diamond_consume>";
    }
	
	rpc GetUserRechargeLottoChance( GetUserRechargeLottoChanceReq ) returns ( GetUserRechargeLottoChanceResp ) {
        option( tlvpickle.CmdID ) = 26;
        option( tlvpickle.OptString ) = "u:g:";
        option( tlvpickle.Usage ) = "-u<uid> -g<ly_game_id>";
    }
	
	rpc UpdateRechargeLottoParam( UpdateRechargeLottoParamReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 27;
        option( tlvpickle.OptString ) = "g:p:a:r:m:";
        option( tlvpickle.Usage ) = "-g<ly_game_id> -p<pkt_channel> -a<recharge_fee_threshold> -r<rebate_ratio_thousandth> -m<min_lottery_gift_value>";
    }
	
	rpc GetRechargeLottoParam( GetRechargeLottoParamReq ) returns ( GetRechargeLottoParamResp ) {
        option( tlvpickle.CmdID ) = 29;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g<ly_game_id>";
    }
	
	rpc BatchGetRechargeLottoParam( BatchGetRechargeLottoParamReq ) returns ( BatchGetRechargeLottoParamResp ) {
        option( tlvpickle.CmdID ) = 30;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g<ly_game_id>";
    }
	
	rpc AddLottoRechargeFee( AddLottoRechargeFeeReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 31;
        option( tlvpickle.OptString ) = "u:g:p:o:n:";
        option( tlvpickle.Usage ) = "-u <uid> -g<ly_game_id> -p<pkt_channel> -o<order_id> -n<recharge_value_penny>";
    }
	
	rpc GetUserCpIdByLyGameId( GetUserCpIdByLyGameIdReq ) returns ( GetUserCpIdByLyGameIdResp ) {
        option( tlvpickle.CmdID ) = 48;
        option( tlvpickle.OptString ) = "u:g:";
        option( tlvpickle.Usage ) = "-u <uid> -g <ly_game_id>";
    }
	
	rpc GetLottoActiveStatus( GetLottoActiveStatusReq ) returns ( GetLottoActiveStatusResp ) {
        option( tlvpickle.CmdID ) = 49;
        option( tlvpickle.OptString ) = "g:x:n:";
        option( tlvpickle.Usage ) = "-g <ly_game_id> -x <cp_id> -n <lotto_name>";
    }

	rpc GetLoginLotttoConfig( GetLoginLotttoConfigReq ) returns ( GetLoginLotttoConfigResp ) {
        option( tlvpickle.CmdID ) = 50;
        option( tlvpickle.OptString ) = "g:x:";
        option( tlvpickle.Usage ) = "-g <ly_game_id> -x <cp_id>";
    }
	rpc EnableLotto( EnableLottoReq ) returns ( tlvpickle.SKBuiltinEmpty_PB  ) {
        option( tlvpickle.CmdID ) = 51;
        option( tlvpickle.OptString ) = "g:e:n:x:";
        option( tlvpickle.Usage ) = "-g <ly_game_id> -e <enable> -n <lotto_name> -x <cp_id>";
    }

	rpc AddFirstLottoItem( AddFirstLottoItemReq ) returns ( tlvpickle.SKBuiltinEmpty_PB  ) {
        option( tlvpickle.CmdID ) = 52;
        option( tlvpickle.OptString ) = "g:x:a:n:";
        option( tlvpickle.Usage ) = "-g <ly_game_id> -x <cp_id> -a <amount> -n <num>";
    }

	rpc GetLottoInfoByName( GetLottoInfoByNameReq ) returns ( GetLottoInfoByNameResp ) {
        option( tlvpickle.CmdID ) = 53;
        option( tlvpickle.OptString ) = "n:x:";
        option( tlvpickle.Usage ) = "-n <name> -x <classic>";
    }
	
	rpc GetHasFirstLoginLotto(  GetHasFirstLoginLottoReq ) returns ( GetHasFirstLoginLottoResp ) {
        option( tlvpickle.CmdID ) = 54;
        option( tlvpickle.OptString ) = "u:g:i:d:";
        option( tlvpickle.Usage ) = "-u<uid> -g<game_id> -i<ipaddr> -d<device_id>";
    }

	rpc GetPublicLottoRecord(  GetPublicLottoRecordReq ) returns ( GetPublicLottoRecordResp ) {
        option( tlvpickle.CmdID ) = 55;
        option( tlvpickle.OptString ) = "l:";
        option( tlvpickle.Usage ) = "-l<limit>";
    }

    rpc UserDoActLottry( UserDoLottryReq ) returns ( UserDoLottryResp ) {
        option( tlvpickle.CmdID ) = 56;
        option( tlvpickle.OptString ) = "u:g:n:e:";
        option( tlvpickle.Usage ) = "-u <uid> -g<game_id> -n<lotto_name> -e <device_id>";
    }	
	
	rpc GetWinningList( GetWinningListReq ) returns ( GetWinningListResp ) {
        option( tlvpickle.CmdID ) = 100;
        option( tlvpickle.OptString ) = "l:s:f:";
        option( tlvpickle.Usage ) = "-l<lotto_id> -s<limit> -f<fill_by_other>";
    }
	
	rpc DelPublicLottry( DelPublicLottryReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 101;
        option( tlvpickle.OptString ) = "l:i";
        option( tlvpickle.Usage ) = "-l<lotto_id> -i<item_id>";
    }

	rpc GetPublicLottry( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetPublicLottryResp ) {
        option( tlvpickle.CmdID ) = 102;
        option( tlvpickle.OptString ) = "l:";
        option( tlvpickle.Usage ) = "-l<lotto_id>";
    }

    rpc UserPreDoPreorderLottry( UserDoLottryReq ) returns ( UserDoLottryResp ) {
        option( tlvpickle.CmdID ) = 103;
        option( tlvpickle.OptString ) = "u:g:n:e:";
        option( tlvpickle.Usage ) = "-u <uid> -g<game_id> -n<lotto_name> -e <device_id>";
    }

    rpc SubmitPreLottoItem( SubmitPreLottoItemReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 104;
        option( tlvpickle.OptString ) = "u:g:n:t:i";
        option( tlvpickle.Usage ) = "-u<uid> -g<game_id> -n<name> -t<item_id> -i<valid_statu>";
    }    

    rpc UserDoPayLottry( UserDoLottryReq ) returns ( UserDoLottryResp ) {
        option( tlvpickle.CmdID ) = 105;
        option( tlvpickle.OptString ) = "u:g:n:e:";
        option( tlvpickle.Usage ) = "-u <uid> -g<game_id> -n<lotto_name> -e <device_id>";
    }

    rpc SetLottoChance( AddLottoChanceReq ) returns ( tlvpickle.SKBuiltinEmpty_PB  ) {
        option( tlvpickle.CmdID ) = 106;
        option( tlvpickle.OptString ) = "u:g:n:a:";
        option( tlvpickle.Usage ) = "-u<uid> -g<game_id> -n<lotto_name> -a<chance>";
    }

    rpc GetLottoValidItems( GetLottryItemReq ) returns ( GetLottryItemResp ) {
        option( tlvpickle.CmdID ) = 107;
        option( tlvpickle.OptString ) = "g:n:";
        option( tlvpickle.Usage ) = "-g<ly_game_id> -n<lotto_name>";
    }   

    rpc SetLottoTypeChanceV2( SetLottoTypeChanceV2Req ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 108;
        option( tlvpickle.OptString ) = "u:g:n:t:a:r:";
        option( tlvpickle.Usage ) = "-u<uid> -g<game_id> -n<lotto_name> -t<type> -a<chance> -r<isReset>";
    }   

    rpc GetLottoTypeChanceV2( GetLottoTypeChanceV2Req ) returns ( GetLottoTypeChanceV2Resp ) {
        option( tlvpickle.CmdID ) = 109;
        option( tlvpickle.OptString ) = "u:g:n:";
        option( tlvpickle.Usage ) = "-u<uid> -g<game_id> -n<lotto_name>";
    }   

    rpc AwardLottoTypeChance( AwardLottoTypeChanceReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 110;
        option( tlvpickle.OptString ) = "u:g:n:t:a:";
        option( tlvpickle.Usage ) = "-u<uid> -g<game_id> -n<lotto_name> -t<type> -a<chance>";
    }   

    rpc SetTypeChanceConfig( SetTypeChanceConfigReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 111;
        option( tlvpickle.OptString ) = "g:n:t:a:";
        option( tlvpickle.Usage ) = "-g<game_id> -n<lotto_name> -t<type> -a<limitChance>";
    }   

    rpc AwardLottoTypeChanceInternal( AwardLottoTypeChanceReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 112;
        option( tlvpickle.OptString ) = "u:g:n:t:a:";
        option( tlvpickle.Usage ) = "-u<uid> -g<game_id> -n<lotto_name> -t<type> -a<chance>";
    }   
}
