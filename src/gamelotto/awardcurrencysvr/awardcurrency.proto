syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package awardcurrency;

enum AwardCurrencyType{
    CurrencyNone = 0;
    WangzherongyaoFragment = 1;//王者荣耀碎片
}

message GetUserCurrencyReq {
	uint32 uid = 1;
    AwardCurrencyType type = 2;
}

message GetUserCurrencyResp {
	int32 amount = 1;
}

message UserCurrency
{
    uint32 uid = 1;
    AwardCurrencyType type = 2;
    int32 amount = 3;
    string order_id = 4;
    string order_desc = 5;
    uint32 op_uid = 6;
    uint64 update_time = 7;
}

message GetUserConsumeLogResp{
    repeated UserCurrency consume_list = 1;
}

message AddUserCurrencyReq {
	uint32 uid = 1;
    AwardCurrencyType type = 2;
	int32  amount = 3;
    string order_id = 4;
    string order_desc = 5;
	uint32 op_uid = 6;
}

message GetConsumeLogReq {
    AwardCurrencyType type = 1;
}

message FreezeUserCurrencyReq {
    uint32 uid = 1;
    AwardCurrencyType type = 2;
    uint32 amount = 3;
    string order_id = 4;
    string order_desc = 5;
    uint32 op_uid = 6;
}

message ConfirmFrozenOrderReq {
    enum OP {
        NOP = 0;
        COMMIT = 1;
        ROLLBACK = 2;
    }
    uint32 uid = 1;
    string order_id = 2;
    OP op = 3;
}

service AwardCurrency {
	option( tlvpickle.Magic ) = 15586;		// 服务监听端口号

	rpc GetUserCurrency( GetUserCurrencyReq ) returns ( GetUserCurrencyResp ){
		option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "u:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<uid> -t<type>";	// 测试工具的命令号帮助
	}

	rpc AddUserCurrency( AddUserCurrencyReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "u:t:a:k:d:o:";                       // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<uid> -t<type> -a<amount> -k<order id> -d<order desc> -o<op_uid>";
	}

    rpc GetUserConsumeLog( GetUserCurrencyReq ) returns ( GetUserConsumeLogResp ){
        option( tlvpickle.CmdID ) = 3;                                      // 命令号
        option( tlvpickle.OptString ) = "u:t:";                         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<uid> -t<type>"; // 测试工具的命令号帮助
    }

    rpc GetConsumeLog( GetConsumeLogReq  ) returns ( GetUserConsumeLogResp ){
        option( tlvpickle.CmdID ) = 4;                                      // 命令号
        option( tlvpickle.OptString ) = "t:";                         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <type>"; // 测试工具的命令号帮助
    }    

    // 冻结消费
    rpc FreezeUserCurrency( FreezeUserCurrencyReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "u:t:a:k:d:o:";
        option( tlvpickle.Usage ) = "-u<uid> -t<type> -a<amount> -k<order id> -d<order desc> -o<op_uid>";
    }

    rpc ConfirmFrozenOrder( ConfirmFrozenOrderReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "u:o:";
        option( tlvpickle.Usage ) = "-u<uid> -o<order_id>";
    }
}
