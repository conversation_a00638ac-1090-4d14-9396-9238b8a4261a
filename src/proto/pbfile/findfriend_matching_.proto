syntax="proto2";

package ga;
import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/find_friends";

// 检查用户是否完成了测试
message CheckUserFinishFindFriendExamReq
{
    required BaseReq base_req = 1;
}
message CheckUserFinishFindFriendExamResp
{
    required BaseResp base_resp = 1;
    required bool is_finish = 2;        // 是否完成测试
    required bool is_init = 3;          // 是否进入过测试页面，判断首次弹窗
}   

// 获取入口标签
message StFindFriendExamEntrance
{
    required uint32 exam_id = 1;        // 测试类id
    optional string name = 2;           // 测试名称
    optional string icon_url = 3;       // 入口icon url
}
message GetFindFriendExamEntranceReq
{
    required BaseReq base_req = 1;
}

message GetFindFriendExamEntranceResp
{
    required BaseResp base_resp = 1;
    repeated StFindFriendExamEntrance entrance_list = 2;
}

// 上传测试用户推荐游戏(已安装的)
message UploadExamInstallRecommendGameReq
{
    required BaseReq base_req = 1;
    repeated uint32 game_id_list = 2;
}
message UploadExamInstallRecommendGameResp
{
    required BaseResp base_resp = 1;
}

// 获取测试题目
message FindfriendExamQuestion
{
    required uint32 question_type = 1;       //题目维度idx
    required string question = 2;            //题目
    repeated string options = 3;             //选项
}

message GetFindFriendExamQuestionReq
{
    required BaseReq base_req = 1;
    required uint32 exam_id = 2;
}

message GetFindFriendExamQuestionResp
{
    required BaseResp base_resp = 1;
    repeated FindfriendExamQuestion question_list = 2;
}


// 用户测试
message AddUserFindFriendExamReq
{
    required BaseReq base_req = 1;   
    required uint32 exam_id = 2;       
    repeated uint32 answer_id_list = 3;     // 答案列表(顺序填充)
    required bool is_finish = 4;            // 是否完成了测试
}

message AddUserFindFriendExamResp
{
    required BaseResp base_resp = 1;
}

// 获取用户测试评价结果
message StUserExamResult
{
    required uint32 uid = 1;
    required string account = 2;        
    required string nickname = 3;       // 用户昵称
    required string result_url = 4;     // 评价生成图
	
	optional string result_desc = 5;        // 测试结果的分类标签对应的描述
	optional string result_exam_name = 6;   // 测试的名称（比如王者荣耀 、第五人格）
	optional string result_tag_name = 7;    // 测试结果的分类标签	
	
	optional string relay_circle_defaut_msg = 8; // 用于转发圈子的默认文案
	
}
message GetUserFindFriendExamResultReq
{
    required BaseReq base_req = 1;
}

message GetUserFindFriendExamResultResp
{
    required BaseResp base_resp = 1;
    required StUserExamResult exam_result = 2;
}

message FindFriendMatchInfo {
	enum FindFriendClassify {
		entertainment = 0;
		game = 1;
	}
	required uint32 uid = 1;
	required string exam_name = 2;   // 测试的名称（比如王者荣耀 、第五人格）
	required string tag_name = 3;    // 测试结果的分类标签
	required string account = 4;
	required string nickname = 5;
	required uint32 sex = 6;
	repeated string question_type_names = 7; 
    repeated uint32 score_list = 8;
	required string desc = 9;       // 测试结果的分类标签对应的描述
	required uint32 classify = 10;  //分类，0为娱乐，1为游戏
}

message MatchFriendReq {
	required BaseReq base_req = 1;
	required uint32 uid = 2;
}

message MatchFriendResp {
	required BaseResp base_resp = 1;
	required FindFriendMatchInfo user_info = 2;
	required FindFriendMatchInfo matched_user_info = 3;
	required uint32 match_percent = 4;
}