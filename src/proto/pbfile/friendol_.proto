syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/online";

// 上报在线用户正在玩的游戏
message ReportPlayingGameReq{
	required BaseReq base_req = 1;
	required string pkg_name = 2;
	optional uint32 game_state = 3; // 1 玩游戏  0 没有玩游戏  （注意客户端没法赋值为0,客户端赋值为0就会导致该字段不存在）
}

message ReportPlayingGameResp{
	required BaseResp base_resp = 1;
}

message FriendsDetail {
	required uint32 uid = 1;
	required uint32 ol_status = 2;        // 0 offline 1 online
	required uint32 last_ol_time = 3;     // 用户状态为离线时 该值表示在线状态最后更新的时间  如果用户状态为在线时 该值无特殊意义 为服务器收到请求时的系统时间
	required string account = 4;
	
	optional uint32 room_id = 5;         // 开黑房间ID channelID 
	optional string game_name = 6;       // 正在玩的游戏名
	optional uint32 room_type = 7;		 // 开黑房间类型, channel_.proto: enum ChannelType
	optional bool room_is_pwd = 8;       // 开黑房间是否有设置密码
	optional string nick_name = 9;
}

// 获取用户的离线好友列表
message GetOfflineFriendsReq {
	required BaseReq base_req = 1;
}

message GetOfflineFriendsResp {
	required BaseResp base_resp = 1;
	repeated FriendsDetail offline_list = 2;
}

// 获取用户的在线好友列表
message GetOnlineFriendsReq {
	required BaseReq base_req = 1;
	optional bool include_following = 2;//包括单向关注的
	
	repeated uint32 ext_check_online_uidlist = 3; // 额外需要检查是否在线的UID列表 

}

message GetOnlineFriendsResp {
	required BaseResp base_resp = 1;
	repeated FriendsDetail online_list = 2;
	
	repeated FriendsDetail ext_check_offline_list = 3; // 额外需要检查是否在线的UID列表里面已经离线的用户信息
}

// 获取指定群的在线人数信息
message GetGroupOnlineCountReq {
	required BaseReq base_req = 1;
	required uint32 group_id = 2;
}

message GetGroupOnlineCountResp {
	required BaseResp base_resp = 1;
	required uint32 group_id = 2;
	optional uint32 online_cnt = 3;       
	optional uint32 offline_cnt = 4;
}

// 好友在线事件的推送消息
message OnlineEventPushMsg {

    enum OnlinePushType {
        OnlinePushTypeDefault = 0;
        OnlinePushTypeUnfollow=1;//取消关注 频道跟随
    }

	required uint32 uid = 1;
	required uint32 ol_status = 2;        	// 0 offline 1 online
	optional uint32 channel_id = 3;       	// 开黑房间ID channelID 
	optional string game_name = 4;        	// 正在玩的游戏名
	optional uint32 room_type = 5;			// 开黑房间类型, channel_.proto: enum ChannelType
	
	optional uint32 channel_id_v2 = 8;	    // 欢城的直播房ID channelID V1 V2同时存在的情况下以V2为准
	optional uint32 room_type_v2 = 9;       // 房间类型, channel_.proto: enum ChannelType  V1 V2同时存在的情况下以V2为准
	
	optional bool channel_is_pwd = 10;      // 开黑房间是否有设置密码
	optional string account = 11;
	optional string nick_name =12;
    optional OnlinePushType push_type = 13;
}

enum EFollowChannelAuthSwitchType {
	ENUM_FollowChannelAuth_CLOSE = 0;             // 关闭全部跟随
	ENUM_FollowChannelAuth_ALL_ALLOW = 1;         // 允许所有人跟随  
	ENUM_FollowChannelAuth_ONLY_FRIEND_ALLOW = 2; // 只允许好友跟随  
	ENUM_FollowChannelAuth_ONLY_FANS_ALLOW = 3;   // 只允许粉丝跟随 （包含了允许好友）
}

// 是否打开 跟随进房开关
message UpdateFollowChannelAuthReq {
	required BaseReq base_req = 1;
	required uint32 uid = 2;
	required bool follow_auth = 3;  // false表示关闭快速加入 (废弃的 过期字段)
	optional uint32 follow_auth_switch = 4;  // see EFollowChannelAuthSwitchType
}

message UpdateFollowChannelAuthResp {
	required BaseResp base_resp = 1;
	optional uint32 follow_auth_switch = 2;  // see EFollowChannelAuthSwitchType
}


message GetFollowChannelAuthReq {
	required BaseReq base_req = 1;
	required uint32 uid = 2;
}

message GetFollowChannelAuthResp {
	required BaseResp base_resp = 1;
	required bool follow_auth = 2;  // false表示关闭快速加入 (废弃的 过期字段)
	optional uint32 follow_auth_switch = 3;  // see EFollowChannelAuthSwitchType
}

//是否显示 输入邀请码
message CheckShowAddInviteCodeReq
{
	required BaseReq base_req = 1;
	required uint32 uid=2;
}
message CheckShowAddInviteCodeResp
{
	required BaseResp base_resp = 1;
	required bool is_show=2;
}

//邀请好友 邀请码验证
message CheckInviteFriendCodeReq
{
	required BaseReq base_req = 1;
	required uint32 uid=2;				
	optional uint32 package_type=3;		//客户端包类型
	required string invite_code=4;		//邀请码
	optional string origin_channel=5;	//源包渠道
	optional string current_channel=6;	//安装包当前渠道号 ,
}


message CheckInviteFriendCodeResp
{
	required BaseResp base_resp = 1;
	required bool is_ok=2;				//邀请码验证结果
	required uint32 lives=3;			
}

message OnlyTestxxxx{
	optional uint32 a = 1;
}

