syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/circle";

////------- 游戏圈业务基本类型 BEGIN ----//

//圈主
message CircleKeeper{
    required uint32 uid                                     = 1;
    required string account                                 = 2;
    required string nick_name                               = 3;
    required uint32 sex                                     = 4;

    enum KEEPR_TYPE {
        MASTER = 1; // 圈主
        VICE  = 2;           // 副圈主
    }
    required uint32 keeper_type                             = 5;
}

//游戏圈主题配图
message CircleTopicImage{
    required string thumb_url                               = 1;
    required string image_url                               = 2;
    optional uint32 image_width                             = 3;
    optional uint32 image_height                            = 4;
}

//游戏圈主题
message CircleTopic{
    enum TOPIC_TYPE {
        NORMAL      = 0;          //用户发布主题
        CUNZAIGAN   = 1;          //占坑， 让OFFICIAL跟SRV存储保持一致
        OFFICIAL    = 2;	      //公众号发布主题
    }
    required uint32 type                                    = 1;
    required uint32 circle_id                               = 2;
    required uint32 topic_id                                = 3;
    required string title                                   = 4;
    required string content                                 = 5;
    required uint32 create_time                             = 6;
    repeated CircleTopicImage image_list                    = 7;
    required uint32 like_count                              = 8;
    required uint32 comment_count                           = 9;
    required uint32 is_liked                                = 10;

    enum TOPIC_STATE{
        STATE_HIGHT_LIGHT   = 1;    //精华
        STATE_STICK         = 2;    //置顶
        STATE_ACTIVITY      = 4;    //活动
        STATE_GAME_DOWNLOAD = 8;    //游戏下载
    }
    required uint32 topic_state                             = 11;
    required uint32 last_comment_time                       = 12;
    optional CircleUser creator                             = 13;
    required string create_time_desc                        = 14;   //create_time的可读串
    required string last_comment_time_desc                  = 15;   //last_comment_time的可读串
    optional CircleTopicGameDownloadInfo download_info      = 16;    // TOPIC_STATE包含STATE_GAME_DOWNLOAD时, 提供游戏的下载信息
}

//游戏圈评论配图
message CircleCommentImage{
	required string thumb_url                               = 1;
    required string image_url                               = 2;
}

//游戏圈评论
message CircleTopicComment {
    required uint32 comment_id                  = 1;
    required uint32 circle_id                   = 2;
    required uint32 topic_id                    = 3;  //评论所属的topic
    required string content                     = 4;  //评论内容
    required CircleUser creator                 = 5;  //发送者
    required uint32 create_time                 = 6;  //发送时间
    enum TopicCommentStatus {
        NORMAL  = 0;        // 正常
        DELETED = 1;        // 被删除  1 << 0
        SHIELD  = 2;        // 被屏蔽  1 << 1
    }
    required uint32 status                      = 7;  // 评论状态(TopicCommentStatus mask)
    optional CircleTopicComment replied_comment = 8;  // 如果有, 表示评论的目标
    required string create_time_desc            = 9;  // create_time的可读串
	repeated CircleCommentImage image_list  	= 10; // 评论图片
	optional int32 floor                        = 11; // 楼层 -1 为老数据 可能没有楼层概念
}

// 描述圈子的一些与用户相关的动态数据, 具有实时性, 作为Circle静态类型的补充
message CircleDynamicData {
	required uint32 circle_id 					= 1;
	required uint32	follower_number 			= 2;
	required uint32 guild_follower_number 		= 3;
	required bool 	is_follow					= 4;
	repeated CircleKeeper keeper_list 			= 5;
    optional string circle_name                 = 6;
    optional string icon                        = 7;
    optional uint32 game_id                     = 8;
    optional uint32 topic_count                 = 9;
    optional uint32 today_topic_count           = 10;
    optional bool game_downloadable             = 11;
    optional string act_tag                     = 12;
    optional string act_desc                    = 13;
    optional bool   has_welfare                 = 14;
	optional bool	is_anncouncement_circle		= 15;
    optional uint32 game_type                   = 16;
}

////------- 游戏圈业务基本类型 END ----//

////------- 请求应答 BEGIN ----//

enum CircleListRequestType{
    REQ_TYPE_RECOMMEND  = 1; //推荐圈子列表
    REQ_TYPE_MY_CIRCLES = 2; //我的圈子列表
    REQ_TYPE_NEW_GAME   = 3; //新游圈子列表
	REQ_TYPE_MY_VISIT_RECENTLY  = 4; //我最近逛过的圈子
}

enum CircleTopicListReqType{
    REQ_TYPE_HOT_TOPICS          = 1; // 热门主题列表
    REQ_TYPE_NEW_TOPICS          = 2; // 最新发表主题列表
    REQ_TYPE_CONTACT_TOPICS      = 3; // 玩伴主题列表
    REQ_TYPE_LAST_UPDATE_TOPICS  = 4; // 最近评论主题列表
    REQ_TYPE_HIGHLIGHT_TOPICS    = 5; // 精华主题列表
}

message CircleGetListReq{
    required BaseReq base_req                               = 1;
    required uint32  req_type                               = 2;
}

message CircleGetListResp{
    required BaseResp 			base_resp					= 1;
    required uint32   			req_type					= 2;
    repeated CircleDynamicData	circle_dynamic_data_list     = 3;
}

//加入游戏圈
message CircleJoinReq {
    required BaseReq base_req                               = 1;
    repeated uint32 circle_id_list                          = 2;
}

message CircleJoinResp {
    required BaseResp base_resp                             = 1;
    repeated uint32 circle_id_list                          = 2;
    repeated uint32 failed_id_list                          = 3;
}

//退出游戏圈
message CircleQuitReq {
    required BaseReq base_req                               = 1;
    required uint32 circle_id                               = 2;
}

message CircleQuitResp {
    required BaseResp base_resp                             = 1;
    required uint32 circle_id                               = 2;
}

//TODO
message CircleGetTopicListReq {
    required BaseReq base_req           = 1;
    required uint32 circle_id           = 2;
    required uint32 page_count          = 3; //请求获取的条数
    required uint32 page_position       = 4; //页码
    required uint32 req_type            = 5; //see CircleTopicListReqType
    required uint32 userfrom            = 6; //see STAT_FROM_PAGE_TYPE
}
//TODO
message CircleGetTopicListResp {
    required BaseResp base_resp             = 1;
    required uint32 circle_id               = 2;
    required uint32 page_count              = 3; //请求获取的条数
    required uint32 page_position           = 4; //页码
    required uint32 req_type                = 5;
    repeated CircleTopic topic_list     	= 6;    //topic列表
    required uint32 newest_topic_id         = 7;//最新主题id
}

//获取某条topic
message CircleGetTopicReq {
    required BaseReq base_req           = 1;
    required uint32 circle_id           = 2;
    required uint32 topic_id            = 3;
    optional uint32 like_user_count     = 4;
    optional uint32 top_comment_count   = 5;
}

message CircleGetTopicResp {
    required BaseResp base_resp                     = 1;
    required uint32 circle_id                       = 2;
    required uint32 topic_id                        = 3;
    required CircleTopic topic 		                = 4;
    repeated string like_user_list                  = 5;
    repeated CircleTopicComment top_comment_list    = 6;
}

//发表主题
message CirclePostTopicReq {
    required BaseReq base_req       = 1;
    required uint32 circle_id       = 2;
    required string client_id       = 3;
    optional string title           = 4;
    required string content         = 5;
    repeated string img_key_list    = 6;
}

message CirclePostTopicResp {
    required BaseResp base_resp     = 1;
    required string client_id       = 2;
    required CircleTopic topic 		= 3;
}

//获取评论列表
message CircleGetCommentListReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;                  //所属主题
    required uint32 start_comment_id = 4;          //从这条评论开始获取之前的评论，为0则获取最新评论
    required uint32 comment_count = 5;             //请求获取的条数
}

message CircleGetCommentListResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 start_comment_id = 4;
    required uint32 comment_count = 5;             //实际获取到的条数
    repeated CircleTopicComment comment_list = 6;  //结果列表
}

//发表评论
message CirclePostCommentReq {
    required BaseReq base_req           = 1;
    required uint32 circle_id           = 2;
    required uint32 topic_id            = 3; //所属主题
    required uint32 replied_comment_id  = 4; //如果是回复别人评论，则为评论id，回复主题则为0
    required string content             = 5;
	repeated string img_key_list    	= 6;
}

message CirclePostCommentResp {
    required BaseResp base_resp         = 1;
    required uint32 circle_id           = 2;
    required uint32 topic_id            = 3;
    required uint32 replied_comment_id  = 4;
    required CircleTopicComment comment = 5;
}

//赞/取消赞 消息  同旧版
message CircleLikeTopicReq {
    required BaseReq base_req           = 1;
    required uint32 circle_id           = 2;
    required uint32 topic_id            = 3; //所属主题
    required bool is_like               = 4; //true为赞，false为取消赞
}

message CircleLikeTopicResp {
    required BaseResp base_resp         = 1;
    required uint32 circle_id           = 2;
    required uint32 topic_id            = 3; //所属主题
    required bool is_like               = 4;
}

//举报主题 同旧版
message CircleReportTopicReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id  = 3;                     //所属主题
}

message CircleReportTopicResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id   = 2;
    required uint32 topic_id   = 3;
}

//删除我发的主题 同旧版
message CircleDeleteTopicReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
}

message CircleDeleteTopicResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
}

//删除我发的评论 同旧版
message CircleDeleteCommentReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 comment_id = 4;
}

message CircleDeleteCommentResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 comment_id = 4;
}

//单查游戏圈
message CircleGetCircleDetailReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
}

message CircleGetCircleDetailResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;					// context
    required Circle circle = 3;
    required CircleDynamicData circle_dynamic = 4;
}

// 查我发表过的主题
message CircleGetUserTopicReq {
    required BaseReq base_req = 1;
    required uint32 start_circle_id = 2;			// circle_id = 0 && topic_id = 0, 表示获取最新的消息
    required uint32 start_topic_id = 3;         	// 否则从这条消息开始，获取之前的消息。为0的话，获取最新的消息
    required uint32 topic_count = 4;            	//请求获取的条数
    required uint32 uid = 5;						//
}

message CircleGetUserTopicResp {
    required BaseResp base_resp = 1;
    required uint32 start_circle_id = 2;			// circle_id = 0 && topic_id = 0, 表示获取最新的消息
    required uint32 start_topic_id = 3;         	// 否则从这条消息开始，获取之前的消息。为0的话，获取最新的消息
    required uint32 topic_count = 4;            	//请求获取的条数
    repeated CircleTopic topic_list = 5;    		//topic列表
    required uint32 uid = 6;						//
}

// 查点赞列表
message CircleGetLikeUserListReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 offset = 4;      // 查询起始
    required uint32 limit = 5;      // 查询数量, 若为0, 则使用服务器缺省值
}

message CircleGetLikeUserListResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 offset = 4;
    required uint32 limit = 5;
    repeated string user_nick_list = 6;  // 用户列表, 按点赞时间倒排
}


///------ 以下不变
// 批量设置已读
message CircleMarkReadedReq {
    required BaseReq base_req = 1;
    required uint32 svr_msg_id = 2; // 服务消息id(最大的那条id)
}

message CircleMarkReadedResp {
    required BaseResp base_resp = 1;
    required uint32 svr_msg_id = 2;
}

message CircleMuteUserReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;             //被禁言的人
    repeated uint32 circle_id_list = 3;        // 为空表示禁言全部
    required uint32 days = 4;           // =0无限期 兼容旧协议，新协议（默认为0）不再有用
    optional uint32 secs = 5;           // =0无限期
    optional string reason = 6;         // 禁言理由
    optional uint32 because_circle_id = 7;      // 因为某个圈子的某个帖子被禁言
    optional uint32 because_topic_id = 8;       //
}

message CircleMuteUserResp {
    required BaseResp base_resp = 1;
    required uint32 uid = 2;
    repeated uint32 circle_id_list = 3;
    required uint32 days = 4;
    optional uint32 secs = 5;
}

message CircleUnmuteUserReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;             //被禁言的人
    repeated uint32 circle_id_list = 3;        // 为空表示禁言全部
}

message CircleUnmuteUserResp {
    required BaseResp base_resp = 1;
    required uint32 uid = 2;
    repeated uint32 circle_id_list = 3;
}


//我的圈子排序 -- v1.5
message MyCircleOrderReq {
    required BaseReq base_req = 1;
    repeated uint32 Circle_id_list = 2;
}
message MyCircleOrderResp {
    required BaseResp base_resp = 1;
    repeated uint32 Circle_id_list = 2;
}

//加精 -- v1.5
message CircleHighlightTopicReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
}

message CircleHighlightTopicResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
}

//取消加精 -- v1.5
message CircleCancelHighlightTopicReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
}

message CircleCancelHighlightTopicResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
}

//管理员删除评论 -- v1.5
message CircleManagerDeleteCommentReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 comment_id = 4;
}

message CircleManagerDeleteCommentResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 comment_id = 4;
}

//检查圈子更新状态
message CircleCheckUpdateReq{
    required BaseReq base_req = 1;
    repeated TopicUpdateInfo topic_update_info = 2 ;
}

message CircleCheckUpdateResp{
    required BaseResp base_resp = 1;
    repeated TopicUpdateInfo topic_update_info = 2 ;
    optional uint32 newest_circle_id = 3 ; //最新更新的游戏圈
}


// 圈子活动

message CircleActivityDetail {
	required uint32 activity_id = 1;
    required string act_url = 2;		// 活动链接
    optional string pic_url = 3;		// 活动头图链接
	required string title = 4;			// 标题
	required uint32 warmed_up_ts_begin = 5;	// 预热时间begin
	required uint32 warmed_up_ts_end = 6;	// 预热时间end
	required uint32 active_ts_begin = 7; 	// 进行时间begin
	required uint32 active_ts_end = 8; 	// 进行时间end
	required uint32 end_ts = 9;			// 结束时间
}

message CircleGetActivityListReq {
    required BaseReq base_req = 1;
	required uint32 is_home_page = 2;	// 0.全部活动 1.首页活动
	optional uint32 start_index = 3;
}

message CircleGetActivityListResp {
    required BaseResp base_resp   = 1;
    repeated CircleActivityDetail activity_list	= 2;
	required uint32 is_home_page = 3;	// 0.全部活动 1.首页活动
}


message CircleGetHotReq {
	required BaseReq base_req = 1;
}

message CircleGetHotResp {
	required BaseResp base_resp   = 1;
	repeated CircleDynamicData hot_circle_list = 2;
}


// 新版圈子评论列表 V3 --- BEGIN

enum CircleTopicCommentStatus{
        COMMENT_STATUS_NORMAL  = 0;        // 正常
        COMMENT_STATUS_DELETED = 1;        // 被删除  1 << 0
        COMMENT_STATUS_SHIELD  = 2;        // 被屏蔽  1 << 1
    }
	
enum CircleTopicCommentType {
        COMMENT_TYPE_NORMAL = 0;		 // 普通评论
        COMMENT_TYPE_REPLY = 1;		     // 回复"普通评论"的评论
		COMMENT_TYPE_REPLY_REPLY = 2;    // 回复"评论'普通评论'评论"的评论
		COMMENT_TYPE_HAVE_REPLY = 4;     // 有过被回复的"普通评论"
}
// 评论回复的目标
message StCommentReplayTargetBase
{
	required uint32 comment_id                  = 1;
    required CircleUser creator                 = 2;  // 发送者
    required uint32 create_time                 = 3;  // 发送时间
    required uint32 status                      = 4;  // 评论状态(CircleTopicCommentStatus mask)
	required string create_time_desc            = 5;  // create_time的可读串
}	

// 一条评论的基本数据	
message StCommentBase
{
	required uint32 comment_id                  = 1;
	required string content                     = 2;  // 评论内容
    required CircleUser creator                 = 3;  // 发送者
    required uint32 create_time                 = 4;  // 发送时间
    required uint32 status                      = 5;  // 评论状态(CircleTopicCommentStatus mask)
	required uint32 type                        = 6;  // 评论类型(CircleTopicCommentType mask)
	required string create_time_desc            = 7;  // create_time的可读串
	optional StCommentReplayTargetBase reply_target = 8; // 如果该条评论是回复某条评论的话 该处填目标评论的信息, 对于普通的评论该字段为空
}
	
// 普通评论
message CircleTopicNomalComment {
	
	required StCommentBase comment_base         = 1;
	required int32 floor                        = 2; // 楼层 -1 为老数据 可能没有楼层概念
	repeated CircleCommentImage image_list  	= 3; // 评论图片
	repeated StCommentBase reply_comment_list   = 4; // 回复了本评论的其他评论列表 默认3条, 如果没有回复本条评论 该字段为空
	optional uint32 reply_total_cnt             = 5; // 回复了本评论的其他评论总数 
}


//获取普通评论列表
message CircleGetNomalCommentListReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;                  //所属主题
    required uint32 start_comment_id = 4;          // 从这条评论开始获取之前的评论，为0则获取最新评论
    required uint32 count = 5;                     // 请求获取的条数
	optional bool include_start_id = 6;			   // 是否包含start,默认否
	optional bool is_desc = 7;			           // 是否降序排列即时间从新往旧排列 默认为否 
}

message CircleGetNomalCommentListResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 start_comment_id = 4;
    repeated CircleTopicNomalComment comment_list = 5;  //结果列表
	optional uint32 nomal_left_cnt    = 6; // 从start_comment_id 开始的剩余评论数 即 剩余楼数
}

//获取指定评论的回复列表
message CircleGetCommentReplyListReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;                  // 所属主题
	required uint32 parent_comment_id = 4;         // 所属父评论ID
    required uint32 start_reply_comment_id = 5;    // 
    required uint32 count = 6;                     // 请求获取的条数
}

message CircleGetCommentReplyListResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
	required uint32 parent_comment_id = 4;             //所属父评论ID
    required uint32 start_reply_comment_id = 5;
    repeated StCommentBase reply_list = 6;  //结果列表
	optional uint32 reply_total_cnt   = 7; // 回复了本评论的回复总数
	optional uint32 reply_left_cnt    = 8; // 从start_reply_comment_id开始的回复了本评论的剩余回复数
}
// 新版圈子评论列表 V3 -- END

////------- 请求应答 END ------//
