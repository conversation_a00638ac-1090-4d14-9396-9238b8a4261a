syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/userrecommend";


enum ContactsOperType
{
	CONTACTS_UNDEFINED = 0;
	CONTACTS_ADD = 1;
	CONTACTS_DELETE = 2;
}


//添加或更新
message AddOrUpdateContactsReq
{
	required BaseReq base_req = 1;			
	repeated string phone_list=2;				//上报手机列表	
	required bool is_full_data=3;				//是否为全量数据
	required ContactsOperType oper_type=4;		//操作类型	
}

message AddOrUpdateContactsResp
{
	required BaseResp base_resp = 1;	
	optional uint32 contact_version=2;	// 通讯录版本
}

//不再推荐
message RejectRecommendReq
{
	enum RejectType
	{
		REJECT_FROM_CONTACT=1;
		REJECT_FROM_GAME=2;
	}
	required BaseReq base_req = 1;
    optional string phone=2;		//拒绝推荐联系人手机号
	optional string account=3;
	optional uint32 type=4;
}


message RejectRecommendResp
{
	required BaseResp base_resp = 1;
	optional string phone=2;
	optional string account=3;
}

//是否向其他用户推荐
message ChangeRecommendStatusReq
{
	required BaseReq base_req = 1;
	required bool status=2;
}
message ChangeRecommendStatusResp
{
	required BaseResp base_resp = 1;
	required bool status=2;
}

//获取自己推荐状态
message GetRecommendStatusReq
{
	required BaseReq base_req = 1;
}
message GetRecommendStatusResp
{
	required BaseResp base_resp = 1;
	required bool status=2;
}


//推荐通讯录细节信息
message RecommendFromContacts
{
	required string phone=1;			//手机号
	optional string account=2;			//账号
	optional string nickname=3;			//昵称
}

//通讯录和同玩游戏推荐
message RecommendFromAll
{
	enum RecommendType
	{
		RECOMMEND_FROM_CONTACTS=1;
		RECOMMEND_FROM_GAME=2;
	}
	required uint32 recommend_type=1;	//推荐类型
	required string account=2;			//账号
	required string nickname=3;			//昵称
	required string phone=4;			//手机号
	optional string game_name=5;		//游戏名
	optional string city=6;				//用户所在城市
}

//从通讯录推荐
message GetRecommendFromContactsReq
{
	required BaseReq base_req = 1;		
}


message GetRecommendFromContactsResp
{
	required BaseResp base_resp = 1;
	repeated RecommendFromContacts recommend_list=2;
	optional uint32 contact_version=3;	// 通讯录版本
}

//总的推荐，包括通讯录和游戏
message GetUserRecommendReq
{
	required BaseReq base_req = 1;	
}


message GetUserRecommendResp
{
	required BaseResp base_resp = 1;
	repeated RecommendFromAll recommend_all_list=2;
	optional uint32 contact_version=3;				// 通讯录版本
	optional uint32 batch_num=4;					//客户端每批显示数量
}

