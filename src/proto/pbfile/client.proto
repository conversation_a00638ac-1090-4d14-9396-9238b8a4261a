syntax="proto2";

package ga;

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/client";

enum ClientCMDType{
	TYPE_SYNC_CFG = 1;
	TYPE_APP_VERSION = 2;
    TYPE_GAME_CIRCLE_CFG = 3;
    TYPE_UNBIND_BOTH = 4; //IPC from App process, unbind all services
    TYPE_ROOT_ALERT_CFG = 5;
    TYPE_GAME_CFG_LAST_MODIFY = 6; //浮窗游戏黑白名单最后修改时间
    TYPE_SERVER_TIME_SYNC = 7;
    TYPE_MODIFY_SYNC_KEYS = 9; //作废！ 丢synckey不能走IPC,因为网络进程一启动就会sync!!
	TYPE_PLUGIN_URL		  = 10; //Android Plugin Config URL
	TYPE_MAGIC_MASK = 11;
	TYPE_LATEST_PUSH_SEQ = 12;
    TYPE_QINIU_LOG_TOKEN = 13; //七牛上传日志的token
    TYPE_WEB_SECURE_TOKEN = 14;    //TT内web使用的token
}

enum GameCircleState{
    STATE_HIDEALL = 0;
    STATE_SHOWALL = 1;
}

message AttachmentDescription{
	required uint32 type = 1;
	required uint32 file_size = 2;
	optional int64 voice_length = 3;
	optional uint32 read_status = 4;
}

//其实是黑名单包名
message SyncConfig{
	repeated string cfg_list = 1;
}

//游戏圈配置
message GameCircleConfig{
    required uint32 state = 1;
}

//其他应用Root权限警告配置
message RootAlertConfig{
    required uint32 state = 1;
}

//浮窗游戏黑白名单
message GameConfigLastModify{
    required uint32 last_update = 1; //单位:秒. ts 为 0 则清空本地名单
}

message ServerTimeSync{
    required uint32 srv_time = 1; //服务器时间
}

message MagicMaskSync{
    required uint32 magic_mask = 1; //各种功能开关
}

message LatestPushSeq {
    required uint32 latest_seq = 1;
}

message LatestAppVersionInfo {
    required string version = 1;
    required uint32 version_code = 2;		
}
