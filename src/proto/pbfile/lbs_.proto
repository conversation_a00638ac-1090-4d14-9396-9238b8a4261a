syntax="proto2";

package ga;

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/lbs";

import "ga_base.proto";



// 上报用户经纬度
message ReportUserGeoReq {
    required BaseReq base_req = 1;
    optional double longitude = 2; // 经度
	optional double latitude  = 3; // 纬度
}

message ReportUserGeoResp {
    required BaseResp base_resp = 1;
}


// 获取附近的人
message LbsNearUser {

    required uint32 uid = 1;
	required string account = 2;
	required string ttid = 3;
	required string nickname = 4;
	required int32 sex = 5;          // 性别
	required string signature = 6;   // 用户的签名
	
    required double distance = 7;  // 距离 单位米
    required uint32 last_time = 8; // 上次更新位置的时间 单位秒

    optional uint32 channel_id = 9;    // 房间ID 如果有的话
    optional uint32 channel_type = 10; // 房间类型 如果有的话
	optional bool channel_is_pwd = 11; // 房间是否有密码
}

message GetNearUserListReq {
    required BaseReq base_req = 1;
    optional double longitude = 2; // 经度 范围在-180 - 180
	optional double latitude  = 3; // 纬度 范围在-90 - 90
	
	// min_distance + begin_uid + count 作为分页选项 存在
	required double min_distance  = 4; // 最小距离 用于翻页 第一页可以填0 单位米 其他页填上一页的最大距离
	required uint32 count  = 5;        // 本次获取数量
	required uint32 begin_uid  = 6;    // 第一页可以填0 其他页填写上一页的最后一个用户
	
	required int32 filter_sex = 7;    // 0 女 1 男 2 全部
}

message GetNearUserListResp {
    required BaseResp base_resp = 1;
	repeated LbsNearUser user_list = 2;
}

// 附近的人开关
message ModifyUserGeoSwitchReq {
    required BaseReq base_req = 1;
    required bool disable_geo = 2; // 是否关闭附近的人
}

message ModifyUserGeoSwitchResp {
    required BaseResp base_resp = 1;
	required bool disable_geo = 2; // 是否关闭附近的人
}

message GetUserGeoSwitchReq {
    required BaseReq base_req = 1;
}

message GetUserGeoSwitchResp {
    required BaseResp base_resp = 1;
	required bool disable_geo = 2; // 是否关闭附近的人
}
