syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel";

enum ChannelGuildListType{
    CHANNELGUILD_LIST_NONE = 0;
    CHANNELGUILD_LIST_HOME = 1;     //主房间
    CHANNELGUILD_LIST_PUBLIC = 2;   //公开房列表
    CHANNELGUILD_LIST_INNER = 3;    //内部房列表
    CHANNELGUILD_LIST_MEMBER = 4;   //成员所在房间列表
}

message ChannelGuildListEntry{
    required uint32 channel_id = 1;     //房间ID
    required uint32 channel_type = 2;   //房间类型
    optional uint32 member_count = 3;   //房间人数
    optional string channel_name = 4;   //房间名
    optional string tag_name     = 5;   //标签名称
    optional string bg_color     = 6;   //标签颜色
    optional string account      = 7;   //列表使用成员头像，无这个字段时使用房间头像
    optional bool has_pwd        = 8;   //是否设置了密码
    optional string icon_md5     = 9;   //房间头像md5
}

//首页列表接口
message ChannelGuildGetListSummaryReq
{
    required BaseReq base_req   = 1;
    required uint32 guild_id    = 2;    
}

message ChannelGuildGetListSummaryResp
{
    required BaseResp base_resp = 1;
    repeated ChannelGuildListEntry public_list = 2; //公开房
    repeated ChannelGuildListEntry inner_list = 3;  //内部房
    repeated ChannelGuildListEntry member_list = 4; //成员所在的组队房间
    repeated ChannelGuildListEntry home_channel = 5; //总房间
}

//分页更多接口
message ChannelGuildGetListByTypeReq{
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 type = 3;   //see ChannelGuildListType
    required uint32 index = 4;  //分页的序号，from 0
}

message ChannelGuildGetListByTypeResp{
    required BaseResp base_resp = 1;
    repeated ChannelGuildListEntry channel_list = 2;
    required uint32 total = 3;  //列表长度
}

//公会房间总人数
message ChannelGuildGetTotalMemberCountReq{
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
}

message ChannelGuildGetTotalMemberCountResp{
    required BaseResp base_resp = 1;
    required uint32 count = 2;
}
