syntax="proto2";

package ga;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/find_friends";

message Location2 {
	optional string country = 1;
	optional string province = 2;
	optional string city = 3;
	optional string district = 4;
}

message FindFriendsSettings {
	enum GenderFilter {
		UNRESTRICTED = 0;
		MALE_ONLY = 1;
		FEMALE_ONLY = 2;
	}
	
	required GenderFilter 	gender_filter = 1;
	repeated string 		playing_games = 2;
	required bool 			auto_play_voice = 3;
}

message FindFriendsMyInfo {
	repeated string 				photo_urls = 1;
	required string 				voice_url = 2;
	required uint32  				voice_duration = 3;
	required FindFriendsSettings 	settings = 4;
}

message FindFriendsGetMyInfoReq {
	required BaseReq base_req = 1;
}

message FindFriendsGetMyInfoResp {
	required BaseResp 			base_resp = 1;
	optional FindFriendsMyInfo 	my_info = 2;
	optional uint32 			free_like_quota = 5;	// 当前还剩下多少次免费送花的机会
}

message FindFriendsUpdateMyInfoReq {
	required BaseReq 				base_req = 1;
	optional FindFriendsSettings 	settings = 2;
	optional string 				voice_key = 3;
	optional uint32  				voice_duration = 4;
}

message FindFriendsUpdateMyInfoResp {
	required BaseResp base_resp = 1;
	optional FindFriendsMyInfo my_info = 2;
}

message FindFriendsUpdatePhotosReq {
	required BaseReq 	base_req = 1;
	required uint32 	index = 2;		// 0,1,2,3
	optional string 	photo_key = 3;	// 如果删除该位置照片, 提交空串或者不填
}

message FindFriendsUpdatePhotosResp {
	required BaseResp	base_resp = 1;
	optional string 	photo_url = 2;	// 如果是更新, 返回照片的完整地址
}

message UserCard {
	required uint32 	uid = 1;
	required string 	username = 2;
	required string 	alias = 3;
	required string 	nickname = 4;
	required uint32 	gender = 5;		// 0: Female, 1: Male
	optional Location2 	location = 6;
	repeated string 	photo_urls = 7;
	required string 	voice_url = 8;
	required uint32  	voice_duration = 9;
	repeated string 	playing_games = 10;

	optional bool 		liked_me = 15;
}

message FindFriendsGetUserCardsReq {
	required BaseReq 	base_req = 1;
	optional string 	session_id = 2;
	required uint32 	start_index = 3;
	required uint32 	count = 4;
}

message FindFriendsGetUserCardsResp {
	required BaseResp 	base_resp = 1;
	required string		session_id = 2;	// if session_id changed, next GetFindFriendsListReq should use 0 as start_index
	repeated UserCard	user_cards = 3;
}

message FindFriendsOperateOnCardsReq {
	required BaseReq 	base_req = 1;
	repeated uint32 	throw_uids = 2;
	optional uint32 	like_uid = 3;
}

message FindFriendsOperateOnCardsResp {
	required BaseResp 	base_resp = 1;
	// 以下字段仅在FindFriendsOperateOnCardsReq中的like_uid有值时返回
	optional uint32 	red_diamonds_cost = 2;	// 消耗红钻
	optional uint32 	red_diamonds_bonus = 3;	// 奖励
	optional bool 		mated_up = 4;			// 是否相互喜欢
	optional uint32 	free_like_quota = 5;	// 当前还剩下多少次免费送花的机会
}

message FindFriendsGetGameListReq {
	required BaseReq 	base_req = 1;
}

message FindFriendsGetGameListResp {
	required BaseResp 	base_resp = 1;
	repeated string 	games = 2;
}

// im.proto: IM_MSG_TYPE = FIND_FRIENDS_MATED_UP_MSG 
message FindFriendsMatedUpMessage {
	required UserCard user_card = 1;
}

// im.proto: IM_MSG_TYPE = FIND_FRIENDS_LIKED_NOTIFY 
/* 
NOTE:
服务器推送一条固定MsgID的消息，每次都设置为未读，客户端覆盖存储，liked_count_during_afk为0时不显示。
服务器处理同步时需要对旧版本屏蔽该消息。
*/ 
message FindFriendsLikedNotification {
	required uint32 liked_count_during_afk = 1;		// 不在玩扩圈期间收到的喜欢数
	required string message = 2;					// 显示的消息
	required uint32 charm = 3;						// 获得的魅力值
	repeated string avatars_of_recent_liked_me = 4;	// 最近三次给我点赞的人的头像	
}

// 快速匹配配置
message QuickMatchConfiguration {
	message Value {
		required uint32 value = 1;
		required string name = 2;
	}

	message Option {
		required string key = 1;		// key for match request
		required string title = 2;		// title for display
		repeated Value 	values = 3;		// acceptable values
	}

	required uint32 game_id = 1;		// 游戏ID
	required string game_name = 2;		// 游戏名称
	required string icon_url = 3;		// 游戏图标
	repeated Option options = 4;		// 需要填写的匹配选项
	optional uint32 channel_tag_id = 5;	// 游戏对应的标签ID
}

message GetQuickMatchConfigurationsReq {
	required BaseReq base_req = 1;
	required uint32 version = 2;		// 客户端配置版本
}

message GetQuickMatchConfigurationsResp {
	required BaseResp base_resp = 1;
	required uint32 version = 2;							// 服务器配置版本
	repeated QuickMatchConfiguration configurations = 3;	// 如果服务器版本不高于客户端版本, 则不返回该字段, 否则全量返回
}

message QuickMatchOption {
	required string key = 1;				// 对应 QuickMatchConfiguration::Option 中的key
	required uint32 value = 2;				// 对应 QuickMatchConfiguration::Option::Value 中的value
}

message QuickMatchGame {
	required string game_name = 1;			// 游戏名称
	repeated QuickMatchOption options = 2;	// 匹配选项
}

message StartQuickMatchReq {
	// 补位信息
	message SupplementInfo {
		required uint32 channel_id = 1;		// 频道ID
		required uint32 supplement_num = 2;	// 补位人数
	}

	required BaseReq base_req = 1;
	required string game_name = 2;			// 游戏名称(deprecated)
	repeated QuickMatchOption options = 3;	// 匹配选项(deprecated)
	// 新版本新增字段
	repeated QuickMatchGame games = 4;		// 支持多游戏匹配, 具有优先级, 靠前的游戏将被提前匹配
	optional SupplementInfo supplement_info = 5;	// 补位信息
}

message StartQuickMatchResp {
	required BaseResp base_resp = 1;
	required uint32 max_duration = 2;		// 匹配最长持续时间
	required string match_id = 3;			// 该次匹配关联的id
		
	optional MatchedInfo matched_info = 4;	// 直接匹配成功
	optional uint32 sequence_in_queue = 5;	// 当前排队序号		
}

message CancelQuickMatchReq {
	required BaseReq base_req = 1;
	required string match_id = 2;			// 匹配关联的id		
}

message CancelQuickMatchResp {
	required BaseResp base_resp = 1;
}

message QuickMatchKeepAliveReq {
	required BaseReq base_req = 1;
	required string match_id = 2;			// 匹配关联的id		
}

message QuickMatchKeepAliveResp {
	required BaseResp base_resp = 1;
	optional string match_id = 2;			// 如果匹配仍在继续, 返回与请求相同的match_id, 否则应当视为匹配停止, 不再继续发KeepAlive
	optional uint32 sequence_in_queue = 3;	// 当前所在队列位置
}

message GetQuickMatchOnlineUsersReq {
	required BaseReq base_req = 1;
}

message GetQuickMatchOnlineUsersResp {
	required BaseResp base_resp = 1;
	required uint32 online_users = 2;		// 正在匹配的人数
}

message MatchedInfo {
	required uint32 		channel_id = 1;			// 随机房间id
	required uint32 		peer_uid = 2;			// 对方的uid
	required string 		peer_account = 3;		// 对方的帐号
	required string 		peer_alias = 4;			// 对方的数字帐号
	required string 		peer_nickname = 5;		// 对方的昵称
	required uint32 		peer_game_id= 6;		// 对方匹配的游戏id
	required uint32 		peer_gender = 7;		// 对方的性别
	required string 		peer_game_name = 8;		// 对方匹配的游戏名称
	repeated QuickMatchGame peer_games = 9;			// 游戏列表
}

message QuickMatchResultNotification {
	optional MatchedInfo matched_info = 1;
	required string match_id = 2;					// 对应的匹配id
	repeated MatchedInfo matched_info_list = 3;		// 支持多人匹配
}

/*
service FindFriends {
	rpc FindFriendsGetMyInfo( FindFriendsGetMyInfoReq ) returns( FindFriendsGetMyInfoResp ) {}
	rpc FindFriendsUpdateMyInfo( FindFriendsUpdateMyInfoReq ) returns( FindFriendsUpdateMyInfoResp ) {}
	rpc FindFriendsUpdatePhotos( FindFriendsUpdatePhotosReq ) returns( FindFriendsUpdatePhotosResp ) {}
	rpc FindFriendsGetUserCards( FindFriendsGetUserCardsReq ) returns( FindFriendsGetUserCardsResp ) {}
	rpc FindFriendsOperateOnCards( FindFriendsOperateOnCardsReq ) returns( FindFriendsOperateOnCardsResp ) {}

	rpc GetQuickMatchConfiguration( GetQuickMatchConfigurationsReq ) returns( GetQuickMatchConfigurationsResp ) {}
}
*/
