syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/contact";

//好友来源
enum FRIEND_SRC_TYPE {
	FRIEND_SRC_DEFAULT = 0;			           // 默认，用户自己搜索账号添加
	FRIEND_SRC_GUILD_AUTO_ADD = 1;		       // 工会会长，系统自动添加
    FRIEND_SRC_INVITE_CODE = 2;                // 填写邀请码
    FRIEND_SRC_CONTACT = 3;                    // 通讯录
    FRIEND_SRC_GAME_INTERNAL = 4;              // 游戏内部
    FRIEND_SRC_WEIXIN_SHARE = 5;               // 微信分享
    FRIEND_SRC_QQ_SHARE = 6;                   // QQ分享
    FRIEND_SRC_USER_RECOMMEND = 7;             // 好友推荐
    FRIEND_SRC_FIND_FRIEND = 8;                // 扩圈
    FRIEND_SRC_FAST_MATHING = 9;               // 快速匹配
    FRIEND_SRC_GROUP_INTERNAL = 10;            // 群组
    FRIEND_SRC_GUILD_MEMBER_LIST = 11;         // 公会成员列表
    FRIEND_SRC_CIRCLE_CREATE_TOPIC_USER = 12;  // 圈子发帖者

    //channel
    FRIEND_SRC_USER_KH_CHANNEL_TYPE = 13;      // 个人开黑房
    FRIEND_SRC_HIGH_KH_CHANNEL_TYPE = 14;      // 高品质开黑房
    FRIEND_SRC_FUN_CHANNEL_TYPE = 15;          // 娱乐房

    //rank 
    FRIEND_SRC_RANK_LIST = 16;                 // 排行榜
	
    FRIEND_SRC_ICEBREAK_FRIEND = 17;           // 破冰匹配
    
    FRIEND_SRC_BIFOLLOW = 18;                  // 双向关注
} 

//添加好友
message AddFriendReq {
	required BaseReq base_req = 1;
	required string target_account= 2;
	required string verify = 3; // 验证信息
    optional uint32 friend_src_type=4; //FRIEND_SRC_TYPE
}

message AddFriendResp {
	required BaseResp base_resp = 1;
	required string target_account= 2;
}

//用户验证是否通过
message VerifyFriendReq {
	required BaseReq base_req = 1;
	required string account = 2;
	required bool is_verify = 3;
	required string reason = 4;
}

message VerifyFriendResp {
	required BaseResp base_resp = 1;
	required string target_account= 2;
}

//删除好友
message DeleteFriendReq {
	required BaseReq base_req = 1;
	required string target_account= 2;
}

// 删除好友响应
message DeleteFriendResp {
	required BaseResp base_resp = 1;
	required string target_account= 2;
}

//备注好友
message RemarkFriendReq{
    required BaseReq base_req = 1;
    required string user_account = 2;
    required string remark = 3;
	optional bool is_clean = 4; // 清除备注信息
}

message RemarkFriendResp{
    required BaseResp base_resp = 1;
    required string user_account = 2;
}

//拉黑
message BanFriendReq{
    required BaseReq base_req = 1;
    required string target_account = 2;
    required bool is_baned = 3;
}

message BanFriendResp{
    required BaseResp base_resp = 1;
    optional string target_account = 2;
    optional bool is_baned = 3;
}

//星标好友
message MarkFriendWithStarReq{
    required BaseReq base_req = 1;
    required string user_account = 2;
    required bool is_star = 3;
}

message MarkFriendWithStarResp{
    required BaseResp base_resp = 1;
}

//精确搜索联系人
message SearchContactReq{
    required BaseReq base_req = 1;
    required string key_word = 2;//phone or account or nick
}

message ContactBrief{
    required string face_md5 = 1;
    required uint32 sex = 2;
    required string signature = 3;
    required string nick_name = 4;
    required string account = 5;
    required uint32 uid = 6;
    required string account_alias = 7;
}

message SearchContactResp{
    required BaseResp base_resp = 1;
    repeated ContactBrief contact_brief = 2;
}

//查看他人的信息详情

message GetUserDetailReq {
	required BaseReq base_req = 1;
	required string target_account = 2; //他人的账号
}

// 用户兴趣群组
message UsersTGroup{
    required uint32 group_id              = 1;      // 群id
    required string group_name            = 2;		// 群名称
    required string portrait_md5          = 3; 		// 群头像
    optional string game_name             = 4;      // 游戏名
}

message GetUserDetailResp {
	required BaseResp base_resp = 1;
	required Contact contact = 2;
	repeated UsersTGroup user_tgroup_list = 3;
    optional string headwear_img = 4;           //头像挂饰key
}


//他人详情背景封面图片
message GetCoverReq {
	required BaseReq base_req = 1;
	required string target_account = 2; // 其他人的账号
}

message GetCoverResp {
	required BaseResp base_resp = 1;
	required bytes cover = 2;
}

message GetUserStatusReq {
    required BaseReq base_req = 1;
    optional uint32 target_uid = 2; //优先用uid，uid=0无效值
    optional string target_account = 3; 
    optional bool get_banned_forever_status = 4;
}

message GetUserStatusResp {
    required BaseResp base_resp = 1;
    optional bool is_banned_forever = 2;
}




