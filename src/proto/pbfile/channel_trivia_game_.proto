syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel";

//-------------房间的知识答题游戏 相关的协议 ---------------------//

message PhaseTimer{	
    required uint32 end_timestamp = 1; //倒计时结束的时间撮
}

message PhaseAnimation{
	enum AnimationId{
		//暂时hardcode三个动画id
		Animation_Reward = 1;		//奖金动画
		Animation_Middle = 2;		//答题过程中的过场动画
		Animation_Last_Question = 3;//最后一题前的动画
	}
    required uint32 anima_id = 1;    //动画id
    optional string text = 2;        //动画上显示的字符,如果有的话
}

message PhaseQuestion{
    required uint32 question_id = 1; //题目id
    required uint32 question_idx = 2;//题目序号
    required uint32 tip_time = 3;    //给主持的答题倒计时提示
    optional bool is_show_solution = 4;	//是否已经公布答案
}

message PhaseEntry{
	enum PhaseType{
	    phase_none  = 0;		//只有主持人BB的阶段
	    phase_timer = 1;		//播放定时器的阶段
	    phase_animation = 2;	//播放动画的阶段
	    phase_question  = 3;	//发送问题的阶段
	    phase_winner    = 4;	//展示获奖者的阶段
	}
    required uint32 phase_idx    = 1; // 阶段序号 0-未开始
    optional uint32 phase_type   = 2;  // 阶段类型 PhaseType
    optional string phase_name   = 3;  // 阶段名称
    optional bytes phase_bin    = 4;  // 阶段内容
}

message ChannelTriviaGameInfo{
    required uint32 act_id     	 = 1;
    required uint32 start_time   = 2;      // 开始时间
    required uint32 reward       = 3;      // 奖金
    required uint32 question_num = 4;		//题目数
}

// 特殊的进房间请求
message ChannelTriviaGameEnterReq
{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;
}

message ChannelTriviaGameEnterResp
{
	required BaseResp base_resp = 1;
	optional int64 server_time  = 2;              // 64bit 毫秒级 服务器当前时间
	required uint32 channel_id              = 3;
    required uint32 session_id              = 4;  // SDK语音房间id
	optional uint32 channel_display_id      = 5;  // 房间的显示ID
	optional uint32 channel_type            = 6;  // 频道类型, see enum ChannelType
	
	optional ChannelTriviaGameInfo game_info = 7;	// 活动信息
	optional PhaseEntry current_phase = 8;          // 房间当前阶段信息
	
	optional uint32 channel_member_cnt = 9;        // 房间用户人数信息
	optional uint32 remain_revive_cnt = 10;        // 个人生命值信息
	optional bool is_answer_qualify = 11;          // 本轮活动中是否有答题资格

}

// 进房前协议
message ChannelTriviaGamePreEnterReq
{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;
}

message ChannelTriviaGameAdimn{
	required uint32 uid = 1;
	required uint32 role = 2;
}

message ChannelTriviaGamePreEnterResp
{
	required BaseResp base_resp = 1;
	required uint32 channel_id  = 2;
	optional int64 server_time  = 3;	//秒，timestamp

	optional ChannelTriviaGameInfo game_info = 4;	// 活动信息
	optional PhaseEntry current_phase = 5;          // 房间当前阶段信息
	
	optional uint32 left_revive_cnt = 6;      // 个人生命值信息
	optional uint32 usr_reward_total = 7;		// 用户累计获得奖金,单位：分
	repeated ChannelTriviaGameAdimn admin_list = 8;			//房间管理员列表
}
// 答题
enum ETriviaGameAnswerResultStatus
{
	ENUM_TRIVIA_GAME_ANSWER_RESULT_CORRECT = 1; // 正确
	ENUM_TRIVIA_GAME_ANSWER_RESULT_ERROR   = 2; // 错误
	ENUM_TRIVIA_GAME_ANSWER_RESULT_TIMEOUT = 3; // 回答超时
};

enum ETriviaGameAnswerId
{
	ENUM_TRIVIA_GAME_ANSWER_ID_A = 1;  // 选项A
	ENUM_TRIVIA_GAME_ANSWER_ID_B = 2;  // 选项B
	ENUM_TRIVIA_GAME_ANSWER_ID_C = 3;  // 选项C
	ENUM_TRIVIA_GAME_ANSWER_ID_D = 4;  // 选项D
	
	ENUM_TRIVIA_GAME_ANSWER_ID_TIMEOUT = 255;  // 用户时间到了 还没有选答案
};
	
message ChannelTriviaGameQuestion
{
	required uint32 active_id  = 1;     // 活动ID
	required uint32 question_id = 2;    // 题目ID
	required uint32 question_idx = 3;	// 题目序号  第几题 1是第一题
	
	required string question_desc = 4;  // 题目描述
	repeated string options_list = 5;	// 题目选项
}

message ChannelTriviaGameAnswerReq
{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;
	required uint32 active_id  = 3;  // 活动ID
	required uint32 question_id = 4;  // 题目ID 
	required uint32 question_idx = 5; // 题目序号 第几题 1是第一题
	
	required uint32 answer_id   = 6;  // 回答的内容 ETriviaGameAnswerId 1=A 2=B 3=C
}

message ChannelTriviaGameAnswerResp
{
	required BaseResp base_resp = 1;
	required uint32 channel_id = 2;
	required uint32 active_id  = 3;   // 活动ID
	required uint32 question_idx = 4; // 题目序号 第几题 1是第一题
	
	required uint32 resulut_status = 5;    // ETriviaGameAnswerResultStatus 答题结果
	required uint32 correct_answer_id = 6; // 正确答案是多少 ETriviaGameAnswerId  1=A 2=B 3=C
	optional bool is_revive = 7;           // 如果 resulut_status 不是回答正确，则 该字段表示下一题该用户是否可以复活
	optional uint32 left_revive_cnt = 8;   // 如果 is_revive = ture这里表示剩余的复活次数
	
	optional uint32 histroy_max_pass_questioncnt = 9;   // 如果 回答不正确，又不能复活，表示给用户已经结束了本轮答题，那么显示用户的历史最高答题数目 最大12
	optional bool is_histroy_max_refreshed = 10;                    // histroy_max_pass_questioncnt 有效情况下 is_histroy_max_refreshed 表明是否是刷新记录
}

/*主持人流程条*/
message ChannelTriviaGameGetPhaseListReq{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;//权限检查用
}

message ChannelTriviaGameGetPhaseListResp{
	required BaseResp base_resp = 1;
	required ChannelTriviaGameInfo game_info = 2;
	repeated PhaseEntry phase_list = 3;
}

/*流程切换*/
message ChannelTriviaGamePhaseForwardReq
{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;//权限检查用
	required uint32 active_id = 3;
	optional uint32 phase_idx = 4; //流程序号
}

message ChannelTriviaGamePhaseForwardResp
{
	required BaseResp base_resp = 1;
	required uint32 phase_idx = 5;//下一阶段序号
}

/*显示答案*/
message ChannelTriviaGameShowSolutionReq{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;//权限检查用
	required uint32 active_id = 3;
	required uint32 question_id = 4; //题目ID
}

message ChannelTriviaGameShowSolutionResp{
	required BaseResp base_resp = 1;
}
