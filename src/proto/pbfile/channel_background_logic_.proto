syntax = "proto3";

package ga.channelbackgroundlogic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channelbackgroundlogic";

// 背景类型
enum BackgroundType {
    Forever = 0;        // 永久
    TimeLimit = 1;      // 限时
    Default = 2;        // 默认
}

message ChannelBackgroundInfo {
    uint64 background_id = 1;           // 背景id
    string background_name = 2;         // 背景名
    BackgroundType background_type = 3; // 背景类型
    bool is_in_using = 4;               // 是否正在使用中
    string background_url = 5;          // 背景图url
    string md5_sum = 6;                 // 背景图md5值
    bool is_new = 7;                    // 永久背景是否为新（72小时内上新标志）
    int64 start_time = 8;               // 限时背景开始时间
    int64 end_time = 9;                 // 限时背景结束时间
}

message GetCurChannelBackgroundInfoReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message GetCurChannelBackgroundInfoResp {
    BaseResp base_resp = 1;
    ChannelBackgroundInfo force_use_channel_background_info = 2; // 后台配置的需要强制应用到所有房间的背景
    ChannelBackgroundInfo user_use_channel_background_info = 3;  // 用户自己更换的房间背景(永久背景和限时背景，不包括默认背景)
}

message ChangeCurChannelBackgroundReq {
    BaseReq base_req = 1;
    uint32 uid = 2;
    uint32 channel_id = 3;
    uint64 background_id = 4;        // 若是切换到默认背景，则 background_id 传 0
}

message ChangeCurChannelBackgroundResp {
    BaseResp base_resp = 1;
}

message GetChannelBackgroundInfoListReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message GetChannelBackgroundInfoListResp {
    BaseResp base_resp = 1;
    repeated ChannelBackgroundInfo forever_channel_background_list = 2;
    repeated ChannelBackgroundInfo time_limit_channel_background_list = 3;
    int64 version = 4;
}

message CheckChannelBackgroundUpdateReq {
    BaseReq base_req = 1;
    uint32 uid = 2;
}

message CheckChannelBackgroundUpdateResp {
    BaseResp base_resp = 1;
    int64 version = 2;
}
