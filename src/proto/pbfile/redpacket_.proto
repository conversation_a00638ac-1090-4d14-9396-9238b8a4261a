syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/redpacket";

/**
 * 红包活动定义
 */
message RedPacketActivityInfo
{
    required uint32 id = 1;             // 红包活动事件ID 为0表示需要创建一个红包活动事件
    required string name = 2;           // 红包活动名称

    required uint64 begin_time = 3;     // 开始时间, 精确至`秒`
    required uint64 end_time = 4;       // 结束时间, 精确至`秒`

    optional uint32 combo_timeout = 5;  // 主阶段内, Combo中断的超时时间, 精确至`毫秒`
    optional string url = 6;            // 跳转 url
}

/**
 * 红包阶段定义
 */
message RedPacketStageInfo
{
    enum StageType {
        DEFAULT = 1;          // 默认阶段 即什么也不会干的阶段 或者其他不连续的空余时间阶段
        LOTTERY = 2;          // 执行抽奖阶段, 主阶段, 以此来决定开始前倒计时显示
        MASS_GUILDBUFF = 3;   // 攒公会BUFF阶段
        PREPARE = 4;          // 准备阶段
    }

    required uint32 stage_id = 1;      // 阶段ID 为0表示需要创建一个阶段信息
    required uint32 activity_id = 2;   // 该阶段对应的红包活动事件ID
    required uint32 type  = 3;         // 阶段类型 see StageType

    optional string name = 4;          // 阶段名称
    optional uint64 begin_time = 5;    // 阶段开始时间, 精确至`秒`
    optional uint64 end_time = 6;      // 阶段结束时间, 精确至`秒`
    repeated string ad_text_list = 7;  // 阶段内的提示广告语

    // 阶段内发送抢红包请求的最小间隔, 精确至`毫秒`
    // 客户端必须严格按照此间隔控制请求频率
    required uint32 min_req_interval = 8;
}

message RandomRedPacketReq {

}

message RandomRedPacketResp {

}

// 礼物
// 礼物类型
enum EREDPACKET_GIFT_TYPE {
	EREDPACKET_GIFT_NOMAL = 1;     // 普通礼物
	EREDPACKET_GIFT_DEBRIS = 2;    // 碎片型礼物
    EREDPACKET_GIFT_VOUCHER = 3;   // 代金券
}

message RedPacketGiftInfoBase
{
	required uint32 gift_id = 1;             // 礼物ID 为0表示需要创建一个礼物大类
	required string gift_name = 2;           // 礼物大类名称 比如'10个Q币', 'TT公会4位短号', '传奇50元代金券', 'CF黄金M4枪'
	required uint32 gift_type = 3;           // 礼物类型 see REDPACKET_GIFT_TYPE
	required uint32 gift_platform = 4;       // 礼物支持的平台类型 see REDPACKET_GIFT_PLATFORM_TYPE
}

message RedPacketGuildInfoBase
{
	required uint32 guild_id = 1;
	required uint32 guild_buff = 2;       //工会中奖buff, 千分比
}

//拉红包
message PullRedPacketReq
{
    required BaseReq base_req = 1;
}

message PullRedPacketResp
{
    required BaseResp base_resp = 1;
	required uint32 activity_id = 2;     //当前的活动id
	required uint32 stage_id = 3;        //当前的阶段id
	optional RedPacketGiftInfoBase gift = 4; //中奖才会有数据
	optional RedPacketGuildInfoBase guild_buff_info = 5; //工会信息
}
