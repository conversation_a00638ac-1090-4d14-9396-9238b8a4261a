syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/sample";

message SampleAddReq {
	required BaseReq base_req = 1;
	required uint32 lhs = 2;
	required uint32 rhs = 3;
}

message SampleAddResp {
	required BaseResp base_resp = 1;
	required uint32 result = 2;
}

message SampleNotify {
	required BaseResp base_resp = 1;
	required string message = 2;
}

message SampleDispatchReq {
    required BaseReq base_req = 1;
    required uint32 cmd = 2;
    required string data = 3;
}

message SampleDispatchResp {
    required BaseResp base_resp = 1;
    required uint32 cmd = 2;
    required string data = 3;
}
