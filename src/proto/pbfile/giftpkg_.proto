syntax="proto2";

package ga;
import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/giftpkg";

enum GIFT_PKG_STATUS {
	NONE = 0;
	GUILD_FETCHED = 1;		// 序列号分派给公会
	USER_FETCHED = 2;		// 用户已领取
	GUILD_APPLYING = 3;		// 公会申请中
	OFFICAL_REJECT = 4;		// 官方拒绝
	OFFICAL_ACCEPT = 5;		// 官方通过
	USER_CAN_FETCHED = 6;	// 用户可以领取
	GIFT_PKG_EXCEED = 7;	// 已过期
	GUILD_CAN_APPLY = 8;	// 公会可以申请
    TAOHAO_OVER = 9;        // 淘完了
    USER_FETCH_REDPKG = 10;	// 用户已领取了这个红包
    PAYING = 11;			// 扣费中
}

//----------------------------------------
// 查看我的公会的礼包列表
//----------------------------------------
message GetMyGuildGiftpkgListReq {
	required BaseReq base_req = 1;
}

message GuildGiftpkg {
	required GiftPackage pkg = 1;
	required uint32 remain_num = 2;
	required uint32 recycle_count = 3;
	required uint32 total_num = 4;
	required uint32 my_status = 5;
	required uint32 guild_status = 6;
	optional string my_serial = 7;
}

message GetMyGuildGiftpkgListResp {
	required BaseResp base_resp = 1;
	repeated GuildGiftpkg gift_pkg_list = 2;
}



//----------------------------------------
// 查看我的礼包列表 (2.9.0废弃)
//----------------------------------------
message GetMyGiftpkgListReq {
	required BaseReq base_req = 1;
}

// 序列号
message MyGiftpkgSerial {
	required GiftPackage pkg = 1;		// 礼包
	required string serial_code = 2;	// 序列号
	required uint32 status = 3;			
}

message GetMyGiftpkgListResp {
	required BaseResp base_resp = 1;
	repeated MyGiftpkgSerial gift_pkg_list = 2;
}



//----------------------------------------
// 会员领取公会礼包 (2.9.0废弃)
//----------------------------------------
message FetchGuildGiftpkgReq {
	required BaseReq base_req = 1;
	required uint32 gift_pkg_id = 2;
}

message FetchGuildGiftpkgResp {
	required BaseResp base_resp = 1;
	required string serial_code = 2;
	required uint32 game_id = 3;
    required uint32 gift_pkg_id = 4;
}

//----------------------------------------
// 会员抢红包 (2.9.0废弃)
//----------------------------------------
message FetchRedpkgReq {
	required BaseReq base_req = 1;
	required uint32 gift_pkg_id = 2;		// 礼包id
	required uint32 red_pkg_id = 3;			// 红包id
    required uint32 guild_id = 4;
}

message FetchRedpkgResp {
	required BaseResp base_resp = 1;
	required string serial_code = 2;
	required uint32 game_id = 3;
}

//----------------------------------------
// 会长发红包 (2.9.0废弃)
//----------------------------------------
message SendGuildRedpkgReq {
	required BaseReq base_req = 1;
	required uint32 gift_pkg_id = 2;
	required uint32 num = 3;
	required string to_account = 4;
}

message SendGuildRedpkgResp {
	required BaseResp base_resp = 1;
}

//----------------------------------------
// 查红包详情 (2.9.0废弃)
//----------------------------------------
message GetGuildRedpkgDetailReq {
	required BaseReq base_req = 1;
	required uint32 gift_pkg_id = 2;		// 礼包id
	required uint32 red_pkg_id = 3;			// 红包id
}

message GetGuildRedpkgDetailResp {
	enum STATUS {
		NONE = 0;
		// todo
	}
	required BaseResp base_resp = 1;		
	required uint32 status = 2;			// 是否领取过、是否可领取
	required uint32 left_num = 3;		// 红包剩余数量
	required uint32 total_num = 4;		// 红包总数
	required string sender_account = 5;	// 红包发送者的帐号
    required uint32 gift_pkg_id = 6;		// 礼包id
    required uint32 red_pkg_id = 7;			// 红包id
    required uint32 game_id = 8; //游戏id
    required string gift_pkg_name = 9; //礼包名称
    required uint32 exchange_begin = 10;//礼包开始兑换时间
    required uint32 exchange_end = 11;//礼包到期时间
    required string gift_pkg_intro = 12;//礼包简介
    required string serial_code = 13;//礼包兑换码
}

//----------------------------------------
// 查红包领取详情 (2.9.0废弃)
//----------------------------------------
message RedPkgFetchedUserInfo{
    required string account = 1;
    required uint32 fetch_time = 2;
}

message GetGuildRedpkgFetchDetailReq {
	required BaseReq base_req = 1;
	required uint32 gift_pkg_id = 2;		// 礼包id
	required uint32 red_pkg_id = 3;			// 红包id
}

message GetGuildRedpkgFetchDetailResp {
	required BaseResp base_resp = 1;
	repeated RedPkgFetchedUserInfo fetch_account_list = 2;		// 抢过红包的会员信息
	required uint32 left_num = 3;		// 红包剩余数量
	required uint32 total_num = 4;		// 红包总数
    required uint32 gift_pkg_id = 5;		// 礼包id
    required uint32 red_pkg_id = 6;			// 红包id
}

//----------------------------------------
// 淘号 (2.9.0废弃)
//----------------------------------------
message GuildTaohaoReq {
	required BaseReq base_req = 1;
	required uint32 gift_pkg_id = 2;
}

message GuildTaohaoResp {
	required BaseResp base_resp = 1;
	required string serial_code = 2;
	required uint32 game_id = 3;
    required uint32 gift_pkg_id = 4;
}

//----------------------------------------
// 查询公会可申请的礼包 (2.9.0废弃)
//----------------------------------------
message GuildGetAppliableGiftpkgReq {
	required BaseReq base_req = 1;
	repeated uint32 game_id = 2;
}

message GuildGetAppliableGiftpkgResp {
	required BaseResp base_resp = 1;
	repeated GuildGiftpkg gift_pkg_list = 2;
}

//----------------------------------------
// 公会申请礼包 (2.9.0废弃)
//----------------------------------------
message GuildApplyGiftpkgReq {
	required BaseReq base_req = 1;
	required uint32 gift_pkg_id = 2;
}

message GuildApplyGiftpkgResp {
	required BaseResp base_resp = 1;
    required uint32 gift_pkg_id = 2;
}


//----------------------------------------
// 公会查询礼包详情 (2.9.0废弃)
//----------------------------------------
message GetGiftpkgDetailReq {
	required BaseReq base_req = 1;
	required uint32 giftpkg_id = 2;
}

message GetGiftpkgDetailResp {
	required BaseResp base_resp = 1;
	required GuildGiftpkg pkg = 2;
	required string content = 3;
	required string usage = 4;
}

//----------------------------------------
// 公会查询申请礼包历史记录 (2.9.0废弃)
//----------------------------------------
message GetGiftpkgApplyHistoryReq {
	required BaseReq base_req = 1;
}

message GiftpkgApply {
	enum STATUS {
		TODO = 0;
		// 申请中
		// 已通过并上架
		// 已拒绝
	}
	required GiftPackage pkg = 1;
	required uint32 apply_id = 2;
	required uint32 status = 3;
	required uint32 apply_time = 4;
}

message GetGiftpkgApplyHistoryResp {
	required BaseResp base_resp = 1;
	repeated GiftpkgApply apply_list = 2;
}

message GetGiftpkgApplyDetailReq{
    required BaseReq base_req = 1;
    required uint32 apply_id = 2;
}

message GetGiftpkgApplyDetailResp{
    required BaseResp base_resp = 1;
    required GiftpkgApply  apply = 2;
    required string content = 3;
    required string usage = 4;
}

//----------------------------------------
// 查看我的公会仓库的礼包列表 (2.9.0废弃)
//----------------------------------------
message GetGuildStorageGiftpkgListReq {
	required BaseReq base_req = 1;
}

message GuildStorageGiftpkg {
	required GiftPackage pkg = 1;
	required uint32 remain_num = 2;
	required uint32 total_num = 3;
    required uint32 apply_pass_time = 4;
}

message GetGuildStorageGiftpkgListResp {
	required BaseResp base_resp = 1;
	repeated GuildStorageGiftpkg gift_pkg_list = 2;
}


//----------------------------------------
// 上报使用淘号 (2.9.0废弃)
//----------------------------------------
message ReportUseTaohaoReq {
	required BaseReq base_req = 1;
	required uint32 gift_pkg_id = 2;
	required uint32 guild_id = 3;
	required string serial_code = 4;
}

message ReportUseTaohaoResp {
	required BaseResp base_resp = 1;
}



//----------------------------------------
// 查公会礼包价格 (2.9.0废弃)
//----------------------------------------
message GetGuildGiftpkgPriceReq {
	required BaseReq base_req = 1;
	required uint32 gift_pkg_id = 2;
}

message GetGuildGiftpkgPriceResp {
	required BaseResp base_resp = 1;
	required uint32 gift_pkg_id = 2;
	required uint32 red_diamond = 3;
}

//----------------------------------------
// 消耗红钻领礼包 (2.9.0废弃)
//----------------------------------------
message FetchGiftpkgCostRedDiamondReq {
	required BaseReq base_req = 1;
	required uint32 gift_pkg_id = 2;
	required uint32 red_diamond = 3;		// 用户当前看到的红钻价格
}

message FetchGiftpkgCostRedDiamondResp {
	required BaseResp base_resp = 1;
    required uint32 gift_pkg_id = 2;
	required string serial_code = 3;
	required uint32 game_id = 4;
}


