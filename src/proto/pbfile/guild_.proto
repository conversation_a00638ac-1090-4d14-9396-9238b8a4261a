syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/guild";

//创建公会
message GuildCreateReq {
    required BaseReq base_req = 1;
    required string name = 2;            //名称
    required string desc = 3;            //简介
    required string guild_prefix = 4;
}


message GuildCreateResp {
    required BaseResp base_resp = 1;
	required GuildDetailInfo detail_info = 2;		// 工会详情
}

//获取公会详细信息
message GuildGetInfoReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
}

message GuildGetInfoResp {
    required BaseResp base_resp = 1;
    required GuildDetailInfo guild_detail = 2;	// 工会详情
}

//修改公会信息
message GuildModifyInfoReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    optional string desc = 3;				// 简介
    optional string guild_name = 4;			// 公会名称
    optional string guild_prefix = 5;		// 公会马甲
    optional string guild_manifesto = 6;	// 公会宣言
}

message GuildModifyInfoResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required GuildDetailInfo detail_info = 3;
}

//搜索公会
message GuildSearchReq {
	enum KEY_WORD_TYPE {
		GUILD_NAME_SEARCH = 2;
		GUILD_GAME_ID_SEARCH = 3;
		GUILD_ID_SEARCH = 4;
	}
    required BaseReq base_req = 1;
    required string key_word = 2;		// 公会ID准确匹配、（名称模糊匹配）
    optional uint32 key_word_type = 3;			// 0：老版本，2：公会名称关键字搜索
}

message GuildBaseInfo {
    required uint32 guild_id = 1;
    required uint32 guild_display_id = 2;
    required string name = 3;
    required uint32 need_verify = 4;	// 是否需要验证
    required uint32 mem_count = 5;		// 公会总人数
    required uint32 gift_pkg_count = 6;	// 公会礼包总数
    optional string guild_manifesto = 7; // 公会宣言
    optional uint32 game_gift_pkg_count = 8; // 某款公会游戏的礼包总数
    optional uint32 guild_star_level = 9;    // 公会星级 只在搜索结果中返回
	optional uint32 home_channel_id = 10;       // 公会主房间的ID
}

message GuildSearchResp {
	enum RESULT_TYPE {
		RESULT_GUILD_NAME = 2;
		RESULT_GUILD_GAME_ID = 3;
		RESULT_GUILD_ID = 4;
	}
    required BaseResp base_resp = 1;
    required string key_word = 2;
    repeated GuildBaseInfo guilds = 3;
    optional uint32 result_type = 4;	// 2：公会名称，3：游戏ID，4：公会ID
    optional uint32 result_limit = 5;   // 当result_type=3时，只取部分结果
}

message GameGuildListReq {
	required BaseReq base_req = 1;
	required uint32 game_id = 2;
}

message GameGuildListResp{
	required BaseResp base_resp = 1;
    required uint32 game_id = 2;
    repeated GuildBaseInfo guilds = 3;
}

//=======================================
//
// 公会排行榜
//
//=======================================
message TopGuildListReq{
    required BaseReq base_req = 1;
}

message TopGuildListResp{
    required BaseResp base_resp = 1;
    repeated GuildBaseInfo guilds = 2;
}

message GuildAlbum {
	enum ALBUM_TYPE {
		NOT_DEFAULT = 0;
		IS_DEFAULT = 1;
	}
    required uint32 album_id = 1;
    required string name = 2;
    required uint32 photo_count = 3;
    required string thumb_url = 4;        	// 缩略图url
    required string creator_account = 5; 	// 创建者帐号
    required uint32 create_time = 6;		// 创建时间
    required uint32 album_type = 7;			// 相册类型
    required uint32 last_update_time = 8;	// 最后更新的时间，更新包括创建相册、相册改名、上传新相片。不包括删除相片
}

//创建相册
message GuildAlbumCreateReq {
    required BaseReq base_req = 1;
    required string name = 2;	//	 相册名称
}

message GuildAlbumCreateResp {
    required BaseResp base_resp = 1;
    required GuildAlbum album = 2;
}

//获取相册列表
message GuildGetMyGuildAlbumListReq {
    required BaseReq base_req = 1;
    required uint32 result_offset = 2;           //分页偏移量
    required uint32 result_count = 3;            //分页大小
}

message GuildGetMyGuildAlbumListResp {
    required BaseResp base_resp = 1;
    repeated GuildAlbum albums = 2;
    required uint32 result_offset = 3;           //分页偏移量
    required uint32 result_count = 4;           //分页大小
}

//删除相册
message GuildDeleteAlbumReq {
    required BaseReq base_req = 1;
    required uint32 album_id = 2;
}

message GuildDeleteAlbumResp {
    required BaseResp base_resp = 1;
    required uint32 album_id = 2;
}

//修改相册名称
message GuildModifyAlbumNameReq {
    required BaseReq base_req = 1;
    required uint32 album_id = 2;
    required string name = 3;		//	 相册名称
}

message GuildModifyAlbumNameResp {
    required BaseResp base_resp = 1;
    required uint32 album_id = 2;
    required string name = 3;
}

//获取本公会相片列表
message GuildGetPhotoListReq {
    required BaseReq base_req = 1;
    required uint32 album_id = 2;
    required uint32 result_offset = 3;           //分页偏移量
    required uint32 result_count = 4;            //分页大小
}

message GuildGetPhotoListResp {
    required BaseResp base_resp = 1;
    required uint32 album_id = 2;
    repeated GuildPhoto photos = 3;
    required uint32 result_offset = 4;           //分页偏移量
    required uint32 result_count = 5;           //分页大小
}

//删除相片
message GuildDeletePhotoReq {
    required BaseReq base_req = 1;
    required uint32 album_id = 2;
    repeated uint32 photo_id_list = 3;
}

message GuildDeletePhotoResp {
    required BaseResp base_resp = 1;
    required uint32 album_id = 2;
    repeated uint32 photo_id_list = 3;
}

// 上传照片
message GuildAddNewPhotoReq {
    required BaseReq base_req = 1;
   	required uint32 album_id = 2;		// 相册ID
   	repeated string url_list = 3;		// 相片url
   	required uint32 client_msg_id = 4;	// 去重id
}

message GuildAddNewPhotoResp {
	required BaseResp base_resp = 1;
   	required uint32 album_id = 2;		// 相册ID
   	required uint32 client_msg_id = 3;	// 去重id
}

// 相片动态
message GuildNews {
	required uint32 news_id = 1;			// 动态id
	required string creator_account = 2;	// 发布者的帐号
	required uint32 album_id = 3;			// 相册id
	repeated string photo_url_list = 4;		// 相片地址
	required string content = 5;			// 动态内容
	required string album_name = 6;			// 相册名称
	required uint32 create_time = 7;		// 动态时间
	required string creator_nick = 8;		// 发布者的昵称
}

// 查公会动态 历史原因协议名不改, 点草ljj
message GuildGetPhotoNewsListReq {
    required BaseReq base_req = 1;
	required uint32 offset_news_id = 2;
	required uint32 page_size = 3;
}

message GuildGetPhotoNewsListResp {
    required BaseResp base_resp = 1;
    required uint32 offset_news_id = 2;
    required uint32 page_size = 3;
	repeated GuildNews news_list = 4;
}

message GuildDeletePhotoNewsReq {
    required BaseReq base_req = 1;
	required uint32 news_id = 2;
}

message GuildDeletePhotoNewsResp {
    required BaseResp base_resp = 1;
	required uint32 news_id = 2;
}

message GuildGetDefaultAlbumPhotoListReq {
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
	required uint32 result_offset = 3;           //分页偏移量
    required uint32 result_count = 4;            //分页大小
}

message GuildGetDefaultAlbumPhotoListResp {
	required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
    repeated GuildPhoto photos = 3;
    required uint32 result_offset = 4;           //分页偏移量
    required uint32 result_count = 5;          	 //分页大小
}

//1.8.1复用协议:拉取公会成员列表
//获取会员列表
message GuildGetMemberListReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 begin_id = 3; // 开始查询的编号 用于翻页 第一页可以填0
    required uint32 req_cnt = 4;   // 每次获取群列表最多需要多少条 一般填50
	optional string search_keyword = 5;  //当此字段有值时，为搜索群成员功能
}

//1.8.1复用协议:拉取公会成员列表
message GuildGetMemberListResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;

    //这里不会返回owner_guild_group_id_list和manager_guild_group_id_list
    repeated GuildMember members = 3;
	required uint32 begin_id = 4;
}

// 1.8.1 新增
//获取公会成员信息
message GuildGetMemberInfosReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;

    repeated uint32 member_uid_list = 3; //批量uid查
    //如果从IM页直接查询，只知道account，不知道uid，所以还要能通过account查询
    repeated string member_account_list = 4; //批量account查
}

// 1.8.1 新增
message GuildGetMemberInfosResp {
    required BaseResp base_resp = 1;
    repeated GuildMember member_list = 2;
}

//添加公会游戏
message GuildAddGameReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
	repeated uint32 game_id_list = 3;		// 游戏ID
}

message GuildAddGameResp {
    required BaseResp base_resp = 1;
    repeated Game game_list = 2;		// 返回公会所有游戏
}

enum GuildJoinApplySourceType {
    GUILD_JOIN_APPLY_SOURCE_NOMAL = 1;   // 普通申请加公会
    GUILD_JOIN_APPLY_SOURCE_RECRUIT = 2; // 从招募入口申请加入公会 
}

//申请加入公会
message GuildJoinReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required string verify = 3;		 // 验证消息
    optional uint32 source_type = 4;    // see GuildJoinApplySourceType
    optional uint32 source_type_id = 5; // 如果 source_type == GUILD_JOIN_APPLY_SOURCE_RECRUIT 那么这里需要填写招募对应的招募ID
}

message GuildJoinResp {
    required BaseResp base_resp = 1;
    optional GuildDetailInfo guild_info = 2;		// 工会详情，如果是直接加入公会，则会返回这个结构
}

//退出公会
message GuildQuitReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
}

message GuildQuitResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
}

//解散公会
message GuildDismissReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
}

message GuildDismissResp {
    required BaseResp base_resp = 1;
}

//处理加入申请
message GuildHandleJoinReq {
    required BaseReq base_req = 1;
    required uint32 apply_id = 2;			//申请记录id
    required string applicant_account = 3;  //申请人帐号
    required bool agree = 4;             	//是否同意
	optional bool set_black_list = 5;		// 0.不加入黑名单 1.加入黑名单
}

message GuildHandleJoinResp {
    required BaseResp base_resp = 1;
    required uint32 apply_id = 2;
    required string applicant_account = 3;    //申请人帐号
    required bool agree = 4;
}

// 用户二次确认加入工会
message GuildConfirmJoinReq {
	required BaseReq base_req = 1;
	required uint32 apply_id = 2;
}

message GuildConfirmJoinResp {
	required BaseResp base_resp = 1;
    required GuildDetailInfo guild_info = 2;		// 工会详情
}

//公会签到
message GuildCheckInReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
}

message GuildCheckInResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 days = 3; //连续签到天数
	optional uint32 supplement_days = 4; // 可补签的天数
	optional uint32 supplement_price = 5; // 补签费用
	optional uint32 supplemented_days = 6; // 补签后连续签到天数
	optional int32 member_contribution_added = 7;	// 增加的个人贡献
	optional int32 guild_contribution_added = 8;	// 增加的公会贡献
	optional uint32 to_bonus_period = 9;	// 连签加成周期(仅显示用，不是本次签到的天数)
	optional int32 to_bonus_contribution = 10;	// 连签加成贡献(仅显示用，不是本次签到增加的贡献)
}

//公会补签
message GuildCheckInSupplementReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
}

message GuildCheckInSupplementResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 days = 3;
	optional int32 member_contribution_added = 4;	// 增加的个人贡献
	optional int32 guild_contribution_added = 5;	// 增加的公会贡献
	optional uint32 to_bonus_period = 6;	// 连签可加成周期(仅显示用，不是本次签到的天数)
	optional int32 to_bonus_contribution = 7;	// 连签可加成贡献(仅显示用，不是本次签到增加的贡献)
}

//签到列表
message GuildGetCheckInListReq {
    required BaseReq base_req = 1;
	optional uint32 page = 2;
	optional uint32 count = 3;
}

message GuildGetCheckInListResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    repeated GuildCheckin checkin = 3;
}

// 查询申请工会的状态列表
message GuildApplyStatusListReq {
	required BaseReq base_req = 1;
}

message GuildApplyStatusListResp {
	required BaseResp base_resp = 1;
	repeated GuildApplyStatus status_list = 2;
}

//获取公会游戏详情
message GuildGetGameInfoReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 game_id = 3;
}

message GuildGetGameInfoResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 game_id = 3;
    required string name = 4;
    //待完善
}

//获取游戏群
message GuildGetGameGroupListReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 game_id = 3;
    enum GroupType {
        MY = 1;
        ALL = 2;
    }
    required uint32 type = 4;  //我参与的群 全部群
    required uint32 result_offset = 5;           //分页偏移量
    required uint32 result_count = 6;            //分页大小
	required uint32 total_num = 7;				// type对应的群的总数
}



message GuildGetGameGroupListResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 game_id = 3;
    required uint32 type = 4;
    required uint32 result_offset = 5;
    required uint32 result_count = 6;            //分页大小
    repeated GuildGroup groups = 7;
	//	群总数？
}

//创建游戏群
message GuildCreateGameGroupReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 game_id = 3;
    required string name = 4;
    required uint32 need_verify = 5;	//0不需要验证，1需要验证，2不允许任何人加入
}

message GuildCreateGameGroupResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 game_id = 3;
    required uint32 group_id = 4;
}

//解散游戏群
message GuildDismissGameGroupReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
}

message GuildDismissGameGroupResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
}

//获取游戏群信息
message GuildGetGameGroupInfoReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
}

message GuildGetGameGroupInfoResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
    required string name = 4;
    required uint32 member_count = 5;
    //待完善
}



//退出游戏群
message GuildQuitGameGroupReq {
    required BaseReq base_req = 1;
    required uint32 group_id = 2;
}

message GuildQuitGameGroupResp {
    required BaseResp base_resp = 1;
    required uint32 group_id = 2;
}



//获取群成员列表
// 1.8.1 开始不用啦
message GuildGetGroupMemberListReq {

    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
}

// 1.8.1 开始不用啦
message GuildGetGroupMemberListResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
    repeated GuildGroupMember members = 4;
}

// 1.8.1 新增
// 获取群成员列表
message GuildGetGroupMemberListReqV2 {
     enum SortType {
        DEFAULT = 0;        // 默认排序
        ONLINE_FIRST = 1;   // 在线优先
    }
    
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
    required uint32 begin_id = 4; // 开始查询的编号 用于翻页 第一页可以填0
    required uint32 req_cnt = 5;   // 每次获取群列表最多需要多少条 一般填50
	optional string search_keyword = 6;  //当此字段有值时，为搜索群成员功能
    optional SortType sort_type = 7; 
    optional bool admin_only = 8;   // 只拉管理员
}

// 1.8.1 新增
message GuildGetGroupMemberListRespV2 {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
	required uint32 begin_id = 4;
    //这里GuildMember不会返回owner_guild_group_id_list和manager_guild_group_id_list
    repeated GuildGroupMemberV2 members = 5;
}

//增加群管理
message GuildAddGroupAdminReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
    repeated uint32 uids = 4;
}

message GuildAddGroupAdminResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
    repeated uint32 uids = 4;
}

//删除群管理
message GuildDeleteGroupAdminReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
    repeated uint32 uids = 4;
}

message GuildDeleteGroupAdminResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
    repeated uint32 uids = 4;
}

//群禁言
message GuildGroupMuteReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
    repeated uint32 uids = 4;
    repeated string account_list = 5;
	optional uint32 mute_second = 6; // 禁言的秒数 如果为0 或者不存在表示永久
}

message GuildGroupMuteResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
    repeated uint32 uids = 4;
    repeated string account_list = 5;
	optional uint32 mute_second = 6; // 禁言的秒数 如果为0 或者不存在表示永久
}

//恢复发言
message GuildGroupUnmuteReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
    repeated uint32 uids = 4;
    repeated string account_list = 5;
}

message GuildGroupUnmuteResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
    repeated uint32 uids = 4;
    repeated string account_list = 5;
}

message GuildGetGamePackageListReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 game_id = 3;
    required uint32 result_offset = 4;           //分页偏移量
    required uint32 result_count = 5;            //分页大小
}

message GuildGamePackage {
    required uint32 guild_id = 1;
    required uint32 game_id = 2;
    required uint32 package_id = 3;
    required string name = 4;
}

message GuildGetGamePackageListResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 game_id = 3;
    required uint32 result_offset = 4;           //分页偏移量
    required uint32 result_count = 5;            //分页大小
    repeated GuildGamePackage packages = 6;
}

message GuildGetGameGuideListReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 game_id = 3;
    required uint32 result_offset = 4;           //分页偏移量
    required uint32 result_count = 5;            //分页大小
}

message GuildGameGuide {
    required uint32 guild_id = 1;
    required uint32 game_id = 2;
    required uint32 guide_id = 3;
    required string title = 4;
    required string content = 5;
    required uint32 createDate = 6;
}

message GuildGetGameGuideListResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 game_id = 3;
    required uint32 result_offset = 4;           //分页偏移量
    required uint32 result_count = 5;            //分页大小
    repeated GuildGameGuide guides = 6;
}

message GuildAddAdminReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;
    required uint32 permission_bit = 3;
}

message GuildAddAdminResp {
    required BaseResp base_resp = 1;
    required uint32 uid = 2;
    required uint32 permission_bit = 3;
}

message GuildDeleteAdminReq {
    required BaseReq base_req = 1;
    repeated uint32 uid_list = 2;
}

message GuildDeleteAdminResp {
    required BaseResp base_resp = 1;
    repeated uint32 uid_list = 2;
}

message GuildSetGroupOwnerReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;
    required uint32 group_id = 3;
}

message GuildSetGroupOwnerResp {
    required BaseResp base_resp = 1;
    required uint32 uid = 2;
    required uint32 group_id = 3;
}

message GuildDeleteGroupOwnerReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;
    required uint32 group_id = 3;
}

message GuildDeleteGroupOwnerResp {
    required BaseResp base_resp = 1;
    required uint32 uid = 2;
    required uint32 group_id = 3;
}

message GuildApplyJoinGroupReq {
    required BaseReq base_req = 1;
    required uint32 group_id = 2;
    required string message = 3;
}

message GuildApplyJoinGroupResp {
    required BaseResp base_resp = 1;
    required uint32 group_id = 2;
    required uint32 need_verify = 3;
}

message GuildHandleJoinGroupReq {
    required BaseReq base_req = 1;
    required string apply_account = 2;
    required uint32 apply_id = 3;
    required bool is_agree = 4;
}

message GuildHandleJoinGroupResp {
    required BaseResp base_resp = 1;
    required string apply_account = 2;
    required uint32 apply_id = 3;
    required bool is_agree = 4;
    required uint32 group_id = 5;
}

message GuildAddGroupMemberReq {
    required BaseReq base_req = 1;
    required uint32 group_id = 2;
    repeated uint32 uid_list = 3;
}

message GuildAddGroupMemberResp {
    required BaseResp base_resp = 1;
    required uint32 group_id = 2;
    repeated uint32 uid_list = 3;
}

message GuildDeleteGroupMemberReq {
    required BaseReq base_req = 1;
    required uint32 group_id = 2;
    repeated uint32 uid_list = 3;
    repeated string account_list = 4;
}

message GuildDeleteGroupMemberResp {
    required BaseResp base_resp = 1;
    required uint32 group_id = 2;
    repeated uint32 uid_list = 3;
    repeated string account_list = 4;
}

message GuildDeleteMemberReq {
    required BaseReq base_req = 1;
    repeated uint32 uid_list = 2;
    repeated string account_list = 3;
	optional bool set_black_list = 4;			// 0.不加入黑名单 1.加入黑名单
}

message GuildDeleteMemberResp {
    required BaseResp base_resp = 1;
    repeated uint32 uid_list = 2;
    repeated string account_list = 3;
}

// 查询公会公告历史
message GuildGetNoticeListReq {
    required BaseReq base_req = 1;
	required uint32 guild_id = 2;		// 工会ID
	required uint32 offset = 3;			//
	required uint32 page_size = 4;
}

message GuildGetNoticeListResp {
    required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
	repeated GuildNotice notice_list = 3;		// 公告列表
	required uint32 offset = 4;
	required uint32 page_size = 5;
	required uint32 total = 6;					// 公告总数
}

message GuildPublishNoticeReq{
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 notice_type = 3;         //公告类型
    required string notice_title = 4;  //公告标题
    required string notice_content = 5;  //公告内容
}

message GuildPublishNoticeResp {
    required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
}

message GuildModifyNoticeReq{
	required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 notice_id = 3;
    required uint32 notice_type = 4;         //公告类型
    required string notice_title = 5;  //公告标题
    required string notice_content = 6;  //公告内容
}

message GuildModifyNoticeResp{
	required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
	required uint32 notice_id = 3;
}

message GuildDeleteNoticeReq{
	required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 notice_id = 3;
}

message GuildDeleteNoticeResp{
	required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
	required uint32 notice_id = 3;
    optional GuildNotice next_notice = 4;
}

// 单查群成员
// 1.8.1 不用这个协议啦
message GuildGetGroupMemberReq {
	required BaseReq base_req = 1;
	required uint32 group_id = 2;
	required uint32 member_uid = 3;
}

// 1.8.1 不用这个协议啦
message GuildGetGroupMemberResp {
	required BaseResp base_resp = 1;
	required uint32 group_id = 2;
	required uint32 member_uid = 3;
	required GuildGroupMember group_mem = 4;
}

// 1.8.1 新增
message GuildGetGroupMemberReqV2 {
    required BaseReq base_req = 1;
    required uint32 group_id = 2;
    required uint32 member_uid = 3;
    //如果从IM页直接查询，只知道account，不知道uid，所以还要能通过account查询
    optional string member_account = 4;
}

// 1.8.1 新增
message GuildGetGroupMemberRespV2 {
    required BaseResp base_resp = 1;
    required uint32 group_id = 2;
    required uint32 member_uid = 3;
    optional string member_account = 4;
    required GuildGroupMemberV2 group_mem = 5;
}

// 1.8.1 新增
// 验证给定人是否在公会里
message GuildVerifyMembersReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    repeated string member_account_list = 3; //查这些人是不是在公会
    repeated uint32 member_uid_list =4; //查这些人是不是在公会
}

// 1.8.1 新增
message GuildVerifyMembersResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    repeated string account_in_guild_list = 3; //这些人在公会（用account查就返回account）
    repeated uint32 uid_in_guild_list = 4; //这些人在公会 （用uid就返回uid）
}

// 修改群名称
message GuildModifyGroupNameReq {
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
	required uint32 group_id = 3;
	required string group_name = 4;
}

message GuildModifyGroupNameResp {
	required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
	required uint32 group_id = 3;
	required string group_name = 4;
}


// 修改群成员名片
message GuildModifyGroupMemberCardReq {
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
	required uint32 group_id = 3;
	required uint32 target_uid = 4;
	required string card = 5;
}

message GuildModifyGroupMemberCardResp {
	required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
	required uint32 group_id = 3;
	required uint32 target_uid = 4;
	required string card = 5;
}

//修改公会成员称号
message GuildModifyMemberRemarkReq{
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
	required uint32 uid = 3;
	required string remark = 4;
}

message GuildModifyMemberRemarkResp{
	required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
	required uint32 uid = 3;
	required string remark = 4;
}

//修改公会成员的马甲开关
message GuildEnablePrefixReq{
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
	required uint32 uid = 3;
	required bool enabled_prefix = 4;
}

message GuildEnablePrefixResp{
	required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
	required uint32 uid = 3;
	required bool enabled_prefix = 4;
}

message GuildModifyVerifyReq{
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
	required bool verify_type = 3;	//0不需要验证，1需要验证
}

message GuildModifyVerifyResp{
	required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
	required bool verify_type = 3;
}

// 设置群加入是否需要验证
message GuildModifyGroupVerifyReq {
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
	required uint32 group_id = 3;
	required uint32 verify_type = 4;	//0不需要验证，1需要验证，2不允许任何人加入
}

message GuildModifyGroupVerifyResp {
	required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
	required uint32 group_id = 3;
	required uint32 verify_type = 4;
}

message GuildModifyPermissionReq {
	required BaseReq base_req = 1;
	required string target_account = 2;
	required uint32 permission_bit = 3;
}


message GuildModifyPermissionResp {
	required BaseResp base_resp = 1;
	required string target_account = 2;
	required uint32 permission_bit = 3;
}

message GuildGroupSetAllMuteReq {
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
	required uint32 group_id = 3;
	required bool is_all_mute = 4;
}


message GuildGroupSetAllMuteResp {
	required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
	required uint32 group_id = 3;
	required bool is_all_mute = 4;
}

// 上传公会语音简介
message GuildUploadVoiceReq {
	required BaseReq base_req = 1;
	required bytes voice = 2;
	required uint32 voice_length = 3;
}

message GuildUploadVoiceResp {
	required BaseResp base_resp = 1;
}

// 下载公会语音简介
message GuildDownloadVoiceReq {
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
}

message GuildDownloadVoiceResp {
	required BaseResp base_resp = 1;
	required bytes voice = 2;
	required uint32 voice_length = 3;
}

// 修改游戏排序
message GuildModifyGameOrderReq {
	required BaseReq base_req = 1;
	repeated uint32 game_id_list = 2;
}

message GuildModifyGameOrderResp {
	required BaseResp base_resp = 1;
	repeated uint32 game_id_list = 2;
}

// 修改游戏群排序
message GuildModifyGameGroupOrderReq {
	required BaseReq base_req = 1;
	repeated uint32 group_id_list = 2;
}

message GuildModifyGameGroupOrderResp {
	required BaseResp base_resp = 1;
	repeated GroupOrder group_id_list = 2;
}

// 删除公会游戏
message GuildDeleteGuildGameReq {
	required BaseReq base_req = 1;
	required uint32 game_id = 2;
}

message GuildDeleteGuildGameResp {
	required BaseResp base_resp = 1;
	required uint32 game_id = 2;
}


message GuildModifyGameUrlReq {
    required BaseReq base_req = 1;
    required uint32 game_id = 2;
    required string game_url = 3;
    required string game_desc = 4;
    required bool use_custom_url = 5;
}

message GuildModifyGameUrlResp {
    required BaseResp base_resp = 1;
    required uint32 game_id = 2;
    required string game_url = 3;
    required string game_desc = 4;
    required bool use_custom_url = 5;
}

// 查游戏下载链接
message GuildGetGameDownloadUrlReq {
	required BaseReq base_req = 1;
	required uint32 game_id = 2;
}

message GuildGetGameDownloadUrlResp {
	required BaseResp base_resp = 1;
	required uint32 game_id = 2;
	required string download_url = 3;
    optional string channel_name = 4;
    optional string file_md5 = 5;
    optional string head_md5 = 6;
    optional uint32 ly_game_id = 7;
}

// 查公会游戏列表
message GuildGetGameListReq {
	required BaseReq base_req = 1;
}

message GuildGetGameListResp {
	required BaseResp base_resp = 1;
	repeated Game game_list = 2;
}

// 公会设置自定义链接
message GuildSetUseCustomUrlReq {
	required BaseReq base_req = 1;
	required uint32 game_id = 2;
	required bool is_use = 3;
}

message GuildSetUseCustomUrlResp {
	required BaseResp base_resp = 1;
	required uint32 game_id = 2;
	required bool is_use = 3;
}

// 公会会长广播
message GuildOwnerBcReq {
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
	repeated string guild_group_account = 3;// 公会群列表
	required string content = 4;			// 广播内容
	required uint32 type = 5;				// 内容类型，预留字段
}

message GuildOwnerBcResp {
	required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
	required uint32 type = 3;
}

//快速加入公会. 一般在公会专用包上使用
message GuildQuickJoinReq{
	enum PACKAGE_TYPE {
		GUILD_PKG = 1;			// 公会包
		GUILD_USER_PKG = 2;		// 公会+用户
	}
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    optional string invite_code = 3;
    optional uint32 package_type = 4;		// 客户端包类型
}

message GuildQuickJoinResp{
    required BaseResp base_resp = 1;
    optional GuildDetailInfo guild_info = 2;
}

message GuildGameDownloadReportReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 game_id = 3;
    required string url = 4;
}

message GuildGameDownloadReportResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 game_id = 3;
}

message GuildMemberGameDownloadInfo {
    required uint32 uid = 1;
    repeated uint32 gameIdList = 2;
}

message GuildGetMemberGameDownloadListReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
}

message GuildGetMemberGameDownloadListResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    repeated GuildMemberGameDownloadInfo downloadInfoList = 3;
}

// 公会群不接收消息
message GuildSetGroupNotRecvMsgReq {
	enum RECV_MSG_OPT {
		DEFAULT_RECV = 0;
		NOT_RECV = 1;
	}
	required BaseReq base_req = 1;
	required uint32 group_id = 2;
    required uint32 recv_msg_opt = 3;		// 接收群消息设置（0:正常接收，1:完全不接收本群消息）
}

message GuildSetGroupNotRecvMsgResp {
	required BaseResp base_resp = 1;
	required uint32 group_id = 2;
    required uint32 recv_msg_opt = 3;		// 接收群消息设置（0:正常接收，1:完全不接收本群消息）
}

message GuildScore{
	enum SCORE_TYPE {
		HUMANS = 0;    //人气值
		ACTIVE = 1;    //活跃值
	}
	required uint32 score_type = 1;
	required string score_name = 2;		//积分名称
	required uint32 today_score = 3;    //当前积分
	required uint32 today_limit = 4;    //当天此种积分上限
	required uint32 total_score = 5;    //历史累积积分
	required uint32 yesterday_scor = 6;   //昨天的积分
}

message GuildStarLevelReq{
	required BaseReq base_req = 1;
    required uint32 guild_id  = 2;
}

message GuildStarLevelResp{
	required BaseResp base_resp = 1;
	repeated GuildScore scores = 2;
	required uint32 star_level = 3;
	required string star_icon = 4;
}

message GuildStarLevelReqV3{
	required BaseReq base_req = 1;
    required uint32 guild_id  = 2;
}

message GuildStarLevelRespV3{
	required BaseResp base_resp = 1;
	required uint32 contribution = 2;				// 当前贡献值
	required uint32 contribution_ext = 3;			// 加成值部分
	required uint32 constribution_cur_level = 4;	// 当前等级所需贡献
	required uint32 constribution_next_level = 5;	// 下一级所需贡献
	required uint32 star_level = 6;
	required string star_icon = 7;
	required uint32 check_in_num = 8;				// 今日已签到人数
	required uint32 donation_num = 9;				// 今日捐献人数
	optional uint32 gift_total_value = 10;			// 礼物总价值
	optional int64 rich_total_value64 = 11;			// 财富值总价值(gift_total_value64位版本)
}

//-------- 禁言列表 取代之前的拉公会群成员列表------ v1.8.0 --------
message GuildGroupGetMuteListReq {
    required BaseReq base_req = 1;
    required uint32 guild_id  = 2;
    required uint32 group_id  = 3;
}

message GuildGroupGetMuteListResp {
    required BaseResp base_resp   = 1;
    required uint32 guild_id      = 2;
    required uint32 group_id      = 3;
    repeated string mute_account_list = 4;     // 禁言uid列表
    repeated uint32 mute_uid_list = 5;
}

//-------- 禁言成员详细信息列表 v 1.8.1 ----------
message GuildGroupGetMuteMemberListReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 group_id = 3;
}

message GuildGroupGetMuteMemberListResp {
    required BaseResp base_resp   = 1;
    required uint32 guild_id      = 2;
    required uint32 group_id      = 3;
    repeated GuildGroupMemberV2 member_list = 4;  //禁言成员列表
}


//---------guildblog------------------
message GuildBlogActivityDetail {
	required uint32 activity_id = 1;
    required string act_url = 2;		// 活动链接
    optional string pic_url = 3;		// 活动头图链接
	required string title = 4;			// 标题
	required uint32 warmed_up_ts_begin = 5;	// 预热时间begin
	required uint32 warmed_up_ts_end = 6;	// 预热时间end
	required uint32 active_ts_begin = 7; 	// 进行时间begin
	required uint32 active_ts_end = 8; 	// 进行时间end
	required uint32 end_ts = 9;			// 结束时间
}

message GuildBlogGetActivityListReq {
    required BaseReq base_req = 1;
	required uint32 is_home_page = 2;	// 0.全部活动 1.首页活动
	optional uint32 start_index = 3;
}

message GuildBlogGetActivityListResp {
    required BaseResp base_resp   = 1;
    repeated GuildBlogActivityDetail activity_list	= 2;
	required uint32 is_home_page = 3;	// 0.全部活动 1.首页活动
}


//公会banner广告
message GuildAdvDetail {
	required string pic_url = 1;
	required string adv_url = 2;
}

message GuildGetAdvDetailReq {
    required BaseReq base_req = 1;
}

message GuildGetAdvDetailResp {
    required BaseResp base_resp = 1;
    repeated GuildAdvDetail adv_list  = 2;
}

// 公会公告
message GuildGetAnnDetailReq {
    required BaseReq base_req = 1;
	required uint32 start = 2;
	required uint32 limit = 3;
	optional uint32 platform = 4;
}

message GuildAnnDetail {
	required string title = 1;
	required string url = 2;
	required uint32 time_subscribe = 3;
}

message GuildGetAnnDetailResp {
    required BaseResp base_resp = 1;
    repeated GuildAnnDetail ann_list  = 2;
}


//------------------公会排行------------------

message GuildGetRankListReq {
enum RANK_TYPE {
		FENG_YUN_BANG = 1;
		REN_QI_BANG = 2;
		HUO_YUE_BANG = 3;
		XIN_RUI_BANG = 4;
	}
    required BaseReq base_req = 1;
	required uint32 start = 2;
	required uint32 limit = 3;
	required uint32 rank_type = 4;
}

message GuildGetRankListResp{
	required BaseResp base_resp = 1;
	repeated GuildBaseInfo guild_info_list = 2;
	required uint32 self_guild_rank = 3;
	required uint32 rank_type = 4;
	optional bool has_rank_auth = 5;
}

message GuildGetMonthRankListReq {
	enum RANK_TYPE{
		DONATION = 1;
		CONSUME = 2;
	}
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
	required uint32 start = 3;
	required uint32 limit = 4;
	required uint32 rank_type = 5; 
}

message MonthRankMember {
	required GuildMember member = 1;
	required uint32 value = 2;
}
message GuildGetMonthRankListResp {
	required BaseResp base_resp = 1;
	repeated MonthRankMember rank_list = 2;
	required uint32 rank_type = 3; 
}

//------------------公会排行------------------

//获取公会的基本信息
message GuildGetBaseInfoReq{
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
}

message GuildGetBaseInfoResp{
	required BaseResp base_resp = 1;
	required GuildBaseInfo base_info = 2;
}

//根据游戏获取推荐的公会列表
message GuildGetRecommendListByGamesReq{
    required BaseReq base_req = 1;
    repeated uint32 game_id_list = 2;
	required bool is_popup = 3;	// 是否弹窗
	required uint32 page = 4;
	required uint32 page_count = 5;
	required uint32 last_query = 6;
	optional bool is_game_guild_page = 7;	// 是否从游戏icon进入的公会列表
}

message GuildGetRecommendListByGamesResp{
    required BaseResp base_resp = 1;
    repeated GuildBaseInfo guild_list = 2;           //公会列表
	required bool is_popup = 3;	// 是否弹窗
	required uint32 page = 4;
	required bool reach_end = 5;
	required uint32 last_query = 6;
}