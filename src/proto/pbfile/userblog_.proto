syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/userblog";

//------------- 用户TT微博 用户动态Trends  ---------------------//

enum EBlogType {
    BLOG_TEXT = 1;        // 纯文本
	BLOG_IMAGE_TEXT = 2;  // 图片 + 文本
	BLOG_VEDIO_TEXT = 3;  // 视频 + 文本
}

enum EBlogSourceType {
    BLOG_SOURCE_USER = 1;   // 用户发的
	BLOG_SOURCE_SYS_TT = 2;     // 
	BLOG_SOURCE_SYS_HC = 3;     // 
}

message BlogBaseUser {
    optional string account   = 1;        // 账号
    optional uint32 uid       = 2;        // uid
    optional string nick_name = 3;        // 昵称
}

// 动态的正文
message BlogBody {
	required uint64 blog_id = 1;
	required uint64 timestamp_ms = 2;
	required uint32 blog_type = 3;          // see EBlogType
	required uint32 blog_source_type = 4 ;  // see EBlogSourceType
	
	optional BlogBaseUser create_user = 5;  
	
	optional bytes text_content     = 6; 
	repeated string opt_url_list    = 7; 
}

// 点赞信息
message BlogZhan {
	required uint64 blog_id = 1;
	required uint64 timestamp_ms = 2;
	required uint32 source_type = 3 ;  // see EBlogSourceType
	
	optional BlogBaseUser create_user = 4;  
}

// 评论信息
message BlogComment {
	required uint64 blog_id = 1;
	required uint64 comment_id = 2;
	required uint64 timestamp_ms = 3;
	required uint32 source_type = 4 ;       // see EBlogSourceType
	
	optional BlogBaseUser create_user = 5;  // 发评论的人 
	
	optional bytes text_content     = 6; 
	
	optional uint64 reply_comment_id = 7;   // 该条评论是 回复 哪条评论
	optional BlogBaseUser reply_user = 8;   // 该条评论是 回复 哪个人
}

// 完整动态
message BlogFullDetail {

	required BlogBody main_body = 1;
	
	repeated BlogZhan zhan_list = 2;
	required uint32 zhan_total_cnt = 3;    // 该动态的点赞总数
	
	repeated BlogComment comment_list = 4;
	required uint32 comment_total_cnt = 5; // 该动态的评论总数
}




// 发布动态
message PubBlogReq {
	required BaseReq base_req 		= 1;

	required uint32 blog_type       = 2;     // see EBlogType
	optional bytes text_content     = 3;     // 文本内容
	repeated string opt_url_list    = 4;     // 图片or视频地址
}

message PubBlogResp {
	required BaseResp base_resp 	= 1;
	
	required uint64 blog_id = 2;
	required uint64 timestamp_ms = 3;
	required uint32 blog_type = 4;          // see EBlogType
}

// 发表评论
message PubCommentReq {
	required BaseReq base_req 		= 1;
	
	// 服务器定位一条 动态 需要 <动态ID,动态发表的时间,动态的作者> 这个三元组
	required uint64 blog_id           = 2;     // 
	required uint64 blog_timestamp_ms = 3;     // 
	required uint64 blog_create_uid   = 4;     // 
	
	// 
	optional bytes text_content     = 5;     // 评论的文本内容
	
	optional uint64 reply_comment_id = 6;    // 本条评论是 回复哪条上级评论
	optional uint32 reply_uid = 7;           // 本条评论是 回复哪个用户
}

message PubCommentResp {
	required BaseResp base_resp = 1;
	
	required uint64 comment_id   = 2;
	required uint64 timestamp_ms = 3;
}

// 点赞
message PubZhanReq {
	required BaseReq base_req 		= 1;
	
	// 服务器定位一条 动态 需要 <动态ID,动态发表的时间,动态的作者> 这个三元组
	required uint64 blog_id           = 2;     // 
	required uint64 blog_timestamp_ms = 3;     // 
	required uint64 blog_create_uid   = 4;     // 
}

message PubZhanResp {
	required BaseResp base_resp = 1;

}

// 获取我的关注动态流
message GetMyFeedBlogListReq {
	required BaseReq base_req 		= 1;
	
	required uint64 begin_ms_ts     = 2;     // 毫秒级别时间
	
}

message GetMyFeedBlogListResp {
	required BaseResp base_resp = 1;
	
	repeated BlogFullDetail  blog_detail_list = 2; 
	required bool is_finised = 3;
}

// 获取我发布的动态流
message GetMyPubBlogListReq {
	required BaseReq base_req 		= 1;
	
	required uint64 begin_ms_ts     = 2;     // 毫秒级别时间
	
}

message GetMyPubBlogListResp {
	required BaseResp base_resp = 1;
	
	repeated BlogFullDetail  blog_detail_list = 2; 
	required bool is_finised = 3;
}


