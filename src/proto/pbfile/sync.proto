syntax="proto2";

package ga;

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/sync";

import "ga_base.proto";
import "game_.proto";
import "redpacket_.proto";

//------------------------sync message--------------------------------
// 新加的每个SYNC类型下的消息类型都预留100个常量值的位置,
// 消息类型严格定义在自己所归属的SYNC类型之下
message SyncMessage{
    enum CMD_TYPE {
        IM_MSG = 1;					// IM_MSG
        CONTACT = 2;				// CONTACT
        NEW_CONTACT = 3;			// CONTACT
        MY_INFO = 4;				// CONTACT
        GUILD_INFO = 5;				// GUILD
        GUILD_MEM_LIST = 6;			// GUILD
        GUILD_APPLY_LIST = 7;		// 用户申请列表(GUILD, discard)
        GUILD_HANDLE_APPLY = 8;		// 会长处理申请请求(GUILD, discard)
        TEMP_GROUP_CONTACT = 9;		// 临时群联系人(CONTACT)
        GAME = 10;					// 游戏基本信息(GAME, discard)
        GUILD_CHECKIN = 11;			// 工会签到同步(GUILD)
        GUILD_GROUP = 12;			// 工会群同步(GUILD)
        GUILD_CHANGE = 13;			// 用户工会有变更(GUILD)
        GUILD_QUIT = 14;			// 用户退出工会(IM_MSG, discard)
        IM_GUILD_GROUP_MEM = 15;    // 群成员信息变化(IM_MSG)
        GAME_CIRCLE_MSG = 16;		// 游戏圈消息提醒(GAME_CIRCLE)
        GROW_INFO = 18;				// 成长体系 - 个人信息(经验值, 红钻值)(GROW)
        GROW_MISSION = 19;			// 成长体系 - 任务消息(GROW)
        OFFICAIL_ACCOUNT = 20;      // 公众号 ----  v1.5
        TGROUP_INFO = 23;		    // 群组同步

        //1.8.1 公会优化版本新增
        GUILD_INFO_V2 = 24;  //公会基本信息 V2... 拆分了的
        GUILD_ADMINS_INFO = 25; //公会管理员同步
        GUILD_CHECKIN_V2 = 26;  //公会签到信息，为了兼容，新版就不发旧的GUILD_CHECKIN了
        //1.8.1 公会优化版本新增 END

        GUILD_OPTIONAL_ACTIVITY = 28;  //公会页可选活动条 (PS：与 CONFIG_OPTIONAL_GUILD_ACTIVITY 发的协议是一样的都是 GuildOptionalActivitySync)
        GUILD_OPTIONAL_ENTRY = 29;  //公会页可选入口 (PS：与 CONFIG_OPTIONAL_GUILD_ENTRY 发的协议是一样的都是 GuildOptionalEntrySync)

        //公会新版
        GUILD_DONATE = 30;	//公会捐献信息
        GUILD_MEMBER_TITLE = 31;	//成员的公会称号
        MEMBER_CONTRIBUTION = 32;	//成员贡献

        GUILD_CIRCLE_MSG = 33;		//公会圈子

        /** CONFIG BEGIN **/

        CONFIG_NEW_MISSION = 101;       // 配置 - 新增任务(CONFIG)
        CONFIG_MEDAL = 102;             // 配置 - 勋章配置有更新(CONFIG)
        CONFIG_OFFICIAL_CONTACT = 103;  // 配置 - 官方帐号
        CONFIG_STRING_TABLE = 104;      // 配置 - 国际化字符表
        CONFIG_ACTIVITY = 105;          // 配置 - 活动
        CONFIG_OPERATION_GAME = 106;    // 配置 - 运营游戏operation
        CONFIG_OPTIONAL_DISCOVER_ACTIVITY = 107;// 配置 - 发现页可选活动条( 协议是 DiscoveryOptionalActivitySync)
        CONFIG_OPTIONAL_GUILD_ACTIVITY = 108;   // 配置 - 公会页可选活动条(PS：与 GUILD_OPTIONAL_ACTIVITY 发的协议是一样的都是 GuildOptionalActivitySync)
        CONFIG_OPTIONAL_GUILD_ENTRY = 109;      // 配置 - 公会页可选入口(PS：与 GUILD_OPTIONAL_ENTRY 发的协议是一样的都是 GuildOptionalEntrySync)
        CONFIG_SPLASH_SCREEN = 110;
		CONFIG_GUILD_MEMBER_RECRUIT_PARAM = 111;   // 配置 - 公会游戏成员招募 的参数(包括每次发起招募扣的红钻数 每次最大的招募人数) 协议为 GuildMemberRecruitParamSync
        CONFIG_SPECIAL_USER_LIST = 112;
		CONFIG_GAME_PAGE_TOP_LABLE = 113;	// 配置-游戏页顶栏标签页（推荐，关注，其他）
		CONFIG_GAME_PAGE_ADVERT = 114;	 // 配置-游戏页推荐广告
		CONFIG_GAME_PAGE_ENTER = 115; // 配置-游戏页其他功能入口（开黑，新游，热游等）
        CONFIG_RAID_RICHES = 116;               //一元夺宝入口 -- RaidRichesSync
		CONFIG_PLAYING_GAME_REPORT_LIST = 117; //客户端需要关心并上报的用户正在玩的游戏列表
		CONFIG_USER_PRESENT = 118; // 配置-用户可赠送的礼物 ConfigPresentSync
		CONFIG_USER_SCORE = 119;	// 配置-积分 ConfigScoreSync
        CONFIG_GAME_TAB_AD = 120;  // 配置-游戏页浮层广告
        CONFIG_FLOAT_LAYER = 121;   //配置-浮层广告：房间

		CONFIG_MSG_PAGE_ADVERT = 122;       //配置-消息页顶部活动入口 ( 协议是 ConfigMsgPageAdvertSync )
        CONFIG_OPTIONAL_COMMON_ENTRY = 123; //配置-通用可选的入口（可以支持各种定义的入口位置） ( 协议是 CommonOptionalEntrySync )

		CONFIG_COMMON_EFFECT_SOURCE = 124;	//配置-通用的特效资源 CommonEffectSourceSync

		CONFIG_CHANNEL_BACKGROUND = 125;   //配置-房间背景

        /** CONFIG END **/

        SESSION = 201;		// 实时语音

        /** GENERAL BEGIN **/

        NEW_GAME = 21;              // 新游期待 (General)
        CIRCLE = 22;				// 圈子 (General)
        MALL_ACT = 27;              // 商城活动(General)

        GAMETAB_WELFARE     = 250;  // 福利活动
        GAMETAB_BEST_GAME   = 251;  // 精品专区
        GAMETAB_NEW_GAME    = 252;  // 新游榜
        GAMETAB_HOT_GAME    = 253;  // 热游榜

        RED_PACKET_ACT      = 300;  // 红包活动同步
        CLIENT_FILE         = 301;  // 客户端文件
		CLIENT_COMMON_CONFIG_FILE  = 302;  // 客户端通用配置文件

        /** GENERAL END **/

        /** GROUP BEGIN **/

        GROUP_BULLETIN      = 400;		// 群组公告
        GROUP_ADMIN_CHANGE  = 401;      //管理员变更

        /** GROUP END **/

		/** ADVANCED_CONFIG BEGIN **/
		// 1次checkSync就可能sync 1次

        ADVANCED_CONFIG_USER_PRESENT      = 500;		// 礼物配置
		ADVANCED_CONFIG_PRESENT_FLOW 	  = 501;		// 流光配置（礼物）

        /** ADVANCED_CONFIG END **/

        /** UGC BEGIN **/
        UGC_INTERACTIVE_INFO_UPDATE = 600;    // 互动信息更新
        UGC_FOLLOWING_LIST_UPDATE = 601;      // 关注列表更新
        // UGC_FOLLOWER_INFO_UPDATE = 602;       // 粉丝信息更新

        /** UGC END **/
    }
    required uint32 cmd = 1;
    required bytes content = 2;//消息，好友，游戏圈，设置，陌生人
    //消息：新消息、消息状态变更、
	optional uint32 extra_flag = 3;
}

message GroupImSyncResult {
	required uint32 group_id = 1;
	required uint32 current_sync_key = 2;		// 此次应答的sync_key，如果小于newest_sync_key，则表示还没同步完，需要继续同步
	required uint32 newest_sync_key = 3;
}

message SyncReq{
    required BaseReq base_req = 1;
    enum SyncType{
        IM_MSG = 1;			// IM消息
        CONTACT = 2;		// 联系人
        GAME = 3;			// 游戏
        GUILD = 4;			// 工会
        GAME_CIRCLE = 5;	// 游戏圈
        GROW = 6;			// 成长体系
        CONFIG = 7;			// 配置信息(5次checkSync才可能sync 1次)
        GENERAL = 8;        // 公共数据
        SESSION = 9;		// 群组开黑 （已经下线 不再进行sync）
        GUILD_V2 = 10;	    // 工会v2版本同步
        IM_MSG_V2 = 11;		// IM消息v2版本,个人消息与群消息分离.
        GROUP = 12;         // 群组
        GUILD_CIRCLE = 13;	// 公会圈子
        ADVANCED_CONFIG = 14;	// 配置信息(1次checkSync就可能sync 1次)
        UGC = 15;           // 异步内容
    }
    enum IM_SYNC_TYPE {
		ALL = 0;
		IM_WITHOUT_PUSH = 1;		// 1V1和临时群的消息（没有主动推送的）
		IM_GROUP_WITH_PUSH = 2;		// 公会群、群组（有主动推送的）
	}
    required uint32 type = 2;			// 1:msg  2:xxx
    required uint32 msg_sync_key = 3;	// 服务器返回的本地保存的同步key
    optional uint32 guild_id = 4;		// 请求公会同步时，带上客户端本地的公会ID
    repeated GroupImSyncKey groupim_sync_key = 5;		// type：IM_MSG_V2，同步IM消息v2时，群消息的同步需要带上客户端本地的群synckey。新增type:GROUP
    optional uint32 im_sync_type = 6; 					// 细化IM同步请求类型

	optional uint32 mission_game_block = 7;	// GROW同步时，带上游戏中心任务显示状态 0.旧版没有填 1.隐藏 2.打开
}

message SyncResp{
    required BaseResp base_resp = 1;
    repeated SyncMessage content = 2;
    required uint32 newest_sync_key = 3;
    required uint32 type = 4; 					// 1:msg 2:xxx
    required uint32 current_sync_key = 5;		// 此次应答的sync_key，如果小于newest_sync_key，则表示还没同步完，需要继续同步
    optional uint32 guild_id = 6;				// 用户当前的工会id
    repeated GroupImSyncResult current_group_sync_key = 7;	// 群IM消息、群公告和管理员当前的sync_key
    optional uint32 im_sync_type = 8;
}

//----------------------------common config sync V2 ------------------------

message ConfigKV{
    required string key = 1;
    required string value = 2;
}

message CommConfigV2KVInfo{
	repeated ConfigKV config_kv_list = 1;
	optional uint32 begin_time = 2;  //
    optional uint32 end_time = 3;    //
}

message ConfigVersion
{
    required uint32 config_type = 1;  //配置类型 see ENUM ConfigInfo::CONFIG_TYPE
    required uint32 version_time = 2; //配置最新版本时间戳
}


message MainPageTagConfig {
    repeated ConfigKV config_kv_list = 1; //主页tag配置KV对
    required uint32 expiration_time = 2; //主页tag配置过期时间戳
    required uint32 begin_time = 3; //生效时间 。 配置在 begin_time -> expiration_time时间段中有效。
}

message Im1V1SessionWebEntry {
    repeated CommConfigV2KVInfo entry_list = 1;   //
}

message ConfigInfo {
    enum CONFIG_TYPE {
		INVALID_CONFIG = 0; // 无效值
        MAIN_PAGE_TAG_CONFIG = 1; //主页ui按钮样式配置 结构体->MainPageTagConfig
		NEWYEAR_2019_BEAT_ACT_CONFIG = 2; // 2019打年兽活动配置 结构体->atcivity_.proto NewYear2019BeatActConf (废弃)
		CHANNEL_MOLE_ATTACK_GAME_CONFIG = 3; // 房间内 打地鼠类型的游戏 结构体->atcivity_.proto ChannelRoomMoleAttackGameConf
		IM_1V1SESSION_WEB_ENTRY_CONFIG = 4; // IM 1V1 会话页 活动入口 结构体-> Im1V1SessionWebEntry
    }
    required uint32 config_type = 1;            //配置类型，对应 enum CONFIG_TYPE
    required uint32 config_version = 2;         //配置最新版本时间戳
	required bytes  config_data = 3;            //配置二进制数据 -> 可以反序列化成 enum CONFIG_TYPE 中定义的结构
}

// V2 配置SYNC
message CheckConfigSyncKeyReq {
	required BaseReq base_req = 1;
    required uint32  req_num  = 2; //请求计数，每次请求+1， CheckConfigSynckeyResp.version_list 有数据清零。
}

message CheckConfigSynckeyResp {
    required BaseResp base_resp = 1;
    repeated ConfigVersion version_list = 2;
}

// V2 配置SYNC
message SyncConfigReq {
    required BaseReq base_req = 1;
    repeated uint32 config_type_list = 2;
}

message SyncConfigResp {
    required BaseResp base_resp = 1;
    repeated ConfigInfo config_info_list = 2;
}

//------------------------sync key--------------------------------
message SyncKey {
    required uint32 type = 1;
    required uint32 msg_sync_key = 2;
}

// 同步所有key
message CheckSyncKeyReq {
    required BaseReq base_req = 1;
    optional uint32 guild_id = 2;			// 客户端本地的公会ID
    optional uint32 is_reconnect = 3;		// 是否是重连之后的checksync
    optional uint32 request_times = 4;      // 请求次数
    optional string curr_ver = 5;           // 客户端版本号，version name
}

message GroupImSyncKey {
	required uint32 group_id = 1;
	required uint32 sync_key = 2;
}

message CheckSyncKeyResp {
    required BaseResp base_resp = 1;
    repeated SyncKey sync_key_list = 2;
    optional uint32 guild_id = 3;			// 用户当前的公会ID，如果与客户端本地公会ID不一致，则客户端需要清数据
    required string album_token = 4;		// 上传相片token
    repeated string cfg_list = 5;			// 包名黑名单
    optional string latest_ver = 6;        	//最新版本
    optional uint32 show_game_circle = 7;	// 是否显示游戏圈 0:关，1:开

    // 是否需要弹"root"的警告 0:不用, 因为Android客户端pbnano无法区分0和optional字段没填
    // 1 << 0: None
    // 1 << 1: KK,
    // 1 << 2: ???
    optional uint32 root_alert_mask = 8;
    optional uint32 game_cfg_update_time = 9;       // 游戏配置的更新时间
    optional uint32 server_time = 10;               // 服务器时间
    repeated GroupImSyncKey group_im_synckey = 11;  // 服务端群IM消息最新的synckey

    enum MagicMask {
        MAGIC_MASK_SHOW_MALL_ENTRY = 1;             // 显示商城入口, 1 << 0
        MAGIC_MASK_ENABLE_HAPPY_CITY = 2;           // 欢城开关, 1 << 1
        MAGIC_MASK_ENABLE_READ_STATUS = 4;          // 已读状态开关, 1 << 2
        MAGIC_MASK_ENABLE_QUICK_MATCH = 8;          // 快速匹配开关, 1 << 3
    }

    optional uint32 magic_mask = 12;
    optional string android_plugin_cfg_url	= 13;	// android插件配置文件地址
    repeated GroupImSyncKey group_synckey = 14;     // 群组公告等信息
    optional uint32 latest_push_seq = 15;           // 最新的推送seq
    optional uint32 latest_ver_code = 16;           // 最新版本的 version code

    optional string crash_log_token = 17;           // 上传崩溃日志的token
    optional string report_img_token = 18;          // 上传举报图片的token

    optional string web_secure_token = 19;          // TT内web使用的token, 只能在https的页面中使用
}



//------------------------sync notify--------------------------------
message SyncNotify {
    required uint32 type = 1; //1:IM Msg 2:Contact 3.xxx
}

message KickNotify {
    required string errMsg = 1;
}

//------------------------sync --------------------------------

// --------------
//同步消息结构
message NewMessageSync {
    enum MSG_STATUS {
        UN_READ = 0;
        READED = 1;
    }
    enum IMG_FORMAT {
        JPG = 0;
        PNG = 1;
        GIF = 2;
    }

    enum MSG_BOOL_FLAG {
        FLAG_NONE = 0;
        FLAG_ON = 1;
        FLAG_OFF = 2;
    }
    required string from_name = 1;
    required string to_name =2;
    required string from_nick =3;
    required string to_nick =4;
    required uint32 type = 5; //消息类型
    required string content = 6; // 消息内容
    required uint32 svr_msg_id = 7; // 服务消息id
    required uint32 svr_msg_time = 8;
    required uint32 status = 9; // 消息状态 已读,未读
    required uint32 notify = 10; // 提醒类型
    optional bytes thumb = 11;//
    required bool has_attachment = 12;		    // 是否有附件
    optional bytes attachment_property = 13;    // 附件附加属性
    optional string sender_login_key = 14;		// 发送者登录的标识key，只有发送者本人才会有这个字段
    optional uint32 client_msg_id = 15;			// 发送者的消息id，只有发送者本人才会有这个字段
    optional uint32 img_format = 16;			// 图片格式，没有填这这个字段则是默认的jpg格式
    optional bytes ext = 17;					// 扩展用，可嵌套结构体 -- v1.5
    optional uint32 exceed_time = 18;			// 过期时间（CALL_IN召集令用到）
	// level, medal_list, 1.8.1版本，公会优化以后的群消息才有
	optional uint32 level = 19;                      // TT个人等级
	repeated uint32 medal_list = 20;                 // 勋章列表
	optional uint32 from_uid = 21;
    optional uint32 origin = 22;                     //  消息来源,0/1：App， 2：语音球
	optional uint32 sync_key = 23;					 //  主动推送的消息会附带上synckey
	optional uint32 to_id = 24;						 //  群的id 或者 user id
	optional uint32 guild_mem_level = 25;            //  公会成员等级
    optional uint32 target_msg_id = 26;              //  对方的消息id
	optional uint32 msg_source_type = 27;            //  消息发送的来源 由客户端填入 参考 im.proto MsgSourceType
    optional uint32 msg_sensitive_type = 28;         //  消息敏感类型 参考 im.proto MsgSensitiveType
    optional uint32 label = 29;                      // 消息的标签，客户端可以依此来做一些特殊的UI展示 参考 im.proto MsgLabel
	optional uint32 msg_redpoint_flag = 30;          // MSG_BOOL_FLAG 1是有 2是没有, 消息是否支持红点的标记 0或者没有填值 此时客户端使用默认逻辑处理 
	optional uint32 msg_exposure_flag = 31;          // MSG_BOOL_FLAG 1是有 2是没有, 消息是否外露的标记 0或者没有填值 此时客户端使用默认逻辑处理
}

//------------------
// 联系人
message ContactSync {
    required Contact contact = 1;
    optional bool isDeleted = 2;
}

//------------------
// 新联系人
message NewContactSync{
    enum ENUM_STATUS {
        I_SEND = 0;		// 我发起邀请
        I_RECV = 1;		// 我收到邀请
    }
    required Contact contact = 1;
    required uint32 status = 2;//0:我发起邀请，1:对方发起邀请
    repeated string verify_msg = 3; //验证信息
	optional uint32 friend_src_type = 4; // contact.proto FRIEND_SRC_TYPE
}

//-----------------
// 我的资料
message MyInfoSync{
    required Contact contact = 1;
    optional string phone = 2;
}

// 公众号 -- v1.5
message OfficialAccountSync{
    optional OfficialAccountInfo official_account_info = 1;
    required bool is_delete = 2;
    required uint32 official_account_id = 3;
}


//-------------------
// 可控（可选）活动入口信息同步

// 公会可控活动展示条
message GuildOptionalActivityInfo
{
	required bool is_allGuild = 1;      // 是否所有公会都有这个活动
	repeated uint32 guildid_list = 2;   // 如果is_allGuild == false 该活动对应的公会ID列表有哪些

    required uint32 activity_id = 3;
	required uint32 activity_begin_ts = 4;
	required uint32 activity_end_ts = 5;

	optional string activity_desc = 6; // 文字描述
    optional string activity_url_prefix = 7;  // url前缀,完整的url需要将当前的公会ID无脑的加在前缀之后
}
message GuildOptionalActivitySync
{
	repeated GuildOptionalActivityInfo optActivity_list = 1;
}

// 公会可控展示入口(只是入口与活动无关)
message GuildOptionalEntryInfo
{
	required bool is_allGuild = 1;     // 是否所有公会都有这个入口
    repeated uint32 guildid_list = 2;  // 如果is_allGuild == false 那么支持该入口的公会有哪些

	optional uint32 entry_id = 3;
	optional string entry_desc = 4;    // 文字描述
	optional string entry_ico_url = 5; // 入口图标ico
    optional string entry_url = 6;     // url,url为空或者不存在 表示该入口不显示
}

message GuildOptionalEntrySync
{
    repeated GuildOptionalEntryInfo optEntry_list = 1;
}

// 发现页可控活动展示条
message OptionalActivityInfo
{
	required uint32 activity_id = 1;
	required uint32 activity_begin_ts = 2;
	required uint32 activity_end_ts = 3;

	optional string activity_title = 4;       // 展示栏文字标题
	optional string activity_desc = 5;        // 展示栏文字描述(不一定有)
	optional string activity_sub_title = 6;   // 展示栏副标题(不一定有)
	optional string activity_ico_url = 7;     // 展示栏图标ico的url
    optional string activity_url = 8;         // url为空或者不存在 表示该展示条不显示
}

message DiscoveryOptionalActivitySync
{
	repeated OptionalActivityInfo optActivity_list = 1;
}

// 通用入口
message CommonOptionalEntryInfo
{
	enum E_COMMON_OPT_ENTRY_TYPE
	{
		ENUM_COM_OPT_ENTRY_IMTAB_1 = 1; // 消息页入口
	}

	optional uint32 entry_type = 1;         // E_COMMON_OPT_ENTRY_TYPE
	optional string entry_desc = 2;         // 文字描述
	optional string entry_ico_url = 3;      // 入口图标ico
    optional string entry_url = 4;          // url,url为空或者不存在 表示该入口不显示
	optional uint32 close_timestamp = 5;    // 入口关闭的时间戳
}
message CommonOptionalEntrySync
{
    repeated CommonOptionalEntryInfo optEntry_list = 1;
}

//-------------------
// 公会游戏成员招募贴 发布用的参数 同步
message GuildMemberRecruitParamInfo
{
	required uint32 reddiamond_cost = 1;         // 每个帖子发布时需要消耗的红钻数目
	required uint32 recruit_member_maxsize = 2;  // 每个帖子招募的成员数目的目标上限
	optional uint32 guild_contribution_cost = 3; // 每个帖子发布时需要消耗的公会贡献数 (贡献数和红钻任选一个进行支付)
}

message GuildMemberRecruitParamSync
{
	repeated GuildMemberRecruitParamInfo param_list = 1;
}

//-------------------
// 工会信息同步
// 从1.8.1 开始没有啦
message GuildDetailSync {
    enum GUILD_RED_POINT {
        // GUILD_BASE = 1;
        GUILD_PHOTO = 2;			// 有新的照片/相册
        GUILD_GAME = 3;				// 有新的主玩游戏
        // GUILD_MEM = 4;
        // GUILD_NOTICE = 5;		// 公告
        // GUILD_HELP = 6;			// 求助
        GUILD_GIFTPKG = 7;			// 礼包
    }
    required GuildDetailInfo info = 1;	// 工会信息
    repeated uint32 red_point_list = 2;		// 红点（有更新的地方）
}



// 1.8.1 新增
message GuildDetailSyncV2 {
    optional GuildGeneralInfoSync general_info = 1; //公会基本信息，和GuildBaseInfo不一样
    optional GuildExtraInfoSync extra_info = 2; //公会其他信息
    optional GuildGameInfoSync game_info = 3; //公会游戏信息
    optional GuildRedPointInfoSync red_point_info = 4;  //公会红点信息
    optional GuildNumbersInfoSync numbers_info = 5; //公会成员、礼包数量变化信息
}



//-------------------
// 工会成员信息同步
// 从1.8.1 开始没有啦
message GuildMemSync {
    optional GuildMember mem = 1;		// 用户信息
    required bool isDeleted = 2;		// 删除成员
    required uint32 mem_uid = 3;		// 成员uid
    required string mem_account = 4;	// 成员帐号信息
}

// 1.8.1 新增 公会管理员信息同步
message GuildAdminsSync {
    optional GuildMember member = 1;    // 用户信息
    required bool isDeleted = 2;        // 删除成员
    required uint32 mem_uid = 3;        // 成员uid
    required string mem_account = 4;    // 成员帐号信息
}

//-------------------
// 申请状态列表
message GuildApplyListSync {
    repeated GuildApplyStatus apply_status_list = 1;
}

//-------------------
// 会长处理入会/入群申请
message GuildHandleApplySync {
    enum STATUS {
        UN_HANDLE = 0;		// 未处理
        DONE = 1;			// 已处理
    }
    required string from_account = 1;		// 发申请的人
    required string from_nick = 2;
    required string to_account = 3;			// 工会帐号/群帐号
    required uint32 apply_id = 4;			// 验证ID
    required string verify_msg = 5;			// 验证消息
    required uint32 status = 6;
    required uint32 apply_time = 7;			// 	申请时间
}


//-------------------
//临时群联系人结构
message TempGroupContactSync{
    required string group_account = 1; 				// 群帐号
    required string group_name = 2;					// 群名称
    required string face_md5 = 3; 					// 用户头像
    required uint32 create_time = 4; 				// 创建时间
    required uint32 group_mem_count = 5;			// 群成员数
    required uint32 group_mem_count_limit = 6;		// 群成员人数上限
    repeated GroupMemSimpleInfo part_mem_list = 7;	// 最先入群的几个人（8个）
    required string create_mem_account = 8;			// 创建群的人
    required bool is_delete = 9;					// 是否已删除
    required bool is_show = 10;						// 客户端是否在玩伴中显示这个群
    required bool need_update_memlist = 11;			// 群成员是否有更新
    required string group_name_pinyin = 12;			// 群名称拼音
}

message GameSync{
    required Game game = 1;
}

//从1.8.1 没有啦
message GuildCheckinSync{
    repeated GuildCheckin checkin_top_n_list = 1;	// 工会签到列表前几个
    required uint32 checkin_num = 2;				// 签到人数
    required uint32 my_checkin_days = 3;			// 我的签到天数
    required string checkin_date = 4;				// 现在的日期，如2014-09-03
    required uint32 server_time = 5;				// 服务器时间
    required uint32 next_checkin_time = 6;			// 下次可以签到的时间
}

//1.8.1 新增，代替原本的签到同步
message GuildCheckInSyncV2 {
    repeated GuildCheckin checkin_top_n_list = 1;      // 工会签到列表前几个 //如果为空表示没有变化
    required uint32 checkin_num = 2;           // 签到人数
    optional uint32 my_checkin_days = 3;       // 我的签到天数
	optional uint32 my_supplement_days = 4; // 可补签的天数
	optional uint32 my_supplement_price = 5; // 补签费用
	optional uint32 my_supplemented_days = 6; // 补签后连续签到天数
}

message GuildDonateSync {
    repeated GuildDonate donate_top_n_list = 1;      // 工会捐献列表前几个 //如果为空表示没有变化
    required uint32 donate_num = 2;           // 捐献人数
    optional uint32 my_donate_days = 3;       // 我的捐献天数
}

message GuildMemberContributionSync {
	required uint32 guild_id = 1;
	required uint32 uid = 2;
	optional uint32 total_contribution = 3;	// 历史总贡献
	optional uint32 valid_contribution = 4;	// 当前可用贡献
	required uint32 member_lv = 5;			// 个人等级
}

message GuildGroupSync {
    optional GuildGroup group_info = 1;				// 群信息，如果被删除了则无此字段
    required bool is_delete = 2;					// 是否被删除了
    required uint32 group_id = 3;					// 群号
}


// 用户的工会改变了
message GuildChangeSync {
    enum QUIT_TYPE {
        NONE = 0;		// 从未加入公会
        QUIT = 1;		// 主动退出
        KICKED = 2;		// 被踢出公会
        DISMISS = 3;	// 公会解散
        CREATE = 4;		// 创建公会，服务端记录上下文使用。一般不会传这个值给客户端
    }
    required uint32 guild_id = 1;					// 用户当前的工会id
    required uint32 last_quit_type = 2;				// 上次退出工会的类型
}

// 退工会通知
message GuildQuitSync {
    enum QUIT_TYPE {
        QUIT = 1;		// 主动退出
        KICKED = 2;		// 被踢出公会
    }
    required uint32 guild_id = 1;
    required string guild_account = 2;
    required uint32 quit_uid = 3;
    required string quit_user_account = 4;
    required uint32 op_uid = 5;
    required string op_account = 6;
    required uint32 quit_type = 7;
}

message MemberGuildTitleSync {
    required uint32 uid = 1;
	required uint32 guild_id = 2;
	optional string guild_title = 3;           // 成员的公会称号

}

// 群成员修改群名片、任命/撤销群主/群管理，才会触发同步
message ImGuildGroupMemSync {
    required uint32 guild_id = 1;
    required uint32 group_id = 2;
    required GuildGroupMember member = 3;
}

//游戏圈消息提醒
message GameCircleMessage {
    enum MSG_TYPE {
        COMMENT = 1;           //评论
        LIKE = 2;              //赞
    }
	enum SYSTEM_MSG_TYPE {
		NONE = 0;
		SYSTEM_MSG = 1;
    }
    required uint32 circle_id = 1;
    required uint32 topic_id = 2;
    required uint32 type = 3;               //见上
    required GameCircleUser sender = 4;     //发送人
    required uint32 comment_id = 5;         //如果是评论类型，则为评论id
    required string comment_content = 6;    //如果是评论类型，则为评论内容
    required uint32 replied_comment_id = 7;          //如果是针对我的评论的评论，则为所引用的我的评论的id
    required string replied_comment_content = 8;     //如果是针对我的评论的评论，则为所引用的我的评论的内容
    required GameCircleUser replied_sender = 9;		 // 被回复的评论的发送者
    required uint32 svr_msg_id = 10;				 // 消息id
    required uint32 msg_time = 11;					 // 消息时间
    optional uint32 system_msg_type = 12;			// 系统消息类型
	optional uint32 parent_comment_id = 13;			// 如果是一条对评论的回复 这里显示最上级父评论的ID
}

message GameCircleActivityMessage {
	required uint64 expire_time = 1;	// 活动结束时间
	optional string act_text = 2;		// 提醒文案
}

message GameCircleMessageSync {
	enum SYNC_MSG_TYPE {
        COMMENT = 1;           //评论, GameCircleMessage有效
        LIKE = 2;              //赞, GameCircleMessage有效
        ACTIVITY = 3 ;         //圈子有新活动
    }
    required uint32 circle_id = 1;
    required bool is_delete = 2;
    required uint32 topic_id = 3;
    required uint32 comment_id = 4;
    required uint32 type = 5;						// SYNC_MSG_TYPE
    optional GameCircleMessage message = 6;
    optional GameCircleActivityMessage activity_message = 7;	// SYNC_TYPE = ACTIVITY 有效
}

// GENERAL: 有新游
// cmd: NEW_GAME
message NewGameUpdateSync {

    required uint32 circle_id = 1 ;   //新游期待有更新
    required uint32 new_begin = 2 ;
    required uint32 new_end = 3;

}

// GENERAL: 有新的圈子信息
// cmd: CIRCLE
message CircleInfoSync {
    optional Circle circle_info     = 1;
    required uint32 circle_id       = 2;
    required bool   is_deleted      = 3;
}

message MallActInfo {
    required uint32 act_id          = 1;    // 活动ID
    required string url             = 2;    // 活动入口跳转链接
    required string title           = 3;    // 活动标题
    optional string sub_title       = 4;    // 活动副标题, reserved
    required uint64 begin_time      = 5;    // 活动开始时间(UNIX TIMESTAMP)
}

// GENERAL: 商城活动更新
message MallActSync {
    optional MallActInfo current_act    = 1;    // 当前活动, 如果该值没有, 客户端清理商城活动入口
}

message GrowInfoSync {
    required MyGrowInfo grow_info = 1;
}

message MissionFinishMessage {
    enum NOTIFY_MASK {
        NONE = 0;					// 无通知(如任务还未解锁时通过其他方式完成了)
        RED_POINT = 1;				// 任务入口显示红点
        ALTER = 2;					// 弹框提示
        // NEW = 4;					// 任务入口显示NEW(Reserved)
        // NOTIFICATION_CENTER = 8;	// 通知中心显示(Reserved)
    }

    required Mission mission = 1;		// 任务
    required uint32 timestamp = 2;		// 完成的时间
    required uint32 notify_mask = 3;	// 通知类型, SEE NOTIFY_MASK
}

message MissionSync {
    enum MSG_TYPE {
        FINISHED = 1;			// 任务完成
        GUIDE = 2;				// 入口引导
    }

    required uint32 msg_type = 1;								// 类型, see MSG_TYPE
    optional MissionFinishMessage mission_finish_msg = 2;		// msg_type为FINISHED时, 使用此字段
    optional string mission_guide = 3;							// msg_type为GUIDE时, 使用此字段
}



//------------------------Config sync--------------------------------

message ConfigNewMissionSync {
    required MissionConf mission_conf = 1;	// 新增的任务
}

message ConfigMedalSync {
    repeated MedalConfig medal_conf_list = 1; // 勋章配置列表
}

message ConfigOfficialContactSync {
    repeated Contact official_contact_list = 1;	// 官方帐号列表
}

// StringTable中的一项
message StringTableComponent {
    required string description = 1;        // 字符串描述(key)
    required string localized_string = 2;   // 本地化字符串
}

message I18NStringTables {
    required string locale_short_string = 1;            // 地区简称(e.g. zh-CN, en-US, etc.)
    repeated StringTableComponent string_table = 2;     // 字符串表
}

message ConfigStringTableSync {
    repeated I18NStringTables i18n_string_table_list = 1;   // languages
}

// 就是发现页的banners
message ActivityInfo {
    enum Action_TYPE {
        NORMAL_WEB = 1;
        TT_JS_WEB = 2;
        TT_APP_NAVIGATION = 3;
    }

    required string identifier = 1;
    required string title = 2;
    required string cover_url = 3;
    required string action_url = 4;
    optional uint32 min_version = 5;
    optional uint32 action_type = 6;
    optional string icon_url = 7;
    optional string sub_title = 8;
}

// 活动配置Sync, 一旦有变化都全量给, 客户端直接替换掉本地Cache就可以
// 就是发现页的banners Sync
message ConfigActivitySync {
    repeated ActivityInfo activity_list = 1;
}

//运营位游戏
message OperationGameInfo{
	required uint32 game_id= 1;
	required string game_name = 2;
	required string game_area_url = 3;
    required string game_icon_url = 4;
    required string game_desc = 5;
    optional string game_package = 6;
    optional bool is_first_pub  =  7;
}

message ConfigOperationGameSync {
	repeated OperationGameInfo operation_game_list = 1;
}

message SplashScreenInfo{
  required uint32 screen_id = 1;
  required string screen_url = 2;
  required string action_url = 3;
  required uint32 start_time = 4;
  required uint32 end_time = 5;
  required uint32 continue_time = 6;
}

message ConfigSplashScreenSync
{
    required SplashScreenInfo splash_screen = 1;
}

message ClientFileSync
{
    required string name = 1;           // 文件名
    required string md5 = 2;            // 文件MD5
    required string download_url = 3;   // 下载地址
}

message CustomEntryInfo
{
    enum ENTRY_TYPE
    {
        ME_TAB = 1;            // '我'页面
    }
    optional string icon            = 1;    // icon
    required string url             = 2;    // 活动入口跳转链接
    required string title           = 3;    // 活动标题
    optional string sub_title       = 4;    // 活动副标题, reserved
    required uint64 begin_time      = 5;    // 活动开始时间(UNIX TIMESTAMP)
    required uint64 end_time        = 6;    // 活动结束时间
    optional uint32 type            = 7;    // 入口条目类型ENTRY_TYPE，扩展用
    optional bool ios_voldemort_lmt     = 8;  // ios 不能说名字的伏地魔字段

	optional bool is_ctl_by_parent_mode = 9;  // 是否受家长模式影响 true表示 家长模式启动的时候 该入口隐藏
}

message RaidRichesSync
{
    required string url             = 1;    // 活动入口跳转链接  -- 废弃 改用 entry_list
    required string title           = 2;    // 活动标题          -- 废弃 改用 entry_list
    optional string sub_title       = 3;    // 活动副标题, reserved -- 废弃 改用 entry_list
    required uint64 begin_time      = 4;    // 活动开始时间(UNIX TIMESTAMP)-- 废弃 改用 entry_list
    required uint64 end_time        = 5;    // 活动结束时间                -- 废弃 改用 entry_list

    repeated CustomEntryInfo entry_list = 6;   //新需求入口

}

message PlayingGameReportCnfInfo
{
    required string pkg        = 1;    // 游戏包名
    optional string name       = 2;    // 游戏名称
    required uint32 game_id    = 3;    // 游戏ID
}

message PlayingGameReportListSync
{
    repeated PlayingGameReportCnfInfo game_list = 1;   //
}

message ConfigPresentSync {
    repeated PresentItemConfig item_conf_list = 1; // 礼物配置列表
	required uint32 config_update_time = 2; // 配置更新时间
}

message ConfigScoreSync {
    optional string desc = 1;
}

message GameTabFloatLayerAdEntry
{
    required uint32 act_id = 1;
    required string act_url = 2;
    required string game_tab_img = 3;       //游戏标签页浮层图片
    required string guild_mgroup_img = 4;   //公会总群浮层图片(不存在为"")
    required uint64 begin_time      = 5;    // 活动开始时间(UNIX TIMESTAMP)
    required uint64 end_time        = 6;    // 活动结束时间
}

message GameTabFloatLayerAdSync
{
    repeated GameTabFloatLayerAdEntry entry_list = 1;
}

message ChannelBackground
{
    required string background_url = 1;
    required uint32 begin_time     = 2;    // 开始时间(UNIX TIMESTAMP)
    required uint32 end_time       = 3;    // 结束时间
}

message ChannelBackgroundSync
{
    repeated ChannelBackground background_list = 1;
}

message ConfigFloatLayerEntry{
    enum FLOAT_LAYER_TYPE
    {
        FLOAT_LAYER_CHANNEL = 1;            // 房间
    }

	//房间浮层的红点类型
	enum FloatLayerDotType
	{
		NONE = 0;
		ONCE = 1;			// 只出现一次
		EVERY_N_DAYS = 2;	// 每N天出现一次
	}

    required string img             = 1;
    required string url             = 2;
    required uint32 type            = 3;
    required uint64 begin_time      = 4;    // 开始时间(UNIX TIMESTAMP)
    required uint64 end_time        = 5;    // 结束时间
	optional uint32 dot_type		= 6;	// 红点类型 FloatLayerDotType
	optional uint32 dot_value		= 7;	// 例如dot_type是EVERY_N_DAYS的话，每dot_value天出现一次红点
	optional string top_img			= 8;	// 上方的小图的url
}

message ConfigFloatLayerSync
{
    repeated ConfigFloatLayerEntry entry_list = 1;
}

message ConfigMsgPageAdvertEntry{
    required uint32 id = 1;             //唯一标识
    required string img = 2;            //入口图片链接
    required string url = 3;            //跳转链接
    required string text = 4;           //入口文案
    required uint64 begin_time      = 5;    // 开始时间(UNIX TIMESTAMP)
    required uint64 end_time        = 6;    // 结束时间
    optional bool ios_audit_lmt        = 7;    //ios审核检查
}

message ConfigMsgPageAdvertSync
{
    repeated ConfigMsgPageAdvertEntry entry_list = 1;
}

//------------------------session sync--------------------------------

message SessionSync {
	repeated GroupSession group_session_list = 1;	// 群实时语音会话列表
	optional MySession my_session = 2;				// 我当前所在的实时语音房间
}

//------------------------report read--------------------------------
message ReportSyncReadReq {
    required BaseReq base_req = 1;
    required uint32 type = 2;		// see SyncReq::SyncType
    required uint32 sync_key = 3;
}

message ReportSyncReadResp {
    required BaseResp base_resp = 1;
    required uint32 type = 2;
    required uint32 sync_key = 3;
}

message NotifyExtendSingle
{
	enum MSG_TYPE {
		NEW_MSG_SYNC = 1;
	}
	required uint32 msg_type = 1;
	required bytes msg = 2;
}

// 追加在notify后面。如果notify后面有解析到这个字段，则直接进行处理，不需要再sync了
message NotifyExtendMulti
{
	repeated NotifyExtendSingle msg_list = 1;
}

//------------------------push message--------------------------------
// PushMessage definitions has been moved to push_.proto

//------------------------------ 群组  v1.8.0 -------------------------------
message TGroupInfoSync {
    required uint32 group_id              = 1;      // 群id
    required string group_name            = 2;		// 群名称
    required uint32 group_number          = 3;      // 群号码
    required string group_owner_account   = 4;		// 群主account
    required uint32 group_game_id         = 5;      // 群游戏id
    required bool is_delete				  = 6;		// 这个群是否被删除了
    required string face_md5			  = 7;		// 头像MD5
    optional uint32 group_type            = 8;       // 群类型, see TGroupType in tgroup_.proto
	optional uint32 is_all_mute			  = 9;		// 全体禁言
}

//-----------------------------------游戏标签页----------------------------------
message WelfareSync {
    required bool is_update = 1;    //福利活动红点
}

message BestGameSync {
    repeated GameDetailInfo best_game_list       = 1;    //精品专区
}

message NewGameSync {
    repeated GameDetailInfo new_game_list       = 1;    //新游推荐榜
}

message HotGameSync {
    repeated GameDetailInfo hot_game_list       = 1;    //精品热游榜
}

message RedPacketActSync {
    optional RedPacketActivityInfo act = 1;
    repeated RedPacketStageInfo stage_list = 2;         //阶段为增量返回
}

message GroupBulletinSync {
	required string title = 1;
	required string content = 2;
	required uint32 op_time = 3;
	required uint32 group_id = 4;
	required string author = 5;
	required uint32 bulletin_id = 6;
}

message GroupAdminInfo
{
    required uint32 uid         = 1;    //用户uid
    required uint32 is_admin    = 2;    //是否管理员，0:非管理员，1:管理员
    optional string account     = 3;
}

message GroupAdminChangeSync {
    enum MSG_TYPE {
        ALL_ADMIN = 1;              //全量同步
        DELTA_ADMIN = 2;            //增量同步
    }
    required uint32 group_id            = 1; //群组ID
    required uint32 type                = 2; //同步类型, 1:全量同步, 2:增量同步
    repeated GroupAdminInfo admin_list  = 3; //管理员变化信息
    optional string group_account       = 4;
}

message ConfigSpecialUserListSync {
    enum SpecialType {
        OFFICIAL = 1;   // 官方号
    }

    message SpecialUser {
        required uint32 uid = 1;
        required string account = 2;
    }

    required uint32 type = 1;           // see ConfigSpecialUserListSync::SpecialType
    repeated SpecialUser user_list = 2;
}

//-------------------用户推荐页-------------------
message GamePageAdvertInfo {
	optional string name = 1;
	optional string url = 2;
	optional string icon = 3;
}

message GamePageTopLableSync {
	repeated GamePageAdvertInfo top_lable_list = 1;
}

message GamePageEnterSync {
	repeated GamePageAdvertInfo enter_list = 1;
}

message GamePageAdvertSync {
	repeated GamePageAdvertInfo main_adv_list = 1;
	repeated GamePageAdvertInfo vice_adv_list = 2;
}

//--客服系统--
message GetKefuHistoryMsgReq
{
	required BaseReq base_req = 1;
	required string client_name=2;
	required uint32 server_db_msgId=3;//数据库消息id
	required uint32 get_msg_size=4; //拉取条数
}

message GetKefuHistoryMsgResp
{
	required BaseResp base_resp = 1;
	required uint32 server_db_msgId=2;
	repeated NewMessageSync content_list=3;
}

//------------------------Advanced Config sync--------------------------------
message AdvancedConfigPresentSync {
	required uint32 config_update_time = 1; // 配置更新时间
	repeated PresentItemConfig item_conf_list = 2; // 礼物配置列表
}

message AdvancedConfigPresentFlowSync {
	required uint32 config_update_time = 1; // 配置更新时间
	repeated PresentFlowConfig flow_list = 2;
}

message CommonEffectSourceSync
{
    repeated DownloadSourceInfo source_list = 1;
}
