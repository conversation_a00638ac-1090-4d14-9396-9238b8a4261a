syntax="proto2";

package ga;
import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/hctran";

//获取公会总群房间入口 直播 概要信息
message GuildGetLiveSummaryReq {
    required BaseReq base_req = 1;
    required uint32 guild_id  = 2;
}

message GuildGetLiveSummaryResp {
    required BaseResp base_resp         = 1;
    required uint32 guild_id            = 2;
    required uint32 live_members_amount = 3;       //公会内正在开播的会员数量
    required uint32 live_member_uid     = 4;       //公会内正在开播的其中一个会员uid
    required string live_member_account = 5;       //该开播成员的account
    required string jump_url            = 6;       //点击进入房间的跳转url 
    required string nick_name           = 7;       //该开播成员的昵称
}

//获取公会总群房间入口 直播 详细列表
message GuildLiveRoomInfo {
    required uint32 live_member_uid            = 1;       //公会内正在开播的会员uid
    required string live_member_account = 2;       //该开播成员的account
    required string live_topic          = 3;       //开播主题
    required uint32 users_amount        = 4;       //开播房间内用户数量
    required string jump_url            = 5;       //点击进入房间的跳转url 
    required string nick_name           = 6;       //该开播成员的昵称
}

message GuildGetLiveListReq {
    required BaseReq base_req = 1;
    required uint32 guild_id  = 2;
}

message GuildGetLiveListResp {
    required BaseResp base_resp               = 1;
    required uint32 guild_id                  = 2;
    repeated GuildLiveRoomInfo live_room_list = 3;
}

message GetTTLivePublishingListReq {
    required BaseReq base_req = 1;
}

message GetTTLivePublishingListResp {
    required BaseResp base_resp = 1;
    repeated uint32 uid_list = 2;   // 关注的开播中的主播列表
}

message TTLivePublishingMessage {
    repeated uint32 uid_list                  = 1;//开播的主播uid，repeated预留做聚合
}
