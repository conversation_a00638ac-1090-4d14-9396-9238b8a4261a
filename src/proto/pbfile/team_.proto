syntax="proto2";

package ga;
import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/team";

//游戏招募

message GameRecruitMemberChange
{
	enum PARTYSTATUS{
		JOIN_GR = 1;
		LEAVE_GR = 2;
	}
	required UserInfo user_info = 1;
	required uint32 party_status = 2; // see PARTYSTATUS
	required uint32 gr_id = 3;
}

message UserInfo {
	required uint32 uid = 1;
	required uint32 sex = 2;
	required string account = 3;
	optional string nickname = 4;
}

message GameRecruitChannelInfo {
	required uint32 channel_id = 1;
	repeated uint32 uid_list = 2;
}

message GameRecruitDetail {
	required uint32 owner_uid = 1;
	repeated UserInfo user_infos = 2;
	required uint32 game_id = 3;
	required string title = 4;
	required string desc = 5;
	optional uint32 gr_id = 6;
	optional string game_name = 7;
	optional uint32 inviter_uid = 8;
}

//查询招募信息
message GetGameRecruitReq {
	required BaseReq base_req = 1;
	required uint32 uid = 2;
}

message GetGameRecruitResp {
	required BaseResp base_resp = 1;
	required GameRecruitDetail grDetail = 2;
}

//创建招募
message CreateGameRecruitReq {
	enum PushToPublic{
		PUSH = 1;
		NOT_PUSH = 2;
	}

	required BaseReq base_req = 1;
	required GameRecruitDetail grDetail = 2;
	optional uint32 push_to_public = 3;
}

message CreateGameRecruitResp {
	required BaseResp base_resp = 1;
	required GameRecruitDetail gr_detail = 2;
}

//解散/退出 招募
message QuitGameRecruitReq {
	required BaseReq base_req = 1;
	required uint32 gr_id = 2;
	required uint32 uid = 3;
}

message QuitGameRecruitResp {
	required BaseResp base_resp = 1;
}

//加入队伍
message JoinGameRecruitReq {
	required BaseReq base_req = 1;
	required uint32 gr_id = 2;
	required uint32 uid = 3;
	required uint32 game_id = 4;
}

message JoinGameRecruitResp {
	required BaseResp base_resp = 1;
	required GameRecruitDetail grDetail = 2;
}

//开启，进入房间
message IntoGameRecruitChannelReq {
	required BaseReq base_req = 1;
	required uint32 gr_id = 2;
	required uint32 uid = 3;
}

message IntoGameRecruitChannelResp {
	required BaseResp base_resp = 1;
	required uint32 channel_id = 2;
	repeated uint32 uid_list = 3;
}

//招募邀请
message InviteGameRecruitReq {
	required BaseReq base_req = 1;
	required uint32 uid = 2;
	required uint32 invitee_uid = 3;
}

message InviteGameRecruitResp {
	required BaseResp base_resp = 1;
}

