syntax="proto2";

package ga;
import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/backpack";

enum PackageItemType
{
    UNKNOW_ITEM_TYPE=0;
    BACKPACK_PRESENT=1;                            // 礼物
    BACKPACK_CARD_RICH_EXP=2;                      // 财富经验卡
    BACKPACK_CARD_CHARM_EXP=3;                     // 魅力经验卡
}

// 功能卡片配置
message FuncCardCfg
{
    required uint32 card_id = 1;                   // 卡片id      
    required uint32 card_type = 2;                 // 卡片类型
    required string card_name = 3;                 // 卡片名称
    required string card_url = 4;                  // 卡片图片url
    required uint32 card_times = 5;                // 卡片倍数
    required uint32 valid_time = 6;                // 有效时间
    optional string card_desc = 7;                 // 卡片描述
    optional uint32 is_del = 8;                    // 是否已删除
}


// 用户背包项
message UserPackageItem
{
    required uint32 item_type = 1;                  // 背包物品类型
    required uint32 user_item_id = 2;               // 用户背包物品唯一标识
    required uint32 item_count = 3;                 // 背包物品数量
    required uint32 fin_time = 4;                   // 截止使用时间
    optional uint32 present_id = 5;                 // 背包礼物id
    optional FuncCardCfg card = 6;                  // 背包卡片
}

// 获取用户背包物品
message GetUserBackpackReq
{
    required BaseReq base_req = 1;
}

message GetUserBackpackResp
{
    required BaseResp base_resp = 1;
    repeated UserPackageItem user_backpack_list = 2;
    optional int64 last_gain_item_ts = 3;        // 最后获得物品的时间
}

// 使用背包卡片
message UseFuncCardReq
{
    required BaseReq base_req = 1;
    required uint32 card_type = 2;
    required uint32 card_id = 3;
    required uint32 user_item_id = 4;           // 唯一标识用户背包物品
}
message UseFuncCardResp
{
    required BaseResp base_resp = 1;
    required uint32 user_item_id = 2;
    required uint32 remain = 3;
    optional string success_info=4;
}

// 获取正在使用的背包卡片
message GetUserFuncCardUseReq
{
    required BaseReq base_req = 1;
    optional uint32 viewed_uid = 2;            // 被查看用户的uid
}

message GetUserFuncCardUseResp
{
    required BaseResp base_resp = 1;
    repeated FuncCardCfg card_list = 2;
}
