syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/app";

enum BIZ_TYPE {
    GUILD_PHOTO = 0;
    GUILD_ALBUM = 1;
    CIRCLE_TOPIC = 2;
    CIRCLE_TOPIC_COMMENT = 3;
    TGROUP = 4;
	GUILD_GAME_MEMBER_RECRUIT = 5;
    CONTACT = 6; // 联系人
	CHANNEL = 7; // 房间
	UGC = 8; // 异步内容
}

enum REPORT_TYPE {
    PRIVACY = 0; //隐私
    PERSONAL_ATTACK = 1;  //人身攻击
    PORNOGRAPHY = 2; //色情
    AD = 3; //广告
    SENSITIVE_INFO = 4; //敏感信息
}

message AppReportReq {
    required BaseReq base_req = 1;
    required uint32 biz_type = 2;
    required uint32 report_type = 3;
    required string report_content = 4;
    optional string pic_url = 5;
}

message AppReportResp {
    required BaseResp base_resp = 1;
    required string msg = 2;
    optional uint32 report_type = 3;
}

// app行为上报

enum BEHAVIOR {
    LAUNCH_APP = 1;
    LAUNCH_GAME = 2;
    USE_TALKBALL = 3;
    ENTER_CIRCLE = 4;
    SHARE_CIRCLE = 5;
    ENTER_BACKGROUND = 6;
}

enum SHARE_PALTFORM{
    SHARE_PLATFORM_QQ              = 1;
    SHARE_PLATFORM_QZONE           = 2;
    SHARE_PLATFORM_WECHAT          = 3;
    SHARE_PLATFORM_WECHAT_MOMENTS  = 4;  //微信朋友圈
}

message ShareCircleMessageBin{
    required uint32 topic_id    = 1;    //主题
    required uint32 circle_id   = 2;    //圈子
    required uint32 to_where    = 3;    //分享目标平台
    required uint32 time_stamp = 4;     //客户端本地时间戳(秒)
}

message AppBehaviorReportReq {
    required BaseReq base_req = 1;
    required uint32 behavior = 2;
    required string msg_bin = 3;
    //上面这个字段不好扩展...  加个新字段。 从SHARE_CIRCLE开始的类型可以使用新字段
    //上面这个字段把名字抢了。。 所以我们叫
    optional bytes  final_msg_bin = 4;
}

message AppBehaviorReportResp {
    required BaseResp base_resp = 1;
    required uint32 behavior = 2;
}

message ClientReportStatisReq {
	required BaseReq base_req = 1;
	required string channel_id = 2;	// 渠道号
	required string type = 3;		// 业务大类（IM、...）
	required string event_id = 4;	// 业务类型
	required int32 value = 5;		// 数值
	required string unit = 6;		// 单位：（秒，次数，...）
}

message ClientReportStatisResp {
	required BaseResp base_resp = 1;
}

message GetRichAndCharmLableInfoReq {
	required BaseReq base_req = 1;
	required uint32 info_limit = 2;
}

message GetRichAndCharmLableInfoResp {
	required BaseResp base_resp = 1;
	repeated string charm_accounts = 2;
	repeated string rich_accounts = 3;
}

// 反黑产 风险检测结果 上报
message RiskExtInfo
{
	enum E_EXT_RISK_TYPE{
		ENUM_EXT_RISK_TYPE_MASS_REG     = 1;  // 批量注册
		ENUM_EXT_RISK_TYPE_ACC_TAKEOVER   = 2;  // 撞库
		ENUM_EXT_RISK_TYPE_MASS_LOGIN   = 3;  // 批量登陆
		ENUM_EXT_RISK_TYPE_PROMOTION_ABUSE   = 4;  // 羊毛党
		ENUM_EXT_RISK_TYPE_SPAMMER   = 5;          // 广告导流内容
		ENUM_EXT_RISK_TYPE_CRAWLER   = 6;         // 盗爬
	}
	
	enum E_EXT_RISK_GRADE{
		ENUM_EXT_RISK_GRADE_VERY_HIGH   = 1; // 风险非常高
		ENUM_EXT_RISK_GRADE_HIGH  = 2;       // 风险高
		ENUM_EXT_RISK_GRADE_MEDIUM = 3;
		ENUM_EXT_RISK_GRADE_LOW  = 4;        // 风险低
		ENUM_EXT_RISK_GRADE_VERY_LOW  = 5;   // 风险非常低
	}
	
	optional uint32 ext_risk_type = 1;  // 注册账号的风险类型 E_EXT_RISK_TYPE
	optional uint32 ext_risk_grade = 2; // 注册账号的风险等级 E_EXT_RISK_GRADE
}


message ReportBlackRiskCheckReq {

	enum E_RISK_EVENT{
		ENUM_RISK_EVENT_REG     = 1;  // 注册
		ENUM_RISK_EVENT_LOGIN   = 2;  // 登陆
	}
	
	enum E_RISK_LEVEL{
		ENUM_RISK_LEVLE_PASS   = 1;    // 判断为没问题
		ENUM_RISK_LEVLE_REJECT   = 2;  // 判断为有问题
		ENUM_RISK_LEVLE_REVIEW   = 3;  // 疑似
	}
	
	required BaseReq base_req = 1;
	required uint32 risk_event = 2; // E_RISK_EVENT 风险事件
	optional uint32 risk_level = 3; // E_RISK_LEVEL 风险等级 就三个级别
	optional uint32 risk_score = 4; // 风险分数 分数越高 风险越大 按照数美 文档 取值在[0,1000]
	optional RiskExtInfo ext_info = 5; // 附加风险信息
	optional string risk_sdk_dev_id = 6; // 数美的设备指纹标识
}

message ReportBlackRiskCheckResp {
	required BaseResp base_resp = 1;
}

message ReportOpenAppReq
{
	required BaseReq base_req = 1;
    required uint32 friends_num = 2;
}

message ReportOpenAppResp
{
	required BaseResp base_resp = 1;
}

