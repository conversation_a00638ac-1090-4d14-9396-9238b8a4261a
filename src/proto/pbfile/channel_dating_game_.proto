syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel";

//-------------房间 相亲 游戏 相关的协议 ---------------------//

enum DatingGamePhaseType
{
	DATING_GAME_PHASE_INVALID  = 0;	 // 无效值
	DATING_GAME_PHASE_FIN  = 1;		 // 本轮 结束阶段
	DATING_GAME_PHASE_DISCUSSION= 2; // 本轮 讨论阶段
	DATING_GAME_PHASE_CHOICE = 3;    // 本轮 选择心动对象阶段
	DATING_GAME_PHASE_PUBLISH = 4;   // 本轮 公布心动对象阶段
	DATING_GAME_PHASE_CLOSE  = 5;	 // 游戏关闭
}

enum DatingGameLevel
{
	DATING_GAME_LEVEL_FREE = 0;			// 免费级
	DATING_GAME_LEVEL_SENIOR = 1;		// 高级
	DATING_GAME_LEVEL_MEDIATE = 2;		// 中级
	DATING_GAME_LEVEL_PRIMARY = 3;		// 初级
	DATING_GAME_LEVEL_PREFECT_CP = 4;	// 天作之合, 废弃
	DATING_GAME_LEVEL_TOPEST = 5;		// 顶级
}

// 检查是否有相亲游戏 入口权限
message CheckChannelDatingGameEntryReq
{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;

}

message CheckChannelDatingGameEntryResp
{
	required BaseResp base_resp = 1;
	required bool is_open = 2;
	optional uint32 level = 3;	// see DatingGameLevel
}

// 设置相亲游戏的阶段
message SetDatingGamePhaseReq
{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;
	required uint32 phase = 3;  // DatingGamePhaseType
}

message SetDatingGamePhaseResp
{
	required BaseResp base_resp = 1;
	required uint32 channel_id = 2;
	required uint32 from_phase = 3;    // DatingGamePhaseType
	required uint32 target_phase = 4;  // DatingGamePhaseType
}

// 阶段变化 的 push notify
message DatingGamePhasePushNotifyOpt
{
	required uint32 channel_id = 1;
	required uint32 from_phase = 2;  // DatingGamePhaseType
	required uint32 to_phase = 3;  // DatingGamePhaseType
}

// vip麦的用户信息
message DatingGameVipUser
{
	required uint32 uid = 1;
	required string account = 2;
	required string nickname = 3;
	optional UserProfile user_profile = 4; // 用户信息
}

// 帽子用户信息
message DatingGameHatUser
{
	required uint32 uid = 1;
	optional DownloadSourceInfo url_source = 2;	// 帽子资源
	optional bool is_male = 3;	// 是否男性
	required string account = 4;
	required string nickname = 5;

	optional UserProfile user_profile = 6; // 用户信息
}

message RuleEntryStyle{
    required uint32 game_level = 1; // see ga::DatingGameLevel
    required string rules_entry_url = 2; // 对应规则页入口图
    required string style_color = 3;     // 对应字体颜色
}

// 获取相亲房信息
// 用于进房的时候拉取初始信息（比如麦上用户的心动值 / 当前帽子用户 / 当前土豪位用户 / 当前阶段 ）
message GetDatingGameInitInfoReq
{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;
}

message GetDatingGameInitInfoResp
{
	required BaseResp base_resp = 1;
	required uint32 phase = 2;      // 房间的当前阶段 DatingGamePhaseType
	optional DatingGameVipUser vip_user = 3;	// vip麦的用户
	repeated DatingGameHatUser hat_user_list = 4;	// 获得帽子的麦上用户
	repeated UserLikeBeatInfo like_beat_list = 5; // 麦上用户心动值和心动状态
	repeated AlreadyOpenLikeUserInfo open_like_user_list = 6;	// 已经公布心动对象的用户
	optional uint32 apply_mic_len = 7;  // 申请排麦人数
	// TODO。。。

    repeated RuleEntryStyle style_list = 8;  // 规则入口样式配置
}

message UserLikeBeatInfo
{
	required uint32 uid = 1;
	required uint32 like_beat_val = 2;		// 心动值
	optional bool select_status = 3;		// 选择状态 0 未选（需根据阶段处理） 1 已选择
}

// 已公布用户
message AlreadyOpenLikeUserInfo
{
	required uint32 open_uid = 1;			// 已公布的用户
	required uint32 like_uid = 2;			// 心动用户
}

// 上vip麦
message DatingGameHoldVipMicReq
{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;
	required bool is_manual = 3;	// 是否用户手动点上麦
}

message DatingGameHoldVipMicResp
{
	required BaseResp base_resp = 1;
}

// 相亲房的可上vip麦位的通知(单推)
message DatingGameVipMsg
{
	required uint32 channel_id = 1;
	required uint32 uid = 2;
}

// 选择心动对象
message SelectLikeDatingUserReq
{
	required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 target_uid = 3;		// 选择对象 取消值为0
}

message SelectLikeDatingUserResp
{
	required BaseResp base_resp = 1;
	required uint32 target_uid = 2;
}

// 公布心动对象
message OpenLikeDatingUserReq
{
	required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 open_uid = 3;	 // 公布用户
}

message OpenLikeDatingUserResp
{
	required BaseResp base_resp = 1;
}

// 获取已经选择心动的用户
message GetAlreadySelectLikeDatingUserReq
{
	required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message GetAlreadySelectLikeDatingUserResp
{
	required BaseResp base_resp = 1;
	repeated uint32 select_uid_list = 2;	// 已经选择对象的用户
}

// 心动值变化push msg
message LikeBeatValChangeMsg
{
	required uint32 channel_id = 1;
	required uint32 uid = 2;
	required uint32 like_beat_val = 3;		// 心动值
}

// 心动状态push msg
message DatingSelectStatusMsg
{
	required uint32 channel_id = 1;
	repeated SelectStatusInfo status_info_list = 2;
}
message SelectStatusInfo
{
	required uint32 uid = 1;
	required bool select_status = 2;				// 0 未选择 1 选择
}

// 公布心动对象push msg
message OpenDatingLikeUserMsg
{
	required uint32 channel_id = 1;
	required uint32 open_uid = 2;					// 公布用户
	required DatingUserInfo like_user = 3;			// 心动用户
}

message DatingUserInfo
{
	required uint32 uid = 1;
	required string account = 2;
	required string nickname = 3;
	required uint32 sex = 4;
	optional UserProfile user_profile = 5; // 用户信息
}

// 排麦
message ApplyDatingMicReq
{
	required BaseReq base_req = 1;
    required uint32 channel_id = 2;
	required bool is_cancel = 3;			// 0 申请 1 取消
}

message ApplyDatingMicResp
{
	required BaseResp base_resp = 1;
	required bool is_cancel = 2;
	repeated ApplyDatingMicUserInfo apply_user_list = 3;
}

// 排麦列表
message ApplyDatingMicUserInfo
{
	required uint32 uid = 1;
	required string account = 2;
	required uint32 sex = 3;
	required string nick_name = 4;
	optional UserProfile user_profile = 5; // 用户信息
}

message GetApplyDatingMicUserListReq
{
	required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message GetApplyDatingMicUserListResp
{
	required BaseResp base_resp = 1;
	repeated ApplyDatingMicUserInfo apply_user_list = 2;
}

// 排麦push msg
message ApplyDatingMicMsg
{
	required uint32 channel_id = 1;
	required bool is_cancel = 2;
	required string account = 3;
	required uint32 apply_user_count = 4;
	optional string entrance_account = 5;
	optional UserProfile apply_user_profile = 6;  
	optional UserProfile entrance_user_profile = 7;  
}


message ShowoffUserInfo
{
	required uint32 uid = 1;
	required string account = 2;
	required string nickname = 3;
	required uint32 sex = 4;
	optional UserProfile user_profile = 5; // 用户信息
}

message DatingGameShowoff
{
	required DownloadSourceInfo ds_info = 1;
	required string showoff_image_url = 2;
	repeated ShowoffUserInfo showoff_uinfos = 3;
	required uint32 server_time = 4;
}

message GamePushLikeUserRecord
{
	required string showoff_image_url = 1;
	required string desc_first = 2;
	required string desc_last = 3;
	required string showoff_full_image_url = 4;
}
