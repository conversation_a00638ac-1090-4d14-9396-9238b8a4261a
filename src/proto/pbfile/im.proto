syntax="proto2";

package ga;
import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/im";

//IM 消息
enum IM_MSG_TYPE{
    //1文本 2图片 3语音 4视频 5可扩展的信息(名片、网页、功能跳转链接等)
    TEXT_MSG = 1;
    IMG_MSG = 2;
    VOICE_MSG = 3;
    VIDEO_MSG = 4;
    EXTENDED_MSG = 5;
    // GUILD_APPLY = 6;		// 会长收到入会申请
    SYSTEM_NOTIFY = 7;		// 系统通知
    GUILD_QUIT = 8;			// 用户退会通知
    EXPRESSION_MSG = 9; //表情包表情消息
    GUILD_BC = 10;		// 公会广播
    GUILD_JOIN = 11;    //用户加入工会
    GUILD_ADMIN = 12;   //副会长任命
    GUILD_GROUP_JOIN = 13;          //用户加入群
    GUILD_GROUP_OWNER = 14;    //群主任命
    GUILD_GROUP_ADMIN = 15;    //群管理任命
    GUILD_ASST_NOMINATE = 16;            //公会消息助手 -- 任命
    GUILD_ASST_APPLY_JOIN_GUILD = 17;    //公会消息助手 -- 入会申请
    GUILD_ASST_APPLY_JOIN_GROUP = 18;    //公会消息助手 -- 入群申请
    GUILD_ASST_GIFT_PKG_RESULT = 19;     //公会消息助手 -- 礼包申请结果
    GUILD_ASST_QUIT_GUILD = 20;          //公会消息助手 -- 退会通知
    NEW_EXTENDED = 21;                   //新的扩展消息
    OFFICAIL_MESSAGE_SINGLE	= 22; //公众号 单消息  -- v1.5, NewMessageSync.ext对应OfficialMessageSingle协议.
    OFFICAIL_MESSAGE_BUNCH = 23;  //公众号 消息簇  -- v1.5, NewMessageSync.ext对应OfficialMessageBunch

    // seesion 临时群实时语音版本 该功能已经下线
    CALL_IN = 24;					// 召集令 （该功能已经下线）
    SESSION_CREATE_MSG = 25;		// 创建开黑房间，NewMessageSync.ext对应SessionCreateMsg （该功能已经下线）
    SESSION_CLOSE_MSG = 26;			// 关闭开黑房间，NewMessageSync.ext对应SessionCloseMsg  （该功能已经下线）
    ACCEPT_CALL_IN = 27;			// 接受召集令 （该功能已经下线）
    CALL_IN_END	= 28;				// 召集令结束 （该功能已经下线）

    //v1.8.0 ----群组 ---- add by hjinw
    GROUP_ASST_APPLY_JOIN_GROUP = 29;   // 群消息助手 -- 入群申请
    GROUP_ASST_QUIT_GROUP = 30;         // 群消息助手 -- 退群通知
    TGROUP_JOIN_GROUP = 31;				// 群组：加群
    AIR_TICKET = 32; //频道飞机票
    GUILD_ASST_NOTIFY = 33;				// 公会消息助手 -- 通用通知
    AT_SOMEONE_MSG = 34; //群聊@Xxx的信息类型
    GUILD_MEMBER_TITLE = 35; // 公会成员称号
    GUILD_ASST_COMMON_URL_NOTIFY = 36;				// 公会消息助手 -- 带客户端跳转链接的通用通知样式
    TT_COMMON_TEXT_NOTIFY = 37;				        // TT OFFICAL 消息助手 -- 带跳转链接和高亮展示的通用通知样式
    AT_EVERYONE_MSG = 38; //群聊@all的信息类型
    CUSTOM_EMOTICON_MSG = 39; // 自定义表情
    GUILD_CIRCLE_ANNOUNCEMENT = 40; // 公会圈的公告
    CANCEL_MSG = 41; 	//撤销消息
    GAME_PREORDER_TEXT_NOTIFY = 42;		// 公会总群--带客户端跳转链接的公会成员预约通知样式
    TGROUP_INVITE_MSG       = 43;       // 游戏兴趣群邀请加群消息。
    FIND_FRIENDS_MATED_UP_MSG = 44;		// 扩圈配对成功消息
    FIND_FRIENDS_LIKED_NOTIFY = 45;		// 扩圈被喜欢时的通知消息
    GAME_CENTER_POP_GAME_NOTIFY = 46;   // 游戏中心游戏推荐
    CHANNEL_DATING_GAME_LIKE_USER_NOTIFY = 47; // 相亲房 心动消息推送(配对成功)(ext消息里面包含了channel_dating_game_.proto里 GamePushLikeUserRecord定义的内容)

	FIND_FRIENDS_ICEBREAK_MATCH = 48; // 破冰交友 配对信息(ext消息里面包含了客户端自定义的透传数据)

	SENSITIVE_NOTIFY_MSG = 49;		// 敏感提示通知

	INTERACTIVE_UNREAD_NOTIFY = 50; //异步内容新增未读互动推送

	CHANNEL_PK_RESULT_NOTIFY = 51; // 房间PK结果信息 (ext 消息里面包含了 channel_.proto里 ChannelVotePKRankInfo)

	KNOCK_INROOM_NOTIFY = 52; //  敲门进房通知

	CIVILIZATION_CONVENTION = 53; // 文明公约
}


// 1v1实时语音--房间创建
message SessionCreateMsg {
	required uint32 session_id = 1;
}

// 1V1实时语音--房间关闭
message SessionCloseMsg {
	required uint32 session_id = 1;
}

enum MsgSourceType{
	NORMAL_SOURCE = 0;
	CHANNEL_ROOM = 1; //  语音房
	NEARBY_PEOPLE = 2; // 附近的人
	MSG_SOURCE_FINDFRIEND_EXAM = 3;  // 破冰匹配 测试结果分享 findfriend_exam
	MSG_SOURCE_FINDFRIEND_MATCH = 4; // 破冰匹配 匹配 findfriend_mating
	MSG_SOURCE_USERTAG_MATCH = 5;           // 用户标签匹配 匹配 usertag_mating 卡片
	MSG_SOURCE_USERTAG_MATCH_NORMAL = 6;    // 用户标签匹配 匹配 usertag_mating 普通消息
	MSG_SOURCE_DEL_FROM_USER_BLACK_LIST = 7; // 用户移出黑名单

}
//删除消息
message DeleteMessageReq {
	required BaseReq base_req = 1;
	required uint32 svr_msg_id = 2; // 服务消息id
}

message DeleteMessageResp {
	required BaseResp base_resp = 1;
}
enum MsgSensitiveType{
	NORMAL_SENSTIVIVE  = 0;
	MONEY_SENSTIVIVE = 1; //  涉及金钱
}

enum MsgLabel {
	NO_LABEL = 0;
	NEW_FOLLOWER = 1; // 新关注者的系统消息
}

//发送消息
message SendMsgReq {
	required BaseReq base_req = 1;
	required string target_name = 2;
	required uint32 type = 3;    //消息类型列表, 文本,图片,语音
	required string content = 4;// 文本和表情字符
	required uint32 client_msg_id = 5; //
	required uint32 client_msg_time = 6;

	optional bytes thumb = 7; // 缩略图10 k 以内
	required bool has_attachment = 8;		// 是否有附件
	required string my_login_key = 9;		// 登录时的标识key
	optional uint32 origin = 10;	//消息来源,0/1：App， 2：语音球
	optional bytes ext = 11;		// 扩展
	optional uint32 msg_source_type = 12; //消息来源类型
}

message SendMsgResp {
	required BaseResp base_resp = 1;
	required uint32 client_msg_id = 2; 		//
	required uint32 svr_msg_id = 3;
	required uint32 svr_msg_time = 4;
	optional string attachment_key = 5;		// 附件key
	required string target_name = 6;
	required string my_login_key = 7;		// 登录时的标识key
	optional uint32 exceed_time = 8;	    // 召集令过期时间（剩余多久可发召集令）
	optional uint32 origin = 9;	            //消息来源,0/1：App， 2：语音球
	optional uint32 target_msg_id = 10;		//对方的svr_msg_id,消息撤销用
	optional uint32 target_uid = 11;		//对方的UID
}


//上传附件
message UploadAttachmentReq {
	required BaseReq base_req = 1;
	required uint32 svr_msg_id = 2;
	required string attachment_key = 3;		// 附件key
	required bytes attachment = 4; // 附件
	required bytes attachment_property = 5;		// 附件附加属性
}

message UploadAttachmentResp {
	required BaseResp base_resp = 1;
	required uint32 svr_msg_id = 2;
}

message DownloadAttachmentReq {
	required BaseReq base_req = 1;
	required uint32 svr_msg_id = 2;
	required string attachment_key = 3;		//
}

message DownloadAttachmentResp {
	required BaseResp base_resp = 1;
	required uint32 svr_msg_id = 2;
	required string attachment_key = 3;
	required bytes attachment = 4; // 附件
	required string target_account = 5;		// 发消息的对象
}



//批量设置已读
message MarkMsgReadReq {
	required BaseReq base_req = 1;
	required string target_name = 2; //和谁的聊天
	required uint32 svr_msg_id = 3; // 服务消息id(最大的那条id)
	optional uint32 peer_svr_msg_id = 4;	// 对端的消息id
}

message MarkMsgReadResp {
	required BaseResp base_resp = 1;
	required string target_name = 2;
	required uint32 svr_msg_id = 3;
	optional uint32 peer_read_svr_msg_id = 4;
}

// 消息接收设定

enum MsgReceiveSetting {
    MSG_RECEIVE_WITH_NOTIFICATION = 0;      // Default
    MSG_RECEIVE_WITHOUT_NOTIFICATION = 1;   // 接收但不通知
    MSG_DO_NOT_RECEIVE = 2;                 // 不接收
}

message UpdateMsgSettingReq {
    required BaseReq base_req = 1;
    required string account = 2;
    required uint32 receive_setting = 3;
}

message UpdateMsgSettingResp {
    required BaseResp base_resp = 1;
}

message QueryMsgSettingReq {
    required BaseReq base_req = 1;
    optional string account = 2;
}

message MsgReceiveSettingItem {
    required string account = 1;
    required uint32 receive_setting = 2;
}

message QueryMsgSettingResp {
    required BaseResp base_resp = 1;
    repeated MsgReceiveSettingItem setting_item_list = 2;
}

// 检查用户是否有AT所有人的权限和剩余次数
message CheckSendAtEveryoneGroupMsgReq {
    required BaseReq base_req = 1;
    required uint32 group_id = 2;
}

message CheckSendAtEveryoneGroupMsgResp {
    required BaseResp base_resp = 1;
    required uint32 remain_cnt = 2; //剩余次数
	required bool is_allow_at_everyone = 3;
}

message CancelMsgReq{
	required BaseReq base_req = 1;
	required string target_name = 2;
	required uint32 svr_msg_id = 3;		//我的服务器msg_id
	required uint32 target_msg_id = 4;	//对方的服务器msg_id
	required uint32 client_msg_id = 5;
	required uint32 client_msg_time = 6;
	required string my_login_key = 7;		// 登录时的标识key
}

message CancelMsgResp{
	required BaseResp base_resp = 1;
}

//用户设置

// 消息已读推送
message MessageReadByPeerMessage {
    required string peer_name = 1;
    required uint32 svr_msg_id = 2;
}

message GetMessagePeerReadStatusReq {
	required BaseReq base_req = 1;
	repeated string account_list = 2;
}

message MessagePeerReadStatus {
	required string account = 1;
	required uint32 svr_msg_id = 2;
}

message GetMessagePeerReadStatusResp {
	required BaseResp base_resp = 1;
	repeated MessagePeerReadStatus peer_read_status_list = 2;
}


////游戏中心好友热玩推送
message PopGameJumpMsg {

	enum JumpType{
		DOWNLOAD_GAME = 1;
		ORDER_GAME = 2;
	}
	required uint32 msg_type = 1; //see JumpType
	required uint32 game_id = 2;
	optional string area_url        = 3;    //游戏专区或游戏圈跳转地址
    optional string icon_url        = 4;    //图标地址
    optional string download_url    = 5;    //下载链接
	optional string game_size		= 6;    //大小
	optional string game_pkt_name    = 7;    //游戏包名
	optional string preorder_url    = 8;   //预约活动页
	optional string game_name		= 9;
}

//表情消息
message EmojiMsg {
	required string id = 1;		//表情id
	required string url = 2; 	//表情下载url，若有优先使用，若无使用id拼接
	required uint32 height = 3;	//表情高像素(px)
	required uint32 width = 4;	//表情宽像素(px)
}

message InteractiveUnreadMsg {
	enum InteractiveType {
		NONE = 0;
		NEW_ATTITUDE = 1;   //‘喜欢’
		NEW_COMMENT = 2;    //‘评论’
	}
	//显示最新一条信息用
	required InteractiveType type = 1;          //1‘喜欢’，2‘评论’
	required int64 time = 2;                    //通知时间
	required uint32 from_user_id = 3;           //用户id
	required string from_user_nickname = 4;     //用户名

	required int32 comment_count = 5;        //新增未读新评论数，与本地的叠加
	required int32 attitude_count = 6;       //新增未读新赞数，与本地的叠加
}