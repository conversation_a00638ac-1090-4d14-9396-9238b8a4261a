syntax="proto2";

package ga;
import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/group";

// 临时聊天加人/创建临时聊天
message GroupAddMemberReq {
    required BaseReq base_req = 1;
    repeated string accounts = 2;
	optional string group_account = 3;
}

message GroupAddMemberResp {
    required BaseResp base_resp = 1;
	required string group_account = 2;		// 群帐号
	repeated string success_accounts = 3;	// 成功加入的帐号
	repeated string fail_accounts = 4;		// 加入失败的帐号
	optional string group_name = 5;			// 群名称，只在创建群时会返回
	optional string create_msg = 6;         // 创建者会收到的一句话，用于显示在最近联系人那里
}

// 临时聊天踢人
message GroupRemoveMemberReq {
    required BaseReq base_req = 1;
    required string mem_account = 2;		
    required string group_account = 3;	// 群帐号
}

message GroupRemoveMemberResp {
    required BaseResp base_resp = 1;
    required string group_account = 2;
    required string mem_account = 3;	
}


// 修改群名称
message GroupModifyNameReq {
	required BaseReq base_req = 1;
	required string group_account = 2;
	required string group_name = 3;
}

message GroupModifyNameResp {
	required BaseResp base_resp = 1;
	required string group_account = 2;
	required string group_name = 3;
}


//获取临时群成员列表
message GroupGetMemberListReq {
    required BaseReq base_req = 1;
    required string group_account = 2;
}

message GroupGetMemberListResp {
    required BaseResp base_resp = 1;
    required string group_account = 2;
    repeated GroupMemSimpleInfo members = 3;
}

message GroupPublishBulletinReq{
	required BaseReq base_req = 1;
	required string title = 2;
	required string content = 3;
	required uint32 group_id = 4;
}

message GroupPublishBulletinResp{
	 required BaseResp base_resp = 1;
	 required uint32 bulletin_id = 2;
	 required uint32 op_time = 3;
}

message GroupDeleteBulletinReq{
	required BaseReq base_req = 1;
	required uint32 group_id = 2;
	required uint32 bulletin_id = 3;
}

message GroupBatchDelBulletinReq{
	required BaseReq base_req = 1;
	required uint32 group_id = 2;
	repeated uint32 bulletin_ids = 3;
}

message GroupDeleteBulletinResp{
	 required BaseResp base_resp = 1;
}

message GroupGetBulletinsReq{
	required BaseReq base_req = 1;
	required uint32 group_id = 2;
}

message BaseBulletinInfo {
	required string title = 1;
	required string content = 2;
	required uint32 op_time = 3;
	required uint32 bulletin_id = 4;
	required string author = 5;
}

message GroupGetBulletinsResp{
	 required BaseResp base_resp = 1;
	 repeated BaseBulletinInfo bulletin_info_list = 2;
}

