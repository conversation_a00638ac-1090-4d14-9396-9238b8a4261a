syntax = "proto2";

package ga;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/helloworld_logic";

message HelloItem {
    required string name = 1;
}

message HelloWorldLogicReq {
    required BaseReq base_req = 1;
    required string a = 2;
}

message HelloWorldLogicResp {
    required BaseResp base_resp = 1;
    required string b = 2;
    repeated HelloItem items = 3;
}
