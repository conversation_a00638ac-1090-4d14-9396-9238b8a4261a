syntax="proto2";

package ga;

option java_package ="com.yiyou.ga.plugin.model.proto";

enum NOTIFY_CMD {
    KICK_OUT = 100;
    QUIT_CHANNEL = 101;
    MY_INFO_CHANGE = 102;
	LOGOUT = 103;
	ON_TAB_SELECT = 104;
	ON_TAB_UNSELECT = 105;
	START_PUBLISH = 106;
	TRY_JOIN_TT_CHANNEL=107;
 }


enum REQ_CMD {
	NONE = 0;
	GET_MY_INFO = 100;
    PLUGIN_SHARE = 101;     // 分享到TT/外部，from 2.6.0
	GET_GUILD_INFO = 102;	// 获取我所在的工会信息，from 2.6.0
	GET_VERSION_NAME = 103;		// 获取TT版本号versionName from 2.6.0
	TO_WEB_ACTIVITY = 104;	// from 2.6.1
	SEND_PACKAGE = 105; 	// from 2.8.1 直接向服务器发送对应cmd的协议只在欢城范围
	NAVI_TT_URL = 106;	// from 2.9.2 直接调用tt的跳转url
	AVOID_RESTART_MAIN_PROCESS = 107;
	TRY_JOIN_HC_CHANNEL=108;
	GET_TT_VERSION = 109;
	CONTEXT_TT_NAVI_URL = 110;
	ADD_CONTACT = 111;
	IS_FRIEND = 112;
	GET_EMOTICON_LIST = 113;
}

message UserInfo {
    required uint32 uid = 1;
    required string account = 2;
    optional string accountAlias = 3;
    optional string nickName = 4;
    optional string signature = 5;
    optional string inviteCode = 6;
    optional string faceMd5 = 7;
	optional uint32 sex = 8;	// from 2.6.0
}

message GuildInfo {
	required uint32 guild_id = 1;	//id
	required uint32 display_id = 2; //显示的id
	required string name = 3;		//公会名称
	required string account = 4;	//账号
	optional string desc = 5;		//描述
	optional uint32 create_date = 6;//创建时间 in UnixTime
	optional uint32 guild_group_id = 7; 	//公会总群
	optional uint32 my_role = 8;	//我在公会的角色
	optional bool need_verify = 9;	//加入哦能够会是否需要验证
	optional string guild_prefix = 10;//前缀，已不使用
	optional string guild_manifesto = 11;	//公会宣言
	optional string face_md5 = 12;	//头像
	optional uint32 mem_count = 13;	//人数
	optional uint32 gift_pkg_count = 14;	//公会礼包总数
}

enum SHARE_TYPE {
    TT_SHARE = 0;
    QQ_SHARE = 1;
    QZONE_SHARE = 2;
    WECHAT_SHARE = 3;
    WECHAT_COMMENT_SHARE = 4; // 微信朋友圈
}

enum TARGET_TYPE {
    DEFAULT = 0;       // 默认选择联系人
    GUILD_GROUP = 1;   // 分享到公会总群
    SELF_DEFINED = 2;  // 自定义分享，要填target_account
}

message ShareInfo {
    required uint32 type = 1;    // see SHARE_TYPE
    required string title = 2;
    required string content = 3;
    required string image_url = 4;
    required string share_url = 5;
    optional uint32 target_type = 6;  // see TARGET_TYPE
    optional string target_account = 7; // target_type == SELF_DEFINED 要填目标account
}

message EmoticonItem{
	required uint32 res_id = 1;
	required string send_key = 2;
}

message Emotions{
	repeated EmoticonItem item_array = 1;
}

message WebParam { // from 2.6.1
	required string url = 1;
}

message SendPackage { //from 2.8.1
	required uint32 cmd = 1;
	required bytes data = 2;
	required uint32 app_id = 3;
}

message SendPackageRsp { //from 2.8.1
	required uint32 seq = 1;

}

message NaviTTUrl { //from 2.9.2
	required string url = 1;
} 

message MainProcessAvoidRestart{ //from 3.0.0
        required bool isAvoided = 1;
}	

message AddContact {
	required string account = 1;
	required string varify_msg = 2;
}

message IsFriend {
	required string account = 1;
}

message IsFriendResp {
	required bool is_friend = 1;
}
