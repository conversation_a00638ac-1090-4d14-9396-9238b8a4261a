syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/audit";


// 获取ios的版本审核状态
message GetIosCheckStatusReq
{
	required BaseReq base_req = 1;
}

message GetIosCheckStatusResp
{
	required BaseResp base_resp = 1;
	required bool is_checking = 2;	// 是否审核中
	
	
	// T豆充值页面 附加跳转链接信息
	optional string tcoin_recharge_page_desc = 3;
	optional string tcoin_recharge_page_jump_desc = 4;
	optional string tcoin_recharge_page_jump_url1 = 5; // tmall
	optional string tcoin_recharge_page_jump_url2 = 6; // taobao
	optional string tcoin_recharge_page_jump_url3 = 7; // web
	
	// IOS 评分页面 文案
	optional string score_page_desc = 8;
	optional uint32 score_page_daycnt = 9;
}
