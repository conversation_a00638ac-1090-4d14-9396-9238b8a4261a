syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/face";

message UserUploadFaceReq {
	required BaseReq base_req = 1;
	required bytes user_face = 2;
	required string account = 3;
	
	optional bool is_reg_upload = 4;  // 是否是注册上传
}

message UserUploadFaceResp {
	required BaseResp base_resp = 1;
	required string face_md5 = 2;
}

message GetSmallFaceReq {
	required BaseReq base_req = 1;
	required string account = 2;
}

message GetSmallFaceResp {
	required BaseResp base_resp = 1;
	required bytes face = 2;
	required string face_md5 = 3;
	required string account = 4;

}

message GetBigFaceReq {
	required BaseReq base_req = 1;
	required string account = 2;
}

message GetBigFaceResp {
	required BaseResp base_resp = 1;
	required bytes face = 2;
	required string face_md5 = 3;
	required string account = 4;
}

message BatchGetSmallFaceUrlReq {
	enum TYPE {
		BY_UID = 0;
        BY_GUILD_ID = 1;
	}
	required BaseReq base_req = 1;
	repeated uint32 id_list = 2;
	required uint32 type = 3;
}

message BatchGetSmallFaceUrlResp {
	required BaseResp base_resp = 1;
	repeated uint32 id_list = 2;
	required uint32 type = 3;
	repeated string md5_list = 4;
	repeated string url_list = 5;
}

