syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel";

// 用户标签

enum UserTagType {
	USERTAG_TYPE_INVALID = 0;  // 无效值
    USERTAG_TYPE_MY_AGE = 1;   // 我的年龄          
	USERTAG_TYPE_FIND_WHO = 2; // 想要找什么样的人
    USERTAG_TYPE_MY_LBS = 3;   // 我的地理位置
	USERTAG_TYPE_GAME = 4;     // 游戏                  ---- UserGameTagExt
	USERTAG_TYPE_OPT_PERSONAL = 5;  // 可选的个性标签   ---- UserOptPersonalTagExt
}


message UserTagBase{
	required string tag_name = 1;
	required uint32 tag_id   = 2;
	required uint32 tag_type = 3;     // UserTagType
	optional bytes  tag_ext_info = 4;
}


// 游戏 类型的标签
message UserGameTagKV
{
	required string key_name = 1;
	required uint32 key_id = 2;
	optional bool is_support_muti_set = 3; // 是否支持多选(废弃，使用support_muti_set_cnt就可以了)
	repeated string value_conf_list = 4;   // value的配置的备选项
	repeated string value_user_set = 5;    // value的用户实际选择项
	optional uint32 support_muti_set_cnt = 6;   // 选项用户可以选择的数量 1就是单选 2就是可以选2个
	optional uint32 partition_id = 7;           // 选项分区（1=第一部分 2=第二部分）
}

message UserGameTagExt{

	optional uint32 game_id   = 1;           // 关联的游戏ID
	optional string back_img_url   = 2;      //  背景图  url
	repeated UserGameTagKV game_kv_list = 3; // 游戏卡片的选项信息
	optional string thumb_img_url = 4;       //  简略图  url
	optional string user_game_nickname = 5;  //  用户的游戏昵称 由用户自己填
}

// 可选的个性 类型的标签
message UserOptPersonalTagClassify
{
	required string classify_name = 1;
	required uint32 classify_id   = 2;
	
	repeated UserTagBase tag_list = 3;
}

message UserOptPersonalTagExt
{
	required uint32 classify_id   = 1;
}



// 获取用户的标签列表
message GetUserTagReq{
	required BaseReq base_req = 1;
	required uint32 target_uid = 2;   // 要获取谁的标签列表，如果是获取自己的 那么填自己的UID
}

message GetUserTagResp{
	required BaseResp base_resp = 1;
	required uint32 target_uid = 2;  
	repeated UserTagBase tag_list = 3; // 获取其他人的标签信息时, UserTagBase 中的 tag_ext_info 字段可能不会填写（除游戏卡片以外）
}

// 设置用户自己的标签列表
message SetUserTagReq{
	required BaseReq base_req = 1;
	required uint32 tag_type = 2;      // UserTagType 指定设置的标签类型（每种类型下是全量覆盖） 如果为0 那么就是全部类型全量覆盖
	repeated UserTagBase tag_list = 3; // 
	
	optional bool is_need_jump_url = 4;
}

message SetUserTagResp{
	required BaseResp base_resp = 1;
	
	optional string app_jump_url = 2; // 设置完成后 客户端的跳转短链
}

// 获取标签的基本配置信息列表(不包含tag_ext_info 字段)
message GetUserTagBaseConfigListReq{
	required BaseReq base_req = 1;
}

message GetUserTagBaseConfigListResp{
	required BaseResp base_resp = 1;
	repeated UserTagBase age_tag_list = 2; 
	repeated UserTagBase findwho_tag_list = 3; 
	repeated UserTagBase game_tag_list = 4; 
	repeated UserTagBase personal_tag_list = 5; // 随机20个返回
}

// 获取标签的全量配置信息列表
message GetUserTagFullConfigListReq
{
	required BaseReq base_req = 1;
	
	optional uint32  tag_type = 2; // UserTagType 指定设置的标签类型 如果为0 那么就是全部类型
}

message GetUserTagFullConfigListResp
{
	required BaseResp base_resp = 1;
	repeated UserTagBase age_tag_list = 2; 
	repeated UserTagBase findwho_tag_list = 3; 
	repeated UserTagBase game_tag_list = 4; 
	repeated UserOptPersonalTagClassify personaltag_classify_list = 5;
	
	optional uint32 tag_type = 6; // req请求中的tag_type参数原样带回
}

// 获取指定游戏标签属性 配置信息
message GetUserGameTypeTagConfigListReq{
	required BaseReq base_req = 1;
	repeated uint32 tag_id_list = 2;
	
}

message GetUserGameTypeTagConfigListResp{
	required BaseResp base_resp = 1;
	repeated UserTagBase game_tag_list = 2;
}



/* 标签匹配 usermatchsvr BEGIN*/
message MatchUser
{
  required uint32 uid = 1;
  required string account = 2;
  required string nickname = 3;
  required int32 sex = 4;
  repeated UserTagBase tags = 5; 
}

message UserTagMatchBeginReq
{
	required BaseReq base_req = 1;
    optional uint32 friends_num = 2;
}

message UserTagMatchBeginResp
{
  required BaseResp base_resp = 1;
}

message GetUserTagMatchUsersReq
{
  required BaseReq base_req = 1;
  required uint32 start = 2;
  required uint32 count = 3;
}

message GetUserTagMatchUsersResp
{
  required BaseResp base_resp = 1;
  repeated MatchUser users_list = 2;
  required bool reach_end = 3;
  /*repeated UserTagBase op_tags = 4;                 // 发起者的 tag*/
}

message UserTagMatchPickReq
{
  required BaseReq base_req = 1;
  required uint32 uid_matched = 2;          // 被匹配的人
}
message UserTagMatchPickResp
{
  required BaseResp base_resp = 1;
  required uint32 match_score = 2;
  required  MatchUser lanuch_user = 3;
  required  MatchUser matched_user = 4;
}
/* 标签匹配 usermatchsvr END*/

