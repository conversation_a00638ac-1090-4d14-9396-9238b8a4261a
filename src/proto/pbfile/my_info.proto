syntax="proto2";

package ga;

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/myinfo";

import "ga_base.proto";

//修改手游宝账号
message ModifyUserNameReq{
    required BaseReq base_req = 1;
    required string new_user_name = 2;
}

message ModifyUserNameResp{
    required BaseResp base_resp = 1;
}

//修改昵称
message ModifyNicknameReq{
	required BaseReq base_req = 1;
    required string new_nickname = 2;
    optional bool prefix_valid = 3;
}

message ModifyNicknameResp{
	required BaseResp base_resp = 1;
}

//修改签名
message ModifySignatureReq{
	required BaseReq base_req = 1;
	required string new_signature = 2;
}

message ModifySignatureResp{
	required BaseResp base_resp = 1;
}


// 批量获取用户grow info
message BatGetUserGrowInfoReq {
required BaseReq base_req = 1;
	repeated uint32 uid_list = 2;
}

message BatGetUserGrowInfoResp {
	required BaseResp base_resp = 1;
	repeated GrowInfo grow_info_list = 2;
}

// 设置勋章尾灯
message SetMyMedalTaillightReq
{
	required BaseReq base_req = 1;
	repeated uint32 medal_taillight_list = 2;
}

message SetMyMedalTaillightResp 
{
	required BaseResp base_resp = 1;
	repeated uint32 medal_taillight_list = 2;
}


message UpdatePhotoAlbumReq {
	required BaseReq base_req = 1;
	required uint32 uid = 2;
	repeated string img_key_list = 3;
}


message UpdatePhotoAlbumResp {
	required BaseResp base_resp = 1;
}


message GetPhotoAlbumReq {
	required BaseReq base_req = 1;
	required uint32 uid = 2;
}

message GetPhotoAlbumResp {
	required BaseResp base_resp = 1;
	repeated string img_key_list = 2;
}

message SetUserHeadwearReq{
	required BaseReq base_req = 1;
	required uint32 headwear_id = 2;
}

message SetUserHeadwearResp{
	required BaseResp base_resp = 1;
	required UserHeadwearInfo headwear_info = 2;	
}

message GetUserHeadwearReq{
	required BaseReq base_req = 1;
}

message GetUserHeadwearResp{
	required BaseResp base_resp = 1;
	repeated UserHeadwearInfo headwear_list = 2;
	required uint32 headwear_id_inuse = 3; //正在使用的头像框ID, 0表示没有使用
}

message RemoveUserHeadwearReq{
	required BaseReq base_req = 1;
	required uint32 headwear_id = 2;
}

message RemoveUserHeadwearResp{
	required BaseResp base_resp = 1;	
}

//用户官方认证
message GetUserCertificationReq {
    required BaseReq base_req = 1;
    required string target_account = 2; //他人的uid
}

message GetUserCertificationResp {
    required BaseResp base_resp = 1;
    required string title = 2;  // 官方认证
    required string intro = 3;  // 认证介绍
	
	optional string style = 4;  // 
}

// 用户服务协议
message UserContractInfo {
	enum UserContractType {
		INVALID_CONTRACT_TYPE = 0;
		TT_SERVICE_CONTRACT = 1;	// TT服务协议
	}

	required uint32 contract_type = 1;	// UserContractType
	required string contract_url = 2;
	required string contract_version = 3;
	optional bool is_agree = 4;
}

message GetUserContractInfoReq {
	required BaseReq base_req = 1;
	required uint32 contract_type = 2;	// UserContractType
}

message GetUserContractInfoResp {
	required BaseResp base_resp = 1;
	optional UserContractInfo contract_info = 2;
}

message AgreeUserContractReq {
	required BaseReq base_req = 1;
	required uint32 contract_type = 2;	// UserContractType
	required string contract_version = 3;
}

message AgreeUserContractResp {
	required BaseResp base_resp = 1;
}