syntax = "proto3";

package ga.ugc;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/ugc";

// ugc中的用户信息
message UserUGCInfo {
    uint32 uid = 1; // id
    string account = 2; // 账号
    string alias = 3; // 数字账号
    string nickname = 4; // 昵称
    uint32 gender = 5; // 性别
    string signature = 6; // 个性签名
    uint32 is_official = 7; // 是否官方人员

    string face_md5 = 20; // 头像id, 用于拼接头像下载链接
    uint32 follower_count = 21; // 粉丝数
    uint32 following_count = 22; // 关注数
    uint32 post_count = 23; // 动态数
    bool following = 24; // 是否关注中
    bool follow_me = 25; // 是否关注了“我”
    repeated Attachment recent_attachments = 26; // 最近发的附件

    message RoomInfo {
        uint32 room_id = 1;
        uint32 room_type = 2; // channel_.proto: enum ChannelType
        bool room_is_pwd = 3;
        uint32 bind_id = 4; // 房间绑定的id
    }
    RoomInfo current_room = 27; // 所在的房间信息
    GrowInfo grow_info = 28; // 成长信息

    //官方认证信息
    string certify_title = 30; //官方认证
    string certify_intro = 31; //认证介绍
    string certify_style = 32; //认证样式, official官方认证，ent娱乐房认证，ugc优质内容生产者

    string recommend_letter = 38; //大V的推荐语
}

// 附件
enum AttachmentType {
    ATTACHMENT_TYPE_NONE = 0;
    IMAGE = 1;
    GIF = 2;
    VIDEO = 3;
    CMS = 4;
}

message Attachment {
    AttachmentType attachment_type = 1; // 附件类型
    string attachment_content = 2; // 附件内容
    string extra_info = 3; //给客户端自定义玩，虽然以后加也可以
    uint64 create_at = 4; // 创建时间

    // 只有type = VIDEO 才有的
    string param = 10;
}

message UploadAttachmentInfo {
    AttachmentType attachment_type = 1;
    string attachment_key = 2;
    string extra_info = 3; //给客户端自定义玩，虽然以后加也可以
}

enum PostType {
    POST_TYPE_NONE = 0;
    POST_TYPE_TEXT = 1;
    POST_TYPE_IMAGE = 2;
    POST_TYPE_VIDEO = 3;
    POST_TYPE_CMS = 4;
}

enum AbnormalStatus {
    ABNORMAL_NONE = 0;
    BANNED = 1; // 被屏蔽
    UNDER_REVIEW = 2; // 审核中
}

enum RichTextType {
    INVALID = 0;
    AT = 1; //@
}

message RichText {
    RichTextType type = 1;

    message AtData {
        uint32 uid = 1;         // id
        string account = 2;     // 账号
        string nickname = 3;    // 昵称
    }

    AtData at_data = 11;        //@人的时候有值
}

// 查看帖子详情
message PostInfo {
    string post_id = 1;
    UserUGCInfo post_owner = 2;
    PostType post_type = 3;
    string content = 4;
    repeated Attachment attachments = 5;
    TopicInfo belong_to_topic = 6;
    uint64 post_time = 7; // 发帖时间, unix second
    uint32 top_level_comment_count = 8;
    uint32 comment_count = 9;
    uint32 attitude_count = 10;
    uint32 view_count = 11; // 浏览量
    Attitude my_attitude = 12;
    // status
    bool is_deleted = 13; // 是否被删除
    uint32 share_count = 14; // 分享数
    string label = 15; // 运营配置的特殊标签
    bool had_favoured = 16; //是否收藏

    AbnormalStatus abnormal_state = 20; // 异常状态判断

    //add by v3.4.2
    TopicInfo belong_to_sub_topic = 21;
    ContentType contentType = 22;   //内容类型，发帖的时候不用传过来，后台识别
}

enum ContentType {
    PRESET_CONTENT = 0;
    FORMATTED = 1;          //部分格式化后的content格式，eg: &^****************************************************************************************************&^
                            //格式化文本前后使用 '&^' 包裹，包裹内容元数据({"at_data":{"uid":1234567,"account":"********","nickname":"Tom"},"type":1})经过base64编码，
}

// 查看评论列表/查看评论的评论列表
message CommentInfo {
    string post_id = 1;
    string conversation_id = 2;
    string comment_id = 3;
    UserUGCInfo from_user = 4;
    string content = 5;
    repeated Attachment attachments = 6;
    Attitude my_attitude = 7;
    UserUGCInfo to_user = 8;
    uint32 total_sub_comment_count = 9;
    repeated CommentInfo sub_comments = 10;
    uint32 attitude_count = 11;
    uint64 create_at = 12; // 发表评论时间
    bool is_deleted = 13; // 是否被删除

    AbnormalStatus abnormal_status = 20; // 异常状态判断
    ContentType contentType = 21;   //内容类型，发评论的时候不用传过来，后台识别
}

// 表态
enum Attitude {
    NO_ATTITUDE = 0; // 未表态
    LIKE = 1; // 点赞
}

// Feed
message Feed {
    // 帖子
    message Post {
        PostInfo post = 1;
        uint64 timestamp = 2;
    }
    // 推荐主题
    message RecommendTopics {
        repeated TopicInfo topics = 1;
        repeated OperationResource resources = 2;

    }

    //推荐话题
    message RecommendSubTopics{
        repeated TopicInfo sub_topics = 1;
    }

    // 推荐好友
    message RecommendUsers {
        repeated UserUGCInfo users = 1;
    }


    oneof feed_data {
        Post post = 1;
        RecommendTopics recommend_topics = 2;
        RecommendUsers recommend_users = 3;
        RecommendSubTopics recommend_topics_sub = 4;
    }

    string feed_id = 10;

}

// UserIdentifier用于对目标用户查询或者操作
message UserIdentifier {
    oneof key_type {
        uint32 uid = 1;
        string account = 2;
        string alias = 3;
    }
}

// ---------------------主题---------------------
//订阅
message SubscribeTopicReq {
    ga.BaseReq base_req = 1;
    string topic_id = 2; //主题id
}

message SubscribeTopicResp {
    ga.BaseResp base_resp = 1;
}

//取消订阅
message UnsubscribeTopicReq {
    ga.BaseReq base_req = 1;
    string topic_id = 2; //主题id
}

message UnsubscribeTopicResp {
    ga.BaseResp base_resp = 1;
}

message TopicInfo {


    string topic_id = 1;
    string name = 2;
    string desc = 3;
    string icon_url = 4;
    uint32 member_count = 5; // 成员数
    uint32 post_count = 6; // 动态数量
    uint32 update_at = 7; // 最后一个帖子发布的时间
    uint32 create_at = 8; // 主题创建时间
    bool is_subscribe = 9; // 是否订阅此主题 此字段仅在获取主题详细资料命令有效

    repeated OperationResource stick_resources = 10; //每个主题下的置顶资源位

    //add by v3.4.2
    repeated TopicInfo parent_topic_infos = 11; //如果为话题的话 此字段有内容
    repeated TopicInfo sub_topic_infos = 12; //如果为主题的话 此字段有内容 表示该主题下的话题


}

//运营资源位
message OperationResource {
    string label = 1; //资源位的tag eg 左上角
    string icon_url = 2; //资源位icon
    string content = 3; //资源位的文本内容
    string jump_url = 4; //资源位的跳转url
}

message TopicLoadMore {
    uint32 page = 1;
}

//获取主题列表
message GetTopicListReq {

    //add by v3.4.2
    enum TopicType {
        TypeTopic = 0; //主题
        TypeSubTopic = 1; //话题
    }

    ga.BaseReq base_req = 1;
    uint32 page = 3; //分页拉取 页数从1开始
    uint32 count = 4; //分页拉取 拉取数量

    //add by v3.4.2
    TopicType topic_type = 5;
    string parent_topic_id = 6; //如果指定主题 则返回该主题下的话题
}

message GetTopicListResp {
    ga.BaseResp base_resp = 1;
    repeated TopicInfo topics = 2; //简短主题列表
}

//返回指定主题详细资料
message GetTopicInfoReq {
    ga.BaseReq base_req = 1;
    string topic_id = 2; //主题id
    //add by v3.4.2
    string name = 3; //根据名字查找
}


message GetTopicInfoResp {
    ga.BaseResp base_resp = 1;
    TopicInfo topic = 2; //主题详细内容
}

//获取订阅的主题列表
message GetSubscribeTopicListReq {
    ga.BaseReq base_req = 1;
    uint32 count = 2; //分页拉取 拉取数量
    TopicLoadMore load_more = 3;
}

message GetSubscribeTopicListResp {
    ga.BaseResp base_resp = 1;
    repeated TopicInfo topics = 2;
    bool isSubscribeAll = 3; //是否订阅所有
    TopicLoadMore load_more = 4;
}

//获取未订阅的主题列表
message GetUnSubscribeTopicListReq {
    ga.BaseReq base_req = 1;
    uint32 count = 2; //分页拉取 拉取数量 返回的实际的数量有可能跟此值不一样
    TopicLoadMore load_more = 3; // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了

}

message GetUnSubscribeTopicListResp {
    ga.BaseResp base_resp = 1;
    repeated TopicInfo topics = 2;
    TopicLoadMore load_more = 3; // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了
}

//判断主题是否已经订阅
message CheckTopicsIsSubscribeReq {
    ga.BaseReq base_req = 1;
    repeated string topic_ids = 2; //主题id 可以批量查询
}

message CheckTopicsIsSubscribeResp {
    ga.BaseResp base_resp = 1;
    map<string, bool> info = 2; // true ：表示已经订阅
}

enum PostOrigin {
    POST_ORIG_NORMAL = 0;
    POST_ORIN_ICE_BREAK = 1; // 破冰
    POST_ORIN_ACT = 2; // 活动页

    POST_ORIG_ANCIENT = 1000; // 旧版本发的认为无效
}

//    -------- 帖子 --------
// 发帖
message PostPostReq {
    ga.BaseReq base_req = 1;
    string belong_to_topic_id = 2; // 主题id
    string content = 3; // 帖子内容
    PostType type = 4; // 帖子类型
    uint32 attachment_image_count = 5; // 图片附件数量
    uint32 attachment_video_count = 6; // 视频附件数量
    repeated string client_attachment_identifiers = 7; // 附件在客户端的唯一表示（如文件路径、文件etag等均可）
    //add by v3.4.2
    string belong_to_sub_topic_id = 8; // 话题id
    PostOrigin origin = 9; // 发帖来源
}

message PostPostResp {
    ga.BaseResp base_resp = 1;
    string post_id = 2; // 帖子id
    string image_token = 3; // upload image token
    string video_token = 4; // upload video token
    repeated string image_keys = 5; // 上传用的key
    repeated string video_keys = 6; // 上传用的key
}

message MarkPostAttachmentUploadedReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
    repeated UploadAttachmentInfo attachment_list = 3; // 上传完的附件key，注意顺序
    // V3.4.2 - 支持图片评论，如果是上传评论的附件，则带上评论的id
    string comment_id = 4;

}

message MarkPostAttachmentUploadedResp {
    ga.BaseResp base_resp = 1;
}

// 删贴
message DeletePostReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
}

message DeletePostResp {
    ga.BaseResp base_resp = 1;
}

message GetPostReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
    ContentType content_type = 3; //默认返回富文本转义后的纯文本格式(旧版)，新版加了@ 传FORMATTED的值，用枚举方便以后扩展
}

message GetPostResp {
    ga.BaseResp base_resp = 1;
    PostInfo post_info = 2;
}

enum FeedOrigin {
    NONE = 0;
    FOLLOW = 1;         // 关注流
    RECOMMENDATION = 2; // 推荐流
    PERSON = 3;         // 个人流
    TOPIC = 4;          // 圈子流
    SUB_TOPIC = 5;      // 话题流
}

// 帖子上报阅读
message PostView {
    string post_id = 1;          // post_id
    uint64 duration = 2;         // 阅读时长, 毫秒
    FeedOrigin feed_origin = 3;  // 哪条流
    string origin_id = 4;        // 来源id, 当"个人/圈子/话题 流"时,分别是对应的uid, topic_id, sub_topic_id
}

// 上报已读; 上报阅读时长
message ReportPostViewReq {
    ga.BaseReq base_req = 1;
    repeated string post_id_list = 2;     // deprecated since Android 3.4.5
    repeated PostView post_view_list = 3; // from Android 3.4.5
}

message ReportPostViewResp {
    ga.BaseResp base_resp = 1;
}

message PostMultimediaView {
    ga.BaseReq base_req = 1;
    string post_id = 2;

    enum MultimediaType {
        NONE = 0;
        IMAGE = 1;
        VIDEO = 2;
    }
    enum ViewLocation {
        INVALID = 0;
        ZOOM_IN = 1;     // 点击多媒体放大
        AUTO_PLAY = 2;   // 在流列表自动播放
        POST_DETAIL = 3; // 在帖子详情页面
    }
    MultimediaType type = 3; // 多媒体类型
    uint64 duration = 4;     // 阅读时长, 毫秒
    FeedOrigin feed_origin = 5;  // 在哪条流阅读的视频/图片, 和上报帖子是一个道理
    string origin_id = 6;        // 来源id, 当"个人/圈子/话题 流"时,分别是对应的uid, topic_id, sub_topic_id
    ViewLocation view_location = 7;      // 播放多媒体的位置
}

message ReportPostMultimediaViewReq {
    ga.BaseReq base_req = 1;
    repeated PostMultimediaView multimedia_view_list = 2;
}

message ReportPostMultimediaViewResp {
    ga.BaseResp base_resp = 1;
}

//    -------- 评论 --------

// 发评论/回复评论
message PostCommentReq {
    ga.BaseReq base_req = 1;
    string post_id = 2; // 帖子id
    string comment_id = 3; // 评论id：传0即评论帖子，非0则评论“某个评论“
    string conversation_id = 4;
    string content = 5; // 评论内容
    repeated Attachment attachments = 6; // 附件类型  DEPRECATED
    repeated string client_attachment_identifiers = 7; // 附件在客户端的唯一表示（如文件路径、文件etag等均可）
    
    // V3.4.2 - 支持图片评论（协议预留支持多图，目前只支持单图）
    uint32 attachment_image_count = 8; // 图片附件数量

}

message PostCommentResp {
    ga.BaseResp base_resp = 1;
    string post_id = 2;
    string comment_id = 3;
    CommentInfo my_comment = 4;

    // V3.4.2 - 上传图片用的token及key（协议预留支持多图，目前只支持单图）
    string image_token = 5; // upload image token
    repeated string image_keys = 6; // 上传用的key
}

// 删评论
message DeleteCommentReq {
    ga.BaseReq base_req = 1;
    string post_id = 2; // 帖子id
    string comment_id = 3;
}

message DeleteCommentResp {
    ga.BaseResp base_resp = 1;
}

message GetCommentListReq {
    enum OrderBy {
        // 兼容旧版
        UNSPECIFIC = 0;
        // 自动，即由服务器决定排序顺序
        AUTOMATIC = 1;
        // 按时间升序
        CREATE_TIME_ASC = 2;
        // 按时间降序
        CREATE_TIME_DESC = 3;
    }

    message LoadMore {
        string  next_comment_id = 1;        
        OrderBy order_by = 2;               // 第一页拉取时决定
        OrderBy sub_comments_order_by = 3;  // 第一页拉取时决定
    }

    ga.BaseReq base_req = 1;
    string post_id = 2;

    // 如果传了，表示拉子评论
    string comment_id = 3; 
    string load_more = 4;
    uint32 count = 5;

    // 排序规则，仅请求第一页（load_more为空）时有效，分页加载更多时，服务器会以load_more中的排序规则为准
    OrderBy order_by = 6;   
    // 在一级评论页面中，露出的二级评论的排序规则。请求二级评论列表时不填
    OrderBy sub_comments_order_by = 7;

    bool query_replying_comment = 8;   // 拉取子评论时，同时拉取当前评论的信息
    ContentType content_type = 9; //默认返回富文本转义后的纯文本格式(旧版)，新版加了@ 传FORMATTED的值，用枚举方便以后扩展
}


message GetCommentListResp {
    ga.BaseResp base_resp = 1;
    string post_id = 2;
    repeated CommentInfo comments = 3;
    string load_more = 4;

    // 返回实际的OrderBy（请求是AUTOMATIC时，服务器会填充实际的排序规则；否则跟客户端传的一致)，分页加载时无需填入下一次请求中
    uint32 order_by = 5;   
    // 返回实际的OrderBy（请求是AUTOMATIC时，服务器会填充实际的排序规则；否则跟客户端传的一致)，展开列表进入二级评论页时可以带上该参数
    uint32 sub_comments_order_by = 6;
    
    CommentInfo replying_comment = 7;   // 被评论的评论信息
}

enum InteractiveType {
    PRESET = 0;        //默认(获取'喜欢'+'评论')
    NEW_ATTITUDE = 1;   //获取'喜欢'
    NEW_COMMENT = 2;    //获取'评论'
    NEW_AT_MSG = 4;     //获取'被@'

    AT_PRESET = 7;
}

//获取互动消息
message GetInteractiveMsgReq {
    ga.BaseReq base_req = 1;
    InteractiveType type = 2; //使用位或运算传，方便后面加类型 [NEW_ATTITUDE | NEW_COMMENT | NEW_AT_MSG]
    uint32 limit = 3; //列表获取多少项
    string last_id = 4; //从last_id开始获取信息,直接传response里面的last_id
    ContentType content_type = 5; //默认返回富文本转义后的纯文本格式(旧版)，新版加了@ 传FORMATTED的值，用枚举方便以后扩展
}

message GetInteractiveMsgResp {
    ga.BaseResp base_resp = 1;
    int32 attitude_count = 2;
    int32 comment_count = 3;
    int32 at_count = 7;
    repeated InteractiveInfo info = 4;
    string history_mark_id = 5;         //当info.id == 此值，表示此行有'历史消息'标志
    string last_id = 6;                 //此值为空，表示全部已拿完
}

message InteractiveInfo {
    InteractiveType type = 1; // DEPRECATED: 直接使用interactive_detail, 1‘新粉丝’，2‘喜欢’，3‘评论’
    int64 time = 2; //通知时间t

    // 下列字段废弃，暂时保留以防现有版本出错，用from_user代替
    uint32 from_user_id = 3; //用户id           // DEPRECATED
    string from_user_nickname = 4; //账号       // DEPRECATED
    string from_user_account = 5; //nickname   // DEPRECATED

    string id = 6;

    UserUGCInfo from_user = 10;

    message NewAttitudeMsg {
        Attitude attitude = 1; // 表态类型
        PostInfo post_object = 2; // 表态所关联的帖子信息
        CommentInfo comment_object = 3; // 若该字段有值，则表示被表态的是一个评论
    }

    message NewCommentMsg {
        CommentInfo comment_info = 1; // 被评论的时候返回，评论的信息
        PostInfo post_object = 2; // 该评论相关的帖子信息
        CommentInfo comment_object = 3; // 被评论的评论的信息
    }

    message NewAtMsg {
        CommentInfo comment_object = 1; // 若该字段有值，则表示在评论中被@了，否则在帖子中被@了
        PostInfo post_object = 2;       // 被@所关联的帖子信息
    }

    oneof interactive_detail {
        NewCommentMsg comment_msg = 7;      //被评论的时候返回
        NewAttitudeMsg attitude_msg = 8;    //被赞的时候返回
        NewAtMsg at_msg = 9;                //被@的时候返回
    }

}

//进入消息列表页，标记已读互动消息
message MarkReadReq {
    enum MarkType {
        DEFAULT = 0; //默认，标记评论+赞通知
        FOLLOW = 1; //标记新粉丝
    }
    ga.BaseReq base_req = 1;
    string first_id = 2; //传获取到列表的第一个id，表示往后信息已读；如标记新粉丝红点无获取互动消息的，不用传
    MarkType type = 3; //标记哪种已读

}

message MarkReadResp {
    ga.BaseResp base_resp = 1;
}

// 上报分享成功
message ReportPostShareReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
    // 分享去哪里, 就不记录了吧
}

message ReportPostShareResp {
    ga.BaseResp base_resp = 1;
}

//删除读互动消息
message DelInteractiveMsgReq {
    ga.BaseReq base_req = 1;
    repeated string ids = 2; //可指定删除多个动态消息
}

message DelInteractiveMsgResp {
    ga.BaseResp base_resp = 1;
}

//    -------- 表态 --------
// 表态
message ExpressAttitudeReq {
    ga.BaseReq base_req = 1;
    string post_id = 2; //点赞(或者取消)的帖子id，对象是帖子传这个
    string comment_id = 3; //点赞(或者取消)的评论id，对象是评论传这个
    Attitude attitude = 4; //传Attitude::NO_ATTITUDE表示取消之前的Attitude
}
message ExpressAttitudeResp {
    ga.BaseResp base_resp = 1;
}

// 帖子的表态列表
message GetAttitudeUserListReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
}

message AttitudeUser {
    UserUGCInfo user = 1;
    uint64 create_at = 2; //点赞时间
    Attitude attitude = 3; //
}

message GetAttitudeUserListResp {
    ga.BaseResp base_resp = 1;
    repeated AttitudeUser user_list = 2;
}

//    -------- 关注/粉丝 --------
/*
FriendshipOperationReq/Resp适用于下列命令:
const unsigned int CMD_FollowUser = 2562; 				// 关注用户
const unsigned int CMD_UnfollowUser = 2563;				// 取消关注用户
const unsigned int CMD_RemoveFollower = 2564;			// 移除粉丝
*/
message FriendshipOperationReq {
    ga.BaseReq base_req = 1;
    UserIdentifier user_identifier = 2;

    // 操作来源
    enum Source {
        DEFAULT = 0;        // 兼容旧版

        // 固定功能点
        USER_DETAIL = 1;         // 用户详情页
        FEEDS = 2;               // 各种信息流
        POST_DETAIL = 3;         // 帖子详情页
        ROOM = 4;                // 房间（进房消息、卡片、麦位按钮）
        GUILD_MEMBER_INFO = 5;   // 公会中的用户信息
        USER_FOLLOWER_LIST = 6;  // 用户粉丝列表
        USER_RECOMMENDATION = 7; // 好友推荐
        IM_CONVERSATION = 8;     // IM会话页
        USER_TAG_MATCH = 9;      // 用户标签匹配
        USER_TAG_RECOMMEND = 10; // 用户标签推荐
        
        // 用于一些活动功能等临时性功能点，前后端协商使用custom_source字段
        CUSTOM = 255;       
    }

    Source source = 3;
    string custom_source = 4;
}

message FriendshipOperationResp {
    ga.BaseResp base_resp = 1;
    uint32 uid = 2;
}

message FriendshipsLoadMore {
    uint32 last_user_id = 1; // deprecated
    uint64 last_friendship_create_at = 2; // deprecated
    string last_id = 3;
}

message GetUserFriendshipsReq {
    enum ListType {
        FOLLOWING = 0; // 关注列表
        FOLLOWER = 1; // 粉丝列表
    }

    ga.BaseReq base_req = 1;
    ListType list_type = 2;
    UserIdentifier user_identifier = 3;
    bool request_total_count = 4; // 如果该值为true, 会返回对应列表的总数
    FriendshipsLoadMore load_more = 10; // 首次拉取不传, 加载更多时原封不动地填入上一次GetNewsFeedsResp中的load_more字段
    uint32 count = 11;
}

message GetUserFriendshipsResp {
    ga.BaseResp base_resp = 1;
    repeated UserUGCInfo user_list = 2; // 用户列表
    uint32 total_count = 3; // 总数
    FriendshipsLoadMore load_more = 10; // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了
}

message QuickCheckFriendshipReq {
    ga.BaseReq base_req = 1;
    UserIdentifier user_identifier = 2;
}

message QuickCheckFriendshipResp {
    ga.BaseResp base_resp = 1;
    bool following = 2; // 是否关注中
    bool follow_me = 3; // 是否关注了“我”
}

//    -------- Feeds --------

message NewFeedsLoadMore {
    string feeds_name = 1;

    message LastFeedInfo {
        uint64 time = 1;
        string id = 2;
        double score = 3;
    }

    bytes last_feed_info = 2;
    uint32 last_page = 3;
}

message GetNewsFeedsReq {
    // enum Type {
    //     UNSPECIFIC = 0;

    //     USER_TIMELINE = 1;          // 用户发布
    //     USER_FOLLOWING = 2;         // 用户关注
    //     USER_RECOMMENDATION = 3;    // 用户推荐

    //     TOPIC_TIMELINE = 11;        // 主题最新
    //     TOPIC_POP = 12;             // 主题热门
    // }

    message UserTimelineReq {
        UserIdentifier user_identifier = 1;
    }
    message UserFollowingReq {
    }
    message UserRecommendationReq {
    }
    message UserAttitudeReq {
    }
    message UserVisitReq {
    }
    message TopicTimelineReq {
        string topic_id = 1;
    }
    message TopicPopularReq {
        string topic_id = 1;
    }
    message UserFavouriteReq {
    }

    ga.BaseReq base_req = 1;
    oneof request_type {
        UserTimelineReq user_timeline_req = 2; //用户个人
        UserFollowingReq user_following_req = 3; //用户关注
        UserRecommendationReq user_recommendation_req = 4; //用户推荐
        TopicTimelineReq topic_timeline_req = 5; //主题
        TopicPopularReq topic_popular_req = 6; //主题热门
        UserAttitudeReq user_attitude_req = 7; //用户点赞记录
        UserVisitReq user_visit_req = 8; //用户浏览记录
        UserFavouriteReq user_favourite_req = 9; //用户收藏
    }

    NewFeedsLoadMore load_more = 20; // 首次拉取不传, 加载更多时原封不动地填入上一次GetNewsFeedsResp中的load_more字段
    uint32 count = 21; // 拉取数量

    ContentType content_type = 22; //默认返回富文本转义后的纯文本格式(旧版)，新版加了@ 传FORMATTED的值，用枚举方便以后扩展
}

message GetNewsFeedsResp {
    ga.BaseResp base_resp = 1;
    repeated Feed feeds = 2; // feed列表, 数量可能会超过请求指定的count
    NewFeedsLoadMore load_more = 3; // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了
    bool reset_to_first_page = 4; // 表示流可能已经失效或其他异常, 重置为第一页数据
    string group = 5; //feed 类型，后台用于区分feed类型，对应请求的request_type
}

message RemoveFeedsReq {
    enum Group {
        INVALID = 0;
        USER_VISIT = 1; //浏览历史
        USER_FAVOURITE = 2; //用户收藏
    }
    ga.BaseReq base_req = 1;
    Group group = 2;                    //feed 类型
    repeated string id_list = 3;        //指定feed id删除
    bool clear_all = 4;                 //true清空列表

    repeated string post_id_list = 5;   //指定帖子id删除，跟'id_list'二选一
}

message RemoveFeedsResp {
    ga.BaseResp base_resp = 1;
}

//上报浏览记录
message ReportVisitRecordReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
}

message ReportVisitRecordResp {
    ga.BaseResp base_resp = 1;
}

//添加收藏
message AddFavouriteReq {
    ga.BaseReq base_req = 1;
    //收藏帖子，post_id,feed_id二选一
    string post_id = 2;
    string feed_id = 3;
}

message AddFavouriteResp {
    ga.BaseResp base_resp = 1;
}

//    -------- User Info --------

message GetUserUGCInfoReq {
    ga.BaseReq base_req = 1;
    UserIdentifier user_identifier = 2;
}

message GetUserUGCInfoResp {
    ga.BaseResp base_resp = 1;
    UserUGCInfo user_info = 2;
}

//    -------- Sync Messages --------

message InteractiveInfoUpdate {
    uint32 unread_new_comment_count = 1;  // 未读的评论数
    uint32 unread_new_follower_count = 2; // 未读的粉丝数
    uint32 unread_new_attitude_count = 3; // 未读的表态数
    uint64 following_feeds_update_at = 4; // 关注流的更新时间(unix timestamp)
    uint32 unread_new_at_me_count = 5;    // 未读的@我数
}

message FollowingListUpdate {
    enum UpdateType {
        INCREMENTAL = 0; // 增量更新, 客户端需要合并本地列表
        REPLACE = 1; // 全量替换, 客户端直接替换掉本地列表
    }

    UpdateType update_type = 1; // 更新类型
    repeated uint32 following_uid_list = 2; // DEPRECATED, use following_uid_to_account_map instead
    repeated uint32 unfollowed_uid_list = 3; // DEPRECATED, use unfollowed_uid_to_account_map instead
    map<uint32, string> following_uid_to_account_map = 4; // 关注中的uid->account
    map<uint32, string> unfollowed_uid_to_account_map = 5; // 取消关注的uid->account, 仅在update_type为INCREMENTAL时可能有值
}

message FollowBatchUserReq {
    ga.BaseReq base_req = 1;
    repeated uint32 uid = 2;
    enum Origin {
        DEFAULT = 0;        // 兼容旧版
    }
    Origin origin = 3;
}

message FollowBatchUserResp {
    ga.BaseResp base_resp = 1;
    repeated uint32 uid = 2;
}

