syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/exchange";

enum CurrencyType {
    Unspecified = 0;    // 未指定
    TBean = 1;          // T豆
    Points = 2;         // 积分
}

message RedDiamondExchangeItem {
    required uint32 item_id = 1;    // 商品id
    required string name = 2;       // 名称
    required string desc = 3;       // 描述
    required uint32 amount = 4;     // 包含红钻的数量
}

message RedDiamondExchangeItemWithPrice {
    required RedDiamondExchangeItem item = 1;   // 商品信息
    required uint32 currency_type = 2;          // 货币类型, see CurrencyType
    required uint32 price = 3;                  // 价格
}

message GetRedDiamondExchangeItemListReq {
    optional BaseReq base_req = 1;
    optional uint32 currency_type = 2 [default = 1];    // 货币类型, see CurrencyType
}

message GetRedDiamondExchangeItemListResp {
    required BaseResp base_resp = 1;
    optional uint32 currency_type = 2;                      // 货币类型, see CurrencyType
    repeated RedDiamondExchangeItemWithPrice item_list = 3; // 商品价格列表
}

message ExchangeRedDiamondReq {
    optional BaseReq base_req = 1;
    required RedDiamondExchangeItemWithPrice item_info = 2; // 商品信息
    optional uint32 count = 3 [default = 1]; // 兑换数量
}

message ExchangeRedDiamondResp {
    required BaseResp base_resp = 1;
}

message GetRedDiamondExchangeHistoryReq {
    optional BaseReq base_req = 1;
    optional uint32 offset = 2 [default = 0];
    optional uint32 count = 3 [default = 20];
}

message GetRedDiamondExchangeHistoryResp {
    required BaseResp base_resp = 1;
}
