syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/recruit";

// 招募的平台类型
enum GUILD_GAMERECRUIT_PLATFORM_TYPE { 
	GAMERECRUIT_PLATFORM_ALL = 1;         //全平台
    GAMERECRUIT_PLATFORM_ANDROID = 2;     //安卓
    GAMERECRUIT_PLATFORM_IOS     = 3;     //苹果
}

// 招募的图
message GuildRecruitImage { 
	required string thumb_url = 1;
	required string image_url = 2;
	optional uint32 image_width = 3;
	optional uint32 image_height = 4;
}

//招募帖数据结构
message GuildRecruitDetail {
	
    required uint32 recruit_id       = 1; // 招募ID
    required uint32 game_id          = 2;
    required uint32 guild_id         = 3; // 公会ID
    required uint32 taget_count      = 4; // 招募的目标数
    required uint32 finished_count   = 5; // 当前招募完成的次数
    required uint32 support_count    = 6; // 顶的次数
	
    required uint32 creator_uid      = 7; // 创建者UID
   
    required string guild_name        = 8; // 公会名
    required string guild_game_server = 9; // 公会所在游戏的区服名
    required string recruit_content   = 10; // 招募的描述内容 (招募宣言)
	
    repeated GuildRecruitImage image_list = 11; // 招募的图片列表
	optional string creator_account  = 12;      // 创建者帐号
	optional uint32 platform_type    = 13;      // 游戏的平台类型 see GUILD_GAMERECRUIT_PLATFORM_TYPE	
	optional bool guild_need_verify  = 14;      // 该公会是否需要验证入会
	optional uint32 guild_short_id   = 15;      // 短位ID 一定要填 没有短位ID填长ID
}

// 发起招募的类型
enum GUILD_GAMERECRUIT_POST_TYPE { 
    RECRUIT_CREATE = 1;     //创建
    RECRUIT_MODIFY = 2;     //修改
    RECRUIT_DELETE = 3;	    //删除
}

// 发布招募 会长需要支付的货币类型
enum EPostRecruitCurrencyType{
	
	RECRUIT_CURRENCY_TYPE_RED_DIAMOND = 1;                // 红钻
    RECRUIT_CURRENCY_TYPE_GUILD_CONTRIBUTION  = 2;        // 公会贡献值
}

// 发起招募
message PostGuildGameMemberRecruitReq {
    required BaseReq base_req         = 1;	
    required uint32 game_id           = 2;
    required uint32 post_type         = 3;    // 必须是GUILD_GAMERECRUIT_POST_TYPE类型
    required string guild_game_server = 4;    // 公会所在游戏的区服名
    required string recruit_content   = 5;    // 招募的描述内容 (招募宣言)
    repeated string image_key_list    = 6;    // client将图片上传七牛后得到的后缀key列表
    optional uint32 recruit_id        = 7;    // 如果类型是修改和删除 需要填写招募ID
	optional uint32 platform_type     = 8;    // 游戏的平台类型 see GUILD_GAMERECRUIT_PLATFORM_TYPE
	optional uint32 post_currency_type = 9;   // 发布这个招募 会长需要支付的货币类型 目前支持红钻和公会贡献值 默认是红钻 see EPostRecruitCurrencyType
}

message PostGuildGameMemberRecruitResp {
    required BaseResp base_resp      = 1;
    required uint32 recruit_id       = 2; // 招募ID
	optional uint32 post_type        = 3; // 必须是GUILD_GAMERECRUIT_POST_TYPE类型
	optional uint32 red_diamond_cost = 4;        // 本次招募花费的红钻数目 只在创建招募的时候返回
	optional uint32 guild_contribution_cost = 5; // 本次招募花费的公会贡献数目 只在创建招募的时候返回
}


// 获取游戏的招募列表
message GetGuildRecruitListByGameIDReq { 
    required BaseReq base_req    = 1;
    required uint32 begin_idx    = 2; // 每个游戏下 起始偏移index 0表示从第一个开始
    required uint32 max_getsize  = 3; // 每个游戏下 最大获取量 
    required uint32 game_id      = 4; // 游戏ID
	optional uint32 platform_type = 5;      // 指定游戏的平台类型 see GUILD_GAMERECRUIT_PLATFORM_TYPE 不指定则不填或者为0
}

message GetGuildRecruitListByGameIDResp {
    required BaseResp base_resp       = 1;
    required uint32 game_id           = 2; 
    repeated GuildRecruitDetail recruit_list = 3;  // 列表
    required uint32 all_recruit_size  = 4;     // 该游戏下当前正在进行招募的总数all_recruit_size
}

// 获取有公会正在进行成员招募的游戏列表(目前只返回运营配置的推荐游戏)
message RecruitGameInfo{
    required uint32 game_id       = 1;
    required string game_name     = 2;
    required string game_ico_url  = 3;
    required uint32 recruit_cnt   = 4; // 正在该游戏招募成员的公会数目
    optional string summary       = 5; // 游戏简要描述
	optional string jump_url      = 6; // 跳转的URL 目前是跳转到对应的圈子
}
message GetRecruitGameListReq { 
    required BaseReq base_req    = 1;
}

message GetRecruitGameListResp {
    required BaseResp base_resp        = 1;
    repeated RecruitGameInfo game_list = 2;  // 列表
}

// 获取单个招募的详情(貌似没有用到)
message GetGuildGameRecruitDetailReq {
    required BaseReq base_req  = 1;
    required uint32 recruit_id = 2;
}

message GetGuildGameRecruitDetailResp {
    required BaseResp base_resp             = 1;
    required GuildRecruitDetail channel_info = 2;
}

// 顶一下
message SupportGuildGameMemberRecruitReq {
    required BaseReq base_req  = 1;
    required uint32 recruit_id = 2;
    required uint32 game_id    = 3; 
}
message SupportGuildGameMemberRecruitResp {
    required BaseResp base_resp = 1;
    required uint32 recruit_id  = 2;
    required uint32 support_cnt = 3; 
}

// 举报
message ReportGuildGameMemberRecruitReq {
    required BaseReq base_req      = 1;
    required uint32 recruit_id     = 2;
    optional string report_content = 3; // 举报的信息
}

message ReportGuildGameMemberRecruitResp {
    required BaseResp base_resp = 1;
    required uint32 recruit_id  = 2;
}