syntax = "proto3";

package ga.knocklogic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/knocklogic";

//const unsigned int CMD_Knock = 2800;                        //敲门请求
//const unsigned int CMD_HandleKnock = 2801;                  //处理敲门请求

message KnockLogicReq {
    ga.BaseReq base_req = 1;
    uint32 knock_uid = 2;       //敲门用户
    uint32 follow_uid = 3;      //被跟随者（玩伴）
    uint32 channelid = 4;       //房间Id
    int32  enter_room_type = 5; //进房类型
}

//bool oneuser_oneminute = 4;         //一分钟内对同一玩家请求数只能一次,此时Msg会有消息展示,-->此消息放在err那边展示
message KnockLogicResp {
    ga.BaseResp base_resp = 1;
    string msg = 2;          //返回消息
    bool is_pwd = 3;         //是否上锁,未上锁直接拉进房
    uint32 knock_uid = 4;    //敲门用户
    uint32 follow_uid = 5;   //被跟随者（玩伴）
    uint32 channelid = 6;    //房间Id
    uint32 countdown = 7;    //倒计时时间
}

message HandleKnockLogicReq {
    ga.BaseReq base_req = 1;
    uint32 knock_uid = 2;
    uint32 follow_uid = 3;
    uint32 handle_knock_uid = 4;    //如果是被跟随者，就是follow_uid,如果管理者，就是admin_uid
    uint32 channelid = 5;
    bool is_accept = 6;       //是否同意
}

message HandleKnockLogicRsp {
    ga.BaseResp base_resp = 1;
    uint32 knock_uid = 3;
    uint32 handle_knock_uid = 4;
    uint32 channelid = 5;
    uint32 follow_uid = 6;
    uint32 countdown = 7;    //倒计时时间
}

enum KnockSubCmd {
    Knock_Notify = 0;           //敲门过推送，告知管理员是否同意
    Handle_Knock_Notify = 1;    //同意拒绝敲门过推送，告知相关人员敲门情况
}

enum KnockType {
    FollowIsAdmin = 0;
    FollowIsNotAdmin = 1;
}

enum KnockStep {
    FirstStep = 0;
    SecondStep = 1;
}

//Type    :KNOCK_PUSH = 33;
message KnockCommonPush {
    KnockSubCmd type = 1;
    bytes content = 2;  //KnockPush或者HandleKnockPush进行proto marshal后的字符串
}

message KnockPush {
    uint32 knock_uid = 1;
    string knock_user_name = 2;    //account
    string knock_nick_name = 3;
    int32 knock_gender = 9;           //男：1，女：0
    uint32 follow_uid = 4;
    string follow_user_name = 5;
    string follow_nick_name = 6;
    repeated uint32 handle_knock_uid = 7;    //如果是发给被跟随者，就是follow_uid,如果发给管理者，就是admin_uid,管理员可能多个
    uint32 channelid = 8;
    string msg = 10;
    KnockType knockType = 11;    //用于追踪识别哪种操作方式
    KnockStep knockStep = 12;    //用于追踪识别哪步+
    uint32 countdown = 13;       //倒计时时间
}

enum KnockAcceptType {
    KnockDefault = 0;
    KnockAccept = 1;
    KnockReject = 2;
}

//公屏和用户结果都用这个消息推送
message HandleKnockPush {
    uint32 knock_uid = 1;
    string knock_nick_name = 2;
    string knock_user_name = 3;      //account
    uint32 handle_knock_uid = 4;    //knock信息的处理方，如果knock信息是发给被跟随者，就是follow_uid,如果发给管理者，就是admin_uid
    string handle_knock_nick_name = 5;
    string handle_knock_user_name = 6;    //account
    uint32 follow_uid = 7;
    string follow_nick_name = 8;
    uint32 channelid = 9;
    string msg = 10;
    KnockAcceptType knock_accept_type = 11;
    string cipher_text = 12;        //加密过的数据 knockuid+timestamp+begintime+followuid+timeout+channelid用tocken加密
    bool come_room = 13;            //进房操作
    KnockType knockType = 14;       //用于追踪识别哪种操作方式
    KnockStep knockStep = 15;       //用于追踪识别哪步
    int32 roomType = 16;            //房间类型
    int32 enter_room_type = 17;    //进房类型
}

//im推送消息
message ImHandleKnockPush {
    uint32 knock_uid = 1;
    uint32 handle_knock_uid = 2;    //knock信息的处理方，如果knock信息是发给被跟随者，就是follow_uid,如果发给管理者，就是admin_uid
    uint32 follow_uid = 3;
    uint32 channelid = 4;
    string msg = 5;
    KnockAcceptType knock_accept_type = 6;
    string cipher_text = 7;        //加密过的数据 knockuid+timestamp+begintime+followuid+timeout+channelid用tocken加密
    KnockType knockType = 8;       //用于追踪识别哪种操作方式
    KnockStep knockStep = 9;       //用于追踪识别哪步
    int32 roomType = 10;            //房间类型
    int64 timeOutEndTime = 11;     //服务端超时结束时间
    int32 enter_room_type = 12;    //进房类型
}