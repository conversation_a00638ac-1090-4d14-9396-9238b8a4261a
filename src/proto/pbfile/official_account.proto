syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/official_account";

enum OfficialAccountCmd
{
    IN_APP_NAVIGATION = 1;
    SYSTEM_BROWSER = 2;

    ///////////////////////
    SPECIAL_COMMAND_BEGIN = 10000;  // 特殊命令开始
    GAME_DOWNLOAD = 10001;          // 游戏下载
    GET_LATEST_MESSAGE = 10002;     // 拉最新消息
}

message InAppNavigationCommand
{
    required string uri = 1;
}

message SystemBrowserCommand
{
    required string url = 1;
}

message GameDownloadCommand
{
    required string game_url  = 1;
    required uint32 game_id   = 2;
    required string game_name = 3;
    optional string game_pkg = 4;
}

message OfficialAccountSettings {
    enum SettingConstants {
        ENABLE = 1;
        DISABLE = 2;
    }

    optional uint32 message_notification = 1;   // ENABLE or DISABLE, any other values will be ignored
}

message OfficialAccountUpdateSettingsReq {
    required BaseReq base_req = 1;
    required uint32 official_account_id = 2;
    required OfficialAccountSettings settings = 3;
}

message OfficialAccountUpdateSettingsResp {
    required BaseResp base_resp = 1;
    required uint32 official_account_id = 2;
    required OfficialAccountSettings updated_settings = 3;
}

//拉取最新消息 -- v1.5
message OfficialAccountGetNewestMessageReq {
	required BaseReq   base_req               = 1;
	required uint32    official_account_id   = 2;
}

message OfficialAccountGetNewestMessageResp {
    required BaseResp base_resp               = 1;
    required uint32   official_account_id   = 2;
    required bool 	  has_message = 3;
}

//关注公众号  1.5版无入口， 暂不事先
//message subscribeOfficialAccountReq{
//    required BaseReq   base_req               = 1;
//    required uint32    official_account_id   = 2;
//}
//
//message subscribeOfficialAccountResp{
//    required BaseResp  base_resp             = 1;
//    required uint32    official_account_id   = 2;
//}

//取消关注公众号
message unsubscribeOfficialAccountReq{
    required BaseReq base_req = 1;
    required uint32  official_account_id = 2;
}

message unsubscribeOfficialAccountResp{
    required BaseResp base_resp = 1;
    required uint32   official_account_id = 2;
}

message GetOfficialAccountDetailReq {
	required BaseReq base_req = 1;
	required uint32 official_account_id = 2;
}

message GetOfficialAccountDetailResp {
	required BaseResp base_resp = 1;
	required OfficialAccountInfo detail = 2;
}
