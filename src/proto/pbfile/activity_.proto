syntax="proto2";

package ga;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/activity";

message GetMyFirstVoucherReq
{
	required BaseReq base_req = 1;
	required uint32 uid = 2;
}

message FirstVoucherInfo
{
	required uint32 product_id = 1;
	required uint32 worth = 2;
	required string game_name = 3;
	required string voucher_account = 4;
	required string voucher_password = 5;
	required uint32 purchase_time = 6;
	required string game_icon = 7;
}

message GetMyFirstVoucherResp
{
	required BaseResp base_resp = 1;
	repeated FirstVoucherInfo first_voucher_list = 2;
}

message AwardUserTTGiftPkgReq
{
	required BaseReq base_req = 1;
	required uint32 target_uid = 2;
}

message AwardUserTTGiftPkgResp
{
	required BaseResp base_resp = 1;
}

// 首次充值资格查询（资格 与 实际充值时间之间 是有时间间隔的）
message FirstRechargeActivitiEntry
{
	required string entry_icon_url = 1; // 入口图标
	required string back_img_url = 2;   // 背景图
	required uint32 finish_ts = 3 ;     // 截止时间戳
	optional uint32 pop_up_frequence = 4;	// 首充弹窗频率 N天一次
}

message CheckUserFirstRechargeActEntryReq
{
	required BaseReq base_req = 1;
}

message CheckUserFirstRechargeActEntryResp
{
	required BaseResp base_resp = 1;
	required bool is_first_recharge_fin = 2; // 首次充值是否完成
	optional FirstRechargeActivitiEntry entry_info = 3;
}

// 2019新年打年兽活动配置
message NewYear2019BeatActConf
{
	required uint32 act_begin_ts = 1;         // 整个活动开始时间点
	required uint32 act_finish_ts = 2;        // 整个活动结束时间点
	required uint32 interval_period_ts = 3 ;  // 每次打年兽活动的间隔周期 比如3600表示每个整点，600表示每个整10分钟
	
	optional uint32 payed_newyear_calls_ts = 4 ;  // 年兽 拜年时间点 比如2019-02-04 0点
	
	optional uint32 random_gamestart_delay_second = 5;      // 每次打年兽游戏开始前 随机等待的时间秒数 默认为5
	optional uint32 random_gamefin_report_delay_second = 6; // 每次打年兽游戏结束后 随机等待的上报结果时间秒数 默认为5
	
	optional uint32 catch_atleast_beat_cnt = 7; // 抓住年兽 最少需要击中多少次 默认为10
}

// 打地鼠类型的活动配置
message ChannelRoomMoleAttackGamePeriod
{
	required uint32 period_begin_ts  = 1 ;   // 周期开始时间点
	required uint32 period_finish_ts = 2;    // 周期结束时间点
	required uint32 period_interval_ts = 3;  // 周期内 每场游戏的间隔
}

message ChannelRoomMoleAttackGameConf
{
	required string game_name = 1;         // 活动的唯一标识 
	required uint32 game_begin_ts = 2 ;    // 整个活动开始时间点
	required uint32 game_finish_ts = 3;    // 整个活动结束时间点
	
	repeated ChannelRoomMoleAttackGamePeriod period_conf_list = 4;
	
	optional uint32 random_gamestart_delay_second = 5;      // 每次打地鼠 游戏开始前 随机等待的时间秒数 默认为5
	optional uint32 random_gamefin_report_delay_second = 6; // 每次打地鼠 游戏结束后 随机等待的上报结果时间秒数 默认为5
	
	optional uint32 game_prepare_second  = 7;            // 每场打地鼠游戏的准备(预备)阶段的时间秒
	optional uint32 game_duration_second = 8;            // 每场打地鼠游戏的游戏时间 （不包括准备(预备)阶段的时间）
	
	optional uint32 mole_appear_cnt = 9;                 // 地鼠在每场游戏中出现的次数
	optional uint32 attack_atleast_cnt = 10;             // 最少需要击中多少次 地鼠 默认为10
}

// 客户端上报打年兽的结果
message ReportNewYear2019BeatResultReq
{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;  
	required uint32 beat_cnt = 3;  
}
message ReportNewYear2019BeatResultResp
{
	required BaseResp base_resp = 1;
}

// 客户端 如果超时还没有收到奖励结果的PUSH 可以调用该接口来拉取
message GetNewYear2019BeatLotteryResultReq
{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;   
}
message GetNewYear2019BeatLotteryResultResp
{
	required BaseResp base_resp = 1;
	required NewYear2019BeatAward award_info = 2; // 可能没中奖哦(里面的字段为空)
}


// 打年兽活动的奖励
message NewYear2019BeatAward
{
	required uint32 channel_id = 1; 
	required uint32 uid = 2;	
	optional string award_name = 3;
	optional string award_url = 4;
	optional GenericMember recommend_user = 5; // 注意这个用户结构体 可能没有值哦
}

