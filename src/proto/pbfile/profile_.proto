syntax = "proto3";

package ga.profile;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/profile";

// 用户装饰品的类型
enum Type {
    INVALID = 0; // 无效类型
    FLOAT   = 1; // 主页飘
}

// 装饰品
message Decoration {
    Type typ = 1; // typ 代表装饰品的类型
    string id = 2; // id 代表（特定类型下的）装饰品ID
    string version = 3; // version 代表装饰品的版本
    string url = 4; // url 代表装饰品（z包）对应的链接
    string md5 = 5; // md5 代表装饰品（z包）的MD5
    string url_pic = 6; // url_pic 代表装饰品（图片）对应的链接
    int64 expiry_time = 7; // expiry_time 代表装饰品的过期时间（时间点）
    bool is_adorn = 8; // is_adorn 代表是否佩戴了装饰品
    string name = 9; // 代表装饰品的名字
}

message UserDecorationsReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2; // uid 代表用户ID
    Type typ = 3; // typ 代表希望获得哪一类装饰品
}

message UserDecorationsResp {
    ga.BaseResp base_resp = 1;
    repeated Decoration decs = 2; // decs 代表要用户所拥有的某类装饰品
}

message UserCurrDecorationReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2; // uid 代表用户ID
    Type typ = 3; // typ 代表希望获取哪一类用户当前佩戴的装饰品
}

message UserCurrDecorationResp {
    ga.BaseResp base_resp = 1;
    Decoration dec = 2; // dec 代表用户当前佩戴的装饰品
}

message UserAdornDecorationReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2; // uid 代表用户ID
    Type typ = 3; // typ 代表用户佩戴装饰品的类型
    string id = 4; // id 代表用户佩戴（特定类型下的）装饰品ID
    string version = 5; // version 代表用户佩戴装饰品的版本
}

message UserAdornDecorationResp {
    ga.BaseResp base_resp = 1;
    uint32 uid = 2; // uid 代表用户ID
    Type typ = 3; // typ 代表用户佩戴装饰品的类型
    string id = 4; // id 代表用户佩戴（特定类型下的）装饰品ID
    string version = 5; // version 代表用户佩戴装饰品的版本
}

message UserRemoveDecorationReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2; // uid 代表用户ID
    Type typ = 3; // typ 代表用户卸下装饰品的类型
}

message UserRemoveDecorationResp {
    ga.BaseResp base_resp = 1;
}
