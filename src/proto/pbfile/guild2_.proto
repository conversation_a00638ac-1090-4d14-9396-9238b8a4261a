syntax="proto2";

package ga;
import "ga_base.proto";
import "giftpkg2_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/guild";

//领取记录
message GuildProductGainRecord
{
    required uint32 uid = 1;
	required string nick_name = 2;
	required uint32 contribution_lv = 3; //贡献等级
    required uint64 product_id = 4;
	required uint32 date = 5;
}

//公会商品详情
enum GuildProductStatus
{
    Shelve = 1; // 上架
    SoldOut = 2; // 已售完
    UnShelve = 3;  // 下架
}

message GuildProductDetail
{
    required GiftProductDetail product     = 1;
	required uint32 examine                = 2; //是否需要审查,0:不需要
    required uint32 product_status         = 3; //see GuildProductStatus
    optional uint32 record_total           = 4; //被领取数量
    repeated GuildProductGainRecord record_lst = 5; //领取记录
	optional string extend_info            = 6;      //拓展json字段
/*
代金券类型拓展属性:
ly_ticket_id //礼品卡id
gift_card_balance //礼品卡余额
*/
}

//message GuildGameProductLst
//{
//    required uint32 guild_id = 1;
//    required uint32 game_id = 2;
//    repeated GuildProductDetail product_lst = 3;
//}

//首页
message GuildStorageReq
{
    required BaseReq base_req = 1;
    optional uint32 source_type = 2;//see giftpkg2_.EGiftPktSourceType
    optional uint32 product_type = 3;//see giftpkg2_.EGiftPktItemType
    optional uint32 product_status = 4; //see GuildProductStatus
	optional uint32 index = 5;
	optional uint32 cnt = 6;
}

message GuildStorageResp
{
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 contribution = 3;//可用贡献
    repeated GuildProductDetail product_lst = 4;
    optional uint32 examine_total = 5; //待审批总数
	optional uint32 product_total = 6;
}

//根据类型啦商品列表
//message GetGuildProductLstReq
//{
//    required BaseReq base_req = 1;
//    required uint32 guild_id  = 2;
//    optional uint32 source_type = 3;//see giftpkg2_.EGiftPktSourceType
//    optional uint32 product_type = 4;//see giftpkg2_.EGiftPktItemType
//    optional uint32 product_status = 5;
//    optional uint32 index = 6;
//    optional uint32 cnt = 7;
//}
//
//message GetGuildProductLstResp
//{
//    required BaseResp base_resp = 1;
//    repeated GuildProductDetail product_lst = 2;
//}

//调整,上下架
enum GuildStorageOperType
{
    Oper_Shelve = 1; // 上架
    Oper_UnShelve = 2; //下架 
	Oper_Modify = 3; //调整
	Oper_BUY_FROM_TT = 4; //向TT商城购买
	Oper_Allot = 5; //分配
	Oper_TT_Grant = 6; //官方发放
	Oper_Owner_Upload = 7; //会长上传物品
}

message GuildProductModifyReq
{
    required BaseReq base_req      = 1;
    required uint64 product_id     = 2; //商品ID
    required uint32 oper_type      = 3; //操作类型,see GuildStorageOperType(上架，下架，修改)
    optional uint32 num            = 4; //上架数量
    optional uint32 price          = 5; //商品价格
	optional uint32 examine        = 6; //是否需要审批0表示不用
	optional uint32 fetch_unlimited= 7; //不限领 0限领 1不限领
	optional uint32 day_limit	   = 8; //成员入会时间限制
}

message GuildProductModifyResp
{
    required BaseResp base_resp    = 1;
	optional string tips = 2;
	optional uint32 cooldown = 3;
}

//分配
message GuildProductAllotReq
{
    required BaseReq base_req      = 1;
	required uint32 to_uid         = 2; //uid
    required uint64 product_id     = 3; //商品ID
    required uint32 num            = 4; //
}

message GuildProductAllotResp
{
    required BaseResp base_resp    = 1;
	optional string tips = 2;
	optional uint32 cooldown = 3;
}

//搜索
message GuildProductSearchReq
{
    required BaseReq base_req = 1;
    required string  key_word = 2;
    optional uint32  index    = 3;
    optional uint32  cnt      = 4;
}

message GuildProductSearchResp
{
    required BaseResp base_resp = 1;
    repeated GuildProductDetail product_lst = 2;
    optional uint32 total = 3;
}

//删除商品
message GuildRemoveProductReq
{
    required BaseReq base_req = 1;
	required uint64 product_id = 2;
}

message GuildRemoveProductResp
{
    required BaseResp base_resp = 1;
}

/*礼品卡相关*/

//礼品卡结构
enum EGiftCardStatusType
{
	EGiftCardStatus_Normal = 1;
	EGiftCardStatus_Forzen = 2; //冻结
	EGiftCardStatus_Recycle = 3; //撤回
}

message GuildGiftCardInfo
{
	required uint32 ly_ticket_id = 1;
	required string name = 2;       //代金券名字
	required uint32 upper_bound= 3; //单张面额上限
	required uint32 remain_limit = 4;//剩余额度

	optional uint32 valid_s_date = 5; //有效期，目前只有结束时间, 此处先为0
	optional uint32 valid_e_date = 6; //

	optional uint32 game_id = 7; //

	optional string usage = 8; //使用条件
	optional string description = 9; //注意事项
	optional uint32 status = 10; //状态 see EGiftCardStatusType
}

//获取礼品卡
message GuildGetGiftCardReq
{
    required BaseReq base_req = 1;
	optional uint32 ly_ticket_id = 2; //礼品卡id,不填或者填0拉全部
}

message GuildGetGiftCardResp
{
    required BaseResp base_resp = 1;
	repeated GuildGiftCardInfo card_lst = 2; //礼品卡列表
}


//拆分礼品卡
message GuildSplitGiftCardReq
{
    required BaseReq base_req = 1;
	required uint32 ly_ticket_id = 2; //礼品卡id
	required uint32 value = 3; //面值

	//商品属性
	required uint32 num = 4; //数量
	required uint32 price =  5;  //购买价格
	required uint32 examine = 6; //是否需要审核
}

message GuildSplitGiftCardResp
{
    required BaseResp base_resp = 1;
	optional GuildProductDetail product = 2;
	optional string tips = 3;
	optional uint32 cooldown = 4;
}

//分配礼品卡
message GuildAllotGiftCardReq
{
    required BaseReq base_req = 1;
	required uint32 ly_ticket_id = 2; //礼品卡id
	required uint32 to_uid = 3;
	required uint32 value = 4; //面值
	required uint32 num = 5; //数量
}

message GuildAllotGiftCardResp
{
    required BaseResp base_resp = 1;
	optional string tips = 2;
	optional uint32 cooldown = 3;
}

//删除礼品卡
message GuildRemoveGiftCardReq
{
    required BaseReq base_req = 1;
	required uint32 ly_ticket_id = 2; //礼品卡id
}

message GuildRemoveGiftCardResp
{
    required BaseResp base_resp = 1;
}

/*礼品卡相关end*/

//会长上传
message GuildProductUpLoadReq
{
    required BaseReq base_req = 1;
	required uint32 examine = 2;   //是否审查
    required GiftProductDetail product = 3;
/*
忽略字段:examine, icon, product_id, status, price, currency_type

currency_type = GIFTPKT_CURRENCY_TYPE_GUILD_MEMBER_CONTRIBUTION
game_id没有绑定的填0或者不填，绑定的填游戏id

*/
}

message GuildProductUpLoadResp
{
    required BaseResp base_resp = 1;
}

//仓库操作记录
message GuildStorageOperRecord
{
    required uint32 uid = 1;
	required string nick_name = 2; //操作人
	required uint32 oper_type = 3; //操作类型 see GuildStorageOperType
	required uint32 date = 4;
    optional string product_name = 5; //商品名字
    optional uint32 num  = 6;
    optional uint32 price_type = 7; //货币类型
    optional uint32 price = 8; //价格
    optional uint32 payment = 9; //付款
    optional uint32 to_uid = 10; //被操作人
    optional string to_nick_name = 11;
}

message GuildStorageGetOperRecordReq
{
    required BaseReq base_req = 1;
	optional uint32 index = 2;
	optional uint32 cnt = 3;
}

message GuildStorageGetOperRecordResp
{
    required BaseResp base_resp = 1;
	required uint32 total = 2;
	repeated GuildStorageOperRecord record_lst = 3;
}

//礼包审查相关
enum EGuildExamineStatus
{
	ExamineStatusNone   = 0; //未审核
	ExamineStatusAccept = 1; //同意
	ExamineStatusReject = 2; //拒绝
}

message GuildProductExamineRecord
{
	required uint32 examine_id = 1; //审查Id
	required GuildMember user = 2;
	required GiftProduct product = 3; //商品快照
    required uint32 examine_status = 4; //审核状态
	required uint32 date = 5;
    optional uint32 total_contribution = 6; //用户历史总贡献
}

message GetGuildProductExamineLstReq
{
    required BaseReq base_req = 1;
}

message GetGuildProductExamineLstResp
{
    required BaseResp base_resp = 1;
	repeated GuildProductExamineRecord record_lst = 2;
}

message GuildProductExamineReq
{
    required BaseReq base_req = 1;
	required uint32 examine_id = 2; //审查ID
	required uint32 agree = 3; //0:拒绝 1:同意
    optional string reject_msg = 4; //拒绝原因
}

message GuildProductExamineResp
{
    required BaseResp base_resp = 1;
}


//打开商品修改页面
message GuildGetProductDetailReq
{
    required BaseReq base_req = 1;
    required uint32  product_id = 2;
}

message GuildGetProductDetailResp
{
    required BaseResp base_resp = 1;
    required GiftProductDetail product = 2;
}

//修改下架商品
message GuildEditProductReq
{
    required BaseReq base_req = 1;
	optional GiftProductDetail product = 2;
	//不准改卡密
}

message GuildEditProductResp
{
    required BaseResp base_resp = 1;
}

//我的宝箱
//message GuildGetRewardReq
//{
//    required BaseReq base_req = 1;
//}
//
//message GuildGetRewardResp
//{
//    required BaseResp base_resp = 1;
//    repeated GuildProductDetail product_lst = 2;
//}

// 最近登录游戏
message RecentLoginGame {
	required uint32 game_id = 1;
	optional uint32	login_ts = 2;	// 登录时间
	optional string icon_url = 3;	// 图标
	required string name = 4;	// 游戏名称
}

// 个人贡献列表
message GuildMemberContributionDetail
{
	enum CONTRIBUTION_TYPE {
		CHECK_IN_DAILY = 1;			// 每日签到
		CHECK_IN_SUPPLEMENT = 2;	// 补签
		DONATION = 3;				// 捐献
		CAPITAL_INJECTION = 4;		// 注资消费
		CONSUME_GAME = 5;			// 游戏消费
		CONSUME_HAPPY_CENTER = 6;	// 欢城消费
		BUY_GIFT = 7;				// 购买礼包
		SEND_PRESENT = 8;			// 送礼物
		
		SEND_BACK = 101;			// 退还贡献（如申请购买公会商品后，审批不通过时退还购买时消耗的贡献）
		OFFICIAL_AWARD = 102;		// 后台发放给用户的奖励（补偿，奖励之类的）
	
		CHECK_IN_CONTINUOUS = 201;	// 连续签到
		DONATE_CONTINUOUS = 202;	// 连续捐献
	}

	required uint32 uid = 1;
	required uint32 guild_id = 2;
	required uint32 contribution_type = 3;	// CONTRIBUTION_TYPE
	required int32 contribution_value = 4;	
	required uint32 create_ts = 5;
	optional string desc = 6;
}

// 获取公会成员贡献明细
message GuildGetMemberContributionListReq {
	required BaseReq base_req = 1;
	required uint32 offset = 2;
    required uint32 count = 3;
	required uint32 guild_id = 4;
}

message GuildGetMemberContributionListResp {
	required BaseResp base_resp = 1;
	required uint32 offset = 2;
    repeated GuildMemberContributionDetail contribution_list = 3;
}

// 公会个人名片
message GuildGetMemberCardReq {
	required BaseReq base_req = 1;
	required uint32 target_uid = 2;
	required uint32 guild_id = 3;
	optional uint32 target_guild_id = 4; // 一般不填；填了的话，target_guild_id与target_uid的当前公会id不一致时报错
}

message GuildGetMemberCardResp {
	required BaseResp base_resp = 1;
	required GuildMember member_info = 2;	// 公会成员信息
	required GuildCheckin checkin_info = 3;	// 签到信息
	required GuildDonate donate_info = 4;	// 捐献信息
	required GuildMemberContribution contribution = 5;	//公会成员贡献
	required uint32 last_checkin_ts = 6;	// 上次签到时间
	required uint32 last_donate_ts = 7;		// 上次捐献时间
	repeated GuildGroup group_list = 8;		// 成员所在公会群
	optional RecentLoginGame login_game = 9;	// 最近登录游戏
	repeated UserGame games = 10;			// 在玩游戏
	optional string valid_msg = 11;			// 账号有效性提示
	optional PresentSummary present_info = 12;	// 用户在公会送出的礼物
}

// 成员的排行信息
message GuildMemberRankInfo {
	required GuildMember member_info = 1;
	required string desc = 2; // 客户端显示的描述
	
	optional uint32 online_status      = 3;    // 在线状态 0 离线 1在线 公会总群的成员在线状态
    optional uint32 online_status_ts   = 4;    // 在线状态变化的时间 只有离线用户才会有值 表示最后离线的时间
}

//获取成员列表(公会成员列表 总群成员列表)
message GuildGetMemberListByRankTypeReq {
	enum RankType {
        CHECK_IN = 0;		// 签到
        CONTRIBUTION = 1;	// 贡献
		CONSUME = 2;		// 消费
		ONLINESTATUS = 3;  // 在线情况
    }
	
    required BaseReq base_req = 1;
	required uint32 guild_id = 2;
    required uint32 rank_type = 3;	//RankType
	
    optional uint32 member_offset = 4;
    optional uint32 member_count = 5;
	
	repeated uint32 opt_uid_list = 6;
	optional uint32 data_type = 7;	//0.非默认初始数据 1.默认初始数据
}

message GuildGetMemberListByRankTypeResp {
    required BaseResp base_resp = 1;
	required uint32 rank_type = 2;	//RankType
	optional uint32 offset = 3;
    repeated GuildMemberRankInfo member_info = 4;
	optional uint32 data_type = 5;	//0.非默认初始数据 1.默认初始数据
}

// 捐献选项
message GuildDonateOption {
	required uint32 donate_value = 1;	// 捐献值
	optional bool	is_valid = 2;	// 是否可选
}

// 查询捐献选项
message GuildGetDonateOptionReq {
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
}

message GuildGetDonateOptionResp {
	required BaseResp base_resp = 1;
	repeated GuildDonateOption option_list = 2;	// 捐献额度列表
	required uint32 total_contribution = 3;	// 历史总贡献
	required uint32 valid_contribution = 4;	// 当前可用贡献
	required uint32 member_lv = 5;	// 个人等级
}

// 公会捐献
message GuildDonateReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
	required uint32 donate_value = 3;	// 捐献值
}

message GuildDonateResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 days = 3; // 连续捐献天数
	optional int32 member_contribution_added = 4;	// 增加的个人贡献
	optional int32 guild_contribution_added = 5;	// 增加的公会贡献
	optional uint32 to_bonus_period = 6;	// 连捐加成周期(仅显示用，不是本次捐献的天数)
	optional int32 to_bonus_contribution = 7;	// 连捐加成贡献(仅显示用，不是本次捐献增加的贡献)
}

// 捐献列表
message GuildGetDonateListReq {
    required BaseReq base_req = 1;
	required uint32 guild_id = 2;
	optional uint32 page = 3;
	optional uint32 count = 4;
}

message GuildGetDonateListResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    repeated GuildDonate donate_info = 3;
}

// 设置公会成员称号
message GuildSetMemberTitleReq {
    required BaseReq base_req = 1;
	required uint32 target_uid = 2;
	required string title = 3;
	required uint32 guild_id = 4;
}

message GuildSetMemberTitleResp {
    required BaseResp base_resp = 1;
	required uint32 target_uid = 2;
	required string title = 3;
	required uint32 guild_id = 4;
}

message GuildMemberTitleInfo {
	required uint32 uid = 1;
	required uint32 guild_id = 2;
	required string title = 3;
}

// 获取公会成员称号列表
message GuildGetMemberTitleListReq {
    required BaseReq base_req = 1;
	required uint32 guild_id = 2;
	required uint32 offset = 3;
	required uint32 count = 4;
}

message GuildGetMemberTitleListResp {
    required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
	required uint32 offset = 3;
	repeated GuildMemberTitleInfo title_list = 4;
}

//公会主打游戏列表
message GuildGameListGetReq{
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
}

message GuildGameListGetResp{
    required BaseResp base_resp = 1;
    required uint32 game_count = 2;         //已添加主打游戏数
    required uint32 game_all = 3;          //可添加游戏总数
    required uint32 extra_count = 4;        //已扩充主打游戏数
    required uint32 extra_all = 5;         //剩余扩充游戏数
    required uint32 extra_cost = 6;         //扩充贡献值

    required uint32 level = 7;              //公会等级
    required uint32 contribution = 8;      //公会贡献值
    repeated Game game_list = 9;           //游戏列表
}


//添加额外主打游戏
message GuildGameExtraListAddReq{
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;  
}

message GuildGameExtraListAddResp {
    required BaseResp base_resp = 1;
    required uint32 game_all = 2;          //可添加游戏总数
    required uint32 extra_count = 3;
}

//修改游戏群
message GuildModifyGroupGameReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 group_id =3;
    required uint32 game_id = 4;
}

message GuildModifyGroupGameResp {
    required BaseResp base_resp = 1;
    required uint32 game_id = 2;
}

//创建公会职位
message GuildOfficialCreateReq{
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required string name = 3;
    required uint32 permission = 4;
}

message GuildOfficialCreateResp{
    required BaseResp base_resp = 1;
    required string name = 2;
    required uint32 permission = 3;
}


//修改公会职位
message GuildOfficialModifyReq{
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 Official_id = 3;
    required uint32 permission = 4;
    required string name = 5;
}

message GuildOfficialModifyResp{
    required BaseResp base_resp = 1;
    required uint32 official_id = 2;
    required uint32 permission = 3;
    required string name = 4;
}

//获取职位成员列表
message GuildOfficialInfoGetReq{
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required bool req_all = 3;
}

message GuildOfficialInfoGetResp{
    required BaseResp base_resp = 1;
    repeated GuildOfficialMember member_list = 2;
}

//获取职位下的成员
message GuildOfficialMemberGetReq{
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 official_id = 3;
}

message GuildOfficialMemberGetResp{
    required BaseResp base_resp = 1;
    repeated GuildMember member_list = 2;
}

//任命官员
message GuildOfficialMemberAddReq{
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 official_id = 3;
    repeated uint32 uid_list = 4;
}

message GuildOfficialMemberAddResp{
    required BaseResp base_resp = 1;
    repeated GuildOfficialMember member_list = 2;
}

//撤销官员
message GuildOfficialMemberRemoveReq{
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    repeated uint32 uid_list = 3;
}

message GuildOfficialMemberRemoveResp{
    required BaseResp base_resp = 1;
    repeated GuildOfficialMember member_list = 2;
}

//删除职位
message GuildOfficailDelReq{
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 official_id = 3;   
}

message GuildOfficailDelResp{
    required BaseResp base_resp = 1;
}

message GuildJoinRecord {
	required uint32 uid = 1;
	required string user_account = 2;
    required string user_nick = 3;
	required uint32 joined_at = 4;
}

//获取公会的成员加入历史
message GuildGetJoinHistoryReq{
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
	optional uint32 offset = 3;
	optional uint32 count = 4;
}

message GuildGetJoinHistoryResp{
	required BaseResp base_resp = 1;
	repeated GuildJoinRecord join_list = 2;
}

//查询用户的职位信息
message GuildOfficialInfoGetByUidReq{
    required BaseReq base_req = 1;
    required uint32 uid = 2;
}

message GuildOfficialInfoGetByUidResp{
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required string guild_name = 3;
    required uint32 role = 4;
    required string role_name = 5;
    optional uint32 short_id = 6;
    optional string title_name = 7;
    optional uint32 member_lv = 8;
}

// 解除公会黑名单
message GuildRemoveBlackListReq{
	required BaseReq base_req = 1;
    repeated uint32 uid_black_list = 2;
}

message GuildRemoveBlackListResp{
	required BaseResp base_resp = 1;
}

message GuildBlackListUser {
	 required uint32 uid = 1;
    required string name = 2;		// 昵称
    required string account = 3;	// 用户唯一账号
	optional string account_alias = 4; //账号别名
    required string face_md5 = 5;	// 头像md5
}

//获取公会黑名单
message GuildGetBlackListReq{
	required BaseReq base_req = 1;
	optional uint32 offset = 2;
	optional uint32 limit = 3;
}

message GuildGetBlackListResp{
	required BaseResp base_resp = 1;
	repeated GuildBlackListUser black_list = 2;
}

// 获取公会礼物信息
message GetGuildPresentInfoReq{
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
}

message GetGuildPresentInfoResp{
	required BaseResp base_resp = 1;
	required uint32 total_value = 2; // 礼物总价值
}

message GuildGameInfo
{
	required uint32 game_id = 1;
	required string game_name = 2;
	required string game_icon = 3;
	optional uint32 guild_count = 4;	// 入驻公会数量
	required string game_pkg = 5;
	optional string ios_game_key = 6;
}

//公会主打游戏列表
message GuildGameListGetV2Req{
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
}

message GuildGameListGetV2Resp{
    required BaseResp base_resp = 1;
    repeated GuildGameInfo game_list = 2;           //游戏列表
	required uint32 guild_id = 3;
}

//更换公会游戏
message GuildChangeGameReq {
    required BaseReq base_req = 1;
	required uint32 old_game_id = 2;		// 旧的游戏ID
	required uint32 new_game_id = 3;		// 新的游戏ID
	required uint32 guild_id = 4;
}

message GuildChangeGameResp {
    required BaseResp base_resp = 1;
    repeated GuildGameInfo game_list = 2;		// 返回公会所有游戏
}

//公会热门游戏列表
message GuildGetHotGameListReq{
    required BaseReq base_req = 1;
}

message GuildGetHotGameListResp{
    required BaseResp base_resp = 1;
    repeated GuildGameInfo games = 2;
}

//添加公会游戏
message GuildAddGameV2Req {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
	repeated uint32 game_id_list = 3;		// 游戏ID
}

message GuildAddGameV2Resp {
    required BaseResp base_resp = 1;
    repeated GuildGameInfo game_list = 2;		// 返回公会所有游戏
}

// 删除公会游戏
message GuildDeleteGuildGameV2Req {
	required BaseReq base_req = 1;
	required uint32 guild_id = 2;
	repeated uint32 game_id_list = 3;
}

message GuildDeleteGuildGameV2Resp {
	required BaseResp base_resp = 1;
	repeated GuildGameInfo game_list = 2;		// 返回公会所有游戏
}
