syntax="proto2";

package ga;
import "ga_base.proto";
import "circle_.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/game";

message GetUserGamesReq {
    required BaseReq base_req = 1;
    required string target_account = 2;	//用户账号
}

message GetUserGamesResp {
    required BaseResp base_resp = 1;
    repeated UserGame games = 2;
    required string target_account = 3;	//用户帐号
}

// 安卓上报 用户安装的游戏
message UploadUserGamesReq{
    required BaseReq base_req = 1;
    repeated LocalGame games = 2;
	optional bool is_full_cover = 3; // 上报的游戏是否是全量数据
}

message UploadUserGamesResp{
    required BaseResp base_resp = 1;
}

// IOS上报 用户安装的游戏
// IOS 只有一个特殊的KEY，每个KEY唯一对应一个应用
message UploadIosUserGamesReq{
    required BaseReq base_req = 1;
	repeated string install_ios_game_key_list = 2;  // 安装的游戏列表
	optional bool is_full_cover = 3; 				// 上报的游戏是否是全量数据
	repeated string uninstall_game_key_list = 4;    // 删除的游戏列表 只有的非全量上报的时候填写
}

message UploadIosUserGamesResp{
    required BaseResp base_resp = 1;
}

// 拉取IOS关心的需要上报的游戏KEY列表
// IOS根据这个列表去查询他本机是否有安装指定的游戏 然后再上报
message GetIosNeedUploadConfGameListReq{
    required BaseReq base_req = 1;
}

message GetIosNeedUploadConfGameListResp{
    required BaseResp base_resp = 1;
	repeated string ios_game_key_list = 2;
}

message MatchUserGameReq {
    required BaseReq base_req = 1;
    repeated LocalGame games = 2;
}

message MatchedGame {
    required uint32 server_game_id = 1;
    required string local_package = 2;
}

message MatchUserGameResp {
    required BaseResp base_resp = 1;
    repeated MatchedGame matched_game_list = 2;
}

message HotGamesReq{
    required BaseReq base_req = 1;
}

message HotGamesResp{
    required BaseResp base_resp = 1;
    repeated Game games = 2;
}

message FeaturedGamesReq{
    required BaseReq base_req = 1;
}

message FeaturedGamesResp{
    required BaseResp base_resp = 1;
    repeated Game games = 2;
}

message GuildPlayingGameReq{
    required BaseReq base_req = 1;
    repeated LocalGame local_game_pkg_list = 2;
}

message GuildPlayingGameResp{
    required BaseResp base_resp = 1;
    repeated Game guild_playing_list = 2;
    repeated Game my_game_list = 3;
}


message SearchGameReq{
    required BaseReq base_req = 1;
    required string key_word = 2; 	//游戏名称 id
    optional uint32 need_guild_info = 3;	// 0：不需要公会信息，1：需要返回公会信息
}

message SearchGameResp{
    required BaseResp base_resp = 1;
    repeated Game games = 2;
    required string key_word = 3;
}

message GetGameConfigReq {
    required BaseReq base_req = 1;
}

message GetGameConfigResp{
    required BaseResp base_resp = 1;
    required uint32 last_update = 2;
    repeated string black_list = 3;		// 语音球包名黑名单(强制不显示)
    repeated string white_list = 4; 	// 语音球包名白名单(强制显示)
}

message IncreaseGamePackageDownloadCountReq {
    required BaseReq base_req = 1;
    required uint32 game_id = 2;
}

message IncreaseGamePackageDownloadCountResp{
    required BaseResp base_resp = 1;
}

message TopGame {
    required uint32 game_id= 1;
    required string game_name = 2;              /* 游戏名称 */
    required string game_pkt_name = 3;          /* 游戏包名 */
    required string game_area_url = 4;          /* 跳转游戏专区 */
    required string game_icon_url = 5;          /* 图标链接     */
    required string game_desc = 6;              /* 游戏描述     */
    required uint32 game_status = 7;            /* 游戏状态：参考ga_base.proto的GAME_STATUS*/
    required string game_type = 8;              /* 游戏类型：RPG、FPS */
    required uint32 downloaded_num  = 9;         /* 已下载用户数 */
    required uint32 followed_num    = 10;        /* 已关注用户数 */
    required bool is_followed     = 11;        /* 是否关注 */
    required bool is_pkt_ready    = 12;        /* 是否可下载 */
    required uint32 circle_id = 13;             /*游戏圈ID*/
    required string game_ver = 14;              //版本;
    required string game_size = 15;             //大小
    repeated string game_tag = 16;
    optional bool   is_order_ready  = 17;   //是否可以预约
    optional bool   is_order        = 18;   //是否预约
    optional string preorder_url    = 19;   //预约活动页
    repeated string tags            = 20;   //游戏tag
    optional string corner_icon     = 21;   //角标
	optional uint32 publish_time	= 22;	//发行时间
	optional uint32 order_count		= 23;   //预约用户数
}

message TopGameGetListReq {
    required BaseReq base_req = 1;
    optional uint32 game_type = 2; //see ga_ase.proto:enum GAME_LIST_TYPE
}

message TopGameGetListResp {
    required BaseResp base_resp = 1;
    repeated TopGame topgame_list = 2;
    optional uint32 game_type = 3;
}

message TopGameGetDownloadUrlReq {
	required BaseReq base_req = 1;
	required uint32 game_id = 2;
}

message TopGameGetDownloadUrlResp {
	required BaseResp base_resp = 1;
	required uint32 game_id = 2;
	required string download_url = 3;
    required string channel_name = 4;
    optional string file_md5 = 6;
    optional string head_md5 = 7;
    optional uint32 ly_game_id = 8;
}

message CheckGameUpgrade {
    required uint32 game_id = 1;
	required string game_pkg = 2;
	required uint32 version_code = 3;
    optional string new_game_pkt = 4;
    optional string game_name = 5;
}

message CheckGameUpgradeReq {
	required BaseReq base_req = 1;
	repeated CheckGameUpgrade tt_game_pkg_list = 2;
	repeated CheckGameUpgrade other_game_pkg_list = 3;
}

message CheckGameUpgradeResp {
	required BaseResp base_resp = 1;
	repeated CheckGameUpgrade tt_update_pkg_list = 2;
	repeated CheckGameUpgrade other_update_pkg_list = 3;
    repeated CheckGameUpgrade preorder_pkg_list = 4;    /*里面的version_code无效*/
}

message SearchGameAndCircleReq {
	required BaseReq base_req = 1;
	required string key_word = 2; 	//游戏名称
	optional uint32 need_guild_info = 3; //游戏工会信息,0不需要,1需要
}

message SearchGameAndCircleResp {
    required BaseResp base_resp = 1;
    required string key_word = 2;
    repeated TopGame games = 3;         //游戏
	repeated GameCircle circles = 4; //相关圈子
}

message SearchGuildPlayingReq {
	required BaseReq base_req = 1;  //
}

message SearchGuildPlayingResp {
    required BaseResp base_resp = 1;
	repeated Game guild_playing_list = 2; //工会在玩游戏
}

// 福利活动类型
message WelfareDetailInfo {
    required uint32 game_id     = 1; // 游戏 ID
    required uint32 type        = 2; // 官方或者非官方活动类型
    required string tt_url      = 3; // 跳转 webview 的 url
    required string main_icon   = 4; // 福利活动 ICON
    required string title       = 5; // 福利活动标题
    required uint32 start_date  = 6; // 福利活动开始时间
    required uint32 end_date    = 7; // 福利活动结束时间
    required string game_name   = 8; //  福利活动所匹配的游戏名
    required uint32 game_user_count     = 9; // 福利活动所匹配的游戏用户数
    required string game_icon           = 10; // 福利活动所匹配的游戏 ICON
    optional uint32 red_diamond_count   = 11; // 福利活动所匹配的所需红钻数
    optional string remark_content      = 12; //福利活动备注
}

// 获取福利活动列表
message GetWelfareListReq {
    required BaseReq base_req    = 1;
    required uint32 request_type = 2;
    optional uint32 game_id = 3;
}

message GetWelfareListResp {
    required BaseResp base_resp       = 1;
    required uint32 request_type      = 2;
    repeated WelfareDetailInfo welfare_list = 3;  // 福利活动列表
}

//////////////////////////////游戏标签页////////////////////////////////
message GameDetailInfo
{
    enum GameType {
        NORMAL = 0;         // 正式上线
        H5Game = 1;         // html5游戏
    }
    required uint32 game_id         = 1;    //游戏ID
    required string game_name       = 2;    //游戏名
    required string area_url        = 3;    //游戏专区或游戏圈跳转地址
    required string icon_url        = 4;    //图标地址
    required bool   is_pkt_ready    = 5;    //是否可下载
    optional bool   is_followed     = 6;    //是否关注
    optional string package_name    = 7;    //游戏包名
    optional string game_desc       = 8;    //游戏描述
    optional uint32 downloaded_num  = 9;   //下载数
    optional uint32 followed_num    = 10;   //关注数
    optional uint32 circle_id       = 11;   //游戏圈ID
    optional string game_size       = 12;   //游戏大小
    optional string game_status     = 13;   //游戏状态:正式上线,删档内测，不删档公测
    optional bool   is_top          = 14;   //精品专区首位游戏标记
    optional uint32 game_type       = 15;   //游戏类型:0,normal;1,H5game
    optional string h5_game_url     = 16;   //Html5游戏链接
    optional bool   is_order_ready  = 17;   //是否可以预约
    optional bool   is_order        = 18;   //是否预约
    optional string preorder_url    = 19;   //预约活动页
    repeated string tags            = 20;   //游戏tag
    optional string corner_icon     = 21;   //角标
	optional uint32 topic_count		= 22;	//圈子主题数
}

//最近玩的游戏
message RecentGame
{
    required uint32 game_id = 1;
    required string game_name = 2;
    required string game_icon = 3;
    required uint32 topic_num = 4;
    required uint32 act_num = 5;
    required uint32 gift_num = 6;
    optional string corner_icon     = 7;   //角标
}

message PreorderGame
{
    required uint32 game_id = 1;
    required string url = 2;        //预约专题页链接
    required string img_url = 3;    //预约图
    required uint32 count = 4;      //预约人数
    required bool is_order = 5;     //是否预约
}

//运营广告
message GameActivityAd{
    required string img = 1;
    required string url = 2;
}

message GetGameTabListReq {
    required BaseReq base_req = 1;
}


message GetGameTabListResp {
    required BaseResp base_resp             = 1;
    repeated GameDetailInfo best_game_list  = 2;
    repeated GameDetailInfo new_game_list   = 3;    //新游榜
    repeated GameDetailInfo hot_game_list   = 4;    //热游榜
    optional string match_title          = 5;
    optional string match_icon           = 6;
    optional string match_url            = 7;
    repeated RecentGame recent_game_list = 8;//最近爱玩游戏
    repeated GameDetailInfo guild_game_list = 9;//新游推荐榜前5
    repeated PreorderGame preorder_game_list = 10;//预约游戏    
    optional string seach_default_name = 11;    //搜索栏默认字符串
    repeated GameActivityAd ad_list = 12;       //广告位
}

////////////////////////////

//////// 2.7版本 游戏tab首页推荐

message GameWelfareBaseInfo
{
	required string id          = 1; // 官方或者非官方活动类型
    optional string main_icon   = 2; // 福利活动 ICON
    optional string title       = 3; // 福利活动标题
    optional uint32 start_date  = 4; // 福利活动开始时间
    optional uint32 end_date    = 5; // 福利活动结束时间
	optional string detail_url  = 6; // 详情url 可能是TT内的也可能是h5
}

message GameVideoInfo
{
	required string video_url         = 1; // 视频url
    optional string snapshot_url      = 2; // 视频截图url
    optional string name   = 3;            // 视频名称
}

message GameActiveInfo
{
	required string act_url         = 1; // 游戏活动跳转url
    optional string act_img_url     = 2; // 游戏活动图片url
    optional string act_name        = 3; // 名称
}

message GameEventInfo
{
	required uint32 event_time         = 1;
	required string event_title        = 2;
	optional string event_sub_title   = 3;
}

//
message GameActivity
{
	required string act_title = 1;
	required string act_url = 2;          // 活动链接
	optional string act_img_url = 3;      // 卡片配图链接
	optional string act_sub_title = 4;    // 副标题 或者 描述
	optional uint32 begin_timestamp = 5;  // 开始时间
	optional uint32 end_timestamp = 6;    // 结束时间
	
}
// 复合游戏卡(2.9.2版本取消这种卡片)
message GameComplexRecommendCard
{
	required uint64 card_id = 1;
	
	required uint32 game_id = 2;
	required string game_name = 3;
	repeated string tag_list = 4;           // 游戏的标签
	
	required uint32 download_total_cnt = 5; // 下载数
	required string download_url = 6;       // 下载链接

	optional uint32 circle_id = 7;          // 圈子ID
	optional uint32 circle_topic_ount = 8;  // 圈子里的主题数目
	optional uint32 circle_focus_ount = 9;  // 圈子关注数
	
	optional uint32 group_id = 10;           // 官方T群ID
	optional uint32 group_member_cnt = 11;   // T群内的成员数目
	
	optional string background_image_url = 12; // 游戏底图 默认情况下与游戏专区的顶部底图一致 如果有game_activity_info信息
	
	optional uint32 guild_total_cnt = 13;      // 该游戏下的公会数目
	
	// 福利活动列表
	repeated GameWelfareBaseInfo welfare_list = 14;
	
	// 视频列表
	repeated GameVideoInfo video_list = 16;
	
	// 事件列表（开服信息）
	repeated GameEventInfo event_list = 17;
	
	// 游戏底图 跳转活动地址
	optional GameActivity game_activity_info = 18;   
	
	// 专区或者圈子链接
	optional string game_zone_url = 19;   

	optional string package_name = 20;    //游戏包名	
}

// 固定榜单卡
message FixedTopListRecommendCard
{
	required uint64 card_id = 1;
	repeated GameDetailInfo game_list = 2;
	required string toplist_title = 3;
}

// 标签榜单卡
message TagTopListRecommendCard
{
	required uint64 card_id = 1;
	repeated GameDetailInfo game_list = 2;
	required string toplist_title = 3;
	required string toplist_tag = 4;     // 标签
}

// 游戏下载卡(2.9.2版本取消这种卡片)
message GameSimpleRecommendCard
{
	required uint64 card_id = 1;
	required uint32 game_id = 2;
	required string game_name = 3;
	required string game_url = 4;          // 专区或者圈子链接
	required string game_img_url = 5;      // 卡片配图 或者 游戏的图标 链接
	repeated string tag_list = 6;          // 游戏的标签
	optional string game_desc = 7;         // 游戏描述
	optional string package_name = 8;      // 游戏包名
}

// 活动卡
message OperationActRecommendCard
{
	required uint64 card_id = 1;
	required uint32 game_id = 2;          // 游戏ID
	required GameActivity game_activity_info = 3;
}

enum RecommendCardType {

	CARD_TYPE_SIMPLE_GAME = 10;      // 简单游戏卡(2.9.2版本取消这种卡片)
	CARD_TYPE_OPERATION_ACTIVE = 12; // 运营活动卡
	CARD_TYPE_TAG_LIST = 14;         // 标签榜单卡
	CARD_TYPE_FIXED_LIST = 16;       // 固定榜单卡
	CARD_TYPE_COMPLEX_GAME = 18;     // 复合游戏卡(2.9.2版本取消这种卡片)

	CARD_TYPE_SPECIAL_INTERNAL = 254; // 内部使用的特殊卡标记
	CARD_TYPE_MAX_INVALID = 255;      // 无效值
}

message RecommendCard
{
	required uint32 card_type = 1; // see RecommendCardType
	optional bytes  card_detail = 2; 
}

message GetGameRecommendCardListReq 
{
    enum ExGameIDType {
        EX_GAMEID_TT = 1;         // TT游戏ID
        EX_GAMEID_LIANYUN = 2;    // 联运游戏ID
    }
    required BaseReq base_req = 1;
	
	required uint32 start_idx = 2; // 从0开始
	required uint32 count_limit = 3;
	
	optional uint32 external_game_id = 4;   // 请求中携带的游戏ID 如果有效 且start_idx==0 那么一定会出现在推荐列表中 
	optional uint32 external_game_type = 5; // external_game_id的类型 see ExGameIDType
}

// 游戏公告
message GameGetAnnDetailReq {
    required BaseReq base_req = 1;
	required uint32 start = 2;
	required uint32 limit = 3;
	optional uint32 platform = 4;
}

message GameAnnDetail {
	required string title = 1;
	required string url = 2;
	required uint32 time_subscribe = 3;
}

message GameGetAnnDetailResp {
    required BaseResp base_resp = 1;
    repeated GameAnnDetail ann_list  = 2;
}

message GetGameRecommendCardListResp
{
    required BaseResp base_resp  = 1;
	repeated RecommendCard card_list = 2;
}

message GetBothLikeGameReq {
	required BaseReq  base_req = 1;
	required string fri_account = 2;
}

message GetBothLikeGameResp {
	required BaseResp base_resp  = 1;
	repeated string game_list = 2;
}

//3.2发现页游戏
message GetDiscoveryGameReq
{
    required BaseReq base_req = 1;
}

message GetDiscoveryGameResp
{
    required BaseResp base_resp        = 1;
    repeated GameDetailInfo game_list  = 2;
}


//新版发现与游戏中心 2017/12/18

//HotGame满街都是，POPgame也能翻译成热游的
message PopGameDetail {
	required uint32 game_id         = 1;    //游戏ID
    required string game_name       = 2;    //游戏名
    required string area_url        = 3;    //游戏专区或游戏圈跳转地址
    required string icon_url        = 4;    //图标地址
    required string download_url    = 5;    //下载链接
	repeated bytes fri_icon_url_list = 6;  //好友头像链接集
	repeated string fri_nickname_list = 7;  //好友昵称集
	required uint32 playing_fri_count = 8;	//正在玩的好友数
	required string game_size		= 9;             //大小
	required string game_pkt_name    = 10;    //游戏包名
	repeated string fri_account_list = 11; 
	optional string summary        = 12;
}

//热玩
message GetPopGameReq {
	required BaseReq base_req = 1;
}

message GetPopGameResp {
	required BaseResp base_resp        = 1;
	repeated PopGameDetail channel_game_list = 2;
	repeated PopGameDetail fri_game_list = 3;
	optional string diaplay_icon = 4;
}



//游戏分类标签
message GameTagInfo {
	required uint32 tag_id = 1;
	required string tag_name = 2;
}

message GetGameTagReq {
	required BaseReq base_req = 1;
}

message GetGameTagResp {
	required BaseResp base_resp          = 1;
	repeated GameTagInfo game_tag_list = 2;
}

//游戏分类查询

message GetGameByTagReq {
	required BaseReq base_req = 1;
	required uint32 tag_id = 2;
}

message GetGameByTagResp {
	required BaseResp base_resp          = 1;
	required uint32 tag_id  = 2;
	repeated GameDetailInfo tag_game_list  = 3;

}


//发现页内容展现

message DiscoverPageContentDetail {
	required string title = 1;
	required string sub_title = 2;
	required string jump_url = 3;
}

message GetDiscoverPageContentReq {
	required BaseReq base_req = 1;
}

message GetDiscoverPageContentResp {
	required BaseResp base_resp	= 1;
	repeated DiscoverPageContentDetail discover_content_list = 2;
}




//游戏中心TAB页数据
message GameAreaAdv {
	required string desc = 1;
	required string icon = 2;
	required string url = 3;
}

message GetGameCenterTabListReq {
	required BaseReq base_req = 1;
}

message GetGameCenterTabListResp {
    required BaseResp base_resp             = 1;
	required GameAreaAdv game_area_adv		= 2;
    repeated PreorderGame preorder_game_list = 3;//预约游戏    
    optional string seach_default_name = 4;    //搜索栏默认字符串
}