syntax = "proto3";

package ga;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/user-black-list-logic";

message GetUserBlackListReq {
     BaseReq base_req = 1;
     uint32 active_uid = 2;  //拉黑方uid
     uint32 page = 3;        //页码，从1开始
     uint32 count = 4;       //该页个数
}

message GetUserBlackListResp {
    BaseResp base_resp = 1;
    uint32 total = 2;         //黑名单内总数
    repeated GenericMember account_list  = 3;  //被拉黑方账户信息列表
}

message AddUserBlackListReq {
     BaseReq base_req = 1;
     uint32 active_uid = 2;   //拉黑方uid
     uint32 passive_uid = 3;  //被拉黑方uid
}

message AddUserBlackListResp {
     BaseResp base_resp = 1;
     GenericMember account_info = 2;  //被拉黑方用户信息
}

message DelUserBlackListReq {
     BaseReq base_req = 1;
     uint32 active_uid = 2;   //拉黑方uid
     uint32 passive_uid = 3;  //被拉黑方uid
}

message DelUserBlackListResp {
     BaseResp base_resp = 1;
     GenericMember account_info = 2;  //被拉黑方用户信息
}

message CheckIsInBlackListReq {
     BaseReq base_req = 1;
     uint32 active_uid = 2;   //拉黑方uid
     uint32 passive_uid = 3;  //被拉黑方uid
}

message CheckIsInBlackListResp {
     BaseResp base_resp = 1;
     bool bIsIn = 2;               //是否被拉黑
}