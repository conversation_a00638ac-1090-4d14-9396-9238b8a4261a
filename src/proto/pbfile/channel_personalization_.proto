syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel";

//-------------房间 进房 效果 相关的协议 ---------------------//

// 获取用户的进房特效列表
message GetUserChannelDecorationListReq
{
	required BaseReq base_req = 1;
	required uint32 type = 2;	// UserDecorationType, see ga_base.proto
}

message GetUserChannelDecorationListResp
{
	required BaseResp base_resp = 1;
	repeated UserDecorationInfo decoration_list = 2;
}

// 激活/取消用户的进房特效
message ActivateUserChannelDecorationReq
{
	required BaseReq base_req = 1;
	required uint32 type = 2;	// UserDecorationType, see ga_base.proto
	required string decoration_id = 3;	// 取消坐骑时，用空字符串
}

message ActivateUserChannelDecorationResp
{
	required BaseResp base_resp = 1;
}