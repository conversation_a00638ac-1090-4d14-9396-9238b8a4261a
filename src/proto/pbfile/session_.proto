syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/session";

//// ------ 实时语音Req/Resp BEGIN ------ ////

/** 在群聊中创建一个实时语音会话 **/
message SessionCreateInGroupReq {
	required BaseReq base_req 		= 1;
	required uint32 group_id 		= 2;
	optional bool quit_if_exist		= 3;	// 如果当前已经在房间里，则强制退出房间
}

message SessionCreateInGroupResp {
	required BaseResp base_resp 	= 1;
	required uint32 group_id 		= 2;		// context
	required uint32 session_id 		= 3;		// 创建成功, 返回sessionId
}

/** 创建1V1实时语音 **/
message SessionCreateIn1v1Req {
	required BaseReq base_req = 1;
	required string target_account 	= 2;
	optional bool quit_if_exist		= 3;	// 如果当前已经在房间里，则强制退出房间
}

message SessionCreateIn1v1Resp {
	required BaseResp base_resp = 1;
	required string target_account = 2;
	required uint32 session_id = 3;
}

/** 加入实时语音前发送此协议 **/
message SessionJoinReq {
	required BaseReq base_req		= 1;
	required uint32 session_id 		= 2;
	optional bool quit_if_exist		= 3;	// 如果当前已经在房间里，则强制退出房间
	required string context_account = 4;	// 客户端上下文用的帐号
}

message SessionJoinResp {
	required BaseResp base_resp 	= 1;
	required uint32 session_id 		= 2;		// context
	optional uint32 group_id		= 3;		// 群聊:群id
	optional string ref_account		= 4;		// 1V1:对方的account
	required uint32 session_type	= 5;		// 房间类型
	required string context_account = 6;	// 客户端上下文用的帐号
}

/** 退出实时语音后发送此协议 **/
message SessionQuitReq {
	required BaseReq base_req		= 1;
	required uint32 session_id		= 2;
	required string context_account = 3;
}

message SessionQuitResp {
	required BaseResp base_resp		= 1;
	required uint32 session_id		= 2;		// context
	required string context_account = 3;
	required bool is_session_destroy= 4;		// 房间是否被销毁
}

/** 在房间变化时, 以4/N的概率发送此协议 **/
message SessionReportUpdateReq {
	required BaseReq base_req		= 1;
	repeated string join_account_list	= 2;
	repeated string quit_account_list	= 3;
	required uint32 session_id = 4;
}

message SessionReportUpdateResp {
	required BaseResp base_resp		= 1;
}

/** 在实时语音会话中的人, 以一定频率发送KeepAlive心跳包 **/
message SessionKeepAlive {
	required BaseReq base_req 		= 1;
	required uint32	session_id		= 2;
}

//------------------------------------
// 开黑召集令
//------------------------------------
message SessionCallUpReq {
	required BaseReq base_req = 1;
	required string target_account = 2;
}

message SessionCallUpResp {
	required BaseResp base_resp = 1;
	required string target_account = 2;
}
















//// ------ 实时语音Req/Resp  END  ------ ////

