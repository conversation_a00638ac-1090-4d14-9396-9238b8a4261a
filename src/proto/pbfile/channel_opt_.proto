syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel";

//-------------房间的可选消息---------------------//

enum RICH_CHARM_LEVEL_UPDATE_TYPE
{
	NO_LEVEL_UPDATE = 0;
	RICH_LEVEL_UPDATE = 1;   // 财富值等级变化
	CHARM_LEVEL_UPDATE = 2;  // 魅力值等级变化
}

message RichUpgradeAward
{
	required string award_name = 1;    // 财富等级升级的奖励列表
}


// 仅用于 CHANNEL_MEMBER_OPT_INFO_CHANGED 消息
message ChannelMemberOpt
{
    optional uint32 new_rich_level         = 1;          // 用户的财富等级
	optional uint32 new_charm_level        = 2;          // 用户的魅力等级
	optional uint32 rich_charm_level_update = 3;         // 用户的财富魅力值本次是否有触发升级 定义 RICH_CHARM_LEVEL_UPDATE_TYPE
	repeated RichUpgradeAward rich_upgrade_award_list = 4;    // 财富等级升级的奖励列表
}

// 进房间的可选数据字段
// 仅用于 CHANNEL_ENTER_MSG 消息
message ChannelEnterOpt
{
    optional uint32 new_rich_level          = 1;    // 用户的财富等级
	optional uint32 new_charm_level         = 2;    // 用户的魅力等级
    optional bool is_newbie                 = 3;    // 是否是新人
	optional uint32 member_cnt              = 4;    // 房间当前成员数量
	optional RichAndCharmTopRank rich_charm_toprank = 5; // 用户的财富魅力榜 的排名

	optional GenericMember follow_member  = 6;   // 如果是跟随进房 这里填写跟随的用户信息
	optional string follow_info_msg   = 7;     	 // 如果是跟随进房 这里填写跟随的描述语句 比如 "从附近的人跟随"

	// 在房间类型为 GUILD_HOME_CHANNEL_TYPE 的 公会主房间里面 才有(8，9, 10, 11)字段
	optional string guild_official_name        = 8;   // 在公会内的职位名字(比如会长/副会长...)
	optional bool is_guild_channel_permission  = 9;   // 是否有公会的房间管理权限
	optional string guild_title_name           = 10;  // 在公会内的角色的头衔(比如公会之花...)
	optional bool is_guild_member              = 11;  // 是否是房间对应的公会的成员

	optional bool is_robot                     = 12;  // 是否是机器人进房
	
	// 等级
	optional ChannelMemberVipLevel vip_level = 13;    	// 房间内VIP等级 定义在ga_base 中
	optional ChannelEnterSpecialEffect special_effect = 14;	// 进房特效
	
	optional bool is_fresh_register_user              = 15;    // 是否是新注册用户
}

message ChannelEnterSpecialEffect {
	required string resource_id = 1;
	required string version = 2;
}

// 仅用于 CHANNEL_EXIT_MSG 消息
message ChannelExitOpt
{
	optional uint32 member_cnt              = 1;    // 房间当前成员数量
}

enum ECHANNEL_MSG_SPC_EFFECT_TYPE
{
	ENUM_CHANNEL_MSG_SPC_EFFECT_NONE = 0;  // 没有特效
	ENUM_CHANNEL_MSG_SPC_EFFECT_FIREWORKS = 1;  // 烟花
}

message ChannelMsgSpcEffectResource
{
	optional uint32 resource_id = 1;
	optional string resource_url  = 2;     // 消息特效 的资源包 中
	optional string resource_url_ios = 3;  // 消息特效 的资源包 中
}

// 房间IM消息的可选数据字段
// 仅用于 CHANNEL_TEXT_MSG / CHANNEL_IMAGE_MSG 消息
message ChannelImOpt
{
	//
    optional uint32 new_rich_level          = 1;    // 用户的财富等级
	optional uint32 new_charm_level         = 2;    // 用户的魅力等级
    optional bool is_newbie                 = 3;    // 是否是新人

	// 在房间类型为GUILD_HOME_CHANNEL_TYPE的房间里面 才有(3,4,5)字段 同时其他字段(1,2)不填写
	optional string guild_official_name        = 4;  // 在公会内的角色名字(比如会长/副会长...)
	optional bool is_guild_channel_permission  = 5;  // 是否有公会的房间管理权限
	
	// 等级
	optional ChannelMemberVipLevel vip_level = 6;     // 房间内VIP等级 定义在ga_base 中
	
	// 消息特效
	optional uint32 msg_special_effect_type = 7;     // 消息特效类型 定义在 ECHANNEL_MSG_SPC_EFFECT_TYPE 中
	optional ChannelMsgSpcEffectResource msg_special_effect_resource = 8; // 特效资源包
}

message SimpleMicrSpace
{
	required uint32 mic_id = 1;              // 麦位ID 1 - 9
	optional uint32 mic_state = 2;           // EMicrSpaceState 麦位状态 1 正常 2 禁用 3 不可发言
	optional uint32 uid = 3;
}

// 麦位操作 消息 可选数据字段
// 仅用于 麦位相关的消息
// EN_MIC_QUEUE 上麦消息 DE_MIC_QUEUE 下麦消息 CHANNEL_TAKE_USER_HOLD_MIC 被抱上麦 CHANNEL_KICKED_MIC 踢下麦 CHANNEL_MIC_ENTRY_ENABLE 解锁一个麦位 CHANNEL_MIC_ENTRY_DISABLE 锁定一个麦位
message ChannelMicOpt
{
	repeated SimpleMicrSpace all_mic_list = 1; // 当前的全量麦位列表
	optional uint32 op_mic_uid = 2;        // 涉及本次麦位操作的 用户的UID (比如上麦者，下麦者，被踢者，解锁麦位操作者，锁定麦位操作者)
	optional string op_mic_facemd5 = 3;    // 涉及本次麦位操作的 用户的头像md5 (仅在上麦时填写)
	optional uint32 op_micid = 4;          // 涉及本次麦位操作的 麦位ID
	optional uint64 op_time_ms = 5;        // 本次操作的服务器时间
	optional uint32 curr_micmode = 6;      // 当前房间的麦模式

	optional string op_mic_headware_key = 7;    // 涉及本次麦位操作的 用户的头像装饰框 key (仅在EN_MIC_QUEUE上麦 、 CHANNEL_TAKE_USER_HOLD_MIC被抱上麦 类型的消息时填写)
	optional int32 op_mic_sex = 8;    	 		// 涉及本次麦位操作的 用户的性别(仅在EN_MIC_QUEUE上麦 、 CHANNEL_TAKE_USER_HOLD_MIC被抱上麦 类型的消息时填写)

	// 在房间类型为 GUILD_HOME_CHANNEL_TYPE 的 公会主房间里面 且仅在EN_MIC_QUEUE上麦  才有(9，10)字段
	optional string guild_official_name        = 9;   // 在公会内的角色名字(比如会长/副会长...)
	optional bool is_guild_channel_permission  = 10;  // 是否有公会的房间管理权限

	//官方认证信息
    optional string certify_title = 11;          //官方认证
    optional string certify_intro = 12;          //认证介绍
	
	// 等级
	optional ChannelMemberVipLevel vip_level = 13;     // 房间内VIP等级 定义在ga_base 中 (仅在EN_MIC_QUEUE上麦 、 CHANNEL_TAKE_USER_HOLD_MIC被抱上麦 类型的消息时填写)
}

// 移动麦位操作 消息 可选数据字段
// 仅用于 移动麦位的消息
// CHANNEL_CHANGE_MIC_POS 移动麦位消息
message ChannelChangeMicPosOpt
{

	optional uint32 op_uid = 1;        // 移动麦位的用户的UID
	optional uint32 from_mic_id = 2;       // 从哪个麦位ID进行移动
	optional uint32 target_mic_id = 3;     // 移动的目标麦位ID
	optional uint32 target_mic_state = 4;  // 目标麦位的状态
	optional uint64 op_time_ms = 5;        // 本次操作的服务器时间
}

// 房间配置变更消息操作 消息 可选数据字段
// 仅用于 房间配置变更消息
// CHANNEL_CONFIG_MODIFY_MSG 房间配置变更消息
message ChannelConfModifyOpt
{
	optional uint32 sub_mofdiy_type = 1;   // ChannelMsgSubType 房间名称 密码 麦模式 房间图标 房间描述 房间各种开关 等配置
	optional uint64 op_time_ms = 2;        // 本次操作的服务器时间
	optional bytes sub_pb_info = 3;
}

message ChannelConfModifyOpt_Switch
{
	optional bool is_open = 1;
}
message ChannelConfModifyOpt_Name 
{
	optional string new_channelname = 1;
}
message ChannelConfModifyOpt_Pwd
{
	optional bool is_have_pwd = 1;
	optional string pwd_desc = 2;     // 修改密码描述文本
}
message ChannelConfModifyOpt_MicMode
{
	optional uint32 curr_micmode = 1;
	optional string micmode_desc = 2;          // 切模式描述文本
	repeated SimpleMicrSpace all_mic_list = 3; // 当前的全量麦位列表
}

// 头饰变化 消息 可选数据字段
// 仅用于 麦位头饰的消息 
// CHANNEL_USER_HEADWEAR_CHANGED 用户头饰变化 仅在用户佩戴或者取消时 用于广播给房间内的其他用户
message ChannelUserHeadwearOpt
{
	optional uint32 uid = 1;           // 涉及本次头像装饰的用户UID
	optional string headware_key = 2;  // 头像装饰的KEY 有值表示用户佩戴，没有值表示用户取消
}

//答题服务 消息
//倒计时消息
message ChannelTriviaGameTimerOpt{
	optional uint32 end_timestamp = 1;
}

//播放动画消息
message ChannelTriviaGameAnimationOpt{
    optional uint32 anima_id = 1;    //动画id
    optional string text = 2;        //动画上显示的字符,如果有的话
}

//题目消息
message ChannelTriviaGameOption{
	optional string option   = 1;    //选项内容
	optional uint32 option_num = 2;  //每个选项人数,公布答案时才有值
}

message ChannelTriviaGameQuestionOpt{
    optional uint32 question_id   = 1;	 //题目ID
    optional uint32 question_idx  = 2;	 //题目序号，第x题
    optional string question = 3;    //问题
    repeated ChannelTriviaGameOption option   = 4;    //选项
    optional uint32 solution = 5;    //答案, 0表示提问状态,非0表示公布答案,1表示第1个选项...
}

//获奖名单
message ChannelTriviaGameWinner{
	optional uint32 uid = 1;
	optional string account = 2;
	optional string nick_name = 3;
	optional uint32 reward = 4; //获得的奖金,单位：分
}

message ChannelTriviaGameWinnerListOpt
{
	repeated ChannelTriviaGameWinner user_list = 1;	//只显示100人
    optional uint32 total_num = 2; //获奖总人数
}

//阶段变化
message ChannelTriviaGamePhaseUpdateOpt{
	optional uint32 act_id   = 1;    //活动id
	optional uint32 phase_idx = 2;	 //阶段序号
	optional bool 	is_show_answer = 3;	 //是否展示答案
}



//
message ChannelLiveConnectMicApplyOpt
{
	optional bool is_cancel = 1;	  // true 表示是取消申请 false 表示是申请
	optional uint32 ramin_apply_cnt = 2;  // 剩余的申请人数
	optional uint32 sex = 3;	      // is_cancel = false 时填写 申请者的性别
	optional uint64 op_time_ms = 4;   // 本次操作的服务器时间
}
message ChannelLiveConnectMicHandleOpt
{
	optional bool is_allow = 1;	  //ture 表示是通过申请 false表示是拒绝申请
	repeated uint32 uid_list = 2; 		// 被操作的UID列表
	optional uint32 ramin_apply_cnt = 3;// 剩余的申请人数
}
message ChannelLiveConnectMicApplyExpireOpt
{
	optional uint32 remain_apply_cnt = 1;  // 剩余的申请人数
	repeated uint32 expire_uid_list = 2;  // 本次超时的用户列表
}

// 排麦变化事件
message ChannelQueueUpMicOpt
{
	enum EQueueUpMicOpType
	{
		ENUM_QUEUE_UP_MIC_APPLY = 1;        // 用户主动申请
		ENUM_QUEUE_UP_MIC_APPLY_CANCEL = 2; // 用户主动取消
		ENUM_QUEUE_UP_MIC_APPLY_KICK = 3;   // 管理员踢除
		ENUM_QUEUE_UP_MIC_APPLY_PASS = 4;   // 管理员同意
	};
	optional uint32 op_uid = 1;	      
	optional uint32 target_uid = 2;	      
	optional uint32 ramin_apply_cnt = 3;  // 剩余的申请人数
	optional uint64 op_time_ms = 4;       // 本次操作的服务器时间
	optional uint32 op_type = 5;	      // EQueueUpMicOpType  
	
	optional uint32 apply_user_sex = 6;    // 申请者的性别
	optional string apply_user_nick    = 7;// 申请者的nickname
	optional string apply_user_account = 8;// 申请者的username
	optional string apply_user_facemd5 = 9;// 仅在用户主动发起申请时 这里才有值。表示用户头像的 MD5
}

// 公会公告
message ChannelGuildBulletinOpt
{
	optional uint32 guild_id = 1;	      //
	optional string title_msg = 2;	  //
	optional uint32 topic_id = 3;	  //
	optional string content_msg = 4;	 //
}


// 房间成员VIP等级
message ChannelMemberVipNotifyOpt
{
	optional ChannelMemberVipLevel vip_level = 1; // 定义在ga_base 中
	optional string msg = 2;                      // notify信息
	optional string member_face_md5 = 3;          
}


//----------------------channelPresentCount push def begin-----------------

// 给麦上用户送礼时推送 以及 用户上麦时
// type:CHANNEL_PRESNET_COUNT_CHANGE
message ChannelPresentCountNotifyMsg
{
    required uint32 uid = 1;
    required uint32 channel_id = 2;
    required uint32 price = 3;
}

// 房间送礼统计开关改变时推送
// type:CHANNEL_PRESENT_COUNT_OFF
message ChannelPresentCountStatusMsg
{
    required uint32 channel_id = 1;
    required bool state = 2;//false ,关闭统计；true 开启统计
}

//----------------------channelPresentCount push def end-----------------

message ChannelPopUpMsgOpt
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required string content_msg = 3;
}

message CreateTopicChannelMsg {
	required uint32 channel_id = 1;
	required string admin_msg = 2;	//成功在大厅创建房间后，需要在公屏处，该公屏信息仅房主、房管可见
	required string player_msg = 3;	//成功在大厅创建房间后，房间的其他人(非房主、房管)可以看到人的公屏
	required uint32 user_id = 4;	//创建者id
	required uint32 tab_id = 5;		//主题房的tab id
	required string tab_name = 6;	//主题房的tab 名
}

message HideTopicChannelMsg {
	required string content_msg = 1;	//不在大厅显示房间时推送，显示在公屏处， 房主可见
}



message ChannelDressChangeNofityOpt
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required uint32 dress_type = 3;
	required uint32 dress_id = 3;
}

