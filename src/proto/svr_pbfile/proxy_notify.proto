syntax = "proto2";

// 推送信息的通知

package notify;

import "usergroup_relation_register.proto";
option go_package = "golang.52tt.com/protocol/services/proxy_notify;proxy_notify";

message PushCmd {
  enum Values {
    PUSH_CMD_NOTIFY = 1;  /**< 通知 */
    PUSH_CMD_KICKOUT = 2; /**< 通知 */
    PUSH_CMD_PUSH = 3;
    PUSH_CMD_BATCHPUSH = 4;
    PUSH_CMD_REGEVENT = 5;
    PUSH_CMD_BATCH_REGEVENT = 6;
    PUSH_CMD_PUSH_BYGROUPID = 7;
    PUSH_CMD_NOTIFY_BYGROUPID = 8;
    PUSH_CMD_PUSH_BYCHANNELID = 9;
    PUSH_CMD_NOTIFY_WITH_DATA = 10;
    PUSH_CMD_NOTIFY_WITH_DATA_BY_GROUPID = 11;
    PUSH_CMD_PUSH_WITH_USER_LIST = 12;
    PUSH_CMD_PUSH_WITH_MULTICAST_ACCOUNT = 13;
    PUSH_CMD_PUSH_V2_CH_CAST_MSG = 14; // push-v2-agent 消息
    PUSH_CMD_PUSH_V2_UN_CAST_MSG = 15; // push-v2-agent 消息
    PUSH_CMD_PUSH_V2_BC_CAST_MSG = 16; // push-v2-agent 消息
  }
}

// 通知用户
message NotifyUser {
  required uint32 clientid = 1;
  required uint32 uid = 2;
}

message MulticastAccount {
  // 保持与定义一致
  enum AccountType {
    ACCOUNT_TYPE_USER = 0;            // 用户
    ACCOUNT_TYPE_GUILD = 1;           // 工会
    ACCOUNT_TYPE_GROUP = 2;           // 临时群
    ACCOUNT_TYPE_GAME = 3;            // 游戏
    ACCOUNT_TYPE_GUILD_GROUP = 4;     // 工会群
    ACCOUNT_TYPE_GAME_GROUP = 5;      // 游戏群
    ACCOUNT_TYPE_PUBLIC_ACCOUNT = 6;  // 公众号
    ACCOUNT_TYPE_TGROUP = 7;          // 群组
    ACCOUNT_TYPE_CHANNEL = 8;         // 频道
  }

  required uint64 id = 1;
  required uint32 account_type = 2;
  required string account = 3;
}

// 业务无关的推送, 所有业务相关数据放置在PushBizData中
message PushBizData {
  required uint32 push_cmd = 1;  // 推送命令号: CMD_NOTIFY/CMD_KICKOUT/CMD_PUSH/CMD_TransmissionPush
  required bytes data = 2;       // 推送的消息体, proxy不作解析, 直接发给客户端
}

// 根据用户列表推送, 由调用方去查询在线信息并填写进notify_user_list
message PushWithUserList {
  repeated uint32 target_terminal_list = 1;
  repeated NotifyUser notify_user_list = 2;
  required PushBizData push_biz_data = 3;
  optional uint32 sequence = 4;

  optional uint64 server_received_at = 15;
  optional string request_id = 16;
  optional uint64 dye_id = 17;
  optional uint32 push_type = 18;
  optional string task_id = 19;
}

// 根据多播订阅关系进行推送
message PushWithMulticastAccount {
  repeated uint32 target_terminal_list = 1;
  required MulticastAccount multicast_account = 2;
  repeated uint32 skip_uids = 3;
  required PushBizData push_biz_data = 4;
  optional uint32 sequence = 5;
  repeated MulticastAccount multicast_accounts = 6;  // 批量推送支持, 如果设置了, 则忽略`multicast_account = 2`

  optional uint64 server_received_at = 15;
  optional string request_id = 16;
  optional uint64 dye_id = 17;
  optional uint32 push_type = 18;
  optional string task_id = 19;
}

// 批量通知用户
message BatchNotifyInfo {
  required uint32 notify_type = 1;
  required bytes info = 2;
  repeated NotifyUser usrlists = 3;
}

// 带内容的批量通知
message NotifyWithDataInfo {
  required uint32 notify_type = 1;
  required bytes notify_extend_multi = 2;  // sync.proto : NotifyExtendMulti
  repeated NotifyUser usrlists = 3;
}

message NotifyWithDataByGroupId {
  required uint32 group_id = 1;
  required uint32 notify_type = 2;
  required bytes notify_extend_multi = 3;  // sync.proto : NotifyExtendMulti
  repeated uint32 skip_uids = 4;  // 不需要推送的uid列表。比如屏蔽了群消息的用户
}

// 这个协议和transmission.proto中保持一致
message TransmissionPacket {
  required uint32 app_id = 1;  // for client dispatching
  required bytes payload = 2;
}

message Empty {
  // nothing
}

message NotifyReq {
  required uint32 uid = 1;
  required uint32 client_id = 2;
  required uint32 notify_type = 3;
}

message NotifyPayload {
  required bytes payload = 1;
}

service notify {
  rpc Notify(NotifyReq) returns (Empty) {}
  rpc Kick(NotifyPayload) returns (Empty) {}
  rpc Push(NotifyPayload) returns (Empty) {}
  rpc BatchPush(BatchNotifyInfo) returns (Empty) {}
  rpc RegEvent(UserGroupRelationReg.UserGroupRelationRegistEvent) returns (Empty) {}
  rpc BatchRegEvent(UserGroupRelationReg.BatchUserGroupRelationRegistEvent) returns (Empty) {}
  rpc PushByGroupId(NotifyPayload) returns (Empty) {}
  rpc NotifyByGroupId(NotifyPayload) returns (Empty) {}
  rpc PushByChannelId(NotifyPayload) returns (Empty) {}
  rpc NotifyWithData(NotifyWithDataInfo) returns (Empty) {}
  rpc NotifyWithDataByGroup(NotifyWithDataByGroupId) returns (Empty) {}
  rpc PushWithUserLst(PushWithUserList) returns (Empty) {}
  rpc PushWithMultiCastAccount(PushWithMulticastAccount) returns (Empty) {}
}
