syntax = "proto2";

// 用户与群组关系的注册订阅

// namespace
package UserGroupRelationReg;

option go_package = "golang.52tt.com/protocol/services/proxy_notify/user_group_relation;user_group_relation";

enum E_GROUP_RELATION_REGENENT_TYPE {
  // 群组订阅关系
  ENUM_GROUP_RELATION_REGENENT_REGIST = 1;      // 注册关系（用户上线 or 用户加新群）
  ENUM_GROUP_RELATION_REGENENT_UNREGIST = 2;    // 反注册关系(用户退群)
  ENUM_GROUP_RELATION_REGENENT_USERLOGOUT = 3;  // 用户下线
  ENUM_GROUP_RELATION_REGENENT_USERQUICKRELOGIN = 4;  // 用户相同前端断线后快速重连
  ENUM_GROUP_RELATION_REGENENT_PROXYDOWN = 5;  // Proxy停止(清理该Proxy上全部订阅关系记录)

  // 频道订阅关系
  ENUM_CHANNEL_RELATION_REGENENT_REGIST = 10;  // 频道注册关系（用户进频道）
  ENUM_CHANNEL_RELATION_REGENENT_UNREGIST = 11;  // 频道反注册关系（用户退出频道）
}

// 由PresenceAsyncPub进程 向接入组件Proxy的Notify模块发送的用户群组注册事件
message UserGroupRelationRegistEvent {
  required uint32 regEventType = 1;  // E_GROUP_RELATION_REGENENT_TYPE 订阅事件的类型：注册用户与群组的关系 反注册用户与群组的关系 用户下线 Proxy重启
  required uint32 uid = 2;
  required uint32 clientID = 3;
  repeated uint32 groupID_list = 4;
  optional uint32 terminal_type = 5;
  optional uint64 event_seq = 6;  // 事件的序号 递增
  optional uint32 delay_ms = 7;   // 延迟处理, 毫秒

  optional string request_id = 16;
  optional uint64 dye_id = 17;
}

message BatchUserGroupRelationRegistEvent {
  repeated UserGroupRelationRegistEvent event_list = 1;

  optional string request_id = 16;
  optional uint64 dye_id = 17;
}
