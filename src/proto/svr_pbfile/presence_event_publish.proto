syntax = "proto2";

// 在线事件的发布
package PresAsyncPub;

option go_package = "golang.52tt.com/protocol/services/presencesvr/async;pres_async_pub";

// presence服务向异步队列发布的在线事件类型
enum E_PRES_ASYNCENENT_TYPE {
  ENUM_PRES_ASYNCENENT_USERLOGIN = 1;         // 用户登录
  ENUM_PRES_ASYNCENENT_USERLOGOUT = 2;        // 用户下线
  ENUM_PRES_ASYNCENENT_USERQUICKRELOGIN = 3;  // 用户相同前端 快速重登录
  ENUM_PRES_ASYNCENENT_PROXYDOWN = 4;         // Proxy停止
  ENUM_PRES_ASYNCENENT_PRESUPDATE = 5;        // 所有连接上下线
}

// 由Presence进程向 异步队列发布的在线事件
message PresEvent {
  required uint32 presEventType =
      1;  // PRES_ASYNC_ENENT_TYPE 用户上线 用户下线 Proxy重启
  required uint32 proxy_ip = 2;
  required uint32 proxy_port = 3;
  optional uint32 uid = 4;
  optional uint32 clientID = 5;
  optional uint32 timestamp = 6;  // 在线事件发生的时间
  optional uint32 terminal_type = 7;

  optional uint32 client_ip = 8;
  optional bytes device_id = 9;
  optional uint64 online_at = 10;  // ms
  optional uint64 offline_at = 11; // ms
  optional bool last_one= 12; //last one login
}
