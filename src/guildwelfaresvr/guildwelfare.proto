syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package GuildWelfare;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

enum WELFARE_TYPE {
    EACH_USER = 0;          // 公会新增用户
    SOONEST_JOIN = 1;		// 公会新增人数最快
    TOP_JOIN = 2;			// 公会新增人数最多
}

enum DAILY_SETTLEMENT_LIMIT {
    AWARD_GUILDS_LIMIT = 5;          // 奖励公会数量
}

/**
 *	Request & Response
 */
// 用户首次加入公会
message AddGuildFirstJoinReq {
    required uint32 guild_id = 1;
    required uint32 uid = 2;
}

message AddGuildFirstJoinResp {
}

message GuildFirstJoinCount {
    required uint32 guild_id = 1;               // 公会ID
    required uint32 member_count = 2;           // 新增成员数量
}

//获取指定日期新增人数最高的公会列表
message GetTopJoinGuildListReq {
    required uint32 timestamp = 1;
    required uint32 limit = 2;
}

message GetTopJoinGuildListResp {
    repeated GuildFirstJoinCount guild_list = 1;
}

//获取指定日期最快达到新增30人的公会列表
message GetSoonestJoinGuildListReq {
    required uint32 timestamp = 1;
    required uint32 limit = 2;
}

message GetSoonestJoinGuildListResp {
    repeated uint32 guild_list = 1;
}

//附加福利奖励
message AwardFirstJoinGuildReq {
    required uint32 timestamp = 1;
    required uint32 guild_id = 2;
    required uint32 rank = 3;
    required uint32 type = 4;	//WELFARE_TYPE
}

message AwardFirstJoinGuildResp {
}

message FirstJoinActivityInfo {
    enum Type {
        ALL_GUILD = 0;	// 所有公会可参加
        NEW_GUILD = 1;	// 新公会可参加,
    }

    required uint32 activity_id = 1;
    required string name = 2;
    required uint64 begin_time = 3;
    required uint64 end_time = 4;
    required uint32 type = 5;	//TODO，未实现
}

//获取公会拉新增活动信息
message GetFirstJoinActivityInfoReq {
    required uint32 timestamp = 1;
}


//////////////////
service GuildWelfare {
    option( tlvpickle.Magic ) = 15320;		// 服务监听端口号

    rpc AddGuildFirstJoin(AddGuildFirstJoinReq) returns(AddGuildFirstJoinResp) {
        option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "u:g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id>";			// 测试工具的命令号帮助
    }

    rpc GetTopJoinGuildList(GetTopJoinGuildListReq) returns(GetTopJoinGuildListResp) {
        option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "t:n:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <timestamp> -n <limit>";			// 测试工具的命令号帮助
    }

    rpc GetSoonestJoinGuildList(GetSoonestJoinGuildListReq) returns(GetSoonestJoinGuildListResp) {
        option( tlvpickle.CmdID ) = 3;										// 命令号
        option( tlvpickle.OptString ) = "t:n:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <timestamp> -n <limit>";			// 测试工具的命令号帮助
    }

    rpc AwardFirstJoinGuild(AwardFirstJoinGuildReq) returns(AwardFirstJoinGuildResp) {
        option( tlvpickle.CmdID ) = 4;										// 命令号
        option( tlvpickle.OptString ) = "g:t:r:p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -t <timestamp> -r <rank> -p <type>";			// 测试工具的命令号帮助
    }

    rpc GetFirstJoinActivityInfo(GetFirstJoinActivityInfoReq) returns(FirstJoinActivityInfo) {
        option( tlvpickle.CmdID ) = 5;										// 命令号
        option( tlvpickle.OptString ) = "t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <timestamp>";			// 测试工具的命令号帮助
    }

}