syntax="proto3";

import "common/tlvpickle/skbuiltintype.proto";

option go_package="golang.52tt.com/protocol/services/src/userTagSvr";

package usertag;


// 想要找什么样的人 类型的标签
enum EUserTagFindWhoType 
{
	ENUM_FINDWHOTAG_INVALID = 0;          // 无效值
	ENUM_FINDWHOTAG_OPPOSITE_SEX = 1;     // 异性类型
	ENUM_FINDWHOTAG_ASSIGN_SEX = 2;       // 指定性别类型
	ENUM_FINDWHOTAG_CORRELATION_PAIR = 3; // 成对关联类型
	ENUM_FINDWHOTAG_GAME = 4;             // 新的找什么人, 2019/09/17 版本迭代
}


// 用户标签
message UserTagBase{
	uint32 tag_type = 1; // ga::UserTagType 1年龄段标签 2找什么人标签 4游戏标签 5个性标签 6生日日期标签
	string tag_name = 2;
	uint32 tag_id   = 3;
	bytes  tag_info = 4; // 扩展信息 结构体Ext字段，根据tag_type不同有不同类型 比如 tag_type=2时为 UserFindWhoTagExt / tag_type=4时为 UserGameTagExt
	bool is_del = 5;
}

message UserTagList
{
	uint32 uid = 1;
	repeated UserTagBase tag_list = 2;
}

// 年龄段 类型的标签
message UserAgeTagExt
{
	uint32 age_year_min = 1;
	uint32 age_year_max = 2;
}

// 想要找什么样的人 类型的标签
message UserFindWhoTagExt
{
	uint32 findwho_type = 1;            // EUserTagFindWhoType
	uint32 assign_sex = 2;              // 如果  findwho_tag_type == FINDWHO_TYPE_ASSIGN_SEX 这里填指定的性别类型
	uint32 correlation_pair_tag_id = 3; // 如果  findwho_tag_type == USERT AG_TYPE_CORRELATION_PAIR 这里填与之关联的tagID
}

//选项二级属性
message GameOptSecondProp{            
   string opt_prop = 1; 
   repeated string value_list = 2;
}

message UserGameTagOpt
{
	string opt_name = 1;
	uint32 opt_id = 2;
	bool is_support_muti_set = 3;            // 是否支持多选 (该字段废弃，填support_muti_set_cnt就可以了)
	repeated string value_conf_list = 4;     // 游戏属性 系统配置的备选项
	repeated string value_userset_list = 5;  // 游戏属性 用户实际设置项
	
	uint32 support_muti_set_cnt = 6;        // 选项用户可以选择的数量 1就是单选 2就是可以选2个
	uint32 partition_id = 7;                // 选项分区（1=第一部分 2=第二部分）
    repeated GameOptSecondProp prop_value_list = 8;  //二级属性，例如王者荣耀的英雄选项
}

enum EGameScreenStatus{
    EGAMESCREEN_AUDITING = 0 ;             //审核中
    EGAMESCREEN_AUDIT_REJECT = 1 ;         //不通过
    EGAMESCREEN_AUDIT_PASS =2 ;            //通过
}

message GameScreenShot{
    uint32 audit_status = 1;           //图片审核状态，参考EGameScreenStatus
    string img_url = 2;                  //原图
    string img_url_invisible_gamenick = 3 ;  //昵称打码图
    uint32 begin_audit_time = 4 ;            //开始审核时间，超过多久没审核，则在客户端主动拉取游戏卡时，设置为审核失败
    uint32 index = 5 ;                      //游戏截图索引(坑位)，从0开始
    uint32 upload_time = 6;                    //上传时间截
}
message UserGameTagExt
{
	uint32 game_id   = 1;                    // 关联的游戏ID
	string back_img_url = 2;
	repeated UserGameTagOpt opt_list = 3;    // 选项列表
	string thumb_img_url = 4;                // 缩略图 缩略图为圆形图 目前用于好友匹配中展示
	string game_nickname = 5;                // 游戏的昵称 由用户自己填写
    GameScreenShot game_screenshot = 6;      //游戏上分图,用户上传
    repeated GameScreenShot game_screenshot_list = 7 ; //20211125，支持多张游戏截图
}

message LevelImg{
    string level_name = 1;
    string img_url = 2;
}
message ConfGameTagOpt    //配置模板
{
	string opt_name = 1;
	uint32 opt_id = 2;
	bool is_support_muti_set = 3;            // 已废弃，用下面的support_muti_set_cnt = 6
	repeated string value_conf_list = 4;     // 游戏属性 系统配置的备选项
	repeated string value_userset_list = 5;  // 游戏属性 用户实际设置项
	
	uint32 support_muti_set_cnt = 6;        // 选项用户可以选择的数量1,2,3...
	uint32 partition_id = 7;                // 选项分区（1=第一部分 2=第二部分）
    repeated GameOptSecondProp prop_value_list = 8;      ////二级属性，例如王者荣耀的英雄选项
    bool is_must          = 9;       //是否是必填项
    uint32 text_box_style  = 10;      //0斜杠 1带方框
}

enum EGameExtraOpt
{
	ENUM_GAMEEXTRAOPT_INVALID = 0;                 // 无效值
    ENUM_GAMEEXTRAOPT_GAME_NICK = 1;              //是否有游戏昵称
    ENUM_GAMEEXTRAOPT_SCREENSHOT = 2;            //是否有截图
    ENUM_GAMEEXTRAOPT_GAMENICK_TEAM_VISIBLE = 4; //游戏昵称仅小队可见
}

message ConfGameTagExt
{
	uint32 game_id   = 1;                    // 关联的游戏ID
	string back_img_url = 2;                    //旧版的背景图
	repeated ConfGameTagOpt opt_list = 3;    // 选项列表
	string thumb_img_url = 4;                // 缩略图 (旧版本的缩略图，已废弃)
	string game_nickname = 5;                // 游戏的昵称 由用户自己填写
    GameScreenShot game_screenshot = 6;      //游戏上分图,用户上传
    string game_card_img_url = 7 ;           //游戏缩略图
    string game_back_img = 8 ;               //新版的背景图(大尺寸)
    string game_icon_img = 9 ;               //新建游戏卡时的小icon图片
    string game_corner_mark_img = 10;        //游戏卡角标图
    string game_no_level_img_url = 11;       //无段位时的填充图，旧版的类型图也用了该张图
    repeated LevelImg level_img_url = 12 ;     //旧版段位图
    repeated LevelImg mic_level_img_url = 13 ; //麦位-段位图*/
    string game_back_img_mini = 14 ;               //新版的背景图(小尺寸)
    uint32 game_back_color_num = 15 ;             //背景底色值
    uint32 game_extra_opt_switch = 16;                       //是否有游戏昵称，截图等选项等，参考EGameExtraOpt，有则或上去
    repeated LevelImg level_img_url_new =17;          //新版段位图
    string game_no_level_img_url_new = 18;           //新版游戏无端位图
}

message SimpleGameTagList
{
    uint32 uid = 1;
    repeated SimpleGameTagExt ext_list = 2;
}

message SimpleGameTagExt
{
	uint32 game_id   = 1;                    // 关联的游戏ID
    string game_name = 2;
    string game_nickname = 3;               // 用户游戏昵称
    string game_area = 4;                   // 游戏区服
    string game_dan = 5;                    // 游戏段位 
    uint32 uid = 6;
    string game_dan_url_for_mic = 7;            // 麦下展示的游戏段位图
    string game_role = 8;                // 游戏角色
    repeated string game_position = 9;            //游戏位置
    repeated string game_hero_list = 10; //想玩英雄列表
    string game_screenshot = 11;         //游戏上分图(已经审核通过的图片)
    repeated string game_style = 12;     //游戏风格
    string game_level_url = 13 ;         //段位图
    uint32 tag_id = 14 ;
    bool is_completed = 15;               //游戏卡是否完善(指所有必填字段都已填完整)
}

// 个性 类型的标签
message UserOptPersonalTagClassify
{
	uint32 classify_id   = 1;                //  个性标签的分类
	string classify_name = 2;                //  个性标签的分类的名字
	repeated UserTagBase tag_list = 3;
	bool is_del = 4;
}

message UserOptPersonalTagExt
{
	uint32 classify_id   = 1;                //  个性标签的分类
}


// 获取指定用户的全部标签列表
message GetUserTagReq
{
	uint32 target_uid = 1;    // 要获取谁的标签列表，如果是获取自己的 那么填自己的UID
	bool is_need_tag_ext = 2; // 是否需要填充 tag的ext信息字段
}

message GetUserTagResp
{
	UserTagList user_taglist = 1;
}


// 设置用户自己的标签列表
message SetUserTagReq
{
	uint32 target_uid = 1;
	uint32 tag_type = 2;      // ga::UserTagType 指定设置的标签类型（每种类型下是全量覆盖） 如果为0 那么就是全部类型全量覆盖
	repeated UserTagBase tag_list = 3; // 
    uint32 setting_cnt = 4;    // 第几次设置标签，0，默认值。1 第一次设置(只有注册的时候设置才填1)
    bool ignore_game = 5;
}

message SetUserTagResp
{
}

message UserTagReplaceInfo{
   uint32 from_tag_id = 1;
   uint32 from_tag_type = 2;
   uint32 to_tag_id = 3;
   uint32 to_tag_type = 4;     // must 2==4
}


message SetUserOneTagReq
{
  uint32 target_uid = 1;
  uint32 tag_type =2;
  uint32 oper_type = 4;      // see ga::user_taglist ENUM_SET_USERTAG_OPER
  UserTagBase tag_base = 5;
  UserTagReplaceInfo tag_replace = 6; //替换时必须有值
  int32 tag_sort_value = 7;      // 游戏卡片的卡槽编号，标识操作第几个卡片
  uint32 app_id = 8;            // for oss
  uint32 market_id = 9;         // for oss
  bool just_set_gamenick = 10 ;   //审核后只需设置游戏昵称(5.5.0之后的游戏昵称审核)

}
message SetUserOneTagResp
{
}


// 批量获取用户标签
message BatGetUserTagReq
{
  repeated uint32 uid_list = 1;
  bool is_need_tag_ext = 2; // 是否需要填充 tag的ext信息字段
}
message BatGetUserTagResp
{
  repeated UserTagList usertaglist_list = 1;
}

// 获取配置标签列表
message GetTagConfigListReq
{
  uint32 tag_type = 1;      // ga::UserTagType 指定设置的标签类型 如果为0 那么就是全部类型
}

message GetTagConfigListResp
{
  repeated UserTagBase tag_list = 1;
  repeated uint32 game_id_list = 2;   // 注册时就需要 完善二级属性的游戏 id
}


// 创建普通标签配置
message CreateTagConfigReq
{
  uint32 tag_type = 1;      // ga::UserTagType 指定设置的标签类型
  UserTagBase tag = 2;
}

message CreateTagConfigResp
{
  uint32 tag_id = 1;
}


// 修改 
message ModifyTagConfigReq
{
  uint32 tag_id = 1;        
  UserTagBase tag = 2; // tag 信息必须填全 （因为该修改接口实际上是根据tag覆盖旧的数据）
}

message ModifyTagConfigResp
{}

// 删除
message DelTagConfigReq
{
  uint32 tag_id = 1;
  bool is_recover = 2;
}

message DelTagConfigResp
{
}

// 排序
message SortTagConfigReq
{
  uint32 tag_type = 1;                  // ga::UserTagType 指定设置的标签类型
  repeated uint32 tag_id_list = 2;      // 按照这个列表顺序进行排序

  uint32 personaltype_classify_id = 3;  // 如果是对 tag_type=USERTAG_TYPE_OPT_PERSONAL 的可选的个性标签进行排序 需要指定标签的分类
}

message SortTagConfigResp
{	
}

// 创建findwho类型的标签
message CreateFindWhoTypeTagConfigReq
{
  uint32 findwho_type = 1;      // EUserTagFindWhoType 指定findwho类型
  string tag_name = 2;

  uint32 assign_sex = 3;           // 如果  findwho_tag_type == FINDWHO_TYPE_ASSIGN_SEX 这里填指定的性别类型
  string correlation_tag_name = 4; // 如果  findwho_tag_type == USERT AG_TYPE_CORRELATION_PAIR 这里填与之关联的另外一个标签名字
}

message CreateFindWhoTypeTagConfigResp
{
  uint32 findwho_type = 1;       // EUserTagFindWhoType 指定findwho类型
  uint32 tag_id = 2;
  uint32 correlation_tag_id = 3; // 如果  findwho_tag_type == USERT AG_TYPE_CORRELATION_PAIR 这里填与之关联的另外一个标签Id
}

// 创建个性标签的分类
message CreateOptPersonalTagClassifyReq
{
  string classify_name = 1;
}
message CreateOptPersonalTagClassifyResp
{
  uint32 classify_id = 1;
}

// 获取全量分类列表
message GetOptPersonalTagClassifyListReq
{
}
message GetOptPersonalTagClassifyListResp
{
  repeated UserOptPersonalTagClassify classify_list = 1;
}


// 根据分类获取个性标签列表
message GetOptPersonalTagByClassifyReq
{
  uint32 classify_id = 1;
}
message GetOptPersonalTagByClassifyResp
{
  repeated UserTagBase tag_list = 1;
}


// 修改个性标签的分类的名字
message ModifyOptPersonalTagClassifyNameReq
{
  uint32 classify_id = 1;
  string classify_name = 2;
}
message ModifyOptPersonalTagClassifyNameResp
{
}


// 排序 个性标签的分类
message SortOptPersonalTagClassifyReq
{
  repeated uint32 classify_id_list = 1;
}
message SortOptPersonalTagClassifyResp
{}

// 删除个性标签的分类
message DelOptPersonalTagClassifyReq
{
  uint32 classify_id = 1;
  bool is_recover = 2;
}
message DelOptPersonalTagClassifyResp
{
}

message GetUserGameTagReq
{
  uint32 uid = 1;
  string game_name = 2;
}

message GetUserGameTagResp
{
  UserGameTagExt game_ext=1;
}


// 获取用户游戏段位
message GetUserGameTagDanReq
{
  repeated uint32 uid_list = 1;
  string game_name = 2;
}


message GameTagDan
{
  uint32 uid = 1;
  string game_name = 2;
  string game_dan = 3;
}

message GetUserGameTagDanResp
{
  repeated GameTagDan game_dan_list = 1;
}

message GetSimpleGameTagReq
{
  repeated uint32 uid_list = 1;
  string game_name = 2;
}

message GetSimpleGameTagResp
{
  repeated SimpleGameTagExt game_ext_list = 1;
}

message BatGetSimpleGameTagReq
{
  repeated uint32 uid_list = 1;
  repeated string game_name = 2;
}

message BatGetSimpleGameTagResp
{
  repeated SimpleGameTagList game_ext_list = 1;
}

message NotifyChannelPlayChangeReq
{
    uint32 channel_id = 1;
    uint32 play_type = 2;
    string play_name = 3;
}

message NotifyChannelPlayChangeResp
{
}

message SetUserGameNickReq
{
  uint32 target_uid = 1;
  uint32 tag_type =2;
  uint32 oper_type = 3;      // see ga::user_taglist ENUM_SET_USERTAG_OPER
  UserTagBase tag_base = 4;
  uint32 app_id = 5;            // for oss
  uint32 market_id = 6;         // for oss
}
message SetUserGameNickResp
{
}

message SetUserGameScreenShotReq
{
  uint32 target_uid = 1;
  uint32 tag_type =2;
  uint32 oper_type = 3;      // see ga::user_taglist ENUM_SET_USERTAG_OPER
  uint32 tag_id = 4;
  GameScreenShot game_screenshot = 5 ;
  uint32 app_id = 6;            // for oss
  uint32 market_id = 7;         // for oss
}
message SetUserGameScreenShotResp
{
}


/*仅提供开关“屏蔽手机联系人”功能的记录。*/
//改变推荐状态（是否向别人推荐自己）
message ChangeRecommendStatusReq
{
  uint32 uid=1;
  uint32 status=2;
}
message ChangeRecommendStatusResp
{
}

//获取推荐状态
message GetRecommendStatusReq
{
  uint32 uid=1;
}

message GetRecommendStatusResp
{
  uint32 status=2;
}

message SetMosaicImageContext{
    SetUserGameScreenShotReq game_screenshot_req = 1 ;
    string                   game_name = 2 ;
}

message SetMosaicImageReq{
    bool  result                       = 1;
    string mosaic_image_url		       = 2;
	bytes  context                     = 3;   //上下文，在回调中透传回来
    bool  source                       = 4 ;  //来源:0：回调,1：超时
}

message SetMosaicImageResp{
}

// deprecated 请使用user-tag-go服务或者userline-common-api接口服务
service usertag {
  option( tlvpickle.Magic ) = 15497;

  rpc GetUserTag ( GetUserTagReq ) returns ( GetUserTagResp ) {
    option( tlvpickle.CmdID ) = 1;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  rpc SetUserTag ( SetUserTagReq ) returns ( SetUserTagResp ) {
    option( tlvpickle.CmdID ) = 2;
    option( tlvpickle.OptString ) = "u:x:";
    option( tlvpickle.Usage ) = "-u <uid> -x <tagID>";
  }

  rpc BatGetUserTag ( BatGetUserTagReq ) returns ( BatGetUserTagResp ) {
    option( tlvpickle.CmdID ) = 3;
    option( tlvpickle.OptString ) = "u:x:";
    option( tlvpickle.Usage ) = "-u <uidlist like uid1,uid2> -x <is need ext info>";
  }

  // 获取标签列表
  rpc GetTagConfigList ( GetTagConfigListReq ) returns ( GetTagConfigListResp ) {
    option( tlvpickle.CmdID ) = 11;
    option( tlvpickle.OptString ) = "u:t:n:p:";
    option( tlvpickle.Usage ) = "-u <uid> -t<type>";
  }

  // 一般标签创建（ 年龄标签 游戏标签 个性标签 用这个创建）
  rpc CreateTagConfig ( CreateTagConfigReq ) returns ( CreateTagConfigResp ) {
    option( tlvpickle.CmdID ) = 12;
    option( tlvpickle.OptString ) = "x:p:";
    option( tlvpickle.Usage ) = "-x <tag type> -p <tag name> ";
  }

  // 一般标签修改（ 就是全量覆盖，年龄标签 游戏标签 FindWho标签 个性标签 都可以用这个修改，包括修改名称 修改游戏属性 修改标签分类）
  rpc ModifyTagConfig ( ModifyTagConfigReq ) returns ( ModifyTagConfigResp ) {
    option( tlvpickle.CmdID ) = 13;
    option( tlvpickle.OptString ) = "x:";
    option( tlvpickle.Usage ) = "-x <tag_id>";
  }

  // 标签 删除 / 恢复
  rpc DelTagConfig ( DelTagConfigReq ) returns ( DelTagConfigResp ) {
    option( tlvpickle.CmdID ) = 14;
    option( tlvpickle.OptString ) = "x:p:";
    option( tlvpickle.Usage ) = "-x <tag_id> -p <is recover>";
  }

  // 标签排序（ 年龄标签 游戏标签 FindWho标签的排序）
  rpc SortTagConfig ( SortTagConfigReq ) returns ( SortTagConfigResp ) {
    option( tlvpickle.CmdID ) = 15;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }


  // findwho 标签的创建
  rpc CreateFindWhoTypeTagConfig ( CreateFindWhoTypeTagConfigReq ) returns ( CreateFindWhoTypeTagConfigResp ) {
    option( tlvpickle.CmdID ) = 21;
    option( tlvpickle.OptString ) = "u:x:p:s:";
    option( tlvpickle.Usage ) = "-u <uid> ";
  }

  // 创建个性标签的分类
  rpc CreateOptPersonalTagClassify ( CreateOptPersonalTagClassifyReq ) returns ( CreateOptPersonalTagClassifyResp ) {
    option( tlvpickle.CmdID ) = 31;
    option( tlvpickle.OptString ) = "x:";
    option( tlvpickle.Usage ) = "-x <classify name> ";
  }

  // 获取个性标签的分类列表
  rpc GetOptPersonalTagClassifyList ( GetOptPersonalTagClassifyListReq ) returns ( GetOptPersonalTagClassifyListResp ) {
    option( tlvpickle.CmdID ) = 32;
    option( tlvpickle.OptString ) = "";
    option( tlvpickle.Usage ) = "";
  }

  // 根据分类获取 这个分类下的个性标签的列表
  rpc GetOptPersonalTagByClassify ( GetOptPersonalTagByClassifyReq ) returns ( GetOptPersonalTagByClassifyResp ) {
    option( tlvpickle.CmdID ) = 33;
    option( tlvpickle.OptString ) = "x:";
    option( tlvpickle.Usage ) = "-x <classify id> ";
  }

  // 修改指定个性标签的分类de 名字
  rpc ModifyOptPersonalTagClassifyName ( ModifyOptPersonalTagClassifyNameReq ) returns ( ModifyOptPersonalTagClassifyNameResp ) {
    option( tlvpickle.CmdID ) = 34;
    option( tlvpickle.OptString ) = "x:p:";
    option( tlvpickle.Usage ) = "-x <classify id> -x <classify name> ";
  }

  // 个性标签的分类的排序
  rpc SortOptPersonalTagClassify ( SortOptPersonalTagClassifyReq ) returns ( SortOptPersonalTagClassifyResp ) {
    option( tlvpickle.CmdID ) = 35;
    option( tlvpickle.OptString ) = "";
    option( tlvpickle.Usage ) = "";
  }

  // 个性标签的分类的 删除 / 恢复
  rpc DelOptPersonalTagClassify ( DelOptPersonalTagClassifyReq ) returns ( DelOptPersonalTagClassifyResp ) {
    option( tlvpickle.CmdID ) = 36;
    option( tlvpickle.OptString ) = "x:d:";
    option( tlvpickle.Usage ) = "-x <classify_id>  -d <recover>";
  }

  rpc SetUserOneTag ( SetUserOneTagReq ) returns ( SetUserOneTagResp ) {
    option( tlvpickle.CmdID ) = 37;
    option( tlvpickle.OptString ) = "u:x:";
    option( tlvpickle.Usage ) = "-u <uid> -x <tagID>";
  }
  /*
  rpc GetUserGameTag ( GetUserGameTagReq ) returns ( GetUserGameTagResp ) {
    option( tlvpickle.CmdID ) = 38;
    option( tlvpickle.OptString ) = "u:s:";
    option( tlvpickle.Usage ) = "-u <uid> -s <game name>";
  }*/
  /*
  rpc GetUserGameTagDan ( GetUserGameTagDanReq ) returns ( GetUserGameTagDanResp ) {
    option( tlvpickle.CmdID ) = 39;
    option( tlvpickle.OptString ) = "u:s:";
    option( tlvpickle.Usage ) = "-u <uid> -s <game name>";
  }*/

  rpc GetSimpleGameTag ( GetSimpleGameTagReq ) returns ( GetSimpleGameTagResp ) {
    option( tlvpickle.CmdID ) = 40;
    option( tlvpickle.OptString ) = "u:s:";
    option( tlvpickle.Usage ) = "-u <uid> -s <game name>";
  }

  rpc NotifyChannelPlayChange ( NotifyChannelPlayChangeReq ) returns ( NotifyChannelPlayChangeResp ) {
    option( tlvpickle.CmdID ) = 41;
    option( tlvpickle.OptString ) = "u:t:s:";
    option( tlvpickle.Usage ) = "-u <uid> -t<play_type> -s <play_name>";
  }

  rpc BatGetSimpleGameTag ( BatGetSimpleGameTagReq ) returns ( BatGetSimpleGameTagResp ) {
    option( tlvpickle.CmdID ) = 42;
    option( tlvpickle.OptString ) = "u:s:";
    option( tlvpickle.Usage ) = "-u <uid> -s <game name list>";
  }

  //单独设置游戏昵称/游戏截图，在审核后进行的设置
  rpc SetUserGameNick(SetUserGameNickReq) returns (SetUserGameNickResp) {
    option( tlvpickle.CmdID ) = 43;
    option( tlvpickle.OptString ) = "u:s:";
    option( tlvpickle.Usage ) = "-u <uid> -s <>";
  }
  rpc SetUserGameScreenShot(SetUserGameScreenShotReq) returns(SetUserGameScreenShotResp){
    option( tlvpickle.CmdID ) = 44;
    option( tlvpickle.OptString ) = "u:s:";
    option( tlvpickle.Usage ) = "-u <uid> -s <>";
  }

  /*仅提供开关“屏蔽手机联系人”功能的记录。*/
  rpc  ChangeRecommendStatus(ChangeRecommendStatusReq) returns( ChangeRecommendStatusResp )
  {
    option( tlvpickle.CmdID ) = 45;														  // 命令号
    option( tlvpickle.OptString ) = "u:s:";												  // 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = " -u<uid> -s<status>";										  // 测试工具的命令号帮助
  }

  rpc  GetRecommendStatus(GetRecommendStatusReq) returns( GetRecommendStatusResp )
  {
    option( tlvpickle.CmdID ) = 46;														  // 命令号
    option( tlvpickle.OptString ) = "u:";												  // 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = " -u<uid>";										  // 测试工具的命令号帮助
  }

   rpc  SetMosaicImage(SetMosaicImageReq) returns( SetMosaicImageResp )
  {
    option( tlvpickle.CmdID ) = 47;														  // 命令号
    option( tlvpickle.OptString ) = "u:";												  // 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = " -u<uid>";		             							  // 测试工具的命令号帮助
  }
}
