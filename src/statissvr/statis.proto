syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package Statis;

message StatisData {
	required uint32 total_cost = 1;
	required uint32 min_cost = 2;
	required uint32 max_cost = 3;
	required uint32 cnt = 4;
	required uint32 success_cnt = 5;
	required uint32 failed_cnt = 6;
	optional uint32 logic_failed_cnt = 7;
}

message StatisData2 {
	required string unit = 1;
	required double min_value = 2;
	required double max_value = 3;
	required double total_value = 4;
	required uint32 cnt = 5;
}

message CommonReport {
	required string time = 1;
	required string hostname = 2;
	required string localip = 3;
	required string type = 4;
	required string min_type = 5;
	required StatisData2 data = 6;
}

message AppRequestReport {
	required string time = 1;
	required string localip = 2;
	required string name = 3;
	required StatisData data = 4;
}

message SvrClientReport {
	required string time = 1;
	required string localip = 2;
	required string func = 3;
	required string remoteip = 4;
	required int32 port = 5;
	required StatisData data = 6;
	required string svr = 7;
}

// 上报app请求和svrClient的请求
message ReportReq {
	repeated AppRequestReport app_report_list = 1;
	repeated SvrClientReport svr_report_list = 2;
	repeated CommonReport common_report_list = 3;
}

message ReportResp {
}

message GetAppRequestStatisReq {
	required string begin_time = 1;
	required string end_time = 2;
	optional string name = 3;
}

message GetAppRequestStatisResp {
	repeated AppRequestReport data_list = 1;
}

message GetSvrClientStatisReq {
	required string begin_time = 1;
	required string end_time = 2;
	optional string svr = 3;
	optional string func = 4;
}

message GetSvrClientStatisResp {
	repeated SvrClientReport data_list = 1;
}

message GetCommonStatisReq {
	required string begin_time = 1;
	required string end_time = 2;
	optional string hostname = 3; 
	optional string localip = 4;
	optional string type = 5;
	optional string min_type = 6;
}

message GetCommonStatisResp {
	repeated CommonReport data_list = 1;
}

service Statis {
	option( tlvpickle.Magic ) = 15071;		// 服务监听端口号
	
	rpc Report( ReportReq ) returns( ReportResp ){
		option( tlvpickle.CmdID ) = 1;								// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";								// 测试工具的命令号帮助
	}
	
	rpc GetAppRequestStatis( GetAppRequestStatisReq ) returns( GetAppRequestStatisResp ){
		option( tlvpickle.CmdID ) = 2;								// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";								// 测试工具的命令号帮助
	}
	
	rpc GetSvrClientStatis( GetSvrClientStatisReq ) returns( GetSvrClientStatisResp ){
		option( tlvpickle.CmdID ) = 3;								// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";								// 测试工具的命令号帮助
	}
	
	rpc GetCommonStatis( GetCommonStatisReq ) returns( GetCommonStatisResp ) {
		option( tlvpickle.CmdID ) = 4;								// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";								// 测试工具的命令号帮助
	}
	
}




