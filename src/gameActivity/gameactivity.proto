syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package gameactivity;




// 设置 开黑大厅 单个房间的属性
message SetKhCenterChannelInfoReq
{
	required uint32 channel_id = 1;
	optional uint32 game_id = 2;
	optional uint32 member_cnt = 3;
	
	optional bool is_pwd = 4;
	optional bool is_open_recommend = 5;
}

message SetKhCenterChannelInfoResp
{
}	

// 获取 开黑大厅 指定属性的房间列表
message GetKhCenterChannelListReq
{
	optional uint32 game_id = 1;
	optional uint32 pageid = 2;
	optional uint32 count = 3;
	
	optional uint32 context_id = 4;
}
message GetKhCenterChannelListResp
{
	repeated uint32 channel_id_list = 1;
	optional uint32 context_id = 2;
	
	optional uint32 all_count = 3;
	optional bool is_finished = 4;
}

// 通知 开黑大厅 房间事件
message NotifyKhCenterChannelEventReq
{
	required uint32 channel_id = 1;
	required uint32 member_cnt = 2;
}
message NotifyKhCenterChannelEventResp
{
}
	
//  获取 开黑大厅 指定游戏ID的 开黑人数
message GetKhCenterMemCntReq
{
	repeated uint32 game_id_list = 1;
	
}
message GetKhCenterMemCntResp
{
	repeated uint32 member_cnt_list = 1;
}	

service gameactivity {
	option( tlvpickle.Magic ) = 15575;		// 服务监听端口号
	
	
	// 设置 开黑大厅 单个房间的属性
	rpc SetKhCenterChannelInfo( SetKhCenterChannelInfoReq ) returns( SetKhCenterChannelInfoResp){
		option( tlvpickle.CmdID ) = 1;										
        option( tlvpickle.OptString ) = "x:g:m:p:r:";						
        option( tlvpickle.Usage ) = "-x <channel id> -g <game id> -m <mem cnt> -p <is pwd> -r <is recommend>";
	}
	
	// 获取 开黑大厅 指定属性的房间列表
	rpc GetKhCenterChannelList( GetKhCenterChannelListReq ) returns( GetKhCenterChannelListResp ){
		option( tlvpickle.CmdID ) = 2;										
        option( tlvpickle.OptString ) = "g:o:p:l:";						
        option( tlvpickle.Usage ) = "-g <game id> -o <context id> -p <page id> -l <limit count>";
	}
	
	// 通知 开黑大厅 房间事件
	rpc NotifyKhCenterChannelEvent( NotifyKhCenterChannelEventReq ) returns( NotifyKhCenterChannelEventResp ){
		option( tlvpickle.CmdID ) = 3;										
        option( tlvpickle.OptString ) = "x:m:";						
        option( tlvpickle.Usage ) = "-x <channel id> -m <mem cnt>";
	}
	
	//  获取 开黑大厅 指定游戏ID的 开黑人数
	rpc GetKhCenterMemCnt( GetKhCenterMemCntReq ) returns( GetKhCenterMemCntResp ){
		option( tlvpickle.CmdID ) = 4;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <game id> ";
	}
}

