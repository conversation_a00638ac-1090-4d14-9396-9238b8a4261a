syntax="proto2";

// namespace
package Audit.dbquery;

//比较运算符，一般格式: field:{$op: value}
enum CompOperator{
    OP_COMP_EQ   = 1;
    OP_COMP_LT   = 2;
    OP_COMP_LTE  = 3;
    OP_COMP_GT   = 4;
    OP_COMP_GTE  = 5;

    OP_COMP_IN   = 6;
    OP_COMP_NIN  = 7;

    OP_COMP_LIKE  = 8;
}

//逻辑运算符，一般格式: $op:[{...}... ]
enum LogicalOperator{
    OP_LOGICAL_OR   = 1;
    OP_LOGICAL_AND  = 2;
}

enum TODO{
    OP_REGEX    = 2;
}

enum ValueType{
    VT_INT32    = 1;
    VT_UINT32   = 2;
    VT_INT64    = 3;
    VT_UINT64   = 4;
    VT_BOOL     = 5;
    VT_STRING   = 6;
}

message Value{
    required uint32 type = 1;   //ValueType
    optional int32 i32val =2;
    optional int32 u32val = 3;
    optional int64 i64val = 4;
    optional uint64 u64val = 5;
    optional bool   bval = 6;
    optional string strVal = 7;    
}

message SingleExpr{
    required string field = 1;
    required uint32 op = 2;     //CompOperator
    repeated Value value_list  = 3;
}

message CompoundExpr{
    required uint32 op = 1; //LogicalOperator
    repeated SingleExpr single_expr_list = 2;
    repeated CompoundExpr compound_expr_list = 3;
}

message QueryExpr{    
    repeated SingleExpr single_expr_list = 1;
    optional CompoundExpr compound_expr = 2;
}
