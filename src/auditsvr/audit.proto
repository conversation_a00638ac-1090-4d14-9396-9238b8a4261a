syntax="proto2";

// namespace
package Audit;

// 必须import
import "common/tlvpickle/skbuiltintype.proto";
import "auditquery.proto" ;

//内容类型
//enum CONTENT_TYPE{
//    CONTENT_TYPE_MSG = 1;
//}


//审核进度
enum AUDIT_PROGRESS{
    PROGRESS_TOAUDIT = 1;   // = 0x0001 未审核/待审核
    PROGRESS_AUDITED = 2;   // = 0x0002 已审核
    PROGRESS_AUDITING = 4;  // = 0x0004 审核中...，以后根据需要可以增加 进展

    PROGRESS_ALL = 7;       // = 0x0007 PROGRESS_TOAUDIT | PROGRESS_AUDITED | PROGRESS_AUDITING
}

//
//审核结果
//
enum AUDIT_DECISION{
    DECISION_PASS = 1;  //=0x0001  通过
    DECISION_DENY = 2;  //=0x0002  不通过

    DECISION_ALL  = 3;  //=0x0003,    DECISION_PASS| DECISION_DENY
}

message AuditDecision{
    required uint32 decision = 1;       //审核结果  AUDIT_DECISION
    optional uint64 audit_time = 2;     //审核时间
    optional string auditor = 3;        //审核者    
    optional string annotation = 4;     //原因备注
}

message ContentAudit {
    required uint32 type = 1;
    required uint32 id = 2;
    required string content = 3;
    required uint64 generate_time = 4;
    required uint32 progress = 5;
    optional AuditDecision audit_decision = 6;
}

//查询时分页
message Paginate{
    optional uint32 start_id = 1;   
    optional uint32 offset = 2;     //skip
    optional uint32 limit = 3;      //limit
}

//查询过滤
message Filter{
    required uint32 type = 1;
    optional uint32 progress = 2;   //进展
    optional uint32 decision = 3;   //结果
    optional int64 gen_begin_time = 4;	// unix timestamp, 内容生成开始时间
    optional int64 gen_end_time = 5;	// unix timestamp, 内容生成开始时间

    optional string auditor = 6;         //审核者
    optional int64 audit_begin_time = 7;    //unix timestamp,审核开始时间
    optional int64 audit_end_time = 8;      //unix timestamp,审核开始时间
    optional string annotation = 9; //原因

    optional string content_filter = 10;  //内容相关的过滤条件
    optional dbquery.QueryExpr content_queryexpr = 11;  //内容相关的过滤条件
}


//增加审核内容
message OrigContent{
    required uint32 type = 1;    //内容类型 CONTENT_TYPE
    required string content = 2;    
}

message AddContentReq{
    required OrigContent content = 1;
}

message AddContentRsp{
    required uint32 id = 1;
}

message BatchAddContentReq{ 
    repeated OrigContent content_list = 1;
}

message BatchAddContentRsp{
    repeated uint32 id_list = 1; 
}

//删除审核内容
message RemoveContentReq {
    required Filter filter = 1;
}

message RemoveContentRsp {
}

message RemoveContentWithIdReq{
    required uint32 type = 1;
    repeated uint32 id_list = 2;
}
message RemoveContentWithIdRsp{
}


//审核 内容（修改)
message AuditContentReq {
    required AuditDecision audit_decision = 1;
    required Filter filter = 2;
}

message AuditContentRsp {
}


message AuditContentWithIdReq{
    required uint32 type = 1;
    repeated uint32 id_list = 2;
    required AuditDecision audit_decision = 3;
}

message AuditContentWithIdRsp{
}


//查询 审核内容
message FindContentReq {
    required Filter filter = 1;
    optional Paginate page = 2;    //分页
}

message FindContentRsp {
    repeated ContentAudit content_list = 1;
}

message GetContentCountReq{
    required Filter filter = 1;
}

message GetContentCountRsp{
    required uint32 count = 1;
}

message FindContentWithIdReq{
    required uint32 type = 1;
    repeated uint32 id_list = 2;
}

message FindContentWithIdRsp{
    repeated ContentAudit content_list = 1;
}

//审核操作员

message AuditOperatorId{
    optional uint32 uid = 1;
    optional string username = 2;
    
}

message AddAuditOperatorReq{
    required uint32 audit_type = 1;
    required AuditOperatorId oper = 2;
}
message AddAuditOperatorRsp{
}

message DelAuditOperatorReq{
    required uint32 audit_type = 1;
    required AuditOperatorId oper = 2;
}
message DelAuditOperatorRsp{
}

message GetAuditOperatorReq{
    required uint32 audit_type = 1;
}
message GetAuditOperatorRsp{    
    repeated AuditOperatorId operator_list = 1;
}

service Audit {
    rpc AddContent ( AddContentReq ) returns ( AddContentRsp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "t:m:s:";
        option( tlvpickle.Usage ) = "-t <type:1=msg> -m <content json> [-s <time>]";
    }

    rpc BatchAddContent ( BatchAddContentReq ) returns ( BatchAddContentRsp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "-to be implemented...";
    }


    rpc RemoveContent ( RemoveContentReq ) returns ( RemoveContentRsp ){
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "-to be implemented...";
    }
    rpc RemoveContentWithId ( RemoveContentWithIdReq ) returns ( RemoveContentWithIdRsp ){
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "t:m:";
        option( tlvpickle.Usage ) = "-t <type:1=msg> -m <id1,id2,...>";
    }

    rpc AuditContent ( AuditContentReq ) returns ( AuditContentRsp ) {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "-to be implemented...";
    }
    rpc AuditContentWithId ( AuditContentWithIdReq ) returns ( AuditContentWithIdRsp ){
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "t:m:r:n:y:";
        option( tlvpickle.Usage ) = "-t <type:1=msg> -m <id1,id2,...> -r <decision:1=pass/2=deny> [-n <auditor>] [-y <annotation>]";
    }

    rpc FindContent ( FindContentReq ) returns ( FindContentRsp ) {
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "t:p:r:g:h:n:b:e:y:i:o:l:";
        option( tlvpickle.Usage ) = "-t <type> [-p <progress:1=to_audit/2=audited/4=auditing/7=all>] [-r <decision:1=pass/2=deny/3=all>]"
                        " [-g <gen_begin_time>] [ -h <gen_end_time>] [-n <auditor>] [-b <audit_begin_time> -e <audit_end_time>]"
                        " [-y <annotation>] [-i <content_filter>] [-o <offset id>] [-l <limit>]";
    }

    rpc FindContentWithId ( FindContentWithIdReq ) returns ( FindContentWithIdRsp ) {
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "-to be implemented...";
    }

    rpc GetContentCount ( GetContentCountReq ) returns ( GetContentCountRsp ) {
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "t:p:r:g:h:n:b:e:y:i:";
        option( tlvpickle.Usage ) = "-t <type> [-p <progress:1=to_audit/2=audited/4=auditing/7=all>] [-r <decision:1=pass/2=deny/3=all>]"
                        " [-g <gen_begin_time>] [ -h <gen_end_time>] [-n <auditor>] [-b <audit_begin_time> -e <audit_end_time>]"
                        " [-y <annotation>] [-i <content_filter>]";
    }

    
    rpc AddAuditOperator ( AddAuditOperatorReq ) returns ( AddAuditOperatorRsp ) {
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "t:u:n:";
        option( tlvpickle.Usage ) = "-t <type> -u <uid> -n <username>";
    }
    rpc DelAuditOperator ( DelAuditOperatorReq ) returns ( DelAuditOperatorRsp ) {
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "t:u:n:";
        option( tlvpickle.Usage ) = "-t <type> [-u <uid>] [-n <username>]";
    }
    rpc GetAuditOperator ( GetAuditOperatorReq ) returns ( GetAuditOperatorRsp ) {
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "t:";
        option( tlvpickle.Usage ) = "-t <type>";
    }
}
