syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Game;

enum GameLevel{
    GAME_LEVER_360 = 2;
    GAME_LEVER_BAIDU = 11;
    GAME_LEVER_ADMIN = 100;
}

enum GameStatus {
    NORMAL = 0;			// 正式上线
	COMING = 1;			// 即将测试
	COMMING_DELETE = 2;	// 删档内测
	DOWN   = 3;			// 下架
}

enum GameChannelStatus {
    CHANNEL_TT          =   0;      //公会成员和非公会成员均使用TT渠道
    CHANNEL_GUILD       =   1;     //公会成员使用公会渠道
    CHANNEL_WHITE_LIST  =   2;     //只有公会白名单内的公会成员下载才用公会渠道
}

message CreateGameReq
{
	required string game_package = 1;
	required string name 	= 2;
	optional string summary = 3;
	optional string intro 	= 4;
	optional uint32 type_id = 5;
	optional float rate 	= 6;
	optional string tags 	= 7;
	optional string game_size	= 8;
	optional string last_version = 9;
	optional uint32 status 	= 10;
	optional string vendor	= 11;
	optional string url		= 12;
	optional string icon	= 13;
	optional string game_source = 14;
	optional string screenshot = 15;	//	截图，多张截图通过空格分隔
	optional string begin_test  = 16;	//	开测时间（字符串）
	optional uint32 source_level = 17;
    optional uint32 game_lock = 18;
    optional string corner_icon = 19;	//角标
}

message CreateGameResp
{
	required uint32 game_id = 1;
}

message GameIdReq
{
	required uint32 game_id = 1;
}

message GameResp
{
	enum GameType {
	    NORMAL = 0;			// 正式上线
	    H5GAME = 1;			// html5游戏
	}

	required uint32 game_id 		= 1;
	required string game_package 	= 2;
	required string name 			= 3;
	required string summary 		= 4;
	required string intro			= 5;
	required uint32 type_id			= 6;	/*0:normal,1:h5game*/
	required float rate				= 7;
	required string tags			= 8;
	required string game_size		= 9;
	required string last_version 	= 10;
	required uint32 status 			= 11;
	required string vendor			= 12;
	required string url				= 13;
	required string icon			= 14;
	required uint32 hot				= 15;
	required uint32 featured		= 16;
	required string game_source		= 17;
	required bool partner			= 18;
	optional string screenshot		= 19;
	optional string begin_test		= 20;
	optional uint32 new_game_weight	= 21;
	optional uint32 source_level 	= 22;
    optional uint32 game_lock        = 23;  /* 是否自动跟新url等信息 */
    optional uint32 channel_status  = 24;   /* GameChannelStatus, 0:TT渠道, 1:公会渠道, 2:白名单公会使用公会渠道 */
    optional string corner_icon = 25;	//角标
}

message GetGameListReq
{
}

message GameListResp
{
	repeated GameResp games			= 1;
}

message GetHotGameListReq
{
	required uint32 limit = 1;
}

message GetFeaturedGameListReq
{
	required uint32 limit = 1;
}

message SearchGameReq
{
	required string keyword		= 1;
	required uint32 offset		= 2;
	required uint32 limit		= 3;
}

message SearchGameResp
{
	repeated GameResp games			= 1;
	required uint32 total			= 2;
}

message PartnerGameListReq
{
	required uint32 offset		= 1;
	required uint32 limit		= 2;
}

message PartnerGameListResp
{
	repeated GameResp games			= 1;
	required uint32 total			= 2;
}

message UpdateGamePartnerStatusReq
{
	repeated uint32 game_id_list = 1;
	required bool is_partner = 2;
}

message AddGuildPlayingGameReq {
	required uint32 game_id = 1;
	required uint32 weight = 2;
}

message RemoveGuildPlayingGameReq {
	required uint32 game_id = 1;
}

message UpdateGuildPlayingGameWeightReq {
	required uint32 game_id = 1;
	required uint32 weight = 2;
}

message LyGameIdReq {
	required uint32 game_id = 1;
}

message LyGameResp {
	required uint32 game_id = 1;
	optional string name = 2;
	optional string code = 3;
	optional string icon = 4;
	optional string sdk_key = 5;
	optional string charge_key = 6;
	optional string version = 7;
	optional string durl = 8;
	optional string cdnkey = 9;
	optional string md5 = 10;
	optional string status = 11;
	optional string svid = 12;
	optional string package_name = 13;
}

/////////////////////////////////////////////
// Import game
/////////////////////////////////////////////

message StGame {
	required string game_package = 1;		// 包名
	required string name = 2;				// 游戏名
	optional string custom_icon = 3;
	optional uint32 type = 4;
	optional string summary = 5;
	optional string intro = 6;
	optional float rate = 7;
	optional string tags = 8;
	optional string size = 9;
	optional string version = 10;
	optional uint32 status = 11;
	optional string icon = 12;
	optional string url = 13;
	optional uint32 hot = 14;
	optional uint32 featured = 15;
	optional string game_source = 16;
	optional string vendor = 17;
	optional string screenshot		= 18;
	optional string begin_test		= 19;
    optional uint32 game_lock       = 20;
    optional string corner_icon		= 21;
}

message ImportGamesReq {
    repeated StGame game_list = 1;
    required uint32 source_level = 3;		// 来源等级(低等级无法Update高等级的数据)
}

message ImportGamesResp {
    required uint32 success = 1;
}

message ImportGamesDetailResp {
	required uint32 created = 1;		// 成功创建的游戏数
	required uint32 updated = 2;		// 成功更新的游戏数
	repeated StGame failed_list = 3;	// 导入失败的游戏列表
}

/////////////////////////////////////////////
// Fuzzy game matching
/////////////////////////////////////////////

message StFuzzyMatchedGameResult {
	required uint32 match_game_id = 1;			// the matched game id
	required uint32 match_ratio = 2;			// match ratio (percent)
	required string match_game_package = 3;		// the matched game package
	required string game_package = 4;
	required string game_name = 5;
}

message StFuzzyMatchGameInfo {
	required string game_package = 1;			// 游戏包名
	required string game_name = 2;				// 游戏名
}

message FuzzyMatchGameReq {
	repeated StFuzzyMatchGameInfo game_list = 1;	// 游戏列表
}

message FuzzyMatchGameResp {
	repeated StFuzzyMatchedGameResult match_result_list = 1;		// 匹配结果
}

message ContainsAnyGameNameReq {
	required string word_to_test = 1;		// 被测的字符串
}

message ContainsAnyGameNameResp {
	required bool contains = 1;				// 是否包含
}

message AddOrUpdateGameResp {
	required uint32 game_id = 1;
}

/////////////////////////////////////////////
// Game Black/White Config
/////////////////////////////////////////////

message GetGameConfigUpdateTimeResp {
	required uint32 update_at = 1;		// 最后更新时间
}

message GetGameConfigResp {
	required uint32 update_at = 1;		// 更新时间
	repeated string black_list = 2;		// 黑名单
	repeated string white_list = 3;		// 白名单
}

message UpdateGameConfigReq {
	repeated string package_list = 1;	// 包名列表
	required uint32 type = 2;			// 0: 白名单 1: 黑名单
	required bool is_del = 3;			// 0: 增加 1: 删除
}

message UpdateGameConfigResp {
}

/////////////////////////////////////////////
// Batch Get Game List
/////////////////////////////////////////////

message GetGamesByIdListReq {
	repeated uint32 game_id_list = 1;
}

message GetGamesByIdListResp {
	repeated GameResp game_list = 1;
}

message GetGuildPlayingGameListReq {

}

//获取最多公会入驻的游戏列表
message GetTopGuildGameListReq {
	required uint32 limit = 1;
}


//更新游戏资料
message UpdateGameReq {
	required uint32 game_id = 1;
	required StGame game = 2;
	required uint32 level = 3;
	required bool shield = 4;
	required bool should_update_package = 5;	// 是否更新包名
}

message UpdateGameResp {

}

//更新游戏状态
message UpdateGameStatusReq {
	required uint32 game_id = 1;
	required uint32 status = 2;
}

message UpdateGameStatusResp {
}

// 获取新游列表
message GetNewGameListReq
{
}

message GetNewGameListResp
{
	repeated GameResp games			= 1;
}

// 对新游进行排序
message ReorderNewGameListReq {
	repeated uint32 game_id_list	= 1;	// 游戏id列表，根据顺序重置new_game_weight属性
}

message ReorderNewGameListResp {

}


// 游戏安装包
message StGamePackage {
	required uint32 game_package_id = 1;
	required uint32 game_id = 2;
	required string name = 3;
	required string url = 4;
	required string version = 5;
	required string size = 6;
	required uint32 upload_time = 7;
	required uint32 download_count = 8;
}

// 添加游戏安装包



message CreateGamePackageReq {
	required uint32 game_id = 1;
	required string name = 2;
	required string url = 3;
	required string version = 4;
	required string size = 5;
}

message CreateGamePackageResp {
	required uint32 game_package_id = 1;
}

// 获取游戏安装包列表


message GetGamePackageListReq {
	required uint32 game_id = 1;
	required uint32 limit = 2;
}

message GetGamePackageListResp {
	repeated StGamePackage game_package_list = 1;
}

// 开服信息
message StGameServer {
	required uint32 game_server_id = 1;
	required uint32 game_id = 2;
	required string name = 3;
	required uint32 open_time = 4;
    optional uint32 os_platform = 5; //手机平台, 1安卓， 2ios
}

message StGameServerList {
	required uint32 game_id = 1;
	repeated StGameServer server_list = 2;
}

// 添加开服信息
message CreateGameServerReq {
	required uint32 game_id = 1;
	required string name = 2;
	required uint32 open_time = 3;
    optional uint32 os_platform = 4; //手机平台, 1安卓， 2ios
}

message CreateGameServerResp{
	required uint32 game_server_id = 1;
}

message BatchCreateGameServerReq {
    repeated StGameServer server_list = 1;
}

message GetGameServerReq {
	required uint32 game_server_id = 1;
}

message GetGameServerResp {
	required StGameServer game_server = 1;
}

message GetGameServerListReq {
	required uint32 game_id = 1;
	required uint32 offset = 2;
	required uint32 limit = 3;
	required bool only_future = 4;	//是否只获取open_time大于现在的开服列表
}

message GetGameServerListResp {
	repeated StGameServer game_server_list = 1;
}

message BatchGetGameServerListReq {
	repeated uint32 game_list = 1;
	required bool only_future = 2;	//是否只获取open_time大于现在的开服列表
}

message BatchGetGameServerListResp {
	repeated StGameServerList game_server_list = 1;
}


message CountGameServerReq{
	required uint32 game_id = 1;
}

message CountGameServerResp{
	required uint32 total = 1;
}

message UpdateGameServerReq {
	required StGameServer game_server = 1;
}

message UpdateGameServerResp {

}

message DeleteGameServerReq {
	required uint32 game_server_id = 1;
}

message DeleteGameServerResp {
}


message IncreaseGamePackageDownloadCountReq {
	required uint32 game_package_id = 1;
}

message IncreaseGamePackageDownloadCountResp {

}

//=======================================================
// Game Resources
//=======================================================

message GameSnapshot {
    required string snapshot_url = 1;
}

message GameVideo {
    required string video_url = 1;
    required string snapshot_url = 2;
    required string title = 3;
}

message AbstractGameResource {
    enum ResourceType {
        SNAPSHOT = 1;
        VIDEO = 2;
    }

    enum ScaleType {
    	horizontal = 1;	//横屏
    	vertical = 2;	//竖屏
    }

    required uint32 res_id = 1;
    required uint32 game_id = 2;
    required uint32 res_type = 3;
    required bytes  res_binary = 4;
    required uint32 add_time = 5;
    optional uint32 scale_type = 6;
}

message AddGameResourceReq {
    required AbstractGameResource resource = 1;     // res_id/add_time填0
}

message AddGameResourceResp {
    required uint32 res_id = 1;
}

message GetGameResourcesReq {
    required uint32 game_id = 1;
    required uint32 res_type = 2;           // GameResource::ResourceType
    required uint32 offset = 3;
    required uint32 count = 4;
}

message GetGameResourcesResp {
    repeated AbstractGameResource resource_list = 1;
    required uint32 total = 2;
}

message DeleteGameResourceReq {
    required uint32 game_id = 1;
    required uint32 res_id = 2;
}

message GetTopGameListReq
{
}

message TopGame
{
    required uint32 game_id= 1;
    required uint32 extra_followed_num = 2;
    required uint32 extra_downloaded_num = 3;
    required uint32 rank = 4;
    optional uint32 visible = 5;
}

message GetTopGameListResp
{
    repeated TopGame  game_list = 1;
}

message ModifyTopGameReq
{
    required TopGame game = 1;
}

message ModifyTopGameResp
{
}

message DeleteTopGameReq
{
    required uint32 game_id = 1;
}

message DeleteTopGameResp
{
}

message GetDownloadTimesReq
{
    repeated uint32 game_id_list = 1;
}

message GameStatics
{
    required uint32 game_id = 1;
    required uint32 download_times = 2;
}

message GetDownloadTimesResp
{
    repeated GameStatics game_statics_list = 1;
}

message IncreaseGameDownloadTimesReq {
	required uint32 game_id = 1;
}

message IncreaseGameDownloadTimesResp {

}

message ModifyExtraDownloadTimesReq {
	required uint32 game_id = 1;
    required uint32 extra_download_times = 2;
}

message ModifyExtraDownloadTimesResp {
}

message GetExtraDownloadTimesReq {
    repeated uint32 game_id_list = 1;
}

message GetExtraDownloadTimesResp {
    repeated GameStatics download_time_list = 1;
}

message BatchGetGameTestPackageCountReq {
    repeated uint32 game_id_list = 1;
}

message GameTestPackageCount {
    required uint32 game_id = 1;
    required uint32 package_count = 2;
}

message BatchGetGameTestPackageCountResp {
    repeated GameTestPackageCount package_count_list = 1;
}

//获取多个游戏安装包列表
message GetNewestGamePackageReq{
    repeated uint32 game_id_list = 1;
}

message GetNewestGamePackageResp{
    repeated StGamePackage game_package_list = 1;
}

//删除游戏安装包
message DeleteGamePackageReq{
    required uint32 package_id = 1;
}

message DeleteGamePackageResp{
}

//获取一个topgame
message GetTopGameReq{
    required uint32 game_id = 1;
}

message GetTopGameResp{
    required TopGame topgame = 1;
}

message TTGameVersionInfo {
    required string game_package = 1;
    required string version_name = 2;
    required uint32 version_code = 3;
    optional string download_url = 4;
    optional uint32 game_id = 5;
    optional string file_md5 = 6;
    optional string head_md5 = 7;
    optional uint32 ly_game_id = 8;
}

message GetTTGameVersionsReq {
    repeated string game_package_list = 1;
}

message GetTTGameVersionsResp {
    repeated TTGameVersionInfo game_version_info_list = 1;
}

message SearchGuildPlayingGameListReq{
	required int32 type = 1;
}

message GetTopGameByGameIdsReq{
	repeated uint32 gameIds = 1;
}


//GAME ZONE

message GameZoneInfo{
	required uint32 game_id = 1;
	required uint32 tt_group_id = 2;
	required string game_zone_banner_url = 3;
	optional uint32 has_lottery = 4;
}

message GetGameZoneInfoReq{
	required uint32 index = 1;
	required uint32 limit = 2;
}

message GetGameZoneInfoByIdReq{
	required uint32 game_id = 1;
}

message GetGameZoneInfoByIdResp{
	required uint32 tt_group_id = 1;
	required string game_zone_banner_url = 2;
	optional uint32 has_lottery = 3;
}

message GetGameZoneInfoResp{
	repeated GameZoneInfo game_zone_info_list = 1;
}

message GetGameZoneInfoByGroupIdReq{
	required uint32 group_id = 1;
}

message SetGameZoneInfoReq{
	required uint32 game_id = 1;
	optional uint32 tt_group_id = 2;
	optional string game_zone_banner_url = 3;
	optional uint32 has_lottery = 4;
}

message DeleteGameZoneInfoReq{
	required uint32 game_id = 1;
}

enum GAME_RANK_TYPE{
    NUMBER_ONE_GAME = 1;
    BEST_GAME = 2;
    NEW_GAME = 3;
    HOT_GAME = 4;
	GUILD_RECRUIT_GAME = 5; //工会招募游戏榜
	NOT_TT_GAME = 6;		//非TT联运游戏
    DISCOVERY_GAME  =   7;  //发现页游戏    
	CLOSED_BETA = 8;       //内测
	FRI_POP	    = 9;       //好友热玩
}

message GameZoneRankInfo{
    required uint32 game_id = 1 ;
    required uint32 rank = 2;
	optional string url = 3;
}

message SetGameZoneGamesRankReq{
	required uint32 game_id = 1;
	required uint32 rank_type = 2;
    required uint32 rank = 3;
	optional string url = 4;
}

message GetGameZoneGamesRankReq{
	required uint32 rank_type = 1;
    optional uint32 limit = 2;
}

message GetGameZoneGamesRankResp{
	repeated uint32 game_id_list = 1;
}

message DelGameZoneGamesRankReq{
    required uint32 game_id = 1;
    required uint32 rank_type = 2;
}

message GetGameZoneRankValueReq{
    required uint32 rank_type = 1;
}

message GetGameZoneRankValueResp{
	repeated GameZoneRankInfo game_list = 1;
}

message SearchGameAreaByNameResp {
	repeated uint32 game_id_list = 1;
}

message UnsetGameZoneTTGroupReq {
	required uint32 group_id = 1;
}

//游戏后台任务
message GameAutoTask{
    required uint32 game_id = 1;
    required string url = 2;
    required uint32 task_type = 3; //任务类型,1更新开服信息
}

message SetGameAutoTaskReq{
    repeated GameAutoTask task_list = 1;
}

message GetGameAutoTaskReq{
    required uint32 game_id = 1; //game_id==0时，获取所有
}

message GetGameAutoTaskResp{
    repeated GameAutoTask task_list = 1;
}

message RemoveGameAutoTaskReq{
    required uint32 game_id = 1;
}
/*********游戏后台任务END********/

message GameTTagRank{
    required uint32 game_id = 1;
    required uint32 tag_id = 2;
    optional uint32 game_rank = 3;	//game ranking of tag
}

message AddGameTTagReq {
	required uint32 game_id = 1;
	required uint32 tag_id = 2;
	optional uint32 game_rank = 3;	////game ranking of tag
}

message DelGameTTagReq {
	optional uint32 game_id = 1;
	required uint32 tag_id = 2;
}

message GetGameTTagListReq {
	required uint32 game_id = 1;
}

message GetGameTTagListResp {
	repeated uint32 tag_id_list = 1;
}

message GetGameRankListByTTagReq {
	required uint32 tag_id = 1;
	optional uint32 offset = 2;
	optional uint32 limit = 3;
}

message GetGameRankListByTTagResp {
	repeated GameTTagRank game_list = 1;
}

message GetGamesByTTagResp {
	repeated GameResp game_list = 1;
}

message SetGameZoneLotteryInfoReq{
	required uint32 game_id = 1;
	required uint32 has_lottery = 2;
}

message ModifyGuildPlayingGameReq{
	repeated uint32 game_ids = 1;
	optional uint32 rank_type = 2;
}

message GetGuildPlayingGameResp{
	repeated uint32 game_ids_one = 1;
	repeated uint32 game_ids_zero = 2;
}

message GameCornerIcon{
	required uint32 game_id = 1;
	required string icon = 2;
	required uint64 start_time = 3;
	required uint64 end_time = 4;
}

message UpdateGameCornerIconReq{
	required GameCornerIcon icon = 1;
}

message DelGameCornerIconReq{
	required uint32 game_id = 1;
}

message GetGameCornerIconListResp{
	repeated GameCornerIcon list = 1;
}

message GameActAdEntry{
    required uint32 id = 1;
    required string img = 2;
    required string url = 3;
}

message GetGameActAdListResp{
    repeated GameActAdEntry ad_list = 1;
}

message GameActAdReq{
    required uint32 id = 1;    
}

service Game {
	option( tlvpickle.Magic ) = 14900;		// 服务监听端口号

	rpc CreateGame( CreateGameReq ) returns( CreateGameResp ){
		option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "p:n:s:i:o:t:r:g:z:e:d:u:h:m:a:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <package> -n <game name> -s <summary> -i <intro> -o <icon> -t <game type> -r <rate> -g <tags> -z <size> -e <version> -d <vendor> -u <url> -h <screenshot> -m <begin test time> -a <status>";	// 测试工具的命令号帮助
	}

	rpc GetGame( GameIdReq ) returns ( GameResp ){
		option( tlvpickle.CmdID ) = 2;									// 命令号
        option( tlvpickle.OptString ) = "g:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game id> -t <total>";
	}

	rpc GetGameList ( GetGameListReq ) returns ( GameListResp ){
		option( tlvpickle.CmdID ) = 3;								// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
	}

	rpc SearchGame ( SearchGameReq ) returns ( SearchGameResp ){
		option( tlvpickle.CmdID ) = 4;										// 命令号
        option( tlvpickle.OptString ) = "k:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-k <keyword>";
	}

	rpc GetHotGameList ( GetHotGameListReq ) returns ( GameListResp ){
		option( tlvpickle.CmdID ) = 5;										// 命令号
        option( tlvpickle.OptString ) = "l:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <limit>";
	}

	rpc GetFeaturedGameList ( GetFeaturedGameListReq ) returns ( GameListResp ){
		option( tlvpickle.CmdID ) = 6;										// 命令号
        option( tlvpickle.OptString ) = "l:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <limit>";
	}

	rpc UpdateGamePartnerStatus ( UpdateGamePartnerStatusReq ) returns ( tlvpickle.SKBuiltinEmpty_PB  ){
		option( tlvpickle.CmdID ) = 7;										// 命令号
        option( tlvpickle.OptString ) = "g:p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game id> -p <is partner>";
	}

	rpc PartnerGameList ( PartnerGameListReq ) returns ( PartnerGameListResp ){
		option( tlvpickle.CmdID ) = 8;										// 命令号
        option( tlvpickle.OptString ) = "o:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-o <offset> -s <size>";
	}

	// 旧的导入游戏接口, 目前用于导入360库
    rpc ImportGameList( ImportGamesReq ) returns ( ImportGamesResp ) {
        option( tlvpickle.CmdID ) = 9;										// 命令号
        option( tlvpickle.OptString ) = "p:n:s:i:o:t:r:g:z:e:d:u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <package> -n <game name> -s <summary> -i <intro> -o <icon> -t <game type> -r <rate> -g <tags> -z <size> -e <version> -d <vendor> -u <url>";	// 测试工具的命令号帮助
    }

 	rpc FuzzyMatchGame( FuzzyMatchGameReq ) returns ( FuzzyMatchGameResp) {
 		option( tlvpickle.CmdID ) = 10;										// 命令号
        option( tlvpickle.OptString ) = "g:n:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_package> -n <game_name>";
 	}

 	rpc ContainsAnyGameName( ContainsAnyGameNameReq ) returns ( ContainsAnyGameNameResp ) {
 		option( tlvpickle.CmdID ) = 11;										// 命令号
        option( tlvpickle.OptString ) = "w:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-w <word>";
 	}

 	// 新的导入游戏接口, 还在测试阶段. 测试接口只能导单个
 	rpc ImportGames( ImportGamesReq ) returns( ImportGamesDetailResp ) {
 		option( tlvpickle.CmdID ) = 12;										// 命令号
        option( tlvpickle.OptString ) = "p:n:o:z:e:u:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <package> -n <game name> -o <icon> -z <size> -e <version> -u <url>";	// 测试工具的命令号帮助
 	}

 	rpc GetGameConfigUpdateTime( tlvpickle.SKBuiltinEmpty_PB ) returns( GetGameConfigUpdateTimeResp ) {
 		option( tlvpickle.CmdID ) = 13;
 		option( tlvpickle.OptString ) = "";
 		option( tlvpickle.Usage ) = "";
 	}

 	rpc GetGameConfig( tlvpickle.SKBuiltinEmpty_PB ) returns( GetGameConfigResp ) {
 		option( tlvpickle.CmdID ) = 14;
 		option( tlvpickle.OptString ) = "";
 		option( tlvpickle.Usage ) = "";
 	}

 	rpc UpdateGameConfig( UpdateGameConfigReq ) returns( UpdateGameConfigResp ) {
 		option( tlvpickle.CmdID ) = 15;
 		option( tlvpickle.OptString ) = "p:t:d:";
 		option( tlvpickle.Usage ) = "-p <package> -t <type> -d <is_del>";
 	}

 	rpc GetGamesByIdList( GetGamesByIdListReq ) returns( GetGamesByIdListResp ) {
 		option( tlvpickle.CmdID ) = 16;
 		option( tlvpickle.OptString ) = "g:";
 		option( tlvpickle.Usage ) = "-g <game_id1,game_id2,...>";
 	}

 	rpc GetGuildPlayingGameList ( GetGuildPlayingGameListReq ) returns ( GameListResp ){
 		option( tlvpickle.CmdID ) = 17;
 		option( tlvpickle.OptString ) = "";
 		option( tlvpickle.Usage ) = "";
 	}

 	rpc GetTopGuildGameList ( GetTopGuildGameListReq ) returns ( GameListResp ){
 		option( tlvpickle.CmdID ) = 18;
 		option( tlvpickle.OptString ) = "l:";
 		option( tlvpickle.Usage ) = "-l <limit>";
 	}

 	rpc AddGuildPlayingGame ( AddGuildPlayingGameReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 19;
 		option( tlvpickle.OptString ) = "g:w:";
 		option( tlvpickle.Usage ) = "-g <game id> -w <weight>";
 	}

 	rpc RemoveGuildPlayingGame ( RemoveGuildPlayingGameReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 20;
 		option( tlvpickle.OptString ) = "g:";
 		option( tlvpickle.Usage ) = "-g <game id>";
 	}

 	rpc UpdateGuildPlayingGameWeight ( UpdateGuildPlayingGameWeightReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 21;
 		option( tlvpickle.OptString ) = "g:w:";
 		option( tlvpickle.Usage ) = "-g <game id> -w <weight>";
 	}

 	rpc UpdateGame ( UpdateGameReq ) returns ( UpdateGameResp ){
 		option( tlvpickle.CmdID ) = 22;										// 命令号
        option( tlvpickle.OptString ) = "b:p:n:s:i:o:t:r:g:z:e:d:u:h:m:a:l:k:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-b <game id> -p <package> -n <game name> -s <summary> -i <intro> -o <icon> -t <game type> -r <rate> -g <tags> -z <size> -e <version> -d <vendor> -u <url> -h <screenshot> -m <begin test time> -a <status> -l <level> -k <lock_status>";	// 测试工具的命令号帮助

 	}

 	rpc GetNewGameList ( GetNewGameListReq ) returns ( GetNewGameListResp ){
 		option( tlvpickle.CmdID ) = 23;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
 	}

 	rpc ReorderNewGameList ( ReorderNewGameListReq ) returns ( ReorderNewGameListResp ){
 		option( tlvpickle.CmdID ) = 24;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id1,game_id2,...>";
 	}

 	rpc CreateGamePackage ( CreateGamePackageReq ) returns ( CreateGamePackageResp ){
 		option( tlvpickle.CmdID ) = 25;										// 命令号
        option( tlvpickle.OptString ) = "g:n:u:e:s:o:t:d:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game id> -n <game package name> -u <url> -e <version> -s <size> -o <open download time> -t <test begin> -d <test end>";
 	}

 	rpc GetGamePackageList ( GetGamePackageListReq ) returns ( GetGamePackageListResp ){
 		option( tlvpickle.CmdID ) = 26;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id>";
 	}

 	rpc CreateGameServer ( CreateGameServerReq ) returns ( CreateGameServerResp ){
 		option( tlvpickle.CmdID ) = 27;										// 命令号
        option( tlvpickle.OptString ) = "g:n:o:p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id> -n <name> -o <open time> -p <os_platform>";
 	}

 	rpc GetGameServerList ( GetGameServerListReq ) returns ( GetGameServerListResp ){
 		option( tlvpickle.CmdID ) = 28;										// 命令号
        option( tlvpickle.OptString ) = "g:o:l:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id> -o <offset> -l <limit>";
 	}

 	rpc UpdateGameServer ( UpdateGameServerReq ) returns ( UpdateGameServerResp ){
 		option( tlvpickle.CmdID ) = 29;										// 命令号
        option( tlvpickle.OptString ) = "s:n:o:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-s <game server id> -n <name> -o <open time>";
 	}

 	rpc GetGameServer ( GetGameServerReq ) returns ( GetGameServerResp ){
 		option( tlvpickle.CmdID ) = 30;										// 命令号
        option( tlvpickle.OptString ) = "s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-s <game server id>";
 	}

 	rpc CountGameServer ( CountGameServerReq ) returns ( CountGameServerResp ){
 		option( tlvpickle.CmdID ) = 31;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id>";
 	}

 	rpc UpdateGameStatus ( UpdateGameStatusReq ) returns ( UpdateGameStatusResp ){
 		option( tlvpickle.CmdID ) = 32;										// 命令号
        option( tlvpickle.OptString ) = "g:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id> -s <status>";
 	}

 	rpc DeleteGameServer ( DeleteGameServerReq ) returns ( DeleteGameServerResp ){
 		option( tlvpickle.CmdID ) = 33;										// 命令号
        option( tlvpickle.OptString ) = "s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-s <game server id>";
 	}

 	rpc IncreaseGamePackageDownloadCount ( IncreaseGamePackageDownloadCountReq ) returns ( IncreaseGamePackageDownloadCountResp ){
 		option( tlvpickle.CmdID ) = 34;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_package>";
 	}

 	rpc BatchGetGameServerList( BatchGetGameServerListReq ) returns ( BatchGetGameServerListResp ){
 		option( tlvpickle.CmdID ) = 35;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id>";
 	}

    rpc GetTopGameList(GetTopGameListReq) returns(GetTopGameListResp)
    {
        option( tlvpickle.CmdID ) = 36;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
    }

    rpc AddGameResource( AddGameResourceReq ) returns( AddGameResourceResp ) {
        option( tlvpickle.CmdID ) = 37;										// 命令号
        option( tlvpickle.OptString ) = "g:t:u:r:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id> -t <res_type> -u <res_url> -r <res_title>";
    }

    rpc GetGameResources( GetGameResourcesReq ) returns( GetGameResourcesResp ) {
        option( tlvpickle.CmdID ) = 38;										// 命令号
        option( tlvpickle.OptString ) = "g:t:o:n:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id> -t <res_type> -o <offset> -n <count>";
    }

    rpc DeleteGameResource( DeleteGameResourceReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 39;										// 命令号
        option( tlvpickle.OptString ) = "g:r:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id> -r <res_id>";
    }
    rpc GetDownloadTimesByGameIds(GetDownloadTimesReq) returns(GetDownloadTimesResp)
    {
        option( tlvpickle.CmdID ) = 40;										// 命令号
        option( tlvpickle.OptString ) = "g:";
 		option( tlvpickle.Usage ) = "-g <game_id1,game_id2,...>";
    }
    rpc IncreaseGameDownloadTimes(IncreaseGameDownloadTimesReq) returns(IncreaseGameDownloadTimesResp)
    {
        option( tlvpickle.CmdID ) = 41;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id>";
    }

    rpc ModifyTopGame(ModifyTopGameReq) returns (ModifyTopGameResp)
    {
        option( tlvpickle.CmdID ) = 42;										// 命令号
        option( tlvpickle.OptString ) = "g:l:d:r:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id> -l <followed_num> -d <downloaded_num> -r <rank>";
    }

    rpc DeleteTopGame(DeleteTopGameReq) returns (DeleteTopGameResp)
    {
        option( tlvpickle.CmdID ) = 43;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id>";
    }

    rpc BatchGetGameTestPackageCount( BatchGetGameTestPackageCountReq ) returns ( BatchGetGameTestPackageCountResp )
    {
        option( tlvpickle.CmdID ) = 44;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id_list>";
    }

    rpc GetNewestGamePackageByIdList( GetNewestGamePackageReq ) returns ( GetNewestGamePackageResp )
    {
        option( tlvpickle.CmdID ) = 45;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id_list>";
    }

    rpc DeleteGamePackage( DeleteGamePackageReq ) returns (DeleteGamePackageResp)
    {
        option( tlvpickle.CmdID ) = 46;										// 命令号
        option( tlvpickle.OptString ) = "p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <package_id>";
    }

    rpc GetTopGame( GetTopGameReq ) returns (GetTopGameResp)
    {
        option( tlvpickle.CmdID ) = 47;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id>";
    }

    rpc UpdateTTGameVersion( TTGameVersionInfo ) returns ( tlvpickle.SKBuiltinEmpty_PB )
    {
        option( tlvpickle.CmdID ) = 48;
        option( tlvpickle.OptString ) = "p:v:k:u:";
        option( tlvpickle.Usage ) = "-p <game_pkg> -n <version_name> -k <version_code> -u <download_url>";
    }

    rpc GetTTGameVersions( GetTTGameVersionsReq ) returns ( GetTTGameVersionsResp )
    {
        option( tlvpickle.CmdID ) = 49;
        option( tlvpickle.OptString ) = "p:";
        option( tlvpickle.Usage ) = "-p <game_pkg_list>";
    }

    rpc AddTopGame(ModifyTopGameReq) returns (ModifyTopGameResp)
    {
        option( tlvpickle.CmdID ) = 50;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id>";
    }

	rpc SearchGameAreaByName ( SearchGameReq ) returns ( SearchGameAreaByNameResp ){
		option( tlvpickle.CmdID ) = 51;										// 命令号
        option( tlvpickle.OptString ) = "k:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-k <keyword>";
	}

 	rpc SearchGuildPlayingGameList ( SearchGuildPlayingGameListReq ) returns ( GameListResp ){
 		option( tlvpickle.CmdID ) = 52;
 		option( tlvpickle.OptString ) = "t:";
 		option( tlvpickle.Usage ) = "-t <type>";
 	}

 	rpc GetTopGameByGameIds ( GetTopGameByGameIdsReq ) returns ( GetTopGameListResp ){
 		option( tlvpickle.CmdID ) = 53;
 		option( tlvpickle.OptString ) = "t:";
 		option( tlvpickle.Usage ) = "-t <type>";
 	}

	rpc GetGameZoneInfo ( GetGameZoneInfoReq) returns ( GetGameZoneInfoResp ){
 		option( tlvpickle.CmdID ) = 54;
 		option( tlvpickle.OptString ) = "";
 		option( tlvpickle.Usage ) = "";
 	}

	rpc SetGameZoneInfo ( SetGameZoneInfoReq) returns (  tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 55;
 		option( tlvpickle.OptString ) = "";
 		option( tlvpickle.Usage ) = "";
 	}

	rpc DeleteGameZoneInfo ( DeleteGameZoneInfoReq) returns (  tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 56;
 		option( tlvpickle.OptString ) = "";
 		option( tlvpickle.Usage ) = "";
 	}

	rpc GetGameZoneGamesRank ( GetGameZoneGamesRankReq) returns (  GetGameZoneGamesRankResp ){
 		option( tlvpickle.CmdID ) = 57;
 		option( tlvpickle.OptString ) = "";
 		option( tlvpickle.Usage ) = "";
 	}

	rpc SetGameZoneGamesRank ( SetGameZoneGamesRankReq) returns (  tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 58;
 		option( tlvpickle.OptString ) = "";
 		option( tlvpickle.Usage ) = "";
 	}

	rpc GetGameZoneInfoById ( GetGameZoneInfoByIdReq) returns ( GetGameZoneInfoByIdResp ){
 		option( tlvpickle.CmdID ) = 59;
 		option( tlvpickle.OptString ) = "";
 		option( tlvpickle.Usage ) = "";
 	}

    rpc ModifyExtraDownloadTimes(ModifyExtraDownloadTimesReq) returns(ModifyExtraDownloadTimesResp){
        option( tlvpickle.CmdID ) = 60;
        option( tlvpickle.OptString ) = "g:t:";
        option( tlvpickle.Usage ) = "-g <game_id> -t <extra_download_times>";
    }

    rpc GetExtraDownloadTimes(GetExtraDownloadTimesReq) returns(GetExtraDownloadTimesResp){
        option( tlvpickle.CmdID ) = 61;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g <game_id1,game_id2,...>";
    }

    rpc DelGameZoneGamesRank(DelGameZoneGamesRankReq) returns(tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 62;
        option( tlvpickle.OptString ) = "g:t:";
        option( tlvpickle.Usage ) = "-g <game_id1> -t <type>";
    }

    rpc GetGameZoneRankValue(GetGameZoneRankValueReq) returns (GetGameZoneRankValueResp){
        option( tlvpickle.CmdID ) = 63;
        option( tlvpickle.OptString ) = "t:";
        option( tlvpickle.Usage ) = "-t <rank_type>";
    }

	rpc GetGameZoneInfoByGroupId ( GetGameZoneInfoByGroupIdReq) returns ( GetGameZoneInfoResp ){
 		option( tlvpickle.CmdID ) = 64;
 		option( tlvpickle.OptString ) = "g:";
 		option( tlvpickle.Usage ) = "-g <groupId>";
 	} 

    rpc UpdateTTGameReleaseVersion( TTGameVersionInfo ) returns ( tlvpickle.SKBuiltinEmpty_PB )
    {
        option( tlvpickle.CmdID ) = 65;
        option( tlvpickle.OptString ) = "p:v:k:u:";
        option( tlvpickle.Usage ) = "-p <game_pkg> -n <version_name> -k <version_code> -u <download_url>";
    }

    rpc GetTTGameReleaseVersions( GetTTGameVersionsReq ) returns ( GetTTGameVersionsResp )
    {
        option( tlvpickle.CmdID ) = 66;
        option( tlvpickle.OptString ) = "p:";
        option( tlvpickle.Usage ) = "-p <game_pkg_list>";
    } 	   

	rpc UnsetGameZoneTTGroup ( UnsetGameZoneTTGroupReq) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 67;
 		option( tlvpickle.OptString ) = "g:";
 		option( tlvpickle.Usage ) = "-g <group_id>";
 	} 

    rpc GetGameAutoTask ( GetGameAutoTaskReq ) returns ( GetGameAutoTaskResp ){
 		option( tlvpickle.CmdID ) = 68;
 		option( tlvpickle.OptString ) = "g:";
 		option( tlvpickle.Usage ) = "-g <game_id>";
 	} 

    rpc SetGameAutoTask ( SetGameAutoTaskReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 69;
 		option( tlvpickle.OptString ) = "g:l:t:";
 		option( tlvpickle.Usage ) = "-g <game_id> -l <url> -t <type>";
 	} 

    rpc RemoveGameAutoTask ( RemoveGameAutoTaskReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 70;
 		option( tlvpickle.OptString ) = "g:";
 		option( tlvpickle.Usage ) = "-g <game_id>";
 	} 

    rpc BatchCreateGameServer( BatchCreateGameServerReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 71;
 		option( tlvpickle.OptString ) = "g:n:p:";
 		option( tlvpickle.Usage ) = "-g <game_id> -n <server_name> -p <os_platform>";
 	}

	rpc AddGameTTag ( AddGameTTagReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 72;
 		option( tlvpickle.OptString ) = "g:t:r:";
 		option( tlvpickle.Usage ) = "-g <game_id> -t <tag_id> -r <rank>";
 	}
	
	rpc DelGameTTag ( DelGameTTagReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 74;
 		option( tlvpickle.OptString ) = "g:t:";
 		option( tlvpickle.Usage ) = "-g <game_id> -t <tag_id>";
 	}
	
	rpc GetGameTTagList ( GetGameTTagListReq ) returns ( GetGameTTagListResp ){
 		option( tlvpickle.CmdID ) = 76;
 		option( tlvpickle.OptString ) = "g:";
 		option( tlvpickle.Usage ) = "-g <game_id>";
 	}
	
	rpc GetGameRankListByTTag ( GetGameRankListByTTagReq ) returns ( GetGameRankListByTTagResp ){
 		option( tlvpickle.CmdID ) = 77;
 		option( tlvpickle.OptString ) = "t:o:n:";
 		option( tlvpickle.Usage ) = "-t <tag_id> -o <offset> -n <limit>";
 	}
	
	rpc GetGamesByTTag ( GetGameRankListByTTagReq ) returns ( GetGamesByTTagResp ){
 		option( tlvpickle.CmdID ) = 78;
 		option( tlvpickle.OptString ) = "t:o:n:";
 		option( tlvpickle.Usage ) = "-t <tag_id> -o <offset> -n <limit>";
 	}

	rpc GetLyGame( LyGameIdReq ) returns ( LyGameResp ){
		option( tlvpickle.CmdID ) = 79;									// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <ly_game_id>";
	}

	rpc SetGameZoneLotteryInfo(SetGameZoneLotteryInfoReq) returns(tlvpickle.SKBuiltinEmpty_PB){
		option( tlvpickle.CmdID ) = 80;									// 命令号
        option( tlvpickle.OptString ) = "g:s:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id> -s<is_enable>";		
	}

	rpc ModifyGuildPlayingGame(ModifyGuildPlayingGameReq) returns(tlvpickle.SKBuiltinEmpty_PB){
		option( tlvpickle.CmdID ) = 81;									// 命令号
        option( tlvpickle.OptString ) = "g:r:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_ids> -r<rank_type>";		
	}

	rpc GetGuildPlayingGame(tlvpickle.SKBuiltinEmpty_PB) returns(GetGuildPlayingGameResp){
		option( tlvpickle.CmdID ) = 82;									// 命令号
        option( tlvpickle.OptString ) = "";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";		
	}

	rpc UpdateGameCornerIcon(UpdateGameCornerIconReq) returns(tlvpickle.SKBuiltinEmpty_PB){
		option( tlvpickle.CmdID ) = 83;									// 命令号
        option( tlvpickle.OptString ) = "g:i:s:e:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g<gid> -i<icon> -s<start_time> -e<end_time>";		
	}

	rpc DelGameCornerIcon(DelGameCornerIconReq) returns(tlvpickle.SKBuiltinEmpty_PB){
		option( tlvpickle.CmdID ) = 84;									// 命令号
        option( tlvpickle.OptString ) = "g:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g<gid>";		
	}

	rpc GetGameCornerIconList(tlvpickle.SKBuiltinEmpty_PB) returns(GetGameCornerIconListResp){
		option( tlvpickle.CmdID ) = 85;									// 命令号
        option( tlvpickle.OptString ) = "";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";		
	}

	rpc GetGameCornerIconListAll(tlvpickle.SKBuiltinEmpty_PB) returns(GetGameCornerIconListResp){
		option( tlvpickle.CmdID ) = 86;									// 命令号
        option( tlvpickle.OptString ) = "";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";		
	}	

    rpc GetGameActAdList(tlvpickle.SKBuiltinEmpty_PB) returns(GetGameActAdListResp){
        option( tlvpickle.CmdID ) = 87;         // 命令号
        option( tlvpickle.OptString ) = "";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";         
    }

    rpc AddGameActAd(GameActAdEntry) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 88;         // 命令号
        option( tlvpickle.OptString ) = "i:m:l:";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i<id> -m<img> -l<url>";
    }

    rpc DelGameActAd(GameActAdReq) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 89;         // 命令号
        option( tlvpickle.OptString ) = "i:";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i<id>";       
    }

    rpc GetGameActAd(GameActAdReq) returns (GameActAdEntry)
    {
        option( tlvpickle.CmdID ) = 90;         // 命令号
        option( tlvpickle.OptString ) = "i:";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i<id>";      
    }
}

