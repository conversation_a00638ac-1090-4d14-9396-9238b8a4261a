syntax ="proto2";

import "common/tlvpickle/skbuiltintype.proto";
package presentCount;

message PresentCountReq
{
	required uint32 channel_id = 1;//房间id
	required bool is_off = 2;// true 打开 false 关闭
    repeated uint32 micr_users = 3;
}

message GetPresentCountByIdReq
{
    required uint32 channel_id = 1;
}

message GetPresentCountByIdResp
{
    required uint32 price = 1;
}

message PresentCountResp
{
}

//获得房间送礼统计开关状态
message GetPresentCountStateReq
{
    required uint32 channel_id = 1;
}

message MicsPresentCountInfo
{
    required uint32 uid = 1;
    required uint32 price = 2;
}

message GetPresentCountStateResp
{
    required bool state = 1;
    repeated MicsPresentCountInfo mics_present_count = 2;
}

service PresentCount
{
	option( tlvpickle.Magic ) = 15642;		// 服务监听端口号
	rpc SwitchPresentCount( PresentCountReq ) returns( PresentCountResp ){
		option( tlvpickle.CmdID ) = 1;								// 命令号
        option( tlvpickle.OptString ) = "x:u:o:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <channelId> -u <uid> -o <on:1,off:0>";								// 测试工具的命令号帮助
	}

    rpc GetPresentCountById( GetPresentCountByIdReq ) returns ( GetPresentCountByIdResp )
    {
        option ( tlvpickle.CmdID ) = 2;
        option ( tlvpickle.OptString ) = "x:u:";
        option (tlvpickle.Usage ) = "-x <channelId> -u <uid>";
    }

    rpc GetPresentCountState( GetPresentCountStateReq ) returns ( GetPresentCountStateResp )
    {
        option ( tlvpickle.CmdID ) = 3;
        option ( tlvpickle.OptString ) = "x:";
        option ( tlvpickle.Usage ) = "-x <channelId>";
    }
}

