syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package Mission;

// 用户任务状态
enum USER_MISSION_STATUS {
    IN_PROGRESS = 1;    // 进行中
    FINISHED = 2;       // 任务已经完成, 奖励未领取
    COLLECTED = 3;      // 已领取奖励
}

// 里程碑(MASK, 各枚举字段需要用位移计算)
enum MILESTONE {
    MILESTONE_NONE = 1;     // 无任务里程碑
    GREENER_COMPLETED = 2;  // 完成新手任务
}

// 用户进行中或已经完成的任务列表
message StUserMission {
    required uint32 mission_id = 1;         // 对应任务id
    required uint32 uid = 2;
    required uint32 start_time = 3;         // 任务开始的实际时间, 由missionlogicsvr传入
    optional uint32 expire_time = 4;        // 任务过期时间     
    optional uint32 bonus_expire_time = 5;  // 奖励领取过期时间
	required uint32 status = 6;	            // 任务状态
	required uint32 finish_count = 7;		// 当finish_count >= repeat_count时, 表示任务已经完成
    required string identifier = 8;         // 用户任务的惟一标识
	optional uint32 reward_count = 9;		// 已领奖次数
	optional uint32 accept_time = 10;		// 接受任务的时间, 暂时只有限时任务用到
}

// 获取任务配置以及用户任务列表
message GetUserMissionListReq {
}

message GetUserMissionListResp {
	repeated StUserMission user_mission_list = 1;
}

// 根据UserMissionIdentifier获取任务数据
message GetUserMissionByIdentifierReq {
    required string user_mission_identifier = 1;
}

message GetUserMissionByIdentifierResp {
    // 若不存在该字段, 表示该任务未被触发
    optional StUserMission user_mission = 1;
}

message GetUserMissionsByIdentifierListReq {
    repeated string user_mission_identifier_list = 1;
}

message GetUserMissionsByIdentifierListResp {
    repeated StUserMission user_mission_list = 2;
}

// 增加任务完成次数, 若不存在对应的任务, 则会创建任务并增加相应次数
message IncreaseUserMissionFinishCountReq {
   	required StUserMission user_mission = 1;
   	required uint32 finish_count = 2;
}

message IncreaseUserMissionFinishCountResp {
	required StUserMission user_mission = 1;
}

// 增加任务完成至次数, 若不存在对应的任务, 则会创建并写入相应次数
// 此接口只能增加完成次数不能减少 
message IncreaseUserMissionToFinishCountReq {
    required StUserMission user_mission = 1;
    required uint32 finish_count = 2;
	optional bool force_update = 3;	// 1.请求指定的完成次数比库中的次数还小也更新完成次数
}

// 如果请求指定的完成次数比库中的次数还小或相等, 则success=false; 否则为true
message IncreaseUserMissionToFinishCountResp {
    required bool success = 1;
    optional StUserMission user_mission = 2;    // 如果success=True才会赋值
}

message UpdateUserMissionStatusByIdentifierReq {
    required string user_mission_identifier = 1;
    required uint32 new_status = 2;
	optional uint32 add_reward_count = 3;
	optional uint32 force_update_status = 4;
}

message UpdateUserMissionStatusByIdentifierResp {
}

// 更新用户的成长里程碑
message UpdateUserMilestoneReq {
    required uint32 uid = 1;
    required uint32 new_milestone = 2;
}

message UpdateUserMilestoneResp {
    required bool success = 1;
    required uint32 milestone = 2;
}

message GetUserMilestoneReq {
    required uint32 uid = 1;
}

message GetUserMilestoneResp {
    required uint32 milestone = 1;
}

// 取用户任务
message GetLatestUserMissionReq {
    required uint32 uid = 1;
    required uint32 mission_id = 2;
}

message GetLatestUserMissionResp {
    optional StUserMission user_mission = 1;
}

// 更新用户任务
message UpdateUserMissionReq {
    required string user_mission_identifier = 1;    // 用户任务的惟一标识
    optional uint32 expire_time = 2;        // 任务过期时间     
    optional uint32 bonus_expire_time = 3;  // 奖励领取过期时间
    optional uint32 status = 4;             // 任务状态表示任务已经完成
	optional uint32 accept_time = 5;		// 任务接受时间
}

message UpdateUserMissionResp {
    required bool updated = 1;
}

message GetMissionTotalFinishCountReq {
    required uint32 uid = 1;
    required uint32 mission_id = 2;
}

message GetMissionTotalFinishCountResp {
    required uint32 total_finish_count = 1;
}

message GetMissionTotalCollectCountReq {
    required uint32 uid = 1;
    required uint32 mission_id = 2;
}

message GetMissionTotalCollectCountResp {
    required uint32 total_collect_count = 1;
}


/********************* 限时任务数据 begin *********************/
// 数据来源
enum DAY_OPER_TYPE {
	GAME_LOGIN = 1;
	GAME_RECHARGE = 2;
	TT_LOGIN = 3;
	GUILD_CHECK_IN = 4;
}

// 数据来源
enum RECHARGE_SOURCE {
	GAME = 1;
	HAPPY_CENTER = 2;
}

// 用户每日连续行为（如游戏连续登陆x天）
message RecordUserContinuousDayOperReq {
	required uint32 uid = 1;
	required uint32 oper_type = 2; // DAY_OPER_TYPE
	optional uint64 ly_game_id = 3;
	optional uint32 force_add_accum = 4;	// add accum days
}

message GetUserContinuousDayOperReq {
	required uint32 uid = 1;
	required uint32 oper_type = 2; // DAY_OPER_TYPE
	optional uint64 ly_game_id = 3;
}

message GetUserContinuousDayOperResp {
	required uint32 last_oper_at = 1;
	required uint32 oper_count = 2;	// 连续天数
	required uint32 accum_oper_count = 3;		// 累计天数
}

message ClearUserContinuousDayOperReq {
	required uint32 oper_type = 1; // DAY_OPER_TYPE
	optional uint64 ly_game_id = 2;
}

// 用户充值行为
message RecordUserRechargeReq {
	required uint32 uid = 1;
	required uint32 recharge_source = 2;	// RECHARGE_SOURCE
	required uint32 recharge_penny = 3;		// 充值金额，单位：分
	optional uint64 ly_game_id = 4;
}

message GetUserRechargeReq {
	required uint32 uid = 1;
	required uint32 recharge_source = 2;	// RECHARGE_SOURCE
	optional uint64 ly_game_id = 3;
}

message GetUserRechargeResp {
	required uint32 last_recharge_at = 1;
	required uint32 accum_recharge_penny = 2;	// 累计充值金额，单位：分
}

message ClearUserRechargeReq {
	required uint32 recharge_source = 1; // RECHARGE_SOURCE
	optional uint64 ly_game_id = 2;
}

/********************* 限时任务数据 end *********************/

service Mission {
	option( tlvpickle.Magic ) = 15010;		// 服务监听端口号	

	// 获取用户的任务列表
	rpc GetUserMissionList( GetUserMissionListReq ) returns( GetUserMissionListResp ) {
		option( tlvpickle.CmdID ) = 2;			// 命令号
        option( tlvpickle.OptString ) = "u:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <user_id>";    		// 测试工具的命令号帮助
	}

	// 单查用户任务
    rpc GetUserMissionByIdentifier( GetUserMissionByIdentifierReq ) returns( GetUserMissionByIdentifierResp ) {
        option( tlvpickle.CmdID ) = 3;			// 命令号
        option( tlvpickle.OptString ) = "";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    		// 测试工具的命令号帮助
    }

    // 批量查用户任务
    rpc GetUserMissionsByIdentifierList( GetUserMissionsByIdentifierListReq ) returns( GetUserMissionsByIdentifierListResp ) {
        option( tlvpickle.CmdID ) = 4;			// 命令号
        option( tlvpickle.OptString ) = "";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    		// 测试工具的命令号帮助
    }

    // 增加任务完成次数
    rpc IncreaseUserMissionFinishCount( IncreaseUserMissionFinishCountReq ) returns( IncreaseUserMissionFinishCountResp ) {
    	option( tlvpickle.CmdID ) = 5;			// 命令号
        option( tlvpickle.OptString ) = "";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    		// 测试工具的命令号帮助
    }

    rpc UpdateUserMissionStatusByIdentifier( UpdateUserMissionStatusByIdentifierReq ) returns( UpdateUserMissionStatusByIdentifierResp ) {
        option( tlvpickle.CmdID ) = 6;          // 命令号
        option( tlvpickle.OptString ) = "";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";         // 测试工具的命令号帮助
    }

    rpc IncreaseUserMissionToFinishCount( IncreaseUserMissionToFinishCountReq ) returns( IncreaseUserMissionToFinishCountResp ) {
        option( tlvpickle.CmdID ) = 7;          // 命令号
        option( tlvpickle.OptString ) = "";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";         // 测试工具的命令号帮助
    }

    rpc UpdateUserMilestone( UpdateUserMilestoneReq ) returns( UpdateUserMilestoneResp ) {
        option( tlvpickle.CmdID ) = 8;          // 命令号
        option( tlvpickle.OptString ) = "u:m:";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <milestone>";         // 测试工具的命令号帮助          
    }

    rpc GetUserMilestone( GetUserMilestoneReq ) returns( GetUserMilestoneResp ) {
        option( tlvpickle.CmdID ) = 9;          // 命令号
        option( tlvpickle.OptString ) = "u:";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";         // 测试工具的命令号帮助    
    }

    rpc GetLatestUserMission( GetLatestUserMissionReq ) returns( GetLatestUserMissionResp ) {
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "u:m:";
        option( tlvpickle.Usage ) = "-u <uid> -m <mission_id>";
    }

    rpc UpdateUserMission( UpdateUserMissionReq ) returns( UpdateUserMissionResp ) {
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "u:m:e:b:s:";
        option( tlvpickle.Usage ) = "-u <uid> -m <mission_id> -e <expire> -b <bonus_expire> -s <status>";
    }

    rpc GetMissionTotalFinishCount( GetMissionTotalFinishCountReq ) returns( GetMissionTotalFinishCountResp ) {
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "u:m:";
        option( tlvpickle.Usage ) = "-u <uid> -m <mission_id>";
    }
	
	rpc GetMissionTotalCollectCount( GetMissionTotalCollectCountReq ) returns( GetMissionTotalCollectCountResp ) {
        option( tlvpickle.CmdID ) = 27;
        option( tlvpickle.OptString ) = "u:m:";
        option( tlvpickle.Usage ) = "-u <uid> -m <mission_id>";
    }
}

