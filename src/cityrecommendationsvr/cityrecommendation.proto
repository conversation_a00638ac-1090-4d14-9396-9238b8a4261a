syntax="proto2";
import "common/tlvpickle/skbuiltintype.proto";

package cityrecommendation;

//------------------------------------------同城推荐---------------------------------------------//

///////////
//添加用户地理位置信息(废弃)
message AddAddrReflectionReq
{
	required uint32 uid=1;				//uid
	required uint32 ad_code=2;			//地区编号
	optional string province=3;			//省份
	optional string city=4;				//城市
}

message AddAddrReflectionResp
{
}

//(废弃)
message OperCityUserFilterReq
{
	required uint32 uid=1;						//用户uid
    required uint32 sex=2;						//用户性别
	required uint32 filter_type=3;				//FilterType
	required uint32 filter_value=4;				//类型值：游戏ID或时间戳，为0时只上报用户位置
	required uint32 ad_code=5;					//地区编号（lbssvr中的ad_code）
	required string city=6;						//城市
	optional uint32 m_time=7;					//游戏类型使用
	optional uint32 oper_type=8;				//OperType (增加或删除)	
}
message OperCityUserFilterResp
{
}
//////////////

//类型
enum FilterType
{
 	UnkownRecommend=0;
	GameRecommend=1;					//游戏类型
	FeedRecommend=2;					//动态流
}

enum OperType
{
	AddRecommend=0;						//增加推荐
	DeleteRecommend=1;					//删除推荐
}

message BatchOperCityUserFilterReq
{
	required uint32 uid=1;						//uid
    required uint32 sex=2;						//性别
	required uint32 filter_type=3;				//类型
	repeated uint32 filter_value_list=4;		//类型值
	required uint32 ad_code=5;					//lbssvr ad_code
	required string city=6;						//城市
	optional uint32 m_time=7;					//游戏类型使用
	optional uint32 oper_type=8;				//OperType	
}

message BatchOperCityUserFilterResp
{
}


//根据筛选条件获取同城推荐
message RecommendMsg
{
	required uint32 uid=1;			
	required string city=2;
}

message GetCityUserRecommendReq
{	
	required uint32 uid=1;				//获取推荐的用户ID
	required uint32 sex=2;				//获取指定性别用户的推荐
	required uint32 filter_type=3;		//指定类型
	required uint32 filter_value=4;		//类型值
	optional uint32 num=5;				//拉取数量
	repeated uint32 friend_list=6;		//好友列表 
	optional uint32 start_time=7;		//开始时间
	optional uint32 end_time=8;			//结束时间
}

message GetCityUserRecommendResp
{
	repeated RecommendMsg recommend_msg_list=1;
}

service CityRecommendation 
{
	option( tlvpickle.Magic ) = 15569;														 			// 服务监听端口号

	rpc  AddAddrReflection( AddAddrReflectionReq) returns(  AddAddrReflectionResp)
	{
		option( tlvpickle.CmdID ) = 1;														 			// 命令号
        option( tlvpickle.OptString ) = "u:a:p:m:";											 			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid> -a<ad_code> -p<province> -m<city>";    					// 测试工具的命令号帮助
	}
	rpc  OperCityUserFilter( OperCityUserFilterReq) returns(OperCityUserFilterResp)
	{
		option( tlvpickle.CmdID ) = 2;														 			// 命令号
        option( tlvpickle.OptString ) = "u:s:p:t:y:a:i:l:o:";											 		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid> -s<sex> -p<filter_type> -t<filter_value> -a<ad_code> -i<city> -l<m_time> -o<oper_type> ";    	// 测试工具的命令号帮助
	}
	rpc  GetCityUserRecommend(GetCityUserRecommendReq) returns(GetCityUserRecommendResp)
	{
		option( tlvpickle.CmdID ) = 3;														 			// 命令号
        option( tlvpickle.OptString ) = "u:s:n:p:t:e:";											 		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid> -s<sex> -n<num> -p<filter_type> -t<filter_value> -e<ex_uid_list ...>";    		// 测试工具的命令号帮助
	}	
	rpc  BatchOperCityUserFilter( BatchOperCityUserFilterReq) returns(BatchOperCityUserFilterResp)
	{
		option( tlvpickle.CmdID ) = 4;														 			// 命令号
        option( tlvpickle.OptString ) = "u:s:p:t:y:a:i:l:o:";											 		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -u<uid> -s<sex> -p<filter_type> -t<filter_value...> -a<ad_code> -i<city> -l<m_time> -o<oper_type> ";    	// 测试工具的命令号帮助
	}
}
