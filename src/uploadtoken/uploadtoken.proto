syntax = "proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package uploadtoken;

enum TokenType {
  CHAT_1V1 = 0;
}

enum UploadPlatform {
  QINIU = 0;
}

message GenTokenReq {
  TokenType type = 1;
  string scope = 2;
  int32 ttl = 3;
  UploadPlatform platform = 4; // 预设，没使用
}

message GenTokenResp {
  bytes token = 1;
}

service uploadtoken {
  option(tlvpickle.Magic) = 15702;

  rpc GenToken(GenTokenReq) returns(GenTokenResp) {
    option(tlvpickle.CmdID) = 1;
    option(tlvpickle.OptString) = "t:s:l:";
    option(tlvpickle.Usage) = "-t type<chat:0> -s <scope> -l <ttl>";
  }
}