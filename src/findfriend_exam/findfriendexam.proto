syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package findfriendexam;


// 添加用户测试
message UpdateUserFindFriendExamReq
{
    uint32 uid = 1;
    uint32 exam_id = 2;                 // 测试大类
    uint64 scores = 3;                  // 测试分数
}
message UpdateUserFindFriendExamResp
{

}


message StUserExamResult
{
    uint32 exam_id = 1;
    uint32 game_id = 2;
    string exam_name = 3;         // 测试的分类名称
    uint64 scores = 4;
    string tag_name = 5;          // 测试结果的分类标签
    string img = 6;               // 测试结果的分数
    string desc = 7;              // 测试结果的分类标签对应的描述
    repeated uint32 score_list = 8;
}

// 获取用户测试结果
message GetUserFindFriendExamReq
{
    uint32 uid = 1;
}
message GetUserFindFriendExamResp
{
    StUserExamResult exam_result = 1;
}

// 检查用户是否测试
message CheckUserFindFriendExamReq
{
    uint32 uid = 1;
}

message CheckUserFindFriendExamResp
{
    uint32 exam_id = 1;
    uint64 scores = 2;
}

//***************   题库相关api *****************
message FindfriendExamSummary
{
    uint32 exam_id = 1;      
    uint32 game_id = 2;     //如果和游戏有关则
    string name = 3;        //题库名
    string icon = 4;        //题库图标
    uint32 status = 5;       //题库发布状态，0-未发布，1已发布
    uint32 rank = 6;
    repeated string question_type_names = 7; //题目维度名
}

message FindfriendExamIdReq
{
    uint32 id = 1;
}

message GetFindfriendExamSummaryListReq
{
    bool include_invalid = 1;
}

message GetFindfriendExamSummaryListResp
{
    repeated FindfriendExamSummary exam_list = 1;
}

message FindfriendExamQuestion
{
    uint32 id = 1;
    uint32 exam_id = 2;             //题库id
    uint32 question_type = 3;   //题目维度idx
    string question = 4;            //题目
    repeated string options = 5;    //选项
}

message GetFindfriendExamQuestionListResp
{
    repeated FindfriendExamQuestion question_list = 1;
}

message FindfriendExamResult
{
    uint32 id = 1;
    uint32 exam_id = 2;     //题库id
    uint64 result = 3;      //选择结果
    string tag = 4;         //标签名
    string desc = 5;        //介绍
    string img = 6;         //图片url
}

message GetFindfriendExamResultReq
{
    uint32 exam_id = 1;
    uint64 result = 2;
}

message GetFindfriendExamResultListResp
{
    repeated FindfriendExamResult result_list = 1;
}


service FindFriendExam {
	option( tlvpickle.Magic ) = 15639;		// 服务监听端口号

    rpc UpdateUserFindFriendExam( UpdateUserFindFriendExamReq ) returns( UpdateUserFindFriendExamResp ){
		option( tlvpickle.CmdID ) = 1;										
        option( tlvpickle.OptString ) = "u:e:s:";							
        option( tlvpickle.Usage ) = "-u <uid> -e <exam_id> -s <scores>";
	}

    rpc GetUserFindFriendExam( GetUserFindFriendExamReq ) returns( GetUserFindFriendExamResp ){
		option( tlvpickle.CmdID ) = 2;										
        option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid>";
	}

    rpc CheckUserFindFriendExam( CheckUserFindFriendExamReq ) returns( CheckUserFindFriendExamResp ){
		option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid>";
	}

    rpc SetFindfriendExamSummary(FindfriendExamSummary) returns(tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "i:g:n:o:s:d:r:";
        option( tlvpickle.Usage ) = "-i<id> -g<game_id> -n<name> -o<icon> -s<status> -d<question_type_names> -r<rank>";
    }
    rpc DelFindfriendExamSummary(FindfriendExamIdReq) returns(tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "i:";
        option( tlvpickle.Usage ) = "-i<id>";
    }
    rpc GetFindfriendExamSummary(FindfriendExamIdReq) returns(FindfriendExamSummary){
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "i:";
        option( tlvpickle.Usage ) = "-i<id>";
    }
    rpc GetFindfriendExamSummaryByGameId(FindfriendExamIdReq) returns(FindfriendExamSummary){
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "i:";
        option( tlvpickle.Usage ) = "-i<game_id>";
    }
    rpc GetFindfriendExamSummaryList(GetFindfriendExamSummaryListReq) returns(GetFindfriendExamSummaryListResp){
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc SetFindfriendExamQuestion(FindfriendExamQuestion) returns(tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "i:e:t:q:o:";
        option( tlvpickle.Usage ) = "-i<id> -e<exam_id> -t<question_type> -q<question> -o<optons>";
    }
    rpc GetFindfriendExamQuestionList(FindfriendExamIdReq) returns(GetFindfriendExamQuestionListResp){
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "i:";
        option( tlvpickle.Usage ) = "-i<exam_id>";
    }
    rpc GetFindfriendExamQuestionListForUser(FindfriendExamIdReq) returns(GetFindfriendExamQuestionListResp){
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "i:";
        option( tlvpickle.Usage ) = "-i<exam_id>";
    }

    rpc SetFindfriendExamResult(FindfriendExamResult) returns(tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "i:e:r:t:d:m:";
        option( tlvpickle.Usage ) = "-i<id> -e<exam_id> -r<result> -t<tag> -d<desc> -m<img>";
    }
    rpc GetFindfriendExamResultList(FindfriendExamIdReq) returns(GetFindfriendExamResultListResp){
        option( tlvpickle.CmdID ) = 13;
        option( tlvpickle.OptString ) = "i:";
        option( tlvpickle.Usage ) = "-i<exam_id>";
    }

    rpc DelFindfriendExamQuestion(FindfriendExamIdReq) returns(tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 14;
        option( tlvpickle.OptString ) = "i:";
        option( tlvpickle.Usage ) = "-i<id>";
    }    
    rpc DelFindfriendExamResult(FindfriendExamIdReq) returns(tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 15;
        option( tlvpickle.OptString ) = "i:";
        option( tlvpickle.Usage ) = "-i<id>";
    }

    rpc BatSetFindfriendExamQuestion(GetFindfriendExamQuestionListResp) returns(tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 16;
        option( tlvpickle.OptString ) = "i:e:t:q:o:";
        option( tlvpickle.Usage ) = "-i<id> -e<exam_id> -t<question_type> -q<question> -o<optons>";
    }
    rpc BatSetFindfriendExamResult(GetFindfriendExamResultListResp) returns(tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 17;
        option( tlvpickle.OptString ) = "i:e:r:t:d:m:";
        option( tlvpickle.Usage ) = "-i<id> -e<exam_id> -r<result> -t<tag> -d<desc> -m<img>";
    }
    rpc GetFindfriendExamQuestion(FindfriendExamIdReq) returns(FindfriendExamQuestion){
        option( tlvpickle.CmdID ) = 18;
        option( tlvpickle.OptString ) = "i:";
        option( tlvpickle.Usage ) = "-i<id>";
    }     
}
