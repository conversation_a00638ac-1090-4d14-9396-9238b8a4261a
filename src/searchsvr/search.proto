syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";
import "sqlmodel.proto";

// namespace
package search;		


// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list


// 加索引请求
message UpdateIndexReq {
	required string index = 1;
	required string primary_key = 2;
	required bytes binData = 3;
	required uint32 type = 4;
}

// 加索引应答
message UpdateIndexResp {
}

message ResultRow {
	required string primary_key = 1;
	required bytes binData = 2;
}

message SearchByIndexReq {
	required string index = 1;
	required uint32 type = 3;
}

message SearchByIndexResp {
	repeated ResultRow row_list = 1;
}

// 批量加索引请求
message BatchUpdateIndexReq {
	repeated UpdateIndexReq idx = 1;
}


//
//支持搜素引擎sphinx
//
message AddDocumentReq{
    required sqlmodel.InsertReq req = 1;
}

message AddDocumentRsp{
}

message MatchItem{
    required string keyword = 1;
    repeated sqlmodel.Field field_list = 2;
}
message MatchSubclause {
    repeated MatchItem match = 1;
}

message DeleteDocumentReq{
    required sqlmodel.DeleteReq req = 1;
    optional string keyword = 2;
    optional MatchSubclause match = 3;
}

message DeleteDocumentRsp{
}

message ModifyDocumentReq{
    required sqlmodel.UpdateReq req = 1;
    optional string keyword = 2;
    optional MatchSubclause match = 3;
}

message ModifyDocumentRsp{
}

//查询
message SearchDocumentReq{
    required sqlmodel.SelectReq req = 1;
    optional string keyword = 2;
    optional MatchSubclause match = 3;
}
message SearchResultRow {
    repeated bytes col_val = 1;
}
message SearchResult{
    repeated SearchResultRow row_list = 1;
}
message SearchDocumentRsp{
    optional SearchResult result = 1;
}
//
message ReplaceDocumentReq{
    required sqlmodel.ReplaceReq req = 1;
}
message ReplaceDocumentRsp{
}

message GetDocumentCountReq {
    required sqlmodel.CountReq req = 1;
    optional MatchSubclause match = 2;
}
message GetDocumentCountRsp {
    optional uint32 count = 1;
}

message SearchCelebrityUserByNameReq
{
    required string name = 1;
}

message SearchCelebrityUserByNameResp
{
    repeated uint32 uid_list = 1;
}

//////////////////
service Search {
    option( tlvpickle.Magic ) = 14950;		// 服务监听端口号

	// 上传文件
    rpc UpdateIndex(UpdateIndexReq) returns(UpdateIndexResp) {
        option( tlvpickle.CmdID ) = 1;												// 命令号
        option( tlvpickle.OptString ) = "i:p:b:";										// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <index> -p <primary_key> -b <binData>";					// 测试工具的命令号帮助
    }

    rpc SearchByIndex(SearchByIndexReq) returns(SearchByIndexResp) {
        option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "i:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <index>";							// 测试工具的命令号帮助
    }

    rpc BatchUpdateIndex(BatchUpdateIndexReq) returns(UpdateIndexResp) {
        option( tlvpickle.CmdID ) = 3;												// 命令号
        option( tlvpickle.OptString ) = "";										// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";					// 测试工具的命令号帮助
    }

    //支持搜素引擎sphinx
    rpc AddDocument( AddDocumentReq ) returns ( AddDocumentRsp ) {
        option( tlvpickle.CmdID ) = 20;
        option( tlvpickle.OptString ) = "s:";
        option( tlvpickle.Usage ) = "-s <sql>";
    }
    rpc DeleteDocument ( DeleteDocumentReq ) returns ( DeleteDocumentRsp ){
        option( tlvpickle.CmdID ) = 21;
        option( tlvpickle.OptString ) = "s:";
        option( tlvpickle.Usage ) = "-s <sql>";
    }
    rpc ModifyDocument ( ModifyDocumentReq ) returns ( ModifyDocumentRsp ){
        option( tlvpickle.CmdID ) = 22;
        option( tlvpickle.OptString ) = "s:";
        option( tlvpickle.Usage ) = "-s <sql>";
    }
    rpc SearchDocument( SearchDocumentReq ) returns ( SearchDocumentRsp ){
        option( tlvpickle.CmdID ) = 23;
        option( tlvpickle.OptString ) = "s:";
        option( tlvpickle.Usage ) = "-s <sql>";
    }
    rpc ReplaceDocument( ReplaceDocumentReq ) returns ( ReplaceDocumentRsp ) {
        option( tlvpickle.CmdID ) = 24;
        option( tlvpickle.OptString ) = "s:";
        option( tlvpickle.Usage ) = "-s <sql>";
    }
    rpc GetDocumentCount( GetDocumentCountReq ) returns ( GetDocumentCountRsp ) {
        option( tlvpickle.CmdID ) = 25;
        option( tlvpickle.OptString ) = "s:";
        option( tlvpickle.Usage ) = "-s <sql>";
    }
    
    rpc SearchCelebrityUserByName( SearchCelebrityUserByNameReq ) returns ( SearchCelebrityUserByNameResp ) {
        option( tlvpickle.CmdID ) = 26;
        option( tlvpickle.OptString ) = "n:";
        option( tlvpickle.Usage ) = "-n <name>";
    }
}



