syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";		

// namespace
package ttpush;									

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

//////////////////
message NotifyReq {
	required uint32 uid = 1;
	required uint32 type = 2;
}

message NotifyResp {
}

//////////////////
message KickoutReq {
	required uint32 uid = 1;
	required string why_kick_me = 2;
}

message KickoutResp {
}

message PushDataReq {
	required uint32 uid = 1;
	required bytes bin_msg = 2;
}

message PushDataResp {
}

message BatchPushReq {
	repeated uint32 uid_list = 1;
	required bytes bin_msg = 2;
}

message BatchPushResp {
}

message UserGroupRelationRegistEvent {
	required uint32 uid = 1;
	required uint32 regeventtype = 2;
	repeated uint32 group_id_list = 3;
	optional uint32 client_id = 4;
}

message RegEventReq {
	required UserGroupRelationRegistEvent event = 1;
}

message RegEventResp {
}

message BatchRegEventReq {
	repeated UserGroupRelationRegistEvent event_list = 1;
}

message BatchRegEventResp {
}

message PushByGroupIDReq {
	required uint32 group_id = 1;
	required bytes bin_msg = 2;
}

message PushByGroupIDResp {
}

message NotifyByGroupIDReq {
	required uint32 group_id = 1;
	required uint32 type = 2;
}

message NotifyByGroupIDResp {
}

message PushByChannelIDReq {
	required uint32 channel_id = 1;
	required bytes bin_msg = 2;
}

message PushByChannelIDResp {
}

//////////////////
service TTPush {
    option( tlvpickle.Magic ) = 15230;		// 服务监听端口号

    rpc Notify(NotifyReq) returns(NotifyResp) {
        option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "u:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t <type>";			// 测试工具的命令号帮助
    }

    rpc Kickout(KickoutReq) returns(KickoutResp) {
        option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "u:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -s <why_kick_me>";			// 测试工具的命令号帮助
    }
    
    rpc PushData(PushDataReq) returns(PushDataResp) {
        option( tlvpickle.CmdID ) = 3;									// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -b <bin_msg>";						// 测试工具的命令号帮助
    }
    
    rpc BatchPush(BatchPushReq) returns(BatchPushResp) {
        option( tlvpickle.CmdID ) = 4;									// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid1,uid2...> -b <bin_msg>";						// 测试工具的命令号帮助
    }
    
    rpc RegEvent(RegEventReq) returns(RegEventResp) {
        option( tlvpickle.CmdID ) = 5;									// 命令号
        option( tlvpickle.OptString ) = "u:t:g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t <type> -g <group1,group2...>";						// 测试工具的命令号帮助
    }
    
    rpc BatchRegEvent( BatchRegEventReq ) returns ( BatchRegEventResp ) {
        option( tlvpickle.CmdID ) = 6;									// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";						// 测试工具的命令号帮助
    }
    
    rpc PushByGroupID( PushByGroupIDReq ) returns ( PushByGroupIDResp ) {
        option( tlvpickle.CmdID ) = 7;									// 命令号
        option( tlvpickle.OptString ) = "g:b:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <group_id> -b <bin_msg>";						// 测试工具的命令号帮助
    }
    
    rpc NotifyByGroupID( NotifyByGroupIDReq ) returns ( NotifyByGroupIDResp ) {
        option( tlvpickle.CmdID ) = 8;								// 命令号
        option( tlvpickle.OptString ) = "g:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <group_id> -t <type>";								// 测试工具的命令号帮助
    }
    
    rpc PushByChannelID( PushByChannelIDReq ) returns ( PushByChannelIDResp ) {
        option( tlvpickle.CmdID ) = 9;								// 命令号
        option( tlvpickle.OptString ) = "c:b:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-c <chid> -b <bin_msg>";								// 测试工具的命令号帮助
    }
    
}