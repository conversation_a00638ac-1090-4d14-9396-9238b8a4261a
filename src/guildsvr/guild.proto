syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Guild;

enum E_GUILD_ROLE_TYPE
{
	E_GUILD_ROLE_OWNER = 1;
	E_GUILD_ROLE_VICEOWNER = 2;
	E_GUILD_ROLE_NOMAL = 3;
}

enum E_GUILD_CHECK_IN_RANK_TYPE
{
	E_GUILD_CHECK_IN_TIME = 0;
	E_GUILD_CHECK_IN_COUNT = 1;
}

enum E_OPT_INVALID_TYPE 
{
	CHECK_IN = 1;		// 签到
	DONATE = 2;			// 捐献
	//xxx = 4;
}



message CreateGuildReq
{
	required string name = 1;
	required string intro = 2;
	required string prefix = 3;
}

message CreateGuildResp
{
	required uint32 guild_id = 1;
}

message CreateSpecialGuildReq
{
    required uint32 special_guild_id = 1;
    required string name = 2;
    required string desc = 3;
    required uint32 owner_uid = 4;
}

message UpdateGuildReq{
	required uint32 guild_id = 1;
	optional string name	 = 2;			// 公会名称
	optional string intro 	 = 3;				// 简介
    optional string prefix = 4;		// 公会马甲
    optional string manifesto = 5;
}

message GuildIdReq
{
	required uint32 guild_id = 1;
    optional bool getAll = 2;
}

message GuildResp
{
	required uint32 guild_id 	= 1;
	required uint32 short_id 	= 2;
	required string name 		= 3;
	required string intro 		= 4;
	required uint32 member_count = 5;
	required uint32 main_group = 6;
	required uint32 creator		= 7;
	required uint32 owner 		= 8;
	required bool business		= 9;
	required uint32 created_at = 10;
	required uint32 game_count = 11;
	required string prefix		= 12;
	required string manifesto	= 13;
	required bool need_verify	= 14;
	optional uint32 guild_game_lv = 15;
	optional uint32 is_delete = 16;
	optional uint32 need_verify_v2	= 17;//真实值
}

message SetShortIdReq
{
	required uint32 guild_id	= 1;
	required uint32 short_id	= 2;
}

message CheckNameExistReq
{
	required string name = 1;
}

message CheckNameExistResp
{
	required bool exist = 1;
}

message GuildAddGameReq
{
	required uint32 guild_id = 1;
	required uint32 game_id  = 2;
	required string main_group_name = 3;
}

message GuildAddGameResp{
	required uint32 group_id = 1;
}

message GuildGame
{
	required uint32 game_id = 1; // 游戏id
	required string url = 2;	 // 游戏下载url
	required bool use_custom_url = 3;	// 是否使用自定义链接
	required string download_desc = 4;	// 自定义下载链接说明
	required string pkg_size = 5;		// 自定义包大小
	optional uint32 is_delete = 6;		// 游戏是否已经删除
	optional uint32 download_times = 7;	// 游戏下载次数
}

message GetGuildGameListResp
{
	repeated GuildGame games = 9;
}


message CheckGuildChairmanReq
{
	required uint32 guild_id = 1;
	required uint32 uid		 = 2;
}

message CheckGuildChairmanResp
{
	required bool is_chairman = 1;
}

message GetGuildMemberReq{
	required uint32 uid = 1;
	required uint32 guild_id = 2;
}

message GuildMemberResp{
	required uint32 uid = 1;
	required uint32 role = 2;
	required string remark = 3;
	required uint32 weight = 4;
	required uint32 permission = 5;
	optional uint32 join_at = 6;
	optional string title = 7;
	optional uint32 opt_invalid = 8;	// E_OPT_INVALID_TYPE
}

message GroupMemberResp{
	required uint32 uid = 1;
	required uint32 role = 2;
	required string remark = 3;
	required uint32 weight = 4;
	required string group_card = 5;
	required bool   group_mute = 6;
	required uint32 joined_at = 7;
	optional uint32 recv_msg_opt = 8;
}


message GetGroupMemberListReq{
	required uint32 group_id = 1;
	required uint32 offset = 2;
	required uint32 page_size = 3;
	required bool only_admin = 4;
	optional uint32 guild_id = 5;
	optional bool is_all_duty = 6; // 在only_admin=true且guild_id>0的情况下, 获取的群管理员列表，是否需要包含有公会权限的群成员
}

message GroupMemberListResp{
	repeated GroupMemberResp members = 1;
}

message GetGroupMembersByUidsReq{
	required uint32 group_id = 1;
	repeated uint32 uids = 2;
}

message GetGroupMemberReq{
	required uint32 group_id = 1;
	required uint32 uid = 2;
}

message GetGuildMemberListByUidsReq{
	repeated uint32 uids = 1;
	required uint32 guild_id = 2;
}

message GetGuildMemberListReq{
	required uint32 guild_id = 1;
	required bool only_admin = 4;
	optional bool skip_admin = 5;
}

message GuildMemberListResp{
	repeated GuildMemberResp members = 1;
}

message GetGuildMemberListSegmentReq{
    required uint32 guild_id = 1;
    required uint32 offset   = 2;
    required uint32 limit    = 3;
}

message GetGuildMemberListSegmentResp{
    repeated GuildMemberResp members = 1;
}


// 记录需要管理员批准的入会申请
message ApplyGuildReq{
    required uint32 uid = 1;
    required uint32 guild_id = 2;
    required string verify = 3;
    required uint32 seq = 4;
    optional uint32 source_type = 5;      // 记录申请发起的来源 定义在guild_.proto文件的 GuildJoinApplySourceType
    optional uint32 source_type_id = 6;   // source_type 对应的ID 如果source_type == GUILD_JOIN_APPLY_SOURCE_RECRUIT 那么这里需要填写招募对应的招募ID
}

message ApplyGuildResp{
	required uint32 apply_id = 1;
}

// 记录需要管理员批准的入群申请
message ApplyGroupReq{
	required uint32 uid = 1;
	required uint32 group_id = 2;
	required string verify = 3;
	required uint32 seq = 4;
}

message ApplyGroupResp{
	required uint32 apply_id = 1;
}

// 根据申请ID 获取需要管理员批准的入会申请详情
message GetGuildApplyReq{
	required uint32 uid = 1;
	required uint32 apply_id = 2;
}

message GuildApplyResp{
	required uint32 apply_id = 1;
	required uint32 uid = 2;
	required uint32 guild_id = 3;
	required string verify = 4;
	required uint32 status = 5;
	required uint32 apply_at = 6;
	required uint32 reviewer = 7;
	required uint32 reviewed_at = 8;
	required uint32 confirm_at = 9;
	required uint32 seq = 10;
	required uint32 msg_send_success = 11;
	optional uint32 source_type = 12;      // 记录申请发起的来源 定义在guild_.proto文件的 GuildJoinApplySourceType
	optional uint32 source_type_id = 13;   // source_type 对应的ID 如果source_type == GUILD_JOIN_APPLY_SOURCE_RECRUIT 那么这里需要填写招募对应的招募ID
}

// 根据申请ID 获取需要管理员批准的入群申请详情
message GetGroupApplyReq{
	required uint32 uid = 1;
	required uint32 apply_id = 2;
}

message GroupApplyResp{
	required uint32 apply_id = 1;
	required uint32 uid = 2;
	required uint32 group_id = 3;
	required string verify = 4;
	required uint32 status = 5;
	required uint32 apply_at = 6;
	required uint32 reviewer = 7;
	required uint32 reviewed_at = 8;
	required uint32 seq = 9;
}

message GroupApply {
	required uint32 apply_id = 1;
	required uint32 uid = 2;
	required uint32 group_id = 3;
	required string verify = 4;
	required uint32 status = 5;
	required uint32 apply_at = 6;
	required uint32 reviewer = 7;
	required uint32 reviewed_at = 8;
	required uint32 seq = 9;
}

message GetGroupApplyByGroupIdReq {
	required uint32 uid = 1;
	required uint32 group_id = 2;
}

message GetGroupApplyByGroupIdResp {
	optional GroupApply group_apply = 1;
}


message ReviewApplyGuildReq{
	required uint32 reviewer = 1;
	required uint32 apply_uid = 2;
	required uint32 apply_id = 3;
	required bool agree = 4;
}

message ReviewApplyGroupReq{
	required uint32 reviewer = 1;
	required uint32 apply_uid = 2;
	required uint32 apply_id = 3;
	required bool agree = 4;
}



message RemoveGuildMemberReq{
	required uint32 guild_id = 1;
	required uint32 uid = 2;
	required uint32 quit_type = 3;
}

message GetUserGuildApplyListReq{
	required uint32 uid = 1;
}

message GetUserGuildApplyListResp{
	repeated GuildApplyResp guild_apply_list = 1;
}

message AddGroupMemberReq{
	required uint32 group_id = 1;
	required uint32 uid = 2;
	required uint32 weight = 3;
}

message GetGroupReq{
	required uint32 group_id = 1;
}

message TGroupExtra {
    enum TYPE {
        GAME_HOBBY = 1;     // 游戏兴趣群组, 缺省值
        GAME_OFFICIAL = 2;  // 游戏的官方T群
    }
	required string group_desc = 1;
	required string city_code = 2;
	required string city_name = 3;
	required uint32 game_id = 4;
    optional uint32 tgroup_type = 5;        // T群的类型
    optional uint32 level = 6;
}

message GroupResp{
	required uint32 group_id = 1;
	required uint32 guild_id = 2;
	required uint32 game_id  = 3;
	required uint32 group_type = 4;
	required string name	 = 5;
	required uint32 member_count = 6;
	required uint32 creator	 = 7;
	required uint32 owner	 = 8;
	required uint32 created_at = 9;
	required uint32 need_verify = 10;
	required uint32 all_mute = 11;
	optional uint32 display_id = 12;
	optional TGroupExtra tgroup_extra = 13;		// 群组才有的特殊字段
	optional uint32 need_verify_v2 = 14;
	optional uint32 member_limit = 15;  // 群组成员人数限制
}

message MyGroupInfo{
	required GroupResp groupinfo = 1;
	optional GroupMemberResp myrole = 2;
	optional GroupMemberListResp adminlist = 3;
}

message BatchGetGroupReq{
	repeated uint32 group_list = 1;
	required bool is_needmyrole = 2;
	required bool is_needadmin = 3;
}

message BatchGetGroupResp{
	repeated MyGroupInfo groupinfo_list = 1;
}

message CreateTGroupExtra {
	required string city_code = 1;
	required string city_name = 2;
	required string tgroup_desc = 3;
    optional uint32 tgroup_type = 4;
    optional uint32 init_level = 5;
}

message CreateGroupReq{
	required uint32 guild_id = 1;
	required uint32 game_id  = 2;
	required uint32 uid		 = 3;
	required string name	 = 4;
	required uint32 group_type= 5;
	required uint32 need_verify = 6;
	optional CreateTGroupExtra tgroup_extra = 7;
	optional bool need_display_id = 8;
}

message CreateGroupResp{
	required uint32 group_id = 1;
}

message UpdateGroupNameReq{
	required uint32 group_id = 1;
	required string name	 = 2;
}

message RemoveGroupMemberReq{
	required uint32 group_id = 1;
	required uint32 uid = 2;
}

message GuildCheckinReq{
	required uint32 guild_id = 1;
	required uint32 uid = 2;
	optional uint32 invalid = 3;
}

message GetGuildCheckinReq{
	required uint32 guild_id = 1;
	required uint32 uid = 2;
}

message GetGuildCheckinResp{
	required uint32 guild_id = 1;
	required uint32 uid = 2;
	required uint32 last_checkin_at = 3; // 上次签到时间（实际上是今天的签到时间）
	required uint32 checkin_count = 4;	// 连续签到天数
	optional uint32 supplement_days = 5; // 可补签的天数
	optional uint32 supplement_price = 6; // 补签费用
	optional uint32 supplemented_days = 7; // 补签后连续签到天数
	optional uint32 accum_checkin_count = 8; // 累计签到天数
	repeated uint32 supplement_day_list = 9; // 补签的列表
	optional uint32 real_last_checkin_at = 10; // 真正的上次签到时间
}

message GetGuildCheckinListReq{
	required uint32 guild_id = 1;
	optional uint32 limit = 2;
	optional uint32 offset = 3;
	optional uint32 rank_type = 4;	// E_GUILD_CHECK_IN_RANK_TYPE
}

message GetGuildCheckinListResp{
	repeated GetGuildCheckinResp checkinList = 1;
}

message GetGuildCheckinListByUidsReq{
	required uint32 guild_id = 1;
	repeated uint32 uid_list = 2;
}

message GetGuildCheckinCountReq{
	required uint32 guild_id = 1;
}

message GetGuildCheckinCountResp{
	required uint32 checkin_count =1;
}

message SetGuildMemberToAdminReq{
	required uint32 guild_id = 1;
	required uint32 uid = 2;
	required uint32 permission = 3;
}

message SetGuildMemberToAdminResp{
	repeated uint32 modify_group_list = 3;
}


message SetGuildAdminToMemberReq{
	required uint32 guild_id = 1;
	required uint32 uid = 2;
}

message SetGroupMemberToOwnerReq{
	required uint32 group_id = 1;
	required uint32 uid = 2;
}

message SetGroupOwnerToMemberReq{
	required uint32 group_id = 1;
	required uint32 uid = 2;
}

message SetGroupMemberToAdminReq{
	required uint32 group_id = 1;
	required uint32 uid = 2;
}

message SetGroupAdminToMemberReq{
	required uint32 group_id = 1;
	required uint32 uid = 2;
}

message GetGuildGroupListReq{
	required uint32 guild_id = 1;
}


message GetUserGroupListReq{
	required uint32 uid = 1;
	required uint32 guild_id = 2;
}

message GroupListResp{
	repeated GroupResp group_list = 1;
}

message GroupIdListResp{
	repeated uint32 group_id_list = 1;
}

message UpdateGroupMemberMuteReq{
	required uint32 group_id = 1;
	required uint32 uid		 = 2;
	required bool mute		 = 3;
	optional uint32 mute_second = 4; // 如果mute为true 这里表示禁言多少秒 为0或者为空 表示永久禁言
}

message UpdateGroupMemberCardReq{
	required uint32 group_id	= 1;
	required uint32 uid			= 2;
	required string card		= 3;
}

message SearchGuildReq{
	required string keyword = 1;
}

message GuildListResp{
	repeated GuildResp guild_list = 1;
}

message GameGuildIdListReq{
	required uint32 game_id = 1;
	optional uint32 start_idx = 2;
	optional uint32 limit = 3;
}

message GameGuildIdListResp{
	repeated uint32 guild_id_list = 1;
}

message UpdateGroupNeedVerifyReq{
	required uint32 group_id = 1;
	required uint32 need_verify = 2;
}

message PublishGuildBulletinReq{
	required uint32 uid = 1;
	required uint32 guild_id = 2;
	required string title = 3;
	required string content = 4;
	required uint32 bulletin_type = 5;
}

message PublishGuildBulletinResp{
	required uint32 bulletin_id = 1;
}

message GetGuildBulletinListReq{
	required uint32 guild_id = 1;
	required uint32 offset = 2;
	required uint32 limit = 3;
}

message GuildBulletin{
	required uint32 guild_id = 1;
	required uint32 bulletin_id = 2;
	required uint32 uid = 3;
	required string title = 4;
	required string content = 5;
	required uint32 bulletin_type = 6;
	required uint32 created_at = 7;
	required uint32 updated_at = 8;
}

message GetGuildBulletinListResp{
	repeated GuildBulletin guild_bulletin_list = 1;
	required uint32 total	= 2;
}

message DeleteGuildBulletinReq {
	required uint32 guild_id = 1;
	required uint32 bulletin_id = 2;
}

message DeleteGuildBulletinResp {
}

message UpdateGroupAllMuteReq{
	required uint32 group_id = 1;
	required bool	all_mute = 2;
}

message UpdateGuildMemberPermissionReq{
	required uint32 guild_id = 1;
	required uint32 uid		 = 2;
	required uint32 permission = 3;
}

message UpdateGuildGameOrderReq {
	required uint32 guild_id = 1;
	required uint32 game_id = 2;
	required uint32 order = 3;
}

message UpdateGuildGameOrderResp {
}

message DismissGuildReq{
	required uint32 guild_id = 1;
	optional uint32 uid = 2;
}

// 查公会游戏
message GetGuildGameReq {
	required uint32 guild_id = 1;
	required uint32 game_id = 2;
}

message GetGuildGameResp {
	required GuildGame guild_game = 1;
}

// 设置公会游戏下载地址
message SetGuildGameUrlReq {
	required uint32 guild_id = 1;
	required uint32 game_id = 2;
	required string url = 3;
	required bool use_custom_url = 4;
	required string desc = 5;
	required string pkg_size = 6;
}

message SetGuildGameUrlResp {
}

message GuildModfiyVerifyReq{
	required uint32 guild_id = 1;
	required bool need_verify = 2;
}

// 直接加入公会
message GuildDirectJoinReq {
	required uint32 guild_id = 1;
	optional uint32 need_record = 2;
	optional uint32 invite_pkg_type = 3;
	optional uint32 inviter = 4;
	optional uint32 inviter_guild_id = 5;
}

message GuildDirectJoinResp {
}


//批量查公会昨天签到人数
message stGuildCheckinCount{
	required uint32 guild_id = 1;
	required uint32 checkin_count = 2;
}

message GetGuildYesterdayCheckinCountBatReq{
	repeated uint32 guild_id_list = 1;
}

message GetGuildYesterdayCheckinCountBatResp{
	repeated stGuildCheckinCount checkin_count_list = 1;
}


// 批量获取公会信息
message GetGuildBatReq{
	repeated uint32 guild_id_list = 1;
    optional bool with_dismissed = 2;
}

message GetGuildBatResp{
	repeated GuildResp guild_list = 2;
}

//----------------------------------------
// 设置开启游戏自定义下载链接
//----------------------------------------
message SetGuildGameUseCustomUrlReq {
	required uint32 guild_id = 1;
	required uint32 game_id = 2;
	required bool is_use = 3;
}

message SetGuildGameUseCustomUrlResp {
}


//=========================================
// 解散群
//=========================================
message DismissGroupReq {
	required uint32 group_id = 1;
}

//=========================================
// 公会游戏统计
//=========================================
message GetGuildGamesUserCountReq {
	required uint32 guild_id = 1;		// 公会id
	repeated uint32 game_id_list = 2;	// 游戏id列表, 如果传空, 会查询所有该公会的游戏
}

message StGuildGameUserCount {
	required uint32 game_id = 1;	// 游戏id
	required uint32 user_count = 2;	// 用户数
}

message GetGuildGamesUserCountResp {
	repeated StGuildGameUserCount game_user_count_list = 1;
}

//=========================================
// 公会申请发送成功，打标记
//=========================================
message MarkGuildApplySendMsgSuccessReq {
	required uint32 apply_id = 1;
}

message MarkGuildApplySendMsgSuccessResp {
}

//=========================================
// 查用户当前的公会申请记录
//=========================================
message GetUserCurrentGuildApplyReq {
	required uint32 guild_id = 1;
}

message GuildApply {
	required uint32 apply_id = 1;
	required uint32 uid = 2;
	required uint32 guild_id = 3;
	required string verify = 4;
	required uint32 status = 5;
	required uint32 apply_at = 6;
	required uint32 reviewer = 7;
	required uint32 reviewed_at = 8;
	required uint32 confirm_at = 9;
	required uint32 seq = 10;
	required uint32 msg_send_success = 11;
}

message GetUserCurrentGuildApplyResp {
	required bool is_exist = 1;
	optional GuildApply apply = 2;
}

message GuildGameDownloadReportReq {
	required uint32 guild_id = 1;
	required uint32 game_id = 2;
	required string game_url = 3;
}

message StGuildMemberGameDownloadInfo {
	required uint32 uid = 1;
	repeated uint32 game_id_list = 2;
}

message GetMemberGameDownloadListReq {
	required uint32 guild_id = 1;
}

message GetMemberGameDownloadListResp {
	repeated StGuildMemberGameDownloadInfo download_info_list = 1;
}

message SetGroupNotRecvMsgReq {
	required uint32 group_id = 1;
	required uint32 recv_msg_opt = 2;
	optional uint32 guild_id = 3;	// id of guild to which the group belongs
}

message SetGroupNotRecvMsgResp {
}

message ModifyGuildNameReq {
	required uint32 guild_id = 1;
	required string oldName = 2;
	required string newName = 3;
}

message ModifyGuildNameResp {
}

message CountGameGuildReq{
	required uint32 game_id = 1;
}

message CountGameGuildResp {
	required uint32 count = 1;
}

message GetAllGuildIdListReq {

}


message GetAllGuildIdListResp {
	repeated uint32 guild_id_list = 1;
}

message GetAllDeletedGuildIdListReq {

}

message GetAllDeletedGuildIdListResp {
	repeated uint32 guild_id_list = 1;
}

message UpdateGameGuildUserCountReq {
	required uint32 game_id = 1;
	required uint32 guild_id = 2;
	required uint32 user_count = 3;
}

message UpdateGameGuildOnlineUserCountReq {
	required uint32 game_id = 1;
	required uint32 guild_id = 2;
	required uint32 user_count = 3;
	required bool is_deleted = 4;
}

message TopGameGuildReq {
	required uint32 game_id = 1;
	required uint32 limit = 2;
	optional uint32 start_idx = 3;
}

message GuildDeleteGameReq {
	required uint32 guild_id = 1;
	required uint32 game_id = 2;
}

message GuildDeleteGameResp {
}

message GuildDeleteGamesReq {
	required uint32 guild_id = 1;
	repeated uint32 game_id_list = 2;
}

message GuildModifyGameGroupOrderReq {
	required uint32 guild_id = 1;
	repeated uint32 game_group_id = 2;
	repeated uint32 order = 3;
}

message GuildModifyGameGroupOrderResp {
}

message GuildRecoverGameReq {
	required uint32 guild_id = 1;
	required uint32 game_id = 2;
}

message GuildRecoverGameResp {
}

message GuildGetGroupOrderReq {
	required uint32 guild_id = 1;
}

message GroupOrder {
	required uint32 group_id = 1;
	required uint32 game_id = 2;
}

message GuildGetGroupOrderResp {
	repeated GroupOrder group_list = 1;
	optional bool is_set_order = 2;
}

message GetGuildMemberJoinedCountReq {
	required uint32 guild_id = 1;
	optional string from_date_string = 2;
	optional string to_date_string = 3;
}

message GetGuildMemberJoinedCountResp {
	required uint32 joined_count = 1;
}

message GuildSetUidNeedVerifyReq {
	required uint32 guild_id = 1;
	required uint32 uid = 2;
}

message GuildSetUidNeedVerifyResp {
}

message GuildCheckUidNeedVerifyReq {
	required uint32 guild_id = 1;
	required uint32 uid = 2;
}

message GuildCheckUidNeedVerifyResp {
	required bool is_need_verify = 1;			// 被该公会踢过，则需要验证
	optional uint32 kicked_count = 2;			// 被多少个公会踢过
	optional bool in_white_list = 3;
}

message GroupCheckUidNeedVerifyReq {
	required uint32 group_id = 1;
	required uint32 uid = 2;
}

message GroupCheckUidNeedVerifyResp {
	required bool is_need_verify = 1;			// 被该公会踢过，则需要验证
	optional uint32 kicked_count = 2;			// 被多少个公会踢过
}

message GroupSetUidNeedVerifyReq {
	required uint32 group_id = 1;
	required uint32 uid = 2;
}

message GroupSetUidNeedVerifyResp {
}
//==================================
//
// 公会排行榜
//
//==================================
message GetTopGuildIdListReq {
	required uint32 limit = 1 ;
}

message GetTopGuildIdListResp {
	repeated uint32 guild_id_list = 1;
}

// 修改公会游戏上限
message UpdateGuildGameLimitReq {
	required uint32 guild_id = 1;
	required uint32 game_limit = 2;
}

message UpdateGuildGameLimitResp {
}

// 查用户当前创建了多少个TGroup
message TGroupGetUserCreateGroupCountReq {
	required uint32 uid = 1;
}

message TGroupGetUserCreateGroupCountResp {
	required uint32 group_count = 1;
}

// 根据游戏ID获取备选的推荐列表(正常的推荐列表 由recommendGuildSrv产生)
message GetRecommendGuildIDListByGameIDReq {
	required uint32 game_id = 1;
	required uint32 limit = 2;
	repeated uint32 exceptguildid_list = 3; // 需要排除在外的工会ID列表
}
message GetRecommendGuildIDListByGameIDResp
{
	repeated uint32 guildid_list = 1; // 结果工会ID列表
}

// 修改T群的描述 地理位置 游戏ID
message ModifyTGroupDescReq {
	required uint32 group_id = 1;
	required string group_desc = 2;
	
	optional string city_code = 3;
	optional string city_name = 4;
	optional uint32 game_id = 5;
}


message ModifyTGroupDescResp {
}

message GetTGroupByDisplayIdReq {
	required uint32 tgroup_display_id = 1;
}

message GetTGroupByDisplayIdResp {
	required GroupResp groupResp = 1;
}

// 获取用户所在的群ID列表
message GetUserGroupIDListByGuildIDReq {
	required uint32 uid = 1;
	optional uint32 guildID = 2;
}
message GetUserGroupIDListByGuildIDResp
{
	repeated uint32 groupid_list = 1;
	repeated uint32 not_recv_groups = 2;
}

message TGroupSearchByGameIdReq {
	repeated uint32 game_id = 1;
	optional string city_code = 2;
	required uint32 limit = 3;
	optional uint32 is_active = 4;
}

message TGroupGameGroupIdList {
	required uint32 game_id = 1;
	repeated uint32 group_id_list = 2;
}

message TGroupSearchByGameIdResp {
	repeated TGroupGameGroupIdList group_list = 1;
}

message TGroupGetUserJoinGroupListReq {
	required uint32 uid = 1;
}

message TGroupGetUserJoinGroupListResp {
	repeated uint32 create_group_id_list = 1;
	repeated uint32 join_group_id_list = 2;
}

message GetGroupMuteUserListReq {
	required uint32 group_id = 1;
}

message GetGroupMuteUserListResp {
	repeated uint32 uid_list = 1;
}


message GetAllGroupIdListReq{
	required uint32 group_type = 1;
}
message GetAllGroupIdListResp{
	repeated uint32	group_list = 1;
}

// 对指定用户检查所在公会的全部群 获取这些用户在群内管理权限
message CheckUserGroupAdminInfoInAllGroupReq
{
	required uint32	guild_id = 1;
	repeated uint32	uid_list = 2;
	optional uint32 except_group_id = 3;
}

message stUserGroupAdminInfo
{
	required uint32 uid = 1;
	repeated uint32 owner_group_list = 2; // 该用户为群主的群ID列表
	repeated uint32 admin_group_list = 3; // 该用户为管理员的群ID列表
}

message CheckUserGroupAdminInfoInAllGroupResp
{
	required uint32	guild_id = 1;
	repeated stUserGroupAdminInfo userGroupAdminInf_list = 2;
	optional uint32 except_group_id = 3;
}

message UpdateGuildApplyExceedReq
{
	required uint32 uid = 1;
	required uint32 apply_id = 2;
	required uint32 guild_id = 3;
}

message UpdateGuildApplyExceedResp {
}

message UpdateGroupApplyExceedReq {
	required uint32 uid = 1;
	required uint32 apply_id = 2;
	required uint32 group_id = 3;
}

message UpdateGroupApplyExceedResp {
}

// 判断用户是否在公会有头衔(会长 副会长 群主 群管理 or 普通银)
message CheckUserGuildDutyReq
{
	required uint32	guild_id = 1;
	repeated uint32	uid_list = 2;
}

message stUserDutyInfo
{
	required uint32 uid = 1;
	required uint32 guild_duty_role = 2; // 用户在公会的角色 1会长 2副会长 3普通会员
	required uint32 group_duty_role = 3; // 用户在公会群组里面的最高角色 1群主 2群管理 3普通群成员
}

message CheckUserGuildDutyResp
{
	required uint32	guild_id = 1;
	repeated stUserDutyInfo userDutyInfo_list = 2;
}

message GetGuildAllDutyListReq
{
	required uint32	guild_id = 1;
}

message GetGuildAllDutyListResp
{
	repeated stUserDutyInfo user_dutyinfo_list = 1;
}


message GuildCheckinRecord
{
	required uint32 uid = 1;
	required uint32 days_diff = 2;
	required uint32 supplement_at = 3;
	required uint32 checkin_count = 4;
}

// 补签
message GuildCheckinSupplementReq
{
	required uint32 guild_id = 1;
	required uint32 uid = 2;
}

message ModifyGameTopGuildReq
{
	required uint32 game_id = 1;
	repeated uint32 guild_id_list = 2;
}

message ModifyGameTopGuildResp
{
}

// 最近登录游戏
message StMemberRecentLoginLyGame {
	required uint32 ly_game_id = 1;
	required uint32	login_ts = 2;	// 登录时间
}

// 上报公会成员的最近登录游戏
message ReportMemberGameLoginReq {
	required uint32 guild_id = 1;
	required uint32 ly_game_id = 2;
}

// 获取公会成员的最近登录游戏
message GetMemberRecentGameLoginReq {
	required uint32 guild_id = 1;
}

message GetMemberRecentGameLoginResp {
	optional StMemberRecentLoginLyGame login_game = 1;
}

//------------------公会捐献------------------

// 公会成员捐献
message GuildDonateReq{
	required uint32 guild_id = 1;
	required uint32 uid = 2;
	required uint32 donate_value = 3;
	optional uint32 invalid = 4;
}

// 获取公会成员捐献信息
message GetGuildDonateReq{
	required uint32 guild_id = 1;
	required uint32 uid = 2;
}

message GetGuildDonateResp{
	required uint32 guild_id = 1;
	required uint32 uid = 2;
	required uint32 last_donate_at = 3;	// 上次捐献时间（实际上是今天的捐献时间）
	required uint32 donate_count = 4;	// 连续捐献天数
	required uint32 accum_donate_value = 5;	// 累计捐献值
	required uint32 last_donate_value = 6;	// 上次捐献值
	optional uint32 real_last_donate_at = 7; // 真正的上次捐献时间
}

// 获取公会成员捐献列表
message GetGuildDonateListReq{
	required uint32 guild_id = 1;
	optional uint32 limit = 2;
	optional uint32 offset = 3;
}

message GetGuildDonateListResp{
	repeated GetGuildDonateResp donate_list = 1;
}

// 获取公会捐献人数
message GetGuildDonateCountReq{
	required uint32 guild_id = 1;
}

message GetGuildDonateCountResp{
	required uint32 donate_count =1;
}

// 批量查公会昨天捐献人数
message stGuildDonateCount{
	required uint32 guild_id = 1;
	required uint32 donate_count = 2;
}

message GetGuildYesterdayDonateCountBatReq{
	repeated uint32 guild_id_list = 1;
}

message GetGuildYesterdayDonateCountBatResp{
	repeated stGuildDonateCount donate_count_list = 1;
}

// 设置公会成员称号
message SetGuildMemberTitleReq {
	required uint32 member_uid = 1;
	required uint32 guild_id = 2;
	required string title = 3;
}

//获取公会等级对应主打游戏数
message GetGuildGameCountConfigReq{
	required uint32 level =  1;
}

message GetGuildGameCountConfigResp{
	required uint32 level = 1;
	required uint32 count = 2;	//游戏数
	required uint32 extra = 3;	//扩展游戏数
	required uint32 cost = 4;	//扩展消费贡献值
}

//获取公会已扩充的游戏数
message GetGuildExtraGameCountReq{
	required uint32 guild_id = 1;
}

message GetGuildExtraGameCountResp{
	required uint32 count = 2;
}

//增加扩充游戏数
message AddGuildExtraGameCountReq{
	required uint32 guild_id = 1;
	required uint32 count = 2;
}

//添加公会游戏

message AddGuildGameV2Req
{
	required uint32 guild_id = 1;
	repeated uint32 game_id_list  = 2;	//
}

//更新游戏公会表的贡献值记录
message UpdateGameGuildStarLevelReq {
	required uint32 game_id = 1;
	required uint32 guild_id = 2;
	required uint32 value = 3;
}

//批量更新游戏公会表贡献值
message GuildStarLevel{
	required uint32 guild_id = 1;
	required uint32 value = 2;
}
message UpdateGameGuildStarLevelBatReq{
	repeated GuildStarLevel guild_list = 1;
}


//修改群组绑定游戏
message ModifyGroupGameReq
{
	required uint32 guild_id = 1;
	required uint32 group_id = 2;
	required uint32 game_id = 3;
}

//官员职位信息
message GuildOfficialInfo{
	required uint32 official_id = 1;
	required string official_name = 2;
	required uint32 permission = 3;
}

//创建职位
message CreateGuildOfficialReq{
	required uint32 guild_id = 1;
	required string official_name = 2;
	required uint32 permission = 3;
}

//删除职位
message DelGuildOfficialReq{
	required uint32 guild_id = 1;
	required uint32 official_id = 2;
}

//修改职位
message ModifyGuildOfficialReq{
	required uint32 guild_id = 1;
	required uint32 official_id = 2;
	required string official_name = 3;
	required uint32 permission = 4;
}

//获取公会所有职位
message GetGuildOfficialAllReq{
	required uint32 guild_id = 1;	
}

message GetGuildOfficialAllResp{
	repeated GuildOfficialInfo official_list = 1;
}

//通过ID获取公会职位
message GetGuildOfficialByIdReq{
	required uint32 guild_id = 1;	
	required uint32 official_id = 2;
}

message GetGuildOfficialByIdResp{
	required GuildOfficialInfo official_info = 1;
}

//任命官员
message AddGuildOfficialMemberReq{
	required uint32 guild_id = 1;
	required uint32 official_id = 2;
	repeated uint32 uids = 3;
}

message AddGuildOfficialMemberByNameReq{
	required uint32 guild_id = 1;
	required string official_name = 2;
	repeated uint32 uids = 3;
}

//撤销任命官员
message RemoveGuildOfficialMemberReq{
	required uint32 guild_id = 1;
	repeated uint32 uids = 2;
}

//获取职位下的成员
message GetGuildOfficialMemberReq{
	required uint32 guild_id = 1;
	required uint32 official_id = 2;
}

message GetGuildOfficialMemberResp{
	repeated uint32 member_list = 1;
}

//获取成员权限
message GetGuildOfficialByUidReq{
	required uint32 guild_id = 1;
	required uint32 uid = 2;
}

message GetGuildOfficialByUidResp{
	required GuildOfficialInfo info = 1;
}

// 批量获取成员权限
message GuildOfficialMember{
	required uint32 uid = 1;
	required GuildOfficialInfo info = 2;
}
message BatGetGuildOfficialByUidsReq{
	required uint32 guild_id = 1;
	repeated uint32 uid_list = 2;
}

message BatGetGuildOfficialByUidsResp{
	repeated GuildOfficialMember info_list = 1;
}

//获取所有官员职位信息
message GetGuildOfficialMemberMapReq{
	required uint32 guild_id = 1;
}

message GetGuildOfficialMemberMapResp{
	repeated GuildOfficialMember member_list = 1;
}


message GetGuildByMultiCondReq {
	optional uint32 guild_state = 1;
	optional string guild_name = 2;
	repeated uint32 owner_id_list = 3;
	optional uint32 member_count_begin = 4;
	optional uint32 member_count_end = 5;
	optional uint32 time_created_begin = 6;
	optional uint32 time_created_end = 7;
	repeated uint32 game_joined_list = 8;
	repeated uint32 guild_id_list = 9;
	required uint32 start = 10;
	required uint32 limit = 11;
}

message GetGuildByMultiCondResp {
	repeated GuildResp guild_info_list = 1;
}


message StGuildJoinRecord {
	required uint32 uid = 1;
	required uint32 joined_at = 2;
}

// 获取指定公会 成员加入的历史流水记录
message GetGuildJoinHistoryReq {
	required uint32 guild_id = 1;
	optional uint32 offset = 2;
	optional uint32 limit = 3;
}

message GetGuildJoinHistoryResp {
	repeated StGuildJoinRecord join_list = 1;
}

message GetGuildPopGamesReq
{
	required uint32 limit = 1;
	repeated uint32 exclude_id_list = 2; //不获取的游戏id
}

message GetGuildPopGamesResp
{
	repeated uint32 game_id_list = 1;
}

// 获取指定用户 加入公会的历史流水记录
message StUserJoinGuildRecord {
	required uint32 guild_id = 1;
	required uint32 join_ts = 2;
	required uint32 quit_ts = 3;
}
message GetUserJoinGuildHistoryReq
{
	required uint32 uid = 1;
	required uint32 limit = 2;
	optional uint32 opt_guild_id = 3;
}
message GetUserJoinGuildHistoryResp
{
	repeated StUserJoinGuildRecord record_list = 1;
}

message ResetGuildNameReq{
	required uint32 guild_id = 1;
}

message GetGroupByIdsReq{
	repeated uint32 group_ids = 1;
}

message GetGroupByIdsResp{
	repeated GroupResp group_list = 1;
}

message StGuildBlackListUser {
	required uint32 uid = 1;
	required uint32 expired_time = 2;	// 自动解除黑名单时间
	required uint32 opt_uid = 3;
}

message SetGuildBlackListReq {
	required uint32 guild_id = 1;
	repeated uint32 uid_list = 2;
}

message RemoveGuildBlackListReq {
	required uint32 guild_id = 1;
	repeated uint32 uid_list = 2;
}

message GetGuildBlackListReq {
	required uint32 guild_id = 1;
	optional uint32 offset = 2;
	optional uint32 limit = 3;
}

message GetGuildBlackListResp {
	repeated StGuildBlackListUser black_list = 1;
}

message CheckUidInBlackListReq {
	required uint32 guild_id = 1;
}

message CheckUidInBlackListResp {
	optional StGuildBlackListUser user_info = 1;
}

message GuildStatus{
    //公会有效性
    enum E_GUILD_AVAILABILITY
    {
        E_GUILD_NORMAL = 1;
        E_GUILD_DISMISSED = 2;
        E_GUILD_NOT_EXIST = 3;
    }
    required uint32 guild_id = 1;
    optional uint32 availability = 2;   //E_GUILD_AVAILABILITY
}

message GetGuildStatusReq{
    repeated uint32 guild_id_list = 1;
    optional bool from_slavedb = 2;
}

message GetGuildStatusRsp{
    repeated GuildStatus status_list = 1;
}

//根据display id获取group id
message GetTGroupIdByDisplayIdReq{
    repeated uint32 display_ids = 1;
}
message TGroupDisplayIdMapping{
    required uint32 display_id = 1;
    required uint32 group_id = 2;
}

message GetTGroupIdByDisplayIdResp{
    repeated TGroupDisplayIdMapping tgroup_id_mappings = 1;
}

//根据group type获取group id
message GetGroupIdsByTypeReq{
    repeated uint32 group_type_list = 1;
}

message GroupIdTypeResp{
    required uint32 group_id = 1;
    required uint32 group_type = 2;
}

message GetGroupIdsByTypeResp{
    repeated GroupIdTypeResp group_resp_list = 1;
}


//查询tgroup禁言列表
message GetTGroupMuteListReq {
	required uint32 group_id = 1;
}

message GetTGroupMuteListResp {
	repeated uint32 mute_uid_list = 1;
}

message DeleteGuildTransReq {
	required uint32 uid = 1;
	required uint32 from_guildid = 2;
	required uint32 to_guildid = 3;
	required uint32 quit_type = 4;
}

message DeleteGuildTransResp {
}

message BatchDeleteGuildTransReq {
	repeated uint32 uids = 1;
	required uint32 from_guildid = 2;
	required uint32 to_guildid = 3;
	required uint32 quit_type = 4;
}

message BatchDeleteGuildTransResp {
}

message GuildTrans {
	required uint32 uid = 1;
	required uint32 from_guildid = 2;
	required uint32 to_guildid = 3;
	required uint32 quit_type = 4;
	required string oper_time = 5;
}

message GetGuildTransReq {
	required uint32 uid = 1;
}

message GetGuildTransResp {
	optional GuildTrans guildtrans = 1;
}

message BatchGetGuildTransReq {
	repeated uint32 uids = 1;
}

message BatchGetGuildTransResp {
	repeated GuildTrans guildtrans_list = 1;
}

message CreateGuildLimitReq{

}

message CreateGuildLimitResp{
	enum REAL_NAME_STATUS {
        NOT_AUTH = 0;   //未实名
        AUTH_PASS = 1;  //已实名
    }
	required uint32 current_level = 1; //当前平台等级
	required uint32 need_level = 2; //需要平台等级
	required uint32 real_name_status = 3;  //实名认证状态 see REAL_NAME_STATUS
 }

message AddMulticastRelationsReq {
	map<uint32, string> groups = 1;
	repeated uint32 uids = 2;
}

message AddMulticastRelationsResp {

}

message DelMulticastRelationsReq {
	map<uint32, string> groups = 1;
	repeated uint32 uids = 2;
}

message DelMulticastRelationsResp {

}

message AllocGroupDisplayIdReq {

}

message AllocGroupDisplayIdResp {
	optional uint32 display_id = 1;
}

message GenTGroupHeadImageReq {
	optional uint32 group_id = 1;
	optional string name_prefix = 2;
}

message GenTGroupHeadImageResp {
}

service Guild {
	option( tlvpickle.Magic ) = 14700;		// 服务监听端口号

	rpc CreateGuild( CreateGuildReq ) returns( CreateGuildResp ){
		option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "n:d:u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <guild name> -d <desc> -u <uid>";	// 测试工具的命令号帮助
	}

	rpc GetGuild( GuildIdReq ) returns ( GuildResp ){
		option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "g:d:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild id> -d <desc>";
	}

	rpc SetShortId( SetShortIdReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 3;
		option( tlvpickle.OptString ) = "g:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild id> -s <shot id>";
	}

	rpc CheckNameExist( CheckNameExistReq ) returns ( CheckNameExistResp ){
		option( tlvpickle.CmdID ) = 4;
		option( tlvpickle.OptString ) = "n:";
		option( tlvpickle.Usage ) = "-n <guild name>";
	}

	rpc GuildAddGame( GuildAddGameReq ) returns ( GuildAddGameResp ){
		option( tlvpickle.CmdID ) = 5;
		option( tlvpickle.OptString ) = "g:a:";
		option( tlvpickle.Usage ) = "-g <guild id> -a <game id>";
	}

	rpc CheckGuildChairman(CheckGuildChairmanReq) returns (CheckGuildChairmanResp){
		option( tlvpickle.CmdID ) = 6;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <guild id> -u <uid>";
	}

	rpc GetGuildMember ( GetGuildMemberReq ) returns ( GuildMemberResp ){
		option( tlvpickle.CmdID ) = 7;
		option( tlvpickle.OptString ) = "u:g:";
		option( tlvpickle.Usage ) = "-u <uid> -g <guild id>";
	}

	rpc GetGuildGameList ( GuildIdReq ) returns ( GetGuildGameListResp ){
		option( tlvpickle.CmdID ) = 8;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <guild id>";
	}

	// 记录需要管理员批准的入会申请
	rpc ApplyGuild ( ApplyGuildReq ) returns ( ApplyGuildResp ){
		option( tlvpickle.CmdID ) = 9;
		option( tlvpickle.OptString ) = "u:g:v:";
		option( tlvpickle.Usage ) = "-u <uid> -g <guild id> -v <verify>";
	}
	// 获取需要管理员批准的入会申请
	rpc GetGuildApply( GetGuildApplyReq ) returns ( GuildApplyResp ){
		option( tlvpickle.CmdID ) = 10;
		option( tlvpickle.OptString ) = "u:a:";
		option( tlvpickle.Usage ) = "-u <uid> -a <apply id>";
	}

	rpc GetGuildMemberList( GetGuildMemberListReq ) returns ( GuildMemberListResp ){
		option( tlvpickle.CmdID ) = 11;
		option( tlvpickle.OptString ) = "g:o:p:";
		option( tlvpickle.Usage ) = "-g <guid id> -o <offset> -p <page size>";
	}

	rpc ReviewApplyGuild( ReviewApplyGuildReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 12;
		option( tlvpickle.OptString ) = "r:u:a:";
		option( tlvpickle.Usage ) = "-r <reviewer> -u <apply uid> -a <apply id>";
	}



	rpc GetUserGuildApplyList ( GetUserGuildApplyListReq ) returns ( GetUserGuildApplyListResp ){
		option( tlvpickle.CmdID ) = 13;
		option( tlvpickle.OptString ) = "u:";
		option( tlvpickle.Usage ) = "-u <apply uid>";
	}



	rpc GetGroupMemberList ( GetGroupMemberListReq ) returns ( GroupMemberListResp ){
		option( tlvpickle.CmdID ) = 15;
		option( tlvpickle.OptString ) = "g:l:";
		option( tlvpickle.Usage ) = "-g <group id> -l <guildid>";
	}

	rpc AddGroupMember ( AddGroupMemberReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 16;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <group id> -u <uid>";
	}

	rpc GetGroup ( GetGroupReq ) returns ( GroupResp ){
		option( tlvpickle.CmdID ) = 17;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <group id>";
	}

	rpc CreateGroup ( CreateGroupReq ) returns ( CreateGroupResp ){
		option( tlvpickle.CmdID ) = 18;
		option( tlvpickle.OptString ) = "g:a:u:n:t:";
		option( tlvpickle.Usage ) = "-g <guild id> -a <game id> -u <uid> -n <group name> -t <group type>";
	}

	rpc UpdateGroupName ( UpdateGroupNameReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 19;
		option( tlvpickle.OptString ) = "g:n:";
		option( tlvpickle.Usage ) = "-g <group id> -n <name>";
	}

	rpc RemoveGroupMember ( RemoveGroupMemberReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 20;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <group id> -u <uid>";
	}

	rpc GetGroupMember ( GetGroupMemberReq ) returns ( GroupMemberResp ){
		option( tlvpickle.CmdID ) = 21;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <group id> -u <uid>";
	}

	rpc GuildCheckin ( GuildCheckinReq ) returns ( GetGuildCheckinResp ){
		option( tlvpickle.CmdID ) = 22;
		option( tlvpickle.OptString ) = "g:u:t:";
		option( tlvpickle.Usage ) = "-g <guild id> -u <uid> [-t <invalid>]";
	}

	rpc GetGuildCheckin ( GetGuildCheckinReq ) returns ( GetGuildCheckinResp ){
		option( tlvpickle.CmdID ) = 23;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <guild id> -u <uid>";
	}

	rpc GetGuildCheckinList ( GetGuildCheckinListReq ) returns ( GetGuildCheckinListResp ){
		option( tlvpickle.CmdID ) = 24;
		option( tlvpickle.OptString ) = "g:o:n:r:l";
		option( tlvpickle.Usage ) = "-g <guild id> [ -o <offset> -n <limit> -r <rank_type>  -l, with_user_list ]";
	}

	rpc GetGuildCheckinCount ( GetGuildCheckinCountReq ) returns ( GetGuildCheckinCountResp ){
		option( tlvpickle.CmdID ) = 25;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <guild id>";
	}

	rpc RemoveGuildMember ( RemoveGuildMemberReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 26;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <guild id> -u <uid>";
	}

	rpc SetGuildMemberToAdmin ( SetGuildMemberToAdminReq ) returns ( SetGuildMemberToAdminResp ){
		option( tlvpickle.CmdID ) = 27;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <guild id> -u <uid>";
	}

	rpc SetGuildAdminToMember ( SetGuildAdminToMemberReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 28;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <guild id> -u <uid>";
	}

	rpc SetGroupMemberToAdmin ( SetGroupMemberToAdminReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 29;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <group id> -u <uid>";
	}

	rpc SetGroupAdminToMember ( SetGroupAdminToMemberReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 30;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <group id> -u <uid>";
	}

	rpc ApplyGroup ( ApplyGroupReq ) returns ( ApplyGroupResp ){
		option( tlvpickle.CmdID ) = 31;
		option( tlvpickle.OptString ) = "u:g:v:";
		option( tlvpickle.Usage ) = "-u <uid> -g <group id> -v <verify>";
	}

	rpc GetGroupApply( GetGroupApplyReq ) returns ( GroupApplyResp ){
		option( tlvpickle.CmdID ) = 32;
		option( tlvpickle.OptString ) = "u:a:";
		option( tlvpickle.Usage ) = "-u <uid> -a <apply id>";
	}

	rpc ReviewApplyGroup( ReviewApplyGroupReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 33;
		option( tlvpickle.OptString ) = "r:u:a:";
		option( tlvpickle.Usage ) = "-r <reviewer> -u <apply uid> -a <apply id>";
	}

	rpc SetGroupMemberToOwner ( SetGroupMemberToOwnerReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 34;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <group id> -u <uid>";
	}

	rpc GetUserGroupList ( GetUserGroupListReq ) returns ( GroupListResp ){
		option( tlvpickle.CmdID ) = 35;
		option( tlvpickle.OptString ) = "u:g:";
		option( tlvpickle.Usage ) = "-u <uid> -g <guild id>";
	}

	rpc UpdateGroupMemberMute ( UpdateGroupMemberMuteReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 36;
		option( tlvpickle.OptString ) = "g:u:m:";
		option( tlvpickle.Usage ) = "-g <group id> -u <uid> -m <mute>";
	}

	rpc SearchGuild ( SearchGuildReq ) returns ( GuildListResp ){
		option( tlvpickle.CmdID ) = 37;
		option( tlvpickle.OptString ) = "k:";
		option( tlvpickle.Usage ) = "-k <keyword>";
	}

	rpc UpdateGroupMemberCard ( UpdateGroupMemberCardReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 38;
		option( tlvpickle.OptString ) = "g:u:c:";
		option( tlvpickle.Usage ) = "-g <group id> -u <uid> -c <card>";
	}

	rpc GameGuildIdList ( GameGuildIdListReq ) returns ( GameGuildIdListResp ){
		option( tlvpickle.CmdID ) = 39;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <game id>";
	}

	rpc UpdateGroupNeedVerify ( UpdateGroupNeedVerifyReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 40;
		option( tlvpickle.OptString ) = "g:n:";
		option( tlvpickle.Usage ) = "-g <group id> -n <need verify>";
	}

	rpc PublishGuildBulletin ( PublishGuildBulletinReq ) returns ( PublishGuildBulletinResp ){
		option( tlvpickle.CmdID ) = 41;
		option( tlvpickle.OptString ) = "u:g:t:c:y:";
		option( tlvpickle.Usage ) = "-u <uid> -g <guild id> -t <title> -c <content> -y <type>";
	}

	rpc GetGuildBulletinList ( GetGuildBulletinListReq ) returns ( GetGuildBulletinListResp ){
		option( tlvpickle.CmdID ) = 42;
		option( tlvpickle.OptString ) = "g:o:l:";
		option( tlvpickle.Usage ) = "-g <guild id> -o <offset> -l <limit>";
	}

	rpc UpdateGuild ( UpdateGuildReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 43;
		option( tlvpickle.OptString ) = "g:n:i:p:m";
		option( tlvpickle.Usage ) = "-g <guild id> -n <name> -i <intro> -p <prefix> -m <manifesto>";
	}

	rpc UpdateGroupAllMute ( UpdateGroupAllMuteReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 44;
		option( tlvpickle.OptString ) = "g:m:";
		option( tlvpickle.Usage ) = "-g <group id> -m <all mute>";
	}

	rpc UpdateGuildMemberPermission ( UpdateGuildMemberPermissionReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 45;
		option( tlvpickle.OptString ) = "g:u:p:";
		option( tlvpickle.Usage ) = "-g <guild id> -u <uid> -p <permission>";
	}

	rpc DeleteGuildBulletin( DeleteGuildBulletinReq ) returns (DeleteGuildBulletinResp) {
		option( tlvpickle.CmdID ) = 46;
		option( tlvpickle.OptString ) = "g:b:";
		option( tlvpickle.Usage ) = "-g <guild id> -b <bulletin id>";
	}

	rpc UpdateGuildGameOrder( UpdateGuildGameOrderReq ) returns (UpdateGuildGameOrderResp) {
		option( tlvpickle.CmdID ) = 47;
		option( tlvpickle.OptString ) = "g:a:o:";
		option( tlvpickle.Usage ) = "-g <guild id> -a <game id> -o <order>";
	}

	rpc SetGroupOwnerToMember( SetGroupOwnerToMemberReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 48;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <group id> -u <uid>";
	}

	rpc GetGuildGroupList( GetGuildGroupListReq ) returns ( GroupIdListResp ){
		option( tlvpickle.CmdID ) = 49;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <guild id>";
	}
	rpc GetGuildGame( GetGuildGameReq ) returns ( GetGuildGameResp ){
		option( tlvpickle.CmdID ) = 50;
		option( tlvpickle.OptString ) = "g:a:";
		option( tlvpickle.Usage ) = "-g <guild id> -a <game_id>";
	}

	rpc SetGuildGameUrl( SetGuildGameUrlReq ) returns ( SetGuildGameUrlResp ){
		option( tlvpickle.CmdID ) = 51;
		option( tlvpickle.OptString ) = "g:a:u:d:";
		option( tlvpickle.Usage ) = "-g <guild id> -a <game_id> -u <url> -d <desc>";
	}

	rpc DismissGuild( DismissGuildReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 52;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <guild id> -u <uid>";
	}

	rpc GuildModfiyVerify( GuildModfiyVerifyReq ) returns (tlvpickle.SKBuiltinEmpty_PB){
		option( tlvpickle.CmdID ) = 53;
		option( tlvpickle.OptString ) = "g:n:";
		option( tlvpickle.Usage ) = "-g <guild id> -n <need verify>";
	}

	rpc GuildDirectJoin( GuildDirectJoinReq ) returns ( GuildDirectJoinResp ) {
		option( tlvpickle.CmdID ) = 54;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <guild id> -u <uid>";
	}

	rpc GetGuildYesterdayCheckinCountBat( GetGuildYesterdayCheckinCountBatReq ) returns ( GetGuildYesterdayCheckinCountBatResp ){
		option( tlvpickle.CmdID ) = 55;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <guild_id>";
	}

	rpc GetGuildBat( GetGuildBatReq ) returns ( GetGuildBatResp ){
		option( tlvpickle.CmdID ) = 56;
		option( tlvpickle.OptString ) = "g:d:";
		option( tlvpickle.Usage ) = "-g <guild_id1, guild_id2,...> [-d, with_dismissed]";
	}

	rpc SetGuildGameUseCustomUrl( SetGuildGameUseCustomUrlReq ) returns ( SetGuildGameUseCustomUrlResp ) {
		option( tlvpickle.CmdID ) = 57;
		option( tlvpickle.OptString ) = "g:a:s:";
		option( tlvpickle.Usage ) = "-g <guild_id> -a <game_id> -s <[1:open|0:close]>";
	}

	rpc DismissGroup( DismissGroupReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 58;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <group id>";
	}

	rpc GetGuildGamesUserCount( GetGuildGamesUserCountReq ) returns ( GetGuildGamesUserCountResp ) {
		option( tlvpickle.CmdID ) = 59;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <group id>";
	}

	rpc MarkGuildApplySendMsgSuccess( MarkGuildApplySendMsgSuccessReq ) returns ( MarkGuildApplySendMsgSuccessResp ) {
		option( tlvpickle.CmdID ) = 60;
		option( tlvpickle.OptString ) = "u:a:";
		option( tlvpickle.Usage ) = "-u <uid> -a <apply_id>";
	}

	rpc GetUserCurrentGuildApply( GetUserCurrentGuildApplyReq ) returns ( GetUserCurrentGuildApplyResp ) {
		option( tlvpickle.CmdID ) = 61;
		option( tlvpickle.OptString ) = "u:g:";
		option( tlvpickle.Usage ) = "-u <uid> -g <guild_id>";
	}

	rpc	GuildGameDownloadReport( GuildGameDownloadReportReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 62;
		option( tlvpickle.OptString ) = "i:g:a:u:";
		option( tlvpickle.Usage ) = "-i <uid> -g <guild_id> -a <game_id> -u <dl_url>";
	}

	rpc GetGuildMemberGameDownloadList( GetMemberGameDownloadListReq ) returns ( GetMemberGameDownloadListResp ) {
		option( tlvpickle.CmdID ) = 63;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <guild_id>";
	}

	rpc SetGroupNotRecvMsg( SetGroupNotRecvMsgReq ) returns ( SetGroupNotRecvMsgResp ) {
		option( tlvpickle.CmdID ) = 64;
		option( tlvpickle.OptString ) = "u:g:b:";
		option( tlvpickle.Usage ) = "-u <uid> -g <group_id> -r <recv_msg_opt>";
	}

	rpc ModifyGuildName( ModifyGuildNameReq ) returns (ModifyGuildNameResp) {
		option( tlvpickle.CmdID ) = 65;
		option( tlvpickle.OptString ) = "g:o:n:";
		option( tlvpickle.Usage ) = "-g <guild_id> -o <old_name> -n <new_name>";
	}

	rpc GetGuildMemberListByUids( GetGuildMemberListByUidsReq ) returns ( GuildMemberListResp ) {
		option( tlvpickle.CmdID ) = 66;
		option( tlvpickle.OptString ) = "u:g:";
		option( tlvpickle.Usage ) = "-u <uid> -g <guild_id>";
	}

	rpc CountGameGuild( CountGameGuildReq ) returns ( CountGameGuildResp ){
		option( tlvpickle.CmdID ) = 67;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <game_id>";
	}

	rpc GetAllGuildIdList ( GetAllGuildIdListReq ) returns ( GetAllGuildIdListResp ){
		option( tlvpickle.CmdID ) = 68;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc UpdateGameGuildUserCount ( UpdateGameGuildUserCountReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 69;
		option( tlvpickle.OptString ) = "g:u:c:";
		option( tlvpickle.Usage ) = "-g <game_id> -u <guild id> -c <user count>";
	}

	rpc TopGameGuild ( TopGameGuildReq ) returns ( GuildListResp ){
		option( tlvpickle.CmdID ) = 70;
		option( tlvpickle.OptString ) = "g:l:";
		option( tlvpickle.Usage ) = "-g <game_id> -l <limit>";
	}

	rpc GuildDeleteGame ( GuildDeleteGameReq ) returns ( GuildDeleteGameResp ) {
		option( tlvpickle.CmdID ) = 71;
		option( tlvpickle.OptString ) = "g:a:";
		option( tlvpickle.Usage ) = "-g <guild_id> -a <game_id>";
	}

	rpc GuildModifyGameGroupOrder ( GuildModifyGameGroupOrderReq ) returns ( GuildModifyGameGroupOrderResp ) {
		option( tlvpickle.CmdID ) = 72;
		option( tlvpickle.OptString ) = "g:r:";
		option( tlvpickle.Usage ) = "-g <guild_id> -r <group_id, group_id>";
	}

	rpc GuildRecoverGame ( GuildRecoverGameReq ) returns ( GuildRecoverGameResp ) {
		option( tlvpickle.CmdID ) = 73;
		option( tlvpickle.OptString ) = "g:a:";
		option( tlvpickle.Usage ) = "-g <guild_id> -a <game_id>";
	}

	rpc GuildGetGroupOrder ( GuildGetGroupOrderReq ) returns ( GuildGetGroupOrderResp ) {
		option( tlvpickle.CmdID ) = 74;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <guild_id>";
	}

	rpc GetGuildMemberJoinedCount( GetGuildMemberJoinedCountReq ) returns ( GetGuildMemberJoinedCountResp ) {
		option( tlvpickle.CmdID ) = 75;
		option( tlvpickle.OptString ) = "g:s:e:";
		option( tlvpickle.Usage ) = "-g<guild_id> -s<from_date> -e<to_date>";
	}

	rpc UpdateGameGuildOnlineUserCount ( UpdateGameGuildOnlineUserCountReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 76;
		option( tlvpickle.OptString ) = "g:u:c:";
		option( tlvpickle.Usage ) = "-g <game_id> -u <guild id> -c <user count>";
	}

	rpc GetAllDeletedGuildIdList ( GetAllDeletedGuildIdListReq ) returns ( GetAllDeletedGuildIdListResp ){
		option( tlvpickle.CmdID ) = 77;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc GuildSetUidNeedVerify ( GuildSetUidNeedVerifyReq ) returns ( GuildSetUidNeedVerifyResp ) {
		option( tlvpickle.CmdID ) = 78;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc GuildCheckUidNeedVerify ( GuildCheckUidNeedVerifyReq ) returns ( GuildCheckUidNeedVerifyResp ) {
		option( tlvpickle.CmdID ) = 79;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc GetTopGuildIdList ( GetTopGuildIdListReq ) returns ( GetTopGuildIdListResp ) {
		option( tlvpickle.CmdID ) = 80;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc UpdateGuildGameLimit( UpdateGuildGameLimitReq ) returns ( UpdateGuildGameLimitResp ) {
		option( tlvpickle.CmdID ) = 81;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc UnSetShortId( SetShortIdReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 82;
		option( tlvpickle.OptString ) = "g:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild id> -s <shot id>";
	}
	rpc BatchGetGroup( BatchGetGroupReq ) returns ( BatchGetGroupResp ){
		option( tlvpickle.CmdID ) = 83;
		option( tlvpickle.OptString ) = "g:r:a:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild id> -r <is_needrole> -a <is_needadmin>";
	}
	rpc TGroupGetUserCreateGroupCount( TGroupGetUserCreateGroupCountReq ) returns ( TGroupGetUserCreateGroupCountResp ) {
		option( tlvpickle.CmdID ) = 84;
		option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";
	}

	// 根据gameID获取备选的推荐公会ID列表
	rpc GetRecommendGuildIDListByGameID( GetRecommendGuildIDListByGameIDReq ) returns ( GetRecommendGuildIDListByGameIDResp ) {
		option( tlvpickle.CmdID ) = 85;
		option( tlvpickle.OptString ) = "g:l:d:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game id> -l <limitSize> -d <except guildID>";
	}

	rpc ModifyTGroupDesc( ModifyTGroupDescReq ) returns ( ModifyTGroupDescResp ) {
		option( tlvpickle.CmdID ) = 86;
		option( tlvpickle.OptString ) = "g:d:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <group_id> -d <desc>";
	}

	rpc GetTGroupByDisplayId( GetTGroupByDisplayIdReq ) returns ( GetTGroupByDisplayIdResp ) {
		option( tlvpickle.CmdID ) = 87;
		option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <group_id>";
	}

	rpc GetUserGroupIDListByGuildID( GetUserGroupIDListByGuildIDReq ) returns ( GetUserGroupIDListByGuildIDResp ) {
		option( tlvpickle.CmdID ) = 88;
		option( tlvpickle.OptString ) = "u:g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild id> ";
	}

	rpc TGroupSearchByGameId( TGroupSearchByGameIdReq ) returns ( TGroupSearchByGameIdResp ) {
		option( tlvpickle.CmdID ) = 89;
		option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id1,game_id2> -t [city_code]";
	}

	rpc TGroupGetUserJoinGroupList( TGroupGetUserJoinGroupListReq ) returns ( TGroupGetUserJoinGroupListResp ) {
		option( tlvpickle.CmdID ) = 90;
		option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> ";
	}

	rpc GetGroupMuteUserList( GetGroupMuteUserListReq ) returns ( GetGroupMuteUserListResp ) {
		option( tlvpickle.CmdID ) = 91;
		option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <group_id> ";
	}

	rpc GetAllGroupIdList( GetAllGroupIdListReq ) returns ( GetAllGroupIdListResp ) {
		option( tlvpickle.CmdID ) = 92;
		option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
	}

	rpc GetGroupMembersByUids( GetGroupMembersByUidsReq ) returns ( GroupMemberListResp ) {
		option( tlvpickle.CmdID ) = 93;
		option( tlvpickle.OptString ) = "g:u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <group_id> -u <uid>";
	}

	// 对指定用户检查所在公会的全部群 获取这些用户在群内管理权限
	rpc CheckUserGroupAdminInfoInAllGroup( CheckUserGroupAdminInfoInAllGroupReq ) returns ( CheckUserGroupAdminInfoInAllGroupResp ) {
		option( tlvpickle.CmdID ) = 94;
		option( tlvpickle.OptString ) = "g:u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -u <uid>";
	}

	rpc UpdateGuildApplyExceed( UpdateGuildApplyExceedReq ) returns ( UpdateGuildApplyExceedResp ) {
		option( tlvpickle.CmdID ) = 95;
		option( tlvpickle.OptString ) = "u:g:a:";
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -a <apply_id>";
	}

	rpc UpdateGroupApplyExceed( UpdateGroupApplyExceedReq ) returns ( UpdateGroupApplyExceedResp ) {
		option( tlvpickle.CmdID ) = 96;
		option( tlvpickle.OptString ) = "u:g:a:";
        option( tlvpickle.Usage ) = "-u <uid> -g <group_id> -a <apply_id>";
	}

	rpc GetGroupApplyByGroupId( GetGroupApplyByGroupIdReq ) returns ( GetGroupApplyByGroupIdResp ) {
		option( tlvpickle.CmdID ) = 97;
		option( tlvpickle.OptString ) = "u:g:";
        option( tlvpickle.Usage ) = "-u <uid> -g <group_id>";
	}

	// 判断用户是否在公会有头衔(会长 副会长 群主 群管理 or 普通银)
	// 注意如果目标用户不在该公会 也会返回用户没有头衔 为普通成员
	// 也就是该接口只用于判断是否有头衔 不能判断是否在公会
	rpc CheckUserGuildDuty( CheckUserGuildDutyReq ) returns ( CheckUserGuildDutyResp ) {
		option( tlvpickle.CmdID ) = 98;
		option( tlvpickle.OptString ) = "u:g:";
        option( tlvpickle.Usage ) = "-u <tagert uid> -g <guild id>";
	}

	rpc GuildCheckinSupplement ( GuildCheckinSupplementReq ) returns ( GetGuildCheckinResp ){
		option( tlvpickle.CmdID ) = 99;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <guild id> -u <uid>";
	}

	rpc GroupSetUidNeedVerify(GroupSetUidNeedVerifyReq) returns (GroupSetUidNeedVerifyResp){
		option( tlvpickle.CmdID ) = 100;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <guild id> -u <uid>";
	}

	rpc GroupCheckUidNeedVerify(GroupCheckUidNeedVerifyReq) returns (GroupCheckUidNeedVerifyResp){
		option( tlvpickle.CmdID ) = 101;
		option( tlvpickle.OptString ) = "g:u:";
		option( tlvpickle.Usage ) = "-g <guild id> -u <uid>";
	}

    //分段获取guild member list
    rpc GetGuildMemberListSegment( GetGuildMemberListSegmentReq ) returns ( GetGuildMemberListSegmentResp ){
		option( tlvpickle.CmdID ) = 111;
		option( tlvpickle.OptString ) = "g:o:l:";
		option( tlvpickle.Usage ) = "-g <guid id> -o <offset> -l <limit>";
	}

    rpc CreateSpecialGuild( CreateSpecialGuildReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 112;
        option( tlvpickle.OptString ) = "g:n:d:u:";
        option( tlvpickle.Usage ) = "-g <guild id> -n <guild name> -d <desc> -u <owner uid>";
    }

    rpc ModifyGameTopGuild( ModifyGameTopGuildReq ) returns( ModifyGameTopGuildResp ) {
        option( tlvpickle.CmdID ) = 113;
        option( tlvpickle.OptString ) = "g:r:";
        option( tlvpickle.Usage ) = "-g <gameid> -r <guild_id1,guild_id2,...>";
    }
    
    rpc ReportMemberGameLogin ( ReportMemberGameLoginReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 114;
 		option( tlvpickle.OptString ) = "u:g:l:";
 		option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -l <ly_game_id>";
 	}
	
	rpc GetMemberRecentGameLogin ( GetMemberRecentGameLoginReq ) returns ( GetMemberRecentGameLoginResp ){
 		option( tlvpickle.CmdID ) = 115;
 		option( tlvpickle.OptString ) = "u:g:";
 		option( tlvpickle.Usage ) = "-u <uid> -g <guild_id>";
 	}
	
	rpc GuildDonate ( GuildDonateReq ) returns ( GetGuildDonateResp ){
 		option( tlvpickle.CmdID ) = 116;
 		option( tlvpickle.OptString ) = "u:g:n:t:";
 		option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -n <donate_value> [-t <invalid>]";
 	}
	
	rpc GetGuildDonate ( GetGuildDonateReq ) returns ( GetGuildDonateResp ){
 		option( tlvpickle.CmdID ) = 117;
 		option( tlvpickle.OptString ) = "u:g:";
 		option( tlvpickle.Usage ) = "-u <uid> -g <guild_id>";
 	}
	
	rpc GetGuildDonateList ( GetGuildDonateListReq ) returns ( GetGuildDonateListResp ){
 		option( tlvpickle.CmdID ) = 118;
 		option( tlvpickle.OptString ) = "g:n:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -n <limit>";
 	}
	
	rpc GetGuildDonateCount ( GetGuildDonateCountReq ) returns ( GetGuildDonateCountResp ){
 		option( tlvpickle.CmdID ) = 119;
 		option( tlvpickle.OptString ) = "g:";
 		option( tlvpickle.Usage ) = "-g <guild_id>";
 	}
	
	rpc GetGuildYesterdayDonateCountBat ( GetGuildYesterdayDonateCountBatReq ) returns ( GetGuildYesterdayDonateCountBatResp ){
 		option( tlvpickle.CmdID ) = 120;
 		option( tlvpickle.OptString ) = "g:";
 		option( tlvpickle.Usage ) = "-g <guild_id>";
 	}
	
	rpc SetGuildMemberTitle ( SetGuildMemberTitleReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 121;
 		option( tlvpickle.OptString ) = "g:m:t:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -m <member_uid>, -t <title>";
 	}
	
	rpc GetGuildGameCountConfig (GetGuildGameCountConfigReq)returns(
	GetGuildGameCountConfigResp){
 		option( tlvpickle.CmdID ) = 122;
 		option( tlvpickle.OptString ) = "l:";
 		option( tlvpickle.Usage ) = "-l <level>";
	}

	rpc GetGuildExtraGameCount(GetGuildExtraGameCountReq) returns(GetGuildExtraGameCountResp){
 		option( tlvpickle.CmdID ) = 123;
 		option( tlvpickle.OptString ) = "g:";
 		option( tlvpickle.Usage ) = "-g <guild_id>";
	}

	rpc AddGuildGameV2( AddGuildGameV2Req ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 124;
		option( tlvpickle.OptString ) = "g:a:";
		option( tlvpickle.Usage ) = "-g <guild id> -a <game id list>";
	}

	rpc ModifyGroupGame(ModifyGroupGameReq)	returns (tlvpickle.SKBuiltinEmpty_PB){
		option( tlvpickle.CmdID ) = 125;
		option( tlvpickle.OptString ) = "g:o:a:";
		option( tlvpickle.Usage ) = "-g <guild id> -o <group Id> -a <game id>";
	}

	rpc GetGuildMemberTitleList (GetGuildMemberListSegmentReq) returns (
	GuildMemberListResp){
 		option( tlvpickle.CmdID ) = 126;
 		option( tlvpickle.OptString ) = "g:o:n:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -o <offset> -n <limit>";
	}
	
	rpc GetGuildCheckinListByUids (GetGuildCheckinListByUidsReq) returns (
	GetGuildCheckinListResp){
 		option( tlvpickle.CmdID ) = 127;
 		option( tlvpickle.OptString ) = "g:u:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -u <uid>";
	}

	rpc AddGuildExtraGameCount(AddGuildExtraGameCountReq) returns (tlvpickle.SKBuiltinEmpty_PB){
 		option( tlvpickle.CmdID ) = 128;
 		option( tlvpickle.OptString ) = "g:o:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -o <count> ";		
	}

	rpc CreateGuildOfficial(CreateGuildOfficialReq) returns(tlvpickle.SKBuiltinEmpty_PB)
	{
 		option( tlvpickle.CmdID ) = 129;
 		option( tlvpickle.OptString ) = "g:n:p:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -n <official_name> -p <permission>";		
	}

	rpc DelGuildOfficial(DelGuildOfficialReq) returns (tlvpickle.SKBuiltinEmpty_PB)
	{
 		option( tlvpickle.CmdID ) = 130;
 		option( tlvpickle.OptString ) = "g:o:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -o <official_id>";
	}

	rpc ModifyGuildOfficial(ModifyGuildOfficialReq) returns (tlvpickle.SKBuiltinEmpty_PB)
	{
 		option( tlvpickle.CmdID ) = 131;
 		option( tlvpickle.OptString ) = "g:o:n:p:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -o <official_id> -n <official_name> -p <permission>";
	}

	rpc GetGuildOfficialAll(GetGuildOfficialAllReq) returns(GetGuildOfficialAllResp)
	{
 		option( tlvpickle.CmdID ) = 132;
 		option( tlvpickle.OptString ) = "g:";
 		option( tlvpickle.Usage ) = "-g <guild_id>";	
	}

	rpc AddGuildOfficialMember(AddGuildOfficialMemberReq)returns(tlvpickle.SKBuiltinEmpty_PB)
	{
 		option( tlvpickle.CmdID ) = 133;
 		option( tlvpickle.OptString ) = "g:o:u:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -o <official_id> -u <uid>";
	}

	rpc RemoveGuildOfficialMember(RemoveGuildOfficialMemberReq)returns(tlvpickle.SKBuiltinEmpty_PB)
	{
		option( tlvpickle.CmdID ) = 134;
 		option( tlvpickle.OptString ) = "g:u:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -u <uid>";
	}

	rpc GetGuildOfficialMember(GetGuildOfficialMemberReq)returns(GetGuildOfficialMemberResp)
	{
		option( tlvpickle.CmdID ) = 135;
 		option( tlvpickle.OptString ) = "g:o:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -o <official_id>";
	}

	rpc GetGuildOfficialByUid(GetGuildOfficialByUidReq) returns(GetGuildOfficialByUidResp)
	{
		option( tlvpickle.CmdID ) = 136;
 		option( tlvpickle.OptString ) = "u:g:";
 		option( tlvpickle.Usage ) = "-u <uid> -g <guild_id>";
	}

	rpc GetGuildOfficialMemberMap(GetGuildOfficialMemberMapReq)returns(GetGuildOfficialMemberMapResp)
	{
		option( tlvpickle.CmdID ) = 137;
 		option( tlvpickle.OptString ) = "g:";
 		option( tlvpickle.Usage ) = "-g <guild_id>";	
	}

	rpc AddGuildOfficialMemberByName(AddGuildOfficialMemberByNameReq)returns(tlvpickle.SKBuiltinEmpty_PB)
	{
 		option( tlvpickle.CmdID ) = 138;
 		option( tlvpickle.OptString ) = "g:n:u:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -n <official_name> -u <uid>";
	}

	rpc GetGuildOfficialById(GetGuildOfficialByIdReq)
	returns (GetGuildOfficialByIdResp)
	{
 		option( tlvpickle.CmdID ) = 139;
 		option( tlvpickle.OptString ) = "g:o:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -o <official_id>";	
	}

	rpc UpdateGameGuildStarLevel(UpdateGameGuildStarLevelReq)
	returns (tlvpickle.SKBuiltinEmpty_PB)
	{
 		option( tlvpickle.CmdID ) = 140;
 		option( tlvpickle.OptString ) = "g:a:u:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -a <game_id> -u<value>";	
	}

	rpc UpdateGameGuildStarLevelBat(UpdateGameGuildStarLevelBatReq)
	returns (tlvpickle.SKBuiltinEmpty_PB)
	{
 		option( tlvpickle.CmdID ) = 141;
 		option( tlvpickle.OptString ) = "g:u:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -u<value>";	
	}

	rpc GetGuildByMultiCond (GetGuildByMultiCondReq)
	returns (GetGuildByMultiCondResp)
	{
 		option( tlvpickle.CmdID ) = 142;
 		option( tlvpickle.OptString ) = "g:a:u:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -a <game_id> -u<value>";	
	}	
	rpc GuildDeleteGames(GuildDeleteGamesReq) returns(tlvpickle.SKBuiltinEmpty_PB)
	{
 		option( tlvpickle.CmdID ) = 143;
 		option( tlvpickle.OptString ) = "g:a:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -a<game_id,game_id>";		
	}
	
	// 获取指定公会的 成员加入的历史流水记录
	rpc GetGuildJoinHistory(GetGuildJoinHistoryReq) returns (GetGuildJoinHistoryResp)
	{
 		option( tlvpickle.CmdID ) = 144;
 		option( tlvpickle.OptString ) = "g:o:n:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -o <offset> -n <limit>";	
	}

	rpc GetGuildGameListOld ( GuildIdReq ) returns ( GetGuildGameListResp ){
		option( tlvpickle.CmdID ) = 145;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <guild id>";
	}
	rpc GetGuildPopGames(GetGuildPopGamesReq)returns(GetGuildPopGamesResp)
	{
		option( tlvpickle.CmdID ) = 146;
		option( tlvpickle.OptString ) = "l:";
		option( tlvpickle.Usage ) = "-l<limit>";	
	}
	
	// 获取 指定用户的 加入公会的历史流水记录
	rpc GetUserJoinGuildHistory(GetUserJoinGuildHistoryReq)returns(GetUserJoinGuildHistoryResp)
	{
		option( tlvpickle.CmdID ) = 147;
		option( tlvpickle.OptString ) = "u:n:";
		option( tlvpickle.Usage ) = "-u <uid> -n <limit>";	
	}

	//修公会名数据用
	rpc ResetGuildName(ResetGuildNameReq) returns(tlvpickle.SKBuiltinEmpty_PB){
		option( tlvpickle.CmdID ) = 148;									// 命令号
        option( tlvpickle.OptString ) = "g:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g<guild_id>";		
	}

	rpc GetGroupByIds(GetGroupByIdsReq) returns(GetGroupByIdsResp)
	{
		option( tlvpickle.CmdID ) = 149;									// 命令号
        option( tlvpickle.OptString ) = "g:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g<group_id1,group_id2,group_id3...>";		
	}
	
	// 加入公会黑名单
	rpc SetGuildBlackList(SetGuildBlackListReq) returns(tlvpickle.SKBuiltinEmpty_PB)
	{
		option( tlvpickle.CmdID ) = 150;									// 命令号
        option( tlvpickle.OptString ) = "u:g:b:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<opt_uid> -g<guild_id> -b<black_list uid1,uid2,uid3...>";		
	}
	
	// 解除公会黑名单
	rpc RemoveGuildBlackList(RemoveGuildBlackListReq) returns(tlvpickle.SKBuiltinEmpty_PB)
	{
		option( tlvpickle.CmdID ) = 151;									// 命令号
        option( tlvpickle.OptString ) = "u:g:b:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<opt_uid> -g<guild_id> -b<black_list uid1,uid2,uid3...>";		
	}
	
	// 获取公会黑名单
	rpc GetGuildBlackList(GetGuildBlackListReq) returns(GetGuildBlackListResp)
	{
		option( tlvpickle.CmdID ) = 152;									// 命令号
        option( tlvpickle.OptString ) = "g:o:n:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g<guild_id> [-o<offset> -n<limit>]";		
	}
	
	// 用户是否在公会黑名单
	rpc CheckUidInBlackList(CheckUidInBlackListReq) returns(CheckUidInBlackListResp)
	{
		option( tlvpickle.CmdID ) = 153;									// 命令号
        option( tlvpickle.OptString ) = "u:g:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<uid> -g<guild_id>";		
	}

    // 公会状态
    rpc GetGuildStatus( GetGuildStatusReq ) returns ( GetGuildStatusRsp ){
	    option( tlvpickle.CmdID ) = 155;										// 命令号
        option( tlvpickle.OptString ) = "g:s";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id1,guild_id2,...> [-s <from slave db>]";
    }

  	rpc GetTGroupIdByDisplayId( GetTGroupIdByDisplayIdReq ) returns ( GetTGroupIdByDisplayIdResp ) {
		option( tlvpickle.CmdID ) = 156;
		option( tlvpickle.OptString ) = "d:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-d <display_id1,display_id2,...>";
	}
    // 获取某种/些类型的所有group
    rpc GetGroupIdsByType( GetGroupIdsByTypeReq ) returns ( GetGroupIdsByTypeResp ){
		option( tlvpickle.CmdID ) = 157;
		option( tlvpickle.OptString ) = "t:";
        option( tlvpickle.Usage ) = "-t <type1,type2,...,0-guild main/1-game main/2-game/3-temp/4-tgroup>";
    }
	
	// 查询tgroup禁言列表
    rpc GetTGroupMuteList( GetTGroupMuteListReq ) returns ( GetTGroupMuteListResp ){
		option( tlvpickle.CmdID ) = 158;
		option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g <group_id>";
    }
    
    rpc DeleteGuildTrans( DeleteGuildTransReq ) returns ( DeleteGuildTransResp ) {
		option( tlvpickle.CmdID ) = 159;
		option( tlvpickle.OptString ) = "u:s:t:q:";
        option( tlvpickle.Usage ) = "-u <uid> -s <from_guildid> -t <to_guildid> -q <quit_type>";
    }
    
    rpc BatchDeleteGuildTrans( BatchDeleteGuildTransReq ) returns ( BatchDeleteGuildTransResp ) {
		option( tlvpickle.CmdID ) = 160;
		option( tlvpickle.OptString ) = "u:s:t:q:";
        option( tlvpickle.Usage ) = "-u <uid1,uid2,uid3...> -s <from_guildid> -t <to_guildid> -q <quit_type>";
    }
    
    rpc GetGuildTrans( GetGuildTransReq ) returns ( GetGuildTransResp ) {
		option( tlvpickle.CmdID ) = 161;
		option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u uid";
    }
    
    rpc BatchGetGuildTrans( BatchGetGuildTransReq ) returns ( BatchGetGuildTransResp ) {
		option( tlvpickle.CmdID ) = 162;
		option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid1,uid2,uid3...>";
    }
	
	// 批量检查公会成员的官员权限信息
	rpc BatGetGuildOfficialByUids( BatGetGuildOfficialByUidsReq ) returns ( BatGetGuildOfficialByUidsResp ) {
		option( tlvpickle.CmdID ) = 163;
		option( tlvpickle.OptString ) = "u:g:";
        option( tlvpickle.Usage ) = "-u <uid1,uid2,uid3...> -g <guild_id>";
    }

    // 获取公会内 有职权的人的列表 （包括会长 各类公会管理员 群主 群管理）
	rpc GetGuildAllDutyList( GetGuildAllDutyListReq ) returns ( GetGuildAllDutyListResp ) {
		option( tlvpickle.CmdID ) = 164;
		option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g <guild_id>";
    }
    
	rpc GetGuildByShortId( GuildIdReq ) returns ( GuildResp ){
		option( tlvpickle.CmdID ) = 165;										// 命令号
        option( tlvpickle.OptString ) = "g:d:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild short id> -d <desc>";
	}
	
	rpc CreateGuildLimit( CreateGuildLimitReq ) returns ( CreateGuildLimitResp ){
		option( tlvpickle.CmdID ) = 166;							
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	}

	rpc AddMulticastRelations( AddMulticastRelationsReq ) returns ( AddMulticastRelationsResp ){
		option( tlvpickle.CmdID ) = 167;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc DelMulticastRelations( DelMulticastRelationsReq ) returns ( DelMulticastRelationsResp ){
		option( tlvpickle.CmdID ) = 168;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc AllocGroupDisplayId(AllocGroupDisplayIdReq) returns (AllocGroupDisplayIdResp) {
		option( tlvpickle.CmdID ) = 169;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc GenTGroupHeadImage(GenTGroupHeadImageReq) returns (GenTGroupHeadImageResp) {
		option( tlvpickle.CmdID ) = 170;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
}
