syntax="proto2";


package GuildAsync;

message Commands {
    enum Values {
        PublishGuildJoinEvent = 1;
        NotifySearchGuildChange = 2;        // 通知searchsvr 公会群成员变化
        NotifyGroupMeberChange = 3;         // 通知 searchsvr 公会群成员变化
    }
}

message PublishGuildJoinEventJobData {
    required uint32 uid = 1;
    required uint32 guild_id = 2;
}

message NotifySearchGuildChangeData{
    enum ChangeType{
        GUILD_CREATE = 1;
        GUILD_DISMISS = 2;
        GUILD_NAME_MODIFY = 3;
    }
    required uint32 change_type = 1;
    required uint32 guild_id = 2;
    optional string name = 3;
}

//
// 群成员变化
//
message UserGroupChange{
    required uint32 uid = 1;
    repeated uint32 group_ids = 2;    //uid_list
}

message NotifyGroupMeberChangeData {
    enum ChangeType {
        JOIN_GROUP = 1;
        QUIT_GROUP = 2;
        DISMISS_GROUP = 3;          //group_id
    }

    required uint32 change_type = 1;
    optional UserGroupChange user_group_change = 2;
    repeated uint32 dismissed_groups = 3;
}
