syntax="proto2";

// namespace
package recorder.panel;

// 必须import
import "common/tlvpickle/skbuiltintype.proto";


/*message RegisterWorkerReq {*/
/*    required string host = 1;*/
/*    required uint32 port = 2;*/
/*    [>required uint32 recorder_count = 3;<]*/
/*}*/
/*message RegisterWorkerResp {*/
/*}*/

message StartRecordReq {
    required uint32 channel_id = 1;
    optional uint32 app_id = 2;
    optional int64 at_ms = 3;
}
message StartRecordResp {
    required uint32 channel_id = 1;
    required uint32 app_id = 2;
    required int64 at_ms = 3;
}



message StopRecordReq {
    required uint32 channel_id = 1;
    optional uint32 app_id = 2;
}
message StopRecordResp {
}


//
//debug
//
message WorkerDumpInfo {
    required string worker_id = 1;
    optional string host = 2;
    optional int32 port = 3;
    optional int32 capacity = 4;
    optional int32 idle = 5;
}
message DumpWorkerReq{
    optional string worker_id = 1;
}
message DumpWorkerResp {
   repeated WorkerDumpInfo workers_in_zk = 1; 
   repeated WorkerDumpInfo workers_in_cache = 2; 
}

message TaskDumpInfo  {
    required string worker_id   = 1;
    optional  string recorder_id  = 2;
    optional uint32 channel_id = 3;
    optional uint64 seq = 4;
    optional int64 begin_at = 5;
}
message DumpWorkerTaskReq {
    required string worker_id = 1;
    optional string recorder_id = 2;
}
message DumpWorkerTaskResp {
    repeated TaskDumpInfo tasks = 1;
}

message DumpTaskReq {
    required uint32 channel_id = 1;
}
message DumpTaskResp {
    optional TaskDumpInfo task = 1;
}

service RecorderPanel {
	option( tlvpickle.Magic ) = 15656;		// 服务监听端口号

    rpc StartRecord( StartRecordReq ) returns ( StartRecordResp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channel_id>";
    }
    rpc StopRecord( StopRecordReq ) returns ( StopRecordResp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channel_id>";
    }

    //for debug
    rpc DumpWorker( DumpWorkerReq ) returns ( DumpWorkerResp ) {
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "w:";
        option( tlvpickle.Usage ) = "[-w <workder_id>]";
    }
    rpc DumpWorkerTask( DumpWorkerTaskReq ) returns ( DumpWorkerTaskResp ) {
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "w:r:";
        option( tlvpickle.Usage ) = "[-w <workder_id>] [-r <recorder_id>]";
    }
    rpc DumpTask( DumpTaskReq ) returns ( DumpTaskResp ) {
        option( tlvpickle.CmdID ) = 13;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channel_id>";
    }
}



// 内部使用
message WorkerData {
    required string worker_id = 1;
    required string host = 2;
    required int32 port = 3;
    required int32 capacity = 4;
}

message WorkerListData {
    repeated string workerid_list = 1;
}
message CacheStatusData {
    enum CacheReady{
        CACHE_NOT_READY = 0;
        CACHE_READY = 1;
    }
    required int32 is_ready = 1;  // CacheStatus
}



