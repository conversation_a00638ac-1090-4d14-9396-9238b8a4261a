syntax="proto2";

// namespace
package recorder;
option java_package ="com.yiyou.appsvr.recorder";

message RecorderWorkerData {
    required string id = 1;  //r00001, r000xx
    required string host = 2;
    required int32 port = 3;
    required int32 capacity = 4;
    optional int64 online_at = 5; //online ms
}
message RecordTaskData {
    required uint32 channel_id = 1;
    required uint64 seq = 2;
    required string worker_id = 3;
    required string recorder_id = 4;
    required int64 begin_at = 5;
}
