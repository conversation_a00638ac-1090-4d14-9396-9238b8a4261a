syntax="proto2";

package communicate;


message Command {
    required uint64 time = 1;
    message Request{
        enum CmdType
        {
            StartRec = 0;
            StopRec = 1;
        };
        required string appid = 1;
        required string channel_id = 2;
        required string seq_no = 3;
        required uint64 at = 4;
        required CmdType cmd = 5;
    }; 
    required Request req = 2;
}

message CmdSet {
    repeated Command r = 1;
}

message Command_Response{
    required string seq_no = 1;
    required int32   ret = 2;
}

