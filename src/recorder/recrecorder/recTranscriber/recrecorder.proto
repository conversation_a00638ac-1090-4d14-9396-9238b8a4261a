syntax="proto2";

// namespace
package recorder;

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

message RecordReq {
    optional string appid = 1 [default="0"];
    required string channel_id = 2;
    required uint32 worker_id = 3;
    required uint64 at_ms     = 4;
}

message RecordResp {
    required RecordReq req = 1;
    required int32   success = 2;
}

service RecRecorder {
    option( tlvpickle.Magic ) = 15700;
    rpc StartRecord( RecordReq ) returns ( RecordResp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channel_id>";
    }

    rpc StopRecord( RecordReq ) returns ( RecordResp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channel_id>";
    }
}

