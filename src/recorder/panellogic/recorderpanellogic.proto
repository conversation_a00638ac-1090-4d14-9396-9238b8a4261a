syntax="proto2";

// namespace
package recorder.panellogic;

// 必须import
import "common/tlvpickle/skbuiltintype.proto";



message HoldMicReq {
    required uint32 channel_id = 1;
    optional uint32 app_id = 2;
    optional uint32 channel_type = 3;
    optional uint32 mic_mode = 4;
    optional uint32 uid = 5;
    optional int64 at_ms = 6;
}

message HoldMicResp {
}

message ReleaseMicReq {
    required uint32 channel_id = 1;
    optional uint32 app_id = 2;
    optional uint32 channel_type = 3;
    optional uint32 mic_mode = 4;
    optional uint32 uid = 5;
    optional int64 at_ms = 6;
}
message ReleaseMicResp {
}

service RecorderPanelLogic {
	option( tlvpickle.Magic ) = 15655;		// 服务监听端口号

    //for test
    rpc HoldMic( HoldMicReq ) returns ( HoldMicResp ) {
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channel_id> -u <uid>";
    }
    rpc ReleaseMic( ReleaseMicReq ) returns ( ReleaseMicResp ) {
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channel_id> -u <uid>";
    }
}
