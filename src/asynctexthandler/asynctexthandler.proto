syntax="proto3";

import "common/tlvpickle/skbuiltintype.proto";

// namespace
package asynctexthandler;


message UserInfoContext
{
    string category = 1;
}

//AntiSpamDataType::GUILD_GROUP_NAME (see AntiSpamDataType)
message GuildModifyGroupNameContext
{
    uint32 guild_id = 1;
    uint32 group_id = 2;
}

//AntiSpamDataType::TEMP_GROUP_NAME (see AntiSpamDataType)
message TempGroupModifyNameContext
{
    uint32 group_id = 1;
}

//AntiSpamDataType::TGROUP_DESC (see AntiSpamDataType)
message TGroupModifyDescContext
{
    uint32 group_id = 1;
    uint32 new_game_id = 2;
    string city_code = 3;
    string city_name = 4;
}

//AntiSpamDataType::TGROUP_NAME (see AntiSpamDataType)
message TGroupModifyNameContext
{
    uint32 group_id = 1;
}

//AntiSpamDataType::GUILD_OR_GROUP_POST_NOTICE (see AntiSpamDataType)
message GroupPublishBulletinHandlerContext
{
    uint32 guild_id = 1;
    uint32 group_id = 2;
    uint32 group_type = 3;
	uint32 split_index = 4; //标题与内容的中间索引， text包含的内容为 标题 + “;” + 内容
}

//AntiSpamDataType::CHANNEL_NAME (see AntiSpamDataType)
message ChannelModifyNameContext
{
  uint32 channel_id = 1;
  uint32 oper_uid = 2;
  uint32 timestamp = 3;
  uint32 channel_type = 4;
  uint32 appid=5;
  uint32 market_id= 6;
    
}

//AntispamLogic::CHANNEL_TOPIC (see AntiSpamDataType)
message ChannelModifyDescContext
{
  uint32 channel_id = 1;
  uint32 oper_uid = 2;
  uint32 timestamp = 3;
  uint32 channel_type = 4;
  string desc = 5;          //话题
  string topic_detail =6;   //话题内容
  string oper_platform = 7; //操作平台
  string oper_app = 8; //操作app
}

//AntispamLogic::CHANNEL_TOPIC_DETAIL (see AntiSpamDataType)
message ChannelModifyTopicDetailContext
{
  uint32 channel_id = 1;
  uint32 oper_uid = 2;
  uint32 timestamp = 3;
  uint32 channel_type = 4;
  string desc = 5;          //话题
  string topic_detail =6;   //话题内容
  string oper_platform = 7; //操作平台
  string oper_app = 8; //操作app
}

// AntispamLogic::CHNNNEL_WELCOME (see AntiSpamDataType)
message ChannelModifyWelcomeMsgContext           
{
  uint32 channel_id = 1;
  uint32 oper_uid = 2;
  uint32 timestamp = 3;
  uint32 channel_type = 4;
}

message TextCheckContext
{
    string text = 1;    
    uint32 uid = 2;
    uint32 data_type = 3;    //应用场景
    uint32 result = 4;      //结果 see TextCheckResp::RESULT (antispamlogic.proto)
    bytes  cxt = 5;         //上下文
    string biz_record_id = 6;
    string account = 7;
    uint32 produce_time = 8;
}

message GuildInfoModifyContext
{
    uint32 guild_id = 1;
    uint32 timestamp = 2;
    string before_text = 3;

    uint32 app_id    = 4;
    uint32 market_id = 5;
}

message GuildCircleTopicContext
{
    uint32 guild_id      = 1;
    uint32 timestamp     = 2;
    string topic_title   = 3;  
    string topic_content = 4;
    repeated string topic_img_keylist = 5;
    bool is_highlight = 6;
}

message GuildCircleCommentContext
{
    uint32 guild_id       = 1;
    uint32 timestamp      = 2;
    uint32 topic_id       = 3; 
    uint32 ref_comment_id = 4;
    string content        = 5;
    repeated string img_keylist  = 6;
}


message ModifyNicknameContext
{
  bool prefix_valid = 1;
  uint32 app_id = 2;
  uint32 market_id = 3;
  string device_id_hex = 4;
}

message ModifyGameNickContext
{
  uint32 timestamp = 1;
  uint32 appid = 2;
  uint32 market_id = 3;
  uint32 req_tag_type = 4;
  bytes tag_info = 5;
  string oss_tag_list_str = 6;
  string oss_game_tag_list_str = 7;
  string oss_game_tag_nick_str = 8;
  /*uint32 check_tag_id = 9;*/
  /*uint32 check_text = 10;*/
}


message GuildOrGroupEnterVerifyContext
{
  uint32 need_verify = 1;
  string question = 2;
  string answer = 3;
  uint32 group_id = 4;
}

