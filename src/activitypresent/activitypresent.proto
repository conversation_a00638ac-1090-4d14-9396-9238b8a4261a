syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package activitypresent;

message StActivityPresentBriefConfig
{
	uint32 item_id = 1;
	uint32 price = 2;				// 价格
	uint32 price_type = 3;			// PresentPriceType(userpresent.proto)
	uint32 score = 4;				// 收到一个礼物 收礼者 增加的 积分
	uint32 charm = 5;				// 收到一个礼物 收礼者 增加的 魅力值
	uint32 rich_value = 6;			// 送出一个礼物 送礼者 增加的土豪值
}

/****** 礼物活动 ******/

enum PresentRankingListType
{
	option allow_alias = true;
	
	List_Unknown = 0;
	
	// 通用
	List_Recv = 1;			// 获赠
	List_Send = 2;			// 送出
	
	//开学季
	List_Term_Begin_Min = 3;
	List_XueJing = 3;		// 学精（铅笔150个，书包60个，奖状30个）
	List_XueBa = 4;			// 学霸（铅笔100个，书包40个，奖状20个）
	List_XueZha = 5;		// 学渣（铅笔50个，书包20个，奖状10个）
	List_XueCan = 6;		// 学残（铅笔25个，书包10个，奖状 5个）
	List_Term_Begin_Max = 6;
	
	List_National_Day_2018_Min = 7;
	List_Medal_King = 7;		// 勋章之王
	List_Yu_Di = 8;				// 玉帝
	List_Yue_Lao = 9;			// 月老
	List_Chang_E = 10;			// 嫦娥
	List_Hou_Yi = 11;			// 后羿
	List_Wu_Gang = 12;			// 吴刚
	List_National_Day_2018_Max = 12;

	List_Halloween_Min = 13;
	List_Vampire = 13;			// 吸血鬼
	List_Mummy = 14;			// 木乃伊
	List_Frankenstein = 15;		// 科学怪人
	List_Skeleton = 16;			// 小骷髅
	List_Halloween_Max = 16;
	
	List_Singles_Day_Min = 17;
	List_Singles_Day_Receive_Min = 17;
	// 17. 佳偶天成
	// 18. 心心相印
	// 19. 成双成对
	// 20. 坠入爱河
	List_Singles_Day_Receive_Max = 20;
	List_Singles_Day_Send_Min = 21;
	// 21. 助攻达人
	// 22. 恋爱专家
	// 23. 脱单圣手
	// 24. 情感导师
	List_Singles_Day_Send_Max = 24;
	List_Singles_Day_Max = 24;
	List_Singles_Day_Send_Item = 25;	// 虚构的榜单类型，用来传送礼数量配置
	
	List_Anniversary_Receive = 26;	// 收礼-年度巅峰人气王
	List_Anniversary_Send = 27;	// 送礼-年度杰出大人物
	List_Anniversary_Send_TT = 28;	// 送礼-TT房间
	List_Anniversary_Mic_Receive_Min = 31;	// 收礼麦位框min
	List_Anniversary_Mic_Receive_Max = 35;	// 收礼麦位框max
	List_Anniversary_Mic_Send_Min = 38;	// 送礼麦位框min
	List_Anniversary_Mic_Send_Max = 42;	// 送礼麦位框max
	List_SpringFestival_Rec_Min = 43;	//收礼勋章min
	List_SpringFestival_Rec_Max = 46;	//收礼勋章max
	List_SpringFestival_Send_Min = 47;	//送礼勋章min
	List_SpringFestival_Send_Max = 50;	//送礼勋章max
	List_SpringFestival_Rec = 51;		//鸿运总榜
	List_SpringFestival_Send = 52;		//财神总榜
	List_SpringFestival_Room = 53;		//房间总榜
	List_SpringFestival_Rec_Daily = 54;		//鸿运日榜
	List_SpringFestival_Send_Daily = 55;		//财神日榜
	List_SpringFestival_Room_Daily = 56;		//房间日榜
	
	List_Lantern_Min = 60;
	List_Lantern_Send = 60;				// 元宵——表白榜
	List_Lantern_Receive = 61;			// 元宵——心意榜
	List_Lantern_Score_Min = 62;		// 元宵——积分勋章min
	List_Lantern_Score_Max = 65;		// 元宵——积分勋章max	
	List_Lantern_Max = 65;
	
	List_Fools_Day_Send = 67;			// 愚人节——表白榜
	List_Fools_Day_Receive = 68;		// 愚人节——心意榜
	List_Fools_Day_Medal_Min = 69;		// 愚人节——勋章min
	List_Fools_Day_Medal_Max = 72;		// 愚人节——勋章max
	
	List_May_Day_Send = 74;				// 劳动节——贡献榜
	List_May_Day_Channel = 75;			// 劳动节——房间榜
	List_May_Day_Medal_Min = 76;		// 劳动节——勋章min
	List_May_Day_Medal_Max = 79;		// 劳动节——勋章max
	
	List_Network_Valentines_Day_Send = 80;	// 520网络情人节——送礼榜
	List_Network_Valentines_Day_Recv = 81;	// 520网络情人节——收礼榜
	List_Network_Valentines_Day_Medal_Min = 82;
	List_Network_Valentines_Day_Medal_Send_Min = 82;	// 520网络情人节——送礼勋章min
	List_Network_Valentines_Day_Medal_Send_Max = 85;	// 520网络情人节——送礼勋章max
	List_Network_Valentines_Day_Medal_Recv_Min = 86;	// 520网络情人节——收礼勋章min
	List_Network_Valentines_Day_Medal_Recv_Max = 89;	// 520网络情人节——收礼勋章max
	List_Network_Valentines_Day_Medal_Max = 89;
	
	List_Magpie_Festival_Send = 90;	// 2018七夕——送礼榜
	List_Magpie_Festival_Recv = 91;	// 2018七夕——收礼榜
	List_Magpie_Festival_Medal_Min = 92;
	List_Magpie_Festival_Medal_Send_Min = 92;	// 2018七夕——送礼勋章min
	List_Magpie_Festival_Medal_Send_Max = 94;	// 2018七夕——送礼勋章max
	List_Magpie_Festival_Medal_Recv_Min = 95;	// 2018七夕——收礼勋章min
	List_Magpie_Festival_Medal_Recv_Max = 97;	// 2018七夕——收礼勋章max
	List_Magpie_Festival_Medal_Max = 97;
	
	List_Mid_Autumn_Festival_Send = 98;	// 2018中秋——送礼榜
	List_Mid_Autumn_Festival_Recv = 99;	// 2018中秋——收礼榜
	List_Mid_Autumn_Festival_Medal_Min = 100;
	List_Mid_Autumn_Festival_Medal_Send_Min = 100;	// 2018中秋——送礼勋章min
	List_Mid_Autumn_Festival_Medal_Send_Max = 103;	// 2018中秋——送礼勋章max
	List_Mid_Autumn_Festival_Medal_Recv_Min = 104;	// 2018中秋——收礼勋章min
	List_Mid_Autumn_Festival_Medal_Recv_Max = 107;	// 2018中秋——收礼勋章max
	List_Mid_Autumn_Festival_Medal_Max = 107;
	
	List_National_Day_2018_Send = 108;	// 2018国庆节——送礼榜
	List_National_Day_2018_Recv = 109;	// 2018国庆节——收礼榜
	List_National_Day_2018_Medal_Min = 110;
	List_National_Day_2018_Medal_Send_Min = 110;	// 2018国庆节——送礼勋章min
	List_National_Day_2018_Medal_Send_Max = 113;	// 2018国庆节——送礼勋章max
	List_National_Day_2018_Medal_Recv_Min = 114;	// 2018国庆节——收礼勋章min
	List_National_Day_2018_Medal_Recv_Max = 117;	// 2018国庆节——收礼勋章max
	List_National_Day_2018_Medal_Max = 117;
	
	List_Year_Award_Game_SignUp_2018_Send = 118;	// 2018年度盛典 -- 报名阶段送礼榜
	
	List_Year_Award_Game_KnockOut_2018_Send = 119;	// 2018年度盛典 -- 淘汰赛送礼榜
	List_Year_Award_Game_KnockOut_2018_Recv = 120;	// 2018年度盛典 -- 淘汰赛收礼榜
	
	List_New_Years_Day_2019_Send = 121;	// 2019元旦——送礼榜
	List_New_Years_Day_2019_Recv = 122;	// 2019元旦——收礼榜
	List_New_Years_Day_2019_Medal_Min = 123;
	List_New_Years_Day_2019_Medal_Send_Min = 123;	// 2019元旦——送礼勋章min
	List_New_Years_Day_2019_Medal_Send_Max = 127;	// 2019元旦——送礼勋章max
	List_New_Years_Day_2019_Medal_Recv_Min = 128;	// 2019元旦——收礼勋章min
	List_New_Years_Day_2019_Medal_Recv_Max = 132;	// 2019元旦——收礼勋章max
	List_New_Years_Day_2019_Medal_Max = 132;
	
	List_Catching_Nian_2019_Medal_Min = 135;
	List_Catching_Nian_2019_Medal_Send_Min = 135;	// 2019年兽大作战 -- 送礼勋章min
	List_Catching_Nian_2019_Medal_Send_Max = 139;	// 2019年兽大作战 -- 送礼勋章max
	List_Catching_Nian_2019_Medal_Recv_Min = 140;	// 2019年兽大作战 -- 收礼勋章min
	List_Catching_Nian_2019_Medal_Recv_Max = 144;	// 2019年兽大作战 -- 收礼勋章max
	List_Catching_Nian_2019_Medal_Max = 144;

	List_Valentineday_Day_2019_Min = 152;
	List_Valentineday_Day_2019_Send_Min = 152; //元宵情人节活动送礼榜阶段奖励榜Min。
	List_Valentineday_Day_2019_Send_Max = 156; //元宵情人节活动送礼榜阶段奖励榜Max。
	List_Valentineday_Day_2019_Recv_Min = 157; //元宵情人节活动收礼榜阶段奖励榜Min。
	List_Valentineday_Day_2019_Recv_Max = 161; //元宵情人节活动收礼榜阶段奖励榜Max。
	List_Valentineday_Day_2019_Max = 161;
	List_Valentineday_Day_2019_Love_Letter = 162; //情书列表
	List_Valentineday_Day_2019_Idol_List   = 163; //idol列表，整个活动期间的
	List_Valentineday_Day_2019_Fans_List   = 164; //fans列表，整个活动期间的
	List_Valentineday_Day_2019_Send_Daily  = 165; //发送天奖励列表
	List_Valentineday_Day_2019_Recv_Daily  = 166; //接受天奖励列表
	
	List_Goddess_2019_Medal_Min = 170;
	List_Goddess_2019_Medal_Send_Min = 170;	// 2019女神节 -- 送礼勋章min
	List_Goddess_2019_Medal_Send_Max = 174;	// 2019女神节 -- 送礼勋章max
	List_Goddess_2019_Medal_Recv_Min = 175;	// 2019女神节 -- 收礼勋章min
	List_Goddess_2019_Medal_Recv_Max = 179;	// 2019女神节 -- 收礼勋章max
	List_Goddess_2019_Medal_Max = 179;
}

enum PresentActivityId
{
	PresentActivityId_Unknown = 0;
	Tanabata_Festival = 1;	// 七夕活动
	Honors_Day = 2;			// 开学活动
	National_Day = 3;		// 国庆节活动
	Halloween = 4;			// 万圣节活动
	Singles_Day = 5;		// 光棍节活动
	Anniversary = 6;		// 周年庆典活动
	Spring_Festival = 7;	// 春节活动
	Lantern_Festival = 8;	// 元宵节活动
	Fools_Day = 9;			// 愚人节活动
	World_Earth_Day = 10;	// 世界地球日
	May_Day = 11;			// 五一劳动节
	Network_Valentines_Day = 12;	// 520网络情人节
	Childrens_Day = 13;		// 六一儿童节
	Dragon_Boat_Festival = 14;	// 端午节
	TT_Anniversary = 15;	// TT周年庆
	Magpie_Festival_2018 = 16;	// 2018七夕活动
	Medal_Collecting = 17;	// 勋章集邮
	Mid_Autumn_Festival = 18;	// 中秋节活动
	National_Day_2018 = 19;	// 2018国庆节活动
	Year_Award_Game_SignUp_2018 = 20;	// 2018年度盛典 -- 报名阶段
	Year_Award_Game_KnockOut_2018 = 21;	// 2018年度盛典 -- 淘汰赛
	New_Years_Day_2019 = 22;	// 2019元旦活动
	Catching_Nian_2019 = 23;	// 2019年兽大作战
	Valentine_Day_2019 = 24;    //2019元宵情人节
	Goddess_2019 = 25;			// 2019女神节
	White_Valentines_Day_2019 = 26;	// 2019白色情人节
}

enum PresentFromType
{
	Present_TT = 0;				// TT房间礼物
	Present_Happy_Center = 2;	// 欢城直播礼物
}

message StActivityPresent
{
	uint32 item_id = 1;
	string icon_url = 2;
	uint32 activity_id = 3;
	string extend = 4;
	uint32 show_index = 5;
}

message StPresentCount
{
	uint32 item_id = 1;
	uint32 count = 2;
	uint32 list_type = 3;
}

message StIntKeyValue
{
	uint32 key = 1;
	uint32 value = 2;
}

message StStrKeyValue
{
	string key = 1;
	string value = 2;
}

// 达到指定榜单需要的礼物数量
message StActivityListDetail
{
	uint32 list_type = 1;
	repeated StPresentCount item_list = 2;
}

// 获取礼物活动信息
message GetPresentActivityInfoReq
{
	uint32 activity_id = 1;
}

message GetPresentActivityInfoResp
{
	string title = 1;
	string banner_url = 2;
	uint32 begin_time = 3;
	uint32 end_time = 4;
	repeated StActivityPresent present_list = 5;
	uint32 activity_id = 6;
	repeated StActivityListDetail detail_list = 7;
	string broadcast_img_url = 8;
	string jump_url = 9;
	string back_img_url = 10;
	string icon_img_url = 11;
	bytes activity_extent_info = 12;
}

message StPresentActUserFansInfo
{
	uint32 uid = 1;
	uint32 rank_value = 2;	
	uint32 ranking = 3;
}

message StPresentActUserExtentInfo
{
	uint32 uid = 1;
	uint32 activity_id = 2;
	uint32 list_type = 3;
	uint32 ranking = 4;
	string extent = 5;
}

message StPresentActUserRankInfo
{
	uint32 uid = 1;
	uint32 item_id = 2;
	uint32 list_type = 3;	// PresentRankingListType
	uint32 rank_value = 4;
	uint32 ranking = 5;
	uint32 activity_id = 6;
	uint32 channel_id = 7;	// 用于房间排行榜
	repeated StPresentActUserFansInfo fans_list = 8;
}

// 获取礼物活动排行榜
message GetPresentActivityRankingListReq
{
	uint32 item_id = 1;
	uint32 list_type = 2;	//PresentRankingListType
	uint32 offset = 3;
	uint32 limit = 4;
	uint32 activity_id = 5;
}

message GetPresentActivityRankingListResp
{
	repeated StPresentActUserRankInfo rank_list = 1;
	uint32 total_count = 2;
}

// 获取用户的活动信息
message GetPresentActivityUserInfoReq
{
	uint32 uid = 1;
	uint32 activity_id = 2;
	uint32 list_type = 3;
}

message GetPresentActivityUserInfoResp
{
	bytes user_info = 1;		// 自定义的结构体
	uint32 consume_value = 2;
	uint32 ex_value = 3;
	uint32 ex_value2 = 4;
	bytes user_info2 = 5;
}

// 获取用户的礼物活动排名
message GetPresentActivityRankingByUidReq
{
	uint32 uid = 1;
	uint32 item_id = 2;
	uint32 list_type = 3;	//PresentRankingListType
	uint32 activity_id = 4;
	bool get_upper = 5;	// 是否查询上一名的数据
	bool get_follow = 6;	// 是否查询下一名的数据
}

message GetPresentActivityRankingByUidResp
{
	StPresentActUserRankInfo rank_info = 1;
	StPresentActUserRankInfo upper_rank_info = 2;
	StPresentActUserRankInfo follow_rank_info = 3;
}

// 记录活动中送出的礼物
message RecordActivitySendPresentReq
{
	uint32 uid = 1;
	uint32 target_uid = 2;
	string order_id = 3;
	StActivityPresentBriefConfig item_config = 4;
	uint32 item_count = 5;
	uint32 present_from_type = 6;	// PresentFromType
	uint32 send_time = 7;
	uint32 channel_id = 8;
	uint32 channel_type = 9;		//与 ga::ChannelType 的类型保持一致
}

// 重新计算用户在活动中的信息
message RecalculateUserActivityInfoReq
{
	uint32 uid = 1;
	uint32 activity_id = 2;
	uint32 list_type = 3;
}

// 重新计算活动的信息
message RecalculateActivityInfoReq
{
	uint32 activity_id = 1;
	uint32 list_type = 2;
}

enum ActivityPhaseChangeType
{
	Phase_Change_None = 0;
	Phase_Change_Daily = 1;
}

// 通知活动阶段更新
message NotifyActivityPhaseChangeReq
{
	uint32 type = 1; //	ActivityPhaseChangeType
}

//情书结构
message LoveLetterInfo
{
	uint32 send_uid= 1; //写信人UID
	uint32 target_uid = 2;//收信人UID
	uint32 letter_status = 3; //是否已经写过。0没写过，1已经写过。
	uint32 letter_type = 4;//样式 
	string letter_text = 5;//内容
	uint32 date_int = 6; //日期
	uint32 letter_id = 7;
}

//情书墙列表
message LoveLetterInfoList
{
	repeated LoveLetterInfo love_letter_list = 1;
}

//写情书
message modifyLoveLetterReq
{
	enum LoveLetterType{
		Love_Letter_ty0 = 0;
		Love_Letter_ty1 = 1;
		Love_Letter_ty2 = 2;
	}
	uint32 letter_id  = 1;       //情书ID
	uint32 send_uid   = 2;     
	uint32 target_uid = 3;       //收信人UID
	uint32 activity_id = 4;      //活动ID 
	uint32 date_int = 5;         //日期，转换为天数。
	uint32 letter_type = 6;      //enum LoveLetterType
	string letter_text = 7;    	//情书内容
}

message modifyLoveLetterResp
{
	uint32 code = 1; //设置情书返回状态码。
	string new_content = 2; //修改后情书内容
}

message GetLoveLetterListReq
{
	uint32 date_int = 1;
}

message GetLoveLetterListResp
{
	uint32 code = 1;
	repeated LoveLetterInfo love_letter_list = 2;
}

// 活动奖励
message ActCommonAward
{
	enum ActCommonAwardType {
		Act_Award_None = 0;
		Act_Award_Red_Diamond = 1;				// 红钻
		Act_Award_Medal = 2;					// 勋章
		Act_Award_Headwear = 3;					// 麦位框
		Act_Award_Package = 4;					// 背包包裹
		Act_Award_Channel_Enter_Effect = 5;		// 进房坐骑特效
	}
	
	uint32 award_type = 1;		// ActCommonAwardType
	uint32 award_id = 2;
	string str_award_id = 3;	// 某些奖励要用字符串做id，如房间坐骑
	uint32 award_num = 4;
	uint32 award_time = 5;
	uint32 award_lmt = 6;
	string award_ver = 7;
}

// 消费记录
message ConsumeRecord
{
	uint32 uid = 1;
	uint32 consume_value = 2;
	uint32 ts = 3;
	uint32 activity_id = 4;
	string order_id = 5;	// 用来保证记录唯一
}

// 战况播报
message ActBattleSubMsg
{
	enum ActBattleSubMsgType {
		Act_Battle_Sub_Msg_Normal = 0;
		Act_Battle_Sub_Msg_Nickname = 1;		// 用户昵称
		Act_Battle_Sub_Msg_Prefix = 2;			// 文案前缀
	}
	
	uint32 type = 1;	// ActBattleSubMsgType
	string content = 2;
}

message ActBattleProgress
{
	uint32 time_ts = 1;
	string msg = 2;
	repeated ActBattleSubMsg sub_msg_list = 3;
}

message GetBattleProgressListReq
{
	uint32 activity_id = 1;
	uint32 list_type = 2;
	uint32 offset = 3;
	uint32 limit = 4;
}

message GetBattleProgressListResp
{
	repeated ActBattleProgress progress_list = 1;
}

// 修改用户的活动信息
message ModifyUserActivityInfoReq
{
	enum ModifyUserInfoType
	{
		Modify_Type_None = 0;
		Modify_Type_Love_Letter = 1;
	}

	uint32 uid = 1;
	uint32 activity_id = 2;
	uint32 op_type = 3;
	uint32 op_value = 4;
	string op_data = 5;
}

message ModifyUserActivityInfoResp
{
}

// 测试接口
message DoTestReq
{
	uint32 uid = 1;
	uint32 activity_id = 2;
	uint32 op_type = 3;	// 1.push 战报
	uint32 op_value = 4;
	string op_data = 5;
}

message DoTestResp
{
}

message IdolInfo
{
	uint32 uid = 1;
	uint32 rank_value = 2;
}

message IdolInfoList
{
	repeated IdolInfo idol_info_list = 1;
}

message GetIdolListReq
{
	uint32 offset = 1;
	uint32 limit  = 2;
}

message GetIdolListResp
{
	uint32 code = 1;
	IdolInfoList idol_info_list = 2;
}

enum PresentActivityExtentInfoType
{	
	List_Lanternfestival_2019_IdolList = 0;
	List_Lanternfestival_2019_FansList = 1;
	List_Lanternfestival_2019_Test = 1024;
	List_Lanternfestival_2019_Clear = 1025;
}

/*****拿活动数据或者玩家数据通用接口。通过extent_info_type区别数据类型。******/
message GetPresentActivityExtentInfoReq 
{
	uint32 activity_id = 1;
	uint32 extent_info_type = 2; //信息类型。
}

message GetPresentActivityExtentInfoResp
{
	uint32 code = 1;
	bytes activity_extent_info = 2; //信息类型。
}

message GetPresentActivityUserExtentInfoReq 
{
	uint32 activity_id = 1;
	uint32 uid = 2;
	uint32 extent_info_type = 3;
	string extent_param = 4;
}

message GetPresentActivityUserExtentInfoResp
{
	uint32 code = 1;
	bytes user_extent_info = 2; //信息类型。
}
/***拿数据通用接口结束***/

/******************** 礼物活动内部的数据 ********************/
message ActDailyRanking
{
	repeated StPresentActUserRankInfo rank_list = 1;
	uint32 date = 2;
}

message ActHistoryRanking
{
	repeated ActDailyRanking history_list = 1;
	uint32 last_modified_date = 2;
	bool disable_today_list = 3;
}

message LanternFestivalUserData
{
	uint32 uid = 1;
	uint32 activity_id = 2;
	uint32 phase = 3;
	uint32 score = 4;
	repeated StPresentCount present_list = 5;
	uint32 last_modified_ts = 6;
}

message WorldEarthDayUserData
{
	uint32 uid = 1;
	uint32 activity_id = 2;
	repeated StIntKeyValue history_rank_list = 3;
	uint32 last_modified_date = 4;
	uint32 ex_value = 5;
}

message MayDayUserData
{
	uint32 uid = 1;
	uint32 activity_id = 2;
	uint32 phase = 3;
	uint32 total_score = 4;
	uint32 today_score = 5;
	uint32 present_value = 6;
	uint32 last_modified_ts = 7;	
}

message UserDailyInfoData
{
	uint32 uid = 1;
	uint32 activity_id = 2;
	uint32 list_type = 3;
	uint32 last_modified_date = 4;
	uint32 today_red_diamond = 5;
	uint32 today_tbean = 6;
}

enum LoveLetterLinkType
{
	List_Online = 0;
	List_Gray   = 1;
	List_Test   = 2;
}



// 获取当前 各种送礼/收礼活动通用的 排行榜黑名单用户列表 
// 该接口数据是从配置中心SDK获取，因此该接口只提供给WEB服务来获取数据
// 其他服务 可以直接使用通用配置中心SDK来获取黑名单数据 (src/commConfigCenterSDK/rankblacklist/RankBlackUidListWatcher.h)
message GetRankBlackUidListReq
{

}
message GetRankBlackUidListResp
{
	repeated uint32 recv_rank_uid_list = 1;
	repeated uint32 send_rank_uid_list = 2;
}


service ActivityPresent {
	option( tlvpickle.Magic ) = 15610;		// 服务监听端口号
		
	rpc GetPresentActivityInfo( GetPresentActivityInfoReq ) returns( GetPresentActivityInfoResp ){
		option( tlvpickle.CmdID ) = 1;										
        option( tlvpickle.OptString ) = "a:";							
        option( tlvpickle.Usage ) = "-a <activity_id>";
	}
	
	rpc GetPresentActivityRankingList( GetPresentActivityRankingListReq ) returns( GetPresentActivityRankingListResp ){
		option( tlvpickle.CmdID ) = 2;										
        option( tlvpickle.OptString ) = "a:x:t:o:n:";							
        option( tlvpickle.Usage ) = "-a <activity_id> -x <item_id> -t <list_type 1.send 2.receive> [-o <offset> -n <limit>]";
	}
	
	rpc GetPresentActivityRankingByUid( GetPresentActivityRankingByUidReq ) returns( GetPresentActivityRankingByUidResp ){
		option( tlvpickle.CmdID ) = 3;										
        option( tlvpickle.OptString ) = "a:u:x:t:";							
        option( tlvpickle.Usage ) = "-a <activity_id> -u <uid> -x <item_id> -t <list_type 1.send 2.receive>";
	}
	
	rpc RecordActivitySendPresent( RecordActivitySendPresentReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 4;										
        option( tlvpickle.OptString ) = "u:t:x:o:n:p:";							
        option( tlvpickle.Usage ) = "-u <uid> -t <target_uid> -x <item_id> -n <item_count> -o <order_id> -p <present_from_type>";
	}
	
	rpc RecalculateUserActivityInfo( RecalculateUserActivityInfoReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 5;										
        option( tlvpickle.OptString ) = "u:a:";							
        option( tlvpickle.Usage ) = "-u <uid> -a <activity_id>";
	}
	
	rpc NotifyActivityPhaseChange( NotifyActivityPhaseChangeReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 6;										
        option( tlvpickle.OptString ) = "t:";							
        option( tlvpickle.Usage ) = "-t <type 1.day>";
	}
	
	rpc GetPresentActivityUserInfo( GetPresentActivityUserInfoReq ) returns( GetPresentActivityUserInfoResp ){
		option( tlvpickle.CmdID ) = 7;										
        option( tlvpickle.OptString ) = "u:a:t:";							
        option( tlvpickle.Usage ) = "-u <uid> -a <activity_id> -t <list_type>";
	}
	
	rpc RecalculateActivityInfo( RecalculateActivityInfoReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 8;										
        option( tlvpickle.OptString ) = "a:t:";							
        option( tlvpickle.Usage ) = "-a <activity_id> -t <list_type>";
	}
	
	rpc GetBattleProgressList( GetBattleProgressListReq ) returns( GetBattleProgressListResp ){
		option( tlvpickle.CmdID ) = 9;										
        option( tlvpickle.OptString ) = "a:t:o:n:";							
        option( tlvpickle.Usage ) = "-a <activity_id> -t <list_type> [-o <offset> -n <limit>";
	}
	
	rpc ModifyUserActivityInfo( ModifyUserActivityInfoReq ) returns( ModifyUserActivityInfoResp ){
		option( tlvpickle.CmdID ) = 10;										
        option( tlvpickle.OptString ) = "u:a:t:n:s:";							
        option( tlvpickle.Usage ) = "-u <uid> -a <activity_id> -t <op_type 1.love_letter> -n <op_value> -s <op_data>";
	}

	rpc GetPresentActivityUserExtentInfo ( GetPresentActivityUserExtentInfoReq ) returns ( GetPresentActivityUserExtentInfoResp ){
		option( tlvpickle.CmdID ) = 11;
		option( tlvpickle.OptString ) = "u:a:t:e:";
		option( tlvpickle.Usage ) = "-u <uid>";
	}

	rpc GetPresentActivityExtentInfo ( GetPresentActivityExtentInfoReq ) returns ( GetPresentActivityExtentInfoResp ){
		option( tlvpickle.CmdID ) = 12;
		option( tlvpickle.OptString ) = "u:t:";
		option( tlvpickle.Usage ) = "-u <uid>";
	}

	rpc modifyLetter( modifyLoveLetterReq ) returns ( modifyLoveLetterResp ){
		option( tlvpickle.CmdID ) = 13;
		option( tlvpickle.OptString ) = "u:d:t:s:";							
        option( tlvpickle.Usage ) = "-u <uid> -d <dateint> -t<letter type> -s<letter text>";
	}
	
	rpc DoTest( DoTestReq ) returns ( DoTestResp ){
		option( tlvpickle.CmdID ) = 14;
		option( tlvpickle.OptString ) = "u:a:t:n:s:";							
        option( tlvpickle.Usage ) = "-u <uid> -a <activity_id> -t <op_type 1.push battle msg> -n <op_value> -s <op_data>";
	}
	
	// 获取当前 各种送礼/收礼活动通用的 排行榜黑名单用户列表
	rpc GetRankBlackUidList( GetRankBlackUidListReq ) returns ( GetRankBlackUidListResp ){
		option( tlvpickle.CmdID ) = 30;
		option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	}
	
}

