syntax="proto2";

package mds;

// 必须import
import "common/tlvpickle/skbuiltintype.proto";


message MdsRecord {
    required string appid = 1;
    required string biz_type = 2;
    required string id = 3;
    required uint32 at = 4;
    required bytes biz_data = 5;
}
message InsertReq {
    required MdsRecord record = 1;
}
message InsertResp {
    optional int32 error_code = 1;
    optional uint32  cost_ms = 2;
}

message FindReq {
    required string appid = 1;
    required string biz_type = 2;
    required string id = 3;
    optional int64 begin_time = 4;	// second, unix timestamp
    optional int64 end_time = 5;    // second, unix timestamp

    optional bool count_only = 6;   // 只返回 count
}
message FindResp {
    optional int32 error_code = 1;
    repeated MdsRecord records = 2;
    optional uint32  cost_ms = 3;
    optional int64 count = 4;
}
message BatchInsertReq {
    repeated MdsRecord record_list = 1;
}

message BatchInsertResp {
    optional int32 error_code = 1;
    optional uint32 cost_ms = 2;
}


service MdsStorageGw {
	option( tlvpickle.Magic ) = 15667;

    rpc Insert ( InsertReq ) returns ( InsertResp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    rpc Find ( FindReq ) returns ( FindResp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "a:t:i:b:e:n";
        option( tlvpickle.Usage ) = "-a <appid> -t <biz_type> -i <id> [-b <begin_time>] [-e <end_time>] [-n, count only,no data]";
    }
    rpc BatchInsert ( BatchInsertReq ) returns ( BatchInsertResp ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:x:";
        option( tlvpickle.Usage ) = "-u <uid> -x <channel_id>";
    }

}
