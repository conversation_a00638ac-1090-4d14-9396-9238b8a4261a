syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package channeldrawgame.async;

message Point
{
	required uint32 x = 1;
	required uint32 y = 2;
}

enum LineType
{
	PURE_COLOR = 0; //纯色线
	MULT_COLOR = 1; //荧光笔
	ICON_IMAGE = 2; //icon
	RUBBER = 3;     //橡皮擦
}

//线结构，由很多点组成。
message Line
{
	required uint32 lineID = 1;
	required uint32 uid  = 2;
	required uint32 size = 3;
	required string color = 4;
	repeated Point  PointList = 5;
	optional string para_id = 6;
	optional uint32 line_type = 7; //enum LineType
}

message AddLineEvent
{	
	required uint32 operID = 1;
	required uint32 channelID = 2;
	required Line line = 3;
	required uint32 uid = 5;
}

message AddPointEvent
{
	required uint32 operID = 1;
	required uint32 channelID = 2;
	required uint32 lineID = 3;
	repeated Point  PointList = 4;
	required uint32 uid = 5;
}

message CancelLineEvent
{
	required uint32 operID = 1;
	required uint32 channelID = 2;
	required uint32 lineID = 3;
	required uint32 uid = 4;
}

message RemovePictureEvent
{
	required uint32 operID = 1;
	required uint32 channelID = 2;
	required uint32 uid = 3;
	required string color = 4;
}

message CancelByUidEvent
{
	required uint32 operID = 1;
	required uint32 channelID = 2;
	required uint32 uid = 3;
}

message BroadStatusEvent
{
	required uint32 operID = 1;
	required uint32 channelID = 2;
	required uint32 status = 3;
	required uint32 uid    = 4;
	required string color = 5;
	required uint32 outTime = 6;
}

message DrawStatusEvent
{
	required uint32 operID = 1;
	required uint32 channelID = 2;
	required uint32 status = 3;
	required uint32 uid = 4;
}

message DrawTimeOutNotifyEvent
{
	required uint32 channelID = 1;
	required string color = 2;
}
