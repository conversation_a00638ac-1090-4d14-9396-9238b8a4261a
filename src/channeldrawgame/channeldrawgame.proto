syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package channeldrawgame;

message Point
{
	required uint32 x = 1;
	required uint32 y = 2;
}

enum LineType
{
	PURE_COLOR = 0; //纯色线
	MULT_COLOR = 1; //荧光笔
	ICON_IMAGE = 2; //icon
	RUBBER = 3;     //橡皮擦
}

//线结构，由很多点组成。
message Line
{
	required uint32 lineID = 1;
	required uint32 uid  = 2;
	required uint32 size = 3;
	required string color = 4;
	repeated Point  PointList = 5;
	optional string para_id = 6;
	optional uint32 line_type = 7; //enum LineType
}

//画结构，由很多线组成
message Picture 
{
	required uint32 channelID = 1;
	repeated Line   lineList   = 2;
}

//撤回一条线
message CancelLineReq
{
	required uint32 channelID = 1;
	required uint32 lineID  = 2;
}

message CancelLineResp
{
}

//房管用于删除整幅画
message RemovePictureReq
{
	required uint32 channelID = 1;
}

message RemovePictureResp
{
	optional uint32 operID = 1;
}

//拿整幅画
message GetPictureReq
{
	required uint32 channelID = 1;
}

message GetPictureResp
{
	optional uint32   operID  = 1;
	optional Picture  picture = 2;
}

message CreatLineReq
{
	required uint32 channelID = 1;
	required uint32 size = 2;
	required string color = 3;    //如果有para_id 需要给透明色，兼容旧版本
	optional string para_id = 4; //对应LinePara结构ID
	optional uint32 line_type = 5; //enum LineType
}

message CreatLineResp
{
	required uint32 channelID = 1;
	required uint32 lineID = 2;
	required uint32 operID = 3;
}

message AddPointListReq
{
	required uint32 channelID = 1;
	required uint32 lineID = 2;
	repeated Point pointList = 3;
}

message AddPointListResp
{
}

message CancelByUidReq
{
	required uint32 channelID =1;
}

message CancelByUidResp
{
}

message SetDrawStatusReq
{
	required uint32 channelID = 1;
	required uint32 status    = 2;
}

message SetDrawStatusResp
{
	required uint32 status    = 1;
}

message SetBoardStatusReq
{
	required uint32 channelID = 1;
	required uint32 status    = 2;
}

message SetBoardStatusResp
{
	required uint32 status    = 1;
}

message GetBoardStatusReq
{
	required uint32 channelID = 1;
}

message GetBoardStatusResp
{
	required uint32 channelID = 1;
	required uint32 status    = 2;
	optional uint32 interval  = 3;
}

//拿正在绘画UID列表
message GetDrawStatusListReq
{
	required uint32 channelID = 1;
}

message GetDrawStatusListResp
{
	repeated uint32 uidList = 1;
}

message LinePara
{
	required string para_id = 1; 
	required string name = 2;
	required string preview_url = 3; //预览图URL
	required string icon_url = 4; //小图资源 ZIP URL
	repeated string color_list = 5; // 荧光笔颜色
	required uint32 type = 6;  //enum LineType
}

message GetLineParaListReq
{
}

message GetLineParaListResp
{
	repeated LinePara line_list = 1;
}

service channeldrawgame {
	option( tlvpickle.Magic ) = 15660;		// 服务监听端口号

	rpc CreateLine ( CreatLineReq ) returns ( CreatLineResp )
	{
		option( tlvpickle.CmdID ) = 1;
		option( tlvpickle.OptString ) = "u:x:s:r:";
		option( tlvpickle.Usage ) = "-u <uid> -x <channelID> -s<size> -r<color>";
	}

	rpc CancelLine ( CancelLineReq ) returns ( CancelLineResp )
	{
		option( tlvpickle.CmdID ) = 2;
		option( tlvpickle.OptString ) = "u:x:l:";
		option( tlvpickle.Usage ) = "-u <uid> -x <channelID> -l <lineID>";
	}

	rpc AddPointList ( AddPointListReq ) returns ( AddPointListResp )
	{
		option( tlvpickle.CmdID ) = 3;
		option( tlvpickle.OptString ) = "u:x:p:l:";
		option( tlvpickle.Usage ) = "-u <uid> -x <channelID> -p<points> -l<lineID>";
	}

	rpc CancelByUid ( CancelByUidReq ) returns ( CancelByUidResp )
	{
		option( tlvpickle.CmdID ) = 4;
		option( tlvpickle.OptString ) = "u:x:";
		option( tlvpickle.Usage ) = "-u <uid> -x <channelID>";
	}

	rpc RemovePicture ( RemovePictureReq ) returns ( RemovePictureResp )
	{
		option( tlvpickle.CmdID ) = 5;
		option( tlvpickle.OptString ) = "u:x:";
		option( tlvpickle.Usage ) = "-u <uid> -x <channelID>";
	}

	rpc GetPicture ( GetPictureReq ) returns ( GetPictureResp )
	{
		option( tlvpickle.CmdID ) = 6;
		option( tlvpickle.OptString ) = "u:x:";
		option( tlvpickle.Usage ) = "-u <uid> -x <channelID>";
	}

	rpc SetBoardStatus ( SetBoardStatusReq ) returns ( SetBoardStatusResp )
	{
		option( tlvpickle.CmdID ) = 7;
		option( tlvpickle.OptString ) = "u:x:t:";
		option( tlvpickle.Usage ) = "-u <uid> -x <channelID> -t<status>";
	}

	rpc SetDrawStatus ( SetDrawStatusReq ) returns ( SetDrawStatusResp )
	{
		option( tlvpickle.CmdID ) = 8;
		option( tlvpickle.OptString ) = "u:x:t:";
		option( tlvpickle.Usage ) = "-u <uid> -x <channelID> -t<status>";
	}

	rpc GetBoardStatus ( GetBoardStatusReq ) returns ( GetBoardStatusResp )
	{
		option( tlvpickle.CmdID ) = 9;
		option( tlvpickle.OptString ) = "u:x:";
		option( tlvpickle.Usage ) = "-u <uid> -x <channelID> ";
	}

	rpc GetDrawStatusList ( GetDrawStatusListReq ) returns ( GetDrawStatusListResp )
	{
		option( tlvpickle.CmdID ) = 10;
		option( tlvpickle.OptString ) = "u:x:";
		option( tlvpickle.Usage ) = "-u <uid> -x <channelID> ";
	}

	rpc GetLineParaList ( GetLineParaListReq ) returns ( GetLineParaListResp )
	{
		option( tlvpickle.CmdID ) = 11;
		option( tlvpickle.OptString ) = "u:x:";
		option( tlvpickle.Usage ) = "-u <uid> -x <channelID> ";
	}
}
