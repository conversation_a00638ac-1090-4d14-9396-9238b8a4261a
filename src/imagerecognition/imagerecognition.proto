
syntax="proto2";

package imagerecognition;

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

//
enum ImagePresent{
    IMG_PRES_NIL = 0;
    IMG_PRES_RAW = 1;
    IMG_PRES_URL = 2;
    IMG_PRES_FILE = 3;
}

message Image{
    required string name = 1; //唯一标示图像
    required uint32 present = 2;
    optional bytes raw = 3;
    optional string url = 4;
    optional string file = 5;
}

//task
enum RecognizeType{
    RECOGNIZE_PORN_CLASSIFY = 0x0001;     //色情识别
    RECOGNIZE_POLITICIAN_MATCH = 0x0002;  //政治人物搜索
    RECOGNIZE_ADVERTISING_IDENTIFY = 0x0004;  //广告识别
    RECOGNIZE_VIOLENCE_TERROR = 0x0008;  //暴恐识别
}

message ImageRecognizeReq{
    required uint32 trans_id = 1;
    required uint32 recognize_type = 2; //RecognizeType
    repeated Image image_list = 3;
}

message RecognizeReq{
    required ImageRecognizeReq req = 1;
    optional bool with_detailed_result = 2; //reserved
    optional uint32 request_timeout = 3;  // 单位: 毫秒
    optional uint32 try_count = 4;   //重试次数
}

//识别结果

//概述
enum BriefResult{
    RESULT_DESIRED = 0x0001;
    RESULT_NOTSURE =0x0002;
    RESULT_UNDESIRED = 0x0004;

    RESULT_UNSUPPORT = 0x0008;
    RESULT_INVALID = 0x0010;
}

//色情
message PornClassifyResult{
    enum RESULT{
        PORNY = 0;
        SEXY = 1;
        NORMAL = 2;
    }
    required uint32 result = 1 ; 
    required uint32 require_review = 2;   //是否需要人工复审
}

//政治人物
message PoliticanMatchResult{
    enum RESULT{
        POLITICAN = 0;
        NOT_POLITICAN = 2;
        UNKOWN = 3;
        //0：政治人物； 2：非政治人物； 3：无人脸；
    }
    required uint32 result = 1;
}

message ImageRecognizeResult{
    required string img_name = 1;
    required uint32 result = 2; //BriefResult
    optional PornClassifyResult porn_classify = 3;
    optional PoliticanMatchResult politican_match = 4;
}

message ImageRecognizeRsp{
    required uint32 trans_id = 1;
    required uint32 recognize_type = 2;
    repeated ImageRecognizeResult results = 3;
}

message RecognizeRsp{
    required ImageRecognizeRsp rsp = 1;
}

message GetViolationNoticeImageReq{
}
message GetViolationNoticeImageRsp{
    required bytes img_data = 1;
}
message RecognizeSyncReq {
    required ImageRecognizeReq req = 1;
    optional uint32 request_timeout = 2;  // 单位: 毫秒
}
message RecognizeSyncResp {
    optional int32 error_code = 1;
    optional ImageRecognizeRsp rsp = 2;
}
message RecognizeAsyncReq {
    required ImageRecognizeReq req = 1;
    optional uint32 request_timeout = 2;  // 单位: 毫秒
    optional uint32 try_count = 3;   //重试次数
}
message RecognizeAsyncResp {
    optional int32 error_code = 1;
    optional ImageRecognizeRsp rsp = 2;
}

service ImageRecognition{
    option (tlvpickle.Magic) = 15605;

    rpc Recognize( RecognizeReq ) returns (RecognizeRsp) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "i:l:r:d";
        option( tlvpickle.Usage ) = "-r <recogize,1-porn,2-politician,4-advertising> -i <img1,img2,...> [-d] -l <url1,url2,...>";
    }
    
    rpc GetViolationNoticeImage ( GetViolationNoticeImageReq ) returns ( GetViolationNoticeImageRsp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "-O";
        option( tlvpickle.Usage ) = "-O <file>";
    }
    
    //错误率太高，老告警，因此把错误码放到pb里返回跳过检查, 新增接口
    //同步
    rpc RecognizeSync( RecognizeSyncReq ) returns ( RecognizeSyncResp ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "i:l:r:d";
        option( tlvpickle.Usage ) = "-r <recogize,1-porn,2-politician,4-advertising> -i <img1,img2,...> [-d] -l <url1,url2,...>";
    }
    
    //同步
    rpc RecognizeAsync( RecognizeAsyncReq ) returns ( RecognizeAsyncResp ) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "i:l:r:d";
        option( tlvpickle.Usage ) = "-r <recogize,1-porn,2-politician,4-advertising> -i <img1,img2,...> [-d] -l <url1,url2,...>";
    }
}


//内部使用
//色情识别
message TupuPornResult{
    required string image_name = 1;
    required uint32 brief_result = 2;
    required int32 label = 3; //类别: 0：色情； 1：性感； 2：正常；
    required bool review = 4;
    optional double rate = 5;
    optional string tab = 6;
    optional string zip_file_name = 7;
}

//政治任务搜索
message TupuPoliticianResult{
    required string image_name = 1;
    required uint32 brief_result = 2;
    required int32 label = 3;//类别: 0：政治人物； 2：非政治人物； 3：无人脸；
    required bool review = 4;
    optional double similarity = 5;
    optional string face_id = 6;
}

// 广告识别2.0 
message TupuAdvertisingResult{
    required string image_name = 1;
    required uint32 brief_result = 2;
    required int32 label = 3; //类别: 0：正常； 1：二维码； 2：正常文字图片； 3：广告文字图片； 
    required bool review = 4;
}
//暴恐识别3.0
message TupuViolenceTerrorResult{
    required string image_name = 1;
    required uint32 brief_result = 2;
    required int32 label = 3; // 0-正常 1-特殊着装人物 2-特殊符号 3-武器或持武器者 4-血腥场景 5-暴乱场景 6-战争场景
    required bool review = 4;
}
message TupuSearchGalleryResult{
    required string image_name = 1;
    required uint32 brief_result = 2;
    required int32 label = 3; // 0：重复；2：不重复
    required bool review = 4;
}

//汇总
message TupuRecognitionResult{
    required uint32 result = 1;
    optional TupuPornResult porn_result = 2;
    optional TupuPoliticianResult politician_result = 3;
    optional TupuAdvertisingResult advertising_result = 4;
    optional TupuViolenceTerrorResult violence_terror_result = 5;
    optional TupuSearchGalleryResult search_gallery_result = 6;
}







