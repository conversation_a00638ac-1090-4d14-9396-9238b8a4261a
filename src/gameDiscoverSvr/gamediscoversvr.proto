syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package gamediscoversvr;


//游戏分类标签
message GameTypeInfo {
	required uint32 type = 1;
	required string type_desc = 2;
}
message GetGamtTypeResp {
	repeated GameTypeInfo game_type_list = 1;
}

//游戏分类查询

message LableGameDetail {
	required uint32 game_id= 1;
    required string game_name = 2;              /* 游戏名称 */
    required string game_area_url = 4;          /* 跳转游戏专区 */
    required string game_icon_url = 5;          /* 图标链接     */
    repeated string game_type = 7;              /* 游戏类型：RPG、FPS */
    required uint32 downloaded_num  = 8;         /* 已下载用户数 */
    required uint32 followed_num    = 9;        /* 已关注用户数 */
    required bool   is_pkt_ready	    = 10;        /* 是否可下载 */
    optional bool   is_order_ready  = 11;   //是否可以预约
    optional string preorder_url    = 12;   //预约活动页
	optional string download_url	= 13;   //下载链接
}

message GetGameByLableReq {
	required uint32 game_type = 1;
}

message GetGameByLableResp {
	required LableGameDetail game_list   = 2;
}


//HotGame满街都是，POPgame也能翻译成热游的
message PopGameDetail {
	required uint32 game_id         = 1;    //游戏ID
    required string game_name       = 2;    //游戏名
    required string area_url        = 3;    //游戏专区或游戏圈跳转地址
    required string icon_url        = 4;    //图标地址
    required string download_url    = 5;    //下载链接
	repeated uint32 fri_uid_list		= 6;  //好友UID
	required string game_size		= 7;             //大小
	required string game_pkt_name    = 8;    //游戏包名
	optional string summary			= 9; //
}

message PlayingFriInfo {
	repeated uint32 fri_uid_list = 1;
}

//热玩

message GetPopGameReq {
	required uint32 uid = 1;
}

message GetPopGameResp {
	repeated PopGameDetail game_detail = 1;
}



//发现页标题
message DiscoverContentDetail {
	required string title = 1;
	required string sub_title = 2;
	required string jump_url = 3;
	required uint32 rank = 4;
}

message GetDiscoverContentResp {
	repeated DiscoverContentDetail discover_content_list = 1;
}

message SetDiscoverContentReq {
	required DiscoverContentDetail discover_content = 1;
}


message GameAreaAdv {
	required string adv_desc = 1;
	required string icon = 2;
	required string jump_url = 3;
}

message PopGameDisplayIcon {
	required string icon = 1;
}

service GameDiscoverSvr{
    option( tlvpickle.Magic ) = 15620;

	rpc GetFriPopGame ( GetPopGameReq ) returns ( GetPopGameResp ) {
        option( tlvpickle.CmdID ) = 1;	
	    option( tlvpickle.OptString ) = "";	
        option( tlvpickle.Usage ) = "";	
    }

	rpc GetChannelPopGame ( GetPopGameReq ) returns ( GetPopGameResp ) {
        option( tlvpickle.CmdID ) = 2;	
	    option( tlvpickle.OptString ) = "";	
        option( tlvpickle.Usage ) = "";	
    }

	rpc GetDiscoverContent ( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetDiscoverContentResp ) {
        option( tlvpickle.CmdID ) = 3;	
	    option( tlvpickle.OptString ) = "";	
        option( tlvpickle.Usage ) = "";	
    } 

	rpc SetDiscoverContent ( SetDiscoverContentReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 4;	
	    option( tlvpickle.OptString ) = "";	
        option( tlvpickle.Usage ) = "";	
    }

	rpc SetGameAreaAdv ( GameAreaAdv ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 5;	
	    option( tlvpickle.OptString ) = "";	
        option( tlvpickle.Usage ) = "";	
    }

	rpc GetGameAreaAdv ( tlvpickle.SKBuiltinEmpty_PB ) returns ( GameAreaAdv ) {
        option( tlvpickle.CmdID ) = 6;	
	    option( tlvpickle.OptString ) = "";	
        option( tlvpickle.Usage ) = "";	
    }

	rpc SetPopGameDisplayIcon ( PopGameDisplayIcon ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 7;	
	    option( tlvpickle.OptString ) = "";	
        option( tlvpickle.Usage ) = "";	
    }

	rpc GetPopGameDisplayIcon ( tlvpickle.SKBuiltinEmpty_PB ) returns ( PopGameDisplayIcon ) {
        option( tlvpickle.CmdID ) = 8;	
	    option( tlvpickle.OptString ) = "";	
        option( tlvpickle.Usage ) = "";	
    }
}
