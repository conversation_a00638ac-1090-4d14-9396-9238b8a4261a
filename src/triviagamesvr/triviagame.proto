syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";  

// namespace
package triviagame;

message QuestionEntry{
    uint32 id       = 1;
    string question = 2;    //问题
    repeated string option   = 3;    //选项
    uint32 solution = 4;   //答案, 1表示第1个选项
}

enum PhaseType{
    phase_none  = 0;
    phase_timer = 1;
    phase_animation = 2;
    phase_question  = 3;
    phase_winner    = 4;
}

message PhaseTimer{
    uint32 timer = 1;
    uint32 end_timestamp = 2; //倒计时结束的时间戳
}

message PhaseAnimation{
    uint32 anima_id = 1;    //动画id
    string text = 2;        //动画上显示的字符,如果有的话
}

message PhaseQuestion{
    uint32 question_id = 1; //题目id
    uint32 question_idx = 2;//题目序号
    uint32 tip_time = 3;    //给主持的答题倒计时提示
    QuestionEntry question_entry = 4;
    uint32 end_timestamp = 5;   //允许公布答案的时间
    bool   is_show_solution = 6;  //是否公布答案
}

message PhaseEntry{
    uint32 phase_idx     = 1; //阶段序号
    uint32 phase_type   = 2; //阶段类型
    string phase_name   = 3; //阶段名称
    bytes phase_bin    = 4; //阶段内容
}

message PhaseList{
    repeated PhaseEntry phase_list = 1;
}

message TriviaGameEntry{
    uint32 id       = 1;
    uint32 start_time   = 2;//开始时间
    uint32 reward       = 3;//奖金
    PhaseList phase_list = 4; //阶段列表
    uint32 current_phase = 5; //正在进行的阶段，0未开始，255结束
    uint32 question_count = 6;//题目数量，客户端需要
}

message TriviaGamePhaseEntry{
    uint32 id       = 1;
    uint32 start_time   = 2;//开始时间
    uint32 reward       = 3;//奖金
    uint32 current_phase = 4; //正在进行的阶段，0未开始，255结束
    PhaseEntry phase_entry = 5;//正在进行的阶段详情
    uint32 question_count = 6;//题目数量，客户端需要
}

message TriviaGameList{
    repeated TriviaGameEntry activity_list = 1;
}

message TriviaGameEntryId{
    uint32 id = 1;
}

message SetTriviaGamePhaseReq{
    uint32 act_id = 1;
    uint32 phase_idx = 2;
}

message GetCurrentPhaseQuestionReq{
    uint32 phase_idx = 1;
}

service TriviaGame {
    option( tlvpickle.Magic ) = 15577;      // 服务监听端口号

    rpc SetTriviaGame( TriviaGameEntry ) returns( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 1;                                      
        option( tlvpickle.OptString ) = "";                         
        option( tlvpickle.Usage ) = "";
    }

    rpc GetTriviaGame( TriviaGameEntryId ) returns( TriviaGameEntry ){
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "i:";
        option( tlvpickle.Usage ) = "-i<triviagame_id>";
    }

    rpc GetNextTriviaGame( tlvpickle.SKBuiltinEmpty_PB ) returns( TriviaGameEntry ){
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetTriviaGameList( tlvpickle.SKBuiltinEmpty_PB ) returns( TriviaGameList ){
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc ForwardTriviaGamePhase( tlvpickle.SKBuiltinEmpty_PB ) returns( TriviaGamePhaseEntry ){
        option( tlvpickle.CmdID ) = 5;                                      
        option( tlvpickle.OptString ) = "";                         
        option( tlvpickle.Usage ) = "";
    }

    rpc CreateDefaultTriviaGame( tlvpickle.SKBuiltinEmpty_PB ) returns( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "";                         
        option( tlvpickle.Usage ) = "";
    }

    rpc GetCurrentTriviaGame(tlvpickle.SKBuiltinEmpty_PB) returns(TriviaGameEntry){
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "";                         
        option( tlvpickle.Usage ) = "";        
    }

    rpc SetCurrentPhaseShowSolution(tlvpickle.SKBuiltinEmpty_PB) returns(PhaseQuestion){
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "";                         
        option( tlvpickle.Usage ) = "";        
    }

    rpc GetCurrentTriviaGamePhase(tlvpickle.SKBuiltinEmpty_PB) returns(TriviaGamePhaseEntry){
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "";                         
        option( tlvpickle.Usage ) = "";        
    }

    rpc SetCurrentTriviaGamePhase( SetTriviaGamePhaseReq ) returns(tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 10;                                      
        option( tlvpickle.OptString ) = "i:";                         
        option( tlvpickle.Usage ) = "-i<phase_idx>";
    }

    rpc ReloadCurrentTriviaGame( tlvpickle.SKBuiltinEmpty_PB ) returns( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "";                         
        option( tlvpickle.Usage ) = "";
    }    
}