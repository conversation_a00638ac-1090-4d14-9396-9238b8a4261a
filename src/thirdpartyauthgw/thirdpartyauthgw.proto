syntax = "proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package thirdpartyauthgw;

message BaseResult {
    required int32 code = 1;
    optional string msg = 2;
}


message VerifyAppleUserReq {
    required string auth_code = 1; 
    optional string id_token = 2; 
    optional string user_id = 3;
}

message VerifyAppleUserResp {
    optional BaseResult base_result = 1;
    optional string user_id = 2;
}

message VerifyWeixinUserReq {
    required string access_token = 1;
    required bytes open_id = 2;
}

message VerifyWeixinUserResp {
    optional bytes data = 1;
}

message VerifyQQUserReq {
    required string app_id = 1;
    required string access_token = 2;
    required bytes open_id = 3;
}

message VerifyQQUserResp {
    optional bytes oauth_me = 1;
    optional bytes data = 2;
}

message VerifyQQMiniUserReq {
    required string app_id = 1;
    required string secret = 2;
    required string jscode = 3;
}

message VerifyQQMiniUserResp {
    optional bytes data = 1;
}

message VerifyWeixinMiniUserReq {
    required string app_id = 1;
    required string secret = 2;
    required string jscode = 3;
}

message VerifyWeixinMiniUserResp {
    optional bytes data = 1;
}

enum PHONE_TYPE {
    PHONE_UNSPECIFIED = 0;
    PHONE_CHUANG_LAN = 1; // china unicom
    PHONE_CHINA_MOBILE = 2;
}

message LocalCheckPhoneReq {
    required PHONE_TYPE type = 1;
    required string token = 2;
    required string phone = 3;
}

message LocalCheckPhoneResp {
    required bool is_pass = 1;
}

message HttpGetPhoneReq {
    required PHONE_TYPE type = 1;
    optional string special_appid = 2; // chinamobile need
    required int32 client_type = 3;
    required string token = 4;
}

message HttpGetPhoneResp {
    required string phone = 1;
}

message LocalGetPhoneByTokenReq {
    required PHONE_TYPE type = 1;
    required string token = 2;
}

message LocalGetPhoneByTokenResp {
    required string phone = 1;
}

message LocalCleanTokenPhoneReq {
    required PHONE_TYPE type = 1;
    required string token = 2;
    required string phone = 3;
}

message LocalCleanTokenPhoneResp {
}

message GetCMCCPhoneReq  {
    required string appid = 1;
    required string token = 2;
}
message GetCMCCPhoneResp  {
    optional string phone = 1;
}
message GetCUCCPhoneReq {
    required string access_code = 1;
    optional string apikey = 2;
    optional string md5 = 3; //应用 md5

}
message GetCUCCPhoneResp {
    optional string phone = 1;
}
message GetChuanglanPhoneReq {
    required string appid = 1;
    required string token = 2;
}
message GetChuanglanPhoneResp  {
    optional string phone = 1;
}

message VerifyQQUserExReq {
    optional string access_token = 1;
}
message VerifyQQUserExResp {
     optional string appid = 1;
     optional string openid = 2;
     optional string unionid = 3;
}
message GetQQUserInfoReq {
    optional string appid = 1;
    optional string openid = 2;
    optional string access_token = 3;
}
message GetQQUserInfoResp {
    optional QQUserInfo info = 1;
}
message QQUserInfo {
    optional string appid = 1;
    optional string openid = 2;
    optional string nickname = 3;
    optional string gender = 4;
    optional string figureurl_qq_1 = 5; //大小为40×40像素的QQ头像URL。
    optional string figureurl_qq_2 = 6; //大小为100×100像素的QQ头像URL。需要注意，不是所有的用户都拥有QQ的100x100的头像，但40x40像素则是一定会有。 
}
message GetWeChatUserInfoReq {
    optional string openid = 1;
    optional string access_token = 2;
}
message GetWeChatUserInfoResp {
    optional WeChatUserInfo info = 1;
}
message WeChatUserInfo {
    optional string openid = 1;
    optional string unionid = 2;
    optional string nickname = 3;
    optional string gender = 4;
    optional string headimage = 5; 
}
message VerifyQQMiniUserExReq {
    optional string appid = 1;
    optional string jscode = 2;
}
message VerifyQQMiniUserExResp {
    optional string openid = 1;
    optional string unionid = 2;
}
message VerifyWeChatMiniUserExReq {
    optional string appid = 1;
    optional string jscode = 2;
}
message VerifyWeChatMiniUserExResp  {
    optional string openid = 1;
    optional string unionid = 2;
} 

service ThirdpartyAuthGw {
  option (tlvpickle.Magic) = 14007;  // 服务监听端口号

  rpc VerifyAppleUser(VerifyAppleUserReq) returns (VerifyAppleUserResp) {
      option (tlvpickle.CmdID) = 1;
      option (tlvpickle.OptString) = "a:u:t:";
      option (tlvpickle.Usage) = "-a <auth_code> -t <id_token> -u <user_id>";
  }

  rpc VerifyWeixinUser(VerifyWeixinUserReq) returns (VerifyWeixinUserResp) {
      option (tlvpickle.CmdID) = 2;
      option (tlvpickle.OptString) = "a:o:";
      option (tlvpickle.Usage) = "-a <acc_token> -o <open_id>";
  }

  rpc VerifyQQUser(VerifyQQUserReq) returns (VerifyQQUserResp) {
      option (tlvpickle.CmdID) = 3;
      option (tlvpickle.OptString) = "a:o:i:";
      option (tlvpickle.Usage) = "-i <app_id> -a <acc_token> -o <open_id>";
  }

  rpc VerifyQQMiniUser(VerifyQQMiniUserReq) returns (VerifyQQMiniUserResp) {
      option (tlvpickle.CmdID) = 4;
      option (tlvpickle.OptString) = "a:o:i:s:";
      option (tlvpickle.Usage) = "-i <app_id> -s <secret> -j <jsCode>";
  }

  rpc VerifyWeixinMiniUser(VerifyWeixinMiniUserReq) returns (VerifyWeixinMiniUserResp) {
      option (tlvpickle.CmdID) = 5;
      option (tlvpickle.OptString) = "a:o:i:s:";
      option (tlvpickle.Usage) = "-i <app_id> -s <secret> -j <jsCode>";
  }

   rpc LocalCheckPhone(LocalCheckPhoneReq) returns (LocalCheckPhoneResp) {
       option (tlvpickle.CmdID) = 6;
       option (tlvpickle.OptString) = "t:p:";
       option (tlvpickle.Usage) = "-t <token> -p <phone>";
   }

    rpc HttpGetPhone(HttpGetPhoneReq) returns (HttpGetPhoneResp) {
        option (tlvpickle.CmdID) = 7;
        option (tlvpickle.OptString) = "t:";
        option (tlvpickle.Usage) = "-t <token>";
    }

    rpc LocalGetPhoneByToken(LocalGetPhoneByTokenReq) returns (LocalGetPhoneByTokenResp) {
        option (tlvpickle.CmdID) = 8;
        option (tlvpickle.OptString) = "t:";
        option (tlvpickle.Usage) = "-t <token>";
    }

    rpc LocalCleanTokenPhone(LocalCleanTokenPhoneReq) returns (LocalCleanTokenPhoneResp) {
        option (tlvpickle.CmdID) = 9;
        option (tlvpickle.OptString) = "t:p:";
        option (tlvpickle.Usage) = "-t <token> -p <phone>";
    }

    //中国移动 获取手机号
    rpc GetCMCCPhone (GetCMCCPhoneReq) returns (GetCMCCPhoneResp) {
        option (tlvpickle.CmdID) = 10;
        option (tlvpickle.OptString) = "a:t:";
        option (tlvpickle.Usage) = "-a <appid> -t <token>";
    }
    // 中国联通 置换手机号
    rpc GetCUCCPhone ( GetCUCCPhoneReq ) returns ( GetCUCCPhoneResp ) {
        option (tlvpickle.CmdID) = 11;
        option (tlvpickle.OptString) = "k:m:a";
        option (tlvpickle.Usage) = "-k <apikey> -a <access code> -m <md5> ";
    }
    //中国电信预留

    // 创蓝 
    rpc GetChuanglanPhone ( GetChuanglanPhoneReq ) returns ( GetChuanglanPhoneResp ) {
        option (tlvpickle.CmdID) = 13;
        option (tlvpickle.OptString) = "a:t:";
        option (tlvpickle.Usage) = "-a <appid> -t <token>";
    }

    rpc VerifyQQUserEx( VerifyQQUserExReq ) returns (VerifyQQUserExResp) {
        option (tlvpickle.CmdID) = 15;
        option (tlvpickle.OptString) = "t:";
        option (tlvpickle.Usage) = "-t <access_token>";
    }

    rpc GetQQUserInfo( GetQQUserInfoReq ) returns ( GetQQUserInfoResp ) {
        option (tlvpickle.CmdID) = 16;
        option (tlvpickle.OptString) = "a:o:t:";
        option (tlvpickle.Usage) = "-a <appid> -o <openid> -t <access token>";
    }
    //reserved : WeChat verify
    //
    rpc GetWeChatUserInfo( GetWeChatUserInfoReq ) returns ( GetWeChatUserInfoResp ) {
        option (tlvpickle.CmdID) = 18;
        option (tlvpickle.OptString) = "o:t:";
        option (tlvpickle.Usage) = "-o <openid> -t <access token>";
    }
    //Mini
    rpc VerifyQQMiniUserEx( VerifyQQMiniUserExReq ) returns ( VerifyQQMiniUserExResp ) {
        option (tlvpickle.CmdID) = 19;
        option (tlvpickle.OptString) = "a:j:";
        option (tlvpickle.Usage) = "-a <appid> -j <jscode>";
    }
    rpc VerifyWeChatMiniUserEx( VerifyWeChatMiniUserExReq ) returns ( VerifyWeChatMiniUserExResp ) {
        option (tlvpickle.CmdID) = 20;
        option (tlvpickle.OptString) = "a:j:";
        option (tlvpickle.Usage) = "-a <appid> -j <jscode>";
    }



}





