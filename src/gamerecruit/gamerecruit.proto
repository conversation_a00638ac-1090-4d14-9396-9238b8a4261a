syntax="proto2";

// svrkit support
import "common/tlvpickle/skbuiltintype.proto";

package GameRecruit;

message GameRecruitInfo {
	required uint32 owner_uid = 1;
	repeated uint32 member_uids = 2;
	required uint32 game_id = 3;
	required string title = 4;
	required string desc = 5;
}


message CreateGameRecruitReq {
	required GameRecruitInfo gr_info = 1;
}

message CreateGameRecruitResp {
	required uint32 gr_id = 2;
}

message GetGameRecruitDetailReq {
	required uint32 uid = 1;
}

message GetGameRecruitDetailResp {
	required GameRecruitInfo gr_info = 1;
	required uint32 gr_id = 2;
}

message JoinGameRecruitReq {
	required uint32 uid = 1;
	required uint32 gr_id = 2;
	required uint32 game_id = 3;
}

message JoinGameRecruitResp {
	required GameRecruitInfo gr_info = 1;
}

message QuitGameRecruitReq {
	required uint32 uid = 1;
	required uint32 gr_id = 2;
}

message QuitGameRecruitResp {

}


service GameRecruit {
	/**
	 * Server listening port
	 */
	option( tlvpickle.Magic ) = 15631;

	// 0 - 99, product related

	//tlvpickle.SKBuiltinEmpty_PB

	rpc CreateGameRecruit( CreateGameRecruitReq ) returns ( CreateGameRecruitResp ) {
		option( tlvpickle.CmdID ) = 1;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc GetGameRecruitDetail( GetGameRecruitDetailReq ) returns ( GetGameRecruitDetailResp ) {
		option( tlvpickle.CmdID ) = 2;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc JoinGameRecruit( JoinGameRecruitReq ) returns ( JoinGameRecruitResp ) {
		option( tlvpickle.CmdID ) = 3;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc QuitGameRecruit( QuitGameRecruitReq ) returns ( QuitGameRecruitResp ) {
		option( tlvpickle.CmdID ) = 4;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
}
