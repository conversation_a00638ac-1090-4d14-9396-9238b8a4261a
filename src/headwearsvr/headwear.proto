syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";  

// namespace
package headwear;

enum HEADWEAR_TYPE{
    HEADWEAR_TYPE_COMMON = 0;   //普通麦位框
    HEADWEAR_TYPE_CP     = 1;   //cp麦位框
}

message HeadwearConfig{
    uint32 headwear_id = 1;
    uint32 suite_id = 2;
    string name = 3;
    string img = 4;           //img url
    uint32 level = 5; 
    uint32 holding_time = 6;
    string static_img = 7;      // static img url
    string gif_img = 8;         // gif img url 动态图 不一定是gif格式

    uint32 headwear_type = 9;   // 麦位框类型 见 HEADWEAR_TYPE
    string cp_headwear_img = 10;// cp麦位框 img url
}

message UserHeadwearInfo{
    uint32 uid         = 1;
    uint32 headwear_id = 2;
    uint32 expire_time = 3; //过期时间的timestamp
    uint32 extra_time  = 4; //额外增加的秒数，未计入expire_time
    HeadwearConfig headwear_config = 5;
    uint32 cp_uid      = 6; // cp对象uid
}

message GetUserHeadwearReq{
    uint32 uid = 1;
}

message GetUserHeadwearResp{
    repeated UserHeadwearInfo headwear_list = 1;
}

message GetUserHeadwearBySuiteReq{
    uint32 uid = 1;
    uint32 suite_id = 2;
    uint32 cp_uid = 3;
}

message GetHeadwearConfigReq{
    repeated uint32 id_list = 1;
}

message GetHeadwearConfigResp{
    repeated HeadwearConfig headwear_list = 1;
}

message SetUserHeadwearInUseReq{
    uint32 uid = 1;
    uint32 headwear_id = 2;
    uint32 cp_uid = 3;
}

message GetUserHeadwearInUseReq{
    uint32 uid  = 1;
}

message RemoveUserHeadwearInUseReq{
    uint32 uid  = 1;
}

message GetUserHeadwearInUseResp{
    uint32 headwear_id = 1;
    uint32 cp_uid = 2;
}

message GetUserHeadwearInUseListReq{
    repeated uint32 uids  = 1;
}

message GetUserHeadwearInUseListResp{
    repeated UserHeadwearInfo headwear_list = 1;
}

enum GIVE_TYPE{
    GIVE_TYPE_NONE = 0;
    GIVE_TYPE_NEW = 1;      //new holding time
    GIVE_TYPE_APPEND = 2;   //append holding time, default
}

message GiveHeadweartoUserReq{
    uint32 uid = 1;
    uint32 suite_id = 2;    //headwear suite
    uint32 level = 3;   //headwear level
    uint32 give_type = 4;   //see enum GIVE_TYPE
    uint32 expire_time  = 5;        // 绝对值 过期时间的时间戳（表示具体时间）
    uint32 expire_time_rel  = 6;    // 相对值 过期时间的时间戳 (表示秒) expire_time_rel 和 expire_time 只能选一个填 
    uint32 cp_uid  = 7;       // cp对象uid ，用于发放cp麦位框时填
    string order_id = 8;      //幂等orderID
    string pushmsg_text_prefix = 9;// TT助手推送文本前缀
}

message HeadwearConfigReq
{
    uint32 headwear_id = 1;
}

message GetHeadwearConfigAllResp
{
    repeated HeadwearConfig config_list = 1;
}

message AddUserHeadwearExtraTimeReq
{
    uint32 uid = 1;
    uint32 extra_time = 2;  //增加的时间
    repeated uint32 suite_ids = 3;
}

service HeadWear {
    option( tlvpickle.Magic ) = 15589;      // 服务监听端口号

    rpc GetUserHeadwear( GetUserHeadwearReq ) returns( GetUserHeadwearResp ){
        option( tlvpickle.CmdID ) = 1;                                      
        option( tlvpickle.OptString ) = "u:";                         
        option( tlvpickle.Usage ) = "-u<uid>";
    }

    rpc SetUserHeadwearInUse( SetUserHeadwearInUseReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 2;                                      
        option( tlvpickle.OptString ) = "u:h:q:";
        option( tlvpickle.Usage ) = "-u<uid> -h<headwear_id> -q <cp_uid>";
    }

    rpc GetUserHeadwearInUse( GetUserHeadwearInUseReq ) returns( GetUserHeadwearInUseResp ){
        option( tlvpickle.CmdID ) = 3;                                      
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u<uid>";
    }    

    rpc GiveHeadweartoUser( GiveHeadweartoUserReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 4;                                      
        option( tlvpickle.OptString ) = "u:s:l:q:o:p:";                         
        option( tlvpickle.Usage ) = "-u<uid> -s<headwear_suite_id> -l<headwear_level> -q<cp_uid> -o<order_id> -p<pushmsg_prefix>";
    }

    rpc AddHeadwearConfig( HeadwearConfig ) returns( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 5;                                      
        option( tlvpickle.OptString ) = "s:n:i:l:t:m:g:p:q:";
        option( tlvpickle.Usage ) = "-s<suite_id> -n<name> -i<img> -l<level> -t<holding_time> -m<static_img> -g<gif_img> -p<type> -q<cp_img>";
    }

    rpc GetHeadwearConfig( HeadwearConfigReq ) returns( HeadwearConfig ){
        option( tlvpickle.CmdID ) = 6;                                      
        option( tlvpickle.OptString ) = "h:";                         
        option( tlvpickle.Usage ) = "-h<headwear_id>";
    }

    rpc DelHeadwearConfig( HeadwearConfigReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 7;                                      
        option( tlvpickle.OptString ) = "h:";                         
        option( tlvpickle.Usage ) = "-h<headwear_id>";
    }

    rpc GetHeadwearConfigAll( tlvpickle.SKBuiltinEmpty_PB ) returns( GetHeadwearConfigAllResp ){
        option( tlvpickle.CmdID ) = 8;                                      
        option( tlvpickle.OptString ) = "";                         
        option( tlvpickle.Usage ) = "";
    }

    rpc GetUserHeadwearInUseList( GetUserHeadwearInUseListReq ) returns( GetUserHeadwearInUseListResp ){
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u<uid>";
    }

    rpc RemoveUserHeadwearInUse( RemoveUserHeadwearInUseReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 10;                                      
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u<uid>";
    }

    rpc GetUserHeadwearBySuite( GetUserHeadwearBySuiteReq ) returns( UserHeadwearInfo ){
        option( tlvpickle.CmdID ) = 11;                                      
        option( tlvpickle.OptString ) = "u:s:q:";
        option( tlvpickle.Usage ) = "-u<uid> -s<suite_id> -q<cp_uid>";
    }

    rpc AddUserHeadwearExtraTime( AddUserHeadwearExtraTimeReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 12;                                      
        option( tlvpickle.OptString ) = "u:s:t:";
        option( tlvpickle.Usage ) = "-u<uid> -s<suite_id> -t<time:1s>";
    }

    rpc GiveHeadweartoUserWithExpireTime( GiveHeadweartoUserReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 13;                                      
        option( tlvpickle.OptString ) = "u:s:l:t:q:o:p:";                         
        option( tlvpickle.Usage ) = "-u<uid> -s<headwear_suite_id> -l<headwear_level> -t<timestamp> -q<cp_uid> -o<order_id> -p<pushmsg_prefix>";
    }

	rpc UpdateHeadwearConfig( HeadwearConfig ) returns( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 14;                                      
        option( tlvpickle.OptString ) = "h:s:n:i:l:t:m:g:p:q:";
        option( tlvpickle.Usage ) = "-h<headwear_id> [-s<suite_id> -n<name> -i<img> -l<level> -t<holding_time> -m<static_img> -g<gif_img> -p<type> -q<cp_img>]";
    }

    rpc GiveHeadweartoUserWithExpireTimeRel( GiveHeadweartoUserReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 15;                                      
        option( tlvpickle.OptString ) = "u:s:l:t:q:o:p:";                         
        option( tlvpickle.Usage ) = "-u<uid> -s<headwear_suite_id> -l<headwear_level> -t<timestamp_rel> -q<cp_uid> -o<order_id> -p<pushmsg_prefix>";
    }
}