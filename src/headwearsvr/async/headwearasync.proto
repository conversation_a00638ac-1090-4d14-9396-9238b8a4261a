syntax="proto3";

// namespace
package headwearasync;

enum AsyncCmdType{
    CMD_TYPE_NONE   = 0;
    HEADWEAR_CMD    = 1;
}

enum HeadwearOpType{
    OP_TYPE_NONE    = 0;
    GIVE_HEADWEAR   = 1;
    LEV_UP_HEADWEAR = 2;
    USE_HEADWEAR    = 3;
    EXPIRE_HEADWEAR_IM = 4;
    GIVE_HEADWEAR_IM = 5;
}

message UserHeadwear{
    uint32 uid = 1;
    uint32 headwear_id = 2;
    HeadwearOpType op_type = 3;
	uint32 expired_ts = 4;
}

