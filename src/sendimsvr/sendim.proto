syntax="proto2"; 

package sendim;

import "common/tlvpickle/skbuiltintype.proto";


service SendIm {
    option( tlvpickle.Magic ) = 15393;		// 服务监听端口号

    rpc SendSync( SendSyncReq ) returns ( SendSyncResp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "o:t:d:a:p:be:n";
        option( tlvpickle.Usage ) = "-o <sender, type:id> -t <receiver, type:id1,id2,...> -d <content, type:content-p1:content-p2> [-a <app-name>] [-p <platform>] [-b(both send)] [-e <expires_at>] [-n(with notify)]";
    }
    rpc SendAsync( SendAsyncReq ) returns ( SendAsyncResp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "o:t:d:a:p:be:n";
        option( tlvpickle.Usage ) = "-o <type:id> -t <type:id1,id2,...> -d <type:content-p1:content-p2> [-a <app-name,...>] [-p <platform,...>] [-b(both send)] [-e <expires_at>] [-n(with notify)]";
    }
    // 撤回
    rpc Withdraw ( WithdrawReq  ) returns( WithdrawResp ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    rpc SendToUser ( SendToUserReq  ) returns( SendToUserResp ) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "o:t:d:a:p:be:ns";
        option( tlvpickle.Usage ) = "-o <sender, type:id> -t <uid1,uid2,...> -d <type:content-p1:content-p2> "
                                    "[-a <app-name,...>] [-p <platform,...>] [-b(both send)] [-e <expires_at>] "
                                    "[-n(with notify)] [-s <async>]";
    }
}
message SendSyncReq {
    required Sender sender = 1;
    required Receiver receiver = 2;
    required ImMsg msg = 3; 
    optional bool with_notify = 4;  /* 接收者为 public/broadcast 没用 */
}
message SendSyncResp  {
    optional string withdraw_token = 1;  // 撤销凭证，为空则不支持撤回
}
message SendAsyncReq {
    required Sender sender = 1;
    required Receiver receiver = 2;
    required ImMsg msg = 3; 
    optional bool with_notify = 4;/* 接收者为 public/broadcast 没用 */
}
message SendAsyncResp {
    optional string withdraw_token = 1;  // 撤销凭证，为空则不支持撤回
}

// 支持消息与接收者一对一，同步/异步发送
message SendToUserReq {
    optional Sender sender = 1;
    repeated uint32 uid_list = 2;
    repeated ImMsg msg_list = 3; // 与 uid_list 对应
    optional bool with_notify = 4;
    optional bool is_async = 5;
}
message SendToUserResp {
}

message WithdrawReq {
    required string withdraw_token = 1;  // 撤销凭证
}
message WithdrawResp {
}

message ImMsg {
    required Content content = 1;

    optional uint32 client_msg_id = 2;  //发送者去重
    optional uint32 client_msg_time = 3; 
    
    /* 取值: ttvoice/huanyou/zaiya, 空表示所有 */
    optional string app_name = 4; 
    /* 取值: android/ios/pc, 空表示全平台 */
    optional  string app_platform  = 5; 

    // 消息过期时间, second
    optional uint32 expired_at = 6;   /* 广播 或 公众号 才有意义 */ 
} 

message Sender { 
    enum SenderType {
        Nil = 0;
        User = 1;
        Public= 2;
    }
    required int32 type = 1;
    required uint32 id = 2;
}
message Receiver {
    enum ReceiverType {
        Nil = 0;
        User = 1;
        Public = 2;
        Broadcast = 3;
    }
    required int32  type = 1;
    repeated uint32 id_list = 2; //若type=Public/Broadcast, 忽略
}
message Content {
    enum ContentType {
        Nil = 0;     // 
        //最普通的文本，对应 proto/pbfile/im.proto TEXT_MSG (= 1)
        Text = 1;    
        //带高亮和跳转的文本，对应 proto/pbfile/im.proto TT_COMMON_TEXT_NOTIFY (= 37)
        TextWithHighlightUrl = 2; 

        //带图片、标题、跳转、内容等的公众号推送消息，对应 src/proto/pbfile/im.proto OFFICAIL_MESSAGE_SINGLE(22)/OFFICAIL_MESSAGE_BUNCH(23)
        OfficialMessage = 3;

        //扩展字段 对应 proto/pbfile/im.proto NEW_EXTENDED (= 21)
        NewExtended = 4;
    }
    required int32 type = 1;
    optional ImTextNormal text_normal = 2;  //IM_CONTENT_TEXT && 
    optional ImTextWithHighlightUrl text_hl_url = 3;
    optional ImOfficialMessage official_message = 4;
    optional ImNewExtended new_extended = 5;
    //type
    //
}

//Content::Text 
message ImTextNormal{
    required string content = 1;
} 
//Content::TextWithHighlightUrl  
message ImTextWithHighlightUrl{
    required string content = 1;
    required string highlight = 2;
    required string url = 3;
} 
//Content::IM_CONTENT_NEW_EXTENDED
message ImNewExtended {
    required string content = 1;
}

//Content::OfficialMessage  
//对应 proto/pbfile/ga_base.proto : OfficialMessageSingle、OfficialMessageBunch
message ImOfficialMessage{
    required uint32 message_id = 1; //set 为 0, 未使用
    repeated OfficialMessageBody messages = 2;
}


// 对应 proto/pbfile/ga_base.proto : OfficialMessageBody
message OfficialMessageBody{
    required string title = 1;
    required string content = 2;
    required uint32 at = 3;
    optional string image = 4;  //url
    optional OfficialMessageAction action = 5;
} 

//action 对应 proto/pbfile/ga_base.proto : ControlBlock
message OfficialMessageAction{
    enum Action{
        IN_APP_NAVIGATION = 1;   //内部跳转
        GLOBAL_NAVIGATION = 2;   //外部跳转
    }
    required uint32 action = 1;
    optional string uri = 2;    //全路径
}

//
// 内部使用
//

message SendImBizStatus {
    enum Value {
        Nil = 0;
        Done = 1;  // all taskunit complete, maybe some taskunit failed
        Splitting = 2;
        ToSplit = 3; 
    }
}
message SendImTaskStatus {
    enum Value {
        Nil = 0;
        Done = 1;  // all taskunit complete, maybe some taskunit failed
        Sending = 2;
        ToSend = 3; 
        Failed = 4; // all taskunit failed
        Timeout = 5;
    }
} 
message SendImTaskUnitStage {
    enum Value {
        Init = 0;
        Complete = 1;
    }
} 
message SendImData { 
    optional Sender sender = 1;
    optional ImAccount sender_account = 2; 
    optional Receiver receiver = 3;
    repeated ImMsg msg_list = 4;
    optional bool with_notify = 5;
}; 
message ImAccount {
    required uint32 id = 1;
    required string account = 2;
    required string nickname = 3; 
    optional string alias = 4;
};

message WithdrawToken {
    required uint32 id = 1;
    required string suffix = 2;
    required uint32 seq_id = 3;
}
message MultiWithdrawToken {
    repeated WithdrawToken tokens = 1;
}

