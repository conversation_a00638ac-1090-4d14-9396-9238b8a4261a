syntax = "proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package userpresent;

message StConfigIosExtend
{
  bytes video_effect_url = 1;      // 特效url
}

message StPresentItemConfigExtend
{
  uint32 item_id = 1;
  bytes video_effect_url = 2;      // 特效url
  uint32 show_effect = 3;        // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE
  bool unshow_batch_option = 4;  // 是否展示批量送礼选项
  bool is_test = 5;          // 该礼物是否是测试 只有白名单用户可以拉取到
  uint32 flow_id = 6;        // 使用指定的流光id(流光配置不存在时，客户端会按默认的流光规则)
  StConfigIosExtend ios_extend = 7;  // ios的扩展信息
  bool notify_all = 8;      // 是否全服通知
  uint32 tag = 9;          // ga::PresentTagType
  bool force_sendable = 10;    // 强制可送的礼物(暂时只有背包礼物，这种礼物即使下架了也可以送出去)
  uint32 nobility_level = 11;     // 使用这个礼物的最低贵族等级
  bool unshow_present_shelf = 12;   //是否在礼物架上显示           true:不在礼物架显示          false:在礼物架显示
  bool show_effect_end = 13; // 是否展示礼物下架时间
  bool effect_end_delay = 14; // 是否支持延长上架时间
  repeated CustomText custom_text = 15;  // 礼物的自定义文案
  string small_vap_url = 16; // 小礼物的vap动效url
  string small_vap_md5 = 17; // 小礼物的vap动效md5
  string mic_effect_url = 18; // 麦位延展特效的动效url
  string mic_effect_md5 = 19; // 麦位延展特效的动效md5
  bool fusion_present = 20; // 是否是融合头像礼物
  bool is_box_breaking = 21; // 是否是需要开盒的全服礼物
  uint32 fans_level = 22; // 送礼所需粉丝团等级，tag = 11(粉丝团专属礼物) 才生效
}

message CustomText{
  string key = 1;
  repeated string text = 2;
}

// 礼物配置信息
message StPresentItemConfig
{
  uint32 item_id = 1;
  string name = 2;        // 名称
  string icon_url = 3;      // 图标url
  uint32 price = 4;        // 价格
  uint32 score = 5;        // 收到一个礼物 收礼者 增加的 积分
  uint32 charm = 6;        // 收到一个礼物 收礼者 增加的 魅力值
  uint32 rank = 7;        // 排名
  uint32 effect_begin = 8;    // 上架时间
  uint32 effect_end = 9;      // 下架时间
  uint32 update_time = 10;    // 更新时间
  uint32 create_time = 11;    // 添加时间
  bool   is_del = 12;        // 是否已删除
  uint32 price_type = 13;      // PresentPriceType
  uint32 rich_value = 14;      // 送出一个礼物 送礼者 增加的土豪值
  StPresentItemConfigExtend extend = 15;  // 扩展信息，这部分会整块存到mysql的一个字段里
  float rank_float = 16; // float类型的排序
}

// 购买礼物的货币
enum PresentPriceType {
  PRESENT_PRICE_UNKNOWN = 0;
  PRESENT_PRICE_RED_DIAMOND = 1;
  PRESENT_PRICE_TBEAN = 2;
}

// 用户的礼物汇总
message StUserPresentSummary
{
  uint32 uid = 1;
  uint32 item_id = 2;
  uint32 count = 3;
}

// 用户的礼物明细
message StUserPresentDetail
{
  uint32 from_uid = 1;
  uint32 target_uid = 2;
  StPresentItemConfig item_config = 3;
  uint32 receive_time = 4;
  uint32 item_count = 5;
  uint32 add_charm = 6; // 增加的魅力值
  string order_id = 7;
  uint32 item_id = 8;
  uint32 score = 9;
  uint32 send_source = 10;
  uint32 send_method = 11; //礼物来源 房间送礼：0，IM送礼：1
  uint32 add_rich = 12; // 增加的财富值
  bool is_ukw = 13;  // 是否是神秘人
}

// 获取礼物配置
message GetPresentConfigByIdReq
{
  uint32 item_id = 1;
}

message GetPresentConfigByIdResp
{
  StPresentItemConfig item_config = 1;
}

// 礼物配置列表类型
enum ConfigListTypeBitMap {
  CONFIG_UNLIMIT = 0;
  CONFIG_NOT_EXPIRED = 1;
  CONFIG_NOT_DELETED = 2;
}

message GetPresentConfigListReq
{
  uint32 type_bitmap = 1;
}

message GetPresentConfigListResp
{
  repeated StPresentItemConfig item_list = 1;
}

// 获取礼物配置加上本地内存存储，防止礼物配置更新读redis负载过高
// 需要带礼物配置的更新时间参数比较本地缓存是否失效
message GetPresentConfigListV2Req
{
  uint32 type_bitmap = 1;
  uint32 update_time = 2;
}

message GetPresentConfigListV2Resp
{
  repeated StPresentItemConfig item_list = 1;
  uint32 update_time = 2;
}


// 根据id列表获取礼物配置
message GetPresentConfigByIdListReq
{
  repeated uint32 item_id_list = 1;
  uint32 type_bitmap = 2;
}

message GetPresentConfigByIdListResp
{
  repeated StPresentItemConfig item_list = 1;
}

// 增加礼物配置
message AddPresentConfigReq
{
  string name = 1;          // 名称
  string icon_url = 2;        // 图标url
  uint32 price = 3;          // 价格
  uint32 effect_begin = 4;      // 上架时间
  uint32 effect_end = 5;        // 下架时间
  uint32 rank = 6;          // 排名
  uint32 price_type = 7;        // PresentPriceType
  StPresentItemConfigExtend extend = 8;
  float rank_float = 9; // float类型的排序
}

message AddPresentConfigResp
{
  StPresentItemConfig item_config = 1;
}

// 删除礼物配置
message DelPresentConfigReq
{
  uint32 item_id = 1;
}

// 更新礼物配置
message UpdatePresentConfigReq
{
  StPresentItemConfig item_config = 1;
}

message StPresentItemRank
{
  uint32 item_id = 1;
  uint32 rank = 2;
}

// 批量更新礼物排名
message BatchUpdatePresentRankReq
{
  repeated StPresentItemRank rank_list = 1;
}

// 获取礼物配置的更新时间
message GetPresentConfigUpdateTimeReq
{
}

message GetPresentConfigUpdateTimeResp
{
  uint32 update_time = 1;
}

// 赠送礼物
message SendPresentReq
{
  uint32 uid = 1;
  uint32 target_uid = 2;
  string order_id = 3;
  uint32 item_id = 4;
  uint32 channel_id = 5;
  uint32 guild_id = 6;
  uint32 item_count = 7;
  uint32 add_charm = 8;  // 增加的魅力值
  uint32 send_time = 9;
  StPresentItemConfig item_config = 10;
  bool opt_invalid = 11;
  bool async_flag = 12;  // 是否在礼物服务处理积分、金钻之类的
  string user_from_ip = 13;  // 送礼者的ip
  uint32 channel_type = 14;  //与 ga::ChannelType 的类型保持一致
  uint32 item_source = 15;  // ga::PresentSourceType
  string channel_name = 16;
  uint32 channel_display_id = 17;  // 用于oss上报
  uint32 send_source = 18;        // 赠送来源，用于 oss 上报
  uint32 send_platform = 19;       // 送礼人的平台类型，用于 oss 上报
  uint32 batch_type = 20;             // 0，单个送礼,1,全麦送礼，oss 上报
  uint32 app_id = 21;
  uint32 market_id = 22;
  uint32 receiver_guild_id = 23;
  uint32 giver_guild_id = 24;
  uint32 add_rich = 25;
  uint32 send_method = 26;  //送礼方法 0，房间送礼；1，IM送礼
  uint32 bind_channel_id = 27;  // 绑定的房间id
  string deal_token = 28; //送礼调用链校验
  string from_ukw_account = 29;  // 送礼神秘人账号，为空则不是神秘人
  string from_ukw_nickname = 30;  // 送礼神秘人昵称
  string to_ukw_account = 31;  // 收礼神秘人账号，为空则不是神秘人
  string to_ukw_nickname = 32;  // 收礼神秘人昵称
  uint32 channel_game_id = 33; //  房间弹幕游戏id
  bool is_virtual_live = 34; // 用户是否属于虚拟主播
  uint32 score_type = 35; // 积分类型
  uint32 send_channel_id = 36; // 送礼的房间id
}

// 获取用户的礼物数量总览
message GetUserPresentSummaryReq
{
  uint32 uid = 1;
  bool is_send = 2;  // 0.收到的礼物 1.送出的礼物
}

message GetUserPresentSummaryResp
{
  repeated StUserPresentSummary summary_list = 1;
  uint32 total_value = 2;
  uint32 total_count = 3;
}

// 获取用户的礼物数量总览(指定礼物)
message GetUserPresentSummaryByItemListReq
{
  uint32 uid = 1;
  bool is_send = 2;  // 0.收到的礼物 1.送出的礼物
  repeated uint32 item_list = 3;
}

message GetUserPresentSummaryByItemListResp
{
  repeated StUserPresentSummary summary_list = 1;
}

// 获取用户收到的礼物明细
message GetUserPresentDetailListReq
{
  uint32 uid = 1;
}

message GetUserPresentDetailListResp
{
  repeated StUserPresentDetail detail_list = 1;
}

// 获取用户送出的礼物明细
message GetUserPresentSendDetailListReq
{
  uint32 uid = 1;
}

message GetUserPresentSendDetailListResp
{
  repeated StUserPresentDetail detail_list = 1;
}

// 获取礼物订单的状态
message GetPresentOrderStatusReq
{
  uint32 uid = 1;
  string order_id = 2;
}

message GetPresentOrderStatusResp
{
  uint32 order_status = 1;  // 0.订单不存在
}

/****** 礼物送出的场景 ******/

// 礼物绑定类型
enum PresentSceneType
{
  UNKNOWN_PRESENT = 0;
  CHANNEL_PRESENT = 1;  // 在房间送出的礼物
  GUILD_PRESENT = 2;    // 在公会送出的礼物（只算公会房间送出的）
  USER_PRESENT = 3;    // 在个人名片送出的礼物
}

message StSceneInfo
{
  uint32 scene_id = 1;
  uint32 scene_type = 2;  // PresentSceneType
  uint32 sub_scene_id = 3;
}

message StScenePresentSummary
{
  uint32 scene_id = 1;
  uint32 scene_type = 2;  // PresentSceneType
  uint32 item_id = 3;
  uint32 count = 4;
  uint32 sub_scene_id = 5;
  uint64 count64 = 6;
}

message StScenePresentDetail
{
  uint32 scene_id = 1;
  uint32 scene_type = 2;  // PresentSceneType
  uint32 from_uid = 3;
  uint32 to_uid = 4;
  StPresentItemConfig item_config = 5;
  uint32 count = 6;
  string order_id = 7;
  uint32 create_time = 8;
  uint64 count64 = 9;
}

// 记录场景中送出的礼物
message RecordSceneSendPresentReq
{
  uint32 uid = 1;
  uint32 target_uid = 2;
  string order_id = 3;
  StPresentItemConfig item_config = 4;
  repeated StSceneInfo scene_list = 5;
  uint32 item_count = 6;
  uint32 send_time = 7;
}

// 获取场景的礼物汇总
message GetScenePresentSummaryReq
{
  StSceneInfo scene_info = 1;
}

message GetScenePresentSummaryResp
{
  repeated StScenePresentSummary summary_list = 1;
  uint32 total_value = 2;  // 送出的礼物总值
  uint32 total_count = 3;  // 送出的礼物总数
  uint64 total_count64 = 4;
  uint64 total_value64 = 5;
}

// 通知清空场景的礼物信息
message ClearScenePresentReq
{
  StSceneInfo scene_info = 1;
}

// 获取场景的礼物明细
message GetScenePresentDetailListReq
{
  StSceneInfo scene_info = 1;
  uint32 begin_time = 2;
  uint32 end_time = 3;
  uint32 offset = 4;
  uint32 limit = 5;
}

message GetScenePresentDetailListResp
{
  repeated StScenePresentDetail detail_list = 1;
}

/****************** 礼物流光 ******************/

// 礼物流光配置
message StPresentFlowConfig
{
  uint32 flow_id = 1;
  string url = 2;
  string md5 = 3;
  uint32 update_time = 4;
  uint32 create_time = 5;
  string desc = 6;  // 流光描述
}

// 获取礼物流光配置
message GetPresentFlowConfigByIdReq
{
  uint32 flow_id = 1;
}

message GetPresentFlowConfigByIdResp
{
  StPresentFlowConfig flow_config = 1;
}

// 获取礼物流光配置
message GetPresentFlowConfigListReq
{
}

message GetPresentFlowConfigListResp
{
  repeated StPresentFlowConfig flow_list = 1;
}

// 获取礼物流光配置的更新时间
message GetPresentFlowConfigUpdateTimeReq
{
}

message GetPresentFlowConfigUpdateTimeResp
{
  uint32 update_time = 1;
}

// 增加礼物流光配置
message AddPresentFlowConfigReq
{
  string flow_url = 1;
  string flow_md5 = 2;
  string flow_desc = 3;
}

// 删除礼物流光配置
message DelPresentFlowConfigReq
{
  uint32 flow_id = 1;
}

message UpdatePresentFlowConfigReq
{
  uint32 flow_id = 1;
  string flow_url = 2;
  string flow_md5 = 3;
  string flow_desc = 4;
}

/************ asyncjob notify ************/
enum ENUM_USER_PRESENT_ASYNC_TYPE
{
  USER_PRESENT_ASYNC_NONE = 0;
  USER_PRESENT_ASYNC_SEND = 1;
}

message PresentAsyncJobSendNotify
{
  uint32 send_uid = 1;
  uint32 target_uid = 2;
  string order_id = 3;
  uint32 channel_id = 4;
  uint32 guild_id = 5;
  uint32 item_count = 6;
  uint32 send_time = 7;
  StPresentItemConfig item_config = 8;
  bool opt_invalid = 9;
  bool async_flag = 10;
  uint32 channel_type = 11; //与 ga::ChannelType 的类型保持一致
  uint32 item_source = 12;  // ga::PresentSourceType
  string channel_name = 13;
  uint32 add_charm = 14;  // 增加的魅力值
  uint32 channel_display_id = 15;      // 用于oss上报
  uint32 send_source = 16;            // 赠送来源，用于 oss 上报
  uint32 send_platform = 17;          // 送礼人的平台类型，用于 oss 上报
  uint32 batch_type = 18;             // 0，单个送礼,1,全麦送礼，oss 上报
  uint32 app_id = 19;
  uint32 market_id = 20;

  uint32 receiver_guild_id = 21;
  uint32 giver_guild_id = 22;
  uint32 add_rich = 23;
  uint32 send_method = 24;
}

//message GetUserMonthScoreReq
//{
//	uint32 uid = 1;
//	uint32 timestamp = 2;
//}

//message GetUserMonthScoreResp
//{
//	uint32 score = 1;
//}

//message GetChannelsMonthScoreReq
//{
//	repeated uint32 channel_list = 1;
//	uint32 timestamp = 2;
//}

//message GetChannelsMonthScoreResp
//{
//	repeated uint32 score_list = 1;
//}


// 获取一段时间内所有用户收到礼物总数
message GetAllUserRecvPresentCountReq
{
  uint32 begin_time = 1;  //开始时间
  uint32 end_time = 2;    //结束时间
  int32 source = 3;  //礼物来源 -1所有 0.购买 1.背包
}

message GetAllUserRecvPresentCountResp
{
  uint32 total_count = 1;  // 送出的礼物总数量
}

// 获取一段时间内所有用户收到礼物订单详情
message GetAllUserRecvPresentOrderListReq
{
  uint32 begin_time = 1;
  uint32 end_time = 2;
  int32 source = 3;  //礼物来源 -1所有 0.购买 1.背包
}

message GetAllUserRecvPresentOrderListResp
{
  repeated string orders = 1;
}

message StUserPresentOrderLog
{
  uint32 from_uid = 1;
  uint32 target_uid = 2;
  uint32 change_score = 3;
  string order_id = 4;
  uint32 create_time = 5;
  string deal_token = 6; //校验信息
}

//(对账用) 根据orderId获取订单
message GetOrderLogByOrderIdsReq {
  repeated string order_id_list = 1;
}

message GetOrderLogByOrderIdsResp {
  repeated StUserPresentOrderLog order_log_list = 1;
}



/***********礼物冠名************/

message NamingPresentInfo
{
  uint32 id = 1;
  uint32 uid = 2;
  uint32 gift_id = 3;
  string naming_content = 4;
  uint32 begin_ts = 5;
  uint32 end_ts = 6;
  string account = 7;
}

message AddNamingPresentInfoReq
{
  NamingPresentInfo info = 1;
}
message AddNamingPresentInfoResp
{
}

message UpdateNamingPresentInfoReq
{
  NamingPresentInfo info = 1;
}
message UpdateNamingPresentInfoResp
{
}

message DelNamingPresentInfoReq
{
  uint32 id = 1;
}
message DelNamingPresentInfoResp
{
}

message GetNamingPresentInfoListReq
{
  uint32 offset = 1;
  uint32 limit = 2;
  uint32 uid = 3;
  uint32 gift_id = 4;
  uint32 begin_ts = 5;
  uint32 end_ts = 6;
}
message GetNamingPresentInfoListResp
{
  repeated NamingPresentInfo info_list = 1;
  uint32 next_offset = 2;
  uint32 total_cnt = 3;
}

//获取所有生效的礼物冠名信息
message GetValidNamingPresentInfosReq
{
}
message GetValidNamingPresentInfosResp
{
  repeated NamingPresentInfo info_list = 1;
}



/***********非全屏礼物动效模板配置************/

//动效模板配置
message DynamicEffectTemplate
{
  uint32 id = 1;
  string name = 2;
  string icon = 3;     //模板效果图片
  string url = 4;      //模板资源
  string md5 = 5;
  string op_account = 6;
  uint32 update_ts = 7;
  string bg_color = 8;
}

message AddDynamicEffectTemplateReq
{
  DynamicEffectTemplate template_info = 1;
}
message AddDynamicEffectTemplateResp
{
}

message UpdateDynamicEffectTemplateReq
{
  DynamicEffectTemplate template_info = 1;
}
message UpdateDynamicEffectTemplateResp
{
}

message DelDynamicEffectTemplateReq
{
  uint32 id = 1;
}
message DelDynamicEffectTemplateResp
{
}

message GetDynamicEffectTemplateListReq
{
  string name = 1;
  uint32 offset = 2;
  uint32 limit = 3;
}
message GetDynamicEffectTemplateListResp
{
  repeated DynamicEffectTemplate info_list = 1;
  uint32 total_cnt = 2;
  uint32 next_offset = 3;
}


// 礼物动效模板配置
message PresentEffectTemplateConfig
{
  uint32 id = 1;
  uint32 present_id = 2;
  uint32 present_cnt = 3;
  uint32 template_id = 4;
  string op_account = 5;
  uint32 update_ts = 6;
}

message AddPresentEffectTemplateConfigReq
{
  PresentEffectTemplateConfig info = 1;
}
message AddPresentEffectTemplateConfigResp
{
}

message UpdatePresentEffectTemplateConfigReq
{
  PresentEffectTemplateConfig info = 1;
}
message UpdatePresentEffectTemplateConfigResp
{
}

message DelPresentEffectTemplateConfigReq
{
  uint32 id = 1;
}
message DelPresentEffectTemplateConfigResp
{
}

message GetPresentEffectTemplateConfigListReq
{
  uint32 present_id = 1;
  uint32 template_id = 2;
  uint32 offset = 3;
  uint32 limit = 4;
}
message GetPresentEffectTemplateConfigListResp
{
  repeated PresentEffectTemplateConfig config_list = 1;
  uint32 total_cnt = 2;
  uint32 next_offset = 3;
}

// 获取动效模板配置和礼物动效配置
message GetPresentDynaminEffectTemplateConfigReq
{
}
message GetPresentDynaminEffectTemplateConfigResp
{
  repeated DynamicEffectTemplate template_list = 1;
  repeated PresentEffectTemplateConfig present_effect_list = 2;
  uint32 info_version = 3;
}

// 获取非全屏礼物动效模板配置的更新时间
message GetPresentDynamicTemplateConfUpdateTimeReq
{
}
message GetPresentDynamicTemplateConfUpdateTimeResp
{
  uint32 update_ts = 1;
}

// 获取指定礼物动效配置
message GetPresentDETConfigByIdReq
{
  uint32 present_id = 1;
}
message GetPresentDETConfigByIdResp
{
  repeated PresentEffectTemplateConfig present_effect_list = 1;
}

service UserPresent {
  option(tlvpickle.Magic) = 15565;    // 服务监听端口号

  rpc GetPresentConfigList(GetPresentConfigListReq) returns(GetPresentConfigListResp){
    option(tlvpickle.CmdID) = 1;
    option(tlvpickle.OptString) = "u:t:";
    option(tlvpickle.Usage) = "[-u <uid> -t <type_bitmap 1.CONFIG_NOT_EXPIRED 2.CONFIG_NOT_DELETED>]";
  }

  rpc GetPresentConfigById(GetPresentConfigByIdReq) returns(GetPresentConfigByIdResp){
    option(tlvpickle.CmdID) = 2;
    option(tlvpickle.OptString) = "x:";
    option(tlvpickle.Usage) = "-x <item_id>";
  }

  rpc AddPresentConfig(AddPresentConfigReq) returns(AddPresentConfigResp){
    option(tlvpickle.CmdID) = 3;
    option(tlvpickle.OptString) = "n:i:p:b:e:r:y:d:x:s:u:t:z:l:h:g:j:o:k:";
    option(tlvpickle.Usage) = "-n<name> -i<icon_url> -p<price> -b<effect_begin> -e<effect_end> -r<rank> -y<price_type> -d<item_id> -x<video_effect_url> -s<show_effect> -u<unshow_batch_option> -t<is_test> -z<flow_id> -l<ios_video_effect_url> -h<notify_all> -g<tag> -j<force_sendable> -o<nobility_level> -k<unshow_present_shelf>";
  }

  rpc DelPresentConfig(DelPresentConfigReq) returns(tlvpickle.SKBuiltinEmpty_PB){
    option(tlvpickle.CmdID) = 4;
    option(tlvpickle.OptString) = "x:";
    option(tlvpickle.Usage) = "-x <item_id>";
  }

  rpc UpdatePresentConfig(UpdatePresentConfigReq) returns(tlvpickle.SKBuiltinEmpty_PB){
    option(tlvpickle.CmdID) = 5;
    option(tlvpickle.OptString) = "d:n:i:r:b:e:x:s:u:t:z:l:h:g:j:o:k:";
    option(tlvpickle.Usage) = "-d<item_id> -n<name> -i<icon_url> -r<rank> -b<effect_begin> -e<effect_end> -x<video_effect_url> -s<show_effect> -u<unshow_batch_option> -t<is_test> -z<flow_id> -l<ios_video_effect_url> -h<notify_all> -g<tag> -j<force_sendable> -o<nobility_level> -k<unshow_present_shelf>";
  }

  rpc SendPresent(SendPresentReq) returns(tlvpickle.SKBuiltinEmpty_PB){
    option(tlvpickle.CmdID) = 6;
    option(tlvpickle.OptString) = "u:t:x:o:h:g:n:m:p:r:";
    option(tlvpickle.Usage) = "-u <uid> -t <target_uid> -x <item_id> -n <item_count> -o <order_id>  -m <add_charm> [-h <channel_id> -g <guild_id> -p <opt_invalid> -r <item_source>]";
  }

  rpc GetUserPresentSummary(GetUserPresentSummaryReq) returns(GetUserPresentSummaryResp){
    option(tlvpickle.CmdID) = 7;
    option(tlvpickle.OptString) = "u:";
    option(tlvpickle.Usage) = "-u <uid>";
  }

  rpc GetUserPresentDetailList(GetUserPresentDetailListReq) returns(GetUserPresentDetailListResp){
    option(tlvpickle.CmdID) = 8;
    option(tlvpickle.OptString) = "u:o:n:";
    option(tlvpickle.Usage) = "-u <uid> -o <offset> -n <limit>";
  }

  rpc RecordSceneSendPresent(RecordSceneSendPresentReq) returns(tlvpickle.SKBuiltinEmpty_PB){
    option(tlvpickle.CmdID) = 12;
    option(tlvpickle.OptString) = "u:t:x:n:o:s:p:b:";
    option(tlvpickle.Usage) = "-u <uid> -t <target_uid> -x <item_id> -n <item_count> -o <order_id> -s <scene_id> -p <scene_type 1.channel 2.guild> [-b <sub_scene_id>]";
  }

  rpc GetScenePresentSummary(GetScenePresentSummaryReq) returns(GetScenePresentSummaryResp){
    option(tlvpickle.CmdID) = 13;
    option(tlvpickle.OptString) = "s:p:b:";
    option(tlvpickle.Usage) = "-s <scene_id> -p <scene_type 1.channel 2.guild> [-b <sub_scene_id>]";
  }

  rpc ClearScenePresent(ClearScenePresentReq) returns(tlvpickle.SKBuiltinEmpty_PB){
    option(tlvpickle.CmdID) = 14;
    option(tlvpickle.OptString) = "s:p:b:";
    option(tlvpickle.Usage) = "-s <scene_id> -p <scene_type 1.channel 2.guild> [-b <sub_scene_id>]";
  }

  rpc BatchUpdatePresentRank(BatchUpdatePresentRankReq) returns(tlvpickle.SKBuiltinEmpty_PB){
    option(tlvpickle.CmdID) = 15;
    option(tlvpickle.OptString) = "x:n:";
    option(tlvpickle.Usage) = "-x <item_id> -n <rank>";
  }

  rpc GetPresentConfigUpdateTime(GetPresentConfigUpdateTimeReq) returns(GetPresentConfigUpdateTimeResp){
    option(tlvpickle.CmdID) = 16;
    option(tlvpickle.OptString) = "u:";
    option(tlvpickle.Usage) = "[-u <uid>]";
  }

  rpc GetScenePresentDetailList(GetScenePresentDetailListReq) returns(GetScenePresentDetailListResp){
    option(tlvpickle.CmdID) = 17;
    option(tlvpickle.OptString) = "s:p:b:e:o:n:";
    option(tlvpickle.Usage) = "-s <scene_id> -p <scene_type 1.channel 2.guild> [-b <begin_time> -e <end_time> -o <offset> -n <limit>]";
  }

  rpc GetPresentOrderStatus(GetPresentOrderStatusReq) returns(GetPresentOrderStatusResp){
    option(tlvpickle.CmdID) = 18;
    option(tlvpickle.OptString) = "u:o:";
    option(tlvpickle.Usage) = "-u <uid> -o <order_id>";
  }

  rpc GetPresentFlowConfigById(GetPresentFlowConfigByIdReq) returns(GetPresentFlowConfigByIdResp){
    option(tlvpickle.CmdID) = 23;
    option(tlvpickle.OptString) = "x:";
    option(tlvpickle.Usage) = "-x <flow_id>";
  }

  rpc GetPresentFlowConfigList(GetPresentFlowConfigListReq) returns(GetPresentFlowConfigListResp){
    option(tlvpickle.CmdID) = 24;
    option(tlvpickle.OptString) = "";
    option(tlvpickle.Usage) = "";
  }

  rpc GetPresentFlowConfigUpdateTime(GetPresentFlowConfigUpdateTimeReq) returns(GetPresentFlowConfigUpdateTimeResp){
    option(tlvpickle.CmdID) = 25;
    option(tlvpickle.OptString) = "";
    option(tlvpickle.Usage) = "";
  }

  rpc AddPresentFlowConfig(AddPresentFlowConfigReq) returns(tlvpickle.SKBuiltinEmpty_PB){
    option(tlvpickle.CmdID) = 26;
    option(tlvpickle.OptString) = "r:m:s:";
    option(tlvpickle.Usage) = "-r <url> -m <md5> [-s <desc>]";
  }

  rpc DelPresentFlowConfig(DelPresentFlowConfigReq) returns(tlvpickle.SKBuiltinEmpty_PB){
    option(tlvpickle.CmdID) = 27;
    option(tlvpickle.OptString) = "x:";
    option(tlvpickle.Usage) = "-x <flow_id>";
  }

  rpc UpdatePresentFlowConfig(UpdatePresentFlowConfigReq) returns(tlvpickle.SKBuiltinEmpty_PB){
    option(tlvpickle.CmdID) = 28;
    option(tlvpickle.OptString) = "x:r:m:s:";
    option(tlvpickle.Usage) = "-x <flow_id> -r <url> -m <md5> [-s <desc>]";
  }

  rpc GetUserPresentSummaryByItemList(GetUserPresentSummaryByItemListReq) returns(GetUserPresentSummaryByItemListResp){
    option(tlvpickle.CmdID) = 29;
    option(tlvpickle.OptString) = "u:x:t:";
    option(tlvpickle.Usage) = "-u <uid> -x <item_id> -t <is_send>";
  }

  rpc GetUserPresentDetailListNew(GetUserPresentDetailListReq) returns(GetUserPresentDetailListResp){
    option(tlvpickle.CmdID) = 30;
    option(tlvpickle.OptString) = "u:o:n:";
    option(tlvpickle.Usage) = "-u <uid> -o <offset> -n <limit>";
  }

  rpc GetUserPresentSendDetailList(GetUserPresentSendDetailListReq) returns(GetUserPresentSendDetailListResp){
    option(tlvpickle.CmdID) = 31;
    option(tlvpickle.OptString) = "u:o:n:";
    option(tlvpickle.Usage) = "-u <uid> -o <offset> -n <limit>";
  }

  //rpc GetUserMonthScore( GetUserMonthScoreReq ) returns( GetUserMonthScoreResp ){
  //	option( tlvpickle.CmdID ) = 31;
  //    option( tlvpickle.OptString ) = "u:m:";
  //    option( tlvpickle.Usage ) = "-u <uid> -m <month>";
  //}

  //rpc GetChannelsMonthScore( GetChannelsMonthScoreReq ) returns( GetChannelsMonthScoreResp ){
  //	option( tlvpickle.CmdID ) = 32;
  //    option( tlvpickle.OptString ) = "u:m:g:";
  //    option( tlvpickle.Usage ) = "-u <uid> -m <month> -g <guild>";
  //}

  rpc GetAllUserRecvPresentCount(GetAllUserRecvPresentCountReq) returns(GetAllUserRecvPresentCountResp){
    option(tlvpickle.CmdID) = 33;
    option(tlvpickle.OptString) = "";
    option(tlvpickle.Usage) = "";
  }

  rpc GetAllUserRecvPresentOrderList(GetAllUserRecvPresentOrderListReq) returns(GetAllUserRecvPresentOrderListResp){
    option(tlvpickle.CmdID) = 34;
    option(tlvpickle.OptString) = "";
    option(tlvpickle.Usage) = "";
  }

  rpc GetOrderLogByOrderIds(GetOrderLogByOrderIdsReq) returns(GetOrderLogByOrderIdsResp){
    option(tlvpickle.CmdID) = 35;
    option(tlvpickle.OptString) = "o:";
    option(tlvpickle.Usage) = " -o <order_id>";
  }

  rpc AddNamingPresentInfo(AddNamingPresentInfoReq) returns(AddNamingPresentInfoResp){
    option(tlvpickle.CmdID) = 36;
    option(tlvpickle.OptString) = "u:o:n:";
    option(tlvpickle.Usage) = "-u <uid> -o <offset> -n <limit>";
  }

  rpc UpdateNamingPresentInfo(UpdateNamingPresentInfoReq) returns(UpdateNamingPresentInfoResp){
    option(tlvpickle.CmdID) = 37;
    option(tlvpickle.OptString) = "u:o:n:";
    option(tlvpickle.Usage) = "-u <uid> -o <offset> -n <limit>";
  }

  rpc DelNamingPresentInfo(DelNamingPresentInfoReq) returns(DelNamingPresentInfoResp){
    option(tlvpickle.CmdID) = 38;
    option(tlvpickle.OptString) = "u:o:n:";
    option(tlvpickle.Usage) = "-u <uid> -o <offset> -n <limit>";
  }

  rpc GetNamingPresentInfoList(GetNamingPresentInfoListReq) returns(GetNamingPresentInfoListResp){
    option(tlvpickle.CmdID) = 39;
    option(tlvpickle.OptString) = "o:n:";
    option(tlvpickle.Usage) = "-o <offset> -n <limit>";
  }

  rpc GetValidNamingPresentInfos(GetValidNamingPresentInfosReq) returns(GetValidNamingPresentInfosResp){
    option(tlvpickle.CmdID) = 40;
    option(tlvpickle.OptString) = "u:o:n:";
    option(tlvpickle.Usage) = "-u <uid> -o <offset> -n <limit>";
  }

  rpc AddDynamicEffectTemplate(AddDynamicEffectTemplateReq) returns(AddDynamicEffectTemplateResp){
    option(tlvpickle.CmdID) = 41;
    option(tlvpickle.OptString) = "n:i:u:m:o:t:";
    option(tlvpickle.Usage) = "-n <name> -i <icon> -u <url> -m <md5> -o <op_account> -t <update_ts>";
  }

  rpc UpdateDynamicEffectTemplate(UpdateDynamicEffectTemplateReq) returns(UpdateDynamicEffectTemplateResp){
    option(tlvpickle.CmdID) = 42;
    option(tlvpickle.OptString) = "u:o:n:";
    option(tlvpickle.Usage) = "-u <uid> -o <offset> -n <limit>";
  }

  rpc DelDynamicEffectTemplate(DelDynamicEffectTemplateReq) returns(DelDynamicEffectTemplateResp){
    option(tlvpickle.CmdID) = 43;
    option(tlvpickle.OptString) = "u:o:n:";
    option(tlvpickle.Usage) = "-u <uid> -o <offset> -n <limit>";
  }

  rpc GetDynamicEffectTemplateList(GetDynamicEffectTemplateListReq) returns(GetDynamicEffectTemplateListResp){
    option(tlvpickle.CmdID) = 44;
    option(tlvpickle.OptString) = "u:o:n:";
    option(tlvpickle.Usage) = "-u <uid> -o <offset> -n <limit>";
  }

  rpc AddPresentEffectTemplateConfig(AddPresentEffectTemplateConfigReq) returns(AddPresentEffectTemplateConfigResp){
    option(tlvpickle.CmdID) = 45;
    option(tlvpickle.OptString) = "p:n:t:o:u:";
    option(tlvpickle.Usage) = "-p <present_id> -n <present_cnt> -t <template_id> -o <op_account> -u <update_ts>";
  }

  rpc UpdatePresentEffectTemplateConfig(UpdatePresentEffectTemplateConfigReq) returns(UpdatePresentEffectTemplateConfigResp){
    option(tlvpickle.CmdID) = 46;
    option(tlvpickle.OptString) = "u:o:n:";
    option(tlvpickle.Usage) = "-u <uid> -o <offset> -n <limit>";
  }

  rpc DelPresentEffectTemplateConfig(DelPresentEffectTemplateConfigReq) returns(DelPresentEffectTemplateConfigResp){
    option(tlvpickle.CmdID) = 47;
    option(tlvpickle.OptString) = "u:o:n:";
    option(tlvpickle.Usage) = "-u <uid> -o <offset> -n <limit>";
  }

  rpc GetPresentEffectTemplateConfigList(GetPresentEffectTemplateConfigListReq) returns(GetPresentEffectTemplateConfigListResp){
    option(tlvpickle.CmdID) = 48;
    option(tlvpickle.OptString) = "u:o:n:";
    option(tlvpickle.Usage) = "-u <uid> -o <offset> -n <limit>";
  }

  rpc GetPresentConfigByIdList(GetPresentConfigByIdListReq) returns(GetPresentConfigByIdListResp){
    option(tlvpickle.CmdID) = 49;
    option(tlvpickle.OptString) = "x:";
    option(tlvpickle.Usage) = "-x <item_id>";
  }

  rpc GetPresentDynaminEffectTemplateConfig(GetPresentDynaminEffectTemplateConfigReq) returns(GetPresentDynaminEffectTemplateConfigResp){
    option(tlvpickle.CmdID) = 50;
    option(tlvpickle.OptString) = "x:";
    option(tlvpickle.Usage) = "-x <item_id>";
  }

  rpc GetPresentDynamicTemplateConfUpdateTime(GetPresentDynamicTemplateConfUpdateTimeReq) returns(GetPresentDynamicTemplateConfUpdateTimeResp){
    option(tlvpickle.CmdID) = 51;
    option(tlvpickle.OptString) = "x:";
    option(tlvpickle.Usage) = "-x <item_id>";
  }

  rpc GetPresentDETConfigById(GetPresentDETConfigByIdReq) returns(GetPresentDETConfigByIdResp){
    option(tlvpickle.CmdID) = 52;
    option(tlvpickle.OptString) = "x:";
    option(tlvpickle.Usage) = "-x <item_id>";
  }

  rpc GetPresentConfigListV2(GetPresentConfigListV2Req) returns(GetPresentConfigListV2Resp){
    option(tlvpickle.CmdID) = 53;
    option(tlvpickle.OptString) = "u:t:h:";
    option(tlvpickle.Usage) = "-u <uid> -t <type> -h <update_time>";
  }
}
