syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Channel;

// 与 ga::ChannelType 的类型保持一致
enum ChannelBindType {
	GUILD_BIND_CHANNEL = 1;                   // 公会绑定的普通频道
	FREE_BIND_CHANNEL = 2;                    // 不和任何ID绑定的 独立的频道 目前只有欢城使用
	USER_BIND_CHANNEL = 3;                    // 与用户绑定的频道
	GUILD_PUBLIC_FUN_BIND_CHANNEL = 4;        // 公会绑定的公开娱乐频道
}

// 与 ga::EChannelAdminRoleType 的类型保持一致
enum ChannelAdminRole {
	CHANNEL_INVALID_ROLE = 0;           // 无效
	CHANNEL_OWNER = 1;                  // 频道所有者
	CHANNEL_ADMIN = 2;                  // 频道管理员
	CHANNEL_NOMAL_ROLE = 3;             // 普通用户
	CHANNEL_ADMIN_SUPER = 4;            // 频道超级管理员
	CHANNEL_ADMIN_ROLE_MAX = 255;            // 
}

enum ChannelSortType {
	NOT_SORT = 0;
	SORT_BY_USER_COUNT = 1;	// 根据房间人数排序
}

message ChannelMemberRole
{
	optional uint32 guild_role = 1;   // 废弃字段 无效
	optional uint32 group_role = 2;   // 废弃字段 无效
	optional uint32 channel_role = 3;
}

// 房间成员
message ChannelMemberBaseInfo {
    required uint32 uid  = 1;         // 成员uid
    required uint32 ts   = 2;         // 时间
}
message ChannelMemberDetailInfo {
	required uint32 uid  = 1;         // 成员uid
	required uint32 ts   = 2;         // 时间
	required uint32 is_robot = 3;     // 是否是机器人 1表示是机器人
	required uint32 group_role = 4;   // 废弃字段 无效
	required bool is_holdmic = 5;
	required bool is_mute = 6;
	optional uint32 reddiamond   = 7; // 在房间内历史消费的红钻值(or T豆值)
}

// 麦位信息
message MicrSpaceInfo
{
	required uint32 mic_id = 1;                       // 麦位ID 1 - 9
	optional uint32 mic_state = 2;                    // ga::EMicrSpaceState 麦位状态 1 正常 2 禁用 3 不可发言
	optional uint32 mic_uid = 3;                      // 麦上用户 如果麦上有人的话
	optional uint32 mic_ts = 4;                       // 麦位最后更新时间
}

message ChannelSimpleInfo {
    required uint32 channel_id              = 1;  // 频道id   
    required uint32 display_id              = 2;  // 显示Id
	optional uint32 app_id                  = 3;  // 频道对应的APPID
	optional bool   has_pwd                 = 4;  // 是否有密码  //以后尽量不要使用该字段，可以用进房控制类型enter_control_type来判断
	optional uint32 channel_type            = 5;  // 频道类型 see ga::ChannelType
	optional uint32 bind_id                 = 6;
	optional uint32 switch_flag             = 7;  // 各种房间开关的标记 
	optional uint32 creater_uid             = 8;
	optional uint32 create_ts               = 9;
	
	optional string icon_md5                = 10;  // 房间图标
	optional string topic_title             = 11;  // 房间话题描述（标题）
	optional string passwd                  = 12;  // 密码 明文
	optional string name                    = 13;  // 名字
	optional bool is_del                    = 14;  // 是否是删除

	optional uint32 enter_control_type      = 15;  //进房控制,密码进房，白名单进房等，参考channel_.proto的EnterControlType

	optional string channel_view_id = 16;    //增加新的string类型房间显示id，旧的uint32类型display_id继续保留，只有在显示id超过uint32范围时，两者会不同
}


// 该结构废弃
message ChannelBaseInfo {
    required uint32 channel_id              = 1;  // 频道id   
    required string channel_name            = 2;  // 频道名字
    required uint32 channel_member_size     = 3;  // 频道人数
    required uint32 sdk_session_id          = 4;  // SDK语音房间id
	optional uint32 app_id                  = 5;  // 频道对应的APPID
	optional bool   has_pwd                 = 6;  // 是否有密码
	optional string passwd                  = 7;  // 密码
    optional uint32 display_id              = 8;  // 显示Id
	optional string icon_md5                = 9;  // 房间图标
	optional string desc                    = 10; // 房间话题描述（标题）
}

// 该结构废弃
message ChannelDetailInfo
{
	required ChannelBaseInfo channel_baseinfo = 1;
	optional uint32 creater_uid = 2;
	optional uint32 create_ts = 3;
	optional uint32 channel_bind_type = 4; // 频道类型 see ga::ChannelType, 1:guild 2:free
	optional uint32 bind_id = 5;           // 如果频道类型为公会绑定的 这里填写公会ID
	optional uint32 disable_mic_size = 6;  // 被禁用的麦位数量 // 该字段废弃
    optional uint32 mic_mode = 7;          // mic模式
	optional uint32 switch_flag = 8;       // 各种房间开关的标记 
	optional bool is_temp_alloced = 9;     // 是否处于临时分配状态 只有临时房 才有这个状态
}


// 创建频道
message CreateChannelReq
{
	required string name = 1;
	required uint32 creater_uid = 2;
	required uint32 channel_bind_type = 3;  // see ga::ChannelType, 1:公会普通房 2:欢城用的房间 3:个人房 4:公会开黑房
	optional uint32 bind_id = 4;            // 表明新创建的频道是与哪个ID绑定的,在 channel_bind_type 为 GUILD_BIND_CHANNEL 以及 GUILD_PUBLIC_FUN_BIND_CHANNEL 时 bindID表示公会ID
	optional uint32 creater_guild_role = 5; // 创建者 公会角色 1=会长 2=副会长 仅仅在channel_bind_type == GUILD_BIND_CHANNEL起效 (不用填了 字段废弃)
	optional uint32 creater_group_role = 6; // 创建者 群角色   1=群主 2=群管理 仅仅在channel_bind_type == GUILD_BIND_CHANNEL起效 (不用填了 字段废弃)
	optional uint32 appid = 7;              // appid 参见protodef.h文件 protocol::EAppID 用于描述使用频道服务的第三方应用 直播使用11
	optional bool has_pwd = 8;              // 是否有密码
	optional string passwd = 9;             // 密码
	optional uint32 guild_level = 11;       // 公会等级，用于创建公会公开娱乐房
	optional uint32 market_id = 12;         // 配合 appid 区分 app
}

message CreateChannelResp
{
	required uint32 channel_id = 1; // 服务器内部唯一标识
	required uint32 sdk_session_id = 2; // 用于语音SDK使用的唯一会话标识, 目前两个ID的值一样
	
	optional uint32 guild_channel_count = 3;   // 根据创建的类型 返回该类型下 公会已经创建的房间数量
	optional uint32 display_id = 4;            // 如果创建的是公会公开房 那么是有显示ID 的
}

// 解散频道
message DissolveChannelReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
}

// 修改房间属性（名字 密码 描述 头像 标签 各种标志位）(2022-11-15:修改密码请使用ModifyChannelEnterControlType)
//2024-03-19:修改房间名，房间开关，房间密码请使用channel-go的接口
message ModifyChannelReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
	optional string name = 3;     // 为空表示 不修改 频道名称
	optional uint32 pwd_flag = 4; // 修改密码的标记, 0表示没有密码即把密码取消 1表示有密码 2 是不修改密码
	optional string passwd = 5;   // 密码
	optional string icon_md5 = 6; // 头像
	optional string desc = 7;     // 描述(房间话题的标题)
	optional uint32 switch_flag_bitmap = 8; // 各种开关的全量数据 没有修改的字段也要传过来
	optional string topic_detail = 9;       // 房间话题的详细内容
	optional string welcome_msg = 10;               // 如果是修改房间欢迎语 这里填房间欢迎语内容

	optional uint32 switch_flag_bits_add = 11;    // 需要打开的开关全量数据, switch_flag_bitmap 不存在时生效
	optional uint32 switch_flag_bits_del = 12;    // 需要关闭的开关全量数据, switch_flag_bitmap 不存在时生效
}
message ModifyChannelResp
{
}

// 修改进房控制类型 //2024-03-19:请使用channel-go的接口
message ModifyChannelEnterControlTypeReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
	required uint32 enter_control_type = 3;       //进房控制,密码进房，白名单进房等，参考channel_.proto的EnterControlType
	optional string passwd = 4;                   //密码进房时，不能为空
}
message ModifyChannelEnterControlTypeResp
{
}

// 加入频道
//message EnterChannelReq
//{
//	required uint32 channel_id = 1;
//	required uint32 uid = 2;
//	optional uint32 enter_source = 3; // see ga::ChannelEnterReq::EChannelEnterSource 来源字段 0为默认 5表示是机器人
//	optional uint32 group_role = 4; // (废弃字段) 群角色   1=群主 2=群管理
//}
//message EnterChannelResp
//{
//	required uint32 channel_id = 1;
//	
//	required uint32 member_size = 2;  // 加入以后的成员数量
//	optional bool is_already_join= 3; // 是否 已经加入了该房间 但是还没有退出
//	
//	optional bool is_admin_join= 4;        // 是否 是房间管理员加入  (该字段仅对个人房和公开娱乐房有效)
//	optional uint32 online_admin_size = 5; // 当前房间在线管理员数量 (该字段仅对个人房和公开娱乐房有效)
//	optional string welcome_msg = 6; //
//}
//
// 退出频道
//message QuitChannelReq
//{
//	required uint32 channel_id = 1;
//	required uint32 uid = 2;
//}
//message QuitChannelResp
//{
//	optional bool is_real_quit = 1;  // 是否真实执行了退出操作
//	optional bool is_admin_quit = 2; // 是否是以房间管理员身份退出房间
//	optional uint32 before_hold_micid = 3; // 退房之前是否已经持有的麦位ID，0表示没有持有麦
//	optional uint32 remain_member_cnt = 4;
//	
//}

// 开麦(上麦)// 接口废弃 已经不再支持
message HoldMicrSpaceReq
{
	required uint32 channel_id = 1;
	required uint32 uid = 2;
	optional uint32 guild_role = 3;  // 公会角色 1=会长 2=副会长 (废弃字段)
	optional uint32 group_role = 4;  // 群角色   1=群主 2=群管理 (废弃字段)
	optional uint32 mic_pos_id = 5;  // 麦位ID
	optional bool is_force = 6;      // 是否强行上麦 必须指定mic_pos_id 如果指定的麦位上有人则踢人下麦
}
// 接口废弃 已经不再支持
message HoldMicrSpaceResp
{
	optional MicrSpaceInfo open_mic_info = 1; // 麦位
	optional uint32 kick_out_uid = 2;         // 被踢下的用户 只有在is_force=true时才有可能
	repeated MicrSpaceInfo all_mic_list = 3;  // 全体麦位信息 包括各个麦位状态
	optional uint64 server_time_ms  = 4;      // 64bit 毫秒级 服务器时间
}

// 关麦(下麦)// 接口废弃 已经不再支持
message ReleaseMicrSpaceReq
{
	required uint32 channel_id = 1;
	required uint32 uid = 2;
}
// 接口废弃 已经不再支持
message ReleaseMicrSpaceResp
{
	optional MicrSpaceInfo close_mic_info = 1;  // 成功被下麦的麦位信息 如果没有下麦那么该值为空
	repeated MicrSpaceInfo all_mic_list = 2;    // 全体麦位信息 包括各个麦位状态
	optional uint64 server_time_ms  = 3;        // 64bit 毫秒级 服务器时间
	optional bool is_auto_disable_mic = 4;      // 是否自动完成了锁麦
}

// 接口废弃 已经不再支持
// 检查并从指定的绑定上频道的麦列表上剔除某人
// 比如从公会下所有频道踢某人下麦
message CheckAndKickChannelMicrByBindIdReq
{
	required uint32 uid = 1;
	optional uint32 channel_bind_type = 2; // 频道类型 see ga::ChannelType, 1:guild 2:free 如果是踢出公会内的频道麦上用户 需要填写
	optional uint32 bind_id = 3;           // 如果频道类型为公会绑定的 这里填写公会ID
}

message CheckAndKickChannelMicrByBindIdResp
{
	required uint32 uid = 1;
	required bool is_kicked = 2;
	optional uint32 channel_bind_type = 3; // 频道类型 see ga::ChannelType, 1:guild 2:free
	optional uint32 bind_id = 4;           // 如果频道类型为公会绑定的 这里填写公会ID
	repeated uint32 kicked_channel_id_list = 5; // 如果有踢除麦列表 这里填写被踢出的频道ID
}




// 频道禁言(有响应的禁言+踢麦)
// 废弃 改用 MuteChannelMemberReq
message SetChannelMuteAndKickMicReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
	repeated uint32 target_uid_list = 3;
}
message SetChannelMuteAndKickMicResp
{
	required uint32 channel_id = 1;
	repeated uint32 mute_uid_list = 2;
	repeated uint32 kickmic_uid_list = 3;
	repeated MicrSpaceInfo mute_kick_mic_list = 4;  // 由于禁言被踢下的麦位
	repeated MicrSpaceInfo all_mic_list = 5;        // 禁言和踢下麦后 全体麦位信息 包括各个麦位状态 仅在mute_kick_mic_list不空的情况下 才会填充
	optional uint64 server_time_ms  = 6;            // 64bit 毫秒级 服务器时间 仅在有踢人下麦情况下 会填充
}

// 解除频道禁言
// 废弃 改用 UnmuteChannelMemberReq
message UnsetChannelMuteReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
	repeated uint32 target_uid_list = 3;
}

// 频道禁言
message MuteChannelMemberReq {
	required uint32 channel_id = 1;
	repeated uint32 target_uids = 2;
}
message MuteChannelMemberResp {
	required uint32 channel_id = 1;
	repeated uint32 muted_uids = 2;
}
message UnmuteChannelMemberReq {
	required uint32 channel_id = 1;
	repeated uint32 target_uids = 2;
}
message UnmuteChannelMemberResp {
}

// 获取频道详细信息
message GetChannelDetailInfoReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
}

message GetChannelDetailInfoResp
{
	required ChannelBaseInfo channel_baseinfo = 1;
	required uint32 creater_uid = 2;
	required uint32 create_ts = 3;
	required uint32 channel_bind_type = 4; // 频道类型 see ga::ChannelType, 1:guild 2:free
	optional uint32 bind_id = 5;           // 如果频道类型为公会绑定的 这里填写公会ID
	optional uint32 disable_mic_size = 6;  // 被禁用的麦位数量(该字段废弃)
    optional uint32 mic_mode = 7;
	optional uint32 switch_flag = 8;       // 各种房间开关的标记 
	optional bool is_temp_alloced = 9;     // 是否处于临时分配状态 只有临时房 才有这个状态
}

message GetChannelDetailInfoBatchReq
{
	required uint32 op_uid = 1;
	repeated uint32 channel_id_list = 2;
	optional uint32 max_getsize = 3;
	optional uint32 sort_type = 4; // ChannelSortType
}

message GetChannelDetailInfoBatchResp
{
    repeated ChannelDetailInfo channel_detail_list = 1;
}

// 获取频道基础信息
message GetChannelSimpleInfoReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
	optional uint32 display_id = 3;
	optional string channel_view_id = 4;
}

message GetChannelSimpleInfoResp
{
	required ChannelSimpleInfo channel_simple = 1;
}
message BatchGetChannelSimpleInfoReq
{
	required uint32 op_uid = 1;
	repeated uint32 channel_id_list = 2;
	repeated uint32 display_id_list = 3;
	repeated string channel_view_id_list = 4;
}

message BatchGetChannelSimpleInfoResp
{
    repeated ChannelSimpleInfo channel_simple_list = 1;
}

// 获取频道成员数目 (废弃过期)
message GetChannelMemberSizeReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
}

message GetChannelMemberSizeResp
{
	required uint32 channel_id = 1;
	required uint32 member_size = 2;
}

// 获取频道成员列表
//message GetChannelMemberListReq
//{
//	required uint32 channel_id = 1;
//	required uint32 op_uid = 2;
//	required uint32 start_idx = 3;
//	required uint32 getsize = 4;
//}

//message GetChannelMemberListResp
//{
//	required uint32 channel_id = 1;
//	repeated ChannelMemberDetailInfo member_list = 2;
//	required uint32 all_member_size = 3;
//}

// 获取频道禁言成员列表
message GetChannelMuteListReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
}

message GetChannelMuteListResp
{
	required uint32 channel_id = 1;
	repeated ChannelMemberBaseInfo mute_list = 2;
}

// 接口废弃 不再支持
// 获取麦列表
message GetMicrListReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
}

// 接口废弃 不再支持
message GetMicrListResp
{
	required uint32 channel_id = 1;
	repeated ChannelMemberBaseInfo openMicr_list = 2; // (字段废弃) 当前上麦用户列表
	optional uint32 all_disabled_micsize = 3;         // (字段废弃) 当前总共被关闭的麦位数量
	repeated MicrSpaceInfo all_mic_list = 4;          // 全体麦位信息 包括各个麦位状态
	optional uint32 micr_mode = 5;                    // 麦模式
	optional uint64 server_time_ms  = 6;              // 64bit 毫秒级 服务器时间
}

// 获取与绑定ID关联的 频道列表
// 比如获取指定公会ID绑定的频道列表(按照当前人数排序)
message GetChannelListByBindIdReq
{
	required uint32 op_uid = 1;
	required uint32 max_getsize = 2;
	required uint32 bindID = 3;
	required uint32 bind_type = 4; // see ga::ChannelType, 1:guild 2:free
}

message GetChannelListByBindIdResp
{
	required uint32 bindID = 1;
	required uint32 bind_type = 2; // 频道类型 see ga::ChannelType, 1:guild 2:free
	repeated ChannelBaseInfo channel_list = 3;
	optional uint32 total_channel_size = 4;
}

// 获取的频道的绑定关系
// 比如获取频道是属于哪个公会的
message GetChannelBindInfoReq
{
	required uint32 channelID = 1;
	required uint32 op_uid = 2;
}

message GetChannelBindInfoResp
{
	required uint32 bindID = 1;
	required uint32 bind_type = 2; // 频道类型 see ga::ChannelType 1:guild 2:free 。。。
	optional uint32 app_id = 3;
}

// 获取用户当前频道ID
//message GetUserChannelIDReq
//{
//	required uint32 uid = 1;
//}
//
//message GetUserChannelIDResp
//{
//	required uint32 channelID = 1;
//	optional uint32 appid = 2;      // 房间的APP类型
//}
//
message UserChannelID{
    required uint32 uid = 1;
    required uint32 channelID = 2;  // 0=not exist
    optional uint32 appid = 3;      // 房间的APP类型
}

//message BatchGetUserCurrChannelIDReq
//{
//	repeated uint32 uid_list = 1;
//}
//
//message BatchGetUserCurrChannelIDResp
//{
//	repeated UserChannelID user_channel_list = 1;
//}
//
// 用户心跳
//message UserHeartbeatReq
//{
//	required uint32 uid = 1;
//	required uint32 channelID = 2;
//	optional uint32 ts = 3;
//}

// 解散某个绑定ID下面的所有频道
// 比如解散公会下的全部频道
//message DismissAllBindedChannelReq
//{
//	required uint32 op_uid = 1;
//	required uint32 bindID = 2;
//	required uint32 bind_type = 3; // 频道类型 see ga::ChannelType, 0:free 1:guild
//}
//message DismissAllBindedChannelResp
//{
//	required uint32 bindID = 1;
//	required uint32 bind_type = 2; // 频道类型 see ga::ChannelType, 0:free 1:guild
//	repeated uint32 channel_id_list = 3;
//}

// 检查用户是否被禁言
message CheckUserIsMuteReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
}
message CheckUserIsMuteResp
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required bool is_mute = 3;
}


message User2ChannelRelation
{
	required uint32 uid = 1;
	optional uint32 channelid = 2;
}

// 将用户踢出公会下面的所在频道
// 比如用户被踢出公会 需要被踢出当前公会所在的频道
message KickUserOutOfGuildChannelReq
{
	required uint32 op_uid = 1;
	required uint32 bindID = 2;
	required uint32 bind_type = 3; // 无效值
	repeated uint32 uid_list = 4;
}
message KickUserOutOfGuildChannelResp
{
	required uint32 bindID = 1;
	required uint32 bind_type = 2; // 无效值
	repeated User2ChannelRelation user2channel_list = 3; // 用户被实际踢出的频道关系
}

// 接口废弃不再支持
// 关闭麦位入口
message DisableChannelMicEntryReq
{
	required uint32 op_uid = 1;
	required uint32 channel_id = 2;
	optional uint32 disable_mic_size = 3;
	optional uint32 kick_uid = 4;    // 如果麦位上正好有个银 需要把这个银给踢走
	optional uint32 mic_pos_id = 5;  // 指定 被关闭麦位的ID
}
// 接口废弃不再支持
message DisableChannelMicEntryResp
{
	required uint32 channel_id = 1;
	required uint32 all_close_size = 2;     // (废弃字段) 当前总共被关闭的麦位数量
	optional uint32 mic_pos_id = 3;         // 被关闭的麦位ID
	repeated MicrSpaceInfo all_mic_list = 4;// 当前全量麦位列表信息
	optional uint64 server_time_ms  = 5;      // 64bit 毫秒级 服务器时间
}

// 接口废弃不再支持
// 开启麦位入口
message EnableChannelMicEntryReq
{
	required uint32 op_uid = 1;
	required uint32 channel_id = 2;
	optional uint32 mic_pos_id = 3;  // 需要开启的指定麦位ID
	optional bool open_mic = 4;      // 是否需要同时上麦
}
// 接口废弃不再支持
message EnableChannelMicEntryResp
{
	required uint32 channel_id = 1;
	required uint32 all_close_size = 2;      // (废弃字段) 当前还有多少个被关闭的麦位
	optional uint32 mic_pos_id = 3;          // 被开启的麦位ID
	repeated MicrSpaceInfo all_mic_list = 4; // 当前全量麦位列表信息
	optional uint64 server_time_ms  = 5;      // 64bit 毫秒级 服务器时间
}

// 踢下麦 // 接口废弃不再支持
message KickoutChannelMicReq
{
	required uint32 op_uid = 1;
	required uint32 channel_id = 2;
	repeated uint32 target_uid_list = 3;
	
	optional uint32 ban_second = 4; // 踢下麦后 多长时间 禁止上麦。 0为不限制
}
// 接口废弃不再支持
message KickoutChannelMicResp
{
	required uint32 channel_id = 1;
	
	repeated uint32 disable_mic_id_list = 2;     // 如果开启了自动锁麦 这里是被锁的麦位ID列表

	repeated MicrSpaceInfo kickout_mic_list = 3; // 成功被踢的麦位列表
	repeated MicrSpaceInfo all_mic_list = 4;     // 当前全量麦位列表信息
	optional uint64 server_time_ms  = 5;         // 64bit 毫秒级 服务器时间
}

// 踢人// 接口废弃不再支持
message KickoutChannelMemberReq
{
	required uint32 op_uid = 1;
	required uint32 channel_id = 2;
	repeated uint32 target_uid_list = 3;
	optional uint32 ban_enter_second = 4;
}
// 接口废弃不再支持
message KickoutChannelMemberResp
{
	required uint32 channel_id = 1;

	repeated uint32 success_uid_list = 2;
}

message CheckUserKickoutFromChannelReq{
    required uint32 channel_id = 1;
    required uint32 uid = 2;
}
message CheckUserKickoutFromChannelResp{
    optional bool is_kicked = 1;
}
message KickoutChannelMemberLiteReq {
	required uint32 channel_id = 1;
	repeated uint32 target_uids = 2;
	optional uint32 ban_duration = 3;
}
message KickoutChannelMemberLiteResp {
	required uint32 channel_id = 1;
	repeated uint32 kickouted_uids = 2;
}


//根据displayId查找channel
message GetChannelByDisplayIdReq
{
    required uint32 display_id = 1;  // channel_diplayID 不同APP间可以数值是重叠的 所以必须指定APPID
	required uint32 app_id = 2;
}

message GetChannelByDisplayIdResp
{
    required ChannelDetailInfo channel_info = 1;
}

//channel的管理权限
message UserChannelRoleInfo
{
	required uint32 uid = 1;
	required uint32 role = 2;
	optional ChannelDetailInfo channel_info = 3;
}

message GetUserChannelRoleListReq
{
    required uint32 uid = 1;
    required uint32 admin_role = 2; //筛选用, 0代表全部
    optional uint32 appid = 3;      // for create channel ,客户端带来的
    optional uint32 market_id = 4;  // for create channel ,客户端带来的
}

message GetUserChannelRoleListResp
{
    repeated UserChannelRoleInfo role_list = 1;
}

// 修改mic模式 // 接口废弃 不再支持
message SetChannelMicModelReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required uint32 mic_model = 3;              // 1主席模式 2自由模式 3娱乐模式 see ga::EChannelMicMode
    optional bool is_disable_all_mic = 4;       // 是否锁定所有的麦位 仅在 HAVE_MIC_SPACE_MODE 模式下有效
    optional bool is_need_hold_mic = 5;         // 本人是否需要上麦 仅在HAVE_MIC_SPACE_MODE模式下有效
	optional ChannelMemberRole op_uid_role = 6; // (废弃字段) 在操作者需要上麦的情况下填写
	
}
// 接口废弃 不再支持
message SetChannelMicModelResp
{
    required uint32 channel_id = 1;
    required uint32 mic_mode = 2;
	
    optional uint32 disabled_mic_size = 3; // (废弃字段) 本次锁麦数量
    repeated uint32 kick_uid_list = 4;     // (废弃字段) 被T的人
	optional uint32 enabled_mic_size = 5;  // (废弃字段) 本次开启麦位的数量
	optional uint32 hold_mic_uid = 6;      // (废弃字段) 本次成功上麦的用户ID
	
	// 因为本次模式切换 而变化的麦位信息
	repeated MicrSpaceInfo disable_mic_list = 7;
	repeated MicrSpaceInfo enable_mic_list = 8;  
	repeated MicrSpaceInfo kickout_mic_list = 9;
	optional MicrSpaceInfo hold_mic_info = 10;
	
	// 模式设置之后 当前的麦位信息
	repeated MicrSpaceInfo all_mic_list = 11; 

	optional uint64 server_time_ms  = 12;      // 64bit 毫秒级 服务器时间	
	
	optional uint32 from_mic_mode = 13; // 修改前的麦模式
}

// 通过displayID创建频道
message CreateChannelByDisplayIDReq
{
    required uint32 display_id = 1;         // 显示ID
	required string name = 2;
	required uint32 channel_bind_type = 3;  // see ga::ChannelType, 1:guild 2:free
	optional uint32 bind_id = 4;            // 表明新创建的频道是与哪个ID绑定的,目前只有在channel_bind_type == GUILD_BIND_CHANNEL时bindID表示公会ID
	optional uint32 appid = 5;              // appid 参见protodef.h文件 protocol::EAppID 用于描述使用频道服务的第三方应用
	optional string passwd = 6;             // 密码
	optional uint32 mic_mode = 7;           // mic模式(废弃字段，channelsvr 不再获取mic信息)
	optional uint32 creator_uid = 8;        // 创建者UID 
	optional uint32 market_id = 9;        // 创建者UID 
}

message CreateChannelByDisplayIDResp
{
    required uint32 channel_id = 1;
    required uint32 display_id = 2;
    required uint32 sdk_session_id = 3;
}




// 房间在线系统 通知房间用户心跳超时

//message NotifyUserHeartbeatExpireReq
//{
//	repeated uint32 uid_list = 1;
//}

//message NotifyUserHeartbeatExpireResp
//{
//}
//
// 分配一个临时房间ID
message AllocTempChannelReq
{
	required uint32 channel_type  = 1; // see ga::ChannelType
}

message AllocTempChannelResp
{
    required uint32 channel_id              = 1;  // 频道id   
    required string channel_name            = 2;  // 频道名字
    required uint32 sdk_session_id          = 3;  // SDK语音房间id
	required uint32 app_id                  = 4;  // 频道对应的APPID
	required uint32 channel_type  			= 5;  // see ga::ChannelType
	optional uint32 display_id              = 6;  // 显示Id
}

message ReleaseTempChannelReq {
    required uint32 channel_id = 1;
	required uint32 bind_type = 2;   
}
message ReleaseTempChannelResp {
}

// 批量分配多个临时房间ID
message BatchAllocTempChannelReq
{
	required uint32 channel_type  = 1;    // see ga::ChannelType
	required uint32 batch_count   = 2;    // 批量申请的房间数目
}

message BatchAllocTempChannelResp
{
	repeated AllocTempChannelResp temp_channels = 1;
}

// 批量释放多个临时房间
message BatchReleaseTempChannelReq {
    repeated uint32 channel_ids = 1;
    required uint32 bind_type   = 2;
}

message BatchReleaseTempChannelResp {
}

// 查询临时房池余量
message QueryTempChannelsNumReq {
	required uint32 channel_type = 1;
}

message QueryTempChannelsNumResp {
	required uint32 total_num = 1;    // 总数量
	required uint32 idle_num  = 2;    // 空闲数量
}

// 检查临时房的分配状态
message CheckTmpChannelIsAllocedReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required uint32 channel_type = 3;
}
message CheckTmpChannelIsAllocedResp
{
	required bool is_alloced = 1;
}

message BatchCheckIsTempAllocChannelReq
{
	repeated uint32 channelid_list  = 1; // 检查这些房间ID 是否是临时分配房
}

message BatchCheckIsTempAllocChannelResp
{
    repeated uint32 tempalloc_id_list  = 1; // 确认是临时分配房的ID 列表
}

// 配置歌曲
message ConfigMusicInfo
{
	required string music_name = 1;            // 音乐名
	required string download_url = 2;          // 下载地址
	optional string artist = 3;                // 歌手
	optional string img_url = 4;               // 歌曲配图
	optional uint32 music_id = 5;              // 音乐ID
	optional string album_name = 6;			   // 专辑 
	optional uint32 music_type = 7;				//歌曲类型 0原唱、1伴奏
}

message AddConfigMusicReq
{
	required ConfigMusicInfo config_music = 1;
}
message AddConfigMusicResp
{
	required uint32 music_id = 1;              // 音乐ID 
}
message DelConfigMusicReq
{
	repeated uint32 musicid_list = 1;   
}
message DelConfigMusicResp
{
}

message GetConfigMusicListReq
{}
message GetConfigMusicListResp
{
	repeated ConfigMusicInfo config_list = 1;      // 后台配置的歌曲列表
}

message GetChannelCreateTsReq
{
	required uint32 channel_id = 1;
}

message GetChannelCreateTsResp
{
	required uint32 create_ts = 1;
}

// 记录房间礼物信息
message RecordSendGiftEventReq
{
	required uint32 channel_id = 1;
	required uint32 send_uid = 2;
	required uint32 recv_uid = 3;
	required uint32 gift_id = 4;
	required uint32 gift_cnt = 5;
	required uint32 gift_unit_cost = 6; //单价
	optional string order_id = 7;        // 订单ID 用于去重
	optional uint32 gift_price_type = 8; //价格类型 1 红钻 2 T豆
	optional uint32 channel_type = 9; 
}
message RecordSendGiftEventResp
{
}

// 获取房间消费土豪榜// 接口已经废弃
message MemberConsumeInfo 
{
	required uint32 uid = 1;
	required uint32 consume_reddiamond_cnt = 2;
}
// 接口已经废弃
message GetConsumeTopNReq 
{
	required uint32 channel_id = 1;
	required uint32 begin_idx = 2;
	required uint32 limit  = 3;
}
// 接口已经废弃
message GetConsumeTopNResp 
{
	repeated MemberConsumeInfo consume_list = 1;
	optional uint32 total_cnt  = 2;
}

// 接口已经废弃
message GetUserAccumulateConsumeReq {
    required uint32 channel_id = 1;
    required uint32 uid = 2;
} 
// 接口已经废弃
message GetUserAccumulateConsumeResp {
    optional uint32 amount = 1;
}

// 获取房间送礼物的统计信息
message ChannelGiftStat
{
	required uint32 gift_id = 1;
	required uint32 gift_cnt = 2;
	required uint32 gift_reddiamond = 3;
}

//接口废弃，改用ChangeChannelViewID接口
// 更新房间的displayID
message ChangeDisplayIDReq
{
	required uint32 channel_id = 1;
	required uint32 new_display_id = 2;	
	required uint32 magic_id = 3;	
}

message ChangeDisplayIDResp
{
}

// 获取房间最近访客列表
message ChannelHistory 
{
	required uint32 uid = 1;
	required uint32 last_enter_ts = 2;
}

message GetChannelHistoryListReq 
{
	required uint32 channel_id = 1;
	required uint32 begin_idx = 2;
	required uint32 limit  = 3;
}

message GetChannelHistoryListResp 
{
	repeated ChannelHistory enter_list = 1;
	required uint32 all_history_size = 2;
}

// 获取房间统计信息(包括房间最大在线人数)
message GetChannelStatReq
{
	required uint32 channel_id = 1;
}

message GetChannelStatResp
{
	required uint32 max_online_cnt = 1;
	required uint32 max_online_time = 2;
}	

// 获取房间管理员列表
message ChannelAdmin
{
	required uint32 uid = 1;
	required uint32 admin_role = 2;
}

message GetChannelAdminReq
{
	required uint32 channel_id = 1;
	optional uint32 opt_uid = 2;	// 如果该字段有值 就只获取该UID的管理员信息 不是管理员返回结果为空
}
message GetChannelAdminResp
{
	repeated ChannelAdmin admin_list = 1;
}	

message AddChannelAdminReq
{
	required uint32 channel_id = 1;
	required uint32 uid = 2;
    required uint32 role = 3; //ChannelAdminRole
	optional uint32 max_count = 4;	// 业务控制管理员数量上限
}
message AddChannelAdminResp
{
}
message RemoveChannelAdminReq
{
	required uint32 channel_id = 1;
	required uint32 uid = 2;
}
message RemoveChannelAdminResp
{
}

// 设置麦位状态 比如打开/关闭/禁言 麦位 (可以取代之前的麦位开启和麦位关闭命令)
// 接口已经废弃
message SetChannelMicSpaceStatusReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
	required MicrSpaceInfo mic_info = 3;
}
// 接口已经废弃
message SetChannelMicSpaceStatusResp
{
	repeated MicrSpaceInfo all_mic_list = 1; // 当前全量麦位列表信息
	optional uint32 kicked_uid = 2;	     // 如果麦位上有人 且 请求锁麦 那么原来麦上的用户会被踢下麦
	optional uint64 server_time_ms  = 3;      // 64bit 毫秒级 服务器时间
}  

// 设置房间管理员
//message OperChannelAdminReq 
//{
//	required uint32 channel_id = 1;
//	required uint32 target_uid = 2;
//	required bool is_del = 3;
//	optional uint32	admin_role    	= 5; // 管理员类型 see ga::EChannelAdminRoleType
//}

//message OperChannelAdminResp
//{
//}

// 换自己的麦的位置
// 接口已经废弃
message ChangeMicrophoneReq 
{
	required uint32 op_uid = 1;
	required uint32	channel_id		= 2;
	required MicrSpaceInfo to_mic_info = 3; // 目标麦位
}
// 接口已经废弃
message ChangeMicrophoneResp
{
	required MicrSpaceInfo from_mic_info  = 1; // 如果换成功 源麦位的状态信息
	required MicrSpaceInfo to_mic_info  = 2;   // 如果换成功 目标麦位的状态信息
	optional uint64 server_time_ms  = 3;       // 64bit 毫秒级 服务器时间
	repeated MicrSpaceInfo all_mic_list = 4;   // 当前全量麦位列表信息
	optional uint32 mic_mode = 5;              // 当前麦模式
}

// 获取房间主题详情
message GetChannelTopicDetailReq
{
	required uint32 channel_id = 1;
}
	
message GetChannelTopicDetailResp
{
	required string topic_detail = 1;
}

// 获取房间欢迎语
message GetChannelWelcomeMsgReq
{
	required uint32 channel_id = 1;
}
	
message GetChannelWelcomeMsgResp
{
	required string welcome_msg = 1;
}

// 对所有空麦位锁麦
// 接口已经废弃
message DisableAllEmptyMicrSpaceReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
}

message DisableAllEmptyMicrSpaceResp
{
	repeated uint32 disable_micid_list = 1;   // 被关闭的麦位ID
	repeated MicrSpaceInfo all_mic_list = 2;  // 当前全量麦位列表信息
	required uint32 mic_mode  = 3;            // 麦模式
	required uint64 server_time_ms  = 4;      // 64bit 毫秒级 服务器时间
}	



// 开播// 接口已经废弃
message StartLiveReq
{
	required uint32 channel_id = 1;
}

message StartLiveResp
{
}

// 结束直播 // 接口已经废弃
message FinishLiveReq
{
	required uint32 channel_id = 1;
	optional uint32 op_uid = 2;
}
// 接口已经废弃
message FinishLiveResp
{
	required uint32 user_pv = 1; 
	required uint32 user_uv = 2;
	required uint32 tcoin_gift_cnt = 3;  
	required uint32 tcoin_gift_cost = 4;  
	required uint32 time_second = 5; 
}

// 检查是否在直播中// 接口已经废弃
message CheckIsLiveStartingReq
{
	required uint32 channel_id = 1;
}
// 接口已经废弃
message CheckIsLiveStartingResp
{
	required uint32 start_ts = 1;
}
// 接口已经废弃
message EnterLiveChannelReq 
{
	required uint32 channel_id = 1;
}
// 接口已经废弃
message EnterLiveChannelResp 
{
}
// 接口已经废弃
message QuitLiveChannelReq {
	required uint32 channel_id = 1;
    optional bool finish = 2;
}
// 接口已经废弃
message QuitLiveChannelResp {
}

// 申请连麦// 接口已经废弃
message LiveConnectMicApplyReq
{
	required uint32 channel_id = 1;
	required uint32 uid = 2;
	required bool is_cancel = 3;
}
// 接口已经废弃
message LiveConnectMicApplyResp
{
	required uint32 ramin_apply_cnt = 1;
}

// 回应连麦// 接口已经废弃
message LiveConnectMicHandleReq
{
	required uint32 channel_id = 1;
	repeated uint32 apply_uid_list = 2;
	required uint32 op_uid = 3;
	required bool is_allow = 4;
}
// 接口已经废弃
message LiveConnectMicHandleResp
{
	optional uint64 server_time_ms  = 1;       // 64bit 毫秒级 服务器时间
	repeated uint32 success_uid_list = 2;
	repeated MicrSpaceInfo all_mic_list = 3;   // 只有是允许连麦的情况下 才会返回当前全量麦位列表信息
	required uint32 ramin_apply_cnt = 4;       // 当前剩余的申请连麦的人数
}

// 获取连麦申请人列表// 接口已经废弃
message GetLiveConnectMicApplyUserListReq
{
	required uint32 channel_id = 1;
	optional uint32 limit_cnt = 2;
}
// 接口已经废弃
message GetLiveConnectMicApplyUserListResp
{
	repeated uint32 uid_list = 1;  
	optional uint32 all_apply_cnt = 2;
}

// 获取直播记录// 接口已经废弃
message GetLiveStatReq {
	required uint32 channel_id = 1;
}
// 接口已经废弃
message GetLiveStatResp {

	required uint32 user_pv = 1; 
	required uint32 user_uv = 2;
	required uint32 tcoin_gift_cnt = 3;  
	required uint32 tcoin_gift_cost = 4;  
	required uint32 start_ts = 5; 
}

message CreateChannelLiteReq {
    optional CreateChannelReq create_req = 1;
}
message CreateChannelLiteResp {
    optional CreateChannelResp create_resp = 1;
}

// 批量获取频道类型
message BatchGetChannelBindTypeReq
{
	repeated uint32 channel_id_list = 1;
}

message BatchGetChannelBindTypeResp
{
	repeated uint32 channel_bind_type_list = 1;
}


message RefixChannelOwnerReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
}

message RefixChannelOwnerResp
{
}

message ChackAndRefixChannelOwnerReq
{
	required uint32 context_id = 1;
}

message ChackAndRefixChannelOwnerResp
{
}

//白名单
message AddChannelToWhiteListReq 
{
	repeated uint32 channel_list = 1;
}
		
message AddChannelToWhiteListResp
{
	
}

message RemoveFromWhiteListReq
{
	repeated uint32 channel_list = 1;
}

message RemoveFromWhiteListResp
{
	
}

message GetChannelWhiteListReq
{
	
}

message GetChannelWhiteListResp
{
	repeated uint32 channel_list = 1;
}


message ChangeChannelTypeReq
{
	required uint32 channel_id = 1;
	required uint32 channel_type = 2;
	required uint32 bind_id = 3;
	required uint32 create_uid = 4;

	optional bool is_need_clean_admin = 5;
	required uint32 magic_id = 6;
}
message ChangeChannelTypeResp
{
}

message ChangeChannelViewIdReq
{
	required uint32 channel_id = 1;
	required string new_channel_view_id = 2;
	required uint32 magic_id = 3;
}
message ChangeChannelViewIdResp
{
}


service Channel {
	option( tlvpickle.Magic ) = 15200;		// 服务监听端口号

	// CreateChannel 接口已经废弃 目前只是作为test tool工具调用保留
	rpc CreateChannel( CreateChannelReq ) returns( CreateChannelResp ){
		option( tlvpickle.CmdID ) = 1;										
        option( tlvpickle.OptString ) = "n:u:x:b:a:h:p:s:";							
        option( tlvpickle.Usage ) = "-n <channel name> -u <uid> -x <channeltype> -b <bindid> -a <appid> -h <haspwd> -p <passwd> -s <starlevel>";
	}
	
	rpc DissolveChannel( DissolveChannelReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 2;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel id> ";
	}
	
	//rpc EnterChannel( EnterChannelReq ) returns( EnterChannelResp ){
	//	option( tlvpickle.CmdID ) = 3;										
	//    option( tlvpickle.OptString ) = "x:u:";						
	//    option( tlvpickle.Usage ) = "-x <channel id> -u <join uid>";
	//}
	
	//rpc QuitChannel( QuitChannelReq ) returns( QuitChannelResp ){
	//	option( tlvpickle.CmdID ) = 4;										
	//    option( tlvpickle.OptString ) = "x:u:";						
	//    option( tlvpickle.Usage ) = "-x <channel id> -u <quit uid>";
	//}
	
	// 过期接口 废弃 改为使用channelmic服务提供的接口
	//rpc HoldMicrSpace( HoldMicrSpaceReq ) returns( HoldMicrSpaceResp ){
	//    option( tlvpickle.CmdID ) = 5;										
	//    option( tlvpickle.OptString ) = "x:u:";						
	//    option( tlvpickle.Usage ) = "-x <channel id> -u <uid>";
	//}
	
	// 过期接口 废弃 改为使用channelmic服务提供的接口
	//rpc ReleaseMicrSpace( ReleaseMicrSpaceReq ) returns( ReleaseMicrSpaceResp){
	//    option( tlvpickle.CmdID ) = 6;										
	//    option( tlvpickle.OptString ) = "x:u:";						
	//    option( tlvpickle.Usage ) = "-x <channel id> -u <uid>";
	//}
	
	// 获取房间主题的详情
	rpc GetChannelTopicDetail( GetChannelTopicDetailReq ) returns( GetChannelTopicDetailResp ){
		option( tlvpickle.CmdID ) = 7;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel id> ";
	}

	// 废弃 改用 UnmuteChannelMember
	rpc UnsetChannelMute( UnsetChannelMuteReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 8;										
        option( tlvpickle.OptString ) = "x:u:";						
        option( tlvpickle.Usage ) = "-x <channel id> -u <uid>";
	}

	// 过期接口 改用GetChannelSimpleInfo
	rpc GetChannelDetailInfo( GetChannelDetailInfoReq ) returns( GetChannelDetailInfoResp ){
		option( tlvpickle.CmdID ) = 9;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel id>";
	}

	// (废弃过期)
	//rpc GetChannelMemberSize( GetChannelMemberSizeReq ) returns( GetChannelMemberSizeResp ){
	//    option( tlvpickle.CmdID ) = 10;										
	//    option( tlvpickle.OptString ) = "x:";						
	//    option( tlvpickle.Usage ) = "-x <channel id>";
	//}
	
//	rpc GetChannelMemberList( GetChannelMemberListReq ) returns( GetChannelMemberListResp ){
//		option( tlvpickle.CmdID ) = 11;										
//        option( tlvpickle.OptString ) = "x:b:l:";						
//        option( tlvpickle.Usage ) = "-x <channel id> -b <begin idx> -l <limit count>";
//	}
	
	rpc GetChannelMuteList( GetChannelMuteListReq ) returns( GetChannelMuteListResp ){
		option( tlvpickle.CmdID ) = 12;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel id>";
	}

	// 过期接口 废弃 改为使用channelmic服务提供的接口
	//rpc GetMicrList( GetMicrListReq ) returns( GetMicrListResp ){
	//    option( tlvpickle.CmdID ) = 13;										
	//    option( tlvpickle.OptString ) = "x:";						
	//    option( tlvpickle.Usage ) = "-x <channel id>";
	//}

	// 过期接口 废弃 查询公会下的房间列表 改有channelguild服务提供
	rpc GetChannelListByBindId( GetChannelListByBindIdReq ) returns( GetChannelListByBindIdResp ){
		option( tlvpickle.CmdID ) = 14;										
        option( tlvpickle.OptString ) = "n:x:t:";						
        option( tlvpickle.Usage ) = "-n <max_getsize> -x <bindid> -t <bindtype>";
	}
	
	rpc GetChannelBindInfo( GetChannelBindInfoReq ) returns( GetChannelBindInfoResp ){
		option( tlvpickle.CmdID ) = 15;										
        option( tlvpickle.OptString ) = "u:x:";						
        option( tlvpickle.Usage ) = "-u <uid> -x <channelID>";
	}
	
	rpc ModifyChannel( ModifyChannelReq ) returns( ModifyChannelResp ){
		option( tlvpickle.CmdID ) = 16;										
        option( tlvpickle.OptString ) = "x:n:h:p:m:";						
        option( tlvpickle.Usage ) = "-x <channelID> -n <channel name> -h <hasPwd> -p <passwd> -m <img md5>";
	}
	
	//rpc GetUserChannelID( GetUserChannelIDReq ) returns( GetUserChannelIDResp ){
	//	option( tlvpickle.CmdID ) = 17;										
	//    option( tlvpickle.OptString ) = "u:";						
	//    option( tlvpickle.Usage ) = "-u <uid>";
	//}

	// 早已废弃
	//rpc UserHeartbeat( UserHeartbeatReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
	//	option( tlvpickle.CmdID ) = 18;										
	//    option( tlvpickle.OptString ) = "u:x:";						
	//    option( tlvpickle.Usage ) = "-u <uid> -x <channelID>";
	//}
	
	// 接口废弃 已经不再支持
	// 检查并从指定的绑定上频道的麦列表上剔除某人
	// 比如从公会下所有频道踢某人下麦(当用户被撤销管理员时)
	rpc CheckAndKickChannelMicrByBindId( CheckAndKickChannelMicrByBindIdReq ) returns( CheckAndKickChannelMicrByBindIdResp ){
		option( tlvpickle.CmdID ) = 19;										
        option( tlvpickle.OptString ) = "u:x:";						
        option( tlvpickle.Usage ) = "-u <target uid> -x <guild_id>";
	}
	
	//废弃, 2018.09.28
	//rpc DismissAllBindedChannel( DismissAllBindedChannelReq ) returns( DismissAllBindedChannelResp ){
	//    option( tlvpickle.CmdID ) = 20;										
	//    option( tlvpickle.OptString ) = "u:x:";						
	//    option( tlvpickle.Usage ) = "-u <uid> -x <guild_id>";
	//}
	
	rpc CheckUserIsMute( CheckUserIsMuteReq ) returns( CheckUserIsMuteResp ){
		option( tlvpickle.CmdID ) = 21;										
        option( tlvpickle.OptString ) = "u:x:";						
        option( tlvpickle.Usage ) = "-u <uid> -x <channel id>";
	}
	
	// 将用户出公会下面的所在频道 比如用户移出公会时被调用
	// 废弃
	rpc KickUserOutOfGuildChannel( KickUserOutOfGuildChannelReq ) returns( KickUserOutOfGuildChannelResp ){
		option( tlvpickle.CmdID ) = 22;										
        option( tlvpickle.OptString ) = "u:x:";						
        option( tlvpickle.Usage ) = "-u <target uid> -x <guild id>";
	}
	
	// 关闭一个麦位入口
	// 过期接口 废弃
	//rpc DisableChannelMicEntry( DisableChannelMicEntryReq ) returns( DisableChannelMicEntryResp ){
	//    option( tlvpickle.CmdID ) = 23;										
	//    option( tlvpickle.OptString ) = "u:x:";						
	//    option( tlvpickle.Usage ) = "-u <uid> -x <channel id>";
	//}
	
	// 开启一个麦位入口
	// 过期接口 废弃
	//rpc EnableChannelMicEntry( EnableChannelMicEntryReq ) returns( EnableChannelMicEntryResp ){
	//    option( tlvpickle.CmdID ) = 24;										
	//    option( tlvpickle.OptString ) = "u:x:";						
	//    option( tlvpickle.Usage ) = "-u <uid> -x <channel id>";
	//}
	
	// 踢人下麦
	// 过期接口 废弃
	//rpc KickoutChannelMic( KickoutChannelMicReq ) returns( KickoutChannelMicResp  ){
	//    option( tlvpickle.CmdID ) = 25;										
	//    option( tlvpickle.OptString ) = "x:u:";						
	//    option( tlvpickle.Usage ) = "-x <channel id> -u <target uid> ";
	//}
	
	// 踢人出频道   
	// 过期接口 废弃
	rpc KickoutChannelMember( KickoutChannelMemberReq ) returns( KickoutChannelMemberResp){
		option( tlvpickle.CmdID ) = 26;										
        option( tlvpickle.OptString ) = "x:u:";						
        option( tlvpickle.Usage ) = "-x <channel id> -u <target uid> ";
	}
	
	// 获取临时房的 分配状态
	rpc CheckTmpChannelIsAlloced( CheckTmpChannelIsAllocedReq ) returns( CheckTmpChannelIsAllocedResp  ){
		option( tlvpickle.CmdID ) = 27;										
        option( tlvpickle.OptString ) = "u:x:";						
        option( tlvpickle.Usage ) = "-u <target uid> -x <guild role> ";
	}
	
	// 禁言(如果有上麦的话 会被踢下麦)
	// 废弃 改用 MuteChannelMember接口
	rpc SetChannelMuteAndKickMic( SetChannelMuteAndKickMicReq ) returns( SetChannelMuteAndKickMicResp ){
		option( tlvpickle.CmdID ) = 28;										
        option( tlvpickle.OptString ) = "x:u:";						
        option( tlvpickle.Usage ) = "-x <channel id> -u <target uid>";
	}
	
	// 批量获取用户当前房间ID
	//rpc BatchGetUserCurrChannelID( BatchGetUserCurrChannelIDReq ) returns( BatchGetUserCurrChannelIDResp ){
	//	option( tlvpickle.CmdID ) = 29;										
	//    option( tlvpickle.OptString ) = "u:";						
	//    option( tlvpickle.Usage ) = "-u <uid>";
	//}
	//
	// 根据displayId搜索channel
	rpc GetChannelByDisplayId( GetChannelByDisplayIdReq ) returns( GetChannelByDisplayIdResp ){
		option( tlvpickle.CmdID ) = 30;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <displayID>";
	}

	// 根据uid获取权限列表
	rpc GetUserChannelRoleList( GetUserChannelRoleListReq ) returns( GetUserChannelRoleListResp ){
		option( tlvpickle.CmdID ) = 31;										
		option( tlvpickle.OptString ) = "u:r:";						
		option( tlvpickle.Usage ) = "-u <uid> -r <role>";
	}

	// 改变mic模式
	// 过期接口 废弃 改为使用channelmic服务提供的接口
	//rpc SetChannelMicModel( SetChannelMicModelReq ) returns( SetChannelMicModelResp ){
	//    option( tlvpickle.CmdID ) = 32;										
	//    option( tlvpickle.OptString ) = "u:x:m:h:d:";						
	//    option( tlvpickle.Usage ) = "-u <uid> -x <channelID> -m <micModel>";
	//}

	// 通过displayId创建频道
	rpc CreateChannelByDisplayID( CreateChannelByDisplayIDReq ) returns( CreateChannelByDisplayIDResp ){
		option( tlvpickle.CmdID ) = 33;										
        option( tlvpickle.OptString ) = "u:d:b:t:p:a:";							
        option( tlvpickle.Usage ) = "-u <uid> -d <displayId> -b <bindType> -t <bindID> -p <passwd> -a <appid>";
	}
	
	// 根据channelID列表批量获取房间信息
	// 过期接口
	rpc GetChannelDetailInfoBatch( GetChannelDetailInfoBatchReq ) returns( GetChannelDetailInfoBatchResp ){
		option( tlvpickle.CmdID ) = 34;
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	}
	
	// 房间在线系统 通知用户超时下线
	//rpc NotifyUserHeartbeatExpire( NotifyUserHeartbeatExpireReq ) returns( NotifyUserHeartbeatExpireResp ){
	//	option( tlvpickle.CmdID ) = 35;
	//    option( tlvpickle.OptString ) = "u:x:";							
	//    option( tlvpickle.Usage ) = "-u <uid> -x <channelID> ";
	//}
	
	// 分配临时房间
	rpc AllocTempChannel( AllocTempChannelReq ) returns( AllocTempChannelResp ){
		option( tlvpickle.CmdID ) = 36;
        option( tlvpickle.OptString ) = "t:";							
        option( tlvpickle.Usage ) = "-t <channel type> ";
	}
	
	// 获取房间的欢迎语
	rpc GetChannelWelcomeMsg(  GetChannelWelcomeMsgReq) returns( GetChannelWelcomeMsgResp ){
		option( tlvpickle.CmdID ) = 37;
        option( tlvpickle.OptString ) = "x:";							
        option( tlvpickle.Usage ) = "-x <channelID>";
	}
	
	// 关闭房间内所有的空麦位
	// 过期接口 废弃 改为使用channelmic服务提供的接口
	//rpc DisableAllEmptyMicrSpace( DisableAllEmptyMicrSpaceReq ) returns( DisableAllEmptyMicrSpaceResp ){
	//    option( tlvpickle.CmdID ) = 38;
	//    option( tlvpickle.OptString ) = "x:";							
	//    option( tlvpickle.Usage ) = "-x <channelID>";
	//}
	
	// 添加一首配置歌曲
	rpc AddConfigMusic( AddConfigMusicReq ) returns( AddConfigMusicResp ){
		option( tlvpickle.CmdID ) = 39;
        option( tlvpickle.OptString ) = "m:x:d:";							
        option( tlvpickle.Usage ) = "-m <music name> -x <music img url> -d <music download url>";
	}
	
	// 删除一首配置歌曲
	rpc DelConfigMusic( DelConfigMusicReq ) returns( DelConfigMusicResp ){
		option( tlvpickle.CmdID ) = 40;
        option( tlvpickle.OptString ) = "m:";							
        option( tlvpickle.Usage ) = "-m <music id> ";
	}
	
	// 获取配置歌曲列表
	rpc GetConfigMusicList( GetConfigMusicListReq ) returns( GetConfigMusicListResp ){
		option( tlvpickle.CmdID ) = 41;
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	}
	
	// 获取频道创建时间
	rpc GetChannelCreateTs( GetChannelCreateTsReq ) returns( GetChannelCreateTsResp ){
		option( tlvpickle.CmdID ) = 42;
        option( tlvpickle.OptString ) = "x:";							
        option( tlvpickle.Usage ) = "-x <channelID>";
	}
	
	// 记录房间礼物信息
	rpc RecordSendGiftEvent( RecordSendGiftEventReq ) returns( RecordSendGiftEventResp ){
		option( tlvpickle.CmdID ) = 43;
        option( tlvpickle.OptString ) = "u:r:x:g:p:";							
        option( tlvpickle.Usage ) = "-u <send uid> -r <recv uid> -x <channelID> -g <gift id> -p <gift value>";
	}
	
	// 获取房间消费土豪榜 // 接口已经废弃
	rpc GetConsumeTopN( GetConsumeTopNReq ) returns( GetConsumeTopNResp ){
		option( tlvpickle.CmdID ) = 44;
        option( tlvpickle.OptString ) = "x:b:l:";							
        option( tlvpickle.Usage ) = "-x <channelID> -b <begin> -l <limit >";
	}
	
	// 更新房间的displayID  接口废弃，改用ChangeChannelViewID接口
	rpc ChangeDisplayID( ChangeDisplayIDReq ) returns( ChangeDisplayIDResp ){
		option( tlvpickle.CmdID ) = 45;
        option( tlvpickle.OptString ) = "x:d:m:";							
        option( tlvpickle.Usage ) = "-x <channelID> -d <new displayID> -m <magic_id> ";
	}

	// 获取房间最近访客列表 （接口废弃）
	rpc GetChannelHistoryList( GetChannelHistoryListReq ) returns( GetChannelHistoryListResp ){
		option( tlvpickle.CmdID ) = 46;
        option( tlvpickle.OptString ) = "x:";							
        option( tlvpickle.Usage ) = "-x <channelID>";
	}
	
	// 获取房间统计信息(包括房间最大在线人数)（接口废弃）
	rpc GetChannelStat( GetChannelStatReq ) returns( GetChannelStatResp ){
		option( tlvpickle.CmdID ) = 47;
        option( tlvpickle.OptString ) = "x:";							
        option( tlvpickle.Usage ) = "-x <channelID>";
	}
	
	// 获取房间管理员列表
	rpc GetChannelAdmin( GetChannelAdminReq ) returns( GetChannelAdminResp ){
		option( tlvpickle.CmdID ) = 48;
        option( tlvpickle.OptString ) = "x:";							
        option( tlvpickle.Usage ) = "-x <channelID> ";
	}
	
	// 设置麦位状态 比如打开/关闭/禁言 麦位 (可以取代之前的麦位开启和麦位关闭命令)
	// 过期接口 废弃 改为使用channelmic服务提供的接口
	//rpc SetChannelMicSpaceStatus( SetChannelMicSpaceStatusReq ) returns( SetChannelMicSpaceStatusResp ){
	//    option( tlvpickle.CmdID ) = 49;
	//    option( tlvpickle.OptString ) = "x:u:m:s:";							
	//    option( tlvpickle.Usage ) = "-x <channelID> -u <target uid> -m <micr id> -s <status>";
	//}
	
	// 设置房间管理员
	//rpc OperChannelAdmin( OperChannelAdminReq ) returns( OperChannelAdminResp ){
	//	option( tlvpickle.CmdID ) = 50;
	//    option( tlvpickle.OptString ) = "x:u:t:r:";							
	//    option( tlvpickle.Usage ) = "-x <channelID> -u <target uid> -t < if 1 is del > -r <admin_role>";
	//}
	
	// 换麦位
	// 过期接口 废弃 改为使用channelmic服务提供的接口
	rpc ChangeMicrophone( ChangeMicrophoneReq ) returns( ChangeMicrophoneResp ){
		option( tlvpickle.CmdID ) = 51;
        option( tlvpickle.OptString ) = "x:u:t:";							
        option( tlvpickle.Usage ) = "-x <channelID> -u <target uid> -t < target mic ID >";
	}
	
	// 批量判断房间是否属于临时分配房
	rpc BatchCheckIsTempAllocChannel( BatchCheckIsTempAllocChannelReq ) returns( BatchCheckIsTempAllocChannelResp ){
		option( tlvpickle.CmdID ) = 52;
        option( tlvpickle.OptString ) = "x:";							
        option( tlvpickle.Usage ) = "-x <channelID> ";
	}
	// 获取房间简单信息
	rpc GetChannelSimpleInfo( GetChannelSimpleInfoReq ) returns( GetChannelSimpleInfoResp ){
		option( tlvpickle.CmdID ) = 53;
        option( tlvpickle.OptString ) = "x:d:";							
        option( tlvpickle.Usage ) = "-x <channelID> -d <optional displayID>";
	}
	
	// 批量获取房间简单信息
	rpc BatchGetChannelSimpleInfo( BatchGetChannelSimpleInfoReq ) returns( BatchGetChannelSimpleInfoResp ){
		option( tlvpickle.CmdID ) = 54;
        option( tlvpickle.OptString ) = "x:";							
        option( tlvpickle.Usage ) = "-x <channelID> ";
	}
	
	// 连麦请求
	// 过期接口 改为使用channellive服务提供的接口
	rpc LiveConnectMicApply( LiveConnectMicApplyReq ) returns( LiveConnectMicApplyResp ){
		option( tlvpickle.CmdID ) = 55;
        option( tlvpickle.OptString ) = "x:u:";							
        option( tlvpickle.Usage ) = "-x <channelID> -u <uid>";
	}
	
	// 回应连麦
	// 过期接口 改为使用channellive服务提供的接口
	rpc LiveConnectMicHandle( LiveConnectMicHandleReq ) returns( LiveConnectMicHandleResp ){
		option( tlvpickle.CmdID ) = 56;
        option( tlvpickle.OptString ) = "x:u:t:";							
        option( tlvpickle.Usage ) = "-x <channelID> -u <uid> -t <is allow>";
	}
	
	// 获取连麦申请人列表
	// 过期接口 改为使用channellive服务提供的接口
	rpc GetLiveConnectMicApplyUserList( GetLiveConnectMicApplyUserListReq ) returns( GetLiveConnectMicApplyUserListResp ){
		option( tlvpickle.CmdID ) = 57;
        option( tlvpickle.OptString ) = "x:";							
        option( tlvpickle.Usage ) = "-x <channelID> ";
	}
	
	
	// 结束直播 
	// 过期接口 改为使用channellive服务提供的接口
	rpc FinishLive( FinishLiveReq ) returns( FinishLiveResp ){
		option( tlvpickle.CmdID ) = 58;
        option( tlvpickle.OptString ) = "x:u:";							
        option( tlvpickle.Usage ) = "-x <channelID> -u <uid>";
	}
	
	// 检查是否在直播中
	// 过期接口 改为使用channellive服务提供的接口
	rpc CheckIsLiveStarting( CheckIsLiveStartingReq ) returns( CheckIsLiveStartingResp ){
		option( tlvpickle.CmdID ) = 59;
        option( tlvpickle.OptString ) = "x:";							
        option( tlvpickle.Usage ) = "-x <channelID> ";
	}
	
	// 标记直播开始 
	// 过期接口 改为使用channellive服务提供的接口
	rpc StartLive( StartLiveReq ) returns( StartLiveResp ){
		option( tlvpickle.CmdID ) = 60;
        option( tlvpickle.OptString ) = "x:";							
        option( tlvpickle.Usage ) = "-x <channelID>";
	}

	//检测用户是否被踢
	rpc CheckUserKickoutFromChannel( CheckUserKickoutFromChannelReq ) returns ( CheckUserKickoutFromChannelResp ){
		option( tlvpickle.CmdID ) = 61;										
        option( tlvpickle.OptString ) = "x:u:";						
        option( tlvpickle.Usage ) = "-x <channel id> -u <target uid> ";
	}
	//获取用户房间内累计消费 // 接口已经废弃
	rpc GetUserAccumulateConsume( GetUserAccumulateConsumeReq ) returns ( GetUserAccumulateConsumeResp ){
		option( tlvpickle.CmdID ) = 62;
		option( tlvpickle.OptString ) = "x:u:";							
        	option( tlvpickle.Usage ) = "-x <channelID> -u <uid>";
    	}

	// 过期接口 改为使用channellive服务提供的接口
	rpc EnterLiveChannel(EnterLiveChannelReq ) returns (EnterLiveChannelResp ) {
		option( tlvpickle.CmdID ) = 63;
        	option( tlvpickle.OptString ) = "x:u:";							
        	option( tlvpickle.Usage ) = "-x <channelID> -u <uid>";
	}
	// 过期接口 改为使用channellive服务提供的接口
	rpc QuitLiveChannel( QuitLiveChannelReq ) returns ( QuitLiveChannelResp ) {
		option( tlvpickle.CmdID ) = 64;
        	option( tlvpickle.OptString ) = "x:u:";							
        	option( tlvpickle.Usage ) = "-x <channelID> -u <uid>";
	}
	// 释放临时房间
	rpc ReleaseTempChannel( ReleaseTempChannelReq ) returns( ReleaseTempChannelResp ){
		option( tlvpickle.CmdID ) = 65;
        	option( tlvpickle.OptString ) = "x:";							
        	option( tlvpickle.Usage ) = "-x <channel_id> ";
	}
	rpc AddChannelAdmin( AddChannelAdminReq ) returns( AddChannelAdminResp ){
		option( tlvpickle.CmdID ) = 66;
        	option( tlvpickle.OptString ) = "x:u:r:";
        	option( tlvpickle.Usage ) = "-x <channel_id> -u <uid> -r <role> ";
	}
	rpc RemoveChannelAdmin( RemoveChannelAdminReq ) returns( RemoveChannelAdminResp ){
		option( tlvpickle.CmdID ) = 67;
        	option( tlvpickle.OptString ) = "x:u:";							
        	option( tlvpickle.Usage ) = "-x <channel_id> -u <uid> ";
	}
	
	
	// 新建频道简化版: 只创建频道，不进入频道
	rpc CreateChannelLite( CreateChannelLiteReq ) returns ( CreateChannelLiteResp ) {
		option( tlvpickle.CmdID ) = 100;
        option( tlvpickle.OptString ) = "x:u:";
        option( tlvpickle.Usage ) = "-x <channel_id> -u <uid>";
	}

	rpc BatchGetChannelBindType( BatchGetChannelBindTypeReq ) returns ( BatchGetChannelBindTypeResp ) {
		option( tlvpickle.CmdID ) = 102;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	// 新的简单禁言接口
	rpc MuteChannelMember( MuteChannelMemberReq ) returns ( MuteChannelMemberResp ){
		option( tlvpickle.CmdID ) = 103;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	rpc UnmuteChannelMember( UnmuteChannelMemberReq ) returns ( UnmuteChannelMemberResp ) {
		option( tlvpickle.CmdID ) = 104;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	rpc KickoutChannelMemberLite( KickoutChannelMemberLiteReq ) returns ( KickoutChannelMemberLiteResp ) {
		option( tlvpickle.CmdID ) = 105;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// ============= bugfix 系列接口 ============ 
	rpc ChackAndRefixChannelOwner( ChackAndRefixChannelOwnerReq ) returns( ChackAndRefixChannelOwnerResp ){
		option( tlvpickle.CmdID ) = 200;
        option( tlvpickle.OptString ) = "t:";							
        option( tlvpickle.Usage ) = "-t <context id>";
	}
	
	rpc RefixChannelOwner( RefixChannelOwnerReq ) returns( RefixChannelOwnerResp ){
		option( tlvpickle.CmdID ) = 201;
        option( tlvpickle.OptString ) = "u:x:";							
        option( tlvpickle.Usage ) = "-u <uid> -x <channel id>";
	}

	// ============ 白名单 系列接口 =======================
	rpc AddChannelToWhiteList ( AddChannelToWhiteListReq ) returns (AddChannelToWhiteListResp){
		option( tlvpickle.CmdID ) = 202;
		option( tlvpickle.OptString ) = "u:k:";						
        option( tlvpickle.Usage ) = "-u <uid> -k <channel ids>";
	}

	rpc RemoveFromWhiteList ( RemoveFromWhiteListReq ) returns (RemoveFromWhiteListResp){
		option( tlvpickle.CmdID ) = 203;
		option( tlvpickle.OptString ) = "u:k:";						
        option( tlvpickle.Usage ) = "-u <uid> -k <channel ids>";
	}

	rpc GetChannelWhiteList ( GetChannelWhiteListReq ) returns (GetChannelWhiteListResp){
		option( tlvpickle.CmdID ) = 204;
		option( tlvpickle.OptString ) = "u:";						
        option( tlvpickle.Usage ) = "-u <uid> ";
	}

	// ============ 修改房间的bind类型  =======================
	rpc ChangeChannelType ( ChangeChannelTypeReq ) returns (ChangeChannelTypeResp){
		option( tlvpickle.CmdID ) = 210;
		option( tlvpickle.OptString ) = "x:t:i:r:b:m:";						
        option( tlvpickle.Usage ) = "-x <cid> -t <new type> -i <new bindID> -r <new creater uid> -b <is clean admin> -m<magicid>";
	}

	// 更新房间的displayID，允许房间不存在displayid，用于修复异常情况   接口废弃，改用ChangeChannelViewID接口
	rpc ChangeDisplayIDV2( ChangeDisplayIDReq ) returns( ChangeDisplayIDResp ){
		option( tlvpickle.CmdID ) = 211;
		option( tlvpickle.OptString ) = "x:d:m:";
		option( tlvpickle.Usage ) = "-x <channelID> -d <new displayID> -m <magic_id> ";
	}

	//修改房间进房控制类型:密码进房，白名单进房等
	rpc ModifyChannelEnterControlType(ModifyChannelEnterControlTypeReq) returns(ModifyChannelEnterControlTypeResp) {
		option( tlvpickle.CmdID ) = 212;
		option( tlvpickle.OptString ) = "c:e:p:";
		option( tlvpickle.Usage ) = "-c <channelID> -c <control_type> -p <passwd> ";
	}

	// ============ 临时房的批量和监控接口  =======================
	rpc BatchAllocTempChannels( BatchAllocTempChannelReq ) returns( BatchAllocTempChannelResp ) {
		option( tlvpickle.CmdID ) = 220;
		option( tlvpickle.OptString ) = "t:c:";
		option( tlvpickle.Usage ) = "-t <channel type> -c <batch_count>";
	}

	rpc BatchReleaseTempChannels( BatchReleaseTempChannelReq ) returns( BatchReleaseTempChannelResp ){
		option( tlvpickle.CmdID ) = 221;
		option( tlvpickle.OptString ) = "t:k:";
		option( tlvpickle.Usage ) = "-t <channel type> -k <channel ids>";
	}

	rpc QueryTempChannelsNum( QueryTempChannelsNumReq ) returns( QueryTempChannelsNumResp ) {
		option( tlvpickle.CmdID ) = 222;
		option( tlvpickle.OptString ) = "t:";
		option( tlvpickle.Usage ) = "-t <channel type>";
	}

	rpc ChangeChannelViewId(ChangeChannelViewIdReq) returns (ChangeChannelViewIdResp) {
		option( tlvpickle.CmdID ) = 223;
		option( tlvpickle.OptString ) = "c:v:";
		option( tlvpickle.Usage ) = "-c <channel id> -v <channel view id>";
	}
}

