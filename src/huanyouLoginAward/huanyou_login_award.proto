syntax="proto3";

import "common/tlvpickle/skbuiltintype.proto";

package huanyou_login_award;

// 欢游 登录 发T豆奖励


enum EAwardFlag
{
	ENUM_FLAG_NO_RIGHT = 0;                    // 没有资格
    ENUM_FLAG_NO_AWARD = 1;                    // 有资格未兑换
    ENUM_FLAG_ALREADY_AWARD = 2;               // 有资格已兑换
}

// 查询他的状态
message CheckUserAwardReq
{
	uint32 uid = 1;
}

message CheckUserAwardResp
{
	uint32 award_flag = 1;  // EAwardFlag
	uint32 ts = 2;
}


// 设置他有资格
message AddNewAwardUserReq
{
	uint32 uid = 1;
}

message AddNewAwardUserResp
{

}

service huanyou_login_award {
	option( tlvpickle.Magic ) = 15675;

    rpc CheckUserAward ( CheckUserAwardReq ) returns (CheckUserAwardResp){
      option (tlvpickle.CmdID) = 1;
      option (tlvpickle.OptString) = "u:";
      option (tlvpickle.Usage) = "-u <uid>";
    }

    rpc AddNewAwardUser ( AddNewAwardUserReq ) returns (AddNewAwardUserResp){
      option (tlvpickle.CmdID) = 2;
      option (tlvpickle.OptString) = "u:";
      option (tlvpickle.Usage) = "-u <uid>";
    }

}
