syntax="proto2";

// svrkit support
import "common/tlvpickle/skbuiltintype.proto";

package GuildStorage;

// 礼包来源: TT官方 会长录入
//enum EGuildProductSourceType{
//	PRODUCT_SOURCE_TYPE_TT  = 1;         // TT官方
//    PRODUCT_SOURCE_TYPE_GUILDOWNER  = 2; // 会长录入
//    PRODUCT_SOURCE_TYPE_TT_PUSH = 3;     // 官方推送
//}

// 只有卡号 的礼物的兑换信息
//message Product_CodeID {
//    required string code_id = 1;
//}
//
//// 卡号+密码 的礼物的兑换信息
//message Product_CodeID_Pwd {
//    required string code_id 	= 1;
//    required string pwd	= 2;
//}
//

// 商品基础类
message MinGuildProduct{
    //enum ProductStatus {
    //    Shelve = 1; //上架
    //    SoldOut = 2; //卖完
    //    UnShelve = 3; //下架
    //}

    //enum ProductItemType {
    //    AccountAndPassword  = 1;    //帐号密码
    //    Vhoucher            = 2;    //代金券
    //    PrepaidCard         = 3;    //充值卡
    //    Peripheral          = 4;    //周边产品
    //    FirstVoucherCode    = 5;    //首充号
    //    ActivationCode      = 6;    //激活码
    //    Gift                = 7;    //礼包
    //}

    optional uint32 product_id      = 1;   // 商品ID
    required string name            = 2;   // 商品名称
    required uint32 source_type     = 3;   // 来源, see gift2_.proto ga::EGiftPktSourceType
    required uint64 source_id       = 4;   // 来源Id
    required uint32 item_type       = 5;   // item类型, see gift2_.proto ga::EGiftPktItemType
    required string usage           = 6;   // 使用说明
    required string description     = 7;   // 商品描述
    required uint32 status          = 8;   // 商品状态 see guild2_.proto ga::GuildProductStatus
    required uint32 game_id         = 9;   // 绑定的游戏ID
    required string icon_url        = 10;  // 商品图标
    required uint32 guild_id        = 11;  // 公会Id
    optional uint32 exchange_type   = 12;  // 兑换类型
    optional uint32 platform        = 13;  // 平台
}

// 商品
message GuildProduct {
    required MinGuildProduct min_product  = 1;   // 商品基类
    required uint32 currency_type         = 2;   // 消耗代币类型
    required uint32 price                 = 3;   // 商品价格
    required uint32 remain                = 4;   // 上架数量
    required uint32 total                 = 5;   // 仓库数量
    optional uint32 valid_s_date          = 6;   // 过期时间
    optional uint32 valid_e_date          = 7;   // 过期时间
	optional uint32 examine               = 8;   // 领取是否需要审查
    optional uint32 fetch_limit           = 9;   // 限购次数 当前版本默认1
    optional uint32 fetch_times           = 10;  // 一共被领取的次数
	optional uint32 update_ts             = 11;  // 商品更新时间 目前用于插入旧数据时指定更新时间
    optional string extend_info           = 12;  // 商品拓展信息(json)
	optional uint32 day_limit			  = 13; //入会天数限制
}

// 物品信息
message ProductItem {
    required uint32 id          = 1;    //兑换id,内部用
    required uint32 uid         = 2;
    required uint32 guild_id    = 3;
    required uint32 product_id  = 4;

    optional uint32 exchange_type = 5;   // 兑换类型
	optional bytes  exchange_info = 6;	// 物品内容 根据兑换类型ga::EGiftPktExchangeType 判断使用那种兑换数据的结构体  ga::ExchangeInfo_CodeIDPassword / ga::ExchangeInfo_CodeID

    optional uint32 date        = 7;
}

message AddProductReq
{
    required uint32 uid = 1;
    required GuildProduct product = 2; // 忽略status,remain,price,currency_type字段
    repeated bytes  item_binary = 3;   // 物品详细信息 根据兑换类型EGiftPktExchangeType 判断使用那种兑换数据的结构体  ga::ExchangeInfo_CodeIDPassword / ga::ExchangeInfo_CodeID
    optional uint32 payment = 4;       // 花钱买的礼包要填这个，log用
}

message AddProductResp
{
    required uint32 product_id = 1;
}


//查找礼包
message GetProductReq
{
    required uint32 guild_id = 1;
    optional uint32 product_id = 2;
    optional uint32 source_type = 3;    //see giftpkg2_.EGiftPktSourceType
    optional uint32 item_type = 4;   //see giftpkg2_.EGiftPktItemType
    optional uint32 product_status = 5; //see guild2_.GuildProductStatus
    optional uint32 need_unshelve = 6; //是否要下架的物品
    optional uint32 index = 7;
    optional uint32 cnt = 8;
    optional uint32 platform = 9;
}

message GetProductResp
{
    repeated GuildProduct product_lst = 1;
    optional uint32 total = 2;
}

message BatchGetProductByIdReq
{
    required uint32 guild_id = 1;
    repeated uint32 product_id_lst = 2;
    optional uint32 platform = 3;
}

message BatchGetProductByIdResp
{
    repeated GuildProduct product_lst = 1;
}

//搜索
message SearchProductReq
{
    required uint32 guild_id = 1;
    required string key_word = 2;
    optional uint32 index = 3;
    optional uint32 cnt = 4;
    optional uint32 need_unshelve = 5; //是否要下架的物品
    optional uint32 platform = 6;
}

message SearchProductResp
{
    required uint32 total = 1;
    repeated GuildProduct product_lst = 2;
}

//审查相关
message ExamineRecord
{
	required uint32 examine_id = 1; //审查Id
    required uint32 guild_id = 2;
	required uint32 uid = 3;
	required uint32 date = 4;
    required uint32 status = 5;
    required uint32 num = 6;
	required GuildProduct product = 7; //商品快照
    optional uint32 guild_msg_id = 8; //TT助手Id
}

//拉审核列表
message GetExamineRecordLstReq
{
    required uint32 guild_id = 1;
    optional uint32 product_id = 2; //为0跳过这个条件
    optional uint32 to_uid     = 3; //为0跳过这个条件
    optional uint32 examine_id = 4; //为0跳过这个条件
    optional uint32 examine_status = 5; //审核状态0表示未处理1同意2拒绝
}

message GetExamineRecordLstResp
{
	repeated ExamineRecord record_lst = 1;
}

//处理审核
message DealExamineReq
{
    required uint32 guild_id = 1;
	required uint32 examine_id = 2; //审查ID
	required uint32 status = 3; //0:拒绝 1:同意
}

message DealExamineResp
{
    required uint32 to_uid  = 1;
    required GuildProduct product = 2; //申请时的product快照
    optional ProductItem item = 3;//兑换信息, 不一定有，如果是兑换类型为None就没有
    optional uint32 guild_msg_id = 4;//公会助手消息Id
}

//examine写入msg_id
message ModifyExamineApplyReq
{
    required uint32 guild_id =1;
    required uint32 examine_id = 2;
    required uint32 guild_msg_id = 3;
}

message ModifyExamineApplyResp
{
    required uint32 guild_id = 1;
    required uint32 examine_id = 2;
}

//仓库操作记录
message StorageOperRecord
{
    enum CATEGORY_TYPE
    {
        CATEGORY_GUILD_OWNER = 1; //会长或者仓管操作
        CATEGORY_GUILD_MEMBER = 2; //普通成员
    }

    //会员操作
    enum GUILD_MEM_OPER
    {
        OPER_FETCH_P = 1; //领取礼包
        OPER_ALLOT_P = 2; //被分配记录
    }

    required uint32 record_id = 1;
    required uint32 uid = 2;
    required uint32 guild_id = 3;
	required uint32 oper_type = 4; //操作类型 see GuildStorageOperType
	optional uint32 date = 5;
    optional GuildProduct product = 6;
    optional uint32 payment = 7; //支付总额
    optional uint32 to_uid = 8;
    optional string extend_info = 9; //拓展属性,json格式
    optional uint32 category = 10; //记录大类类型
}

message StorageGetOperRecordReq
{
    required uint32 guild_id = 1;
    optional uint32 index  = 2;
    optional uint32 cnt = 3;
}

message StorageGetOperRecordResp
{
    required uint32 total_record = 1;
	repeated StorageOperRecord record_lst = 2;
}

//申请商品
message GainProductReq
{
    required uint32 uid = 1;
    required uint32 guild_id = 2;
    required uint32 product_id = 3;
    //价格核对用
    optional uint32 currency_type = 4;
    optional uint32 price = 5;
//    optional uint32 num = 4;
}

message GainProductResp
{
    required uint32 status = 1;  //0已经到账1要审核
    optional ProductItem item = 2; //兑换信息, 不一定有，如果是兑换类型为None就没有
    optional GuildProduct product = 3;
    optional uint32 examine_id = 4; //需要审核时用到
}

//分配商品
message AllotProductReq
{
    required uint32 to_uid = 1;
    required uint32 guild_id = 2;
    required uint32 product_id = 3;
    required uint32 num = 4;
}

message AllotProductResp
{
    required uint32 to_uid = 1;
    optional GuildProduct product = 2;
    repeated ProductItem item_lst = 3;//不一定有，如果是兑换类型为None就没有
    optional uint32 total = 4; //礼包数
}

//修改商品
message ModifyProductStatusReq
{
    enum  ModifyProductOperType
    {
        ModifyProductOperType_Shelve = 1; //上架
        ModifyProductOperType_UnShelve = 2; //下架
        ModifyProductOperType_Modify = 3; //调整
    }
    required uint32 guild_id       = 1;
    required uint32 product_id     = 2; //商品ID
    required uint32 oper_type      = 3; //操作类型
    optional uint32 num            = 4; //上架数量
    optional uint32 price          = 5; //商品价格
    optional uint32 examine        = 6; //是否需要审核0表示不用
    optional uint32 fetch_limit    = 7; //限购数量
	optional uint32 day_limit	   = 8; //购买者入会时间限制
}

message ModifyProductStatusResp
{
}

message GetProductBySourceIdReq
{
    required uint32 guild_id = 1;
    optional uint32 source_type = 2;
    repeated uint64 source_id_lst = 3; //来源的礼包Id
}

message GetProductBySourceIdResp
{
    repeated GuildProduct product_lst = 1;
}

message GetGameProductReq
{
    required uint32 guild_id = 1;
    optional uint32 game_id  = 2;  //为0时，返回所有游戏的gift
    optional uint32 source_type = 3; //为0时，跳过此筛选条件
    optional uint32 item_type = 4; //为0时，跳过此筛选条件
    optional uint32 need_unshelve = 5; //是否要下架的物品
    optional uint32 platform = 6; 
    repeated uint32 install_game_id_lst = 7; //安装的游戏
}

message GameProduct
{
    required uint32 game_id = 1;
    repeated GuildProduct product_lst = 2;
}

//结果以gameId分类
message GetGameProductResp
{
    repeated GameProduct game_product_lst = 1;
    optional uint32 total = 2;
}

//移除商品
message DeleteProductReq
{
    required uint32 guild_id = 1;
    required uint32 product_id = 2;
    optional uint32 num = 3;
}

message DeleteProductResp
{
}


//领取记录
message GetFetchRecordReq
{
    required uint32 guild_id = 1;
    required uint32 product_id = 2;
    optional uint32 index  = 3;
    optional uint32 cnt    = 4;
}

message UserFetchRecord
{
    required uint32 record_id = 1;
    required uint32 uid = 2;
    required uint32 date = 3;
}

message GetFetchRecordResp
{
    required uint32 guild_id = 1;
    required uint32 product_id = 2;
    required uint32 total = 3;
    repeated UserFetchRecord record_lst = 4;
}

message BatchGetUserFetchRecordReq
{
    required uint32 uid = 1;
    required uint32 guild_id = 2;
    repeated uint32 product_id_lst = 3;
    optional bool only_total = 4; //true表示只要领取次数,不要详细记录
}

message BatchGetUserFetchRecordResp
{
    repeated GetFetchRecordResp record_lst = 1;
}


//礼包统计
message CalculateProductReq
{
    required uint32 guild_id = 1;
    required uint32 source_type = 2; //必填,填0跳过此筛选条件
    optional uint32 item_type = 3; //填0时返回各个类型的礼包数
    optional uint32 need_unshelve = 4; //是否要下架的物品
    optional uint32 platform = 5;
}

message CalculateProductItem
{
    required uint32 source_type = 1;
    required uint32 item_type = 2;
    required uint32 total = 3;
}

message CalculateProductResp
{
    required uint32 guild_id = 1;
    repeated CalculateProductItem item_lst = 2;
}

//通过sourceId获得物品
message GetGuildBySourceIdReq
{
    required uint32 source_id = 1;
    optional uint32 source_type = 2; //来源,填0跳过次条件
    optional uint32 item_status = 3; //物品状态,填0跳过此条件 see GuildProductStatus
}

message GetGuildBySourceIdResp
{
    repeated uint32 guild_id_lst = 1;
}

//获取用户状态
message UserStorageInfo
{
    required uint32 uid = 1;
    required uint32 guild_id = 2;
    required uint32 product_id = 3;
    optional uint32 examine_id = 4;
    optional uint32 fetch_times = 5;
}

message GetUserStorageInfoReq
{
    required uint32 uid = 1;
    required uint32 guild_id = 2;
    required uint32 product_id = 3;
}

message GetUserStorageInfoResp
{
}

//获取商品账号密码
message UserProductItem
{
    required uint32 uid = 1;
    required uint32 guild_id = 2;
    required uint32 product_id = 3;
    optional uint32 exchange_type = 4; //兑换类型
    optional string exchange_info = 5; //商品卡密
    optional uint32 fetch_date = 6; //领取时间
    optional uint32 item_id = 7; //卡的id
}

message GetUserProductItemReq
{
    required uint32 uid = 1;
    required uint32 guild_id = 2;
    required uint32 product_id = 3;
}

message GetUserProductItemResp
{
    repeated UserProductItem product_item_lst = 1;
}

message BatGetGameProductCountReq
{
	repeated uint32 guild_id_list = 1;
}

message GuildProductCount
{
	required uint32 guild_id = 1;
	required uint32 product_count = 2;
}

message BatGetGameProductCountResp
{
	repeated GuildProductCount product_count_list = 1;
}

////////////////
//公会金券相关//
////////////////
message GuildGiftCardInfo
{
    required uint32 ly_ticket_id = 1; //联运ticketId
    required uint32 guild_id = 2;
    required uint32 game_id = 3; 
    required uint32 total_price = 4; //总限额
    optional uint32 used_price = 5; //已经用掉的额度
    optional uint32 expiry_date = 6; //过期时间

    optional uint32 act_type = 7; //活动类型
    optional uint32 act_id = 8; //活动id

    optional uint32 lower_bound = 9; //转换成单张金券的下限
    optional uint32 upper_bound = 10; //

    optional uint32 create_date = 11; //创建时间
    optional uint32 balance = 12; // 代金券余额
    optional uint32 min_recharge = 13; //最低消费额度
    optional uint32 ly_game_id = 14; //联运gameId 
    optional uint32 status = 15; //代金券状态
    optional uint32 repeat = 16; //是否可以重复领取
    optional uint32 platform = 17; //代金券平台
}

//获取公会金券
message GetGuildGiftCardReq
{
    required uint32 guild_id = 1;
    optional uint32 ly_ticket_id = 2; //填0时返回全部
	optional uint32 need_delete = 3; //0不需要，1需要
}

message GetGuildGiftCardResp
{
    repeated GuildGiftCardInfo ticket_lst = 1;
}

//增加公会金券
message AddGuildGiftCardReq
{
    required GuildGiftCardInfo ticket = 1;
}

message AddGuildGiftCardResp
{
}

//公会金券拆分成商品
message SplitGuildGiftCardReq
{
    required uint32 guild_id = 1;
    required uint32 ly_ticket_id = 2;
    required uint32 value = 3; //面值
    optional uint32 game_id = 4; //绑定的游戏

    required uint32 num = 5; //数量
    required uint32 price = 6; //商品销售价格
    required uint32 examine = 7; //是否需要审核
    optional GuildProduct product = 8; //切分出来的商品
    //optional uint32 to_uid = 9; //不为0时，直接分配给用户
}

message SplitGuildGiftCardResp
{
    required GuildProduct product = 1;
}


//代金券异步任务
enum EGiftCardAsyncType
{
    EGiftCardAynsc_GetGiftCard = 1;  //更新礼品卡信息,GetGuildVouncherReq
    EGiftCardAynsc_GrantVoucher = 2; //发放联运代金券
}

//异步发放代金券
message GrantVoucherData
{
    required uint32 uid = 1;
    required uint32 guild_id = 2;
    required uint32 ly_ticket_id = 3;
    required uint32 value = 4; //面值
    required uint32 ly_game_id = 5; //游戏
    required string order_id = 6; // 订单号
    optional string msg = 7; // 描述
}

//礼品卡消费记录
enum EGrantVoucherStatus
{
    EGrantVoucherStatus_None = 1;
    EGrantVoucherStatus_Success = 2;
    EGrantVoucherStatus_Fail = 3;
}


//获取giftCard消费记录
message GetGiftCardLogInfoReq
{
    required uint32 guild_id = 1;
    required uint32 ly_ticket_id = 2;
    optional string order_id = 3; //筛选条件
    optional uint32 status = 4; //see EGrantVoucherStatus
    optional uint32 uid = 5; //领取人
    optional uint32 index = 6;
    optional uint32 cnt = 7;
}

message GetGiftCardLogInfo
{
    required GrantVoucherData order_info = 1;
    required uint32 status = 2;
    optional uint32 create_date = 3;
}

message GetGiftCardLogInfoResp
{
    required uint32 log_total = 1;
    repeated GetGiftCardLogInfo log_lst = 2;
}

//修改giftCardLog
message SetGiftCardLogStatusReq
{
    required string order_id = 1;
    required uint32 guild_id = 2;
    required uint32 status = 3;
    optional string resp_msg = 4;
}

message SetGiftCardLogStatusResp
{
}

//删除代金券
message DeleteGiftCardReq
{
    required uint32 guild_id = 1;
    required uint32 ly_ticket_id = 2;
}

message DeleteGiftCardResp
{
}

/**************公会金券相关end***********/



service GuildStorage {
	option( tlvpickle.Magic ) = 15496;		// 服务监听端口号

    rpc AddProduct( AddProductReq ) returns ( AddProductResp ) {
		option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "u:g:s:m:a:e:n:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guildId> -s <source> -m <status> -a <gameId> -e <examine> -n <name>";	// 测试工具的命令号帮助
    }

    rpc GetProduct( GetProductReq ) returns ( GetProductResp ) {
		option( tlvpickle.CmdID ) = 2;						// 命令号
        option( tlvpickle.OptString ) = "u:g:s:p:t:m:";				// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guildId> -s <source> -p <pid> -t <product_type> -m <status>";	            // 测试工具的命令号帮助
    }

    rpc SearchProduct ( SearchProductReq ) returns ( SearchProductResp ) {
		option( tlvpickle.CmdID ) = 3;						// 命令号
        option( tlvpickle.OptString ) = "u:g:k:i:n:";				// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guildID> -k <key_word> -i <index> -n <cnt>";	            // 测试工具的命令号帮助
    }

    rpc GetExamineRecordLst( GetExamineRecordLstReq ) returns ( GetExamineRecordLstResp ) {
		option( tlvpickle.CmdID ) = 4;							// 命令号
        option( tlvpickle.OptString ) = "u:g:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guildID>";	                        // 测试工具的命令号帮助
    }

    rpc DealExamine ( DealExamineReq ) returns ( DealExamineResp ) {
		option( tlvpickle.CmdID ) = 5;					   // 命令号
        option( tlvpickle.OptString ) = "u:g:e:s:";				   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -e <examineId> -s <status>";	                   // 测试工具的命令号帮助
    }

    rpc StorageGetOperRecord ( StorageGetOperRecordReq ) returns ( StorageGetOperRecordResp ) {
		option( tlvpickle.CmdID ) = 6;			// 命令号
        option( tlvpickle.OptString ) = "u:g:i:n:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -i <index> -n <cnt>";	        // 测试工具的命令号帮助
    }

    rpc ModifyProductStatus ( ModifyProductStatusReq ) returns ( ModifyProductStatusResp ) {
		option( tlvpickle.CmdID ) = 7;			// 命令号
        option( tlvpickle.OptString ) = "u:p:o:m:n:g:e:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -p <pid> -o <oper_type> -m <price> -n <num> -e examine";	        // 测试工具的命令号帮助
    }

    rpc GainProduct ( GainProductReq ) returns ( GainProductResp ) {
		option( tlvpickle.CmdID ) = 8;			// 命令号
        option( tlvpickle.OptString ) = "u:p:g:t:m:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -p <pid> -g <guild_id> -t <currenty_type> -m <money>";	        // 测试工具的命令号帮助
    }

    rpc AllotProduct ( AllotProductReq ) returns ( AllotProductResp ) {
		option( tlvpickle.CmdID ) = 9;			// 命令号
        option( tlvpickle.OptString ) = "u:p:g:n:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -p <pid> -g <guild_id> -n <num>";	        // 测试工具的命令号帮助
    }

    rpc GetProductBySourceId( GetProductBySourceIdReq ) returns ( GetProductBySourceIdResp ) {
		option( tlvpickle.CmdID ) = 10;			// 命令号
        option( tlvpickle.OptString ) = "u:s:g:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -s <source_id> -g <guild_id>";	        // 测试工具的命令号帮助
    }

    rpc GetGameProduct (GetGameProductReq) returns (GetGameProductResp){
		option( tlvpickle.CmdID ) = 11;			// 命令号
        option( tlvpickle.OptString ) = "u:a:g:t:s:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -a <game_id> -g <guild_id> -t <item_type> -s <source_type>";	        // 测试工具的命令号帮助
    }

    rpc GetFetchRecord (GetFetchRecordReq) returns (GetFetchRecordResp){
		option( tlvpickle.CmdID ) = 12;			// 命令号
        option( tlvpickle.OptString ) = "u:p:g:i:n:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -p <product_id> -g <guild_id> -i <index> -n <num>";	        // 测试工具的命令号帮助
    }

    rpc BatchGetUserFetchRecord (BatchGetUserFetchRecordReq) returns (BatchGetUserFetchRecordResp){
		option( tlvpickle.CmdID ) = 13;			// 命令号
        option( tlvpickle.OptString ) = "u:p:g:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -p <product_id> -g <guild_id> ";	        // 测试工具的命令号帮助
    }

    rpc CalculateProduct(CalculateProductReq) returns (CalculateProductResp){
		option( tlvpickle.CmdID ) = 14;			// 命令号
        option( tlvpickle.OptString ) = "u:s:t:g:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -s <source_type> -t <item_type> -g <guild_id> ";	        // 测试工具的命令号帮助
    }

    rpc GetGuildBySourceId( GetGuildBySourceIdReq ) returns ( GetGuildBySourceIdResp ) {
		option( tlvpickle.CmdID ) = 15;			// 命令号
        option( tlvpickle.OptString ) = "u:s:t:m:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -s <souce_id> -t <source_type> -m <status>";	        // 测试工具的命令号帮助
    }

    rpc BatchGetProductById( BatchGetProductByIdReq ) returns ( BatchGetProductByIdResp ) {
		option( tlvpickle.CmdID ) = 16;			// 命令号
        option( tlvpickle.OptString ) = "u:g:p:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -p <product_id>";	        // 测试工具的命令号帮助
    }

    rpc ModifyExamineApply( ModifyExamineApplyReq ) returns ( ModifyExamineApplyResp ) {
		option( tlvpickle.CmdID ) = 17;			// 命令号
        option( tlvpickle.OptString ) = "u:g:e:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -e <examine_id>";	        // 测试工具的命令号帮助
    }

    rpc GetUserProductItem( GetUserProductItemReq ) returns ( GetUserProductItemResp ) {
		option( tlvpickle.CmdID ) = 18;			// 命令号
        option( tlvpickle.OptString ) = "u:g:p:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -p <product_id>";	        // 测试工具的命令号帮助
    }

    rpc DeleteProduct( DeleteProductReq ) returns ( DeleteProductResp ) {
		option( tlvpickle.CmdID ) = 19;			// 命令号
        option( tlvpickle.OptString ) = "u:g:p:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -p <product_id>";	        // 测试工具的命令号帮助
    }

	rpc BatGetGameProductCount ( BatGetGameProductCountReq ) returns ( BatGetGameProductCountResp ) {
		option( tlvpickle.CmdID ) = 20;			// 命令号
        option( tlvpickle.OptString ) = "u:g:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> ";	        // 测试工具的命令号帮助
	}

    //////////////////
    ////代金券相关操作
    //////////////////
    rpc GetGuildGiftCard( GetGuildGiftCardReq ) returns ( GetGuildGiftCardResp ) {
		option( tlvpickle.CmdID ) = 30;			// 命令号
        option( tlvpickle.OptString ) = "u:g:p:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -p <product_id>";	        // 测试工具的命令号帮助
    }

    rpc AddGuildGiftCard ( AddGuildGiftCardReq ) returns ( AddGuildGiftCardResp ) {
		option( tlvpickle.CmdID ) = 31;			// 命令号
        option( tlvpickle.OptString ) = "u:g:p:t:a:s:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -p <product_id> -t <total_price> -a <game_id> -s <used_price>";	        // 测试工具的命令号帮助
    }

    rpc SplitGuildGiftCard ( SplitGuildGiftCardReq ) returns ( SplitGuildGiftCardResp ) {
		option( tlvpickle.CmdID ) = 32;			// 命令号
        option( tlvpickle.OptString ) = "u:g:p:t:a:s:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -p <product_id> -t <price> -a <game_id> -s <num>";	        // 测试工具的命令号帮助
    }

    rpc DeleteGiftCard ( DeleteGiftCardReq ) returns ( DeleteGiftCardResp ) {
		option( tlvpickle.CmdID ) = 33;			// 命令号
        option( tlvpickle.OptString ) = "u:g:t";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -t <ticket_id>";	        // 测试工具的命令号帮助
    }

    rpc GetGiftCardLogInfo ( GetGiftCardLogInfoReq ) returns ( GetGiftCardLogInfoResp ) {
		option( tlvpickle.CmdID ) = 34;			// 命令号
        option( tlvpickle.OptString ) = "u:g:t:s:b:e:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id>  -t <price> -s <status> -b <index> -e <cnt>";	        // 测试工具的命令号帮助
    }

    rpc SetGiftCardLogStatus ( SetGiftCardLogStatusReq ) returns ( SetGiftCardLogStatusResp ) {
		option( tlvpickle.CmdID ) = 35;			// 命令号
        option( tlvpickle.OptString ) = "u:k:s:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -k <order_id>  -s <status> ";	        // 测试工具的命令号帮助
    }

    rpc EditProduct ( AddProductReq ) returns ( AddProductResp ) {
		option( tlvpickle.CmdID ) = 36;										// 命令号
        option( tlvpickle.OptString ) = "u:g:s:m:a:e:n:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guildId> -s <source> -m <status> -a <gameId> -e <examine> -n <name>";	// 测试工具的命令号帮助
    }
}
