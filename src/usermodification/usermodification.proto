syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

package usermodification;

enum MODIFY_TYPE
{
    SEX = 1;
}

message UpdateUserModificationCountReq
{
    required uint32 uid = 1;
    required uint32 type = 2;
}

message UpdateUserModificationCountResp
{
}

message GetUserModificationCountReq
{
    required uint32 uid = 1;
    required uint32 type = 2;
}

message GetUserModificationCountResp
{
    optional uint32 count = 1;
}

message DeleteUserModificationCountReq
{
    required uint32 uid = 1;
    required uint32 type = 2;
}

message DeleteUserModificationCountResp
{

}

message BatchDelUserModificationCountReq
{
    repeated uint32 uid_list = 1;
    required uint32 type = 2;
}

message BatchDelUserModificationCountResp
{

}

service UserModification {
    option( tlvpickle.Magic ) = 13650;

    rpc UpdateUserModificationCount ( UpdateUserModificationCountReq ) returns ( UpdateUserModificationCountResp ) {
        option( tlvpickle.CmdID ) = 1;
	    option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u <uid> -t <type>";
    }

    rpc GetUserModificationCount ( GetUserModificationCountReq ) returns ( GetUserModificationCountResp ) {
            option( tlvpickle.CmdID ) = 2;
    	    option( tlvpickle.OptString ) = "u:t:";
            option( tlvpickle.Usage ) = "-u <uid> -t <type>";
    }

    rpc DeleteUserModificationCount ( DeleteUserModificationCountReq ) returns ( DeleteUserModificationCountResp ) {
                option( tlvpickle.CmdID ) = 3;
        	    option( tlvpickle.OptString ) = "u:t:";
                option( tlvpickle.Usage ) = "-u <uid> -t <type>";
    }

    rpc BatchDelUserModificationCount ( BatchDelUserModificationCountReq ) returns ( BatchDelUserModificationCountResp ) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u <uid1,uid2> -t <type>";
    }
}
