syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package Mission;


//------------------------------------------
// timeline的通用存储结构，所有消息都是包含在这个结构体里面。
//------------------------------------------
message MissionTimelineMsg {
	enum TYPE {
		MISSION_FINISH_MSG = 1;			// 任务完成消息
		EXP_CURRENCY_CHANGED_MSG = 2;	// 经验、红钻变化消息
		MISSION_GUIDE_MSG	= 3;		// 入口引导
		GUILD_MEMBER_CONTRIBUTION = 4;	// 公会成员贡献信息
		
		USER_SOCRE_CHANGED = 5;	        // 用户积分变化
		USER_CHARM_RICH_CHANGED = 6;	// 用户土豪魅力值变化
	}
	required uint32 type = 1;
	required uint32 seqid = 2;
	required bytes msg_bin = 3;
}

message GrowInfoTimelineMsg{
	enum TYPE {
		EXP_CURRENCY_CHANGED_MSG = 1;	// 经验、红钻变化消息 对应的是 GrowInfoMessage
		USER_SOCRE_CHANGED = 2;	        // 用户积分变化       对应的是 UserScoreMessage
		USER_CHARM_RICH_CHANGED = 3;	// 用户土豪魅力值变化 对应的是 NumericInfoMessage
	}
	required uint32 seqid = 1;
	required uint32 type = 2;
	required bytes msg_bin = 3;
}


message MissionFinishMessage {
	enum NOTIFY_MASK {
		NONE = 0;					// 无通知(如任务还未解锁时通过其他方式完成了)
		RED_POINT = 1;				// 任务入口显示红点
		ALTER = 2;					// 弹框提示
		// NEW = 4;					// 任务入口显示NEW(Reserved)
		// NOTIFICATION_CENTER = 8;	// 通知中心显示(Reserved)
	}

	required string mission_key = 1;		// 任务
	required uint32 timestamp = 2;		// 完成的时间 
	required uint32 notify_mask = 3;	// 通知类型, SEE NOTIFY_MASK
	optional string new_guide = 4;		// 是否要修改入口引导文案
}

message GrowInfoMessage {
	required int32 exp 					= 1;		// 最新经验值
	required uint32 level				= 2;		// 最新等级
	required uint32 current_level_exp_min = 3;		// 本等级所需的最小经验
	required uint32 current_level_exp_max = 4;		// 本等级可获取的最大经验
	required int32 currency				= 5;		// 最新红钻
}

message GuildMemContInfoMessage {
	required uint32 guild_id = 1;
	required uint32 total_contribution = 2;	// 历史总贡献
	required uint32 valid_contribution = 3;	// 当前可用贡献
	required uint32 member_lv = 4;	// 公会个人等级
}


message MissionGuideMessage {
	required string guide  = 1;
}


message NumericInfoMessage {
	required uint32 charm = 1;
	required uint32 rich = 2;
	optional uint64 charm64 = 3;
	optional uint64 rich64 = 4;
}

message UserScoreMessage {
	required uint32 score = 1;
}

//------------------------------------------
// 读写协议
//------------------------------------------
message WriteTimelineMsgReq {
	required uint32 id = 1;
	required string suffix = 2;
	required MissionTimelineMsg msg = 3;
}

message WriteTimelineMsgResp {
}

message UpdateGrowInfoReq {
	required uint32 uid = 1;
	required GrowInfoTimelineMsg msg = 2;
}

message UpdateGrowInfoResp {
}


message GetGrowInfoReq {
	required uint32 uid = 1;
	required uint32 seqid = 2;
}

message GetGrowInfoResp {
	repeated GrowInfoTimelineMsg msgs = 1; 
}


//------------------------------------------
// 根据seq删除消息
//------------------------------------------
message BatchDeleteTimelineReq {
	required uint32 id = 1;
	required string suffix = 2;
	repeated uint32 seq_id_list = 3;
}

message BatchDeleteTimelineResp {
}

//------------------------------------------
// 拉取timeline
//------------------------------------------
message PullTimelineMsgReq {
	required uint32 id = 1;
	required string suffix = 2;
	required uint32 start_seqid = 3;
	required uint32 limit = 4;
	optional uint32 is_rev = 5;			// 反向拉取
}

message PullTimelineMsgResp {
	repeated MissionTimelineMsg msg_list = 1;
}

//------------------------------------------
// 标记已读
//------------------------------------------
message MarkReadedReq {
	required uint32 id = 1;
	required string suffix = 2;
	required uint32 seqid = 3;
}

message MarkReadedResp {
}

//------------------------------------------
// 查已读
//------------------------------------------
message GetReadedReq {
	required uint32 id = 1;
	required string suffix = 2;
}

message GetReadedResp {
	required uint32 seqid = 1;
}



service MissionTimeline {
	option( tlvpickle.Magic ) = 15040;		// 服务监听端口号	

    rpc WriteTimelineMsg(WriteTimelineMsgReq) returns (WriteTimelineMsgResp) {
        option( tlvpickle.CmdID ) = 1;																		// 命令号
        option( tlvpickle.OptString ) = "i:s:";														// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <id> -s <suffix>";			// 测试工具的命令号帮助
    }
    
    rpc BatchDeleteTimeline(BatchDeleteTimelineReq) returns (BatchDeleteTimelineResp) {
        option( tlvpickle.CmdID ) = 2;																		// 命令号
        option( tlvpickle.OptString ) = "i:s:l:";														// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <id> -s <suffix> -l <seq_id_1, seq_id_2>";			// 测试工具的命令号帮助
    }
    
    rpc PullTimelineMsg(PullTimelineMsgReq) returns (PullTimelineMsgResp) {
        option( tlvpickle.CmdID ) = 3;																		// 命令号
        option( tlvpickle.OptString ) = "i:s:b:l:";														// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <id> -s <suffix> -b <begin_seq> -l <limit>";			// 测试工具的命令号帮助
    }
    
    rpc MarkReaded(MarkReadedReq) returns (MarkReadedResp) {
        option( tlvpickle.CmdID ) = 4;											// 命令号
        option( tlvpickle.OptString ) = "i:s:d:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <id> -s <suffix> -d <seqid>";			// 测试工具的命令号帮助
    }
    
    rpc GetReaded(GetReadedReq) returns (GetReadedResp) {
        option( tlvpickle.CmdID ) = 5;											// 命令号
        option( tlvpickle.OptString ) = "i:s:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <id> -s <suffix>";			// 测试工具的命令号帮助
    }


	rpc UpdateGrowInfo(UpdateGrowInfoReq) returns (UpdateGrowInfoResp) {
        option( tlvpickle.CmdID ) = 6;											// 命令号
        option( tlvpickle.OptString ) = "";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";			// 测试工具的命令号帮助
    }

	rpc GetGrowInfo(GetGrowInfoReq) returns (GetGrowInfoResp) {
        option( tlvpickle.CmdID ) = 7;											// 命令号
        option( tlvpickle.OptString ) = "u:s:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -s <seq_id>";			// 测试工具的命令号帮助
    }
}