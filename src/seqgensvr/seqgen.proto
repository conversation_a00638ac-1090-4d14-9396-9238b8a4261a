syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";		

// namespace
package SeqGen;									

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

//////////////////
message GenSeqReq {
	required uint32 id = 1;
	required uint32 type = 2;
	optional string suffix = 3;
	optional uint32 incr = 4;		// 自增的值，如果不填默认为自增1
}

message GenSeqRsp {
	required int32 ret = 1;
	required uint32 seq = 2;
}

//////////////////
message GetLastestSeqReq {
	required uint32 id = 1;
	required uint32 type = 2;
	optional string suffix = 3;
}

message GetLastestSeqResp {
	required int32 ret = 1;
	required uint32 seq = 2;
}

message SeqPair {
	required uint32 id = 1;		// groupid
	required uint32 seq = 2;
}

// for marshall
message SeqPairList {
	repeated SeqPair seq_pair_list = 1;	// 
}

message GetSeqMapReq {
	required uint32 type = 1;	// type
}

message GetSeqMapResp {
	repeated SeqPair seq_pair_list = 1;
}

message SetSeqMapReq {
	required uint32 type = 1;
	repeated SeqPair seq_pair_list = 2;
}

message SetSeqMapResp {
}

message DelSeqMapReq {
	required uint32 type = 1;
	repeated uint32 id_list = 2;
}

message DelSeqMapResp {
}


message BatchSetUserGroupNeedUpdateReq {
	required uint32 group_id = 1;
	required uint32 type = 2;
	repeated uint32 member_uid = 3;
}

message BatchSetUserGroupNeedUpdateResp {
}

message SetSeqReq {
	required uint32 id = 1;
	required uint32 type = 2;
	required uint32 seq_id = 3;
	optional string suffix = 4;
}

message SetSeqResp {
}

message BatchGetLastestSeqReq {
	repeated uint32 id = 1;
	required uint32 type = 2;
	optional string suffix = 3;
}

message BatchGetLastestSeqResp {
	repeated SeqPair seq_id_list = 1;
}

//////////////////
service SeqGen {
    option( tlvpickle.Magic ) = 14000;		// 服务监听端口号

    rpc GenSeqId(GenSeqReq) returns(GenSeqRsp) {
        option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "u:s:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -s <suffix> -t <type>";			// 测试工具的命令号帮助
    }

    rpc GetLastestSeq(GetLastestSeqReq) returns(GetLastestSeqResp) {
        option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "u:s:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -s <suffix> -t <type>";			// 测试工具的命令号帮助
    }
    
    rpc GetSeqMap(GetSeqMapReq) returns(GetSeqMapResp) {
        option( tlvpickle.CmdID ) = 3;									// 命令号
        option( tlvpickle.OptString ) = "u:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t <type>";						// 测试工具的命令号帮助
    }
    
    rpc SetSeqMap(SetSeqMapReq) returns(SetSeqMapResp) {
        option( tlvpickle.CmdID ) = 4;									// 命令号
        option( tlvpickle.OptString ) = "t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <type>";						// 测试工具的命令号帮助
    }
    
    rpc DelSeqMap(DelSeqMapReq) returns(DelSeqMapResp) {
        option( tlvpickle.CmdID ) = 5;									// 命令号
        option( tlvpickle.OptString ) = "t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <type>";						// 测试工具的命令号帮助
    }
    
    rpc BatchSetUserGroupNeedUpdate( BatchSetUserGroupNeedUpdateReq ) returns ( BatchSetUserGroupNeedUpdateResp ) {
        option( tlvpickle.CmdID ) = 6;									// 命令号
        option( tlvpickle.OptString ) = "t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <type>";						// 测试工具的命令号帮助
    }
    
    rpc SetSeq( SetSeqReq ) returns ( SetSeqResp ) {
        option( tlvpickle.CmdID ) = 7;									// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";						// 测试工具的命令号帮助
    }
    
    rpc BatchGetLastestSeq( BatchGetLastestSeqReq ) returns ( BatchGetLastestSeqResp ) {
        option( tlvpickle.CmdID ) = 8;                                      // 命令号
        option( tlvpickle.OptString ) = "u:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <id_list> -t <type>";   // 测试工具的命令号帮助
    }
}