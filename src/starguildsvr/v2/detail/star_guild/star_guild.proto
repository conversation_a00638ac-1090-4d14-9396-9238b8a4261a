syntax="proto2";

package starguild_internal;

message Index {
    optional uint32 current_score                   = 1;    // 截止昨日数值
    optional uint32 settle_up_timestamp             = 2;    // 结算时间
    optional uint32 today_score                     = 3;    // 今日数值
    optional uint32 today_score_update_timestamp    = 4;    // 今日数值更新时间

    optional uint32 yesterday_score                 = 5;    // 昨日的结算分
    optional sint32 yesterday_adjust_score          = 6;    // 昨日的结算调整分
}

message RealtimeStarGuildInfo {
    required uint32 guild_id    = 1;            // 公会ID
    optional uint32 star_level  = 2;            // 星级
    optional Index popularity   = 3;            // 人气值指标
    optional Index aliveness    = 4;            // 活跃值指标

    optional uint32 under_floor_aliveness_days = 11; // 活跃值连续未达标天数
}
