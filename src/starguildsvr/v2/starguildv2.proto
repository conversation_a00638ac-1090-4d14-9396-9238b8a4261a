syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package starguildv2;


// 用户活跃事件
message UserAlivenessEvent {
    enum Type {
        OPEN_APP = 1;
        HEARTBEAT = 2;
    }

    required uint32 type = 1;
    required uint32 uid = 2;
    optional uint32 guild_id = 3;   // 如果不传, 则服务器查询结果为准
    optional uint32 timestamp = 4;  // 如果不传, 则以服务器时间为准
}

// 用户上报活跃事件
message UserReportAlivenessEventReq {
    required UserAlivenessEvent user_aliveness_info = 1;
}

message StarScore {
    required uint32 current     = 1;    // 当前分值
    required uint32 today       = 2;    // 今日实时分值
    required uint32 yesterday   = 3;    // 昨日实际结算分值
}

message StarLevelRequirement {
    required uint32 popularity_required = 1;        // 人气值需求
    required uint32 aliveness_required = 2;         // 活跃值需求
}

message StarGuildInfo {
    required uint32 guild_id = 1;               // 公会ID
    required uint32 star_level = 2;             // 当前公会星级
    required StarScore popularity_score = 3;    // 当前人气值
    required StarScore aliveness_score = 4;     // 当前活跃值
}

message GetGuildStarInfosReq {
    repeated uint32 guild_id_list = 1;
}

message GetGuildStarInfosResp {
    // 不带next_level_requirement
    repeated StarGuildInfo star_info_list = 1;
}

message GetGuildStarInfoDetailReq {
    required uint32 guild_id = 1;
}

message GetGuildStarInfoDetailResp {
    required StarGuildInfo star_info = 1;
    required StarLevelRequirement next_level_requirement = 10;
}

message ManullySettleUpReq {
    required uint32 year = 1;
    required uint32 month = 2;
    required uint32 day = 3;
}

service StarGuildV2 {
	option( tlvpickle.Magic ) = 15121;

    rpc UserReportAlivenessEvent( UserReportAlivenessEventReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:t:g:";
        option( tlvpickle.Usage ) = "-u <uid> -t <timestamp> -g <guild_id>";
	}

    rpc GetGuildStarInfos( GetGuildStarInfosReq ) returns ( GetGuildStarInfosResp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g <guild_id_list>";
    }

    rpc GetGuildStarInfoDetail( GetGuildStarInfoDetailReq ) returns ( GetGuildStarInfoDetailResp ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g <guild_id>";
    }

    rpc ManullySettleUp( ManullySettleUpReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 199;
        option( tlvpickle.OptString ) = "y:m:d:";
        option( tlvpickle.Usage ) = "-y <year> -m <month> -d <day>";
    }

    rpc DumpAllStarGuildInfos( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 200;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
}
