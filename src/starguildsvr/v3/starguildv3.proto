syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package starguildv3;

enum UserConsumeType {
	UCT_GAME = 0;
	UCT_LIVE = 1;
	UCT_FUNNY_ROOM_GIFT = 2;
	UCT_MAX = 3;
}

enum UserOperateType {
	CHECK_IN_DAILY = 1;
	UNUSE_1 = 2;
	DONATION = 3;
	CAPITAL_INJECTION = 4;
	CONSUME = 5;
}

// 用户贡献事件

message UserAddContributeReq {
	required uint32 uid = 1;
	optional uint32 guild_id = 2;
	required uint32 event_type = 3; // 见其他协议(1.签到 2.补签 3.捐献 4.注资 5.消费)

	optional uint32 reddiamond_amount = 4; // 捐献用
	optional uint32 money_amount = 5;// (注资/消费)用
	optional uint32 consume_type = 6;// 消费类型

	optional bool valid = 7;//操作是否有效
	optional string order_id = 8; // 去重id

}

message UserAddContributeResp {
	required uint32 value_added = 1;
}

message StarGuildInfo {
    required uint32 guild_id = 1;						// 公会ID
    required uint32 star_level = 2;						// 当前公会星级
    required uint32 contribution_base = 3;				// 基础贡献值
	required uint32 contribution_ext = 4;				// 贡献值加成部分
	required uint32 contribution_ext_pay = 5;			// 当前贡献值加成的消费额
	required uint32 contribution_ext_percent = 6;		// 当前贡献加成百分比
	required uint32 contribution_next_pay = 7;			// 下一级加成所需消费
	required uint32 contribution_next_percent = 8;		// 下一级加成百分比
	required uint32 contribution_next_level = 9;		// 下一星级所需贡献
	optional uint32 contribution_cur_level = 10;		// 当前星级所需贡献
	required uint32 join_num_yesterday = 11;			// 昨日加入人数
	optional uint32 contribution_30_days_ago = 12;		// 30日前的当日贡献值
	optional uint32 contribution_today = 13;			// 今日贡献（非实时）
	optional uint32 last_calc_time = 14;
}

message GetGuildStarInfosReq {
    repeated uint32 guild_id_list = 1;
	optional bool query_today = 2;
}

message GetGuildStarInfosResp {
    repeated StarGuildInfo star_info_list = 1;
}

message GetMemberContributionExtReq {
	enum TIME_LIMIT{
		TIME_LIMIT_ALL = 1;
		TIME_LIMIT_RECENT_MONTH = 2;
	}

	required uint32 guild_id = 1;
	optional uint32 start = 2;
	optional uint32 limit = 3;
	required uint32 time_limit = 4;

	repeated uint32 uid_list = 5;
}

message MemberContributionExt{
	required uint32 uid = 1;
	required uint32 total = 2;
	required uint32 game_consume = 3;
	required uint32 live_consume = 4;
	optional uint32 funny_room_gift = 5;
}

message GetMemberContributionExtResp {
	required uint32 guild_id = 1;
	repeated MemberContributionExt contri_ext_list = 2;
	optional uint32 total_count = 3;
}

message GetGuildMutableContributionReq {
	required uint32 guild_id = 1;
}

message GetGuildMutableContributionResp {
	required uint32 mutable_contribution = 1;
}

message AddGuildMutableContributionReq {
	required uint32 guild_id = 1;
	required int32  change_value = 2;
	required string order_id = 3;
	required string order_desc = 4;
	required uint32 op_uid = 5;
}

message AddGuildMutableContributionResp {

}

message MemberQuitGuildReq{
    required uint32 uid = 1;
	required uint32 guild_id = 2;
}

message GetGuildInfoByLevelReq {
	repeated uint32 guild_id_list = 1;
	optional uint32 level_begin = 2;
	optional uint32 level_end = 3;
	required uint32 limit = 4;

}

message GetGuildInfoByLevelObj {
	required uint32 guild_id = 1;
	required uint32 star_level = 2;
	required uint32 contribution = 3;
	required uint32 contribution_percent = 4;
}

message GetGuildInfoByLevelResp {
	repeated GetGuildInfoByLevelObj obj_list = 1;
}

message CalcSingleGuildReq {
	required uint32 guild_id = 1;
}

// 以下为统计查询接口
message GetGuildLevelCountByDateReq {
	required uint32 date_from = 1;
	required uint32 date_to = 2;
}

message LevelCount {
	required uint32 star_level = 1;
	required uint32 guild_count = 2;
}
message GuildLevelCount {
	required uint32 date = 1;
	repeated LevelCount level_count_list = 2;
}

message GetGuildLevelCountByDateResp {
	repeated GuildLevelCount guild_level_list = 1;
}

message GetGuildInfoByDateReq {
	required uint32 date = 1;
	required uint32 star_level = 2;
	required uint32 start = 3;
	required uint32 limit = 4;
}
message GuildInfoByDate {
	required uint32 guild_id = 1;
	required uint32 member_count = 2;
}
message GetGuildInfoByDateResp {
	repeated GuildInfoByDate guild_info_list = 1;
}

message GetRankListByContributionReq {
	required uint32 guild_id = 1;
	required uint32 start = 2;
	required uint32 limit = 3;
}

message RankListByContribution {
	required uint32 guild_id = 1;
	required uint32 contribution = 2;
	optional uint32 consume = 3;
}

message GetRankListByContributionResp {
	repeated RankListByContribution rank_list = 1;
	required uint32 my_rank = 2;
	required uint32 my_contribution = 3;
}

message ReportDismissGuildReq {
	required uint32 guild_id = 1;
}

message GetGuildLevelMemberHistoryReq {
	required uint32 date = 1;
	repeated uint32 guild_id_list = 2;
}
message GuildLevelMemberHistory {
	required uint32 guild_id = 1;
	required uint32 guild_level = 2;
	required uint32 guild_member_count = 3;
}
message GetGuildLevelMemberHistoryResp {
	repeated GuildLevelMemberHistory result_list = 1;
}


message GuildMonthRank {
	required uint32 uid = 1;
	required uint32 value = 2;
}
message GetGuildMonthRankReq {
	required uint32 guild_id = 1;
	required uint32 value_type = 2;
	required uint32 start = 3;
	required uint32 limit = 4;
}
message GetGuildMonthRankResp {
	repeated GuildMonthRank rank_list = 1;
}

message GetGuildEventCountReq {
	required uint32 guild_id = 1;
	required uint32 event_type = 2;  //(同上类型)
	required uint32 dt_time = 3;
}
message GetGuildEventCountResp {
	required uint32 count = 1;
}

service StarGuildV3 {
	option( tlvpickle.Magic ) = 15122;

    rpc UserAddContribute( UserAddContributeReq ) returns( UserAddContributeResp ) {
		option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:g:e:r:m:t:";
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -e <event 1.checkin 3.donation 4.inject> -r<reddiamond> -m<money_amount> -t<consume_type>";
	}

    rpc GetGuildStarInfos( GetGuildStarInfosReq ) returns ( GetGuildStarInfosResp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g <guild_id>";
    }
	rpc GetMemberContributionExt ( GetMemberContributionExtReq  ) returns ( GetMemberContributionExtResp ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "g:s:l:t:";
        option( tlvpickle.Usage ) = "-g <guild_id_list> -s<start> -l<limit> -t<time_limit>";
    }
	
	rpc CalcYesterdayManually ( tlvpickle.SKBuiltinEmpty_PB  ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

	rpc GetGuildMutableContribution (GetGuildMutableContributionReq) returns (GetGuildMutableContributionResp) {
		option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g<guild_id>";
	}

	rpc AddGuildMutableContribution (AddGuildMutableContributionReq) returns (AddGuildMutableContributionResp) {
		option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "g:a:o:d:u:";
        option( tlvpickle.Usage ) = "-g<guild_id> -a<amount> -o<order_id> -d<order_desc> -u<op_uid>";
	}

	rpc MemberQuitGuild (MemberQuitGuildReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
		option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "u:g:";
        option( tlvpickle.Usage ) = "-u<uid> -g<guild_id>";
	}

	rpc GetGuildInfoByLevel (GetGuildInfoByLevelReq) returns (GetGuildInfoByLevelResp) {
		option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "g:b:e:";
        option( tlvpickle.Usage ) = "-u<uid> -b<level_begin> -e<level_end>";
	}
    
	rpc CalcSingleGuild (CalcSingleGuildReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
		option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g<guild_id>";
	}

	rpc GetGuildLevelCountByDate (GetGuildLevelCountByDateReq) returns (GetGuildLevelCountByDateResp) {
		option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "s:e:";
        option( tlvpickle.Usage ) = "-s<date_from> -e<date_to>";
	}
	rpc GetGuildInfoByDate (GetGuildInfoByDateReq) returns (GetGuildInfoByDateResp) {
		option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "d:l:s:n:";
        option( tlvpickle.Usage ) = "-d<date> -l<level> -s<start> -n<limit>";
	}

	rpc GetRankListByContribution (GetRankListByContributionReq) returns (GetRankListByContributionResp) {
		option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "g:s:l:";
        option( tlvpickle.Usage ) = "-g<guild_id> -s<start> -l<limit>";
	}

	rpc ReportDismissGuild (ReportDismissGuildReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
		option( tlvpickle.CmdID ) = 13;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g<guild_id>";
	}

	rpc GetGuildLevelMemberHistory (GetGuildLevelMemberHistoryReq) returns (GetGuildLevelMemberHistoryResp) {
		option( tlvpickle.CmdID ) = 14;
        option( tlvpickle.OptString ) = "g:d:";
        option( tlvpickle.Usage ) = "-g<guild_id> -d<date>";
	}

	rpc GetGuildMonthRank (GetGuildMonthRankReq) returns (GetGuildMonthRankResp) {
		option( tlvpickle.CmdID ) = 15;
        option( tlvpickle.OptString ) = "g:t:s:l:";
        option( tlvpickle.Usage ) = "-g<guild_id> -t<value_type 3.donation 5.consume> -s<start> -l<limit>";
	}

	rpc GetGuildEventCount (GetGuildEventCountReq) returns (GetGuildEventCountResp) {
		option( tlvpickle.CmdID ) = 16;
        option( tlvpickle.OptString ) = "g:e:d:";
        option( tlvpickle.Usage ) = "-g<guild_id> -e<event 3.donation 5.consume> -d<timestamp>";
	}
	
}

