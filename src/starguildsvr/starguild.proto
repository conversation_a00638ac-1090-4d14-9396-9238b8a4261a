syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package starguild;

enum UserOperType {
	 QUITGUILD    = 1;
	 LOGIN        = 2;
	 GUILDSIGNIN  = 3;
	 OPENCIRCLE   = 4;
	 REPRINT	  = 5;
	 TALKED       = 6;
	 ESSENCE      = 7;
	 GUILDSUPPLEMENT = 8;
}

// 用户行为上报信息
message ReportInfoReq {
	required UserOperType opertype = 1;		// 操作类型
	optional uint32 guild_id = 2;		// 如果有公会ID，则带上公会ID
	optional uint32 timestamp = 3;      //无则用客服器时间做时间戳
	optional uint32 continue_signin = 4;  //连续签到
	optional bool	is_post_essence = 5; //是否发了精华帖	
}

message ReportInfoResp {
	
}

// 新用户的添加
message AddUserReq {
	required bool is_valid = 1;			// 用户是否有效
	required bool is_invite = 2;        // 公会邀请新增用户上报为true，且还要带上guild_id字段
	optional uint32 guild_id = 3;       // 邀请新增用户加入的公会
}

message DailyUser{
	required uint32 uid = 1;
	required bool today_has_quit_guild = 2;	//统计当天是否有退出公会行为
	required bool is_login = 3;			   //是否有登陆
	required bool is_guild_signin = 4;     // 是否有公会签到
	required uint32 continue_signin = 5;   //连续签到天数
	required bool is_open_circle = 6; 		//是否有打开过游戏圈
	required bool is_reprint = 7;			//是否有转载过游戏圈贴子到外网
	required bool is_talk = 8;			//是否有发言
	optional uint32 guild_id = 9;       //当前公会
	optional bool is_post_essence = 10;	//是否发了精华帖	
}

enum SCORE_TYPE {
    HUMANS = 0;    //人气值
    ACTIVE = 1;    //活跃值
}

message GuildScore{
    required uint32 score_type = 1;
    required string score_name = 2;     //积分名称
    required uint32 today_score = 3;    
    required uint32 today_limit = 4;    //用途已改字段，代表升至下一级需要的值
    required uint32 total_score = 5;    //历史累积积分
    required uint32 yesterday_score = 6;   //昨天的积分
	optional uint32	daysof_lt_base = 7;	//累计多少天少于基础分了
}

message GuildScoreInfo{
	required uint32 guildid = 1;
	repeated GuildScore scores = 2;	 
	required uint32 star_level = 3;
	optional uint32 actstamp = 4;	
	optional uint32 humstamp = 5;	
}

message GetGuildScoreReq{
	required uint32 guildid = 1;
	optional bool is_real = 2;
}

message InvitedInfo{
	required uint32 uid = 1;
	required uint32 guildid = 2;	
}

message GetDailyUserReq{
		
}

message AddUserResp {
}

message CheckUserReq{
}

message CheckUserResp{
	required bool is_valid = 1;			// 用户是否有效
}

// 批量查公会等级信息
message BatchGetGuildLevelReq {
	repeated uint32 guild_id_list = 1;	// 公会ID列表
}

message BatchGetGuildLevelResp {
	repeated GuildScoreInfo guild_lv_list = 1;
}

message GetStarGuildInfoReq {
	required uint32 guildid = 1;
}

message GetStarGuildInfoResp {
	required uint32 star_level = 1;
	required uint32 invalid_user_count = 2;
}

message GuildMemberInfo{
	required uint32	gid = 1;
	required string name = 2;
	required uint32 memcount = 3;
}

service StarGuild {
	option( tlvpickle.Magic ) = 15120;		// 服务监听端口号
	
	rpc ReportInfo( ReportInfoReq ) returns( ReportInfoResp ){
		option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "u:o:g:t:s:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -o <oper> -g <guild_id> -t <timestamp> -s <continuelogin>";	
	}
	
	rpc AddUser( AddUserReq ) returns( AddUserResp ){
		option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "u:i:g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -i <is_valid> -g <guildid>";	// 测试工具的命令号帮助
	}
	
	rpc BatchGetGuildLevel( BatchGetGuildLevelReq ) returns( BatchGetGuildLevelResp ){
		option( tlvpickle.CmdID ) = 3;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id1,guild_id2,...>";	// 测试工具的命令号帮助
	}
	rpc CheckUser( CheckUserReq ) returns( CheckUserResp ){
		option( tlvpickle.CmdID ) = 4;										// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";	// 测试工具的命令号帮助
	}
	rpc GetDailyUser( GetDailyUserReq) returns( DailyUser ){
		option( tlvpickle.CmdID ) = 5;										// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";	// 测试工具的命令号帮助
	}
	rpc GetGuildScore( GetGuildScoreReq ) returns( GuildScoreInfo ){
		option( tlvpickle.CmdID ) = 6;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guildid> ";	// 测试工具的命令号帮助
	}
	rpc GetStarGuildInfo( GetStarGuildInfoReq ) returns( GetStarGuildInfoResp ){
		option( tlvpickle.CmdID ) = 7;										// 命令号
        option( tlvpickle.OptString ) = "g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guildid> ";	// 测试工具的命令号帮助
	}
}

