syntax="proto2";

package guildrecruit;

import "common/tlvpickle/skbuiltintype.proto";

//奖励介绍图
message AwardIntro{
    required uint32 group_id = 1;			//reward group id
    required string image_url = 2;
    optional string title = 3;
}

//奖品图
message AwardInfo{
    required uint32 award_id = 1;
    required string image_url = 2;
    required string title = 3;
}

message GuildRecruitInfo{
    required uint32 act_id          = 1;
    required uint32 game_id         = 2;
    required string act_name        = 3;	//default value:game_name
    required string banner          = 4;
    required string act_rules       = 5;
    optional string sign_rules      = 6;
    optional string time_axis       = 7;	//format:"2016-06-16 00:00:00,2016-06-16 00:00:00"
    optional string axis_url        = 8;
    optional string progress_url    = 9;
    optional bytes addtional_info    = 10;
    optional string style            = 11;
    optional uint32 areas           = 12;
    repeated AwardIntro award_intro_list = 13; 
}

message DelGuildRecruitReq{
    required uint32 act_id = 1;
}

message GetGuildRecruitByGameIdReq{
    required uint32 game_id = 1;
}

message GetGuildRecruitAllResp{
    repeated GuildRecruitInfo list = 1;
}

message UpdateAwardIntroReq{
    required uint32 act_id      = 1;
    required AwardIntro award_intro = 2;
}

message DelAwardInfoReq{
    required uint32 award_id = 1;
}

service guildrecruit {
	/*  Server listening port */
	option( tlvpickle.Magic ) = 15257;

    rpc UpdateGuildRecruitInfo( GuildRecruitInfo ) returns (tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "i:g:b:a:s:t:x:p:d:y:r:";
        option( tlvpickle.Usage ) = "-i<act id> -g<game_id> -b<banner> -a<act_rules> -s<sign_rules> -t<time_axis> -x<axis_url> -p<progress_url> -d<addt_info> -y<style> -r<areas>";
    }

    rpc DelGuildRecruit( DelGuildRecruitReq ) returns (tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "a:";
        option( tlvpickle.Usage ) = "-a <act id>";
    }

    rpc GetGuildRecruitByGameId( GetGuildRecruitByGameIdReq ) returns (GuildRecruitInfo) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "a:";
        option( tlvpickle.Usage ) = "-a <act id>";
    }

    rpc GetGuildRecruitAll( tlvpickle.SKBuiltinEmpty_PB ) returns (GetGuildRecruitAllResp) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "a:";
        option( tlvpickle.Usage ) = "-a <act id>";
    }

    rpc UpdateAwardIntro(UpdateAwardIntroReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "a:g:i:t:";
        option( tlvpickle.Usage ) = "-a<act id> -g<group id> -i<image url> -t<title>";
    }

    rpc UpdateAwardInfo(AwardInfo) returns (tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "a:i:t:";
        option( tlvpickle.Usage ) = "-a<award id> -i<image url> -t<title>";    
    }

    rpc DelAwardInfo(DelAwardInfoReq) returns (tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "a:";
        option( tlvpickle.Usage ) = "-a<award id>";    
    }    
}