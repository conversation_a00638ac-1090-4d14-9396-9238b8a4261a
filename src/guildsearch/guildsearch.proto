syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package GuildSearch;


message GuildSearchReq
{
	required uint32 uid	= 1;
	required string keyword = 2;
	required uint32 groupid = 3;
}

message GuildSearchResult
{
	required uint32 uid = 1;
	required string name = 2;
}
message GuildSearchResp
{
	repeated GuildSearchResult results = 1;
}

message GuildSearchJoinGroupReq
{
	required uint32	guildid = 1;
	required uint32	groupid = 2;
}

message GuildSearchQuitGroupReq
{
	required uint32	guildid = 1;
	repeated uint32	grouplist = 2;
}
message GuildSearchReportResp{}


message GuildSearchUpdateLoginReq
{
	required uint64	logintime = 1;
}
message GuildSearchUpdateLoginResp{}


message GuildSearchUpdateNickNameReq
{
	required string	nickname = 1;
}
message GuildSearchUpdateNickNameResp{}


service GuildSearch {
	option( tlvpickle.Magic ) = 15180;		// 服务监听端口号	
	
	rpc GetGuildSearch( GuildSearchReq ) returns ( GuildSearchResp ){
		option( tlvpickle.CmdID ) = 1;									// 命令号
        option( tlvpickle.OptString ) = "u:k:g:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -k <keyword> -g<groupid>";
	}

	rpc ReportJoinGroup ( GuildSearchJoinGroupReq ) returns ( GuildSearchReportResp ){
		option( tlvpickle.CmdID ) = 2;									// 命令号
        option( tlvpickle.OptString ) = "u:g:p:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guildid> -p<groupid>";
	}

	rpc ReportQuitGroup ( GuildSearchQuitGroupReq ) returns ( GuildSearchReportResp ){
		option( tlvpickle.CmdID ) = 3;									// 命令号
        option( tlvpickle.OptString ) = "u:g:p:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <guildid> -p<groupid>";
	}

	rpc ReportLoginTime ( GuildSearchUpdateLoginReq ) returns ( GuildSearchUpdateLoginResp ){
		option( tlvpickle.CmdID ) = 4;									// 命令号
        option( tlvpickle.OptString ) = "u:t:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t<logintime>";
	}

	rpc ReportNewNickName ( GuildSearchUpdateNickNameReq ) returns ( GuildSearchUpdateNickNameResp ){
		option( tlvpickle.CmdID ) = 5;									// 命令号
        option( tlvpickle.OptString ) = "u:s:";						// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -s<nickname>";
	}
}
