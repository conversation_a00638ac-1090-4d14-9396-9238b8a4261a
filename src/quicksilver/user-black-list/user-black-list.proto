syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/user-black-list";
package userBlackListService;


service UserBlackList {

    rpc GetUserBlackList (GetUserBlackListReq) returns (GetUserBlackListResp) {
    }

    rpc AddUserBlackList (AddUserBlackListReq) returns (AddUserBlackListResp) {
    }

    rpc DelUserBlackList (DelUserBlackListReq) returns (DelUserBlackListResp) {
    }

    rpc CheckIsInBlackList (CheckIsInBlackListReq) returns (CheckIsInBlackListResp) {
    }

}
/*
message BlackListItem {
    uint32 passive_uid = 1; //被拉黑方uid
    uint32 update_time = 2;   //拉黑时间

}*/

message GetUserBlackListReq {
    uint32 active_uid = 1;
}

message GetUserBlackListResp {
    uint32 active_uid = 1;
    repeated  uint32 black_list = 2;
}

message AddUserBlackListReq {
    uint32 active_uid = 1;  //主动拉黑方uid
    uint32 passive_uid = 2; //被拉黑方uid
}

message AddUserBlackListResp {
}

message DelUserBlackListReq {
    uint32 active_uid = 1;  //主动拉黑方uid
    uint32 passive_uid = 2; //被拉黑方uid
}

message DelUserBlackListResp {
}

message CheckIsInBlackListReq {
    uint32 active_uid = 1;  //主动拉黑方uid
    uint32 passive_uid = 2; //被拉黑方uid
}

message CheckIsInBlackListResp {
    bool b_in = 1;
}

