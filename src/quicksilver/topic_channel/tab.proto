syntax="proto3";

package topic_channel.tab;

option go_package = "golang.52tt.com/protocol/services/topic_channel/tab";

service TopicChannelTab {

    /* -------运营后台------- */

    // InsertTab 用于插入一种类型。
    rpc InsertTab ( InsertTabReq ) returns ( InsertTabResp );

    // UpdateTab 用于更新某个类型。
    rpc UpdateTab ( UpdateTabReq ) returns ( UpdateTabResp );

    // DeleteTab 用于删除某个类型。
    rpc DeleteTab ( DeleteTabReq ) returns ( DeleteTabResp );

    // RearrangeTabs 用于重新排序所有的类型。
    rpc RearrangeTabs ( RearrangeTabsReq ) returns ( RearrangeTabsResp );

    // TabsForTT 用于获取所有的类型。
    rpc TabsForTT ( TabsForTTReq ) returns ( TabsForTTResp );

    /* ---------APP--------- */

    // Tabs 用于获取所有的类型。
    rpc Tabs ( TabsReq ) returns ( TabsResp );

    // FiniteTabs 用于获取指定的类型。
    rpc FiniteTabs ( FiniteTabsReq ) returns ( FiniteTabsResp );

    // FiniteTabsByTags 通过指定tagid获取相应的类型。
    rpc FiniteTabsByTags ( FiniteTabsByTagsReq ) returns ( FiniteTabsByTagsResp );

}

// Tab 是主题房间类型。
message Tab {
    uint32          id        = 1; // id 是类型的唯一标识。
    uint32          weight    = 2; // weight 代表每种类型对应的权重，weight越小权重越高。
    string          name      = 3; // name 是主题房间类型名。
    enum TabType {
        NORMAL = 0;     //普通分类
        GAME = 1;       //游戏分类
    }
    TabType         tab_type  = 4; // tab_type 代表主题房类型所属的父级分类。
    uint32          tag_id    = 5; // tag_id 代表用户所填游戏卡对应类型的ID。
    string          image_uri = 6; // image_uri 代表背景用图的URI。
    repeated string room_name = 7; // room_name 代表房间具体名称。
    uint32          version   = 8; // version 代表版本。
}

message InsertTabReq {
    Tab tab = 1; // tab 代表要插入的类型，插入时需要填写的字段包括name、tab_type、tag_id、image_uri和room_name。
}

message InsertTabResp {
    bool result = 1; // result 反应插入是否成功。
}

message UpdateTabReq {
    Tab tab = 1; // tab 代表要更新的类型，更新时需要将不参与更新的字段一并填充（未更新字段填充旧值）。
}

message UpdateTabResp {
    bool result = 1; // result 反应更新是否成功。
}

message DeleteTabReq {
    Tab tab = 1; // tab 代表要删除的类型，删除时只需要id字段。
}

message DeleteTabResp {
    bool result = 1; // result 反应删除是否成功。
}

message RearrangeTabsReq {
    repeated Tab tabs = 1; // tabs 包含所有类型，它们被编排为新的顺序。
}

message RearrangeTabsResp {
    bool result = 1; // result 反应重新排序是否成功。
}

message TabsForTTReq {
    uint32 skip  = 1; // skip 表示上一次请求得到的类型总数，首次填0即可。
    uint32 limit = 2; // limit 表示本次请求希望获得的类型数量。
}

message TabsForTTResp {
    repeated Tab tabs  = 1; // tabs 代表本次请求返回的所有类型。
    uint32       skip  = 2; // skip 表示已请求得到的类型总数（包括本次请求）。
    uint32       limit = 3; // limit 表示本次请求实际获得的类型数量。
    uint32       total = 4; // total 表示数据库中保存类型的总量。
}

message TabsReq {
    uint32 skip  = 1; // skip 表示上一次请求得到的类型总数，首次填0即可。
    uint32 limit = 2; // limit 表示本次请求希望获得的类型数量。
}

message TabsResp {
    repeated Tab tabs  = 1; // tabs 代表本次请求返回的所有类型。
    uint32       skip  = 2; // skip 表示已请求得到的类型总数（包括本次请求）。
    uint32       limit = 3; // limit 表示本次请求实际获得的类型数量。
    uint32       total = 4; // total 表示数据库中保存类型的总量。
}

message FiniteTabsReq {
    repeated Tab tabs = 1; // tabs 包含指定的所有类型，请求时只需填写id字段。
}

message FiniteTabsResp {
    repeated Tab tabs  = 1; // tabs 代表所有指定的类型。
}

message FiniteTabsByTagsReq {
    repeated uint32 tag_id = 1;
}

message FiniteTabsByTagsResp {
    repeated Tab tabs  = 1; // tabs 代表所有指定的类型。
}