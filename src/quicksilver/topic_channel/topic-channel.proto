syntax="proto3";

package topic_channel.channel;

option go_package = "golang.52tt.com/protocol/services/topic_channel/channel";

service Channel {
    //创建主题房
    rpc AddChannel(AddChannelReq) returns (AddChannelResp);
    //修改主题房字段
    rpc UpdateChannelInfo(UpdateChannelInfoReq) returns (UpdateChannelInfoResp);
    //解散主题房
    rpc DismissChannel(DismissChannelReq) returns (DismissChannelResp);
    //不限分类获取列表
    rpc GetRecommendChannelList(GetRecommendChannelListReq) returns (GetRecommendChannelListResp);
    //指定分类获取列表
    rpc GetRecommendChannelListByTab(GetRecommendChannelListByTabReq) returns (GetRecommendChannelListByTabResp);
    //获取主题房信息
    rpc GetChannelByIds(GetChannelByIdsReq) returns (GetChannelByIdsResp);
    //解散某个分类
    rpc DismissTab(DismissTabReq) returns (DismissTabResp);

    //房间保持心跳
    rpc KeepChannelAlive(KeepChannelAliveReq) returns (KeepChannelAliveResp);

    //清除房间
    rpc DisappearChannel(DisappearChannelReq) returns (DisappearChannelResp);
}

message ChannelInfo {
    uint32 id = 1;
    uint32 tab_id = 2;
    int64 create_time = 3;
    uint32 male_count = 4;       //麦上男性人数
    uint32 female_count = 5;     //麦上女性人数
    bool is_recommend_channel = 6;
    uint32 creator = 7;         //创建者id
    uint32 total_count = 8;      //房间里总人数
}

message AddChannelReq {
    ChannelInfo channel = 1;
}

message AddChannelResp {

}

message UpdateChannelInfoReq {
    uint32 id = 1;
    uint32 tab_id = 2;
    uint32 male_count = 4;
    uint32 female_count = 5;
    uint32 total_count = 6;
}

message UpdateChannelInfoResp {

}

message DismissChannelReq {
    uint32 channel_id = 1;
}

message DismissChannelResp {
    bool dismiss = 1;
}

message GetRecommendChannelListLoadMore {
    uint32 num = 1;
}

message GetRecommendChannelListReq {
    uint32 uid = 1;
    uint32 limit = 2;
    GetRecommendChannelListLoadMore load_more = 3;
}

message GetRecommendChannelListResp {
    repeated ChannelInfo channel_list = 1;
    GetRecommendChannelListLoadMore load_more = 2;
}

message GetListByTabLoadMore {
    GetListByTabLoadMoreItem newborn = 1;
    GetListByTabLoadMoreItem sink = 2;
    GetListByTabLoadMoreItem big = 3;
}

message GetListByTabLoadMoreItem {
    uint64 cursor = 1;
    uint32 last_value = 2;
    uint32 last_index = 3;
    int64  last_count = 4;
    bool   the_end = 5;
}


message GetRecommendChannelListByTabReq {
    uint32 uid = 1;
    uint32 limit = 2;
    uint32 tab_id = 3;
    GetListByTabLoadMore load_more = 4;
    repeated uint32 except_channel_id = 5;
}

message GetRecommendChannelListByTabResp {
    repeated ChannelInfo channel_list = 1;
    GetListByTabLoadMore load_more = 2;
}

message GetChannelByIdsReq {
    repeated uint32 ids = 1;
}

message GetChannelByIdsResp {
    repeated ChannelInfo info = 1;
}

message DismissTabReq {
    uint32 tab_id = 1;
}

message DismissTabResp {

}

enum KeepAliveStatus {
    ALIVE = 0;
    DISCONNECTED = 1;
}

message KeepChannelAliveReq {
    uint32 channel_id = 1;
    KeepAliveStatus status = 2;
}

message KeepChannelAliveResp {
    bool is_alive = 1;
}

//message AcquireLockReq {
//    string type = 1;
//    string client_id = 2;
//    uint64 duration = 3;
//}
//
//message AcquireLockResp {
//    bool success = 1;
//}

message DisappearChannelReq {
    string client_id = 1;
    uint64 acquire_duration = 2;        //超时此时间段可重入

    message Timeout {
        uint64 timeout_duration = 1;    //创建超出该时间段清除
    }

    message Keepalive {
        uint64 Keepalive_duration = 1; //不保持心跳超出该时间段
        uint32 member_count = 2;        //房间人数大于此数
    }

    Timeout timeout_event = 10;
    Keepalive keepalive_event = 11;
}

message DisappearChannelResp {
    repeated uint32 channel_ids = 1;
}