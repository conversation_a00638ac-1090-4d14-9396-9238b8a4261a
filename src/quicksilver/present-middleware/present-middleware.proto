syntax = "proto3";

package present_middleware;
option go_package = "golang.52tt.com/protocol/services/present-middleware";

// 涂鸦礼物图
message DrawPresentPicture
{
  repeated PresentLine line_list = 1;
}

message PresentLine
{
  uint32 item_id = 1;      // 礼物id
  repeated PresentPoint point_list = 2;   // 礼物坐标
}

message PresentPoint
{
  float x = 1;
  float y = 2;
}

//赠送礼物
message PresentSendMsg
{
  PresentSendItemInfo item_info = 1;
  uint64 send_time = 2;
  uint32 channel_id = 3;
  uint32 send_uid = 4;
  string send_account = 5;
  string send_nickname = 6;
  uint32 target_uid = 7;
  string target_account = 8;
  string target_nickname = 9;
  string extend_json = 10;
}

message SendPresentReq
{
  uint32 target_uid = 1;
  uint32 item_id = 2;    // 礼物ID
  uint32 channel_id = 3;  // 通过房间赠送礼物时才需要填
  uint32 count = 4;          // 兼容旧版本，代码需要特殊处理，值为0时默认为1
  uint32 send_source = 5;  // 赠送时的点击来源 PresentSendSourceType 0.默认类型(兼容旧版本) 1.送礼转盘 2.礼物架（点击头像或成员列表） 3.语音球
  uint32 item_source = 6;  // 礼物来源 PresentSourceType
  uint32 source_id = 7;    // item_source对应的类型ID。比如如果是背包物品，那这里就是背包物品ID
  uint32 send_type = 8;   // 送礼类型 PresentSendType
  DrawPresentPicture draw_present_pic = 9;       // 涂鸦礼物图
  uint32 appId = 10; //设备的appId，上报用
  uint32 marketId = 11; // 设备的marketId，上报用
  uint32 send_uid = 12; // 送礼用户的uid
  ServiceCtrlInfo service_info = 13; //service_info，部分逻辑会用到
}

message SendPresentResp
{
  uint32 item_id = 1;
  PresentSendMsg msg_info = 2;
  uint32 member_contribution_added = 3;  // 增加的个人公会贡献
  uint32 count = 4;    // 礼物数量
  uint64 cur_tbeans = 5;    // 当前T豆余额
  uint32 item_source = 6;  // 礼物来源 PresentSourceType
  uint32 source_id = 7;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品ID
  uint32 source_remain = 8;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品的剩余数量
}

message PresentTargetUserInfo
{
  uint32 uid = 1;
  string account = 2;
  string name = 3;
}

message PresentBatchTargetInfo
{
  uint32 uid = 1;
  string account = 2;
  string nickname = 3;
  string extend_json = 4;
}

message PresentSendItemInfo
{
  uint32 item_id = 1;
  uint32 count = 2;
  uint32 show_effect = 3;  // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE
  uint32 show_effect_v2 = 4;  // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE_V2
  uint32 flow_id = 5;    // 流光id
  bool is_batch = 6;      // 0.普通送礼 1.批量送礼
  bool show_batch_effect = 7;
  uint32 send_type = 8;      // 送礼类型 PresentSendType
  DrawPresentPicture draw_present_pic = 9;  // 涂鸦礼物图
  uint32 dynamic_template_id = 10;   // 非全屏礼物动效模板id
}

// 批量送礼信息
message PresentBatchInfoMsg
{
  uint32 item_id = 1;
  uint32 total_item_count = 2;  // 送出的礼物总数量
  uint32 batch_type = 3;  // 批量送礼类型 PresentBatchSendType
  uint64 send_time = 4;
  uint32 channel_id = 5;
  uint32 send_uid = 6;
  string send_account = 7;
  string send_nickname = 8;
  string extend_json = 9;
  repeated PresentBatchTargetInfo target_list = 10;
  PresentSendItemInfo item_info = 11;
}

message ServiceCtrlInfo{
  string client_ip = 1;
  uint32 client_port = 2;
  string device_id = 3;
  uint32 client_type = 4;
  uint32 terminal_type = 5;
  uint32 client_id = 6;
  uint32 client_version = 7;
}

//批量赠送礼物
message BatchSendPresentReq
{
  uint32 item_id = 1;
  uint32 channel_id = 2;  // 通过房间赠送礼物时才需要填
  uint32 count = 3;  // 礼物数量
  uint32 send_source = 4;  // 赠送时的点击来源 PresentSendSourceType 0.默认类型(兼容旧版本) 1.送礼转盘 2.礼物架（点击头像或成员列表） 3.语音球
  uint32 item_source = 5;  // 礼物来源 PresentSourceType
  uint32 source_id = 6;    // 如果是背包里的礼物，就是背包礼物对应的唯一id
  uint32 batch_type = 7;  // 批量送礼类型 PresentBatchSendType
  uint32 send_type = 8;     // 送礼类型 PresentSendType
  DrawPresentPicture draw_present_pic = 9;       // 涂鸦礼物图
  uint32 app_id = 10; //base_req里的appId
  uint32 market_id = 11; // base_req里的marketId
  repeated uint32 target_uid_list = 12; // 收礼用户id列表
  uint32 send_uid = 13; // 送礼用户的uid
  ServiceCtrlInfo service_info = 14; //service_info，部分逻辑会用到
}

message BatchSendPresentResp
{
  PresentBatchInfoMsg msg_info = 1;
  uint64 cur_tbeans = 2; // 当前T豆余额
  uint32 item_source = 3;  // 礼物来源 PresentSourceType
  uint32 source_id = 4;    // 如果是背包里的礼物，就是背包礼物对应的唯一id
  uint32 source_remain = 5;    // 如果是背包里的礼物，就是背包礼物的剩余数量
  repeated PresentTargetUserInfo target_list = 6;  // 收礼对象列表
  PresentSendItemInfo item_info = 7;    // 礼物信息
}


//各种type，方便查阅

// 送礼来源类型
enum PresentSendSourceType
{
  E_SEND_SOURCE_DEFLAUTE = 0;         //默认类型(兼容旧版本)
  E_SEND_SOURCE_GIFT_TURNTABLE = 1;   //送礼转盘
  E_SEND_SOURCE_GIFT_SHELF = 2;       //礼物架
  E_SEND_SOURCE_SPEECH_BALL = 3;      //语音球
  E_SEND_SOURCE_DRAW_GIFT = 4;         //手绘
  E_SEND_SOURCE_MASKED_CALL = 5;      //语音匹配聊天
  E_SEND_SOURCE_IM = 6;                // IM
  E_SEND_SOURCE_OFFICIAL_CHANNEL = 7;                // 官频抽奖
}

//购买还是背包
enum PresentSourceType
{
  PRESENT_SOURCE_BUY = 0;      // 购买（红钻、T豆）
  PRESENT_SOURCE_PACKAGE = 1;    // 背包
  PRESENT_SOURCE_PACKAGE_FIRST = 2;    // 背包优先
}

// 送礼类型
enum PresentSendType
{
  PRESENT_SEND_NORMAL = 0;       // 普通送礼
  PRESENT_SEND_DRAW = 1;         // 涂鸦送礼
}

service PresentMiddleware
{
  rpc SendPresent(SendPresentReq) returns (SendPresentResp){
  }

  rpc BatchSendPresent(BatchSendPresentReq) returns (BatchSendPresentResp){
  }
}