syntax="proto3";

package channel.personalization;

import "google/protobuf/empty.proto";

service ChannelPersonalization {
    // Common API for all decoration types
    rpc GetUserDecorations( GetUserDecorationsReq ) returns ( GetUserDecorationsResp );
    rpc GrantDecorationToUser( GrantDecorationToUserReq ) returns ( GrantDecorationToUserResp );
    rpc ActivateUserDecoration( ActivateUserDecorationReq ) returns ( ActivateUserDecorationResp );

    // Specific API for Channel-Enter-Special-Effect
    rpc GetChannelEnterSpecialEffectConfig( google.protobuf.Empty ) returns ( ChannelEnterSpecialEffectConfig );

    // 运营后台用
    rpc AddUserDecorationConfig( AddUserDecorationConfigReq ) returns ( AddUserDecorationConfigResp );
    rpc DelUserDecorationConfig( DelUserDecorationConfigReq ) returns ( DelUserDecorationConfigResp );
    rpc UpdateUserDecorationConfig( UpdateUserDecorationConfigReq ) returns ( UpdateUserDecorationConfigResp );

    rpc GrantDecorationToUserV2( GrantDecorationToUserReq ) returns ( GrantDecorationToUserResp );
};

message ChannelEnterSpecialEffect {
    string name = 1;        // 特效名称
    string desc = 2;        // 特效描述
    string preview_url = 3; // 特效预览图地址
    uint32 min_level = 4;	// 0表示非等级坐骑
    EffectType effect_type = 5;   // 特效类型
}

message ChannelEnterSpecialEffectList {
    repeated ChannelEnterSpecialEffect special_effects = 1;
}

message ChannelEnterFunMessage {
    bool    random = 1;
    string  fixed_text = 2;
}

enum DecorationType {
    INVALID = 0;
    CHANNEL_ENTER_SPECIAL_EFFECT = 1;
    CHANNEL_ENTER_FUN_MESSAGE = 2;
}


//特效类型，0普通，1财富值特效，2贵族特效
enum EffectType {
    NORMAL = 0;
    RICH = 1;
    NOBILITY = 2;
}

// 个性化装饰
// NOTE: 一个用户拥有多个分类的个性化装饰，每个类别下都按照<uid, decoration_id>作为唯一索引
message Decoration {
    string id = 1;
    DecorationType type = 2;
    string ver = 3;

    DecorationDetail detail = 4;
}

message DecorationDetail {
    // 用oneof更合适，但是想直接把pb的结构存进mongo，序列化成问题
    // 根据DecorationType决定哪个字段填值
    ChannelEnterSpecialEffect   channel_enter_special_effect = 1;
    ChannelEnterFunMessage      channel_enter_fun_message = 2;
}

message UserDecoration {
    uint32 uid = 1;
    Decoration decoration = 2;

    uint64 effect_begin = 3;
    uint64 effect_end = 4;
    uint64 grant_at = 5;
    string grant_reason = 6;
    string grant_operator = 7;

    bool actived = 8;
    string order_id = 9;
}

message UserInfo {
    uint32 uid = 1;        // 用户id
    uint32 rich_level = 2; // 用户的财富等级
    uint32 nobility_level  = 3; // 用户的贵族等级
}

message GetUserDecorationsReq {
    UserInfo user_info = 1;             // 用户id
    bool only_effective = 2;            // 是否只查询有效的
    DecorationType decoration_type = 3; // 查询类型
}

message GetUserDecorationsResp {
    repeated UserDecoration user_decorations = 1;
}

message GrantDecorationToUserReq {
    UserDecoration user_decoration = 1;
    bool add_ttl_for_existing = 2;
}

message GrantDecorationToUserResp {

}

message ActivateUserDecorationReq {
    uint32 uid = 1;
    DecorationType decoration_type = 2;
    string decoration_id = 3;
}

message ActivateUserDecorationResp {

}

message AddUserDecorationConfigReq {
    Decoration decoration = 1;
}

message AddUserDecorationConfigResp {
}

message DelUserDecorationConfigReq {
    string id = 1;
    DecorationType type = 2;
}

message DelUserDecorationConfigResp {
}

message UpdateUserDecorationConfigReq {
    Decoration decoration = 1;
}

message UpdateUserDecorationConfigResp {
}

// 财富等级特效
message RichLevelSpecialEffect {
    uint32 min_level = 1;
    Decoration effect_info = 2;
}

// 贵族等级特效
message NobilityLevelSpecialEffect {
    uint32 min_level = 1;
    Decoration effect_info = 2;
}

message ChannelEnterSpecialEffectConfig {
    repeated RichLevelSpecialEffect rich_level_config = 1;  // 财富等级特效列表
    map<string, Decoration> non_rich_level_decorations = 2; // 非财富等级特效, id->Decoration
    repeated NobilityLevelSpecialEffect nobility_level_config = 3;  // 贵族等级特效列表
}
