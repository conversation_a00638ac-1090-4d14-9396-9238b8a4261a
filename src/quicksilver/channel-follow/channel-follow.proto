syntax = "proto3";

package channel_follow;
option go_package = "golang.52tt.com/protocol/services/channel-follow";


service ChannelFollow {
    rpc GetChannelFollowInfo (GetChannelFollowInfoReq) returns (GetChannelFollowInfoResp) { }
    rpc BatchGetChannelFollowInfo (BatchGetChannelFollowInfoReq) returns (BatchGetChannelFollowInfoResp) { }

    rpc UpdateFollowChannelAuthSwitch(UpdateFollowChannelAuthSwitchReq)returns(UpdateFollowChannelAuthSwitchResp){}
}

message GetChannelFollowInfoReq {

    uint32 uid = 1;
    uint32 target_uid = 2;
}

message GetChannelFollowInfoResp {
    RoomInfo info = 1;
}

message RoomInfo {
    uint32 room_id = 1;
    uint32 room_type = 2;
    uint32 bind_id = 3;
    bool is_lock = 4;
}


message BatchGetChannelFollowInfoReq {
    uint32 uid = 1;
    repeated uint32 target_uids = 2;
}

message BatchGetChannelFollowInfoResp {
     map<uint32,RoomInfo> infos = 1;
}


//FollowChannelAuthSwitchType
message UpdateFollowChannelAuthSwitchReq{
    uint32 uid = 1;
    uint32 follow_channel_auth_type = 2;
}

message UpdateFollowChannelAuthSwitchResp{

}