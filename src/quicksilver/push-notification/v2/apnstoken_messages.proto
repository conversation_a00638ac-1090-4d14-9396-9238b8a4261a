syntax="proto3";

import "apnsdef.proto";

option go_package = "golang.52tt.com/PushNotificationService/v2/gen-go/APNs;apns";

// namespace
package PushNotification.APNs;

message UnregisterDeviceTokenReq {
     string token       = 1;
     uint32 timestamp   = 2;
     uint32 uid         = 3;
}

message QueryDeviceTokensReq {
	repeated uint32 app_id_list = 1;
	repeated uint32 uid_list	= 2;
}

message QueryDeviceTokensResp {
	repeated UserDeviceToken user_device_token_list = 1;
}

message FullScanDeviceTokenContext {
    UserDeviceToken last_device_token  = 1;
}

message FullScanDeviceTokenReq {
    FullScanDeviceTokenContext current_context = 1;
    repeated uint32 app_id_list = 2;
    uint32 max_token_count = 3;
}

message FullScanDeviceTokenResp {
    FullScanDeviceTokenContext next_context = 1;
    repeated UserDeviceToken user_device_token_list = 2;
    bool finished = 3;
}


