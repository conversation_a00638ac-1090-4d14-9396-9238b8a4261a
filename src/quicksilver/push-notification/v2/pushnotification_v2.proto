syntax="proto3";

import "apnsdef.proto";

package PushNotification;

service PushNotification {
    rpc PushToUsers( stream PushToUsersReq ) returns ( stream PushToUsersResp ) {}
    rpc PushMulticast( stream PushMulticastReq ) returns ( stream PushMulticastResp ) {}

    rpc UnaryPushToUsers( PushToUsersReq ) returns ( PushToUsersResp ) {}
    rpc UnaryPushMulticast( PushMulticastReq ) returns ( PushMulticastResp ) {}

    // Reliable push
    rpc GetReliableProxyNotifications( GetReliableProxyNotificationsReq ) returns ( GetReliableProxyNotificationsResp ) {}
    rpc MessageReceivedAck( MessageReceivedAckReq ) returns ( MessageReceivedAckResp ) {}
}

// option go_package="golang.52tt.com/PushNotificationService/v2/gen-go;push";

message ProxyNotification {
    enum Type {
        INVALID = 0;
        NOTIFY = 1;                 // 使用CMD_Notify进行推送, 仅限TT使用(appId将被忽略), 暂时还不支持
        PUSH = 2;                   // 使用CMD_Push进行推送, 仅限TT使用(appId将被忽略), 消息体为ga.PushMessage
        TRANSMISSION_PUSH = 3;      // 使用CMD_TransmissionPush透传, 并用TransmissionPacket对payload进行包装
    }

    enum Policy {
        DEFAULT = 0;      // 默认策略, 只推送一次, 不保证到达
        RELIABLE = 1;     // 过期前保证送达
    }

    uint32 type = 1;
    bytes  payload = 2;
    Policy policy = 3;
    uint32 expire_time = 4; // unix timestamp, not ttl, default is server time + 3600s
}

message CompositiveNotification {
    uint32              sequence = 1;           // sequence id for this push operation
    repeated uint32     terminal_type_list = 2; // you have to specify at least one terminal
    uint32              app_id = 3;             // app id
    ProxyNotification   proxy_notification = 4; // tt proxy notification
    APNs.Notification   apns_notification = 5;  // APNs notification
    uint32              apns_frequence = 6;
    uint32              client_timestamp = 7;
}

message MulticastAccount {
    uint64 id = 1;
    string account = 2;
}

message ProxyPushResult {
    int32 result = 1;
    map<uint32, uint32> user_notify_counts_map = 2;
}

message PushToUsersReq {
    repeated uint32         uid_list = 1;
    CompositiveNotification notification = 2;
}

message PushMulticastReq {
    MulticastAccount            multicast_account = 1;
    CompositiveNotification     notification = 2;
    repeated uint32             skip_uids = 3;
    repeated MulticastAccount   multicast_accounts = 4; // if `multicast_accounts` isn't empty, `multicast_account` will be ignored.
}

message PushToUsersResp {
    uint32 sequence = 1;
    ProxyPushResult proxy_push_result = 2;
}

message PushMulticastResp {
    uint32 sequence = 1;
}

message GetReliableProxyNotificationsReq {
    uint32 uid = 1;
    uint32 sequence_begin = 2;
    uint32 sequence_end = 3;
    uint32 count = 4;       // the MAX count of notifications to get, 0 means no limit
}

message ReliableProxyNotification {
    uint32              sequence = 1;
    repeated uint32     terminal_type_list = 2; // you have to specify at least one terminal
    uint32              app_id = 3;             // app id
    ProxyNotification   proxy_notification = 4; // tt proxy notification
}

// Clients are responsible for filtering the notifications with terminal types or app ids
message GetReliableProxyNotificationsResp {
    repeated ReliableProxyNotification notifications = 1;
}

message MessageReceivedAckReq {
    uint32 uid = 1;
    repeated uint32 sequence_list = 2;
}

message MessageReceivedAckResp {
}

message ExpireKey {
    uint32 uid = 1;
    uint32 seq = 2;
}