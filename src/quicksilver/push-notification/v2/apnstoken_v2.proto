syntax="proto3";

import "apnsdef.proto";
import "apnstoken_messages.proto";

option go_package = "golang.52tt.com/PushNotificationService/v2/gen-go/APNs;apns";

// namespace
package PushNotification.APNs;

message RegisterDeviceTokenResp {}
message UnregisterDeviceTokenResp {
    uint32 deleted = 1;
}

service ApnsToken {
    /**
     * Register a device token
     */
    rpc RegisterDeviceToken( UserDeviceToken ) returns( RegisterDeviceTokenResp ) {}

    /**
     * Unregister a device token
     */
    rpc UnregisterDeviceToken( UnregisterDeviceTokenReq ) returns( UnregisterDeviceTokenResp ) {}

    /**
     * Query device tokens by uid list and app id list
     */
    rpc QueryDeviceTokens( QueryDeviceTokensReq ) returns (QueryDeviceTokensResp ) {}

    rpc FullScanDeviceToken( FullScanDeviceTokenReq ) returns ( FullScanDeviceTokenResp ) {}
}
