syntax="proto3";

// svrkit support
import "common/tlvpickle/skbuiltintype.proto";
import "apnsdef.proto";
import "apnstoken_messages.proto";

option go_package = "golang.52tt.com/PushNotificationService/v2/gen-go/APNs;apns";

// namespace
package PushNotification.APNs;

service ApnsToken {
    /**
     * Server listening port
     */
    option( tlvpickle.Magic ) = 15151;

    /**
     * Register a device token
     */
    rpc RegisterDeviceToken( DeviceToken ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u <uid> -t <token_hex>";
    }

    /**
     * Unregister a device token
     */
    rpc UnregisterDeviceToken( UnregisterDeviceTokenReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u <uid> -t <token_hex>";
    }

    /**
     * Query device tokens by uid list and app id list
     */
    rpc QueryDeviceTokens( QueryDeviceTokensReq ) returns (QueryDeviceTokensResp ) {
        option( tlvpickle.CmdID) = 3;
        option( tlvpickle.OptString ) = "u:a:";
        option( tlvpickle.Usage ) = "-u <uid_list> -a <app_id_list>";
    }

    rpc FullScanDeviceToken( FullScanDeviceTokenReq ) returns ( FullScanDeviceTokenResp ) {
        option( tlvpickle.CmdID) = 4;
        option( tlvpickle.OptString ) = "k:";
        option( tlvpickle.Usage ) = "-k <batch_count>";
    }
}