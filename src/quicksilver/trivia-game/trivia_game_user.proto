syntax="proto3";

package triviagame;

service User {
    rpc RegisterScore( RegisterScoreRequest ) returns( RegisterScoreResponse ) { }
    rpc GrantReward( GrantRewardRequest ) returns( GrantRewardResponse ) { }   
    rpc GetRewardLeaderboards( GetRewardLeaderboardsRequest ) returns( GetRewardLeaderboardsResponse ) { }
    rpc GetUserReward( GetUserRewardRequest ) returns( GetUserRewardResponse ) { }
    rpc GetUserRewardRank( GetUserRewardRankRequest ) returns( GetUserRewardRankResponse ) { }
}

message RegisterScoreRequest {
    uint32 user_id = 1;             // 用户id
    uint32 score = 2;               // 成绩（答对的题目数）
}

message RegisterScoreResponse {
    uint32 score_record = 1;        // 返回历史最高纪录
    bool refreshed_record = 2;      // 是否刷新纪录
}

message UserReward {
    uint32 user_id = 1;     // 用户id
    uint32 amount = 2;      // 金额(精确到：分)
}

message GrantRewardRequest {
    repeated uint32 user_id_list = 1;   // 用户id列表
    uint32 amount = 2;                  // 金额(精确到：分)
    string exclusive_key = 3;           // 排他key 
    string description = 4;             // 描述
    uint32 timestamp = 5;               // 时间戳
}

message GrantRewardResponse {
    repeated uint32 affected_user_id_list = 1;  // 生效的用户id列表(排除掉key重复的那些)
}

 enum LeaderboardsType {
    Historical = 0;     // 历史总榜
    Weekly = 1;         // 最近7天排行榜
}

message GetRewardLeaderboardsRequest {
    LeaderboardsType    type    = 1;    // 排行榜类型
    uint32              offset  = 2;    // 分页起始
    uint32              limit   = 3;    // 请求数量
    uint32              timestamp = 4;  // 周榜以该时间戳所在的周为准, 若为0, 则以服务器时间为准
}

message GetRewardLeaderboardsResponse {
    repeated UserReward user_reward_list = 1;   // 排行榜
    uint32 total_in_leaderboards = 2;           // 排行榜总人数
}

message GetUserRewardRankRequest {
    LeaderboardsType    type        = 1;    // 排行榜类型
    uint32              user_id     = 2;    // 用户id
    uint32              timestamp   = 3;    // 周榜以该时间戳所在的周为准, 若为0, 则以服务器时间为准
}

message GetUserRewardRankResponse {
    uint32              rank    = 1;    // 用户排名, 为0代表未上榜
}

message GetUserRewardRequest {
    uint32 user_id = 1;
}

message GetUserRewardResponse {
    uint32 reward = 1;
}

service Life {
    // 获取用户的生命数
    rpc GetUserLives( GetUserLivesRequest ) returns ( GetUserLivesResponse ) { }
    // 赠予用户生命
    rpc GrantLives( GrantLivesRequest ) returns ( GrantLivesResponse ) { }
    // 初始化复活周期
    rpc InitResurrectPeriod( InitResurrectPeriodRequest ) returns ( InitResurrectPeriodResponse ) { }
    // 尝试复活
    rpc Resurrect( ResurrectRequest ) returns ( ResurrectResponse ) { } 
    // 统计数据
    rpc GetResurrectStatistics( GetResurrectStatisticsRequest ) returns ( GetResurrectStatisticsResponse ) { } 
}

message GetUserLivesRequest {
    uint32 user_id = 1;             // 查询的用户id
}

message GetUserLivesResponse {
    uint32 lives = 2;               // 剩余生命数
}

message GrantLivesRequest {
    uint32 user_id = 1;             // 目标uid
    uint32 grant_lives = 2;         // 生命数
    string exclusive_key = 3;       // 排他key 
    string reason = 4;              // 原因
}

message GrantLivesResponse {
    uint32 lives = 1;               // 剩余生命值
}

message InitResurrectPeriodRequest {
    string resurrect_period = 1;    // 复活周期（通常是活动的id）
    uint32 resurrect_times = 2;     // 允许复活的次数
}

message InitResurrectPeriodResponse {   
}

message ResurrectRequest {
    string resurrect_period = 1;    // 复活周期（同一个复活周期的复活次数不能超过初始化时的上限）
    uint32 user_id = 2;             // 需要复活的用户
    string statistics_hint = 3;     // 用于统计的hint 
    string exclusive_key = 4;       // 排他key 
}

message ResurrectResponse {
    bool   resurrected = 1;                             // 是否成功复活
    uint32 lives = 2;                                   // 剩余生命
    uint32 remain_resurrect_times_in_this_period = 3;   // 本周期中还可以复活的次数 
}

message GetResurrectStatisticsRequest {
    string resurrect_period = 1;        // 复活周期
    string statistics_hint = 2;         // 统计hint
}

message GetResurrectStatisticsResponse {
    uint32 resurrent_user_count = 1;    // 复活人数
}
