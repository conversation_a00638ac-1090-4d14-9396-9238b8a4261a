syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/tagmatchrecommend";
package Tagmatchrecommend;


service Tagmatchrecommend {

    rpc GetHello (TagmatchrecommendReq) returns (TagmatchrecommendResp) {
    }

    rpc RegisterTagMatchRecommendEvent (RegisterTagMatchRecommendEventReq) returns (RegisterTagMatchRecommendEventResp){
    }
}

enum RegisterTagMatchRecommendType {
  RECOMMEND_TYPE_UNVALID =  0;
  RECOMMEND_TYPE_REGISTER = 1;          // 完成注册 --> 填完标签
  RECOMMEND_TYPE_OPENAPP = 2;           // 打开 app
}

message RegisterTagMatchRecommendEventReq {
  uint32 uid = 1;
  uint32 exe_time = 2;          // 发起时间
  uint32 exe_type = 3;          // 发起类型
}

message RegisterTagMatchRecommendEventResp {
}

message TagmatchrecommendReq {
    //string a = 1;
}

message TagmatchrecommendResp {
    //string b = 1;
}
