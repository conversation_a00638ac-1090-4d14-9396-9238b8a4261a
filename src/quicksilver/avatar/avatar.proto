syntax = "proto3";

package avatar;

service Avatar {
    rpc SaveAvatar(SaveAvatarReq) returns (SaveAvatarResp);
    rpc GetAvatar(GetAvatarReq) returns (GetAvatarResp);
}

message SaveAvatarReq {
    string account = 1;
    bytes big = 2;
    bytes small = 3;
}

message SaveAvatarResp { string version = 1; }

enum AvatarType {
    SMALL = 0;
    BIG = 1;
}

message GetAvatarReq {
    string account = 1;
    AvatarType avatar_type = 2;
    string version = 3;
}

message GetAvatarResp { bytes avatar = 1; }
