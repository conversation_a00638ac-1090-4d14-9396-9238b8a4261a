syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/golddiamonn";
package golddiamonn;


service GoldDiamond {
    rpc GetGuildInfo (GuildInfoReq) returns (GuildInfoRsp) {
    }

    rpc GetGuildList (GuildListReq) returns (GuildListRsp) {
    }

    rpc OpeGuildInfo (GuildInfoOpeReq) returns (GuildInfoOpeRsp) {
    }

    rpc GetGuildRooms (GoldRoomsReq) returns (GoldRoomsRsp) {
    }

    rpc GetGuildInitInfo (GuildInitInfoReq) returns (GuildInitInfoRsp) {
    }

    rpc GetGuildChannelIncomeInfo (GuildChannelIncomeReq) returns (GuildChannelIncomeRsp) {
    }

    rpc GetGuildTodayIncomeInfo (GuildTodayIncomeReq) returns (GuildTodayIncomeRsp) {
    }

    rpc GetGuildMonthTrendInfo (GuildMonthIncomeReq) returns (GuildMonthIncomeRsp) {
    }

    rpc GetUnSettlementDetals (GuildUnSettlementDetailReq) returns (GuildUnSettlementDetailRsp) {
    }

    rpc GetGuildIncomeDetails (GuildIncomeDetailReq) returns (GuildIncomeDetailRsp) {
    }

    rpc GetGuildUnSettlementSummary (GuildUnSettlementSummaryReq) returns (GuildUnSettlementSummaryRsp) {
    }

    rpc AwardGameDiamond (AwardDiamondReq) returns (AwardDiamondRsp) {
    }
}

enum GoldIncomeType {
    UNKNOW_TYPE = 0;
    GAME_INCOME = 1;
    GAME_SETTLEMENT = 4;
    ROOM_INCOME = 6;
    ROOM_SETTLEMENT = 7;
};

//查询
message GuildInfoReq {
    uint32 guild_id = 1;
}

message GuildInfoRsp {
    uint32 guild_id = 1;
    uint32 guild_type = 2;
}

message GuildListReq {
    uint32 offset = 1;
    uint32 limit = 2;
}

message GuildListRsp {
    repeated uint32 guild_ids = 1;
}

enum GuildInfoType {
    GUILD_INSERT = 0;
    GUILD_DELETE = 1;
}

//插入，删除
message GuildInfoOpeReq {
    GuildInfoType operation = 1;
    uint32 guild_id = 2;
    uint32 guild_type = 3;
}

message GuildInfoOpeRsp {
    uint32 guild_id = 1;
}

enum IncomeType {
    NONE_INDEX = 0;
    GAME_RECHARGE_REBATE = 1; 			// 通过游戏充值返利 获得金钻
    GUILD_SYS_TASK = 2;       			// 通过完成公会系统任务 获得金钻
    GUILD_OPE_TASK = 3;       			// 通过完成公会运营任务 获得金钻
    SETTLEMENT = 4;           			// 通过 系统结算 一般是扣除金钻
    GUILD_WELFARE_FIRST_JOIN = 5;    	// 通过参加会长福利活动——新增有效成员 获得金钻
    CHANNEL_SEND_GIFT = 6;				// 开黑房间送礼物 获得金钻
    CHANNEL_SETTLEMENT = 7;				// 房间收益金钻结算
}

message AwardDiamondReq {
    uint32 guild_id = 1;
    uint32 paid_uid = 2;
    uint64 bought_time = 3;
    IncomeType source_type = 4;
    int64 income = 5;
    string order_id = 6;
    string uniq_sign = 7;
    string desc = 8;
    string extand = 9;
}

message AwardDiamondRsp {
}

message GoldRoomsReq {
    uint32 guild_id = 1;
}

message GoldRoomsRsp {
    repeated uint32 channel_ids = 1;
}

message GuildInitInfoReq {
    uint32 guild_id = 1;
}

message DayTrendInfo {
    uint32 day = 1;
    int64 income = 2;
}

message RoomIdFee {
    uint32 roomid = 1;
    int64 fee = 2;
}

message GuildInitInfoRsp {
    int64 today_income = 1;
    int64 yestoday_income = 2;
    int64 thismonth_income = 3;
    int64 lastmonth_income = 4;
    repeated RoomIdFee roomid_fees = 5;
    repeated DayTrendInfo day_trend_info = 6;
    int64 server_time = 7;
    uint32 guild_type = 8;
    int32 day_qoq = 9;
    int32 month_qoq = 10;
}

enum RangeType {
    DAY_RANGE_TYPE = 0;
    MONTH_RANGE_TYPE = 1;
}

message GuildChannelIncomeReq {
    uint32 guild_id = 1;
    uint32 offset = 2;
    uint32 limit = 3;
    RangeType range_type = 4;
    int64 begin_time = 5;
    int64 end_time = 6;
    uint32 roomid = 7;
}

message StatIncomeInfo {
    int64 stat_time = 1;
    int64 members = 2;
    int64 incomes = 3;
    int64 fees = 4;

}

message GuildChannelIncomeRsp {
    int64 total_fees = 1;
    int64 total_incomes = 2;
    bool next_page = 3;
    repeated StatIncomeInfo stat_income_info = 4;
}

message GuildTodayIncomeReq {
    uint32 guild_id = 1;
}

message StatTodayRoomIncomeInfo {
    uint32 roomid = 1;
    int64 members = 2;
    int64 incomes = 3;
    int64 fees = 4;
}

message GuildTodayIncomeRsp {
    repeated StatTodayRoomIncomeInfo stat_room_info = 1;
}

message GuildMonthIncomeReq {
    uint32 guild_id = 1;
    int64 month = 2;
}

message DayTrendMoreInfo {
    int64 day = 1;
    int64 fee = 2;
    int64 income = 3;
    int64 members= 4;
}

message GuildMonthIncomeRsp {
    repeated DayTrendMoreInfo stat_trend_info = 1;
}

enum QueryType {
    NONE_TYPE = 0;
    ROOM_ID_TYPE = 1;
    PAID_UID_TYPE = 2;
}

message GuildUnSettlementDetailReq {
    uint32 guild_id = 1;
    QueryType query_type = 2;
    uint32 offset = 3;
    uint32 limit = 4;
}

message GuildUnSettlementDetail {
    uint32 roomid = 1;
    int64 fee = 2;
    int64 income = 3;
    uint32 paiduid = 4;
}

message GuildUnSettlementDetailRsp {
    repeated GuildUnSettlementDetail details = 1;
    bool next_page = 2;
}

message GuildIncomeDetailReq {
    uint32 guild_id = 1;
    uint32 offset = 2;
    uint32 limit = 3;
    int64 begin_time = 4;
    int64 end_time = 5;
    QueryType query_type = 6;
    uint32 paid_uid = 7;
    uint32 channel_id = 8;
}

message GuildIncomeDetail {
    uint32 roomid = 1;
    int64 fee = 2;
    int64 income = 3;
    uint32 paiduid = 4;
    int64 bought_time = 5;
}

message GuildIncomeDetailRsp {
    repeated GuildIncomeDetail income_details = 1;
    bool next_page = 2;
}

message GuildUnSettlementSummaryReq {
    uint32 guild_id = 1;
}

message GuildUnSettlementSummaryRsp {
    int64 paid_uid_cnt = 1;
    int64 total_income = 2;
    int64 total_fee = 3;
    int64 income_balance = 4;
    int64 begin_time = 5;
    int64 end_time = 6;
}
