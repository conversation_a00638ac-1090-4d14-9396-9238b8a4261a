syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Friend;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

// 好友信息存储结构
message stFriend {
	enum STATUS {
		NORMAL = 0;				// 正常状态
		BLACK = 1;				// 被我拉黑了
	}
	//enum FRIEND_SRC_TYPE {
	//	DEFAULT = 0;			// 默认，用户自己搜索账号添加
	//	GUILD_AUTO_ADD = 1;		// 工会会长，系统自动添加
	//}
	required uint32 uid = 1;
	required string user_account = 2;		// 用户的唯一账号
	required string phone = 3;				// 手机
	required string nick = 4;				// 昵称
	required string signature = 5;			// 个性签名
	required uint32 info_update_seq = 6;	// 资料更新的序列号
	required uint32 status = 7;				// 好友状态，是否被拉黑？
	required string face_md5 = 8;			// 头像md5
	optional uint32 friend_src_type = 9;	// 好友来源（通过账号搜索、会长自动添加，等）
	required bool is_delete = 10;			// 这个好友是否已经被我删除了
	required string cover_md5 = 11;			// 封面md5
	required string account_alias = 12;		// 账号别名
	required string nick_remark = 13;		// 昵称备注
	required uint32 sex = 14;				// 性别
	required uint32 user_type = 15;			// 用户类型
	required uint32 source_flag = 16;		// 来源标识,掩码.e.g: 同一公会、手机联系人
	required uint32 friend_add_time = 17;	// 加好友时间
	required bool is_star_marked = 18;		// 是否星标好友
	required bool is_banked = 19;			// 是否拉黑
	required uint32 last_update_time = 20;	// 最后更新时间
	optional uint64 create_or_delete_seq = 21;	// 好友关系创建或者删除时的seq
    optional uint32 nobility_level       = 22;  // 好友的贵族等级，用于好友列表红名
    optional string dynamic_face_md5 = 23; //动态头像md5
}

// 待验证的好友消息
message stFriendVerify {
	required uint32 uid = 1;
	required string user_account = 2;		// 用户的唯一账号
	required string phone = 3;				// 手机
	required string nick = 4;				// 昵称
	required string signature = 5;			// 个性签名
	required uint32 info_update_seq = 6;	// 资料更新的序列号
	required uint32 status = 7;				// 好友状态
	required string face_md5 = 8;			// 头像md5
	repeated string verify_msg_list = 9;	// 验证消息
	required uint32 last_update_time = 10;	// 最后更新时间（以后可能要清理旧数据之用）
	required uint32 user_type = 11;			// 用户类型
	required uint32 source_flag = 12;		// 来源标识,掩码.e.g: 同一公会、手机联系人
	required string account_alias = 13;		// 账号别名
	required uint32 sex = 14;				// 用户性别
	required string cover_md5 = 15;			// 用户封面
	optional uint32 friend_src_type = 16;   // 好友来源	详看contact.proto
}

// 好友列表中的群
message stFriendGroup {
	enum STATUS {
		NORMAL = 0;				// 正常状态
	}
	required uint32 group_id = 1;			// 工会id
	required uint32 status = 2;
	required bool is_deleted = 3;			// 是否已经被我删除了
	required uint32 info_update_seq = 4;	// 资料更新的序列号
	required bool is_show = 5;				// 是否显示
	optional uint32 mem_list_last_update_seq = 6;	// 成员列表有更新的seq
	optional uint32 type = 7;				// 群类型(NULL/0:临时群，4:TGroup)
}

message stPublicAccountSettings {
    optional uint32 message_notification = 1;
}

// 公众号
message stPublicAccount {
	required uint32 public_id = 1;			// 公众号id
	required string account = 2;			// 帐号
	required string name = 3;				// 名字
	required uint32 type = 4;				// 类型
	required uint32 info_update_seq = 5; 	// 资料更新的序列号
	required string face_md5 = 6;			// 头像md5
	required bool active = 7;				// deprecated
	required bool show_profile = 8;			// 是否能显示公众号详情(deprecated)
	required bool is_delete = 9;			// 删除标记
    optional stPublicAccountSettings settings = 10; // 用户的设置
    optional string intro = 11;             // 简介
    optional uint32 update_time = 12;       // 更新时间
}

//////////////////
// 添加好友验证
message AddFriendVerifyReq {
	required uint32 uid = 1;
	required uint32 friend_uid = 2;
	required stFriendVerify verify = 3;		// 验证信息等等
}

message AddFriendVerifyRsp {
	required int32 ret = 1;
}

//////////////////
// 添加好友
message AddFriendReq {
	required uint32 uid = 1;
	required uint32 friend_uid = 2;
	required stFriend friend_info = 3;
}

message AddFriendRsp {
	required int32 ret = 1;
}

//////////////////
// 更新好友信息
message UpdateFriendInfoReq {
	required uint32 uid = 1;
	required uint32 friend_uid = 2;
	required stFriend friend_info = 3;
}

message UpdateFriendInfoRsp {
	required int32 ret = 1;
}

//////////////////
// 更新好友验证信息
message UpdateFriendVerifyReq {
	required uint32 uid = 1;
	required uint32 friend_uid = 2;
	required stFriendVerify verify = 3;
}

message UpdateFriendVerifyRsp {
	required int32 ret = 1;
}

//////////////////
// 删除好友验证
message DelFriendVerifyReq {
	required uint32 uid = 1;
	required uint32 friend_uid = 2;
}

message DelFriendVerifyRsp {
	required int32 ret = 1;
}

//////////////////
// 删除好友
message DelFriendReq {
	required uint32 uid = 1;
	required uint32 friend_uid = 2;
}

message DelFriendRsp {
	required int32 ret = 1;
}

//////////////////
// 查询单个朋友
message GetOneFriendReq {
	required uint32 uid = 1;
	required uint32 friend_uid = 2;
}

message GetOneFriendRsp {
	required uint32 ret = 1;
	optional stFriend friend_info = 2;		// 如果有这个字段，则表示是朋友，没有则则不是。
	optional stFriendVerify verify_info = 3;// 如果有这个字段，则表示有好友验证
}

//////////////////
// 查询所有朋友信息
message GetAllFriendInfoOrVerifyReq {
	required uint32 uid = 1;
	optional bool getFriendInfo = 2;
	optional bool getVerifyInfo = 3;
	optional bool is_only_valid = 4; // 只要有效数据
}

message GetAllFriendInfoOrVerifyRsp {
	required int32 ret = 1;
	repeated stFriend friend_info_list = 2;		// 用户信息
	repeated stFriendVerify verify_info_list = 3;// 用户验证信息
}

//黑名单
message AddBlackListReq{
	required uint32 uid = 1;
	required uint32 target_uid = 2;
}

message DelBlackListReq{
	required uint32 uid = 1;
	required uint32 target_uid = 2;	
}

message GetBlackListReq{
	required uint32 uid = 1;
}

message GetBlackListResp{
	repeated uint32 uid_list = 2; 
}

//////////////////
// 根据seq查询好友资料
message GetFriendDataBySeqReq {
	required uint32 seq_id = 1;
}

message GetFriendDataBySeqResp {
	repeated stFriend friend_list = 1;
	repeated stFriendVerify verify_list = 2;
	repeated stFriendGroup group = 3;
	repeated stPublicAccount public_account_list = 4;
}

message UpdateFriendGroupReq {
	required stFriendGroup group_info = 1;
}

message UpdateFriendGroupResp {
}

message GetFriendGroupReq {
	required uint32 group_id = 1;
}

message GetFriendGroupResp {
	required stFriendGroup group_info = 1;
}

message GetAllMyGroupReq {
}

message GetAllMyGroupResp {
	repeated stFriendGroup group_list = 1;
}

//////////////////
// 公众号
//////////////////
message UpdatePublicAccountReq {
	required stPublicAccount public_detail = 1;
}

message UpdatePublicAccountResp {
}

message DeleteAllPublicAccountsReq {
}

message DeleteAllPublicAccountsResp {
	required uint64 deleted = 1;		// 被删除的数量
}

message GetPublicAccountReq {
	required uint32 public_id = 1;
}

message GetPublicAccountResp {
	// 如果用户关注了公众号, 则返回详情; 否则该项为空
	optional stPublicAccount public_detail = 1;
}

// 取用户关注的公众号列表
message GetUserSubscribingPublicAccountsReq {
	required bool include_deleted = 1;
}

message GetUserSubscribingPublicAccountsResp {
	repeated stPublicAccount public_account_list = 1;
}

//获得用户的公众号和好友群
message GetFriendGroupAndPublicAccountReq {
	required uint32 seq_id = 1;
}

message GetFriendGroupAndPublicAccountResp {
	repeated stFriendGroup group = 1;
	repeated stPublicAccount public_account_list = 2;
}

//////////////////
service Friend {
    option( tlvpickle.Magic ) = 14500;		// 服务监听端口号

    rpc AddFriendVerify(AddFriendVerifyReq) returns(AddFriendVerifyRsp) {
        option( tlvpickle.CmdID ) = 1;												// 命令号
        option( tlvpickle.OptString ) = "u:t:a:";									// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t <target_friend_uid> -a <account_name>";				// 测试工具的命令号帮助
    }

    rpc AddFriend(AddFriendReq) returns(AddFriendRsp) {
        option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "u:t:a:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t <target_friend_uid> -a <account_name>";				// 测试工具的命令号帮助
    }

    rpc UpdateFriendInfo(UpdateFriendInfoReq) returns(UpdateFriendInfoRsp) {
        option( tlvpickle.CmdID ) = 3;										// 命令号
        option( tlvpickle.OptString ) = "u:t:a:";									// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t <target_friend_uid> -a <account_name>";				// 测试工具的命令号帮助
    }

    rpc UpdateFriendVerify(UpdateFriendVerifyReq) returns(UpdateFriendVerifyRsp) {
        option( tlvpickle.CmdID ) = 4;										// 命令号
        option( tlvpickle.OptString ) = "u:t:a:";									// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t <target_friend_uid> -a <account_name>";				// 测试工具的命令号帮助
    }

    rpc DelFriendVerify(DelFriendVerifyReq) returns(DelFriendVerifyRsp) {
        option( tlvpickle.CmdID ) = 5;										// 命令号
        option( tlvpickle.OptString ) = "u:t:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t <target_friend_uid>";				// 测试工具的命令号帮助
    }

    rpc DelFriend(DelFriendReq) returns(DelFriendRsp) {
        option( tlvpickle.CmdID ) = 6;										// 命令号
        option( tlvpickle.OptString ) = "u:t:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t <target_friend_uid>";				// 测试工具的命令号帮助
    }

    rpc GetOneFriend(GetOneFriendReq) returns(GetOneFriendRsp) {
        option( tlvpickle.CmdID ) = 8;										// 命令号
        option( tlvpickle.OptString ) = "u:t:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t <target_friend_uid>";				// 测试工具的命令号帮助
    }

    rpc GetAllFriendInfoOrVerify(GetAllFriendInfoOrVerifyReq) returns(GetAllFriendInfoOrVerifyRsp) {
        option( tlvpickle.CmdID ) = 9;										// 命令号
        option( tlvpickle.OptString ) = "u:i:y:p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -i <getInfo> -y <getVerify>";	// 测试工具的命令号帮助
    }

    rpc GetFriendDataBySeq(GetFriendDataBySeqReq) returns(GetFriendDataBySeqResp) {
        option( tlvpickle.CmdID ) = 10;										// 命令号
        option( tlvpickle.OptString ) = "u:s:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -s <seq_id>";					// 测试工具的命令号帮助
    }

    rpc UpdateFriendGroup(UpdateFriendGroupReq) returns(UpdateFriendGroupResp) {
        option( tlvpickle.CmdID ) = 11;										// 命令号
        option( tlvpickle.OptString ) = "u:g:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <group_id>";					// 测试工具的命令号帮助
    }

    rpc GetFriendGroup(GetFriendGroupReq) returns(GetFriendGroupResp) {
        option( tlvpickle.CmdID ) = 12;										// 命令号
        option( tlvpickle.OptString ) = "u:g:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <group_id>";				// 测试工具的命令号帮助
    }

    rpc GetAllMyGroup(GetAllMyGroupReq) returns(GetAllMyGroupResp) {
        option( tlvpickle.CmdID ) = 13;										// 命令号
        option( tlvpickle.OptString ) = "u:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> ";				// 测试工具的命令号帮助
    }

    rpc UpdatePublicAccount(UpdatePublicAccountReq) returns(UpdatePublicAccountResp) {
    	option( tlvpickle.CmdID ) = 14;				// 命令号
        option( tlvpickle.OptString ) = "";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";				// 测试工具的命令号帮助
    }

    rpc DeleteAllPublicAccounts(DeleteAllPublicAccountsReq) returns(DeleteAllPublicAccountsResp) {
    	option( tlvpickle.CmdID ) = 15;				// 命令号
        option( tlvpickle.OptString ) = "u:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";		// 测试工具的命令号帮助
    }

	rpc GetPublicAccount(GetPublicAccountReq) returns (GetPublicAccountResp) {
    	option( tlvpickle.CmdID ) = 16;				// 命令号
        option( tlvpickle.OptString ) = "u:p:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -p <public_id>";		// 测试工具的命令号帮助
    }

	rpc GetUserSubscribingPublicAccounts (GetUserSubscribingPublicAccountsReq) returns (GetUserSubscribingPublicAccountsResp) {
    	option( tlvpickle.CmdID ) = 17;				// 命令号
        option( tlvpickle.OptString ) = "u:i:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -i <include_deleted>";		// 测试工具的命令号帮助
    }

	rpc AddBlackList (AddBlackListReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
    	option( tlvpickle.CmdID ) = 18;				// 命令号
        option( tlvpickle.OptString ) = "u:t:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t <target_uid>";		// 测试工具的命令号帮助
    }

    rpc GetBlackList (GetBlackListReq) returns(GetBlackListResp) {
     	option( tlvpickle.CmdID ) = 19;				// 命令号
        option( tlvpickle.OptString ) = "u:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";		// 测试工具的命令号帮助   
    }

	rpc DelBlackList (DelBlackListReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
    	option( tlvpickle.CmdID ) = 20;				// 命令号
        option( tlvpickle.OptString ) = "u:t:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t <target_uid>";		// 测试工具的命令号帮助
    }

	rpc GetFriendGroupAndPublicAccount(GetFriendGroupAndPublicAccountReq) returns(GetFriendGroupAndPublicAccountResp) {
		option( tlvpickle.CmdID ) = 22;										// 命令号
		option( tlvpickle.OptString ) = "u:s:";								// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u <uid> -s <seq_id>";					// 测试工具的命令号帮助
	}
}

