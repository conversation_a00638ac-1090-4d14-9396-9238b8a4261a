syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package entertainmentRecommendBack;

//begin common

enum ADV_TYPE
{
	AT_CHANNEL_FUNNY = 0;
	AT_CHANNEL_HOMEPAGE = 1;
}

enum UserCategory
{
	NewUser_Type = 0;
	OldUser_Type = 1;
	BothUser_Type = 2;   // 所有类型
	QuickEnterUser_Type = 3;   // 快速进房用户类型
}

enum ChannelCategory
{
	Activity_BIG = 0; //大活动房   官方活动房
	Activity_SAMLL = 1; //小活动房 公会活动房
	Used_Enter = 2; //曾经进入过
	Normal_Level = 3; //普通等级房
	Hot_Channel  = 4; //热门房
}

// 活动房间状态
enum ActivityChStatus
{
    ACT_STATUS_DEFAULT = 0;  // 默认全部
    ACT_STATUS_NOW_VALID = 1;  // 正在生效
    ACT_STATUS_SOON_VALID = 2;  // 未来生效
    ACT_STATUS_EXPIRE = 3;  //已过期
}

enum ChannelLevel
{
	Channel_Invalid = 0;
	Channel_Level_S = 1;
	Channel_Level_A = 2;
	Channel_Level_B = 3;
	Channel_Level_C = 4;
	Channel_Level_D = 5;
	Channel_Level_E = 6;
	Channel_Level_F = 7;
	Channel_Level_G = 8;
}

message TimeSection
{
	required uint32 begin_time = 1;
	required uint32 end_time = 2;
}

//
message ChannelCommonInfo
{
	required uint32 channel_id = 1;
	required uint32 tag_id = 2;
	optional string sub_tag = 3;
	repeated TimeSection sections = 4;
}

message ChannelCacheInfo
{
	required uint32 new_level = 1;
	required uint32 old_level = 2;
	optional uint32 quick_level = 3;
}

//预备库信息
message PrepareChannelInfo
{
	required uint32 channel_id = 1;
	required uint32 tag_id = 2;
	required uint32 new_level = 3; //新用户等级 enum ChannelLevel
	required uint32 old_level = 4; //旧玩家等级 enum ChannelLevel
	required uint32 new_start_time = 5;//新玩家开始时间戳
	required uint32 old_start_time = 6;//旧玩家开始时间戳
	optional string sub_tag = 7;
	optional uint32 modify_time = 8; //修改时间
	repeated TimeSection sections = 9; //sub_tag标签生效时间段
	optional uint32 quick_level = 10;  // 快速进房入口等级 enum ChannelLevel
	optional uint32 quick_start_time = 11;  // 快速进房入口等级开始时间戳
	optional uint32 channel_type = 12;  // 
}

//主持人信息
message McSimpleInfo
{
	required uint32 uid = 1;

	optional string account = 2;
	optional string nick_name = 3;
	optional string head_img = 4;
}

//推荐流房间信息
message ChannelRecommendSimpleInfo
{
	required uint32 channel_id = 1;
	required uint32 category = 2;  //enum ChannelCategory
	
	optional uint32 tag_id   = 3;  //tag_id
	optional string sub_tag  = 4;  //配置的标签，TOP10什么鬼的

	optional uint32 channel_level = 5; //enum ChannelLevel
	optional uint32 score = 6;

	optional string tag_name = 7;   
	optional string label = 8;   
}

message ActivityChannelInfo
{
	required uint32 channel_id = 1;
	required uint32 begin_time = 2;
	required uint32 end_time   = 3;
	required uint32 category   = 4; //enum Activity_BIG or Activity_SAMLL

	optional uint32 tag_id = 5; 
	optional string sub_tag = 6;
	optional uint32 id = 7;  // 唯一标识id
    optional uint32 act_status = 8;  // see enum ActivityChStatus
}
//end common

//备选库相关
message AddPrepareChannelReq
{
	repeated PrepareChannelInfo prepare_channel_list = 1;
}

message AddPrepareChannelResp
{
}

message GetPrepareChannelByChannelIDReq
{
	repeated uint32 channel_list = 1;
}

message GetPrepareChannelByChannelIDResp
{
	repeated PrepareChannelInfo prepare_channel_list = 1;
}

// 尽量不要使用GetPrepareChannelByPara接口 性能很低
// 可以使用 GetPrepareIDListByChannelType 或者 GetPrepareChannelListV2
message GetPrepareChannelByParaReq
{
	optional uint32 tag_id = 1;
	optional uint32 new_level  = 2;
	optional uint32 old_level  = 3;
	optional bool   is_begin = 4;
	optional bool   is_filter_live_all = 5;   // 是否过滤所有语音直播房 true:过滤 false:不过滤
	optional uint32 filter_live_by_ts = 6;  // 根据开播时间过滤语音直播 填 0:不过滤  非0:获取ts之后开播的房间
}

message GetPrepareChannelByParaResp
{
	repeated PrepareChannelInfo prepare_channel_list = 1;
}


message GetPrepareIDListByChannelTypeReq
{
	repeated uint32 channel_type_list = 1; // 是否指定房间类型
	optional uint32 offset  = 2;
	optional uint32 limit   = 3;
}

message GetPrepareIDListByChannelTypeResp
{
	repeated uint32 channel_id_list = 1;
}

message GetPrepareChannelListV2Req
{
	repeated uint32 channel_type_list = 1; // 是否指定房间类型
	optional bool   is_enable_cache   = 2; // 如果填true表示可以使用cache但是会有数据更新延时
	optional uint32 offset  = 3;
	optional uint32 limit   = 4;
}

message GetPrepareChannelListV2Resp
{
	repeated PrepareChannelInfo prepare_channel_list = 1;
}

//活动库相关
message AddActivityChannelReq
{
	repeated  ActivityChannelInfo activity_channel_list = 1;
}

message AddActivityChannelResp
{
}

message GetActivityChannelListReq
{
	optional bool is_begin   = 1;
	optional uint32 category   = 2;
}

message GetActivityChannelListResp
{
	repeated ActivityChannelInfo activity_channel_list = 1;
}

//活动房间支持配置多个时间段，给运营后台提供一个新的获取列表接口
message GetActivityChannelListV2Req {
    required uint32 offset = 1;    // 偏移量
    required uint32 limit = 2;     // 数量
    optional uint32 tag_id = 3;   
    optional uint32 channel_id = 4;   
    optional uint32 category = 5;  
    repeated uint32 cid_list = 6; 
    optional uint32 min_ts = 7;  // 时间段筛选
    optional uint32 max_ts = 8;   // 时间段筛选
    optional uint32 act_status = 9;  // see enum ActivityChStatus
}

message GetActivityChannelListV2Resp {
   repeated ActivityChannelInfo info_list = 1;
   required uint32 next_offset = 2;   // 下一次的偏移量
   required uint32 total_count = 3;   // 总数
}

message UpdateActivityChannelReq
{
	optional ActivityChannelInfo info = 1;
}

message UpdateActivityChannelResp
{
}


//tag配置
message ChannelTagConfigInfo
{
	required uint32 tag_id = 1;
	required string name = 2;
	optional string bk_color = 3;
	optional string icon = 4;
	optional uint32 tag_type = 5;
	optional string sub_tag = 6;
	optional string tag_url = 7; //开黑tag标识
	optional string bg_url = 8; //怼脸弹框背景图
	optional string welcome_text = 9; //弹窗欢迎语
	optional string button_color = 10; //按钮颜色
	repeated string multi_color = 11; // 渐变色

}

//多级tab配置
message ChannelMultiTagConfigInfo
{
	required string root_tag_name = 1;   //一级tag名称
	required uint32 root_tag_id = 2;     //一级tagId
	repeated ChannelTagConfigInfo sub_tag_list = 3;  //二级标签配置
}

message GetChannelTagConfigInfoReq
{
	optional uint32 register_at = 1;
}

message GetChannelTagConfigInfoResp
{
	repeated ChannelTagConfigInfo channel_tag_list = 1;
	repeated ChannelMultiTagConfigInfo channel_multi_tag_list = 2;
}

message GetChannelTagReq
{
	required uint32 channel_id = 1;
}

message GetChannelTagResp
{
	required ChannelTagConfigInfo tag_info = 1;
}

message BatchGetChannelTagReq
{
	repeated uint32 channel_id_list = 1;
}

message BatchGetChannelTagResp
{
	repeated ChannelTagConfigInfo channel_tag_list = 1;
}

message DeletePrepareChannelReq
{
	repeated uint32 channel_list = 1; //删除预备库中的房间ID列表
}

message DeletePrepareChannelResp
{
	
}

message DeleteActivityChannelReq
{
	repeated uint32 channel_list = 1; //删除活动的房间ID列表
	repeated uint32 id_list = 2;
}

message DeleteActivityChannelResp
{
	
}

message SetChannelCommonReq
{
	required uint32 channel_id = 1;
	required uint32 tag_id = 2;
	optional string sub_tag = 3;
	optional uint32 channel_type = 4; //活动房tag还是推荐房tag 推荐房0，活动房1
	repeated TimeSection effect_times = 5; //生效时间
}

message SetChannelCommonResp
{
	
}

message GetRecommendChannelReq
{
	required uint32 user_category = 1; //enum UserCategory
	required uint32 start = 2;
	required uint32 count = 3;
}

message GetRecommendChannelResp
{
	repeated ChannelRecommendSimpleInfo channel_list = 1;
	required bool is_end = 2; //是否到头了
}

message GetChannelByTagIdReq
{
	required uint32 tag_id = 1;
	required uint32 start = 2;
	required uint32 count = 3;
	required uint32 user_category = 4;
}

message GetChannelByTagIdResp
{
	repeated ChannelRecommendSimpleInfo channel_list = 1;
	required bool is_end = 2;
}


message HotChannelInfo
{
	required uint32 channel_id = 1;
	required uint32 channel_type = 2;	// HotChannelType
}

message GetHotChannelReq
{
	required uint32 start = 1;
	required uint32 count = 2;
	optional uint32 ts = 3;
	optional uint32 uid = 4;
}

message GetHotChannelResp
{
	repeated uint32 channel_id_list = 1;
	required bool reach_end = 2;
	repeated uint32 activity_id_list = 3;
}

message ChannelTagAdv
{
	required string pic_url = 1;
	required string adv_url = 2;
	optional uint32 score_idx = 3;
	optional uint32 app_id = 4;
	optional uint32 market_id = 5;
	optional uint32 start_time = 6;
	optional uint32 end_time = 7;
	optional uint32 client_type = 8;  //娱乐tab广告展示客户端类型 0: 所有客户端  1: Android  2:IOS 3:PC
}

message GetChannelTagAdvReq
{
	optional uint32 adv_type = 1; // ADV_TYPE
	optional uint32 app_id = 2;
	optional uint32 market_id = 3;
	optional bool   is_interior = 4; //该字段有值，表示是运营后台获取，区别于APP客户端获取
	optional uint32 client_type = 5;  //娱乐tab广告展示客户端类型 0: 所有客户端  1: Android  2:IOS 3:PC
}

message GetChannelTagAdvResp
{
	repeated ChannelTagAdv tag_adv_list = 1;
}

message AddChannelTagAdvReq
{
	required string pic_url = 1;
	required string adv_url = 2;
	optional uint32 adv_type = 3;
	optional uint32 score_idx = 4;
	optional uint32 market_id = 5;
	optional uint32 app_id = 6;
	optional string start_time = 7;
	optional string end_time = 8;
	optional uint32 client_type = 9;  //娱乐tab广告展示客户端类型 0: 所有客户端  1: Android  2:IOS
}

message AddChannelTagAdvResp
{
}

message DelChannelTagAdvReq
{
	required string pic_url = 1;
	optional uint32 market_id = 2;
	optional uint32 app_id = 3;
	optional uint32 adv_type = 4;
	optional uint32 client_type = 5;  //娱乐tab广告展示客户端类型 0: 所有客户端  1: Android  2:IOS
}

message DelChannelTagAdvResp
{
}

message AddChannelGiftReq
{
	required uint32 channel_id = 1;
	required uint32 gift = 2;	// HotChannelType
}

message AddChannelGiftResp
{
}

message GetRankChannelsReq{
    required uint32 rank_type = 1;
    required uint32 page = 2; //页数
    required uint32 count = 3; //每页数量
}

message GetRankChannelsResp{
    repeated uint32 channel_id_list = 1; //排行榜
    required bool reach_end = 2;
}

message ChannelTagRankItem
{
	required uint32 channel_id = 1;
	required uint32 tag_id = 2;
	required uint32 value = 3;
	required uint32 rank  = 4;
	required uint32 last_week_rank = 5;
	required string tag_name = 6;
}

message GetChannelTagRankReq
{
	required uint32 channel_id = 1;
	optional uint32 tag_id = 2;
}

message GetChannelTagRankResp
{
	repeated ChannelTagRankItem ranks = 1;
}

message BatchGetChannelRecommendLevelReq
{
    repeated uint32 cid_list = 1;
}

message ChannelRecommendLevel
{
    required uint32 cid = 1;
    required uint32 recommend_level = 2;        // UserCategory * 10 + ChannelLevel
}
message BatchGetChannelRecommendLevelResp
{
    repeated ChannelRecommendLevel recommend_level_list = 1;
}

//根据tagId获取快速进房的推荐房
message GetQuickRecommendChannelByTagIdReq
{
	required uint32 tag_id = 1;
}
message GetQuickRecommendChannelByTagIdResp
{
	optional uint32 channel_id = 1;
}

//每天的时间段
message DayTimeSection
{
    optional uint32 begin_ts = 1;
    optional uint32 end_ts = 2;
}

message DayTimeSectionList
{
    repeated DayTimeSection sect_list = 1;
}

enum ELivePrepareConfType 
{
    EPREPARE_CONF_PERMANENT = 0;  // 永久循环
    EPREPARE_CONF_CONSTANT = 1;   // 固定时间
}

message LiveQuickEnterPrepareInfo
{ 
    optional uint32 type = 1;   // see ELivePrepareConfType
    optional uint32 quick_level = 2; // 快速进房入口等级 enum ChannelLevel
    optional TimeSection sections = 3;  
    optional DayTimeSectionList day_section_list = 4; 
}

// 语音直播房预备库信息
message LivePrepareChannelInfo
{    
    required uint32 id = 1;         
    required uint32 uid = 2;             // 主播uid
	required uint32 channel_id = 3;
	required uint32 guild_id = 4;         // 工会id
	required uint32 tag_id = 5;          // 标签
	required uint32 level = 6;    // 等级  see ChannelLevel
	required TimeSection tag_sections = 7;    
	optional string sub_tag = 8;        // 特殊标签
    optional TimeSection sub_sections = 9; //sub_tag标签生效时间段
    optional DayTimeSectionList tag_section_list = 10; 
    optional uint32 conf_type = 11;  // see ELivePrepareConfType
    optional LiveQuickEnterPrepareInfo quick_enter_info = 12;    //语音直播快速进房入口等级配置
}


// 增加语音直播推荐库
message AddLivePrepareChannelReq
{
	repeated LivePrepareChannelInfo channel_list = 1;
}

message AddLivePrepareChannelResp
{
}

// 分页获取语音直播推荐库
message GetLivePrepareChannelListReq {
    required uint32 offset = 1;    // 偏移量
    required uint32 limit = 2;     // 数量
}

message GetLivePrepareChannelListResp {
   repeated LivePrepareChannelInfo info_list = 1;
   required uint32 next_offset = 2;   // 下一次的偏移量
   required uint32 total_count = 3;   // 总数
}

// 更新语音直播推荐库
message UpdateLivePrepareChannelReq {
   required uint32 id = 1; 
   optional uint32 level = 2;    
   optional TimeSection tag_sections = 3;    // tag生效时间段
   optional string sub_tag = 4;             // 特殊标签
   optional TimeSection sub_sections = 5;   //sub_tag标签生效时间段
   optional DayTimeSectionList tag_daysections = 6; 
   optional uint32 conf_type = 7;  // see ELivePrepareConfType
   optional LiveQuickEnterPrepareInfo quick_enter_info = 8;    //语音直播快速进房入口等级配置
}
message UpdateLivePrepareChannelResp {
}

// 删除语音直播推荐库
message DelLivePrepareChannelReq {
   repeated uint32 id_list = 1;
}
message DelLivePrepareChannelResp {
}

// 清空语音直播推荐库
message ClearLivePrepareChannelReq {
}
message ClearLivePrepareChannelResp {
}

// 语音直播自动加入推荐库
message AutoAddLivePrepareChannelReq
{
    required uint32 channel_id = 1;
    required uint32 tag_id = 2;
    required uint32 level = 3;
    required uint32 uid = 4;
}

message AutoAddLivePrepareChannelResp
{
}

enum SubTagQueryType
{
	Sub_Query_All = 0;   
	Sub_Query_No = 1;    //查询没有特殊标签的
	Sub_Query_Have = 2;   //查询有特殊标签的
}

// 多条件查询
message GetLivePrepareChannelByParaReq {
    repeated uint32 uids = 1;             // 主播uid
	optional uint32 guild_id = 2;         // 公会id
	optional uint32 tag_id = 3;          // 标签
	optional uint32 level = 4;        // 等级  see ChannelLevel
	optional uint32 sub_query_type = 5;    // see SubTagQueryType
	optional uint32 id = 6; 
	optional uint32 conf_type = 7;       // see ELivePrepareConfType 
	optional uint32 quick_conf_type = 8;  // see ELivePrepareConfType 
}

message GetLivePrepareChannelByParaResp {
   repeated LivePrepareChannelInfo info_list = 1;
}

message AutoGenReq {
}
message AutoGenResp {
}

// 战歌主播推荐位信息
message WarSongRecommendInfo {
   required uint32 channel_id = 1;
   required uint32 uid = 2;
   required string label_id = 3;
   required string intro = 4;
   required uint32 weight = 5;
   required uint32 update_ts = 6;
   required uint32 op_uid = 7;
   optional uint32 channel_type = 8;  // see channel_.proto ChannelType
}

message AddWarSongRecommendReq {
   repeated WarSongRecommendInfo info_list = 1;
}
message AddWarSongRecommendResp {
}

message GetWarSongRecommendListReq {
   required uint32 offset = 1;  
   required uint32 limit = 2;   
}
message GetWarSongRecommendListResp {
   repeated WarSongRecommendInfo info_list = 1;
   required uint32 next_offset = 2;   // 下一次的偏移量
   required uint32 total_count = 3;   // 总数
}

message UpdateWarSongRecommendReq {
   required WarSongRecommendInfo info = 1;
}
message UpdateWarSongRecommendResp {
}

message DelWarSongRecommendReq {
   required uint32 channel_id = 1;
}
message DelWarSongRecommendResp {
}

// 清空战歌位推荐库
message ClearWarSongRecommendReq {
}
message ClearWarSongRecommendResp {
}


// 战歌处房间推荐信息
message WarSongRecChannelInfo {
    required uint32 channel_id = 1;
    required uint32 uid = 2;
    required string intro = 3;  //简介
    required string bg_color = 4; 
    required string text_color = 5;
    required string content = 6;
    optional uint32 channel_type = 7;  // see channel_.proto ChannelType
}

message GetWarSongRecChannelListReq {
    optional bool is_new_ver = 1;      // 版本限制字段
}
message GetWarSongRecChannelListResp {
    repeated WarSongRecChannelInfo channel_list = 1;
    required bool is_end = 2;
}


// 房间推荐信息
message ChannelRecInfo {
   optional ChannelRecommendSimpleInfo rec_info = 1;
   optional ChannelTagConfigInfo tag_info = 2; 
}

// 批量获取房间的推荐信息
message BatGetChannelRecInfoReq{
   repeated uint32 cid_list = 1;
   optional uint32 uid = 2;  // 判断是否是曾经进过房间，需要填
}
message BatGetChannelRecInfoResp{
   repeated ChannelRecInfo channel_list = 1;
}

service EntertainmentRecommendBack {
	option( tlvpickle.Magic ) = 15666;		// 服务监听端口号

	rpc AddPrepareChannel ( AddPrepareChannelReq ) returns ( AddPrepareChannelResp )
	{
		option( tlvpickle.CmdID ) = 1;              // 命令号
		option( tlvpickle.OptString ) = "t:l:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-t<tagId> -l<channelidlist>";
	}

	// 根据房间ID 获取指定的推荐库的房间信息 列表
	rpc GetPrepareChannelByChannelID ( GetPrepareChannelByChannelIDReq ) returns ( GetPrepareChannelByChannelIDResp )
	{
		option( tlvpickle.CmdID ) = 2;              // 命令号
		option( tlvpickle.OptString ) = "u:l:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -l<channellist>";
	}

	// 获取全量推荐库房间信息 列表
	// 不建议使用该接口，由于数据量巨大导致性能太低
	// 如果仅是一般查询建议改用 GetPrepareIDListByChannelType 或者 GetPrepareChannelListV2
	rpc GetPrepareChannelByPara ( GetPrepareChannelByParaReq ) returns ( GetPrepareChannelByParaResp )
	{
		option( tlvpickle.CmdID ) = 3;              // 命令号
		option( tlvpickle.OptString ) = "t:n:o:a:b:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-t<tag_id> -n<new_level> -o<old_level> -a<is_filter_live_all> -b<filter_live_by_ts>";
	}

	// 添加活动库房间
	rpc AddActivityChannel ( AddActivityChannelReq ) returns ( AddActivityChannelResp )
	{
		option( tlvpickle.CmdID ) = 4;              // 命令号
		option( tlvpickle.OptString ) = "x:y:t:s:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";
	}

	// 获取活动库房间信息 列表
	rpc GetActivityChannelList ( GetActivityChannelListReq ) returns ( GetActivityChannelListResp )
	{
		option( tlvpickle.CmdID ) = 5;              // 命令号
		option( tlvpickle.OptString ) = "u:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid>";
	}

	rpc GetChannelTagConfigInfo ( GetChannelTagConfigInfoReq ) returns ( GetChannelTagConfigInfoResp )
	{
		option( tlvpickle.CmdID ) = 6;              // 命令号
		option( tlvpickle.OptString ) = "u:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid>";
	}

	// 删除推荐库房间
	rpc DeletePrepareChannel ( DeletePrepareChannelReq ) returns ( DeletePrepareChannelResp )
	{
		option( tlvpickle.CmdID ) = 7;              // 命令号
		option( tlvpickle.OptString ) = "u:x:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -x<channelId>";
	}

	// 删除活动库房间
	rpc DeleteActivityChannel ( DeleteActivityChannelReq ) returns ( DeleteActivityChannelResp )
	{
		option( tlvpickle.CmdID ) = 8;              // 命令号
		option( tlvpickle.OptString ) = "u:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid>";
	}

	rpc SetChannelCommon ( SetChannelCommonReq ) returns ( SetChannelCommonResp )
	{
		option( tlvpickle.CmdID ) = 10;              // 命令号
		option( tlvpickle.OptString ) = "u:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid>";
	}

	rpc GetRecommendChannel ( GetRecommendChannelReq ) returns( GetRecommendChannelResp ) {
		option( tlvpickle.CmdID ) = 11;              // 命令号
		option( tlvpickle.OptString ) = "u:t:s:n:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -t<type> -s<start> -n<count>";
	}

	rpc GetChannelByTagId ( GetChannelByTagIdReq ) returns ( GetChannelByTagIdResp )
	{
		option( tlvpickle.CmdID ) = 12;              // 命令号
		option( tlvpickle.OptString ) = "u:t:s:n:g";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -t<tagid> -s<start> -n<count> -g<category>";
	} 

	rpc GetChannelTag ( GetChannelTagReq ) returns ( GetChannelTagResp )
	{
		option( tlvpickle.CmdID ) = 13;              // 命令号
		option( tlvpickle.OptString ) = "u:t:s:n:g";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -t<tagid> -s<start> -n<count> -g<category>";
	}

	rpc BatchGetChannelTag ( BatchGetChannelTagReq ) returns ( BatchGetChannelTagResp )
	{
		option( tlvpickle.CmdID ) = 14;              // 命令号
		option( tlvpickle.OptString ) = "u:t:s:n:g";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid> -t<tagid> -s<start> -n<count> -g<category>";
	}

	rpc GetHotChannel ( GetHotChannelReq ) returns ( GetHotChannelResp )
	{
		option( tlvpickle.CmdID ) = 15;              // 命令号
		option( tlvpickle.OptString ) = "u:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u<uid>";
	}

	rpc GetChannelTagAdv (GetChannelTagAdvReq) returns (GetChannelTagAdvResp){
		option( tlvpickle.CmdID ) = 16;              // 命令号
		option( tlvpickle.OptString ) = "a:i:m:t:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-a <adv_type> -i <app_id> -m <market_id> -t <client_type>";    // 测试工具的命令号帮助
	}

	rpc AddChannelGift (AddChannelGiftReq) returns (AddChannelGiftResp){
		option( tlvpickle.CmdID ) = 17;              // 命令号
		option( tlvpickle.OptString ) = "x:g:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
	}

	rpc AddChannelTagAdv (AddChannelTagAdvReq) returns (AddChannelTagAdvResp){
		option( tlvpickle.CmdID ) = 18;              // 命令号
		option( tlvpickle.OptString ) = "p:d:t:i:m:s:b:e:n:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-p <picl> -d<adv> -t<type> -i<app> -m<market> -s<score> -b<start_time> -e<end_time> -n<client_type>";    // 测试工具的命令号帮助
	}

	rpc DelChannelTagAdv (DelChannelTagAdvReq) returns (DelChannelTagAdvResp){
		option( tlvpickle.CmdID ) = 19;              // 命令号
		option( tlvpickle.OptString ) = "p:i:m:t:n:";   // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-p <picl> -i<app> -m<market> -t<type> -n<client_type>";    // 测试工具的命令号帮助
	}

	rpc GetRankChannels ( GetRankChannelsReq ) returns ( GetRankChannelsResp ){
		option( tlvpickle.CmdID ) = 20;                                      
		option( tlvpickle.OptString ) = "t:p:n:";                         
		option( tlvpickle.Usage ) = "-t<rank_type> -p<page> -n<num>";
	}

    rpc GetChannelTagRanks ( GetChannelTagRankReq ) returns ( GetChannelTagRankResp ){
        option( tlvpickle.CmdID ) = 21;                                      
        option( tlvpickle.OptString ) = "x:";                         
        option( tlvpickle.Usage ) = "-x<channelId>";
    }
    rpc BatchGetChannelRecommendLevel ( BatchGetChannelRecommendLevelReq ) returns ( BatchGetChannelRecommendLevelResp ){
        option( tlvpickle.CmdID ) = 22;                                      
        option( tlvpickle.OptString ) = "x:";                         
        option( tlvpickle.Usage ) = "-x<channelId>";
    }
    rpc GetQuickRecChannelByTagId ( GetQuickRecommendChannelByTagIdReq ) returns ( GetQuickRecommendChannelByTagIdResp ){
        option( tlvpickle.CmdID ) = 23;                                      
        option( tlvpickle.OptString ) = "t:n:";                         
        option( tlvpickle.Usage ) = "-t<tagId> -n<test_count>";
    }
    rpc AddLivePrepareChannel ( AddLivePrepareChannelReq ) returns ( AddLivePrepareChannelResp ){
        option( tlvpickle.CmdID ) = 24;                                      
        option( tlvpickle.OptString ) = "i:d:t";                         
        option( tlvpickle.Usage ) = "-i <id> -d <channelId> -t <tagId>";
    }

    rpc GetLivePrepareChannelList ( GetLivePrepareChannelListReq ) returns ( GetLivePrepareChannelListResp ){
        option( tlvpickle.CmdID ) = 25;                                      
        option( tlvpickle.OptString ) = "i:d:";                         
        option( tlvpickle.Usage ) = "-i <offset> -d <limit>";
    }

    rpc DelLivePrepareChannel ( DelLivePrepareChannelReq  ) returns ( DelLivePrepareChannelResp ){
        option( tlvpickle.CmdID ) = 26;                                      
        option( tlvpickle.OptString ) = "i:";                         
        option( tlvpickle.Usage ) = "-i <id_list split by ,>";
    }

    rpc AutoAddLivePrepareChannel ( AutoAddLivePrepareChannelReq ) returns ( AutoAddLivePrepareChannelResp ){
        option( tlvpickle.CmdID ) = 27;                                      
        option( tlvpickle.OptString ) = "i:t:l:";                         
        option( tlvpickle.Usage ) = "-i <channel_id> -t <tag_id> -l <level>";
    }

    rpc  GetLivePrepareChannelByPara (  GetLivePrepareChannelByParaReq ) returns (  GetLivePrepareChannelByParaResp ){
        option( tlvpickle.CmdID ) = 28;                                      
        option( tlvpickle.OptString ) = "u:g:t:l:h:";                         
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -t <tag_id> -l <level> -h <sub_query_tag>";
    }

    rpc UpdateLivePrepareChannel (  UpdateLivePrepareChannelReq ) returns (  UpdateLivePrepareChannelResp ){
        option( tlvpickle.CmdID ) = 29;                                      
        option( tlvpickle.OptString ) = "i:";                         
        option( tlvpickle.Usage ) = "-i <id>";
    }

    rpc AutoGen (  AutoGenReq ) returns (  AutoGenResp ){
        option( tlvpickle.CmdID ) = 30;                                      
        option( tlvpickle.OptString ) = "u:";                         
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc AddWarSongRecommend ( AddWarSongRecommendReq ) returns (  AddWarSongRecommendResp ){
        option( tlvpickle.CmdID ) = 31;                                      
        option( tlvpickle.OptString ) = "u:";                         
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc GetWarSongRecommendList ( GetWarSongRecommendListReq ) returns ( GetWarSongRecommendListResp ){
        option( tlvpickle.CmdID ) = 32;                                      
        option( tlvpickle.OptString ) = "u:";                         
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc UpdateWarSongRecommend ( UpdateWarSongRecommendReq ) returns ( UpdateWarSongRecommendResp ){
        option( tlvpickle.CmdID ) = 33;                                      
        option( tlvpickle.OptString ) = "u:";                         
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc DelWarSongRecommend ( DelWarSongRecommendReq ) returns ( DelWarSongRecommendResp ){
        option( tlvpickle.CmdID ) = 34;                                      
        option( tlvpickle.OptString ) = "u:";                         
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc GetWarSongRecChannelList ( GetWarSongRecChannelListReq ) returns ( GetWarSongRecChannelListResp ){
        option( tlvpickle.CmdID ) = 35;                                      
        option( tlvpickle.OptString ) = "u:";                         
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc GetActivityChannelListV2 ( GetActivityChannelListV2Req ) returns ( GetActivityChannelListV2Resp ){
        option( tlvpickle.CmdID ) = 36;                                      
        option( tlvpickle.OptString ) = "u:";                         
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc UpdateActivityChannel ( UpdateActivityChannelReq ) returns ( UpdateActivityChannelResp ){
        option( tlvpickle.CmdID ) = 37;                                      
        option( tlvpickle.OptString ) = "u:";                         
        option( tlvpickle.Usage ) = "-u <uid>";
    }

	// 根据房间类型 获取推荐库的房间ID 列表
	// 但是直播间类型的房间太多，不建议一次获取
    rpc GetPrepareIDListByChannelType ( GetPrepareIDListByChannelTypeReq ) returns ( GetPrepareIDListByChannelTypeResp ){
        option( tlvpickle.CmdID ) = 38;                                      
        option( tlvpickle.OptString ) = "u:x:s:l:";                         
        option( tlvpickle.Usage ) = "-u <uid> -x <channel type> -s <offset> -l <limit> ";
    }

	// 根据房间类型 获取推荐库的房间 列表
	// 但是直播间类型的房间太多，不建议一次获取
    rpc GetPrepareChannelListV2 ( GetPrepareChannelListV2Req ) returns ( GetPrepareChannelListV2Resp ){
        option( tlvpickle.CmdID ) = 39;                                      
        option( tlvpickle.OptString ) = "u:x:s:l:b:";                         
        option( tlvpickle.Usage ) = "-u <uid> -x <channel type> -s <offset> -l <limit> -b <is enable cache>";
    }
    
    rpc ClearLivePrepareChannel ( ClearLivePrepareChannelReq ) returns ( ClearLivePrepareChannelResp ){
        option( tlvpickle.CmdID ) = 40;                                      
        option( tlvpickle.OptString ) = "u:";                         
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc ClearWarSongRecommend ( ClearWarSongRecommendReq ) returns ( ClearWarSongRecommendResp ){
        option( tlvpickle.CmdID ) = 41;                                      
        option( tlvpickle.OptString ) = "u:";                         
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc BatGetChannelRecInfo ( BatGetChannelRecInfoReq ) returns ( BatGetChannelRecInfoResp ){
        option( tlvpickle.CmdID ) = 43;                                      
        option( tlvpickle.OptString ) = "u:";                         
        option( tlvpickle.Usage ) = "-u <uid>";
    }
    
}

