syntax="proto3";

package FindFriends;

service FindFriends {
    /* 扩圈相关 */
    rpc GetUser( GetUserRequest ) returns( GetUserResponse ) {}
    rpc CreateOrUpdateUser( CreateOrUpdateUserRequest ) returns( CreateOrUpdateUserResponse ) {}
    rpc UpdatePhoto( UpdatePhotoRequest ) returns( UpdatePhotoResponse ) {}
    rpc ReportActive( ReportActiveRequest ) returns( ReportActiveResponse ) {}

    rpc GetRecommendUsers( GetRecommendUsersRequest ) returns( GetRecommendUsersResponse ) {} 
    
    rpc Like( LikeRequest ) returns( LikeResponse ) {}
    rpc CheckLiked( CheckLikedRequest ) returns( CheckLikedResponse ) {}
    rpc CheckBeenLiked( CheckBeenLikedRequest ) returns( CheckBeenLikedResponse ) {}
    rpc GetRecentLikedMe( GetRecentLikedMeRequest ) returns( GetRecentLikedMeResponse ) {}
    rpc ClearMutualLikesMe( ClearMutualLikesMeRequest ) returns( ClearMutualLikesMeResponse ) {}
    rpc GetTodayLikedCount( GetTodayLikedCountRequest ) returns( GetTodayLikedCountResponse ) {}

    rpc AddAFKLiked( AddAFKLikedRequest ) returns( AddAFKLikedResponse ) {}
    rpc ClearAFKLiked( ClearAFKLikedRequest ) returns( ClearAFKLikedResponse ) {}

    rpc GetFreeLikeQuota( GetFreeLikeQuotaRequest ) returns( GetFreeLikeQuotaResponse ) {}
    rpc GrantFreeLikeQuota( GrantFreeLikeQuotaRequest ) returns( GrantFreeLikeQuotaResponse ) {}

    /* 快速匹配 */
    rpc StartQuickMatch( StartQuickMatchRequest ) returns( StartQuickMatchResponse) { }
    rpc CancelQuickMatch( CancelQuickMatchRequest ) returns( CancelQuickMatchResponse) { }
    rpc GetQuickMatchStatistics( GetQuickMatchStatisticsRequest ) returns( GetQuickMatchStatisticsResponse ) { }
    rpc QuickMatchKeepAlive( QuickMatchKeepAliveRequest ) returns( QuickMatchKeepAliveResponse ) { }
    rpc PunishQuickMatchDeserter( PunishQuickMatchDeserterRequest ) returns( PunishQuickMatchDeserterResponse ) { }
}

message Location {
    uint32 ad_code = 1;
    string country = 2;
    string province = 3;
    string city = 4;
    string district = 5;
}


enum Gender {
    Gender_UNKNOWN = 0;
    Gender_FEMALE = 1;
    Gender_MALE = 2;
}

enum GenderFilter {
    GenderFilter_ZERO = 0;       // 0值, 不设置表示不更新
    GenderFilter_MALE_ONLY = 1;
    GenderFilter_FEMALE_ONLY = 2;
    GenderFilter_UNRESTRICTED = 3;
}

message UserInfo {
    uint32              user_id = 1;
    repeated string     photo_urls = 2;
    string              voice_url = 3;
    Location            location = 4;
    GenderFilter        gender_filter = 5;
	repeated string     playing_games = 6;

    enum AutoPlayVoice {
        AutoPlayVoice_UNSPECIFIC = 0;
        AutoPlayVoice_ENABLED = 1;
        AutoPlayVoice_DISABLED = 2;
    }
	AutoPlayVoice   auto_play_voice = 7;
    Gender          gender = 8;
    uint32          voice_duration = 9;
}

message GetUserRequest {
    uint32 user_id = 1;
}

message GetUserResponse {
    UserInfo user_info = 1;
}

message CreateOrUpdateUserRequest {
    UserInfo user_info = 1;
    bool     clear_playing_games = 2;
}

message CreateOrUpdateUserResponse {
    UserInfo user_info = 1;
}

message UpdatePhotoRequest {
    uint32 user_id = 1;
    uint32 index = 2;
    string photo_url = 3;   // an empty values means deletion
}

message UpdatePhotoResponse {

}


message GetRecommendUsersRequest {
    uint32          user_id = 1;
    repeated uint32 filter_uids = 2;    // uid list should be filtered
    uint32          count = 3;
    GenderFilter    gender_filter = 4;
    Location        location = 5;
    string          session_id = 6;
}

message GetRecommendUsersResponse {
    repeated UserInfo users = 1;
}

message LikeRequest {
    uint32          user_id = 1;
    repeated uint32 throw_uids = 2;
    uint32          like_uid = 3;
}

message LikeResponse {
    bool    like_is_effectual = 1;
    bool    is_free = 2;
    uint32  free_like_quota = 3;
}

message CheckLikedRequest {
    uint32 from_user_id = 1;
    uint32 target_user_id = 2;
}

message CheckLikedResponse {
    bool liked = 1;
}

message CheckBeenLikedRequest {
    uint32          user_id = 1;
    repeated uint32 test_uid_set = 2;
}

message CheckBeenLikedResponse {
    repeated uint32 liked_by_uid_set = 1;
}

// 清理相互的被喜欢状态
message ClearMutualLikesMeRequest {
    uint32 user_id_A = 1;
    uint32 user_id_B = 2;
}

message ClearMutualLikesMeResponse {
}

message GetTodayLikedCountRequest {
    uint32 user_id = 1;
    uint32 timestamp = 2;
}

message GetTodayLikedCountResponse {
    uint32 count = 1;
}

message GetFreeLikeQuotaRequest {
    uint32 user_id = 1;
    uint32 timestamp = 2;
}

message GetFreeLikeQuotaResponse {
    uint32 quota = 1;
}

message GrantFreeLikeQuotaRequest {
    uint32 user_id = 1;
    uint32 timestamp = 2;
    uint32 quota = 3;
}

message GrantFreeLikeQuotaResponse {
    bool    granted = 1;
    uint32  quota = 2;
}

message AddAFKLikedRequest {
    uint32 user_id = 1;
}

message AddAFKLikedResponse {
    uint32 liked_count = 1;
}

message ClearAFKLikedRequest {
    uint32 user_id = 1;
}

message ClearAFKLikedResponse {
    bool changed = 1;
}

message ReportActiveRequest {
    uint32 user_id = 1;
} 

message ReportActiveResponse {
    
}

message GetRecentLikedMeRequest {
    uint32 user_id = 1;
    uint32 count = 2;
}

message GetRecentLikedMeResponse {
    repeated uint32 user_id_list = 1;
}


message QuickMatchGame {
    string              game_name = 1;
    map<string, uint32> options = 2;
    uint32              game_id = 3;
    string              match_message = 9;    // 匹配成功的需要推送的频道消息
}

message MatchedPeerInfo {
    uint32 user_id = 1;
    uint32 user_gender = 2;
    string session_id = 3;
}

message StartQuickMatchRequest {
    uint32                  user_id = 1;
    string                  game_name = 2;              // deprecated, use `game_list` instead
    map<string, uint32>     options = 3;                // deprecated, use `game_list` instead
    uint32                  duration = 4;
    uint32                  user_gender = 5;
    repeated QuickMatchGame game_list = 6;              // support multiple game matching
    uint32                  supplement_channel_id = 7;  // 补位频道ID
    uint32                  supplement_num = 8;	        // 补位人数
}

message StartQuickMatchResponse {
    string          session_id = 1;
    MatchedPeerInfo matched_peer_info = 2;
    uint32          sequence_in_queue = 3;
}

message CancelQuickMatchRequest {
    uint32 user_id = 1;
    string session_id = 2;
}

message CancelQuickMatchResponse {
}

message GetQuickMatchStatisticsRequest {
    uint64 begin_time = 1;
    uint64 end_time = 2;
}

message GetQuickMatchStatisticsResponse {
    uint32 current_session_count = 1;   // 正在匹配的数量
    uint32 total_started_match = 2;     // 统计时间段内参与统计的数量
}

message QuickMatchKeepAliveRequest {
    uint32 user_id = 1;
    string session_id = 2;
}

message QuickMatchKeepAliveResponse {
    bool   is_alive = 1;
    uint32 rank = 2;
}

message PunishQuickMatchDeserterRequest {
    uint32 user_id = 1;
    uint32 channel_id = 2;
    uint32 online_seconds = 3;
}

message PunishQuickMatchDeserterResponse {
    
}