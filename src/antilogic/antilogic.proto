syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package antilogic;


// enum class {}
message UserBehaviorType{
    enum VALUE{
        LOGIN = 1;
    }
}

//login行为
message UserLogin{
    enum LOGIN_TYPE{
        REG = 1;
        AUTH_AUTO = 2;
        AUTH_MANUAL = 3;
    }
    required uint32 login_type = 1;
    required uint32 client_type = 2;
    required uint32 login_at = 3;
    required string device_id = 4;    
    optional string imei = 5;
    optional string device_info = 6;
    optional string client_ip = 7;
    
}

message UserBehavior{
    required uint32 uid = 1;
    required uint32 behavior = 2;   //UserBehaviorType
    optional UserLogin login = 3;
}

service AntiLogic {
    option( tlvpickle.Magic ) = 15062;
}
