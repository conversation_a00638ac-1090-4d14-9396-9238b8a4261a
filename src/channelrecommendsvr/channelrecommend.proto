syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package channelrecommend;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list


// 进出房间事件
message UserEnterChannelReq
{
	required uint32 channel_id = 1;
	required uint32 user_count = 2;
	optional bool is_admin = 3;
	optional bool is_enter = 4;
	optional uint32 admin_count = 5;
	optional bool is_normal = 6;
	optional uint32 channel_type = 7;
}
message UserEnterChannelResp
{
	
}

// 房间礼物
message AddChannelGiftReq
{
	required uint32 channel_id = 1;
	required uint32 gift_value = 2;
}
message AddChannelGiftResp
{

}

// 获取接口
message GetRecommendChannelReq
{
	required uint32 uid = 1;
	required uint32 start = 2;
	required uint32 count = 3;
	required bool   is_new_user = 4;
}

message ChannelInfo
{
	required uint32 channel_id = 1;
	optional uint32 tag = 2;
	optional string info_tag = 3;
	optional string bk_color = 4;
	optional uint32 info_ex_flag = 5;
}
message GetRecommendChannelResp
{
	repeated uint32 channel_id_list = 1;
	required bool reach_end = 2;
	repeated ChannelInfo channel_info_list = 3;
	repeated uint32 may_fix_top_id_list = 4;
}

message GetChannelTagColorReq 
{
	repeated uint32 channel_id_list = 1;
}
message GetChannelTagColorResp
{
	// 只包含info_tag, bk_color
	repeated ChannelInfo channel_info_list = 1;
}

enum AddChannelType
{
	ACT_OFFICIAL_ACTIVITY = 1;	// 官方活动
	ACT_BLACKLIST = 2;			// 黑名单
	ACT_OFFICIAL_RECOMMEND = 3;	// 官方推荐
	ACT_FIX_RECOMMEND = 4;		// 指定推荐列表
	ACT_RECOMMENDV2	=	5;		// 推荐库(v2)
	ACT_LIVE_RECOMMEND = 6;		// 语音直播房
	ACT_RECOMMEND_NEWUSER =7;   // 新玩家推荐
	
	ACT_BLOCKLIST = 101;		// 临时屏蔽列表（管理员不在之类的）
}
message AddChannelReq
{
	repeated uint32 channel_id_list = 1;
	required bool is_add = 2;
	required uint32 channel_type = 3;
}
message AddChannelResp
{
}

message GetChannelReq
{
	required uint32 channel_type = 1;
}

message GetChannelResp
{
	repeated uint32 channel_id_list = 1;
}

message ChannelTypeInfo
{
	required uint32 channel_id = 1;
	required uint32 channel_type = 2;	// AddChannelType
}

message BatchGetChannelTypeReq
{
	repeated uint32 channel_id_list = 1;
}

message BatchGetChannelTypeResp
{
	repeated ChannelTypeInfo channel_type_list = 1;
}

// 后续要为2幂
enum ChannelInfoExFlag
{
	EXFLAG_CANBETOP = 1;	// 是否为TOPN
	EXFLAG_ISESSENCE = 2;	// 是否精选
}

message ChannelInfoEx
{
	required uint32 channel_id = 1;
	required uint32 start_ts = 2;
	required uint32 end_ts = 3;
	required string desc = 4;
	optional uint32 user_count = 5;
	repeated uint32 hours_to_add_list = 6;
	optional uint32 flags = 7;
	optional string info_tag = 8;	// 给客户端的简要标签 
	optional string bk_color = 9;
}

message SetChannelInfoExReq
{
	required uint32 channel_type = 1;
	repeated ChannelInfoEx channel_ex_list = 2;
}
message SetChannelInfoExResp
{
	
}


message GetChannelInfoExReq
{
	optional uint32 channel_type = 1;    // AddChannelType
	repeated uint32 channel_id_list = 2;
}
message GetChannelInfoExResp
{
	repeated ChannelInfoEx channel_ex_list = 2;
}

message RemoveChannelReq
{
	repeated uint32 channel_id_list = 1;
}
message RemoveChannelResp
{
}

message RemoveChannelByTypeReq
{
	repeated uint32 channel_id_list = 1;
	required uint32 channel_type = 2;
}

message RemoveChannelByTypeResp
{
	
}

message AddUserLikeChannelReq
{
	repeated uint32 channel_id_list = 1;
}

message AddUserLikeChannelResp
{
}

message GetChannelInfoTagReq
{
	repeated uint32 channel_id_list = 1;
}
message ChannelInfoTag
{
	required uint32 channel_id = 1;
	optional string info_tag = 2;
	optional string bk_color = 3;
	optional uint32 channel_type = 4; // AddChannelType
}
message GetChannelInfoTagResp
{
	repeated ChannelInfoTag channel_infotag_list = 1;
}

message GetHotChannelReq
{
	required uint32 uid = 1;
	required uint32 start = 2;
	required uint32 count = 3;
	optional uint32 ts = 4;
}

message ChannelHotInfo
{
	required uint32 channel_id = 1;
	optional uint32 day_gift_value = 2;
	optional uint32 rank_gift_value = 3;
}

message GetHotChannelResp
{
	repeated uint32 channel_id_list = 1;
	required bool reach_end = 2;
}

message SetHotChannelCfgReq
{
	required uint32 total_gift_value = 1;	// 上热门榜的房间当天流水限制
}

message GetHotChannelCfgReq
{
}

message GetHotChannelCfgResp
{
	required uint32 total_gift_value = 1;	// 上热门榜的房间当天流水限制
}

enum HotChannelType
{
	HOT_BLACKLIST = 1;			// 黑名单
	
	HOT_BLOCKLIST = 101;		// 临时屏蔽列表（管理员不在之类的）
}

// 设置热门房间的类型
message SetHotChannelTypeReq
{
	required uint32 channel_id = 1;
	required uint32 channel_type = 2;	// HotChannelType
}

message HotChannelInfo
{
	required uint32 channel_id = 1;
	required uint32 channel_type = 2;	// HotChannelType
}

// 获取热门房间的类型
message BatchGetHotChannelTypeReq
{
	repeated uint32 channel_id_list = 1;
}

message BatchGetHotChannelTypeResp
{
	repeated HotChannelInfo channel_list = 1;
}

// 获取指定类型的热门房间列表
message GetHotChannelListByTypeReq
{
	required uint32 channel_type = 1;	// HotChannelType
}

message GetHotChannelListByTypeResp
{
	repeated HotChannelInfo channel_list = 1;
}

message BatchDelHotChannelReq
{
	repeated uint32 channel_id_list = 1;
}

message ColorInfo
{
	required string tag_name = 1;
	required string color = 2;
}

message AddColorReq
{
	repeated ColorInfo color_info_list = 1;
	required bool is_add = 2;
}
message AddColorResp
{
}

message GetAllColorReq
{
	
}
message GetAllColorResp
{
	repeated ColorInfo color_info_list = 1;
}

message CardInfo
{
	required string card_name = 1;
	required string card_image = 2;
	required string back_image = 3;
	required string client_link = 4;
	optional uint32 tag_id = 5;
	optional uint32 user_count = 6; // 无效废弃字段
	optional uint32 game_id =  7;
	optional bool fix_position = 8; // 位置不变
}

enum RootTagType
{
	RTT_INVALID_TAG = 0;
	RTT_FUNNY_ROOM = 1;
	RTT_GAME = 2;
}
message RootTag
{
	required string tag_name = 1;
	required uint32 tag_id = 2;
	optional uint32 calc_sub = 3;
	optional uint32 tag_type = 4;
}
message SubTag
{
	enum SubTagFlag
	{
		STF_HIDE_SETTING = 1;	// 是否隐藏在设置里
		STF_HIDE_TAB = 2;		// 是否有子标签显示列表
	}
	required string tag_name = 1;
	required string icon = 2;
	required uint32 tag_id = 3;
	optional bool is_show = 4;
	optional string jump_url = 5;
	optional uint32 root_tag_id = 6;
	optional string bk_color = 7;
	optional uint32 ext_flag = 8;//如果有,优先使用这个字段,兼容4
}
message TagTypes
{
	required RootTag root_tag = 1;
	repeated SubTag sub_tag_list = 2;
}
message GetAllTagTypeReq
{
	
}
message GetAllTagTypeResp
{
	repeated TagTypes tag_types_list = 1;
}

message GetChannelByTagReq
{
	required uint32 tag_id = 1;
	required uint32 start = 2;
	required uint32 count = 3;
}
message GetChannelByTagResp
{
	repeated uint32 channel_id_list = 1;
	required bool reach_end = 2;
	optional uint32 user_count = 3; // 无效过期字段 不再填值
}

message GetChannelCardReq
{
}
message GetChannelCardResp
{
	repeated CardInfo card_info_list = 1;
	required SubTag game_sub_tag = 2;
	required bool game_on_top = 3;
	required bool has_visit_game = 4;
}

message RefreshChannelTimeReq
{
	required uint32 channel_id = 1;
}
message RefreshChannelTimeResp
{
	required uint32 remain_seconds = 1;
}

message GetChannelRefreshCDReq
{
	required uint32 channel_id = 1;
}
message GetChannelRefreshCDResp
{
	required uint32 remain_seconds = 1;
}

message SetChannelTagIdReq
{
	required uint32 channel_id = 1;
	required uint32 tag_id = 2;
}
message SetChannelTagIdResp
{
	optional uint32 old_tag_id = 1; 
}
message GetChannelTagIdReq
{
	required uint32 channel_id = 1;	
}
message GetChannelTagIdResp
{
	required SubTag sub_tag = 1;
	required bool is_set = 2;
}

enum ADV_TYPE
{
	AT_CHANNEL_FUNNY = 0;
	AT_CHANNEL_HOMEPAGE = 1;
}
message ChannelTagAdv
{
	required string pic_url = 1;
	required string adv_url = 2;
	optional uint32 score_idx = 3;
}

message AddChannelTagAdvReq
{
	repeated ChannelTagAdv tag_adv_list = 1;
	optional bool clean_old = 2;
	optional uint32 adv_type = 3; // ADV_TYPE
}
message AddChannelTagAdvResp{}
message RemoveChannelTagAdvReq
{
	required ChannelTagAdv tag_adv = 1;
	optional uint32 adv_type = 2; // ADV_TYPE
}
message RemoveChannelTagAdvResp{}
message GetChannelTagAdvReq
{
	optional uint32 adv_type = 1; // ADV_TYPE
}
message GetChannelTagAdvResp
{
	repeated ChannelTagAdv tag_adv_list = 1;
}

message TagTopChannel
{
	required uint32 channel_id = 1;
	required uint32 score = 2;
}

message AddTagTopChannelReq
{
	repeated TagTopChannel top_channel_list = 1;
	required uint32 tag_id = 2;
}
message AddTagTopChannelResp{}

message GetTagTopChannelReq{
	required uint32 tag_id = 1;
}
message GetTagTopChannelResp{
	repeated TagTopChannel top_channel_list = 1;
}

message RemoveTagTopChannelReq {
	repeated uint32 channel_id_list = 1;
}
message RemoveTagTopChannelResp {}

enum RecommendChannelType{
	GAME_ROOM = 1;
}

message CommonRecommendChannel{
	required uint32 channel_id = 1;	
}
message AddCommonRecommendReq{
	required CommonRecommendChannel recommend_channel = 1;
	required uint32 fun_type = 2; // RecommendChannelType
}
message AddCommonRecommendResp{}

message RemoveCommonRecommendReq{
	required CommonRecommendChannel recommend_channel = 1;
	required uint32 fun_type = 2; // RecommendChannelType
}
message RemoveCommonRecommendResp{}

message GetCommonRecommendReq{	
	required uint32 fun_type = 1;
}
message GetCommonRecommendResp{
	repeated CommonRecommendChannel recommend_channel_list = 1;
}
message RandomChannelInfo{
	required uint32 channel_id = 1;
	required uint32 block_tag = 2;
}
message AddRandomRecommendChannelReq {
	required uint32 channel_id = 1;
}
message AddRandomRecommendChannelResp {
}
message GetRandomRecommendChannelReq {
	required uint32 count = 1;
}
message GetRandomRecommendChannelResp {
	repeated uint32 channel_id_list = 1;
}
message RemoveRandomRecommendChannelReq {
	required uint32 channel_id = 1;
}
message RemoveRandomRecommendChannelResp {
}

message GetChannelTypeByTagReq {
	optional uint32 tag_id = 1;
	optional uint32 channel_id = 2;
}
message GetChannelTypeByTagResp {
	required uint32 tag_type = 1;// RootTagType
	required uint32 tag_id = 2;
}

message GetTagInfoReq{
	required uint32 tag_id = 1;
}
message GetTagInfoResp{
	required SubTag sub_tag_info = 1;
}

message GetMajorLiveChannelReq{
	required uint32 uid = 1;
}
message GetMajorLiveChannelResp{
	repeated uint32 channel_id_list = 1;
}

message LiveChannelEx{
	required uint32 channel_id = 1;
	required uint32 score_idx = 2;
	optional uint32 has_admin = 3;
	repeated uint32 hours_to_add_list = 4;
}

message AddLiveRecommendChannelReq{
	required uint32 channel_id = 1;
	required uint32 score_idx = 2;
	repeated uint32 hours_to_add_list = 3;
}
message AddLiveRecommendChannelResp{

}

message DelLiveRecommendChannelReq{
	required uint32 channel_id = 1;
}
message DelLiveRecommendChannelResp{

}

message GetLiveRecommendChannelReq{
}
message GetLiveRecommendChannelResp{
	repeated LiveChannelEx channel_ex_list = 1;
}

message GetLiveChannelListReq{
	required uint32 start = 1;
	required uint32 count = 2;
}

message GetLiveChannelListResp{
	repeated uint32 channel_id_list = 1;
	required bool reach_end = 2;
	optional uint32 user_count = 3;
}

service ChannelRecommend {
	option( tlvpickle.Magic ) = 15567;		// 服务监听端口号

    rpc UserEnterChannel ( UserEnterChannelReq ) returns( UserEnterChannelResp ) {
        option( tlvpickle.CmdID ) = 1;              // 命令号
        option( tlvpickle.OptString ) = "u:i:n:a:e:x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<uid> -i <channel_id> -n<user_count> -a<is_admin> -e<is_enter> -x<admin_count>";    // 测试工具的命令号帮助
    }
	
	rpc GetRecommendChannel (GetRecommendChannelReq) returns (GetRecommendChannelResp){
		option( tlvpickle.CmdID ) = 2;              // 命令号
        option( tlvpickle.OptString ) = "u:s:n:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<uid> -s<start> -n<user_count> ";    // 测试工具的命令号帮助
	}

	rpc AddChannel (AddChannelReq) returns (AddChannelResp){
		option( tlvpickle.CmdID ) = 3;              // 命令号
        option( tlvpickle.OptString ) = "x:a:t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id> -a<is_add> -t<channel_type>";    // 测试工具的命令号帮助
	}
	rpc GetChannel (GetChannelReq) returns (GetChannelResp){
		option( tlvpickle.CmdID ) = 4;              // 命令号
        option( tlvpickle.OptString ) = "t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<channel_type>";    // 测试工具的命令号帮助
	}
	rpc AddChannelGift (AddChannelGiftReq) returns (AddChannelGiftResp){
		option( tlvpickle.CmdID ) = 5;              // 命令号
        option( tlvpickle.OptString ) = "x:g:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id> -g<gift_value>";    // 测试工具的命令号帮助
	}
	rpc SetChannelInfoEx (SetChannelInfoExReq) returns (SetChannelInfoExResp){
		option( tlvpickle.CmdID ) = 6;              // 命令号
        option( tlvpickle.OptString ) = "t:a:s:e:d:u:h:i:x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<channel_type> -a<channel_id> -s<start_ts> -e<end_ts> -d<desc> -u<user_count> -h<hour> -i<info_tag> -x<flags>";    // 测试工具的命令号帮助
	}
	rpc GetChannelInfoEx (GetChannelInfoExReq) returns (GetChannelInfoExResp){
		option( tlvpickle.CmdID ) = 7;              // 命令号
        option( tlvpickle.OptString ) = "t:x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<channel_type(opt)> -x<channel_id(opt)>";    // 测试工具的命令号帮助
	}
	rpc RemoveChannel (RemoveChannelReq) returns (RemoveChannelResp){
		option( tlvpickle.CmdID ) = 8;              // 命令号
        option( tlvpickle.OptString ) = "x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id> ";    // 测试工具的命令号帮助
	}
	
	rpc GetHotChannel (GetHotChannelReq) returns (GetHotChannelResp){
		option( tlvpickle.CmdID ) = 9;              // 命令号
        option( tlvpickle.OptString ) = "s:n:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-s<start> -n<count>";    // 测试工具的命令号帮助
	}
	
	rpc AddUserLikeChannel (AddUserLikeChannelReq) returns (AddUserLikeChannelResp){
		option( tlvpickle.CmdID ) = 10;              // 命令号
        option( tlvpickle.OptString ) = "u:x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<uid> -x<channel_id> ";    // 测试工具的命令号帮助
	}
	rpc GetChannelInfoTag (GetChannelInfoTagReq) returns (GetChannelInfoTagResp){
		option( tlvpickle.CmdID ) = 11;              // 命令号
        option( tlvpickle.OptString ) = "x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id> ";    // 测试工具的命令号帮助
	}
	
	rpc SetHotChannelCfg (SetHotChannelCfgReq) returns (tlvpickle.SKBuiltinEmpty_PB){
		option( tlvpickle.CmdID ) = 12;              // 命令号
        option( tlvpickle.OptString ) = "n:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n<total_gift_value> ";    // 测试工具的命令号帮助
	}
	
	rpc GetHotChannelCfg (GetHotChannelCfgReq) returns (GetHotChannelCfgResp){
		option( tlvpickle.CmdID ) = 13;              // 命令号
        option( tlvpickle.OptString ) = "";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
	}
	
	rpc BatchGetChannelType (BatchGetChannelTypeReq) returns (BatchGetChannelTypeResp){
		option( tlvpickle.CmdID ) = 14;              // 命令号
        option( tlvpickle.OptString ) = "i:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i<channel_id>";    // 测试工具的命令号帮助
	}

	// 废弃
	rpc AddColor (AddColorReq) returns (AddColorResp){
		option( tlvpickle.CmdID ) = 15;              // 命令号
        option( tlvpickle.OptString ) = "o:t:a:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-o<color> -t<tag_name> -a<is_add>";    // 测试工具的命令号帮助
	}

	// 废弃
	rpc GetAllColor (GetAllColorReq) returns (GetAllColorResp){
		option( tlvpickle.CmdID ) = 16;              // 命令号
        option( tlvpickle.OptString ) = "";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
	}
	
	rpc SetHotChannelType (SetHotChannelTypeReq) returns (tlvpickle.SKBuiltinEmpty_PB){
		option( tlvpickle.CmdID ) = 17;              // 命令号
        option( tlvpickle.OptString ) = "x:t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id> -t<channel_type>";    // 测试工具的命令号帮助
	}

	rpc BatchGetHotChannelType (BatchGetHotChannelTypeReq) returns (BatchGetHotChannelTypeResp){
		option( tlvpickle.CmdID ) = 18;              // 命令号
        option( tlvpickle.OptString ) = "x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id> ";    // 测试工具的命令号帮助
	}
	
	rpc GetHotChannelListByType (GetHotChannelListByTypeReq) returns (GetHotChannelListByTypeResp){
		option( tlvpickle.CmdID ) = 19;              // 命令号
        option( tlvpickle.OptString ) = "t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<channel_type> ";    // 测试工具的命令号帮助
	}
	rpc GetAllTagType (GetAllTagTypeReq) returns (GetAllTagTypeResp){
		option( tlvpickle.CmdID ) = 20;              // 命令号
        option( tlvpickle.OptString ) = "";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
	}	
	rpc GetChannelCard (GetChannelCardReq) returns (GetChannelCardResp){
		option( tlvpickle.CmdID ) = 21;              // 命令号
        option( tlvpickle.OptString ) = "u:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<uid>";    // 测试工具的命令号帮助
	}	
	rpc GetChannelByTag (GetChannelByTagReq) returns (GetChannelByTagResp){
		option( tlvpickle.CmdID ) = 22;              // 命令号
        option( tlvpickle.OptString ) = "u:t:s:n:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<uid> -t<tag_id> -s<start> -n<count>";    // 测试工具的命令号帮助
	}	
	rpc RefreshChannelTime(RefreshChannelTimeReq) returns (RefreshChannelTimeResp){
		option( tlvpickle.CmdID ) = 23;              // 命令号
        option( tlvpickle.OptString ) = "x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id>";    // 测试工具的命令号帮助
	}
	rpc SetChannelTagId (SetChannelTagIdReq) returns (SetChannelTagIdResp){
		option( tlvpickle.CmdID ) = 24;              // 命令号
        option( tlvpickle.OptString ) = "x:t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id> -t<tag_id>";    // 测试工具的命令号帮助
	}
	rpc GetChannelTagId (GetChannelTagIdReq) returns (GetChannelTagIdResp){
		option( tlvpickle.CmdID ) = 25;              // 命令号
        option( tlvpickle.OptString ) = "x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id> ";    // 测试工具的命令号帮助
	}
	rpc AddChannelTagAdv (AddChannelTagAdvReq) returns (AddChannelTagAdvResp){
		option( tlvpickle.CmdID ) = 26;              // 命令号
        option( tlvpickle.OptString ) = "p:a:s:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p<pic_url> -a<adv_url> -s<score_idx>";    // 测试工具的命令号帮助
	}
	rpc RemoveChannelTagAdv (RemoveChannelTagAdvReq) returns (RemoveChannelTagAdvResp){
		option( tlvpickle.CmdID ) = 27;              // 命令号
        option( tlvpickle.OptString ) = "p:a:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p<pic_url> -a<adv_url> ";    // 测试工具的命令号帮助
	}
	rpc GetChannelTagAdv (GetChannelTagAdvReq) returns (GetChannelTagAdvResp){
		option( tlvpickle.CmdID ) = 28;              // 命令号
        option( tlvpickle.OptString ) = "";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
	}
	rpc GetChannelRefreshCD (GetChannelRefreshCDReq) returns (GetChannelRefreshCDResp){
		option( tlvpickle.CmdID ) = 29;              // 命令号
        option( tlvpickle.OptString ) = "x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id>";    // 测试工具的命令号帮助
	}
	rpc GetChannelTagColor (GetChannelTagColorReq) returns (GetChannelTagColorResp){
		option( tlvpickle.CmdID ) = 30;              // 命令号
        option( tlvpickle.OptString ) = "x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id>";    // 测试工具的命令号帮助
	}

	rpc AddTagTopChannel (AddTagTopChannelReq) returns (AddTagTopChannelResp){
		option( tlvpickle.CmdID ) = 31;              // 命令号
        option( tlvpickle.OptString ) = "x:s:t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id> -s<score> -t<tag_id>";    // 测试工具的命令号帮助
	}

	rpc GetTagTopChannel (GetTagTopChannelReq) returns (GetTagTopChannelResp){
		option( tlvpickle.CmdID ) = 32;              // 命令号
        option( tlvpickle.OptString ) = "t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<tag_id>";    // 测试工具的命令号帮助
	}

	rpc RemoveTagTopChannel (RemoveTagTopChannelReq) returns (RemoveTagTopChannelResp){
		option( tlvpickle.CmdID ) = 33;              // 命令号
        option( tlvpickle.OptString ) = "x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id>";    // 测试工具的命令号帮助
	}
	
	rpc AddCommonRecommend (AddCommonRecommendReq) returns (AddCommonRecommendResp){
		option( tlvpickle.CmdID ) = 34;              // 命令号
        option( tlvpickle.OptString ) = "x:t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id> -t<fun_type>";    // 测试工具的命令号帮助
	}

	rpc RemoveCommonRecommend (RemoveCommonRecommendReq) returns (RemoveCommonRecommendResp){
		option( tlvpickle.CmdID ) = 35;              // 命令号
        option( tlvpickle.OptString ) = "x:t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id> -t<fun_type>";    // 测试工具的命令号帮助
	}

	rpc GetCommonRecommend (GetCommonRecommendReq) returns (GetCommonRecommendResp){
		option( tlvpickle.CmdID ) = 36;              // 命令号
        option( tlvpickle.OptString ) = "t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<fun_type>";    // 测试工具的命令号帮助
	}

	rpc AddRandomRecommendChannel (AddRandomRecommendChannelReq) returns (AddRandomRecommendChannelResp){
		option( tlvpickle.CmdID ) = 37;              // 命令号
        option( tlvpickle.OptString ) = "x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id>";    // 测试工具的命令号帮助
	}

	rpc GetRandomRecommendChannel (GetRandomRecommendChannelReq) returns (GetRandomRecommendChannelResp){
		option( tlvpickle.CmdID ) = 38;              // 命令号
        option( tlvpickle.OptString ) = "n:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n<count>";    // 测试工具的命令号帮助
	}

	rpc RemoveRandomRecommendChannel (RemoveRandomRecommendChannelReq) returns (RemoveRandomRecommendChannelResp){
		option( tlvpickle.CmdID ) = 39;              // 命令号
        option( tlvpickle.OptString ) = "x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id>";    // 测试工具的命令号帮助
	}

	rpc BatchDelHotChannel (BatchDelHotChannelReq) returns (tlvpickle.SKBuiltinEmpty_PB){
		option( tlvpickle.CmdID ) = 40;              // 命令号
        option( tlvpickle.OptString ) = "x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id> ";    // 测试工具的命令号帮助
	}

	rpc GetChannelTypeByTag (GetChannelTypeByTagReq) returns (GetChannelTypeByTagResp){
		option( tlvpickle.CmdID ) = 41;              // 命令号
        option( tlvpickle.OptString ) = "t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<tag_id> ";    // 测试工具的命令号帮助
	}

	rpc GetTagInfo (GetTagInfoReq) returns (GetTagInfoResp){
		option( tlvpickle.CmdID ) = 42;              // 命令号
        option( tlvpickle.OptString ) = "t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<tag_id> ";    // 测试工具的命令号帮助
	}

	rpc GetMajorLiveChannel (GetMajorLiveChannelReq) returns (GetMajorLiveChannelResp){
		option( tlvpickle.CmdID ) = 43;              // 命令号
        option( tlvpickle.OptString ) = "u:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";    // 测试工具的命令号帮助
	}

	rpc AddLiveRecommendChannel (AddLiveRecommendChannelReq) returns (AddLiveRecommendChannelResp){
		option( tlvpickle.CmdID ) = 44;              // 命令号
        option( tlvpickle.OptString ) = "x:s:t:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id> -s<score_idx> -t<hours>";    // 测试工具的命令号帮助
	}

	rpc DelLiveRecommendChannel (DelLiveRecommendChannelReq) returns (DelLiveRecommendChannelResp){
		option( tlvpickle.CmdID ) = 45;              // 命令号
        option( tlvpickle.OptString ) = "x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x<channel_id> ";    // 测试工具的命令号帮助
	}

	rpc GetLiveRecommendChannel (GetLiveRecommendChannelReq) returns (GetLiveRecommendChannelResp){
		option( tlvpickle.CmdID ) = 46;              // 命令号
        option( tlvpickle.OptString ) = "";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
	}
	rpc GetLiveChannelList (GetLiveChannelListReq) returns (GetLiveChannelListResp){
		option( tlvpickle.CmdID ) = 47;              // 命令号
        option( tlvpickle.OptString ) = "s:l:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-s<start> -l<count>";    // 测试工具的命令号帮助
	}

	rpc RemoveChannelByType ( RemoveChannelByTypeReq ) returns (RemoveChannelByTypeResp) {
		option( tlvpickle.CmdID ) = 48;              // 命令号
        option( tlvpickle.OptString ) = "t:i:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<channel_type> -i<channel_id>";    // 测试工具的命令号帮助
	}

	rpc SetRecommendChannelTag (SetChannelInfoExReq) returns (SetChannelInfoExResp){
		option( tlvpickle.CmdID ) = 49;              // 命令号
        option( tlvpickle.OptString ) = "t:a:s:e:d:u:h:i:x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<channel_type> -a<channel_id> -s<start_ts> -e<end_ts> -d<desc> -u<user_count> -h<hour> -i<info_tag> -x<flags>";    // 测试工具的命令号帮助
	}
}

