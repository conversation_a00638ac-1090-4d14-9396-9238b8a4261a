syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package statisticscollect;

message SCommonStatisticsData 
{
    required uint32 data_id = 1;
    optional uint32 data_num_value = 2;
	optional string data_bin_value = 3;
}

// 上报 通用统计数据
message ReportCommonStatisticsReq
{
	required uint32 timestamp = 1; // 上报数据对应的起始周期时间戳
	required uint32 type = 2;
	repeated SCommonStatisticsData data_list = 3;
}



service statisticscollect {
	option( tlvpickle.Magic ) = 15072;		// 服务监听端口号
	
	rpc ReportCommonStatistics ( ReportCommonStatisticsReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 1;
		option( tlvpickle.OptString ) = "t:u:x:";
		option( tlvpickle.Usage ) = "-t <type> -u <uid> -x <channel id>";
	}

}
