syntax = "proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package channelolmember;

// channelolmember 服务仅提供根据channelId查询用户的接口
// 不提供 根据uid查询房间的接口


// 房间成员
message ChannelMember {
    required uint32 uid  = 1;            // 成员uid

	optional bool is_holdmic = 2;  // 废弃
	optional bool is_mute = 3;     // 废弃
	
	optional uint32 cost_value   = 4;    // 没有使用
	optional uint32 ts   = 5;            // 没有使用
}


// 获取房间成员列表
message GetChannelMemberListReq
{
	required uint32 channel_id = 1;
	required uint32 start_idx = 2;
	required uint32 limit = 3;
}

message GetChannelMemberListResp
{
	repeated ChannelMember member_list = 1;
	required uint32 all_size = 2;
}

// 获取房间成员uid列表
message GetChannelMemberUidListReq
{
    required uint32 channel_id = 1;
    required uint32 start_idx = 2;
    required uint32 limit = 3;
}

message GetChannelMemberUidListResp
{
    repeated uint32 member_uid_list = 1;
}

// 批量获取一组房间的指定个数成员列表
message ChannelMemberListInfo
{
	required uint32 channel_id = 1;
	repeated ChannelMember member_list = 2;
	required uint32 all_size = 3;
}
message BatchGetChannelMemberListReq
{
	repeated uint32 channel_id_list = 1;
	required uint32 member_count = 2; 
}

message BatchGetChannelMemberListResp
{
	repeated ChannelMemberListInfo channelmemberinfo_list = 1;
}


message GetChannelMemberReq{
    optional uint32 app_id = 1;//reserved
    required uint32 channel_id = 2;
    required uint32 uid = 3;
}
message GetChannelMemberResp{
    optional ChannelMember member = 1;
}

message GetChannelMemberSizeReq{
    optional uint32 app_id = 1;//reserved
    required uint32 channel_id = 2;
}
message GetChannelMemberSizeResp{
    optional uint32 size = 1;
}

message BatchGetChannelMemberSizeReq
{
	repeated uint32 channel_id_list = 1;
}

message BatchGetChannelMemberSizeResp
{
	repeated uint32 membersize_list = 1;
}


service channelolmember {
	option( tlvpickle.Magic ) = 16025;		// 服务监听端口号
	

	rpc GetChannelMemberList( GetChannelMemberListReq ) returns( GetChannelMemberListResp ){
		option( tlvpickle.CmdID ) = 2;										
        option( tlvpickle.OptString ) = "x:b:l:";						
        option( tlvpickle.Usage ) = "-x <channel id> [-b <begin idx>] [-l <limit >]";
	}

	
	// 批量获取一组房间的指定个数成员列表
	rpc BatchGetChannelMemberList( BatchGetChannelMemberListReq ) returns( BatchGetChannelMemberListResp ){
		option( tlvpickle.CmdID ) = 4;										
        option( tlvpickle.OptString ) = "x:l:";						
        option( tlvpickle.Usage ) = "-x <channel_id> -l <member count limit>";
	}

    rpc GetChannelMemberSize( GetChannelMemberSizeReq ) returns ( GetChannelMemberSizeResp ) {
		option( tlvpickle.CmdID ) = 6;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel_id> ";
    }

    rpc BatchGetChannelMemberSize ( BatchGetChannelMemberSizeReq ) returns ( BatchGetChannelMemberSizeResp ) {
        option( tlvpickle.CmdID ) = 9;										
        option( tlvpickle.OptString ) = "x:";						
        option( tlvpickle.Usage ) = "-x <channel_id> ";
    }
	
    rpc GetChannelMemberUidList ( GetChannelMemberUidListReq ) returns ( GetChannelMemberUidListResp ) {
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "x:b:l:";
        option( tlvpickle.Usage ) = "-x <channel id> [-b <begin idx>] [-l <limit >]";
    }

}
