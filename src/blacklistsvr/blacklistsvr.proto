syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package blacklistsvr;

message OperationRiskInfo
{
	optional string riskLevel = 1;
	optional string message = 2;
	optional uint32 score = 3;
}

message GetRandomBlackUserReq
{
	required uint32 count = 1; 
}

message GetRandomBlackUserResp
{
	repeated uint32 uid_list = 1;
}

message IsBlackUserReq
{
}

message IsBlackUserResp
{
	optional bool is_black_user = 1;
}

message BlackUserInfo
{
	required uint32 uid = 1;
	required uint32 sex = 2;
}

message RemBlackUserReq
{
	repeated uint32 black_user_list = 1;
}

message RemBlackUserResp
{
}

service blacklistsvr {
	option( tlvpickle.Magic ) = 15661;		// 服务监听端口号

	rpc GetRandomBlackUser ( GetRandomBlackUserReq ) returns ( GetRandomBlackUserResp ) {
		option( tlvpickle.CmdID ) = 1;
		option( tlvpickle.OptString ) = "u:s:";
		option( tlvpickle.Usage ) = "-u <uid> -s <size>";
	}

	rpc IsBlackUser ( IsBlackUserReq ) returns ( IsBlackUserResp ) {
		option( tlvpickle.CmdID ) = 2;
		option( tlvpickle.OptString ) = "u:x:s:r:";
		option( tlvpickle.Usage ) = "-u <uid>";
	}

	rpc RemBlackUser ( RemBlackUserReq ) returns ( RemBlackUserResp ) {
		option( tlvpickle.CmdID ) = 3;
		option( tlvpickle.OptString ) = "u:";
		option( tlvpickle.Usage ) = "-u <uid>";
	}
}