syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package friendolsvr;


enum OnlineStatus {
	OFFLINE = 0;
	ONLINE = 1;
	
}

message OnlineInfo {
	required uint32 room_id = 1;
	required string game_name = 2;
}

message FriendsDetail {
	required uint32 uid = 1;
	required OnlineStatus ol_status = 2;
	required uint32 last_ol_time = 3;
	optional uint32 room_id = 4;
	optional string game_name = 5;
	optional uint32 channel_type = 6;	// 房间类型, channel_.proto: enum ChannelType
	optional bool channel_is_pwd = 7;	// 房间是否有密码 仅在进房时该字段更新
	optional bool invisible = 8;        // 隐身
}

message GetOnlineFriendsReq {
	required uint32 uid = 1;
	
	repeated uint32 ext_check_online_uidlist = 2; // 额外需要检查是否在线的UID列表 
}

message GetOnlineFriendsResp {
	repeated FriendsDetail detail_list = 1;
	
	repeated FriendsDetail ext_check_offline_list = 2; // 额外需要检查是否在线的UID列表里面已经离线的用户信息
}


message GetOfflineFriendsReq {
	required uint32 uid = 1;
}

message GetOfflineFriendsResp {
	repeated FriendsDetail detail_list = 1;
}

message UpdateOnlineStatusReq {
	required uint32 uid = 1;
	required OnlineStatus ol_status = 2;
}

message UpdateUserPlayingGameReq {
	required uint32 uid = 1;
	required bool is_playing = 2;
	required string game_name = 3;
}


message UpdateUserRoomIdReq {
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required bool is_enter = 3;       // 是否是进房
	optional uint32 channel_type = 4;
	optional bool is_pwd = 5;   // 房间是否有密码 仅在 is_enter == true 时填写
}



message UpdateFollowChannelAuthReq {
	required uint32 uid = 1;
	required bool follow_auth = 2; // false: Not allow friends quick join (废弃的 过期字段)
	
	optional uint32 follow_auth_switch = 3;  // see proto/friendol_.proto EFollowChannelAuthSwitchType
}


message GetFollowChannelAuthReq {
	required uint32 uid = 1;
}

message GetFollowChannelAuthResp {
	required bool follow_auth = 1;           // (废弃的 过期字段)
	optional uint32 follow_auth_switch = 2;  // see proto/friendol_.proto EFollowChannelAuthSwitchType
}

// 批量获取指定用户的跟随开关设置
message BatGetFollowChannelAuthReq {
	repeated uint32 uid_list = 1;
}
message BatGetFollowChannelAuthResp {
	repeated bool follow_auth_list = 1;             // (废弃的 过期字段)
	repeated uint32 follow_auth_switch_list = 2;  // see proto/friendol_.proto EFollowChannelAuthSwitchType
}


// 获取好友数量
message GetFirendCacheCountReq {
	required uint32 uid = 1;
}
message GetFirendCacheCountResp 
{
	required uint32 count = 1;  
}

service FriendOLSvr{
    option( tlvpickle.Magic ) = 15550;

    rpc GetOnlineFriends ( GetOnlineFriendsReq ) returns ( GetOnlineFriendsResp ) {
        option( tlvpickle.CmdID ) = 1;	
	    option( tlvpickle.OptString ) = "u:x:";	
        option( tlvpickle.Usage ) = "-u <uid> -x <ext_check_online_uidlist>";
    }

	rpc GetOfflineFriends ( GetOfflineFriendsReq ) returns ( GetOfflineFriendsResp ) {
        option( tlvpickle.CmdID ) = 2;	
	    option( tlvpickle.OptString ) = "u:";	
        option( tlvpickle.Usage ) = "-u <uid>";
    }

	rpc UpdateOnlineStatus ( UpdateOnlineStatusReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 3;	
	    option( tlvpickle.OptString ) = "";	
        option( tlvpickle.Usage ) = "";	
    }

	rpc UpdateUserPlayingGame ( UpdateUserPlayingGameReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 4;
	    option( tlvpickle.OptString ) = "";	
        option( tlvpickle.Usage ) = "";
    }

	rpc UpdateUserRoomId ( UpdateUserRoomIdReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 5;	
	    option( tlvpickle.OptString ) = "";	
        option( tlvpickle.Usage ) = "";
    }

	// 更新用户的跟随开关设置
	rpc UpdateFollowChannelAuth ( UpdateFollowChannelAuthReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 6;	
	    option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";	
    }

	// 获取指定用户的跟随开关设置
	rpc GetFollowChannelAuth ( GetFollowChannelAuthReq ) returns ( GetFollowChannelAuthResp ) {
        option( tlvpickle.CmdID ) = 7;	
	    option( tlvpickle.OptString ) = "u:";	
        option( tlvpickle.Usage ) = "-u <uid>";
    }
    
	// 批量获取指定用户的跟随开关设置
	rpc BatGetFollowChannelAuth ( BatGetFollowChannelAuthReq ) returns ( BatGetFollowChannelAuthResp ) {
        option( tlvpickle.CmdID ) = 8;	
	    option( tlvpickle.OptString ) = "u:";	
        option( tlvpickle.Usage ) = "-u1 <uid>";
    }
	
	// 从cache中获取用户好友数量
	rpc GetFirendCacheCount ( GetFirendCacheCountReq ) returns ( GetFirendCacheCountResp ) {
        option( tlvpickle.CmdID ) = 9;	
	    option( tlvpickle.OptString ) = "u:";	
        option( tlvpickle.Usage ) = "-u1 <uid>";
    }

}
