syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";  

// namespace
package channelcommonvotepk;

//人气投票的PK信息
message ChannelCommonPkInfo
{
	required uint32 channel_id = 1; 
	required uint32 start_timestamp  = 2; //开始时间戳
	required uint32 duration_min = 3;
	required uint32 vote_cnt_limit = 4; //每个观众的可投票数
}

message ChannelCommonPkCompetitor
{
	required uint32 channel_id = 1; 
	required uint32 start_timestamp  = 2; 
	required uint32 uid = 3;          //选手uid
}

message SetChannelCommonPkInfoReq
{
	required ChannelCommonPkInfo info = 1;
}

message SetChannelCommonPkInfoResp
{
	required uint32 code = 1;  //错误码
}


message GetUserLeftVoteCntReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2; 
	required uint32 start_timestamp  = 3; //开始时间戳
}

message GetUserLeftVoteCntResp
{
	required uint32 left_vote_cnt = 1;
}


message ChannelCommonPkVoteReq
{
	required uint32 from_uid = 1;    //投票的观众
	required uint32 channel_id = 2; 
	required uint32 start_timestamp  = 3; //开始时间戳
	required uint32 to_uid = 4;          //收票的选手
}

message ChannelCommonPkVoteResp
{
	required uint32 code = 1;  //错误码
	
}

service channelcommonvotepk 
{
	option( tlvpickle.Magic ) = 15659;		// 服务监听端口号

	//设置人气投票的相关信息
	rpc SetChannelCommonPkInfo( SetChannelCommonPkInfoReq ) returns( SetChannelCommonPkInfoResp ){
		option( tlvpickle.CmdID ) = 1;										
        option( tlvpickle.OptString ) = "u:h:s:d:l:";		
        option( tlvpickle.Usage ) = "-u <uid> -h <channel_id> -s <start_timestamp> -d <duration_min> -l <vote_cnt_limit>";
	} 

	//获得观众的剩余票数
	rpc GetUserLeftVoteCnt ( GetUserLeftVoteCntReq ) returns ( GetUserLeftVoteCntResp )
	{
		option( tlvpickle.CmdID ) = 2;										
        option( tlvpickle.OptString ) = "h:s:u:";		
        option( tlvpickle.Usage ) = "-h <channel_id> -s <start_timestamp> -u <uid>";
	}
	
	//投票
	rpc ChannelCommonPkVote ( ChannelCommonPkVoteReq ) returns ( ChannelCommonPkVoteResp )
	{
		option( tlvpickle.CmdID ) = 3;										
        option( tlvpickle.OptString ) = "u:h:s:t:";		
        option( tlvpickle.Usage ) = "-u <from_uid> -h <channel_id> -s <start_timestamp> -t <to_uid> ";
	}
}

