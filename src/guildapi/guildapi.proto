syntax="proto3";

package guildapi;

enum E_REQ_SOURCE_TYPE
{
  ENUM_REQ_SOURCE_INVALID =0;
  ENUM_REQ_SOURCE_LOGIC   = 1;
  ENUM_REQ_SOURCE_GOLOGIC = 2;
  ENUM_REQ_SOURCE_GOSVR   = 3;
  ENUM_REQ_SOURCE_CPPSVR  = 4;
  ENUM_REQ_SOURCE_TOOLS   = 5;
}

message ApiBaseReq{
  uint32 uid = 1;
  uint32 app_id = 2;
  uint32 market_id = 3;
  uint32 source_type = 4; // 请求的来源 E_REQ_SOURCE_TYPE
  string source_msg = 5;  //

  uint32 client_version = 6;
  uint32 client_type = 7;
  string client_ip = 8;
  uint32 client_terminal = 9;

}

message ApiBaseResp{

  int32 ret = 1;
  string err_msg = 2;
}


// 公会解散
message GuildDismissReq{
  ApiBaseReq base_req = 1;
  uint32 guild_id = 2;
}

message GuildDismissResp{
  ApiBaseResp base_resp = 1;
}

// 公会创建
message GuildCreateReq{
  ApiBaseReq base_req = 1;

  uint32 owner_uid = 2;
  string name = 3;
  string desc = 4;
  string prefix = 5;

}

message GuildCreateResp{
  ApiBaseResp base_resp = 1;
  uint32 guild_id = 2;
}

service guildapi{

  rpc GuildDismiss( GuildDismissReq ) returns ( GuildDismissResp ){
  }

  rpc GuildCreate( GuildCreateReq ) returns ( GuildCreateResp ){
  }

}
