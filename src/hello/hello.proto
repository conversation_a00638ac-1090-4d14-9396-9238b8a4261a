syntax="proto2";
// 必须import
import "common/tlvpickle/skbuiltintype.proto";
// namespace
package Hello;
message HelloStrReq{
    required string     str = 1;
}
message HelloStrResp{
    required string     str = 1;
}

service Hello{
    option( tlvpickle.Magic ) = 15400;        // 服务监听端口号

    rpc SayHello( HelloStrReq ) returns( HelloStrResp ){
        option( tlvpickle.CmdID ) = 1;       // 命令号
        option( tlvpickle.OptString ) = "s:";// 测试工具的命令号参数,注意最后的冒号
        option( tlvpickle.Usage ) = "-s <val>";// 测试工具的命令号帮助    
    }
}