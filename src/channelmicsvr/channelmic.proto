syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package ChannelMic;



// 麦位信息
message MicrSpaceInfo
{
    required uint32 mic_id = 1;                       // 麦位ID 1 - 9
    optional uint32 mic_state = 2;                    // ga::EMicrSpaceState 麦位状态 1 正常 2 禁用 3 不可发言
    optional uint32 mic_uid = 3;                      // 麦上用户 如果麦上有人的话
    optional uint32 mic_ts = 4;                       // 麦位最后更新时间
}


// 上麦
message SimpleHoldMicrSpaceReq
{
    required uint32 channel_id = 1;
    required uint32 uid = 2;
    optional uint32 mic_pos_id = 3;  // 麦位ID
    optional bool is_force = 4;      // 是否强行上麦 必须指定mic_pos_id 如果指定的麦位上有人则踢人下麦

    optional uint32 channel_display_id = 5;
    optional uint32 channel_type = 6;
    optional uint32 user_sex = 7;

    optional uint32 op_uid = 8;
}

message SimpleHoldMicrSpaceResp
{
    optional MicrSpaceInfo open_mic_info = 1; // 麦位
    optional uint32 kick_out_uid = 2;         // 被踢下的用户 只有在is_force=true时才有可能
    repeated MicrSpaceInfo all_mic_list = 3;  // 全体麦位信息 包括各个麦位状态
    optional uint64 server_time_ms  = 4;      // 64bit 毫秒级 服务器时间
}


// 假上麦，触发麦位时间更新和麦位kafka
message FakeHoldMicrSpaceReq
{
  required uint32 channel_id = 1;
  required uint32 uid = 2;
  optional uint32 mic_pos_id = 3;  // 麦位ID
  optional bool is_force = 4;      // [废弃字段 无效]

  optional uint32 channel_display_id = 5;
  optional uint32 channel_type = 6;
  optional uint32 user_sex = 7;

  optional uint32 op_uid = 8;
}

message FakeHoldMicrSpaceResp
{
  optional MicrSpaceInfo open_mic_info = 1; // 麦位
  optional uint32 kick_out_uid = 2;         // [废弃字段 无效]
  repeated MicrSpaceInfo all_mic_list = 3;  // 全体麦位信息 包括各个麦位状态
  optional uint64 server_time_ms  = 4;      // 64bit 毫秒级 服务器时间
}

// 下麦
message SimpleReleaseMicrSpaceReq
{
    required uint32 channel_id = 1;
    required uint32 uid = 2;
    required uint32 switch_flag = 3;

    optional uint32 channel_display_id = 4;
    optional uint32 channel_type = 5;
    optional int32 user_sex = 6;

    optional uint32 op_uid = 7;
}

message SimpleReleaseMicrSpaceResp
{
    optional MicrSpaceInfo close_mic_info = 1;  // 成功被下麦的麦位信息 如果没有下麦那么该值为空
    repeated MicrSpaceInfo all_mic_list = 2;    // 全体麦位信息 包括各个麦位状态
    optional uint64 server_time_ms  = 3;        // 64bit 毫秒级 服务器时间
    optional bool is_auto_disable_mic = 4;      // 是否自动完成了锁麦
}


// 随机上空余的某个麦位
message SimpleRandomHoldMicrSpaceReq
{
    required uint32 channel_id = 1;
    required uint32 uid = 2;
    optional uint32 opt_min_micr_id = 3;  // 随机的麦位ID 将在min - max 之间选择
    optional uint32 opt_max_micr_id = 4;  // 随机的麦位ID 将在min - max 之间选择
}

message SimpleRandomHoldMicrSpaceResp
{
    optional MicrSpaceInfo open_mic_info = 1; // 麦位
    repeated MicrSpaceInfo all_mic_list = 2;  // 全体麦位信息 包括各个麦位状态
    optional uint64 server_time_ms  = 3;      // 64bit 毫秒级 服务器时间
}

message GetMicrListReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
	optional bool force_local_time = 3; // 此值为true则返回服务器本地时间, 用于特殊场景
}

message GetMicrListResp
{
	required uint32 channel_id = 1;
	repeated MicrSpaceInfo all_mic_list = 2;          // 全体麦位信息 包括各个麦位状态
	optional uint32 micr_mode = 3;                    // 麦模式
	optional uint64 server_time_ms  = 4;              // 64bit 毫秒级 服务器时间
}

// 换自己的麦的位置
message ChangeMicrophoneReq
{
	required uint32 op_uid = 1;
	required uint32	channel_id = 2;
	required MicrSpaceInfo to_mic_info = 3; // 目标麦位
	required uint32 switch_flag = 4;

    optional uint32 channel_display_id = 5;
    optional uint32 channel_type = 6;
}

message ChangeMicrophoneResp
{
	required MicrSpaceInfo from_mic_info  = 1; // 如果换成功 源麦位的状态信息
	required MicrSpaceInfo to_mic_info  = 2;   // 如果换成功 目标麦位的状态信息
	optional uint64 server_time_ms  = 3;       // 64bit 毫秒级 服务器时间
	repeated MicrSpaceInfo all_mic_list = 4;   // 当前全量麦位列表信息
	optional uint32 mic_mode = 5;              // 当前麦模式
}

message ResetMicSpaceListReq
{
	required uint32 channel_id = 1;
}

message ResetMicSpaceListResp
{
	repeated MicrSpaceInfo success_mic_space_list = 1;	// 被重置的麦位（麦上有人的麦位）
	repeated MicrSpaceInfo after_mic_space_list = 2;
	required uint32 after_mic_mode = 3;
	required uint64 after_server_time_ms = 4;
}

// 关闭麦位入口
message DisableChannelMicEntryReq
{
	required uint32 op_uid = 1;
	required uint32 channel_id = 2;
	optional uint32 disable_mic_size = 3;
	optional uint32 kick_uid = 4;    // 如果麦位上正好有个银 需要把这个银给踢走
	optional uint32 mic_pos_id = 5;  // 指定 被关闭麦位的ID
}
message DisableChannelMicEntryResp
{
	required uint32 channel_id = 1;
	optional uint32 mic_pos_id = 2;         // 被关闭的麦位ID
	repeated MicrSpaceInfo all_mic_list = 3;// 当前全量麦位列表信息
	optional uint64 server_time_ms  = 4;      // 64bit 毫秒级 服务器时间
}

// 开启麦位入口
message EnableChannelMicEntryReq
{
	required uint32 op_uid = 1;
	required uint32 channel_id = 2;
	optional uint32 mic_pos_id = 3;  // 需要开启的指定麦位ID
	optional bool hold_mic = 4;      // 是否需要同时上麦
}

message EnableChannelMicEntryResp
{
	required uint32 channel_id = 1;
	optional uint32 mic_pos_id = 2;          // 被开启的麦位ID
	repeated MicrSpaceInfo all_mic_list = 3; // 当前全量麦位列表信息
	optional uint64 server_time_ms  = 4;      // 64bit 毫秒级 服务器时间
}

// 对所有空麦位锁麦
message DisableAllEmptyMicrSpaceReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
}

message DisableAllEmptyMicrSpaceResp
{
	repeated uint32 disable_micid_list = 1;   // 被关闭的麦位ID
	repeated MicrSpaceInfo all_mic_list = 2;  // 当前全量麦位列表信息
	required uint32 mic_mode  = 3;            // 麦模式
	required uint64 server_time_ms  = 4;      // 64bit 毫秒级 服务器时间
}


// 设置麦位状态 比如打开/关闭/禁言 麦位 (可以取代之前的麦位开启和麦位关闭命令)
message SetChannelMicSpaceStatusReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
	required MicrSpaceInfo mic_info = 3;
}

message SetChannelMicSpaceStatusResp
{
	repeated MicrSpaceInfo all_mic_list = 1; // 当前全量麦位列表信息
	optional uint32 kicked_uid = 2;	         // 如果麦位上有人 且 请求锁麦 那么原来麦上的用户会被踢下麦
	optional uint64 server_time_ms  = 3;     // 64bit 毫秒级 服务器时间
	optional uint32 mic_mode  = 4;           // mic mode
}

message BatchSetChannelMicSpaceStatusReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
	repeated uint32 mic_id_list = 3;
	required uint32 mic_status = 4;
}

message BatchSetChannelMicSpaceStatusResp
{
	repeated MicrSpaceInfo all_mic_list = 1;         // 当前全量麦位列表信息
	repeated MicrSpaceInfo kicked_micr_list = 2;	 // 如果麦位上有人 且 请求锁麦 那么原来麦上的用户会被踢下麦
	optional uint64 server_time_ms  = 3;             // 64bit 毫秒级 服务器时间
	optional uint32 mic_mode  = 4;                   // mic mode
}


// 踢下麦
message KickoutChannelMicReq
{
	required uint32 op_uid = 1;
	required uint32 channel_id = 2;
	repeated uint32 target_uid_list = 3;
	optional uint32 ban_second = 4; // 踢下麦后 多长时间 禁止上麦。 0为不限制
	required uint32 switch_flag = 5;	// 房间开关flag
}

message KickoutChannelMicResp
{
	required uint32 channel_id = 1;
	repeated uint32 disable_mic_id_list = 2;     // 如果开启了自动锁麦 这里是被锁的麦位ID列表
	repeated MicrSpaceInfo kickout_mic_list = 3; // 成功被踢的麦位列表
	repeated MicrSpaceInfo all_mic_list = 4;     // 当前全量麦位列表信息
	optional uint64 server_time_ms  = 5;         // 64bit 毫秒级 服务器时间
}

// 修改mic模式
message SetChannelMicModeReq
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	required uint32 mic_mode = 3;              // 1主席模式 2自由模式 3娱乐模式 see ga::EChannelMicMode
    optional bool is_disable_all_mic = 4;       // 是否锁定所有的麦位 仅在 HAVE_MIC_SPACE_MODE 模式下有效
    optional bool is_need_hold_mic = 5;         // 本人是否需要上麦 仅在HAVE_MIC_SPACE_MODE模式下有效

}

message SetChannelMicModeResp
{
    required uint32 channel_id = 1;
    required uint32 mic_mode = 2; // 修改后的麦模式

	// 因为本次模式切换 而变化的麦位信息
    repeated MicrSpaceInfo disable_mic_list = 3;
    repeated MicrSpaceInfo enable_mic_list = 4;
    repeated MicrSpaceInfo kickout_mic_list = 5;
    optional MicrSpaceInfo hold_mic_info = 6;

	// 模式设置之后 当前的麦位信息
    repeated MicrSpaceInfo all_mic_list = 7;

    optional uint64 server_time_ms  = 8;      // 64bit 毫秒级 服务器时间

    optional uint32 from_mic_mode = 9; // 修改前的麦模式
}

// 批量获取mic模式
message BatchGetChannelMicModeReq
{
    repeated uint32 channel_id_list = 1;
}

message BatchGetChannelMicModeResp
{
    repeated uint32 mic_mode_list = 1;
}

message GetChannelMicModeReq
{
    required uint32 channel_id = 1;
}

message GetChannelMicModeResp
{
    required uint32 mic_mode = 1;
}

// 创建房间时初始化麦位
message InitCreateMicrSpaceReq
{
  required uint32 uid = 1;
  required uint32 channel_id = 2;
  required uint32 mic_mode = 3;

  optional uint32 channel_display_id = 4;
  optional uint32 channel_type = 5;
}
message InitCreateMicrSpaceResp
{

}

//一次最多请求20个房间，否则会被截断
message BatGetMicrListReq
{
  repeated uint32 channel_id_list = 1;
}

message MicrData {
  required uint32 channel_id = 1;
  repeated MicrSpaceInfo all_mic_list = 2;          // 全体麦位信息 包括各个麦位状态
  optional uint32 micr_mode = 3;                    // 麦模式
  optional uint64 server_time_ms  = 4;              // 64bit 毫秒级 服务器时间
}

message BatGetMicrListResp
{
  repeated MicrData mic_data_list = 1;
}

// 切换玩法时，重新初始化麦位数据，包括麦位数，麦位状态等(之前是SetChannelMicMode，但是麦位模式逐渐淘汰，需要换成这个新的接口)
message ReInitChannelMicDataReq
{
  required uint32 uid = 1;
  required uint32 cid = 2;
  required uint32 mic_num = 3;
  required uint32 mic_mode = 4;               // 兼容之前的接口用
  required uint32 scheme_id = 5;              // 记录是切换到什么玩法时初始化的麦位数据
  optional bool not_kick_out_mic = 6;         // 不踢掉麦上所有人，默认是踢的
  optional bool not_unlock_mic = 7;           // 不解锁已经锁上的麦位，默认是解锁的
  optional bool unmute_mic = 8 ;              // 把禁言的麦位解禁言,默认是不解的

  optional uint32 channel_display_id = 9;
  optional uint32 channel_type = 10;

  optional bool use_new_control_mic = 11;
}
message ReInitChannelMicDataResp
{
  required uint32 channel_id = 1;

  // 因为本次模式切换 而变化的麦位信息
  repeated MicrSpaceInfo enable_mic_list = 2;
  repeated MicrSpaceInfo kickout_mic_list = 3;
  optional MicrSpaceInfo hold_mic_info = 4;
  // 模式设置之后 当前的麦位信息
  repeated MicrSpaceInfo all_mic_list = 5;
  optional uint64 server_time_ms  = 6;      // 64bit 毫秒级 服务器时间

  //兼容之前的接口
  required uint32 mic_mode = 7; // 修改后的麦模式
  optional uint32 from_mic_mode = 8; // 修改前的麦模式
}

service ChannelMic {
    option( tlvpickle.Magic ) = 15223;      // 服务监听端口号
    // 开麦(上麦)
    rpc SimpleHoldMicrSpace( SimpleHoldMicrSpaceReq ) returns( SimpleHoldMicrSpaceResp ){
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "x:u:t:f:";
        option( tlvpickle.Usage ) = "-x <channelID> -u <uid> -t <target mic ID> -f <is force>";
    }

    // 关麦(下麦)
    rpc SimpleReleaseMicrSpace( SimpleReleaseMicrSpaceReq ) returns( SimpleReleaseMicrSpaceResp ){
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "x:u:";
        option( tlvpickle.Usage ) = "-x <channelID> -u <uid>";
    }

    // 获取麦位列表
    rpc GetMicrList( GetMicrListReq ) returns( GetMicrListResp ){
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channelID>";
    }

    // 换麦位
    rpc ChangeMicrophone( ChangeMicrophoneReq ) returns ( ChangeMicrophoneResp ) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channelID>";
    }

    // 重置麦位
    rpc ResetMicSpaceList( ResetMicSpaceListReq ) returns ( ResetMicSpaceListResp ) {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channelID>";
    }

	// 关闭麦位入口
    rpc DisableChannelMicEntry( DisableChannelMicEntryReq ) returns ( DisableChannelMicEntryResp ) {
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channelID>";
    }

	// 开启麦位入口
    rpc EnableChannelMicEntry( EnableChannelMicEntryReq ) returns ( EnableChannelMicEntryResp ) {
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channelID>";
    }

	// 对所有空麦位锁麦
    rpc DisableAllEmptyMicrSpace( DisableAllEmptyMicrSpaceReq ) returns ( DisableAllEmptyMicrSpaceResp ) {
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channelID>";
    }

	// 设置麦位状态 比如打开/关闭/禁言 麦位 (可以取代之前的麦位开启和麦位关闭命令)
    rpc SetChannelMicSpaceStatus( SetChannelMicSpaceStatusReq ) returns ( SetChannelMicSpaceStatusResp ) {
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channelID>";
    }

	// 设置麦位状态 比如打开/关闭/禁言 麦位 (可以取代之前的麦位开启和麦位关闭命令)
    rpc KickoutChannelMic( KickoutChannelMicReq ) returns ( KickoutChannelMicResp ) {
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channelID>";
    }

	// 逐渐淘汰，需要换成ReInitChannelMicData这个新的接口
    rpc SetChannelMicMode( SetChannelMicModeReq ) returns ( SetChannelMicModeResp ) {
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <channelID>";
    }

    rpc BatchGetChannelMicMode( BatchGetChannelMicModeReq ) returns ( BatchGetChannelMicModeResp ) {
        option ( tlvpickle.CmdID ) = 12;
        option ( tlvpickle.OptString ) = "";
        option ( tlvpickle.Usage ) = "";
    }

	// 批量修改麦位的状态
	rpc BatchSetChannelMicSpaceStatus( BatchSetChannelMicSpaceStatusReq ) returns ( BatchSetChannelMicSpaceStatusResp ) {
        option ( tlvpickle.CmdID ) = 13;
        option ( tlvpickle.OptString ) = "";
        option ( tlvpickle.Usage ) = "";
    }

    // 获取麦模式
    rpc GetChannelMicMode ( GetChannelMicModeReq ) returns ( GetChannelMicModeResp ) {
        option ( tlvpickle.CmdID ) = 14;
        option ( tlvpickle.OptString ) = "";
        option ( tlvpickle.Usage ) = "";
    }

    // 上随机的空麦位
    rpc SimpleRandomHoldMicrSpace ( SimpleRandomHoldMicrSpaceReq ) returns ( SimpleRandomHoldMicrSpaceResp ) {
        option ( tlvpickle.CmdID ) = 15;
        option ( tlvpickle.OptString ) = "u:x:";
        option ( tlvpickle.Usage ) = "-u <uid> -x <channelid>";
   }

  // 创建初始的麦位信息
  rpc InitCreateMicrSpace ( InitCreateMicrSpaceReq ) returns ( InitCreateMicrSpaceResp ) {
    option ( tlvpickle.CmdID ) = 16;
    option ( tlvpickle.OptString ) = "u:x:";
    option ( tlvpickle.Usage ) = "-u <uid> -x <channelid>";
  }

  // 假上麦，用于神秘人现身场景,请求上麦的人原来已经在麦位上了，触发更新kafka
  rpc FakeHoldMicrSpace( FakeHoldMicrSpaceReq ) returns( FakeHoldMicrSpaceResp ){
    option( tlvpickle.CmdID ) = 17;
    option( tlvpickle.OptString ) = "x:u:t:f:";
    option( tlvpickle.Usage ) = "-x <channelID> -u <uid> -t <target mic ID> -f <is force>";
  }

  // 批量获取麦位列表,一次最多获取20个房间
  rpc BatGetMicrList( BatGetMicrListReq ) returns( BatGetMicrListResp ){
    option( tlvpickle.CmdID ) = 18;
    option( tlvpickle.OptString ) = "x:";
    option( tlvpickle.Usage ) = "-x <channelID>";
  }

  rpc ReInitChannelMicData(ReInitChannelMicDataReq) returns (ReInitChannelMicDataResp){
    option( tlvpickle.CmdID ) = 19;
    option( tlvpickle.OptString ) = "u:x:n:";
    option( tlvpickle.Usage ) = "-u <uid> -x <channelID> -n <micnum>";
  }
}


