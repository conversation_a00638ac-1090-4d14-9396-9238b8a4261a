syntax="proto2";
import "common/tlvpickle/skbuiltintype.proto";

package channelgame;

enum channelgameStatus
{
	OUT=0;									//房间不在游戏
	IN=1;									//房间在游戏中					
};

message StartChannelGameReq
{
	required uint32 channelId=1;			//房间id
	required uint32 gameId=2;				//房间游戏id
	required uint32 ts=3;					//超时时间	
	repeated uint32 microphone_pos_list=4;		//麦位
}

message ChannelGameResult					//游戏结果
{
    required uint32 microphone_pos=1;		//麦位
	required uint32 random_ret=2;			//麦位对应的结果
}

message StartChannelGameResp
{	
	required uint32 bIsInTheGame=1;
	optional string strGameResult=2;//结果文本显示
	repeated ChannelGameResult gameResult_list=3;
}

message CheckChannelGameStatusReq			//检查房间是否正在进行游戏
{
	required uint32 channelId=1;
}

message CheckChannelGameStatusResp
{
	required uint32 channelGameStatus=1;
}

service ChannelGame 
{
	option( tlvpickle.Magic ) = 14903;														 // 服务监听端口号

	rpc StartChannelGame(StartChannelGameReq) returns( StartChannelGameResp )
	{
		option( tlvpickle.CmdID ) = 1;														 // 命令号
        option( tlvpickle.OptString ) = "n:g:t:m:";											 // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -n<channelId> -g<gameId> -t<ts> -m<micpos1,micpos2,...>";    // 测试工具的命令号帮助
	}

	rpc CheckChannelGameStatus(CheckChannelGameStatusReq) returns( CheckChannelGameStatusResp )
	{
		option( tlvpickle.CmdID ) = 2;														  // 命令号
        option( tlvpickle.OptString ) = "n:";												  // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -n<channelId>";										  // 测试工具的命令号帮助
	}
}