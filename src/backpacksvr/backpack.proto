syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package backpack;

enum LogType
{
    LOG_TYPE_INVALID = 0;
    LOG_TYPE_USE =1;                    // 使用
    LOG_TYPE_EXPIRE =2;                 // 过期
    LOG_TYPE_FRAGMENT_EXCHANGE = 3;     // 兑换碎片
    LOG_TYPE_FRAGMENT_ROLLBACK = 4;     // 回滚碎片
    LOG_TYPE_ITEM_CONVERSION = 5;     // 物品合成
    LOG_TYPE_ROLLBACK = 6;            //物品回滚
    LOG_TYPE_OPRATE_DEDUCT= 7;            //运营扣除
    LOG_TYPE_BUSINESS_DEDUCT = 8;            //业务扣除
    LOG_TYPE_ENERGY_ITEM_CONVERSION = 9;     // 暗黑礼物合成
    LOG_TYPE_ENERGY_ITEM_STAR_TREK_USE = 10; // 星级巡航使用扣除
    LOG_TYPE_CHANNEL_LOTTERY = 11;      // 房间抽奖扣除
    LOG_TYPE_ENERGY_ITEM_CAT_CANTEEN_USE = 12; // 猫猫餐厅使用扣除
    LOG_TYPE_GLORY_WORLD_USE = 13;  //星钻兑换
    LOG_TYPE_GLORY_WORLD_LOTTERY_USE = 14;   //星钻抽奖
}

enum PackageItemType
{
    UNKNOW_ITEM_TYPE=0;
    BACKPACK_PRESENT=1;             // 礼物
    BACKPACK_CARD_RICH_EXP=2;       // 财富经验加速卡
    BACKPACK_CARD_CHARM_EXP=3;      // 每日经验加速卡
    BACKPACK_LOTTERY_FRAGMENT = 4;  // 抽奖碎片,砸蛋获得
    BACKPACK_CARD_RICH_INCR = 5;    // 财富卡
}

// 包裹来源
enum PackageSourceType
{
    UNKNOW_PACKAGE_SOURCE=0;
    PACKAGE_SOURCE_ACTIVITY_PRESENT = 1;    // 礼物活动
    PACKAGE_SOURCE_DAILY_CHECKIN = 2;       // 每日签到任务
    PACKAGE_SOURCE_FIRST_RECHARGE = 3;      // 首充活动
    PACKAGE_SOURCE_OFFICIAL = 4;            // 内容管理后台中发放
    PACKAGE_SOURCE_SMASHEGG = 5;            // 砸蛋活动
    PACKAGE_SOURCE_CONVERSION = 6;          // 碎片兑换
    PACKAGE_SOURCE_AWARD_CENTER = 7;        // 发奖中心
    PACKAGE_SOURCE_AWARD_YUYIN_LIVE_MISSION = 8; // 语音直播任务奖励
    PACKAGE_SOURCE_INTERACTION_INTIMACY_MISSION = 9; // 亲密度任务
    PACKAGE_SOURCE_ITEM_CONVERSION = 10; // 物品合成
    PACKAGE_SOURCE_NOBILITY = 11;  // 贵族系统发放
    PACKAGE_SOURCE_TBEAN_BUY = 12;  // T豆购买礼物
    PACKAGE_SOURCE_CHANNEL_RED_PACKET = 13; // 房间礼物红包
    PACKAGE_SOURCE_SMASH_GOLD_EGG = 14; // 金色转转活动
    PACKAGE_SOURCE_VIP_MILLION = 15; // VIP百万豪礼
    PACKAGE_SOURCE_ENERGY_STONE_CONVERSION = 16; //黑暗礼物合成(能量石相关)
    PACKAGE_SOURCE_DARK_GIFT_BONUS = 17;  // 黑暗礼物奖励（玩法保底奖励）
    PACKAGE_SOURCE_ONE_PIECE = 18;      // 航海寻宝
    PACKAGE_SOURCE_STAR_TREK = 19;      // 星级巡航
    PACKAGE_SOURCE_YOU_KNOW_WHO = 20;   // 神秘人
    PACKAGE_SOURCE_GAME_TICKET_RECHARGE = 21;  // 游戏券充值(购买指定礼物送券)
    PACKAGE_SOURCE_CHANNEL_LOTTERY = 23;    // 房间抽奖
    PACKAGE_SOURCE_CAT_CANTEEN = 24;    // 猫猫餐厅玩法
    PACKAGE_SOURCE_GLORY_WORLD = 25;    // 荣誉世界
    PACKAGE_SOURCE_GOLRY_STORE = 26;    // 荣誉商店
    PACKAGE_SOURCE_GOLRY_MISSION = 27;    // 荣誉任务
}

// 包裹配置
message PackageCfg
{
    uint32 bg_id=1;
    string name=2;
    string desc=3;
    bool is_del=4;
}


message PackageItemCfg
{
    uint32 bg_item_id=1;
    uint32 bg_id=2;
    uint32 item_type=3;         // PackageItemType
    uint32 source_id=4;         // 礼物id/卡片id/碎片id
    uint32 item_count=5;        // 数量
    uint32 fin_time=6;          // 截止时间
    bool is_del=7;
    uint32 weight=8;            // 权重
    uint32 dynamic_fin_time=9;  // 发放后的有效时间(秒)
    uint32 months = 10;         // 发放后第几个月的1号过期，半年=6，一年=12
}

// 功能卡片配置
message FuncCardCfg
{
    uint32 card_id=1;
    uint32 card_type=2;
    string card_name=3;
    string card_desc=4;
    string card_url=5;
    uint32 card_times=6;
    uint32 valid_time=7;
    uint32 is_del=8;
    uint32 uid   = 9;
    uint64 card_value=10; // 数值（财富卡）
}

// 砸蛋得的抽奖碎片
message LotteryFragmentCfg
{
    uint32 fragment_id = 1;           // 添加配置时，不填此字段
    uint32 fragment_type = 2;         // 0默认, 1合并碎片
    string fragment_name = 3;
    string fragment_desc = 4;
    string fragment_url = 5;
    uint32 is_del = 6;
    uint32 fragment_price = 7;        // 后台配置的价格,fragment_price/100 = ￥1
    uint32 is_show_expire_hint = 8; //是否展示过期提醒
}

message UserBackpackItem
{
    uint32 item_type=1;
    uint32 user_item_id=2;
    uint32 item_count=3;
    uint32 fin_time=4;
    uint32 source_id=5;
    uint32 weight=6;         // 物品权重
    uint32 obtain_time=7;
    uint32 source_type = 8; //see  PackageSourceType
    uint32 final_item_count = 9; //
}

message UserBackpackLog
{
    uint32 item_type=1; // PackageItemType
    uint32 item_count=2;
    uint32 source_id=3;
    uint32 log_type=4;  // LogType
    uint32 log_time=5;
}

message AddPackageCfgReq
{
    PackageCfg cfg=1;
}

message AddPackageCfgResp
{
    PackageCfg cfg=1;
}

message DelPackageCfgReq
{
    uint32 bg_id=1;
}

message DelPackageCfgResp
{
}

message GetPackageCfgReq
{
    uint32 offset = 1;
    uint32 limit = 2;
}

message GetPackageCfgResp
{
    repeated PackageCfg cfg_list=1;
    uint32 total_count = 2;
}

message AddPackageItemCfgReq
{
    PackageItemCfg item_cfg=1;
}

message AddPackageItemCfgResp
{
    PackageItemCfg item_cfg=1;
}

message ModPackageItemCfgReq
{
    PackageItemCfg item_cfg=1;
}

message ModPackageItemCfgResp
{
}

message DelPackageItemCfgReq
{
    uint32 bg_id=1;
    uint32 bg_item_id=2;
}

message DelPackageItemCfgResp
{
}

message GetPackageItemCfgReq
{
    uint32 bg_id=1;
    repeated uint32 bg_id_list = 2;
}

message PackageItemCfgList
{
    repeated PackageItemCfg item_cfg_list=1;
}

message GetPackageItemCfgResp
{
    repeated PackageItemCfg item_cfg_list=1;
    repeated PackageItemCfgList package_item_cfg_list=2;
}

// user backpack
message GiveUserPackageReq
{
    uint32 uid=1;
    uint32 bg_id=2;
    uint32 num=3;
    uint32 source=4;            // 包裹来源 PackageSourceType
    string order_id = 5;        //幂等订单号
    uint32 expire_duration = 6; // 按照自然天过期该物品
                                // example:expire_duration=1,当前时间为 2019-10-28 10:42:00 这该物品过期时间为 2019-10-29 00:00:00
                                // note :expire_duration>=1 则会忽略 包裹配置的 dynamic_fin_time, months
    string source_app_id = 7;   // 来源明细
    uint32 total_price = 8;     // 包裹总价值
    uint32 outside_time = 9;    // 赠送包裹时间
}

message GiveUserPackageResp
{

}

message UseBackpackExtraInfo
{
    uint32 target_uid = 1; //收礼人
    uint32 channel_id = 2;
    uint32 use_count = 3;
    string deal_token = 4;
}

message UseBackpackItemReq
{
    uint32 uid=1;
    uint32 item_type=2;
    uint32 user_item_id=3;
    uint32 source_id=4;
    string order_id=5;
    uint32 use_count=6;
    uint32 outside_time = 7;
    repeated string order_id_list = 8; //for batch send
    uint32 item_price = 9;
    uint32 price_type = 10;  // 1红钻 2t豆

    repeated uint32 target_uid_list = 11; //for batch send
    UseBackpackExtraInfo extra_info = 12;
    uint32 use_reasion_type = 13; //消耗的理由enum LogType
}
message UseBackpackItemResp
{
    uint32 remain=1;
    string deal_token = 2;
    uint32 fin_time = 3;
}


message GetUseItemOrderInfoReq
{
    string order_id = 1;
}

message GetUseItemOrderInfoResp
{
    UseOrderDetail use_order_detail = 1;
    UseBackpackExtraInfo use_order_extra_info = 2;
}

message ProcBackpackItemTimeoutReq
{
    uint32 user_item_id=1;
    uint32 uid=2;
    uint32 item_type=3;
    uint32 source_id=4;
    uint32 item_count=5;
    uint32 fin_time=6;
}
message ProcBackpackItemTimeoutResp
{

}

message GetUserBackpackReq
{
    uint32 uid=1;
}
message GetUserBackpackResp
{
    repeated UserBackpackItem user_item_list=1;
    int64 last_obtain_ts = 2;          // 最后获得背包礼物奖励的时间
}

message GetUserFuncCardUseReq
{
    uint32 uid=1;
}

message GetUserFuncCardUseResp
{
    repeated FuncCardCfg user_item_list=1;
}

message AddFuncCardCfgReq
{
    FuncCardCfg card_cfg=1;
}

message AddFuncCardCfgResp
{
}

message BatchGetUserFuncCardUseReq
{
    repeated uint32 uid_list = 1;
}

message BatchGetUserFuncCardUseResp{
    repeated FuncCardCfg user_item_list = 1;
}

message DelFuncCardCfgReq
{
    uint32 card_id=1;
}

message DelFuncCardCfgResp
{
}

message GetFuncCardCfgReq
{
    repeated uint32 card_id_list=1;
}

message GetFuncCardCfgResp
{
    repeated FuncCardCfg card_cfg_list=1;
}

message CheckUserFuncCardUseReq
{
    uint32 uid=1;
    uint32 card_type=2;
}

message CheckUserFuncCardUseResp
{
   bool is_use=1;
}

message SetUserFuncCardUseReq
{
    uint32 uid=1;
    uint32 card_type=2;
    uint32 card_id=3;
}
message SetUserFuncCardUseResp
{
}

message GetUserBackpackLogReq
{
    uint32 uid = 1;
    uint32 begin_time = 2;
    uint32 end_time = 3;
    uint32 item_type = 4;   // PackageItemType
    uint32 source_id = 5;
    uint32 log_type = 6;    // LogType
}

message GetUserBackpackLogResp
{
    repeated UserBackpackLog log_list = 1;
}

// 此接口暂时不支持添加卡片
message AddItemCfgReq{
  uint32 item_type = 1;
  bytes  item_cfg = 2; //填入具体类型的 Cfg;example：item_type==BACKPACK_LOTTERY_FRAGMENT, 将LotteryFragmentCfg 序列化填入
}

message AddItemCfgResp{
  uint32 item_type = 1;
  bytes  item_cfg = 2; // 补充了 id
}

message DelItemCfgReq{
  uint32 item_type = 1;
  /*uint32 item_source_id = 2;*/
  /*uint32 item_source_type = 3;*/
  bytes  item_cfg = 2; //填入具体类型的 Cfg;example：item_type==BACKPACK_LOTTERY_FRAGMENT, 将LotteryFragmentCfg 序列化填入
}

message DelItemCfgResp{
}

message GetItemCfgReq{
  uint32 item_type = 1;
  repeated uint32 item_source_id_list = 2;
  bool get_all = 3;                         // true;忽略 item_id_list,获取类型为 item_type 的 所有的 item 的配置
}

message GetItemCfgResp{
  repeated bytes item_cfg_list = 1;
}

enum FREEZETYPE{
  FREEZETYPE_UNVALID = 0;               // 无效的类型
  FREEZETYPE_PREPARE = 1;               // 初始化操作--预扣除
  FREEZETYPE_COMMIT = 2;                // 操作确认
  FREEZETYPE_ROLLBACK = 3;              // 操作回滚
}

message UseItemInfo
{
    uint32 item_type = 1;
    uint32 user_item_id = 2;
    uint32 source_id = 3;
    uint32 use_count = 4;
    uint32 total_price = 5;
}

message TransactionInfo{
    uint32 freeze_type = 1;       //see FREEZETYPE
    uint32 oper_time = 2;
    string order_id = 3;
    uint32 expire_time = 4;
}

//only support BACKPACK_LOTTERY_FRAGMENT
//TODO support other type  (2019-10-22,T1035)
//doc: see FreeZeItem.md
message FreeZeItemReq
{
    TransactionInfo tansacation_info = 1;
    repeated UseItemInfo Item_info_list = 2;
    uint32 uid = 3;
}

message FreeZeItemResp
{
}

message UserPackageSum{
  uint32 item_type = 1;
  uint32 item_id=2;
  uint32 item_count=3;
}

message GetUserPackageSumReq
{
  uint32 uid = 1;
  uint32 item_type = 2;
  uint32 item_id = 3;
}

message GetUserPackageSumResp
{
  UserPackageSum item_sum = 1;
}


message GetOrderCountByTimeRangeReq
{
    uint32 begin_time = 1;
    uint32 end_time = 2;
    uint32 log_type = 3;
    string paras = 4;
}

message GetOrderCountByTimeRangeResp
{
    uint32 count = 1; //订单数量
    uint32 use_count = 2; //消耗道具总数
    uint32 value = 3;
}

message GetOrderListByTimeRangeReq
{
    uint32 begin_time = 1;
    uint32 end_time = 2;
    uint32 log_type = 3;
}

message GetOrderListByTimeRangeResp
{
    repeated string order_list = 1;
}

message TransformOpt
{
    uint32 uid = 1;
    uint32 source = 2;   // 包裹来源 PackageSourceType
    uint32 log_type = 3; //消耗原因LogType
    uint32 outside_time = 4; //外部服务时间
    string order_id = 5; //订单号

    uint32 bg_id = 6; //可选
    uint32 bg_num = 7; //可选
    uint32 material_price = 8;//材料价格
    uint32 gain_price = 9; //获得物品价格
}

message ConversionItemReq
{
    uint32 uid = 1;
    uint32 outside_time = 2;  // 操作时间
    uint32 bg_id = 3;    // 合成的包裹ID
    uint32 bg_num = 4;   //包裹数量
    uint32 source = 5;   // 包裹来源 PackageSourceType
    uint32 material_price = 6; // 原料总价值
    uint32 conversion_price = 7;  // 合成包裹价值
    string order_id = 8;  // 幂等订单号
    repeated UseItemInfo material_item_list = 9; // 合成消耗物品列表
}

message ConversionItemResp
{
}

message RollBackUserItemReq {
    uint32 uid = 1;
    uint32 create_time = 2; //背包月表中的create_time
    string origin_order_id = 3;   // user_backpack_month_v2_xx 表中需要回退的订单ID
}

message RollBackUserItemResp {
}

//批量回滚相关

message DeductItem{
  uint32 item_type = 1;
  uint32 source_id = 2;
  uint32 count = 3;
}

message DeductDetail {
  uint32 uid = 1;
  repeated DeductItem item_list = 2;
  uint32 count = 3;
  uint32 source_type = 4;
  bool is_all_source = 5;
}

enum DeductType
{
    UNKNOW_DEDUCT_TYPE=0;
    OPRATE_DEDUCT=1;        // 运营扣除（手动）
    BUSINESS_DEDUCT=2;      // 业务扣除
}

message BatchDeductUserItemReq {
  repeated DeductDetail deduct_list = 1; //要回滚物品的信息
  string oper = 2; // 操作者
  string order_id = 3;   // 订单id，用于防止重复回滚
  uint32 deduct_type = 4; // DeductType
}

enum DeductFailType{
  DEDUCTFAILTYPE_UNVALID = 0;               // 未知类型
  DEDUCTFAILTYPE_UID_NOT_EXIST = 1;               // 错误类型：uid不存在
  DEDUCTFAILTYPE_ITEM_NOT_ENOUGH = 2;                // 错误类型：物品数量不足
  DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL = 3;                // 错误类型：扣除物品失败
}

message DeductResult {
  uint32 uid = 1;
  repeated DeductItem success_item_list = 2;
  repeated DeductItem fail_item_list = 3;
  uint32 fail_type = 4; //失败类型DeductFailType
}

message BatchDeductUserItemResp {
  repeated DeductResult deduct_list = 1; //实际回滚物品的信息
}

message UseOrderDetail
{
    uint32 uid = 1;
    string order_id = 2;
    uint32 source_id = 3;
    uint32 item_type = 4;
    uint32 use_count = 5;
    uint32 price_type = 6;
    uint32 create_time = 7;
    uint32 user_item_id = 8;
}


//获得订单数据
message TimeRangeReq {
  int64 begin_time = 1;
  int64 end_time = 2;
  string params = 3;
}

//响应order_id个数
message CountResp {
  uint32 count = 1;
  uint32 value = 2; //总价值，如果没有填0
}

//响应orderId详情
message OrderIdsResp {
  repeated string order_ids = 1;
}



service Backpack {
    option( tlvpickle.Magic ) = 15637;      // 服务监听端口号

    rpc AddPackageCfg( AddPackageCfgReq ) returns( AddPackageCfgResp ){
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "n:d:";
        option( tlvpickle.Usage ) = "-n <name> -d <desc>";
    }

    rpc GetPackageCfg( GetPackageCfgReq ) returns( GetPackageCfgResp ){
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc DelPackageCfg( DelPackageCfgReq ) returns( DelPackageCfgResp ){
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <bg_id>";
    }

    rpc AddPackageItemCfg( AddPackageItemCfgReq ) returns( AddPackageItemCfgResp ){
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "x:t:s:n:m:w:y:";
        option( tlvpickle.Usage ) = "-x <bg_id> -t <type> -s <source_id> -n <count> -m <fin_time> -w <weight> [-y <dynamic_fin_time>]";
    }

    rpc GetPackageItemCfg( GetPackageItemCfgReq ) returns( GetPackageItemCfgResp ){
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <bg_id>";
    }

    rpc ModPackageItemCfg( ModPackageItemCfgReq ) returns( ModPackageItemCfgResp ){
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "a:x:t:s:n:m:w:y:";
        option( tlvpickle.Usage ) = "-a <bg_item_id> -x <bg_id> -t <type> -s <source_id> -n <count> -m <fin_time> -w <weight> [-y <dynamic_fin_time>]";
    }

    rpc DelPackageItemCfg( DelPackageItemCfgReq ) returns( DelPackageItemCfgResp ){
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "x:s:";
        option( tlvpickle.Usage ) = "-x <bg_id> -s<item_id>";
    }

    rpc AddFuncCardCfg( AddFuncCardCfgReq ) returns( AddFuncCardCfgResp ){
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "x:n:t:s:";
        option( tlvpickle.Usage ) = "-x <type> -n <name> -t <times> -s <valid_time>";
    }

    rpc DelFuncCardCfg( DelFuncCardCfgReq ) returns( DelFuncCardCfgResp ){
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <card_id>";
    }

    rpc GetFuncCardCfg( GetFuncCardCfgReq ) returns( GetFuncCardCfgResp ){
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc GiveUserPackage( GiveUserPackageReq ) returns( GiveUserPackageResp ){
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "u:b:n:s:t:x:";
        option( tlvpickle.Usage ) = "-u <uid> -b <bg_id> -n <num> -s <source> -x<order_id>";
    }

    rpc UseBackpackItem( UseBackpackItemReq ) returns( UseBackpackItemResp ){
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "u:t:i:s:";
        option( tlvpickle.Usage ) = "-u <uid> -t <item_type> -i <user_item_id> -s <source_id>";
    }

    rpc GetUserBackpack( GetUserBackpackReq ) returns( GetUserBackpackResp ){
        option( tlvpickle.CmdID ) = 13;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc CheckUserFuncCardUse( CheckUserFuncCardUseReq ) returns( CheckUserFuncCardUseResp ){
        option( tlvpickle.CmdID ) = 14;
        option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u <uid> -t <card_type>";
    }

    rpc SetUserFuncCardUse( SetUserFuncCardUseReq ) returns( SetUserFuncCardUseResp ){
        option( tlvpickle.CmdID ) = 15;
        option( tlvpickle.OptString ) = "u:t:d:";
        option( tlvpickle.Usage ) = "-u <uid> -t <card_type> -d <card_id>";
    }

    rpc GetUserFuncCardUse( GetUserFuncCardUseReq ) returns( GetUserFuncCardUseResp ){
        option( tlvpickle.CmdID ) = 16;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc ProcBackpackItemTimeout( ProcBackpackItemTimeoutReq ) returns( ProcBackpackItemTimeoutResp ){
        option( tlvpickle.CmdID ) = 17;
        option( tlvpickle.OptString ) = "t:u:i:s:n:m:";
        option( tlvpickle.Usage ) = "-t <user_item_id> -u <uid> -i <item_type> -s <source_id> -n <item_count> -m <fin_time>";
    }

    rpc GetUserBackpackLog( GetUserBackpackLogReq ) returns( GetUserBackpackLogResp ){
        option( tlvpickle.CmdID ) = 18;
        option( tlvpickle.OptString ) = "u:t:i:s:b:e:";
        option( tlvpickle.Usage ) = "-u <uid> [-t <log_type 1.use 2.expire> -i <item_type 1.present> -s <source_id> -b <begin_time> -e <end_time>]";
    }

    rpc AddItemCfg ( AddItemCfgReq ) returns ( AddItemCfgResp ){
        option( tlvpickle.CmdID ) = 19;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc DelItemCfg ( DelItemCfgReq ) returns (DelItemCfgResp){
        option( tlvpickle.CmdID ) = 20;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetItemCfg ( GetItemCfgReq ) returns (GetItemCfgResp){
        option( tlvpickle.CmdID ) = 21;
        option( tlvpickle.OptString ) = "t:";
        option( tlvpickle.Usage ) = "-t:<item_type>";
    }

    rpc FreeZeItem (FreeZeItemReq) returns (FreeZeItemResp)
    {
        option( tlvpickle.CmdID ) = 22;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetUserPackageSum(GetUserPackageSumReq) returns (GetUserPackageSumResp)
    {
        option( tlvpickle.CmdID ) = 23;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetOrderCountByTimeRange(GetOrderCountByTimeRangeReq) returns (GetOrderCountByTimeRangeResp)
    {
        option( tlvpickle.CmdID ) = 24;
        option( tlvpickle.OptString ) = "b:e:";
        option( tlvpickle.Usage ) = "-b <begin_time> -e<end_time>";
    }

    rpc GetOrderListByTimeRange(GetOrderListByTimeRangeReq) returns (GetOrderListByTimeRangeResp)
    {
        option( tlvpickle.CmdID ) = 25;
        option( tlvpickle.OptString ) = "b:e:";
        option( tlvpickle.Usage ) = "-b <begin_time> -e<end_time>";
    }

    rpc ConversionItem(ConversionItemReq) returns (ConversionItemResp)
    {
        option( tlvpickle.CmdID ) = 26;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc RollBackUserItem(RollBackUserItemReq) returns (RollBackUserItemResp)
    {
        option( tlvpickle.CmdID ) = 27;
        option( tlvpickle.OptString ) = "u:t:x:";
        option( tlvpickle.Usage ) = "";
    }

    rpc BatchDeductUserItem(BatchDeductUserItemReq) returns (BatchDeductUserItemResp)
    {
        option( tlvpickle.CmdID ) = 28;
        option( tlvpickle.OptString ) = "u:t:x:";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetUseItemOrderInfo(GetUseItemOrderInfoReq) returns (GetUseItemOrderInfoResp)
    {
        option( tlvpickle.CmdID ) = 29;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetTimeRangeOrderData( TimeRangeReq ) returns ( CountResp )
    {
        option( tlvpickle.CmdID ) = 30;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetTimeRangeUseOrderData( TimeRangeReq ) returns ( CountResp )
    {
        option( tlvpickle.CmdID ) = 31;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetTimeRangeOrderList( TimeRangeReq ) returns ( OrderIdsResp )
    {
        option( tlvpickle.CmdID ) = 32;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "";
    }

    rpc BatchGetUserFuncCardUse( BatchGetUserFuncCardUseReq ) returns ( BatchGetUserFuncCardUseResp )
    {
        option( tlvpickle.CmdID ) = 33;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "";
    }
}
