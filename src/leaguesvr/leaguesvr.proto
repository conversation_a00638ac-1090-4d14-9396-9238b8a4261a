syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package leaguesvr;

enum GroupType {
	GROUP_FINAL = 0;
	QQAndroid = 1;
	QQiOS = 2;
	WechatAndroid = 3;
	WechatiOS = 4;
}

enum LeagueState {
	Regular = 1;		//常规赛
	RegularExtra = 2;	//附加赛
	Promotion = 3;		//晋级赛
	SemiFinal = 4;		//半决赛
	Final = 5;			//总决赛
	ThirdWinner = 6;	//季军赛
	Complete = 7;		//已结束
}

enum MatchState {
	NotStart = 0;	// 未开始
	Finished = 1;	// 结束
	DirectWin = 2;	// 轮空
}

// --比赛信息
message CreateLeagueReq{
	required uint32 game_id = 1;
	required uint64 time_from = 2;
	required uint64 time_to = 3;
}
message CreateLeagueResp{
	required uint32 league_id = 1;
}

message CreateLeagueTeamReq {
	required uint32 league_id = 1;
	required uint32 team_id = 2;
	required uint32 group_type = 3;//分区组
	required bool is_seed = 4;//种子队
}

message LeagueResult{
	required uint32 team_from = 1;
	required uint32 team_to = 2;
	optional uint32	from_wins = 3;
	optional uint32	to_wins = 4;
	optional uint32 match_type = 5;
	optional uint32	match_round = 6;
	repeated string from_img_list = 7;//返回结果时用
	repeated string to_img_list = 8;//返回结果时用
	optional uint32 audit_state = 9;//返回结果时用
	optional uint64 time_from = 10;
	optional uint64 time_to = 11;
	optional uint32 match_state = 12;
	optional uint32 match_id = 13;
}
message LeagueResultSet{
	required GroupType group_type = 1;
	repeated LeagueResult result_list = 2;
}

message AddLeagueResultReq {
	required uint32	league_id = 1;
	required LeagueResult result = 2;
}

// 获取比赛结果
message GetTeamResultReq {
	required uint32	league_id = 1;
	required uint32 team_id = 2;
}
message GetTeamResultResp {
	repeated LeagueResult result_list = 1;
	required uint32 score = 2;
	required uint32 win = 3;
	required uint32 lose = 4;
	required uint32 draw = 5;
	required uint32 match_type = 6;
	required uint32 match_round = 7;
	required uint32 group_type = 8;
}

message GetAllTeamScoreReq {
	required uint32 league_id = 1;
	optional uint32 group_type = 2;
	optional uint32 from_index = 3;
	optional uint32 to_index = 4;
}
message TeamScore {
	required uint32 team_id = 1;
	required uint32 score = 2;
	required uint32 win = 3;
	required uint32 lose = 4;
	required uint32 draw = 5;
}
message TeamScoreSet {
	required uint32 group_type = 1;
	repeated TeamScore team_score_list = 2;
}
message GetAllTeamScoreResp {
	repeated TeamScoreSet team_score_set_list = 1;
}

message GetTeamScoreByIdReq {
	required uint32 league_id = 1;
	repeated uint32 team_id_list = 2;
}
message GetTeamScoreByIdResp {
	repeated TeamScore team_score_list = 2;
}

message GetLeagueResultByStateReq {
	required uint32 league_id = 1;
	optional uint32 match_type = 2;
	optional uint32 match_round = 3;
	optional uint32 group_type = 4;
	optional uint32 from_index = 5;
	optional uint32 to_index = 6;
}
message GetLeagueResultByStateResp {
	repeated LeagueResultSet result_set_list = 1;
}

message GetLeagueResultByIdReq {
	required uint32 league_id = 1;
	optional uint32 match_type = 2;
	optional uint32 match_round = 3;
	optional uint32 group_type = 4;
	required uint32 team_id = 5;
}
message GetLeagueResultByIdResp {
	required LeagueResult league_result = 1;
}

message LeagueSchedule {
	required uint32 team_from = 1;
	required uint32 team_to = 2;
	required uint64	timestamp_from = 3;
	required uint64	timestamp_to = 4;
	required uint32 match_type = 5;
	required uint32 match_round = 6;
}
message LeagueScheduleSet {
	required GroupType group_type = 1;
	repeated LeagueSchedule schedule_list = 2;
}

message GetScheduleByTeamIdReq {
	required uint32	league_id = 1;
	required uint32 team_id = 2;
}

message GetScheduleByTeamIdResp {
	repeated LeagueSchedule schedule_list = 1;
}

message GetScheduleByDateReq {
	required uint32	league_id = 1;
	required uint64 timestamp = 2;
}

message GetScheduleByDateResp {
	repeated LeagueScheduleSet schedule_set_list = 1;
}


message GetLeagueScheduleByStateReq {
	required uint32 league_id = 1;
	optional uint32 group_type = 2;
	optional uint32 from_index = 3;
	optional uint32 to_index = 4;
	optional bool force_get = 5;
	optional bool time_limit = 6;
}
message GetLeagueScheduleByStateResp {
	repeated LeagueScheduleSet schedule_set_list = 1;
}

message GenLeagueScheduleReq {
	required uint32	league_id = 1;
	required uint64	timestamp_from = 2;
	required uint64	timestamp_to = 3;
}

// 联赛信息
message GetLeagueInfoReq {
	required uint32 league_id = 1;
}

message GetLeagueInfoResp {
	required uint32 game_id = 1;
	required uint32 match_type = 2;
	required uint32 match_round = 3;
	required uint32 league_state = 4;
	required uint64 time_from = 5;
	required uint64 time_to = 6;
	required uint32 regular_match_round = 7;
	required uint32 promotion_team_count = 8;
}

//更新上传的比赛截图
message UploadSnapshotReq{
	required uint32	league_id = 1;
	required uint32 team_id = 2;
	repeated string img_list = 3;
	optional uint32 match_type = 4;	
	optional uint32 match_round = 5;	
	required bool force_update = 6;
}

message GetSnapshotReq {
	required uint32 league_id = 1;
	optional uint64 timestamp_from = 2;
	optional uint64 timestamp_to = 3;
	optional uint32 team_id = 4;
	optional uint32 match_type = 5;
	optional uint32 match_round = 6;
	optional GroupType group_type = 7;
}

message GetSnapshotResp {
	repeated LeagueResultSet result_set_list = 1;
}

message LeagueTeamCount {
	required GroupType group_type = 1;
	required uint32 count = 2;
}

message GetLeagueTeamCountReq {
	required uint32 league_id = 1;
	optional uint32 match_type = 2;
	optional uint32 match_round = 3;
}

message GetLeagueTeamCountResp {
	repeated LeagueTeamCount team_count_list = 1;
}

message GetExtraMatchTeamReq {
	required uint32 league_id = 1;
}
message ExtraMatchTeamSet {
	required uint32 group_type = 1;
	optional uint32 quota = 2;
	repeated uint32 team_list = 3;
}
message GetExtraMatchTeamResp {
	repeated ExtraMatchTeamSet team_set_list = 1;
}

message SetNextMatchTypeReq {
	required uint32 league_id = 1;
	repeated ExtraMatchTeamSet team_set_list = 2;
	required uint64 time_from = 3;
	required uint64 time_to = 4;
}

message GetAllMatchByStateReq {
	required uint32 league_id = 1;
	required uint32 match_type = 2;
	required uint32 match_round = 3;
	optional uint32 group_type = 4;
	optional uint32 from_index = 5;
	optional uint32 to_index = 6;
}
message GetAllMatchByStateResp {
	repeated LeagueResultSet result_set_list = 1;
}

message AuditNotResultMatchReq{
	required uint32 league_id = 1;
}

service LeagueSvr{
    option( tlvpickle.Magic ) = 15350;

	rpc CreateLeague( CreateLeagueReq ) returns ( CreateLeagueResp ) {
        option( tlvpickle.CmdID ) = 1;										// 命令号
	    option( tlvpickle.OptString ) = "g:s:e:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id> -s <time_from> -e <time_to>";// 测试工具的命令号帮助	
    }

	rpc AddLeagueResult ( AddLeagueResultReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 2;										// 命令号
	    option( tlvpickle.OptString ) = "l:s:e:q:t:m:r:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -s <from_id> -e <to_id> -q <from_win> -t <to_win> -m<match_type> -r<match_round>";	// 测试工具的命令号帮助	
    }

	rpc GetTeamResult( GetTeamResultReq ) returns ( GetTeamResultResp ) {
        option( tlvpickle.CmdID ) = 3;										// 命令号
	    option( tlvpickle.OptString ) = "l:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -t <team_id>";	// 测试工具的命令号帮助	
    }

	rpc GetScheduleByTeamId ( GetScheduleByTeamIdReq ) returns ( GetScheduleByTeamIdResp ) {
        option( tlvpickle.CmdID ) = 4;										// 命令号
	    option( tlvpickle.OptString ) = "l:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -t <team_id>";	// 测试工具的命令号帮助	
    }

	rpc GetScheduleByDate ( GetScheduleByDateReq ) returns ( GetScheduleByDateResp ) {
        option( tlvpickle.CmdID ) = 5;										// 命令号
	    option( tlvpickle.OptString ) = "l:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -t <date e.g.2016-06-30 00:00:00>";	// 测试工具的命令号帮助	
    }

	rpc GenLeagueSchedule ( GenLeagueScheduleReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 6;				// 命令号
	    option( tlvpickle.OptString ) = "l:s:e:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -s <time_from> -e <time_to>";	// 测试工具的命令号帮助	
    }
	
	rpc UploadSnapshot ( UploadSnapshotReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 7;				// 命令号
	    option( tlvpickle.OptString ) = "l:t:k:m:r:p:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -t <team_id> -k <key> -m<match_type> -r<round> -p <force_update>";	// 测试工具的命令号帮助	
    }

	rpc CreateLeagueTeam ( CreateLeagueTeamReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 8;				// 命令号
	    option( tlvpickle.OptString ) = "l:t:g:s:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -t <team_id> -g <group_type> -s <is_seed>";	// 测试工具的命令号帮助	
    }

	rpc GetLeagueInfo ( GetLeagueInfoReq ) returns ( GetLeagueInfoResp ) {
        option( tlvpickle.CmdID ) = 9;				// 命令号
	    option( tlvpickle.OptString ) = "l:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> ";	// 测试工具的命令号帮助	
    }
	rpc GetLeagueResultByState ( GetLeagueResultByStateReq ) returns ( GetLeagueResultByStateResp ) {
        option( tlvpickle.CmdID ) = 10;				// 命令号
	    option( tlvpickle.OptString ) = "l:m:r:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -m <match_type> -r <match_round>";	// 测试工具的命令号帮助	
    }
	rpc GetLeagueScheduleByState ( GetLeagueScheduleByStateReq ) returns ( GetLeagueScheduleByStateResp ) {
        option( tlvpickle.CmdID ) = 11;				// 命令号
	    option( tlvpickle.OptString ) = "l:g:s:e:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -g<group_type> -s<from_index> -e<to_index>";	// 测试工具的命令号帮助	
    }
	rpc GetSnapshot ( GetSnapshotReq ) returns ( GetSnapshotResp ) {
        option( tlvpickle.CmdID ) = 12;				// 命令号
	    option( tlvpickle.OptString ) = "l:s:e:t:m:r:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -s <time_from 0 invalid> -e <time_to> -t <team_id> -m <match_type> -r <round>";	// 测试工具的命令号帮助	
    }
	rpc GetAllTeamScore ( GetAllTeamScoreReq ) returns ( GetAllTeamScoreResp ) {
        option( tlvpickle.CmdID ) = 13;				// 命令号
	    option( tlvpickle.OptString ) = "l:g:s:e:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -g <group_type 0 invalid> -s <from_index> -e <to_index>";	// 测试工具的命令号帮助	
    }
	rpc GetExtraMatchTeam ( GetExtraMatchTeamReq ) returns ( GetExtraMatchTeamResp ) {
        option( tlvpickle.CmdID ) = 14;				// 命令号
	    option( tlvpickle.OptString ) = "u:l:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -l <league_id>";	// 测试工具的命令号帮助	
    }
	rpc SetNextMatchType ( SetNextMatchTypeReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 15;				// 命令号
	    option( tlvpickle.OptString ) = "u:l:g:s:e:t:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -l <league_id> -g <group_type> -s <from_time> -e <to_time> -t<id list, split by->";	// 测试工具的命令号帮助	
    }
	rpc GetLeagueTeamCount ( GetLeagueTeamCountReq ) returns ( GetLeagueTeamCountResp ) {
        option( tlvpickle.CmdID ) = 16;				// 命令号
	    option( tlvpickle.OptString ) = "l:m:r:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -m <match_type> -r <round>";	// 测试工具的命令号帮助	
    }

	rpc GetTeamScoreById ( GetTeamScoreByIdReq ) returns ( GetTeamScoreByIdResp ) {
        option( tlvpickle.CmdID ) = 17;				// 命令号
	    option( tlvpickle.OptString ) = "l:t:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -t <id list, split by->";	// 测试工具的命令号帮助	
    }

	rpc GetAllMatchByState( GetAllMatchByStateReq ) returns ( GetAllMatchByStateResp ) {
        option( tlvpickle.CmdID ) = 18;				// 命令号
	    option( tlvpickle.OptString ) = "l:g:s:e:m:r:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -g <group_type 0 invalid> -s <from_index> -e <to_index> -m<match_type> -r<round>";	// 测试工具的命令号帮助	
    }
	
	rpc GetLeagueResultById( GetLeagueResultByIdReq ) returns ( GetLeagueResultByIdResp ) {
        option( tlvpickle.CmdID ) = 19;				// 命令号
	    option( tlvpickle.OptString ) = "l:g:t:m:r:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id> -g <group_type 0 invalid> -t <team_id> -m <match_type> -r <round>";	// 测试工具的命令号帮助	
    }

	rpc AuditNotResultMatch( AuditNotResultMatchReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 20;				// 命令号
	    option( tlvpickle.OptString ) = "l:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <league_id>";	// 测试工具的命令号帮助	
    }    
}
