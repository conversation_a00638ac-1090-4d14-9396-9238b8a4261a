syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package rebateprotect;



message RebateProtectionInfo 
{	
	required uint32 uid = 1;                   // 触发保护期绑定的uid
	required uint32 bind_guild_id = 2;         // 保护期内绑定的公会
	required uint32 protection_begin_ts = 3;   // 保护期开始的时间
}


// 检测保护期设置
message CheckRebateProtectionInfoReq
{
	required uint32 uid= 1;
}

message CheckRebateProtectionInfoResp
{
	required RebateProtectionInfo protection_info = 1;
}

// 设置保护期
message SetRebateProtectionInfoReq
{
	required RebateProtectionInfo protection_info = 1;
}
message SetRebateProtectionInfoResp
{
	
}

// 获取游戏的返利比例值
message GetGameRebateRatioReq
{
	required uint32 game_id= 1;
	required uint32 guild_id = 2;
}

message GetGameRebateRatioResp
{
	required uint32 game_id= 1;
	required double ratio= 2;
}

// 设置游戏的返利比例值
message SetGameRebateRatioReq
{
	required uint32 ratio_ptt = 1;   // per ten thousand 万分之几 整数 不能超过10000
	repeated uint32 game_list = 2;   // tt游戏ID
	repeated uint32 guild_list = 3;  // 公会ID
}

message SetGameRebateRatioResp
{
}

// 批量获取游戏返利比例值
message GameRebateRatio
{
	required uint32 game_id = 1;
	required uint32 guild_id = 2;
	required double ratio = 3;
}

message BatGetGameRebateRatioReq
{
	required uint32 game_id = 1;
	required uint32 guild_id = 2;
}

message BatGetGameRebateRatioResp
{
	repeated GameRebateRatio ratio_list = 1;
}

// 删除游戏返利信息
message DelGameRebateRatioReq
{
	repeated uint32 game_list = 1;   // tt游戏ID
	repeated uint32 guild_list = 2;  // 公会ID
}
message DelGameRebateRatioResp
{
	required uint32 del_cnt = 1;   //删除数量
}


// 获取首充号帐号对应的领取者的UID
message GetFirstVoucherApplyUIDReq
{
	required string first_voucher_acc = 1;
}

message GetFirstVoucherApplyUIDResp
{
	required string first_voucher_acc = 1;
	required uint32 apply_uid = 2;
}

service rebateprotect {
    option( tlvpickle.Magic ) = 15305;

	// 检测保护期设置
    rpc CheckRebateProtectionInfo( CheckRebateProtectionInfoReq ) returns( CheckRebateProtectionInfoResp ){
		option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
	}

	// 设置
	rpc SetRebateProtectionInfo( SetRebateProtectionInfoReq ) returns( SetRebateProtectionInfoResp  ){
		option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:g:";
        option( tlvpickle.Usage ) = "-u <uid> -g <guild>";
	}
	
	// 设置返利比例值
	rpc SetGameRebateRatio (SetGameRebateRatioReq ) returns( SetGameRebateRatioResp  ){
		option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "g:x:r:";
        option( tlvpickle.Usage ) = "-g <game> -x <guildid> -r <ratio_ptt>";
	}
	
	// 获取返利比例值
	rpc GetGameRebateRatio ( GetGameRebateRatioReq ) returns( GetGameRebateRatioResp  ){
		option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "g:x:";
        option( tlvpickle.Usage ) = "-g <game> -x <guildid>";
	}

	// 获取首充号帐号对应的领取者的UID
	rpc GetFirstVoucherApplyUID ( GetFirstVoucherApplyUIDReq ) returns( GetFirstVoucherApplyUIDResp  ){
		option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "x:";
        option( tlvpickle.Usage ) = "-x <first voucher account>";
	}
	
	// 批量获取游戏返利比例值
	rpc BatGetGameRebateRatio ( BatGetGameRebateRatioReq ) returns( BatGetGameRebateRatioResp  ){
		option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
	}
	
	// 批量获取游戏返利比例值
	rpc DelGameRebateRatio ( DelGameRebateRatioReq ) returns( DelGameRebateRatioResp  ){
		option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
	}

}
