syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package obsession;

enum ROOM_TYPE {
	ROOM_TYPE_GROUP = 0;
	ROOM_TYPE_1V1 = 1;
}

message MaxRoomId {
	required uint32 max_room_id = 1;
}

message CallIn {
	required uint32 create_time = 1;
	repeated uint32 accept_uids = 2;
	required uint32 creator_uid = 3;
}

message GroupCallIn {
	required uint32 group_id = 1;
	required CallIn callin = 2;
}

message GroupRoomList {
	repeated uint32 room_id_list = 1;
	repeated CallIn call_in = 2; 
}

message RoomUser {
	required uint32 uid = 1;
}

message Room {
	required uint32 room_id = 1;
	required uint32 group_id = 2;		// 如果是群聊，则group_id > 0
	required uint32 user_count = 3;
	repeated RoomUser user_list = 4;
	required uint32 creator_uid = 5;	// 创建者uid
	required uint32 create_time = 6;
	required uint32 update_time = 7;
	optional uint32 receiver_uid = 8;	// 如果是1v1，那么receiver_uid > 0
	optional uint32 room_type = 9;		// 房间类型，0：群聊，1： 1v1开黑
}

// --------------------------------------------------------

// 创建一个房间
message CreateRoomReq {
	required uint32 group_id = 1;
	required uint32 room_id = 2;	// 房间号,由seqgen分配生成
}

message CreateRoomResp {
}

// 用户加入房间
message JoinRoomReq {
	required uint32 uid = 1;
	required uint32 room_id = 2;
}

message JoinRoomResp {
}

// 用户离开房间
message LeaveRoomReq {
	required uint32 uid = 1;
	required uint32 room_id = 2;
}

message LeaveRoomResp {
}

// 销毁房间
message DestroyRoomReq {
	required uint32 uid = 1;
	required uint32 room_id = 2;
}

message DestroyRoomResp {
}

// 批量查询房间状态和人数
message BatchGetRoomStatusReq {
	repeated uint32 room_id_list = 1;
	required bool need_user_list = 2;
}

message BatchGetRoomStatusResp {
	repeated Room room_list = 1;
}

// 查某个群下面有哪些房间
message BatchGetGroupRoomListReq {
	repeated uint32 group_id_list = 1;
	required bool need_user_list = 2;
}

message BatchGetGroupRoomListResp {
	repeated Room room_list = 1;		// 不带user_list字段
}

// 查某个用户的synckey
message GetUserSyncKeyReq {
	required uint32 uid = 1;
}

message GetUserSyncKeyResp {
	required uint32 sync_key = 1;
}

// 批量更新用户的synckey
message BatchUpdateUserSyncKeyReq {
	repeated uint32 uid_list = 1;
}

message BatchUpdateUserSyncKeyResp {
}

message UserRoom {
	required uint32 uid = 1;
	required Room room = 2;
}

// 查询用户当前在哪个房间 
message BatchGetUserCurRoomReq {
	repeated uint32 uid_list = 1;
}

message BatchGetUserCurRoomResp {
	repeated UserRoom room_list = 1;
}

// 创建1V1开黑房间
message Create1v1RoomReq {
	required uint32 creator_uid = 1;
	required uint32 receiver_uid = 2;
	required uint32 room_id = 3;		// 房间号,由seqgen分配生成
}

message Create1v1RoomResp {
}

message BatchUpdateUserExpiredTimeReq {
	repeated uint32 uid_list = 1;
}

message BatchUpdateUserExpiredTimeResp {
}

message CheckUserIfExpiredReq {
	required uint32 uid = 1;
}

message CheckUserIfExpiredResp {
	required bool is_expired = 1;
	optional Room room = 2;	
}

// 发起召集令
message GroupCallInReq {
	required uint32 group_id = 1;
	required uint32 creator_uid = 2;
}

message GroupCallInResp {
}

// 接受召集令
message GroupAcceptCallInReq {
	required uint32 group_id = 1;
	required uint32 accept_uid = 2;
}

message GroupAcceptCallInResp {
}

// 查询群上次发起的召集令
message BatchGetGroupCallInReq {
	repeated uint32 group_id = 1;
}

message BatchGetGroupCallInResp {
	repeated GroupCallIn callin_list = 2;
}

// 群召集令结束
message GroupCallInEndReq {
	required uint32 group_id = 1;
}

message GroupCallInEndResp {
}

// 清除用户synckey
message DeleteUserSyncKeyIfExpiredReq {
	required uint32 uid = 1;
}

message DeleteUserSyncKeyIfExpiredResp {
}

service ObSession {
	option( tlvpickle.Magic ) = 15110;		// 服务监听端口号	

    rpc CreateRoom(CreateRoomReq) returns (CreateRoomResp) {
        option( tlvpickle.CmdID ) = 1;																		// 命令号
        option( tlvpickle.OptString ) = "u:g:r:";														// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <group_id> -r <room_id>";			// 测试工具的命令号帮助
    }

    rpc JoinRoom(JoinRoomReq) returns (JoinRoomResp) {
        option( tlvpickle.CmdID ) = 2;																		// 命令号
        option( tlvpickle.OptString ) = "u:r:";														// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -r <room>";			// 测试工具的命令号帮助
    }
    
    rpc LeaveRoom(LeaveRoomReq) returns (LeaveRoomResp) {
        option( tlvpickle.CmdID ) = 3;																		// 命令号
        option( tlvpickle.OptString ) = "u:r:";														// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -r <room>";			// 测试工具的命令号帮助
    }
    
    rpc DestroyRoom(DestroyRoomReq) returns (DestroyRoomResp) {
        option( tlvpickle.CmdID ) = 4;														// 命令号
        option( tlvpickle.OptString ) = "u:r:";												// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -r <room>";									// 测试工具的命令号帮助
    }
    
    rpc BatchGetRoomStatus(BatchGetRoomStatusReq) returns (BatchGetRoomStatusResp) {
        option( tlvpickle.CmdID ) = 5;														// 命令号
        option( tlvpickle.OptString ) = "u:n:r:";												// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -n <need_user_list> -r <room_id1,room_id2>";			// 测试工具的命令号帮助
    }
    
    rpc BatchGetGroupRoomList(BatchGetGroupRoomListReq) returns (BatchGetGroupRoomListResp) {
        option( tlvpickle.CmdID ) = 6;														// 命令号
        option( tlvpickle.OptString ) = "u:n:g:";												// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -n <need_user_list> -g <group_id1,group_id2>";			// 测试工具的命令号帮助
    }
    
    rpc GetUserSyncKey(GetUserSyncKeyReq) returns (GetUserSyncKeyResp) {
        option( tlvpickle.CmdID ) = 7;														// 命令号
        option( tlvpickle.OptString ) = "u:";												// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";			// 测试工具的命令号帮助
    }
    
    rpc BatchUpdateUserSyncKey(BatchUpdateUserSyncKeyReq) returns (BatchUpdateUserSyncKeyResp) {
        option( tlvpickle.CmdID ) = 8;														// 命令号
        option( tlvpickle.OptString ) = "u:";												// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid1, uid2, uid3>";			// 测试工具的命令号帮助
    }
    
    rpc BatchGetUserCurRoom(BatchGetUserCurRoomReq) returns (BatchGetUserCurRoomResp) {
        option( tlvpickle.CmdID ) = 9;														// 命令号
        option( tlvpickle.OptString ) = "u:";												// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid1, uid2, uid3>";			// 测试工具的命令号帮助
    }
    
    rpc Create1v1Room( Create1v1RoomReq ) returns ( Create1v1RoomResp ) {
        option( tlvpickle.CmdID ) = 10;														// 命令号
        option( tlvpickle.OptString ) = "u:r:";												// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -r <receiver_uid>";			// 测试工具的命令号帮助
    }
    
    rpc BatchUpdateUserExpiredTime( BatchUpdateUserExpiredTimeReq ) returns ( BatchUpdateUserExpiredTimeResp ) {
        option( tlvpickle.CmdID ) = 11;														// 命令号
        option( tlvpickle.OptString ) = "u:";												// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";			// 测试工具的命令号帮助
    }
    
    rpc CheckUserIfExpired( CheckUserIfExpiredReq ) returns ( CheckUserIfExpiredResp ) {
        option( tlvpickle.CmdID ) = 12;														// 命令号
        option( tlvpickle.OptString ) = "u:";												// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";			// 测试工具的命令号帮助
    }
    
    rpc GroupCallIn( GroupCallInReq ) returns ( GroupCallInResp ) {
        option( tlvpickle.CmdID ) = 13;														// 命令号
        option( tlvpickle.OptString ) = "g:u:";												// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <group_id> -u <creater_uid>";			// 测试工具的命令号帮助
    }
    
    rpc GroupAcceptCallIn( GroupAcceptCallInReq ) returns ( GroupAcceptCallInResp ) {
        option( tlvpickle.CmdID ) = 14;														// 命令号
        option( tlvpickle.OptString ) = "g:u:";												// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <group_id> -u <creater_uid>";			// 测试工具的命令号帮助
    }
    
    rpc BatchGetGroupCallIn( BatchGetGroupCallInReq ) returns ( BatchGetGroupCallInResp ) {
        option( tlvpickle.CmdID ) = 15;														// 命令号
        option( tlvpickle.OptString ) = "g:";												// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <group_id1,group_id2,...>";			// 测试工具的命令号帮助
    }
    
    rpc GroupCallInEnd( GroupCallInEndReq ) returns ( GroupCallInEndResp ) {
        option( tlvpickle.CmdID ) = 16;														// 命令号
        option( tlvpickle.OptString ) = "g:";												// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <group_id>";			// 测试工具的命令号帮助
    }
    
    rpc DeleteUserSyncKeyIfExpired( DeleteUserSyncKeyIfExpiredReq ) returns ( DeleteUserSyncKeyIfExpiredResp ) {
        option( tlvpickle.CmdID ) = 17;														// 命令号
        option( tlvpickle.OptString ) = "u:";												// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";			// 测试工具的命令号帮助
    }

}
