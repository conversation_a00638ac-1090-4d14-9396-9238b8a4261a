syntax="proto2";

// asyncjob.proto

import "common/tlvpickle/skbuiltintype.proto";

package AsyncJob;

//======================================================
// 异步任务定义
//======================================================
message NotifyUserInfoUpdated {
}

message NotifyGuildInfoUpdated {
    required uint32 guild_id = 1;       // 公会id
}

message NotifyCircleTopicDeleted {
	required uint32 circle_id = 1;
	required uint32 topic_id = 2;
}

message MissionMessage {
    required uint32 message_cmd_id = 1;
    required string message_body = 2;
    optional uint32 uid = 3;
    optional uint32 client_type = 4;
}

message MissionEventMessage {
    required bytes message_body = 1;
    optional uint32 uid = 2;
    optional uint32 client_type = 3;
}

message CircleUpdate_NewTopic {
	required uint32 topic_id = 1;
}

message CircleUpdate_ActBegin {
	required uint32 topic_id = 1;
	required string act_desc = 2;
	required uint64 expire_time = 3;
}

message CircleUpdate_ActEnd {
	required uint32 topic_id = 1;
}

message NotifyCircleUpdated {
	enum UpdateType {
		NEW_TOPIC = 1;
		ACT_BEGIN = 2;
		ACT_END = 3;
	}

	required uint32 circle_id = 1;
	required uint32 update_type = 2;
	optional CircleUpdate_NewTopic new_topic_update = 3;
	optional CircleUpdate_ActBegin act_begin_update = 4;
	optional CircleUpdate_ActEnd act_end_update = 5;
}

message NotifyPublicAccountUpdated {
	enum NotifyType {
		BY_PUBLIC_ID = 1;		// 通过public_id通知
		BY_BIZ_ID = 2;			// 通过biz_id & biz_type通知
	}
	required uint32 notify_type = 1;
	optional uint32 public_id = 2;
	optional uint32 biz_type = 3;
	optional uint64 biz_id = 4;
}

message NotifyThirdPartyRegSuccess {
	enum ThirdPartyType {
		QQ = 1;
		WECHAT = 2;
	}
	required uint32 uid = 1;
	required uint32 third_party_type = 2;
	required string openid = 3;
	required string access_token = 4;
}

//======================================================
// 异步任务请求消息
//======================================================
message AsyncJobRequest {
    required uint32 cmd = 1;            // 命令号
    required uint32 key = 2;            // key, 用于分配到不同的队列
    required bytes  job_data = 3;       // data
}

service AsyncJob {
    option( tlvpickle.Magic ) = 14315;

    rpc AddJob(AsyncJobRequest) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 1;              // 命令号
        option( tlvpickle.OptString ) = "u:m:k:j:";       // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <cmd> -k <key> -j <job>";     // 测试工具的命令号帮助
    }
}
