syntax = "proto2";

import "common/tlvpickle/skbuiltintype.proto";

package security;

//安全级别
enum Security_Level {
  SECURITY_LEVEL_LOW = 1;
  SECURITY_LEVEL_MID = 2;
  SECURITY_LEVEL_HIGH = 3;
}

//安全问题: 预置问题、用户问题
message SecurityQuestion {
  required uint32 id = 1;
  required string question = 2;
}

//安全问题答案
message SecurityQuestionAnswer {
  required uint32 id = 1;
  optional string answer = 2;
}

//安全状态信息  /security/summary
message GetSummaryReq {
  required uint32 uid = 1;
}

//第三方注册类型
enum ThirdParty_Attached {
  THIRD_PARTY_NIL = 0;
  THIRD_PARTY_QQ = 1;
  THIRD_PARTY_WECHAT = 2;
}

enum PhoneType {
  UNKNOWN_PHONE = 0;
  LOGIN_PHONE = 1;   // 登录手机
  SECURE_PHONE = 2;  // 安全手机
}

message SecuritySummary {
  required uint32 uid = 1;
  required uint32 level = 2;
  optional string phone = 3;
  repeated SecurityQuestion questions = 4;
  optional uint32 thirdParty_attached = 5;  // ThirdParty_Attached
  optional bool password_set = 6;           //是否设置过密码
  optional PhoneType phone_type = 7;
}

message GetSummaryRsp {
  required SecuritySummary summary = 1;
}

// 令牌
message TokenModel {
  required uint32 uid = 1;
  required uint32 expired = 2;  //过期时间点
}

message AccessToken {
  required string token = 1;
}

message GetAccessTokenReq {
  required uint32 uid = 1;
}

message GetAccessTokenRsp {
  required AccessToken access_token = 1;
}

message ValidateAccessTokenReq {
  required AccessToken access_token = 1;
}

message ValidateAccessTokenRsp {
  optional uint32 uid = 1;
}

// session
enum Session_Usage {
  SESSION_USAGE_PHONE_BIND = 1;
  SESSION_USAGE_PHONE_UNBIND = 2;
  SESSION_USAGE_QUESTION_SET = 3;
  SESSION_USAGE_PASSWORD_UPDATE = 4;
  SESSION_USAGE_PHONE_REBIND = 5;
  SESSION_USAGE_DETACH_THIRD_PARTY = 6;
}

message SessionModel {
  required uint32 uid = 1;
  required uint32 usage = 2;
  required uint32 create_time = 3;
  required string salt = 4;
  optional string validated_phone = 5;
}

message SessionInfo {
  required uint32 uid = 1;
  required uint32 usage = 2;  // Session_Usage
  optional string validated_phone = 3;
}

message GetSessionReq {
  required uint32 uid = 1;
  required SessionInfo session = 2;  //
}

message GetSessionRsp {
  required string session_id = 1;
}

message ValidateSessionReq {
  required string session_id = 1;
}

message ValidateSessionRsp {
  optional SessionInfo session = 1;
}

// 预置安全问题列表
message GetPresetQuestionReq {
}

message GetPresetQuestionRsp {
  repeated SecurityQuestion questions = 1;
}

//获取验证方式：手机验证码，密保问题
enum Verification_Scheme {
  VERIFY_SCHEME_SMS = 1;       //短信验证码方式
  VERIFY_SCHEME_QUESTION = 2;  //密保问题方式
  VERIFY_SCHEME_CODE = 3;      //(图形)输入验证码方式(预留)
}

enum OP_TYPE {
  OP_NIL = 0;
  OP_BIND_PHONE = 0x0001;
  OP_UNBIND_PHONE = 0x0002;
  OP_REBIND_PHONE = 0x0004;
  OP_UPDATE_PASSWD = 0x0008;
  OP_SET_QUESTION = 0x0010;
  OP_DETACH_THIRDPARTY = 0x0020;
}

//设置密保问题
message SetQuestionReq {
  required uint32 uid = 1;
  repeated SecurityQuestionAnswer answers = 2;
  optional AccessToken access_token = 3;
  optional string session_id = 4;
  optional int32 source = 5;  //
}
message SetQuestionRsp {
  optional SecuritySummary summary = 1;
}

// 验证密保问题
message VerifyQuestionReq {
  required uint32 uid = 1;
  repeated SecurityQuestionAnswer answers = 2;
}

message VerifyQuestionRsp {
}

message BindPhoneReq {
  required uint32 uid = 1;
  required string phone = 2;
  optional AccessToken access_token = 3;
  optional string session_id = 4;
  optional bool without_summary = 5;
  optional int32 source = 6;  //
  optional PhoneType phone_type = 7;
  optional string scene = 8;
}

message BindPhoneRsp {
  optional SecuritySummary summary = 1;
}

message UnbindPhoneReq {
  required uint32 uid = 1;
  optional AccessToken access_token = 2;
  optional string session_id = 3;
  optional bool without_summary = 4;
  optional int32 source = 5;  //
  optional string scene = 6;
}
message RebindPhoneReq {
  required uint32 uid = 1;
  required string phone = 2;
  required bool is_bind = 3;  // false:解绑 true:绑定
  optional int32 source = 4;  //

  optional bool with_summary = 5;
  optional AccessToken access_token = 6;
  optional string session_id = 7;
  optional PhoneType phone_type = 8;  // is_bind=true时有效
  optional string scene = 9;
}
message RebindPhoneResp {
  optional SecuritySummary summary = 1;
}

message UnbindPhoneRsp {
  optional SecuritySummary summary = 1;
}

//密码
message UpdatePasswordReq {
  required uint32 uid = 1;
  required string new_passwd = 2;
  optional AccessToken access_token = 3;
  optional string session_id = 4;
  optional bool without_summary = 5;
  optional int32 source = 6;  //
}

message UpdatePasswordRsp {
  optional SecuritySummary summary = 1;
}

message DetachThirdPartyReq {
  required uint32 uid = 1;
  optional AccessToken access_token = 2;
  optional string session_id = 3;
  optional bool without_summary = 4;
  optional int32 source = 5;  //
}

message DetachThirdPartyRsp {
  optional SecuritySummary summary = 1;
}

message GetOperationLogReq {
  optional uint32 uid = 1;
  optional uint32 op_type = 2;
  optional int32 op_source = 3;
  optional uint32 begin_time = 4;
  optional uint32 end_time = 5;
  optional uint32 offset = 6;
  optional uint32 limit = 7;
}

message LogInfo {
  required uint32 uid = 1;
  required uint32 op_type = 2;
  required int32 op_source = 3;
  required string content = 4;
  required uint32 op_time = 5;
}

message GetOperationLogRsp {
  repeated LogInfo info_list = 1;
}

message BatchGetLatestOperationReq {
  repeated uint32 uid_list = 1;
  optional uint32 op_type = 2;
}

message UserLatestOperation {
  required uint32 uid = 1;
  repeated LogInfo info_list = 2;
}

message BatchGetLatestOperationRsp {
  repeated UserLatestOperation op_list = 1;
}

message GetUnregApplyAuditStatusReq {
  required uint32 uid = 1;
}

message GetUnregApplyAuditStatusRsp {
  optional uint32 status = 1;
}

message UpdateUnregApplyAuditStatusReq {
  required uint32 uid = 1;
  required uint32 status = 2;
  optional uint32 apply_id = 3;
  optional string operator_id = 4;
}
message UpdateUnregApplyAuditStatusRsp {
}

message ClearUnregApplyAuditStatusReq {
  required uint32 uid = 1;
}

message ClearUnregApplyAuditStatusRsp {
}

enum SortType {
  SORT_ASC = 1;
  SORT_DESC = 2;
}

message GetUnregApplyAuditRecordReq {
  optional uint32 uid = 1;
  optional uint32 status = 2;
  optional uint32 offset = 3;
  optional uint32 offset_apply_id = 4;
  optional uint32 limit = 5;
  optional uint32 begin_time = 6;
  optional uint32 end_time = 7;
  optional uint32 sort_type = 8;
}

message UnregApplyAuditInfo {
  required uint32 apply_id = 1;
  required uint32 uid = 2;
  required uint32 status = 3;
  required uint32 apply_at = 4;  //提交时间
  optional string operator_id = 5;
  optional uint32 op_at = 6;  //运营操作时间
}

message GetUnregApplyAuditRecordRsp {
  repeated UnregApplyAuditInfo info_list = 1;
  required uint32 total = 2;
}

message GetUserLastOperationReq {
  required uint32 uid = 1;
  optional uint32 op_type = 2;  // enum OP_TYPE union
}

message GetUserLastOperationRsp {
  repeated LogInfo info_list = 1;
}

message UpdateAutoProcUnregApplyStatusReq {
  required uint32 uid = 1;
  required uint32 status = 2;
  optional uint32 apply_id = 3;
  optional string ttid = 4;
  optional uint32 cancel_time = 5 ;
  optional uint32 market_id = 6;
}
message UpdateAutoProcUnregApplyStatusRsp{
}

message GetAutoProcUnregRecordReq {
  required uint32 status = 1;
  optional uint32 uid = 2;
  optional string ttid = 3;
  optional bool   sort = 4;   //true 为排序由时间由近到远   
  optional uint32 offset = 5;
  optional uint32 limit = 6;
}

message AutoProcUnregInfo {
  required uint32 id = 1;
  required uint32 uid = 2;
  required string ttid = 3;    
  required uint32 status = 4;           //审核状态 
  required uint32 apply_at = 5;         //提交时间
  required uint32 remain_wait = 6;   //剩余等待时间,秒
  optional bool   visit = 7;            //是否已回访
  optional string unreg_reason = 8 ;    //注销原因
  optional uint32 market_id = 9;
}

message GetAutoProcUnregRecordRsp {
  repeated AutoProcUnregInfo info_list = 1;
  required uint32 total = 2;
}

//注销回访
message UpdateUnregVisitReq{
    required uint32 id =1;
    required bool visit =2 ;          //是否已回访
    required string unreg_reason = 3;  //注销原因
}

message UpdateUnregVisitRsp {
}

service Security {
    option( tlvpickle.Magic ) = 15580;// 服务监听端口号


  //获取安全状态
  rpc GetSummary(GetSummaryReq) returns (GetSummaryRsp) {
    option (tlvpickle.CmdID) = 1;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  //访问令牌
  rpc GetAccessToken(GetAccessTokenReq) returns (GetAccessTokenRsp) {
    option (tlvpickle.CmdID) = 10;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  rpc ValidateAccessToken(ValidateAccessTokenReq)
      returns (ValidateAccessTokenRsp) {
    option (tlvpickle.CmdID) = 11;
    option (tlvpickle.OptString) = "t:";
    option (tlvpickle.Usage) = "-t <token>";
  }

  // session
  rpc GetSession(GetSessionReq) returns (GetSessionRsp) {
    option (tlvpickle.CmdID) = 15;
    option (tlvpickle.OptString) = "u:e:";
    option (tlvpickle.Usage) = "-u <uid> -e <usage:0-4>";
  }
  rpc ValidateSession(ValidateSessionReq) returns (ValidateSessionRsp) {
    option (tlvpickle.CmdID) = 16;
    option (tlvpickle.OptString) = "s:";
    option (tlvpickle.Usage) = "-s <session id>";
  }

  //获取预置问题列表
  rpc GetPresetQuestion(GetPresetQuestionReq) returns (GetPresetQuestionRsp) {
    option (tlvpickle.CmdID) = 20;
    option (tlvpickle.OptString) = "";
    option (tlvpickle.Usage) = "";
  }

  //设置/取消密保问题
  rpc SetQuestion(SetQuestionReq) returns (SetQuestionRsp) {
    option (tlvpickle.CmdID) = 21;
    option (tlvpickle.OptString) = "u:o:p:q:x:y:z:s:t:";
    option (tlvpickle.Usage) =
        "-u <uid> -o <q1 id> -p <q2 id> -q <q3 id> [-x <a1> -y<a2> -z <a3>] "
        "[-s <session id> -t <token>]";
  }

  //验证密保问题
  rpc VerifyQuestion(VerifyQuestionReq) returns (VerifyQuestionRsp) {
    option (tlvpickle.CmdID) = 22;
    option (tlvpickle.OptString) = "u:o:p:q:x:y:z:";
    option (tlvpickle.Usage) =
        "-u <uid> -o <q1 id> -p <q2 id> -q <q3 id> -x <a1> -y<a2> -z <a3>";
  }

  //手机号

  rpc BindPhone(BindPhoneReq) returns (BindPhoneRsp) {
    option (tlvpickle.CmdID) = 26;
    option (tlvpickle.OptString) = "u:p:s:t:";
    option (tlvpickle.Usage) =
        "-u <uid> -p <phone> [-s <session id> -t <token>]";
  }

  rpc UnbindPhone(UnbindPhoneReq) returns (UnbindPhoneRsp) {
    option (tlvpickle.CmdID) = 27;
    option (tlvpickle.OptString) = "u:s:t:";
    option (tlvpickle.Usage) = "-u <uid> [-s <session id> -t <token>]";
  }

  rpc RebindPhone(RebindPhoneReq) returns (RebindPhoneResp) {
    option (tlvpickle.CmdID) = 28;
    option (tlvpickle.OptString) = "u:p:b";
    option (tlvpickle.Usage) = "-u <uid> -p <phone> -b ";
  }

  //修改密码
  rpc UpdatePassword(UpdatePasswordReq) returns (UpdatePasswordRsp) {
    option (tlvpickle.CmdID) = 30;
    option (tlvpickle.OptString) = "u:p:s:t:";
    option (tlvpickle.Usage) =
        "-u <uid> -p <new password> -s <session id> -t <token>";
  }

  //
  rpc DetachThirdParty(DetachThirdPartyReq) returns (DetachThirdPartyRsp) {
    option (tlvpickle.CmdID) = 36;
    option (tlvpickle.OptString) = "u:s:t:";
    option (tlvpickle.Usage) = "-u <uid> -s <session id> -t <token>";
  }

  rpc GetOperationLog(GetOperationLogReq) returns (GetOperationLogRsp) {
    option (tlvpickle.CmdID) = 40;
    option (tlvpickle.OptString) = "u:t:s:b:e:o:l:";
    option (tlvpickle.Usage) =
        "-u <uid> [-t <op_type>] [-s <op_source>] [-b <begin_time, eg:2022-02-01 00:00:00>] [-e <end_time, eg:2022-02-28 00:00:00>] [-o <offset>] [-l <limit>]";
  }

  rpc GetUnregApplyAuditStatus(GetUnregApplyAuditStatusReq)
      returns (GetUnregApplyAuditStatusRsp) {
    option (tlvpickle.CmdID) = 41;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  rpc UpdateUnregApplyAuditStatus(UpdateUnregApplyAuditStatusReq)
      returns (UpdateUnregApplyAuditStatusRsp) {
    option (tlvpickle.CmdID) = 42;
    option (tlvpickle.OptString) = "u:s:a:o:";
    option (tlvpickle.Usage) = "-u <uid> -s <status> -a <apply_id> -o operator";
  }

  rpc ClearUnregApplyAuditStatus(ClearUnregApplyAuditStatusReq)
      returns (ClearUnregApplyAuditStatusRsp) {
    option (tlvpickle.CmdID) = 43;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  rpc GetUnregApplyAuditRecord(GetUnregApplyAuditRecordReq)
      returns (GetUnregApplyAuditRecordRsp) {
    option (tlvpickle.CmdID) = 44;
    option (tlvpickle.OptString) = "u:s:o:a:l:d:b:e:";
    option (tlvpickle.Usage) =
        "-u <uid> -s <status> -o <offset> -a <offset_apply_id> -l <limit> -b "
        "<begin_time> -e <end_time>";
  }

  rpc BatchGetLatestOperation(BatchGetLatestOperationReq) // deprecated
      returns (BatchGetLatestOperationRsp) {
    option (tlvpickle.CmdID) = 45;
    option (tlvpickle.OptString) = "u:t:";
    option (tlvpickle.Usage) =
        "-u <uid_list> -t <op_type> ";
  }
	
  rpc GetUserLastOperation(GetUserLastOperationReq)
      returns (GetUserLastOperationRsp) {
    option (tlvpickle.CmdID) = 46;
    option (tlvpickle.OptString) = "u:t:";
    option (tlvpickle.Usage) =
        "-u <uid> -t <op_type> ";
  }

  //新的用户注销流程接口
  rpc UpdateAutoProcUnregApplyStatus(UpdateAutoProcUnregApplyStatusReq)
      returns (UpdateAutoProcUnregApplyStatusRsp) {
    option (tlvpickle.CmdID) = 47;
    option (tlvpickle.OptString) = "u:s:a:t:";
    option (tlvpickle.Usage) = "-u <uid> -s <status> -a <apply_id> -t ttid";
  }
  rpc GetAutoProcUnregRecord(GetAutoProcUnregRecordReq)
      returns (GetAutoProcUnregRecordRsp) {
    option (tlvpickle.CmdID) = 48;
    option (tlvpickle.OptString) = "s:u:t:o:f:l";
    option (tlvpickle.Usage) =
        "-s <status> -u <uid> -t <ttid> -o <sort> -f <offset> -l <limit> ";
  }
  rpc UpdateUnregVisit(UpdateUnregVisitReq)
      returns (UpdateUnregVisitRsp) {
    option (tlvpickle.CmdID) = 49;
    option (tlvpickle.OptString) = "i:v:u";
    option (tlvpickle.Usage) =
        "-i <id> -v <visit> -u <unreg_reason>";
  }
}
