syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package MsgPushSvr;

message GetMsgReq {
	required uint32 game_id = 1;
	required uint32 user_id = 2;
    required uint32 seq = 3;
}

message Msg {
	required uint32 msg_id = 1;
	required uint32 msg_type = 2;
	required string content = 3;
	required string content_url = 4;
	required uint64 msg_action_type = 5;
	required uint32 start_time = 6;
	required uint32 end_time = 7;
}

message GetMsgResp {
	repeated Msg msg_list = 1;
    required uint32 seq = 2;
}


message AddMsgReq {
	required uint32 game_id = 1;
	required Msg msg = 2;
}

message AddMsgResp {
	required uint32 msg_id = 1;
}

// 我要很无情无耻无理取闹地把SDK游戏和TT游戏圈绑定关系放在这个服务里
message SdkGameConfig {
	required uint32 sdk_game_id 	= 1;
	required uint32 tt_game_id 		= 2;
	required uint32 tt_circle_id 	= 3;
	required string game_name 		= 4;
	required bool enable_giftpkg 	= 5;
}

message SdkGameConfigList {
	repeated SdkGameConfig game_config_list = 1;
}

message UpdateSdkGameConfigReq {
	required uint32 sdk_game_id 	= 1;
	required string game_name 		= 2;
	required bool enable_giftpkg	= 3;
}

message UpdateSdkGameConfigWithCircleIdReq {
	required uint32 sdk_game_id 	= 1;
	required uint32 circle_id 		= 2;
	required bool enable_giftpkg	= 3;
}

message GetSdkGameConfigReq {
	required uint32 sdk_game_id 	= 1;
}

message BatchGetSdkGameConfigReq {
	repeated uint32 sdk_game_id_list 	= 1;
}

message BatchGetSdkGameConfigResp {
	repeated SdkGameConfig game_config_list = 1;
}

service MsgPush
{
	option( tlvpickle.Magic ) = 15130;

	rpc GetMsg ( GetMsgReq ) returns ( GetMsgResp ) {
		option( tlvpickle.CmdID ) = 1;
	    option( tlvpickle.OptString ) = "u:g:";
        option( tlvpickle.Usage ) = "-u <uid> -g <game_id>";
	}

	rpc AddMsg (AddMsgReq) returns (AddMsgResp) {
		option( tlvpickle.CmdID ) = 2;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <game_id>";
	}

	rpc UpdateSdkGameConfig ( UpdateSdkGameConfigReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 3;
		option( tlvpickle.OptString ) = "g:n:p:";
		option( tlvpickle.Usage ) = "-g <sdk_game_id> -n <game_name> -p <enable_giftpkg>";
	}

	rpc GetSdkGameConfig ( GetSdkGameConfigReq ) returns ( SdkGameConfig ) {
		option( tlvpickle.CmdID ) = 4;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <sdk_game_id>";
	}

	rpc UpdateSdkGameConfigWithCircleId ( UpdateSdkGameConfigWithCircleIdReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 5;
		option( tlvpickle.OptString ) = "g:s:p:";
		option( tlvpickle.Usage ) = "-g <sdk_game_id> -s <circle_id> -p <enable_giftpkg>";
	}

	rpc BatchGetSdkGameConfig ( BatchGetSdkGameConfigReq ) returns ( BatchGetSdkGameConfigResp ) {
		option( tlvpickle.CmdID ) = 6;
		option( tlvpickle.OptString ) = "g:";
		option( tlvpickle.Usage ) = "-g <sdk_game_id>";
	}
}
