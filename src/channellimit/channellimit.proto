syntax = "proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package channellimit;

enum IpLimitType
{
  ENUM_IPLIMIT_EMPTY = 0;
  ENUM_IPLIMIT_IM = 1;
  ENUM_IPLIMIT_ENTER = 2;
};

message CheckIpLimitReq {
  string ip = 1;
  uint32 channel_id = 2;
  uint32 type = 3;
}

message CheckIpLimitResp {
  uint32 current_size = 1;
}

message SetIpLimitReq {
  string ip = 1;
  uint32 channel_id = 2;
  uint32 type = 3;
  uint32 size = 4;
}

message SetIpLimitResp {

}

message CheckMsgLimitReq {
  uint32 channel_id = 1;
  uint32 type = 2;
}

message CheckMsgLimitResp {
  uint32 current_size = 1;
}

message SetMsgLimitReq {
  uint32 channel_id = 1;
  uint32 type = 2;
  uint32 size = 3;
  uint32 expire = 4;
}

message SetMsgLimitResp {

}

message CheckOperCooldownReq {
  uint32 channel_id = 1;
  uint32 cooldown_type = 2;
}

message CheckOperCooldownResp {
  uint32 remain_cd = 1;
}

message ChannelAttachmentMsgLimitReq {
  uint32 uid = 1;
  uint32 channel_id = 2;
  uint32 type = 3;        // use channel_.proto->ChannelMsgImageSetType
  string client_key = 4;
}

message ChannelAttachmentMsgLimitResp {
  uint32 current_count = 1;
}

service ChannelLimit {
  option (tlvpickle.Magic) = 15709;

  rpc CheckIpLimit(CheckIpLimitReq) returns (CheckIpLimitResp) {
    option (tlvpickle.CmdID) = 1;
    option (tlvpickle.OptString) = "";
    option (tlvpickle.Usage) = "";
  }

  rpc SetIpLimit(SetIpLimitReq) returns (SetIpLimitResp) {
    option (tlvpickle.CmdID) = 2;
    option (tlvpickle.OptString) = "";
    option (tlvpickle.Usage) = "";
  }

  rpc CheckMsgLimit(CheckMsgLimitReq) returns (CheckMsgLimitResp) {
    option (tlvpickle.CmdID) = 3;
    option (tlvpickle.OptString) = "";
    option (tlvpickle.Usage) = "";
  }

  rpc SetMsgLimit(SetMsgLimitReq) returns (SetMsgLimitResp) {
    option (tlvpickle.CmdID) = 4;
    option (tlvpickle.OptString) = "";
    option (tlvpickle.Usage) = "";
  }

  rpc CheckOperCooldown(CheckOperCooldownReq) returns (CheckOperCooldownResp) {
    option (tlvpickle.CmdID) = 5;
    option (tlvpickle.OptString) = "";
    option (tlvpickle.Usage) = "";
  }

  rpc ChannelAttachmentMsgLimit(ChannelAttachmentMsgLimitReq) returns (ChannelAttachmentMsgLimitResp) {
    option (tlvpickle.CmdID) = 6;
    option (tlvpickle.OptString) = "";
    option (tlvpickle.Usage) = "";
  }
}