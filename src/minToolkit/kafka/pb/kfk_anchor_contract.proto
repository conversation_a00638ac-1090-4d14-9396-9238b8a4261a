syntax="proto2";

package kafkaanchorcontract;

enum EVENT_TYPE{
    EVENT_SIGN_CONTRACT = 0; //签约事件
    EVENT_CANCEL_CONTRACT = 1; //解约事件
    EVENT_CONTRACT_ADD_LIVE_PERMISSION = 2; // 赋予语音直播权限
    EVENT_CONTRACT_DEL_LIVE_PERMISSION = 3; // 回收语音直播权限
    EVENT_CONTRACT_DEL_MULTIPLAYER = 4; // 移除多人互动身份，只有当两个身份都有时才会触发
    EVENT_CONTRACT_DEL_ESPORTS = 5;//回收公会电竞指导身份
    EVENT_EXTENSION_CONTRACT = 6; //续约事件
}

message AnchorContractEvent{
    required uint32 event_type = 1; //EVENT_TYPE
    required uint32 uid = 2; //主播UID 
    required uint32 guild_id = 3; //公会ID
    required uint32 event_time = 4; //发生事件的时间
}