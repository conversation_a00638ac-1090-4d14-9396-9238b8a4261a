syntax="proto2";
package kafka_channelmsg;

//
message ChannelMsgEvent
{
	required uint32 uid 		= 1;
	required uint32 ch_id		= 2;
	required uint32 type		= 3;   // EVENT_TYPE
	optional uint32 channel_type = 4;
	optional bytes opt_pb_info = 5;    // 根据不同的event_type 可能有不同的pb协议
	optional uint32 app_id = 6;
	optional uint32 bind_id = 7;
	optional uint32 channel_box_id = 8; // 包厢id
	optional uint32 seq_id = 9;         // 消息序号，对应ga.ChannelMsg.seq
	optional bytes pb_opt_content = 10; // 房间IM消息附加内容，对应ga.ChannelMsg.pb_opt_content
}

// 公屏消息opt
message ChannelImMsgOpt
{
	required uint32 msg_type = 1;     // see ga.ChannelMsgType, 1:文本; 7:图片
	optional string text_content = 2; // 文本明文
	optional bytes att_content = 3;   // 房间图片消息内容(缩略图) see ga.ChannelImageMsgContent
}