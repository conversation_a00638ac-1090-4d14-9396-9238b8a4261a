syntax = "proto3";

package kfk_esport_trade;

// 电竞陪玩订单结算事件
message EsportTradeSettleEvent {
  string order_id = 1;   // 订单id
  uint32 player_uid = 2; // 玩家id
  uint32 coach_uid = 3;  // 电竞陪玩id
  uint32 total_price = 4;// 总价值/T豆，是用户实际支付的金额
  int64 pay_time = 5;    // 支付时间
  int64 event_time = 6;  // 事件时间
  string deal_token = 7; // token
  int64 settle_time = 8; // 结算时间
  uint32 sign_guild_id = 9;   // 电竞陪玩签约公会id
  uint32 coach_total_price = 10; // 大神侧看到的订单总价
}

// 订单状态
enum TradeStatus {
  ORDER_STATUS_UNSPECIFIED = 0;
  ORDER_STATUS_PAYED = 1;             // 已支付/待接单
  ORDER_STATUS_RECEIVED = 2;          // 已接单/进行中
  ORDER_STATUS_REFUND = 3;            // 退款申诉中
  ORDER_STATUS_FINISHED = 4;          // 已完成
  ORDER_STATUS_CANCELED = 5;          // 已取消
  ORDER_STATUS_REFUNDED = 6;          // 已退款
}

// 电竞陪玩订单状态变化事件
message EsportTradeStatusChangeEvent {
  string order_id = 1;   // 订单id
  uint32 player_uid = 2; // 玩家id
  uint32 coach_uid = 3;  // 电竞陪玩id
  uint32 total_price = 4;// 总价值/T豆，是用户实际支付的金额
  int64 pay_time = 5;    // 支付时间
  int64 event_time = 6;  // 状态变化时间
  uint32 status = 7;     // 订单状态 see TradeStatus
  uint64 product_id = 8; // 商品id
  uint32 game_id = 9;    // 游戏id
  bool use_first_round_discount = 10; // 是否使用首局优惠
  bool use_new_customer_discount = 11; // 是否使用新客优惠
  uint32 coach_total_price = 12; // 大神侧看到的订单总价
}
