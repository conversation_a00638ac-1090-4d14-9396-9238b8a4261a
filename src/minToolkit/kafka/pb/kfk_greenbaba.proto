syntax="proto2";

package kafkagreenbaba;

enum ENUM_TARGET_TYPE
{
	E_TARGET_TYPE_CHANNEL = 1;	// 对房间 --- ChannelReportEventOpt
	E_TARGET_TYPE_USER = 2;	    // 对用户 -- target_user
	E_TARGET_TYPE_MUSIC = 3;    //music   -- MusicReportEvent
	E_TARGET_TYPE_GROUP = 4;    //群组    -- GroupReportEventOpt
	E_TARGET_TYPE_CHAT  = 5;    //聊天    -- ChatReportEvent
	E_TARGET_TYPE_UGC   = 6;    //圈子 & 评论 -- UgcReportEventOpt

	E_TARGET_TYPE_GUILD = 8;   //公会举报
	E_TARGET_TYPE_MASKED_CALL = 9;   //1v1聊天举报
	E_TARGET_TYPE_SLIP_NOTE = 10;   // 小纸条内容举报

	E_TARGET_TYPE_CHAT_CARD = 11;   //扩列墙卡片举报

	E_TARGET_TYPE_PIA = 12; // pia戏剧本库举报
	E_TARGET_TYPE_GAME_PAL_CARD = 13; // 游戏搭子卡片举报 see GamePalCardReportEventOpt
}

// 制裁类型
enum ENUM_SANCTION_OP_TYPE
{
	E_SANCTION_OP_TYPE_NONE = 0;	            // 没有处理

	E_SANCTION_OP_TYPE_WARNING = 3;	    // 警告
	E_SANCTION_OP_TYPE_BANNED  = 4;	    // 封禁
	E_SANCTION_OP_TYPE_IGNORE  = 25;	// 忽略
}

// 制裁类型中 封禁情况下 的封禁类型
enum ENUM_BANNED_TYPE
{
	E_BANNED_NONE  = 0;	            // 没有封禁
	E_BANNED_LOGIN = 1;	            // 禁止登录
	E_BANNED_CHANNEL = 2;	        // 禁止与房间相关的操作
}

// 制裁事件
message GreenBabaSanctionEvent
{
	required uint32 target_type = 1;     // 制裁的目标类型 ENUM_TARGET_TYPE
	required uint32 id          = 2;     // 根据 target_type 可以是 uid 或者 房间ID
	optional uint32 banned_type  = 3;    // 封禁的类型 ENUM_BANNED_TYPE 。 当且仅当 sanction_type = E_SANCTION_OP_TYPE_BANNED 时才有值
	optional uint32 second   = 4;        // 剩余制裁的时间 0 表示取消封禁
	optional uint32 sanction_type   = 5; // 制裁操作的类型 ENUM_SANCTION_OP_TYPE
	optional string sanction_reason = 6; // 制裁原因
}

// 举报事件
enum ENUM_REPORT_TYPE {
	E_REPORT_PRIVACY = 0;         // 泄露隐私
	E_REPORT_PERSONAL_ATTACK = 1; // 人身攻击
	E_REPORT_PORNOGRAPHY = 2;     // 淫秽色情
	E_REPORT_AD = 3;              // 广告垃圾
	E_REPORT_SENSITIVE_INFO = 4;  // 敏感话题
	E_REPORT_TORT = 5; 			//侵权
	E_REPORT_GAMBLING = 6; 	    //赌博
}

enum ENUM_REPORT_MESSAGE_TYPE{
	E_REPORT_MESSAGE_TEXT = 1;   //文本
	E_REPORT_MESSAGE_IMG = 2;    //图片
}

enum ENUM_REPORT_USER_ENTRANCE_TYPE {
	E_REPORT_USER_ENTRANCE_TYPE_OTHER = 201; // 其他页面举报
	E_REPORT_USER_ENTRANCE_TYPE_USER_DETAIL = 202; //用户主页举报
	E_REPORT_USER_ENTRANCE_TYPE_USER_IM = 203; //用户私信举报
	E_REPORT_USER_ENTRANCE_TYPE_CHANNEL_MIC_HOLD = 204; // 房间麦上举报
	E_REPORT_USER_ENTRANCE_TYPE_CHANNEL_MIC_DOWN = 205 ; // 房间麦下举报
	E_REPORT_USER_ENTRANCE_TYPE_CHANNEL_MESSAGE = 206; // 房间公屏举报
	E_REPORT_USER_ENTRANCE_TYPE_CHANNEL_LIVE_RANK =207; // 房间在线榜举报
	E_REPORT_USER_ENTRANCE_TYPE_CHANNEL_SEND_PRESENT = 208; // 房间送礼举报
	E_REPORT_USER_ENTRANCE_TYPE_EXPORT_USER = 209; // 电竞大神举报
}

message ReportMessageContent
{
	required uint32 type = 1;  //聊天消息类型ENUM_REPORT_MESSAGE_TYPE
	required string msg = 2;
	optional uint32 timestamp = 3;     //消息时间截
	optional uint32 from_uid  = 4;     //发送该条聊天信息的用户uid
	optional string from_account = 5;
	optional uint32 msg_seq_id = 6 ;  //带seqid是经过服务端验证过的
}
message GreenBabaReportEvent
{
	required uint32 target_type = 1;     // 目标类型 ENUM_TARGET_TYPE
	required uint32 id          = 2;     // 根据 target_type 可以是 uid 或者 房间ID

	optional uint32 report_type  = 3;	// ENUM_REPORT_TYPE
	optional string report_reason = 4;	//
	optional string report_img_url = 5;
	optional uint32 report_uid = 6;    // 举报者的UID
	optional bytes opt_bin = 7;        //举报结构，可根据report_type确定(E_TARGET_TYPE_CHANNEL和E_TARGET_TYPE_USER时皆为ChannelReportEventOpt)
	optional uint32 timestamp = 8;     // 举报的时间戳
	optional UserSimpleInfo report_user = 9; //举报人信息
	optional UserSimpleInfo target_user = 10; //被举报人信息
	repeated uint32 report_type_list = 11;    //2020-10-26，增加复选举报类型
	repeated ReportMessageContent content_list = 12 ;//支持多条聊天内容，文本，图片等
	repeated string reason_list = 13 ;//上报时选的原因
	optional uint32 market_id = 14; // 马甲包id
	optional string belong_obj_id = 15; // 举报所属对象id
	optional uint32 report_entrance_type = 16; // 举报入口类型 see ENUM_REPORT_USER_ENTRANCE_TYPE
}

message UserSimpleInfo
{
	required uint32 uid = 1;
	required string nickname = 2;
	required string ttid = 3;
}

message ChannelReportEventOpt
{
	required uint32 channel_id = 1;
	required uint32 channel_type = 2;
	optional uint32 channel_displayid = 3;   // 旧房间显示id，不建议使用
	optional string channel_name = 4;
	optional uint32 channel_bind_id = 5;
	optional uint32 channel_scheme_id = 6;   // 房间玩法id
	optional string channel_scheme_name = 7; // 房间玩法名
	optional string channel_view_id = 8;     // 新房间显示id，替代channel_displayid
	optional uint32 channel_scheme_detail_type = 9;   // 房间玩法类型
	optional uint32 channel_tag_id = 10; // 公开厅id
}

message UserReportEventOpt
{
	required uint32 target_uid = 1;
	required string target_nickname = 2;
	required string target_ttid = 3;
}

message MusicReportEvent
{
	required uint32 report_uid = 1;
	required string owner_account = 2;
	required uint32 channel_id = 3;
	required string music_id = 4;
	required string music_name = 5;
	required string music_singer = 6;
	optional string music_url = 7;
	optional uint32 channel_displayid = 8;  // 旧房间显示id，不建议使用
	optional string channel_view_id = 9;    // 新房间显示id，替代channel_displayid
}

message ChatReportEvent
{
	required uint32 report_uid = 1;
	required uint32 target_uid = 2;
	required string target_nickname = 3;
	required string reason = 4;
	required string content = 5; //聊天内容
}

message GroupReportEventOpt
{
	required uint32 group_id = 1;
	required string group_name = 2;
	required uint32 group_displayid = 3;
	optional uint32 guild_id = 4;
	optional string head_md5 = 5;
	repeated UserSimpleInfo guild_admin = 6; //会长和管理员列表
	optional uint32 group_type = 7;          //群组类型
}

message UgcReportEventOpt
{
	required string post_id = 1; //帖子
	optional string comment_id = 2; //评论ID
	optional string comment = 3; //评论内容
}

message GuildReportEventOpt
{
	required uint32 guild_id = 1;
	required string guild_name = 2;
	optional uint32 short_id = 3;
	optional uint32 main_group = 4;
	optional string intro = 5; //公会简介
	optional string manifesto = 6; //公会宣言
	optional string notify = 7;
	optional string head_md5 = 8; //公会头像
	optional string content = 9; //公会公告

	repeated UserSimpleInfo guild_admin = 10; //会长和管理员列表
}

message ChatCardReportEventOpt
{
	required string slogan = 1; // 扩列墙文本
	required string bg = 2; // 扩列墙图片
	required uint32 version = 3; // 卡片版本
}

message MaskedCallAudio
{
	required string url = 1;				//音频下载地址
	required uint64 start_timestamp = 2;	//音频开始时间戳
}

message MaskedCallReportEventOpt
{
	required uint32 call_id = 1;				//通话标识

	repeated MaskedCallAudio report_audio = 2; 	//举报者音频列表（选填，第一期为空）
	repeated MaskedCallAudio target_audio = 3; 	//被举报者音频列表（必填）
}

message SlipNoteReportEventOpt{
	required uint32 slip_note_owner_uid = 1;
	required string slip_note_content = 2;
}

message GamePalCardReportEventOpt
{
	optional string card_id = 1; // 游戏搭子卡id
	optional string social_decl = 2; // 交友宣言
	repeated string image_url_list = 3; // 图片url列表
	optional uint32 card_uid = 4;
}
