syntax = "proto3";

package kfk_channel_guide;

message GameAppointmentTeam
{
  string team_id = 1; // 队伍id
  uint32 tab_id = 2; // 房间类型id
  string game_name = 4; // 游戏名称
  repeated uint32 uid_list = 5;
}

message KUserGameAppointmentTeamEvent
{
  repeated GameAppointmentTeam game_team_list = 1;
}

message KfkAppStatusReport
{
    enum AppStatus {
        AppNone = 0;   //none
        AppBackGround = 1; //后台
        AppAWait = 2;    //唤醒
    }
    AppStatus app_status  = 1;
    int64 app_change_time  = 2;
    uint32 uid = 3;
}


message KfkAppointment{
    enum AppointmentType {
        NoneAppointment = 0; //没有预约
        FirstAppointment = 1;    //第一次预约
        SecondAppointment = 2;   //第二次预约（选游戏卡）
    }
    message GameCardOpt
    {
      string opt_name = 1;               //段位,区服等字段
      repeated string value_list = 2;    // 选项属性，例如段位会有:青铜，白银，王者等
      uint32 opt_id = 3;                 //还是加上这个吧
    }
    uint32 uid = 1;
    AppointmentType appointment_type = 2;    //预约类型
    repeated GameCardOpt  opt_list = 3;   //游戏卡选项
    uint32 time_duration = 4;        //时长杪
    uint32 game_tab_id = 5;     //游戏id
    string game_tab_name = 6;  //游戏名称
    int64 over_time = 7; //到期时间
}

message KfkJoinTeamSuc{
    repeated uint32 uids = 1;
    uint32 game_tab_id = 2;     //游戏id
    string game_tab_name = 3;  //游戏名称
    string ori_teamid = 4; //推荐车队id
}