syntax="proto3";

package kafka_im;

// 通用IM事件
message CommImEvent {
  enum EventType {
    EVENT_TYPE_UNSPECIFIED = 0;
    EVENT_TYPE_GROUP_IM = 1; // 群聊
    EVENT_TYPE_1V1_SEND_IM = 2; // 私聊 发送者
    EVENT_TYPE_1V1_RECV_IM = 3; // 私聊 接收者
    EVENT_TYPE_SENDIM_BOT = 4; // 运营后台助手推送
  }

  enum DataType {
    DATA_TYPE_UNSPECIFIED = 0;
    DATA_TYPE_TIMELINE_MSG = 1; // see Timeline.TimelineMsg
    DATA_TYPE_BATCH_IM_MSG = 2; // see Timeline.BatchImMsg
  }

  EventType event_type = 1; // 事件类型
  DataType data_type = 2; // 数据类型
  bytes data = 3;
  int64 create_ts = 4; // 事件创建时间戳
}

