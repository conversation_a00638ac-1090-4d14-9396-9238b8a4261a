syntax="proto2";
package kafka_ch_modify;

enum EVENT_TYPE
{
  EVENT_CHANE_CHANNEL_PWD = 1;	    // 房间改密码
  EVENT_CHANE_CHANNEL_NAME = 2;	    // 房间改名称
  EVENT_DISMISS_CHANNEL    = 3;			// 解散房间
	EVENT_CHANE_CHANNEL_SWITCH = 4;	  // 房间改开关
}

message ChannelModifyEvent
{
	required uint32 uid 		= 1;
	required uint32 ch_id		= 2;
	required uint32 type		= 3;   // EVENT_TYPE
	optional uint32 channel_type = 4;
	optional bytes opt_pb_info = 5;    // 根据不同的event_type 可能有不同的pb协议
	optional uint32 app_id = 6;
	optional uint32 bind_id = 7;
	optional uint64 op_ts_ms = 8;  // 毫秒时间戳
}

message ChannelPwdEventOpt
{
	optional bool is_before_has_pwd = 1;
	optional bool is_after_has_pwd = 2;
}

message ChannelNameEventOpt
{
	optional string channel_name = 1;
}

message ChannelSwitchEventOpt
{
	optional uint32 before_switch = 1;
	optional uint32 after_switch = 2;
}
