syntax = "proto3";

package kafkapresent;

message PresentEvent
{
  uint32 uid = 1;
  uint32 target_uid = 2;
  string order_id = 3;
  uint32 channel_id = 4;
  uint32 channel_type = 5;  // ga::ChannelType
  uint32 guild_id = 6;
  uint32 send_time = 7;
  uint32 item_id = 8;
  uint32 item_count = 9;
  uint32 price = 10;
  uint32 price_type = 11;    // userpresent::PresentPriceType
  uint32 item_source = 12;  // ga::PresentSourceType
  uint32 score = 13;          // 积分
  uint32 ranking_ratio = 14;  // 是否正常增加排行榜price,取值0-100

  uint32 add_rich = 15;
  uint32 add_charm = 16;
  uint32 receiver_guild_id = 17;
  uint32 giver_guild_id = 18;

  uint32 tag_type = 19;      // 礼物类型 see ga::PresentTagType
  uint32 send_method = 20;   //送礼方法 0，房间送礼；1，IM送礼
  uint32 msg_type = 21;      // 消息类型 0，默认  1 需要发RPC

  uint32 bind_channel_id = 22; // 绑定的房间id（用于官方频道）
  string deal_token = 23;

  string from_ukw_account = 24;  // 送礼神秘人账号，为空则不是神秘人
  string from_ukw_nickname = 25;  // 送礼神秘人昵称
  string to_ukw_account = 26;  // 收礼神秘人账号，为空则不是神秘人
  string to_ukw_nickname = 27;  // 收礼神秘人昵称
  uint32 batch_type = 28;       // 批量送礼类型

  uint32 channel_game_id = 29; //  房间弹幕游戏id
  bool is_virtual_live = 30; // 用户是否属于虚拟主播
  uint32 score_type = 31; // 积分类型 0 - 普通积分（不可提现） 1 - 可提现积分
  uint32 send_channel_id = 32; // 送礼房间id (通常与channel_id一致，特殊情况不一致)
}
