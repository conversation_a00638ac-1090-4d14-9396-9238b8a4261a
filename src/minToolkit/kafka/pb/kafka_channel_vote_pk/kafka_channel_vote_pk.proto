syntax = "proto3";

package kafkachannelvotepk;

// 房间投票pk - 投票事件
message ChannelVotePkVoteEvent {
   uint32 channel_id = 1; // 房间id
   uint32 start_timestamp  = 2; //开始时间戳
   uint32 from_uid = 3; // 投票用户
   uint32 target_uid = 4; // 目标用户
   uint32 vote_count = 5; // 票数
   uint32 type = 6; //enum see channel-vote-pk-go.proto ChannelVotePkType PK类型
   uint32 channel_type = 7; //房间类型
   uint32 vote_timestamp = 8; // 投票发生的时间戳
   uint32 base_vote_ticket_cnt = 9; /* 基础票数 */
}

enum VotePKChangeStatus{
   UnknownStatus = 0;
   StartStatus = 1;
   EndStatus = 2;
}
/* PK 开始 结束 */
message VotePKChangeEvent{
   uint32 status = 1; /* VotePKChangeStatus */
   uint32 channel_id = 2;
   uint32 start_timestamp  = 3; //开始时间戳
   uint32 pk_type = 4; //enum see channel-vote-pk-go.proto ChannelVotePkType PK类型
   uint32 pk_time = 5; /* 投票分钟数 */
}