syntax="proto2";

package kafkachanneldating;

// 相亲厅牵手成功事件
message ChannelDatingSuccessEvent{
    required uint32 channel_id = 1;
    required uint32 uid_a = 2;
    required uint32 value_a = 3;    // 心动值
    required uint32 uid_b = 4;
    required uint32 value_b = 5;    // 心动值
    required uint32 level = 6;      // 牵手场景等级
    required uint32 event_time = 7; // 发生事件的时间

    required uint32 level_score = 8;   // 牵手场景等级对应值

    optional uint32 scene_id = 9;      // 牵手场景id
    optional string scene_name = 10;   // 牵手名称
}