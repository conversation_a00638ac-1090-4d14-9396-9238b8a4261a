syntax="proto2";

package kafkaonline;

message FrinedUpdate
{
	required uint32 uid 		= 1;
	required uint32 target_uid	= 2;
	required bool   is_add		= 3;	// true:add friend, false:remove friend
	optional uint64 sequence 	= 4; 	// 事件发生时的序列号
	optional uint32 src_type 	= 5;	// 好友来源
	optional uint32 ev_ts 	= 6;
}

enum GroupUpdateEventType {
	GroupUpdateLegacy = 0;
	GroupUpdateCreateGroup = 1;
	GroupUpdateDismissGroup = 2;
	GroupUpdateVerifyChange = 3;
	GroupUpdateMemberChange = 4;
	GroupUpdateRoleChange = 5;
}

// 为了兼容以前的时间，当event-type = 0 的时候，就是以前的老事件的数据
// 新事件的内容是在 event_payload 内定义
message GroupUpdate {
	required uint32 group_id	= 1;
	required uint32 target_uid	= 2; // event_type = 0 老事件专用
	required bool is_add 		= 3; // event_type = 0 老事件专用
	optional bool is_dismiss 	= 4; // event_type = 0 老事件专用
	optional uint32 group_type  = 5; //群组类型, see: ga_base.proto -> enum GroupType
	optional uint32 role		= 6; // event_type = 0 老事件专用
	optional uint32 ev_ts 	= 7;
	optional string group_create_from = 8; // 适用于所有event type 包括老事件
	optional uint32 event_type = 9; // 历史原因，新增event_type 用来标识新event，以前的继续沿用老方式(上面的用户进出群)
	oneof event_payload { // 新event的具体内容
		GroupUpdateEventCreateGroup create_group = 10;
		GroupUpdateEventDismissGroup dismiss_group = 11;
		GroupUpdateEventVerifyChange verify_change = 12;
		GroupUpdateEventMemberChange member_change = 13;  // 针对需求新加的数据，event-type = 0的时候，有可能也设置这些数据
		GroupUpdateEventRoleChange role_change = 14;
	}
}

message GroupUpdateEventCreateGroup {
	optional uint64 creator = 1;
}

message GroupUpdateEventDismissGroup {
	optional uint64 operator = 1;
}

message GroupUpdateEventVerifyChange {
	optional uint64 operator = 1;
	optional uint32 current_status = 2;
}

message GroupUpdateEventMemberChange {
	optional uint64 member = 1;
	optional uint32 role = 2;
	optional bool is_join = 3;
	optional uint32 current_member_count = 4;
	optional uint32 max_member_count = 5;
	optional uint64 operator = 6;
}

message GroupUpdateEventRoleChange {
	optional uint64 member = 1;
	optional uint32 old = 2;
	optional uint32 current = 3;
}

message OnlineUpdate
{
	required uint32 uid 	= 1;
	required bool is_online = 2;
	optional uint32 os_type = 3; //see protodef.h EOSType 1-android,2-IOS
	optional bool is_logout = 4; //for ios logout, ios logout and offline is diffence.
	optional uint32 ts 	= 5;
}

message OnlineUpdateInfo
{
	required OnlineUpdate msg = 1;
	optional bool is_last_one= 2;
}
