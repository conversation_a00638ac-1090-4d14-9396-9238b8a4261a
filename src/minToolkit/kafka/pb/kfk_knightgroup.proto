syntax="proto3";

package kafka_knight_event;


enum JoinKnightGroupSource {
    NORMAL  = 0;              //正常T豆开通
    FREE_CARD  = 1;           //骑士体验卡开通
}
//加入骑士团
message JoinKnightGroupEvent {
    string order_id = 1;
    uint32 anchor_uid = 2; //主播UID
    uint32 knight_uid = 3;
    uint32 channel_id = 4;
    uint32 channel_type = 5;
    uint32 guild_id = 6;
    uint32 price = 7;           //如果是骑士体验卡开通price为0
    uint32 create_time = 8;
    uint32 begin_time  = 9;
    uint32 expire_time = 10;
    string deal_token  = 11;
    uint32 source      = 12;    //开通来源类型:JoinKnightGroupSource
    uint32 total_day   = 13;    //开通累计天数
}
