syntax="proto2";

package kafkachannel;

enum EVENT_TYPE
{
    EVENT_ENTER_CHANNEL = 1;	//进入房间 [事件已废弃]
    EVENT_LEAVE_CHANNEL = 2;	//离开房间 [事件已废弃]
    EVENT_ON_MIC = 3;		//上麦     [事件已废弃]
    EVENT_OFF_MIC = 4;		//下麦     [事件已废弃]
	
    EVENT_CHANE_CHANNEL_MICMODE = 5;	// 房间麦位模式切换 [事件已废弃]
    EVENT_CHANE_CHANNEL_PWD = 6;	// 房间改密码       [事件已废弃]
    EVENT_CHANE_CHANNEL_NAME = 7;	// 房间改名称

    EVENT_LIVE_FIN = 8;	    			// 直播结束
	
	EVENT_CHANGE_MIC = 9;			 // 更换麦位
	EVENT_EXPIRE_QUIT_CHANNEL = 10;	 // 超时退出房间
    EVENT_CHANNEL_DISMISS = 11;      //解散频道
    EVENT_SWITCH_FLAG_MODIFY = 12;   //房间推荐位开关改变
	EVENT_SEND_MSG = 13; //发送公屏事件
	
}

message ChannelEvent
{
    required uint32 uid 		= 1;
    required uint32 ch_id		= 2;
    required uint32 type		= 3;   // EVENT_TYPE
	
    optional uint32 mode 		  = 4; // 当前麦位模式(EChannelMicMode),0无效值没有意义(没有填值) 1主席 2自由 3娱乐 5高音质开黑
    optional uint32 channel_type  = 5; // 房间类型(ChannelType),0无效值没有意义(没有填值) 1普通公会房 2直播房 3个人房 4公会公开房 6临时游戏房
    optional uint32 remain_member = 6; // 房间当前人数

    optional uint32 online_second = 7; // 用户房间在线时长,仅在EVENT_LEAVE_CHANNEL用户退房事件中有填写
    optional uint32 remain_admin  = 8; // 房间剩余的管理员人数 仅在 EVENT_ENTER_CHANNEL / EVENT_LEAVE_CHANNEL 用户进房/退房事件中有填写
	
    optional bytes opt_pb_info = 9;    // 根据不同的event_type 可能有不同的pb协议
    optional uint32 app_id = 10;
    optional uint32 bind_id = 11;
    optional uint32 switch_flag = 12;
}

// [事件已废弃]
message ChannelEnterEventOpt
{
	optional bool is_pwd = 1;   // 进入的房间是否上锁
	
	optional uint64 ts_ms = 2;  // 毫秒时间戳
}

// [事件已废弃]
message ChannelMicModeEventOpt
{
	optional uint32 before_mic_mode = 1;
	optional uint32 after_mic_mode = 2;
}

message ChannelPwdEventOpt
{
	optional bool is_before_has_pwd = 1;
	optional bool is_after_has_pwd = 2;
}

message ChannelNameEventOpt
{
	optional string channel_name = 1;
}

// [事件已废弃]
message ChannelMicEventOpt
{
	optional uint32 mic_id = 1;
	optional uint32 mic_uid = 2;
	optional uint32 mic_user_sex = 3;
	optional uint32 mic_from_id = 4;
}
// [事件已废弃]
message ChannelMemberExpireOpt 
{
    optional uint32 mic_id = 1;
    optional uint32 channel_creater = 2;
    optional uint32 channel_display_id = 3;
    optional uint32 expire_ts = 4;
}

//解散频道
message ChannelDismissOpt
{
}

message ChannelSwitchFlagOpt
{
	required uint32 modify_type = 1;
	required uint32 switch_flag = 2;
}


