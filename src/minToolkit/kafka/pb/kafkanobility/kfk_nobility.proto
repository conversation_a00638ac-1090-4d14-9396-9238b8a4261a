syntax="proto2";

package kafkanobility;


message NobilityInfoChange
{
    required uint32 uid   = 1;
    required uint32 level = 2;
    optional uint64 nobility_value = 3;
    optional uint64 keep_nobility_value = 4;
    optional uint32 cycle_begin_ts = 5;
    repeated uint32 privilege_list = 6; //当前可用特权列表
	optional bool   visible_status = 7; //1隐身，0不隐身
    optional uint32 channel_id = 8;
    optional uint32 event_type = 9; //2无等级变化, 1升级，0降级,3 在线隐身状态变化，
    optional uint32 rank_value = 10; //T豆消费数据

    optional uint32 old_level = 11; //旧等级
    optional bool is_old_level_keep = 12;//旧等级是否已经保级
    optional bool is_new_level_keep = 13;//新等级是否已经保级

    optional float f_level = 14; // 浮点型贵族等级
    optional float f_old_level = 15; // 浮点型旧贵族等级
}

message NobilitySwitchFlagChange
{
    required uint32 uid   = 1;
    required uint32 swtich_flag = 2;
}

//神王加时事件
message NobilityExtentTimeEvent
{
    required uint32 uid   = 1;//uid
    required uint32 level = 2;//当前等级
    required uint32 cnt   = 3;//连续次数
    required uint32 create_at   = 4;//事件触发时间
}