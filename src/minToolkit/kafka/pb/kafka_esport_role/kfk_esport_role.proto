syntax = "proto3";

package kfk_esport_role;

// 电竞大神身份变更事件类型
enum CoachChangeType {
    COACH_CHANGE_TYPE_UNSPECIFIED = 0;
    COACH_CHANGE_TYPE_BECOME_COACH = 1;            // 直接签约成为个人大神
    COACH_CHANGE_TYPE_BECOME_GUILD_COACH = 2;      // 直接签约成为工会大神
    COACH_CHANGE_TYPE_PERSONAL_TO_GUILD_SIGN = 3;  // 个人大神 to 签约工会大神
    COACH_CHANGE_TYPE_GUILD_SIGN_TO_PERSONAL = 4;  // 工会签约大神 to 个人大神
    COACH_CHANGE_TYPE_RECLAIMED = 5;               // 回收大神身份
}

// 电竞大神身份变更事件
message EsportCoachChangeEvent {
  uint32 change_type = 1; // see CoachChangeType
  uint32 uid = 2;
  uint32 old_role = 3;    // esport_role.ESportErType
  uint32 new_role = 4;    // esport_role.ESportErType
  uint32 guild_id = 5;    // 签约工会id

  string audit_token = 6; // 身份变更审核唯一标识

  int64 ts = 7; // 事件时间戳
}

// topic : esport_coach_role_change