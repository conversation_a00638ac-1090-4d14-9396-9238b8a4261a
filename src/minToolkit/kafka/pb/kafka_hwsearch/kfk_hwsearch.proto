syntax = "proto2";

package kafka_hwsearch;

enum EKHWSEventType
{
  ENUM_SEARCH_USER_NAME = 1;     // 搜索用户名称
  ENUM_SEARCH_CHANNEL_NAME= 2;   // 搜索房间名称
  ENUM_SEARCH_GUILD_NAME= 3;     // 搜索工会名称
  ENUM_PRESS_CHANNEL_NAME = 4;    // 发布房间名称
}

message KHWSearchEvent
{
  required uint32 uid = 1;          // uid
  repeated string word_list = 2;    // 搜索内容
  required uint32 time_stamp = 3;   // 时间戳
  required EKHWSEventType type = 4; // 事件种类
}
