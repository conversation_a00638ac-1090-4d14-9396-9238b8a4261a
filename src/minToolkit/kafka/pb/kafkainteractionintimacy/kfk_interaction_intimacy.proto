syntax = "proto3";

package kafkainteractionintimacy;

message InteractionIntimacyChangeMsg {
    uint32 small_uid = 1;
    uint32 big_uid = 2;
    uint32 source = 3; // 玩伴来源
    uint32 relationship = 4; // 玩伴关系， 1： 好友
    uint32 update_time = 5;
    uint32 create_time = 6;
    map<uint32, IntimacyMission> intimacy_mission_map = 7;
    uint32 update_mission_id = 8;
}

message IntimacyMission {
    uint32 status = 1; // 0：禁用， 1: 启用， 2: 已完成
    uint32 current_progress = 2;
    uint32 max_progress = 3;
    uint32 update_time = 4;
    uint32 level = 5;
    uint32 status_type = 6;
}