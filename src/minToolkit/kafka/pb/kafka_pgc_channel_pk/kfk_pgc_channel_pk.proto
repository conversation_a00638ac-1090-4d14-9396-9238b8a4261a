syntax = "proto3";

package kfk_pgc_channel_pk;
 

// 每次PK中送礼触发
message PgcChannelPKSendPresentOpt {
  uint32 channel_id = 1;      
   uint32 uid = 2;           //送礼人UID
  uint32 target_uid = 3;    //收礼人UID
  string order_id = 4;      //订单号
  uint32 item_id = 5;       //礼物ID
  uint32 item_count = 6;    //礼物数量
  uint32 total_price = 7;   //订单总价值      
  string from_ukw_account = 8;  // 送礼神秘人账号，为空则不是神秘人
  string from_ukw_nickname = 9;  // 送礼神秘人昵称
  uint32 send_time = 10;  //送礼时间
}
 


// MVP
message PgcChannelPKMicRankInfo {
  uint32 from_uid = 1;               //送礼者
  uint32 to_uid = 2;                 //收礼者
  uint32 score = 3;                  //2送给1的送礼值
  uint32 rank = 4;                   //当前排名  
}

// PK单方结果
message PgcChannelPKMemResult {
  enum ResultType {
    Unknown = 0;
    Win = 1;  // 胜
    Draw = 2; // 平
    Loss = 3; // 败
  }
  uint32 channel_id = 1;                 //房间基本信息 比分MVP情况
  uint32 result_type = 2;                       // see ResultType
  repeated PgcChannelPKMicRankInfo end_mvp = 6;        //mvp动效信息  收礼1-3名排
}


message PgcChannelPKEventOpt {
  enum ResultType {
    Unknown = 0;
    Win = 1;  // 胜
    Draw = 2; // 平
    Loss = 3; // 败
  }

  uint32 from_channel = 1; 
  uint32 to_channel = 2;
  bool valid_pk = 3;                                   // 当前是否为有效局
  repeated PgcChannelPKMemResult result = 4;
 }

//PgcChannelPKEvent 
message PgcChannelPKEvent {
  enum EventType {
    Unknown = 0;
    PkBegin = 1;          // pk开始
    PkEnd = 2;            // pk结束
    SendPresent = 3;      // pk送礼
  }

  EventType event_type = 1;
  uint32 pk_id = 2;
  PgcChannelPKEventOpt event = 3;   
  PgcChannelPKSendPresentOpt present = 4; 
 }