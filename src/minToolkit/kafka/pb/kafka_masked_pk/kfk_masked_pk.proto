syntax = "proto3";

package kfk_masked_pk;

message MaskedPkConf {
  uint32 game_id = 1;           // 配置场次id
  uint32 withdraw_win_cnt = 2;  // 可提现最低胜利场数
  uint32 die_out_chip = 3;      // 淘汰最低筹码数（小于等于此值算淘汰）
}

message PkMemInfo {
  enum ResultType {
    Unknown = 0;
    Win = 1;  // 胜
    Draw = 2; // 平
    Loss = 3; // 败
  }

  uint32 channel_id = 1;
  uint32 pk_score = 2;        // pk值
  ResultType result_type = 3; // 结果
  int32 change_chip = 4;      // 变化筹码
  uint32 final_chip = 5;      // 变化后筹码
  uint32 curr_win_cnt = 6;    // 当前累计胜利场数
  uint32 present_score = 7;        // 送礼pk值(不包含巅峰对决加成)
}

message PkEventOpt {
  uint32 pk_id = 2;
  repeated PkMemInfo mem_list = 3;
  bool valid_pk = 4;              // 当前是否为有效局

  enum EndType {
    Common = 0; // 正常结束
    QuickKill = 1;// 斩杀
  }
  uint32 pk_end_type = 5;    // pk结束类型
}

message ReviveEventOpt {
  enum ReviveRetType {
    Fail = 0;   // 复活成功
    Success = 1;// 复活失败
  }
  ReviveRetType ret_type = 1;
  uint32 channel_id = 2;
  uint32 revive_score = 3;  // 复活期间流水
  int32 change_chip = 4;    // 复活后变化筹码数（复活失败为0）
  uint32 final_chip = 5;    // 变化后筹码
}

message JoinGameEventOpt {
  uint32 channel_id = 1;
  uint32 chip_cnt = 2;
}

message SettleAwardScoreOpt {
  uint32 channel_id = 1;
  uint32 target_uid = 2;
  uint32 award_score = 3;
}

message LiveMaskedPkEvent {
  enum EventType {
    Unknown = 0;
    PkBegin = 1;    // pk开始
    PkEnd = 2;      // pk结束
    ReviveRet = 3;  // 复活结果 ReviveEventOpt
    JoinGame = 4;   // 参与成功（领取筹码） JoinGameEventOpt
    GameEnd = 5;    // 比赛结束
    GameBegin = 6;  // 比赛开始
    SettleAwardScore = 7; // 结算奖励蒙面pk积分 SettleAwardScoreOpt
  }

  EventType event_type = 1;
  MaskedPkConf conf = 2;
  PkEventOpt pk_event_opt = 3;
  ReviveEventOpt revive_event_opt = 4;
  JoinGameEventOpt join_game_opt = 5;
  uint32 event_ts = 6;
  SettleAwardScoreOpt settle_award_opt = 7;
}

message EntertainmentMaskedPkEvent {
  enum EventType {
    Unknown = 0;
    PkBegin = 1;    // pk开始
    PkEnd = 2;      // pk结束
    ReviveRet = 3;  // 复活结果 ReviveEventOpt
    JoinGame = 4;   // 参与成功（领取筹码） JoinGameEventOpt
    GameEnd = 5;    // 比赛结束
    GameBegin = 6;  // 比赛开始
    SettleAwardScore = 7; // 结算奖励蒙面pk积分 SettleAwardScoreOpt
  }

  EventType event_type = 1;
  MaskedPkConf conf = 2;
  PkEventOpt pk_event_opt = 3;
  ReviveEventOpt revive_event_opt = 4;
  JoinGameEventOpt join_game_opt = 5;
  uint32 event_ts = 6;
  SettleAwardScoreOpt settle_award_opt = 7;
}