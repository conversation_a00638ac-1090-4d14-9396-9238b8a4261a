syntax="proto2";

package kafka_tt_auth_ev;
option go_package = "golang.52tt.com/protocol/services/minToolkit/kafka/pb/tt_auth_ev";

enum ETT_AUTH_EVENT_TYPE
{
	ENUM_TTAUTH_REG = 1;	// 注册
	ENUM_TTAUTH_LOGIN = 2;	// 登录
	ENUM_TTAUTH_SDKGAME_LOGIN  = 3 ; //SDK游戏登录
}

message TTAuthEvent
{
	required uint32 event_type  = 1;   // ETT_AUTH_EVENT_TYPE
	optional uint32 uid 		= 2;   
	optional string phone       = 3;
	optional string imei        = 4;
	optional string idfa        = 5;
	optional uint32 client_type = 6;
	optional string device_id_hex = 7;
	optional string ip = 8;          // 客户端公网IP
	optional uint32 ts = 9;
	optional bool is_anti = 10;
	optional uint32 app_id = 11;
	optional uint32 market_id = 12;
	optional uint32 cmd_id = 13;
	optional uint32 client_version = 14;
	optional bool is_auto_login = 15;
	optional string pkg_channel = 16;    // 客户端本次事件所携带的 客户端渠道号
	optional string sm_device_id = 17;   // shumei deviceid
	optional string device_model = 18;
	optional int64 sdk_game_id   = 19;   // 登录游戏id
	optional uint32 last_login_ts = 20;  // 上次登录时间
	optional string device_info = 21;
	optional string oaid = 22;
	optional string android_id = 23;
	optional uint32 third_party_type = 24;    // 第三方账号类型 ga::THIRD_PARTY_TYPE 0表示没有使用三方注册/登录
	optional string third_party_open_id = 25; // 第三方认证获得的open_id; appleid:必填: apple user id
	optional string third_party_union_id = 26; 
	optional uint32 source = 27;
	optional string third_party_app_id = 28; 
	optional uint32 terminal_type = 29;
    optional string os_ver = 30;        // 操作系统版本
    optional string os_type = 31;       // 操作系统类型
    optional uint32 screen_width = 32;  // 分辨率宽度
    optional uint32 screen_height = 33; // 分辨率高度
}










