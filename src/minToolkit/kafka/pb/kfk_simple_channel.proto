syntax="proto2";

package kafka_simple_ch;

enum ESIMPLE_EVENT_TYPE
{
	ENUM_SIMPLE_ENTER = 1;	//进入房间
	ENUM_SIMPLE_LEAVE = 2;	//离开房间
	ENUM_SIMPLE_EXPIRE_QUIT = 3;	// 用户由于超时事件被处理 已经退出房间

	ENUM_SIMPLE_EXPIRE_NOTIFY = 4;	// 用户超时事件发出 此时还并没有退出

	ENUM_SIMPLE_ADMIN_UPDATE = 5;	// 管理员变更事件

	ENUM_SIMPLE_MUTE = 6;	// 静音
	ENUM_SIMPLE_UNMUTE = 7;	// 解除静音
	ENUM_SIMPLE_PCHELPER_NOTIFY = 8;	// 使用PC助手
}

enum CHANNEL_TYPE
{
	DEFAULT = 0; // 无效值
	COMMON_GAME_UNION_CHANNEL = 1; // 普通公会
	LIVE_CHANNEL = 2; // 直播房
	TOPIC_CHANNEL = 3; // 个人房
	PUBLIC_GAME_UNION_CHANNEL = 4; // 公会公开房
	TEMPORARY_GAME_CHANNEL = 6;
}

message ChSimpleEvent
{
	required uint32 uid 		= 1;
	required uint32 ch_id		= 2;
	required uint32 event_type  = 3;   // EVENT_TYPE

	optional uint32 channel_type  = 4; // 房间类型(ChannelType),0无效值没有意义(没有填值) 1普通公会房 2直播房 3个人房 4公会公开房 6临时游戏房
	optional bytes opt_pb_info    = 5; // 根据不同的event_type 可能有不同的pb协议
}

message ChSimpleLeaveOpt
{
	optional uint64 ts_ms = 1;  // 毫秒时间戳
	optional uint32 remain_membercnt = 2;
	optional uint32 remain_admincnt = 3; //该数据不准确，不建议使用
	optional uint32 online_second = 4;
	optional uint32 op_uid = 5;  //区分被踢出房还是自己退房
	optional uint32 creater_uid = 6;        //房间创建者uid
	optional uint32 channel_display_id = 7; //老的显示id，不建议使用，新业务使用channel_view_id
	optional string channel_view_id = 8;    //房间显示Id
	optional uint32 last_enter_ts = 9;      //seconds, 上次进房时间，如果还有记录的话
	optional uint32 app_id = 10;
	optional uint32 market_id = 11;
}

message ChSimpleEnterOpt
{
	optional uint64 ts_ms = 1;  // 毫秒时间戳
	optional bool is_pwd = 2;   // 进入的房间是否上锁
	optional uint32 remain_membercnt = 3;
	optional uint32 remain_admincnt = 4;
	optional uint32 nobility_level = 5; //贵族等级
	optional bool invisible = 6; //是否隐身
	optional uint32 source = 7; //进房来源
	optional uint32 follow_friend_uid = 8; //跟随进房uid
	optional uint32 last_cid = 9; // 从其他房间进房的情况，携带上一个房间id
	optional uint32 channel_display_id = 10; //老的显示id，不建议使用，新业务使用channel_view_id
	optional string channel_view_id = 11;    //房间显示Id
	optional uint32 app_id = 12;
	optional uint32 market_id = 13;
}

message ChSimpleExpiredNotifyOpt
{
	optional uint32 expire_ts = 1;  // 毫秒时间戳
	optional uint32 channel_switch = 2;
	optional uint32 mic_id = 3;
    optional uint32 channel_creater = 4;
    optional uint32 channel_display_id = 5;
	optional uint32 channel_bind_id = 6;
}

message ChSimpleAdminUpdateOpt
{
	required uint32 target_uid = 1;
	required uint32 op_type = 2;		// 1是增加管理员 2是删除管理员
	required uint32 admin_role = 3;		// 管理员类型 see channel_.proto EChannelAdminRoleType

	optional uint32 ts_ms = 4;  		// 毫秒时间戳
}






