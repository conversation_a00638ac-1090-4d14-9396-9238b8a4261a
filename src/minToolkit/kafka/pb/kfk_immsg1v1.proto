syntax="proto2";

package kafkaimmsg1v1;

// 1v1聊天uid 事件通知
message IMMsgEvent
{
	required uint32 uin = 1;
	required uint32 target_uid = 2;
	optional bool is_uin_normal_user = 3; // 除去客服、官方运营号、tt语音助手
	optional bool is_target_normal_user = 4; // 除去客服、官方运营号、tt语音助手
	optional uint32 msg_source_type = 5; //消息来源类型
	optional string msg_text = 6; //消息内容
	optional uint32 svr_id = 7; //消息id
	optional uint32 msg_type = 8; //消息类型
	optional bool is_stranger = 9; // 是否是陌生人聊天
	optional uint32 event_ts = 10; //事件时间戳
	optional uint32 risk_control_type = 11; //风控类型  see RiskControlType
}

enum RiskControlType
{
	// 未定义，不命中任何风控
	RISK_CONTROL_TYPE_UNSPECIFIED = 0;
	// IM 自见，发送的消息仅自己可见
	RISK_CONTROL_TYPE_IM_SEEN_SELF = 1;
}