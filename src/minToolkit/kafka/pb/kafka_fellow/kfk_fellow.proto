syntax = "proto3";

package kafkafellow;

enum FellowType {
  ENUM_FELLOW_TYPE_UNKNOWN = 0;  // 未知
  ENUM_FELLOW_TYPE_BRO = 1;  // 基友
  ENUM_FELLOW_TYPE_LADYBRO = 2;  // 闺蜜
  ENUM_FELLOW_TYPE_INTIMATE = 3;  // 知己
}

enum FellowBindType {
  ENUM_FELLOW_BIND_TYPE_UNKNOWN = 0;  // 没有关系
  ENUM_FELLOW_BIND_TYPE_UNIQUE = 1;  // 唯一
  ENUM_FELLOW_BIND_TYPE_MULTI = 2;  // 非唯一
}

  
message FellowPointChangeEvent {
    uint32 uid = 1;          //uid
    uint32 fellow_uid = 2;   //挚友UID
    uint32 fellow_type = 3;    //see FellowType
    uint32 bind_type = 4;    //see FellowBindType
    uint32 point = 5;        //挚友值
    uint32 level = 6;        //挚友等级
    string present_url = 7;  //挚友信物
    int64 timestamp = 8;     //上报的时间戳 时间秒
    int64  current  = 9;     //存量数据为0
    uint32 add_point = 10;   //当次新增挚友值
    string present_bg_url = 11;   //信物背景图片
    uint32 last_level = 12;   //加分前等级
    string fellow_name = 13;  //挚友名称
 }

 
message FellowUnBindEvent {
    uint32 uid = 1;//uid
    uint32 fellow_uid = 2;//挚友UID
    uint32 fellow_type = 3;    //see FellowType
    uint32 bind_type = 4;    //see FellowBindType
    int64 timestamp = 5;//上报的时间戳 时间秒
}

message FellowBindEvent {
    uint32 uid = 1;//uid
    uint32 fellow_uid = 2;//挚友UID
    uint32 fellow_type = 3;    //see FellowType
    uint32 bind_type = 4;    //see FellowBindType
    int64 timestamp = 5;//上报的时间戳 时间秒
}


