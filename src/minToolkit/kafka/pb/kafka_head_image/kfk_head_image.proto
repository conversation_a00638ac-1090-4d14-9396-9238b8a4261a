syntax="proto2";

package kafka_head_image;

// 用户头像审核通过成功修改事件
message HeadImageChangeEvent
{
	required uint32 uid		= 1;    
	required string account = 2;
	required string version = 3;   //md5
	optional uint32 account_type = 4;
}
 
// 通用头像事件
message CommHeadImageEvent
{
    enum EventType {
        EVENT_TYPE_UNSPECIFIED = 0;
        EVENT_TYPE_AUDIT_PASS = 1; // 审核通过，修改头像
        EVENT_TYPE_AUDIT_REJECT_BAN = 2; // 审核拒绝，屏蔽头像
        EVENT_TYPE_AUDIT_REJECT_NOTIFY = 3; // 审核拒绝，仅通知
    }

    optional EventType event_type = 1;
    optional uint32 uid = 2;
    optional string account = 3;
    optional string version = 4;
    optional uint32 market_id = 5;
    optional uint32 terminal_type = 6;
    optional uint32 upload_source = 7;
    optional string device_id = 8;
}

