syntax = "proto3";

package kafkatbean;


enum TBeanConsumeSourceType
{
  ENUM_UNKOWN_TYPE = 0;  //
  EVENT_EVENT_PRESENT = 1; //送礼
  ENUM_BUY_YKW_TYPE = 2;  //购买神秘人
  ENUM_WEREWOLF_BUY_IDENTITY = 3;  //狼人杀购买身份
  ENUM_WEREWOLF_BUY_TIME = 4;  //狼人杀购买时间
  ENUM_TREASURE_HOUSE_BUY = 5; // 珍宝馆购买权限
  ENUM_BUY_FELLOW_HOUSE = 6; // 购买挚友小屋
  ENUM_VIRTUAL_IMAGE_BUY = 7; // 虚拟形象购买
}

message TBeanConsumeEvent
{
  uint32 uid = 1;
  uint32 value = 2;
  uint32 timestamp = 3;
  string source = 4;      //文字说明
  string order_id = 5;
  uint32 source_type = 6; //enum TBeanConsumeSourceType
  uint32 channel_id = 7;
  string deal_token = 8;
  uint32 target_uid = 9;
}


//