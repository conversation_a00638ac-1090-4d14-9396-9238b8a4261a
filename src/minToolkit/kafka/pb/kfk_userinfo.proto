syntax="proto2";

package kafka_user_info;

enum EVENT_TYPE
{
  EVENT_REG = 1;    //注册后首次登陆
  EVENT_LOGIN = 2;  //用户登录
  EVENT_LOGOUT = 3; //用户下线
  EVENT_NAME_CHG = 4;  //修改昵称等
  EVENT_LEVEL_CHG = 5;  //级别变动
  EVENT_CERTIFY = 6;  //认证为大V
  EVENT_UNCERTIFY = 7;  //取消大V认证
  EVENT_UNREG = 8;  //用户注销
  EVENT_FORBID = 9;  //用户被封禁
  EVENT_UNFORBID = 10;  //用户解封禁
  EVENT_PWD_CHG = 11;   //用户修改密码
  EVENT_SEX_CHG = 12;   //用户修改性别 UserSexEventOpt
  EVENT_SIGN_CHG = 13; // 用户个性签名修改 UserSignatureEventOpt
}

message KUserInfoUpdateEvent
{
  required uint32 uid = 1;
  required uint32 sex = 2;
  required uint32 registered_timestamp = 3;
  required uint32 update_timestamp = 4;
}

message UserEvent
{
	required uint32 uid 		= 1;
	required uint32 type		= 2;   // EVENT_TYPE
    required uint32 update_ts = 3;
	optional bytes opt_pb_info = 4;    // 根据不同的event_type 可能有不同的pb协议
}

message UserUnregEventOpt
{
	optional string app_name = 1;
}

message UserNameEventOpt
{
	required string nickname = 1;
	required uint32 level = 2;
}

message UserLevelEventOpt
{
	required uint32 origin_level = 1;
	required uint32 level = 2;
}

message UserCertifyEventOpt
{
	required string certify_style = 1;
}

message BanUserEventOpt
{
  optional uint32 op_type = 1;    //BAN_OP_TYPE
  optional string device_id = 2;
  optional string client_ip = 3;
  optional uint32 at = 4;
  optional uint32 recovery_at = 5;
  optional string reason = 6;
  optional string operator_id = 7;
  optional string proof_pic = 8;
  optional string ext_info = 9;
  optional bool  no_log = 10; //不记日志
  optional string reason_detail = 11;
}

message UserSexEventOpt
{
  required uint32 sex = 1;
  required uint32 registered_at= 2;
}

message UserSignatureEventOpt
{
  optional string signature = 1;
}