syntax = "proto3";

package kafkaplayroom;

option go_package="golang.52tt.com/protocol/services/src/minToolkit/kafka/pb/kafkaplayroom";

message PlayerInfo {
    uint32 user_id = 1; //房间的人的uid
    uint32 total_time = 2;//当前的统计的总时间 时间秒
}

//客户端每分钟上报的情况
message PlayroomEvent {
    uint32 room_owner_uid = 1;//房主
    repeated PlayerInfo total_player_list = 2;//房间的在线用户id
    repeated PlayerInfo  team_player_list = 3;//房间的小队在线用户id
    repeated PlayerInfo microphone_player_list = 4;//房间的在线且在麦位上用户id
    int64 timestamp = 5;//上报的时间戳 时间秒
    uint32 room_id = 6;//房间id
    uint32 channel_type = 7;//房间类型 
}

//用户切换到后台/前台
message AppSwitchGroundEvent {
    uint32 uid = 1;         // 上报用户
    bool background = 2;    // 切换到 后台（true） / 前台（false）
    
    uint32 room_id = 3;     // 所在房间
}


