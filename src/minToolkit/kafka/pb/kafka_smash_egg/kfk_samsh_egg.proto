syntax = "proto3";

package kfk_smash_egg;

// 砸蛋方式
enum Source{
  Manual = 0;  //手动
  AUTO = 1;  //自动
}

// 砸蛋状态
enum Flag {
  NORMAL = 0;  //正常
  MORPH = 1;  //变身
}

// 砸蛋方式
enum Mode {
  NORMAL_MODE = 0;  //普通
  GOLD_MODE = 1;  //金色
}


// 转转中奖事件
message SmashEggWinningEvent {
  string order_id = 1;
  uint32 uid = 2;
  Source source = 3;    //中奖来源，0：手动， 1：自动
  Flag flag = 4;      //0: 普通礼物 1：变身礼物
  Mode mode = 14;

  uint32 pack_id = 5;    //包裹id
  uint32 pack_worth = 6; //包裹价值
  string pack_name = 7;  //包裹名
  string gift_pic = 8;   //礼物图片
  uint32 gift_amount = 9;  //包裹中礼物数量
  string pack_desc = 10;  //包裹描述，备用

  uint32 create_time = 11;
  uint32 channel_id = 12;
}