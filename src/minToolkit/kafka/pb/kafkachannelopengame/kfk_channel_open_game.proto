syntax = "proto3";

package kafkachannelopengame;

// use channel_open_game_controller.ChannelGameModeInfo
message ChannelGameModeInfo {
    string mode_key = 1;                   //模式key
    string game_param = 2;                 //游戏参数
    repeated uint32 player_limit_list = 3; //限制人数
}

message ChannelOpenGameStatus {
    uint32 channel_id = 1;
    uint32 game_id = 2;
    uint32 game_status = 3;

    int64 game_load_seq = 4;
    int64 game_change_seq = 5;

    repeated uint32 join_uids = 6;
    repeated uint32 ready_uids = 7;
    repeated uint32 loaded_uids = 8;
    ChannelGameModeInfo game_mode = 9;
}