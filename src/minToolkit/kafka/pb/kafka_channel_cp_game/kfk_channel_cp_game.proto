syntax = "proto3";

package kfk_channel_cp_game;

message CpTeamInfo {
  uint32 uid_a = 1;
  uint32 uid_b = 2;
  uint32 cp_score = 3;
  uint32 levelScore = 4;   // 场景等级值，仅该场次第一名有
}

message ChannelCpGameEvent {
  enum EventType {
    Unknown = 0;
    CreateTeam = 1; // 组队成功
    FinishGame = 2; // 完成cp战
  }

  uint32 channel_id = 1;
  uint32 event_type = 2;
  repeated CpTeamInfo team_rank_list = 3; // 队伍信息，按排名先后排序
  uint32 event_time = 4;
}