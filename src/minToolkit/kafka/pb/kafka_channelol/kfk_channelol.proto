syntax="proto3";

package kafka_channelol;

message ChannelEvent {
  oneof event {
      ChannelEnterEvent enter_event = 1;
      ChannelLeaveEvent leave_event = 2;
  }
}

// 进退房事件基本信息
message ChannelEventBase {
  uint32 uid = 1;
  uint32 ch_id = 2;
  uint32 channel_type = 3;      // see app/channel_.proto ChannelType
  uint32 creater_uid = 4;       // 房间创建者uid
  string channel_view_id = 5;   // 房间显示Id
  uint32 remain_member_cnt = 6;
  uint64 ts_ms = 7;
  uint32 app_id = 8;            // 非用户主动触发事件时该字段为空，如被踢出房，超时导致退房等
  uint32 market_id = 9;         // 非用户主动触发事件时该字段为空，如被踢出房，超时导致退房等
}

// 玩法信息
message ChannelSchemeInfo {
  uint32 scheme_id = 1;           // 对应之前的tabId
  string scheme_name = 2;
  uint64 scheme_switch_time_ms = 3; // 玩法切换服务端时间戳，毫秒
  uint32 scheme_detail_type = 4;  // 玩法详细类型，用于区分不同玩法类型，see app/channel-scheme_.proto SchemeDetailType
}

message ChannelEnterEvent {
  ChannelEventBase base_info = 1;
  ChannelSchemeInfo scheme_info = 2;
  bool is_pwd = 3;
  uint32 nobility_level = 4;     // 贵族等级
  bool invisible = 5;            // 是否隐身
  uint32 source = 6;             // 进房来源
  uint32 follow_friend_uid = 7;  // 跟随进房uid
  uint32 last_cid = 8;           // 从其他房间进房的情况，携带上一个房间id
}

message ChannelLeaveEvent {
  ChannelEventBase base_info = 1;
  ChannelSchemeInfo scheme_info = 2;
  uint32 online_second = 3;
  uint32 op_uid = 4;         // 区分被踢出房还是自己退房
  uint32 last_enter_ts = 5;  // 上次进房时间，如果还有记录的话，秒级时间戳
  bool is_expire_quit = 6;   // 是否由于超时事件被处理退出房间
}