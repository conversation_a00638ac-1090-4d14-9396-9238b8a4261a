syntax="proto2";

package kafka_usertag;


message KUserTagUpdateEvent
{
  required uint32 uid = 1;
  repeated KTagInfo tag_list = 2;              // 一般标签
  repeated KGameTagInfo game_tag_list = 3;     // 游戏标签 
  required uint32 update_timestamp = 4;         // 更新时间戳
  optional uint32 setting_cnt = 5;              // 设置次数，0，默认值。1 注册流程第一次设置
}

message KTagInfo
{
  required uint32 tag_id = 1;
  required string tag_name = 2;
  required uint32 tag_type = 3;
}

message KGameTagInfo 
{
  required uint32 tag_id = 1;
  required string tag_name = 2;
  required uint32 tag_type = 3;
  repeated KGameTagExt game_ext_list = 4;      // 游戏标签二级属性
  optional string game_nick = 5;               //游戏昵称
  optional string game_screenshot =6;            //游戏截图
  optional uint32 tab_id = 7;                  //通用游戏卡配置，对应的所属tab
}

message KGameTagExt
{
  required string opt_name = 1;
  repeated string value_list = 2;
  repeated GameCardInputVal input_val = 4;    // 输入值
}

message GameCardInputVal {
  optional string elem_title = 1; // 标题
  optional string elem_val   = 2; // 输入的值
}