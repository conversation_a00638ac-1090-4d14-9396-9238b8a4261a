syntax="proto2";

package kafkafootmark;

message FootmarkRec{
    required string appid = 1;   // 取值: ttabse,sdk, 用于选择存储集群
    required string id = 2;  	 //实体标识: uid, group_id 等
    required uint32 at = 3;		// unix time(0)
    required string biz_type = 4 ; // 业务类型, 取值:login/channelactivity/groupentry/im/groupim/channelim/ugc
    required bytes biz_data = 5; // 业务数据
}

//登录
message UserLoginData {
    //账号
    required uint32 uid = 1;
    optional string account = 2; //tt 账号, 对应: user 表 username

    //登录
	required string login_type = 3; //登录类型:REG/MANUAL/AUTO_LOGIN/SDK_ACTIVATE (全是大写)
	optional string login_username = 4;
	required int32 result = 5;  // 结果
	optional uint32 third_party_type = 6;   // 第三方账号类型
	optional string third_party_openid = 7; // openid
    required uint32 login_at = 8;

    //设备
	optional string imei = 9;
	optional string os_ver = 10;				// 操作系统版本
	optional string os_type = 11;				// 操作系统类型
	optional string device_model = 12;			// 机器型号
	optional string device_info = 13;			// 设备信息
	optional uint32 is_emulator = 14;			// 是否模拟器
	optional string device_id = 15;				// 设备号

    // 客户端
    optional uint32 terminal_type = 16;         // 终端类型(包含平台/操作系统/APPID)
    optional string client_type = 17;           // 客户端类型(仅限TT -- 0: Android, 1: iOS)
	optional uint32 client_ver = 18;			// 客户端版本类型
	optional string pkg_signature = 19;		    // 签名
    optional string client_channel_id = 20;     // 客户端渠道号

    optional string client_ip = 21;				// 客户端ip
    optional int32 client_port = 22;
    optional string client_ip_location = 23;     // 客户端ip归属地

    optional string alias = 24; 
    optional string phone = 25;
    optional string nickname = 26;
    optional string avatar = 27;
    optional string signature = 28;  //个人签名

    optional string proxy_ip = 29;
    optional int32 proxy_port = 30;

    optional int32 client_id = 31;
    
    optional uint32 market_id = 32;
}

//房间事件:  进房/退房 上麦/下麦
message ChannelMemberEnterData {
}
message ChannelMemberLeaveData {
    required uint32 duration = 1;
}
message ChannelMemberHoldMicData {
    optional uint32 mic_id = 1;
}
message ChannelMemberReleaseMicData {
    optional uint32 mic_id = 1;
}
message ChannelMemberEventData {
	enum EventType {
		ENTER_CHANNEL = 1;
		LEAVE_CHANNEL = 2;
		HOLD_MIC = 3;
	    RELEASE_MIC = 4;
    }
    required uint32 channel_id = 1;
    required uint32 appid = 2;
    required uint32 uid = 3;
    required int32 event = 4; //enter,leave,holdmic,releasemic
    required uint32 at = 5;
    optional uint32 channel_display_id = 6;
    optional string channel_type = 7;

    optional ChannelMemberEnterData enter_channel = 8;
    optional ChannelMemberLeaveData leave_channel = 9;
    optional ChannelMemberHoldMicData hold_mic = 10;
    optional ChannelMemberReleaseMicData release_mic = 11;

    // 用户信息 for wa
    optional string account = 12;
    optional string ip = 13;
    optional int32  port = 14;
    optional string imei = 15;
    optional string os_ver = 16;
    optional string os_type = 17;
    optional string device_model = 18;
}


// 群进出事件
message GroupEntryEventData {
    enum EntryType {
        JOIN_GROUP = 1;
        QUIT_GROUP = 2;
    }
    required uint32 uid = 1;
    required int32 event = 2;
    required uint32 at = 3;
    required uint32 group_id = 4;
    optional uint32 group_type = 5;
    optional uint32 creator_uid = 6;
    optional uint32 create_at = 7;
    optional uint32 display_id = 8;
    optional string name = 9; 

    optional string account = 10;
    optional string nickname = 11;
    optional uint32 role = 12;
    optional uint32 inviter_uid = 13;  // 拉入群操作人，用于游戏群，临时群，T群
    optional string inviter_account = 14;
//    optional string last_chat_at = 13;
}

// 群变更事件
message GroupChangeEventData {
    enum ChangeType {
        CREATE_GROUP = 1;
        DISMISS_GROUP = 2;
        //MODIFY_GROUP = 3;
    }
    required uint32 uid = 1;
    required uint32 group_id = 2;
    required int32 change_type = 3;
    required uint32 at = 4;
    optional uint32 group_type = 5;
    optional uint32 create_at = 6;
    optional uint32 display_id = 7;
    optional string name = 8; 

    // 创建者信息
    optional string account = 9;
    optional string nickname = 10;
    optional string ip = 11;
    optional int32  port = 12;
    optional string imei = 13;
    optional string device_id = 14;

//    optional string group_avatar = 16;
}

//biz_type:friend
message FriendshipChangeData {
    enum ChangeType{
        ADD_FRIEND = 1;
        DEL_FRIEND = 2;
        ADD_FOLLOW = 3;
        DEL_FOLLOW = 4;
    }
    required uint32 uid = 1;
    required uint32 target_uid = 2;
    required int32 change_type = 3;
    optional uint32 at = 4;
    optional string account = 5;
    optional string nickname = 6;
    optional string target_account = 7;
    optional string target_nickname = 8;
    optional string target_phone = 9;
}

// im 消息

enum ContentType {
    CT_INVALID = 0;
    CT_TEXT = 1;
    CT_IMAGE = 2;
    CT_VOICE = 3;
    CT_TEXT_AT_ONE = 4;
    CT_TEXT_AT_ALL = 5;
    /*CT_EMOTICON = 6;*/
}
message TextContent{
    required bytes data = 1;
}

message ImageContent {
    enum Type {
        RAW = 1;
        URL = 2;  //未使用? 废弃?
        KEY = 3; //对象存储 key
    }
    required bytes data = 1; //raw data
    required int32 type = 2; 
    required string image_format = 3; //业务填充：JPG, PNG, GIF, 缺省是JPG
    optional string key = 4; //type=KEY时，存 key 值
}
message VoiceContent {
    enum AV_CODEC {
        AV_CUSTOM_OPUS = 1; //原始格式
        AV_PCM = 2; //
        AV_MP3 = 3; //
    }
    required bytes data = 1; //raw data
    optional int32 codec = 2; //AV_CODEC 
    optional string key = 3; //对象存储对象 key
}

message IMData {
    enum TargetTo{
        TO_USER = 1;
        TO_GROUP = 2;
    }
    required uint32 from_uid = 1;
    optional string from_account = 2;
    required uint32 target_type = 3;    //TargetTo
    required uint32 target_id = 4;
    optional string target_account = 5;
    required uint32 at = 6;
    required uint32 content_type = 7;   //ContentType
    required bytes content = 8; //

    //发送者信息: 暂时群消息需要填充
    optional string sender_ip = 9;
    optional int32  sender_port = 10;
    optional string sender_device_id = 11;
    optional string sender_imei = 12;

    // 消息id, 使用 svr_msg_id
    optional int64 msg_id = 13; 

    optional string from_nickname = 14;
    optional string target_nickname = 15;
    optional uint32 group_display_id = 16;

    optional uint32 msg_source_type = 17; //消息来源类型 see im.proto 中 MsgSourceType
}

//
message ChannelImData {
    required uint32 from_uid = 1;
    optional string from_account = 2;
    required uint32 channel_id = 3;
    required uint32 appid = 4;
    optional uint32 channel_display_id = 5;
    optional string channel_type = 6;
    required uint32 at = 7;
    required uint32 content_type = 8;   //ContentType
    required bytes content = 9; //
}

//
//ugc: Post/Comment
//
message UgcUserInfo {
    required uint32 uid = 1;
    optional string account = 2;
    optional string alias = 3;
    optional string nickname = 4;
    optional string client_ip = 5;
    optional int32  client_port = 6;
    optional string imei = 7;
}

message UgcAttachmentInfo {
    enum AttachmentType {
        None = 0;
        IMAGE = 1;
        GIF = 2;
        VIDEO = 3;
        CMS = 4; // cms帖子
    }
    required AttachmentType type = 1; 
    required string content = 2; // 附件内容,一般来说是url
}
message UgcPostEvent {
    enum OP_TYPE {
        POST_OP_ADD = 1;
        /*POST_OP_DELETE = 2;*/
        /*POST_OP_BANNED = 3; */
    }
    required int32 op_type = 1;
    required string post_id = 2;
    optional string content = 3;
    repeated UgcAttachmentInfo attachments = 4;
    optional uint32 create_at = 5; // 发帖时间, unix second
    optional UgcUserInfo creator = 6;
}

message UgcCommentEvent {
    enum OP_TYPE {
        COMMENT_OP_ADD = 1;
      
    }
    required int32 op_type = 1;
    required string comment_id = 2;
    optional string ref_id = 3;  //被评论的 post_id or comment_id
    optional string post_id = 4;
    optional string content = 5;
    repeated UgcAttachmentInfo attachments = 6;
    optional uint32 create_at = 7;
    optional UgcUserInfo creator = 8;
    optional UgcUserInfo reply_to = 9;
}

message UgcEventData {
    enum EventType {
        UGC_ET_POST = 1;
        UGC_ET_COMMENT = 2;
    }
    required int32 event_type = 1; //UgcEventType
    required uint32 at = 2; 
    optional UgcPostEvent post = 3;
    optional UgcCommentEvent comment = 4;
}




