syntax="proto3";

package kafka_fans_event;

enum EnumAnchorEventType {
    E_NEW_FANS = 0;              // 加入粉丝团
    E_POPULARITY_CHANGE = 1;     // 人气值变化
    E_LEAVE_FANS = 2;  // 退出粉丝团
}

//主播事件
message AnchorInfoEvent {
    uint32 event_type      = 1;   // see EnumFansEventType
    uint32 fans_uid        = 2;   //粉丝uid
    uint32 anchor_uid      = 3;   //主播uid
    uint32 tag_id          = 4;   //房间类型
    uint32 join_at         = 5;   //加入时间
    uint32 channel_id      = 6;   //房间ID
}

//亲密值变化事件
message FansLoveValueChange {
    uint32 fans_uid          = 1; //粉丝uid
    uint32 anchor_uid        = 2; //主播uid
    uint32 love_value        = 3; //亲密值变化值
    uint32 channel_id        = 4;
    uint32 update_ts         = 5;
    uint32 mission_id        = 6;   // 任务id
    uint32 new_level         = 7;  // 新的粉丝等级，亲密值变化后的等级，可能和old_level一样
    bool   is_light_plate    = 8;   // 是否重新点亮粉丝铭牌
    uint32 old_level = 9;  // 亲密值变化前的等级
}
