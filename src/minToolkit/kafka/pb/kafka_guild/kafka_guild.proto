syntax="proto2";

package kafka_guild;

enum EKGuildEventType
{
  ENUM_GUILD_CREATE = 1;
  ENUM_GUILD_DISMISS = 2;
  ENUM_GUILD_JOIN = 3;
  ENUM_GUILD_QUIT = 4;
  ENUM_GUILD_RENAME = 5;
}

message KGuildEvent
{
  required uint32 guild_id = 1;
  required uint32 uid = 2;
  required uint32 event_type = 3;
  optional uint32 ts = 4;       // time stamp
  optional string guild_name = 5;
}

message UserGroupChange{
  required uint32 uid = 1;
  repeated uint32 group_ids = 2;    //uid_list
}

message GroupMemberEvent {
  enum ChangeType {
    JOIN_GROUP = 1;
    QUIT_GROUP = 2;
    DISMISS_GROUP = 3;          //group_id
  }

  required uint32 change_type = 1;
  optional UserGroupChange user_group_change = 2;
  repeated uint32 dismissed_groups = 3;

  optional uint32 uid = 4;
}