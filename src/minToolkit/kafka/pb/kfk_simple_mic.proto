syntax="proto2";

package kafka_simple_mic;
option go_package = "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkasimplemic";

enum ESIMPLE_MIC_EVENT_TYPE
{
	ENUM_SIMPLE_MIC_EV_HOLD = 1;	    // 上麦
	ENUM_SIMPLE_MIC_EV_RELEASE = 2;	    // 下麦
	ENUM_SIMPLE_MIC_EV_CHANGE = 3;	    // 移动麦位
	ENUM_SIMPLE_MIC_EV_MODE_SET = 4;	// 模式设置
}

message SimpleMicEvent
{
	required uint32 ch_id		= 1;
	required uint32 event_type  = 2;   // ESIMPLE_MIC_EVENT_TYPE
	
	optional uint32 channel_type  = 3; // 房间类型(ChannelType),0无效值没有意义(没有填值) 1普通公会房 2直播房 3个人房 4公会公开房 6临时游戏房
	
	optional uint32 mic_target_mode = 4;
	optional uint32 mic_origin_mode = 5;
	optional uint32 mic_target_id = 6;
	optional uint32 mic_origin_id = 7;
	optional uint32 mic_user_id = 8;
	
	optional uint64 op_ts_ms = 9;  // 毫秒时间戳

	optional uint32 mic_user_sex = 10; // 不一定有值 ，需要先判断has
	optional uint64 last_hold_mic_ts= 11;  //最近一次上麦时间，下麦事件中才有

	optional uint32 op_uid = 12;
}

// 麦位信息
message MicrSpace
{
	required uint32 mic_id = 1;                       // 麦位ID 1 - 9
	optional uint32 mic_state = 2;                    // ga::EMicrSpaceState 麦位状态 1 正常 2 禁用 3 不可发言
	optional uint32 mic_uid = 3;                      // 麦上用户 如果麦上有人的话
}

// 麦位列表事件
message SimpleMicListEvent
{
	required uint32 chid = 1;
	optional uint32 ch_type = 2;

	repeated MicrSpace mic_list = 3;          // 麦位信息 不包含空麦位且状态正常的麦位

	optional uint32 mic_mode = 4;             // 麦模式
	optional uint64 server_time_ms = 5;       //  64bit 毫秒级 服务器时
}






