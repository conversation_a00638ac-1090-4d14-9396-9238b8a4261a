syntax="proto3";

import "bsoncxx/odm/descriptor.proto";

package example;

option (bsoncxx.odm.default_omit_empty) = false;

// enum is int32
enum Gender {
    UNKNOWN = 0;
    MALE = 1;
    FEMALE = 2;
}

message User {
    uint32 id = 1                               [ (bsoncxx.odm.field_name) = "_id", (bsoncxx.odm.omit_empty) = false]; 
    string name = 2                             [ (bsoncxx.odm.omit_empty) = false];
    Location location = 3                       [ (bsoncxx.odm.omit_empty) = false];
    repeated Location history_location_list = 4 [ (bsoncxx.odm.omit_empty) = false];
    map<string, Location> location_map = 5;
    Gender gender = 6;

    sint32 sint32_field = 7;
    sint64 sint64_field = 8;
    fixed32 fixed32_field = 9;
    fixed64 fixed64_field = 10;
    sfixed32 sfixed32_field = 11;
    sfixed64 sfixed64_field = 12;

    bytes binary_field = 13;

    repeated uint32 uint32_list = 14;
    map<uint32, uint32> uint32_map = 15;
    map<string, string> string_map = 16;
    map<string, bytes> bytes_map = 17;
    repeated string string_list = 18;
    repeated bytes binary_list = 19;
}

message Location {
    float latitude = 1;
    float longitude = 2;
}
