syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";  

// namespace
package adservice;

//设备类型
enum DeviceInfoType
{
    FROM_NONE=0;
    FROM_ANDROID=1;
    FROM_IOS=2;
}


message TriggerInfo
{
	uint32 timestamp = 1;        // 触发监测时间
	
    uint32 dev_type=2;           // 必填 设备类型 see DeviceInfoType
    string device_info = 3;      // 必填 设备信息  android imei deviceId or ios idfa deviceId
    
    string user_ip = 4;          // 可选 用户IP
    string mac = 5;          	 // 可选 eth0 mac地址的原值
    
    string callback_url= 6;      // 回调链接 
	string ad_source = 7;        // 广告 来源 比如 adbriage 今日头条 ....
	string ad_info = 8;          // 可选 广告 信息
    string tt_channel_id = 9;    // 可选 TT 渠道  
}

//广告 投放商 上报的数据
message AddAdTriggerReq
{
    TriggerInfo trigger_info = 1;
}

message AddAdTriggerResp
{
}

//匹配 adbright买量用户
//注意：匹配成功会删除对应数据
message MatchAdUserReq
{
    uint32 uid=1;                 // 用户ID
    TriggerInfo trigger_info = 2;
    
    bool  is_call = 3;           // 如果命中是否回调并清理数据
}

message MatchAdUserResp
{
	string ad_source = 1;        // 必填 广告 来源
	string ad_info = 2;          // 可选 广告 信息
}

//异步队列
enum ADSERVICE_ASYNC_TYPE
{
    ADSERVICE_ASYNC_NONE = 0;
	ADSERVICE_ASYNC_CALLBACK = 1;
}
message CallbackNotify
{
    uint32 uid=1;
    string callback_url =2;
    string device_info =3;   // android imei deviceId or ios idfa deviceId
	uint32 dev_type =4;      // 设备类型 see DeviceInfoType
	string ad_source = 5;    // 广告 来源
	string ad_info = 6;      // 广告 信息
}

service AdService {
    option( tlvpickle.Magic ) = 15615;      

    rpc AddAdTrigger( AddAdTriggerReq ) returns( AddAdTriggerResp )
    {
        option( tlvpickle.CmdID ) = 1;                                      
        option( tlvpickle.OptString ) = "e:d:i:l:t:h:x:";                         
        option( tlvpickle.Usage ) = "-e <type> -d <deviceId> -i <userIp> -l <callbackUrl> -t <triggerTime> -h <tt_channel> -x <ad channel>";
    }

    rpc MatchAdUser( MatchAdUserReq ) returns( MatchAdUserResp )
    {
        option( tlvpickle.CmdID ) = 2;                                      
        option( tlvpickle.OptString ) = "u:e:d:m:t:l:";                         
        option( tlvpickle.Usage ) = "-u <uid> -e <type> -d <deviceId> -m <deviceIdMd5> -l <isCall>";
    }
}
