syntax="proto2";

// svrkit support
import "common/tlvpickle/skbuiltintype.proto";

package TTGiftCenter;

enum ActivityType {
	GIFT_BANNER		= 1;
	GIFT_ACTIVITY	= 2;
}

enum SubType {
	TT_GIFT			= 1;
	GUILD_GIFT		= 2;
	ACTIVITY_GIFT	= 3;
	Y_GIFT			= 4;
	OPERATE_GIFT	= 5;//运营部门礼包
}

enum OrderByType {
	PRODUCT_ID			= 1;  //same as activity id , when in the activity table
	RANK				= 2;
	STOCK				= 3;
	REMAIN				= 4;
	NAME				= 5;
}

enum TTGiftPlatform {
	ALL_PLATFORM	= 255;
	ANDROID			= 1;
	IOS				= 2;
}


message ProductItem {
	required uint32 storage_id			= 1;
	required bytes item_binary			= 2;
	optional uint32 uid					= 3; // uid 或者公会ID
}

message CreateProductReq {
	required string name 			= 1;	// 商品名称
	required string description 	= 2;	// 商品描述
	required string usage_desc		= 3;	// 使用方法
	required uint32 game_id 		= 4;	// 绑定的游戏ID
	required uint32 item_type 		= 5;	// item类型, see EGiftPktExchangeType  from /proto/pbfile/giftpkg2_.proto
	required uint32 gift_type 		= 6;	// gift类型, see EGiftPktItemType from /proto/pbfile/giftpkg2_.proto
	required uint32 exchange_s_date	= 7;	// 开卖时间
	required uint32 exchange_e_date	= 8;	// 停卖时间
	required string operator		= 9;
	required uint32 platform		= 10;	// see TTGiftPlatform
}

message CreateProductResp {
	required uint64 product_id = 1;
}

message ModifyProductReq {
	required uint64 product_id		= 1;
	required string name 			= 2;	// 商品名称
	required string description 	= 3;	// 商品描述
	required string usage_desc		= 4;	// 使用方法
	required uint32 gift_type 		= 5;	// gift类型, see EGiftPktItemType from /proto/pbfile/giftpkg2_.proto
	required uint32 exchange_s_date	= 6;	// 开卖时间
	required uint32 exchange_e_date	= 7;	// 停卖时间
	required string operator		= 8;
	required uint32 platform		= 9;	// see TTGiftPlatform
}


// 商品
message Product {
	required uint64 product_id 		= 1;	// 商品ID
	required string name 			= 2;	// 商品名称
	required string description 	= 3;	// 商品描述
	required string usage_desc		= 4;	// 使用方法
	required uint32 game_id 		= 5;	// 绑定的游戏ID
	required uint32 item_type 		= 6;	// item类型 兑换类型, see EGiftPktExchangeType  from /proto/pbfile/giftpkg2_.proto
	required uint32 gift_type 		= 7;	// gift类型 商品类型, see EGiftPktItemType from /proto/pbfile/giftpkg2_.proto
	required uint32 exchange_s_date	= 8;	// 开卖时间
	required uint32 exchange_e_date	= 9;	// 停卖时间
	required uint32 left			= 10;	// 剩余数量
	required uint32 stock			= 11;	// 总库存
	required uint32 create_time		= 12;	// 创建时间
	required uint32 platform		= 13;	// see TTGiftPlatform
}


message SearchProductReq {
	optional uint64 product_id			= 1;
	optional uint32 game_id				= 2;
	optional uint32 gift_type			= 3; // gift类型 商品类型, see EGiftPktItemType from /proto/pbfile/giftpkg2_.proto
	optional string name				= 4;
	optional uint32 create_after_time	= 5;
	optional uint32 create_before_time	= 6;
	optional uint32 order_by_type		= 7;  // see OrderByType
	optional bool desc					= 8;  // 降序排列
	required uint32 limit_start_idx	    = 9;  // mysql limit限制的起始idx
	required uint32 limit_count	        = 10; // mysql limit限制的数量
	required uint32 platform			= 11;	// see TTGiftPlatform
}

message SearchProductResp {
	repeated Product product_list		= 1;
	required uint32 total				= 2;
}

// 根据product id批量获取主库商品信息
message BatchGetProductReq
{
	repeated uint64 productid_list		= 1;
}
message BatchGetProductResp
{
	repeated Product product_list		= 1;
}

// 将主库商品的物品分配到子库
message DispatchItemReq {
	required uint64 product_id			= 1;
	required uint32 sub_type			= 2;	// see SubType
	required int32 amount				= 3;
	required string operator			= 4;
}

message AccountPwdItem {
	required string account				= 1;
	required string pwd					= 2;
}

message ExchangeCardItem {
	required string code				= 1;
}


message AddItemReq{
	required uint64 product_id		= 1;
	repeated bytes item_binary_list	= 2; //ExchangeCardItem or AccountPwdItem
	required string operator		= 3;
	repeated string item_hash_list	= 4;  // item_binary_list 对应的hash值
}

message AddItemResp {
	repeated bytes success_items	= 1;
	repeated bytes failed_items	= 2;
}

//-----------------------------------------//
//sub gift include (1:tt gift, guild gift, activity gift, y package gift)
//-----------------------------------------//

// TT子库商品的条件位
enum ESubTTGiftPurchaseCond {
	SUB_TT_PURCHASE_COND_SDK_ALLOW	= 1;  // 允许SDK领取
}


message UpdateSubTTGiftReq {
	required uint64 product_id			= 1;
	required uint32 price				= 2;
	required uint32 status				= 3;
	required uint32 level_min			= 4;
	required uint32 level_max			= 5;
	required uint32 purchase_limit		= 6;
	required uint64 purchase_condition	= 7; // TT子库商品的条件位 bitmap see ESubTTGiftPurchaseCond
	required uint32 day_limit			= 8;
	required uint32 hour_limit			= 9;
	required uint32 buy_s_time			= 10;
	required uint32 buy_e_time			= 11;
	required uint32 pick_able			= 12;
	required string operator			= 13;
	optional uint32 pick_s_time			= 14;
	optional uint32 pick_e_time			= 15;
	optional uint32 register_in			= 16;
}

message UpdateSubGuildGiftReq {
	required uint64 product_id			= 1;
	required uint32 price				= 2;
	required uint32 status				= 3;
	required uint32 level_min			= 4;
	required uint32 level_max			= 5;
	required uint32 purchase_limit		= 6; // TT公会子库商品的单公会购买数量限制
	required uint32 buy_s_time			= 7;
	required uint32 buy_e_time			= 8;
	required string operator			= 9;
}

message AddSubYorActGiftReq{
	required uint64 product_id			= 1;
	required uint32 status				= 2;
	required string operator			= 3;
	required uint32 sub_type			= 4; // see SubType
}

message GetSubGiftReq {
	optional uint64 product_id			= 1;
	optional uint32 game_id				= 2;
	optional string name				= 3;
	optional uint64 create_after_time	= 4;
	optional uint64 create_before_time	= 5;
	optional uint32 status				= 6;
	optional uint32 order_by_type		= 7;  // see OrderByType
	optional bool desc					= 8;  // 是否降序排列
	required uint32 limit_start			= 9;  // mysql limit限制的起始idx
	required uint32 limit_end			= 10; // mysql limit限制的总数
	optional uint32 gift_type			= 11; // gift类型 商品类型, see EGiftPktItemType from /proto/pbfile/giftpkg2_.proto
	optional bool is_need_expire		= 12; // 是否需要过期数据 默认是不需要的 过滤过期数据
	required uint32 platform			= 13;	// see TTGiftPlatform
	
}

message SubTTGift {
	required uint64 product_id			= 1;
	required uint32 price				= 2;
	required uint32 status				= 3;
	required uint32 rank				= 4;
	required uint32 level_min			= 5;
	required uint32 level_max			= 6;
	required uint32 purchase_limit		= 7; // TT子库商品的单用户购买数量限制
	required uint64 purchase_condition	= 8; // TT子库商品的条件位 bitmap see ESubTTGiftPurchaseCond
	required uint32 day_limit			= 9;
	required uint32 hour_limit			= 10;
	required uint32 buy_s_time			= 11;
	required uint32 buy_e_time			= 12;
	required uint32 pick_able			= 13;
	required uint32 pick_times			= 14;
	required uint32 left				= 15;
	required uint32 stock				= 16;
	required uint32 update_time			= 17;
	required Product product			= 18;
	optional uint32 pick_s_time			= 19;
	optional uint32 pick_e_time			= 20;
	optional uint32 unclaimed_count		= 21;
	optional uint32 register_in			= 22;
}


message GetSubTTGiftResp {
	repeated SubTTGift tt_gift_list		= 1;
	required uint32 total				= 2;
}


message SubGuildGift {
	required uint64 product_id			= 1;
	required uint32 price				= 2;
	required uint32 status				= 3;
	required uint32 rank				= 4;
	required uint32 level_min			= 5;
	required uint32 level_max			= 6;
	required uint32 purchase_limit		= 7; // TT公会子库商品的单公会购买数量限制
	required uint32 buy_s_time			= 8;
	required uint32 buy_e_time			= 9;
	required uint32 left				= 10;
	required uint32 stock				= 11;
	required uint32 update_time			= 12;
	required Product product			= 13;
	optional uint32 unclaimed_count		= 14;
}

message GetSubGuildGiftResp {
	repeated SubGuildGift guild_gift_list	= 1;
	required uint32 total					= 2;
}


message ActivitGift {
	required uint64 product_id			= 1;
	required uint32 left				= 2;
	required uint32 stock				= 3;
	required uint32 update_time			= 4;
	required Product product			= 5;
	optional uint32 unclaimed_count		= 6;
}

message GetSubActivityGiftResp {
	repeated ActivitGift activity_gift_list = 1;
	required uint32 total					= 2;
}


message YGift {
	required uint64 product_id			= 1;
	required uint32 left				= 2;
	required uint32 stock				= 3;
	required uint32 update_time			= 4;
	required Product product			= 5;
}

message GetSubYGiftResp {
	repeated YGift y_gift_list			= 1;
	required uint32 total				= 2;
}

message OperateGift {
	required uint64 product_id			= 1;
	required uint32 left				= 2;
	required uint32 stock				= 3;
	required uint32 update_time			= 4;
	required Product product			= 5;
}


message GetSubOperateGiftResp {
	repeated OperateGift operate_gift_list	= 1;
	required uint32 total				= 2;
}


message ModifyGiftRankReq {
	required uint32 sub_type			= 1;
	repeated uint64 product_ids			= 2;
	optional uint32 gift_type 		    = 3;	// gift类型, see EGiftPktItemType from /proto/pbfile/giftpkg2_.proto
}

message DispatchSubTTGiftReq {
	required uint64 product_id			= 1;
	repeated uint32 uid_list			= 2;
	required string operator			= 3;
}

//only for tt or guild subgift
message DispatchSubGiftResp {
	required Product product			= 1;
	repeated ProductItem item_list		= 2;

}

message DispatchSubGuildGiftReq {
	required uint64 product_id			= 1;
	required uint32 guild_id			= 2;
	required uint32 count				= 3;
	required string operator			= 4;
}


message GetGameGiftAmountReq {
	repeated uint32 game_id_list		= 1;
	required uint32 sub_type			= 2;
	required uint32 platform			= 3;
}

message GameGiftAmount {
	required uint32 game_id				= 1;
	required uint32 gift_amount			= 2;
}

message GetGameGiftAmountResp {
	repeated GameGiftAmount amount_list	= 1;
}


message GetTTGiftPurchaseAuthReq {
	required uint32 uid					= 1;
	repeated uint64 product_id_list		= 2;
}

message TTGiftPurchaseAuth {
	required uint64 product_id			= 1;
	required bool auth					= 2;
	optional uint32 already_purchase	= 3;
}

message GetTTGiftPurchaseAuthResp {
	repeated TTGiftPurchaseAuth purchase_auth_list = 1;
}

message GetGuildGiftPurchaseAuthReq {
	required uint32 guild_id			= 1;
	required uint64 product_id			= 2;
	required uint32 guild_level			= 3;
	required uint32 buy_number			= 4;
}


message GetGuildGiftPurchaseAuthResp {
	required int32 buy_limit			= 1;
	required bool level_auth			= 2;
}

// 导出 指定子库的礼物商品的指定数量的物品 并将该物品设置为已经发放
message ExportSubGiftReq {
	required uint64 product_id			= 1; // 导出的目标商品ID
	required uint32 amount				= 2; // 导出的总数量
	required uint32 sub_type			= 3; // 导出的子库类型 see SubType
	optional uint32 export_to_type       = 4; // 导出的目的 0: 没有明确目的 1: 给个人 2:给公会
	repeated uint32 export_to_id_list    = 5; // 当export_to_type不为0时 传入需要发放的对象ID 可以是UID或者guildID
	optional uint32 export_to_id_per_cnt = 6; // 当export_to_type不为0时 每个uid或者guildID发几个物品 export_to_id_list_size * export_to_id_per_cnt 不能超过amount
}

message ExportSubGiftResp {
	repeated ProductItem item_list		= 1;
}


message ClaimGiftReq{
	required uint32 id = 1;
}

message ClaimGiftResp{
	repeated DispatchSubGiftResp gift_list = 1;
}

message GetGiftDispatchRecordReq {
	required uint64 product_id = 1;
}


message TTGiftDispatchInfo {
	required uint32 uid = 1;
	required uint32 dispatch_time = 2;
	required uint32 dispatch_count = 3;
	required uint32 status = 4;
	required uint32 claim_time = 5;
}


message GetTTGiftDispatchRecordResp {
	repeated TTGiftDispatchInfo dispatch_list = 1;
}

message GuildGiftDispatchInfo {
	required uint32 guild_id = 1;
	required uint32 dispatch_time = 2;
	required uint32 dispatch_count = 3;
}


message GetGuildGiftDispatchRecordResp {
	repeated GuildGiftDispatchInfo dispatch_list = 1;
}

message RecycleGiftReq{
	required uint64 product_id= 1;
}


//----------------------------------------------//
//gift avtivity part
//----------------------------------------------//

message AddGiftActivityReq {
	required string title				= 1;
	required uint32	activity_type		= 2;
	required string pic_url				= 3;
	required string jump_url			= 4;
	required string icon_url			= 5;
	required uint32 game_id				= 6;
	required uint32 start_time			= 7;
	required uint32 end_time			= 8;
	required string operator			= 9;
}

message AddGiftActivityResp {
	required uint32 activity_id			= 1;
}


message DelGiftActivityReq {
	required uint32 activity_id			= 1;
	required string operator			= 2;
}

message ModifyGiftActivityRankReq {
	repeated uint32 activity_id_list	= 1;
}

message GiftActivity {
	required uint32 activity_id			= 1;
	required string title				= 2;
	required uint32	activity_type		= 3;
	required string pic_url				= 4;
	required string icon_url			= 5;
	required string jump_url			= 6;
	required uint32 game_id				= 7;
	required uint32 start_time			= 8;
	required uint32 end_time			= 9;
	required uint32 rank				= 10;
	required uint32 create_time			= 11;
}

message GetGiftActivityReq	{
	optional uint32 activity_id			= 1;
	optional uint32 game_id				= 2;
	optional uint64 create_after_time	= 3;
	optional uint64 create_before_time	= 4;
	optional uint32 activity_type		= 5;
	required uint32 order_by_type		= 6; //see OrderByType
	required bool desc					= 7;
	required uint32 limit_start			= 8;
	required uint32 limit_end			= 9;
}

message GetGiftActivityResp {
	repeated GiftActivity gift_activity_list = 1;
	required uint32 total = 2;
}

message ActivityBanner {
	required string pic_url				= 1;
	required string jump_url			= 2;
	required uint32 activity_id			= 3;
}

message GetActivityBannerResp {
	repeated ActivityBanner banner_list	= 1;
}

//----------------------------------------------//
//purchase and pick
//----------------------------------------------//



message PurchaseTTGiftReq {
	required uint64 product_id			= 1;
	required uint32 uid					= 2;
}

message PurchaseTTGiftResp {
	required SubTTGift	gift_info		= 1;
	required ProductItem item_info		= 2;
}

message PurchaseGuildGiftReq {
	required uint64 product_id			= 1;
	required uint32 guild_id			= 2;
	required uint32 count				= 3;
}

message PurchaseGuildGiftResp {
	required SubGuildGift gift_info		= 1;
	repeated ProductItem item_list		= 2;
}

message PickTTGiftReq {
	required uint64 product_id			= 1;
}


message GetTTGiftDayLimitReq {
	required uint64 product_id			= 1;
}

message GetTTGiftDayLimitResp {
	required uint32 used				= 1;
	required uint32 total_limit			= 2;
}

service TTGiftCenter {
	/**
	 * Server listening port
	 */
	option( tlvpickle.Magic ) = 15490;

	// 0 - 99, product related

	//----------------------------------------------//
	//major gift part
	//----------------------------------------------//
	// 创建主库礼物商品
	rpc CreateProduct( CreateProductReq ) returns ( CreateProductResp ) {
		option( tlvpickle.CmdID ) = 1;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 修改主库的礼物商品
	rpc ModifyProduct( ModifyProductReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 2;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 获取主库的礼物商品列表
	rpc SearchProduct( SearchProductReq ) returns ( SearchProductResp ) {
		option( tlvpickle.CmdID ) = 3;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 将主库的指定礼物商品对应的物品分配给子库(在此之前需要在子库建立该商品的信息)
	rpc DispatchItem( DispatchItemReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 4;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 为主库的指定礼物商品增加物品库存
	rpc AddItem( AddItemReq ) returns ( AddItemResp ) {
		option( tlvpickle.CmdID ) = 5;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}



	//----------------------------------------------//
	//sub gift part
	//----------------------------------------------//
	
	// 建立或更新 TT个人子库的商品信息
	rpc UpdateSubTTGift ( UpdateSubTTGiftReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 51;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 建立或更新 TT公会子库的商品信息
	rpc UpdateSubGuildGift ( UpdateSubGuildGiftReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 52;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 获取 TT个人子库的商品信息列表
	rpc GetSubTTGift ( GetSubGiftReq ) returns ( GetSubTTGiftResp ) {
		option( tlvpickle.CmdID ) = 53;
		option( tlvpickle.OptString ) = "p:";
		option( tlvpickle.Usage ) = "-p<product_id>";
	}
	
	// 获取 TT公会子库的商品信息列表
	rpc GetSubGuildGift ( GetSubGiftReq ) returns ( GetSubGuildGiftResp ) {
		option( tlvpickle.CmdID ) = 54;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 获取 活动子库的商品信息列表
	rpc GetSubActivityGift ( GetSubGiftReq ) returns ( GetSubActivityGiftResp ) {
		option( tlvpickle.CmdID ) = 55;
		option( tlvpickle.OptString ) = "p:";
		option( tlvpickle.Usage ) = "-p<product_id>";
	}
	
	// 获取 Y包子库的商品信息列表
	rpc GetSubYGift ( GetSubGiftReq ) returns ( GetSubYGiftResp ) {
		option( tlvpickle.CmdID ) = 56;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 修改子库商品的排序值
	rpc ModifyGiftRank ( ModifyGiftRankReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 57;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 发放 TT个人子库的商品对应的物品给个人(记录分配信息 返回分配结果)
	rpc DispatchSubTTGift ( DispatchSubTTGiftReq ) returns ( DispatchSubGiftResp ) {
		option( tlvpickle.CmdID ) = 58;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 发放 TT公会子库的商品对应的物品给公会(记录分配信息 返回分配结果)
	rpc DispatchSubGuildGift ( DispatchSubGuildGiftReq ) returns ( DispatchSubGiftResp ) {
		option( tlvpickle.CmdID ) = 59;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc GetGameGiftAmount ( GetGameGiftAmountReq ) returns ( GetGameGiftAmountResp ) {
		option( tlvpickle.CmdID ) = 60;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc GetTTGiftPurchaseAuth ( GetTTGiftPurchaseAuthReq ) returns ( GetTTGiftPurchaseAuthResp ) {
		option( tlvpickle.CmdID ) = 61;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 导出 指定子库的礼物商品的指定数量的物品 并将该物品设置为已经发放
	rpc ExportSubGift ( ExportSubGiftReq ) returns ( ExportSubGiftResp ) {
		option( tlvpickle.CmdID ) = 62;
		option( tlvpickle.OptString ) = "p:n:t:";
		option( tlvpickle.Usage ) = "-p <product_id> -n <ammount> -t <subtype>";
	}

	rpc GetGuildGiftPurchaseAuth ( GetGuildGiftPurchaseAuthReq ) returns ( GetGuildGiftPurchaseAuthResp ) {
		option( tlvpickle.CmdID ) = 63;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}


	rpc ClaimTTGift ( ClaimGiftReq ) returns ( ClaimGiftResp ) {
		option( tlvpickle.CmdID ) = 64;
		option( tlvpickle.OptString ) = "u:";
		option( tlvpickle.Usage ) = "-u<uid>";
	}
	
	rpc ClaimGuildGift ( ClaimGiftReq ) returns ( ClaimGiftResp ) {
		option( tlvpickle.CmdID ) = 65;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc GetTTGiftDispatchRecord ( GetGiftDispatchRecordReq ) returns ( GetTTGiftDispatchRecordResp ) {
		option( tlvpickle.CmdID ) = 66;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc GetGuildGiftDispatchRecord ( GetGiftDispatchRecordReq ) returns ( GetGuildGiftDispatchRecordResp ) {
		option( tlvpickle.CmdID ) = 67;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc RecycleGift ( RecycleGiftReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 68;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

		// 发放 TT个人子库的商品对应的物品给个人(记录分配信息 返回分配结果)
	rpc DispatchSubTTGiftForActivity ( DispatchSubTTGiftReq ) returns ( DispatchSubGiftResp ) {
		option( tlvpickle.CmdID ) = 69;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 发放 TT公会子库的商品对应的物品给公会(记录分配信息 返回分配结果)
	rpc DispatchSubGuildGiftForActivity ( DispatchSubGuildGiftReq ) returns ( DispatchSubGiftResp ) {
		option( tlvpickle.CmdID ) = 70;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}


	// 获取 运营子库的商品信息列表
	rpc GetSubOperateGift ( GetSubGiftReq ) returns ( GetSubOperateGiftResp ) {
		option( tlvpickle.CmdID ) = 71;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	//----------------------------------------------//
	//gift avtivity part 礼包活动相关设置
	//----------------------------------------------//
	rpc AddGiftActivity ( AddGiftActivityReq ) returns ( AddGiftActivityResp ) {
		option( tlvpickle.CmdID ) = 100;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	rpc DelGiftActivity ( DelGiftActivityReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 101;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc ModifyGiftActivityRank ( ModifyGiftActivityRankReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 102;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc GetGiftActivity ( GetGiftActivityReq ) returns ( GetGiftActivityResp ) {
		option( tlvpickle.CmdID ) = 103;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc GetActivityBanner ( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetActivityBannerResp ) {
		option( tlvpickle.CmdID ) = 104;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	//----------------------------------------------//
	//purchase and pick
	//----------------------------------------------//
	// 购买TT子库的礼包
	rpc PurchaseTTGift ( PurchaseTTGiftReq ) returns ( PurchaseTTGiftResp ) {
		option( tlvpickle.CmdID ) = 120;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	// 购买TT公会子库的礼包
	rpc PurchaseGuildGift ( PurchaseGuildGiftReq ) returns ( PurchaseGuildGiftResp ) {
		option( tlvpickle.CmdID ) = 121;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 淘号
	rpc PickTTGift ( PickTTGiftReq ) returns ( PurchaseTTGiftResp ) {
		option( tlvpickle.CmdID ) = 122;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 建立 Y包子库 或者 活动子库 的礼物商品信息
	rpc AddSubYorActGiftInfo ( AddSubYorActGiftReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 123;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
	
	// 根据product id批量获取主库商品信息
	rpc BatchGetProduct ( BatchGetProductReq ) returns ( BatchGetProductResp ){
		option( tlvpickle.CmdID ) = 124;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

		// 购买TT子库的礼包
	rpc SDKApplyTTGift ( PurchaseTTGiftReq ) returns ( PurchaseTTGiftResp ) {
		option( tlvpickle.CmdID ) = 125;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

			// 购买TT子库的礼包
	rpc GetTTGiftDayLimit ( GetTTGiftDayLimitReq ) returns ( GetTTGiftDayLimitResp ) {
		option( tlvpickle.CmdID ) = 130;
		option( tlvpickle.OptString ) = "p:";
		option( tlvpickle.Usage ) = "-p<product_id>";
	}
}