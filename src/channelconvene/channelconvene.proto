syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package channelconvene;

// 收藏频道
message CollectChannelReq
{
	required uint32 channel_id = 1;
}

// 取消收藏频道
message RemoveChannelCollectionReq
{
	repeated uint32 channel_id_list = 1;
}

// 是否已收藏频道
message HasCollectChannelReq
{
	required uint32 channel_id = 1;
}

message HasCollectChannelResp
{
	required bool is_collected = 1;
}

// 从指定的频道列表中获取收藏的频道
message BatchHasCollectChannelReq
{
	repeated uint32 channel_id_list = 1;
}

message BatchHasCollectChannelResp
{
	repeated uint32 channel_id_list = 1;	// 已收藏的房间列表
}

// 获取用户的收藏频道列表
message GetChannelCollectionListByUidReq
{
}

message GetChannelCollectionListByUidResp
{
	repeated uint32 channel_id_list = 1;
}

message StChannelCollectMember
{
	required uint32 uid = 1;
	required uint32 collect_ts = 2;
}

message StChannelConveneMember
{
	required uint32 uid = 1;
	required uint32 channel_id = 2;
	optional uint32 convene_status = 3;	// see ga::MemberConfirmStatus
}

message StChannelConveneInfo
{
	required uint32 channel_id = 1;
	optional uint32 last_convene_ts = 2;	// 上次召集时间
	optional uint32 last_cancel_ts = 3;		// 上次取消召集时间
	optional uint32 confirm_count = 4;		// 响应召集人数(上次下发的人数)
	optional uint32 confirm_count_real = 5;	// 响应召集人数(真实人数)
	optional bytes convene_msg = 6;		// 频道召集描述
	optional uint32 valid_convene_ts = 7;	// 下次可召集时间
	optional uint32 convene_duration_min = 8;	// 召集有效时间(分钟)
}

// 获取收藏频道的用户列表
message GetChannelCollectionMemberListReq
{
	required uint32 channel_id = 1;
	optional uint32 offset = 2;
	optional uint32 limit = 3;
}

message GetChannelCollectionMemberListResp
{
	repeated StChannelCollectMember member_list = 1;
}

// 获取收藏频道的用户数
message GetChannelCollectionMemberCountReq
{
	required uint32 channel_id = 1;
}

message GetChannelCollectionMemberCountResp
{
	required uint32 member_count = 1;
}

// 获取频道的召集信息
message GetChannelConveneInfoReq
{
	required uint32 channel_id = 1;
}

message GetChannelConveneInfoResp
{
	required StChannelConveneInfo convene_info = 1;
}

// 召集收藏频道的用户
message ConveneChannelReq
{
	required uint32 channel_id = 1;
	required string convene_msg = 2;
	repeated uint32 channel_online_members = 3;
}

message ConveneChannelResp
{
	required StChannelConveneInfo convene_info = 1;
}

// 召集收藏频道的用户(异步队列的专用接口)
message ConveneChannelInTurnReq
{
	required uint32 channel_id = 1;
	required uint32 offset = 2;
	optional uint32 version = 3;
}

// 取消召集
message DisableConveneChannelReq
{
	required uint32 channel_id = 1;
}

// 取消召集(异步队列的专用接口)
message DisableConveneChannelInTurnReq
{
	required uint32 channel_id = 1;
	required uint32 offset = 2;
	required uint32 status = 3;
}

// 用户响应频道召集
message ConfirmChannelConveneReq
{
}

message ConfirmChannelConveneResp
{
	repeated StChannelConveneInfo channel_list = 1;
}

// 通知创建频道
message InformCreateChannelReq
{
	required uint32 channel_id = 1;
}

// 通知解散频道
message InformDismissChannelReq
{
	required uint32 channel_id = 1;
}

// 解散频道(异步队列的专用接口)
message DismissChannelInTurnReq
{
	required uint32 channel_id = 1;
	required uint32 offset = 2;
}

// 通知用户已进入频道
message InformUserEnterChannelReq
{
	required uint32 channel_id = 1;
}

// 通知用户已退出频道
message InformUserExitChannelReq
{
	required uint32 channel_id = 1;
}

// 更新用户的召集状态
message UpdateUserConveneStatusReq
{
	required uint32 channel_id = 1;
	required uint32 status = 2;	// see ga::MemberConfirmStatus
}

// 获取响应的召集成员列表
message GetConveneMemberListReq
{
	required uint32 channel_id = 1;
} 

message GetConveneMemberListResp
{
	repeated StChannelConveneMember member_list = 1;
}

// 获取用户的频道召集响应信息
message GetUserConveneInfoReq
{
	required uint32 channel_id = 1;
}

message GetUserConveneInfoResp
{
	optional StChannelConveneMember member_info = 1;
}

// 获取指定用户响应状态的用户数量
message GetConfirmCountByStatusReq
{
	required uint32 channel_id = 1;
	required uint32 status = 2;	// see ga::MemberConfirmStatus
}

message GetConfirmCountByStatusResp
{
	optional uint32 member_count = 1;
}

/********* asyncjob notify *********/
message ChannelConveneAsyncJobConveneChangeNotify
{
	required uint32 opt_uid = 1;
	required StChannelConveneInfo convene_info = 2;
}

message ChannelConveneAsyncJobConveneInTurnNotify
{
	required uint32 opt_uid = 1;
	required uint32 channel_id = 2;
	required uint32 convene_ts = 3;
	required uint32 offset = 4;
	repeated uint32 notify_uid_list = 5;
	optional uint32 version = 6;
}

message ChannelConveneAsyncJobDisableConveneInTurnNotify
{
	required uint32 opt_uid = 1;
	required uint32 channel_id = 2;
	required uint32 status = 3;
	required uint32 offset = 4;
	repeated uint32 notify_uid_list = 5;
}

message ChannelConveneAsyncJobConfirmConveneNotify
{
	required uint32 uid = 1;
	repeated StChannelConveneInfo channel_list = 2;
}

message ChannelConveneAsyncJobDismissChannelNotify
{
	required uint32 opt_uid = 1;
	required uint32 channel_id = 2;
	required uint32 offset = 3;
}

//service
service ChannelConvene {
	option( tlvpickle.Magic ) = 15560;		// 服务监听端口号
	
	rpc CollectChannel( CollectChannelReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 1;										
        option( tlvpickle.OptString ) = "u:h:";							
        option( tlvpickle.Usage ) = "-u <uid> -h <channel_id>";
	}
	
	rpc RemoveChannelCollection( RemoveChannelCollectionReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 2;										
        option( tlvpickle.OptString ) = "u:h:";							
        option( tlvpickle.Usage ) = "-u <uid> -h <channel_id>";
	}
	
	rpc GetChannelCollectionListByUid( GetChannelCollectionListByUidReq ) returns( GetChannelCollectionListByUidResp ){
		option( tlvpickle.CmdID ) = 3;										
        option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid>";
	}
	
	rpc GetChannelCollectionMemberList( GetChannelCollectionMemberListReq ) returns( GetChannelCollectionMemberListResp ){
		option( tlvpickle.CmdID ) = 4;										
        option( tlvpickle.OptString ) = "h:o:n:";							
        option( tlvpickle.Usage ) = "-h <channel_id> [-o <offset> -n <limit>]";
	}
	
	rpc GetChannelConveneInfo( GetChannelConveneInfoReq ) returns( GetChannelConveneInfoResp ){
		option( tlvpickle.CmdID ) = 5;										
        option( tlvpickle.OptString ) = "h:";							
        option( tlvpickle.Usage ) = "-h <channel_id>";
	}
	
	rpc ConveneChannel( ConveneChannelReq ) returns( ConveneChannelResp ){
		option( tlvpickle.CmdID ) = 6;										
        option( tlvpickle.OptString ) = "h:m:";							
        option( tlvpickle.Usage ) = "-h <channel_id> -m <convene_msg>";
	}
	
	rpc ConveneChannelInTurn( ConveneChannelInTurnReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 7;										
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	}
	
	rpc DisableConveneChannel( DisableConveneChannelReq ) returns( GetChannelConveneInfoResp ){
		option( tlvpickle.CmdID ) = 8;										
        option( tlvpickle.OptString ) = "h:";							
        option( tlvpickle.Usage ) = "-h <channel_id>";
	}
	
	rpc DisableConveneChannelInTurn( DisableConveneChannelInTurnReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 9;										
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	}
	
	rpc InformCreateChannel( InformCreateChannelReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 10;										
        option( tlvpickle.OptString ) = "h:";							
        option( tlvpickle.Usage ) = "-h <channel_id>";
	}
	
	rpc InformDismissChannel( InformDismissChannelReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 11;										
        option( tlvpickle.OptString ) = "h:";							
        option( tlvpickle.Usage ) = "-h <channel_id>";
	}
	
	rpc ConfirmChannelConvene( ConfirmChannelConveneReq ) returns( ConfirmChannelConveneResp ){
		option( tlvpickle.CmdID ) = 12;										
        option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid>";
	}
	
	rpc InformUserEnterChannel( InformUserEnterChannelReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 13;										
        option( tlvpickle.OptString ) = "u:h:";							
        option( tlvpickle.Usage ) = "-u <uid> -h <channel_id>";
	}
	
	rpc InformUserExitChannel( InformUserExitChannelReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 14;										
        option( tlvpickle.OptString ) = "u:h:";							
        option( tlvpickle.Usage ) = "-u <uid> -h <channel_id>";
	}
	
	rpc UpdateUserConveneStatus( UpdateUserConveneStatusReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 15;										
        option( tlvpickle.OptString ) = "u:h:t:";							
        option( tlvpickle.Usage ) = "-u <uid> -h <channel_id> -t <convene_status>";
	}
	
	rpc HasCollectChannel( HasCollectChannelReq ) returns( HasCollectChannelResp ){
		option( tlvpickle.CmdID ) = 16;										
        option( tlvpickle.OptString ) = "u:h:";							
        option( tlvpickle.Usage ) = "-u <uid> -h <channel_id>";
	}
	
	rpc GetConveneMemberList( GetConveneMemberListReq ) returns( GetConveneMemberListResp ){
		option( tlvpickle.CmdID ) = 17;										
        option( tlvpickle.OptString ) = "h:";							
        option( tlvpickle.Usage ) = "-h <channel_id>";
	}
	
	rpc GetUserConveneInfo( GetUserConveneInfoReq ) returns( GetUserConveneInfoResp ){
		option( tlvpickle.CmdID ) = 18;										
        option( tlvpickle.OptString ) = "u:h:";							
        option( tlvpickle.Usage ) = "-u <uid> -h <channel_id>";
	}
	
	rpc GetConfirmCountByStatus( GetConfirmCountByStatusReq ) returns( GetConfirmCountByStatusResp ){
		option( tlvpickle.CmdID ) = 19;										
        option( tlvpickle.OptString ) = "h:t:";							
        option( tlvpickle.Usage ) = "-h <channel_id> -t <convene_status>";
	}
	
	rpc DismissChannelInTurn( DismissChannelInTurnReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 20;										
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	}
	
	rpc GetChannelCollectionMemberCount( GetChannelCollectionMemberCountReq ) returns( GetChannelCollectionMemberCountResp ){
		option( tlvpickle.CmdID ) = 21;										
        option( tlvpickle.OptString ) = "h:";							
        option( tlvpickle.Usage ) = "-h <channel_id>";
	}
	
	rpc BatchHasCollectChannel( BatchHasCollectChannelReq ) returns( BatchHasCollectChannelResp ){
		option( tlvpickle.CmdID ) = 22;										
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	}
}

