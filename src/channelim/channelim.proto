syntax="proto2";

// 必须import
import "tlvpickle/skbuiltintype.proto";	

// namespace
package channelim;


enum ChannelMsgType {
    CHANNEL_COMMON_MSG = 1;
}

message ChannelMsg
{
	required uint32 seq_id = 1;
	required uint32 type = 2;
	required bytes msg_bin = 3;
}

enum MSG_SUB_TYPE {
   ENUM_ROOM_TRUMPET_MSG = 1;          //房间小喇叭
}   

message ChannelCommonMsg
{
    required uint32 from_uid        = 1;
    required string from_account    = 2;
    required string from_nick       = 3;
    required uint64 time            = 4;
    required uint32 to_channel_id   = 5;
    required uint32 type            = 6;   // see ga::ChannelMsgType
    required string content         = 7;   // 消息的明文UTF8内容部分(不包括附件内容)
    required uint32 origin			= 8;
    optional uint32 target_uid		= 9;
    optional string target_account	= 10;
    optional string target_nick		= 11;
    optional bytes att_content		= 12;   // 附件内容 二进制流 (ps 由于IOS旧版本的实现问题 他们将该字段强行解析为ChannelImageMsgContent 那么该字段以后只能放 ChannelImageMsgContent 结构体)
    optional bytes opt_content		= 13;   // 附加内容 （新增 务必注意该字段 可能存放任何类型的消息 务必根据type进行判断）
    optional bytes pb_opt_content	= 14;   // 附加内容 （新增 妈蛋 因为 原先 opt_content 里面放的多是json json传输数据太大了 为了兼容又不能换 后续改用pb, 不同type 会有不同pb协议 定义在channel_opt_.proto）
    optional int32 from_sex          = 15;  // from_uid 对应的用户的性别
    optional int32 target_sex        = 16;  // target_uid 对应的用户的性别
    optional int32 sub_type          = 17;  // 小喇叭消息类型 enum MSG_SUB_TYPE
    optional uint32 sub_channel_id   = 18;  // 即包厢之类的子频道 
    optional UserProfile from_profile   = 19;  // from_uid 对应的用户信息
    optional UserProfile target_profile = 20;  // target_uid 对应的用户信息
	  optional string from_head_img_md5 = 21;  //from_uid对应的头像key
	  optional string target_head_img_md5 = 22;  //from_uid对应的头像key
}

message UserProfile
{
    optional UserPrivilege privilege = 1;  // 用户权益信息
}

message UserPrivilege
{
	required string account = 1;  // 权益指定的账号，如神秘人服务提供的：神秘人头像对应的account
    required string nickname = 2; // 权益指定的昵称，如神秘人服务提供的：神秘人+编号
	
	optional uint32 type = 3;     // 权益类型, 0: 无效，1： 神秘人
	optional bytes options = 4;   // 权益特殊属性，如 神秘人 对应 ga.UserUKWInfo
}

// 发送频道消息
message SendChannelMsgReq
{
	required uint32 channel_id = 1;
	required uint32 sender_uid = 2;
	required uint32 type = 3;             // channelim::ChannelMsgType。 注意该字段与 ga::ChannelMsgType 定义不一样
	required bytes msg_bin = 4;           // ChannelCommonMsg的二进制序列化
	optional bool is_need_directpush = 5; // ture表明由channelim直接完成后续的消息推送 false表明由调用者进行消息推送 默认false
	optional uint32 channel_msg_type = 6; // ga::ChannelMsgType 。 注意该字段与 channelim::ChannelMsgType 定义不一样
	optional uint32 channel_type = 7;     // ga::ChannelType 
}

message SendChannelMsgResp
{
	enum EPUSH_MSG_TYPE {
		ENUM_PUSH_NONE = 1;      // 没有push 
		ENUM_PUSH_WITH_SEQ = 2;  // 带有Seq的push 使用ga::ChannelMsg 向客户端进行了push
		ENUM_PUSH_WITH_NOSEQ = 3;// 不带有Seq的push 使用ga::ChannelBroadcastMsg 向客户端进行了push
		ENUM_PUSH_BY_CLIENT = 4;//  满足特定条件的消息要求客户端走第三方通道发送
	}

	required uint32 seq_id = 5;
	
	optional bool is_store_msg = 6;    // ture表明由channelim对消息进行了存储,false则表明没有对消息存储
	
	optional uint32 push_msg_type = 7; // EPUSH_MSG_TYPE

}

message GetChannelMsgReq
{
	required uint32 channel_id = 1;
	required uint32 begin_seq = 2;	// (begin, end]
	required uint32 end_seq = 3;
	optional bool is_init_get = 4;  // 是否是初始拉取，init拉取 只是客户端在进房时 拉取最近5分钟的 文本/图片消息
	optional uint32 channel_type = 5;     // ga::ChannelType 
}

message GetChannelMsgResp {
	repeated ChannelMsg channel_msg_list = 1;
}


// 房间消息附件 索引信息操作
message SaveChannelMsgAttachmentIndexReq
{
	required string attachment_key  = 1;
	required bytes attachment_idx_inf  = 2;
	required uint32 ttl  = 3;
	required uint32 channel_id  = 4;
}
message SaveChannelMsgAttachmentIndexResp
{
}

message GetChannelMsgAttachmentIndexReq
{
	required uint32 channel_id      = 1;
	required string attachment_key  = 2;
}
message GetChannelMsgAttachmentIndexResp
{
	required bytes attachment_idx_inf  = 1;
}

message ModifyMsgBySqeIdReq
{
	required uint32 seq_id = 1;
	required uint32 channel_id = 2;
	required uint32 type = 3;   // channelim::ChannelMsgType。 注意该字段与 ga::ChannelMsgType 定义不一样
	required bytes  msg_bin = 4;
}

message ModifyMsgBySqeIdResp
{
	
}

message DisableHistoryMsgReq
{
	required uint32 channel_id = 2;
}

message DisableHistoryMsgResp
{
	
}

service ChannelIm {
	option( tlvpickle.Magic ) = 15220;		// 服务监听端口号
	
	rpc SendChannelMsg( SendChannelMsgReq ) returns( SendChannelMsgResp ){
		option( tlvpickle.CmdID ) = 1;										
        option( tlvpickle.OptString ) = "h:s:t:";							
        option( tlvpickle.Usage ) = "-h <chid> -s <sender_uid> -t <type>";
	}
	
	rpc GetChannelMsg( GetChannelMsgReq ) returns( GetChannelMsgResp ){
		option( tlvpickle.CmdID ) = 2;										
        option( tlvpickle.OptString ) = "h:b:e:";							
        option( tlvpickle.Usage ) = "-h <chid> -b <begin_seq> -e <end_seq>";
	}
	
	// 房间消息附件 索引信息操作
	rpc SaveChannelMsgAttachmentIndex( SaveChannelMsgAttachmentIndexReq ) returns( SaveChannelMsgAttachmentIndexResp ){
		option( tlvpickle.CmdID ) = 3;										
        option( tlvpickle.OptString ) = "h:k:x:t:";							
        option( tlvpickle.Usage ) = "-h <chid> -k <key> -x <idx info> -t <ttl>";
	}
	
	rpc GetChannelMsgAttachmentIndex( GetChannelMsgAttachmentIndexReq ) returns( GetChannelMsgAttachmentIndexResp ){
		option( tlvpickle.CmdID ) = 4;										
        option( tlvpickle.OptString ) = "h:k:";							
        option( tlvpickle.Usage ) = "-h <chid> -k <key> ";
	}
	
	rpc ModifyMsgBySqeId ( ModifyMsgBySqeIdReq ) returns ( ModifyMsgBySqeIdResp ){
		option( tlvpickle.CmdID ) = 5;										
        option( tlvpickle.OptString ) = "s:m:";							
        option( tlvpickle.Usage ) = "-s <seq> -m <msgbin> ";
	}

	// 设置消息无法获取标记
	rpc DisableHistoryMsg ( DisableHistoryMsgReq ) returns ( DisableHistoryMsgResp ){
		option( tlvpickle.CmdID ) = 6;										
        option( tlvpickle.OptString ) = "x:";							
        option( tlvpickle.Usage ) = "-x <channelid>";
	}
}

