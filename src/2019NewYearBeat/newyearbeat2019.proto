syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package newyearbeat2019;


// GetActConfigReq GetActConfigResp 协议过期 废弃
message GetActConfigReq
{

}
message GetActConfigResp
{
	uint32 act_begin_ts = 1;       // 活动开始时间点
	uint32 act_end_ts = 2;         // 活动结束时间点
	uint32 interval_period_ts = 3; // 间隔周期
	uint32 payed_newyear_calls_ts = 4 ;  // 年兽 拜年时间点 比如2019-02-04 0点
	
	uint32 random_gamestart_delay_second = 5;           // 每次打年兽游戏开始前 随机等待的时间秒数 默认为5
	uint32 last_change_ts = 6;                          // 配置最后变化的时间
	uint32 random_gamefin_report_delay_second = 7;      // 每次打年兽游戏开始前 随机等待的时间秒数 默认为5
	uint32 catch_atleast_beat_cnt = 8;                  // 抓住年兽  最少需要击中年兽的次数 默认为10
}

// 新的 通用打地鼠类型游戏配置
message MoleAttackGamePeriodInfo
{
	uint32 period_begin_ts  = 1 ;   // 周期(阶段)开始时间点
	uint32 period_finish_ts = 2;    // 周期(阶段)结束时间点
	uint32 period_interval_ts = 3;  // 周期(阶段)内 每场游戏的间隔
}

message GetMoleAttackGameConfigReq
{

}
message GetMoleAttackGameConfigResp
{
	uint32 config_last_change_ts = 1;     // 配置最后变化的时间
	
	string game_name = 2;         // 活动的唯一标识 
	uint32 game_begin_ts = 3 ;    // 整个活动开始时间点
	uint32 game_finish_ts = 4;    // 整个活动结束时间点
	
	repeated MoleAttackGamePeriodInfo period_info_list = 5;
	
	uint32 random_gamestart_delay_second = 6;      // 每次打地鼠 游戏开始前 随机等待的时间秒数 默认为5
	uint32 random_gamefin_report_delay_second = 7; // 每次打地鼠 游戏结束后 随机等待的上报结果时间秒数 默认为5

	uint32 game_prepare_second = 8;             // 每场打地鼠游戏的准备(预备)阶段的时间秒
	uint32 game_duration_second = 9;            // 每场打地鼠游戏的游戏时间 不包括准备(预备)阶段的时间

	uint32 mole_appear_cnt = 10;                // 地鼠在每场游戏中出现的次数
	uint32 attack_atleast_cnt = 11;             // 最少需要击中多少次 地鼠 默认为10
}

// 打地鼠类型游戏的游戏结果上报协议
message ReportBeatResultReq
{
	enum ETEST_FALG
	{
		ENUM_TEST_FLAG_NONE = 0;
		ENUM_TEST_FLAG_LOTTERY_NOT_SUB_STOCK = 1;   // 抽奖不减库存 但是不发奖
		ENUM_TEST_FLAG_LOTTERY_SUB_STOCK = 2;       // 抽奖减库存        但是不发奖
	}

	uint32 uid = 1;   
	uint32 cid = 2;  
	uint32 beat_cnt = 3;  	

	uint32 test_flag = 4;
}
message ReportBeatResultResp
{

}

// 打地鼠的奖品
enum ELOTTERY_ITEM_TYPE
{
	ENUM_LOTTERY_ITEM_INVALID = 0;
	ENUM_LOTTERY_ITEM_RedDiamond = 1;
	ENUM_LOTTERY_ITEM_Medal = 2;
	ENUM_LOTTERY_ITEM_Headwaer = 3;
	ENUM_LOTTERY_ITEM_Package = 4;
	ENUM_LOTTERY_ITEM_ChannelEffect = 5;
}

message LotteryResultItem
{
	uint32 type = 1;   // ELOTTERY_ITEM_TYPE
	string name = 2;  	
	string img_url = 3;  
	bool is_limit = 4; // 是否限量奖品
	uint32 price = 5;  // 价值
}

message UserLotteryResultInfo
{
	LotteryResultItem item = 1;   
	uint32 uid = 2;
	uint32 ts = 3;
}

message UserTotalBeatInfo
{
	uint32 uid = 1;
	uint32 beat_cnt = 2;	// 击中次数
	uint32 award_cnt = 3;	// 获奖次数
	uint32 ranking = 4;
}

// 获取用户的打年兽的抽奖奖励结果 (只在没有收到奖励PUSH的情况下 才需要调用)
message GetUserBeatLotteryResultReq
{
	uint32 uid = 1;
	uint32 cid = 2;	
}
message GetUserBeatLotteryResultResp
{
	UserLotteryResultInfo user_result_into = 1;
}

// 获取活动排行榜
message GetActRankListReq
{
	uint32 offset = 1;
	uint32 count = 2;
}

message GetActRankListResp
{
	repeated UserTotalBeatInfo rank_list = 1;
	uint32 total_count = 2;
}

message GetActRankByUidReq
{
	uint32 uid = 1;
}

message GetActRankByUidResp
{
	UserTotalBeatInfo rank_info = 1;
}

// 获取奖励历史
message GetActAwardHistoryReq
{
}

message GetActAwardHistoryResp
{
	repeated UserLotteryResultInfo award_list = 1;
}

service newyearbeat2019 {
	option( tlvpickle.Magic ) = 15235;		// 服务监听端口号

	// 获取当前的配置信息
	rpc GetActConfig( GetActConfigReq ) returns( GetActConfigResp ){
		option( tlvpickle.CmdID ) = 1;										
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	} 
	
	// 获取当前打地鼠类型游戏配置
	rpc GetMoleAttackGameConfig( GetMoleAttackGameConfigReq ) returns( GetMoleAttackGameConfigResp ){
		option( tlvpickle.CmdID ) = 2;										
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	} 
	
	// 上报用户的打年兽结果
    rpc ReportBeatResult( ReportBeatResultReq ) returns( ReportBeatResultResp ){
		option( tlvpickle.CmdID ) = 10;										
        option( tlvpickle.OptString ) = "u:x:";							
        option( tlvpickle.Usage ) = "-u <uid> -x <beat cnt>";
	}
	
	// 获取用户的打年兽的抽奖奖励结果
    rpc GetUserBeatLotteryResult( GetUserBeatLotteryResultReq ) returns( GetUserBeatLotteryResultResp ){
		option( tlvpickle.CmdID ) = 11;										
        option( tlvpickle.OptString ) = "u:x:";							
        option( tlvpickle.Usage ) = "-u <uid> -x <channel id>";
	}
	

	// 获取活动排行榜
	rpc GetActRankList( GetActRankListReq ) returns( GetActRankListResp ){
		option( tlvpickle.CmdID ) = 20;										
        option( tlvpickle.OptString ) = "o:n:";							
        option( tlvpickle.Usage ) = "-o <offset> -n <count>";
	}

	// 获取奖励历史
	rpc GetActAwardHistory( GetActAwardHistoryReq ) returns( GetActAwardHistoryResp ){
		option( tlvpickle.CmdID ) = 21;										
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	}
	
	rpc GetActRankByUid( GetActRankByUidReq ) returns( GetActRankByUidResp ){
		option( tlvpickle.CmdID ) = 22;										
        option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid>";
	}
}
