syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package usergiftbox;

// 用户宝箱 / 我的宝箱服务

message UserGiftBoxItem
{
    required uint32 gift_item_type          = 1;   // item类型, 定义 giftpkg2_.proto 中 ga.EGiftPktItemType     礼包类型: 激活码，游戏礼包，代金券，充值卡，首充号，周边 
	required uint32 gift_item_exchange_type = 2;   // 兑换类型, 定义 giftpkg2_.proto 中 ga.EGiftPktExchangeType 兑换形式: 兑换码，卡号+密码
	required uint32 gift_item_source_type   = 3;   // 礼包来源, 定义 giftpkg2_.proto 中 ga.EGiftPktSourceType   礼包来源: TT官方礼包中心 会长录入 TT运营活动
	required bool is_taohao                 = 4;   // 是否是淘号来的
	required bytes  gift_detail_info        = 5;   // 礼包的详细信息结构体 giftpkg2_.proto ga.GiftProductDetail
	optional uint32 giftbox_itemid   = 6;          // 该物品在我的宝箱内的ID 新加入的礼包该值为0
	optional uint32 update_ts   = 7;               // 该物品在我的宝箱内的时间戳
	optional uint64 pkg_product_id   = 8;          // 该物品对应的礼包 在礼包中心的ID 仅对来源为TT官方礼包中心的礼包有效
}

// 添加到我的宝箱
message AddToUserGiftBoxReq
{
	required uint32 uid = 1;
    required UserGiftBoxItem item	= 2;

}

message AddToUserGiftBoxResp
{
    required uint32 giftbox_itemid   = 1;  // 该物品在我的宝箱内的ID
}

message BatchAddToUserGiftBoxReq
{
	required uint32 uid = 1;
    repeated UserGiftBoxItem item_list  = 2;   

}
message BatchAddToUserGiftBoxResp
{
  
}

// 获取我的宝箱列表
message GetUserGiftBoxListReq
{
	enum EExceptTaohaoFlag{
		
		EXCEPT_TAOHAO_DEFAULT = 0;           // 默认不区分淘号非淘号
		EXCEPT_TAOHAO_ONLY_TAOHAO  = 1;      // 仅返回淘号数据 
		EXCEPT_TAOHAO_ONLY_NOT_TAOHAO  = 2;  // 仅返回非淘号数据
	}
	
    required uint32 uid  = 1;   
	required uint32 start_giftbox_itemid = 2;
	required uint32 count = 3;
	required uint32 gift_item_type          = 4;   // item类型, 定义 giftpkg2_.proto 中 ga.EGiftPktItemType     礼包类型: 激活码，游戏礼包，代金券，充值卡，首充号，周边 
	required uint32 gift_item_source_type   = 5;   // 礼包来源, 定义 giftpkg2_.proto 中 ga.EGiftPktSourceType   礼包来源: TT官方 会长录入
	required bool is_include_start_boxid    = 6;   // 返回的数据是否包含 start_giftbox_itemid 本身
	optional uint32 except_taohao_flag    = 7;     // SEE EExceptTaohaoFlag
}

message GetUserGiftBoxListResp
{
    repeated UserGiftBoxItem item_list  = 1;   
	required bool is_have_more    = 2;            // 是否还有更多的数据
}

// 计算我的宝箱中某种类型的物品数量
message CountUserGiftBoxByItemTypeReq
{
    required uint32 uid  = 1;   
	required uint32 gift_item_type = 2; // item类型, 定义 giftpkg2_.proto 中 ga.EGiftPktItemType     礼包类型: 激活码，游戏礼包，代金券，充值卡，首充号，周边 
}

message CountUserGiftBoxByItemTypeResp
{
    required uint32 count  = 1;   
}



service usergiftbox {
    option( tlvpickle.Magic ) = 15495;

	// 添加到我的宝箱
    rpc AddToUserGiftBox( AddToUserGiftBoxReq ) returns( AddToUserGiftBoxResp ){
		option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
	}

	// 获取我的宝箱列表
	rpc GetUserGiftBoxList( GetUserGiftBoxListReq ) returns( GetUserGiftBoxListResp  ){
		option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:s:x:";
        option( tlvpickle.Usage ) = "-u <uid> -s <start id> -x <count>";
	}
	
	// 批量 添加到我的宝箱
    rpc BatchAddToUserGiftBox( BatchAddToUserGiftBoxReq ) returns( BatchAddToUserGiftBoxResp ){
		option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
	}
	
	// 计算某种类型的物品在宝箱中的数量
	rpc CountUserGiftBoxByItemType( CountUserGiftBoxByItemTypeReq ) returns( CountUserGiftBoxByItemTypeResp ){
		option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "u:x:";
        option( tlvpickle.Usage ) = "-u <uid> -x <type>";
	}
}
