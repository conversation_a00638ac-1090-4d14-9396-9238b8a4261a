syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package findfriend_matching;

message FindFriendExamResult {
	required uint32 uid = 1;
	required uint32 classify = 2;
	required uint32 tag_id = 3;
	required uint32 sex = 5;
	required uint32 register_at = 6;
}

message NotifyExamResultResp {

}

message MatchingFriendReq {
	required uint32 uid = 1;
	required uint32 sex = 2;
	required uint32 register_at = 3;
}

message MatchingFriendResp {
	required uint32 matching_uid = 1;
	
	optional uint32 matching_wait_amount = 2; // 候选集合数量
	optional string matching_rule = 3;
}

message CheckDoMatchCntLimitReq
{
	required uint32 uid = 1;
}

message CheckDoMatchCntLimitResp
{
	required bool result = 1;
}

message IncrDoMatchCntLimitReq
{
	required uint32 uid = 1;
	required string reason = 2;
}

message IncrDoMatchCntLimitResp
{
}

service FindFriendMatchingSvr{
    option( tlvpickle.Magic ) = 15640;

    rpc NotifyExamResult ( FindFriendExamResult ) returns ( NotifyExamResultResp ) {
        option( tlvpickle.CmdID ) = 1;	
	    option( tlvpickle.OptString ) = "u:t:i:s:r:";	
        option( tlvpickle.Usage ) = "-u <uid> -t <tag_type> -i <tag_id> -s <sex> -r <register_at>";
    }

	rpc MatchingFriend ( MatchingFriendReq ) returns ( MatchingFriendResp ) {
        option( tlvpickle.CmdID ) = 2;	
	    option( tlvpickle.OptString ) = "u:s:r:";	
        option( tlvpickle.Usage ) = "-u <uid> -s <sex> -r <register_at>";
    }

    rpc CheckDoMatchCntLimit ( CheckDoMatchCntLimitReq ) returns ( CheckDoMatchCntLimitResp ) {
        option( tlvpickle.CmdID ) = 3;	
	    option( tlvpickle.OptString ) = "u:s:r:";	
        option( tlvpickle.Usage ) = "-u <uid> -s <sex> -r <register_at>";
    }

    rpc IncrDoMatchCntLimit ( IncrDoMatchCntLimitReq ) returns ( IncrDoMatchCntLimitResp ) {
        option( tlvpickle.CmdID ) = 4;
	    option( tlvpickle.OptString ) = "u:s:r:";	
        option( tlvpickle.Usage ) = "-u <uid> -s <sex> -r <register_at>";
    }
}
