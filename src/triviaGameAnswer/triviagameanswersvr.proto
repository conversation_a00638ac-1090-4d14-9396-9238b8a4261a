syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

// 知识问答 答题阶段处理

package triviagame_answer;





// 获取答题统计结果
message ResultStat
{
	required uint32 answer_id = 1;   // ETriviaGameAnswerId  1=A 2=B 3=C
	required uint32 answer_cnt = 2;  // 回答人数
}


message AnswerReq
{
	required uint32 active_id  = 1;   // 活动ID 类似 2018010820
	required uint32 question_idx = 2;  // 题目序号 第几题 取值 1-12

	required uint32 uid         = 3;	
	required uint32 answer_id   = 4;  // 回答的内容 ETriviaGameAnswerId 1=A 2=B 3=C 255=没有答题
}

message AnswerResp	
{
	required uint32 resulut_status = 1;     // ETriviaGameAnswerResultStatus
	required uint32 correct_answer_id = 2;  // 正确答案是多少 ETriviaGameAnswerId  1=A 2=B 3=C 255=没有答题
}

message GetQuestionResultStatReq
{
	required uint32 active_id  = 1;   // 活动ID 类似 2018010820
	required uint32 question_idx = 2;  // 题目序号 第几题 取值 1-12
}

message GetQuestionResultStatResp
{
	repeated ResultStat stat_list = 1 ; // 各个选项的回答人数
}

// (废弃)
message GetUserAnswerStatReq
{
	required uint32 uid = 1; 
}
message GetUserAnswerStatResp
{
}

// 获取指定问题的 正确回答用户列表
message GetAnswerCorrectUidListReq
{
	required uint32 active_id  = 1;   // 活动ID 类似 2018010820
	required uint32 question_idx = 2;  // 题目序号 第几题 取值 1-12
	
	
	required uint32 begin_idx = 3;  // 翻页参数
	required uint32 limit = 4;      // 翻页参数
}

message GetAnswerCorrectUidListResp
{
	repeated uint32 uid_list = 1 ; // 
	optional uint32 total_cnt = 2; // 答对问题的总人数
}

// 检查用户在当前活动当前阶段是否有答题资格
message CheckUserAnswerQualifyReq
{
	required uint32 uid = 1; 
}
message CheckUserAnswerQualifyResp
{
	required bool is_qualify = 1; 
}

// 给活动获胜用户发奖
message AwardMoneyToWinUserReq
{
	required uint32 act_id = 1; 
	required uint32 moneypoll_fullcnt = 2;  // 奖金池总额 单位分
}
message AwardMoneyToWinUserResp
{
	required uint32 win_user_cnt = 1; // 获胜用户总数
	required uint32 money_unit = 2;   // 单个用户分到的前 单位分
}

// 淘汰用户
message WashedoutUserReq
{
	required uint32 act_id = 1; 
	required uint32 uid = 2; 
}
message WashedoutUserResp
{
}

service triviaGameAnswerSvr
{
	option( tlvpickle.Magic ) = 15235;

	// 答题
	rpc Answer (AnswerReq)returns( AnswerResp){
	option( tlvpickle.CmdID ) = 1;						
    option( tlvpickle.OptString ) = "a:q:u:s:";	
    option( tlvpickle.Usage ) = "-a <activer_id> -q <question_idx> -u <uid> -s <answer_id>";	
	}
	
	// 获取答题统计结果
	rpc GetQuestionResultStat (GetQuestionResultStatReq)returns( GetQuestionResultStatResp){
	option( tlvpickle.CmdID ) = 2;						
    option( tlvpickle.OptString ) = "a:q:";				
    option( tlvpickle.Usage ) = "-a <activer_id> -q <question_idx> ";	
	}
	

	// 获取用户统计数据(废弃)
	rpc GetUserAnswerStat (GetUserAnswerStatReq)returns( GetUserAnswerStatResp){
	option( tlvpickle.CmdID ) = 3;						
    option( tlvpickle.OptString ) = "u:";				
    option( tlvpickle.Usage ) = "-u <uid> ";	
	}
	
	// 获取指定问题的 正确回答用户列表
	rpc GetAnswerCorrectUidList (GetAnswerCorrectUidListReq)returns( GetAnswerCorrectUidListResp){
	option( tlvpickle.CmdID ) = 4;						
    option( tlvpickle.OptString ) = "a:q:";				
    option( tlvpickle.Usage ) = "-a <activer_id> -q <question_idx> ";	
	}
	
	// 检查用户在当前活动当前阶段 有没有答题资格
	rpc CheckUserAnswerQualify (CheckUserAnswerQualifyReq)returns( CheckUserAnswerQualifyResp){
	option( tlvpickle.CmdID ) = 5;						
    option( tlvpickle.OptString ) = "u:";				
    option( tlvpickle.Usage ) = "-u <uid> ";	
	}

	// 给活动获胜用户发奖
	rpc AwardMoneyToWinUser (AwardMoneyToWinUserReq)returns( AwardMoneyToWinUserResp){
	option( tlvpickle.CmdID ) = 6;						
    option( tlvpickle.OptString ) = "a:s:";				
    option( tlvpickle.Usage ) = "-a <act id> -s <money score unit>";	
	}
	
	// 淘汰用户
	rpc WashedoutUser (WashedoutUserReq)returns( WashedoutUserResp){
		option( tlvpickle.CmdID ) = 7;						
		option( tlvpickle.OptString ) = "a:u:";				
		option( tlvpickle.Usage ) = "-a <act id> -u <uid>";	
	}
}
