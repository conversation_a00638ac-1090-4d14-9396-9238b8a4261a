syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Push;

enum PLATFORM {
    ANDROID = 1;            // 1 << 0
    IPHONE = 2;             // 1 << 1
    ALL_PLATFORM = 65535;   // ALL_PLATFORM
};

message PushMessage {
	enum PUSH_MESSAGE_TYPE {
		ALL = 0;
		GUILD_OWNER = 1;
	}
	enum PUSH_MESSAGE_STATUS {
		NEW = 0;
		REVIEWED = 1;
		REFUSED = 2;
		DELETED = 3;
		SENT = 4;
	}

	required uint32 push_message_id = 1;
	required string content = 2;
	required uint32 type = 3;
	required uint32 status = 4;
	required uint32 creator_uid = 5;
	required uint32 review_uid = 6;
	required uint32 delete_uid = 7;
	required uint32 create_time = 8;
	required uint32 push_time = 9;
	required uint32 expire_time = 10;
	required uint32 badge = 11;
	required bool sound = 12;
	required string ext_content = 13;
	required string receiver_group = 14;
	required string refuse_reason = 15;
}

message CreatePushMessageReq {
	required string content = 1;
	required uint32 type = 2;
	required uint32 creator_uid = 3;
	required uint32 push_time = 4;
	required uint32 expire_time = 5;
	required uint32 badge = 6;
	required bool sound = 7;
	required string ext_content = 8;
	required string receiver_group = 9;
}

message CreatePushMessageResp {
	required uint32 push_message_id = 1;
}


message ReviewPushMessageReq {
	required uint32 push_message_id = 1;
	required uint32 review_uid = 2;
	required bool   agree = 3;
	required string refuse_reason = 4;
}

message ReviewPushMessageResp {

}

message DeletePushMessageReq {
	required uint32 push_message_id = 1;
	required uint32 delete_uid = 2;
}

message DeletePushMessageResp {

}

message GetPushMessageReq {
	required uint32 push_message_id = 1;
}

message GetPushMessageResp {
	required PushMessage push_message = 1;
}

message GetPushMessageListReq {
	required uint32 offset = 1;
	required uint32 limit = 2;
}



message GetPushMessageListResp {
	repeated PushMessage push_message_list = 1;
}

message BatGetPushMessageReq {
	repeated uint32 push_message_id_list = 1;
}

message BatGetPushMessageResp {
	repeated PushMessage push_message_list = 1;
}


/////////////////////////////////////////////////
// 新推送存储
/////////////////////////////////////////////////

message PushMessageV2 {
	required uint32 push_id = 1;
	required uint32 msg_type = 2;
	required bytes msg_bin = 3;
	required uint32 biz_type = 4;
	required uint64 biz_id = 5;
	required bytes biz_data = 6;
	required uint32 creator_uid = 7;
	required uint32 review_uid = 8;
	required uint32 delete_uid = 9;
	required uint32 create_time = 10;
	required uint32 push_time = 11;
	required uint32 expire_time = 12;
	required uint32 badge = 13;
	required uint32 sound = 14;
	required uint32 status = 15;
	required string refuse_reason = 16;
    optional uint32 target_platform = 17;   // mask, default is both
}

message CreatePushMessageV2Req {
	required uint32 msg_type = 1;
	required bytes msg_bin = 2;
	required uint32 biz_type = 3;
	required uint64 biz_id = 4;
	required bytes biz_data = 5;
	required uint32 creator_uid = 6;
	required uint32 review_uid = 7;
	required uint32 delete_uid = 8;
	required uint32 push_time = 9;
	required uint32 expire_time = 10;
	required uint32 badge = 11;
	required uint32 sound = 12;
    optional uint32 target_platform = 13;
}

message CreatePushMessageV2Resp {
	required uint32 push_id = 1;
}

message GetPushMessageV2Req {
	required uint32 push_id = 1;
}

message GetPushMessageV2Resp {
	required PushMessageV2 push_message = 1;
}

message GetPushMessageV2ListForBizReq {
	required uint32 biz_type = 1;
	required uint64 biz_id = 2;
}

message GetPushMessageV2ListForBizResp {
	repeated PushMessageV2 push_message_list = 1;
}

message UpdatePushMessageStatusV2Req {
	required uint32 push_id = 1;
	required uint32 status = 2;
	optional string refuse_reason = 3;	// 如果status是REFUSED. 则需要填写该字段
}

message UpdatePushMessageStatusV2Resp {

}

/////////////////////////////////////////////////
// BIZ定义(先放这吧)
/////////////////////////////////////////////////

enum PushBizType {
	GLOBAL = 1;				// 全网推送
	PUBLIC_ACCOUNT = 2;		// 公众号推送(推送至公众号的所有订阅者)
}

service Push {
	option( tlvpickle.Magic ) = 15070;		// 服务监听端口号

	rpc CreatePushMessage ( CreatePushMessageReq ) returns ( CreatePushMessageResp ){
		option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "m:t:u:e:p:b:s:x:r:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <message content> -t <type> -u <creator uid> -e <expire time> -p <push time> -b <badge> -s <sound> -x <message extend content> -r < receiver group >";	// 测试工具的命令号帮助
	}

	rpc ReviewPushMessage ( ReviewPushMessageReq ) returns ( ReviewPushMessageResp ){
		option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "m:u:a:r:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <push message id>  -u <review uid> -a <is agree> -r <refuse reason> ";	// 测试工具的命令号帮助
	}

	rpc DeletePushMessage ( DeletePushMessageReq ) returns ( DeletePushMessageResp ){
		option( tlvpickle.CmdID ) = 3;										// 命令号
        option( tlvpickle.OptString ) = "m:u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <push message id>  -u <delete uid> ";

	}

	rpc GetPushMessage ( GetPushMessageReq ) returns ( GetPushMessageResp ){
		option( tlvpickle.CmdID ) = 4;										// 命令号
        option( tlvpickle.OptString ) = "m:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <push message id> ";

	}

	rpc GetPushMessageList ( GetPushMessageListReq ) returns ( GetPushMessageListResp ){
		option( tlvpickle.CmdID ) = 5;										// 命令号
        option( tlvpickle.OptString ) = "o:l:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-o <offset> -l <limit> ";

	}

	rpc BatGetPushMessage ( BatGetPushMessageReq ) returns ( BatGetPushMessageResp ){
		option( tlvpickle.CmdID ) = 6;										// 命令号
        option( tlvpickle.OptString ) = "m:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <push message id list> ";
	}


	rpc CreatePushMessageV2 ( CreatePushMessageV2Req ) returns ( CreatePushMessageV2Resp ) {
		option( tlvpickle.CmdID ) = 7;									// 命令号
        option( tlvpickle.OptString ) = "";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
	}

	rpc GetPushMessageV2 ( GetPushMessageV2Req ) returns ( GetPushMessageV2Resp ) {
		option( tlvpickle.CmdID ) = 8;							// 命令号
        option( tlvpickle.OptString ) = "p:";					// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <push_id>";
	}

	rpc GetPushMessageV2ListForBiz ( GetPushMessageV2ListForBizReq ) returns ( GetPushMessageV2ListForBizResp ) {
		option( tlvpickle.CmdID ) = 9;							// 命令号
        option( tlvpickle.OptString ) = "t:z:";					// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <biz_type> -z <biz_id>";
	}

	rpc UpdatePushMessageStatusV2 ( UpdatePushMessageStatusV2Req ) returns ( UpdatePushMessageStatusV2Resp ){
		option( tlvpickle.CmdID ) = 10;								// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";								// 测试工具的命令号帮助
	}
}
