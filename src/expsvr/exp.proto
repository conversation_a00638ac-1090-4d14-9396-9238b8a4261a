syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package Exp;

message UserExp {
	required uint32 uid = 1;
	required int32 exp = 2;
	required uint32 level = 3;
}

// ===================================================
//
// 获取用户经验值及等级
//
// ===================================================

message GetUserExpReq {
	required uint32 uid = 1;
}

message GetUserExpResp {
	required int32 exp = 1;
	required uint32 level = 2;
}


message BatGetUserExpReq {
	repeated uint32 uid_list = 1;
}

message BatGetUserExpResp {
	repeated UserExp user_exp_list = 1;
}

message GetUserExpLogReq {
	required uint32 uid = 1;
	optional uint32 begin_time = 2;
	optional uint32 end_time = 3;
	optional string desc_like = 4;
}

message UserExpLogInfo {
	required uint32 e_value = 1;
	required string op_time = 2;
	required string desc = 3;
}

message GetUserExpLogResp {
	repeated UserExpLogInfo log_infos = 1;
}


// ====================================================
//
// 添加用户经验
//
// ====================================================

message AddUserExpReq {
	required uint32 uid = 1;
	required int32 exp = 2;
	required string mission_key = 3;
	required string mission_desc = 4;
	required uint32 op_uid = 5;
}

message AddUserExpResp {
	
}

// ======================================================
//
// 特定等级对应的起始经验值
// 
// ======================================================

message GetLevelExpScopeReq {
	required uint32 level = 1 ;
}

message GetLevelExpScopeResp {
	required uint32 start_exp = 1;
	required uint32 end_exp   = 2;
}


// =======================================================
//
// 判断是否已经为用户在对应任务上添加相应经验
//
// =======================================================

message HasAddedUserExpReq {
	required uint32 uid = 1;
	required string mission_key =2 ;
}

message HasAddedUserExpResp {
	required bool added = 1;
	required int32 changed_exp = 2;
}

// ========================================================
//
// 加勋章
//
// ========================================================

message AddUserMedalReq {
	required uint32 uid = 1;
	required uint32 medal_id = 2;
}

message AddUserMedalResp {
	
}


// ========================================================
// 
// 用户获得的勋章列表
//
// ========================================================


message UserMedal {
	required uint32 uid = 1;
	required uint32 medal_id = 2;
	required uint32 time = 3;
}

message GetUserMedalReq {
	required uint32 uid = 1;
}

message GetUserMedalResp {
	repeated UserMedal user_medal_list = 1;
}

message BatGetUserMedalReq {
	repeated uint32 uid_list = 1;
}

message BatGetUserMedalResp {
	repeated UserMedal user_medal_list = 1;
}

// 上报用户活跃 用于计算用户的在线时长
message ReportUserAlivenessReq {
	required uint32 uid = 1;
}

message ReportUserAlivenessResp {
	optional uint32 changed_exp = 1;
}

message GetUserOnlineTimeReq {
	required uint32 uid = 1;
}

message GetUserOnlineTimeResp {
	required uint32 online_time = 1;
}

service Exp {
	option( tlvpickle.Magic ) = 15020;		// 服务监听端口号	

	rpc GetUserExp( GetUserExpReq ) returns ( GetUserExpResp ){
		option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>	";	// 测试工具的命令号帮助
	}

	rpc AddUserExp( AddUserExpReq ) returns ( AddUserExpResp ){
		option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "t:u:e:r:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <1:uid,2:ttid> -u <id> -e <exp> -r <red diamonds>";	// 测试工具的命令号帮助
	}

	rpc GetLevelExpScope( GetLevelExpScopeReq ) returns ( GetLevelExpScopeResp ){
		option( tlvpickle.CmdID ) = 3;										// 命令号
        option( tlvpickle.OptString ) = "l:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <level>	";
	}

	rpc HasAddedUserExp( HasAddedUserExpReq ) returns ( HasAddedUserExpResp ){
		option( tlvpickle.CmdID ) = 4;										// 命令号
        option( tlvpickle.OptString ) = "u:k:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -k <mission key>";
	}

	rpc BatGetUserExp( BatGetUserExpReq ) returns ( BatGetUserExpResp ){
		option( tlvpickle.CmdID ) = 5;										// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid list>";
	}

	rpc AddUserMedal( AddUserMedalReq ) returns ( AddUserMedalResp ){
		option( tlvpickle.CmdID ) = 6;										// 命令号
        option( tlvpickle.OptString ) = "u:m:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <medal id>";
	}

	rpc GetUserMedal( GetUserMedalReq ) returns ( GetUserMedalResp ){
		option( tlvpickle.CmdID ) = 7;										// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";
	}

	rpc BatGetUserMedal( BatGetUserMedalReq ) returns ( BatGetUserMedalResp ){
		option( tlvpickle.CmdID ) = 8;										// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid list>";
	}

	rpc ReportUserAliveness( ReportUserAlivenessReq ) returns ( ReportUserAlivenessResp ){
		option( tlvpickle.CmdID ) = 10;										// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";
	}	
	
	rpc GetUserOnlineTime( GetUserOnlineTimeReq ) returns ( GetUserOnlineTimeResp ){
		option( tlvpickle.CmdID ) = 11;										// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";
	}

	// 经验记录
	rpc GetUserExpLog( GetUserExpLogReq ) returns ( GetUserExpLogResp ) {
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "u:b:e:d:";
        option( tlvpickle.Usage ) = "-u <uid> -b <begin_time> -e <end_time> -d <desc_like>";
    }
}

