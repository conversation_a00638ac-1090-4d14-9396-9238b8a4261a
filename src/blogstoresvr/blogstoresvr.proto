syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package blogstoresvr;


//查询接口
message GetUpvoteListReq {
	required uint32 uid = 1;
	required uint64 moment_id = 2;
}

message GetUpvoteListResp {
	repeated uint32 uid_list = 1;
}

message UpvoteMomentReq {
	required uint32 auth_uid = 1;
	required uint64 moment_id = 2;
	required uint32 vote_uid = 3;
}



//写入接口

service BlogStoreSvr{
    option( tlvpickle.Magic ) = 15610;

	rpc GetUpvoteList ( GetUpvoteListReq ) returns (GetUpvoteListResp ) {
        option( tlvpickle.CmdID ) = 1;										
	    option( tlvpickle.OptString ) = "u:m:";							
        option( tlvpickle.Usage ) = "-u <uid>  -m <moment_id>";	
    }

	rpc UpvoteMoment ( UpvoteMomentReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 50;								
	    option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
    }
}
 