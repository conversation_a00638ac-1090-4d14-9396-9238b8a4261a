syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package channelguild;

//channel of guild apis
message AddChannelGuildReq{
    uint32 guild_id = 1;
    uint32 channel_id = 2;
    uint32 type = 3;    // see ga::ChannelType
}

message DelChannelGuildReq{
    uint32 guild_id = 1;
    uint32 channel_id = 2;
}

message GetChannelGuildListReq{
    uint32 guild_id = 1;
    uint32 type = 2;    // see ga::ChannelType, 0 - all type
    uint32 size = 3;
}

message GetChannelGuildListResp{
    repeated uint32 channel_ids = 1;
    uint32 total = 2; // total channel list size
}

message BatGetChannelGuildListReq{
    repeated uint32 guild_ids = 1;
    uint32 type = 2;
    uint32 size = 3;
}

message BatGetChannelGuildListEntry{
    uint32 guild_id = 1;
    repeated uint32 channel_ids = 2;
    uint32 total = 3;
}

message BatGetChannelGuildListResp{
    repeated BatGetChannelGuildListEntry result_list = 1;
}

message DismissChannelGuildReq{
    uint32 guild_id = 1 ;
}

message DismissChannelGuildResp{
    repeated uint32 channel_ids = 1; 
}

//成员所在房间
message AddGuildMemberChannelReq{
    uint32 guild_id = 1;
    uint32 channel_id = 2;
    string account = 3; //用户account
}

message DelGuildMemberChannelReq{
    uint32 guild_id = 1;
    uint32 channel_id = 2;
    string account = 3; //用户account
}

message GuildMemberChannelInfo{
    uint32 channel_id = 1;
    string account = 3; //用户account
}

message GetGuildMemberChannelReq{
    uint32 guild_id = 1;
    uint32 start_index = 2;
    uint32 size = 3;
}

message GetGuildMemberChannelResp{
    repeated GuildMemberChannelInfo channel_list = 1;
    uint32 total = 2;
}

//运营配置给公会的合作房间
message PartnerChannelGuildInfo{
    uint32 guild_id = 1;
    uint32 channel_id = 2;
}

message GetPartnerChannelGuildListResp
{
    repeated PartnerChannelGuildInfo info_list =1;
}

message GetPartnerChannelGuildListByGuildReq{
    uint32 guild_id = 1;
}

message GetPartnerChannelGuildListByGuildResp{
    repeated uint32 channel_ids = 1;
}


// 检查公会房间的数量限制
message CheckGuildChannelCntLimitReq
{
    uint32 guild_id = 1;
    uint32 type = 2;           // see ga::ChannelType
    uint32 guild_level = 3;    // 公会等级
}
message CheckGuildChannelCntLimitResp
{
    uint32 curr_count = 1;      // 当前数量
    uint32 remain_count = 2;    // 剩余可创建数量
    string ban_msg = 3;         // 剩余可创建数量为0时的 提示语句
}

// 创建公会公开房
message CreateGuildPubChannelReq
{
    uint32 guild_id = 1;      
    uint32 guild_owner = 2; 
}
message CreateGuildPubChannelResp
{
}

service ChannelGuild {
    option( tlvpickle.Magic ) = 15222;      // 服务监听端口号

    rpc AddChannelGuild( AddChannelGuildReq ) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 1;             
        option( tlvpickle.OptString ) = "g:x:t:";  
        option( tlvpickle.Usage ) = "-g<guild_id> -x<channel_id> -t<type>";    
    }

    rpc DelChannelGuild( DelChannelGuildReq ) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 2;             
        option( tlvpickle.OptString ) = "g:x:";   
        option( tlvpickle.Usage ) = "-g<guild_id> -x<channel_id>";   
    }

    rpc GetChannelGuildList(GetChannelGuildListReq) returns(GetChannelGuildListResp) {
        option( tlvpickle.CmdID ) = 3;              // 命令号
        option( tlvpickle.OptString ) = "g:t:s:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g<guild_id> -t<type> -s<size>";    // 测试工具的命令号帮助
    }

    rpc AddPartnerChannelGuild( PartnerChannelGuildInfo ) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 4;              // 命令号
        option( tlvpickle.OptString ) = "g:x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g<guild_id> -x<channel_id>";    // 测试工具的命令号帮助
    }

    rpc DelPartnerChannelGuild( PartnerChannelGuildInfo ) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 5;              // 命令号
        option( tlvpickle.OptString ) = "g:x:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g<guild_id> -x<channel_id>";    // 测试工具的命令号帮助
    }

    rpc GetPartnerChannelGuildList( tlvpickle.SKBuiltinEmpty_PB ) returns(GetPartnerChannelGuildListResp) {
        option( tlvpickle.CmdID ) = 6;              // 命令号
        option( tlvpickle.OptString ) = "";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
    }

    rpc BatGetChannelGuildList(BatGetChannelGuildListReq) returns(BatGetChannelGuildListResp) {
        option( tlvpickle.CmdID ) = 7;              // 命令号
        option( tlvpickle.OptString ) = "g:t:s:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g<guild_id> -t<type> -s<size>";    // 测试工具的命令号帮助
    }

    rpc DismissChannelGuild(DismissChannelGuildReq) returns(DismissChannelGuildResp) {
        option( tlvpickle.CmdID ) = 8;             
        option( tlvpickle.OptString ) = "g:";  
        option( tlvpickle.Usage ) = "-g<guild_id>"; 
    }

    rpc GetPartnerChannelGuildListByGuild( GetPartnerChannelGuildListByGuildReq ) returns(GetPartnerChannelGuildListByGuildResp) {
        option( tlvpickle.CmdID ) = 9;              
        option( tlvpickle.OptString ) = "g:";  
        option( tlvpickle.Usage ) = "-g<guild_id>";   
    }

    rpc AddGuildMemberChannel( AddGuildMemberChannelReq ) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 10;             
        option( tlvpickle.OptString ) = "g:x:a:";   
        option( tlvpickle.Usage ) = "-g<guild_id> -x<channel_id> -a<account>";  
    }

    rpc DelGuildMemberChannel( DelGuildMemberChannelReq ) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 11;              
        option( tlvpickle.OptString ) = "g:x:a:";  
        option( tlvpickle.Usage ) = "-g<guild_id> -x<channel_id> -a<account>";   
    }

    rpc GetGuildMemberChannel(GetGuildMemberChannelReq) returns(GetGuildMemberChannelResp) {
        option( tlvpickle.CmdID ) = 12;              
        option( tlvpickle.OptString ) = "g:t:s:";   
        option( tlvpickle.Usage ) = "-g<guild_id> -t<start_index> -s<size>";   
    }  

	// 检查公会房间的数量限制
    rpc CheckGuildChannelCntLimit(CheckGuildChannelCntLimitReq) returns(CheckGuildChannelCntLimitResp) {
        option( tlvpickle.CmdID ) = 13;              
        option( tlvpickle.OptString ) = "g:t:";   
        option( tlvpickle.Usage ) = "-g<guild_id> -t<type>";   
    }   

     // 创建公会公开房
    rpc CreateGuildPubChannel(CreateGuildPubChannelReq) returns(CreateGuildPubChannelResp) {
        option( tlvpickle.CmdID ) = 14;              
        option( tlvpickle.OptString ) = "g:o:";   
        option( tlvpickle.Usage ) = "-g<guild_id> -o<guild_owner>";   
    }   
}

