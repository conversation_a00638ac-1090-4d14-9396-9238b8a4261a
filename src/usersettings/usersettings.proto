syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package UserSettings;

message Setting {
    required string key = 1;
    required string value = 2;
    required uint32 type = 3;
}

message SetSettingsReq {
    required Setting setting = 1;
}

message SetSettingsResp {
    required int32 result = 1;
}

message MultiType{
    repeated uint32 type_list = 1;
    required uint32 uid = 2;
}

message MultiSettings{
    repeated Setting setting_list = 1;
    required uint32 uid = 2;
}

message MultiKey{
    repeated string key_list = 1;
    required uint32 uid = 2;
}

message GetMultiSettingsByKeyReq{
    repeated MultiKey mul_key_list = 1;
}

message GetMultiSettingsByKeyResp{
    repeated MultiSettings multi_settings_list = 1;
}

message GetMultiSettingsByTypeReq{
    repeated MultiType mul_type_list = 1;
}

message GetMultiSettingsByTypeResp{
    repeated MultiSettings multi_settings_list = 1;
}


message GetSingleSettingReq{
    required uint32 uid = 1;
    repeated string key_list = 2;
    required uint32 type = 3;
}

message GetSingleSettingResp{
    repeated Setting setting_list = 1;
}

message GetSettingsBySingleKeyReq{
    repeated uint32 uid_list = 1;
    required string key = 2;
    required uint32 type = 3;
}

message FullSettingInfo{
    required uint32 uid = 1;
    required string key = 2;
    required uint32 type = 3;
    required string value = 4;
}

message GetSettingsBySingleKeyResp{
    repeated FullSettingInfo mul_full_setting_list = 1;
}

service UserSettings{
    option( tlvpickle.Magic ) = 15160;

    rpc SetSettings(  SetSettingsReq) returns ( SetSettingsResp ) {
		option( tlvpickle.CmdID ) = 1;										// 命令号
	    option( tlvpickle.OptString ) = "u:g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <group_id>";	// 测试工具的命令号帮助	
    }

    rpc GetMultiSettingsByKey ( GetMultiSettingsByKeyReq ) returns ( GetMultiSettingsByKeyResp ) {
		option( tlvpickle.CmdID ) = 2;										// 命令号
	    option( tlvpickle.OptString ) = "u:g:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uids> -g <group_ids>";	// 测试工具的命令号帮助	
	}

   	rpc GetMultiSettingsByType ( GetMultiSettingsByTypeReq ) returns ( GetMultiSettingsByTypeResp ) {
		option( tlvpickle.CmdID ) = 3;										// 命令号
	    option( tlvpickle.OptString ) = "u:g:b";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <group_id> -b <is_receive>";	// 测试工具的命令号帮助	
	}

   	rpc GetSingleSetting ( GetSingleSettingReq ) returns ( GetSingleSettingResp ) {
		option( tlvpickle.CmdID ) = 4;										// 命令号
	    option( tlvpickle.OptString ) = "u:k:t";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -g <key_list> -b <type>";	// 测试工具的命令号帮助	
	}   	

    rpc GetSettingsBySingleKey ( GetSettingsBySingleKeyReq ) returns ( GetSettingsBySingleKeyResp ) {
		option( tlvpickle.CmdID ) = 5;										// 命令号
	    option( tlvpickle.OptString ) = "u:k:t";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid_list> -g <key> -b <type>";	// 测试工具的命令号帮助	
	}
}

