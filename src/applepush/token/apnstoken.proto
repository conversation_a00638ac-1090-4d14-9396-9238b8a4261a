syntax="proto2";

// svrkit support
import "common/tlvpickle/skbuiltintype.proto";
import "src/applepush/apns/apns.proto";

// namespace
package Apns;

message UnregisterDeviceTokenReq {
    required string token       = 1;
    required uint32 timestamp   = 2;
}

message QueryDeviceTokensReq {
	repeated uint32 app_id_list = 1;
	repeated uint32 uid_list	= 2;
}

message QueryDeviceTokensResp {
	repeated UserDeviceToken user_device_token_list = 1;
}

message FullScanDeviceTokenContext {
    optional UserDeviceToken last_device_token  = 1;
}

message FullScanDeviceTokenReq {
    required FullScanDeviceTokenContext current_context = 1;
    repeated uint32 app_id_list = 2;
    required uint32 max_token_count = 3;
}

message FullScanDeviceTokenResp {
    required FullScanDeviceTokenContext next_context = 1;
    repeated UserDeviceToken user_device_token_list = 2;
    required bool finished = 3;
}

service ApnsToken {
	/**
	 * Server listening port
	 */
	option( tlvpickle.Magic ) = 15151;

	/**
	 * Register a device token
	 */
	rpc RegisterDeviceToken( DeviceToken ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 1;
		option( tlvpickle.OptString ) = "u:t:";
		option( tlvpickle.Usage ) = "-u <uid> -t <token_hex>";
	}

	/**
	 * Unregister a device token
	 */
	rpc UnregisterDeviceToken( UnregisterDeviceTokenReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 2;
		option( tlvpickle.OptString ) = "u:t:";
		option( tlvpickle.Usage ) = "-u <uid> -t <token_hex>";
	}

	/**
	 * Query device tokens by uid list and app id list
	 */
	rpc QueryDeviceTokens( QueryDeviceTokensReq ) returns (QueryDeviceTokensResp ) {
		option( tlvpickle.CmdID) = 3;
		option( tlvpickle.OptString ) = "u:a:";
		option( tlvpickle.Usage ) = "-u <uid_list> -a <app_id_list>";
	}

    rpc FullScanDeviceToken( FullScanDeviceTokenReq ) returns ( FullScanDeviceTokenResp ) {
        option( tlvpickle.CmdID) = 4;
        option( tlvpickle.OptString ) = "k:";
        option( tlvpickle.Usage ) = "-k <batch_count>";
    }
}
