syntax="proto2";


// namespace
package Apns;

message DeviceToken {
    required string token		= 1;
    required string device_id	= 2;
    required uint32 timestamp	= 3;
    required uint32 os_type		= 4;
    required string os_ver		= 5;
    required uint32 app_id		= 6;
    required uint32 app_ver		= 7;
    optional string sound		= 8;
    optional string voip_sound	= 9;
    optional string bundle_id   = 10;
}

message UserDeviceToken {
	required uint32 uid = 1;
	required uint32 app_id = 2;
	required DeviceToken device_token = 3;
}

message Localizable {
    required string key = 1;
    repeated string args = 2;
}

message AlertDict {
    required string         body = 1;
    optional string         title = 2;          // >= iOS8.2 required, for Apple Watch only
    optional Localizable    title_loc = 3;      // >= iOS8.2 required, for Apple Watch only
    optional string         action_loc_key = 4;
    optional Localizable    message_loc = 5;
    optional string         launch_image = 6;
}

message Alert {
    optional string         alert_simple = 1;
    optional AlertDict      alert_dict = 2;
}

message Notification {
    optional string category = 1;
    required uint32 seq = 2;
    optional uint32 time = 3;
    required Alert	alert = 4;
    optional uint32	badge = 5;
    optional uint32	expiry = 6;
    optional string	ext = 7;			// JSON ext
    required uint32 sound_type = 8;
    optional bool content_available = 9;
    optional string thread_id = 10;
    optional string sound = 11;
}

enum SOUND_TYPE {
	NO_SOUND	 	= 0;
	MSG_SOUND		= 1;
	VOIP_SOUND		= 2;
}
