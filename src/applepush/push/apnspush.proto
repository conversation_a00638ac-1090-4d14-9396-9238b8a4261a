syntax="proto2";

// svrkit support
import "common/tlvpickle/skbuiltintype.proto";
import "src/applepush/apns/apns.proto";

// namespace
package Apns;

// 单推
message SinglecastNotification {
	required uint32 uid = 1;
    // required string from_account = 2;
    repeated uint32 target_app_list = 3;	// you have to specify at least one app (id)
    required Notification body = 4;
    optional uint32 frequence = 5;
}

message MulticastNotification {
    required string multicast_account = 1;
    repeated uint32 exclude_uid_list = 2;
    repeated uint32 target_app_list = 3;	// you have to specify at least one app (id)
    required Notification body = 4;
    optional uint32 frequence = 5;
}

message BroadcastNotification {
    required string from_account = 1;
    repeated uint32 target_app_list = 2;	// you have to specify at least one app (id)
    required Notification body = 3;
}

// 指向性推送, 一般用于特殊用途...(比如踢下线)
message DirectiveNotification {
    required UserDeviceToken user_device_token = 1;
    required Notification body = 4;
}

//////////////////////////////////////////////////////////////////
// Mulicast Relationship
//////////////////////////////////////////////////////////////////

message UpdateMulticastRelationReq {
    enum CMD {
        CMD_Register = 1;
        CMD_Unregister = 2;
        CMD_Clean = 3;
    }
    required uint32 cmd = 1;            // defined above
    repeated uint32 uid_list = 2;
    repeated string account_list = 3;   // list of multicast accounts
}

message UpdateMulticastRelationResp {
}

message QueryUserSubscribingAccountsReq {
    required uint32 uid = 1;
}

message QueryUserSubscribingAccountsResp {
    repeated string account_list = 1;
}

message QueryAccountSubscribersReq {
    required string account = 1;
}

message QueryAccountSubscribersResp {
    repeated uint32 uid_list = 2;
}

// For internal use
message QueueNotification {

    optional SinglecastNotification singlecast_notification = 1;
    optional MulticastNotification multicast_notification = 2;
    optional BroadcastNotification broadcast_notification = 3;

    optional DirectiveNotification directive_notification = 4;
}

message FrequenceData {
    required uint32 last_apns_enqueue_timestamp = 1;
    required uint32 last_apns_enqueue_seq = 2;
}

service ApnsPush {
	/**
	 * Server listening port
	 */
	option( tlvpickle.Magic ) = 15155;

	rpc PushNotification( SinglecastNotification ) returns (tlvpickle.SKBuiltinEmpty_PB) {
		option( tlvpickle.CmdID ) = 1;
		option( tlvpickle.OptString ) = "a:b:e:u:q:x:t:";
		option( tlvpickle.Usage ) = "-u <uid> -a <alert> -t <title> -b <badge> -e <expire> -q <seq> -x <ext>";
	}

    rpc PushMulticastNotification( MulticastNotification ) returns (tlvpickle.SKBuiltinEmpty_PB) {
		option( tlvpickle.CmdID ) = 2;
		option( tlvpickle.OptString ) = "a:b:e:m:q:x:";
		option( tlvpickle.Usage ) = "-m <multicast_account> -a <alert> -b <badge> -e <expire> -q <seq> -x <ext>";
	}

    rpc PushBroadcastNotification( BroadcastNotification ) returns (tlvpickle.SKBuiltinEmpty_PB) {
		option( tlvpickle.CmdID ) = 3;
		option( tlvpickle.OptString ) = "b:e:a:q:x:";
		option( tlvpickle.Usage ) = "-a <alert> -b <badge> -e <expire> -q <seq> -x <ext>";
	}

    rpc PushDirectiveNotification( DirectiveNotification ) returns (tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "t:b:e:m:q:x:";
        option( tlvpickle.Usage ) = "-t <token_hex> -a <alert> -b <badge> -e <expire> -q <seq> -x <ext>";
    }

    rpc UpdateMulticastRelation( UpdateMulticastRelationReq ) returns( UpdateMulticastRelationResp ) {
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "o:u:m:";
        option( tlvpickle.Usage ) = "-o <command> -u <uid> -a <multicast_account>";
    }

    rpc QueryUserSubscribingAccounts( QueryUserSubscribingAccountsReq ) returns( QueryUserSubscribingAccountsResp ) {
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc QueryAccountSubscribers( QueryAccountSubscribersReq ) returns( QueryAccountSubscribersResp ) {
        option( tlvpickle.CmdID ) = 13;
        option( tlvpickle.OptString ) = "m:";
        option( tlvpickle.Usage ) = "-m <multicast_account>";
    }
}
