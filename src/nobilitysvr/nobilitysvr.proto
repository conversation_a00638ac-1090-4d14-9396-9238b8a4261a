syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package NobilitySvr;

enum KfkWorkerType
{
    PresentWorker = 1;
    CatFishWoker = 2;
}

enum InvisibleType
{
    ENUM_NotInvisible = 0;
    ENUM_Invisible = 1;    
}

enum NobilityLevelType
{
    Nobility_Level_Invalid = 0;
    Nobility_Level_Baron = 1;  // 男爵
    Nobility_Level_Viscount = 2;  // 子爵
    Nobility_Level_Earl = 3;  // 伯爵
    Nobility_Level_Marquis = 4;  // 侯爵
    Nobility_Level_Duke = 5;  // 公爵
    Nobility_Level_Commandery_Prince = 6;  // 郡王
    Nobility_Level_Prince = 7;  // 亲王
    Nobility_Level_King = 8;  // 国王
    Nobility_Level_God = 9;  // 神王
    Nobility_Level_Young = 10;  //少爵
}

message NobilityRecored
{
    required uint32 ts = 1;
    required uint32 level = 2;
}

// 贵族待充值差额
message NoilityInvestInfo
{
    optional uint32 level = 1;
    optional int64 gap_value = 2;   // 距离等级待充值数额
}

message NobilityInfo
{
    required uint64 value = 1; //贵族值
    required uint64 keep_value = 2;
    required uint32 level = 3; //贵族等级
    required uint32 cycle_ts = 4; //重置时间戳
    required uint32 uid = 5;
    optional bool   invisible = 6; //隐身true 
    optional uint32 wait_cost_value = 7; //待消费金额
    optional uint32 total_wait_cost_value = 8; //待消费总金额
    optional string level_name = 9; //贵族等级名
    repeated NoilityInvestInfo invest_info_list = 10;   // 距离等级待充值数额
    optional uint32 temp_nobility_remain_ts = 11;   // 限时体验贵族剩余时间
    optional uint32 real_level = 12; // 实际贵族等级,限时体验贵族有体验等级
    optional uint32 extent_cnt = 13; //已经延长次数
    optional uint32 lack_extent_val = 14; //还差多少触发下次延长
    optional float f_level = 15;  // 浮点数贵族等级
    optional uint64 level_value = 16; //等级贵族值，可以用来进行贵族大小比较
    optional uint32 real_id = 17;  // 贵族实际唯一标识id(不判断体验贵族id)
    optional float real_f_level = 18;  // 实际浮点数贵族等级
}

message GetNobilityInfoReq
{
    optional bool need_recoreds = 1; //是否需要等级记录
    optional string source = 2; //请求来源
    optional uint32 uid = 3; //uid
}

message GetNobilityInfoResp
{
    required NobilityInfo info = 1;
    repeated NobilityRecored records = 2; //等级记录
}

message BatchGetNobilityInfoReq
{
    repeated uint32 uids = 1;
}

message BatchGetNobilityInfoResp
{
    repeated NobilityInfo nobility_infos = 1;
}

message SetInvisibleStatusReq
{
    required int32 status = 1; //enum InvisibleType
    optional uint32 status_type = 2; //0 全局，1临时
}

message SetInvisibleStatusResp
{
    required int32 status = 1; //设置完成后服务端当前的状态
}

message GetInvisibleStatusReq
{
}

message GetInvisibleStatusResp
{
    required int32 status = 1;//enum InvisibleType
}

message BatchGetInvisibleStatusReq
{
    repeated uint32 uids = 1;
}

message BatchGetInvisibleStatusResp
{
    repeated bool visible_status_list = 1; //隐身状态
}

message GetTrumpetLeftCntReq
{
    required uint32 privilege_id = 1;
    required uint32 cycle_ts = 2;
}

message GetTrumpetLeftCntResp
{
    optional uint32 left_cnt = 1;
}

message ReduceTrumpetLeftCntReq
{
    required uint32 privilege_id = 1;
    required uint32 cycle_ts = 2;
}

message ReduceTrumpetLeftCntResp
{
    optional uint32 left_cnt = 1;
}

message NobilityNotifyMsg
{
    required uint32 ty = 1;
    required uint32 uid = 2;
    required uint32 level = 3;
    optional uint32 day = 4;
    optional uint32 left = 5;
}

message AddRechargeReq
{
    required uint32 add_value = 1;
    required uint32 goal_level = 2;
    optional uint32 uid = 3;
}

message AddRechargeResp
{
    
}

message GetRechargeResultInfoReq
{
    required uint32 add_value = 1;
    required uint32 goal_level = 2;
    optional uint32 uid = 3;
}

message GetRechargeResultInfoResp
{
    required NobilityInfo curr_info = 1; //增加前的信息
    required NobilityInfo result_info = 2; //增加后的信息
}

message AddUserNobilityValueReq
{
    required uint32 uid = 1;
    required uint64 value = 2;
    required string order_id = 3;
    required string source = 4;
}
message AddUserNobilityValueResp
{  
}

message GetMemberNobilityInfoReq
{
    required uint32 uid = 1;
    required uint32 channel_id = 2;
}

message GetMemberNobilityInfoResp
{
    required uint32 nobility_level = 1;
    required bool   invisible = 2;
}

message PushNobilityLevelUpMsgReq
{
    required uint32 uid = 1;
    required string level_name = 2;
    required uint32 channel_id = 3;
    required uint32 level = 4;
    optional uint32 trigger = 5;
}

message PushNobilityLevelUpMsgResp
{
}

message AddRechargeTbeanReq {
    required uint32 uid = 1;
    required int64  num = 2;
    required string orderId = 3;
    required string chargeType = 4;
    required string payChannel = 5;
    required string chargeTime = 6;
}
message AddRechargeTbeanResp {
}

// 限时体验贵族信息
message TempNobilityInfo {
   optional uint32 uid = 1;
   optional uint32 begin_ts = 2;  // 体验开始时间
   optional uint32 end_ts = 3;   // 体验结束时间
   optional uint32 update_ts = 4;  // 更新时间
   optional uint32 level = 5;  // 开通的等级
   optional uint32 id = 6;  // db 自增id
}

// 奖励配置
message TempNobilityAwardInfo {
   optional string award_img_url = 1; // 图片链接
   optional string award_name = 2;  // 名称
   optional uint32 cnt = 3;  // 现在所有类型奖励都是天数
}

// 开通限时体验贵族
message AddTempNobilityReq {
   optional uint32 uid = 1;
   optional uint32 begin_ts = 2; // 体验开始时间
   optional uint32 level = 3;   // 开通等级
}
message AddTempNobilityResp {
}

// 获取限时体验贵族列表
message GetTempNobilityListReq {
   optional bool is_valid = 1;   // true : 处于有效期的  false ：获取全部
}
message GetTempNobilityListResp {
   repeated TempNobilityInfo info_list = 1;
   repeated TempNobilityAwardInfo award_info_list = 2;   // 奖励配置
}

message GetConsumeOrderCountReq{
  optional uint32 begin_ts = 1;
  optional uint32 end_ts = 2;
}
message GetConsumeOrderCountResp{
  optional uint32 count = 1;
  optional uint32 value = 2; //总价值
}

message GetConsumeOrderListReq{
  optional uint32 begin_ts = 1;
  optional uint32 end_ts = 2;
}
message GetConsumeOrderListResp{
  repeated string order_list = 1;
}

message FixConsumeOrderReq {
  optional string order_id = 1;
  optional uint32 uid = 2;
  optional uint32 incr_value = 3;
  optional uint32 consume_ts = 4;
}
message FixConsumeOrderResp {
}


service NobilitySvr {
    option( tlvpickle.Magic ) = 15699;      // 服务监听端口号

    rpc GetNobilityInfo ( GetNobilityInfoReq ) returns ( GetNobilityInfoResp )
    {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc BatchGetNobilityInfo( BatchGetNobilityInfoReq ) returns ( BatchGetNobilityInfoResp )
    {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc SetInvisibleStatus( SetInvisibleStatusReq ) returns ( SetInvisibleStatusResp )
    {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:a:";
        option( tlvpickle.Usage ) = "-u <uid> -a <status>";
    }

    rpc GetInvisibleStatus( GetInvisibleStatusReq ) returns ( GetInvisibleStatusResp )
    {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc BatchGetInvisibleStatus( BatchGetInvisibleStatusReq ) returns ( BatchGetInvisibleStatusResp )
    {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc GetTrumpetLeftCnt( GetTrumpetLeftCntReq ) returns ( GetTrumpetLeftCntResp )
    {
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc ReduceTrumpetLeftCnt( ReduceTrumpetLeftCntReq ) returns ( ReduceTrumpetLeftCntResp )
    {
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }
    
    rpc AddRecharge( AddRechargeReq ) returns ( AddRechargeResp )
    {
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "u:r:l:";
        option( tlvpickle.Usage ) = "-u <uid> -r<recharge> -l <goalLevel>";
    }

    rpc GetRechargeResultInfo( GetRechargeResultInfoReq ) returns ( GetRechargeResultInfoResp )
    {
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "u:r:l:";
        option( tlvpickle.Usage ) = "-u <uid> -r<recharge> -l <goalLevel>";
    }

    rpc AddUserNobilityValue( AddUserNobilityValueReq ) returns ( AddUserNobilityValueResp )
    {
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "u:l:o:s:";
        option( tlvpickle.Usage ) = "-u <uid> -l <value> -o <orderId> -s <source>";
    }

    //设置房间临时隐身状态
    rpc GetMemberNobilityInfo( GetMemberNobilityInfoReq ) returns( GetMemberNobilityInfoResp ){
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "x:u:";
        option( tlvpickle.Usage ) = "-x <channel id> -u <uid>";
    }

    rpc PushNobilityLevelUpMsg( PushNobilityLevelUpMsgReq ) returns( PushNobilityLevelUpMsgResp ){
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "u:x:l:a:t:";
        option( tlvpickle.Usage ) = "-u <uid> -x <channelid> -l<level> -a <level name> -t <trigger>";
    }

    rpc AddRechargeTbean( AddRechargeTbeanReq ) returns( AddRechargeTbeanResp ){
        option( tlvpickle.CmdID ) = 13;
        option( tlvpickle.OptString ) = "u:x:l:a:t:";
        option( tlvpickle.Usage ) = "-u <uid> -x <channelid> -l<level> -a <level name> -t <trigger>";
    }//

    rpc AddTempNobility(AddTempNobilityReq) returns (AddTempNobilityResp) {
        option( tlvpickle.CmdID ) = 14;
        option( tlvpickle.OptString ) = "u:b:l:";
        option( tlvpickle.Usage ) = "-u <uid> -x <begin_ts> -l <level>";
    }

    rpc GetTempNobilityList(GetTempNobilityListReq) returns (GetTempNobilityListResp) {
       option( tlvpickle.CmdID ) = 15;
       option( tlvpickle.OptString ) = "u:";
       option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc GetConsumeOrderCount(GetConsumeOrderCountReq) returns (GetConsumeOrderCountResp) {
       option( tlvpickle.CmdID ) = 16;
       option( tlvpickle.OptString ) = "u:";
       option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc GetConsumeOrderList(GetConsumeOrderListReq) returns (GetConsumeOrderListResp) {
       option( tlvpickle.CmdID ) = 17;
       option( tlvpickle.OptString ) = "u:";
       option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc FixConsumeOrder(FixConsumeOrderReq) returns (FixConsumeOrderResp) {
       option( tlvpickle.CmdID ) = 18;
       option( tlvpickle.OptString ) = "u:";
       option( tlvpickle.Usage ) = "-u <uid>";
    }
}
