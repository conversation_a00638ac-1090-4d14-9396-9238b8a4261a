syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package svrkittest;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

message SimpleTestReq {
}

message SimpleTestResp {
}

// Anti服务
service SvrkitTest {
    option( tlvpickle.Magic ) = 11999;      // 服务监听端口号

    rpc SimpleTest( SimpleTestReq ) returns( SimpleTestResp ) {
        option( tlvpickle.CmdID ) = 1;              // 命令号
        option( tlvpickle.OptString ) = "";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
    }

}

