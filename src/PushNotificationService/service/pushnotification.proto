syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";
import "src/applepush/apns/apns.proto";

package PushNotificationService;

message ProxyNotification {
    enum Type {
        NOTIFY = 1;                 // 使用CMD_Notify进行推送, 仅限TT使用(appId将被忽略), 暂时还不支持
        PUSH = 2;                   // 使用CMD_Push进行推送, 仅限TT使用(appId将被忽略)
        TRANSMISSION_PUSH = 3;      // 使用CMD_TransmissionPush透传, 并用TransmissionPacket对payload进行包装
    }

    required uint32 type = 1;
    required bytes  payload = 2;
}

message CompositiveNotification {
    required uint32             sequence = 1;           // sequence id for this push operation
    repeated uint32             terminal_type_list = 2; // you have to specify at least one terminal
    optional uint32             app_id = 3;             // app id
    optional ProxyNotification  proxy_notification = 4; // tt proxy notification
    optional Apns.Notification  apns_notification = 5;  // APNs notification
    optional uint32             hash_id = 6;            // notifications with same hash id will be pushed sequently(for proxy notifications only)
    optional uint32             apns_frequence = 7;
}

message MulticastAccount {
    required uint64 id = 1;
    required string account = 2;
}

message PushNotificationWithUidListReq {
    repeated uint32 uid_list = 1;
    required CompositiveNotification notification = 2;
}

message PushNotificationWithMulticastRelationReq {
    required MulticastAccount multicast_account = 1;
    required CompositiveNotification notification = 2;
    repeated uint32 skip_uids = 3;
}

service PushNotification {
    option( tlvpickle.Magic ) = 10000;

    rpc PushNotificationWithUidList( PushNotificationWithUidListReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc PushNotificationWithMulticastRelation( PushNotificationWithMulticastRelationReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
}
