syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package appconfig;

enum AdvertType {
    Unknow = 0;
	Top_Lable = 1;
	Advert = 2;
	Fun_Enter = 3;
	Raid_Riches = 4;	//不再使用
	Custom_Entry = 5;	//代替Raid_riches
    Game_Tab_Ad = 6;    //游戏标签浮层广告
    Float_Layer = 7;    ///浮层图标合集
    Msg_Page_Advert = 8; //消息页顶部活动入口
	Common_Effect_Source = 9;	// 通用的特效资源配置
	Channel_Background = 10; //房间背景配置
}

enum FloatLayerType{
    CHANNEL = 1;                  // 房间普通活动类型
    CHANNEL_HUNT_MONSTER = 2;     // 房间打龙活动类型          丢弃
    CHANNEL_HUNT_MONSTER_V2 = 3;  // 新的房间打龙活动类型
}

message GamePageAdvert {
	optional string name = 1;
	optional string url = 2;
	optional string icon = 3;
}

message SetGamePageTopLableReq {
	repeated GamePageAdvert top_lable_list = 1;
}

message GetGamePageTopLableResp {
	repeated GamePageAdvert top_lable_list = 1;
}

message SetGamePageEnterReq {
	repeated GamePageAdvert enter_list = 1;
}

message GetGamePageEnterResp {
	repeated GamePageAdvert enter_list = 1;
}

message SetGamePageAdvertReq {
	repeated GamePageAdvert main_adv_list = 1;
	repeated GamePageAdvert vice_adv_list = 2;
}

message GetGamePageAdvertResp {
	repeated GamePageAdvert main_adv_list = 1;
	repeated GamePageAdvert vice_adv_list = 2;
}

message SetGamePageGrayUidsReq {
	required string uids = 1;
	required AdvertType g_type = 2;
}

message GetGamePageGrayUidsReq {
	required AdvertType g_type = 1;
}

message GetGamePageGrayUidsResp {
	required string uids = 1;
}

message GetUpdateTimeReq {
	required AdvertType advert_type = 1;
}

message GetUpdateTimeResp {
	required uint32 update_time = 1;
}

message RaidRichesConfig{
    required string url             = 1;    // 活动入口跳转链接
    required string title           = 2;    // 活动标题
    optional string sub_title       = 3;    // 活动副标题, reserved
    required uint64 begin_time      = 4;    // 活动开始时间
    required uint64 end_time        = 5;    // 活动结束时间
    repeated uint32 uid_suffix		= 6;	// 生效的uid后缀
}

enum CUSTOM_ENTRY_TYPE
{
    ME_TAB = 1;            // '我'页面
}

message CustomEntryInfo{
    required string url             = 1;    // 活动入口跳转链接
    required string title           = 2;    // 活动标题
    optional string sub_title       = 3;    // 活动副标题, reserved
    required uint64 begin_time      = 4;    // 活动开始时间
    required uint64 end_time        = 5;    // 活动结束时间
    repeated uint32 uid_suffix		= 6;	// 生效的uid后缀
    optional string icon 			= 7;	// icon
    optional uint32 client_type		= 8;    // 0-android, 1-ios
    optional uint32 entry_type 		= 9; 	// 入口类型，扩展用
    optional string android_version = 10;    // android高于此版本不显示
    optional string ios_version     = 11;   // ios高于此版本不显示
}

message CustomEntryList{
	repeated CustomEntryInfo entry_list = 1;
}

message GameTabAdEntry{
    required uint32 act_id = 1;
    required string act_url = 2;
    required string game_tab_img = 3;       //游戏标签页浮层图片
    required string guild_mgroup_img = 4;   //公会总群浮层图片
    required uint64 begin_time      = 5;    // 活动开始时间(UNIX TIMESTAMP)
    required uint64 end_time        = 6;    // 活动结束时间
    required bool is_valid          = 7;    // 是否生效
}

message GameTabAdList{
    repeated GameTabAdEntry entry_list = 1;
}

message ChannelBackground{
    required string background_url  = 1;    // 房间背景图url
    required uint64 begin_time      = 2;    // 开始时间(UNIX TIMESTAMP)
    required uint64 end_time        = 3;    // 结束时间
}

message ChannelBackgroundList{
    repeated ChannelBackground background_list = 1;
}

// rush限流机制相关参数，有些活动需要配上
message RushInfo {
   optional uint32 type = 1;  // see ga_base.proto RushType
   optional uint32 rush_max_rand_ts = 2; //rush 最大随机值 秒
   optional uint32 rush_wait_ts = 3; //  rush 最大等待时间 秒
}

message FloatLayerEntry{
    required string img             = 1;
    required string url             = 2;
    required uint32 type            = 3;
    required uint64 begin_time      = 4;    // 开始时间(UNIX TIMESTAMP)
    required uint64 end_time        = 5;    // 结束时间 
    optional uint32 dot_type	    = 6;	// sync_.proto里的FloatLayerDotType）
    optional uint32 dot_value	    = 7;	// 例如dot_type是DAYS的话，每dot_value天出现一次红点
    optional string top_img	        = 8;	// 入口图标上方的小图标的url
    optional uint32 app_id          = 9;
    optional uint32 market_id       = 10;   
    optional uint32 platform        = 11;    // 客户端平台类型 0 all, 0x1 android, 0x2 ios 0x5 pc
    optional uint32 display_channel_type = 12;   //see sync_.proto EFloatLayerChannelType
    optional RushInfo rush_info = 13;       // rush限流机制相关参数，有些活动需要配上
}

message FloatLayerList{
    repeated FloatLayerEntry entry_list = 1;
}

message MsgPageAdvertEntry{
    required uint32 id              = 1;
    required string img             = 2;
    required string url             = 3;
    required string text            = 4;
    required uint64 begin_time      = 5;    // 开始时间(UNIX TIMESTAMP)
    required uint64 end_time        = 6;    // 结束时间
    optional uint32 platform        = 7;    // 客户端平台类型 0 all, 0x1 android, 0x2 ios 0x3 pc 
    repeated string include_channel = 8;    // 生效的渠道, 优先级大于exclude
    repeated string exclude_channel = 9;    // 不生效的渠道号
    optional bool is_newbie_entry   = 10;
    optional uint32 app_id          = 11;
    optional uint32 market_id       = 12; 
}

message MsgPageAdvertList{
    repeated MsgPageAdvertEntry entry_list = 1;
}

message GetAppConfigReq{
    required AdvertType advert_type = 1;
}

message GetAppConfigDetailResp
{
	required bytes detail = 1;
}

message SetAppConfigDetailReq{
	required AdvertType advert_type = 1;
	required bytes detail = 2;
}

message CommonEffectSource{
	required uint32 type = 1;	// see DownloadSourceInfo::DOWNLOAD_SOURCE_TYPE
	required string url = 2;
	required string md5 = 3;
	optional uint32 id = 4;
}

message CommonEffectSourceList{
    repeated CommonEffectSource entry_list = 1;
}

enum ClientAuditType {
	Pass = 0;	// 已通过
	Auditing = 1;	// 审核中
}

message ClientAuditInfo{
	required uint32 client_type = 1;	// 1.ios
	required uint32 client_version = 2;	// 版本号
	required uint32 audit_status = 3;	// ClientAuditType
}

// ios审核信息
message SetIosAuditInfoReq{
	required uint32 client_version = 1;	// 版本号
	required uint32 audit_status = 2;	// ClientAuditType
}

message GetIosAuditInfoResp{
	required ClientAuditInfo audit_info = 1;
}

message ConfigKV{
    required string key = 1;
    required string value = 2;
}

message ConfigData {
    repeated ConfigKV config_kv_list = 1;
}

message MainPageTagConfig {
    repeated ConfigKV config_kv_list = 1; //主页tag配置KV对
    required uint32 expiration_time = 2; //主页tag配置过期时间戳
    required uint32 begin_time = 3; //生效时间 。 配置在 begin_time -> expiration_time时间段中有效。
}

message ConfigInfo{                             //一个config_type一个时间段的配置。
    required uint32 config_type = 1;            //配置类型
    required uint32 config_version = 2;         //配置最新版本时间戳
    required bytes config_data = 3;             //配置二进制数据 -> 对应到客户端的结构
    required uint32 begin_time = 4;             //开始时间
    required uint32 expiration_time = 5;     //结束时间
}

message ConfigRangeInfoList { //对应一个config_type，可以配置多个时间段的配置，每个时间段一个ConfigInfo结构。
    required uint32 config_type = 1;
    required uint32 config_version = 2; //配置最新版本时间戳
    repeated ConfigInfo config_info_list = 3; //
}

message GetSyncConfigV2AllVersionReq {
}

message GetSyncConfigV2AllVersionResp {
    repeated ConfigKV version_list = 1;
}

message GetSyncConfigV2InfoByKeyListReq {
    repeated uint32 config_type_list = 1;
}

message GetSyncConfigV2InfoByKeyListResp {
    repeated ConfigInfo config_info_list = 1;
} 

message StrReq 
{
    required uint32 config_type = 1;
}

message CodeResp
{
    required uint32 code = 1;
}

// 获取当前 各种送礼/收礼活动通用的 排行榜黑名单用户列表 
// 该接口数据是从配置中心SDK获取，因此该接口只提供给WEB服务来获取数据
// 其他服务 可以直接使用通用配置中心SDK来获取黑名单数据 (src/commConfigCenterSDK/rankblacklist/RankBlackUidListWatcher.h)
message GetRankBlackUidListReq
{

}
message GetRankBlackUidListResp
{
    repeated uint32 recv_rank_uid_list = 1;
    repeated uint32 send_rank_uid_list = 2;
}

service AppConfigSvr{
    option( tlvpickle.Magic ) = 15350;

	rpc SetGamePageTopLable ( SetGamePageTopLableReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 1;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
    }

	rpc GetGamePageTopLable ( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetGamePageTopLableResp ) {
        option( tlvpickle.CmdID ) = 2;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
    }

	rpc SetGamePageEnter ( SetGamePageEnterReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 3;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
    }

	rpc GetGamePageEnter ( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetGamePageEnterResp ) {
        option( tlvpickle.CmdID ) = 4;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助	
    }

	rpc SetGamePageAdvert ( SetGamePageAdvertReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 5;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
    }

	rpc GetGamePageAdvert ( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetGamePageAdvertResp ) {
        option( tlvpickle.CmdID ) = 6;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
    }

	rpc SetGamePageGrayUids ( SetGamePageGrayUidsReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 7;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
    }

	rpc GetGamePageGrayUids ( GetGamePageGrayUidsReq ) returns ( GetGamePageGrayUidsResp ) {
        option( tlvpickle.CmdID ) = 8;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
    }

	rpc GetUpdateTime ( GetUpdateTimeReq ) returns ( GetUpdateTimeResp ) {
        option( tlvpickle.CmdID ) = 9;										// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
    }

    rpc GetAppConfigDetail(GetAppConfigReq) returns(GetAppConfigDetailResp)
    {
        option( tlvpickle.CmdID ) = 10;										// 命令号
	    option( tlvpickle.OptString ) = "t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<type>";	// 测试工具的命令号帮助
    }

    rpc SetAppConfigDetail(SetAppConfigDetailReq) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
		option( tlvpickle.CmdID ) = 11;									// 命令号
	    option( tlvpickle.OptString ) = "t:u:l:i:s:e:x:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<type> -u<url> -l<title> -i<sub_title> -s<start_time> -e<end_time> -x<uid_suffix(e.g. 521,67,9)>"; // 测试工具的命令号帮助    
    }

    rpc AddCustomEntry(CustomEntryInfo) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
		option( tlvpickle.CmdID ) = 12;									// 命令号
	    option( tlvpickle.OptString ) = "u:l:i:s:e:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<url> -l<title> -i<sub_title> -s<start_time> -e<end_time> "; // 测试工具的命令号帮助       
    }

    rpc GetCustomEntry(tlvpickle.SKBuiltinEmpty_PB) returns(CustomEntryList)
    {
		option( tlvpickle.CmdID ) = 13;									// 命令号
	    option( tlvpickle.OptString ) = "";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = ""; // 测试工具的命令号帮助       
    }

    rpc RemoveCustomEntry(tlvpickle.SKBuiltinEmpty_PB) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
		option( tlvpickle.CmdID ) = 14;									// 命令号
	    option( tlvpickle.OptString ) = "";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = ""; // 测试工具的命令号帮助       
    }

    rpc AddGameTabAd(GameTabAdEntry) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 15;                                 // 命令号
        option( tlvpickle.OptString ) = "a:u:m:n:s:e:";           // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-a<act_id> -u<url> -m<big_img> -n<small_img> -s<start_time> -e<end_time> "; // 测试工具的命令号帮助       
    }

    rpc GetGameTabAd(tlvpickle.SKBuiltinEmpty_PB) returns(GameTabAdEntry)
    {
        option( tlvpickle.CmdID ) = 16;                                 // 命令号
        option( tlvpickle.OptString ) = "";         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = ""; // 测试工具的命令号帮助       
    }

    rpc RemoveGameTabAd(tlvpickle.SKBuiltinEmpty_PB) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 17;                                 // 命令号
        option( tlvpickle.OptString ) = "a:";         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-a<act_id>"; // 测试工具的命令号帮助       
    }

    rpc AddFloatLayer(FloatLayerEntry) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 18;                                 // 命令号
        option( tlvpickle.OptString ) = "i:u:t:s:e:d:n:p:m:a:";           // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i<img> -u<url> -t<type> -s<start_time> -e<end_time> -a<appID> m<marketID> [-d <dot_type 0.none 1.once 2.every_n_days> -n <dot_value> -p <top_img>]"; // 测试工具的命令号帮助     
    }

    rpc GetFloatLayer(tlvpickle.SKBuiltinEmpty_PB) returns(FloatLayerList)
    {
        option( tlvpickle.CmdID ) = 19;                                 // 命令号
        option( tlvpickle.OptString ) = "";         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = ""; // 测试工具的命令号帮助       
    }

    rpc DelFloatLayer(tlvpickle.SKBuiltinEmpty_PB) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 20;                                 // 命令号
        option( tlvpickle.OptString ) = "t:";         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<type>"; // 测试工具的命令号帮助       
    }

    rpc AddMsgPageAdvert(MsgPageAdvertEntry) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 21;                                 
        option( tlvpickle.OptString ) = "i:p:u:t:s:e:o:h:n:x:a:m:";           
        option( tlvpickle.Usage ) = "-i<id> -p<img> -u<url> -t<text> -s<startTime> -e<endTime> -o<0 all, 1 android, 2 ios> -h<include_ch> -n<exclude_ch> -x<isNewbie> -a <appid> -m <marketId>";    
    }

    rpc GetMsgPageAdvert(tlvpickle.SKBuiltinEmpty_PB) returns(MsgPageAdvertList)
    {
        option( tlvpickle.CmdID ) = 22;                                 // 命令号
        option( tlvpickle.OptString ) = "";         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = ""; // 测试工具的命令号帮助       
    }

    rpc DelMsgPageAdvert(tlvpickle.SKBuiltinEmpty_PB) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 23;                                 // 命令号
        option( tlvpickle.OptString ) = "i:";         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i<id>"; // 测试工具的命令号帮助       
    }
	
	rpc SetIosAuditInfo(SetIosAuditInfoReq) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 24;                                 // 命令号
        option( tlvpickle.OptString ) = "s:a:";         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-s<ios_version> -a<is_audit>"; // 测试工具的命令号帮助       
    }

	rpc GetIosAuditInfo(tlvpickle.SKBuiltinEmpty_PB) returns(GetIosAuditInfoResp)
    {
        option( tlvpickle.CmdID ) = 25;                                 // 命令号
        option( tlvpickle.OptString ) = "";         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = ""; // 测试工具的命令号帮助       
    }

    rpc MoveCustomEntry(tlvpickle.SKBuiltinEmpty_PB) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 26;                                 // 命令号
        option( tlvpickle.OptString ) = "o:p:";         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-o<orign index> -p<new index>"; // 测试工具的命令号帮助       
    }
	
	rpc AddCommonEffectSource(CommonEffectSource) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 27;                                 // 命令号
        option( tlvpickle.OptString ) = "t:u:m:x:";           // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<type 3.rocket> -x <id> -u<url> -m<md5>"; // 测试工具的命令号帮助     
    }

    rpc GetCommonEffectSource(tlvpickle.SKBuiltinEmpty_PB) returns(CommonEffectSourceList)
    {
        option( tlvpickle.CmdID ) = 28;                                 // 命令号
        option( tlvpickle.OptString ) = "";         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = ""; // 测试工具的命令号帮助       
    }

    rpc DelCommonEffectSource(tlvpickle.SKBuiltinEmpty_PB) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 29;                                 // 命令号
        option( tlvpickle.OptString ) = "t:";         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t<type>"; // 测试工具的命令号帮助       
    }
	
	// 获取所有的 ConfigKEY 对应的 Config版本ID
    rpc GetSyncConfigV2AllVersion ( GetSyncConfigV2AllVersionReq ) returns ( GetSyncConfigV2AllVersionResp )
    {
        option( tlvpickle.CmdID ) = 30;                               
        option( tlvpickle.OptString ) = "u:";   
        option( tlvpickle.Usage ) = "-u<uid>"; 
    }
	
	// 根据 ConfigKEY 获取configV2的数据
    rpc GetSyncConfigV2InfoByKeyList ( GetSyncConfigV2InfoByKeyListReq ) returns (GetSyncConfigV2InfoByKeyListResp) 
    {
        option( tlvpickle.CmdID ) = 31;             
        option( tlvpickle.OptString ) = "u:k:";        
        option( tlvpickle.Usage ) = "-u<u> -k<keys>"; 
    }

    rpc SetSyncConfigObj ( ConfigRangeInfoList ) returns ( CodeResp ) 
    {
        option( tlvpickle.CmdID ) = 32;   
        option( tlvpickle.OptString ) = "k:q:w:e:r:t:y:"; 
        option( tlvpickle.Usage ) = "-k<config_type> -q<k1> -w<v1> -e<k2> -r<v2> -t<k3> -y<v3>"; 
    } 

    rpc DelSyncConfigObj (StrReq ) returns ( CodeResp ) 
    {
        option( tlvpickle.CmdID ) = 33;                                
        option( tlvpickle.OptString ) = "u:k:";        
        option( tlvpickle.Usage ) = "-u<u> -k<keys>"; 
    }

    rpc GetSyncConfigObj ( StrReq ) returns ( ConfigInfo )
    {
        option ( tlvpickle.CmdID ) = 34;
        option( tlvpickle.OptString ) = "u:k:";
        option( tlvpickle.Usage ) = "-u<u> -k<keys>";
    }

    rpc AddChannelBackground(ChannelBackground) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 35;                                 // 命令号
        option( tlvpickle.OptString ) = "u:s:e:";           // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<url> -s<start_time> -e<end_time> "; // 测试工具的命令号帮助       
    }

    rpc GetChannelBackground(tlvpickle.SKBuiltinEmpty_PB) returns(ChannelBackgroundList)
    {
        option( tlvpickle.CmdID ) = 36;                                 // 命令号
        option( tlvpickle.OptString ) = "";         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = ""; // 测试工具的命令号帮助       
    }

    rpc RemoveChannelBackground(tlvpickle.SKBuiltinEmpty_PB) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 37;                                 // 命令号
        option( tlvpickle.OptString ) = "e:";         // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-e<end_time>"; // 测试工具的命令号帮助       
    }

    // 获取当前 各种送礼/收礼活动通用的 排行榜黑名单用户列表
    rpc GetRankBlackUidList( GetRankBlackUidListReq ) returns ( GetRankBlackUidListResp ){
        option( tlvpickle.CmdID ) = 38;
        option( tlvpickle.OptString ) = "";                         
        option( tlvpickle.Usage ) = "";
    }
}


