syntax="proto3";

// ����import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package imrobot;


message CheckAndPullWaitPullLogReq {
  uint32 uid = 1;
}

message CheckAndPullWaitPullLogResp {

}

message UploadLogReq {
  bytes data = 1;
  uint32 client_type = 2;
  uint32 client_version = 3;
}

message UploadLogResp {
}

message AddPullLogOfflineUserReq {
  uint32 uid = 1;
  uint32 fromUserId = 2;
  bytes data = 3;
}

message AddPullLogOfflineUserResp {
}

service ImRobot {
  option(tlvpickle.Magic) = 15703;
  //option( tlvpickle.ServerName ) = "imrobot";

  rpc CheckAndPullWaitPullLog (CheckAndPullWaitPullLogReq) returns (CheckAndPullWaitPullLogResp) {
    option(tlvpickle.CmdID) = 1;
    option(tlvpickle.OptString) = "u:";
    option(tlvpickle.Usage) = "-u <uid>";
  }

  rpc UploadLog (UploadLogReq) returns (UploadLogResp) {
    option(tlvpickle.CmdID) = 2;
    option(tlvpickle.OptString) = "";
    option(tlvpickle.Usage) = "";
  }

  rpc AddPullLogOfflineUser (AddPullLogOfflineUserReq) returns (AddPullLogOfflineUserResp) {
    option(tlvpickle.CmdID) = 3;
    option(tlvpickle.OptString) = "";
    option(tlvpickle.Usage) = "";
  }
}