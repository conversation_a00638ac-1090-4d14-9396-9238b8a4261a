syntax="proto2";

// namespace
package usergeo;

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// 用户位置
message GeoUserInfo {

    required uint32 uid = 1;
	optional double longitude = 2; // 经度
	optional double latitude  = 3; // 纬度
}

message GeoUserExtendInfo {

    optional uint32 channel_id = 1;
    optional uint32 channel_type = 2;
	optional uint32 channel_app = 3;
	optional int32 sex = 4; // 0 女 1 男
	optional bool is_allow_follow = 5; // 是否允许跟随
	optional bool is_channel_pwd = 6;  // 房间是否有密码
}

// 附近的人
message NearUserInfo {

    required uint32 uid = 1;
    required double distance = 2; // 单位米
    
    optional GeoUserExtendInfo extend_info = 3;
	
	optional uint32 last_time = 4; // 上次更新位置的时间 单位秒
}

// 更新 用户地理位置完整信息 
message UpdateUserGeoReq
{
	required GeoUserInfo geo_info = 1;
	optional GeoUserExtendInfo extend_info = 2;
}

message UpdateUserGeoResp
{
}


message FindGeoUserListReq
{
	required GeoUserInfo geo_info = 1;
	
	// 翻页选项 min_distance + begin_uid + count
	
	required double min_distance = 2; // 最小距离 单位米 第一页可以使用0
	required uint32 count = 3;
	
	optional uint32 begin_uid = 4;  // 最小UID，第一页使用0
	optional int32 filter_sex = 5;  // 0 女 1 男 2 全部
}
message FindGeoUserListResp
{
	repeated NearUserInfo user_list = 1;
}

// 单独更新 地理位置扩展信息
message UpdateUserGeoExtendInfoReq
{
	required uint32 uid = 1;
	required GeoUserExtendInfo extend_info = 2;
}
message UpdateUserGeoExtendInfoResp
{

}

// 更新用户的地理信息设置
message UpdateUserGeoSettingReq
{
	required uint32 uid = 1;
	optional bool disable_geo = 2;
}
message UpdateUserGeoSettingResp
{
}

// 获取用户的地理信息设置
message GetUserGeoSettingReq
{
	required uint32 uid = 1;
}
message GetUserGeoSettingResp
{
	optional bool disable_geo = 1; // 是否打开了开关
	
	optional uint32 last_report_geo_time = 2; // 上次客户端上报地理位置信息的时间
}

service usergeo {
	option( tlvpickle.Magic ) = 15392;
	
    rpc UpdateUserGeo ( UpdateUserGeoReq ) returns ( UpdateUserGeoResp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:j:w:";
        option( tlvpickle.Usage ) = "-u <uid> -j <longitude> -w <latitude>";
    }

    rpc FindGeoUserList ( FindGeoUserListReq ) returns ( FindGeoUserListResp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:j:w:m:x:";
        option( tlvpickle.Usage ) = "-u <uid> -j <longitude> -w <latitude> -m <min distance> -x <bgein uid> ";
    }
	
	rpc UpdateUserGeoExtendInfo ( UpdateUserGeoExtendInfoReq ) returns ( UpdateUserGeoExtendInfoResp ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:x:p:s:";
        option( tlvpickle.Usage ) = "-u <uid> -x <channelid> -p <channel type> -s <sex>";
	}
	
	rpc UpdateUserGeoSetting ( UpdateUserGeoSettingReq ) returns ( UpdateUserGeoSettingResp ) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "u:g:";
        option( tlvpickle.Usage ) = "-u <uid> -g <disable_geo>";
    }
	
	rpc GetUserGeoSetting ( GetUserGeoSettingReq ) returns ( GetUserGeoSettingResp ) {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }
}
