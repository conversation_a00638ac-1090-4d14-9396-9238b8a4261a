syntax = "proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package installedgame;

message AddInstalledGameReq {
  uint32 uid = 1;
  uint32 game_id = 2;
  string package = 3;
  string name = 4;
  string version = 5;
}

message AddInstalledGameResp {}

message RemoveInstalledGameReq {
  uint32 uid = 1;
  uint32 game_id = 2;
}

message RemoveInstalledGameResp {}

message GetInstalledGameListReq {
  uint32 uid = 1;
}

message StUserInstalledGame {
  uint32 game_id = 1;
  uint32 last_report_at = 2;
}

message GetInstalledGameListResp {
  repeated StUserInstalledGame games = 1;
}

message InstalledGameInfo {
  uint32 game_id = 1;
  string package = 2;
  string name = 3;
  string version = 4;
}

// 添加用户上报的游戏安装信息 (全量覆盖)
message BatchAddInstalledGameReq {
  uint32 uid = 1;
  repeated InstalledGameInfo game_list = 2;
}

message BatchAddInstalledGameResp {
  uint32 last_game_size = 1;  // 被全量覆盖前 该用户安装游戏的数量
}

// 清除用户上报的游戏安装信息
message ClearInstalledGameReq {
  uint32 uid = 1;
}

message ClearInstalledGameResp {
  uint32 last_game_size = 1;  // 被清除前 该用户安装游戏的数量
}

message GetGameInstalledUserListReq {
  uint32 game_id = 1;
}

message GetGameInstalledUserListResp {
  repeated uint32 uids = 1;
}

service InstalledGame {
  option (tlvpickle.Magic) = 15707;

  rpc AddInstalledGame(AddInstalledGameReq) returns (AddInstalledGameResp) {
    option (tlvpickle.CmdID) = 1;
    option (tlvpickle.OptString) = "u:g:";
    option (tlvpickle.Usage) = "-u <uid> -g <game id>";
  }

  rpc RemoveInstalledGame(RemoveInstalledGameReq) returns (RemoveInstalledGameResp) {
    option (tlvpickle.CmdID) = 2;
    option (tlvpickle.OptString) = "u:g:";
    option (tlvpickle.Usage) = "-u <uid> -g <game id>";
  }

  rpc GetInstalledGameList(GetInstalledGameListReq) returns (GetInstalledGameListResp) {
    option (tlvpickle.CmdID) = 3;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  // 用户上报的游戏安装信息 (全量覆盖)
  rpc BatchAddInstalledGame(BatchAddInstalledGameReq) returns (BatchAddInstalledGameResp) {
    option (tlvpickle.CmdID) = 4;
    option (tlvpickle.OptString) = "u:g:p:";
    option (tlvpickle.Usage) = "-u <uid> -g <game id> -p <pack name>";
  }

  // 清除用户上报的游戏安装信息
  rpc ClearInstalledGame(ClearInstalledGameReq) returns (ClearInstalledGameResp) {
    option (tlvpickle.CmdID) = 5;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  rpc GetGameInstalledUserList(GetGameInstalledUserListReq) returns (GetGameInstalledUserListResp) {
    option (tlvpickle.CmdID) = 6;
    option (tlvpickle.OptString) = "g:";
    option (tlvpickle.Usage) = "-g <game_id>";
  }
}