syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";


package channelolstat;



message GetAllChannelMemberCntReq
{
	required uint32 channeltype = 1;    
}

message GetAllChannelMemberCntResp			
{
	required uint32 member_cnt = 1;  // 满足channel_type限制的全部在线人数   
	required uint32 channel_cnt = 2; // 满足channel_type限制的全部房间数
}

message ChannelOLStatInfo
{
	required uint32 channel_id = 1;
	required uint32 member_cnt = 2;  
}

message GetChannelOLListReq
{
	required uint32 count = 1;  
	required uint32 offset = 2;  
	required uint32 min_member_limit = 3;
	required uint32 channel_type = 4;
}

message GetChannelOLListResp			
{
	repeated ChannelOLStatInfo channel_list = 1; 
	
	optional uint32 all_channel_cnt = 2;   // 满足channel_type和min_member_limit限制的全部房间数
	optional uint32 all_member_cnt = 3;    // 满足channel_type和min_member_limit限制的全部在线人数   
}

service channelolstat
{
	option( tlvpickle.Magic ) = 15206;

	// 获取当前全网房间在线人数
	rpc GetAllChannelMemberCnt(GetAllChannelMemberCntReq)returns( GetAllChannelMemberCntResp){
	option( tlvpickle.CmdID ) = 1;						
    option( tlvpickle.OptString ) = "t:";				
    option( tlvpickle.Usage ) = "-t <channeltype> ";	
	}
	
	// 获取当前全网在线房间列表
	rpc GetChannelOLList(GetChannelOLListReq)returns( GetChannelOLListResp){
	option( tlvpickle.CmdID ) = 2;						
    option( tlvpickle.OptString ) = "t:x:o:n:";				
    option( tlvpickle.Usage ) = "-t <channeltype> -x <minMemberCnt> -o <offset> -n <count>";	
	}
}