syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package lbs;


message AdDetail
{
    required string province = 1;   //省份
    required string city = 2;       //城市
    optional string district = 3;   //街道
    optional string nation = 4;
}

message LocationInfo
{
	required float lat = 1;
	required float lng = 2;
	optional uint64 op_time = 3;	  //用于老化IP和经纬度的映射关系
	optional AdDetail ad_detail = 4;
}



message GetLocationByIpReq
{
	required string ip = 1;
	optional bool is_need_adinfo = 2;
}
message GetLocationByIpResp
{
	required LocationInfo data = 1;
}

message DelLocationByIpReq
{
	required string ip = 1;
}

message SetLocationByIpReq
{
	required string ip = 1;
	required LocationInfo data = 2;
}

message GetAdCodeByIpReq
{
	required string ip = 1;
}

message GetAdCodeByIpResp
{
	required uint32 ad_code = 1;
	required string ad_info = 2;
    optional AdDetail ad_detail = 3;
}


message GetAdCodeByLocationReq
{
	required LocationInfo data = 1;
}
message GetAdCodeByLocationResp
{
	required uint32 ad_code = 1;
	required string ad_info = 2;
    optional AdDetail ad_detail = 3;
}

// 获取调用外部接口（百度/腾讯）获得的原始JSON数据
message GetLocationWebApiJsonReq
{
	required LocationInfo data = 2;
}

message GetLocationWebApiJsonResp
{
	required string json_info = 1;
}

service lbs {
	option( tlvpickle.Magic ) = 15374;		// 服务监听端口号

    rpc GetLocationByIp( GetLocationByIpReq ) returns( GetLocationByIpResp ) {
        option( tlvpickle.CmdID ) = 1;              
        option( tlvpickle.OptString ) = "i:";   
        option( tlvpickle.Usage ) = "-i <ip_addr>";    
    }

    rpc SetLocationByIp( SetLocationByIpReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 2;              
        option( tlvpickle.OptString ) = "i:t:g:";   
        option( tlvpickle.Usage ) = "-i <ip_addr> -t <lat> -g <lng>";    
    }

	rpc GetAdCodeByIp( GetAdCodeByIpReq ) returns( GetAdCodeByIpResp ) {
	    option( tlvpickle.CmdID ) = 3;              
	    option( tlvpickle.OptString ) = "i:";   	
	    option( tlvpickle.Usage ) = "-i <ip_addr>";    
	}

	// 获取调用外部接口（百度/腾讯）获得的原始JSON数据
	rpc GetLocationWebApiJson( GetLocationWebApiJsonReq ) returns( GetLocationWebApiJsonResp ) {
	    option( tlvpickle.CmdID ) = 5;             
	    option( tlvpickle.OptString ) = "t:g:";   	
	    option( tlvpickle.Usage ) = "-t <lat> -g <lng>";  
	}

    rpc DelLocationByIp( DelLocationByIpReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 6;             
        option( tlvpickle.OptString ) = "i:";   
        option( tlvpickle.Usage ) = "-i <ip_addr>";   
    }

    rpc GetAdCodeByLocation( GetAdCodeByLocationReq ) returns( GetAdCodeByLocationResp ) {
        option( tlvpickle.CmdID ) = 7;              
        option( tlvpickle.OptString ) = "t:g:";     
        option( tlvpickle.Usage ) = "-t <lat> -g <lng>";    
    }    
}
