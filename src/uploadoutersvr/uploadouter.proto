syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";  

// namespace
package uploadouter;

enum UploadQiniuType
{
    QINIU_UPLOAD_TYPE_NONE = 0;
    QINIU_UPLOAD_TYPE_IMG = 1; //上传到七牛的ga-album空间
	QINIU_UPLOAD_TYPE_CONFIG_FILE = 2; //上传到七牛的ga-album空间
}

message UploadQiniuReq
{
    bytes file = 1;
    uint32 upload_type = 2; //enum UploadQiniuType
}

message UploadQiniuResp
{
    string resp_url = 1;
}

service uploadouter {
    option( tlvpickle.Magic ) = 14005; 

    rpc UploadQiniu( UploadQiniuReq ) returns( UploadQiniuResp ){
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "p:";
        option( tlvpickle.Usage ) = "-p<path>";
    }
}