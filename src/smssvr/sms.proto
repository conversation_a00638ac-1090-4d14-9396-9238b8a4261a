syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Sms;

//enum SMS_TYPE
//{
//	SMS_TYPE_TT_ACCOUNT = 1;   //2019.7.29: 可能已不再使用
//	SMS_TYPE_TT_REG = 2;		// 验证码:  注册
//	SMS_TYPE_TT_RESET_PWD = 3;	// 验证码: 重置密码
//	SMS_TYPE_CLAM_DEFAULT = 4;	//
//	SMS_TYPE_SERVER_FAIL = 5;  //运维报警
//	SMS_TYPE_DISK_OVERUSED = 6; //运维报警
//	SMS_TYPE_PHONE_DISBIND = 7;  // 解绑手机
//    SMS_TYPE_TT_BIND_PHONE = 8; //绑定手机
//    SMS_TYPE_VERIFY_CODE_WITH_REASON = 9;   // 带原因字串的验证码
//    SMS_TYPE_PASSWORD_BEEN_RESET = 10;      // 密码被重置
//    SMS_TYPE_THRESHOLD_EXCEED_NTFY = 11; //   ######，将超过####阀值，请留意。
//    SMS_TYPE_VERIFY_CODE_FOR_R_ASSISTANT = 12; // 【R助手】验证码%1%（%2%）
//    SMS_TYPE_GAME_AWARD_DIGITMONTRI_20070115 = 13;   //恭喜你在《数码宝贝tri》游戏活动中获得%1%奖品，请于1月15日前联系游戏运营团队QQ%2%进行领取
//    SMS_TYPE_LOGIN_DEVICE_CHANGE_ALARM = 14;  //您的帐号 [#id#]在#time#时通过#kind#手机登陆，若不是你本人操作请立即修改密码。
//    SMS_TYPE_SUBCODE_4_BOY_GALLANTRY_ODE = 15; //《少年歌行》预约验证码
//    SMS_TYPE_SUBCODE_4_LingNengShijie = 16;   //【前进吧悟空】恭喜获得灵能世界的预约资格！！您的公测验证码为：$code$。关注灵能小福星微信公众号，可获取更多福利！
//    SMS_TYPE_NTFY_UNREG_AUDIT_PASS = 17; // 账号注销审核成功
//    SMS_TYPE_NTFY_UNREG_AUDIT_DENY = 18; // 账号注销审核失败
//    SMS_TYPE_NTFY_RECOVERY_PASSWD = 19; // 通过密保问题重置密码
//    SMS_TYPE_CODE_SHAONIANGEXING_TEST = 20; // 《少年歌行》情缘测试H5活动: 【少年歌行】恭喜您获得江湖伙伴！！您的领取验证码为：$code$。关注《少年歌行官方手游》微信公众号，可获取更多江湖情报！
//    SMS_TYPE_VOICE_VERIFY_CODE = 21;    //语音验证码站位
//    SMS_TYPE_ChanganShiErShiCen_SubCode = 22;  //长安十二时辰, 预约验证码
//    SMS_TYPE_VC_LOGIN_WITH_ABNORMAL_DEV = 23; //验证码: 异常设备登录
//    SMS_TYPE_VC_UNREGISTER = 24;              //验证码: 注销账号
//    SMS_TYPE_VC_REBIND_PHONE = 25;            //验证码: 手机换绑
//    SMS_TYPE_VC_LOGIN = 26;             //验证码: 登录
//    SMS_TYPE_VC_WITH_DOWITH = 27;       //您的账号正在 %1%，验证码：%2%
//    SMS_TYPE_VC_LOGIN_SDK_DBBACKEND = 28;       //您正在请求数据后台登录，验证码：%1%
//    SMS_TYPE_VC_REALNAME_CERT = 29;       //您正在进行实名认证，验证码 %1%
//    SMS_TYPE_VC_CAMPUS_RECRUIT = 30;      // 2019 校园招聘 主力 验证码
//    SMS_TYPE_VC_TEST_4_QianXingZhuiZong = 31;      // 【潜行追踪】暗号不要外泄！您的验证码为：$code$。
//    SMS_TYPE_VC_USER_SCORE_WITHDRAW = 32;      // 个人积分体现
//
//    //不再使用这种方式了, 改为头文件, 为了做到加模板不重启 sms, 2019.12.26
//}

// 专用错误码从-20开始
enum ERR_SMS
{
  ERR_SMS_VERFIYCODE_VALIDATE_FAIL = -20;		// 验证码验证失败
  ERR_SMS_TOO_MANY_PHONE = -21;		// 同时群发手机号码不能超过100个
  ERR_SMS_SEND_SMS_FREQ = -22;		// 30s内不能对同一个手机号码调用sendsms
  ERR_SMS_TYPE_INVALID = -23;         // 无效的sms类型
  ERR_SMS_NO_ENOUGH_PARAMS = -24;     // 参数数量不够
  ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED = -25;        //向同一用户发送消息当日超限
  ERR_SMS_INVALID_PHONE = -26;
  ERR_SMS_INVALID_VERIFYCODE = -27;
  ERR_SMS_NOT_SUPPORT_INN = -28; //不支持国际号码

  ERR_VALID_APK_URL_CANNOT_REACH = -31;	// URL访问不到
  ERR_VALID_APK_URL_NOT_APK	= -32;		// 非apkurl
}

//单发短信
message SendSmsReq {
  required string phone = 1;
  required uint32 sms_type  = 2;		//短信模板，
  repeated string param_list = 3;		//短信模板参数
  optional bool without_cooldown = 4;  //(每日)周期发送频率限制
  optional string verify_code_key = 5;    //短信验证码key
  optional string verify_code_usage = 6;  //短信验证码用途
  optional uint32 market_id = 7;
  //	required uint32 retry_times = 4;	//当前的重试次数(start with 0)
  optional uint32 biz_id = 8;
  optional uint32 ext_biz_id = 9;
  optional string request_id = 10;
  optional uint64 create_time = 11;
}

message DirectSendSmsReq {
  required string phone = 1;
  required string text = 2;
  optional uint32 biz_id = 3;
  optional uint32 ext_biz_id = 4;
}

// 群发短信, 所有人收到短信都一样
//message SendGroupSmsReq {
//	required uint32 sms_type = 1;	// 短信模版
//	repeated string param_list = 2;  //短信模板参数
//	repeated string phone_list = 3;  // 批量手机号
//}


////////////////////////////////////////////////
// 验证码相关逻辑， 暂时放在此server， 以后独立
////////////////////////////////////////////////
// 为uid生成验证码
message createVerifyCodeReq {
  required string key = 1;
  optional uint32 code_len = 2;   //请求的验证码长度
}

message createVerifyCodeResp {
  required string key = 1;
  required string verify_code = 2; 	// 验证码
  required uint32 expire_time	= 3;	// 过期时间
}

// 验证验证码是否正确
message validateVerifyCodeReq {
  required string key = 1;
  required string verify_code = 2;
}
/////////////////////////////////////////////////////////

message checkUrlValidApkUrlReq {
  required string url = 1;
}

message checkUrlValidApkUrlResp {
  required uint32 content_length = 1;
}

message downLoadUrlReq{
  required string url = 1;
  optional uint32 timeout = 2;
}

message downLoadUrlResp{
  required string msg = 1;
}

message downLoadUrlByteReq{
  required string url = 1;
  optional uint32 timeout = 2;
}

message downLoadUrlByteResp{
  required bytes msg = 1;
}

message Foo{
}

// post url 接口
message PostUrlDataReq{
  required string url = 1;
  repeated bytes head_info_list = 2;
  required bytes data_info = 3;
}

message PostUrlDataResp{
  required bytes resp_msg = 1;
}

message SendVoiceVerifyCodeReq {
  required string phone = 1;
  required string verify_code  = 2;
  optional uint32 uid = 3;
  optional string nation_code = 5;
  optional uint32 voice_type  = 6;
  repeated string param_list = 7;
  optional uint32 biz_id  = 8;
  optional uint32 ext_biz_id  = 9;
}

message SendVoiceVerifyCodeResp {
}

message SendSmsWithProviderReq{
  required string provider = 1;
  repeated string phones = 2;
  required uint32 sms_type  = 3;		//短信模板，
  repeated string param_list = 4;		//短信模板参数
  optional uint32 market_id = 5;
}
message SendSmsWithProviderResp {
}

message RecordVerifyCodePassReq {
  required string verify_code_key = 1;
  optional uint32 verify_at = 2;
}

message RecordVerifyCodePassResp {
}

// 多个phone 对应一个message 或者
// 多个phone 对应 同样数量的message，对应关系发送
message SendMarketingSmsReq {
  repeated string phones = 1; // 一次最多发送100个手机号码
  repeated string messages = 2;
  optional uint32 biz_id = 3;
  optional uint32 ext_biz_id = 4;
}

message SendMarketingPhoneErrResult {
  optional string phone = 1;
  optional int32 code = 2;
}

message SendMarketingSmsResp {
  required int32 req_result = 1; // 处理是否成功( == 0)
  repeated SendMarketingPhoneErrResult err_phones = 2; // 处理成功情况下，有问题的号码（号码有问题，达到频率限制等等）
}

service Sms {
  option( tlvpickle.Magic ) = 14002;		// 服务监听端口号

  rpc SendSms( SendSmsReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
    option( tlvpickle.CmdID ) = 1;										// 命令号
    option( tlvpickle.OptString ) = "t:s:p:m:";							// 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-t <telephone> -s <sms type> -p <param list> [-m <market_id>]";	// 测试工具的命令号帮助
  }

  //未使用
  //rpc SendGroupSms( SendGroupSmsReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
  //	option( tlvpickle.CmdID ) = 2;										// 命令号
  //    option( tlvpickle.OptString ) = "t:y:s:p:";							// 测试工具的命令号参数， 注意最后的冒号
  //    option( tlvpickle.Usage ) = "-t <telephone1> -y <tel2> -s <sms type> -p <param list>";	// 测试工具的命令号帮助
  //}

  // 这个接口测试用, 业务不要调用该接口
  rpc SendSmsWithProvider( SendSmsWithProviderReq ) returns (SendSmsWithProviderResp) {
    option( tlvpickle.CmdID ) = 3;
    option( tlvpickle.OptString ) = "s:t:m:p:a:u:";
    option( tlvpickle.Usage ) = "-s <Service provider> -t <Telephone> -m <Module type> -p <Param list> [-u <uid, used for hashkey>] [-a <market_id>]";
  }

  //rpc CreateVerifyCode( createVerifyCodeReq ) returns( createVerifyCodeResp ){
  //	option( tlvpickle.CmdID ) = 10;										// 命令号
  //    option( tlvpickle.OptString ) = "k:";							// 测试工具的命令号参数， 注意最后的冒号
  //    option( tlvpickle.Usage ) = "-k <key>";	// 测试工具的命令号帮助
  //}

  //rpc ValidateVerifyCode( validateVerifyCodeReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
  //	option( tlvpickle.CmdID ) = 11;									// 命令号
  //    option( tlvpickle.OptString ) = "k:s:";							// 测试工具的命令号参数， 注意最后的冒号
  //    option( tlvpickle.Usage ) = "-k <key> -s <verifycode>";	// 测试工具的命令号帮助
  //}

  rpc CheckUrlValidApkUrl( checkUrlValidApkUrlReq ) returns( checkUrlValidApkUrlResp ){
    option( tlvpickle.CmdID ) = 12;									// 命令号
    option( tlvpickle.OptString ) = "k:";							// 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-k <url>";	// 测试工具的命令号帮助
  }

  rpc DownLoadUrl( downLoadUrlReq ) returns( downLoadUrlResp ){
    option( tlvpickle.CmdID ) = 13;									// 命令号
    option( tlvpickle.OptString ) = "k:";							// 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-k <url>";	// 测试工具的命令号帮助
  }

  rpc DirectSendSms( DirectSendSmsReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
    option( tlvpickle.CmdID ) = 14;										// 命令号
    option( tlvpickle.OptString ) = "t:m:";							// 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-t <telephone> -m <msg>";	// 测试工具的命令号帮助
  }

  rpc PostUrlData( PostUrlDataReq ) returns( PostUrlDataResp ){
    option( tlvpickle.CmdID ) = 15;
    option( tlvpickle.OptString ) = "k:x:s:";
    option( tlvpickle.Usage ) = "-k <url> -x <head info> -s <data info>";
  }

  rpc DownLoadUrlByte( downLoadUrlByteReq ) returns( downLoadUrlByteResp ){
    option( tlvpickle.CmdID ) = 16;									// 命令号
    option( tlvpickle.OptString ) = "k:";							// 测试工具的命令号参数， 注意最后的冒号
    option( tlvpickle.Usage ) = "-k <url>";	// 测试工具的命令号帮助
  }

  rpc SendVoiceVerifyCode( SendVoiceVerifyCodeReq ) returns ( SendVoiceVerifyCodeResp ) {
    option( tlvpickle.CmdID ) = 20;
    option( tlvpickle.OptString ) = "t:e:o:p:";
    option( tlvpickle.Usage ) = "-t <telephone> -e <verifycode> -o <voice type> -p <param list>";
  }

  rpc RecordVerifyCodePass( RecordVerifyCodePassReq ) returns ( RecordVerifyCodePassResp ) {
    option( tlvpickle.CmdID ) = 21;
    option( tlvpickle.OptString ) = "";
    option( tlvpickle.Usage ) = "";
  }

  rpc SendMarketingSms( SendMarketingSmsReq ) returns ( SendMarketingSmsResp ) {
    option( tlvpickle.CmdID ) = 22;
    option( tlvpickle.OptString ) = "t:m:";
    option( tlvpickle.Usage ) = "-t <phone> -m <message>";
  }
}



