syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package realnameauth;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

enum EOperType{
	ENUM_REALNAME_OPER_SET = 1;   //
	ENUM_REALNAME_OPER_UNSET = 2;
	ENUM_REALNAME_OPER_RESET = 3;
}

enum EAuthPhoneType{
  ENUM_REALNAME_AUTH_PHONE_TYPE = 1;         // 实名认证手机
  ENUM_REALNAME_SECURITY_PHONE_TYPE = 2;     // 安全手机
  ENUM_REALNAME_TT_PHONE_TYPE = 3;           // TT 绑定手机
}

// 认证类型
enum AuthType {
   ENUM_AUTH_TYPE_IDCARD = 1; //身份证认证
   ENUM_AUTH_TYPE_FACE = 2;   // 人脸认证
}

// 认证状态 
enum EAuthStatus {
   ENUM_AUTH_NONE = 1;  // 未认证
   ENUM_AUTH_CHECK = 2; // 审核中
   ENUM_AUTH_PASS = 3;   // 审核通过
   ENUM_AUTH_UNAPPROVE = 4; // 审核不通过
}

enum EAuthType{
	ENUM_REALNAME_AUTH_PHONE = 1;
}

// 认证类型信息
message AuthTypeInfo {
    optional uint32 auth_type = 1; // see AuthType
    optional uint32 auth_stauts = 2; // see  AuthType
}

// 认证的身份证信息
message AuthIdCardInfo
{
  optional string name = 1;
  optional string identity_num = 2;
}

// 认证信息, 从货币组那边获取存到redis的结构
message AuthInfo 
{
   optional uint32 uid = 1;
   optional int32 status = 2;   // 认证状态
   optional string birthday = 3;  // 生日
   optional uint32 level = 4;  // 实名认证档位 0为认证或认证失败; 1:身份证和姓名认证通过 2:人脸认证通过
}


message GetIsEnableCheckReq
{
}
message GetIsEnableCheckResp
{
	required bool is_enable_check = 1;
}


message AddAuthOperHistoryReq
{
	required uint32 uid = 1;
	required uint32 oper_type = 2;
	required uint32 auth_type = 3;
	required string log_info = 4;
}
message AddAuthOperHistoryResp
{
}

/* 新的认证逻辑 */
//add 2018/11/15/11:15 by T1035
message AuthInfoReq
{
    required uint32 uid =1;
    required int32 status=2;    // 认证状态 -1 未认证;0 待审核; 1 审核通过; 2 审核未通过
}

message AuthInfoResp
{
}

message GetAuthInfoReq
{
    required uint32 uid =1;
}

message GetAuthInfoResp
{
    // 忘记给1 占坑了。如果改回1，会导致线上上一个版本的 pb 解析失败
    required int32 status = 2;              // see ga::ENUM_REALNAME_AUTH_STATUS(auto.proto)
    optional RealNameAuthInfo realname_info = 3; 
}

// 获取实名认证信息
message GetUserRealNameAuthInfoV2Req 
{
    required uint32 uid = 1;
    optional bool is_need_auth_phone = 2; // 是否需要实名认证手机信息
    optional bool is_need_idcard_info = 3;  // 是否需要身份证信息
}
message GetUserRealNameAuthInfoV2Resp
{
    repeated AuthTypeInfo auth_list = 1;    // 实名认证信息
    optional AuthIdCardInfo idcard_info = 2;  // 实名身份证信息
    optional string auth_phone = 3;   // 实名认证手机
    optional bool   is_adult = 4;  // 是否成年 未实名认证默认是成年人，完成身份证和人脸认证根据身份证信息判断
    optional uint32 age = 5;   // 未实名认证默认为 0 
    optional uint32 realname_version = 6; // 实名版本
}

message GetUserIdentityExpireDateReq
{
  required uint32 uid =1;
}

message GetUserIdentityExpireDateResp
{
  optional string identity_expire_date = 1;
}

message GetUserIdentityInfoReq
{
  required uint32 uid = 1;
}

message GetUserIdentityInfoResp
{
  // 未认证/认证未通过/审核中/该功能上线前已认证的用户，数据库没有他们的身份证信息
  // 该功能上线前已认证的用户的身份证信息会逐步补充
  required bool have_info = 1;                          // 数据库是否有用户的身份证信息
  required bool is_adult = 3;                           // 用户是否成年
  optional string identity_number = 4;                  // 身份证号码
  optional string identity_valid_time = 5;              // 身份证有效期的截止日期(0000-00-00)，永久有效为 0000-00-00
  optional uint32 age = 6;                              // 用户年龄
  optional int32 status = 7;              // see ga::ENUM_REALNAME_AUTH_STATUS(auto.proto)
}

message UserIdentifyInfo
{
   required uint32 uid = 1;         
   required bool have_info = 2;    //数据库是否有用户的身份证信息
   optional bool is_adult = 3;     // 用户是否成年
   optional string identity_number = 4;    // 身份证号码
   optional string identity_valid_time = 5;      // 身份证有效期的截止日期(0000-00-00)，永久有效为 0000-00-00
   optional string name = 6;  // 姓名
   optional uint32 level = 7;  // 实名认证档位 0为认证或认证失败; 1一档认证（二元素）; 2二档认证(三元素)
}

// 这个批量接口只返回用户 UserIdentifyInfo(uid,have_info,is_adult)
message BatchGetUserIdentifyInfoReq
{ 
   repeated uint32 uid_list = 1; 
}
message BatchGetUserIdentifyInfoResp
{
   repeated UserIdentifyInfo info_list = 1;
}

message AddUserIdentityInfoReq
{
  required uint32 uid = 1;
  required string identity_number = 2;
  required string identity_valid_time = 3;
  required int32  have_push =4;                         // 是否发送过过期推送 1 发送过，0,没有
  optional string name = 5;
  optional uint32 level = 6;  // 实名认证档位 0为认证或认证失败; 1一档认证（二元素）; 2二档认证(三元素)
}

message AddUserIdentityInfoResp
{
}

message UpdateUserIdentityInfoReq
{
  required uint32 uid = 1;
  optional string identity_number = 2;
  optional string identity_valid_time = 3;
  required int32 have_push = 4;
}

message UpdateUserIdentityInfoResp
{
}

message AddOrUpdateUserIdentityInfoReq
{
  required uint32 uid = 1;
  optional string identity_number = 2;
  optional string identity_valid_time = 3;
  required int32 have_push = 4;
  optional string name = 5;
}

message AddOrUpdateUserIdentityInfoResp
{
}

message DelUserIdentityInfoReq
{
  required uint32 uid = 1;
}

message DelUserIdentityInfoResp
{
}

message AddIdentityExpireUserPushTimeReq
{
  required int32 uid = 1;
  required int32 push_time = 2;
}

message AddIdentityExpireUserPushTimeResp
{
}

message RecordUserAuthPhoneReq
{
  required uint32 uid = 1;
  required string auth_phone = 2;
  optional uint32 phone_type = 3;  // see EAuthPhoneType
}

message RecordUserAuthPhoneResp
{

}

message GetUserAuthPhoneReq
{
  required uint32 uid = 1;
}

message GetUserAuthPhoneResp
{
  required bool have_phone = 1;
  optional string auth_phone = 2;
}


message GetPhoneBindNumberReq
{
  required string phone = 1;
}

message GetPhoneBindNumberResp
{
  required uint32 bind_number = 1;
}

message RecordUserIdentityInfoReq
{
  required uint32 uid = 1;
  required string identity_num = 2;
  required string identity_valid_time = 3;
  required string name = 4;
}

message RecordUserIdentityInfoResp
{
}

message GetRealNameCurrVersionReq
{
  required uint32 uid = 1;
}

message GetRealNameCurrVersionResp
{
  required uint32 version = 1;
}



///////////////////////////////////////////////////////////////

/* 家长监护模式 BEGIN */
//add 2018/11/1/18:15 by T1035

message ParentGuardianSwitchReq
{
    required uint32 uid = 1;
    required bool on_off = 2; // true:on;false:off
    required string password = 3;
    optional bool is_force_off = 4; // 无需密码，强制关闭，用于忘记密码申诉成功后关闭该模式
}

message ParentGuardianSwitchResp
{
}

message ParentGuardianCheckPasswordReq
{
    required uint32 uid = 1;
    required string password = 3;
}

message ParentGuardianCheckPasswordResp
{
	required bool is_pass = 1;
}

message ParentGuardianStateReq
{
    required uint32 uid = 1;
}

message ParentGuardianStateResp
{
    required bool on_off = 2;
}

message ParentGuardianInfo
{
    required uint32 uid = 1;
    required bool is_on = 2;   //是否打开家长模式
}

message BatchGetParentGuardianInfoReq
{
    repeated uint32 uid_list = 1;
}
message BatchGetParentGuardianInfoResp
{
    repeated ParentGuardianInfo info_list = 1;
}

//已实名用户扫脸验证身份
message CheckIdentityByFaceReq
{
	required uint32 uid =1;
	required string faceid_check_data = 2;
	required string faceid_token = 3;
}

message CheckIdentityByFaceResp
{
	required bool is_pass = 1;
	optional uint32 result_code = 2;
  	optional string result_msg  = 3;
}

//检查是否超过今日申诉限制次数
message CheckAppealCntIsOverLimitReq
{
	required uint32 uid = 1;
}

message CheckAppealCntIsOverLimitResp
{
	required bool is_over_limit = 1;
}

message ParentGuardianUpdatePwdReq
{
    required uint32 uid = 1;
    required string password = 2;
}

/* 家长监护模式 END*/

/**  第三代 实名认证 BEGIN **/

message RealNameAuthInfo
{
  optional string auth_phone = 1;
  optional string name = 2;
  optional string identity_num = 3;
  optional uint32 age = 4;
}

message GetRealNameAuthTokenReq
{
  required uint32 uid = 1;
  required RealNameAuthInfo realname_info =2;
}

message GetRealNameAuthTokenResp
{
  required string faceid_token = 1;
  optional uint32 result_code = 2;
  optional string result_msg  = 3;
}

//已实名用户通过uid获取token
message GetRealNameFaceIdTokenByUidReq
{
  required uint32 uid = 1;
}

message GetRealNameFaceIdTokenByUidResp
{
  required string faceid_token = 1;
  optional uint32 result_code = 2;
  optional string result_msg  = 3;
}

message ApplyRealNameAuthDataReq
{
  required uint32 uid = 1;
  required string faceid_token = 2;
  required string faceid_check_data = 3; 
}

message ApplyRealNameAuthDataResp
{
  optional uint32 result_code = 1;
  optional string result_msg  = 2;
}

message RelieveRealNameAuthReq { repeated uint32 uid = 1; }

message RelieveRealNameErrDetail {
  required uint32 uid = 1;
  required string reason = 2;
}
message RelieveRealNameAuthResp {
  repeated RelieveRealNameErrDetail errDetail = 1;
  optional string errMsg = 2;
}

message GetRealNameApplyInfoReq
{
  required uint32 uid = 1;
}

message GetRealNameApplyInfoResp
{
  optional string identity_num=1;
  optional string name = 2;
  optional uint32 apply_num = 3;
}

message DelRealNameInfoReq
{
  required uint32 uid =1;
}

message DelRealNameInfoResp
{
}

message GetTheSameRealNameUserListReq
{
  required uint32 uid = 1;
}

message GetTheSameRealNameUserListResp
{
  repeated uint32 uids = 1;
}

/**  第三代 实名认证 END **/



/** 阿里人脸识别认证 BEGIN **/

// 获取人脸识别 certifyid_token
message GetFaceAuthAliTokenReq
{
   required uint32 uid = 1;
   required RealNameAuthInfo realname_info =2;
   required string meta_info = 3;   //阿里实名认证SDK参数
}
message GetFaceAuthAliTokenResp
{
   optional string certifyid_token = 1;
   optional uint32 result_code = 2;
   optional string result_msg  = 3;
}

// 查询人脸识别认证结果
message GetFaceAuthAliResultReq
{
   required uint32 uid = 1;
   required string certifyid_token = 2;
   optional string scene = 3;
}

message GetFaceAuthAliResultResp
{
   optional uint32 result_code = 1;
   optional string result_msg  = 2;
}

//已实名用户通过uid获取token
message GetFaceAuthAliTokenByUidReq
{
   required uint32 uid = 1;
   required string meta_info = 2;   //阿里实名认证SDK参数
}

message GetFaceAuthAliTokenByUidResp
{
   optional string certifyid_token = 1;
   optional uint32 result_code = 2;
   optional string result_msg  = 3;
}

//已实名用户扫脸验证身份
message CheckFaceByAliTokenReq
{
	required uint32 uid =1;
	required string certifyid_token = 2;
  optional string scene = 3; // 场景标识，货币组需要
}

message CheckFaceByAliTokenResp
{
	optional bool is_pass = 1;
}

/** 阿里人脸识别认证         END **/

// 二元素实名认证 (身份证和姓名)
message AuthByTwoElementReq {
   required uint32 uid = 1;
   required RealNameAuthInfo realname_info =2;
}
message AuthByTwoElementResp {
   optional bool is_adult = 1;  // 是否是成年人
}

// 检测是否在实名姓名黑名单中
message CheckIsInNameBlackListReq {
   optional string name = 1;
}
message CheckIsInNameBlackListResp {
   optional bool is_black = 1;
}

message GetFaceRecognitionProviderReq {
  optional string app_id = 1;
  required uint64 uid = 2;
  repeated string support_providers = 3;
}

//0	人工认证
//1	旷视人脸识别
//2	阿里人脸识别认证
//3	中宣部网络游戏实名认证
//4	诺正通认证
//5	阿里云身份证二元素
//6	腾讯云身份证二元素
//7	华为云人脸识别
//8	腾讯云人脸识别
message GetFaceRecognitionProviderResp {
  optional string provider_name = 1;
  optional string provider_code = 2;
}

message GetFaceRecognitionCertifyIdReq {
  optional string app_id = 1;
  required string provider_code = 2;
  required uint64 uid = 3;
  optional string meta_info = 4;
  optional string device_info = 5;
  optional RealNameAuthInfo auth_info = 6;
}

// http://s-doc2.ttyuyin.com/web/#/3/1651
message FaceRecognitionCertifyData {
  optional string order_no = 1; // 本次人脸的业务订单号
  optional string certify_id = 2; // 供应商返回的certifyId
  optional string request_id = 3; // 请求id
  optional string message = 4; // 供应商返回信息
  optional string exclusive = 5; // 各供应商返回的专属参数
}

message GetFaceRecognitionCertifyIdResp {
  optional FaceRecognitionCertifyData data = 1;
}

message GetFaceRecognitionResultReq {
  optional string app_id = 1;
  required string provider_code = 2;
  required uint64 uid = 3;
  required string certify_id = 4;
  required string scene = 5;
  optional bool is_real_name_auth = 6; // 标记这次是否实名的人脸验证
  optional string provider_data = 7; // 部分供应商需要提供额外的数据进行查询结果
}

// http://s-doc2.ttyuyin.com/web/#/3/1652
message GetFaceRecognitionResult {
  required bool is_pass = 1;
  optional string certify_id = 2;
  optional string request_id = 3;
  optional string provider_response_code = 4;
  optional string provider_response_result = 5;
  optional string provider_response_description = 6;
}

message GetFaceRecognitionResultResp {
  optional GetFaceRecognitionResult result = 1;
}

service RealNameAuth {
	option( tlvpickle.Magic ) = 15641;		// 服务监听端口号

    rpc GetIsEnableCheck( GetIsEnableCheckReq ) returns( GetIsEnableCheckResp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

	rpc AddAuthOperHistory( AddAuthOperHistoryReq ) returns( AddAuthOperHistoryResp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:d:t:x:";
        option( tlvpickle.Usage ) = "-u<uid> -d<is del> -s<auth type> -x<info log>";
    }

    // 家长监护模式 add 2018/11/1/18:15 by T1035
    rpc SwitchParentGuardian(ParentGuardianSwitchReq) returns (ParentGuardianSwitchResp)
    {
        option ( tlvpickle.CmdID ) = 3;
        option ( tlvpickle.OptString ) = "u:p:s:";
        option ( tlvpickle.Usage ) = "-u<uid> -p<1:on,0:off> -s<password>";
    }

    rpc GetParentGuardianState(ParentGuardianStateReq) returns (ParentGuardianStateResp)
    {
        option ( tlvpickle.CmdID ) = 4;
        option ( tlvpickle.OptString ) = "u:";
        option ( tlvpickle.Usage ) = "-u<uid>";
    }

    rpc AddUserRealNameAuthInfo(AuthInfoReq) returns (AuthInfoResp)
    {
        option ( tlvpickle.CmdID ) = 5;
        option ( tlvpickle.OptString ) ="u:s:";
        option ( tlvpickle.Usage) = "-u<uid> -s<stauts:1,2,3,4,5,6>";
    }

    rpc GetUserRealNameAuthInfo(GetAuthInfoReq) returns (GetAuthInfoResp)
    {
        option ( tlvpickle.CmdID ) = 6;
        option ( tlvpickle.OptString ) ="u:";
        option ( tlvpickle.Usage) = "-u<uid>";
    }

    rpc ModifyUserRealNameAuthInfo(AuthInfoReq) returns (AuthInfoResp)
    {
        option ( tlvpickle.CmdID ) = 7;
        option ( tlvpickle.OptString ) ="u:s:";
        option ( tlvpickle.Usage) = "-u<uid> -s<status:1,2,3,4,5,6>";
    }

    rpc GetUserIdentityInfo(GetUserIdentityInfoReq) returns (GetUserIdentityInfoResp)
    {
      option ( tlvpickle.CmdID ) = 9;
      option ( tlvpickle.OptString ) = "u:";
      option ( tlvpickle.Usage ) = "-u<uid>";
    }

    rpc AddUserIdentityInfo(AddUserIdentityInfoReq) returns (AddUserIdentityInfoResp)
    {
      option ( tlvpickle.CmdID ) = 10;
      option ( tlvpickle.OptString ) = "u:n:t:p:";
      option ( tlvpickle.Usage ) = "-u<uid> -n<identity_number> -t<identity_valid_time> ";
    }

    rpc UpdateUserIdentityInfo(UpdateUserIdentityInfoReq) returns (UpdateUserIdentityInfoResp)
    {
      option ( tlvpickle.CmdID ) = 11;
      option ( tlvpickle.OptString ) = "u:n:t:";
      option ( tlvpickle.Usage ) = "-u<uid> -n<identity_number> -t<identity_valid_time>";
    }

    rpc AddOrUpdateUserIdentityInfo(AddOrUpdateUserIdentityInfoReq) returns (AddOrUpdateUserIdentityInfoResp)
    {
      option ( tlvpickle.CmdID ) = 12;
      option ( tlvpickle.OptString ) = "u:n:t:";
      option ( tlvpickle.Usage ) = "-u<uid> -n<identity_number> -t<identity_valid_time>";
    }

    rpc DelUserIdentityInfo (DelUserIdentityInfoReq) returns (DelUserIdentityInfoResp)
    {
      option ( tlvpickle.CmdID ) = 13;
      option ( tlvpickle.OptString ) = "u:";
      option ( tlvpickle.Usage ) = "-u<uid>";
    }

    rpc AddIdentityExpireUserPushTime (AddIdentityExpireUserPushTimeReq) returns (AddIdentityExpireUserPushTimeResp)
    {
      option ( tlvpickle.CmdID ) = 14;
      option ( tlvpickle.OptString ) = "u:t:";
      option ( tlvpickle.Usage ) = "-u<uid> -t<push_time>";
    }

    rpc GetUserIdentityExpireDate ( GetUserIdentityExpireDateReq ) returns ( GetUserIdentityExpireDateResp )
    {
        option ( tlvpickle.CmdID ) = 15;
        option ( tlvpickle.OptString ) = "u:";
        option ( tlvpickle.Usage ) = "-u<uid>";
    }
    
    rpc RecordUserAuthPhone ( RecordUserAuthPhoneReq ) returns ( RecordUserAuthPhoneResp )
    {
        option ( tlvpickle.CmdID ) = 16;
        option ( tlvpickle.OptString ) = "u:s:";
        option ( tlvpickle.Usage ) = "-u<uid> -s<auth_phone>";
    }

    rpc GetUserAuthPhone ( GetUserAuthPhoneReq ) returns ( GetUserAuthPhoneResp )
    {
      option ( tlvpickle.CmdID ) = 17;
      option ( tlvpickle.OptString ) = "u:";
      option ( tlvpickle.Usage ) = "-u<uid>";
    }

    rpc GetPhoneBindNumber ( GetPhoneBindNumberReq ) returns ( GetPhoneBindNumberResp )
    {
      option ( tlvpickle.CmdID ) = 18;
      option ( tlvpickle.OptString ) = "p:";
      option ( tlvpickle.Usage ) = "-p<phone>";
    }

    rpc GetRealNameAuthToken ( GetRealNameAuthTokenReq ) returns ( GetRealNameAuthTokenResp )
    {
      option ( tlvpickle.CmdID ) = 19;
      option ( tlvpickle.OptString ) = "u:n:m:p:";
      option ( tlvpickle.Usage ) = "-u<uid> -n<name> -m<identity_number> -p<phone>";
    }

    rpc ApplyRealNameAuthData ( ApplyRealNameAuthDataReq ) returns ( ApplyRealNameAuthDataResp )
    {
      option ( tlvpickle.CmdID ) = 20;
      option ( tlvpickle.OptString ) = "u:t:d:";
      option ( tlvpickle.Usage ) = "-u<uid> -t<token> -d<data>";
    }

    rpc RecordUserIdentityInfo ( RecordUserIdentityInfoReq ) returns ( RecordUserIdentityInfoResp )
    {
      option ( tlvpickle.CmdID ) = 21;
      option ( tlvpickle.OptString ) = "u:i:n:t:";
      option ( tlvpickle.Usage ) = "-u<uid> -i<identity_number> -n<name> -t<identity_valid_time>";
    }

    rpc GetRealNameApplyInfo ( GetRealNameApplyInfoReq ) returns ( GetRealNameApplyInfoResp )
    {
      option ( tlvpickle.CmdID ) = 22;
      option ( tlvpickle.OptString ) = "u:";
      option ( tlvpickle.Usage ) = "-u<uid> ";
    }

    rpc GetRealNameCurrVersion ( GetRealNameCurrVersionReq ) returns ( GetRealNameCurrVersionResp )
    {
      option ( tlvpickle.CmdID ) = 23;
      option ( tlvpickle.OptString ) = "u:";
      option ( tlvpickle.Usage ) = "-u<uid> ";
    }

    rpc DelRealNameInfo ( DelRealNameInfoReq ) returns ( DelRealNameInfoResp )
    {
      option ( tlvpickle.CmdID ) = 24;
      option ( tlvpickle.OptString ) = "u:";
      option ( tlvpickle.Usage ) = "-u<uid> ";
    }

    rpc CheckIdentityByFace ( CheckIdentityByFaceReq ) returns ( CheckIdentityByFaceResp )
    {
      option ( tlvpickle.CmdID ) = 25;
      option ( tlvpickle.OptString ) = "u:";
      option ( tlvpickle.Usage ) = "-u<uid> ";
    }

    rpc CheckAppealCntIsOverLimit ( CheckAppealCntIsOverLimitReq ) returns ( CheckAppealCntIsOverLimitResp )
    {
      option ( tlvpickle.CmdID ) = 26;
      option ( tlvpickle.OptString ) = "u:";
      option ( tlvpickle.Usage ) = "-u<uid> ";
    }

    rpc GetRealNameFaceIdTokenByUid ( GetRealNameFaceIdTokenByUidReq ) returns ( GetRealNameFaceIdTokenByUidResp )
    {
      option ( tlvpickle.CmdID ) = 27;
      option ( tlvpickle.OptString ) = "u:";
      option ( tlvpickle.Usage ) = "-u<uid> ";
    }

	rpc CheckParentGuardianPassword ( ParentGuardianCheckPasswordReq ) returns ( ParentGuardianCheckPasswordResp )
    {
      option ( tlvpickle.CmdID ) = 28;
      option ( tlvpickle.OptString ) = "u:s:";
      option ( tlvpickle.Usage ) = "-u<uid> -s<password>";
    }

    rpc ParentGuardianUpdatePassword ( ParentGuardianUpdatePwdReq ) returns ( tlvpickle.SKBuiltinEmpty_PB )
    {
      option ( tlvpickle.CmdID ) = 29;
      option ( tlvpickle.OptString ) = "u:p:";
      option ( tlvpickle.Usage ) = "-u<uid> -p<password>";
    }

    rpc RelieveRealNameAuth(RelieveRealNameAuthReq)
        returns (RelieveRealNameAuthResp) {
      option (tlvpickle.CmdID) = 30;
      option (tlvpickle.OptString) = "u:x:";
      option (tlvpickle.Usage) = "-u <uid> -x<uid list (string, split by ',') >";
    }

    rpc BatchGetParentGuardianInfo(BatchGetParentGuardianInfoReq)
        returns (BatchGetParentGuardianInfoResp) {
      option (tlvpickle.CmdID) = 31;
      option (tlvpickle.OptString) = "u:";
      option (tlvpickle.Usage) = "-u <uid list (string, split by ',')>";
    }

    rpc BatchGetUserIdentifyInfo(BatchGetUserIdentifyInfoReq)
        returns (BatchGetUserIdentifyInfoResp) {
      option (tlvpickle.CmdID) = 32;
      option (tlvpickle.OptString) = "u:";
      option (tlvpickle.Usage) = "-u <uid list (string, split by ',')>";
    }

    rpc GetTheSameRealNameUserList( GetTheSameRealNameUserListReq ) returns( GetTheSameRealNameUserListResp ) {
        option( tlvpickle.CmdID ) = 33;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc GetFaceAuthAliToken( GetFaceAuthAliTokenReq ) returns( GetFaceAuthAliTokenResp ) {
        option( tlvpickle.CmdID ) = 34;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc GetFaceAuthAliResult( GetFaceAuthAliResultReq ) returns( GetFaceAuthAliResultResp ) {
        option( tlvpickle.CmdID ) = 35;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc GetFaceAuthAliTokenByUid( GetFaceAuthAliTokenByUidReq ) returns( GetFaceAuthAliTokenByUidResp ) {
        option( tlvpickle.CmdID ) = 36;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc CheckFaceByAliToken( CheckFaceByAliTokenReq ) returns( CheckFaceByAliTokenResp ) {
        option( tlvpickle.CmdID ) = 37;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc AuthByTwoElement( AuthByTwoElementReq ) returns( AuthByTwoElementResp ) {
        option( tlvpickle.CmdID ) = 38;
        option( tlvpickle.OptString ) = "u:a:n:d:";
        option( tlvpickle.Usage ) = "-u <uid> -a<auth_phone> -n<name> -d<identity_num>";
    }

    rpc GetUserRealNameAuthInfoV2(GetUserRealNameAuthInfoV2Req) returns (GetUserRealNameAuthInfoV2Resp) {
        option( tlvpickle.CmdID ) = 39;
        option( tlvpickle.OptString ) = "u:p:b:";
        option( tlvpickle.Usage ) = "-u <uid> -p<is_need_phone> -b<is_need_idcard_info>";
    }

    rpc CheckIsInNameBlackList(CheckIsInNameBlackListReq) returns (CheckIsInNameBlackListResp) {
        option( tlvpickle.CmdID ) = 40;
        option( tlvpickle.OptString ) = "n:";
        option( tlvpickle.Usage ) = "-n <name>";
    }

    // 多人脸供应商接口

    rpc GetFaceRecognitionProvider(GetFaceRecognitionProviderReq) returns (GetFaceRecognitionProviderResp) {
        option( tlvpickle.CmdID ) = 41;
        option( tlvpickle.OptString ) = "a:u:";
        option( tlvpickle.Usage ) = "-a <appid> -u <uid>";
    }

    rpc GetFaceRecognitionCertifyId(GetFaceRecognitionCertifyIdReq) returns (GetFaceRecognitionCertifyIdResp) {
        option( tlvpickle.CmdID ) = 42;
        option( tlvpickle.OptString ) = "a:u:";
        option( tlvpickle.Usage ) = "-a <appid> -u <uid>";
    }

    rpc GetFaceRecognitionResult(GetFaceRecognitionResultReq) returns (GetFaceRecognitionResultResp) {
        option( tlvpickle.CmdID ) = 43;
        option( tlvpickle.OptString ) = "a:u:";
        option( tlvpickle.Usage ) = "-a <appid> -u <uid>";
    }
}
