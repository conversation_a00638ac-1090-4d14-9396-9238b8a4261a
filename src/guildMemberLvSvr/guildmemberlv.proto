syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package guildmemberlv;

// 与下面的CONTRIBUTION_TYPE保持一致
enum OPER_TYPE {
	OPER_CHECK_IN = 1;				// 签到
	OPER_CHECK_IN_SUPPLEMENT = 2;	// 补签
	OPER_DONATE = 3;				// 捐献
	OPER_CAPITAL_INJECTION = 4;		// 注资消费
	OPER_CONSUME_GAME = 5;			// 游戏消费
	OPER_CONSUME_HAPPY_CENTER = 6;	// 欢城消费
	OPER_BUY_GIFT = 7;				// 购买礼包
	OPER_SEND_PRESENT = 8;			// 送礼物
	OPER_LOTTO = 9;					// 抽奖
	
	
	OPER_SEND_BACK = 101;			// 退还贡献（如申请购买公会商品后，审批不通过时退还购买时消耗的贡献）
	OPER_OFFICIAL_AWARD = 102;		// 后台发放给用户1:1的贡献（补偿，奖励之类的）
}

// 与上面的OPER_TYPE保持一致
enum CONTRIBUTION_TYPE {
	CONT_CHECK_IN_DAILY = 1;		// 每日签到
	CONT_CHECK_IN_SUPPLEMENT = 2;	// 补签
	CONT_DONATE_DAILY = 3;			// 捐献
	CONT_CAPITAL_INJECTION = 4;		// 注资消费
	CONT_CONSUME_GAME = 5;			// 游戏消费
	CONT_CONSUME_HAPPY_CENTER = 6;	// 欢城消费
	CONT_BUY_GIFT = 7;				// 购买礼包
	CONT_SEND_PRESENT = 8;			// 送礼物
	CONT_LOTTO = 9;					// 抽奖
		
	CONT_SEND_BACK = 101;			// 退还贡献（如申请购买公会商品后，审批不通过时退还购买时消耗的贡献）
	CONT_OFFICIAL_AWARD = 102;		// 后台发放给用户的奖励（补偿，奖励之类的）
	
	CONT_CHECK_IN_CONTINUOUS = 201;	// 连续签到
	CONT_DONATE_CONTINUOUS = 202;	// 连续捐献
}

enum CONTRIBUTION_RANK_TYPE {
	TOT_DESC = 0;		// 历史总贡献降序
	TOT_ASC = 1;		// 历史总贡献升序
	VALID_DESC = 2;		// 当前可用贡献降序
	VALID_ASC = 3;		// 当前可用贡献升序
}

// 个人贡献的相关操作
message StMemberContributionOper
{
	required uint32 oper_type = 1;	// OPER_TYPE/CONTRIBUTION_TYPE
	required int32 oper_value = 2;	// OPER_VALUE/CONTRIBUTION_VALUE
	optional string order_id = 3;	// 个别操作需要指定的订单id，一般可不填
	optional string order_desc = 4;	// 订单描述（客户端显示）
	optional string extend = 5;	// 自定义的订单信息
	
	optional uint32 continuous_days = 6; // 连签/连捐天数
	repeated uint32 supplement_day_list = 7; // 补签的列表
	optional bool opt_invalid = 8;
}

// 个人贡献订单
message StMemberContributionOrder
{
	required uint32 uid = 1;
	required uint32 guild_id = 2;
	required uint32 contribution_type = 3;	// CONTRIBUTION_TYPE
	required int32 contribution_value = 4;
	required string order_id = 5;
	optional string order_desc = 6;	// 订单描述（客户端显示）
	optional string extend = 7;	// 自定义的订单信息（json）
	optional bool opt_invalid = 8;
}

// 个人贡献明细
message StMemberContributionDetail
{
	required uint32 uid = 1;
	required uint32 guild_id = 2;
	required uint32 contribution_type = 3;	// CONTRIBUTION_TYPE
	required int32 contribution_value = 4;	
	optional uint32 create_ts = 5;
	optional string order_desc = 6; // 订单描述（客户端显示）
	optional string extend = 7;	// 自定义的订单信息（json）
}

// 个人贡献信息
message StMemberContributionInfo
{
	required uint32 uid = 1;
	required uint32 guild_id = 2;
	required uint32 total_contribution = 3;	// 历史总贡献
	required uint32 valid_contribution = 4;	// 当前可用贡献
	required uint32 member_lv = 5;	// 个人等级
	optional uint32 max_member_lv = 6;		// 最大个人等级
	optional uint32 cur_lv_contribution = 7;	// 当前等级的贡献进度
	optional uint32 max_lv_contribution = 8;	// 当前等级的贡献最大值
}


// 增加个人贡献
message AddMemberContributionReq
{
	required uint32 guild_id = 1;
	required StMemberContributionOper oper_info = 2;
}

message AddMemberContributionResp
{
	required StMemberContributionInfo contribution = 1;
	required int32 contribution_added = 2;	// 增加的个人贡献
	
	optional uint32 to_bonus_period = 3;	// 连签，连捐可加成周期
	optional int32 to_bonus_contribution = 4;	// 连签，连捐可加成贡献（contribution_value中已包含）
}


// 获取个人贡献
message GetMemberContributionReq
{
	required uint32 guild_id = 1;
}

message GetMemberContributionResp
{
	required StMemberContributionInfo contribution = 1;
}

// 批量查询个人贡献
message BatchGetMemberContributionReq
{
	required uint32 guild_id = 1;
	repeated uint32 uid_list = 2;
}

message BatchGetMemberContributionResp
{
	repeated StMemberContributionInfo info_list = 1;
}

// 根据排序获取成员列表
message GetMemberContributionListInOrderReq
{
	required uint32 guild_id = 1;
	optional uint32 offset = 2;
	optional uint32 limit = 3;
	optional uint32 rank_type = 4; // CONTRIBUTION_RANK_TYPE
}

// 获取个人贡献明细列表
message GetMemberContributionDetailListReq
{
	required uint32 guild_id = 1;
	optional uint32 offset = 2;
	optional uint32 limit = 3;
}

message GetMemberContributionDetailListResp
{
	repeated StMemberContributionDetail detail_list = 1;
}

// 清除个人贡献(离开公会)
message ClearMemberContributionReq
{
	required uint32 guild_id = 1;
}

// 捐献选项
message StGuildDonateOption 
{
	required uint32 donate_value = 1;	// 捐献值
	required uint32 member_contribution_added = 2;	// 增加的个人贡献
	required bool is_valid = 3;	// 是否可选
}

// 查询捐献选项
message GetGuildDonateOptionReq 
{
	required uint32 member_lv = 1;
}

message GetGuildDonateOptionResp
{
	repeated StGuildDonateOption option_list = 1;
}

// 获取用户在指定日期的特定的贡献记录
message GetMemberSpecContTypeByDateReq
{
	required uint32 guild_id = 1;
	required uint32 date_ts = 2;
	required uint32 contribution_type = 3;
}


service guildmemberlv {

    option( tlvpickle.Magic ) = 15395;
	
	rpc AddMemberContribution ( AddMemberContributionReq ) returns ( AddMemberContributionResp ){
 		option( tlvpickle.CmdID ) = 1;
 		option( tlvpickle.OptString ) = "u:g:t:n:o:d:a:y:l:";
 		option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -t <oper_type> -n <oper_value> [-o <order_id> -d <order_desc> -a <opt_invalid> -y <continuous_days> -l <supplement_day_list, split with ','>]";
 	}

	rpc GetMemberContribution ( GetMemberContributionReq ) returns ( GetMemberContributionResp ){
 		option( tlvpickle.CmdID ) = 2;
 		option( tlvpickle.OptString ) = "u:g:";
 		option( tlvpickle.Usage ) = "-u <uid> -g <guild_id>";
 	}
	
	rpc BatchGetMemberContribution ( BatchGetMemberContributionReq ) returns ( BatchGetMemberContributionResp ){
 		option( tlvpickle.CmdID ) = 3;
 		option( tlvpickle.OptString ) = "u:g:";
 		option( tlvpickle.Usage ) = "-u <uid_list, split with ','> -g <guild_id>";
 	}
	
	rpc GetMemberContributionListInOrder ( GetMemberContributionListInOrderReq ) returns ( BatchGetMemberContributionResp ){
 		option( tlvpickle.CmdID ) = 4;
 		option( tlvpickle.OptString ) = "g:o:l:t:";
 		option( tlvpickle.Usage ) = "-g <guild_id> -o <offset> -l <limit> -t <rank_type>";
 	}

	rpc GetMemberContributionDetailList ( GetMemberContributionDetailListReq ) returns ( GetMemberContributionDetailListResp ){
 		option( tlvpickle.CmdID ) = 5;
 		option( tlvpickle.OptString ) = "u:g:o:l:";
 		option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -o <offset> -l <limit>";
 	}
	
	rpc ClearMemberContribution ( ClearMemberContributionReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 6;
 		option( tlvpickle.OptString ) = "u:g:";
 		option( tlvpickle.Usage ) = "-u <uid> -g <guild_id>";
 	}
	
	rpc GetGuildDonateOption ( GetGuildDonateOptionReq ) returns ( GetGuildDonateOptionResp ){
 		option( tlvpickle.CmdID ) = 7;
 		option( tlvpickle.OptString ) = "n:";
 		option( tlvpickle.Usage ) = "-n <member_lv>";
 	}
	
	rpc GetMemberSpecContTypeByDate ( GetMemberSpecContTypeByDateReq ) returns ( GetMemberContributionDetailListResp ){
 		option( tlvpickle.CmdID ) = 8;
 		option( tlvpickle.OptString ) = "u:g:t:p:";
 		option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -t <date_ts> -p <contribution_type>";
 	}
}