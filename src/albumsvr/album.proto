syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package Album;

enum UrlVersion
{
	URL_VERSION_DEFAULT = 0;    // v1
	URL_VERSION_QINIU = 1;      // v1
	URL_VERSION_OBS = 2;        // v2
}

message stPhoto
{
	required uint32 parent_album_id = 1;
	required uint32 child_album_id = 2;
	required uint32 creator = 3;
	required string thumb_url = 4;
	required string photo_url = 5;
	required uint32 create_at = 6;
	required uint32 photo_id = 7;
	optional uint32 url_version = 8;
}

message ChildAlbum {
	required uint32 child_album_id = 1;
	required string name = 2;
	required string thumb_url = 3;
	required bool is_default = 4;
	required uint32 create_at = 5;
	required uint32 photo_count = 6;
	required uint32 last_update_time = 7;
	required uint32 creator = 8;
	optional uint32 url_version = 9;
}


//--------------------------------------
// 创建子相册
message CreateAlbumReq
{
	required uint32 parent_id = 1;
	required bool is_default  = 2;
	required string name	  = 3;
	required uint32 creator	  = 4;
}

message CreateAlbumResp
{
	required uint32 parent_id = 1;
	required uint32 second_id = 2;
}

//--------------------------------------
// 生成主相册id
message GetNextAlbumIdResp
{
	required uint32 album_id = 1;
}

//--------------------------------------
// 创建相片
message CreatePhotoReq
{	
	required uint32 parent_album_id = 1;
	required uint32 child_album_id = 2;
	repeated stPhoto photo_list = 3;
}

message CreatePhotoResp{
	repeated uint32 photo_id_list = 1;
}

//--------------------------------------
// 查相册列表
message GetChildAlbumListReq {
	required uint32 parent_album_id = 1;
	required uint32 offset = 2;
	required uint32 size = 3;
}

message GetChildAlbumListResp {
	repeated ChildAlbum child_album_list = 1;
}

//--------------------------------------
// 查相册照片数量
message GetChildAlbumPhotoCountReq {
	required uint32 parent_album_id = 1;
}

message GetChildAlbumPhotoCountResp {
	required uint32 photo_count = 1;
}


//--------------------------------------
// 查相片列表
message GetChildAlbumPhotoListReq {
	required uint32 parent_album_id = 1;
	required uint32 child_album_id = 2;
	required uint32 offset = 3;
	required uint32 size = 4;
}

message GetChildAlbumPhotoListResp {
	repeated stPhoto photo_list = 1;
}

//---------------------------------------
// 删除子相册，同时删除子相册内的所有照片
message DeleteChildAlbumReq {
	required uint32 parent_album_id = 1;
	required uint32 child_album_id = 2;
}

message DeleteChildAlbumResp {
}

//---------------------------------------
// 删除照片
message DeletePhotoReq {
	required uint32 parent_album_id = 1;
	required uint32 child_album_id = 2;
	required uint32 photo_id = 3;
}

message DeletePhotoResp {
}

//---------------------------------------
// 单查照片信息
message GetPhotoReq {
	required uint32 parent_album_id = 1;
	required uint32 child_album_id = 2;
	required uint32 photo_id = 3;
}

message GetPhotoResp {
	optional stPhoto photo = 1;
}

//---------------------------------------
// 单查相册信息
message GetChildAlbumReq {
	required uint32 parent_album_id = 1;
	required uint32 child_album_id = 2;
}

message GetChildAlbumResp {
	optional ChildAlbum album = 1;
}

message ModifyAlbumNameReq{
	required uint32 parent_album_id = 1;
	required uint32 child_album_id = 2;
	required string name = 3;
}

message ModifyAlbumThumbReq
{
	required uint32 parent_album_id = 1;
	required uint32 child_album_id = 2;
	required string thumb_url = 3;
	optional uint32 url_version = 4;
}

message ModifyAlbumThumbResp
{
	
}

service Album {
	option( tlvpickle.Magic ) = 14960;		// 服务监听端口号
	
	rpc CreateAlbum( CreateAlbumReq ) returns( CreateAlbumResp ){
		option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "p:d:n:u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <parent id> -d <is default album> -n <album name> -u <creator uid>";	// 测试工具的命令号帮助
	}
	
	rpc GetNextAlbumId( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetNextAlbumIdResp ){
		option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
	}
	
	rpc CreatePhoto( CreatePhotoReq ) returns ( CreatePhotoResp ){
		option( tlvpickle.CmdID ) = 3;										// 命令号
        option( tlvpickle.OptString ) = "p:c:u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <parent album id> -c <child album id> -u <creator uid>";
	}
	
	rpc GetChildAlbumList( GetChildAlbumListReq ) returns ( GetChildAlbumListResp ){
		option( tlvpickle.CmdID ) = 4;										// 命令号
        option( tlvpickle.OptString ) = "p:o:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <parent album id> -o <offset> -s <size>";
	}
	
	rpc GetChildAlbumPhotoList( GetChildAlbumPhotoListReq ) returns ( GetChildAlbumPhotoListResp ){
		option( tlvpickle.CmdID ) = 5;										// 命令号
        option( tlvpickle.OptString ) = "p:c:o:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <parent album id> -c <child album id> -o <offset> -s <size>";
	}
	
	rpc DeleteChildAlbum( DeleteChildAlbumReq ) returns ( DeleteChildAlbumResp ) {
		option( tlvpickle.CmdID ) = 6;										// 命令号
        option( tlvpickle.OptString ) = "p:c:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <parent album id> -c <child album id>";
	}

	rpc DeletePhoto( DeletePhotoReq ) returns ( DeletePhotoResp ) {
		option( tlvpickle.CmdID ) = 7;										// 命令号
        option( tlvpickle.OptString ) = "p:c:h:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <parent album id> -c <child album id> -h <photo id>";
	}
	
	rpc GetPhoto( GetPhotoReq ) returns ( GetPhotoResp ) {
		option( tlvpickle.CmdID ) = 8;										// 命令号
        option( tlvpickle.OptString ) = "p:c:h:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <parent album id> -c <child album id> -h <photo id>";
	}
	
	rpc GetChildAlbum( GetChildAlbumReq ) returns ( GetChildAlbumResp ) {
		option( tlvpickle.CmdID ) = 9;										// 命令号
        option( tlvpickle.OptString ) = "p:c:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <parent album id> -c <child album id> ";
	}
	
	rpc ModifyAlbumName( ModifyAlbumNameReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 10;										// 命令号
        option( tlvpickle.OptString ) = "p:c:n:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <parent album id> -c <child album id> -n <name>";
	}

	rpc GetChildAlbumPhotoCount ( GetChildAlbumPhotoCountReq ) returns ( GetChildAlbumPhotoCountResp ){
		option( tlvpickle.CmdID ) = 11;										// 命令号
        option( tlvpickle.OptString ) = "p:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-p <parent album id>";
	}
	
	rpc ModifyAlbumThumb ( ModifyAlbumThumbReq ) returns ( ModifyAlbumThumbResp ){
		option( tlvpickle.CmdID ) = 12;										// 命令号
		option( tlvpickle.OptString ) = "p:";							// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-p <parent album id>";
	}	
}
