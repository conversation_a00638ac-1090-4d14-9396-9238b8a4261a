syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package imattachment;

message ImMsgCache {
    uint32 target_uid = 1;
    uint32 target_group = 2;
    uint32 svr_msg_id = 3;
    bytes msg = 4;                         // Timeline::ImMsg
}

message ImInfo {
    uint32 type = 1;                        // ga::IM_MSG_TYPE
    string from_account = 2;
    string target_account = 3;
    repeated ImMsgCache cache_msg_list = 4;
}

message FileExtraInfo {
    ImInfo im = 1;
    bytes client_prop = 2;                  // 客户端带上来的文件属性
    map<string, string> custom_params = 3;
}

enum FileType {
    TYPE_UNKNOWN = 0;
    TYPE_TEXT = 1;
    TYPE_IMAGE = 2;
    TYPE_VOICE = 3;
    TYPE_VIDEO = 4;
}

message FileProperty {
    string key = 1;
    uint32 type = 2;                        // FileType
    uint32 size = 3;                        // byte
    uint32 creator = 4;
    uint32 create_at = 5;
    uint32 expire_at = 6;
    FileExtraInfo extra = 7;
    string mime_type = 8;
}

message File {
    FileProperty prop = 1;
    bytes data = 2;
}

message AddFileReq {
    File file = 1;
}

message AddFileResp {
    string ret_url = 1;
}

message GetFileReq {
    string key = 1;
}

message GetFileResp {
    File file = 1;
}

message GetFilePropertyReq {
    string key = 1;
}

message GetFilePropertyResp {
    FileProperty prop = 1;
}

service ImAttachment {
    option( tlvpickle.Magic ) = 14601;

    rpc AddFile ( AddFileReq ) returns ( AddFileResp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "k:t:u:e:d:p:";
        option( tlvpickle.Usage ) = "-k <key> -t <type> -u <creator> -e <ttl> -d <input_data> -p <input_path>";
    }

    rpc GetFile ( GetFileReq ) returns ( GetFileResp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "k:p:";
        option( tlvpickle.Usage ) = "-k <key> -p <output_path>";
    }

    rpc GetFileProperty ( GetFilePropertyReq ) returns ( GetFilePropertyResp ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "k:";
        option( tlvpickle.Usage ) = "-k <key>";
    }

}
