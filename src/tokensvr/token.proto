syntax = "proto2";


import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Token;

enum TokenBizType {
  TokenBiz_SDK = 1;
  TokenBiz_TT_WEB = 2;
}

message TokenSeed {
  required uint32 uid = 1;
  required string seed = 2;
  required uint32 expire_time = 3;
}

message CreateTokenSeedReq {
  required uint32 biz = 1;
  required bool replace_existed = 2;    // 是否覆盖已经存在且未过期的TokenSeed
}

message CreateTokenSeedResp {
  required TokenSeed token_seed = 1;
}

message DecodeTokenReq {
  required bytes token = 1;
  required uint32 biz = 2;
  required bool refresh_expire = 3;
}

message DecodeTokenResp {
  required uint32 uid = 1;
}

message ClearTokenReq {
  required uint32 uid = 1;
}

message ClearTokenResp {
}

service Token {
  option(tlvpickle.Magic) = 15131;
  //	option( tlvpickle.ServerName ) = "token";

  rpc CreateTokenSeed(CreateTokenSeedReq) returns(CreateTokenSeedResp) {
    option(tlvpickle.CmdID) = 1;        // 命令号
    option(tlvpickle.OptString) = "u:b:r:";    // 测试工具的命令号参数， 注意最后的冒号
    option(tlvpickle.Usage) = "-u <uid> -b <biz> -r <replace_existed>";    // 测试工具的命令号帮助
  }

  rpc DecodeToken(DecodeTokenReq) returns(DecodeTokenResp) {
    option(tlvpickle.CmdID) = 2;
    option(tlvpickle.OptString) = "t:b:r:";
    option(tlvpickle.Usage) = "-t <token> -b <biz> -r <refresh_expire>";
  }

  rpc ClearToken(ClearTokenReq) returns(ClearTokenResp) {
    option(tlvpickle.CmdID) = 3;
    option(tlvpickle.OptString) = "u:";
    option(tlvpickle.Usage) = "-u <uid>";
  }
}
