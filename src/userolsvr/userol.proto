syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package userol;

message UserOnlineInfo {
    uint32 uid = 1;
    uint64 online_at = 2;                   // 毫秒
    uint64 offline_at = 3;                  // 毫秒，大于 0 时表示离线记录
    string proxy_ip = 4;
    uint32 proxy_port = 5;
    uint32 client_id = 6;
    string client_ip = 7;
    string device_id = 8;
    uint32 terminal_type = 9;               // protocol.h (platform, os, appid)
    string device_model = 10;
    uint32 market_id = 11;

    bool is_login = 12; // internal, ignore
    uint32 client_version = 13;
}

// phone, pc, 小程序 多端同时在线信息
message UserMultiOnlineInfo {
    uint32 uid = 1;
    repeated UserOnlineInfo info_list = 2;
}

message GetUserOnlineInfoReq {
    repeated uint32 uid_list = 1;
}
message GetUserOnlineInfoResp {
    repeated UserMultiOnlineInfo info_list = 1;
}

message GetUserOnlineLogReq {
    uint32 uid = 1;	
    uint64 begin_at = 2;                    // online_at ms
    uint64 end_at = 3;
    uint32 limit = 4;
}
message GetUserOnlineLogResp {
    repeated UserOnlineInfo info_list = 1;
}

// 表示创建或断开连接
message AddUserOnlineInfoReq {
    UserOnlineInfo info = 1;
}
message AddUserOnlineInfoResp {
}
// 表示登录成功，补充 device_model, market_id
message AddUserLoginInfoReq {
    UserOnlineInfo info = 1;
}
message AddUserLoginInfoResp {
}

service UserOL {
    option( tlvpickle.Magic ) = 15229;

    rpc AddUserOnlineInfo (AddUserOnlineInfoReq) returns (AddUserOnlineInfoResp) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:i:o:t:n:d:e:m:";
        option( tlvpickle.Usage ) = "-u <uid> -i <online_at> -o <offline_at> -t <terminal_type> -n <client_id> -d <device_id> -e <device_model> -m <market_id>";
    }

    // 用户多端在线和离线信息，至少保存 60 天
    rpc GetUserOnlineInfo (GetUserOnlineInfoReq) returns (GetUserOnlineInfoResp) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:b";
        option( tlvpickle.Usage ) = "-u <uid> -b <ret_current_online>";
    }

    // 用户上下线日志，用于问题定位，保存 7 天
    rpc GetUserOnlineLog (GetUserOnlineLogReq) returns (GetUserOnlineLogResp) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:b:e:l:";
        option( tlvpickle.Usage ) = "-u <uid> -b <begin_at> -e <end_at> -l <limit>";
    }

    rpc AddUserLoginInfo (AddUserLoginInfoReq) returns (AddUserLoginInfoResp) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "u:i:t:n:d:e:m:";
        option( tlvpickle.Usage ) = "-u <uid> -i <online_at> -t <terminal_type> -n <client_id> -d <device_id> -e <device_model> -m <market_id>";
    }
}
