syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package imstranger;

message IsStrangerMsgSenderReq {
    uint32 sender = 1;
    uint32 target = 2;
}

message IsStrangerMsgSenderResp {
    bool result = 1;
}

message CheckStrangerMsgTargeterReq {
    uint32 sender = 1;
    uint32 target = 2;
}

message CheckStrangerMsgTargeterResp {
    bool result = 1;
}

message CheckStrangerMsgLimitReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
    string ip = 3;
    string check_man_cnt_key = 4;
}

message CheckStrangerMsgLimitResp {
    uint32 result = 1;
    bool is_new_stranger_target_user = 2;
    bool is_new_stranger_ip = 3;
}

message IncrStrangerMsgLimitReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
    string ip = 3;
    string check_man_cnt_key = 4;
    bool is_new_stranger_target_user = 5;
    bool is_new_stranger_ip = 6;
}

message SetHighConsumeIn24HourReq{
    uint32 uid = 1;
}
message SetHighConsumeIn24HourResp{
}

message IsHighConsumeIn24HourReq{
    uint32 uid = 1;
}
message IsHighConsumeIn24HourResp{
    bool isHighConsume = 1;
}

message IncrStrangerMsgLimitResp {
    uint32 result = 1;
}

/*聊天奖励 每日发起的前5个会话*/
message ChatAwardReq{
    uint32 uin = 1;
    uint32 targetUid = 2;
}
message ChatAwardResp{
    bool awardUin = 1;  /*true 符合条件 发奖*/
    bool awardTarget = 2;  /*true 符合条件 发奖*/
}

// 用户向陌生人聊天 targetuid的集合查询（当天）
message GetStrangerGreetDetailReq{
    uint32 uid = 1;
}
message GetStrangerGreetDetailResp{
    repeated uint32 target_uids = 1;
}

// 检查用户有没有到被撩上限 批量
message BatchCheckUserRecvLimitReq{
    uint32 uid = 1;
    repeated uint32 uid_list = 2;
}
message LimitInfo{
    uint32 uid = 1;
    bool isLimit = 2;
}
message BatchCheckUserRecvLimitResp{
    repeated LimitInfo result_list = 1;
}

// 设置当天IM送礼记录
message SetIMPresentToTargetReq{
    uint32 uid = 1;
    uint32 target_uid = 2;
}
message SetIMPresentToTargetResp{
}
message CheckIMPresentToTargetReq{
    uint32 uid = 1;
    uint32 target_uid = 2;
}
message CheckIMPresentToTargetResp{
    bool has_record = 1;
}


service ImStranger {
    option( tlvpickle.Magic ) = 15701;

    // 是否是陌生人聊天的发起者
    rpc IsStrangerMsgSender ( IsStrangerMsgSenderReq ) returns ( IsStrangerMsgSenderResp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u <sender> -t <target uid>";
    }

    // 是陌生人聊天的接收者，需要记录回复记录
    rpc CheckStrangerMsgTargeter ( CheckStrangerMsgTargeterReq ) returns ( CheckStrangerMsgTargeterResp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u <sender> -t <target uid>";
    }

    rpc CheckStrangerMsgLimit ( CheckStrangerMsgLimitReq ) returns ( CheckStrangerMsgLimitResp ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc IncrStrangerMsgLimit ( IncrStrangerMsgLimitReq ) returns ( IncrStrangerMsgLimitResp ) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }


    rpc SetHighConsumeIn24Hour ( SetHighConsumeIn24HourReq ) returns ( SetHighConsumeIn24HourResp ) {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }


    rpc IsHighConsumeIn24Hour ( IsHighConsumeIn24HourReq ) returns ( IsHighConsumeIn24HourResp ) {
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc ChatAward ( ChatAwardReq ) returns ( ChatAwardResp ) {
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u uin -t targetUid";
    }

    rpc GetStrangerGreetDetail ( GetStrangerGreetDetailReq ) returns ( GetStrangerGreetDetailResp ) {
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u uid ";
    }

    // 检查用户有没有到被撩上限 批量
    rpc BatchCheckUserRecvLimit ( BatchCheckUserRecvLimitReq ) returns ( BatchCheckUserRecvLimitResp ) {
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u uid ";
    }
    // 设置当天IM送礼记录
    rpc SetIMPresentToTarget ( SetIMPresentToTargetReq ) returns ( SetIMPresentToTargetResp ) {
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u uid ";
    }

    rpc CheckIMPresentToTarget ( CheckIMPresentToTargetReq ) returns ( CheckIMPresentToTargetResp ) {
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u uid ";
    }

}