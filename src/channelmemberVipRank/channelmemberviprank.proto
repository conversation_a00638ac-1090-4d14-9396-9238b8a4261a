syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";	

// namespace
package channelmemberviprank;


message ChannelMemberVip {

	optional uint32 curr_level_id   = 1;   // 成员当前的VIP ID 
	optional string curr_level_name = 2;   // 成员当前VIP ID 对应的Vip名称
	optional uint32 curr_level_value = 3;  // 成员当前的VIP等级数值	
	
	optional uint32 next_level_id = 4;         // 达到下一级的VIP ID
	optional string next_level_name = 5;       // 达到下一级的VIP等级名称
	optional uint32 next_level_min_value = 6;  // 达到下一集需要的最小VIP数值
    optional uint32 nobility_level = 7;        // 贵族等级
    optional bool   invisible = 8; //是否隐身
}

// 房间成员排名信息
message ChannelMemberRank {
    required uint32 uid  = 1;         // 成员uid
    optional uint32 ts   = 2;         // 时间
	
	optional uint32 rank_value  = 3;  // 分数
	optional ChannelMemberVip vip_level_info = 4; // vip信息

    optional uint32 rank = 5;         // 排名
    optional uint32 total_consum = 6; // 历史总消费 ,在线榜用
}


// 获取频道成员排序列表
message GetMemberRankListReq
{
	required uint32 channel_id = 1;
	required uint32 op_uid = 2;
	required uint32 start_idx = 3;
	required uint32 getsize = 4;

    // 需要获取自己的排名信息才填以下字段
    optional uint32 uid = 5;
    optional uint32 view_cnt = 6; // 半屏页榜单上展示个数

    optional bool is_test_mod = 7; // 测试模式，测试时用
    optional bool invisible_vaild = 8; // 隐身是否不展示

    optional bool is_live_channel = 9; // 是否是语音直播房
    //optional uint32 live_id = 10;      // 语音直播场次

}

message GetMemberRankListResp
{
	required uint32 channel_id = 1;
	repeated ChannelMemberRank member_list = 2;
	required uint32 all_member_size = 3;

    optional ChannelMemberRank my_rank_info = 4; 
    optional uint32 d_value = 5;    // 距离上一名的差值
    optional uint32 rank = 6;       // 我的排名
}

// 获取房间消费土豪榜
message MemberConsumeInfo 
{
	required uint32 uid = 1;
	required uint32 consume_cnt = 2;
	
	optional ChannelMemberVip vip_level_info = 3;
    optional bool is_auto_hidden_consume = 4; //用户是否被自动隐藏消费
}

message GetConsumeTopNReq 
{
	required uint32 channel_id = 1;
	required uint32 begin_idx = 2;
	required uint32 limit  = 3;

    // 需要获取自己的排名信息才填以下字段
    optional uint32 uid = 4;
    optional uint32 view_cnt = 5; // 半屏页榜单上展示个数
    optional bool is_show_hidden = 6; //是否自动隐藏不活跃消费用户
    optional uint32 channel_type = 7;
}

message GetConsumeTopNResp 
{
	repeated MemberConsumeInfo consume_list = 1;
	optional uint32 total_cnt  = 2;

    optional MemberConsumeInfo my_rank_info = 3; 
    optional uint32 d_value = 4;    // 距离上一名的差值
    optional uint32 rank = 5;       // 我的排名
    optional bool is_can_hide_consume = 6;
    optional bool is_hidden_consume = 7; //用户是否已手动隐藏消费
    optional bool is_auto_hidden_consume = 8; //用户是否被自动隐藏消费
}

// 获取指定用户的消费信息信息
message GetUserConsumeInfoReq {
    required uint32 channel_id = 1;
    required uint32 uid = 2;
} 
message GetUserConsumeInfoResp {
	required MemberConsumeInfo consume_info = 1;
}

message BatGetUserConsumeInfoReq {
    required uint32 channel_id = 1;
    repeated uint32 uid_list = 2;
} 
message BatGetUserConsumeInfoResp {
	repeated MemberConsumeInfo consume_info = 1;
}


// 将用户移出房间成员列表 用于修正
message RemoveOnlineMemberRankReq
{
    required uint32 channel_id = 1;
    required uint32 uid = 2;
}

message RemoveOnlineMemberRankResp
{
    optional uint32 removed_uid = 1;
}


// 测试发送广播事件
message SendCommBreakingNewsReq{
    optional uint32 channel_id = 1;
    optional uint32 uid = 2;
} 
message SendCommBreakingNewsResp {

}

// 运维补发房间礼物
message OperAddUserChannelTCoinReq{
    optional uint32 channel_id = 1;
    optional uint32 uid = 2;
    optional uint32 tcoin_cnt = 3;
    optional string order_id = 4;
} 
message OperAddUserChannelTCoinResp {
	optional bool is_orderid_exist = 1;
}

// 麦下用户列表
message GetUnderTheMircoRankReq
{
  required uint32 channel_id = 1;
  required uint32 begin =2;
  required uint32 limit =3;
}

message GetUnderTheMircoRankResp
{
  repeated uint32 uid_list = 1;
  required uint32 curr_member_total = 2;      // 房间当前人员数量
}

// 基于 麦下用户 计算房间在线人数，不是 100% 准确
message GetUnderTheMircoOnlineCntReq
{
    repeated uint32 cid_list = 1;
}

message SimpleChannelOnlineCount
{
    required uint32 cid = 1;
    required uint32 online_count = 2;
}
message GetUnderTheMircoOnlineCntResp
{
    repeated SimpleChannelOnlineCount info_list = 1;
}

message GetMemberWeekRankListReq {
    required uint32 cid = 1;
    required uint32 begin = 2;  
    required uint32 limit = 3;

    // 需要获取自己的排名信息才填以下字段
    optional uint32 uid = 4;
    optional uint32 view_cnt = 5; // 半屏页榜单上展示个数
}

message GetMemberWeekRankListResp {
    repeated MemberConsumeInfo rank_info_list = 1;

    optional MemberConsumeInfo my_rank_info = 2; 
    optional uint32 d_value = 3;    // 距离上一名的差值
    optional uint32 rank = 4;       // 我的排名
}

// 小时榜类型 与ga::ChannelHourRankType保持一致
enum ChannelHourRankType {
    TOTAL = 0;           // 小时总榜

    /* 其他的用2000以上的娱乐tag下的tag_id做类型值 */
}

message ChannelHourRankInfo {
    required uint32 cid = 1;
    required uint32 score = 2;   // 分数
    required uint32 rank = 3;   // 排名
    optional uint32 d_value = 4; // 分数差值（当排名为第一名时d_value为与第二名的差值，否则d_value为与上一名的差值）
    optional uint32 tag_id = 5; // (仅在ChannelHourRankType 为 TotalHourRank 时有效)房间所在分类的tag_id
}

message GetChannelHourRankListReq {
    required uint32 type = 1;       // see ChannelHourRankType
    required uint32 hour = 2;
    required uint32 begin = 3;  
    required uint32 limit = 4;

    // 需要获取本房间的排名信息才填以下字段
    optional uint32 cid = 5;            // 当前房间id
    optional uint32 view_cnt = 6; // 半屏页榜单上配置的展示个数 
}

message GetChannelHourRankListResp {
    repeated ChannelHourRankInfo rank_info_list = 1;
    required uint32 real_view_cnt = 2;              // 当前半屏页榜单上实际展示个数
    optional ChannelHourRankInfo my_rank_info = 3;  // 当前房间的排名信息

}

message GetHourRankByIdReq {
    required uint32 cid = 1;
    required uint32 type = 2;       // see ChannelHourRankType
    required uint32 hour = 3;
    required uint32 view_cnt = 4;   // 半屏页榜单上配置的展示个数
}

message GetHourRankByIdResp {
    required ChannelHourRankInfo rank_info = 1;
    required uint32 real_view_cnt = 2;          // 当前半屏页榜单上实际展示个数
}

// 测试用 增加小时榜分数
message IncrChannelHourRankScoreReq {
    required uint32 cid = 1;
    required uint32 type = 2;       // see ChannelHourRankType
    required uint32 hour = 3;
    required uint32 score = 4;
}
message IncrChannelHourRankScoreResp {}

// 测试用 增加房间成员周榜分数
message IncrMemberWeekRankScoreReq {
    required uint32 cid = 1;
    required uint32 uid = 2;      
    required uint32 ts = 3;  // 时间
    required uint32 score = 4;
}
message IncrMemberWeekRankScoreResp {}

message UpdateUserTempInvisibleStatusReq
{
    required uint32 uid = 1;
    required uint32 channel_id = 2;
    required bool invisible = 3;
    required uint32 level = 4;
    optional uint32 channel_type = 5;
}

message UpdateUserTempInvisibleStatusResp
{
}

message GetMemberNobilityInfoReq
{
    required uint32 uid = 1;
    required uint32 channel_id = 2;
}

message GetMemberNobilityInfoResp
{
    required uint32 nobility_level = 1;
    required bool   invisible = 2;
}

//（语音直播主播下播时）清除直播房在线榜
message RemoveLiveChannelOnlineRankReq
{
    required uint32 channel_id = 1;
}

message RemoveLiveChannelOnlineRankResp{}

message HideUserChannelConsumeReq
{
    required uint32 uid = 1;
    required uint32 channel_id = 2;
    required bool is_hide = 3; // true 代表隐藏，false 代表取消隐藏
}

message HideUserChannelConsumeResp{}

service ChannelMemberVipRank {
	option( tlvpickle.Magic ) = 15643;		// 服务监听端口号
	
	// 获取房间的排名列表
	rpc GetMemberRankList( GetMemberRankListReq ) returns( GetMemberRankListResp ){
	option( tlvpickle.CmdID ) = 1;										
        option( tlvpickle.OptString ) = "x:b:l:u:m:";							
        option( tlvpickle.Usage ) = "-x <channel id> -b <begin idx> -l <limit> -u <uid> -m <view_cnt>";
	}
	
	// 获取指定用户的排名信息
	rpc GetUserConsumeInfo( GetUserConsumeInfoReq ) returns( GetUserConsumeInfoResp ){
	option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "x:u:";							
        option( tlvpickle.Usage ) = "-x <channelID> -u <target uid> ";
	}
	
	// 获取房间的消费榜
	rpc GetConsumeTopN( GetConsumeTopNReq ) returns( GetConsumeTopNResp ){
	option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "x:b:l:u:m:";							
        option( tlvpickle.Usage ) = "-x <channel id> -b <begin idx> -l <limit> -u <uid> -m <view_cnt>";
	}
	
	// 批量获取指定用户的排名信息
	rpc BatGetUserConsumeInfo( BatGetUserConsumeInfoReq ) returns( BatGetUserConsumeInfoResp ){
	option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "x:u:";							
        option( tlvpickle.Usage ) = "-x <channel id> -u <target uid>";
	}
	
	// 麦下用户列表
	rpc GetUnderTheMircoRank( GetUnderTheMircoRankReq ) returns( GetUnderTheMircoRankResp ){
	    option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "c:u:b:l";							
        option( tlvpickle.Usage ) = "-c <channel id> -u <uid> -b <begin> -l <limit>";
	}

    // 基于 麦下用户列表 计算房间在线人数
	rpc GetUnderTheMircoOnlineCnt( GetUnderTheMircoOnlineCntReq ) returns( GetUnderTheMircoOnlineCntResp ){
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "c:";							
        option( tlvpickle.Usage ) = "-c <channel id> ";
	}
    
    // 获取房间成员周流水榜
    rpc GetMemberWeekRankList( GetMemberWeekRankListReq ) returns ( GetMemberWeekRankListResp ){
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "x:b:l:u:m:";							
        option( tlvpickle.Usage ) = "-x <channel id> -b <begin> -l <limit> -u <uid> -m <view_cnt>";
    }
     
    // 获取房间小时榜
    rpc GetChannelHourRankList( GetChannelHourRankListReq ) returns ( GetChannelHourRankListResp ){
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "x:b:l:t:m:h:";							
        option( tlvpickle.Usage ) = "-x <channel id> -b <begin> -l <limit> -t <type> -m <view_cnt> -h <hour>";
    }

    // 通过房间id获取房间在小时榜中的排名信息
    rpc GetHourRankById( GetHourRankByIdReq ) returns ( GetHourRankByIdResp ){
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "x:t:m:h:";							
        option( tlvpickle.Usage ) = "-x <channel id> -t <type> -m <view_cnt> -h <hour>";
    }

	// 将用户移出房间成员列表 用于修正数据
	rpc RemoveOnlineMemberRank( RemoveOnlineMemberRankReq ) returns( RemoveOnlineMemberRankResp ){
	option( tlvpickle.CmdID ) = 100;
        option( tlvpickle.OptString ) = "x:u:";							
        option( tlvpickle.Usage ) = "-x <channel id> -u <uid>";
	}
	
	// 测试发送全网大事记消息
	rpc SendCommBreakingNews( SendCommBreakingNewsReq ) returns( SendCommBreakingNewsResp ){
	option( tlvpickle.CmdID ) = 200;
        option( tlvpickle.OptString ) = "x:u:";							
        option( tlvpickle.Usage ) = "-x <channel id> -u <uid>";
	}
	// 运维给用户补发房间T豆消费
	rpc OperAddUserChannelTCoin( OperAddUserChannelTCoinReq ) returns( OperAddUserChannelTCoinResp ){
	option( tlvpickle.CmdID ) = 201;
        option( tlvpickle.OptString ) = "x:u:b:d:";							
        option( tlvpickle.Usage ) = "-x <channel id> -u <uid> -b <tcoin cnt> -d <order_id>";
	}

    // 测试用 增加小时榜分数
    rpc IncrChannelHourRankScore( IncrChannelHourRankScoreReq ) returns ( IncrChannelHourRankScoreResp ){
        option( tlvpickle.CmdID ) = 202;
        option( tlvpickle.OptString ) = "x:t:h:s:";							
        option( tlvpickle.Usage ) = "-x <channel id> -t <type> -h <hour> -s <score>";
	}

    // 测试用 增加房间成员周榜分数
    rpc IncrMemberWeekRankScore( IncrMemberWeekRankScoreReq ) returns ( IncrMemberWeekRankScoreResp ){
        option( tlvpickle.CmdID ) = 203;
        option( tlvpickle.OptString ) = "x:u:t:s:";							
        option( tlvpickle.Usage ) = "-x <channel id> -u <uid> -t <ts> -s <score>";
	}

    //设置房间临时隐身状态
    rpc UpdateUserTempInvisibleStatus( UpdateUserTempInvisibleStatusReq ) returns( UpdateUserTempInvisibleStatusResp ){
        option( tlvpickle.CmdID ) = 204;
        option( tlvpickle.OptString ) = "x:u:b:d:";                         
        option( tlvpickle.Usage ) = "-x <channel id> -u <uid> -b <invisible> -d <channel_id>";
    }

    //设置房间临时隐身状态
    rpc GetMemberNobilityInfo( GetMemberNobilityInfoReq ) returns( GetMemberNobilityInfoResp ){
        option( tlvpickle.CmdID ) = 205;
        option( tlvpickle.OptString ) = "x:u:";
        option( tlvpickle.Usage ) = "-x <channel id> -u <uid>";
    }

    //（语音直播主播下播时）清除直播房在线榜
    rpc RemoveLiveChannelOnlineRank( RemoveLiveChannelOnlineRankReq ) returns( RemoveLiveChannelOnlineRankResp ){
        option( tlvpickle.CmdID ) = 206;
        option( tlvpickle.OptString ) = "x:u:";
        option( tlvpickle.Usage ) = "-x <channel id> -u <uid>";
    }

    rpc HideChannelConsume( HideUserChannelConsumeReq ) returns( HideUserChannelConsumeResp ){
        option( tlvpickle.CmdID ) = 207;
        option( tlvpickle.OptString ) = "x:u:";
        option( tlvpickle.Usage ) = "-x <channel id> -u <uid>";
    }

}


