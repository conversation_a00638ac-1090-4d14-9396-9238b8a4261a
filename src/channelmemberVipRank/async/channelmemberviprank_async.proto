syntax="proto2";

package channelmemberviprank.async;

enum Type{
    ET_MEMBER_RANK_SORT_CHANGE = 1;      /* 房间成员列表排名有变化 */
    ET_MEMBER_VIP_CHANGE = 2;            /* vip等级 */
    ET_BREAKING_NEWS = 3;                /* 大事件 */
    ET_MEMBER_STATUS_CHECK = 4;          /* 成员状态的检查 */
}

// 房间成员列表的排名有变化
message MemberRankChangeNotify
{
	required uint32 chid = 1;
	repeated uint32 notify_uid_list = 2;
	optional uint32 channel_member_cnt   = 3;
}

// vip等级
message MemberVipChangeNotify
{
	required uint32 chid = 1;
	required uint32 uid = 2;
	required bool is_new_upgrade = 3;
	
	optional uint32 curr_level_id   = 4;   // 成员当前的VIP ID 
	optional string curr_level_name = 5;   // 成员当前VIP ID 对应的Vip名称
	optional uint32 curr_level_value = 6;  // 成员当前的VIP等级数值	
	
	optional uint32 next_level_id = 7;         // 达到下一级的VIP ID
	optional string next_level_name = 8;       // 达到下一级的VIP等级名称
	optional uint32 next_level_min_value = 9;  // 达到下一集需要的最小VIP数值
}

// 全服大事件通知
message ConsumeBreakingNewsNotify
{
	required uint32 chid = 1;
	required uint32 uid = 2;

	optional uint32 breaking_type   = 3; 
}

// 
message CheckMemberRankListOnlineStatus
{
	required uint32 chid = 1;
}