syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package guildcircle;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list


/**
 *	公会圈子
 */
message CircleBaseInfo {
	required uint32 guild_id = 1;			// 所在guild
	required uint32 today_topic_count = 2;	// 今日话题数
	optional uint32 topic_count = 3;		// 主题数
}

/**
 *	主题
 */
 // 主题基本信息
message GuildTopicBase {

    enum TOPIC_STATUS {
        NORMAL = 0;			// 正常
        DELETED = 1;		// 主题被删除
    }

	required uint32 topic_id = 1;		// 主题id
	required uint32 guild_id = 2;		// 圈子id
	required string title = 3;			// 标题
	required uint32 creator = 4;		// 发布者uid
	required uint32 create_time = 5;	// 发布时间
	required uint32 like_count = 6;		// 点赞数
	required uint32 comment_count = 7;	// 评论数
    required uint32 msg_seq_id = 8;     // 发消息者timeline中的seqId
    required uint32 status = 9;         // 状态, see TOPIC_STATUS
	repeated string img_list = 10;		// 图片列表
	optional uint32 report_count = 11;  // 举报次数
	optional uint32 offical_report = 12;// 官方举报
	optional uint32 tag = 13;			// 帖子状态
	optional uint32 last_time = 14;     // 最后评论时间
}

// 主题简要信息
message GuildTopicBrief {
	required GuildTopicBase base = 1;			// 主题基本部分
	required string	content_preview = 2;	// 内容预览
}

// 主题详细信息
message GuildTopicDetail {
	required GuildTopicBase base = 1;			// 主题基本部分
	required string content = 2;			// 完整内容
}

/**
 *	评论
 */
message CommentBase {
	enum COMMENT_STATUS {
        NORMAL = 0;			// 评论正常
        DELETED = 1;		// 评论被删除
	}
	
	required uint32 comment_id = 1;     // 评论id
	required uint32 guild_id = 2;      // 圈子id
	required uint32 topic_id = 3;       // 主题id
	required string content = 4;        // 评论内容
	required uint32 creator = 5;        // 评论者
	required uint32 create_time = 6;    // 评论时间
	required uint32 status = 7;         // 评论状态
	required uint32 msg_seq_id = 8;     // 发消息者timeline中的seqId
	required uint32 ref_comment_id = 9; // 该评论回复的目标评论id
	required uint32 like_count = 10;    // 点赞数
	optional uint32 report_count = 11;  // 举报次数
	optional uint32 offical_report = 12;// 官方举报
	repeated string img_list = 13;		// 评论图片
	optional int32 floor     = 14;      // 楼层 默认-1为老数据没有楼层概念, 0为回复评论也没有楼层概念 
	optional uint32 parent_comment_id = 15; // 该评论回复的祖先目标评论ID 0默认表示没有
	optional uint32 comment_type      = 16; // see ga::CircleTopicCommentType

}


message GuildCommentWithRef {
    required CommentBase comment = 1;
    optional CommentBase ref_comment = 2; // 被引用的评论
}

message CircleImageAttr {
    required string key = 1;
    required uint32 width = 2;
    required uint32 height = 3;
    optional string format = 4;
    optional string color_model = 5;
    optional uint32 frame_number = 6;
}


/**
 *	Request & Response
 */
// 创建一个公会圈子
message CreateGuildCircleReq {
    required uint32 guild_id = 1;
}

// 发表主题
message CreateTopicReq {
	required uint32 guild_id = 1;		// 圈子id
	required string title = 2;			// 标题
	required string content = 3;		// 主题内容
    required uint32 msg_seq_id = 4;     // 发消息者timeline中的seqId
	repeated string img_list = 5;		// 图片列表(may be full url)
	optional bool highlight = 6;		// 精华贴
}

message CreateTopicResp {
	required uint32 topic_id = 1;           // 主题id
    optional uint32 remain_cooldown = 2;    // 发生ERR_CIRCLE_POST_TOPIC_COOLINGDOWN错误时的剩余冷却时间
}

// 删除主题
message DeleteTopicReq {
	required uint32 guild_id = 1;
	required uint32 topic_id = 2;
}

message DeleteTopicResp {
}

// 发表评论
message CreateTopicCommentReq {
	required uint32 guild_id = 1;		// 圈子id
	required uint32 topic_id = 2;		// 主题id
	required string content = 3;		// 评论内容
	required uint32 msg_seq_id = 4;		// 发消息者timeline中的seqId
	optional uint32 ref_comment_id = 5;	// 引用的评论id
	optional uint32 user_type = 6; 		//用户类型
	optional string user_name = 7;		//用户账号名称
	repeated string img_list = 8;		// 图片列表(may be full url)
	optional uint32 anti_ad_flag = 9;   // 反广告标识 0x01表示不参与自动反广告 其他标识待定
}

message CreateTopicCommentResp {
	required uint32 comment_id = 1;         // 评论id
    optional uint32 remain_cooldown = 2;    // 发生ERR_CIRCLE_POST_COMMENT_COOLINGDOWN错误时的剩余冷却时间
	optional GuildCommentWithRef comment = 3;
}

// 删除评论
message DeleteTopicCommentReq {
	required uint32 guild_id = 1;		// 圈子id
	required uint32 topic_id = 2;		// 主题id
	required uint32 comment_id = 3;		// 评论id
}

message DeleteTopicCommentResp {
}

// 点赞
message LikeReq {
	required uint32 guild_id = 1;		// 圈子id
	required uint32 topic_id = 2;		// 主题id
	optional uint32 comment_id = 3;		// 评论id
}

message LikeResp {
    //required uint32 is_first_time_like = 1; // 是否首次赞该主题/评论
}

// 取消赞
message CancelLikeReq {
	required uint32 guild_id = 1;		// 圈子id
	required uint32 topic_id = 2;		// 主题id
	optional uint32 comment_id = 3;		// 评论id
}

message CancelLikeResp {

}

// 获取主题列表
message GetTopicsReq {
	required uint32 guild_id = 1;		// 圈子id
	required uint32 start_topic_id = 2;	// 起始主题id, 若从头查起, 则传0
	required uint32 count = 3;			// 数量
	optional uint32 tag = 4;
	optional uint32 sorttype = 5;       // TOPIC_SORT_TYPE
	optional uint32 userfrom = 6;		// 调用来源
    optional uint32 client_type = 7;    // see protodef
	
}

message GetTopicsResp {
	repeated GuildTopicBrief topic_brief_list = 1;		// 查列表只会返回brief
    repeated CircleImageAttr image_attr_list = 2;   // 相关主题图片的属性
}

// 查询主题详情
message GetTopicDetailReq {
	required uint32 guild_id = 1;		// 圈子id
	required uint32 topic_id = 2;		// 主题id
}

message GetTopicDetailResp {
	required GuildTopicDetail topic_detail = 1;	  // 主题详情
    repeated CircleImageAttr image_attr_list = 2;   // 相关主题图片的属性
}

// 获取主题的点赞列表
message GetTopicLikersReq {
	required uint32 guild_id = 1;
	required uint32 topic_id = 2;
	required uint32 offset = 3;
	required uint32 limit = 4;
}

message GetTopicLikersResp {
	repeated uint32 uid_list = 1;
}

// 获取某主题中所有被引用评论的作者列表
message GetTopicReferencedCommentCreatorsReq {
	required uint32 guild_id = 1;
	required uint32 topic_id = 2;
}

message GetTopicReferencedCommentCreatorsResp {
	repeated uint32 uid_list = 1;
}

enum CIRCLE_TOPIC_TAG {
    HIGHLIGHT = 1;
    TOP = 2;
    ACTIVITY = 4;
    GAME_DOWNLOAD = 8;
    SHIELD_ON_ANDROID = 16;
    SHIELD_ON_IOS = 32;
}

message CheckLikeResp {

    required uint32 like_status = 1;
}

// 举报某条主题
message ReportTopicReq {
	required uint32 guild_id = 1;
	required uint32 topic_id = 2;
}

message ReportTopicResp {
}

// 获取一个圈子下, 指定区间中被删除的主题id列表
message GetDeletedTopicIdListReq {
	required uint32 guild_id = 1;		// 圈子id
	required uint32 min_topic_id = 2;	// 区间最小
	required uint32 max_topic_id = 3;	// 区间最大
}

message GetDeletedTopicIdListResp {
	repeated uint32 deleted_topic_id_list = 3;	// 主题id列表
}

message GetCircleReq {
    required uint32 guild_id = 1;
}

message AddTopicTagReq {
	required uint32 guild_id = 1;
	required uint32 topic_id = 2;
	required uint32 tag = 3;
	required bool add = 4;
}
message AddTopicTagResp {
}

// 用过户点赞过的topic_id
message CheckCircleTopicsLikeReq {
	required uint32 guild_id = 1;
	repeated uint32 topic_id_list = 2;
}

message CheckCircleTopicsLikeResp {
	repeated uint32 topic_liked_list = 1;
}


message GetTopicLikedUsersReq{
	required uint32 guild_id = 1;
	required uint32 topic_id = 2;
	required uint32 start = 3;
	required uint32 count = 4;

}
message GetTopicLikedUsersResp{
	repeated uint32 topic_liked_user_list = 1;
}

message GetTopicCommentsReq{
	required uint32 guild_id = 1;
	required uint32 topic_id = 2;
	required uint32 start_comment_id = 3;
	required uint32 count = 4;
}
message GetTopicCommentsResp{
	repeated GuildCommentWithRef comment_list = 1;
}


message GuildCircleSimpleCommentInfo {
	required uint32 comment_id = 1;     // 评论id
	required uint32 guild_id = 2;       // 圈子id
	required uint32 topic_id = 3;       // 主题id
	required uint32 creator = 4;        // 评论者
	required uint32 create_time = 5;    // 评论时间
	required uint32 status = 6;         // 评论状态( ga circle2 TopicCommentStatusV3 mask)
	required uint32 msg_seq_id = 7;     // 发消息者timeline中的seqId
	required uint32 ref_comment_id = 8; // 该评论回复的目标评论id
	optional uint32 offical_report = 9; // 官方举报
	required uint32 parent_comment_id = 10; // 该评论回复的祖先目标评论ID 0默认表示没有
	required uint32 comment_type      = 11; // see ga circle2 TopicCommentType
}

message GuildCircleCommentWithSimpleRefInfo {
	required CommentBase comment = 1;
	optional GuildCircleSimpleCommentInfo ref_comment_info = 2; // 如果该条评论是回复其他的评论 那么此处有值：回复的目标评论的信息
}

message GuildCircleCommentWithReply {
	required GuildCircleCommentWithSimpleRefInfo comment = 1;
	required uint32 reply_comment_total_cnt = 2;
	repeated GuildCircleCommentWithSimpleRefInfo reply_comment_list = 3; // 如果该条评论被其他人回复过 此处有值 值为回复列表 默认为3条
}

message GetCommentsReq
{
	required uint32 guild_id = 1;
	required uint32 topic_id = 2;
	repeated uint32 comment_id_list = 3;
}

message GetCommentsResp
{
	repeated CommentBase comment_base_list = 1;
}

// 获取评论列表 2016-9-7 Add
message GetCommentListReq
{
	required uint32 guild_id = 1;         // 圈子id
	required uint32 topic_id = 2;          // 主题id
	required uint32 start_comment_id = 3;  // 
	required uint32 count = 4;             // 
	optional bool	include_start_id = 5;  // 是否包含 start_comment_id 本身，默认不包含
}

message GetCommentListResp
{
	repeated GuildCircleCommentWithReply comment_list = 1;
	optional uint32 nomal_comment_left_cnt = 2;  // 从 start_comment_id 开始还剩余多少评论 即剩余多少楼层
}


// 获取指定评论的回复列表 2016-9-7 Add
message GetCommentReplyListReq
{
	required uint32 guild_id = 1;         // 圈子id
	required uint32 topic_id = 2;          // 主题id
	required uint32 parent_comment_id = 3; // 指定的评论
	required uint32 start_comment_id = 4;  // 
	required uint32 count = 5;                   // 
}

message GetCommentReplyListResp
{
	repeated GuildCircleCommentWithSimpleRefInfo reply_comment_list = 1;
	required uint32 reply_comment_total_cnt = 2;
	optional uint32 reply_comment_left_cnt = 3; // 从 start_reply_comment_id 开始还剩余多少回复
	
}

message GetTopicCountReq{
	required uint32 guild_id = 1;
}

message GetTopicCountResp{
	required uint32 topic_count = 1;
}


// Circle服务
service GuildCircle {
	option( tlvpickle.Magic ) = 15308;		// 服务监听端口号

    rpc CreateGuildCircle( CreateGuildCircleReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 1;              // 命令号
        option( tlvpickle.OptString ) = "g:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id>";    // 测试工具的命令号帮助
    }

	/**
	 *	创建主题
	 */
	rpc CreateTopic( CreateTopicReq ) returns( CreateTopicResp ) {
		option( tlvpickle.CmdID ) = 6;			// 命令号
        option( tlvpickle.OptString ) = "u:x:t:e:s:p:i:";	// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u <uid> -x <guild_id> -t <title> -e <content> -s <seq_id> -i <img_url>";	// 测试工具的命令号帮助
	}

	/**
	 *	删除主题
	 */
	rpc DeleteTopic( DeleteTopicReq ) returns( DeleteTopicResp ) {
		option( tlvpickle.CmdID ) = 7;			// 命令号
        option( tlvpickle.OptString ) = "x:t:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <guild_id> -t <topic_id>";		// 测试工具的命令号帮助
	}

	/**
	 *	创建评论
	 */
	rpc CreateTopicComment( CreateTopicCommentReq ) returns( CreateTopicCommentResp ) {
		option( tlvpickle.CmdID ) = 8;			// 命令号
        option( tlvpickle.OptString ) = "u:x:t:s:n:r:p:i:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -x <guild_id> -t <topic_id> -s <seq_id> -n <content> -r <ref_comment_id> -i <img_url>" ;	// 测试工具的命令号帮助
	}

	/**
	 *	删除评论
	 */
	rpc DeleteTopicComment( DeleteTopicCommentReq ) returns( DeleteTopicCommentResp ) {
		option( tlvpickle.CmdID ) = 9;			// 命令号
        option( tlvpickle.OptString ) = "x:t:n:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <guild_id> -t <topic_id> -n <comment_id>";		// 测试工具的命令号帮助
	}

	/**
	 * 	点了个赞
	 */
	rpc Like( LikeReq ) returns( LikeResp ) {
		option( tlvpickle.CmdID ) = 10;			// 命令号
        option( tlvpickle.OptString ) = "u:x:t:n:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -x <guild_id> -t <topic_id> -n <comment_id>";		// 测试工具的命令号帮助
	}

	/**
	 *	取消赞
	 */
	rpc CancelLike( CancelLikeReq ) returns( CancelLikeResp ) {
		option( tlvpickle.CmdID ) = 11;			// 命令号
        option( tlvpickle.OptString ) = "u:x:t:n:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -x <guild_id> -t <topic_id> -n <comment_id>";		// 测试工具的命令号帮助
	}

	/**
	 *	获取圈子的主题列表
	 */
	rpc GetTopics( GetTopicsReq ) returns( GetTopicsResp ) {
		option( tlvpickle.CmdID ) = 12;			// 命令号
        option( tlvpickle.OptString ) = "x:s:n:t:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <guild_id> -s <start_with> -n <count> -t <tag>";			// 测试工具的命令号帮助
	}

	/**
	 *	获取特定主题的详细信息
	 */
	rpc GetTopicDetail( GetTopicDetailReq ) returns( GetTopicDetailResp ) {
		option( tlvpickle.CmdID ) = 13;			// 命令号
        option( tlvpickle.OptString ) = "x:t:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <guild_id> -t <topic_id>";			// 测试工具的命令号帮助
	}

	/**
	 *	帖子加精、取消
	 */
	rpc AddTopicTag( AddTopicTagReq ) returns( AddTopicTagResp ) {
		option( tlvpickle.CmdID ) = 14;			// 命令号
        option( tlvpickle.OptString ) = "x:t:a:i:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <guild_id> -t <topic_id> -a<add> -i<tag>";			// 测试工具的命令号帮助
	}

	/**
	 *	用户点过的赞
	 */
	rpc CheckCircleTopicsLike ( CheckCircleTopicsLikeReq ) returns( CheckCircleTopicsLikeResp ) {
		option( tlvpickle.CmdID ) = 15;			// 命令号
        option( tlvpickle.OptString ) = "u:x:t:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u<uid> -x <guild_id> -t <topic_id>";			// 测试工具的命令号帮助
	}

	/**
	 *	帖子点赞的用户
	 */
	rpc GetTopicLikedUsers ( GetTopicLikedUsersReq ) returns( GetTopicLikedUsersResp ) {
		option( tlvpickle.CmdID ) = 16;			// 命令号
        option( tlvpickle.OptString ) = "x:t:s:n:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <guild_id> -t <topic_id> -s<start> -n<count>";			// 测试工具的命令号帮助
	}
	
	/**
	 *	评论 
	 */
	rpc GetTopicComments ( GetTopicCommentsReq ) returns( GetTopicCommentsResp ) {
		option( tlvpickle.CmdID ) = 17;			// 命令号
        option( tlvpickle.OptString ) = "x:t:s:n:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <guild_id> -t <topic_id> -s<start> -n<count>";			// 测试工具的命令号帮助
	}

	/**
	 *	一级评论
	 */
	rpc GetCommentList ( GetCommentListReq ) returns( GetCommentListResp ) {
		option( tlvpickle.CmdID ) = 18;			// 命令号
        option( tlvpickle.OptString ) = "x:t:s:n:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <guild_id> -t <topic_id> -s<start> -n<count>";			// 测试工具的命令号帮助
	}
	/**
	 * 评论的回复
	 */
	rpc GetCommentReplyList ( GetCommentReplyListReq ) returns( GetCommentReplyListResp ) {
		option( tlvpickle.CmdID ) = 19;			// 命令号
        option( tlvpickle.OptString ) = "x:t:s:n:p:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <guild_id> -t <topic_id> -s<start> -n<count> -p<parent_comment_id>";			// 测试工具的命令号帮助
	}
	/**
	 * 获取指定的评论
	 */
	rpc GetComments ( GetCommentsReq ) returns( GetCommentsResp ) {
		option( tlvpickle.CmdID ) = 20;			// 命令号
        option( tlvpickle.OptString ) = "x:t:c:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <guild_id> -t <topic_id> -c<comment_id>";			// 测试工具的命令号帮助
	}

	// 话题数量
	rpc GetTopicCount ( GetTopicCountReq ) returns ( GetTopicCountResp ){
		option( tlvpickle.CmdID ) = 21;						// 命令号
		option( tlvpickle.OptString ) = "x:";				// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-x <guild_id>";				// 测试工具的命令号帮助
	}
}
