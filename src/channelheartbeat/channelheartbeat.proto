syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package channelheartbeat;

service ChannelHeartbeat {
    option( tlvpickle.Magic ) = 15207;
    rpc UpdateUserHeartbeat (UpdateUserHeartbeatReq) returns (UpdateUserHeartbeatResp) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:x:";
        option( tlvpickle.Usage ) = "-u <uid> -x <channel_id>";
    }

    rpc RemoveUserHeartbeat( RemoveUserHeartbeatReq ) returns (RemoveUserHeartbeatResp) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc CheckUserHeartRecordExist( CheckUserHeartRecordExistReq ) returns (CheckUserHeartRecordExistResp) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }
}

message UserHeartbeatInfo {
    uint32 uid = 1;
    uint32 channel_id = 2;
}

message UpdateUserHeartbeatReq {
    UserHeartbeatInfo info = 1;
}

message UpdateUserHeartbeatResp {
}

message RemoveUserHeartbeatReq {
    UserHeartbeatInfo info = 1;
}

message RemoveUserHeartbeatResp {
}

message CheckUserHeartRecordExistReq {
    repeated uint32 uid_list = 1;
}

message CheckUserHeartRecordExistResp {
    repeated  uint32 exist_uid_list = 1;
}