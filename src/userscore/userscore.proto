syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package userscore;

message StScoreConfig
{
  string title = 1;
  uint32 update_time = 2;
}

// 获取积分配置
message GetScoreConfigReq
{
}

message GetScoreConfigResp
{
  StScoreConfig config = 1;
}

// 获取积分配置的更新时间
message GetScoreConfigUpdateTimeReq
{
}

message GetScoreConfigUpdateTimeResp
{
  uint32 update_time = 1;
}

// 更新积分配置
message SetScoreConfigReq
{
  StScoreConfig config = 1;
}

// 获取用户的积分
message GetUserScoreReq
{
  uint32 uid = 1;
  uint32 score_type = 2;
}

message GetUserScoreResp
{
  uint32 score = 1;
}

enum ScoreType {
  Origin = 0;
  TbeanOnly = 1;
}

enum ScoreChangeReason
{
  REASON_UNKNOWN = 0;
  REASON_RECEIVE_PRESENT = 1;		// 收到礼物获得积分
  REASON_EXCHANGE_RED_DIAMOND = 2;// 积分兑换红钻（不存在这种情况）
  REASON_SETTLEMENT = 3; 			// 积分提现
  REASON_EXCHANGE_TBEANS = 4;		// 积分兑换T豆
  REASON_SETTLEMENT_REVERT = 5;	// 提现余额返积分,2017-09一次性操作
  REASON_TRIVIAGAME_REWARD = 6;	// 答题活动奖励积分
  REASON_WITHDRAW_FAILED_ROLL= 7;	        // 提现打款失败，回滚积分
  REASON_EXCHANGE_TCOIN_FAILED_ROLL= 8;	// 兑换T豆失败，回滚积分
  REASON_OFFICIAL_RECYCLE= 9;	            // 官方运营回收积分
  REASON_OFFICIAL_REWARD= 10;	            // 官方运营发放积分
  REASON_GUILD_CHANGE_PRIVATE = 11;
  REASON_GUILD_QUIT = 12;
  REASON_GUILD_OFFICIAL_RECYCLE = 13;
  REASON_GUILD_EXCHANGE = 14;
  REASON_WERWOLF_BUY_IDENTITY = 15;
  REASON_WERWOLF_BUY_TIME = 16;
}

// 增加用户积分
message AddUserScoreReq
{
  uint32 uid = 1;
  int32 add_score = 2;
  string order_id = 3;
  uint32 op_uid = 4;
  uint32 change_reason = 5;	// ScoreChangeReason
  string order_desc = 6;
  bytes extend = 7;
  uint32 time_value = 8;	// 时间值会记录在日志表中，一般情况下改字段不允许填值，默认0的时候，svr处理时会取当前时间
  string deal_token = 9; //校验链
  uint32 score_type = 10;
}

message AddUserScoreResp
{
  uint32 final_score = 1;
}

message StUserScoreDetail
{
  uint32 uid = 1;
  string order_id = 2;
  int32 change_score = 3;
  uint32 change_reason = 4;
  uint32 op_uid = 5;
  uint32 create_time = 6;
  uint32 score_type = 7;
}

// 获取用户的积分明细
message GetUserScoreDetailListReq
{
  uint32 uid = 1;
  uint32 offset = 2;
  uint32 limit = 3;
}

message GetUserScoreDetailListResp
{
  repeated StUserScoreDetail detail_list = 1;
}

// 根据reason获取用户的积分明细
message GetUserScoreDetailListByReasonReq
{
  uint32 uid = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  repeated uint32 change_reason_list = 4;
}

message GetUserScoreDetailListByReasonResp
{
  repeated StUserScoreDetail detail_list = 1;
}

message GetUserScoreByTableIndexReq{
  uint32 index = 1;
  string current_month =  2; //"%Y-%02m", ex:2017-07
}

message UserScoreEntry{
  uint32 uid = 1;
  uint32 score = 2;
}

message GetUserScoreByTableIndexResp{
  repeated UserScoreEntry score_list = 1;
}

message SetUserExchangeTypeReq{
  uint32 uid = 1;
  uint32 exchange_type = 2;
}

message GetUserExchangeTypeReq{
  uint32 uid = 1;
}

message GetUserExchangeTypeResp{
  uint32 uid = 1;
  uint32 exchange_type = 2;
}

// (对账用)根据指定时间段及reason获取订单数
message GetAllUserScoreOrderCountReq{
  uint32 change_reason = 1;	//来源 see ScoreChangeReason
  uint32 begin_time = 2;  	//开始时间
  uint32 end_time = 3;    	//结束时间
}

message GetAllUserScoreOrderCountResp{
  uint32 total_count = 1;
}

//(对账用)指定时间段及reason获取订单id列表
message GetAllUserScoreOrderListReq{
  uint32 change_reason = 1;	//来源 see ScoreChangeReason
  uint32 begin_time = 2;  	//开始时间
  uint32 end_time = 3;    	//结束时间
}

message GetAllUserScoreOrderListResp{
  repeated string orders = 1;
}

message StUserScoreOrderLog
{
  uint32 uid = 1;
  string order_id = 2;
  int32 change_score = 3;
  uint32 change_reason = 4;
  uint32 op_uid = 5;
  uint32 create_time = 6;
  bytes extend = 7;
  uint32 time_value = 8; // 外部系统时间
}

message GetOrderLogByOrderIdsReq {
  repeated string order_id_list = 1;
}

//(对账用) 根据orderId获取订单
message GetOrderLogByOrderIdsResp {
  repeated StUserScoreOrderLog order_log_list = 1;
}

service UserScore {
  option( tlvpickle.Magic ) = 15585;		// 服务监听端口号
//  option( tlvpickle.ServerName ) = "userscore";

  rpc GetScoreConfig( GetScoreConfigReq ) returns( GetScoreConfigResp ){
    option( tlvpickle.CmdID ) = 1;
    option( tlvpickle.OptString ) = "";
    option( tlvpickle.Usage ) = "";
  }

  rpc GetScoreConfigUpdateTime( GetScoreConfigUpdateTimeReq ) returns( GetScoreConfigUpdateTimeResp ){
    option( tlvpickle.CmdID ) = 2;
    option( tlvpickle.OptString ) = "";
    option( tlvpickle.Usage ) = "";
  }

  rpc SetScoreConfig( SetScoreConfigReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
    option( tlvpickle.CmdID ) = 3;
    option( tlvpickle.OptString ) = "t:";
    option( tlvpickle.Usage ) = "-t <title>";
  }

  rpc GetUserScore( GetUserScoreReq ) returns( GetUserScoreResp ){
    option( tlvpickle.CmdID ) = 4;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc GetUserScoreDetailList( GetUserScoreDetailListReq ) returns( GetUserScoreDetailListResp ){
    option( tlvpickle.CmdID ) = 5;
    option( tlvpickle.OptString ) = "u:o:n:";
    option( tlvpickle.Usage ) = "-u <uid> [-o <offset> -n <limit>]";
  }

  rpc AddUserScore( AddUserScoreReq ) returns( AddUserScoreResp ){
    option( tlvpickle.CmdID ) = 6;
    option( tlvpickle.OptString ) = "u:n:t:p:o:s:e:";
    option( tlvpickle.Usage ) = "-u <uid> -n <change_score> -t <change_type> -p <op_uid> -o <order_id> [-s <order_desc> -e <order_extend>]";
  }

  rpc GetUserScoreByTableIndex( GetUserScoreByTableIndexReq ) returns( GetUserScoreByTableIndexResp ){
    option( tlvpickle.CmdID ) = 7;
    option( tlvpickle.OptString ) = "i:m:";
    option( tlvpickle.Usage ) = "-i <table_index> -m <month>";
  }

  rpc SetUserExchangeType(SetUserExchangeTypeReq) returns(tlvpickle.SKBuiltinEmpty_PB){
    option( tlvpickle.CmdID ) = 8;
    option( tlvpickle.OptString ) = "u:t:";
    option( tlvpickle.Usage ) = "-u <uid> -t <type>";
  }

  rpc GetUserExchangeType(GetUserExchangeTypeReq) returns(GetUserExchangeTypeResp){
    option( tlvpickle.CmdID ) = 9;
    option( tlvpickle.OptString ) = "u:";
    option( tlvpickle.Usage ) = "-u <uid>";
  }

  rpc GetUserScoreDetailListByReason( GetUserScoreDetailListByReasonReq ) returns( GetUserScoreDetailListByReasonResp ){
    option( tlvpickle.CmdID ) = 10;
    option( tlvpickle.OptString ) = "u:o:n:r:";
    option( tlvpickle.Usage ) = "-u <uid> -r <change_reason> [-o <offset> -n <limit>]";
  }

  rpc GetAllUserScoreOrderCount( GetAllUserScoreOrderCountReq ) returns( GetAllUserScoreOrderCountResp ){
    option( tlvpickle.CmdID ) = 11;
    option( tlvpickle.OptString ) = "b:e:r:";
    option( tlvpickle.Usage ) = "-r <change_reason> -b <begin_time> -e <end_time>";
  }

  rpc GetAllUserScoreOrderList( GetAllUserScoreOrderListReq ) returns( GetAllUserScoreOrderListResp ){
    option( tlvpickle.CmdID ) = 12;
    option( tlvpickle.OptString ) = "b:e:r:";
    option( tlvpickle.Usage ) = " -r <change_reason> -b <begin_time> -e <end_time>";
  }

  rpc GetOrderLogByOrderIds( GetOrderLogByOrderIdsReq ) returns( GetOrderLogByOrderIdsResp ){
    option( tlvpickle.CmdID ) = 13;
    option( tlvpickle.OptString ) = "o:";
    option( tlvpickle.Usage ) = " -o <order_id>";
  }

}

