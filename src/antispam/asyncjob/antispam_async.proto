syntax = "proto2";

package antispam.async;


enum TASK_TYPE {
    TEXT_CHECK_MESSAGE_PUSH_TASK = 1;
    SHUMEI_REGISTER_CHECK_TASK = 2;
    SHUMEI_AUTH_CHECK_TASK = 3;
    DATAVISOR_LOGIN_CHECK_TASK = 4;
    DATAVISOR_COMM_CHECK_TASK = 5;
}

message TextCheckMessagePushTask
{
	required string content = 1;
	required int32 result = 2;
	required string lable_info = 3;
	optional string account = 4;
}

message ShumeiRegisterCheckTask
{
	required bytes shumei_register_check_req = 1;
}

message ShumeiAuth<PERSON>heckTask
{
	required bytes shumei_auth_check_req = 1;
}

message DataVisorLoginCheckTask
{
	required bytes datavisor_login_check_req = 1;
}

message DataVisorCommCheckTask
{
	required bytes datavisor_comm_check_req = 1;
}

