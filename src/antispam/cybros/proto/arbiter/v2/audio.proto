syntax = "proto3";

package cybros.arbiter.v2;

import "v2/global.proto";
import "v2/callback.proto";

service Audio {
  rpc AsyncScanAudio(ScanAudioReq) returns (ScanAudioResp);
  rpc QueryAudioTaskResult(QueryAudioTaskResultReq)
      returns (QueryAudioTaskResultResp);
}

message AudioBinaryData {
  message AudioFormatInfo {
    string format = 1;  // pcm, wav
    uint32 sample_rate = 2;
    uint32 track = 3;
  }

  // optional format info for PCM format audio data
  AudioFormatInfo format_info = 1;
  bytes content = 2;
}

message AudioData {
  Metadata metadata = 1;

  oneof audio_data {
    string url = 2;
    AudioBinaryData audio_bin = 3;
  }
}

message ScanAudioReq {
  TaskContext context = 1;
  AudioData audio_data = 2;
  Callback callback = 3;
}

message ScanAudioResp {
  string task_id = 1;
}

message QueryAudioTaskResultReq {
  string task_id = 1;
}

message QueryAudioTaskResultResp {
  TaskStatus task_status = 1;
}
