syntax = "proto3";

package cybros.arbiter.v2;

import "v2/global.proto";
import "v2/callback.proto";

service Image {
  rpc ScanImage(ScanImageReq) returns (ScanImageResp);
  rpc AsyncScanImage(ScanImageReq) returns (ScanImageResp);
}

message ImageData {
  Metadata metadata = 1;

  oneof image_data {
    string url = 3;
    bytes content = 4;
  }
}

message ScanImageReq {
  TaskContext context = 1;
  // all image data MUST have a unique data id
  repeated ImageData image_datas = 2;
  Callback callback = 3;
}

message ScanImageResp {
  string task_id = 1;
}

message ImageResultDetail {
}
