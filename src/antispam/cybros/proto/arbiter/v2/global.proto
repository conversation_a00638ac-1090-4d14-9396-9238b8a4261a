syntax = "proto3";

package cybros.arbiter.v2;

import "google/protobuf/any.proto";

message User {
    uint32 id = 1;
    string alias = 2;
    string phone = 3;
    string nickname = 4;
}

message Device {
    string id = 1;
    string ip = 2;
    string imei = 3;
    string mac = 4;
    string idfv = 5;
    string idfa = 6;
}

message SourceInfo {
    string id = 1;
    string name = 2;
}

message TaskContext {
    string app_id = 1;

    repeated Scene scenes = 2; // 识别场景

    User user_info = 3;     // 用户信息
    Device device_info = 4; // 设备信息

    string category = 5; // 类别
    string source_info =
        6; // 来源信息（如频道号、群号等）, deprecated, use source instead
    SourceInfo source = 7; // 来源信息
}

message Metadata {
    string data_id = 1; // 请求方需要保证data_id在单次提交中所有任务数据中唯一
    string name = 2;
    string mime_type = 3;
}

enum DataType {
    UNSPECIFIC = 0;
    IMAGE = 1;
    AUDIO = 2;
    VIDEO = 3;
}

enum Suggestion {
    PASS = 0;
    REJECT = 1;
    REVIEW = 2;
}
enum Scene {
    SCENE_DEFAULT = 0;
    SCENE_PORN = 1;
    SCENE_TERRORISM = 2;
    SCENE_AD = 3;
    SCENE_LOGO = 4;
    SCENE_LIVE = 5;
    SCENE_QRCODE = 6;
    SCENE_OTHER = 99;
}

enum Label {
    LABEL_NORMAL = 0;

    // Scene: Porn
    LABEL_SEXY = 1; // 色情
    LABEL_PORN = 2; // 性感

    // Scene: TERRORISM
    LABEL_BLOODY = 11;    // 血腥
    LABEL_EXPLOSION = 12; // 爆炸烟光
    LABEL_OUTFIT = 13;    // 特殊装束
    LABEL_LOGO = 14;      // 特殊标识
    LABEL_WEAPON = 15;    // 武器
    LABEL_POLITICS = 16;  // 涉政
    LABEL_VIOLENCE = 17;  // 打斗
    LABEL_CROWD = 18;     // 聚众
    LABEL_PARADE = 19;    // 游行
    LABEL_CARCRASH = 20;  // 车祸现场
    LABEL_OTHERS = 21;    // 其他

    // Scene: AD
    LABEL_AD = 30; // 含广告图片

    // Scene: Logo
    LABEL_TV = 40;        // 带有管控logo的图片
    LABEL_TRADEMARK = 41; // 商标

    // Scene: Live
    LABEL_MEANINGLESS = 50; // 无意义
    LABEL_PIP = 51;         // 画中画
    LABEL_SMOKING = 52;     // 吸烟
    LABEL_DRIVELIVE = 53;   // 车内直播

    // Scene: QRCode
    LABEL_QRCODE = 60;       // 二维码
    LABEL_PROGRAM_CODE = 61; // 小程序二维码

    LABEL_ILLEGAL = 70;
    LABEL_BLACKLIST = 71;
    LABEL_WHITELIST = 72;
    LABEL_HIGH_RISK_USER = 73;
    LABEL_USER_DEFINED = 74;
}

enum TaskStatus {
    SUCCESS = 0;
    PROCEEDING = 1;
}

message Result {
    Scene scene = 1;
    Suggestion suggestion = 2;
    Label label = 3;
    double rate = 4;
    string description = 5;

    google.protobuf.Any detail = 6;
}

message DataResult {
    string data_id = 1;
    repeated Result results = 2;

    Suggestion suggestion = 3;
    string description = 4;
}

message ScanResult {
    string task_id = 1;
    repeated DataResult data = 2;
    string provider = 3;
}

message CancelScanTaskReq { string task_id = 1; }

message CancelScanTaskResp {}
