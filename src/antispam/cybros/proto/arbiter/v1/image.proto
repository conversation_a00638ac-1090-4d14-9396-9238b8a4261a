syntax="proto3";

package cybros.arbiter.v1;

import "v1/global.proto";

service Image {
    rpc ScanImage( ScanImageReq ) returns( ScanImageResp );
    rpc AsyncScanImage( ScanImageReq ) returns( ScanImageResp );
}

message ImageData {
    oneof image_data {
        string url = 1;
        bytes content = 2;
    }
}

message ScanImageReq {
    Context context = 1;
    ImageData image_data = 2;
}

message ScanImageResp {
    string query_token = 1; // only available in AsyncScanImage
}
