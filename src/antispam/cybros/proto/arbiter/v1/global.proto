syntax="proto3";

package cybros.arbiter.v1;

enum Scene {
    DEFAULT = 0;
    PORN = 1;
    TERRORISM = 2;
    AD = 3;
    LOGO = 4;
    LIVE = 5; 
    QRCODE = 6;
};

enum TaskStatus {
    SUCCESS = 0;
    PROCEEDING = 1;
}

message User {
    uint32 id = 1;
    string alias = 2;
    string phone = 3;
    string nick_name = 4;
}

message Device {
    string id = 1;
    string ip = 2;
    string imei = 3;
    string mac = 4;
    string idfv = 5;
    string idfa = 6;
}

message Context {
    repeated Scene scenes = 1;

    User user_info = 2;
    Device device_info = 3;

    string source = 4;      // 来源（频道、群聊、单聊等）
    string source_info = 5; // 来源信息（如频道号、群号等）
}
