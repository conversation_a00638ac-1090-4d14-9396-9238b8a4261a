syntax="proto3";

package cybros.arbiter.v1;

import "v1/global.proto";


service Audio {
    rpc AsyncScanAudio (ScanAudioReq) returns (ScanAudioResp);
    rpc QueryAudioTaskResult (QueryAudioTaskResultReq) returns (QueryAudioTaskResultResp);
}

message AudioFormatInfo {
    string format = 1;      // pcm, wav
    uint32 sample_rate = 2;
    uint32 track = 3;
}

message ScanAudioReq {
    Context context = 1;

    string reserved = 2;
    string audio_name = 3;

    // optional format info for PCM format audio data
    AudioFormatInfo format_info = 4;

    oneof audio_data {
        string url = 5;
        bytes content = 6;
    }
}

message ScanAudioResp {
    string query_token = 1;
}

message QueryAudioTaskResultReq {
    string token = 1;
}

message QueryAudioTaskResultResp {
    TaskStatus task_status = 1;
}