syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Antispam;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

message TextCheckReq {
    enum DeviceType {
        none = 0;
        web = 1;
        wap = 2;
        android = 3;
        iphone = 4;
        ipad = 5;
        pc = 6;
        wp = 7;
    } 
    required string text = 1;           // 待检测的文本
    optional string ip = 2;             // 用户ip
    optional string account = 3;        // 用户帐号（用户唯一标识）
    optional DeviceType device_type = 4;// 用户设备类型  discard
    optional string device_id = 5;      // 用户设备ID
    optional uint32 data_type = 6;      // 数据类型区分（业务场景）
    optional string data_id = 7;        // 数据唯一标识
    optional string shumei_device_id = 8;
    optional string nickname = 9;
    optional string alias = 10;			// user alias(ttid)
    optional uint32 room_display_id = 11;
    optional string scene_type = 12;	// prioritize over than data_type
    optional string to_tokenid = 13;	// recv tokenid
}

enum RiskType {
	RESULT_DEFAULT = 0;
	RESULT_HIGH_RISK_ACCOUNT = 1;
}

message TextCheckResp {
    enum RESULT{
        PASS = 0;           // 0:通过
        SUSPICION = 1;      // 1:嫌疑
        NOT_PASS = 2;       // 2:不通过
    }
    required RESULT result = 1;     // 文本检测结果
    optional string label_info = 2;     // 判定结果分类信息
    optional string requestId = 3;
    optional uint32 risk_type = 4;			// 
}

enum UserBehavierCheckResult {
    RESULT_OK = 0;             // 0:正常
    RESULT_SUSPICION = 10;     // 10:嫌疑
    RESULT_FATAL = 20;         // 20:致命
}

enum UserBehavierCheckResultHitType {
    HIT_OK = 0;                 // 0:正常
    HIT_DATA_ABNORMAL = 1;      // 1:数据异常（数据完整性校验不通过或数据伪造等）
    HIT_BEHAVIOR_ABNORMAL = 2;  // 2:行为异常（用户的操作行为(鼠标点击/移动等)无法通过行为验证模型等）
    HIT_ENVIROMENT_ABNORMAL = 3;// 3:环境异常（设备指纹等信息无法通过设备验证模型等）
    HIT_BIZ_ABNORMAL = 4;       // 4:业务模型（撞库、批量操作、违反业务规则等）
}

// 注册时行为检测
message RegisterCheckReq {
    required string token = 1;          // 反作弊结果查询token，由业务前端页面提交给业务后端。
    optional string account = 2;        // 用户唯一标识。antispam会进行md5哈希后提交到易云
    optional string email = 3;          // 用户的邮箱。antispam会进行md5哈希后提交到易云
    optional string phone = 4;          // 用户的手机号。antispam会进行md5哈希后提交到易云
    optional string ip = 5;             // 用户注册时的ip
    optional string nickname = 6;       // 注册的昵称
}

message RegisterCheckResp {
    required UserBehavierCheckResult result = 1;            // 检测结果
    required UserBehavierCheckResultHitType hit_type = 2;   // 命中类型
    required string task_id = 3;                            // 任务ID，与检测请求一一对应
}

// 登录时行为检测
message AuthCheckReq {
    required string token = 1;          // 反作弊结果查询token，由业务前端页面提交给业务后端。
    optional string account = 2;        // 用户唯一标识。antispam会进行md5哈希后提交到易云
    optional string email = 3;          // 用户的邮箱。antispam会进行md5哈希后提交到易云
    optional string phone = 4;          // 用户的手机号。antispam会进行md5哈希后提交到易云
    optional string ip = 5;             // 用户登录时的ip
    optional uint32 register_time = 6;  // 用户的注册时间
    optional string register_ip = 7;    // 用户的注册IP
}

message AuthCheckResp {
    required UserBehavierCheckResult result = 1;            // 检测结果
    required UserBehavierCheckResultHitType hit_type = 2;   // 命中类型
    required string task_id = 3;                            // 任务ID，与检测请求一一对应
}

// 营销活动行为检测
message CommonActivityCheckReq {
    required string token = 1;          // 反作弊结果查询token，由业务前端页面提交给业务后端。
    optional string account = 2;        // 用户唯一标识。antispam会进行md5哈希后提交到易云
    optional string email = 3;          // 用户的邮箱。antispam会进行md5哈希后提交到易云
    optional string phone = 4;          // 用户的手机号。antispam会进行md5哈希后提交到易云
    optional string ip = 5;             // 用户登录时的ip
    optional uint32 register_time = 6;  // 用户的注册时间
    optional string register_ip = 7;    // 用户的注册IP
    optional string activity_id = 8;    // 活动的唯一标识
    optional string target = 9;         // 活动操作的目标，比如：A给B点赞，则target为B。可以是account、手机、email，antispam会做md5哈希后提交到易云
}

message CommonActivityCheckResp {
    required UserBehavierCheckResult result = 1;            // 检测结果
    required UserBehavierCheckResultHitType hit_type = 2;   // 命中类型
    required string task_id = 3;                            // 任务ID，与检测请求一一对应
}

// 验证码验证
message VerifyCodeCheckReq {
    required string captcha_id = 1;     // 验证码id
    required string validate = 2;       // 提交二次校验的验证数据
    optional string user = 3;           // 用户信息，值可为空
}

message VerifyCodeCheckResp {
    required bool is_pass = 1;          // 是否校验通过
}

message GpsLocation {
    required double gps_longitude = 1;  // 经度
    required double gps_latitude = 2;   // 纬度
}

enum ShumeiOSType {
    none = 0;
    android = 1;
    ios = 2;
    weapp = 3;
    web = 4;
}

// 数美注册检测
message ShumeiRegisterCheckReq {
    enum SignupPlatformType {
        tt = 0;
        qq = 1;
        weixin = 2;
    }
    optional string appId = 1;  // 应用ID，用于区分相同公司的不同应用
    required string account = 2;
    required string client_ip = 3;
    required int64 timestamp = 4;   // 事件发生时的时间戳，单位为毫秒（ms）
    optional string shumei_deviceid = 5;
    optional string phone = 6;
    optional ShumeiOSType os = 7;
    optional string app_version = 8;
    optional string nickname = 9;
    optional GpsLocation gps_location = 10;
    optional SignupPlatformType signup_platform = 11;
}

message ShumeiRegisterCheckResp {
    optional string request_id = 1;
    optional int32 code = 2;
    optional string message = 3;
    optional string risk_level = 4;
    optional int32 score = 5;
}

// 数美登录检测
message ShumeiAuthCheckReq {
    optional string appId = 1;  // 应用ID，用于区分相同公司的不同应用
    required string account = 2;
    required string client_ip = 3;
    required int64 timestamp = 4;   // 事件发生时的时间戳，单位为毫秒（ms）
    optional string shumei_deviceid = 5;
    optional string phone = 6;
    optional ShumeiOSType os = 7;
    optional string app_version = 8;
    optional int32 passwd_matched = 9;    // 用户名和密码验证结果 1:成功，0:失败
}

message ShumeiAuthCheckResp {
    optional string request_id = 1;
    optional int32 code = 2;
    optional string message = 3;
    optional string risk_level = 4;
    optional int32 score = 5;
}

// shumei text check
message ShumeiTextCheckReq {
	optional string shumei_device_id = 1;
	optional uint32 uid = 2;
	optional string username = 3;
	required string alias = 4;
	required string text = 5;
	optional string ip = 6;
	optional string nickname = 7;
	optional int32 data_type = 8;
	optional uint32 room_display_id = 9;
}

message ShumeiTextCheckResp {
	optional int32 score = 1;	// [0, 1000], low -> high risk
	optional string riskLevel = 2; // PASS, REVIEW, REJECT
	optional string requestId = 3;
}

message AntispamUserInfo {
	optional uint32 uid = 1;
	optional string tokenid = 2;
	optional string nickname = 3;
	optional uint32 gender = 4;
	optional string phone_number = 5;
}

message DVRegisterCheckReq {
    enum RegType {
        phone = 0;	// "phone"
        wx = 1;		// "wx"
        qq = 2;		// "qq"
    }
	optional AntispamUserInfo user_info = 1;
	optional string ip = 2;
	optional string device_info = 3;
	optional string os_ver = 4;
	optional string os_type = 5;
	optional uint32 client_version = 6;
	optional string device_id = 7;
	optional string origin_channel = 8;
	optional string current_channel = 9;
	optional string reg_type = 10;
	optional string imei = 11;
	optional string useraddtime = 12;
	optional string net = 13;
	optional string language = 14;
	optional string ssid = 15;
	optional string free_storage = 16;
	optional string resolution = 17;
	optional string system_volume = 18;
	optional string total_storage = 19;
	optional string mac = 20;
	optional string battery_state = 21;
	optional string memory = 22;
	optional string device_carrier = 23;
	optional string battery_level = 24;
	optional string device_name = 25;
}

message DVLoginCheckReq {
	optional AntispamUserInfo user_info = 1;
	optional string event_type = 2;
	optional string ip = 3;
	optional uint32 clientVersion = 4;
	optional string event_time = 5;
	optional string device_id = 6;
	optional string os_ver = 7;
}

message DVCommCheckReq {
	optional AntispamUserInfo user_info = 1;
	optional AntispamUserInfo to_user_info = 2;
	optional string event_type = 3;
	optional string event_time = 4;
	optional string ext_info = 5;
}

message DVCheckResp {
	optional string responseType = 1;
	optional string cust_no = 2;
	optional string apply_no = 3;
	optional double score = 4;
	optional string errorType = 5;
	optional string errorDetail = 6;
}

// Antispam服务
service Antispam {
	option( tlvpickle.Magic ) = 15075;		// 服务监听端口号

    rpc TextCheck( TextCheckReq ) returns( TextCheckResp ) {
        option( tlvpickle.CmdID ) = 1;                  // 命令号
		option( tlvpickle.OptString ) = "t:";   	    // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-t <text>";    	// 测试工具的命令号帮助
    }

    rpc RegisterCheck( RegisterCheckReq ) returns ( RegisterCheckResp ) {
        option( tlvpickle.CmdID ) = 2;                  // 命令号
		option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
    }

    rpc AuthCheck( AuthCheckReq ) returns ( AuthCheckResp ) {
        option( tlvpickle.CmdID ) = 3;                  // 命令号
		option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
    }

    rpc CommonActivityCheck( CommonActivityCheckReq ) returns ( CommonActivityCheckResp ) {
        option( tlvpickle.CmdID ) = 4;                  // 命令号
		option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
    }

    rpc VerifyCodeCheck( VerifyCodeCheckReq ) returns ( VerifyCodeCheckResp ) {
        option( tlvpickle.CmdID ) = 5;                  // 命令号
		option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
    }

    rpc ShumeiRegisterCheck( ShumeiRegisterCheckReq ) returns ( ShumeiRegisterCheckResp ) {
        option( tlvpickle.CmdID ) = 6;                  // 命令号
		option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
    }

    rpc ShumeiAuthCheck( ShumeiAuthCheckReq ) returns ( ShumeiAuthCheckResp ) {
        option( tlvpickle.CmdID ) = 7;                  // 命令号
		option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
    }

    rpc ShumeiTextCheck( ShumeiTextCheckReq ) returns ( ShumeiTextCheckResp ) {
        option( tlvpickle.CmdID ) = 8;                  // 命令号
		option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
    }
    
    rpc DVRegisterCheck( DVRegisterCheckReq ) returns ( DVCheckResp ) {
        option( tlvpickle.CmdID ) = 9;                  // 命令号
		option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
    }

    rpc DVLoginCheck( DVLoginCheckReq ) returns ( DVCheckResp ) {
        option( tlvpickle.CmdID ) = 10;                  // 命令号
		option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
    }
    rpc DVCommCheck( DVCommCheckReq ) returns ( DVCheckResp ) {
        option( tlvpickle.CmdID ) = 11;                  // 命令号
		option( tlvpickle.OptString ) = "";   	    // 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "";    	// 测试工具的命令号帮助
    }
}

