syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package GeneralTimeline;

//------------------------------------------
// timeline的通用存储结构，所有消息都是包含在这个结构体里面。
//------------------------------------------
message GeneralTimelineMsg {
	enum TYPE {
		NEW_GAME_Circle_MSG = 1;			// 任务完成消息
		CIRCLE_UPDATE_MSG = 2;				// 圈子更新消息
		MALL_ACT_MSG = 3;					// 红钻商城活动消息
		APP_FILE_UPDATE = 4;				// 客户端文件更新
		GAME_TAB_MSG = 5;					// 游戏标签更新
		RED_PACKET_MSG = 6;					// 红包活动
	}
	required uint32 type = 1;
	required uint32 seqid = 2;
	required bytes msg_bin = 3;
}

//////////////////////
// type: NEW_GAME_Circle_MSG

message NewGameCircleMsg {
    required uint32 circle_id = 1;
    required uint32 new_begin = 2;      //  提示有新游戏的开始时间
    required uint32 new_end   = 3;      //  提示有新游戏的结束时间
}

//////////////////////
// type: CIRCLE_UPDATE_MSG

message CircleUpdateMsg {
	required uint32 circle_id = 1;    // 圈子ID
	required bool   is_delete = 2;
}

//////////////////////
// type: MALL_ACT_MSG

message MallActivity {
    required uint32 act_id		= 1;    // 活动ID
    required string url         = 2;    // 活动入口跳转链接
    required string title       = 3;    // 活动标题
    optional string sub_title   = 4;    // 活动副标题, reserved
    required uint64 begin_time  = 5;    // 活动开始时间(UNIX TIMESTAMP)
}

message MallActivityMsg {
	optional MallActivity current_act	= 1;
    optional uint32 app_id				= 2;
}

//////////////////////
// type: APP_FILE_UPDATE

message AppFileUpdateMsg {
	required uint32 app_id			= 1;	// app_id
	required string file_name		= 2;	// 文件名
	required string download_url	= 3;	// 下载url
	required string md5_sum			= 4;	// md5校验和
}

/////////////////////////
// type:: GAME_TAB_MSG

message GameTabUpdateMsg {
	enum TYPE {
		GAMETAB_TYPE_WELFARE = 1;			// 福利活动
		GAMETAB_TYPE_BEST_GAME = 2;			// 精品专区
		GAMETAB_TYPE_NEW_GAME = 3;			// 新游榜
		GAMETAB_TYPE_HOT_GAME = 4;			// 热游榜
	}
	required uint32 msg_type 			= 1;
}

///////////////////////////
// type:: RED_PACKET_MSG
/**
 * 红包活动定义
 */
message RedPacketActivityInfo
{
    required uint32 id = 1;             // 红包活动事件ID 为0表示需要创建一个红包活动事件
    required string name = 2;           // 红包活动名称

    required uint64 begin_time = 3;     // 开始时间, 精确至`秒`
    required uint64 end_time = 4;       // 结束时间, 精确至`秒`

    optional uint32 combo_timeout = 5;  // 主阶段内, Combo中断的超时时间, 精确至`毫秒`
}

/**
 * 红包阶段定义
 */
message RedPacketStageInfo
{
    enum StageType {
        LOTTERY = 1;          // 执行抽奖阶段, 主阶段, 以此来决定开始前倒计时显示
        MASS_GUILDBUFF = 2;   // 攒公会BUFF阶段
        PREPARE = 3;          // 准备阶段
        DEFAULT = 4;          // 默认阶段 即什么也不会干的阶段 或者其他不连续的空余时间阶段
    }

    required uint32 stage_id = 1;      // 阶段ID 为0表示需要创建一个阶段信息
    required uint32 activity_id = 2;   // 该阶段对应的红包活动事件ID
    required uint32 type  = 3;         // 阶段类型 see StageType

    optional string name = 4;          // 阶段名称
    optional uint64 begin_time = 5;    // 阶段开始时间, 精确至`秒`
    optional uint64 end_time = 6;      // 阶段结束时间, 精确至`秒`
    repeated string ad_text_list = 7;  // 阶段内的提示广告语

    // 阶段内发送抢红包请求的最小间隔, 精确至`毫秒`
    // 客户端必须严格按照此间隔控制请求频率
    required uint32 min_req_interval = 8;
}

message RedPacketUpdateMsg {
    optional RedPacketActivityInfo act = 1;
    repeated RedPacketStageInfo stage_list = 2;         //阶段为增量返回
}

//------------------------------------------
// 读写协议
//------------------------------------------

// 使用setx/set存储
message WriteTimelineMsgReq {
	required GeneralTimelineMsg msg = 1;
	optional uint32 ttl	= 2; 				// time to live
}

message WriteTimelineMsgResp {
}

// 使用hset存储
message WriteUpdateMsgReq {
	required string bucket = 1;			        // hset name
	required string key = 2;					// hset key
	required GeneralTimelineMsg msg = 3;		// hset value
}

message WriteUpdateMsgResp {
}

//------------------------------------------
// 拉取timeline
//------------------------------------------
message PullTimelineMsgReq {
    required uint32 start_seqid = 1;
    required uint32 limit = 2;
    repeated string bucket_list = 3;	// 需要拉取差集的bucket列表
	optional bool include_timeline = 4;	// 是否包含timeline消息
}

message PullTimelineMsgResp {
    repeated GeneralTimelineMsg msg_list = 1;
}

//------------------------------------------
// 用户标记已读
//------------------------------------------
message MarkReadedReq {
    required uint32 uid = 1;
    required uint32 seqid = 3;
}

message MarkReadedResp {
}


//----------------------------------------------------
// 获取用户标记为已读的seq id
//----------------------------------------------------

message GetReadedReq {
    required uint32 uid = 1;
}

message GetReadedResp {
    required uint32 seqid = 1;
}

//-----------------------------------------------------
// 最新的seq id
//-----------------------------------------------------
message GetLastSeqidReq{

}

message GetLastSeqidResp{
    required uint32 seqid = 1;
}




service GeneralTimeline {
	option( tlvpickle.Magic ) = 15100;		// 服务监听端口号

    rpc WriteTimelineMsg(WriteTimelineMsgReq) returns (WriteTimelineMsgResp) {
        option( tlvpickle.CmdID ) = 1;																		// 命令号
        option( tlvpickle.OptString ) = "i:s:";														// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <id> -s <suffix>";			// 测试工具的命令号帮助
    }

    rpc GetLastSeqid( GetLastSeqidReq ) returns ( GetLastSeqidResp ){
        option( tlvpickle.CmdID ) = 2;                                                                      // 命令号
        option( tlvpickle.OptString ) = "";                                                     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
    }

    rpc PullTimelineMsg(PullTimelineMsgReq) returns (PullTimelineMsgResp) {
        option( tlvpickle.CmdID ) = 3;                                                                      // 命令号
        option( tlvpickle.OptString ) = "b:l:";                                                     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = " -b <begin_seq> -l <limit>";            // 测试工具的命令号帮助
    }

    rpc MarkReaded(MarkReadedReq) returns (MarkReadedResp) {
        option( tlvpickle.CmdID ) = 4;                                          // 命令号
        option( tlvpickle.OptString ) = "i:s:d:";                               // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-i <id> -s <suffix> -d <seqid>";           // 测试工具的命令号帮助
    }

    rpc GetReaded ( GetReadedReq ) returns ( GetReadedResp ){
        option( tlvpickle.CmdID ) = 5;                                                                      // 命令号
        option( tlvpickle.OptString ) = "u:";                                                     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc WriteUpdateMsg ( WriteUpdateMsgReq ) returns ( WriteUpdateMsgResp ) {
        option( tlvpickle.CmdID ) = 6;                  // 命令号
        option( tlvpickle.OptString ) = "";             // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
    }



}
