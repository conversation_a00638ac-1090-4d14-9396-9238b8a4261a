syntax="proto2";

package esgw;

// 必须import
import "common/tlvpickle/skbuiltintype.proto";


message SearchReq{
    optional string index = 1;
    optional string doc_type = 2;
    optional string body = 3;
    optional int64 offset = 4;
    optional int64 limit = 5;
}

message SearchResp{
    optional bytes data = 1; //tODO
}

message CreateReq{
    required string index = 1;
    required string doc_type= 2;
    required string doc_id = 3;
    required string body = 4;
}

message CreateResp{
    /*nothing*/
}

message DeleteReq {
    required string index = 1;
    required string doc_type= 2;
    required string doc_id = 3;
}
message DeleteResp{
    //nothing
}

message IndexReq {
    required string index = 1;
    required string doc_type= 2;
    optional string doc_id = 3;
    required string body = 4;
}
message IndexResp{
    //nothing
}

message CountReq{
    optional string index = 1;
    optional string doc_type = 2;
    optional string body = 3;
}

message CountResp{
    optional bytes data = 1;  
}

message GetByIdReq{
    required string index = 1;
    optional string doc_type= 2;
    required string doc_id = 3;
}
message GetByIdResp {
    optional bytes data = 1;  
}

message BulkReq {
    optional string index = 1;
    optional string doc_type= 2;
    required bytes body = 3;
}

message BulkResp {
    //nothing
}

message DeleteByQueryReq {
    required string index = 1;
    optional string doc_type = 2;
    optional string body = 3;
}
message DeleteByQueryResp {
}
service ESGw {
	option(tlvpickle.Magic) = 14955;		// 服务监听端口号

    rpc Search (SearchReq) returns (SearchResp){
        option (tlvpickle.CmdID)=1;
        option (tlvpickle.OptString)="u:";
        option (tlvpickle.Usage)= "-u <uid> ";
    }
    rpc Create (CreateReq) returns (CreateResp) {
       option (tlvpickle.CmdID) = 2;
       option (tlvpickle.OptString) = "u:";
       option (tlvpickle.Usage) = "-u <uid> ";
    }
    rpc Delete(DeleteReq) returns (DeleteResp) {
       option (tlvpickle.CmdID) = 3;
       option (tlvpickle.OptString) = "u:";
       option (tlvpickle.Usage) = "-u <uid> ";
    }
    rpc Index(IndexReq) returns (IndexResp) {
       option (tlvpickle.CmdID) = 4;
       option (tlvpickle.OptString) = "u:";
       option (tlvpickle.Usage) = "-u <uid> ";
    };
    rpc Count(CountReq) returns (CountResp) {
       option (tlvpickle.CmdID) = 5;
       option (tlvpickle.OptString) = "u:";
       option (tlvpickle.Usage) = "-u <uid> ";
    }
    rpc GetById(GetByIdReq) returns (GetByIdResp){
       option (tlvpickle.CmdID) = 6;
       option (tlvpickle.OptString) = "u:";
       option (tlvpickle.Usage) = "-u <uid> ";
    }
    rpc Bulk (BulkReq)  returns (BulkResp) {
       option (tlvpickle.CmdID) = 7;
       option (tlvpickle.OptString) = "u:";
       option (tlvpickle.Usage) = "-u <uid> ";
    }
    rpc DeleteByQuery(DeleteByQueryReq) returns (DeleteByQueryResp) {
       option (tlvpickle.CmdID) = 8;
       option (tlvpickle.OptString) = "u:";
       option (tlvpickle.Usage) = "-u <uid> ";
    }



}

