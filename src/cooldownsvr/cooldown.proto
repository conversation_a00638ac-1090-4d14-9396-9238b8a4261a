syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package cooldown;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list


message FetchVisitReq{
    required string key = 1;
    required uint32 type = 2;
    required int64 val = 3;
    optional uint32 ttl = 4;
    optional uint32 update_ttl = 5;   //是否每次都更新ttl
}

message FetchVisitRsp{
    required int64 old_val = 1;
    optional string key = 2;
    optional uint32 type = 3;

}

message FetchAndAddGeneralVisitReq{
    required FetchVisitReq visit_req = 1;
}

message FetchAndAddGeneralVisitResp{
    optional FetchVisitRsp visit_resp = 1;
}

message FetchAndAddPeriodVisitReq{
    required FetchVisitReq visit_req = 1;
}

message FetchAndAddPeriodVisitResp{
    optional FetchVisitRsp visit_resp = 1;
}

message FetchAndSubGeneralVisitReq{
    required FetchVisitReq visit_req = 1;
}

message FetchAndSubGeneralVisitResp{
    optional FetchVisitRsp visit_resp = 1;
}

message FetchAndSubPeriodVisitReq{
    required FetchVisitReq visit_req = 1;
}

message FetchAndSubPeriodVisitResp{
    optional FetchVisitRsp visit_resp = 1;
}


message FetchGeneralVisitReq
{
    required FetchVisitReq visit_req = 1;
    optional bool ret_ttl = 2;
}

message FetchGeneralVisitResp
{
    optional FetchVisitRsp visit_resp = 1;
    optional uint32 ttl = 2;
}

message ClearGeneralVisitReq
{
    required string key = 1;
    required uint32 type = 2;
}

message ClearGeneralVisitResp
{
}

// 复杂结构(HMAP)
message FetchComplexVisit
{
    required string sub_type = 1;
    required int64 old_val = 2;
}

message FetchAndIncrGeneralComplexVisitReq
{
    required string key = 1;
    optional string sub_type = 2;
    optional int64 val = 3;
    optional int32 ttl = 4; // if ttl < 0 表示 删除该key
}

message FetchAndIncrGeneralComplexVisitResp
{
    optional FetchComplexVisit complex_visit = 1;
}

message FetchGeneralComplexVisitReq
{
    required string key = 1;
}

message FetchGeneralComplexVisitResp
{
    repeated FetchComplexVisit complex_visit_list = 1;
}

message CheckNxReq
{
    required string key = 1;
    required int32 ttl  = 2; // if ttl < 0 表示 删除该key
    required uint32 uid = 3;
	optional string val = 4;
}

message CheckNxResp
{
    required bool is_exist_before = 1;     // key存在时 is_exist_before = true
	optional string before_exist_val = 2;  // 仅is_exist_before = true 填写
}

//清除登录密码错误限制
message DelTryPwdDevLimitReq
{
    required uint32 uid = 1;
}
message DelTryPwdDevLimitResp
{
}

service Cooldown {
    option( tlvpickle.Magic ) = 15063;		// 服务监听端口号

    //通用的
    rpc FetchAndAddGeneralVisit( FetchAndAddGeneralVisitReq ) returns ( FetchAndAddGeneralVisitResp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "k:t:a:l:";  
        option( tlvpickle.Usage ) = "-k <key> -t <type> -a <value> [-l <ttl>]";
    }
    rpc FetchAndSubGeneralVisit( FetchAndSubGeneralVisitReq ) returns ( FetchAndSubGeneralVisitResp ) {
        option( tlvpickle.CmdID ) = 2;       
        option( tlvpickle.OptString ) = "k:t:a:";  
        option( tlvpickle.Usage ) = "-k <key> -t <type> -a <value>";      
    }

    //周期 : 每日/每小时/每分钟等
    rpc FetchAndAddPeriodVisit( FetchAndAddPeriodVisitReq ) returns ( FetchAndAddPeriodVisitResp ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "k:t:a:p:";  
        option( tlvpickle.Usage ) = "-k <key> -t <type> -a <value> -p <1=day/2=hour>";
    }
    rpc FetchAndSubPeriodVisit( FetchAndSubPeriodVisitReq ) returns ( FetchAndSubPeriodVisitResp ) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "k:t:a:p:";  
        option( tlvpickle.Usage ) = "-k <key> -t <type> -a <value> -p <1=day/2=hour>";      
    }

	// 通用的获取
    rpc FetchGeneralVisit( FetchGeneralVisitReq ) returns ( FetchGeneralVisitResp ) {
        option( tlvpickle.CmdID ) = 5;       
        option( tlvpickle.OptString ) = "k:t:";  
        option( tlvpickle.Usage ) = "-k <key> -t <type> ";      
    }

    // 复杂通用结构的设置/获取
    rpc FetchAndIncrGeneralComplexVisit( FetchAndIncrGeneralComplexVisitReq ) returns ( FetchAndIncrGeneralComplexVisitResp ) {
        option( tlvpickle.CmdID ) = 6;       
        option( tlvpickle.OptString ) = "k:t:a:e:";  
        option( tlvpickle.Usage ) = "-k <key> -t <sub_type> -a <incr value> -e <ttl>";      
    }
    
	rpc FetchGeneralComplexVisit( FetchGeneralComplexVisitReq ) returns ( FetchGeneralComplexVisitResp ) {
        option( tlvpickle.CmdID ) = 7;       
        option( tlvpickle.OptString ) = "k:";  
        option( tlvpickle.Usage ) = "-k <key> ";      
    }

    // 
	rpc CheckNx( CheckNxReq ) returns ( CheckNxResp ) {
        option( tlvpickle.CmdID ) = 8;       
        option( tlvpickle.OptString ) = "k:e:";  
        option( tlvpickle.Usage ) = "-k <key> -e <ttl>";      
    }
    
    rpc ClearGeneralVisit( ClearGeneralVisitReq ) returns ( ClearGeneralVisitResp ) {
        option( tlvpickle.CmdID ) = 9;       
        option( tlvpickle.OptString ) = "";  
        option( tlvpickle.Usage ) = "";      
    }

    rpc DelTryPwdDevLimit( DelTryPwdDevLimitReq ) returns ( DelTryPwdDevLimitResp ) {
        option( tlvpickle.CmdID ) = 10;       
        option( tlvpickle.OptString ) = "u:";  
        option( tlvpickle.Usage ) = "-u <uid>";      
    }
    
}
