syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package guildrecommend;

message GuildHotGameCfg
{
	uint32 game_id = 1;
	uint32 sort_weight = 2;
	uint32 guild_count = 3;
}

// 获取公会热门游戏
message GetGuildHotGameCfgListReq
{
}

message GetGuildHotGameCfgListResp
{
	repeated GuildHotGameCfg game_list = 1;
}

// 添加公会热门游戏
message AddGuildHotGameCfgReq
{
	GuildHotGameCfg game_cfg = 1;
}

// 删除公会热门游戏
message DelGuildHotGameCfgReq
{
	uint32 game_id = 1;
}

// 更新公会热门游戏
message UpdateGuildHotGameCfgReq
{
	GuildHotGameCfg game_cfg = 1;
}

// 公会游戏信息
message StGuildGameInfo
{
	uint32 guild_id = 1;
	uint32 game_id = 2; // 游戏id
	uint32 sort_weight = 3;
}

// 获取公会游戏
message GetGuildGameListReq
{
	uint32 guild_id = 1;
}

message GetGuildGameListResp
{
	repeated StGuildGameInfo game_list = 1;
}

// 添加公会游戏
message AddGuildGameReq
{
	uint32 guild_id = 1;
	repeated uint32 game_id_list  = 2;
}

// 删除公会游戏
message DelGuildGameReq {
	uint32 guild_id = 1;
	repeated uint32 game_id_list = 2;
}

// 更换公会游戏
message ChangeGuildGameReq {
	uint32 guild_id = 1;
	uint32 old_game_id = 2;
	uint32 new_game_id = 3;
}

// 根据游戏获取入驻的公会列表
message GetGuildListByGameReq
{
	uint32 game_id = 1;
	uint32 start = 2;
	uint32 count = 3;
}

message GetGuildListByGameResp
{
	repeated uint32 guild_id_list = 1;
	bool reach_end = 2;
}

// 根据游戏获取推荐的公会列表
message GetGuildRecommendListByGamesReq
{
	repeated uint32 game_id_list = 1;
	uint32 start = 2;
	uint32 count = 3;
	bool is_game_guild_page = 4;
}

message GetGuildRecommendListByGamesResp
{
	repeated uint32 guild_id_list = 1;
	bool reach_end = 2;
	uint32 last_query = 3;
}

// 公会主房间中触发的条件类型
enum ActivateGuildType
{
	ACTIVATE_NONE = 0;
	ACTIVATE_SEND_MSG = 1;			// 发消息
	ACTIVATE_HOLD_MIC = 2;			// 上麦
	ACTIVATE_OFF_MIC = 3;			// 下麦
}

// 通知公会达到某项推荐条件
message NotifyActivateGuildReq
{
	uint32 uid = 1;
	uint32 guild_id = 2;
	uint32 activate_type = 3;	// ActivateGuildType
	uint32 channel_id = 4;
}

enum RecommendGuildType
{
	ACT_NONE = 0;
	ACT_RECOMMEND_LIBRARY = 1;		// 推荐库
	ACT_BLACK_LIST = 2;				// 黑名单
}

message GuildTypeInfo
{
	uint32 guild_id = 1;
	uint32 recommend_type = 2;	// RecommendGuildType
}

// 配置的推荐公会名单
message AddGuildTypeInfoReq
{
	uint32 guild_id = 1;
	uint32 recommend_type = 2;	// RecommendGuildType
}

message DelGuildTypeInfoReq
{
	uint32 guild_id = 1;
	uint32 recommend_type = 2;	// RecommendGuildType
}

message GetGuildTypeInfoListReq
{
	uint32 start = 1;
	uint32 count = 2;
	uint32 recommend_type = 3;	// RecommendGuildType
}

message GetGuildTypeInfoListResp
{
	repeated GuildTypeInfo guild_list = 1;
}

message GuildChannelMicInfo
{
	uint32 guild_id = 1;
	uint32 channel_id = 2;
	uint32 mic_uid = 3;
	uint32 ts = 4;
}

// 去掉公会的所有推荐信息
message RemoveGuildInfoByGuildIdReq
{
	uint32 guild_id = 1;
}

service GuildRecommend {
	option( tlvpickle.Magic ) = 15635;		// 服务监听端口号
		
	rpc GetGuildHotGameCfgList( GetGuildHotGameCfgListReq ) returns( GetGuildHotGameCfgListResp ){
		option( tlvpickle.CmdID ) = 1;										
        option( tlvpickle.OptString ) = "";							
        option( tlvpickle.Usage ) = "";
	}
	
	rpc AddGuildHotGameCfg( AddGuildHotGameCfgReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 2;										
        option( tlvpickle.OptString ) = "a:w:";							
        option( tlvpickle.Usage ) = "-a <game_id> -w <sort_weight>";
	}
	
	rpc DelGuildHotGameCfg( DelGuildHotGameCfgReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 3;										
        option( tlvpickle.OptString ) = "a:";							
        option( tlvpickle.Usage ) = "-a <game_id>";
	}
	
	rpc UpdateGuildHotGameCfg( UpdateGuildHotGameCfgReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 4;										
        option( tlvpickle.OptString ) = "a:w:";							
        option( tlvpickle.Usage ) = "-a <game_id> -w <sort_weight>";
	}
	
	rpc GetGuildGameList( GetGuildGameListReq ) returns( GetGuildGameListResp ){
		option( tlvpickle.CmdID ) = 5;										
        option( tlvpickle.OptString ) = "g:";							
        option( tlvpickle.Usage ) = "-g <guild_id>";
	}
	
	rpc AddGuildGame( AddGuildGameReq ) returns( GetGuildGameListResp ){
		option( tlvpickle.CmdID ) = 6;										
        option( tlvpickle.OptString ) = "g:a:";							
        option( tlvpickle.Usage ) = "-g <guild_id> -a <game_id>";
	}
	
	rpc DelGuildGame( DelGuildGameReq ) returns( GetGuildGameListResp ){
		option( tlvpickle.CmdID ) = 7;										
        option( tlvpickle.OptString ) = "g:a:";							
        option( tlvpickle.Usage ) = "-g <guild_id> -a <game_id>";
	}
	
	rpc ChangeGuildGame( ChangeGuildGameReq ) returns( GetGuildGameListResp ){
		option( tlvpickle.CmdID ) = 8;										
        option( tlvpickle.OptString ) = "g:o:n:";
		option( tlvpickle.Usage ) = "-g <guild id> -o <old_game id> -n <new_game_id>";
	}
	
	rpc GetGuildRecommendListByGames( GetGuildRecommendListByGamesReq ) returns( GetGuildRecommendListByGamesResp ){
		option( tlvpickle.CmdID ) = 9;										
        option( tlvpickle.OptString ) = "g:s:n:t:";							
        option( tlvpickle.Usage ) = "-g <game_id> -s <start> -n <count> -t <is_game_guild_page>";
	}
	
	rpc NotifyActivateGuild( NotifyActivateGuildReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 10;										
        option( tlvpickle.OptString ) = "u:g:t:x:";							
        option( tlvpickle.Usage ) = "-u <uid> -g <guild_id> -x <channel_id> -t <activate_type 1.send msg 2.hold mic 3.off mic>";
	}
	
	rpc GetGuildListByGame( GetGuildListByGameReq ) returns( GetGuildListByGameResp ){
		option( tlvpickle.CmdID ) = 11;										
        option( tlvpickle.OptString ) = "a:s:n:";							
        option( tlvpickle.Usage ) = "-a <game_id> -s <start> -n <count>";
	}
	
	rpc AddGuildTypeInfo( AddGuildTypeInfoReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 12;										
        option( tlvpickle.OptString ) = "g:t:";							
        option( tlvpickle.Usage ) = "-g <guild_id> -t <recommend_type 1.recommend_lib 2.black_list>";
	}
	
	rpc DelGuildTypeInfo( DelGuildTypeInfoReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 13;										
        option( tlvpickle.OptString ) = "g:t:";							
        option( tlvpickle.Usage ) = "-g <guild_id> -t <recommend_type 1.recommend_lib 2.black_list>";
	}
	
	rpc GetGuildTypeInfoList( GetGuildTypeInfoListReq ) returns( GetGuildTypeInfoListResp ){
		option( tlvpickle.CmdID ) = 14;										
        option( tlvpickle.OptString ) = "t:s:n:";							
        option( tlvpickle.Usage ) = "-t <recommend_type 1.recommend_lib 2.black_list> -s <start> -n <count>";
	}
	
	rpc RemoveGuildInfoByGuildId( RemoveGuildInfoByGuildIdReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 15;										
        option( tlvpickle.OptString ) = "g:";							
        option( tlvpickle.Usage ) = "-g <guild_id>";
	}
}

