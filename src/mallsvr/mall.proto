syntax="proto2";

// svrkit support
import "common/tlvpickle/skbuiltintype.proto";

package Mall;

enum CurrencyBizMinType {
    Biz_Purchase = 11;
    Biz_Feedback = 12;
    Biz_Share = 13;
}

// 商品类别
message Category {
	required uint32 category_id 	= 1;	// 类别ID
	required string category_name 	= 2;	// 类别名称
}

// 商品
message Product {
	enum ProductStatus {
		Normal = 0;
		Deleted = 1;
	}

	enum ProductItemType {
		AccountAndPassword	= 1;	// 帐号密码
		Medal = 2;
		TTStaff = 3;
    }

	required uint32 product_id 		= 1;	// 商品ID
	required string name 			= 2;	// 商品名称
	required uint32 category_id 	= 3;	// 类别ID
	required string description 	= 4;	// 商品描述
	required uint32 status			= 5;	// 商品状态 see ProductStatus
	required uint32 price 			= 6;	// 商品价格
	required uint32 level_required 	= 7;	// 商品要求等级
	required uint32 game_id 		= 8;	// 绑定的游戏ID
	required string icon_url 		= 9;	// 商品图标
	required uint32 item_type 		= 10;	// item类型, see ProductItemType
}

// 物品
message ProductItem {
	required string item_hash	= 1;	// hash, 唯一标识
	required bytes item_binary 	= 2;	// 物品内容
	required uint32 product_id 	= 3;	// 所属的商品ID
}

// 用户的物品
message UserProductItem {
	required uint32 uid					= 1;	// uid
	required uint64 timestamp 			= 2;	// 获取时间
	required Product product_snapshot	= 3;	// 商品快照
	required ProductItem item_snapshot	= 4;	// 物品快照
	required string order_id			= 5;	// 订单号
}

message ProductIdList {
	repeated uint32 product_id_list	= 1;
}

message ProductList {
	repeated Product product_list	= 1;
}

message CategoryList {
	repeated Category category_list = 1;
}

message GetProductReg {
    required uint32 uid = 1;
    required uint32 index = 2;
    required uint32 count = 3;
}

//=======================================================
// Item Descriptor
//=======================================================

message ProductItem_AccountPassword {
    required string account 	= 1;
    required string password 	= 2;
}


message ProductItem_Medal {
    required uint32 medal_id = 1;
    required string medal_name = 2;
}

message ProductItem_TTStaff {
    required uint32 staff_id = 1;
    required string staff_name = 2;
}

//=======================================================
// Activity
//=======================================================

message ActivityProductConfig {
	required uint32 product_id = 1;
	required uint32 count = 2;
	optional uint32 coeff = 3;	// 放大系数
}

// 活动
message Activity {
	enum ActivityStatus {
		Normal = 0;
		Locking = 1;
		Opening = 2;
		Closed = 3;
		Deleted = 99;
	}

	required uint32 activity_id 	= 1;	// 活动ID
	required string name 			= 2;
	required uint64 begin_time		= 3;	// unix timestamp, 秒
	required uint64 end_time		= 4;	// unix timestamp, 秒
	required uint32 status 			= 5;	// 活动状态
	repeated ActivityProductConfig activity_product_config_list = 6;
}

message ActivityProductDetail {
	required Product product 		= 1;
	required uint32 total			= 2;	// 上架商品总数
	required uint32 remain			= 3;	// 剩余数量
	optional uint32 sold_out		= 4;	// 如果有值, 表时从活动开始到抢空所用的时间
	optional uint32 total_purchased	= 5;	// 累计购买次数
}

// 当前活动详情
message CurrentActivityDetail {
	required Activity activity 				= 1;
	repeated ActivityProductDetail product_list	= 2;
}

message ActivityProductResult {
    required uint32 product_id = 1;
    required uint32 total = 2;
    required uint32 purchased = 3;
    optional uint32 sold_out = 4;
}

message ActivityPurchaseRecord {
    required uint32 product_id = 1;
    required uint32 purchase_uid = 2;
    required uint64 timestamp = 3;
}

// 活动快照
message ActivitySnapshot {
    required Activity activity = 1;                                     // 活动
    repeated Product product_snapshot_list = 2;                         // 商品快照列表
    repeated ActivityProductResult activity_product_result_list = 3;    // 各商品结果
    repeated ActivityPurchaseRecord purchase_time_line = 4;             // 抢购时间线
}

//=======================================================
// Req / Resp
//=======================================================

message CreateProductReq {
	required string name 			= 1;
	required uint32 category_id 	= 2;
	required string description 	= 3;
	required uint32 price 			= 4;
	required uint32 level_required 	= 5;
	required uint32 game_id 		= 6;
	required string icon_url 		= 7;
}

message CreateProductResp {
	required uint32 product_id		= 1;
}

message UpdateProductReq {
	required uint32 product_id		= 1;
	optional string name 			= 2;
	optional string description		= 3;
	optional uint32 price			= 4;
	optional uint32 level_required 	= 5;
	optional uint32 game_id			= 6;
	optional string icon_url		= 7;
}

message GetProductsByCategoryReq {
	required uint32 category_id		= 1;
	required uint32 from_index		= 2;
	required uint32 count			= 3;
}

message AddProductItemReq_AccountPassword {
	required uint32 product_id									= 1;
	repeated ProductItem_AccountPassword account_password_list 	= 2;
}

message AddProductItemResp_AccountPassword {
	repeated ProductItem_AccountPassword success_account_password_list 	= 1;
	repeated ProductItem_AccountPassword failed_account_password_list	= 2;
}

message RemainInfo {
    required uint32 product_id = 1;
    required uint32 item_num = 2;
}

message GetProductsRemainReq {
	repeated uint32 product_id_list = 1;
	optional uint32 query_for_activity = 2;
}

message GetProductsRemainResp {
    repeated RemainInfo  remain_info_list = 1;
}

message GetProductRemainsByCategoryReq {
    required uint32 category_id = 1;
}

message GetProductRemainsByCategoryResp {
    required uint32 remain = 1;
}

message GetProductItemReq {
    required uint32 product_id = 1;
}

message GetProductItemResp {
    repeated ProductItem product_item_list = 1;
}

message DeleteProductItemReq {
    required uint32 product_id = 1;
    repeated string hash_list = 2;
}

message CreateActivityReq {
	required string name 			= 1;
	required uint64 begin_time		= 2;	// unix timestamp, 秒
	required uint64 end_time		= 3;	// unix timestamp, 秒
	repeated ActivityProductConfig activity_product_config_list = 4;
}

message CreateActivityResp {
	optional uint32 activity_id		= 1;
	optional string error_message 	= 2;
}

message UpdateActivityReq {
	required uint32 activity_id		= 1;
	required string name 			= 2;
	required uint64 begin_time		= 3;	// unix timestamp, 秒
	required uint64 end_time		= 4;	// unix timestamp, 秒
	repeated ActivityProductConfig activity_product_config_list = 5;
}

message UpdateActivityResp {
	optional string error_message	= 1;
}

message DeleteActivityReq {
	required uint32 activity_id 	= 1;
}

message CloseActivityReq {
	required uint32 activity_id 	= 1;
}

message GetActivityListReq {
	enum Options {
		NotBegin 	= 1;
		Opening 	= 2;
		Closed		= 4;
	}

	required uint32 options = 1;
	required uint32 from_index = 2;
	required uint32 count = 3;
	required bool reverse = 4;	// true: 将按照beginTime进行逆序查询
}

message GetActivityListResp {
	repeated Activity activity_list = 1;
}

message AddProductItemsReq {
    repeated ProductItem product_item_list = 1;
}

message AddProductItemsResp {
    repeated ProductItem success_product_item_list = 1;
    repeated ProductItem failed_product_item_list = 2;
}

message PurchaseActivityProductItemReq {
	required uint32 uid = 1;
	required uint32 activity_id = 2;
	required uint32 product_id = 3;
    optional uint32 activity_category = 4;
}

message PurchaseActivityProductItemResp {
	optional UserProductItem purchased_item = 1;
	optional ActivityProductDetail purchasing_product_detail = 2;
	optional Activity current_activity = 3;
}

message GetUserProductsReq {
    required uint32 uid = 1;
    required uint32 index = 2;
    required uint32 count = 3;
}

message GetUserProductsResp {
    repeated UserProductItem user_item_list = 1;
}

message SetCurrentActivityReq {
	required uint32 activity_id = 1;
    optional uint32 activity_category = 2;      // default is Android_TT, see protodef.h
}

message ActivityCategoryReq {
    optional uint32 activity_category = 1;      // default is Android_TT, see protodef.h
}

message FeedbackDiamondReq {
    required uint32 uid = 1;
    required string order_id = 2;
}

message FeedbackDiamondResp {
    required uint32 get_diamond = 1;
    required uint32 user_diamond = 2;
}

message GetUserProductItemByOrderReq {
	required uint32 uid = 1;
	required string order_id = 2;
}

message GetProductTotalPurchaseTimesReq {
    repeated uint32 product_id_list = 1;
}

message ProductTotalPurchaseTimes {
    required uint32 product_id = 1;
    required uint32 total_purchased_times = 2;
}

message GetProductTotalPurchaseTimesResp {
    repeated ProductTotalPurchaseTimes product_purchase_time_list = 1;
}

message GetActivityWinningReq {
	required uint32 activity_id = 1;
}


message ActivityWinningDetail {
	required string product_name = 1;
	required uint32 uid  = 2;
	required string account = 3;
	required string pwd = 4;
}

message GetActivityWinningDetailResp {
	repeated ActivityWinningDetail winning_list = 1;
}

message ActivityWinningCount {
	required string product_name = 1;
	required uint32 count  = 2;
}

message GetActivityWinningCountResp {
	repeated ActivityWinningCount count_list = 1;
}

service Mall {
	/**
	 * Server listening port
	 */
	option( tlvpickle.Magic ) = 15190;

	// 0 - 99, product related

	rpc GetCategories( tlvpickle.SKBuiltinEmpty_PB ) returns ( CategoryList ) {
		option( tlvpickle.CmdID ) = 1;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc CreateProduct( CreateProductReq ) returns ( CreateProductResp ) {
		option( tlvpickle.CmdID ) = 2;
		option( tlvpickle.OptString ) = "n:k:d:p:l:g:o:";
		option( tlvpickle.Usage ) = "-n <name> -k <category> -d <desc> -p <price> -l <level_required> -g <game_id> -o <icon>";
	}

	rpc UpdateProduct( UpdateProductReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 3;
		option( tlvpickle.OptString ) = "i:n:d:p:l:g:o:";
		option( tlvpickle.Usage ) = "-i <product_id> -n <name> -d <desc> -p <price> -l <level_required> -g <game_id> -o <icon>";
	}

	rpc DeleteProducts( ProductIdList ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 4;
		option( tlvpickle.OptString ) = "i:";
		option( tlvpickle.Usage ) = "-i <product_id_list>";
	}

	rpc GetProductsByPage( GetProductReg ) returns ( ProductList ) {
		option( tlvpickle.CmdID ) = 5;
		option( tlvpickle.OptString ) = "i:n:";
		option( tlvpickle.Usage ) = "-i <index> -n <count>";
	}

	rpc GetProductsByCategory( GetProductsByCategoryReq ) returns ( ProductList ) {
		option( tlvpickle.CmdID ) = 6;
		option( tlvpickle.OptString ) = "k:r:n:";
		option( tlvpickle.Usage ) = "-k <category> -r <from_index> -n <count>";
	}

	rpc AddProductItem_AccountPassword( AddProductItemReq_AccountPassword ) returns ( AddProductItemResp_AccountPassword ) {
		option( tlvpickle.CmdID ) = 7;
		option( tlvpickle.OptString ) = "p:i:";
		option( tlvpickle.Usage ) = "-p <product_id> -i <account1:password1,account2:password2,...>";
	}

	rpc GetProductsRemain( GetProductsRemainReq ) returns ( GetProductsRemainResp ) {
       	option( tlvpickle.CmdID ) = 8;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
    }

	rpc GetProductsByIdList( ProductIdList ) returns ( ProductList ) {
		option( tlvpickle.CmdID ) = 9;
		option( tlvpickle.OptString ) = "i:";
		option( tlvpickle.Usage ) = "-i <id,id>";
	}


    rpc GetProductRemainsByCategory( GetProductRemainsByCategoryReq ) returns ( GetProductRemainsByCategoryResp ) {
		option( tlvpickle.CmdID ) = 10;
		option( tlvpickle.OptString ) = "i:";
		option( tlvpickle.Usage ) = "-i <category_id>";
    }

    rpc GetProductItemList( GetProductItemReq ) returns ( GetProductItemResp ) {
		option( tlvpickle.CmdID ) = 11;
		option( tlvpickle.OptString ) = "i:";
		option( tlvpickle.Usage ) = "-i <product_id>";
    }

    rpc DeleteProductItems( DeleteProductItemReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 12;
		option( tlvpickle.OptString ) = "i:";
		option( tlvpickle.Usage ) = "-i <product_id>";
    }

    rpc AddProductItems( AddProductItemsReq ) returns ( AddProductItemsResp ) {
		option( tlvpickle.CmdID ) = 13;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
    }

	rpc GetProductSeedItem( GetProductItemReq ) returns ( ProductItem ) {
		option( tlvpickle.CmdID ) = 14;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

    rpc GetProductTotalPurchaseTimes( GetProductTotalPurchaseTimesReq ) returns ( GetProductTotalPurchaseTimesResp ) {
        option( tlvpickle.CmdID ) = 15;
        option( tlvpickle.OptString ) = "-i:";
        option( tlvpickle.Usage ) = "-i <product_id>";
    }

	// 100 - 199, Activity related
	rpc CreateActivity( CreateActivityReq ) returns ( CreateActivityResp ) {
		option( tlvpickle.CmdID ) = 100;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc UpdateActivity( UpdateActivityReq ) returns ( UpdateActivityResp ) {
		option( tlvpickle.CmdID ) = 101;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc DeleteActivity( DeleteActivityReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 102;
		option( tlvpickle.OptString ) = "a:";
		option( tlvpickle.Usage ) = "-a <activity_id>";
	}

	rpc GetActivityList( GetActivityListReq ) returns ( GetActivityListResp ) {
		option( tlvpickle.CmdID ) = 103;
		option( tlvpickle.OptString ) = "o:s:n:r:";
		option( tlvpickle.Usage ) = "-o <options> -s <start_from> -n <count> -r <reverse>";
	}

    // Deprecated, use GetCurrentActivityDetailByCategory instead
	rpc GetCurrentActivityDetail( tlvpickle.SKBuiltinEmpty_PB ) returns ( CurrentActivityDetail ) {
		option( tlvpickle.CmdID ) = 104;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc PurchaseActivityProductItem( PurchaseActivityProductItemReq ) returns (PurchaseActivityProductItemResp ) {
		option( tlvpickle.CmdID ) = 105;
		option( tlvpickle.OptString ) = "u:a:p:";
		option( tlvpickle.Usage ) = "-u <uid> -a <activity_id> -p <product_id>";
	}

	rpc SetCurrentActivity( SetCurrentActivityReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 106;
		option( tlvpickle.OptString ) = "a:k:";
		option( tlvpickle.Usage ) = "-a <activity_id> -k <category>";
	}

    // Deprecated
	rpc GetCurrentActivity( tlvpickle.SKBuiltinEmpty_PB ) returns ( Activity ) {
		option( tlvpickle.CmdID ) = 107;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc CloseActivity( CloseActivityReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 108;
		option( tlvpickle.OptString ) = "a:";
		option( tlvpickle.Usage ) = "-a <activity_id>";
	}

    rpc GetCurrentActivityDetailByCategory( ActivityCategoryReq ) returns ( CurrentActivityDetail ) {
		option( tlvpickle.CmdID ) = 109;
		option( tlvpickle.OptString ) = "k:";
		option( tlvpickle.Usage ) = "-k <category>";
	}

    // 201 - 299, User's storage related
    rpc GetUserProducts( GetUserProductsReq ) returns ( GetUserProductsResp ) {
		option( tlvpickle.CmdID ) = 201;
		option( tlvpickle.OptString ) = "i:";
		option( tlvpickle.Usage ) = "-i <categoryId>";
	}

    rpc FeedbackDiamond ( FeedbackDiamondReq ) returns ( FeedbackDiamondResp ) {
		option( tlvpickle.CmdID ) = 202;
		option( tlvpickle.OptString ) = "u:o:";
		option( tlvpickle.Usage ) = "-u <uid> -o<order_id>";
	}

	rpc GetUserProductItemByOrder( GetUserProductItemByOrderReq ) returns ( UserProductItem ) {
		option( tlvpickle.CmdID ) = 203;
		option( tlvpickle.OptString ) = "u:o:";
		option( tlvpickle.Usage ) = "-u <uid> -o<order_id>";
	}

	rpc GetActivityWinningDetail( GetActivityWinningReq ) returns ( GetActivityWinningDetailResp ) {
		option( tlvpickle.CmdID ) = 204;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}

	rpc GetActivityWinningCount( GetActivityWinningReq ) returns ( GetActivityWinningCountResp ) {
		option( tlvpickle.CmdID ) = 205;
		option( tlvpickle.OptString ) = "";
		option( tlvpickle.Usage ) = "";
	}
}
