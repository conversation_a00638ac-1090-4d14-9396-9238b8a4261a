syntax="proto2";


package ActivityRuntime;

message ActivityProductItem
{
    required uint32 product_id = 1;
    required string item_hash = 2;
    required uint32 item_weight = 3;
    optional bytes  item_bin = 4;
}

message ActivityMetadata
{
    required uint32 activity_id = 1;
    required uint32 begin_time = 2;
    required uint32 end_time = 3;
    required uint32 product_item_mc = 4;    // magnification coefficient
}

message ActivityProductStatus
{
    required uint32 product_id = 1;             // 商品ID
    required uint32 total = 2;                  // 总数
    required uint32 remain = 3;                 // 剩余权重
    required uint64 sold_out_timestamp = 4;     // 售罄时间
}

message ActivityStatus {
    repeated ActivityProductStatus product_status_list = 1;
}
