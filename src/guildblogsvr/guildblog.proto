syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package guildblog;

enum ActivityOsPlatform {
	ANDROID		= 1;
    IOS			= 2;
}


enum GuildRankType{
	FENG_YUN_BANG = 1;
	REN_QI_BANG = 2;
	HUO_YUE_BANG = 3;
	XIN_RUI_BANG = 4;
}


message ActivityDetail {
	required uint32 activity_id = 1;
    required string act_url = 2;		// 活动链接
    required string pic_url = 3;		// 首推活动头图链接
	required string title = 4;			// 标题
	required uint32 create_ts = 5;		// 活动的创建时间
	required uint32 update_ts = 6;		// 活动的更新时间
	required uint32 is_home_page = 7;	// 活动是否显示在首页
	required uint32 platform = 8;		// 活动平台
	required uint32 warmed_up_ts_begin = 9;	// 预热时间begin
	required uint32 warmed_up_ts_end = 10;	// 预热时间end
	required uint32 active_ts_begin = 11; 	// 运行时间begin
	required uint32 active_ts_end = 12; 	// 运行时间end
	required uint32 end_ts = 13;			// 结束时间
}

message GetGuildBlogActivitysReq {
    required uint32 is_home_page = 1;
	required uint32 platform = 2;
	optional uint32 start_index = 3;
	optional uint32 limit = 4;
}

message GetGuildBlogActivitysResp {
    repeated ActivityDetail activity_list = 1;
	required uint32 activity_total_size = 2;	// 活动总数量(指定平台&&指定类型)
}

message GetCircleActivitysReq {
    required uint32 is_home_page = 1;
	required uint32 platform = 2;
	optional uint32 start_index = 3;
	optional uint32 limit = 4;
}

message GetCircleActivitysResp {
    repeated ActivityDetail activity_list = 1;
	required uint32 activity_total_size = 2;	// 活动总数量(指定平台&&指定类型)
}

message GuildAdvDetail {
	required string pic_url = 1;
	required string adv_url = 2;
}

message GuildAnnDetail {
	required string title = 1;
	required string url = 2;
	required uint32 time_subscribe = 3;
}

message GetGuildBlogAdvReq {	
}

message GetGuildBlogAdvResp {	
	repeated GuildAdvDetail adv_list = 1;
}

message GetGuildBlogAnnReq {
    required uint32 start = 1;
	required int32 limit = 2;
	optional uint32 platform = 3;
}

message GetGuildBlogAnnResp {
    repeated GuildAnnDetail ann_list = 1;
}

message GetCircleAnnReq {
    required uint32 start = 1;
	required int32 limit = 2;
	optional uint32 platform = 3;
}

message GetCircleAnnResp {
    repeated GuildAnnDetail ann_list = 1;
}

message AddGuildBlogAdvReq {
	repeated GuildAdvDetail adv_list = 1;
	required bool add_op = 2;       // 如果是ture表示是增加 false表示是删除
	optional bool is_clean_old = 3; // 如果是ture表示在增加前 需要全部清除旧数据
}


message GetGuildRankListReq{
	required uint32 rank_type = 1;
	required uint32 start = 2;
	required uint32 limit = 3;
}

message GetGuildRankListResp{
	repeated uint32 guild_ids = 1;	
}


message ReportActiveUserReq {
	enum ActiveType{
		USER_CHECKIN = 1;
		USER_JOIN_GUILD = 2;
	}
	required uint32 uid = 1;
	required uint32 guild_id = 2;
	required uint32 active_type = 3;
	optional bool valid = 4; // 行为是否有效
}

message ReportGuildDismissReq
{
	required uint32 guild_id = 1;
}

// 运营扶植公会列表
message SupportGuildInfo
{
	required uint32 guild_id = 1;
	required uint32 expire_ts = 2;
	optional uint32 update_ts = 3 ;
	optional uint32 game_id = 4;
}

message GetAllSupportGuildListReq
{
	required bool is_need_expire = 1;
	required uint32 game_id = 2;
}
message GetAllSupportGuildListResp
{
	repeated SupportGuildInfo guild_list = 1;
}

message SetAllSupportGuildListReq
{
	repeated SupportGuildInfo guild_list = 1;
	required bool is_clean_old = 2;
	required uint32 game_id = 3;
}

message DelSupportGuildListReq
{
	required uint32 guild_id = 1;
	required uint32 game_id = 2;
}


service guildblog {

    option( tlvpickle.Magic ) = 15382;

    rpc UpdateGuildBlogActivitys ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 1;
 		option( tlvpickle.OptString ) = "";
 		option( tlvpickle.Usage ) = "";
 	}
	
	rpc GetGuildBlogActivitys ( GetGuildBlogActivitysReq ) returns ( GetGuildBlogActivitysResp ){
 		option( tlvpickle.CmdID ) = 2;
 		option( tlvpickle.OptString ) = "t:p:s:n:";
 		option( tlvpickle.Usage ) = "-t <is_home_page> -p <platform, 1.Android 2.Ios> -s <start_index> -n <limit>";
 	}

	// 获取广告列表
	rpc GetGuildBlogAdv ( GetGuildBlogAdvReq ) returns ( GetGuildBlogAdvResp ){
 		option( tlvpickle.CmdID ) = 10;
 		option( tlvpickle.OptString ) = "";
 		option( tlvpickle.Usage ) = "";
 	}

	rpc GetGuildBlogAnn ( GetGuildBlogAnnReq ) returns ( GetGuildBlogAnnResp ){
 		option( tlvpickle.CmdID ) = 11;
 		option( tlvpickle.OptString ) = "s:l:p:";
 		option( tlvpickle.Usage ) = "-s <start> -l <limit> -p <platform>";
 	}
	
	// 设置广告列表
	rpc AddGuildBlogAdv ( AddGuildBlogAdvReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 12;
 		option( tlvpickle.OptString ) = "p:a:";
 		option( tlvpickle.Usage ) = "-p <pic_url> -a <adv_url>";
 	}

	rpc FetchCmsData ( tlvpickle.SKBuiltinEmpty_PB ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 13;
 		option( tlvpickle.OptString ) = "";
 		option( tlvpickle.Usage ) = "";
 	}

	rpc GetGuildRankList ( GetGuildRankListReq ) returns ( GetGuildRankListResp ){
 		option( tlvpickle.CmdID ) = 15;
 		option( tlvpickle.OptString ) = "r:s:l:";
 		option( tlvpickle.Usage ) = "-r<rank_type> -s<start> -l<limit>";
 	}

	rpc ReportActiveUser ( ReportActiveUserReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 16;
 		option( tlvpickle.OptString ) = "u:g:r:";
 		option( tlvpickle.Usage ) = "-u<uid> -g<guild_id> -r<rank_type>";
 	}
	
	// 全量获取运营扶植公会列表
	rpc GetAllSupportGuildList ( GetAllSupportGuildListReq ) returns ( GetAllSupportGuildListResp ){
 		option( tlvpickle.CmdID ) = 17;
 		option( tlvpickle.OptString ) = "";
 		option( tlvpickle.Usage ) = "";
 	}
	
	// 设置运营扶植公会列表
	rpc SetAllSupportGuildList ( SetAllSupportGuildListReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 18;
 		option( tlvpickle.OptString ) = "u:g:x:";
 		option( tlvpickle.Usage ) = "-u <uid> -g <guild_id_1> -x <guild_id_2>";
 	}
	
	rpc DelSupportGuildList ( DelSupportGuildListReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 19;
 		option( tlvpickle.OptString ) = "u:g:";
 		option( tlvpickle.Usage ) = "-u <uid> -g <guild_id>";
 	}
	
	rpc ReportGuildDismiss ( ReportGuildDismissReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
 		option( tlvpickle.CmdID ) = 20;
 		option( tlvpickle.OptString ) = "g:";
 		option( tlvpickle.Usage ) = "-g <guild_id>";
 	}

	rpc GetCircleActivitys ( GetCircleActivitysReq ) returns ( GetCircleActivitysResp ){
 		option( tlvpickle.CmdID ) = 21;
 		option( tlvpickle.OptString ) = "t:p:s:n:";
 		option( tlvpickle.Usage ) = "-t <is_home_page> -p <platform, 1.Android 2.Ios> -s <start_index> -n <limit>";
 	}

	rpc GetCircleAnn ( GetCircleAnnReq ) returns ( GetCircleAnnResp ){
 		option( tlvpickle.CmdID ) = 22;
 		option( tlvpickle.OptString ) = "s:l:p:";
 		option( tlvpickle.Usage ) = "-s <start> -l <limit> -p <platform>";
 	}
	
	
}