syntax="proto2";


package channelapi;

import "common/tlvpickle/skbuiltintype.proto";


enum E_REQ_SOURCE_TYPE
{
	ENUM_REQ_SOURCE_LOGIC = 1;
	ENUM_REQ_SOURCE_GOLOGIC = 2;
	ENUM_REQ_SOURCE_GOSVR = 3;
}

message ApiBaseReq{
	optional uint32 uid = 1;
    optional uint32 app_id = 2;
	optional uint32 market_id = 3;
	optional uint32 source_type = 4; // 请求的来源
	optional string source_msg = 5;  // E_REQ_SOURCE_TYPE
	
	optional uint32 client_version = 6;
	optional uint32 client_type = 7;
	optional string client_ip = 8;
	optional uint32 client_terminal = 9;
	
}

message ApiBaseResp{

    required int32 ret = 1;
    optional string err_msg = 2; 
}

message BaseMicrSpace
{
	required uint32 mic_id = 1;              // 麦位ID 1 - 9
	optional uint32 mic_state = 2;           // EMicrSpaceState 麦位状态 1 正常 2 禁用 3 不可发言
	optional uint32 mic_uid = 3;             // 麦上用户UID 如果麦上有人的话
	optional uint32 hold_ts = 4;    	     // 麦上用户的上麦时间
}

message ExtMicrSpace
{
	required BaseMicrSpace base_info = 1;

}

// 设置麦模式
message SetMicModeReq{
    required ApiBaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 mic_mode = 3;
}

message SetMicModeResp{
    required ApiBaseResp base_resp = 1;
    repeated BaseMicrSpace mic_list = 2; // 操作完之后 完整的麦位信息
    optional uint64 server_time_ms  = 3; // 64bit 毫秒级 服务器时间
}

// 房间改名
message ModifyNameReq{
    required ApiBaseReq base_req = 1;
    required uint32 channel_id = 2;
    required string name = 3;
	
	required bool is_skip_anti_check = 4; // 跳过铭感词检测
  oneof audit_strategy{
    SyncAuditStrategy sync_strategy= 5; // 同步策略
  }
}

message SyncAuditStrategy {
  optional bool is_report_bizrecord_id = 1; // 是否上报业务记录ID
  optional ReportBizRecordIdStrategy report_strategy = 2; // 上报业务记录ID策略
}

message ReportBizRecordIdStrategy {
  optional string biz_record_id = 1; // 业务记录ID
  optional string scene_code = 2; // 场景码
  optional string ttid = 3; // ttid 业务有就填
}
message ModifyNameResp{
    required ApiBaseResp base_resp = 1;
}

service channelapi{
    option( tlvpickle.Magic ) = 15301;		// 服务监听端口号
	

    rpc SetMicMode( SetMicModeReq ) returns ( SetMicModeResp ){
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc ModifyName( ModifyNameReq ) returns ( ModifyNameResp ){
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

}
