syntax="proto2";

package LogicAsync;

message LogicAsyncTasks {
    enum Values {
        NotifyUserInfoUpdate = 1;		        // 通知用户信息变化
        NotifyGuildInfoUpdate = 2;		        // 通知公会信息变化
        NotifyCircleTopicDelete = 3;	        // 通知圈子主题删除
        HandleMission = 4;                      // 任务
        NotifyCircleUpdate = 5;                 // 通知圈子有更新(新主题)
        NotifyPublicAccountUpdate = 6;	        // 通知公众号有更新
        GetThirdPartyUserAvatar = 7;            // 获取第三方用户头像
		NotifyGroupInfoUpdate = 8;              // 群组公告
        UpdateMulticastMessage = 9;             // 更新组播消息(公众号等)
		CheckWechatUnionId = 10;		        // 检查微信登录用户的unionid
        ReportUserAlivenessEvent = 11;          // 上报用户活跃事件
        EventCenterPublish = 12;                // 发布至事件中心
        ReportIMMSG = 13;                       // IM
        UserUploadFaceAsync = 14;		        //  异步处理用户上传头像
        CircleImageRecognize = 15;		        //  图片鉴定
        UploadImAttachmentImg = 16;             //  聊天图片附件
        UploadChannelAttachmentImg = 17;        // 频道图片附件
        HandleGuildJoinMission = 18;            // 进公会任务
        ModifyGuildMemberNicknamePrefix = 19;   // 修改公会成员的昵称前缀
        AddInviteCodeTask = 20;                 // 添加正确邀请码
        ReportAdvertTask = 21;                  // 上报数据至广告服务
		RegAsyncTask = 22;                      // 注册异步事件
        NotifyActivateGuildRecommend = 23; 	    // 上报公会达到推荐激活条件
        LoginAsyncTask = 24;                    // 用户登录异步事件
        CompactMission = 25;                    // 因为每日任务刷新改到5点，为客户端做的兼容
		USER_DAILY_CHECKIN = 26;				// 用户每日签到
        RecordUserMatchIM = 27;               // 记录标配匹配用户的 im 消息
    }
}

message ServiceInfo {
    required uint32 uid = 1;
    required uint32 cmd = 2;
    required uint32 client_version = 3;
    required string client_ip = 4;
    required bytes  device_id = 5;
    required uint32 client_type = 6;
    required uint32 terminal_type = 7;
    optional int32 client_port = 8;
}

message RegAsyncTaskData {
    required uint32 uid = 1;
	
	optional string idfa = 2; 			    // idfa, only for IOS
	optional string mac  = 3; 			    // eth0 MAC地址原值, only for android
	optional string imei  = 4; 			    // imei , only for android
	optional string pkg_channel = 5;        // 包渠道
	
	optional string reg_invite_code = 6;	// 注册时填写的邀请码
    optional uint32 package_type = 7;		// 客户端包类型
	optional uint32 third_party_type = 8;   // 第三方账号类型
	
	optional int64 reg_timestamp_ms = 9;    //  毫秒 时间戳
	optional string client_ip = 10;         //  IP
	
	optional uint32 auto_join_guild_id = 11;  //  如果需要自动加公会 那么这里填写工会ID
	optional ServiceInfo service_info  = 12;
	optional bytes client_feature = 13;      // req中的feature值
	optional string user_nick = 14;         // 
	optional string user_account = 15;      // 
	optional uint32 market_id = 16;         // base_req().market_id()
	optional uint32 app_id = 17;            //
}


message NotifyUserInfoUpdateTaskData {
    required uint32 uid = 1;
}

message NotifyGuildInfoUpdateTaskData {
    required uint32 guild_id = 1;
}

message MissionTaskData {
    required uint32 req_cmd_id = 1;
    required bytes req_body = 2;
    optional uint32 uid = 3;
    optional uint32 client_type = 4;
    optional ServiceInfo service_info = 5;
}

message NotifyCircleTopicDeleteTaskData {
	required uint32 circle_id = 1;
	required uint32 topic_id = 2;
}

message NotifyCircleUpdateTaskData {
	enum UpdateType {
		NEW_TOPIC = 1;
		ACT_BEGIN = 2;
		ACT_END = 3;
	}

    message CircleUpdate_NewTopic {
    	required uint32 topic_id = 1;
    }

    message CircleUpdate_ActBegin {
    	required uint32 topic_id = 1;
    	required string act_desc = 2;
    	required uint64 expire_time = 3;
    }

    message CircleUpdate_ActEnd {
    	required uint32 topic_id = 1;
    }

	required uint32 circle_id = 1;
	required uint32 update_type = 2;
	optional CircleUpdate_NewTopic new_topic_update = 3;
	optional CircleUpdate_ActBegin act_begin_update = 4;
	optional CircleUpdate_ActEnd act_end_update = 5;
}


message NotifyGroupInfoUpdateTaskData {
	required uint32 group_id = 1;
}

message GetThirdPartyUserAvatarTaskData {
	required uint32 uid = 1;
	required uint32 third_party_type = 2;
	required string openid = 3;
	required string access_token = 4;
}

message UpdateMulticastMessageTaskData {
    enum UpdateOption {
        UPDATE_OPTION_BROADCAST_TIMELINE = 1;
        UPDATE_OPTION_PUBLIC_TIMELINE = 2;
    }

    required uint32 uid = 1;
    required uint32 update_options = 2;
    required uint32 client_type = 3;
}

message CheckWechatUnionIdData {
	required uint32 uid = 1;
	required string open_id = 2;
	required string access_token = 3;
}

message ReportUserAlivenessEventTaskData {
    enum Type {
        OPEN_APP = 1;
        HEARTBEAT = 2;
    }

    required uint32 type = 1;
    required uint32 uid = 2;
    optional uint32 guild_id = 3;
    optional uint32 timestamp = 4;
}

message EventCenterPublishData {
    required uint32 publish_type = 1;
    required uint32 event_type = 2;
    required bytes event_data = 3;
}

message ReportIMMSGData{
    enum ReportUsage{
        IM_AUDIT = 1;   //消息审核
    }
    required uint32 usage = 1;
    required uint32 sender_id = 2;
    required uint32 receiver_id = 3;
    required string sender_name = 4;
    required string receiver_name = 5;
    required uint32 im_type = 6;    
    required string content = 7;    
	required uint32 server_msg_time = 8;
}

message UserUploadFaceContext {
	required uint32 op_uid = 1;
	required string account = 2;
	required uint32 account_type = 3;
	required uint32 account_id = 4;
	required bytes bigFace = 5;
	optional uint32 guildgroup_guildid = 6;
	optional bytes src_req_bin = 7;
}

// comment字段:
//   0 - 鉴定帖子
//  非0 - 鉴定评论
message CircleImageRecognizeContext {
	required uint32 creator_uid = 1;
	required uint32 circle_id = 2;
	required uint32 topic_id = 3;
	optional uint32 comment_id = 4;
	repeated string image_url_list = 5;
	optional uint32 topic_seq_id = 6;
}

//群聊 图片
message UploadImAttachmentImgData{
    required uint32 sender_uid = 1;
    required uint32 svr_msg_id = 2;
    required bytes attachment_img = 3;
    required string attachment_key = 4;	
    required bytes file_property = 5; 
}

//频道 图片
message UploadChannelAttachmentImgData{
    required uint32 sender_uid = 1;
    required uint32 channel_id = 2;
    required uint32 channel_msg_type = 3;
    required uint32 svr_time = 4;

    //image info
    required bytes attachment_img = 5;
    required string attachment_img_format = 6;

   //attachment server info
   required int32 attachment_svr_id = 7;

   //channelim server info
    required string channelim_attachment_key = 8;
    required uint32 channelim_attachment_ttl = 9;
}

message GuildJoinMissionData {
    required uint32 uid = 1;
    required uint32 guild_id = 2;
}

message ModifyGuildMemberNicknamePrefixData {
	required uint32 guild_id = 1;
	required string guild_nickname_prefix = 2;
}

//添加邀请码成功
message AddInviteCodeTaskData{
    required uint32 uid=1;
    required string nickname=2;
    required uint32 invite_uid=3;
    required bool invalid=4;
    required uint32 package_type=5;
    required uint32 current_guild_id=6; //invitor_guildid
    required string clientDeviceId=7;
}


//上报广告服务
message ReportAdvertTaskData{
    required uint32 dev_type = 1;   //见adservice.proto DeviceInfoType
    required string dev_info = 2;   //设备信息  android imei deviceId or ios idfa deviceId
}

//上报公会推荐服务
message NotifyActivateGuildRecommendData{
	required uint32 uid = 1;
	required uint32 guild_id = 2;
	required uint32 channel_id = 3;
	optional uint32 activate_type = 4;	// see guildrecommend::ActivateGuildType
}

message LoginAsyncTaskData {
    required ServiceInfo service_info = 1;
    required uint32      uid = 2;
    required bool        is_first_time_login = 3;
    required bytes       request = 4;
    required int32       result_code = 5;
    optional bool        is_device_invalid = 6; // default is false
}

//用户每日签到
message UserDailyCheckInData {
    required uint32      uid = 1;
    required uint32      checkin_type = 2;
	required uint32 	 client_type = 3;
	required uint32		 client_version = 4;
}

// 记录 标签匹配 用户的 im
message RecordUserMatchIMData{
  required uint32 from_uid = 1;
  required uint32 to_uid = 2;
  required uint32 source_type = 3;
  required uint32 im_time = 4;          // 发起时间
  optional uint32 type = 5;
  optional string content = 6;
}
