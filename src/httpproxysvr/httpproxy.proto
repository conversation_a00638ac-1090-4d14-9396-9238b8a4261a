syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package HttpProxy;

// 专用错误码从-30开始
enum ERR_SMS
{
    ERR_VALID_APK_URL_CANNOT_REACH = -31;   // URL访问不到
    ERR_VALID_APK_URL_NOT_APK   = -32;      // 非apkurl
}

message CheckUrlValidApkUrlReq {
	required string url = 1;
}

message CheckUrlValidApkUrlResp {
	required uint32 content_length = 1;
}

message DownLoadUrlReq{
	required string url = 1;
    optional uint32 timeout = 2; //单位:秒
}

message DownLoadUrlResp{
	required string msg = 1;
}

message DownLoadUrlByteReq{
	required string url = 1;
    optional uint32 timeout = 2;
}

message DownLoadUrlByteResp{
	required bytes msg = 1;
}

// post url 接口
message PostUrlDataReq{
	required string url = 1;
	repeated bytes head_info_list = 2;
	required bytes data_info = 3;
}

message PostUrlDataResp{
	required bytes resp_msg = 1;
}

///////////////////////
message PostReq {
    enum ContentType {
        NODATA = 0;
        POSTFIELDS = 1;   //CURLOPT_POSTFIELDS, post_data
        //reserved
        //HTTPPOST      //HTTPPOST
        //CURLOPT_MIMEPOST
    }

    required string url = 1;
    repeated string headers = 2; 
    required int32 type = 3;
    optional string post_data = 4;  //type=POSTFIELDS 
    optional int32 timeout = 5; //单位:ms 
    optional int32 conn_timeout = 6; //单位:ms 

}

message PostResp {
    optional int32 resp_code = 1;
    optional string resp_content = 2;

}

service HttpProxy {
	option( tlvpickle.Magic ) = 15678;		// 服务监听端口号

    //不再用调用
	rpc CheckUrlValidApkUrl( CheckUrlValidApkUrlReq ) returns( CheckUrlValidApkUrlResp ){
		option( tlvpickle.CmdID ) = 1;									// 命令号
        option( tlvpickle.OptString ) = "k:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-k <url>";	// 测试工具的命令号帮助
	}

	rpc DownLoadUrl( DownLoadUrlReq ) returns( DownLoadUrlResp ){
		option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u <url> [-t <timeout_sec>]";
	}

	rpc DownLoadUrlByte( DownLoadUrlByteReq ) returns( DownLoadUrlByteResp ){
		option( tlvpickle.CmdID ) = 3;									// 命令号
        option( tlvpickle.OptString ) = "k:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-k <url>";	// 测试工具的命令号帮助
    }

    //should deprecated
	rpc PostUrlData( PostUrlDataReq ) returns( PostUrlDataResp ){
		option( tlvpickle.CmdID ) = 4;					
        option( tlvpickle.OptString ) = "k:x:s:";				
        option( tlvpickle.Usage ) = "-k <url> -x <head info> -s <data info>";
	} 
    rpc Post (PostReq) returns (PostResp) {
		option( tlvpickle.CmdID ) = 6;					
        option( tlvpickle.OptString ) = "k:t:s:h:d:";
        option( tlvpickle.Usage ) = "-k <url> [-t <timeout_ms, default:5s>] [-s <conn_timeout_ms, default:2s>] -h <headers, sep by sp> -d <data>";
    }

}



