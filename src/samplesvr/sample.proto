syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Sample;

message SMSReq {
	required string message = 1;
}

message SMSResp {
	required string message = 1;
}

message AddReq {
	required uint32 lhs = 1;
	required uint32 rhs = 2;
}

message AddResp {
	required uint32 result = 1;
}

message ScheduleReq {
}

message ScheduleResp {
}

message FindReq {
    required uint32 id = 1;
}

message FindResp {
    required string message = 1;
}

service Sample {
	option( tlvpickle.Magic ) = 34567;		// 服务监听端口号

	rpc SendSMS( SMSReq ) returns ( SMSResp ) {
		option( tlvpickle.CmdID ) = 1;										// 命令号
		option( tlvpickle.OptString ) = "m:";							// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-m <message>";	// 测试工具的命令号帮助
	}

	rpc Add( AddReq ) returns ( AddResp ) {
		option( tlvpickle.CmdID ) = 2;										// 命令号
		option( tlvpickle.OptString ) = "l:r:";							// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-l <num> -r <num>";	// 测试工具的命令号帮助
	}

	rpc ScheduleTask( ScheduleReq ) returns ( ScheduleResp ) {
		option( tlvpickle.CmdID ) = 3;										// 命令号
	}

    rpc FindFromDB( FindReq ) returns ( FindResp ) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "i:";
        option( tlvpickle.Usage ) = "-i <id>";
    }
}
