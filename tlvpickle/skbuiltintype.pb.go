// Code generated by protoc-gen-gogo.
// source: skbuiltintype.proto
// DO NOT EDIT!

/*
	Package tlvpickle is a generated protocol buffer package.

	It is generated from these files:
		skbuiltintype.proto

	It has these top-level messages:
		SKBuiltinInt32_PB
		SKBuiltinUint32_PB
		SKBuiltinChar_PB
		SKBuiltinUchar_PB
		SKBuiltinInt8_PB
		SKBuiltinUint8_PB
		SKBuiltinInt16_PB
		SKBuiltinUint16_PB
		SKBuiltinInt64_PB
		SKBuiltinUint64_PB
		SKBuiltinFloat32_PB
		SKBuiltinDouble64_PB
		SKBuiltinBuffer_PB
		SKBuiltinString_PB
		SKBuiltinEmpty_PB
		SKBuiltinEchoInfo_PB
*/
package tlvpickle

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import google_protobuf "github.com/gogo/protobuf/protoc-gen-gogo/descriptor"

import math1 "math"

import io "io"
import math2 "math"
import fmt1 "fmt"
import github_com_gogo_protobuf_proto "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type SKBuiltinInt32_PB struct {
	IVal uint32 `protobuf:"varint,1,req,name=iVal" json:"iVal"`
}

func (m *SKBuiltinInt32_PB) Reset()                    { *m = SKBuiltinInt32_PB{} }
func (m *SKBuiltinInt32_PB) String() string            { return proto.CompactTextString(m) }
func (*SKBuiltinInt32_PB) ProtoMessage()               {}
func (*SKBuiltinInt32_PB) Descriptor() ([]byte, []int) { return fileDescriptorSkbuiltintype, []int{0} }

func (m *SKBuiltinInt32_PB) GetIVal() uint32 {
	if m != nil {
		return m.IVal
	}
	return 0
}

type SKBuiltinUint32_PB struct {
	UiVal uint32 `protobuf:"varint,1,req,name=uiVal" json:"uiVal"`
}

func (m *SKBuiltinUint32_PB) Reset()                    { *m = SKBuiltinUint32_PB{} }
func (m *SKBuiltinUint32_PB) String() string            { return proto.CompactTextString(m) }
func (*SKBuiltinUint32_PB) ProtoMessage()               {}
func (*SKBuiltinUint32_PB) Descriptor() ([]byte, []int) { return fileDescriptorSkbuiltintype, []int{1} }

func (m *SKBuiltinUint32_PB) GetUiVal() uint32 {
	if m != nil {
		return m.UiVal
	}
	return 0
}

type SKBuiltinChar_PB struct {
	IVal int32 `protobuf:"varint,1,req,name=iVal" json:"iVal"`
}

func (m *SKBuiltinChar_PB) Reset()                    { *m = SKBuiltinChar_PB{} }
func (m *SKBuiltinChar_PB) String() string            { return proto.CompactTextString(m) }
func (*SKBuiltinChar_PB) ProtoMessage()               {}
func (*SKBuiltinChar_PB) Descriptor() ([]byte, []int) { return fileDescriptorSkbuiltintype, []int{2} }

func (m *SKBuiltinChar_PB) GetIVal() int32 {
	if m != nil {
		return m.IVal
	}
	return 0
}

type SKBuiltinUchar_PB struct {
	UiVal uint32 `protobuf:"varint,1,req,name=uiVal" json:"uiVal"`
}

func (m *SKBuiltinUchar_PB) Reset()                    { *m = SKBuiltinUchar_PB{} }
func (m *SKBuiltinUchar_PB) String() string            { return proto.CompactTextString(m) }
func (*SKBuiltinUchar_PB) ProtoMessage()               {}
func (*SKBuiltinUchar_PB) Descriptor() ([]byte, []int) { return fileDescriptorSkbuiltintype, []int{3} }

func (m *SKBuiltinUchar_PB) GetUiVal() uint32 {
	if m != nil {
		return m.UiVal
	}
	return 0
}

type SKBuiltinInt8_PB struct {
	IVal int32 `protobuf:"varint,1,req,name=iVal" json:"iVal"`
}

func (m *SKBuiltinInt8_PB) Reset()                    { *m = SKBuiltinInt8_PB{} }
func (m *SKBuiltinInt8_PB) String() string            { return proto.CompactTextString(m) }
func (*SKBuiltinInt8_PB) ProtoMessage()               {}
func (*SKBuiltinInt8_PB) Descriptor() ([]byte, []int) { return fileDescriptorSkbuiltintype, []int{4} }

func (m *SKBuiltinInt8_PB) GetIVal() int32 {
	if m != nil {
		return m.IVal
	}
	return 0
}

type SKBuiltinUint8_PB struct {
	UiVal uint32 `protobuf:"varint,1,req,name=uiVal" json:"uiVal"`
}

func (m *SKBuiltinUint8_PB) Reset()                    { *m = SKBuiltinUint8_PB{} }
func (m *SKBuiltinUint8_PB) String() string            { return proto.CompactTextString(m) }
func (*SKBuiltinUint8_PB) ProtoMessage()               {}
func (*SKBuiltinUint8_PB) Descriptor() ([]byte, []int) { return fileDescriptorSkbuiltintype, []int{5} }

func (m *SKBuiltinUint8_PB) GetUiVal() uint32 {
	if m != nil {
		return m.UiVal
	}
	return 0
}

type SKBuiltinInt16_PB struct {
	IVal int32 `protobuf:"varint,1,req,name=iVal" json:"iVal"`
}

func (m *SKBuiltinInt16_PB) Reset()                    { *m = SKBuiltinInt16_PB{} }
func (m *SKBuiltinInt16_PB) String() string            { return proto.CompactTextString(m) }
func (*SKBuiltinInt16_PB) ProtoMessage()               {}
func (*SKBuiltinInt16_PB) Descriptor() ([]byte, []int) { return fileDescriptorSkbuiltintype, []int{6} }

func (m *SKBuiltinInt16_PB) GetIVal() int32 {
	if m != nil {
		return m.IVal
	}
	return 0
}

type SKBuiltinUint16_PB struct {
	UiVal uint32 `protobuf:"varint,1,req,name=uiVal" json:"uiVal"`
}

func (m *SKBuiltinUint16_PB) Reset()                    { *m = SKBuiltinUint16_PB{} }
func (m *SKBuiltinUint16_PB) String() string            { return proto.CompactTextString(m) }
func (*SKBuiltinUint16_PB) ProtoMessage()               {}
func (*SKBuiltinUint16_PB) Descriptor() ([]byte, []int) { return fileDescriptorSkbuiltintype, []int{7} }

func (m *SKBuiltinUint16_PB) GetUiVal() uint32 {
	if m != nil {
		return m.UiVal
	}
	return 0
}

type SKBuiltinInt64_PB struct {
	LlVal int64 `protobuf:"varint,1,req,name=llVal" json:"llVal"`
}

func (m *SKBuiltinInt64_PB) Reset()                    { *m = SKBuiltinInt64_PB{} }
func (m *SKBuiltinInt64_PB) String() string            { return proto.CompactTextString(m) }
func (*SKBuiltinInt64_PB) ProtoMessage()               {}
func (*SKBuiltinInt64_PB) Descriptor() ([]byte, []int) { return fileDescriptorSkbuiltintype, []int{8} }

func (m *SKBuiltinInt64_PB) GetLlVal() int64 {
	if m != nil {
		return m.LlVal
	}
	return 0
}

type SKBuiltinUint64_PB struct {
	UllVal uint64 `protobuf:"varint,1,req,name=ullVal" json:"ullVal"`
}

func (m *SKBuiltinUint64_PB) Reset()                    { *m = SKBuiltinUint64_PB{} }
func (m *SKBuiltinUint64_PB) String() string            { return proto.CompactTextString(m) }
func (*SKBuiltinUint64_PB) ProtoMessage()               {}
func (*SKBuiltinUint64_PB) Descriptor() ([]byte, []int) { return fileDescriptorSkbuiltintype, []int{9} }

func (m *SKBuiltinUint64_PB) GetUllVal() uint64 {
	if m != nil {
		return m.UllVal
	}
	return 0
}

type SKBuiltinFloat32_PB struct {
	FVal float32 `protobuf:"fixed32,1,req,name=fVal" json:"fVal"`
}

func (m *SKBuiltinFloat32_PB) Reset()         { *m = SKBuiltinFloat32_PB{} }
func (m *SKBuiltinFloat32_PB) String() string { return proto.CompactTextString(m) }
func (*SKBuiltinFloat32_PB) ProtoMessage()    {}
func (*SKBuiltinFloat32_PB) Descriptor() ([]byte, []int) {
	return fileDescriptorSkbuiltintype, []int{10}
}

func (m *SKBuiltinFloat32_PB) GetFVal() float32 {
	if m != nil {
		return m.FVal
	}
	return 0
}

type SKBuiltinDouble64_PB struct {
	DVal float64 `protobuf:"fixed64,1,req,name=dVal" json:"dVal"`
}

func (m *SKBuiltinDouble64_PB) Reset()         { *m = SKBuiltinDouble64_PB{} }
func (m *SKBuiltinDouble64_PB) String() string { return proto.CompactTextString(m) }
func (*SKBuiltinDouble64_PB) ProtoMessage()    {}
func (*SKBuiltinDouble64_PB) Descriptor() ([]byte, []int) {
	return fileDescriptorSkbuiltintype, []int{11}
}

func (m *SKBuiltinDouble64_PB) GetDVal() float64 {
	if m != nil {
		return m.DVal
	}
	return 0
}

type SKBuiltinBuffer_PB struct {
	ILen   uint32 `protobuf:"varint,1,req,name=iLen" json:"iLen"`
	Buffer []byte `protobuf:"bytes,2,opt,name=Buffer" json:"Buffer"`
}

func (m *SKBuiltinBuffer_PB) Reset()                    { *m = SKBuiltinBuffer_PB{} }
func (m *SKBuiltinBuffer_PB) String() string            { return proto.CompactTextString(m) }
func (*SKBuiltinBuffer_PB) ProtoMessage()               {}
func (*SKBuiltinBuffer_PB) Descriptor() ([]byte, []int) { return fileDescriptorSkbuiltintype, []int{12} }

func (m *SKBuiltinBuffer_PB) GetILen() uint32 {
	if m != nil {
		return m.ILen
	}
	return 0
}

func (m *SKBuiltinBuffer_PB) GetBuffer() []byte {
	if m != nil {
		return m.Buffer
	}
	return nil
}

type SKBuiltinString_PB struct {
	String_ string `protobuf:"bytes,1,opt,name=String" json:"String"`
}

func (m *SKBuiltinString_PB) Reset()                    { *m = SKBuiltinString_PB{} }
func (m *SKBuiltinString_PB) String() string            { return proto.CompactTextString(m) }
func (*SKBuiltinString_PB) ProtoMessage()               {}
func (*SKBuiltinString_PB) Descriptor() ([]byte, []int) { return fileDescriptorSkbuiltintype, []int{13} }

func (m *SKBuiltinString_PB) GetString_() string {
	if m != nil {
		return m.String_
	}
	return ""
}

type SKBuiltinEmpty_PB struct {
}

func (m *SKBuiltinEmpty_PB) Reset()                    { *m = SKBuiltinEmpty_PB{} }
func (m *SKBuiltinEmpty_PB) String() string            { return proto.CompactTextString(m) }
func (*SKBuiltinEmpty_PB) ProtoMessage()               {}
func (*SKBuiltinEmpty_PB) Descriptor() ([]byte, []int) { return fileDescriptorSkbuiltintype, []int{14} }

type SKBuiltinEchoInfo_PB struct {
	EchoLen int32  `protobuf:"varint,1,req,name=EchoLen" json:"EchoLen"`
	EchoStr []byte `protobuf:"bytes,2,req,name=EchoStr" json:"EchoStr"`
}

func (m *SKBuiltinEchoInfo_PB) Reset()         { *m = SKBuiltinEchoInfo_PB{} }
func (m *SKBuiltinEchoInfo_PB) String() string { return proto.CompactTextString(m) }
func (*SKBuiltinEchoInfo_PB) ProtoMessage()    {}
func (*SKBuiltinEchoInfo_PB) Descriptor() ([]byte, []int) {
	return fileDescriptorSkbuiltintype, []int{15}
}

func (m *SKBuiltinEchoInfo_PB) GetEchoLen() int32 {
	if m != nil {
		return m.EchoLen
	}
	return 0
}

func (m *SKBuiltinEchoInfo_PB) GetEchoStr() []byte {
	if m != nil {
		return m.EchoStr
	}
	return nil
}

var E_ServerType = &proto.ExtensionDesc{
	ExtendedType:  (*google_protobuf.ServiceOptions)(nil),
	ExtensionType: (*string)(nil),
	Field:         1000000,
	Name:          "tlvpickle.ServerType",
	Tag:           "bytes,1000000,opt,name=ServerType",
	Filename:      "skbuiltintype.proto",
}

var E_Magic = &proto.ExtensionDesc{
	ExtendedType:  (*google_protobuf.ServiceOptions)(nil),
	ExtensionType: (*int32)(nil),
	Field:         1000001,
	Name:          "tlvpickle.Magic",
	Tag:           "varint,1000001,opt,name=Magic",
	Filename:      "skbuiltintype.proto",
}

var E_CmdID = &proto.ExtensionDesc{
	ExtendedType:  (*google_protobuf.MethodOptions)(nil),
	ExtensionType: (*int32)(nil),
	Field:         1000000,
	Name:          "tlvpickle.CmdID",
	Tag:           "varint,1000000,opt,name=CmdID",
	Filename:      "skbuiltintype.proto",
}

var E_OptString = &proto.ExtensionDesc{
	ExtendedType:  (*google_protobuf.MethodOptions)(nil),
	ExtensionType: (*string)(nil),
	Field:         1000001,
	Name:          "tlvpickle.OptString",
	Tag:           "bytes,1000001,opt,name=OptString",
	Filename:      "skbuiltintype.proto",
}

var E_Usage = &proto.ExtensionDesc{
	ExtendedType:  (*google_protobuf.MethodOptions)(nil),
	ExtensionType: (*string)(nil),
	Field:         1000002,
	Name:          "tlvpickle.Usage",
	Tag:           "bytes,1000002,opt,name=Usage",
	Filename:      "skbuiltintype.proto",
}

func init() {
	proto.RegisterType((*SKBuiltinInt32_PB)(nil), "tlvpickle.SKBuiltinInt32_PB")
	proto.RegisterType((*SKBuiltinUint32_PB)(nil), "tlvpickle.SKBuiltinUint32_PB")
	proto.RegisterType((*SKBuiltinChar_PB)(nil), "tlvpickle.SKBuiltinChar_PB")
	proto.RegisterType((*SKBuiltinUchar_PB)(nil), "tlvpickle.SKBuiltinUchar_PB")
	proto.RegisterType((*SKBuiltinInt8_PB)(nil), "tlvpickle.SKBuiltinInt8_PB")
	proto.RegisterType((*SKBuiltinUint8_PB)(nil), "tlvpickle.SKBuiltinUint8_PB")
	proto.RegisterType((*SKBuiltinInt16_PB)(nil), "tlvpickle.SKBuiltinInt16_PB")
	proto.RegisterType((*SKBuiltinUint16_PB)(nil), "tlvpickle.SKBuiltinUint16_PB")
	proto.RegisterType((*SKBuiltinInt64_PB)(nil), "tlvpickle.SKBuiltinInt64_PB")
	proto.RegisterType((*SKBuiltinUint64_PB)(nil), "tlvpickle.SKBuiltinUint64_PB")
	proto.RegisterType((*SKBuiltinFloat32_PB)(nil), "tlvpickle.SKBuiltinFloat32_PB")
	proto.RegisterType((*SKBuiltinDouble64_PB)(nil), "tlvpickle.SKBuiltinDouble64_PB")
	proto.RegisterType((*SKBuiltinBuffer_PB)(nil), "tlvpickle.SKBuiltinBuffer_PB")
	proto.RegisterType((*SKBuiltinString_PB)(nil), "tlvpickle.SKBuiltinString_PB")
	proto.RegisterType((*SKBuiltinEmpty_PB)(nil), "tlvpickle.SKBuiltinEmpty_PB")
	proto.RegisterType((*SKBuiltinEchoInfo_PB)(nil), "tlvpickle.SKBuiltinEchoInfo_PB")
	proto.RegisterExtension(E_ServerType)
	proto.RegisterExtension(E_Magic)
	proto.RegisterExtension(E_CmdID)
	proto.RegisterExtension(E_OptString)
	proto.RegisterExtension(E_Usage)
}
func (m *SKBuiltinInt32_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinInt32_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSkbuiltintype(dAtA, i, uint64(m.IVal))
	return i, nil
}

func (m *SKBuiltinUint32_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinUint32_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSkbuiltintype(dAtA, i, uint64(m.UiVal))
	return i, nil
}

func (m *SKBuiltinChar_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinChar_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSkbuiltintype(dAtA, i, uint64(m.IVal))
	return i, nil
}

func (m *SKBuiltinUchar_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinUchar_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSkbuiltintype(dAtA, i, uint64(m.UiVal))
	return i, nil
}

func (m *SKBuiltinInt8_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinInt8_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSkbuiltintype(dAtA, i, uint64(m.IVal))
	return i, nil
}

func (m *SKBuiltinUint8_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinUint8_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSkbuiltintype(dAtA, i, uint64(m.UiVal))
	return i, nil
}

func (m *SKBuiltinInt16_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinInt16_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSkbuiltintype(dAtA, i, uint64(m.IVal))
	return i, nil
}

func (m *SKBuiltinUint16_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinUint16_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSkbuiltintype(dAtA, i, uint64(m.UiVal))
	return i, nil
}

func (m *SKBuiltinInt64_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinInt64_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSkbuiltintype(dAtA, i, uint64(m.LlVal))
	return i, nil
}

func (m *SKBuiltinUint64_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinUint64_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSkbuiltintype(dAtA, i, uint64(m.UllVal))
	return i, nil
}

func (m *SKBuiltinFloat32_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinFloat32_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xd
	i++
	i = encodeFixed32Skbuiltintype(dAtA, i, uint32(math1.Float32bits(float32(m.FVal))))
	return i, nil
}

func (m *SKBuiltinDouble64_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinDouble64_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x9
	i++
	i = encodeFixed64Skbuiltintype(dAtA, i, uint64(math1.Float64bits(float64(m.DVal))))
	return i, nil
}

func (m *SKBuiltinBuffer_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinBuffer_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSkbuiltintype(dAtA, i, uint64(m.ILen))
	if m.Buffer != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSkbuiltintype(dAtA, i, uint64(len(m.Buffer)))
		i += copy(dAtA[i:], m.Buffer)
	}
	return i, nil
}

func (m *SKBuiltinString_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinString_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSkbuiltintype(dAtA, i, uint64(len(m.String_)))
	i += copy(dAtA[i:], m.String_)
	return i, nil
}

func (m *SKBuiltinEmpty_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinEmpty_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SKBuiltinEchoInfo_PB) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SKBuiltinEchoInfo_PB) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSkbuiltintype(dAtA, i, uint64(m.EchoLen))
	if m.EchoStr != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSkbuiltintype(dAtA, i, uint64(len(m.EchoStr)))
		i += copy(dAtA[i:], m.EchoStr)
	}
	return i, nil
}

func encodeFixed64Skbuiltintype(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Skbuiltintype(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintSkbuiltintype(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *SKBuiltinInt32_PB) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSkbuiltintype(uint64(m.IVal))
	return n
}

func (m *SKBuiltinUint32_PB) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSkbuiltintype(uint64(m.UiVal))
	return n
}

func (m *SKBuiltinChar_PB) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSkbuiltintype(uint64(m.IVal))
	return n
}

func (m *SKBuiltinUchar_PB) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSkbuiltintype(uint64(m.UiVal))
	return n
}

func (m *SKBuiltinInt8_PB) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSkbuiltintype(uint64(m.IVal))
	return n
}

func (m *SKBuiltinUint8_PB) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSkbuiltintype(uint64(m.UiVal))
	return n
}

func (m *SKBuiltinInt16_PB) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSkbuiltintype(uint64(m.IVal))
	return n
}

func (m *SKBuiltinUint16_PB) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSkbuiltintype(uint64(m.UiVal))
	return n
}

func (m *SKBuiltinInt64_PB) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSkbuiltintype(uint64(m.LlVal))
	return n
}

func (m *SKBuiltinUint64_PB) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSkbuiltintype(uint64(m.UllVal))
	return n
}

func (m *SKBuiltinFloat32_PB) Size() (n int) {
	var l int
	_ = l
	n += 5
	return n
}

func (m *SKBuiltinDouble64_PB) Size() (n int) {
	var l int
	_ = l
	n += 9
	return n
}

func (m *SKBuiltinBuffer_PB) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSkbuiltintype(uint64(m.ILen))
	if m.Buffer != nil {
		l = len(m.Buffer)
		n += 1 + l + sovSkbuiltintype(uint64(l))
	}
	return n
}

func (m *SKBuiltinString_PB) Size() (n int) {
	var l int
	_ = l
	l = len(m.String_)
	n += 1 + l + sovSkbuiltintype(uint64(l))
	return n
}

func (m *SKBuiltinEmpty_PB) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SKBuiltinEchoInfo_PB) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSkbuiltintype(uint64(m.EchoLen))
	if m.EchoStr != nil {
		l = len(m.EchoStr)
		n += 1 + l + sovSkbuiltintype(uint64(l))
	}
	return n
}

func sovSkbuiltintype(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozSkbuiltintype(x uint64) (n int) {
	return sovSkbuiltintype(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *SKBuiltinInt32_PB) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinInt32_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinInt32_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IVal", wireType)
			}
			m.IVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IVal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("iVal")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinUint32_PB) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinUint32_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinUint32_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field UiVal", wireType)
			}
			m.UiVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UiVal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uiVal")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinChar_PB) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinChar_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinChar_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IVal", wireType)
			}
			m.IVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IVal |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("iVal")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinUchar_PB) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinUchar_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinUchar_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field UiVal", wireType)
			}
			m.UiVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UiVal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uiVal")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinInt8_PB) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinInt8_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinInt8_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IVal", wireType)
			}
			m.IVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IVal |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("iVal")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinUint8_PB) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinUint8_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinUint8_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field UiVal", wireType)
			}
			m.UiVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UiVal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uiVal")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinInt16_PB) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinInt16_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinInt16_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IVal", wireType)
			}
			m.IVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IVal |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("iVal")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinUint16_PB) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinUint16_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinUint16_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field UiVal", wireType)
			}
			m.UiVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UiVal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uiVal")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinInt64_PB) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinInt64_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinInt64_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field LlVal", wireType)
			}
			m.LlVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LlVal |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("llVal")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinUint64_PB) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinUint64_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinUint64_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field UllVal", wireType)
			}
			m.UllVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UllVal |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("ullVal")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinFloat32_PB) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinFloat32_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinFloat32_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FVal", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += 4
			v = uint32(dAtA[iNdEx-4])
			v |= uint32(dAtA[iNdEx-3]) << 8
			v |= uint32(dAtA[iNdEx-2]) << 16
			v |= uint32(dAtA[iNdEx-1]) << 24
			m.FVal = float32(math2.Float32frombits(v))
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("fVal")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinDouble64_PB) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinDouble64_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinDouble64_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DVal", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.DVal = float64(math2.Float64frombits(v))
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("dVal")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinBuffer_PB) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinBuffer_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinBuffer_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ILen", wireType)
			}
			m.ILen = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ILen |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Buffer", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Buffer = append(m.Buffer[:0], dAtA[iNdEx:postIndex]...)
			if m.Buffer == nil {
				m.Buffer = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("iLen")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinString_PB) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinString_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinString_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field String_", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.String_ = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinEmpty_PB) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinEmpty_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinEmpty_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SKBuiltinEchoInfo_PB) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SKBuiltinEchoInfo_PB: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SKBuiltinEchoInfo_PB: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field EchoLen", wireType)
			}
			m.EchoLen = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EchoLen |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field EchoStr", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EchoStr = append(m.EchoStr[:0], dAtA[iNdEx:postIndex]...)
			if m.EchoStr == nil {
				m.EchoStr = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipSkbuiltintype(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSkbuiltintype
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("EchoLen")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("EchoStr")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipSkbuiltintype(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowSkbuiltintype
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSkbuiltintype
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthSkbuiltintype
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowSkbuiltintype
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipSkbuiltintype(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt1.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthSkbuiltintype = fmt1.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowSkbuiltintype   = fmt1.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("skbuiltintype.proto", fileDescriptorSkbuiltintype) }

var fileDescriptorSkbuiltintype = []byte{
	// 554 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x94, 0x41, 0x6f, 0xd3, 0x3e,
	0x18, 0xc6, 0x97, 0xfc, 0x9b, 0x3f, 0xaa, 0x05, 0x12, 0xa4, 0x20, 0x45, 0x13, 0xca, 0xaa, 0x9c,
	0x7a, 0x60, 0x09, 0x14, 0x28, 0xa8, 0x68, 0x48, 0x64, 0x1b, 0xa2, 0xa2, 0xd3, 0xa6, 0x96, 0xee,
	0xc0, 0x65, 0x4a, 0x5d, 0x27, 0xb5, 0xea, 0xd8, 0x51, 0xe2, 0x54, 0xea, 0x9d, 0x0f, 0xc0, 0x47,
	0x62, 0x9c, 0x76, 0xe4, 0x13, 0x20, 0x54, 0xbe, 0x08, 0x4a, 0x1d, 0xa7, 0x69, 0x26, 0xb5, 0x70,
	0x6b, 0x1e, 0x3f, 0xbf, 0xf7, 0x7d, 0x5e, 0xbb, 0x36, 0x68, 0x24, 0xb3, 0x71, 0x8a, 0x09, 0xc7,
	0x94, 0x2f, 0x22, 0x64, 0x47, 0x31, 0xe3, 0x4c, 0xaf, 0x73, 0x32, 0x8f, 0x30, 0x9c, 0x11, 0xb4,
	0xdf, 0x0c, 0x18, 0x0b, 0x08, 0x72, 0x56, 0x0b, 0xe3, 0xd4, 0x77, 0x26, 0x28, 0x81, 0x31, 0x8e,
	0x38, 0x8b, 0x85, 0xd9, 0x3a, 0x04, 0x0f, 0x86, 0x1f, 0x5d, 0x51, 0xa3, 0x47, 0xf9, 0xf3, 0xf6,
	0xd5, 0x85, 0xab, 0x1b, 0xa0, 0x86, 0x2f, 0x3d, 0x62, 0x28, 0x4d, 0xb5, 0x75, 0xcf, 0xad, 0xdd,
	0xfc, 0x3c, 0xd8, 0x1b, 0xac, 0x14, 0xeb, 0x29, 0xd0, 0x0b, 0xfb, 0x08, 0x4b, 0xff, 0x3e, 0xd0,
	0xd2, 0x5b, 0x80, 0x90, 0xac, 0x27, 0xe0, 0x7e, 0x41, 0x1c, 0x4f, 0xbd, 0xb8, 0x5a, 0x5f, 0xdb,
	0xa8, 0xef, 0x94, 0xe2, 0x8c, 0x60, 0x6e, 0xff, 0xdb, 0xf2, 0x3d, 0xca, 0x5f, 0xff, 0x43, 0x79,
	0x9c, 0xdb, 0xb7, 0x95, 0xaf, 0x6c, 0xcf, 0xb3, 0xce, 0xf6, 0xfa, 0xd5, 0xed, 0x11, 0xfe, 0x6d,
	0x0d, 0x9c, 0xcd, 0x06, 0x9d, 0x17, 0x39, 0x40, 0x88, 0x04, 0xfe, 0x93, 0xc0, 0x4a, 0xb2, 0xda,
	0x95, 0x16, 0x82, 0x78, 0x0c, 0xfe, 0x4f, 0xd7, 0x48, 0x2d, 0x47, 0x72, 0xcd, 0x72, 0x40, 0xa3,
	0x60, 0xde, 0x13, 0xe6, 0xad, 0x8f, 0xd9, 0x97, 0x88, 0x2a, 0xe7, 0xf0, 0xc5, 0x1c, 0x0f, 0x0b,
	0xe0, 0x84, 0xa5, 0x63, 0x82, 0x44, 0x1b, 0x03, 0xd4, 0x26, 0x92, 0x50, 0x24, 0x91, 0x29, 0x56,
	0xbf, 0x14, 0xcb, 0x4d, 0x7d, 0x1f, 0x15, 0x07, 0xdd, 0x47, 0xb4, 0xf2, 0x47, 0xea, 0x23, 0x9a,
	0x05, 0x16, 0x36, 0x43, 0x6d, 0x2a, 0xad, 0xbb, 0x32, 0xb0, 0xd0, 0x36, 0x86, 0x1c, 0xf2, 0x18,
	0xd3, 0x20, 0x1f, 0x52, 0x7c, 0x18, 0x4a, 0x53, 0x69, 0xd5, 0x25, 0x23, 0x34, 0xab, 0x51, 0xda,
	0xc9, 0xd3, 0x30, 0xe2, 0x8b, 0xab, 0x0b, 0xd7, 0xba, 0x2c, 0x0d, 0x72, 0x0a, 0xa7, 0xac, 0x47,
	0x7d, 0x96, 0x95, 0x32, 0xc1, 0x9d, 0xec, 0x53, 0x66, 0x93, 0xa7, 0x28, 0x45, 0xb9, 0x3e, 0xe4,
	0x59, 0x3e, 0xb5, 0xc8, 0x27, 0xc5, 0xee, 0x3b, 0x00, 0x86, 0x28, 0x9e, 0xa3, 0xf8, 0xd3, 0x22,
	0x42, 0xfa, 0x81, 0x2d, 0xee, 0x99, 0x2d, 0xef, 0x99, 0x9d, 0x2d, 0x62, 0x88, 0xce, 0x23, 0x8e,
	0x19, 0x4d, 0x8c, 0x6f, 0x5f, 0x8e, 0xb2, 0xc4, 0x83, 0x12, 0xd4, 0x7d, 0x05, 0xb4, 0x33, 0x2f,
	0xc0, 0x70, 0x37, 0x7d, 0xbd, 0xa2, 0xb5, 0x81, 0xf0, 0x77, 0x3b, 0x40, 0x3b, 0x0e, 0x27, 0xbd,
	0x13, 0xdd, 0xbc, 0x05, 0x9e, 0x21, 0x3e, 0x65, 0x93, 0xcd, 0xae, 0xda, 0x40, 0xd8, 0xbb, 0x6f,
	0x41, 0xfd, 0x3c, 0xe2, 0x62, 0xb7, 0x76, 0xb2, 0xd7, 0x79, 0xe2, 0x35, 0x92, 0xf5, 0x1d, 0x25,
	0x5e, 0x80, 0x76, 0xb2, 0xdf, 0x73, 0x56, 0xd8, 0xdd, 0xe4, 0x66, 0x69, 0x2a, 0x3f, 0x96, 0xa6,
	0xf2, 0x6b, 0x69, 0x2a, 0x5f, 0x7f, 0x9b, 0x7b, 0xe0, 0x11, 0x64, 0xa1, 0xcd, 0x11, 0x85, 0x88,
	0x72, 0xbb, 0x78, 0xad, 0xdc, 0xf5, 0xc3, 0xf5, 0x41, 0xfd, 0x7c, 0x14, 0x30, 0xe2, 0xd1, 0xc0,
	0x7e, 0xd9, 0xe6, 0xdc, 0x86, 0x2c, 0x74, 0x92, 0x79, 0x3c, 0xc3, 0xfc, 0x30, 0x60, 0xe2, 0x35,
	0x83, 0x8c, 0x38, 0x90, 0x85, 0x21, 0xa3, 0x4e, 0x81, 0xbd, 0x29, 0x7e, 0xfd, 0x09, 0x00, 0x00,
	0xff, 0xff, 0x9f, 0x2c, 0xc6, 0x41, 0x1a, 0x05, 0x00, 0x00,
}
