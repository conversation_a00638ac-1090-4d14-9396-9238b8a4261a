package channel_red_packet

import (
	"fmt"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	pb "golang.52tt.com/protocol/services/channel-red-packet"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
	"time"
)

var stdClient *Client

func init() {
	stdClient, _ = NewClient(grpc.WithBlock())
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestBooks(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Rp Suite")
}

var _ = Describe("AddRedPacketConf", func() {
	err := stdClient.AddRedPacketConf(context.Background(), &pb.AddRedPacketConfReq{
		BgIdList:   []uint32{846},
		BeginTime:  uint32(time.Now().Unix()),
		EndTime:    uint32(time.Now().Add(time.Hour).Unix()),
		TotalPrice: 1000,
	})

	Context("增加红包配置", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetRedPacketConf", func() {
	resp, err := stdClient.GetRedPacketConf(context.Background(), 0)

	Context("获取红包配置列表", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
		It("红包配置列表不存在，异常！", func() {
			Expect(len(resp.GetList())).NotTo(Equal(0))
		})
	})
})

var _ = Describe("GetRedPacketConfById", func() {
	testRpId := uint32(0)
	resp, err := stdClient.GetRedPacketConf(context.Background(), 0)
	if len(resp.GetList()) > 0 {
		testRpId = resp.GetList()[0].GetRedPacketId()
	}

	resp2, err := stdClient.GetRedPacketConfById(context.Background(), 0, testRpId)

	Context("获取红包配置", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
		It("红包配置不存在，异常！", func() {
			Expect(resp2.GetConf().GetRedPacketId()).To(Equal(testRpId))
		})
	})
})

var _ = Describe("CheckIfCanSendRedPacket", func() {
	ok, err := stdClient.CheckIfCanSendRedPacket(context.Background(), 2207965, 1)

	Context("检查能否发红包", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
		It("不能发红包，异常！", func() {
			Expect(ok).To(Equal(true))
		})
	})
})

var _ = Describe("SendRedPacket", func() {
	testRpId := uint32(0)
	resp, err := stdClient.GetRedPacketConf(context.Background(), 0)
	if len(resp.GetList()) > 0 {
		testRpId = resp.GetList()[0].GetRedPacketId()
	}

	err = stdClient.SendRedPacket(context.Background(), 2207965, &pb.SendRedPacketReq{
		OpUid:       2207965,
		ChannelId:   1,
		RedPacketId: testRpId,
		PublicMsg:   "hhh",
	})

	Context("发红包", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetRedPacketList", func() {
	_, err := stdClient.GetRedPacketList(context.Background(), 0, 1)

	Context("获取房间红包列表", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("ReportRedPacketClickCnt", func() {
	err := stdClient.ReportRedPacketClickCnt(context.Background(), 2207965, &pb.ReportRedPacketClickCntReq{
		Uid: 2207965, OrderId: "hhh", ChannelId: 1, Cnt: 1,
	})

	Context("上报红包点击次数", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("DelRedPacketConf", func() {
	err := stdClient.DelRedPacketConf(context.Background(), 0)

	Context("删除红包配置", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetRedPacketOrderTotal", func() {
	_, err := stdClient.GetRedPacketOrderTotal(context.Background(), uint32(time.Now().Unix()), uint32(time.Now().Unix()))

	Context("获取订单汇总信息", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetRedPacketAwardTotal", func() {
	_, err := stdClient.GetRedPacketAwardTotal(context.Background(), uint32(time.Now().Unix()), uint32(time.Now().Unix()))

	Context("获取奖励汇总信息", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})
