package channel_red_packet

import (
	"context"
	"strconv"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/channel-red-packet"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

const (
	serviceName = "channel-red-packet"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelRedPacketClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelRedPacketClient {
	return c.Stub().(pb.ChannelRedPacketClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) AddRedPacketConf(ctx context.Context, req *pb.AddRedPacketConfReq) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	_, err := c.typedStub().AddRedPacketConf(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) DelRedPacketConf(ctx context.Context, id uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	_, err := c.typedStub().DelRedPacketConf(ctx, &pb.DelRedPacketConfReq{RedPacketId: id})
	return protocol.ToServerError(err)
}

func (c *Client) GetRedPacketConf(ctx context.Context, opUid uint32) (*pb.GetRedPacketConfResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetRedPacketConf(ctx, &pb.GetRedPacketConfReq{OpUid: opUid})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRedPacketConfById(ctx context.Context, opUid, id uint32) (*pb.GetRedPacketConfByIdResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetRedPacketConfById(ctx, &pb.GetRedPacketConfByIdReq{OpUid: opUid, RedPacketId: id})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRedPacketList(ctx context.Context, opUid, channelId uint32) (*pb.GetRedPacketListResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetRedPacketList(ctx, &pb.GetRedPacketListReq{OpUid: opUid, ChannelId: channelId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) CheckIfCanSendRedPacket(ctx context.Context, opUid, cid uint32) (bool, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().CheckIfCanSendRedPacket(ctx, &pb.CheckIfCanSendRedPacketReq{OpUid: opUid, ChannelId: cid})
	return resp.GetOk(), protocol.ToServerError(err)
}

func (c *Client) SendRedPacket(ctx context.Context, opUid uint32, req *pb.SendRedPacketReq) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	_, err := c.typedStub().SendRedPacket(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) ReportRedPacketClickCnt(ctx context.Context, opUid uint32, req *pb.ReportRedPacketClickCntReq) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	_, err := c.typedStub().ReportRedPacketClickCnt(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) GetRedPacketOrderTotal(ctx context.Context, beginTime, endTime uint32) (*pb.GetRedPacketOrderTotalResp, protocol.ServerError) {
	req := &pb.GetRedPacketOrderTotalReq{
		BeginTime: beginTime,
		EndTime:   endTime,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().GetRedPacketOrderTotal(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRedPacketAwardTotal(ctx context.Context, beginTime, endTime uint32) (*pb.GetRedPacketAwardTotalResp, protocol.ServerError) {
	req := &pb.GetRedPacketAwardTotalReq{
		BeginTime: beginTime,
		EndTime:   endTime,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().GetRedPacketAwardTotal(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatGetRedPacketChannel(ctx context.Context, channelIds []uint32) ([]uint32, protocol.ServerError) {
	resp, err := c.typedStub().BatGetRedPacketChannel(ctx, &pb.BatGetRedPacketChannelReq{ChannelIds: channelIds})
	return resp.GetChannelIds(), protocol.ToServerError(err)
}
