package comm_push_user_cache

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/comm-push-user-cache"
	"google.golang.org/grpc"
)

const (
	serviceName = "comm-push-user-cache"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewCommPushUserCacheClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.CommPushUserCacheClient { return c.Stub().(pb.CommPushUserCacheClient) }

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func (c *Client) GetUserByUids(ctx context.Context, in *pb.GetUserInfoByUidsReq) (out *pb.GetUserInfoByUidsResp, err error) {
	out, err = c.typedStub().GetUserByUids(ctx, in)
	return out, err
}

func (c *Client) GetUserOnlineStateByUids(ctx context.Context, in *pb.GetUserOnlineStateByUidsReq) (out *pb.GetUserOnlineStateByUidsResp, err error) {
	out, err = c.typedStub().GetUserOnlineStateByUids(ctx, in)
	return out, err
}

func (c *Client) GetUserTagsByUids(ctx context.Context, in *pb.GetUserTagsByUidsReq) (out *pb.GetUserTagsByUidsResp, err error) {
	out, err = c.typedStub().GetUserTagsByUids(ctx, in)
	return out, err
}

func (c *Client) SetBatchUserInfoPre(ctx context.Context, in *pb.SetBatchUserInfoPreReq) (out *pb.SetBatchUserInfoPreResp, err error) {
	out, err = c.typedStub().SetBatchUserInfoPre(ctx, in)
	return out, err
}

func (c *Client) BatchGetUserIsOnlineMap(ctx context.Context, uids []uint32) (out map[uint32]struct{}, err error) {

	out = make(map[uint32]struct{},len(uids))
	resp, err := c.typedStub().GetUserOnlineStateByUids(ctx, &pb.GetUserOnlineStateByUidsReq{Uids: uids})

	if err != nil {
		return out,err
	}

	for uid,onlineUpdate := range resp.OnlineUpdateMap {
		if !onlineUpdate.IsOnline {
			continue
		}

		out[uid] = struct{}{}
	}

	return out, err
}

func (c *Client) GetUser(ctx context.Context,uid uint32) (out *pb.UserResp,err error) {
	out = &pb.UserResp{}
	resp ,err := c.typedStub().GetUserByUids(ctx,&pb.GetUserInfoByUidsReq{Uids: []uint32{uid}})
	if err != nil {
		return out,err
	}

	if _,ok := resp.UserInfoMap[uid]; !ok {
		return out,nil
	}

	return resp.UserInfoMap[uid],nil
}

func (c *Client) BatchGetUser (ctx context.Context,uidList []uint32) (out map[uint32]*pb.UserResp,err error) {
	resp ,err := c.typedStub().GetUserByUids(ctx,&pb.GetUserInfoByUidsReq{Uids: uidList})
	if err != nil {
		return out,err
	}

	return resp.UserInfoMap,nil
}

