package comm_push_user_cache

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/comm-push-user-cache"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetUserByUids(ctx context.Context, in *pb.GetUserInfoByUidsReq) (out *pb.GetUserInfoByUidsResp, err error)
	GetUserOnlineStateByUids(ctx context.Context, in *pb.GetUserOnlineStateByUidsReq) (out *pb.GetUserOnlineStateByUidsResp, err error)
	GetUserTagsByUids(ctx context.Context, in *pb.GetUserTagsByUidsReq) (out *pb.GetUserTagsByUidsResp, err error)
	SetBatchUserInfoPre(ctx context.Context, in *pb.SetBatchUserInfoPreReq) (out *pb.SetBatchUserInfoPreResp, err error)
	BatchGetUserIsOnlineMap(ctx context.Context, uids []uint32) (out map[uint32]struct{}, err error)
	GetUser(ctx context.Context, uid uint32) (out *pb.UserResp, err error)
	BatchGetUser(ctx context.Context, uidList []uint32) (out map[uint32]*pb.UserResp, err error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
