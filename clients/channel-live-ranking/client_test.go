package channelliveranking

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
	"time"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.<PERSON>ut, os.Stdout, os.Stdout))
}

func TestClient_GiveAnchorHonorNameplate(t *testing.T) {
	cli , _ := newClient(grpc.WithBlock())

	_, err := cli.GiveAnchorHonorNameplate(context.Background(), 0, 2204941, 1, uint32(time.Now().Unix()+86400))
	t.Log(err)

	_, err = cli.GiveAnchorHonorNameplate(context.Background(), 0, 2210582, 2, uint32(time.Now().Unix()+86400))
	t.Log(err)

	_, err = cli.GiveAnchorHonorNameplate(context.Background(), 0, 2212627, 3, uint32(time.Now().Unix()+86400))
	t.Log(err)

	_, err = cli.GiveAnchorHonorNameplate(context.Background(), 0, 2214408, 4, uint32(time.Now().Unix()+86400))
	t.Log(err)
}
