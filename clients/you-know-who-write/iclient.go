// Code generated by quicksilver-cli. DO NOT EDIT.
package youknowwho

import (
	context "context"

	"golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/youknowwho/youknowwhowrite"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	UserChangeUKWStatus(ctx context.Context, uid uint32, switchTy pb.SwitchType) (*pb.ChangeStatusResp, protocol.ServerError)
	UserChangeRankSwitch(ctx context.Context, uid uint32, switchTy pb.RankType) (*pb.ChangeRankSwitchResp, protocol.ServerError)
	SendShowUpMsg(ctx context.Context, req *pb.SendShowMsgReq) (*pb.SendShowMsgResp, protocol.ServerError)
	ExposureUKW(ctx context.Context, uid uint32) protocol.ServerError
	UserChangeUKWEnterNotice(ctx context.Context, uid uint32, switchTy pb.SwitchType) (*pb.ChangeEnterNoticeResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
