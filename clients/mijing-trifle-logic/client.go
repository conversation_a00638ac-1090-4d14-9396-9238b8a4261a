package mijing_trifle

import (
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	"golang.52tt.com/protocol/app/api/mijing_trifle"
	"google.golang.org/grpc"
)

var (
	serviceName = "mijing-trifle-logic"
)

type IClient interface {
	client.BaseClient
	mijing_trifle.MijingTrifleLogicClient
}

type Client struct {
	client.BaseClient
	mijing_trifle.MijingTrifleLogicClient
}

func newClient(dopts ...grpc.DialOption) (IClient, error) {
	c := &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return mijing_trifle.NewMijingTrifleLogicClient(cc)
			},
			dopts...,
		),
	}
	c.MijingTrifleLogicClient = c.typedStub()
	return c, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (IClient, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (IClient, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() mijing_trifle.MijingTrifleLogicClient {
	return c.Stub().(mijing_trifle.MijingTrifleLogicClient)
}
