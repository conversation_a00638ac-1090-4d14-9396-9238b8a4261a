package knock

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/knock"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	Knock(ctx context.Context, req pb.KnockReq) (*pb.KnockResp, protocol.ServerError)
	HandleKnock(ctx context.Context, req pb.HandleKnockReq) (*pb.HandleKnockRsp, protocol.ServerError)
	GetKnockInfo(ctx context.Context, req pb.KnockInfoReq) (*pb.KnockInfoRsp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
