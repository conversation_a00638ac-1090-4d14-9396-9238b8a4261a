package channellive

import (
	"context"
	"strconv"

	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/channellivesvr"
)

const (
	serviceName = "channellive"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewChannelLiveClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.ChannelLiveClient { return c.Stub().(pb.ChannelLiveClient) }

func (c *Client) QueueUpMicHandle(ctx context.Context, uin, channelId, uid uint32, allow bool) (*pb.QueueUpMicHandleResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().QueueUpMicHandle(ctx, &pb.QueueUpMicHandleReq{
		ChannelId: channelId,
		TargetUid: uid,
		OpUid:     uin,
		IsAllow:   allow,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

func (c *Client) GetQueueUpMicApplyList(ctx context.Context, uin, channelId, offset, limit uint32) (*pb.GetQueueUpMicApplyListResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetQueueUpMicApplyList(ctx, &pb.GetQueueUpMicApplyListReq{
		ChannelId: channelId,
		Offset:    offset,
		LimitCnt:  limit,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

func (c *Client) QueueUpMicApply(ctx context.Context, userID uint32, cid uint32, IsCancel bool) (resp *pb.QueueUpMicApplyResp, err error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.FormatUint(uint64(userID), 10)))
	req := &pb.QueueUpMicApplyReq{
		ChannelId: cid,
		OpUid:     userID,
		IsCancel:  IsCancel,
	}

	resp, err = c.typedStub().QueueUpMicApply(ctx, req)
	if nil != err {
		return nil, err
	}
	return resp, nil
}

func (c *Client) CleanQueueUpMicApplyList(ctx context.Context, opUid, cid uint32) (*pb.CleanQueueUpMicApplyListResp, error) {
	req := &pb.CleanQueueUpMicApplyListReq{
		ChannelId: cid,
		OpUid:     opUid,
	}

	resp, err := c.typedStub().CleanQueueUpMicApplyList(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
