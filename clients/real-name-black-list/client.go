package real_name_black_list

import (
	"context"
	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/tracing"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/real-name-black-list"
	"google.golang.org/grpc"
)

const (
	serviceName = "real-name-black-list"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewRealNameBlackListServerClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.RealNameBlackListServerClient {
	return c.Stub().(pb.RealNameBlackListServerClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func (c *Client) AddBlackList(ctx context.Context, in *pb.AddBlackListReq) (*pb.AddBlackListResp, protocol.ServerError) {
	resp, err := c.typedStub().AddBlackList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DelBlackList(ctx context.Context, in *pb.DelBlackListReq) (*pb.DelBlackListResp, protocol.ServerError) {
	resp, err := c.typedStub().DelBlackList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetBlackList(ctx context.Context, in *pb.GetBlackListReq) (*pb.GetBlackListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetBlackList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) IsBlackList(ctx context.Context, in *pb.IsBlackListReq) (*pb.IsBlackListResp, protocol.ServerError) {
	resp, err := c.typedStub().IsBlackList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddSuspectedBlackList(ctx context.Context, in *pb.AddSuspectedBlackListReq) (*pb.AddSuspectedBlackListResp, protocol.ServerError) {
	resp, err := c.typedStub().AddSuspectedBlackList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSuspectedBlackList(ctx context.Context, in *pb.GetSuspectedBlackListReq) (*pb.GetSuspectedBlackListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSuspectedBlackList(ctx, in)
	return resp, protocol.ToServerError(err)
}
