package real_name_black_list

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/real-name-black-list"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AddBlackList(ctx context.Context, in *pb.AddBlackListReq) (*pb.AddBlackListResp, protocol.ServerError)
	DelBlackList(ctx context.Context, in *pb.DelBlackListReq) (*pb.DelBlackListResp, protocol.ServerError)
	GetBlackList(ctx context.Context, in *pb.GetBlackListReq) (*pb.GetBlackListResp, protocol.ServerError)
	IsBlackList(ctx context.Context, in *pb.IsBlackListReq) (*pb.IsBlackListResp, protocol.ServerError)
	AddSuspectedBlackList(ctx context.Context, in *pb.AddSuspectedBlackListReq) (*pb.AddSuspectedBlackListResp, protocol.ServerError)
	GetSuspectedBlackList(ctx context.Context, in *pb.GetSuspectedBlackListReq) (*pb.GetSuspectedBlackListResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
