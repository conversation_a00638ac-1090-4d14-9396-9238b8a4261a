package revenuenameplate

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/revenuenameplate"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	SetNameplate(ctx context.Context, nameplateInfo *pb.RevenueNameplateInfo) (uint32, protocol.ServerError)
	UpdateNameplate(ctx context.Context, nameplateInfo *pb.RevenueNameplateInfo) (uint32, protocol.ServerError)
	GetAllNameplates(ctx context.Context, req *pb.GetAllNameplatesReq) (*pb.GetAllNameplatesResp, protocol.ServerError)
	GetNamePlate(ctx context.Context, idList []uint32) ([]*pb.RevenueNameplateInfo, protocol.ServerError)
	AssignNamePlate(ctx context.Context, records []*pb.AssignRecord) protocol.ServerError
	GetAssignRecord(ctx context.Context, limit, page uint32) ([]*pb.AssignR<PERSON>ord, protocol.ServerError)
	GetUserNameplateInfo(ctx context.Context, uid uint32, scenes pb.ScenesType) ([]*pb.NameplateDetailInfo, protocol.ServerError)
	BatchGetUserNameplates(ctx context.Context, uidList []uint32, scenes pb.ScenesType) (map[uint32][]*pb.NameplateDetailInfo, protocol.ServerError)
	SetUserNameplateInfo(ctx context.Context, uid uint32, idList []uint32) protocol.ServerError
	GetUserAllNameplateList(ctx context.Context, uid uint32) (*pb.GetUserAllNameplateListResp, protocol.ServerError)
	ActivityAssignNameplate(ctx context.Context, req *pb.ActivityAssignNameplateReq) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
