package revenuenameplate

import (
	"context"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/tracing"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/revenuenameplate"
	"google.golang.org/grpc"
)

const (
	serviceName = "revenue-nameplate"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewRevenueNameplateClient(cc)
			}, dopts...,
		),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return NewClient(dopts...)
}

func (c *Client) typedStub() pb.RevenueNameplateClient { return c.Stub().(pb.RevenueNameplateClient) }

// SetNameplate 设置铭牌配置
func (c *Client) SetNameplate(ctx context.Context, nameplateInfo *pb.RevenueNameplateInfo) (uint32, protocol.ServerError) {
	req := &pb.SetNameplateReq{
		Nameplate: nameplateInfo,
	}
	resp, err := c.typedStub().SetNameplate(ctx, req)
	return resp.GetId(), protocol.ToServerError(err)
}

// UpdateNameplate 更新铭牌配置
func (c *Client) UpdateNameplate(ctx context.Context, nameplateInfo *pb.RevenueNameplateInfo) (uint32, protocol.ServerError) {
	req := &pb.UpdateNameplateReq{
		Nameplate: nameplateInfo,
	}
	resp, err := c.typedStub().UpdateNameplate(ctx, req)
	return resp.GetId(), protocol.ToServerError(err)
}

// GetAllNameplates 获取所有铭牌配置（包含模糊查询）
func (c *Client) GetAllNameplates(ctx context.Context, req *pb.GetAllNameplatesReq) (*pb.GetAllNameplatesResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAllNameplates(ctx, req)
	return resp, protocol.ToServerError(err)
}

// GetNamePlate 获取指定铭牌配置
func (c *Client) GetNamePlate(ctx context.Context, idList []uint32) ([]*pb.RevenueNameplateInfo, protocol.ServerError) {
	req := &pb.GetNamePlateReq{
		IdList: idList,
	}
	resp, err := c.typedStub().GetNamePlate(ctx, req)
	return resp.GetNameplates(), protocol.ToServerError(err)
}

// AssignNamePlate 发放铭牌配置
func (c *Client) AssignNamePlate(ctx context.Context, records []*pb.AssignRecord) protocol.ServerError {
	req := &pb.AssignNamePlateReq{
		Record: records,
	}
	_, err := c.typedStub().AssignNamePlate(ctx, req)
	return protocol.ToServerError(err)
}

// GetAssignRecord 获取发放详情
func (c *Client) GetAssignRecord(ctx context.Context, limit, page uint32) ([]*pb.AssignRecord, protocol.ServerError) {
	req := &pb.GetAssignRecordReq{
		Limit: limit,
		Page:  page,
	}
	resp, err := c.typedStub().GetAssignRecord(ctx, req)
	return resp.GetRecord(), protocol.ToServerError(err)
}

// GetUserNameplateInfo 获取用户铭牌配置
func (c *Client) GetUserNameplateInfo(ctx context.Context, uid uint32, scenes pb.ScenesType) ([]*pb.NameplateDetailInfo, protocol.ServerError) {
	req := &pb.GetUserNameplateInfoReq{
		Uid:    uid,
		Scenes: scenes,
	}
	resp, err := c.typedStub().GetUserNameplateInfo(ctx, req)
	return resp.GetNameplates(), protocol.ToServerError(err)
}

// BatchGetUserNameplates 批量获取用户铭牌配置
func (c *Client) BatchGetUserNameplates(ctx context.Context, uidList []uint32, scenes pb.ScenesType) (map[uint32][]*pb.NameplateDetailInfo, protocol.ServerError) {
	req := &pb.BatchGetUserNameplatesReq{
		UidList: uidList,
		Scenes:  scenes,
	}
	resp, err := c.typedStub().BatchGetUserNameplates(ctx, req)

	result := make(map[uint32][]*pb.NameplateDetailInfo)
	for _, i := range resp.GetUserNameplates() {
		info := i
		result[info.GetUid()] = info.GetNameplates()
	}
	return result, protocol.ToServerError(err)
}

// SetUserNameplateInfo 用户设置需要佩戴的铭牌
func (c *Client) SetUserNameplateInfo(ctx context.Context, uid uint32, idList []uint32) protocol.ServerError {
	req := &pb.SetUserNameplateInfoReq{
		Uid:    uid,
		IdList: idList,
	}
	_, err := c.typedStub().SetUserNameplateInfo(ctx, req)
	return protocol.ToServerError(err)
}

// GetUserAllNameplateList 获取用户所有的铭牌信息
func (c *Client) GetUserAllNameplateList(ctx context.Context, uid uint32) (*pb.GetUserAllNameplateListResp, protocol.ServerError) {
	req := &pb.GetUserAllNameplateListReq{
		Uid: uid,
	}
	resp, err := c.typedStub().GetUserAllNameplateList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ActivityAssignNameplate(ctx context.Context, req *pb.ActivityAssignNameplateReq) protocol.ServerError {
	_, err := c.typedStub().ActivityAssignNameplate(ctx, req)
	return protocol.ToServerError(err)
}