package interaction_intimacy

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/interaction-intimacy"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	BatchGetIntimacyInfo(ctx context.Context, in *pb.BatchGetIntimacyInfoReq) (*pb.BatchGetIntimacyInfoResp, protocol.ServerError)
	GetIntimacyRule(ctx context.Context, in *pb.GetIntimacyRuleReq) (*pb.GetIntimacyRuleResp, protocol.ServerError)
	GetPlaymateList(ctx context.Context, in *pb.GetPlaymateListReq) (*pb.GetPlaymateListResp, protocol.ServerError)
	CheckUserLightStatus(ctx context.Context, in *pb.CheckUserLightStatusReq) (*pb.CheckUserLightStatusResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
