package channel_open_game_controller_logic

import (
	"context"
	"fmt"
	pb "golang.52tt.com/protocol/app/channel-open-game-controller"
	"google.golang.org/grpc"
	"testing"
)

func TestClient_GetChannelGamePlayerOpenid(t *testing.T) {
	client, err := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("channel-open-game-controller-logic.52tt.local"))
	fmt.Println(client, err)

	//base := ga.BaseReq{
	//	AppId:    0,
	//	MarketId: 2,
	//}

	req := pb.GetChannelGamePlayerOpenidReq{
		//BaseReq: &base,
		ChannelId: uint32(123123123),
		LoadId: uint32(123123123),
	}

	resp, err := client.GetChannelGamePlayerOpenid(context.Background(), &req)
	fmt.Println("GetChannelGamePlayerOpenid")
	fmt.Println("err:", err)
	fmt.Println("req:", req)
	fmt.Println("rep:", resp)
}