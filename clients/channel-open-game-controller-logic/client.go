package channel_open_game_controller_logic

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/app/channel-open-game-controller"
	logic "golang.52tt.com/protocol/services/logicsvr-go/channel-open-game-controller-logic"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-open-game-controller-logic"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return logic.NewChannelOpenGameControllerLogicClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() logic.ChannelOpenGameControllerLogicClient {
	return c.Stub().(logic.ChannelOpenGameControllerLogicClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetChannelGamePlayerOpenid(ctx context.Context, in *pb.GetChannelGamePlayerOpenidReq) (*pb.GetChannelGamePlayerOpenidResp, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelGamePlayerOpenid(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) JoinChannelGame(ctx context.Context, in *pb.JoinChannelGameReq) (*pb.JoinChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().JoinChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) QuitChannelGame(ctx context.Context, in *pb.QuitChannelGameReq) (*pb.QuitChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().QuitChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ReadyChannelGame(ctx context.Context, in *pb.ReadyChannelGameReq) (*pb.ReadyChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().ReadyChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UnReadyChannelGame(ctx context.Context, in *pb.UnReadyChannelGameReq) (*pb.UnReadyChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().UnReadyChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelGameStatusInfo(ctx context.Context, in *pb.GetChannelGameStatusInfoReq) (*pb.GetChannelGameStatusInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelGameStatusInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ExitChannelGame(ctx context.Context, in *pb.ExitChannelGameReq) (*pb.ExitChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().ExitChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) StartChannelGame(ctx context.Context, in *pb.StartChannelGameReq) (*pb.StartChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().StartChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelGamePlayerLoading(ctx context.Context, in *pb.SetChannelGamePlayerLoadingReq) (*pb.SetChannelGamePlayerLoadingResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelGamePlayerLoading(ctx, in)
	return resp, protocol.ToServerError(err)
}

//SetChannelGameModeInfo
func (c *Client) SetChannelGameModeInfo(ctx context.Context, in *pb.SetChannelGameModeInfoReq) (*pb.SetChannelGameModeInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelGameModeInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}
