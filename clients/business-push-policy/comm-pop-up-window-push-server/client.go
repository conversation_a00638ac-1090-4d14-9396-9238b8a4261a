package comm_pop_up_window_push_server

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/business-push-policy/comm-pop-up-window-push-server"
)

const (
	serviceName = "comm-pop-up-window-push-server"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewCommPopUpWindowPushServerClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.CommPopUpWindowPushServerClient {
	return c.Stub().(pb.CommPopUpWindowPushServerClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) BatchPushPwnKRaceToUser(ctx context.Context, req pb.BatchPushPwnKRaceToUserReq) (*pb.BatchPushPwnKRaceToUserResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchPushPwnKRaceToUser(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) FakeChannelEnterMsgPush(ctx context.Context, req pb.FakeChannelEnterMsgReq) (*pb.FakeChannelEnterMsgResp, protocol.ServerError) {
	resp, err := c.typedStub().FakeChannelEnterMsgPush(ctx, &req)
	return resp, protocol.ToServerError(err)
}
