package one_piece_notify

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/one-piece-notify"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"
)

const (
	serviceName = "one-piece-notify"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewOnePieceNotifyClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.OnePieceNotifyClient {
	return c.Stub().(pb.OnePieceNotifyClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) SetNotifyInfo(ctx context.Context, opUid uint32, req *pb.SetNotifyInfoReq) (*pb.SetNotifyInfoResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().SetNotifyInfo(ctx, &pb.SetNotifyInfoReq{
		NotifyType: req.GetNotifyType(),
		NotifyInfo: req.GetNotifyInfo(),
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetNotifyInfo(ctx context.Context, opUid uint32, req *pb.GetNotifyInfoReq) (*pb.GetNotifyInfoResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetNotifyInfo(ctx, &pb.GetNotifyInfoReq{
		NotifyType: req.GetNotifyType(),
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetCurrentNotify(ctx context.Context, opUid uint32, req *pb.GetCurrentNotifyReq) (*pb.GetCurrentNotifyResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetCurrentNotify(ctx, &pb.GetCurrentNotifyReq{
		NotifyType: req.GetNotifyType(),
	})
	return resp, protocol.ToServerError(err)
}
