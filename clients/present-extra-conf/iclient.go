// Code generated by quicksilver-cli. DO NOT EDIT.
package present_extra_conf

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/presentextraconf"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AddCustomizedPresentConfig(ctx context.Context, req *pb.AddCustomizedPresentConfigReq) (*pb.AddCustomizedPresentConfigResp,error)
	AddEffectDelayLevel(ctx context.Context, req *pb.AddEffectDelayLevelReq) (*pb.AddEffectDelayLevelResp,error)
	AddFlashEffectConfig(ctx context.Context, req *pb.AddFlashEffectConfigReq) (*pb.AddFlashEffectConfigResp,error)
	AddPresentFloatLayer(ctx context.Context, req *pb.AddPresentFloatLayerReq) (*pb.AddPresentFloatLayerResp,error)
	BoundPresentFlashEffect(ctx context.Context, req *pb.BoundPresentFlashEffectReq) (*pb.BoundPresentFlashEffectResp,error)
	CheckCustomizedGift(ctx context.Context, req *pb.CheckCustomizedGiftReq) (*pb.CheckCustomizedGiftResp,error)
	DelCustomizedPresentConfig(ctx context.Context, req *pb.DelCustomizedPresentConfigReq) (*pb.DelCustomizedPresentConfigResp,error)
	DelEffectDelayLevel(ctx context.Context, req *pb.DelEffectDelayLevelReq) (*pb.DelEffectDelayLevelResp,error)
	DelFlashEffectConfig(ctx context.Context, req *pb.DelFlashEffectConfigReq) (*pb.DelFlashEffectConfigResp,error)
	DelPresentFloatLayer(ctx context.Context, req *pb.DelPresentFloatLayerReq) (*pb.DelPresentFloatLayerResp,error)
	GetCustomPresentEffectTime(ctx context.Context, req *pb.GetCustomPresentEffectTimeReq) (*pb.GetCustomPresentEffectTimeResp,error)
	GetCustomizedPresentConfig(ctx context.Context, req *pb.GetAllCustomizedPresentConfigReq) (*pb.GetAllCustomizedPresentConfigResp,error)
	GetEffectDelayLevel(ctx context.Context, req *pb.GetEffectDelayLevelReq) (*pb.GetEffectDelayLevelResp,error)
	GetFlashEffectConfig(ctx context.Context, req *pb.GetFlashEffectConfigReq) (*pb.GetFlashEffectConfigResp,error)
	GetPopUpPresentList(ctx context.Context) (*pb.GetPopUpPresentListResp,protocol.ServerError)
	GetPresentEffectTime(ctx context.Context, req *pb.GetPresentEffectTimeReq) (*pb.GetPresentEffectTimeResp,error)
	GetPresentEffectTimeDetail(ctx context.Context, req *pb.GetPresentEffectTimeDetailReq) (*pb.GetPresentEffectTimeDetailResp,error)
	GetPresentFlashEffect(ctx context.Context, req *pb.GetPresentFlashEffectReq) (*pb.GetPresentFlashEffectResp,error)
	GetPresentFloatLayer(ctx context.Context, req *pb.GetPresentFloatLayerReq) (*pb.GetPresentFloatLayerResp,error)
	GetUserCustomizedInfo(ctx context.Context, req *pb.GetUserCustomizedInfoReq) (*pb.GetUserCustomizedInfoResp,error)
	GetUserCustomizedInfoByGiftId(ctx context.Context, req *pb.GetUserCustomizedInfoByGiftIdReq) (*pb.GetUserCustomizedInfoByGiftIdResp,error)
	NotifyPresentSend(ctx context.Context, req *pb.NotifyPresentSendReq) (*pb.NotifyPresentSendResp,error)
	NotifyPrivilegeLevelChange(ctx context.Context, req *pb.NotifyPrivilegeLevelChangeReq) (*pb.NotifyPrivilegeLevelChangeResp,error)
	ReportCustomOptionChoose(ctx context.Context, req *pb.ReportCustomOptionChooseReq) (*pb.ReportCustomOptionChooseResp,error)
	ReportCustomPresentSend(ctx context.Context, req *pb.ReportCustomPresentSendReq) (*pb.ReportCustomPresentSendResp,error)
	SearchPresent(ctx context.Context, req *pb.SearchPresentReq) (*pb.SearchPresentResp,error)
	UpdateCustomizedPresentConfig(ctx context.Context, req *pb.UpdateCustomizedPresentConfigReq) (*pb.UpdateCustomizedPresentConfigResp,error)
	UpdateEffectDelayLevel(ctx context.Context, req *pb.UpdateEffectDelayLevelReq) (*pb.UpdateEffectDelayLevelResp,error)
	UpdateFlashEffectConfig(ctx context.Context, req *pb.UpdateFlashEffectConfigReq) (*pb.UpdateFlashEffectConfigResp,error)
	UpdatePresentFloatLayer(ctx context.Context, req *pb.UpdatePresentFloatLayerReq) (*pb.UpdatePresentFloatLayerResp,error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
