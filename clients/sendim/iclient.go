package sendim

import (
	"golang.52tt.com/pkg/client"
	sendimPB "golang.52tt.com/protocol/services/sendimsvr"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	SendSync(ctx context.Context, req *sendimPB.SendSyncReq) (resp *sendimPB.SendSyncResp, err error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
