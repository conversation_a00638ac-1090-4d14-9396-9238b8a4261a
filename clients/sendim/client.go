package sendim

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc" //
	sendimPB "golang.52tt.com/protocol/services/sendimsvr"
)

const (
	serviceName = "sendim"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return sendimPB.NewSendImClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() sendimPB.SendImClient { return c.Stub().(sendimPB.SendImClient) }

func (c *Client) SendSync(ctx context.Context, req  *sendimPB.SendSyncReq) (resp *sendimPB.SendSyncResp ,err error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	r, err := c.typedStub().SendSync(ctx,req)
	if err != nil {
		return r, protocol.ToServerError(err)
	}
	return r, nil
}
