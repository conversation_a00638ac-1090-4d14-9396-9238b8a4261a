// Code generated by quicksilver-cli. DO NOT EDIT.
package accountlogic

import (
	context "context"
	"golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	contactPB "golang.52tt.com/protocol/app/contact"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetUserDetail(ctx context.Context, ttid uint32, opts ...grpc.CallOption) (*contactPB.GetUserDetailResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
