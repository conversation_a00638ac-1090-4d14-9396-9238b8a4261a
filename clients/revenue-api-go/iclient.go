// Code generated by quicksilver-cli. DO NOT EDIT.
package revenue_api_go

import (
	"golang.52tt.com/pkg/client"
	revenue_api_go "golang.52tt.com/protocol/services/revenue-api-go"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	BatchGetRevenueAwardInfosByIds(ctx context.Context, in *revenue_api_go.BatchGetRevenueAwardInfosByIdsReq, opts ...grpc.CallOption) (*revenue_api_go.BatchGetRevenueAwardInfosByIdsResp, error)
	BatchGetRevenueMicInfo(ctx context.Context, req *revenue_api_go.BatchGetRevenueMicInfoReq, opts ...grpc.CallOption) (*revenue_api_go.BatchGetRevenueMicInfoResp, error)
	GetRevenueAwardInfosByType(ctx context.Context, in *revenue_api_go.GetRevenueAwardInfosByTypeReq, opts ...grpc.CallOption) (*revenue_api_go.GetRevenueAwardInfosByTypeResp, error)
	GetRevenueEnterChannelInfo(ctx context.Context, in *revenue_api_go.GetRevenueEnterChannelInfoReq, opts ...grpc.CallOption) (*revenue_api_go.GetRevenueEnterChannelInfoResp, error)
	GetRevenueMicInfo(ctx context.Context, req *revenue_api_go.GetRevenueMicInfoReq, opts ...grpc.CallOption) (*revenue_api_go.GetRevenueMicInfoResp, error)
	GetRevenueUserVisitorRecord(ctx context.Context, in *revenue_api_go.GetRevenueUserVisitorRecordReq, opts ...grpc.CallOption) (*revenue_api_go.GetRevenueUserVisitorRecordResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
