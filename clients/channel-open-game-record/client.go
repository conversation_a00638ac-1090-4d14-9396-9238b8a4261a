package channel_open_game_record

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-open-game-record"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-open-game-record"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelOpenGameRecordClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelOpenGameRecordClient {
	return c.Stub().(pb.ChannelOpenGameRecordClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetRecord(ctx context.Context, uid, gameId uint32) (played, wined, broken uint32, serr protocol.ServerError) {
	resp, err := c.typedStub().BatchGetRecordByUid(ctx, &pb.BatchGetRecordByUidReq{
		UidList: []uint32{uid},
		GameId:  gameId,
	})

	if err != nil {
		return 0, 0, 0, protocol.ToServerError(err)
	}

	if nil == resp || nil == resp.GameRecordMap || nil == resp.GameRecordMap[uid] {
		return 0, 0, 0, nil
	}

	record := resp.GameRecordMap[uid]
	return record.PlayGames, record.PlayGamesWin, record.PlayGamesBreak, nil
}
