package entertainmentrecommendback

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGrpc "golang.52tt.com/pkg/tracing/grpc"

	pb "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
)

const (
	serviceName = "entertainmentrecommendback"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer, dopts ...grpc.DialOption) *Client {
	tracerInt := traceGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(tracerInt))
	return newClient(dopts...)
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewEntertainmentRecommendBackClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.EntertainmentRecommendBackClient {
	return c.Stub().(pb.EntertainmentRecommendBackClient)
}

func (c *Client) GetPrepareChannelByChannelID(ctx context.Context, uin uint32, in *pb.GetPrepareChannelByChannelIDReq) (*pb.GetPrepareChannelByChannelIDResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetPrepareChannelByChannelID(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetChannelTag(ctx context.Context, uin uint32, in *pb.GetChannelTagReq) (*pb.GetChannelTagResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetChannelTag(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) BatchGetChannelTag(ctx context.Context, uin uint32, in *pb.BatchGetChannelTagReq) (*pb.BatchGetChannelTagResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().BatchGetChannelTag(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) BatchGetChannelTagMap(ctx context.Context, channelIds []uint32) (map[uint32]*pb.ChannelTagConfigInfo, error) {
	m := make(map[uint32]*pb.ChannelTagConfigInfo)
	r, err := c.BatchGetChannelTag(ctx, 0, &pb.BatchGetChannelTagReq{ChannelIdList: channelIds})
	if err != nil {
		return m, protocol.ToServerError(err)
	}
	for i, item := range r.GetChannelTagList() {
		m[channelIds[i]] = item
	}
	return m, protocol.ToServerError(err)
}

// BatchGetChannelRecommendLevelReq

func (c *Client) BatchGetChannelRecommendLevel(ctx context.Context, uin uint32, cidList []uint32) (*pb.BatchGetChannelRecommendLevelResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().BatchGetChannelRecommendLevel(ctx, &pb.BatchGetChannelRecommendLevelReq{
		CidList: cidList,
	})
	return r, protocol.ToServerError(err)
}

func (c *Client) GetChannelTagByChannelId(ctx context.Context, uin, chanenlId uint32) (*pb.GetChannelTagResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetChannelTag(ctx, &pb.GetChannelTagReq{
		ChannelId: &chanenlId,
	})
	return r, protocol.ToServerError(err)
}

func (c *Client) GetChannelTagConfigInfo(ctx context.Context, uin uint32, in *pb.GetChannelTagConfigInfoReq) (*pb.GetChannelTagConfigInfoResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetChannelTagConfigInfo(ctx, in)
	return r, protocol.ToServerError(err)
}

// 获取全量推荐库房间列表信息
// 由于推荐库中 直播房数据巨大（9W+）该接口耗时比较长
// 如果只是为了获取房间ID列表，可以使用 GetPrepareIDListByChannelType
func (c *Client) GetPrepareChannelByPara(ctx context.Context, uin uint32, in *pb.GetPrepareChannelByParaReq) (*pb.GetPrepareChannelByParaResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetPrepareChannelByPara(ctx, in)
	return r, protocol.ToServerError(err)
}

// 根据房间类型 获取推荐库的房间ID列表
// 但是直播间类型的房间太多，不建议一次获取
func (c *Client) GetPrepareIDListByChannelType(ctx context.Context, uin uint32, in *pb.GetPrepareIDListByChannelTypeReq) (*pb.GetPrepareIDListByChannelTypeResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetPrepareIDListByChannelType(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) AutoAddLivePrepareChannel(ctx context.Context, uin uint32, in *pb.AutoAddLivePrepareChannelReq) (*pb.AutoAddLivePrepareChannelResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().AutoAddLivePrepareChannel(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetActivityChannelList(ctx context.Context, uin uint32, in *pb.GetActivityChannelListReq) (*pb.GetActivityChannelListResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetActivityChannelList(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) AddPrepareChannel(ctx context.Context, req *pb.AddPrepareChannelReq) (*pb.AddPrepareChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().AddPrepareChannel(ctx, req)

	return resp, protocol.ToServerError(err)
}

func (c *Client) DeletePrepareChannel(ctx context.Context, channelIdList []uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	_, err := c.typedStub().DeletePrepareChannel(ctx, &pb.DeletePrepareChannelReq{
		ChannelList: channelIdList,
	})

	return protocol.ToServerError(err)
}

func (c *Client) GetPrepareChannelListV2(ctx context.Context, in *pb.GetPrepareChannelListV2Req) (*pb.GetPrepareChannelListV2Resp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().GetPrepareChannelListV2(ctx, in)

	return resp, protocol.ToServerError(err)
}

func (c *Client) GetQuickRecChannelByTagId(ctx context.Context, tagId uint32) (uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().GetQuickRecChannelByTagId(ctx, &pb.GetQuickRecommendChannelByTagIdReq{TagId: &tagId})

	return resp.GetChannelId(), protocol.ToServerError(err)
}

func (c *Client) ClearLivePrepareChannel(ctx context.Context) (*pb.ClearLivePrepareChannelResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().ClearLivePrepareChannel(ctx, &pb.ClearLivePrepareChannelReq{})
	return resp, protocol.ToServerError(err)
}

func (c *Client) ClearWarSongRecommend(ctx context.Context) (*pb.ClearWarSongRecommendResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().ClearWarSongRecommend(ctx, &pb.ClearWarSongRecommendReq{})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelByTagId(ctx context.Context, in *pb.GetChannelByTagIdReq) (*pb.GetChannelByTagIdResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().GetChannelByTagId(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddLivePrepareChannel(ctx context.Context, uin uint32, in *pb.AddLivePrepareChannelReq) (*pb.AddLivePrepareChannelResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().AddLivePrepareChannel(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetWarSongRecChannelList(ctx context.Context, uin uint32, in *pb.GetWarSongRecChannelListReq) (*pb.GetWarSongRecChannelListResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetWarSongRecChannelList(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) AddActivityChannel(ctx context.Context, uin uint32, in *pb.AddActivityChannelReq) (*pb.AddActivityChannelResp, error) {
	r, err := c.typedStub().AddActivityChannel(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetChannelTagAdv(ctx context.Context, in *pb.GetChannelTagAdvReq) (*pb.GetChannelTagAdvResp, error) {
	r, err := c.typedStub().GetChannelTagAdv(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) BatGetChannelRecInfo(ctx context.Context, in *pb.BatGetChannelRecInfoReq) (*pb.BatGetChannelRecInfoResp, error) {
	r, err := c.typedStub().BatGetChannelRecInfo(ctx, in)
	return r, protocol.ToServerError(err)
}
