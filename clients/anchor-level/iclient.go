// Code generated by quicksilver-cli. DO NOT EDIT.
package anchor_level

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/anchor-level"
)

type IClient interface {
	client.BaseClient
	GetAnchorLevelMonthTask(ctx context.Context, uid uint32) (*pb.GetAnchorLevelMonthTaskResp,error)
	GetAnchorLevelNewTask(ctx context.Context, uid uint32) (*pb.AnchorLevelNewTask,error)
	GetAnchorMicPosTask(ctx context.Context, uid uint32) (*pb.AnchorMicPosTask,error)
	GetLiveAnchorLevel(ctx context.Context, level pb.ANCHOR_LEVEL_TYPE) (*pb.GetLiveAnchorLevelResp,error)
	GetLiveAnchorLevelByUid(ctx context.Context, uids []uint32) (map[uint32]*pb.LiveAnchorLevelInfo,error)
	GetLiveAnchorTaskEntry(ctx context.Context, uid, channelId uint32) (*pb.GetLiveAnchorTaskEntryResp,error)
	SetAnchorCheckPass(ctx context.Context, uid uint32, checkLevel string) (*pb.Empty,error)
	GetAnchorLevel(ctx context.Context, uid uint32)  (pb.ANCHOR_LEVEL_TYPE, error)
	ReplenishAnchorLevel(ctx context.Context, req *pb.ReplenishAnchorLevelReq) (*pb.ReplenishAnchorLevelResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
