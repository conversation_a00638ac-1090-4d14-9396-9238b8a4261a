package backpackgo

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/services/backpackgo"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {

	<PERSON><PERSON>("GetBackpackServer", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.BackpackServerReq
		resp, err := client.GetBackpackServer(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetBackpackServer %+v", resp)
	})

}
