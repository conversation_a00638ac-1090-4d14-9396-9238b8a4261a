package masked_call

import (
	"context"
	"fmt"
	masked_call "golang.52tt.com/protocol/services/masked-call"
	"google.golang.org/grpc"
	"testing"
)

func TestClient_QueryCallInfo(t *testing.T) {
	client, err := NewClient(grpc.WithBlock())
	fmt.Println(client, err)

	resp, err := client.QueryCallInfo(context.Background(), &masked_call.QueryCallInfoReq{
		Uid:       2208646,
		ChannelId: 10048070,
	})
	fmt.Println(resp)
	fmt.Println(err)
}

func TestClient_SetConnectStatus(t *testing.T) {
	client, err := NewClient(grpc.WithBlock())
	fmt.Println(client, err)
	// 2208647
	// 2208646
	resp, err := client.SetConnectStatus(context.Background(), &masked_call.SetConnectStatusReq{
		Uid:       2208646,
		ChannelId: 10048070,
		Status:    2,
	})
	// resp, err = client.SetConnectStatus(context.Background(), &masked_call.SetConnectStatusReq{
	// 	Uid:       2208647,
	// 	ChannelId: 10048070,
	// 	Status: 1,
	// })
	fmt.Println(resp)
	fmt.Println(err)
}

func TestClient_Unmask(t *testing.T) {
	client, err := NewClient(grpc.WithBlock())
	fmt.Println(client, err)
	// 2208647
	// 2208646
	resp, err := client.Unmask(context.Background(), &masked_call.UnmaskReq{
		Uid:       2208646,
		ChannelId: 10048070,
	})
	fmt.Println(resp)
	fmt.Println(err)
}

func TestClient_Roll(t *testing.T) {
	client, err := NewClient(grpc.WithBlock())
	fmt.Println(client, err)
	// 2208647
	// 2208646
	// channelId(10058903) uid(2216586)
	// 2205806
	resp, err := client.Roll(context.Background(), &masked_call.RollReq{
		Uid:       2216586,
		ChannelId: 10058903,
	})
	fmt.Println(resp)
	fmt.Println(err)
}

func TestClient_Comment(t *testing.T) {
	client, err := NewClient(grpc.WithBlock())
	fmt.Println(client, err)
	// 2208647
	// 2208646
	resp, err := client.Comment(context.Background(), &masked_call.CommentReq{
		Uid:       2208647,
		ChannelId: 10048070,
		Attitude:  1,
	})
	fmt.Println(resp)
	fmt.Println(err)
}

func TestClient_TipOff(t *testing.T) {
	client, err := NewClient(grpc.WithBlock())
	fmt.Println(client, err)
	// 2208647
	// 2208646
	resp, err := client.TipOff(context.Background(), &masked_call.TipOffReq{
		Uid:       2208647,
		ChannelId: 10048070,
		CallId:    1,
	})
	fmt.Println(resp)
	fmt.Println(err)
}
