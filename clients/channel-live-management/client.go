package channellivemanagement

import (
	"context"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/channel-live-management"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-live-management"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelLiveManagementClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelLiveManagementClient {
	return c.Stub().(pb.ChannelLiveManagementClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) CheckUserRegisterEntry(ctx context.Context, req *pb.CheckUserRegisterEntryReq) (*pb.CheckUserRegisterEntryResp, protocol.ServerError) {
	resp, err := c.typedStub().CheckUserRegisterEntry(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRegisterOfficialChList(ctx context.Context, req *pb.GetRegisterOfficialChListReq) (*pb.GetRegisterOfficialChListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRegisterOfficialChList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) RegisterOfficialChannel(ctx context.Context, req *pb.RegisterOfficialChannelReq) (*pb.RegisterOfficialChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().RegisterOfficialChannel(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CancelRegisterOfficialCh(ctx context.Context, req *pb.CancelRegisterOfficialChReq) (*pb.CancelRegisterOfficialChResp, protocol.ServerError) {
	resp, err := c.typedStub().CancelRegisterOfficialCh(ctx, req)
	return resp, protocol.ToServerError(err)
}
