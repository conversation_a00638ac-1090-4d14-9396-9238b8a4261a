package levelup_present_inner_logic

import (
    "context"
    "golang.52tt.com/pkg/client"
    grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
    pb "golang.52tt.com/protocol/services/levelup-present-inner-logic"
    "google.golang.org/grpc"
)

const (
    serviceName = "levelup-present-logic"
)

type Client struct {
    client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
    return &Client{
        BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
            return pb.NewLevelupPresentInnerLogicClient(cc)
        }, dopts...),
    }, nil
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
    dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
    return newClient(dopts...)
}

func (c *Client) typedStub() pb.LevelupPresentInnerLogicClient {
    return c.<PERSON>ub().(pb.LevelupPresentInnerLogicClient)
}

func (c *Client) SendLevelUpPresent (ctx context.Context, req *pb.SendLevelUpPresentReq) (*pb.SendLevelUpPresentResp, error) {
    return c.typedStub().SendLevelUpPresent(ctx, req)
}
