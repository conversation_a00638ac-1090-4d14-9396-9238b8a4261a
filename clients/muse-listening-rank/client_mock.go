// Code generated by MockGen. DO NOT EDIT.
// Source: iclient.go

// Package muse_listening_rank is a generated GoMock package.
package muse_listening_rank

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	muse_listening_rank "golang.52tt.com/protocol/services/muse-listening-rank"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddFlower mocks base method.
func (m *MockIClient) AddFlower(ctx context.Context, req *muse_listening_rank.AddFlowerReq) (*muse_listening_rank.AddFlowerResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddFlower", ctx, req)
	ret0, _ := ret[0].(*muse_listening_rank.AddFlowerResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddFlower indicates an expected call of AddFlower.
func (mr *MockIClientMockRecorder) AddFlower(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFlower", reflect.TypeOf((*MockIClient)(nil).AddFlower), ctx, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetMirrorData mocks base method.
func (m *MockIClient) GetMirrorData(ctx context.Context, req *muse_listening_rank.GetMirrorDataReq) (*muse_listening_rank.GetMirrorDataResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMirrorData", ctx, req)
	ret0, _ := ret[0].(*muse_listening_rank.GetMirrorDataResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMirrorData indicates an expected call of GetMirrorData.
func (mr *MockIClientMockRecorder) GetMirrorData(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMirrorData", reflect.TypeOf((*MockIClient)(nil).GetMirrorData), ctx, req)
}

// GetMirrorId mocks base method.
func (m *MockIClient) GetMirrorId(ctx context.Context, req *muse_listening_rank.GetMirrorIdReq) (*muse_listening_rank.GetMirrorIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMirrorId", ctx, req)
	ret0, _ := ret[0].(*muse_listening_rank.GetMirrorIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMirrorId indicates an expected call of GetMirrorId.
func (mr *MockIClientMockRecorder) GetMirrorId(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMirrorId", reflect.TypeOf((*MockIClient)(nil).GetMirrorId), ctx, req)
}

// GetSchoolRank mocks base method.
func (m *MockIClient) GetSchoolRank(ctx context.Context, req *muse_listening_rank.GetSchoolRankReq) (*muse_listening_rank.GetSchoolRankResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSchoolRank", ctx, req)
	ret0, _ := ret[0].(*muse_listening_rank.GetSchoolRankResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSchoolRank indicates an expected call of GetSchoolRank.
func (mr *MockIClientMockRecorder) GetSchoolRank(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSchoolRank", reflect.TypeOf((*MockIClient)(nil).GetSchoolRank), ctx, req)
}

// GetUserInfo mocks base method.
func (m *MockIClient) GetUserInfo(ctx context.Context, req *muse_listening_rank.GetUserInfoReq) (*muse_listening_rank.GetUserInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInfo", ctx, req)
	ret0, _ := ret[0].(*muse_listening_rank.GetUserInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserInfo indicates an expected call of GetUserInfo.
func (mr *MockIClientMockRecorder) GetUserInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInfo", reflect.TypeOf((*MockIClient)(nil).GetUserInfo), ctx, req)
}

// GetUserRankInSchool mocks base method.
func (m *MockIClient) GetUserRankInSchool(ctx context.Context, req *muse_listening_rank.GetUserRankInSchoolReq) (*muse_listening_rank.GetUserRankInSchoolResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRankInSchool", ctx, req)
	ret0, _ := ret[0].(*muse_listening_rank.GetUserRankInSchoolResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserRankInSchool indicates an expected call of GetUserRankInSchool.
func (mr *MockIClientMockRecorder) GetUserRankInSchool(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRankInSchool", reflect.TypeOf((*MockIClient)(nil).GetUserRankInSchool), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
