package muse_listening_rank

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/muse-listening-rank"
	"google.golang.org/grpc"
)

const (
	serviceName = "muse-listening-rank"
)

type Client struct {
	client.BaseClient
}

func (c *Client) typedStub() pb.MuseListeningRankClient {
	return c.Stub().(pb.MuseListeningRankClient)
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewMuseListeningRankClient(cc)
		}, dopts...),
	}, nil
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetUserInfo(ctx context.Context, req *pb.GetUserInfoReq) (*pb.GetUserInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserInfo(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserRankInSchool(ctx context.Context, req *pb.GetUserRankInSchoolReq) (*pb.GetUserRankInSchoolResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserRankInSchool(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSchoolRank(ctx context.Context, req *pb.GetSchoolRankReq) (*pb.GetSchoolRankResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSchoolRank(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetMirrorId(ctx context.Context, req *pb.GetMirrorIdReq) (*pb.GetMirrorIdResp, protocol.ServerError) {
	resp, err := c.typedStub().GetMirrorId(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetMirrorData(ctx context.Context, req *pb.GetMirrorDataReq) (*pb.GetMirrorDataResp, protocol.ServerError) {
	resp, err := c.typedStub().GetMirrorData(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddFlower(ctx context.Context, req *pb.AddFlowerReq) (*pb.AddFlowerResp, protocol.ServerError) {
	resp, err := c.typedStub().AddFlower(ctx, req)
	return resp, protocol.ToServerError(err)
}
