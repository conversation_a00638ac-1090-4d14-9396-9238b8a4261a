package guildtimeline2

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/guildtimeline2"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"
)

const (
	serviceName = "guildtimeline2"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewGuildTimeline2Client(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.GuildTimeline2Client { return c.Stub().(pb.GuildTimeline2Client) }

func (c *Client) CheckHaveGuildData(ctx context.Context, uin uint32, req *pb.CheckGuildDataIsExistReq) (resp *pb.CheckGuildDataIsExistResp, err error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err = c.typedStub().CheckGuildDataIsExist(ctx, req)
	return resp, err
}

func (c *Client) UpdateGuildBaseInfSeq(ctx context.Context, uin uint32, req *pb.UpdateGuildBaseInfoSeqReq) (err error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	_, err = c.typedStub().UpdateGuildBaseInfoSeq(ctx, req)
	return err
}

func (c *Client) UpdateGuildExtraInfoSeq(ctx context.Context, uin uint32, req *pb.UpdateGuildExtraInfoSeqReq) (err error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	_, err = c.typedStub().UpdateGuildExtraInfoSeq(ctx, req)
	return err
}

func (c *Client) UpdateGuildNumberDataInfoSeq(ctx context.Context, uin uint32, req *pb.UpdateGuildNumberInfoSeqReq) (err error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	_, err = c.typedStub().UpdateGuildNumberInfoSeq(ctx, req)
	return err
}

func (c *Client) UpdateGuildCheckinNum(ctx context.Context, uin uint32, req *pb.UpdateGuildCheckinNumSeqReq) (err error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	_, err = c.typedStub().UpdateGuildCheckinNumSeq(ctx, req)
	return err
}

func (c *Client) UpdateGuildCheckinTopUserSeq(ctx context.Context, uin uint32, req *pb.UpdateGuildCheckinTopUserSeqReq) (err error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	_, err = c.typedStub().UpdateGuildCheckinTopUserSeq(ctx, req)
	return err
}

func (c *Client) UpdateCommonIncrTimelineSeq(ctx context.Context, uin uint32, req *pb.UpdateCommonIncrTimelineSeqReq) (err error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	_, err = c.typedStub().UpdateCommonIncrTimelineSeq(ctx, req)
	return err
}

func (c *Client) UpdateGuildDonateNumSeq(ctx context.Context, uin uint32, req *pb.UpdateGuildDonateNumSeqReq) (err error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	_, err = c.typedStub().UpdateGuildDonateNumSeq(ctx, req)
	return err
}

func (c *Client) UpdateGuildDonateTopUserSeq(ctx context.Context, uin uint32, req *pb.UpdateGuildDonateTopUserSeqReq) (err error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	_, err = c.typedStub().UpdateGuildDonateTopUserSeq(ctx, req)
	return err
}

func (c *Client) GetGuildCheckInLastTime(ctx context.Context, uin uint32, guildId uint32) (uint32, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err := c.typedStub().GetGuildCheckInLastTime(ctx, &pb.GetGuildCheckInLastTimeReq{
		GuildId: guildId,
	})

	return resp.GetLastUpdateTime(), err
}

func (c *Client) GetGuildDonateLastTime(ctx context.Context, uin uint32, guildId uint32) (uint32, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err := c.typedStub().GetGuildDonateLastTime(ctx, &pb.GetGuildDonateLastTimeReq{
		GuildId: guildId,
	})

	return resp.GetLastUpdateTime(), err
}