// Code generated by quicksilver-cli. DO NOT EDIT.
package guildtimeline2

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "golang.org/x/net/context"
	pb "golang.52tt.com/protocol/services/guildtimeline2"
)

type IClient interface {
	client.BaseClient
	CheckHaveGuildData(ctx context.Context, uin uint32, req *pb.CheckGuildDataIsExistReq) (resp *pb.CheckGuildDataIsExistResp,err error)
	UpdateCommonIncrTimelineSeq(ctx context.Context, uin uint32, req *pb.UpdateCommonIncrTimelineSeqReq) (err error)
	UpdateGuildBaseInfSeq(ctx context.Context, uin uint32, req *pb.UpdateGuildBaseInfoSeqReq) (err error)
	UpdateGuildCheckinNum(ctx context.Context, uin uint32, req *pb.UpdateGuildCheckinNumSeqReq) (err error)
	UpdateGuildCheckinTopUserSeq(ctx context.Context, uin uint32, req *pb.UpdateGuildCheckinTopUserSeqReq) (err error)
	UpdateGuildDonateNumSeq(ctx context.Context, uin uint32, req *pb.UpdateGuildDonateNumSeqReq) (err error)
	UpdateGuildDonateTopUserSeq(ctx context.Context, uin uint32, req *pb.UpdateGuildDonateTopUserSeqReq) (err error)
	UpdateGuildExtraInfoSeq(ctx context.Context, uin uint32, req *pb.UpdateGuildExtraInfoSeqReq) (err error)
	UpdateGuildNumberDataInfoSeq(ctx context.Context, uin uint32, req *pb.UpdateGuildNumberInfoSeqReq) (err error)
	GetGuildCheckInLastTime(ctx context.Context, uin uint32, guildId uint32) (uint32, error)
	GetGuildDonateLastTime(ctx context.Context, uin uint32, guildId uint32) (uint32, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
