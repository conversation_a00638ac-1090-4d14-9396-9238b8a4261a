package cooldown

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/cooldownsvr"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AddDailyVisit(ctx context.Context, uid uint32, key string, typ uint32, val int64) (oldVal int64, err error)
	FetchGeneralVisit(ctx context.Context, uid uint32, in *pb.FetchGeneralVisitReq) (*pb.FetchGeneralVisitResp, error)
	AddPeriodVisit(ctx context.Context, uid uint32, key string, typ, ttl uint32, val int64) (oldVal int64, err error)
	FetchAndAddGeneralVisit(ctx context.Context, uid uint32, key string, typ, ttl uint32, val int64, updateTTL uint32) (oldVal int64, err error)
	FetchAndAddPeriodVisit(ctx context.Context, uid uint32, key string, typ, ttl uint32, val int64) (oldVal int64, err error)
	FetchAndAddGeneralVisitUpdateTtl(ctx context.Context, uid uint32, key string, typ uint32, ttl uint32, val int64) (oldVal int64, err error)
	ClearGeneralVisit(ctx context.Context, uid uint32, key string, typ uint32) (err error)
	CheckNx(ctx context.Context, uid uint32, key string, ttl int32) (bool, error)
	CheckNx2(ctx context.Context, uid uint32, req *pb.CheckNxReq) (*pb.CheckNxResp, error)
	FetchGeneralComplexVisit(ctx context.Context, uid uint32, key string) (*pb.FetchGeneralComplexVisitResp, error)
	FetchAndIncrGeneralComplexVisit(ctx context.Context, uid uint32, key, subType string, val int64, ttl int32) (*pb.FetchAndIncrGeneralComplexVisitResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
