package cooldown

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"
	"time"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc" //
	pb "golang.52tt.com/protocol/services/cooldownsvr"
)

const (
	serviceName = "cooldown"

	secondsPerHour = uint32(3600)
	secondsPerDay  = uint32(secondsPerHour * 24)
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewCooldownClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.CooldownClient { return c.Stub().(pb.CooldownClient) }

func (c *Client) AddDailyVisit(ctx context.Context, uid uint32, key string, typ uint32, val int64) (oldVal int64, err error) {
	keyWithDate := key + "_" + time.Now().Format("20060102")
	return c.AddPeriodVisit(ctx, uid, keyWithDate, typ, secondsPerDay, val)
}

func (c *Client) FetchGeneralVisit(ctx context.Context, uid uint32, in *pb.FetchGeneralVisitReq) (*pb.FetchGeneralVisitResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	out, err := c.typedStub().FetchGeneralVisit(ctx, in)
	return out, err
}

func (c *Client) AddPeriodVisit(ctx context.Context, uid uint32, key string, typ, ttl uint32, val int64) (oldVal int64, err error) {
	visit := &pb.FetchVisitReq{
		Key:  key,
		Type: typ,
		Val:  val,
		Ttl:  ttl,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().FetchAndAddPeriodVisit(ctx, &pb.FetchAndAddPeriodVisitReq{VisitReq: visit})
	return r.GetVisitResp().GetOldVal(), protocol.ToServerError(err)
}

func (c *Client) FetchAndAddGeneralVisit(ctx context.Context, uid uint32, key string, typ, ttl uint32, val int64, updateTTL uint32) (oldVal int64, err error) {
	visit := &pb.FetchVisitReq{
		Key:       key,
		Type:      typ,
		Val:       val,
		Ttl:       ttl,
		UpdateTtl: updateTTL,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().FetchAndAddGeneralVisit(ctx, &pb.FetchAndAddGeneralVisitReq{VisitReq: visit})
	return r.GetVisitResp().GetOldVal(), protocol.ToServerError(err)
}

func (c *Client) FetchAndAddPeriodVisit(ctx context.Context, uid uint32, key string, typ, ttl uint32, val int64) (oldVal int64, err error) {
	visit := &pb.FetchVisitReq{
		Key:  key,
		Type: typ,
		Val:  val,
		Ttl:  ttl,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().FetchAndAddPeriodVisit(ctx, &pb.FetchAndAddPeriodVisitReq{VisitReq: visit})
	return r.GetVisitResp().GetOldVal(), protocol.ToServerError(err)
}

func (c *Client) FetchAndAddGeneralVisitUpdateTtl(ctx context.Context, uid uint32, key string, typ uint32, ttl uint32, val int64) (oldVal int64, err error) {
	visit := &pb.FetchVisitReq{
		Key:       key,
		Type:      typ,
		Val:       val,
		Ttl:       ttl,
		UpdateTtl: 1,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().FetchAndAddGeneralVisit(ctx, &pb.FetchAndAddGeneralVisitReq{VisitReq: visit})
	return r.GetVisitResp().GetOldVal(), protocol.ToServerError(err)
}

func (c *Client) ClearGeneralVisit(ctx context.Context, uid uint32, key string, typ uint32) (err error) {
	req := &pb.ClearGeneralVisitReq{
		Key:  key,
		Type: typ,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err = c.typedStub().ClearGeneralVisit(ctx, req)
	return err
}

func (c *Client) CheckNx(ctx context.Context, uid uint32, key string, ttl int32) (bool, error) {
	req := &pb.CheckNxReq{
		Key: key,
		Ttl: ttl,
		Uid: uid,
		Val: "1",
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().CheckNx(ctx, req)
	return resp.GetIsExistBefore(), err
}

func (c *Client) CheckNx2(ctx context.Context, uid uint32, req *pb.CheckNxReq) (*pb.CheckNxResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().CheckNx(ctx, req)
	return resp, err
}

func (c *Client) FetchGeneralComplexVisit(ctx context.Context, uid uint32, key string) (*pb.FetchGeneralComplexVisitResp, error) {
	req := &pb.FetchGeneralComplexVisitReq{
		Key: key,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().FetchGeneralComplexVisit(ctx, req)
	return resp, err
}

func (c *Client) FetchAndIncrGeneralComplexVisit(ctx context.Context, uid uint32, key, subType string, val int64, ttl int32) (*pb.FetchAndIncrGeneralComplexVisitResp, error) {
	req := &pb.FetchAndIncrGeneralComplexVisitReq{
		Key:     key,
		SubType: subType,
		Val:     val,
		Ttl:     ttl,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().FetchAndIncrGeneralComplexVisit(ctx, req)
	return resp, err
}
