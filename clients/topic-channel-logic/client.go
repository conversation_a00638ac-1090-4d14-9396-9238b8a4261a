package topic_channel_logic

import (
	"context"
	"golang.52tt.com/protocol/app/channel"

	"golang.52tt.com/pkg/protocol"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/logicsvr-go/topic_channel_logic"
	"google.golang.org/grpc"
)

const (
	// serviceName = "topic-channel-logic.staging"
	serviceName = "topic-channel-logic"
	// serviceName = "topic-channel-logic-local"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewTopicChannelLogicClient(cc)
			},
			dopts...,
		),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.TopicChannelLogicClient {
	return c.Stub().(pb.TopicChannelLogicClient)
}

func (c *Client) CreateTopicChannel(ctx context.Context, in *topic_channel.CreateTopicChannelReq) (*topic_channel.CreateTopicChannelResp, protocol.ServerError) {
	//in := &topic_channel.CreateTopicChannelReq{
	//	BaseReq: &app.BaseReq{
	//		AntispamToken: &app.AntispamToken{},
	//		VerifyCodeInfo: &app.AntispamVerifyCodeInfo{},
	//		AntispamInfo: &app.AntispamInfomation{},
	//	},
	//}

	resp, err := c.typedStub().CreateTopicChannel(ctx, in)

	return resp, protocol.ToServerError(err)
}

func (c *Client) GetTopicChannelInfo(ctx context.Context, in *topic_channel.GetTopicChannelInfoReq) (*topic_channel.GetTopicChannelInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetTopicChannelInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) KeepAliveTopicChannel(ctx context.Context, in *topic_channel.KeepAliveTopicChannelReq) (*topic_channel.KeepAliveTopicChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().KeepAliveTopicChannel(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRoomProxyTip(ctx context.Context, in *topic_channel.GetRoomProxyTipReq) (*topic_channel.GetRoomProxyTipResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRoomProxyTip(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListRecommendTopicChannel(ctx context.Context, in *topic_channel.ListRecommendTopicChannelReq) (*topic_channel.ListRecommendTopicChannelResp, protocol.ServerError) {
	//in := &topic_channel.ListRecommendTopicChannelReq{
	//	BaseReq: &app.BaseReq{
	//		AntispamToken: &app.AntispamToken{},
	//		VerifyCodeInfo: &app.AntispamVerifyCodeInfo{},
	//		AntispamInfo: &app.AntispamInfomation{},
	//	},
	//	LoadMore: &topic_channel.ListRecommendTopicChannelLoadMore{},
	//}

	resp, err := c.typedStub().ListRecommendTopicChannel(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) TabTopicChannel(ctx context.Context, in *topic_channel.TabTopicChannelReq) (
	*topic_channel.TabTopicChannelResp, protocol.ServerError) {

	resp, err := c.typedStub().TabTopicChannel(ctx, in)

	return resp, protocol.ToServerError(err)
}

func (c *Client) GetTopicChannelRoomName(ctx context.Context, in *topic_channel.GetTopicChannelRoomNameReq) (
	*topic_channel.GetTopicChannelRoomNameResp, protocol.ServerError) {

	resp, err := c.typedStub().GetTopicChannelRoomName(ctx, in)

	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelRoomNameConfig(ctx context.Context, in *topic_channel.GetChannelRoomNameConfigReq) (
	*topic_channel.GetChannelRoomNameConfigResp, error) {

	resp, err := c.typedStub().GetChannelRoomNameConfig(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetBannerList(ctx context.Context, in *channel.GetChannelAdvReq) (
	*channel.GetChannelAdvResp, error) {

	resp, err := c.typedStub().GetBannerList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HideTopicChannel(ctx context.Context, in *topic_channel.HideTopicChannelReq) (*topic_channel.HideTopicChannelResp, error) {
	resp, err := c.typedStub().HideTopicChannel(ctx, in)
	return resp, err
}

func (c *Client) CreateTopicChannelV2(ctx context.Context, in *topic_channel.CreateTopicChannelV2Req) (*topic_channel.CreateTopicChannelV2Resp, error) {
	resp, err := c.typedStub().CreateTopicChannelV2(ctx, in)
	return resp, err
}

func (c *Client) QuickFormTeam(ctx context.Context, in *topic_channel.QuickFormTeamReq) (*topic_channel.QuickFormTeamResp, error) {
	resp, err := c.typedStub().QuickFormTeam(ctx, in)
	return resp, err
}

func (c *Client) ListTopicChannelV2(ctx context.Context, in *topic_channel.ListTopicChannelV2Req) (*topic_channel.ListTopicChannelV2Resp, error) {
	resp, err := c.typedStub().ListTopicChannelV2(ctx, in)
	return resp, err
}

func (c *Client) GetChannelDialog(ctx context.Context, in *topic_channel.GetChannelDialogReq) (*topic_channel.GetChannelDialogResp, error) {
	resp, err := c.typedStub().GetChannelDialog(ctx, in)
	return resp, err
}

func (c *Client) ListTabBlocks(ctx context.Context, in *topic_channel.ListTabBlocksReq) (*topic_channel.ListTabBlocksResp, error) {
	resp, err := c.typedStub().ListTabBlocks(ctx, in)
	return resp, err
}

func (c *Client) GetFormTeamInfo(ctx context.Context, in *topic_channel.GetFormTeamInfoReq) (*topic_channel.GetFormTeamInfoResp, error) {
	resp, err := c.typedStub().GetFormTeamInfo(ctx, in)
	return resp, err
}

func (c *Client) SwitchGamePlay(ctx context.Context, in *topic_channel.SwitchGamePlayReq) (*topic_channel.SwitchGamePlayResp, error) {
	resp, err := c.typedStub().SwitchGamePlay(ctx, in)
	return resp, err
}

func (c *Client) GetTabList(ctx context.Context, in *topic_channel.GetTabListReq) (*topic_channel.GetTabListResp, error) {
	resp, err := c.typedStub().GetTabList(ctx, in)
	return resp, err
}

func (c *Client) GetHomePageCardList(ctx context.Context, in *topic_channel.GetHomePageCardListReq) (*topic_channel.GetHomePageCardListResp, error) {
	resp, err := c.typedStub().GetHomePageCardList(ctx, in)
	return resp, err
}

func (c *Client) GetDialogV2(ctx context.Context, in *topic_channel.GetDialogV2Req) (*topic_channel.GetDialogV2Resp, error) {
	resp, err := c.typedStub().GetDialogV2(ctx, in)
	return resp, err
}

func (c *Client) QuickFormTeamV2(ctx context.Context, in *topic_channel.QuickFormTeamV2Req) (*topic_channel.QuickFormTeamV2Resp, error) {
	resp, err := c.typedStub().QuickFormTeamV2(ctx, in)
	return resp, err
}

func (c *Client) GetTCCache(ctx context.Context, in *topic_channel.GetTCCacheReq) (
	*topic_channel.GetTCCacheResp, protocol.ServerError) {
	resp, err := c.typedStub().GetTCCache(ctx, in)
	return resp, protocol.ToServerError(err)
}
