package FindFriends

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/find_friends"
	"google.golang.org/grpc"
	"time"
)

type IClient interface {
	client.BaseClient
	GetUser(ctx context.Context, userID uint32) (*pb.UserInfo, protocol.ServerError)
	CreateOrUpdateUser(ctx context.Context, userInfo *pb.UserInfo, clearPlayingGames bool) (*pb.UserInfo, protocol.ServerError)
	UpdatePhoto(ctx context.Context, userID, index uint32, photoURL string) protocol.ServerError
	GetRecommendUsers(ctx context.Context, userID, count uint32, sessionID string, filterUserIDs []uint32, genderFilter GenderFilter, location *pb.Location) ([]*pb.UserInfo, protocol.ServerError)
	Like(ctx context.Context, userID uint32, throws []uint32, like uint32) (*pb.LikeResponse, protocol.ServerError)
	CheckLiked(ctx context.Context, userID uint32, targetUserID uint32) (bool, protocol.ServerError)
	CheckBeenLiked(ctx context.Context, userID uint32, testUserIDs []uint32) ([]uint32, protocol.ServerError)
	GetFreeLikeQuota(ctx context.Context, userID uint32, t time.Time) (uint32, protocol.ServerError)
	GrantFreeLikeQuota(ctx context.Context, userID, quota uint32, t time.Time) (bool, uint32, protocol.ServerError)
	ReportActive(ctx context.Context, userID uint32) protocol.ServerError
	AddAFKLiked(ctx context.Context, userID uint32) (uint32, protocol.ServerError)
	ClearAFKLiked(ctx context.Context, userID uint32) (bool, protocol.ServerError)
	ClearMutualLikesMe(ctx context.Context, userID_A, userID_B uint32) protocol.ServerError
	GetTodayLikedCount(ctx context.Context, userID uint32, t time.Time) (uint32, protocol.ServerError)
	StartQuickMatch(ctx context.Context, userID, userGender uint32, gameName string, options map[string]uint32, expiration time.Duration) (string, protocol.ServerError)
	StartQuickMatchNew(ctx context.Context, userID, userGender uint32, games []*pb.QuickMatchGame, expiration time.Duration, supplementChannelId, supplementNum uint32) (*pb.StartQuickMatchResponse, protocol.ServerError)
	CancelQuickMatch(ctx context.Context, userID uint32, sessionID string) protocol.ServerError
	QuickMatchKeepAlive(ctx context.Context, userID uint32, sessionID string) (bool, uint32, protocol.ServerError)
	GetQuickMatchStatistics(ctx context.Context, beginTime, endTime time.Time) (uint32, uint32, protocol.ServerError)
	PunishQuickMatchDeserter(ctx context.Context, userID, channelID, onlineSeconds uint32) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
