// Code generated by quicksilver-cli. DO NOT EDIT.
package channellivemgr

import (
	context "context"
	"golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	apiCenterPb "golang.52tt.com/protocol/services/apicentergo"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AcceptAppointPk(ctx context.Context, in *pb.AcceptAppointPkReq) (*pb.AcceptAppointPkResp, protocol.ServerError)
	AddAppointPkInfo(ctx context.Context, in *pb.AddAppointPkInfoReq) (*pb.AddAppointPkInfoResp, protocol.ServerError)
	AddChannelLiveAnchorScore(ctx context.Context, req pb.AddChannelLiveAnchorScoreReq) (*pb.AddChannelLiveAnchorScoreResp, protocol.ServerError)
	AddVirtualAnchorPer(ctx context.Context, req *pb.AddVirtualAnchorPerReq) (*pb.AddVirtualAnchorPerResp, protocol.ServerError)
	ApplyPk(ctx context.Context, req *pb.ApplyPkReq) (*pb.ApplyPkResp, protocol.ServerError)
	BatGetChannelLiveInfo(ctx context.Context, req *pb.BatGetChannelLiveInfoReq) (*pb.BatGetChannelLiveInfoResp, protocol.ServerError)
	BatchGetAllChannelLive(ctx context.Context, req *pb.BatchGetAllChannelLiveReq) (*pb.BatchGetAllChannelLiveResp, protocol.ServerError)
	BatchGetAnchorTotalData(ctx context.Context, uidList []uint32) (*pb.BatchGetAnchorTotalDataResp, protocol.ServerError)
	BatchGetChannelLiveRecord(ctx context.Context, req pb.BatchGetChannelLiveRecordReq) (*pb.BatchGetChannelLiveRecordResp, protocol.ServerError)
	BatchGetChannelLiveStatus(ctx context.Context, req pb.BatchGetChannelLiveStatusReq) (*pb.BatchGetChannelLiveStatusResp, protocol.ServerError)
	BatchGetChannelLiveStatusSimple(ctx context.Context, req pb.BatchGetChannelLiveStatusSimpleReq) (*pb.BatchGetChannelLiveStatusSimpleResp, protocol.ServerError)
	BatchGetChannelLiveTotalData(ctx context.Context, req *pb.BatchGetChannelLiveTotalDataReq) (*pb.BatchGetChannelLiveTotalDataResp, protocol.ServerError)
	BatchGetGroupFansGiftValue(ctx context.Context, req *pb.BatchGetGroupFansGiftValueReq) (*pb.BatchGetGroupFansGiftValueResp, protocol.ServerError)
	CancelPKApply(ctx context.Context, req pb.CancelPKApplyReq) (*pb.CancelPKApplyResp, protocol.ServerError)
	CancelPkMatch(ctx context.Context, uid, channelId uint32) (*pb.CancelPkMatchResp, protocol.ServerError)
	ChannelLiveHeartbeat(ctx context.Context, req pb.ChannelLiveHeartbeatReq) (*pb.ChannelLiveHeartbeatResp, protocol.ServerError)
	CheckHasVirtualAnchorPer(ctx context.Context, req *pb.CheckHasVirtualAnchorPerReq) (*pb.CheckHasVirtualAnchorPerResp, protocol.ServerError)
	CheckIsAnchorInBackList(ctx context.Context, uid uint32) (*pb.CheckIsAnchorInBackListResp, error)
	ConfirmAppointPkPush(ctx context.Context, in *pb.ConfirmAppointPkPushReq) (*pb.ConfirmAppointPkPushResp, protocol.ServerError)
	DelAppointPkInfo(ctx context.Context, in *pb.DelAppointPkInfoReq) (*pb.DelAppointPkInfoResp, protocol.ServerError)
	DelChannelLiveInfo(ctx context.Context, uid uint32, reason string) (*pb.DelChannelLiveInfoResp, protocol.ServerError)
	DelChannelLiveInfoV2(ctx context.Context, req *pb.DelChannelLiveInfoReq) (*pb.DelChannelLiveInfoResp, protocol.ServerError)
	DelVirtualAnchorPer(ctx context.Context, req *pb.DelVirtualAnchorPerReq) (*pb.DelVirtualAnchorPerResp, protocol.ServerError)
	GetAllAnchor(ctx context.Context, req *pb.GetAllAnchorReq) (*pb.GetAllAnchorResp, protocol.ServerError)
	GetAnchorByUidList(ctx context.Context, in *pb.GetAnchorByUidListReq) (*pb.GetAnchorByUidListResp, protocol.ServerError)
	GetAnchorList(ctx context.Context, page, pageSize uint32) ([]*pb.AnchorInfo, protocol.ServerError)
	GetAnchorListV2(ctx context.Context, req *pb.GetAnchorListReq) (*pb.GetAnchorListResp, protocol.ServerError)
	GetAnchorMonthScoreList(ctx context.Context, req *pb.GetAnchorMonthScoreListReq) (*pb.GetAnchorMonthScoreListResp, protocol.ServerError)
	GetAnchorOperRecord(ctx context.Context, req *pb.GetAnchorOperRecordReq) (*pb.GetAnchorOperRecordResp, protocol.ServerError)
	GetAnchorScoreList(ctx context.Context, req *pb.GetAnchorScoreListReq) (*pb.GetAnchorScoreListResp, protocol.ServerError)
	GetAnchorScoreOrderList(ctx context.Context, req *pb.GetAnchorScoreOrderListReq) (*pb.GetAnchorScoreOrderListResp, protocol.ServerError)
	GetApplyList(ctx context.Context, req *pb.GetApplyListReq) (*pb.GetApplyListResp, protocol.ServerError)
	GetAppointPkInfo(ctx context.Context, in *pb.GetAppointPkInfoReq) (*pb.GetAppointPkInfoResp, protocol.ServerError)
	GetAppointPkInfoList(ctx context.Context, in *pb.GetAppointPkInfoListReq) (*pb.GetAppointPkInfoListResp, protocol.ServerError)
	GetChanneLivePkRankUser(ctx context.Context, req pb.GetChanneLivePkRankUserReq) (*pb.GetChanneLivePkRankUserResp, protocol.ServerError)
	GetChannelLiveAnchorScore(ctx context.Context, uid uint32) (*pb.GetChannelLiveAnchorScoreResp, protocol.ServerError)
	GetChannelLiveAnchorScoreLog(ctx context.Context, req *pb.GetChannelLiveAnchorScoreLogReq) (*pb.GetChannelLiveAnchorScoreLogResp, protocol.ServerError)
	GetChannelLiveData(ctx context.Context, req pb.GetChannelLiveDataReq) (*pb.GetChannelLiveDataResp, protocol.ServerError)
	GetChannelLiveHistoryRecord(ctx context.Context, req pb.GetChannelLiveHistoryRecordReq) (*pb.GetChannelLiveHistoryRecordResp, protocol.ServerError)
	GetChannelLiveInfo(ctx context.Context, uid uint32, expire bool) (*pb.GetChannelLiveInfoResp, protocol.ServerError)
	GetChannelLiveInfoByChannelId(ctx context.Context, channelId uint32) (*pb.GetChannelLiveInfoResp, protocol.ServerError)
	GetChannelLivePKRecord(ctx context.Context, req pb.GetChannelLivePKRecordReq) (*pb.GetChannelLivePKRecordResp, protocol.ServerError)
	GetChannelLiveRankUser(ctx context.Context, req pb.GetChannelLiveRankUserReq) (*pb.GetChannelLiveRankUserResp, protocol.ServerError)
	GetChannelLiveStatus(ctx context.Context, req pb.GetChannelLiveStatusReq) (*pb.GetChannelLiveStatusResp, protocol.ServerError)
	GetChannelLiveTOPN(ctx context.Context, req pb.GetChannelLiveTOPNReq) (*pb.GetChannelLiveTOPNResp, protocol.ServerError)
	GetChannelLiveTotalData(ctx context.Context, req pb.GetChannelLiveTotalDataReq) (*pb.GetChannelLiveTotalDataResp, protocol.ServerError)
	GetChannelLiveWatchTimeRankUser(ctx context.Context, req pb.GetChannelLiveWatchTimeRankUserReq) (*pb.GetChannelLiveWatchTimeRankUserResp, protocol.ServerError)
	GetHeartBeatTimeOut(ctx context.Context, expire int64, del bool) (*pb.GetHeartBeatTimeOutResp, protocol.ServerError)
	GetItemConfig(ctx context.Context, req *pb.GetItemConfigReq) (*pb.GetItemConfigResp, protocol.ServerError)
	GetMyToolList(ctx context.Context, req *pb.GetMyToolListReq) (*pb.GetMyToolListResp, protocol.ServerError)
	GetPKMatchInfo(ctx context.Context, uid, channelId uint32) (*pb.GetPKMatchInfoResp, protocol.ServerError)
	GetPkInfo(ctx context.Context, uid uint32, channelId uint32) (*pb.GetPkInfoResp, protocol.ServerError)
	GetUserDayPushCnt(ctx context.Context, req *pb.GetUserPushCntReq) (*pb.GetUserPushCntResp, protocol.ServerError)
	GetUserPushCnt(ctx context.Context, req *pb.GetUserPushCntReq) (*pb.GetUserPushCntResp, protocol.ServerError)
	GetVirtualAnchorPerList(ctx context.Context, req *pb.GetVirtualAnchorPerListReq) (*pb.GetVirtualAnchorPerListResp, protocol.ServerError)
	GetVirtualLiveChannelSecret(ctx context.Context, req *pb.GetVirtualLiveChannelSecretReq) (*pb.GetVirtualLiveChannelSecretResp, protocol.ServerError)
	GetVirtualLiveInfoBySecret(ctx context.Context, req *pb.GetVirtualLiveInfoBySecretReq) (*pb.GetVirtualLiveInfoBySecretResp, protocol.ServerError)
	HandlerApply(ctx context.Context, req *pb.HandlerApplyReq) (*pb.HandlerApplyResp, protocol.ServerError)
	PushTest(ctx context.Context, req *pb.PushTestReq) (*pb.PushTestResp, protocol.ServerError)
	ReportClientIDChange(ctx context.Context, req *pb.ReportClientIDChangeReq) (*pb.ReportClientIDChangeResp, protocol.ServerError)
	SearchAnchor(ctx context.Context, req *pb.SearchAnchorReq) (*pb.SearchAnchorResp, protocol.ServerError)
	SetAuthFlag(ctx context.Context, req *pb.SetAuthFlagReq) (*pb.SetAuthFlagResp, protocol.ServerError)
	SetChannelLiveInfo(ctx context.Context, req *pb.SetChannelLiveInfoReq) (*pb.SetChannelLiveInfoResp, protocol.ServerError)
	SetChannelLiveInfoForTest(ctx context.Context, req *pb.SetChannelLiveInfoForTestReq) (*pb.SetChannelLiveInfoForTestResp, protocol.ServerError)
	SetChannelLiveOpponentMicFlag(ctx context.Context, channelId uint32, flag pb.ChannelLiveOpponentMicFlag) (*pb.SetChannelLiveOpponentMicFlagResp, protocol.ServerError)
	SetChannelLiveStatus(ctx context.Context, req pb.SetChannelLiveStatusReq) (*pb.SetChannelLiveStatusResp, protocol.ServerError)
	SetChannelLiveTag(ctx context.Context, in *pb.SetChannelLiveTagReq) (*pb.SetChannelLiveTagResp, protocol.ServerError)
	SetPkStatus(ctx context.Context, req pb.SetPkStatusReq) (*pb.SetPkStatusResp, protocol.ServerError)
	StartPkMatch(ctx context.Context, uid, channelId uint32, ty pb.ChannelLivePKMatchType) (*pb.StartPkMatchResp, protocol.ServerError)
	UpdateAppointPkInfo(ctx context.Context, in *pb.UpdateAppointPkInfoReq) (*pb.UpdateAppointPkInfoResp, protocol.ServerError)
	UpdateVirtualAnchorPer(ctx context.Context, req *pb.UpdateVirtualAnchorPerReq) (*pb.UpdateVirtualAnchorPerResp, protocol.ServerError)
	GetAnchorListMgr(ctx context.Context, req *apiCenterPb.GetAnchorListReq) (*apiCenterPb.GetAnchorListResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
