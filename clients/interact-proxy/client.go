package interact_proxy

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/interact-proxy"
	"google.golang.org/grpc"
)

const (
	serviceName = "interact-proxy"
)

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewInteractProxyClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.InteractProxyClient {
	return c.Stub().(pb.InteractProxyClient)
}

func (c *Client) ReportCommonInvitationResult(ctx context.Context, inviteId string, style uint32, content []byte) error {
	_, err := c.typedStub().ReportCommonInvitationResult(ctx, &pb.ReportCommonInvitationResultReq{
		InviteId:            inviteId,
		InviteStyle:         pb.InviteStyle(style),
		InviteResultContent: content,
	})
	return err
}

func (c *Client) GetCommonInvitationResult(ctx context.Context, inviteId string) (*pb.GetCommonInvitationResultResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCommonInvitationResult(ctx, &pb.GetCommonInvitationResultReq{
		InviteId: inviteId,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}

func (c *Client) SendCommonInvitation(ctx context.Context, req *pb.SendCommonInvitationReq) (*pb.SendCommonInvitationResp, protocol.ServerError) {
	resp, err := c.typedStub().SendCommonInvitation(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}
