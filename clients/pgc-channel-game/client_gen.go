// Code generated by protoc-gen-go-quicksilver. DO NOT EDIT.

package pgc_channel_game

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	protocol "golang.52tt.com/pkg/protocol"
	pgc_channel_game "golang.52tt.com/protocol/services/pgc-channel-game"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "pgc-channel-game"
)

// Client is the wrapper-client for PgcChannelGame client.
type Client struct {
	client.BaseClient
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pgc_channel_game.NewPgcChannelGameClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of PgcChannelGameClient.
func (c *Client) typedStub() pgc_channel_game.PgcChannelGameClient {
	return c.Stub().(pgc_channel_game.PgcChannelGameClient)
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// NewClientOrg creates a new Client with wrapper-client instance.
func NewClientOrg(dopts ...grpc.DialOption) (pgc_channel_game.PgcChannelGameClient, error) {
	c, err := NewClient(dopts...)
	if err != nil {
		return nil, err
	}
	return c, nil
}

// GetGameList
func (c *Client) GetGameList(ctx context.Context, req *pgc_channel_game.GetGameListReq, opts ...grpc.CallOption) (*pgc_channel_game.GetGameListResp, error) {
	resp, err := c.typedStub().GetGameList(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// SetGamePhase
func (c *Client) SetGamePhase(ctx context.Context, req *pgc_channel_game.SetGamePhaseReq, opts ...grpc.CallOption) (*pgc_channel_game.SetGamePhaseResp, error) {
	resp, err := c.typedStub().SetGamePhase(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetChannelGameInfo
func (c *Client) GetChannelGameInfo(ctx context.Context, req *pgc_channel_game.GetChannelGameInfoReq, opts ...grpc.CallOption) (*pgc_channel_game.GetChannelGameInfoResp, error) {
	resp, err := c.typedStub().GetChannelGameInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// SetNextBombUser
func (c *Client) SetNextBombUser(ctx context.Context, req *pgc_channel_game.SetNextBombUserReq, opts ...grpc.CallOption) (*pgc_channel_game.SetNextBombUserResp, error) {
	resp, err := c.typedStub().SetNextBombUser(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// ClearUserBombInfo
func (c *Client) ClearUserBombInfo(ctx context.Context, req *pgc_channel_game.ClearUserBombInfoReq, opts ...grpc.CallOption) (*pgc_channel_game.ClearUserBombInfoResp, error) {
	resp, err := c.typedStub().ClearUserBombInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// SetChannelCurrentGameInfo
func (c *Client) SetChannelCurrentGameInfo(ctx context.Context, req *pgc_channel_game.SetChannelCurrentGameInfoReq, opts ...grpc.CallOption) (*pgc_channel_game.SetChannelCurrentGameInfoResp, error) {
	resp, err := c.typedStub().SetChannelCurrentGameInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetChannelCurrentGameInfo
func (c *Client) GetChannelCurrentGameInfo(ctx context.Context, req *pgc_channel_game.GetChannelCurrentGameInfoReq, opts ...grpc.CallOption) (*pgc_channel_game.GetChannelCurrentGameInfoResp, error) {
	resp, err := c.typedStub().GetChannelCurrentGameInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DelChannelCurrentGameInfo(ctx context.Context, in *pgc_channel_game.DelChannelCurrentGameInfoReq, opts ...grpc.CallOption) (*pgc_channel_game.DelChannelCurrentGameInfoResp, error) {
	resp, err := c.typedStub().DelChannelCurrentGameInfo(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetGameBombPhase(ctx context.Context, in *pgc_channel_game.BatchGetGameBombPhaseReq, opts ...grpc.CallOption) (*pgc_channel_game.BatchGetGameBombPhaseResp, error) {
	resp, err := c.typedStub().BatchGetGameBombPhase(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}
