// Code generated by quicksilver-cli. DO NOT EDIT.
package pgc_channel_game

import(
	"golang.52tt.com/pkg/client"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
	pgc_channel_game "golang.52tt.com/protocol/services/pgc-channel-game"
)

type IClient interface {
	client.BaseClient
	BatchGetGameBombPhase(ctx context.Context, in *pgc_channel_game.BatchGetGameBombPhaseReq, opts ...grpc.CallOption) (*pgc_channel_game.BatchGetGameBombPhaseResp,error)
	ClearUserBombInfo(ctx context.Context, req *pgc_channel_game.ClearUserBombInfoReq, opts ...grpc.CallOption) (*pgc_channel_game.ClearUserBombInfoResp,error)
	DelChannelCurrentGameInfo(ctx context.Context, in *pgc_channel_game.DelChannelCurrentGameInfoReq, opts ...grpc.CallOption) (*pgc_channel_game.DelChannelCurrentGameInfoResp,error)
	GetChannelCurrentGameInfo(ctx context.Context, req *pgc_channel_game.GetChannelCurrentGameInfoReq, opts ...grpc.CallOption) (*pgc_channel_game.GetChannelCurrentGameInfoResp,error)
	GetChannelGameInfo(ctx context.Context, req *pgc_channel_game.GetChannelGameInfoReq, opts ...grpc.CallOption) (*pgc_channel_game.GetChannelGameInfoResp,error)
	GetGameList(ctx context.Context, req *pgc_channel_game.GetGameListReq, opts ...grpc.CallOption) (*pgc_channel_game.GetGameListResp,error)
	SetChannelCurrentGameInfo(ctx context.Context, req *pgc_channel_game.SetChannelCurrentGameInfoReq, opts ...grpc.CallOption) (*pgc_channel_game.SetChannelCurrentGameInfoResp,error)
	SetGamePhase(ctx context.Context, req *pgc_channel_game.SetGamePhaseReq, opts ...grpc.CallOption) (*pgc_channel_game.SetGamePhaseResp,error)
	SetNextBombUser(ctx context.Context, req *pgc_channel_game.SetNextBombUserReq, opts ...grpc.CallOption) (*pgc_channel_game.SetNextBombUserResp,error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
