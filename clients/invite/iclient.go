package invite

import (
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/invite"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	RecordInviteFromChannel(ctx context.Context, in *pb.RecordInviteFromChannelReq) (*pb.RecordInviteFromChannelResp, error)
	GetInviteFromChannelList(ctx context.Context, in *pb.GetInviteFromChannelListReq) (*pb.GetInviteFromChannelListResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
