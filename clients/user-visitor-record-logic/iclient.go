package user_visitor_record_logic

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/app/user-visitor-record"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	ReportUserVisitorRecord(ctx context.Context, in *pb.ReportUserVisitorRecordReq) (*pb.ReportUserVisitorRecordResp, protocol.ServerError)
	GetUserVisitorRecordList(ctx context.Context, in *pb.GetUserVisitorRecordListReq) (*pb.GetUserVisitorRecordListResp, protocol.ServerError)
	GetUserBeVisitorRecordList(ctx context.Context, in *pb.GetUserBeVisitorRecordListReq) (*pb.GetUserBeVisitorRecordListResp, protocol.ServerError)
	GetUserBeVisitorRecordCount(ctx context.Context, in *pb.GetUserBeVisitorRecordCountReq) (*pb.GetUserBeVisitorRecordCountResp, protocol.ServerError)
	GetAllTaskStatus(ctx context.Context, in *pb.GetAllTaskStatusReq) (*pb.GetAllTaskStatusResp, protocol.ServerError)
	SetShowUserBeVisitorRecordCount(ctx context.Context, in *pb.SetShowUserBeVisitorRecordCountReq) (*pb.SetShowUserBeVisitorRecordCountResp, protocol.ServerError)
	GetShowUserBeVisitorRecordCount(ctx context.Context, in *pb.GetShowUserBeVisitorRecordCountReq) (*pb.GetShowUserBeVisitorRecordCountResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
