package probgamecenter

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/services/probgamecenter"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

/*func TestGetHelloWorld(t *testing.T) {

	<PERSON><PERSON>("GetProbGameCenter", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.ProbGameCenterReq
		resp, err := client.GetProbGameCenter(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetProbGameCenter %+v", resp)
	})

}*/
