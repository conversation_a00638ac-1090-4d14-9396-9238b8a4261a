package push_funnel

import (
	"context"
	"time"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/push-notification/v2"
	"google.golang.org/grpc"
)

const (
	serviceName      = "push-v2-funnel"
	channelPushScene = "channel-push"
	userPushScene    = "user-push"
	appBroadcast     = "app-broadcast"
	channelBroadcast = "channel-broadcast"
	groupPush        = "group-push"
	agentChannel     = "agent"
	proxyChannel     = "proxy"
	castChannel      = "cast"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) *Client {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewPushFunnelClient(cc)
		}, dopts...),
	}
}

func NewClient(dopts ...grpc.DialOption) *Client {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typeStub() pb.PushFunnelClient {
	return c.Stub().(pb.PushFunnelClient)
}

// GetChannelPushOverAgentFunnel 基于push-agent的通道的房间推送漏斗
func (c *Client) GetChannelPushOverAgentFunnel(ctx context.Context, day time.Time, pushLabelList []string) ([]*pb.FunnelStep, protocol.ServerError) {
	resp, err := c.typeStub().QueryFunnelData(ctx, &pb.QueryFunnelDataReq{
		PushLabelList: pushLabelList,
		SceneType:     channelPushScene,
		PushChannel:   agentChannel,
		UnixTime:      day.Unix(),
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.AgentChannel, nil
}

// GetChannelPushOverProxyFunnel 基于push-proxy的通道的房间推送漏斗
func (c *Client) GetChannelPushOverProxyFunnel(ctx context.Context, day time.Time, pushLabelList []string) ([]*pb.FunnelStep, protocol.ServerError) {
	resp, err := c.typeStub().QueryFunnelData(ctx, &pb.QueryFunnelDataReq{
		PushLabelList: pushLabelList,
		SceneType:     channelPushScene,
		PushChannel:   proxyChannel,
		UnixTime:      day.Unix(),
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.ProxyChannel, nil
}

// GetUserPushOverAgentFunnel 基于push-agent的通道的用户推送漏斗
func (c *Client) GetUserPushOverAgentFunnel(ctx context.Context, day time.Time, pushLabelList []string) ([]*pb.FunnelStep, protocol.ServerError) {
	resp, err := c.typeStub().QueryFunnelData(ctx, &pb.QueryFunnelDataReq{
		PushLabelList: pushLabelList,
		SceneType:     userPushScene,
		PushChannel:   agentChannel,
		UnixTime:      day.Unix(),
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.AgentChannel, nil
}

// GetAppBroadcastOverAgentFunnel 基于push-agent的通道的全服广播推送漏斗
func (c *Client) GetAppBroadcastOverAgentFunnel(ctx context.Context, day time.Time, pushLabelList []string) ([]*pb.FunnelStep, protocol.ServerError) {
	resp, err := c.typeStub().QueryFunnelData(ctx, &pb.QueryFunnelDataReq{
		PushLabelList: pushLabelList,
		SceneType:     appBroadcast,
		PushChannel:   agentChannel,
		UnixTime:      day.Unix(),
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.AgentChannel, nil
}

// GetChannelBroadcastOverAgentFunnel 基于push-agent的通道的全频广播推送漏斗
func (c *Client) GetChannelBroadcastOverAgentFunnel(ctx context.Context, day time.Time, pushLabelList []string) ([]*pb.FunnelStep, protocol.ServerError) {
	resp, err := c.typeStub().QueryFunnelData(ctx, &pb.QueryFunnelDataReq{
		PushLabelList: pushLabelList,
		SceneType:     channelBroadcast,
		PushChannel:   agentChannel,
		UnixTime:      day.Unix(),
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.AgentChannel, nil
}

// GetGroupPushOverAgentFunnel 基于push-agent的通道的群组推送漏斗
func (c *Client) GetGroupPushOverAgentFunnel(ctx context.Context, day time.Time, pushLabelList []string) ([]*pb.FunnelStep, protocol.ServerError) {
	resp, err := c.typeStub().QueryFunnelData(ctx, &pb.QueryFunnelDataReq{
		PushLabelList: pushLabelList,
		SceneType:     groupPush,
		PushChannel:   agentChannel,
		UnixTime:      day.Unix(),
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.AgentChannel, nil
}

// GetChannelPushOverCastFunnel 基于push-cast的通道的房间推送漏斗
func (c *Client) GetChannelPushOverCastFunnel(ctx context.Context, day time.Time, pushLabelList []string) ([]*pb.FunnelStep, protocol.ServerError) {
	resp, err := c.typeStub().QueryFunnelData(ctx, &pb.QueryFunnelDataReq{
		PushLabelList: pushLabelList,
		SceneType:     channelPushScene,
		PushChannel:   castChannel,
		UnixTime:      day.Unix(),
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.CastChannel, nil
}

// GetUserPushOverCastFunnel 基于push-cast的通道的用户推送漏斗
func (c *Client) GetUserPushOverCastFunnel(ctx context.Context, day time.Time, pushLabelList []string) ([]*pb.FunnelStep, protocol.ServerError) {
	resp, err := c.typeStub().QueryFunnelData(ctx, &pb.QueryFunnelDataReq{
		PushLabelList: pushLabelList,
		SceneType:     userPushScene,
		PushChannel:   castChannel,
		UnixTime:      day.Unix(),
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.CastChannel, nil
}

// GetAppBroadcastOverCastFunnel 基于push-cast的通道的全服广播推送漏斗
func (c *Client) GetAppBroadcastOverCastFunnel(ctx context.Context, day time.Time, pushLabelList []string) ([]*pb.FunnelStep, protocol.ServerError) {
	resp, err := c.typeStub().QueryFunnelData(ctx, &pb.QueryFunnelDataReq{
		PushLabelList: pushLabelList,
		SceneType:     appBroadcast,
		PushChannel:   castChannel,
		UnixTime:      day.Unix(),
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.CastChannel, nil
}

// GetChannelBroadcastOverCastFunnel 基于push-cast的通道的全频广播推送漏斗
func (c *Client) GetChannelBroadcastOverCastFunnel(ctx context.Context, day time.Time, pushLabelList []string) ([]*pb.FunnelStep, protocol.ServerError) {
	resp, err := c.typeStub().QueryFunnelData(ctx, &pb.QueryFunnelDataReq{
		PushLabelList: pushLabelList,
		SceneType:     channelBroadcast,
		PushChannel:   castChannel,
		UnixTime:      day.Unix(),
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.CastChannel, nil
}
