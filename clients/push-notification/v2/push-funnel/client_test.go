package push_funnel

import (
	"context"
	"fmt"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
)

func TestClient_GetChannelPushOverAgentFunnel(t *testing.T) {
	Convey("TestClient_GetChannelPushOverAgentFunnel", t, func() {
		client := NewClient()
		resp, err := client.GetChannelPushOverAgentFunnel(context.Background(), time.Now(), []uint32{0})
		So(err, ShouldBeNil)
		for i := range resp {
			fmt.Println(resp[i].Stage, ":", resp[i].Count)
		}
	})
}

func TestClient_GetChannelPushOverProxyFunnel(t *testing.T) {
	Convey("TestClient_GetChannelPushOverProxyFunnel", t, func() {
		client := NewClient()
		resp, err := client.GetChannelPushOverProxyFunnel(context.Background(), time.Now(), []uint32{0})
		So(err, ShouldBeNil)
		for i := range resp {
			fmt.Println(resp[i].Stage, ":", resp[i].Count)
		}
	})
}

func TestClient_GetUserPushOverAgentFunnel(t *testing.T) {
	Convey("TestClient_GetUserPushOverAgentFunnel", t, func() {
		client := NewClient()
		resp, err := client.GetUserPushOverAgentFunnel(context.Background(), time.Now(), []uint32{0})
		So(err, ShouldBeNil)
		for i := range resp {
			fmt.Println(resp[i].Stage, ":", resp[i].Count)
		}
	})
}

func TestClient_GetGroupPushOverAgentFunnel(t *testing.T) {
	Convey("TestClient_GetGroupPushOverAgentFunnel", t, func() {
		client := NewClient()
		resp, err := client.GetGroupPushOverAgentFunnel(context.Background(), time.Now(), []uint32{0})
		So(err, ShouldBeNil)
		for i := range resp {
			fmt.Println(resp[i].Stage, ":", resp[i].Count)
		}
	})
}

func TestClient_GetAppBroadcastOverAgentFunnel(t *testing.T) {
	Convey("TestClient_GetAppBroadcastOverAgentFunnel", t, func() {
		client := NewClient()
		resp, err := client.GetAppBroadcastOverAgentFunnel(context.Background(), time.Now(), []uint32{0})
		So(err, ShouldBeNil)
		for i := range resp {
			fmt.Println(resp[i].Stage, ":", resp[i].Count)
		}
	})
}

func GetChannelBroadcastOverAgentFunnel(t *testing.T) {
	Convey("GetChannelBroadcastOverAgentFunnel", t, func() {
		client := NewClient()
		resp, err := client.GetChannelBroadcastOverAgentFunnel(context.Background(), time.Now(), []uint32{0})
		So(err, ShouldBeNil)
		for i := range resp {
			fmt.Println(resp[i].Stage, ":", resp[i].Count)
		}
	})
}
