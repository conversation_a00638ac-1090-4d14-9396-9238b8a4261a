package apns

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/push-notification/v2/APNs"
)

const (
	tokenServiceName = "token.apns"
)

type TokenV2Client struct {
	client.BaseClient
}

func (c *TokenV2Client) typedStub() pb.ApnsTokenClient { return c.Stub().(pb.ApnsTokenClient) }

func NewTokenV2Client(dopts ...grpc.DialOption) *TokenV2Client {
	return &TokenV2Client{
		BaseClient: client.NewInsecureGRPCClient(
			tokenServiceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewApnsTokenClient(cc)
			}, dopts...,
		),
	}
}

// QueryUserDeviceTokens call by apns-push
func (c *TokenV2Client) QueryUserDeviceTokens(ctx context.Context, uidList, appIDList []uint32) ([]*pb.UserDeviceToken, protocol.ServerError) {
	r, err := c.typedStub().QueryDeviceTokens(ctx, &pb.QueryDeviceTokensReq{
		AppIdList: appIDList,
		UidList:   uidList,
	})
	return r.GetUserDeviceTokenList(), protocol.ToServerError(err)
}

func (c *TokenV2Client) RegisterUserDeviceToken(ctx context.Context,
	token *pb.UserDeviceToken) (res *pb.RegisterDeviceTokenResp, err error) {
	res, err = c.typedStub().RegisterDeviceToken(ctx, token)
	return
}

func (c *TokenV2Client) RevokeUserDeviceToken(ctx context.Context, userID uint32, token string, timestamp uint32) error {
	_, err := c.typedStub().UnregisterDeviceToken(ctx, &pb.UnregisterDeviceTokenReq{
		Token:     token,
		Timestamp: timestamp,
		Uid:       userID,
	})

	return err
}