package apns

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/push-notification/v2/APNs"
)

const (
	pushServiceName = "push.apns"
)

// PushClient ...
type PushClient struct {
	client.BaseClient
}

func (c *PushClient) typedStub() pb.ApnsPushClient { return c.Stub().(pb.ApnsPushClient) }

func NewPushClient(dopts ...grpc.DialOption) *PushClient {
	return &PushClient{
		BaseClient: client.NewInsecureGRPCClient(
			pushServiceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewApnsPushClient(cc)
			}, dopts...,
		),
	}
}

// PushNotification ...
func (c *PushClient) PushNotification(ctx context.Context, notification *pb.SinglecastNotification) protocol.ServerError {
	_, err := c.typedStub().PushNotification(ctx, notification)
	return protocol.ToServerError(err)
}

// Multicast ...
func (c *PushClient) Multicast(ctx context.Context, notification *pb.MulticastNotification) protocol.ServerError {
	_, err := c.typedStub().PushMulticastNotification(ctx, notification)
	return protocol.ToServerError(err)
}

// CleanMulticastAccounts call by push-logic
func (c *PushClient) CleanMulticastAccounts(ctx context.Context, uidList []uint32) protocol.ServerError {
	_, err := c.typedStub().UpdateMulticastRelation(ctx, &pb.UpdateMulticastRelationReq{
		Cmd:     uint32(pb.UpdateMulticastRelationReq_CMD_Clean),
		UidList: uidList,
	})
	return protocol.ToServerError(err)
}

// SubscribeMulticastAccounts call by push-logic
func (c *PushClient) SubscribeMulticastAccounts(ctx context.Context, uidList []uint32, accountList []string) protocol.ServerError {
	_, err := c.typedStub().UpdateMulticastRelation(ctx, &pb.UpdateMulticastRelationReq{
		Cmd:         uint32(pb.UpdateMulticastRelationReq_CMD_Register),
		UidList:     uidList,
		AccountList: accountList,
	})
	return protocol.ToServerError(err)

}

func (c *PushClient) PushDirectiveNotification(ctx context.Context, req *pb.DirectiveNotification) protocol.ServerError {

	_, err := c.typedStub().PushDirectiveNotification(ctx, req)
	return protocol.ToServerError(err)

}

