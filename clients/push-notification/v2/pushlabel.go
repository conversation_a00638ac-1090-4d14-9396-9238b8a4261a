package PushNotification

type PushLabelType = string

// 按业务场景添加不同push label类型，请务必加上场景描述的相关注释，按不同场景区分，避免与appsvr中的src\quicksilver\push-notification\v2\client\cpp\pushlabel.h使用重复名称
// 注意：房间内的推送请对接channel-msg-express服务，不在这里添加类型
const (
	//	LabelAntispamNotify  PushLabelType = "AntispamNotify"  // 内审通知
	LabelPeerMessageRead             PushLabelType = "PeerMessageRead"             // 对方IM消息已读
	LabelChannelFollowUpdate         PushLabelType = "ChannelFollowUpdate"         // 跟随状态变化
	LabelFriendOnlineUpdate          PushLabelType = "FriendOnlineUpdate"          // 好友在线状态变化
	LabelCommPopUpWindow             PushLabelType = "LabelCommPopUpWindow"        // 统一弹窗推送
	LabelTenantEnterChannel          PushLabelType = "TenantEnterChannel"          // 房客进房
	LabelKickOutClient               PushLabelType = "KickOutClient"               // 踢下线
	LabelUdeskApi                    PushLabelType = "UdeskApi"                    // 推送用户udesk客服消息
	LabelSakuraBot                   PushLabelType = "SakuraBot"                   // 樱桃小T
	LabelAvatarNotify                PushLabelType = "AvatarNotify"                // 头像变更通知
	LabelSendInvite                  PushLabelType = "SendInvite"                  // 发送邀请
	LabelReplyInvite                 PushLabelType = "ReplyInvite"                 // 回复邀请
	LabelInvitePlayer                PushLabelType = "InvitePlayer"                // 邀请玩伴
	LabelRecentPlayed                PushLabelType = "RecentPlayed"                // 最近玩过
	LabelCancelGroupMsg              PushLabelType = "CancelGroupMsg"              // 群消息撤回
	LabelUploadAttachment            PushLabelType = "UploadAttachment"            // 上传附件
	LabelNotifyUserIm                PushLabelType = "NotifyUserIm"                // IM通知用户
	LabelNotifyGroupIm               PushLabelType = "NotifyGroupIm"               // IM通知群组
	LabelCancel1v1Msg                PushLabelType = "Cancel1v1Msg"                // 1v1 消息撤回
	LabelDelMsg                      PushLabelType = "DelMsg"                      // 删除消息
	LabelFollowUser                  PushLabelType = "FollowUser"                  // 关注用户
	LabelFriendshipSync              PushLabelType = "FriendshipSync"              // 好友关系同步
	LabelChannelConvene              PushLabelType = "ChannelConvene"              // 房间召集
	LabelChannelLevelExpChange       PushLabelType = "ChannelLevelExpChange"       // 房间的等级经验改变
	LabelMeleeChannelWhiteListChange PushLabelType = "MeleeChannelWhiteListChange" // 团战房进房白名单变更
	LabelKnightOpenMsgIm             PushLabelType = "KnightOpenMsgIm"             // 骑士开通TT助手消息
)
