package push_cast

import (
	"context"
	"time"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/push-notification/v2"
	"google.golang.org/grpc"
)

const (
	serviceName = "push-v2-cast"
)

var (
	DefaultStrategy = &pb.Strategy{
		UserId:       &pb.Strategy_AllUser{AllUser: true},
		TerminalType: &pb.Strategy_AllTerminal{AllTerminal: true},
	}
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewPushCastClient(cc)
		}, dopts...),
	}, nil
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typeStub() pb.PushCastClient {
	return c.Stub().(pb.PushCastClient)
}

func (c *Client) ChannelCast(ctx context.Context, task string, strategy *pb.Strategy, important bool,
	channelId, sequence uint32, notification *pb.ProxyNotification, pushType, appId, ttlSec uint32) protocol.ServerError {
	if notification == nil {
		log.ErrorWithCtx(ctx, "invalid notify")
		return nil
	}
	if strategy == nil {
		strategy = DefaultStrategy
	}
	now := time.Now().UnixNano() / 1e6
	expireAt := uint32(0)
	if ttlSec > 0 {
		expireAt = ttlSec + uint32(now/1e3)
	}
	_, err := c.typeStub().ChannelCast(ctx, &pb.ChannelCastReq{
		TaskId:    task,
		Important: important,
		Strategy:  strategy,
		ClientMs:  now,
		ExpireAt:  expireAt,
		Sequence:  sequence,
		ChannelId: channelId,
		Notify:    notification,
		PushType:  pushType,
		AppId:     appId,
	})
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}

func (c *Client) UniversalCast(ctx context.Context, task string, strategy *pb.Strategy, important bool,
	universalId, sequence uint32, notification *pb.ProxyNotification, pushType, appId, ttlSec uint32) protocol.ServerError {
	if notification == nil {
		log.ErrorWithCtx(ctx, "invalid notify")
		return nil
	}
	if strategy == nil || strategy.UserId == nil {
		log.ErrorWithCtx(ctx, "invalid strategy")
		return nil
	}
	target, ok := strategy.UserId.(*pb.Strategy_WhiteUser)
	if !ok || len(target.WhiteUser.List) == 0 {
		log.ErrorWithCtx(ctx, "invalid strategy")
		return nil
	}
	now := time.Now().UnixNano() / 1e6
	expireAt := uint32(0)
	if ttlSec > 0 {
		expireAt = ttlSec + uint32(now/1e3)
	}
	_, err := c.typeStub().UniversalCast(ctx, &pb.UniversalCastReq{
		TaskId:      task,
		Important:   important,
		Strategy:    strategy,
		ClientMs:    now,
		ExpireAt:    expireAt,
		Sequence:    sequence,
		UniversalId: universalId,
		Notify:      notification,
		PushType:    pushType,
		AppId:       appId,
	})
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}

func (c *Client) UniversalCastWithPresInfo(ctx context.Context, task string, presInfo map[uint64]*pb.UniversalCastReq_PresIdList, important bool,
	universalId, sequence uint32, notification *pb.ProxyNotification, pushType, appId, ttlSec uint32) protocol.ServerError {
	if notification == nil {
		log.ErrorWithCtx(ctx, "invalid notify")
		return nil
	}
	now := time.Now().UnixNano() / 1e6
	expireAt := uint32(0)
	if ttlSec > 0 {
		expireAt = ttlSec + uint32(now/1e3)
	}
	_, err := c.typeStub().UniversalCast(ctx, &pb.UniversalCastReq{
		TaskId:      task,
		Important:   important,
		Strategy:    DefaultStrategy,
		ClientMs:    now,
		Sequence:    sequence,
		ExpireAt:    expireAt,
		UniversalId: universalId,
		Notify:      notification,
		PushType:    pushType,
		PresInfo:    presInfo,
		AppId:       appId,
	})
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}

func (c *Client) BroadCast(ctx context.Context, task string, strategy *pb.Strategy, important bool,
	broadcastId, sequence uint32, notification *pb.ProxyNotification, pushType, appId, ttlSec uint32) protocol.ServerError {
	if notification == nil {
		log.ErrorWithCtx(ctx, "invalid notify")
		return nil
	}
	if strategy == nil {
		strategy = DefaultStrategy
	}
	now := time.Now().UnixNano() / 1e6
	expireAt := uint32(0)
	if ttlSec > 0 {
		expireAt = ttlSec + uint32(now/1e3)
	}
	_, err := c.typeStub().BroadCast(ctx, &pb.BroadCastReq{
		TaskId:      task,
		Important:   important,
		Strategy:    strategy,
		ClientMs:    now,
		ExpireAt:    expireAt,
		Sequence:    sequence,
		BroadcastId: broadcastId,
		Notify:      notification,
		PushType:    pushType,
		AppId:       appId,
	})
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}
