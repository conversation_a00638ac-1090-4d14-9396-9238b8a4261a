// Code generated by MockGen. DO NOT EDIT.
// Source: ./iclient.go

// Package PushNotification is a generated GoMock package.
package PushNotification

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	push "golang.52tt.com/protocol/app/push"
	push_notification "golang.52tt.com/protocol/services/push-notification/v2"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchPushRelationRegisterEvents mocks base method.
func (m *MockIClient) BatchPushRelationRegisterEvents(ctx context.Context, events []*push_notification.RelationRegisterEvent, opts ...grpc.CallOption) (map[uint32]*push_notification.PushRelationEventResult, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, events}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchPushRelationRegisterEvents", varargs...)
	ret0, _ := ret[0].(map[uint32]*push_notification.PushRelationEventResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchPushRelationRegisterEvents indicates an expected call of BatchPushRelationRegisterEvents.
func (mr *MockIClientMockRecorder) BatchPushRelationRegisterEvents(ctx, events interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, events}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchPushRelationRegisterEvents", reflect.TypeOf((*MockIClient)(nil).BatchPushRelationRegisterEvents), varargs...)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetReliableProxyNotifications mocks base method.
func (m *MockIClient) GetReliableProxyNotifications(ctx context.Context, uid, seqBegin, seqEnd, count uint32) ([]*push_notification.ReliableProxyNotification, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReliableProxyNotifications", ctx, uid, seqBegin, seqEnd, count)
	ret0, _ := ret[0].([]*push_notification.ReliableProxyNotification)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetReliableProxyNotifications indicates an expected call of GetReliableProxyNotifications.
func (mr *MockIClientMockRecorder) GetReliableProxyNotifications(ctx, uid, seqBegin, seqEnd, count interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReliableProxyNotifications", reflect.TypeOf((*MockIClient)(nil).GetReliableProxyNotifications), ctx, uid, seqBegin, seqEnd, count)
}

// HandlePushMsg mocks base method.
func (m *MockIClient) HandlePushMsg(uid uint32, uidList []uint32, cmdType push.PushMessage_CMD_TYPE, pushInfo []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlePushMsg", uid, uidList, cmdType, pushInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandlePushMsg indicates an expected call of HandlePushMsg.
func (mr *MockIClientMockRecorder) HandlePushMsg(uid, uidList, cmdType, pushInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlePushMsg", reflect.TypeOf((*MockIClient)(nil).HandlePushMsg), uid, uidList, cmdType, pushInfo)
}

// HandlePushMsgV2 mocks base method.
func (m *MockIClient) HandlePushMsgV2(ctx context.Context, uid uint32, uidList []uint32, cmdType push.PushMessage_CMD_TYPE, pushInfo []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlePushMsgV2", ctx, uid, uidList, cmdType, pushInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandlePushMsgV2 indicates an expected call of HandlePushMsgV2.
func (mr *MockIClientMockRecorder) HandlePushMsgV2(ctx, uid, uidList, cmdType, pushInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlePushMsgV2", reflect.TypeOf((*MockIClient)(nil).HandlePushMsgV2), ctx, uid, uidList, cmdType, pushInfo)
}

// HandlePushMsgWithLabel mocks base method.
func (m *MockIClient) HandlePushMsgWithLabel(uid uint32, uidList []uint32, cmdType push.PushMessage_CMD_TYPE, pushInfo []byte, pushLable string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlePushMsgWithLabel", uid, uidList, cmdType, pushInfo, pushLable)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandlePushMsgWithLabel indicates an expected call of HandlePushMsgWithLabel.
func (mr *MockIClientMockRecorder) HandlePushMsgWithLabel(uid, uidList, cmdType, pushInfo, pushLable interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlePushMsgWithLabel", reflect.TypeOf((*MockIClient)(nil).HandlePushMsgWithLabel), uid, uidList, cmdType, pushInfo, pushLable)
}

// HandlePushMsgWithLabelV2 mocks base method.
func (m *MockIClient) HandlePushMsgWithLabelV2(ctx context.Context, uid uint32, uidList []uint32, cmdType push.PushMessage_CMD_TYPE, pushInfo []byte, pushLabel string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlePushMsgWithLabelV2", ctx, uid, uidList, cmdType, pushInfo, pushLabel)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandlePushMsgWithLabelV2 indicates an expected call of HandlePushMsgWithLabelV2.
func (mr *MockIClientMockRecorder) HandlePushMsgWithLabelV2(ctx, uid, uidList, cmdType, pushInfo, pushLabel interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlePushMsgWithLabelV2", reflect.TypeOf((*MockIClient)(nil).HandlePushMsgWithLabelV2), ctx, uid, uidList, cmdType, pushInfo, pushLabel)
}

// MessageReceivedAck mocks base method.
func (m *MockIClient) MessageReceivedAck(ctx context.Context, uid uint32, seqList []uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MessageReceivedAck", ctx, uid, seqList)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// MessageReceivedAck indicates an expected call of MessageReceivedAck.
func (mr *MockIClientMockRecorder) MessageReceivedAck(ctx, uid, seqList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MessageReceivedAck", reflect.TypeOf((*MockIClient)(nil).MessageReceivedAck), ctx, uid, seqList)
}

// NewPushMulticastStream mocks base method.
func (m *MockIClient) NewPushMulticastStream(ctx context.Context, block bool) (push_notification.PushNotification_PushMulticastClient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewPushMulticastStream", ctx, block)
	ret0, _ := ret[0].(push_notification.PushNotification_PushMulticastClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewPushMulticastStream indicates an expected call of NewPushMulticastStream.
func (mr *MockIClientMockRecorder) NewPushMulticastStream(ctx, block interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewPushMulticastStream", reflect.TypeOf((*MockIClient)(nil).NewPushMulticastStream), ctx, block)
}

// NewPushToUsersStream mocks base method.
func (m *MockIClient) NewPushToUsersStream(ctx context.Context, block bool) (push_notification.PushNotification_PushToUsersClient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewPushToUsersStream", ctx, block)
	ret0, _ := ret[0].(push_notification.PushNotification_PushToUsersClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewPushToUsersStream indicates an expected call of NewPushToUsersStream.
func (mr *MockIClientMockRecorder) NewPushToUsersStream(ctx, block interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewPushToUsersStream", reflect.TypeOf((*MockIClient)(nil).NewPushToUsersStream), ctx, block)
}

// PushMulticast mocks base method.
func (m *MockIClient) PushMulticast(ctx context.Context, multicastID uint64, multicastAccount string, skipUserIDList []uint32, notification *push_notification.CompositiveNotification) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushMulticast", ctx, multicastID, multicastAccount, skipUserIDList, notification)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// PushMulticast indicates an expected call of PushMulticast.
func (mr *MockIClientMockRecorder) PushMulticast(ctx, multicastID, multicastAccount, skipUserIDList, notification interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushMulticast", reflect.TypeOf((*MockIClient)(nil).PushMulticast), ctx, multicastID, multicastAccount, skipUserIDList, notification)
}

// PushMulticastSync mocks base method.
func (m *MockIClient) PushMulticastSync(ctx context.Context, multicastID uint64, multicastAccount string, skipUserIDList []uint32, notification *push_notification.CompositiveNotification) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushMulticastSync", ctx, multicastID, multicastAccount, skipUserIDList, notification)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// PushMulticastSync indicates an expected call of PushMulticastSync.
func (mr *MockIClientMockRecorder) PushMulticastSync(ctx, multicastID, multicastAccount, skipUserIDList, notification interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushMulticastSync", reflect.TypeOf((*MockIClient)(nil).PushMulticastSync), ctx, multicastID, multicastAccount, skipUserIDList, notification)
}

// PushMulticasts mocks base method.
func (m *MockIClient) PushMulticasts(ctx context.Context, multicastMap map[uint64]string, skipUserIDList []uint32, notification *push_notification.CompositiveNotification) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushMulticasts", ctx, multicastMap, skipUserIDList, notification)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// PushMulticasts indicates an expected call of PushMulticasts.
func (mr *MockIClientMockRecorder) PushMulticasts(ctx, multicastMap, skipUserIDList, notification interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushMulticasts", reflect.TypeOf((*MockIClient)(nil).PushMulticasts), ctx, multicastMap, skipUserIDList, notification)
}

// PushMulticasts2 mocks base method.
func (m *MockIClient) PushMulticasts2(ctx context.Context, multicastMap map[uint64]string, skipUserIDList []uint32, notification *push_notification.CompositiveNotification) (*push_notification.PushMulticastResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushMulticasts2", ctx, multicastMap, skipUserIDList, notification)
	ret0, _ := ret[0].(*push_notification.PushMulticastResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// PushMulticasts2 indicates an expected call of PushMulticasts2.
func (mr *MockIClientMockRecorder) PushMulticasts2(ctx, multicastMap, skipUserIDList, notification interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushMulticasts2", reflect.TypeOf((*MockIClient)(nil).PushMulticasts2), ctx, multicastMap, skipUserIDList, notification)
}

// PushRelationRegisterEvent mocks base method.
func (m *MockIClient) PushRelationRegisterEvent(ctx context.Context, event *push_notification.RelationRegisterEvent, opts ...grpc.CallOption) (*push_notification.PushRelationEventResult, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, event}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PushRelationRegisterEvent", varargs...)
	ret0, _ := ret[0].(*push_notification.PushRelationEventResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PushRelationRegisterEvent indicates an expected call of PushRelationRegisterEvent.
func (mr *MockIClientMockRecorder) PushRelationRegisterEvent(ctx, event interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, event}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushRelationRegisterEvent", reflect.TypeOf((*MockIClient)(nil).PushRelationRegisterEvent), varargs...)
}

// PushToPresenceList mocks base method.
func (m *MockIClient) PushToPresenceList(ctx context.Context, presenceList []*push_notification.Presence, notification *push_notification.CompositiveNotification) (*push_notification.PushToUsersResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushToPresenceList", ctx, presenceList, notification)
	ret0, _ := ret[0].(*push_notification.PushToUsersResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// PushToPresenceList indicates an expected call of PushToPresenceList.
func (mr *MockIClientMockRecorder) PushToPresenceList(ctx, presenceList, notification interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushToPresenceList", reflect.TypeOf((*MockIClient)(nil).PushToPresenceList), ctx, presenceList, notification)
}

// PushToUserMap mocks base method.
func (m *MockIClient) PushToUserMap(ctx context.Context, uidMap map[uint32]*push_notification.CompositiveNotification) (*push_notification.PushToUserMapResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushToUserMap", ctx, uidMap)
	ret0, _ := ret[0].(*push_notification.PushToUserMapResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PushToUserMap indicates an expected call of PushToUserMap.
func (mr *MockIClientMockRecorder) PushToUserMap(ctx, uidMap interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushToUserMap", reflect.TypeOf((*MockIClient)(nil).PushToUserMap), ctx, uidMap)
}

// PushToUsers mocks base method.
func (m *MockIClient) PushToUsers(ctx context.Context, userIDList []uint32, notification *push_notification.CompositiveNotification) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushToUsers", ctx, userIDList, notification)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushToUsers indicates an expected call of PushToUsers.
func (mr *MockIClientMockRecorder) PushToUsers(ctx, userIDList, notification interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushToUsers", reflect.TypeOf((*MockIClient)(nil).PushToUsers), ctx, userIDList, notification)
}

// PushToUsers2 mocks base method.
func (m *MockIClient) PushToUsers2(ctx context.Context, userIDList []uint32, notification *push_notification.CompositiveNotification) (*push_notification.PushToUsersResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushToUsers2", ctx, userIDList, notification)
	ret0, _ := ret[0].(*push_notification.PushToUsersResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// PushToUsers2 indicates an expected call of PushToUsers2.
func (mr *MockIClientMockRecorder) PushToUsers2(ctx, userIDList, notification interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushToUsers2", reflect.TypeOf((*MockIClient)(nil).PushToUsers2), ctx, userIDList, notification)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UnaryPushMulticastWithReqId mocks base method.
func (m *MockIClient) UnaryPushMulticastWithReqId(ctx context.Context, in *push_notification.PushMulticastReq) (string, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnaryPushMulticastWithReqId", ctx, in)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UnaryPushMulticastWithReqId indicates an expected call of UnaryPushMulticastWithReqId.
func (mr *MockIClientMockRecorder) UnaryPushMulticastWithReqId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnaryPushMulticastWithReqId", reflect.TypeOf((*MockIClient)(nil).UnaryPushMulticastWithReqId), ctx, in)
}

// UnaryPushToPresenceListWithReqId mocks base method.
func (m *MockIClient) UnaryPushToPresenceListWithReqId(ctx context.Context, in *push_notification.PushToPresenceListReq) (string, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnaryPushToPresenceListWithReqId", ctx, in)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UnaryPushToPresenceListWithReqId indicates an expected call of UnaryPushToPresenceListWithReqId.
func (mr *MockIClientMockRecorder) UnaryPushToPresenceListWithReqId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnaryPushToPresenceListWithReqId", reflect.TypeOf((*MockIClient)(nil).UnaryPushToPresenceListWithReqId), ctx, in)
}

// UnaryPushToUserMapWithReqId mocks base method.
func (m *MockIClient) UnaryPushToUserMapWithReqId(ctx context.Context, in *push_notification.PushToUserMapReq) (string, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnaryPushToUserMapWithReqId", ctx, in)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UnaryPushToUserMapWithReqId indicates an expected call of UnaryPushToUserMapWithReqId.
func (mr *MockIClientMockRecorder) UnaryPushToUserMapWithReqId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnaryPushToUserMapWithReqId", reflect.TypeOf((*MockIClient)(nil).UnaryPushToUserMapWithReqId), ctx, in)
}

// UnaryPushToUsersWithReqId mocks base method.
func (m *MockIClient) UnaryPushToUsersWithReqId(ctx context.Context, in *push_notification.PushToUsersReq) (string, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnaryPushToUsersWithReqId", ctx, in)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UnaryPushToUsersWithReqId indicates an expected call of UnaryPushToUsersWithReqId.
func (mr *MockIClientMockRecorder) UnaryPushToUsersWithReqId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnaryPushToUsersWithReqId", reflect.TypeOf((*MockIClient)(nil).UnaryPushToUsersWithReqId), ctx, in)
}
