package pushV3

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/push-notification/v3"
	"google.golang.org/grpc"
)

const (
	serviceName = "push-v3"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewPushClient(cc)
		}, dopts...),
	}, nil
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.PushClient { return c.Stub().(pb.PushClient) }

func (c *Client) NewTask(ctx context.Context, in *pb.NewTaskReq) (*pb.NewTaskResp, error) {
	resp, err := c.typedStub().NewTask(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) InitPartOfTask(ctx context.Context, in *pb.InitPartOfTaskReq) (*pb.InitPartOfTaskResp, error) {
	resp, err := c.typedStub().InitPartOfTask(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SaveTask(ctx context.Context, in *pb.SaveTaskReq) (*pb.SaveTaskResp, error) {
	resp, err := c.typedStub().SaveTask(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SendPushTask(ctx context.Context, in *pb.SendPushTaskReq) (*pb.SendPushTaskResp, error) {
	resp, err := c.typedStub().SendPushTask(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddExtParams(notification *pb.Notification, pushType pb.PushType, optUser pb.OptUser) {

	notification.PushType = pushType
	notification.OptUser = optUser

	if notification.Extra == nil {
		notification.Extra = make(map[string]string)
	}
	notification.Extra["push_type"] = fmt.Sprintf("%d", pushType)
	notification.Extra["opt_user"] = fmt.Sprintf("%d", optUser)

}

func EncodeBase64(data []byte) (string, error) {
	bb := &bytes.Buffer{}

	encoder := base64.NewEncoder(base64.StdEncoding, bb)

	_, err := encoder.Write(data)
	if err != nil {
		log.Errorf("Failed to encoder.Write %+v", err)
		return "", err
	}

	err = encoder.Close()
	if err != nil {
		log.Errorf("Failed to encoder.Close %+v", err)
		return "", err
	}

	return bb.String(), nil
}
