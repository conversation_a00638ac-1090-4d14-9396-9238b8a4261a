package push_ops

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/push-notification/v3"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	SendPush(ctx context.Context, in *pb.SendPushReq) (*pb.SendPushResp, protocol.ServerError)
	BatchTargetUidList(ctx context.Context, in *pb.BatchTargetUidListReq) (*pb.BatchTargetUidListResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
