package push_ops

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/push-notification/v3"
	"google.golang.org/grpc"
)

const (
	serviceName = "push-ops"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewPushOpsClient(cc)
		}, dopts...),
	}, nil
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.PushOpsClient { return c.Stub().(pb.PushOpsClient) }

func (c *Client) SendPush(ctx context.Context, in *pb.SendPushReq) (*pb.SendPushResp, protocol.ServerError) {
	resp, err := c.typedStub().SendPush(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchTargetUidList(ctx context.Context, in *pb.BatchTargetUidListReq) (*pb.BatchTargetUidListResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchTargetUidList(ctx, in)
	return resp, protocol.ToServerError(err)
}
