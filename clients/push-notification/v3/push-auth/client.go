package push_auth

import (
	"context"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	pb "golang.52tt.com/protocol/services/push-notification/v3"
	"google.golang.org/grpc"
)

const (
	serviceName = "push-v3-auth"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewPushAuthClient(cc)
		}, dopts...),
	}, nil
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.PushAuthClient { return c.Stub().(pb.PushAuthClient) }

func (c *Client) GetAuthToken(ctx context.Context, in *pb.GetAuthTokenReq, opts ...grpc.CallOption) (*pb.GetAuthTokenResp, error) {
	return c.typedStub().GetAuthToken(ctx, in, opts...)
}

func (c *Client) DelAuthToken(ctx context.Context, in *pb.DelAuthTokenReq, opts ...grpc.CallOption) (*pb.DelAuthTokenResp, error) {
	return c.typedStub().DelAuthToken(ctx, in, opts...)
}
