package push_auth

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/push-notification/v3"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetAuthToken(ctx context.Context, in *pb.GetAuthTokenReq, opts ...grpc.CallOption) (*pb.GetAuthTokenResp, error)
	DelAuthToken(ctx context.Context, in *pb.DelAuthTokenReq, opts ...grpc.CallOption) (*pb.DelAuthTokenResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
