package push_user

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/push-notification/v3"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	BindAlias(ctx context.Context, in *pb.BindAliasReq, opts ...grpc.CallOption) (*pb.BindAliasResp, error)
	QueryAliasByCid(ctx context.Context, in *pb.QueryAliasReq, opts ...grpc.CallOption) (*pb.QueryAliasResp, error)
	QueryCidByAlias(ctx context.Context, in *pb.QueryCidReq, opts ...grpc.CallOption) (*pb.QueryCidResp, error)
	UnbindAlias(ctx context.Context, in *pb.UnbindAliasReq, opts ...grpc.CallOption) (*pb.UnbindAliasResp, error)
	RevokeAlias(ctx context.Context, in *pb.RevokeAliasReq, opts ...grpc.CallOption) (*pb.RevokeAliasResp, error)
	BindUserWithTag(ctx context.Context, in *pb.BindUserWithTagReq, opts ...grpc.CallOption) (*pb.BindUserWithTagResp, error)
	BindTagWithUser(ctx context.Context, in *pb.BindTagWithUserReq, opts ...grpc.CallOption) (*pb.BindTagWithUserResp, error)
	UnbindTagFromUser(ctx context.Context, in *pb.UnbindTagFromUserReq, opts ...grpc.CallOption) (*pb.UnbindTagFromUserResp, error)
	QueryUserTag(ctx context.Context, in *pb.QueryUserTagReq, opts ...grpc.CallOption) (*pb.QueryUserTagResp, error)
	AddBlackList(ctx context.Context, in *pb.AddBlackListReq, opts ...grpc.CallOption) (*pb.AddBlackListResp, error)
	DelBlackList(ctx context.Context, in *pb.DelBlackListReq, opts ...grpc.CallOption) (*pb.DelBlackListResp, error)
	QueryUserStatus(ctx context.Context, in *pb.QueryUserStatusReq, opts ...grpc.CallOption) (*pb.QueryUserStatusResp, error)
	QueryDeviceStatus(ctx context.Context, in *pb.QueryDeviceStatusReq, opts ...grpc.CallOption) (*pb.QueryDeviceStatusResp, error)
	QueryUserInfo(ctx context.Context, in *pb.QueryUserInfoReq, opts ...grpc.CallOption) (*pb.QueryUserInfoResp, error)
	SetPushBadge(ctx context.Context, in *pb.SetPushBadgeReq, opts ...grpc.CallOption) (*pb.SetPushBadgeResp, error)
	QueryUserCount(ctx context.Context, in *pb.QueryUserCountReq, opts ...grpc.CallOption) (*pb.QueryUserCountResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
