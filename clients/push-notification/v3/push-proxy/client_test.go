package push_proxy

import (
    "context"
    "google.golang.org/grpc"
    "math/rand"
    "os"
    "strconv"
    "sync"
    "testing"
    "time"

    . "github.com/smartystreets/goconvey/convey"
    pb "golang.52tt.com/protocol/services/push-notification/v3"
    "google.golang.org/grpc/grpclog"
)

var TestUidList = []uint32{
    2238488,
}
const (
    localTest = true
    taskId = "taskId1234"
    title = "title123"
    content = "content123"
    testUid = 2238488
)
var ops []grpc.DialOption

func init() {
    grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stderr))
    if localTest {
        ops = []grpc.DialOption{grpc.WithAuthority("push-v3-proxy.52tt.local"), grpc.WithBlock()}
    }
}

func TestClient_QueryDeviceStatusByUids(t *testing.T) {
    <PERSON>vey("TestClient_QueryDeviceStatusByUids", t, func() {
        client, err := NewClient(ops...)
        So(err, ShouldBeNil)
        resp, err := client.QueryDeviceStatusByUids(context.Background(), &pb.QueryDeviceStatusByUidsReq{
            Appid:   pb.QwAppId_TT,
            UidList: []uint32{2238488,22000,2229591},
        })
        So(err, ShouldBeNil)
        t.Log(resp)
    })
}

func TestClient_SendOpPush(t *testing.T) {
    start := time.Now()
    t.Logf("Start to test SendOpPush %v", start.Format("2006-01-02 15:04:05"))
    var wg sync.WaitGroup
    for i := 0; i < 1; i++ {
        wg.Add(1)
        go func(idx int) {
            defer wg.Done()
                Convey("TestClient_SendOpPush", t, func() {
                    client, err := NewClient(ops...)
                    So(err, ShouldBeNil)
                    for j := 0; j < 1; j++ {
                        ts := time.Now()
                        t.Logf("idx %d call SendOpPush %d time start at %v", idx, j, ts.Format("2006-01-02 15:04:05"))
                        resp, err := client.SendOpPush(context.Background(), &pb.SendOpPushReq{
                            Appid:       pb.QwAppId_TT,
                            TaskId:      "FUNNEL-taskid-" + strconv.Itoa(idx*1000+j) + "-" + strconv.FormatInt(time.Now().UnixNano(), 10),
                            UidList:     TestUidList,
                            ExpiredTime: 0,
                            Notification: &pb.Notification{
                                Title:    "testTitle" + strconv.FormatInt(time.Now().UnixNano(), 10),
                                Content:  "testContent" + strconv.Itoa(rand.Intn(100)),
                                PushType: pb.PushType_Im,
                            },
                        })
                        So(err, ShouldBeNil)
                        t.Logf("idx %d call SendOpPush finished %d time cost %v, resp %v", idx, j, time.Since(ts), resp)
                    }
                })
        }(i)
    }
    wg.Wait()
    t.Logf("TestSendOpPush finished at %v cost %v", time.Now().Format("2006-01-02 15:04:05"), time.Since(start))
}

func TestClient_SendCustomOpPush(t *testing.T) {
    Convey("TestClient_SendCustomOpPush", t, func() {
        client, err := NewClient(ops...)
        So(err, ShouldBeNil)
        resp, err := client.SendCustomOpPush(context.Background(), &pb.SendCustomOpPushReq{
            Appid:   pb.QwAppId_TT,
            TaskId: taskId,
            NotificationMap: map[uint32]*pb.Notification{
                testUid: &pb.Notification{
                    Title:    title,
                    Content:  content,
                    PushType: pb.PushType_Normal,
                },
            },
            ExpiredTime: time.Now().Unix() + 3600,
        })
        So(err, ShouldBeNil)
        t.Log(resp)
    })
}

func TestClient_SendCorePush(t *testing.T) {
    Convey("TestClient_SendCorePush", t, func() {
        client, err := NewClient(ops...)
        So(err, ShouldBeNil)
        resp, err := client.SendCorePush(context.Background(), &pb.SendCorePushReq{
            Appid:   pb.QwAppId_TT,
            TaskId: taskId,
            Notification: &pb.Notification{
                Title: title,
                Content: content,
                PushType: pb.PushType_Normal,
            },
            UidList: TestUidList,
            ExpiredTime: time.Now().Unix() + 3600,
        })
        So(err, ShouldBeNil)
        t.Log(resp)
    })
}
