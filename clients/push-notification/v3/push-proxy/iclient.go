package push_proxy

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/push-notification/v3"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	QueryDeviceStatusByUids(ctx context.Context, in *pb.QueryDeviceStatusByUidsReq) (*pb.QueryDeviceStatusByUidsResp, error)
	SendOpPush(ctx context.Context, in *pb.SendOpPushReq) (*pb.SendOpPushResp, error)
	SendCustomOpPush(ctx context.Context, in *pb.SendCustomOpPushReq) (*pb.SendCustomOpPushResp, error)
	SendCorePush(ctx context.Context, in *pb.SendCorePushReq) (*pb.SendCorePushResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
