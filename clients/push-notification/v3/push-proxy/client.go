package push_proxy

import (
    "context"
    "golang.52tt.com/pkg/client"
    grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
    "golang.52tt.com/pkg/protocol"
    pb "golang.52tt.com/protocol/services/push-notification/v3"
    "google.golang.org/grpc"
)

const (
	serviceName = "push-v3-proxy"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewPushProxyClient(cc)
		}, dopts...),
	}, nil
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.PushProxyClient { return c.Stub().(pb.PushProxyClient) }

func (c *Client) QueryDeviceStatusByUids(ctx context.Context, in *pb.QueryDeviceStatusByUidsReq) (*pb.QueryDeviceStatusByUidsResp, error) {
	resp, err := c.typedStub().QueryDeviceStatusByUids(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SendOpPush(ctx context.Context, in *pb.SendOpPushReq) (*pb.SendOpPushResp, error) {
	resp, err := c.typedStub().SendOpPush(ctx, in)
	return resp, protocol.ToServerError(err)
}
func (c *Client) SendCustomOpPush(ctx context.Context, in *pb.SendCustomOpPushReq) (*pb.SendCustomOpPushResp, error) {
	resp, err := c.typedStub().SendCustomOpPush(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SendCorePush(ctx context.Context, in *pb.SendCorePushReq) (*pb.SendCorePushResp, error) {
	resp, err := c.typedStub().SendCorePush(ctx, in)
	return resp, protocol.ToServerError(err)
}
