package rcmd_channel_label

import (
	"context"

	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	LabelSearch(ctx context.Context, in *pb.LabelSearchReq) (*pb.LabelSearchResp, error)
	GetGameLabels(ctx context.Context, in *pb.GetGameLabelsReq) (*pb.GetGameLabelsResp, error)
	CutWord(ctx context.Context, in *pb.CutWordReq) (*pb.CutWordResp, error)
	GetSearchHint(ctx context.Context, in *pb.GetSearchHintReq) (*pb.GetSearchHintResp, error)
	ConvertGameLabels(ctx context.Context, in *pb.ConvertGameLabelsReq) (*pb.ConvertGameLabelsResp, error)
	BatchHotGameLabels(ctx context.Context, in *pb.BatchHotGameLabelsReq) (*pb.BatchHotGameLabelsResp, error)
	GetPublishGameLabels(ctx context.Context, in *pb.GetPublishGameLabelsReq) (*pb.GetPublishGameLabelsResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
