/**
 * Author: Orange
 * Date: 19-5-11
 */

package channelmic

import (
	"context"
	"strconv"

	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"

	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"

	pb "golang.52tt.com/protocol/services/channelmicsvr"
)

const (
	serviceName = "channelmic"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewChannelMicClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.ChannelMicClient { return c.Stub().(pb.ChannelMicClient) }

func (c *Client) GetMicrList(ctx context.Context, channelId uint32, opUid uint32) (*pb.GetMicrListResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	r, err := c.typedStub().GetMicrList(ctx, &pb.GetMicrListReq{
		ChannelId: channelId,
		OpUid:     opUid,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return r, nil
}

func (c *Client) GetMicrListV2(ctx context.Context, channelId, opUid uint32,
	forceLocalTime bool) (*pb.GetMicrListResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	r, err := c.typedStub().GetMicrList(ctx, &pb.GetMicrListReq{
		ChannelId:      channelId,
		OpUid:          opUid,
		ForceLocalTime: forceLocalTime,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return r, nil
}

func (c *Client) SetChannelMicMode(ctx context.Context, uin uint32, in *pb.SetChannelMicModeReq) (*pb.SetChannelMicModeResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().SetChannelMicMode(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

func (c *Client) SetChannelMicSpaceStatus(ctx context.Context, uin, opuid, channelId, micId, status uint32) (*pb.SetChannelMicSpaceStatusResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	in := &pb.SetChannelMicSpaceStatusReq{
		ChannelId: channelId,
		OpUid:     opuid,
		MicInfo: &pb.MicrSpaceInfo{
			MicId:    micId,
			MicState: status,
		},
	}
	r, err := c.typedStub().SetChannelMicSpaceStatus(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

func (c *Client) BatchSetChannelMicSpaceStatus(ctx context.Context, uin, opuid, channelId, status uint32, micIdList []uint32) (*pb.BatchSetChannelMicSpaceStatusResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	in := &pb.BatchSetChannelMicSpaceStatusReq{
		ChannelId: channelId,
		OpUid:     opuid,
		MicIdList: micIdList,
		MicStatus: status,
	}
	r, err := c.typedStub().BatchSetChannelMicSpaceStatus(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

func (c *Client) SimpleHoldMicrSpace(ctx context.Context, uin uint32, in *pb.SimpleHoldMicrSpaceReq) (*pb.SimpleHoldMicrSpaceResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().SimpleHoldMicrSpace(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

func (c *Client) SimpleReleaseMicrSpace(ctx context.Context, uin uint32, in *pb.SimpleReleaseMicrSpaceReq) (*pb.SimpleReleaseMicrSpaceResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().SimpleReleaseMicrSpace(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

func (c *Client) ChangeMicrSpace(ctx context.Context, uin uint32, in *pb.ChangeMicrophoneReq) (*pb.ChangeMicrophoneResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().ChangeMicrophone(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

func (c *Client) KickoutChannelMic(ctx context.Context, uin uint32, in *pb.KickoutChannelMicReq) (*pb.KickoutChannelMicResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().KickoutChannelMic(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}
func (c *Client) GetChannelMicMode(ctx context.Context, uin uint32, in *pb.GetChannelMicModeReq) (*pb.GetChannelMicModeResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetChannelMicMode(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}
func (c *Client) BatchGetChannelMicMode(ctx context.Context, uin uint32, in *pb.BatchGetChannelMicModeReq) (*pb.BatchGetChannelMicModeResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().BatchGetChannelMicMode(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

func (c *Client) GetMicrListAndForceLocalTime(ctx context.Context, channelId uint32, opUid uint32) (*pb.GetMicrListResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	r, err := c.typedStub().GetMicrList(ctx, &pb.GetMicrListReq{
		ChannelId:      channelId,
		OpUid:          opUid,
		ForceLocalTime: true,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}
func (c *Client) GetChannelMicModeV2(ctx context.Context, uin, cid uint32) (uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetChannelMicMode(ctx, &pb.GetChannelMicModeReq{ChannelId: cid})
	if err != nil {
		return 0, protocol.ToServerError(err)
	}
	return r.GetMicMode(), nil
}

// 创建初始的麦位信息
func (c *Client) InitCreateMicrSpace(ctx context.Context, opUid uint32, req *pb.InitCreateMicrSpaceReq) (*pb.InitCreateMicrSpaceResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	r, err := c.typedStub().InitCreateMicrSpace(ctx, req)
	return r, protocol.ToServerError(err)
}

func (c *Client) FakeHoldMicrSpace(ctx context.Context, uin uint32, in *pb.FakeHoldMicrSpaceReq) (*pb.FakeHoldMicrSpaceResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().FakeHoldMicrSpace(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

// 批量获取麦位列表,一次最多获取20个房间
func (c *Client) BatGetMicrList(ctx context.Context, uin uint32, in *pb.BatGetMicrListReq) (*pb.BatGetMicrListResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().BatGetMicrList(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

func (c *Client) ReInitChannelMicData(ctx context.Context, uin uint32, in *pb.ReInitChannelMicDataReq) (*pb.ReInitChannelMicDataResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().ReInitChannelMicData(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

func (c *Client) DisableAllEmptyMicrSpace(ctx context.Context, opUid, cid uint32) (*pb.DisableAllEmptyMicrSpaceResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	r, err := c.typedStub().DisableAllEmptyMicrSpace(ctx, &pb.DisableAllEmptyMicrSpaceReq{
		ChannelId: cid,
		OpUid:     opUid,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}
