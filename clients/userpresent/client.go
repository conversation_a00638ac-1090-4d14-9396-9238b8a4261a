package userPresent

import (
	"strconv"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/userpresent"
)

const (
	serviceName = "userpresent"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewUserPresentClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.UserPresentClient { return c.Stub().(pb.UserPresentClient) }

func (c *Client) RealStub() pb.UserPresentClient {
	return c.typedStub()
}
func (c *Client) GetPresentOrderStatus(ctx context.Context, userID uint32, orderID string) (uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(userID))))
	resp, err := c.typedStub().GetPresentOrderStatus(ctx, &pb.GetPresentOrderStatusReq{
		Uid:     userID,
		OrderId: orderID,
	})
	return resp.GetOrderStatus(), protocol.ToServerError(err)
}

func (c *Client) GetUserPresentDetailList(ctx context.Context, userID uint32) (pb.GetUserPresentDetailListResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(userID))))
	resp, err := c.typedStub().GetUserPresentDetailList(ctx, &pb.GetUserPresentDetailListReq{})
	return *resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentConfigList(ctx context.Context) (*pb.GetPresentConfigListResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	resp, err := c.typedStub().GetPresentConfigList(ctx, &pb.GetPresentConfigListReq{})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentConfigListV2(ctx context.Context) (*pb.GetPresentConfigListV2Resp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	resp, err := c.typedStub().GetPresentConfigListV2(ctx, &pb.GetPresentConfigListV2Req{})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentConfigListV2ByUpdateTime(ctx context.Context, updateTime uint32) (*pb.GetPresentConfigListV2Resp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	resp, err := c.typedStub().GetPresentConfigListV2(ctx, &pb.GetPresentConfigListV2Req{UpdateTime: updateTime})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentConfigListByType(ctx context.Context, typeBitMap uint32) (*pb.GetPresentConfigListResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	resp, err := c.typedStub().GetPresentConfigList(ctx, &pb.GetPresentConfigListReq{
		TypeBitmap: typeBitMap,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserPresentSummary(ctx context.Context, uid uint32) (*pb.GetUserPresentSummaryResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().GetUserPresentSummary(ctx, &pb.GetUserPresentSummaryReq{Uid: uid})
	return resp, protocol.ToServerError(err)
}

// GetPresentConfigById .
func (c *Client) GetPresentConfigById(ctx context.Context, itemId uint32) (*pb.GetPresentConfigByIdResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	resp, err := c.typedStub().GetPresentConfigById(ctx, &pb.GetPresentConfigByIdReq{
		ItemId: itemId,
	})
	return resp, protocol.ToServerError(err)
}

// GetAllUserRecvPresentCount .
func (c *Client) GetAllUserRecvPresentCount(ctx context.Context, userID, beginTime, endTime uint32, source int32) (uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(userID))))
	resp, err := c.typedStub().GetAllUserRecvPresentCount(ctx, &pb.GetAllUserRecvPresentCountReq{
		BeginTime: beginTime,
		EndTime:   endTime,
		Source:    source,
	})
	return resp.GetTotalCount(), protocol.ToServerError(err)
}

// GetAllUserRecvPresentOrderList .
func (c *Client) GetAllUserRecvPresentOrderList(ctx context.Context, userID, beginTime, endTime uint32, source int32) ([]string, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(userID))))
	resp, err := c.typedStub().GetAllUserRecvPresentOrderList(ctx, &pb.GetAllUserRecvPresentOrderListReq{
		BeginTime: beginTime,
		EndTime:   endTime,
		Source:    source,
	})
	return resp.GetOrders(), protocol.ToServerError(err)
}

// GetOrderLogByOrderIdsReq .
func (c *Client) GetOrderLogByOrderIds(ctx context.Context, userID uint32, orders []string) (*pb.GetOrderLogByOrderIdsResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(userID))))
	resp, err := c.typedStub().GetOrderLogByOrderIds(ctx, &pb.GetOrderLogByOrderIdsReq{
		OrderIdList: orders,
	})
	return resp, protocol.ToServerError(err)
}

// GetPresentConfigByIdList
func (c *Client) GetPresentConfigByIdList(ctx context.Context, userID uint32, itemIdList []uint32, typeBitmap uint32) (*pb.GetPresentConfigByIdListResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(userID))))
	resp, err := c.typedStub().GetPresentConfigByIdList(ctx, &pb.GetPresentConfigByIdListReq{
		ItemIdList: itemIdList,
		TypeBitmap: typeBitmap,
	})
	return resp, protocol.ToServerError(err)
}

// GetUserPresentSummaryByItemList
func (c *Client) GetUserPresentSummaryByItemList(ctx context.Context, userID uint32, isSend bool, itemIdList []uint32) (*pb.GetUserPresentSummaryByItemListResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(userID))))
	resp, err := c.typedStub().GetUserPresentSummaryByItemList(ctx, &pb.GetUserPresentSummaryByItemListReq{
		Uid:      userID,
		IsSend:   isSend,
		ItemList: itemIdList,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddPresentConfig(ctx context.Context, req *pb.AddPresentConfigReq) (*pb.AddPresentConfigResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().AddPresentConfig(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdatePresentConfig(ctx context.Context, req *pb.UpdatePresentConfigReq) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	_, err := c.typedStub().UpdatePresentConfig(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) DelPresentConfig(ctx context.Context, itemId uint32) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	_, err := c.typedStub().DelPresentConfig(ctx, &pb.DelPresentConfigReq{
		ItemId: itemId,
	})
	return protocol.ToServerError(err)
}

func (c *Client) SendPresent(ctx context.Context, req *pb.SendPresentReq) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	_, err := c.typedStub().SendPresent(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) GetPresentDETConfigById(ctx context.Context, presentId uint32) (*pb.GetPresentDETConfigByIdResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().GetPresentDETConfigById(ctx, &pb.GetPresentDETConfigByIdReq{PresentId: presentId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) RecordSceneSendPresent(ctx context.Context, req *pb.RecordSceneSendPresentReq) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	_, err := c.typedStub().RecordSceneSendPresent(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) GetScenePresentSummary(ctx context.Context, req *pb.GetScenePresentSummaryReq) (*pb.GetScenePresentSummaryResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().GetScenePresentSummary(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentConfigUpdateTime(ctx context.Context, req *pb.GetPresentConfigUpdateTimeReq) (*pb.GetPresentConfigUpdateTimeResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().GetPresentConfigUpdateTime(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentFlowConfigUpdateTime(ctx context.Context, req *pb.GetPresentFlowConfigUpdateTimeReq) (*pb.GetPresentFlowConfigUpdateTimeResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().GetPresentFlowConfigUpdateTime(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentFlowConfigList(ctx context.Context, req *pb.GetPresentFlowConfigListReq) (*pb.GetPresentFlowConfigListResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().GetPresentFlowConfigList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentDynaminEffectTemplateConfig(ctx context.Context, req *pb.GetPresentDynaminEffectTemplateConfigReq) (*pb.GetPresentDynaminEffectTemplateConfigResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().GetPresentDynaminEffectTemplateConfig(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetValidNamingPresentInfos(ctx context.Context, req *pb.GetValidNamingPresentInfosReq) (*pb.GetValidNamingPresentInfosResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	resp, err := c.typedStub().GetValidNamingPresentInfos(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserPresentDetailListNew(ctx context.Context, userID uint32) (*pb.GetUserPresentDetailListResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(userID))))
	resp, err := c.typedStub().GetUserPresentDetailListNew(ctx, &pb.GetUserPresentDetailListReq{Uid: userID})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserPresentSendDetailList(ctx context.Context, userID uint32) (*pb.GetUserPresentSendDetailListResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(userID))))
	resp, err := c.typedStub().GetUserPresentSendDetailList(ctx, &pb.GetUserPresentSendDetailListReq{Uid: userID})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentConfigListWithUT(ctx context.Context, updateTime, typeBitMap uint32) (*pb.GetPresentConfigListV2Resp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	resp, err := c.typedStub().GetPresentConfigListV2(ctx, &pb.GetPresentConfigListV2Req{
		TypeBitmap: typeBitMap,
		UpdateTime: updateTime,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentDynamicTemplateConfUpdateTime(ctx context.Context) (uint32, protocol.ServerError) {
	resp, err := c.typedStub().GetPresentDynamicTemplateConfUpdateTime(ctx, &pb.GetPresentDynamicTemplateConfUpdateTimeReq{})
	return resp.GetUpdateTs(), protocol.ToServerError(err)
}
