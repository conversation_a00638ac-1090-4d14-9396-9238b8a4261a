// Code generated by quicksilver-cli. DO NOT EDIT.
package channel_lottery

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/channel-lottery"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	BatchGetChannelLotteryId(ctx context.Context, channelIds []uint32) (*pb.BatchGetChannelLotteryIdResp,protocol.ServerError)
	BeginChannelLottery(ctx context.Context, in *pb.BeginChannelLotteryReq) (*pb.BeginChannelLotteryResp,protocol.ServerError)
	BreakChannelLottery(ctx context.Context, channelId uint32) (*pb.BreakChannelLotteryResp,protocol.ServerError)
	ChangeOfficialInfo(ctx context.Context, officialChannelId uint32, endTime int64) (*pb.ChangeOfficialInfoResp,protocol.ServerError)
	GetChannelLotteryInfo(ctx context.Context, in *pb.GetChannelLotteryInfoReq) (*pb.GetChannelLotteryInfoResp,protocol.ServerError)
	GetChannelLotteryInfoList(ctx context.Context, in *pb.GetChannelLotteryInfoListReq) (*pb.GetChannelLotteryInfoListResp,protocol.ServerError)
	GetChannelLotterySetting(ctx context.Context, in *pb.GetChannelLotterySettingReq) (*pb.GetChannelLotterySettingResp,protocol.ServerError)
	GetEnterChannelUserCnt(ctx context.Context, in *pb.GetEnterChannelUserCntReq) (*pb.GetEnterChannelUserCntResp,protocol.ServerError)
	JoinChannelLottery(ctx context.Context, in *pb.JoinChannelLotteryReq) (*pb.JoinChannelLotteryResp,protocol.ServerError)
	ReportEnterShareChannel(ctx context.Context, in *pb.ReportEnterShareChannelReq) (*pb.ReportEnterShareChannelResp,protocol.ServerError)
	SearchCustomGifts(ctx context.Context, in *pb.SearchCustomGiftsReq) (*pb.SearchCustomGiftsResp,protocol.ServerError)
	SearchLotteryOpens(ctx context.Context, channelId, displayId, offset, limit uint32) (*pb.SearchLotteryOpensResp,protocol.ServerError)
	SendChannelLotteryPresent(ctx context.Context, in *pb.SendChannelLotteryPresentReq) (*pb.SendChannelLotteryPresentResp,protocol.ServerError)
	SetChannelLotteryInfo(ctx context.Context, in *pb.SetChannelLotteryInfoReq) (*pb.SetChannelLotteryInfoResp,protocol.ServerError)
	SetLotteryOpenList(ctx context.Context, in *pb.SetLotteryOpenListReq) (*pb.SetLotteryOpenListResp,protocol.ServerError)
	ShowChannelLotterySetting(ctx context.Context, in *pb.ShowChannelLotterySettingReq) (*pb.ShowChannelLotterySettingResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
