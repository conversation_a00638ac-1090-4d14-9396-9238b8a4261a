package alloc

import (
	"context"

	pb "golang.52tt.com/protocol/services/seqgen/v3"
	"google.golang.org/grpc"
)

type Alloc struct {
	pb.AllocClient
	conn *grpc.ClientConn
}

func NewAllocClient(target string, opts ...grpc.DialOption) (*Alloc, error) {
	conn, err := grpc.Dial(target, opts...)
	if err != nil {
		return nil, err
	}
	return &Alloc{
		AllocClient: pb.NewAllocClient(conn),
		conn:        conn,
	}, nil
}

func (c *Alloc) GenUserSeq(ctx context.Context, id uint32, tag string) (int64, error) {
	resp, err := c.AllocClient.GenUserSeq(ctx, &pb.GenUserSeqReq{
		Id:  id,
		Tag: tag,
	})
	if err != nil {
		return 0, err
	}
	return resp.Id, nil
}

func (c *Alloc) GetUserSeq(ctx context.Context, id uint32, tag string) (int64, error) {
	resp, err := c.AllocClient.GetUserSeq(ctx, &pb.GetUserSeqReq{
		Id:  id,
		Tag: tag,
	})
	if err != nil {
		return 0, err
	}
	return resp.Id, nil
}

func (c *Alloc) Close(ctx context.Context) error {
	return c.conn.Close()
}
