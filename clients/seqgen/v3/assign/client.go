package assign

import (
	"context"

	pb "golang.52tt.com/protocol/services/seqgen/v3"
	"google.golang.org/grpc"
)

type Assign struct {
	pb.AssignClient
	conn *grpc.ClientConn
}

func NewAssignClient(target string, opts ...grpc.DialOption) (*Assign, error) {
	conn, err := grpc.Dial(target, opts...)
	if err != nil {
		return nil, err
	}
	return &Assign{
		AssignClient: pb.NewAssignClient(conn),
		conn:         conn,
	}, nil
}

func (c *Assign) RegSection(ctx context.Context, tag string, async bool) error {
	_, err := c.AssignClient.RegSection(ctx, &pb.RegSectionReq{
		Tag:   tag,
		Async: async,
	})
	if err != nil {
		return err
	}
	return nil
}

func (c *Assign) UnRegSection(ctx context.Context, tag string, async bool) error {
	_, err := c.AssignClient.UnRegSection(ctx, &pb.UnRegSectionReq{
		Tag:   tag,
		Async: async,
	})
	if err != nil {
		return err
	}
	return nil
}

func (c *Assign) ReBalance(ctx context.Context) error {
	_, err := c.AssignClient.ReBalance(ctx, &pb.ReBalanceReq{})
	if err != nil {
		return err
	}
	return nil
}

func (c *Assign) Close(ctx context.Context) error {
	return c.conn.Close()
}
