// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/channel-open-game/iclient.go

// Package channel_open_game is a generated GoMock package.
package channel_open_game

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channel_open_game "golang.52tt.com/protocol/services/channel-open-game"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetGameInfo mocks base method.
func (m *MockIClient) BatchGetGameInfo(ctx context.Context, in *channel_open_game.BatchGetGameInfoReq) (*channel_open_game.BatchGetGameInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGameInfo", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.BatchGetGameInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGameInfo indicates an expected call of BatchGetGameInfo.
func (mr *MockIClientMockRecorder) BatchGetGameInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGameInfo", reflect.TypeOf((*MockIClient)(nil).BatchGetGameInfo), ctx, in)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetChannelGameBaseInfo mocks base method.
func (m *MockIClient) GetChannelGameBaseInfo(ctx context.Context, in *channel_open_game.GetChannelGameBaseInfoReq) (*channel_open_game.GetChannelGameBaseInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelGameBaseInfo", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.GetChannelGameBaseInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelGameBaseInfo indicates an expected call of GetChannelGameBaseInfo.
func (mr *MockIClientMockRecorder) GetChannelGameBaseInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelGameBaseInfo", reflect.TypeOf((*MockIClient)(nil).GetChannelGameBaseInfo), ctx, in)
}

// GetChannelGameInfo mocks base method.
func (m *MockIClient) GetChannelGameInfo(ctx context.Context, in *channel_open_game.GetChannelGameInfoReq) (*channel_open_game.GetChannelGameInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelGameInfo", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.GetChannelGameInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelGameInfo indicates an expected call of GetChannelGameInfo.
func (mr *MockIClientMockRecorder) GetChannelGameInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelGameInfo", reflect.TypeOf((*MockIClient)(nil).GetChannelGameInfo), ctx, in)
}

// GetGameMaintain mocks base method.
func (m *MockIClient) GetGameMaintain(ctx context.Context, in *channel_open_game.GetGameMaintainReq) (*channel_open_game.GetGameMaintainResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameMaintain", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.GetGameMaintainResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGameMaintain indicates an expected call of GetGameMaintain.
func (mr *MockIClientMockRecorder) GetGameMaintain(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameMaintain", reflect.TypeOf((*MockIClient)(nil).GetGameMaintain), ctx, in)
}

// GetGameModeInfo mocks base method.
func (m *MockIClient) GetGameModeInfo(ctx context.Context, in *channel_open_game.GetGameModeInfoReq) (*channel_open_game.GetGameModeInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameModeInfo", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.GetGameModeInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGameModeInfo indicates an expected call of GetGameModeInfo.
func (mr *MockIClientMockRecorder) GetGameModeInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameModeInfo", reflect.TypeOf((*MockIClient)(nil).GetGameModeInfo), ctx, in)
}

// GetSupportGameList mocks base method.
func (m *MockIClient) GetSupportGameList(ctx context.Context, in *channel_open_game.GetSupportGameListReq) (*channel_open_game.GetSupportGameListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSupportGameList", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.GetSupportGameListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSupportGameList indicates an expected call of GetSupportGameList.
func (mr *MockIClientMockRecorder) GetSupportGameList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportGameList", reflect.TypeOf((*MockIClient)(nil).GetSupportGameList), ctx, in)
}

// SetChannelGame mocks base method.
func (m *MockIClient) SetChannelGame(ctx context.Context, in *channel_open_game.SetChannelGameReq) (*channel_open_game.SetChannelGameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelGame", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.SetChannelGameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelGame indicates an expected call of SetChannelGame.
func (mr *MockIClientMockRecorder) SetChannelGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelGame", reflect.TypeOf((*MockIClient)(nil).SetChannelGame), ctx, in)
}

// SetChannelGameAsync mocks base method.
func (m *MockIClient) SetChannelGameAsync(ctx context.Context, in *channel_open_game.SetChannelGameAsyncReq) (*channel_open_game.SetChannelGameAsyncResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelGameAsync", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.SetChannelGameAsyncResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelGameAsync indicates an expected call of SetChannelGameAsync.
func (mr *MockIClientMockRecorder) SetChannelGameAsync(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelGameAsync", reflect.TypeOf((*MockIClient)(nil).SetChannelGameAsync), ctx, in)
}

// SetChannelGameInfo mocks base method.
func (m *MockIClient) SetChannelGameInfo(ctx context.Context, in *channel_open_game.SetChannelGameInfoReq) (*channel_open_game.SetChannelGameInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelGameInfo", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.SetChannelGameInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelGameInfo indicates an expected call of SetChannelGameInfo.
func (mr *MockIClientMockRecorder) SetChannelGameInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelGameInfo", reflect.TypeOf((*MockIClient)(nil).SetChannelGameInfo), ctx, in)
}

// SetChannelGameMaster mocks base method.
func (m *MockIClient) SetChannelGameMaster(ctx context.Context, in *channel_open_game.SetChannelGameMasterReq) (*channel_open_game.SetChannelGameMasterResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelGameMaster", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.SetChannelGameMasterResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelGameMaster indicates an expected call of SetChannelGameMaster.
func (mr *MockIClientMockRecorder) SetChannelGameMaster(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelGameMaster", reflect.TypeOf((*MockIClient)(nil).SetChannelGameMaster), ctx, in)
}

// SetChannelGameMember mocks base method.
func (m *MockIClient) SetChannelGameMember(ctx context.Context, in *channel_open_game.SetChannelGameMemberReq) (*channel_open_game.SetChannelGameMemberResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelGameMember", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.SetChannelGameMemberResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelGameMember indicates an expected call of SetChannelGameMember.
func (mr *MockIClientMockRecorder) SetChannelGameMember(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelGameMember", reflect.TypeOf((*MockIClient)(nil).SetChannelGameMember), ctx, in)
}

// SetChannelGamePlayerOpenId mocks base method.
func (m *MockIClient) SetChannelGamePlayerOpenId(ctx context.Context, in *channel_open_game.SetChannelGamePlayerOpenIdReq) (*channel_open_game.SetChannelGamePlayerOpenIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelGamePlayerOpenId", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.SetChannelGamePlayerOpenIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelGamePlayerOpenId indicates an expected call of SetChannelGamePlayerOpenId.
func (mr *MockIClientMockRecorder) SetChannelGamePlayerOpenId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelGamePlayerOpenId", reflect.TypeOf((*MockIClient)(nil).SetChannelGamePlayerOpenId), ctx, in)
}

// SetChannelGameStatus mocks base method.
func (m *MockIClient) SetChannelGameStatus(ctx context.Context, in *channel_open_game.SetChannelGameStatusReq) (*channel_open_game.SetChannelGameStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelGameStatus", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.SetChannelGameStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelGameStatus indicates an expected call of SetChannelGameStatus.
func (mr *MockIClientMockRecorder) SetChannelGameStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelGameStatus", reflect.TypeOf((*MockIClient)(nil).SetChannelGameStatus), ctx, in)
}

// SetChannelGameSync mocks base method.
func (m *MockIClient) SetChannelGameSync(ctx context.Context, in *channel_open_game.SetChannelGameSyncReq) (*channel_open_game.SetChannelGameSyncResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelGameSync", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.SetChannelGameSyncResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelGameSync indicates an expected call of SetChannelGameSync.
func (mr *MockIClientMockRecorder) SetChannelGameSync(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelGameSync", reflect.TypeOf((*MockIClient)(nil).SetChannelGameSync), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdateChannelGame mocks base method.
func (m *MockIClient) UpdateChannelGame(ctx context.Context, in *channel_open_game.UpdateChannelGameReq) (*channel_open_game.UpdateChannelGameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChannelGame", ctx, in)
	ret0, _ := ret[0].(*channel_open_game.UpdateChannelGameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateChannelGame indicates an expected call of UpdateChannelGame.
func (mr *MockIClientMockRecorder) UpdateChannelGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChannelGame", reflect.TypeOf((*MockIClient)(nil).UpdateChannelGame), ctx, in)
}
