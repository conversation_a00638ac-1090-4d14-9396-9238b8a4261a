// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/channelol_member (interfaces: ChannelolMemberClient)

// Package channelol_member is a generated GoMock package.
package channelol_member

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	channelol_member "golang.52tt.com/protocol/services/channelol_member"
	grpc "google.golang.org/grpc"
)

// MockChannelolMemberClient is a mock of ChannelolMemberClient interface.
type MockChannelolMemberClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelolMemberClientMockRecorder
}

// MockChannelolMemberClientMockRecorder is the mock recorder for MockChannelolMemberClient.
type MockChannelolMemberClientMockRecorder struct {
	mock *MockChannelolMemberClient
}

// NewMockChannelolMemberClient creates a new mock instance.
func NewMockChannelolMemberClient(ctrl *gomock.Controller) *MockChannelolMemberClient {
	mock := &MockChannelolMemberClient{ctrl: ctrl}
	mock.recorder = &MockChannelolMemberClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelolMemberClient) EXPECT() *MockChannelolMemberClientMockRecorder {
	return m.recorder
}

// GetChannelOnlineMember mocks base method.
func (m *MockChannelolMemberClient) GetChannelOnlineMember(arg0 context.Context, arg1 *channelol_member.GetChannelOnlineMemberRequest, arg2 ...grpc.CallOption) (*channelol_member.GetChannelOnlineMemberResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelOnlineMember", varargs...)
	ret0, _ := ret[0].(*channelol_member.GetChannelOnlineMemberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelOnlineMember indicates an expected call of GetChannelOnlineMember.
func (mr *MockChannelolMemberClientMockRecorder) GetChannelOnlineMember(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelOnlineMember", reflect.TypeOf((*MockChannelolMemberClient)(nil).GetChannelOnlineMember), varargs...)
}
