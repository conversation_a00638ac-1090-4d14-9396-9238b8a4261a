// Code generated by MockGen. DO NOT EDIT.
// Source: E:\quicksilver\clients\im-attachment\iclient.go

// Package im_attachment is a generated GoMock package.
package im_attachment

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	imattachment "golang.52tt.com/protocol/services/imattachmentsvr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddFile mocks base method.
func (m *MockIClient) AddFile(ctx context.Context, uid uint32, msgType imattachment.FileType, curTime int64, ttl uint32, key string, extra *imattachment.FileExtraInfo) (*imattachment.AddFileResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddFile", ctx, uid, msgType, curTime, ttl, key, extra)
	ret0, _ := ret[0].(*imattachment.AddFileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddFile indicates an expected call of AddFile.
func (mr *MockIClientMockRecorder) AddFile(ctx, uid, msgType, curTime, ttl, key, extra interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFile", reflect.TypeOf((*MockIClient)(nil).AddFile), ctx, uid, msgType, curTime, ttl, key, extra)
}

// AddFileData mocks base method.
func (m *MockIClient) AddFileData(ctx context.Context, uid uint32, msgType imattachment.FileType, curTime int64, ttl uint32, key string, data []byte, extra *imattachment.FileExtraInfo) (*imattachment.AddFileResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddFileData", ctx, uid, msgType, curTime, ttl, key, data, extra)
	ret0, _ := ret[0].(*imattachment.AddFileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddFileData indicates an expected call of AddFileData.
func (mr *MockIClientMockRecorder) AddFileData(ctx, uid, msgType, curTime, ttl, key, data, extra interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFileData", reflect.TypeOf((*MockIClient)(nil).AddFileData), ctx, uid, msgType, curTime, ttl, key, data, extra)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetFile mocks base method.
func (m *MockIClient) GetFile(ctx context.Context, key string) (*imattachment.GetFileResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFile", ctx, key)
	ret0, _ := ret[0].(*imattachment.GetFileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFile indicates an expected call of GetFile.
func (mr *MockIClientMockRecorder) GetFile(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFile", reflect.TypeOf((*MockIClient)(nil).GetFile), ctx, key)
}

// GetFileProperty mocks base method.
func (m *MockIClient) GetFileProperty(ctx context.Context, uin uint32, in *imattachment.GetFilePropertyReq) (*imattachment.GetFilePropertyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFileProperty", ctx, uin, in)
	ret0, _ := ret[0].(*imattachment.GetFilePropertyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFileProperty indicates an expected call of GetFileProperty.
func (mr *MockIClientMockRecorder) GetFileProperty(ctx, uin, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFileProperty", reflect.TypeOf((*MockIClient)(nil).GetFileProperty), ctx, uin, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
