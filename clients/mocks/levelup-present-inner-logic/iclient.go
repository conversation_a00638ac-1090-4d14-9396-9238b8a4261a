// Code generated by MockGen. DO NOT EDIT.
// Source: D:\go-tt\quicksilver\clients\levelup-present-inner-logic\iclient.go

// Package levelup_present_inner_logic is a generated GoMock package.
package levelup_present_inner_logic

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	LevelupPresentInnerLogic "golang.52tt.com/protocol/services/levelup-present-inner-logic"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// SendLevelUpPresent mocks base method.
func (m *MockIClient) SendLevelUpPresent(ctx context.Context, req *LevelupPresentInnerLogic.SendLevelUpPresentReq) (*LevelupPresentInnerLogic.SendLevelUpPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendLevelUpPresent", ctx, req)
	ret0, _ := ret[0].(*LevelupPresentInnerLogic.SendLevelUpPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendLevelUpPresent indicates an expected call of SendLevelUpPresent.
func (mr *MockIClientMockRecorder) SendLevelUpPresent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendLevelUpPresent", reflect.TypeOf((*MockIClient)(nil).SendLevelUpPresent), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
