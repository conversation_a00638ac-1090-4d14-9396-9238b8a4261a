// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/TTProjects/quicksilver/clients/game-group-chat/iclient.go

// Package game_group_chat is a generated GoMock package.
package game_group_chat

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	game_group_chat "golang.52tt.com/protocol/services/game-group-chat"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetUsersSetExtraInfo mocks base method.
func (m *MockIClient) BatchGetUsersSetExtraInfo(ctx context.Context, in *game_group_chat.BatchGetUsersSetExtraInfoReq, opts ...grpc.CallOption) (*game_group_chat.BatchGetUsersSetExtraInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUsersSetExtraInfo", varargs...)
	ret0, _ := ret[0].(*game_group_chat.BatchGetUsersSetExtraInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUsersSetExtraInfo indicates an expected call of BatchGetUsersSetExtraInfo.
func (mr *MockIClientMockRecorder) BatchGetUsersSetExtraInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUsersSetExtraInfo", reflect.TypeOf((*MockIClient)(nil).BatchGetUsersSetExtraInfo), varargs...)
}

// GetGameGroupActiveStatus mocks base method.
func (m *MockIClient) GetGameGroupActiveStatus(ctx context.Context, in *game_group_chat.GetGameGroupActiveStatusReq, opts ...grpc.CallOption) (*game_group_chat.GetGameGroupActiveStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameGroupActiveStatus", varargs...)
	ret0, _ := ret[0].(*game_group_chat.GetGameGroupActiveStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameGroupActiveStatus indicates an expected call of GetGameGroupActiveStatus.
func (mr *MockIClientMockRecorder) GetGameGroupActiveStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameGroupActiveStatus", reflect.TypeOf((*MockIClient)(nil).GetGameGroupActiveStatus), varargs...)
}

// GetUserExtraInfo mocks base method.
func (m *MockIClient) GetUserExtraInfo(ctx context.Context, in *game_group_chat.GetUserExtraInfoReq, opts ...grpc.CallOption) (*game_group_chat.GetUserExtraInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserExtraInfo", varargs...)
	ret0, _ := ret[0].(*game_group_chat.GetUserExtraInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExtraInfo indicates an expected call of GetUserExtraInfo.
func (mr *MockIClientMockRecorder) GetUserExtraInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExtraInfo", reflect.TypeOf((*MockIClient)(nil).GetUserExtraInfo), varargs...)
}

// GetUserExtraInfoConfig mocks base method.
func (m *MockIClient) GetUserExtraInfoConfig(ctx context.Context, in *game_group_chat.GetUserExtraInfoConfigReq, opts ...grpc.CallOption) (*game_group_chat.GetUserExtraInfoConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserExtraInfoConfig", varargs...)
	ret0, _ := ret[0].(*game_group_chat.GetUserExtraInfoConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExtraInfoConfig indicates an expected call of GetUserExtraInfoConfig.
func (mr *MockIClientMockRecorder) GetUserExtraInfoConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExtraInfoConfig", reflect.TypeOf((*MockIClient)(nil).GetUserExtraInfoConfig), varargs...)
}

// SetUserExtraInfo mocks base method.
func (m *MockIClient) SetUserExtraInfo(ctx context.Context, in *game_group_chat.SetUserExtraInfoReq, opts ...grpc.CallOption) (*game_group_chat.SetUserExtraInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserExtraInfo", varargs...)
	ret0, _ := ret[0].(*game_group_chat.SetUserExtraInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserExtraInfo indicates an expected call of SetUserExtraInfo.
func (mr *MockIClientMockRecorder) SetUserExtraInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserExtraInfo", reflect.TypeOf((*MockIClient)(nil).SetUserExtraInfo), varargs...)
}
