// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/masked-call/iclient.go

// Package masked_call is a generated GoMock package.
package masked_call

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	masked_call "golang.52tt.com/protocol/services/masked-call"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CancelMatch mocks base method.
func (m *MockIClient) CancelMatch(ctx context.Context, in *masked_call.CancelMatchReq) (*masked_call.CancelMatchResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelMatch", ctx, in)
	ret0, _ := ret[0].(*masked_call.CancelMatchResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CancelMatch indicates an expected call of CancelMatch.
func (mr *MockIClientMockRecorder) CancelMatch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelMatch", reflect.TypeOf((*MockIClient)(nil).CancelMatch), ctx, in)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// Comment mocks base method.
func (m *MockIClient) Comment(ctx context.Context, in *masked_call.CommentReq) (*masked_call.CommentResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Comment", ctx, in)
	ret0, _ := ret[0].(*masked_call.CommentResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// Comment indicates an expected call of Comment.
func (mr *MockIClientMockRecorder) Comment(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Comment", reflect.TypeOf((*MockIClient)(nil).Comment), ctx, in)
}

// InviteUnmask mocks base method.
func (m *MockIClient) InviteUnmask(ctx context.Context, in *masked_call.InviteUnmaskReq) (*masked_call.InviteUnmaskResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InviteUnmask", ctx, in)
	ret0, _ := ret[0].(*masked_call.InviteUnmaskResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// InviteUnmask indicates an expected call of InviteUnmask.
func (mr *MockIClientMockRecorder) InviteUnmask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InviteUnmask", reflect.TypeOf((*MockIClient)(nil).InviteUnmask), ctx, in)
}

// QueryCallInfo mocks base method.
func (m *MockIClient) QueryCallInfo(ctx context.Context, in *masked_call.QueryCallInfoReq) (*masked_call.QueryCallInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryCallInfo", ctx, in)
	ret0, _ := ret[0].(*masked_call.QueryCallInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// QueryCallInfo indicates an expected call of QueryCallInfo.
func (mr *MockIClientMockRecorder) QueryCallInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryCallInfo", reflect.TypeOf((*MockIClient)(nil).QueryCallInfo), ctx, in)
}

// QueryMatchInfo mocks base method.
func (m *MockIClient) QueryMatchInfo(ctx context.Context, in *masked_call.QueryMatchInfoReq) (*masked_call.QueryMatchInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryMatchInfo", ctx, in)
	ret0, _ := ret[0].(*masked_call.QueryMatchInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// QueryMatchInfo indicates an expected call of QueryMatchInfo.
func (mr *MockIClientMockRecorder) QueryMatchInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryMatchInfo", reflect.TypeOf((*MockIClient)(nil).QueryMatchInfo), ctx, in)
}

// ReportAudio mocks base method.
func (m *MockIClient) ReportAudio(ctx context.Context, in *masked_call.ReportAudioReq) (*masked_call.ReportAudioResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportAudio", ctx, in)
	ret0, _ := ret[0].(*masked_call.ReportAudioResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ReportAudio indicates an expected call of ReportAudio.
func (mr *MockIClientMockRecorder) ReportAudio(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportAudio", reflect.TypeOf((*MockIClient)(nil).ReportAudio), ctx, in)
}

// Roll mocks base method.
func (m *MockIClient) Roll(ctx context.Context, in *masked_call.RollReq) (*masked_call.RollResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Roll", ctx, in)
	ret0, _ := ret[0].(*masked_call.RollResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// Roll indicates an expected call of Roll.
func (mr *MockIClientMockRecorder) Roll(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Roll", reflect.TypeOf((*MockIClient)(nil).Roll), ctx, in)
}

// SetConnectStatus mocks base method.
func (m *MockIClient) SetConnectStatus(ctx context.Context, in *masked_call.SetConnectStatusReq) (*masked_call.SetConnectStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetConnectStatus", ctx, in)
	ret0, _ := ret[0].(*masked_call.SetConnectStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetConnectStatus indicates an expected call of SetConnectStatus.
func (mr *MockIClientMockRecorder) SetConnectStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetConnectStatus", reflect.TypeOf((*MockIClient)(nil).SetConnectStatus), ctx, in)
}

// StartMatch mocks base method.
func (m *MockIClient) StartMatch(ctx context.Context, in *masked_call.StartMatchReq) (*masked_call.StartMatchResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartMatch", ctx, in)
	ret0, _ := ret[0].(*masked_call.StartMatchResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// StartMatch indicates an expected call of StartMatch.
func (mr *MockIClientMockRecorder) StartMatch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartMatch", reflect.TypeOf((*MockIClient)(nil).StartMatch), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// TipOff mocks base method.
func (m *MockIClient) TipOff(ctx context.Context, in *masked_call.TipOffReq) (*masked_call.TipOffResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TipOff", ctx, in)
	ret0, _ := ret[0].(*masked_call.TipOffResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// TipOff indicates an expected call of TipOff.
func (mr *MockIClientMockRecorder) TipOff(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TipOff", reflect.TypeOf((*MockIClient)(nil).TipOff), ctx, in)
}

// Unmask mocks base method.
func (m *MockIClient) Unmask(ctx context.Context, in *masked_call.UnmaskReq) (*masked_call.UnmaskResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Unmask", ctx, in)
	ret0, _ := ret[0].(*masked_call.UnmaskResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// Unmask indicates an expected call of Unmask.
func (mr *MockIClientMockRecorder) Unmask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unmask", reflect.TypeOf((*MockIClient)(nil).Unmask), ctx, in)
}
