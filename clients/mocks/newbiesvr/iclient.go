// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/newbiesvr/iclient.go

// Package newbiesvr is a generated GoMock package.
package newbiesvr

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	newbiesvr "golang.52tt.com/protocol/services/newbiesvr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CheckRegDevIsNew mocks base method.
func (m *MockIClient) CheckRegDevIsNew(ctx context.Context, uid uint32, devId string) (*newbiesvr.CheckRegDevIsNewResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRegDevIsNew", ctx, uid, devId)
	ret0, _ := ret[0].(*newbiesvr.CheckRegDevIsNewResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckRegDevIsNew indicates an expected call of CheckRegDevIsNew.
func (mr *MockIClientMockRecorder) CheckRegDevIsNew(ctx, uid, devId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRegDevIsNew", reflect.TypeOf((*MockIClient)(nil).CheckRegDevIsNew), ctx, uid, devId)
}

// CheckUserRegDev mocks base method.
func (m *MockIClient) CheckUserRegDev(ctx context.Context, uid uint32) (*newbiesvr.CheckUserRegDevResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserRegDev", ctx, uid)
	ret0, _ := ret[0].(*newbiesvr.CheckUserRegDevResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserRegDev indicates an expected call of CheckUserRegDev.
func (mr *MockIClientMockRecorder) CheckUserRegDev(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserRegDev", reflect.TypeOf((*MockIClient)(nil).CheckUserRegDev), ctx, uid)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
