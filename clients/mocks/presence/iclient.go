// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/presence/iclient.go

// Package presence is a generated GoMock package.
package presence

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	Presence "golang.52tt.com/protocol/services/presencesvr"
	context "golang.org/x/net/context"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetOnlineMap mocks base method.
func (m *MockIClient) GetOnlineMap(ctx context.Context, uids []uint32) (map[uint32]bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOnlineMap", ctx, uids)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetOnlineMap indicates an expected call of GetOnlineMap.
func (mr *MockIClientMockRecorder) GetOnlineMap(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOnlineMap", reflect.TypeOf((*MockIClient)(nil).GetOnlineMap), ctx, uids)
}

// GetPresence mocks base method.
func (m *MockIClient) GetPresence(ctx context.Context, uid uint32) ([]*Presence.Pres, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresence", ctx, uid)
	ret0, _ := ret[0].([]*Presence.Pres)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresence indicates an expected call of GetPresence.
func (mr *MockIClientMockRecorder) GetPresence(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresence", reflect.TypeOf((*MockIClient)(nil).GetPresence), ctx, uid)
}

// GetPresences mocks base method.
func (m *MockIClient) GetPresences(ctx context.Context, uids []uint32) ([]*Presence.Pres, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresences", ctx, uids)
	ret0, _ := ret[0].([]*Presence.Pres)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresences indicates an expected call of GetPresences.
func (mr *MockIClientMockRecorder) GetPresences(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresences", reflect.TypeOf((*MockIClient)(nil).GetPresences), ctx, uids)
}

// GetPresencesMap mocks base method.
func (m *MockIClient) GetPresencesMap(ctx context.Context, uids []uint32) (map[uint32][]*Presence.Pres, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresencesMap", ctx, uids)
	ret0, _ := ret[0].(map[uint32][]*Presence.Pres)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresencesMap indicates an expected call of GetPresencesMap.
func (mr *MockIClientMockRecorder) GetPresencesMap(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresencesMap", reflect.TypeOf((*MockIClient)(nil).GetPresencesMap), ctx, uids)
}

// StatPres mocks base method.
func (m *MockIClient) StatPres(ctx context.Context, in *Presence.StatPresReq) (*Presence.StatPresResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StatPres", ctx, in)
	ret0, _ := ret[0].(*Presence.StatPresResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// StatPres indicates an expected call of StatPres.
func (mr *MockIClientMockRecorder) StatPres(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StatPres", reflect.TypeOf((*MockIClient)(nil).StatPres), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdatePresence mocks base method.
func (m *MockIClient) UpdatePresence(ctx context.Context, uid uint32, proxy *Presence.Proxy, presences []*Presence.Pres) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresence", ctx, uid, proxy, presences)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// UpdatePresence indicates an expected call of UpdatePresence.
func (mr *MockIClientMockRecorder) UpdatePresence(ctx, uid, proxy, presences interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresence", reflect.TypeOf((*MockIClient)(nil).UpdatePresence), ctx, uid, proxy, presences)
}
