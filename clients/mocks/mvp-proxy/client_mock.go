// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/mvp-proxy (interfaces: IClient)

// Package mvp_proxy is a generated GoMock package.
package mvp_proxy

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	mvp_proxy "golang.52tt.com/protocol/services/mvp-proxy"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetBlackWhiteBoxSuperviseSwitch mocks base method.
func (m *MockIClient) BatchGetBlackWhiteBoxSuperviseSwitch(arg0 context.Context, arg1 uint32, arg2 []*mvp_proxy.BlackWhiteBoxSuperviseSwitchInfo) (*mvp_proxy.BatchGetBlackWhiteBoxSuperviseSwitchResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBlackWhiteBoxSuperviseSwitch", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mvp_proxy.BatchGetBlackWhiteBoxSuperviseSwitchResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetBlackWhiteBoxSuperviseSwitch indicates an expected call of BatchGetBlackWhiteBoxSuperviseSwitch.
func (mr *MockIClientMockRecorder) BatchGetBlackWhiteBoxSuperviseSwitch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBlackWhiteBoxSuperviseSwitch", reflect.TypeOf((*MockIClient)(nil).BatchGetBlackWhiteBoxSuperviseSwitch), arg0, arg1, arg2)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CheckFraudDevice mocks base method.
func (m *MockIClient) CheckFraudDevice(arg0 context.Context, arg1 uint64, arg2 []byte) (*mvp_proxy.CheckFraudDeviceResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckFraudDevice", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mvp_proxy.CheckFraudDeviceResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckFraudDevice indicates an expected call of CheckFraudDevice.
func (mr *MockIClientMockRecorder) CheckFraudDevice(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFraudDevice", reflect.TypeOf((*MockIClient)(nil).CheckFraudDevice), arg0, arg1, arg2)
}

// CheckUserIdentity mocks base method.
func (m *MockIClient) CheckUserIdentity(arg0 context.Context, arg1 *mvp_proxy.CheckUserIdentityReq) (*mvp_proxy.CheckUserIdentityResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserIdentity", arg0, arg1)
	ret0, _ := ret[0].(*mvp_proxy.CheckUserIdentityResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckUserIdentity indicates an expected call of CheckUserIdentity.
func (mr *MockIClientMockRecorder) CheckUserIdentity(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserIdentity", reflect.TypeOf((*MockIClient)(nil).CheckUserIdentity), arg0, arg1)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetBlackWhiteBoxSuperviseSwitch mocks base method.
func (m *MockIClient) GetBlackWhiteBoxSuperviseSwitch(arg0 context.Context, arg1 *mvp_proxy.GetBlackWhiteBoxSuperviseSwitchReq) (*mvp_proxy.GetBlackWhiteBoxSuperviseSwitchResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlackWhiteBoxSuperviseSwitch", arg0, arg1)
	ret0, _ := ret[0].(*mvp_proxy.GetBlackWhiteBoxSuperviseSwitchResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetBlackWhiteBoxSuperviseSwitch indicates an expected call of GetBlackWhiteBoxSuperviseSwitch.
func (mr *MockIClientMockRecorder) GetBlackWhiteBoxSuperviseSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlackWhiteBoxSuperviseSwitch", reflect.TypeOf((*MockIClient)(nil).GetBlackWhiteBoxSuperviseSwitch), arg0, arg1)
}

// RealtimeRule mocks base method.
func (m *MockIClient) RealtimeRule(arg0 context.Context, arg1 *mvp_proxy.RealtimeRuleReq) (*mvp_proxy.RealtimeRuleResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RealtimeRule", arg0, arg1)
	ret0, _ := ret[0].(*mvp_proxy.RealtimeRuleResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// RealtimeRule indicates an expected call of RealtimeRule.
func (mr *MockIClientMockRecorder) RealtimeRule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RealtimeRule", reflect.TypeOf((*MockIClient)(nil).RealtimeRule), arg0, arg1)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
