// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/rcmd/perfect-match (interfaces: IClient)

// Package perfect_match is a generated GoMock package.
package perfect_match

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	perfect_match "golang.52tt.com/protocol/services/rcmd/perfect_match"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AckMatchedUsers mocks base method.
func (m *MockIClient) AckMatchedUsers(arg0 context.Context, arg1 perfect_match.AckMatchedUsersReq) (*perfect_match.AckMatchedUsersRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AckMatchedUsers", arg0, arg1)
	ret0, _ := ret[0].(*perfect_match.AckMatchedUsersRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AckMatchedUsers indicates an expected call of AckMatchedUsers.
func (mr *MockIClientMockRecorder) AckMatchedUsers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AckMatchedUsers", reflect.TypeOf((*MockIClient)(nil).AckMatchedUsers), arg0, arg1)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Cancel mocks base method.
func (m *MockIClient) Cancel(arg0 context.Context, arg1 perfect_match.CancelReq) (*perfect_match.CancelRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel", arg0, arg1)
	ret0, _ := ret[0].(*perfect_match.CancelRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// Cancel indicates an expected call of Cancel.
func (mr *MockIClientMockRecorder) Cancel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockIClient)(nil).Cancel), arg0, arg1)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetMatchInfo mocks base method.
func (m *MockIClient) GetMatchInfo(arg0 context.Context, arg1 perfect_match.GetMatchInfoReq) (*perfect_match.GetMatchInfoRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMatchInfo", arg0, arg1)
	ret0, _ := ret[0].(*perfect_match.GetMatchInfoRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMatchInfo indicates an expected call of GetMatchInfo.
func (mr *MockIClientMockRecorder) GetMatchInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMatchInfo", reflect.TypeOf((*MockIClient)(nil).GetMatchInfo), arg0, arg1)
}

// GetMatchedUsers mocks base method.
func (m *MockIClient) GetMatchedUsers(arg0 context.Context, arg1 perfect_match.GetMatchedUsersReq) (*perfect_match.GetMatchedUsersRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMatchedUsers", arg0, arg1)
	ret0, _ := ret[0].(*perfect_match.GetMatchedUsersRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMatchedUsers indicates an expected call of GetMatchedUsers.
func (mr *MockIClientMockRecorder) GetMatchedUsers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMatchedUsers", reflect.TypeOf((*MockIClient)(nil).GetMatchedUsers), arg0, arg1)
}

// GetQuestions mocks base method.
func (m *MockIClient) GetQuestions(arg0 context.Context, arg1 perfect_match.GetQuestionsReq) (*perfect_match.GetQuestionsRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQuestions", arg0, arg1)
	ret0, _ := ret[0].(*perfect_match.GetQuestionsRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetQuestions indicates an expected call of GetQuestions.
func (mr *MockIClientMockRecorder) GetQuestions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuestions", reflect.TypeOf((*MockIClient)(nil).GetQuestions), arg0, arg1)
}

// GetResult mocks base method.
func (m *MockIClient) GetResult(arg0 context.Context, arg1 perfect_match.GetResultReq) (*perfect_match.GetResultRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResult", arg0, arg1)
	ret0, _ := ret[0].(*perfect_match.GetResultRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetResult indicates an expected call of GetResult.
func (mr *MockIClientMockRecorder) GetResult(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResult", reflect.TypeOf((*MockIClient)(nil).GetResult), arg0, arg1)
}

// GetWaitingTime mocks base method.
func (m *MockIClient) GetWaitingTime(arg0 context.Context, arg1 perfect_match.GetWaitingTimeReq) (*perfect_match.GetWaitingTimeRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWaitingTime", arg0, arg1)
	ret0, _ := ret[0].(*perfect_match.GetWaitingTimeRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetWaitingTime indicates an expected call of GetWaitingTime.
func (mr *MockIClientMockRecorder) GetWaitingTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWaitingTime", reflect.TypeOf((*MockIClient)(nil).GetWaitingTime), arg0, arg1)
}

// Inspect mocks base method.
func (m *MockIClient) Inspect(arg0 context.Context, arg1 perfect_match.InspectReq) (*perfect_match.InspectRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Inspect", arg0, arg1)
	ret0, _ := ret[0].(*perfect_match.InspectRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// Inspect indicates an expected call of Inspect.
func (mr *MockIClientMockRecorder) Inspect(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Inspect", reflect.TypeOf((*MockIClient)(nil).Inspect), arg0, arg1)
}

// Start mocks base method.
func (m *MockIClient) Start(arg0 context.Context, arg1 perfect_match.StartReq) (*perfect_match.StartRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start", arg0, arg1)
	ret0, _ := ret[0].(*perfect_match.StartRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// Start indicates an expected call of Start.
func (mr *MockIClientMockRecorder) Start(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockIClient)(nil).Start), arg0, arg1)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
