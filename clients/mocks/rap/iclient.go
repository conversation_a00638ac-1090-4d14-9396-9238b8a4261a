// Code generated by MockGen. DO NOT EDIT.
// Source: clients/rap/iclient.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	rap "golang.52tt.com/protocol/services/rap"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// MuseChannelAddLike mocks base method.
func (m *MockIClient) MuseChannelAddLike(ctx context.Context, req rap.MuseChannelAddLikeReq) (*rap.MuseChannelAddLikeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MuseChannelAddLike", ctx, req)
	ret0, _ := ret[0].(*rap.MuseChannelAddLikeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// MuseChannelAddLike indicates an expected call of MuseChannelAddLike.
func (mr *MockIClientMockRecorder) MuseChannelAddLike(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MuseChannelAddLike", reflect.TypeOf((*MockIClient)(nil).MuseChannelAddLike), ctx, req)
}

// MuseChannelGetInteraction mocks base method.
func (m *MockIClient) MuseChannelGetInteraction(ctx context.Context, req rap.MuseChannelGetInteractionReq) (*rap.MuseChannelGetInteractionResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MuseChannelGetInteraction", ctx, req)
	ret0, _ := ret[0].(*rap.MuseChannelGetInteractionResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// MuseChannelGetInteraction indicates an expected call of MuseChannelGetInteraction.
func (mr *MockIClientMockRecorder) MuseChannelGetInteraction(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MuseChannelGetInteraction", reflect.TypeOf((*MockIClient)(nil).MuseChannelGetInteraction), ctx, req)
}

// MuseChannelGetLikeCount mocks base method.
func (m *MockIClient) MuseChannelGetLikeCount(ctx context.Context, req rap.MuseChannelGetLikeCountReq) (*rap.MuseChannelGetLikeCountResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MuseChannelGetLikeCount", ctx, req)
	ret0, _ := ret[0].(*rap.MuseChannelGetLikeCountResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// MuseChannelGetLikeCount indicates an expected call of MuseChannelGetLikeCount.
func (mr *MockIClientMockRecorder) MuseChannelGetLikeCount(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MuseChannelGetLikeCount", reflect.TypeOf((*MockIClient)(nil).MuseChannelGetLikeCount), ctx, req)
}

// MuseChannelGetLikeInfo mocks base method.
func (m *MockIClient) MuseChannelGetLikeInfo(ctx context.Context, req rap.MuseChannelGetLikeInfoReq) (*rap.MuseChannelGetLikeInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MuseChannelGetLikeInfo", ctx, req)
	ret0, _ := ret[0].(*rap.MuseChannelGetLikeInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// MuseChannelGetLikeInfo indicates an expected call of MuseChannelGetLikeInfo.
func (mr *MockIClientMockRecorder) MuseChannelGetLikeInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MuseChannelGetLikeInfo", reflect.TypeOf((*MockIClient)(nil).MuseChannelGetLikeInfo), ctx, req)
}

// RapExpressRespect mocks base method.
func (m *MockIClient) RapExpressRespect(ctx context.Context, req rap.RapExpressRespectReq) (*rap.RapExpressRespectResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RapExpressRespect", ctx, req)
	ret0, _ := ret[0].(*rap.RapExpressRespectResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// RapExpressRespect indicates an expected call of RapExpressRespect.
func (mr *MockIClientMockRecorder) RapExpressRespect(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RapExpressRespect", reflect.TypeOf((*MockIClient)(nil).RapExpressRespect), ctx, req)
}

// RapGetChannelRespectInfo mocks base method.
func (m *MockIClient) RapGetChannelRespectInfo(ctx context.Context, req rap.RapGetChannelRespectInfoReq) (*rap.RapGetChannelRespectInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RapGetChannelRespectInfo", ctx, req)
	ret0, _ := ret[0].(*rap.RapGetChannelRespectInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// RapGetChannelRespectInfo indicates an expected call of RapGetChannelRespectInfo.
func (mr *MockIClientMockRecorder) RapGetChannelRespectInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RapGetChannelRespectInfo", reflect.TypeOf((*MockIClient)(nil).RapGetChannelRespectInfo), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
