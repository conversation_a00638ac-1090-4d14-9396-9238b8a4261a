// Code generated by MockGen. DO NOT EDIT.
// Source: F:\griffin\clients\numeric\iclient.go

// Package numeric is a generated GoMock package.
package numeric

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	numeric "golang.52tt.com/clients/numeric"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	numericsvr "golang.52tt.com/protocol/services/numericsvr"
	context "golang.org/x/net/context"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddUserNumeric mocks base method.
func (m *MockIClient) AddUserNumeric(ctx context.Context, Req *numericsvr.AddUserNumericReq) (*numericsvr.AddUserNumericResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserNumeric", ctx, Req)
	ret0, _ := ret[0].(*numericsvr.AddUserNumericResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddUserNumeric indicates an expected call of AddUserNumeric.
func (mr *MockIClientMockRecorder) AddUserNumeric(ctx, Req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserNumeric", reflect.TypeOf((*MockIClient)(nil).AddUserNumeric), ctx, Req)
}

// BatchGetPersonalNumeric mocks base method.
func (m *MockIClient) BatchGetPersonalNumeric(ctx context.Context, uidList []uint32) (map[uint32]*numeric.UserNumericValue, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPersonalNumeric", ctx, uidList)
	ret0, _ := ret[0].(map[uint32]*numeric.UserNumericValue)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetPersonalNumeric indicates an expected call of BatchGetPersonalNumeric.
func (mr *MockIClientMockRecorder) BatchGetPersonalNumeric(ctx, uidList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPersonalNumeric", reflect.TypeOf((*MockIClient)(nil).BatchGetPersonalNumeric), ctx, uidList)
}

// BatchRecordSendGiftEvent mocks base method.
func (m *MockIClient) BatchRecordSendGiftEvent(ctx context.Context, Req *numericsvr.BatchRecordSendGiftEventReq) (*numericsvr.BatchRecordSendGiftEventResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchRecordSendGiftEvent", ctx, Req)
	ret0, _ := ret[0].(*numericsvr.BatchRecordSendGiftEventResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchRecordSendGiftEvent indicates an expected call of BatchRecordSendGiftEvent.
func (mr *MockIClientMockRecorder) BatchRecordSendGiftEvent(ctx, Req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRecordSendGiftEvent", reflect.TypeOf((*MockIClient)(nil).BatchRecordSendGiftEvent), ctx, Req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetGuildGiftTotalValue mocks base method.
func (m *MockIClient) GetGuildGiftTotalValue(ctx context.Context, guildId uint32) (*numericsvr.GetGuildGiftTotalValueResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildGiftTotalValue", ctx, guildId)
	ret0, _ := ret[0].(*numericsvr.GetGuildGiftTotalValueResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildGiftTotalValue indicates an expected call of GetGuildGiftTotalValue.
func (mr *MockIClientMockRecorder) GetGuildGiftTotalValue(ctx, guildId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildGiftTotalValue", reflect.TypeOf((*MockIClient)(nil).GetGuildGiftTotalValue), ctx, guildId)
}

// GetPersonalNumeric mocks base method.
func (m *MockIClient) GetPersonalNumeric(ctx context.Context, uid uint32) (uint32, uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalNumeric", ctx, uid)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// GetPersonalNumeric indicates an expected call of GetPersonalNumeric.
func (mr *MockIClientMockRecorder) GetPersonalNumeric(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalNumeric", reflect.TypeOf((*MockIClient)(nil).GetPersonalNumeric), ctx, uid)
}

// GetPersonalNumericData mocks base method.
func (m *MockIClient) GetPersonalNumericData(ctx context.Context, uid uint32) (*numericsvr.GetPersonalNumericResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalNumericData", ctx, uid)
	ret0, _ := ret[0].(*numericsvr.GetPersonalNumericResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPersonalNumericData indicates an expected call of GetPersonalNumericData.
func (mr *MockIClientMockRecorder) GetPersonalNumericData(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalNumericData", reflect.TypeOf((*MockIClient)(nil).GetPersonalNumericData), ctx, uid)
}

// GetPersonalRankingFromLocalCache mocks base method.
func (m *MockIClient) GetPersonalRankingFromLocalCache(ctx context.Context, req *numericsvr.GetPersonalRankingReq) (*numericsvr.GetPersonalRankingResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalRankingFromLocalCache", ctx, req)
	ret0, _ := ret[0].(*numericsvr.GetPersonalRankingResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPersonalRankingFromLocalCache indicates an expected call of GetPersonalRankingFromLocalCache.
func (mr *MockIClientMockRecorder) GetPersonalRankingFromLocalCache(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalRankingFromLocalCache", reflect.TypeOf((*MockIClient)(nil).GetPersonalRankingFromLocalCache), ctx, req)
}

// GetRankList mocks base method.
func (m *MockIClient) GetRankList(ctx context.Context, req *numericsvr.GetRankListReq) (*numericsvr.GetRankListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRankList", ctx, req)
	ret0, _ := ret[0].(*numericsvr.GetRankListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRankList indicates an expected call of GetRankList.
func (mr *MockIClientMockRecorder) GetRankList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRankList", reflect.TypeOf((*MockIClient)(nil).GetRankList), ctx, req)
}

// RecordSendGiftEvent mocks base method.
func (m *MockIClient) RecordSendGiftEvent(ctx context.Context, uid uint32, ev *numericsvr.RecordSendGiftEventReq) (*numericsvr.RecordSendGiftEventResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordSendGiftEvent", ctx, uid, ev)
	ret0, _ := ret[0].(*numericsvr.RecordSendGiftEventResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// RecordSendGiftEvent indicates an expected call of RecordSendGiftEvent.
func (mr *MockIClientMockRecorder) RecordSendGiftEvent(ctx, uid, ev interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordSendGiftEvent", reflect.TypeOf((*MockIClient)(nil).RecordSendGiftEvent), ctx, uid, ev)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
