// Code generated by MockGen. DO NOT EDIT.
// Source: E:\git\quicksilver\clients\albumsvr\iclient.go

// Package albumsvr is a generated GoMock package.
package albumsvr

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	Album "golang.52tt.com/protocol/services/albumsvr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// CreatePhoto mocks base method.
func (m *MockIClient) CreatePhoto(ctx context.Context, uin uint32, req *Album.CreatePhotoReq) (*Album.CreatePhotoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePhoto", ctx, uin, req)
	ret0, _ := ret[0].(*Album.CreatePhotoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePhoto indicates an expected call of CreatePhoto.
func (mr *MockIClientMockRecorder) CreatePhoto(ctx, uin, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePhoto", reflect.TypeOf((*MockIClient)(nil).CreatePhoto), ctx, uin, req)
}

// DeletePhoto mocks base method.
func (m *MockIClient) DeletePhoto(ctx context.Context, uin uint32, req *Album.DeletePhotoReq) (*Album.DeletePhotoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePhoto", ctx, uin, req)
	ret0, _ := ret[0].(*Album.DeletePhotoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeletePhoto indicates an expected call of DeletePhoto.
func (mr *MockIClientMockRecorder) DeletePhoto(ctx, uin, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePhoto", reflect.TypeOf((*MockIClient)(nil).DeletePhoto), ctx, uin, req)
}

// GetChildAlbumPhotoCount mocks base method.
func (m *MockIClient) GetChildAlbumPhotoCount(ctx context.Context, guildId uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChildAlbumPhotoCount", ctx, guildId)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChildAlbumPhotoCount indicates an expected call of GetChildAlbumPhotoCount.
func (mr *MockIClientMockRecorder) GetChildAlbumPhotoCount(ctx, guildId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChildAlbumPhotoCount", reflect.TypeOf((*MockIClient)(nil).GetChildAlbumPhotoCount), ctx, guildId)
}

// GetChildAlbumPhotoList mocks base method.
func (m *MockIClient) GetChildAlbumPhotoList(ctx context.Context, uin uint32, req *Album.GetChildAlbumPhotoListReq) (*Album.GetChildAlbumPhotoListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChildAlbumPhotoList", ctx, uin, req)
	ret0, _ := ret[0].(*Album.GetChildAlbumPhotoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChildAlbumPhotoList indicates an expected call of GetChildAlbumPhotoList.
func (mr *MockIClientMockRecorder) GetChildAlbumPhotoList(ctx, uin, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChildAlbumPhotoList", reflect.TypeOf((*MockIClient)(nil).GetChildAlbumPhotoList), ctx, uin, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
