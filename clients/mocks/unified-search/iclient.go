// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/unified-search/client (interfaces: IUnifiedSearchClient)

// Package unifiedSearch is a generated GoMock package.
package unifiedSearch

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	protocol "golang.52tt.com/pkg/protocol"
	unified_search "golang.52tt.com/protocol/services/unified-search"
)

// MockIUnifiedSearchClient is a mock of IUnifiedSearchClient interface.
type MockIUnifiedSearchClient struct {
	ctrl     *gomock.Controller
	recorder *MockIUnifiedSearchClientMockRecorder
}

// MockIUnifiedSearchClientMockRecorder is the mock recorder for MockIUnifiedSearchClient.
type MockIUnifiedSearchClientMockRecorder struct {
	mock *MockIUnifiedSearchClient
}

// NewMockIUnifiedSearchClient creates a new mock instance.
func NewMockIUnifiedSearchClient(ctrl *gomock.Controller) *MockIUnifiedSearchClient {
	mock := &MockIUnifiedSearchClient{ctrl: ctrl}
	mock.recorder = &MockIUnifiedSearchClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIUnifiedSearchClient) EXPECT() *MockIUnifiedSearchClientMockRecorder {
	return m.recorder
}

// AddRisky mocks base method.
func (m *MockIUnifiedSearchClient) AddRisky(arg0 context.Context, arg1 unified_search.AddRiskyReq_SceneType, arg2 ...*unified_search.RiskyObject) protocol.ServerError {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddRisky", varargs...)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddRisky indicates an expected call of AddRisky.
func (mr *MockIUnifiedSearchClientMockRecorder) AddRisky(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRisky", reflect.TypeOf((*MockIUnifiedSearchClient)(nil).AddRisky), varargs...)
}

// CheckRisky mocks base method.
func (m *MockIUnifiedSearchClient) CheckRisky(arg0 context.Context, arg1 ...*unified_search.RiskyObject) ([]bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckRisky", varargs...)
	ret0, _ := ret[0].([]bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckRisky indicates an expected call of CheckRisky.
func (mr *MockIUnifiedSearchClientMockRecorder) CheckRisky(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRisky", reflect.TypeOf((*MockIUnifiedSearchClient)(nil).CheckRisky), varargs...)
}

// CheckRiskyById mocks base method.
func (m *MockIUnifiedSearchClient) CheckRiskyById(arg0 context.Context, arg1 unified_search.ObjectType, arg2 []uint32) ([]bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRiskyById", arg0, arg1, arg2)
	ret0, _ := ret[0].([]bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckRiskyById indicates an expected call of CheckRiskyById.
func (mr *MockIUnifiedSearchClientMockRecorder) CheckRiskyById(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRiskyById", reflect.TypeOf((*MockIUnifiedSearchClient)(nil).CheckRiskyById), arg0, arg1, arg2)
}

// RemoveRisky mocks base method.
func (m *MockIUnifiedSearchClient) RemoveRisky(arg0 context.Context, arg1 ...*unified_search.RiskyObject) protocol.ServerError {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RemoveRisky", varargs...)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// RemoveRisky indicates an expected call of RemoveRisky.
func (mr *MockIUnifiedSearchClientMockRecorder) RemoveRisky(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveRisky", reflect.TypeOf((*MockIUnifiedSearchClient)(nil).RemoveRisky), varargs...)
}
