// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/cybros/arbiter/v2 (interfaces: VideoClient)

// Package image is a generated GoMock package.
package image

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	cybros_arbiter_v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	grpc "google.golang.org/grpc"
)

// MockVideoClient is a mock of VideoClient interface.
type MockVideoClient struct {
	ctrl     *gomock.Controller
	recorder *MockVideoClientMockRecorder
}

// MockVideoClientMockRecorder is the mock recorder for MockVideoClient.
type MockVideoClientMockRecorder struct {
	mock *MockVideoClient
}

// NewMockVideoClient creates a new mock instance.
func NewMockVideoClient(ctrl *gomock.Controller) *MockVideoClient {
	mock := &MockVideoClient{ctrl: ctrl}
	mock.recorder = &MockVideoClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVideoClient) EXPECT() *MockVideoClientMockRecorder {
	return m.recorder
}

// AsyncScanVideo mocks base method.
func (m *MockVideoClient) AsyncScanVideo(arg0 context.Context, arg1 *cybros_arbiter_v2.ScanVideoReq, arg2 ...grpc.CallOption) (*cybros_arbiter_v2.ScanVideoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AsyncScanVideo", varargs...)
	ret0, _ := ret[0].(*cybros_arbiter_v2.ScanVideoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AsyncScanVideo indicates an expected call of AsyncScanVideo.
func (mr *MockVideoClientMockRecorder) AsyncScanVideo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncScanVideo", reflect.TypeOf((*MockVideoClient)(nil).AsyncScanVideo), varargs...)
}

// QueryVideoTaskResult mocks base method.
func (m *MockVideoClient) QueryVideoTaskResult(arg0 context.Context, arg1 *cybros_arbiter_v2.QueryVideoTaskResultReq, arg2 ...grpc.CallOption) (*cybros_arbiter_v2.QueryVideoTaskResultResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryVideoTaskResult", varargs...)
	ret0, _ := ret[0].(*cybros_arbiter_v2.QueryVideoTaskResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryVideoTaskResult indicates an expected call of QueryVideoTaskResult.
func (mr *MockVideoClientMockRecorder) QueryVideoTaskResult(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryVideoTaskResult", reflect.TypeOf((*MockVideoClient)(nil).QueryVideoTaskResult), varargs...)
}
