// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/cybros/arbiter/v2 (interfaces: ImageClient)

// Package image is a generated GoMock package.
package image

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	cybros_arbiter_v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	grpc "google.golang.org/grpc"
)

// MockImageClient is a mock of ImageClient interface.
type MockImageClient struct {
	ctrl     *gomock.Controller
	recorder *MockImageClientMockRecorder
}

// MockImageClientMockRecorder is the mock recorder for MockImageClient.
type MockImageClientMockRecorder struct {
	mock *MockImageClient
}

// NewMockImageClient creates a new mock instance.
func NewMockImageClient(ctrl *gomock.Controller) *MockImageClient {
	mock := &MockImageClient{ctrl: ctrl}
	mock.recorder = &MockImageClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockImageClient) EXPECT() *MockImageClientMockRecorder {
	return m.recorder
}

// AsyncScanImage mocks base method.
func (m *MockImageClient) AsyncScanImage(arg0 context.Context, arg1 *cybros_arbiter_v2.ScanImageReq, arg2 ...grpc.CallOption) (*cybros_arbiter_v2.ScanImageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AsyncScanImage", varargs...)
	ret0, _ := ret[0].(*cybros_arbiter_v2.ScanImageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AsyncScanImage indicates an expected call of AsyncScanImage.
func (mr *MockImageClientMockRecorder) AsyncScanImage(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncScanImage", reflect.TypeOf((*MockImageClient)(nil).AsyncScanImage), varargs...)
}

// ScanImage mocks base method.
func (m *MockImageClient) ScanImage(arg0 context.Context, arg1 *cybros_arbiter_v2.ScanImageReq, arg2 ...grpc.CallOption) (*cybros_arbiter_v2.ScanImageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ScanImage", varargs...)
	ret0, _ := ret[0].(*cybros_arbiter_v2.ScanImageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScanImage indicates an expected call of ScanImage.
func (mr *MockImageClientMockRecorder) ScanImage(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScanImage", reflect.TypeOf((*MockImageClient)(nil).ScanImage), varargs...)
}
