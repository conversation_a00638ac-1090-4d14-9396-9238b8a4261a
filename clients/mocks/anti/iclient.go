// Code generated by MockGen. DO NOT EDIT.
// Source: iclient.go

// Package anti is a generated GoMock package.
package anti

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	Anti "golang.52tt.com/protocol/services/antisvr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetUserLastLoginInfo mocks base method.
func (m *MockIClient) BatchGetUserLastLoginInfo(ctx context.Context, uin uint32, uids []uint32) (*Anti.BatchGetUserLastLoginInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserLastLoginInfo", ctx, uin, uids)
	ret0, _ := ret[0].(*Anti.BatchGetUserLastLoginInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserLastLoginInfo indicates an expected call of BatchGetUserLastLoginInfo.
func (mr *MockIClientMockRecorder) BatchGetUserLastLoginInfo(ctx, uin, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserLastLoginInfo", reflect.TypeOf((*MockIClient)(nil).BatchGetUserLastLoginInfo), ctx, uin, uids)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CheckUsualDevice mocks base method.
func (m *MockIClient) CheckUsualDevice(ctx context.Context, uid uint32, req *Anti.CheckUsualDeviceReq) (*Anti.CheckUsualDeviceResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUsualDevice", ctx, uid, req)
	ret0, _ := ret[0].(*Anti.CheckUsualDeviceResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckUsualDevice indicates an expected call of CheckUsualDevice.
func (mr *MockIClientMockRecorder) CheckUsualDevice(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUsualDevice", reflect.TypeOf((*MockIClient)(nil).CheckUsualDevice), ctx, uid, req)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetDeviceIdInfo mocks base method.
func (m *MockIClient) GetDeviceIdInfo(ctx context.Context, uid uint32, req *Anti.GetDeviceIdInfoReq) (*Anti.GetDeviceIdInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeviceIdInfo", ctx, uid, req)
	ret0, _ := ret[0].(*Anti.GetDeviceIdInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetDeviceIdInfo indicates an expected call of GetDeviceIdInfo.
func (mr *MockIClientMockRecorder) GetDeviceIdInfo(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeviceIdInfo", reflect.TypeOf((*MockIClient)(nil).GetDeviceIdInfo), ctx, uid, req)
}

// GetLastVerifySuccessInfo mocks base method.
func (m *MockIClient) GetLastVerifySuccessInfo(ctx context.Context, uin uint32, req *Anti.GetLastVerifySuccessInfoReq) (*Anti.GetLastVerifySuccessInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastVerifySuccessInfo", ctx, uin, req)
	ret0, _ := ret[0].(*Anti.GetLastVerifySuccessInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLastVerifySuccessInfo indicates an expected call of GetLastVerifySuccessInfo.
func (mr *MockIClientMockRecorder) GetLastVerifySuccessInfo(ctx, uin, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastVerifySuccessInfo", reflect.TypeOf((*MockIClient)(nil).GetLastVerifySuccessInfo), ctx, uin, req)
}

// GetUserLastLoginInfo mocks base method.
func (m *MockIClient) GetUserLastLoginInfo(ctx context.Context, uin, UID uint32) (*Anti.UserLoginInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLastLoginInfo", ctx, uin, UID)
	ret0, _ := ret[0].(*Anti.UserLoginInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserLastLoginInfo indicates an expected call of GetUserLastLoginInfo.
func (mr *MockIClientMockRecorder) GetUserLastLoginInfo(ctx, uin, UID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLastLoginInfo", reflect.TypeOf((*MockIClient)(nil).GetUserLastLoginInfo), ctx, uin, UID)
}

// GetUserLoginDevice mocks base method.
func (m *MockIClient) GetUserLoginDevice(ctx context.Context, uin, UID uint32) ([]*Anti.UserLoginDevice, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLoginDevice", ctx, uin, UID)
	ret0, _ := ret[0].([]*Anti.UserLoginDevice)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserLoginDevice indicates an expected call of GetUserLoginDevice.
func (mr *MockIClientMockRecorder) GetUserLoginDevice(ctx, uin, UID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLoginDevice", reflect.TypeOf((*MockIClient)(nil).GetUserLoginDevice), ctx, uin, UID)
}

// GetUserLoginHistory mocks base method.
func (m *MockIClient) GetUserLoginHistory(ctx context.Context, uid uint32, req *Anti.GetUserLoginHistoryReq) (*Anti.GetUserLoginHistoryResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLoginHistory", ctx, uid, req)
	ret0, _ := ret[0].(*Anti.GetUserLoginHistoryResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserLoginHistory indicates an expected call of GetUserLoginHistory.
func (mr *MockIClientMockRecorder) GetUserLoginHistory(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLoginHistory", reflect.TypeOf((*MockIClient)(nil).GetUserLoginHistory), ctx, uid, req)
}

// GetUserLoginWithDevice mocks base method.
func (m *MockIClient) GetUserLoginWithDevice(ctx context.Context, uin uint32, deviceId string) (*Anti.GetUserLoginWithDeviceRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLoginWithDevice", ctx, uin, deviceId)
	ret0, _ := ret[0].(*Anti.GetUserLoginWithDeviceRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserLoginWithDevice indicates an expected call of GetUserLoginWithDevice.
func (mr *MockIClientMockRecorder) GetUserLoginWithDevice(ctx, uin, deviceId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLoginWithDevice", reflect.TypeOf((*MockIClient)(nil).GetUserLoginWithDevice), ctx, uin, deviceId)
}

// GetUserProfile mocks base method.
func (m *MockIClient) GetUserProfile(ctx context.Context, uin uint32, withFootmark, withDetail bool) (*Anti.GetUserProfileRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfile", ctx, uin, withFootmark, withDetail)
	ret0, _ := ret[0].(*Anti.GetUserProfileRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserProfile indicates an expected call of GetUserProfile.
func (mr *MockIClientMockRecorder) GetUserProfile(ctx, uin, withFootmark, withDetail interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfile", reflect.TypeOf((*MockIClient)(nil).GetUserProfile), ctx, uin, withFootmark, withDetail)
}

// GetUserRegInfo mocks base method.
func (m *MockIClient) GetUserRegInfo(ctx context.Context, uid uint32, req *Anti.GetUserRegInfoReq) (*Anti.GetUserRegInfoRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRegInfo", ctx, uid, req)
	ret0, _ := ret[0].(*Anti.GetUserRegInfoRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserRegInfo indicates an expected call of GetUserRegInfo.
func (mr *MockIClientMockRecorder) GetUserRegInfo(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRegInfo", reflect.TypeOf((*MockIClient)(nil).GetUserRegInfo), ctx, uid, req)
}

// GetUserUsualDevice mocks base method.
func (m *MockIClient) GetUserUsualDevice(ctx context.Context, uin, UID, beginTime, endTime uint32, limit int32) ([]*Anti.UserLoginDevice, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserUsualDevice", ctx, uin, UID, beginTime, endTime, limit)
	ret0, _ := ret[0].([]*Anti.UserLoginDevice)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserUsualDevice indicates an expected call of GetUserUsualDevice.
func (mr *MockIClientMockRecorder) GetUserUsualDevice(ctx, uin, UID, beginTime, endTime, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserUsualDevice", reflect.TypeOf((*MockIClient)(nil).GetUserUsualDevice), ctx, uin, UID, beginTime, endTime, limit)
}

// RecordUsualDevice mocks base method.
func (m *MockIClient) RecordUsualDevice(ctx context.Context, uid uint32, req *Anti.RecordUsualDeviceReq) (*Anti.RecordUsualDeviceResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordUsualDevice", ctx, uid, req)
	ret0, _ := ret[0].(*Anti.RecordUsualDeviceResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// RecordUsualDevice indicates an expected call of RecordUsualDevice.
func (mr *MockIClientMockRecorder) RecordUsualDevice(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordUsualDevice", reflect.TypeOf((*MockIClient)(nil).RecordUsualDevice), ctx, uid, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
