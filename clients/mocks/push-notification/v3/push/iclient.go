// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/push-notification/v3/push/iclient.go

// Package pushV3 is a generated GoMock package.
package pushV3

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	push_server "golang.52tt.com/protocol/services/push-notification/v3"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddExtParams mocks base method.
func (m *MockIClient) AddExtParams(notification *push_server.Notification, pushType push_server.PushType, optUser push_server.OptUser) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddExtParams", notification, pushType, optUser)
}

// AddExtParams indicates an expected call of AddExtParams.
func (mr *MockIClientMockRecorder) AddExtParams(notification, pushType, optUser interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddExtParams", reflect.TypeOf((*MockIClient)(nil).AddExtParams), notification, pushType, optUser)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// InitPartOfTask mocks base method.
func (m *MockIClient) InitPartOfTask(ctx context.Context, in *push_server.InitPartOfTaskReq) (*push_server.InitPartOfTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitPartOfTask", ctx, in)
	ret0, _ := ret[0].(*push_server.InitPartOfTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitPartOfTask indicates an expected call of InitPartOfTask.
func (mr *MockIClientMockRecorder) InitPartOfTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitPartOfTask", reflect.TypeOf((*MockIClient)(nil).InitPartOfTask), ctx, in)
}

// NewTask mocks base method.
func (m *MockIClient) NewTask(ctx context.Context, in *push_server.NewTaskReq) (*push_server.NewTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewTask", ctx, in)
	ret0, _ := ret[0].(*push_server.NewTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewTask indicates an expected call of NewTask.
func (mr *MockIClientMockRecorder) NewTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewTask", reflect.TypeOf((*MockIClient)(nil).NewTask), ctx, in)
}

// SaveTask mocks base method.
func (m *MockIClient) SaveTask(ctx context.Context, in *push_server.SaveTaskReq) (*push_server.SaveTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveTask", ctx, in)
	ret0, _ := ret[0].(*push_server.SaveTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveTask indicates an expected call of SaveTask.
func (mr *MockIClientMockRecorder) SaveTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveTask", reflect.TypeOf((*MockIClient)(nil).SaveTask), ctx, in)
}

// SendPushTask mocks base method.
func (m *MockIClient) SendPushTask(ctx context.Context, in *push_server.SendPushTaskReq) (*push_server.SendPushTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPushTask", ctx, in)
	ret0, _ := ret[0].(*push_server.SendPushTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPushTask indicates an expected call of SendPushTask.
func (mr *MockIClientMockRecorder) SendPushTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPushTask", reflect.TypeOf((*MockIClient)(nil).SendPushTask), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
