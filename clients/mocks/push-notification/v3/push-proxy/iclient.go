// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/push-notification/v3/push-proxy/iclient.go

// Package push_proxy is a generated GoMock package.
package push_proxy

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	push_server "golang.52tt.com/protocol/services/push-notification/v3"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// QueryDeviceStatusByUids mocks base method.
func (m *MockIClient) QueryDeviceStatusByUids(ctx context.Context, in *push_server.QueryDeviceStatusByUidsReq) (*push_server.QueryDeviceStatusByUidsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryDeviceStatusByUids", ctx, in)
	ret0, _ := ret[0].(*push_server.QueryDeviceStatusByUidsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryDeviceStatusByUids indicates an expected call of QueryDeviceStatusByUids.
func (mr *MockIClientMockRecorder) QueryDeviceStatusByUids(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryDeviceStatusByUids", reflect.TypeOf((*MockIClient)(nil).QueryDeviceStatusByUids), ctx, in)
}

// SendCorePush mocks base method.
func (m *MockIClient) SendCorePush(ctx context.Context, in *push_server.SendCorePushReq) (*push_server.SendCorePushResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendCorePush", ctx, in)
	ret0, _ := ret[0].(*push_server.SendCorePushResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendCorePush indicates an expected call of SendCorePush.
func (mr *MockIClientMockRecorder) SendCorePush(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCorePush", reflect.TypeOf((*MockIClient)(nil).SendCorePush), ctx, in)
}

// SendCustomOpPush mocks base method.
func (m *MockIClient) SendCustomOpPush(ctx context.Context, in *push_server.SendCustomOpPushReq) (*push_server.SendCustomOpPushResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendCustomOpPush", ctx, in)
	ret0, _ := ret[0].(*push_server.SendCustomOpPushResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendCustomOpPush indicates an expected call of SendCustomOpPush.
func (mr *MockIClientMockRecorder) SendCustomOpPush(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCustomOpPush", reflect.TypeOf((*MockIClient)(nil).SendCustomOpPush), ctx, in)
}

// SendOpPush mocks base method.
func (m *MockIClient) SendOpPush(ctx context.Context, in *push_server.SendOpPushReq) (*push_server.SendOpPushResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendOpPush", ctx, in)
	ret0, _ := ret[0].(*push_server.SendOpPushResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendOpPush indicates an expected call of SendOpPush.
func (mr *MockIClientMockRecorder) SendOpPush(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOpPush", reflect.TypeOf((*MockIClient)(nil).SendOpPush), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
