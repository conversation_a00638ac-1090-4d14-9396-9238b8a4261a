// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/push-notification/v3/token/iclient.go

// Package push_token is a generated GoMock package.
package push_token

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	push_server "golang.52tt.com/protocol/services/push-notification/v3"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// QueryDeviceToken mocks base method.
func (m *MockIClient) QueryDeviceToken(ctx context.Context, in *push_server.QueryDeviceTokenReq, opts ...grpc.CallOption) (*push_server.QueryDeviceTokenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryDeviceToken", varargs...)
	ret0, _ := ret[0].(*push_server.QueryDeviceTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryDeviceToken indicates an expected call of QueryDeviceToken.
func (mr *MockIClientMockRecorder) QueryDeviceToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryDeviceToken", reflect.TypeOf((*MockIClient)(nil).QueryDeviceToken), varargs...)
}

// RegisterDeviceToken mocks base method.
func (m *MockIClient) RegisterDeviceToken(ctx context.Context, in *push_server.RegisterDeviceTokenReq, opts ...grpc.CallOption) (*push_server.RegisterDeviceTokenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RegisterDeviceToken", varargs...)
	ret0, _ := ret[0].(*push_server.RegisterDeviceTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RegisterDeviceToken indicates an expected call of RegisterDeviceToken.
func (mr *MockIClientMockRecorder) RegisterDeviceToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterDeviceToken", reflect.TypeOf((*MockIClient)(nil).RegisterDeviceToken), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UnregisterDeviceToken mocks base method.
func (m *MockIClient) UnregisterDeviceToken(ctx context.Context, in *push_server.UnregisterDeviceTokenReq, opts ...grpc.CallOption) (*push_server.UnregisterDeviceTokenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnregisterDeviceToken", varargs...)
	ret0, _ := ret[0].(*push_server.UnregisterDeviceTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnregisterDeviceToken indicates an expected call of UnregisterDeviceToken.
func (mr *MockIClientMockRecorder) UnregisterDeviceToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnregisterDeviceToken", reflect.TypeOf((*MockIClient)(nil).UnregisterDeviceToken), varargs...)
}
