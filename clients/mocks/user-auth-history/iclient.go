// Code generated by MockGen. DO NOT EDIT.
// Source: /home/<USER>/git/quicksilver/clients/user-auth-history/iclient.go

// Package user_auth_history is a generated GoMock package.
package user_auth_history

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	user_auth_history "golang.52tt.com/clients/user-auth-history"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	user_auth_history0 "golang.52tt.com/protocol/services/user-auth-history"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetUserLastLoginInfo mocks base method.
func (m *MockIClient) BatchGetUserLastLoginInfo(ctx context.Context, uids []uint64) ([]*user_auth_history.UserLoginInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserLastLoginInfo", ctx, uids)
	ret0, _ := ret[0].([]*user_auth_history.UserLoginInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserLastLoginInfo indicates an expected call of BatchGetUserLastLoginInfo.
func (mr *MockIClientMockRecorder) BatchGetUserLastLoginInfo(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserLastLoginInfo", reflect.TypeOf((*MockIClient)(nil).BatchGetUserLastLoginInfo), ctx, uids)
}

// BatchGetUserLoginDevice mocks base method.
func (m *MockIClient) BatchGetUserLoginDevice(ctx context.Context, uids []uint64) ([]*user_auth_history.BatchUserLoginDevice, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserLoginDevice", ctx, uids)
	ret0, _ := ret[0].([]*user_auth_history.BatchUserLoginDevice)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserLoginDevice indicates an expected call of BatchGetUserLoginDevice.
func (mr *MockIClientMockRecorder) BatchGetUserLoginDevice(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserLoginDevice", reflect.TypeOf((*MockIClient)(nil).BatchGetUserLoginDevice), ctx, uids)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetDeviceIdInfo mocks base method.
func (m *MockIClient) GetDeviceIdInfo(ctx context.Context, hexDeviceId string) (*user_auth_history.GetDeviceIdInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeviceIdInfo", ctx, hexDeviceId)
	ret0, _ := ret[0].(*user_auth_history.GetDeviceIdInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetDeviceIdInfo indicates an expected call of GetDeviceIdInfo.
func (mr *MockIClientMockRecorder) GetDeviceIdInfo(ctx, hexDeviceId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeviceIdInfo", reflect.TypeOf((*MockIClient)(nil).GetDeviceIdInfo), ctx, hexDeviceId)
}

// GetLastVerifySuccessInfo mocks base method.
func (m *MockIClient) GetLastVerifySuccessInfo(ctx context.Context, uid uint64, reason uint32) (*user_auth_history0.GetLastVerifySuccessInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastVerifySuccessInfo", ctx, uid, reason)
	ret0, _ := ret[0].(*user_auth_history0.GetLastVerifySuccessInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLastVerifySuccessInfo indicates an expected call of GetLastVerifySuccessInfo.
func (mr *MockIClientMockRecorder) GetLastVerifySuccessInfo(ctx, uid, reason interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastVerifySuccessInfo", reflect.TypeOf((*MockIClient)(nil).GetLastVerifySuccessInfo), ctx, uid, reason)
}

// GetUserLastLoginInfo mocks base method.
func (m *MockIClient) GetUserLastLoginInfo(ctx context.Context, uid uint64) (*user_auth_history.UserLoginInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLastLoginInfo", ctx, uid)
	ret0, _ := ret[0].(*user_auth_history.UserLoginInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserLastLoginInfo indicates an expected call of GetUserLastLoginInfo.
func (mr *MockIClientMockRecorder) GetUserLastLoginInfo(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLastLoginInfo", reflect.TypeOf((*MockIClient)(nil).GetUserLastLoginInfo), ctx, uid)
}

// GetUserLoginDevice mocks base method.
func (m *MockIClient) GetUserLoginDevice(ctx context.Context, uid uint64) ([]*user_auth_history.UserLoginDevice, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLoginDevice", ctx, uid)
	ret0, _ := ret[0].([]*user_auth_history.UserLoginDevice)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserLoginDevice indicates an expected call of GetUserLoginDevice.
func (mr *MockIClientMockRecorder) GetUserLoginDevice(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLoginDevice", reflect.TypeOf((*MockIClient)(nil).GetUserLoginDevice), ctx, uid)
}

// GetUserLoginHistory mocks base method.
func (m *MockIClient) GetUserLoginHistory(ctx context.Context, uid uint64, start, end int64) (*user_auth_history.GetUserLoginHistoryResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLoginHistory", ctx, uid, start, end)
	ret0, _ := ret[0].(*user_auth_history.GetUserLoginHistoryResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserLoginHistory indicates an expected call of GetUserLoginHistory.
func (mr *MockIClientMockRecorder) GetUserLoginHistory(ctx, uid, start, end interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLoginHistory", reflect.TypeOf((*MockIClient)(nil).GetUserLoginHistory), ctx, uid, start, end)
}

// GetUserLoginWithDevice mocks base method.
func (m *MockIClient) GetUserLoginWithDevice(ctx context.Context, device string, start, end int64) (*user_auth_history.GetUserLoginWithDeviceResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLoginWithDevice", ctx, device, start, end)
	ret0, _ := ret[0].(*user_auth_history.GetUserLoginWithDeviceResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserLoginWithDevice indicates an expected call of GetUserLoginWithDevice.
func (mr *MockIClientMockRecorder) GetUserLoginWithDevice(ctx, device, start, end interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLoginWithDevice", reflect.TypeOf((*MockIClient)(nil).GetUserLoginWithDevice), ctx, device, start, end)
}

// GetUserRegInfo mocks base method.
func (m *MockIClient) GetUserRegInfo(ctx context.Context, uid uint64) (*user_auth_history.UserLoginInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRegInfo", ctx, uid)
	ret0, _ := ret[0].(*user_auth_history.UserLoginInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserRegInfo indicates an expected call of GetUserRegInfo.
func (mr *MockIClientMockRecorder) GetUserRegInfo(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRegInfo", reflect.TypeOf((*MockIClient)(nil).GetUserRegInfo), ctx, uid)
}

// IsUserInvalid mocks base method.
func (m *MockIClient) IsUserInvalid(ctx context.Context, uid uint64) (bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsUserInvalid", ctx, uid)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// IsUserInvalid indicates an expected call of IsUserInvalid.
func (mr *MockIClientMockRecorder) IsUserInvalid(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUserInvalid", reflect.TypeOf((*MockIClient)(nil).IsUserInvalid), ctx, uid)
}

// RecordUserLogin mocks base method.
func (m *MockIClient) RecordUserLogin(ctx context.Context, in *user_auth_history.RecordUserLoginReq) (bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordUserLogin", ctx, in)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// RecordUserLogin indicates an expected call of RecordUserLogin.
func (mr *MockIClientMockRecorder) RecordUserLogin(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordUserLogin", reflect.TypeOf((*MockIClient)(nil).RecordUserLogin), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// TrackUserLogin mocks base method.
func (m *MockIClient) TrackUserLogin(ctx context.Context, in *user_auth_history.TrackUserLoginReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TrackUserLogin", ctx, in)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// TrackUserLogin indicates an expected call of TrackUserLogin.
func (mr *MockIClientMockRecorder) TrackUserLogin(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TrackUserLogin", reflect.TypeOf((*MockIClient)(nil).TrackUserLogin), ctx, in)
}

// VerifyCAPTCHASuccess mocks base method.
func (m *MockIClient) VerifyCAPTCHASuccess(ctx context.Context, uid uint64, reason uint32, deviceId string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyCAPTCHASuccess", ctx, uid, reason, deviceId)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// VerifyCAPTCHASuccess indicates an expected call of VerifyCAPTCHASuccess.
func (mr *MockIClientMockRecorder) VerifyCAPTCHASuccess(ctx, uid, reason, deviceId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyCAPTCHASuccess", reflect.TypeOf((*MockIClient)(nil).VerifyCAPTCHASuccess), ctx, uid, reason, deviceId)
}
