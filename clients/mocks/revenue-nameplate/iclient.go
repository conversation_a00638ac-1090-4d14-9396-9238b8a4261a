// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/revenue-nameplate (interfaces: IClient)

// Package revenuenameplate is a generated GoMock package.
package revenuenameplate

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	revenuenameplate "golang.52tt.com/protocol/services/revenuenameplate"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AssignNamePlate mocks base method.
func (m *MockIClient) AssignNamePlate(arg0 context.Context, arg1 []*revenuenameplate.AssignRecord) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssignNamePlate", arg0, arg1)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AssignNamePlate indicates an expected call of AssignNamePlate.
func (mr *MockIClientMockRecorder) AssignNamePlate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssignNamePlate", reflect.TypeOf((*MockIClient)(nil).AssignNamePlate), arg0, arg1)
}

// BatchGetUserNameplates mocks base method.
func (m *MockIClient) BatchGetUserNameplates(arg0 context.Context, arg1 []uint32, arg2 revenuenameplate.ScenesType) (map[uint32][]*revenuenameplate.NameplateDetailInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserNameplates", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32][]*revenuenameplate.NameplateDetailInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserNameplates indicates an expected call of BatchGetUserNameplates.
func (mr *MockIClientMockRecorder) BatchGetUserNameplates(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserNameplates", reflect.TypeOf((*MockIClient)(nil).BatchGetUserNameplates), arg0, arg1, arg2)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetAllNameplates mocks base method.
func (m *MockIClient) GetAllNameplates(arg0 context.Context, arg1 *revenuenameplate.GetAllNameplatesReq) (*revenuenameplate.GetAllNameplatesResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllNameplates", arg0, arg1)
	ret0, _ := ret[0].(*revenuenameplate.GetAllNameplatesResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllNameplates indicates an expected call of GetAllNameplates.
func (mr *MockIClientMockRecorder) GetAllNameplates(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllNameplates", reflect.TypeOf((*MockIClient)(nil).GetAllNameplates), arg0, arg1)
}

// GetAssignRecord mocks base method.
func (m *MockIClient) GetAssignRecord(arg0 context.Context, arg1, arg2 uint32) ([]*revenuenameplate.AssignRecord, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssignRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*revenuenameplate.AssignRecord)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAssignRecord indicates an expected call of GetAssignRecord.
func (mr *MockIClientMockRecorder) GetAssignRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssignRecord", reflect.TypeOf((*MockIClient)(nil).GetAssignRecord), arg0, arg1, arg2)
}

// GetNamePlate mocks base method.
func (m *MockIClient) GetNamePlate(arg0 context.Context, arg1 []uint32) ([]*revenuenameplate.RevenueNameplateInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNamePlate", arg0, arg1)
	ret0, _ := ret[0].([]*revenuenameplate.RevenueNameplateInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetNamePlate indicates an expected call of GetNamePlate.
func (mr *MockIClientMockRecorder) GetNamePlate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNamePlate", reflect.TypeOf((*MockIClient)(nil).GetNamePlate), arg0, arg1)
}

// GetUserAllNameplateList mocks base method.
func (m *MockIClient) GetUserAllNameplateList(arg0 context.Context, arg1 uint32) (*revenuenameplate.GetUserAllNameplateListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAllNameplateList", arg0, arg1)
	ret0, _ := ret[0].(*revenuenameplate.GetUserAllNameplateListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserAllNameplateList indicates an expected call of GetUserAllNameplateList.
func (mr *MockIClientMockRecorder) GetUserAllNameplateList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAllNameplateList", reflect.TypeOf((*MockIClient)(nil).GetUserAllNameplateList), arg0, arg1)
}

// GetUserNameplateInfo mocks base method.
func (m *MockIClient) GetUserNameplateInfo(arg0 context.Context, arg1 uint32, arg2 revenuenameplate.ScenesType) ([]*revenuenameplate.NameplateDetailInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserNameplateInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*revenuenameplate.NameplateDetailInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserNameplateInfo indicates an expected call of GetUserNameplateInfo.
func (mr *MockIClientMockRecorder) GetUserNameplateInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserNameplateInfo", reflect.TypeOf((*MockIClient)(nil).GetUserNameplateInfo), arg0, arg1, arg2)
}

// SetNameplate mocks base method.
func (m *MockIClient) SetNameplate(arg0 context.Context, arg1 *revenuenameplate.RevenueNameplateInfo) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNameplate", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetNameplate indicates an expected call of SetNameplate.
func (mr *MockIClientMockRecorder) SetNameplate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNameplate", reflect.TypeOf((*MockIClient)(nil).SetNameplate), arg0, arg1)
}

// SetUserNameplateInfo mocks base method.
func (m *MockIClient) SetUserNameplateInfo(arg0 context.Context, arg1 uint32, arg2 []uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserNameplateInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// SetUserNameplateInfo indicates an expected call of SetUserNameplateInfo.
func (mr *MockIClientMockRecorder) SetUserNameplateInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserNameplateInfo", reflect.TypeOf((*MockIClient)(nil).SetUserNameplateInfo), arg0, arg1, arg2)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdateNameplate mocks base method.
func (m *MockIClient) UpdateNameplate(arg0 context.Context, arg1 *revenuenameplate.RevenueNameplateInfo) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNameplate", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateNameplate indicates an expected call of UpdateNameplate.
func (mr *MockIClientMockRecorder) UpdateNameplate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNameplate", reflect.TypeOf((*MockIClient)(nil).UpdateNameplate), arg0, arg1)
}
