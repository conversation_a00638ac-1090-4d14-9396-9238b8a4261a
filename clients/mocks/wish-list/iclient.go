// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/wish-list (interfaces: IClient)

// Package mocks is a generated GoMock package.
package wish_list

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	wish_list "golang.52tt.com/protocol/services/wish-list"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetAnchorWishList mocks base method.
func (m *MockIClient) GetAnchorWishList(arg0 context.Context, arg1 *wish_list.GetAnchorWishListReq, arg2 ...grpc.CallOption) (*wish_list.GetAnchorWishListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorWishList", varargs...)
	ret0, _ := ret[0].(*wish_list.GetAnchorWishListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorWishList indicates an expected call of GetAnchorWishList.
func (mr *MockIClientMockRecorder) GetAnchorWishList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorWishList", reflect.TypeOf((*MockIClient)(nil).GetAnchorWishList), varargs...)
}

// GetWishGiftList mocks base method.
func (m *MockIClient) GetWishGiftList(arg0 context.Context, arg1 *wish_list.GetWishGiftListReq, arg2 ...grpc.CallOption) (*wish_list.GetWishGiftListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWishGiftList", varargs...)
	ret0, _ := ret[0].(*wish_list.GetWishGiftListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWishGiftList indicates an expected call of GetWishGiftList.
func (mr *MockIClientMockRecorder) GetWishGiftList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWishGiftList", reflect.TypeOf((*MockIClient)(nil).GetWishGiftList), varargs...)
}

// GetWishGratitudeWords mocks base method.
func (m *MockIClient) GetWishGratitudeWords(arg0 context.Context, arg1 *wish_list.GetWishGratitudeWordsReq, arg2 ...grpc.CallOption) (*wish_list.GetWishGratitudeWordsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWishGratitudeWords", varargs...)
	ret0, _ := ret[0].(*wish_list.GetWishGratitudeWordsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWishGratitudeWords indicates an expected call of GetWishGratitudeWords.
func (mr *MockIClientMockRecorder) GetWishGratitudeWords(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWishGratitudeWords", reflect.TypeOf((*MockIClient)(nil).GetWishGratitudeWords), varargs...)
}

// SetAnchorWishList mocks base method.
func (m *MockIClient) SetAnchorWishList(arg0 context.Context, arg1 *wish_list.SetAnchorWishListReq, arg2 ...grpc.CallOption) (*wish_list.SetAnchorWishListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetAnchorWishList", varargs...)
	ret0, _ := ret[0].(*wish_list.SetAnchorWishListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAnchorWishList indicates an expected call of SetAnchorWishList.
func (mr *MockIClientMockRecorder) SetAnchorWishList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAnchorWishList", reflect.TypeOf((*MockIClient)(nil).SetAnchorWishList), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
