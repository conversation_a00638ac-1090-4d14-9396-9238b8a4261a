// Code generated by MockGen. DO NOT EDIT.
// Source: D:\TT-go\quicksilver\clients\security-go\iclient.go

// Package security_go is a generated GoMock package.
package security_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	security_go "golang.52tt.com/clients/security-go"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddUserUnregWhite mocks base method.
func (m *MockIClient) AddUserUnregWhite(ctx context.Context, UnregWhiteUserInfoList []*security_go.UnregWhiteUserInfo) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserUnregWhite", ctx, UnregWhiteUserInfoList)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddUserUnregWhite indicates an expected call of AddUserUnregWhite.
func (mr *MockIClientMockRecorder) AddUserUnregWhite(ctx, UnregWhiteUserInfoList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserUnregWhite", reflect.TypeOf((*MockIClient)(nil).AddUserUnregWhite), ctx, UnregWhiteUserInfoList)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetUnregWhiteUserInfo mocks base method.
func (m *MockIClient) GetUnregWhiteUserInfo(ctx context.Context, uidList []uint32, offset, limit uint32, beginTime, endTime int64) ([]*security_go.UnregWhiteUserInfo, uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnregWhiteUserInfo", ctx, uidList, offset, limit, beginTime, endTime)
	ret0, _ := ret[0].([]*security_go.UnregWhiteUserInfo)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// GetUnregWhiteUserInfo indicates an expected call of GetUnregWhiteUserInfo.
func (mr *MockIClientMockRecorder) GetUnregWhiteUserInfo(ctx, uidList, offset, limit, beginTime, endTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnregWhiteUserInfo", reflect.TypeOf((*MockIClient)(nil).GetUnregWhiteUserInfo), ctx, uidList, offset, limit, beginTime, endTime)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
