// Code generated by MockGen. DO NOT EDIT.
// Source: iclient.go

// Package missionlogic is a generated GoMock package.
package missionlogic

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	MissionLogic "golang.52tt.com/protocol/services/missionlogicsvr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// CollectMissionBonus mocks base method.
func (m *MockIClient) CollectMissionBonus(ctx context.Context, uid uint32, identifier string) (*MissionLogic.CollectMissionBonusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CollectMissionBonus", ctx, uid, identifier)
	ret0, _ := ret[0].(*MissionLogic.CollectMissionBonusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CollectMissionBonus indicates an expected call of CollectMissionBonus.
func (mr *MockIClientMockRecorder) CollectMissionBonus(ctx, uid, identifier interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CollectMissionBonus", reflect.TypeOf((*MockIClient)(nil).CollectMissionBonus), ctx, uid, identifier)
}

// GetStaticMissionConfig mocks base method.
func (m *MockIClient) GetStaticMissionConfig(ctx context.Context, uid uint32) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStaticMissionConfig", ctx, uid)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetStaticMissionConfig indicates an expected call of GetStaticMissionConfig.
func (mr *MockIClientMockRecorder) GetStaticMissionConfig(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaticMissionConfig", reflect.TypeOf((*MockIClient)(nil).GetStaticMissionConfig), ctx, uid)
}

// HandleBindPhoneMission mocks base method.
func (m *MockIClient) HandleBindPhoneMission(ctx context.Context, uid uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleBindPhoneMission", ctx, uid)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleBindPhoneMission indicates an expected call of HandleBindPhoneMission.
func (mr *MockIClientMockRecorder) HandleBindPhoneMission(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleBindPhoneMission", reflect.TypeOf((*MockIClient)(nil).HandleBindPhoneMission), ctx, uid)
}

// HandleChannelOnlineMission mocks base method.
func (m *MockIClient) HandleChannelOnlineMission(ctx context.Context, uid, cid, ctype uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleChannelOnlineMission", ctx, uid, cid, ctype)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleChannelOnlineMission indicates an expected call of HandleChannelOnlineMission.
func (mr *MockIClientMockRecorder) HandleChannelOnlineMission(ctx, uid, cid, ctype interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleChannelOnlineMission", reflect.TypeOf((*MockIClient)(nil).HandleChannelOnlineMission), ctx, uid, cid, ctype)
}

// HandleEnterChannelFromRankMission mocks base method.
func (m *MockIClient) HandleEnterChannelFromRankMission(ctx context.Context, uid uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleEnterChannelFromRankMission", ctx, uid)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleEnterChannelFromRankMission indicates an expected call of HandleEnterChannelFromRankMission.
func (mr *MockIClientMockRecorder) HandleEnterChannelFromRankMission(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleEnterChannelFromRankMission", reflect.TypeOf((*MockIClient)(nil).HandleEnterChannelFromRankMission), ctx, uid)
}

// HandleMission mocks base method.
func (m *MockIClient) HandleMission(ctx context.Context, uid, cmd uint32, body []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleMission", ctx, uid, cmd, body)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleMission indicates an expected call of HandleMission.
func (mr *MockIClientMockRecorder) HandleMission(ctx, uid, cmd, body interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleMission", reflect.TypeOf((*MockIClient)(nil).HandleMission), ctx, uid, cmd, body)
}

// IncreaseMissionFinishCount mocks base method.
func (m *MockIClient) IncreaseMissionFinishCount(ctx context.Context, uid, missionid, count uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncreaseMissionFinishCount", ctx, uid, missionid, count)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncreaseMissionFinishCount indicates an expected call of IncreaseMissionFinishCount.
func (mr *MockIClientMockRecorder) IncreaseMissionFinishCount(ctx, uid, missionid, count interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncreaseMissionFinishCount", reflect.TypeOf((*MockIClient)(nil).IncreaseMissionFinishCount), ctx, uid, missionid, count)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
