// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/super-star-protector/client (interfaces: IClient)

// Package super_star_protector_client is a generated GoMock package.
package super_star_protector_client

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	protocol "golang.52tt.com/pkg/protocol"
	super_star_protector "golang.52tt.com/protocol/services/super-star-protector"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CheckByChannelId mocks base method.
func (m *MockIClient) CheckByChannelId(arg0 context.Context, arg1 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckByChannelId", arg0, arg1)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckByChannelId indicates an expected call of CheckByChannelId.
func (mr *MockIClientMockRecorder) CheckByChannelId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckByChannelId", reflect.TypeOf((*MockIClient)(nil).CheckByChannelId), arg0, arg1)
}

// CheckByUid mocks base method.
func (m *MockIClient) CheckByUid(arg0 context.Context, arg1 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckByUid", arg0, arg1)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckByUid indicates an expected call of CheckByUid.
func (mr *MockIClientMockRecorder) CheckByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckByUid", reflect.TypeOf((*MockIClient)(nil).CheckByUid), arg0, arg1)
}

// GetAllStarList mocks base method.
func (m *MockIClient) GetAllStarList(arg0 context.Context) (*super_star_protector.GetAllStarListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllStarList", arg0)
	ret0, _ := ret[0].(*super_star_protector.GetAllStarListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllStarList indicates an expected call of GetAllStarList.
func (mr *MockIClientMockRecorder) GetAllStarList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllStarList", reflect.TypeOf((*MockIClient)(nil).GetAllStarList), arg0)
}

// GetStarFromCache mocks base method.
func (m *MockIClient) GetStarFromCache(arg0 context.Context, arg1 uint32) *super_star_protector.StarItem {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStarFromCache", arg0, arg1)
	ret0, _ := ret[0].(*super_star_protector.StarItem)
	return ret0
}

// GetStarFromCache indicates an expected call of GetStarFromCache.
func (mr *MockIClientMockRecorder) GetStarFromCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStarFromCache", reflect.TypeOf((*MockIClient)(nil).GetStarFromCache), arg0, arg1)
}
