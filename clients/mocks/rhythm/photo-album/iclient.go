// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/rhythm/photo-album/iclient.go

// Package rhythm_photo_album is a generated GoMock package.
package rhythm_photo_album

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	photo_album "golang.52tt.com/protocol/services/rhythm/photo-album"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetPhotoAlbum mocks base method.
func (m *MockIClient) GetPhotoAlbum(ctx context.Context, account string) (*photo_album.GetPhotoAlbumResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPhotoAlbum", ctx, account)
	ret0, _ := ret[0].(*photo_album.GetPhotoAlbumResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPhotoAlbum indicates an expected call of GetPhotoAlbum.
func (mr *MockIClientMockRecorder) GetPhotoAlbum(ctx, account interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPhotoAlbum", reflect.TypeOf((*MockIClient)(nil).GetPhotoAlbum), ctx, account)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdatePhotoAlbum mocks base method.
func (m *MockIClient) UpdatePhotoAlbum(ctx context.Context, account string, imgKeyList []string) (*photo_album.UpdatePhotoAlbumResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePhotoAlbum", ctx, account, imgKeyList)
	ret0, _ := ret[0].(*photo_album.UpdatePhotoAlbumResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdatePhotoAlbum indicates an expected call of UpdatePhotoAlbum.
func (mr *MockIClientMockRecorder) UpdatePhotoAlbum(ctx, account, imgKeyList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePhotoAlbum", reflect.TypeOf((*MockIClient)(nil).UpdatePhotoAlbum), ctx, account, imgKeyList)
}
