// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/award-center/iclient.go

// Package award_center is a generated GoMock package.
package award_center

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	award_center "golang.52tt.com/protocol/services/risk-control/award-center"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddAwardConf mocks base method.
func (m *MockIClient) AddAwardConf(ctx context.Context, req *award_center.AddAwardConfReq) (*award_center.AddAwardConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAwardConf", ctx, req)
	ret0, _ := ret[0].(*award_center.AddAwardConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddAwardConf indicates an expected call of AddAwardConf.
func (mr *MockIClientMockRecorder) AddAwardConf(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAwardConf", reflect.TypeOf((*MockIClient)(nil).AddAwardConf), ctx, req)
}

// AddBusiness mocks base method.
func (m *MockIClient) AddBusiness(ctx context.Context, req *award_center.AddBusinessConfReq) (*award_center.AddBusinessConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBusiness", ctx, req)
	ret0, _ := ret[0].(*award_center.AddBusinessConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddBusiness indicates an expected call of AddBusiness.
func (mr *MockIClientMockRecorder) AddBusiness(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBusiness", reflect.TypeOf((*MockIClient)(nil).AddBusiness), ctx, req)
}

// Award mocks base method.
func (m *MockIClient) Award(ctx context.Context, req *award_center.AwardReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Award", ctx, req)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// Award indicates an expected call of Award.
func (mr *MockIClientMockRecorder) Award(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Award", reflect.TypeOf((*MockIClient)(nil).Award), ctx, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DelAwardConf mocks base method.
func (m *MockIClient) DelAwardConf(ctx context.Context, businessId, giftType uint32, giftId string) (*award_center.DelAwardConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelAwardConf", ctx, businessId, giftType, giftId)
	ret0, _ := ret[0].(*award_center.DelAwardConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelAwardConf indicates an expected call of DelAwardConf.
func (mr *MockIClientMockRecorder) DelAwardConf(ctx, businessId, giftType, giftId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAwardConf", reflect.TypeOf((*MockIClient)(nil).DelAwardConf), ctx, businessId, giftType, giftId)
}

// DelBusinessConf mocks base method.
func (m *MockIClient) DelBusinessConf(ctx context.Context, businessId uint32) (*award_center.DelBusinessConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelBusinessConf", ctx, businessId)
	ret0, _ := ret[0].(*award_center.DelBusinessConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelBusinessConf indicates an expected call of DelBusinessConf.
func (mr *MockIClientMockRecorder) DelBusinessConf(ctx, businessId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBusinessConf", reflect.TypeOf((*MockIClient)(nil).DelBusinessConf), ctx, businessId)
}

// GetAwardConf mocks base method.
func (m *MockIClient) GetAwardConf(ctx context.Context, businessId, giftType uint32, giftId string) (*award_center.GetAwardConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardConf", ctx, businessId, giftType, giftId)
	ret0, _ := ret[0].(*award_center.GetAwardConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAwardConf indicates an expected call of GetAwardConf.
func (mr *MockIClientMockRecorder) GetAwardConf(ctx, businessId, giftType, giftId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardConf", reflect.TypeOf((*MockIClient)(nil).GetAwardConf), ctx, businessId, giftType, giftId)
}

// GetBusinessConf mocks base method.
func (m *MockIClient) GetBusinessConf(ctx context.Context, businessId uint32) (*award_center.GetBusinessConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBusinessConf", ctx, businessId)
	ret0, _ := ret[0].(*award_center.GetBusinessConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetBusinessConf indicates an expected call of GetBusinessConf.
func (mr *MockIClientMockRecorder) GetBusinessConf(ctx, businessId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBusinessConf", reflect.TypeOf((*MockIClient)(nil).GetBusinessConf), ctx, businessId)
}

// RollbackAward mocks base method.
func (m *MockIClient) RollbackAward(ctx context.Context, orderId string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RollbackAward", ctx, orderId)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// RollbackAward indicates an expected call of RollbackAward.
func (mr *MockIClientMockRecorder) RollbackAward(ctx, orderId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RollbackAward", reflect.TypeOf((*MockIClient)(nil).RollbackAward), ctx, orderId)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
