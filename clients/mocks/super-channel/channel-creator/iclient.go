// Code generated by MockGen. DO NOT EDIT.
// Source: G:\gitlab.ttyuyin.com\quicksilver\services\super-channel\channel-creator\client\iclient.go

// Package super_channel_creator_client is a generated GoMock package.
package super_channel_creator_client

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	protocol "golang.52tt.com/pkg/protocol"
	super_channel "golang.52tt.com/protocol/services/super-channel"
)

// MockSuperChannelCreatorIClient is a mock of SuperChannelCreatorIClient interface.
type MockSuperChannelCreatorIClient struct {
	ctrl     *gomock.Controller
	recorder *MockSuperChannelCreatorIClientMockRecorder
}

// MockSuperChannelCreatorIClientMockRecorder is the mock recorder for MockSuperChannelCreatorIClient.
type MockSuperChannelCreatorIClientMockRecorder struct {
	mock *MockSuperChannelCreatorIClient
}

// NewMockSuperChannelCreatorIClient creates a new mock instance.
func NewMockSuperChannelCreatorIClient(ctrl *gomock.Controller) *MockSuperChannelCreatorIClient {
	mock := &MockSuperChannelCreatorIClient{ctrl: ctrl}
	mock.recorder = &MockSuperChannelCreatorIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSuperChannelCreatorIClient) EXPECT() *MockSuperChannelCreatorIClientMockRecorder {
	return m.recorder
}

// CreateChannel mocks base method.
func (m *MockSuperChannelCreatorIClient) CreateChannel(ctx context.Context, uid uint32, name string) (uint32, uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChannel", ctx, uid, name)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// CreateChannel indicates an expected call of CreateChannel.
func (mr *MockSuperChannelCreatorIClientMockRecorder) CreateChannel(ctx, uid, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChannel", reflect.TypeOf((*MockSuperChannelCreatorIClient)(nil).CreateChannel), ctx, uid, name)
}

// DismissChannel mocks base method.
func (m *MockSuperChannelCreatorIClient) DismissChannel(ctx context.Context, channelId uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DismissChannel", ctx, channelId)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// DismissChannel indicates an expected call of DismissChannel.
func (mr *MockSuperChannelCreatorIClientMockRecorder) DismissChannel(ctx, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DismissChannel", reflect.TypeOf((*MockSuperChannelCreatorIClient)(nil).DismissChannel), ctx, channelId)
}

// GetActiveChannelList mocks base method.
func (m *MockSuperChannelCreatorIClient) GetActiveChannelList(ctx context.Context, channelType, activeAfter uint32) ([]uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveChannelList", ctx, channelType, activeAfter)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetActiveChannelList indicates an expected call of GetActiveChannelList.
func (mr *MockSuperChannelCreatorIClientMockRecorder) GetActiveChannelList(ctx, channelType, activeAfter interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveChannelList", reflect.TypeOf((*MockSuperChannelCreatorIClient)(nil).GetActiveChannelList), ctx, channelType, activeAfter)
}

// GetAllActiveChannelList mocks base method.
func (m *MockSuperChannelCreatorIClient) GetAllActiveChannelList(ctx context.Context, activeAfter uint32) ([]uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllActiveChannelList", ctx, activeAfter)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllActiveChannelList indicates an expected call of GetAllActiveChannelList.
func (mr *MockSuperChannelCreatorIClientMockRecorder) GetAllActiveChannelList(ctx, activeAfter interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllActiveChannelList", reflect.TypeOf((*MockSuperChannelCreatorIClient)(nil).GetAllActiveChannelList), ctx, activeAfter)
}

// GetAllActiveChannelListEx mocks base method.
func (m *MockSuperChannelCreatorIClient) GetAllActiveChannelListEx(ctx context.Context, activeAfter, activeEnd uint32) ([]uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllActiveChannelListEx", ctx, activeAfter, activeEnd)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllActiveChannelListEx indicates an expected call of GetAllActiveChannelListEx.
func (mr *MockSuperChannelCreatorIClientMockRecorder) GetAllActiveChannelListEx(ctx, activeAfter, activeEnd interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllActiveChannelListEx", reflect.TypeOf((*MockSuperChannelCreatorIClient)(nil).GetAllActiveChannelListEx), ctx, activeAfter, activeEnd)
}

// GetAllActiveChannelMap mocks base method.
func (m *MockSuperChannelCreatorIClient) GetAllActiveChannelMap(ctx context.Context, activeAfter uint32) (map[uint32][]uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllActiveChannelMap", ctx, activeAfter)
	ret0, _ := ret[0].(map[uint32][]uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllActiveChannelMap indicates an expected call of GetAllActiveChannelMap.
func (mr *MockSuperChannelCreatorIClientMockRecorder) GetAllActiveChannelMap(ctx, activeAfter interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllActiveChannelMap", reflect.TypeOf((*MockSuperChannelCreatorIClient)(nil).GetAllActiveChannelMap), ctx, activeAfter)
}

// GetAllActiveChannelMapEx mocks base method.
func (m *MockSuperChannelCreatorIClient) GetAllActiveChannelMapEx(ctx context.Context, activeAfter, activeEnd uint32) (map[uint32][]uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllActiveChannelMapEx", ctx, activeAfter, activeEnd)
	ret0, _ := ret[0].(map[uint32][]uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllActiveChannelMapEx indicates an expected call of GetAllActiveChannelMapEx.
func (mr *MockSuperChannelCreatorIClientMockRecorder) GetAllActiveChannelMapEx(ctx, activeAfter, activeEnd interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllActiveChannelMapEx", reflect.TypeOf((*MockSuperChannelCreatorIClient)(nil).GetAllActiveChannelMapEx), ctx, activeAfter, activeEnd)
}

// GetChannelAdmin mocks base method.
func (m *MockSuperChannelCreatorIClient) GetChannelAdmin(ctx context.Context, channelId uint32) (map[uint32]super_channel.ChannelAdminRole, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelAdmin", ctx, channelId)
	ret0, _ := ret[0].(map[uint32]super_channel.ChannelAdminRole)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelAdmin indicates an expected call of GetChannelAdmin.
func (mr *MockSuperChannelCreatorIClientMockRecorder) GetChannelAdmin(ctx, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelAdmin", reflect.TypeOf((*MockSuperChannelCreatorIClient)(nil).GetChannelAdmin), ctx, channelId)
}

// GetChannelBindInfo mocks base method.
func (m *MockSuperChannelCreatorIClient) GetChannelBindInfo(ctx context.Context, uid, channelType uint32) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelBindInfo", ctx, uid, channelType)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelBindInfo indicates an expected call of GetChannelBindInfo.
func (mr *MockSuperChannelCreatorIClientMockRecorder) GetChannelBindInfo(ctx, uid, channelType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelBindInfo", reflect.TypeOf((*MockSuperChannelCreatorIClient)(nil).GetChannelBindInfo), ctx, uid, channelType)
}

// GetChannelInfo mocks base method.
func (m *MockSuperChannelCreatorIClient) GetChannelInfo(ctx context.Context, channelId uint32) (*super_channel.ChannelInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelInfo", ctx, channelId)
	ret0, _ := ret[0].(*super_channel.ChannelInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelInfo indicates an expected call of GetChannelInfo.
func (mr *MockSuperChannelCreatorIClientMockRecorder) GetChannelInfo(ctx, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelInfo", reflect.TypeOf((*MockSuperChannelCreatorIClient)(nil).GetChannelInfo), ctx, channelId)
}

// ListChannelBindInfo mocks base method.
func (m *MockSuperChannelCreatorIClient) ListChannelBindInfo(ctx context.Context, channelType uint32) ([]*super_channel.ChannelBindInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListChannelBindInfo", ctx, channelType)
	ret0, _ := ret[0].([]*super_channel.ChannelBindInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListChannelBindInfo indicates an expected call of ListChannelBindInfo.
func (mr *MockSuperChannelCreatorIClientMockRecorder) ListChannelBindInfo(ctx, channelType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListChannelBindInfo", reflect.TypeOf((*MockSuperChannelCreatorIClient)(nil).ListChannelBindInfo), ctx, channelType)
}

// SetChannelAdmin mocks base method.
func (m *MockSuperChannelCreatorIClient) SetChannelAdmin(ctx context.Context, uid, channelId uint32, role super_channel.ChannelAdminRole) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelAdmin", ctx, uid, channelId, role)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// SetChannelAdmin indicates an expected call of SetChannelAdmin.
func (mr *MockSuperChannelCreatorIClientMockRecorder) SetChannelAdmin(ctx, uid, channelId, role interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelAdmin", reflect.TypeOf((*MockSuperChannelCreatorIClient)(nil).SetChannelAdmin), ctx, uid, channelId, role)
}
