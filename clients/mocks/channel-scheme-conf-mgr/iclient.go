// Code generated by MockGen. DO NOT EDIT.
// Source: /mnt/e/work_go/quicksilver/clients/channel-scheme-conf-mgr/iclient.go

// Package channel_scheme_conf_mgr is a generated GoMock package.
package channel_scheme_conf_mgr

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channel_scheme_conf_mgr "golang.52tt.com/protocol/services/channel-scheme-conf-mgr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// CreateChannelScheme mocks base method.
func (m *MockIClient) CreateChannelScheme(ctx context.Context, req *channel_scheme_conf_mgr.CreateChannelSchemeReq) (*channel_scheme_conf_mgr.CreateChannelSchemeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChannelScheme", ctx, req)
	ret0, _ := ret[0].(*channel_scheme_conf_mgr.CreateChannelSchemeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CreateChannelScheme indicates an expected call of CreateChannelScheme.
func (mr *MockIClientMockRecorder) CreateChannelScheme(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChannelScheme", reflect.TypeOf((*MockIClient)(nil).CreateChannelScheme), ctx, req)
}

// DeleteChannelScheme mocks base method.
func (m *MockIClient) DeleteChannelScheme(ctx context.Context, req *channel_scheme_conf_mgr.DeleteChannelSchemeReq) (*channel_scheme_conf_mgr.DeleteChannelSchemeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteChannelScheme", ctx, req)
	ret0, _ := ret[0].(*channel_scheme_conf_mgr.DeleteChannelSchemeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DeleteChannelScheme indicates an expected call of DeleteChannelScheme.
func (mr *MockIClientMockRecorder) DeleteChannelScheme(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteChannelScheme", reflect.TypeOf((*MockIClient)(nil).DeleteChannelScheme), ctx, req)
}

// GetAllChannelSchemeBaseConfCache mocks base method.
func (m *MockIClient) GetAllChannelSchemeBaseConfCache(ctx context.Context) ([]*channel_scheme_conf_mgr.ChannelSchemeBaseConf, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllChannelSchemeBaseConfCache", ctx)
	ret0, _ := ret[0].([]*channel_scheme_conf_mgr.ChannelSchemeBaseConf)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllChannelSchemeBaseConfCache indicates an expected call of GetAllChannelSchemeBaseConfCache.
func (mr *MockIClientMockRecorder) GetAllChannelSchemeBaseConfCache(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllChannelSchemeBaseConfCache", reflect.TypeOf((*MockIClient)(nil).GetAllChannelSchemeBaseConfCache), ctx)
}

// GetAllChannelSchemeConfCache mocks base method.
func (m *MockIClient) GetAllChannelSchemeConfCache(ctx context.Context) ([]*channel_scheme_conf_mgr.ChannelSchemeConf, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllChannelSchemeConfCache", ctx)
	ret0, _ := ret[0].([]*channel_scheme_conf_mgr.ChannelSchemeConf)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllChannelSchemeConfCache indicates an expected call of GetAllChannelSchemeConfCache.
func (mr *MockIClientMockRecorder) GetAllChannelSchemeConfCache(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllChannelSchemeConfCache", reflect.TypeOf((*MockIClient)(nil).GetAllChannelSchemeConfCache), ctx)
}

// GetAllChannelSchemeExtraConfCacheV2 mocks base method.
func (m *MockIClient) GetAllChannelSchemeExtraConfCacheV2(ctx context.Context) ([]*channel_scheme_conf_mgr.ChannelSchemeExtraConfV2, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllChannelSchemeExtraConfCacheV2", ctx)
	ret0, _ := ret[0].([]*channel_scheme_conf_mgr.ChannelSchemeExtraConfV2)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllChannelSchemeExtraConfCacheV2 indicates an expected call of GetAllChannelSchemeExtraConfCacheV2.
func (mr *MockIClientMockRecorder) GetAllChannelSchemeExtraConfCacheV2(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllChannelSchemeExtraConfCacheV2", reflect.TypeOf((*MockIClient)(nil).GetAllChannelSchemeExtraConfCacheV2), ctx)
}

// GetAllSpecSchemeDetailTypeConfCache mocks base method.
func (m *MockIClient) GetAllSpecSchemeDetailTypeConfCache(ctx context.Context) ([]*channel_scheme_conf_mgr.SpeChannelSchemeTypeConf, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllSpecSchemeDetailTypeConfCache", ctx)
	ret0, _ := ret[0].([]*channel_scheme_conf_mgr.SpeChannelSchemeTypeConf)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllSpecSchemeDetailTypeConfCache indicates an expected call of GetAllSpecSchemeDetailTypeConfCache.
func (mr *MockIClientMockRecorder) GetAllSpecSchemeDetailTypeConfCache(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllSpecSchemeDetailTypeConfCache", reflect.TypeOf((*MockIClient)(nil).GetAllSpecSchemeDetailTypeConfCache), ctx)
}

// GetChannelSchemeBaseConfList mocks base method.
func (m *MockIClient) GetChannelSchemeBaseConfList(ctx context.Context, req *channel_scheme_conf_mgr.GetChannelSchemeBaseConfListReq) (*channel_scheme_conf_mgr.GetChannelSchemeBaseConfListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelSchemeBaseConfList", ctx, req)
	ret0, _ := ret[0].(*channel_scheme_conf_mgr.GetChannelSchemeBaseConfListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelSchemeBaseConfList indicates an expected call of GetChannelSchemeBaseConfList.
func (mr *MockIClientMockRecorder) GetChannelSchemeBaseConfList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelSchemeBaseConfList", reflect.TypeOf((*MockIClient)(nil).GetChannelSchemeBaseConfList), ctx, req)
}

// GetChannelSchemeConfCache mocks base method.
func (m *MockIClient) GetChannelSchemeConfCache(ctx context.Context, schemeId, schemeDetailType uint32) (*channel_scheme_conf_mgr.GetChannelSchemeConfCacheResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelSchemeConfCache", ctx, schemeId, schemeDetailType)
	ret0, _ := ret[0].(*channel_scheme_conf_mgr.GetChannelSchemeConfCacheResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelSchemeConfCache indicates an expected call of GetChannelSchemeConfCache.
func (mr *MockIClientMockRecorder) GetChannelSchemeConfCache(ctx, schemeId, schemeDetailType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelSchemeConfCache", reflect.TypeOf((*MockIClient)(nil).GetChannelSchemeConfCache), ctx, schemeId, schemeDetailType)
}

// ModifyChannelScheme mocks base method.
func (m *MockIClient) ModifyChannelScheme(ctx context.Context, req *channel_scheme_conf_mgr.ModifyChannelSchemeReq) (*channel_scheme_conf_mgr.ModifyChannelSchemeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyChannelScheme", ctx, req)
	ret0, _ := ret[0].(*channel_scheme_conf_mgr.ModifyChannelSchemeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ModifyChannelScheme indicates an expected call of ModifyChannelScheme.
func (mr *MockIClientMockRecorder) ModifyChannelScheme(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyChannelScheme", reflect.TypeOf((*MockIClient)(nil).ModifyChannelScheme), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
