// Code generated by MockGen. DO NOT EDIT.
// Source: iclient.go

// Package mock_muse_social_community_achieve is a generated GoMock package.
package muse_social_community_achieve

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	muse_social_community_achieve "golang.52tt.com/protocol/services/muse-social-community-achieve"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CheckIn mocks base method.
func (m *MockIClient) CheckIn(ctx context.Context, socialCommunityId string) (*muse_social_community_achieve.CheckInResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIn", ctx, socialCommunityId)
	ret0, _ := ret[0].(*muse_social_community_achieve.CheckInResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckIn indicates an expected call of CheckIn.
func (mr *MockIClientMockRecorder) CheckIn(ctx, socialCommunityId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIn", reflect.TypeOf((*MockIClient)(nil).CheckIn), ctx, socialCommunityId)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetCheckInSimple mocks base method.
func (m *MockIClient) GetCheckInSimple(ctx context.Context, socialCommunityId string) (*muse_social_community_achieve.GetCheckInSimpleResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCheckInSimple", ctx, socialCommunityId)
	ret0, _ := ret[0].(*muse_social_community_achieve.GetCheckInSimpleResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCheckInSimple indicates an expected call of GetCheckInSimple.
func (mr *MockIClientMockRecorder) GetCheckInSimple(ctx, socialCommunityId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCheckInSimple", reflect.TypeOf((*MockIClient)(nil).GetCheckInSimple), ctx, socialCommunityId)
}

// GetLevelDetail mocks base method.
func (m *MockIClient) GetLevelDetail(ctx context.Context, req *muse_social_community_achieve.GetLevelDetailRequest) (*muse_social_community_achieve.GetLevelDetailResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelDetail", ctx, req)
	ret0, _ := ret[0].(*muse_social_community_achieve.GetLevelDetailResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLevelDetail indicates an expected call of GetLevelDetail.
func (mr *MockIClientMockRecorder) GetLevelDetail(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelDetail", reflect.TypeOf((*MockIClient)(nil).GetLevelDetail), ctx, req)
}

// GetSocialCommunityUpdateLevelTip mocks base method.
func (m *MockIClient) GetSocialCommunityUpdateLevelTip(ctx context.Context, req *muse_social_community_achieve.GetSocialCommunityUpdateLevelTipRequest) (*muse_social_community_achieve.GetSocialCommunityUpdateLevelTipResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocialCommunityUpdateLevelTip", ctx, req)
	ret0, _ := ret[0].(*muse_social_community_achieve.GetSocialCommunityUpdateLevelTipResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSocialCommunityUpdateLevelTip indicates an expected call of GetSocialCommunityUpdateLevelTip.
func (mr *MockIClientMockRecorder) GetSocialCommunityUpdateLevelTip(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocialCommunityUpdateLevelTip", reflect.TypeOf((*MockIClient)(nil).GetSocialCommunityUpdateLevelTip), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
