// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/unclaimed/iclient.go

// Package unclaimed is a generated GoMock package.
package unclaimed

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	unclaimed "golang.52tt.com/protocol/services/unclaimed"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetExpandEntranceList mocks base method.
func (m *MockIClient) GetExpandEntranceList(ctx context.Context, in *unclaimed.GetExpandEntranceListReq) (*unclaimed.GetExpandEntranceListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExpandEntranceList", ctx, in)
	ret0, _ := ret[0].(*unclaimed.GetExpandEntranceListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetExpandEntranceList indicates an expected call of GetExpandEntranceList.
func (mr *MockIClientMockRecorder) GetExpandEntranceList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExpandEntranceList", reflect.TypeOf((*MockIClient)(nil).GetExpandEntranceList), ctx, in)
}

// PushRedPoint mocks base method.
func (m *MockIClient) PushRedPoint(ctx context.Context, in *unclaimed.PushRedPointReq) (*unclaimed.PushRedPointResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushRedPoint", ctx, in)
	ret0, _ := ret[0].(*unclaimed.PushRedPointResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// PushRedPoint indicates an expected call of PushRedPoint.
func (mr *MockIClientMockRecorder) PushRedPoint(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushRedPoint", reflect.TypeOf((*MockIClient)(nil).PushRedPoint), ctx, in)
}

// PwnkKickAllChannelMember mocks base method.
func (m *MockIClient) PwnkKickAllChannelMember(ctx context.Context, in *unclaimed.PwnkKickAllChannelMemberReq) (*unclaimed.PwnkKickAllChannelMemberResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PwnkKickAllChannelMember", ctx, in)
	ret0, _ := ret[0].(*unclaimed.PwnkKickAllChannelMemberResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// PwnkKickAllChannelMember indicates an expected call of PwnkKickAllChannelMember.
func (mr *MockIClientMockRecorder) PwnkKickAllChannelMember(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PwnkKickAllChannelMember", reflect.TypeOf((*MockIClient)(nil).PwnkKickAllChannelMember), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
