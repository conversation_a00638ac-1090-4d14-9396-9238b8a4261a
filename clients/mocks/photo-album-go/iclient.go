// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/photo-album-go/iclient.go

// Package photoalbumgo is a generated GoMock package.
package photoalbumgo

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	photoalbumgo "golang.52tt.com/protocol/services/photoalbumgo"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DeletePhotoAlbum mocks base method.
func (m *MockIClient) DeletePhotoAlbum(ctx context.Context, uids ...uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range uids {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeletePhotoAlbum", varargs...)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// DeletePhotoAlbum indicates an expected call of DeletePhotoAlbum.
func (mr *MockIClientMockRecorder) DeletePhotoAlbum(ctx interface{}, uids ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, uids...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePhotoAlbum", reflect.TypeOf((*MockIClient)(nil).DeletePhotoAlbum), varargs...)
}

// GetPhotoAlbum mocks base method.
func (m *MockIClient) GetPhotoAlbum(ctx context.Context, uid uint32) ([]string, []string, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPhotoAlbum", ctx, uid)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].([]string)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// GetPhotoAlbum indicates an expected call of GetPhotoAlbum.
func (mr *MockIClientMockRecorder) GetPhotoAlbum(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPhotoAlbum", reflect.TypeOf((*MockIClient)(nil).GetPhotoAlbum), ctx, uid)
}

// MoveOldPhotoAlbum mocks base method.
func (m *MockIClient) MoveOldPhotoAlbum(ctx context.Context, uid uint32) (*photoalbumgo.MoveOldPhotoAlbumResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MoveOldPhotoAlbum", ctx, uid)
	ret0, _ := ret[0].(*photoalbumgo.MoveOldPhotoAlbumResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// MoveOldPhotoAlbum indicates an expected call of MoveOldPhotoAlbum.
func (mr *MockIClientMockRecorder) MoveOldPhotoAlbum(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MoveOldPhotoAlbum", reflect.TypeOf((*MockIClient)(nil).MoveOldPhotoAlbum), ctx, uid)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdatePhotoAlbum mocks base method.
func (m *MockIClient) UpdatePhotoAlbum(ctx context.Context, uid uint32, imgKeys, newImgKeys, prevImgKeys, prevNewImgKeys string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePhotoAlbum", ctx, uid, imgKeys, newImgKeys, prevImgKeys, prevNewImgKeys)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// UpdatePhotoAlbum indicates an expected call of UpdatePhotoAlbum.
func (mr *MockIClientMockRecorder) UpdatePhotoAlbum(ctx, uid, imgKeys, newImgKeys, prevImgKeys, prevNewImgKeys interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePhotoAlbum", reflect.TypeOf((*MockIClient)(nil).UpdatePhotoAlbum), ctx, uid, imgKeys, newImgKeys, prevImgKeys, prevNewImgKeys)
}
