// Code generated by MockGen. DO NOT EDIT.
// Source: ../../sign-anchor-stats/iclient.go

// Package sign_anchor_stats is a generated GoMock package.
package sign_anchor_stats

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	sign_anchor_stats "golang.52tt.com/protocol/services/sign-anchor-stats"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddHallTaskConf mocks base method.
func (m *MockIClient) AddHallTaskConf(ctx context.Context, in *sign_anchor_stats.AddHallTaskConfReq) (*sign_anchor_stats.AddHallTaskConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddHallTaskConf", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.AddHallTaskConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddHallTaskConf indicates an expected call of AddHallTaskConf.
func (mr *MockIClientMockRecorder) AddHallTaskConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddHallTaskConf", reflect.TypeOf((*MockIClient)(nil).AddHallTaskConf), ctx, in)
}

// BatchGetBindGuildInfo mocks base method.
func (m *MockIClient) BatchGetBindGuildInfo(ctx context.Context, req *sign_anchor_stats.BatchGetBindGuildInfoReq) (*sign_anchor_stats.BatchGetBindGuildInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBindGuildInfo", ctx, req)
	ret0, _ := ret[0].(*sign_anchor_stats.BatchGetBindGuildInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetBindGuildInfo indicates an expected call of BatchGetBindGuildInfo.
func (mr *MockIClientMockRecorder) BatchGetBindGuildInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBindGuildInfo", reflect.TypeOf((*MockIClient)(nil).BatchGetBindGuildInfo), ctx, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CheckUserInteractEntry mocks base method.
func (m *MockIClient) CheckUserInteractEntry(ctx context.Context, in *sign_anchor_stats.CheckUserInteractEntryReq) (*sign_anchor_stats.CheckUserInteractEntryResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserInteractEntry", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.CheckUserInteractEntryResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckUserInteractEntry indicates an expected call of CheckUserInteractEntry.
func (mr *MockIClientMockRecorder) CheckUserInteractEntry(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserInteractEntry", reflect.TypeOf((*MockIClient)(nil).CheckUserInteractEntry), ctx, in)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DelHallTask mocks base method.
func (m *MockIClient) DelHallTask(ctx context.Context, guildId, uid, opUid uint32) (*sign_anchor_stats.DelHallTaskResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelHallTask", ctx, guildId, uid, opUid)
	ret0, _ := ret[0].(*sign_anchor_stats.DelHallTaskResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelHallTask indicates an expected call of DelHallTask.
func (mr *MockIClientMockRecorder) DelHallTask(ctx, guildId, uid, opUid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelHallTask", reflect.TypeOf((*MockIClient)(nil).DelHallTask), ctx, guildId, uid, opUid)
}

// DelHallTaskConf mocks base method.
func (m *MockIClient) DelHallTaskConf(ctx context.Context, in *sign_anchor_stats.DelHallTaskConfReq) (*sign_anchor_stats.DelHallTaskConfResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelHallTaskConf", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.DelHallTaskConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelHallTaskConf indicates an expected call of DelHallTaskConf.
func (mr *MockIClientMockRecorder) DelHallTaskConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelHallTaskConf", reflect.TypeOf((*MockIClient)(nil).DelHallTaskConf), ctx, in)
}

// DistributeHallTask mocks base method.
func (m *MockIClient) DistributeHallTask(ctx context.Context, in *sign_anchor_stats.DistributeHallTaskReq) (*sign_anchor_stats.DistributeHallTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DistributeHallTask", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.DistributeHallTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DistributeHallTask indicates an expected call of DistributeHallTask.
func (mr *MockIClientMockRecorder) DistributeHallTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DistributeHallTask", reflect.TypeOf((*MockIClient)(nil).DistributeHallTask), ctx, in)
}

// GetBindGuildInfo mocks base method.
func (m *MockIClient) GetBindGuildInfo(ctx context.Context, req *sign_anchor_stats.GetBindGuildInfoReq) (*sign_anchor_stats.GetBindGuildInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindGuildInfo", ctx, req)
	ret0, _ := ret[0].(*sign_anchor_stats.GetBindGuildInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetBindGuildInfo indicates an expected call of GetBindGuildInfo.
func (mr *MockIClientMockRecorder) GetBindGuildInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindGuildInfo", reflect.TypeOf((*MockIClient)(nil).GetBindGuildInfo), ctx, req)
}

// GetGuildHallTask mocks base method.
func (m *MockIClient) GetGuildHallTask(ctx context.Context, in *sign_anchor_stats.GetGuildHallTaskReq) (*sign_anchor_stats.GetGuildHallTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildHallTask", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetGuildHallTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildHallTask indicates an expected call of GetGuildHallTask.
func (mr *MockIClientMockRecorder) GetGuildHallTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildHallTask", reflect.TypeOf((*MockIClient)(nil).GetGuildHallTask), ctx, in)
}

// GetGuildHallTaskStats mocks base method.
func (m *MockIClient) GetGuildHallTaskStats(ctx context.Context, in *sign_anchor_stats.GetGuildHallTaskStatsReq) (*sign_anchor_stats.GetGuildHallTaskStatsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildHallTaskStats", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetGuildHallTaskStatsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildHallTaskStats indicates an expected call of GetGuildHallTaskStats.
func (mr *MockIClientMockRecorder) GetGuildHallTaskStats(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildHallTaskStats", reflect.TypeOf((*MockIClient)(nil).GetGuildHallTaskStats), ctx, in)
}

// GetGuildHallTaskStatsDetial mocks base method.
func (m *MockIClient) GetGuildHallTaskStatsDetial(ctx context.Context, in *sign_anchor_stats.GetGuildHallTaskStatsDetialReq) (*sign_anchor_stats.GetGuildHallTaskStatsDetialResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildHallTaskStatsDetial", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetGuildHallTaskStatsDetialResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildHallTaskStatsDetial indicates an expected call of GetGuildHallTaskStatsDetial.
func (mr *MockIClientMockRecorder) GetGuildHallTaskStatsDetial(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildHallTaskStatsDetial", reflect.TypeOf((*MockIClient)(nil).GetGuildHallTaskStatsDetial), ctx, in)
}

// GetGuildMonthlyStatsInfoList mocks base method.
func (m *MockIClient) GetGuildMonthlyStatsInfoList(ctx context.Context, in *sign_anchor_stats.GetGuildMonthlyStatsInfoListReq) (*sign_anchor_stats.GetGuildMonthlyStatsInfoListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildMonthlyStatsInfoList", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetGuildMonthlyStatsInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildMonthlyStatsInfoList indicates an expected call of GetGuildMonthlyStatsInfoList.
func (mr *MockIClientMockRecorder) GetGuildMonthlyStatsInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildMonthlyStatsInfoList", reflect.TypeOf((*MockIClient)(nil).GetGuildMonthlyStatsInfoList), ctx, in)
}

// GetGuildMultiPlayerHall mocks base method.
func (m *MockIClient) GetGuildMultiPlayerHall(ctx context.Context, guildId uint32) (*sign_anchor_stats.GetGuildMultiPlayerHallResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildMultiPlayerHall", ctx, guildId)
	ret0, _ := ret[0].(*sign_anchor_stats.GetGuildMultiPlayerHallResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildMultiPlayerHall indicates an expected call of GetGuildMultiPlayerHall.
func (mr *MockIClientMockRecorder) GetGuildMultiPlayerHall(ctx, guildId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildMultiPlayerHall", reflect.TypeOf((*MockIClient)(nil).GetGuildMultiPlayerHall), ctx, guildId)
}

// GetHallTask mocks base method.
func (m *MockIClient) GetHallTask(ctx context.Context, in *sign_anchor_stats.GetHallTaskReq) (*sign_anchor_stats.GetHallTaskResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHallTask", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetHallTaskResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetHallTask indicates an expected call of GetHallTask.
func (mr *MockIClientMockRecorder) GetHallTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTask", reflect.TypeOf((*MockIClient)(nil).GetHallTask), ctx, in)
}

// GetHallTaskCacheInfo mocks base method.
func (m *MockIClient) GetHallTaskCacheInfo(ctx context.Context, uid, cid uint32) (*sign_anchor_stats.GetHallTaskResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHallTaskCacheInfo", ctx, uid, cid)
	ret0, _ := ret[0].(*sign_anchor_stats.GetHallTaskResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetHallTaskCacheInfo indicates an expected call of GetHallTaskCacheInfo.
func (mr *MockIClientMockRecorder) GetHallTaskCacheInfo(ctx, uid, cid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskCacheInfo", reflect.TypeOf((*MockIClient)(nil).GetHallTaskCacheInfo), ctx, uid, cid)
}

// GetHallTaskConfById mocks base method.
func (m *MockIClient) GetHallTaskConfById(ctx context.Context, req *sign_anchor_stats.GetHallTaskConfByIdReq) (*sign_anchor_stats.GetHallTaskConfByIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHallTaskConfById", ctx, req)
	ret0, _ := ret[0].(*sign_anchor_stats.GetHallTaskConfByIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetHallTaskConfById indicates an expected call of GetHallTaskConfById.
func (mr *MockIClientMockRecorder) GetHallTaskConfById(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskConfById", reflect.TypeOf((*MockIClient)(nil).GetHallTaskConfById), ctx, req)
}

// GetHallTaskConfList mocks base method.
func (m *MockIClient) GetHallTaskConfList(ctx context.Context, in *sign_anchor_stats.GetHallTaskConfListReq) (*sign_anchor_stats.GetHallTaskConfListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHallTaskConfList", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetHallTaskConfListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHallTaskConfList indicates an expected call of GetHallTaskConfList.
func (mr *MockIClientMockRecorder) GetHallTaskConfList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskConfList", reflect.TypeOf((*MockIClient)(nil).GetHallTaskConfList), ctx, in)
}

// GetHallTaskDistributeHistory mocks base method.
func (m *MockIClient) GetHallTaskDistributeHistory(ctx context.Context, uid uint32) (*sign_anchor_stats.GetHallTaskDistributeHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHallTaskDistributeHistory", ctx, uid)
	ret0, _ := ret[0].(*sign_anchor_stats.GetHallTaskDistributeHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHallTaskDistributeHistory indicates an expected call of GetHallTaskDistributeHistory.
func (mr *MockIClientMockRecorder) GetHallTaskDistributeHistory(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskDistributeHistory", reflect.TypeOf((*MockIClient)(nil).GetHallTaskDistributeHistory), ctx, uid)
}

// GetHallTaskHistory mocks base method.
func (m *MockIClient) GetHallTaskHistory(ctx context.Context, in *sign_anchor_stats.GetHallTaskHistoryReq) (*sign_anchor_stats.GetHallTaskHistoryResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHallTaskHistory", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetHallTaskHistoryResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetHallTaskHistory indicates an expected call of GetHallTaskHistory.
func (mr *MockIClientMockRecorder) GetHallTaskHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskHistory", reflect.TypeOf((*MockIClient)(nil).GetHallTaskHistory), ctx, in)
}

// GetMultiAnchorChannelStat mocks base method.
func (m *MockIClient) GetMultiAnchorChannelStat(ctx context.Context, req *sign_anchor_stats.GetMultiAnchorChannelStatReq) (*sign_anchor_stats.GetMultiAnchorChannelStatResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiAnchorChannelStat", ctx, req)
	ret0, _ := ret[0].(*sign_anchor_stats.GetMultiAnchorChannelStatResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMultiAnchorChannelStat indicates an expected call of GetMultiAnchorChannelStat.
func (mr *MockIClientMockRecorder) GetMultiAnchorChannelStat(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiAnchorChannelStat", reflect.TypeOf((*MockIClient)(nil).GetMultiAnchorChannelStat), ctx, req)
}

// GetMultiAnchorDailyStatsList mocks base method.
func (m *MockIClient) GetMultiAnchorDailyStatsList(ctx context.Context, in *sign_anchor_stats.GetMultiAnchorDailyStatsListReq) (*sign_anchor_stats.GetMultiAnchorDailyStatsListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiAnchorDailyStatsList", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetMultiAnchorDailyStatsListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiAnchorDailyStatsList indicates an expected call of GetMultiAnchorDailyStatsList.
func (mr *MockIClientMockRecorder) GetMultiAnchorDailyStatsList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiAnchorDailyStatsList", reflect.TypeOf((*MockIClient)(nil).GetMultiAnchorDailyStatsList), ctx, in)
}

// GetMultiAnchorDailyStatsListByGuildId mocks base method.
func (m *MockIClient) GetMultiAnchorDailyStatsListByGuildId(ctx context.Context, in *sign_anchor_stats.GetMultiAnchorDailyStatsListByGuildIdReq) (*sign_anchor_stats.GetMultiAnchorDailyStatsListByGuildIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiAnchorDailyStatsListByGuildId", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetMultiAnchorDailyStatsListByGuildIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiAnchorDailyStatsListByGuildId indicates an expected call of GetMultiAnchorDailyStatsListByGuildId.
func (mr *MockIClientMockRecorder) GetMultiAnchorDailyStatsListByGuildId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiAnchorDailyStatsListByGuildId", reflect.TypeOf((*MockIClient)(nil).GetMultiAnchorDailyStatsListByGuildId), ctx, in)
}

// GetMultiAnchorMonthStat mocks base method.
func (m *MockIClient) GetMultiAnchorMonthStat(ctx context.Context, req *sign_anchor_stats.GetMultiAnchorMonthStatReq) (*sign_anchor_stats.GetMultiAnchorMonthStatResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiAnchorMonthStat", ctx, req)
	ret0, _ := ret[0].(*sign_anchor_stats.GetMultiAnchorMonthStatResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMultiAnchorMonthStat indicates an expected call of GetMultiAnchorMonthStat.
func (mr *MockIClientMockRecorder) GetMultiAnchorMonthStat(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiAnchorMonthStat", reflect.TypeOf((*MockIClient)(nil).GetMultiAnchorMonthStat), ctx, req)
}

// GetMultiPlayerBaseInfo mocks base method.
func (m *MockIClient) GetMultiPlayerBaseInfo(ctx context.Context, in *sign_anchor_stats.GetMultiPlayerBaseInfoReq) (*sign_anchor_stats.GetMultiPlayerBaseInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiPlayerBaseInfo", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetMultiPlayerBaseInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMultiPlayerBaseInfo indicates an expected call of GetMultiPlayerBaseInfo.
func (mr *MockIClientMockRecorder) GetMultiPlayerBaseInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerBaseInfo", reflect.TypeOf((*MockIClient)(nil).GetMultiPlayerBaseInfo), ctx, in)
}

// GetMultiPlayerHomepage mocks base method.
func (m *MockIClient) GetMultiPlayerHomepage(ctx context.Context, in *sign_anchor_stats.GetMultiPlayerHomepageReq) (*sign_anchor_stats.GetMultiPlayerHomepageResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiPlayerHomepage", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetMultiPlayerHomepageResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMultiPlayerHomepage indicates an expected call of GetMultiPlayerHomepage.
func (mr *MockIClientMockRecorder) GetMultiPlayerHomepage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerHomepage", reflect.TypeOf((*MockIClient)(nil).GetMultiPlayerHomepage), ctx, in)
}

// GetMultiPlayerMonthCommunityInfo mocks base method.
func (m *MockIClient) GetMultiPlayerMonthCommunityInfo(ctx context.Context, in *sign_anchor_stats.GetMultiPlayerMonthCommunityInfoReq) (*sign_anchor_stats.GetMultiPlayerMonthCommunityInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiPlayerMonthCommunityInfo", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetMultiPlayerMonthCommunityInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMultiPlayerMonthCommunityInfo indicates an expected call of GetMultiPlayerMonthCommunityInfo.
func (mr *MockIClientMockRecorder) GetMultiPlayerMonthCommunityInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerMonthCommunityInfo", reflect.TypeOf((*MockIClient)(nil).GetMultiPlayerMonthCommunityInfo), ctx, in)
}

// GetBindGuildInfoList mocks base method.
func (m *MockIClient) GetBindGuildInfoList(ctx context.Context, in *sign_anchor_stats.GetBindGuildInfoListReq) (*sign_anchor_stats.GetBindGuildInfoListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindGuildInfoList", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetBindGuildInfoListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetBindGuildInfoList indicates an expected call of GetMultiPlayerMonthCommunityInfo.
func (mr *MockIClientMockRecorder) GetBindGuildInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindGuildInfoList", reflect.TypeOf((*MockIClient)(nil).GetBindGuildInfoList), ctx, in)
}

// GetMultiPlayerMonthConsumeTop10 mocks base method.
func (m *MockIClient) GetMultiPlayerMonthConsumeTop10(ctx context.Context, in *sign_anchor_stats.GetMultiPlayerMonthConsumeTop10Req) (*sign_anchor_stats.GetMultiPlayerMonthConsumeTop10Resp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiPlayerMonthConsumeTop10", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetMultiPlayerMonthConsumeTop10Resp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMultiPlayerMonthConsumeTop10 indicates an expected call of GetMultiPlayerMonthConsumeTop10.
func (mr *MockIClientMockRecorder) GetMultiPlayerMonthConsumeTop10(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerMonthConsumeTop10", reflect.TypeOf((*MockIClient)(nil).GetMultiPlayerMonthConsumeTop10), ctx, in)
}

// GetMultiThisMonthChannelStat mocks base method.
func (m *MockIClient) GetMultiThisMonthChannelStat(ctx context.Context, req *sign_anchor_stats.GetMultiThisMonthChannelStatReq) (*sign_anchor_stats.GetMultiThisMonthChannelStatResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiThisMonthChannelStat", ctx, req)
	ret0, _ := ret[0].(*sign_anchor_stats.GetMultiThisMonthChannelStatResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMultiThisMonthChannelStat indicates an expected call of GetMultiThisMonthChannelStat.
func (mr *MockIClientMockRecorder) GetMultiThisMonthChannelStat(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiThisMonthChannelStat", reflect.TypeOf((*MockIClient)(nil).GetMultiThisMonthChannelStat), ctx, req)
}

// GetPgcDailyInfoList mocks base method.
func (m *MockIClient) GetPgcDailyInfoList(ctx context.Context, in *sign_anchor_stats.GetPgcDailyInfoListReq) (*sign_anchor_stats.GetPgcDailyInfoListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPgcDailyInfoList", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetPgcDailyInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPgcDailyInfoList indicates an expected call of GetPgcDailyInfoList.
func (mr *MockIClientMockRecorder) GetPgcDailyInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPgcDailyInfoList", reflect.TypeOf((*MockIClient)(nil).GetPgcDailyInfoList), ctx, in)
}

// GetPgcMonthlyInfoList mocks base method.
func (m *MockIClient) GetPgcMonthlyInfoList(ctx context.Context, in *sign_anchor_stats.GetPgcMonthlyInfoListReq) (*sign_anchor_stats.GetPgcMonthlyInfoListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPgcMonthlyInfoList", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetPgcMonthlyInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPgcMonthlyInfoList indicates an expected call of GetPgcMonthlyInfoList.
func (mr *MockIClientMockRecorder) GetPgcMonthlyInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPgcMonthlyInfoList", reflect.TypeOf((*MockIClient)(nil).GetPgcMonthlyInfoList), ctx, in)
}

// GetUserInteractInfo mocks base method.
func (m *MockIClient) GetUserInteractInfo(ctx context.Context, in *sign_anchor_stats.GetUserInteractInfoReq) (*sign_anchor_stats.GetUserInteractInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInteractInfo", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetUserInteractInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserInteractInfo indicates an expected call of GetUserInteractInfo.
func (mr *MockIClientMockRecorder) GetUserInteractInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInteractInfo", reflect.TypeOf((*MockIClient)(nil).GetUserInteractInfo), ctx, in)
}

// GetUserInteractViewPer mocks base method.
func (m *MockIClient) GetUserInteractViewPer(ctx context.Context, in *sign_anchor_stats.GetUserInteractViewPerReq) (*sign_anchor_stats.GetUserInteractViewPerResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInteractViewPer", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.GetUserInteractViewPerResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserInteractViewPer indicates an expected call of GetUserInteractViewPer.
func (mr *MockIClientMockRecorder) GetUserInteractViewPer(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInteractViewPer", reflect.TypeOf((*MockIClient)(nil).GetUserInteractViewPer), ctx, in)
}

// GetUserTbeanConsume mocks base method.
func (m *MockIClient) GetUserTbeanConsume(ctx context.Context, uid, beginTs, endTs uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserTbeanConsume", ctx, uid, beginTs, endTs)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTbeanConsume indicates an expected call of GetUserTbeanConsume.
func (mr *MockIClientMockRecorder) GetUserTbeanConsume(ctx, uid, beginTs, endTs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTbeanConsume", reflect.TypeOf((*MockIClient)(nil).GetUserTbeanConsume), ctx, uid, beginTs, endTs)
}

// GetValidHoldDayUid mocks base method.
func (m *MockIClient) GetValidHoldDayUid(ctx context.Context, req *sign_anchor_stats.GetValidHoldDayUidReq) (*sign_anchor_stats.GetValidHoldDayUidResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetValidHoldDayUid", ctx, req)
	ret0, _ := ret[0].(*sign_anchor_stats.GetValidHoldDayUidResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetValidHoldDayUid indicates an expected call of GetValidHoldDayUid.
func (mr *MockIClientMockRecorder) GetValidHoldDayUid(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetValidHoldDayUid", reflect.TypeOf((*MockIClient)(nil).GetValidHoldDayUid), ctx, req)
}

// ListMultiPlayerHall mocks base method.
func (m *MockIClient) ListMultiPlayerHall(ctx context.Context, req *sign_anchor_stats.ListMultiPlayerHallReq) (*sign_anchor_stats.ListMultiPlayerHallResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMultiPlayerHall", ctx, req)
	ret0, _ := ret[0].(*sign_anchor_stats.ListMultiPlayerHallResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListMultiPlayerHall indicates an expected call of ListMultiPlayerHall.
func (mr *MockIClientMockRecorder) ListMultiPlayerHall(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMultiPlayerHall", reflect.TypeOf((*MockIClient)(nil).ListMultiPlayerHall), ctx, req)
}

// SetUserInteractViewPer mocks base method.
func (m *MockIClient) SetUserInteractViewPer(ctx context.Context, in *sign_anchor_stats.SetUserInteractViewPerReq) (*sign_anchor_stats.SetUserInteractViewPerResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserInteractViewPer", ctx, in)
	ret0, _ := ret[0].(*sign_anchor_stats.SetUserInteractViewPerResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetUserInteractViewPer indicates an expected call of SetUserInteractViewPer.
func (mr *MockIClientMockRecorder) SetUserInteractViewPer(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserInteractViewPer", reflect.TypeOf((*MockIClient)(nil).SetUserInteractViewPer), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
