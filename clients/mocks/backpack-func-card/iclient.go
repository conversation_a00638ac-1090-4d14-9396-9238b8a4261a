// Code generated by MockGen. DO NOT EDIT.
// Source: F:\griffin\clients\backpack-func-card\iclient.go

// Package backpack_func_card is a generated GoMock package.
package backpack_func_card

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	backpack_func_card "golang.52tt.com/protocol/services/backpack-func-card"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddFuncCardCfg mocks base method.
func (m *MockIClient) AddFuncCardCfg(ctx context.Context, req *backpack_func_card.AddFuncCardCfgReq, opts ...grpc.CallOption) (*backpack_func_card.AddFuncCardCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddFuncCardCfg", varargs...)
	ret0, _ := ret[0].(*backpack_func_card.AddFuncCardCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddFuncCardCfg indicates an expected call of AddFuncCardCfg.
func (mr *MockIClientMockRecorder) AddFuncCardCfg(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFuncCardCfg", reflect.TypeOf((*MockIClient)(nil).AddFuncCardCfg), varargs...)
}

// BatchGetAccelerateCardUsage mocks base method.
func (m *MockIClient) BatchGetAccelerateCardUsage(ctx context.Context, uidList []uint32, opts ...grpc.CallOption) (*backpack_func_card.BatchGetAccelerateCardUsageResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, uidList}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetAccelerateCardUsage", varargs...)
	ret0, _ := ret[0].(*backpack_func_card.BatchGetAccelerateCardUsageResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetAccelerateCardUsage indicates an expected call of BatchGetAccelerateCardUsage.
func (mr *MockIClientMockRecorder) BatchGetAccelerateCardUsage(ctx, uidList interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, uidList}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAccelerateCardUsage", reflect.TypeOf((*MockIClient)(nil).BatchGetAccelerateCardUsage), varargs...)
}

// BatchGetUserFuncCardUse mocks base method.
func (m *MockIClient) BatchGetUserFuncCardUse(ctx context.Context, uidList []uint32) (map[uint32][]*backpack_func_card.FuncCardCfg, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserFuncCardUse", ctx, uidList)
	ret0, _ := ret[0].(map[uint32][]*backpack_func_card.FuncCardCfg)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserFuncCardUse indicates an expected call of BatchGetUserFuncCardUse.
func (mr *MockIClientMockRecorder) BatchGetUserFuncCardUse(ctx, uidList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserFuncCardUse", reflect.TypeOf((*MockIClient)(nil).BatchGetUserFuncCardUse), ctx, uidList)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DelFuncCardCfg mocks base method.
func (m *MockIClient) DelFuncCardCfg(ctx context.Context, cardId uint32, opts ...grpc.CallOption) (*backpack_func_card.DelFuncCardCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, cardId}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelFuncCardCfg", varargs...)
	ret0, _ := ret[0].(*backpack_func_card.DelFuncCardCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelFuncCardCfg indicates an expected call of DelFuncCardCfg.
func (mr *MockIClientMockRecorder) DelFuncCardCfg(ctx, cardId interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, cardId}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelFuncCardCfg", reflect.TypeOf((*MockIClient)(nil).DelFuncCardCfg), varargs...)
}

// GetAccelerateCardUsage mocks base method.
func (m *MockIClient) GetAccelerateCardUsage(ctx context.Context, uid uint32, opts ...grpc.CallOption) (*backpack_func_card.GetAccelerateCardUsageResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, uid}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccelerateCardUsage", varargs...)
	ret0, _ := ret[0].(*backpack_func_card.GetAccelerateCardUsageResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAccelerateCardUsage indicates an expected call of GetAccelerateCardUsage.
func (mr *MockIClientMockRecorder) GetAccelerateCardUsage(ctx, uid interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, uid}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccelerateCardUsage", reflect.TypeOf((*MockIClient)(nil).GetAccelerateCardUsage), varargs...)
}

// GetFuncCardCfg mocks base method.
func (m *MockIClient) GetFuncCardCfg(ctx context.Context, cardIds []uint32, opts ...grpc.CallOption) (*backpack_func_card.GetFuncCardCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, cardIds}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFuncCardCfg", varargs...)
	ret0, _ := ret[0].(*backpack_func_card.GetFuncCardCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFuncCardCfg indicates an expected call of GetFuncCardCfg.
func (mr *MockIClientMockRecorder) GetFuncCardCfg(ctx, cardIds interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, cardIds}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFuncCardCfg", reflect.TypeOf((*MockIClient)(nil).GetFuncCardCfg), varargs...)
}

// GetFuncCardCfgWithOriginDesc mocks base method.
func (m *MockIClient) GetFuncCardCfgWithOriginDesc(ctx context.Context, cardIds []uint32, opts ...grpc.CallOption) (*backpack_func_card.GetFuncCardCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, cardIds}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFuncCardCfgWithOriginDesc", varargs...)
	ret0, _ := ret[0].(*backpack_func_card.GetFuncCardCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFuncCardCfgWithOriginDesc indicates an expected call of GetFuncCardCfgWithOriginDesc.
func (mr *MockIClientMockRecorder) GetFuncCardCfgWithOriginDesc(ctx, cardIds interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, cardIds}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFuncCardCfgWithOriginDesc", reflect.TypeOf((*MockIClient)(nil).GetFuncCardCfgWithOriginDesc), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UseFuncCard mocks base method.
func (m *MockIClient) UseFuncCard(ctx context.Context, req *backpack_func_card.UseFuncCardReq, opts ...grpc.CallOption) (*backpack_func_card.UseFuncCardResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UseFuncCard", varargs...)
	ret0, _ := ret[0].(*backpack_func_card.UseFuncCardResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UseFuncCard indicates an expected call of UseFuncCard.
func (mr *MockIClientMockRecorder) UseFuncCard(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UseFuncCard", reflect.TypeOf((*MockIClient)(nil).UseFuncCard), varargs...)
}
