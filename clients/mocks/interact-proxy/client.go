// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/interact-proxy (interfaces: InteractProxyClient)

// Package interact_proxy is a generated GoMock package.
package interact_proxy

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	interact_proxy "golang.52tt.com/protocol/services/interact-proxy"
	grpc "google.golang.org/grpc"
)

// MockInteractProxyClient is a mock of InteractProxyClient interface.
type MockInteractProxyClient struct {
	ctrl     *gomock.Controller
	recorder *MockInteractProxyClientMockRecorder
}

// MockInteractProxyClientMockRecorder is the mock recorder for MockInteractProxyClient.
type MockInteractProxyClientMockRecorder struct {
	mock *MockInteractProxyClient
}

// NewMockInteractProxyClient creates a new mock instance.
func NewMockInteractProxyClient(ctrl *gomock.Controller) *MockInteractProxyClient {
	mock := &MockInteractProxyClient{ctrl: ctrl}
	mock.recorder = &MockInteractProxyClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInteractProxyClient) EXPECT() *MockInteractProxyClientMockRecorder {
	return m.recorder
}

// GetCommonInvitationResult mocks base method.
func (m *MockInteractProxyClient) GetCommonInvitationResult(arg0 context.Context, arg1 *interact_proxy.GetCommonInvitationResultReq, arg2 ...grpc.CallOption) (*interact_proxy.GetCommonInvitationResultResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCommonInvitationResult", varargs...)
	ret0, _ := ret[0].(*interact_proxy.GetCommonInvitationResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommonInvitationResult indicates an expected call of GetCommonInvitationResult.
func (mr *MockInteractProxyClientMockRecorder) GetCommonInvitationResult(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommonInvitationResult", reflect.TypeOf((*MockInteractProxyClient)(nil).GetCommonInvitationResult), varargs...)
}

// ReportCommonInvitationResult mocks base method.
func (m *MockInteractProxyClient) ReportCommonInvitationResult(arg0 context.Context, arg1 *interact_proxy.ReportCommonInvitationResultReq, arg2 ...grpc.CallOption) (*interact_proxy.ReportCommonInvitationResultResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReportCommonInvitationResult", varargs...)
	ret0, _ := ret[0].(*interact_proxy.ReportCommonInvitationResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportCommonInvitationResult indicates an expected call of ReportCommonInvitationResult.
func (mr *MockInteractProxyClientMockRecorder) ReportCommonInvitationResult(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportCommonInvitationResult", reflect.TypeOf((*MockInteractProxyClient)(nil).ReportCommonInvitationResult), varargs...)
}

// SendCommonInvitation mocks base method.
func (m *MockInteractProxyClient) SendCommonInvitation(arg0 context.Context, arg1 *interact_proxy.SendCommonInvitationReq, arg2 ...grpc.CallOption) (*interact_proxy.SendCommonInvitationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendCommonInvitation", varargs...)
	ret0, _ := ret[0].(*interact_proxy.SendCommonInvitationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendCommonInvitation indicates an expected call of SendCommonInvitation.
func (mr *MockInteractProxyClientMockRecorder) SendCommonInvitation(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCommonInvitation", reflect.TypeOf((*MockInteractProxyClient)(nil).SendCommonInvitation), varargs...)
}
