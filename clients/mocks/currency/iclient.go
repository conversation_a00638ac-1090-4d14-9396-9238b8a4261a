// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/currency/iclient.go

// Package currency is a generated GoMock package.
package currency

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	Currency "golang.52tt.com/protocol/services/currencysvr"
	context "golang.org/x/net/context"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddUserCurrency mocks base method.
func (m *MockIClient) AddUserCurrency(ctx context.Context, uid uint32, add int32, orderID, desc string, reason uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserCurrency", ctx, uid, add, orderID, desc, reason)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddUserCurrency indicates an expected call of AddUserCurrency.
func (mr *MockIClientMockRecorder) AddUserCurrency(ctx, uid, add, orderID, desc, reason interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserCurrency", reflect.TypeOf((*MockIClient)(nil).AddUserCurrency), ctx, uid, add, orderID, desc, reason)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetUserCurrency mocks base method.
func (m *MockIClient) GetUserCurrency(ctx context.Context, uid uint32) (int32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserCurrency", ctx, uid)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserCurrency indicates an expected call of GetUserCurrency.
func (mr *MockIClientMockRecorder) GetUserCurrency(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCurrency", reflect.TypeOf((*MockIClient)(nil).GetUserCurrency), ctx, uid)
}

// GetUserCurrencyLog mocks base method.
func (m *MockIClient) GetUserCurrencyLog(ctx context.Context, uid uint32, in *Currency.GetUserCurrencyLogReq) (*Currency.GetUserCurrencyLogResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserCurrencyLog", ctx, uid, in)
	ret0, _ := ret[0].(*Currency.GetUserCurrencyLogResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserCurrencyLog indicates an expected call of GetUserCurrencyLog.
func (mr *MockIClientMockRecorder) GetUserCurrencyLog(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCurrencyLog", reflect.TypeOf((*MockIClient)(nil).GetUserCurrencyLog), ctx, uid, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
