// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/user-visitor-record-logic/iclient.go

// Package user_visitor_record_logic is a generated GoMock package.
package user_visitor_record_logic

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	user_visitor_record "golang.52tt.com/protocol/app/user-visitor-record"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetAllTaskStatus mocks base method.
func (m *MockIClient) GetAllTaskStatus(ctx context.Context, in *user_visitor_record.GetAllTaskStatusReq) (*user_visitor_record.GetAllTaskStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTaskStatus", ctx, in)
	ret0, _ := ret[0].(*user_visitor_record.GetAllTaskStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllTaskStatus indicates an expected call of GetAllTaskStatus.
func (mr *MockIClientMockRecorder) GetAllTaskStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTaskStatus", reflect.TypeOf((*MockIClient)(nil).GetAllTaskStatus), ctx, in)
}

// GetShowUserBeVisitorRecordCount mocks base method.
func (m *MockIClient) GetShowUserBeVisitorRecordCount(ctx context.Context, in *user_visitor_record.GetShowUserBeVisitorRecordCountReq) (*user_visitor_record.GetShowUserBeVisitorRecordCountResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowUserBeVisitorRecordCount", ctx, in)
	ret0, _ := ret[0].(*user_visitor_record.GetShowUserBeVisitorRecordCountResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetShowUserBeVisitorRecordCount indicates an expected call of GetShowUserBeVisitorRecordCount.
func (mr *MockIClientMockRecorder) GetShowUserBeVisitorRecordCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowUserBeVisitorRecordCount", reflect.TypeOf((*MockIClient)(nil).GetShowUserBeVisitorRecordCount), ctx, in)
}

// GetUserBeVisitorRecordCount mocks base method.
func (m *MockIClient) GetUserBeVisitorRecordCount(ctx context.Context, in *user_visitor_record.GetUserBeVisitorRecordCountReq) (*user_visitor_record.GetUserBeVisitorRecordCountResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserBeVisitorRecordCount", ctx, in)
	ret0, _ := ret[0].(*user_visitor_record.GetUserBeVisitorRecordCountResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserBeVisitorRecordCount indicates an expected call of GetUserBeVisitorRecordCount.
func (mr *MockIClientMockRecorder) GetUserBeVisitorRecordCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBeVisitorRecordCount", reflect.TypeOf((*MockIClient)(nil).GetUserBeVisitorRecordCount), ctx, in)
}

// GetUserBeVisitorRecordList mocks base method.
func (m *MockIClient) GetUserBeVisitorRecordList(ctx context.Context, in *user_visitor_record.GetUserBeVisitorRecordListReq) (*user_visitor_record.GetUserBeVisitorRecordListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserBeVisitorRecordList", ctx, in)
	ret0, _ := ret[0].(*user_visitor_record.GetUserBeVisitorRecordListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserBeVisitorRecordList indicates an expected call of GetUserBeVisitorRecordList.
func (mr *MockIClientMockRecorder) GetUserBeVisitorRecordList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBeVisitorRecordList", reflect.TypeOf((*MockIClient)(nil).GetUserBeVisitorRecordList), ctx, in)
}

// GetUserVisitorRecordList mocks base method.
func (m *MockIClient) GetUserVisitorRecordList(ctx context.Context, in *user_visitor_record.GetUserVisitorRecordListReq) (*user_visitor_record.GetUserVisitorRecordListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserVisitorRecordList", ctx, in)
	ret0, _ := ret[0].(*user_visitor_record.GetUserVisitorRecordListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserVisitorRecordList indicates an expected call of GetUserVisitorRecordList.
func (mr *MockIClientMockRecorder) GetUserVisitorRecordList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserVisitorRecordList", reflect.TypeOf((*MockIClient)(nil).GetUserVisitorRecordList), ctx, in)
}

// ReportUserVisitorRecord mocks base method.
func (m *MockIClient) ReportUserVisitorRecord(ctx context.Context, in *user_visitor_record.ReportUserVisitorRecordReq) (*user_visitor_record.ReportUserVisitorRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportUserVisitorRecord", ctx, in)
	ret0, _ := ret[0].(*user_visitor_record.ReportUserVisitorRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ReportUserVisitorRecord indicates an expected call of ReportUserVisitorRecord.
func (mr *MockIClientMockRecorder) ReportUserVisitorRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportUserVisitorRecord", reflect.TypeOf((*MockIClient)(nil).ReportUserVisitorRecord), ctx, in)
}

// SetShowUserBeVisitorRecordCount mocks base method.
func (m *MockIClient) SetShowUserBeVisitorRecordCount(ctx context.Context, in *user_visitor_record.SetShowUserBeVisitorRecordCountReq) (*user_visitor_record.SetShowUserBeVisitorRecordCountResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetShowUserBeVisitorRecordCount", ctx, in)
	ret0, _ := ret[0].(*user_visitor_record.SetShowUserBeVisitorRecordCountResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetShowUserBeVisitorRecordCount indicates an expected call of SetShowUserBeVisitorRecordCount.
func (mr *MockIClientMockRecorder) SetShowUserBeVisitorRecordCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetShowUserBeVisitorRecordCount", reflect.TypeOf((*MockIClient)(nil).SetShowUserBeVisitorRecordCount), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
