// Code generated by MockGen. DO NOT EDIT.
// Source: E:\TT\gitmaven\quicksilver\clients\topic-channel\channel\iclient.go

// Package channel is a generated GoMock package.
package channel

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channel "golang.52tt.com/protocol/services/topic_channel/channel"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddChannel mocks base method.
func (m *MockIClient) AddChannel(ctx context.Context, in *channel.AddChannelReq) (*channel.AddChannelResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChannel", ctx, in)
	ret0, _ := ret[0].(*channel.AddChannelResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddChannel indicates an expected call of AddChannel.
func (mr *MockIClientMockRecorder) AddChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannel", reflect.TypeOf((*MockIClient)(nil).AddChannel), ctx, in)
}

// AddTemporaryChannel mocks base method.
func (m *MockIClient) AddTemporaryChannel(ctx context.Context, in *channel.AddTemporaryChannelReq) (*channel.AddTemporaryChannelResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTemporaryChannel", ctx, in)
	ret0, _ := ret[0].(*channel.AddTemporaryChannelResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddTemporaryChannel indicates an expected call of AddTemporaryChannel.
func (mr *MockIClientMockRecorder) AddTemporaryChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTemporaryChannel", reflect.TypeOf((*MockIClient)(nil).AddTemporaryChannel), ctx, in)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DisappearChannel mocks base method.
func (m *MockIClient) DisappearChannel(ctx context.Context, in *channel.DisappearChannelReq) (*channel.DisappearChannelResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisappearChannel", ctx, in)
	ret0, _ := ret[0].(*channel.DisappearChannelResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DisappearChannel indicates an expected call of DisappearChannel.
func (mr *MockIClientMockRecorder) DisappearChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisappearChannel", reflect.TypeOf((*MockIClient)(nil).DisappearChannel), ctx, in)
}

// DismissChannel mocks base method.
func (m *MockIClient) DismissChannel(ctx context.Context, in *channel.DismissChannelReq) (*channel.DismissChannelResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DismissChannel", ctx, in)
	ret0, _ := ret[0].(*channel.DismissChannelResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DismissChannel indicates an expected call of DismissChannel.
func (mr *MockIClientMockRecorder) DismissChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DismissChannel", reflect.TypeOf((*MockIClient)(nil).DismissChannel), ctx, in)
}

// DismissTab mocks base method.
func (m *MockIClient) DismissTab(ctx context.Context, in *channel.DismissTabReq) (*channel.DismissTabResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DismissTab", ctx, in)
	ret0, _ := ret[0].(*channel.DismissTabResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DismissTab indicates an expected call of DismissTab.
func (mr *MockIClientMockRecorder) DismissTab(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DismissTab", reflect.TypeOf((*MockIClient)(nil).DismissTab), ctx, in)
}

// FreezeChannel mocks base method.
func (m *MockIClient) FreezeChannel(ctx context.Context, in *channel.FreezeChannelReq) (*channel.FreezeChannelResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FreezeChannel", ctx, in)
	ret0, _ := ret[0].(*channel.FreezeChannelResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// FreezeChannel indicates an expected call of FreezeChannel.
func (mr *MockIClientMockRecorder) FreezeChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreezeChannel", reflect.TypeOf((*MockIClient)(nil).FreezeChannel), ctx, in)
}

// GetChannelByIds mocks base method.
func (m *MockIClient) GetChannelByIds(ctx context.Context, in *channel.GetChannelByIdsReq) (*channel.GetChannelByIdsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelByIds", ctx, in)
	ret0, _ := ret[0].(*channel.GetChannelByIdsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelByIds indicates an expected call of GetChannelByIds.
func (mr *MockIClientMockRecorder) GetChannelByIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelByIds", reflect.TypeOf((*MockIClient)(nil).GetChannelByIds), ctx, in)
}

// GetChannelFreezeInfo mocks base method.
func (m *MockIClient) GetChannelFreezeInfo(ctx context.Context, in *channel.GetChannelFreezeInfoReq) (*channel.GetChannelFreezeInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelFreezeInfo", ctx, in)
	ret0, _ := ret[0].(*channel.GetChannelFreezeInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelFreezeInfo indicates an expected call of GetChannelFreezeInfo.
func (mr *MockIClientMockRecorder) GetChannelFreezeInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelFreezeInfo", reflect.TypeOf((*MockIClient)(nil).GetChannelFreezeInfo), ctx, in)
}

// GetChannelPlayModel mocks base method.
func (m *MockIClient) GetChannelPlayModel(ctx context.Context, in *channel.GetChannelPlayModelReq) (*channel.GetChannelPlayModelResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelPlayModel", ctx, in)
	ret0, _ := ret[0].(*channel.GetChannelPlayModelResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelPlayModel indicates an expected call of GetChannelPlayModel.
func (mr *MockIClientMockRecorder) GetChannelPlayModel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelPlayModel", reflect.TypeOf((*MockIClient)(nil).GetChannelPlayModel), ctx, in)
}

// GetChannelRoomUserNumber mocks base method.
func (m *MockIClient) GetChannelRoomUserNumber(ctx context.Context, in *channel.GetChannelRoomUserNumberReq) (*channel.GetChannelRoomUserNumberResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelRoomUserNumber", ctx, in)
	ret0, _ := ret[0].(*channel.GetChannelRoomUserNumberResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelRoomUserNumber indicates an expected call of GetChannelRoomUserNumber.
func (mr *MockIClientMockRecorder) GetChannelRoomUserNumber(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelRoomUserNumber", reflect.TypeOf((*MockIClient)(nil).GetChannelRoomUserNumber), ctx, in)
}

// GetExtraHistory mocks base method.
func (m *MockIClient) GetExtraHistory(ctx context.Context, in *channel.GetExtraHistoryReq) (*channel.GetExtraHistoryResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExtraHistory", ctx, in)
	ret0, _ := ret[0].(*channel.GetExtraHistoryResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetExtraHistory indicates an expected call of GetExtraHistory.
func (mr *MockIClientMockRecorder) GetExtraHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtraHistory", reflect.TypeOf((*MockIClient)(nil).GetExtraHistory), ctx, in)
}

// GetLastEnterRoomTabIdByUid mocks base method.
func (m *MockIClient) GetLastEnterRoomTabIdByUid(ctx context.Context, in *channel.GetLastEnterRoomTabIdByUidReq) (*channel.GetLastEnterRoomTabIdByUidResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastEnterRoomTabIdByUid", ctx, in)
	ret0, _ := ret[0].(*channel.GetLastEnterRoomTabIdByUidResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLastEnterRoomTabIdByUid indicates an expected call of GetLastEnterRoomTabIdByUid.
func (mr *MockIClientMockRecorder) GetLastEnterRoomTabIdByUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastEnterRoomTabIdByUid", reflect.TypeOf((*MockIClient)(nil).GetLastEnterRoomTabIdByUid), ctx, in)
}

// GetLastEnterRoomTimeByUid mocks base method.
func (m *MockIClient) GetLastEnterRoomTimeByUid(ctx context.Context, in *channel.GetLastEnterRoomTimeByUidReq) (*channel.GetLastEnterRoomTimeByUidResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastEnterRoomTimeByUid", ctx, in)
	ret0, _ := ret[0].(*channel.GetLastEnterRoomTimeByUidResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLastEnterRoomTimeByUid indicates an expected call of GetLastEnterRoomTimeByUid.
func (mr *MockIClientMockRecorder) GetLastEnterRoomTimeByUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastEnterRoomTimeByUid", reflect.TypeOf((*MockIClient)(nil).GetLastEnterRoomTimeByUid), ctx, in)
}

// GetOnlineInfo mocks base method.
func (m *MockIClient) GetOnlineInfo(ctx context.Context, in *channel.GetOnlineInfoReq) (*channel.GetOnlineInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOnlineInfo", ctx, in)
	ret0, _ := ret[0].(*channel.GetOnlineInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetOnlineInfo indicates an expected call of GetOnlineInfo.
func (mr *MockIClientMockRecorder) GetOnlineInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOnlineInfo", reflect.TypeOf((*MockIClient)(nil).GetOnlineInfo), ctx, in)
}

// GetRecommendChannelList mocks base method.
func (m *MockIClient) GetRecommendChannelList(ctx context.Context, in *channel.GetRecommendChannelListReq) (*channel.GetRecommendChannelListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecommendChannelList", ctx, in)
	ret0, _ := ret[0].(*channel.GetRecommendChannelListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRecommendChannelList indicates an expected call of GetRecommendChannelList.
func (mr *MockIClientMockRecorder) GetRecommendChannelList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendChannelList", reflect.TypeOf((*MockIClient)(nil).GetRecommendChannelList), ctx, in)
}

// GetRecommendChannelListByTab mocks base method.
func (m *MockIClient) GetRecommendChannelListByTab(ctx context.Context, in *channel.GetRecommendChannelListByTabReq) (*channel.GetRecommendChannelListByTabResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecommendChannelListByTab", ctx, in)
	ret0, _ := ret[0].(*channel.GetRecommendChannelListByTabResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRecommendChannelListByTab indicates an expected call of GetRecommendChannelListByTab.
func (mr *MockIClientMockRecorder) GetRecommendChannelListByTab(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendChannelListByTab", reflect.TypeOf((*MockIClient)(nil).GetRecommendChannelListByTab), ctx, in)
}

// KeepChannelAlive mocks base method.
func (m *MockIClient) KeepChannelAlive(ctx context.Context, in *channel.KeepChannelAliveReq) (*channel.KeepChannelAliveResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KeepChannelAlive", ctx, in)
	ret0, _ := ret[0].(*channel.KeepChannelAliveResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// KeepChannelAlive indicates an expected call of KeepChannelAlive.
func (mr *MockIClientMockRecorder) KeepChannelAlive(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KeepChannelAlive", reflect.TypeOf((*MockIClient)(nil).KeepChannelAlive), ctx, in)
}

// SetExtraHistory mocks base method.
func (m *MockIClient) SetExtraHistory(ctx context.Context, in *channel.SetExtraHistoryReq) (*channel.SetExtraHistoryResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetExtraHistory", ctx, in)
	ret0, _ := ret[0].(*channel.SetExtraHistoryResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetExtraHistory indicates an expected call of SetExtraHistory.
func (mr *MockIClientMockRecorder) SetExtraHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetExtraHistory", reflect.TypeOf((*MockIClient)(nil).SetExtraHistory), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// SwitchChannelTab mocks base method.
func (m *MockIClient) SwitchChannelTab(ctx context.Context, in *channel.SwitchChannelTabReq) (*channel.SwitchChannelTabResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwitchChannelTab", ctx, in)
	ret0, _ := ret[0].(*channel.SwitchChannelTabResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SwitchChannelTab indicates an expected call of SwitchChannelTab.
func (mr *MockIClientMockRecorder) SwitchChannelTab(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwitchChannelTab", reflect.TypeOf((*MockIClient)(nil).SwitchChannelTab), ctx, in)
}

// SwitchChannelTabMq mocks base method.
func (m *MockIClient) SwitchChannelTabMq(ctx context.Context, in *channel.SwitchChannelTabMqReq) (*channel.SwitchChannelTabMqResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwitchChannelTabMq", ctx, in)
	ret0, _ := ret[0].(*channel.SwitchChannelTabMqResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SwitchChannelTabMq indicates an expected call of SwitchChannelTabMq.
func (mr *MockIClientMockRecorder) SwitchChannelTabMq(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwitchChannelTabMq", reflect.TypeOf((*MockIClient)(nil).SwitchChannelTabMq), ctx, in)
}

// UnfreezeChannel mocks base method.
func (m *MockIClient) UnfreezeChannel(ctx context.Context, in *channel.UnfreezeChannelReq) (*channel.UnfreezeChannelResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnfreezeChannel", ctx, in)
	ret0, _ := ret[0].(*channel.UnfreezeChannelResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UnfreezeChannel indicates an expected call of UnfreezeChannel.
func (mr *MockIClientMockRecorder) UnfreezeChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfreezeChannel", reflect.TypeOf((*MockIClient)(nil).UnfreezeChannel), ctx, in)
}

// UpdateChannelInfo mocks base method.
func (m *MockIClient) UpdateChannelInfo(ctx context.Context, in *channel.UpdateChannelInfoReq) (*channel.UpdateChannelInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChannelInfo", ctx, in)
	ret0, _ := ret[0].(*channel.UpdateChannelInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateChannelInfo indicates an expected call of UpdateChannelInfo.
func (mr *MockIClientMockRecorder) UpdateChannelInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChannelInfo", reflect.TypeOf((*MockIClient)(nil).UpdateChannelInfo), ctx, in)
}

// UpdateLastEnterRoomTabIdByUid mocks base method.
func (m *MockIClient) UpdateLastEnterRoomTabIdByUid(ctx context.Context, in *channel.UpdateLastEnterRoomTabIdByUidReq) (*channel.UpdateLastEnterRoomTabIdByUidResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLastEnterRoomTabIdByUid", ctx, in)
	ret0, _ := ret[0].(*channel.UpdateLastEnterRoomTabIdByUidResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateLastEnterRoomTabIdByUid indicates an expected call of UpdateLastEnterRoomTabIdByUid.
func (mr *MockIClientMockRecorder) UpdateLastEnterRoomTabIdByUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLastEnterRoomTabIdByUid", reflect.TypeOf((*MockIClient)(nil).UpdateLastEnterRoomTabIdByUid), ctx, in)
}

// UpdateLastEnterRoomTimeByUid mocks base method.
func (m *MockIClient) UpdateLastEnterRoomTimeByUid(ctx context.Context, in *channel.UpdateLastEnterRoomTimeByUidReq) (*channel.UpdateLastEnterRoomTimeByUidResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLastEnterRoomTimeByUid", ctx, in)
	ret0, _ := ret[0].(*channel.UpdateLastEnterRoomTimeByUidResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateLastEnterRoomTimeByUid indicates an expected call of UpdateLastEnterRoomTimeByUid.
func (mr *MockIClientMockRecorder) UpdateLastEnterRoomTimeByUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLastEnterRoomTimeByUid", reflect.TypeOf((*MockIClient)(nil).UpdateLastEnterRoomTimeByUid), ctx, in)
}

// UpdateTopicChannelInfo mocks base method.
func (m *MockIClient) UpdateTopicChannelInfo(ctx context.Context, in *channel.UpdateTopicChannelInfoReq) (*channel.UpdateTopicChannelInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTopicChannelInfo", ctx, in)
	ret0, _ := ret[0].(*channel.UpdateTopicChannelInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateTopicChannelInfo indicates an expected call of UpdateTopicChannelInfo.
func (mr *MockIClientMockRecorder) UpdateTopicChannelInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTopicChannelInfo", reflect.TypeOf((*MockIClient)(nil).UpdateTopicChannelInfo), ctx, in)
}
