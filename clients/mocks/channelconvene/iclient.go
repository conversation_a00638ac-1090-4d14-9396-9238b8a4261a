// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/channelconvene/iclient.go

// Package channelconvene is a generated GoMock package.
package channelconvene

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channelconvene "golang.52tt.com/protocol/services/channelconvene"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// CollectChannel mocks base method.
func (m *MockIClient) CollectChannel(ctx context.Context, uin, cid uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CollectChannel", ctx, uin, cid)
	ret0, _ := ret[0].(error)
	return ret0
}

// CollectChannel indicates an expected call of CollectChannel.
func (mr *MockIClientMockRecorder) CollectChannel(ctx, uin, cid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CollectChannel", reflect.TypeOf((*MockIClient)(nil).CollectChannel), ctx, uin, cid)
}

// GetChannelCollectionListByUid mocks base method.
func (m *MockIClient) GetChannelCollectionListByUid(ctx context.Context, uin uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelCollectionListByUid", ctx, uin)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelCollectionListByUid indicates an expected call of GetChannelCollectionListByUid.
func (mr *MockIClientMockRecorder) GetChannelCollectionListByUid(ctx, uin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelCollectionListByUid", reflect.TypeOf((*MockIClient)(nil).GetChannelCollectionListByUid), ctx, uin)
}

// GetChannelCollectionMemberCount mocks base method.
func (m *MockIClient) GetChannelCollectionMemberCount(ctx context.Context, cid uint32) (*channelconvene.GetChannelCollectionMemberCountResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelCollectionMemberCount", ctx, cid)
	ret0, _ := ret[0].(*channelconvene.GetChannelCollectionMemberCountResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelCollectionMemberCount indicates an expected call of GetChannelCollectionMemberCount.
func (mr *MockIClientMockRecorder) GetChannelCollectionMemberCount(ctx, cid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelCollectionMemberCount", reflect.TypeOf((*MockIClient)(nil).GetChannelCollectionMemberCount), ctx, cid)
}

// GetChannelConveneInfo mocks base method.
func (m *MockIClient) GetChannelConveneInfo(ctx context.Context, uin, cid uint32) (*channelconvene.StChannelConveneInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelConveneInfo", ctx, uin, cid)
	ret0, _ := ret[0].(*channelconvene.StChannelConveneInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelConveneInfo indicates an expected call of GetChannelConveneInfo.
func (mr *MockIClientMockRecorder) GetChannelConveneInfo(ctx, uin, cid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelConveneInfo", reflect.TypeOf((*MockIClient)(nil).GetChannelConveneInfo), ctx, uin, cid)
}

// GetUserConveneInfo mocks base method.
func (m *MockIClient) GetUserConveneInfo(ctx context.Context, uin, cid uint32) (*channelconvene.StChannelConveneMember, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserConveneInfo", ctx, uin, cid)
	ret0, _ := ret[0].(*channelconvene.StChannelConveneMember)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserConveneInfo indicates an expected call of GetUserConveneInfo.
func (mr *MockIClientMockRecorder) GetUserConveneInfo(ctx, uin, cid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserConveneInfo", reflect.TypeOf((*MockIClient)(nil).GetUserConveneInfo), ctx, uin, cid)
}

// HasCollectChannel mocks base method.
func (m *MockIClient) HasCollectChannel(ctx context.Context, uin, cid uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasCollectChannel", ctx, uin, cid)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasCollectChannel indicates an expected call of HasCollectChannel.
func (mr *MockIClientMockRecorder) HasCollectChannel(ctx, uin, cid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasCollectChannel", reflect.TypeOf((*MockIClient)(nil).HasCollectChannel), ctx, uin, cid)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
