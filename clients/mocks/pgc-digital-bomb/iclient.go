// Code generated by MockGen. DO NOT EDIT.
// Source: F:\go-griffin\griffin\clients\pgc-digital-bomb\iclient.go

// Package pgc_digital_bomb is a generated GoMock package.
package pgc_digital_bomb

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	pgc_digital_bomb "golang.52tt.com/protocol/services/pgc-digital-bomb"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetDigitalBombPhase mocks base method.
func (m *MockIClient) BatchGetDigitalBombPhase(ctx context.Context, req *pgc_digital_bomb.BatchGetDigitalBombPhaseReq, opts ...grpc.CallOption) (*pgc_digital_bomb.BatchGetDigitalBombPhaseResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetDigitalBombPhase", varargs...)
	ret0, _ := ret[0].(*pgc_digital_bomb.BatchGetDigitalBombPhaseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetDigitalBombPhase indicates an expected call of BatchGetDigitalBombPhase.
func (mr *MockIClientMockRecorder) BatchGetDigitalBombPhase(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetDigitalBombPhase", reflect.TypeOf((*MockIClient)(nil).BatchGetDigitalBombPhase), varargs...)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DigitalBombEnroll mocks base method.
func (m *MockIClient) DigitalBombEnroll(ctx context.Context, req *pgc_digital_bomb.DigitalBombEnrollReq, opts ...grpc.CallOption) (*pgc_digital_bomb.DigitalBombEnrollResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DigitalBombEnroll", varargs...)
	ret0, _ := ret[0].(*pgc_digital_bomb.DigitalBombEnrollResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DigitalBombEnroll indicates an expected call of DigitalBombEnroll.
func (mr *MockIClientMockRecorder) DigitalBombEnroll(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DigitalBombEnroll", reflect.TypeOf((*MockIClient)(nil).DigitalBombEnroll), varargs...)
}

// DigitalBombSelectGameUser mocks base method.
func (m *MockIClient) DigitalBombSelectGameUser(ctx context.Context, req *pgc_digital_bomb.DigitalBombSelectGameUserReq, opts ...grpc.CallOption) (*pgc_digital_bomb.DigitalBombSelectGameUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DigitalBombSelectGameUser", varargs...)
	ret0, _ := ret[0].(*pgc_digital_bomb.DigitalBombSelectGameUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DigitalBombSelectGameUser indicates an expected call of DigitalBombSelectGameUser.
func (mr *MockIClientMockRecorder) DigitalBombSelectGameUser(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DigitalBombSelectGameUser", reflect.TypeOf((*MockIClient)(nil).DigitalBombSelectGameUser), varargs...)
}

// DigitalBombSelectNumber mocks base method.
func (m *MockIClient) DigitalBombSelectNumber(ctx context.Context, req *pgc_digital_bomb.DigitalBombSelectNumberReq, opts ...grpc.CallOption) (*pgc_digital_bomb.DigitalBombSelectNumberResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DigitalBombSelectNumber", varargs...)
	ret0, _ := ret[0].(*pgc_digital_bomb.DigitalBombSelectNumberResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DigitalBombSelectNumber indicates an expected call of DigitalBombSelectNumber.
func (mr *MockIClientMockRecorder) DigitalBombSelectNumber(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DigitalBombSelectNumber", reflect.TypeOf((*MockIClient)(nil).DigitalBombSelectNumber), varargs...)
}

// GetDigitalBombInfo mocks base method.
func (m *MockIClient) GetDigitalBombInfo(ctx context.Context, req *pgc_digital_bomb.GetDigitalBombInfoReq, opts ...grpc.CallOption) (*pgc_digital_bomb.GetDigitalBombInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDigitalBombInfo", varargs...)
	ret0, _ := ret[0].(*pgc_digital_bomb.GetDigitalBombInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDigitalBombInfo indicates an expected call of GetDigitalBombInfo.
func (mr *MockIClientMockRecorder) GetDigitalBombInfo(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDigitalBombInfo", reflect.TypeOf((*MockIClient)(nil).GetDigitalBombInfo), varargs...)
}

// SetDigitalBombPhase mocks base method.
func (m *MockIClient) SetDigitalBombPhase(ctx context.Context, req *pgc_digital_bomb.SetDigitalBombPhaseReq, opts ...grpc.CallOption) (*pgc_digital_bomb.SetDigitalBombPhaseResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetDigitalBombPhase", varargs...)
	ret0, _ := ret[0].(*pgc_digital_bomb.SetDigitalBombPhaseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetDigitalBombPhase indicates an expected call of SetDigitalBombPhase.
func (mr *MockIClientMockRecorder) SetDigitalBombPhase(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDigitalBombPhase", reflect.TypeOf((*MockIClient)(nil).SetDigitalBombPhase), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
