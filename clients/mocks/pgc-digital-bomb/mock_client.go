// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/pgc-digital-bomb (interfaces: PgcDigitalBombClient)

// Package pgc_digital_bomb is a generated GoMock package.
package pgc_digital_bomb

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	pgc_digital_bomb "golang.52tt.com/protocol/services/pgc-digital-bomb"
	grpc "google.golang.org/grpc"
)

// MockPgcDigitalBombClient is a mock of PgcDigitalBombClient interface.
type MockPgcDigitalBombClient struct {
	ctrl     *gomock.Controller
	recorder *MockPgcDigitalBombClientMockRecorder
}

// MockPgcDigitalBombClientMockRecorder is the mock recorder for MockPgcDigitalBombClient.
type MockPgcDigitalBombClientMockRecorder struct {
	mock *MockPgcDigitalBombClient
}

// NewMockPgcDigitalBombClient creates a new mock instance.
func NewMockPgcDigitalBombClient(ctrl *gomock.Controller) *MockPgcDigitalBombClient {
	mock := &MockPgcDigitalBombClient{ctrl: ctrl}
	mock.recorder = &MockPgcDigitalBombClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPgcDigitalBombClient) EXPECT() *MockPgcDigitalBombClientMockRecorder {
	return m.recorder
}

// BatchGetDigitalBombPhase mocks base method.
func (m *MockPgcDigitalBombClient) BatchGetDigitalBombPhase(arg0 context.Context, arg1 *pgc_digital_bomb.BatchGetDigitalBombPhaseReq, arg2 ...grpc.CallOption) (*pgc_digital_bomb.BatchGetDigitalBombPhaseResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetDigitalBombPhase", varargs...)
	ret0, _ := ret[0].(*pgc_digital_bomb.BatchGetDigitalBombPhaseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetDigitalBombPhase indicates an expected call of BatchGetDigitalBombPhase.
func (mr *MockPgcDigitalBombClientMockRecorder) BatchGetDigitalBombPhase(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetDigitalBombPhase", reflect.TypeOf((*MockPgcDigitalBombClient)(nil).BatchGetDigitalBombPhase), varargs...)
}

// DigitalBombEnroll mocks base method.
func (m *MockPgcDigitalBombClient) DigitalBombEnroll(arg0 context.Context, arg1 *pgc_digital_bomb.DigitalBombEnrollReq, arg2 ...grpc.CallOption) (*pgc_digital_bomb.DigitalBombEnrollResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DigitalBombEnroll", varargs...)
	ret0, _ := ret[0].(*pgc_digital_bomb.DigitalBombEnrollResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DigitalBombEnroll indicates an expected call of DigitalBombEnroll.
func (mr *MockPgcDigitalBombClientMockRecorder) DigitalBombEnroll(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DigitalBombEnroll", reflect.TypeOf((*MockPgcDigitalBombClient)(nil).DigitalBombEnroll), varargs...)
}

// DigitalBombSelectGameUser mocks base method.
func (m *MockPgcDigitalBombClient) DigitalBombSelectGameUser(arg0 context.Context, arg1 *pgc_digital_bomb.DigitalBombSelectGameUserReq, arg2 ...grpc.CallOption) (*pgc_digital_bomb.DigitalBombSelectGameUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DigitalBombSelectGameUser", varargs...)
	ret0, _ := ret[0].(*pgc_digital_bomb.DigitalBombSelectGameUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DigitalBombSelectGameUser indicates an expected call of DigitalBombSelectGameUser.
func (mr *MockPgcDigitalBombClientMockRecorder) DigitalBombSelectGameUser(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DigitalBombSelectGameUser", reflect.TypeOf((*MockPgcDigitalBombClient)(nil).DigitalBombSelectGameUser), varargs...)
}

// DigitalBombSelectNumber mocks base method.
func (m *MockPgcDigitalBombClient) DigitalBombSelectNumber(arg0 context.Context, arg1 *pgc_digital_bomb.DigitalBombSelectNumberReq, arg2 ...grpc.CallOption) (*pgc_digital_bomb.DigitalBombSelectNumberResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DigitalBombSelectNumber", varargs...)
	ret0, _ := ret[0].(*pgc_digital_bomb.DigitalBombSelectNumberResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DigitalBombSelectNumber indicates an expected call of DigitalBombSelectNumber.
func (mr *MockPgcDigitalBombClientMockRecorder) DigitalBombSelectNumber(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DigitalBombSelectNumber", reflect.TypeOf((*MockPgcDigitalBombClient)(nil).DigitalBombSelectNumber), varargs...)
}

// GetDigitalBombInfo mocks base method.
func (m *MockPgcDigitalBombClient) GetDigitalBombInfo(arg0 context.Context, arg1 *pgc_digital_bomb.GetDigitalBombInfoReq, arg2 ...grpc.CallOption) (*pgc_digital_bomb.GetDigitalBombInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDigitalBombInfo", varargs...)
	ret0, _ := ret[0].(*pgc_digital_bomb.GetDigitalBombInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDigitalBombInfo indicates an expected call of GetDigitalBombInfo.
func (mr *MockPgcDigitalBombClientMockRecorder) GetDigitalBombInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDigitalBombInfo", reflect.TypeOf((*MockPgcDigitalBombClient)(nil).GetDigitalBombInfo), varargs...)
}

// SetDigitalBombPhase mocks base method.
func (m *MockPgcDigitalBombClient) SetDigitalBombPhase(arg0 context.Context, arg1 *pgc_digital_bomb.SetDigitalBombPhaseReq, arg2 ...grpc.CallOption) (*pgc_digital_bomb.SetDigitalBombPhaseResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetDigitalBombPhase", varargs...)
	ret0, _ := ret[0].(*pgc_digital_bomb.SetDigitalBombPhaseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetDigitalBombPhase indicates an expected call of SetDigitalBombPhase.
func (mr *MockPgcDigitalBombClientMockRecorder) SetDigitalBombPhase(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDigitalBombPhase", reflect.TypeOf((*MockPgcDigitalBombClient)(nil).SetDigitalBombPhase), varargs...)
}
