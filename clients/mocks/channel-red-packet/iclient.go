// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/channel-red-packet/iclient.go

// Package channel_red_packet is a generated GoMock package.
package channel_red_packet

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channel_red_packet "golang.52tt.com/protocol/services/channel-red-packet"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddRedPacketConf mocks base method.
func (m *MockIClient) AddRedPacketConf(ctx context.Context, req *channel_red_packet.AddRedPacketConfReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRedPacketConf", ctx, req)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddRedPacketConf indicates an expected call of AddRedPacketConf.
func (mr *MockIClientMockRecorder) AddRedPacketConf(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRedPacketConf", reflect.TypeOf((*MockIClient)(nil).AddRedPacketConf), ctx, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CheckIfCanSendRedPacket mocks base method.
func (m *MockIClient) CheckIfCanSendRedPacket(ctx context.Context, opUid, cid uint32) (bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfCanSendRedPacket", ctx, opUid, cid)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckIfCanSendRedPacket indicates an expected call of CheckIfCanSendRedPacket.
func (mr *MockIClientMockRecorder) CheckIfCanSendRedPacket(ctx, opUid, cid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfCanSendRedPacket", reflect.TypeOf((*MockIClient)(nil).CheckIfCanSendRedPacket), ctx, opUid, cid)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DelRedPacketConf mocks base method.
func (m *MockIClient) DelRedPacketConf(ctx context.Context, id uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelRedPacketConf", ctx, id)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// DelRedPacketConf indicates an expected call of DelRedPacketConf.
func (mr *MockIClientMockRecorder) DelRedPacketConf(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelRedPacketConf", reflect.TypeOf((*MockIClient)(nil).DelRedPacketConf), ctx, id)
}

// GetRedPacketAwardTotal mocks base method.
func (m *MockIClient) GetRedPacketAwardTotal(ctx context.Context, beginTime, endTime uint32) (*channel_red_packet.GetRedPacketAwardTotalResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedPacketAwardTotal", ctx, beginTime, endTime)
	ret0, _ := ret[0].(*channel_red_packet.GetRedPacketAwardTotalResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRedPacketAwardTotal indicates an expected call of GetRedPacketAwardTotal.
func (mr *MockIClientMockRecorder) GetRedPacketAwardTotal(ctx, beginTime, endTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedPacketAwardTotal", reflect.TypeOf((*MockIClient)(nil).GetRedPacketAwardTotal), ctx, beginTime, endTime)
}

// GetRedPacketConf mocks base method.
func (m *MockIClient) GetRedPacketConf(ctx context.Context, opUid uint32) (*channel_red_packet.GetRedPacketConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedPacketConf", ctx, opUid)
	ret0, _ := ret[0].(*channel_red_packet.GetRedPacketConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRedPacketConf indicates an expected call of GetRedPacketConf.
func (mr *MockIClientMockRecorder) GetRedPacketConf(ctx, opUid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedPacketConf", reflect.TypeOf((*MockIClient)(nil).GetRedPacketConf), ctx, opUid)
}

// GetRedPacketConfById mocks base method.
func (m *MockIClient) GetRedPacketConfById(ctx context.Context, opUid, id uint32) (*channel_red_packet.GetRedPacketConfByIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedPacketConfById", ctx, opUid, id)
	ret0, _ := ret[0].(*channel_red_packet.GetRedPacketConfByIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRedPacketConfById indicates an expected call of GetRedPacketConfById.
func (mr *MockIClientMockRecorder) GetRedPacketConfById(ctx, opUid, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedPacketConfById", reflect.TypeOf((*MockIClient)(nil).GetRedPacketConfById), ctx, opUid, id)
}

// GetRedPacketList mocks base method.
func (m *MockIClient) GetRedPacketList(ctx context.Context, opUid, channelId uint32) (*channel_red_packet.GetRedPacketListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedPacketList", ctx, opUid, channelId)
	ret0, _ := ret[0].(*channel_red_packet.GetRedPacketListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRedPacketList indicates an expected call of GetRedPacketList.
func (mr *MockIClientMockRecorder) GetRedPacketList(ctx, opUid, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedPacketList", reflect.TypeOf((*MockIClient)(nil).GetRedPacketList), ctx, opUid, channelId)
}

// GetRedPacketOrderTotal mocks base method.
func (m *MockIClient) GetRedPacketOrderTotal(ctx context.Context, beginTime, endTime uint32) (*channel_red_packet.GetRedPacketOrderTotalResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedPacketOrderTotal", ctx, beginTime, endTime)
	ret0, _ := ret[0].(*channel_red_packet.GetRedPacketOrderTotalResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRedPacketOrderTotal indicates an expected call of GetRedPacketOrderTotal.
func (mr *MockIClientMockRecorder) GetRedPacketOrderTotal(ctx, beginTime, endTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedPacketOrderTotal", reflect.TypeOf((*MockIClient)(nil).GetRedPacketOrderTotal), ctx, beginTime, endTime)
}

// ReportRedPacketClickCnt mocks base method.
func (m *MockIClient) ReportRedPacketClickCnt(ctx context.Context, opUid uint32, req *channel_red_packet.ReportRedPacketClickCntReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportRedPacketClickCnt", ctx, opUid, req)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// ReportRedPacketClickCnt indicates an expected call of ReportRedPacketClickCnt.
func (mr *MockIClientMockRecorder) ReportRedPacketClickCnt(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportRedPacketClickCnt", reflect.TypeOf((*MockIClient)(nil).ReportRedPacketClickCnt), ctx, opUid, req)
}

// SendRedPacket mocks base method.
func (m *MockIClient) SendRedPacket(ctx context.Context, opUid uint32, req *channel_red_packet.SendRedPacketReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendRedPacket", ctx, opUid, req)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// SendRedPacket indicates an expected call of SendRedPacket.
func (mr *MockIClientMockRecorder) SendRedPacket(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendRedPacket", reflect.TypeOf((*MockIClient)(nil).SendRedPacket), ctx, opUid, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
