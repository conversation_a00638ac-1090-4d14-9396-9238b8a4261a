// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/pgc-channel-pk (interfaces: IClient)

// Package mocks is a generated GoMock package.
package pgc_channel_pk

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	pgc_channel_pk "golang.52tt.com/protocol/services/pgc-channel-pk"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AcceptPgcChannelPK mocks base method.
func (m *MockIClient) AcceptPgcChannelPK(arg0 context.Context, arg1 *pgc_channel_pk.AcceptPgcChannelPKReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.AcceptPgcChannelPKResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AcceptPgcChannelPK", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.AcceptPgcChannelPKResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcceptPgcChannelPK indicates an expected call of AcceptPgcChannelPK.
func (mr *MockIClientMockRecorder) AcceptPgcChannelPK(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptPgcChannelPK", reflect.TypeOf((*MockIClient)(nil).AcceptPgcChannelPK), varargs...)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// ChoseInteraction mocks base method.
func (m *MockIClient) ChoseInteraction(arg0 context.Context, arg1 *pgc_channel_pk.ChoseInteractionReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.ChoseInteractionResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ChoseInteraction", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.ChoseInteractionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChoseInteraction indicates an expected call of ChoseInteraction.
func (mr *MockIClientMockRecorder) ChoseInteraction(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChoseInteraction", reflect.TypeOf((*MockIClient)(nil).ChoseInteraction), varargs...)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetPgcChannelPKAudienceRank mocks base method.
func (m *MockIClient) GetPgcChannelPKAudienceRank(arg0 context.Context, arg1 *pgc_channel_pk.GetPgcChannelPKAudienceRankReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.GetPgcChannelPKAudienceRankResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPgcChannelPKAudienceRank", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.GetPgcChannelPKAudienceRankResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPgcChannelPKAudienceRank indicates an expected call of GetPgcChannelPKAudienceRank.
func (mr *MockIClientMockRecorder) GetPgcChannelPKAudienceRank(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPgcChannelPKAudienceRank", reflect.TypeOf((*MockIClient)(nil).GetPgcChannelPKAudienceRank), varargs...)
}

// GetPgcChannelPKChannelList mocks base method.
func (m *MockIClient) GetPgcChannelPKChannelList(arg0 context.Context, arg1 *pgc_channel_pk.GetPgcChannelPKChannelListReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.GetPgcChannelPKChannelListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPgcChannelPKChannelList", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.GetPgcChannelPKChannelListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPgcChannelPKChannelList indicates an expected call of GetPgcChannelPKChannelList.
func (mr *MockIClientMockRecorder) GetPgcChannelPKChannelList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPgcChannelPKChannelList", reflect.TypeOf((*MockIClient)(nil).GetPgcChannelPKChannelList), varargs...)
}

// GetPgcChannelPKEntry mocks base method.
func (m *MockIClient) GetPgcChannelPKEntry(arg0 context.Context, arg1 *pgc_channel_pk.GetPgcChannelPKEntryReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.GetPgcChannelPKEntryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPgcChannelPKEntry", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.GetPgcChannelPKEntryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPgcChannelPKEntry indicates an expected call of GetPgcChannelPKEntry.
func (mr *MockIClientMockRecorder) GetPgcChannelPKEntry(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPgcChannelPKEntry", reflect.TypeOf((*MockIClient)(nil).GetPgcChannelPKEntry), varargs...)
}

// GetPgcChannelPKId mocks base method.
func (m *MockIClient) GetPgcChannelPKId(arg0 context.Context, arg1 *pgc_channel_pk.GetPgcChannelPKIdReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.GetPgcChannelPKIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPgcChannelPKId", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.GetPgcChannelPKIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPgcChannelPKId indicates an expected call of GetPgcChannelPKId.
func (mr *MockIClientMockRecorder) GetPgcChannelPKId(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPgcChannelPKId", reflect.TypeOf((*MockIClient)(nil).GetPgcChannelPKId), varargs...)
}

// GetPgcChannelPKInfo mocks base method.
func (m *MockIClient) GetPgcChannelPKInfo(arg0 context.Context, arg1 *pgc_channel_pk.GetPgcChannelPKInfoReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.GetPgcChannelPKInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPgcChannelPKInfo", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.GetPgcChannelPKInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPgcChannelPKInfo indicates an expected call of GetPgcChannelPKInfo.
func (mr *MockIClientMockRecorder) GetPgcChannelPKInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPgcChannelPKInfo", reflect.TypeOf((*MockIClient)(nil).GetPgcChannelPKInfo), varargs...)
}

// GetPgcChannelPKSendGiftScore mocks base method.
func (m *MockIClient) GetPgcChannelPKSendGiftScore(arg0 context.Context, arg1 *pgc_channel_pk.GetPgcChannelPKSendGiftScoreReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.GetPgcChannelPKSendGiftScoreResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPgcChannelPKSendGiftScore", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.GetPgcChannelPKSendGiftScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPgcChannelPKSendGiftScore indicates an expected call of GetPgcChannelPKSendGiftScore.
func (mr *MockIClientMockRecorder) GetPgcChannelPKSendGiftScore(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPgcChannelPKSendGiftScore", reflect.TypeOf((*MockIClient)(nil).GetPgcChannelPKSendGiftScore), varargs...)
}

// PgcChannelPKReportClientIDChange mocks base method.
func (m *MockIClient) PgcChannelPKReportClientIDChange(arg0 context.Context, arg1 *pgc_channel_pk.PgcChannelPKReportClientIDChangeReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.PgcChannelPKReportClientIDChangeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PgcChannelPKReportClientIDChange", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.PgcChannelPKReportClientIDChangeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PgcChannelPKReportClientIDChange indicates an expected call of PgcChannelPKReportClientIDChange.
func (mr *MockIClientMockRecorder) PgcChannelPKReportClientIDChange(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PgcChannelPKReportClientIDChange", reflect.TypeOf((*MockIClient)(nil).PgcChannelPKReportClientIDChange), varargs...)
}

// SetPgcChannelPKEnd mocks base method.
func (m *MockIClient) SetPgcChannelPKEnd(arg0 context.Context, arg1 *pgc_channel_pk.SetPgcChannelPKEndReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.SetPgcChannelPKEndResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetPgcChannelPKEnd", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.SetPgcChannelPKEndResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPgcChannelPKEnd indicates an expected call of SetPgcChannelPKEnd.
func (mr *MockIClientMockRecorder) SetPgcChannelPKEnd(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPgcChannelPKEnd", reflect.TypeOf((*MockIClient)(nil).SetPgcChannelPKEnd), varargs...)
}

// SetPgcChannelPKOpponentMicFlag mocks base method.
func (m *MockIClient) SetPgcChannelPKOpponentMicFlag(arg0 context.Context, arg1 *pgc_channel_pk.SetPgcChannelPKOpponentMicFlagReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.SetPgcChannelPKOpponentMicFlagResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetPgcChannelPKOpponentMicFlag", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.SetPgcChannelPKOpponentMicFlagResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPgcChannelPKOpponentMicFlag indicates an expected call of SetPgcChannelPKOpponentMicFlag.
func (mr *MockIClientMockRecorder) SetPgcChannelPKOpponentMicFlag(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPgcChannelPKOpponentMicFlag", reflect.TypeOf((*MockIClient)(nil).SetPgcChannelPKOpponentMicFlag), varargs...)
}

// SetPgcChannelPKSwitch mocks base method.
func (m *MockIClient) SetPgcChannelPKSwitch(arg0 context.Context, arg1 *pgc_channel_pk.SetPgcChannelPKSwitchReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.SetPgcChannelPKSwitchResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetPgcChannelPKSwitch", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.SetPgcChannelPKSwitchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPgcChannelPKSwitch indicates an expected call of SetPgcChannelPKSwitch.
func (mr *MockIClientMockRecorder) SetPgcChannelPKSwitch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPgcChannelPKSwitch", reflect.TypeOf((*MockIClient)(nil).SetPgcChannelPKSwitch), varargs...)
}

// StartPgcChannelPK mocks base method.
func (m *MockIClient) StartPgcChannelPK(arg0 context.Context, arg1 *pgc_channel_pk.StartPgcChannelPKReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.StartPgcChannelPKResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartPgcChannelPK", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.StartPgcChannelPKResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartPgcChannelPK indicates an expected call of StartPgcChannelPK.
func (mr *MockIClientMockRecorder) StartPgcChannelPK(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartPgcChannelPK", reflect.TypeOf((*MockIClient)(nil).StartPgcChannelPK), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// TestPushMvpImMsg mocks base method.
func (m *MockIClient) TestPushMvpImMsg(arg0 context.Context, arg1 *pgc_channel_pk.TestPushMvpImMsgReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.TestPushMvpImMsgResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestPushMvpImMsg", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.TestPushMvpImMsgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestPushMvpImMsg indicates an expected call of TestPushMvpImMsg.
func (mr *MockIClientMockRecorder) TestPushMvpImMsg(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestPushMvpImMsg", reflect.TypeOf((*MockIClient)(nil).TestPushMvpImMsg), varargs...)
}

// TestSetPgcChannelPKStatus mocks base method.
func (m *MockIClient) TestSetPgcChannelPKStatus(arg0 context.Context, arg1 *pgc_channel_pk.TestSetPgcChannelPKStatusReq, arg2 ...grpc.CallOption) (*pgc_channel_pk.TestSetPgcChannelPKStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestSetPgcChannelPKStatus", varargs...)
	ret0, _ := ret[0].(*pgc_channel_pk.TestSetPgcChannelPKStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestSetPgcChannelPKStatus indicates an expected call of TestSetPgcChannelPKStatus.
func (mr *MockIClientMockRecorder) TestSetPgcChannelPKStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestSetPgcChannelPKStatus", reflect.TypeOf((*MockIClient)(nil).TestSetPgcChannelPKStatus), varargs...)
}
