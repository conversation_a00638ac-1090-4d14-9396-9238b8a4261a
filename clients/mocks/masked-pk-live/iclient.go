// Code generated by MockGen. DO NOT EDIT.
// Source: D:\go-tt\quicksilver\clients\masked-pk-live\iclient.go

// Package masked_pk_live is a generated GoMock package.
package masked_pk_live

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	masked_pk_live "golang.52tt.com/protocol/services/masked-pk-live"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchAddUserToQualification mocks base method.
func (m *MockIClient) BatchAddUserToQualification(ctx context.Context, req *masked_pk_live.BatchAddUserToQualificationReq) (*masked_pk_live.BatchAddUserToQualificationResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddUserToQualification", ctx, req)
	ret0, _ := ret[0].(*masked_pk_live.BatchAddUserToQualificationResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchAddUserToQualification indicates an expected call of BatchAddUserToQualification.
func (mr *MockIClientMockRecorder) BatchAddUserToQualification(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddUserToQualification", reflect.TypeOf((*MockIClient)(nil).BatchAddUserToQualification), ctx, req)
}

// BatchDelUserFromQualification mocks base method.
func (m *MockIClient) BatchDelUserFromQualification(ctx context.Context, uid []uint32) (*masked_pk_live.BatchDelUserFromQualificationResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelUserFromQualification", ctx, uid)
	ret0, _ := ret[0].(*masked_pk_live.BatchDelUserFromQualificationResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchDelUserFromQualification indicates an expected call of BatchDelUserFromQualification.
func (mr *MockIClientMockRecorder) BatchDelUserFromQualification(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelUserFromQualification", reflect.TypeOf((*MockIClient)(nil).BatchDelUserFromQualification), ctx, uid)
}

// BatchGetLiveChannelMaskedPKStatus mocks base method.
func (m *MockIClient) BatchGetLiveChannelMaskedPKStatus(ctx context.Context, gameId uint32, channelIds []uint32) (map[uint32]uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetLiveChannelMaskedPKStatus", ctx, gameId, channelIds)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetLiveChannelMaskedPKStatus indicates an expected call of BatchGetLiveChannelMaskedPKStatus.
func (mr *MockIClientMockRecorder) BatchGetLiveChannelMaskedPKStatus(ctx, gameId, channelIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetLiveChannelMaskedPKStatus", reflect.TypeOf((*MockIClient)(nil).BatchGetLiveChannelMaskedPKStatus), ctx, gameId, channelIds)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CancelLiveChannelMaskedPK mocks base method.
func (m *MockIClient) CancelLiveChannelMaskedPK(ctx context.Context, uid, channelId uint32) (*masked_pk_live.CancelLiveChannelMaskedPKResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelLiveChannelMaskedPK", ctx, uid, channelId)
	ret0, _ := ret[0].(*masked_pk_live.CancelLiveChannelMaskedPKResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CancelLiveChannelMaskedPK indicates an expected call of CancelLiveChannelMaskedPK.
func (mr *MockIClientMockRecorder) CancelLiveChannelMaskedPK(ctx, uid, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelLiveChannelMaskedPK", reflect.TypeOf((*MockIClient)(nil).CancelLiveChannelMaskedPK), ctx, uid, channelId)
}

// CheckMaskedPkRankEntry mocks base method.
func (m *MockIClient) CheckMaskedPkRankEntry(ctx context.Context) (bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckMaskedPkRankEntry", ctx)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckMaskedPkRankEntry indicates an expected call of CheckMaskedPkRankEntry.
func (mr *MockIClientMockRecorder) CheckMaskedPkRankEntry(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckMaskedPkRankEntry", reflect.TypeOf((*MockIClient)(nil).CheckMaskedPkRankEntry), ctx)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetAudienceRankList mocks base method.
func (m *MockIClient) GetAudienceRankList(ctx context.Context, uid, channelId, begin, limit uint32) (*masked_pk_live.GetAudienceRankListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAudienceRankList", ctx, uid, channelId, begin, limit)
	ret0, _ := ret[0].(*masked_pk_live.GetAudienceRankListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAudienceRankList indicates an expected call of GetAudienceRankList.
func (mr *MockIClientMockRecorder) GetAudienceRankList(ctx, uid, channelId, begin, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAudienceRankList", reflect.TypeOf((*MockIClient)(nil).GetAudienceRankList), ctx, uid, channelId, begin, limit)
}

// GetLiveChannelMaskedPKCurrConf mocks base method.
func (m *MockIClient) GetLiveChannelMaskedPKCurrConf(ctx context.Context, uid uint32) (*masked_pk_live.GetLiveChannelMaskedPKCurrConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiveChannelMaskedPKCurrConf", ctx, uid)
	ret0, _ := ret[0].(*masked_pk_live.GetLiveChannelMaskedPKCurrConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLiveChannelMaskedPKCurrConf indicates an expected call of GetLiveChannelMaskedPKCurrConf.
func (mr *MockIClientMockRecorder) GetLiveChannelMaskedPKCurrConf(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveChannelMaskedPKCurrConf", reflect.TypeOf((*MockIClient)(nil).GetLiveChannelMaskedPKCurrConf), ctx, uid)
}

// GetLiveChannelMaskedPKCurrConfWithUser mocks base method.
func (m *MockIClient) GetLiveChannelMaskedPKCurrConfWithUser(ctx context.Context, uid, channelId uint32) (*masked_pk_live.GetLiveChannelMaskedPKCurrConfWithUserResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiveChannelMaskedPKCurrConfWithUser", ctx, uid, channelId)
	ret0, _ := ret[0].(*masked_pk_live.GetLiveChannelMaskedPKCurrConfWithUserResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLiveChannelMaskedPKCurrConfWithUser indicates an expected call of GetLiveChannelMaskedPKCurrConfWithUser.
func (mr *MockIClientMockRecorder) GetLiveChannelMaskedPKCurrConfWithUser(ctx, uid, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveChannelMaskedPKCurrConfWithUser", reflect.TypeOf((*MockIClient)(nil).GetLiveChannelMaskedPKCurrConfWithUser), ctx, uid, channelId)
}

// GetLiveChannelMaskedPKInfo mocks base method.
func (m *MockIClient) GetLiveChannelMaskedPKInfo(ctx context.Context, uid, channelId uint32) (*masked_pk_live.GetLiveChannelMaskedPKInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiveChannelMaskedPKInfo", ctx, uid, channelId)
	ret0, _ := ret[0].(*masked_pk_live.GetLiveChannelMaskedPKInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLiveChannelMaskedPKInfo indicates an expected call of GetLiveChannelMaskedPKInfo.
func (mr *MockIClientMockRecorder) GetLiveChannelMaskedPKInfo(ctx, uid, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveChannelMaskedPKInfo", reflect.TypeOf((*MockIClient)(nil).GetLiveChannelMaskedPKInfo), ctx, uid, channelId)
}

// GetLiveChannelMaskedPKStatus mocks base method.
func (m *MockIClient) GetLiveChannelMaskedPKStatus(ctx context.Context, uid, channelId uint32) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiveChannelMaskedPKStatus", ctx, uid, channelId)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLiveChannelMaskedPKStatus indicates an expected call of GetLiveChannelMaskedPKStatus.
func (mr *MockIClientMockRecorder) GetLiveChannelMaskedPKStatus(ctx, uid, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveChannelMaskedPKStatus", reflect.TypeOf((*MockIClient)(nil).GetLiveChannelMaskedPKStatus), ctx, uid, channelId)
}

// GetMaskedPkGetConsumeTopN mocks base method.
func (m *MockIClient) GetMaskedPkGetConsumeTopN(ctx context.Context, channelId uint32) (*masked_pk_live.MaskedPkGetConsumeTopNResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaskedPkGetConsumeTopN", ctx, channelId)
	ret0, _ := ret[0].(*masked_pk_live.MaskedPkGetConsumeTopNResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMaskedPkGetConsumeTopN indicates an expected call of GetMaskedPkGetConsumeTopN.
func (mr *MockIClientMockRecorder) GetMaskedPkGetConsumeTopN(ctx, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaskedPkGetConsumeTopN", reflect.TypeOf((*MockIClient)(nil).GetMaskedPkGetConsumeTopN), ctx, channelId)
}

// GetUserFromQualification mocks base method.
func (m *MockIClient) GetUserFromQualification(ctx context.Context, uid, count, page uint32) (*masked_pk_live.GetUserFromQualificationResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserFromQualification", ctx, uid, count, page)
	ret0, _ := ret[0].(*masked_pk_live.GetUserFromQualificationResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserFromQualification indicates an expected call of GetUserFromQualification.
func (mr *MockIClientMockRecorder) GetUserFromQualification(ctx, uid, count, page interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserFromQualification", reflect.TypeOf((*MockIClient)(nil).GetUserFromQualification), ctx, uid, count, page)
}

// GiveUpLiveChannelMaskedPK mocks base method.
func (m *MockIClient) GiveUpLiveChannelMaskedPK(ctx context.Context, uid, gameId, channelId uint32) (*masked_pk_live.GiveUpLiveChannelMaskedPKResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GiveUpLiveChannelMaskedPK", ctx, uid, gameId, channelId)
	ret0, _ := ret[0].(*masked_pk_live.GiveUpLiveChannelMaskedPKResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GiveUpLiveChannelMaskedPK indicates an expected call of GiveUpLiveChannelMaskedPK.
func (mr *MockIClientMockRecorder) GiveUpLiveChannelMaskedPK(ctx, uid, gameId, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GiveUpLiveChannelMaskedPK", reflect.TypeOf((*MockIClient)(nil).GiveUpLiveChannelMaskedPK), ctx, uid, gameId, channelId)
}

// StartLiveChannelMaskedPK mocks base method.
func (m *MockIClient) StartLiveChannelMaskedPK(ctx context.Context, uid, channelId uint32) (*masked_pk_live.StartLiveChannelMaskedPKResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartLiveChannelMaskedPK", ctx, uid, channelId)
	ret0, _ := ret[0].(*masked_pk_live.StartLiveChannelMaskedPKResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// StartLiveChannelMaskedPK indicates an expected call of StartLiveChannelMaskedPK.
func (mr *MockIClientMockRecorder) StartLiveChannelMaskedPK(ctx, uid, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartLiveChannelMaskedPK", reflect.TypeOf((*MockIClient)(nil).StartLiveChannelMaskedPK), ctx, uid, channelId)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
