// Code generated by MockGen. DO NOT EDIT.
// Source: /home/<USER>/git/quicksilver/clients/token.v2/iclient.go

// Package token is a generated GoMock package.
package token

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	token_v2 "golang.52tt.com/protocol/services/token.v2"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// ClearToken mocks base method.
func (m *MockIClient) ClearToken(ctx context.Context, uid uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearToken", ctx, uid)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// ClearToken indicates an expected call of ClearToken.
func (mr *MockIClientMockRecorder) ClearToken(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearToken", reflect.TypeOf((*MockIClient)(nil).ClearToken), ctx, uid)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// CreateTokenSeed mocks base method.
func (m *MockIClient) CreateTokenSeed(ctx context.Context, uid, biz uint32, replace bool) (*token_v2.TokenSeed, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTokenSeed", ctx, uid, biz, replace)
	ret0, _ := ret[0].(*token_v2.TokenSeed)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CreateTokenSeed indicates an expected call of CreateTokenSeed.
func (mr *MockIClientMockRecorder) CreateTokenSeed(ctx, uid, biz, replace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTokenSeed", reflect.TypeOf((*MockIClient)(nil).CreateTokenSeed), ctx, uid, biz, replace)
}

// Decode mocks base method.
func (m *MockIClient) Decode(ctx context.Context, appId uint32, token string) (*token_v2.TokenInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Decode", ctx, appId, token)
	ret0, _ := ret[0].(*token_v2.TokenInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Decode indicates an expected call of Decode.
func (mr *MockIClientMockRecorder) Decode(ctx, appId, token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Decode", reflect.TypeOf((*MockIClient)(nil).Decode), ctx, appId, token)
}

// DecodeToken mocks base method.
func (m *MockIClient) DecodeToken(ctx context.Context, token []byte, biz uint32, refresh bool) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecodeToken", ctx, token, biz, refresh)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DecodeToken indicates an expected call of DecodeToken.
func (mr *MockIClientMockRecorder) DecodeToken(ctx, token, biz, refresh interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecodeToken", reflect.TypeOf((*MockIClient)(nil).DecodeToken), ctx, token, biz, refresh)
}

// GetWechatAccessToken mocks base method.
func (m *MockIClient) GetWechatAccessToken(ctx context.Context, appId string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWechatAccessToken", ctx, appId)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWechatAccessToken indicates an expected call of GetWechatAccessToken.
func (mr *MockIClientMockRecorder) GetWechatAccessToken(ctx, appId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWechatAccessToken", reflect.TypeOf((*MockIClient)(nil).GetWechatAccessToken), ctx, appId)
}

// GetWechatApiTicket mocks base method.
func (m *MockIClient) GetWechatApiTicket(ctx context.Context, appId string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWechatApiTicket", ctx, appId)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWechatApiTicket indicates an expected call of GetWechatApiTicket.
func (mr *MockIClientMockRecorder) GetWechatApiTicket(ctx, appId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWechatApiTicket", reflect.TypeOf((*MockIClient)(nil).GetWechatApiTicket), ctx, appId)
}

// GrantToken mocks base method.
func (m *MockIClient) GrantToken(ctx context.Context, tokenInfo *token_v2.TokenInfoV2) (*token_v2.GrantTokenResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GrantToken", ctx, tokenInfo)
	ret0, _ := ret[0].(*token_v2.GrantTokenResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GrantToken indicates an expected call of GrantToken.
func (mr *MockIClientMockRecorder) GrantToken(ctx, tokenInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GrantToken", reflect.TypeOf((*MockIClient)(nil).GrantToken), ctx, tokenInfo)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// ValidateToken mocks base method.
func (m *MockIClient) ValidateToken(ctx context.Context, tokenType token_v2.TokenType, token string) (*token_v2.TokenInfoV2, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateToken", ctx, tokenType, token)
	ret0, _ := ret[0].(*token_v2.TokenInfoV2)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ValidateToken indicates an expected call of ValidateToken.
func (mr *MockIClientMockRecorder) ValidateToken(ctx, tokenType, token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateToken", reflect.TypeOf((*MockIClient)(nil).ValidateToken), ctx, tokenType, token)
}
