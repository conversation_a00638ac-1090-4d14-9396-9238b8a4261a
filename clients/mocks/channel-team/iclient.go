// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/channel-team/iclient.go

// Package channel_team is a generated GoMock package.
package channel_team

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channel_team "golang.52tt.com/protocol/services/channel-team"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AgreeChannelTeamApply mocks base method.
func (m *MockIClient) AgreeChannelTeamApply(ctx context.Context, in *channel_team.AgreeChannelTeamApplyReq) (*channel_team.AgreeChannelTeamApplyResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AgreeChannelTeamApply", ctx, in)
	ret0, _ := ret[0].(*channel_team.AgreeChannelTeamApplyResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AgreeChannelTeamApply indicates an expected call of AgreeChannelTeamApply.
func (mr *MockIClientMockRecorder) AgreeChannelTeamApply(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AgreeChannelTeamApply", reflect.TypeOf((*MockIClient)(nil).AgreeChannelTeamApply), ctx, in)
}

// BatchGetChannelTeamInfo mocks base method.
func (m *MockIClient) BatchGetChannelTeamInfo(ctx context.Context, in *channel_team.BatchGetChannelTeamInfoReq) (*channel_team.BatchGetChannelTeamInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelTeamInfo", ctx, in)
	ret0, _ := ret[0].(*channel_team.BatchGetChannelTeamInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetChannelTeamInfo indicates an expected call of BatchGetChannelTeamInfo.
func (mr *MockIClientMockRecorder) BatchGetChannelTeamInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelTeamInfo", reflect.TypeOf((*MockIClient)(nil).BatchGetChannelTeamInfo), ctx, in)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// ChangeTabId mocks base method.
func (m *MockIClient) ChangeTabId(ctx context.Context, in *channel_team.ChangeTabIdReq) (*channel_team.ChangeTabIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeTabId", ctx, in)
	ret0, _ := ret[0].(*channel_team.ChangeTabIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ChangeTabId indicates an expected call of ChangeTabId.
func (mr *MockIClientMockRecorder) ChangeTabId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeTabId", reflect.TypeOf((*MockIClient)(nil).ChangeTabId), ctx, in)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetAllChannelMember mocks base method.
func (m *MockIClient) GetAllChannelMember(ctx context.Context, in *channel_team.GetAllChannelMemberReq) (*channel_team.GetAllChannelMemberResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllChannelMember", ctx, in)
	ret0, _ := ret[0].(*channel_team.GetAllChannelMemberResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllChannelMember indicates an expected call of GetAllChannelMember.
func (mr *MockIClientMockRecorder) GetAllChannelMember(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllChannelMember", reflect.TypeOf((*MockIClient)(nil).GetAllChannelMember), ctx, in)
}

// GetChannelTeamApplyList mocks base method.
func (m *MockIClient) GetChannelTeamApplyList(ctx context.Context, in *channel_team.GetChannelTeamApplyListReq) (*channel_team.GetChannelTeamApplyListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelTeamApplyList", ctx, in)
	ret0, _ := ret[0].(*channel_team.GetChannelTeamApplyListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelTeamApplyList indicates an expected call of GetChannelTeamApplyList.
func (mr *MockIClientMockRecorder) GetChannelTeamApplyList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelTeamApplyList", reflect.TypeOf((*MockIClient)(nil).GetChannelTeamApplyList), ctx, in)
}

// GetChannelTeamMemberList mocks base method.
func (m *MockIClient) GetChannelTeamMemberList(ctx context.Context, in *channel_team.GetChannelTeamMemberListReq) (*channel_team.GetChannelTeamMemberListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelTeamMemberList", ctx, in)
	ret0, _ := ret[0].(*channel_team.GetChannelTeamMemberListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelTeamMemberList indicates an expected call of GetChannelTeamMemberList.
func (mr *MockIClientMockRecorder) GetChannelTeamMemberList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelTeamMemberList", reflect.TypeOf((*MockIClient)(nil).GetChannelTeamMemberList), ctx, in)
}

// GetGangUpHistory mocks base method.
func (m *MockIClient) GetGangUpHistory(ctx context.Context, in *channel_team.GetGangUpHistoryReq) (*channel_team.GetGangUpHistoryResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGangUpHistory", ctx, in)
	ret0, _ := ret[0].(*channel_team.GetGangUpHistoryResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGangUpHistory indicates an expected call of GetGangUpHistory.
func (mr *MockIClientMockRecorder) GetGangUpHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGangUpHistory", reflect.TypeOf((*MockIClient)(nil).GetGangUpHistory), ctx, in)
}

// GetLocationConfig mocks base method.
func (m *MockIClient) GetLocationConfig(ctx context.Context, in *channel_team.GetLocationConfigReq) (*channel_team.GetLocationConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLocationConfig", ctx, in)
	ret0, _ := ret[0].(*channel_team.GetLocationConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLocationConfig indicates an expected call of GetLocationConfig.
func (mr *MockIClientMockRecorder) GetLocationConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLocationConfig", reflect.TypeOf((*MockIClient)(nil).GetLocationConfig), ctx, in)
}

// JoinChannelTeam mocks base method.
func (m *MockIClient) JoinChannelTeam(ctx context.Context, in *channel_team.JoinChannelTeamReq) (*channel_team.JoinChannelTeamResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JoinChannelTeam", ctx, in)
	ret0, _ := ret[0].(*channel_team.JoinChannelTeamResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// JoinChannelTeam indicates an expected call of JoinChannelTeam.
func (mr *MockIClientMockRecorder) JoinChannelTeam(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JoinChannelTeam", reflect.TypeOf((*MockIClient)(nil).JoinChannelTeam), ctx, in)
}

// SetChannelTeamInfo mocks base method.
func (m *MockIClient) SetChannelTeamInfo(ctx context.Context, in *channel_team.SetChannelTeamInfoReq) (*channel_team.SetChannelTeamInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelTeamInfo", ctx, in)
	ret0, _ := ret[0].(*channel_team.SetChannelTeamInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelTeamInfo indicates an expected call of SetChannelTeamInfo.
func (mr *MockIClientMockRecorder) SetChannelTeamInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelTeamInfo", reflect.TypeOf((*MockIClient)(nil).SetChannelTeamInfo), ctx, in)
}

// SetGameNickname mocks base method.
func (m *MockIClient) SetGameNickname(ctx context.Context, in *channel_team.SetGameNicknameReq) (*channel_team.SetGameNicknameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGameNickname", ctx, in)
	ret0, _ := ret[0].(*channel_team.SetGameNicknameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetGameNickname indicates an expected call of SetGameNickname.
func (mr *MockIClientMockRecorder) SetGameNickname(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGameNickname", reflect.TypeOf((*MockIClient)(nil).SetGameNickname), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// TickChannelTeamMember mocks base method.
func (m *MockIClient) TickChannelTeamMember(ctx context.Context, in *channel_team.TickChannelTeamMemberReq) (*channel_team.TickChannelTeamMemberResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TickChannelTeamMember", ctx, in)
	ret0, _ := ret[0].(*channel_team.TickChannelTeamMemberResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// TickChannelTeamMember indicates an expected call of TickChannelTeamMember.
func (mr *MockIClientMockRecorder) TickChannelTeamMember(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TickChannelTeamMember", reflect.TypeOf((*MockIClient)(nil).TickChannelTeamMember), ctx, in)
}
