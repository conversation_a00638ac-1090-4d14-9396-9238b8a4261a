// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/channel_music_go (interfaces: ChannelMusicGoClient)

// Package channel_music_go is a generated GoMock package.
package channel_music_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	channel_music_go "golang.52tt.com/protocol/services/channel_music_go"
	grpc "google.golang.org/grpc"
)

// MockChannelMusicGoClient is a mock of ChannelMusicGoClient interface.
type MockChannelMusicGoClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelMusicGoClientMockRecorder
}

// MockChannelMusicGoClientMockRecorder is the mock recorder for MockChannelMusicGoClient.
type MockChannelMusicGoClientMockRecorder struct {
	mock *MockChannelMusicGoClient
}

// NewMockChannelMusicGoClient creates a new mock instance.
func NewMockChannelMusicGoClient(ctrl *gomock.Controller) *MockChannelMusicGoClient {
	mock := &MockChannelMusicGoClient{ctrl: ctrl}
	mock.recorder = &MockChannelMusicGoClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelMusicGoClient) EXPECT() *MockChannelMusicGoClientMockRecorder {
	return m.recorder
}

// AddChannelMusic mocks base method.
func (m *MockChannelMusicGoClient) AddChannelMusic(arg0 context.Context, arg1 *channel_music_go.AddChannelMusicRequest, arg2 ...grpc.CallOption) (*channel_music_go.AddChannelMusicResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddChannelMusic", varargs...)
	ret0, _ := ret[0].(*channel_music_go.AddChannelMusicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddChannelMusic indicates an expected call of AddChannelMusic.
func (mr *MockChannelMusicGoClientMockRecorder) AddChannelMusic(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelMusic", reflect.TypeOf((*MockChannelMusicGoClient)(nil).AddChannelMusic), varargs...)
}

// ChannelMusicCtrl mocks base method.
func (m *MockChannelMusicGoClient) ChannelMusicCtrl(arg0 context.Context, arg1 *channel_music_go.ChannelMusicCtrlRequest, arg2 ...grpc.CallOption) (*channel_music_go.ChannelMusicCtrlResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ChannelMusicCtrl", varargs...)
	ret0, _ := ret[0].(*channel_music_go.ChannelMusicCtrlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChannelMusicCtrl indicates an expected call of ChannelMusicCtrl.
func (mr *MockChannelMusicGoClientMockRecorder) ChannelMusicCtrl(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelMusicCtrl", reflect.TypeOf((*MockChannelMusicGoClient)(nil).ChannelMusicCtrl), varargs...)
}

// ClearChannelMusicList mocks base method.
func (m *MockChannelMusicGoClient) ClearChannelMusicList(arg0 context.Context, arg1 *channel_music_go.ClearChannelMusicListRequest, arg2 ...grpc.CallOption) (*channel_music_go.ClearChannelMusicListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearChannelMusicList", varargs...)
	ret0, _ := ret[0].(*channel_music_go.ClearChannelMusicListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearChannelMusicList indicates an expected call of ClearChannelMusicList.
func (mr *MockChannelMusicGoClientMockRecorder) ClearChannelMusicList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearChannelMusicList", reflect.TypeOf((*MockChannelMusicGoClient)(nil).ClearChannelMusicList), varargs...)
}

// GetChannelMusicList mocks base method.
func (m *MockChannelMusicGoClient) GetChannelMusicList(arg0 context.Context, arg1 *channel_music_go.GetChannelMusicListRequest, arg2 ...grpc.CallOption) (*channel_music_go.GetChannelMusicListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelMusicList", varargs...)
	ret0, _ := ret[0].(*channel_music_go.GetChannelMusicListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelMusicList indicates an expected call of GetChannelMusicList.
func (mr *MockChannelMusicGoClientMockRecorder) GetChannelMusicList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelMusicList", reflect.TypeOf((*MockChannelMusicGoClient)(nil).GetChannelMusicList), varargs...)
}

// GetChannelMusicListType mocks base method.
func (m *MockChannelMusicGoClient) GetChannelMusicListType(arg0 context.Context, arg1 *channel_music_go.GetChannelMusicListTypeRequest, arg2 ...grpc.CallOption) (*channel_music_go.GetChannelMusicListTypeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelMusicListType", varargs...)
	ret0, _ := ret[0].(*channel_music_go.GetChannelMusicListTypeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelMusicListType indicates an expected call of GetChannelMusicListType.
func (mr *MockChannelMusicGoClientMockRecorder) GetChannelMusicListType(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelMusicListType", reflect.TypeOf((*MockChannelMusicGoClient)(nil).GetChannelMusicListType), varargs...)
}

// GetChannelMusicStatus mocks base method.
func (m *MockChannelMusicGoClient) GetChannelMusicStatus(arg0 context.Context, arg1 *channel_music_go.GetChannelMusicStatusRequest, arg2 ...grpc.CallOption) (*channel_music_go.GetChannelMusicStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelMusicStatus", varargs...)
	ret0, _ := ret[0].(*channel_music_go.GetChannelMusicStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelMusicStatus indicates an expected call of GetChannelMusicStatus.
func (mr *MockChannelMusicGoClientMockRecorder) GetChannelMusicStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelMusicStatus", reflect.TypeOf((*MockChannelMusicGoClient)(nil).GetChannelMusicStatus), varargs...)
}

// SetChannelMusicCurrent mocks base method.
func (m *MockChannelMusicGoClient) SetChannelMusicCurrent(arg0 context.Context, arg1 *channel_music_go.SetChannelMusicCurrentRequest, arg2 ...grpc.CallOption) (*channel_music_go.SetChannelMusicCurrentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChannelMusicCurrent", varargs...)
	ret0, _ := ret[0].(*channel_music_go.SetChannelMusicCurrentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelMusicCurrent indicates an expected call of SetChannelMusicCurrent.
func (mr *MockChannelMusicGoClientMockRecorder) SetChannelMusicCurrent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelMusicCurrent", reflect.TypeOf((*MockChannelMusicGoClient)(nil).SetChannelMusicCurrent), varargs...)
}

// SetChannelMusicListType mocks base method.
func (m *MockChannelMusicGoClient) SetChannelMusicListType(arg0 context.Context, arg1 *channel_music_go.SetChannelMusicListTypeRequest, arg2 ...grpc.CallOption) (*channel_music_go.SetChannelMusicListTypeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChannelMusicListType", varargs...)
	ret0, _ := ret[0].(*channel_music_go.SetChannelMusicListTypeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelMusicListType indicates an expected call of SetChannelMusicListType.
func (mr *MockChannelMusicGoClientMockRecorder) SetChannelMusicListType(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelMusicListType", reflect.TypeOf((*MockChannelMusicGoClient)(nil).SetChannelMusicListType), varargs...)
}

// SetChannelMusicStatus mocks base method.
func (m *MockChannelMusicGoClient) SetChannelMusicStatus(arg0 context.Context, arg1 *channel_music_go.SetChannelMusicStatusRequest, arg2 ...grpc.CallOption) (*channel_music_go.SetChannelMusicStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChannelMusicStatus", varargs...)
	ret0, _ := ret[0].(*channel_music_go.SetChannelMusicStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelMusicStatus indicates an expected call of SetChannelMusicStatus.
func (mr *MockChannelMusicGoClientMockRecorder) SetChannelMusicStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelMusicStatus", reflect.TypeOf((*MockChannelMusicGoClient)(nil).SetChannelMusicStatus), varargs...)
}
