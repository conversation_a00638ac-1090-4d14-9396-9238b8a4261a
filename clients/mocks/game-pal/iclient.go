// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/game-pal (interfaces: IClient)

// Package game_pal is a generated GoMock package.
package game_pal

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	game_pal "golang.52tt.com/protocol/services/game-pal"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BanGamePalCard mocks base method.
func (m *MockIClient) BanGamePalCard(arg0 context.Context, arg1 *game_pal.BanGamePalCardReq, arg2 ...grpc.CallOption) (*game_pal.BanGamePalCardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BanGamePalCard", varargs...)
	ret0, _ := ret[0].(*game_pal.BanGamePalCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BanGamePalCard indicates an expected call of BanGamePalCard.
func (mr *MockIClientMockRecorder) BanGamePalCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BanGamePalCard", reflect.TypeOf((*MockIClient)(nil).BanGamePalCard), varargs...)
}

// BatchExtinctGamePalCard mocks base method.
func (m *MockIClient) BatchExtinctGamePalCard(arg0 context.Context, arg1 *game_pal.BatchExtinctGamePalCardReq, arg2 ...grpc.CallOption) (*game_pal.BatchExtinctGamePalCardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchExtinctGamePalCard", varargs...)
	ret0, _ := ret[0].(*game_pal.BatchExtinctGamePalCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchExtinctGamePalCard indicates an expected call of BatchExtinctGamePalCard.
func (mr *MockIClientMockRecorder) BatchExtinctGamePalCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchExtinctGamePalCard", reflect.TypeOf((*MockIClient)(nil).BatchExtinctGamePalCard), varargs...)
}

// BatchGetGamePalReleaseCondition mocks base method.
func (m *MockIClient) BatchGetGamePalReleaseCondition(arg0 context.Context, arg1 *game_pal.BatchGetGamePalReleaseConditionReq, arg2 ...grpc.CallOption) (*game_pal.BatchGetGamePalReleaseConditionResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetGamePalReleaseCondition", varargs...)
	ret0, _ := ret[0].(*game_pal.BatchGetGamePalReleaseConditionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGamePalReleaseCondition indicates an expected call of BatchGetGamePalReleaseCondition.
func (mr *MockIClientMockRecorder) BatchGetGamePalReleaseCondition(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGamePalReleaseCondition", reflect.TypeOf((*MockIClient)(nil).BatchGetGamePalReleaseCondition), varargs...)
}

// BatchPolishGamePalCard mocks base method.
func (m *MockIClient) BatchPolishGamePalCard(arg0 context.Context, arg1 *game_pal.BatchPolishGamePalCardReq, arg2 ...grpc.CallOption) (*game_pal.BatchPolishGamePalCardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchPolishGamePalCard", varargs...)
	ret0, _ := ret[0].(*game_pal.BatchPolishGamePalCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchPolishGamePalCard indicates an expected call of BatchPolishGamePalCard.
func (mr *MockIClientMockRecorder) BatchPolishGamePalCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchPolishGamePalCard", reflect.TypeOf((*MockIClient)(nil).BatchPolishGamePalCard), varargs...)
}

// DelSocialDeclConfig mocks base method.
func (m *MockIClient) DelSocialDeclConfig(arg0 context.Context, arg1 *game_pal.DelSocialDeclConfigReq, arg2 ...grpc.CallOption) (*game_pal.DelSocialDeclConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelSocialDeclConfig", varargs...)
	ret0, _ := ret[0].(*game_pal.DelSocialDeclConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelSocialDeclConfig indicates an expected call of DelSocialDeclConfig.
func (mr *MockIClientMockRecorder) DelSocialDeclConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSocialDeclConfig", reflect.TypeOf((*MockIClient)(nil).DelSocialDeclConfig), varargs...)
}

// DeleteGamePalCard mocks base method.
func (m *MockIClient) DeleteGamePalCard(arg0 context.Context, arg1 *game_pal.DeleteGamePalCardReq, arg2 ...grpc.CallOption) (*game_pal.DeleteGamePalCardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteGamePalCard", varargs...)
	ret0, _ := ret[0].(*game_pal.DeleteGamePalCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteGamePalCard indicates an expected call of DeleteGamePalCard.
func (mr *MockIClientMockRecorder) DeleteGamePalCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteGamePalCard", reflect.TypeOf((*MockIClient)(nil).DeleteGamePalCard), varargs...)
}

// DeleteGamePalReleaseCondition mocks base method.
func (m *MockIClient) DeleteGamePalReleaseCondition(arg0 context.Context, arg1 *game_pal.DeleteGamePalReleaseConditionReq, arg2 ...grpc.CallOption) (*game_pal.DeleteGamePalReleaseConditionResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteGamePalReleaseCondition", varargs...)
	ret0, _ := ret[0].(*game_pal.DeleteGamePalReleaseConditionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteGamePalReleaseCondition indicates an expected call of DeleteGamePalReleaseCondition.
func (mr *MockIClientMockRecorder) DeleteGamePalReleaseCondition(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteGamePalReleaseCondition", reflect.TypeOf((*MockIClient)(nil).DeleteGamePalReleaseCondition), varargs...)
}

// GetGamePalCard mocks base method.
func (m *MockIClient) GetGamePalCard(arg0 context.Context, arg1 *game_pal.GetGamePalCardReq, arg2 ...grpc.CallOption) (*game_pal.GetGamePalCardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGamePalCard", varargs...)
	ret0, _ := ret[0].(*game_pal.GetGamePalCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGamePalCard indicates an expected call of GetGamePalCard.
func (mr *MockIClientMockRecorder) GetGamePalCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGamePalCard", reflect.TypeOf((*MockIClient)(nil).GetGamePalCard), varargs...)
}

// GetGamePalCardList mocks base method.
func (m *MockIClient) GetGamePalCardList(arg0 context.Context, arg1 *game_pal.GetGamePalCardListReq, arg2 ...grpc.CallOption) (*game_pal.GetGamePalCardListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGamePalCardList", varargs...)
	ret0, _ := ret[0].(*game_pal.GetGamePalCardListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGamePalCardList indicates an expected call of GetGamePalCardList.
func (mr *MockIClientMockRecorder) GetGamePalCardList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGamePalCardList", reflect.TypeOf((*MockIClient)(nil).GetGamePalCardList), varargs...)
}

// GetSocialDeclConfigs mocks base method.
func (m *MockIClient) GetSocialDeclConfigs(arg0 context.Context, arg1 *game_pal.GetSocialDeclConfigsReq, arg2 ...grpc.CallOption) (*game_pal.GetSocialDeclConfigsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSocialDeclConfigs", varargs...)
	ret0, _ := ret[0].(*game_pal.GetSocialDeclConfigsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSocialDeclConfigs indicates an expected call of GetSocialDeclConfigs.
func (mr *MockIClientMockRecorder) GetSocialDeclConfigs(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocialDeclConfigs", reflect.TypeOf((*MockIClient)(nil).GetSocialDeclConfigs), varargs...)
}

// GetTabPolishedCardCountMap mocks base method.
func (m *MockIClient) GetTabPolishedCardCountMap(arg0 context.Context, arg1 *game_pal.GetTabPolishedCardCountMapReq, arg2 ...grpc.CallOption) (*game_pal.GetTabPolishedCardCountMapResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTabPolishedCardCountMap", varargs...)
	ret0, _ := ret[0].(*game_pal.GetTabPolishedCardCountMapResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabPolishedCardCountMap indicates an expected call of GetTabPolishedCardCountMap.
func (mr *MockIClientMockRecorder) GetTabPolishedCardCountMap(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabPolishedCardCountMap", reflect.TypeOf((*MockIClient)(nil).GetTabPolishedCardCountMap), varargs...)
}

// GetUserGamePalCard mocks base method.
func (m *MockIClient) GetUserGamePalCard(arg0 context.Context, arg1 *game_pal.GetUserGamePalCardReq, arg2 ...grpc.CallOption) (*game_pal.GetUserGamePalCardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserGamePalCard", varargs...)
	ret0, _ := ret[0].(*game_pal.GetUserGamePalCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserGamePalCard indicates an expected call of GetUserGamePalCard.
func (mr *MockIClientMockRecorder) GetUserGamePalCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserGamePalCard", reflect.TypeOf((*MockIClient)(nil).GetUserGamePalCard), varargs...)
}

// GetUserGamePalCardList mocks base method.
func (m *MockIClient) GetUserGamePalCardList(arg0 context.Context, arg1 *game_pal.GetUserGamePalCardListReq, arg2 ...grpc.CallOption) (*game_pal.GetUserGamePalCardListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserGamePalCardList", varargs...)
	ret0, _ := ret[0].(*game_pal.GetUserGamePalCardListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserGamePalCardList indicates an expected call of GetUserGamePalCardList.
func (mr *MockIClientMockRecorder) GetUserGamePalCardList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserGamePalCardList", reflect.TypeOf((*MockIClient)(nil).GetUserGamePalCardList), varargs...)
}

// SendEditCardEvent mocks base method.
func (m *MockIClient) SendEditCardEvent(arg0 context.Context, arg1 *game_pal.SendEditCardEventReq, arg2 ...grpc.CallOption) (*game_pal.SendEditCardEventResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendEditCardEvent", varargs...)
	ret0, _ := ret[0].(*game_pal.SendEditCardEventResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendEditCardEvent indicates an expected call of SendEditCardEvent.
func (mr *MockIClientMockRecorder) SendEditCardEvent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEditCardEvent", reflect.TypeOf((*MockIClient)(nil).SendEditCardEvent), varargs...)
}

// UpsertGamePalCard mocks base method.
func (m *MockIClient) UpsertGamePalCard(arg0 context.Context, arg1 *game_pal.UpsertGamePalCardReq, arg2 ...grpc.CallOption) (*game_pal.UpsertGamePalCardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertGamePalCard", varargs...)
	ret0, _ := ret[0].(*game_pal.UpsertGamePalCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertGamePalCard indicates an expected call of UpsertGamePalCard.
func (mr *MockIClientMockRecorder) UpsertGamePalCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertGamePalCard", reflect.TypeOf((*MockIClient)(nil).UpsertGamePalCard), varargs...)
}

// UpsertGamePalReleaseCondition mocks base method.
func (m *MockIClient) UpsertGamePalReleaseCondition(arg0 context.Context, arg1 *game_pal.UpsertGamePalReleaseConditionReq, arg2 ...grpc.CallOption) (*game_pal.UpsertGamePalReleaseConditionResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertGamePalReleaseCondition", varargs...)
	ret0, _ := ret[0].(*game_pal.UpsertGamePalReleaseConditionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertGamePalReleaseCondition indicates an expected call of UpsertGamePalReleaseCondition.
func (mr *MockIClientMockRecorder) UpsertGamePalReleaseCondition(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertGamePalReleaseCondition", reflect.TypeOf((*MockIClient)(nil).UpsertGamePalReleaseCondition), varargs...)
}

// UpsertSocialDeclConfig mocks base method.
func (m *MockIClient) UpsertSocialDeclConfig(arg0 context.Context, arg1 *game_pal.UpsertSocialDeclConfigReq, arg2 ...grpc.CallOption) (*game_pal.UpsertSocialDeclConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertSocialDeclConfig", varargs...)
	ret0, _ := ret[0].(*game_pal.UpsertSocialDeclConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertSocialDeclConfig indicates an expected call of UpsertSocialDeclConfig.
func (mr *MockIClientMockRecorder) UpsertSocialDeclConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertSocialDeclConfig", reflect.TypeOf((*MockIClient)(nil).UpsertSocialDeclConfig), varargs...)
}
