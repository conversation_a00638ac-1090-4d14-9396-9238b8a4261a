// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/user-checkin/iclient.go

// Package usercheckin is a generated GoMock package.
package usercheckin

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	usercheckin "golang.52tt.com/protocol/services/usercheckin"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetConfig mocks base method.
func (m *MockIClient) GetConfig(ctx context.Context, req usercheckin.GetConfigReq) (*usercheckin.GetConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfig", ctx, req)
	ret0, _ := ret[0].(*usercheckin.GetConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetConfig indicates an expected call of GetConfig.
func (mr *MockIClientMockRecorder) GetConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfig", reflect.TypeOf((*MockIClient)(nil).GetConfig), ctx, req)
}

// GetTBeanCnt mocks base method.
func (m *MockIClient) GetTBeanCnt(ctx context.Context) (*usercheckin.GetTBeanCntResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTBeanCnt", ctx)
	ret0, _ := ret[0].(*usercheckin.GetTBeanCntResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetTBeanCnt indicates an expected call of GetTBeanCnt.
func (mr *MockIClientMockRecorder) GetTBeanCnt(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTBeanCnt", reflect.TypeOf((*MockIClient)(nil).GetTBeanCnt), ctx)
}

// GetUserAwardInfo mocks base method.
func (m *MockIClient) GetUserAwardInfo(ctx context.Context, req usercheckin.GetUserAwardInfoReq) (*usercheckin.GetUserAwardInfoRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAwardInfo", ctx, req)
	ret0, _ := ret[0].(*usercheckin.GetUserAwardInfoRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserAwardInfo indicates an expected call of GetUserAwardInfo.
func (mr *MockIClientMockRecorder) GetUserAwardInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAwardInfo", reflect.TypeOf((*MockIClient)(nil).GetUserAwardInfo), ctx, req)
}

// ReceiveAward mocks base method.
func (m *MockIClient) ReceiveAward(ctx context.Context, req usercheckin.ReceiveAwardReq) (*usercheckin.ReceiveAwardRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReceiveAward", ctx, req)
	ret0, _ := ret[0].(*usercheckin.ReceiveAwardRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ReceiveAward indicates an expected call of ReceiveAward.
func (mr *MockIClientMockRecorder) ReceiveAward(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReceiveAward", reflect.TypeOf((*MockIClient)(nil).ReceiveAward), ctx, req)
}

// SetDeeplinkSource mocks base method.
func (m *MockIClient) SetDeeplinkSource(ctx context.Context, in *usercheckin.SetDeeplinkSourceReq) (*usercheckin.SetDeeplinkSourceRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDeeplinkSource", ctx, in)
	ret0, _ := ret[0].(*usercheckin.SetDeeplinkSourceRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetDeeplinkSource indicates an expected call of SetDeeplinkSource.
func (mr *MockIClientMockRecorder) SetDeeplinkSource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDeeplinkSource", reflect.TypeOf((*MockIClient)(nil).SetDeeplinkSource), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
