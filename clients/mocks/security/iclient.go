// Code generated by MockGen. DO NOT EDIT.
// Source: /home/<USER>/git/quicksilver/clients/security/iclient.go

// Package security is a generated GoMock package.
package security

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	security "golang.52tt.com/protocol/services/security"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetOperationLog mocks base method.
func (m *MockIClient) GetOperationLog(ctx context.Context, req *security.GetOperationLogReq) (*security.GetOperationLogRsp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOperationLog", ctx, req)
	ret0, _ := ret[0].(*security.GetOperationLogRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOperationLog indicates an expected call of GetOperationLog.
func (mr *MockIClientMockRecorder) GetOperationLog(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOperationLog", reflect.TypeOf((*MockIClient)(nil).GetOperationLog), ctx, req)
}

// GetSession mocks base method.
func (m *MockIClient) GetSession(ctx context.Context, uid uint32, req *security.GetSessionReq) (*security.GetSessionRsp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSession", ctx, uid, req)
	ret0, _ := ret[0].(*security.GetSessionRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSession indicates an expected call of GetSession.
func (mr *MockIClientMockRecorder) GetSession(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSession", reflect.TypeOf((*MockIClient)(nil).GetSession), ctx, uid, req)
}

// GetUnregApplyAuditStatus mocks base method.
func (m *MockIClient) GetUnregApplyAuditStatus(ctx context.Context, uid uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnregApplyAuditStatus", ctx, uid)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUnregApplyAuditStatus indicates an expected call of GetUnregApplyAuditStatus.
func (mr *MockIClientMockRecorder) GetUnregApplyAuditStatus(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnregApplyAuditStatus", reflect.TypeOf((*MockIClient)(nil).GetUnregApplyAuditStatus), ctx, uid)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UnbindPhone mocks base method.
func (m *MockIClient) UnbindPhone(ctx context.Context, uid uint32, req *security.UnbindPhoneReq) (*security.UnbindPhoneRsp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindPhone", ctx, uid, req)
	ret0, _ := ret[0].(*security.UnbindPhoneRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnbindPhone indicates an expected call of UnbindPhone.
func (mr *MockIClientMockRecorder) UnbindPhone(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindPhone", reflect.TypeOf((*MockIClient)(nil).UnbindPhone), ctx, uid, req)
}
