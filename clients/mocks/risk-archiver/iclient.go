// Code generated by MockGen. DO NOT EDIT.
// Source: /home/<USER>/git/quicksilver/clients/risk-archiver/iclient.go

// Package risk_archiver is a generated GoMock package.
package risk_archiver

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	risk_archiver "golang.52tt.com/protocol/services/risk-archiver"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetIDCardPageStatus mocks base method.
func (m *MockIClient) GetIDCardPageStatus(ctx context.Context, in *risk_archiver.GetIDCardPageStatusReq) (*risk_archiver.GetIDCardPageStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIDCardPageStatus", ctx, in)
	ret0, _ := ret[0].(*risk_archiver.GetIDCardPageStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetIDCardPageStatus indicates an expected call of GetIDCardPageStatus.
func (mr *MockIClientMockRecorder) GetIDCardPageStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIDCardPageStatus", reflect.TypeOf((*MockIClient)(nil).GetIDCardPageStatus), ctx, in)
}

// GetPhonePageStatus mocks base method.
func (m *MockIClient) GetPhonePageStatus(ctx context.Context, in *risk_archiver.GetPhonePageStatusReq) (*risk_archiver.GetPhonePageStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPhonePageStatus", ctx, in)
	ret0, _ := ret[0].(*risk_archiver.GetPhonePageStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPhonePageStatus indicates an expected call of GetPhonePageStatus.
func (mr *MockIClientMockRecorder) GetPhonePageStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPhonePageStatus", reflect.TypeOf((*MockIClient)(nil).GetPhonePageStatus), ctx, in)
}

// IsRiskIDCardInReason mocks base method.
func (m *MockIClient) IsRiskIDCardInReason(ctx context.Context, idCard, encryptIdCard string) (bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsRiskIDCardInReason", ctx, idCard, encryptIdCard)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// IsRiskIDCardInReason indicates an expected call of IsRiskIDCardInReason.
func (mr *MockIClientMockRecorder) IsRiskIDCardInReason(ctx, idCard, encryptIdCard interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsRiskIDCardInReason", reflect.TypeOf((*MockIClient)(nil).IsRiskIDCardInReason), ctx, idCard, encryptIdCard)
}

// IsRiskPhoneInReason mocks base method.
func (m *MockIClient) IsRiskPhoneInReason(ctx context.Context, phone string) (bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsRiskPhoneInReason", ctx, phone)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// IsRiskPhoneInReason indicates an expected call of IsRiskPhoneInReason.
func (mr *MockIClientMockRecorder) IsRiskPhoneInReason(ctx, phone interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsRiskPhoneInReason", reflect.TypeOf((*MockIClient)(nil).IsRiskPhoneInReason), ctx, phone)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdateRisk mocks base method.
func (m *MockIClient) UpdateRisk(ctx context.Context, in *risk_archiver.UpdateRiskReq) (*risk_archiver.UpdateRiskResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRisk", ctx, in)
	ret0, _ := ret[0].(*risk_archiver.UpdateRiskResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateRisk indicates an expected call of UpdateRisk.
func (mr *MockIClientMockRecorder) UpdateRisk(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisk", reflect.TypeOf((*MockIClient)(nil).UpdateRisk), ctx, in)
}
