// Code generated by MockGen. DO NOT EDIT.
// Source: iclient.go

// Package thirdpartyauthgw is a generated GoMock package.
package thirdpartyauthgw

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	thirdpartyauthgw "golang.52tt.com/protocol/services/thirdpartyauthgw"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetCMCCPhone mocks base method.
func (m *MockIClient) GetCMCCPhone(ctx context.Context, appId, token string) (*thirdpartyauthgw.GetCMCCPhoneResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCMCCPhone", ctx, appId, token)
	ret0, _ := ret[0].(*thirdpartyauthgw.GetCMCCPhoneResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCMCCPhone indicates an expected call of GetCMCCPhone.
func (mr *MockIClientMockRecorder) GetCMCCPhone(ctx, appId, token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCMCCPhone", reflect.TypeOf((*MockIClient)(nil).GetCMCCPhone), ctx, appId, token)
}

// GetCUCCPhone mocks base method.
func (m *MockIClient) GetCUCCPhone(ctx context.Context, apiKey, accessCode, md5 string) (*thirdpartyauthgw.GetCUCCPhoneResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCUCCPhone", ctx, apiKey, accessCode, md5)
	ret0, _ := ret[0].(*thirdpartyauthgw.GetCUCCPhoneResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCUCCPhone indicates an expected call of GetCUCCPhone.
func (mr *MockIClientMockRecorder) GetCUCCPhone(ctx, apiKey, accessCode, md5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCUCCPhone", reflect.TypeOf((*MockIClient)(nil).GetCUCCPhone), ctx, apiKey, accessCode, md5)
}

// GetQQUserInfo mocks base method.
func (m *MockIClient) GetQQUserInfo(ctx context.Context, appId, openId, accessToken string) (*thirdpartyauthgw.GetQQUserInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQQUserInfo", ctx, appId, openId, accessToken)
	ret0, _ := ret[0].(*thirdpartyauthgw.GetQQUserInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQQUserInfo indicates an expected call of GetQQUserInfo.
func (mr *MockIClientMockRecorder) GetQQUserInfo(ctx, appId, openId, accessToken interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQQUserInfo", reflect.TypeOf((*MockIClient)(nil).GetQQUserInfo), ctx, appId, openId, accessToken)
}

// GetWeChatUserInfo mocks base method.
func (m *MockIClient) GetWeChatUserInfo(ctx context.Context, openId, accessToken string) (*thirdpartyauthgw.GetWeChatUserInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeChatUserInfo", ctx, openId, accessToken)
	ret0, _ := ret[0].(*thirdpartyauthgw.GetWeChatUserInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeChatUserInfo indicates an expected call of GetWeChatUserInfo.
func (mr *MockIClientMockRecorder) GetWeChatUserInfo(ctx, openId, accessToken interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeChatUserInfo", reflect.TypeOf((*MockIClient)(nil).GetWeChatUserInfo), ctx, openId, accessToken)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// VerifyAppleUser mocks base method.
func (m *MockIClient) VerifyAppleUser(ctx context.Context, authCode, idToken, userId string) (*thirdpartyauthgw.VerifyAppleUserResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyAppleUser", ctx, authCode, idToken, userId)
	ret0, _ := ret[0].(*thirdpartyauthgw.VerifyAppleUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyAppleUser indicates an expected call of VerifyAppleUser.
func (mr *MockIClientMockRecorder) VerifyAppleUser(ctx, authCode, idToken, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyAppleUser", reflect.TypeOf((*MockIClient)(nil).VerifyAppleUser), ctx, authCode, idToken, userId)
}

// VerifyQQUserEx mocks base method.
func (m *MockIClient) VerifyQQUserEx(ctx context.Context, accessToken string) (*thirdpartyauthgw.VerifyQQUserExResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyQQUserEx", ctx, accessToken)
	ret0, _ := ret[0].(*thirdpartyauthgw.VerifyQQUserExResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyQQUserEx indicates an expected call of VerifyQQUserEx.
func (mr *MockIClientMockRecorder) VerifyQQUserEx(ctx, accessToken interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyQQUserEx", reflect.TypeOf((*MockIClient)(nil).VerifyQQUserEx), ctx, accessToken)
}
