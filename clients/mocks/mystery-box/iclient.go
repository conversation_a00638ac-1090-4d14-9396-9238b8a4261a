// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/mystery-box/iclient.go

// Package mysterybox is a generated GoMock package.
package mysterybox

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	mystery_box "golang.52tt.com/protocol/services/mystery-box"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetMysteryBoxTask mocks base method.
func (m *MockIClient) GetMysteryBoxTask(ctx context.Context, in *mystery_box.GetMysteryBoxTaskReq) (*mystery_box.GetMysteryBoxTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMysteryBoxTask", ctx, in)
	ret0, _ := ret[0].(*mystery_box.GetMysteryBoxTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMysteryBoxTask indicates an expected call of GetMysteryBoxTask.
func (mr *MockIClientMockRecorder) GetMysteryBoxTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMysteryBoxTask", reflect.TypeOf((*MockIClient)(nil).GetMysteryBoxTask), ctx, in)
}

// GetMysteryBoxWin mocks base method.
func (m *MockIClient) GetMysteryBoxWin(ctx context.Context, in *mystery_box.GetMysteryBoxWinReq) (*mystery_box.GetMysteryBoxWinResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMysteryBoxWin", ctx, in)
	ret0, _ := ret[0].(*mystery_box.GetMysteryBoxWinResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMysteryBoxWin indicates an expected call of GetMysteryBoxWin.
func (mr *MockIClientMockRecorder) GetMysteryBoxWin(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMysteryBoxWin", reflect.TypeOf((*MockIClient)(nil).GetMysteryBoxWin), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
