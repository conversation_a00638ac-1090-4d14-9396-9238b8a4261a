// Code generated by MockGen. DO NOT EDIT.
// Source: D:\go-tt\griffin\clients\headwear-go\iclient.go

// Package headwear_go is a generated GoMock package.
package headwear_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	headwear_go "golang.52tt.com/protocol/services/headwear-go"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// ChangeDecorationCustomText mocks base method.
func (m *MockIClient) ChangeDecorationCustomText(ctx context.Context, req *headwear_go.ChangeDecorationCustomTextReq) (*headwear_go.ChangeDecorationCustomTextResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeDecorationCustomText", ctx, req)
	ret0, _ := ret[0].(*headwear_go.ChangeDecorationCustomTextResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ChangeDecorationCustomText indicates an expected call of ChangeDecorationCustomText.
func (mr *MockIClientMockRecorder) ChangeDecorationCustomText(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeDecorationCustomText", reflect.TypeOf((*MockIClient)(nil).ChangeDecorationCustomText), ctx, req)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetHeadwearConfig mocks base method.
func (m *MockIClient) GetHeadwearConfig(ctx context.Context, uid, headwearId uint32) (*headwear_go.HeadwearConfig, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHeadwearConfig", ctx, uid, headwearId)
	ret0, _ := ret[0].(*headwear_go.HeadwearConfig)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetHeadwearConfig indicates an expected call of GetHeadwearConfig.
func (mr *MockIClientMockRecorder) GetHeadwearConfig(ctx, uid, headwearId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeadwearConfig", reflect.TypeOf((*MockIClient)(nil).GetHeadwearConfig), ctx, uid, headwearId)
}

// GetHeadwearConfigAll mocks base method.
func (m *MockIClient) GetHeadwearConfigAll(ctx context.Context) (*headwear_go.GetHeadwearConfigAllResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHeadwearConfigAll", ctx)
	ret0, _ := ret[0].(*headwear_go.GetHeadwearConfigAllResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetHeadwearConfigAll indicates an expected call of GetHeadwearConfigAll.
func (mr *MockIClientMockRecorder) GetHeadwearConfigAll(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeadwearConfigAll", reflect.TypeOf((*MockIClient)(nil).GetHeadwearConfigAll), ctx)
}

// GetUserHeadwear mocks base method.
func (m *MockIClient) GetUserHeadwear(ctx context.Context, uid uint32, req *headwear_go.GetUserHeadwearReq) (*headwear_go.GetUserHeadwearResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserHeadwear", ctx, uid, req)
	ret0, _ := ret[0].(*headwear_go.GetUserHeadwearResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserHeadwear indicates an expected call of GetUserHeadwear.
func (mr *MockIClientMockRecorder) GetUserHeadwear(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserHeadwear", reflect.TypeOf((*MockIClient)(nil).GetUserHeadwear), ctx, uid, req)
}

// GetUserHeadwearBySuite mocks base method.
func (m *MockIClient) GetUserHeadwearBySuite(ctx context.Context, uid uint32, req *headwear_go.GetUserHeadwearBySuiteReq) (*headwear_go.UserHeadwearInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserHeadwearBySuite", ctx, uid, req)
	ret0, _ := ret[0].(*headwear_go.UserHeadwearInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserHeadwearBySuite indicates an expected call of GetUserHeadwearBySuite.
func (mr *MockIClientMockRecorder) GetUserHeadwearBySuite(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserHeadwearBySuite", reflect.TypeOf((*MockIClient)(nil).GetUserHeadwearBySuite), ctx, uid, req)
}

// GetUserHeadwearInUse mocks base method.
func (m *MockIClient) GetUserHeadwearInUse(ctx context.Context, uid uint32) (*headwear_go.GetUserHeadwearInUseResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserHeadwearInUse", ctx, uid)
	ret0, _ := ret[0].(*headwear_go.GetUserHeadwearInUseResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserHeadwearInUse indicates an expected call of GetUserHeadwearInUse.
func (mr *MockIClientMockRecorder) GetUserHeadwearInUse(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserHeadwearInUse", reflect.TypeOf((*MockIClient)(nil).GetUserHeadwearInUse), ctx, uid)
}

// GetUserHeadwearInUseList mocks base method.
func (m *MockIClient) GetUserHeadwearInUseList(ctx context.Context, uid []uint32) (*headwear_go.GetUserHeadwearInUseListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserHeadwearInUseList", ctx, uid)
	ret0, _ := ret[0].(*headwear_go.GetUserHeadwearInUseListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserHeadwearInUseList indicates an expected call of GetUserHeadwearInUseList.
func (mr *MockIClientMockRecorder) GetUserHeadwearInUseList(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserHeadwearInUseList", reflect.TypeOf((*MockIClient)(nil).GetUserHeadwearInUseList), ctx, uid)
}

// GiveHeadwearToUser mocks base method.
func (m *MockIClient) GiveHeadwearToUser(ctx context.Context, uid uint32, req *headwear_go.GiveHeadwearToUserReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GiveHeadwearToUser", ctx, uid, req)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// GiveHeadwearToUser indicates an expected call of GiveHeadwearToUser.
func (mr *MockIClientMockRecorder) GiveHeadwearToUser(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GiveHeadwearToUser", reflect.TypeOf((*MockIClient)(nil).GiveHeadwearToUser), ctx, uid, req)
}

// SetUserHeadwearInUse mocks base method.
func (m *MockIClient) SetUserHeadwearInUse(ctx context.Context, uid, headwearId uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserHeadwearInUse", ctx, uid, headwearId)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// SetUserHeadwearInUse indicates an expected call of SetUserHeadwearInUse.
func (mr *MockIClientMockRecorder) SetUserHeadwearInUse(ctx, uid, headwearId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserHeadwearInUse", reflect.TypeOf((*MockIClient)(nil).SetUserHeadwearInUse), ctx, uid, headwearId)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
