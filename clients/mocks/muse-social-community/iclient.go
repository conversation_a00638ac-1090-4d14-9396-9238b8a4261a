// Code generated by MockGen. DO NOT EDIT.
// Source: iclient.go

// Package mock_muse_social_community is a generated GoMock package.
package muse_social_community

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	muse_social_community "golang.52tt.com/protocol/services/muse-social-community"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// ApplyCreateSocialCommunity mocks base method.
func (m *MockIClient) ApplyCreateSocialCommunity(ctx context.Context, req *muse_social_community.ApplyCreateSocialCommunityReq) (*muse_social_community.ApplyCreateSocialCommunityResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyCreateSocialCommunity", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.ApplyCreateSocialCommunityResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ApplyCreateSocialCommunity indicates an expected call of ApplyCreateSocialCommunity.
func (mr *MockIClientMockRecorder) ApplyCreateSocialCommunity(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyCreateSocialCommunity", reflect.TypeOf((*MockIClient)(nil).ApplyCreateSocialCommunity), ctx, req)
}

// BatchBrandBackgroundExtras mocks base method.
func (m *MockIClient) BatchBrandBackgroundExtras(ctx context.Context, req *muse_social_community.BatchBrandBackgroundExtrasReq) (*muse_social_community.BatchBrandBackgroundExtrasResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchBrandBackgroundExtras", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.BatchBrandBackgroundExtrasResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchBrandBackgroundExtras indicates an expected call of BatchBrandBackgroundExtras.
func (mr *MockIClientMockRecorder) BatchBrandBackgroundExtras(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchBrandBackgroundExtras", reflect.TypeOf((*MockIClient)(nil).BatchBrandBackgroundExtras), ctx, req)
}

// BatchBrandChannelByChannelIds mocks base method.
func (m *MockIClient) BatchBrandChannelByChannelIds(ctx context.Context, req *muse_social_community.BatchBrandChannelByChannelIdsReq) (*muse_social_community.BatchBrandChannelByChannelIdsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchBrandChannelByChannelIds", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.BatchBrandChannelByChannelIdsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchBrandChannelByChannelIds indicates an expected call of BatchBrandChannelByChannelIds.
func (mr *MockIClientMockRecorder) BatchBrandChannelByChannelIds(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchBrandChannelByChannelIds", reflect.TypeOf((*MockIClient)(nil).BatchBrandChannelByChannelIds), ctx, req)
}

// BatchBrandChannelsByBrandIds mocks base method.
func (m *MockIClient) BatchBrandChannelsByBrandIds(ctx context.Context, req *muse_social_community.BatchBrandChannelsByBrandIdsReq) (*muse_social_community.BatchBrandChannelsByBrandIdsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchBrandChannelsByBrandIds", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.BatchBrandChannelsByBrandIdsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchBrandChannelsByBrandIds indicates an expected call of BatchBrandChannelsByBrandIds.
func (mr *MockIClientMockRecorder) BatchBrandChannelsByBrandIds(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchBrandChannelsByBrandIds", reflect.TypeOf((*MockIClient)(nil).BatchBrandChannelsByBrandIds), ctx, req)
}

// BatchBrandMembersByUids mocks base method.
func (m *MockIClient) BatchBrandMembersByUids(ctx context.Context, req *muse_social_community.BatchBrandMembersByUidsReq) (*muse_social_community.BatchBrandMembersByUidsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchBrandMembersByUids", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.BatchBrandMembersByUidsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchBrandMembersByUids indicates an expected call of BatchBrandMembersByUids.
func (mr *MockIClientMockRecorder) BatchBrandMembersByUids(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchBrandMembersByUids", reflect.TypeOf((*MockIClient)(nil).BatchBrandMembersByUids), ctx, req)
}

// BatchBrandTypesByIds mocks base method.
func (m *MockIClient) BatchBrandTypesByIds(ctx context.Context, brandTypeIds []string) (*muse_social_community.BatchBrandTypesByIdsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchBrandTypesByIds", ctx, brandTypeIds)
	ret0, _ := ret[0].(*muse_social_community.BatchBrandTypesByIdsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchBrandTypesByIds indicates an expected call of BatchBrandTypesByIds.
func (mr *MockIClientMockRecorder) BatchBrandTypesByIds(ctx, brandTypeIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchBrandTypesByIds", reflect.TypeOf((*MockIClient)(nil).BatchBrandTypesByIds), ctx, brandTypeIds)
}

// BatchBrandsByIds mocks base method.
func (m *MockIClient) BatchBrandsByIds(ctx context.Context, req *muse_social_community.BatchBrandsByIdsReq) (*muse_social_community.BatchBrandsByIdsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchBrandsByIds", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.BatchBrandsByIdsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchBrandsByIds indicates an expected call of BatchBrandsByIds.
func (mr *MockIClientMockRecorder) BatchBrandsByIds(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchBrandsByIds", reflect.TypeOf((*MockIClient)(nil).BatchBrandsByIds), ctx, req)
}

// BatchCategoriesByBrandIds mocks base method.
func (m *MockIClient) BatchCategoriesByBrandIds(ctx context.Context, req *muse_social_community.BatchCategoriesByBrandIdsReq) (*muse_social_community.BatchCategoriesByBrandIdsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCategoriesByBrandIds", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.BatchCategoriesByBrandIdsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchCategoriesByBrandIds indicates an expected call of BatchCategoriesByBrandIds.
func (mr *MockIClientMockRecorder) BatchCategoriesByBrandIds(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCategoriesByBrandIds", reflect.TypeOf((*MockIClient)(nil).BatchCategoriesByBrandIds), ctx, req)
}

// BatchCategoriesByIds mocks base method.
func (m *MockIClient) BatchCategoriesByIds(ctx context.Context, categoryIds []string) (*muse_social_community.BatchCategoriesByIdsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCategoriesByIds", ctx, categoryIds)
	ret0, _ := ret[0].(*muse_social_community.BatchCategoriesByIdsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchCategoriesByIds indicates an expected call of BatchCategoriesByIds.
func (mr *MockIClientMockRecorder) BatchCategoriesByIds(ctx, categoryIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCategoriesByIds", reflect.TypeOf((*MockIClient)(nil).BatchCategoriesByIds), ctx, categoryIds)
}

// BatchCategoryTypesByIds mocks base method.
func (m *MockIClient) BatchCategoryTypesByIds(ctx context.Context, req *muse_social_community.BatchCategoryTypesByIdsReq) (*muse_social_community.BatchCategoryTypesByIdsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCategoryTypesByIds", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.BatchCategoryTypesByIdsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchCategoryTypesByIds indicates an expected call of BatchCategoryTypesByIds.
func (mr *MockIClientMockRecorder) BatchCategoryTypesByIds(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCategoryTypesByIds", reflect.TypeOf((*MockIClient)(nil).BatchCategoryTypesByIds), ctx, req)
}

// BatchGetCategoriesByCategoryTypeId mocks base method.
func (m *MockIClient) BatchGetCategoriesByCategoryTypeId(ctx context.Context, req *muse_social_community.BatchGetCategoriesByCategoryTypeIdReq) (*muse_social_community.BatchGetCategoriesByCategoryTypeIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetCategoriesByCategoryTypeId", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.BatchGetCategoriesByCategoryTypeIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetCategoriesByCategoryTypeId indicates an expected call of BatchGetCategoriesByCategoryTypeId.
func (mr *MockIClientMockRecorder) BatchGetCategoriesByCategoryTypeId(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCategoriesByCategoryTypeId", reflect.TypeOf((*MockIClient)(nil).BatchGetCategoriesByCategoryTypeId), ctx, req)
}

// BatchGetOpenCategoryTypes mocks base method.
func (m *MockIClient) BatchGetOpenCategoryTypes(ctx context.Context, req *muse_social_community.BatchGetOpenCategoryTypesReq) (*muse_social_community.BatchGetOpenCategoryTypesResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetOpenCategoryTypes", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.BatchGetOpenCategoryTypesResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetOpenCategoryTypes indicates an expected call of BatchGetOpenCategoryTypes.
func (mr *MockIClientMockRecorder) BatchGetOpenCategoryTypes(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetOpenCategoryTypes", reflect.TypeOf((*MockIClient)(nil).BatchGetOpenCategoryTypes), ctx, req)
}

// BatchMuseSocialAnnounceByAnnounceIds mocks base method.
func (m *MockIClient) BatchMuseSocialAnnounceByAnnounceIds(ctx context.Context, announceId []string) (*muse_social_community.BatchMuseSocialAnnounceByAnnounceIdsResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchMuseSocialAnnounceByAnnounceIds", ctx, announceId)
	ret0, _ := ret[0].(*muse_social_community.BatchMuseSocialAnnounceByAnnounceIdsResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchMuseSocialAnnounceByAnnounceIds indicates an expected call of BatchMuseSocialAnnounceByAnnounceIds.
func (mr *MockIClientMockRecorder) BatchMuseSocialAnnounceByAnnounceIds(ctx, announceId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchMuseSocialAnnounceByAnnounceIds", reflect.TypeOf((*MockIClient)(nil).BatchMuseSocialAnnounceByAnnounceIds), ctx, announceId)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetGroupMemberLimit mocks base method.
func (m *MockIClient) GetGroupMemberLimit(ctx context.Context, in *muse_social_community.GetGroupMemberLimitReq) (*muse_social_community.GetGroupMemberLimitResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupMemberLimit", ctx, in)
	ret0, _ := ret[0].(*muse_social_community.GetGroupMemberLimitResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGroupMemberLimit indicates an expected call of GetGroupMemberLimit.
func (mr *MockIClientMockRecorder) GetGroupMemberLimit(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupMemberLimit", reflect.TypeOf((*MockIClient)(nil).GetGroupMemberLimit), ctx, in)
}

// GetSocialCommunityAllMembersCount mocks base method.
func (m *MockIClient) GetSocialCommunityAllMembersCount(ctx context.Context, req *muse_social_community.GetSocialCommunityAllMembersCountReq) (*muse_social_community.GetSocialCommunityAllMembersCountResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocialCommunityAllMembersCount", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.GetSocialCommunityAllMembersCountResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSocialCommunityAllMembersCount indicates an expected call of GetSocialCommunityAllMembersCount.
func (mr *MockIClientMockRecorder) GetSocialCommunityAllMembersCount(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocialCommunityAllMembersCount", reflect.TypeOf((*MockIClient)(nil).GetSocialCommunityAllMembersCount), ctx, req)
}

// GetSocialCommunityAnnounceNewsCountMap mocks base method.
func (m *MockIClient) GetSocialCommunityAnnounceNewsCountMap(ctx context.Context, request *muse_social_community.GetSocialCommunityAnnounceNewsCountMapRequest) (*muse_social_community.GetSocialCommunityAnnounceNewsCountMapResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocialCommunityAnnounceNewsCountMap", ctx, request)
	ret0, _ := ret[0].(*muse_social_community.GetSocialCommunityAnnounceNewsCountMapResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSocialCommunityAnnounceNewsCountMap indicates an expected call of GetSocialCommunityAnnounceNewsCountMap.
func (mr *MockIClientMockRecorder) GetSocialCommunityAnnounceNewsCountMap(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocialCommunityAnnounceNewsCountMap", reflect.TypeOf((*MockIClient)(nil).GetSocialCommunityAnnounceNewsCountMap), ctx, request)
}

// GetSocialCommunityBase mocks base method.
func (m *MockIClient) GetSocialCommunityBase(ctx context.Context, in *muse_social_community.GetSocialCommunityBaseReq) (*muse_social_community.GetSocialCommunityBaseResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocialCommunityBase", ctx, in)
	ret0, _ := ret[0].(*muse_social_community.GetSocialCommunityBaseResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSocialCommunityBase indicates an expected call of GetSocialCommunityBase.
func (mr *MockIClientMockRecorder) GetSocialCommunityBase(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocialCommunityBase", reflect.TypeOf((*MockIClient)(nil).GetSocialCommunityBase), ctx, in)
}

// GetSocialCommunityCaption mocks base method.
func (m *MockIClient) GetSocialCommunityCaption(ctx context.Context, req *muse_social_community.GetSocialCommunityCaptionReq) (*muse_social_community.GetSocialCommunityCaptionResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocialCommunityCaption", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.GetSocialCommunityCaptionResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSocialCommunityCaption indicates an expected call of GetSocialCommunityCaption.
func (mr *MockIClientMockRecorder) GetSocialCommunityCaption(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocialCommunityCaption", reflect.TypeOf((*MockIClient)(nil).GetSocialCommunityCaption), ctx, req)
}

// GetSocialCommunityContentStreamNewsCount mocks base method.
func (m *MockIClient) GetSocialCommunityContentStreamNewsCount(ctx context.Context, req *muse_social_community.GetSocialCommunityContentStreamNewsCountRequest) (*muse_social_community.GetSocialCommunityContentStreamNewsCountResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocialCommunityContentStreamNewsCount", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.GetSocialCommunityContentStreamNewsCountResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSocialCommunityContentStreamNewsCount indicates an expected call of GetSocialCommunityContentStreamNewsCount.
func (mr *MockIClientMockRecorder) GetSocialCommunityContentStreamNewsCount(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocialCommunityContentStreamNewsCount", reflect.TypeOf((*MockIClient)(nil).GetSocialCommunityContentStreamNewsCount), ctx, req)
}

// GetSocialCommunityDetailInfo mocks base method.
func (m *MockIClient) GetSocialCommunityDetailInfo(ctx context.Context, req *muse_social_community.GetSocialCommunityDetailInfoReq) (*muse_social_community.GetSocialCommunityDetailInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocialCommunityDetailInfo", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.GetSocialCommunityDetailInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSocialCommunityDetailInfo indicates an expected call of GetSocialCommunityDetailInfo.
func (mr *MockIClientMockRecorder) GetSocialCommunityDetailInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocialCommunityDetailInfo", reflect.TypeOf((*MockIClient)(nil).GetSocialCommunityDetailInfo), ctx, req)
}

// GetSocialCommunityFansCount mocks base method.
func (m *MockIClient) GetSocialCommunityFansCount(ctx context.Context, req *muse_social_community.GetSocialCommunityFansCountReq) (*muse_social_community.GetSocialCommunityFansCountResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocialCommunityFansCount", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.GetSocialCommunityFansCountResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSocialCommunityFansCount indicates an expected call of GetSocialCommunityFansCount.
func (mr *MockIClientMockRecorder) GetSocialCommunityFansCount(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocialCommunityFansCount", reflect.TypeOf((*MockIClient)(nil).GetSocialCommunityFansCount), ctx, req)
}

// GetSocialCommunityFloat mocks base method.
func (m *MockIClient) GetSocialCommunityFloat(ctx context.Context, in *muse_social_community.GetSocialCommunityFloatRequest) (*muse_social_community.GetSocialCommunityFloatResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocialCommunityFloat", ctx, in)
	ret0, _ := ret[0].(*muse_social_community.GetSocialCommunityFloatResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSocialCommunityFloat indicates an expected call of GetSocialCommunityFloat.
func (mr *MockIClientMockRecorder) GetSocialCommunityFloat(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocialCommunityFloat", reflect.TypeOf((*MockIClient)(nil).GetSocialCommunityFloat), ctx, in)
}

// GetSocialCommunityKernelCount mocks base method.
func (m *MockIClient) GetSocialCommunityKernelCount(ctx context.Context, req *muse_social_community.GetSocialCommunityKernelCountReq) (*muse_social_community.GetSocialCommunityKernelCountResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocialCommunityKernelCount", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.GetSocialCommunityKernelCountResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSocialCommunityKernelCount indicates an expected call of GetSocialCommunityKernelCount.
func (mr *MockIClientMockRecorder) GetSocialCommunityKernelCount(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocialCommunityKernelCount", reflect.TypeOf((*MockIClient)(nil).GetSocialCommunityKernelCount), ctx, req)
}

// GetSocialCommunityKernelMembers mocks base method.
func (m *MockIClient) GetSocialCommunityKernelMembers(ctx context.Context, socialCommunityIds []string) (*muse_social_community.GetSocialCommunityKernelMembersResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocialCommunityKernelMembers", ctx, socialCommunityIds)
	ret0, _ := ret[0].(*muse_social_community.GetSocialCommunityKernelMembersResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSocialCommunityKernelMembers indicates an expected call of GetSocialCommunityKernelMembers.
func (mr *MockIClientMockRecorder) GetSocialCommunityKernelMembers(ctx, socialCommunityIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocialCommunityKernelMembers", reflect.TypeOf((*MockIClient)(nil).GetSocialCommunityKernelMembers), ctx, socialCommunityIds)
}

// GetSocialCommunityMemberList mocks base method.
func (m *MockIClient) GetSocialCommunityMemberList(ctx context.Context, SocialCommunityId, offsetId string, count uint32) (*muse_social_community.GetSocialCommunityMemberListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocialCommunityMemberList", ctx, SocialCommunityId, offsetId, count)
	ret0, _ := ret[0].(*muse_social_community.GetSocialCommunityMemberListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSocialCommunityMemberList indicates an expected call of GetSocialCommunityMemberList.
func (mr *MockIClientMockRecorder) GetSocialCommunityMemberList(ctx, SocialCommunityId, offsetId, count interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocialCommunityMemberList", reflect.TypeOf((*MockIClient)(nil).GetSocialCommunityMemberList), ctx, SocialCommunityId, offsetId, count)
}

// GetSocialCommunityProfilePages mocks base method.
func (m *MockIClient) GetSocialCommunityProfilePages(ctx context.Context, req *muse_social_community.GetSocialCommunityProfilePagesReq) (*muse_social_community.GetSocialCommunityProfilePagesResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocialCommunityProfilePages", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.GetSocialCommunityProfilePagesResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSocialCommunityProfilePages indicates an expected call of GetSocialCommunityProfilePages.
func (mr *MockIClientMockRecorder) GetSocialCommunityProfilePages(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocialCommunityProfilePages", reflect.TypeOf((*MockIClient)(nil).GetSocialCommunityProfilePages), ctx, req)
}

// GetUserRecentSocialCommunityOrder mocks base method.
func (m *MockIClient) GetUserRecentSocialCommunityOrder(ctx context.Context, req *muse_social_community.GetUserRecentSocialCommunityOrderReq) (*muse_social_community.GetUserRecentSocialCommunityOrderResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRecentSocialCommunityOrder", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.GetUserRecentSocialCommunityOrderResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserRecentSocialCommunityOrder indicates an expected call of GetUserRecentSocialCommunityOrder.
func (mr *MockIClientMockRecorder) GetUserRecentSocialCommunityOrder(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRecentSocialCommunityOrder", reflect.TypeOf((*MockIClient)(nil).GetUserRecentSocialCommunityOrder), ctx, req)
}

// JoinSocialCommunityFans mocks base method.
func (m *MockIClient) JoinSocialCommunityFans(ctx context.Context, req *muse_social_community.JoinSocialCommunityFansReq) (*muse_social_community.JoinSocialCommunityFansResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JoinSocialCommunityFans", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.JoinSocialCommunityFansResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// JoinSocialCommunityFans indicates an expected call of JoinSocialCommunityFans.
func (mr *MockIClientMockRecorder) JoinSocialCommunityFans(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JoinSocialCommunityFans", reflect.TypeOf((*MockIClient)(nil).JoinSocialCommunityFans), ctx, req)
}

// JoinSocialCommunityKernel mocks base method.
func (m *MockIClient) JoinSocialCommunityKernel(ctx context.Context, req *muse_social_community.JoinSocialCommunityKernelReq) (*muse_social_community.JoinSocialCommunityKernelResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JoinSocialCommunityKernel", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.JoinSocialCommunityKernelResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// JoinSocialCommunityKernel indicates an expected call of JoinSocialCommunityKernel.
func (mr *MockIClientMockRecorder) JoinSocialCommunityKernel(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JoinSocialCommunityKernel", reflect.TypeOf((*MockIClient)(nil).JoinSocialCommunityKernel), ctx, req)
}

// ListAnnounceDestinations mocks base method.
func (m *MockIClient) ListAnnounceDestinations(ctx context.Context, request *muse_social_community.ListAnnounceDestinationsRequest) (*muse_social_community.ListAnnounceDestinationsResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAnnounceDestinations", ctx, request)
	ret0, _ := ret[0].(*muse_social_community.ListAnnounceDestinationsResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListAnnounceDestinations indicates an expected call of ListAnnounceDestinations.
func (mr *MockIClientMockRecorder) ListAnnounceDestinations(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAnnounceDestinations", reflect.TypeOf((*MockIClient)(nil).ListAnnounceDestinations), ctx, request)
}

// ListApplyJoinCommunityWorkOrders mocks base method.
func (m *MockIClient) ListApplyJoinCommunityWorkOrders(ctx context.Context, req *muse_social_community.ListApplyJoinCommunityWorkOrdersRequest) (*muse_social_community.ListApplyJoinCommunityWorkOrdersResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListApplyJoinCommunityWorkOrders", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.ListApplyJoinCommunityWorkOrdersResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListApplyJoinCommunityWorkOrders indicates an expected call of ListApplyJoinCommunityWorkOrders.
func (mr *MockIClientMockRecorder) ListApplyJoinCommunityWorkOrders(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListApplyJoinCommunityWorkOrders", reflect.TypeOf((*MockIClient)(nil).ListApplyJoinCommunityWorkOrders), ctx, req)
}

// ListBrandMembersByUid mocks base method.
func (m *MockIClient) ListBrandMembersByUid(ctx context.Context, req *muse_social_community.ListBrandMembersByUidReq) (*muse_social_community.ListBrandMembersByUidResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBrandMembersByUid", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.ListBrandMembersByUidResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListBrandMembersByUid indicates an expected call of ListBrandMembersByUid.
func (mr *MockIClientMockRecorder) ListBrandMembersByUid(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBrandMembersByUid", reflect.TypeOf((*MockIClient)(nil).ListBrandMembersByUid), ctx, req)
}

// ListMuseSocialAnnounceInterestUsers mocks base method.
func (m *MockIClient) ListMuseSocialAnnounceInterestUsers(ctx context.Context, announceId, offsetId string, limit uint32) (*muse_social_community.ListMuseSocialAnnounceInterestUsersResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMuseSocialAnnounceInterestUsers", ctx, announceId, offsetId, limit)
	ret0, _ := ret[0].(*muse_social_community.ListMuseSocialAnnounceInterestUsersResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListMuseSocialAnnounceInterestUsers indicates an expected call of ListMuseSocialAnnounceInterestUsers.
func (mr *MockIClientMockRecorder) ListMuseSocialAnnounceInterestUsers(ctx, announceId, offsetId, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMuseSocialAnnounceInterestUsers", reflect.TypeOf((*MockIClient)(nil).ListMuseSocialAnnounceInterestUsers), ctx, announceId, offsetId, limit)
}

// ListMuseSocialAnnounces mocks base method.
func (m *MockIClient) ListMuseSocialAnnounces(ctx context.Context, request *muse_social_community.ListMuseSocialAnnouncesRequest) (*muse_social_community.ListMuseSocialAnnouncesResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMuseSocialAnnounces", ctx, request)
	ret0, _ := ret[0].(*muse_social_community.ListMuseSocialAnnouncesResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListMuseSocialAnnounces indicates an expected call of ListMuseSocialAnnounces.
func (mr *MockIClientMockRecorder) ListMuseSocialAnnounces(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMuseSocialAnnounces", reflect.TypeOf((*MockIClient)(nil).ListMuseSocialAnnounces), ctx, request)
}

// ListMuseSocialCommunityNavBars mocks base method.
func (m *MockIClient) ListMuseSocialCommunityNavBars(ctx context.Context, req *muse_social_community.ListMuseSocialCommunityNavBarsReq) (*muse_social_community.ListMuseSocialCommunityNavBarsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMuseSocialCommunityNavBars", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.ListMuseSocialCommunityNavBarsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListMuseSocialCommunityNavBars indicates an expected call of ListMuseSocialCommunityNavBars.
func (mr *MockIClientMockRecorder) ListMuseSocialCommunityNavBars(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMuseSocialCommunityNavBars", reflect.TypeOf((*MockIClient)(nil).ListMuseSocialCommunityNavBars), ctx, req)
}

// ListMuseSocialCommunityNavSecondaryBars mocks base method.
func (m *MockIClient) ListMuseSocialCommunityNavSecondaryBars(ctx context.Context, req *muse_social_community.ListMuseSocialCommunityNavSecondaryBarsReq) (*muse_social_community.ListMuseSocialCommunityNavSecondaryBarsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMuseSocialCommunityNavSecondaryBars", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.ListMuseSocialCommunityNavSecondaryBarsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListMuseSocialCommunityNavSecondaryBars indicates an expected call of ListMuseSocialCommunityNavSecondaryBars.
func (mr *MockIClientMockRecorder) ListMuseSocialCommunityNavSecondaryBars(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMuseSocialCommunityNavSecondaryBars", reflect.TypeOf((*MockIClient)(nil).ListMuseSocialCommunityNavSecondaryBars), ctx, req)
}

// ListUserFansMembers mocks base method.
func (m *MockIClient) ListUserFansMembers(ctx context.Context, req *muse_social_community.ListUserFansMembersReq) (*muse_social_community.ListUserFansMembersResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListUserFansMembers", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.ListUserFansMembersResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListUserFansMembers indicates an expected call of ListUserFansMembers.
func (mr *MockIClientMockRecorder) ListUserFansMembers(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUserFansMembers", reflect.TypeOf((*MockIClient)(nil).ListUserFansMembers), ctx, req)
}

// ListUserKernelMembers mocks base method.
func (m *MockIClient) ListUserKernelMembers(ctx context.Context, req *muse_social_community.ListUserKernelMembersReq) (*muse_social_community.ListUserKernelMembersResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListUserKernelMembers", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.ListUserKernelMembersResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListUserKernelMembers indicates an expected call of ListUserKernelMembers.
func (mr *MockIClientMockRecorder) ListUserKernelMembers(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUserKernelMembers", reflect.TypeOf((*MockIClient)(nil).ListUserKernelMembers), ctx, req)
}

// MuseSocialPreviewGroupMessage mocks base method.
func (m *MockIClient) MuseSocialPreviewGroupMessage(ctx context.Context, groupId, uid uint32) (*muse_social_community.MuseSocialPreviewGroupMessageResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MuseSocialPreviewGroupMessage", ctx, groupId, uid)
	ret0, _ := ret[0].(*muse_social_community.MuseSocialPreviewGroupMessageResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// MuseSocialPreviewGroupMessage indicates an expected call of MuseSocialPreviewGroupMessage.
func (mr *MockIClientMockRecorder) MuseSocialPreviewGroupMessage(ctx, groupId, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MuseSocialPreviewGroupMessage", reflect.TypeOf((*MockIClient)(nil).MuseSocialPreviewGroupMessage), ctx, groupId, uid)
}

// RemoveMuseSocialAnnounce mocks base method.
func (m *MockIClient) RemoveMuseSocialAnnounce(ctx context.Context, announceId string) (*muse_social_community.RemoveMuseSocialAnnounceResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveMuseSocialAnnounce", ctx, announceId)
	ret0, _ := ret[0].(*muse_social_community.RemoveMuseSocialAnnounceResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// RemoveMuseSocialAnnounce indicates an expected call of RemoveMuseSocialAnnounce.
func (mr *MockIClientMockRecorder) RemoveMuseSocialAnnounce(ctx, announceId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveMuseSocialAnnounce", reflect.TypeOf((*MockIClient)(nil).RemoveMuseSocialAnnounce), ctx, announceId)
}

// SearchApplyJoinCommunityWorkOrder mocks base method.
func (m *MockIClient) SearchApplyJoinCommunityWorkOrder(ctx context.Context, uid, limit uint32, status []uint32, socialCommunityId, id, offsetId string) (*muse_social_community.SearchApplyJoinCommunityWorkOrderResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchApplyJoinCommunityWorkOrder", ctx, uid, limit, status, socialCommunityId, id, offsetId)
	ret0, _ := ret[0].(*muse_social_community.SearchApplyJoinCommunityWorkOrderResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SearchApplyJoinCommunityWorkOrder indicates an expected call of SearchApplyJoinCommunityWorkOrder.
func (mr *MockIClientMockRecorder) SearchApplyJoinCommunityWorkOrder(ctx, uid, limit, status, socialCommunityId, id, offsetId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchApplyJoinCommunityWorkOrder", reflect.TypeOf((*MockIClient)(nil).SearchApplyJoinCommunityWorkOrder), ctx, uid, limit, status, socialCommunityId, id, offsetId)
}

// SearchBrandMembers mocks base method.
func (m *MockIClient) SearchBrandMembers(ctx context.Context, req *muse_social_community.SearchBrandMembersReq) (*muse_social_community.SearchBrandMembersResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchBrandMembers", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.SearchBrandMembersResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SearchBrandMembers indicates an expected call of SearchBrandMembers.
func (mr *MockIClientMockRecorder) SearchBrandMembers(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchBrandMembers", reflect.TypeOf((*MockIClient)(nil).SearchBrandMembers), ctx, req)
}

// SearchBrandTypes mocks base method.
func (m *MockIClient) SearchBrandTypes(ctx context.Context, req *muse_social_community.SearchBrandTypesReq) (*muse_social_community.SearchBrandTypesResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchBrandTypes", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.SearchBrandTypesResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SearchBrandTypes indicates an expected call of SearchBrandTypes.
func (mr *MockIClientMockRecorder) SearchBrandTypes(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchBrandTypes", reflect.TypeOf((*MockIClient)(nil).SearchBrandTypes), ctx, req)
}

// SearchBrands mocks base method.
func (m *MockIClient) SearchBrands(ctx context.Context, req *muse_social_community.SearchBrandsReq) (*muse_social_community.SearchBrandsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchBrands", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.SearchBrandsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SearchBrands indicates an expected call of SearchBrands.
func (mr *MockIClientMockRecorder) SearchBrands(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchBrands", reflect.TypeOf((*MockIClient)(nil).SearchBrands), ctx, req)
}

// SearchMemberWorkerOrders mocks base method.
func (m *MockIClient) SearchMemberWorkerOrders(ctx context.Context, req *muse_social_community.SearchMemberWorkerOrdersReq) (*muse_social_community.SearchMemberWorkerOrdersResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchMemberWorkerOrders", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.SearchMemberWorkerOrdersResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SearchMemberWorkerOrders indicates an expected call of SearchMemberWorkerOrders.
func (mr *MockIClientMockRecorder) SearchMemberWorkerOrders(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchMemberWorkerOrders", reflect.TypeOf((*MockIClient)(nil).SearchMemberWorkerOrders), ctx, req)
}

// SetMuseSocialAnnounceInterest mocks base method.
func (m *MockIClient) SetMuseSocialAnnounceInterest(ctx context.Context, announceId string, uid, interestType uint32) (*muse_social_community.SetMuseSocialAnnounceInterestResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMuseSocialAnnounceInterest", ctx, announceId, uid, interestType)
	ret0, _ := ret[0].(*muse_social_community.SetMuseSocialAnnounceInterestResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetMuseSocialAnnounceInterest indicates an expected call of SetMuseSocialAnnounceInterest.
func (mr *MockIClientMockRecorder) SetMuseSocialAnnounceInterest(ctx, announceId, uid, interestType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMuseSocialAnnounceInterest", reflect.TypeOf((*MockIClient)(nil).SetMuseSocialAnnounceInterest), ctx, announceId, uid, interestType)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// SubmitApplicationToJoinCommunity mocks base method.
func (m *MockIClient) SubmitApplicationToJoinCommunity(ctx context.Context, req *muse_social_community.SubmitApplicationToJoinCommunityRequest) (*muse_social_community.SubmitApplicationToJoinCommunityResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitApplicationToJoinCommunity", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.SubmitApplicationToJoinCommunityResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SubmitApplicationToJoinCommunity indicates an expected call of SubmitApplicationToJoinCommunity.
func (mr *MockIClientMockRecorder) SubmitApplicationToJoinCommunity(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitApplicationToJoinCommunity", reflect.TypeOf((*MockIClient)(nil).SubmitApplicationToJoinCommunity), ctx, req)
}

// UpsertApplyJoinCommunityWorkOrder mocks base method.
func (m *MockIClient) UpsertApplyJoinCommunityWorkOrder(ctx context.Context, uid, status uint32, socialCommunityId, id, reason string) (*muse_social_community.UpsertApplyJoinCommunityWorkOrderResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertApplyJoinCommunityWorkOrder", ctx, uid, status, socialCommunityId, id, reason)
	ret0, _ := ret[0].(*muse_social_community.UpsertApplyJoinCommunityWorkOrderResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpsertApplyJoinCommunityWorkOrder indicates an expected call of UpsertApplyJoinCommunityWorkOrder.
func (mr *MockIClientMockRecorder) UpsertApplyJoinCommunityWorkOrder(ctx, uid, status, socialCommunityId, id, reason interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertApplyJoinCommunityWorkOrder", reflect.TypeOf((*MockIClient)(nil).UpsertApplyJoinCommunityWorkOrder), ctx, uid, status, socialCommunityId, id, reason)
}

// UpsertBrandMembers mocks base method.
func (m *MockIClient) UpsertBrandMembers(ctx context.Context, req *muse_social_community.UpsertBrandMembersReq) (*muse_social_community.UpsertBrandMembersResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertBrandMembers", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.UpsertBrandMembersResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpsertBrandMembers indicates an expected call of UpsertBrandMembers.
func (mr *MockIClientMockRecorder) UpsertBrandMembers(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertBrandMembers", reflect.TypeOf((*MockIClient)(nil).UpsertBrandMembers), ctx, req)
}

// UpsertBrands mocks base method.
func (m *MockIClient) UpsertBrands(ctx context.Context, brands []*muse_social_community.Brand) (*muse_social_community.UpsertBrandsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertBrands", ctx, brands)
	ret0, _ := ret[0].(*muse_social_community.UpsertBrandsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpsertBrands indicates an expected call of UpsertBrands.
func (mr *MockIClientMockRecorder) UpsertBrands(ctx, brands interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertBrands", reflect.TypeOf((*MockIClient)(nil).UpsertBrands), ctx, brands)
}

// UpsertMemberWorkerOrders mocks base method.
func (m *MockIClient) UpsertMemberWorkerOrders(ctx context.Context, req *muse_social_community.UpsertMemberWorkerOrdersReq) (*muse_social_community.UpsertMemberWorkerOrdersResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertMemberWorkerOrders", ctx, req)
	ret0, _ := ret[0].(*muse_social_community.UpsertMemberWorkerOrdersResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpsertMemberWorkerOrders indicates an expected call of UpsertMemberWorkerOrders.
func (mr *MockIClientMockRecorder) UpsertMemberWorkerOrders(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertMemberWorkerOrders", reflect.TypeOf((*MockIClient)(nil).UpsertMemberWorkerOrders), ctx, req)
}

// UpsertMuseSocialAnnounce mocks base method.
func (m *MockIClient) UpsertMuseSocialAnnounce(ctx context.Context, request *muse_social_community.UpsertMuseSocialAnnounceRequest) (*muse_social_community.UpsertMuseSocialAnnounceResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertMuseSocialAnnounce", ctx, request)
	ret0, _ := ret[0].(*muse_social_community.UpsertMuseSocialAnnounceResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpsertMuseSocialAnnounce indicates an expected call of UpsertMuseSocialAnnounce.
func (mr *MockIClientMockRecorder) UpsertMuseSocialAnnounce(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertMuseSocialAnnounce", reflect.TypeOf((*MockIClient)(nil).UpsertMuseSocialAnnounce), ctx, request)
}

// ValidateUserHasCreateAnnouncePermissions mocks base method.
func (m *MockIClient) ValidateUserHasCreateAnnouncePermissions(ctx context.Context, socialCommunityId string) (*muse_social_community.ValidateUserHasCreateAnnouncePermissionsResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateUserHasCreateAnnouncePermissions", ctx, socialCommunityId)
	ret0, _ := ret[0].(*muse_social_community.ValidateUserHasCreateAnnouncePermissionsResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ValidateUserHasCreateAnnouncePermissions indicates an expected call of ValidateUserHasCreateAnnouncePermissions.
func (mr *MockIClientMockRecorder) ValidateUserHasCreateAnnouncePermissions(ctx, socialCommunityId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateUserHasCreateAnnouncePermissions", reflect.TypeOf((*MockIClient)(nil).ValidateUserHasCreateAnnouncePermissions), ctx, socialCommunityId)
}
