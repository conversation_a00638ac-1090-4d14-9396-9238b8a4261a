// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/channelcommonvotepk/iclient.go

// Package channelcommonvotepk is a generated GoMock package.
package channelcommonvotepk

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channelcommonvotepk "golang.52tt.com/protocol/services/channelvotepkcommonsvr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// ChannelCommonPkVote mocks base method.
func (m *MockIClient) ChannelCommonPkVote(ctx context.Context, from, to, channelID, startTime uint32) (*channelcommonvotepk.ChannelCommonPkVoteResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelCommonPkVote", ctx, from, to, channelID, startTime)
	ret0, _ := ret[0].(*channelcommonvotepk.ChannelCommonPkVoteResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ChannelCommonPkVote indicates an expected call of ChannelCommonPkVote.
func (mr *MockIClientMockRecorder) ChannelCommonPkVote(ctx, from, to, channelID, startTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelCommonPkVote", reflect.TypeOf((*MockIClient)(nil).ChannelCommonPkVote), ctx, from, to, channelID, startTime)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetUserLeftVoteCnt mocks base method.
func (m *MockIClient) GetUserLeftVoteCnt(ctx context.Context, uin, channelID, startTime uint32) (*channelcommonvotepk.GetUserLeftVoteCntResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLeftVoteCnt", ctx, uin, channelID, startTime)
	ret0, _ := ret[0].(*channelcommonvotepk.GetUserLeftVoteCntResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserLeftVoteCnt indicates an expected call of GetUserLeftVoteCnt.
func (mr *MockIClientMockRecorder) GetUserLeftVoteCnt(ctx, uin, channelID, startTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLeftVoteCnt", reflect.TypeOf((*MockIClient)(nil).GetUserLeftVoteCnt), ctx, uin, channelID, startTime)
}

// SetChannelCommonPkInfo mocks base method.
func (m *MockIClient) SetChannelCommonPkInfo(ctx context.Context, channelId, startTime, dur, limit uint32) (*channelcommonvotepk.SetChannelCommonPkInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelCommonPkInfo", ctx, channelId, startTime, dur, limit)
	ret0, _ := ret[0].(*channelcommonvotepk.SetChannelCommonPkInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelCommonPkInfo indicates an expected call of SetChannelCommonPkInfo.
func (mr *MockIClientMockRecorder) SetChannelCommonPkInfo(ctx, channelId, startTime, dur, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelCommonPkInfo", reflect.TypeOf((*MockIClient)(nil).SetChannelCommonPkInfo), ctx, channelId, startTime, dur, limit)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
