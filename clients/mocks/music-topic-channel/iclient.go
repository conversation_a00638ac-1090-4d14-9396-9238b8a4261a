// Code generated by MockGen. DO NOT EDIT.
// Source: E:\TT\gitmaven\quicksilver\clients\music-topic-channel\iclient.go

// Package music_topic_channel is a generated GoMock package.
package music_topic_channel

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	music_topic_channel "golang.52tt.com/protocol/services/music-topic-channel"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddTemporaryChannel mocks base method.
func (m *MockIClient) AddTemporaryChannel(ctx context.Context, in *music_topic_channel.AddTemporaryChannelReq) (*music_topic_channel.AddTemporaryChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTemporaryChannel", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.AddTemporaryChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddTemporaryChannel indicates an expected call of AddTemporaryChannel.
func (mr *MockIClientMockRecorder) AddTemporaryChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTemporaryChannel", reflect.TypeOf((*MockIClient)(nil).AddTemporaryChannel), ctx, in)
}

// BatchFilterIdsByGameCard mocks base method.
func (m *MockIClient) BatchFilterIdsByGameCard(ctx context.Context, in *music_topic_channel.BatchFilterIdsByGameCardReq) (*music_topic_channel.BatchFilterIdsByGameCardResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchFilterIdsByGameCard", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.BatchFilterIdsByGameCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchFilterIdsByGameCard indicates an expected call of BatchFilterIdsByGameCard.
func (mr *MockIClientMockRecorder) BatchFilterIdsByGameCard(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchFilterIdsByGameCard", reflect.TypeOf((*MockIClient)(nil).BatchFilterIdsByGameCard), ctx, in)
}

// BatchIsHighQualityChannels mocks base method.
func (m *MockIClient) BatchIsHighQualityChannels(ctx context.Context, in *music_topic_channel.BatchIsHighQualityChannelsReq) (*music_topic_channel.BatchIsHighQualityChannelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchIsHighQualityChannels", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.BatchIsHighQualityChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchIsHighQualityChannels indicates an expected call of BatchIsHighQualityChannels.
func (mr *MockIClientMockRecorder) BatchIsHighQualityChannels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchIsHighQualityChannels", reflect.TypeOf((*MockIClient)(nil).BatchIsHighQualityChannels), ctx, in)
}

// BatchIsPublishing mocks base method.
func (m *MockIClient) BatchIsPublishing(ctx context.Context, in *music_topic_channel.BatchIsPublishingReq) (*music_topic_channel.BatchIsPublishingResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchIsPublishing", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.BatchIsPublishingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchIsPublishing indicates an expected call of BatchIsPublishing.
func (mr *MockIClientMockRecorder) BatchIsPublishing(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchIsPublishing", reflect.TypeOf((*MockIClient)(nil).BatchIsPublishing), ctx, in)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DismissMusicChannel mocks base method.
func (m *MockIClient) DismissMusicChannel(ctx context.Context, in *music_topic_channel.DismissMusicChannelReq) (*music_topic_channel.DismissMusicChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DismissMusicChannel", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.DismissMusicChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DismissMusicChannel indicates an expected call of DismissMusicChannel.
func (mr *MockIClientMockRecorder) DismissMusicChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DismissMusicChannel", reflect.TypeOf((*MockIClient)(nil).DismissMusicChannel), ctx, in)
}

// GetChannelRoomUserNumber mocks base method.
func (m *MockIClient) GetChannelRoomUserNumber(ctx context.Context, in *music_topic_channel.GetChannelRoomUserNumberReq) (*music_topic_channel.GetChannelRoomUserNumberResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelRoomUserNumber", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.GetChannelRoomUserNumberResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelRoomUserNumber indicates an expected call of GetChannelRoomUserNumber.
func (mr *MockIClientMockRecorder) GetChannelRoomUserNumber(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelRoomUserNumber", reflect.TypeOf((*MockIClient)(nil).GetChannelRoomUserNumber), ctx, in)
}

// GetMusicChannelByIds mocks base method.
func (m *MockIClient) GetMusicChannelByIds(ctx context.Context, in *music_topic_channel.GetMusicChannelByIdsReq) (*music_topic_channel.GetMusicChannelByIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMusicChannelByIds", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.GetMusicChannelByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMusicChannelByIds indicates an expected call of GetMusicChannelByIds.
func (mr *MockIClientMockRecorder) GetMusicChannelByIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicChannelByIds", reflect.TypeOf((*MockIClient)(nil).GetMusicChannelByIds), ctx, in)
}

// GetMusicChannelFilterV2 mocks base method.
func (m *MockIClient) GetMusicChannelFilterV2(ctx context.Context, in *music_topic_channel.GetMusicChannelFilterV2Req) (*music_topic_channel.GetMusicChannelFilterV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMusicChannelFilterV2", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.GetMusicChannelFilterV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMusicChannelFilterV2 indicates an expected call of GetMusicChannelFilterV2.
func (mr *MockIClientMockRecorder) GetMusicChannelFilterV2(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicChannelFilterV2", reflect.TypeOf((*MockIClient)(nil).GetMusicChannelFilterV2), ctx, in)
}

// GetMusicChannelList mocks base method.
func (m *MockIClient) GetMusicChannelList(ctx context.Context, in *music_topic_channel.GetMusicChannelListReq) (*music_topic_channel.GetMusicChannelListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMusicChannelList", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.GetMusicChannelListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMusicChannelList indicates an expected call of GetMusicChannelList.
func (mr *MockIClientMockRecorder) GetMusicChannelList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicChannelList", reflect.TypeOf((*MockIClient)(nil).GetMusicChannelList), ctx, in)
}

// GetMusicFilterItemByIds mocks base method.
func (m *MockIClient) GetMusicFilterItemByIds(ctx context.Context, in *music_topic_channel.GetMusicFilterItemByIdsReq) (*music_topic_channel.GetMusicFilterItemByIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMusicFilterItemByIds", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.GetMusicFilterItemByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMusicFilterItemByIds indicates an expected call of GetMusicFilterItemByIds.
func (mr *MockIClientMockRecorder) GetMusicFilterItemByIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicFilterItemByIds", reflect.TypeOf((*MockIClient)(nil).GetMusicFilterItemByIds), ctx, in)
}

// GetRcmdPgcChannel mocks base method.
func (m *MockIClient) GetRcmdPgcChannel(ctx context.Context, in *music_topic_channel.GetRcmdPgcChannelReq) (*music_topic_channel.GetRcmdPgcChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRcmdPgcChannel", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.GetRcmdPgcChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRcmdPgcChannel indicates an expected call of GetRcmdPgcChannel.
func (mr *MockIClientMockRecorder) GetRcmdPgcChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRcmdPgcChannel", reflect.TypeOf((*MockIClient)(nil).GetRcmdPgcChannel), ctx, in)
}

// GetTabPublishHotRcmd mocks base method.
func (m *MockIClient) GetTabPublishHotRcmd(ctx context.Context, in *music_topic_channel.GetTabPublishHotRcmdReq) (*music_topic_channel.GetTabPublishHotRcmdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabPublishHotRcmd", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.GetTabPublishHotRcmdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabPublishHotRcmd indicates an expected call of GetTabPublishHotRcmd.
func (mr *MockIClientMockRecorder) GetTabPublishHotRcmd(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabPublishHotRcmd", reflect.TypeOf((*MockIClient)(nil).GetTabPublishHotRcmd), ctx, in)
}

// GetUserSchoolLast mocks base method.
func (m *MockIClient) GetUserSchoolLast(ctx context.Context, in *music_topic_channel.GetUserSchoolLastReq) (*music_topic_channel.GetUserSchoolLastResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSchoolLast", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.GetUserSchoolLastResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSchoolLast indicates an expected call of GetUserSchoolLast.
func (mr *MockIClientMockRecorder) GetUserSchoolLast(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSchoolLast", reflect.TypeOf((*MockIClient)(nil).GetUserSchoolLast), ctx, in)
}

// InitMusicChannelReleaseInfo mocks base method.
func (m *MockIClient) InitMusicChannelReleaseInfo(ctx context.Context, in *music_topic_channel.InitMusicChannelReleaseInfoReq) (*music_topic_channel.InitMusicChannelReleaseInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitMusicChannelReleaseInfo", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.InitMusicChannelReleaseInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitMusicChannelReleaseInfo indicates an expected call of InitMusicChannelReleaseInfo.
func (mr *MockIClientMockRecorder) InitMusicChannelReleaseInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitMusicChannelReleaseInfo", reflect.TypeOf((*MockIClient)(nil).InitMusicChannelReleaseInfo), ctx, in)
}

// IsOlderForMusicHomePage mocks base method.
func (m *MockIClient) IsOlderForMusicHomePage(ctx context.Context, in *music_topic_channel.IsOlderForMusicHomePageReq) (*music_topic_channel.IsOlderForMusicHomePageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsOlderForMusicHomePage", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.IsOlderForMusicHomePageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsOlderForMusicHomePage indicates an expected call of IsOlderForMusicHomePage.
func (mr *MockIClientMockRecorder) IsOlderForMusicHomePage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsOlderForMusicHomePage", reflect.TypeOf((*MockIClient)(nil).IsOlderForMusicHomePage), ctx, in)
}

// IsPublishing mocks base method.
func (m *MockIClient) IsPublishing(ctx context.Context, in *music_topic_channel.IsPublishingReq) (*music_topic_channel.IsPublishingResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsPublishing", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.IsPublishingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsPublishing indicates an expected call of IsPublishing.
func (mr *MockIClientMockRecorder) IsPublishing(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsPublishing", reflect.TypeOf((*MockIClient)(nil).IsPublishing), ctx, in)
}

// ListHomePageFilterItems mocks base method.
func (m *MockIClient) ListHomePageFilterItems(ctx context.Context, in *music_topic_channel.ListHomePageFilterItemsReq) (*music_topic_channel.ListHomePageFilterItemsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListHomePageFilterItems", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.ListHomePageFilterItemsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListHomePageFilterItems indicates an expected call of ListHomePageFilterItems.
func (mr *MockIClientMockRecorder) ListHomePageFilterItems(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListHomePageFilterItems", reflect.TypeOf((*MockIClient)(nil).ListHomePageFilterItems), ctx, in)
}

// ListMusicChannelViewPbs mocks base method.
func (m *MockIClient) ListMusicChannelViewPbs(ctx context.Context, in *music_topic_channel.ListMusicChannelViewPbsReq) (*music_topic_channel.ListMusicChannelViewPbsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMusicChannelViewPbs", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.ListMusicChannelViewPbsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMusicChannelViewPbs indicates an expected call of ListMusicChannelViewPbs.
func (mr *MockIClientMockRecorder) ListMusicChannelViewPbs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMusicChannelViewPbs", reflect.TypeOf((*MockIClient)(nil).ListMusicChannelViewPbs), ctx, in)
}

// ListMusicChannelViews mocks base method.
func (m *MockIClient) ListMusicChannelViews(ctx context.Context, in *music_topic_channel.ListMusicChannelViewsReq) (*music_topic_channel.ListMusicChannelViewsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMusicChannelViews", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.ListMusicChannelViewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMusicChannelViews indicates an expected call of ListMusicChannelViews.
func (mr *MockIClientMockRecorder) ListMusicChannelViews(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMusicChannelViews", reflect.TypeOf((*MockIClient)(nil).ListMusicChannelViews), ctx, in)
}

// ListMusicChannelViewsForMusic mocks base method.
func (m *MockIClient) ListMusicChannelViewsForMusic(ctx context.Context, in *music_topic_channel.ListMusicChannelViewsForMusicReq) (*music_topic_channel.ListMusicChannelViewsForMusicResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMusicChannelViewsForMusic", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.ListMusicChannelViewsForMusicResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMusicChannelViewsForMusic indicates an expected call of ListMusicChannelViewsForMusic.
func (mr *MockIClientMockRecorder) ListMusicChannelViewsForMusic(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMusicChannelViewsForMusic", reflect.TypeOf((*MockIClient)(nil).ListMusicChannelViewsForMusic), ctx, in)
}

// ListPublishingChannelIds mocks base method.
func (m *MockIClient) ListPublishingChannelIds(ctx context.Context, in *music_topic_channel.ListPublishingChannelIdsReq) (*music_topic_channel.ListPublishingChannelIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPublishingChannelIds", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.ListPublishingChannelIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPublishingChannelIds indicates an expected call of ListPublishingChannelIds.
func (mr *MockIClientMockRecorder) ListPublishingChannelIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPublishingChannelIds", reflect.TypeOf((*MockIClient)(nil).ListPublishingChannelIds), ctx, in)
}

// SearchHighQualityChannels mocks base method.
func (m *MockIClient) SearchHighQualityChannels(ctx context.Context, in *music_topic_channel.SearchHighQualityChannelsReq) (*music_topic_channel.SearchHighQualityChannelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchHighQualityChannels", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.SearchHighQualityChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchHighQualityChannels indicates an expected call of SearchHighQualityChannels.
func (mr *MockIClientMockRecorder) SearchHighQualityChannels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchHighQualityChannels", reflect.TypeOf((*MockIClient)(nil).SearchHighQualityChannels), ctx, in)
}

// SetMusicChannelReleaseInfo mocks base method.
func (m *MockIClient) SetMusicChannelReleaseInfo(ctx context.Context, in *music_topic_channel.SetMusicChannelReleaseInfoReq) (*music_topic_channel.SetMusicChannelReleaseInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMusicChannelReleaseInfo", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.SetMusicChannelReleaseInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetMusicChannelReleaseInfo indicates an expected call of SetMusicChannelReleaseInfo.
func (mr *MockIClientMockRecorder) SetMusicChannelReleaseInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMusicChannelReleaseInfo", reflect.TypeOf((*MockIClient)(nil).SetMusicChannelReleaseInfo), ctx, in)
}

// SetUserSchoolLast mocks base method.
func (m *MockIClient) SetUserSchoolLast(ctx context.Context, in *music_topic_channel.SetUserSchoolLastReq) (*music_topic_channel.SetUserSchoolLastResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSchoolLast", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.SetUserSchoolLastResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserSchoolLast indicates an expected call of SetUserSchoolLast.
func (mr *MockIClientMockRecorder) SetUserSchoolLast(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSchoolLast", reflect.TypeOf((*MockIClient)(nil).SetUserSchoolLast), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// SwitchChannelTab mocks base method.
func (m *MockIClient) SwitchChannelTab(ctx context.Context, in *music_topic_channel.SwitchChannelTabReq) (*music_topic_channel.SwitchChannelTabResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwitchChannelTab", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.SwitchChannelTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SwitchChannelTab indicates an expected call of SwitchChannelTab.
func (mr *MockIClientMockRecorder) SwitchChannelTab(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwitchChannelTab", reflect.TypeOf((*MockIClient)(nil).SwitchChannelTab), ctx, in)
}

// UpdateHighQualityChannels mocks base method.
func (m *MockIClient) UpdateHighQualityChannels(ctx context.Context, in *music_topic_channel.UpdateHighQualityChannelsReq) (*music_topic_channel.UpdateHighQualityChannelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateHighQualityChannels", ctx, in)
	ret0, _ := ret[0].(*music_topic_channel.UpdateHighQualityChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateHighQualityChannels indicates an expected call of UpdateHighQualityChannels.
func (mr *MockIClientMockRecorder) UpdateHighQualityChannels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateHighQualityChannels", reflect.TypeOf((*MockIClient)(nil).UpdateHighQualityChannels), ctx, in)
}
