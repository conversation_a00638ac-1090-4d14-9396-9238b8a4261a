// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/knock/iclient.go

// Package knock is a generated GoMock package.
package knock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	knock "golang.52tt.com/protocol/services/knock"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetKnockInfo mocks base method.
func (m *MockIClient) GetKnockInfo(ctx context.Context, req knock.KnockInfoReq) (*knock.KnockInfoRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKnockInfo", ctx, req)
	ret0, _ := ret[0].(*knock.KnockInfoRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetKnockInfo indicates an expected call of GetKnockInfo.
func (mr *MockIClientMockRecorder) GetKnockInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKnockInfo", reflect.TypeOf((*MockIClient)(nil).GetKnockInfo), ctx, req)
}

// HandleKnock mocks base method.
func (m *MockIClient) HandleKnock(ctx context.Context, req knock.HandleKnockReq) (*knock.HandleKnockRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleKnock", ctx, req)
	ret0, _ := ret[0].(*knock.HandleKnockRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// HandleKnock indicates an expected call of HandleKnock.
func (mr *MockIClientMockRecorder) HandleKnock(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleKnock", reflect.TypeOf((*MockIClient)(nil).HandleKnock), ctx, req)
}

// Knock mocks base method.
func (m *MockIClient) Knock(ctx context.Context, req knock.KnockReq) (*knock.KnockResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Knock", ctx, req)
	ret0, _ := ret[0].(*knock.KnockResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// Knock indicates an expected call of Knock.
func (mr *MockIClientMockRecorder) Knock(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Knock", reflect.TypeOf((*MockIClient)(nil).Knock), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
