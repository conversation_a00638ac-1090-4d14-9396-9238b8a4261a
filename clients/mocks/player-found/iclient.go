// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/player-found/iclient.go

// Package playerfound is a generated GoMock package.
package playerfound

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	playerfound "golang.52tt.com/protocol/services/playerfound"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetUserPosts mocks base method.
func (m *MockIClient) BatchGetUserPosts(ctx context.Context, uids []uint32) (*playerfound.BatchGetUserPostsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserPosts", ctx, uids)
	ret0, _ := ret[0].(*playerfound.BatchGetUserPostsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserPosts indicates an expected call of BatchGetUserPosts.
func (mr *MockIClientMockRecorder) BatchGetUserPosts(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserPosts", reflect.TypeOf((*MockIClient)(nil).BatchGetUserPosts), ctx, uids)
}

// BatchSetUserPosts mocks base method.
func (m *MockIClient) BatchSetUserPosts(ctx context.Context, userPosts []*playerfound.PlaymateUserPosts) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetUserPosts", ctx, userPosts)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// BatchSetUserPosts indicates an expected call of BatchSetUserPosts.
func (mr *MockIClientMockRecorder) BatchSetUserPosts(ctx, userPosts interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetUserPosts", reflect.TypeOf((*MockIClient)(nil).BatchSetUserPosts), ctx, userPosts)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DelUserPosts mocks base method.
func (m *MockIClient) DelUserPosts(ctx context.Context, uid uint32) (*playerfound.DelUserPostsRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserPosts", ctx, uid)
	ret0, _ := ret[0].(*playerfound.DelUserPostsRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelUserPosts indicates an expected call of DelUserPosts.
func (mr *MockIClientMockRecorder) DelUserPosts(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserPosts", reflect.TypeOf((*MockIClient)(nil).DelUserPosts), ctx, uid)
}

// GetPlayerFoundSetting mocks base method.
func (m *MockIClient) GetPlayerFoundSetting(ctx context.Context, uids []uint32) (*playerfound.GetPlayerFoundSettingResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlayerFoundSetting", ctx, uids)
	ret0, _ := ret[0].(*playerfound.GetPlayerFoundSettingResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPlayerFoundSetting indicates an expected call of GetPlayerFoundSetting.
func (mr *MockIClientMockRecorder) GetPlayerFoundSetting(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlayerFoundSetting", reflect.TypeOf((*MockIClient)(nil).GetPlayerFoundSetting), ctx, uids)
}

// GetPlayerFoundSettingWithType mocks base method.
func (m *MockIClient) GetPlayerFoundSettingWithType(ctx context.Context, uids []uint32, settingType playerfound.Settings) (map[uint32]bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlayerFoundSettingWithType", ctx, uids, settingType)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPlayerFoundSettingWithType indicates an expected call of GetPlayerFoundSettingWithType.
func (mr *MockIClientMockRecorder) GetPlayerFoundSettingWithType(ctx, uids, settingType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlayerFoundSettingWithType", reflect.TypeOf((*MockIClient)(nil).GetPlayerFoundSettingWithType), ctx, uids, settingType)
}

// GetUserPosts mocks base method.
func (m *MockIClient) GetUserPosts(ctx context.Context, uid uint32) (*playerfound.GetUserPostsRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPosts", ctx, uid)
	ret0, _ := ret[0].(*playerfound.GetUserPostsRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserPosts indicates an expected call of GetUserPosts.
func (mr *MockIClientMockRecorder) GetUserPosts(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPosts", reflect.TypeOf((*MockIClient)(nil).GetUserPosts), ctx, uid)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdatePlayerFoundSetting mocks base method.
func (m *MockIClient) UpdatePlayerFoundSetting(ctx context.Context, uid uint32, on bool, settingType playerfound.Settings) (*playerfound.UpdatePlayerFoundSettingResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePlayerFoundSetting", ctx, uid, on, settingType)
	ret0, _ := ret[0].(*playerfound.UpdatePlayerFoundSettingResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdatePlayerFoundSetting indicates an expected call of UpdatePlayerFoundSetting.
func (mr *MockIClientMockRecorder) UpdatePlayerFoundSetting(ctx, uid, on, settingType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePlayerFoundSetting", reflect.TypeOf((*MockIClient)(nil).UpdatePlayerFoundSetting), ctx, uid, on, settingType)
}
