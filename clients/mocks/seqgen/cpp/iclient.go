// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/seqgen/cpp (interfaces: IClient)

// Package SeqGen is a generated GoMock package.
package SeqGen

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	SeqGen "golang.52tt.com/protocol/services/seqgensvr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetLastestSeq mocks base method.
func (m *MockIClient) BatchGetLastestSeq(arg0 context.Context, arg1 uint32, arg2 []uint32, arg3 uint32, arg4 string) (*SeqGen.BatchGetLastestSeqResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetLastestSeq", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*SeqGen.BatchGetLastestSeqResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetLastestSeq indicates an expected call of BatchGetLastestSeq.
func (mr *MockIClientMockRecorder) BatchGetLastestSeq(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetLastestSeq", reflect.TypeOf((*MockIClient)(nil).BatchGetLastestSeq), arg0, arg1, arg2, arg3, arg4)
}

// BatchSetUserGroupNeedUpdate mocks base method.
func (m *MockIClient) BatchSetUserGroupNeedUpdate(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 []uint32) (*SeqGen.BatchSetUserGroupNeedUpdateResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetUserGroupNeedUpdate", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*SeqGen.BatchSetUserGroupNeedUpdateResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchSetUserGroupNeedUpdate indicates an expected call of BatchSetUserGroupNeedUpdate.
func (mr *MockIClientMockRecorder) BatchSetUserGroupNeedUpdate(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetUserGroupNeedUpdate", reflect.TypeOf((*MockIClient)(nil).BatchSetUserGroupNeedUpdate), arg0, arg1, arg2, arg3, arg4)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DelSeqMap mocks base method.
func (m *MockIClient) DelSeqMap(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) (*SeqGen.DelSeqMapResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelSeqMap", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*SeqGen.DelSeqMapResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelSeqMap indicates an expected call of DelSeqMap.
func (mr *MockIClientMockRecorder) DelSeqMap(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSeqMap", reflect.TypeOf((*MockIClient)(nil).DelSeqMap), arg0, arg1, arg2, arg3)
}

// GenSeqId mocks base method.
func (m *MockIClient) GenSeqId(arg0 context.Context, arg1, arg2 uint32, arg3 string, arg4 uint32) (*SeqGen.GenSeqRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenSeqId", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*SeqGen.GenSeqRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GenSeqId indicates an expected call of GenSeqId.
func (mr *MockIClientMockRecorder) GenSeqId(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenSeqId", reflect.TypeOf((*MockIClient)(nil).GenSeqId), arg0, arg1, arg2, arg3, arg4)
}

// GetLastestSeq mocks base method.
func (m *MockIClient) GetLastestSeq(arg0 context.Context, arg1, arg2 uint32, arg3 string) (*SeqGen.GetLastestSeqResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastestSeq", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*SeqGen.GetLastestSeqResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLastestSeq indicates an expected call of GetLastestSeq.
func (mr *MockIClientMockRecorder) GetLastestSeq(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastestSeq", reflect.TypeOf((*MockIClient)(nil).GetLastestSeq), arg0, arg1, arg2, arg3)
}

// GetSeqMap mocks base method.
func (m *MockIClient) GetSeqMap(arg0 context.Context, arg1, arg2 uint32) (map[uint32]uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSeqMap", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSeqMap indicates an expected call of GetSeqMap.
func (mr *MockIClientMockRecorder) GetSeqMap(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSeqMap", reflect.TypeOf((*MockIClient)(nil).GetSeqMap), arg0, arg1, arg2)
}

// SetSeq mocks base method.
func (m *MockIClient) SetSeq(arg0 context.Context, arg1, arg2, arg3, arg4 uint32, arg5 string) (*SeqGen.SetSeqResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSeq", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*SeqGen.SetSeqResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetSeq indicates an expected call of SetSeq.
func (mr *MockIClientMockRecorder) SetSeq(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSeq", reflect.TypeOf((*MockIClient)(nil).SetSeq), arg0, arg1, arg2, arg3, arg4, arg5)
}

// SetSeqMap mocks base method.
func (m *MockIClient) SetSeqMap(arg0 context.Context, arg1, arg2 uint32, arg3 []*SeqGen.SeqPair) (*SeqGen.SetSeqMapResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSeqMap", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*SeqGen.SetSeqMapResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetSeqMap indicates an expected call of SetSeqMap.
func (mr *MockIClientMockRecorder) SetSeqMap(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSeqMap", reflect.TypeOf((*MockIClient)(nil).SetSeqMap), arg0, arg1, arg2, arg3)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
