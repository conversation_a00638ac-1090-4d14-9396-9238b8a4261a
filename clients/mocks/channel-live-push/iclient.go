// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/channel-live-push/iclient.go

// Package channellivepush is a generated GoMock package.
package channellivepush

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channellivepush "golang.52tt.com/protocol/services/channellivepush"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// ChannelLiveReport mocks base method.
func (m *MockIClient) ChannelLiveReport(ctx context.Context, req *channellivepush.ChannelLiveReportReq) (*channellivepush.ChannelLiveReportResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelLiveReport", ctx, req)
	ret0, _ := ret[0].(*channellivepush.ChannelLiveReportResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ChannelLiveReport indicates an expected call of ChannelLiveReport.
func (mr *MockIClientMockRecorder) ChannelLiveReport(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelLiveReport", reflect.TypeOf((*MockIClient)(nil).ChannelLiveReport), ctx, req)
}

// ClearUserData mocks base method.
func (m *MockIClient) ClearUserData(ctx context.Context, uid uint32, all bool) (*channellivepush.ClearUserDataResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearUserData", ctx, uid, all)
	ret0, _ := ret[0].(*channellivepush.ClearUserDataResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ClearUserData indicates an expected call of ClearUserData.
func (mr *MockIClientMockRecorder) ClearUserData(ctx, uid, all interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearUserData", reflect.TypeOf((*MockIClient)(nil).ClearUserData), ctx, uid, all)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetChannelLivePushData mocks base method.
func (m *MockIClient) GetChannelLivePushData(ctx context.Context, uid uint32) (*channellivepush.GetChannelLivePushDataResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLivePushData", ctx, uid)
	ret0, _ := ret[0].(*channellivepush.GetChannelLivePushDataResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelLivePushData indicates an expected call of GetChannelLivePushData.
func (mr *MockIClientMockRecorder) GetChannelLivePushData(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLivePushData", reflect.TypeOf((*MockIClient)(nil).GetChannelLivePushData), ctx, uid)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
