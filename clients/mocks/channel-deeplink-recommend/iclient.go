// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/channel-deeplink-recommend/iclient.go

// Package channel_deeplink_recommend is a generated GoMock package.
package channel_deeplink_recommend

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channel_deeplink_recommend "golang.52tt.com/protocol/services/channel-deeplink-recommend"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddRecommendDiversionConf mocks base method.
func (m *MockIClient) AddRecommendDiversionConf(ctx context.Context, opUid uint32, in *channel_deeplink_recommend.AddRecommendDiversionConfReq) (*channel_deeplink_recommend.AddRecommendDiversionConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRecommendDiversionConf", ctx, opUid, in)
	ret0, _ := ret[0].(*channel_deeplink_recommend.AddRecommendDiversionConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddRecommendDiversionConf indicates an expected call of AddRecommendDiversionConf.
func (mr *MockIClientMockRecorder) AddRecommendDiversionConf(ctx, opUid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRecommendDiversionConf", reflect.TypeOf((*MockIClient)(nil).AddRecommendDiversionConf), ctx, opUid, in)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetRecommendChannelByRules mocks base method.
func (m *MockIClient) GetRecommendChannelByRules(ctx context.Context, in *channel_deeplink_recommend.GetRecommendChannelByRulesReq) (*channel_deeplink_recommend.GetRecommendChannelByRulesResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecommendChannelByRules", ctx, in)
	ret0, _ := ret[0].(*channel_deeplink_recommend.GetRecommendChannelByRulesResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRecommendChannelByRules indicates an expected call of GetRecommendChannelByRules.
func (mr *MockIClientMockRecorder) GetRecommendChannelByRules(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendChannelByRules", reflect.TypeOf((*MockIClient)(nil).GetRecommendChannelByRules), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
