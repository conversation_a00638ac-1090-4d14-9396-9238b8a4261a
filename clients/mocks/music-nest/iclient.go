// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/music-nest/iclient.go

// Package music_nest is a generated GoMock package.
package music_nest

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	music_nest "golang.52tt.com/protocol/services/music-nest"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

func (m *MockIClient) GetAllStayAddTicketActs(ctx context.Context) (*music_nest.GetAllStayAddTicketActsResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) RandomGetRobotImage(ctx context.Context, limit int64) (*music_nest.RandomGetRobotImageResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DisappearNextDirectionAct mocks base method.
func (m *MockIClient) DisappearNextDirectionAct(ctx context.Context, chid uint32) (*music_nest.DisappearNextDirectionActResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisappearNextDirectionAct", ctx, chid)
	ret0, _ := ret[0].(*music_nest.DisappearNextDirectionActResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DisappearNextDirectionAct indicates an expected call of DisappearNextDirectionAct.
func (mr *MockIClientMockRecorder) DisappearNextDirectionAct(ctx, chid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisappearNextDirectionAct", reflect.TypeOf((*MockIClient)(nil).DisappearNextDirectionAct), ctx, chid)
}

// GetActivityList mocks base method.
func (m *MockIClient) GetActivityList(ctx context.Context, req *music_nest.GetActivityListReq) (*music_nest.GetActivityListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActivityList", ctx, req)
	ret0, _ := ret[0].(*music_nest.GetActivityListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetActivityList indicates an expected call of GetActivityList.
func (mr *MockIClientMockRecorder) GetActivityList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActivityList", reflect.TypeOf((*MockIClient)(nil).GetActivityList), ctx, req)
}

// GetMusicNestActivityInfo mocks base method.
func (m *MockIClient) GetMusicNestActivityInfo(ctx context.Context, req *music_nest.GetMusicNestActivityInfoReq) (*music_nest.GetMusicNestActivityInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMusicNestActivityInfo", ctx, req)
	ret0, _ := ret[0].(*music_nest.GetMusicNestActivityInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMusicNestActivityInfo indicates an expected call of GetMusicNestActivityInfo.
func (mr *MockIClientMockRecorder) GetMusicNestActivityInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicNestActivityInfo", reflect.TypeOf((*MockIClient)(nil).GetMusicNestActivityInfo), ctx, req)
}

// GetMusicNestCoverAndLiveList mocks base method.
func (m *MockIClient) GetMusicNestCoverAndLiveList(ctx context.Context, req *music_nest.GetMusicNestCoverAndLiveListReq) (*music_nest.GetMusicNestCoverAndLiveListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMusicNestCoverAndLiveList", ctx, req)
	ret0, _ := ret[0].(*music_nest.GetMusicNestCoverAndLiveListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMusicNestCoverAndLiveList indicates an expected call of GetMusicNestCoverAndLiveList.
func (mr *MockIClientMockRecorder) GetMusicNestCoverAndLiveList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicNestCoverAndLiveList", reflect.TypeOf((*MockIClient)(nil).GetMusicNestCoverAndLiveList), ctx, req)
}

// GetMusicNestHomePage mocks base method.
func (m *MockIClient) GetMusicNestHomePage(ctx context.Context, req *music_nest.GetMusicNestHomePageReq) (*music_nest.GetMusicNestHomePageResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMusicNestHomePage", ctx, req)
	ret0, _ := ret[0].(*music_nest.GetMusicNestHomePageResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMusicNestHomePage indicates an expected call of GetMusicNestHomePage.
func (mr *MockIClientMockRecorder) GetMusicNestHomePage(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicNestHomePage", reflect.TypeOf((*MockIClient)(nil).GetMusicNestHomePage), ctx, req)
}

// GetMyMusicNestList mocks base method.
func (m *MockIClient) GetMyMusicNestList(ctx context.Context, req *music_nest.GetMyMusicNestListReq) (*music_nest.GetMyMusicNestListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyMusicNestList", ctx, req)
	ret0, _ := ret[0].(*music_nest.GetMyMusicNestListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMyMusicNestList indicates an expected call of GetMyMusicNestList.
func (mr *MockIClientMockRecorder) GetMyMusicNestList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyMusicNestList", reflect.TypeOf((*MockIClient)(nil).GetMyMusicNestList), ctx, req)
}

// GetPerformance mocks base method.
func (m *MockIClient) GetPerformance(ctx context.Context, req *music_nest.GetPerformanceReq) (*music_nest.GetPerformanceResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPerformance", ctx, req)
	ret0, _ := ret[0].(*music_nest.GetPerformanceResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPerformance indicates an expected call of GetPerformance.
func (mr *MockIClientMockRecorder) GetPerformance(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPerformance", reflect.TypeOf((*MockIClient)(nil).GetPerformance), ctx, req)
}

// GetSpecifiedChannelVisitedSize mocks base method.
func (m *MockIClient) GetSpecifiedChannelVisitedSize(ctx context.Context, channelIdList []uint32) (*music_nest.GetSpecifiedChannelVisitedSizeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpecifiedChannelVisitedSize", ctx, channelIdList)
	ret0, _ := ret[0].(*music_nest.GetSpecifiedChannelVisitedSizeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSpecifiedChannelVisitedSize indicates an expected call of GetSpecifiedChannelVisitedSize.
func (mr *MockIClientMockRecorder) GetSpecifiedChannelVisitedSize(ctx, channelIdList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpecifiedChannelVisitedSize", reflect.TypeOf((*MockIClient)(nil).GetSpecifiedChannelVisitedSize), ctx, channelIdList)
}

// GetWelcomePop mocks base method.
func (m *MockIClient) GetWelcomePop(ctx context.Context, channelId, uid uint32) (*music_nest.GetWelcomePopResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWelcomePop", ctx, channelId, uid)
	ret0, _ := ret[0].(*music_nest.GetWelcomePopResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetWelcomePop indicates an expected call of GetWelcomePop.
func (mr *MockIClientMockRecorder) GetWelcomePop(ctx, channelId, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWelcomePop", reflect.TypeOf((*MockIClient)(nil).GetWelcomePop), ctx, channelId, uid)
}

// IsMusicNestActChannel mocks base method.
func (m *MockIClient) IsMusicNestActChannel(ctx context.Context, channelid uint32) (*music_nest.IsMusicNestActChannelResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsMusicNestActChannel", ctx, channelid)
	ret0, _ := ret[0].(*music_nest.IsMusicNestActChannelResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// IsMusicNestActChannel indicates an expected call of IsMusicNestActChannel.
func (mr *MockIClientMockRecorder) IsMusicNestActChannel(ctx, channelid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsMusicNestActChannel", reflect.TypeOf((*MockIClient)(nil).IsMusicNestActChannel), ctx, channelid)
}

// SetCurrentPerformanceStage mocks base method.
func (m *MockIClient) SetCurrentPerformanceStage(ctx context.Context, req *music_nest.SetCurrentPerformanceStageReq) (*music_nest.SetCurrentPerformanceStageResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCurrentPerformanceStage", ctx, req)
	ret0, _ := ret[0].(*music_nest.SetCurrentPerformanceStageResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetCurrentPerformanceStage indicates an expected call of SetCurrentPerformanceStage.
func (mr *MockIClientMockRecorder) SetCurrentPerformanceStage(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCurrentPerformanceStage", reflect.TypeOf((*MockIClient)(nil).SetCurrentPerformanceStage), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// SubMusicNest mocks base method.
func (m *MockIClient) SubMusicNest(ctx context.Context, req *music_nest.SubMusicNestReq) (*music_nest.SubMusicNestResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubMusicNest", ctx, req)
	ret0, _ := ret[0].(*music_nest.SubMusicNestResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SubMusicNest indicates an expected call of SubMusicNest.
func (mr *MockIClientMockRecorder) SubMusicNest(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubMusicNest", reflect.TypeOf((*MockIClient)(nil).SubMusicNest), ctx, req)
}

// SubMusicNestActivity mocks base method.
func (m *MockIClient) SubMusicNestActivity(ctx context.Context, req *music_nest.SubMusicNestActivityReq) (*music_nest.SubMusicNestActivityResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubMusicNestActivity", ctx, req)
	ret0, _ := ret[0].(*music_nest.SubMusicNestActivityResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SubMusicNestActivity indicates an expected call of SubMusicNestActivity.
func (mr *MockIClientMockRecorder) SubMusicNestActivity(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubMusicNestActivity", reflect.TypeOf((*MockIClient)(nil).SubMusicNestActivity), ctx, req)
}

// UserClickPop mocks base method.
func (m *MockIClient) UserClickPop(ctx context.Context, channelId uint32, content string, index, opUid uint32) (*music_nest.UserClickPopResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserClickPop", ctx, channelId, content, index, opUid)
	ret0, _ := ret[0].(*music_nest.UserClickPopResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UserClickPop indicates an expected call of UserClickPop.
func (mr *MockIClientMockRecorder) UserClickPop(ctx, channelId, content, index, opUid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserClickPop", reflect.TypeOf((*MockIClient)(nil).UserClickPop), ctx, channelId, content, index, opUid)
}
