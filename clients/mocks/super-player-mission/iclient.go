// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/super-player-mission/iclient.go

// Package superplayermission is a generated GoMock package.
package superplayermission

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	super_player_mission "golang.52tt.com/protocol/services/super-player-mission"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetSuperPlayerMission mocks base method.
func (m *MockIClient) GetSuperPlayerMission(ctx context.Context, req *super_player_mission.GetSuperPlayerMissionReq) (*super_player_mission.GetSuperPlayerMissionResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSuperPlayerMission", ctx, req)
	ret0, _ := ret[0].(*super_player_mission.GetSuperPlayerMissionResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSuperPlayerMission indicates an expected call of GetSuperPlayerMission.
func (mr *MockIClientMockRecorder) GetSuperPlayerMission(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSuperPlayerMission", reflect.TypeOf((*MockIClient)(nil).GetSuperPlayerMission), ctx, req)
}

// GetSuperPlayerMissionById mocks base method.
func (m *MockIClient) GetSuperPlayerMissionById(ctx context.Context, req *super_player_mission.GetSuperPlayerMissionByIdReq) (*super_player_mission.GetSuperPlayerMissionByIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSuperPlayerMissionById", ctx, req)
	ret0, _ := ret[0].(*super_player_mission.GetSuperPlayerMissionByIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSuperPlayerMissionById indicates an expected call of GetSuperPlayerMissionById.
func (mr *MockIClientMockRecorder) GetSuperPlayerMissionById(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSuperPlayerMissionById", reflect.TypeOf((*MockIClient)(nil).GetSuperPlayerMissionById), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
