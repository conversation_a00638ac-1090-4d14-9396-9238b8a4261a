// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/channellive/iclient.go

// Package channellive is a generated GoMock package.
package channellive

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	channellive "golang.52tt.com/protocol/services/channellivesvr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetQueueUpMicApplyList mocks base method.
func (m *MockIClient) GetQueueUpMicApplyList(ctx context.Context, uin, channelId, offset, limit uint32) (*channellive.GetQueueUpMicApplyListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQueueUpMicApplyList", ctx, uin, channelId, offset, limit)
	ret0, _ := ret[0].(*channellive.GetQueueUpMicApplyListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQueueUpMicApplyList indicates an expected call of GetQueueUpMicApplyList.
func (mr *MockIClientMockRecorder) GetQueueUpMicApplyList(ctx, uin, channelId, offset, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQueueUpMicApplyList", reflect.TypeOf((*MockIClient)(nil).GetQueueUpMicApplyList), ctx, uin, channelId, offset, limit)
}

// QueueUpMicApply mocks base method.
func (m *MockIClient) QueueUpMicApply(ctx context.Context, userID, cid uint32, IsCancel bool) (*channellive.QueueUpMicApplyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueueUpMicApply", ctx, userID, cid, IsCancel)
	ret0, _ := ret[0].(*channellive.QueueUpMicApplyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueueUpMicApply indicates an expected call of QueueUpMicApply.
func (mr *MockIClientMockRecorder) QueueUpMicApply(ctx, userID, cid, IsCancel interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueueUpMicApply", reflect.TypeOf((*MockIClient)(nil).QueueUpMicApply), ctx, userID, cid, IsCancel)
}

// QueueUpMicHandle mocks base method.
func (m *MockIClient) QueueUpMicHandle(ctx context.Context, uin, channelId, uid uint32, allow bool) (*channellive.QueueUpMicHandleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueueUpMicHandle", ctx, uin, channelId, uid, allow)
	ret0, _ := ret[0].(*channellive.QueueUpMicHandleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueueUpMicHandle indicates an expected call of QueueUpMicHandle.
func (mr *MockIClientMockRecorder) QueueUpMicHandle(ctx, uin, channelId, uid, allow interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueueUpMicHandle", reflect.TypeOf((*MockIClient)(nil).QueueUpMicHandle), ctx, uin, channelId, uid, allow)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
