// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/dark-gift-bonus/iclient.go

// Package dark_gift_bonus is a generated GoMock package.
package dark_gift_bonus

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	dark_gift_bonus "golang.52tt.com/protocol/services/dark-gift-bonus"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddBuffConf mocks base method.
func (m *MockIClient) AddBuffConf(ctx context.Context, opUid uint32, conf *dark_gift_bonus.BuffConf) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBuffConf", ctx, opUid, conf)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddBuffConf indicates an expected call of AddBuffConf.
func (mr *MockIClientMockRecorder) AddBuffConf(ctx, opUid, conf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBuffConf", reflect.TypeOf((*MockIClient)(nil).AddBuffConf), ctx, opUid, conf)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DelBuffConf mocks base method.
func (m *MockIClient) DelBuffConf(ctx context.Context, opUid, confId, businessSource uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelBuffConf", ctx, opUid, confId, businessSource)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// DelBuffConf indicates an expected call of DelBuffConf.
func (mr *MockIClientMockRecorder) DelBuffConf(ctx, opUid, confId, businessSource interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBuffConf", reflect.TypeOf((*MockIClient)(nil).DelBuffConf), ctx, opUid, confId, businessSource)
}

// GetBuffConf mocks base method.
func (m *MockIClient) GetBuffConf(ctx context.Context, opUid, businessSource uint32) (*dark_gift_bonus.GetBuffConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBuffConf", ctx, opUid, businessSource)
	ret0, _ := ret[0].(*dark_gift_bonus.GetBuffConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetBuffConf indicates an expected call of GetBuffConf.
func (mr *MockIClientMockRecorder) GetBuffConf(ctx, opUid, businessSource interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBuffConf", reflect.TypeOf((*MockIClient)(nil).GetBuffConf), ctx, opUid, businessSource)
}

// GetDarkGiftBonusRecord mocks base method.
func (m *MockIClient) GetDarkGiftBonusRecord(ctx context.Context, opUid, businessSource, offset, limit uint32) (*dark_gift_bonus.GetDarkGiftBonusRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDarkGiftBonusRecord", ctx, opUid, businessSource, offset, limit)
	ret0, _ := ret[0].(*dark_gift_bonus.GetDarkGiftBonusRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetDarkGiftBonusRecord indicates an expected call of GetDarkGiftBonusRecord.
func (mr *MockIClientMockRecorder) GetDarkGiftBonusRecord(ctx, opUid, businessSource, offset, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDarkGiftBonusRecord", reflect.TypeOf((*MockIClient)(nil).GetDarkGiftBonusRecord), ctx, opUid, businessSource, offset, limit)
}

// GetDarkGiftBonusSummary mocks base method.
func (m *MockIClient) GetDarkGiftBonusSummary(ctx context.Context, opUid, businessSource, begin, end uint32) (int64, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDarkGiftBonusSummary", ctx, opUid, businessSource, begin, end)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetDarkGiftBonusSummary indicates an expected call of GetDarkGiftBonusSummary.
func (mr *MockIClientMockRecorder) GetDarkGiftBonusSummary(ctx, opUid, businessSource, begin, end interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDarkGiftBonusSummary", reflect.TypeOf((*MockIClient)(nil).GetDarkGiftBonusSummary), ctx, opUid, businessSource, begin, end)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdateBuffConf mocks base method.
func (m *MockIClient) UpdateBuffConf(ctx context.Context, opUid uint32, conf *dark_gift_bonus.BuffConf) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBuffConf", ctx, opUid, conf)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// UpdateBuffConf indicates an expected call of UpdateBuffConf.
func (mr *MockIClientMockRecorder) UpdateBuffConf(ctx, opUid, conf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBuffConf", reflect.TypeOf((*MockIClient)(nil).UpdateBuffConf), ctx, opUid, conf)
}
