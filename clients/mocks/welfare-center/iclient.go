// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/welfare-center/iclient.go

// Package roommasterapprentice is a generated GoMock package.
package roommasterapprentice

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	welfarecenter "golang.52tt.com/protocol/services/welfarecenter"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BindWXUserPayInfo mocks base method.
func (m *MockIClient) BindWXUserPayInfo(ctx context.Context, in *welfarecenter.BindWXUserPayInfoReq) (*welfarecenter.BindWXUserPayInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindWXUserPayInfo", ctx, in)
	ret0, _ := ret[0].(*welfarecenter.BindWXUserPayInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BindWXUserPayInfo indicates an expected call of BindWXUserPayInfo.
func (mr *MockIClientMockRecorder) BindWXUserPayInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindWXUserPayInfo", reflect.TypeOf((*MockIClient)(nil).BindWXUserPayInfo), ctx, in)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DrawBalance mocks base method.
func (m *MockIClient) DrawBalance(ctx context.Context, in *welfarecenter.DrawBalanceReq) (*welfarecenter.DrawBalanceResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DrawBalance", ctx, in)
	ret0, _ := ret[0].(*welfarecenter.DrawBalanceResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DrawBalance indicates an expected call of DrawBalance.
func (mr *MockIClientMockRecorder) DrawBalance(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DrawBalance", reflect.TypeOf((*MockIClient)(nil).DrawBalance), ctx, in)
}

// GetBindingInfo mocks base method.
func (m *MockIClient) GetBindingInfo(ctx context.Context, in *welfarecenter.GetBindingInfoReq) (*welfarecenter.GetBindingInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindingInfo", ctx, in)
	ret0, _ := ret[0].(*welfarecenter.GetBindingInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetBindingInfo indicates an expected call of GetBindingInfo.
func (mr *MockIClientMockRecorder) GetBindingInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindingInfo", reflect.TypeOf((*MockIClient)(nil).GetBindingInfo), ctx, in)
}

// GetDrawLimitList mocks base method.
func (m *MockIClient) GetDrawLimitList(ctx context.Context, in *welfarecenter.GetDrawLimitListReq) (*welfarecenter.GetDrawLimitListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDrawLimitList", ctx, in)
	ret0, _ := ret[0].(*welfarecenter.GetDrawLimitListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetDrawLimitList indicates an expected call of GetDrawLimitList.
func (mr *MockIClientMockRecorder) GetDrawLimitList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDrawLimitList", reflect.TypeOf((*MockIClient)(nil).GetDrawLimitList), ctx, in)
}

// GetOrderInfoByUserIDOrderID mocks base method.
func (m *MockIClient) GetOrderInfoByUserIDOrderID(ctx context.Context, in *welfarecenter.GetOrderInfoByUserIDOrderIDReq) (*welfarecenter.GetOrderInfoByUserIDOrderIDResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderInfoByUserIDOrderID", ctx, in)
	ret0, _ := ret[0].(*welfarecenter.GetOrderInfoByUserIDOrderIDResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetOrderInfoByUserIDOrderID indicates an expected call of GetOrderInfoByUserIDOrderID.
func (mr *MockIClientMockRecorder) GetOrderInfoByUserIDOrderID(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderInfoByUserIDOrderID", reflect.TypeOf((*MockIClient)(nil).GetOrderInfoByUserIDOrderID), ctx, in)
}

// GetOrderListByUserID mocks base method.
func (m *MockIClient) GetOrderListByUserID(ctx context.Context, in *welfarecenter.GetOrderListByUserIDReq) (*welfarecenter.GetOrderListByUserIDResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderListByUserID", ctx, in)
	ret0, _ := ret[0].(*welfarecenter.GetOrderListByUserIDResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetOrderListByUserID indicates an expected call of GetOrderListByUserID.
func (mr *MockIClientMockRecorder) GetOrderListByUserID(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderListByUserID", reflect.TypeOf((*MockIClient)(nil).GetOrderListByUserID), ctx, in)
}

// GetUserBalanceByUserID mocks base method.
func (m *MockIClient) GetUserBalanceByUserID(ctx context.Context, in *welfarecenter.GetUserBalanceByUserIDReq) (*welfarecenter.GetUserBalanceByUserIDResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserBalanceByUserID", ctx, in)
	ret0, _ := ret[0].(*welfarecenter.GetUserBalanceByUserIDResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserBalanceByUserID indicates an expected call of GetUserBalanceByUserID.
func (mr *MockIClientMockRecorder) GetUserBalanceByUserID(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBalanceByUserID", reflect.TypeOf((*MockIClient)(nil).GetUserBalanceByUserID), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
