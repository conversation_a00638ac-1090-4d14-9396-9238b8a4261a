// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/WorkSpace/tt/server/quicksilver/clients/channel-minigame/iclient.go

// Package channel_minigame is a generated GoMock package.
package channel_minigame

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	channel_minigame "golang.52tt.com/protocol/services/channel-minigame"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// CloseNumBomb mocks base method.
func (m *MockIClient) CloseNumBomb(ctx context.Context, in *channel_minigame.CloseNumBombReq) (*channel_minigame.CloseNumBombResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseNumBomb", ctx, in)
	ret0, _ := ret[0].(*channel_minigame.CloseNumBombResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CloseNumBomb indicates an expected call of CloseNumBomb.
func (mr *MockIClientMockRecorder) CloseNumBomb(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseNumBomb", reflect.TypeOf((*MockIClient)(nil).CloseNumBomb), ctx, in)
}

// GetNumBombStatus mocks base method.
func (m *MockIClient) GetNumBombStatus(ctx context.Context, in *channel_minigame.GetNumBombStatusReq) (*channel_minigame.GetNumBombStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNumBombStatus", ctx, in)
	ret0, _ := ret[0].(*channel_minigame.GetNumBombStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNumBombStatus indicates an expected call of GetNumBombStatus.
func (mr *MockIClientMockRecorder) GetNumBombStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNumBombStatus", reflect.TypeOf((*MockIClient)(nil).GetNumBombStatus), ctx, in)
}

// OpenNumBomb mocks base method.
func (m *MockIClient) OpenNumBomb(ctx context.Context, in *channel_minigame.OpenNumBombReq) (*channel_minigame.OpenNumBombResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OpenNumBomb", ctx, in)
	ret0, _ := ret[0].(*channel_minigame.OpenNumBombResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OpenNumBomb indicates an expected call of OpenNumBomb.
func (mr *MockIClientMockRecorder) OpenNumBomb(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OpenNumBomb", reflect.TypeOf((*MockIClient)(nil).OpenNumBomb), ctx, in)
}

// PlayNumBomb mocks base method.
func (m *MockIClient) PlayNumBomb(ctx context.Context, in *channel_minigame.PlayNumBombReq) (*channel_minigame.PlayNumBombResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PlayNumBomb", ctx, in)
	ret0, _ := ret[0].(*channel_minigame.PlayNumBombResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PlayNumBomb indicates an expected call of PlayNumBomb.
func (mr *MockIClientMockRecorder) PlayNumBomb(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PlayNumBomb", reflect.TypeOf((*MockIClient)(nil).PlayNumBomb), ctx, in)
}

// StartNumBomb mocks base method.
func (m *MockIClient) StartNumBomb(ctx context.Context, in *channel_minigame.StartNumBombReq) (*channel_minigame.StartNumBombResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartNumBomb", ctx, in)
	ret0, _ := ret[0].(*channel_minigame.StartNumBombResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartNumBomb indicates an expected call of StartNumBomb.
func (mr *MockIClientMockRecorder) StartNumBomb(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartNumBomb", reflect.TypeOf((*MockIClient)(nil).StartNumBomb), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
