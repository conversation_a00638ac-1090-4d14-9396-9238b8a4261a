// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/TTProjects/quicksilver/clients/channel-mic-go/iclient.go

// Package channel_mic_go is a generated GoMock package.
package channel_mic_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	protocol "golang.52tt.com/pkg/protocol"
	channel_mic "golang.52tt.com/protocol/services/channel-mic"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// ApplyHoldMic mocks base method.
func (m *MockIClient) ApplyHoldMic(ctx context.Context, cid, uid uint32) (uint32, string, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyHoldMic", ctx, cid, uid)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// ApplyHoldMic indicates an expected call of ApplyHoldMic.
func (mr *MockIClientMockRecorder) ApplyHoldMic(ctx, cid, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyHoldMic", reflect.TypeOf((*MockIClient)(nil).ApplyHoldMic), ctx, cid, uid)
}

// ApplyHoldMicPreCheck mocks base method.
func (m *MockIClient) ApplyHoldMicPreCheck(ctx context.Context, cid, uid uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyHoldMicPreCheck", ctx, cid, uid)
	ret0, _ := ret[0].(error)
	return ret0
}

// ApplyHoldMicPreCheck indicates an expected call of ApplyHoldMicPreCheck.
func (mr *MockIClientMockRecorder) ApplyHoldMicPreCheck(ctx, cid, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyHoldMicPreCheck", reflect.TypeOf((*MockIClient)(nil).ApplyHoldMicPreCheck), ctx, cid, uid)
}

// ApplyMic mocks base method.
func (m *MockIClient) ApplyMic(ctx context.Context, req *channel_mic.ApplyHoldMicReq) (*channel_mic.ApplyHoldMicResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyMic", ctx, req)
	ret0, _ := ret[0].(*channel_mic.ApplyHoldMicResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ApplyMic indicates an expected call of ApplyMic.
func (mr *MockIClientMockRecorder) ApplyMic(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyMic", reflect.TypeOf((*MockIClient)(nil).ApplyMic), ctx, req)
}
