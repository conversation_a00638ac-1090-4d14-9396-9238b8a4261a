// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/ad-center/iclient.go

// Package ad_center is a generated GoMock package.
package ad_center

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	ad_center "golang.52tt.com/protocol/services/ad-center"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetAd mocks base method.
func (m *MockIClient) BatchGetAd(ctx context.Context, in *ad_center.BatchGetAdReq) (*ad_center.BatchGetAdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAd", ctx, in)
	ret0, _ := ret[0].(*ad_center.BatchGetAdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetAd indicates an expected call of BatchGetAd.
func (mr *MockIClientMockRecorder) BatchGetAd(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAd", reflect.TypeOf((*MockIClient)(nil).BatchGetAd), ctx, in)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CheckABTest mocks base method.
func (m *MockIClient) CheckABTest(ctx context.Context, tag string, uid uint32) (*ad_center.CheckABTestResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckABTest", ctx, tag, uid)
	ret0, _ := ret[0].(*ad_center.CheckABTestResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckABTest indicates an expected call of CheckABTest.
func (mr *MockIClientMockRecorder) CheckABTest(ctx, tag, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckABTest", reflect.TypeOf((*MockIClient)(nil).CheckABTest), ctx, tag, uid)
}

// CheckTagIdMate mocks base method.
func (m *MockIClient) CheckTagIdMate(ctx context.Context, in *ad_center.CheckTagIdMateReq) (*ad_center.CheckTagIdMateResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckTagIdMate", ctx, in)
	ret0, _ := ret[0].(*ad_center.CheckTagIdMateResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckTagIdMate indicates an expected call of CheckTagIdMate.
func (mr *MockIClientMockRecorder) CheckTagIdMate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckTagIdMate", reflect.TypeOf((*MockIClient)(nil).CheckTagIdMate), ctx, in)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// CommitAdClick mocks base method.
func (m *MockIClient) CommitAdClick(ctx context.Context, in *ad_center.CommitAdClickReq) (*ad_center.CommitAdClickResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommitAdClick", ctx, in)
	ret0, _ := ret[0].(*ad_center.CommitAdClickResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CommitAdClick indicates an expected call of CommitAdClick.
func (mr *MockIClientMockRecorder) CommitAdClick(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommitAdClick", reflect.TypeOf((*MockIClient)(nil).CommitAdClick), ctx, in)
}

// CommitAdExposure mocks base method.
func (m *MockIClient) CommitAdExposure(ctx context.Context, in *ad_center.CommitAdExposureReq) (*ad_center.CommitAdExposureResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommitAdExposure", ctx, in)
	ret0, _ := ret[0].(*ad_center.CommitAdExposureResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CommitAdExposure indicates an expected call of CommitAdExposure.
func (mr *MockIClientMockRecorder) CommitAdExposure(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommitAdExposure", reflect.TypeOf((*MockIClient)(nil).CommitAdExposure), ctx, in)
}

// DiagnosisCampaign mocks base method.
func (m *MockIClient) DiagnosisCampaign(ctx context.Context, in *ad_center.DiagnosisCampaignReq) (*ad_center.DiagnosisCampaignResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DiagnosisCampaign", ctx, in)
	ret0, _ := ret[0].(*ad_center.DiagnosisCampaignResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DiagnosisCampaign indicates an expected call of DiagnosisCampaign.
func (mr *MockIClientMockRecorder) DiagnosisCampaign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DiagnosisCampaign", reflect.TypeOf((*MockIClient)(nil).DiagnosisCampaign), ctx, in)
}

// GeneralMsg mocks base method.
func (m *MockIClient) GeneralMsg(ctx context.Context, in *ad_center.GeneralMsgReq) (*ad_center.GeneralMsgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeneralMsg", ctx, in)
	ret0, _ := ret[0].(*ad_center.GeneralMsgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GeneralMsg indicates an expected call of GeneralMsg.
func (mr *MockIClientMockRecorder) GeneralMsg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneralMsg", reflect.TypeOf((*MockIClient)(nil).GeneralMsg), ctx, in)
}

// GetAd mocks base method.
func (m *MockIClient) GetAd(ctx context.Context, in *ad_center.GetAdReq) (*ad_center.GetAdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAd", ctx, in)
	ret0, _ := ret[0].(*ad_center.GetAdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAd indicates an expected call of GetAd.
func (mr *MockIClientMockRecorder) GetAd(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAd", reflect.TypeOf((*MockIClient)(nil).GetAd), ctx, in)
}

// GetCampaign mocks base method.
func (m *MockIClient) GetCampaign(ctx context.Context, in *ad_center.GetCampaignReq) (*ad_center.GetCampaignResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCampaign", ctx, in)
	ret0, _ := ret[0].(*ad_center.GetCampaignResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCampaign indicates an expected call of GetCampaign.
func (mr *MockIClientMockRecorder) GetCampaign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCampaign", reflect.TypeOf((*MockIClient)(nil).GetCampaign), ctx, in)
}

// GetFilter mocks base method.
func (m *MockIClient) GetFilter(ctx context.Context, in *ad_center.GetFilterReq) (*ad_center.GetFilterResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFilter", ctx, in)
	ret0, _ := ret[0].(*ad_center.GetFilterResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFilter indicates an expected call of GetFilter.
func (mr *MockIClientMockRecorder) GetFilter(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilter", reflect.TypeOf((*MockIClient)(nil).GetFilter), ctx, in)
}

// SearchAds mocks base method.
func (m *MockIClient) SearchAds(ctx context.Context, in *ad_center.SearchAdsReq) (*ad_center.SearchAdsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAds", ctx, in)
	ret0, _ := ret[0].(*ad_center.SearchAdsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SearchAds indicates an expected call of SearchAds.
func (mr *MockIClientMockRecorder) SearchAds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAds", reflect.TypeOf((*MockIClient)(nil).SearchAds), ctx, in)
}

// SearchCampaigns mocks base method.
func (m *MockIClient) SearchCampaigns(ctx context.Context, in *ad_center.SearchCampaignsReq) (*ad_center.SearchCampaignsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchCampaigns", ctx, in)
	ret0, _ := ret[0].(*ad_center.SearchCampaignsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SearchCampaigns indicates an expected call of SearchCampaigns.
func (mr *MockIClientMockRecorder) SearchCampaigns(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchCampaigns", reflect.TypeOf((*MockIClient)(nil).SearchCampaigns), ctx, in)
}

// SearchFilters mocks base method.
func (m *MockIClient) SearchFilters(ctx context.Context, in *ad_center.SearchFiltersReq) (*ad_center.SearchFiltersResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchFilters", ctx, in)
	ret0, _ := ret[0].(*ad_center.SearchFiltersResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SearchFilters indicates an expected call of SearchFilters.
func (mr *MockIClientMockRecorder) SearchFilters(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchFilters", reflect.TypeOf((*MockIClient)(nil).SearchFilters), ctx, in)
}

// SetABTestInfo mocks base method.
func (m *MockIClient) SetABTestInfo(ctx context.Context, in *ad_center.SetABTestInfoReq) (*ad_center.SetABTestInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetABTestInfo", ctx, in)
	ret0, _ := ret[0].(*ad_center.SetABTestInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetABTestInfo indicates an expected call of SetABTestInfo.
func (mr *MockIClientMockRecorder) SetABTestInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetABTestInfo", reflect.TypeOf((*MockIClient)(nil).SetABTestInfo), ctx, in)
}

// SetAd mocks base method.
func (m *MockIClient) SetAd(ctx context.Context, in *ad_center.SetAdReq) (*ad_center.SetAdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAd", ctx, in)
	ret0, _ := ret[0].(*ad_center.SetAdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetAd indicates an expected call of SetAd.
func (mr *MockIClientMockRecorder) SetAd(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAd", reflect.TypeOf((*MockIClient)(nil).SetAd), ctx, in)
}

// SetCampaign mocks base method.
func (m *MockIClient) SetCampaign(ctx context.Context, in *ad_center.SetCampaignReq) (*ad_center.SetCampaignResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCampaign", ctx, in)
	ret0, _ := ret[0].(*ad_center.SetCampaignResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetCampaign indicates an expected call of SetCampaign.
func (mr *MockIClientMockRecorder) SetCampaign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCampaign", reflect.TypeOf((*MockIClient)(nil).SetCampaign), ctx, in)
}

// SetFilter mocks base method.
func (m *MockIClient) SetFilter(ctx context.Context, in *ad_center.SetFilterReq) (*ad_center.SetFilterResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFilter", ctx, in)
	ret0, _ := ret[0].(*ad_center.SetFilterResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetFilter indicates an expected call of SetFilter.
func (mr *MockIClientMockRecorder) SetFilter(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFilter", reflect.TypeOf((*MockIClient)(nil).SetFilter), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
