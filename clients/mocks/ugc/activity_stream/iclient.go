// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/ugc/activity_stream (interfaces: IClient)

// Package activity_stream is a generated GoMock package.
package activity_stream

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	activity_stream "golang.52tt.com/clients/ugc/activity_stream"
	client "golang.52tt.com/pkg/client"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetFeed mocks base method.
func (m *MockIClient) GetFeed(arg0 context.Context, arg1, arg2 string) activity_stream.Feed {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFeed", arg0, arg1, arg2)
	ret0, _ := ret[0].(activity_stream.Feed)
	return ret0
}

// GetFeed indicates an expected call of GetFeed.
func (mr *MockIClientMockRecorder) GetFeed(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFeed", reflect.TypeOf((*MockIClient)(nil).GetFeed), arg0, arg1, arg2)
}

// GetFeeds mocks base method.
func (m *MockIClient) GetFeeds(arg0 context.Context, arg1 string, arg2 ...string) activity_stream.IFeeds {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFeeds", varargs...)
	ret0, _ := ret[0].(activity_stream.IFeeds)
	return ret0
}

// GetFeeds indicates an expected call of GetFeeds.
func (mr *MockIClientMockRecorder) GetFeeds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFeeds", reflect.TypeOf((*MockIClient)(nil).GetFeeds), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
