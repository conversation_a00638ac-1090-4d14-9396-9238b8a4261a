// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/ugc/interactive/iclient.go

// Package interactive is a generated GoMock package.
package interactive

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	interactive "golang.52tt.com/protocol/services/ugc/interactive"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddFollowCount mocks base method.
func (m *MockIClient) AddFollowCount(ctx context.Context, in *interactive.AddFollowCountReq, opts ...grpc.CallOption) (*interactive.AddFollowCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddFollowCount", varargs...)
	ret0, _ := ret[0].(*interactive.AddFollowCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddFollowCount indicates an expected call of AddFollowCount.
func (mr *MockIClientMockRecorder) AddFollowCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFollowCount", reflect.TypeOf((*MockIClient)(nil).AddFollowCount), varargs...)
}

// AddUnreadCounts mocks base method.
func (m *MockIClient) AddUnreadCounts(ctx context.Context, in *interactive.AddUnreadCountsReq, opts ...grpc.CallOption) protocol.ServerError {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddUnreadCounts", varargs...)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddUnreadCounts indicates an expected call of AddUnreadCounts.
func (mr *MockIClientMockRecorder) AddUnreadCounts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUnreadCounts", reflect.TypeOf((*MockIClient)(nil).AddUnreadCounts), varargs...)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// ClearNewFollowCount mocks base method.
func (m *MockIClient) ClearNewFollowCount(ctx context.Context, in *interactive.ClearNewFollowCountReq, opts ...grpc.CallOption) (*interactive.ClearNewFollowCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearNewFollowCount", varargs...)
	ret0, _ := ret[0].(*interactive.ClearNewFollowCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearNewFollowCount indicates an expected call of ClearNewFollowCount.
func (mr *MockIClientMockRecorder) ClearNewFollowCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearNewFollowCount", reflect.TypeOf((*MockIClient)(nil).ClearNewFollowCount), varargs...)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetInteractiveUinfo mocks base method.
func (m *MockIClient) GetInteractiveUinfo(ctx context.Context, in *interactive.GetUinfoReq, opts ...grpc.CallOption) (*interactive.GetUinfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInteractiveUinfo", varargs...)
	ret0, _ := ret[0].(*interactive.GetUinfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInteractiveUinfo indicates an expected call of GetInteractiveUinfo.
func (mr *MockIClientMockRecorder) GetInteractiveUinfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInteractiveUinfo", reflect.TypeOf((*MockIClient)(nil).GetInteractiveUinfo), varargs...)
}

// MarkRead mocks base method.
func (m *MockIClient) MarkRead(ctx context.Context, in *interactive.MarkReadReq, opts ...grpc.CallOption) (*interactive.MarkReadResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MarkRead", varargs...)
	ret0, _ := ret[0].(*interactive.MarkReadResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MarkRead indicates an expected call of MarkRead.
func (mr *MockIClientMockRecorder) MarkRead(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkRead", reflect.TypeOf((*MockIClient)(nil).MarkRead), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdateFollowingStreamInfo mocks base method.
func (m *MockIClient) UpdateFollowingStreamInfo(ctx context.Context, uidList []uint32, updateAt uint64, actors []uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFollowingStreamInfo", ctx, uidList, updateAt, actors)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// UpdateFollowingStreamInfo indicates an expected call of UpdateFollowingStreamInfo.
func (mr *MockIClientMockRecorder) UpdateFollowingStreamInfo(ctx, uidList, updateAt, actors interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFollowingStreamInfo", reflect.TypeOf((*MockIClient)(nil).UpdateFollowingStreamInfo), ctx, uidList, updateAt, actors)
}
