// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/ugc/subscribe_topic/iclient.go

// Package subscribe_topic is a generated GoMock package.
package subscribe_topic

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	subscribe_topic "golang.52tt.com/protocol/services/ugc/subscribe_topic"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatGetTopicViewCount mocks base method.
func (m *MockIClient) BatGetTopicViewCount(ctx context.Context, in *subscribe_topic.BatGetTopicViewCountReq, opts ...grpc.CallOption) (*subscribe_topic.BatGetTopicViewCountRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetTopicViewCount", varargs...)
	ret0, _ := ret[0].(*subscribe_topic.BatGetTopicViewCountRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatGetTopicViewCount indicates an expected call of BatGetTopicViewCount.
func (mr *MockIClientMockRecorder) BatGetTopicViewCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetTopicViewCount", reflect.TypeOf((*MockIClient)(nil).BatGetTopicViewCount), varargs...)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetTopicViewCount mocks base method.
func (m *MockIClient) GetTopicViewCount(ctx context.Context, in *subscribe_topic.GetTopicViewCountReq, opts ...grpc.CallOption) (*subscribe_topic.GetTopicViewCountRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTopicViewCount", varargs...)
	ret0, _ := ret[0].(*subscribe_topic.GetTopicViewCountRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetTopicViewCount indicates an expected call of GetTopicViewCount.
func (mr *MockIClientMockRecorder) GetTopicViewCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopicViewCount", reflect.TypeOf((*MockIClient)(nil).GetTopicViewCount), varargs...)
}

// HadSubscribeTopic mocks base method.
func (m *MockIClient) HadSubscribeTopic(ctx context.Context, in *subscribe_topic.SubscribeReq, opts ...grpc.CallOption) (*subscribe_topic.HadSubscribeTopicResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HadSubscribeTopic", varargs...)
	ret0, _ := ret[0].(*subscribe_topic.HadSubscribeTopicResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// HadSubscribeTopic indicates an expected call of HadSubscribeTopic.
func (mr *MockIClientMockRecorder) HadSubscribeTopic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HadSubscribeTopic", reflect.TypeOf((*MockIClient)(nil).HadSubscribeTopic), varargs...)
}

// ListRecentViewTopic mocks base method.
func (m *MockIClient) ListRecentViewTopic(ctx context.Context, in *subscribe_topic.ListRecentViewTopicReq, opts ...grpc.CallOption) (*subscribe_topic.ListRecentViewTopicRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListRecentViewTopic", varargs...)
	ret0, _ := ret[0].(*subscribe_topic.ListRecentViewTopicRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListRecentViewTopic indicates an expected call of ListRecentViewTopic.
func (mr *MockIClientMockRecorder) ListRecentViewTopic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRecentViewTopic", reflect.TypeOf((*MockIClient)(nil).ListRecentViewTopic), varargs...)
}

// ListSubscribeTopic mocks base method.
func (m *MockIClient) ListSubscribeTopic(ctx context.Context, in *subscribe_topic.ListSubscribeReq, opts ...grpc.CallOption) (*subscribe_topic.ListSubscribeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListSubscribeTopic", varargs...)
	ret0, _ := ret[0].(*subscribe_topic.ListSubscribeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSubscribeTopic indicates an expected call of ListSubscribeTopic.
func (mr *MockIClientMockRecorder) ListSubscribeTopic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSubscribeTopic", reflect.TypeOf((*MockIClient)(nil).ListSubscribeTopic), varargs...)
}

// ReportRecentViewTopic mocks base method.
func (m *MockIClient) ReportRecentViewTopic(ctx context.Context, in *subscribe_topic.ReportRecentViewTopicReq, opts ...grpc.CallOption) (*subscribe_topic.ReportRecentViewTopicRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReportRecentViewTopic", varargs...)
	ret0, _ := ret[0].(*subscribe_topic.ReportRecentViewTopicRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ReportRecentViewTopic indicates an expected call of ReportRecentViewTopic.
func (mr *MockIClientMockRecorder) ReportRecentViewTopic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportRecentViewTopic", reflect.TypeOf((*MockIClient)(nil).ReportRecentViewTopic), varargs...)
}

// ReportTopicViewCount mocks base method.
func (m *MockIClient) ReportTopicViewCount(ctx context.Context, in *subscribe_topic.ReportTopicViewCountReq, opts ...grpc.CallOption) (*subscribe_topic.ReportTopicViewCountRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReportTopicViewCount", varargs...)
	ret0, _ := ret[0].(*subscribe_topic.ReportTopicViewCountRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ReportTopicViewCount indicates an expected call of ReportTopicViewCount.
func (mr *MockIClientMockRecorder) ReportTopicViewCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportTopicViewCount", reflect.TypeOf((*MockIClient)(nil).ReportTopicViewCount), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// SubscribeTopic mocks base method.
func (m *MockIClient) SubscribeTopic(ctx context.Context, in *subscribe_topic.SubscribeReq, opts ...grpc.CallOption) (*subscribe_topic.SubscribeTopicResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubscribeTopic", varargs...)
	ret0, _ := ret[0].(*subscribe_topic.SubscribeTopicResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SubscribeTopic indicates an expected call of SubscribeTopic.
func (mr *MockIClientMockRecorder) SubscribeTopic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubscribeTopic", reflect.TypeOf((*MockIClient)(nil).SubscribeTopic), varargs...)
}

// UnsubscribeTopic mocks base method.
func (m *MockIClient) UnsubscribeTopic(ctx context.Context, in *subscribe_topic.SubscribeReq, opts ...grpc.CallOption) (*subscribe_topic.SubscribeTopicResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnsubscribeTopic", varargs...)
	ret0, _ := ret[0].(*subscribe_topic.SubscribeTopicResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UnsubscribeTopic indicates an expected call of UnsubscribeTopic.
func (mr *MockIClientMockRecorder) UnsubscribeTopic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnsubscribeTopic", reflect.TypeOf((*MockIClient)(nil).UnsubscribeTopic), varargs...)
}
