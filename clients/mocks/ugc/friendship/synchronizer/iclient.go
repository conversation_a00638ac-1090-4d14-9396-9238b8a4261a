// Code generated by MockGen. DO NOT EDIT.
// Source: E:\quicksilver-another\clients\ugc\friendship\synchronizer\iclient.go

// Package friendship_synchronizer is a generated GoMock package.
package friendship_synchronizer

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	friendship_synchronizer "golang.52tt.com/clients/ugc/friendship/synchronizer"
	protocol "golang.52tt.com/pkg/protocol"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetSynchronizedSequence mocks base method.
func (m *MockIClient) GetSynchronizedSequence(ctx context.Context, userID uint32) (uint64, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSynchronizedSequence", ctx, userID)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSynchronizedSequence indicates an expected call of GetSynchronizedSequence.
func (mr *MockIClientMockRecorder) GetSynchronizedSequence(ctx, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSynchronizedSequence", reflect.TypeOf((*MockIClient)(nil).GetSynchronizedSequence), ctx, userID)
}

// LoadUGCSyncCheckPoint mocks base method.
func (m *MockIClient) LoadUGCSyncCheckPoint(ctx context.Context, userID uint32) (uint64, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoadUGCSyncCheckPoint", ctx, userID)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// LoadUGCSyncCheckPoint indicates an expected call of LoadUGCSyncCheckPoint.
func (mr *MockIClientMockRecorder) LoadUGCSyncCheckPoint(ctx, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadUGCSyncCheckPoint", reflect.TypeOf((*MockIClient)(nil).LoadUGCSyncCheckPoint), ctx, userID)
}

// SaveUGCSyncCheckPoint mocks base method.
func (m *MockIClient) SaveUGCSyncCheckPoint(ctx context.Context, userID uint32, checkpoint uint64) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveUGCSyncCheckPoint", ctx, userID, checkpoint)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// SaveUGCSyncCheckPoint indicates an expected call of SaveUGCSyncCheckPoint.
func (mr *MockIClientMockRecorder) SaveUGCSyncCheckPoint(ctx, userID, checkpoint interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUGCSyncCheckPoint", reflect.TypeOf((*MockIClient)(nil).SaveUGCSyncCheckPoint), ctx, userID, checkpoint)
}

// SyncFromFriendList mocks base method.
func (m *MockIClient) SyncFromFriendList(ctx context.Context, userID uint32, friendList []*friendship_synchronizer.Friend, maxUgcSequence uint64, addReverseSequenceZeroFollowing bool) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncFromFriendList", ctx, userID, friendList, maxUgcSequence, addReverseSequenceZeroFollowing)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// SyncFromFriendList indicates an expected call of SyncFromFriendList.
func (mr *MockIClientMockRecorder) SyncFromFriendList(ctx, userID, friendList, maxUgcSequence, addReverseSequenceZeroFollowing interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncFromFriendList", reflect.TypeOf((*MockIClient)(nil).SyncFromFriendList), ctx, userID, friendList, maxUgcSequence, addReverseSequenceZeroFollowing)
}
