package superplayerdress

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/superplayerdress"
	"google.golang.org/grpc"
)

const (
	serviceName = "super-player-dress"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewSuperPlayerDressClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.SuperPlayerDressClient { return c.Stub().(pb.SuperPlayerDressClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetDressConfigMaxVersion(ctx context.Context, version int64, dressType uint32) (*pb.GetDressConfigMaxVersionResp, protocol.ServerError) {
	req := &pb.GetDressConfigMaxVersionReq{
		MaxVersion: version,
		DressType:  pb.DressType(dressType),
	}
	resp, err := c.typedStub().GetDressConfigMaxVersion(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetDressConfigList(ctx context.Context, dressType, offset, limit uint32) (*pb.GetDressConfigListResp, protocol.ServerError) {
	req := &pb.GetDressConfigListReq{
		DressType: pb.DressType(dressType),
		Offset:    offset,
		Limit:     limit,
	}
	resp, err := c.typedStub().GetDressConfigList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetDressInUse(ctx context.Context, id int64, dressType, level uint32) (*pb.GetDressInUseResp, protocol.ServerError) {
	req := &pb.GetDressInUseReq{
		DressType: pb.DressType(dressType),
		Id:        id,
		Level:     level,
	}
	resp, err := c.typedStub().GetDressInUse(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetDressInUse(ctx context.Context, id int64, dressId, dressType, level uint32) (*pb.SetDressInUseResp, protocol.ServerError) {
	req := &pb.SetDressInUseReq{
		DressType: pb.DressType(dressType),
		Id:        id,
		Level:     level,
		DressId:   dressId,
	}
	resp, err := c.typedStub().SetDressInUse(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetWebDressConfig(ctx context.Context, dressType uint32) (*pb.GetWebDressConfigResp, protocol.ServerError) {
	req := &pb.GetWebDressConfigReq{
		DressType: pb.DressType(dressType),
	}
	resp, err := c.typedStub().GetWebDressConfig(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChatBgDressList(ctx context.Context, uid, level uint32) (*pb.GetUserCurrChatBgDressIdListResp, protocol.ServerError) {
	req := &pb.GetUserCurrChatBgDressIdListReq{
		Uid:   uid,
		Level: level,
	}
	resp, err := c.typedStub().GetUserCurrChatBgDressIdList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChatBgSpecialDressInUse(ctx context.Context, uid, level, dressID uint32, toAccount, replaceAccount string) (*pb.SetChatBgSpecialDressInUseResp, protocol.ServerError) {
	req := &pb.SetChatBgSpecialDressInUseReq{
		Id:             int64(uid),
		Level:          level,
		ToAccount:      toAccount,
		ReplaceAccount: replaceAccount,
		DressId:        dressID,
	}
	resp, err := c.typedStub().SetChatBgSpecialDressInUse(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetDefaultDressInUse(ctx context.Context, uid uint32) (*pb.SetDefaultDressInUseResp, protocol.ServerError) {
	req := &pb.SetDefaultDressInUseReq{
		Uid: int64(uid),
	}
	resp, err := c.typedStub().SetDefaultDressInUse(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChatBgSpecialDressInUse(ctx context.Context, uid, level uint32, account string) (uint32, protocol.ServerError) {
	req := &pb.GetUserCurrChatBgSpecialDressIdReq{
		Uid:     uid,
		Level:   level,
		Account: account,
	}
	resp, err := c.typedStub().GetUserCurrChatBgSpecialDressId(ctx, req)
	if nil != err {
		return 0, protocol.ToServerError(err)
	}
	return resp.DressId, protocol.ToServerError(err)
}

func (c *Client) GetUserDressHistory(ctx context.Context, uid, dressType, offset, limit uint32) (*pb.GetUserDressHistoryResp, protocol.ServerError) {
	req := &pb.GetUserDressHistoryReq{
		Uid:    uid,
		Type:   pb.DressType(dressType),
		Offset: offset,
		Limit:  limit,
	}
	resp, err := c.typedStub().GetUserDressHistory(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetDressUserExperienceInfo(ctx context.Context, uid uint32, dressType pb.DressType) (*pb.GetDressUserExperienceInfoResp, protocol.ServerError) {
	req := &pb.GetDressUserExperienceInfoReq{
		Uid:       uid,
		DressType: dressType,
	}
	resp, err := c.typedStub().GetDressUserExperienceInfo(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SendDressExperience(ctx context.Context, req *pb.SendDressExperienceReq) protocol.ServerError {
	_, err := c.typedStub().SendDressExperience(ctx, req)
	return protocol.ToServerError(err)
}
