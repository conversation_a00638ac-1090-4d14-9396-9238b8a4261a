package Timeline

import (
	"golang.52tt.com/pkg/protocol"
	timelinePB "golang.52tt.com/protocol/services/timeline-v2"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

type IClient interface {
	WriteTimelineMsg(ctx context.Context, id uint32, namespace string, msg *timelinePB.TimelineMsg) protocol.ServerError
	BatchWriteTimelineMsg(ctx context.Context, id uint32, namespace string, msgList []*timelinePB.TimelineMsg) protocol.ServerError
	BatchWriteTimelineMsgV2(ctx context.Context, in *timelinePB.BatchWriteTimelineMsgV2Req, opts ...grpc.CallOption) (*timelinePB.BatchWriteTimelineMsgV2Resp, protocol.ServerError)
	PullTimelineMsg(ctx context.Context, id uint32, namespace string, start, limit uint32) ([]*timelinePB.TimelineMsg, protocol.ServerError)
	PullTimelineMsgReverse(ctx context.Context, id uint32, namespace string, start, limit uint32) ([]*timelinePB.TimelineMsg, protocol.ServerError)
	WriteTimeline(ctx context.Context, in *timelinePB.WriteTimelineMsgReq, opts ...grpc.CallOption) (*timelinePB.WriteTimelineMsgResp, protocol.ServerError)
	BatchWriteTimeline(ctx context.Context, in *timelinePB.BatchWriteTimelineMsgReq, opts ...grpc.CallOption) (*timelinePB.BatchWriteTimelineMsgResp, protocol.ServerError)
	PullTimeline(ctx context.Context, in *timelinePB.PullTimelineMsgReq, opts ...grpc.CallOption) (*timelinePB.PullTimelineMsgResp, protocol.ServerError)
	DeleteTimelineMsg(ctx context.Context, in *timelinePB.DeleteTimelineMsgReq, opts ...grpc.CallOption) (*timelinePB.DeleteTimelineMsgResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
