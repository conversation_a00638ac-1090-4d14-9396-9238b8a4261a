// Code generated by protoc-gen-go-quicksilver. DO NOT EDIT.

package yswfukwdelay

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	protocol "golang.52tt.com/pkg/protocol"
	yswfukwdelay "golang.52tt.com/protocol/services/yswfukwdelay"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "yswf-ukw-delay"
)

type IClient interface {
	AddYswfUkwDelayData(ctx context.Context, req *yswfukwdelay.AddYswfUkwDelayDataReq, opts ...grpc.CallOption) (*yswfukwdelay.AddYswfUkwDelayDataResp, error)
}

// Client is the wrapper-client for YswfUkwDelay client.
type Client struct {
	client.BaseClient
}

func (c *Client) ManualRecover(ctx context.Context, in *yswfukwdelay.ManualRecoverReq, opts ...grpc.CallOption) (*yswfukwdelay.ManualRecoverResp, error) {
	_, err := c.typedStub().ManualRecover(ctx, &yswfukwdelay.ManualRecoverReq{Uid: in.GetUid()})
	return &yswfukwdelay.ManualRecoverResp{}, err
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return yswfukwdelay.NewYswfUkwDelayClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of YswfUkwDelayClient.
func (c *Client) typedStub() yswfukwdelay.YswfUkwDelayClient {
	return c.Stub().(yswfukwdelay.YswfUkwDelayClient)
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// NewClientOrg creates a new Client with wrapper-client instance.
func NewClientOrg(dopts ...grpc.DialOption) (yswfukwdelay.YswfUkwDelayClient, error) {
	c, err := NewClient(dopts...)
	if err != nil {
		return nil, err
	}
	return c, nil
}

// AddYswfUkwDelayData
func (c *Client) AddYswfUkwDelayData(ctx context.Context, req *yswfukwdelay.AddYswfUkwDelayDataReq, opts ...grpc.CallOption) (*yswfukwdelay.AddYswfUkwDelayDataResp, error) {
	resp, err := c.typedStub().AddYswfUkwDelayData(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}
