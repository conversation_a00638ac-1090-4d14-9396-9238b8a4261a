package security

import (
	"context"
	"strconv"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/security"
)

const (
	serviceName = "security"
)

const ( // AUDIT_STATUS
	AUDIT_NIL  = 0
	AUDIT_WAIT = 1 //待审核   (新的状态:等待注销)
	AUDIT_OK   = 2 //审核通过   (新的状态:注销成功)
	AUDIT_DENY = 3 //审核不通过  (新的流程不用这个状态了)
)

const ( // OP_SOURCE
	OP_FROM_NIL           = 0
	OP_FROM_TT            = 1
	OP_FROM_WEB           = 2
	OP_FROM_TACCOUNT      = 3
	OP_FROM_SDK           = 4
	OP_FROM_APPEAL_WEB    = 5 // 申诉系统后台
	OP_FROM_OPERATION_WEB = 6 // 运营后台
	OP_FROM_APPEAL_SVR    = 7 // 申诉系统
	OP_FROM_TESTTOOL      = 11
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewSecurityClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.SecurityClient { return c.Stub().(pb.SecurityClient) }

func (c *Client) GetSession(ctx context.Context, uid uint32, req *pb.GetSessionReq) (*pb.GetSessionRsp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	out, err := c.typedStub().GetSession(ctx, req)
	return out, err
}

func (c *Client) GetOperationLog(ctx context.Context, req *pb.GetOperationLogReq) (*pb.GetOperationLogRsp, error) {
	out, err := c.typedStub().GetOperationLog(ctx, req)
	return out, err
}

func (c *Client) GetUnregApplyAuditStatus(ctx context.Context, uid uint32) (uint32, error) {
	req := &pb.GetUnregApplyAuditStatusReq{
		Uid: uid,
	}
	out, err := c.typedStub().GetUnregApplyAuditStatus(ctx, req)
	return out.GetStatus(), err
}

func (c *Client) UnbindPhone(ctx context.Context, uid uint32, req *pb.UnbindPhoneReq) (*pb.UnbindPhoneRsp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	return c.typedStub().UnbindPhone(ctx, req)
}
