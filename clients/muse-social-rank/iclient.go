package muse_social_rank

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/muse-social-rank"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetSocialCommunityRankTopTabList(ctx context.Context, uid uint32) (*pb.GetSocialCommunityRankTopTabListResponse, protocol.ServerError)
	GetSocialCommunityRank(ctx context.Context, req *pb.GetSocialCommunityRankRequest) (*pb.GetSocialCommunityRankResponse, protocol.ServerError)
	GetSocialCommunityRankBrandChannelStatus(ctx context.Context, req *pb.GetSocialCommunityRankBrandChannelStatusRequest) (*pb.GetSocialCommunityRankBrandChannelStatusResponse, protocol.ServerError)
	OnRankCheck(ctx context.Context, uid uint32) (*pb.OnRankCheckResponse, protocol.ServerError)
	GetRankHonorSignByChannelIds(ctx context.Context, channelIdList []uint32) (*pb.GetRankHonorSignByChannelIdsResponse, protocol.ServerError)
	BatGetRankInChannel(ctx context.Context, channelIdList []uint32) (*pb.BatGetRankInChannelResponse, protocol.ServerError)
	GetRankHonorSignBySocialCommunityId(ctx context.Context, socialCommunityId string) (*pb.GetRankHonorSignBySocialCommunityIdResponse, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
