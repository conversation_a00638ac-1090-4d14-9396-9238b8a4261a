package star_trek

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/star-trek"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"
)

const (
	serviceName = "star-trek"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewStarTrekClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.StarTrekClient {
	return c.Stub().(pb.StarTrekClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

// 获取星际巡航信息
func (c *Client) GetStatTrekInfo(ctx context.Context, opUid uint32, req *pb.GetStatTrekInfoReq) (*pb.GetStatTrekInfoResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetStatTrekInfo(ctx, req)
	return resp, protocol.ToServerError(err)
}

// 获取全站、用户补给值变化
func (c *Client) GetSupplyValueChange(ctx context.Context, opUid uint32, req *pb.GetSupplyValueChangeReq) (*pb.GetSupplyValueChangeResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetSupplyValueChange(ctx, req)
	return resp, protocol.ToServerError(err)
}

// 去探险
func (c *Client) DoInvest(ctx context.Context, opUid uint32, req *pb.DoInvestReq) (*pb.DoInvestResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().DoInvest(ctx, req)
	return resp, protocol.ToServerError(err)
}

// 获取用户巡航记录
func (c *Client) GetMyTrekRecord(ctx context.Context, opUid uint32, req *pb.GetMyTrekRecordReq) (*pb.GetMyTrekRecordResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetMyTrekRecord(ctx, req)
	return resp, protocol.ToServerError(err)
}

// 往期回顾
func (c *Client) GetAllTrekHistory(ctx context.Context, opUid uint32, req *pb.GetAllTrekHistoryReq) (*pb.GetAllTrekHistoryResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetAllTrekHistory(ctx, req)
	return resp, protocol.ToServerError(err)
}

// 活动配置（包括时间、奖池）
func (c *Client) SetStarTrekConf(ctx context.Context, opUid uint32, req *pb.SetStarTrekConfReq) (*pb.SetStarTrekConfResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().SetStarTrekConf(ctx, req)
	return resp, protocol.ToServerError(err)
}

// 获取活动配置
func (c *Client) GetStarTrekConf(ctx context.Context, opUid uint32, req *pb.GetStarTrekConfReq) (*pb.GetStarTrekConfResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetStarTrekConf(ctx, req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetStarTrekConfById(ctx context.Context, opUid uint32, req *pb.GetStarTrekConfByIdReq) (*pb.GetStarTrekConfByIdResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetStarTrekConfById(ctx, req)
	return resp, protocol.ToServerError(err)
}

// 修改活动配置
func (c *Client) UpdateStarTrekConf(ctx context.Context, opUid uint32, req *pb.UpdateStarTrekConfReq) (*pb.UpdateStarTrekConfResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().UpdateStarTrekConf(ctx, req)
	return resp, protocol.ToServerError(err)
}

// 删除活动配置
func (c *Client) DelStarTrekConf(ctx context.Context, opUid uint32, req *pb.DelStarTrekConfReq) (*pb.DelStarTrekConfResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().DelStarTrekConf(ctx, req)
	return resp, protocol.ToServerError(err)
}

// 补给配置
func (c *Client) SetStarTrekSupply(ctx context.Context, opUid uint32, req *pb.SetStarTrekSupplyReq) (*pb.SetStarTrekSupplyResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().SetStarTrekSupply(ctx, req)
	return resp, protocol.ToServerError(err)
}

// 获取补给配置
func (c *Client) GetStarTrekSupply(ctx context.Context, opUid uint32, in *pb.GetStarTrekSupplyReq) (*pb.GetStarTrekSupplyResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetStarTrekSupply(ctx, &pb.GetStarTrekSupplyReq{})
	return resp, protocol.ToServerError(err)
}

// 删除补给配置
func (c *Client) DelStarTrekSupply(ctx context.Context, opUid uint32, req *pb.DelStarTrekSupplyReq) (*pb.DelStarTrekSupplyResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().DelStarTrekSupply(ctx, req)
	return resp, protocol.ToServerError(err)
}

// 判断入口可见性
func (c *Client) GetCurAtcTimeAndRoundTime(ctx context.Context, opUid uint32, req *pb.GetCurAtcTimeAndRoundTimeReq) (*pb.GetCurAtcTimeAndRoundTimeResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetCurAtcTimeAndRoundTime(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetStarTrekExemptValue(ctx context.Context, opUid uint32, req *pb.GetStarTrekExemptValueReq) (*pb.GetStarTrekExemptValueResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetStarTrekExemptValue(ctx, req)
	return resp, protocol.ToServerError(err)
}
