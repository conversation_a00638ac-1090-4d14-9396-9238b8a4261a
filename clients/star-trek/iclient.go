// Code generated by quicksilver-cli. DO NOT EDIT.
package star_trek

import (
	context "context"
	"golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/star-trek"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	DelStarTrekConf(ctx context.Context, opUid uint32, req *pb.DelStarTrekConfReq) (*pb.DelStarTrekConfResp, protocol.ServerError)
	DelStarTrekSupply(ctx context.Context, opUid uint32, req *pb.DelStarTrekSupplyReq) (*pb.DelStarTrekSupplyResp, protocol.ServerError)
	DoInvest(ctx context.Context, opUid uint32, req *pb.DoInvestReq) (*pb.DoInvestResp, protocol.ServerError)
	GetAllTrekHistory(ctx context.Context, opUid uint32, req *pb.GetAllTrekHistoryReq) (*pb.GetAllTrekHistoryResp, protocol.ServerError)
	GetCurAtcTimeAndRoundTime(ctx context.Context, opUid uint32, req *pb.GetCurAtcTimeAndRoundTimeReq) (*pb.GetCurAtcTimeAndRoundTimeResp, protocol.ServerError)
	GetMyTrekRecord(ctx context.Context, opUid uint32, req *pb.GetMyTrekRecordReq) (*pb.GetMyTrekRecordResp, protocol.ServerError)
	GetStarTrekConf(ctx context.Context, opUid uint32, req *pb.GetStarTrekConfReq) (*pb.GetStarTrekConfResp, protocol.ServerError)
	GetStarTrekConfById(ctx context.Context, opUid uint32, req *pb.GetStarTrekConfByIdReq) (*pb.GetStarTrekConfByIdResp, protocol.ServerError)
	GetStarTrekExemptValue(ctx context.Context, opUid uint32, req *pb.GetStarTrekExemptValueReq) (*pb.GetStarTrekExemptValueResp, protocol.ServerError)
	GetStarTrekSupply(ctx context.Context, opUid uint32, in *pb.GetStarTrekSupplyReq) (*pb.GetStarTrekSupplyResp, protocol.ServerError)
	GetStatTrekInfo(ctx context.Context, opUid uint32, req *pb.GetStatTrekInfoReq) (*pb.GetStatTrekInfoResp, protocol.ServerError)
	GetSupplyValueChange(ctx context.Context, opUid uint32, req *pb.GetSupplyValueChangeReq) (*pb.GetSupplyValueChangeResp, error)
	SetStarTrekConf(ctx context.Context, opUid uint32, req *pb.SetStarTrekConfReq) (*pb.SetStarTrekConfResp, protocol.ServerError)
	SetStarTrekSupply(ctx context.Context, opUid uint32, req *pb.SetStarTrekSupplyReq) (*pb.SetStarTrekSupplyResp, protocol.ServerError)
	UpdateStarTrekConf(ctx context.Context, opUid uint32, req *pb.UpdateStarTrekConfReq) (*pb.UpdateStarTrekConfResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
