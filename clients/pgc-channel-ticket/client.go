package pgc_channel_ticket

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/pgc-channel-ticket"
	"google.golang.org/grpc"
)

const (
	serviceName = "pgc-channel-ticket"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewPgcChannelTicketSvrClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.PgcChannelTicketSvrClient {
	return c.Stub().(pb.PgcChannelTicketSvrClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) SetTicketConf(ctx context.Context, req *pb.SetTicketConfReq) (*pb.SetTicketConfResp, protocol.ServerError) {
	resp, err := c.typedStub().SetTicketConf(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DelTicketConf(ctx context.Context, req *pb.DelTicketConfReq) (*pb.DelTicketConfResp, protocol.ServerError) {
	resp, err := c.typedStub().DelTicketConf(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CheckUserTicket(ctx context.Context, req *pb.CheckUserTicketReq) (*pb.CheckUserTicketResp, protocol.ServerError) {
	resp, err := c.typedStub().CheckUserTicket(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserTicketList(ctx context.Context, req *pb.GetUserTicketListReq) (*pb.GetUserTicketListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserTicketList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetTicketConfList(ctx context.Context) (*pb.GetTicketConfListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetTicketConfList(ctx, &pb.GetTicketConfListReq{})
	return resp, protocol.ToServerError(err)
}

func (c *Client) CheckTicketUsePer(ctx context.Context, req *pb.CheckTicketUsePerReq) (*pb.CheckTicketUsePerResp, protocol.ServerError) {
	resp, err := c.typedStub().CheckTicketUsePer(ctx, req)
	return resp, protocol.ToServerError(err)
}
