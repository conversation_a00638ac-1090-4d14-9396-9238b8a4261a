package muse_night_plan

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/muse-night-plan"
	"google.golang.org/grpc"
)

const (
	serviceName = "muse-night-plan"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewMuseNightPlanClient(cc)
		}, dopts...),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.MuseNightPlanClient {
	return c.Stub().(pb.MuseNightPlanClient)
}

func (c *Client) GetAppointChannelIds(ctx context.Context) (*pb.GetAppointChannelIdsResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAppointChannelIds(ctx, &pb.GetAppointChannelIdsReq{})
	return resp, protocol.ToServerError(err)
}

func (c *Client) UserClickPop(ctx context.Context, channelId, index, uid, optionId uint32, optionText string) (*pb.UserClickPopResp, protocol.ServerError) {
	resp, err := c.typedStub().UserClickPop(ctx, &pb.UserClickPopReq{ChannelId: channelId, Index: index, Uid: uid, Option: &pb.OptionInfo{OptionId: optionId, OptionText: optionText}})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetNightPlanWelcomePop(ctx context.Context, channelId, uid uint32) (*pb.GetNightPlanWelcomePopResp, protocol.ServerError) {
	resp, err := c.typedStub().GetNightPlanWelcomePop(ctx, &pb.GetNightPlanWelcomePopReq{ChannelId: channelId, Uid: uid})
	return resp, protocol.ToServerError(err)
}
