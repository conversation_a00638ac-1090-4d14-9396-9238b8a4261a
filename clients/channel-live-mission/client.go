package channel_live_mission

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/channel-live-mission"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-live-mission"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelLiveMissionClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelLiveMissionClient {
	return c.Stub().(pb.ChannelLiveMissionClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetUserMission(ctx context.Context, uid uint32) (*pb.GetUserMissionResp, protocol.ServerError) {
	req := &pb.GetUserMissionReq{
		Uid: uid,
	}

	resp, err := c.typedStub().GetUserMission(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HandleUserMission(ctx context.Context, uid, channelId, missionId, incrFinishCnt uint32) (*pb.HandleUserMissionResp, protocol.ServerError) {
	req := &pb.HandleUserMissionReq{
		Uid:           uid,
		MissionId:     missionId,
		ChannelId:     channelId,
		IncrFinishCnt: incrFinishCnt,
	}

	resp, err := c.typedStub().HandleUserMission(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HandleUserMissionWithAntiRepeatedMem(ctx context.Context, uid, channelId, missionId, incrFinishCnt, antiRepeatedMem uint32) (*pb.HandleUserMissionResp, protocol.ServerError) {
	req := &pb.HandleUserMissionReq{
		Uid:             uid,
		MissionId:       missionId,
		ChannelId:       channelId,
		IncrFinishCnt:   incrFinishCnt,
		AntiRepeatedMem: antiRepeatedMem,
	}

	resp, err := c.typedStub().HandleUserMission(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SwitchTimeUserMissionTs(ctx context.Context, uid uint32, switchStatus bool) (*pb.SwitchTimeUserMissionTsResp, protocol.ServerError) {
	req := &pb.SwitchTimeUserMissionTsReq{
		Uid:    uid,
		Switch: switchStatus,
	}

	resp, err := c.typedStub().SwitchTimeUserMissionTs(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFansMission(ctx context.Context, uid, actorUid, cid uint32) (*pb.GetFansMissionResp, protocol.ServerError) {
	req := &pb.GetFansMissionReq{
		Uid:       uid,
		ActorUid:  actorUid,
		ChannelId: cid,
	}

	resp, err := c.typedStub().GetFansMission(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HandleFansMission(ctx context.Context, uid, channelId, actorUid, missionId, incrCnt uint32) (*pb.HandleFansMissionResp, protocol.ServerError) {
	req := &pb.HandleFansMissionReq{
		Uid:           uid,
		ActorUid:      actorUid,
		ChannelId:     channelId,
		MissionId:     missionId,
		IncrFinishCnt: incrCnt,
	}

	resp, err := c.typedStub().HandleFansMission(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetActorMission(ctx context.Context, uid, channelId uint32) (*pb.GetActorMissionResp, protocol.ServerError) {
	req := &pb.GetActorMissionReq{
		Uid:       uid,
		ChannelId: channelId,
	}

	resp, err := c.typedStub().GetActorMission(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) IncrActorLiveTimeCnt(ctx context.Context, uid, channelId uint32, liveId uint64, isLiving bool) (*pb.IncrActorLiveTimeCntResp, protocol.ServerError) {
	req := &pb.IncrActorLiveTimeCntReq{
		Uid:           uid,
		ChannelId:     channelId,
		ChannelLiveId: liveId,
		IsLiving:      isLiving,
	}

	resp, err := c.typedStub().IncrActorLiveTimeCnt(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GrantLiveMissionAward(ctx context.Context, req *pb.GrantLiveMissionAwardReq) (*pb.GrantLiveMissionAwardResp, protocol.ServerError) {
	resp, err := c.typedStub().GrantLiveMissionAward(ctx, req)
	return resp, protocol.ToServerError(err)
}
