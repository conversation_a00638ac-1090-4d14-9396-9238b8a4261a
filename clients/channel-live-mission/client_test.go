package channel_live_mission

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/clients/public"
	"golang.52tt.com/pkg/protocol"
	aapb "golang.52tt.com/protocol/services/apicenter/apiserver"
	publicPB "golang.52tt.com/protocol/services/publicsvr"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
	"time"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

/*func TestGetHelloWorld(t *testing.T) {

	Convey("GetChannelLiveMission", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.ChannelLiveMissionReq
		resp, err := client.GetChannelLiveMission(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetChannelLiveMission %+v", resp)
	})

}*/

func TestClient_GetActorMission(t *testing.T) {
	Convey("GetActorMission", t, func() {
		client, err := NewClient(grpc.WithBlock())
		So(err, ShouldBeNil)
		resp, err := client.GetActorMission(context.Background(), 2204923, 10091342)
		So(err, ShouldBeNil)

		t.Logf("GetChannelLiveMission %+v", resp)
	})
}

func TestClient_IncrActorLiveTimeCnt(t *testing.T) {
	Convey("IncrActorLiveTimeCnt", t, func() {
		client, err := NewClient(grpc.WithBlock())
		So(err, ShouldBeNil)
		resp, err := client.IncrActorLiveTimeCnt(context.Background(), 2204586, 10091342, 0, false)
		So(err, ShouldBeNil)

		t.Logf("IncrActorLiveTimeCnt %+v", resp)
	})
}

func TestClient_HandleFansMission(t *testing.T) {
	Convey("HandleFansMission", t, func() {
		client, err := NewClient(grpc.WithBlock())
		So(err, ShouldBeNil)
		resp, err := client.HandleFansMission(context.Background(), 0, 0, 0, 203, 1)
		So(err, ShouldBeNil)

		t.Logf("HandleFansMission %+v", resp)
	})
}

func TestClient_GetFansMission(t *testing.T) {
	Convey("GetFansMission", t, func() {
		client, err := NewClient(grpc.WithBlock())
		So(err, ShouldBeNil)
		resp, err := client.GetFansMission(context.Background(), 0, 0, 0)
		So(err, ShouldBeNil)

		t.Logf("GetFansMission %+v", resp)
	})
}

func TestPushActorFuWuHao(t *testing.T) {
	publicClient := public.NewClient(grpc.WithBlock())
	apiCenterClient := apicenter.NewClient(grpc.WithBlock())
	ctx := context.Background()

	publicReq := &publicPB.GetPublicAccountByBindedIdReq{
		Type:     50,
		BindedId: 90004,
	}
	publicRsp, err := publicClient.GetPublicAccountByBindedId(ctx, 0, publicReq)
	if err != nil {
		t.Errorf("publicClient.GetPublicAccountByBindedId err:%s\n", err.Error())
	}

	toIDList := []uint32{
		2203052,
		2210427,
	}

	imContent := &aapb.ImContent{
		TextNormal: &aapb.ImTextNormal{
			Content: "test single push",
		},
	}
	imType := &aapb.ImType{
		SenderType:   uint32(aapb.IM_SENDER_TYPE_IM_SENDER_PUBLIC_ACCOUNT),
		ReceiverType: uint32(aapb.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
		ContentType:  uint32(aapb.IM_CONTENT_TYPE_IM_CONTENT_TEXT),
	}

	msg := &aapb.ImMsg{
		ImType:    imType,
		FromUid:   publicRsp.GetPublicAccount().GetPublicId(),
		ToIdList:  toIDList,
		ImContent: imContent,
		Platform:  aapb.Platform_UNSPECIFIED,
	}
	msgList := []*aapb.ImMsg{
		msg,
	}
	if serr := apiCenterClient.SendImMsg(ctx, 0, protocol.TT, msgList, true); serr != nil {
		t.Errorf("OpeApplication apiCenterClient.SendImMsg err: %s", serr.Error())
	}
}

func TestGenMonth(t *testing.T) {
	now := time.Date(2020, 8, 30, 23, 56, 0, 0, time.Local)
	if now.Hour() == 23 && now.Minute() > 55 && now.AddDate(0, 0, 1).Month() != now.Month() {
		// 生成主播积分月表
		t.Logf("***********")
	}
}
