package channel_live_mission

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-live-mission"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetUserMission(ctx context.Context, uid uint32) (*pb.GetUserMissionResp, protocol.ServerError)
	HandleUserMission(ctx context.Context, uid, channelId, missionId, incrFinishCnt uint32) (*pb.HandleUserMissionResp, protocol.ServerError)
	HandleUserMissionWithAntiRepeatedMem(ctx context.Context, uid, channelId, missionId, incrFinishCnt, antiRepeatedMem uint32) (*pb.HandleUserMissionResp, protocol.ServerError)
	SwitchTimeUserMissionTs(ctx context.Context, uid uint32, switchStatus bool) (*pb.SwitchTimeUserMissionTsResp, protocol.ServerError)
	GetFansMission(ctx context.Context, uid, actorUid, cid uint32) (*pb.GetFansMissionResp, protocol.ServerError)
	HandleFansMission(ctx context.Context, uid, channelId, actorUid, missionId, incrCnt uint32) (*pb.HandleFansMissionResp, protocol.ServerError)
	GetActorMission(ctx context.Context, uid, channelId uint32) (*pb.GetActorMissionResp, protocol.ServerError)
	IncrActorLiveTimeCnt(ctx context.Context, uid, channelId uint32, liveId uint64, isLiving bool) (*pb.IncrActorLiveTimeCntResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
