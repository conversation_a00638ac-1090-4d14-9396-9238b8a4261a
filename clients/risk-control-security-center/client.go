package risk_control_security_center

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/risk-control-security-center"
)

const (
	serviceName = "risk-control-security-center"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewRiskControlSecurityCenterClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.RiskControlSecurityCenterClient {
	return c.Stub().(pb.RiskControlSecurityCenterClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetAllSecurityCenterPost(ctx context.Context, searchTitle string,appId uint32) (*pb.GetAllSecurityCenterPostResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAllSecurityCenterPost(ctx, &pb.GetAllSecurityCenterPostReq{
		SearchText: searchTitle,
		AppId: appId,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAllConfigTab(ctx context.Context) (*pb.GetAllConfigTabResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAllConfigTab(ctx, &pb.GetAllConfigTabReq{})
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateSecurityCenterPost(ctx context.Context,req *pb.UpdateSecurityCenterPostReq) (*pb.UpdateSecurityCenterPostResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateSecurityCenterPost(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSecurityCenterPost(ctx context.Context,offset,limit uint32) (*pb.GetSecurityCenterPostResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSecurityCenterPost(ctx, &pb.GetSecurityCenterPostReq{
		Offset: offset,
		Limit: limit,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateConfigTab(ctx context.Context,req *pb.UpdateConfigTabReq) (*pb.UpdateConfigTabResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateConfigTab(ctx, req)
	return resp, protocol.ToServerError(err)
}