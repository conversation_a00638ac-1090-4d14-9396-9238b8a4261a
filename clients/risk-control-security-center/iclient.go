package risk_control_security_center

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/risk-control-security-center"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetAllSecurityCenterPost(ctx context.Context, searchTitle string,appId uint32) (*pb.GetAllSecurityCenterPostResp,
		protocol.ServerError)
	GetAllConfigTab(ctx context.Context) (*pb.GetAllConfigTabResp, protocol.ServerError)
	UpdateSecurityCenterPost(ctx context.Context,req *pb.UpdateSecurityCenterPostReq) (*pb.UpdateSecurityCenterPostResp,
		protocol.ServerError)
	GetSecurityCenterPost(ctx context.Context,offset,limit uint32) (*pb.GetSecurityCenterPostResp, protocol.ServerError)
	UpdateConfigTab(ctx context.Context,req *pb.UpdateConfigTabReq) (*pb.UpdateConfigTabResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}

