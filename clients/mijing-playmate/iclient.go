package mijing_playmate

import (
	"context"

	pb "golang.52tt.com/protocol/services/mijing-playmate"

	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	InvitedPiecingGroup(ctx context.Context, in *pb.InvitedPiecingGroupReq, opts ...grpc.CallOption) (*pb.InvitedPiecingGroupResp, error)
	PiecingGroupList(ctx context.Context, in *pb.PiecingGroupListReq, opts ...grpc.CallOption) (*pb.PiecingGroupListResp, error)
	SetPiecingGroupNotify(ctx context.Context, in *pb.SetPiecingGroupNotifyReq, opts ...grpc.CallOption) (*pb.SetPiecingGroupNotifyResp, error)
	GetPiecingGroupNotify(ctx context.Context, in *pb.GetPiecingGroupNotifyReq, opts ...grpc.CallOption) (*pb.GetPiecingGroupNotifyResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
