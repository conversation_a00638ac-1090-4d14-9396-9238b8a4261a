// Code generated by MockGen. DO NOT EDIT.
// Source: iclient.go

// Package mijing_playmate is a generated GoMock package.
package mijing_playmate

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	mijing_playmate "golang.52tt.com/protocol/services/mijing-playmate"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetPiecingGroupNotify mocks base method.
func (m *MockIClient) GetPiecingGroupNotify(ctx context.Context, in *mijing_playmate.GetPiecingGroupNotifyReq, opts ...grpc.CallOption) (*mijing_playmate.GetPiecingGroupNotifyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPiecingGroupNotify", varargs...)
	ret0, _ := ret[0].(*mijing_playmate.GetPiecingGroupNotifyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPiecingGroupNotify indicates an expected call of GetPiecingGroupNotify.
func (mr *MockIClientMockRecorder) GetPiecingGroupNotify(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPiecingGroupNotify", reflect.TypeOf((*MockIClient)(nil).GetPiecingGroupNotify), varargs...)
}

// InvitedPiecingGroup mocks base method.
func (m *MockIClient) InvitedPiecingGroup(ctx context.Context, in *mijing_playmate.InvitedPiecingGroupReq, opts ...grpc.CallOption) (*mijing_playmate.InvitedPiecingGroupResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InvitedPiecingGroup", varargs...)
	ret0, _ := ret[0].(*mijing_playmate.InvitedPiecingGroupResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InvitedPiecingGroup indicates an expected call of InvitedPiecingGroup.
func (mr *MockIClientMockRecorder) InvitedPiecingGroup(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InvitedPiecingGroup", reflect.TypeOf((*MockIClient)(nil).InvitedPiecingGroup), varargs...)
}

// PiecingGroupList mocks base method.
func (m *MockIClient) PiecingGroupList(ctx context.Context, in *mijing_playmate.PiecingGroupListReq, opts ...grpc.CallOption) (*mijing_playmate.PiecingGroupListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiecingGroupList", varargs...)
	ret0, _ := ret[0].(*mijing_playmate.PiecingGroupListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiecingGroupList indicates an expected call of PiecingGroupList.
func (mr *MockIClientMockRecorder) PiecingGroupList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiecingGroupList", reflect.TypeOf((*MockIClient)(nil).PiecingGroupList), varargs...)
}

// SetPiecingGroupNotify mocks base method.
func (m *MockIClient) SetPiecingGroupNotify(ctx context.Context, in *mijing_playmate.SetPiecingGroupNotifyReq, opts ...grpc.CallOption) (*mijing_playmate.SetPiecingGroupNotifyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetPiecingGroupNotify", varargs...)
	ret0, _ := ret[0].(*mijing_playmate.SetPiecingGroupNotifyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPiecingGroupNotify indicates an expected call of SetPiecingGroupNotify.
func (mr *MockIClientMockRecorder) SetPiecingGroupNotify(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPiecingGroupNotify", reflect.TypeOf((*MockIClient)(nil).SetPiecingGroupNotify), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
