package present_week_card

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	present_week_card "golang.52tt.com/protocol/services/present-week-card"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "present-week-card"
)

// Client is the wrapper-client for PresentWeekCard client.
type Client struct {
	client.BaseClient
}

func (c *Client) ManualOnlineEvent(ctx context.Context, in *present_week_card.ManualOnlineEventRequest, opts ...grpc.CallOption) (*present_week_card.ManualOnlineEventResponse, error) {
	resp, err := c.typedStub().ManualOnlineEvent(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	resp, err := c.typedStub().GetAwardTotalCount(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	resp, err := c.typedStub().GetAwardOrderIds(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	resp, err := c.typedStub().GetConsumeTotalCount(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	resp, err := c.typedStub().GetConsumeOrderIds(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetWeekCardInfoLocal(ctx context.Context, in *present_week_card.GetWeekCardInfoLocalRequest, opts ...grpc.CallOption) (*present_week_card.GetWeekCardInfoLocalResponse, error) {
	resp, err := c.typedStub().GetWeekCardInfoLocal(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAllAwardInfo(ctx context.Context, in *present_week_card.GetAllAwardInfoReq, opts ...grpc.CallOption) (*present_week_card.GetAllAwardInfoResp, error) {
	resp, err := c.typedStub().GetAllAwardInfo(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return present_week_card.NewPresentWeekCardClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of CatCanteenClient.
func (c *Client) typedStub() present_week_card.PresentWeekCardClient {
	return c.Stub().(present_week_card.PresentWeekCardClient)
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// NewClientOrg creates a new Client with wrapper-client instance.
func NewClientOrg(dopts ...grpc.DialOption) (present_week_card.PresentWeekCardClient, error) {
	c, err := NewClient(dopts...)
	if err != nil {
		return nil, err
	}
	return c, nil
}

func (c *Client) GetPresentWeekCardInfo(ctx context.Context, in *present_week_card.GetPresentWeekCardInfoReq, opts ...grpc.CallOption) (*present_week_card.GetPresentWeekCardInfoResp, error) {
	resp, err := c.typedStub().GetPresentWeekCardInfo(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentWeekCardAccess(ctx context.Context, in *present_week_card.GetPresentWeekCardEntryReq, opts ...grpc.CallOption) (*present_week_card.GetPresentWeekCardEntryResp, error) {
	resp, err := c.typedStub().GetPresentWeekCardAccess(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BuyPresentWeekCard(ctx context.Context, in *present_week_card.BuyPresentWeekCardReq, opts ...grpc.CallOption) (*present_week_card.BuyPresentWeekCardResp, error) {
	resp, err := c.typedStub().BuyPresentWeekCard(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ReceivePresentWeekCardReward(ctx context.Context, in *present_week_card.ReceivePresentWeekCardRewardReq, opts ...grpc.CallOption) (*present_week_card.ReceivePresentWeekCardRewardResp, error) {
	resp, err := c.typedStub().ReceivePresentWeekCardReward(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentWeekCardConfig(ctx context.Context, in *present_week_card.GetPresentWeekCardConfigRequest, opts ...grpc.CallOption) (*present_week_card.GetPresentWeekCardConfigResponse, error) {
	resp, err := c.typedStub().GetPresentWeekCardConfig(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetPresentWeekCardConfig(ctx context.Context, in *present_week_card.SetPresentWeekCardInfoRequest, opts ...grpc.CallOption) (*present_week_card.SetPresentWeekCardInfoResponse, error) {
	resp, err := c.typedStub().SetPresentWeekCardConfig(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdatePresentWeekCardConfig(ctx context.Context, in *present_week_card.UpdatePresentWeekCardConfigRequest, opts ...grpc.CallOption) (*present_week_card.UpdatePresentWeekCardConfigResponse, error) {
	resp, err := c.typedStub().UpdatePresentWeekCardConfig(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DeletePresentWeekCardConfig(ctx context.Context, in *present_week_card.DeletePresentWeekCardConfigRequest, opts ...grpc.CallOption) (*present_week_card.DeletePresentWeekCardConfigResponse, error) {
	resp, err := c.typedStub().DeletePresentWeekCardConfig(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdatePresentWeekCardAwardInfo(ctx context.Context, in *present_week_card.UpdatePresentWeekCardAwardInfoRequest, opts ...grpc.CallOption) (*present_week_card.UpdatePresentWeekCardAwardInfoResponse, error) {
	resp, err := c.typedStub().UpdatePresentWeekCardAwardInfo(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentWeekCardAwardInfo(ctx context.Context, in *present_week_card.GetPresentWeekCardAwardInfoRequest, opts ...grpc.CallOption) (*present_week_card.GetPresentWeekCardAwardInfoResponse, error) {
	resp, err := c.typedStub().GetPresentWeekCardAwardInfo(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}
