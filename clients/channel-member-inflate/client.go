package channel_member_inflate

import (
	"context"
	"golang.52tt.com/pkg/log"
	channelPB "golang.52tt.com/protocol/app/channel"
	"strconv"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/channelmemberinflate"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-member-inflate"
)

type Client struct {
	client.BaseClient
	*LocalCache
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelMemberInflateClient(cc)
			}, dopts...,
		),
		LocalCache: NewLocalCache(),
	}, nil
}

func (c *Client) typedStub() pb.ChannelMemberInflateClient {
	return c.Stub().(pb.ChannelMemberInflateClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) CheckChannelMemberInflateChType(chType uint32) bool {
	if uint32(channelPB.ChannelType_USER_CHANNEL_TYPE) == chType {
		return true
	}
	return false
}

func (c *Client) BatGetUGCChannelMemberInflateSize(ctx context.Context, channelids []uint32) (*pb.BatGetUGCChannelMemberInflateSizeResp, protocol.ServerError) {
	sizes, err := c.batGetUGCChannelMemberInflate(ctx, channelids)
	return &pb.BatGetUGCChannelMemberInflateSizeResp{ChannelIdMemberSize: sizes}, protocol.ToServerError(err)
}

func (c *Client) batGetUGCChannelMemberInflate(ctx context.Context, channelids []uint32) (map[uint32]*pb.ChannelMemberInflateInfo, protocol.ServerError) {
	channelIdMemberSize := make(map[uint32]*pb.ChannelMemberInflateInfo)
	req := make([]uint32, 0)
	for _, v := range channelids {
		channelIdMemberSize[v] = &pb.ChannelMemberInflateInfo{}
		if ret, ok := c.LocalCache.Cache.Get(strconv.Itoa(int(v))); ok {
			if size, ok := ret.(*pb.ChannelMemberInflateInfo); ok {
				channelIdMemberSize[v] = size
			} else {
				req = append(req, v)
			}
		} else {
			req = append(req, v)
		}
	}
	resp, err := c.typedStub().BatGetUGCChannelMemberInflateSize(ctx, &pb.BatGetUGCChannelMemberInflateSizeReq{ChannelIdList: req})
	if err != nil {
		return channelIdMemberSize, protocol.ToServerError(err)
	}
	for k, v := range resp.GetChannelIdMemberSize() {
		channelIdMemberSize[k] = v
		c.LocalCache.Cache.SetDefault(strconv.Itoa(int(k)), v)
	}
	log.DebugWithCtx(ctx, "BatGetUGCChannelMemberInflateSize req:%+v,resp:%+v", channelids, channelIdMemberSize)
	return channelIdMemberSize, nil
}

func (c *Client) GetChannelRobotList(ctx context.Context, channelId uint32) ([]uint32, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelRobotList(ctx, &pb.GetChannelRobotListReq{ChannelId: channelId})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	log.DebugWithCtx(ctx, "GetChannelRobotList req:%+v,resp:%+v", channelId, resp)
	return resp.GetUidList(), nil
}
