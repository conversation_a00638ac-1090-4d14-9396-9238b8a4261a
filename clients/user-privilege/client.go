package userprivilege

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/userprivilege"
	"google.golang.org/grpc"
)

const (
	serviceName = "user-privilege"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewUserPrivilegeClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.UserPrivilegeClient { return c.Stub().(pb.UserPrivilegeClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetPrivilegeConfList(ctx context.Context, req pb.GetPrivilegeConfListReq) (*pb.GetPrivilegeConfListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetPrivilegeConfList(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GiveUserPrivilege(ctx context.Context, req pb.GiveUserPrivilegeReq) (*pb.GiveUserPrivilegeResp, protocol.ServerError) {
	resp, err := c.typedStub().GiveUserPrivilege(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserprivilege(ctx context.Context, req pb.GetUserprivilegeReq) (*pb.GetUserprivilegeResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserprivilege(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetUserprivilege(ctx context.Context, req pb.BatchGetUserprivilegeReq) (*pb.BatchGetUserprivilegeResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetUserprivilege(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetUserprivilegeList(ctx context.Context, req *pb.BatchGetUserprivilegeListReq) (*pb.BatchGetUserprivilegeListResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetUserprivilegeList(ctx, req)
	return resp, protocol.ToServerError(err)
}
