package userprivilege

import (

	//	. "github.com/smartystreets/goconvey/convey"

	"os"
	"testing"

	"google.golang.org/grpc/grpclog"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {
	/*
		<PERSON><PERSON>("GetUserPrivilege", t, func() {
			client, err := NewClient()
			So(err, ShouldBeNil)
			var req pb.UserPrivilegeReq
			resp, err := client.GetUserPrivilege(context.Background(), req)
			So(err, ShouldBeNil)

			t.Logf("GetUserPrivilege %+v", resp)
		})
	*/
}
