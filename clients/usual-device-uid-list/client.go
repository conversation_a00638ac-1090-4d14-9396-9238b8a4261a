package usual_device_uid_list

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/usual-device-uid-list"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

const (
	serviceName = "usual-device-uid-list"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewUsualDeviceUidListClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.UsualDeviceUidListClient {
	return c.Stub().(pb.UsualDeviceUidListClient)
}

func (c *Client) InAuthWhiteList(ctx context.Context, uid uint64) (bool, protocol.ServerError) {
	ret, err := c.typedStub().InAuthWhiteList(ctx, &pb.InAuthWhiteListReq{
		Uid: uid,
	})

	if err != nil {
		return false, protocol.ToServerError(err)
	}

	return ret.In, nil
}

func (c *Client) UpdateAuthWhiteList(ctx context.Context, in *pb.UpdateAuthWhiteListReq) protocol.ServerError {
	_, err := c.typedStub().UpdateAuthWhiteList(ctx, in)
	return protocol.ToServerError(err)
}
