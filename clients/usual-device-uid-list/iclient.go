// Code generated by quicksilver-cli. DO NOT EDIT.
package usual_device_uid_list

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "golang.org/x/net/context"
	pb "golang.52tt.com/protocol/services/usual-device-uid-list"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	InAuthWhiteList(ctx context.Context, uid uint64) (bool,protocol.ServerError)
	UpdateAuthWhiteList(ctx context.Context, in *pb.UpdateAuthWhiteListReq) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
