package masked_call_logic

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/app/masked-call"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	QueryMatchInfo(ctx context.Context, in *pb.QueryMatchInfoReq) (*pb.QueryMatchInfoResp, protocol.ServerError)
	StartMatch(ctx context.Context, in *pb.StartMatchReq) (*pb.StartMatchResp, protocol.ServerError)
	CancelMatch(ctx context.Context, in *pb.CancelMatchReq) (*pb.CancelMatchResp, protocol.ServerError)
	QueryCallInfo(ctx context.Context, in *pb.QueryCallInfoReq) (*pb.QueryCallInfoResp, protocol.ServerError)
	SetConnectStatus(ctx context.Context, in *pb.SetConnectStatusReq) (*pb.SetConnectStatusResp, protocol.ServerError)
	Unmask(ctx context.Context, in *pb.UnmaskReq) (*pb.UnmaskResp, protocol.ServerError)
	InviteUnmask(ctx context.Context, in *pb.InviteUnmaskReq) (*pb.InviteUnmaskResp, protocol.ServerError)
	Roll(ctx context.Context, in *pb.RollReq) (*pb.RollResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
