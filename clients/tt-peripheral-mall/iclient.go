// Code generated by quicksilver-cli. DO NOT EDIT.
package tt_peripheral_mall

import(
	"golang.52tt.com/pkg/client"
	context "context"
	context0 "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
	pb "golang.52tt.com/protocol/services/tt-peripheral-mall"
)

type IClient interface {
	client.BaseClient
	BatAddProduct(ctx context0.Context, in *pb.BatAddProductReq, opts ...grpc.CallOption) (*pb.BatAddProductResp,error)
	DeleteProduct(ctx context0.Context, in *pb.DeleteProductReq, opts ...grpc.CallOption) (*pb.DeleteProductResp,error)
	GetProductDetailInfo(ctx context.Context, in *pb.GetProductDetailInfoReq, opts ...grpc.CallOption) (*pb.GetProductDetailInfoResp,error)
	GetProductSimpleInfoList(ctx context.Context, in *pb.GetProductSimpleInfoListReq, opts ...grpc.CallOption) (*pb.GetProductSimpleInfoListResp,error)
	GetUserOrderAddr(ctx context0.Context, in *pb.GetUserOrderAddrReq, opts ...grpc.CallOption) (*pb.GetUserOrderAddrResp,error)
	GetUserOrderList(ctx context.Context, in *pb.GetUserOrderListReq, opts ...grpc.CallOption) (*pb.GetUserOrderListResp,error)
	OrderProduct(ctx context.Context, in *pb.OrderProductReq, opts ...grpc.CallOption) (*pb.OrderProductResp,error)
	PayRmbResultNotify(ctx context0.Context, in *pb.PayRmbResultNotifyReq, opts ...grpc.CallOption) (*pb.PayRmbResultNotifyResp,error)
	SetUserOrderAddr(ctx context0.Context, in *pb.SetUserOrderAddrReq, opts ...grpc.CallOption) (*pb.SetUserOrderAddrResp,error)
	UpdateProduct(ctx context0.Context, in *pb.UpdateProductReq, opts ...grpc.CallOption) (*pb.UpdateProductResp,error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
