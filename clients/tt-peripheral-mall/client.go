package tt_peripheral_mall

import (
	"context"
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/tt-peripheral-mall"
	context0 "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "tt-peripheral-mall"
)

// Client is the wrapper-client for CatCanteen client.
type Client struct {
	client.BaseClient
}

func (c *Client) GetUserOrderAddr(ctx context0.Context, in *pb.GetUserOrderAddrReq, opts ...grpc.CallOption) (*pb.GetUserOrderAddrResp, error) {
	resp, err := c.typedStub().GetUserOrderAddr(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetUserOrderAddr(ctx context0.Context, in *pb.SetUserOrderAddrReq, opts ...grpc.CallOption) (*pb.SetUserOrderAddrResp, error) {
	resp, err := c.typedStub().SetUserOrderAddr(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) PayRmbResultNotify(ctx context0.Context, in *pb.PayRmbResultNotifyReq, opts ...grpc.CallOption) (*pb.PayRmbResultNotifyResp, error) {
	resp, err := c.typedStub().PayRmbResultNotify(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatAddProduct(ctx context0.Context, in *pb.BatAddProductReq, opts ...grpc.CallOption) (*pb.BatAddProductResp, error) {
	resp, err := c.typedStub().BatAddProduct(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateProduct(ctx context0.Context, in *pb.UpdateProductReq, opts ...grpc.CallOption) (*pb.UpdateProductResp, error) {
	resp, err := c.typedStub().UpdateProduct(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DeleteProduct(ctx context0.Context, in *pb.DeleteProductReq, opts ...grpc.CallOption) (*pb.DeleteProductResp, error) {
	resp, err := c.typedStub().DeleteProduct(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewTTPeripheralMallClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of RevenueExtGameClient.
func (c *Client) typedStub() pb.TTPeripheralMallClient {
	return c.Stub().(pb.TTPeripheralMallClient)
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// NewClientOrg creates a new Client with wrapper-client instance.
func NewClientOrg(dopts ...grpc.DialOption) (pb.TTPeripheralMallClient, error) {
	c, err := NewClient(dopts...)
	if err != nil {
		return nil, err
	}
	return c, nil
}

func (c *Client) GetProductSimpleInfoList(ctx context.Context, in *pb.GetProductSimpleInfoListReq, opts ...grpc.CallOption) (*pb.GetProductSimpleInfoListResp, error) {
	resp, err := c.typedStub().GetProductSimpleInfoList(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetProductDetailInfo(ctx context.Context, in *pb.GetProductDetailInfoReq, opts ...grpc.CallOption) (*pb.GetProductDetailInfoResp, error) {
	resp, err := c.typedStub().GetProductDetailInfo(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) OrderProduct(ctx context.Context, in *pb.OrderProductReq, opts ...grpc.CallOption) (*pb.OrderProductResp, error) {
	resp, err := c.typedStub().OrderProduct(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserOrderList(ctx context.Context, in *pb.GetUserOrderListReq, opts ...grpc.CallOption) (*pb.GetUserOrderListResp, error) {
	resp, err := c.typedStub().GetUserOrderList(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}
