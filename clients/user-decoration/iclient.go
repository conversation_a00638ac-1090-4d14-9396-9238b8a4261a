// Code generated by quicksilver-cli. DO NOT EDIT.
package user_decoration

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/user-decoration"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	Adorn(ctx context.Context, uid uint32, typ pb.Type, id string, version string, fusionId string) protocol.ServerError
	BatchUpsertDecoration(ctx context.Context, req *pb.BatchUpsertDecorationReq) (*pb.BatchUpsertDecorationResp,protocol.ServerError)
	Current(ctx context.Context, uid uint32, typ pb.Type) (*pb.CurrentResp,protocol.ServerError)
	Decorations(ctx context.Context, typ pb.Type) ([]*pb.DecorationInfo,protocol.ServerError)
	DelDecoration(ctx context.Context, req *pb.DelDecorationReq) (*pb.DelDecorationResp,protocol.ServerError)
	GetDecorationById(ctx context.Context, req *pb.GetDecorationByIdReq) (*pb.GetDecorationByIdResp,protocol.ServerError)
	InsertDecoration(ctx context.Context, req *pb.InsertDecorationReq) (*pb.InsertDecorationResp,protocol.ServerError)
	Remove(ctx context.Context, uid uint32, typ pb.Type) protocol.ServerError
	Upsert(ctx context.Context, uid uint32, typ pb.Type, decId string, version string, timeOverlayType pb.TimeOverlayType, duration int64, orderId string) protocol.ServerError
	UpsertDec(ctx context.Context, req *pb.UpsertReq) protocol.ServerError
	UserDecorations(ctx context.Context, uid uint32, typ pb.Type) ([]*pb.DecInfo,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
