package channel_open_game_auth

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-open-game-auth"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetUid(ctx context.Context, in *pb.GetUidReq) (*pb.GetUidResp, protocol.ServerError)
	GetOpenid(ctx context.Context, in *pb.GetOpenidReq) (*pb.GetOpenidResp, protocol.ServerError)
	BatchGetOpenidByUid(ctx context.Context, in *pb.BatchGetOpenidByUidReq) (*pb.BatchGetOpenidByUidResp, protocol.ServerError)
	BatchGetUidByOpenid(ctx context.Context, in *pb.BatchGetUidByOpenidReq) (*pb.BatchGetUidByOpenidResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
