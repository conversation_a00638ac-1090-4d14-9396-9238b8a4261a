package channel_open_game_auth

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/foundation/utils"
	pb "golang.52tt.com/protocol/services/channel-open-game-auth"
	"google.golang.org/grpc"
	"testing"
)

func Test(t *testing.T) {
	client, _ := NewClient(grpc.WithBlock())
	resp, err := client.GetOpenid(context.Background(), &pb.GetOpenidReq{
		Uid:    2208646,
		GameId: 59,
		CpId:   4,
	})

	fmt.Println(utils.<PERSON><PERSON><PERSON>(resp), err)
}
