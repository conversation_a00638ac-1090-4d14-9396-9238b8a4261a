package channel_open_game_auth

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-open-game-auth"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-open-game-auth"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelOpenGameAuthClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelOpenGameAuthClient {
	return c.Stub().(pb.ChannelOpenGameAuthClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetUid(ctx context.Context, in *pb.GetUidReq) (*pb.GetUidResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUid(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetOpenid(ctx context.Context, in *pb.GetOpenidReq) (*pb.GetOpenidResp, protocol.ServerError) {
	resp, err := c.typedStub().GetOpenid(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetOpenidByUid(ctx context.Context, in *pb.BatchGetOpenidByUidReq) (*pb.BatchGetOpenidByUidResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetOpenidByUid(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetUidByOpenid(ctx context.Context, in *pb.BatchGetUidByOpenidReq) (*pb.BatchGetUidByOpenidResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetUidByOpenid(ctx, in)
	return resp, protocol.ToServerError(err)
}
