// Code generated by protoc-gen-go-quicksilver. DO NOT EDIT.

package channel_game_mutual

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	protocol "golang.52tt.com/pkg/protocol"
	channel_game_mutual "golang.52tt.com/protocol/services/channel-game-mutual"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "channel-game-mutual"
)

// Client is the wrapper-client for ChannelGameMutualCenter client.
type Client struct {
	client.BaseClient
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return channel_game_mutual.NewChannelGameMutualCenterClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of ChannelGameMutualCenterClient.
func (c *Client) typedStub() channel_game_mutual.ChannelGameMutualCenterClient {
	return c.Stub().(channel_game_mutual.ChannelGameMutualCenterClient)
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// NewClientOrg creates a new Client with wrapper-client instance.
func NewClientOrg(dopts ...grpc.DialOption) (channel_game_mutual.ChannelGameMutualCenterClient, error) {
	c, err := NewClient(dopts...)
	if err != nil {
		return nil, err
	}
	return c, nil
}

// Register
func (c *Client) Register(ctx context.Context, req *channel_game_mutual.RegisterRequest, opts ...grpc.CallOption) (*channel_game_mutual.RegisterRespone, error) {
	resp, err := c.typedStub().Register(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// UnRegister
func (c *Client) UnRegister(ctx context.Context, req *channel_game_mutual.UnregisterRequest, opts ...grpc.CallOption) (*channel_game_mutual.EmptyResponse, error) {
	resp, err := c.typedStub().UnRegister(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetCurGameId
func (c *Client) GetCurGameId(ctx context.Context, req *channel_game_mutual.GetCurGameIdRequest, opts ...grpc.CallOption) (*channel_game_mutual.GetCurGameIdResponse, error) {
	resp, err := c.typedStub().GetCurGameId(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetRelationByGameID
func (c *Client) GetRelationByGameID(ctx context.Context, req *channel_game_mutual.GetRelationByGameIDRequest, opts ...grpc.CallOption) (*channel_game_mutual.GetRelationByGameIDResponse, error) {
	resp, err := c.typedStub().GetRelationByGameID(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// CanOpenGame
func (c *Client) CanOpenGame(ctx context.Context, req *channel_game_mutual.CanOpenGameRequest, opts ...grpc.CallOption) (*channel_game_mutual.CanOpenGameResponse, error) {
	resp, err := c.typedStub().CanOpenGame(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}
