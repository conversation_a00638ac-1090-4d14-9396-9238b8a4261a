// Code generated by quicksilver-cli. DO NOT EDIT.
package channel_game_mutual

import(
	"golang.52tt.com/pkg/client"
	channel_game_mutual "golang.52tt.com/protocol/services/channel-game-mutual"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetCurGameId(ctx context.Context, req *channel_game_mutual.GetCurGameIdRequest, opts ...grpc.CallOption) (*channel_game_mutual.GetCurGameIdResponse,error)
	GetRelationByGameID(ctx context.Context, req *channel_game_mutual.GetRelationByGameIDRequest, opts ...grpc.CallOption) (*channel_game_mutual.GetRelationByGameIDResponse,error)
	Register(ctx context.Context, req *channel_game_mutual.RegisterRequest, opts ...grpc.CallOption) (*channel_game_mutual.RegisterRespone,error)
	UnRegister(ctx context.Context, req *channel_game_mutual.UnregisterRequest, opts ...grpc.CallOption) (*channel_game_mutual.EmptyResponse,error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
