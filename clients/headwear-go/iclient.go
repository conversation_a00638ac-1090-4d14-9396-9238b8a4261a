// Code generated by quicksilver-cli. DO NOT EDIT.
package headwear_go

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/headwear-go"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	ChangeDecorationCustomText(ctx context.Context, req *pb.ChangeDecorationCustomTextReq) (*pb.ChangeDecorationCustomTextResp,protocol.ServerError)
	GetHeadwearConfig(ctx context.Context, uid uint32, headwearId uint32) (*pb.HeadwearConfig,protocol.ServerError)
	GetHeadwearConfigAll(ctx context.Context) (*pb.GetHeadwearConfigAllResp,protocol.ServerError)
	GetUserHeadwear(ctx context.Context, uid uint32, req *pb.GetUserHeadwearReq) (*pb.GetUserHeadwearResp,error)
	GetUserHeadwearBySuite(ctx context.Context, uid uint32, req *pb.GetUserHeadwearBySuiteReq) (*pb.UserHeadwearInfo,error)
	GetUserHeadwearInUse(ctx context.Context, uid uint32) (*pb.GetUserHeadwearInUseResp,protocol.ServerError)
	GetUserHeadwearInUseList(ctx context.Context, uid []uint32) (*pb.GetUserHeadwearInUseListResp,protocol.ServerError)
	GiveHeadwearToUser(ctx context.Context, uid uint32, req *pb.GiveHeadwearToUserReq) protocol.ServerError
	SetUserHeadwearInUse(ctx context.Context, uid uint32, headwearId uint32) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
