package interaction_intimacy_logic

import (
	"context"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/app/interaction-intimacy"
	logic "golang.52tt.com/protocol/services/logicsvr-go/interaction-intimacy-logic"
	"google.golang.org/grpc"
)

const (
	serviceName = "interaction-intimacy-logic"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return logic.NewInteractionLogicClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() logic.InteractionLogicClient {
	return c.Stub().(logic.InteractionLogicClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

// BatchGetIntimacyInfo
func (c *Client) BatchGetIntimacyInfo(ctx context.Context, in *pb.BatchGetIntimacyInfoReq) (*pb.BatchGetIntimacyInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetIntimacyInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

// CheckReward
func (c *Client) CheckReward(ctx context.Context, in *pb.CheckRewardReq) (*pb.CheckRewardResp, protocol.ServerError) {
	resp, err := c.typedStub().CheckReward(ctx, in)
	return resp, protocol.ToServerError(err)
}
