package superplayermission

import (
	"context"
	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/super-player-mission"
	"google.golang.org/grpc"
)

const (
	serviceName = "super-player-mission"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewSuperPlayerMissionClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.SuperPlayerMissionClient {
	return c.Stub().(pb.SuperPlayerMissionClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetSuperPlayerMissionById(ctx context.Context, req *pb.GetSuperPlayerMissionByIdReq) (*pb.GetSuperPlayerMissionByIdResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSuperPlayerMissionById(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSuperPlayerMission(ctx context.Context, req *pb.GetSuperPlayerMissionReq) (*pb.GetSuperPlayerMissionResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSuperPlayerMission(ctx, req)
	return resp, protocol.ToServerError(err)
}
