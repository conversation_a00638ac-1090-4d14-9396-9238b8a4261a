package channel_core_shield

import (
	"context"

	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-core-shield"
)

const (
	serviceName = "channel-core-shield"
)

type Client struct {
	client.BaseClient
}

func NewClient(opts ...grpc.DialOption) (*Client, error) {
	return newClient(opts...)
}

func newClient(opts ...grpc.DialOption) (*Client, error) {
	opts = append(opts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelCoreShieldClient(cc)
			}, opts...,
			),
	}, nil
}

func (c *Client) typeStub() pb.ChannelCoreShieldClient {
	return c.Stub().(pb.ChannelCoreShieldClient)
}

func (c *Client) HoldMicDetect(ctx context.Context,
	req *pb.HoldMicDetectReq) (*pb.DetectResult, protocol.ServerError) {
	resp, err := c.typeStub().HoldMicDetect(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.Result, nil
}

func (c *Client) TakeHoldMicDetect(ctx context.Context,
	req *pb.TakeHoldMicDetectReq) (*pb.DetectResult, protocol.ServerError) {
	resp, err := c.typeStub().TakeHoldMicDetect(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.Result, nil
}

func (c *Client) ChannelEnterDetect(ctx context.Context,
	req *pb.ChannelEnterDetectReq) (*pb.DetectResult, protocol.ServerError) {
	resp, err := c.typeStub().ChannelEnterDetect(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.Result, nil
}
