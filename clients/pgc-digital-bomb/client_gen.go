// Code generated by protoc-gen-go-quicksilver. DO NOT EDIT.

package pgc_digital_bomb

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	protocol "golang.52tt.com/pkg/protocol"
	pgc_digital_bomb "golang.52tt.com/protocol/services/pgc-digital-bomb"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "pgc-digital-bomb"
)

// Client is the wrapper-client for PgcDigitalBomb client.
type Client struct {
	client.BaseClient
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pgc_digital_bomb.NewPgcDigitalBombClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of PgcDigitalBombClient.
func (c *Client) typedStub() pgc_digital_bomb.PgcDigitalBombClient {
	return c.Stub().(pgc_digital_bomb.PgcDigitalBombClient)
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// NewClientOrg creates a new Client with wrapper-client instance.
func NewClientOrg(dopts ...grpc.DialOption) (pgc_digital_bomb.PgcDigitalBombClient, error) {
	c, err := NewClient(dopts...)
	if err != nil {
		return nil, err
	}
	return c, nil
}

// SetDigitalBombPhase
func (c *Client) SetDigitalBombPhase(ctx context.Context, req *pgc_digital_bomb.SetDigitalBombPhaseReq, opts ...grpc.CallOption) (*pgc_digital_bomb.SetDigitalBombPhaseResp, error) {
	resp, err := c.typedStub().SetDigitalBombPhase(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetDigitalBombInfo
func (c *Client) GetDigitalBombInfo(ctx context.Context, req *pgc_digital_bomb.GetDigitalBombInfoReq, opts ...grpc.CallOption) (*pgc_digital_bomb.GetDigitalBombInfoResp, error) {
	resp, err := c.typedStub().GetDigitalBombInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// DigitalBombEnroll
func (c *Client) DigitalBombEnroll(ctx context.Context, req *pgc_digital_bomb.DigitalBombEnrollReq, opts ...grpc.CallOption) (*pgc_digital_bomb.DigitalBombEnrollResp, error) {
	resp, err := c.typedStub().DigitalBombEnroll(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// DigitalBombSelectGameUser
func (c *Client) DigitalBombSelectGameUser(ctx context.Context, req *pgc_digital_bomb.DigitalBombSelectGameUserReq, opts ...grpc.CallOption) (*pgc_digital_bomb.DigitalBombSelectGameUserResp, error) {
	resp, err := c.typedStub().DigitalBombSelectGameUser(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// DigitalBombSelectNumber
func (c *Client) DigitalBombSelectNumber(ctx context.Context, req *pgc_digital_bomb.DigitalBombSelectNumberReq, opts ...grpc.CallOption) (*pgc_digital_bomb.DigitalBombSelectNumberResp, error) {
	resp, err := c.typedStub().DigitalBombSelectNumber(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// BatchGetDigitalBombPhase
func (c *Client) BatchGetDigitalBombPhase(ctx context.Context, req *pgc_digital_bomb.BatchGetDigitalBombPhaseReq, opts ...grpc.CallOption) (*pgc_digital_bomb.BatchGetDigitalBombPhaseResp, error) {
	resp, err := c.typedStub().BatchGetDigitalBombPhase(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}
