package smssvr

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/sms/mo"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

const (
	serviceName = "sms-mo"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewSmsMoClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.SmsMoClient { return c.Stub().(pb.SmsMoClient) }

func (c *Client) GetProviderPhone(ctx context.Context) (string, protocol.ServerError) {
	res, err := c.typedStub().GetProviderPhone(ctx, &pb.GetProviderPhoneReq{}, grpc.WaitForReady(true))
	if err != nil {
		return "", protocol.ToServerError(err)
	}
	return res.GetPhone(), protocol.ToServerError(err)
}

func (c *Client) GetSmsContent(ctx context.Context, phone string) ([]*pb.SmsContentInfo, protocol.ServerError) {
	out := make([]*pb.SmsContentInfo, 0)
	res, err := c.typedStub().GetSmsContent(ctx, &pb.GetSmsContentReq{
		Phone: phone,
	}, grpc.WaitForReady(true))
	if err != nil {
		return out, protocol.ToServerError(err)
	}

	return res.GetContents(), protocol.ToServerError(err)
}
