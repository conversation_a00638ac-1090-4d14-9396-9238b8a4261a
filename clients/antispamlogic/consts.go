package antispamlogic

import (
	pb "golang.52tt.com/protocol/services/antispamlogic"
)

const (
	UgcFollow       = uint32(pb.UserBehaviorCheckReq_ugc_follow)
	UgcBeenFollowed = uint32(pb.UserBehaviorCheckReq_ugc_been_followed)
	UgcPost         = uint32(pb.UserBehaviorCheckReq_ugc_post)
	UgcCommentText  = uint32(pb.UserBehaviorCheckReq_ugc_comment_text)
	UgcCommentImage = uint32(pb.UserBehaviorCheckReq_ugc_comment_image)
	UgcAttitude     = uint32(pb.UserBehaviorCheckReq_ugc_attitude)

	GameMatch = uint32(pb.UserBehaviorCheckReq_game_match)
	NearUser  = uint32(pb.UserBehaviorCheckReq_near_user)

	HoldMic = uint32(pb.UserBehaviorCheckReq_hold_mic)

	BlackList = uint32(pb.UserBehaviorCheckReq_black_list)
)
