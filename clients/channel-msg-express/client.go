package channel_msg_express

import (
	"context"
	"os"
	"path/filepath"

	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	channelpb "golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/services/channel-msg-express"
)

const (
	serviceName = "channel-msg-express"
)

var callerServerName = "unknown"

func init() {
	if len(os.Args) > 0 {
		callerServerName = filepath.Base(os.Args[0])
	}
}

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelMsgExpressClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelMsgExpressClient {
	return c.Stub().(pb.ChannelMsgExpressClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) SendChannelMsg(ctx context.Context, msg *channelpb.ChannelMsg) protocol.ServerError {
	_, err := c.typedStub().SendChannelMsg(ctx, &pb.SendChannelMsgReq{
		Msg:        msg,
		ServerName: callerServerName,
	})
	return protocol.ToServerError(err)
}

func (c *Client) SendChannelBroadcastMsg(ctx context.Context, msg *channelpb.ChannelBroadcastMsg) protocol.ServerError {
	_, err := c.typedStub().SendChannelBroadcastMsg(ctx, &pb.SendChannelBroadcastMsgReq{
		Msg:        msg,
		ServerName: callerServerName,
	})
	return protocol.ToServerError(err)
}

func (c *Client) PushToUsers(ctx context.Context, req *pb.PushToUsersReq) protocol.ServerError {
	if len(req.ServerName) == 0 {
		req.ServerName = callerServerName
	}
	_, err := c.typedStub().PushToUsers(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) SetDuplicateRule(ctx context.Context, req *pb.SetDuplicateRuleReq) protocol.ServerError {
	_, err := c.typedStub().SetDuplicateRule(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) SendChannelMsgV2(ctx context.Context, req *pb.SendChannelMsgReq) protocol.ServerError {
	if len(req.ServerName) == 0 {
		req.ServerName = callerServerName
	}
	_, err := c.typedStub().SendChannelMsg(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) SendChannelBroadcastMsgV2(ctx context.Context, req *pb.SendChannelBroadcastMsgReq) protocol.ServerError {
	if len(req.ServerName) == 0 {
		req.ServerName = callerServerName
	}
	_, err := c.typedStub().SendChannelBroadcastMsg(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) PushToUsersWithReqId(ctx context.Context, req *pb.PushToUsersReq) (string, protocol.ServerError) {
	if len(req.ServerName) == 0 {
		req.ServerName = callerServerName
	}
	rsp, err := c.typedStub().PushToUsers(ctx, req)
	return rsp.GetRequestId(), protocol.ToServerError(err)
}

func (c *Client) SendChannelMsgWithReqId(ctx context.Context, req *pb.SendChannelMsgReq) (string, protocol.ServerError) {
	if len(req.ServerName) == 0 {
		req.ServerName = callerServerName
	}
	rsp, err := c.typedStub().SendChannelMsg(ctx, req)
	return rsp.GetRequestId(), protocol.ToServerError(err)
}

func (c *Client) SendChannelBroadcastMsgWithReqId(ctx context.Context,
	req *pb.SendChannelBroadcastMsgReq) (string, protocol.ServerError) {
	if len(req.ServerName) == 0 {
		req.ServerName = callerServerName
	}
	rsp, err := c.typedStub().SendChannelBroadcastMsg(ctx, req)
	return rsp.GetRequestId(), protocol.ToServerError(err)
}

func (c *Client) PushToUserMapWithReqId(ctx context.Context, req *pb.PushToUserMapReq) (string, protocol.ServerError) {
	if len(req.ServerName) == 0 {
		req.ServerName = callerServerName
	}
	rsp, err := c.typedStub().PushToUserMap(ctx, req)
	return rsp.GetRequestId(), protocol.ToServerError(err)
}
