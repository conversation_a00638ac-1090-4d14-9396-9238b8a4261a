// Code generated by protoc-gen-go-quicksilver. DO NOT EDIT.

package music_channel_push

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	protocol "golang.52tt.com/pkg/protocol"
	music_channel_push "golang.52tt.com/protocol/services/music-channel-push"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "music-channel-push"
)

// Client is the wrapper-client for MusicChannelPush client.
type Client struct {
	client.BaseClient
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return music_channel_push.NewMusicChannelPushClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of MusicChannelPushClient.
func (c *Client) typedStub() music_channel_push.MusicChannelPushClient {
	return c.Stub().(music_channel_push.MusicChannelPushClient)
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// NewClientOrg creates a new Client with wrapper-client instance.
func NewClientOrg(dopts ...grpc.DialOption) (music_channel_push.MusicChannelPushClient, error) {
	c, err := NewClient(dopts...)
	if err != nil {
		return nil, err
	}
	return c, nil
}

// Push
func (c *Client) Push(ctx context.Context, req *music_channel_push.PushReq, opts ...grpc.CallOption) (*music_channel_push.PushResp, error) {
	resp, err := c.typedStub().Push(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// Push
func (c *Client) PushSecondNew(ctx context.Context, req *music_channel_push.PushSecondNewReq, opts ...grpc.CallOption) (*music_channel_push.PushSecondNewResp, error) {
	resp, err := c.typedStub().PushSecondNew(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}
