package channelrecommend

import (
	"context"
	"fmt"
	"golang.52tt.com/protocol/services/channelrecommendsvr"
	"google.golang.org/grpc"
	"testing"
)

func TestClient_GetChannelInfoEx(t *testing.T) {
	client := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("channelrecommend.52tt.local"))

	exResp, err := client.GetChannelInfoEx(context.Background(), 2198153, &channelrecommend.GetChannelInfoExReq{ChannelIdList: []uint32{1000572}, ChannelType: 5})
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println(len(exResp.ChannelExList))
	fmt.Println(exResp.ChannelExList)

	batchResp, err := client.BatchGetChannelType(context.Background(), 2198153, &channelrecommend.BatchGetChannelTypeReq{ChannelIdList: []uint32{10001572}})
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(batchResp.ChannelTypeList)

	getResp, err := client.GetChannel(context.Background(), 2198153, &channelrecommend.GetChannelReq{ChannelType: 5})
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(getResp.ChannelIdList)

	getTagResp, err := client.GetChannelInfoTag(context.Background(), 2198153, &channelrecommend.GetChannelInfoTagReq{ChannelIdList: []uint32{1000157}})
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(getTagResp.ChannelInfotagList)
}
