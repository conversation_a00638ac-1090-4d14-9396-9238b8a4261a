package usergrant

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/usergrantsvr"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"
)

const (
	serviceName = "usergrant"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewUsergrantClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.UsergrantClient { return c.Stub().(pb.UsergrantClient) }

func (c *Client) SetUserGrant(ctx context.Context, req *pb.SetUserGrantReq) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(req.GetUid()))))
	_, err := c.typedStub().SetUserGrant(ctx, req)
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}
