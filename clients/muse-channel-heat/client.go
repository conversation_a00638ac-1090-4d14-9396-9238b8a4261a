package muse_channel_heat

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/muse-channel-heat"
	"google.golang.org/grpc"
)

const (
	serviceName = "muse-channel-heat"
)

type Client struct {
	client.BaseClient
}

func (c *Client) typedStub() pb.ChannelHeatClient {
	return c.Stub().(pb.ChannelHeatClient)
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewChannelHeatClient(cc)
		}, dopts...),
	}, nil
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) SetMainSwitchStatus(ctx context.Context, req *pb.SetMainSwitchStatusReq) (*pb.SetMainSwitchStatusResp, protocol.ServerError) {
	resp, err := c.typedStub().SetMainSwitchStatus(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) StartHeatChannel(ctx context.Context, channelID, tabId uint32) (*pb.StartHeatChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().StartHeatChannel(ctx, &pb.StartHeatChannelReq{ChannelId: channelID, TabId: tabId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) RefusalToHeat(ctx context.Context, channelID, tabId uint32) (*pb.RefusalToHeatResp, protocol.ServerError) {
	resp, err := c.typedStub().RefusalToHeat(ctx, &pb.RefusalToHeatReq{ChannelId: channelID, TabId: tabId})
	return resp, protocol.ToServerError(err)
}
