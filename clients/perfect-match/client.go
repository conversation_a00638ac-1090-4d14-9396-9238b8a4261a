// Code generated by protoc-gen-go-quicksilver. DO NOT EDIT.

package perfect_match

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	protocol "golang.52tt.com/pkg/protocol"
	perfect_match "golang.52tt.com/protocol/services/perfect-match"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "perfect-match"
)

// Client is the wrapper-client for PgcChannelPK client.
type Client struct {
	client.BaseClient
}

func (c *Client) GenFinancialFile(ctx context.Context, in *reconcile_v2.GenFinancialFileReq, opts ...grpc.CallOption) (*reconcile_v2.GenFinancialFileResp, error) {
	//TODO implement me
	panic("implement me")
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return perfect_match.NewPerfectMatchClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of PerfectMatchClient.
func (c *Client) typedStub() perfect_match.PerfectMatchClient {
	return c.Stub().(perfect_match.PerfectMatchClient)
}

func NewClientTo(target string, dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewGrpcClientTo(
			target,
			func(cc *grpc.ClientConn) interface{} {
				return perfect_match.NewPerfectMatchClient(cc)
			}, dopts...),
	}, nil
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// NewClientOrg creates a new Client with wrapper-client instance.
func NewClientOrg(dopts ...grpc.DialOption) (perfect_match.PerfectMatchClient, error) {
	c, err := NewClient(dopts...)
	if err != nil {
		return nil, err
	}
	return c, nil
}

// GetPerfectMatchEntry
func (c *Client) GetPerfectMatchEntry(ctx context.Context, req *perfect_match.GetPerfectMatchEntryReq, opts ...grpc.CallOption) (*perfect_match.GetPerfectMatchEntryResp, error) {
	resp, err := c.typedStub().GetPerfectMatchEntry(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) EnrollPerfectMatch(ctx context.Context, req *perfect_match.EnrollPerfectMatchReq, opts ...grpc.CallOption) (*perfect_match.EnrollPerfectMatchResp, error) {
	resp, err := c.typedStub().EnrollPerfectMatch(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPerfectMatchQuestions(ctx context.Context, req *perfect_match.GetPerfectMatchQuestionsReq, opts ...grpc.CallOption) (*perfect_match.GetPerfectMatchQuestionsResp, error) {
	resp, err := c.typedStub().GetPerfectMatchQuestions(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SendPerfectMatchHeartbeat(ctx context.Context, req *perfect_match.SendPerfectMatchHeartbeatReq, opts ...grpc.CallOption) (*perfect_match.SendPerfectMatchHeartbeatResp, error) {
	resp, err := c.typedStub().SendPerfectMatchHeartbeat(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SendPerfectMatchAnswer(ctx context.Context, req *perfect_match.SendPerfectMatchAnswerReq, opts ...grpc.CallOption) (*perfect_match.SendPerfectMatchAnswerResp, error) {
	resp, err := c.typedStub().SendPerfectMatchAnswer(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CancelPerfectMatch(ctx context.Context, req *perfect_match.CancelPerfectMatchReq, opts ...grpc.CallOption) (*perfect_match.CancelPerfectMatchResp, error) {
	resp, err := c.typedStub().CancelPerfectMatch(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelPerfectMatchStatus(ctx context.Context, req *perfect_match.SetChannelPerfectMatchStatusReq, opts ...grpc.CallOption) (*perfect_match.SetChannelPerfectMatchStatusResp, error) {
	resp, err := c.typedStub().SetChannelPerfectMatchStatus(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelPerfectMatchGameEnd(ctx context.Context, req *perfect_match.SetChannelPerfectMatchGameEndReq, opts ...grpc.CallOption) (*perfect_match.SetChannelPerfectMatchGameEndResp, error) {
	resp, err := c.typedStub().SetChannelPerfectMatchGameEnd(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// T豆消费数据对账
func (c *Client) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	resp, err := c.typedStub().GetConsumeTotalCount(ctx, in)
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	resp, err := c.typedStub().GetConsumeOrderIds(ctx, in)
	return resp, protocol.ToServerError(err)
}
