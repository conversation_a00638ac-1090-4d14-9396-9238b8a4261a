package account_appeal

import (
	"context"
	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/account-appeal"
	"google.golang.org/grpc"
)

const (
	serviceName = "account-appeal"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewAccountAppealClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.AccountAppealClient { return c.Stub().(pb.AccountAppealClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetAccountAppealInfo(ctx context.Context, req *pb.GetAccountAppealInfoReq) (*pb.GetAccountAppealInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAccountAppealInfo(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddAccountAppealApply(ctx context.Context, req *pb.AddAccountAppealApplyReq) protocol.ServerError {
	_, err := c.typedStub().AddAccountAppealApply(ctx, req, grpc.WaitForReady(true))
	return protocol.ToServerError(err)
}

func (c *Client) CheckIfCanAppeal(ctx context.Context, uid uint32) (bool, protocol.ServerError) {
	resp, err := c.typedStub().CheckIfCanAppeal(ctx, &pb.CheckIfCanAppealReq{Uid: uid})
	return resp.GetCanAppeal(), protocol.ToServerError(err)
}

func (c *Client) HandleAppeal(ctx context.Context, serialNumber, handler, reason string, isPass bool) protocol.ServerError {
	_, err := c.typedStub().HandleAppeal(ctx, &pb.HandleAppealReq{
		SerialNumber: serialNumber,
		Name:         handler,
		IsPass:       isPass,
		Reason:       reason,
	}, grpc.WaitForReady(true))
	return protocol.ToServerError(err)
}
