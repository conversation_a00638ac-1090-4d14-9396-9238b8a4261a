package account_appeal

import (
	"fmt"
	"golang.52tt.com/pkg/foundation/utils"
	pb "golang.52tt.com/protocol/services/account-appeal"
	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"
	"io"
	"os"
	"testing"
	"time"

	"github.com/opentracing/opentracing-go"
	"github.com/uber/jaeger-client-go/config"
	"golang.org/x/net/context"
	"google.golang.org/grpc/grpclog"
)

var (
	tracer opentracing.Tracer
	closer io.Closer
)

func init() {

	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))

	tracer, closer, _ = (&config.Configuration{
		Sampler: &config.SamplerConfig{
			Type:  "const",
			Param: 1,
		},
		Reporter: &config.ReporterConfig{
			LogSpans:            false,
			BufferFlushInterval: 1 * time.Second,
		},
	}).New("account_appeal_client_test")
}

func TestClient_GetAccountAppealInfo(t *testing.T) {
	client, _ := NewClient(grpc.WithKeepaliveParams(keepalive.ClientParameters{Time: 30 * time.Second, Timeout: 30 * time.Second}))
	resp, err := client.GetAccountAppealInfo(context.Background(), &pb.GetAccountAppealInfoReq{
		IsHandled: false,
	})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(utils.ToJson(resp))
	t.Logf("GetAccountAppealInfo:%+v", resp)

}
