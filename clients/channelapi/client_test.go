package channelapi

import (
	"context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func TestClient_SetMicMode(t *testing.T) {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
	client := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("channelapi.52tt.local"))
	resp, err := client.SetMicMode(context.Background(), 5200123,10007103,5, 0 ,2)
	if err != nil {
		t.<PERSON>rror(err)
	}
	t.Log("success", resp)

}
