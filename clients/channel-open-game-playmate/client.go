package channel_open_game_playmate

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-open-game-playmate"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-open-game-playmate"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelOpenGamePlaymateClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelOpenGamePlaymateClient {
	return c.Stub().(pb.ChannelOpenGamePlaymateClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetPlaymateList(ctx context.Context, in *pb.GetPlaymateListReq) (*pb.GetPlaymateListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetPlaymateList(ctx, in)
	return resp, protocol.ToServerError(err)
}
