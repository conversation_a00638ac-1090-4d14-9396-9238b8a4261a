package present_middleware

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	magic_spirit_logic "golang.52tt.com/protocol/app/magic-spirit-logic"
	"golang.52tt.com/protocol/app/userpresent"
	pb "golang.52tt.com/protocol/services/present-middleware"
	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
	"google.golang.org/grpc"
	"strconv"
	"testing"
	"time"
)

func TestClient_SendPresent(t *testing.T) {

	client, err := NewClient(grpc.WithBlock())
	if err != nil {
		fmt.Println("newclient err is :", err)
	}
	ctx := context.Background()
	in := &pb.SendPresentReq{
		TargetUid:   2202086,
		ChannelId:   2042425,
		ItemId:      2,
		Count:       1,
		SendSource:  1,
		ItemSource:  0,
		SourceId:    0,
		SendType:    0,
		AppId:       1,
		MarketId:    1,
		SendUid:     2202538,
		ServiceInfo: &pb.ServiceCtrlInfo{},
		IsOptValid:  true,
	}
	_, err = client.SendPresent(ctx, in)
	if err != nil {
		fmt.Println("SendPresent err is :", err)
	}
	return
}

func TestClient_ImSendPresent(t *testing.T) {

	client, err := NewClient(grpc.WithBlock())
	if err != nil {
		fmt.Println("newclient err is :", err)
	}
	ctx := context.Background()
	in := &pb.ImSendPresentReq{
		TargetUid:       2202086,
		ItemId:          2,
		Count:           1,
		SendSource:      1,
		ItemSource:      0,
		SourceId:        0,
		SendType:        0,
		AppId:           1,
		MarketId:        1,
		SendUid:         2202538,
		ServiceInfo:     &pb.ServiceCtrlInfo{},
		IsOptValid:      true,
		PresentTextType: uint32(userpresent.PresentTextType_E_TEXT_TYPE_INTIMACY),
		SendMethod:      uint32(pb.PresentSendMethodType_PRESENT_TYPE_IM),
	}
	_, err = client.ImSendPresent(ctx, in)
	if err != nil {
		fmt.Println("SendPresent err is :", err)
	}
	return
}

func TestClient_MagicSendPresent(t *testing.T) {

	client, err := NewClient(grpc.WithBlock())
	if err != nil {
		fmt.Println("newclient err is :", err)
	}
	ctx := context.Background()

	comboInfo := &magic_spirit_logic.CombInfo{
		ComboText: "",
		CombLevel: 3,
		LevelUp:   true,
		LevelName: "普天同庆",
		Duration:  0,
	}

	comboByte, _ := proto.Marshal(comboInfo)

	in := &pb.MagicSendPresentReq{
		ChannelId:       2042425,
		SendUid:         2202538,
		AppId:           1,
		MarketId:        1,
		ServiceInfo:     &pb.ServiceCtrlInfo{},
		ConsumeOrderId:  "test_" + time.Now().Format("2006-01-02 15:04:05"),
		IsBatch:         true,
		MagicSpiritId:   1,
		MagicSpiritCnt:  1,
		MagicSpiritIcon: "",
		MagicSpiritName: "幸运精灵",
		SendTime:        1638780898,
		GiftItemList: []*pb.GiftItem{
			{
				Uid:         2202086,
				ItemId:      3,
				Count:       5,
				OrderId:     "test_" + time.Now().Format("2006-01-02 15:04:05") + "_" + strconv.Itoa(2202086),
				AwardEffect: 4,
			},
			{
				Uid:         2202086,
				ItemId:      4,
				Count:       5,
				OrderId:     "test_" + time.Now().Format("2006-01-02 15:04:05") + "_" + strconv.Itoa(2404972) + "_3",
				AwardEffect: 3,
			},
			{
				Uid:         2402972,
				ItemId:      3,
				Count:       5,
				OrderId:     "test_" + time.Now().Format("2006-01-02 15:04:05") + "_" + strconv.Itoa(2404972) + "_3",
				AwardEffect: 4,
			},
			{
				Uid:         2402972,
				ItemId:      4,
				Count:       5,
				OrderId:     "test_" + time.Now().Format("2006-01-02 15:04:05") + "_" + strconv.Itoa(2404972) + "_4",
				AwardEffect: 3,
			},
		},
		CombConfig: comboByte,
	}
	out, err := client.MagicSendPresent(ctx, in)
	if err != nil {
		fmt.Println("SendPresent err is :", err)
	}
	fmt.Println(out)
	return
}
