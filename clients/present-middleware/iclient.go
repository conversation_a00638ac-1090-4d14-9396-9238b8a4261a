// Code generated by quicksilver-cli. DO NOT EDIT.
package present_middleware

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/present-middleware"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AllMicSendPresent(ctx context.Context, in *pb.AllMicSendPresentReq) (*pb.AllMicSendPresentResp,protocol.ServerError)
	BatchSendPresent(ctx context.Context, in *pb.BatchSendPresentReq) (*pb.BatchSendPresentResp,protocol.ServerError)
	FellowSendPresent(ctx context.Context, in *pb.FellowSendPresentReq) (*pb.FellowSendPresentResp,protocol.ServerError)
	GetChannelPresentBox(ctx context.Context, in *pb.GetChannelPresentBoxReq) (*pb.GetChannelPresentBoxResp,protocol.ServerError)
	ImSendPresent(ctx context.Context, in *pb.ImSendPresentReq) (*pb.ImSendPresentResp,protocol.ServerError)
	MagicSendPresent(ctx context.Context, in *pb.MagicSendPresentReq) (*pb.MagicSendPresentResp,protocol.ServerError)
	SendPresent(ctx context.Context, in *pb.SendPresentReq) (*pb.SendPresentResp,protocol.ServerError)
	SetSendPresent(ctx context.Context, in *pb.SetSendPresentReq) (*pb.SetSendPresentResp,protocol.ServerError)
	UnpackPresentBox(ctx context.Context, in *pb.UnpackPresentBoxReq) (*pb.UnpackPresentBoxResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
