package present_middleware

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/present-middleware"
	"google.golang.org/grpc"
)

const (
	serviceName = "present-middleware"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewPresentMiddlewareClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.PresentMiddlewareClient {
	return c.Stub().(pb.PresentMiddlewareClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) SendPresent(ctx context.Context, in *pb.SendPresentReq) (*pb.SendPresentResp, protocol.ServerError) {
	resp, err := c.typedStub().SendPresent(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchSendPresent(ctx context.Context, in *pb.BatchSendPresentReq) (*pb.BatchSendPresentResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchSendPresent(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ImSendPresent(ctx context.Context, in *pb.ImSendPresentReq) (*pb.ImSendPresentResp, protocol.ServerError) {
	resp, err := c.typedStub().ImSendPresent(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AllMicSendPresent(ctx context.Context, in *pb.AllMicSendPresentReq) (*pb.AllMicSendPresentResp, protocol.ServerError) {
	resp, err := c.typedStub().AllMicSendPresent(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) FellowSendPresent(ctx context.Context, in *pb.FellowSendPresentReq) (*pb.FellowSendPresentResp, protocol.ServerError) {
	resp, err := c.typedStub().FellowSendPresent(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) MagicSendPresent(ctx context.Context, in *pb.MagicSendPresentReq) (*pb.MagicSendPresentResp, protocol.ServerError) {
	resp, err := c.typedStub().MagicSendPresent(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelPresentBox(ctx context.Context, in *pb.GetChannelPresentBoxReq) (*pb.GetChannelPresentBoxResp, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelPresentBox(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UnpackPresentBox(ctx context.Context, in *pb.UnpackPresentBoxReq) (*pb.UnpackPresentBoxResp, protocol.ServerError) {
	resp, err := c.typedStub().UnpackPresentBox(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetSendPresent(ctx context.Context, in *pb.SetSendPresentReq) (*pb.SetSendPresentResp, protocol.ServerError) {
	resp, err := c.typedStub().SetSendPresent(ctx, in)
	return resp, protocol.ToServerError(err)
}
