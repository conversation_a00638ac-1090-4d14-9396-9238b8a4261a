// Code generated by quicksilver-cli. DO NOT EDIT.
package usertag

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	usertag "golang.52tt.com/protocol/services/userTagSvr"
)

type IClient interface {
	client.BaseClient
	BatGetSimpleGameTag(ctx context.Context, uids []uint32, games []string) (map[uint32]map[string]*usertag.SimpleGameTagExt,error)
	BatGetUserTag(ctx context.Context, uin uint32, userId []uint32) (map[uint32][]*usertag.UserTagBase,error)
	GetOptPersonalTagClassifyList(ctx context.Context, uin uint32) ([]*usertag.UserOptPersonalTagClassify,error)
	GetSimpleGame(ctx context.Context, uids []uint32, gameName string) (*usertag.GetSimpleGameTagResp,error)
	GetTagConfigList(ctx context.Context, uin, tagType uint32) ([]*usertag.UserTagBase,error)
	GetUserTag(ctx context.Context, uin uint32) ([]*usertag.UserTagBase,error)
	NotifyChannelPlayChange(ctx context.Context, uin uint32, tabName string, tabType uint32, channelId uint32) (*usertag.NotifyChannelPlayChangeResp,error)
	SetUserGameScreenShot(ctx context.Context, in *usertag.SetUserGameScreenShotReq) (*usertag.SetUserGameScreenShotResp,error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
