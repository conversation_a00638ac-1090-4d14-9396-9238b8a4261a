package masked_pk_svr

import (
	"context"
	"strconv"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/masked-pk-svr"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

const (
	serviceName = "masked-pk-svr"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewMaskedPKSvrClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.MaskedPKSvrClient {
	return c.Stub().(pb.MaskedPKSvrClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetChannelMaskedPKStatus(ctx context.Context, uid, channelId uint32) (uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().GetChannelMaskedPKStatus(ctx, &pb.GetChannelMaskedPKStatusReq{Uid: uid, ChannelId: channelId})
	return resp.GetStatus(), protocol.ToServerError(err)
}

func (c *Client) StartChannelMaskedPK(ctx context.Context, uid, channelId uint32) (*pb.StartChannelMaskedPKResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().StartChannelMaskedPK(ctx, &pb.StartChannelMaskedPKReq{Uid: uid, ChannelId: channelId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GiveUpChannelMaskedPK(ctx context.Context, uid, gameId, channelId uint32) (*pb.GiveUpChannelMaskedPKResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().GiveUpChannelMaskedPK(ctx, &pb.GiveUpChannelMaskedPKReq{Uid: uid, ConfId: gameId, ChannelId: channelId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) CancelChannelMaskedPK(ctx context.Context, uid, channelId uint32) (*pb.CancelChannelMaskedPKResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().CancelChannelMaskedPK(ctx, &pb.CancelChannelMaskedPKReq{Uid: uid, ChannelId: channelId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelMaskedPKCurrConf(ctx context.Context, uid uint32) (*pb.GetChannelMaskedPKCurrConfResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().GetChannelMaskedPKCurrConf(ctx, &pb.GetChannelMaskedPKCurrConfReq{})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelMaskedPKCurrConfWithUser(ctx context.Context, uid, channelId uint32) (*pb.GetChannelMaskedPKCurrConfWithUserResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().GetChannelMaskedPKCurrConfWithUser(ctx, &pb.GetChannelMaskedPKCurrConfWithUserReq{Uid: uid, ChannelId: channelId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelMaskedPKInfo(ctx context.Context, uid, channelId uint32) (*pb.GetChannelMaskedPKInfoResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().GetChannelMaskedPKInfo(ctx, &pb.GetChannelMaskedPKInfoReq{Uid: uid, ChannelId: channelId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelMaskedPKAnchorRank(ctx context.Context, uid, channelId, begin, limit uint32) (*pb.GetChannelMaskedPKAnchorRankResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().GetChannelMaskedPKAnchorRank(ctx, &pb.GetChannelMaskedPKAnchorRankReq{
		Uid:       uid,
		ChannelId: channelId,
		Begin:     begin,
		Limit:     limit,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelSinglePKAnchorRank(ctx context.Context, uid, channelId, begin, limit uint32) (*pb.GetChannelSinglePKAnchorRankResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().GetChannelSinglePKAnchorRank(ctx, &pb.GetChannelSinglePKAnchorRankReq{
		Uid:       uid,
		ChannelId: channelId,
		Begin:     begin,
		Limit:     limit,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchAddUserToEntertainmentQualification(ctx context.Context, qualificationMap []*pb.Qualification) (*pb.BatchAddUserToEntertainmentQualificationResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs())
	resp, err := c.typedStub().BatchAddUserToEntertainmentQualification(ctx, &pb.BatchAddUserToEntertainmentQualificationReq{
		Qualifications: qualificationMap,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchDelUserFromEntertainmentQualification(ctx context.Context, qualificationMap []*pb.Qualification) (*pb.BatchDelUserFromEntertainmentQualificationResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs())
	resp, err := c.typedStub().BatchDelUserFromEntertainmentQualification(ctx, &pb.BatchDelUserFromEntertainmentQualificationReq{
		Qualifications: qualificationMap,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelQualifiedAnchorList(ctx context.Context, channelId uint32) ([]uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs())
	resp, err := c.typedStub().GetChannelQualifiedAnchorList(ctx, &pb.GetChannelQualifiedAnchorListReq{
		ChannelId: channelId,
	})
	return resp.GetAnchorUidList(), protocol.ToServerError(err)
}

func (c *Client) GetUserFromEntertainmentQualification(ctx context.Context, uid, count, page uint32) ([]*pb.Qualification, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs())
	resp, err := c.typedStub().GetUserFromEntertainmentQualification(ctx, &pb.GetUserFromEntertainmentQualificationReq{
		Page:  page,
		Count: count,
		Uid:   uid,
	})
	return resp.Qualifications, protocol.ToServerError(err)
}

func (c *Client) GetAllWhiteList(ctx context.Context) (*pb.GetAllWhiteListResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs())
	resp, err := c.typedStub().GetAllWhiteList(ctx, &pb.GetAllWhiteListReq{})
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchDelChannelToWhiteList(ctx context.Context, list []uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs())
	_, err := c.typedStub().BatchDelChannelToWhiteList(ctx, &pb.BatchDelChannelToWhiteListReq{Channel: list})
	return protocol.ToServerError(err)
}

func (c *Client) GetMaskedPkGetConsumeTopN(ctx context.Context, channelId uint32) (*pb.MaskedPkGetConsumeTopNResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs())
	res, err := c.typedStub().MaskedPkGetConsumeTopN(ctx, &pb.MaskedPkGetConsumeTopNReq{ChannelId: channelId})
	return res, protocol.ToServerError(err)
}

func (c *Client) CheckMaskedPkRankEntry(ctx context.Context) (bool, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs())
	res, err := c.typedStub().CheckMaskedPkRankEntry(ctx, &pb.CheckMaskedPkRankEntryReq{})
	return res.GetEntryEnable(), protocol.ToServerError(err)
}

func (c *Client) PushGameBeginConf(ctx context.Context, channelId uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs())
	_, err := c.typedStub().PushGameBeginConf(ctx, &pb.PushGameBeginConfReq{ChannelId: channelId})
	return protocol.ToServerError(err)
}

func (c *Client) BatchGetChannelMaskedPKStatus(ctx context.Context, gameId uint32, channelIds []uint32) (
	map[uint32]uint32, error) {
	res, err := c.typedStub().BatchGetChannelMaskedPKStatus(ctx, &pb.BatchGetChannelMaskedPKStatusReq{GameId: gameId, ChannelIdList: channelIds})
	return res.GetChannelStatusMap(), protocol.ToServerError(err)
}
