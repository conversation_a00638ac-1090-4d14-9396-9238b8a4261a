// Code generated by quicksilver-cli. DO NOT EDIT.
package masked_pk_svr

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/masked-pk-svr"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	BatchAddUserToEntertainmentQualification(ctx context.Context, qualificationMap []*pb.Qualification) (*pb.BatchAddUserToEntertainmentQualificationResp,protocol.ServerError)
	BatchDelChannelToWhiteList(ctx context.Context, list []uint32) protocol.ServerError
	BatchDelUserFromEntertainmentQualification(ctx context.Context, qualificationMap []*pb.Qualification) (*pb.BatchDelUserFromEntertainmentQualificationResp,protocol.ServerError)
	BatchGetChannelMaskedPKStatus(ctx context.Context, gameId uint32, channelIds []uint32) (map[uint32]uint32,error)
	CancelChannelMaskedPK(ctx context.Context, uid, channelId uint32) (*pb.CancelChannelMaskedPKResp,protocol.ServerError)
	CheckMaskedPkRankEntry(ctx context.Context) (bool,protocol.ServerError)
	GetAllWhiteList(ctx context.Context) (*pb.GetAllWhiteListResp,protocol.ServerError)
	GetChannelMaskedPKAnchorRank(ctx context.Context, uid, channelId, begin, limit uint32) (*pb.GetChannelMaskedPKAnchorRankResp,protocol.ServerError)
	GetChannelMaskedPKCurrConf(ctx context.Context, uid uint32) (*pb.GetChannelMaskedPKCurrConfResp,protocol.ServerError)
	GetChannelMaskedPKCurrConfWithUser(ctx context.Context, uid, channelId uint32) (*pb.GetChannelMaskedPKCurrConfWithUserResp,protocol.ServerError)
	GetChannelMaskedPKInfo(ctx context.Context, uid, channelId uint32) (*pb.GetChannelMaskedPKInfoResp,protocol.ServerError)
	GetChannelMaskedPKStatus(ctx context.Context, uid, channelId uint32) (uint32,protocol.ServerError)
	GetChannelQualifiedAnchorList(ctx context.Context, channelId uint32) ([]uint32,protocol.ServerError)
	GetChannelSinglePKAnchorRank(ctx context.Context, uid, channelId, begin, limit uint32) (*pb.GetChannelSinglePKAnchorRankResp,protocol.ServerError)
	GetMaskedPkGetConsumeTopN(ctx context.Context, channelId uint32) (*pb.MaskedPkGetConsumeTopNResp,protocol.ServerError)
	GetUserFromEntertainmentQualification(ctx context.Context, uid, count, page uint32) ([]*pb.Qualification,protocol.ServerError)
	GiveUpChannelMaskedPK(ctx context.Context, uid, gameId, channelId uint32) (*pb.GiveUpChannelMaskedPKResp,protocol.ServerError)
	PushGameBeginConf(ctx context.Context, channelId uint32) protocol.ServerError
	StartChannelMaskedPK(ctx context.Context, uid, channelId uint32) (*pb.StartChannelMaskedPKResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
