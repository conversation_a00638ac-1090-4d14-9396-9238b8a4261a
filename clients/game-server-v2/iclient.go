package game_server_v2

import (
	context "context"
	protocol "golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/game-server-v2"
	"google.golang.org/grpc"
)

type IConfClient interface {
	AddScanGameList(ctx context.Context, gameName string, score uint32, iosPack, androidPackage []string) (uint32, protocol.ServerError)
	CreateGame(ctx context.Context, gameName string, gameType uint32) (uint32, error)
	GetGameByName(ctx context.Context, gameName string, offSet, limit uint32) (*pb.GetGameByNameResp, protocol.ServerError)
	GetGameByUGameIds(ctx context.Context, uGameIds []uint32) (map[uint32]*pb.GameInfo, protocol.ServerError)
	GetGameMapByGameCardIds(ctx context.Context, gameCardIds []uint32) (map[uint32][]*pb.GameInfo, protocol.ServerError)
	GetScanGameList(ctx context.Context, gameName string, offset, limit uint32) (*pb.GetScanGameListResp, protocol.ServerError)
	UpdateGameByUGameId(ctx context.Context, req *pb.ModifyGameReq) (*pb.ModifyGameResp, protocol.ServerError)
}

func NewIConfClient(dopts ...grpc.DialOption) IConfClient {
	cli, _ := NewConfClient(dopts...)
	return cli
}
