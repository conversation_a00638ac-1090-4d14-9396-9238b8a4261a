package channel_play_logic

import (
	"context"
	"golang.52tt.com/pkg/protocol"
	channelPlay "golang.52tt.com/protocol/app/channel-play"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/logicsvr-go/channel-play-logic"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-play-logic"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelPlayLogicClient(cc)
			},
			dopts...,
		),
	}, nil
}

func NewCCClient(cc *grpc.ClientConn) pb.ChannelPlayLogicClient {
	return pb.NewChannelPlayLogicClient(cc)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.ChannelPlayLogicClient {
	return c.Stub().(pb.ChannelPlayLogicClient)
}

func (c *Client) GetHomePageHeadConfig(ctx context.Context, in *channelPlay.HomePageHeadConfigReq) (*channelPlay.HomePageHeadConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().GetHomePageHeadConfig(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetCache(ctx context.Context, in *channelPlay.GetCacheReq) (*channelPlay.GetCacheResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCache(ctx, in)
	return resp, protocol.ToServerError(err)
}
