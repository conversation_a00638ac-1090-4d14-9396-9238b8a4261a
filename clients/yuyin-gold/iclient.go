package yuyingold

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/yuyingold"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetGuildUnSettlementSummary(ctx context.Context, guildid uint32) (*pb.GuildUnSettlementSummaryRsp, protocol.ServerError)
	GetUnSettlementDetals(ctx context.Context, req pb.GuildUnSettlementDetailReq) (*pb.GuildUnSettlementDetailRsp, protocol.ServerError)
	GetGuildAnchorList(ctx context.Context, guildid uint32) (*pb.GetGuildAnchorListRsp, protocol.ServerError)
	GetGuildIncomeDetails(ctx context.Context, req pb.GuildIncomeDetailReq) (*pb.GuildIncomeDetailRsp, protocol.ServerError)
	GetGuildTodayIncomeInfo(ctx context.Context, req pb.GuildTodayIncomeReq) (*pb.GuildTodayIncomeRsp, protocol.ServerError)
	GuildConsumeDayQoq(ctx context.Context, req pb.GuildConsumeDayQoqReq) (*pb.GuildConsumeDayQoqRsp, protocol.ServerError)
	GetGuildMonthTrendInfo(ctx context.Context, req pb.GuildMonthIncomeReq) (*pb.GuildMonthIncomeRsp, protocol.ServerError)
	GetGeneralIncome(ctx context.Context, req pb.GetGeneralIncomeReq) (*pb.GetGeneralIncomeRsp, protocol.ServerError)
	GetDateDimenIncome(ctx context.Context, req pb.GetGeneralIncomeReq) (*pb.GetDateDimenIncomeRsp, protocol.ServerError)
	GetWeekDimenIncome(ctx context.Context, req pb.GetGeneralIncomeReq) (*pb.GetWeekDimenIncomeRsp, protocol.ServerError)
	GetMonthDimenIncome(ctx context.Context, req pb.GetGeneralIncomeReq) (*pb.GetMonthDimenIncomeRsp, protocol.ServerError)
	GetAnchorInfo(ctx context.Context, req pb.GetGeneralIncomeReq) (*pb.GetAnchorInfoRsp, protocol.ServerError)
	GetGuildExtraIncome(ctx context.Context, req pb.GetGuildsGeneralIncomeReq) (*pb.GetGuildExtraIncomeRsp, protocol.ServerError)
	GetGuildTaskList(ctx context.Context, req pb.GetGeneralIncomeReq) (*pb.GetGuildTaskListRsp, protocol.ServerError)
	GetGuildTaskListV2(ctx context.Context, req pb.GetGeneralIncomeReq) (*pb.GetGuildTaskListV2Rsp, protocol.ServerError)
	GetValidAnchorTrendListV2(ctx context.Context, req pb.GetGeneralTrendListReq) (*pb.GetValidAnchorTrendListRsp, protocol.ServerError)
	GetGeneralIncomeV2(ctx context.Context, req pb.GetGeneralIncomeReq) (*pb.GetGeneralIncomeRsp, protocol.ServerError)
	GetAnchorList(ctx context.Context, req pb.GetGeneralIncomeReq) (*pb.GetAnchorListRsp, protocol.ServerError)
	GetIncomeTrendList(ctx context.Context, req pb.GetGeneralTrendListReq) (*pb.GetIncomeTrendListRsp, protocol.ServerError)
	GetLastIncomeTrendList(ctx context.Context, req pb.GetGeneralTrendListReq) (*pb.GetIncomeTrendListRsp, protocol.ServerError)
	GetValidAnchorTrendList(ctx context.Context, req pb.GetGeneralTrendListReq) (*pb.GetValidAnchorTrendListRsp, protocol.ServerError)
	GetConsumeRank(ctx context.Context, req pb.ConsumeRankReq) (*pb.ConsumeRankRsp, protocol.ServerError)
	GetAllHandlePresentCount(ctx context.Context, req pb.GetAllHandlePresentCountReq) (*pb.GetAllHandlePresentCountResp, protocol.ServerError)
	GetAllHandlePresentOrderList(ctx context.Context, req pb.GetAllHandlePresentOrderListReq) (*pb.GetAllHandlePresentOrderListResp, protocol.ServerError)
	AddOrder(ctx context.Context, req pb.AddOrderReq) (*pb.AddOrderResp, protocol.ServerError)
	GetGuildsBaseIncomeInfo(ctx context.Context, req pb.GetGuildsBaseIncomeInfoReq) (*pb.GetGuildsBaseIncomeInfoRsp, protocol.ServerError)
	GetGuildsUnSettlementSummary(ctx context.Context, req *pb.GetGuildsUnSettlementSummaryReq) (*pb.GetGuildsUnSettlementSummaryRsp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
