package avatar

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/avatar"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	SaveAvatar(ctx context.Context, in *pb.SaveAvatarReq) (*pb.SaveAvatarResp, error)
	GetAvatar(ctx context.Context, in *pb.GetAvatarReq) (*pb.GetAvatarResp, error)
	DeleteAvatar(ctx context.Context, in *pb.DeleteAvatarReq) (*pb.DeleteAvatarResp, error)
	UploadAvatar(ctx context.Context, in *pb.UploadAvatarReq) (*pb.UploadAvatarResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
