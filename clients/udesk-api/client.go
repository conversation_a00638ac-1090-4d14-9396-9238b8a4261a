package udesk_api

import (
	"context"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/udesk-api"
	"google.golang.org/grpc"
)

const (
	serviceName = "udesk-api"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewUdeskApiClient(cc)
		}, dopts...),
	}
}

func (c *Client) typedStub() pb.UdeskApiClient { return c.Stub().(pb.UdeskApiClient) }

func (c *Client) GetUdeskUnReadMsg(ctx context.Context, uid uint32) (out *pb.GetUdeskUnReadMsgResp, err protocol.ServerError) {
	r, e := c.typedStub().GetUdeskUnReadMsg(ctx, &pb.GetUdeskUnReadMsgReq{Uid: uid})
	if e == nil {
		out = r
	} else {
		err = protocol.ToServerError(e)
	}
	return
}

func (c *Client) SetUdeskUnReadMsg(ctx context.Context, uid uint32, now int64) (out *pb.SetUdeskUnReadMsgResp, err protocol.ServerError) {
	r, e := c.typedStub().SetUdeskUnReadMsg(ctx, &pb.SetUdeskUnReadMsgReq{Uid: uid, LastUnreadTime: now})
	if e == nil {
		out = r
	} else {
		err = protocol.ToServerError(e)
	}
	return
}

func (c *Client) GetVipKefuInfo(ctx context.Context, uid, marketId uint32) (out *pb.GetVipKefuInfoResp, err protocol.ServerError) {
	r, e := c.typedStub().GetVipKefuInfo(ctx, &pb.GetVipKefuInfoReq{
		UidList:  []uint32{uid},
		MarketId: marketId,
	})
	if e == nil {
		out = r
	} else {
		err = protocol.ToServerError(e)
	}
	return
}

func (c *Client) SetVipKefuTempAccess(ctx context.Context, uid uint32, expireTs int64) (err protocol.ServerError) {
	_, e := c.typedStub().SetVipKefuTempAccess(ctx, &pb.SetVipKefuTempAccessReq{
		UidList:  []uint32{uid},
		ExpireTs: expireTs,
	})
	if e != nil {
		err = protocol.ToServerError(e)
	}
	return
}

func (c *Client) DelVipKefuTempAccess(ctx context.Context, uids ...uint32) (err protocol.ServerError) {
	_, e := c.typedStub().DelVipKefuTempAccess(ctx, &pb.DelVipKefuTempAccessReq{
		UidList: uids,
	})
	if e != nil {
		err = protocol.ToServerError(e)
	}
	return
}

func (c *Client) StartVipKefuRead(ctx context.Context, uids ...uint32) (err protocol.ServerError) {
	_, e := c.typedStub().StartVipKefuRead(ctx, &pb.StartVipKefuReadReq{
		UidList: uids,
	})
	if e != nil {
		err = protocol.ToServerError(e)
	}
	return
}

func (c *Client) UpdateVipKefuMsg(ctx context.Context, uid uint32, lastMsgTs int64, lastMsgContent string, unreadMsgIncr int) (err protocol.ServerError) {
	_, e := c.typedStub().UpdateVipKefuMsg(ctx, &pb.UpdateVipKefuMsgReq{
		UidList:        []uint32{uid},
		LastMsgTs:      lastMsgTs,
		LastMsgContent: lastMsgContent,
		UnreadMsgIncr:  int32(unreadMsgIncr),
	})
	if e != nil {
		err = protocol.ToServerError(e)
	}
	return
}

func (c *Client) NotifyVipKefuAction(ctx context.Context, actionType pb.VipKefuActionType, id int32) (err protocol.ServerError) {
	_, e := c.typedStub().NotifyVipKefuAction(ctx, &pb.NotifyVipKefuActionReq{
		ActionType: actionType,
		Id:         id,
	})
	if e != nil {
		err = protocol.ToServerError(e)
	}
	return
}
