package comm_push_op_schedule

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/comm-push-op-schedule"
	"google.golang.org/grpc"
)

const (
	serviceName = "comm-push-op-schedule"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewCommPushOpScheduleClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.CommPushOpScheduleClient { return c.Stub().(pb.CommPushOpScheduleClient) }

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func (c *Client) GetOpPushTaskFromSetListLen(ctx context.Context) (uint32,error) {
	resp,err := c.typedStub().GetOpPushTaskFromSetListLen(ctx,&pb.GetOpPushTaskFromSetListLenReq{})

	if err != nil {
		return 0, err
	}

	return resp.Length,nil
}

func (c *Client) GetPaidOpOfflineSendPushCnt(ctx context.Context) (*pb.GetPaidOpOfflineSendPushCntResp,error) {
	resp,err := c.typedStub().GetPaidOpOfflineSendPushCnt(ctx,&pb.GetPaidOpOfflineSendPushCntReq{})

	if err != nil {
		return resp, err
	}

	return resp, nil
}