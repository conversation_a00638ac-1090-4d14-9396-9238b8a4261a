package photoalbum

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/photoalbumsvr"
	"google.golang.org/grpc"
)

const (
	serviceName = "photoalbum"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewPhotoAlbumSvrClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.PhotoAlbumSvrClient {
	return c.Stub().(pb.PhotoAlbumSvrClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetPhotoAlbum(ctx context.Context, uid uint32) (*pb.GetPhotoAlbumResp, protocol.ServerError) {
	resp, err := c.typedStub().GetPhotoAlbum(ctx, &pb.GetPhotoAlbumReq{
		Uid: uid,
	})
	return resp, protocol.ToServerError(err)
}
