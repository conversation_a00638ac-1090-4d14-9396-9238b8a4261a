package iop_home_recommend

import (
	"context"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/iop-home-recommend"
	"google.golang.org/grpc"
)

func GetTarget(env string) string {
	tarGet := "dsp-iop-client-api.data-service.svc.cluster.local:8297"
	if env == "staging" {
		tarGet = "dsp-iop-client-api-test.data-service.svc.cluster.local:8297"
	}

	log.Infof("env:%s GetTarget: %s", env, tarGet)
	return tarGet
}

type Client struct {
	*grpc.ClientConn
}

func NewClient(ctx context.Context, env string) (*Client, error) {
	connI, err := grpc.DialContext(ctx, GetTarget(env), grpc.WithInsecure(), grpc.WithBlock())
	if err != nil {
		return nil, err

	}

	return &Client{
		ClientConn: connI,
	}, nil
}

func (c *Client) GetRecommendPgcList(ctx context.Context, in *pb.GetRecommendPgcListReq) (*pb.GetRecommendPgcListResp, error) {
	resp := &pb.GetRecommendPgcListResp{}
	err := c.ClientConn.Invoke(ctx, "/com.quwan.dspiopother.proto.HomePageRecommendService/GetRecommendPgcList", in, resp)
	return resp, err
}
