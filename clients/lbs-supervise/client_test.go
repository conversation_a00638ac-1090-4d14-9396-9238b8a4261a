package lbs_supervise

import (
	"context"
	"fmt"
	"golang.52tt.com/protocol/services/lbs/lbs_supervise"
	"testing"
)

var cli *Client

func init() {
	var err error
	cli, err = NewClient()
	if err != nil {
		panic(err)
	}
}

func TestClient_GetUserRegionInfo(t *testing.T) {
	resp, err := cli.GetUserRegionInfo(context.Background(), &lbs_supervise.GetUserRegionInfoReq{
		Uid: []uint32{2214969, 2198917},
	})
	if err != nil {
		panic(err)
	}
	fmt.Println(resp)
	for _, x := range resp.UserRegionInfo {
		fmt.Println(x.Uid, x.City, x.Province, x.Country)
	}
}
