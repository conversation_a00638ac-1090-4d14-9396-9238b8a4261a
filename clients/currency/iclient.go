package currency

import (
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	currencyPB "golang.52tt.com/protocol/services/currencysvr"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetUserCurrency(ctx context.Context, uid uint32) (int32, protocol.ServerError)
	AddUserCurrency(ctx context.Context, uid uint32, add int32, orderID, desc string, reason uint32) protocol.ServerError
	GetUserCurrencyLog(ctx context.Context, uid uint32, in *currencyPB.GetUserCurrencyLogReq) (*currencyPB.GetUserCurrencyLogResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
