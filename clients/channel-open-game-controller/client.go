package channel_open_game_controller

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-open-game-controller"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-open-game-controller"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelOpenGameControllerClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelOpenGameControllerClient {
	return c.Stub().(pb.ChannelOpenGameControllerClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) SetChannelGame(ctx context.Context, in *pb.SetChannelGameReq) (*pb.SetChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelGameModeInfo(ctx context.Context, in *pb.SetChannelGameModeInfoReq) (*pb.SetChannelGameModeInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelGameModeInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetOpenid(ctx context.Context, in *pb.GetOpenidReq) (*pb.GetOpenidResp, protocol.ServerError) {
	resp, err := c.typedStub().GetOpenid(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) JoinChannelGame(ctx context.Context, in *pb.JoinChannelGameReq) (*pb.JoinChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().JoinChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) QuitChannelGame(ctx context.Context, in *pb.QuitChannelGameReq) (*pb.QuitChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().QuitChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ReadyChannelGame(ctx context.Context, in *pb.ReadyChannelGameReq) (*pb.ReadyChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().ReadyChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UnReadyChannelGame(ctx context.Context, in *pb.UnReadyChannelGameReq) (*pb.UnReadyChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().UnReadyChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelGameStatusInfo(ctx context.Context, in *pb.GetChannelGameStatusInfoReq) (*pb.GetChannelGameStatusInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelGameStatusInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetChannelGameStatusInfo(ctx context.Context, in *pb.BatchGetChannelGameStatusInfoReq) (*pb.BatchGetChannelGameStatusInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetChannelGameStatusInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetLoading(ctx context.Context, in *pb.SetLoadingReq) (*pb.SetLoadingResp, protocol.ServerError) {
	resp, err := c.typedStub().SetLoading(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) StartChannelGame(ctx context.Context, in *pb.StartChannelGameReq) (*pb.StartChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().StartChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ExitChannelGame(ctx context.Context, in *pb.ExitChannelGameReq) (*pb.ExitChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().ExitChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) KickOutChannelGame(ctx context.Context, in *pb.KickOutChannelGameReq) (*pb.KickOutChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().KickOutChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelGameHost(ctx context.Context, channelId uint32) (*pb.GetChannelGameHostResp, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelGameHost(ctx, &pb.GetChannelGameHostReq{ChannelId: channelId})
	return resp, protocol.ToServerError(err)
}