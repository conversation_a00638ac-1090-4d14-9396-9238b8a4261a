package channel_ktv

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/channel-ktv"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	BatchGetChannelKTVInfo(
		ctx context.Context, in *pb.BatchGetChannelKTVInfoReq, opts ...grpc.CallOption,
	) (*pb.BatchGetChannelKTVInfoResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
