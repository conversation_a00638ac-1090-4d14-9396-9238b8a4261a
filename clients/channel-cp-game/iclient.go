// Code generated by quicksilver-cli. DO NOT EDIT.
package channel_cp_game

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/channel-cp-game"
	pb_logic "golang.52tt.com/protocol/app/channel-cp-game-logic"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AddCpGamePhaseEndTime(ctx context.Context, opUid, channelId, phase, addSec uint32) protocol.ServerError
	AddCpStrengthDelay(ctx context.Context, req *pb.AddCpStrengthDelayReq) protocol.ServerError
	AddCpStrengthLogDelay(ctx context.Context, req *pb.AddCpStrengthLogDelayReq) protocol.ServerError
	BatchGetChannelCpGamePhase(ctx context.Context, channelIds []uint32) (*pb.BatchGetChannelCpGamePhaseResp,protocol.ServerError)
	BatchGetStrength(ctx context.Context, myUid uint32, toUids []uint32) (*pb.BatchGetStrengthResp,protocol.ServerError)
	ChoseCpRare(ctx context.Context, channelId, rareId uint32, teamId string, confirm bool, choseRare []*pb_logic.ChoseRare) protocol.ServerError
	GetCardConf(ctx context.Context) (*pb.GetAllCpCardConfResp,protocol.ServerError)
	GetChannelCpGameEntryLevel(ctx context.Context, opUid, channelId uint32) (uint32,protocol.ServerError)
	GetChannelCpGamePhase(ctx context.Context, opUid, channelId uint32) (uint32,protocol.ServerError)
	GetChannelCpGodRankList(ctx context.Context, opUid, channelId uint32) (*pb.GetChannelCpGodRankListResp,protocol.ServerError)
	GetCpStrength(ctx context.Context, myUid, toUid uint32) (*pb.GetCpStrengthResp,protocol.ServerError)
	GetCpStrengthHistory(ctx context.Context, myUid, toUid, offset, limit uint32) (*pb.GetCpStrengthHistoryResp,protocol.ServerError)
	GetCurrCpGameInfo(ctx context.Context, opUid, channelId uint32) (*pb.CpGameInfo,protocol.ServerError)
	GetNameplateInfo(ctx context.Context, myUid, toUid uint32) (*pb.GetNameplateInfoResp,protocol.ServerError)
	MvpAutoHoldMic(ctx context.Context, channelId, mvpUid uint32) (*pb.MvpAutoHoldMicResp,protocol.ServerError)
	ReportCpGameInfoDelay(ctx context.Context, req *pb.ReportCpGameInfoDelayReq) protocol.ServerError
	SetChannelCpGamePhase(ctx context.Context, opUid, channelId, phase uint32) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
