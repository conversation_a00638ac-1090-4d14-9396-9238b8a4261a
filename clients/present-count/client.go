package present_count

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/present-count-go"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"
)

const (
	serviceName = "present-count"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewPresentCountGoClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *C<PERSON>) typedStub() pb.PresentCountGoClient {
	return c.Stub().(pb.PresentCountGoClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) SwitchPresentCount(ctx context.Context, opUid uint32, req *pb.PresentCountReq) (*pb.PresentCountResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().SwitchPresentCount(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentCountById(ctx context.Context, opUid uint32, req *pb.GetPresentCountByIdReq) (*pb.GetPresentCountByIdResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetPresentCountById(ctx, &pb.GetPresentCountByIdReq{
		ChannelId: req.GetChannelId(),
		Uid:       req.GetUid(),
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentCountState(ctx context.Context, opUid uint32, req *pb.GetPresentCountStateReq) (*pb.GetPresentCountStateResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetPresentCountState(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ClearUserPresentCount(ctx context.Context, uid, channelId uint32) (*pb.ClearUserPresentCountResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().ClearUserPresentCount(ctx, &pb.ClearUserPresentCountReq{
		ChannelId: channelId,
		Uid:       uid,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPresentCountRank(ctx context.Context, opUid uint32, req *pb.GetPresentCountRankReq) (*pb.GetPresentCountRankResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetPresentCountRank(ctx, req)
	return resp, protocol.ToServerError(err)
}
