package present_count

import (
	"context"
	pb "golang.52tt.com/protocol/services/present-count-go"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

var cli *Client

func init() {
	cli, _ = NewClient(grpc.WithBlock())
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestClient_SwitchPresentCount(t *testing.T) {
	_, err := cli.SwitchPresentCount(context.Background(), 0, &pb.PresentCountReq{
		ChannelId: 0,
		IsOff:     false,
		MicrUsers: nil,
		Uid:       0,
	})
	if err != nil {
		t.Errorf("SwitchPresentCount fail, err:%v", err)
	}
	t.Logf("SwitchPresentCount success.")
}

func TestClient_GetPresentCountById(t *testing.T) {
	resp, err := cli.GetPresentCountById(context.Background(), 0, &pb.GetPresentCountByIdReq{
		ChannelId: 0,
		Uid:       0,
	})
	if err != nil {
		t.Errorf("GetPresentCountById fail,err:%v", err)
	}
	t.Logf("GetPresentCountById success. resp.getPrice(%d)", resp.GetPrice())
}

func TestClient_GetPresentCountState(t *testing.T) {
	resp, err := cli.GetPresentCountState(context.Background(), 0, &pb.GetPresentCountStateReq{
		ChannelId: 0,
	})
	if err != nil {
		t.Errorf("GetPresentCountState fail,err:%v", err)
	}

	t.Logf("GetPresentCountState success. resp.GetState(%v)", resp.GetState())

	countList := resp.GetMicsPresentCount()
	for i, item := range countList {
		t.Logf("GetPresentCountState: (%d) uid(%d) count(%d)", i, item.GetUid(), item.GetPrice())
	}
}
