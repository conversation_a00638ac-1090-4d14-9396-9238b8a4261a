package vipprivilegesvr_go

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/vipprivilegesvr-go"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "vipprivilegesvr-go"
)

// Client is the wrapper-client for VipPrivilegeSvr client.
type Client struct {
	client.BaseClient
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewVipPrivilegeSvrClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of VipPrivilegeSvrClient.
func (c *Client) typedStub() pb.VipPrivilegeSvrClient {
	return c.Stub().(pb.VipPrivilegeSvrClient)
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// OnRichChange
func (c *Client) OnRichChange(ctx context.Context, uid uint32, oldRich, newRich uint64, opts ...grpc.CallOption) protocol.ServerError {
	req := &pb.OnRichChangeReq{
		Uid:     uid,
		OldRich: oldRich,
		NewRich: newRich,
	}
	_, err := c.typedStub().OnRichChange(ctx, req, opts...)
	return protocol.ToServerError(err)
}

// GetVipLevelConfigs
func (c *Client) GetVipLevelConfigs(ctx context.Context, opts ...grpc.CallOption) (*pb.GetVipLevelConfigsResp, protocol.ServerError) {
	req := &pb.GetVipLevelConfigsReq{}
	resp, err := c.typedStub().GetVipLevelConfigs(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetVipPrivilegeList
func (c *Client) GetVipPrivilegeList(ctx context.Context, req *pb.GetVipPrivilegeListReq, opts ...grpc.CallOption) (*pb.GetVipPrivilegeListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetVipPrivilegeList(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// AddVipKefu
func (c *Client) AddVipKefu(ctx context.Context, req *pb.AddVipKefuReq, opts ...grpc.CallOption) (*pb.AddVipKefuResp, protocol.ServerError) {
	resp, err := c.typedStub().AddVipKefu(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// SetKefuOnlineStatus
func (c *Client) SetKefuOnlineStatus(ctx context.Context, req *pb.SetKefuOnlineStatusReq, opts ...grpc.CallOption) (*pb.SetKefuOnlineStatusResp, protocol.ServerError) {
	resp, err := c.typedStub().SetKefuOnlineStatus(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// SetKefu2Uid
func (c *Client) SetKefu2Uid(ctx context.Context, req *pb.SetKefu2UidReq, opts ...grpc.CallOption) (*pb.SetKefu2UidResp, protocol.ServerError) {
	resp, err := c.typedStub().SetKefu2Uid(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetKefuByUid
func (c *Client) GetKefuByUid(ctx context.Context, req *pb.GetKefuByUidReq, opts ...grpc.CallOption) (*pb.GetKefuByUidResp, protocol.ServerError) {
	resp, err := c.typedStub().GetKefuByUid(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetVipKefus
func (c *Client) GetVipKefus(ctx context.Context, req *pb.GetVipKefusReq, opts ...grpc.CallOption) (*pb.GetVipKefusResp, protocol.ServerError) {
	resp, err := c.typedStub().GetVipKefus(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetVipKefuInfoList
func (c *Client) GetVipKefuInfoList(ctx context.Context, req *pb.GetVipKefuInfoListReq, opts ...grpc.CallOption) (*pb.GetVipKefuInfoListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetVipKefuInfoList(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// SetKefuVip
func (c *Client) SetKefuVip(ctx context.Context, req *pb.SetKefuVipReq, opts ...grpc.CallOption) (*pb.SetKefuVipResp, protocol.ServerError) {
	resp, err := c.typedStub().SetKefuVip(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// DeleteVipKefu
func (c *Client) DeleteVipKefu(ctx context.Context, req *pb.DeleteVipKefuReq, opts ...grpc.CallOption) (*pb.DeleteVipKefuResp, protocol.ServerError) {
	resp, err := c.typedStub().DeleteVipKefu(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetVipInfoByRich
func (c *Client) GetVipInfoByRich(ctx context.Context, req *pb.GetVipInfoByRichReq, opts ...grpc.CallOption) (*pb.GetVipInfoByRichResp, protocol.ServerError) {
	resp, err := c.typedStub().GetVipInfoByRich(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserVipGiftPackageEntrance(ctx context.Context, uid uint32, opts ...grpc.CallOption) (*pb.GetUserVipGiftPackageEntranceResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserVipGiftPackageEntrance(ctx, &pb.GetUserVipGiftPackageEntranceReq{Uid: uid}, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserVipGiftPackageInfo(ctx context.Context, uid uint32, opts ...grpc.CallOption) (*pb.GetUserVipGiftPackageInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserVipGiftPackageInfo(ctx, &pb.GetUserVipGiftPackageInfoReq{Uid: uid}, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SubmitReceiveVipGiftPackage(ctx context.Context, req *pb.SubmitReceiveVipGiftPackageReq, opts ...grpc.CallOption) (*pb.SubmitReceiveVipGiftPackageResp, protocol.ServerError) {
	resp, err := c.typedStub().SubmitReceiveVipGiftPackage(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}
