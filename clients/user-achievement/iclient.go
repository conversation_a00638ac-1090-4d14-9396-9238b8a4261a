package user_achievement

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/user-achievement"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	ModifyCertifyStyle(ctx context.Context, in *pb.ModifyCertifyStyleReq) (*pb.ModifyCertifyStyleResp, error)
	DeleteCertifyStyle(ctx context.Context, in *pb.DeleteCertifyStyleReq) (*pb.DeleteCertifyStyleResp, error)
	ListCertifyStyle(ctx context.Context, in *pb.ListCertifyStyleReq) (*pb.ListCertifyStyleResp, error)
	ResetCertifyStyleSort(ctx context.Context, in *pb.ResetCertifyStyleSortReq) (*pb.ResetCertifyStyleSortResp, error)
	DelUserOfficialCertify(ctx context.Context, in *pb.DelUserOfficialCertifyReq) (*pb.DelUserOfficialCertifyResp, error)
	SetUserOfficialCertify(ctx context.Context, in *pb.SetUserOfficialCertifyReq) (*pb.SetUserOfficialCertifyResp, error)
	GetUserOfficialCertifyByUids(ctx context.Context, in *pb.GetUserOfficialCertifyByUidsReq) (*pb.GetUserOfficialCertifyListResp, error)
	GetUserOfficialCertifyList(ctx context.Context, in *pb.GetUserOfficialCertifyListReq) (*pb.GetUserOfficialCertifyListResp, error)
	GetUserOfficialCertify(ctx context.Context, in *pb.GetUserOfficialCertifyReq) (*pb.UserOfficialCertifyInfo, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
