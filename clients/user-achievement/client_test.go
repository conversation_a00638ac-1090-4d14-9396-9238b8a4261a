package user_achievement

import (
	"context"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/protocol/services/user-achievement"
	"testing"
)

func TestClient(t *testing.T) {
	client, err := NewClient()
	if err != nil {
		panic(err)
	}
	//
	//resp, err := client.GetUserOfficialCertifyByUids(context.Background(), &user_achievement.GetUserOfficialCertifyByUidsReq{Uids: []uint32{2200507}})
	//if err != nil {
	//	panic(err)
	//}
	//
	//fmt.Println(resp)
	//fmt.Println(len(resp.InfoList[0].Info))
	//
	//
	//resp2, err := client.GetUserOfficialCertifyList(context.Background(), &user_achievement.GetUserOfficialCertifyListReq{})
	//if err != nil {
	//	panic(err)
	//}
	//
	//fmt.Println(resp2)
	//fmt.Println(resp2.TotalCount)
	//return
	//return

	var uid uint32 = 2214969
	var style = "test-style"
	_, err = client.ModifyCertifyStyle(context.Background(), &user_achievement.ModifyCertifyStyleReq{
		Style: &user_achievement.CertifyStyle{
			Id: style, Icon: "http://icon/", Background: "http://background/", FontColor: "#f0f0f0", Weight: 555, PushSample: "恭喜你，###，你成为了山霸王！！！！！！！！",
		},
	})
	if err != nil {
		panic(err)
	}

	_, err = client.SetUserOfficialCertify(context.Background(), &user_achievement.SetUserOfficialCertifyReq{
		Uid: uid, Info: []*user_achievement.ChangeInfo{{Title: "test头衔", Style: style, PushArgs: "小黑哥", Op: user_achievement.ChangeInfo_ADD}},
		Intro: "年度最" +
			"霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸霸" +
			"气描述",
	})
	if err != nil {
		panic(err)
	}

	_, err = client.SetUserOfficialCertify(context.Background(), &user_achievement.SetUserOfficialCertifyReq{
		Uid: uid, Info: []*user_achievement.ChangeInfo{{Title: "test头衔", Style: style, PushArgs: "小黑哥", Op: user_achievement.ChangeInfo_ADD}},
		Intro: "test-intro",
	})
	if err != nil {
		panic(err)
	}

	resp, err := client.GetUserOfficialCertifyByUids(context.Background(), &user_achievement.GetUserOfficialCertifyByUidsReq{Uids: []uint32{uid}})
	if err != nil {
		panic(err)
	}
	if len(resp.InfoList) != 1 {
		panic(utils.ToJson(resp))
	}
	if len(resp.InfoList[0].Info) != 1 || resp.InfoList[0].Intro != "test-intro" {
		panic(utils.ToJson(resp))
	}

	_, err = client.DelUserOfficialCertify(context.Background(), &user_achievement.DelUserOfficialCertifyReq{Uid: uid})
	if err != nil {
		panic(err)
	}

	resp, err = client.GetUserOfficialCertifyByUids(context.Background(), &user_achievement.GetUserOfficialCertifyByUidsReq{Uids: []uint32{uid}})
	if err != nil {
		panic(err)
	}
	if len(resp.InfoList) != 0 {
		panic(utils.ToJson(resp))
	}

	var pageNo uint32 = 1
	for i := 0; i < 10; i++ {
		listResp, err := client.GetUserOfficialCertifyList(context.Background(), &user_achievement.GetUserOfficialCertifyListReq{
			PageNo: pageNo, PageSize: 10,
		})
		if err != nil {
			panic(err)
		}
		for _, item := range listResp.InfoList {
			if item.Uid == uid {
				panic(listResp)
			}
		}
		if listResp.TotalCount < (pageNo+1)*10 {
			break
		}
		pageNo++
	}
}
