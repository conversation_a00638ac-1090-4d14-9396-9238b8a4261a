package channel_ktv_heartbeat

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-ktv-heartbeat"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-ktv-heartbeat"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelKTVHeartbeatClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelKTVHeartbeatClient {
	return c.Stub().(pb.ChannelKTVHeartbeatClient)
}

func (c *Client) ReportKTVHeartBeat(ctx context.Context, in *pb.ReportKTVHeartbeatReq) (out *pb.ReportKTVHeartbeatResp, err error) {
	resp, err := c.typedStub().ReportKTVHeartBeat(ctx, in)
	return resp, protocol.ToServerError(err)
}
