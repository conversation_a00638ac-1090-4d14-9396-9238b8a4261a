package newbiepagelogic

//import (
//	"context"
//
//	"github.com/grpc-ecosystem/go-grpc-middleware"
//	"golang.52tt.com/pkg/client"
//	"golang.52tt.com/pkg/foundation/grpc/client"
//	"golang.52tt.com/pkg/protocol"
//	"golang.52tt.com/pkg/tracing"
//	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
//    app_pb "golang.52tt.com/protocol/app/newbie_page_logic"
//	pb "golang.52tt.com/protocol/services/logicsvr-go/newbiepagelogic"
//	"google.golang.org/grpc"
//)
//
//const (
//	serviceName = "newbie-page-logic"
//)
//
//type Client struct {
//	client.BaseClient
//}
//
//func newClient(dopts ...grpc.DialOption) (*Client, error) {
//	return &Client{
//		BaseClient: client.NewInsecureGRPCClient(
//			serviceName,
//			func(cc *grpc.ClientConn) interface{} {
//				return pb.NewNewbiePageLogicClient(cc)
//			}, dopts...,
//		),
//	}, nil
//}
//
//func (c *Client) typedStub() pb.NewbiePageLogicClient { return c.Stub().(pb.NewbiePageLogicClient) }
//
//func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
//	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
//		tracing.UsingTracer(t), tracing.LogPayloads(true),
//	))
//	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
//	return newClient(dopts...)
//}
//
//func NewClient(dopts ...grpc.DialOption) (*Client, error) {
//	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
//	return newClient(dopts...)
//}
//
//func (c *Client) GetNewbiePageLogic(ctx context.Context, req app_pb.NewbiePageLogicReq) (*app_pb.NewbiePageLogicResp, protocol.ServerError) {
//	resp, err := c.typedStub().GetHello(ctx, &req)
//	return resp, protocol.ToServerError(err)
//}
