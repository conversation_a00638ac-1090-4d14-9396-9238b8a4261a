package newbiepagelogic

//import (
//	"context"
//	. "github.com/smartystreets/goconvey/convey"
//	pb "golang.52tt.com/protocol/app/newbiepagelogic"
//	"google.golang.org/grpc/grpclog"
//	"os"
//	"testing"
//)
//
//func init() {
//	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
//}
//
//func TestGetHelloWorld(t *testing.T) {
//
//	Con<PERSON>("GetNewbiePageLogic", t, func() {
//		client, err := NewClient()
//		So(err, ShouldBeNil)
//		var req pb.NewbiePageLogicReq
//		resp, err := client.GetNewbiePageLogic(context.Background(), req)
//		So(err, ShouldBeNil)
//
//		t.Logf("GetNewbiePageLogic %+v", resp)
//	})
//
//}
