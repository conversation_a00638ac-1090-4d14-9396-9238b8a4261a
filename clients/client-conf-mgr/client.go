package client_conf_mgr

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/client-conf-mgr"
	"google.golang.org/grpc"
)

const (
	serviceName = "client-conf-mgr"
)

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewClientConfSyncClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ClientConfSyncClient {
	return c.Stub().(pb.ClientConfSyncClient)
}

func (c *Client) CheckConfFileUpdate(ctx context.Context, clientType, appid, marketid uint32, files []*pb.CheckFileInfo) ([]*pb.ConfFile, protocol.ServerError) {
	resp, err := c.typedStub().CheckConfFileUpdate(ctx, &pb.CheckConfFileUpdateReq{
		Files:                files,
		ClientType:           clientType,
		Appid:                appid,
		Marketid:             marketid,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.Files, nil
}

func (c *Client) GetAllUpdateConfList(ctx context.Context,clientType, appid, marketid uint32, seq uint64) (uint64, uint64, []*pb.ConfFile, protocol.ServerError){
	resp, err := c.typedStub().GetAllUpdateConfList(ctx, &pb.GetAllUpdateConfListReq{
		Seq:                  seq,
		ClientType:           clientType,
		Appid:                appid,
		Marketid:             marketid,
	})
	if err != nil{
		return 0, 0, nil, protocol.ToServerError(err)
	}
	return resp.GetCurMaxSeq(), resp.GetMaxSeq(), resp.GetFiles(), nil
}

func (c *Client) CheckAnnouncementUpdate(ctx context.Context) (string, protocol.ServerError){
	resp, err := c.typedStub().CheckAnnouncementUpdate(ctx, &pb.CheckAnnouncementUpdateReq{
	})
	if err != nil{
		return "", protocol.ToServerError(err)
	}
	return resp.GetMd5(), nil
}

//----------------------------------------------------------
func NewConfClient(dopts ...grpc.DialOption) (*ConfClient, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newConfClient(dopts...)
}

type ConfClient struct {
	client.BaseClient
}

func newConfClient(dopts ...grpc.DialOption) (*ConfClient, error) {
	return &ConfClient{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewClientConfCreateClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *ConfClient) typedStubConf() pb.ClientConfCreateClient {
	return c.Stub().(pb.ClientConfCreateClient)
}

func (c *ConfClient) CreateConfFile(ctx context.Context, content []byte, file_name, opt_name, comment string, client_type, appid, marketid uint32) (string, error) {
	resp, err := c.typedStubConf().CreateConfFile(ctx, &pb.CreateConfFileReq{
		FileName:             file_name,
		ClientType:           client_type,
		Appid:                appid,
		Markteid:             marketid,
		OptName:              opt_name,
		Comment:              comment,
		Content:              content,
	})
	if err != nil {
		return "", protocol.ToServerError(err)
	}

	return resp.GetUrl(), nil
}

func (c *ConfClient) ModifyConfFile(ctx context.Context, content []byte, file_name, opt_name, comment string, id uint64) (string, error) {
	resp, err := c.typedStubConf().ModifyConfFile(ctx, &pb.ModifyConfFileReq{
		Id:                   id,
		FileName:             file_name,
		OptName:              opt_name,
		Comment:              comment,
		Content:              content,
	})
	if err != nil {
		return "", protocol.ToServerError(err)
	}

	return resp.GetUrl(), nil
}

func (c *ConfClient) ModifyAnnouncement(ctx context.Context, content []byte, opt_name string) (string, error) {
	resp, err := c.typedStubConf().ModifyAnnouncement(ctx, &pb.ModifyAnnouncementReq{
		OptName:              opt_name,
		Content:              content,
	})
	if err != nil {
		return "", protocol.ToServerError(err)
	}

	return resp.GetUrl(), nil
}

func (c *ConfClient) GetAnnouncement(ctx context.Context) (string, string, error) {
	resp, err := c.typedStubConf().GetAnnouncement(ctx, &pb.GetAnnouncementReq{
	})
	if err != nil {
		return "", "", protocol.ToServerError(err)
	}

	return resp.GetUrl(), resp.GetMd5(), nil
}
