package HeadImage

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"testing"
)

func TestClient_BatchGetHeadImageMd5(t *testing.T) {
	accounts := make([]string, 101)
	for i := 0; i < 101; i++ {
		accounts[i] = "ezreal"
	}

	c := NewClient(grpc.WithInsecure(), grpc.WithBlock(), grpc.WithAuthority("headimage.52tt.local"))
	m, err := c.BatchGetHeadImageMd5(context.Background(), 500001, accounts)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("BatchGetHeadImageMd5: %v", err)
	}
	t.Logf("BatchGetHeadImageMd5: %v", m)
}

func TestNewClient(t *testing.T) {
	c := NewClient(grpc.WithInsecure(), grpc.WithBlock(), grpc.WithAuthority("headimage.52tt.local"))
	x, err := c.GetHeadImageMd5(context.Background(), 500001, "tt10010444")
	if err != nil {
		t.Error(err)
	}
	t.Log(x)

	m, err := c.BatchGetHeadImageMd5(context.Background(), 500001, []string{"tt221496", "tt10010444", "tt198563586"})
	if err != nil {
		t.Error(err)
	}
	t.Log(m)
}
