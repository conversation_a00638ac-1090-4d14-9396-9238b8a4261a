package channelguild

import (
	"context"
	"strconv"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/channelguildsvr"
)

const (
	serviceName = "channelguild"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewChannelGuildClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.ChannelGuildClient { return c.Stub().(pb.ChannelGuildClient) }

// GetChannelGuildList
// Deprecated: use channelguild_go.GetChannelGuildList
func (c *Client) GetChannelGuildList(ctx context.Context, guildId uint32, channelType uint32) ([]uint32, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(guildId))))
	req := &pb.GetChannelGuildListReq{
		Type:    channelType,
		GuildId: guildId,
		Size_:   50,
	}
	rsp, err := c.typedStub().GetChannelGuildList(ctx, req)
	return rsp.GetChannelIds(), err
}

// GetChannelGuildTotal
// Deprecated: use channelguild_go.GetChannelGuildTotal
func (c *Client) GetChannelGuildTotal(ctx context.Context, guildId uint32, channelType uint32) (uint32, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(guildId))))
	req := &pb.GetChannelGuildListReq{
		Type:    channelType,
		GuildId: guildId,
		Size_:   50,
	}
	rsp, err := c.typedStub().GetChannelGuildList(ctx, req)
	return rsp.GetTotal(), err
}

// CreateGuildPubChannel
// Deprecated: use channelguild_go.CreateGuildPubChannel
func (c *Client) CreateGuildPubChannel(ctx context.Context, guildId uint32, uid uint32) (*pb.CreateGuildPubChannelResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	req := &pb.CreateGuildPubChannelReq{
		GuildOwner: uid,
		GuildId:    guildId,
	}
	resp, err := c.typedStub().CreateGuildPubChannel(ctx, req)
	return resp, err
}
