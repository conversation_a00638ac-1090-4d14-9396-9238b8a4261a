package unified_pay_rmb

import (
	client "golang.52tt.com/pkg/client"
	face_recognition_scene "golang.52tt.com/pkg/face-recognition-scene"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	protocol "golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	basePb "golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/common/status"
	unified_pay_rmb "golang.52tt.com/protocol/services/unified-pay-rmb"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "unified-pay-rmb"
)

// Client is the wrapper-client for UnifiedPayRmb client.
type Client struct {
	client.BaseClient
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return unified_pay_rmb.NewUnifiedPayRmbClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of UnifiedPayRmbClient.
func (c *Client) typedStub() unified_pay_rmb.UnifiedPayRmbClient {
	return c.Stub().(unified_pay_rmb.UnifiedPayRmbClient)
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// RmbPayOrder
func (c *Client) RmbPayOrder(ctx context.Context, req *unified_pay_rmb.RmbPayOrderReq, opts ...grpc.CallOption) (*unified_pay_rmb.RmbPayOrderRsp, protocol.ServerError) {
	resp, err := c.typedStub().RmbPayOrder(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetRmbPayOrderInfo
func (c *Client) GetRmbPayOrderInfo(ctx context.Context, req *unified_pay_rmb.GetRmbPayOrderInfoReq, opts ...grpc.CallOption) (*unified_pay_rmb.GetRmbPayOrderInfoRsp, protocol.ServerError) {
	resp, err := c.typedStub().GetRmbPayOrderInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateRmbPayOrderInfo(ctx context.Context, info *unified_pay_rmb.NoFinishOrderInfo, opts ...grpc.CallOption) protocol.ServerError {
	req := &unified_pay_rmb.UpdateRmbPayOrderInfoReq{
		OrderInfo: info,
	}
	_, err := c.typedStub().UpdateRmbPayOrderInfo(ctx, req, opts...)
	return protocol.ToServerError(err)
}

func (c *Client) GetNoFinishCbOrderList(ctx context.Context, orderTime uint32, limit uint32, opts ...grpc.CallOption) (*unified_pay_rmb.GetNoFinishCbOrderListRsp, protocol.ServerError) {
	req := &unified_pay_rmb.GetNoFinishCbOrderListReq{
		OrderTime: orderTime,
		Limit:     limit,
	}
	resp, err := c.typedStub().GetNoFinishCbOrderList(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CheckRmbPayRiskOrigin(ctx context.Context, req *unified_pay_rmb.CheckRmbPayRiskReq, opts ...grpc.CallOption) (*unified_pay_rmb.CheckRmbPayRiskRsp, protocol.ServerError) {
	resp, err := c.typedStub().CheckRmbPayRisk(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

//前置风控检查
func (c *Client) CheckRmbPayRisk(ctx context.Context, riskArgs *unified_pay_rmb.RmbRiskArgs, serviceInfo *protogrpc.ServiceInfo, baseReq *basePb.BaseReq, opts ...grpc.CallOption) (*basePb.BaseResp, protocol.ServerError) {
	baseRsp := &basePb.BaseResp{}
	req := &unified_pay_rmb.CheckRmbPayRiskReq{
		RiskArgs:      riskArgs,
		UserIp:        serviceInfo.ClientIPAddr().String(),
		DeviceId:      serviceInfo.DeviceID,
		RequestId:     serviceInfo.RequestID,
		ClientType:    uint32(serviceInfo.ClientType),
		ClientVersion: serviceInfo.ClientVersion,
		MarketId:      serviceInfo.MarketID,
	}

	_, osType, _ := protocol.UnPackTerminalType(serviceInfo.TerminalType)
	switch osType {
	case protocol.ANDROID:
		req.OsType = "a"
	case protocol.IOS:
		req.OsType = "i"
	default:
	}
	if baseReq.GetFaceAuthInfo() != nil {
		req.FaceResultToken = baseReq.GetFaceAuthInfo().GetResultToken()
	}

	resp, err := c.CheckRmbPayRiskOrigin(ctx, req, opts...)
	if err == nil {
		return baseRsp, nil
	}
	if err.Code() == int(status.ErrUnifiedPayRmbRiskNeedFace) { //需要人脸识别
		if baseRsp.FaceAuthInfo == nil {
			baseRsp.FaceAuthInfo = &basePb.FaceAuthInfo{}
		}
		baseRsp.FaceAuthInfo.Scene = resp.FaceScene
		baseRsp.FaceAuthInfo.BizScene = face_recognition_scene.NonageConsume

		if len(resp.FaceAuthContextJson) > 0 {
			baseRsp.ErrInfo = []byte(resp.FaceAuthContextJson)
		}
	}
	return baseRsp, err
}
