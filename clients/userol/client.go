package userol

import (
	"context"

	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	"google.golang.org/grpc"

	"golang.52tt.com/clients/user-online"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	v2Pb "golang.52tt.com/protocol/services/user-online"
	pb "golang.52tt.com/protocol/services/userolsvr"
)

const (
	serviceName = "userol"
)

type Client struct {
	client.BaseClient
	v2 *user_online.Client
}

// Deprecated: 改用 "golang.52tt.com/clients/user-online"
func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewClient2(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient2(dopts...)
}

// Deprecated: 改用 "golang.52tt.com/clients/user-online"
func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}
func newClient2(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewUserOLClient(cc)
			}, dopts...,
		),
	}, nil
}
func newClient(dopts ...grpc.DialOption) *Client {
	v2, _ := user_online.NewClient(dopts...)

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		v2: v2,
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewUserOLClient(cc)
		}, dopts...),
	}
}

func (c *Client) typedStub() pb.UserOLClient { return c.Stub().(pb.UserOLClient) }

// Deprecated: 改用 "golang.52tt.com/clients/user-online"
// 最近一次手机上线信息
func (c *Client) GetLastMobileOnlineInfo(ctx context.Context, uid uint32) (*pb.UserOnlineInfo, protocol.ServerError) {
	infos, err := c.GetLastOnlineInfo(ctx, []uint32{uid}, protocol.MOBILE)
	if err != nil {
		return nil, err
	}

	if info, ok := infos[uid]; ok {
		return info, nil
	}

	// NOTE: 记录不存在
	return &pb.UserOnlineInfo{}, nil
}

// Deprecated: 改用 "golang.52tt.com/clients/user-online"
func (c *Client) BatchGetLastMobileOnlineInfo(ctx context.Context, uids []uint32) (map[uint32]*pb.UserOnlineInfo, protocol.ServerError) {
	return c.GetLastOnlineInfo(ctx, uids, protocol.MOBILE)
}

// Deprecated: 改用 "golang.52tt.com/clients/user-online"
// 最近一次上线信息
func (c *Client) GetLastOnlineInfo(ctx context.Context, uids []uint32, platform protocol.Platform) (map[uint32]*pb.UserOnlineInfo, protocol.ServerError) {
	multiInfos, err := c.BatchGetLastMultiOnlineInfo(ctx, uids)
	if err != nil {
		return nil, err
	}

	infos := make(map[uint32]*pb.UserOnlineInfo)
	for _, multiInfo := range multiInfos {
		for _, info := range multiInfo.GetInfoList() {
			p, _, _ := protocol.UnPackTerminalType(info.TerminalType)
			if platform == p {
				infos[info.Uid] = info
			}
		}
	}
	return infos, nil
}

// Deprecated: 改用 "golang.52tt.com/clients/user-online"
// 最近多端同时在线信息
func (c *Client) BatchGetLastMultiOnlineInfo(ctx context.Context, uids []uint32) ([]*pb.UserMultiOnlineInfo, protocol.ServerError) {
	opt := &v2Pb.Option{
		PlatformTypeList: []uint32{
			uint32(protocol.PC),
			uint32(protocol.MOBILE),
			uint32(protocol.QQ_EMBEDED),
			uint32(protocol.WECHAT_EMBEDED),
		},
	}
	multiInfoV2s, err := c.v2.BatchGetMultiOnlineInfo(ctx, uids, opt)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	multiInfos := make([]*pb.UserMultiOnlineInfo, 0, len(multiInfoV2s))
	for _, multiInfoV2 := range multiInfoV2s {
		multiInfo := &pb.UserMultiOnlineInfo{
			Uid:      multiInfoV2.Uid,
			InfoList: make([]*pb.UserOnlineInfo, 0, len(multiInfoV2.OnlineInfoList)),
		}
		for _, infoV2 := range multiInfoV2.OnlineInfoList {
			info := &pb.UserOnlineInfo{
				Uid:           infoV2.Uid,
				OnlineAt:      uint64(infoV2.OnlineAt) * 1000,
				OfflineAt:     uint64(infoV2.OfflineAt) * 1000,
				ClientIp:      infoV2.ClientIp,
				DeviceId:      infoV2.DeviceIdHex,
				TerminalType:  infoV2.TerminalType,
				DeviceModel:   infoV2.DeviceModel,
				MarketId:      infoV2.MarketId,
				ClientVersion: infoV2.ClientVersion,
			}
			multiInfo.InfoList = append(multiInfo.InfoList, info)
		}
		multiInfos = append(multiInfos, multiInfo)
	}

	return multiInfos, nil
}

// Deprecated: 改用 "golang.52tt.com/clients/user-online"
func (c *Client) GetLastMultiOnlineInfo(ctx context.Context, uid uint32) (*pb.UserMultiOnlineInfo, protocol.ServerError) {

	infos, err := c.BatchGetLastMultiOnlineInfo(ctx, []uint32{uid})
	if err != nil {
		return nil, err
	}

	for _, info := range infos {
		if info.GetUid() == uid {
			return info, nil
		}
	}

	// NOTE: 记录不存在
	return &pb.UserMultiOnlineInfo{}, nil
}

func (c *Client) GetUserOnlineLog(ctx context.Context, uid uint32, from, to uint64, limit uint32) (*pb.GetUserOnlineLogResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserOnlineLog(ctx, &pb.GetUserOnlineLogReq{
		Uid:     uid,
		BeginAt: from,
		EndAt:   to,
		Limit:   limit,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}
