package risk_mng_api

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	"golang.52tt.com/protocol/app"
	pb "golang.52tt.com/protocol/services/risk-mng-api"
	"google.golang.org/grpc"
)

const (
	serviceName = "risk-mng-api"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewRiskMngApiClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.RiskMngApiClient {
	return c.Stub().(pb.RiskMngApiClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

// Check 默认全功能检查，特殊场景要求可配置
func (c *Client) Check(ctx context.Context, in *pb.CheckReq) (*pb.CheckResp, protocol.ServerError) {
	resp, err := c.typedStub().Check(ctx, in)
	return resp, protocol.ToServerError(err)
}

// BatchCheck 默认全功能批量检查，特殊场景要求可配置
func (c *Client) BatchCheck(ctx context.Context, in *pb.BatchCheckReq) (*pb.BatchCheckResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchCheck(ctx, in)
	return resp, protocol.ToServerError(err)
}

// CheckHelper 自动填充 Check 默认字段
func (c *Client) CheckHelper(ctx context.Context, req *pb.CheckReq, baseReq *app.BaseReq) (*pb.CheckResp, protocol.ServerError) {

	if baseReq == nil {
		return c.Check(ctx, req)
	}

	faceAuthInfo := baseReq.GetFaceAuthInfo()
	if faceAuthInfo != nil {
		if req.SourceEntity == nil {
			req.SourceEntity = &pb.Entity{}
		}
		req.SourceEntity.FaceAuthInfo = &pb.FaceAuthInfo{
			Token:              faceAuthInfo.GetToken(),
			ProviderCode:       faceAuthInfo.GetProviderCode(),
			ProviderResultData: faceAuthInfo.GetProviderResultData(),
			ResultToken:        faceAuthInfo.GetResultToken(),
		}
	}

	riskCommInfo := baseReq.GetRiskCommInfo()
	if riskCommInfo != nil {
		if req.RiskCommInfo == nil {
			req.RiskCommInfo = &pb.RiskCommInfo{}
		}
		req.RiskCommInfo.IgnoreWarning = baseReq.GetRiskCommInfo().GetIgnoreWarning()
	}

	return c.Check(ctx, req)
}

// CheckUserBlack 用户黑产检查，默认查 darkserver，特殊场景要求可配置
func (c *Client) CheckUserBlack(ctx context.Context, scene string, uidList []uint32) (map[uint32]bool, protocol.ServerError) {
	resp, err := c.typedStub().CheckUserBlack(ctx, &pb.CheckUserBlackReq{
		UidList: uidList,
		Scene:   scene,
	})
	return resp.GetUserBlackMap(), protocol.ToServerError(err)
}

// CheckUserBan 用户封禁检查
func (c *Client) CheckUserBan(ctx context.Context, in *pb.CheckUserBanReq) (map[uint32]bool, protocol.ServerError) {
	resp, err := c.typedStub().CheckUserBan(ctx, in)
	return resp.GetUserBanMap(), protocol.ToServerError(err)
}

// CheckUserBanHelper 用户封禁检查，只检查 uid 的
func (c *Client) CheckUserBanHelper(ctx context.Context, scene string, uidList []uint32) (map[uint32]bool, protocol.ServerError) {

	reqEntityList := make([]*pb.Entity, 0, len(uidList))
	for _, uid := range uidList {
		reqEntity := &pb.Entity{
			Uid: uid,
		}

		reqEntityList = append(reqEntityList, reqEntity)
	}

	resp, err := c.typedStub().CheckUserBan(ctx, &pb.CheckUserBanReq{
		Scene:        scene,
		IdEntityList: reqEntityList,
	})

	return resp.GetUserBanMap(), protocol.ToServerError(err)
}

// CheckChannel 房间检查
func (c *Client) CheckChannel(ctx context.Context, scene string, channelIdList []uint32) (map[uint32]bool, protocol.ServerError) {
	resp, err := c.typedStub().CheckChannel(ctx, &pb.CheckChannelReq{
		ChannelIdList: channelIdList,
		Scene:         scene,
	})
	return resp.GetChannelHitMap(), protocol.ToServerError(err)
}

func (c *Client) SetUserActionRule(ctx context.Context, req *pb.SetUserActionRuleReq) (*pb.SetUserActionRuleResp, error) {
	resp, err := c.typedStub().SetUserActionRule(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}

func (c *Client) GetUserActionRule(ctx context.Context, req *pb.GetUserActionRuleReq) (*pb.GetUserActionRuleResp, error) {
	resp, err := c.typedStub().GetUserActionRule(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}
