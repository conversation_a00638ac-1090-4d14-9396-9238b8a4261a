// Code generated by MockGen. DO NOT EDIT.
// Source: iclient.go

// Package content is a generated GoMock package.
package content

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	musepost "golang.52tt.com/protocol/services/musepost"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddLikeContentHistory mocks base method.
func (m *MockIClient) AddLikeContentHistory(ctx context.Context, req *musepost.AddLikeContentHistoryReq, opts ...grpc.CallOption) (*musepost.AddLikeContentHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddLikeContentHistory", varargs...)
	ret0, _ := ret[0].(*musepost.AddLikeContentHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddLikeContentHistory indicates an expected call of AddLikeContentHistory.
func (mr *MockIClientMockRecorder) AddLikeContentHistory(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLikeContentHistory", reflect.TypeOf((*MockIClient)(nil).AddLikeContentHistory), varargs...)
}

// DeleteContent mocks base method.
func (m *MockIClient) DeleteContent(ctx context.Context, req *musepost.DeleteContentReq, opts ...grpc.CallOption) (*musepost.DeleteContentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteContent", varargs...)
	ret0, _ := ret[0].(*musepost.DeleteContentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteContent indicates an expected call of DeleteContent.
func (mr *MockIClientMockRecorder) DeleteContent(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteContent", reflect.TypeOf((*MockIClient)(nil).DeleteContent), varargs...)
}

// DeleteLikeContentHistory mocks base method.
func (m *MockIClient) DeleteLikeContentHistory(ctx context.Context, req *musepost.DeleteLikeContentHistoryReq, opts ...grpc.CallOption) (*musepost.DeleteLikeContentHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteLikeContentHistory", varargs...)
	ret0, _ := ret[0].(*musepost.DeleteLikeContentHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteLikeContentHistory indicates an expected call of DeleteLikeContentHistory.
func (mr *MockIClientMockRecorder) DeleteLikeContentHistory(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLikeContentHistory", reflect.TypeOf((*MockIClient)(nil).DeleteLikeContentHistory), varargs...)
}

// DescCommentCount mocks base method.
func (m *MockIClient) DescCommentCount(ctx context.Context, req *musepost.DescCommentCountReq, opts ...grpc.CallOption) (*musepost.DescCommentCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescCommentCount", varargs...)
	ret0, _ := ret[0].(*musepost.DescCommentCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescCommentCount indicates an expected call of DescCommentCount.
func (mr *MockIClientMockRecorder) DescCommentCount(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescCommentCount", reflect.TypeOf((*MockIClient)(nil).DescCommentCount), varargs...)
}

// DescLikeCount mocks base method.
func (m *MockIClient) DescLikeCount(ctx context.Context, req *musepost.DescLikeCountReq, opts ...grpc.CallOption) (*musepost.DescLikeCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescLikeCount", varargs...)
	ret0, _ := ret[0].(*musepost.DescLikeCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescLikeCount indicates an expected call of DescLikeCount.
func (mr *MockIClientMockRecorder) DescLikeCount(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescLikeCount", reflect.TypeOf((*MockIClient)(nil).DescLikeCount), varargs...)
}

// GetCommentByIds mocks base method.
func (m *MockIClient) GetCommentByIds(ctx context.Context, req *musepost.GetCommentByIdsReq, opts ...grpc.CallOption) (*musepost.GetCommentByIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCommentByIds", varargs...)
	ret0, _ := ret[0].(*musepost.GetCommentByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommentByIds indicates an expected call of GetCommentByIds.
func (mr *MockIClientMockRecorder) GetCommentByIds(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommentByIds", reflect.TypeOf((*MockIClient)(nil).GetCommentByIds), varargs...)
}

// GetInteractiveContentCount mocks base method.
func (m *MockIClient) GetInteractiveContentCount(ctx context.Context, req *musepost.GetInteractiveContentCountReq, opts ...grpc.CallOption) (*musepost.GetInteractiveContentCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInteractiveContentCount", varargs...)
	ret0, _ := ret[0].(*musepost.GetInteractiveContentCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInteractiveContentCount indicates an expected call of GetInteractiveContentCount.
func (mr *MockIClientMockRecorder) GetInteractiveContentCount(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInteractiveContentCount", reflect.TypeOf((*MockIClient)(nil).GetInteractiveContentCount), varargs...)
}

// GetInteractiveMsgHistory mocks base method.
func (m *MockIClient) GetInteractiveMsgHistory(ctx context.Context, req *musepost.GetInteractiveMsgHistoryReq, opts ...grpc.CallOption) (*musepost.GetInteractiveMsgHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInteractiveMsgHistory", varargs...)
	ret0, _ := ret[0].(*musepost.GetInteractiveMsgHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInteractiveMsgHistory indicates an expected call of GetInteractiveMsgHistory.
func (mr *MockIClientMockRecorder) GetInteractiveMsgHistory(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInteractiveMsgHistory", reflect.TypeOf((*MockIClient)(nil).GetInteractiveMsgHistory), varargs...)
}

// GetLikeContentHistory mocks base method.
func (m *MockIClient) GetLikeContentHistory(ctx context.Context, req *musepost.GetLikeContentHistoryReq, opts ...grpc.CallOption) (*musepost.GetLikeContentHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLikeContentHistory", varargs...)
	ret0, _ := ret[0].(*musepost.GetLikeContentHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLikeContentHistory indicates an expected call of GetLikeContentHistory.
func (mr *MockIClientMockRecorder) GetLikeContentHistory(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLikeContentHistory", reflect.TypeOf((*MockIClient)(nil).GetLikeContentHistory), varargs...)
}

// GetPostById mocks base method.
func (m *MockIClient) GetPostById(ctx context.Context, postId string, opts ...grpc.CallOption) (*musepost.MusePost, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, postId}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPostById", varargs...)
	ret0, _ := ret[0].(*musepost.MusePost)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostById indicates an expected call of GetPostById.
func (mr *MockIClientMockRecorder) GetPostById(ctx, postId interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, postId}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostById", reflect.TypeOf((*MockIClient)(nil).GetPostById), varargs...)
}

// GetPostByIds mocks base method.
func (m *MockIClient) GetPostByIds(ctx context.Context, req *musepost.GetPostByIdsReq, opts ...grpc.CallOption) (*musepost.GetPostByIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPostByIds", varargs...)
	ret0, _ := ret[0].(*musepost.GetPostByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostByIds indicates an expected call of GetPostByIds.
func (mr *MockIClientMockRecorder) GetPostByIds(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostByIds", reflect.TypeOf((*MockIClient)(nil).GetPostByIds), varargs...)
}

// IncrCommentCount mocks base method.
func (m *MockIClient) IncrCommentCount(ctx context.Context, req *musepost.IncrCommentCountReq, opts ...grpc.CallOption) (*musepost.IncrCommentCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IncrCommentCount", varargs...)
	ret0, _ := ret[0].(*musepost.IncrCommentCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrCommentCount indicates an expected call of IncrCommentCount.
func (mr *MockIClientMockRecorder) IncrCommentCount(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrCommentCount", reflect.TypeOf((*MockIClient)(nil).IncrCommentCount), varargs...)
}

// IncrInteractiveMsgUnreadCount mocks base method.
func (m *MockIClient) IncrInteractiveMsgUnreadCount(ctx context.Context, req *musepost.IncrInteractiveMsgUnreadCountReq, opts ...grpc.CallOption) (*musepost.IncrInteractiveMsgUnreadCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IncrInteractiveMsgUnreadCount", varargs...)
	ret0, _ := ret[0].(*musepost.IncrInteractiveMsgUnreadCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrInteractiveMsgUnreadCount indicates an expected call of IncrInteractiveMsgUnreadCount.
func (mr *MockIClientMockRecorder) IncrInteractiveMsgUnreadCount(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrInteractiveMsgUnreadCount", reflect.TypeOf((*MockIClient)(nil).IncrInteractiveMsgUnreadCount), varargs...)
}

// IncrLikeCount mocks base method.
func (m *MockIClient) IncrLikeCount(ctx context.Context, req *musepost.IncrLikeCountReq, opts ...grpc.CallOption) (*musepost.IncrLikeCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IncrLikeCount", varargs...)
	ret0, _ := ret[0].(*musepost.IncrLikeCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrLikeCount indicates an expected call of IncrLikeCount.
func (mr *MockIClientMockRecorder) IncrLikeCount(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrLikeCount", reflect.TypeOf((*MockIClient)(nil).IncrLikeCount), varargs...)
}

// MarkInteractiveMsgRead mocks base method.
func (m *MockIClient) MarkInteractiveMsgRead(ctx context.Context, req *musepost.MarkInteractiveMsgReadReq, opts ...grpc.CallOption) (*musepost.MarkInteractiveMsgReadResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MarkInteractiveMsgRead", varargs...)
	ret0, _ := ret[0].(*musepost.MarkInteractiveMsgReadResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MarkInteractiveMsgRead indicates an expected call of MarkInteractiveMsgRead.
func (mr *MockIClientMockRecorder) MarkInteractiveMsgRead(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkInteractiveMsgRead", reflect.TypeOf((*MockIClient)(nil).MarkInteractiveMsgRead), varargs...)
}

// MarkPostRead mocks base method.
func (m *MockIClient) MarkPostRead(ctx context.Context, req *musepost.MarkPostReadReq, opts ...grpc.CallOption) (*musepost.MarkPostReadResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MarkPostRead", varargs...)
	ret0, _ := ret[0].(*musepost.MarkPostReadResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MarkPostRead indicates an expected call of MarkPostRead.
func (mr *MockIClientMockRecorder) MarkPostRead(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkPostRead", reflect.TypeOf((*MockIClient)(nil).MarkPostRead), varargs...)
}

// PublishComment mocks base method.
func (m *MockIClient) PublishComment(ctx context.Context, req *musepost.PublishCommentReq, opts ...grpc.CallOption) (*musepost.PublishCommentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PublishComment", varargs...)
	ret0, _ := ret[0].(*musepost.PublishCommentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PublishComment indicates an expected call of PublishComment.
func (mr *MockIClientMockRecorder) PublishComment(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishComment", reflect.TypeOf((*MockIClient)(nil).PublishComment), varargs...)
}

// PublishPost mocks base method.
func (m *MockIClient) PublishPost(ctx context.Context, req *musepost.PublishPostReq, opts ...grpc.CallOption) (*musepost.PublishPostResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PublishPost", varargs...)
	ret0, _ := ret[0].(*musepost.PublishPostResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PublishPost indicates an expected call of PublishPost.
func (mr *MockIClientMockRecorder) PublishPost(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishPost", reflect.TypeOf((*MockIClient)(nil).PublishPost), varargs...)
}

// UpdateCommentStatus mocks base method.
func (m *MockIClient) UpdateCommentStatus(ctx context.Context, req *musepost.UpdateCommentStatusReq, opts ...grpc.CallOption) (*musepost.UpdateCommentStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCommentStatus", varargs...)
	ret0, _ := ret[0].(*musepost.UpdateCommentStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCommentStatus indicates an expected call of UpdateCommentStatus.
func (mr *MockIClientMockRecorder) UpdateCommentStatus(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCommentStatus", reflect.TypeOf((*MockIClient)(nil).UpdateCommentStatus), varargs...)
}

// UpdatePostStatus mocks base method.
func (m *MockIClient) UpdatePostStatus(ctx context.Context, req *musepost.UpdatePostStatusReq, opts ...grpc.CallOption) (*musepost.UpdatePostStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePostStatus", varargs...)
	ret0, _ := ret[0].(*musepost.UpdatePostStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePostStatus indicates an expected call of UpdatePostStatus.
func (mr *MockIClientMockRecorder) UpdatePostStatus(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePostStatus", reflect.TypeOf((*MockIClient)(nil).UpdatePostStatus), varargs...)
}
