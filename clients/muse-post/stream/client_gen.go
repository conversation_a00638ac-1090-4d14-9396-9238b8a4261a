// Code generated by protoc-gen-go-quicksilver. DO NOT EDIT.

package stream

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	protocol "golang.52tt.com/pkg/protocol"
	musepost "golang.52tt.com/protocol/services/musepost"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "muse-post"
)

// Client is the wrapper-client for MuseContentSvr client.
type Client struct {
	client.BaseClient
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return musepost.NewMuseStreamSvrClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of MuseContentSvrClient.
func (c *Client) typedStub() musepost.MuseStreamSvrClient {
	return c.Stub().(musepost.MuseStreamSvrClient)
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// NewClientOrg creates a new Client with wrapper-client instance.
func NewClientOrg(dopts ...grpc.DialOption) (musepost.MuseStreamSvrClient, error) {
	c, err := NewClient(dopts...)
	if err != nil {
		return nil, err
	}
	return c, nil
}

func (c *Client) InsertActivities(ctx context.Context, req *musepost.InsertActivitiesReq, opts ...grpc.CallOption) (*musepost.InsertActivitiesResp, error) {
	resp, err := c.typedStub().InsertActivities(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetActivitiesByRange(ctx context.Context, req *musepost.GetActivitiesByRangeReq, opts ...grpc.CallOption) (*musepost.GetActivitiesByRangeResp, error) {
	resp, err := c.typedStub().GetActivitiesByRange(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) IsActivityKeysExisting(ctx context.Context, req *musepost.IsActivityKeysExistingReq, opts ...grpc.CallOption) (*musepost.IsActivityKeysExistResp, error) {
	resp, err := c.typedStub().IsActivityKeysExisting(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) RemoveActivities(ctx context.Context, req *musepost.RemoveActivitiesReq, opts ...grpc.CallOption) (*musepost.RemoveActivitiesResp, error) {
	resp, err := c.typedStub().RemoveActivities(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) TrimActivitiesByCapacity(ctx context.Context, req *musepost.TrimActivitiesByCapacityReq, opts ...grpc.CallOption) (*musepost.TrimActivitiesByCapacityResp, error) {
	resp, err := c.typedStub().TrimActivitiesByCapacity(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetActivitiesByRange(ctx context.Context, req *musepost.BatchGetActivitiesByRangeReq, opts ...grpc.CallOption) (*musepost.BatchGetActivitiesByRangeResp, error) {
	resp, err := c.typedStub().BatchGetActivitiesByRange(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}
