package channeldrawgame

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"

	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"

	pb "golang.52tt.com/protocol/services/channeldrawgame"
)

const (
	serviceName = "channeldrawgame"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewChanneldrawgameClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.ChanneldrawgameClient { return c.Stub().(pb.ChanneldrawgameClient) }

func (c *Client) GetPicture(ctx context.Context, uin uint32, channelID uint32) (*pb.GetPictureResp, protocol.ServerError) {
	in := &pb.GetPictureReq{
		ChannelID: channelID,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	out, err := c.typedStub().GetPicture(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return out, nil
}

func (c *Client) GetBoardStatus(ctx context.Context, uin uint32, channelID uint32) (*pb.GetBoardStatusResp, protocol.ServerError) {
	in := &pb.GetBoardStatusReq{
		ChannelID: channelID,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	out, err := c.typedStub().GetBoardStatus(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return out, nil
}

func (c *Client) RemovePicture(ctx context.Context, channelId uint32) (*pb.RemovePictureResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	out, err := c.typedStub().RemovePicture(ctx, &pb.RemovePictureReq{ChannelID: channelId})
	return out, protocol.ToServerError(err)
}

func (c *Client) SetBoardStatus(ctx context.Context, channelId, status uint32) (*pb.SetBoardStatusResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(0))))
	in := &pb.SetBoardStatusReq{
		ChannelID: channelId,
		Status:    status,
	}
	out, err := c.typedStub().SetBoardStatus(ctx, in)
	return out, protocol.ToServerError(err)
}
