// Code generated by quicksilver-cli. DO NOT EDIT.
package melee_channel

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/melee-channel"
)

type IClient interface {
	client.BaseClient
	AddChannelRoomApplyList(ctx context.Context, channelId, tabId, uid uint32, msg string, enterRoomType int32) (out *pb.AddChannelRoomApplyListResp,err error)
	AddChannelRoomWhiteList(ctx context.Context, channelId uint32, tabId uint32, whitelist []uint32) (out *pb.AddChannelRoomWhiteListResp,err error)
	AddToMicWhiteList(ctx context.Context, uids []uint32, channelId uint32) (out *pb.AddToMicWhiteListResp,err error)
	ApplyToMicWhiteList(ctx context.Context, uid, channelId uint32) (out *pb.ApplyToMicWhiteListResp,err error)
	GetChannelRoomApplyList(ctx context.Context, channelId, tabId, limit uint32, lastTimeMs uint64) (out *pb.GetChannelRoomApplyListResp,err error)
	GetChannelRoomWhitelist(ctx context.Context, channelId uint32, tabId, limit uint32, lastTimeMs uint64) (out *pb.GetChannelRoomWhitelistResp,err error)
	GetMicWhiteApplyCount(ctx context.Context, channelId uint32) (out *pb.GetMicWhiteApplyCountResp,err error)
	GetMicWhiteApplyList(ctx context.Context, channelId, limit, offset uint32) (out *pb.GetMicWhiteApplyListResp,err error)
	GetOnMicWhiteList(ctx context.Context, channelId uint32) (out *pb.GetOnMicWhiteListResp,err error)
	GetSelfOnMicQualifications(ctx context.Context, uid, channelId uint32) (out *pb.GetSelfOnMicQualificationsResp,err error)
	GetUserChannelRoomApplyList(ctx context.Context, channelId, tabId, uid uint32) (out *pb.GetUserChannelRoomApplyListResp,err error)
	HandleChannelRoomApply(ctx context.Context, channelId, tabId, adminId, uid uint32, actionType pb.ActionType) (out *pb.HandleChannelRoomApplyResp,err error)
	HandleMicWhiteListApply(ctx context.Context, in *pb.HandleMicWhiteListApplyReq) (out *pb.HandleMicWhiteListApplyResp,err error)
	IsInChannelRoomWhiteList(ctx context.Context, channelId, tabId, uid uint32) (out *pb.IsInChannelRoomWhiteListResp,err error)
	RemoveChannelRoomWhiteList(ctx context.Context, channelId, tabId uint32, whitelist []uint32) (out *pb.RemoveChannelRoomWhiteListResp,err error)
	RemoveFromWhiteList(ctx context.Context, uids []uint32, channelId uint32) (out *pb.RemoveFromWhiteListResp,err error)
	SetWhiteListSwitchStatus(ctx context.Context, channelId uint32, switchStatus pb.WhiteListSwitchStatus) (out *pb.SetWhiteListSwitchStatusResp,err error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
