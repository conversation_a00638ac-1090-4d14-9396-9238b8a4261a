package melee_channel

import (
	"context"
	. "github.com/onsi/ginkgo"
	"github.com/onsi/ginkgo/reporters"
	. "github.com/onsi/gomega"
	pb "golang.52tt.com/protocol/services/melee-channel"
	"google.golang.org/grpc"
	"testing"
	"time"
)

func TestChannel(t *testing.T) {
	RegisterFailHandler(Fail)
	junitReporter := reporters.NewJUnitReporter("junit.xml")
	RunSpecsWithDefaultAndCustomReporters(t, "melee-channel", []Reporter{junitReporter})
}

func isExist(list []uint32, uid uint32) bool {
	for _, v := range list {
		if v == uid {
			return true
		}
	}
	return false
}

var _ = Describe("melee-channel", func() {
	var client *Client
	//// channel服务连接
	client, _ = NewClient(grpc.WithBlock(), grpc.WithAuthority("melee-channel.52tt.local"))
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	var aChannelId uint32 = 10201953
	var agreeUid uint32 = 111
	//var refuseUid uint32 = 112
	//var errChannelId uint32 = 111
	Context("麦位管理", func() {
		_, err := client.ApplyToMicWhiteList(ctx, agreeUid, aChannelId)
		It("申请上麦接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})

		resp, err := client.GetMicWhiteApplyList(ctx, aChannelId, 100, 0)
		It("获取上麦接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})

		It("申请上麦后申请列表不显示", func() {
			Expect(isExist(resp.GetApplyList(), agreeUid)).To(Equal(false))
		})

		countResp, err := client.GetMicWhiteApplyCount(ctx, aChannelId)
		It("获取上麦人数错误", func() {
			Expect(countResp.Count == 1).To(Equal(false))
		})

		_, err = client.HandleMicWhiteListApply(ctx, &pb.HandleMicWhiteListApplyReq{
			ChannelId:    aChannelId,
			ApplicantUid: agreeUid,
			ActionType:   pb.HandleMicWhiteListApplyReq_Agree,
		})
		It("同意上麦申请接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})

		resp, err = client.GetMicWhiteApplyList(ctx, aChannelId, 100, 0)
		It("获取上麦接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})

		It("同意上麦申请后申请列表依旧显示", func() {
			Expect(isExist(resp.GetApplyList(), agreeUid)).To(Equal(true))
		})

	})
})
