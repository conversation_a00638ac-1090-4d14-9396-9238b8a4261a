package game_ticket_mgr

import (
	"context"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/game-ticket-mgr"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetUserRemainSummary(ctx context.Context, req *pb.GetUserRemainSummaryReq) (*pb.UserRemainSummary, protocol.ServerError)
	GetUserRemainNumByTicketId(ctx context.Context, req *pb.GetUserRemainSummaryReq) (uint32, protocol.ServerError)
	GetUserRemainDetail(ctx context.Context, req *pb.GetUserRemainDetailReq) (*pb.UserRemainDetail, protocol.ServerError)
	SendUserGameTicket(ctx context.Context, req *pb.SendUserGameTicketReq) (*pb.SendUserGameTicketResp, protocol.ServerError)
	FreezeUserGameTicket(ctx context.Context, req *pb.FreezeUserGameTicketReq) (*pb.FreezeUserGameTicketResp, protocol.ServerError)
	ConfirmFreezeOrder(ctx context.Context, req *pb.ConfirmFreezeOrderReq) (*pb.ConfirmFreezeOrderResp, protocol.ServerError)
	GetUserCouponCount(ctx context.Context, req *pb.GetUserCouponCountReq) (*pb.GetUserCouponCountResp, protocol.ServerError)
	GetUserCouponList(ctx context.Context, req *pb.GetUserCouponListReq) (*pb.GetUserCouponListResp, protocol.ServerError)
	GetGameTicketOrderQueryOptions(ctx context.Context, req *pb.GetGameTicketOrderQueryOptionsRequest) (*pb.GetGameTicketOrderQueryOptionsResponse, protocol.ServerError)
	GetGameTicketOrderList(ctx context.Context, req *pb.GetGameTicketOrderListRequest) (*pb.GetGameTicketOrderListResponse, protocol.ServerError)
	GrantCoupon(ctx context.Context, req *pb.GrantCouponReq) (*pb.GrantCouponResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
