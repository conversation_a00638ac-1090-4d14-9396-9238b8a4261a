package medalgo

import (
	"context"
	"google.golang.org/grpc/metadata"
	"strconv"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/medalgo"
	"google.golang.org/grpc"
)

const (
	serviceName = "medalgo"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewMedalgoClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.MedalgoClient { return c.Stub().(pb.MedalgoClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetUserMedalList(ctx context.Context, req pb.GetUserMedalListReq) (*pb.GetUserMedalListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserMedalList(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AwardMedalToUser(ctx context.Context, req pb.AwardMedalToUserReq) (*pb.AwardMedalToUserResp, protocol.ServerError) {
	resp, err := c.typedStub().AwardMedalToUser(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AwardMedalToUserEx(ctx context.Context, uid, medalID, exTime uint32, isExt bool) (*pb.AwardMedalToUserResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().AwardMedalToUser(ctx, &pb.AwardMedalToUserReq{
		Uid:         uid,
		MedalId:     medalID,
		ExpireTime:  exTime,
		IsExtension: isExt,
	})
	return r, err
}

func (c *Client) ReduceUserMedal(ctx context.Context, req pb.ReduceUserMedalReq) (*pb.ReduceUserMedalResp, protocol.ServerError) {
	resp, err := c.typedStub().ReduceUserMedal(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ReduceUserMedalEx(ctx context.Context, uid, medalID, exTime uint32) (*pb.ReduceUserMedalResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().ReduceUserMedal(ctx, &pb.ReduceUserMedalReq{
		Uid:        uid,
		MedalId:    medalID,
		ExpireTime: exTime,
	})
	return r, err
}

func (c *Client) GetMedalConfigUpdateTime(ctx context.Context, req pb.GetMedalConfigUpdateTimeReq) (*pb.GetMedalConfigUpdateTimeResp, protocol.ServerError) {
	resp, err := c.typedStub().GetMedalConfigUpdateTime(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetMedalConfigList(ctx context.Context, req pb.GetMedalConfigListReq) (*pb.GetMedalConfigListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetMedalConfigList(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatGetUserMedalList(ctx context.Context, req pb.BatGetUserMedalListReq) (*pb.BatGetUserMedalListResp, protocol.ServerError) {
	resp, err := c.typedStub().BatGetUserMedalList(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserMedalListWhitTaillight(ctx context.Context, req pb.GetUserMedalListWhitTaillightReq) (*pb.GetUserMedalListWhitTaillightResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserMedalListWhitTaillight(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatGetUserMedalListWhitTaillight(ctx context.Context, req pb.BatGetUserMedalListWhitTaillightReq) (*pb.BatGetUserMedalListWhitTaillightResp, protocol.ServerError) {
	resp, err := c.typedStub().BatGetUserMedalListWhitTaillight(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatGetUserMedalListWhitTaillightEx(ctx context.Context, uin uint32, uidList []uint32) (map[uint32][]*pb.UserMedal, map[uint32][]uint32, map[uint32][]uint32, error) {
	medalMap := make(map[uint32][]*pb.UserMedal)
	medalIDMap := make(map[uint32][]uint32)
	taillightMap := make(map[uint32][]uint32)
	r, err := c.typedStub().BatGetUserMedalListWhitTaillight(ctx, &pb.BatGetUserMedalListWhitTaillightReq{UidList: uidList})
	if len(uidList) == 0 {
		log.Debugf("BatGetUserMedalListWhitTaillightEx uid %d, uidList 0: %v", uin, uidList)
		return medalMap, medalIDMap, taillightMap, nil
	}
	for _, m := range r.GetUserMedalList() {
		medalMap[m.Uid] = append(medalMap[m.Uid], m)
		medalIDMap[m.Uid] = append(medalIDMap[m.Uid], m.MedalId)
	}
	for _, t := range r.GetTaillightMedalList() {
		taillightMap[t.Uid] = append(taillightMap[t.Uid], t.MedalId)
	}
	if err != nil {
		log.Errorf("BatGetUserMedalListWhitTaillight uid %d failed: %v", uin, err)
	}
	return medalMap, medalIDMap, taillightMap, protocol.FromSvrkitError(err)
}

func (c *Client) SetUserMedalTaillight(ctx context.Context, req pb.SetUserMedalTaillightReq) (*pb.SetUserMedalTaillightResp, protocol.ServerError) {
	resp, err := c.typedStub().SetUserMedalTaillight(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddMedal(ctx context.Context, req pb.AddMedalReq) (*pb.AddMedalResp, protocol.ServerError) {
	resp, err := c.typedStub().AddMedal(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateMedal(ctx context.Context, req pb.UpdateMedalReq) (*pb.UpdateMedalResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateMedal(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DeleteMedal(ctx context.Context, req pb.DeleteMedalReq) (*pb.DeleteMedalResp, protocol.ServerError) {
	resp, err := c.typedStub().DeleteMedal(ctx, &req)
	return resp, protocol.ToServerError(err)
}
