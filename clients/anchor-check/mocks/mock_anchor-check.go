// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/anchor-check (interfaces: IClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	AnchorCheck "golang.52tt.com/protocol/services/anchor-check"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CheckInWhite mocks base method.
func (m *MockIClient) CheckInWhite(arg0 context.Context, arg1 uint32) (*AnchorCheck.WhiteData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckInWhite", arg0, arg1)
	ret0, _ := ret[0].(*AnchorCheck.WhiteData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckInWhite indicates an expected call of CheckInWhite.
func (mr *MockIClientMockRecorder) CheckInWhite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInWhite", reflect.TypeOf((*MockIClient)(nil).CheckInWhite), arg0, arg1)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetCheckList mocks base method.
func (m *MockIClient) GetCheckList(arg0 context.Context, arg1 int32, arg2, arg3, arg4, arg5, arg6 uint32) (*AnchorCheck.GetCheckListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCheckList", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(*AnchorCheck.GetCheckListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCheckList indicates an expected call of GetCheckList.
func (mr *MockIClientMockRecorder) GetCheckList(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCheckList", reflect.TypeOf((*MockIClient)(nil).GetCheckList), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// GetLastCreateScore mocks base method.
func (m *MockIClient) GetLastCreateScore(arg0 context.Context, arg1 uint32) (*AnchorCheck.LastCreateScore, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastCreateScore", arg0, arg1)
	ret0, _ := ret[0].(*AnchorCheck.LastCreateScore)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastCreateScore indicates an expected call of GetLastCreateScore.
func (mr *MockIClientMockRecorder) GetLastCreateScore(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastCreateScore", reflect.TypeOf((*MockIClient)(nil).GetLastCreateScore), arg0, arg1)
}

// GetWhiteExpireUidList mocks base method.
func (m *MockIClient) GetWhiteExpireUidList(arg0 context.Context, arg1, arg2 uint32) (*AnchorCheck.GetWhiteExpireUidListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWhiteExpireUidList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*AnchorCheck.GetWhiteExpireUidListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWhiteExpireUidList indicates an expected call of GetWhiteExpireUidList.
func (mr *MockIClientMockRecorder) GetWhiteExpireUidList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWhiteExpireUidList", reflect.TypeOf((*MockIClient)(nil).GetWhiteExpireUidList), arg0, arg1, arg2)
}

// SetCheckData mocks base method.
func (m *MockIClient) SetCheckData(arg0 context.Context, arg1 string, arg2, arg3, arg4, arg5 uint32, arg6 string) (*AnchorCheck.EmptyRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCheckData", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(*AnchorCheck.EmptyRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetCheckData indicates an expected call of SetCheckData.
func (mr *MockIClientMockRecorder) SetCheckData(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCheckData", reflect.TypeOf((*MockIClient)(nil).SetCheckData), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// SetWhite mocks base method.
func (m *MockIClient) SetWhite(arg0 context.Context, arg1 []uint32) (*AnchorCheck.EmptyRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWhite", arg0, arg1)
	ret0, _ := ret[0].(*AnchorCheck.EmptyRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetWhite indicates an expected call of SetWhite.
func (mr *MockIClientMockRecorder) SetWhite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWhite", reflect.TypeOf((*MockIClient)(nil).SetWhite), arg0, arg1)
}

// StartRecord mocks base method.
func (m *MockIClient) StartRecord(arg0 context.Context, arg1 uint32, arg2, arg3 string) (*AnchorCheck.StartRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartRecord", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*AnchorCheck.StartRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartRecord indicates an expected call of StartRecord.
func (mr *MockIClientMockRecorder) StartRecord(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartRecord", reflect.TypeOf((*MockIClient)(nil).StartRecord), arg0, arg1, arg2, arg3)
}

// StopRecord mocks base method.
func (m *MockIClient) StopRecord(arg0 context.Context, arg1 uint32, arg2 string, arg3 bool) (*AnchorCheck.EmptyRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopRecord", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*AnchorCheck.EmptyRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StopRecord indicates an expected call of StopRecord.
func (mr *MockIClientMockRecorder) StopRecord(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopRecord", reflect.TypeOf((*MockIClient)(nil).StopRecord), arg0, arg1, arg2, arg3)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// SubmitWhite mocks base method.
func (m *MockIClient) SubmitWhite(arg0 context.Context, arg1 uint32) (*AnchorCheck.EmptyRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitWhite", arg0, arg1)
	ret0, _ := ret[0].(*AnchorCheck.EmptyRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitWhite indicates an expected call of SubmitWhite.
func (mr *MockIClientMockRecorder) SubmitWhite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitWhite", reflect.TypeOf((*MockIClient)(nil).SubmitWhite), arg0, arg1)
}
