// Code generated by quicksilver-cli. DO NOT EDIT.
package channeldatinggame

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/channel-dating-game"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	BatchGetChannelDatingGamePhase(ctx context.Context, req *pb.BatchGetChannelDatingGamePhaseReq) (*pb.BatchGetChannelDatingGamePhaseResp,error)
	CheckDatingGameEntry(ctx context.Context, cid uint32) (bool,uint32,error)
	ConfirmVipHoldMic(ctx context.Context, cid, uid uint32) (*pb.ConfirmVipHoldMicResp,error)
	GetAllSceneFellowInfo(ctx context.Context) (*pb.GetAllSceneFellowInfoResp,error)
	GetApplyMicUserList(ctx context.Context, cid, uid uint32) (*pb.GetApplyMicUserListResp,error)
	GetDatingGameCurInfo(ctx context.Context, cid uint32) (*pb.GetDatingGameCurInfoResp,error)
	GetGamePhase(ctx context.Context, channelId uint32) (uint32,protocol.ServerError)
	InitDatingMember(ctx context.Context, req *pb.InitDatingMemberReq) (*pb.InitDatingMemberResp,error)
	OpenUserLikeBeatObj(ctx context.Context, cid, openUid uint32) (*pb.GetUserLikeBeatObjResp,error)
	SetGamePhase(ctx context.Context, cid, uid, targetPhase uint32) (*pb.SetGamePhaseResp,error)
	SetUserLikeBeatObj(ctx context.Context, cid, selectUid, likeUid uint32) (*pb.SetUserLikeBeatObjResp,error)
	TestDrawImage(ctx context.Context, req *pb.TestDrawImageReq) (*pb.TestDrawImageResp,error)
	TypedStub() pb.ChannelDatingGameClient
	UserApplyMic(ctx context.Context, channelId, uid uint32, cancel bool) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
