package client_gen_push

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/client-gen-push"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetGenPushSwitch(ctx context.Context, in *pb.GetGenPushSwitchReq) (out *pb.GetGenPushSwitchResp, err error)
	RandomGetPushDocs(ctx context.Context, in *pb.RandomGetPushDocsReq) (out *pb.RandomGetPushDocsResp, err error)
	GetPushFactoryPrivateTemplate(ctx context.Context, in *pb.GetPushFactoryPrivateTemplateReq) (out *pb.GetPushFactoryPrivateTemplateResp, err error)
	GetNewUserPushCntLimitConfig(ctx context.Context, in *pb.GetNewUserPushCntLimitConfigReq)(out *pb.GetNewUserPushCntLimitConfigResp,err error)
	UpdateNewUserPushCntLimitConfig(ctx context.Context,in *pb.UpdateNewUserPushCntLimitConfigReq)(out *pb.UpdateNewUserPushCntLimitConfigResp,err error)
	GetNewUserPushCntLimitConfigByParma(ctx context.Context,in *pb.GetNewUserPushCntLimitConfigByParamReq)(out *pb.GetNewUserPushCntLimitConfigByParamResp,err error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
