package ttregulationconfig

import (
	"context"
	"golang.52tt.com/pkg/log"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/tt-regulation-config"

	"google.golang.org/grpc"
)

const (
	serviceName = "tt-regulation-config"
)

// Client -
type Client struct {
	client.BaseClient
}

// newClient -
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewTtRegulationConfigClient(cc)
			},
			dopts...,
		),
	}, nil
}

// NewTracedClient -
func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

// NewClient -
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.TtRegulationConfigClient {
	return c.Stub().(pb.TtRegulationConfigClient)
}

func (c *Client) GetSearchWhiteList(ctx context.Context, req *pb.GetSearchWhiteListReq) (resp *pb.GetSearchWhiteListResp, err error) {

	resp, err = c.typedStub().GetSearchWhiteList(ctx, req)
	if err != nil {
		log.Errorf("GetSearchWhiteList fail , in:%v err:%v .", req, err)
	}

	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSearchWhiteListStatus(ctx context.Context, req *pb.GetSearchWhiteListStatusReq) (resp *pb.GetSearchWhiteListStatusResp, err error) {

	resp, err = c.typedStub().GetSearchWhiteListStatus(ctx, req)
	if err != nil {
		log.Errorf("GetSearchWhiteListStatus fail , in:%v err:%v .", req, err)
	}

	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchAddSearchWhiteList(ctx context.Context, req *pb.BatchAddSearchWhiteListReq) (resp *pb.BatchAddSearchWhiteListResp, err error) {

	resp, err = c.typedStub().BatchAddSearchWhiteList(ctx, req)
	if err != nil {
		log.Errorf("BatchAddSearchWhiteList fail , in:%v err:%v .", req, err)
	}

	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchDelSearchWhiteList(ctx context.Context, req *pb.BatchDelSearchWhiteListReq) (resp *pb.BatchDelSearchWhiteListResp, err error) {

	resp, err = c.typedStub().BatchDelSearchWhiteList(ctx, req)
	if err != nil {
		log.Errorf("BatchDelSearchWhiteList fail , in:%v err:%v .", req, err)
	}

	return resp, protocol.ToServerError(err)
}

func (c *Client) SwitchSearchWhiteList(ctx context.Context, req *pb.SwitchSearchWhiteListReq) (resp *pb.SwitchSearchWhiteListResp, err error) {

	resp, err = c.typedStub().SwitchSearchWhiteList(ctx, req)
	if err != nil {
		log.Errorf("SwitchSearchWhiteList fail , in:%v err:%v .", req, err)
	}

	return resp, protocol.ToServerError(err)
}

func (c *Client) ExportHotWord(ctx context.Context, req *pb.ExportHotWordReq) (resp *pb.ExportHotWordResp, err error) {

	resp, err = c.typedStub().ExportHotWord(ctx, req)
	if err != nil {
		log.Errorf("ExportHotWord fail , in:%v err:%v .", req, err)
	}

	return resp, protocol.ToServerError(err)
}

func (c *Client) AuthSearchEvent(ctx context.Context, req *pb.AuthSearchEventReq) (resp *pb.AuthSearchEventResp, err error) {
	resp, err = c.typedStub().AuthSearchEvent(ctx, req)
	if err != nil {
		log.Errorf("AuthSearchEvent fail , in:%v err:%v .", req, err)
	}

	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserControlInfo(ctx context.Context, uid uint32) (*pb.GetUserControlInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserControlInfo(ctx, &pb.GetUserControlInfoReq{
		Uid:                  uid,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatGetUserControlInfo(ctx context.Context, uidList []uint32) (*pb.BatGetUserControlInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().BatGetUserControlInfo(ctx, &pb.BatGetUserControlInfoReq{
		UidList:              uidList,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatRemoveUserControlled(ctx context.Context, uidList []uint32) (*pb.BatRemoveUserControlledResp, protocol.ServerError) {
   resp, err := c.typedStub().BatRemoveUserControlled(ctx, &pb.BatRemoveUserControlledReq{
	   UidList:              uidList,
   })
   return resp, protocol.ToServerError(err)
}

func (c Client) SetUserControlInfo(ctx context.Context, req *pb.SetUserControlInfoReq) (*pb.SetUserControlInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().SetUserControlInfo(ctx, req)
	return resp, protocol.ToServerError(err)
}

