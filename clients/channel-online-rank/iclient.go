package channelonlinerank

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channelonlinerank"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetMemberRankList(ctx context.Context, in *pb.GetMemberRankListReq) (*pb.GetMemberRankListResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
