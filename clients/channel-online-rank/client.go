package channelonlinerank

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channelonlinerank"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-online-rank"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelOnlineRankClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelOnlineRankClient { return c.Stub().(pb.ChannelOnlineRankClient) }

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetMemberRankList(ctx context.Context, req *pb.GetMemberRankListReq) (*pb.GetMemberRankListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetMemberRankList(ctx, req)
	return resp, protocol.ToServerError(err)
}
