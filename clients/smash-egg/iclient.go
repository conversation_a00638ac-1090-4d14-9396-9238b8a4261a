// Code generated by quicksilver-cli. DO NOT EDIT.
package smash_egg

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/smash-egg"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AddSmashLightEffectsV2(ctx context.Context, req *pb.AddSmashLightEffectsV2Req) (*pb.AddSmashLightEffectsV2Resp,protocol.ServerError)
	CheckSmashEggActivityConfig(ctx context.Context, req *pb.CheckSmashEggActivityConfigReq) (*pb.CheckSmashEggActivityConfigResp,protocol.ServerError)
	CheckTester(ctx context.Context, req *pb.CheckTesterReq) (*pb.CheckTesterResp,protocol.ServerError)
	CheckWhitelist(ctx context.Context, req *pb.CheckWhitelistReq) (*pb.CheckWhitelistResp,protocol.ServerError)
	DelLightEffectByconfId(ctx context.Context, req *pb.DelLightEffectByconfIdReq) (*pb.DelLightEffectByconfIdResp,protocol.ServerError)
	DelSmashEggActivityConfig(ctx context.Context, req *pb.DelSmashEggActivityConfigReq) (*pb.DelSmashEggActivityConfigResp,protocol.ServerError)
	DelTmpPrizePool(ctx context.Context, req *pb.DelTmpPrizePoolReq) (*pb.DelTmpPrizePoolResp,protocol.ServerError)
	GetAllSmashLightEffectsV2(ctx context.Context, req *pb.GetAllSmashLightEffectsV2Req) (*pb.GetAllSmashLightEffectsV2Resp,protocol.ServerError)
	GetConsumeRecord(ctx context.Context, req *pb.GetConsumeRecordReq) (*pb.GetConsumeRecordResp,protocol.ServerError)
	GetPrizePool(ctx context.Context, req *pb.GetPrizePoolReq) (*pb.GetPrizePoolResp,protocol.ServerError)
	GetPrizePoolTmp(ctx context.Context, req *pb.GetPrizePoolTmpReq) (*pb.GetPrizePoolTmpResp,protocol.ServerError)
	GetSmashConfig(ctx context.Context, req *pb.GetSmashConfigReq) (*pb.GetSmashConfigResp,protocol.ServerError)
	GetSmashEggActivityConfWithCache(ctx context.Context, req *pb.GetSmashEggActivityConfWithCacheReq) (*pb.GetSmashEggActivityConfWithCacheResp,protocol.ServerError)
	GetSmashEggActivityConfig(ctx context.Context, req *pb.GetSmashEggActivityConfigReq) (*pb.GetSmashEggActivityConfigResp,protocol.ServerError)
	GetSmashEggExemptValue(ctx context.Context, req *pb.GetSmashEggExemptValueReq) (*pb.GetSmashEggExemptValueResp,protocol.ServerError)
	GetSmashStatus(ctx context.Context, req *pb.GetSmashStatusReq) (*pb.GetSmashStatusResp,protocol.ServerError)
	GetUserProp(ctx context.Context, req *pb.GetUserPropReq) (*pb.GetUserPropResp,protocol.ServerError)
	GetUserPropExpireDetail(ctx context.Context, req *pb.GetUserPropExpireDetailReq) (*pb.GetUserPropExpireDetailResp,protocol.ServerError)
	GetWinningRecord(ctx context.Context, req *pb.GetWinningRecordReq) (*pb.GetWinningRecordResp,protocol.ServerError)
	Recharge(ctx context.Context, req *pb.RechargeReq) (*pb.RechargeResp,protocol.ServerError)
	SetPrizePool(ctx context.Context, req *pb.SetPrizePoolReq) (*pb.SetPrizePoolResp,protocol.ServerError)
	SetSmashConfig(ctx context.Context, req *pb.SetSmashConfigReq) (*pb.SetSmashConfigResp,protocol.ServerError)
	SimulateWithPrizePool(ctx context.Context, req *pb.SimulateWithPrizePoolReq) (*pb.SimulateWithPrizePoolResp,protocol.ServerError)
	Smash(ctx context.Context, req *pb.SmashReq) (*pb.SmashResp,protocol.ServerError)
	UpdateSmashEggActivityConfig(ctx context.Context, req *pb.UpdateSmashEggActivityConfigReq) (*pb.UpdateSmashEggActivityConfigResp,protocol.ServerError)
	UpdateSmashLightEffectV2(ctx context.Context, req *pb.UpdateSmashLightEffectV2Req) (*pb.UpdateSmashLightEffectV2Resp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
