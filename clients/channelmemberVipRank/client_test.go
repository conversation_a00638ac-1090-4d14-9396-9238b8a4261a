package channelmemberviprank

import (
	"context"
	"github.com/stretchr/testify/assert"
	"golang.52tt.com/pkg/log"
	"google.golang.org/grpc"
	"os"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"golang.52tt.com/protocol/app/channel"
	"google.golang.org/grpc/grpclog"
)

var channelmemberviprankClient *Client

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
	log.SetLevel(log.DebugLevel)
	channelmemberviprankClient = NewClient(grpc.WithBlock(), grpc.WithAuthority("channelmemberviprank.52tt.local"))
}

func TestClient_HideChannelConsume(t *testing.T) {

	Convey("TestClient_HideChannelConsume", t, func() {
		err := channelmemberviprankClient.HideChannelConsume(context.Background(), 1, 1, true)
		So(err, ShouldBeNil)
		err = channelmemberviprankClient.HideChannelConsume(context.Background(), 1, 1, false)
		So(err, ShouldBeNil)
	})

}

func TestClient_GetConsumeTopN(t *testing.T) {
	Convey("TestClient_GetConsumeTopN", t, func() {
		//清理旧数据
		err := channelmemberviprankClient.HideChannelConsume(context.Background(), 1, 1, true)
		So(err, ShouldBeNil)

		resp, err := channelmemberviprankClient.GetConsumeTopN(context.Background(), 1, 1, 0, 50, 50, uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE), true)
		So(err, ShouldBeNil)
		//缓存预热
		time.Sleep(15* time.Second)
		resp, err = channelmemberviprankClient.GetConsumeTopN(context.Background(), 1, 1, 0, 50, 50, uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE), true)
		num1 := len(resp.GetConsumeList())

		// 简单测试某用户被手动隐藏后，榜上应该只少一个人，只做最简单的逻辑接口测试，避免用例过于复杂。
		err = channelmemberviprankClient.HideChannelConsume(context.Background(), 1, 1, false)
		So(err, ShouldBeNil)
		resp, err = channelmemberviprankClient.GetConsumeTopN(context.Background(), 1, 1, 0, 50, 50, uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE), true)
		So(err, ShouldBeNil)
		num2 := len(resp.GetConsumeList())
		assert.Equal(t, num1, num2+1)
		err = channelmemberviprankClient.HideChannelConsume(context.Background(), 1, 1, true)
		So(err, ShouldBeNil)
	})
}
