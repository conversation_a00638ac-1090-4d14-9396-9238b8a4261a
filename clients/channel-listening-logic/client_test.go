package channellisteninglogic

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/app/channellisteninglogic"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {

	<PERSON><PERSON>("GetChannelListeningLogic", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.ChannelListeningLogicReq
		resp, err := client.GetChannelListeningLogic(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetChannelListeningLogic %+v", resp)
	})

}
