package channelroleplaylogic

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	channel_roleplay_logic_pb "golang.52tt.com/protocol/app/channel-roleplay-logic"
	pb "golang.52tt.com/protocol/services/logicsvr-go/channel-roleplay-logic"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-roleplay-logic"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelRoleplayLogicClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelRoleplayLogicClient {
	return c.Stub().(pb.ChannelRoleplayLogicClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetBoxInfo(ctx context.Context, req channel_roleplay_logic_pb.GetBoxInfoReq) (*channel_roleplay_logic_pb.GetBoxInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetBoxInfo(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetBoxInfosByLimit(ctx context.Context, req channel_roleplay_logic_pb.GetBoxInfosByLimitReq) (*channel_roleplay_logic_pb.GetBoxInfosByLimitResp, protocol.ServerError) {
	resp, err := c.typedStub().GetBoxInfosByLimit(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) EnterBox(ctx context.Context, req channel_roleplay_logic_pb.EnterBoxReq) (*channel_roleplay_logic_pb.EnterBoxResp, protocol.ServerError) {
	resp, err := c.typedStub().EnterBox(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ExitBox(ctx context.Context, req channel_roleplay_logic_pb.ExitBoxReq) (*channel_roleplay_logic_pb.ExitBoxResp, protocol.ServerError) {
	resp, err := c.typedStub().ExitBox(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HandleApplyBox(ctx context.Context, req channel_roleplay_logic_pb.HandleApplyBoxReq) (*channel_roleplay_logic_pb.HandleApplyBoxResp, protocol.ServerError) {
	resp, err := c.typedStub().HandleApplyBox(ctx, &req)
	return resp, protocol.ToServerError(err)
}
