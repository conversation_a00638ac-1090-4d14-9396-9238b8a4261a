// Code generated by quicksilver-cli. DO NOT EDIT.
package guild_member_lv

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/guild-member-lv"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	GetMemberContribution(ctx context.Context, guildId uint32, uid uint64) (*pb.StMemberContributionInfo,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
