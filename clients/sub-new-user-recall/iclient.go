package subnewuserrecall

import (
	"context"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/sub-new-user-recall"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	BatchFilterUids(ctx context.Context, req *pb.BatchFilterUidsReq) (*pb.BatchFilterUidsResp, protocol.ServerError)
	RecordNewUserTimeCount(ctx context.Context, req *pb.RecordNewUserTimeCountReq) (*pb.RecordNewUserTimeCountResp, protocol.ServerError)
	GetRecallDataSrc(ctx context.Context, req *pb.GetRecallDataSrcReq) (*pb.GetRecallDataSrcResp, protocol.ServerError)
	GetPushQueueLen(ctx context.Context, req *pb.GetPushQueueLenReq) (*pb.GetPushQueueLenResp, protocol.ServerError)
	SetAlreadyPush(ctx context.Context, uids []uint32, pushType uint32) (*pb.SetAlreadyPushResp, protocol.ServerError)
	GetAlgoRecallDataSrc(ctx context.Context, in *pb.GetAlgoRecallDataSrcReq) (*pb.GetAlgoRecallDataSrcResp, protocol.ServerError)
	BatchGetUserRecallItem(ctx context.Context, uidList []uint32) (*pb.BatchGetUserRecallItemResp, protocol.ServerError)
	BatchGetTodayPushFilter(ctx context.Context, in *pb.BatchGetTodayPushFilterReq) (*pb.BatchGetTodayPushFilterResp, protocol.ServerError)
	BatchSetTodayPushFilter(ctx context.Context, in *pb.BatchSetTodayPushFilterReq) (*pb.BatchSetTodayPushFilterResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
