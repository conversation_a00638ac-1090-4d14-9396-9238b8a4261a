/*
 * @Description:
 * @Date: 2021-06-09 10:17:48
 * @LastEditors: liang
 * @LastEditTime: 2021-09-01 11:56:34
 */
package subnewuserrecall

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/sub-new-user-recall"
	"google.golang.org/grpc"
)

const (
	serviceName = "sub-new-user-recall"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewSubNewUserRecallClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.SubNewUserRecallClient { return c.Stub().(pb.SubNewUserRecallClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) BatchFilterUids(ctx context.Context, req *pb.BatchFilterUidsReq) (*pb.BatchFilterUidsResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchFilterUids(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) RecordNewUserTimeCount(ctx context.Context, req *pb.RecordNewUserTimeCountReq) (*pb.RecordNewUserTimeCountResp, protocol.ServerError) {
	resp, err := c.typedStub().RecordNewUserTimeCount(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRecallDataSrc(ctx context.Context, req *pb.GetRecallDataSrcReq) (*pb.GetRecallDataSrcResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRecallDataSrc(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPushQueueLen(ctx context.Context, req *pb.GetPushQueueLenReq) (*pb.GetPushQueueLenResp, protocol.ServerError) {
	resp, err := c.typedStub().GetPushQueueLen(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetAlreadyPush(ctx context.Context, uids []uint32, pushType uint32) (*pb.SetAlreadyPushResp, protocol.ServerError) {
	resp, err := c.typedStub().SetAlreadyPush(ctx, &pb.SetAlreadyPushReq{
		Uids:     uids,
		PushType: pushType,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAlgoRecallDataSrc(ctx context.Context, in *pb.GetAlgoRecallDataSrcReq) (*pb.GetAlgoRecallDataSrcResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAlgoRecallDataSrc(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetUserRecallItem(ctx context.Context, uidList []uint32) (*pb.BatchGetUserRecallItemResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetUserRecallItem(ctx, &pb.BatchGetUserRecallItemReq{
		Uids: uidList,
	})

	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetTodayPushFilter(ctx context.Context, in *pb.BatchGetTodayPushFilterReq) (*pb.BatchGetTodayPushFilterResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetTodayPushFilter(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchSetTodayPushFilter(ctx context.Context, in *pb.BatchSetTodayPushFilterReq) (*pb.BatchSetTodayPushFilterResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchSetTodayPushFilter(ctx, in)
	return resp, protocol.ToServerError(err)
}
