package molebeat

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	pb "golang.52tt.com/protocol/services/molebeat"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

//func TestGetHelloWorld(t *testing.T) {
//
//	<PERSON>vey("GetMoleBeat", t, func() {
//		client, err := NewClient()
//		So(err, ShouldBeNil)
//		var req pb.MoleBeatReq
//		resp, err := client.GetMoleBeat(context.Background(), req)
//		So(err, ShouldBeNil)
//
//		t.Logf("GetMoleBeat %+v", resp)
//	})
//
//}

var testclient *Client

func init() {
	var err error
	testclient, err = NewClient(grpc.WithBlock())
	if err != nil {
		panic(err)
	}

}
func TestReportBeat(t *testing.T) {
	var ctx, cancel = context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	//cli, _ := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("molebeat.52tt.local"))
	resp, err := testclient.ReportBeat(ctx, &pb.MoleBeatReportReq{
		Uid:     2236251,
		Chid:    10009124,
		BeatCnt: 15,
	})
	if err != nil {
		fmt.Println("err: ", err.Error())
		return
	}
	fmt.Println("resp: ", resp)
	return
}
