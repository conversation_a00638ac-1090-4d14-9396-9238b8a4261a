package concert

import (
	"context"

	"golang.52tt.com/pkg/client"
	concert "golang.52tt.com/protocol/services/concert"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetConcertSongOpts(ctx context.Context, req *concert.GetConcertSongOptsReq, opts ...grpc.CallOption) (*concert.GetConcertSongOptsResp, error)
	SearchConcertSong(ctx context.Context, req *concert.SearchConcertSongReq, opts ...grpc.CallOption) (*concert.SearchConcertSongResp, error)
	GetConcertSongList(ctx context.Context, req *concert.GetConcertSongListReq, opts ...grpc.CallOption) (*concert.GetConcertSongListResp, error)
	StartConcertSinging(ctx context.Context, req *concert.StartConcertSingingReq, opts ...grpc.CallOption) (*concert.StartConcertSingingResp, error)
	GetMusicBook(ctx context.Context, req *concert.GetMusicBookReq, opts ...grpc.CallOption) (*concert.GetMusicBookResp, error)
	CompleteDownloadingMusicBook(ctx context.Context, req *concert.CompleteDownloadingMusicBookReq, opts ...grpc.CallOption) (*concert.CompleteDownloadingMusicBookResp, error)
	StopConcertSinging(ctx context.Context, req *concert.StopConcertSingingReq, opts ...grpc.CallOption) (*concert.StopConcertSingingResp, error)
	GetConcertInfo(ctx context.Context, req *concert.GetConcertInfoReq, opts ...grpc.CallOption) (*concert.GetConcertInfoResp, error)
	BatchGetConcertInfo(ctx context.Context, req *concert.BatchGetConcertInfoReq, opts ...grpc.CallOption) (*concert.BatchGetConcertInfoResp, error)
	GetAllConcertResource(ctx context.Context, req *concert.GetAllConcertResourceReq, opts ...grpc.CallOption) (*concert.GetAllConcertResourceResp, error)
	LostHeart(ctx context.Context, req *concert.LostHeartReq, opts ...grpc.CallOption) (*concert.LostHeartResp, error)
	ConfirmMain(ctx context.Context, req *concert.ConfirmMainReq, opts ...grpc.CallOption) (*concert.ConfirmMainResp, error)
	UpdateBackingTrackStatus(ctx context.Context, req *concert.UpdateBackingTrackStatusReq, opts ...grpc.CallOption) (*concert.UpdateBackingTrackStatusResp, error)
	ReportConcertSuccCount(ctx context.Context, req *concert.ReportConcertSuccCountReq, opts ...grpc.CallOption) (*concert.ReportConcertSuccCountResp, error)
	SearchSongs(ctx context.Context, req *concert.SearchSongsReq, opts ...grpc.CallOption) (*concert.SearchSongsResp, error)
	BatchUpsertSongs(ctx context.Context, req *concert.BatchUpsertSongsReq, opts ...grpc.CallOption) (*concert.BatchUpsertSongsResp, error)
	DeleteSong(ctx context.Context, req *concert.DeleteSongReq, opts ...grpc.CallOption) (*concert.DeleteSongResp, error)
	SearchInstrumentAudios(ctx context.Context, req *concert.SearchInstrumentAudiosReq, opts ...grpc.CallOption) (*concert.SearchInstrumentAudiosResp, error)
	BatchUpsertInstrumentAudios(ctx context.Context, req *concert.BatchUpsertInstrumentAudiosReq, opts ...grpc.CallOption) (*concert.BatchUpsertInstrumentAudiosResp, error)
	DeleteInstrumentAudio(ctx context.Context, req *concert.DeleteInstrumentAudioReq, opts ...grpc.CallOption) (*concert.DeleteInstrumentAudioResp, error)
	GetAllConcertImage(ctx context.Context, req *concert.GetAllConcertImageReq, opts ...grpc.CallOption) (*concert.GetAllConcertImageResp, error)
	SetConcertUserImage(ctx context.Context, req *concert.SetConcertUserImageReq, opts ...grpc.CallOption) (*concert.SetConcertUserImageResp, error)
	GetAllConcertOnMicUserImage(ctx context.Context, req *concert.GetAllConcertOnMicUserImageReq, opts ...grpc.CallOption) (*concert.GetAllConcertOnMicUserImageResp, error)
	AddConcertImage(ctx context.Context, req *concert.AddConcertImageReq, opts ...grpc.CallOption) (*concert.AddConcertImageResp, error)
	JoinConcert(ctx context.Context, req *concert.JoinConcertReq, opts ...grpc.CallOption) (*concert.JoinConcertResp, error)
	DeleteConcertImage(ctx context.Context, req *concert.DeleteConcertImageReq, opts ...grpc.CallOption) (*concert.DeleteConcertImageResp, error)
	GetConcertImageList(ctx context.Context, req *concert.GetConcertImageListReq, opts ...grpc.CallOption) (*concert.GetConcertImageListResp, error)
	ReorderConcertImage(ctx context.Context, req *concert.ReorderConcertImageReq, opts ...grpc.CallOption) (*concert.ReorderConcertImageResp, error)
	SetDefaultImage(ctx context.Context, req *concert.SetDefaultImageReq, opts ...grpc.CallOption) (*concert.SetDefaultImageResp, error)
	GetRecentUploadedSong(ctx context.Context, req *concert.GetRecentUploadedSongReq, opts ...grpc.CallOption) (*concert.GetRecentUploadedSongResp, error)
	GetConcertSongById(ctx context.Context, req *concert.GetConcertSongByIdReq, opts ...grpc.CallOption) (*concert.GetConcertSongByIdResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
