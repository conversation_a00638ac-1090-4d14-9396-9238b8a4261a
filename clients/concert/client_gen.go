// Code generated by protoc-gen-go-quicksilver. DO NOT EDIT.

package concert

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	protocol "golang.52tt.com/pkg/protocol"
	concert "golang.52tt.com/protocol/services/concert"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "concert"
)

// Client is the wrapper-client for Concert client.
type Client struct {
	client.BaseClient
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return concert.NewConcertClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of ConcertClient.
func (c *Client) typedStub() concert.ConcertClient { return c.Stub().(concert.ConcertClient) }

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// NewClientOrg creates a new Client with wrapper-client instance.
func NewClientOrg(dopts ...grpc.DialOption) (concert.ConcertClient, error) {
	c, err := NewClient(dopts...)
	if err != nil {
		return nil, err
	}
	return c, nil
}

// GetConcertSongOpts
func (c *Client) GetConcertSongOpts(ctx context.Context, req *concert.GetConcertSongOptsReq, opts ...grpc.CallOption) (*concert.GetConcertSongOptsResp, error) {
	resp, err := c.typedStub().GetConcertSongOpts(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// SearchConcertSong
func (c *Client) SearchConcertSong(ctx context.Context, req *concert.SearchConcertSongReq, opts ...grpc.CallOption) (*concert.SearchConcertSongResp, error) {
	resp, err := c.typedStub().SearchConcertSong(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetConcertSongList
func (c *Client) GetConcertSongList(ctx context.Context, req *concert.GetConcertSongListReq, opts ...grpc.CallOption) (*concert.GetConcertSongListResp, error) {
	resp, err := c.typedStub().GetConcertSongList(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// StartConcertSinging
func (c *Client) StartConcertSinging(ctx context.Context, req *concert.StartConcertSingingReq, opts ...grpc.CallOption) (*concert.StartConcertSingingResp, error) {
	resp, err := c.typedStub().StartConcertSinging(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetMusicBook
func (c *Client) GetMusicBook(ctx context.Context, req *concert.GetMusicBookReq, opts ...grpc.CallOption) (*concert.GetMusicBookResp, error) {
	resp, err := c.typedStub().GetMusicBook(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// CompleteDownloadingMusicBook
func (c *Client) CompleteDownloadingMusicBook(ctx context.Context, req *concert.CompleteDownloadingMusicBookReq, opts ...grpc.CallOption) (*concert.CompleteDownloadingMusicBookResp, error) {
	resp, err := c.typedStub().CompleteDownloadingMusicBook(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// StopConcertSinging
func (c *Client) StopConcertSinging(ctx context.Context, req *concert.StopConcertSingingReq, opts ...grpc.CallOption) (*concert.StopConcertSingingResp, error) {
	resp, err := c.typedStub().StopConcertSinging(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetConcertInfo
func (c *Client) GetConcertInfo(ctx context.Context, req *concert.GetConcertInfoReq, opts ...grpc.CallOption) (*concert.GetConcertInfoResp, error) {
	resp, err := c.typedStub().GetConcertInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// BatchGetConcertInfo
func (c *Client) BatchGetConcertInfo(ctx context.Context, req *concert.BatchGetConcertInfoReq, opts ...grpc.CallOption) (*concert.BatchGetConcertInfoResp, error) {
	resp, err := c.typedStub().BatchGetConcertInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetAllConcertResource
func (c *Client) GetAllConcertResource(ctx context.Context, req *concert.GetAllConcertResourceReq, opts ...grpc.CallOption) (*concert.GetAllConcertResourceResp, error) {
	resp, err := c.typedStub().GetAllConcertResource(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// LostHeart
func (c *Client) LostHeart(ctx context.Context, req *concert.LostHeartReq, opts ...grpc.CallOption) (*concert.LostHeartResp, error) {
	resp, err := c.typedStub().LostHeart(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// ConfirmMain
func (c *Client) ConfirmMain(ctx context.Context, req *concert.ConfirmMainReq, opts ...grpc.CallOption) (*concert.ConfirmMainResp, error) {
	resp, err := c.typedStub().ConfirmMain(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// UpdateBackingTrackStatus
func (c *Client) UpdateBackingTrackStatus(ctx context.Context, req *concert.UpdateBackingTrackStatusReq, opts ...grpc.CallOption) (*concert.UpdateBackingTrackStatusResp, error) {
	resp, err := c.typedStub().UpdateBackingTrackStatus(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// ReportConcertSuccCount
func (c *Client) ReportConcertSuccCount(ctx context.Context, req *concert.ReportConcertSuccCountReq, opts ...grpc.CallOption) (*concert.ReportConcertSuccCountResp, error) {
	resp, err := c.typedStub().ReportConcertSuccCount(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// JoinConcert
func (c *Client) JoinConcert(ctx context.Context, req *concert.JoinConcertReq, opts ...grpc.CallOption) (*concert.JoinConcertResp, error) {
	resp, err := c.typedStub().JoinConcert(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// SearchSongs
func (c *Client) SearchSongs(ctx context.Context, req *concert.SearchSongsReq, opts ...grpc.CallOption) (*concert.SearchSongsResp, error) {
	resp, err := c.typedStub().SearchSongs(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// BatchUpsertSongs
func (c *Client) BatchUpsertSongs(ctx context.Context, req *concert.BatchUpsertSongsReq, opts ...grpc.CallOption) (*concert.BatchUpsertSongsResp, error) {
	resp, err := c.typedStub().BatchUpsertSongs(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// DeleteSong
func (c *Client) DeleteSong(ctx context.Context, req *concert.DeleteSongReq, opts ...grpc.CallOption) (*concert.DeleteSongResp, error) {
	resp, err := c.typedStub().DeleteSong(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// SearchInstrumentAudios
func (c *Client) SearchInstrumentAudios(ctx context.Context, req *concert.SearchInstrumentAudiosReq, opts ...grpc.CallOption) (*concert.SearchInstrumentAudiosResp, error) {
	resp, err := c.typedStub().SearchInstrumentAudios(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// BatchUpsertInstrumentAudios
func (c *Client) BatchUpsertInstrumentAudios(ctx context.Context, req *concert.BatchUpsertInstrumentAudiosReq, opts ...grpc.CallOption) (*concert.BatchUpsertInstrumentAudiosResp, error) {
	resp, err := c.typedStub().BatchUpsertInstrumentAudios(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// DeleteInstrumentAudio
func (c *Client) DeleteInstrumentAudio(ctx context.Context, req *concert.DeleteInstrumentAudioReq, opts ...grpc.CallOption) (*concert.DeleteInstrumentAudioResp, error) {
	resp, err := c.typedStub().DeleteInstrumentAudio(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ExchangeActivityCode(ctx context.Context, req *concert.ExchangeActivityCodeReq, opts ...grpc.CallOption) (*concert.ExchangeActivityCodeResp, error) {
	resp, err := c.typedStub().ExchangeActivityCode(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddActivityCode(ctx context.Context, req *concert.AddActivityCodeReq, opts ...grpc.CallOption) (*concert.AddActivityCodeResp, error) {
	resp, err := c.typedStub().AddActivityCode(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateActivityCode(ctx context.Context, req *concert.UpdateActivityCodeReq, opts ...grpc.CallOption) (*concert.UpdateActivityCodeResp, error) {
	resp, err := c.typedStub().UpdateActivityCode(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListActivityCode(ctx context.Context, req *concert.ListActivityCodeReq, opts ...grpc.CallOption) (*concert.ListActivityCodeResp, error) {
	resp, err := c.typedStub().ListActivityCode(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchUpsertSongsV2(ctx context.Context, req *concert.BatchUpsertSongsV2Req, opts ...grpc.CallOption) (*concert.BatchUpsertSongsV2Resp, error) {
	resp, err := c.typedStub().BatchUpsertSongsV2(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAllConcertImage(ctx context.Context, req *concert.GetAllConcertImageReq, opts ...grpc.CallOption) (*concert.GetAllConcertImageResp, error) {
	resp, err := c.typedStub().GetAllConcertImage(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetConcertUserImage(ctx context.Context, req *concert.SetConcertUserImageReq, opts ...grpc.CallOption) (*concert.SetConcertUserImageResp, error) {
	resp, err := c.typedStub().SetConcertUserImage(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAllConcertOnMicUserImage(ctx context.Context, req *concert.GetAllConcertOnMicUserImageReq, opts ...grpc.CallOption) (*concert.GetAllConcertOnMicUserImageResp, error) {
	resp, err := c.typedStub().GetAllConcertOnMicUserImage(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddConcertImage(ctx context.Context, req *concert.AddConcertImageReq, opts ...grpc.CallOption) (*concert.AddConcertImageResp, error) {
	resp, err := c.typedStub().AddConcertImage(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DeleteConcertImage(ctx context.Context, req *concert.DeleteConcertImageReq, opts ...grpc.CallOption) (*concert.DeleteConcertImageResp, error) {
	resp, err := c.typedStub().DeleteConcertImage(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetConcertImageList(ctx context.Context, req *concert.GetConcertImageListReq, opts ...grpc.CallOption) (*concert.GetConcertImageListResp, error) {
	resp, err := c.typedStub().GetConcertImageList(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ReorderConcertImage(ctx context.Context, req *concert.ReorderConcertImageReq, opts ...grpc.CallOption) (*concert.ReorderConcertImageResp, error) {
	resp, err := c.typedStub().ReorderConcertImage(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetDefaultImage(ctx context.Context, req *concert.SetDefaultImageReq, opts ...grpc.CallOption) (*concert.SetDefaultImageResp, error) {
	resp, err := c.typedStub().SetDefaultImage(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRecentUploadedSong(ctx context.Context, req *concert.GetRecentUploadedSongReq, opts ...grpc.CallOption) (*concert.GetRecentUploadedSongResp, error) {
	resp, err := c.typedStub().GetRecentUploadedSong(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetConcertSongById(ctx context.Context, req *concert.GetConcertSongByIdReq, opts ...grpc.CallOption) (*concert.GetConcertSongByIdResp, error) {
	resp, err := c.typedStub().GetConcertSongById(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}
