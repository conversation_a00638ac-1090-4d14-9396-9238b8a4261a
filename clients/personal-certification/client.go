package personalcertification

import (
	"context"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/personalcertification"
	"google.golang.org/grpc"
)

const (
	serviceName = "personal-certification"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewPersonalCertificationClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.PersonalCertificationClient {
	return c.Stub().(pb.PersonalCertificationClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetPersonalCertification(ctx context.Context, uid, pos, limit uint32) (*pb.GetPersonalCertificationResp, protocol.ServerError) {
	resp, err := c.typedStub().GetPersonalCertification(ctx, &pb.GetPersonalCertificationReq{
		Uid:             uid,
		PresentPosition: pos,
		Limit:           limit,
		Valid:           true,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatGetPersonalCertification(ctx context.Context, in *pb.BatGetPersonalCertificationReq) (*pb.BatGetPersonalCertificationResp, error) {
	resp, err := c.typedStub().BatGetPersonalCertification(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetPriorityDisplayCert(ctx context.Context, uid, presentPosition, channelid uint32, certId string) error {
	_, err := c.typedStub().SetPriorityDisplayCert(ctx, &pb.SetPriorityDisplayCertReq{Uid: uid, CertId: certId, PresentPosition: presentPosition, ChannelId: channelid})
	return protocol.ToServerError(err)
}

func (c *Client) GetCertType(ctx context.Context, certId string) (*pb.GetCertTypeResp, error) {
	resp, err := c.typedStub().GetCertType(ctx, &pb.GetCertTypeReq{Id: certId})
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetCertTypeById(ctx context.Context, certId string) (*pb.GetCertTypeByIdResp, error) {
	resp, err := c.typedStub().GetCertTypeById(ctx, &pb.GetCertTypeByIdReq{Id: certId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetUserCert(ctx context.Context, in *pb.SetUserCertReq) (*pb.SetUserCertResp, error) {
	resp, err := c.typedStub().SetUserCert(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserCertByUid(ctx context.Context, in *pb.GetUserCertByUidReq) (*pb.GetUserCertByUidResp, error) {
	resp, err := c.typedStub().GetUserCertByUid(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserCert(ctx context.Context, in *pb.GetUserCertReq) (*pb.GetUserCertResp, error) {
	resp, err := c.typedStub().GetUserCert(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DelUserCertByID(ctx context.Context, in *pb.DelUserCertByIDReq) (*pb.DelUserCertByIDResp, error) {
	resp, err := c.typedStub().DelUserCertByID(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatDelUserCert(ctx context.Context, in *pb.BatDelUserCertReq) (*pb.BatDelUserCertResp, error) {
	resp, err := c.typedStub().BatDelUserCert(ctx, in)
	return resp, protocol.ToServerError(err)
}
