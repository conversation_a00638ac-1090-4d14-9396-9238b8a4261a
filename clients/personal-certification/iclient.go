package personalcertification

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/personalcertification"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetPersonalCertification(ctx context.Context, uid, pos, limit uint32) (*pb.GetPersonalCertificationResp, protocol.ServerError)
	BatGetPersonalCertification(ctx context.Context, in *pb.BatGetPersonalCertificationReq) (*pb.BatGetPersonalCertificationResp, error)
	SetPriorityDisplayCert(ctx context.Context, uid, presentPosition, channelid uint32, certId string) error
	SetUserCert(ctx context.Context, in *pb.SetUserCertReq) (*pb.SetUserCertResp, error)
	GetCertType(ctx context.Context, certId string) (*pb.GetCertTypeResp, error)
	GetCertTypeById(ctx context.Context, certId string) (*pb.GetCertTypeByIdResp, error)
	GetUserCertByUid(ctx context.Context, in *pb.GetUserCertByUidReq) (*pb.GetUserCertByUidResp, error)
	DelUserCertByID(ctx context.Context, in *pb.DelUserCertByIDReq) (*pb.DelUserCertByIDResp, error)
	BatDelUserCert(ctx context.Context, in *pb.BatDelUserCertReq) (*pb.BatDelUserCertResp, error)
	GetUserCert(ctx context.Context, in *pb.GetUserCertReq) (*pb.GetUserCertResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
