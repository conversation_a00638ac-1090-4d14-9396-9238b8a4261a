package numeric_rank

import (
	"context"
	"golang.52tt.com/pkg/log"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/numeric-rank"
	"google.golang.org/grpc"
)

const (
	serviceName = "numeric-rank"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewNumericRankClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.NumericRankClient { return c.Stub().(pb.NumericRankClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetUserGloryInfo(ctx context.Context, uid uint32) (*pb.GetUserGloryInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserGloryInfo(ctx, &pb.GetUserGloryInfoReq{Uid: uid})
	log.Debugf("GetUserGloryInfo uid:%d, resp:%+v, err:%v", uid, resp, err)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ReportGloryRank(ctx context.Context, req *pb.ReportGloryRankReq) (*pb.ReportGloryRankResp, protocol.ServerError) {
	resp, err := c.typedStub().ReportGloryRank(ctx, req)
	log.Debugf("GetUserGloryInfo req:%+v, resp:%+v, err:%v", req, resp, err)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetNumericRankList(ctx context.Context, req *pb.GetNumericRankListReq) (*pb.GetNumericRankListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetNumericRankList(ctx, req)
	log.Debugf("GetNumericRankList req:%+v, resp:%+v, err:%v", req, resp, err)
	return resp, protocol.ToServerError(err)
}
