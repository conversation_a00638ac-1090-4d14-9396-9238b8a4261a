// Code generated by quicksilver-cli. DO NOT EDIT.
package official_live_channel

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/official-live-channel"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	BatchGetOfficialChannelFollowInfo(ctx context.Context, channelIds []uint32) (map[uint32]*pb.OfficialChannelFollowInfo,protocol.ServerError)
	BatchGetRelayBySchedule(ctx context.Context, channelIds []uint32) (map[uint32]*pb.GetRelayByScheduleResp,protocol.ServerError)
	CancelRelay(ctx context.Context, channelId, sectionId uint32) protocol.ServerError
	GetAllRelayBy(ctx context.Context, req *pb.GetAllRelayByReq) (*pb.GetAllRelayByResp,protocol.ServerError)
	GetOfficialChannelDescribe(ctx context.Context, channelIds []uint32) (*pb.GetOfficialChannelDescribeResp,protocol.ServerError)
	GetOfficialChannelFollowInfo(ctx context.Context, channelId uint32) (*pb.OfficialChannelFollowInfo,protocol.ServerError)
	GetRelay(ctx context.Context, channelId uint32) (*pb.GetRelayResp,protocol.ServerError)
	GetRelayBy(ctx context.Context, channelId uint32) (*pb.GetRelayByResp,protocol.ServerError)
	GetRelayDetail(ctx context.Context, channelId uint32) (*pb.GetRelayDetailResp,protocol.ServerError)
	GetRelaySchedule(ctx context.Context, channelId uint32, scheduleId uint32) (*pb.GetRelayScheduleResp,protocol.ServerError)
	GetRelayScheduleDetail(ctx context.Context, channelId uint32) (*pb.GetRelayScheduleDetailResp,protocol.ServerError)
	GetRelayScheduleList(ctx context.Context, req *pb.GetRelayScheduleListReq) (*pb.GetRelayScheduleListResp,protocol.ServerError)
	ReportRelayAudio(ctx context.Context, channelId, uid uint32, streamId string) protocol.ServerError
	SetRelaySchedule(ctx context.Context, req *pb.SetRelayScheduleReq) (*pb.SetRelayScheduleResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
