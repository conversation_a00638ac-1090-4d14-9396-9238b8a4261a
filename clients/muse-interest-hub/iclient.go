package muse_interest_hub

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/muse-interest-hub/muse-interest-hub"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetMuseSwitchHub(ctx context.Context) (*pb.GetMuseSwitchHubResponse, protocol.ServerError)
	SetMuseSwitchHub(ctx context.Context, switchType uint32, IsOpen bool) protocol.ServerError
	BatGetUserMuseSwitch(ctx context.Context, uids []uint32, switchType uint32) (*pb.BatGetUserMuseSwitchResponse, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
