// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/muse-interest-hub (interfaces: IClient)

// Package muse_interest_hub is a generated GoMock package.
package muse_interest_hub

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	muse_interest_hub "golang.52tt.com/protocol/services/muse-interest-hub/muse-interest-hub"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatGetUserMuseSwitch mocks base method.
func (m *MockIClient) BatGetUserMuseSwitch(arg0 context.Context, arg1 []uint32, arg2 uint32) (*muse_interest_hub.BatGetUserMuseSwitchResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetUserMuseSwitch", arg0, arg1, arg2)
	ret0, _ := ret[0].(*muse_interest_hub.BatGetUserMuseSwitchResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatGetUserMuseSwitch indicates an expected call of BatGetUserMuseSwitch.
func (mr *MockIClientMockRecorder) BatGetUserMuseSwitch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetUserMuseSwitch", reflect.TypeOf((*MockIClient)(nil).BatGetUserMuseSwitch), arg0, arg1, arg2)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetMuseSwitchHub mocks base method.
func (m *MockIClient) GetMuseSwitchHub(arg0 context.Context) (*muse_interest_hub.GetMuseSwitchHubResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMuseSwitchHub", arg0)
	ret0, _ := ret[0].(*muse_interest_hub.GetMuseSwitchHubResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMuseSwitchHub indicates an expected call of GetMuseSwitchHub.
func (mr *MockIClientMockRecorder) GetMuseSwitchHub(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMuseSwitchHub", reflect.TypeOf((*MockIClient)(nil).GetMuseSwitchHub), arg0)
}

// SetMuseSwitchHub mocks base method.
func (m *MockIClient) SetMuseSwitchHub(arg0 context.Context, arg1 uint32, arg2 bool) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMuseSwitchHub", arg0, arg1, arg2)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// SetMuseSwitchHub indicates an expected call of SetMuseSwitchHub.
func (mr *MockIClientMockRecorder) SetMuseSwitchHub(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMuseSwitchHub", reflect.TypeOf((*MockIClient)(nil).SetMuseSwitchHub), arg0, arg1, arg2)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
