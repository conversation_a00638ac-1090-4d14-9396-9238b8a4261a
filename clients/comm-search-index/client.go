package comm_search_index

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/comm-search-index"
	"google.golang.org/grpc"
)

const (
	serviceName = "comm-search-index"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewCommSearchIndexClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.CommSearchIndexClient { return c.Stub().(pb.CommSearchIndexClient) }

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func (c *Client) AddChannelMusicSearchIndex(ctx context.Context, in *pb.AddChannelMusicSearchIndexReq) (out *pb.AddChannelMusicSearchIndexResp, err error) {
	out, err = c.typedStub().AddChannelMusicSearchIndex(ctx, in)
	return out, err
}

func (c *Client) DelChannelMusicSearchIndex(ctx context.Context, in *pb.DelChannelMusicSearchIndexReq) (out *pb.DelChannelMusicSearchIndexResp, err error) {
	out, err = c.typedStub().DelChannelMusicSearchIndex(ctx, in)
	return out, err
}
