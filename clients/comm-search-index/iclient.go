package comm_search_index

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/comm-search-index"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AddChannelMusicSearchIndex(ctx context.Context, in *pb.AddChannelMusicSearchIndexReq) (out *pb.AddChannelMusicSearchIndexResp, err error)
	DelChannelMusicSearchIndex(ctx context.Context, in *pb.DelChannelMusicSearchIndexReq) (out *pb.DelChannelMusicSearchIndexResp, err error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
