package competition_entrance

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/competition-entrance"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetActiveEntrance(ctx context.Context, in *pb.GetActiveEntranceReq) (*pb.GetActiveEntranceRsp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
