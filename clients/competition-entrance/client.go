package competition_entrance

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/competition-entrance"
	"google.golang.org/grpc"
)

const (
	serviceName = "competition-entrance"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewCompetitionEntranceClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.CompetitionEntranceClient {
	return c.Stub().(pb.CompetitionEntranceClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func (c *Client) GetActiveEntrance(ctx context.Context, in *pb.GetActiveEntranceReq) (*pb.GetActiveEntranceRsp, error) {
	resp, err := c.typedStub().GetActiveEntrance(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGameTmpChannelCfg(ctx context.Context, in *pb.GetGameTmpChannelCfgReq) (*pb.GetGameTmpChannelCfgResp, error) {
	resp, err := c.typedStub().GetGameTmpChannelCfg(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetActiveComponentList(ctx context.Context, in *pb.GetActiveComponentListReq) (*pb.GetActiveComponentListResp, error) {
	resp, err := c.typedStub().GetActiveComponentList(ctx, in)
	return resp, protocol.ToServerError(err)
}
