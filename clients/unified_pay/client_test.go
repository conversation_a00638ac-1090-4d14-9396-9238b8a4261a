package unifiedPay

import (
	"fmt"
	"testing"
	"time"

	pb "golang.52tt.com/protocol/services/unified_pay"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

var (
	testClient *Client
)

func init() {
	var err error
	testClient, err = NewClient(grpc.WithBlock(), grpc.WithAuthority("unified-pay.52tt.local"))
	if err != nil {
		panic(err)
	}
}

func TestClient_DirectPayWithRedDiamonds(t *testing.T) {
	err := testClient.DirectPay(context.Background(), &pb.PayOrder{
		AppId:      "TT_HZ",
		OutTradeNo: fmt.Sprintf("DirectPay_RD_%d", time.Now().UnixNano()),
		Uid:        500009,
		UserName:   "ezreal",
		FeeType:    pb.FeeType_RED_DIAMOND,
		TotalFee:   10,
		Body:       "Direct pay with red diamond",
		Detail:     "Direct pay with red diamond",
	})

	if err != nil {
		t.Errorf("DirectPay with red diamond failed: %v", err)
	} else {
		t.Logf("DirectPay with red diamond OK")
	}
}

func TestClient_DirectPayWithRedDiamonds2(t *testing.T) {
	err := testClient.DirectPay(context.Background(), &pb.PayOrder{
		AppId:      "TT_HZ",
		OutTradeNo: fmt.Sprintf("DirectPay_RD_%d", time.Now().UnixNano()),
		Uid:        509,
		UserName:   "ezreal",
		FeeType:    pb.FeeType_RED_DIAMOND,
		TotalFee:   10,
		Body:       "Direct pay with red diamond",
		Detail:     "Direct pay with red diamond",
	})

	if err != nil {
		t.Errorf("DirectPay with red diamond failed: %v", err)
	} else {
		t.Logf("DirectPay with red diamond OK")
	}
}

func TestClient_DirectPayWithTBean(t *testing.T) {
	err := testClient.DirectPay(context.Background(), &pb.PayOrder{
		AppId:      "TT_HZ",
		OutTradeNo: fmt.Sprintf("DirectPay_TBEAN_%d", time.Now().UnixNano()),
		Uid:        500009,
		UserName:   "ezreal",
		FeeType:    pb.FeeType_TBEAN,
		TotalFee:   10,
		Body:       "Direct pay with tbean",
		Detail:     "Direct pay with tbean",
	})

	if err != nil {
		t.Errorf("DirectPay with tbean failed: %v", err)
	} else {
		t.Logf("DirectPay with tbean OK")
	}
}

func TestClient_DirectTrans(t *testing.T) {
	err := testClient.DirectTrans(context.Background(), &pb.TransOrder{
		AppId:          "TT_HZ",
		OutTradeNo:     fmt.Sprintf("DirectTrans_TBEAN_%d", time.Now().UnixNano()),
		FromUid:        10400,
		FromUserName:   "God of 'Identity V'",
		TargetUid:      500001,
		TargetUserName: "ezreal",
		FeeType:        pb.FeeType_TBEAN,
		TotalFee:       1,
		Status:         pb.PayOrderStatus_INIT,
	})

	if err != nil {
		t.Errorf("DirectTrans with tbean failed: %v", err)
	} else {
		t.Logf("DirectTrans with tbean OK")
	}
}

func TestClient_PresetFreeze(t *testing.T) {
	freeze, serverError := testClient.PresetFreeze(context.Background(), 2190004, 100, "TT", "abc1234567",
		time.Now().Format("2006-01-02 15:04:05"), "test")
	if serverError != nil {
		panic(serverError)
	}
	fmt.Println(freeze)
}

func TestClient_UnfreezeAndConsume(t *testing.T) {
	req := pb.UnfreezeAndConsumeReq{
		AppId:        "TT",
		Uid:          2190004,
		UserName:     "abc",
		ItemId:       123,
		ItemName:     "abc",
		ItemNum:      1,
		ItemPrice:    100,
		TotalPrice:   100,
		Platform:     "0",
		OutTradeNo:   "abc1234567",
		Notes:        "",
		OutOrderTime: "",
	}
	consume, token, serverError := testClient.UnfreezeAndConsume(context.Background(), &req)
	if serverError != nil {
		panic(serverError)
	}
	fmt.Println(consume, token)
}
