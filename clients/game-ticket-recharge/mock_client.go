// Code generated by MockGen. DO NOT EDIT.
// Source: iclient.go

// Package game_ticket_recharge is a generated GoMock package.
package game_ticket_recharge

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	game_ticket_recharge "golang.52tt.com/protocol/services/game-ticket-recharge"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DelGameTicketFreebieCfg mocks base method.
func (m *MockIClient) DelGameTicketFreebieCfg(ctx context.Context, opUid uint32, req *game_ticket_recharge.DelGameTicketFreebieCfgReq) (*game_ticket_recharge.DelGameTicketFreebieCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelGameTicketFreebieCfg", ctx, opUid, req)
	ret0, _ := ret[0].(*game_ticket_recharge.DelGameTicketFreebieCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelGameTicketFreebieCfg indicates an expected call of DelGameTicketFreebieCfg.
func (mr *MockIClientMockRecorder) DelGameTicketFreebieCfg(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelGameTicketFreebieCfg", reflect.TypeOf((*MockIClient)(nil).DelGameTicketFreebieCfg), ctx, opUid, req)
}

// DoRecharge mocks base method.
func (m *MockIClient) DoRecharge(ctx context.Context, req *game_ticket_recharge.DoRechargeReq) (*game_ticket_recharge.DoRechargeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DoRecharge", ctx, req)
	ret0, _ := ret[0].(*game_ticket_recharge.DoRechargeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DoRecharge indicates an expected call of DoRecharge.
func (mr *MockIClientMockRecorder) DoRecharge(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoRecharge", reflect.TypeOf((*MockIClient)(nil).DoRecharge), ctx, req)
}

// GetALLFreebieDetailCfg mocks base method.
func (m *MockIClient) GetALLFreebieDetailCfg(ctx context.Context, opUid uint32, req *game_ticket_recharge.GetALLFreebieDetailCfgReq) (*game_ticket_recharge.GetALLFreebieDetailCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetALLFreebieDetailCfg", ctx, opUid, req)
	ret0, _ := ret[0].(*game_ticket_recharge.GetALLFreebieDetailCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetALLFreebieDetailCfg indicates an expected call of GetALLFreebieDetailCfg.
func (mr *MockIClientMockRecorder) GetALLFreebieDetailCfg(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetALLFreebieDetailCfg", reflect.TypeOf((*MockIClient)(nil).GetALLFreebieDetailCfg), ctx, opUid, req)
}

// GetFreebieCfgByGameType mocks base method.
func (m *MockIClient) GetFreebieCfgByGameType(ctx context.Context, opUid uint32, req *game_ticket_recharge.GetFreebieCfgByGameTypeReq) (*game_ticket_recharge.GetFreebieCfgByGameTypeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFreebieCfgByGameType", ctx, opUid, req)
	ret0, _ := ret[0].(*game_ticket_recharge.GetFreebieCfgByGameTypeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFreebieCfgByGameType indicates an expected call of GetFreebieCfgByGameType.
func (mr *MockIClientMockRecorder) GetFreebieCfgByGameType(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFreebieCfgByGameType", reflect.TypeOf((*MockIClient)(nil).GetFreebieCfgByGameType), ctx, opUid, req)
}

// GetGameTicketFreebieCfg mocks base method.
func (m *MockIClient) GetGameTicketFreebieCfg(ctx context.Context, freebieId uint32) (*game_ticket_recharge.GetGameTicketFreebieCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameTicketFreebieCfg", ctx, freebieId)
	ret0, _ := ret[0].(*game_ticket_recharge.GetGameTicketFreebieCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGameTicketFreebieCfg indicates an expected call of GetGameTicketFreebieCfg.
func (mr *MockIClientMockRecorder) GetGameTicketFreebieCfg(ctx, freebieId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameTicketFreebieCfg", reflect.TypeOf((*MockIClient)(nil).GetGameTicketFreebieCfg), ctx, freebieId)
}

// GetMiJingPayChannel mocks base method.
func (m *MockIClient) GetMiJingPayChannel(ctx context.Context, osType uint32) (*game_ticket_recharge.GetMiJingPayChannelResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMiJingPayChannel", ctx, osType)
	ret0, _ := ret[0].(*game_ticket_recharge.GetMiJingPayChannelResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMiJingPayChannel indicates an expected call of GetMiJingPayChannel.
func (mr *MockIClientMockRecorder) GetMiJingPayChannel(ctx, osType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMiJingPayChannel", reflect.TypeOf((*MockIClient)(nil).GetMiJingPayChannel), ctx, osType)
}

// GetMiJingPayConfig mocks base method.
func (m *MockIClient) GetMiJingPayConfig(ctx context.Context, osType uint32) (*game_ticket_recharge.GetMiJingPayConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMiJingPayConfig", ctx, osType)
	ret0, _ := ret[0].(*game_ticket_recharge.GetMiJingPayConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMiJingPayConfig indicates an expected call of GetMiJingPayConfig.
func (mr *MockIClientMockRecorder) GetMiJingPayConfig(ctx, osType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMiJingPayConfig", reflect.TypeOf((*MockIClient)(nil).GetMiJingPayConfig), ctx, osType)
}

// GetMiJingPayResult mocks base method.
func (m *MockIClient) GetMiJingPayResult(ctx context.Context, req *game_ticket_recharge.GetMiJingPayResultReq) (*game_ticket_recharge.GetMiJingPayResultResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMiJingPayResult", ctx, req)
	ret0, _ := ret[0].(*game_ticket_recharge.GetMiJingPayResultResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMiJingPayResult indicates an expected call of GetMiJingPayResult.
func (mr *MockIClientMockRecorder) GetMiJingPayResult(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMiJingPayResult", reflect.TypeOf((*MockIClient)(nil).GetMiJingPayResult), ctx, req)
}

// MiJingPay mocks base method.
func (m *MockIClient) MiJingPay(ctx context.Context, req *game_ticket_recharge.MiJingPayReq) (*game_ticket_recharge.MiJingPayResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MiJingPay", ctx, req)
	ret0, _ := ret[0].(*game_ticket_recharge.MiJingPayResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// MiJingPay indicates an expected call of MiJingPay.
func (mr *MockIClientMockRecorder) MiJingPay(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MiJingPay", reflect.TypeOf((*MockIClient)(nil).MiJingPay), ctx, req)
}

// SetGameTicketFreebieCfg mocks base method.
func (m *MockIClient) SetGameTicketFreebieCfg(ctx context.Context, opUid uint32, req *game_ticket_recharge.SetGameTicketFreebieCfgReq) (*game_ticket_recharge.SetGameTicketFreebieCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGameTicketFreebieCfg", ctx, opUid, req)
	ret0, _ := ret[0].(*game_ticket_recharge.SetGameTicketFreebieCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetGameTicketFreebieCfg indicates an expected call of SetGameTicketFreebieCfg.
func (mr *MockIClientMockRecorder) SetGameTicketFreebieCfg(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGameTicketFreebieCfg", reflect.TypeOf((*MockIClient)(nil).SetGameTicketFreebieCfg), ctx, opUid, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
