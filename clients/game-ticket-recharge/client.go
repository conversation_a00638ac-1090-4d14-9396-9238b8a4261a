package game_ticket_recharge

import (
	"context"
	"strconv"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/game-ticket-recharge"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

const serviceName = "game-ticket-recharge"

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewGameTicketRechargeClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.GameTicketRechargeClient {
	return c.Stub().(pb.GameTicketRechargeClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) DoRecharge(ctx context.Context, req *pb.DoRechargeReq) (*pb.DoRechargeResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(req.GetUid()))))
	resp, err := c.typedStub().DoRecharge(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFreebieCfgByGameType(ctx context.Context, opUid uint32, req *pb.GetFreebieCfgByGameTypeReq) (*pb.GetFreebieCfgByGameTypeResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetFreebieCfgByGameType(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetGameTicketFreebieCfg(ctx context.Context, opUid uint32, req *pb.SetGameTicketFreebieCfgReq) (*pb.SetGameTicketFreebieCfgResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().SetGameTicketFreebieCfg(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetALLFreebieDetailCfg(ctx context.Context, opUid uint32, req *pb.GetALLFreebieDetailCfgReq) (*pb.GetALLFreebieDetailCfgResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetALLFreebieDetailCfg(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DelGameTicketFreebieCfg(ctx context.Context, opUid uint32, req *pb.DelGameTicketFreebieCfgReq) (*pb.DelGameTicketFreebieCfgResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().DelGameTicketFreebieCfg(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGameTicketFreebieCfg(ctx context.Context, freebieId uint32) (*pb.GetGameTicketFreebieCfgResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGameTicketFreebieCfg(ctx, &pb.GetGameTicketFreebieCfgReq{
		FreebieId: freebieId,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetMiJingPayConfig(ctx context.Context, osType uint32) (*pb.GetMiJingPayConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().GetMiJingPayConfig(ctx, &pb.GetMiJingPayConfigReq{
		OsType: osType,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetMiJingPayChannel(ctx context.Context, osType uint32) (*pb.GetMiJingPayChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().GetMiJingPayChannel(ctx, &pb.GetMiJingPayChannelReq{
		OsType: osType,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) MiJingPay(ctx context.Context, req *pb.MiJingPayReq) (*pb.MiJingPayResp, protocol.ServerError) {
	resp, err := c.typedStub().MiJingPay(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetMiJingPayResult(ctx context.Context, req *pb.GetMiJingPayResultReq) (*pb.GetMiJingPayResultResp, protocol.ServerError) {
	resp, err := c.typedStub().GetMiJingPayResult(ctx, req)
	return resp, protocol.ToServerError(err)
}
