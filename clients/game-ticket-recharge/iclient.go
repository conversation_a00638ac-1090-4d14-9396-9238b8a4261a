package game_ticket_recharge

import (
	"context"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/game-ticket-recharge"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	DoRecharge(ctx context.Context, req *pb.DoRechargeReq) (*pb.DoRechargeResp, protocol.ServerError)
	GetFreebieCfgByGameType(ctx context.Context, opUid uint32, req *pb.GetFreebieCfgByGameTypeReq) (*pb.GetFreebieCfgByGameTypeResp, protocol.ServerError)
	SetGameTicketFreebieCfg(ctx context.Context, opUid uint32, req *pb.SetGameTicketFreebieCfgReq) (*pb.SetGameTicketFreebieCfgResp, protocol.ServerError)
	GetALLFreebieDetailCfg(ctx context.Context, opUid uint32, req *pb.GetALLFreebieDetailCfgReq) (*pb.GetALLFreebieDetailCfgResp, protocol.ServerError)
	DelGameTicketFreebieCfg(ctx context.Context, opUid uint32, req *pb.DelGameTicketFreebieCfgReq) (*pb.DelGameTicketFreebieCfgResp, protocol.ServerError)
	GetGameTicketFreebieCfg(ctx context.Context, freebieId uint32) (*pb.GetGameTicketFreebieCfgResp, protocol.ServerError)
	GetMiJingPayConfig(ctx context.Context, osType uint32) (*pb.GetMiJingPayConfigResp, protocol.ServerError)
	GetMiJingPayChannel(ctx context.Context, osType uint32) (*pb.GetMiJingPayChannelResp, protocol.ServerError)
	MiJingPay(ctx context.Context, req *pb.MiJingPayReq) (*pb.MiJingPayResp, protocol.ServerError)
	GetMiJingPayResult(ctx context.Context, req *pb.GetMiJingPayResultReq) (*pb.GetMiJingPayResultResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
