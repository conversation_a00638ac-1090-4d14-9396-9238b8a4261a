// Code generated by quicksilver-cli. DO NOT EDIT.
package starguild

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "golang.org/x/net/context"
	pb "golang.52tt.com/protocol/services/starguildsvr/v3"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	GetGuildStarInfo(ctx context.Context, guildId uint32, queryToday bool) (*StarGuildInfo,error)
	GetGuildStarInfos(ctx context.Context, req *pb.GetGuildStarInfosReq) (resp *pb.GetGuildStarInfosResp,err error)
	GetGuildStarLevel(ctx context.Context, uid uint32, guildIDs []uint32) (map[uint32]uint32,protocol.ServerError)
	UserAddContribute(ctx context.Context, uid, guildID, eventType, reddiamondAmount, moneyAmount, consumeType uint32, valid bool, orderID string) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
