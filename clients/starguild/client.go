package starguild

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/starguildsvr/v3"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

const (
	serviceName = "starguildv3"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewStarGuildV3Client(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.StarGuildV3Client { return c.Stub().(pb.StarGuildV3Client) }

// UserAddContribute .
func (c *Client) UserAddContribute(ctx context.Context, uid, guildID, eventType, reddiamondAmount, moneyAmount, consumeType uint32, valid bool, orderID string) protocol.ServerError {
	_, err := c.typedStub().UserAddContribute(ctx, &pb.UserAddContributeReq{
		Uid:              uid,
		GuildId:          guildID,
		EventType:        eventType,
		ReddiamondAmount: reddiamondAmount,
		MoneyAmount:      moneyAmount,
		ConsumeType:      consumeType,
		Valid:            valid,
		OrderId:          orderID,
	})
	return protocol.ToServerError(err)
}

// GetGuildStarLevel .
func (c *Client) GetGuildStarLevel(ctx context.Context, uid uint32, guildIDs []uint32) (map[uint32]uint32, protocol.ServerError) {
	resp, err := c.typedStub().GetGuildStarInfos(ctx, &pb.GetGuildStarInfosReq{
		GuildIdList: guildIDs,
		QueryToday:  false,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	t := map[uint32]uint32{}
	for _, info := range resp.GetStarInfoList() {
		t[info.GetGuildId()] = info.GetStarLevel()
	}
	return t, nil
}

func (c *Client) GetGuildStarInfos(ctx context.Context, req *pb.GetGuildStarInfosReq) (resp *pb.GetGuildStarInfosResp, err error) {
	resp, err = c.typedStub().GetGuildStarInfos(ctx, req)
	return resp, err
}

type StarGuildInfo = pb.StarGuildInfo

func (c *Client) GetGuildStarInfo(ctx context.Context, guildId uint32, queryToday bool) (*StarGuildInfo, error) {
	resp, err := c.typedStub().GetGuildStarInfos(ctx, &pb.GetGuildStarInfosReq{
		GuildIdList: []uint32{guildId},
		QueryToday:  queryToday,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	if len(resp.StarInfoList) == 0 {
		return nil, nil
	}
	return resp.StarInfoList[0], nil
}
