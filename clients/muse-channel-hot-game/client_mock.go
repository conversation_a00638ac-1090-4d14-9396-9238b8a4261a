// Code generated by MockGen. DO NOT EDIT.
// Source: iclient.go

// Package mock_muse_channel_hot_game is a generated GoMock package.
package muse_channel_hot_game

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	muse_channel_hot_game "golang.52tt.com/protocol/services/muse-channel-hot-game"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchChannelViewDesc mocks base method.
func (m *MockIClient) BatchChannelViewDesc(ctx context.Context, req *muse_channel_hot_game.BatchChannelViewTopicRequest) (*muse_channel_hot_game.BatchChannelViewTopicResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchChannelViewDesc", ctx, req)
	ret0, _ := ret[0].(*muse_channel_hot_game.BatchChannelViewTopicResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchChannelViewDesc indicates an expected call of BatchChannelViewDesc.
func (mr *MockIClientMockRecorder) BatchChannelViewDesc(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchChannelViewDesc", reflect.TypeOf((*MockIClient)(nil).BatchChannelViewDesc), ctx, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetMuseChannelHotGame mocks base method.
func (m *MockIClient) GetMuseChannelHotGame(ctx context.Context, req *muse_channel_hot_game.GetMuseChannelHotGameRequest) (*muse_channel_hot_game.GetMuseChannelHotGameResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMuseChannelHotGame", ctx, req)
	ret0, _ := ret[0].(*muse_channel_hot_game.GetMuseChannelHotGameResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMuseChannelHotGame indicates an expected call of GetMuseChannelHotGame.
func (mr *MockIClientMockRecorder) GetMuseChannelHotGame(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMuseChannelHotGame", reflect.TypeOf((*MockIClient)(nil).GetMuseChannelHotGame), ctx, req)
}

// GetMuseChannelHotGameDetail mocks base method.
func (m *MockIClient) GetMuseChannelHotGameDetail(ctx context.Context, req *muse_channel_hot_game.GetMuseChannelHotGameDetailRequest) (*muse_channel_hot_game.GetMuseChannelHotGameDetailResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMuseChannelHotGameDetail", ctx, req)
	ret0, _ := ret[0].(*muse_channel_hot_game.GetMuseChannelHotGameDetailResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMuseChannelHotGameDetail indicates an expected call of GetMuseChannelHotGameDetail.
func (mr *MockIClientMockRecorder) GetMuseChannelHotGameDetail(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMuseChannelHotGameDetail", reflect.TypeOf((*MockIClient)(nil).GetMuseChannelHotGameDetail), ctx, req)
}

// GetMuseChannelHotGameFloat mocks base method.
func (m *MockIClient) GetMuseChannelHotGameFloat(ctx context.Context, req *muse_channel_hot_game.GetMuseChannelHotGameFloatRequest) (*muse_channel_hot_game.GetMuseChannelHotGameFloatResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMuseChannelHotGameFloat", ctx, req)
	ret0, _ := ret[0].(*muse_channel_hot_game.GetMuseChannelHotGameFloatResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMuseChannelHotGameFloat indicates an expected call of GetMuseChannelHotGameFloat.
func (mr *MockIClientMockRecorder) GetMuseChannelHotGameFloat(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMuseChannelHotGameFloat", reflect.TypeOf((*MockIClient)(nil).GetMuseChannelHotGameFloat), ctx, req)
}

// GetMuseUserInChannelId mocks base method.
func (m *MockIClient) GetMuseUserInChannelId(ctx context.Context, req *muse_channel_hot_game.GetMuseUserInChannelIdRequest) (*muse_channel_hot_game.GetMuseUserInChannelIdResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMuseUserInChannelId", ctx, req)
	ret0, _ := ret[0].(*muse_channel_hot_game.GetMuseUserInChannelIdResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMuseUserInChannelId indicates an expected call of GetMuseUserInChannelId.
func (mr *MockIClientMockRecorder) GetMuseUserInChannelId(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMuseUserInChannelId", reflect.TypeOf((*MockIClient)(nil).GetMuseUserInChannelId), ctx, req)
}

// SetMuseChannelHotGameJoinStatus mocks base method.
func (m *MockIClient) SetMuseChannelHotGameJoinStatus(ctx context.Context, req *muse_channel_hot_game.SetMuseChannelHotGameJoinStatusRequest) (*muse_channel_hot_game.SetMuseChannelHotGameJoinStatusResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMuseChannelHotGameJoinStatus", ctx, req)
	ret0, _ := ret[0].(*muse_channel_hot_game.SetMuseChannelHotGameJoinStatusResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetMuseChannelHotGameJoinStatus indicates an expected call of SetMuseChannelHotGameJoinStatus.
func (mr *MockIClientMockRecorder) SetMuseChannelHotGameJoinStatus(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMuseChannelHotGameJoinStatus", reflect.TypeOf((*MockIClient)(nil).SetMuseChannelHotGameJoinStatus), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
