package muse_channel_hot_game

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/muse-channel-hot-game"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetMuseChannelHotGame(ctx context.Context, req *pb.GetMuseChannelHotGameRequest) (*pb.GetMuseChannelHotGameResponse, protocol.ServerError)
	GetMuseChannelHotGameFloat(ctx context.Context, req *pb.GetMuseChannelHotGameFloatRequest) (*pb.GetMuseChannelHotGameFloatResponse, protocol.ServerError)
	SetMuseChannelHotGameJoinStatus(ctx context.Context, req *pb.SetMuseChannelHotGameJoinStatusRequest) (*pb.SetMuseChannelHotGameJoinStatusResponse, protocol.ServerError)
	GetMuseChannelHotGameDetail(ctx context.Context, req *pb.GetMuseChannelHotGameDetailRequest) (*pb.GetMuseChannelHotGameDetailResponse, protocol.ServerError)
	GetMuseUserInChannelId(ctx context.Context, req *pb.GetMuseUserInChannelIdRequest) (*pb.GetMuseUserInChannelIdResponse, protocol.ServerError)
	BatchChannelViewDesc(ctx context.Context, req *pb.BatchChannelViewTopicRequest) (*pb.BatchChannelViewTopicResponse, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
