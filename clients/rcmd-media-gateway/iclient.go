package rcmd_media_gateway

import (
	"context"

	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/ai_voice/media_gateway"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GenVoiceUrl(ctx context.Context, channelId, minNum, maxNum uint32) (string, error)
	GetContentVoiceUrls(context.Context, *pb.MediaVoiceWzryGuideReq) (*pb.MediaVoiceWzryGuideRsp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
