package guildhonorhalls

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/guildhonorhalls"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetHonorGuildList(ctx context.Context, req *pb.GetHonorGuildListReq) (*pb.GetHonorGuildListResp, protocol.ServerError)
	GetHonorGuild(ctx context.Context, req *pb.GetHonorGuildReq) (*pb.GetHonorGuildResp, protocol.ServerError)
	GetGuildHotChannelList(ctx context.Context, req *pb.GetGuildHotChannelListReq) (*pb.GetGuildHotChannelListResp, protocol.ServerError)
	GetGuildCharmMemberList(ctx context.Context, req *pb.GetGuildCharmMemberListReq) (*pb.GetGuildCharmMemberListResp, protocol.ServerError)
	SetGuildHotChannelList(ctx context.Context, req *pb.SetGuildHotChannelListReq) (*pb.SetGuildHotChannelListResp, protocol.ServerError)
	SetGuildCharmMemberList(ctx context.Context, req *pb.SetGuildCharmMemberListReq) (*pb.SetGuildCharmMemberListResp, protocol.ServerError)
	SearchGuildMember(ctx context.Context, req *pb.SearchGuildMemberReq) (*pb.SearchGuildMemberResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
