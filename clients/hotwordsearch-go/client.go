package hotwordsearch_go

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/hotwordsearch-go"
	"google.golang.org/grpc"
)

const (
	serviceName = "hotwordsearch-go"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewHotWordSearchClient(cc)
			}, dopts...,
		),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.HotWordSearchClient { return c.Stub().(pb.HotWordSearchClient) }

func (c *Client) GetFirstHotWordGroupLen(ctx context.Context, version uint32) (uint32, protocol.ServerError) {
	resp, err := c.typedStub().GetFirstHotWordGroupLen(ctx, &pb.GetFirstHotWordGroupLenReq{Version: version})
	return resp.GetFirstGroupLen(), protocol.ToServerError(err)
}

func (c *Client) FetchHotWordGroups(ctx context.Context, startId, size, version uint32) (*pb.HotWordSearchFetchResp, protocol.ServerError) {
	resp, err := c.typedStub().FetchHotWordGroups(ctx, &pb.HotWordSearchFetchReq{
		StartGrpIdx:          startId,
		RequiredSize:         size,
		Version:              version,
	})
	return resp, protocol.ToServerError(err)
}