package hotwordsearch_go

import(
	protocol "golang.52tt.com/pkg/protocol"
	hotwordsearch_go "golang.52tt.com/protocol/services/hotwordsearch-go"
	context "context"
)

type IClient interface {
	FetchHotWordGroups(ctx context.Context, startId, size, version uint32) (*hotwordsearch_go.HotWordSearchFetchResp, protocol.ServerError)
	GetFirstHotWordGroupLen(ctx context.Context, version uint32) (uint32, protocol.ServerError)
}

