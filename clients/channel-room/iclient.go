package channel_room

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/channel-room"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	CreateChannelRoom(ctx context.Context, in *pb.CreateChannelRoomReq) (*pb.CreateChannelRoomResp, error)
	DestroyChannelRoom(ctx context.Context, in *pb.DestroyChannelRoomReq) (*pb.DestroyChannelRoomResp, error)
	GetMiniGameMathInfo(ctx context.Context, in *pb.GetMiniGameMathInfoReq) (*pb.GetMiniGameMathInfoResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
