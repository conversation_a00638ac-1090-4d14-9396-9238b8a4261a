package channel_room

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/channel-room"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-room"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelRoomClient(cc)
			},
			dopts...,
		),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.ChannelRoomClient {
	return c.Stub().(pb.ChannelRoomClient)
}

func (c *Client) CreateChannelRoom(ctx context.Context, in *pb.CreateChannelRoomReq) (*pb.CreateChannelRoomResp, error) {
	resp, err := c.typedStub().CreateChannelRoom(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DestroyChannelRoom(ctx context.Context, in *pb.DestroyChannelRoomReq) (*pb.DestroyChannelRoomResp, error)  {
	resp, err := c.typedStub().DestroyChannelRoom(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetMiniGameMathInfo(ctx context.Context, in *pb.GetMiniGameMathInfoReq) (*pb.GetMiniGameMathInfoResp, error) {
	resp, err := c.typedStub().GetMiniGameMathInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}