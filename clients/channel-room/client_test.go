package channel_room

import (
	"context"
	pb "golang.52tt.com/protocol/services/channel-room"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
	"log"
	"os"
	"testing"
)

var cli *Client

func init() {

	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))

	var err error

	cli, err = NewClient(grpc.WithBlock())
	if err != nil {
		log.Fatalln(err)
	}
	return
}

func TestClient_CreateChannelRoom(t *testing.T) {
	req := &pb.CreateChannelRoomReq{
		Uid:          2215142,
		TabId:        104,
		BlockOptions: []*pb.BlockOption{},
	}
	resp, err := cli.CreateChannelRoom(context.Background(), req)
	if err != nil {
		t.Log(err)
		return
	}
	log.Println(resp)
}
