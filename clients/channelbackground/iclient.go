package channelbackground

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channelbackground"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetChannelBackgroundConf(ctx context.Context) (*pb.GetChannelBackgroundConfResp, protocol.ServerError)
	AddChannelBackgroundConf(ctx context.Context, info *pb.ChannelBackgroundInfo) (*pb.AddChannelBackgroundConfResp, protocol.ServerError)
	DelChannelBackgroundConf(ctx context.Context, backgroundId uint64) (*pb.DelChannelBackgroundConfResp, protocol.ServerError)
	GetCurrentChannelBackground(ctx context.Context, channelId uint32) (*pb.GetCurrentChannelBackgroundResp, protocol.ServerError)
	SetCurrentChannelBackground(ctx context.Context, channelId uint32, backgroundId uint64) (*pb.SetCurrentChannelBackgroundResp, protocol.ServerError)
	CheckChannelBackgroundUpdate(ctx context.Context, uid uint32) (*pb.CheckChannelBackgroundUpdateResp, protocol.ServerError)
	GetChannelBackgroundList(ctx context.Context, channelId uint32) (*pb.GetChannelBackgroundListResp, protocol.ServerError)
	GetChannelLiveBgList(ctx context.Context, uid uint32, channelId uint32) (*pb.GetChannelLiveBgListResp, protocol.ServerError)
	GiveChannelLiveBg(ctx context.Context, channelId, targetUid uint32, backgroundId uint64, incrExpireSec int64) protocol.ServerError
	GetChannelCurrLiveBg(ctx context.Context, uid uint32, channelId uint32) (*pb.GetChannelCurrLiveBgResp, protocol.ServerError)
	SetChannelCurrLiveBg(ctx context.Context, uid uint32, channelId uint32, backgroundId uint64) (*pb.SetChannelCurrLiveBgResp, protocol.ServerError)
	AddLiveChannelBgConf(ctx context.Context, req *pb.AddLiveChannelBgConfReq) protocol.ServerError
	GetLiveChannelBgConf(ctx context.Context) (*pb.GetLiveChannelBgConfResp, protocol.ServerError)
	DelLiveChannelBgConf(ctx context.Context, backgroundId uint64) protocol.ServerError
	AddChannelBackgroundConfV2(ctx context.Context, in *pb.AddChannelBackgroundConfReq, opts ...grpc.CallOption) (*pb.AddChannelBackgroundConfResp, error)
	UpdateChannelBackgroundConfV2(ctx context.Context, in *pb.UpdateChannelBackgroundConfReq, opts ...grpc.CallOption) (*pb.UpdateChannelBackgroundConfResp, error)
	DelChannelBackgroundConfV2(ctx context.Context, in *pb.DelChannelBackgroundConfReq, opts ...grpc.CallOption) (*pb.DelChannelBackgroundConfResp, error)
	GetChannelBackgroundConfV2(ctx context.Context, in *pb.GetChannelBackgroundConfV2Req, opts ...grpc.CallOption) (*pb.GetChannelBackgroundConfV2Resp, error)
	BatchGiveChannelBg(ctx context.Context, in *pb.BatchGiveChannelBgReq, opts ...grpc.CallOption) (*pb.BatchGiveChannelBgResp, error)
	UpdateGivenChannelBg(ctx context.Context, in *pb.UpdateGivenChannelBgReq, opts ...grpc.CallOption) (*pb.UpdateGivenChannelBgResp, error)
	DeleteGivenChannelBg(ctx context.Context, in *pb.DeleteGivenChannelBgReq, opts ...grpc.CallOption) (*pb.DeleteGivenChannelBgResp, error)
	ListGivenChannelBg(ctx context.Context, in *pb.ListGivenChannelBgReq, opts ...grpc.CallOption) (*pb.ListGivenChannelBgResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
