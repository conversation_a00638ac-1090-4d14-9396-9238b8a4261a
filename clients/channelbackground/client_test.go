package channelbackground

import (
	"context"
	"google.golang.org/grpc"
	"time"

	pb "golang.52tt.com/protocol/services/channelbackground"
	"testing"
)

/*
func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.<PERSON>dout, os.Stdout, os.Stdout))
}
*/
func TestGetChannelBackgroundConf(t *testing.T) {

	client, err := NewClient(grpc.WithBlock())
	if err != nil {
		t.Fatal(err)
	}

	resp, err := client.GetChannelBackgroundConf(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("GetChannelbackground %+v", resp)
}

func TestAddChannelBackgroundConf(t *testing.T) {

	client, err := NewClient(grpc.WithBlock())
	if err != nil {
		t.Fatal(err)
	}
	info := pb.ChannelBackgroundInfo{
		BackgroundId: uint64(time.Now().Unix()),
		BackgroundUrl: "https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1565870239204&di=9d9ac798c22ae092f470197aaaacbb3c&" +
			"imgtype=0&src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201702%2F05%2F20170205203538_FdaP4.jpeg",
		BackgroundType: pb.BackgroundType(0),
		CreateTime:     time.Now().Unix(),
		BackgroundName: "test1",
		Md5Sum:         "4907beb0aa6c0ec11a94448aed115664",
		StartTime:      time.Now().Unix(),
		EndTime:        time.Now().Unix() + 24*3600,
	}

	resp, err := client.AddChannelBackgroundConf(context.Background(), &info)
	if err != nil {
		t.Fatal(err)
	}

	info = pb.ChannelBackgroundInfo{
		BackgroundId: uint64(time.Now().Unix()),
		BackgroundUrl: "https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1565870239203&di=4344466c5cad7b75d2f7a1fc4a5f9d8d&imgtype" +
			"=0&src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fblog%2F201512%2F01%2F20151201134900_FsA5S.jpeg",
		BackgroundType: pb.BackgroundType(0),
		CreateTime:     time.Now().Unix(),
		BackgroundName: "test2",
		Md5Sum:         "426f29f1a99598953959137854a257c6",
	}

	resp, err = client.AddChannelBackgroundConf(context.Background(), &info)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("AddChannelbackground req %+v resp %+v", &info.BackgroundType, resp)
}

func TestDelChannelBackgroundConf(t *testing.T) {

	client, err := NewClient(grpc.WithBlock())
	if err != nil {
		t.Fatal(err)
	}

	resp, err := client.DelChannelBackgroundConf(context.Background(), 0)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("DelChannelbackground %+v", resp)

}

func TestAddLiveChannelBgConf(t *testing.T) {
	client, err := NewClient(grpc.WithBlock())
	if err != nil {
		t.Fatal(err)
	}

	err = client.AddLiveChannelBgConf(context.Background(), &pb.AddLiveChannelBgConfReq{
		BackgroundInfo: &pb.LiveChannelBackgroundInfo{
			BackgroundId:          10011,
			BackgroundName:        "test4",
			BackgroundType:        uint32(pb.LiveChannelBackgroundInfo_TimeLimit),
			BackgroundVideoUrl:    "https://ga-album-cdnqn.52tt.com/channel/bgtest3.mp4.zip",
			BackgroundVideoMd5Sum: "06e04df0e07dfc812519a2946480c484",
			MicDynamicUrl:         "https://ga-album-cdnqn.52tt.com/channel/frame_lv1_vacation3.zip",
			MicDynamicMd5Sum:      "721e8055b40635b98313ed22e387adac",
			/*BackgroundId:   10000,
			BackgroundType: uint32(pb.LiveChannelBackgroundInfo_TimeLimit),
			BackgroundName: "test",
			BackgroundVideoUrl: "https://ga-album-cdnqn.52tt.com/channel/bg_stage.mp4.zip",
			BackgroundVideoMd5Sum: "133920f5048cb83573bdc6d1342b98cd",
			MicDynamicUrl: "https://ga-album-cdnqn.52tt.com/channel/frame_lv1_zfj3.zip",
			MicDynamicMd5Sum: "adb526eafe803f90e1d7e0c49bdbba63",*/
		},
	})
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("AddLiveChannelBgConf success")
}

func TestGetLiveChannelBgConf(t *testing.T) {
	client, err := NewClient(grpc.WithBlock())
	if err != nil {
		t.Fatal(err)
	}

	resp, err := client.GetLiveChannelBgConf(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("GetLiveChannelBgConf resp:%+v", resp)
}

func TestDelLiveChannelBgConf(t *testing.T) {
	client, err := NewClient(grpc.WithBlock())
	if err != nil {
		t.Fatal(err)
	}

	err = client.DelLiveChannelBgConf(context.Background(), 10025)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("DelLiveChannelBgConf success")
}

func TestClient_GiveChannelLiveBg(t *testing.T) {
	client, err := NewClient(grpc.WithBlock())
	if err != nil {
		t.Fatal(err)
	}

	err = client.GiveChannelLiveBg(context.Background(), 10091604, 2203127, 10019, 300)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("GiveChannelLiveBg success")
}

func TestClient_GetChannelLiveBgList(t *testing.T) {
	client, err := NewClient(grpc.WithBlock())
	if err != nil {
		t.Fatal(err)
	}

	resp, err := client.GetChannelLiveBgList(context.Background(), 0, 10091342)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("GetChannelLiveBgList success %+v", resp)
}

func TestClient_GetChannelCurrLiveBg(t *testing.T) {
	client, err := NewClient(grpc.WithBlock())
	if err != nil {
		t.Fatal(err)
	}

	resp, err := client.GetChannelCurrLiveBg(context.Background(), 0, 0)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("GetChannelLiveBgList success curr:%+v default:%+v", resp.GetCurrBackgroundInfo(), resp.GetDefaultBackgroundInfo())
}
