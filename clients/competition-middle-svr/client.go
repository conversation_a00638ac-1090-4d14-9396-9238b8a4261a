package competition_middle_svr

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/web-server/competition"
	"google.golang.org/grpc"
)

const (
	serviceName = "web-server"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewCompetitionMiddleSvrClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.CompetitionMiddleSvrClient {
	return c.Stub().(pb.CompetitionMiddleSvrClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) IsJoinedGame(ctx context.Context, in *pb.IsJoinedGameReq) (*pb.IsJoinedGameResp, error) {
	resp, err := c.typedStub().IsJoinedGame(ctx, in)
	return resp, protocol.ToServerError(err)
}
