package gold_commission

import (
	"context"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/gold-commission"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetGuildInteractGamePer(ctx context.Context, guildId uint32) (bool, protocol.ServerError)
	GetGuildsUnSettlementSummary(ctx context.Context, req *pb.GetGuildsUnSettlementSummaryReq) (*pb.GetGuildsUnSettlementSummaryRsp, protocol.ServerError)
	GetGuildsUnSettlementSummaryByGuildId(ctx context.Context, goldType pb.GoldType, guildIds []uint32) (*pb.GetGuildsUnSettlementSummaryRsp, protocol.ServerError)
	GetGuildsYuyinAnchorStat(ctx context.Context, req *pb.GetGuildsYuyinAnchorStatReq) (*pb.GetGuildsYuyinAnchorStatResp, protocol.ServerError)
	GetGuildsAmuseChannelStat(ctx context.Context, req *pb.GetGuildsAmuseChannelStatReq) (*pb.GetGuildsAmuseChannelStatResp, protocol.ServerError)
	GetYuyinIncomeDetail(ctx context.Context, uid, guildId uint32) (*pb.GetYuyinIncomeDetailRsp, protocol.ServerError)
	GetIncomeTrendList(ctx context.Context, req *pb.GetIncomeTrendListReq) (*pb.GetIncomeTrendListRsp, protocol.ServerError)
	GetConsumeRank(ctx context.Context, req *pb.GetConsumeRankReq) (*pb.GetConsumeRankRsp, protocol.ServerError)
	GetMonthIncome(ctx context.Context, uid, guildId uint32, GoldType pb.GoldType) (*pb.GetMonthIncomeRsp, protocol.ServerError)
	GetGuildYuyinExtraIncome(ctx context.Context, req *pb.GetGuildYuyinExtraIncomeReq) (*pb.GetGuildYuyinExtraIncomeRsp, protocol.ServerError)
	GetGuildYuyinTaskList(ctx context.Context, uid, guildId uint32) (*pb.GetGuildYuyinTaskListRsp, protocol.ServerError)
	GetYuyinGuildDayIncomeList(ctx context.Context, req *pb.GetGuildDayIncomeListReq) (*pb.GetYuyinGuildDayIncomeListResp, protocol.ServerError)
	GetAmuseGuildDayIncomeList(ctx context.Context, req *pb.GetGuildDayIncomeListReq) (*pb.GetAmuseGuildDayIncomeListResp, protocol.ServerError)
	GetYuyinGuildMonthIncomeList(ctx context.Context, req *pb.GetGuildMonthIncomeListReq) (*pb.GetGuildMonthIncomeListResp, protocol.ServerError)
	GetAmuseGuildMonthIncomeList(ctx context.Context, req *pb.GetGuildMonthIncomeListReq) (*pb.GetGuildMonthIncomeListResp, protocol.ServerError)
	GetGuildMonthMemberList(ctx context.Context, req *pb.GetGuildMonthMemberListReq) (*pb.GetGuildMonthMemberListResp, protocol.ServerError)
	SearchYuyinGuildDetail(ctx context.Context, req *pb.SearchYuyinGuildDetailReq) (*pb.SearchYuyinGuildDetailResp, protocol.ServerError)
	SearchAmuseGuildDetail(ctx context.Context, req *pb.SearchAmuseGuildDetailReq) (*pb.SearchAmuseGuildDetailResp, protocol.ServerError)
	GetGuildDayQoq(ctx context.Context, req *pb.GetGuildDayQoqReq) (*pb.GetGuildDayQoqResp, protocol.ServerError)
	GetAmuseChannelDetail(ctx context.Context, req *pb.GetAmuseChannelDetailReq) (*pb.GetAmuseChannelDetailRsp, protocol.ServerError)
	GetAmuseIncomeDetail(ctx context.Context, req *pb.GetAmuseIncomeDetailReq) (*pb.GetAmuseIncomeDetailRsp, protocol.ServerError)
	GetAmuseGuildRoomIncomeList(ctx context.Context, req *pb.GetAmuseGuildRoomIncomeListReq) (*pb.GetAmuseGuildRoomIncomeListRsp, protocol.ServerError)
	AmuseGuildChannelIncomeList(ctx context.Context, req *pb.AmuseGuildChannelIncomeListReq) (*pb.AmuseGuildChannelIncomeListResp, protocol.ServerError)
	GetAmuseRoomDayQoqInfo(ctx context.Context, req *pb.GetAmuseRoomDayQoqInfoReq) (*pb.GetAmuseRoomDayQoqInfoRsp, protocol.ServerError)
	GetInteractGameExtraIncome(ctx context.Context, req *pb.GetInteractGameExtraIncomeReq) (*pb.GetInteractGameExtraIncomeResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
