package muse_social_community

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/muse-social-community"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	//ListBrandChannelByBrandId(ctx context.Context, req *pb.ListBrandChannelByBrandIdReq) (*pb.ListBrandChannelByBrandIdResp, protocol.ServerError)
	BatchBrandChannelByChannelIds(ctx context.Context, req *pb.BatchBrandChannelByChannelIdsReq) (*pb.BatchBrandChannelByChannelIdsResp, protocol.ServerError)
	ListBrandMembersByUid(ctx context.Context, req *pb.ListBrandMembersByUidReq) (*pb.ListBrandMembersByUidResp, protocol.ServerError)
	BatchBrandsByIds(ctx context.Context, req *pb.BatchBrandsByIdsReq) (*pb.BatchBrandsByIdsResp, protocol.ServerError)
	BatchCategoriesByIds(ctx context.Context, categoryIds []string) (*pb.BatchCategoriesByIdsResp, protocol.ServerError)
	SearchBrandTypes(ctx context.Context, req *pb.SearchBrandTypesReq) (*pb.SearchBrandTypesResp, protocol.ServerError)
	SearchBrands(ctx context.Context, req *pb.SearchBrandsReq) (*pb.SearchBrandsResp, protocol.ServerError)
	BatchBrandTypesByIds(ctx context.Context, brandTypeIds []string) (*pb.BatchBrandTypesByIdsResp, protocol.ServerError)
	BatchBrandMembersByUids(ctx context.Context, req *pb.BatchBrandMembersByUidsReq) (*pb.BatchBrandMembersByUidsResp, protocol.ServerError)
	BatchBrandChannelsByBrandIds(ctx context.Context, req *pb.BatchBrandChannelsByBrandIdsReq) (*pb.BatchBrandChannelsByBrandIdsResp, protocol.ServerError)
	JoinSocialCommunityFans(ctx context.Context, req *pb.JoinSocialCommunityFansReq) (*pb.JoinSocialCommunityFansResp, protocol.ServerError)
	GetSocialCommunityFansCount(ctx context.Context, req *pb.GetSocialCommunityFansCountReq) (*pb.GetSocialCommunityFansCountResp, protocol.ServerError)
	BatchBrandBackgroundExtras(ctx context.Context, req *pb.BatchBrandBackgroundExtrasReq) (*pb.BatchBrandBackgroundExtrasResp, protocol.ServerError)
	GetSocialCommunityCaption(ctx context.Context, req *pb.GetSocialCommunityCaptionReq) (*pb.GetSocialCommunityCaptionResp, protocol.ServerError)
	BatchCategoriesByBrandIds(ctx context.Context, req *pb.BatchCategoriesByBrandIdsReq) (*pb.BatchCategoriesByBrandIdsResp, protocol.ServerError)
	GetSocialCommunityDetailInfo(ctx context.Context, req *pb.GetSocialCommunityDetailInfoReq) (*pb.GetSocialCommunityDetailInfoResp, protocol.ServerError)
	BatchCategoryTypesByIds(ctx context.Context, req *pb.BatchCategoryTypesByIdsReq) (*pb.BatchCategoryTypesByIdsResp, protocol.ServerError)
	ListMuseSocialCommunityNavBars(ctx context.Context, req *pb.ListMuseSocialCommunityNavBarsReq) (*pb.ListMuseSocialCommunityNavBarsResp, protocol.ServerError)
	ListMuseSocialCommunityNavSecondaryBars(ctx context.Context, req *pb.ListMuseSocialCommunityNavSecondaryBarsReq) (*pb.ListMuseSocialCommunityNavSecondaryBarsResp, protocol.ServerError)
	GetSocialCommunityProfilePages(ctx context.Context, req *pb.GetSocialCommunityProfilePagesReq) (*pb.GetSocialCommunityProfilePagesResp, protocol.ServerError)
	SearchBrandMembers(ctx context.Context, req *pb.SearchBrandMembersReq) (*pb.SearchBrandMembersResp, protocol.ServerError)
	GetSocialCommunityAllMembersCount(ctx context.Context,
		req *pb.GetSocialCommunityAllMembersCountReq) (*pb.GetSocialCommunityAllMembersCountResp, protocol.ServerError)
	GetSocialCommunityKernelCount(ctx context.Context,
		req *pb.GetSocialCommunityKernelCountReq) (*pb.GetSocialCommunityKernelCountResp, protocol.ServerError)
	UpsertBrandMembers(ctx context.Context,
		req *pb.UpsertBrandMembersReq) (*pb.UpsertBrandMembersResp, protocol.ServerError)
	SearchMemberWorkerOrders(ctx context.Context,
		req *pb.SearchMemberWorkerOrdersReq) (*pb.SearchMemberWorkerOrdersResp, protocol.ServerError)
	UpsertMemberWorkerOrders(ctx context.Context,
		req *pb.UpsertMemberWorkerOrdersReq) (*pb.UpsertMemberWorkerOrdersResp, protocol.ServerError)
	BatchGetOpenCategoryTypes(ctx context.Context,
		req *pb.BatchGetOpenCategoryTypesReq) (*pb.BatchGetOpenCategoryTypesResp, protocol.ServerError)
	BatchGetCategoriesByCategoryTypeId(ctx context.Context,
		req *pb.BatchGetCategoriesByCategoryTypeIdReq) (*pb.BatchGetCategoriesByCategoryTypeIdResp, protocol.ServerError)
	ApplyCreateSocialCommunity(ctx context.Context,
		req *pb.ApplyCreateSocialCommunityReq) (*pb.ApplyCreateSocialCommunityResp, protocol.ServerError)
	GetUserRecentSocialCommunityOrder(ctx context.Context,
		req *pb.GetUserRecentSocialCommunityOrderReq) (*pb.GetUserRecentSocialCommunityOrderResp,
		protocol.ServerError)
	ListUserKernelMembers(ctx context.Context,
		req *pb.ListUserKernelMembersReq) (*pb.ListUserKernelMembersResp, protocol.ServerError)
	ListUserFansMembers(ctx context.Context,
		req *pb.ListUserFansMembersReq) (*pb.ListUserFansMembersResp, protocol.ServerError)

	GetSocialCommunityFloat(ctx context.Context, in *pb.GetSocialCommunityFloatRequest) (*pb.GetSocialCommunityFloatResponse, protocol.ServerError)

	GetSocialCommunityBase(ctx context.Context, in *pb.GetSocialCommunityBaseReq) (*pb.GetSocialCommunityBaseResp, protocol.ServerError)

	GetGroupMemberLimit(ctx context.Context,
		in *pb.GetGroupMemberLimitReq) (*pb.GetGroupMemberLimitResp, protocol.ServerError)

	GetSocialCommunityKernelMembers(ctx context.Context,
		socialCommunityIds []string) (*pb.GetSocialCommunityKernelMembersResp, protocol.ServerError)

	MuseSocialPreviewGroupMessage(ctx context.Context, groupId, uid uint32) (*pb.MuseSocialPreviewGroupMessageResponse, protocol.ServerError)

	GetSocialCommunityMemberList(ctx context.Context, SocialCommunityId, offsetId string, count uint32) (*pb.GetSocialCommunityMemberListResp, protocol.ServerError)

	SetMuseSocialAnnounceInterest(ctx context.Context, announceId string, uid, interestType uint32) (*pb.SetMuseSocialAnnounceInterestResponse, protocol.ServerError)

	RemoveMuseSocialAnnounce(ctx context.Context, announceId string) (*pb.RemoveMuseSocialAnnounceResponse, protocol.ServerError)

	ListMuseSocialAnnounceInterestUsers(ctx context.Context, announceId, offsetId string, limit uint32) (*pb.ListMuseSocialAnnounceInterestUsersResponse, protocol.ServerError)
	ListMuseSocialAnnounces(ctx context.Context, request *pb.ListMuseSocialAnnouncesRequest) (*pb.ListMuseSocialAnnouncesResponse, protocol.ServerError)
	BatchMuseSocialAnnounceByAnnounceIds(ctx context.Context, announceId []string) (*pb.BatchMuseSocialAnnounceByAnnounceIdsResponse, protocol.ServerError)

	UpsertMuseSocialAnnounce(ctx context.Context, request *pb.UpsertMuseSocialAnnounceRequest) (*pb.UpsertMuseSocialAnnounceResponse, protocol.ServerError)

	ListAnnounceDestinations(ctx context.Context, request *pb.ListAnnounceDestinationsRequest) (*pb.ListAnnounceDestinationsResponse, protocol.ServerError)
	GetSocialCommunityAnnounceNewsCountMap(ctx context.Context, request *pb.GetSocialCommunityAnnounceNewsCountMapRequest) (*pb.GetSocialCommunityAnnounceNewsCountMapResponse, protocol.ServerError)
	ValidateUserHasCreateAnnouncePermissions(ctx context.Context, socialCommunityId string) (*pb.ValidateUserHasCreateAnnouncePermissionsResponse, protocol.ServerError)

	UpsertBrands(ctx context.Context, brands []*pb.Brand) (*pb.UpsertBrandsResp, protocol.ServerError)
	UpsertApplyJoinCommunityWorkOrder(ctx context.Context, uid, status uint32, socialCommunityId,
		id, reason string) (*pb.UpsertApplyJoinCommunityWorkOrderResponse, protocol.ServerError)

	SearchApplyJoinCommunityWorkOrder(ctx context.Context, uid, limit uint32, status []uint32, socialCommunityId,
		id, offsetId string) (*pb.SearchApplyJoinCommunityWorkOrderResponse, protocol.ServerError)
	ListApplyJoinCommunityWorkOrders(ctx context.Context,
		req *pb.ListApplyJoinCommunityWorkOrdersRequest) (*pb.ListApplyJoinCommunityWorkOrdersResponse, protocol.ServerError)

	SubmitApplicationToJoinCommunity(ctx context.Context,
		req *pb.SubmitApplicationToJoinCommunityRequest) (*pb.SubmitApplicationToJoinCommunityResponse, protocol.ServerError)

	JoinSocialCommunityKernel(ctx context.Context,
		req *pb.JoinSocialCommunityKernelReq) (*pb.JoinSocialCommunityKernelResp, protocol.ServerError)

	GetSocialCommunityContentStreamNewsCount(ctx context.Context,
		req *pb.GetSocialCommunityContentStreamNewsCountRequest) (*pb.GetSocialCommunityContentStreamNewsCountResponse, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
