package ugc_friendship_logic

import(
	context "context"
	"golang.52tt.com/pkg/client"
	ugc "golang.52tt.com/protocol/app/ugc"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	FollowBatchUser(ctx context.Context, in *ugc.FollowBatchUserReq) (*ugc.FollowBatchUserResp, error)
	FollowUser(ctx context.Context, in *ugc.FriendshipOperationReq) (*ugc.FriendshipOperationResp, error)
	GetShowUserFollow(ctx context.Context, in *ugc.GetShowUserFollowReq) (*ugc.GetShowUserFollowResp, error)
	GetUserFriendships(ctx context.Context, in *ugc.GetUserFriendshipsReq) (*ugc.GetUserFriendshipsResp, error)
	QuickCheckFriendship(ctx context.Context, in *ugc.QuickCheckFriendshipReq) (*ugc.QuickCheckFriendshipResp, error)
	RemoveFollower(ctx context.Context, in *ugc.FriendshipOperationReq) (*ugc.FriendshipOperationResp, error)
	SetShowUserFollow(ctx context.Context, in *ugc.SetShowUserFollowReq) (*ugc.SetShowUserFollowResp, error)
	UnfollowUser(ctx context.Context, in *ugc.FriendshipOperationReq) (*ugc.FriendshipOperationResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}

