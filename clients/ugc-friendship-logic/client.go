package ugc_friendship_logic

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	appPb "golang.52tt.com/protocol/app/ugc"
	logicPb "golang.52tt.com/protocol/services/logicsvr-go/ugc-friendship-logic"
	"google.golang.org/grpc"
)

const (
	serviceName = "ugc-friendship-logic"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return logicPb.NewUgcFriendshipLogicClient(cc)
			},
			dopts...,
		),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() logicPb.UgcFriendshipLogicClient {
	return c.Stub().(logicPb.UgcFriendshipLogicClient)
}

func (c *Client) GetUserFriendships(ctx context.Context, in *appPb.GetUserFriendshipsReq) (*appPb.GetUserFriendshipsResp,error) {
	resp,err := c.typedStub().GetUserFriendships(ctx,in)
	return resp,err
}

func (c *Client) FollowUser(ctx context.Context, in *appPb.FriendshipOperationReq) (*appPb.FriendshipOperationResp,error) {
	resp,err := c.typedStub().FollowUser(ctx,in)
	return resp,err
}

func (c *Client) UnfollowUser(ctx context.Context, in *appPb.FriendshipOperationReq) (*appPb.FriendshipOperationResp,error) {
	resp,err := c.typedStub().UnfollowUser(ctx,in)
	return resp,err
}

func (c *Client) RemoveFollower(ctx context.Context, in *appPb.FriendshipOperationReq) (*appPb.FriendshipOperationResp,error) {
	resp,err := c.typedStub().RemoveFollower(ctx,in)
	return resp,err
}

func (c *Client) QuickCheckFriendship(ctx context.Context, in *appPb.QuickCheckFriendshipReq) (*appPb.QuickCheckFriendshipResp,error) {
	resp,err := c.typedStub().QuickCheckFriendship(ctx,in)
	return resp,err
}

func (c *Client) FollowBatchUser(ctx context.Context, in *appPb.FollowBatchUserReq) (*appPb.FollowBatchUserResp,error) {
	resp,err := c.typedStub().FollowBatchUser(ctx,in)
	return resp,err
}

func (c *Client) GetShowUserFollow(ctx context.Context, in *appPb.GetShowUserFollowReq) (*appPb.GetShowUserFollowResp,error) {
	resp,err := c.typedStub().GetShowUserFollow(ctx,in)
	return resp,err
}

func (c *Client) SetShowUserFollow(ctx context.Context, in *appPb.SetShowUserFollowReq) (*appPb.SetShowUserFollowResp,error) {
	resp,err := c.typedStub().SetShowUserFollow(ctx,in)
	return resp,err
}
