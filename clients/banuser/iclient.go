// Code generated by quicksilver-cli. DO NOT EDIT.
package banuser

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/banusersvr"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	BatchGetDeviceBannedStatus(ctx context.Context, deviceList []string) (map[string]bool,[]string,protocol.ServerError)
	BatchGetUserBannedStatus(ctx context.Context, uids []uint32) (map[uint32]bool,[]uint32,protocol.ServerError)
	BatchUpdateBanedStatus(ctx context.Context, list []*pb.UpdateBanedStatus) protocol.ServerError
	GetBannedAppealRecord(ctx context.Context, uid uint32, req *pb.GetBannedAppealRecordReq) (*pb.GetBannedAppealRecordResp,protocol.ServerError)
	GetBannedHistory(ctx context.Context, uid uint32, req *pb.GetBannedHistoryReq) (*pb.GetBannedHistoryResp,protocol.ServerError)
	GetBannedOperator(ctx context.Context, uin uint32, operatorName string, limit uint32) (*pb.GetBannedOperatorResp,protocol.ServerError)
	GetBannedStatus(ctx context.Context, uin uint32, UID uint32, deviceID string, clientIP string, phone string) (*pb.BannedStatus,protocol.ServerError)
	GetUserBannedStatuses(ctx context.Context, uin uint32, uidList []uint32) (map[uint32]*pb.BannedStatus,protocol.ServerError)
	IsUserBannedForever(ctx context.Context, uid uint32) (bool,protocol.ServerError)
	SetBannedAppealRecord(ctx context.Context, uid uint32, req *pb.SetBannedAppealRecordReq) protocol.ServerError
	UpdateBanedStatus(ctx context.Context, uin uint32, opType uint32, UID uint32, deviceID string, clientIP string, at uint32, recoveryAt uint32, reason string, operatorID string, proofPic string, extInfo string, reasonDetail string) protocol.ServerError
	UpdateBannedAppealRecord(ctx context.Context, uid uint32, req *pb.UpdateBannedAppealRecordReq) (bool,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
