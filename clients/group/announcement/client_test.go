package announcement

import (
	"context"
	"testing"

	pb "golang.52tt.com/protocol/services/group/groupannouncement"
)

func TestClient_GetGroupAnnouncementList(t *testing.T) {

	client, err := NewClient()
	if err != nil {
		t.<PERSON><PERSON>(err)
	}

	resp, err := client.GetGroupAnnouncementList(context.Background(), &pb.GetGroupAnnouncementListReq{})
	if err != nil {
		t.<PERSON>al(err)
	}
	t.Log(resp)

}
