// Code generated by quicksilver-cli. DO NOT EDIT.
package channel_personalization

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/channelpersonalization"
)

type IClient interface {
	client.BaseClient
	ActivateUserDecoration(ctx context.Context, uid, decorationType uint32, decID string) (*pb.ActivateUserDecorationResp,error)
	AddUserDecorationConfig(ctx context.Context, req *pb.AddUserDecorationConfigReq) (*pb.AddUserDecorationConfigResp,error)
	BatchGrantDecorationToUser(ctx context.Context, req *pb.BatchGrantDecorationToUserReq) (*pb.BatchGrantDecorationToUserResp,error)
	ChangeDecorationCustomText(ctx context.Context, req *pb.ChangeDecorationCustomTextReq) (*pb.ChangeDecorationCustomTextResp,error)
	DelUserDecorationConfig(ctx context.Context, req *pb.DelUserDecorationConfigReq) (*pb.DelUserDecorationConfigResp,error)
	GetChannelEnterSpecialEffectConfig(ctx context.Context) (*pb.ChannelEnterSpecialEffectConfig,error)
	GetChannelEnterSpecialEffectConfigById(ctx context.Context, req *pb.GetChannelEnterSpecialEffectConfigByIdReq) (*pb.GetChannelEnterSpecialEffectConfigByIdResp,error)
	GetUserDecorations(ctx context.Context, uid, richLevel, decorationType uint32, onlyEffective bool) (*pb.GetUserDecorationsResp,error)
	GetUserDecorationsWithNobility(ctx context.Context, uid, richLevel, decorationType, nobilityLevel uint32, onlyEffective bool) (*pb.GetUserDecorationsResp,error)
	GrantDecoration(ctx context.Context, ud *UserDecoration, addTTLForExisting bool) error
	GrantDecorationV2(ctx context.Context, ud *UserDecoration, addTTLForExisting bool) error
	GrantDecorationV2WithOrderInfo(ctx context.Context, ud *UserDecoration, addTTLForExisting bool, outsideTime int64, sourceType uint32) error
	SetUserDecorationSpecialLevel(ctx context.Context, req *pb.SetUserDecorationSpecialLevelReq) (*pb.SetUserDecorationSpecialLevelResp,error)
	TypedStub() pb.ChannelPersonalizationClient
	UpdateUserDecorationConfig(ctx context.Context, req *pb.UpdateUserDecorationConfigReq) (*pb.UpdateUserDecorationConfigResp,error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
