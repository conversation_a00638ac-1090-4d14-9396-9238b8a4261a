package hobby_channel_logic

import (
	"context"
	app_pb "golang.52tt.com/protocol/app/hobby-channel"
	pb "golang.52tt.com/protocol/services/logicsvr-go/hobby-channel-logic"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	"google.golang.org/grpc"
)

const (
	serviceName = "hobby-channel-logic"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewHobbyChannelLogicClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.HobbyChannelLogicClient {
	return c.<PERSON>ub().(pb.HobbyChannelLogicClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) PublishHobbyChannel(ctx context.Context, req *app_pb.PublishHobbyChannelReq) (*app_pb.PublishHobbyChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().PublishHobbyChannel(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SwitchHobbyChannelSubject(ctx context.Context, req *app_pb.SwitchHobbyChannelSubjectReq) (*app_pb.SwitchHobbyChannelSubjectResp, protocol.ServerError) {
	resp, err := c.typedStub().SwitchHobbyChannelSubject(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CancelHobbyChannelPublish(ctx context.Context, req *app_pb.CancelHobbyChannelPublishReq) (*app_pb.CancelHobbyChannelPublishResp, protocol.ServerError) {
	resp, err := c.typedStub().CancelHobbyChannelPublish(ctx, req)
	return resp, protocol.ToServerError(err)
}
