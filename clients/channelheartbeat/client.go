package channelheartbeat

import (
	"context"
	"strconv"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc" //
	pb "golang.52tt.com/protocol/services/channelheartbeat"
)

const (
	serviceName = "channelheartbeat"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewChannelHeartbeatClient(cc)
		}, dopts...),
	}
}

func (c *Client) typedStub() pb.ChannelHeartbeatClient { return c.Stub().(pb.ChannelHeartbeatClient) }

func (c *Client) UpdateUserHeartbeat(ctx context.Context, cid, uid uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.FormatUint(uint64(uid), 10)))
	_, err := c.typedStub().UpdateUserHeartbeat(ctx, &pb.UpdateUserHeartbeatReq{Info: &pb.UserHeartbeatInfo{
		Uid:       uid,
		ChannelId: cid,
	}})
	return protocol.ToServerError(err)
}

func (c *Client) CheckUserHeartRecordExist(ctx context.Context, uidList []uint32) ([]uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	req := &pb.CheckUserHeartRecordExistReq{UidList: uidList}

	r, err := c.typedStub().CheckUserHeartRecordExist(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r.ExistUidList, nil
}
