package channelheartbeat

import (
	"context"
	"fmt"
	"golang.52tt.com/clients/channelguild"
	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
	"google.golang.org/grpc"
	"testing"
)

func TestClient_CreateChannel(t *testing.T) {

	channelGuildCli := channelguild.NewClient(grpc.WithBlock())
	resp , aErr := channelGuildCli.CreateGuildPubChannel(context.Background(), 100255, 2190041)
	if aErr != nil {
		fmt.Println(resp,aErr)
		t.Fatal(aErr)
	}
}

//func TestClient_GetUserChannelRoleList(t *testing.T) {
//	client := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("channel.52tt.local"))
//	channelInfo, err := client.GetUserChannelRoleList(context.Background(), 2208646, 2208646, 1)
//	if err != nil {
//		t.Fatal(err)
//	}
//	fmt.Println(utils.ToJson(channelInfo))
//}
////
//func TestClient_BatchGetChannelSimpleInfo(t *testing.T) {
//	var idList = []uint32{10000251, 10000364, 10000531, 10000548, 10000784, 10001352, 10001573, 10002620, 10002622, 10003292, 10004929, 10005080, 10007175, 10007320, 10008209, 10008210, 10008493, 10009602, 10009608, 10009739}
//
//	client := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("channel.52tt.local"))
//	channelInfo, err := client.BatchGetChannelSimpleInfo(context.Background(), 2193341, idList)
//	if err != nil {
//		t.Fatal(err)
//	}
//	fmt.Println(utils.ToJson(channelInfo))
//
//}
//
//func TestClient_CheckTmpChannelIsAlloced(t *testing.T) {
//	client := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("channel.52tt.local"))
//	resp, err := client.CheckTmpChannelIsAlloced(context.Background(),10087903, 6)
//	if err != nil {
//		t.Fatal(err)
//	}
//	fmt.Println(utils.ToJson(resp))
//}
//
//func TestClient_AllocTempChannel(t *testing.T) {
//	client := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("channel.52tt.local"))
//	tempChannel, err := client.AllocTempChannel(context.Background(),6)
//	if err != nil {
//		t.Fatal(err)
//	}
//	fmt.Println(utils.ToJson(tempChannel))
//}
//
//func TestClient_ModifyChannel(t *testing.T) {
//	client := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("channel.52tt.local"))
//
//	in := &pb.ModifyChannelReq{
//		ChannelId:            nil,
//		OpUid:                nil,
//		Name:                 nil,
//		PwdFlag:              nil,
//		Passwd:               nil,
//		IconMd5:              nil,
//		Desc:                 nil,
//		SwitchFlagBitmap:     nil,
//		TopicDetail:          nil,
//		WelcomeMsg:           nil,
//	}
//
//
//
//	cid := uint32(10091435)
//	uid := uint32(2208646)
//
//	hasPwd := uint32(0)
//	pwd := ""
//
//	in.ChannelId = &cid
//	in.OpUid = &uid
//	in.PwdFlag = &hasPwd
//	in.Passwd = &pwd
//
//	resp, err := client.ModifyChannel(context.Background(),0, in)
//	if err != nil {
//		t.Fatal(err)
//	}
//	fmt.Println(resp)
//}

//func TestClient_OfficialChannel(t *testing.T) {
//	client := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("channel.52tt.local"))
//	resp, err := client.CreateChannel(context.Background(), 0, 0, 9, 0, "语音直播官方频道", "")
//
//	fmt.Println(utils.ToJson(resp), err)
//}
