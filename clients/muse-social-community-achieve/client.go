package muse_social_community_achieve

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/muse-social-community-achieve"

	"google.golang.org/grpc"
)

const (
	serviceName = "muse-social-community-achieve"
)

type Client struct {
	client.BaseClient
}

func (c *Client) typedStub() pb.MuseSocialCommunityAchieveClient {
	return c.Stub().(pb.MuseSocialCommunityAchieveClient)
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewMuseSocialCommunityAchieveClient(cc)
		}, dopts...),
	}, nil
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) CheckIn(ctx context.Context,
	socialCommunityId string) (*pb.CheckInResponse, protocol.ServerError) {
	resp, err := c.typedStub().CheckIn(ctx, &pb.CheckInRequest{SocialCommunityId: socialCommunityId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetCheckInSimple(ctx context.Context,
	socialCommunityId string) (*pb.GetCheckInSimpleResponse, protocol.ServerError) {
	resp, err := c.typedStub().GetCheckInSimple(ctx, &pb.GetCheckInSimpleRequest{SocialCommunityId: socialCommunityId})
	return resp, protocol.ToServerError(err)
}

//func (c *Client) ListBrandChannelByBrandId(ctx context.Context, req *pb.ListBrandChannelByBrandIdReq) (*pb.ListBrandChannelByBrandIdResp, protocol.ServerError) {
//	resp, err := c.typedStub().ListBrandChannelByBrandId(ctx, req)
//	return resp, protocol.ToServerError(err)
//}

func (c *Client) GetLevelDetail(ctx context.Context, req *pb.GetLevelDetailRequest) (*pb.GetLevelDetailResponse, protocol.ServerError) {
	resp, err := c.typedStub().GetLevelDetail(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSocialCommunityUpdateLevelTip(ctx context.Context, req *pb.GetSocialCommunityUpdateLevelTipRequest) (*pb.GetSocialCommunityUpdateLevelTipResponse, protocol.ServerError) {
	resp, err := c.typedStub().GetSocialCommunityUpdateLevelTip(ctx, req)
	return resp, protocol.ToServerError(err)
}
