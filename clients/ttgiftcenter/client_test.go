package ttgiftcenter

import (
	"context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func TestClient_GetProduct(t *testing.T) {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
	r, err := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("ttgiftcenter.52tt.local")).GetProduct(context.Background(), 233)
	if err != nil {
		t.<PERSON>rf("Failed to GetProduct: %v", err)
	}
	t.Log(r)
}
