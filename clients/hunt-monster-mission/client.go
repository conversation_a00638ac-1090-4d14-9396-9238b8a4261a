package huntmonstermission

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/huntmonstermission"
	"google.golang.org/grpc"
)

const (
	serviceName = "hunt-monster-mission"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	 return &Client{
	 	BaseClient: client.NewInsecureGRPCClient(
	 		serviceName,
	 		func(cc *grpc.ClientConn) interface{} {
	 			return pb.NewHuntMonsterMissionClient(cc)
			}, dopts...,
		),
	 }, nil
}

func (c *Client) typedStub() pb.HuntMonsterMissionClient { return c.Stub().(pb.HuntMonsterMissionClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetUserHuntMonsterMission(ctx context.Context, uid uint32) (*pb.GetUserHuntMonsterMissionResp, protocol.ServerError) {
	 resp, err := c.typedStub().GetUserHuntMonsterMission(ctx, &pb.GetUserHuntMonsterMissionReq{
		 Uid:                  uid,
	 })

	 return resp, protocol.ToServerError(err)
}

func (c *Client) CheckChannelIsRec(ctx context.Context, cid uint32) (*pb.CheckChannelIsRecResp, protocol.ServerError) {
	 resp, err := c.typedStub().CheckChannelIsRec(ctx, &pb.CheckChannelIsRecReq{
		 ChannelId:            cid,
	 })

	 return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserHuntMissionInfo(ctx context.Context, uid, cid, missionType uint32) (*pb.GetUserHuntMissionInfoResp, protocol.ServerError) {
	 resp, err := c.typedStub().GetUserHuntMissionInfo(ctx, &pb.GetUserHuntMissionInfoReq{
		 Uid:                  uid,
		 MissionType:          missionType,
		 ChannelId:            cid,
	 })
	 return resp, protocol.ToServerError(err)
}





