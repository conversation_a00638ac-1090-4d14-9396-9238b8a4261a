package presence_v2

import (
	"context"
	"fmt"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/presencesvr"
)

var (
	proxyIp   = uint32(3825496074)
	proxyPort = uint32(8080)
	proxyBoot = uint32(1668362400)
	userId    = uint32(1234)
	clientId  = uint32(5678)

	k11 = &pb.PresKey{
		UserId:    userId,
		ProxyIp:   proxyIp,
		ProxyPort: proxyPort,
		ClientId:  clientId,
	}
	v11 = &pb.PresVal{
		DeviceId: []byte{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'},
		ClientIp: proxyIp,
		OnlineTs: uint32(time.Now().Unix()),
		Terminal: 1,
	}
	k12 = &pb.PresKey{
		UserId:    userId,
		ProxyIp:   proxyIp,
		ProxyPort: proxyPort,
		ClientId:  clientId + 1,
	}
	v12 = &pb.PresVal{
		DeviceId: []byte{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'},
		ClientIp: proxyIp,
		OnlineTs: uint32(time.Now().Unix() + 2),
		Terminal: 1,
	}

	k21 = &pb.PresKey{
		UserId:    userId + 1,
		ProxyIp:   proxyIp,
		ProxyPort: proxyPort,
		ClientId:  clientId,
	}
	v21 = &pb.PresVal{
		DeviceId: []byte{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'},
		ClientIp: proxyIp,
		OnlineTs: uint32(time.Now().Unix()),
		Terminal: 1,
	}
	k22 = &pb.PresKey{
		UserId:    userId + 1,
		ProxyIp:   proxyIp,
		ProxyPort: proxyPort,
		ClientId:  clientId + 1,
	}
	v22 = &pb.PresVal{
		DeviceId: []byte{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'},
		ClientIp: proxyIp,
		OnlineTs: uint32(time.Now().Unix() + 2),
		Terminal: 1,
	}
)

func init() {
	log.SetLevel(log.InfoLevel)
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stderr))

	ts, err := time.Parse("2006 01-02 15:04", "2022 11-14 18:00")
	if err != nil {
		fmt.Println(err)
	} else {
		proxyBoot = uint32(ts.AddDate(0, 0, -1).Unix())
	}
}

func TestClient_UpdatePresence(t *testing.T) {
	Convey("TestClient_UpdatePresence", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		client.UpdatePresence(context.Background(), userId, &pb.Proxy{
			ProxyIp:   proxyIp,
			ProxyPort: proxyPort,
			BootTime:  proxyBoot,
		}, []*pb.Pres{
			{
				ProxyIp:      proxyIp,
				ProxyPort:    proxyPort,
				Uid:          userId,
				DeviceId:     []byte{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'},
				Status:       uint32(pb.PRES_STATUS_PRES_ONLINE),
				ClientId:     clientId,
				ClientIp:     proxyIp,
				OnlineTime:   uint32(time.Now().Unix()),
				TerminalType: uint32(pb.PRES_TYPE_PRES_TYPE_IOS),
			},
		})
		resp, err := client.GetPresence(context.Background(), userId)
		So(err, ShouldBeNil)
		for i := range resp {
			t.Log(resp[i])
		}
	})
}

func TestClient_UpdatePresences(t *testing.T) {
	Convey("TestClient_UpdatePresences", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		client.UpdatePresences(context.Background(), &pb.Proxy{
			ProxyIp:   proxyIp,
			ProxyPort: proxyPort,
			BootTime:  proxyBoot,
		}, []*pb.Pres{
			{
				ProxyIp:      proxyIp,
				ProxyPort:    proxyPort,
				Uid:          userId,
				DeviceId:     []byte{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'},
				Status:       uint32(pb.PRES_STATUS_PRES_ONLINE),
				ClientId:     clientId,
				ClientIp:     proxyIp,
				OnlineTime:   uint32(time.Now().Unix()),
				TerminalType: uint32(pb.PRES_TYPE_PRES_TYPE_IOS),
			},
			{
				ProxyIp:      proxyIp,
				ProxyPort:    proxyPort,
				Uid:          userId + 1,
				DeviceId:     []byte{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'},
				Status:       uint32(pb.PRES_STATUS_PRES_ONLINE),
				ClientId:     clientId,
				ClientIp:     proxyIp,
				OnlineTime:   uint32(time.Now().Unix()),
				TerminalType: uint32(pb.PRES_TYPE_PRES_TYPE_IOS),
			},
			{
				ProxyIp:      proxyIp,
				ProxyPort:    proxyPort,
				Uid:          userId + 2,
				DeviceId:     []byte{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'},
				Status:       uint32(pb.PRES_STATUS_PRES_ONLINE),
				ClientId:     clientId,
				ClientIp:     proxyIp,
				OnlineTime:   uint32(time.Now().Unix()),
				TerminalType: uint32(pb.PRES_TYPE_PRES_TYPE_IOS),
			},
		})
		resp, err := client.GetPresences(context.Background(), []uint32{userId, userId + 1, userId + 2, userId + 3})
		So(err, ShouldBeNil)
		for i := range resp {
			t.Log(resp[i])
		}
	})
}

func TestClient_UpdatePresence2(t *testing.T) {
	Convey("TestClient_UpdatePresence2", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		client.UpdatePresence(context.Background(), userId, &pb.Proxy{
			ProxyIp:   proxyIp,
			ProxyPort: proxyPort,
			BootTime:  proxyBoot,
		}, []*pb.Pres{
			{
				ProxyIp:      proxyIp,
				ProxyPort:    proxyPort,
				Uid:          userId,
				DeviceId:     []byte{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'},
				Status:       uint32(pb.PRES_STATUS_PRES_OFFLINE),
				ClientId:     clientId,
				ClientIp:     proxyIp,
				OnlineTime:   uint32(time.Now().Unix()),
				TerminalType: uint32(pb.PRES_TYPE_PRES_TYPE_IOS),
			},
		})
		resp, err := client.GetPresence(context.Background(), userId)
		So(err, ShouldBeNil)
		for i := range resp {
			t.Log(resp[i])
		}
	})
}

func TestClient_UpdatePresences2(t *testing.T) {
	Convey("TestClient_UpdatePresences2", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		client.UpdatePresences(context.Background(), &pb.Proxy{
			ProxyIp:   proxyIp,
			ProxyPort: proxyPort,
			BootTime:  proxyBoot,
		}, []*pb.Pres{
			{
				ProxyIp:      proxyIp,
				ProxyPort:    proxyPort,
				Uid:          userId,
				DeviceId:     []byte{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'},
				Status:       uint32(pb.PRES_STATUS_PRES_OFFLINE),
				ClientId:     clientId,
				ClientIp:     proxyIp,
				OnlineTime:   uint32(time.Now().Unix()),
				TerminalType: uint32(pb.PRES_TYPE_PRES_TYPE_IOS),
			},
			{
				ProxyIp:      proxyIp,
				ProxyPort:    proxyPort,
				Uid:          userId + 1,
				DeviceId:     []byte{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'},
				Status:       uint32(pb.PRES_STATUS_PRES_OFFLINE),
				ClientId:     clientId,
				ClientIp:     proxyIp,
				OnlineTime:   uint32(time.Now().Unix()),
				TerminalType: uint32(pb.PRES_TYPE_PRES_TYPE_IOS),
			},
			{
				ProxyIp:      proxyIp,
				ProxyPort:    proxyPort,
				Uid:          userId + 3,
				DeviceId:     []byte{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'},
				Status:       uint32(pb.PRES_STATUS_PRES_ONLINE),
				ClientId:     clientId,
				ClientIp:     proxyIp,
				OnlineTime:   uint32(time.Now().Unix()),
				TerminalType: uint32(pb.PRES_TYPE_PRES_TYPE_IOS),
			},
		})

		resp, err := client.GetPresences(context.Background(), []uint32{userId, userId + 1, userId + 2, userId + 3})
		So(err, ShouldBeNil)
		for i := range resp {
			t.Log(resp[i])
		}

		err = client.UpdatePresences(context.Background(), &pb.Proxy{
			ProxyIp:   proxyIp,
			ProxyPort: proxyPort,
			BootTime:  proxyBoot,
		}, []*pb.Pres{
			{
				ProxyIp:      proxyIp,
				ProxyPort:    proxyPort,
				Uid:          userId + 2,
				DeviceId:     []byte{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'},
				Status:       uint32(pb.PRES_STATUS_PRES_OFFLINE),
				ClientId:     clientId,
				ClientIp:     proxyIp,
				OnlineTime:   uint32(time.Now().Unix()),
				TerminalType: uint32(pb.PRES_TYPE_PRES_TYPE_IOS),
			},
			{
				ProxyIp:      proxyIp,
				ProxyPort:    proxyPort,
				Uid:          userId + 3,
				DeviceId:     []byte{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'},
				Status:       uint32(pb.PRES_STATUS_PRES_OFFLINE),
				ClientId:     clientId,
				ClientIp:     proxyIp,
				OnlineTime:   uint32(time.Now().Unix()),
				TerminalType: uint32(pb.PRES_TYPE_PRES_TYPE_IOS),
			},
		})

		resp, err = client.GetPresences(context.Background(), []uint32{userId, userId + 1, userId + 2, userId + 3})
		So(err, ShouldBeNil)
		for i := range resp {
			t.Log(resp[i])
		}
	})
}

func TestClient_SetUserPres(t *testing.T) {
	Convey("TestClient_SetUserPres", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		online, err := client.SetUserPres(context.Background(), userId, &pb.ProxyNode{
			Ip:   proxyIp,
			Port: proxyPort,
			Boot: proxyBoot,
		}, []*pb.PresInfo{
			{
				Key: k11,
				Val: v11,
			},
		})
		So(err, ShouldBeNil)
		So(online, ShouldBeTrue)

		online, err = client.SetUserPres(context.Background(), userId, &pb.ProxyNode{
			Ip:   proxyIp,
			Port: proxyPort,
			Boot: proxyBoot,
		}, []*pb.PresInfo{
			{
				Key: k12,
				Val: v12,
			},
		})
		So(err, ShouldBeNil)
		So(online, ShouldBeFalse)
	})
}

func TestClient_BatchSetUserPres(t *testing.T) {
	Convey("TestClient_BatchSetUserPres", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		result, err := client.BatchSetUserPres(context.Background(), &pb.ProxyNode{
			Ip:   proxyIp,
			Port: proxyPort,
			Boot: proxyBoot,
		}, map[uint32]*pb.PresInfoList{
			userId: {
				InfoList: []*pb.PresInfo{
					{
						Key: k11,
						Val: v11,
					},
				},
			},
			userId + 1: {
				InfoList: []*pb.PresInfo{
					{
						Key: k21,
						Val: v21,
					},
				},
			},
		})
		So(err, ShouldBeNil)
		t.Log(result)
		result, err = client.BatchSetUserPres(context.Background(), &pb.ProxyNode{
			Ip:   proxyIp,
			Port: proxyPort,
			Boot: proxyBoot,
		}, map[uint32]*pb.PresInfoList{
			userId: {
				InfoList: []*pb.PresInfo{
					{
						Key: k12,
						Val: v12,
					},
				},
			},
			userId + 1: {
				InfoList: []*pb.PresInfo{
					{
						Key: k22,
						Val: v22,
					},
				},
			},
		})
		So(err, ShouldBeNil)
		t.Log(result)
	})
}

func TestClient_DelUserPres(t *testing.T) {
	Convey("TestClient_DelUserPres", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		offline, err := client.DelUserPres(context.Background(), userId, &pb.ProxyNode{
			Ip:   proxyIp,
			Port: proxyPort,
			Boot: proxyBoot,
		}, []*pb.PresInfo{
			{
				Key: k11,
			},
		})
		So(err, ShouldBeNil)
		So(offline, ShouldBeFalse)

		offline, err = client.DelUserPres(context.Background(), userId, &pb.ProxyNode{
			Ip:   proxyIp,
			Port: proxyPort,
			Boot: proxyBoot,
		}, []*pb.PresInfo{
			{
				Key: k12,
			},
		})
		So(err, ShouldBeNil)
		So(offline, ShouldBeTrue)
	})
}

func TestClient_BatchDelUserPres(t *testing.T) {
	Convey("TestClient_BatchDelUserPres", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		result, err := client.BatchDelUserPres(context.Background(), &pb.ProxyNode{
			Ip:   proxyIp,
			Port: proxyPort,
			Boot: proxyBoot,
		}, map[uint32]*pb.PresInfoList{
			userId: {
				InfoList: []*pb.PresInfo{
					{
						Key: k11,
					},
				},
			},
			userId + 1: {
				InfoList: []*pb.PresInfo{
					{
						Key: k21,
					},
				},
			},
		})
		So(err, ShouldBeNil)
		t.Log(result)

		result, err = client.BatchDelUserPres(context.Background(), &pb.ProxyNode{
			Ip:   proxyIp,
			Port: proxyPort,
			Boot: proxyBoot,
		}, map[uint32]*pb.PresInfoList{
			userId: {
				InfoList: []*pb.PresInfo{
					{
						Key: k12,
					},
				},
			},
			userId + 1: {
				InfoList: []*pb.PresInfo{
					{
						Key: k22,
					},
				},
			},
		})
		So(err, ShouldBeNil)
		t.Log(result)
	})
}

func TestClient_PeekUserPres(t *testing.T) {
	Convey("TestClient_PeekUserPres", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		result, err := client.PeekUserPres(context.Background(), userId)
		So(err, ShouldBeNil)
		t.Log(result)
		result, err = client.PeekUserPres(context.Background(), userId+1)
		So(err, ShouldBeNil)
		t.Log(result)
	})
}

func TestClient_BatchPeekUserPres(t *testing.T) {
	Convey("TestClient_BatchPeekUserPres", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		result, err := client.BatchPeekUserPres(context.Background(), []uint32{userId, userId + 1, userId + 2})
		So(err, ShouldBeNil)
		t.Log(result)
	})
}

func TestClient_GetUserPres(t *testing.T) {
	Convey("TestClient_GetUserPres", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		result, err := client.GetUserPres(context.Background(), userId)
		So(err, ShouldBeNil)
		t.Log(result)
		result, err = client.GetUserPres(context.Background(), userId+1)
		So(err, ShouldBeNil)
		t.Log(result)
		result, err = client.GetUserPres(context.Background(), userId+2)
		So(err, ShouldBeNil)
		t.Log(result)
	})
}

func TestClient_BatchGetUserPres(t *testing.T) {
	Convey("TestClient_BatchGetUserPres", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		result, err := client.BatchGetUserPres(context.Background(), []uint32{userId, userId + 1, userId + 2})
		So(err, ShouldBeNil)
		t.Log(result)
	})
}
