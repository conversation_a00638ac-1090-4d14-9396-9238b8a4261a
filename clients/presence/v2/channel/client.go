package presence_v2_channel

import (
	"context"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/presencesvr"
	"google.golang.org/grpc"
)

const (
	serviceName = "presence-v2-channel"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelPresClient(cc)
			}, dopts...),
	}, nil
}

func (c *Client) typedStub() pb.ChannelPresClient {
	return c.Stub().(pb.ChannelPresClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts,
		grpc.WithInsecure(),
		grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) SetChannelPres(ctx context.Context, channel, ip, port, index uint32) protocol.ServerError {
	_, err := c.typedStub().SetChannelPres(ctx, &pb.SetChannelPresReq{
		ChannelId:  channel,
		ProxyIp:    ip,
		ProxyPort:  port,
		ProxyIndex: index,
	})
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}

func (c *Client) BatchSetChannelPres(ctx context.Context, infoList []*pb.BatchSetChannelPresReq_Info) protocol.ServerError {
	_, err := c.typedStub().BatchSetChannelPres(ctx, &pb.BatchSetChannelPresReq{
		InfoList: infoList,
	})
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}

func (c *Client) GetChannelProxyPres(ctx context.Context, channel uint32) ([]*pb.GetChannelProxyPresResp_Info, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelProxyPres(ctx, &pb.GetChannelProxyPresReq{
		ChannelId: channel,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.GetProxyList(), nil
}

func (c *Client) GetProxyChannelPres(ctx context.Context, ip, port, index uint32) ([]uint32, protocol.ServerError) {
	resp, err := c.typedStub().GetProxyChannelPres(ctx, &pb.GetProxyChannelPresReq{
		ProxyIp:    ip,
		ProxyPort:  port,
		ProxyIndex: index,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.GetChannelList(), nil
}

func (c *Client) GetProxyChannelWithScore(ctx context.Context, ip, port, index uint32) (map[uint32]int64, error) {
	resp, err := c.typedStub().GetProxyChannelWithScore(ctx, &pb.GetProxyChannelWithScoreReq{
		ProxyIp:    ip,
		ProxyPort:  port,
		ProxyIndex: index,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.GetChannelList(), nil
}

func (c *Client) GetChannelProxyWithoutIndex(ctx context.Context, channel uint32) ([]*pb.GetChannelProxyWithoutIndexResp_Info, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelProxyWithoutIndex(ctx, &pb.GetChannelProxyWithoutIndexReq{
		ChannelId: channel,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.GetProxyList(), nil
}
