package presence_v2

import (
	"context"
	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/presencesvr"
)

const (
	serviceName = "presence-v2" // presence: go version
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewUserPresClient(cc)
			}, dopts...),
	}, nil
}

func (c *Client) typedStub() pb.UserPresClient {
	return c.Stub().(pb.UserPresClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts,
		grpc.WithInsecure(),
		grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

// Deprecated: use SetUserPres() and DelUserPres() instead
func (c *Client) UpdatePresence(ctx context.Context, uid uint32, proxy *pb.Proxy, pres []*pb.Pres) protocol.ServerError {
	return c.UpdatePresences(ctx, proxy, pres)
}

// Deprecated: use BatchSetUserPres() and BatchDelUserPres() instead
func (c *Client) UpdatePresences(ctx context.Context, proxy *pb.Proxy, pres []*pb.Pres) protocol.ServerError {
	req := &pb.UpdateUserPresReq{
		Proxy: &pb.ProxyNode{
			Ip:   proxy.ProxyIp,
			Port: proxy.ProxyPort,
			Boot: proxy.BootTime,
		},
		InfoMap: make(map[uint32]*pb.PresInfoList),
	}
	for i := range pres {
		_, ok := req.InfoMap[pres[i].Uid]
		if !ok {
			req.InfoMap[pres[i].Uid] = &pb.PresInfoList{
				InfoList: make([]*pb.PresInfo, 0),
			}
		}
		info := &pb.PresInfo{
			Key: &pb.PresKey{
				ProxyIp:   proxy.ProxyIp,
				ProxyPort: proxy.ProxyPort,
				UserId:    pres[i].Uid,
				ClientId:  pres[i].ClientId,
			},
			Val: &pb.PresVal{
				DeviceId: pres[i].DeviceId,
				ClientIp: pres[i].ClientIp,
				OnlineTs: pres[i].OnlineTime,
				Terminal: pres[i].TerminalType,
			},
		}
		if pb.PRES_STATUS(pres[i].Status) == pb.PRES_STATUS_PRES_OFFLINE {
			info.Offline = true
		}
		req.InfoMap[pres[i].Uid].InfoList = append(req.InfoMap[pres[i].Uid].InfoList, info)
	}
	_, err := c.typedStub().UpdateUserPres(ctx, req)
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}

func (c *Client) UserKeepAlive(ctx context.Context, proxy *pb.ProxyInfo, pres []*pb.Pres) protocol.ServerError {
	req := &pb.UserKeepAliveReq{
		Proxy: &pb.ProxyNode{
			Ip:   proxy.ProxyIp,
			Port: uint32(proxy.ProxyPort),
		},
		InfoMap: make(map[uint32]*pb.PresInfoList),
	}
	for i := range pres {
		_, ok := req.InfoMap[pres[i].Uid]
		if !ok {
			req.InfoMap[pres[i].Uid] = &pb.PresInfoList{
				InfoList: make([]*pb.PresInfo, 0),
			}
		}
		req.InfoMap[pres[i].Uid].InfoList = append(req.InfoMap[pres[i].Uid].InfoList, &pb.PresInfo{
			Key: &pb.PresKey{
				ProxyIp:   proxy.ProxyIp,
				ProxyPort: uint32(proxy.ProxyPort),
				UserId:    pres[i].Uid,
				ClientId:  pres[i].ClientId,
			},
		})
	}
	_, err := c.typedStub().UserKeepAlive(ctx, req)
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}

// Deprecated: use GetUserPres() instead
func (c *Client) GetPresence(ctx context.Context, uid uint32) ([]*pb.Pres, protocol.ServerError) {
	resp, err := c.typedStub().GetUserPres(ctx, &pb.GetUserPresReq{
		Uid: uid,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	if resp.Info == nil || len(resp.Info.InfoList) == 0 {
		return nil, nil
	}
	result := make([]*pb.Pres, 0, len(resp.Info.InfoList))
	for i := range resp.Info.InfoList {
		result = append(result, &pb.Pres{
			ProxyIp:      resp.Info.InfoList[i].Key.ProxyIp,
			ProxyPort:    resp.Info.InfoList[i].Key.ProxyPort,
			Uid:          resp.Info.InfoList[i].Key.UserId,
			DeviceId:     resp.Info.InfoList[i].Val.DeviceId,
			Status:       uint32(pb.PRES_STATUS_PRES_ONLINE),
			ClientId:     resp.Info.InfoList[i].Key.ClientId,
			ClientIp:     resp.Info.InfoList[i].Val.ClientIp,
			OnlineTime:   resp.Info.InfoList[i].Val.OnlineTs,
			TerminalType: resp.Info.InfoList[i].Val.Terminal,
		})
	}
	return result, protocol.ToServerError(err)
}

// Deprecated: use BatchGetUserPres() instead
func (c *Client) GetPresences(ctx context.Context, uidList []uint32) ([]*pb.Pres, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetUserPres(ctx, &pb.BatchGetUserPresReq{
		UidList: uidList,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	if len(resp.InfoMap) == 0 {
		return nil, nil
	}
	result := make([]*pb.Pres, 0)
	for _, list := range resp.InfoMap {
		if list == nil || len(list.InfoList) == 0 {
			continue
		}
		for i := range list.InfoList {
			result = append(result, &pb.Pres{
				ProxyIp:      list.InfoList[i].Key.ProxyIp,
				ProxyPort:    list.InfoList[i].Key.ProxyPort,
				Uid:          list.InfoList[i].Key.UserId,
				DeviceId:     list.InfoList[i].Val.DeviceId,
				Status:       uint32(pb.PRES_STATUS_PRES_ONLINE),
				ClientId:     list.InfoList[i].Key.ClientId,
				ClientIp:     list.InfoList[i].Val.ClientIp,
				OnlineTime:   list.InfoList[i].Val.OnlineTs,
				TerminalType: list.InfoList[i].Val.Terminal,
			})
		}
	}
	return result, protocol.ToServerError(err)
}

// Deprecated: use BatchGetUserPres() instead
func (c *Client) GetPresencesMap(ctx context.Context, uidList []uint32) (map[uint32][]*pb.Pres, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetUserPres(ctx, &pb.BatchGetUserPresReq{
		UidList: uidList,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	presMap := make(map[uint32][]*pb.Pres)
	for uid, list := range resp.InfoMap {
		if list == nil || len(list.InfoList) == 0 {
			continue
		}
		result := make([]*pb.Pres, 0)
		for i := range list.InfoList {
			result = append(result, &pb.Pres{
				ProxyIp:      list.InfoList[i].Key.ProxyIp,
				ProxyPort:    list.InfoList[i].Key.ProxyPort,
				Uid:          list.InfoList[i].Key.UserId,
				DeviceId:     list.InfoList[i].Val.DeviceId,
				Status:       uint32(pb.PRES_STATUS_PRES_ONLINE),
				ClientId:     list.InfoList[i].Key.ClientId,
				ClientIp:     list.InfoList[i].Val.ClientIp,
				OnlineTime:   list.InfoList[i].Val.OnlineTs,
				TerminalType: list.InfoList[i].Val.Terminal,
			})
		}
		presMap[uid] = result
	}
	return presMap, protocol.ToServerError(err)
}

// Deprecated: use BatchGetUserPres() or BatchPeekUserPres() instead
func (c *Client) GetOnlineMap(ctx context.Context, uidList []uint32) (map[uint32]bool, protocol.ServerError) {
	resp, err := c.typedStub().BatchPeekUserPres(ctx, &pb.BatchPeekUserPresReq{
		UidList: uidList,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.OnlineMap, nil
}

// Deprecated: use StatProxy() instead
func (c *Client) StatPres(ctx context.Context, in *pb.StatPresReq) (*pb.StatPresResp, protocol.ServerError) {
	resp, err := c.typedStub().GetProxyPres(ctx, &pb.GetProxyPresReq{
		// nothing
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	out := &pb.StatPresResp{
		PresStatList: make([]*pb.PresStat, 0),
	}
	for i := range resp.InfoList {
		out.PresStatList = append(out.PresStatList, &pb.PresStat{
			ProxyIp:     resp.InfoList[i].ProxyIp,
			ProxyPort:   resp.InfoList[i].ProxyPort,
			OnlineCount: resp.InfoList[i].OnlineCnt,
			BootAt:      resp.InfoList[i].ProxyBoot,
		})
	}
	return out, protocol.ToServerError(err)
}

// GetUserPres get user presence info, result will be empty if user is offline
func (c *Client) GetUserPres(ctx context.Context, uid uint32) (*pb.PresInfoList, protocol.ServerError) {
	resp, err := c.typedStub().GetUserPres(ctx, &pb.GetUserPresReq{
		Uid: uid,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.Info, nil
}

// BatchGetUserPres get users presence info, result will be empty if user is offline
func (c *Client) BatchGetUserPres(ctx context.Context, uidList []uint32) (map[uint32]*pb.PresInfoList, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetUserPres(ctx, &pb.BatchGetUserPresReq{
		UidList: uidList,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.InfoMap, nil
}

// PeekUserPres return true if user is online (without proxy restart-event-checking)
func (c *Client) PeekUserPres(ctx context.Context, uid uint32) (bool, error) {
	resp, err := c.typedStub().PeekUserPres(ctx, &pb.PeekUserPresReq{
		Uid: uid,
	})
	if err != nil {
		return false, protocol.ToServerError(err)
	}
	return resp.Online, nil
}

// BatchPeekUserPres return true if user is online (without proxy restart-event-checking)
func (c *Client) BatchPeekUserPres(ctx context.Context, uidList []uint32) (map[uint32]bool, protocol.ServerError) {
	resp, err := c.typedStub().BatchPeekUserPres(ctx, &pb.BatchPeekUserPresReq{
		UidList: uidList,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.OnlineMap, nil
}

// SetUserPres set presence info, return true if user is FIRST online (no other presence info found before set)
func (c *Client) SetUserPres(ctx context.Context, uid uint32, proxy *pb.ProxyNode, presList []*pb.PresInfo) (bool, protocol.ServerError) {
	if proxy == nil || len(presList) == 0 {
		return false, nil
	}
	resp, err := c.typedStub().SetUserPres(ctx, &pb.SetUserPresReq{
		Uid:   uid,
		Proxy: proxy,
		Info: &pb.PresInfoList{
			InfoList: presList,
		},
	})
	if err != nil {
		return false, protocol.ToServerError(err)
	}
	return resp.Online, nil
}

// BatchSetUserPres batch set presence info, return true if user is FIRST online (no other presence info found before set)
func (c *Client) BatchSetUserPres(ctx context.Context, proxy *pb.ProxyNode, presList map[uint32]*pb.PresInfoList) (map[uint32]bool, protocol.ServerError) {
	if proxy == nil || len(presList) == 0 {
		return nil, nil
	}
	resp, err := c.typedStub().BatchSetUserPres(ctx, &pb.BatchSetUserPresReq{
		Proxy:   proxy,
		InfoMap: presList,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.Result, nil
}

// DelUserPres del presence info, return true if user is LAST offline (no other presence info found after del)
func (c *Client) DelUserPres(ctx context.Context, uid uint32, proxy *pb.ProxyNode, presList []*pb.PresInfo) (bool, protocol.ServerError) {
	if proxy == nil || len(presList) == 0 {
		return false, nil
	}
	resp, err := c.typedStub().DelUserPres(ctx, &pb.DelUserPresReq{
		Uid:   uid,
		Proxy: proxy,
		Info: &pb.PresInfoList{
			InfoList: presList,
		},
	})
	if err != nil {
		return false, protocol.ToServerError(err)
	}
	return resp.Offline, nil
}

// BatchDelUserPres batch del presence info, return true if user is FIRST online (no other presence info found after del)
func (c *Client) BatchDelUserPres(ctx context.Context, proxy *pb.ProxyNode, presList map[uint32]*pb.PresInfoList) (map[uint32]bool, protocol.ServerError) {
	if proxy == nil || len(presList) == 0 {
		return nil, nil
	}
	resp, err := c.typedStub().BatchDelUserPres(ctx, &pb.BatchDelUserPresReq{
		Proxy:   proxy,
		InfoMap: presList,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.Result, nil
}

func (c *Client) StatProxy(ctx context.Context) ([]*pb.ProxyNodeStat, protocol.ServerError) {
	resp, err := c.typedStub().GetProxyPres(ctx, &pb.GetProxyPresReq{})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.InfoList, nil
}

func (c *Client) GetUserPresReadable(ctx context.Context, uid uint32) (*pb.PresInfoListReadable, protocol.ServerError) {
	resp, err := c.typedStub().GetUserPresReadable(ctx, &pb.GetUserPresReq{
		Uid: uid,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.Info, nil
}

func (c *Client) BatchGetUserPresReadable(ctx context.Context, uidList []uint32) (map[uint32]*pb.PresInfoListReadable, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetUserPresReadable(ctx, &pb.BatchGetUserPresReq{
		UidList: uidList,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.InfoMap, nil
}
