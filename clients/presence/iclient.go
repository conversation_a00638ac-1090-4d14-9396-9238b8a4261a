package presence

import (
	presenceV2 "golang.52tt.com/clients/presence/v2"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/presencesvr"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetPresence(ctx context.Context, uid uint32) ([]*pb.Pres, protocol.ServerError)
	GetPresences(ctx context.Context, uidList []uint32) ([]*pb.Pres, protocol.ServerError)
	GetPresencesMap(ctx context.Context, uidList []uint32) (map[uint32][]*pb.Pres, protocol.ServerError)
	GetOnlineMap(ctx context.Context, uidList []uint32) (map[uint32]bool, protocol.ServerError)
	StatPres(ctx context.Context, in *pb.StatPresReq) (*pb.StatPresResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := presenceV2.NewClient(dopts...)
	return cli
}
