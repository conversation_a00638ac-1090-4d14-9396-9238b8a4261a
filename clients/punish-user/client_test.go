package punishuser

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/services/punishuser"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {

	<PERSON><PERSON>("GetPunishUser", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.PunishUserReq
		resp, err := client.GetPunishUser(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetPunishUser %+v", resp)
	})

}
