// Code generated by quicksilver-cli. DO NOT EDIT.
package backpack_func_card

import(
	"golang.52tt.com/pkg/client"
	context "context"
	grpc "google.golang.org/grpc"
	pb "golang.52tt.com/protocol/services/backpack-func-card"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AddFuncCardCfg(ctx context.Context, req *pb.AddFuncCardCfgReq, opts ...grpc.CallOption) (*pb.AddFuncCardCfgResp,protocol.ServerError)
	BatchGetAccelerateCardUsage(ctx context.Context, uidList []uint32, opts ...grpc.CallOption) (*pb.BatchGetAccelerateCardUsageResp,protocol.ServerError)
	BatchGetUserFuncCardUse(ctx context.Context, uidList []uint32) (map[uint32][]*pb.FuncCardCfg,protocol.ServerError)
	DelFuncCardCfg(ctx context.Context, cardId uint32, opts ...grpc.CallOption) (*pb.DelFuncCardCfgResp,protocol.ServerError)
	GetAccelerateCardUsage(ctx context.Context, uid uint32, opts ...grpc.CallOption) (*pb.GetAccelerateCardUsageResp,protocol.ServerError)
	GetFuncCardCfg(ctx context.Context, cardIds []uint32, opts ...grpc.CallOption) (*pb.GetFuncCardCfgResp,protocol.ServerError)
	GetFuncCardCfgWithOriginDesc(ctx context.Context, cardIds []uint32, opts ...grpc.CallOption) (*pb.GetFuncCardCfgResp,protocol.ServerError)
	UseFuncCard(ctx context.Context, req *pb.UseFuncCardReq, opts ...grpc.CallOption) (*pb.UseFuncCardResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
