package qiniu_storage_token

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	pb "golang.52tt.com/protocol/services/qiniu-storage-token"
	"google.golang.org/grpc"
	"time"
)

const serviceName = "qiniu-storage-token"

// Client of PushNotification service v2
type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewQiniuTokenServerClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.QiniuTokenServerClient { return c.Stub().(pb.QiniuTokenServerClient) }

func (c *Client) GetStorageToken(ctx context.Context, bucket, prefix string, exp time.Duration) (*pb.TokenResp, error) {
	return c.typedStub().GetStorageToken(ctx, &pb.TokenReq{
		Expired: uint32(exp),
	})
}
