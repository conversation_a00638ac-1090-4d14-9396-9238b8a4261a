package qiniu_storage_token

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/qiniu-storage-token"
	"google.golang.org/grpc"
	"time"
)

type IClient interface {
	client.BaseClient
	GetStorageToken(ctx context.Context, bucket, prefix string, exp time.Duration) (*pb.TokenResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
