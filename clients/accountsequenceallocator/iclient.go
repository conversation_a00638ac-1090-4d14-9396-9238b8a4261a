package accountsequenceallocator

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/app/accountsequenceallocator"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GenUid(ctx context.Context) (*pb.GenUidResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
