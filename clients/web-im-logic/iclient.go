package web_im_logic

import (
	"context"

	pb "golang.52tt.com/protocol/app/web-im-logic"
)

type WebImIClient interface {
	SendWebImMsg(context.Context, *pb.SendWebImMsgCommonReq) (*pb.SendWebImMsgCommonResp, error)
	ReadWebImMsg(context.Context, *pb.ReadWebImMsgCommonReq) (*pb.ReadWebImMsgCommonResp, error)
	GetWebImMsgList(context.Context, *pb.GetWebImMsgCommonReq) (*pb.GetWebImMsgCommonResp, error)
}
