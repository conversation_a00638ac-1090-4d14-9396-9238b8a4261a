package web_im_logic

import (
	"context"

	pb "golang.52tt.com/protocol/app/web-im-logic"
)

type WebImBaseClient struct{}

func (c *WebImBaseClient) SendWebImMsg(ctx context.Context, in *pb.SendWebImMsgCommonReq) (out *pb.SendWebImMsgCommonResp, err error) {
	out = new(pb.SendWebImMsgCommonResp)
	return
}
func (c *WebImBaseClient) ReadWebImMsg(ctx context.Context, in *pb.ReadWebImMsgCommonReq) (out *pb.ReadWebImMsgCommonResp, err error) {
	out = new(pb.ReadWebImMsgCommonResp)
	return
}
func (c *WebImBaseClient) GetWebImMsgList(ctx context.Context, in *pb.GetWebImMsgCommonReq) (out *pb.GetWebImMsgCommonResp, err error) {
	out = new(pb.GetWebImMsgCommonResp)
	return
}
