package user_risk_api

import (
	"golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	app "golang.52tt.com/protocol/app"
	pb "golang.52tt.com/protocol/services/user-risk-api"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	Check(ctx context.Context, req *pb.CheckReq) (*pb.CheckResp, protocol.ServerError)
	CheckHelper(ctx context.Context, req *pb.CheckReq, baseReq *app.BaseReq) (*pb.CheckResp, protocol.ServerError)
	Check1V1SuspectFraudHelper(ctx context.Context, category string, uid, toUid uint32, deviceId []byte, baseReq *app.BaseReq) (*pb.CheckSuspectFraudResp, protocol.ServerError)
	CheckEnterChannel(ctx context.Context, uid, channelId uint32, deviceId []byte, baseReq *app.BaseReq, channelOpt *pb.ChannelOpt) (*pb.CheckEnterChannelResp, protocol.ServerError)
	CheckSendChannelMsg(ctx context.Context, baseReq *app.BaseReq, entity *pb.Entity, riskCommInfo *pb.RiskCommInfo, channelOpt *pb.ChannelOpt) (*pb.CheckSendChannelMsgResp, protocol.ServerError)
	CheckJoinGroup(ctx context.Context, uid, groupId uint32, deviceId []byte, baseReq *app.BaseReq, groupOpt *pb.GroupOpt) (*pb.CheckJoinGroupResp, protocol.ServerError)
	CheckPcAuthStealAccount(ctx context.Context, req *pb.CheckPcAuthStealAccountReq) (*pb.CheckPcAuthStealAccountResp, error)
	CheckPhoneAuthStealAccount(ctx context.Context, req *pb.CheckPhoneAuthStealAccountReq) (int32, protocol.ServerError)
	CheckReg(ctx context.Context, in *pb.CheckRegReq) (bool, int32, error)
	CheckSendBackpackPresent(ctx context.Context, sendUid, recvUid uint32, deviceId []byte) (isRisky bool, err error)
	CheckSuspectFraud(ctx context.Context, req *pb.CheckSuspectFraudReq, opts ...grpc.CallOption) (*pb.CheckSuspectFraudResp, error)
	CheckSuspectFraudHelper(ctx context.Context, category string, uid, channelId uint32, deviceId []byte, baseReq *app.BaseReq) (*pb.CheckSuspectFraudResp, protocol.ServerError)
	CheckTBeanProvider(ctx context.Context, appId uint32, uid uint64, scene string, amount int64) (bool, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
