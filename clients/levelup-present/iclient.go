package levelup_present

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/levelup-present"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AddLevelupParentPresent(ctx context.Context, in *pb.LevelupParentPresentData) (*pb.EmptyMsg, error)
	UpdateLevelupParentPresent(ctx context.Context, in *pb.LevelupParentPresentData) (*pb.EmptyMsg, error)
	DeleteLevelupParentPresent(ctx context.Context, itemId uint32) (*pb.EmptyMsg, error)
	AddLevelupChildPresent(ctx context.Context, in *pb.LevelupChildPresentData) (*pb.EmptyMsg, error)
	UpdateLevelupChildPresent(ctx context.Context, in *pb.LevelupChildPresentData) (*pb.EmptyMsg, error)
	DeleteLevelupChildPresent(ctx context.Context, itemId uint32) (*pb.EmptyMsg, error)
	GetAllLevelupParentPresent(ctx context.Context) (*pb.LevelupPresentParentMap, error)
	GetLevelupParentPresentById(ctx context.Context, itemId uint32) (*pb.ItemRes, error)
	AddLevelupBatch(ctx context.Context, in *pb.LevelupPresentBatchData) (*pb.EmptyMsg, error)
	UpdateLevelupBatch(ctx context.Context, in *pb.LevelupPresentBatchData) (*pb.EmptyMsg, error)
	DeleteLevelupBatch(ctx context.Context, itemId uint32, batchCount uint32) (*pb.EmptyMsg, error)
	GetLevelupBatchById(ctx context.Context, itemId uint32) (*pb.LevelupPresentBatchList, error)
	GetAllLevelupBatch(ctx context.Context) (*pb.LevelupPresentBatchDataMap, error)
	GetLevelupPresentVersionList(ctx context.Context, itemId uint32) (*pb.VersionList, error)
	AddLevelupPresentVersion(ctx context.Context, itemId uint32, version uint32) (*pb.EmptyMsg, error)
	AddUserLevelupPresentExp(ctx context.Context, uid uint32, itemId uint32, version uint32, itemCount uint32, orderId string) (*pb.UserLevelExp, error)
	GetUserAllLevelupPresentStatus(ctx context.Context, uid uint32) (*pb.UserLevelupPresentStatusMap, error)
	GetLevelupParentPresent(ctx context.Context, offset uint32, limit uint32, presentType int32) (*pb.LevelupParentPresentList, error)
	GetLevelupChildrenPresent(ctx context.Context, parentItemId uint32) (*pb.LevelupChildPresentList, error)
	GetLevelupParentPresentData(ctx context.Context, parentItemId uint32) (*pb.LevelupParentPresentAllData, error)
	GetLevelupChildPresentData(ctx context.Context, itemId uint32) (*pb.LevelupChildPresentData, error)
	AddLevelupPerson(ctx context.Context, itemId uint32, version uint32, level uint32, uid uint32) (*pb.PushRes, error)
	GetLevelupUserRank(ctx context.Context, itemId uint32, version uint32, level uint32, offset uint32, limit uint32) (*pb.LevelupUserRankRes, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
