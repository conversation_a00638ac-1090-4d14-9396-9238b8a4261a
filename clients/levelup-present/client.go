package levelup_present

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	pb "golang.52tt.com/protocol/services/levelup-present"
	"google.golang.org/grpc"
)

const (
	serviceName = "levelup-present"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewLevelupPresentClient(cc)
		}, dopts...),
	}, nil
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.LevelupPresentClient {
	return c.Stub().(pb.LevelupPresentClient)
}

func (c *Client) AddLevelupParentPresent(ctx context.Context, in *pb.LevelupParentPresentData) (*pb.EmptyMsg, error) {
	return c.typedStub().AddLevelupParentPresent(ctx, in)
}

func (c *Client) UpdateLevelupParentPresent(ctx context.Context, in *pb.LevelupParentPresentData) (*pb.EmptyMsg, error) {
	return c.typedStub().UpdateLevelupParentPresent(ctx, in)
}

func (c *Client) DeleteLevelupParentPresent(ctx context.Context, itemId uint32) (*pb.EmptyMsg, error) {
	in := &pb.ItemReq{ItemId: itemId}
	return c.typedStub().DeleteLevelupParentPresent(ctx, in)
}

func (c *Client) AddLevelupChildPresent(ctx context.Context, in *pb.LevelupChildPresentData) (*pb.EmptyMsg, error) {
	return c.typedStub().AddLevelupChildPresent(ctx, in)
}

func (c *Client) UpdateLevelupChildPresent(ctx context.Context, in *pb.LevelupChildPresentData) (*pb.EmptyMsg, error) {
	return c.typedStub().UpdateLevelupChildPresent(ctx, in)
}

func (c *Client) DeleteLevelupChildPresent(ctx context.Context, itemId uint32) (*pb.EmptyMsg, error) {
	in := &pb.ItemReq{ItemId: itemId}
	return c.typedStub().DeleteLevelupChildPresent(ctx, in)
}

func (c *Client) GetAllLevelupParentPresent(ctx context.Context) (*pb.LevelupPresentParentMap, error) {
	return c.typedStub().GetAllLevelupParentPresent(ctx, &pb.EmptyMsg{})
}

func (c *Client) GetLevelupParentPresentById(ctx context.Context, itemId uint32) (*pb.ItemRes, error) {
	in := &pb.ItemReq{ItemId: itemId}
	return c.typedStub().GetLevelupParentPresentById(ctx, in)
}

func (c *Client) AddLevelupBatch(ctx context.Context, in *pb.LevelupPresentBatchData) (*pb.EmptyMsg, error) {
	return c.typedStub().AddLevelupBatch(ctx, in)
}

func (c *Client) UpdateLevelupBatch(ctx context.Context, in *pb.LevelupPresentBatchData) (*pb.EmptyMsg, error) {
	return c.typedStub().UpdateLevelupBatch(ctx, in)
}

func (c *Client) DeleteLevelupBatch(ctx context.Context, itemId uint32, batchCount uint32) (*pb.EmptyMsg, error) {
	in := &pb.ItemBatchReq{ItemId: itemId, BatchCount: batchCount}
	return c.typedStub().DeleteLevelupBatch(ctx, in)
}

func (c *Client) GetLevelupBatchById(ctx context.Context, itemId uint32) (*pb.LevelupPresentBatchList, error) {
	in := &pb.ItemReq{ItemId: itemId}
	return c.typedStub().GetLevelupBatchById(ctx, in)
}

func (c *Client) GetAllLevelupBatch(ctx context.Context) (*pb.LevelupPresentBatchDataMap, error) {
	return c.typedStub().GetAllLevelupBatch(ctx, &pb.EmptyMsg{})
}

func (c *Client) GetLevelupPresentVersionList(ctx context.Context, itemId uint32) (*pb.VersionList, error) {
	in := &pb.ItemReq{ItemId: itemId}
	return c.typedStub().GetLevelupPresentVersionList(ctx, in)
}

func (c *Client) AddLevelupPresentVersion(ctx context.Context, itemId uint32, version uint32) (*pb.EmptyMsg, error) {
	in := &pb.ItemVersionReq{ItemId:itemId, Version: version}
	return c.typedStub().AddLevelupPresentVersion(ctx, in)
}

func (c *Client) AddUserLevelupPresentExp(ctx context.Context, uid uint32, itemId uint32, version uint32, itemCount uint32, orderId string) (*pb.UserLevelExp, error) {
	in := &pb.UidItemVersionReq{Uid:uid, ItemId:itemId, Version: version, ItemCount: itemCount, OrderId: orderId}
	return c.typedStub().AddUserLevelupPresentExp(ctx, in)
}

func (c *Client) GetUserAllLevelupPresentStatus(ctx context.Context, uid uint32) (*pb.UserLevelupPresentStatusMap, error) {
	in := &pb.UidReq{Uid:uid}
	return c.typedStub().GetUserAllLevelupPresentStatus(ctx, in)
}

func (c *Client) GetLevelupParentPresent(ctx context.Context, offset uint32, limit uint32, presentType int32) (*pb.LevelupParentPresentList, error) {
	in := &pb.OffsetTypeReq{Offset: offset, Limit: limit, PresentType: presentType}
	return c.typedStub().GetLevelupParentPresent(ctx, in)
}

func (c *Client) GetLevelupChildrenPresent(ctx context.Context, parentItemId uint32) (*pb.LevelupChildPresentList, error) {
	in := &pb.ItemReq{ItemId: parentItemId}
	return c.typedStub().GetLevelupChildrenPresent(ctx, in)
}

func (c *Client) GetLevelupParentPresentData(ctx context.Context, parentItemId uint32) (*pb.LevelupParentPresentAllData, error) {
	in := &pb.ItemReq{ItemId: parentItemId}
	return c.typedStub().GetLevelupParentPresentData(ctx, in)
}

func (c *Client) GetLevelupChildPresentData(ctx context.Context, itemId uint32) (*pb.LevelupChildPresentData, error) {
	in := &pb.ItemReq{ItemId: itemId}
	return c.typedStub().GetLevelupChildPresentData(ctx, in)
}

func (c *Client) AddLevelupPerson(ctx context.Context, itemId uint32, version uint32, level uint32, uid uint32) (*pb.PushRes, error) {
	in := &pb.ItemLevelReq{ItemId: itemId, Version: version, Level: level, Uid: uid}
	return c.typedStub().AddLevelupPerson(ctx, in)
}

func (c *Client) GetLevelupUserRank(ctx context.Context, itemId uint32, version uint32, level uint32, offset uint32, limit uint32) (*pb.LevelupUserRankRes, error) {
	in := &pb.LevelupUserRankReq{
		ItemId:               itemId,
		Level:                level,
		Version:              version,
		Offset:               offset,
		Limit:                limit,
	}
	return c.typedStub().GetLevelupUserRank(ctx, in)
}