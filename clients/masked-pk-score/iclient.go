package masked_pk_score

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/masked-pk-score"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetUserMaskPkScore(ctx context.Context, uid uint32) (uint32, protocol.ServerError)
	AddUserMaskPkScore(ctx context.Context, orderId string, uid, sourceType, outsideTs uint32, changeScore int32, desc string) (uint32, protocol.ServerError)
	GetUserMaskPkChangeLog(ctx context.Context, req *pb.GetUserMaskPkChangeLogReq) ([]*pb.ScoreChangeLog, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
