// Code generated by quicksilver-cli. DO NOT EDIT.
package mvp_proxy

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/mvp-proxy"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	BatchGetBlackWhiteBoxSuperviseSwitch(ctx context.Context, switchType uint32, in []*pb.BlackWhiteBoxSuperviseSwitchInfo) (*pb.BatchGetBlackWhiteBoxSuperviseSwitchResp,protocol.ServerError)
	CheckFraudDevice(ctx context.Context, uid uint64, deviceId []byte) (*pb.CheckFraudDeviceResp,protocol.ServerError)
	CheckUserIdentity(ctx context.Context, in *pb.CheckUserIdentityReq) (*pb.CheckUserIdentityResp,protocol.ServerError)
	GetBlackWhiteBoxSuperviseSwitch(ctx context.Context, in *pb.GetBlackWhiteBoxSuperviseSwitchReq) (*pb.GetBlackWhiteBoxSuperviseSwitchResp,protocol.ServerError)
	RealtimeRule(ctx context.Context, in *pb.RealtimeRuleReq) (*pb.RealtimeRuleResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
