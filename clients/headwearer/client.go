package headwear

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"

	//"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc" //
	pb "golang.52tt.com/protocol/services/headwearsvr"
)

const (
	serviceName = "headwear"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewHeadWearClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.HeadWearClient { return c.Stub().(pb.HeadWearClient) }

func (c *Client) GiveHeadweartoUser(ctx context.Context, uid uint32, req *pb.GiveHeadweartoUserReq) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().GiveHeadweartoUser(ctx, req)
	return err
}

func (c *Client) GiveHeadweartoUserWithExpireTimeRel(ctx context.Context, uid uint32, req *pb.GiveHeadweartoUserReq) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().GiveHeadweartoUserWithExpireTimeRel(ctx, req)
	return err
}

func (c *Client) GetHeadwearConfig(ctx context.Context, uid uint32, headwearId uint32) (*pb.HeadwearConfig, error) {
	req := pb.HeadwearConfigReq{
		HeadwearId: headwearId,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().GetHeadwearConfig(ctx, &req)
	return resp, err
}

func (c *Client) GetHeadwearInUseList(ctx context.Context, uid uint32, uids []uint32) (*pb.GetUserHeadwearInUseListResp, error) {
	req := pb.GetUserHeadwearInUseListReq{
		Uids: uids,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().GetUserHeadwearInUseList(ctx, &req)
	return resp, err
}
