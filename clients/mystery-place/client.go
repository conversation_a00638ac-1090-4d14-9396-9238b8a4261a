package mystery_place

import (
	"golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	mystery_place "golang.52tt.com/protocol/services/mystery-place"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

const (
	serviceName = "mystery-place"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return mystery_place.NewMysteryPlaceClient(cc)
			},
			dopts...,
		),
	}, nil
}

func (c *Client) typedStub() mystery_place.MysteryPlaceClient {
	return c.Stub().(mystery_place.MysteryPlaceClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) UpsertScenarioInfo(ctx context.Context, req *mystery_place.UpsertScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place.UpsertScenarioInfoResp, error) {
	resp, err := c.typedStub().UpsertScenarioInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DelScenarioInfo(ctx context.Context, req *mystery_place.DelScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place.DelScenarioInfoResp, error) {
	resp, err := c.typedStub().DelScenarioInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListScenarioInfo(ctx context.Context, req *mystery_place.ListScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place.ListScenarioInfoResp, error) {
	resp, err := c.typedStub().ListScenarioInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetScenarioInfo(ctx context.Context, req *mystery_place.GetScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place.GetScenarioInfoResp, error) {
	resp, err := c.typedStub().GetScenarioInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) IsNewUser(ctx context.Context, req *mystery_place.IsNewUserReq, opts ...grpc.CallOption) (*mystery_place.IsNewUserResp, error) {
	resp, err := c.typedStub().IsNewUser(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPlayerLastRecord(ctx context.Context, req *mystery_place.GetPlayerLastRecordReq, opts ...grpc.CallOption) (*mystery_place.GetPlayerLastRecordResp, error) {
	resp, err := c.typedStub().GetPlayerLastRecord(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetGamePercentByChannelIds(ctx context.Context, req *mystery_place.BatchGetGamePercentByChannelIdsReq, opts ...grpc.CallOption) (*mystery_place.BatchGetGamePercentByChannelIdsResp, error) {
	resp, err := c.typedStub().BatchGetGamePercentByChannelIds(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetPlayerGamePercentByGameIds(ctx context.Context, req *mystery_place.BatchGetPlayerGamePercentByGameIdsReq, opts ...grpc.CallOption) (*mystery_place.BatchGetPlayerGamePercentByGameIdsResp, error) {
	resp, err := c.typedStub().BatchGetPlayerGamePercentByGameIds(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPlayerGamePercentMapByGameIds(ctx context.Context, req *mystery_place.BatchGetPlayerGamePercentByGameIdsReq, opts ...grpc.CallOption) (map[uint32]*mystery_place.UserGamePercent, error) {
	resp, err := c.typedStub().BatchGetPlayerGamePercentByGameIds(ctx, req, opts...)
	percentMap := make(map[uint32]*mystery_place.UserGamePercent, len(resp.GetGamePercents()))
	for _, percent := range resp.GetGamePercents() {
		percentMap[percent.GetGameId()] = percent
	}
	return percentMap, protocol.ToServerError(err)
}

func (c *Client) GetGamePercentMapByChannelIds(ctx context.Context, req *mystery_place.BatchGetGamePercentByChannelIdsReq, opts ...grpc.CallOption) (map[uint32]*mystery_place.RoomGamePercent, error) {
	resp, err := c.typedStub().BatchGetGamePercentByChannelIds(ctx, req, opts...)
	percentMap := make(map[uint32]*mystery_place.RoomGamePercent, len(resp.GetGamePercents()))
	for _, percent := range resp.GetGamePercents() {
		percentMap[percent.GetRoomId()] = percent
	}
	return percentMap, protocol.ToServerError(err)
}

func (c *Client) BatchCheckHelperListMap(ctx context.Context, req *mystery_place.BatchCheckHelperListReq, opts ...grpc.CallOption) (map[uint32]bool, error) {
	resp, err := c.typedStub().BatchCheckHelperList(ctx, req, opts...)
	helperMap := make(map[uint32]bool, len(resp.GetCanWelcomeBeHelper()))
	for _, helperUid := range resp.GetCanWelcomeBeHelper() {
		helperMap[helperUid] = true
	}
	return helperMap, protocol.ToServerError(err)
}

func (c *Client) BatchCheckHelperList(ctx context.Context, req *mystery_place.BatchCheckHelperListReq, opts ...grpc.CallOption) (*mystery_place.BatchCheckHelperListResp, error) {
	return c.typedStub().BatchCheckHelperList(ctx, req, opts...)
}

func (c *Client) RearrangeScenario(ctx context.Context, in *mystery_place.RearrangeScenarioReq, opts ...grpc.CallOption) (*mystery_place.RearrangeScenarioResp, error) {
	return c.typedStub().RearrangeScenario(ctx, in, opts...)
}

func (c *Client) SetScenarioShareLink(ctx context.Context, in *mystery_place.SetScenarioShareLinkReq, opts ...grpc.CallOption) (*mystery_place.SetScenarioShareLinkResp, error) {
	return nil, nil
}

func (c *Client) BatchGetScenarioShareLink(ctx context.Context, in *mystery_place.BatchGetScenarioShareLinkReq, opts ...grpc.CallOption) (*mystery_place.BatchGetScenarioShareLinkResp, error) {
	return c.typedStub().BatchGetScenarioShareLink(ctx, in, opts...)
}

func (c *Client) DelScenarioShareLink(ctx context.Context, in *mystery_place.DelScenarioShareLinkReq, opts ...grpc.CallOption) (*mystery_place.DelScenarioShareLinkResp, error) {
	return c.typedStub().DelScenarioShareLink(ctx, in, opts...)
}

func (c *Client) GetNewUsers(ctx context.Context, in *mystery_place.GetNewUsersReq, opts ...grpc.CallOption) (*mystery_place.GetNewUsersResp, error) {
	return c.typedStub().GetNewUsers(ctx, in, opts...)
}

func (c *Client) GetScenarioInfoTotalCount(ctx context.Context, in *mystery_place.GetScenarioInfoTotalCountReq, opts ...grpc.CallOption) (*mystery_place.GetScenarioInfoTotalCountResp, error) {
	return c.typedStub().GetScenarioInfoTotalCount(ctx, in, opts...)
}

func (c *Client) GetPlaymateTagByIds(ctx context.Context, in *mystery_place.GetPlaymateTagByIdsReq, opts ...grpc.CallOption) (*mystery_place.GetPlaymateTagByIdsResp, error) {
	return c.typedStub().GetPlaymateTagByIds(ctx, in, opts...)
}

func (c *Client) AddUserCommentScenario(ctx context.Context, in *mystery_place.AddUserCommentScenarioReq, opts ...grpc.CallOption) (*mystery_place.AddUserCommentScenarioResp, error) {
	return c.typedStub().AddUserCommentScenario(ctx, in, opts...)
}

func (c *Client) ListScenarioComment(ctx context.Context, in *mystery_place.ListScenarioCommentReq, opts ...grpc.CallOption) (*mystery_place.ListScenarioCommentResp, error) {
	return c.typedStub().ListScenarioComment(ctx, in, opts...)
}

func (c *Client) UpdateScenarioComment(ctx context.Context, in *mystery_place.UpdateScenarioCommentReq, opts ...grpc.CallOption) (*mystery_place.UpdateScenarioCommentResp, error) {
	return c.typedStub().UpdateScenarioComment(ctx, in, opts...)
}

func (c *Client) ListScenarioCommentSummary(ctx context.Context, in *mystery_place.ListScenarioCommentSummaryReq, opts ...grpc.CallOption) (*mystery_place.ListScenarioCommentSummaryResp, error) {
	return c.typedStub().ListScenarioCommentSummary(ctx, in, opts...)
}

func (c *Client) SetScenarioManualAverage(ctx context.Context, in *mystery_place.SetScenarioManualAverageReq, opts ...grpc.CallOption) (*mystery_place.SetScenarioManualAverageResp, error) {
	return c.typedStub().SetScenarioManualAverage(ctx, in, opts...)
}

func (c *Client) AddPlaymateTag(ctx context.Context, in *mystery_place.AddPlaymateTagReq, opts ...grpc.CallOption) (*mystery_place.AddPlaymateTagResp, error) {
	return c.typedStub().AddPlaymateTag(ctx, in, opts...)
}

func (c *Client) DeletePlaymateTag(ctx context.Context, in *mystery_place.DeletePlaymateTagReq, opts ...grpc.CallOption) (*mystery_place.DeletePlaymateTagResp, error) {
	return c.typedStub().DeletePlaymateTag(ctx, in, opts...)
}

func (c *Client) UpdatePlaymateTag(ctx context.Context, in *mystery_place.UpdatePlaymateTagReq, opts ...grpc.CallOption) (*mystery_place.UpdatePlaymateTagResp, error) {
	return c.typedStub().UpdatePlaymateTag(ctx, in, opts...)
}

func (c *Client) ReorderPlaymateTags(ctx context.Context, in *mystery_place.ReorderPlaymateTagsReq, opts ...grpc.CallOption) (*mystery_place.ReorderPlaymateTagsResp, error) {
	return c.typedStub().ReorderPlaymateTags(ctx, in, opts...)
}

func (c *Client) GetPlaymateTags(ctx context.Context, in *mystery_place.GetPlaymateTagsReq, opts ...grpc.CallOption) (*mystery_place.GetPlaymateTagsResp, error) {
	return c.typedStub().GetPlaymateTags(ctx, in, opts...)
}

func (c *Client) AddUserPlaymateTag(ctx context.Context, in *mystery_place.AddUserPlaymateTagReq, opts ...grpc.CallOption) (*mystery_place.AddUserPlaymateTagResp, error) {
	return c.typedStub().AddUserPlaymateTag(ctx, in, opts...)
}

func (c *Client) BatGetUserHotPlaymateTag(ctx context.Context, in *mystery_place.BatGetUserHotPlaymateTagReq, opts ...grpc.CallOption) (map[uint32]*mystery_place.UserHotPlaymateTag, error) {
	resp, err := c.typedStub().BatGetUserHotPlaymateTag(ctx, in, opts...)
	if err != nil {
		return nil, err
	}
	userMap := make(map[uint32]*mystery_place.UserHotPlaymateTag, len(resp.GetUserList()))
	for _, user := range resp.GetUserList() {
		userMap[user.Uid] = user
	}
	return userMap, err
}

func (c *Client) GetNewScenarioTips(ctx context.Context, in *mystery_place.GetNewScenarioTipsReq, opts ...grpc.CallOption) (*mystery_place.GetNewScenarioTipsResp, error) {
	return c.typedStub().GetNewScenarioTips(ctx, in, opts...)
}

func (c *Client) MarkNewScenarioTipRead(ctx context.Context, in *mystery_place.MarkNewScenarioTipReadReq, opts ...grpc.CallOption) (*mystery_place.MarkNewScenarioTipReadResp, error) {
	return c.typedStub().MarkNewScenarioTipRead(ctx, in, opts...)
}

func (c *Client) GetPlayedScenarioRecordList(ctx context.Context, in *mystery_place.GetPlayedScenarioRecordListReq, opts ...grpc.CallOption) (*mystery_place.GetPlayedScenarioRecordListResp, error) {
	return c.typedStub().GetPlayedScenarioRecordList(ctx, in, opts...)
}

func (c *Client) GetPlayedScenarioRecordDetail(ctx context.Context, in *mystery_place.GetPlayedScenarioRecordDetailReq, opts ...grpc.CallOption) (*mystery_place.GetPlayedScenarioRecordDetailResp, error) {
	return c.typedStub().GetPlayedScenarioRecordDetail(ctx, in, opts...)
}

func (c *Client) GetScenarioChapterSummary(ctx context.Context, in *mystery_place.GetScenarioChapterSummaryReq, opts ...grpc.CallOption) (*mystery_place.GetScenarioChapterSummaryResp, error) {
	return c.typedStub().GetScenarioChapterSummary(ctx, in, opts...)
}

func (c *Client) SetPlayedScenarioRecordVisibility(ctx context.Context, in *mystery_place.SetPlayedScenarioRecordVisibilityReq, opts ...grpc.CallOption) (*mystery_place.SetPlayedScenarioRecordVisibilityResp, error) {
	return c.typedStub().SetPlayedScenarioRecordVisibility(ctx, in, opts...)
}

func (c *Client) AddFeedback(ctx context.Context, in *mystery_place.AddFeedbackReq, opts ...grpc.CallOption) (*mystery_place.AddFeedbackResp, error) {
	return c.typedStub().AddFeedback(ctx, in, opts...)
}

func (c *Client) BatchGetRcmdTabById(ctx context.Context, in *mystery_place.BatchGetRcmdTabByIdReq, opts ...grpc.CallOption) (out *mystery_place.BatchGetRcmdTabByIdResp, err error) {
	return c.typedStub().BatchGetRcmdTabById(ctx, in, opts...)
}

func (c *Client) BatchGetUserCommentScenario(ctx context.Context, in *mystery_place.BatchGetUserCommentScenarioReq, opts ...grpc.CallOption) (out *mystery_place.BatchGetUserCommentScenarioResp, err error) {
	return c.typedStub().BatchGetUserCommentScenario(ctx, in, opts...)
}

func (c *Client) GetScenarioHotRank(ctx context.Context, in *mystery_place.GetScenarioHotRankReq, opts ...grpc.CallOption) (out *mystery_place.GetScenarioHotRankResp, err error) {
	return c.typedStub().GetScenarioHotRank(ctx, in, opts...)
}
