package gameradar

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/gameradar"
	"google.golang.org/grpc"
)

const (
	serviceName = "gameradar"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewGameRadarClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.GameRadarClient { return c.Stub().(pb.GameRadarClient) }


func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) InvitePlay(ctx context.Context, req pb.InvitePlayReq) (*pb.InvitePlayRsp, protocol.ServerError) {
	resp, err := c.typedStub().InvitePlay(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) InviteSuc(ctx context.Context, req pb.InviteSucReq) (*pb.InviteSucRsp, protocol.ServerError) {
	resp, err := c.typedStub().InviteSuc(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetInviteInfo(ctx context.Context, req pb.InviteInfoReq) (*pb.InviteInfoRsp, protocol.ServerError) {
	resp, err := c.typedStub().GetInviteInfo(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) RadarDisplay(ctx context.Context, req pb.RadarDisplayReq) (*pb.RadarDisplayRsp, protocol.ServerError) {
	resp, err := c.typedStub().RadarDisplay(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetHasInviteInfo(ctx context.Context, req pb.GetHasInviteReq) (*pb.GetHasInviteRsp, protocol.ServerError) {
	resp, err := c.typedStub().GetHasInviteInfo(ctx, &req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) BatchGetUserRadarStatus(ctx context.Context, uids []uint32) (*pb.BatchGetUserRadarStatusResp, protocol.ServerError) {
	var req pb.BatchGetUserRadarStatusReq = pb.BatchGetUserRadarStatusReq{
		UidList: uids,
	}
	resp, err := c.typedStub().BatchGetUserRadarStatus(ctx, &req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetTotalOpenUserNum(ctx context.Context) (uint32, protocol.ServerError) {
	var req pb.GetTotalOpenUserNumReq
	resp, err := c.typedStub().GetTotalOpenUserNum(ctx, &req)
	return resp.GetNum(), protocol.ToServerError(err)
}
func (c *Client) GetUserRadarStatus(ctx context.Context, uid uint32) (*pb.GetUserRadarStatusResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserRadarStatus(ctx, &pb.GetUserRadarStatusReq{
		Uid: uid,
	})
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetAllUserInRadarOpening(ctx context.Context) (*pb.GetAllUserInRadarOpeningResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAllUserInRadarOpening(ctx, &pb.GetAllUserInRadarOpeningReq{})
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetAllUserInRadarOpeningV2(ctx context.Context) (*pb.GetAllUserInRadarOpeningV2Resp, protocol.ServerError) {
	resp, err := c.typedStub().GetAllUserInRadarOpeningV2(ctx, &pb.GetAllUserInRadarOpeningV2Req{})
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetUserChannelPushInfo(ctx context.Context, uid uint32) (*pb.GetUserChannelPushInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserChannelPushInfo(ctx, &pb.GetUserChannelPushInfoReq{
		Uid: uid,
	})
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetRadarConfig(ctx context.Context, req pb.GetRadarConfigReq) (*pb.GetRadarConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRadarConfig(ctx, &req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetValidOpenRadarUser(ctx context.Context, req pb.GetValidOpenRadarUserReq) (*pb.GetValidOpenRadarUserResp, protocol.ServerError) {
	resp, err := c.typedStub().GetValidOpenRadarUser(ctx, &req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) StartRadar(ctx context.Context, req pb.StartRadarReq) (*pb.StartRadarResp, protocol.ServerError) {
	resp, err := c.typedStub().StartRadar(ctx, &req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) DelInviteInfo(ctx context.Context, req pb.DelInviteInfoReq) (*pb.DelInviteInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().DelInviteInfo(ctx, &req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) StopRadar(ctx context.Context, req pb.StopRadarReq) (*pb.StopRadarResp, protocol.ServerError) {
	resp, err := c.typedStub().StopRadar(ctx, &req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) Tabs(ctx context.Context) ([]*pb.Tab, protocol.ServerError) {
	resp, err := c.typedStub().Tabs(ctx, &pb.TabsReq{})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return resp.Tabs, nil
}

func (c *Client) Blocks(ctx context.Context, tabId, tagId uint32) ([]*pb.Block, protocol.ServerError) {
	resp, err := c.typedStub().Blocks(ctx, &pb.BlocksReq{TabId: tabId, TagId: tagId})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return resp.Blocks, nil
}

func (c *Client) GetGetRadarIconConfig(ctx context.Context) (*pb.GetRadarIconConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRadarIconConfig(ctx, &pb.GetRadarIconConfigReq{})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}

////======================
//func (c *Client) PushRadarEvent(ctx context.Context, req pb.PushRadarEventReq) (*pb.PushRadarEventResp, protocol.ServerError) {
//	resp, err := c.eventTypedStub().PushRadarEvent(ctx, &req)
//	return resp, protocol.ToServerError(err)
//}
//func (c *Client) GetUserRadarEvent(ctx context.Context, req pb.GetUserRadarEventReq) (*pb.GetUserRadarEventResp, protocol.ServerError) {
//	resp, err := c.eventTypedStub().GetUserRadarEvent(ctx, &req)
//	return resp, protocol.ToServerError(err)
//}
//func (c *Client) PushDatingEvent(ctx context.Context, req pb.PushDatingEventReq) (*pb.PushDatingEventResp, protocol.ServerError) {
//	resp, err := c.eventTypedStub().PushDatingEvent(ctx, &req)
//	return resp, protocol.ToServerError(err)
//}
