// Code generated by quicksilver-cli. DO NOT EDIT.
package realnameauth_go

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/realnameauth-go"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	ClearDeviceForbidRealNameStatus(ctx context.Context, uid uint64, deviceId []byte) protocol.ServerError
	CreateFaceAuthResultToken(ctx context.Context, in *pb.FaceAuthResultToken) (string,protocol.ServerError)
	FaceCheckResultReport(ctx context.Context, in *pb.FaceCheckResultReportReq) (*pb.FaceCheckResultReportResp,protocol.ServerError)
	InDeviceForbidRealNameStatus(ctx context.Context, uid uint64, deviceId []byte) (bool,int64,protocol.ServerError)
	ReportFaceAuthResult(ctx context.Context, in *pb.ReportFaceAuthResultReq) (*pb.ReportFaceAuthResultResp,protocol.ServerError)
	SetDeviceForbidRealNameStatus(ctx context.Context, uid uint64, deviceId []byte, ttl int64) (*pb.SetDeviceForbidRealNameStatusResp,protocol.ServerError)
	VerifyFaceAuthResultToken(ctx context.Context, in *pb.VerifyResultTokenReq) (bool,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
