package greenbaba

import (
	"context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func TestClient(t *testing.T) {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
	client := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("channelmusic.52tt.local"))

	_, resp, err := client.ScanSanctionHistoryListById(context.Background(), 2212659, 1, 11918149, true, 1, 1)
	if err != nil {
		t.<PERSON>rror(err)
	}
	t.Log(resp)
}
