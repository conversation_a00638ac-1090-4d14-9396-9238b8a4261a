package youknowwhosettlement

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/tracing"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/youknowwho/youknowwhosettlement"
	"google.golang.org/grpc"
)

const (
	serviceName = "you-know-who-settlement"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewYouKnowWhoSettlementClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.YouKnowWhoSettlementClient {
	return c.Stub().(pb.YouKnowWhoSettlementClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func (c *Client) OpenUKW(ctx context.Context, req *pb.OpenUKWReq) (*pb.OpenUKWResp, protocol.ServerError) {
	resp, err := c.typedStub().OpenUKW(ctx, req)
	return resp, protocol.ToServerError(err)
}
