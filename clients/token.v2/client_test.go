package token

import (
	"context"
	"fmt"
	"os"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/services/token.v2"
	"google.golang.org/grpc/grpclog"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stderr))
}

func TestClient_GrantToken(t *testing.T) {
	Convey("TestClient_GrantToken", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)

		resp, err := client.GrantToken(context.Background(), &pb.TokenInfoV2{
			Uid:          uint32(12345678),
			DeviceId:     []byte{},
			ClientType:   1,
			TerminalType: 1,
			Scopes:       []string{},
			Extra: map[string]string{
				"channelId": "1000",
			},
			Aud:    []string{"pushd"},
			AccTtl: 180,
			RefTtl: 180,
		})
		So(err, ShouldBeNil)
		fmt.Println(resp.GetAccessToken(), resp.GetRefreshToken())
	})
}

func TestClient_ValidateToken(t *testing.T) {
	Convey("TestClient_ValidateToken", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)

		token, err := client.ValidateToken(context.Background(), 1, "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")
		if err != nil {
			fmt.Println(err)
		} else {
			fmt.Println(token)
		}
	})
}

func TestClient_GetWechatAccessToken(t *testing.T) {
	Convey("TestClient_GetWechatAccessToken", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)

		token, err := client.GetWechatAccessToken(context.Background(), "wx7470acff98767f78")
		So(err, ShouldBeNil)
		fmt.Println(token)
	})
}

func TestClient_GetWechatApiTicket(t *testing.T) {
	Convey("TestClient_GetWechatApiTicket", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)

		token, err := client.GetWechatApiTicket(context.Background(), "wx7470acff98767f78")
		So(err, ShouldBeNil)
		fmt.Println(token)
	})
}

func TestClient_CreateTokenSeed(t *testing.T) {
	Convey("TestClient_CreateTokenSeed", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)

		seed, err := client.CreateTokenSeed(context.Background(), 12345678, 1, false)
		So(err, ShouldBeNil)
		fmt.Println(seed)
	})
}

func TestClient_ClearToken(t *testing.T) {
	Convey("TestClient_ClearToken", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)

		err = client.ClearToken(context.Background(), 12345678)
		So(err, ShouldBeNil)
	})
}
