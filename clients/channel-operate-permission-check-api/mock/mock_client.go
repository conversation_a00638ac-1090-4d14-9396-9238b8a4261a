// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/channel-operate-permission-check-api (interfaces: IClient)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	channel_operate_permission_check_api "golang.52tt.com/clients/channel-operate-permission-check-api"
	channel_operate_permission_mgr "golang.52tt.com/protocol/services/channel-operate-permission-mgr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CheckOperatePermission mocks base method.
func (m *MockIClient) CheckOperatePermission(arg0 context.Context, arg1 *channel_operate_permission_mgr.CheckOperatePermissionReq) (*channel_operate_permission_check_api.CheckPermissionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckOperatePermission", arg0, arg1)
	ret0, _ := ret[0].(*channel_operate_permission_check_api.CheckPermissionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckOperatePermission indicates an expected call of CheckOperatePermission.
func (mr *MockIClientMockRecorder) CheckOperatePermission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckOperatePermission", reflect.TypeOf((*MockIClient)(nil).CheckOperatePermission), arg0, arg1)
}

// SimpleCheckOperatePermission mocks base method.
func (m *MockIClient) SimpleCheckOperatePermission(arg0 context.Context, arg1, arg2 uint32, arg3 channel_operate_permission_mgr.ChannelOperate) (*channel_operate_permission_check_api.CheckPermissionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimpleCheckOperatePermission", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*channel_operate_permission_check_api.CheckPermissionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimpleCheckOperatePermission indicates an expected call of SimpleCheckOperatePermission.
func (mr *MockIClientMockRecorder) SimpleCheckOperatePermission(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimpleCheckOperatePermission", reflect.TypeOf((*MockIClient)(nil).SimpleCheckOperatePermission), arg0, arg1, arg2, arg3)
}
