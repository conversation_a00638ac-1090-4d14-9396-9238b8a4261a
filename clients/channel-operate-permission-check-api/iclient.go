package channel_operate_permission_check_api

import (
	"context"
	channel_operate_permission_mgr "golang.52tt.com/protocol/services/channel-operate-permission-mgr"
	"google.golang.org/grpc"
)

type IClient interface {
	CheckOperatePermission(context.Context, *channel_operate_permission_mgr.CheckOperatePermissionReq) (*CheckPermissionResp, error)
	SimpleCheckOperatePermission(ctx context.Context, optUid, cid uint32, optId channel_operate_permission_mgr.ChannelOperate) (*CheckPermissionResp, error)
}

func NewIClient(opts ...grpc.DialOption) (IClient, error) {
	return NewClient(opts...)
}
