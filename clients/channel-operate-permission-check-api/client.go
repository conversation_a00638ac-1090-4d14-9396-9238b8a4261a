package channel_operate_permission_check_api

import (
	"context"
	"golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/channel-operate-permission-check-api/configz"
	channelScheme_client "golang.52tt.com/clients/channel-scheme"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	channel_operate_permission_mgr "golang.52tt.com/protocol/services/channel-operate-permission-mgr"
	"google.golang.org/grpc"
)

var (
	confLoader *configz.ChannelOperatePermissionClientDcLoader
)

type Client struct {
	channelOperatePermissionMgrClient channel_operate_permission_mgr.ChannelOperatePermissionMgrClient
	channelClient                     *channel.Client
	channelSchemeClient               *channelScheme_client.Client
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	c := &Client{}
	var err error

	c.channelOperatePermissionMgrClient, err = channel_operate_permission_mgr.NewClient(context.Background(), dopts...)
	if err != nil {
		return nil, err
	}

	c.channelClient = channel.NewClient(dopts...)

	c.channelSchemeClient = channelScheme_client.NewClient(dopts...)

	//加载配置
	if confLoader, err = configz.NewPermissionApiDcLoader("/data/oss/conf-center/tt/operate-permission-api-conf.json"); err != nil {
		log.ErrorWithCtx(context.Background(), "NewCommonPeopleLimitDcLoader failed:%v", err)
		return c, err
	}

	return c, nil
}

type CheckPermissionResp struct {
	HasPermission     bool
	PermissionErrCode int32
	PermissionErrMsg  string
}

func (c *Client) CheckOperatePermission(ctx context.Context, req *channel_operate_permission_mgr.CheckOperatePermissionReq) (*CheckPermissionResp, error) {
	//检查请求合法性
	if req.Opt == channel_operate_permission_mgr.ChannelOperate_CHANNEL_OPERATE_UNKNOWN {
		log.ErrorWithCtx(ctx, "CheckOperatePermission req.Opt == 0")
		return nil, protocol.NewExactServerError(nil, status.ErrChannelOperatePermissionNotSupport, "操作id不能为0")
	}
	if req.OptUid == 0 {
		log.ErrorWithCtx(ctx, "CheckOperatePermission req.OptUid == 0")
		return nil, protocol.NewExactServerError(nil, status.ErrChannelOperatePermissionNotSupport, "opUid不能为0")
	}
	if req.Cid == 0 {
		log.ErrorWithCtx(ctx, "CheckOperatePermission req.Cid == 0")
		return nil, protocol.NewExactServerError(nil, status.ErrChannelOperatePermissionNotSupport, "cid不能为0")
	}

	//test
	if confLoader.IsChannelTypeWithBindId(req.ChannelType) {
		log.DebugWithCtx(ctx, "IsChannelTypeWithBindId:%d, cid:%d, uid:%d", req.ChannelType, req.Cid, req.OptUid)
	}

	//其他参数，如果为空，自动填充。是否需要自动填充bindId后续改成配置的
	if req.ChannelType == 0 || (req.ChannelBindId == 0 && confLoader.IsChannelTypeWithBindId(req.ChannelType)) {
		simpleInfo, err := c.channelClient.GetChannelSimpleInfo(ctx, req.OptUid, req.Cid)
		if err != nil {
			log.ErrorWithCtx(ctx, "channelClient.GetChannelSimpleInfo failed:%v, optId:%d, uid:%d, cid:%d", err, req.Opt, req.OptUid, req.Cid)
			return nil, err
		}
		req.ChannelType = simpleInfo.GetChannelType()
		req.Cid = simpleInfo.GetChannelId()
	}

	if req.SchemeDetailType == 0 || req.SchemeId == 0 {
		info, err := c.channelSchemeClient.GetCurChannelSchemeInfo(ctx, req.Cid, req.ChannelType)
		if err != nil {
			log.ErrorWithCtx(ctx, "channelSchemeClient.GetCurChannelSchemeInfo failed:%v, optId:%d, uid:%d, cid:%d", err, req.Opt, req.OptUid, req.Cid)
			return nil, err
		}
		req.SchemeId = info.GetSchemeInfo().GetSchemeId()
		req.SchemeDetailType = info.GetSchemeInfo().GetSchemeDetailType()
	}

	resp, err := c.channelOperatePermissionMgrClient.CheckOperatePermission(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "channelOperatePermissionMgrClient.CheckOperatePermission failed:%v, req:%v",
			err, utils.ToJson(req))
		return nil, err
	}

	checkPermissionResp := &CheckPermissionResp{
		HasPermission:     true,
		PermissionErrCode: resp.GetPermissionErrCode(),
		PermissionErrMsg:  resp.GetPermissionErrMsg(),
	}
	if checkPermissionResp.PermissionErrCode != 0 {
		checkPermissionResp.HasPermission = false

		if len(checkPermissionResp.PermissionErrMsg) == 0 {
			checkPermissionResp.PermissionErrMsg = status.MessageFromCode(int(checkPermissionResp.PermissionErrCode))
		}
	}

	log.DebugWithCtx(ctx, "CheckOperatePermission end , req:%v, resp:%v", utils.ToJson(req), utils.ToJson(checkPermissionResp))

	return checkPermissionResp, nil
}

func (c *Client) SimpleCheckOperatePermission(ctx context.Context, optUid, cid uint32, optId channel_operate_permission_mgr.ChannelOperate) (*CheckPermissionResp, error) {
	return c.CheckOperatePermission(ctx, &channel_operate_permission_mgr.CheckOperatePermissionReq{
		Opt:    optId,
		OptUid: optUid,
		Cid:    cid,
	})
}
