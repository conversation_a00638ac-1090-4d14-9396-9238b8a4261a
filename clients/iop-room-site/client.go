package iop_room_site

import (
	"context"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/iop-room-site"
	"google.golang.org/grpc"
)

func GetTarget(env string) string {
	tarGet := "dsp-iop-client-api.data-service.svc.cluster.local:8297"
	if env == "staging" {
		tarGet = "dsp-iop-client-api-test.data-service.svc.cluster.local:8297"
	}

	log.Infof("env:%s GetTarget: %s", env, tarGet)
	return tarGet
}

type Client struct {
	*grpc.ClientConn
}

/*
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewRoomSiteServiceClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.RoomSiteServiceClient {
	return c.Stub().(pb.RoomSiteServiceClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}
*/

func NewClient(ctx context.Context, env string) (*Client, error) {
	connI, err := grpc.DialContext(ctx, GetTarget(env), grpc.WithInsecure(), grpc.WithBlock())
	if err != nil {
		return nil, err

	}

	return &Client{
		ClientConn: connI,
	}, nil
}

func (c *Client) GetRecommendRoomByTagId(ctx context.Context, in *pb.GetRecommendRoomByTagIdReq) (*pb.GetRecommendRoomByTagIdResp, error) {
	resp := &pb.GetRecommendRoomByTagIdResp{}
	err := c.ClientConn.Invoke(ctx, "/com.quwan.dspiopother.proto.RoomSiteService/GetRecommendRoomBySiteId", in, resp)
	return resp, err
}
