// Code generated by protoc-gen-go-quicksilver. DO NOT EDIT.

package offer_room

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	protocol "golang.52tt.com/pkg/protocol"
	offer_room "golang.52tt.com/protocol/services/offer-room"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "offer-room"
)

// Client is the wrapper-client for OfferingRoom client.
type Client struct {
	client.BaseClient
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return offer_room.NewOfferingRoomClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of OfferingRoomClient.
func (c *Client) typedStub() offer_room.OfferingRoomClient {
	return c.Stub().(offer_room.OfferingRoomClient)
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// NewClientOrg creates a new Client with wrapper-client instance.
func NewClientOrg(dopts ...grpc.DialOption) (offer_room.OfferingRoomClient, error) {
	c, err := NewClient(dopts...)
	if err != nil {
		return nil, err
	}
	return c, nil
}

// GetApplyList
func (c *Client) GetApplyList(ctx context.Context, req *offer_room.GetApplyListRequest, opts ...grpc.CallOption) (*offer_room.GetApplyListResponse, error) {
	resp, err := c.typedStub().GetApplyList(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// AddAppliedUser
func (c *Client) AddAppliedUser(ctx context.Context, req *offer_room.AddAppliedUserRequest, opts ...grpc.CallOption) (*offer_room.AddAppliedUserResponse, error) {
	resp, err := c.typedStub().AddAppliedUser(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// DelAppliedUser
func (c *Client) DelAppliedUser(ctx context.Context, req *offer_room.DelAppliedUserRequest, opts ...grpc.CallOption) (*offer_room.DelAppliedUserResponse, error) {
	resp, err := c.typedStub().DelAppliedUser(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// OfferingRelationships
func (c *Client) OfferingRelationships(ctx context.Context, req *offer_room.OfferingRelationshipsRequest, opts ...grpc.CallOption) (*offer_room.OfferingRelationshipsResponse, error) {
	resp, err := c.typedStub().OfferingRelationships(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// DeleteRelationship
func (c *Client) DeleteRelationship(ctx context.Context, req *offer_room.DeleteRelationshipRequest, opts ...grpc.CallOption) (*offer_room.DeleteRelationshipResponse, error) {
	resp, err := c.typedStub().DeleteRelationship(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// SetOfferGeneralConfig
func (c *Client) SetOfferGeneralConfig(ctx context.Context, req *offer_room.SetOfferGeneralConfigRequest, opts ...grpc.CallOption) (*offer_room.SetOfferGeneralConfigResponse, error) {
	resp, err := c.typedStub().SetOfferGeneralConfig(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetOfferGeneralConfig
func (c *Client) GetOfferGeneralConfig(ctx context.Context, req *offer_room.GetOfferGeneralConfigRequest, opts ...grpc.CallOption) (*offer_room.GetOfferGeneralConfigResponse, error) {
	resp, err := c.typedStub().GetOfferGeneralConfig(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// InitOfferingGame
func (c *Client) InitOfferingGame(ctx context.Context, req *offer_room.InitOfferingGameRequest, opts ...grpc.CallOption) (*offer_room.InitOfferingGameResponse, error) {
	resp, err := c.typedStub().InitOfferingGame(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// SubmitOfferingSetting
func (c *Client) SubmitOfferingSetting(ctx context.Context, req *offer_room.SubmitOfferingSettingRequest, opts ...grpc.CallOption) (*offer_room.SubmitOfferingSettingResponse, error) {
	resp, err := c.typedStub().SubmitOfferingSetting(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// NamePrice
func (c *Client) NamePrice(ctx context.Context, req *offer_room.NamePriceRequest, opts ...grpc.CallOption) (*offer_room.NamePriceResponse, error) {
	resp, err := c.typedStub().NamePrice(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// SettleOfferingGame
func (c *Client) SettleOfferingGame(ctx context.Context, req *offer_room.SettleOfferingGameRequest, opts ...grpc.CallOption) (*offer_room.SettleOfferingGameResponse, error) {
	resp, err := c.typedStub().SettleOfferingGame(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// AbortOfferingGame
func (c *Client) AbortOfferingGame(ctx context.Context, req *offer_room.AbortOfferingGameRequest, opts ...grpc.CallOption) (*offer_room.AbortOfferingGameResponse, error) {
	resp, err := c.typedStub().AbortOfferingGame(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// EndOfferingGame
func (c *Client) EndOfferingGame(ctx context.Context, req *offer_room.EndOfferingGameRequest, opts ...grpc.CallOption) (*offer_room.EndOfferingGameResponse, error) {
	resp, err := c.typedStub().EndOfferingGame(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetCurOfferingGameInfo
func (c *Client) GetCurOfferingGameInfo(ctx context.Context, req *offer_room.GetCurOfferingGameInfoRequest, opts ...grpc.CallOption) (*offer_room.GetCurOfferingGameInfoResponse, error) {
	resp, err := c.typedStub().GetCurOfferingGameInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// OfferRoomCardInfo
func (c *Client) OfferRoomCardInfo(ctx context.Context, req *offer_room.OfferRoomCardInfoRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomCardInfoResponse, error) {
	resp, err := c.typedStub().OfferRoomCardInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetUserOfferNum
func (c *Client) GetUserOfferNum(ctx context.Context, req *offer_room.GetUserOfferNumRequest, opts ...grpc.CallOption) (*offer_room.GetUserOfferNumResponse, error) {
	resp, err := c.typedStub().GetUserOfferNum(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// CreateRelationship
func (c *Client) CreateRelationship(ctx context.Context, req *offer_room.CreateRelationshipRequest, opts ...grpc.CallOption) (*offer_room.CreateRelationshipResponse, error) {
	resp, err := c.typedStub().CreateRelationship(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// MergeNamePrice
func (c *Client) MergeNamePrice(ctx context.Context, req *offer_room.NamePriceRequest, opts ...grpc.CallOption) (*offer_room.NamePriceResponse, error) {
	resp, err := c.typedStub().MergeNamePrice(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// BatchGetUserConsumptionByGameRoundId
func (c *Client) BatchGetUserConsumptionByGameRoundId(ctx context.Context, req *offer_room.BatchGetUserConsumptionByGameRoundIdRequest, opts ...grpc.CallOption) (*offer_room.BatchGetUserConsumptionByGameRoundIdResponse, error) {
	resp, err := c.typedStub().BatchGetUserConsumptionByGameRoundId(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// CountOrderNumAndValueByTime
func (c *Client) CountOrderNumAndValueByTime(ctx context.Context, req *offer_room.TimeRangeReq, opts ...grpc.CallOption) (*offer_room.CountResp, error) {
	resp, err := c.typedStub().CountOrderNumAndValueByTime(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetOrderIdsByTime
func (c *Client) GetOrderIdsByTime(ctx context.Context, req *offer_room.TimeRangeReq, opts ...grpc.CallOption) (*offer_room.OrderIdsResp, error) {
	resp, err := c.typedStub().GetOrderIdsByTime(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// ReSendPresent
func (c *Client) ReSendPresent(ctx context.Context, req *offer_room.ReplaceOrderReq, opts ...grpc.CallOption) (*offer_room.EmptyResp, error) {
	resp, err := c.typedStub().ReSendPresent(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// DelUserRelationshipNotExistFlag
func (c *Client) DelUserRelationshipNotExistFlag(ctx context.Context, req *offer_room.DelUserRelationshipNotExistFlagReq, opts ...grpc.CallOption) (*offer_room.EmptyResp, error) {
	resp, err := c.typedStub().DelUserRelationshipNotExistFlag(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// RebuildRelationshipCache
func (c *Client) RebuildRelationshipCache(ctx context.Context, req *offer_room.RebuildRelationshipCacheReq, opts ...grpc.CallOption) (*offer_room.RebuildRelationshipCacheResp, error) {
	resp, err := c.typedStub().RebuildRelationshipCache(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetUserNameplateInfo
func (c *Client) GetUserNameplateInfo(ctx context.Context, req *offer_room.GetUserNameplateInfoReq, opts ...grpc.CallOption) (*offer_room.GetUserNameplateInfoResp, error) {
	resp, err := c.typedStub().GetUserNameplateInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// UpdateUserNameplateInfo
func (c *Client) UpdateUserNameplateInfo(ctx context.Context, req *offer_room.UpdateUserNameplateInfoReq, opts ...grpc.CallOption) (*offer_room.UpdateUserNameplateInfoResp, error) {
	resp, err := c.typedStub().UpdateUserNameplateInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetNameplateConfig
func (c *Client) GetNameplateConfig(ctx context.Context, req *offer_room.GetNameplateConfigReq, opts ...grpc.CallOption) (*offer_room.GetNameplateConfigResp, error) {
	resp, err := c.typedStub().GetNameplateConfig(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetLimitedRelationshipsByPage
func (c *Client) GetLimitedRelationshipsByPage(ctx context.Context, req *offer_room.GetLimitedRelationshipsByPageRequest, opts ...grpc.CallOption) (*offer_room.GetLimitedRelationshipsByPageResponse, error) {
	resp, err := c.typedStub().GetLimitedRelationshipsByPage(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// BatchAddLimitedRelationship
func (c *Client) BatchAddLimitedRelationship(ctx context.Context, req *offer_room.BatchAddLimitedRelationshipRequest, opts ...grpc.CallOption) (*offer_room.BatchAddLimitedRelationshipResponse, error) {
	resp, err := c.typedStub().BatchAddLimitedRelationship(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// BatchDelLimitedRelationship
func (c *Client) BatchDelLimitedRelationship(ctx context.Context, req *offer_room.BatchDelLimitedRelationshipRequest, opts ...grpc.CallOption) (*offer_room.BatchDelLimitedRelationshipResponse, error) {
	resp, err := c.typedStub().BatchDelLimitedRelationship(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// GetLimitedRelationshipDetail
func (c *Client) GetLimitedRelationshipDetail(ctx context.Context, req *offer_room.GetLimitedRelationshipDetailRequest, opts ...grpc.CallOption) (*offer_room.GetLimitedRelationshipDetailResponse, error) {
	resp, err := c.typedStub().GetLimitedRelationshipDetail(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// UpdateLimitedRelationship
func (c *Client) UpdateLimitedRelationship(ctx context.Context, req *offer_room.UpdateLimitedRelationshipRequest, opts ...grpc.CallOption) (*offer_room.UpdateLimitedRelationshipResponse, error) {
	resp, err := c.typedStub().UpdateLimitedRelationship(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// CheckRelationNameAvailability
func (c *Client) CheckRelationNameAvailability(ctx context.Context, req *offer_room.CheckRelationNameAvailabilityRequest, opts ...grpc.CallOption) (*offer_room.CheckRelationNameAvailabilityResponse, error) {
	resp, err := c.typedStub().CheckRelationNameAvailability(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}
