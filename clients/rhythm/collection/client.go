package rhythmcollection

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/rhythm/collection"

	"google.golang.org/grpc"
)

const (
	serviceName = "rhythm-collection"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewCollectionClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.CollectionClient { return c.Stub().(pb.CollectionClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetRhythmSwitch(ctx context.Context, uid, switchType, channelid uint32) (*pb.GetRhythmSwitchResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRhythmSwitch(ctx, &pb.GetRhythmSwitchReq{Uid: uid, SwitchType: switchType, ChannelId: channelid})
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetRhythmSwitch(ctx context.Context, uid, switchType, switchStatus uint32) protocol.ServerError {
	_, err := c.typedStub().SetRhythmSwitch(ctx, &pb.SetRhythmSwitchReq{Uid: uid, SwitchType: switchType, SwitchStatus: switchStatus})
	return protocol.ToServerError(err)
}

func (c *Client) GetStayAddTicketConfig(ctx context.Context, chid uint32) (*pb.GetStayAddTicketConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().GetStayAddTicketConfig(ctx, &pb.GetStayAddTicketConfigReq{ChannelId: chid})
	return resp, protocol.ToServerError(err)
}
func (c *Client) SetStayAddTicketConfig(ctx context.Context, req *pb.SetStayAddTicketConfigReq) protocol.ServerError {
	_, err := c.typedStub().SetStayAddTicketConfig(ctx, req)
	return protocol.ToServerError(err)
}
func (c *Client) ReportStayAddTicket(ctx context.Context, uid, chid, actid uint32) (*pb.ReportStayAddTicketResp, protocol.ServerError) {
	resp, err := c.typedStub().ReportStayAddTicket(ctx, &pb.ReportStayAddTicketReq{
		Uid:       uid,
		ChannelId: chid,
		ActId:     actid,
	})
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetUserForceTopic(ctx context.Context, uid, marketid uint32, tabName string) (*pb.GetUserForceTopicResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserForceTopic(ctx, &pb.GetUserForceTopicReq{
		Uid:      uid,
		MarketId: marketid,
		TabName:  tabName,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserExtraTicketInfo(ctx context.Context, uid, actid, channelID uint32) (*pb.GetUserExtraTicketInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserExtraTicketInfo(ctx, &pb.GetUserExtraTicketInfoReq{
		Uid:       uid,
		Actid:     actid,
		ChannelID: channelID,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetBattleStart(ctx context.Context, chid uint32, channelBattlePkInfo string) protocol.ServerError {
	_, err := c.typedStub().SetBattleStart(ctx, &pb.SetBattleStartRequest{
		ChannelId:           chid,
		ChannelBattlePkInfo: channelBattlePkInfo,
	})
	return protocol.ToServerError(err)
}

func (c *Client) CancelBattle(ctx context.Context, chid uint32) protocol.ServerError {
	_, err := c.typedStub().CancelBattle(ctx, &pb.CancelBattleRequest{
		ChannelId: chid,
	})
	return protocol.ToServerError(err)
}

func (c *Client) GetBattleStart(ctx context.Context, chid uint32) (*pb.GetBattleStartResponse, protocol.ServerError) {
	resp, err := c.typedStub().GetBattleStart(ctx, &pb.GetBattleStartRequest{
		ChannelId: chid,
	})
	return resp, protocol.ToServerError(err)
}
