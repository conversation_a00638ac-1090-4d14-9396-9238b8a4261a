// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/rhythm/mic (interfaces: IClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	protocol "golang.52tt.com/pkg/protocol"
	rhythm "golang.52tt.com/protocol/app/rhythm"
	mic "golang.52tt.com/protocol/services/rhythm/mic"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// DelMicSort mocks base method.
func (m *MockIClient) DelMicSort(arg0 context.Context, arg1 uint32) (*mic.DelMicSortResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMicSort", arg0, arg1)
	ret0, _ := ret[0].(*mic.DelMicSortResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelMicSort indicates an expected call of DelMicSort.
func (mr *MockIClientMockRecorder) DelMicSort(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMicSort", reflect.TypeOf((*MockIClient)(nil).DelMicSort), arg0, arg1)
}

// GetMicSort mocks base method.
func (m *MockIClient) GetMicSort(arg0 context.Context, arg1 uint32) (*mic.GetMicSortResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMicSort", arg0, arg1)
	ret0, _ := ret[0].(*mic.GetMicSortResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMicSort indicates an expected call of GetMicSort.
func (mr *MockIClientMockRecorder) GetMicSort(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMicSort", reflect.TypeOf((*MockIClient)(nil).GetMicSort), arg0, arg1)
}

// SetMicSort mocks base method.
func (m *MockIClient) SetMicSort(arg0 context.Context, arg1, arg2 uint32, arg3 *rhythm.MicSortInfo) (*mic.SetMicSortResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMicSort", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*mic.SetMicSortResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetMicSort indicates an expected call of SetMicSort.
func (mr *MockIClientMockRecorder) SetMicSort(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMicSort", reflect.TypeOf((*MockIClient)(nil).SetMicSort), arg0, arg1, arg2, arg3)
}
