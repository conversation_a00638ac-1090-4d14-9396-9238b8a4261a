package userdefinedvotepk

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/services/rhythm/userdefinedvotepk"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AddVotePkUserTicket(ctx context.Context, cnt, channelId, startTs uint32, uidlist []uint32, now, allExtraVotes, surplusVotes uint32, flag bool) protocol.ServerError
	GetUserDefinedVotePK(ctx context.Context, channelId, uid uint32) (out *userdefinedvotepk.GetUserDefinedVotePKResp, err error)
	HasUserDefinedVotePK(ctx context.Context, channelId uint32) (out *userdefinedvotepk.HasUserDefinedVotePKResp, err error)
	UserDefinedVote(ctx context.Context, req *userdefinedvotepk.UserDefinedVoteReq) (*userdefinedvotepk.UserDefinedVoteResp, protocol.ServerError)
	UserDefinedVotePKCancel(ctx context.Context, uid, channelId, startTs uint32) protocol.ServerError
	UserDefinedVotePKStart(ctx context.Context, channelId, uid, durationMin, pkType, voteCnt uint32, pkName string, optionList []string) (out *userdefinedvotepk.UserDefinedVotePKStartResp, err error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
