package topic_channel_logic

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/channel"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	CreateTopicChannel(ctx context.Context, in *topic_channel.CreateTopicChannelReq) (*topic_channel.CreateTopicChannelResp, protocol.ServerError)
	GetTopicChannelInfo(ctx context.Context, in *topic_channel.GetTopicChannelInfoReq) (*topic_channel.GetTopicChannelInfoResp, protocol.ServerError)
	KeepAliveTopicChannel(ctx context.Context, in *topic_channel.KeepAliveTopicChannelReq) (*topic_channel.KeepAliveTopicChannelResp, protocol.ServerError)
	GetRoomProxyTip(ctx context.Context, in *topic_channel.GetRoomProxyTipReq) (*topic_channel.GetRoomProxyTipResp, protocol.ServerError)
	ListRecommendTopicChannel(ctx context.Context, in *topic_channel.ListRecommendTopicChannelReq) (*topic_channel.ListRecommendTopicChannelResp, protocol.ServerError)
	TabTopicChannel(ctx context.Context, in *topic_channel.TabTopicChannelReq) (*topic_channel.TabTopicChannelResp, protocol.ServerError)
	GetTopicChannelRoomName(ctx context.Context, in *topic_channel.GetTopicChannelRoomNameReq) (*topic_channel.GetTopicChannelRoomNameResp, protocol.ServerError)
	GetChannelRoomNameConfig(ctx context.Context, in *topic_channel.GetChannelRoomNameConfigReq) (*topic_channel.GetChannelRoomNameConfigResp, error)
	GetBannerList(ctx context.Context, in *channel.GetChannelAdvReq) (*channel.GetChannelAdvResp, error)
	HideTopicChannel(ctx context.Context, in *topic_channel.HideTopicChannelReq) (*topic_channel.HideTopicChannelResp, error)
	CreateTopicChannelV2(ctx context.Context, in *topic_channel.CreateTopicChannelV2Req) (*topic_channel.CreateTopicChannelV2Resp, error)
	QuickFormTeam(ctx context.Context, in *topic_channel.QuickFormTeamReq) (*topic_channel.QuickFormTeamResp, error)
	ListTopicChannelV2(ctx context.Context, in *topic_channel.ListTopicChannelV2Req) (*topic_channel.ListTopicChannelV2Resp, error)
	GetChannelDialog(ctx context.Context, in *topic_channel.GetChannelDialogReq) (*topic_channel.GetChannelDialogResp, error)
	ListTabBlocks(ctx context.Context, in *topic_channel.ListTabBlocksReq) (*topic_channel.ListTabBlocksResp, error)
	GetFormTeamInfo(ctx context.Context, in *topic_channel.GetFormTeamInfoReq) (*topic_channel.GetFormTeamInfoResp, error)
	SwitchGamePlay(ctx context.Context, in *topic_channel.SwitchGamePlayReq) (*topic_channel.SwitchGamePlayResp, error)
	GetTabList(ctx context.Context, in *topic_channel.GetTabListReq) (*topic_channel.GetTabListResp, error)
	GetHomePageCardList(ctx context.Context, in *topic_channel.GetHomePageCardListReq) (*topic_channel.GetHomePageCardListResp, error)
	GetDialogV2(ctx context.Context, in *topic_channel.GetDialogV2Req) (*topic_channel.GetDialogV2Resp, error)
	QuickFormTeamV2(ctx context.Context, in *topic_channel.QuickFormTeamV2Req) (*topic_channel.QuickFormTeamV2Resp, error)
	ListTopicChannelV3(ctx context.Context, in *topic_channel.ListTopicChannelV3Req) (*topic_channel.ListTopicChannelV3Resp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
