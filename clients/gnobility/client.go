package gnobility

import (
	"context"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/gnobility"
	"google.golang.org/grpc"
)

const (
	serviceName = "gnobility"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewGNobilityClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.GNobilityClient { return c.Stub().(pb.GNobilityClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetNobilitySwitchFlag(ctx context.Context, uidList []uint32) (*pb.GetNobilitySwitchFlagResp, protocol.ServerError) {
	resp, err := c.typedStub().GetNobilitySwitchFlag(ctx, &pb.GetNobilitySwitchFlagReq{
		UidList: uidList,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddConsumeValue(ctx context.Context, uid, channelid, value uint32) (*pb.AddConsumeValueResp, protocol.ServerError) {
	resp, err := c.typedStub().AddConsumeValue(ctx, &pb.AddConsumeValueReq{
		Uid:       uid,
		ChannelId: channelid,
		Value:     value,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetNobilitySwitchFlag(ctx context.Context, req *pb.SetNobilitySwitchFlagReq) (*pb.SetNobilitySwitchFlagResp, protocol.ServerError) {
	resp, err := c.typedStub().SetNobilitySwitchFlag(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ModifyNobilityData(ctx context.Context, uid, value, waiVal, cycleTs uint32) (*pb.ModifyNobilityDataResp, protocol.ServerError) {
	resp, err := c.typedStub().ModifyNobilityData(ctx, &pb.ModifyNobilityDataReq{
		Uid:      uid,
		WaitCost: waiVal,
		Value:    value,
		CycleTs:  cycleTs,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetNobilityLevelCfg(ctx context.Context, in *pb.GetNobilityLevelCfgReq, opts ...grpc.CallOption) (*pb.GetNobilityLevelCfgResp, error) {
	resp, err := c.typedStub().GetNobilityLevelCfg(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelInvisibleFlag(ctx context.Context, in *pb.GetChannelInvisibleFlagReq) (*pb.GetChannelInvisibleFlagResp, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelInvisibleFlag(ctx, in)
	return resp, protocol.ToServerError(err)
}
