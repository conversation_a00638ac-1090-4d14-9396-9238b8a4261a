package channel_play_tab

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-play-tab"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-play-tab"
)

type Client struct {
	client.BaseClient
}

func newClient(opts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelPlayTabClient(cc)
			}, opts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelPlayTabClient { return c.Stub().(pb.ChannelPlayTabClient) }

func NewClient(opts ...grpc.DialOption) (*Client, error) {
	opts = append(opts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(opts...)
}

//key为tabId， value为白名单uid列表
func (c *Client) BatchGetWhiteUidListByTabIds(ctx context.Context, req *pb.BatchGetWhiteUidListByTabIdsReq) (map[uint32][]uint32, error) {
	listMap := make(map[uint32][]uint32)
	resp, err := c.typedStub().BatchGetWhiteUidListByTabIds(ctx, req)
	if err != nil {
		return listMap, err
	}
	if len(resp.GetTabListMap()) == 0 {
		return listMap, nil
	}
	for k, v := range resp.GetTabListMap() {
		if len(v.GetList()) == 0 {
			continue
		}
		listMap[k] = v.GetList()
	}
	return listMap, protocol.ToServerError(err)
}

func (c *Client) GetTabs(ctx context.Context, req *pb.GetTabsReq) (*pb.GetTabsResp, protocol.ServerError) {
	resp, err := c.typedStub().GetTabs(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetMinorSupervisionConfig(ctx context.Context, req *pb.BatchGetMinorSupervisionConfigReq) (*pb.BatchGetMinorSupervisionConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetMinorSupervisionConfig(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserTabsRealNameConfig(ctx context.Context, in *pb.GetUserTabsRealNameConfigReq) (*pb.GetUserTabsRealNameConfigResp, error) {
	resp, err := c.typedStub().GetUserTabsRealNameConfig(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetQuickMatchConfig(ctx context.Context, in *pb.GetQuickMatchConfigReq) (*pb.GetQuickMatchConfigResp, error) {
	resp, err := c.typedStub().GetQuickMatchConfig(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetNewQuickMatchConfig(ctx context.Context, in *pb.GetNewQuickMatchConfigReq) (*pb.GetNewQuickMatchConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().GetNewQuickMatchConfig(ctx, in)
	return resp, protocol.ToServerError(err)
}
