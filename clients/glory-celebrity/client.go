package glory_celebrity

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	protocol "golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/services/glory-celebrity"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
	"time"
)

const (
	serviceName = "glory-celebrity"
)

type Client struct {
	client.BaseClient
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return glory_celebrity.NewGloryCelebrityServiceClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of GloryCelebrityServiceClient.
func (c *Client) typedStub() glory_celebrity.GloryCelebrityServiceClient {
	return c.Stub().(glory_celebrity.GloryCelebrityServiceClient)
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// 获取名流周榜信息
func (c *Client) GetCelebrityWeekRank(ctx context.Context, uid uint32, t uint32, opts ...grpc.CallOption) (*glory_celebrity.GetCelebrityWeekRankRsp, protocol.ServerError) {
	req := &glory_celebrity.GetCelebrityWeekRankReq{
		Uid:  uid,
		Time: t,
	}
	resp, err := c.typedStub().GetCelebrityWeekRank(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// 获取上周名流周榜
func (c *Client) GetCelebrityLastWeekRank(ctx context.Context, opts ...grpc.CallOption) (*glory_celebrity.GetCelebrityWeekRankRsp, protocol.ServerError) {
	t := uint32(time.Now().AddDate(0, 0, -7).Unix())
	req := &glory_celebrity.GetCelebrityWeekRankReq{
		Uid:      1,
		Time:     t,
		OnlyRank: true,
	}
	resp, err := c.typedStub().GetCelebrityWeekRank(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// 获取名流殿堂-顶部信息(最强，最新)
func (c *Client) GetCelebrityPalaceTopInfo(ctx context.Context, isCurPeriods bool, uid uint32, opts ...grpc.CallOption) (*glory_celebrity.GetCelebrityPalaceTopInfoRsp, protocol.ServerError) {
	req := &glory_celebrity.GetCelebrityPalaceTopInfoReq{
		IsCurPeriods: isCurPeriods,
		Uid:          uid,
	}
	resp, err := c.typedStub().GetCelebrityPalaceTopInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// 获取名流殿堂-分页列表
func (c *Client) GetCelebrityPalaceInfoList(ctx context.Context, isCurPeriods bool, lastId uint32, limit uint32, opts ...grpc.CallOption) (*glory_celebrity.GetCelebrityPalaceInfoListRsp, protocol.ServerError) {
	req := &glory_celebrity.GetCelebrityPalaceInfoListReq{
		IsCurPeriods:    isCurPeriods,
		LastCelebrityId: lastId,
		Limit:           limit,
	}
	resp, err := c.typedStub().GetCelebrityPalaceInfoList(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// 名流殿堂-回顾
func (c *Client) ReplayCelebrityPalace(ctx context.Context, uid uint32, isCurPeriods bool, celebrityId uint32, opts ...grpc.CallOption) (*glory_celebrity.ReplayCelebrityPalaceRsp, protocol.ServerError) {
	req := &glory_celebrity.ReplayCelebrityPalaceReq{
		Uid:          uid,
		CelebrityId:  celebrityId,
		IsCurPeriods: isCurPeriods,
	}
	resp, err := c.typedStub().ReplayCelebrityPalace(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}
