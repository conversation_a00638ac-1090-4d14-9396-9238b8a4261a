package mijing_label

import (
	"context"

	pb "golang.52tt.com/protocol/services/mijing-label"

	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AddEscapeUserLabel(ctx context.Context, in *pb.AddEscapeUserLabelReq, opts ...grpc.CallOption) (*pb.AddEscapeUserLabelResp, error)
	GetEscapeUserLabelList(ctx context.Context, in *pb.GetEscapeUserLabelListReq, opts ...grpc.CallOption) (*pb.GetEscapeUserLabelListResp, error)
	GetEscapeUserLabelSurvey(ctx context.Context, in *pb.GetEscapeUserLabelSurveyReq, opts ...grpc.CallOption) (*pb.GetEscapeUserLabelSurveyResp, error)
	GetEscapeUserLabelConfig(ctx context.Context, in *pb.GetEscapeUserLabelConfigReq, opts ...grpc.CallOption) (*pb.GetEscapeUserLabelConfigResp, error)
	GetEscapeUserLabelDailyInviteeTimes(ctx context.Context, in *pb.GetEscapeUserLabelDailyInviteeTimesReq, opts ...grpc.CallOption) (*pb.GetEscapeUserLabelDailyInviteeTimesResp, error)
	CountEscapeUserLabelDailyInvitee(ctx context.Context, in *pb.CountEscapeUserLabelDailyInviteeReq, opts ...grpc.CallOption) (*pb.CountEscapeUserLabelDailyInviteeResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
