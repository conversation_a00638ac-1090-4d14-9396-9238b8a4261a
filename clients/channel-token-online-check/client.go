package channel_token_online_check

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc" //
	pb "golang.52tt.com/protocol/services/channel-token-online-check"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

const (
	serviceName = "channel-token-online-check"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewClientTo(target string, dopts ...grpc.DialOption) *Client {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewGrpcClientTo(
			target,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelTokenOnlineCheckClient(cc)
			}, dopts...),
	}
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewChannelTokenOnlineCheckClient(cc)
		}, dopts...),
	}
}

func (c *Client) typedStub() pb.ChannelTokenOnlineCheckClient {
	return c.Stub().(pb.ChannelTokenOnlineCheckClient)
}

// GetUserChannelOnlineInfo 不需要查询所在麦位，则micId填0
func (c *Client) GetUserChannelOnlineInfo(ctx context.Context, uid, cid, micId uint32) (*pb.GetUserChannelOnlineResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	r, err := c.typedStub().GetUserChannelOnlineInfo(ctx, &pb.GetUserChannelOnlineReq{
		Uid:   uid,
		Cid:   cid,
		MicId: micId,
	})
	return r, protocol.ToServerError(err)
}
