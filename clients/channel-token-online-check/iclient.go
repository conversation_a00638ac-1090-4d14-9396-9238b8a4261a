package channel_token_online_check

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-token-online-check"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetUserChannelOnlineInfo(ctx context.Context, uid, cid, micId uint32) (*pb.GetUserChannelOnlineResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
