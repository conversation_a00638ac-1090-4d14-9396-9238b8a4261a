package backpack_base

import (
	"golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/backpack-base"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	// 包裹、包裹物品配置的增、删、改、查
	AddPackageCfg(ctx context.Context, cfg *pb.PackageCfg, opts ...grpc.CallOption) (*pb.AddPackageCfgResp, protocol.ServerError)
	GetPackageCfg(ctx context.Context, req *pb.GetPackageCfgReq, opts ...grpc.CallOption) (*pb.GetPackageCfgResp, protocol.ServerError)
	GetPackageCfgV2(ctx context.Context, opts ...grpc.CallOption) (*pb.GetPackageCfgResp, protocol.ServerError)
	DelPackageCfg(ctx context.Context, bgId uint32, opts ...grpc.CallOption) (*pb.DelPackageCfgResp, protocol.ServerError)
	AddPackageItemCfg(ctx context.Context, cfg *pb.PackageItemCfg, opts ...grpc.CallOption) (*pb.AddPackageItemCfgResp, protocol.ServerError)
	GetPackageItemCfg(ctx context.Context, bgId uint32, opts ...grpc.CallOption) (*pb.GetPackageItemCfgResp, protocol.ServerError)
	GetPackageItemCfgV2(ctx context.Context, req *pb.GetPackageItemCfgReq, opts ...grpc.CallOption) (*pb.GetPackageItemCfgResp, protocol.ServerError)
	ModPackageItemCfg(ctx context.Context, cfg *pb.PackageItemCfg, opts ...grpc.CallOption) (*pb.ModPackageItemCfgResp, protocol.ServerError)
	DelPackageItemCfg(ctx context.Context, bgId uint32, bgItemId uint32, opts ...grpc.CallOption) (*pb.DelPackageItemCfgResp, protocol.ServerError)
	// 获取、添加背包物品展示权重配置
	GetItemWeightCfg(ctx context.Context, opts ...grpc.CallOption) (*pb.GetItemWeightCfgRsp, protocol.ServerError)
	//AddItemWeightCfg(ctx context.Context, in *AddItemWeightCfgReq, opts ...grpc.CallOption) (*pb.AddItemWeightCfgRsp, protocol.ServerError)
	// 碎片配置
	AddItemCfg(ctx context.Context, in *pb.AddItemCfgReq, opts ...grpc.CallOption) (*pb.AddItemCfgResp, protocol.ServerError)
	DelItemCfg(ctx context.Context, in *pb.DelItemCfgReq, opts ...grpc.CallOption) (*pb.DelItemCfgResp, protocol.ServerError)
	GetItemCfg(ctx context.Context, in *pb.GetItemCfgReq, opts ...grpc.CallOption) (*pb.GetItemCfgResp, protocol.ServerError)
	// 包裹发放协议
	GiveUserPackage(ctx context.Context, in *pb.GiveUserPackageReq, opts ...grpc.CallOption) (*pb.GiveUserPackageResp, protocol.ServerError)
	// 背包物品使用
	UseBackpackItem(ctx context.Context, in *pb.UseBackpackItemReq, opts ...grpc.CallOption) (*pb.UseBackpackItemResp, protocol.ServerError)
	// 获得背包物品列表
	GetUserBackpack(ctx context.Context, uid uint32, opts ...grpc.CallOption) (*pb.GetUserBackpackResp, protocol.ServerError)
	// 根据物品ID获取背包物品列表, 如果sourceId=0，则返回这一类的物品
	GetUserBackpackByItem(ctx context.Context, uid uint32, itemType, sourceId uint32, opts ...grpc.CallOption) (*pb.GetUserBackpackByItemResp, protocol.ServerError)
	// 根据物品ID获取背包物品列表和最后获取时间, 如果sourceId=0，则返回这一类的物品
	GetUserBackpackItemsAndLastObtainTs(ctx context.Context, uid uint32, itemType, sourceId uint32, opts ...grpc.CallOption) (*pb.GetUserBackpackByItemResp, protocol.ServerError)

	ProcBackpackItemTimeout(ctx context.Context, in *pb.ProcBackpackItemTimeoutReq, opts ...grpc.CallOption) (*pb.ProcBackpackItemTimeoutResp, protocol.ServerError)

	//二阶段消耗-仅碎片
	FreeZeItem(ctx context.Context, in *pb.FreeZeItemReq, opts ...grpc.CallOption) (*pb.FreeZeItemResp, protocol.ServerError)
	FreeZeItemV2(ctx context.Context, in *pb.FreeZeItemV2Req, opts ...grpc.CallOption) (*pb.FreeZeItemV2Resp, protocol.ServerError)
	//获取物品使用订单信息
	GetUseItemOrderInfo(ctx context.Context, orderId string, opts ...grpc.CallOption) (*pb.GetUseItemOrderInfoResp, protocol.ServerError)
	//获取物品使用记录
	GetUserBackpackLog(ctx context.Context, in *pb.GetUserBackpackLogReq, opts ...grpc.CallOption) (*pb.GetUserBackpackLogResp, protocol.ServerError)
	//获取用户某个物品数量
	GetUserPackageSum(ctx context.Context, uid uint32, itemType, itemId uint32, opts ...grpc.CallOption) (*pb.GetUserPackageSumResp, protocol.ServerError)
	//某个时间段的使用物品订单数和物品数量
	GetOrderCountByTimeRange(ctx context.Context, in *pb.GetOrderCountByTimeRangeReq, opts ...grpc.CallOption) (*pb.GetOrderCountByTimeRangeResp, protocol.ServerError)
	//某个时间段使用物品订单ID列表
	GetOrderListByTimeRange(ctx context.Context, in *pb.GetOrderListByTimeRangeReq, opts ...grpc.CallOption) (*pb.GetOrderListByTimeRangeResp, protocol.ServerError)
	// 合成物品
	ConversionItem(ctx context.Context, in *pb.ConversionItemReq, opts ...grpc.CallOption) (*pb.ConversionItemResp, protocol.ServerError)
	// 回滚物品
	RollBackUserItem(ctx context.Context, in *pb.RollBackUserItemReq, opts ...grpc.CallOption) (*pb.RollBackUserItemResp, protocol.ServerError)
	// 批量扣减
	BatchDeductUserItem(ctx context.Context, in *pb.BatchDeductUserItemReq, opts ...grpc.CallOption) (*pb.BatchDeductUserItemResp, protocol.ServerError)
	GetTimeRangeOrderData(ctx context.Context, in *pb.TimeRangeReq, opts ...grpc.CallOption) (*pb.CountResp, protocol.ServerError)
	GetTimeRangeUseOrderData(ctx context.Context, in *pb.TimeRangeReq, opts ...grpc.CallOption) (*pb.CountResp, protocol.ServerError)
	GetTimeRangeOrderList(ctx context.Context, in *pb.TimeRangeReq, opts ...grpc.CallOption) (*pb.OrderIdsResp, protocol.ServerError)

	//BatchUseBackpackItem 批量使用背包物品
	BatchUseBackpackItem(ctx context.Context, in *pb.BatchUseBackpackItemReq, opts ...grpc.CallOption) (*pb.BatchUseBackpackItemResp, protocol.ServerError)
	//CheckUseItemSuccByMainOrder 根据主订单ID检查物品是否使用成功
	CheckUseItemSuccByMainOrder(ctx context.Context, in *pb.CheckUseItemSuccByMainOrderReq, opts ...grpc.CallOption) (*pb.CheckUseItemSuccByMainOrderResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
