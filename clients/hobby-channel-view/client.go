package hobby_channel_view

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	hobby_channel "golang.52tt.com/protocol/services/hobby-channel"
	"google.golang.org/grpc"
)

const (
	serviceName = "hobby-channel"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return hobby_channel.NewHobbyChannelViewClient(cc)
		}, dopts...),
	}, nil
}

func NewClientTo(target string, dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewGrpcClientTo(
			target,
			func(cc *grpc.ClientConn) interface{} {
				return hobby_channel.NewHobbyChannelViewClient(cc)
			}, dopts...),
	}, nil
}

func (c *Client) typedStub() hobby_channel.HobbyChannelViewClient {
	return c.Stub().(hobby_channel.HobbyChannelViewClient)
}

func (c *Client) BatchGetView(ctx context.Context, in *hobby_channel.BatchGetViewReq) (*hobby_channel.BatchGetViewResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetView(ctx, in)

	if nil != resp && nil == resp.ViewMap {
		resp.ViewMap = map[uint32]*hobby_channel.HobbyChannelViewSpecial{}
	}

	return resp, protocol.ToServerError(err)
}
