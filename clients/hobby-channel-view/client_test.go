package hobby_channel_view

import (
	"context"
	"fmt"
	hobby_channel "golang.52tt.com/protocol/services/hobby-channel"
	"google.golang.org/grpc"
	"testing"
)

func TestClient_BatchGetView(t *testing.T) {
	client, err := NewClientTo("127.0.0.1:8080", grpc.WithBlock())
	if err != nil {
		t.Fail()
	}
	reqs := make(map[uint32]*hobby_channel.ViewReq, 0)
	// reqs[2013035] = &hobby_channel.ViewReq{
	// 	ViewType: 3,
	// 	Option: &hobby_channel.HobbyChannelViewOption{
	// 		Option: &hobby_channel.HobbyChannelViewOption_LeisureOption{LeisureOption: &hobby_channel.HobbyChannelViewLeisureOption{
	// 			SceneElemId: 30020,
	// 		}},
	// 	},
	// }
	reqs[2013035] = &hobby_channel.ViewReq{
		ViewType: 4,
		Option:   nil,
	}
	ret, err := client.BatchGetView(context.TODO(), &hobby_channel.BatchGetViewReq{
		Reqs: reqs,
	})
	fmt.Println(ret)
	fmt.Println(err)
}
