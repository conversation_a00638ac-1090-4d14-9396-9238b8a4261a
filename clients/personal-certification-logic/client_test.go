package personalcertificationlogic

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/app/personalcertificationlogic"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {

	<PERSON><PERSON>("GetPersonalCertificationLogic", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.PersonalCertificationLogicReq
		resp, err := client.GetPersonalCertificationLogic(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetPersonalCertificationLogic %+v", resp)
	})

}
