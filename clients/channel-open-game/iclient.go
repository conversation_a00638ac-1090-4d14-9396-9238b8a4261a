package channel_open_game

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-open-game"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetSupportGameList(ctx context.Context, in *pb.GetSupportGameListReq) (*pb.GetSupportGameListResp, protocol.ServerError)
	BatchGetGameInfo(ctx context.Context, in *pb.BatchGetGameInfoReq) (*pb.BatchGetGameInfoResp, error)
	SetChannelGame(ctx context.Context, in *pb.SetChannelGameReq) (*pb.SetChannelGameResp, protocol.ServerError)
	SetChannelGameSync(ctx context.Context, in *pb.SetChannelGameSyncReq) (*pb.SetChannelGameSyncResp, protocol.ServerError)
	SetChannelGameAsync(ctx context.Context, in *pb.SetChannelGameAsyncReq) (*pb.SetChannelGameAsyncResp, protocol.ServerError)
	UpdateChannelGame(ctx context.Context, in *pb.UpdateChannelGameReq) (*pb.UpdateChannelGameResp, protocol.ServerError)
	SetChannelGameMaster(ctx context.Context, in *pb.SetChannelGameMasterReq) (*pb.SetChannelGameMasterResp, protocol.ServerError)
	SetChannelGameStatus(ctx context.Context, in *pb.SetChannelGameStatusReq) (*pb.SetChannelGameStatusResp, protocol.ServerError)
	SetChannelGameMember(ctx context.Context, in *pb.SetChannelGameMemberReq) (*pb.SetChannelGameMemberResp, protocol.ServerError)
	GetChannelGameInfo(ctx context.Context, in *pb.GetChannelGameInfoReq) (*pb.GetChannelGameInfoResp, protocol.ServerError)
	GetGameModeInfo(ctx context.Context, in *pb.GetGameModeInfoReq) (*pb.GetGameModeInfoResp, protocol.ServerError)
	SetChannelGameInfo(ctx context.Context, in *pb.SetChannelGameInfoReq) (*pb.SetChannelGameInfoResp, protocol.ServerError)
	SetChannelGamePlayerOpenId(ctx context.Context, in *pb.SetChannelGamePlayerOpenIdReq) (*pb.SetChannelGamePlayerOpenIdResp, protocol.ServerError)
	GetChannelGameBaseInfo(ctx context.Context, in *pb.GetChannelGameBaseInfoReq) (*pb.GetChannelGameBaseInfoResp, protocol.ServerError)
	GetGameMaintain(ctx context.Context, in *pb.GetGameMaintainReq) (*pb.GetGameMaintainResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
