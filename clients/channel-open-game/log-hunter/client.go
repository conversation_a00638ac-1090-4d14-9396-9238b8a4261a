package log_hunter

import (
	"context"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-open-game/log-hunter"
	"google.golang.org/grpc"
)

const (
	serviceName = "log-hunter"
)

type Client struct {
	client.BaseClient
}

func NewClient(opts ...grpc.DialOption) (*Client, error) {
	opts = append(opts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewLogHunterClient(cc)
			}, opts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.LogHunterClient { return c.Stub().(pb.LogHunterClient) }

func (c *Client) Collect(ctx context.Context, in *pb.CollectReq) (*pb.CollectResp, protocol.ServerError) {
	resp, err := c.typedStub().Collect(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetConfig(ctx context.Context, in *pb.SetConfigReq) (*pb.SetConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().SetConfig(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetConfig(ctx context.Context, in *pb.GetConfigReq) (*pb.GetConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().GetConfig(ctx, in)
	return resp, protocol.ToServerError(err)
}