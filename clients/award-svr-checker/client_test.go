package awardsvrchecker

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/services/awardsvrchecker"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {

	<PERSON><PERSON>("GetAwardSvrChecker", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.AwardSvrCheckerReq
		resp, err := client.GetAwardSvrChecker(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetAwardSvrChecker %+v", resp)
	})

}
