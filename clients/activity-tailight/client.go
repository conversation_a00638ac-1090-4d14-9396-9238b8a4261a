package activity_tailight

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/activity-taillight"
	"google.golang.org/grpc"
)

const (
	serviceName = "activity-taillight"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewActivityTaillightClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ActivityTaillightClient {
	return c.Stub().(pb.ActivityTaillightClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func (c *Client) RewardTaillight(ctx context.Context, in *pb.RewardTaillightReq) (uint32, error) {
	resp, err := c.typedStub().RewardTaillight(ctx, in)
	if err != nil {
		log.Errorf("err: %v", err)
		return 0, protocol.ToServerError(err)
	}

	return resp.Interval, nil
}

func (c *Client) GetUserTaillight(ctx context.Context, uid, channelId uint32) ([]*pb.UserTaillight, error) {
	resp, err := c.typedStub().GetUserTaillight(ctx, &pb.GetUserTaillightReq{
		Uid:       uid,
		ChannelId: channelId,
	})
	if err != nil {
		log.Errorf("err: %v", err)
		return nil, protocol.ToServerError(err)
	}

	return resp.List, nil
}

func (c *Client) BatchGetUserTaillight(ctx context.Context, channelId uint32, uidList []uint32) (map[uint32]*pb.UserTaillightList, error) {
	resp, err := c.typedStub().BatchGetUserTaillight(ctx, &pb.BatchGetUserTaillightReq{
		UidList:   uidList,
		ChannelId: channelId,
	})
	if err != nil {
		log.Errorf("err: %v", err)
		return nil, protocol.ToServerError(err)
	}

	return resp.UserTaillightMap, nil
}

func (c *Client) GetUserFeature(ctx context.Context, in *pb.GetUserFeatureReq) (*pb.GetUserFeatureResp, error) {
	resp, err := c.typedStub().GetUserFeature(ctx, in)
	if err != nil {
		log.Errorf("err: %v", err)
		return nil, protocol.ToServerError(err)
	}

	return resp, nil
}

func (c *Client) RewardNameplate(ctx context.Context, in *pb.RewardNameplateReq, opts ...grpc.CallOption) (*pb.RewardNameplateResp, error) {
	resp, err := c.typedStub().RewardNameplate(ctx, in, opts...)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}

// 批量获取用户铭牌

func (c *Client) GetUserNameplate(ctx context.Context, in *pb.GetUserNameplateReq, opts ...grpc.CallOption) (*pb.GetUserNameplateResp, error) {
	resp, err := c.typedStub().GetUserNameplate(ctx, in, opts...)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}
