package channelbackgroundlogic

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	app_pb "golang.52tt.com/protocol/app/channelbackgroundlogic"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetCurChannelBackgroundInfo(ctx context.Context, req app_pb.GetCurChannelBackgroundInfoReq) (*app_pb.GetCurChannelBackgroundInfoResp, protocol.ServerError)
	ChangeCurChannelBackground(ctx context.Context, req app_pb.ChangeCurChannelBackgroundReq) (*app_pb.ChangeCurChannelBackgroundResp, protocol.ServerError)
	GetChannelBackgroundInfoList(ctx context.Context, req app_pb.GetChannelBackgroundInfoListReq) (*app_pb.GetChannelBackgroundInfoListResp, protocol.ServerError)
	CheckChannelBackgroundUpdate(ctx context.Context, req app_pb.CheckChannelBackgroundUpdateReq) (*app_pb.CheckChannelBackgroundUpdateResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
