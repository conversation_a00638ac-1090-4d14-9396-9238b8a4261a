package LBS

import (
	"context"
	"google.golang.org/grpc"
	"testing"
)

func Test_Client_GetADCodeByIP(t *testing.T) {
	// ip := "**************"
	ip := "&nbsp;"
	code, info, detail, err := NewClient(grpc.WithInsecure(), grpc.WithBlock(), grpc.WithAuthority("lbs.52tt.local")).GetADCodeByIP(context.Background(), 0, ip)
	if err != nil {
		t.Fatalf("Failed to GetADCodeByIP: %v", err)
	}
	t.Logf("GetADCodeByIP %s -> %d %s %s %s %s", ip, code, info, detail.GetProvince(), detail.GetCity(), detail.GetDistrict())
}
