package consistent

import (
	"math"
	"strconv"
	"testing"

	jump "github.com/renstrom/go-jump-consistent-hash"
)

func Test_Constent(t *testing.T) {

	hasher := jump.New(math.MaxInt32, jump.CRC32)
	_ = hasher.N()

	ch := New(100, hasher.Hash, map[string]int{
		"192.168.9.230:1111": 0,
		"192.168.9.230:2222": 0,
		"192.168.9.230:3333": 0,
		"192.168.9.230:4444": 0,
		"192.168.9.230:5555": 0,
	})

	cnt := make(map[string]int)
	for uid := 1; uid < 1000000; uid++ {
		p := ch.Get(strconv.Itoa(uid))
		cnt[p]++
	}

	for k, v := range cnt {
		t.Logf("%s\t%d", k, v)
	}
	t.Logf("%d", hasher.Hash("12345"))
}
