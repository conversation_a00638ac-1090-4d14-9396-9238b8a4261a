package logic_client

import (
	"bufio"
	"bytes"
	"encoding/binary"
	"errors"
	"io"
	"net"
	"os"
	"time"

	"gitlab.ttyuyin.com/golang/gudetama/log"
	"golang.52tt.com/pkg/protocol"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/services/logic-grpc-gateway/logicproto"
)

const (
	timeout = 30 * time.Second
)

type LogicClient struct {
	cfg *logicClientConfig
}

func NewLogicClient() (*LogicClient, error) {

	path := os.Getenv("LOGIC_CLIENT_CONFIG")
	if path == "" {
		path = "/home/<USER>/etc/client/logicsvr_cli.conf"
	}

	log.Debugf("LOGIC_CLIENT_CONFIG %s", path)

	cfg, err := newLogicClientConfig(path)
	if err != nil {
		log.Errorf("Failed to newLogicClientConfig %+v", err)
		return nil, err
	}

	return &LogicClient{cfg: cfg}, nil

}

func (s *LogicClient) ReloadConfig() error {
	return s.cfg.Reload()
}

func buildHeader(cmd, uid, msgBodyLen uint32) *logicproto.ServicePacketHeader {

	terminalType, _ := protocol.TerminalTypeFromClientType(protocol.ClientTypeRobot)

	header := &logicproto.ServicePacketHeader{
		Magic:             logicproto.ServicePacketMagic,
		HeaderVersion:     logicproto.ServicePacketVersion,
		HeaderLength:      uint8(logicproto.ServicePacketHeaderLength),
		CommandID:         cmd,
		Uid:               uid,
		CompressAlgorithm: logicproto.CompressAlgorithmNone,
		CompressVersion:   logicproto.CompressPackVersion1,
		CryptAlgorithm:    logicproto.CryptAlgorithmAESDecryptWithEncrypt,
		BodyLength:        msgBodyLen,
		CompressLength:    msgBodyLen,
		DeviceID:          [16]byte{},
		ClientType:        protocol.ClientTypeRobot,
		TerminalType:      terminalType,
		Return:            0,
		SessionKey:        [32]byte{byte('a')},
		IP:                0,
		SyncType:          0,
		Reserved:          [4]byte{},
	}

	return header
}

func (s *LogicClient) SendRequest(cmd, uid uint32, protoMsg proto.MessageV1) (*logicproto.ServicePacket, error) {

	msgBody, err := proto.Marshal(protoMsg)
	if err != nil {
		log.Errorf("Failed to  proto.Marshal %+v", err)
		return nil, err
	}

	addr := s.cfg.GetServiceAddr(uid, int(cmd))
	log.Debugf("GetServiceAddr cmd(%d) add4(%s)", cmd, addr)

	if addr == "" {
		return nil, errors.New("addr is null")
	}

	conn, err := net.DialTimeout("tcp", addr, timeout)
	if err != nil {
		log.Errorf("Failed to Dial addr(%s) err %+v", addr, err)
		return nil, err
	}

	defer conn.Close()

	err = conn.SetDeadline(time.Now().Add(timeout))
	if err != nil {
		log.Errorf("Failed to SetDeadline  err %+v", err)
		return nil, err
	}

	rw := bufio.NewReadWriter(bufio.NewReader(conn), bufio.NewWriter(conn))

	header := buildHeader(cmd, uid, uint32(len(msgBody)))

	//log.Debugf("header %+v", header)

	pack := &logicproto.ServicePacket{
		Header: header,
		Body:   msgBody,
	}

	err = pack.WriteTo(rw)
	if err != nil {
		log.Errorf("Filed to pack.WriteTo err %+v", err)
		return nil, err
	}
	rw.Flush()

	return readFrom(rw)

}

func readFrom(rw *bufio.ReadWriter) (*logicproto.ServicePacket, error) {
	lenBytes := make([]byte, 4)
	_, err := io.ReadFull(rw, lenBytes)
	if err != nil {
		if err == io.EOF {
			err = io.ErrUnexpectedEOF
		}
		log.Errorf("Failed Read  err %+v", err)
		return nil, err
	}

	length := binary.BigEndian.Uint32(lenBytes)
	log.Debugf("the length(%d)", length)

	headerBytes := make([]byte, logicproto.ServicePacketHeaderLength)

	_, err = io.ReadFull(rw, headerBytes)
	if err != nil {
		log.Errorf("Failed to Read  header err %+v", err)
		return nil, err
	}

	header := &logicproto.ServicePacketHeader{}
	err = header.ReadFrom(bytes.NewReader(headerBytes))
	if err != nil {
		if err == io.EOF {
			err = io.ErrUnexpectedEOF
		}
		log.Errorf("Failed ReadFrom  err %+v", err)
		return nil, err
	}

	//log.Debugf("header %+v", header)

	bodyBytes := make([]byte, header.BodyLength)

	if header.BodyLength > 0 {

		_, err = io.ReadFull(rw, bodyBytes)
		if err != nil {
			if err == io.EOF {
				err = io.ErrUnexpectedEOF
			}
			log.Errorf("Failed to Read Body  err %+v", err)
			return nil, err
		}

		//log.Debugf("Decrypt ====>%v %v", header.SessionKey[:16], header.CryptAlgorithm)
		//decrypt
		bodyBytes, err = logicproto.Decrypt(bodyBytes, header.SessionKey[:16], header.CryptAlgorithm)
		if err != nil {
			log.Errorf("Failed to Decrypt Body  err %+v", err)
			return nil, err
		}

		//Decompress
		bodyBytes, err = logicproto.Decompress(bodyBytes, header.CompressVersion, header.CompressAlgorithm)
		if err != nil {
			log.Errorf("Failed to Decompress Body  err %+v", err)
			return nil, err
		}
	}

	return &logicproto.ServicePacket{
		Header: header,
		Body:   bodyBytes,
	}, nil

}
