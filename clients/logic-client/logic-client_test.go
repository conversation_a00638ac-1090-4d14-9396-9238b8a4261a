package logic_client

import (
	"os"
	"testing"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	. "github.com/smartystreets/goconvey/convey"
	"gitlab.ttyuyin.com/golang/gudetama/log"
	ga_ugc "golang.52tt.com/protocol/app/ugc"
)

func init() {

	log.Init("logic-client", log.DebugLevel, log.UseDefaultLogShmConfigPath)

}

func TestSendRequest(t *testing.T) {

	Convey("TestSendPack", t, func() {
		req := &ga_ugc.GetTopicListReq{
			Page:  1,
			Count: 200,
		}

		lc, err := NewLogicClient()
		So(err, ShouldBeNil)

		pack, err := lc.SendRequest(2552, 500001, req)
		So(err, ShouldBeNil)

		t.Logf("%+v", pack.Header)

		resp := &ga_ugc.GetTopicListResp{}

		err = proto.Unmarshal(pack.Body, resp)
		So(err, ShouldBeNil)

		t.Log(resp)
	})

}

func TestLogicClientConfig_GetServiceAddr(t *testing.T) {

	Convey("Test get addr ", t, func() {

		path := os.Getenv("LOGIC_CLIENT_CONFIG")

		t.Log(path)

		cfg, err := newLogicClientConfig(path)
		So(err, ShouldBeNil)

		addr := cfg.GetServiceAddr(500001, 2552)

		t.Log(addr)
	})

}

func TestSendPresentReq(t *testing.T) {
	Convey("TestSendPresentReq", t, func() {
		lc, err := NewLogicClient()
		So(err, ShouldBeNil)
		err = lc.SendPresentReq(500001, 1982869, 1, 1)
		So(err, ShouldBeNil)
	})
}
