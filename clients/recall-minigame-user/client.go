/*
 * @Description:
 * @Date: 2022-01-10 12:12:31
 * @LastEditors: liang
 * @LastEditTime: 2022-01-10 12:16:29
 */
package recall_minigame_user

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/tracing"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/recall-minigame-user"
	"google.golang.org/grpc"
)

const (
	serviceName = "recall-minigame-user"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewRecallMinigameUserClient(cc)
			}, dopts...,
		),
	}, nil
}

// test
func (c *Client) typedStub() pb.RecallMinigameUserClient {
	return c.Stub().(pb.RecallMinigameUserClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func (c *Client) ReportMinigameEnter(ctx context.Context, in *pb.ReportMinigameEnterReq) (*pb.ReportMinigameEnterResp, protocol.ServerError) {
	resp, err := c.typedStub().ReportMinigameEnter(ctx, in)
	return resp, protocol.ToServerError(err)
}
