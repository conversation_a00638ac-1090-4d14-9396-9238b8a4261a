package recall_minigame_user

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/recall-minigame-user"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	ReportMinigameEnter(ctx context.Context, in *pb.ReportMinigameEnterReq) (*pb.ReportMinigameEnterResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
