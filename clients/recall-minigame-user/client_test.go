/*
 * @Description
 * @Date: 2022-01-10 12:12:31
 * @LastEditors: liang
 * @LastEditTime: 2022-01-10 14:02:56
 */
package recall_minigame_user

import (
	"context"
	"testing"

	pb "golang.52tt.com/protocol/services/recall-minigame-user"
	"google.golang.org/grpc"
)

func Test_Client_GetDuiLianDialog(t *testing.T) {
	cli, _ := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("recall-minigame-user.52tt.local"))
	user, err := cli.ReportMinigameEnter(context.Background(), &pb.ReportMinigameEnterReq{
		Uid: 10084,
	})
	if err != nil {
		t.<PERSON>("ReportMinigameEnter err: %v", err)
	}
	t.Logf("ReportMinigameEnter %v", user)
}
