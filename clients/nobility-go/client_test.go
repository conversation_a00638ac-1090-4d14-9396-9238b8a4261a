package nobilitygo

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/services/nobilitygo"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {

	<PERSON><PERSON>("GetNobilityGo", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.NobilityGoReq
		resp, err := client.GetNobilityGo(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetNobilityGo %+v", resp)
	})

}
