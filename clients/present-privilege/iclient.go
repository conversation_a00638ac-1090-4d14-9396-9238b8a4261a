// Code generated by quicksilver-cli. DO NOT EDIT.
package presentprivilege

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/presentprivilege"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AddTreasurePrivilege(ctx context.Context, req *pb.AddTreasurePrivilegeReq) (*pb.AddTreasurePrivilegeResp,protocol.ServerError)
	DelTreasurePrivilegeList(ctx context.Context, req *pb.DelTreasurePrivilegeListReq) (*pb.DelTreasurePrivilegeListResp,protocol.ServerError)
	GetConditionVal(ctx context.Context, req *pb.GetConditionValReq) (*pb.GetConditionValResp,protocol.ServerError)
	GetGetPrivilegeList(ctx context.Context) (*pb.GetPrivilegeListResp,protocol.ServerError)
	GetNowPrivilegePeople(ctx context.Context, req *pb.GetNowPrivilegePeopleReq) (*pb.GetNowPrivilegePeopleResp,protocol.ServerError)
	GetPresentPrivilege(ctx context.Context, uid uint32) (*pb.GetPresentPrivilegeResp,protocol.ServerError)
	GetTreasurePrivilege(ctx context.Context, req *pb.GetTreasurePrivilegeReq) (*pb.GetTreasurePrivilegeResp,protocol.ServerError)
	GetTreasurePrivilegeHistory(ctx context.Context, req *pb.GetTreasurePrivilegeHistoryReq) (*pb.GetTreasurePrivilegeHistoryResp,protocol.ServerError)
	GetTreasurePrivilegeList(ctx context.Context, req *pb.GetTreasurePrivilegeListReq) (*pb.GetTreasurePrivilegeListResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
