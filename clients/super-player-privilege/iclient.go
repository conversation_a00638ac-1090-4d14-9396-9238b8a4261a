// Code generated by quicksilver-cli. DO NOT EDIT.
package superplayerprivilege

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/super-player-privilege"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AddUserSpecialConcern(ctx context.Context, req *pb.AddUserSpecialConcernReq) (*pb.AddUserSpecialConcernResp,protocol.ServerError)
	BatchGetUserFellowVisibleProfile(ctx context.Context, uidList []uint32) (map[uint32]pb.FellowVisible,protocol.ServerError)
	BatchGetUserOnlineSwitch(ctx context.Context, req *pb.BatchGetUserOnlineSwitchReq) (*pb.BatchGetUserOnlineSwitchResp,protocol.ServerError)
	DelUserSpecialConcern(ctx context.Context, req *pb.DelUserSpecialConcernReq) (*pb.DelUserSpecialConcernResp,protocol.ServerError)
	GetIMPrivilegeCount(ctx context.Context, uid uint32) (*pb.GetIMPrivilegeCountResp,protocol.ServerError)
	GetPrivilegeTarget(ctx context.Context, uid, targetUid, priType uint32) (*pb.GetPrivilegeTargetResp,protocol.ServerError)
	GetUserBeSpecialConcern(ctx context.Context, req *pb.GetUserBeSpecialConcernReq) (*pb.GetUserBeSpecialConcernResp,protocol.ServerError)
	GetUserOnlineSwitch(ctx context.Context, uid uint32) (pb.OnlineSwitch,protocol.ServerError)
	GetUserSVIPPrivilegeProfile(ctx context.Context, uid uint32) (*pb.SVIPPrivilegeProfile,protocol.ServerError)
	GetUserSpecialConcern(ctx context.Context, req *pb.GetUserSpecialConcernReq) (*pb.GetUserSpecialConcernResp,protocol.ServerError)
	ReportSneakilyRead(ctx context.Context, req *pb.ReportSneakilyReadReq) (*pb.ReportSneakilyReadResp,protocol.ServerError)
	SetUserSVIPPrivilegeProfile(ctx context.Context, req *pb.SetUserSVIPPrivilegeProfileReq) (*pb.SVIPPrivilegeProfile,protocol.ServerError)
	UseIMPrivilege(ctx context.Context, uid, targetUid uint32) (*pb.UseIMPrivilegeResp,protocol.ServerError)
	UseSVIPStealthAhead(ctx context.Context, uid uint32) (*pb.UseSVIPStealthAheadResp,protocol.ServerError)
	ValidUserHasFellowVisible(ctx context.Context, req *pb.ValidUserHasFellowVisibleReq) (*pb.ValidUserHasFellowVisibleResp,protocol.ServerError)
	ValidUserHasSneakilyRead(ctx context.Context, req *pb.ValidUserHasSneakilyReadReq) (*pb.ValidUserHasSneakilyReadResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
