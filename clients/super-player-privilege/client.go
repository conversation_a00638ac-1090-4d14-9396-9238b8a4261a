package superplayerprivilege

import (
	"context"
	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/super-player-privilege"
	"google.golang.org/grpc"
)

const (
	serviceName = "super-player-privilege"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewSuperPlayerPrivilegeClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.SuperPlayerPrivilegeClient {
	return c.Stub().(pb.SuperPlayerPrivilegeClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetUserSpecialConcern(ctx context.Context, req *pb.GetUserSpecialConcernReq) (*pb.GetUserSpecialConcernResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserSpecialConcern(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserBeSpecialConcern(ctx context.Context, req *pb.GetUserBeSpecialConcernReq) (*pb.GetUserBeSpecialConcernResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserBeSpecialConcern(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddUserSpecialConcern(ctx context.Context, req *pb.AddUserSpecialConcernReq) (*pb.AddUserSpecialConcernResp, protocol.ServerError) {
	resp, err := c.typedStub().AddUserSpecialConcern(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DelUserSpecialConcern(ctx context.Context, req *pb.DelUserSpecialConcernReq) (*pb.DelUserSpecialConcernResp, protocol.ServerError) {
	resp, err := c.typedStub().DelUserSpecialConcern(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetIMPrivilegeCount(ctx context.Context, uid uint32) (*pb.GetIMPrivilegeCountResp, protocol.ServerError) {
	resp, err := c.typedStub().GetIMPrivilegeCount(ctx, &pb.GetIMPrivilegeCountReq{
		Uid: uid,
	})
	return resp, protocol.ToServerError(err)
}
func (c *Client) UseIMPrivilege(ctx context.Context, uid, targetUid uint32) (*pb.UseIMPrivilegeResp, protocol.ServerError) {
	resp, err := c.typedStub().UseIMPrivilege(ctx, &pb.UseIMPrivilegeReq{
		Uid:       uid,
		TargetUid: targetUid,
	})
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetPrivilegeTarget(ctx context.Context, uid, targetUid, priType uint32) (*pb.GetPrivilegeTargetResp, protocol.ServerError) {
	resp, err := c.typedStub().GetPrivilegeTarget(ctx, &pb.GetPrivilegeTargetReq{
		Uid:           uid,
		TargetUid:     targetUid,
		PrivilegeType: priType,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetUserFellowVisibleProfile(ctx context.Context, uidList []uint32) (map[uint32]pb.FellowVisible, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetUserFellowVisibleProfile(ctx, &pb.BatchGetFellowVisibleProfilesReq{
		UidList: uidList,
	})
	return resp.GetUidFellowVisibleMap(), protocol.ToServerError(err)
}

func (c *Client) SetUserSVIPPrivilegeProfile(ctx context.Context, req *pb.SetUserSVIPPrivilegeProfileReq) (*pb.SVIPPrivilegeProfile, protocol.ServerError) {
	resp, err := c.typedStub().SetUserSVIPPrivilegeProfile(ctx, req)
	return resp.GetPrivilegeProfile(), protocol.ToServerError(err)
}

func (c *Client) GetUserSVIPPrivilegeProfile(ctx context.Context, uid uint32) (*pb.SVIPPrivilegeProfile, protocol.ServerError) {
	resp, err := c.typedStub().GetUserSVIPPrivilegeProfile(ctx, &pb.GetUserSVIPPrivilegeProfileReq{
		Uid: uid,
	})
	return resp.GetPrivilegeProfile(), protocol.ToServerError(err)
}

func (c *Client) UseSVIPStealthAhead(ctx context.Context, uid uint32) (*pb.UseSVIPStealthAheadResp, protocol.ServerError) {
	resp, err := c.typedStub().UseSVIPStealthAhead(ctx, &pb.UseSVIPStealthAheadReq{
		Uid: uid,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserOnlineSwitch(ctx context.Context, uid uint32) (pb.OnlineSwitch, protocol.ServerError) {
	resp, err := c.typedStub().GetUserOnlineSwitch(ctx, &pb.GetUserOnlineSwitchReq{
		Uid: uid,
	})
	return resp.GetOnlineSwitch(), protocol.ToServerError(err)
}

func (c *Client) ReportSneakilyRead(ctx context.Context, req *pb.ReportSneakilyReadReq) (*pb.ReportSneakilyReadResp, protocol.ServerError) {
	resp, err := c.typedStub().ReportSneakilyRead(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ValidUserHasSneakilyRead(ctx context.Context, req *pb.ValidUserHasSneakilyReadReq) (*pb.ValidUserHasSneakilyReadResp, protocol.ServerError) {
	resp, err := c.typedStub().ValidUserHasSneakilyRead(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ValidUserHasFellowVisible(ctx context.Context, req *pb.ValidUserHasFellowVisibleReq) (*pb.ValidUserHasFellowVisibleResp, protocol.ServerError) {
	resp, err := c.typedStub().ValidUserHasFellowVisible(ctx, req)
	return resp, protocol.ToServerError(err)
}

// BatchGetUserOnlineSwitch 批量获取用户隐身状态
func (c *Client) BatchGetUserOnlineSwitch(ctx context.Context, req *pb.BatchGetUserOnlineSwitchReq) (*pb.BatchGetUserOnlineSwitchResp, protocol.ServerError) {
    resp, err := c.typedStub().BatchGetUserOnlineSwitch(ctx, req)
    return resp, protocol.ToServerError(err)
}