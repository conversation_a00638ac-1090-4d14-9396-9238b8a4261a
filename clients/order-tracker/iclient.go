package ordertracker

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/ordertracker"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetTracker(ctx context.Context, req pb.TrackerReq) (pb.TrackerResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
