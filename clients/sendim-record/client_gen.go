// Code generated by protoc-gen-go-quicksilver. DO NOT EDIT.

package sendim_record

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	protocol "golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/sendim/sendim-record"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "sendim-record"
)

// Client is the wrapper-client for SendImRecord client.
type Client struct {
	client.BaseClient
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewSendImRecordClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of SendImRecordClient.
func (c *Client) typedStub() pb.SendImRecordClient { return c.Stub().(pb.SendImRecordClient) }

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// AddSimplePushRecord
func (c *Client) AddSimplePushRecord(ctx context.Context, scene string, records []*pb.SimplePushRecord, opts ...grpc.CallOption) error {
	req := &pb.AddSimplePushRecordReq{
		Scene: scene,
		RecordList: records,
	}
	_, err := c.typedStub().AddSimplePushRecord(ctx, req, opts...)	
	return protocol.ToServerError(err)
}

// GetSimplePushRecord
func (c *Client) GetSimplePushRecord(ctx context.Context, req *pb.GetSimplePushRecordReq, opts ...grpc.CallOption) (*pb.GetSimplePushRecordResp, error) {
	resp, err := c.typedStub().GetSimplePushRecord(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}
