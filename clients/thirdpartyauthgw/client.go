package thirdpartyauthgw

import (
	"context"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/thirdpartyauthgw"
	"google.golang.org/grpc"
)

const (
	serviceName = "thirdpartyauthgw"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewThirdpartyAuthGwClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.ThirdpartyAuthGwClient {
	return c.Stub().(pb.ThirdpartyAuthGwClient)
}

func (c *Client) VerifyQQUserEx(ctx context.Context, accessToken string) (*pb.VerifyQQUserExResp, error) {
	resp, err := c.typedStub().VerifyQQUserEx(ctx, &pb.VerifyQQUserExReq{
		AccessToken: accessToken,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetQQUserInfo(ctx context.Context, appId, openId, accessToken string) (*pb.GetQQUserInfoResp, error) {
	resp, err := c.typedStub().GetQQUserInfo(ctx, &pb.GetQQUserInfoReq{
		Appid:       appId,
		Openid:      openId,
		AccessToken: accessToken,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetWeChatUserInfo(ctx context.Context, openId, accessToken string) (*pb.GetWeChatUserInfoResp, error) {
	resp, err := c.typedStub().GetWeChatUserInfo(ctx, &pb.GetWeChatUserInfoReq{
		Openid:      openId,
		AccessToken: accessToken,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) VerifyAppleUser(ctx context.Context, authCode, idToken, userId string) (*pb.VerifyAppleUserResp, error) {
	resp, err := c.typedStub().VerifyAppleUser(ctx, &pb.VerifyAppleUserReq{
		AuthCode: authCode,
		IdToken:  idToken,
		UserId:   userId,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetCMCCPhone(ctx context.Context, appId, token string) (*pb.GetCMCCPhoneResp, error) {
	resp, err := c.typedStub().GetCMCCPhone(ctx, &pb.GetCMCCPhoneReq{
		Appid: appId,
		Token: token,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetCUCCPhone(ctx context.Context, apiKey, accessCode, md5 string) (*pb.GetCUCCPhoneResp, error) {
	resp, err := c.typedStub().GetCUCCPhone(ctx, &pb.GetCUCCPhoneReq{
		Apikey:     apiKey,
		AccessCode: accessCode,
		Md5:        md5,
	})
	return resp, protocol.ToServerError(err)
}
