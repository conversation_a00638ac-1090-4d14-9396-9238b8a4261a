// Code generated by quicksilver-cli. DO NOT EDIT.
package tt_rev_channel_mode_mgr

import(
	"golang.52tt.com/pkg/client"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	tt_rev_channel_mode_mgr "golang.52tt.com/protocol/services/tt-rev-channel-mode-mgr"
)

type IClient interface {
	client.BaseClient
	BatchGetChannelModeEntry(ctx context.Context, req *tt_rev_channel_mode_mgr.BatchGetChannelModeEntryReq, opts ...grpc.CallOption) (*tt_rev_channel_mode_mgr.BatchGetChannelModeEntryResp,error)
	BatchOperateChannelMode(ctx context.Context, req *tt_rev_channel_mode_mgr.BatchOperateChannelModeReq, opts ...grpc.CallOption) (*tt_rev_channel_mode_mgr.BatchOperateChannelModeResp,error)
	BuyWerwolfItem(ctx context.Context, req *tt_rev_channel_mode_mgr.BuyWerwolfItemReq, opts ...grpc.CallOption) (*tt_rev_channel_mode_mgr.BuyWerwolfItemResp,error)
	GenFinancialFile(ctx context.Context, req *reconcile_v2.GenFinancialFileReq, opts ...grpc.CallOption) (*reconcile_v2.GenFinancialFileResp,error)
	GetChannelMode(ctx context.Context, req *tt_rev_channel_mode_mgr.GetChannelModeReq, opts ...grpc.CallOption) (*tt_rev_channel_mode_mgr.GetChannelModeResp,error)
	GetChannelOperator(ctx context.Context, req *tt_rev_channel_mode_mgr.GetChannelOperatorReq, opts ...grpc.CallOption) (*tt_rev_channel_mode_mgr.GetChannelOperatorResp,error)
	GetConsumeOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp,error)
	GetConsumeTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp,error)
	GetOrderLogByOrderIds(ctx context.Context, req *tt_rev_channel_mode_mgr.GetOrderLogByOrderIdsReq, opts ...grpc.CallOption) (*tt_rev_channel_mode_mgr.GetOrderLogByOrderIdsResp,error)
	SetChannelMode(ctx context.Context, req *tt_rev_channel_mode_mgr.SetChannelModeReq, opts ...grpc.CallOption) (*tt_rev_channel_mode_mgr.SetChannelModeResp,error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
