package telcalllogic

import (
	"context"
	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	"golang.52tt.com/protocol/app/telcalllogic"
	pb "golang.52tt.com/protocol/services/logicsvr-go/telcalllogic"
	"google.golang.org/grpc"
)

const (
	serviceName = "tel-call-logic"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewTelCallLogicClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.TelCallLogicClient { return c.Stub().(pb.TelCallLogicClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetCallGroup(ctx context.Context, req telcalllogic.GetCallGroupReq) (*telcalllogic.GetCallGroupResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCallGroup(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HandleCallGroup(ctx context.Context, req telcalllogic.HandleCallGroupReq) (*telcalllogic.HandleCallGroupResp, protocol.ServerError) {
	resp, err := c.typedStub().HandleCallGroup(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) JoinCallGroup(ctx context.Context, req telcalllogic.JoinCallGroupReq) (*telcalllogic.JoinCallGroupResp, protocol.ServerError) {
	resp, err := c.typedStub().JoinCallGroup(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetCallGroupUsers(ctx context.Context, req telcalllogic.GetCallGroupUsersReq) (*telcalllogic.GetCallGroupUsersResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCallGroupUsers(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSmsVerifyCode(ctx context.Context, req telcalllogic.GetSmsVerifyCodeReq) (*telcalllogic.GetSmsVerifyCodeResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSmsVerifyCode(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetCallUserStatus(ctx context.Context, req telcalllogic.GetCallUserStatusReq) (*telcalllogic.GetCallUserStatusResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCallUserStatus(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetCallGroupAllUsers(ctx context.Context, req telcalllogic.GetCallGroupAllUsersReq) (*telcalllogic.GetCallGroupAllUsersResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCallGroupAllUsers(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchRemoveGroupUser(ctx context.Context, req telcalllogic.BatchRemoveGroupUserReq) (*telcalllogic.BatchRemoveGroupUserResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchRemoveGroupUser(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetCallGroupUser(ctx context.Context, req telcalllogic.GetCallGroupUserReq) (*telcalllogic.GetCallGroupUserResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCallGroupUser(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CallStart(ctx context.Context, req telcalllogic.CallStartReq) (*telcalllogic.CallStartResp, protocol.ServerError) {
	resp, err := c.typedStub().CallStart(ctx, &req)
	return resp, protocol.ToServerError(err)
}