package antispam

import (
	"context"
	"google.golang.org/grpc"
	"testing"
)

func TestClient_TextCheck(t *testing.T) {
	client := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("antispamaccount.52tt.local"))
	resp, err := client.TextCheck(context.Background(), 500001, 111, "123", "aaaaaaaaaaaaaaa", "123123", 1, "asdfasdf")
	if err != nil {
		t.<PERSON><PERSON>r(err)
	}
	t.Log(resp)
}
