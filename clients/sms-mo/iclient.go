package sms_mo

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/sms/mo"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetProviderPhone(ctx context.Context, in *pb.GetProviderPhoneReq, opts ...grpc.CallOption) (*pb.GetProviderPhoneResp, error)
	GetSmsContent(ctx context.Context, in *pb.GetSmsContentReq, opts ...grpc.CallOption) (*pb.GetSmsContentResp, error)
	AddSmsContent(ctx context.Context, in *pb.AddSmsContentReq, opts ...grpc.CallOption) (*pb.AddSmsContentResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
