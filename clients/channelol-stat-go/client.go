package channelol_stat_go

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/channelol-stat-go"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"
)

const (
	serviceName = "channelol-stat-go"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelOlStatGoClient(cc)
			},
			dopts...,
		),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.ChannelOlStatGoClient {
	return c.Stub().(pb.ChannelOlStatGoClient)
}

// 获取当前全网在线房间列表
func (c *Client) GetChannelOLListByType(ctx context.Context, in *pb.GetChannelOLListByTypeReq) (*pb.GetChannelOLListByTypeResp, error) {
	resp, err := c.typedStub().GetChannelOLListByType(ctx, in)
	return resp, protocol.ToServerError(err)
}

// Get 房间在线人数统计
func (c *Client) GetChannelOLStatInfo(ctx context.Context, in *pb.GetChannelOLStatInfoReq) (*pb.GetChannelOLStatInfoRsp, error) {
	resp, err := c.typedStub().GetChannelOLStatInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

// 批量获取房间在线人数
func (c *Client) BatGetChannelMemberSize(ctx context.Context, in *pb.BatGetChannelMemberSizeReq) (*pb.BatGetChannelMemberSizeResp, error) {
	resp, err := c.typedStub().BatGetChannelMemberSize(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetChannelMemberSize(ctx context.Context, uin uint32, channelIds []uint32) (map[uint32]uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().BatGetChannelMemberSize(ctx, &pb.BatGetChannelMemberSizeReq{
		ChannelIdList: channelIds,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	var result = map[uint32]uint32{}
	for _, info := range r.GetChannelMemberSizeList() {
		result[info.GetChannelId()] = info.GetMemberCnt()
	}
	return result, nil
}

func (c *Client) GetChannelMemberSize(ctx context.Context, uin, channelId uint32) (uint32, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelOLStatInfo(ctx, &pb.GetChannelOLStatInfoReq{
		ChannelId: channelId,
	})
	if nil != err {
		return 0, protocol.ToServerError(err)
	}
	return resp.GetStatInfo().GetMemberCnt(), nil
}
