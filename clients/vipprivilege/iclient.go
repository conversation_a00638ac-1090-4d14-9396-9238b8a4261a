package vipprivilege

import (
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/vipPrivilegeSvr"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	RealStub() pb.VipPrivilegeSvrClient
	OnRichChange(ctx context.Context, oldRich uint32, newRich uint32) protocol.ServerError
	GetVipLevelConfigs(ctx context.Context) (*pb.GetVipLevelConfigsResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
