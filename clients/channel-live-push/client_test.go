package channellivepush

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/services/channellivepush"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {

	<PERSON><PERSON>("GetChannelLivePush", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.ChannelLivePushReq
		resp, err := client.GetChannelLivePush(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetChannelLivePush %+v", resp)
	})

}
