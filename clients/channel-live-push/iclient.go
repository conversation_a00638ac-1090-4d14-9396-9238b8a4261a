package channellivepush

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channellivepush"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	ChannelLiveReport(ctx context.Context, req *pb.ChannelLiveReportReq) (*pb.ChannelLiveReportResp, protocol.ServerError)
	GetChannelLivePushData(ctx context.Context, uid uint32) (*pb.GetChannelLivePushDataResp, protocol.ServerError)
	ClearUserData(ctx context.Context, uid uint32, all bool) (*pb.ClearUserDataResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
