package im_api

import (
	"context"

	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"

	pb "golang.52tt.com/protocol/services/im-api"
)

type IClient interface {
	client.BaseClient

	// 发送通用消息
	SendCommonMsg(ctx context.Context, req *pb.SendCommonMsgReq, opts ...grpc.CallOption) (*pb.SendCommonMsgResp, error)
	// 批量发送通用消息
	BatchSendCommonMsg(ctx context.Context, req *pb.BatchSendCommonMsgReq, opts ...grpc.CallOption) (*pb.BatchSendCommonMsgResp, error)

	// 发送TT助手消息
	SendTTAssistantText(ctx context.Context, req *pb.SendTTAssistantTextReq, opts ...grpc.CallOption) (*pb.SendTTAssistantTextResp, error)
	SimpleSendTTAssistantText(ctx context.Context, toUid uint32, content, highlight, url string, opts ...grpc.CallOption) (*pb.SendTTAssistantTextResp, error)

	// 发送私聊文本消息
	Send1V1Text(ctx context.Context, req *pb.Send1V1TextReq, opts ...grpc.CallOption) (*pb.Send1V1TextResp, error)
	SimpleSend1V1Text(ctx context.Context, fromUid, toUid uint32, content, highlight, url string, opts ...grpc.CallOption) (*pb.Send1V1TextResp, error)
	// 发送私聊扩展消息
	Send1V1ExtMsg(ctx context.Context, req *pb.Send1V1ExtMsgReq, opts ...grpc.CallOption) (*pb.Send1V1ExtMsgResp, error)

	// 发送公众号文本消息
	SendPublicAccountText(ctx context.Context, req *pb.SendPublicAccountTextReq, opts ...grpc.CallOption) (*pb.SendPublicAccountTextResp, error)
	// 发送公众号扩展消息
	SendPublicAccountExtMsg(ctx context.Context, req *pb.SendPublicAccountExtMsgReq, opts ...grpc.CallOption) (*pb.SendPublicAccountExtMsgResp, error)

	// 获取公众号最新消息
	GetPublicLatestSimpleMessage(ctx context.Context, req *pb.GetPublicLatestSimpleMessageReq, opts ...grpc.CallOption) (*pb.GetPublicLatestSimpleMessageResp, error)

	// 根据时间拉取（用户、群组、公众号）消息
	PullRecord(ctx context.Context, req *pb.PullRecordReq, opt ...grpc.CallOption) (*pb.PullRecordResp, error)

	// 根据seqId拉取（用户、群组、公众号）消息
	PullRecordBySeqId(ctx context.Context, req *pb.PullRecordBySeqIdReq, opt ...grpc.CallOption) (*pb.PullRecordResp, error)

	// 消息取消，包括消息删除和消息撤回
	CancelMsg(ctx context.Context, req *pb.CancelMsgRequest, opt ...grpc.CallOption) (*pb.CancelMsgResponse, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
