// Code generated by protoc-gen-go-quicksilver. DO NOT EDIT.

package mystery_place_push

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	protocol "golang.52tt.com/pkg/protocol"
	mystery_push "golang.52tt.com/protocol/services/mystery-place-push"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "mystery-place-push"
)

// Client is the wrapper-client for MysteryPush client.
type Client struct {
	client.BaseClient
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return mystery_push.NewMysteryPushClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of MysteryPushClient.
func (c *Client) typedStub() mystery_push.MysteryPushClient {
	return c.Stub().(mystery_push.MysteryPushClient)
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// NewClientOrg creates a new Client with wrapper-client instance.
func NewClientOrg(dopts ...grpc.DialOption) (mystery_push.MysteryPushClient, error) {
	c, err := NewClient(dopts...)
	if err != nil {
		return nil, err
	}
	return c, nil
}

// StartUserRecall
func (c *Client) StartUserRecall(ctx context.Context, req *mystery_push.StartUserRecallReq, opts ...grpc.CallOption) (*mystery_push.StartUserRecallResp, error) {
	resp, err := c.typedStub().StartUserRecall(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}
