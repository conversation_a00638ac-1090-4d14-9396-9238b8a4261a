package muse_rank

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/muse-rank"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	ListRank(ctx context.Context, req *pb.ListRankReq) (*pb.ListRankResp, protocol.ServerError)
	BatchAddScore(ctx context.Context, req *pb.BatchAddScoreReq) (*pb.BatchAddScoreResp, protocol.ServerError)
	BatchMemberRankInfo(ctx context.Context, req *pb.BatchMemberRankInfoReq) (*pb.BatchMemberRankInfoResp, protocol.ServerError)
	BatchMemberRankInfoByPosition(ctx context.Context, req *pb.BatchMemberRankInfoByPositionReq) (*pb.BatchMemberRankInfoByPositionResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
