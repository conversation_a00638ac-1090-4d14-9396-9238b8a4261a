package channelstats

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/services/channelstats"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {

	<PERSON><PERSON>("GetChannelStats", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.ChannelStatsReq
		resp, err := client.GetChannelStats(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetChannelStats %+v", resp)
	})

}
