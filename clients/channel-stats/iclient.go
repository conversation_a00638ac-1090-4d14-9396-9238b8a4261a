package channelstats

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetPresentHotValue(ctx context.Context, channelId uint32) (int64, protocol.ServerError)
	BatchGetPresentHotValue(ctx context.Context, channelIdList []uint32) (map[uint32]uint32, error)
	GetChannelHotValue(ctx context.Context, channelId, channelType uint32) (uint32, error)
	GetChannelHotValueAndFactory(ctx context.Context, channelId, channelType uint32) (uint32, uint32, error)
	BatchGetChannelHotValue(ctx context.Context, channelIdList []uint32) (map[uint32]int64, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
