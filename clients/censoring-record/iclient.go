package censoring_record

import (
	"context"

	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"

	pb "golang.52tt.com/protocol/services/censoring-record"
)

type IClient interface {
	client.BaseClient
	// LockAudit
	LockAudit(ctx context.Context, req *pb.LockAuditReq, opts ...grpc.CallOption) (*pb.LockAuditResp, error)
	// UnlockAudit
	UnlockAudit(ctx context.Context, req *pb.UnlockAuditReq, opts ...grpc.CallOption) (*pb.UnlockAuditResp, error)

	GetAuditEffectInfo(ctx context.Context, req *pb.GetAuditEffectInfoReq, opts ...grpc.CallOption) (*pb.GetAuditEffectInfoResp, error)
	SetAuditEffectInfo(ctx context.Context, req *pb.SetAuditEffectInfoReq, opts ...grpc.CallOption) (*pb.SetAuditEffectInfoResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
