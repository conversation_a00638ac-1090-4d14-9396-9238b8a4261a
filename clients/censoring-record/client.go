package censoring_record

import (
	"context"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/censoring-record"
	"google.golang.org/grpc"
)

const (
	serviceName = "censoring-record"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewCensoringRecordClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.CensoringRecordClient { return c.Stub().(pb.CensoringRecordClient) }

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

// LockAudit
func (c *Client) LockAudit(ctx context.Context, req *pb.LockAuditReq, opts ...grpc.CallOption) (*pb.LockAuditResp, error) {
	resp, err := c.typedStub().LockAudit(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// UnlockAudit
func (c *Client) UnlockAudit(ctx context.Context, req *pb.UnlockAuditReq, opts ...grpc.CallOption) (*pb.UnlockAuditResp, error) {
	resp, err := c.typedStub().UnlockAudit(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAuditEffectInfo(ctx context.Context, req *pb.GetAuditEffectInfoReq, opts ...grpc.CallOption) (*pb.GetAuditEffectInfoResp, error) {
	resp, err := c.typedStub().GetAuditEffectInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetAuditEffectInfo(ctx context.Context, req *pb.SetAuditEffectInfoReq, opts ...grpc.CallOption) (*pb.SetAuditEffectInfoResp, error) {
	resp, err := c.typedStub().SetAuditEffectInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}
