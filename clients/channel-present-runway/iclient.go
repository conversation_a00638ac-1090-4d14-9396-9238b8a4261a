package channel_present_runway

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-present-runway"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetUserRunwayInfo(ctx context.Context, opUid, userId uint32) (*pb.GetUserRunwayInfoResp, protocol.ServerError)
	GetChannelRunwayList(ctx context.Context, opUid, channelId uint32) (*pb.GetChannelRunwayListResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
