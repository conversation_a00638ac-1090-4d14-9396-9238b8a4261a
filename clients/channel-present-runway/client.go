package channel_present_runway

import (
    "context"
    grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
    "golang.52tt.com/pkg/client"
    grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/pkg/tracing"
    traceGRPC "golang.52tt.com/pkg/tracing/grpc"
    pb "golang.52tt.com/protocol/services/channel-present-runway"
    "google.golang.org/grpc"
    "google.golang.org/grpc/metadata"
    "strconv"
)

const (
    serviceName = "channel-present-runway"
)

type Client struct {
    client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
    return &Client{
        BaseClient: client.NewInsecureGRPCClient(
            serviceName,
            func(cc *grpc.ClientConn) interface{} {
                return pb.NewChannelPresentRunwayClient(cc)
            }, dopts...,
        ),
    }, nil
}

func (c *Client) typedStub() pb.ChannelPresentRunwayClient {
    return c.Stub().(pb.ChannelPresentRunwayClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
    unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
        tracing.UsingTracer(t), tracing.LogPayloads(true),
    ))
    dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
    return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
    dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
    return newClient(dopts...)
}

func (c *Client) GetUserRunwayInfo(ctx context.Context, opUid, userId uint32) (*pb.GetUserRunwayInfoResp, protocol.ServerError) {
    ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
    resp, err := c.typedStub().GetUserRunwayInfo(ctx, &pb.GetUserRunwayInfoReq{Uid: userId})
    return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelRunwayList(ctx context.Context, opUid, channelId uint32) (*pb.GetChannelRunwayListResp, protocol.ServerError) {
    ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
    resp, err := c.typedStub().GetChannelRunwayList(ctx, &pb.GetChannelRunwayListReq{ChannelId: channelId})
    return resp, protocol.ToServerError(err)
}
