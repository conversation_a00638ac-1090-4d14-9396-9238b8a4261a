package monkey

import (
	"context"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/monkey"
	"google.golang.org/grpc"
)

type IClient interface {
	SendMsgToChatGroup(ctx context.Context, req pb.SendMsgToChatGroupReq) (*pb.SendMsgToChatGroupRsp, protocol.ServerError)
	ReportMonitor(ctx context.Context, key, useropenid string, rate []float32) protocol.ServerError
	Close()
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
