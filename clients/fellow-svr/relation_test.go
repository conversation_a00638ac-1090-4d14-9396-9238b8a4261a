package fellowsvr

import (
	"context"
	pb "golang.52tt.com/protocol/services/fellow-svr"
	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
	"google.golang.org/grpc"
	"testing"
)

func TestClient_AddRelationship_Normal(t *testing.T) {
	ctx := context.Background()
	cli, _ := newClient(grpc.WithBlock())
	normalRelationship := &pb.Relationship{
		Name: "CP关系",
		Type: pb.RelationType_RELATION_TYPE_RARE,
		AnimationOfSettlement: &pb.Resource{
			Url:  "https://ga-album-cdnqn.52tt.com/web/fellow/20220409110329_69128280.zip",
			Type: pb.ResourceType_RESOURCE_TYPE_ZIP,
			Md5:  "fdde39a9a441233031a9daac123b6ea9",
			Name: "test.zip",
		},
		RelationshipBox: &pb.RelationshipBox{
			Background: &pb.Resource{
				Url:  "https://ga-album-cdnqn.52tt.com/web/fellow/20220407095549_86074039.png",
				Type: pb.ResourceType_RESOURCE_TYPE_IMG,
			},
			BigBackground: &pb.Resource{
				Url:  "https://ga-album-cdnqn.52tt.com/web/fellow/20220407095517_5633066.png",
				Type: pb.ResourceType_RESOURCE_TYPE_IMG,
			},
		},
		ConnectedStringForMic: &pb.ConnectedStringForMic{
			Left:  "https://ga-album-cdnqn.52tt.com/web/fellow/20220408190034_15814760.png",
			Right: "https://ga-album-cdnqn.52tt.com/web/fellow/20220408190034_15814760.png",
		},
		SpaceFlagUrl: "https://ga-album-cdnqn.52tt.com/web/fellow/20220407095658_96559176.png",
		CardFlagColor: &pb.GradientColor{
			StartHex: "#FFFAFA",
		},
		ImFlagUrl:             "https://ga-album-cdnqn.52tt.com/web/fellow/20220408154500_17390831.png",
		FriendSpaceBackground: "https://ga-album-cdnqn.52tt.com/web/fellow/20220407095658_96559176.png",
		MsgNotifyImg: &pb.MsgNotifyImg{
			Origin:    "https://ga-album-cdnqn.52tt.com/web/fellow/20220407095753_7598254.png",
			Thumbnail: "https://ga-album-cdnqn.52tt.com/web/fellow/20220407095753_7598254.png",
		},
		BizType: 1,
	}
	addRelationship, err := cli.AddRelationship(ctx, &pb.RelationshipAddReq{
		Relationship: normalRelationship,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("normalRelationship id: %d", addRelationship.Id)
}
func TestClient_AddRelationship_Com(t *testing.T) {
	ctx := context.Background()
	cli, _ := newClient(grpc.WithBlock())
	normalRelationship := &pb.Relationship{
		Name: "青梅竹马",
		Type: pb.RelationType_RELATION_TYPE_RARE,
		AnimationOfSettlement: &pb.Resource{
			Url:  "https://ga-album-cdnqn.52tt.com/web/fellow/20220409110329_69128280.zip",
			Type: pb.ResourceType_RESOURCE_TYPE_ZIP,
			Md5:  "fdde39a9a441233031a9daac123b6ea9",
			Name: "test.zip",
		},
		RelationshipBox: &pb.RelationshipBox{
			Background: &pb.Resource{
				Url:  "https://ga-album-cdnqn.52tt.com/web/fellow/20220407095549_86074039.png",
				Type: pb.ResourceType_RESOURCE_TYPE_IMG,
			},
			BigBackground: &pb.Resource{
				Url:  "https://ga-album-cdnqn.52tt.com/web/fellow/20220407095517_5633066.png",
				Type: pb.ResourceType_RESOURCE_TYPE_IMG,
			},
		},
		ConnectedStringForMic: &pb.ConnectedStringForMic{
			Left:  "https://ga-album-cdnqn.52tt.com/web/fellow/20220408190034_15814760.png",
			Right: "https://ga-album-cdnqn.52tt.com/web/fellow/20220408190034_15814760.png",
		},
		SpaceFlagUrl: "https://ga-album-cdnqn.52tt.com/web/fellow/20220407095658_96559176.png",
		CardFlagColor: &pb.GradientColor{
			StartHex: "#FFFAFA",
		},
		ImFlagUrl:             "https://ga-album-cdnqn.52tt.com/web/fellow/20220408154500_17390831.png",
		FriendSpaceBackground: "https://ga-album-cdnqn.52tt.com/web/fellow/20220407095658_96559176.png",
		MsgNotifyImg: &pb.MsgNotifyImg{
			Origin:    "https://ga-album-cdnqn.52tt.com/web/fellow/20220407095753_7598254.png",
			Thumbnail: "https://ga-album-cdnqn.52tt.com/web/fellow/20220407095753_7598254.png",
		},
		BizType: 2,
		SubRelation: []*pb.Relationship{
			{
				Name:      "青梅",
				Type:      pb.RelationType_RELATION_TYPE_RARE,
				ImFlagUrl: "https://ga-album-cdnqn.52tt.com/web/fellow/20220408154347_83551006.png",
			}, {
				Name:      "竹马",
				Type:      pb.RelationType_RELATION_TYPE_RARE,
				ImFlagUrl: "https://ga-album-cdnqn.52tt.com/web/fellow/20220408154408_22630409.png",
			},
		},
	}
	addRelationship, err := cli.AddRelationship(ctx, &pb.RelationshipAddReq{
		Relationship: normalRelationship,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("normalRelationship id: %d", addRelationship.Id)
}

func TestClient_UpdateRelationship(t *testing.T) {
	//cli, _ := newClient(grpc.WithBlock())
	//id := uint32(0)
	//ctx := context.Background()
	//relationship, err := cli.GetRelationship(ctx, &pb.RelationshipGetReq{
	//	Id: id,
	//})
	//

}

func TestClient_AddChannelRelationshipBinding(t *testing.T) {
	ctx := context.Background()
	cli, _ := newClient(grpc.WithBlock())
	channelAndRelationship := map[uint32][]uint32{}
	channelRelationshipBinding := &pb.ChannelRelationshipBinding{
		StartTime: 1649239020,
		EndTime:   1651226220,
		IntroUrl: &pb.ChannelRelationshipBinding_Introduction{
			TtUrl:            "https://www.baidu.com/",
			HappyGameIos:     "https://www.baidu.com/",
			HappyGameAndroid: "https://www.baidu.com/",
		},
		EntranceUrl: "https://www.baidu.com/",
	}
	for channelID, relationshipIdList := range channelAndRelationship {
		channelRelationshipBinding.ChannelId = channelID
		channelRelationshipBinding.RelationshipIdList = make([]*pb.ChannelRelationshipBinding_RelationshipSimpleInfo, len(relationshipIdList))
		for i, id := range relationshipIdList {
			channelRelationshipBinding.RelationshipIdList[i] = &pb.ChannelRelationshipBinding_RelationshipSimpleInfo{
				Id: id,
			}
		}
		_, err := cli.AddChannelRelationshipBinding(ctx, &pb.ChannelRelationshipBindingAddReq{
			ChannelRelationshipBinding: channelRelationshipBinding,
		})
		if err != nil {
			t.Fatal(err)
		}
	}

}
