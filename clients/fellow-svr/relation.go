package fellowsvr

import (
	"context"

	pb "golang.52tt.com/protocol/services/fellow-svr"
)

// 关系CRUD

//AddRelationship 添加关系
func (c *Client) AddRelationship(ctx context.Context, req *pb.RelationshipAddReq) (*pb.RelationshipAddResp, error) {
	return c.typedStub().AddRelationship(ctx, req)
}

//UpdateRelationship 更新关系
func (c *Client) UpdateRelationship(ctx context.Context, req *pb.RelationshipUpdateReq) (*pb.RelationshipUpdateResp, error) {
	return c.typedStub().UpdateRelationship(ctx, req)
}

//DelRelationship 删除关系
func (c *Client) DelRelationship(ctx context.Context, req *pb.RelationshipDeleteReq) (*pb.RelationshipDeleteResp, error) {
	return c.typedStub().DelRelationship(ctx, req)
}

//GetRelationshipList 获取关系列表
func (c *Client) GetRelationshipList(ctx context.Context, req *pb.RelationshipListReq) (*pb.RelationshipListResp, error) {
	return c.typedStub().GetRelationshipList(ctx, req)
}

//GetRelationship 获取关系
func (c *Client) GetRelationship(ctx context.Context, req *pb.RelationshipGetReq) (*pb.RelationshipGetResp, error) {
	return c.typedStub().GetRelationship(ctx, req)
}

// 房间绑定关系的CRUD

//GetChannelRelationshipBindingList 获取房间绑定关系列表
func (c *Client) GetChannelRelationshipBindingList(ctx context.Context, req *pb.ChannelRelationshipBindingListReq) (*pb.ChannelRelationshipBindingListResp, error) {
	return c.typedStub().GetChannelRelationshipBindingList(ctx, req)
}

//AddChannelRelationshipBinding 添加房间绑定关系
func (c *Client) AddChannelRelationshipBinding(ctx context.Context, req *pb.ChannelRelationshipBindingAddReq) (*pb.ChannelRelationshipBindingAddResp, error) {
	return c.typedStub().AddChannelRelationshipBinding(ctx, req)
}

//UpdateChannelRelationshipBinding 更新房间绑定关系
func (c *Client) UpdateChannelRelationshipBinding(ctx context.Context, req *pb.ChannelRelationshipBindingUpdateReq) (*pb.ChannelRelationshipBindingUpdateResp, error) {
	return c.typedStub().UpdateChannelRelationshipBinding(ctx, req)
}

//DelChannelRelationshipBinding 删除房间绑定关系
func (c *Client) DelChannelRelationshipBinding(ctx context.Context, req *pb.ChannelRelationshipBindingDeleteReq) (*pb.ChannelRelationshipBindingDeleteResp, error) {
	return c.typedStub().DelChannelRelationshipBinding(ctx, req)
}
