package fellowsvr

import (
	"context"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/fellow-svr"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	channel_cp_game "golang.52tt.com/protocol/services/channel-cp-game"
	"google.golang.org/grpc"
)

const (
	serviceName = "fellow-svr"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewFellowSvrClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.FellowSvrClient { return c.Stub().(pb.FellowSvrClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetFellowList(ctx context.Context, uid int64) (*pb.GetFellowListResp, protocol.ServerError) {
	req := &pb.GetFellowListReq{
		Uid: uid,
	}

	resp, err := c.typedStub().GetFellowList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFellowCandidateInfo(ctx context.Context, req *pb.GetFellowCandidateInfoReq) (*pb.GetFellowCandidateInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetFellowCandidateInfo(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFellowCandidateList(ctx context.Context, req *pb.GetFellowCandidateListReq) (*pb.GetFellowCandidateListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetFellowCandidateList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFellowInviteInfoById(ctx context.Context, req *pb.GetFellowInviteInfoByIdReq) (*pb.GetFellowInviteInfoByIdResp, protocol.ServerError) {
	resp, err := c.typedStub().GetFellowInviteInfoById(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFellowInviteList(ctx context.Context, req *pb.GetFellowInviteListReq) (*pb.GetFellowInviteListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetFellowInviteList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SendFellowInvite(ctx context.Context, req *pb.SendFellowInviteReq) (*pb.SendFellowInviteResp, protocol.ServerError) {
	resp, err := c.typedStub().SendFellowInvite(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HandleFellowInvite(ctx context.Context, req *pb.HandleFellowInviteReq) (*pb.HandleFellowInviteResp, protocol.ServerError) {
	resp, err := c.typedStub().HandleFellowInvite(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetWebFellowInfo(ctx context.Context, uid, fellowUid uint32) (*pb.GetWebFellowListResp, protocol.ServerError) {
	req := &pb.GetWebFellowListReq{
		Uid:       int64(uid),
		FellowUid: int64(fellowUid),
	}

	resp, err := c.typedStub().GetWebFellowInfo(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CheckFellowInvite(ctx context.Context, req *pb.CheckFellowInviteReq) (*pb.CheckFellowInviteResp, protocol.ServerError) {
	resp, err := c.typedStub().CheckFellowInvite(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFellowPoint(ctx context.Context, req *pb.GetFellowPointReq) (*pb.GetFellowPointResp, protocol.ServerError) {
	resp, err := c.typedStub().GetFellowPoint(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CancelFellowInvite(ctx context.Context, req *pb.CancelFellowInviteReq) (*pb.CancelFellowInviteResp, protocol.ServerError) {
	resp, err := c.typedStub().CancelFellowInvite(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddFellowPresentConfig(ctx context.Context, req *pb.AddFellowPresentConfigReq) (*pb.AddFellowPresentConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().AddFellowPresentConfig(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DelFellowPresentConfig(ctx context.Context, req *pb.DelFellowPresentConfigReq) (*pb.DelFellowPresentConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().DelFellowPresentConfig(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAllFellowPresentConfig(ctx context.Context, req *pb.GetAllFellowPresentConfigReq) (*pb.GetAllFellowPresentConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAllFellowPresentConfig(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFellowPresentConfigById(ctx context.Context, req *pb.GetFellowPresentConfigByIdReq) (*pb.GetFellowPresentConfigByIdResp, protocol.ServerError) {
	resp, err := c.typedStub().GetFellowPresentConfigById(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateFellowPresentConfig(ctx context.Context, req *pb.UpdateFellowPresentConfigReq) (*pb.UpdateFellowPresentConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateFellowPresentConfig(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UnlockFellowSite(ctx context.Context, req *pb.UnlockFellowSiteReq) (*pb.UnlockFellowSiteResp, protocol.ServerError) {
	resp, err := c.typedStub().UnlockFellowSite(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UnboundFellow(ctx context.Context, req *pb.UnboundFellowReq) (*pb.UnboundFellowResp, protocol.ServerError) {
	resp, err := c.typedStub().UnboundFellow(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CancelUnboundFellow(ctx context.Context, req *pb.CancelUnboundFellowReq) (*pb.CancelUnboundFellowResp, protocol.ServerError) {
	resp, err := c.typedStub().CancelUnboundFellow(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SendFellowPresent(ctx context.Context, req *pb.SendFellowPresentReq) (*pb.SendFellowPresentResp, protocol.ServerError) {
	resp, err := c.typedStub().SendFellowPresent(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFellowPresentDetail(ctx context.Context, req *pb.GetFellowPresentDetailReq) (*pb.GetFellowPresentDetailResp, protocol.ServerError) {
	resp, err := c.typedStub().GetFellowPresentDetail(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFellowType(ctx context.Context, uid, targetUid uint32) (*pb.GetFellowTypeResp, protocol.ServerError) {
	resp, err := c.typedStub().GetFellowType(ctx, &pb.GetFellowTypeReq{
		Uid:       uid,
		TargetUid: targetUid,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) DirectUnboundFellow(ctx context.Context, uid, targetUid uint32) (*pb.DirectUnboundFellowResp, protocol.ServerError) {
	resp, err := c.typedStub().DirectUnboundFellow(ctx, &pb.DirectUnboundFellowReq{
		Uid:       uid,
		TargetUid: targetUid,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRoomFellowList(ctx context.Context, req *pb.GetRoomFellowListReq) (*pb.GetRoomFellowListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRoomFellowList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ChannelSendFellowPresent(ctx context.Context, req *pb.ChannelSendFellowPresentReq) (*pb.ChannelSendFellowPresentResp, protocol.ServerError) {

	resp, err := c.typedStub().ChannelSendFellowPresent(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HandleChannelFellowInvite(ctx context.Context, req *pb.HandleChannelFellowInviteReq) (*pb.HandleChannelFellowInviteResp, protocol.ServerError) {

	resp, err := c.typedStub().HandleChannelFellowInvite(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SendChannelFellowInvite(ctx context.Context, req *pb.SendChannelFellowInviteReq) (*pb.SendChannelFellowInviteResp, protocol.ServerError) {

	resp, err := c.typedStub().SendChannelFellowInvite(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFellowInfoByUid(ctx context.Context, req *pb.GetFellowInfoByUidReq) (*pb.GetFellowInfoByUidResp, protocol.ServerError) {

	resp, err := c.typedStub().GetFellowInfoByUid(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelFellowCandidateInfo(ctx context.Context, req *pb.GetChannelFellowCandidateInfoReq) (*pb.GetChannelFellowCandidateInfoResp, protocol.ServerError) {

	resp, err := c.typedStub().GetChannelFellowCandidateInfo(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAllChannelFellowInvite(ctx context.Context, req *pb.GetAllChannelFellowInviteReq) (*pb.GetAllChannelFellowInviteResp, protocol.ServerError) {

	resp, err := c.typedStub().GetAllChannelFellowInvite(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetOnMicFellowList(ctx context.Context, req *pb.GetOnMicFellowListReq) (*pb.GetOnMicFellowListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetOnMicFellowList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSendInviteList(ctx context.Context, req *pb.GetSendInviteListReq) (*pb.GetSendInviteListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSendInviteList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRareConfig(ctx context.Context) (*pb.GetRareConfigResp, protocol.ServerError) {
	req := &pb.GetRareConfigReq{}
	resp, err := c.typedStub().GetRareConfig(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelRareConfig(ctx context.Context, channelId uint32) (*pb.GetChannelRareConfigResp, protocol.ServerError) {
	req := &pb.GetChannelRareConfigReq{
		ChannelId: channelId,
	}
	resp, err := c.typedStub().GetChannelRareConfig(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFromAllRelationshipByIds(ctx context.Context, ids []uint32) (*pb.GetFromAllRelationshipByIdsResp, protocol.ServerError) {
	req := &pb.GetFromAllRelationshipByIdsReq{
		Ids: ids,
	}

	resp, err := c.typedStub().GetFromAllRelationshipByIds(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRareTags(ctx context.Context, uid, toUid uint32) (*pb.GetRareTagsResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRareTags(ctx, &pb.GetRareTagsReq{
		Uid:   uid,
		ToUid: toUid,
	})

	return resp, protocol.ToServerError(err)
}

func (c *Client) SetBindRelation(ctx context.Context, uid, toUid, rareId, subRareId uint32, bindStatus bool) (*pb.SetBindRelationResp, protocol.ServerError) {
	req := &pb.SetBindRelationReq{
		Uid:       uid,
		ToUid:     toUid,
		RareId:    rareId,
		SubRareId: subRareId,
		Bind:      bindStatus,
	}
	resp, err := c.typedStub().SetBindRelation(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddRare(ctx context.Context, uid, toUid, rareId, gameId, channelId, addTime uint32, userRare []*channel_cp_game.ChoseRare) (*pb.AddRareResp, error) {
	req := &pb.AddRareReq{
		Uid:       uid,
		ToUid:     toUid,
		RareId:    rareId,
		GameId:    gameId,
		ChannelId: channelId,
		AddTime:   addTime,
	}
	if len(userRare) == 2 {
		userRareList := make([]*pb.ChoseRare, 2)
		for index, c := range userRare {
			choseRare := &pb.ChoseRare{
				Uid:       c.Uid,
				SubRareId: c.SubRareId,
			}
			userRareList[index] = choseRare
		}
		req.UserRare = userRareList
	}
	resp, err := c.typedStub().AddRare(ctx, req)
	return resp, err
}

func (c *Client) AddRare2(ctx context.Context, req *pb.AddRareReq) protocol.ServerError {
	_, err := c.typedStub().AddRare(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) GetRareList(ctx context.Context, uid, toUid uint32) (*pb.GetRareListResp, protocol.ServerError) {
	req := &pb.GetRareListReq{
		Uid:   uid,
		ToUid: toUid,
	}
	resp, err := c.typedStub().GetRareList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DelRare(ctx context.Context, uid, toUid, rareId, subRareId uint32) (*pb.DelRareResp, protocol.ServerError) {
	req := &pb.DelRareReq{
		Uid:       uid,
		ToUid:     toUid,
		RareId:    rareId,
		SubRareId: subRareId,
	}
	resp, err := c.typedStub().DelRare(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddFellowPointDelay(ctx context.Context, req *pb.AddFellowPointDelayReq) protocol.ServerError {
	_, err := c.typedStub().AddFellowPointDelay(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) ChangeFellowBindType(ctx context.Context, req *pb.ChangeFellowBindTypeReq) (*pb.ChangeFellowBindTypeResp, protocol.ServerError) {
	resp, err := c.typedStub().ChangeFellowBindType(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetHistoryFellowType(ctx context.Context, req *pb.GetHistoryFellowTypeReq) (*pb.GetHistoryFellowTypeResp, protocol.ServerError) {
	resp, err := c.typedStub().GetHistoryFellowType(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFellowSimpleInfo(ctx context.Context, req *pb.GetFellowSimpleInfoReq) (*pb.GetFellowSimpleInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetFellowSimpleInfo(ctx, req)
	return resp, protocol.ToServerError(err)
}
