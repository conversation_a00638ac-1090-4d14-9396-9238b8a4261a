package userBlackListLogic

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	"golang.52tt.com/protocol/app/user-black-list-logic"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestClient_GetUserBlackListLogic(t *testing.T) {

	Convey("GetUserBlackListLogic", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		req := user_black_list_logic.GetUserBlackListReq{}

		req.ActiveUid =  200

		resp, err := client.GetUserBlackListLogic(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetUserBlackListLogic %+v", resp.AccountList)
	})
}

func TestClient_AddUserBlackListLogic(t *testing.T) {

	<PERSON><PERSON>("AddUserBlackListLogic", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		req := user_black_list_logic.AddUserBlackListReq{}

		req.ActiveUid =  200
		req.PassiveUid = 20

		resp, err := client.AddUserBlackListLogic(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetUserBlackListLogic %+v", resp)
	})

}

func TestClient_DelUserBlackListLogic(t *testing.T) {

	Convey("DelUserBlackListLogic", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		req := user_black_list_logic.DelUserBlackListReq{}

		req.ActiveUid =  200
		req.PassiveUid = 20


		resp, err := client.DelUserBlackListLogic(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("DelUserBlackListLogic %+v", resp)
	})

}

func TestClient_CheckIsInBlackList(t *testing.T) {

	Convey("CheckIsInBlackListLogic", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		req := user_black_list_logic.CheckIsInBlackListReq{}

		req.ActiveUid =  200
		req.PassiveUid = 20

		resp, err := client.CheckIsInBlackList(context.Background(),req)
		So(err, ShouldBeNil)

		t.Logf("CheckIsInBlackListLogic %+v", resp.BIsIn)
	})

}
