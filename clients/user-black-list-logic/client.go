package userBlackListLogic

import (
	"context"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	"golang.52tt.com/protocol/app/user-black-list-logic"
	pb "golang.52tt.com/protocol/services/logicsvr-go/user-black-list-logic"
	"google.golang.org/grpc"
)

const (
	serviceName = "user-black-list-logic"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewUserBlackListLogicClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.UserBlackListLogicClient { return c.Stub().(pb.UserBlackListLogicClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetUserBlackListLogic(ctx context.Context, req user_black_list_logic.GetUserBlackListReq) (user_black_list_logic.GetUserBlackListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserBlackList(ctx, &req)
	return *resp, protocol.ToServerError(err)
}

func (c *Client) AddUserBlackListLogic(ctx context.Context, req user_black_list_logic.AddUserBlackListReq) (user_black_list_logic.AddUserBlackListResp, protocol.ServerError) {
	resp, err := c.typedStub().AddUserBlackList(ctx, &req)
	return *resp, protocol.ToServerError(err)
}

func (c *Client) DelUserBlackListLogic(ctx context.Context, req user_black_list_logic.DelUserBlackListReq) (user_black_list_logic.DelUserBlackListResp, protocol.ServerError) {
	resp, err := c.typedStub().DelUserBlackList(ctx, &req)
	return *resp, protocol.ToServerError(err)
}

func (c *Client) CheckIsInBlackList(ctx context.Context, req user_black_list_logic.CheckIsInBlackListReq) (user_black_list_logic.CheckIsInBlackListResp, protocol.ServerError) {
	resp, err := c.typedStub().CheckIsInBlackList(ctx, &req)
	return *resp, protocol.ToServerError(err)
}
