package userBlackListLogic

import (
	"context"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/user-black-list-logic"
	"google.golang.org/grpc"
)

type IClient interface {
	GetUserBlackListLogic(ctx context.Context, req user_black_list_logic.GetUserBlackListReq) (user_black_list_logic.GetUserBlackListResp, protocol.ServerError)
	AddUserBlackListLogic(ctx context.Context, req user_black_list_logic.AddUserBlackListReq) (user_black_list_logic.AddUserBlackListResp, protocol.ServerError)
	DelUserBlackListLogic(ctx context.Context, req user_black_list_logic.DelUserBlackListReq) (user_black_list_logic.DelUserBlackListResp, protocol.ServerError)
	CheckIsInBlackList(ctx context.Context, req user_black_list_logic.CheckIsInBlackListReq) (user_black_list_logic.CheckIsInBlackListResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
