package channel_open_game_logic

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/app/channel-open-game"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetChannelSupportGameList(ctx context.Context, req *pb.GetChannelSupportGameListReq) (*pb.GetChannelSupportGameListResp, protocol.ServerError)
	SetChannelLoadingGame(ctx context.Context, req *pb.SetChannelLoadingGameReq) (*pb.SetChannelLoadingGameResp, protocol.ServerError)
	GetChannelLoadingGame(ctx context.Context, req *pb.GetChannelLoadingGameReq) (*pb.GetChannelLoadingGameResp, protocol.ServerError)
	SubmitGameCenterCmd(ctx context.Context, req *pb.SubmitGameCenterCmdReq) (*pb.SubmitGameCenterCmdResp, protocol.ServerError)
	GetGameActivityReward(ctx context.Context, req *pb.GetGameActivityRewardReq) (*pb.GetGameActivityRewardResp, protocol.ServerError)
	ReportGameActivityShared(ctx context.Context, req *pb.ReportGameActivitySharedReq) (*pb.ReportGameActivitySharedResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
