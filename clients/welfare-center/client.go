package roommasterapprentice

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/welfarecenter"
	"google.golang.org/grpc"
)

const (
	serviceName = "welfare-center"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewWelfareCenterClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.WelfareCenterClient {
	return c.Stub().(pb.WelfareCenterClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

///////佣金相关

//DrawBalance 提现
func (c *Client) DrawBalance(ctx context.Context, in *pb.DrawBalanceReq) (*pb.DrawBalanceResp, protocol.ServerError) {
	resp, err := c.typedStub().DrawBalance(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}

//GetUserBalanceByUserID 获取余额
func (c *Client) GetUserBalanceByUserID(ctx context.Context, in *pb.GetUserBalanceByUserIDReq) (*pb.GetUserBalanceByUserIDResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserBalanceByUserID(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}

//GetOrderListByUserID 获取其订单列表
func (c *Client) GetOrderListByUserID(ctx context.Context, in *pb.GetOrderListByUserIDReq) (*pb.GetOrderListByUserIDResp, protocol.ServerError) {
	resp, err := c.typedStub().GetOrderListByUserID(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}

//GetOrderInfoByUserIDOrderID 获取订单信息
func (c *Client) GetOrderInfoByUserIDOrderID(ctx context.Context, in *pb.GetOrderInfoByUserIDOrderIDReq) (*pb.GetOrderInfoByUserIDOrderIDResp, protocol.ServerError) {
	resp, err := c.typedStub().GetOrderInfoByUserIDOrderID(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}

//BindWXUserPayInfo 绑定微信信息
func (c *Client) BindWXUserPayInfo(ctx context.Context, in *pb.BindWXUserPayInfoReq) (*pb.BindWXUserPayInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().BindWXUserPayInfo(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}

//GetBindingInfo 查询绑定信息
func (c *Client) GetBindingInfo(ctx context.Context, in *pb.GetBindingInfoReq) (*pb.GetBindingInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetBindingInfo(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}

//GetDrawLimitList 获取draw 列表
func (c *Client) GetDrawLimitList(ctx context.Context, in *pb.GetDrawLimitListReq) (*pb.GetDrawLimitListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetDrawLimitList(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}
