package PresentGoLogic

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	app_pb "golang.52tt.com/protocol/app/present-go-logic"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetUserActPresentArea(ctx context.Context, req app_pb.GetUserActPresentAreaReq) (*app_pb.GetUserActPresentAreaResp, protocol.ServerError)
	SendPresent(ctx context.Context, req *presentPB_.SendPresentReq) (*presentPB_.SendPresentResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
