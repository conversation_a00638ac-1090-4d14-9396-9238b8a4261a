package account_isolator

import (
	"context"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/account-isolator"
	"google.golang.org/grpc"
)

const (
	serviceName = "account-isolator"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewAccountIsolatorClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.AccountIsolatorClient { return c.Stub().(pb.AccountIsolatorClient) }

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

// GetIsolationSequence
func (c *Client) GetIsolationSequence(ctx context.Context, req *pb.GetIsolationSequenceReq, opts ...grpc.CallOption) (*pb.GetIsolationSequenceResp, error) {
	resp, err := c.typedStub().GetIsolationSequence(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// TryLockUidIsolation
func (c *Client) TryLockUidIsolation(ctx context.Context, req *pb.TryLockUidIsolationReq, opts ...grpc.CallOption) (*pb.TryLockUidIsolationResp, error) {
	resp, err := c.typedStub().TryLockUidIsolation(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// CheckUidIsolation
func (c *Client) CheckUidIsolation(ctx context.Context, req *pb.CheckUidIsolationReq, opts ...grpc.CallOption) (*pb.CheckUidIsolationResp, error) {
	resp, err := c.typedStub().CheckUidIsolation(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}
