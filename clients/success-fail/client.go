package successfail

import (
	"context"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/successfail"
	"google.golang.org/grpc"
)

const (
	serviceName = "success-fail"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewSuccessFailClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.SuccessFailClient { return c.Stub().(pb.SuccessFailClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) ReportSucc(ctx context.Context, req *pb.ReportSuccReq) protocol.ServerError {
	_, err := c.typedStub().ReportSucc(ctx, req)
	return protocol.ToServerError(err)
}
func (c *Client) RegisterSuccFail(ctx context.Context, str, openid string) (string, protocol.ServerError) {
	resp, err := c.typedStub().RegisterSuccFail(ctx, &pb.RegisterSuccFailReq{
		KeyStr:     str,
		UserOpenid: openid,
	})
	return resp.GetKey(), protocol.ToServerError(err)
}
func (c *Client) ChangeUserKey(ctx context.Context, str, openid string) protocol.ServerError {
	_, err := c.typedStub().ChangeUserKey(ctx, &pb.ChangeUserKeyReq{
		KeyStr:     str,
		UserOpenid: openid,
	})
	return protocol.ToServerError(err)
}
func (c *Client) DelSuccFailKey(ctx context.Context, key string) protocol.ServerError {
	_, err := c.typedStub().DelSuccFailKey(ctx, &pb.DelSuccFailKeyReq{
		Key: key,
	})
	return protocol.ToServerError(err)
}
func (c *Client) RegisterList(ctx context.Context) (map[string]string, protocol.ServerError) {
	resp, err := c.typedStub().RegisterList(ctx, &pb.RegisterListReq{})
	return resp.GetStrKeyMap(), protocol.ToServerError(err)
}
