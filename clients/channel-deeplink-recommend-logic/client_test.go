package channel_deeplink_recommend

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/app/channel-deeplink-recommend-logic"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestClient_GetRecommendChannelByRules(t *testing.T) {
	Convey("GetRecommendChannelByRules", t, func() {
		client, err := NewClient(grpc.WithBlock())
		So(err, ShouldBeNil)

		req := &pb.GetRecommendChannelByRulesReq{
			TagId:           1,
			SubTagIdBitmap:  0,
			SexMode:         0,
			DiversionMode:   3,
			BeginRank:       2,
			EndRank:         3,
			DivisionConfIdx: 1,
		}

		mapRes := make(map[uint32]uint32)
		for i := 0; i < 1; i++ {
			resp, err := client.GetRecommendChannelByRules(context.Background(), 0, req)
			t.Log(resp, err)
			mapRes[resp.GetChannelId()]++
		}

		t.Log(mapRes)
		So(err, ShouldBeNil)
	})
}
