package channel_deeplink_recommend

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/app/channel-deeplink-recommend-logic"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetRecommendChannelByRules(ctx context.Context, opUid uint32, in *pb.GetRecommendChannelByRulesReq) (*pb.GetRecommendChannelByRulesResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
