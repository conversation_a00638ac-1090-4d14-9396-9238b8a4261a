package music_topic_channel

import (
	"context"
	. "github.com/onsi/ginkgo"
	"github.com/onsi/ginkgo/reporters"
	. "github.com/onsi/gomega"
	pb "golang.52tt.com/protocol/services/music-topic-channel"
	"google.golang.org/grpc"
	"testing"
	"time"
)

func TestChannel(t *testing.T) {
	RegisterFailHandler(Fail)
	junitReporter := reporters.NewJUnitReporter("junit.xml")
	RunSpecsWithDefaultAndCustomReporters(t, "music-channel", []Reporter{junitReporter})
}

var _ = Describe("music-channel", func() {
	var client *Client
	// channel服务连接
	client, _ = NewClient(grpc.WithBlock(), grpc.WithAuthority("music-channel.52tt.local"))
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	var aChannelId uint32 = 10201953
	//var errChannelId uint32 = 111
	// 发布房间

	Context("发布房间", func() {
		_, err := client.SetMusicChannelReleaseInfo(ctx, &pb.SetMusicChannelReleaseInfoReq{
			MusicChannelReleaseInfo: &pb.MusicChannelReleaseInfo{
				Id:          aChannelId,
				TabId:       1,
				ReleaseTime: time.Now().Unix(),
				BlockOptions: []*pb.BlockOption{&pb.BlockOption{
					BlockId: 1,
					ElemId:  1,
				}},
				DisplayType: []pb.ChannelDisplayType{pb.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE, pb.ChannelDisplayType_DISPLAY_AT_FIND_FRIEND},
				ShowGeoInfo: false,
			},
			WantFresh: false,
			ReleaseIp: "",
		})
		It("发布房间接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})

		channelInfoResp, gerr := client.GetMusicChannelByIds(ctx, &pb.GetMusicChannelByIdsReq{
			Ids:       []uint32{aChannelId},
			Types:     nil,
			ReturnAll: false,
		})
		It("获取房间接口失败", func() {
			Expect(gerr).ShouldNot(HaveOccurred())
		})
		It("房间发布但未查询到房间发布信息", func() {
			Expect(isDisplayChannel(channelInfoResp.Info[0].DisplayType)).To(Equal(false))
		})

		_, err = client.DismissMusicChannel(ctx, &pb.DismissMusicChannelReq{ChannelId: aChannelId})
		It("取消发布房间接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})

		channelInfoResp, gerr = client.GetMusicChannelByIds(ctx, &pb.GetMusicChannelByIdsReq{
			Ids:       []uint32{aChannelId},
			Types:     nil,
			ReturnAll: false,
		})
		It("获取房间接口失败", func() {
			Expect(gerr).ShouldNot(HaveOccurred())
		})
		It("取消房间发布但仍查询到房间发布信息", func() {
			b := isDisplayChannel(channelInfoResp.Info[0].DisplayType)
			Expect(b).To(Equal(true))
		})
	})
})

func isDisplayChannel(displayType []pb.ChannelDisplayType) bool {
	for _, v := range displayType {
		if v == pb.ChannelDisplayType_DISMISSED {
			//仅展示发布的
			return true
		}
	}
	return false
}
