package music_topic_channel

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/music-topic-channel"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	InitMusicChannelReleaseInfo(ctx context.Context, in *pb.InitMusicChannelReleaseInfoReq) (out *pb.InitMusicChannelReleaseInfoResp, err error)
	SetMusicChannelReleaseInfo(ctx context.Context, in *pb.SetMusicChannelReleaseInfoReq) (out *pb.SetMusicChannelReleaseInfoResp, err error)
	DismissMusicChannel(ctx context.Context, in *pb.DismissMusicChannelReq) (out *pb.DismissMusicChannelResp, err error)
	GetMusicChannelList(ctx context.Context, in *pb.GetMusicChannelListReq) (out *pb.GetMusicChannelListResp, err error)
	GetMusicChannelByIds(ctx context.Context, in *pb.GetMusicChannelByIdsReq) (out *pb.GetMusicChannelByIdsResp, err error)
	GetChannelRoomUserNumber(ctx context.Context, in *pb.GetChannelRoomUserNumberReq) (out *pb.GetChannelRoomUserNumberResp, err error)
	AddTemporaryChannel(ctx context.Context, in *pb.AddTemporaryChannelReq) (out *pb.AddTemporaryChannelResp, err error)
	SwitchChannelTab(ctx context.Context, in *pb.SwitchChannelTabReq) (out *pb.SwitchChannelTabResp, err error)
	ListPublishingChannelIds(ctx context.Context, in *pb.ListPublishingChannelIdsReq) (out *pb.ListPublishingChannelIdsResp, err error)
	IsPublishing(ctx context.Context, in *pb.IsPublishingReq) (out *pb.IsPublishingResp, err error)
	GetMusicChannelFilterV2(ctx context.Context, in *pb.GetMusicChannelFilterV2Req) (out *pb.GetMusicChannelFilterV2Resp, err error)
	IsOlderForMusicHomePage(ctx context.Context, in *pb.IsOlderForMusicHomePageReq) (out *pb.IsOlderForMusicHomePageResp, err error)
	BatchIsHighQualityChannels(ctx context.Context, in *pb.BatchIsHighQualityChannelsReq) (out *pb.BatchIsHighQualityChannelsResp, err error)
	UpdateHighQualityChannels(ctx context.Context, in *pb.UpdateHighQualityChannelsReq) (out *pb.UpdateHighQualityChannelsResp, err error)
	SearchHighQualityChannels(ctx context.Context, in *pb.SearchHighQualityChannelsReq) (out *pb.SearchHighQualityChannelsResp, err error)
	BatchFilterIdsByGameCard(ctx context.Context, in *pb.BatchFilterIdsByGameCardReq) (out *pb.BatchFilterIdsByGameCardResp, err error)
	GetRcmdPgcChannel(ctx context.Context, in *pb.GetRcmdPgcChannelReq) (out *pb.GetRcmdPgcChannelResp, err error)
	SetUserSchoolLast(ctx context.Context, in *pb.SetUserSchoolLastReq) (out *pb.SetUserSchoolLastResp, err error)
	GetUserSchoolLast(ctx context.Context, in *pb.GetUserSchoolLastReq) (out *pb.GetUserSchoolLastResp, err error)

	ListMusicChannelViews(ctx context.Context, in *pb.ListMusicChannelViewsReq) (out *pb.ListMusicChannelViewsResp, err error)
	ListMusicChannelViewPbs(ctx context.Context, in *pb.ListMusicChannelViewPbsReq) (out *pb.ListMusicChannelViewPbsResp, err error)
	ListMusicChannelViewsForMusic(ctx context.Context, in *pb.ListMusicChannelViewsForMusicReq) (out *pb.ListMusicChannelViewsForMusicResp, err error)
	ListHomePageFilterItems(ctx context.Context, in *pb.ListHomePageFilterItemsReq) (out *pb.ListHomePageFilterItemsResp, err error)
	GetMusicFilterItemByIds(ctx context.Context, in *pb.GetMusicFilterItemByIdsReq) (out *pb.GetMusicFilterItemByIdsResp, err error)
	BatchIsPublishing(ctx context.Context, in *pb.BatchIsPublishingReq) (out *pb.BatchIsPublishingResp, err error)
	GetTabPublishHotRcmd(ctx context.Context, in *pb.GetTabPublishHotRcmdReq) (out *pb.GetTabPublishHotRcmdResp, err error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
