package verifycode

import (
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/verifycodesvr"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	CheckValid(ctx context.Context, uid, checkType uint32, session, code string) (*pb.CheckValidResp, protocol.ServerError)
	CheckVerifyCodeInUse(ctx context.Context, uid uint32) (bool, protocol.ServerError)
	CheckVerifyCodeStatus(ctx context.Context, uid, checkType uint32, session string) (*pb.CheckVerifyCodeStatusResp, protocol.ServerError)
	CreateVerifyCodeByKey(ctx context.Context, key string, len, ttl uint32) (string, protocol.ServerError)
	ValidateVerifyCode(ctx context.Context, key, code string) protocol.ServerError
	ValidateVerifyCodeV2(ctx context.Context, key, code string, delIfSuccess bool) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
