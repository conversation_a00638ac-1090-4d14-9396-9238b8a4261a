package channellistening

import (
	"context"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channellistening"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	LikeSong(ctx context.Context, req pb.LikeSongReq) (*pb.LikeSongResp, protocol.ServerError)
	GetBeLikedSongList(ctx context.Context, req pb.GetBeLikedSongListReq) (*pb.GetBeLikedSongListResp, protocol.ServerError)
	GetLikedSongList(ctx context.Context, req pb.GetLikedSongListReq) (*pb.GetLikedSongListResp, protocol.ServerError)
	GetChannelListeningSimpleInfo(ctx context.Context, req pb.GetChannelListeningSimpleInfoReq) (*pb.GetChannelListeningSimpleInfoResp, protocol.ServerError)
	GetUserFlowerList(ctx context.Context, req pb.GetUserFlowerListReq) (*pb.GetUserFlowerListResp, protocol.ServerError)
	GetFlowerDetail(ctx context.Context, req pb.GetFlowerDetailReq) (*pb.GetFlowerDetailResp, protocol.ServerError)
	SetListeningCheckInTopic(ctx context.Context, req pb.SetListeningCheckInTopicReq) (*pb.SetListeningCheckInTopicResp, protocol.ServerError)
	ListeningCheckIn(ctx context.Context, req pb.ListeningCheckInReq) (*pb.ListeningCheckInResp, protocol.ServerError)
	ListeningCheckInV2(ctx context.Context, req pb.ListeningCheckInReq) (*pb.ListeningCheckInResp, protocol.ServerError)
	ListeningCheckInShared(ctx context.Context, req pb.ListeningCheckInSharedReq) (*pb.ListeningCheckInSharedResp, protocol.ServerError)
	GetListeningUserCheckInInfoList(ctx context.Context, req pb.GetListeningUserCheckInInfoListReq) (*pb.GetListeningUserCheckInInfoListResp, protocol.ServerError)
	GetAllSongMenuForApp(ctx context.Context, req pb.GetAllSongMenuReq) (*pb.GetAllSongMenuResp, protocol.ServerError)
	GetSongByMenuId(ctx context.Context, req pb.GetSongByMenuIdReq) (*pb.GetSongByMenuIdResp, protocol.ServerError)
	GetSharedInfo(ctx context.Context, req pb.GetSharedInfoReq) (*pb.GetSharedInfoResp, protocol.ServerError)
	GetChannelListeningCheckInShareCard(ctx context.Context, req pb.GetChannelListeningCheckInShareCardReq) (*pb.GetChannelListeningCheckInShareCardResp, protocol.ServerError)
	GetAllMoodCfg(ctx context.Context, req pb.GetAllMoodCfgReq) (*pb.GetAllMoodCfgResp, protocol.ServerError)
	SetListeningUserMood(ctx context.Context, req pb.SetListeningUserMoodReq) (*pb.SetListeningUserMoodResp, protocol.ServerError)
	GetAllListeningOnMicUserMood(ctx context.Context, req pb.GetAllListeningOnMicUserMoodReq) (*pb.GetAllListeningOnMicUserMoodResp, protocol.ServerError)
	BatchGetCheckInInfo(ctx context.Context, req pb.BatchGetCheckInInfoReq) (*pb.BatchGetCheckInInfoResp, protocol.ServerError)
	GetUserFlower(ctx context.Context, req pb.GetUserFlowerReq) (*pb.GetUserFlowerResp, protocol.ServerError)
	ListeningCheckInV3(ctx context.Context, req pb.ListeningCheckInV3Req) (*pb.ListeningCheckInV3Resp, protocol.ServerError)
	ListeningCheckInSharedV3(ctx context.Context, req pb.ListeningCheckInSharedReq) (*pb.ListeningCheckInSharedResp, protocol.ServerError)
	GetListeningCheckInShareCardV3(ctx context.Context, req pb.GetListeningCheckInShareCardV3Req) (*pb.GetListeningCheckInShareCardV3Resp, protocol.ServerError)
	Constellation(ctx context.Context, req pb.ConstellationReq) (*pb.ConstellationResp, protocol.ServerError)
	ListeningDropCardV3(ctx context.Context, req pb.ListeningDropCardV3Req) (*pb.ListeningDropCardV3Resp, protocol.ServerError)
	BatchGetUserFlower(ctx context.Context, req pb.BatchGetUserFlowerReq) (*pb.BatchGetUserFlowerResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
