package channel_level

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-level"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetChannelInfo(ctx context.Context, channelId uint32) (*pb.GetChannelInfoResp, protocol.ServerError)
	BatchGetChannelInfo(ctx context.Context, channelIds []uint32) (map[uint32]*pb.GetChannelInfoResp, protocol.ServerError)
	GetSettings(ctx context.Context, settingsUpdateTime int64) (*pb.GetSettingsResp, protocol.ServerError)
	GetChannelInfoByWeb(ctx context.Context, channelId, uid uint32) (*pb.GetChannelInfoByWebResp, protocol.ServerError)
	GetChannelLevelId(ctx context.Context, channelId uint32) (uint32, protocol.ServerError)
	GetChannelAdminCountLimit(ctx context.Context, channelId uint32) (uint32, protocol.ServerError)
	AddChannelExp(ctx context.Context, channelId uint32, exp int32, seq, reason string) (bool, uint32, protocol.ServerError)
	UseChannelExpOrb(ctx context.Context, channelId uint32, seq string) (bool, uint32, protocol.ServerError)
	AddChannelExpOrb(ctx context.Context, channelId uint32, exp int32, seq, reason string, expireAt int64) (bool, uint32, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
