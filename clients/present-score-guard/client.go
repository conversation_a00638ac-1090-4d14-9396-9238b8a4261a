package present_score_guard

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/present-score-guard"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"
)

const (
	serviceName = "present-score-guard"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewPresentScoreGuardClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.PresentScoreGuardClient {
	return c.Stub().(pb.PresentScoreGuardClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) AddUserScore(ctx context.Context, uid, opUid uint32, amount int32, changeReason uint32,
	orderID, desc, dealToken string, ext []byte, timeValue uint32, scoreType uint32) (uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().AddUserScore(ctx, &pb.AddUserScoreReq{
		Uid:          uid,
		AddScore:     amount,
		OrderId:      orderID,
		OpUid:        opUid,
		ChangeReason: changeReason,
		OrderDesc:    desc,
		Extend:       ext,
		TimeValue:    timeValue,
		DealToken:    dealToken,
		ScoreType:    scoreType,
	})
	return r.GetFinalScore(), protocol.ToServerError(err)
}
