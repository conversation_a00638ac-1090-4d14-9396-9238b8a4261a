package channellisteningautoplay

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/channellisteningautoplay"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-listening-auto-play"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelListeningAutoPlayClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelListeningAutoPlayClient {
	return c.Stub().(pb.ChannelListeningAutoPlayClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) ChannelPlayStatus(ctx context.Context, req pb.ChannelPlayStatusReq) (*pb.ChannelPlayStatusResp, protocol.ServerError) {
	resp, err := c.typedStub().ChannelPlayStatus(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SwitchChannelPlay(ctx context.Context, req pb.SwitchChannelPlayReq) (*pb.SwitchChannelPlayResp, protocol.ServerError) {
	resp, err := c.typedStub().SwitchChannelPlay(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CutAutoModeSong(ctx context.Context, req pb.CutAutoModeSongReq) (*pb.CutAutoModeSongResp, protocol.ServerError) {
	resp, err := c.typedStub().CutAutoModeSong(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ReportChannelAutoSongProgress(ctx context.Context, req pb.ReportChannelAutoSongProgressReq) (*pb.ReportChannelAutoSongProgressResp, protocol.ServerError) {
	resp, err := c.typedStub().ReportChannelAutoSongProgress(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ChannelRcmdMusicMenu(ctx context.Context, req pb.ChannelRcmdMusicMenuReq) (*pb.ChannelRcmdMusicMenuResp, protocol.ServerError) {
	resp, err := c.typedStub().ChannelRcmdMusicMenu(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetVolume(ctx context.Context, req pb.SetVolumeReq) (*pb.SetVolumeResp, protocol.ServerError) {
	resp, err := c.typedStub().SetVolume(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelPlayMode(ctx context.Context, req pb.SetChannelPlayModeReq) (*pb.SetChannelPlayModeResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelPlayMode(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelPlayStatus(ctx context.Context, req pb.SetChannelPlayStatusReq) (*pb.SetChannelPlayStatusResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelPlayStatus(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchChannelPlayingMusic(ctx context.Context, req pb.BatchChannelPlayingMusicReq) (*pb.BatchChannelPlayingMusicResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchChannelPlayingMusic(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListeningChangePlayer(ctx context.Context, req pb.ListeningChangePlayerReq) (*pb.ListeningChangePlayerResp, protocol.ServerError) {
	resp, err := c.typedStub().ListeningChangePlayer(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListeningGetPlayerStatus(ctx context.Context, req pb.ListeningGetPlayerStatusReq) (*pb.ListeningGetPlayerStatusResp, protocol.ServerError) {
	resp, err := c.typedStub().ListeningGetPlayerStatus(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListeningSearchSongByKey(ctx context.Context, req pb.ListeningSearchSongByKeyReq) (*pb.ListeningSearchSongByKeyResp, protocol.ServerError) {
	resp, err := c.typedStub().ListeningSearchSongByKey(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListeningGetSongListByType(ctx context.Context, req pb.ListeningGetSongListByTypeReq) (*pb.ListeningGetSongListByTypeResp, protocol.ServerError) {
	resp, err := c.typedStub().ListeningGetSongListByType(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListeningGetSongListList(ctx context.Context, req pb.ListeningGetSongListListReq) (*pb.ListeningGetSongListListResp, protocol.ServerError) {
	resp, err := c.typedStub().ListeningGetSongListList(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListeningGetPlayerUserInfo(ctx context.Context, req pb.ListeningGetPlayerUserInfoReq) (*pb.ListeningGetPlayerUserInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().ListeningGetPlayerUserInfo(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListeningOrderSong(ctx context.Context, req pb.ListeningOrderSongReq) (*pb.ListeningOrderSongResp, protocol.ServerError) {
	resp, err := c.typedStub().ListeningOrderSong(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListeningGetPlayList(ctx context.Context, req pb.ListeningGetPlayListReq) (*pb.ListeningGetPlayListResp, protocol.ServerError) {
	resp, err := c.typedStub().ListeningGetPlayList(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListeningGetSongResource(ctx context.Context, req pb.ListeningGetSongResourceReq) (*pb.ListeningGetSongResourceResp, protocol.ServerError) {
	resp, err := c.typedStub().ListeningGetSongResource(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) WYYRedirectApi(ctx context.Context, req pb.WYYRedirectApiReq) (*pb.WYYRedirectApiResp, protocol.ServerError) {
	resp, err := c.typedStub().WYYRedirectApi(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListeningReportRecord(ctx context.Context, req pb.ListeningReportRecordReq) (*pb.ListeningReportRecordResp, protocol.ServerError) {
	resp, err := c.typedStub().ListeningReportRecord(ctx, &req)
	return resp, protocol.ToServerError(err)
}
