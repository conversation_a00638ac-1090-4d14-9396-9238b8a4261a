// Code generated by MockGen. DO NOT EDIT.
// Source: clients/channel-listening-auto-play/iclient.go

// Package channellisteningautoplay is a generated GoMock package.
package channellisteningautoplay

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channellisteningautoplay "golang.52tt.com/protocol/services/channellisteningautoplay"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchChannelPlayingMusic mocks base method.
func (m *MockIClient) BatchChannelPlayingMusic(ctx context.Context, req channellisteningautoplay.BatchChannelPlayingMusicReq) (*channellisteningautoplay.BatchChannelPlayingMusicResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchChannelPlayingMusic", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.BatchChannelPlayingMusicResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchChannelPlayingMusic indicates an expected call of BatchChannelPlayingMusic.
func (mr *MockIClientMockRecorder) BatchChannelPlayingMusic(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchChannelPlayingMusic", reflect.TypeOf((*MockIClient)(nil).BatchChannelPlayingMusic), ctx, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// ChannelPlayStatus mocks base method.
func (m *MockIClient) ChannelPlayStatus(ctx context.Context, req channellisteningautoplay.ChannelPlayStatusReq) (*channellisteningautoplay.ChannelPlayStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelPlayStatus", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.ChannelPlayStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ChannelPlayStatus indicates an expected call of ChannelPlayStatus.
func (mr *MockIClientMockRecorder) ChannelPlayStatus(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelPlayStatus", reflect.TypeOf((*MockIClient)(nil).ChannelPlayStatus), ctx, req)
}

// ChannelRcmdMusicMenu mocks base method.
func (m *MockIClient) ChannelRcmdMusicMenu(ctx context.Context, req channellisteningautoplay.ChannelRcmdMusicMenuReq) (*channellisteningautoplay.ChannelRcmdMusicMenuResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelRcmdMusicMenu", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.ChannelRcmdMusicMenuResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ChannelRcmdMusicMenu indicates an expected call of ChannelRcmdMusicMenu.
func (mr *MockIClientMockRecorder) ChannelRcmdMusicMenu(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelRcmdMusicMenu", reflect.TypeOf((*MockIClient)(nil).ChannelRcmdMusicMenu), ctx, req)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// CutAutoModeSong mocks base method.
func (m *MockIClient) CutAutoModeSong(ctx context.Context, req channellisteningautoplay.CutAutoModeSongReq) (*channellisteningautoplay.CutAutoModeSongResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CutAutoModeSong", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.CutAutoModeSongResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CutAutoModeSong indicates an expected call of CutAutoModeSong.
func (mr *MockIClientMockRecorder) CutAutoModeSong(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CutAutoModeSong", reflect.TypeOf((*MockIClient)(nil).CutAutoModeSong), ctx, req)
}

// ListeningChangePlayer mocks base method.
func (m *MockIClient) ListeningChangePlayer(ctx context.Context, req channellisteningautoplay.ListeningChangePlayerReq) (*channellisteningautoplay.ListeningChangePlayerResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListeningChangePlayer", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.ListeningChangePlayerResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListeningChangePlayer indicates an expected call of ListeningChangePlayer.
func (mr *MockIClientMockRecorder) ListeningChangePlayer(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListeningChangePlayer", reflect.TypeOf((*MockIClient)(nil).ListeningChangePlayer), ctx, req)
}

// ListeningGetPlayList mocks base method.
func (m *MockIClient) ListeningGetPlayList(ctx context.Context, req channellisteningautoplay.ListeningGetPlayListReq) (*channellisteningautoplay.ListeningGetPlayListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListeningGetPlayList", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.ListeningGetPlayListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListeningGetPlayList indicates an expected call of ListeningGetPlayList.
func (mr *MockIClientMockRecorder) ListeningGetPlayList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListeningGetPlayList", reflect.TypeOf((*MockIClient)(nil).ListeningGetPlayList), ctx, req)
}

// ListeningGetPlayerStatus mocks base method.
func (m *MockIClient) ListeningGetPlayerStatus(ctx context.Context, req channellisteningautoplay.ListeningGetPlayerStatusReq) (*channellisteningautoplay.ListeningGetPlayerStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListeningGetPlayerStatus", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.ListeningGetPlayerStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListeningGetPlayerStatus indicates an expected call of ListeningGetPlayerStatus.
func (mr *MockIClientMockRecorder) ListeningGetPlayerStatus(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListeningGetPlayerStatus", reflect.TypeOf((*MockIClient)(nil).ListeningGetPlayerStatus), ctx, req)
}

// ListeningGetPlayerUserInfo mocks base method.
func (m *MockIClient) ListeningGetPlayerUserInfo(ctx context.Context, req channellisteningautoplay.ListeningGetPlayerUserInfoReq) (*channellisteningautoplay.ListeningGetPlayerUserInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListeningGetPlayerUserInfo", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.ListeningGetPlayerUserInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListeningGetPlayerUserInfo indicates an expected call of ListeningGetPlayerUserInfo.
func (mr *MockIClientMockRecorder) ListeningGetPlayerUserInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListeningGetPlayerUserInfo", reflect.TypeOf((*MockIClient)(nil).ListeningGetPlayerUserInfo), ctx, req)
}

// ListeningGetSongListByType mocks base method.
func (m *MockIClient) ListeningGetSongListByType(ctx context.Context, req channellisteningautoplay.ListeningGetSongListByTypeReq) (*channellisteningautoplay.ListeningGetSongListByTypeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListeningGetSongListByType", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.ListeningGetSongListByTypeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListeningGetSongListByType indicates an expected call of ListeningGetSongListByType.
func (mr *MockIClientMockRecorder) ListeningGetSongListByType(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListeningGetSongListByType", reflect.TypeOf((*MockIClient)(nil).ListeningGetSongListByType), ctx, req)
}

// ListeningGetSongListList mocks base method.
func (m *MockIClient) ListeningGetSongListList(ctx context.Context, req channellisteningautoplay.ListeningGetSongListListReq) (*channellisteningautoplay.ListeningGetSongListListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListeningGetSongListList", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.ListeningGetSongListListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListeningGetSongListList indicates an expected call of ListeningGetSongListList.
func (mr *MockIClientMockRecorder) ListeningGetSongListList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListeningGetSongListList", reflect.TypeOf((*MockIClient)(nil).ListeningGetSongListList), ctx, req)
}

// ListeningGetSongResource mocks base method.
func (m *MockIClient) ListeningGetSongResource(ctx context.Context, req channellisteningautoplay.ListeningGetSongResourceReq) (*channellisteningautoplay.ListeningGetSongResourceResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListeningGetSongResource", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.ListeningGetSongResourceResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListeningGetSongResource indicates an expected call of ListeningGetSongResource.
func (mr *MockIClientMockRecorder) ListeningGetSongResource(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListeningGetSongResource", reflect.TypeOf((*MockIClient)(nil).ListeningGetSongResource), ctx, req)
}

// ListeningOrderSong mocks base method.
func (m *MockIClient) ListeningOrderSong(ctx context.Context, req channellisteningautoplay.ListeningOrderSongReq) (*channellisteningautoplay.ListeningOrderSongResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListeningOrderSong", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.ListeningOrderSongResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListeningOrderSong indicates an expected call of ListeningOrderSong.
func (mr *MockIClientMockRecorder) ListeningOrderSong(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListeningOrderSong", reflect.TypeOf((*MockIClient)(nil).ListeningOrderSong), ctx, req)
}

// ListeningReportRecord mocks base method.
func (m *MockIClient) ListeningReportRecord(ctx context.Context, req channellisteningautoplay.ListeningReportRecordReq) (*channellisteningautoplay.ListeningReportRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListeningReportRecord", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.ListeningReportRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListeningReportRecord indicates an expected call of ListeningReportRecord.
func (mr *MockIClientMockRecorder) ListeningReportRecord(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListeningReportRecord", reflect.TypeOf((*MockIClient)(nil).ListeningReportRecord), ctx, req)
}

// ListeningSearchSongByKey mocks base method.
func (m *MockIClient) ListeningSearchSongByKey(ctx context.Context, req channellisteningautoplay.ListeningSearchSongByKeyReq) (*channellisteningautoplay.ListeningSearchSongByKeyResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListeningSearchSongByKey", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.ListeningSearchSongByKeyResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListeningSearchSongByKey indicates an expected call of ListeningSearchSongByKey.
func (mr *MockIClientMockRecorder) ListeningSearchSongByKey(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListeningSearchSongByKey", reflect.TypeOf((*MockIClient)(nil).ListeningSearchSongByKey), ctx, req)
}

// ReportChannelAutoSongProgress mocks base method.
func (m *MockIClient) ReportChannelAutoSongProgress(ctx context.Context, req channellisteningautoplay.ReportChannelAutoSongProgressReq) (*channellisteningautoplay.ReportChannelAutoSongProgressResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportChannelAutoSongProgress", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.ReportChannelAutoSongProgressResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ReportChannelAutoSongProgress indicates an expected call of ReportChannelAutoSongProgress.
func (mr *MockIClientMockRecorder) ReportChannelAutoSongProgress(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportChannelAutoSongProgress", reflect.TypeOf((*MockIClient)(nil).ReportChannelAutoSongProgress), ctx, req)
}

// SetChannelPlayMode mocks base method.
func (m *MockIClient) SetChannelPlayMode(ctx context.Context, req channellisteningautoplay.SetChannelPlayModeReq) (*channellisteningautoplay.SetChannelPlayModeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelPlayMode", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.SetChannelPlayModeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelPlayMode indicates an expected call of SetChannelPlayMode.
func (mr *MockIClientMockRecorder) SetChannelPlayMode(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelPlayMode", reflect.TypeOf((*MockIClient)(nil).SetChannelPlayMode), ctx, req)
}

// SetChannelPlayStatus mocks base method.
func (m *MockIClient) SetChannelPlayStatus(ctx context.Context, req channellisteningautoplay.SetChannelPlayStatusReq) (*channellisteningautoplay.SetChannelPlayStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelPlayStatus", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.SetChannelPlayStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelPlayStatus indicates an expected call of SetChannelPlayStatus.
func (mr *MockIClientMockRecorder) SetChannelPlayStatus(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelPlayStatus", reflect.TypeOf((*MockIClient)(nil).SetChannelPlayStatus), ctx, req)
}

// SetVolume mocks base method.
func (m *MockIClient) SetVolume(ctx context.Context, req channellisteningautoplay.SetVolumeReq) (*channellisteningautoplay.SetVolumeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetVolume", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.SetVolumeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetVolume indicates an expected call of SetVolume.
func (mr *MockIClientMockRecorder) SetVolume(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetVolume", reflect.TypeOf((*MockIClient)(nil).SetVolume), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// SwitchChannelPlay mocks base method.
func (m *MockIClient) SwitchChannelPlay(ctx context.Context, req channellisteningautoplay.SwitchChannelPlayReq) (*channellisteningautoplay.SwitchChannelPlayResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwitchChannelPlay", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.SwitchChannelPlayResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SwitchChannelPlay indicates an expected call of SwitchChannelPlay.
func (mr *MockIClientMockRecorder) SwitchChannelPlay(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwitchChannelPlay", reflect.TypeOf((*MockIClient)(nil).SwitchChannelPlay), ctx, req)
}

// WYYRedirectApi mocks base method.
func (m *MockIClient) WYYRedirectApi(ctx context.Context, req channellisteningautoplay.WYYRedirectApiReq) (*channellisteningautoplay.WYYRedirectApiResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WYYRedirectApi", ctx, req)
	ret0, _ := ret[0].(*channellisteningautoplay.WYYRedirectApiResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// WYYRedirectApi indicates an expected call of WYYRedirectApi.
func (mr *MockIClientMockRecorder) WYYRedirectApi(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WYYRedirectApi", reflect.TypeOf((*MockIClient)(nil).WYYRedirectApi), ctx, req)
}
