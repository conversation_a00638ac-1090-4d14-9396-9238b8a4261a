// Code generated by quicksilver-cli. DO NOT EDIT.
package currency

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "golang.org/x/net/context"
	currencyPB1 "golang.52tt.com/protocol/services/currency-go"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AddUserCurrency(ctx context.Context, uid uint32, add int32, orderID, desc string, reason uint32) protocol.ServerError
	GetUserCurrency(ctx context.Context, uid uint32) (int32,protocol.ServerError)
	GetUserCurrencyLog(ctx context.Context, uid uint32, in *currencyPB1.GetUserCurrencyLogReq) (*currencyPB1.GetUserCurrencyLogResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
