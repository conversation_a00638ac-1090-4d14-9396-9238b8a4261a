package currency

import (
	"context"
	"fmt"
	"golang.52tt.com/clients/exp"
	missionTL "golang.52tt.com/clients/missiontimeline"
	seqgen "golang.52tt.com/clients/seqgen/v2"
	"golang.52tt.com/pkg/log"
	missionTLPB "golang.52tt.com/protocol/services/missiontimelinesvr"
	"golang.52tt.com/services/notify"
	"google.golang.org/grpc"
	"testing"
	"time"
)

func notifyCurrencyChanged(userID uint32) (err error) {
	ctx := context.Background()

	//ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	//defer func() {
	//	if err != nil {
	//		log.Errorf("Failed to notifyCurrencyChanged: %d %v", userID, err)
	//	}
	//	cancel()
	//}()

	expClient := exp.NewClient(grpc.WithBlock())
	currencyClient:= NewClient(grpc.WithBlock())
	seqgenClient, _ := seqgen.NewClient(grpc.WithBlock())
	missionTLClient := missionTL.NewClient(grpc.WithBlock())


	ex, lv, err := expClient.GetUserExp(ctx, userID)
	if err != nil {
		return
	}

	b, e, err := expClient.GetLevelExpScope(ctx, userID, lv)
	if err != nil {
		return
	}


	cur, err := currencyClient.GetUserCurrency(ctx, userID)
	if err != nil {
		return
	}

	seq, err := seqgenClient.GenerateSequence(ctx, userID, seqgen.NamespaceUser, seqgen.KeyGrowth, 1)
	if err != nil {
		return
	}

	msg := &missionTLPB.GrowInfoMessage{
		Exp:                ex,
		Level:              lv,
		CurrentLevelExpMin: b,
		CurrentLevelExpMax: e,
		Currency:           cur,
	}

	err = missionTLClient.ExpCurrencyChanged(ctx, userID, uint32(seq), msg)
	if err != nil {
		return
	}

	log.Debugf("WriteTimelineMsg: %d seq %d exp %d lv %d(%d, %d) currency %d",
		userID, seq, ex, lv, b, e, cur)

	// notify grow sync
	notify.NotifySync(userID, notify.Grow)
	return nil
}

func Test_Client_AddUserCurrency(t *testing.T) {
	c := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("currency.52tt.local"))
	err := c.AddUserCurrency(context.Background(), 1983722, 111, fmt.Sprintf("test_%d", time.Now().UnixNano()), "test.", 0)
	if err != nil {
		fmt.Errorf("AddUserCurrency failed: %v\n", err)
	} else {
		fmt.Println("AddUserCurrency OK")
	}

	notifyCurrencyChanged(1983722)
}
