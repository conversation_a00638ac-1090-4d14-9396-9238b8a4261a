package missionTL

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/missiontimelinesvr"
)

const (
	serviceName = "missiontimeline"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewMissionTimelineClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.MissionTimelineClient { return c.Stub().(pb.MissionTimelineClient) }

const (
	mtlSuffixMission = "@mission"
	mtlSuffixReaded  = "RD"
)

func (c *Client) ExpCurrencyChanged(ctx context.Context, uid uint32, seq uint32, msg *pb.GrowInfoMessage) protocol.ServerError {
	tlMsg := &pb.GrowInfoTimelineMsg{
		Seqid: seq,
		Type:  uint32(pb.GrowInfoTimelineMsg_EXP_CURRENCY_CHANGED_MSG),
	}

	if msgBin, err := msg.Marshal(); err == nil {
		tlMsg.MsgBin = msgBin
	} else {
		return protocol.NewServerError(status.ErrSys)
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().UpdateGrowInfo(ctx, &pb.UpdateGrowInfoReq{
		Uid: uid,
		Msg: tlMsg,
	})

	return protocol.ToServerError(err)
}

func (c *Client) NumericChanged(ctx context.Context, uid uint32, seq uint32, msg *pb.NumericInfoMessage) protocol.ServerError {
	tlMsg := &pb.GrowInfoTimelineMsg{
		Seqid: seq,
		Type:  uint32(pb.GrowInfoTimelineMsg_USER_CHARM_RICH_CHANGED),
	}

	if msgBin, err := msg.Marshal(); err == nil {
		tlMsg.MsgBin = msgBin
	} else {
		return protocol.NewServerError(status.ErrSys)
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().UpdateGrowInfo(ctx, &pb.UpdateGrowInfoReq{
		Uid: uid,
		Msg: tlMsg,
	})

	return protocol.ToServerError(err)
}

func (c *Client) ScoreChanged(ctx context.Context, uid uint32, seq uint32, msg *pb.UserScoreMessage) protocol.ServerError {
	tlMsg := &pb.GrowInfoTimelineMsg{
		Seqid: seq,
		Type:  uint32(pb.GrowInfoTimelineMsg_USER_SOCRE_CHANGED),
	}

	if msgBin, err := msg.Marshal(); err == nil {
		tlMsg.MsgBin = msgBin
	} else {
		return protocol.NewServerError(status.ErrSys)
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().UpdateGrowInfo(ctx, &pb.UpdateGrowInfoReq{
		Uid: uid,
		Msg: tlMsg,
	})

	return protocol.ToServerError(err)
}

func (c *Client) WriteTimeLineMsg(ctx context.Context, uid uint32, seq uint32, msgBin []byte) protocol.ServerError {
	tlMsg := &pb.MissionTimelineMsg{
		Seqid: seq,
		Type:  uint32(pb.MissionTimelineMsg_MISSION_FINISH_MSG),
		MsgBin: msgBin,
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().WriteTimelineMsg(ctx, &pb.WriteTimelineMsgReq{
		Id: uid,
		Suffix: mtlSuffixMission,
		Msg: tlMsg,
	})
	return protocol.ToServerError(err)
}
