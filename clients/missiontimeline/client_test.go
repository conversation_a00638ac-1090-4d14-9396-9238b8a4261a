package missionTL

import (
	"context"
	"encoding/base64"
	"os"
	"testing"

	Mission "golang.52tt.com/protocol/services/missiontimelinesvr"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
)

func TestClient_ExpCurrencyChanged(t *testing.T) {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
	cli := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("missiontimeline.52tt.local"))
	err := cli.ExpCurrencyChanged(context.Background(), 233, 12321, &Mission.GrowInfoMessage{})
	if err != nil {
		t.<PERSON>rf("Failed to GetProduct: %v", err)
	}
	t.Log("success")
}

func TestDecodeGrowInfo(t *testing.T) {
	s := "CMzFPRABGhIIqp0FECQYnoUFILq7BSiLmQQ="

	data, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		t.Errorf("base64 decode err: %v", err)
		return
	}

	var tlMsg Mission.MissionTimelineMsg
	err = tlMsg.Unmarshal(data)
	if err != nil {
		t.Errorf("unmarshal timeline msg err: %v", err)
		return
	}

	var growMsg Mission.GrowInfoMessage
	err = growMsg.Unmarshal(tlMsg.GetMsgBin())
	if err != nil {
		t.Errorf("unmarshal grow msg err: %v", err)
		return
	}

	t.Logf("grow info: %+v", growMsg)
}
