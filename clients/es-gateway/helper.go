package es_gateway

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/log"
)

type Action struct {
	Index string `json:"_index,omitempty"`
	Id    string `json:"_id,omitempty"`
}

type BulkDelete struct {
	Delete Action `json:"delete,omitempty"`
}

func CreateBulkDeleteBody(ctx context.Context, index string, docIds []string) (body []byte) {
	for _, docId := range docIds {
		bulk := &BulkDelete{
			Delete: Action{
				Index: index,
				Id:    docId,
			},
		}
		b, err := json.Marshal(bulk)
		if err != nil {
			log.ErrorWithCtx(ctx, "createBulkDeleteBody json.Marshal failed, err:%v, bulk:%+v", err, bulk)
			continue
		}
		body = append(body, b...)
		body = append(body, []byte("\n")...)
	}
	log.DebugWithCtx(ctx, "createBulkDeleteBody ok, %s", string(body))
	return
}
