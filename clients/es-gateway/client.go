package es_gateway

import (
	"context"
	"encoding/json"
	"golang.52tt.com/protocol/common/status"

	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"

	pb "golang.52tt.com/protocol/services/es-gateway"
)

const (
	serviceName = "es-gateway"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewEsGatewayClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.EsGatewayClient { return c.Stub().(pb.EsGatewayClient) }

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) Search(ctx context.Context, req *pb.SearchReq) (*pb.SearchResp, error) {
	resp, err := c.typedStub().Search(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) Create(ctx context.Context, req *pb.CreateReq) (*pb.CreateResp, error) {
	resp, err := c.typedStub().Create(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) Delete(ctx context.Context, req *pb.DeleteReq) (*pb.DeleteResp, error) {
	resp, err := c.typedStub().Delete(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) Index(ctx context.Context, req *pb.IndexReq) (*pb.IndexResp, error) {
	resp, err := c.typedStub().Index(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) Count(ctx context.Context, req *pb.CountReq) (*pb.CountResp, error) {
	resp, err := c.typedStub().Count(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetById(ctx context.Context, req *pb.GetByIdReq) (*pb.GetByIdResp, error) {
	resp, err := c.typedStub().GetById(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) Bulk(ctx context.Context, req *pb.BulkReq) (*pb.BulkResp, error) {
	resp, err := c.typedStub().Bulk(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DeleteByQuery(ctx context.Context, req *pb.DeleteByQueryReq) (*pb.DeleteByQueryResp, error) {
	resp, err := c.typedStub().DeleteByQuery(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) Analyze(ctx context.Context, index, body string) (*pb.AnalyzeResp, error) {
	resp, err := c.typedStub().Analyze(ctx, &pb.AnalyzeReq{
		Index: index,
		Body:  body,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) SearchDeep(ctx context.Context, req *pb.SearchDeepReq) (*pb.SearchDeepResp, error) {
	resp, err := c.typedStub().SearchDeep(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SearchScroll(ctx context.Context, req *pb.SearchScrollReq) (*pb.SearchScrollResp, error) {
	resp, err := c.typedStub().SearchScroll(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DeleteScroll(ctx context.Context, req *pb.DeleteScrollReq) (*pb.DeleteScrollResp, error) {
	resp, err := c.typedStub().DeleteScroll(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HelpSearch(ctx context.Context, index, body string, offset, count int64, result interface{}) protocol.ServerError {
	req := &pb.SearchReq{
		Index:  index,
		Body:   body,
		Offset: offset,
		Limit:  count,
	}

	resp, err := c.typedStub().Search(ctx, req)
	if err != nil {
		return protocol.ToServerError(err)
	}

	if len(resp.Data) != 0 {
		err = json.Unmarshal(resp.GetData(), result)
		if err != nil {
			return protocol.NewExactServerError(nil, status.ErrBizBaseSysErr, "搜索结果格式错误")
		}
	}
	return nil
}