/*
 * @Description:
 * @Date: 2021-07-09 16:01:43
 * @LastEditors: liang
 * @LastEditTime: 2021-08-12 17:42:04
 */
package comm_push_placeholder

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/comm-push-placeholder"
	"google.golang.org/grpc"
)

const (
	serviceName = "comm-push-placeholder"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewCommPushPlaceholderClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.CommPushPlaceholderClient {
	return c.Stub().(pb.CommPushPlaceholderClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func (c *Client) GetPlaceHold(ctx context.Context, in *pb.GetPlaceHoldReq) (out *pb.GetPlaceHoldResp, err error) {
	out, err = c.typedStub().GetPlaceHold(ctx, in)
	return out, err
}

func (c *Client) GetCopyWritingTemplateByLibId(ctx context.Context, libId string) (out *pb.GetCopyWritingTemplateByLibIdResp, err error) {

	out, err = c.typedStub().GetCopyWritingTemplateByLibId(ctx, &pb.GetCopyWritingTemplateByLibIdReq{
		LibId: libId,
	})
	return out, err

}

func (c *Client) BatchGetUserPushCopyWriting(ctx context.Context, in *pb.BatchGetUserPushCopyWritingReq) (out *pb.BatchGetUserPushCopyWritingResp, err error) {

	out, err = c.typedStub().BatchGetUserPushCopyWriting(ctx, in)
	return out, err

}
