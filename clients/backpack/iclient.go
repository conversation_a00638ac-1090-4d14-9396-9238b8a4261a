package backpack

import (
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/backpacksvr"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

// Deprecated: IClient is deprecated, please use backpack-base instead
type IClient interface {
	client.BaseClient
	// Deprecated: use backpack-base instead
	FreeZeItem(ctx context.Context, uid uint32, req *pb.FreeZeItemReq) (*pb.FreeZeItemResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	GiveUserPackage(ctx context.Context, uid uint32, req *pb.GiveUserPackageReq) (*pb.GiveUserPackageResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	GetPackageCfg(ctx context.Context, uid uint32, req *pb.GetPackageCfgReq) (*pb.GetPackageCfgResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	GetPackageCfgV2(ctx context.Context) (*pb.GetPackageCfgResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	GetPackageItemCfg(ctx context.Context, uid uint32, req *pb.GetPackageItemCfgReq, opts ...grpc.CallOption) (*pb.GetPackageItemCfgResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	GetItemCfg(ctx context.Context, uid uint32, req *pb.GetItemCfgReq) (*pb.GetItemCfgResp, error)
	// Deprecated: use backpack-base instead
	GetFuncCardCfg(ctx context.Context, uid uint32, req *pb.GetFuncCardCfgReq) (*pb.GetFuncCardCfgResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	GetUserBackpack(ctx context.Context, uid uint32) (*pb.GetUserBackpackResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	GetUserBackpackLog(ctx context.Context, uid uint32) (*pb.GetUserBackpackLogResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	ConversionItem(ctx context.Context, uid uint32, req *pb.ConversionItemReq) (*pb.ConversionItemResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	GetOrderListByTimeRange(ctx context.Context, startTime, endTime, logType uint32) ([]string, protocol.ServerError)
	// Deprecated: use backpack-base instead
	RollBackUserItem(ctx context.Context, uid, createTime uint32, orderID string) (*pb.RollBackUserItemResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	AddPackageCfg(ctx context.Context, bgName, bgDesc string) (*pb.AddPackageCfgResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	DelPackageCfg(ctx context.Context, bgId uint32) (*pb.DelPackageCfgResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	AddPackageItemCfg(ctx context.Context, cfg *pb.PackageItemCfg) (*pb.AddPackageItemCfgResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	ModPackageItemCfg(ctx context.Context, cfg *pb.PackageItemCfg) (*pb.ModPackageItemCfgResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	DelPackageItemCfg(ctx context.Context, bgId, bgItemId uint32) (*pb.DelPackageItemCfgResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	AddFuncCardCfg(ctx context.Context, cfg *pb.FuncCardCfg) (*pb.AddFuncCardCfgResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	DelFuncCardCfg(ctx context.Context, cardId uint32) (*pb.DelFuncCardCfgResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	BatchDeductUserItem(ctx context.Context, req *pb.BatchDeductUserItemReq) (*pb.BatchDeductUserItemResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	UseBackpackItem(ctx context.Context, in *pb.UseBackpackItemReq) (*pb.UseBackpackItemResp, protocol.ServerError)
	// Deprecated: use backpack-func-card instead
	GetUserFuncCardUse(ctx context.Context, in *pb.GetUserFuncCardUseReq) (*pb.GetUserFuncCardUseResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	GetOrderCountByTimeRange(ctx context.Context, in *pb.GetOrderCountByTimeRangeReq) (*pb.GetOrderCountByTimeRangeResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	AddItemCfg(ctx context.Context, in *pb.AddItemCfgReq) (*pb.AddItemCfgResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	GetUseItemOrderInfo(ctx context.Context, in *pb.GetUseItemOrderInfoReq) (*pb.GetUseItemOrderInfoResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	GetTimeRangeUseOrderData(ctx context.Context, in *pb.TimeRangeReq) (*pb.CountResp, protocol.ServerError)
	// Deprecated: use backpack-base instead
	GetTimeRangeOrderList(ctx context.Context, in *pb.TimeRangeReq) (*pb.OrderIdsResp, protocol.ServerError)
	// Deprecated: use backpack-func-card instead
	BatchGetUserFuncCardUse(ctx context.Context, in *pb.BatchGetUserFuncCardUseReq) (map[uint32][]*pb.FuncCardCfg, protocol.ServerError)
}

// Deprecated: IClient is deprecated, please use backpack-base instead
func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
