package FindFriends

import (
	"context"
	"testing"

	"google.golang.org/grpc"
)

func Test_ClientFindFriendsGetUserCards(t *testing.T) {
	cli := newClient(grpc.WithBlock())

	cards, err := cli.GetUserCards(context.Background(), 500009)
	if err != nil {
		t.Fatalf("GetUserCards FAILED: %v", err)
	}
	t.Logf("GetUserCards: %v", cards)
}

func Test_ClientFindFriendsGetQuickMatchConfigurations(t *testing.T) {
	cli := newClient(grpc.WithBlock())

	cfgs, err := cli.GetQuickMatchConfigurations(context.Background())
	if err != nil {
		t.Fatalf("GetUserCards FAILED: %v", err)
	}
	t.Logf("Configurations: %v", cfgs)
}
