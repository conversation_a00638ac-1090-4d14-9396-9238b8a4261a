package FindFriends

import (
	"context"

	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	pb "golang.52tt.com/protocol/services/logicsvr-go/find_friends_logic"
	"golang.52tt.com/services/logic-grpc-gateway/logicproto"

	ga1 "golang.52tt.com/protocol/app"
	ga "golang.52tt.com/protocol/app/find_friends"
)

const serviceName = "find-friends-logic"

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) *Client {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewFindFriendsLogicClient(cc)
			}, dopts...,
		),
	}
}

func (c *Client) typedStub() pb.FindFriendsLogicClient { return c.Stub().(pb.FindFriendsLogicClient) }

func (c *Client) GetUserCards(ctx context.Context, uid uint32) (out []*ga.UserCard, err error) {
	ctx = metadata.NewOutgoingContext(ctx, runtime.MetadataFromServicePacketHeader(
		&logicproto.ServicePacketHeader{Uid: uid}),
	)

	r, err := grpcClient.Call(func(opts ...grpc.CallOption) (interface{}, error) {
		return c.typedStub().FindFriendsGetUserCards(ctx, &ga.FindFriendsGetUserCardsReq{
			BaseReq:    &ga1.BaseReq{},
			SessionId:  "",
			StartIndex: 0,
			Count:      20,
		}, opts...)
	})

	return r.(*ga.FindFriendsGetUserCardsResp).GetUserCards(), err
}

func (c *Client) GetQuickMatchConfigurations(ctx context.Context) (out []*ga.QuickMatchConfiguration, err error) {
	ctx = metadata.NewOutgoingContext(ctx, runtime.MetadataFromServicePacketHeader(
		&logicproto.ServicePacketHeader{Uid: 500009}),
	)

	r, err := grpcClient.Call(func(opts ...grpc.CallOption) (interface{}, error) {
		return c.typedStub().GetQuickMatchConfigurations(ctx, &ga.GetQuickMatchConfigurationsReq{
			BaseReq: &ga1.BaseReq{},
			Version: 0,
		}, opts...)
	})

	return r.(*ga.GetQuickMatchConfigurationsResp).GetConfigurations(), err
}
