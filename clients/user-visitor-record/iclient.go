package user_visitor_record

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/user-visitor-record"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	ReportUserVisitorRecord(ctx context.Context, in *pb.ReportUserVisitorRecordReq) (*pb.ReportUserVisitorRecordResp, protocol.ServerError)
	GetUserVisitorRecordList(ctx context.Context, in *pb.GetUserVisitorRecordListReq) (*pb.GetUserVisitorRecordListResp, protocol.ServerError)
	GetUserBeVisitorRecordList(ctx context.Context, in *pb.GetUserBeVisitorRecordListReq) (*pb.GetUserBeVisitorRecordListResp, protocol.ServerError)
	GetUserBeVisitorRecordCount(ctx context.Context, in *pb.GetUserBeVisitorRecordCountReq) (*pb.GetUserBeVisitorRecordCountResp, protocol.ServerError)
	GetAllTaskStatus(ctx context.Context, in *pb.GetAllTaskStatusReq) (*pb.GetAllTaskStatusResp, protocol.ServerError)
	SetTaskStatus(ctx context.Context, in *pb.SetTaskStatusReq) (*pb.SetTaskStatusResp, protocol.ServerError)
	UpsertTask(ctx context.Context, in *pb.UpsertTaskReq) (*pb.UpsertTaskResp, protocol.ServerError)
	SetShowUserBeVisitorRecordCount(ctx context.Context, in *pb.SetShowUserBeVisitorRecordCountReq) (*pb.SetShowUserBeVisitorRecordCountResp, protocol.ServerError)
	GetShowUserBeVisitorRecordCount(ctx context.Context, in *pb.GetShowUserBeVisitorRecordCountReq) (*pb.GetShowUserBeVisitorRecordCountResp, protocol.ServerError)
	GetHideList(ctx context.Context, in *pb.GetHideListReq) (*pb.GetHideListResp, protocol.ServerError)
	GetHideStatusByUid(ctx context.Context, in *pb.GetHideStatusByUidReq) (*pb.GetHideStatusByUidResp, protocol.ServerError)
	SetHideStatusByUid(ctx context.Context, in *pb.SetHideStatusByUidReq) (*pb.SetHideStatusByUidResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
