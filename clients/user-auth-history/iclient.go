// Code generated by quicksilver-cli. DO NOT EDIT.
package user_auth_history

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/user-auth-history"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	BatchGetUserLastLoginInfo(ctx context.Context, uids []uint64) ([]*UserLoginInfo,protocol.ServerError)
	BatchGetUserLoginDevice(ctx context.Context, uids []uint64) ([]*BatchUserLoginDevice,protocol.ServerError)
	GetDeviceIdInfo(ctx context.Context, hexDeviceId string) (*GetDeviceIdInfoResp,protocol.ServerError)
	GetLastVerifySuccessInfo(ctx context.Context, uid uint64, reason uint32) (*pb.GetLastVerifySuccessInfoResp,protocol.ServerError)
	GetUserLastLoginInfo(ctx context.Context, uid uint64) (*UserLoginInfo,protocol.ServerError)
	GetUserLoginDevice(ctx context.Context, uid uint64) ([]*UserLoginDevice,protocol.ServerError)
	GetUserLoginHistory(ctx context.Context, uid uint64, start, end int64) (*GetUserLoginHistoryResp,protocol.ServerError)
	GetUserLoginWithDevice(ctx context.Context, device string, start, end int64) (*GetUserLoginWithDeviceResp,protocol.ServerError)
	GetUserRegInfo(ctx context.Context, uid uint64) (*UserLoginInfo,protocol.ServerError)
	IsUserInvalid(ctx context.Context, uid uint64) (bool,protocol.ServerError)
	RecordUserLogin(ctx context.Context, in *RecordUserLoginReq) (bool,protocol.ServerError)
	TrackUserLogin(ctx context.Context, in *TrackUserLoginReq) protocol.ServerError
	VerifyCAPTCHASuccess(ctx context.Context, uid uint64, reason uint32, deviceId string) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
