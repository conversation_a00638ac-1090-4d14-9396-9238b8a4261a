package user_auth_history

import (
	"context"
	"github.com/grpc-ecosystem/go-grpc-middleware"
	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/user-auth-history"
)

const (
	serviceName = "user-auth-history"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewUserAuthHistoryClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.UserAuthHistoryClient { return c.Stub().(pb.UserAuthHistoryClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

type TrackUserLoginReq = pb.TrackUserLoginReq

func (c *Client) TrackUserLogin(ctx context.Context, in *TrackUserLoginReq) protocol.ServerError {
	_, err := c.typedStub().TrackUserLogin(ctx, in)
	return protocol.ToServerError(err)
}

type RecordUserLoginReq = pb.RecordUserLoginReq

func (c *Client) RecordUserLogin(ctx context.Context, in *RecordUserLoginReq) (bool, protocol.ServerError) {
	out, err := c.typedStub().RecordUserLogin(ctx, in)
	if err != nil {
		return false, protocol.ToServerError(err)
	}
	return out.IsNewUsualDevice, nil
}

type GetUserLoginWithDeviceResp = pb.GetUserLoginWithDeviceResp
type UserLoginHit = pb.UserLoginHit

func (c *Client) GetUserLoginWithDevice(ctx context.Context, device string, start, end int64) (*GetUserLoginWithDeviceResp, protocol.ServerError) {
	out, err := c.typedStub().GetUserLoginWithDevice(ctx, &pb.GetUserLoginWithDeviceReq{
		DeviceId:     device,
		BeginLoginAt: start,
		EndLoginAt:   end,
	})
	return out, protocol.ToServerError(err)
}

type GetUserLoginHistoryResp = pb.GetUserLoginHistoryResp
type UserLoginInfo = pb.UserLoginInfo

func (c *Client) GetUserLoginHistory(ctx context.Context, uid uint64, start, end int64) (*GetUserLoginHistoryResp, protocol.ServerError) {
	out, err := c.typedStub().GetUserLoginHistory(ctx, &pb.GetUserLoginHistoryReq{
		Uid:       uid,
		BeginTime: start,
		EndTime:   end,
	})
	return out, protocol.ToServerError(err)
}

func (c *Client) IsUserInvalid(ctx context.Context, uid uint64) (bool, protocol.ServerError) {
	out, err := c.typedStub().IsUserInvalid(ctx, &pb.IsUserInvalidReq{
		Uid: uid,
	})

	if err != nil {
		return false, protocol.ToServerError(err)
	}
	return out.Invalid, nil
}

type UserLoginDevice = pb.UserLoginDevice

func (c *Client) GetUserLoginDevice(ctx context.Context, uid uint64) ([]*UserLoginDevice, protocol.ServerError) {
	out, err := c.typedStub().GetUserLoginDevice(ctx, &pb.GetUserLoginDeviceReq{
		Uid: uid,
	})

	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return out.LoginDevices, nil
}

type BatchUserLoginDevice = pb.BatchUserLoginDevice

func (c *Client) BatchGetUserLoginDevice(ctx context.Context, uids []uint64) ([]*BatchUserLoginDevice, protocol.ServerError) {
	out, err := c.typedStub().BatchGetUserLoginDevice(ctx, &pb.BatchGetUserLoginDeviceReq{
		Uids: uids,
	})

	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return out.LoginDevices, nil
}

type GetDeviceIdInfoResp = pb.GetDeviceIdInfoResp

func (c *Client) GetDeviceIdInfo(ctx context.Context, hexDeviceId string) (*GetDeviceIdInfoResp, protocol.ServerError) {
	out, err := c.typedStub().GetDeviceIdInfo(ctx, &pb.GetDeviceIdInfoReq{
		DeviceId: hexDeviceId,
	})
	return out, protocol.ToServerError(err)
}

func (c *Client) GetUserLastLoginInfo(ctx context.Context, uid uint64) (*UserLoginInfo, protocol.ServerError) {
	out, err := c.typedStub().GetUserLastLoginInfo(ctx, &pb.GetUserLastLoginInfoReq{
		Uid: uid,
	})
	return out, protocol.ToServerError(err)
}

func (c *Client) BatchGetUserLastLoginInfo(ctx context.Context, uids []uint64) ([]*UserLoginInfo, protocol.ServerError) {
	out, err := c.typedStub().BatchGetUserLastLoginInfo(ctx, &pb.BatchGetUserLastLoginInfoReq{
		UidList: uids,
	})

	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return out.InfoList, nil
}

func (c *Client) VerifyCAPTCHASuccess(ctx context.Context, uid uint64, reason uint32, deviceId string) protocol.ServerError {
	_, err := c.typedStub().VerifyCAPTCHASuccess(ctx, &pb.VerifyCAPTCHASuccessReq{
		Uid:          uid,
		VerifyReason: reason,
		DeviceId:     deviceId,
	})
	return protocol.ToServerError(err)
}

func (c *Client) GetLastVerifySuccessInfo(ctx context.Context, uid uint64, reason uint32) (*pb.GetLastVerifySuccessInfoResp, protocol.ServerError) {
	out, err := c.typedStub().GetLastVerifySuccessInfo(ctx, &pb.GetLastVerifySuccessInfoReq{
		Uid:          uid,
		VerifyReason: reason,
	})
	return out, protocol.ToServerError(err)
}

func (c *Client) GetUserRegInfo(ctx context.Context, uid uint64) (*UserLoginInfo, protocol.ServerError) {
	out, err := c.typedStub().GetUserRegInfo(ctx, &pb.GetUserRegInfoReq{
		Uid: uid,
	})

	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return out.Info, nil
}
