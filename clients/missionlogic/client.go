package missionlogic

import (
	"context"
	"strconv"

	"gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"
	"golang.52tt.com/pkg/protocol"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/missionlogicsvr"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

const (
	serviceName = "missionlogic"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewMissionLogicClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.MissionLogicClient {
	return c.Stub().(pb.MissionLogicClient)
}

func (c *Client) HandleMission(ctx context.Context, uid, cmd uint32, body []byte) error {

	if body == nil {
		body = make([]byte, 0)
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().HandleMission(ctx, &pb.HandleMissionReq{
		Uid:     uid,
		CmdId:   cmd,
		CmdBody: body,
	})
	return err
}

func (c *Client) IncreaseMissionFinishCount(ctx context.Context, uid, missionid, count uint32) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().IncreaseMissionFinishCount(ctx, &pb.IncreaseMissionFinishCountReq{
		Uid:       uid,
		MissionId: missionid,
		Count:     count,
	})
	return err
}

func (c *Client) HandleChannelOnlineMission(ctx context.Context, uid uint32, cid uint32, ctype uint32) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	req := &pb.HandleChannelOnlineMissionReq{
		Uid:         uid,
		ChannelId:   cid,
		ChannelType: ctype,
	}
	_, err := c.typedStub().HandleChannelOnlineMission(ctx, req)
	return err
}

func (c *Client) HandleEnterChannelFromRankMission(ctx context.Context, uid uint32) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	req := &pb.HandleEnterChannelFromRankMissionReq{Uid: uid}
	_, err := c.typedStub().HandleEnterChannelFromRankMission(ctx, req)
	return err
}

func (c *Client) HandleBindPhoneMission(ctx context.Context, uid uint32) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().HandleBindPhoneMission(ctx, &pb.HandleBindPhoneMissionReq{
		Uid: uid,
	})
	return err
}

func (c *Client) GetStaticMissionConfig(ctx context.Context, uid uint32) (uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().GetStaticMissionConfig(ctx, &tlvpickle.SKBuiltinEmpty_PB{})
	if err != nil {
		return 0, protocol.ToServerError(err)
	}

	return resp.GetUpdateTime(), nil
}

func (c *Client) CollectMissionBonus(ctx context.Context, uid uint32, identifier string) (*pb.CollectMissionBonusResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().CollectMissionBonus(ctx, &pb.CollectMissionBonusReq{Uid: uid, MissionIdentifier: identifier})
	return resp, protocol.ToServerError(err)
}
