package photoalbumgo

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/photoalbumgo"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetPhotoAlbum(ctx context.Context, uid uint32) (keys []string, newKeys []string, _ protocol.ServerError)
	UpdatePhotoAlbum(ctx context.Context, uid uint32, imgKeys, newImgKeys, prevImgKeys, prevNewImgKeys string) protocol.ServerError
	DeletePhotoAlbum(ctx context.Context, uids ...uint32) protocol.ServerError
	MoveOldPhotoAlbum(ctx context.Context, uid uint32) (*pb.MoveOldPhotoAlbumResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
