package tbean_logic

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/protocol/app/tbeanlogic"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetUserTbeanConsume(ctx context.Context, req *tbeanlogic.GetUserTbeanConsumeReq) (resp *tbeanlogic.GetUserTbeanConsumeResp, err error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
