package user_tag_go

import (
	"context"
	"golang.52tt.com/pkg/protocol"
	user_tag_go "golang.52tt.com/protocol/services/user-tag-go"
)

type IClient interface {
	GetPersonalTagClassifyConfFromCache(ctx context.Context) ([]*user_tag_go.UserOptPersonalTagClassify, protocol.ServerError)
	GetUserTag(ctx context.Context, uid uint32, isNeedTagExt bool) (*user_tag_go.UserTagList, protocol.ServerError)
	GetUserTagConfigListFromCache(ctx context.Context, tagType uint32) ([]*user_tag_go.UserTagConf, protocol.ServerError)
	SetRegistTag(ctx context.Context, in *user_tag_go.SetRegistTagReq) (protocol.ServerError)
	SetUserTag(ctx context.Context, uid, tagType, SettingCnt uint32, tagList []*user_tag_go.UserTagBase) (protocol.ServerError)
}


type IConfClient interface {
	GetAllClassifyTagConf(ctx context.Context, tagType uint32) ([]*user_tag_go.ClassifyTagInfo, error)
	GetAllRegistGameRelatedTagConf(ctx context.Context) ([]*user_tag_go.RegistGameRelatedTagConf, error)
	GetAllRegistMyTagConf(ctx context.Context) ([]*user_tag_go.ClassifyTagInfo, error)
}
