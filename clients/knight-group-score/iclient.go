package knightgroupscore

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/knightgroupscore"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AddKnightScore(ctx context.Context, req *pb.AddKnightScoreReq) (*pb.AddKnightScoreResp, protocol.ServerError)
	GetKnightScore(ctx context.Context, req *pb.GetKnightScoreReq) (*pb.GetKnightScoreResp, protocol.ServerError)
	GetKnightScoreDetail(ctx context.Context, req *pb.GetKnightScoreDetailReq) (*pb.GetKnightScoreDetailResp, protocol.ServerError)
	GetKnightScoreRecordList(ctx context.Context, req *pb.GetKnightScoreRecordListReq) (*pb.GetKnightScoreRecordListResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
