package group_go

import (
	"context"
	"golang.52tt.com/pkg/client"
	group_go "golang.52tt.com/protocol/services/group/group-go"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GroupOnlinePush(ctx context.Context, req *group_go.GroupOnlinePushReq, opts ...grpc.CallOption) (*group_go.GroupOnlinePushResp, error)
	TryUpdateMyGroupImMsg(ctx context.Context, req *group_go.TryUpdateMyGroupImMsgReq, opts ...grpc.CallOption) (*group_go.TryUpdateMyGroupImMsgResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
