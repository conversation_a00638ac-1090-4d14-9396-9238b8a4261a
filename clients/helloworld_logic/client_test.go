package helloworldlogic

import (
	"context"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/metadata"
	"os"
	"strconv"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestHelloWorld(t *testing.T) {

	var userA_id = 1

	var newUserContext = func(id int) context.Context {
		return metadata.NewIncomingContext(context.Background(), metadata.New(map[string]string{"req_uid": strconv.Itoa(id)}))
	}
	client, err := newClient()
	if err != nil {
		return
	}
	resp, err := client.GetHelloWorld(newUserContext(userA_id), "test")

	t.Log("GetHelloWorld:", resp)

}
