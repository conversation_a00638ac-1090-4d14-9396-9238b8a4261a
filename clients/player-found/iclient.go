package playerfound

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/playerfound"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetPlayerFoundSetting(ctx context.Context, uids []uint32) (*pb.GetPlayerFoundSettingResp, protocol.ServerError)
	GetPlayerFoundSettingWithType(ctx context.Context, uids []uint32, settingType pb.Settings) (map[uint32]bool, protocol.ServerError)
	UpdatePlayerFoundSetting(ctx context.Context, uid uint32, on bool, settingType pb.Settings) (*pb.UpdatePlayerFoundSettingResp, protocol.ServerError)
	DelUserPosts(ctx context.Context, uid uint32) (*pb.DelUserPostsRsp, protocol.ServerError)
	BatchGetUserPosts(ctx context.Context, uids []uint32) (*pb.BatchGetUserPostsResp, protocol.ServerError)
	GetUserPosts(ctx context.Context, uid uint32) (*pb.GetUserPostsRsp, protocol.ServerError)
	BatchSetUserPosts(ctx context.Context, userPosts []*pb.PlaymateUserPosts) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
