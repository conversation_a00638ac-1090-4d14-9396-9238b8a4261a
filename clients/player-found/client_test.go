package playerfound

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/services/playerfound"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {

	<PERSON><PERSON>("GetPlayerFound", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.PlayerFoundReq
		resp, err := client.GetPlayerFound(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetPlayerFound %+v", resp)
	})

}
