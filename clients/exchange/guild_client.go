package Exchange

import (
	"context"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/exchange"
	"google.golang.org/grpc"
)

type GuildClient struct {
	client.BaseClient
}

func newGuildClient(dopts ...grpc.DialOption) (*GuildClient, error) {

	return &GuildClient{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewGuildExchangeClient(cc)
			}, dopts...,
		),
	}, nil
}

func NewGuildClient(dopts ...grpc.DialOption) (*GuildClient, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithBlock(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newGuildClient(dopts...)
}

func (c *GuildClient) GetExchangeHis(ctx context.Context, masterUid uint32, offset uint32, limit uint32) (*pb.GetExchangeListRes, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetExchangeHis(ctx, &pb.GetExchangeListReq{
		Uid:    masterUid,
		Offset: offset,
		Limit:  limit,
	})
}

func (c *GuildClient) GetExchangeDetail(ctx context.Context, guildOrderId string, masterUid uint32, offset uint32, limit uint32) (*pb.GetExchangeListRes, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetExchangeDetail(ctx, &pb.GetExchangeDetailReq{
		GuildOrderId: guildOrderId,
		MasterUid:    masterUid,
		Offset:       offset,
		Limit:        limit,
	})
}

func (c *GuildClient) GetUidGuildExchangeData(ctx context.Context, uid uint32) (*pb.UserGuildExchangeData, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	r, err := ct.GetUidGuildExchangeData(ctx, &pb.UidReq{Uid: uid})
	return r, protocol.ToServerError(err)
}

func (c *GuildClient) GetExchangeAmount(ctx context.Context, masterUid uint32) (*pb.UserAllScoreResp, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	r, err := ct.GetExchangeAmount(ctx, &pb.UidReq{
		Uid: masterUid,
	})
	return r, err
}

func (c *GuildClient) GetExchangeAmountList(ctx context.Context, masterUid uint32, offset uint32, limit uint32) (*pb.UserAllScoreList, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	r, err := ct.GetExchangeAmountList(ctx, &pb.UidOffsetReq{
		Uid:    masterUid,
		Offset: offset,
		Limit:  limit,
	})
	return r, err
}

func (c *GuildClient) ExchangeV2(ctx context.Context, masterUid uint32, exchangeType uint32) (*pb.AllScore, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	r, err := ct.ExchangeV2(ctx, &pb.GuildExchangeTypeReq{
		Uid:  masterUid,
		Type: exchangeType,
	})
	return r, err
}

func (c *GuildClient) OnSignFlowUpdate(ctx context.Context, flowId string, thirdOrderNo string, signResult uint32) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.OnSignFlowUpdate(ctx, &pb.OnSignFlowUpdateReq{FlowId: flowId, ThirdOrderNo: thirdOrderNo, SignResult: signResult})

}

func (c *GuildClient) OnSignFlowFinish(ctx context.Context, flowId string, flowStatus string) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.OnSignFlowFinish(ctx, &pb.OnSignFlowFinishReq{FlowId: flowId, FlowStatus: flowStatus})
}

func (c *GuildClient) OnSignDocExpire(ctx context.Context, flowId string, fileId string) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.OnSignDocExpire(ctx, &pb.OnSignFlowExpireReq{FlowId: flowId, FileId: fileId})
}

func (c *GuildClient) AnchorGetExchangeHis(ctx context.Context, uid uint32, offset uint32, limit uint32) (*pb.AnchorExchangeHisRes, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.AnchorGetExchangeHis(ctx, &pb.AnchorExchangeHisReq{
		Uid:    uid,
		Offset: offset,
		Limit:  limit,
	})
}

func (c *GuildClient) AnchorCheckAuth(ctx context.Context, uid uint32) (*pb.AuthRes, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.AnchorCheckAuth(ctx, &pb.UidReq{
		Uid: uid,
	})
}

func (c *GuildClient) AnchorCheckPrivate(ctx context.Context, uid uint32) (*pb.AuthRes, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.AnchorCheckPrivate(ctx, &pb.UidReq{
		Uid: uid,
	})
}

func (c *GuildClient) AnchorApplyPublic(ctx context.Context, uid uint32) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.AnchorApplyPublic(ctx, &pb.UidReq{
		Uid: uid,
	})
}

func (c *GuildClient) GetApplyStatus(ctx context.Context, uid uint32) (*pb.ApplyStatus, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetApplyStatus(ctx, &pb.UidReq{
		Uid: uid,
	})
}

func (c *GuildClient) AnchorStartSign(ctx context.Context, uid uint32, id uint32) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.AnchorStartSign(ctx, &pb.ApplyIdReq{
		Uid: uid,
		Id:  id,
	})
}

func (c *GuildClient) AnchorGetSignUrl(ctx context.Context, uid uint32) (*pb.ApplyStatus, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.AnchorGetSignUrl(ctx, &pb.UidReq{
		Uid: uid,
	})
}

func (c *GuildClient) AnchorGetContractList(ctx context.Context, uid uint32, offset uint32, limit uint32) (*pb.ContractList, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.AnchorGetContractList(ctx, &pb.UidOffsetReq{
		Uid:    uid,
		Offset: offset,
		Limit:  limit,
	})
}

func (c *GuildClient) AnchorApplyPrivate(ctx context.Context, uid uint32) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.AnchorApplyPrivate(ctx, &pb.UidReq{
		Uid: uid,
	})
}

func (c *GuildClient) GetMasterApplyCount(ctx context.Context, uid uint32) (*pb.ApplyCount, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetMasterApplyCount(ctx, &pb.UidReq{
		Uid: uid,
	})
}

func (c *GuildClient) GetMasterApplyList(ctx context.Context, uid uint32, toPublic uint32, offset uint32, limit uint32) (*pb.AnchorApplyList, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetMasterApplyList(ctx, &pb.ApplyListReq{
		MasterUid: uid,
		ToPublic:  toPublic,
		Offset:    offset,
		Limit:     limit,
	})
}

func (c *GuildClient) SearchApplyListTTid(ctx context.Context, masterUid uint32, toPublic uint32, searchTTid string) (*pb.AnchorApplyData, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.SearchApplyListTTid(ctx, &pb.SearchApplyListTTidReq{
		MasterUid:  masterUid,
		ToPublic:   toPublic,
		SearchTtid: searchTTid,
	})
}

func (c *GuildClient) MasterBatchExaApply(ctx context.Context, masterUid uint32, pass bool, IdList *[]uint32, toPublic uint32) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.MasterBatchExaApply(ctx, &pb.MasterBatchExaApplyReq{
		MasterUid: masterUid,
		Pass:      pass,
		IdList:    *IdList,
		ToPublic:  toPublic,
	})
}

func (c *GuildClient) MasterGetContractList(ctx context.Context, uid uint32, offset uint32, limit uint32) (*pb.AnchorApplyList, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.MasterGetContractList(ctx, &pb.UidOffsetReq{
		Uid:    uid,
		Offset: offset,
		Limit:  limit,
	})
}

func (c *GuildClient) MasterStartSign(ctx context.Context, uid uint32, id uint32) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.MasterStartSign(ctx, &pb.ApplyIdReq{
		Uid: uid,
		Id:  id,
	})
}

func (c *GuildClient) MasterGetSignUrl(ctx context.Context, uid uint32, id uint32) (*pb.SignUrl, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.MasterGetSignUrl(ctx, &pb.ApplyIdReq{
		Uid: uid,
		Id:  id,
	})
}

func (c *GuildClient) MasterGetContractFinishedList(ctx context.Context, uid uint32, offset uint32, limit uint32, validType uint32) (*pb.ContractList, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.MasterGetContractFinishedList(ctx, &pb.OffsetValidReq{
		Uid:       uid,
		Offset:    offset,
		Limit:     limit,
		ValidType: validType,
	})
}

func (c *GuildClient) GetSettlementAttribute(ctx context.Context, uid, eType, offset, limit uint32, startTime, endTime uint64) (*pb.UserAllScoreList, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetSettlementAttribute(ctx, &pb.UidTimeRangeReq{
		Uid:       uid,
		StartTime: startTime,
		EndTime:   endTime,
		Type:      eType,
		Offset:    offset,
		Limit:     limit,
	})
}

func (c *GuildClient) MasterGetMainApplyList(ctx context.Context, uid, offset, limit uint32) (*pb.MasterGetMainApplyListResp, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.MasterGetMainApplyList(ctx, &pb.MasterGetMainApplyListReq{
		MasterUid: uid,
		Offset:    offset,
		Limit:     limit,
	})
}

func (c *GuildClient) MasterInsertMainApply(ctx context.Context, data *pb.ExchangeGuildMainApplyData) error {
	ct := c.Stub().(pb.GuildExchangeClient)
	_, err := ct.MasterInsertMainApply(ctx, data)
	return err
}

type ReasonType uint32

const (
	ReasonTypeSum    ReasonType = 4 // 汇总
	ReasonTypeSettle ReasonType = 5 // 结算
)

func (c *GuildClient) GetSettlementAttributeV2(ctx context.Context, uid, eType, offset, limit uint32, startTime, endTime uint64, rType ReasonType) (*pb.UserAllScoreList, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetSettlementAttribute(ctx, &pb.UidTimeRangeReq{
		Uid:        uid,
		StartTime:  startTime,
		EndTime:    endTime,
		Type:       eType,
		Offset:     offset,
		Limit:      limit,
		ReasonType: uint32(rType),
	})
}

func (c *GuildClient) GetExchangeAmountListType(ctx context.Context, uid, eType, offset, limit uint32) (*pb.UserAllScoreList, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetExchangeAmountListType(ctx, &pb.UidOffsetTypeReq{
		Uid:    uid,
		Type:   eType,
		Offset: offset,
		Limit:  limit,
	})
}

func (c *GuildClient) QueryUidAutoExchangeScore(ctx context.Context, exchangeType pb.ExchangeType, uidList []uint32,
	beginTime uint32, endTime uint32) (*pb.QueryUidAutoExchangeScoreResp, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	req := &pb.QueryUidAutoExchangeScoreReq{
		Type:      exchangeType,
		UidList:   uidList,
		BeginTime: beginTime,
		EndTime:   endTime,
	}
	return ct.QueryUidAutoExchangeScore(ctx, req)
}

func (c *GuildClient) ManualSettlement(ctx context.Context, masterUid uint32, exchangeType pb.ExchangeType) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	req := &pb.ManualSettlementReq{
		MasterUid: masterUid,
		XType:     exchangeType,
	}
	return ct.ManualSettlement(ctx, req)
}

func (c *GuildClient) SumAndSettlement(ctx context.Context, masterUid uint32, exchangeType pb.ExchangeType) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	req := &pb.ManualSettlementReq{
		MasterUid: masterUid,
		XType:     exchangeType,
	}
	return ct.SumAndSettlement(ctx, req)
}

func (c *GuildClient) CheckSumAndSettlementStatus(ctx context.Context, masterUid uint32, exchangeType pb.ExchangeType) (*pb.CheckSumAndSettlementStatusResp, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	req := &pb.ManualSettlementReq{
		MasterUid: masterUid,
		XType:     exchangeType,
	}
	return ct.CheckSumAndSettlementStatus(ctx, req)
}

func (c *GuildClient) GetMasterFreezeUser(ctx context.Context, masterUid uint32, exchangeType pb.ExchangeType) (*pb.GetMasterFreezeUserResp, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	req := &pb.ManualSettlementReq{
		MasterUid: masterUid,
		XType:     exchangeType,
	}
	return ct.GetMasterFreezeUser(ctx, req)
}

func (c *GuildClient) GetMasterFreezeTotal(ctx context.Context, masterUid uint32, exchangeType pb.ExchangeType) (*pb.GetMasterFreezeTotalResp, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	req := &pb.ManualSettlementReq{
		MasterUid: masterUid,
		XType:     exchangeType,
	}
	return ct.GetMasterFreezeTotal(ctx, req)
}

func (c *GuildClient) OnSignMasterFlowUpdate(ctx context.Context, flowId string, thirdOrderNo string, signResult uint32) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.OnSignMasterFlowUpdate(ctx, &pb.OnSignFlowUpdateReq{FlowId: flowId, ThirdOrderNo: thirdOrderNo, SignResult: signResult})

}

func (c *GuildClient) OnSignMasterFlowFinish(ctx context.Context, flowId string, flowStatus string) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.OnSignMasterFlowFinish(ctx, &pb.OnSignFlowFinishReq{FlowId: flowId, FlowStatus: flowStatus})
}

func (c *GuildClient) OnSignMasterDocExpire(ctx context.Context, flowId string, fileId string) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.OnSignMasterDocExpire(ctx, &pb.OnSignFlowExpireReq{FlowId: flowId, FileId: fileId})
}
