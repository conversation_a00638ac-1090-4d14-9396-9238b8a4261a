// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/exchange (interfaces: IClient)

// Package exchange is a generated GoMock package.
package exchange

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	protocol "golang.52tt.com/pkg/protocol"
	Exchange "golang.52tt.com/protocol/services/exchange"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddPrivateWithdrawLog mocks base method.
func (m *MockIClient) AddPrivateWithdrawLog(arg0 context.Context, arg1 string, arg2 uint32, arg3 uint64, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPrivateWithdrawLog", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddPrivateWithdrawLog indicates an expected call of AddPrivateWithdrawLog.
func (mr *MockIClientMockRecorder) AddPrivateWithdrawLog(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPrivateWithdrawLog", reflect.TypeOf((*MockIClient)(nil).AddPrivateWithdrawLog), arg0, arg1, arg2, arg3, arg4)
}

// BatchGetFreezeStatus mocks base method.
func (m *MockIClient) BatchGetFreezeStatus(arg0 context.Context, arg1 *Exchange.BatchGetFreezeStatusReq) (*Exchange.BatchGetFreezeStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetFreezeStatus", arg0, arg1)
	ret0, _ := ret[0].(*Exchange.BatchGetFreezeStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetFreezeStatus indicates an expected call of BatchGetFreezeStatus.
func (mr *MockIClientMockRecorder) BatchGetFreezeStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetFreezeStatus", reflect.TypeOf((*MockIClient)(nil).BatchGetFreezeStatus), arg0, arg1)
}

// BeginTransaction mocks base method.
func (m *MockIClient) BeginTransaction(arg0 context.Context, arg1 uint32, arg2 Exchange.CurrencyType, arg3, arg4 uint32, arg5 Exchange.CurrencyType, arg6 uint32) (*Exchange.Transaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BeginTransaction", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(*Exchange.Transaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BeginTransaction indicates an expected call of BeginTransaction.
func (mr *MockIClientMockRecorder) BeginTransaction(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BeginTransaction", reflect.TypeOf((*MockIClient)(nil).BeginTransaction), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// BeginTransactionEx mocks base method.
func (m *MockIClient) BeginTransactionEx(arg0 context.Context, arg1 *Exchange.Transaction) (*Exchange.Transaction, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BeginTransactionEx", arg0, arg1)
	ret0, _ := ret[0].(*Exchange.Transaction)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BeginTransactionEx indicates an expected call of BeginTransactionEx.
func (mr *MockIClientMockRecorder) BeginTransactionEx(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BeginTransactionEx", reflect.TypeOf((*MockIClient)(nil).BeginTransactionEx), arg0, arg1)
}

// CheckIfInBlacklist mocks base method.
func (m *MockIClient) CheckIfInBlacklist(arg0 context.Context, arg1, arg2 uint32) (bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfInBlacklist", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckIfInBlacklist indicates an expected call of CheckIfInBlacklist.
func (mr *MockIClientMockRecorder) CheckIfInBlacklist(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfInBlacklist", reflect.TypeOf((*MockIClient)(nil).CheckIfInBlacklist), arg0, arg1, arg2)
}

// CheckInnerWhite mocks base method.
func (m *MockIClient) CheckInnerWhite(arg0 context.Context, arg1 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckInnerWhite", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckInnerWhite indicates an expected call of CheckInnerWhite.
func (mr *MockIClientMockRecorder) CheckInnerWhite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInnerWhite", reflect.TypeOf((*MockIClient)(nil).CheckInnerWhite), arg0, arg1)
}

// CheckPrivateWithdraw mocks base method.
func (m *MockIClient) CheckPrivateWithdraw(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckPrivateWithdraw", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckPrivateWithdraw indicates an expected call of CheckPrivateWithdraw.
func (mr *MockIClientMockRecorder) CheckPrivateWithdraw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckPrivateWithdraw", reflect.TypeOf((*MockIClient)(nil).CheckPrivateWithdraw), arg0, arg1)
}

// GetCurrentMonthWithdrawRemain mocks base method.
func (m *MockIClient) GetCurrentMonthWithdrawRemain(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentMonthWithdrawRemain", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentMonthWithdrawRemain indicates an expected call of GetCurrentMonthWithdrawRemain.
func (mr *MockIClientMockRecorder) GetCurrentMonthWithdrawRemain(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentMonthWithdrawRemain", reflect.TypeOf((*MockIClient)(nil).GetCurrentMonthWithdrawRemain), arg0, arg1)
}

// GetScorePartFreezeList mocks base method.
func (m *MockIClient) GetScorePartFreezeList(arg0 context.Context, arg1 *Exchange.GetScorePartFreezeListReq) (*Exchange.GetScorePartFreezeListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScorePartFreezeList", arg0, arg1)
	ret0, _ := ret[0].(*Exchange.GetScorePartFreezeListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetScorePartFreezeList indicates an expected call of GetScorePartFreezeList.
func (mr *MockIClientMockRecorder) GetScorePartFreezeList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScorePartFreezeList", reflect.TypeOf((*MockIClient)(nil).GetScorePartFreezeList), arg0, arg1)
}

// GetSettlementHistory mocks base method.
func (m *MockIClient) GetSettlementHistory(arg0 context.Context, arg1 string, arg2 []Exchange.Status, arg3, arg4 uint32) ([]*Exchange.Transaction, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettlementHistory", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*Exchange.Transaction)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSettlementHistory indicates an expected call of GetSettlementHistory.
func (mr *MockIClientMockRecorder) GetSettlementHistory(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettlementHistory", reflect.TypeOf((*MockIClient)(nil).GetSettlementHistory), arg0, arg1, arg2, arg3, arg4)
}

// GetTransactionHistory mocks base method.
func (m *MockIClient) GetTransactionHistory(arg0 context.Context, arg1 uint32, arg2 []Exchange.Status, arg3, arg4, arg5, arg6, arg7 uint32, arg8 bool, arg9 Exchange.CurrencyType) ([]*Exchange.Transaction, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionHistory", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9)
	ret0, _ := ret[0].([]*Exchange.Transaction)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetTransactionHistory indicates an expected call of GetTransactionHistory.
func (mr *MockIClientMockRecorder) GetTransactionHistory(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionHistory", reflect.TypeOf((*MockIClient)(nil).GetTransactionHistory), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9)
}

// PrivateWithdraw mocks base method.
func (m *MockIClient) PrivateWithdraw(arg0 context.Context, arg1 *Exchange.PrivateWithdrawReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrivateWithdraw", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PrivateWithdraw indicates an expected call of PrivateWithdraw.
func (mr *MockIClientMockRecorder) PrivateWithdraw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrivateWithdraw", reflect.TypeOf((*MockIClient)(nil).PrivateWithdraw), arg0, arg1)
}
