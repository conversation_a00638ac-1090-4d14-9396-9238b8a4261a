package Exchange

import (
	"context"
	pb "golang.52tt.com/protocol/services/exchange"
)

func (c *GuildClient) GetMainList(ctx context.Context, offset uint32, limit uint32, uid uint32) (*pb.GetMainListRes, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetMainList(ctx, &pb.UidOffsetReq{
		Offset: offset,
		Limit:  limit,
		Uid:    uid,
	})
}

func (c *GuildClient) InsertMain(ctx context.Context, masterUid uint32, company string, companyCode string) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.InsertMain(ctx, &pb.SetGuildMainListReq{
		MasterUid:   masterUid,
		Company:     company,
		CompanyCode: companyCode,
	})
}

func (c *GuildClient) UpdateMain(ctx context.Context, masterUid uint32, company string, companyCode string) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.UpdateMain(ctx, &pb.SetGuildMainListReq{
		MasterUid:   masterUid,
		Company:     company,
		CompanyCode: companyCode,
	})
}

func (c *GuildClient) DelMain(ctx context.Context, masterUid uint32) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.DelMain(ctx, &pb.UidReq{
		Uid: masterUid,
	})
}

func (c *GuildClient) GetMainGuilds(ctx context.Context, masterUid uint32) (*pb.GuildList, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetMainGuilds(ctx, &pb.UidReq{
		Uid: masterUid,
	})
}

func (c *GuildClient) SetMainGuild(ctx context.Context, masterUid uint32, guildIdList []uint32) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.SetMainGuild(ctx, &pb.MainGuild{
		MasterUid:   masterUid,
		GuildIdList: guildIdList,
	})
}

func (c *GuildClient) GetMainPlaceCount(ctx context.Context, uid uint32) (*pb.GetMain, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetMainPlaceCount(ctx, &pb.UidReq{
		Uid: uid,
	})
}

func (c *GuildClient) GetMainHasPlaceCount(ctx context.Context, offset uint32, limit uint32, uid uint32) (*pb.GetMainListRes, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetMainHasPlaceCount(ctx, &pb.UidOffsetReq{
		Offset: offset,
		Limit:  limit,
		Uid:    uid,
	})
}

func (c *GuildClient) SetMainPlaceCount(ctx context.Context, masterUid uint32, placeCount uint32) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.SetMainPlaceCount(ctx, &pb.SetMainPlaceCountReq{
		MasterUid:  masterUid,
		PlaceCount: placeCount,
	})
}

func (c *GuildClient) DelMainPlaceCount(ctx context.Context, masterUid uint32) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.DelMainPlaceCount(ctx, &pb.UidReq{
		Uid: masterUid,
	})
}

func (c *GuildClient) GetMainData(ctx context.Context, masterUid uint32) (*pb.GetMain, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetMainData(ctx, &pb.UidReq{
		Uid: masterUid,
	})
}

func (c *GuildClient) GetMainOnly(ctx context.Context, masterUid uint32) (*pb.GetMain, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetMainOnly(ctx, &pb.UidReq{
		Uid: masterUid,
	})
}

func (c *GuildClient) GetMasterUid(ctx context.Context, guildId uint32) (*pb.UidRes, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetMasterUid(ctx, &pb.GuildReq{
		GuildId: guildId,
	})
}

func (c *GuildClient) GetMainAccountInfoList(ctx context.Context, company string) (*pb.OaInfoList, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.GetMainAccountInfoList(ctx, &pb.OaInfoReq{
		Company: company,
	})
}

func (c *GuildClient) BindMainAccountId(ctx context.Context, uid uint32, oaAccountId string) (*pb.EmptyMsg, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.BindMainAccountId(ctx, &pb.BindOaReq{
		Uid:         uid,
		OaAccountId: oaAccountId,
	})
}

func (c *GuildClient) BatchGetMasterUid(ctx context.Context, guildIdList []uint32) (*pb.BatchGetMasterUidResp, error) {
	ct := c.Stub().(pb.GuildExchangeClient)
	return ct.BatchGetMasterUid(ctx, &pb.BatchGetMasterUidReq{
		GuildList: guildIdList,
	})
}
