package muse_social_community_chat_channel

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/muse-social-community-chat-channel"

	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetSocialCommunityChatChannelHistoryMsg(ctx context.Context, channelId,
		count uint32) (*pb.GetSocialCommunityChatChannelHistoryMsgResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
