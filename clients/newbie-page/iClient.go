package newbiepage

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/newbiepage"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetNewbiePageConfig(ctx context.Context) (*pb.GetNewbiePageConfigResponse, error)
	SetUserNewbiePageTag(ctx context.Context, uid uint32, tagList []string) error
	GetUserNewbiePageTag(ctx context.Context, uid uint32) (*pb.GetUserNewbiePageTagResponse, error)
	GetSquareTagVersion(ctx context.Context) (*pb.GetSquareTagVersionResp, error)
	ListAllValidPageTag(ctx context.Context) (*pb.ListAllValidPageTagResp, error)
	GetPageTagById(ctx context.Context, ids []string) (*pb.GetPageTagByIdResp, error)
	GetFrequencyByBusiness(ctx context.Context, business string, addNum, expireTime uint32) (uint32, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
