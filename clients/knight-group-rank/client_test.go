package knightgrouprank

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/services/knightgrouprank"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {

	<PERSON><PERSON>("GetKnightGroupRank", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.KnightGroupRankReq
		resp, err := client.GetKnightGroupRank(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetKnightGroupRank %+v", resp)
	})

}
