package knightgrouprank

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/knightgrouprank"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetLoveRank(ctx context.Context, req *pb.GetLoveRankReq) (*pb.GetLoveRankResp, protocol.ServerError)
	GetCampInfo(ctx context.Context, req *pb.GetCampInfoReq) (*pb.GetCampInfoResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
