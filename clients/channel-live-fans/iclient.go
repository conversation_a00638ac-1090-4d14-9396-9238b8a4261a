// Code generated by quicksilver-cli. DO NOT EDIT.
package channellivefans

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/channellivefans"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AddAnchorPopularity(ctx context.Context, fansUid, anchorUid, channelId uint32) (*pb.AddAnchorPopularityResp,protocol.ServerError)
	AddFansLoveValue(ctx context.Context, fansUid, anchorUid, loveValue, channelId, missionId uint32) (*pb.AddFansLoveValueResp,protocol.ServerError)
	AddFansLoveValueV2(ctx context.Context, req *pb.AddFansLoveValueReq) (*pb.AddFansLoveValueResp,protocol.ServerError)
	AddPlateConfig(ctx context.Context, req *pb.AddPlateConfigReq) (*pb.AddPlateConfigResp,protocol.ServerError)
	BatchGetFansInfo(ctx context.Context, anchorUid uint32, uids []uint32) (map[uint32]*pb.FansInfo,protocol.ServerError)
	BatchGetFansInfoWithType(ctx context.Context, anchorUid uint32, uids []uint32, knightTypeMap map[uint32]uint32) (map[uint32]*pb.FansInfo,protocol.ServerError)
	CheckGroupNameExist(ctx context.Context, anchorUid uint32, groupName string) (*pb.CheckGroupNameExistResp,protocol.ServerError)
	CheckSetGroupNamePermit(ctx context.Context, anchorUid uint32) (*pb.CheckSetGroupNamePermitResp,protocol.ServerError)
	CheckUserIsFans(ctx context.Context, anchorUid, uid uint32) (*pb.CheckUserIsFansResp,protocol.ServerError)
	CreateFansGroup(ctx context.Context, anchorUid uint32) (*pb.CreateFansGroupResp,protocol.ServerError)
	DelGrantedPlateInfo(ctx context.Context, req *pb.DelGrantedPlateInfoReq) (*pb.DelGrantedPlateInfoResp,protocol.ServerError)
	DelPlateConfig(ctx context.Context, req *pb.DelPlateConfigReq) (*pb.DelPlateConfigResp,protocol.ServerError)
	GetAnchorFansData(ctx context.Context, anchorUid, beginTs, endTs uint32) (*pb.GetAnchorFansDataResp,protocol.ServerError)
	GetAnchorFansInfo(ctx context.Context, anchorUid uint32) (*pb.GetAnchorFansInfoResp,protocol.ServerError)
	GetAnchorValidGrantPlates(ctx context.Context, anchorUid uint32) (*pb.GetAnchorValidGrantPlatesResp,protocol.ServerError)
	GetFansAddedGroupList(ctx context.Context, fansUid, offset, limit uint32) (*pb.GetFansAddedGroupListResp,protocol.ServerError)
	GetFansCntByTs(ctx context.Context, anchorUid, ts uint32) (*pb.GetFansCntByTsResp,protocol.ServerError)
	GetFansInfo(ctx context.Context, uid, anchorUid, knightType uint32) (*pb.GetFansInfoResp,protocol.ServerError)
	GetFansRankList(ctx context.Context, anchorUid, rankType, offset, limit uint32) (*pb.GetFansRankListResp,protocol.ServerError)
	GetFansRankListV2(ctx context.Context, req *pb.GetFansRankListReq) (*pb.GetFansRankListResp,protocol.ServerError)
	GetGrantedPlateList(ctx context.Context, req *pb.GetGrantedPlateListReq) (*pb.GetGrantedPlateListResp,protocol.ServerError)
	GetPlateConfigById(ctx context.Context, req *pb.GetPlateConfigByIdReq) (*pb.GetPlateConfigByIdResp,protocol.ServerError)
	GetPlateConfigList(ctx context.Context, req *pb.GetPlateConfigListReq) (*pb.GetPlateConfigListResp,protocol.ServerError)
	GetUserFansGiftPri(ctx context.Context, req *pb.GetUserFansGiftPriReq) (*pb.GetUserFansGiftPriResp,protocol.ServerError)
	GrantAnchorPlate(ctx context.Context, req *pb.GrantAnchorPlateReq) (*pb.GrantAnchorPlateResp,protocol.ServerError)
	GrantFansGiftPrivilege(ctx context.Context, req *pb.GrantFansGiftPrivilegeReq) (*pb.GrantFansGiftPrivilegeResp,protocol.ServerError)
	JoinFansGroup(ctx context.Context, req *pb.JoinFansGroupReq) (*pb.JoinFansGroupResp,protocol.ServerError)
	LeaveFansGroup(ctx context.Context, req *pb.LeaveFansGroupReq) (*pb.LeaveFansGroupResp,protocol.ServerError)
	ResetFansGroup(ctx context.Context, req *pb.ResetFansGroupReq) (*pb.ResetFansGroupResp,protocol.ServerError)
	SendFansGiftCheck(ctx context.Context, req *pb.SendFansGiftCheckReq) (*pb.SendFansGiftCheckResp,protocol.ServerError)
	SetFansGroupName(ctx context.Context, anchorUid uint32, groupName, ip, deviceId, smDeviceId string) (*pb.SetFansGroupNameResp,protocol.ServerError)
	SetFansMissionFinishStatus(ctx context.Context, anchorUid, fansUid uint32) (*pb.SetFansMissionFinishStatusResp,protocol.ServerError)
	UpdateGrantedPlateInfo(ctx context.Context, req *pb.UpdateGrantedPlateInfoReq) (*pb.UpdateGrantedPlateInfoResp,protocol.ServerError)
	UpdatePlateConfig(ctx context.Context, req *pb.UpdatePlateConfigReq) (*pb.UpdatePlateConfigResp,protocol.ServerError)
	WearAnchorPlate(ctx context.Context, req *pb.WearAnchorPlateReq) (*pb.WearAnchorPlateResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
