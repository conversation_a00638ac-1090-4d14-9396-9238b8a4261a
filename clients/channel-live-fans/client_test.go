package channellivefans

import (
	"fmt"
	"testing"
)

func init() {
	//grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func scoreChange(score uint64) {
	tS := score << 32
	oS := uint32((tS >> 32) & (0x00000000ffffffff))
	fmt.Printf("%v %v %v \n", score, oS, tS)
}

func TestGetHelloWorld(t *testing.T) {

	scoreChange(8000)
	scoreChange(10000)
	scoreChange(11000)

	fmt.Println(uint32(34359738368000>>32) & (0x00000000ffffffff))
}
