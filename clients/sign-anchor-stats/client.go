package sign_anchor_stats

import (
	"context"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/sign-anchor-stats"
	"google.golang.org/grpc"
)

const (
	serviceName = "sign-anchor-stats"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewSignAnchorStatsClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.SignAnchorStatsClient { return c.Stub().(pb.SignAnchorStatsClient) }

func (c *Client) GetMultiThisMonthChannelStat(ctx context.Context, req *pb.GetMultiThisMonthChannelStatReq) (
	*pb.GetMultiThisMonthChannelStatResp, protocol.ServerError) {
	r, err := c.typedStub().GetMultiThisMonthChannelStat(ctx, req)
	log.Debugf("GetMultiThisMonthChannelStat resp:%d, %+v", r, err)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetMultiAnchorMonthStat(ctx context.Context, req *pb.GetMultiAnchorMonthStatReq) (
	*pb.GetMultiAnchorMonthStatResp, protocol.ServerError) {
	r, err := c.typedStub().GetMultiAnchorMonthStat(ctx, req)
	log.Debugf("GetMultiAnchorMonthStat resp:%d, %+v", r, err)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetMultiAnchorChannelStat(ctx context.Context, req *pb.GetMultiAnchorChannelStatReq) (
	*pb.GetMultiAnchorChannelStatResp, protocol.ServerError) {
	r, err := c.typedStub().GetMultiAnchorChannelStat(ctx, req)
	log.Debugf("GetMultiAnchorChannelStat resp:%d, %+v", r, err)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetUserTbeanConsume(ctx context.Context, uid, beginTs, endTs uint32) (uint32, error) {
	r, err := c.typedStub().GetUserTbeanConsume(ctx, &pb.GetUserTbeanConsumeReq{Uid: uid, BeginTs: beginTs, EndTs: endTs})
	return r.GetTotal(), err
}

func (c *Client) GetMultiAnchorDailyStatsListByGuildId(ctx context.Context, in *pb.GetMultiAnchorDailyStatsListByGuildIdReq) (*pb.GetMultiAnchorDailyStatsListByGuildIdResp, error) {
	r, err := c.typedStub().GetMultiAnchorDailyStatsListByGuildId(ctx, in)
	return r, err
}

func (c *Client) GetPgcMonthlyInfoList(ctx context.Context, in *pb.GetPgcMonthlyInfoListReq) (*pb.GetPgcMonthlyInfoListResp, error) {
	r, err := c.typedStub().GetPgcMonthlyInfoList(ctx, in)
	return r, err
}

func (c *Client) GetPgcDailyInfoList(ctx context.Context, in *pb.GetPgcDailyInfoListReq) (*pb.GetPgcDailyInfoListResp, error) {
	r, err := c.typedStub().GetPgcDailyInfoList(ctx, in)
	return r, err
}

func (c *Client) GetGuildMonthlyStatsInfoList(ctx context.Context, in *pb.GetGuildMonthlyStatsInfoListReq) (*pb.GetGuildMonthlyStatsInfoListResp, error) {
	r, err := c.typedStub().GetGuildMonthlyStatsInfoList(ctx, in)
	return r, err
}

func (c *Client) GetMultiAnchorDailyStatsList(ctx context.Context, in *pb.GetMultiAnchorDailyStatsListReq) (*pb.GetMultiAnchorDailyStatsListResp, error) {
	r, err := c.typedStub().GetMultiAnchorDailyStatsList(ctx, in)
	return r, err
}

func (c *Client) GetMultiPlayerHomepage(ctx context.Context, in *pb.GetMultiPlayerHomepageReq) (*pb.GetMultiPlayerHomepageResp, protocol.ServerError) {
	r, err := c.typedStub().GetMultiPlayerHomepage(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetMultiPlayerBaseInfo(ctx context.Context, in *pb.GetMultiPlayerBaseInfoReq) (*pb.GetMultiPlayerBaseInfoResp, protocol.ServerError) {
	r, err := c.typedStub().GetMultiPlayerBaseInfo(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetMultiPlayerMonthConsumeTop10(ctx context.Context, in *pb.GetMultiPlayerMonthConsumeTop10Req) (*pb.GetMultiPlayerMonthConsumeTop10Resp, protocol.ServerError) {
	r, err := c.typedStub().GetMultiPlayerMonthConsumeTop10(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetMultiPlayerMonthCommunityInfo(ctx context.Context, in *pb.GetMultiPlayerMonthCommunityInfoReq) (*pb.GetMultiPlayerMonthCommunityInfoResp, protocol.ServerError) {
	r, err := c.typedStub().GetMultiPlayerMonthCommunityInfo(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) CheckUserInteractEntry(ctx context.Context, in *pb.CheckUserInteractEntryReq) (*pb.CheckUserInteractEntryResp, protocol.ServerError) {
	r, err := c.typedStub().CheckUserInteractEntry(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetUserInteractInfo(ctx context.Context, in *pb.GetUserInteractInfoReq) (*pb.GetUserInteractInfoResp, protocol.ServerError) {
	r, err := c.typedStub().GetUserInteractInfo(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetUserInteractViewPer(ctx context.Context, in *pb.GetUserInteractViewPerReq) (*pb.GetUserInteractViewPerResp, protocol.ServerError) {
	r, err := c.typedStub().GetUserInteractViewPer(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) SetUserInteractViewPer(ctx context.Context, in *pb.SetUserInteractViewPerReq) (*pb.SetUserInteractViewPerResp, protocol.ServerError) {
	r, err := c.typedStub().SetUserInteractViewPer(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetHallTask(ctx context.Context, in *pb.GetHallTaskReq) (*pb.GetHallTaskResp, protocol.ServerError) {
	r, err := c.typedStub().GetHallTask(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetHallTaskHistory(ctx context.Context, in *pb.GetHallTaskHistoryReq) (*pb.GetHallTaskHistoryResp, protocol.ServerError) {
	r, err := c.typedStub().GetHallTaskHistory(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetGuildMultiPlayerHall(ctx context.Context, guildId uint32) (*pb.GetGuildMultiPlayerHallResp, protocol.ServerError) {
	r, err := c.typedStub().GetGuildMultiPlayerHall(ctx, &pb.GetGuildMultiPlayerHallReq{GuildId: guildId})
	return r, protocol.ToServerError(err)
}

// 公会查询任务配置信息
func (c *Client) GetHallTaskConfList(ctx context.Context, in *pb.GetHallTaskConfListReq) (*pb.GetHallTaskConfListResp, error) {
	r, err := c.typedStub().GetHallTaskConfList(ctx, in)
	return r, protocol.ToServerError(err)
}

// 公会配置任务
func (c *Client) AddHallTaskConf(ctx context.Context, in *pb.AddHallTaskConfReq) (*pb.AddHallTaskConfResp, protocol.ServerError) {
	r, err := c.typedStub().AddHallTaskConf(ctx, in)
	return r, protocol.ToServerError(err)
}

// 公会删除任务
func (c *Client) DelHallTaskConf(ctx context.Context, in *pb.DelHallTaskConfReq) (*pb.DelHallTaskConfResp, error) {
	r, err := c.typedStub().DelHallTaskConf(ctx, in)
	return r, protocol.ToServerError(err)
}

// 公会分配任务
func (c *Client) DistributeHallTask(ctx context.Context, in *pb.DistributeHallTaskReq) (*pb.DistributeHallTaskResp, error) {
	r, err := c.typedStub().DistributeHallTask(ctx, in)
	return r, protocol.ToServerError(err)
}

// 公会查询分配任务
func (c *Client) GetGuildHallTask(ctx context.Context, in *pb.GetGuildHallTaskReq) (*pb.GetGuildHallTaskResp, error) {
	r, err := c.typedStub().GetGuildHallTask(ctx, in)
	return r, protocol.ToServerError(err)
}

// 公会查询厅数据
func (c *Client) GetGuildHallTaskStats(ctx context.Context, in *pb.GetGuildHallTaskStatsReq) (*pb.GetGuildHallTaskStatsResp, error) {
	r, err := c.typedStub().GetGuildHallTaskStats(ctx, in)
	return r, protocol.ToServerError(err)
}

// 公会查询当天接档成员详情
func (c *Client) GetGuildHallTaskStatsDetial(ctx context.Context, in *pb.GetGuildHallTaskStatsDetialReq) (*pb.GetGuildHallTaskStatsDetialResp, error) {
	r, err := c.typedStub().GetGuildHallTaskStatsDetial(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetHallTaskDistributeHistory(ctx context.Context, uid uint32) (*pb.GetHallTaskDistributeHistoryResp, error) {
	r, err := c.typedStub().GetHallTaskDistributeHistory(ctx, &pb.GetHallTaskDistributeHistoryReq{Uid: uid})
	return r, protocol.ToServerError(err)
}

func (c *Client) GetHallTaskCacheInfo(ctx context.Context, uid, cid uint32) (*pb.GetHallTaskResp, protocol.ServerError) {
	r, err := c.typedStub().GetHallTaskCacheInfo(ctx, &pb.GetHallTaskReq{Uid: uid, ChannelId: cid})
	return r, protocol.ToServerError(err)
}

func (c *Client) DelHallTask(ctx context.Context, guildId, uid, opUid uint32) (*pb.DelHallTaskResp, protocol.ServerError) {
	r, err := c.typedStub().DelHallTask(ctx, &pb.DelHallTaskReq{GuildId: guildId, Uid: uid, OpUid: opUid})
	return r, protocol.ToServerError(err)
}

func (c *Client) ListMultiPlayerHall(ctx context.Context, req *pb.ListMultiPlayerHallReq) (*pb.ListMultiPlayerHallResp, protocol.ServerError) {
	r, err := c.typedStub().ListMultiPlayerHall(ctx, req)
	return r, protocol.ToServerError(err)
}

// 查询时间范围内在指定房间有效接档的签约成员uid
func (c *Client) GetValidHoldDayUid(ctx context.Context, req *pb.GetValidHoldDayUidReq) (*pb.GetValidHoldDayUidResp, protocol.ServerError) {
	r, err := c.typedStub().GetValidHoldDayUid(ctx, req)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetHallTaskConfById(ctx context.Context, req *pb.GetHallTaskConfByIdReq) (*pb.GetHallTaskConfByIdResp, protocol.ServerError) {
	r, err := c.typedStub().GetHallTaskConfById(ctx, req)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetBindGuildInfo(ctx context.Context, req *pb.GetBindGuildInfoReq) (*pb.GetBindGuildInfoResp, protocol.ServerError) {
	r, err := c.typedStub().GetBindGuildInfo(ctx, req)
	return r, protocol.ToServerError(err)
}

func (c *Client) BatchGetBindGuildInfo(ctx context.Context, req *pb.BatchGetBindGuildInfoReq) (*pb.BatchGetBindGuildInfoResp, protocol.ServerError) {
	r, err := c.typedStub().BatchGetBindGuildInfo(ctx, req)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetBindGuildInfoList(ctx context.Context, req *pb.GetBindGuildInfoListReq) (*pb.GetBindGuildInfoListResp, protocol.ServerError) {
	r, err := c.typedStub().GetBindGuildInfoList(ctx, req)
	return r, protocol.ToServerError(err)
}
