// Code generated by quicksilver-cli. DO NOT EDIT.
package sign_anchor_stats

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/sign-anchor-stats"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AddHallTaskConf(ctx context.Context, in *pb.AddHallTaskConfReq) (*pb.AddHallTaskConfResp,protocol.ServerError)
	CheckUserInteractEntry(ctx context.Context, in *pb.CheckUserInteractEntryReq) (*pb.CheckUserInteractEntryResp,protocol.ServerError)
	DelHallTask(ctx context.Context, guildId, uid, opUid uint32) (*pb.DelHallTaskResp,protocol.ServerError)
	DelHallTaskConf(ctx context.Context, in *pb.DelHallTaskConfReq) (*pb.DelHallTaskConfResp,error)
	DistributeHallTask(ctx context.Context, in *pb.DistributeHallTaskReq) (*pb.DistributeHallTaskResp,error)
	GetBindGuildInfo(ctx context.Context, req *pb.GetBindGuildInfoReq) (*pb.GetBindGuildInfoResp,protocol.ServerError)
	BatchGetBindGuildInfo(ctx context.Context, req *pb.BatchGetBindGuildInfoReq) (*pb.BatchGetBindGuildInfoResp,protocol.ServerError)
	GetGuildHallTask(ctx context.Context, in *pb.GetGuildHallTaskReq) (*pb.GetGuildHallTaskResp,error)
	GetGuildHallTaskStats(ctx context.Context, in *pb.GetGuildHallTaskStatsReq) (*pb.GetGuildHallTaskStatsResp,error)
	GetGuildHallTaskStatsDetial(ctx context.Context, in *pb.GetGuildHallTaskStatsDetialReq) (*pb.GetGuildHallTaskStatsDetialResp,error)
	GetGuildMonthlyStatsInfoList(ctx context.Context, in *pb.GetGuildMonthlyStatsInfoListReq) (*pb.GetGuildMonthlyStatsInfoListResp,error)
	GetGuildMultiPlayerHall(ctx context.Context, guildId uint32) (*pb.GetGuildMultiPlayerHallResp,protocol.ServerError)
	GetHallTask(ctx context.Context, in *pb.GetHallTaskReq) (*pb.GetHallTaskResp,protocol.ServerError)
	GetHallTaskCacheInfo(ctx context.Context, uid, cid uint32) (*pb.GetHallTaskResp,protocol.ServerError)
	GetHallTaskConfById(ctx context.Context, req *pb.GetHallTaskConfByIdReq) (*pb.GetHallTaskConfByIdResp,protocol.ServerError)
	GetHallTaskConfList(ctx context.Context, in *pb.GetHallTaskConfListReq) (*pb.GetHallTaskConfListResp,error)
	GetHallTaskDistributeHistory(ctx context.Context, uid uint32) (*pb.GetHallTaskDistributeHistoryResp,error)
	GetHallTaskHistory(ctx context.Context, in *pb.GetHallTaskHistoryReq) (*pb.GetHallTaskHistoryResp,protocol.ServerError)
	GetMultiAnchorChannelStat(ctx context.Context, req *pb.GetMultiAnchorChannelStatReq) (*pb.GetMultiAnchorChannelStatResp,protocol.ServerError)
	GetMultiAnchorDailyStatsList(ctx context.Context, in *pb.GetMultiAnchorDailyStatsListReq) (*pb.GetMultiAnchorDailyStatsListResp,error)
	GetMultiAnchorDailyStatsListByGuildId(ctx context.Context, in *pb.GetMultiAnchorDailyStatsListByGuildIdReq) (*pb.GetMultiAnchorDailyStatsListByGuildIdResp,error)
	GetMultiAnchorMonthStat(ctx context.Context, req *pb.GetMultiAnchorMonthStatReq) (*pb.GetMultiAnchorMonthStatResp,protocol.ServerError)
	GetMultiPlayerBaseInfo(ctx context.Context, in *pb.GetMultiPlayerBaseInfoReq) (*pb.GetMultiPlayerBaseInfoResp,protocol.ServerError)
	GetMultiPlayerHomepage(ctx context.Context, in *pb.GetMultiPlayerHomepageReq) (*pb.GetMultiPlayerHomepageResp,protocol.ServerError)
	GetMultiPlayerMonthCommunityInfo(ctx context.Context, in *pb.GetMultiPlayerMonthCommunityInfoReq) (*pb.GetMultiPlayerMonthCommunityInfoResp,protocol.ServerError)
	GetMultiPlayerMonthConsumeTop10(ctx context.Context, in *pb.GetMultiPlayerMonthConsumeTop10Req) (*pb.GetMultiPlayerMonthConsumeTop10Resp,protocol.ServerError)
	GetMultiThisMonthChannelStat(ctx context.Context, req *pb.GetMultiThisMonthChannelStatReq) (*pb.GetMultiThisMonthChannelStatResp,protocol.ServerError)
	GetPgcDailyInfoList(ctx context.Context, in *pb.GetPgcDailyInfoListReq) (*pb.GetPgcDailyInfoListResp,error)
	GetPgcMonthlyInfoList(ctx context.Context, in *pb.GetPgcMonthlyInfoListReq) (*pb.GetPgcMonthlyInfoListResp,error)
	GetUserInteractInfo(ctx context.Context, in *pb.GetUserInteractInfoReq) (*pb.GetUserInteractInfoResp,protocol.ServerError)
	GetUserInteractViewPer(ctx context.Context, in *pb.GetUserInteractViewPerReq) (*pb.GetUserInteractViewPerResp,protocol.ServerError)
	GetUserTbeanConsume(ctx context.Context, uid, beginTs, endTs uint32) (uint32,error)
	GetValidHoldDayUid(ctx context.Context, req *pb.GetValidHoldDayUidReq) (*pb.GetValidHoldDayUidResp,protocol.ServerError)
	ListMultiPlayerHall(ctx context.Context, req *pb.ListMultiPlayerHallReq) (*pb.ListMultiPlayerHallResp,protocol.ServerError)
	SetUserInteractViewPer(ctx context.Context, in *pb.SetUserInteractViewPerReq) (*pb.SetUserInteractViewPerResp,protocol.ServerError)
	GetBindGuildInfoList(ctx context.Context, req *pb.GetBindGuildInfoListReq) (*pb.GetBindGuildInfoListResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
