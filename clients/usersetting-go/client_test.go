package usersettinggo

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/services/usersettinggo"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {

	Convey("SetSettings", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.SetSettingsReq
		resp, err := client.SetSettings(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("SetSettings %+v", resp)
	})

}
