package mood

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/ugc/mood"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetAllValidMood(ctx context.Context, in *pb.GetAllValidMoodReq) (*pb.GetAllValidMoodResp, protocol.ServerError)
	GetMoods(ctx context.Context, in *pb.GetMoodsReq) (*pb.GetMoodsResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
