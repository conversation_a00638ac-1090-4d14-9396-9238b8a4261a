package activity_stream

import (
	"context"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"

	pb "golang.52tt.com/protocol/services/ugc/activity_stream"
	"google.golang.org/grpc"
)

const (
	serviceName = "ugc-activity-stream"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewActivityStreamClient(cc)
			}, dopts...,
		),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.ActivityStreamClient { return c.Stub().(pb.ActivityStreamClient) }

func (c *Client) GetFeed(ctx context.Context, group, id string) Feed {
	return Feed{
		Feed: pb.Feed{Group: group, Id: id},
		ctx:  ctx, cli: c,
	}
}

func (c *Client) GetFeeds(ctx context.Context, group string, idList ...string) IFeeds {
	return Feeds{
		Feeds: pb.Feeds{Group: group, IdList: idList},
		ctx:   ctx, cli: c,
	}
}
