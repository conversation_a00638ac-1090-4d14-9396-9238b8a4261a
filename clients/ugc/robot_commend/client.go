package robotcommend

import (
	"context"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/ugc/robotcommend"
	"google.golang.org/grpc"
)

const (
	serviceName = "robot-commend"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewRobotCommendClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.RobotCommendClient { return c.Stub().(pb.RobotCommendClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetRobotCommend(ctx context.Context, req pb.GetRobotCommendsReq) (pb.GetRobotCommendsResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRobotCommends(ctx, &req)
	return *resp, protocol.ToServerError(err)
}

func (c *Client) AddRobotCommends(ctx context.Context, req pb.AddRobotCommendsReq) (pb.AddRobotCommendsResp, protocol.ServerError) {
	resp, err := c.typedStub().AddRobotCommends(ctx, &req)
	return *resp, protocol.ToServerError(err)
}

func (c *Client) DelRobotCommend(ctx context.Context, req pb.DelRobotCommendReq) (pb.DelRobotCommendResp, protocol.ServerError) {
	resp, err := c.typedStub().DelRobotCommend(ctx, &req)
	return *resp, protocol.ToServerError(err)
}
