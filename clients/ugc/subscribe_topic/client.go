package subscribe_topic

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	"golang.52tt.com/protocol/services/ugc/subscribe_topic"
)

const (
	serviceName = "ugc-user-behavior"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return subscribe_topic.NewSubscribeClient(cc)
		}, dopts...),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() subscribe_topic.SubscribeClient {
	return c.Stub().(subscribe_topic.SubscribeClient)
}

func (c *Client) SubscribeTopic(ctx context.Context, in *subscribe_topic.SubscribeReq, opts ...grpc.CallOption) (*subscribe_topic.SubscribeTopicResp, protocol.ServerError) {
	resp, err := c.typedStub().SubscribeTopic(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UnsubscribeTopic(ctx context.Context, in *subscribe_topic.SubscribeReq, opts ...grpc.CallOption) (*subscribe_topic.SubscribeTopicResp, protocol.ServerError) {
	resp, err := c.typedStub().UnsubscribeTopic(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HadSubscribeTopic(ctx context.Context, in *subscribe_topic.SubscribeReq, opts ...grpc.CallOption) (*subscribe_topic.HadSubscribeTopicResp, protocol.ServerError) {
	resp, err := c.typedStub().HadSubscribeTopic(ctx, in)

	return resp, protocol.ToServerError(err)
}

func (c *Client) ListSubscribeTopic(ctx context.Context, in *subscribe_topic.ListSubscribeReq, opts ...grpc.CallOption) (*subscribe_topic.ListSubscribeResp, error) {
	return c.typedStub().ListSubscribeTopic(ctx, in)
}

//话题的帖子查看次数上报
func (c *Client) ReportTopicViewCount(ctx context.Context, in *subscribe_topic.ReportTopicViewCountReq,
	opts ...grpc.CallOption) (*subscribe_topic.ReportTopicViewCountRsp, protocol.ServerError) {
	if resp, err := c.typedStub().ReportTopicViewCount(ctx, in); err != nil {
		log.Errorf("ReportTopicViewCount %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

//上报最近观看
func (c *Client) ReportRecentViewTopic(ctx context.Context, in *subscribe_topic.ReportRecentViewTopicReq,
	opts ...grpc.CallOption) (*subscribe_topic.ReportRecentViewTopicRsp, protocol.ServerError) {
	if resp, err := c.typedStub().ReportRecentViewTopic(ctx, in); err != nil {
		log.Errorf("ReportRecentViewTopic %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

//话题帖子的查看次数查询
func (c *Client) GetTopicViewCount(ctx context.Context, in *subscribe_topic.GetTopicViewCountReq,
	opts ...grpc.CallOption) (*subscribe_topic.GetTopicViewCountRsp, protocol.ServerError) {
	if resp, err := c.typedStub().GetTopicViewCount(ctx, in); err != nil {
		log.Errorf("GetTopicViewCount %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

//话题帖子的查看次数查询
func (c *Client) BatGetTopicViewCount(ctx context.Context, in *subscribe_topic.BatGetTopicViewCountReq,
	opts ...grpc.CallOption) (*subscribe_topic.BatGetTopicViewCountRsp, protocol.ServerError) {
	if resp, err := c.typedStub().BatGetTopicViewCount(ctx, in); err != nil {
		log.Errorf("BatGetTopicViewCount %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

//最近查看列表
func (c *Client) ListRecentViewTopic(ctx context.Context, in *subscribe_topic.ListRecentViewTopicReq,
	opts ...grpc.CallOption) (*subscribe_topic.ListRecentViewTopicRsp, protocol.ServerError) {
	if resp, err := c.typedStub().ListRecentViewTopic(ctx, in); err != nil {
		log.Errorf("ListRecentViewTopic %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}
