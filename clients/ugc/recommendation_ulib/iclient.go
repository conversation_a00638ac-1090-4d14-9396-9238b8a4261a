package recommendation_ulib

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/ugc/recommendation_ulib"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetFocusUserByLibrary(ctx context.Context, uid uint32, needTag []string, gameTag []string, gender uint32, filter *pb.FilterOption) (*pb.GetFocusUserByLibraryResp, error)
	SetUserOnlineStatus(ctx context.Context, uid uint32, userStatus uint32) (*pb.SetUserOnlineStatusResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
