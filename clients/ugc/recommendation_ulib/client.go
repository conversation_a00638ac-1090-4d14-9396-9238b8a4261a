package recommendation_ulib

import (
	"context"
	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/ugc/recommendation_ulib"
	"google.golang.org/grpc"
)

const (
	serviceName = "ugc-recommendation-ulib"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewUgcRecommendationUserLibraryClient(cc)
			},
			dopts...,
		),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.UgcRecommendationUserLibraryClient {
	return c.Stub().(pb.UgcRecommendationUserLibraryClient)
}

func (c *Client) GetFocusUserByLibrary(ctx context.Context, uid uint32, needTag []string, gameTag []string, gender uint32, filter *pb.FilterOption) (*pb.GetFocusUserByLibraryResp, error)  {
	in := &pb.GetFocusUserByLibraryReq{Condition:&pb.QueryUserOption{UserId:uid, NeedTag:needTag, GameTag:gameTag, Gender:gender}, Filter:filter}
	resp, err := c.typedStub().GetFocusUserByLibrary(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetUserOnlineStatus(ctx context.Context, uid uint32, userStatus uint32) (*pb.SetUserOnlineStatusResp, error) {
	in := &pb.SetUserOnlineStatusReq{UserId: uid, Status: userStatus}
	resp, err := c.typedStub().SetUserOnlineStatus(ctx, in)
	return resp, protocol.ToServerError(err)
}







