package friendship

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"os"
	"testing"
	"time"

	"golang.52tt.com/pkg/foundation/utils"

	pb "golang.52tt.com/protocol/services/ugc/friendship"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"

	. "github.com/smartystreets/goconvey/convey"
)

var (
	c *Client

	rs = rand.NewSource(time.Now().UnixNano())
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))

	nc, err := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("ugc-friendship.52tt.local"))
	if err != nil {
		log.Fatalf("Failed to create client %+v", err)
	}
	c = nc
}

func Test_ModifyUserRelations(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*15)
	defer cancel()

	var A, B uint32 = 500001, 500002

	var (
		followingA, followerB uint32
	)

	Convey("initialize and get counts", t, func() {
		var err error
		_, err = c.HardDeleteFollowing(ctx, A, B)
		So(err, ShouldBeNil)

		followingA, _, err = c.GetUserCounts(ctx, A)
		So(err, ShouldBeNil)

		_, followerB, err = c.GetUserCounts(ctx, B)
		So(err, ShouldBeNil)
	})

	Convey("Follow and check counts", t, func() {
		affected, first, err := c.FollowUserSimple(ctx, A, B, 1)
		So(err, ShouldBeNil)
		So(affected, ShouldBeTrue)
		So(first, ShouldBeTrue)

		followingA2, _, err := c.GetUserCounts(ctx, A)
		So(err, ShouldBeNil)
		So(followingA2, ShouldEqual, followingA+1)

		_, followerB2, err := c.GetUserCounts(ctx, B)
		So(err, ShouldBeNil)
		So(followerB2, ShouldEqual, followerB+1)
	})

	Convey("Unfollow and check counts", t, func() {
		// 取消关注的seq不大于关注时的seq，不应该生效
		affected, err := c.UnfollowUser(ctx, A, B, 1)
		So(err, ShouldBeNil)
		So(affected, ShouldBeFalse)

		affected, err = c.UnfollowUser(ctx, A, B, 2)
		So(err, ShouldBeNil)
		So(affected, ShouldBeTrue)

		followingA3, _, err := c.GetUserCounts(ctx, A)
		So(err, ShouldBeNil)
		So(followingA3, ShouldEqual, followingA)

		_, followerB3, err := c.GetUserCounts(ctx, B)
		So(err, ShouldBeNil)
		So(followerB3, ShouldEqual, followerB)
	})

	Convey("Follow again and check counts", t, func() {
		// 重新关注的seq不大于上次取消关注时的seq，不应该生效
		affected, first, err := c.FollowUserSimple(ctx, A, B, 2)
		So(err, ShouldBeNil)
		So(affected, ShouldBeFalse)
		So(first, ShouldBeFalse)

		// seq+1，生效但不是第一次关注
		affected, first, err = c.FollowUserSimple(ctx, A, B, 3)
		So(err, ShouldBeNil)
		So(affected, ShouldBeTrue)
		So(first, ShouldBeFalse)

		followingA4, _, err := c.GetUserCounts(ctx, A)
		So(err, ShouldBeNil)
		So(followingA4, ShouldEqual, followingA+1)

		_, followerB4, err := c.GetUserCounts(ctx, B)
		So(err, ShouldBeNil)
		So(followerB4, ShouldEqual, followerB+1)
	})
}

func Test_FollowingList(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*60)
	defer cancel()

	var A uint32 = 500002
	var followingA uint32

	Convey("init at least 50 followings", t, func() {
		var err error
		followingA, _, err = c.GetUserCounts(ctx, A)
		So(err, ShouldBeNil)
		if followingA < 50 {
			var seq uint64 = 1000
			for uid := uint32(800000); uid < 800050; uid++ {
				_, _, err := c.FollowUserSimple(ctx, A, uid, seq)
				So(err, ShouldBeNil)
				seq++
			}
		}
		followingA, _, err = c.GetUserCounts(ctx, A)
		So(err, ShouldBeNil)
	})

	// 按时间分页拉取
	Convey("list by create_at asc/desc", t, func() {
		var resultAsc, resultDesc []*pb.Following

		asc := NewLoadMoreByID(SortAscending, "")

		count := uint32(rs.Int63()%10 + 5)
		for {
			followings, next, err := c.GetUserFollowingList(ctx, A, false, asc, count)
			So(err, ShouldBeNil)

			asc = next
			t.Logf("next: %v", next)
			resultAsc = append(resultAsc, followings...)
			if asc == nil {
				break
			}
		}

		count = uint32(rs.Int63()%10 + 5)
		desc := NewLoadMoreByID(SortAscending, "")
		for {
			followings, next, err := c.GetUserFollowingList(ctx, A, false, desc, count)
			So(err, ShouldBeNil)

			desc = next
			t.Logf("next: %v", next)
			resultDesc = append(resultDesc, followings...)
			if desc == nil {
				break
			}
		}

		So(len(resultAsc), ShouldEqual, followingA)      // 列表长度应该与counts相等
		So(len(resultAsc), ShouldEqual, len(resultDesc)) // 不同顺序结果长度应该相等

		i, j := 0, len(resultAsc)-1
		for {
			if j < i {
				break
			}

			So(resultAsc[i], ShouldResemble, resultDesc[j])

			i++
			j--
		}
	})

	Convey("list by seq asc/desc", t, func() {
		var resultAsc, resultDesc []*pb.Following

		asc := NewLoadMoreBySequenceId(SortAscending, 0)

		count := uint32(rs.Int63()%10 + 5)
		for {
			followings, next, err := c.GetUserFollowingList(ctx, A, true, asc, count)
			So(err, ShouldBeNil)

			asc = next
			t.Logf("next: %v", next)
			resultAsc = append(resultAsc, followings...)
			if asc == nil {
				break
			}
		}

		count = uint32(rs.Int63()%10 + 5)
		desc := NewLoadMoreBySequenceId(SortDescending, 0)
		for {
			followings, next, err := c.GetUserFollowingList(ctx, A, true, desc, count)
			So(err, ShouldBeNil)

			desc = next
			t.Logf("next: %v", next)
			resultDesc = append(resultDesc, followings...)
			if desc == nil {
				break
			}
		}

		So(len(resultAsc), ShouldEqual, followingA)      // 列表长度应该与counts相等
		So(len(resultAsc), ShouldEqual, len(resultDesc)) // 不同顺序结果长度应该相等

		i, j := 0, len(resultAsc)-1
		for {
			if j < i {
				break
			}

			So(resultAsc[i], ShouldResemble, resultDesc[j])

			i++
			j--
		}
	})
}

func Test_FollowerList(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*60)
	defer cancel()

	var B uint32 = 600002
	var followerB uint32

	Convey("init at least 50 followers", t, func() {
		var err error
		_, followerB, err = c.GetUserCounts(ctx, B)
		So(err, ShouldBeNil)
		if followerB < 50 {
			var seq uint64 = 2000
			for uid := uint32(500000); uid < 500050; uid++ {
				_, _, err := c.FollowUserSimple(ctx, uid, B, seq)
				So(err, ShouldBeNil)
				seq++
			}
		}
		_, followerB, err = c.GetUserCounts(ctx, B)
		So(err, ShouldBeNil)
	})

	// 按时间分页拉取
	Convey("list by create_at asc/desc", t, func() {
		var resultAsc, resultDesc []*pb.Following

		asc := NewLoadMoreByID(SortAscending, "")

		count := uint32(rs.Int63()%10 + 5)
		for {
			followings, next, err := c.GetUserFollowerList(ctx, B, false, asc, count)
			So(err, ShouldBeNil)

			asc = next
			t.Logf("next: %v", next)
			resultAsc = append(resultAsc, followings...)
			if asc == nil {
				break
			}
		}

		count = uint32(rs.Int63()%10 + 5)
		desc := NewLoadMoreByID(SortAscending, "")
		for {
			followings, next, err := c.GetUserFollowerList(ctx, B, false, desc, count)
			So(err, ShouldBeNil)

			desc = next
			t.Logf("next: %v", next)
			resultDesc = append(resultDesc, followings...)
			if desc == nil {
				break
			}
		}

		So(len(resultAsc), ShouldEqual, followerB)       // 列表长度应该与counts相等
		So(len(resultAsc), ShouldEqual, len(resultDesc)) // 不同顺序结果长度应该相等

		i, j := 0, len(resultAsc)-1
		for {
			if j < i {
				break
			}

			So(resultAsc[i], ShouldResemble, resultDesc[j])

			i++
			j--
		}
	})
}

func Test_BatchGetBiFollowings(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*60)
	defer cancel()

	var (
		uid     = uint32(500001)
		testSet = []uint32{1, 2, 3, 4, 5, 500002, 500003, 1960099, 1968515, 1967961}
	)

	m1, m2, err := c.BatchGetBiFollowing(ctx, uid, testSet, true, true)
	if err != nil {
		t.Fatalf("Failed to BatchGetBiFollowing: %v", err)
	}

	t.Logf("followings=%v", m1)
	t.Logf("followers=%v", m2)
}

func TestClient_GetBiFollowing(t *testing.T) {

	Convey("TestClient_GetBiFollowing", t, func() {

		f1, f2, err := c.GetBiFollowing(context.Background(), 2219118, 2201286, false)
		So(err, ShouldBeNil)

		t.Logf("%+v", f1)
		t.Logf("%+v", f2)

		f3, f4, err := c.GetBiFollowing(context.Background(), 2219118, 2201286, true)
		So(err, ShouldBeNil)

		t.Logf("%+v", f3)
		t.Logf("%+v", f4)

		f5, f6, err := c.GetBiFollowing(context.Background(), 2201286, 2219118, true)
		So(err, ShouldBeNil)

		t.Logf("%+v", f5)
		t.Logf("%+v", f6)

		So(f1 == nil || f1.GetDropped(), ShouldEqual, f3 == nil || f3.GetDropped())
		So(f2 == nil || f2.GetDropped(), ShouldEqual, f4 == nil || f4.GetDropped())
		So(f3, ShouldResemble, f6)
		So(f4, ShouldResemble, f5)
	})

}
func TestClient_GetFollowingList(t *testing.T) {

	Convey("GetUserFollowerList", t, func() {

		lm := NewLoadMoreByID(SortDescending, "")
		fs, _, err := c.GetUserFollowerList(context.Background(), 1979376, false, lm, 100)
		So(err, ShouldBeNil)

		t.Log("len ", len(fs))

		for _, f := range fs {
			t.Logf("GetUserFollowerList %+v", f)
			if f.ToUid == f.FromUid {
				t.Logf("======================")
			}
		}

	})

	/*	Convey("GetUserFollowingList", t, func() {

		lm := NewLoadMoreByID(SortDescending, "")
		fs, _, err := c.GetUserFollowingList(context.Background(), 1982869, false, lm, 100)
		So(err, ShouldBeNil)

		t.Log("len ",len(fs))

		for _, f := range fs {
			t.Logf("GetUserFollowingList %+v", f)
			if f.ToUid == f.FromUid{
				t.Logf("======================")
			}
		}

	})*/

}

func TestClient_FollowUser(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()

	Convey("TestClient_GetBiFollowing", t, func() {
		_, _, err := c.FollowUser(ctx, &pb.FollowUserReq{
			Uid:                1,
			FollowingUid:       2,
			SequenceId:         1,
			Source:             pb.Source_USER_OPERATE,
			ClientSource:       2,
			ClientCustomSource: "test second source",
		})

		So(err, ShouldBeNil)
	})
}

func TestGetList(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	followings, next, err := c.GetUserFollowingList(ctx, 2214969, true, NewLoadMoreByID(SortDescending, ""), 0)
	if err != nil {
		t.Error(err)
		return
	}
	fmt.Println(next)
	fmt.Println(utils.ToJson(followings))
}

// BatchGetUserCounts

func Test_BatchGetUserCounts(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	a, err := c.BatchGetUserCounts(ctx, []uint32{2214969})
	fmt.Println(a)
	fmt.Println(a[2214969].Show)
	fmt.Println(err)
}

func Test_GetFriendCount(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	Convey("GetFriendCount", t, func() {
		count, err := c.GetFriendCount(ctx, 2207805, 0)
		So(err, ShouldBeNil)
		fmt.Printf("friend count %v\n", count)

		count, err = c.GetFriendCount(ctx, 2207805, 1)
		So(err, ShouldBeNil)
		fmt.Printf("friend count %v\n", count)

		count, err = c.GetFriendCount(ctx, 2207805, 2)
		So(err, ShouldBeNil)
		fmt.Printf("friend count %v\n", count)

		count, err = c.GetFriendCount(ctx, 2207805, 3)
		So(err, ShouldNotBeNil)
		fmt.Printf("friend err %v\n", err)
	})
}

func Test_GetFriendByPage(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	Convey("GetFriendByPage", t, func() {
		friends, err := c.GetFriendByPage(ctx, 2220638, 1, 50)
		So(err, ShouldBeNil)
		fmt.Printf("friends %v\n", friends)

		friends, err = c.GetFriendByPage(ctx, 2220638, 1, 101)
		fmt.Printf("friends %v err %v\n", friends, err)
		So(err, ShouldBeNil)

		friends, err = c.GetFriendByPage(ctx, 2220638, 0, 100)
		So(err, ShouldBeNil)
		fmt.Printf("friends %v\n", friends)

	})
}
