package friendship_synchronizer

import (
	"context"
	"log"
	"testing"
	"time"

	"google.golang.org/grpc"

	. "github.com/smartystreets/goconvey/convey"
)

var (
	client *Client
)

func init() {
	if c, err := NewClient(grpc.WithBlock(), grpc.WithTimeout(time.Second*3)); err != nil {
		log.Fatalf("Failed to create client: %v", err)
	} else {
		client = c
	}
}

func Test_Client_GetSynchronizedSequence(t *testing.T) {
	Convey("get-synchronized-sequence", t, func() {
		seq, err := client.GetSynchronizedSequence(context.Background(), 500001)
		So(err, ShouldBeNil)

		t.Logf("seq=%d", seq)
	})
}
