package friendship_synchronizer

import (
	"context"
	"golang.52tt.com/pkg/protocol"
	"google.golang.org/grpc"
)

type IClient interface {
	GetSynchronizedSequence(ctx context.Context, userID uint32) (uint64, protocol.ServerError)
	SyncFromFriendList(ctx context.Context, userID uint32, friendList []*Friend, maxUgcSequence uint64, addReverseSequenceZeroFollowing bool) protocol.ServerError
	SaveUGCSyncCheckPoint(ctx context.Context, userID uint32, checkpoint uint64) protocol.ServerError
	LoadUGCSyncCheckPoint(ctx context.Context, userID uint32) (uint64, protocol.ServerError)
	Close()
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
