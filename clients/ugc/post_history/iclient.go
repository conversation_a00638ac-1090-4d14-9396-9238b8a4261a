package post_history

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/ugc/post_history"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AddPostHistory(ctx context.Context, uid uint32, record *PostRecord) (affected bool, err protocol.ServerError)
	DeletePostHistory(ctx context.Context, uid uint32, record *PostRecord) (affected bool, err protocol.ServerError)
	GetUserTopNPostHistories(ctx context.Context, uid, N uint32, onlyAttachmentPosts bool) (records []*PostRecord, err protocol.ServerError)
	GetUserPostedCount(ctx context.Context, uid uint32) (count uint32, err protocol.ServerError)
	GetUserPostedCounts(ctx context.Context, uidList []uint32) (map[uint32]uint32, map[uint32]uint32, map[uint32]uint32, protocol.ServerError)
	BatchGetUserTopNPostHistories(ctx context.Context, uids []uint32, N uint32) (records []*pb.UserPostRecord, err protocol.ServerError)
	BatchGetUserTopNPostHistoriesEx(ctx context.Context, uids []uint32, N, M uint32) (records []*pb.UserPostRecord, err protocol.ServerError)
	IncrPostPrivateCountBy(ctx context.Context, uid uint32, incrAmount int32) (err protocol.ServerError)
	IncrPostNormalCountBy(ctx context.Context, uid uint32, incrAmount int32) (err protocol.ServerError)
	UpdateUserPostCount(ctx context.Context, uid uint32, count, privateCount, normalCount uint32) (err protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
