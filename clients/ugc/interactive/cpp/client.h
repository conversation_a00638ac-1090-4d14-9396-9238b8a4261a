#pragma once 

#include <cstdint>
#include <memory>
#include <string>
#include <vector>

#include <grpcpp/grpcpp.h>
#include <grpc_ext/grpc_ext.h>

#include "interactive/cpp/interactive.pb.h"
#include "interactive/cpp/interactive.grpc.pb.h"

namespace ugc { namespace interactive {

class Client final : private grpc_ext::GrpcClient { 
public:
    Client();
    Client(const std::string& target);  // for test only
    virtual ~Client();

    int GetInteractiveUinfo(uint32_t user_id, Uinfo *uinfo);

private:
    grpc_ext::StubWrapper<Interactive::Stub> stub_;
};

} } // ugc::friendship