package geo_topic

import (
	"context"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/ugc/geo_topic"
	"google.golang.org/grpc"
)

const (
	serviceName = "ugc-geo-topic"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewGeoTopicClient(cc)
		}, dopts...),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.GeoTopicClient {
	return c.Stub().(pb.GeoTopicClient)
}

func (c *Client) CreateGeoTopic(ctx context.Context, in *pb.CreateGeoTopicReq, opts ...grpc.CallOption) (*pb.CreateGeoTopicResp, protocol.ServerError) {
	r, err := c.typedStub().CreateGeoTopic(ctx, in, opts...)
	return r, protocol.ToServerError(err)
}

func (c *Client) EnableGeoTopic(ctx context.Context, in *pb.EnableGeoTopicReq, opts ...grpc.CallOption) (*pb.EnableGeoTopicResp, protocol.ServerError) {
	r, err := c.typedStub().EnableGeoTopic(ctx, in, opts...)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetGeoTopicById(ctx context.Context, tid string, opts ...grpc.CallOption) (*pb.GeoTopicInfo, protocol.ServerError) {
	r, err := c.typedStub().GetGeoTopicById(ctx, &pb.GetGeoTopicByIdReq{
		GeoTopicId: tid,
	}, opts...)
	return r.GetInfo(), protocol.ToServerError(err)
}

func (c *Client) GetGeoTopicByCode(ctx context.Context, cityCode string, opts ...grpc.CallOption) (*pb.GeoTopicInfo, protocol.ServerError) {
	r, err := c.typedStub().GetGeoTopicByCode(ctx, &pb.GetGeoTopicByCityCodeReq{
		CityCode: cityCode,
	}, opts...)
	return r.GetInfo(), protocol.ToServerError(err)
}

func (c *Client) GetGeoTopicsByIds(ctx context.Context, ids []string, opts ...grpc.CallOption) (map[string]*pb.GeoTopicInfo, protocol.ServerError) {
	r, err := c.typedStub().GetGeoTopicsByIds(ctx, &pb.GetGeoTopicsByIdsReq{
		GeoTopicIds: ids,
	}, opts...)
	return r.GetInfos(), protocol.ToServerError(err)
}

func (c *Client) UpdateGeoTopicPostCount(ctx context.Context, in *pb.UpdateGeoTopicPostCountReq, opts ...grpc.CallOption) protocol.ServerError {
	_, err := c.typedStub().UpdateGeoTopicPostCount(ctx, in, opts...)
	return protocol.ToServerError(err)
}
