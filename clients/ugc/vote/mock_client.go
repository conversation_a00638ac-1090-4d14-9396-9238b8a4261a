// Code generated by MockGen. DO NOT EDIT.
// Source: iclient.go

// Package vote is a generated GoMock package.
package vote

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	vote "golang.52tt.com/protocol/services/ugc/vote"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// CreateVote mocks base method.
func (m *MockIClient) CreateVote(ctx context.Context, req *vote.CreateVoteReq, opts ...grpc.CallOption) (*vote.CreateVoteRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateVote", varargs...)
	ret0, _ := ret[0].(*vote.CreateVoteRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateVote indicates an expected call of CreateVote.
func (mr *MockIClientMockRecorder) CreateVote(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateVote", reflect.TypeOf((*MockIClient)(nil).CreateVote), varargs...)
}

// GetVote mocks base method.
func (m *MockIClient) GetVote(ctx context.Context, req *vote.GetVoteReq, opts ...grpc.CallOption) (*vote.GetVoteRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVote", varargs...)
	ret0, _ := ret[0].(*vote.GetVoteRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVote indicates an expected call of GetVote.
func (mr *MockIClientMockRecorder) GetVote(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVote", reflect.TypeOf((*MockIClient)(nil).GetVote), varargs...)
}

// GetVoteList mocks base method.
func (m *MockIClient) GetVoteList(ctx context.Context, req *vote.GetVoteListReq, opts ...grpc.CallOption) (*vote.GetVoteListRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVoteList", varargs...)
	ret0, _ := ret[0].(*vote.GetVoteListRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVoteList indicates an expected call of GetVoteList.
func (mr *MockIClientMockRecorder) GetVoteList(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVoteList", reflect.TypeOf((*MockIClient)(nil).GetVoteList), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// VoteOption mocks base method.
func (m *MockIClient) VoteOption(ctx context.Context, req *vote.VoteOptionReq, opts ...grpc.CallOption) (*vote.VoteOptionRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VoteOption", varargs...)
	ret0, _ := ret[0].(*vote.VoteOptionRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VoteOption indicates an expected call of VoteOption.
func (mr *MockIClientMockRecorder) VoteOption(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VoteOption", reflect.TypeOf((*MockIClient)(nil).VoteOption), varargs...)
}
