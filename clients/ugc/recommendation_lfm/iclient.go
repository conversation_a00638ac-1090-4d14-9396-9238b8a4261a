package recommendation_lfm

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/ugc/recommendation_lfm"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AddUserReadHistory(ctx context.Context, in *pb.AddUserReadHistoryReq, opts ...grpc.CallOption) (*pb.AddUserReadHistoryResp, protocol.ServerError)
	AddPostsInto(ctx context.Context, in *pb.AddPostsIntoReq, opts ...grpc.CallOption) (*pb.AddPostsIntoResp, protocol.ServerError)
	RemovePosts(ctx context.Context, postIds ...string) (*pb.RemovePostsResp, protocol.ServerError)
	GetPostsWithFilter(ctx context.Context, in *pb.GetPostsWithFilterReq, opts ...grpc.CallOption) (*pb.GetPostsWithFilterResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
