package non_public_content

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/ugc/non_public_content"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AddNonPublicPost(ctx context.Context, post *pb.AddNonPublicPostReq) (*pb.AddNonPublicPostResp, protocol.ServerError)
	NonPublicMarkAttachmentUploaded(ctx context.Context,
		in *pb.NonPublicMarkAttachmentUploadedReq) (*pb.NonPublicMarkAttachmentUploadedResp, protocol.ServerError)
	NonPublicBanPostById(ctx context.Context,
		in *pb.NonPublicBanPostByIdReq) (*pb.NonPublicBanPostByIdResp, protocol.ServerError)
	GetNonPublishStickyContent(ctx context.Context,
		in *pb.GetNonPublishStickyContentReq) (*pb.GetNonPublishStickyContentResp, protocol.ServerError)
	RemoveNonPublishStickyContent(ctx context.Context,
		in *pb.RemoveNonPublishStickyContentReq) (*pb.RemoveNonPublishStickyContentResp, protocol.ServerError)
	UpdateNonPublicPostMachineAuditById(ctx context.Context,
		in *pb.UpdateNonPublicPostMachineAuditByIdReq) (*pb.UpdateNonPublicPostMachineAuditByIdResp, protocol.ServerError)
	AddNonPublicStickyContent(ctx context.Context,
		in *pb.AddNonPublishStickyContentReq) (*pb.AddNonPublishStickyContentResp, protocol.ServerError)
	GetNonPublicPostById(ctx context.Context,
		in *pb.GetNonPublicPostByIdReq) (*pb.GetNonPublicPostByIdResp, protocol.ServerError)
	BatGetNonPublicPostListById(ctx context.Context,
		in *pb.BatGetNonPublicPostListByIdReq) (*pb.BatGetNonPublicPostListByIdResp, protocol.ServerError)
	GetAndUpdateInsertPosts(ctx context.Context,
		in *pb.GetAndUpdateInsertPostsReq) (*pb.GetAndUpdateInsertPostsResp, protocol.ServerError)
	UpsertNonPublicHotComment(ctx context.Context,
		in *pb.UpsertNonPublicHotCommentReq) protocol.ServerError
	DeleteNonPublicHotComment(ctx context.Context,
		in *pb.DeleteNonPublicHotCommentReq) protocol.ServerError
	UpsertNonPublicAttitude(ctx context.Context,
		in *pb.AddNonPublicAttitudeReq) protocol.ServerError
	AddNonPublicPostDirectly(ctx context.Context,
		in *pb.AddNonPublicPostDirectlyReq) protocol.ServerError
	GetSceneStreamPostRecord(ctx context.Context,
		scene string, streamId string) (*pb.GetSceneStreamPostRecordResp, protocol.ServerError)
	BatchGetNonPublicCommentByIds(ctx context.Context,
		req *pb.BatchGetNonPublicCommentByIdsReq) (*pb.BatchGetNonPublicCommentByIdsResp, protocol.ServerError)
	AddNonPublicComment(ctx context.Context,
		req *pb.AddNonPublicCommentReq) (*pb.AddNonPublicCommentResp, protocol.ServerError)
	GetNonPublicCommentList(ctx context.Context,
		req *pb.GetNonPublicCommentListReq) (*pb.GetNonPublicCommentListResp, protocol.ServerError)

	BanNonPublicCommentById(ctx context.Context, commentId string, isBan bool) protocol.ServerError
	GetNonPublicHotComment(ctx context.Context, sceneId, postId string, count int32) (*pb.GetNonPublicHotCommentResp, protocol.ServerError)
	DistributePostFilter(ctx context.Context, filterType uint32, postIds []string) (*pb.DistributePostFilterResp, protocol.ServerError)
	UpdateNonPublicPostUpdateTime(ctx context.Context, postId string, updateTime uint32) protocol.ServerError
	BatchGetHotCommentSubComments(ctx context.Context,
		req *pb.BatchGetHotCommentSubCommentsReq) (*pb.BatchGetHotCommentSubCommentsResp, protocol.ServerError)
	InsertNonPublicPostIndex(ctx context.Context,
		req *pb.InsertNonPublicPostIndexReq) (*pb.InsertNonPublicPostIndexResp, protocol.ServerError)
	HideNonPublicPostInStream(ctx context.Context,
		postId, operator string, hide uint32) (*pb.HideNonPublicPostInStreamResp, protocol.ServerError)
	BatchNonPublicHotComment(ctx context.Context,
		sceneId string, postIds []string, count int32) (*pb.BatchNonPublicHotCommentResp, protocol.ServerError)
	BatGetNonPostHideStatus(ctx context.Context, postIds []string) (*pb.BatGetNonPostHideStatusResp, protocol.ServerError)
	ListNonPublicPostForceInsertInStream(ctx context.Context,
		in *pb.ListNonPublicPostForceInsertInStreamReq) (*pb.ListNonPublicPostForceInsertInStreamResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
