package non_public_content

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	"golang.52tt.com/protocol/services/ugc/content"
	pb "golang.52tt.com/protocol/services/ugc/non_public_content"
	"google.golang.org/grpc"
)

const (
	serviceName = "ugc-content"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewUgcNonPublicContentClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.UgcNonPublicContentClient {
	return c.Stub().(pb.UgcNonPublicContentClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) AddNonPublicPost(ctx context.Context, post *pb.AddNonPublicPostReq) (*pb.AddNonPublicPostResp, protocol.ServerError) {
	var generalContents []*content.GeneralContent
	for _, v := range post.GetGeneralContents() {
		if originName, ok := content.ContentOrigin_name[int32(v.GetContentOrigin())]; ok {
			v.OriginName = originName
		}
		generalContents = append(generalContents, v)
	}
	if len(generalContents) != 0 {
		post.GeneralContents = generalContents
	}

	resp, err := c.typedStub().AddNonPublicPost(ctx, post)
	return resp, protocol.ToServerError(err)
}

func (c *Client) NonPublicMarkAttachmentUploaded(ctx context.Context,
	in *pb.NonPublicMarkAttachmentUploadedReq) (*pb.NonPublicMarkAttachmentUploadedResp, protocol.ServerError) {

	resp, err := c.typedStub().NonPublicMarkAttachmentUploaded(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) NonPublicBanPostById(ctx context.Context,
	in *pb.NonPublicBanPostByIdReq) (*pb.NonPublicBanPostByIdResp, protocol.ServerError) {

	resp, err := c.typedStub().NonPublicBanPostById(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetNonPublishStickyContent(ctx context.Context,
	in *pb.GetNonPublishStickyContentReq) (*pb.GetNonPublishStickyContentResp, protocol.ServerError) {
	resp, err := c.typedStub().GetNonPublishStickyContent(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) RemoveNonPublishStickyContent(ctx context.Context,
	in *pb.RemoveNonPublishStickyContentReq) (*pb.RemoveNonPublishStickyContentResp, protocol.ServerError) {
	resp, err := c.typedStub().RemoveNonPublishStickyContent(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateNonPublicPostMachineAuditById(ctx context.Context,
	in *pb.UpdateNonPublicPostMachineAuditByIdReq) (*pb.UpdateNonPublicPostMachineAuditByIdResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateNonPublicPostMachineAuditById(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddNonPublicStickyContent(ctx context.Context,
	in *pb.AddNonPublishStickyContentReq) (*pb.AddNonPublishStickyContentResp, protocol.ServerError) {
	resp, err := c.typedStub().AddNonPublicStickyContent(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetNonPublicPostById(ctx context.Context,
	in *pb.GetNonPublicPostByIdReq) (*pb.GetNonPublicPostByIdResp, protocol.ServerError) {
	resp, err := c.typedStub().GetNonPublicPostById(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatGetNonPublicPostListById(ctx context.Context,
	in *pb.BatGetNonPublicPostListByIdReq) (*pb.BatGetNonPublicPostListByIdResp, protocol.ServerError) {
	resp, err := c.typedStub().BatGetNonPublicPostListById(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAndUpdateInsertPosts(ctx context.Context,
	in *pb.GetAndUpdateInsertPostsReq) (*pb.GetAndUpdateInsertPostsResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAndUpdateInsertPosts(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpsertNonPublicHotComment(ctx context.Context,
	in *pb.UpsertNonPublicHotCommentReq) protocol.ServerError {
	_, err := c.typedStub().UpsertNonPublicHotComment(ctx, in)
	return protocol.ToServerError(err)
}

func (c *Client) DeleteNonPublicHotComment(ctx context.Context,
	in *pb.DeleteNonPublicHotCommentReq) protocol.ServerError {
	_, err := c.typedStub().DeleteNonPublicHotComment(ctx, in)
	return protocol.ToServerError(err)
}

func (c *Client) UpsertNonPublicAttitude(ctx context.Context,
	in *pb.AddNonPublicAttitudeReq) protocol.ServerError {
	_, err := c.typedStub().AddNonPublicAttitude(ctx, in)
	return protocol.ToServerError(err)
}

func (c *Client) GetSceneStreamPostRecord(ctx context.Context,
	scene string, streamId string) (*pb.GetSceneStreamPostRecordResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSceneStreamPostRecord(ctx, &pb.GetSceneStreamPostRecordReq{Scene: scene, StreamId: streamId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetNonPublicCommentByIds(ctx context.Context,
	req *pb.BatchGetNonPublicCommentByIdsReq) (*pb.BatchGetNonPublicCommentByIdsResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetNonPublicCommentByIds(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddNonPublicPostDirectly(ctx context.Context,
	in *pb.AddNonPublicPostDirectlyReq) protocol.ServerError {
	var generalContents []*content.GeneralContent
	for _, v := range in.GetGeneralContents() {
		if originName, ok := content.ContentOrigin_name[int32(v.GetContentOrigin())]; ok {
			v.OriginName = originName
		}
		generalContents = append(generalContents, v)
	}
	if len(generalContents) != 0 {
		in.GeneralContents = generalContents
	}
	_, err := c.typedStub().AddNonPublicPostDirectly(ctx, in)
	return protocol.ToServerError(err)
}

func (c *Client) AddNonPublicComment(ctx context.Context,
	req *pb.AddNonPublicCommentReq) (*pb.AddNonPublicCommentResp, protocol.ServerError) {
	var generalContents []*content.GeneralContent
	for _, v := range req.GetGeneralContents() {
		if originName, ok := content.ContentOrigin_name[int32(v.GetContentOrigin())]; ok {
			v.OriginName = originName
		}
		generalContents = append(generalContents, v)
	}
	if len(generalContents) != 0 {
		req.GeneralContents = generalContents
	}
	resp, err := c.typedStub().AddNonPublicComment(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetNonPublicCommentList(ctx context.Context,
	req *pb.GetNonPublicCommentListReq) (*pb.GetNonPublicCommentListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetNonPublicCommentList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BanNonPublicCommentById(ctx context.Context, commentId string, isBan bool) protocol.ServerError {
	_, err := c.typedStub().BanNonPublicCommentById(ctx, &pb.BanNonPublicCommentByIdReq{CommentId: commentId, IsBan: isBan})
	return protocol.ToServerError(err)
}

func (c *Client) GetNonPublicHotComment(ctx context.Context, sceneId, postId string, count int32) (*pb.GetNonPublicHotCommentResp, protocol.ServerError) {
	resp, err := c.typedStub().GetNonPublicHotComment(ctx, &pb.GetNonPublicHotCommentReq{SceneId: sceneId, PostId: postId, Count: count})
	return resp, protocol.ToServerError(err)
}

func (c *Client) DistributePostFilter(ctx context.Context, filterType uint32, postIds []string) (*pb.DistributePostFilterResp, protocol.ServerError) {
	resp, err := c.typedStub().DistributePostFilter(ctx, &pb.DistributePostFilterReq{FilterType: filterType, PostIdList: postIds})
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateNonPublicPostUpdateTime(ctx context.Context, postId string, updateTime uint32) protocol.ServerError {
	_, err := c.typedStub().UpdateNonPublicPostUpdateTime(ctx, &pb.UpdateNonPublicPostUpdateTimeReq{PostId: postId, UpdateTime: updateTime})
	return protocol.ToServerError(err)
}

func (c *Client) BatchGetHotCommentSubComments(ctx context.Context,
	req *pb.BatchGetHotCommentSubCommentsReq) (*pb.BatchGetHotCommentSubCommentsResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetHotCommentSubComments(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) InsertNonPublicPostIndex(ctx context.Context,
	req *pb.InsertNonPublicPostIndexReq) (*pb.InsertNonPublicPostIndexResp, protocol.ServerError) {
	resp, err := c.typedStub().InsertNonPublicPostIndex(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HideNonPublicPostInStream(ctx context.Context,
	postId, operator string, hide uint32) (*pb.HideNonPublicPostInStreamResp, protocol.ServerError) {
	resp, err := c.typedStub().HideNonPublicPostInStream(ctx, &pb.HideNonPublicPostInStreamReq{
		PostId:     postId,
		HideStatus: hide,
		Operator:   operator,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchNonPublicHotComment(ctx context.Context,
	sceneId string, postIds []string, count int32) (*pb.BatchNonPublicHotCommentResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchNonPublicHotComment(ctx, &pb.BatchNonPublicHotCommentReq{
		SceneId: sceneId, PostId: postIds, Count: count,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatGetNonPostHideStatus(ctx context.Context, postIds []string) (*pb.BatGetNonPostHideStatusResp, protocol.ServerError) {
	resp, err := c.typedStub().BatGetNonPostHideStatus(ctx, &pb.BatGetNonPostHideStatusReq{
		PostIdList: postIds,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListNonPublicPostForceInsertInStream(ctx context.Context,
	in *pb.ListNonPublicPostForceInsertInStreamReq) (*pb.ListNonPublicPostForceInsertInStreamResp, protocol.ServerError) {
	resp, err := c.typedStub().ListNonPublicPostForceInsertInStream(ctx, in)
	return resp, protocol.ToServerError(err)
}
