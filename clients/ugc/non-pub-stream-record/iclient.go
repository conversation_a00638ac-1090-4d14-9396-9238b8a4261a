package non_pub_stream_record

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/ugc/non-pub-stream-record"
	"google.golang.org/grpc"
)

type (
	IClient interface {
		client.BaseClient
		BatchAddStreamRecord(ctx context.Context, req *pb.BatchAddStreamRecordReq) (*pb.BatchAddStreamRecordResp, protocol.ServerError)
		BatchGetStreamRecord(ctx context.Context, req *pb.BatchGetStreamRecordReq) (*pb.BatchGetStreamRecordResp, protocol.ServerError)
		MarkUserStreamRecord(ctx context.Context, req *pb.MarkUserStreamRecordReq) (*pb.MarkUserStreamRecordResp, protocol.ServerError)
		BatchUserStreamUnReadNum(ctx context.Context, req *pb.BatchUserStreamUnReadNumReq) (*pb.BatchUserStreamUnReadNumResp, protocol.ServerError)
	}
)

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
