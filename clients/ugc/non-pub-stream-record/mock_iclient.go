// Code generated by MockGen. DO NOT EDIT.
// Source: iclient.go

// Package mock_non_pub_stream_record is a generated GoMock package.
package non_pub_stream_record

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	non_pub_stream_record "golang.52tt.com/protocol/services/ugc/non-pub-stream-record"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchAddStreamRecord mocks base method.
func (m *MockIClient) BatchAddStreamRecord(ctx context.Context, req *non_pub_stream_record.BatchAddStreamRecordReq) (*non_pub_stream_record.BatchAddStreamRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddStreamRecord", ctx, req)
	ret0, _ := ret[0].(*non_pub_stream_record.BatchAddStreamRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchAddStreamRecord indicates an expected call of BatchAddStreamRecord.
func (mr *MockIClientMockRecorder) BatchAddStreamRecord(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddStreamRecord", reflect.TypeOf((*MockIClient)(nil).BatchAddStreamRecord), ctx, req)
}

// BatchGetStreamRecord mocks base method.
func (m *MockIClient) BatchGetStreamRecord(ctx context.Context, req *non_pub_stream_record.BatchGetStreamRecordReq) (*non_pub_stream_record.BatchGetStreamRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetStreamRecord", ctx, req)
	ret0, _ := ret[0].(*non_pub_stream_record.BatchGetStreamRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetStreamRecord indicates an expected call of BatchGetStreamRecord.
func (mr *MockIClientMockRecorder) BatchGetStreamRecord(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetStreamRecord", reflect.TypeOf((*MockIClient)(nil).BatchGetStreamRecord), ctx, req)
}

// BatchUserStreamUnReadNum mocks base method.
func (m *MockIClient) BatchUserStreamUnReadNum(ctx context.Context, req *non_pub_stream_record.BatchUserStreamUnReadNumReq) (*non_pub_stream_record.BatchUserStreamUnReadNumResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUserStreamUnReadNum", ctx, req)
	ret0, _ := ret[0].(*non_pub_stream_record.BatchUserStreamUnReadNumResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchUserStreamUnReadNum indicates an expected call of BatchUserStreamUnReadNum.
func (mr *MockIClientMockRecorder) BatchUserStreamUnReadNum(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUserStreamUnReadNum", reflect.TypeOf((*MockIClient)(nil).BatchUserStreamUnReadNum), ctx, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// MarkUserStreamRecord mocks base method.
func (m *MockIClient) MarkUserStreamRecord(ctx context.Context, req *non_pub_stream_record.MarkUserStreamRecordReq) (*non_pub_stream_record.MarkUserStreamRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkUserStreamRecord", ctx, req)
	ret0, _ := ret[0].(*non_pub_stream_record.MarkUserStreamRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// MarkUserStreamRecord indicates an expected call of MarkUserStreamRecord.
func (mr *MockIClientMockRecorder) MarkUserStreamRecord(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkUserStreamRecord", reflect.TypeOf((*MockIClient)(nil).MarkUserStreamRecord), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
