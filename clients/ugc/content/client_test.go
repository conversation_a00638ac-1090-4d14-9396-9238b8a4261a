/**
 * Author: Orange
 * Date: 18-11-7
 */

package content

import (
	"context"
	"fmt"
	"io"
	"log"
	"math/rand"
	"os"
	"runtime"
	"sync"
	"testing"
	"time"

	"github.com/opentracing/opentracing-go"
	"github.com/uber/jaeger-client-go/config"
	"golang.52tt.com/pkg/foundation/random"
	"google.golang.org/grpc"

	pb "golang.52tt.com/protocol/services/ugc/content"
	"google.golang.org/grpc/grpclog"

	. "github.com/smartystreets/goconvey/convey"
)

var (
	tracer opentracing.Tracer
	closer io.Closer
	cli    *Client
)

// for test
// func (c *Client) AddPostDirectly(ctx context.Context, in *pb.AddPostDirectlyReq,
// 	opts ...grpc.CallOption) (*pb.AddPostDirectlyResp, protocol.ServerError) {
// 	if resp, err := c.typedStub().AddPostDirectly(ctx, in, opts...); err != nil {
// 		return nil, protocol.ToServerError(err)
// 	} else {
// 		return resp, nil
// 	}
// }

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
	c, err := NewClient()
	if err != nil {
		log.Fatal(err)
	}

	cli = c
}

func Test_Client_Init(t *testing.T) {
	tracer, closer, _ = (&config.Configuration{
		Sampler: &config.SamplerConfig{
			Type:  "const",
			Param: 1,
		},
		Reporter: &config.ReporterConfig{
			LogSpans:            true,
			BufferFlushInterval: 1 * time.Second,
		},
	}).New("ugc_content_client_test")

	cli, _ = NewTracedClient(tracer)
}

func TestClient_BatchGetStickyPost(t *testing.T) {
	cli, err := NewClient(grpc.WithBlock())
	if err != nil {
		t.Fatal(err)
	}

	userIds := []uint32{
		2181050, 2205704, 1982812, 1,
	}
	r, err := cli.BatchGetStickyPost(context.Background(), &pb.BatchGetStickyPostReq{
		UserIdList: userIds,
	})
	if err != nil {
		t.Fatal(err)
	}

	t.Log(r)
}

func TestClient_BatchGetPostListById(t *testing.T) {
	cli, err := NewClient(grpc.WithTimeout(3 * time.Second))
	if err != nil {
		t.Fatal(err)
	}

	req := &pb.BatchGetPostListByIdReq{
		PostIdList: []string{"5bf28fd9ef57057cd20fac28", "5bf28b47ef57057cd20fac25", "5bf28917ef57057cd20fac24", "5bf28879ef57057cd20fac22", "5bf28e40ef57057cd20fac26"},
	}
	posts, err := cli.BatchGetPostListById(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}

	t.Log(posts)
}

func TestClient_BanPostById(t *testing.T) {
	resp, err := cli.BanPostById(context.Background(), &pb.BanPostByIdReq{
		PostId: "5c172d7bef5705176cb15772",
		IsBan:  true,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)

}

func TestClient_AddPost(t *testing.T) {
	t.Log(os.Getenv("GODEBUG"))
	cli, err := NewClient(grpc.WithTimeout(3 * time.Second))
	if err != nil {
		t.Fatal(err)
	}

	req := &pb.AddPostReq{
		UserId: 500001,
		/*		TopicId:              "topic-1234567666666",*/
		Type:                 pb.PostInfo_CMS,
		AttachmentImageCount: 0,
		AttachmentVideoCount: 0,
		Content:              "哈哈哈312313231哈哈",
		Status:               pb.ContentStatus_CONTENT_STATUS_NORMAL,
	}
	resp, err := cli.AddPost(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}

	t.Log(resp)
}

func TestClient_AddCmsPost(t *testing.T) {
	cli, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	req := &pb.AddPostDirectlyReq{
		UserId:           2203052,
		TopicId:          "",
		Type:             pb.PostInfo_CMS,
		Content:          "cms test",
		CreateAt:         uint64(time.Now().Unix()),
		AvailabilityMask: uint32(pb.AddPostDirectlyReq_ALL),
		Attachments: []*pb.AttachmentInfo{
			{
				Key:     "images-test/5c04d35cef57054e0f60f467_0_1543820124_2",
				Type:    pb.AttachmentInfo_IMAGE,
				Content: "https://tt-ugc-cdnqn.52tt.com/images-test/5c04d35cef57054e0f60f467_0_1543820124_2",
				Extra:   "{}",
				Status:  pb.ContentStatus_CONTENT_STATUS_NORMAL,
			},
			{
				Key:     "https://activity.52tt.com/yy-act/2018/wzgl-zk-180810/",
				Type:    pb.AttachmentInfo_CMS,
				Content: "https://activity.52tt.com/yy-act/2018/wzgl-zk-180810/",
				Extra:   "{}",
				Status:  pb.ContentStatus_CONTENT_STATUS_NORMAL,
			},
		},
	}
	resp, err := cli.AddPostDirectly(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}

	t.Log(resp)
}

func TestClient_GetPostById(t *testing.T) {
	cli, err := NewClient(grpc.WithTimeout(3 * time.Second))
	if err != nil {
		t.Fatal(err)
	}

	post, err_ := cli.GetPostById(context.Background(), "5c174510ef5705176cb157a2")
	if err_ != nil {
		t.Log(err_.Code())
		t.Fatal(err)
	}
	t.Log(post)
}

func TestClient_BatchBanPostsByIds(t *testing.T) {
	cli, err := NewClient()
	if err != nil {
		t.Fatal(err)
	}

	postIds := []string{
		"5c13ad4eeb341b5b072f3d0a",
	}
	for _, postId := range postIds {
		resp, err := cli.BanPostById(context.Background(), &pb.BanPostByIdReq{
			PostId: postId,
			IsBan:  false,
		})
		if err != nil {
			t.Fatal(err)
		}

		t.Log(resp)
	}

}

func TestClient_AddComment(t *testing.T) {
	for i := 0; i < 1480; i++ {
		resp, err := cli.AddComment(context.Background(), &pb.AddCommentReq{
			UserId:           7598229,
			PostId:           "5c174510ef5705176cb157a2",
			ReplyToCommentId: "",
			ConversationId:   "5c174510ef5705176cb157a2",
			Content:          fmt.Sprintf("评论 %d", i),
			Status:           pb.ContentStatus_CONTENT_STATUS_NORMAL,
			Attachments:      nil,
		})
		if err != nil {
			t.Fatal(err)
		}
		t.Log(resp)
	}
}

// 二级评论
func TestClient_AddCommentToComment(t *testing.T) {
	for i := 0; i < 100; i++ {
		resp, err := cli.AddComment(context.Background(), &pb.AddCommentReq{
			UserId:           1966030,
			PostId:           "5c2d857af7c241538e7e8dd1",
			ReplyToCommentId: "5c2d859ff7c241538e7e8dd8",
			ConversationId:   "5c2d859ff7c241538e7e8dd8",
			Content:          fmt.Sprintf("评论的评论%d", i+100),
			Status:           pb.ContentStatus_CONTENT_STATUS_NORMAL,
			Attachments:      nil,
		})
		if err != nil {
			t.Fatal(err)
		}
		t.Log(resp)
	}
}

// 回复二级评论
func TestClient_AddLevel2Comment(t *testing.T) {
	resp, err := cli.AddComment(context.Background(), &pb.AddCommentReq{
		UserId:           1966030,
		PostId:           "P201811141623520182413762840",
		ReplyToCommentId: "5bebe54cf7c241553504d315",
		ConversationId:   "5bebde1ff7c2413686073972",
		Content:          "评论的评论" + random.RandString(5),
		Status:           pb.ContentStatus_CONTENT_STATUS_NORMAL,
		Attachments:      nil,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestClient_DelComment(t *testing.T) {
	resp, err := cli.DelComment(context.Background(), &pb.DelCommentReq{
		PostId:    "",
		CommentId: "5bebe3edf7c2414dbb7bf131",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestClient_GetCommentList(t *testing.T) {
	postId := "5c301cceeb341b224fdcc7b9"
	conversationId := postId
	var count uint32 = 20
	loadMore := ""
	for true {
		resp, err := cli.GetCommentList(context.Background(), &pb.GetCommentListReq{
			PostId:         postId,
			ConversationId: conversationId,
			LoadMore:       loadMore,
			Count:          count,
		})
		if err != nil {
			t.Fatal(err)
		}
		t.Log(resp)

		loadMore = resp.GetLoadMore()
		if loadMore == "" {
			break
		}
	}
}

func TestClient_BatchGetCommentByIds(t *testing.T) {
	mapComment, err := cli.BatchGetCommentByIds(context.Background(), []string{
		"5c0e1be0ef570544279be8ae",
		"5c0ccf46f7c2412e005c9a00",
		"5c0ccbdef7c2412e005c99fc",
		"5c0e1be6ef570544279be8af",
		"5c0e1bdbef570544279be8ad",
		"5c0e1bc0ef570544279be8ac",
		"5c0e1358ef570544279be7fb",
		"5c0cc4b2f7c2412e005c99fb",
		"5c0cbe16ef570544279be786",
		"5c0cbe02ef570544279be785",
		"5c0cbdfcef570544279be784",
		"5c0ccbcfef570544279be78f",
		"5c0cced9f7c2412e005c99ff",
		"5c0cc492f7c2412e005c99fa",
		"5c0b762af7c2412e005c99da",
		"5c0baf75ef570544279be76c",
		"5c0cca19ef570544279be78e",
		"5c0cc438f7c2412e005c99f9",
	}, 0,0 )
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("%+v", mapComment)
}

func TestClient_GetCommentInCommentList(t *testing.T) {
	postId := "P201811141623520182413762840"
	conversationId := "5bebde1ff7c2413686073972"
	var count uint32 = 1
	loadMore := ""
	for true {
		resp, err := cli.GetCommentList(context.Background(), &pb.GetCommentListReq{
			PostId:         postId,
			ConversationId: conversationId,
			LoadMore:       loadMore,
			Count:          count,
		})
		if err != nil {
			t.Fatal(err)
		}
		t.Log(resp)

		loadMore = resp.GetLoadMore()
		if loadMore == "" {
			break
		}
	}
}

func TestClient_AddAttitude(t *testing.T) {
	postId := "P201811141623520182413762840"
	resp, err := cli.AddAttitude(context.Background(), &pb.AddAttitudeReq{
		PostId:       postId,
		UserId:       1966030,
		AttitudeType: 1,
	})

	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestClient_DelAttitude(t *testing.T) {
	postId := "P201811141623520182413762840"
	resp, err := cli.DelAttitude(context.Background(), &pb.DelAttitudeReq{
		PostId: postId,
		UserId: 1966037,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestClient_GetAttitudeUserList(t *testing.T) {
	resp, err := cli.GetAttitudeUserList(context.Background(), &pb.GetAttitudeUserListReq{
		PostId: "P201811141623520182413762840",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestClient_TestAddPostWithAttachmentAndReview(t *testing.T) {
	runtime.GOMAXPROCS(6)
	now := time.Now().Unix()
	r, err := cli.AddPost(context.Background(), &pb.AddPostReq{
		UserId:               1966030,
		TopicId:              "1",
		Type:                 pb.PostInfo_IMAGE,
		Content:              fmt.Sprintf("test%d", now),
		AttachmentImageCount: 9,
		Status:               pb.ContentStatus_CONTENT_STATUS_UNDER_REVIEW,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(r)

	attachmentInfo := make([]*pb.AttachmentInfo, len(r.AttachmentImageKeys))
	for i, a := range r.AttachmentImageKeys {
		attachmentInfo[i] = &pb.AttachmentInfo{
			Key:     a,
			Type:    pb.AttachmentInfo_IMAGE,
			Content: "http://",
			Extra:   "ext",
		}
	}

	wg := sync.WaitGroup{}
	wg.Add(1 + len(r.GetAttachmentImageKeys()))

	go func() {
		defer wg.Done()
		time.Sleep(time.Millisecond * time.Duration(20))
		resp, err := cli.MarkAttachmentUploaded(context.Background(), &pb.MarkAttachmentUploadedReq{
			PostId:             r.GetPostId(),
			AttachmentInfoList: attachmentInfo,
		})
		if err != nil {
			t.Fatal(err)
		}
		t.Log(resp)
	}()

	for _, k := range r.GetAttachmentImageKeys() {
		key := k
		go func() {
			defer wg.Done()
			time.Sleep(time.Millisecond * time.Duration(rand.Intn(30)))
			_, err := cli.UpdateAttachmentStatus(context.Background(), &pb.UpdateAttachmentStatusReq{
				PostId:        r.GetPostId(),
				AttachmentKey: key,
				Status:        pb.ContentStatus_CONTENT_STATUS_NORMAL,
			})
			if err != nil {
				t.Fatal(err)
			}
		}()
	}

	wg.Wait()
	_, err = cli.GetPostById(context.Background(), r.GetPostId())
	if err != nil {
		t.Fatal(err)
	}
	//pass := true
	//for _, a := range post.GetAttachments() {
	//	pass = a.Status == pb.ContentStatus_CONTENT_STATUS_NORMAL
	//}
	//if pass && post.Status == pb.ContentStatus_CONTENT_STATUS_NORMAL {
	//	//pass
	//	t.Logf("pass: %+v", post)
	//} else {
	//	t.Errorf("failed: %+v", post)
	//}
}

func TestClient_UpdateAttachmentStatus(t *testing.T) {
	resp, err := cli.UpdateAttachmentStatus(context.Background(), &pb.UpdateAttachmentStatusReq{
		PostId:        "5bf2b28df7c2410aa7da792c",
		AttachmentKey: "images-test/5bf2b28df7c2410aa7da792c_0_1542632077_8",
		Status:        pb.ContentStatus_CONTENT_STATUS_NORMAL,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestClient_UpdatePostSpecialLabel(t *testing.T) {
	postIds := []string{
		"5c301d65ef57053b39e299f9",
	}
	for _, p := range postIds {
		resp, err := cli.UpdatePostSpecialLabel(context.Background(), &pb.UpdatePostSpecialLabelReq{
			PostId: p,
			Label:  "这是带泪的标签",
		})
		if err != nil {
			t.Fatal(err)
		}
		t.Log(resp)
	}
}

func TestClient_UpdatePostTags(t *testing.T) {
	_, err := cli.UpdatePostTags(context.Background(), &pb.UpdatePostTagsReq{
		PostId: "5c04c493ef5705738199a8f6",
		//Tags: []uint32{1},
	})
	if err != nil {
		t.Fatal(err)
	}
}

func TestClient_GetPostsByFilter(t *testing.T) {
	userIDs := []uint32{0, 500001, 500002}
	topicIDs := []string{"", "all", "exists", "non-exists", "5c0e17edef5705579dfd5598"}
	subTopicIDs := []string{"", "all", "exists", "non-exists", "5c43087413a4f41c4801f59c"}

	for _, userID := range userIDs {
		for _, topicID := range topicIDs {
			for _, subTopicID := range subTopicIDs {
				Convey(fmt.Sprintf("get-posts-by-filter(%d [%s] [%s])", userID, topicID, subTopicID), t, func() {
					// *
					// 按圈子过滤, 空值表示不按此条件过滤；
					// Magic strings:
					// "all": 所有圈子
					// "exsits": 包含圈子
					// "non-exists": 不包含圈子
					now := time.Now()
					start := now.AddDate(0, -1, 0)
					resp, err := cli.GetPostsByFilter(context.Background(), &pb.GetPostsByFilterReq{
						ContentType: pb.ContentType_ORIGIN_TEXT,
						Limit:       10,
						CreateBegin: start.Unix(),
						CreateEnd:   now.Unix(),
						UserId:      userID,
						TopicId:     topicID,
						SubTopicId:  subTopicID,
					})

					So(err, ShouldBeNil)
					t.Logf("%d posts got", len(resp.PostList))
					for _, p := range resp.PostList {
						if userID > 0 {
							So(p.UserId, ShouldEqual, userID)
						}
						So(p.CreateAt, ShouldBeGreaterThanOrEqualTo, start.Unix())
						So(p.CreateAt, ShouldBeLessThan, now.Unix())

						switch topicID {
						case "", "non-exists":
							So(p.TopicId, ShouldBeEmpty)
						case "all":
						case "exists":
							So(p.TopicId, ShouldNotBeBlank)
						default:
							So(p.TopicId, ShouldEqual, topicID)
						}

						switch subTopicID {
						case "", "non-exists":
							So(p.SubTopicId, ShouldBeEmpty)
						case "all":
						case "exists":
							So(p.SubTopicId, ShouldNotBeBlank)
						default:
							So(p.SubTopicId, ShouldEqual, subTopicID)
						}
					}
				})
			}
		}
	}

}

func TestClient_GetPostByUidList(t *testing.T) {
	userIDs := []uint32{500001}
	Convey("GetPostByUidList", t, func() {
		now := time.Now()
		start := now.AddDate(-2, -2, 0)
		resp, err := cli.GetPostsByFilter(context.Background(), &pb.GetPostsByFilterReq{
			ContentType: pb.ContentType_ORIGIN_TEXT,
			TopicId:     "5bf2b99bef57057ef6ae2429",
			PostTypes: []pb.PostInfo_PostType{
				pb.PostInfo_IMAGE,
				pb.PostInfo_VIDEO,
			},
			Limit:        10,
			CreateBegin:  start.Unix(),
			CreateEnd:    now.Unix(),
			OwnerUidList: userIDs,
		})
		So(err, ShouldBeNil)
		t.Log(resp.PostList)
	})
}

func TestClient_GetPostTags(t *testing.T) {
	resp, err := cli.GetPostTags(context.Background(), &pb.GetPostTagsReq{
		PostId: "5c04c493ef5705738199a8f6",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestClient_GetCommentsByFilter(t *testing.T) {
	resp, err := cli.GetCommentsByFilter(context.Background(), &pb.GetCommentsByFilterReq{
		PostId: "5dc22bf9aa1e9f000125ef19",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)

}
