#define CATCH_CONFIG_MAIN

#include <test/catch.hpp>
#include <cstdint>
#include <vector>
#include <memory>
#include <iostream>

#include "iLog.h"

#include "content/cpp/client.h"

using std::cout;
using std::endl;

using namespace ugc::content;

static std::shared_ptr<Client> client;


TEST_CASE("setup") {
    Comm::OpenLog ( "ugc::content-client-test", 16, "/home/<USER>/log");
}

TEST_CASE("ugc-report-post") {
    uint32_t uid = 2191335;
    int ret = Client::instance()->AppReport(uid, "5c064fa2ef57050d7f161ab8", "", 2, "use case");
    printf("ugc report ret %d", ret);
}