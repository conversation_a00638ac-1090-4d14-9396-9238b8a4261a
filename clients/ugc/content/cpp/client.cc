#include "content/cpp/client.h"

#include "iLog.h"
#include "grpc_runtime.h"

namespace ugc { namespace content {

using namespace std::placeholders;
using Comm::LogErr;

static const char *k_service_name = "ugc-content";
static const char *k_target = "dns:///ugc-content.52tt.local";

Client::Client()
: grpc_ext::GrpcClient(k_target, k_service_name)
, stub_(k_service_name) {

}

Client::Client(const std::string& target)
: grpc_ext::GrpcClient(target, k_service_name)
, stub_(k_service_name) {

}

Client::~Client() {

}

Client * Client::instance() {
    static std::unique_ptr<Client> instance;
    static once_flag once;
    call_once(once, [&]() {
        instance.reset(new Client);
    });
    return instance.get();
}

// `MemFnIfStub` must be a member function of `Stub`
template<class Request, class Reply, class Stub, class MemFnOfStub>
grpc::Status invoke(const char* method, grpc::ClientContext *ctx, Stub *stub, MemFnOfStub mem_fn, const Request &request, Reply *reply) {
    auto fn = std::bind(mem_fn, stub, _1, _2, _3);
    auto status = fn(ctx, request, reply);
    if (!status.ok()) {
        LogErr("%s grpc err %d %s", method, status.error_code(), status.error_message().c_str());
        return status;
    }
    status = grpc::GetUnaryRPCStatus(*ctx);
    if (!status.ok()) {
        LogErr("%s server err %d %s", method, status.error_code(), status.error_message().c_str());
    }

    return status;
}

int Client::AppReport(uint32_t user_id, string post_id, string comment_id, uint32_t report_type, string content) {
    static const char *k_method = __FUNCTION__;

    auto stub = stub_.WithChannel(channel());

    AppReportReq request;
    AppReportResp response;

    request.set_post_id(post_id);
    request.set_comment_id(comment_id);
    request.set_from_user_id(user_id);
    request.set_report_type(report_type);
    request.set_content(content);

    grpc::ClientContext context;
    auto status = BlockingUnaryCall(
        &context,
        [&] (grpc::ClientContext *ctx) {
            return invoke(
                k_method, ctx, stub.get(),
                &UgcContent::StubInterface::AppReport,
                request, &response
            );
        },
        WithMethod(k_method)
    );

    if (status.ok()) {
        
    }

    return status.error_code();
}

} }