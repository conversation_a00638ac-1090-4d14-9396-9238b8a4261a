package following_stream

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	"golang.52tt.com/protocol/services/ugc/following_stream"
	"google.golang.org/grpc"
)

//const serviceName = "192.168.9.229:48841"
const serviceName = "ugc-following-stream"

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return following_stream.NewFollowingStreamClient(cc)
		}, dopts...),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() following_stream.FollowingStreamClient {
	return c.Stub().(following_stream.FollowingStreamClient)
}

// -------- rpc --------
func (c *Client) AddStreamItem(ctx context.Context, in *following_stream.AddStreamItemReq,
	opts ...grpc.CallOption) (*following_stream.AddStreamItemResp, protocol.ServerError) {
	resp, err := c.typedStub().AddStreamItem(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DeleteStreamItem(ctx context.Context, in *following_stream.DeleteStreamItemReq,
	opts ...grpc.CallOption) (*following_stream.DeleteStreamItemResp, protocol.ServerError) {
	resp, err := c.typedStub().DeleteStreamItem(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListStream(ctx context.Context, in *following_stream.ListStreamReq,
	opts ...grpc.CallOption) (*following_stream.ListStreamResp, protocol.ServerError) {
	resp, err := c.typedStub().ListStream(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchListStream(ctx context.Context, in *following_stream.BatchListStreamReq,
	opts ...grpc.CallOption) (*following_stream.BatchListStreamResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchListStream(ctx, in)
	return resp, protocol.ToServerError(err)
}
