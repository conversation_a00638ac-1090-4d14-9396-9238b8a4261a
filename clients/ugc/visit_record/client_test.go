package visit_record

import (
	"context"
	"fmt"
	"math/rand"
	"testing"

	"github.com/globalsign/mgo/bson"
	"golang.52tt.com/protocol/services/ugc/visit_record"
)

func TestClient(t *testing.T) {
	client, err := NewClient()
	if err != nil {
		t.Error(err)
		return
	}

	var postId = bson.NewObjectId().Hex()

	var id = rand.Uint32()
	_, err = client.AddVisitRecord(context.Background(), &visit_record.AddVisitRecordReq{UserId: id, PostIds: []string{postId}, Type: TypeVisit})
	if err != nil {
		t.Error(err)
		return
	}

	resp, err := client.GetVisitRecord(context.Background(), &visit_record.GetVisitRecordReq{UserId: id, Type: TypeVisit})
	if err != nil {
		t.Error(err)
		return
	}
	fmt.Println(resp.PostIds)
	if resp.PostIds[len(resp.PostIds)-1] != postId {
		t.Error("post id error")
		return
	}
}
