package celebrity

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/ugc/celebrity"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	Celebrities(ctx context.Context, page uint32, number uint32, all bool) ([]*pb.Celebrity, protocol.ServerError)
	CelebritiesForTT(ctx context.Context, offset uint32, limit uint32) ([]*pb.Celebrity, uint32, protocol.ServerError)
	Users(ctx context.Context, offset uint32, limit uint32, userType pb.UsersReq_UserType) ([]uint32, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
