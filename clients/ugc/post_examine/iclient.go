package post_examine

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/ugc/post_examine"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetNewestPostList(ctx context.Context, in *pb.GetNewestPostListReq) (*pb.GetNewestPostListResp, protocol.ServerError)
	ExamineNewestPost(ctx context.Context, in *pb.ExamineNewestPostReq) (*pb.ExamineNewestPostResp, protocol.ServerError)
	RemoveFromNewestPostList(ctx context.Context, postId string) error
	GetHotPostList(ctx context.Context, in *pb.GetHotPostListReq) (*pb.GetHotPostListResp, protocol.ServerError)
	GetHotPostListLite(ctx context.Context, in *pb.GetHotPostListLiteReq) (*pb.GetHotPostListLiteResp, protocol.ServerError)
	ExamineHotPost(ctx context.Context, postId, topicId string, status pb.HotPostExamineStatus, hotVal, author uint32) protocol.ServerError
	BatchUpdatePostHotVal(ctx context.Context, in *pb.BatchUpdatePostHotValReq) (*pb.BatchUpdatePostHotValResp, protocol.ServerError)
	UpsertPostHotVal(ctx context.Context, in *pb.UpsertPostHotValReq) protocol.ServerError
	RemoveFromHotPostList(ctx context.Context, postId string) protocol.ServerError
	GetHotPostInfo(ctx context.Context, postId string, queryOption pb.GetHotPostInfoReq_QueryOption) ([]*pb.HotPostInfo, protocol.ServerError)
	GetAllHotPostConfig(ctx context.Context) ([]*pb.HotPostConfig, error)
	UpdateHotPostConfig(ctx context.Context, topicId string, thresholdVal uint32, so float64) error
	UpdatePostHotVal(ctx context.Context, postId string, expectVal uint32) error
	RecordPostInteract(ctx context.Context, in *pb.RecordPostInteractReq) (uint32, protocol.ServerError)
	QueryPostInteractByDay(ctx context.Context, startDate, endDate, postId string) ([]*pb.PostInteractInfo, protocol.ServerError)
	QueryPostInteractCount(ctx context.Context, postId string) (*pb.PostInteractInfo, protocol.ServerError)
	UpdatePostRecommendWeight(ctx context.Context, postId string, recommendWeight float64) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
