package recommend_backup

import (
	"context"
	. "github.com/onsi/ginkgo"
	grpc2 "golang.52tt.com/pkg/protocol/grpc"
	"google.golang.org/grpc"
	"log"
)

var _ = Describe("ugc-recommend-backup", func() {
	cli, err := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("ugc-recommend-backup.52tt.com"))
	if err != nil {
		log.Printf("RecommendBackup NewClient err: %v", err)
		return
	}

	// 获取兜底推荐流
	ctx := grpc2.WithServiceInfo(context.Background(), &grpc2.ServiceInfo{UserID: 2405864})
	rsp, err := cli.GetRecommendBackup(ctx, 20, 1)
	if err != nil {
		log.Printf("RecommendBackup GetCommendBackup err: %v", err)
		return
	}

	log.Printf("GetRecommendBackup rsp: %v", rsp)
})
