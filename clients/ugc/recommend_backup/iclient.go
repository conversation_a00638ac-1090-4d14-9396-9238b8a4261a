package recommend_backup

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/ugc/recommend-backup"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetRecommendBackup(ctx context.Context, limit uint32, contentType int32) (*pb.GetRecommendBackupRsp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
