package emoji

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/emoji"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	CreateEmoji(ctx context.Context, in *pb.CreateEmojiReq) (*pb.CreateEmojiResp, protocol.ServerError)
	CheckEmojiExists(ctx context.Context, in *pb.CheckEmojiExistsReq) (*pb.CheckEmojiExistsResp, protocol.ServerError)
	AddEmojiToPackage(ctx context.Context, in *pb.AddEmojiToPackageReq) (*pb.AddEmojiToPackageResp, protocol.ServerError)
	DelEmoji(ctx context.Context, in *pb.DeleteEmojiReq) (*pb.DeleteEmojiResp, protocol.ServerError)
	RemoveEmojiFromPackage(ctx context.Context, in *pb.RemoveEmojiFromPackageReq) (*pb.RemoveEmojiFromPackageResp, protocol.ServerError)
	GetEmoji(ctx context.Context, in *pb.GetEmojiReq) (*pb.GetEmojiResp, protocol.ServerError)
	GetEmojiInfo(ctx context.Context, in *pb.GetEmojiReq) (*pb.GetEmojiInfoResp, protocol.ServerError)
	GetEmojiListByPackage(ctx context.Context, in *pb.GetEmojiListByPackageReq) (*pb.GetEmojiListByPackageResp, protocol.ServerError)
	DeletePackage(ctx context.Context, in *pb.DeletePackageReq) (*pb.DeletePackageResp, protocol.ServerError)
	ModifyPublicPackage(ctx context.Context, in *pb.ModifyPublicPackageReq) (*pb.ModifyPublicPackageResp, protocol.ServerError)
	GetPublicPackage(ctx context.Context, in *pb.GetPublicPackageReq) (*pb.GetPublicPackageResp, protocol.ServerError)
	AddEmojiAntiMapping(ctx context.Context, req *pb.AddEmojiAntiMappingReq) protocol.ServerError
	CheckEmojiAntiMappingExists(ctx context.Context, emojiId string) (bool, protocol.ServerError)
	BatchFindEmojiAntiMapping(ctx context.Context, emojiIds []string) (map[string]string, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
