package parent_guardian

import (
	"context"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/parent-guardian"
)

const (
	serviceName = "parent-guardian"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewParentGuardianClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ParentGuardianClient { return c.Stub().(pb.ParentGuardianClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetParentGuardianState(ctx context.Context, uid uint64) (bool, protocol.ServerError) {
	resp, err := c.typedStub().GetParentGuardianState(ctx, &pb.ParentGuardianStateReq{
		Uid: uint32(uid),
	})

	if err != nil {
		return false, protocol.ToServerError(err)
	}

	return resp.OnOff, nil
}

func (c *Client) ParentGuardianUpdatePassword(ctx context.Context, uid uint64, pwd string) protocol.ServerError {
	_, err := c.typedStub().ParentGuardianUpdatePassword(ctx, &pb.ParentGuardianUpdatePwdReq{
		Uid:      uint32(uid),
		Password: pwd,
	})

	if err != nil {
		return protocol.ToServerError(err)
	}

	return nil
}

func (c *Client) SwitchParentGuardian(ctx context.Context, uid uint64, pwd string, onOff bool, forceOff bool) protocol.ServerError {
	_, err := c.typedStub().SwitchParentGuardian(ctx, &pb.ParentGuardianSwitchReq{
		Uid:        uint32(uid),
		OnOff:      onOff,
		Password:   pwd,
		IsForceOff: forceOff,
	})

	if err != nil {
		return protocol.ToServerError(err)
	}

	return nil
}

func (c *Client) CheckAppealCntIsOverLimit(ctx context.Context, uid uint64) (bool, protocol.ServerError) {
	resp, err := c.typedStub().CheckAppealCntIsOverLimit(ctx, &pb.CheckAppealCntIsOverLimitReq{
		Uid: uint32(uid),
	})

	if err != nil {
		return false, protocol.ToServerError(err)
	}

	return resp.IsOverLimit, nil
}

func (c *Client) CheckParentGuardianPassword(ctx context.Context, uid uint64, password string) (bool, protocol.ServerError) {
	resp, err := c.typedStub().CheckParentGuardianPassword(ctx, &pb.ParentGuardianCheckPasswordReq{
		Uid:      uint32(uid),
		Password: password,
	})

	if err != nil {
		return false, protocol.ToServerError(err)
	}

	return resp.IsPass, nil
}
