package configserver

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/configserver"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	BatchGetUserConfig(ctx context.Context, req pb.BatchGetUserConfigReq) (*pb.BatchGetUserConfigRsp, protocol.ServerError)
	BatchSaveConfig(ctx context.Context, req pb.BatchSaveConfigReq) (*pb.BatchSaveConfigRsp, protocol.ServerError)
	GetConfig(ctx context.Context, req pb.GetConfigReq) (*pb.GetConfigRsp, protocol.ServerError)
	GetUsersConfig(ctx context.Context, req pb.GetUsersConfigReq) (*pb.GetUsersConfigRsp, protocol.ServerError)
	GetUgcMoreConfigList(ctx context.Context, req pb.GetUgcMoreConfigListReq) (*pb.GetUgcMoreConfigListResp, protocol.ServerError)
	GetFallBackSwitchByTypes(ctx context.Context, req pb.GetFallBackSwitchByTypesReq) (*pb.GetFallBackSwitchByTypesResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
