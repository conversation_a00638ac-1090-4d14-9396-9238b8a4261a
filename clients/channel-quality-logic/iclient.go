package channel_quality

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/app/channel-quality"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	QualityEventReport(ctx context.Context, in *pb.QualityEventReportReq) (*pb.QualityEventReportResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
