// Code generated by quicksilver-cli. DO NOT EDIT.
package channeldatinggame

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/channel-dating-game"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	CheckDatingGameEntry(ctx context.Context, cid uint32) (bool,uint32,error)
	GetDatingGameCurInfo(ctx context.Context, cid uint32) (*pb.GetDatingGameCurInfoResp,error)
	GetGamePhase(ctx context.Context, channelId uint32) (uint32,protocol.ServerError)
	UserApplyMic(ctx context.Context, channelId, uid uint32, cancel bool) protocol.ServerError
	TestDrawImage(ctx context.Context, req *pb.TestDrawImageReq) (*pb.TestDrawImageResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
