package smash_egg_notify_logic

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	app_pb "golang.52tt.com/protocol/app/smash-egg-notify"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetNotify(ctx context.Context, req *app_pb.GetNotifyReq) (*app_pb.GetNotifyResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
