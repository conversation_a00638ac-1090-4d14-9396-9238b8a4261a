package appconfigsvr_go

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	"google.golang.org/grpc"

	traceGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/appconfigsvr-go"
)

const (
	serviceName = "appconfigsvr-go"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer, dopts ...grpc.DialOption) *Client {
	tracerInt := traceGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(tracerInt))
	return newClient(dopts...)
}

func newClient(dopts ...grpc.DialOption) *Client {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewAppConfigSvrGoClient(cc)
		}, dopts...),
	}
}

func (c *Client) typedStub() pb.AppConfigSvrGoClient {
	return c.Stub().(pb.AppConfigSvrGoClient)
}

func (c *Client) GetAppAuditSw(ctx context.Context, in *pb.GetAppAuditSwReq) (*pb.GetAppAuditSwResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAppAuditSw(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetAppAuditSw(ctx context.Context, in *pb.BatchGetAppAuditSwReq) (*pb.BatchGetAppAuditSwResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetAppAuditSw(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAccountIsolationSwConfig(ctx context.Context, in *pb.GetAccountIsolationSwConfigReq) (*pb.GetAccountIsolationSwConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAccountIsolationSwConfig(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetCurHighestVersion(ctx context.Context, os, marketId uint32) (*pb.GetCurHighestVersionResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCurHighestVersion(ctx, &pb.GetCurHighestVersionReq{Os: os, MarketId: marketId})
	return resp, protocol.ToServerError(err)
}

