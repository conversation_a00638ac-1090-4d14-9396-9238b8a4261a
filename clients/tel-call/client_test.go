package telcall

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/services/telcall"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {

	<PERSON><PERSON>("GetTelCall", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.TelCallReq
		resp, err := client.GetTelCall(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetTelCall %+v", resp)
	})

}
