package session

import (
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	"golang.org/x/net/context"
	"google.golang.org/grpc"

	pb "golang.52tt.com/protocol/services/sessionsvr"
)

type IClient interface {
	client.BaseClient
	GetSession(ctx context.Context, uid, app uint32) (string, protocol.ServerError)
	CreateSession(ctx context.Context, uid, app uint32) (*pb.SessionInfo, protocol.ServerError)
	RemoveMultiSession(ctx context.Context, uid uint32, includeAppIds, excludeAppIds []uint32) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
