package rush

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"math/rand"
	"testing"
	"time"
)

var (
	cli, _ = NewClient(grpc.WithBlock())
)

func TestClient_GetActs(t *testing.T) {
	now := time.Now()
	begin := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	end := begin.AddDate(0, 0, 2)
	acts, err := cli.GetRushEvents(context.Background(), begin, end, 0)
	if err != nil {
		t.<PERSON><PERSON>("GetRushEvents failed: %v", err)
	} else {
		t.Logf("GetRushEvents OK")
		for idx, act := range acts {
			b := time.Unix(int64(act.Begin), 0)
			e := time.Unix(int64(act.End), 0)
			t.Logf("#%d: %v %v %s %v", idx+1, b, e, act.Name, act.ProductAmount)
		}
	}
}

func BenchmarkClient_Rush(b *testing.B) {
	b.StopTimer()
	clients := make([]*Client, 20)
	for i := 0; i < len(clients); i++ {
		clients[i], _ = NewClient(grpc.WithBlock())
	}

	b.StartTimer()
	b.SetParallelism(300)
	b.RunParallel(func(pb *testing.PB) {
		cli := clients[rand.Int()%len(clients)]
		for pb.Next() {
			cli.Rush(context.Background(), 500009, 235, 214)
		}
	})
}
