package channel_lottery_logic

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	logic "golang.52tt.com/protocol/services/logicsvr-go/channel-lottery-logic"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-lottery-logic"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return logic.NewChannelLotteryLogicClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() logic.ChannelLotteryLogicClient {
	return c.Stub().(logic.ChannelLotteryLogicClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

// XXXX
// func (c *Client) XXXX(ctx context.Context, in *pb.XXXXReq) (*pb.XXXXResp, protocol.ServerError) {
// 	resp, err := c.typedStub().XXXX(ctx, in)
// 	return resp, protocol.ToServerError(err)
// }