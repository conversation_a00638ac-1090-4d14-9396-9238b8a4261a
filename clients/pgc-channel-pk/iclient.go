// Code generated by quicksilver-cli. DO NOT EDIT.
package pgc_channel_pk

import (
	"context"
	"golang.52tt.com/pkg/client"
	pgc_channel_pk "golang.52tt.com/protocol/services/pgc-channel-pk"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AcceptPgcChannelPK(ctx context.Context, req *pgc_channel_pk.AcceptPgcChannelPKReq, opts ...grpc.CallOption) (*pgc_channel_pk.AcceptPgcChannelPKResp, error)
	GetPgcChannelPKAudienceRank(ctx context.Context, req *pgc_channel_pk.GetPgcChannelPKAudienceRankReq, opts ...grpc.CallOption) (*pgc_channel_pk.GetPgcChannelPKAudienceRankResp, error)
	GetPgcChannelPKChannelList(ctx context.Context, req *pgc_channel_pk.GetPgcChannelPKChannelListReq, opts ...grpc.CallOption) (*pgc_channel_pk.GetPgcChannelPKChannelListResp, error)
	GetPgcChannelPKEntry(ctx context.Context, req *pgc_channel_pk.GetPgcChannelPKEntryReq, opts ...grpc.CallOption) (*pgc_channel_pk.GetPgcChannelPKEntryResp, error)
	GetPgcChannelPKInfo(ctx context.Context, req *pgc_channel_pk.GetPgcChannelPKInfoReq, opts ...grpc.CallOption) (*pgc_channel_pk.GetPgcChannelPKInfoResp, error)
	GetPgcChannelPKSendGiftScore(ctx context.Context, req *pgc_channel_pk.GetPgcChannelPKSendGiftScoreReq, opts ...grpc.CallOption) (*pgc_channel_pk.GetPgcChannelPKSendGiftScoreResp, error)
	PgcChannelPKReportClientIDChange(ctx context.Context, req *pgc_channel_pk.PgcChannelPKReportClientIDChangeReq, opts ...grpc.CallOption) (*pgc_channel_pk.PgcChannelPKReportClientIDChangeResp, error)
	SetPgcChannelPKOpponentMicFlag(ctx context.Context, req *pgc_channel_pk.SetPgcChannelPKOpponentMicFlagReq, opts ...grpc.CallOption) (*pgc_channel_pk.SetPgcChannelPKOpponentMicFlagResp, error)
	SetPgcChannelPKSwitch(ctx context.Context, req *pgc_channel_pk.SetPgcChannelPKSwitchReq, opts ...grpc.CallOption) (*pgc_channel_pk.SetPgcChannelPKSwitchResp, error)
	StartPgcChannelPK(ctx context.Context, req *pgc_channel_pk.StartPgcChannelPKReq, opts ...grpc.CallOption) (*pgc_channel_pk.StartPgcChannelPKResp, error)
	TestPushMvpImMsg(ctx context.Context, req *pgc_channel_pk.TestPushMvpImMsgReq, opts ...grpc.CallOption) (*pgc_channel_pk.TestPushMvpImMsgResp, error)
	TestSetPgcChannelPKStatus(ctx context.Context, req *pgc_channel_pk.TestSetPgcChannelPKStatusReq, opts ...grpc.CallOption) (*pgc_channel_pk.TestSetPgcChannelPKStatusResp, error)
	ChoseInteraction(ctx context.Context, req *pgc_channel_pk.ChoseInteractionReq, opts ...grpc.CallOption) (*pgc_channel_pk.ChoseInteractionResp, error)
	GetPgcChannelPKId(ctx context.Context, req *pgc_channel_pk.GetPgcChannelPKIdReq, opts ...grpc.CallOption) (*pgc_channel_pk.GetPgcChannelPKIdResp, error)
	SetPgcChannelPKEnd(ctx context.Context, req *pgc_channel_pk.SetPgcChannelPKEndReq, opts ...grpc.CallOption) (*pgc_channel_pk.SetPgcChannelPKEndResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
