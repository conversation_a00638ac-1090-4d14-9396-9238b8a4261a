package comm_push_schedule

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/comm-push-schedule"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	OfflineTimedQueuePush(ctx context.Context, in *pb.OfflineTimedQueuePushReq) (out *pb.OfflineTimedQueuePushResp, err error)
	GetPushQueueLen(ctx context.Context, in *pb.GetPushQueueLenReq) (out *pb.GetPushQueueLenResp, err error)
	BatchGetPushQLen(ctx context.Context, in *pb.BatchGetPushQLenReq) (out *pb.BatchGetPushQLenResp, err error)
	DirectOfflinePush(ctx context.Context, in *pb.DirectOfflinePushReq) (out *pb.DirectOfflinePushResp, err error)
	BatchDirectOfflinePush(ctx context.Context, in *pb.BatchDirectOfflinePushReq) (out *pb.BatchDirectOfflinePushResp, err error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
