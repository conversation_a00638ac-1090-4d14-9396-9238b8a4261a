/*
 * @Description:
 * @Date: 2021-07-09 16:01:43
 * @LastEditors: liang
 * @LastEditTime: 2021-08-09 11:07:30
 */
package comm_push_schedule

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/comm-push-schedule"
	"google.golang.org/grpc"
)

const (
	serviceName = "comm-push-schedule"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewCommPushScheduleClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.CommPushScheduleClient { return c.Stub().(pb.CommPushScheduleClient) }

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func (c *Client) OfflineTimedQueuePush(ctx context.Context, in *pb.OfflineTimedQueuePushReq) (out *pb.OfflineTimedQueuePushResp, err error) {
	out, err = c.typedStub().OfflineTimedQueuePush(ctx, in)
	return out, err
}

func (c *Client) GetPushQueueLen(ctx context.Context, in *pb.GetPushQueueLenReq) (out *pb.GetPushQueueLenResp, err error) {
	out, err = c.typedStub().GetPushQueueLen(ctx, in)
	return out, err
}

func (c *Client) BatchGetPushQLen(ctx context.Context, in *pb.BatchGetPushQLenReq) (out *pb.BatchGetPushQLenResp, err error) {
	out, err = c.typedStub().BatchGetPushQLen(ctx, in)
	return out, err
}

func (c *Client) DirectOfflinePush(ctx context.Context, in *pb.DirectOfflinePushReq) (out *pb.DirectOfflinePushResp, err error) {
	out, err = c.typedStub().DirectOfflinePush(ctx, in)
	return out, err
}

func (c *Client) BatchDirectOfflinePush(ctx context.Context, in *pb.BatchDirectOfflinePushReq) (out *pb.BatchDirectOfflinePushResp, err error) {
	out, err = c.typedStub().BatchDirectOfflinePush(ctx,in)
	return out,err
}
