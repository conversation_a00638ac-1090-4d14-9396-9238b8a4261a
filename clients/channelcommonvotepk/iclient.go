package channelcommonvotepk

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channelvotepkcommonsvr"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	ChannelCommonPkVote(ctx context.Context, from, to, channelID, startTime uint32) (*pb.ChannelCommonPkVoteResp, protocol.ServerError)
	GetUserLeftVoteCnt(ctx context.Context, uin, channelID, startTime uint32) (*pb.GetUserLeftVoteCntResp, protocol.ServerError)
	SetChannelCommonPkInfo(ctx context.Context, channelId, startTime, dur, limit uint32) (*pb.SetChannelCommonPkInfoResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
