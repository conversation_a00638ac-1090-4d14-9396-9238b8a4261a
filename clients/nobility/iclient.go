// Code generated by quicksilver-cli. DO NOT EDIT.
package nobility

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	Nobility "golang.52tt.com/protocol/services/nobilitysvr"
	context "context"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AddTempNobility(ctx context.Context, uid, beginTs, level uint32) (*Nobility.AddTempNobilityResp,protocol.ServerError)
	AddUserNobilityValue(ctx context.Context, uid uint32, val uint64, orderId, source string) (*Nobility.AddUserNobilityValueResp,protocol.ServerError)
	BatchGetNobilityInfo(ctx context.Context, uid uint32, uidList []uint32) (map[uint32]*Nobility.NobilityInfo,protocol.ServerError)
	FixConsumeOrder(ctx context.Context, req *Nobility.FixConsumeOrderReq) (*Nobility.FixConsumeOrderResp,protocol.ServerError)
	GetConsumeOrderCount(ctx context.Context, req *Nobility.GetConsumeOrderCountReq) (*Nobility.GetConsumeOrderCountResp,protocol.ServerError)
	GetConsumeOrderList(ctx context.Context, req *Nobility.GetConsumeOrderListReq) (*Nobility.GetConsumeOrderListResp,protocol.ServerError)
	GetInvisibleStatus(ctx context.Context, uid uint32) (bool,protocol.ServerError)
	GetNobilityInfo(ctx context.Context, uid uint32, queryRecords bool) (*Nobility.NobilityInfo,protocol.ServerError)
	GetTempNobilityList(ctx context.Context, isValid bool) (*Nobility.GetTempNobilityListResp,protocol.ServerError)
	GetTrumpetLeftCnt(ctx context.Context, uid, privilegeId, cycleTs uint32) (*Nobility.GetTrumpetLeftCntResp,protocol.ServerError)
	ReduceTrumpetLeftCnt(ctx context.Context, uid, privilegeId, cycleTs uint32) (*Nobility.ReduceTrumpetLeftCntResp,protocol.ServerError)
	SetInvisibleStatus(ctx context.Context, uid uint32, status int32) (*Nobility.SetInvisibleStatusResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
