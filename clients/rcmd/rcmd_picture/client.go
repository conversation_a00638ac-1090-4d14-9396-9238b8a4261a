package rcmd_get_picture

import (
	"context"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	pb "golang.52tt.com/protocol/services/rcmd/rcmd_picture"
	"google.golang.org/grpc"
)

const (
	serviceName = "rcmd-picture"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewRCMDPictureClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.RCMDPictureClient {
	return c.Stub().(pb.RCMDPictureClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetPictures(ctx context.Context, in *pb.GetPicturesReq) (*pb.GetPicturesResp, error) {
	return c.typedStub().GetPictures(ctx, in)
}
