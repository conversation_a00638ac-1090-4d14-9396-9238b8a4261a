package ai_partner

import (
	"context"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner"
	"google.golang.org/grpc"
)

const (
	serviceName = "rcmd-ai-partner"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewRCMDAIPartnerClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.RCMDAIPartnerClient {
	return c.Stub().(pb.RCMDAIPartnerClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) SetAIPartnerInfo(ctx context.Context, in *pb.SetAIPartnerInfoReq, opts ...grpc.CallOption) (out *pb.SetAIPartnerInfoResp, err error) {
	out, err = c.typedStub().SetAIPartnerInfo(ctx, in, opts...)
	return out, protocol.ToServerError(err)
}

func (c *Client) ReceiveMsgFromUser(ctx context.Context, in *pb.ReceiveMsgFromUserReq, opts ...grpc.CallOption) (out *pb.ReceiveMsgFromUserResp, err error) {
	out, err = c.typedStub().ReceiveMsgFromUser(ctx, in, opts...)
	return out, protocol.ToServerError(err)
}

func (c *Client) UserEnterChattingNotify(ctx context.Context, in *pb.UserEnterChattingNotifyReq, opts ...grpc.CallOption) (out *pb.UserEnterChattingNotifyResp, err error) {
	out, err = c.typedStub().UserEnterChattingNotify(ctx, in, opts...)
	return out, protocol.ToServerError(err)
}

func (c *Client) SetAIPartnerChattingStatus(ctx context.Context, in *pb.SetAIPartnerChattingStatusReq, opts ...grpc.CallOption) (out *pb.SetAIPartnerChattingStatusResp, err error) {
	out, err = c.typedStub().SetAIPartnerChattingStatus(ctx, in, opts...)
	return out, protocol.ToServerError(err)
}

func (c *Client) Trigger(ctx context.Context, in *pb.TriggerReq, opts ...grpc.CallOption) (out *pb.TriggerResp, err error) {
	out, err = c.typedStub().Trigger(ctx, in, opts...)
	return out, protocol.ToServerError(err)
}
