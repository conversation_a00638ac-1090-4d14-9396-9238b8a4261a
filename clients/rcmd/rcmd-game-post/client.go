package rcmd_game_post

import (
	"context"
	"golang.52tt.com/pkg/protocol"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/rcmd/rcmd_game_post"
	"google.golang.org/grpc"
)

const (
	serviceName = "rcmd-game-post"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewRcmdGamePostClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.RcmdGamePostClient {
	return c.Stub().(pb.RcmdGamePostClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetRcmdGamePost(ctx context.Context, in *pb.GetRcmdGamePostReq) (out *pb.GetRcmdGamePostRsp, err error) {
	resp, err := c.typedStub().GetRcmdGamePost(ctx, in)
	return resp, protocol.ToServerError(err)
}
