// Code generated by quicksilver-cli. DO NOT EDIT.
package rcmd_game_post

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/rcmd/rcmd_game_post"
)

type IClient interface {
	client.BaseClient
	GetRcmdGamePost(ctx context.Context, in *pb.GetRcmdGamePostReq) (out *pb.GetRcmdGamePostRsp,err error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
