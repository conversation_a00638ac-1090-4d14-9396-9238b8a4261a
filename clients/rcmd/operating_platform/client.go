package operating_platform

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/rcmd/operating_platform"
	"google.golang.org/grpc"
)

const (
	serviceName = "rcmd-operating-platform"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewRcmdOperatingPlatformClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.RcmdOperatingPlatformClient {
	return c.Stub().(pb.RcmdOperatingPlatformClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetHighInteractPostList(ctx context.Context, req pb.GetHighInteractPostListReq) (*pb.GetHighInteractPostListRsp, protocol.ServerError) {
	resp, err := c.typedStub().GetHighInteractPostList(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetVCPostList(ctx context.Context, req *pb.GetVCPostListReq) (*pb.GetVCPostListRsp, protocol.ServerError) {
	resp, err := c.typedStub().GetVCPostList(ctx, req)
	return resp, protocol.ToServerError(err)
}
