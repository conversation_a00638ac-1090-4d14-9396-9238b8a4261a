package music_song_ktv

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/rcmd/music_song"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetListenSongOrder(ctx context.Context, in *pb.ListenSongOrderReq) (out *pb.ListenSongOrderRsp, err error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
