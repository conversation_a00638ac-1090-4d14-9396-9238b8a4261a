package rcmd_associate_word

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/rcmd/rcmd_search/rcmd_associate_word"
	"google.golang.org/grpc"
)

const (
	serviceName = "rcmd-entrance-server"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewRcmdAssociateWordClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.RcmdAssociateWordClient {
	return c.Stub().(pb.RcmdAssociateWordClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) SearchAssociateWord(ctx context.Context, req *pb.SearchAssociateWordReq) (*pb.SearchAssociateWordResp, protocol.ServerError) {
	resp, err := c.typedStub().SearchAssociateWord(ctx, req)
	return resp, protocol.ToServerError(err)
}
