package ugc_live_together

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/ugc-live-together"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetChannelLiveInfo(ctx context.Context, in *pb.GetChannelLiveInfoReq) (out *pb.GetChannelLiveInfoResp, err error)
	SetChannelLiveStatus(ctx context.Context, in *pb.SetChannelLiveStatusReq) (out *pb.SetChannelLiveStatusResp, err error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
