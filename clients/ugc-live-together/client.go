package ugc_live_together

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/ugc-live-together"
	"google.golang.org/grpc"
)

const (
	serviceName = "ugc-live-together"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewUgcLiveTogetherClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.UgcLiveTogetherClient { return c.Stub().(pb.UgcLiveTogetherClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetChannelLiveInfo(ctx context.Context, in *pb.GetChannelLiveInfoReq) (out *pb.GetChannelLiveInfoResp, err error) {
	return c.typedStub().GetChannelLiveInfo(ctx, in)
}

func (c *Client) SetChannelLiveStatus(ctx context.Context, in *pb.SetChannelLiveStatusReq) (out *pb.SetChannelLiveStatusResp, err error) {
	return c.typedStub().SetChannelLiveStatus(ctx, in)
}
