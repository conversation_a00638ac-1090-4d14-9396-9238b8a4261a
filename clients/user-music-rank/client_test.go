package user_music_rank

import (
	"context"
	"fmt"
	"github.com/stretchr/testify/assert"
	pb "golang.52tt.com/protocol/services/user-music-rank"
	"google.golang.org/grpc"
	"testing"
)

var (
	cli *Client
	ctx = context.Background()

	// 操作者
	uid = uint32(2207805)
)

func init() {
	var err error
	cli, err = NewClientTo("10.112.55.118:80", grpc.WithBlock(), grpc.WithAuthority("user-music-rank.52tt.local"))
	if err != nil {
		panic(err)
	}
}

func TestClient_GetUserLocationAuth(t *testing.T) {
	a := assert.New(t)
	req := &pb.GetUserLocationAuthReq{
		Uid: uid,
	}
	_, err := cli.GetUserLocationAuth(ctx, req)
	a.Nil(err)
}

func TestClient_GetMusicRankUserInfo(t *testing.T) {
	client, err := NewClientTo("127.0.0.1:8080", grpc.WithBlock())
	if err != nil {
		t.Fail()
	}
	a, err := client.GetMusicRankUserInfo(context.TODO(), &pb.GetMusicRankUserInfoReq{
		NeedMic: false,
		Uids:    []uint32{2285686},
	})

	fmt.Println(a)
	fmt.Println(err)
}
