package user_music_rank

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/user-music-rank"
	"google.golang.org/grpc"
)

const (
	serviceName = "user-music-rank"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewUserMusicRankClient(cc)
			}, dopts...,
		),
	}, nil
}

func NewClientTo(target string, dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewGrpcClientTo(
			target,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewUserMusicRankClient(cc)
			}, dopts...),
	}, nil
}

func (c *Client) typedStub() pb.UserMusicRankClient {
	return c.Stub().(pb.UserMusicRankClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) BatchUserPowerInfo(ctx context.Context, in *pb.BatchUserPowerInfoReq) (*pb.BatchUserPowerInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchUserPowerInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListUserGlories(ctx context.Context, in *pb.ListUserGloriesReq) (*pb.ListUserGloriesResp, protocol.ServerError) {
	resp, err := c.typedStub().ListUserGlories(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetUserGlory(ctx context.Context, in *pb.SetUserGloryReq) (*pb.SetUserGloryResp, protocol.ServerError) {
	resp, err := c.typedStub().SetUserGlory(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetUserLocation(ctx context.Context, in *pb.SetUserLocationReq) (*pb.SetUserLocationResp, protocol.ServerError) {
	resp, err := c.typedStub().SetUserLocation(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListUserSingerScore(ctx context.Context, in *pb.ListUserSingerScoreReq) (*pb.ListUserSingerScoreResp, protocol.ServerError) {
	resp, err := c.typedStub().ListUserSingerScore(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserSingerScoreDetail(ctx context.Context, in *pb.GetUserSingerScoreDetailReq) (*pb.GetUserSingerScoreDetailResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserSingerScoreDetail(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListUserMusicRecords(ctx context.Context, in *pb.ListUserMusicRecordsReq) (*pb.ListUserMusicRecordsResp, protocol.ServerError) {
	resp, err := c.typedStub().ListUserMusicRecords(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetStarRank(ctx context.Context, in *pb.GetStarRankReq) (*pb.GetStarRankResp, protocol.ServerError) {
	resp, err := c.typedStub().GetStarRank(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSingerScoreRank(ctx context.Context, in *pb.GetSingerScoreRankReq) (*pb.GetSingerScoreRankResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSingerScoreRank(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchUserGlory(ctx context.Context, in *pb.BatchUserGloryReq) (*pb.BatchUserGloryResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchUserGlory(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetMusicRankUserInfo(ctx context.Context, in *pb.GetMusicRankUserInfoReq) (*pb.GetMusicRankUserInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetMusicRankUserInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserMatchGlory(ctx context.Context, in *pb.GetUserMatchGloryReq) (*pb.GetUserMatchGloryResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserMatchGlory(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserLocation(ctx context.Context, in *pb.GetUserLocationReq) (*pb.GetUserLocationResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserLocation(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetUserLocationAuth(ctx context.Context, in *pb.SetUserLocationAuthReq) (*pb.SetUserLocationAuthResp, protocol.ServerError) {
	resp, err := c.typedStub().SetUserLocationAuth(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserLocationAuth(ctx context.Context, in *pb.GetUserLocationAuthReq) (*pb.GetUserLocationAuthResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserLocationAuth(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserMusicRankDialog(ctx context.Context, in *pb.GetUserMusicRankDialogReq) (*pb.GetUserMusicRankDialogResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserMusicRankDialog(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UserMusicRankDialogConfirm(ctx context.Context, in *pb.UserMusicRankDialogConfirmReq) (*pb.UserMusicRankDialogConfirmResp, protocol.ServerError) {
	resp, err := c.typedStub().UserMusicRankDialogConfirm(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) IsSongsInRank(ctx context.Context, in *pb.IsSongsInRankReq) (*pb.IsSongsInRankResp, protocol.ServerError) {
	resp, err := c.typedStub().IsSongsInRank(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRatingForScore(ctx context.Context, in *pb.GetRatingForScoreReq) (*pb.GetRatingForScoreResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRatingForScore(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSeasonWebInfo(ctx context.Context, in *pb.GetSeasonWebInfoReq) (*pb.GetSeasonWebInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSeasonWebInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListSchools(ctx context.Context, in *pb.ListSchoolsReq) (*pb.ListSchoolsResp, protocol.ServerError) {
	resp, err := c.typedStub().ListSchools(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BindSchool(ctx context.Context, in *pb.BindSchoolReq) (*pb.BindSchoolResp, protocol.ServerError) {
	resp, err := c.typedStub().BindSchool(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserSchoolInfo(ctx context.Context, in *pb.GetUserSchoolInfoReq) (*pb.GetUserSchoolInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserSchoolInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserRankInSchool(ctx context.Context, in *pb.GetUserRankInSchoolReq) (*pb.GetUserRankInSchoolResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserRankInSchool(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserRankInCity(ctx context.Context, in *pb.GetUserRankInCityReq) (*pb.GetUserRankInCityResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserRankInCity(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSchoolRankInCity(ctx context.Context, in *pb.GetSchoolRankInCityReq) (*pb.GetSchoolRankInCityResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSchoolRankInCity(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSchoolRank(ctx context.Context, in *pb.GetSchoolRanReq) (*pb.GetSchoolRanResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSchoolRank(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetMirrorId(ctx context.Context, in *pb.GetMirrorIdReq) (*pb.GetMirrorIdResp, protocol.ServerError) {
	resp, err := c.typedStub().GetMirrorId(ctx, in)
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetMirrorData(ctx context.Context, in *pb.GetMirrorDataReq) (*pb.GetMirrorDataResp, protocol.ServerError) {
	resp, err := c.typedStub().GetMirrorData(ctx, in)
	return resp, protocol.ToServerError(err)
}
