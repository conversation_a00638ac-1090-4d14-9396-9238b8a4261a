package usercomplaint

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/user-complaint"
	"google.golang.org/grpc"
)

const (
	serviceName = "user-complaint"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewUserComplaintClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.UserComplaintClient { return c.Stub().(pb.UserComplaintClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) *Client {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	cli, _ := newClient(dopts...)
	return cli
}

func (c *Client) GetUserComplaintEntry(ctx context.Context, uid, guildDisplayId, anchorUid uint32, channelName string) (*pb.GetUserComplaintEntryResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserComplaintEntry(ctx, &pb.GetUserComplaintEntryReq{Uid: uid, GuildDisplayId: guildDisplayId, AnchorUid: anchorUid, ChannelName: channelName})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserComplaintList(ctx context.Context, in *pb.GetUserComplaintListReq) (*pb.GetUserComplaintListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserComplaintList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CreateComplaint(ctx context.Context, in *pb.CreateComplaintReq) (*pb.CreateComplaintResp, protocol.ServerError) {
	resp, err := c.typedStub().CreateComplaint(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGuildComplaintList(ctx context.Context, in *pb.GetGuildComplaintListReq) (*pb.GetGuildComplaintListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGuildComplaintList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SubmitGuildComplaint(ctx context.Context, in *pb.SubmitGuildComplaintReq) (*pb.SubmitGuildComplaintResp, protocol.ServerError) {
	resp, err := c.typedStub().SubmitGuildComplaint(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ArchiveUserComplaint(ctx context.Context, in *pb.ArchiveUserComplaintReq) (*pb.ArchiveUserComplaintResp, protocol.ServerError) {
	resp, err := c.typedStub().ArchiveUserComplaint(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserComplaintByRecordId(ctx context.Context, in *pb.GetUserComplaintByRecordIdReq) (*pb.GetUserComplaintByRecordIdResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserComplaintByRecordId(ctx, in)
	return resp, protocol.ToServerError(err)
}
