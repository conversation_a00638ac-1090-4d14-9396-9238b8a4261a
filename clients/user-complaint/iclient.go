package usercomplaint

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/user-complaint"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetUserComplaintEntry(ctx context.Context, uid, guildDisplayId, anchorUid uint32, channelName string) (*pb.GetUserComplaintEntryResp, protocol.ServerError)
	GetUserComplaintList(ctx context.Context, in *pb.GetUserComplaintListReq) (*pb.GetUserComplaintListResp, protocol.ServerError)
	CreateComplaint(ctx context.Context, in *pb.CreateComplaintReq) (*pb.CreateComplaintResp, protocol.ServerError)
	GetGuildComplaintList(ctx context.Context, in *pb.GetGuildComplaintListReq) (*pb.GetGuildComplaintListResp, protocol.ServerError)
	SubmitGuildComplaint(ctx context.Context, in *pb.SubmitGuildComplaintReq) (*pb.SubmitGuildComplaintResp, protocol.ServerError)
	ArchiveUserComplaint(ctx context.Context, in *pb.ArchiveUserComplaintReq) (*pb.ArchiveUserComplaintResp, protocol.ServerError)
	GetUserComplaintByRecordId(ctx context.Context, in *pb.GetUserComplaintByRecordIdReq) (*pb.GetUserComplaintByRecordIdResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
