package darkserver

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/protocol/common/status"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/darkserver"
	"google.golang.org/grpc"
)

const (
	serviceName = "darkserver"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewDarkServerClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.DarkServerClient { return c.Stub().(pb.DarkServerClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

//天域的临时黑产查询接口
func (c *Client) GetTianYuDark(ctx context.Context, req pb.GetTianYuDarkReq) (*pb.GetTianYuDarkResp, protocol.ServerError) {
	resp, err := c.typedStub().GetTianYuDark(ctx, &req)
	return resp, protocol.ToServerError(err)
}

//天域的临时黑产设置接口
func (c *Client) UpdateTianYuDark(ctx context.Context, req pb.UpdateTianYuDarkReq) (*pb.UpdateTianYuDarkResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateTianYuDark(ctx, &req)
	return resp, protocol.ToServerError(err)
}

//黑产查询接口
func (c *Client) BlackUserCheck(ctx context.Context, req pb.GetBlackUserReq) (*pb.GetBlackUserResp, protocol.ServerError) {
	resp, err := c.typedStub().BlackUserCheck(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UserBehaviorCheck(ctx context.Context, uid uint32) (uint32, protocol.ServerError) {
	resp, err := c.typedStub().BlackUserCheck(ctx, &pb.GetBlackUserReq{Uid: uid})
	if err != nil {
		return 0, protocol.ToServerError(err)
	}

	if resp.GetBlackFlag() {
		return uid, nil
	}
	return 0, nil
}

func (c *Client) BatchBlackUserListCheck(ctx context.Context, req pb.BatchGetBlackUserListReq) (*pb.BatchGetBlackUserListResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchBlackUserListCheck(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchBlackUserListCheckMap(ctx context.Context, uids []uint32) (uid2Status map[uint32]bool, serr protocol.ServerError) {
	req := &pb.BatchGetBlackUserListReq{
		UidList: uids,
	}
	resp, err := c.typedStub().BatchBlackUserListCheck(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	uid2Status = make(map[uint32]bool, len(uids))
	for _, info := range resp.GetUidBehaviorList() {
		uid2Status[info.Uid] = info.Status
	}
	return
}

func (c *Client) UpdateBlackStatus(ctx context.Context, req pb.UpdateBlackStatusReq) (*pb.UpdateBlackStatusResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateBlackStatus(ctx, &req)
	return resp, protocol.ToServerError(err)
}

// 检查临时黑产
func (c *Client) CheckTempBlackUser(ctx context.Context, uid uint32) protocol.ServerError {
	resp, err := c.typedStub().GetTianYuDark(ctx, &pb.GetTianYuDarkReq{Uid: uid})
	if err != nil {
		return protocol.ToServerError(err)
	}

	if resp.GetIsDark() {
		leftMin := (resp.GetEndTime() - time.Now().Unix()) / 60
		if (resp.GetEndTime()-time.Now().Unix())%60 > 0 {
			leftMin++
		}
		return protocol.NewServerError(status.ErrAccountAbnormalFunctionInvalid, fmt.Sprintf("资源加载中，预计%dmin后完毕", leftMin))
	}

	return nil
}

func (c *Client) GetBlackSample(ctx context.Context, req *pb.GetBlackSampleReq) (*pb.GetBlackSampleResp, protocol.ServerError) {
	resp, err := c.typedStub().GetBlackSample(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetBlackSample(ctx context.Context, req *pb.SetBlackSampleReq) (*pb.SetBlackSampleResp, protocol.ServerError) {
	resp, err := c.typedStub().SetBlackSample(ctx, req)
	return resp, protocol.ToServerError(err)
}
