package usersettings

import (
	"context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func TestClient_GetMicrList(t *testing.T) {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
	client := NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("usersettings.52tt.local"))

	resp, err := client.GetSettings(context.Background(), 500001, []string{}, 1)
	if err != nil {
		t.Error(err)
	}
	t.Log("success:", resp)
}
