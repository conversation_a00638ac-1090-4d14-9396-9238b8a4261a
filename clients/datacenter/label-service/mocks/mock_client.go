// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/datacenter/label-service (interfaces: IClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	protocol "golang.52tt.com/pkg/protocol"
	com_quwan_common_grpc_apiserver "golang.52tt.com/protocol/datacenter/label-query-service"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// LabelSingleQuery mocks base method.
func (m *MockIClient) LabelSingleQuery(arg0 context.Context, arg1 *com_quwan_common_grpc_apiserver.SingleQueryRequest) (*com_quwan_common_grpc_apiserver.SingleQueryResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LabelSingleQuery", arg0, arg1)
	ret0, _ := ret[0].(*com_quwan_common_grpc_apiserver.SingleQueryResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// LabelSingleQuery indicates an expected call of LabelSingleQuery.
func (mr *MockIClientMockRecorder) LabelSingleQuery(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LabelSingleQuery", reflect.TypeOf((*MockIClient)(nil).LabelSingleQuery), arg0, arg1)
}
