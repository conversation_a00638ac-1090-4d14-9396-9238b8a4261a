package device_info_service

import (
	"context"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/datacenter/device-info-service"
	"google.golang.org/grpc"
)

const (
	serviceName = "datacenter-device-service"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewDeviceInfoServiceClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.DeviceInfoServiceClient { return c.Stub().(pb.DeviceInfoServiceClient) }

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetDeviceState(ctx context.Context, in *pb.DeviceInfoRequestData) (*pb.DeviceInfoResponse, protocol.ServerError) {
	resp, err := c.typedStub().GetDeviceState(ctx, in)
	return resp, protocol.ToServerError(err)
}
