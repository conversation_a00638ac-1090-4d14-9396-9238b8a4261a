package device_info_service

import (
	"context"
	"fmt"
	pb "golang.52tt.com/protocol/datacenter/device-info-service"
	"google.golang.org/grpc"
	"testing"
)

func TestNewClient(t *testing.T) {
	type args struct {
		dopts []grpc.DialOption
	}
	tests := []struct {
		name    string
		args    args
		want    *Client
		wantErr bool
	}{
		{
			name:    "TestNewClient",
			args:    args{},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, err := NewClient(tt.args.dopts...)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			ctx := context.Background()
			// 调用你的gRPC
			labelRsp, err := c.GetDeviceState(ctx, &pb.DeviceInfoRequestData{
				AppId:    "ttvoice",
				DeviceId: "2830975B01C14C02ACE7DD9FAFE926B8",
				Uid:      "2598165",
			})
			fmt.Printf("服务响应：%s, err:%v", labelRsp.Message, err)
			fmt.Printf("服务响应：%v, err:%v", labelRsp, err)

		})
	}
}
