# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

4001:
api-route-configurator --etcd-endpoints *************:2379 create --id 4001 --source api/channel_live_multi_pk/grpc_channel_live_multi_pk.proto --lang go --method /ga.api.channel_live_multi_pk.ChannelLiveMultiPkLogic/GetChannelLiveMultiPkPermission
4002:
api-route-configurator --etcd-endpoints *************:2379 create --id 4002 --source api/channel_live_multi_pk/grpc_channel_live_multi_pk.proto --lang go --method /ga.api.channel_live_multi_pk.ChannelLiveMultiPkLogic/SerarchPkAnchor
4003:
api-route-configurator --etcd-endpoints *************:2379 create --id 4003 --source api/channel_live_multi_pk/grpc_channel_live_multi_pk.proto --lang go --method /ga.api.channel_live_multi_pk.ChannelLiveMultiPkLogic/GetRecentPkAnchorList
4004:
api-route-configurator --etcd-endpoints *************:2379 create --id 4004 --source api/channel_live_multi_pk/grpc_channel_live_multi_pk.proto --lang go --method /ga.api.channel_live_multi_pk.ChannelLiveMultiPkLogic/ApplyChannelLiveMultiPk
4005:
api-route-configurator --etcd-endpoints *************:2379 create --id 4005 --source api/channel_live_multi_pk/grpc_channel_live_multi_pk.proto --lang go --method /ga.api.channel_live_multi_pk.ChannelLiveMultiPkLogic/MatchMultiPk
4006:
api-route-configurator --etcd-endpoints *************:2379 create --id 4006 --source api/channel_live_multi_pk/grpc_channel_live_multi_pk.proto --lang go --method /ga.api.channel_live_multi_pk.ChannelLiveMultiPkLogic/CancelMatchMultiPk
4007:
api-route-configurator --etcd-endpoints *************:2379 create --id 4007 --source api/channel_live_multi_pk/grpc_channel_live_multi_pk.proto --lang go --method /ga.api.channel_live_multi_pk.ChannelLiveMultiPkLogic/AcceptChannelLiveMultiPk
4008:
api-route-configurator --etcd-endpoints *************:2379 create --id 4008 --source api/channel_live_multi_pk/grpc_channel_live_multi_pk.proto --lang go --method /ga.api.channel_live_multi_pk.ChannelLiveMultiPkLogic/StartChannelLiveMultiPk
4009:
api-route-configurator --etcd-endpoints *************:2379 create --id 4009 --source api/channel_live_multi_pk/grpc_channel_live_multi_pk.proto --lang go --method /ga.api.channel_live_multi_pk.ChannelLiveMultiPkLogic/GetChannelLiveMultiPkRank
4010:
api-route-configurator --etcd-endpoints *************:2379 create --id 4010 --source api/channel_live_multi_pk/grpc_channel_live_multi_pk.proto --lang go --method /ga.api.channel_live_multi_pk.ChannelLiveMultiPkLogic/GetChannelLiveMultiPkKnightList
