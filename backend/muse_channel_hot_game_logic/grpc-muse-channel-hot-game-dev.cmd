# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

39250:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 39250 --source api/muse_channel_hot_game_logic/grpc_muse_channel_hot_game.proto --lang go --method /ga.api.muse_channel_hot_game_logic.MuseChannelHotGame/GetMuseChannelHotGame
39251:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 39251 --source api/muse_channel_hot_game_logic/grpc_muse_channel_hot_game.proto --lang go --method /ga.api.muse_channel_hot_game_logic.MuseChannelHotGame/GetMuseChannelHotGameFloat
39252:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 39252 --source api/muse_channel_hot_game_logic/grpc_muse_channel_hot_game.proto --lang go --method /ga.api.muse_channel_hot_game_logic.MuseChannelHotGame/SetMuseChannelHotGameJoinStatus
