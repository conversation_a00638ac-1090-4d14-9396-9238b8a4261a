apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-game-server-v2-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.game_server_v2.GameServerLogicV2/
    rewrite:
      uri: /logic.GameServerLogicV2/
    delegate:
       name: game-server-logic-v2-delegator-80
       namespace: quicksilver


