apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-official-live-channel-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - dev-apiv2.ttyuyin.com
  http:
  - match:
    - uri:
        prefix: /ga.api.official_live_channel.OfficialLiveChannelLogic/
    rewrite:
      uri: /logic.OfficialLiveChannelLogic/
    route:
    - destination:
        host: official-live-channel-logic.quicksilver.svc.cluster.local
        port:
          number: 80
