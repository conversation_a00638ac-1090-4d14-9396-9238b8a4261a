cmdInfoList:
  - source: api/present_go/grpc_present_go.proto
    cmd: 1174
    lang: go
    method: /ga.api.present_go.PresentGoLogic/GetNeedPopUpPresentList
  - source: api/present_go/grpc_present_go.proto
    cmd: 1190
    lang: go
    method: /ga.api.present_go.PresentGoLogic/GetUserActPresentArea
  - source: api/present_go/grpc_present_go.proto
    cmd: 1191
    lang: go
    method: /ga.api.present_go.PresentGoLogic/GetPresentExtraConfig
  - source: api/present_go/grpc_present_go.proto
    cmd: 1192
    lang: go
    method: /ga.api.present_go.PresentGoLogic/GetPresentEffectTimeDetail
  - source: api/present_go/grpc_present_go.proto
    cmd: 1193
    lang: go
    method: /ga.api.present_go.PresentGoLogic/UnpackPresentBox
  - source: api/present_go/grpc_present_go.proto
    cmd: 1194
    lang: go
    method: /ga.api.present_go.PresentGoLogic/CommonSendPresent
  - source: api/present_go/grpc_present_go.proto
    cmd: 1195
    lang: go
    method: /ga.api.present_go.PresentGoLogic/PresentConfigSync
  - source: api/present_go/grpc_present_go.proto
    cmd: 1196
    lang: go
    method: /ga.api.present_go.PresentGoLogic/EmperorSetSend
  - source: api/present_go/grpc_present_go.proto
    cmd: 1197
    lang: go
    method: /ga.api.present_go.PresentGoLogic/GetEmperorSetConfigById
  - source: api/present_go/grpc_present_go.proto
    cmd: 1198
    lang: go
    method: /ga.api.present_go.PresentGoLogic/UnpackEmperorBox
  - source: api/present_go/grpc_present_go.proto
    cmd: 1199
    lang: go
    method: /ga.api.present_go.PresentGoLogic/GetTimePresentList
  - source: api/present_go/grpc_present_go.proto
    cmd: 36200
    lang: go
    method: /ga.api.present_go.PresentGoLogic/GetCustomizedPresentList
  - source: api/present_go/grpc_present_go.proto
    cmd: 36201
    lang: go
    method: /ga.api.present_go.PresentGoLogic/GetCustomizedPresentDetail
  - source: api/present_go/grpc_present_go.proto
    cmd: 36202
    lang: go
    method: /ga.api.present_go.PresentGoLogic/ReportCustomOptionChoose
  - source: api/present_go/grpc_present_go.proto
    cmd: 39301
    lang: go
    method: /ga.api.present_go.PresentGoLogic/GetPresentSetInfo
  - source: api/present_go/grpc_present_go.proto
    cmd: 39302
    lang: go
    method: /ga.api.present_go.PresentGoLogic/GetPresentSetDetail
  - source: api/present_go/grpc_present_go.proto
    cmd: 39400
    lang: go
    method: /ga.api.present_go.PresentGoLogic/GetTimePresentOnShelf

