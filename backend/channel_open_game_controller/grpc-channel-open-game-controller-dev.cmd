# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

30111:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30111 --source api/channel_open_game_controller/grpc_channel_open_game_controller.proto --lang go --method /ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic/GetChannelGamePlayerOpenid
30112:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30112 --source api/channel_open_game_controller/grpc_channel_open_game_controller.proto --lang go --method /ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic/JoinChannelGame
30113:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30113 --source api/channel_open_game_controller/grpc_channel_open_game_controller.proto --lang go --method /ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic/QuitChannelGame
30114:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30114 --source api/channel_open_game_controller/grpc_channel_open_game_controller.proto --lang go --method /ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic/ReadyChannelGame
30115:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30115 --source api/channel_open_game_controller/grpc_channel_open_game_controller.proto --lang go --method /ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic/UnReadyChannelGame
30116:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30116 --source api/channel_open_game_controller/grpc_channel_open_game_controller.proto --lang go --method /ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic/GetChannelGameStatusInfo
30117:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30117 --source api/channel_open_game_controller/grpc_channel_open_game_controller.proto --lang go --method /ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic/ExitChannelGame
30118:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30118 --source api/channel_open_game_controller/grpc_channel_open_game_controller.proto --lang go --method /ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic/StartChannelGame
30119:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30119 --source api/channel_open_game_controller/grpc_channel_open_game_controller.proto --lang go --method /ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic/SetChannelGameModeInfo
30120:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30120 --source api/channel_open_game_controller/grpc_channel_open_game_controller.proto --lang go --method /ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic/SetChannelGamePlayerLoading
30121:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30121 --source api/channel_open_game_controller/grpc_channel_open_game_controller.proto --lang go --method /ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic/BatchGetUidByOpenid
50861:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50861 --source api/channel_open_game_controller/grpc_channel_open_game_controller.proto --lang go --method /ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic/CheckUserStartGamePermission
