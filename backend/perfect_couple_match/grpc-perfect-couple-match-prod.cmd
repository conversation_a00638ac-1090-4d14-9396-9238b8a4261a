# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

36531: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36531 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/GetPrefectCpGameInfo
36532: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36532 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/SetPrefectCpGamePhase
36533: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36533 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/ApplyToHoldMic
36534: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36534 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/BlowLight
36535: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36535 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/ChooseTheOne
36536: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36536 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/GetCoupleClues
36537: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36537 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/GetMyQuestionnaire
36538: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36538 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/GetMyCluesProp
36539: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36539 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/UseCluesProp
36540: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36540 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/PublishClues
36551: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36551 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/GetPerfectMatchEntry
36552: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36552 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/EnrollPerfectMatch
36553: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36553 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/GetPerfectMatchQuestions
36554: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36554 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/SendPerfectMatchHeartbeat
36555: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36555 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/SendPerfectMatchAnswer
36556: api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36556 --source api/perfect_couple_match/grpc_perfect_couple_match.proto --lang go --method /ga.api.perfect_couple_match.PerfectCoupleMatchLogic/CancelPerfectMatch
