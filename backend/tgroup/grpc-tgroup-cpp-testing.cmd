# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

370:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 370 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupCreatePermission
371:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 371 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/SearchTGroup
372:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 372 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/HotGameList
373:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 373 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/CreateTGroup
374:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 374 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupRecommended
375:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 375 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/JoinTGroup
376:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 376 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/QuitTGroup
377:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 377 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupAddMember
378:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 378 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupRemoveMember
379:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 379 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupModifyName
380:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 380 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupGetMemberList
381:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 381 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupMuteMember
382:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 382 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupApprove
383:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 383 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupGetMuteList
384:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 384 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupGetDetailInfo
385:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 385 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/SearchTGroupByGameId
386:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 386 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupDismiss
387:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 387 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupModifyMyCard
388:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 388 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupModifyGroupDesc
389:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 389 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupSearchCircleGame
390:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 390 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupSetNeedVerify
391:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 391 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupAddAdmin
392:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 392 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupRemoveAdmin
393:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 393 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupUnmuteMember
394:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 394 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupSetAllMute
396:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 396 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupInviteMember
397:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 397 --source api/tgroup/grpc_tgroup_cpp.proto --lang cpp --method /ga.api.tgroup.TGroupLogic/TGroupAcceptJoin
