# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

3565:
api-route-configurator --etcd-endpoints *************:2379 create --id 3565 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetAnchorValidPlateList
3566:
api-route-configurator --etcd-endpoints *************:2379 create --id 3566 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/WearAnchorPlate
3567:
api-route-configurator --etcd-endpoints *************:2379 create --id 3567 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/AcceptAppointPk
3568:
api-route-configurator --etcd-endpoints *************:2379 create --id 3568 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/ConfirmAppointPkPush
3569:
api-route-configurator --etcd-endpoints *************:2379 create --id 3569 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetAppointPkInfo
3570:
api-route-configurator --etcd-endpoints *************:2379 create --id 3570 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetApplyList
3571:
api-route-configurator --etcd-endpoints *************:2379 create --id 3571 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetPkInfo
3572:
api-route-configurator --etcd-endpoints *************:2379 create --id 3572 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetMyToolList
3573:
api-route-configurator --etcd-endpoints *************:2379 create --id 3573 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetItemConfig
3574:
api-route-configurator --etcd-endpoints *************:2379 create --id 3574 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/SetChannelLiveOpponentMicFlag
3575:
api-route-configurator --etcd-endpoints *************:2379 create --id 3575 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/StartPkMatch
3576:
api-route-configurator --etcd-endpoints *************:2379 create --id 3576 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/CancelPkMatch
3577:
api-route-configurator --etcd-endpoints *************:2379 create --id 3577 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetPKMatchInfo
3580:
api-route-configurator --etcd-endpoints *************:2379 create --id 3580 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetLiveChannelInfo
3581:
api-route-configurator --etcd-endpoints *************:2379 create --id 3581 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/ChannelLiveHeartbeat
3582:
api-route-configurator --etcd-endpoints *************:2379 create --id 3582 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/SetChannelLiveStatus
3583:
api-route-configurator --etcd-endpoints *************:2379 create --id 3583 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetChannelLiveStatus
3584:
api-route-configurator --etcd-endpoints *************:2379 create --id 3584 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/ApplyPk
3585:
api-route-configurator --etcd-endpoints *************:2379 create --id 3585 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/HandlerApply
3586:
api-route-configurator --etcd-endpoints *************:2379 create --id 3586 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/BatchGetChannelLiveStatusByAccount
3587:
api-route-configurator --etcd-endpoints *************:2379 create --id 3587 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/SetPkStatus
3588:
api-route-configurator --etcd-endpoints *************:2379 create --id 3588 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/CancelPKApply
3589:
api-route-configurator --etcd-endpoints *************:2379 create --id 3589 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetChannelLivePKRecord
3590:
api-route-configurator --etcd-endpoints *************:2379 create --id 3590 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetChannelLivePkRankUser
3591:
api-route-configurator --etcd-endpoints *************:2379 create --id 3591 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetChannelLiveRankUser
3592:
api-route-configurator --etcd-endpoints *************:2379 create --id 3592 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetChannelLiveWatchTimeRankUser
3593:
api-route-configurator --etcd-endpoints *************:2379 create --id 3593 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetChannelLiveData
3594:
api-route-configurator --etcd-endpoints *************:2379 create --id 3594 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetFansRankList
3595:
api-route-configurator --etcd-endpoints *************:2379 create --id 3595 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetFansInfo
3596:
api-route-configurator --etcd-endpoints *************:2379 create --id 3596 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetAnchorFansInfo
3598:
api-route-configurator --etcd-endpoints *************:2379 create --id 3598 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/SearchAnchor
3599:
api-route-configurator --etcd-endpoints *************:2379 create --id 3599 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/ReportClientIDChange
3600:
api-route-configurator --etcd-endpoints *************:2379 create --id 3600 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetUserMissionList
3601:
api-route-configurator --etcd-endpoints *************:2379 create --id 3601 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetFansMissionList
3602:
api-route-configurator --etcd-endpoints *************:2379 create --id 3602 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetActorMissionList
3603:
api-route-configurator --etcd-endpoints *************:2379 create --id 3603 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/HandleUserMissionAtInterval
3604:
api-route-configurator --etcd-endpoints *************:2379 create --id 3604 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/HandleShareLiveChannelMission
3605:
api-route-configurator --etcd-endpoints *************:2379 create --id 3605 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetProcessActorMissionDesc
3606:
api-route-configurator --etcd-endpoints *************:2379 create --id 3606 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/HandleFansMissionAtInterval
3610:
api-route-configurator --etcd-endpoints *************:2379 create --id 3610 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetAnchorHonorNameplate
3611:
api-route-configurator --etcd-endpoints *************:2379 create --id 3611 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetRankingList
3630:
api-route-configurator --etcd-endpoints *************:2379 create --id 3630 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetFansAddedGroupList
3631:
api-route-configurator --etcd-endpoints *************:2379 create --id 3631 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/SetFansGroupName
3632:
api-route-configurator --etcd-endpoints *************:2379 create --id 3632 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/CheckSetGroupNamePermit
3633:
api-route-configurator --etcd-endpoints *************:2379 create --id 3633 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/CheckUserIsFans
3634:
api-route-configurator --etcd-endpoints *************:2379 create --id 3634 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/ChannelLiveReport
3635:
api-route-configurator --etcd-endpoints *************:2379 create --id 3635 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/LeaveFansGroup
3636:
api-route-configurator --etcd-endpoints *************:2379 create --id 3636 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetVirtualLiveChannelSecret
3637:
api-route-configurator --etcd-endpoints *************:2379 create --id 3637 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetUserFansGiftPri
4001:
api-route-configurator --etcd-endpoints *************:2379 create --id 4001 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkPermission
4002:
api-route-configurator --etcd-endpoints *************:2379 create --id 4002 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/SerarchPkAnchor
4003:
api-route-configurator --etcd-endpoints *************:2379 create --id 4003 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/ApplyChannelLiveMultiPk
4004:
api-route-configurator --etcd-endpoints *************:2379 create --id 4004 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/MatchMultiPk
4005:
api-route-configurator --etcd-endpoints *************:2379 create --id 4005 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/CancelMatchMultiPk
4006:
api-route-configurator --etcd-endpoints *************:2379 create --id 4006 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/AcceptChannelLiveMultiPk
4007:
api-route-configurator --etcd-endpoints *************:2379 create --id 4007 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/StartChannelLiveMultiPk
4008:
api-route-configurator --etcd-endpoints *************:2379 create --id 4008 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkRank
4009:
api-route-configurator --etcd-endpoints *************:2379 create --id 4009 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkKnightList
4011:
api-route-configurator --etcd-endpoints *************:2379 create --id 4011 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkRecordList
4012:
api-route-configurator --etcd-endpoints *************:2379 create --id 4012 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/CancelChannelLiveMultiPkTeam
4013:
api-route-configurator --etcd-endpoints *************:2379 create --id 4013 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/StopChannelLiveMultiPk
4014:
api-route-configurator --etcd-endpoints *************:2379 create --id 4014 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/DisinviteChannelLiveMultiPk
4015:
api-route-configurator --etcd-endpoints *************:2379 create --id 4015 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkTeamInfo
4016:
api-route-configurator --etcd-endpoints *************:2379 create --id 4016 --source api/channel_live/grpc_channel_live.proto --lang go --method /ga.api.channel_live.ChannelLiveLogic/InitChannelLiveMultiPkTeam
