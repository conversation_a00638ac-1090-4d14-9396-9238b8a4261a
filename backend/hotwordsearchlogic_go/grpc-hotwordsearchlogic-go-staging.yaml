apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-hotwordsearchlogic-go-logic-staging
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.hotwordsearchlogic_go.HotwordsearchlogicGo/
    rewrite:
      uri: /logic.HotwordsearchlogicGo/
    delegate:
       name: hotwordsearchlogic-go-delegator-80
       namespace: quicksilver


