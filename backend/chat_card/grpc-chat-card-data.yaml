cmdInfoList:
  - source: configurator
    cmd: 30500
    lang: go
    method: /ga.api.chat_card.ChatCardLogic/GetChatCardConfig
  - source: configurator
    cmd: 30501
    lang: go
    method: /ga.api.chat_card.ChatCardLogic/GetChatCardStatus
  - source: configurator
    cmd: 30502
    lang: go
    method: /ga.api.chat_card.ChatCardLogic/OpenChatCard
  - source: configurator
    cmd: 30503
    lang: go
    method: /ga.api.chat_card.ChatCardLogic/CloseChatCard
  - source: configurator
    cmd: 30504
    lang: go
    method: /ga.api.chat_card.ChatCardLogic/GetChatCardList
  - source: configurator
    cmd: 30505
    lang: go
    method: /ga.api.chat_card.ChatCardLogic/SayHi
  - source: configurator
    cmd: 30506
    lang: go
    method: /ga.api.chat_card.ChatCardLogic/Reply
  - source: configurator
    cmd: 30507
    lang: go
    method: /ga.api.chat_card.ChatCardLogic/LikeChatCard
  - source: configurator
    cmd: 30508
    lang: go
    method: /ga.api.chat_card.ChatCardLogic/MarkChatCardMsgRead
  - source: configurator
    cmd: 30509
    lang: go
    method: /ga.api.chat_card.ChatCardLogic/GetChatCardMsgUnreadCount
  - source: configurator
    cmd: 30510
    lang: go
    method: /ga.api.chat_card.ChatCardLogic/GetChatCardMsgList
  - source: configurator
    cmd: 30511
    lang: go
    method: /ga.api.chat_card.ChatCardLogic/ReportInterestedChatCardTags
  - source: configurator
    cmd: 30512
    lang: go
    method: /ga.api.chat_card.ChatCardLogic/SayHiV2

