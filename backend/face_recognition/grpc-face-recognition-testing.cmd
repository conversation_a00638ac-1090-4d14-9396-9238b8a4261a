# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

50500:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50500 --source api/face_recognition/grpc_face_recognition.proto --lang  --method /ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionProvider
50501:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50501 --source api/face_recognition/grpc_face_recognition.proto --lang  --method /ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionCertifyId
50502:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50502 --source api/face_recognition/grpc_face_recognition.proto --lang  --method /ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionResult
50503:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50503 --source api/face_recognition/grpc_face_recognition.proto --lang  --method /ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionProviderNoAuth
50504:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50504 --source api/face_recognition/grpc_face_recognition.proto --lang  --method /ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionCertifyIdNoAuth
50505:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50505 --source api/face_recognition/grpc_face_recognition.proto --lang  --method /ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionResultNoAuth
