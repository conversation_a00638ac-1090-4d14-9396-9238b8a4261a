syntax = "proto3";

package ga.grpc_transport_cfg;

import "ga_base.proto";
import "grpc_transport_cfg/transport_v2.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/grpc-transport-cfg";
  
message RefreshTransportConfigRequest {
    ga.BaseReq base_req = 1;
    string previous_check_sum = 2;
}


message RefreshTransportConfigResponse {
    ga.BaseResp base_resp = 1;
    ga.grpc_transport_cfg.TransportConfigV2 transport_config = 2;
    string new_check_sum = 3;
}
