//这里不用proto3会因为proto2的proto引用这个proto时，如果是proto3版本，会让服务端编译不了
syntax = "proto2";

package ga.grpc_transport_cfg;

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/grpc-transport-cfg";

message EndpointV2 {
    message TLSConfig {
        enum TLSMode {
          TLS_MODE_DISABLE = 0;
          TLS_MODE_SIMPLE = 1;
          TLS_MODE_MUTUAL = 2;
        }
        optional TLSMode mode = 1;
        optional string authority = 2;
    }
    // address like: dev-apv2.ttyuyin.com:443
    optional string address = 1; 
    optional TLSConfig tls_config = 2;
}

//大背景 全面grpc通信
message TransportConfigV2 {
    // endpoints 有则采用endpoint，没有则让客户端hard code使用现有域名：apiv2.ttyuyin.com
    repeated EndpointV2 endpoints = 1;
    // expire_at字段和min_app_version字段好像没有用了，已经是全面grpc通信
    optional ApiInfo black_list_api_info = 2;
    optional GzipInfo gzip_black_list_api_info = 3; //gzip黑名单优先
    optional GzipInfo gzip_white_list_api_info = 4; //gzip白名单
}

//oneof类型再服务端这边因为proto插件的原因，导致编译不了，所以只能寻找其他的方式来实现
message StringMatch {
    enum MatchType {
        MATCH_TYPE_UNSPECIFIED = 0;
        MATCH_TYPE_EXACT = 1;
        MATCH_TYPE_PREFIX = 2;
        MATCH_TYPE_ALL = 3;
    }
    optional MatchType match_type = 1;
    optional string match_value = 2;
}

message ApiInfo {
    repeated StringMatch api_rule = 1;
}


message GzipInfo {
    repeated StringMatch api_rule = 1;
}
