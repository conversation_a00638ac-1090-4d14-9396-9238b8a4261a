syntax = "proto3";

package ga.game_report_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/game-report-logic";

// 反馈场景
enum FeedbackSource {
  FEEDBACK_SOURCE_UNSPECIFIED = 0;
  FEEDBACK_SOURCE_GAME_ZONE = 1;        // 从开黑专区反馈，客户端传game_zone_game_tab_page
  FEEDBACK_SOURCE_HOME_PAGE = 2;        // 从首页更多反馈，客户端传ganpup_channel_filter_page
  FEEDBACK_SOURCE_CREATE_CHANNEL = 3;   // 从创建房间反馈，客户端传create_room_page
  FEEDBACK_SOURCE_SWITCH_PLAY = 4;      // 从切换玩法反馈，客户端传switch_room_type_page
  FEEDBACK_SOURCE_GAME_CARD = 5;        // 从游戏卡反馈，客户端传create_interest_card
}

message UserFeedbackNewTabReq {
  ga.BaseReq base_req = 1;
  string content = 2;   // 反馈的玩法名称
  string source = 3;    // 反馈场景，详见FeedbackSource
}

message UserFeedbackNewTabResp {
  ga.BaseResp base_resp = 1;
}

// 新用户事件
enum NewUserEvent {
  NEW_USER_EVENT_UNSPECIFIED = 0;
  // 访问首页
  NEW_USER_EVENT_VISIT_HOME_PAGE = 1;
}

message ReportNewUserEventReq {
  ga.BaseReq base_req = 1;

  // 上报事件 see NewUserEvent
  uint32 event = 2;
}

message ReportNewUserEventResp {
  ga.BaseResp base_resp = 1;
}

//每日任务完成上报
enum GameReportDailyTaskType {
  GAME_REPORT_DAILY_TASK_TYPE_INVALID_UNSPECIFIED = 0; // 无效
  GAME_REPORT_DAILY_TASK_TYPE_ENTER_GAME_ZONE_TASK = 1; //每日第一次进入游戏专区时上报
  GAME_REPORT_DAILY_TASK_TYPE_CONFIG_TAB_VIEW_DATA = 2; // 用户在xxxx主题xxxxtab的停留的时长 和 对应元素的数量
}

//活动任务上报
message GameReportDailyTaskReq{
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  uint32 task_type = 3;  // 1-每日第一次进入游戏专区时上报; 2-用户在xxxx主题xxxxtab的停留的时长 和 对应元素的数量, see GameReportDailyTaskType
  uint32 tab_id = 4; // 主题玩法id
  string config_tab_id = 5; // xxxtab动态tab id
  uint32 stay_duration = 6; // 停留时长, 单位：秒
  uint32 view_type = 7; // 浏览数据类型， 1-开黑房，2-动态帖子， 3-搭子卡片，4-群聊
  uint32 view_count = 8; // 浏览元素数量
}

message GameReportDailyTaskResp{
  ga.BaseResp base_resp = 1;
}

message ReportGameActivityEventsRequest {
  message Event {
    // 搭子卡事件 
    message GamePalCardEvent {
      enum Action {
        ACTION_UNSPECIFIED = 0;
        // 曝光
        ACTION_EXPOSED = 1;
        // 点击下一个
        ACTION_SWITCHED = 2;
        // 打招呼
        ACTION_GREETED = 3;
      }

      // see enum Action
      uint32 action = 1;
      // 玩法ID
      uint32 tab_id = 2;
      // 搭子卡ID
      string card_id = 3;
    }

    oneof event {
      GamePalCardEvent game_pal_card_event = 1;
    }
  }

  ga.BaseReq base_req = 1;
  // 事件列表，可聚合上报
  repeated Event events = 2;
}

message ReportGameActivityEventsResponse {
  ga.BaseResp base_resp = 1;
}
