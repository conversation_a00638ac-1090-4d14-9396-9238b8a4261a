syntax = "proto3";

package ga.revenue_audio_stream_logic;

import "ga_base.proto";
option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/revenue_audio_stream_logic";

/* 由于该服务开发时期协议分支合并策略调整，不允许修改已合入master分支的字段，出现部分废弃的字段及协议，请注意注释选用 */

// 上报语音流 (开启依托语音流类型玩法, 上麦时上报)
message ReportAudioStreamReq {
  ga.BaseReq base_req = 1;
  AudioStreamInfo audio_stream_info = 2;
}

message ReportAudioStreamResp {
  ga.BaseResp base_resp = 1;
}

// 进房获取语音流信息
message GetAudioStreamInfoReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;  // 本房间id
}

message GetAudioStreamInfoResp {
  ga.BaseResp base_resp = 1;
  PKAudioStreamInfo pk_audio_stream_info = 2; // PK语音流信息
  BlankingStreamInfo blanking_stream_info = 3;    // 当前房对其他房间的闭麦数据
  uint64 server_time = 4; // // 服务器时间， 用于区别新旧消息
}


// 设置麦位状态, 开闭麦
message SetBlankingStreamStatusReq {
  ga.BaseReq base_req = 1;
  uint32 my_channel_id = 7;    // 我方房间id
  uint32 target_channel_id = 8; // 被闭麦的房间id
  uint32 target_mic_id = 9;    // 被闭麦的麦位id, 全房间闭麦填0
  uint32 status = 6;   // 开/闭麦 见 BlankingStreamStatus

  // ============== 废弃 =================
  string pk_id = 2; // pk id
  uint32 pk_type = 3; // pk类型, 见 PKType
  uint32 channel_id = 4; // 房间id
  uint32 mic_id = 5;    // 麦位id
}

message SetBlankingStreamStatusResp {
  ga.BaseResp base_resp = 1;
}

message AudioStreamInfo {
  uint32 channel_id = 1;   // 房间id
  uint32 mic_id = 2;       // 麦位id
  string stream_id = 3;    // 语音流id
  ga.UserProfile user_info = 5; // 用户信息
  uint32 uid = 6; // 上报时候填
  string video_stream_id = 7; // 视频流id

  // ============= 废弃 ============
  uint32 user_profile = 4; // 用户信息, 上报时只填uid即可
}

message ChannelAudioStreamInfo {
  uint32 channel_id = 1;
  repeated AudioStreamInfo audio_stream_info = 2;
}

// PK业务类型
enum PKType {
  PK_TYPE_UNSPECIFIED = 0;
  PK_TYPE_DANMU = 1;
  PK_TYPE_CHANNEL_LIVE_MULTI_PK = 2;
}

enum PKStatus {
  PK_STATUS_UNSPECIFIED = 0;
  PK_STATUS_START = 1; // 进行中
  PK_STATUS_END = 2; // 结束
}

message PKAudioStreamInfo {
  string pk_id = 1; //  跟具体业务玩法数据携带的pk_id对应
  uint32 pk_type = 2; // PK业务类型, 见 PKType
  uint32 pk_status = 3;  // PK状态
  repeated ChannelAudioStreamInfo channel_audio_stream_info = 5; // 语音流信息


  repeated ChannelAudioStreamInfo pk_audio_stream_info = 4; // 语音流信息, 废弃!!!
}

// PK语音流信息推送
message PKAudioStreamInfoMsgOpt {
    PKAudioStreamInfo pk_audio_stream_info = 1;
    uint64 server_time = 2; // 服务器时间， 用于区别新旧消息
}

// 闭麦类型
enum BlankingStreamStatus {
  BLANKING_STREAM_STATUS_UNSPECIFIED = 0;
  BLANKING_STREAM_STATUS_OPEN = 1; //开麦，能听到对面声音
  BLANKING_STREAM_STATUS_CLOSE = 2; //闭麦，不能听到对面声音
}

message BlankingStreamInfo {
  message ChannelBlankingInfo {
    uint32 channel_id = 1;
    uint32 status = 3; // 开/闭麦 见 BlankingStreamStatus

    repeated BlankingItem blanking_item = 2; // 废弃!!!
  }

  string pk_id = 1; // 跟具体业务玩法数据携带的pk_id对应
  uint32 pk_type = 2; // PK业务类型, 见 PKType
  repeated ChannelBlankingInfo channel_blanking_info = 3;

  // =============== 废弃 =============
  message BlankingItem {
    uint32 mic_id = 1;
    uint32 status = 2; // 开/闭麦 见 BlankingStreamStatus
  }
}

// 闭麦信息推送
message PKBlankingStreamInfoMsgOpt {
  BlankingStreamInfo blanking_stream_info = 1;
  uint64 server_time = 2; // 服务器时间， 用于区别新旧消息
}


// ======================== 废弃 ========================
message SetAudioChatStreamStatusResp {
  ga.BaseResp base_resp = 1;
}


// 闭麦信息推送
message AudioChatBlankingMsgOpt {
  BlankingStreamInfo blanking_stream_info = 1;
}

// 进房获取语音流信息
message GetAudioChatInfoReq {
  ga.BaseReq base_req = 1;
  string pk_id = 2;
  uint32 pk_type = 3;     // pk类型, 见 PKType
  uint32 channel_id = 4;  // 本房间id
}

message GetAudioChatInfoResp {
  ga.BaseResp base_resp = 1;
  PKAudioStreamInfo pk_audio_stream_info = 2; // PK语音流信息
  BlankingStreamInfo blanking_stream_info = 3;    // 本房间闭麦信息
  uint64 server_time = 4; // // 服务器时间， 用于区别新旧消息
}

