#!/usr/bin/python
# -*- coding: utf-8 -*-

import descriptor, os, printer

class CodeGenerator(descriptor.CodeGeneratorInterface):
    def __init__(self, outdir):
        self.outdir = outdir

    def generate(self, fd):
          # generate status_code.pb.h
        status_code_h = os.path.join(self.outdir, "ErrDef.java")# % os.path.splitext(fd.source)[0])
        with open(status_code_h, "w+") as f:
            p = printer.Printer(f, 4)
            GENERATED_FILE_HEADER = """// Generated by gen_error tool (java plugin).
// If you make any local change, they will be lost.
// source: %s

/**
    ErrCode == 0, Success.
    ErrCode > 0, Logic Error, Client must handle it.
    ErrCode < 0, Error.
**/

package com.yiyou.ga.net.protocol;""" % (fd.source)
            p.P(GENERATED_FILE_HEADER)
            p.P("")
            p.P("public class ErrDef {")
            p.In()
            for d in fd.descriptors:
                if isinstance(d, descriptor.DocumentDescriptor):
                    p.P("")
                    p.P(d.doc)
                elif isinstance(d, descriptor.StatusEnumDescriptor):
                    name = d.name
                    if d.value <> 0:
                        name = "ERR_" + d.name
                    if len(d.msg) > 0:
                        p.P("public static final int %s = %s; // %s" % (name, d.value, d.msg))
                    else:
                        p.P("public static final int %s = %s;" % (name, d.value))
            p.Out()
            p.P("} // ErrDef")
            p.P("")
