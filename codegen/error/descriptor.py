#!/usr/bin/python
# -*- coding: utf-8 -*-

class DocumentDescriptor(object):
    def __init__(self, doc):
        self.doc = doc

class StatusEnumDescriptor(object):
    def __init__(self, name, value, msg):
        self.name = name
        self.value = value
        self.msg = msg

class FileDescriptor(object):
    def __init__(self, source, descriptors):
        self.source = source
        self.descriptors = descriptors

from abc import ABCMeta, abstractmethod 

class CodeGeneratorInterface(object):
    __metaclass__ = ABCMeta
    
    @abstractmethod
    def generate(self, fd):
        pass