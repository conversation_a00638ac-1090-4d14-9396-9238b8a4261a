syntax = "proto3";

package rcmd.tt_adapter;
option go_package = "golang.52tt.com/protocol/services/rcmd/tt_adapter";

message ChannelDynamicMicEvent{
  uint32 event_type = 1;
  uint32 channel_type = 2;
}

message ChannelDynamicEnterEvent{
  uint32 event_type = 1;
  uint32 channel_type = 2;
  bytes opt_pb_info = 3;
}

message ChannelDynamicStatEvent{
  enum EventType {
    INVALID = 0;
    Enter = 1;
    Mic = 2;
  }
  EventType event_type = 1;
  uint32 channel_id = 2;
  uint32 uid = 3;  // 事件触发者
  uint32 sex = 4; // uid性别
  uint32 age = 5; // uid年龄
  uint32 room_uid = 6;
  ChannelDynamicEnterEvent enter_event = 7;
  ChannelDynamicMicEvent mic_event = 8;
  int64 send_time = 9;
  uint32 like_cnt = 10;
  uint32 dislike_cnt = 11;
}



// ----------------小游戏--------------//
// 小游戏玩家信息同步
message GameUserInfoEvent {
  uint32 tab_id = 1; // 游戏的标签ID
  string open_id = 2; // 游戏opneid
  string nick_name = 3;// 用户昵称
  string game_user_id = 4; // 游戏用户ID
  string platform_uid = 5; // 平台UID
  string platform_account = 6; // 平台外显示ID
  string sex = 7; // 性别
  uint32 game_type = 8; // 游戏类型 男/女/未知
  string game_platform = 9; // 游戏平台
  string game_name = 10; //游戏名字
  uint32 game_id = 11; // 游戏ID
  string platform_app_name = 12; // 游戏APP
  string business_field_json_str = 13;
}

message GameUserBusinessField {
  int32 Grade = 1;
}

// ----------------小游戏--------------//