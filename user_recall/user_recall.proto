syntax = "proto3";

package ga.user_recall;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/user_recall;user_recall";

// 检查是否是召回用户
message CheckIsRecallRequest {
  ga.BaseReq base_req = 1;
}
message CheckIsRecallResponse {
  ga.BaseResp base_resp = 1;
  bool is_recall = 2; // 是否是回归用户
  uint32 min_value = 3; //礼包最小值
  uint32 max_value = 4; //礼包最大值
  uint32 remain_time = 5; //剩余时间（秒）
  string recalled_web_url = 6;  //被邀请方，支持马甲包的页面
}

// 召回用户领奖
message UserRecallGetPrizeRequest {
  ga.BaseReq base_req = 1;
}
message UserRecallGetPrizeResponse {
  ga.BaseResp base_resp = 1;
  string present_icon = 2;  //礼物图标
  string present_name = 3;  //礼物名
  uint32 present_value = 4; //礼物价值
}
// 绑定邀请人的ttid web端实现
//message BindInviterRequest {
//  ga.BaseReq base_req = 1;
//  string ttid = 2;
//}
//message BindInviterResponse {
//  ga.BaseResp base_resp = 1;
//}

//message UserComeBackMsg {
//  uint32 prize_value = 1; //奖励的T豆价值
//}


enum RecallType {
    RECALL_TYPE_UNSPECIFIED =0; // 无效值
    RECALL_TYPE_SMS = 1; // 官方短信
	RECALL_TYPE_WECHAT = 2; //
    RECALL_TYPE_QQ = 3; //
}
enum UserRecallStatus {
    USER_RECALL_STATUS_UNSPECIFIED =0; // 无效值
    USER_RECALL_STATUS_WAIT = 1; // 可召回
	USER_RECALL_STATUS_CALLED = 2; // 已召回
}
enum RecallRewardStatus {
  RECALL_REWARD_STATUS_UNSPECIFIED =0; // 无效值
  RECALL_REWARD_STATUS_TASK=1; // 回归礼
  RECALL_REWARD_STATUS_WAITGET=2; // 奖励待领
}

// 获取可召回好友列表
message GetUserRecallListRequest {
    ga.BaseReq base_req = 1;
}
message GetUserRecallListResponse {
    ga.BaseResp base_resp = 1;
    uint32 reward_status=2;// 回归任务奖励状态 see RecallRewardStatus
    repeated UserRecallInfo recall_user_list=3; // 可召回好友列表
    uint32 gift_val=4;// 入口文案 xx豆
    string jump_url=5;//跳转url

    string invite_title=6;
    string invite_content=7;
    string invite_img_url=8;
}
message UserRecallInfo {
    ga.UserProfile user_info=1;
    uint32 status=2;// UserRecallStatus
    uint32 gift_val=3; // 礼物价值
    string gift_name=4; // 礼物名称
    string gift_icon_url=5; // 礼物图标

    string invite_token=6;
    string invite_url=7;
}


// 发起召回
message SendRecallRequest {
	ga.BaseReq base_req = 1;
    uint32 target_uid=2;
    uint32 recall_type=3;// RecallType
}
message SendRecallResponse {
	ga.BaseResp base_resp = 1;
}