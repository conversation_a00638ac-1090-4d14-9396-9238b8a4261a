syntax = "proto3";

package ga.mijing_rcmd_logic;

import "ga_base.proto";
import "ugc/ugc_.proto";
import "mijing_label_logic/mijing_label_logic.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/mijing-rcmd-logic";

//首页推荐本信息
message MijingScenarioHomePageInfo{
    uint32 scenario_id = 1;
    string title = 2;//剧本名称
    string labels_color = 3; //标签颜色
    repeated string labels = 4; //剧本标签
    string tv_picture = 5;//推荐本电视框图
    repeated string avatar_toast = 6;//一句话推荐语
    float machine_average = 7;  //机器平均分
    string home_page_video = 8;//首页展示的视频
    repeated string loop_play_picture = 9;//轮播图片
    float rcmd_score = 10;  //推荐的权重分
    uint32 hot_score = 11; //剧本的热度值
    string small_picture = 12;//显示的小图图片
    uint32 tab_id = 13;
    repeated string reason = 14;//推荐原因
    bool is_super_scenario = 15;//是否精品本
    bool is_rookie = 16;// 是否新手必玩
    repeated mijing_label_logic.ScenarioContentLabel scenario_content_label_list = 17;  // 剧本内容标签列表
}

//获取首页推荐本接口
message MijingGetRecommendedScenarioForHomePageRequest{
  ga.BaseReq base_req = 1;
}
message MijingGetRecommendedScenarioForHomePageResponse{
  ga.BaseResp base_resp = 1;
  repeated MijingScenarioHomePageInfo infos = 2;
  uint32 show_tv_count = 3; //展示电视框剧本的个数
  uint32 show_small_count = 4; //展示小图剧本的个数
  bool rcmd_new_user = 5;//主页推荐用户标识
}

message GetMijingScenarioCommentListRequest{
  ga.BaseReq base_req = 1;
  uint32 scenario_id = 2;
  uint32 count = 3; // 数量
  MijingRcmdLoadMore load_more = 4; // 首次拉取不传, 加载更多时原封不动地填入上一次Resp中的load_more字段
}

message GetMijingScenarioCommentListResponse{
  ga.BaseResp base_resp = 1;
  repeated ScenarioCommentInfo comments = 2;
  MijingRcmdLoadMore load_more = 3; // 下一次加载更多时, 将load_more原封不动地填入请求Req的load_more中; 如果不包含此字段, 表示已经拉完了
}

message MijingRcmdLoadMore{
  uint32 last_page = 1;
  uint32 last_count = 2;
}

message ScenarioCommentInfo {
  ga.ugc.PostInfo info = 1;
  uint32 score = 2; // 分数
  ShortChainInfo short_chain = 3;
}

message ShortChainInfo{
  uint32 id = 1; // 剧本id
  string icon = 2;
  string text = 3;
}

message GetMijingCommentListRequest{
  ga.BaseReq base_req = 1;
  uint32 count = 2; // 数量
  MijingRcmdLoadMore load_more = 3; // 首次拉取不传, 加载更多时原封不动地填入上一次Resp中的load_more字段
  string client_channel_id = 4; // 渠道号
}

message GetMijingCommentListResponse{
  ga.BaseResp base_resp = 1;
  repeated ScenarioCommentInfo comments = 2;
  MijingRcmdLoadMore load_more = 3; // 下一次加载更多时, 将load_more原封不动地填入请求Req的load_more中; 如果不包含此字段, 表示已经拉完了
}

//找搭子需求 -- start

//获取推荐搭子卡片id 列表
message GetMijingPartnerCardListRequest{
  ga.BaseReq base_req = 1;
}

message GetMijingPartnerCardListResponse{
  ga.BaseResp base_resp = 1;
  repeated string card_id_list = 2; //搭子卡片id list
}

// 搭子标签颜色
enum LabelColourType {
  LABEL_COLOUR_TYPE_UNSPECIFIED = 0;
  LABEL_COLOUR_TYPE_NOCOLOR = 1;  // 无颜色
  LABEL_COLOUR_TYPE_PINK = 2;  // 粉色
  LABEL_COLOUR_TYPE_GREEN = 3;  // 绿颜色
  LABEL_COLOUR_TYPE_PURPLE = 4;  // 紫色
  LABEL_COLOUR_TYPE_BLUE = 5;  // 蓝色
}

message LabelNameInfo{
    string name = 1;  //标签名字
    string img_url = 2;//标签图片路径
    LabelColourType colour_type = 3;  // 颜色
}

// 搭子用户在线状态
enum PartnerOnlineType {
  PARTNER_ONLINE_TYPE_UNSPECIFIED = 0;
  PARTNER_ONLINE_TYPE_ONLINE = 1;  // 在线
  PARTNER_ONLINE_TYPE_OFFLINE = 2;  // 离线
}

//搭子用户信息
message PartnerUserInfo{
    uint32 uid = 1;  // 用户 UID
    PartnerOnlineType online_type = 2;  // 在线状态
    string user_nickname = 3;  // 用户昵称
    string user_account = 4;  // 用户 Account，e.x: tt123456
    uint32 gender = 5; // 性别
    repeated uint32 taillight_medal_list = 6; // 用户佩戴的勋章列表，最多只能戴1个，空则表示没佩戴
}

//搭子卡片信息
message MijingPartnerCardInfo{  
    string card_id = 1; //搭子卡片id
    PartnerUserInfo user_info = 2;//卡片用户信息
    uint32 passed_chapters_count = 3;//通关的章节数
    repeated uint32 play_mode = 4;//寻找的玩本方式 1-语音交流，2-文字交流
    repeated LabelNameInfo label_info_list = 5; // 自选标签信息
    repeated LabelNameInfo partner_info_list = 6; //搭子的信息
    string custom_text = 7;// 自定义文案
    repeated uint32 play_scenario_id_list = 8; // 最近玩本记录
    uint32 exposure_count = 9;//曝光次数
    uint32 accost_count = 10;//被搭次数
    uint32 play_count = 11;//剩余滑动次数
    string avatar_img_url = 12;// 用户虚拟形象
}

// 根据卡片ID获取搭子卡片详情 
message BatchGetMijingPartnerCardInfoByCardIdRequest{
  ga.BaseReq base_req = 1;
  repeated string card_id_list = 2; //搭子卡片id list
}

message BatchGetMijingPartnerCardInfoByCardIdResponse{
  ga.BaseResp base_resp = 1;
  repeated MijingPartnerCardInfo card_info_list = 2; //搭子卡片详情列表
}

// 根据uid获取搭子卡片详情 
message BatchGetMijingPartnerCardInfoByUidRequest{
  ga.BaseReq base_req = 1;
  repeated uint32 uids = 2;
}

message BatchGetMijingPartnerCardInfoByUidResponse{
  ga.BaseResp base_resp = 1;
  repeated MijingPartnerCardInfo card_info_list = 2; //搭子卡片详情列表
}

//编辑更新或者新增搭子卡片信息
message UpdateMijingPartnerCardInfoRequest{
  ga.BaseReq base_req = 1;
  MijingPartnerCardInfo card_info_list = 2; //搭子卡片
}

message UpdateMijingPartnerCardInfoResponse{
  ga.BaseResp base_resp = 1;
}

// 用户行为
enum CardActionType {
  CARD_ACTION_TYPE_UNSPECIFIED = 0;
  CARD_ACTION_TYPE_LOOK = 1;  // 查看别人搭子卡片
  CARD_ACTION_TYPE_ENTER = 2;  // 进入找搭子功能
  CARD_ACTION_TYPE_LEAVE = 3;  // 离开找搭子功能
  CARD_ACTION_TYPE_PASS = 4;  // 左滑别人搭子卡片（不匹配）
}

//搭子行为
message MijingExposurePartnerCardRequest{
  ga.BaseReq base_req = 1;
  CardActionType  action_type = 2;//行为代码
  string card_id = 3; //搭子卡片id：传入自己的卡片id
  string target_card_id = 4; //搭子卡片id：目标行为的卡片id
}

message MijingExposurePartnerCardResponse{
  ga.BaseResp base_resp = 1;
}

//擦亮搭子卡片
message MijingIncrExposurePartnerCardRequest{
  ga.BaseReq base_req = 1;
  string card_id = 2; //搭子卡片id
}

message MijingIncrExposurePartnerCardResponse{
  ga.BaseResp base_resp = 1;
}

//搭一下卡片
message MijingAccostPartnerCardRequest{
  ga.BaseReq base_req = 1;
  repeated string card_ids = 2; //搭子卡片id
}

message MijingAccostPartnerCardResponse{
  ga.BaseResp base_resp = 1;
}

// 匹配成功通知 - 推送
message AccostPartnerCardPush {
  string from_card_id = 1;  // 发送者搭子卡片id
  uint32 from_uid = 2;// 发送者的uid
  PartnerOnlineType online_type = 3; // 发送者的在线状态
  uint32 channel_id = 4; // 发送者的房间ID（在房时有）
  string from_user_nickname = 5;  // 用户昵称
  string from_user_account = 6;  // 用户 Account，e.x: tt123456
  uint32 gender = 7; // 性别
}

//找搭子需求 -- end

//根据剧本id获取gameid ---start
message MijingScenarioGameInfo{  
  uint32 scenario_id = 1;//剧本id
  uint32 game_id = 2;//对应的游戏id
}

message GetMijingScenarioGameIdListRequest{
  ga.BaseReq base_req = 1;
  repeated uint32 scenario_id_list = 2;//剧本id
}

message GetMijingScenarioGameIdListResponse{
  ga.BaseResp base_resp = 1;
  repeated MijingScenarioGameInfo info_list = 2; //剧本id对应的游戏id
}
// 根据剧本id获取gameid --end

//搭子虚拟形象需求 ---start
enum PromptGenType {
  PROMPT_GEN_TYPE_UNSPECIFIED = 0;
  PROMPT_GEN_TYPE_INIT = 1;  // 首次生成
  PROMPT_GEN_TYPE_REPEAT = 2;  // 重复生成
}

// 形象照片生成状态
enum PromptGenAvatarStatus {
  PROMPT_GEN_AVATAR_STATUS_UNSPECIFIED = 0;
  PROMPT_GEN_AVATAR_STATUS_INIT = 1;  // 初始化
  PROMPT_GEN_AVATAR_STATUS_CHECKOK = 2;  //上传头像检测通过
  PROMPT_GEN_AVATAR_STATUS_DOING = 3;  // 正在生成中
  PROMPT_GEN_AVATAR_STATUS_CANCEL = 4;  // 生成操作用户取消
  PROMPT_GEN_AVATAR_STATUS_SUCCES = 5;  // 生成完成
  PROMPT_GEN_AVATAR_STATUS_FAIL = 6;  // 生成或检查失败
}

// 泼墨体产生虚拟形象错误原因
enum PromptErrorCode {
  PROMPT_ERROR_CODE_UNSPECIFIED = 0;
  PROMPT_ERROR_CODE_NOFACE = 1;  // 无人脸
  PROMPT_ERROR_CODE_BAN = 2;  // 上传的图片审核不过
}

//搭子卡片虚拟形象信息
message MijingPartnerCardAvatarInfo{  
  string avatar_id = 1;//唯一id
  string native_img_url = 2;//原生图像
  PromptGenType gen_type = 3;//prompt生成图片的模式
  PromptGenAvatarStatus status = 4;//prompt生成图片的进度状态
  PromptErrorCode reason_code = 5;//当生成失败的话，会注明原因
  repeated string avatar_img_list = 6;//prompt返回的形象照片（最多四张）
}

//检测用户上传的照片是否符合要求
message CheckMijingGenerateAvatarRequest{
  ga.BaseReq base_req = 1;
  MijingPartnerCardAvatarInfo info = 2;//形象照片请求信息
}

message CheckMijingGenerateAvatarResponse{
  ga.BaseResp base_resp = 1;
  string avatar_id = 2;//唯一id
}

//请求生成搭子卡片形象照片
message SubmitMijingGenerateAvatarRequest{
  ga.BaseReq base_req = 1;
  MijingPartnerCardAvatarInfo info = 2;//形象照片请求信息
}

message SubmitMijingGenerateAvatarResponse{
  ga.BaseResp base_resp = 1;
  string avatar_id = 2;//唯一id
}

//停止生成搭子卡片形象照片
message StopMijingGenerateAvatarRequest{
  ga.BaseReq base_req = 1;
  string avatar_id = 2;//唯一id
}

message StopMijingGenerateAvatarResponse{
  ga.BaseResp base_resp = 1;
}

//获取用户生成搭子形象照片的进度
message GetMijingGenerateAvatarRequest{
  ga.BaseReq base_req = 1;
  string avatar_id = 2;//唯一id
}

message GetMijingGenerateAvatarResponse{
  ga.BaseResp base_resp = 1;
  MijingPartnerCardAvatarInfo info = 2;//形象照片信息
}

//push形象照片生成进度结果信息
//收到这个推送之后，再调用GetMijingGenerateAvatar，获取详情进度
message PromptGenAvatarPush {
  string avatar_id = 1;//唯一id
}

//搭子虚拟形象需求 --end


