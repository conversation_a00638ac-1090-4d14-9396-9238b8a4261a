#!/bin/sh

if [ $# -lt 1 ]; then
    echo "Usage: init_service.sh <name> [<gateway>]"
    echo "       e.g.: init_service.sh ugc-logic *************:80"
    exit 0
fi

etcdEndpoints=$ETCD_ENDPOINTS
name=$1

grpcConfig='{"text":"grpc_config=[{\"clientLanguage\":[\"CPP\",\"GO\"],\"serviceConfig\":{\"loadBalancingPolicy\":\"grpclb\"}}]"}'
grpclbAddr='{"host":"grpclb.52tt.local","port":80}'

alias etcdput='etcdctl --endpoints=${etcdEndpoints} put' 
export ETCDCTL_API=3


# add dns record  
etcdput /skydns/local/52tt/${name}/_grpc_config ${grpcConfig}
etcdput /skydns/local/52tt/${name}/_tcp/_grpclb ${grpclbAddr}

checkGateway=0
if [ ! -z "$2" ]; then
    gateway=$2

    k8sGateway="{\"Op\":0,\"Addr\":\""${gateway}"\",\"Metadata\":null}"
    # add kubernetes gateway address
    etcdput ${name}.52tt.local/k8s-gateway ${k8sGateway} 
    checkGateway=1
fi


# dns_check
dns_check() {
    echo 'checking dns records...\n'
    dig @$DNS TXT _grpc_config.${name}.52tt.local | grep "ANSWER SECTION" -A 1 | grep -v ANSWER
    dig @$DNS SRV _grpclb._tcp.${name}.52tt.local | grep "ANSWER SECTION" -A 1 | grep -v ANSWER
    echo ""
}

command -v dig >/dev/null 2>&1 && dns_check

gateway_check() {
    echo "checking gateway by grpcurl...\n"
    grpcurl --plaintext --authority ${name}.52tt.local $gateway list 2>&1
    echo ""
}

# gateway check
if [ $checkGateway -eq 1 ]; then
    command -v grpcurl >/dev/null 2>&1 && gateway_check
fi 
