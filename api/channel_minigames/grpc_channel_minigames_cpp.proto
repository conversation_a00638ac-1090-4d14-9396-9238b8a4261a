syntax = "proto3";
package ga.api.channel_minigames;



import "api/extension/extension.proto";
// import "channel_.proto";

//server_name=channelminigameslogic
//language=cpp

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/channel_minigames;channel_minigames";
option objc_class_prefix = "RPC";

service ChannelMiniGamesLogic {
    // rpc ChannelGame(ga.ChannelGameReq) returns (ga.ChannelGameResp) {
    //     option (ga.api.extension.command) = {
    //         id: 2063
    //     };
    // }

    // rpc ChannelGetPresentRunwayList(ga.GetChannelPresentRunwayListReq) returns (ga.GetChannelPresentRunwayListResp) {
    //     option (ga.api.extension.command) = {
    //         id: 2510
    //     };
    // }

    
    // rpc ChannelMagicExpression(ga.SendMagicExpressionReq) returns (ga.SendMagicExpressionResp) {
    //     option (ga.api.extension.command) = {
    //         id: 2067
    //     };
    // }

    // rpc ChannelPresentCountInit(ga.GetChannelPresentCountStateReq) returns (ga.GetChannelPresentCountStateResp) {
    //     option (ga.api.extension.command) = {
    //         id: 30001
    //     };
    // }

    // rpc ChannelPresentCount(ga.ChannelPresentCountReq) returns (ga.ChannelPresentCountResp) {
    //     option (ga.api.extension.command) = {
    //         id: 30000
    //     };
    // }

    // rpc ChannelVotePKCancel(ga.ChannelPKCancelResp) returns (ga.ChannelPKCancelResp) {
    //     option (ga.api.extension.command) = {
    //         id: 5004
    //     };
    // }

    // rpc ChannelVotePKGetInfo(ga.GetChannelVotePKInfoResp) returns (ga.GetChannelVotePKInfoResp) {
    //     option (ga.api.extension.command) = {
    //         id: 5005
    //     };
    // }

    // rpc ChannelVotePKStart(ga.ChannelVotePkStartResp) returns (ga.ChannelVotePkStartResp) {
    //     option (ga.api.extension.command) = {
    //         id: 5003
    //     };
    // }

    // rpc ChannelVotePKVote(ga.ChannelVotePkVoteResp) returns (ga.ChannelVotePkVoteResp) {
    //     option (ga.api.extension.command) = {
    //         id: 5002
    //     };
    // }

    option (ga.api.extension.logic_service_name) = "channelminigameslogic";
    option (ga.api.extension.logic_service_language) = "cpp";
}
