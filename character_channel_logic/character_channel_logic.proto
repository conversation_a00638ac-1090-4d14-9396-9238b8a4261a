syntax = "proto3";

package ga.character_channel_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/character-channel-logic";

// const unsigned int CMD_CharacterApplyOnMic = 5700;         // 申请/邀请 上麦
message CharacterApplyOnMicReq {
  ga.BaseReq base_req = 1;
  uint32 op_type = 2;     // 1-用户申请； 2-房管邀请
  uint32 cid = 3;  // 房间id
  uint32 invite_uid = 4; // 被邀请的用户id
}

message CharacterApplyOnMicResp {
  ga.BaseResp base_resp = 1;
}

// const unsigned int CMD_CharacterAgreeApplyMic = 5701;         // 同意/接受 上麦
message CharacterAgreeApplyMicReq {
  ga.BaseReq base_req = 1;
  uint32 op_type = 2;  // 1-房主同意申请； 2-用户接受邀请
  uint32 applicant_uid = 3;
  string invite_code = 4; // 邀请上麦业务邀请码
  uint32 cid = 5;
  int64 ts = 6; // 邀请推送时间
}

message CharacterAgreeApplyMicResp {
  ga.BaseResp base_resp = 1;
  uint32 mic_id = 2;  // 麦位id
  string token = 3;   // 上麦token
}

// 申请/邀请 上麦推送
message CharacterApplyOnMicNotify {
  uint32 op_type = 1; // 推送行为，1-用户申请（房间广播，客户端判断是房管的才能看到），2-房管邀请(只推被邀请人)
  uint32 cid = 2; // 房间id
  uint32 uid = 3; // 申请人/邀请人 id
  string nickname = 4; // 申请人/邀请人 昵称
  string invite_code = 5; // 邀请上麦业务邀请码
  int64 ts = 6; // 邀请推送时间
  string account = 7; // 用户ttid 账号
  uint32 sex = 8; // 用户性别，
}

// 上麦推送，只推给申请人
message CharacterAgreeOnMicNotify {
  uint32 cid = 1; // 房间id
  uint32 uid = 2; // 申请人/邀请人 id
  uint32 mic_id = 3; // 麦位id
  string token = 4; // 上麦token
}

// 处理申请通知，房间广播，公屏消失
message CharacterHandleMicNotify {
  uint32 cid = 1; // 房间id
  uint32 uid = 2; // 申请人id
}