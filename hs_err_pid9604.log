#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1048576 bytes for AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./src/hotspot/share/memory/allocation.cpp:46), pid=9604, tid=8408
#
# JRE version:  (11.0.9+11) (build )
# Java VM: OpenJDK 64-Bit Server VM (11.0.9+11-b1145.21, mixed mode, sharing, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.git4idea.http.GitAskPassApp Username for 'https://gitlab.ttyuyin.com': 

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.2913)
Time: Sun May 14 19:00:24 2023 �й���׼ʱ�� elapsed time: 0.004832 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001ddbb20a000):  JavaThread "Unknown thread" [_thread_in_vm, id=8408, stack(0x0000006946700000,0x0000006946800000)]

Stack: [0x0000006946700000,0x0000006946800000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x5ed3ca]
V  [jvm.dll+0x722725]
V  [jvm.dll+0x723c7d]
V  [jvm.dll+0x724333]
V  [jvm.dll+0x242bd8]
V  [jvm.dll+0xb3614]
V  [jvm.dll+0x2e0d97]
V  [jvm.dll+0x2d9f5a]
V  [jvm.dll+0x701517]
V  [jvm.dll+0x702d0c]
V  [jvm.dll+0x358b39]
V  [jvm.dll+0x6e4f9e]
V  [jvm.dll+0x3c0db3]
V  [jvm.dll+0x3c2f61]
C  [jli.dll+0x5373]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17614]
C  [ntdll.dll+0x526a1]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001ddb984e6b0, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x000001ddbb224000 GCTaskThread "GC Thread#0" [stack: 0x0000006946800000,0x0000006946900000] [id=6968]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007fff08496160]

VM state:not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001ddbb2072f0] Heap_lock - owner thread: 0x000001ddbb20a000

Heap address: 0x0000000702600000, size: 4058 MB, Compressed Oops mode: Non-zero based: 0x0000000702600000
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0

GC Heap History (0 events):
No events

Deoptimization events (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

Events (1 events):
Event: 0.003 Loaded shared library D:\GoLand 2020.3\jbr\bin\zip.dll


Dynamic libraries:
0x00007ff60a860000 - 0x00007ff60a86a000 	D:\GoLand 2020.3\jbr\bin\java.exe
0x00007fff5bdb0000 - 0x00007fff5bfa8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007fff5a0f0000 - 0x00007fff5a1af000 	C:\Windows\System32\KERNEL32.DLL
0x00007fff59800000 - 0x00007fff59af6000 	C:\Windows\System32\KERNELBASE.dll
0x00007fff59b00000 - 0x00007fff59c00000 	C:\Windows\System32\ucrtbase.dll
0x00007fff46960000 - 0x00007fff46979000 	D:\GoLand 2020.3\jbr\bin\jli.dll
0x00007fff468a0000 - 0x00007fff468b7000 	D:\GoLand 2020.3\jbr\bin\VCRUNTIME140.dll
0x00007fff5aa20000 - 0x00007fff5abbd000 	C:\Windows\System32\USER32.dll
0x00007fff595b0000 - 0x00007fff595d2000 	C:\Windows\System32\win32u.dll
0x00007fff4e930000 - 0x00007fff4ebca000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e\COMCTL32.dll
0x00007fff5b950000 - 0x00007fff5b97c000 	C:\Windows\System32\GDI32.dll
0x00007fff5b500000 - 0x00007fff5b59e000 	C:\Windows\System32\msvcrt.dll
0x00007fff59490000 - 0x00007fff595a5000 	C:\Windows\System32\gdi32full.dll
0x00007fff59d70000 - 0x00007fff59e0d000 	C:\Windows\System32\msvcp_win.dll
0x00007fff5bc20000 - 0x00007fff5bc50000 	C:\Windows\System32\IMM32.DLL
0x00007fff13cc0000 - 0x00007fff13d5d000 	D:\GoLand 2020.3\jbr\bin\msvcp140.dll
0x00007fff081b0000 - 0x00007fff08c7e000 	D:\GoLand 2020.3\jbr\bin\server\jvm.dll
0x00007fff5bcc0000 - 0x00007fff5bd6f000 	C:\Windows\System32\ADVAPI32.dll
0x00007fff5b7b0000 - 0x00007fff5b84c000 	C:\Windows\System32\sechost.dll
0x00007fff5b5a0000 - 0x00007fff5b6c6000 	C:\Windows\System32\RPCRT4.dll
0x00007fff5bcb0000 - 0x00007fff5bcb8000 	C:\Windows\System32\PSAPI.DLL
0x00007fff54e90000 - 0x00007fff54e99000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007fff4d8b0000 - 0x00007fff4d8d7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007fff54ce0000 - 0x00007fff54cea000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007fff5af40000 - 0x00007fff5afab000 	C:\Windows\System32\WS2_32.dll
0x00007fff57370000 - 0x00007fff57382000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007fff52ae0000 - 0x00007fff52af1000 	D:\GoLand 2020.3\jbr\bin\verify.dll
0x00007fff534f0000 - 0x00007fff536d4000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007fff530b0000 - 0x00007fff530e4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007fff595e0000 - 0x00007fff59662000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007fff52ab0000 - 0x00007fff52ad9000 	D:\GoLand 2020.3\jbr\bin\java.dll
0x00007fff54e80000 - 0x00007fff54e8b000 	D:\GoLand 2020.3\jbr\bin\jimage.dll
0x00007fff52a90000 - 0x00007fff52aa8000 	D:\GoLand 2020.3\jbr\bin\zip.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\GoLand 2020.3\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e;D:\GoLand 2020.3\jbr\bin\server

VM Arguments:
java_command: org.jetbrains.git4idea.http.GitAskPassApp Username for 'https://gitlab.ttyuyin.com': 
java_class_path (initial): D:/GoLand 2020.3/plugins/git4idea/lib/git4idea-rt.jar;D:/GoLand 2020.3/lib/xmlrpc-2.0.1.jar;D:/GoLand 2020.3/lib/commons-codec-1.14.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4255121408                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7594288                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122031976                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122031976                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=D:\Git\mingw64\libexec\git-core;D:\Git\mingw64\libexec\git-core;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;D:\VMware\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;D:\GO1.16\bin;D:\xshell7;D:\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Bandizip;D:\GoLand 2020.3\bin;D:\IDEA\IntelliJ IDEA 2022.1\bin;D:\GO1.16\bin;E:\protoc-3.7.0-win64\bin;F:\gopath\bin;C:\Users\<USER>\Desktop\grpcurl-master\cmd\grpcurl;D:\Microsoft VS Code\bin
USERNAME=TT
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 19041 (10.0.19041.2913)
OS uptime: 4 days 17:03 hours

CPU:total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 16225M (2562M free)
TotalPageFile size 45949M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 10M, peak: 10M
current process commit charge ("private bytes"): 59M, peak: 59M

vm_info: OpenJDK 64-Bit Server VM (11.0.9+11-b1145.21) for windows-amd64 JRE (11.0.9+11-b1145.21), built on Nov 21 2020 14:37:09 by "" with MS VC++ 14.0 (VS2015)

END.
