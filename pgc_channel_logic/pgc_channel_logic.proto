syntax = "proto3";

package ga.pgc_channel_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/pgc-channel-logic";


// 体验券信息
message TicketInfo {
   uint32 id = 1;  // 券id
   string name = 2;  // 券名称
   string popup_msg = 3;  // 弹窗说明文案
   uint32 expire_ts = 4; // 有效期 单位秒
   string icon_url = 5; // 券图标
   string ch_jump_rul = 6;  // 房间跳转短链接 
}

// 获取用户体验券列表
message GetUserTicketListRequest{
   ga.BaseReq base_req = 1;
}
message GetUserTicketListResponse{
   ga.BaseResp base_resp = 1;
   repeated TicketInfo ticket_list = 2;
}

// 检测体验券的使用权限
message CheckTicketUsePerRequest{
   ga.BaseReq base_req = 1;
   uint32 uid = 2; 
   uint32 cid = 3; 
   uint32 ticket_id = 4;  // 券id
}
message CheckTicketUsePerResponse{
   ga.BaseResp base_resp = 1;
   bool has_per_channel = 2;  //房间是否有使用权限
   bool has_per_user = 3;   // 用户是有收礼权限
   string ch_jump_rul = 4;  // 房间跳转短链接 
   string pkg_top_msg = 5;  // 背包顶部说明文案
   string user_no_per_toast = 6; //用户没权限toast
}   

// 获取券的提醒信息
message GetUserTicketRemindRequest{
   ga.BaseReq base_req = 1;
   uint32 cid = 2; // 房间id
}
message GetUserTicketRemindResponse{
   ga.BaseResp base_resp = 1;
   bool is_remind = 2;  //是否提醒
   repeated TicketInfo info_list = 3;  //用户房间可用券列表
}

// 用户房间可用券信息推送
message UserTicketListPushMsg {
   uint32 cid = 1;  // 房间id
   repeated TicketInfo info_list = 2;  //用户房间可用券列表
}


//获取用户的被冠名信息
message GetUserTitledInfoRequest {
   ga.BaseReq base_req = 1;
   uint32 target_uid = 2;
   uint32 channel_id = 3;
}
message GetUserTitledInfoResponse {
   ga.BaseResp base_resp = 1;
   bool has_entry = 2; // 是否有被冠名入口
   string channel_titled_rank = 3;  // 房间被冠名榜文案
   uint32 titled_cnt = 4;  //被冠名人数
   repeated ga.UserProfile top_titled_list = 5; //被冠名榜单
}


// 用户冠名公屏提醒
message UserTitleImRemindInfo {
   string msg = 1; 
   string high_light_txt = 2;  //高亮字段
   ga.UserProfile titled_user = 3;  //被冠名用户信息
}

//用户冠名弹窗提醒
message UserTitlePopRemindInfo {
   string title = 1;  // 标题
   string content = 2;  //内容
   ga.UserProfile titled_user = 3;  //被冠名用户信息
}

// 冠名类型
enum ETitleType {
    E_TITLE_TYPE_UNSPECIFIED = 0;   //无效
    E_TITLE_TYPE_WEEK = 1;   // 周冠名
    E_TITLE_TYPE_MONTH = 2;  // 月冠名
    E_TITLE_TYPE_DAY = 3;  // 日冠名
}

//冠名用户进房提示
message TitleUserEnterChannelMsg {
   string msg = 1; // 废弃
   uint32 title_type = 2;  //冠名类型 see ETitleType
   ga.UserProfile title_user = 3;  //冠名用户信息
}

//权益类型 移位定义
enum PrivilegeType {
   PRIVILEGE_TYPE_UNSPECIFIED = 0;  //无效
   PRIVILEGE_TYPE_ENTER_EFFECT = 1;  // 进房特效
   PRIVILEGE_TYPE_HEADWEAR = 2;  // 冠名麦位框
   PRIVILEGE_TYPE_GIFT_SEND = 4;  // 礼物赠送权
   PRIVILEGE_TYPE_POP_UP = 8;  // 冠名弹窗
   PRIVILEGE_TYPE_PLATE = 16;  // 铭牌
}

message TitlePrivilege {
    //图片样式
    enum ImgStyle {
       IMG_STYLE_UNSPECIFIED = 0 ;  //无效
       IMG_STYLE_SHORT = 1;  // 短样式
       IMG_STYLE_LONG = 2; // 长样式
    }
    // 图片资源类型
    enum ImgType {
       IMG_TYPE_UNSPECIFIED = 0 ;  //无效
       IMG_TYPE_STATIC = 1;  // 静态图
       IMG_TYPE_DYNAMIC = 2; // 动态图
    }
    string small_img = 1;  // 缩略图
    string big_img = 2;  //大图
    string name = 3;
    string desc_msg = 4; 
    uint32 img_style = 5;  // 图片样式 see ImgStyle
    uint32 img_type = 6;  // 大图资源类型 see ImgType
    uint32 privilege_type = 7;   // 权益类型 see PrivilegeType
}

message TitlePrivilegeList {
    repeated TitlePrivilege list = 1; 
}

//获取用户的冠名信息
message GetUserTitleInfoRequest{
   ga.BaseReq base_req = 1;
   uint32 target_uid = 2;
   uint32 channel_id = 3;
}
message GetUserTitleInfoResponse { 
   ga.BaseResp base_resp = 1;
   ga.UserProfile title_user = 2;  // 冠名用户信息
   ga.UserProfile titled_user = 3;  // 被冠名用户信息
   uint32 titled_cnt = 4;  //冠名人数
   string process_msg = 5; //进度文案
   uint32 send_gift_val = 6;  //送礼值 
   map<uint32,uint32> map_type_total = 7; //冠名总值 type see ETitleType
   map<uint32,uint32> map_type_expire_ts = 8; //冠名过期时间，不为0表示完成冠名 type see ETitleType
   bool privilege_switch = 9;  // true:打开 false:关闭
   uint32 title_type = 10; //当前的冠名类型 see ETitleType
   map<uint32, TitlePrivilegeList> map_type_privilege = 11; // 特权列表 type see ETitleType
   string desc_msg = 12;  //描述文案
   map<uint32, bool> map_type_switch = 13;  // 权益的全局开关， type see PrivilegeType
}


//冠名成功弹窗
message TitleSuccessNotify{
    string effect_url = 1;      
    string effect_md5 = 2; 
    string effect_extend_json = 3; // 拓展特效json
}


//用户冠名权益开关
message UserTitlePrivilegeSwitchRequest {
   ga.BaseReq base_req = 1;
   bool is_on = 2; // true:打开 false:关闭
   uint32 target_uid = 3;
   uint32 channel_id = 4;
   uint32 privilge_type = 5;   // 某一权益的全局开关， 为0时表示对某一冠名用户的所有权益开关
}
message UserTitlePrivilegeSwitchResponse {
   ga.BaseResp base_resp = 1;
}


// 获取房间冠名信息
message GetChannelTitleInfoRequest {
   ga.BaseReq base_req = 1;
   uint32 channel_id = 2; 
}
message GetChannelTitleInfoResponse {
   ga.BaseResp base_resp = 1;
   bool has_entry = 2;  //是否有冠名权限
}


//判断用户是否能被冠名
message CheckUserIsCanTitledRequest {
   ga.BaseReq base_req = 1;
   uint32 target_uid = 2;
}
message CheckUserIsCanTitledResponse {
   ga.BaseResp base_resp = 1;
   bool is_can = 2;  //是否能被冠名
   string toast_msg = 3;  // toast文案
}


// 房间成员冠名周榜信息
message PgcGloryTitleAnchorRank {
   uint32 rank = 1;              // 排名
   ga.UserProfile anchor_info = 2;  // 主播信息
   uint32 title_user_num = 3;    // 冠名人数
   int64  love_val = 4;          // 爱意值
   repeated ga.UserProfile top_user_list = 5; // 冠名用户TOP3
   bool   is_titled_my = 6;         // 我是否冠名过
}

//麦上可冠名主播信息
message PgcGloryTitleMicAnchor {
   uint32 mic_id = 1;              // 麦位id
   ga.UserProfile anchor_info = 2;  // 主播信息
}

// 获取房间主播冠名周榜信息
message GetPgcGloryTitleAnchorRankRequest{
   ga.BaseReq base_req = 1;
   uint32 cid = 2; // 房间id
   uint32 last_rank = 3;      // 最后排名,第一页传0
}
message GetPgcGloryTitleAnchorRankResponse {
   ga.BaseResp base_resp = 1;
   repeated PgcGloryTitleAnchorRank rank_list = 2;  // 冠名周榜
   repeated ga.UserProfile last_top_list = 3;          // 上周冠名榜TOP3
   PgcGloryTitleAnchorRank my_rank = 4;             // 我的排名: 若用户非主播则为空
   int64  my_diff_val = 5;                          // 我的排名差值
   int64  end_ts = 6;                               // 结束时间戳
   bool   is_all_end = 7;                           // 是否最后一页
   repeated PgcGloryTitleMicAnchor mic_anchor_list = 8; // 麦上可冠名主播列表(第1页rank_list为空时返回)
}

message PgcGloryTitleInvestorRank {
   uint32 rank = 1;                // 排名
   ga.UserProfile investor_info = 2;  // 金主信息
   int64  love_val = 3;            // 爱意值
   int64  expire_ts = 4;           // 到期时间戳
   uint32 title_type = 5;          // 冠名类型 ETitleType
}
// 获取房间冠名-金主榜月榜信息
message GetPgcGloryTitleInvestorRankRequest{
   ga.BaseReq base_req = 1;
   uint32 cid = 2; // 房间id
   uint32 target_uid = 3;  // 目标用户id
   uint32 last_rank = 4;      // 最后排名,第一页传0
}
message GetPgcGloryTitleInvestorRankResponse {
   ga.BaseResp base_resp = 1;
   repeated PgcGloryTitleInvestorRank rank_list = 2;
   repeated ga.UserProfile last_top_list = 3;   // 上月冠名榜TOP3
   uint32 total_num = 4;  // 冠名总人数
   int64  end_ts = 5;     // 结束时间戳
   PgcGloryTitleInvestorRank my_rank = 6; // 我的排名
   int64  my_diff_val = 7;                // 我的排名差值
   bool   is_all_end = 8;                 // 是否最后一页
 }
