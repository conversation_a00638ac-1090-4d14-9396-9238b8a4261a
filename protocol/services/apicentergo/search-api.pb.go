// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/search-api.proto

package apicentergo // import "golang.52tt.com/protocol/services/apicentergo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 白名单开关，true 开 false 关
type SwitchSearchWhiteListReq struct {
	WhiteListStatus      bool     `protobuf:"varint,1,opt,name=white_list_status,json=whiteListStatus,proto3" json:"white_list_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchSearchWhiteListReq) Reset()         { *m = SwitchSearchWhiteListReq{} }
func (m *SwitchSearchWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*SwitchSearchWhiteListReq) ProtoMessage()    {}
func (*SwitchSearchWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_search_api_b9297a8703f02a22, []int{0}
}
func (m *SwitchSearchWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchSearchWhiteListReq.Unmarshal(m, b)
}
func (m *SwitchSearchWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchSearchWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *SwitchSearchWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchSearchWhiteListReq.Merge(dst, src)
}
func (m *SwitchSearchWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_SwitchSearchWhiteListReq.Size(m)
}
func (m *SwitchSearchWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchSearchWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchSearchWhiteListReq proto.InternalMessageInfo

func (m *SwitchSearchWhiteListReq) GetWhiteListStatus() bool {
	if m != nil {
		return m.WhiteListStatus
	}
	return false
}

// 白名单开关，true 开 false 关
type SwitchSearchWhiteListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchSearchWhiteListResp) Reset()         { *m = SwitchSearchWhiteListResp{} }
func (m *SwitchSearchWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*SwitchSearchWhiteListResp) ProtoMessage()    {}
func (*SwitchSearchWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_search_api_b9297a8703f02a22, []int{1}
}
func (m *SwitchSearchWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchSearchWhiteListResp.Unmarshal(m, b)
}
func (m *SwitchSearchWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchSearchWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *SwitchSearchWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchSearchWhiteListResp.Merge(dst, src)
}
func (m *SwitchSearchWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_SwitchSearchWhiteListResp.Size(m)
}
func (m *SwitchSearchWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchSearchWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchSearchWhiteListResp proto.InternalMessageInfo

// 获取白名单开关状态
type GetSearchWhiteListStatusReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSearchWhiteListStatusReq) Reset()         { *m = GetSearchWhiteListStatusReq{} }
func (m *GetSearchWhiteListStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetSearchWhiteListStatusReq) ProtoMessage()    {}
func (*GetSearchWhiteListStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_search_api_b9297a8703f02a22, []int{2}
}
func (m *GetSearchWhiteListStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchWhiteListStatusReq.Unmarshal(m, b)
}
func (m *GetSearchWhiteListStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchWhiteListStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetSearchWhiteListStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchWhiteListStatusReq.Merge(dst, src)
}
func (m *GetSearchWhiteListStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetSearchWhiteListStatusReq.Size(m)
}
func (m *GetSearchWhiteListStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchWhiteListStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchWhiteListStatusReq proto.InternalMessageInfo

type GetSearchWhiteListStatusResp struct {
	WhiteListStatus      bool     `protobuf:"varint,1,opt,name=white_list_status,json=whiteListStatus,proto3" json:"white_list_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSearchWhiteListStatusResp) Reset()         { *m = GetSearchWhiteListStatusResp{} }
func (m *GetSearchWhiteListStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetSearchWhiteListStatusResp) ProtoMessage()    {}
func (*GetSearchWhiteListStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_search_api_b9297a8703f02a22, []int{3}
}
func (m *GetSearchWhiteListStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchWhiteListStatusResp.Unmarshal(m, b)
}
func (m *GetSearchWhiteListStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchWhiteListStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetSearchWhiteListStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchWhiteListStatusResp.Merge(dst, src)
}
func (m *GetSearchWhiteListStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetSearchWhiteListStatusResp.Size(m)
}
func (m *GetSearchWhiteListStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchWhiteListStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchWhiteListStatusResp proto.InternalMessageInfo

func (m *GetSearchWhiteListStatusResp) GetWhiteListStatus() bool {
	if m != nil {
		return m.WhiteListStatus
	}
	return false
}

// 添加白名单
// 不用填create_time，begin_time、end_time都为0则为永久白名单
type BatchAddSearchWhiteListReq struct {
	ItemList             []*SearchWhiteListItem `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchAddSearchWhiteListReq) Reset()         { *m = BatchAddSearchWhiteListReq{} }
func (m *BatchAddSearchWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddSearchWhiteListReq) ProtoMessage()    {}
func (*BatchAddSearchWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_search_api_b9297a8703f02a22, []int{4}
}
func (m *BatchAddSearchWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddSearchWhiteListReq.Unmarshal(m, b)
}
func (m *BatchAddSearchWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddSearchWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddSearchWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddSearchWhiteListReq.Merge(dst, src)
}
func (m *BatchAddSearchWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddSearchWhiteListReq.Size(m)
}
func (m *BatchAddSearchWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddSearchWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddSearchWhiteListReq proto.InternalMessageInfo

func (m *BatchAddSearchWhiteListReq) GetItemList() []*SearchWhiteListItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type BatchAddSearchWhiteListResp struct {
	FailList             []*SearchWhiteListFailResult `protobuf:"bytes,1,rep,name=fail_list,json=failList,proto3" json:"fail_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *BatchAddSearchWhiteListResp) Reset()         { *m = BatchAddSearchWhiteListResp{} }
func (m *BatchAddSearchWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddSearchWhiteListResp) ProtoMessage()    {}
func (*BatchAddSearchWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_search_api_b9297a8703f02a22, []int{5}
}
func (m *BatchAddSearchWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddSearchWhiteListResp.Unmarshal(m, b)
}
func (m *BatchAddSearchWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddSearchWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddSearchWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddSearchWhiteListResp.Merge(dst, src)
}
func (m *BatchAddSearchWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddSearchWhiteListResp.Size(m)
}
func (m *BatchAddSearchWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddSearchWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddSearchWhiteListResp proto.InternalMessageInfo

func (m *BatchAddSearchWhiteListResp) GetFailList() []*SearchWhiteListFailResult {
	if m != nil {
		return m.FailList
	}
	return nil
}

type SearchWhiteListItem struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemContent          string   `protobuf:"bytes,2,opt,name=item_content,json=itemContent,proto3" json:"item_content,omitempty"`
	OperaterId           string   `protobuf:"bytes,3,opt,name=operater_id,json=operaterId,proto3" json:"operater_id,omitempty"`
	CreateTime           uint32   `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	BeginTime            uint32   `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchWhiteListItem) Reset()         { *m = SearchWhiteListItem{} }
func (m *SearchWhiteListItem) String() string { return proto.CompactTextString(m) }
func (*SearchWhiteListItem) ProtoMessage()    {}
func (*SearchWhiteListItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_search_api_b9297a8703f02a22, []int{6}
}
func (m *SearchWhiteListItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchWhiteListItem.Unmarshal(m, b)
}
func (m *SearchWhiteListItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchWhiteListItem.Marshal(b, m, deterministic)
}
func (dst *SearchWhiteListItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchWhiteListItem.Merge(dst, src)
}
func (m *SearchWhiteListItem) XXX_Size() int {
	return xxx_messageInfo_SearchWhiteListItem.Size(m)
}
func (m *SearchWhiteListItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchWhiteListItem.DiscardUnknown(m)
}

var xxx_messageInfo_SearchWhiteListItem proto.InternalMessageInfo

func (m *SearchWhiteListItem) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *SearchWhiteListItem) GetItemContent() string {
	if m != nil {
		return m.ItemContent
	}
	return ""
}

func (m *SearchWhiteListItem) GetOperaterId() string {
	if m != nil {
		return m.OperaterId
	}
	return ""
}

func (m *SearchWhiteListItem) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *SearchWhiteListItem) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *SearchWhiteListItem) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type SearchWhiteListFailResult struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemContent          string   `protobuf:"bytes,2,opt,name=item_content,json=itemContent,proto3" json:"item_content,omitempty"`
	FailReason           string   `protobuf:"bytes,3,opt,name=fail_reason,json=failReason,proto3" json:"fail_reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchWhiteListFailResult) Reset()         { *m = SearchWhiteListFailResult{} }
func (m *SearchWhiteListFailResult) String() string { return proto.CompactTextString(m) }
func (*SearchWhiteListFailResult) ProtoMessage()    {}
func (*SearchWhiteListFailResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_search_api_b9297a8703f02a22, []int{7}
}
func (m *SearchWhiteListFailResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchWhiteListFailResult.Unmarshal(m, b)
}
func (m *SearchWhiteListFailResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchWhiteListFailResult.Marshal(b, m, deterministic)
}
func (dst *SearchWhiteListFailResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchWhiteListFailResult.Merge(dst, src)
}
func (m *SearchWhiteListFailResult) XXX_Size() int {
	return xxx_messageInfo_SearchWhiteListFailResult.Size(m)
}
func (m *SearchWhiteListFailResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchWhiteListFailResult.DiscardUnknown(m)
}

var xxx_messageInfo_SearchWhiteListFailResult proto.InternalMessageInfo

func (m *SearchWhiteListFailResult) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *SearchWhiteListFailResult) GetItemContent() string {
	if m != nil {
		return m.ItemContent
	}
	return ""
}

func (m *SearchWhiteListFailResult) GetFailReason() string {
	if m != nil {
		return m.FailReason
	}
	return ""
}

// 删除白名单
// 只填id即可
type BatchDelSearchWhiteListReq struct {
	ItemList             []*SearchWhiteListItem `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchDelSearchWhiteListReq) Reset()         { *m = BatchDelSearchWhiteListReq{} }
func (m *BatchDelSearchWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelSearchWhiteListReq) ProtoMessage()    {}
func (*BatchDelSearchWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_search_api_b9297a8703f02a22, []int{8}
}
func (m *BatchDelSearchWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelSearchWhiteListReq.Unmarshal(m, b)
}
func (m *BatchDelSearchWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelSearchWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelSearchWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelSearchWhiteListReq.Merge(dst, src)
}
func (m *BatchDelSearchWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelSearchWhiteListReq.Size(m)
}
func (m *BatchDelSearchWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelSearchWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelSearchWhiteListReq proto.InternalMessageInfo

func (m *BatchDelSearchWhiteListReq) GetItemList() []*SearchWhiteListItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type BatchDelSearchWhiteListResp struct {
	FailList             []*SearchWhiteListFailResult `protobuf:"bytes,1,rep,name=fail_list,json=failList,proto3" json:"fail_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *BatchDelSearchWhiteListResp) Reset()         { *m = BatchDelSearchWhiteListResp{} }
func (m *BatchDelSearchWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelSearchWhiteListResp) ProtoMessage()    {}
func (*BatchDelSearchWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_search_api_b9297a8703f02a22, []int{9}
}
func (m *BatchDelSearchWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelSearchWhiteListResp.Unmarshal(m, b)
}
func (m *BatchDelSearchWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelSearchWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelSearchWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelSearchWhiteListResp.Merge(dst, src)
}
func (m *BatchDelSearchWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelSearchWhiteListResp.Size(m)
}
func (m *BatchDelSearchWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelSearchWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelSearchWhiteListResp proto.InternalMessageInfo

func (m *BatchDelSearchWhiteListResp) GetFailList() []*SearchWhiteListFailResult {
	if m != nil {
		return m.FailList
	}
	return nil
}

// 获取白名单
// content 要搜索的词汇
// begin_time、end_time 时间范围，都为0无范围
// page 、count 页码 每页个数
type GetSearchWhiteListReq struct {
	Content              string   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSearchWhiteListReq) Reset()         { *m = GetSearchWhiteListReq{} }
func (m *GetSearchWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*GetSearchWhiteListReq) ProtoMessage()    {}
func (*GetSearchWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_search_api_b9297a8703f02a22, []int{10}
}
func (m *GetSearchWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchWhiteListReq.Unmarshal(m, b)
}
func (m *GetSearchWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *GetSearchWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchWhiteListReq.Merge(dst, src)
}
func (m *GetSearchWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_GetSearchWhiteListReq.Size(m)
}
func (m *GetSearchWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchWhiteListReq proto.InternalMessageInfo

func (m *GetSearchWhiteListReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GetSearchWhiteListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetSearchWhiteListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetSearchWhiteListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetSearchWhiteListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetSearchWhiteListResp struct {
	ItemList             []*SearchWhiteListItem `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	TotalCount           uint32                 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetSearchWhiteListResp) Reset()         { *m = GetSearchWhiteListResp{} }
func (m *GetSearchWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*GetSearchWhiteListResp) ProtoMessage()    {}
func (*GetSearchWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_search_api_b9297a8703f02a22, []int{11}
}
func (m *GetSearchWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchWhiteListResp.Unmarshal(m, b)
}
func (m *GetSearchWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *GetSearchWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchWhiteListResp.Merge(dst, src)
}
func (m *GetSearchWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_GetSearchWhiteListResp.Size(m)
}
func (m *GetSearchWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchWhiteListResp proto.InternalMessageInfo

func (m *GetSearchWhiteListResp) GetItemList() []*SearchWhiteListItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *GetSearchWhiteListResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

// 导出热词
type ExportHotWordReq struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExportHotWordReq) Reset()         { *m = ExportHotWordReq{} }
func (m *ExportHotWordReq) String() string { return proto.CompactTextString(m) }
func (*ExportHotWordReq) ProtoMessage()    {}
func (*ExportHotWordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_search_api_b9297a8703f02a22, []int{12}
}
func (m *ExportHotWordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExportHotWordReq.Unmarshal(m, b)
}
func (m *ExportHotWordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExportHotWordReq.Marshal(b, m, deterministic)
}
func (dst *ExportHotWordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExportHotWordReq.Merge(dst, src)
}
func (m *ExportHotWordReq) XXX_Size() int {
	return xxx_messageInfo_ExportHotWordReq.Size(m)
}
func (m *ExportHotWordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExportHotWordReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExportHotWordReq proto.InternalMessageInfo

func (m *ExportHotWordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ExportHotWordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type ExportHotWordResp struct {
	WordList             []string `protobuf:"bytes,1,rep,name=word_list,json=wordList,proto3" json:"word_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExportHotWordResp) Reset()         { *m = ExportHotWordResp{} }
func (m *ExportHotWordResp) String() string { return proto.CompactTextString(m) }
func (*ExportHotWordResp) ProtoMessage()    {}
func (*ExportHotWordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_search_api_b9297a8703f02a22, []int{13}
}
func (m *ExportHotWordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExportHotWordResp.Unmarshal(m, b)
}
func (m *ExportHotWordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExportHotWordResp.Marshal(b, m, deterministic)
}
func (dst *ExportHotWordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExportHotWordResp.Merge(dst, src)
}
func (m *ExportHotWordResp) XXX_Size() int {
	return xxx_messageInfo_ExportHotWordResp.Size(m)
}
func (m *ExportHotWordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExportHotWordResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExportHotWordResp proto.InternalMessageInfo

func (m *ExportHotWordResp) GetWordList() []string {
	if m != nil {
		return m.WordList
	}
	return nil
}

func init() {
	proto.RegisterType((*SwitchSearchWhiteListReq)(nil), "apicentergo.SwitchSearchWhiteListReq")
	proto.RegisterType((*SwitchSearchWhiteListResp)(nil), "apicentergo.SwitchSearchWhiteListResp")
	proto.RegisterType((*GetSearchWhiteListStatusReq)(nil), "apicentergo.GetSearchWhiteListStatusReq")
	proto.RegisterType((*GetSearchWhiteListStatusResp)(nil), "apicentergo.GetSearchWhiteListStatusResp")
	proto.RegisterType((*BatchAddSearchWhiteListReq)(nil), "apicentergo.BatchAddSearchWhiteListReq")
	proto.RegisterType((*BatchAddSearchWhiteListResp)(nil), "apicentergo.BatchAddSearchWhiteListResp")
	proto.RegisterType((*SearchWhiteListItem)(nil), "apicentergo.SearchWhiteListItem")
	proto.RegisterType((*SearchWhiteListFailResult)(nil), "apicentergo.SearchWhiteListFailResult")
	proto.RegisterType((*BatchDelSearchWhiteListReq)(nil), "apicentergo.BatchDelSearchWhiteListReq")
	proto.RegisterType((*BatchDelSearchWhiteListResp)(nil), "apicentergo.BatchDelSearchWhiteListResp")
	proto.RegisterType((*GetSearchWhiteListReq)(nil), "apicentergo.GetSearchWhiteListReq")
	proto.RegisterType((*GetSearchWhiteListResp)(nil), "apicentergo.GetSearchWhiteListResp")
	proto.RegisterType((*ExportHotWordReq)(nil), "apicentergo.ExportHotWordReq")
	proto.RegisterType((*ExportHotWordResp)(nil), "apicentergo.ExportHotWordResp")
}

func init() {
	proto.RegisterFile("apicenter-go/search-api.proto", fileDescriptor_search_api_b9297a8703f02a22)
}

var fileDescriptor_search_api_b9297a8703f02a22 = []byte{
	// 532 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x54, 0x4d, 0x8f, 0x12, 0x41,
	0x10, 0xcd, 0xec, 0x17, 0x4c, 0xa1, 0xd1, 0x6d, 0xbf, 0x06, 0x91, 0x88, 0x73, 0x30, 0xc4, 0x04,
	0x30, 0x6b, 0x3c, 0x7a, 0x70, 0xd1, 0x55, 0xcc, 0x9e, 0x06, 0x93, 0x4d, 0xf4, 0x40, 0x9a, 0x99,
	0xda, 0xa1, 0x93, 0x61, 0xba, 0xb7, 0xbb, 0x10, 0xfe, 0x88, 0x3f, 0xcb, 0xff, 0x64, 0xba, 0xc7,
	0x59, 0x01, 0x81, 0xc4, 0xac, 0xde, 0xa8, 0xf7, 0xba, 0x5e, 0xd5, 0xab, 0x2a, 0x06, 0x9a, 0x5c,
	0x89, 0x18, 0x73, 0x42, 0xdd, 0x49, 0x65, 0xcf, 0x20, 0xd7, 0xf1, 0xa4, 0xc3, 0x95, 0xe8, 0x2a,
	0x2d, 0x49, 0xb2, 0xda, 0x35, 0x9d, 0xca, 0xf0, 0x0c, 0x82, 0xe1, 0x5c, 0x50, 0x3c, 0x19, 0xba,
	0x67, 0x17, 0x13, 0x41, 0x78, 0x2e, 0x0c, 0x45, 0x78, 0xc5, 0x5e, 0xc0, 0xf1, 0xdc, 0xc6, 0xa3,
	0x4c, 0x18, 0x1a, 0x19, 0xe2, 0x34, 0x33, 0x81, 0xd7, 0xf2, 0xda, 0xd5, 0xe8, 0xce, 0xbc, 0x7c,
	0x38, 0x74, 0x70, 0xd8, 0x80, 0xfa, 0x16, 0x1d, 0xa3, 0xc2, 0x26, 0x34, 0x3e, 0x20, 0xad, 0x31,
	0x45, 0x62, 0x84, 0x57, 0xe1, 0x27, 0x78, 0xb2, 0x9d, 0x36, 0xea, 0xaf, 0xfa, 0xf8, 0x0a, 0x8f,
	0x4f, 0x39, 0xc5, 0x93, 0xb7, 0x49, 0xb2, 0xc1, 0xd1, 0x1b, 0xf0, 0x05, 0xe1, 0xd4, 0x09, 0x05,
	0x5e, 0x6b, 0xbf, 0x5d, 0x3b, 0x69, 0x75, 0x97, 0xc6, 0xd1, 0x5d, 0xcb, 0x19, 0x10, 0x4e, 0xa3,
	0xaa, 0x4d, 0xb1, 0x51, 0x38, 0x86, 0xc6, 0x56, 0x71, 0xa3, 0x58, 0x1f, 0xfc, 0x4b, 0x2e, 0xb2,
	0x65, 0xf5, 0xe7, 0xbb, 0xd4, 0xcf, 0xb8, 0xc8, 0x22, 0x34, 0xb3, 0x8c, 0xa2, 0xaa, 0x4d, 0x74,
	0x35, 0x7e, 0x78, 0x70, 0x6f, 0x43, 0x17, 0xec, 0x11, 0x54, 0x5c, 0xeb, 0x22, 0x71, 0xd6, 0x6f,
	0x47, 0x47, 0x36, 0x1c, 0x24, 0xec, 0x19, 0xdc, 0x72, 0x44, 0x2c, 0x73, 0xc2, 0x9c, 0x82, 0xbd,
	0x96, 0xd7, 0xf6, 0xa3, 0x9a, 0xc5, 0xfa, 0x05, 0xc4, 0x9e, 0x42, 0x4d, 0x2a, 0xd4, 0x9c, 0x50,
	0xdb, 0xfc, 0x7d, 0xf7, 0x02, 0x4a, 0x68, 0x90, 0xd8, 0x07, 0xb1, 0x46, 0x4e, 0x38, 0x22, 0x31,
	0xc5, 0xe0, 0xc0, 0x15, 0x80, 0x02, 0xfa, 0x2c, 0xa6, 0xc8, 0x9a, 0x00, 0x63, 0x4c, 0x45, 0x5e,
	0xf0, 0x87, 0x8e, 0xf7, 0x1d, 0xe2, 0xe8, 0x3a, 0x54, 0x31, 0x4f, 0x0a, 0xf2, 0xc8, 0x91, 0x15,
	0xcc, 0x13, 0x4b, 0x85, 0x0b, 0xa8, 0x6f, 0xb5, 0x7d, 0x53, 0x53, 0x6e, 0xda, 0x1a, 0xb9, 0x91,
	0x79, 0x69, 0xea, 0xd2, 0x89, 0x5b, 0xe4, 0xfa, 0x14, 0xde, 0x61, 0xf6, 0xff, 0x4e, 0x61, 0x93,
	0xf8, 0xbf, 0x3a, 0x85, 0xef, 0x1e, 0x3c, 0xf8, 0xf3, 0x8f, 0x61, 0x9b, 0x0f, 0xa0, 0x52, 0x4e,
	0xc6, 0x73, 0xbe, 0xcb, 0x70, 0x6d, 0x51, 0x7b, 0xbb, 0x16, 0xb5, 0xbf, 0xb2, 0x28, 0xc6, 0xe0,
	0x40, 0xf1, 0xb4, 0x5c, 0xbe, 0xfb, 0xcd, 0xee, 0xc3, 0x61, 0x2c, 0x67, 0x39, 0xfd, 0xda, 0x78,
	0x11, 0x84, 0x0b, 0x78, 0xb8, 0xa9, 0x2d, 0xa3, 0x6e, 0x38, 0x54, 0xbb, 0x52, 0x92, 0xc4, 0xb3,
	0x51, 0x51, 0xb4, 0xe8, 0x1e, 0x1c, 0xd4, 0x77, 0x95, 0xcf, 0xe1, 0xee, 0xfb, 0x85, 0x92, 0x9a,
	0x3e, 0x4a, 0xba, 0x90, 0x3a, 0xb1, 0xb3, 0x58, 0x75, 0xec, 0xed, 0x72, 0xbc, 0xb7, 0x7a, 0x9a,
	0x2f, 0xe1, 0x78, 0x4d, 0xcd, 0x28, 0xd6, 0x00, 0x7f, 0x2e, 0x75, 0xf2, 0xdb, 0x82, 0x1f, 0x55,
	0x2d, 0x60, 0x1b, 0x3c, 0xed, 0x7d, 0xe9, 0xa4, 0x32, 0xe3, 0x79, 0xda, 0x7d, 0x7d, 0x42, 0xd4,
	0x8d, 0xe5, 0xb4, 0xe7, 0xbe, 0xa9, 0xb1, 0xcc, 0x7a, 0x06, 0xf5, 0x37, 0x11, 0xa3, 0xe9, 0x2d,
	0xf9, 0x1d, 0x1f, 0x39, 0xfa, 0xd5, 0xcf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xd0, 0xb1, 0x18, 0xa6,
	0x93, 0x05, 0x00, 0x00,
}
