// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/treasure-privilege-api.proto

package apicentergo // import "golang.52tt.com/protocol/services/apicentergo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 条件类型
type ConditionType int32

const (
	ConditionType_CONDITION_TYPE_NORMAL         ConditionType = 0
	ConditionType_CONDITION_TYPE_RECHARGE       ConditionType = 1
	ConditionType_CONDITION_TYPE_SEND_GIFT      ConditionType = 2
	ConditionType_CONDITION_TYPE_NOBILITY_LEVEL ConditionType = 3
	ConditionType_CONDITION_TYPE_RICH_LEVEL     ConditionType = 4
	ConditionType_CONDITION_TYPE_CONSUMER       ConditionType = 5
)

var ConditionType_name = map[int32]string{
	0: "CONDITION_TYPE_NORMAL",
	1: "CONDITION_TYPE_RECHARGE",
	2: "CONDITION_TYPE_SEND_GIFT",
	3: "CONDITION_TYPE_NOBILITY_LEVEL",
	4: "CONDITION_TYPE_RICH_LEVEL",
	5: "CONDITION_TYPE_CONSUMER",
}
var ConditionType_value = map[string]int32{
	"CONDITION_TYPE_NORMAL":         0,
	"CONDITION_TYPE_RECHARGE":       1,
	"CONDITION_TYPE_SEND_GIFT":      2,
	"CONDITION_TYPE_NOBILITY_LEVEL": 3,
	"CONDITION_TYPE_RICH_LEVEL":     4,
	"CONDITION_TYPE_CONSUMER":       5,
}

func (x ConditionType) String() string {
	return proto.EnumName(ConditionType_name, int32(x))
}
func (ConditionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_treasure_privilege_api_7dca5731ede7259c, []int{0}
}

// 条件值
type ConditionValue struct {
	Value                uint64   `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	PrivilegeSeconds     uint32   `protobuf:"varint,4,opt,name=privilege_seconds,json=privilegeSeconds,proto3" json:"privilege_seconds,omitempty"`
	PrivilegeEnd         uint32   `protobuf:"varint,5,opt,name=privilege_end,json=privilegeEnd,proto3" json:"privilege_end,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,6,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConditionValue) Reset()         { *m = ConditionValue{} }
func (m *ConditionValue) String() string { return proto.CompactTextString(m) }
func (*ConditionValue) ProtoMessage()    {}
func (*ConditionValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_privilege_api_7dca5731ede7259c, []int{0}
}
func (m *ConditionValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConditionValue.Unmarshal(m, b)
}
func (m *ConditionValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConditionValue.Marshal(b, m, deterministic)
}
func (dst *ConditionValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConditionValue.Merge(dst, src)
}
func (m *ConditionValue) XXX_Size() int {
	return xxx_messageInfo_ConditionValue.Size(m)
}
func (m *ConditionValue) XXX_DiscardUnknown() {
	xxx_messageInfo_ConditionValue.DiscardUnknown(m)
}

var xxx_messageInfo_ConditionValue proto.InternalMessageInfo

func (m *ConditionValue) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *ConditionValue) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ConditionValue) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ConditionValue) GetPrivilegeSeconds() uint32 {
	if m != nil {
		return m.PrivilegeSeconds
	}
	return 0
}

func (m *ConditionValue) GetPrivilegeEnd() uint32 {
	if m != nil {
		return m.PrivilegeEnd
	}
	return 0
}

func (m *ConditionValue) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type Condition struct {
	Type                 ConditionType   `protobuf:"varint,1,opt,name=type,proto3,enum=apicentergo.ConditionType" json:"type,omitempty"`
	Value                *ConditionValue `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *Condition) Reset()         { *m = Condition{} }
func (m *Condition) String() string { return proto.CompactTextString(m) }
func (*Condition) ProtoMessage()    {}
func (*Condition) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_privilege_api_7dca5731ede7259c, []int{1}
}
func (m *Condition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Condition.Unmarshal(m, b)
}
func (m *Condition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Condition.Marshal(b, m, deterministic)
}
func (dst *Condition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Condition.Merge(dst, src)
}
func (m *Condition) XXX_Size() int {
	return xxx_messageInfo_Condition.Size(m)
}
func (m *Condition) XXX_DiscardUnknown() {
	xxx_messageInfo_Condition.DiscardUnknown(m)
}

var xxx_messageInfo_Condition proto.InternalMessageInfo

func (m *Condition) GetType() ConditionType {
	if m != nil {
		return m.Type
	}
	return ConditionType_CONDITION_TYPE_NORMAL
}

func (m *Condition) GetValue() *ConditionValue {
	if m != nil {
		return m.Value
	}
	return nil
}

// 后台礼物权限发放
type TreasurePrivilege struct {
	Id                   uint32       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GiftId               uint32       `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Condition            []*Condition `protobuf:"bytes,3,rep,name=condition,proto3" json:"condition,omitempty"`
	UpdateTime           uint32       `protobuf:"varint,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TreasurePrivilege) Reset()         { *m = TreasurePrivilege{} }
func (m *TreasurePrivilege) String() string { return proto.CompactTextString(m) }
func (*TreasurePrivilege) ProtoMessage()    {}
func (*TreasurePrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_privilege_api_7dca5731ede7259c, []int{2}
}
func (m *TreasurePrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TreasurePrivilege.Unmarshal(m, b)
}
func (m *TreasurePrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TreasurePrivilege.Marshal(b, m, deterministic)
}
func (dst *TreasurePrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TreasurePrivilege.Merge(dst, src)
}
func (m *TreasurePrivilege) XXX_Size() int {
	return xxx_messageInfo_TreasurePrivilege.Size(m)
}
func (m *TreasurePrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_TreasurePrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_TreasurePrivilege proto.InternalMessageInfo

func (m *TreasurePrivilege) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TreasurePrivilege) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *TreasurePrivilege) GetCondition() []*Condition {
	if m != nil {
		return m.Condition
	}
	return nil
}

func (m *TreasurePrivilege) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type AddTreasurePrivilegeReq struct {
	OrderId              string             `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	UidList              []uint32           `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Privilege            *TreasurePrivilege `protobuf:"bytes,3,opt,name=privilege,proto3" json:"privilege,omitempty"`
	EndTime              uint32             `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	EndTimeRel           uint32             `protobuf:"varint,5,opt,name=end_time_rel,json=endTimeRel,proto3" json:"end_time_rel,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddTreasurePrivilegeReq) Reset()         { *m = AddTreasurePrivilegeReq{} }
func (m *AddTreasurePrivilegeReq) String() string { return proto.CompactTextString(m) }
func (*AddTreasurePrivilegeReq) ProtoMessage()    {}
func (*AddTreasurePrivilegeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_privilege_api_7dca5731ede7259c, []int{3}
}
func (m *AddTreasurePrivilegeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTreasurePrivilegeReq.Unmarshal(m, b)
}
func (m *AddTreasurePrivilegeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTreasurePrivilegeReq.Marshal(b, m, deterministic)
}
func (dst *AddTreasurePrivilegeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTreasurePrivilegeReq.Merge(dst, src)
}
func (m *AddTreasurePrivilegeReq) XXX_Size() int {
	return xxx_messageInfo_AddTreasurePrivilegeReq.Size(m)
}
func (m *AddTreasurePrivilegeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTreasurePrivilegeReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddTreasurePrivilegeReq proto.InternalMessageInfo

func (m *AddTreasurePrivilegeReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AddTreasurePrivilegeReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *AddTreasurePrivilegeReq) GetPrivilege() *TreasurePrivilege {
	if m != nil {
		return m.Privilege
	}
	return nil
}

func (m *AddTreasurePrivilegeReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *AddTreasurePrivilegeReq) GetEndTimeRel() uint32 {
	if m != nil {
		return m.EndTimeRel
	}
	return 0
}

type AddTreasurePrivilegeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTreasurePrivilegeResp) Reset()         { *m = AddTreasurePrivilegeResp{} }
func (m *AddTreasurePrivilegeResp) String() string { return proto.CompactTextString(m) }
func (*AddTreasurePrivilegeResp) ProtoMessage()    {}
func (*AddTreasurePrivilegeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_privilege_api_7dca5731ede7259c, []int{4}
}
func (m *AddTreasurePrivilegeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTreasurePrivilegeResp.Unmarshal(m, b)
}
func (m *AddTreasurePrivilegeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTreasurePrivilegeResp.Marshal(b, m, deterministic)
}
func (dst *AddTreasurePrivilegeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTreasurePrivilegeResp.Merge(dst, src)
}
func (m *AddTreasurePrivilegeResp) XXX_Size() int {
	return xxx_messageInfo_AddTreasurePrivilegeResp.Size(m)
}
func (m *AddTreasurePrivilegeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTreasurePrivilegeResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddTreasurePrivilegeResp proto.InternalMessageInfo

type CheckTreasurePrivilegeReq struct {
	Privilege            *TreasurePrivilege `protobuf:"bytes,1,opt,name=privilege,proto3" json:"privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CheckTreasurePrivilegeReq) Reset()         { *m = CheckTreasurePrivilegeReq{} }
func (m *CheckTreasurePrivilegeReq) String() string { return proto.CompactTextString(m) }
func (*CheckTreasurePrivilegeReq) ProtoMessage()    {}
func (*CheckTreasurePrivilegeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_privilege_api_7dca5731ede7259c, []int{5}
}
func (m *CheckTreasurePrivilegeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTreasurePrivilegeReq.Unmarshal(m, b)
}
func (m *CheckTreasurePrivilegeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTreasurePrivilegeReq.Marshal(b, m, deterministic)
}
func (dst *CheckTreasurePrivilegeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTreasurePrivilegeReq.Merge(dst, src)
}
func (m *CheckTreasurePrivilegeReq) XXX_Size() int {
	return xxx_messageInfo_CheckTreasurePrivilegeReq.Size(m)
}
func (m *CheckTreasurePrivilegeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTreasurePrivilegeReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTreasurePrivilegeReq proto.InternalMessageInfo

func (m *CheckTreasurePrivilegeReq) GetPrivilege() *TreasurePrivilege {
	if m != nil {
		return m.Privilege
	}
	return nil
}

type CheckTreasurePrivilegeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckTreasurePrivilegeResp) Reset()         { *m = CheckTreasurePrivilegeResp{} }
func (m *CheckTreasurePrivilegeResp) String() string { return proto.CompactTextString(m) }
func (*CheckTreasurePrivilegeResp) ProtoMessage()    {}
func (*CheckTreasurePrivilegeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_privilege_api_7dca5731ede7259c, []int{6}
}
func (m *CheckTreasurePrivilegeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTreasurePrivilegeResp.Unmarshal(m, b)
}
func (m *CheckTreasurePrivilegeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTreasurePrivilegeResp.Marshal(b, m, deterministic)
}
func (dst *CheckTreasurePrivilegeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTreasurePrivilegeResp.Merge(dst, src)
}
func (m *CheckTreasurePrivilegeResp) XXX_Size() int {
	return xxx_messageInfo_CheckTreasurePrivilegeResp.Size(m)
}
func (m *CheckTreasurePrivilegeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTreasurePrivilegeResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTreasurePrivilegeResp proto.InternalMessageInfo

// 取权限列表接口
type GetTreasurePrivilegeListReq struct {
	Off                  uint32   `protobuf:"varint,1,opt,name=off,proto3" json:"off,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTreasurePrivilegeListReq) Reset()         { *m = GetTreasurePrivilegeListReq{} }
func (m *GetTreasurePrivilegeListReq) String() string { return proto.CompactTextString(m) }
func (*GetTreasurePrivilegeListReq) ProtoMessage()    {}
func (*GetTreasurePrivilegeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_privilege_api_7dca5731ede7259c, []int{7}
}
func (m *GetTreasurePrivilegeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTreasurePrivilegeListReq.Unmarshal(m, b)
}
func (m *GetTreasurePrivilegeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTreasurePrivilegeListReq.Marshal(b, m, deterministic)
}
func (dst *GetTreasurePrivilegeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTreasurePrivilegeListReq.Merge(dst, src)
}
func (m *GetTreasurePrivilegeListReq) XXX_Size() int {
	return xxx_messageInfo_GetTreasurePrivilegeListReq.Size(m)
}
func (m *GetTreasurePrivilegeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTreasurePrivilegeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTreasurePrivilegeListReq proto.InternalMessageInfo

func (m *GetTreasurePrivilegeListReq) GetOff() uint32 {
	if m != nil {
		return m.Off
	}
	return 0
}

func (m *GetTreasurePrivilegeListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetTreasurePrivilegeListResp struct {
	PrivilegeList        []*TreasurePrivilege `protobuf:"bytes,1,rep,name=privilege_list,json=privilegeList,proto3" json:"privilege_list,omitempty"`
	Total                uint32               `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetTreasurePrivilegeListResp) Reset()         { *m = GetTreasurePrivilegeListResp{} }
func (m *GetTreasurePrivilegeListResp) String() string { return proto.CompactTextString(m) }
func (*GetTreasurePrivilegeListResp) ProtoMessage()    {}
func (*GetTreasurePrivilegeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_privilege_api_7dca5731ede7259c, []int{8}
}
func (m *GetTreasurePrivilegeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTreasurePrivilegeListResp.Unmarshal(m, b)
}
func (m *GetTreasurePrivilegeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTreasurePrivilegeListResp.Marshal(b, m, deterministic)
}
func (dst *GetTreasurePrivilegeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTreasurePrivilegeListResp.Merge(dst, src)
}
func (m *GetTreasurePrivilegeListResp) XXX_Size() int {
	return xxx_messageInfo_GetTreasurePrivilegeListResp.Size(m)
}
func (m *GetTreasurePrivilegeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTreasurePrivilegeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTreasurePrivilegeListResp proto.InternalMessageInfo

func (m *GetTreasurePrivilegeListResp) GetPrivilegeList() []*TreasurePrivilege {
	if m != nil {
		return m.PrivilegeList
	}
	return nil
}

func (m *GetTreasurePrivilegeListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 删除权限发放配置接口
type DelTreasurePrivilegeListReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTreasurePrivilegeListReq) Reset()         { *m = DelTreasurePrivilegeListReq{} }
func (m *DelTreasurePrivilegeListReq) String() string { return proto.CompactTextString(m) }
func (*DelTreasurePrivilegeListReq) ProtoMessage()    {}
func (*DelTreasurePrivilegeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_privilege_api_7dca5731ede7259c, []int{9}
}
func (m *DelTreasurePrivilegeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTreasurePrivilegeListReq.Unmarshal(m, b)
}
func (m *DelTreasurePrivilegeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTreasurePrivilegeListReq.Marshal(b, m, deterministic)
}
func (dst *DelTreasurePrivilegeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTreasurePrivilegeListReq.Merge(dst, src)
}
func (m *DelTreasurePrivilegeListReq) XXX_Size() int {
	return xxx_messageInfo_DelTreasurePrivilegeListReq.Size(m)
}
func (m *DelTreasurePrivilegeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTreasurePrivilegeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelTreasurePrivilegeListReq proto.InternalMessageInfo

func (m *DelTreasurePrivilegeListReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelTreasurePrivilegeListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTreasurePrivilegeListResp) Reset()         { *m = DelTreasurePrivilegeListResp{} }
func (m *DelTreasurePrivilegeListResp) String() string { return proto.CompactTextString(m) }
func (*DelTreasurePrivilegeListResp) ProtoMessage()    {}
func (*DelTreasurePrivilegeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_privilege_api_7dca5731ede7259c, []int{10}
}
func (m *DelTreasurePrivilegeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTreasurePrivilegeListResp.Unmarshal(m, b)
}
func (m *DelTreasurePrivilegeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTreasurePrivilegeListResp.Marshal(b, m, deterministic)
}
func (dst *DelTreasurePrivilegeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTreasurePrivilegeListResp.Merge(dst, src)
}
func (m *DelTreasurePrivilegeListResp) XXX_Size() int {
	return xxx_messageInfo_DelTreasurePrivilegeListResp.Size(m)
}
func (m *DelTreasurePrivilegeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTreasurePrivilegeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelTreasurePrivilegeListResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ConditionValue)(nil), "apicentergo.ConditionValue")
	proto.RegisterType((*Condition)(nil), "apicentergo.Condition")
	proto.RegisterType((*TreasurePrivilege)(nil), "apicentergo.TreasurePrivilege")
	proto.RegisterType((*AddTreasurePrivilegeReq)(nil), "apicentergo.AddTreasurePrivilegeReq")
	proto.RegisterType((*AddTreasurePrivilegeResp)(nil), "apicentergo.AddTreasurePrivilegeResp")
	proto.RegisterType((*CheckTreasurePrivilegeReq)(nil), "apicentergo.CheckTreasurePrivilegeReq")
	proto.RegisterType((*CheckTreasurePrivilegeResp)(nil), "apicentergo.CheckTreasurePrivilegeResp")
	proto.RegisterType((*GetTreasurePrivilegeListReq)(nil), "apicentergo.GetTreasurePrivilegeListReq")
	proto.RegisterType((*GetTreasurePrivilegeListResp)(nil), "apicentergo.GetTreasurePrivilegeListResp")
	proto.RegisterType((*DelTreasurePrivilegeListReq)(nil), "apicentergo.DelTreasurePrivilegeListReq")
	proto.RegisterType((*DelTreasurePrivilegeListResp)(nil), "apicentergo.DelTreasurePrivilegeListResp")
	proto.RegisterEnum("apicentergo.ConditionType", ConditionType_name, ConditionType_value)
}

func init() {
	proto.RegisterFile("apicenter-go/treasure-privilege-api.proto", fileDescriptor_treasure_privilege_api_7dca5731ede7259c)
}

var fileDescriptor_treasure_privilege_api_7dca5731ede7259c = []byte{
	// 663 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x54, 0x4d, 0x6f, 0xd3, 0x40,
	0x10, 0xc5, 0xf9, 0x68, 0xc9, 0xa4, 0x89, 0xd2, 0x55, 0xa1, 0x4e, 0x93, 0x96, 0x60, 0x2e, 0x05,
	0x94, 0x44, 0x04, 0xb8, 0x71, 0x69, 0x53, 0xd3, 0x5a, 0x4a, 0x93, 0xca, 0x35, 0x95, 0xca, 0xc5,
	0x72, 0xbd, 0x53, 0xb3, 0xc2, 0xb1, 0x8d, 0xbd, 0xa9, 0xa8, 0xf8, 0x19, 0xfc, 0x25, 0x4e, 0x5c,
	0xf9, 0x43, 0xc8, 0x6b, 0xc7, 0x69, 0xbe, 0x84, 0xb8, 0x65, 0xe6, 0x3d, 0xcf, 0xcc, 0x7b, 0x33,
	0x1b, 0x78, 0x69, 0x05, 0xcc, 0x46, 0x8f, 0x63, 0xd8, 0x76, 0xfc, 0x2e, 0x0f, 0xd1, 0x8a, 0x26,
	0x21, 0xb6, 0x83, 0x90, 0xdd, 0x31, 0x17, 0x1d, 0x6c, 0x5b, 0x01, 0xeb, 0x04, 0xa1, 0xcf, 0x7d,
	0x52, 0xce, 0xa8, 0x8e, 0xaf, 0xfc, 0x91, 0xa0, 0xda, 0xf7, 0x3d, 0xca, 0x38, 0xf3, 0xbd, 0x2b,
	0xcb, 0x9d, 0x20, 0xd9, 0x81, 0xe2, 0x5d, 0xfc, 0x43, 0x96, 0x5a, 0xd2, 0x61, 0x41, 0x4f, 0x02,
	0xb2, 0x0f, 0x70, 0x83, 0x0e, 0xf3, 0x4c, 0xce, 0xc6, 0x28, 0xe7, 0x5a, 0xd2, 0x61, 0x45, 0x2f,
	0x89, 0x8c, 0xc1, 0xc6, 0x48, 0xea, 0xf0, 0x18, 0x3d, 0x9a, 0x80, 0x79, 0x01, 0x6e, 0xa2, 0x47,
	0x05, 0xf4, 0x1a, 0xb6, 0xb3, 0x31, 0xcc, 0x08, 0x6d, 0xdf, 0xa3, 0x91, 0x5c, 0x10, 0x9c, 0x5a,
	0x06, 0x5c, 0x26, 0x79, 0xf2, 0x02, 0x2a, 0x33, 0x32, 0x7a, 0x54, 0x2e, 0x0a, 0xe2, 0x56, 0x96,
	0x54, 0x3d, 0x4a, 0x9e, 0x41, 0x19, 0xbf, 0x07, 0x2c, 0xc4, 0xa4, 0xdf, 0x86, 0xa0, 0x40, 0x92,
	0x8a, 0x5b, 0x2a, 0x1e, 0x94, 0x32, 0x51, 0xa4, 0x03, 0x05, 0x7e, 0x1f, 0x24, 0x72, 0xaa, 0xbd,
	0xbd, 0xce, 0x03, 0xf9, 0x9d, 0x8c, 0x65, 0xdc, 0x07, 0xa8, 0x0b, 0x1e, 0x79, 0x33, 0xd5, 0x1f,
	0x8b, 0x2c, 0xf7, 0x1a, 0xab, 0x3f, 0x10, 0x5e, 0xa5, 0xe6, 0x28, 0x3f, 0x25, 0xd8, 0x36, 0x52,
	0xcf, 0x2f, 0xa6, 0x93, 0x92, 0x2a, 0xe4, 0x18, 0x15, 0x6d, 0x2b, 0x7a, 0x8e, 0x51, 0xb2, 0x0b,
	0x9b, 0x0e, 0xbb, 0xe5, 0x26, 0xa3, 0xa9, 0x7f, 0x1b, 0x71, 0xa8, 0x51, 0xf2, 0x0e, 0x4a, 0xf6,
	0xb4, 0xae, 0x9c, 0x6f, 0xe5, 0x0f, 0xcb, 0xbd, 0xa7, 0xab, 0xbb, 0xea, 0x33, 0x62, 0xec, 0xc2,
	0x24, 0xa0, 0x16, 0x4f, 0x5d, 0x48, 0x1c, 0x85, 0x24, 0x25, 0x5c, 0xf8, 0x2d, 0xc1, 0xee, 0x11,
	0xa5, 0x4b, 0x83, 0xe9, 0xf8, 0x2d, 0xde, 0x97, 0x1f, 0x52, 0x0c, 0xcd, 0x74, 0xc2, 0x92, 0xbe,
	0x29, 0x62, 0x8d, 0xc6, 0xd0, 0x84, 0x51, 0xd3, 0x65, 0x11, 0x97, 0x73, 0xad, 0x7c, 0xbc, 0xca,
	0x09, 0xa3, 0x03, 0x16, 0x71, 0xf2, 0x01, 0x4a, 0xd9, 0x22, 0xc4, 0x9a, 0xcb, 0xbd, 0x83, 0xb9,
	0x41, 0x97, 0x7b, 0xcd, 0x3e, 0x98, 0xbb, 0x91, 0xc2, 0xfc, 0x8d, 0xb4, 0x60, 0x6b, 0x0a, 0x99,
	0x21, 0xba, 0xe9, 0xd6, 0x21, 0x85, 0x75, 0x74, 0x95, 0x3d, 0x90, 0x57, 0x6b, 0x89, 0x02, 0xe5,
	0x1a, 0xea, 0xfd, 0x2f, 0x68, 0x7f, 0x5d, 0xa9, 0x74, 0x6e, 0x66, 0xe9, 0x3f, 0x67, 0x56, 0x9a,
	0xb0, 0xb7, 0xae, 0x74, 0x14, 0x28, 0x2a, 0x34, 0x4e, 0x91, 0x2f, 0x61, 0xb1, 0x57, 0x71, 0xeb,
	0x1a, 0xe4, 0xfd, 0xdb, 0xdb, 0xf4, 0x02, 0xe2, 0x9f, 0xf1, 0xdb, 0x72, 0xd9, 0x98, 0xf1, 0xf4,
	0x00, 0x92, 0x40, 0xf9, 0x01, 0xcd, 0xf5, 0x65, 0xa2, 0x80, 0xa8, 0x50, 0x9d, 0x3d, 0x0a, 0xb1,
	0x17, 0x49, 0x1c, 0xc9, 0xbf, 0x74, 0xcc, 0x9e, 0x92, 0xd8, 0xde, 0x0e, 0x14, 0xb9, 0xcf, 0x2d,
	0x77, 0xda, 0x5c, 0x04, 0x4a, 0x1b, 0x1a, 0x27, 0xe8, 0xae, 0xd5, 0xb0, 0x70, 0xc4, 0xca, 0x01,
	0x34, 0xd7, 0xd3, 0xa3, 0xe0, 0xd5, 0x2f, 0x09, 0x2a, 0x73, 0xaf, 0x8a, 0xd4, 0xe1, 0x49, 0x7f,
	0x34, 0x3c, 0xd1, 0x0c, 0x6d, 0x34, 0x34, 0x8d, 0xeb, 0x0b, 0xd5, 0x1c, 0x8e, 0xf4, 0xf3, 0xa3,
	0x41, 0xed, 0x11, 0x69, 0xc0, 0xee, 0x02, 0xa4, 0xab, 0xfd, 0xb3, 0x23, 0xfd, 0x54, 0xad, 0x49,
	0xa4, 0x09, 0xf2, 0x02, 0x78, 0xa9, 0x0e, 0x4f, 0xcc, 0x53, 0xed, 0xa3, 0x51, 0xcb, 0x91, 0xe7,
	0xb0, 0xbf, 0x54, 0xf5, 0x58, 0x1b, 0x68, 0xc6, 0xb5, 0x39, 0x50, 0xaf, 0xd4, 0x41, 0x2d, 0x4f,
	0xf6, 0xa1, 0xbe, 0x58, 0x5d, 0xeb, 0x9f, 0xa5, 0x70, 0x61, 0x45, 0xf3, 0xfe, 0x68, 0x78, 0xf9,
	0xe9, 0x5c, 0xd5, 0x6b, 0xc5, 0xe3, 0xee, 0xe7, 0xb6, 0xe3, 0xbb, 0x96, 0xe7, 0x74, 0xde, 0xf7,
	0x38, 0xef, 0xd8, 0xfe, 0xb8, 0x2b, 0xfe, 0x3d, 0x6d, 0xdf, 0xed, 0x46, 0x18, 0xde, 0x31, 0x1b,
	0xa3, 0xee, 0x03, 0xf7, 0x6f, 0x36, 0x04, 0xfc, 0xf6, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x26,
	0xcc, 0x8b, 0xf2, 0x89, 0x05, 0x00, 0x00,
}
