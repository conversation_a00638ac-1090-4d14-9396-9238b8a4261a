// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/present-extra-api.proto

package apicentergo // import "golang.52tt.com/protocol/services/apicentergo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EffectStatus int32

const (
	EffectStatus_EffectStatus_Unknown   EffectStatus = 0
	EffectStatus_EffectStatus_Waiting   EffectStatus = 1
	EffectStatus_EffectStatus_Effective EffectStatus = 2
	EffectStatus_EffectStatus_Outdated  EffectStatus = 3
)

var EffectStatus_name = map[int32]string{
	0: "EffectStatus_Unknown",
	1: "EffectStatus_Waiting",
	2: "EffectStatus_Effective",
	3: "EffectStatus_Outdated",
}
var EffectStatus_value = map[string]int32{
	"EffectStatus_Unknown":   0,
	"EffectStatus_Waiting":   1,
	"EffectStatus_Effective": 2,
	"EffectStatus_Outdated":  3,
}

func (x EffectStatus) String() string {
	return proto.EnumName(EffectStatus_name, int32(x))
}
func (EffectStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{0}
}

// 获取已有礼物浮层的礼物列表
type SearchPresentReq struct {
	KeyId                uint32   `protobuf:"varint,1,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	KeyName              string   `protobuf:"bytes,2,opt,name=key_name,json=keyName,proto3" json:"key_name,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchPresentReq) Reset()         { *m = SearchPresentReq{} }
func (m *SearchPresentReq) String() string { return proto.CompactTextString(m) }
func (*SearchPresentReq) ProtoMessage()    {}
func (*SearchPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{0}
}
func (m *SearchPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchPresentReq.Unmarshal(m, b)
}
func (m *SearchPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchPresentReq.Marshal(b, m, deterministic)
}
func (dst *SearchPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchPresentReq.Merge(dst, src)
}
func (m *SearchPresentReq) XXX_Size() int {
	return xxx_messageInfo_SearchPresentReq.Size(m)
}
func (m *SearchPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchPresentReq proto.InternalMessageInfo

func (m *SearchPresentReq) GetKeyId() uint32 {
	if m != nil {
		return m.KeyId
	}
	return 0
}

func (m *SearchPresentReq) GetKeyName() string {
	if m != nil {
		return m.KeyName
	}
	return ""
}

func (m *SearchPresentReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *SearchPresentReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type SearchPresentResp struct {
	PresentConfigs       []*PresentBaseConfig `protobuf:"bytes,1,rep,name=present_configs,json=presentConfigs,proto3" json:"present_configs,omitempty"`
	Total                uint32               `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SearchPresentResp) Reset()         { *m = SearchPresentResp{} }
func (m *SearchPresentResp) String() string { return proto.CompactTextString(m) }
func (*SearchPresentResp) ProtoMessage()    {}
func (*SearchPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{1}
}
func (m *SearchPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchPresentResp.Unmarshal(m, b)
}
func (m *SearchPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchPresentResp.Marshal(b, m, deterministic)
}
func (dst *SearchPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchPresentResp.Merge(dst, src)
}
func (m *SearchPresentResp) XXX_Size() int {
	return xxx_messageInfo_SearchPresentResp.Size(m)
}
func (m *SearchPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchPresentResp proto.InternalMessageInfo

func (m *SearchPresentResp) GetPresentConfigs() []*PresentBaseConfig {
	if m != nil {
		return m.PresentConfigs
	}
	return nil
}

func (m *SearchPresentResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetPresentFloatLayerReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	KeyId                uint32   `protobuf:"varint,3,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	KeyName              string   `protobuf:"bytes,4,opt,name=key_name,json=keyName,proto3" json:"key_name,omitempty"`
	Status               uint32   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentFloatLayerReq) Reset()         { *m = GetPresentFloatLayerReq{} }
func (m *GetPresentFloatLayerReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentFloatLayerReq) ProtoMessage()    {}
func (*GetPresentFloatLayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{2}
}
func (m *GetPresentFloatLayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentFloatLayerReq.Unmarshal(m, b)
}
func (m *GetPresentFloatLayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentFloatLayerReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentFloatLayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentFloatLayerReq.Merge(dst, src)
}
func (m *GetPresentFloatLayerReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentFloatLayerReq.Size(m)
}
func (m *GetPresentFloatLayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentFloatLayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentFloatLayerReq proto.InternalMessageInfo

func (m *GetPresentFloatLayerReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPresentFloatLayerReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetPresentFloatLayerReq) GetKeyId() uint32 {
	if m != nil {
		return m.KeyId
	}
	return 0
}

func (m *GetPresentFloatLayerReq) GetKeyName() string {
	if m != nil {
		return m.KeyName
	}
	return ""
}

func (m *GetPresentFloatLayerReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetPresentFloatLayerResp struct {
	LayerInfos           []*PresentFloatInfo `protobuf:"bytes,1,rep,name=layer_infos,json=layerInfos,proto3" json:"layer_infos,omitempty"`
	Total                uint32              `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetPresentFloatLayerResp) Reset()         { *m = GetPresentFloatLayerResp{} }
func (m *GetPresentFloatLayerResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentFloatLayerResp) ProtoMessage()    {}
func (*GetPresentFloatLayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{3}
}
func (m *GetPresentFloatLayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentFloatLayerResp.Unmarshal(m, b)
}
func (m *GetPresentFloatLayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentFloatLayerResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentFloatLayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentFloatLayerResp.Merge(dst, src)
}
func (m *GetPresentFloatLayerResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentFloatLayerResp.Size(m)
}
func (m *GetPresentFloatLayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentFloatLayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentFloatLayerResp proto.InternalMessageInfo

func (m *GetPresentFloatLayerResp) GetLayerInfos() []*PresentFloatInfo {
	if m != nil {
		return m.LayerInfos
	}
	return nil
}

func (m *GetPresentFloatLayerResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 给礼物添加礼物浮层
type AddPresentFloatLayerReq struct {
	Layer                *PresentFloatLayer `protobuf:"bytes,1,opt,name=layer,proto3" json:"layer,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddPresentFloatLayerReq) Reset()         { *m = AddPresentFloatLayerReq{} }
func (m *AddPresentFloatLayerReq) String() string { return proto.CompactTextString(m) }
func (*AddPresentFloatLayerReq) ProtoMessage()    {}
func (*AddPresentFloatLayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{4}
}
func (m *AddPresentFloatLayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentFloatLayerReq.Unmarshal(m, b)
}
func (m *AddPresentFloatLayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentFloatLayerReq.Marshal(b, m, deterministic)
}
func (dst *AddPresentFloatLayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentFloatLayerReq.Merge(dst, src)
}
func (m *AddPresentFloatLayerReq) XXX_Size() int {
	return xxx_messageInfo_AddPresentFloatLayerReq.Size(m)
}
func (m *AddPresentFloatLayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentFloatLayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentFloatLayerReq proto.InternalMessageInfo

func (m *AddPresentFloatLayerReq) GetLayer() *PresentFloatLayer {
	if m != nil {
		return m.Layer
	}
	return nil
}

type AddPresentFloatLayerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPresentFloatLayerResp) Reset()         { *m = AddPresentFloatLayerResp{} }
func (m *AddPresentFloatLayerResp) String() string { return proto.CompactTextString(m) }
func (*AddPresentFloatLayerResp) ProtoMessage()    {}
func (*AddPresentFloatLayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{5}
}
func (m *AddPresentFloatLayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentFloatLayerResp.Unmarshal(m, b)
}
func (m *AddPresentFloatLayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentFloatLayerResp.Marshal(b, m, deterministic)
}
func (dst *AddPresentFloatLayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentFloatLayerResp.Merge(dst, src)
}
func (m *AddPresentFloatLayerResp) XXX_Size() int {
	return xxx_messageInfo_AddPresentFloatLayerResp.Size(m)
}
func (m *AddPresentFloatLayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentFloatLayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentFloatLayerResp proto.InternalMessageInfo

// 更新礼物的礼物浮层
type UpdatePresentFloatLayerReq struct {
	Layer                *PresentFloatLayer `protobuf:"bytes,1,opt,name=layer,proto3" json:"layer,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdatePresentFloatLayerReq) Reset()         { *m = UpdatePresentFloatLayerReq{} }
func (m *UpdatePresentFloatLayerReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentFloatLayerReq) ProtoMessage()    {}
func (*UpdatePresentFloatLayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{6}
}
func (m *UpdatePresentFloatLayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentFloatLayerReq.Unmarshal(m, b)
}
func (m *UpdatePresentFloatLayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentFloatLayerReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentFloatLayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentFloatLayerReq.Merge(dst, src)
}
func (m *UpdatePresentFloatLayerReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentFloatLayerReq.Size(m)
}
func (m *UpdatePresentFloatLayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentFloatLayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentFloatLayerReq proto.InternalMessageInfo

func (m *UpdatePresentFloatLayerReq) GetLayer() *PresentFloatLayer {
	if m != nil {
		return m.Layer
	}
	return nil
}

type UpdatePresentFloatLayerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePresentFloatLayerResp) Reset()         { *m = UpdatePresentFloatLayerResp{} }
func (m *UpdatePresentFloatLayerResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentFloatLayerResp) ProtoMessage()    {}
func (*UpdatePresentFloatLayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{7}
}
func (m *UpdatePresentFloatLayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentFloatLayerResp.Unmarshal(m, b)
}
func (m *UpdatePresentFloatLayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentFloatLayerResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentFloatLayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentFloatLayerResp.Merge(dst, src)
}
func (m *UpdatePresentFloatLayerResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentFloatLayerResp.Size(m)
}
func (m *UpdatePresentFloatLayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentFloatLayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentFloatLayerResp proto.InternalMessageInfo

// 删除礼物的礼物浮层
type DelPresentFloatLayerReq struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentFloatLayerReq) Reset()         { *m = DelPresentFloatLayerReq{} }
func (m *DelPresentFloatLayerReq) String() string { return proto.CompactTextString(m) }
func (*DelPresentFloatLayerReq) ProtoMessage()    {}
func (*DelPresentFloatLayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{8}
}
func (m *DelPresentFloatLayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentFloatLayerReq.Unmarshal(m, b)
}
func (m *DelPresentFloatLayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentFloatLayerReq.Marshal(b, m, deterministic)
}
func (dst *DelPresentFloatLayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentFloatLayerReq.Merge(dst, src)
}
func (m *DelPresentFloatLayerReq) XXX_Size() int {
	return xxx_messageInfo_DelPresentFloatLayerReq.Size(m)
}
func (m *DelPresentFloatLayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentFloatLayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentFloatLayerReq proto.InternalMessageInfo

func (m *DelPresentFloatLayerReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type DelPresentFloatLayerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentFloatLayerResp) Reset()         { *m = DelPresentFloatLayerResp{} }
func (m *DelPresentFloatLayerResp) String() string { return proto.CompactTextString(m) }
func (*DelPresentFloatLayerResp) ProtoMessage()    {}
func (*DelPresentFloatLayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{9}
}
func (m *DelPresentFloatLayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentFloatLayerResp.Unmarshal(m, b)
}
func (m *DelPresentFloatLayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentFloatLayerResp.Marshal(b, m, deterministic)
}
func (dst *DelPresentFloatLayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentFloatLayerResp.Merge(dst, src)
}
func (m *DelPresentFloatLayerResp) XXX_Size() int {
	return xxx_messageInfo_DelPresentFloatLayerResp.Size(m)
}
func (m *DelPresentFloatLayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentFloatLayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentFloatLayerResp proto.InternalMessageInfo

type PresentFloatInfo struct {
	LayerInfo            *PresentFloatLayer `protobuf:"bytes,1,opt,name=layer_info,json=layerInfo,proto3" json:"layer_info,omitempty"`
	PresentConfig        *PresentBaseConfig `protobuf:"bytes,2,opt,name=present_config,json=presentConfig,proto3" json:"present_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PresentFloatInfo) Reset()         { *m = PresentFloatInfo{} }
func (m *PresentFloatInfo) String() string { return proto.CompactTextString(m) }
func (*PresentFloatInfo) ProtoMessage()    {}
func (*PresentFloatInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{10}
}
func (m *PresentFloatInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentFloatInfo.Unmarshal(m, b)
}
func (m *PresentFloatInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentFloatInfo.Marshal(b, m, deterministic)
}
func (dst *PresentFloatInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentFloatInfo.Merge(dst, src)
}
func (m *PresentFloatInfo) XXX_Size() int {
	return xxx_messageInfo_PresentFloatInfo.Size(m)
}
func (m *PresentFloatInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentFloatInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentFloatInfo proto.InternalMessageInfo

func (m *PresentFloatInfo) GetLayerInfo() *PresentFloatLayer {
	if m != nil {
		return m.LayerInfo
	}
	return nil
}

func (m *PresentFloatInfo) GetPresentConfig() *PresentBaseConfig {
	if m != nil {
		return m.PresentConfig
	}
	return nil
}

type PresentFloatLayer struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	FloatImageUrl        string   `protobuf:"bytes,2,opt,name=float_image_url,json=floatImageUrl,proto3" json:"float_image_url,omitempty"`
	JumpUrl              string   `protobuf:"bytes,3,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	IsActivityUrl        bool     `protobuf:"varint,4,opt,name=is_activity_url,json=isActivityUrl,proto3" json:"is_activity_url,omitempty"`
	EffectBegin          uint32   `protobuf:"varint,5,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32   `protobuf:"varint,6,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	Operator             string   `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	EffectStatus         uint32   `protobuf:"varint,8,opt,name=effect_status,json=effectStatus,proto3" json:"effect_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentFloatLayer) Reset()         { *m = PresentFloatLayer{} }
func (m *PresentFloatLayer) String() string { return proto.CompactTextString(m) }
func (*PresentFloatLayer) ProtoMessage()    {}
func (*PresentFloatLayer) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{11}
}
func (m *PresentFloatLayer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentFloatLayer.Unmarshal(m, b)
}
func (m *PresentFloatLayer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentFloatLayer.Marshal(b, m, deterministic)
}
func (dst *PresentFloatLayer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentFloatLayer.Merge(dst, src)
}
func (m *PresentFloatLayer) XXX_Size() int {
	return xxx_messageInfo_PresentFloatLayer.Size(m)
}
func (m *PresentFloatLayer) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentFloatLayer.DiscardUnknown(m)
}

var xxx_messageInfo_PresentFloatLayer proto.InternalMessageInfo

func (m *PresentFloatLayer) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentFloatLayer) GetFloatImageUrl() string {
	if m != nil {
		return m.FloatImageUrl
	}
	return ""
}

func (m *PresentFloatLayer) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *PresentFloatLayer) GetIsActivityUrl() bool {
	if m != nil {
		return m.IsActivityUrl
	}
	return false
}

func (m *PresentFloatLayer) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *PresentFloatLayer) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *PresentFloatLayer) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *PresentFloatLayer) GetEffectStatus() uint32 {
	if m != nil {
		return m.EffectStatus
	}
	return 0
}

type PresentBaseConfig struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftName             string   `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	PriceValue           uint32   `protobuf:"varint,3,opt,name=price_value,json=priceValue,proto3" json:"price_value,omitempty"`
	PriceType            uint32   `protobuf:"varint,4,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	GiftImage            string   `protobuf:"bytes,5,opt,name=gift_image,json=giftImage,proto3" json:"gift_image,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentBaseConfig) Reset()         { *m = PresentBaseConfig{} }
func (m *PresentBaseConfig) String() string { return proto.CompactTextString(m) }
func (*PresentBaseConfig) ProtoMessage()    {}
func (*PresentBaseConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{12}
}
func (m *PresentBaseConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentBaseConfig.Unmarshal(m, b)
}
func (m *PresentBaseConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentBaseConfig.Marshal(b, m, deterministic)
}
func (dst *PresentBaseConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentBaseConfig.Merge(dst, src)
}
func (m *PresentBaseConfig) XXX_Size() int {
	return xxx_messageInfo_PresentBaseConfig.Size(m)
}
func (m *PresentBaseConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentBaseConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PresentBaseConfig proto.InternalMessageInfo

func (m *PresentBaseConfig) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentBaseConfig) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *PresentBaseConfig) GetPriceValue() uint32 {
	if m != nil {
		return m.PriceValue
	}
	return 0
}

func (m *PresentBaseConfig) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *PresentBaseConfig) GetGiftImage() string {
	if m != nil {
		return m.GiftImage
	}
	return ""
}

type PresentFlashEffect struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	FlashId              uint32   `protobuf:"varint,2,opt,name=flash_id,json=flashId,proto3" json:"flash_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentFlashEffect) Reset()         { *m = PresentFlashEffect{} }
func (m *PresentFlashEffect) String() string { return proto.CompactTextString(m) }
func (*PresentFlashEffect) ProtoMessage()    {}
func (*PresentFlashEffect) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{13}
}
func (m *PresentFlashEffect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentFlashEffect.Unmarshal(m, b)
}
func (m *PresentFlashEffect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentFlashEffect.Marshal(b, m, deterministic)
}
func (dst *PresentFlashEffect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentFlashEffect.Merge(dst, src)
}
func (m *PresentFlashEffect) XXX_Size() int {
	return xxx_messageInfo_PresentFlashEffect.Size(m)
}
func (m *PresentFlashEffect) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentFlashEffect.DiscardUnknown(m)
}

var xxx_messageInfo_PresentFlashEffect proto.InternalMessageInfo

func (m *PresentFlashEffect) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentFlashEffect) GetFlashId() uint32 {
	if m != nil {
		return m.FlashId
	}
	return 0
}

type FlashEffectConfig struct {
	FlashId              uint32   `protobuf:"varint,1,opt,name=flash_id,json=flashId,proto3" json:"flash_id,omitempty"`
	FlashName            string   `protobuf:"bytes,2,opt,name=flash_name,json=flashName,proto3" json:"flash_name,omitempty"`
	FlashUrl             string   `protobuf:"bytes,3,opt,name=flash_url,json=flashUrl,proto3" json:"flash_url,omitempty"`
	FlashMd5             string   `protobuf:"bytes,4,opt,name=flash_md5,json=flashMd5,proto3" json:"flash_md5,omitempty"`
	Operator             string   `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	CreateTime           uint32   `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlashEffectConfig) Reset()         { *m = FlashEffectConfig{} }
func (m *FlashEffectConfig) String() string { return proto.CompactTextString(m) }
func (*FlashEffectConfig) ProtoMessage()    {}
func (*FlashEffectConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{14}
}
func (m *FlashEffectConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlashEffectConfig.Unmarshal(m, b)
}
func (m *FlashEffectConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlashEffectConfig.Marshal(b, m, deterministic)
}
func (dst *FlashEffectConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlashEffectConfig.Merge(dst, src)
}
func (m *FlashEffectConfig) XXX_Size() int {
	return xxx_messageInfo_FlashEffectConfig.Size(m)
}
func (m *FlashEffectConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_FlashEffectConfig.DiscardUnknown(m)
}

var xxx_messageInfo_FlashEffectConfig proto.InternalMessageInfo

func (m *FlashEffectConfig) GetFlashId() uint32 {
	if m != nil {
		return m.FlashId
	}
	return 0
}

func (m *FlashEffectConfig) GetFlashName() string {
	if m != nil {
		return m.FlashName
	}
	return ""
}

func (m *FlashEffectConfig) GetFlashUrl() string {
	if m != nil {
		return m.FlashUrl
	}
	return ""
}

func (m *FlashEffectConfig) GetFlashMd5() string {
	if m != nil {
		return m.FlashMd5
	}
	return ""
}

func (m *FlashEffectConfig) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *FlashEffectConfig) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type GetFlashEffectConfigReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	KeyName              string   `protobuf:"bytes,3,opt,name=key_name,json=keyName,proto3" json:"key_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFlashEffectConfigReq) Reset()         { *m = GetFlashEffectConfigReq{} }
func (m *GetFlashEffectConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetFlashEffectConfigReq) ProtoMessage()    {}
func (*GetFlashEffectConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{15}
}
func (m *GetFlashEffectConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlashEffectConfigReq.Unmarshal(m, b)
}
func (m *GetFlashEffectConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlashEffectConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetFlashEffectConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlashEffectConfigReq.Merge(dst, src)
}
func (m *GetFlashEffectConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetFlashEffectConfigReq.Size(m)
}
func (m *GetFlashEffectConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlashEffectConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlashEffectConfigReq proto.InternalMessageInfo

func (m *GetFlashEffectConfigReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetFlashEffectConfigReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetFlashEffectConfigReq) GetKeyName() string {
	if m != nil {
		return m.KeyName
	}
	return ""
}

type GetFlashEffectConfigResp struct {
	FlashEffects         []*FlashEffectConfig `protobuf:"bytes,1,rep,name=flash_effects,json=flashEffects,proto3" json:"flash_effects,omitempty"`
	Total                uint32               `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetFlashEffectConfigResp) Reset()         { *m = GetFlashEffectConfigResp{} }
func (m *GetFlashEffectConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetFlashEffectConfigResp) ProtoMessage()    {}
func (*GetFlashEffectConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{16}
}
func (m *GetFlashEffectConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlashEffectConfigResp.Unmarshal(m, b)
}
func (m *GetFlashEffectConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlashEffectConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetFlashEffectConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlashEffectConfigResp.Merge(dst, src)
}
func (m *GetFlashEffectConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetFlashEffectConfigResp.Size(m)
}
func (m *GetFlashEffectConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlashEffectConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlashEffectConfigResp proto.InternalMessageInfo

func (m *GetFlashEffectConfigResp) GetFlashEffects() []*FlashEffectConfig {
	if m != nil {
		return m.FlashEffects
	}
	return nil
}

func (m *GetFlashEffectConfigResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type AddFlashEffectConfigReq struct {
	FlashEffect          *FlashEffectConfig `protobuf:"bytes,1,opt,name=flash_effect,json=flashEffect,proto3" json:"flash_effect,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddFlashEffectConfigReq) Reset()         { *m = AddFlashEffectConfigReq{} }
func (m *AddFlashEffectConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddFlashEffectConfigReq) ProtoMessage()    {}
func (*AddFlashEffectConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{17}
}
func (m *AddFlashEffectConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFlashEffectConfigReq.Unmarshal(m, b)
}
func (m *AddFlashEffectConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFlashEffectConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddFlashEffectConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFlashEffectConfigReq.Merge(dst, src)
}
func (m *AddFlashEffectConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddFlashEffectConfigReq.Size(m)
}
func (m *AddFlashEffectConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFlashEffectConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddFlashEffectConfigReq proto.InternalMessageInfo

func (m *AddFlashEffectConfigReq) GetFlashEffect() *FlashEffectConfig {
	if m != nil {
		return m.FlashEffect
	}
	return nil
}

type AddFlashEffectConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFlashEffectConfigResp) Reset()         { *m = AddFlashEffectConfigResp{} }
func (m *AddFlashEffectConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddFlashEffectConfigResp) ProtoMessage()    {}
func (*AddFlashEffectConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{18}
}
func (m *AddFlashEffectConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFlashEffectConfigResp.Unmarshal(m, b)
}
func (m *AddFlashEffectConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFlashEffectConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddFlashEffectConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFlashEffectConfigResp.Merge(dst, src)
}
func (m *AddFlashEffectConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddFlashEffectConfigResp.Size(m)
}
func (m *AddFlashEffectConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFlashEffectConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddFlashEffectConfigResp proto.InternalMessageInfo

type UpdateFlashEffectConfigReq struct {
	FlashEffect          *FlashEffectConfig `protobuf:"bytes,1,opt,name=flash_effect,json=flashEffect,proto3" json:"flash_effect,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdateFlashEffectConfigReq) Reset()         { *m = UpdateFlashEffectConfigReq{} }
func (m *UpdateFlashEffectConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateFlashEffectConfigReq) ProtoMessage()    {}
func (*UpdateFlashEffectConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{19}
}
func (m *UpdateFlashEffectConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFlashEffectConfigReq.Unmarshal(m, b)
}
func (m *UpdateFlashEffectConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFlashEffectConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateFlashEffectConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFlashEffectConfigReq.Merge(dst, src)
}
func (m *UpdateFlashEffectConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateFlashEffectConfigReq.Size(m)
}
func (m *UpdateFlashEffectConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFlashEffectConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFlashEffectConfigReq proto.InternalMessageInfo

func (m *UpdateFlashEffectConfigReq) GetFlashEffect() *FlashEffectConfig {
	if m != nil {
		return m.FlashEffect
	}
	return nil
}

type UpdateFlashEffectConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateFlashEffectConfigResp) Reset()         { *m = UpdateFlashEffectConfigResp{} }
func (m *UpdateFlashEffectConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateFlashEffectConfigResp) ProtoMessage()    {}
func (*UpdateFlashEffectConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{20}
}
func (m *UpdateFlashEffectConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFlashEffectConfigResp.Unmarshal(m, b)
}
func (m *UpdateFlashEffectConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFlashEffectConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateFlashEffectConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFlashEffectConfigResp.Merge(dst, src)
}
func (m *UpdateFlashEffectConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateFlashEffectConfigResp.Size(m)
}
func (m *UpdateFlashEffectConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFlashEffectConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFlashEffectConfigResp proto.InternalMessageInfo

type DelFlashEffectConfigReq struct {
	FlashId              uint32   `protobuf:"varint,1,opt,name=flash_id,json=flashId,proto3" json:"flash_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFlashEffectConfigReq) Reset()         { *m = DelFlashEffectConfigReq{} }
func (m *DelFlashEffectConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelFlashEffectConfigReq) ProtoMessage()    {}
func (*DelFlashEffectConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{21}
}
func (m *DelFlashEffectConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFlashEffectConfigReq.Unmarshal(m, b)
}
func (m *DelFlashEffectConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFlashEffectConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelFlashEffectConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFlashEffectConfigReq.Merge(dst, src)
}
func (m *DelFlashEffectConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelFlashEffectConfigReq.Size(m)
}
func (m *DelFlashEffectConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFlashEffectConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelFlashEffectConfigReq proto.InternalMessageInfo

func (m *DelFlashEffectConfigReq) GetFlashId() uint32 {
	if m != nil {
		return m.FlashId
	}
	return 0
}

type DelFlashEffectConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFlashEffectConfigResp) Reset()         { *m = DelFlashEffectConfigResp{} }
func (m *DelFlashEffectConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelFlashEffectConfigResp) ProtoMessage()    {}
func (*DelFlashEffectConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{22}
}
func (m *DelFlashEffectConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFlashEffectConfigResp.Unmarshal(m, b)
}
func (m *DelFlashEffectConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFlashEffectConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelFlashEffectConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFlashEffectConfigResp.Merge(dst, src)
}
func (m *DelFlashEffectConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelFlashEffectConfigResp.Size(m)
}
func (m *DelFlashEffectConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFlashEffectConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelFlashEffectConfigResp proto.InternalMessageInfo

type PresentFlashInfo struct {
	FlashInfo            *FlashEffectConfig `protobuf:"bytes,1,opt,name=flash_info,json=flashInfo,proto3" json:"flash_info,omitempty"`
	PresentConfig        *PresentBaseConfig `protobuf:"bytes,2,opt,name=present_config,json=presentConfig,proto3" json:"present_config,omitempty"`
	EffectBegin          uint32             `protobuf:"varint,3,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32             `protobuf:"varint,4,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	CreateTime           uint32             `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Operator             string             `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
	EffectStatus         uint32             `protobuf:"varint,7,opt,name=effect_status,json=effectStatus,proto3" json:"effect_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PresentFlashInfo) Reset()         { *m = PresentFlashInfo{} }
func (m *PresentFlashInfo) String() string { return proto.CompactTextString(m) }
func (*PresentFlashInfo) ProtoMessage()    {}
func (*PresentFlashInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{23}
}
func (m *PresentFlashInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentFlashInfo.Unmarshal(m, b)
}
func (m *PresentFlashInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentFlashInfo.Marshal(b, m, deterministic)
}
func (dst *PresentFlashInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentFlashInfo.Merge(dst, src)
}
func (m *PresentFlashInfo) XXX_Size() int {
	return xxx_messageInfo_PresentFlashInfo.Size(m)
}
func (m *PresentFlashInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentFlashInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentFlashInfo proto.InternalMessageInfo

func (m *PresentFlashInfo) GetFlashInfo() *FlashEffectConfig {
	if m != nil {
		return m.FlashInfo
	}
	return nil
}

func (m *PresentFlashInfo) GetPresentConfig() *PresentBaseConfig {
	if m != nil {
		return m.PresentConfig
	}
	return nil
}

func (m *PresentFlashInfo) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *PresentFlashInfo) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *PresentFlashInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *PresentFlashInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *PresentFlashInfo) GetEffectStatus() uint32 {
	if m != nil {
		return m.EffectStatus
	}
	return 0
}

type GetPresentFlashEffectReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	KeyEffectName        string   `protobuf:"bytes,3,opt,name=key_effect_name,json=keyEffectName,proto3" json:"key_effect_name,omitempty"`
	KeyGiftId            uint32   `protobuf:"varint,4,opt,name=key_gift_id,json=keyGiftId,proto3" json:"key_gift_id,omitempty"`
	KeyGiftName          string   `protobuf:"bytes,5,opt,name=key_gift_name,json=keyGiftName,proto3" json:"key_gift_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentFlashEffectReq) Reset()         { *m = GetPresentFlashEffectReq{} }
func (m *GetPresentFlashEffectReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentFlashEffectReq) ProtoMessage()    {}
func (*GetPresentFlashEffectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{24}
}
func (m *GetPresentFlashEffectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentFlashEffectReq.Unmarshal(m, b)
}
func (m *GetPresentFlashEffectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentFlashEffectReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentFlashEffectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentFlashEffectReq.Merge(dst, src)
}
func (m *GetPresentFlashEffectReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentFlashEffectReq.Size(m)
}
func (m *GetPresentFlashEffectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentFlashEffectReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentFlashEffectReq proto.InternalMessageInfo

func (m *GetPresentFlashEffectReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPresentFlashEffectReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetPresentFlashEffectReq) GetKeyEffectName() string {
	if m != nil {
		return m.KeyEffectName
	}
	return ""
}

func (m *GetPresentFlashEffectReq) GetKeyGiftId() uint32 {
	if m != nil {
		return m.KeyGiftId
	}
	return 0
}

func (m *GetPresentFlashEffectReq) GetKeyGiftName() string {
	if m != nil {
		return m.KeyGiftName
	}
	return ""
}

type GetPresentFlashEffectResp struct {
	PresentEffects       []*PresentFlashInfo `protobuf:"bytes,1,rep,name=present_effects,json=presentEffects,proto3" json:"present_effects,omitempty"`
	Total                uint32              `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetPresentFlashEffectResp) Reset()         { *m = GetPresentFlashEffectResp{} }
func (m *GetPresentFlashEffectResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentFlashEffectResp) ProtoMessage()    {}
func (*GetPresentFlashEffectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{25}
}
func (m *GetPresentFlashEffectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentFlashEffectResp.Unmarshal(m, b)
}
func (m *GetPresentFlashEffectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentFlashEffectResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentFlashEffectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentFlashEffectResp.Merge(dst, src)
}
func (m *GetPresentFlashEffectResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentFlashEffectResp.Size(m)
}
func (m *GetPresentFlashEffectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentFlashEffectResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentFlashEffectResp proto.InternalMessageInfo

func (m *GetPresentFlashEffectResp) GetPresentEffects() []*PresentFlashInfo {
	if m != nil {
		return m.PresentEffects
	}
	return nil
}

func (m *GetPresentFlashEffectResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 绑定接口，新增/修改/解绑都用这个
type BoundPresentFlashEffectReq struct {
	GiftId               []uint32 `protobuf:"varint,1,rep,packed,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	FlashId              uint32   `protobuf:"varint,2,opt,name=flash_id,json=flashId,proto3" json:"flash_id,omitempty"`
	EffectBegin          uint32   `protobuf:"varint,3,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32   `protobuf:"varint,4,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	CreateTime           uint32   `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Operator             string   `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoundPresentFlashEffectReq) Reset()         { *m = BoundPresentFlashEffectReq{} }
func (m *BoundPresentFlashEffectReq) String() string { return proto.CompactTextString(m) }
func (*BoundPresentFlashEffectReq) ProtoMessage()    {}
func (*BoundPresentFlashEffectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{26}
}
func (m *BoundPresentFlashEffectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoundPresentFlashEffectReq.Unmarshal(m, b)
}
func (m *BoundPresentFlashEffectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoundPresentFlashEffectReq.Marshal(b, m, deterministic)
}
func (dst *BoundPresentFlashEffectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoundPresentFlashEffectReq.Merge(dst, src)
}
func (m *BoundPresentFlashEffectReq) XXX_Size() int {
	return xxx_messageInfo_BoundPresentFlashEffectReq.Size(m)
}
func (m *BoundPresentFlashEffectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BoundPresentFlashEffectReq.DiscardUnknown(m)
}

var xxx_messageInfo_BoundPresentFlashEffectReq proto.InternalMessageInfo

func (m *BoundPresentFlashEffectReq) GetGiftId() []uint32 {
	if m != nil {
		return m.GiftId
	}
	return nil
}

func (m *BoundPresentFlashEffectReq) GetFlashId() uint32 {
	if m != nil {
		return m.FlashId
	}
	return 0
}

func (m *BoundPresentFlashEffectReq) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *BoundPresentFlashEffectReq) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *BoundPresentFlashEffectReq) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *BoundPresentFlashEffectReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type BoundPresentFlashEffectResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoundPresentFlashEffectResp) Reset()         { *m = BoundPresentFlashEffectResp{} }
func (m *BoundPresentFlashEffectResp) String() string { return proto.CompactTextString(m) }
func (*BoundPresentFlashEffectResp) ProtoMessage()    {}
func (*BoundPresentFlashEffectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_api_c53d2bf7f295bb5f, []int{27}
}
func (m *BoundPresentFlashEffectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoundPresentFlashEffectResp.Unmarshal(m, b)
}
func (m *BoundPresentFlashEffectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoundPresentFlashEffectResp.Marshal(b, m, deterministic)
}
func (dst *BoundPresentFlashEffectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoundPresentFlashEffectResp.Merge(dst, src)
}
func (m *BoundPresentFlashEffectResp) XXX_Size() int {
	return xxx_messageInfo_BoundPresentFlashEffectResp.Size(m)
}
func (m *BoundPresentFlashEffectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BoundPresentFlashEffectResp.DiscardUnknown(m)
}

var xxx_messageInfo_BoundPresentFlashEffectResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*SearchPresentReq)(nil), "apicentergo.SearchPresentReq")
	proto.RegisterType((*SearchPresentResp)(nil), "apicentergo.SearchPresentResp")
	proto.RegisterType((*GetPresentFloatLayerReq)(nil), "apicentergo.GetPresentFloatLayerReq")
	proto.RegisterType((*GetPresentFloatLayerResp)(nil), "apicentergo.GetPresentFloatLayerResp")
	proto.RegisterType((*AddPresentFloatLayerReq)(nil), "apicentergo.AddPresentFloatLayerReq")
	proto.RegisterType((*AddPresentFloatLayerResp)(nil), "apicentergo.AddPresentFloatLayerResp")
	proto.RegisterType((*UpdatePresentFloatLayerReq)(nil), "apicentergo.UpdatePresentFloatLayerReq")
	proto.RegisterType((*UpdatePresentFloatLayerResp)(nil), "apicentergo.UpdatePresentFloatLayerResp")
	proto.RegisterType((*DelPresentFloatLayerReq)(nil), "apicentergo.DelPresentFloatLayerReq")
	proto.RegisterType((*DelPresentFloatLayerResp)(nil), "apicentergo.DelPresentFloatLayerResp")
	proto.RegisterType((*PresentFloatInfo)(nil), "apicentergo.PresentFloatInfo")
	proto.RegisterType((*PresentFloatLayer)(nil), "apicentergo.PresentFloatLayer")
	proto.RegisterType((*PresentBaseConfig)(nil), "apicentergo.PresentBaseConfig")
	proto.RegisterType((*PresentFlashEffect)(nil), "apicentergo.PresentFlashEffect")
	proto.RegisterType((*FlashEffectConfig)(nil), "apicentergo.FlashEffectConfig")
	proto.RegisterType((*GetFlashEffectConfigReq)(nil), "apicentergo.GetFlashEffectConfigReq")
	proto.RegisterType((*GetFlashEffectConfigResp)(nil), "apicentergo.GetFlashEffectConfigResp")
	proto.RegisterType((*AddFlashEffectConfigReq)(nil), "apicentergo.AddFlashEffectConfigReq")
	proto.RegisterType((*AddFlashEffectConfigResp)(nil), "apicentergo.AddFlashEffectConfigResp")
	proto.RegisterType((*UpdateFlashEffectConfigReq)(nil), "apicentergo.UpdateFlashEffectConfigReq")
	proto.RegisterType((*UpdateFlashEffectConfigResp)(nil), "apicentergo.UpdateFlashEffectConfigResp")
	proto.RegisterType((*DelFlashEffectConfigReq)(nil), "apicentergo.DelFlashEffectConfigReq")
	proto.RegisterType((*DelFlashEffectConfigResp)(nil), "apicentergo.DelFlashEffectConfigResp")
	proto.RegisterType((*PresentFlashInfo)(nil), "apicentergo.PresentFlashInfo")
	proto.RegisterType((*GetPresentFlashEffectReq)(nil), "apicentergo.GetPresentFlashEffectReq")
	proto.RegisterType((*GetPresentFlashEffectResp)(nil), "apicentergo.GetPresentFlashEffectResp")
	proto.RegisterType((*BoundPresentFlashEffectReq)(nil), "apicentergo.BoundPresentFlashEffectReq")
	proto.RegisterType((*BoundPresentFlashEffectResp)(nil), "apicentergo.BoundPresentFlashEffectResp")
	proto.RegisterEnum("apicentergo.EffectStatus", EffectStatus_name, EffectStatus_value)
}

func init() {
	proto.RegisterFile("apicenter-go/present-extra-api.proto", fileDescriptor_present_extra_api_c53d2bf7f295bb5f)
}

var fileDescriptor_present_extra_api_c53d2bf7f295bb5f = []byte{
	// 1039 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x57, 0xdf, 0x6e, 0xe3, 0x44,
	0x17, 0xff, 0xdc, 0x34, 0x69, 0x72, 0xd2, 0x6c, 0xd3, 0xd1, 0xee, 0xd6, 0x4d, 0xd5, 0xdd, 0x7e,
	0x06, 0x55, 0x15, 0x52, 0x13, 0xa9, 0x6c, 0x2f, 0x41, 0x6a, 0x97, 0x6e, 0x89, 0x04, 0x2c, 0xca,
	0x6e, 0x41, 0x42, 0x08, 0x6b, 0xd6, 0x9e, 0x78, 0x4d, 0x12, 0x7b, 0xf0, 0x4c, 0x0a, 0x7e, 0x08,
	0xee, 0x11, 0x4f, 0xc0, 0x25, 0x0f, 0xc0, 0x25, 0xf7, 0xbc, 0x12, 0x9a, 0x33, 0x13, 0xc7, 0x4e,
	0x6c, 0xba, 0x2b, 0x2d, 0xe2, 0x2e, 0xe7, 0xff, 0x39, 0xbf, 0x39, 0xe7, 0x57, 0x17, 0xde, 0xa7,
	0x3c, 0xf4, 0x58, 0x24, 0x59, 0x72, 0x1a, 0xc4, 0x03, 0x9e, 0x30, 0xc1, 0x22, 0x79, 0xca, 0x7e,
	0x92, 0x09, 0x3d, 0xa5, 0x3c, 0xec, 0xf3, 0x24, 0x96, 0x31, 0x69, 0x67, 0x5e, 0x41, 0xec, 0x44,
	0xd0, 0x7d, 0xc1, 0x68, 0xe2, 0xbd, 0xfe, 0x52, 0x7b, 0x8f, 0xd8, 0x0f, 0xe4, 0x01, 0x34, 0x26,
	0x2c, 0x75, 0x43, 0xdf, 0xb6, 0x8e, 0xac, 0x93, 0xce, 0xa8, 0x3e, 0x61, 0xe9, 0xd0, 0x27, 0xfb,
	0xd0, 0x54, 0xea, 0x88, 0xce, 0x98, 0xbd, 0x71, 0x64, 0x9d, 0xb4, 0x46, 0x5b, 0x13, 0x96, 0x7e,
	0x41, 0x67, 0x8c, 0x10, 0xd8, 0xe4, 0x34, 0x60, 0x76, 0x0d, 0xfd, 0xf1, 0x37, 0xb9, 0x0f, 0x75,
	0x2f, 0x9e, 0x47, 0xd2, 0xde, 0xd4, 0x49, 0x50, 0x70, 0x12, 0xd8, 0x5d, 0xa9, 0x27, 0x38, 0xb9,
	0x86, 0x1d, 0xd3, 0xac, 0xeb, 0xc5, 0xd1, 0x38, 0x0c, 0x84, 0x6d, 0x1d, 0xd5, 0x4e, 0xda, 0x67,
	0x8f, 0xfa, 0xb9, 0x5e, 0xfb, 0x26, 0xe4, 0x92, 0x0a, 0xf6, 0x14, 0xdd, 0x46, 0xf7, 0x4c, 0x98,
	0x16, 0x85, 0xaa, 0x29, 0x63, 0x49, 0xa7, 0xd8, 0x5f, 0x67, 0xa4, 0x05, 0xe7, 0x67, 0x0b, 0xf6,
	0xae, 0x99, 0x34, 0xe1, 0xcf, 0xa6, 0x31, 0x95, 0x9f, 0xd1, 0x94, 0x25, 0x6a, 0xd6, 0x45, 0xe7,
	0x56, 0x59, 0xe7, 0x1b, 0xb9, 0xce, 0x73, 0xa8, 0xd4, 0xaa, 0x50, 0xd9, 0x2c, 0xa2, 0xf2, 0x10,
	0x1a, 0x42, 0x52, 0x39, 0x17, 0x76, 0x1d, 0x23, 0x8c, 0xe4, 0x70, 0xb0, 0xcb, 0xdb, 0x11, 0x9c,
	0x7c, 0x0c, 0xed, 0xa9, 0x12, 0xdc, 0x30, 0x1a, 0xc7, 0x0b, 0x18, 0x0e, 0xcb, 0x60, 0xc0, 0xc0,
	0x61, 0x34, 0x8e, 0x47, 0x80, 0x11, 0xea, 0x67, 0x15, 0x02, 0xcf, 0x61, 0xef, 0xc2, 0xf7, 0x4b,
	0x01, 0x78, 0x02, 0x75, 0x0c, 0x47, 0x04, 0x2a, 0x10, 0xcf, 0x45, 0x68, 0x67, 0xa7, 0x07, 0x76,
	0x79, 0x42, 0xc1, 0x9d, 0x11, 0xf4, 0x6e, 0xb8, 0x4f, 0x25, 0x7b, 0x87, 0xf5, 0x0e, 0xe1, 0xa0,
	0x32, 0xa7, 0xe0, 0xce, 0x19, 0xec, 0x7d, 0xc2, 0xa6, 0xa5, 0xf5, 0xf6, 0x60, 0x2b, 0x08, 0xc7,
	0x72, 0xb9, 0xcd, 0x0d, 0x25, 0x0e, 0x7d, 0x35, 0x42, 0x79, 0x8c, 0xe0, 0xce, 0x2f, 0x16, 0x74,
	0x57, 0x61, 0x26, 0x1f, 0x01, 0x2c, 0x9f, 0xe6, 0x0d, 0xdb, 0x6f, 0x65, 0x4f, 0x43, 0xae, 0xe0,
	0x5e, 0x71, 0xc9, 0xf1, 0x89, 0xee, 0xde, 0xf1, 0x4e, 0x61, 0xc7, 0x9d, 0x5f, 0x37, 0x60, 0x77,
	0xad, 0x4e, 0xe5, 0x94, 0xe4, 0x18, 0x76, 0xc6, 0xca, 0xcd, 0x0d, 0x67, 0x34, 0x60, 0xee, 0x3c,
	0x99, 0x9a, 0xdb, 0xed, 0xa0, 0x7a, 0xa8, 0xb4, 0x37, 0xc9, 0x54, 0xad, 0xf1, 0xf7, 0xf3, 0x19,
	0x47, 0x87, 0x9a, 0x5e, 0x63, 0x25, 0x2b, 0xd3, 0x31, 0xec, 0x84, 0xc2, 0xa5, 0x9e, 0x0c, 0x6f,
	0x43, 0x99, 0xa2, 0x87, 0x5a, 0xf4, 0xe6, 0xa8, 0x13, 0x8a, 0x0b, 0xa3, 0x55, 0x7e, 0xff, 0x87,
	0x6d, 0x36, 0x1e, 0x33, 0x4f, 0xba, 0xaf, 0x58, 0x10, 0x46, 0x66, 0xe9, 0xdb, 0x5a, 0x77, 0xa9,
	0x54, 0xe4, 0x10, 0xc0, 0xb8, 0xb0, 0xc8, 0xb7, 0x1b, 0xe8, 0xd0, 0xd2, 0x9a, 0xab, 0xc8, 0x27,
	0x3d, 0x68, 0xc6, 0x9c, 0x25, 0x54, 0xc6, 0x89, 0xbd, 0x85, 0x4d, 0x64, 0x32, 0x79, 0x0f, 0x3a,
	0x26, 0xd4, 0xdc, 0x54, 0x13, 0xa3, 0x4d, 0xc9, 0x17, 0xfa, 0xb2, 0x7e, 0xb3, 0x32, 0x70, 0x96,
	0x08, 0x56, 0x83, 0x73, 0x00, 0x2d, 0x34, 0xe4, 0x28, 0xad, 0xa9, 0x14, 0x78, 0xbd, 0x8f, 0xa1,
	0xcd, 0x93, 0xd0, 0x63, 0xee, 0x2d, 0x9d, 0xce, 0x17, 0xd4, 0x06, 0xa8, 0xfa, 0x4a, 0x69, 0xd4,
	0x30, 0xda, 0x41, 0xa6, 0x9c, 0x19, 0x96, 0x6b, 0xa1, 0xe6, 0x65, 0xca, 0xd1, 0xac, 0xab, 0x2a,
	0x88, 0x11, 0x8c, 0xd6, 0x08, 0xcb, 0x21, 0xe6, 0xce, 0xa7, 0x40, 0xb2, 0x67, 0xa4, 0xe2, 0xf5,
	0x15, 0x8e, 0x51, 0xdd, 0xea, 0x3e, 0x34, 0xc7, 0xca, 0x4f, 0x59, 0xf4, 0x69, 0x6f, 0xa1, 0x3c,
	0xf4, 0x9d, 0x3f, 0x2d, 0xd8, 0xcd, 0xe5, 0x30, 0x43, 0xe7, 0x03, 0xac, 0x42, 0x80, 0xea, 0x4c,
	0x9b, 0x72, 0x73, 0xb7, 0x50, 0x83, 0x83, 0x1f, 0x80, 0x16, 0x72, 0xbb, 0xa0, 0x53, 0xa9, 0x47,
	0xce, 0x8c, 0x33, 0xff, 0xdc, 0xf0, 0x9d, 0x36, 0x7e, 0xee, 0x9f, 0x17, 0xde, 0xaf, 0xbe, 0xf2,
	0x7e, 0x8f, 0xa1, 0xed, 0x25, 0x8c, 0x4a, 0xe6, 0xca, 0x70, 0xc6, 0xcc, 0xdb, 0x83, 0x56, 0xbd,
	0x0c, 0x67, 0xcc, 0xf9, 0x0e, 0x49, 0x7a, 0x6d, 0x90, 0xb7, 0x23, 0xe9, 0x3c, 0x1b, 0xd7, 0x0a,
	0x6c, 0xec, 0xcc, 0x91, 0x75, 0x4b, 0xf2, 0x0b, 0x4e, 0x9e, 0x42, 0x47, 0x4f, 0xa5, 0xb7, 0xa9,
	0xfc, 0xcf, 0xcf, 0x7a, 0xe8, 0xf6, 0x78, 0xa9, 0xaa, 0xa2, 0xde, 0x6f, 0x91, 0x7a, 0x4b, 0xc7,
	0xba, 0x80, 0xed, 0x7c, 0xd5, 0x52, 0x4a, 0x59, 0x0f, 0x6c, 0xe7, 0x8a, 0x1a, 0x1e, 0x2e, 0x1d,
	0xca, 0x71, 0x17, 0x3c, 0xfc, 0x6f, 0x15, 0xcf, 0x48, 0xb9, 0xbc, 0xfe, 0x13, 0x24, 0xe5, 0xd2,
	0xe2, 0xd5, 0xcb, 0x69, 0x68, 0xb9, 0x3c, 0xe3, 0x1f, 0x1b, 0x39, 0x5a, 0x56, 0xee, 0x86, 0x96,
	0x4d, 0xae, 0x2a, 0x5a, 0x5e, 0x4f, 0xa6, 0x77, 0xf8, 0x1d, 0xd2, 0xf2, 0x1a, 0xf9, 0xd5, 0xee,
	0x22, 0xbf, 0xcd, 0x55, 0xf2, 0x5b, 0x39, 0x90, 0xfa, 0xea, 0x81, 0x14, 0xae, 0xab, 0x71, 0x17,
	0x3b, 0x6e, 0x95, 0xb0, 0xe3, 0xef, 0x56, 0xf1, 0xc3, 0x23, 0x43, 0xe5, 0xed, 0x6e, 0xec, 0x18,
	0x76, 0xd4, 0x8d, 0x99, 0x7a, 0xb9, 0x53, 0xeb, 0x4c, 0x58, 0xaa, 0x13, 0x22, 0x8f, 0x3c, 0x82,
	0xb6, 0xf2, 0x5b, 0xf0, 0x99, 0x19, 0x78, 0xc2, 0xd2, 0x6b, 0x4d, 0x69, 0x0e, 0x74, 0x32, 0x3b,
	0x66, 0xd1, 0x94, 0xd1, 0x36, 0x1e, 0x78, 0xb4, 0x29, 0xec, 0x57, 0x74, 0x2c, 0x38, 0x79, 0xb6,
	0xfc, 0x6c, 0x2c, 0xde, 0x6d, 0xc5, 0xf7, 0x92, 0x79, 0xf2, 0xec, 0xab, 0xf1, 0x9f, 0x0f, 0xf7,
	0x2f, 0x0b, 0x7a, 0x97, 0xf1, 0x3c, 0xf2, 0xcb, 0xf1, 0x2a, 0x30, 0x75, 0xed, 0x8d, 0x98, 0xfa,
	0x3f, 0x5e, 0x12, 0x75, 0xaf, 0x95, 0x03, 0x09, 0xfe, 0x41, 0x0a, 0xdb, 0x57, 0xb9, 0x75, 0x21,
	0x36, 0xdc, 0xcf, 0xcb, 0xee, 0x4d, 0x34, 0x89, 0xe2, 0x1f, 0xa3, 0xee, 0xff, 0xd6, 0x2c, 0x5f,
	0xd3, 0x50, 0x86, 0x51, 0xd0, 0xb5, 0x48, 0x0f, 0x1e, 0x16, 0x2c, 0x5a, 0x08, 0x6f, 0x59, 0x77,
	0x83, 0xec, 0xc3, 0x83, 0x82, 0xed, 0xf9, 0x5c, 0x2a, 0xf2, 0xf0, 0xbb, 0xb5, 0xcb, 0xc1, 0x37,
	0xa7, 0x41, 0x3c, 0xa5, 0x51, 0xd0, 0x3f, 0x3f, 0x93, 0xb2, 0xef, 0xc5, 0xb3, 0x01, 0xfe, 0xaf,
	0xe2, 0xc5, 0xd3, 0x81, 0x60, 0xc9, 0x6d, 0xe8, 0x31, 0x31, 0xc8, 0xbd, 0xe9, 0xab, 0x06, 0x9a,
	0x3f, 0xfc, 0x3b, 0x00, 0x00, 0xff, 0xff, 0x74, 0x87, 0xaa, 0x8c, 0xf2, 0x0c, 0x00, 0x00,
}
