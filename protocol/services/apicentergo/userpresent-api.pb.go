// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/userpresent-api.proto

package apicentergo // import "golang.52tt.com/protocol/services/apicentergo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 购买礼物的货币
type PresentPriceType int32

const (
	PresentPriceType_PRESENT_PRICE_UNKNOWN     PresentPriceType = 0
	PresentPriceType_PRESENT_PRICE_RED_DIAMOND PresentPriceType = 1
	PresentPriceType_PRESENT_PRICE_TBEAN       PresentPriceType = 2
)

var PresentPriceType_name = map[int32]string{
	0: "PRESENT_PRICE_UNKNOWN",
	1: "PRESENT_PRICE_RED_DIAMOND",
	2: "PRESENT_PRICE_TBEAN",
}
var PresentPriceType_value = map[string]int32{
	"PRESENT_PRICE_UNKNOWN":     0,
	"PRESENT_PRICE_RED_DIAMOND": 1,
	"PRESENT_PRICE_TBEAN":       2,
}

func (x PresentPriceType) String() string {
	return proto.EnumName(PresentPriceType_name, int32(x))
}
func (PresentPriceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{0}
}

// 礼物配置列表类型
type ConfigListTypeBitMap int32

const (
	ConfigListTypeBitMap_CONFIG_UNLIMIT     ConfigListTypeBitMap = 0
	ConfigListTypeBitMap_CONFIG_NOT_EXPIRED ConfigListTypeBitMap = 1
	ConfigListTypeBitMap_CONFIG_NOT_DELETED ConfigListTypeBitMap = 2
)

var ConfigListTypeBitMap_name = map[int32]string{
	0: "CONFIG_UNLIMIT",
	1: "CONFIG_NOT_EXPIRED",
	2: "CONFIG_NOT_DELETED",
}
var ConfigListTypeBitMap_value = map[string]int32{
	"CONFIG_UNLIMIT":     0,
	"CONFIG_NOT_EXPIRED": 1,
	"CONFIG_NOT_DELETED": 2,
}

func (x ConfigListTypeBitMap) String() string {
	return proto.EnumName(ConfigListTypeBitMap_name, int32(x))
}
func (ConfigListTypeBitMap) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{1}
}

type StConfigIosExtend struct {
	VideoEffectUrl       []byte   `protobuf:"bytes,1,opt,name=video_effect_url,json=videoEffectUrl,proto3" json:"video_effect_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StConfigIosExtend) Reset()         { *m = StConfigIosExtend{} }
func (m *StConfigIosExtend) String() string { return proto.CompactTextString(m) }
func (*StConfigIosExtend) ProtoMessage()    {}
func (*StConfigIosExtend) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{0}
}
func (m *StConfigIosExtend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StConfigIosExtend.Unmarshal(m, b)
}
func (m *StConfigIosExtend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StConfigIosExtend.Marshal(b, m, deterministic)
}
func (dst *StConfigIosExtend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StConfigIosExtend.Merge(dst, src)
}
func (m *StConfigIosExtend) XXX_Size() int {
	return xxx_messageInfo_StConfigIosExtend.Size(m)
}
func (m *StConfigIosExtend) XXX_DiscardUnknown() {
	xxx_messageInfo_StConfigIosExtend.DiscardUnknown(m)
}

var xxx_messageInfo_StConfigIosExtend proto.InternalMessageInfo

func (m *StConfigIosExtend) GetVideoEffectUrl() []byte {
	if m != nil {
		return m.VideoEffectUrl
	}
	return nil
}

type StPresentItemConfigExtend struct {
	ItemId               uint32             `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	VideoEffectUrl       []byte             `protobuf:"bytes,2,opt,name=video_effect_url,json=videoEffectUrl,proto3" json:"video_effect_url,omitempty"`
	ShowEffect           uint32             `protobuf:"varint,3,opt,name=show_effect,json=showEffect,proto3" json:"show_effect,omitempty"`
	UnshowBatchOption    bool               `protobuf:"varint,4,opt,name=unshow_batch_option,json=unshowBatchOption,proto3" json:"unshow_batch_option,omitempty"`
	IsTest               bool               `protobuf:"varint,5,opt,name=is_test,json=isTest,proto3" json:"is_test,omitempty"`
	FlowId               uint32             `protobuf:"varint,6,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	IosExtend            *StConfigIosExtend `protobuf:"bytes,7,opt,name=ios_extend,json=iosExtend,proto3" json:"ios_extend,omitempty"`
	NotifyAll            bool               `protobuf:"varint,8,opt,name=notify_all,json=notifyAll,proto3" json:"notify_all,omitempty"`
	Tag                  uint32             `protobuf:"varint,9,opt,name=tag,proto3" json:"tag,omitempty"`
	ForceSendable        bool               `protobuf:"varint,10,opt,name=force_sendable,json=forceSendable,proto3" json:"force_sendable,omitempty"`
	NobilityLevel        uint32             `protobuf:"varint,11,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	UnshowPresentShelf   bool               `protobuf:"varint,12,opt,name=unshow_present_shelf,json=unshowPresentShelf,proto3" json:"unshow_present_shelf,omitempty"`
	ShowEffectEnd        bool               `protobuf:"varint,13,opt,name=show_effect_end,json=showEffectEnd,proto3" json:"show_effect_end,omitempty"`
	EffectEndDelay       bool               `protobuf:"varint,14,opt,name=effect_end_delay,json=effectEndDelay,proto3" json:"effect_end_delay,omitempty"`
	CustomText           []*CustomText      `protobuf:"bytes,15,rep,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	MicEffectUrl         string             `protobuf:"bytes,16,opt,name=mic_effect_url,json=micEffectUrl,proto3" json:"mic_effect_url,omitempty"`
	MicEffectMd5         string             `protobuf:"bytes,17,opt,name=mic_effect_md5,json=micEffectMd5,proto3" json:"mic_effect_md5,omitempty"`
	SmallVapEffectUrl    string             `protobuf:"bytes,18,opt,name=small_vap_effect_url,json=smallVapEffectUrl,proto3" json:"small_vap_effect_url,omitempty"`
	SmallVapEffectMd5    string             `protobuf:"bytes,19,opt,name=small_vap_effect_md5,json=smallVapEffectMd5,proto3" json:"small_vap_effect_md5,omitempty"`
	FusionPresent        bool               `protobuf:"varint,20,opt,name=fusion_present,json=fusionPresent,proto3" json:"fusion_present,omitempty"`
	IsBoxBreaking        bool               `protobuf:"varint,21,opt,name=is_box_breaking,json=isBoxBreaking,proto3" json:"is_box_breaking,omitempty"`
	FansLevel            uint32             `protobuf:"varint,22,opt,name=fans_level,json=fansLevel,proto3" json:"fans_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *StPresentItemConfigExtend) Reset()         { *m = StPresentItemConfigExtend{} }
func (m *StPresentItemConfigExtend) String() string { return proto.CompactTextString(m) }
func (*StPresentItemConfigExtend) ProtoMessage()    {}
func (*StPresentItemConfigExtend) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{1}
}
func (m *StPresentItemConfigExtend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StPresentItemConfigExtend.Unmarshal(m, b)
}
func (m *StPresentItemConfigExtend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StPresentItemConfigExtend.Marshal(b, m, deterministic)
}
func (dst *StPresentItemConfigExtend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StPresentItemConfigExtend.Merge(dst, src)
}
func (m *StPresentItemConfigExtend) XXX_Size() int {
	return xxx_messageInfo_StPresentItemConfigExtend.Size(m)
}
func (m *StPresentItemConfigExtend) XXX_DiscardUnknown() {
	xxx_messageInfo_StPresentItemConfigExtend.DiscardUnknown(m)
}

var xxx_messageInfo_StPresentItemConfigExtend proto.InternalMessageInfo

func (m *StPresentItemConfigExtend) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetVideoEffectUrl() []byte {
	if m != nil {
		return m.VideoEffectUrl
	}
	return nil
}

func (m *StPresentItemConfigExtend) GetShowEffect() uint32 {
	if m != nil {
		return m.ShowEffect
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetUnshowBatchOption() bool {
	if m != nil {
		return m.UnshowBatchOption
	}
	return false
}

func (m *StPresentItemConfigExtend) GetIsTest() bool {
	if m != nil {
		return m.IsTest
	}
	return false
}

func (m *StPresentItemConfigExtend) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetIosExtend() *StConfigIosExtend {
	if m != nil {
		return m.IosExtend
	}
	return nil
}

func (m *StPresentItemConfigExtend) GetNotifyAll() bool {
	if m != nil {
		return m.NotifyAll
	}
	return false
}

func (m *StPresentItemConfigExtend) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetForceSendable() bool {
	if m != nil {
		return m.ForceSendable
	}
	return false
}

func (m *StPresentItemConfigExtend) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetUnshowPresentShelf() bool {
	if m != nil {
		return m.UnshowPresentShelf
	}
	return false
}

func (m *StPresentItemConfigExtend) GetShowEffectEnd() bool {
	if m != nil {
		return m.ShowEffectEnd
	}
	return false
}

func (m *StPresentItemConfigExtend) GetEffectEndDelay() bool {
	if m != nil {
		return m.EffectEndDelay
	}
	return false
}

func (m *StPresentItemConfigExtend) GetCustomText() []*CustomText {
	if m != nil {
		return m.CustomText
	}
	return nil
}

func (m *StPresentItemConfigExtend) GetMicEffectUrl() string {
	if m != nil {
		return m.MicEffectUrl
	}
	return ""
}

func (m *StPresentItemConfigExtend) GetMicEffectMd5() string {
	if m != nil {
		return m.MicEffectMd5
	}
	return ""
}

func (m *StPresentItemConfigExtend) GetSmallVapEffectUrl() string {
	if m != nil {
		return m.SmallVapEffectUrl
	}
	return ""
}

func (m *StPresentItemConfigExtend) GetSmallVapEffectMd5() string {
	if m != nil {
		return m.SmallVapEffectMd5
	}
	return ""
}

func (m *StPresentItemConfigExtend) GetFusionPresent() bool {
	if m != nil {
		return m.FusionPresent
	}
	return false
}

func (m *StPresentItemConfigExtend) GetIsBoxBreaking() bool {
	if m != nil {
		return m.IsBoxBreaking
	}
	return false
}

func (m *StPresentItemConfigExtend) GetFansLevel() uint32 {
	if m != nil {
		return m.FansLevel
	}
	return 0
}

type CustomText struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Text                 []string `protobuf:"bytes,2,rep,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomText) Reset()         { *m = CustomText{} }
func (m *CustomText) String() string { return proto.CompactTextString(m) }
func (*CustomText) ProtoMessage()    {}
func (*CustomText) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{2}
}
func (m *CustomText) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomText.Unmarshal(m, b)
}
func (m *CustomText) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomText.Marshal(b, m, deterministic)
}
func (dst *CustomText) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomText.Merge(dst, src)
}
func (m *CustomText) XXX_Size() int {
	return xxx_messageInfo_CustomText.Size(m)
}
func (m *CustomText) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomText.DiscardUnknown(m)
}

var xxx_messageInfo_CustomText proto.InternalMessageInfo

func (m *CustomText) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CustomText) GetText() []string {
	if m != nil {
		return m.Text
	}
	return nil
}

// 礼物配置信息
type StPresentItemConfig struct {
	ItemId               uint32                     `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Name                 string                     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl              string                     `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price                uint32                     `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	Score                uint32                     `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	Charm                uint32                     `protobuf:"varint,6,opt,name=charm,proto3" json:"charm,omitempty"`
	Rank                 uint32                     `protobuf:"varint,7,opt,name=rank,proto3" json:"rank,omitempty"`
	EffectBegin          uint32                     `protobuf:"varint,8,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32                     `protobuf:"varint,9,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	UpdateTime           uint32                     `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime           uint32                     `protobuf:"varint,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	IsDel                bool                       `protobuf:"varint,12,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	PriceType            uint32                     `protobuf:"varint,13,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	RichValue            uint32                     `protobuf:"varint,14,opt,name=rich_value,json=richValue,proto3" json:"rich_value,omitempty"`
	Extend               *StPresentItemConfigExtend `protobuf:"bytes,15,opt,name=extend,proto3" json:"extend,omitempty"`
	Fellow               *FellowPresentConfig       `protobuf:"bytes,16,opt,name=fellow,proto3" json:"fellow,omitempty"`
	DelayInfo            []*EffectDelayLevelInfo    `protobuf:"bytes,17,rep,name=delay_info,json=delayInfo,proto3" json:"delay_info,omitempty"`
	IsBanned             bool                       `protobuf:"varint,18,opt,name=is_banned,json=isBanned,proto3" json:"is_banned,omitempty"`
	RankFloat            float32                    `protobuf:"fixed32,19,opt,name=rank_float,json=rankFloat,proto3" json:"rank_float,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *StPresentItemConfig) Reset()         { *m = StPresentItemConfig{} }
func (m *StPresentItemConfig) String() string { return proto.CompactTextString(m) }
func (*StPresentItemConfig) ProtoMessage()    {}
func (*StPresentItemConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{3}
}
func (m *StPresentItemConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StPresentItemConfig.Unmarshal(m, b)
}
func (m *StPresentItemConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StPresentItemConfig.Marshal(b, m, deterministic)
}
func (dst *StPresentItemConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StPresentItemConfig.Merge(dst, src)
}
func (m *StPresentItemConfig) XXX_Size() int {
	return xxx_messageInfo_StPresentItemConfig.Size(m)
}
func (m *StPresentItemConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_StPresentItemConfig.DiscardUnknown(m)
}

var xxx_messageInfo_StPresentItemConfig proto.InternalMessageInfo

func (m *StPresentItemConfig) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StPresentItemConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StPresentItemConfig) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *StPresentItemConfig) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *StPresentItemConfig) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *StPresentItemConfig) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *StPresentItemConfig) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *StPresentItemConfig) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *StPresentItemConfig) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *StPresentItemConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *StPresentItemConfig) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *StPresentItemConfig) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *StPresentItemConfig) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *StPresentItemConfig) GetRichValue() uint32 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *StPresentItemConfig) GetExtend() *StPresentItemConfigExtend {
	if m != nil {
		return m.Extend
	}
	return nil
}

func (m *StPresentItemConfig) GetFellow() *FellowPresentConfig {
	if m != nil {
		return m.Fellow
	}
	return nil
}

func (m *StPresentItemConfig) GetDelayInfo() []*EffectDelayLevelInfo {
	if m != nil {
		return m.DelayInfo
	}
	return nil
}

func (m *StPresentItemConfig) GetIsBanned() bool {
	if m != nil {
		return m.IsBanned
	}
	return false
}

func (m *StPresentItemConfig) GetRankFloat() float32 {
	if m != nil {
		return m.RankFloat
	}
	return 0
}

type FellowPresentConfig struct {
	UniqueBackgroundUrl  string   `protobuf:"bytes,1,opt,name=unique_background_url,json=uniqueBackgroundUrl,proto3" json:"unique_background_url,omitempty"`
	UniqueSourceType     uint32   `protobuf:"varint,2,opt,name=unique_source_type,json=uniqueSourceType,proto3" json:"unique_source_type,omitempty"`
	UniqueMd5            string   `protobuf:"bytes,3,opt,name=unique_md5,json=uniqueMd5,proto3" json:"unique_md5,omitempty"`
	MultiBackgroundUrl   string   `protobuf:"bytes,4,opt,name=multi_background_url,json=multiBackgroundUrl,proto3" json:"multi_background_url,omitempty"`
	MultiSourceType      uint32   `protobuf:"varint,5,opt,name=multi_source_type,json=multiSourceType,proto3" json:"multi_source_type,omitempty"`
	MultiMd5             string   `protobuf:"bytes,6,opt,name=multi_md5,json=multiMd5,proto3" json:"multi_md5,omitempty"`
	UniqueBackgroundImg  string   `protobuf:"bytes,7,opt,name=unique_background_img,json=uniqueBackgroundImg,proto3" json:"unique_background_img,omitempty"`
	MultiBackgroundImg   string   `protobuf:"bytes,8,opt,name=multi_background_img,json=multiBackgroundImg,proto3" json:"multi_background_img,omitempty"`
	SourceZip            string   `protobuf:"bytes,9,opt,name=source_zip,json=sourceZip,proto3" json:"source_zip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowPresentConfig) Reset()         { *m = FellowPresentConfig{} }
func (m *FellowPresentConfig) String() string { return proto.CompactTextString(m) }
func (*FellowPresentConfig) ProtoMessage()    {}
func (*FellowPresentConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{4}
}
func (m *FellowPresentConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowPresentConfig.Unmarshal(m, b)
}
func (m *FellowPresentConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowPresentConfig.Marshal(b, m, deterministic)
}
func (dst *FellowPresentConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowPresentConfig.Merge(dst, src)
}
func (m *FellowPresentConfig) XXX_Size() int {
	return xxx_messageInfo_FellowPresentConfig.Size(m)
}
func (m *FellowPresentConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowPresentConfig.DiscardUnknown(m)
}

var xxx_messageInfo_FellowPresentConfig proto.InternalMessageInfo

func (m *FellowPresentConfig) GetUniqueBackgroundUrl() string {
	if m != nil {
		return m.UniqueBackgroundUrl
	}
	return ""
}

func (m *FellowPresentConfig) GetUniqueSourceType() uint32 {
	if m != nil {
		return m.UniqueSourceType
	}
	return 0
}

func (m *FellowPresentConfig) GetUniqueMd5() string {
	if m != nil {
		return m.UniqueMd5
	}
	return ""
}

func (m *FellowPresentConfig) GetMultiBackgroundUrl() string {
	if m != nil {
		return m.MultiBackgroundUrl
	}
	return ""
}

func (m *FellowPresentConfig) GetMultiSourceType() uint32 {
	if m != nil {
		return m.MultiSourceType
	}
	return 0
}

func (m *FellowPresentConfig) GetMultiMd5() string {
	if m != nil {
		return m.MultiMd5
	}
	return ""
}

func (m *FellowPresentConfig) GetUniqueBackgroundImg() string {
	if m != nil {
		return m.UniqueBackgroundImg
	}
	return ""
}

func (m *FellowPresentConfig) GetMultiBackgroundImg() string {
	if m != nil {
		return m.MultiBackgroundImg
	}
	return ""
}

func (m *FellowPresentConfig) GetSourceZip() string {
	if m != nil {
		return m.SourceZip
	}
	return ""
}

// 根据礼物id获取礼物配置
type GetPresentConfigByIdReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentConfigByIdReq) Reset()         { *m = GetPresentConfigByIdReq{} }
func (m *GetPresentConfigByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigByIdReq) ProtoMessage()    {}
func (*GetPresentConfigByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{5}
}
func (m *GetPresentConfigByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigByIdReq.Unmarshal(m, b)
}
func (m *GetPresentConfigByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigByIdReq.Merge(dst, src)
}
func (m *GetPresentConfigByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigByIdReq.Size(m)
}
func (m *GetPresentConfigByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigByIdReq proto.InternalMessageInfo

func (m *GetPresentConfigByIdReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type GetPresentConfigByIdResp struct {
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,1,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetPresentConfigByIdResp) Reset()         { *m = GetPresentConfigByIdResp{} }
func (m *GetPresentConfigByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigByIdResp) ProtoMessage()    {}
func (*GetPresentConfigByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{6}
}
func (m *GetPresentConfigByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigByIdResp.Unmarshal(m, b)
}
func (m *GetPresentConfigByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigByIdResp.Merge(dst, src)
}
func (m *GetPresentConfigByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigByIdResp.Size(m)
}
func (m *GetPresentConfigByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigByIdResp proto.InternalMessageInfo

func (m *GetPresentConfigByIdResp) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

type GetPresentConfigListReq struct {
	TypeBitmap           uint32   `protobuf:"varint,1,opt,name=type_bitmap,json=typeBitmap,proto3" json:"type_bitmap,omitempty"`
	GetAll               bool     `protobuf:"varint,2,opt,name=get_all,json=getAll,proto3" json:"get_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentConfigListReq) Reset()         { *m = GetPresentConfigListReq{} }
func (m *GetPresentConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListReq) ProtoMessage()    {}
func (*GetPresentConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{7}
}
func (m *GetPresentConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListReq.Unmarshal(m, b)
}
func (m *GetPresentConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListReq.Merge(dst, src)
}
func (m *GetPresentConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListReq.Size(m)
}
func (m *GetPresentConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListReq proto.InternalMessageInfo

func (m *GetPresentConfigListReq) GetTypeBitmap() uint32 {
	if m != nil {
		return m.TypeBitmap
	}
	return 0
}

func (m *GetPresentConfigListReq) GetGetAll() bool {
	if m != nil {
		return m.GetAll
	}
	return false
}

type GetPresentConfigListResp struct {
	ItemList             []*StPresentItemConfig `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPresentConfigListResp) Reset()         { *m = GetPresentConfigListResp{} }
func (m *GetPresentConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListResp) ProtoMessage()    {}
func (*GetPresentConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{8}
}
func (m *GetPresentConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListResp.Unmarshal(m, b)
}
func (m *GetPresentConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListResp.Merge(dst, src)
}
func (m *GetPresentConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListResp.Size(m)
}
func (m *GetPresentConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListResp proto.InternalMessageInfo

func (m *GetPresentConfigListResp) GetItemList() []*StPresentItemConfig {
	if m != nil {
		return m.ItemList
	}
	return nil
}

// 增加礼物配置
type AddPresentConfigReq struct {
	Name                 string                     `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl              string                     `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price                uint32                     `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	EffectBegin          uint32                     `protobuf:"varint,4,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32                     `protobuf:"varint,5,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	Rank                 uint32                     `protobuf:"varint,6,opt,name=rank,proto3" json:"rank,omitempty"`
	PriceType            uint32                     `protobuf:"varint,7,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	Extend               *StPresentItemConfigExtend `protobuf:"bytes,8,opt,name=extend,proto3" json:"extend,omitempty"`
	Fellow               *FellowPresentConfig       `protobuf:"bytes,9,opt,name=fellow,proto3" json:"fellow,omitempty"`
	DelayInfo            []*EffectDelayLevelInfo    `protobuf:"bytes,10,rep,name=delay_info,json=delayInfo,proto3" json:"delay_info,omitempty"`
	RankFloat            float32                    `protobuf:"fixed32,11,opt,name=rank_float,json=rankFloat,proto3" json:"rank_float,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *AddPresentConfigReq) Reset()         { *m = AddPresentConfigReq{} }
func (m *AddPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddPresentConfigReq) ProtoMessage()    {}
func (*AddPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{9}
}
func (m *AddPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentConfigReq.Unmarshal(m, b)
}
func (m *AddPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentConfigReq.Merge(dst, src)
}
func (m *AddPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddPresentConfigReq.Size(m)
}
func (m *AddPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentConfigReq proto.InternalMessageInfo

func (m *AddPresentConfigReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AddPresentConfigReq) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *AddPresentConfigReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *AddPresentConfigReq) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *AddPresentConfigReq) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *AddPresentConfigReq) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *AddPresentConfigReq) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *AddPresentConfigReq) GetExtend() *StPresentItemConfigExtend {
	if m != nil {
		return m.Extend
	}
	return nil
}

func (m *AddPresentConfigReq) GetFellow() *FellowPresentConfig {
	if m != nil {
		return m.Fellow
	}
	return nil
}

func (m *AddPresentConfigReq) GetDelayInfo() []*EffectDelayLevelInfo {
	if m != nil {
		return m.DelayInfo
	}
	return nil
}

func (m *AddPresentConfigReq) GetRankFloat() float32 {
	if m != nil {
		return m.RankFloat
	}
	return 0
}

type EffectDelayLevelInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	SendCount            uint32   `protobuf:"varint,2,opt,name=send_count,json=sendCount,proto3" json:"send_count,omitempty"`
	DayCount             uint32   `protobuf:"varint,3,opt,name=day_count,json=dayCount,proto3" json:"day_count,omitempty"`
	ExpireDayCount       uint32   `protobuf:"varint,4,opt,name=expire_day_count,json=expireDayCount,proto3" json:"expire_day_count,omitempty"`
	NoticeDayCount       uint32   `protobuf:"varint,5,opt,name=notice_day_count,json=noticeDayCount,proto3" json:"notice_day_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EffectDelayLevelInfo) Reset()         { *m = EffectDelayLevelInfo{} }
func (m *EffectDelayLevelInfo) String() string { return proto.CompactTextString(m) }
func (*EffectDelayLevelInfo) ProtoMessage()    {}
func (*EffectDelayLevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{10}
}
func (m *EffectDelayLevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EffectDelayLevelInfo.Unmarshal(m, b)
}
func (m *EffectDelayLevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EffectDelayLevelInfo.Marshal(b, m, deterministic)
}
func (dst *EffectDelayLevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EffectDelayLevelInfo.Merge(dst, src)
}
func (m *EffectDelayLevelInfo) XXX_Size() int {
	return xxx_messageInfo_EffectDelayLevelInfo.Size(m)
}
func (m *EffectDelayLevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EffectDelayLevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EffectDelayLevelInfo proto.InternalMessageInfo

func (m *EffectDelayLevelInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *EffectDelayLevelInfo) GetSendCount() uint32 {
	if m != nil {
		return m.SendCount
	}
	return 0
}

func (m *EffectDelayLevelInfo) GetDayCount() uint32 {
	if m != nil {
		return m.DayCount
	}
	return 0
}

func (m *EffectDelayLevelInfo) GetExpireDayCount() uint32 {
	if m != nil {
		return m.ExpireDayCount
	}
	return 0
}

func (m *EffectDelayLevelInfo) GetNoticeDayCount() uint32 {
	if m != nil {
		return m.NoticeDayCount
	}
	return 0
}

type AddPresentConfigResp struct {
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,1,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AddPresentConfigResp) Reset()         { *m = AddPresentConfigResp{} }
func (m *AddPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddPresentConfigResp) ProtoMessage()    {}
func (*AddPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{11}
}
func (m *AddPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentConfigResp.Unmarshal(m, b)
}
func (m *AddPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentConfigResp.Merge(dst, src)
}
func (m *AddPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddPresentConfigResp.Size(m)
}
func (m *AddPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentConfigResp proto.InternalMessageInfo

func (m *AddPresentConfigResp) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

// 删除礼物配置
type DelPresentConfigReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentConfigReq) Reset()         { *m = DelPresentConfigReq{} }
func (m *DelPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelPresentConfigReq) ProtoMessage()    {}
func (*DelPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{12}
}
func (m *DelPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentConfigReq.Unmarshal(m, b)
}
func (m *DelPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentConfigReq.Merge(dst, src)
}
func (m *DelPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelPresentConfigReq.Size(m)
}
func (m *DelPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentConfigReq proto.InternalMessageInfo

func (m *DelPresentConfigReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type DelPresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentConfigResp) Reset()         { *m = DelPresentConfigResp{} }
func (m *DelPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelPresentConfigResp) ProtoMessage()    {}
func (*DelPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{13}
}
func (m *DelPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentConfigResp.Unmarshal(m, b)
}
func (m *DelPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentConfigResp.Merge(dst, src)
}
func (m *DelPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelPresentConfigResp.Size(m)
}
func (m *DelPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentConfigResp proto.InternalMessageInfo

// 更新礼物配置
type UpdatePresentConfigReq struct {
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,1,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdatePresentConfigReq) Reset()         { *m = UpdatePresentConfigReq{} }
func (m *UpdatePresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentConfigReq) ProtoMessage()    {}
func (*UpdatePresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{14}
}
func (m *UpdatePresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentConfigReq.Unmarshal(m, b)
}
func (m *UpdatePresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentConfigReq.Merge(dst, src)
}
func (m *UpdatePresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentConfigReq.Size(m)
}
func (m *UpdatePresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentConfigReq proto.InternalMessageInfo

func (m *UpdatePresentConfigReq) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

type UpdatePresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePresentConfigResp) Reset()         { *m = UpdatePresentConfigResp{} }
func (m *UpdatePresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentConfigResp) ProtoMessage()    {}
func (*UpdatePresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{15}
}
func (m *UpdatePresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentConfigResp.Unmarshal(m, b)
}
func (m *UpdatePresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentConfigResp.Merge(dst, src)
}
func (m *UpdatePresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentConfigResp.Size(m)
}
func (m *UpdatePresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentConfigResp proto.InternalMessageInfo

type UserPresentSend struct {
	ToUid                uint32   `protobuf:"varint,1,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ToTtid               uint32   `protobuf:"varint,2,opt,name=to_ttid,json=toTtid,proto3" json:"to_ttid,omitempty"`
	ToNickname           string   `protobuf:"bytes,3,opt,name=to_nickname,json=toNickname,proto3" json:"to_nickname,omitempty"`
	ItemId               uint32   `protobuf:"varint,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName             string   `protobuf:"bytes,5,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	ItemCount            uint32   `protobuf:"varint,6,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	AddRich              uint32   `protobuf:"varint,7,opt,name=add_rich,json=addRich,proto3" json:"add_rich,omitempty"`
	SendTime             uint32   `protobuf:"varint,8,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPresentSend) Reset()         { *m = UserPresentSend{} }
func (m *UserPresentSend) String() string { return proto.CompactTextString(m) }
func (*UserPresentSend) ProtoMessage()    {}
func (*UserPresentSend) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{16}
}
func (m *UserPresentSend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPresentSend.Unmarshal(m, b)
}
func (m *UserPresentSend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPresentSend.Marshal(b, m, deterministic)
}
func (dst *UserPresentSend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPresentSend.Merge(dst, src)
}
func (m *UserPresentSend) XXX_Size() int {
	return xxx_messageInfo_UserPresentSend.Size(m)
}
func (m *UserPresentSend) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPresentSend.DiscardUnknown(m)
}

var xxx_messageInfo_UserPresentSend proto.InternalMessageInfo

func (m *UserPresentSend) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *UserPresentSend) GetToTtid() uint32 {
	if m != nil {
		return m.ToTtid
	}
	return 0
}

func (m *UserPresentSend) GetToNickname() string {
	if m != nil {
		return m.ToNickname
	}
	return ""
}

func (m *UserPresentSend) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *UserPresentSend) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *UserPresentSend) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *UserPresentSend) GetAddRich() uint32 {
	if m != nil {
		return m.AddRich
	}
	return 0
}

func (m *UserPresentSend) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

type UserPresentReceive struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	FromTtid             uint32   `protobuf:"varint,2,opt,name=from_ttid,json=fromTtid,proto3" json:"from_ttid,omitempty"`
	FromNickname         string   `protobuf:"bytes,3,opt,name=from_nickname,json=fromNickname,proto3" json:"from_nickname,omitempty"`
	ItemId               uint32   `protobuf:"varint,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName             string   `protobuf:"bytes,5,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	ItemCount            uint32   `protobuf:"varint,6,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	AddCharm             uint32   `protobuf:"varint,7,opt,name=add_charm,json=addCharm,proto3" json:"add_charm,omitempty"`
	AddScore             uint32   `protobuf:"varint,8,opt,name=add_score,json=addScore,proto3" json:"add_score,omitempty"`
	ReceiveTime          uint32   `protobuf:"varint,9,opt,name=receive_time,json=receiveTime,proto3" json:"receive_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPresentReceive) Reset()         { *m = UserPresentReceive{} }
func (m *UserPresentReceive) String() string { return proto.CompactTextString(m) }
func (*UserPresentReceive) ProtoMessage()    {}
func (*UserPresentReceive) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{17}
}
func (m *UserPresentReceive) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPresentReceive.Unmarshal(m, b)
}
func (m *UserPresentReceive) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPresentReceive.Marshal(b, m, deterministic)
}
func (dst *UserPresentReceive) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPresentReceive.Merge(dst, src)
}
func (m *UserPresentReceive) XXX_Size() int {
	return xxx_messageInfo_UserPresentReceive.Size(m)
}
func (m *UserPresentReceive) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPresentReceive.DiscardUnknown(m)
}

var xxx_messageInfo_UserPresentReceive proto.InternalMessageInfo

func (m *UserPresentReceive) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *UserPresentReceive) GetFromTtid() uint32 {
	if m != nil {
		return m.FromTtid
	}
	return 0
}

func (m *UserPresentReceive) GetFromNickname() string {
	if m != nil {
		return m.FromNickname
	}
	return ""
}

func (m *UserPresentReceive) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *UserPresentReceive) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *UserPresentReceive) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *UserPresentReceive) GetAddCharm() uint32 {
	if m != nil {
		return m.AddCharm
	}
	return 0
}

func (m *UserPresentReceive) GetAddScore() uint32 {
	if m != nil {
		return m.AddScore
	}
	return 0
}

func (m *UserPresentReceive) GetReceiveTime() uint32 {
	if m != nil {
		return m.ReceiveTime
	}
	return 0
}

// 用户送礼查询
type GetUserPresentSendReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTs              uint32   `protobuf:"varint,2,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,3,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPresentSendReq) Reset()         { *m = GetUserPresentSendReq{} }
func (m *GetUserPresentSendReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentSendReq) ProtoMessage()    {}
func (*GetUserPresentSendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{18}
}
func (m *GetUserPresentSendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentSendReq.Unmarshal(m, b)
}
func (m *GetUserPresentSendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentSendReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentSendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentSendReq.Merge(dst, src)
}
func (m *GetUserPresentSendReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentSendReq.Size(m)
}
func (m *GetUserPresentSendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentSendReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentSendReq proto.InternalMessageInfo

func (m *GetUserPresentSendReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPresentSendReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetUserPresentSendReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetUserPresentSendResp struct {
	PresentSendDetail    []*UserPresentSend `protobuf:"bytes,1,rep,name=present_send_detail,json=presentSendDetail,proto3" json:"present_send_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserPresentSendResp) Reset()         { *m = GetUserPresentSendResp{} }
func (m *GetUserPresentSendResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentSendResp) ProtoMessage()    {}
func (*GetUserPresentSendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{19}
}
func (m *GetUserPresentSendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentSendResp.Unmarshal(m, b)
}
func (m *GetUserPresentSendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentSendResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentSendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentSendResp.Merge(dst, src)
}
func (m *GetUserPresentSendResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentSendResp.Size(m)
}
func (m *GetUserPresentSendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentSendResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentSendResp proto.InternalMessageInfo

func (m *GetUserPresentSendResp) GetPresentSendDetail() []*UserPresentSend {
	if m != nil {
		return m.PresentSendDetail
	}
	return nil
}

// 用户送礼查询
type GetUserPresentReceiveReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTs              uint32   `protobuf:"varint,2,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,3,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPresentReceiveReq) Reset()         { *m = GetUserPresentReceiveReq{} }
func (m *GetUserPresentReceiveReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentReceiveReq) ProtoMessage()    {}
func (*GetUserPresentReceiveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{20}
}
func (m *GetUserPresentReceiveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentReceiveReq.Unmarshal(m, b)
}
func (m *GetUserPresentReceiveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentReceiveReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentReceiveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentReceiveReq.Merge(dst, src)
}
func (m *GetUserPresentReceiveReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentReceiveReq.Size(m)
}
func (m *GetUserPresentReceiveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentReceiveReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentReceiveReq proto.InternalMessageInfo

func (m *GetUserPresentReceiveReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPresentReceiveReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetUserPresentReceiveReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetUserPresentReceiveResp struct {
	PresentReceiveDetail []*UserPresentReceive `protobuf:"bytes,1,rep,name=present_receive_detail,json=presentReceiveDetail,proto3" json:"present_receive_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetUserPresentReceiveResp) Reset()         { *m = GetUserPresentReceiveResp{} }
func (m *GetUserPresentReceiveResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentReceiveResp) ProtoMessage()    {}
func (*GetUserPresentReceiveResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{21}
}
func (m *GetUserPresentReceiveResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentReceiveResp.Unmarshal(m, b)
}
func (m *GetUserPresentReceiveResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentReceiveResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentReceiveResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentReceiveResp.Merge(dst, src)
}
func (m *GetUserPresentReceiveResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentReceiveResp.Size(m)
}
func (m *GetUserPresentReceiveResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentReceiveResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentReceiveResp proto.InternalMessageInfo

func (m *GetUserPresentReceiveResp) GetPresentReceiveDetail() []*UserPresentReceive {
	if m != nil {
		return m.PresentReceiveDetail
	}
	return nil
}

// 用户送礼查询
type GetAllFellowPresentReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllFellowPresentReq) Reset()         { *m = GetAllFellowPresentReq{} }
func (m *GetAllFellowPresentReq) String() string { return proto.CompactTextString(m) }
func (*GetAllFellowPresentReq) ProtoMessage()    {}
func (*GetAllFellowPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{22}
}
func (m *GetAllFellowPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllFellowPresentReq.Unmarshal(m, b)
}
func (m *GetAllFellowPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllFellowPresentReq.Marshal(b, m, deterministic)
}
func (dst *GetAllFellowPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllFellowPresentReq.Merge(dst, src)
}
func (m *GetAllFellowPresentReq) XXX_Size() int {
	return xxx_messageInfo_GetAllFellowPresentReq.Size(m)
}
func (m *GetAllFellowPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllFellowPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllFellowPresentReq proto.InternalMessageInfo

type GetAllFellowPresentResp struct {
	FellowPresentList    []*StPresentItemConfig `protobuf:"bytes,1,rep,name=fellow_present_list,json=fellowPresentList,proto3" json:"fellow_present_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetAllFellowPresentResp) Reset()         { *m = GetAllFellowPresentResp{} }
func (m *GetAllFellowPresentResp) String() string { return proto.CompactTextString(m) }
func (*GetAllFellowPresentResp) ProtoMessage()    {}
func (*GetAllFellowPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{23}
}
func (m *GetAllFellowPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllFellowPresentResp.Unmarshal(m, b)
}
func (m *GetAllFellowPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllFellowPresentResp.Marshal(b, m, deterministic)
}
func (dst *GetAllFellowPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllFellowPresentResp.Merge(dst, src)
}
func (m *GetAllFellowPresentResp) XXX_Size() int {
	return xxx_messageInfo_GetAllFellowPresentResp.Size(m)
}
func (m *GetAllFellowPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllFellowPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllFellowPresentResp proto.InternalMessageInfo

func (m *GetAllFellowPresentResp) GetFellowPresentList() []*StPresentItemConfig {
	if m != nil {
		return m.FellowPresentList
	}
	return nil
}

// 增加礼物配置
type AddCustomizedPresentConfigReq struct {
	Config               *CustomizedPresentConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AddCustomizedPresentConfigReq) Reset()         { *m = AddCustomizedPresentConfigReq{} }
func (m *AddCustomizedPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddCustomizedPresentConfigReq) ProtoMessage()    {}
func (*AddCustomizedPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{24}
}
func (m *AddCustomizedPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCustomizedPresentConfigReq.Unmarshal(m, b)
}
func (m *AddCustomizedPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCustomizedPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddCustomizedPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCustomizedPresentConfigReq.Merge(dst, src)
}
func (m *AddCustomizedPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddCustomizedPresentConfigReq.Size(m)
}
func (m *AddCustomizedPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCustomizedPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCustomizedPresentConfigReq proto.InternalMessageInfo

func (m *AddCustomizedPresentConfigReq) GetConfig() *CustomizedPresentConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type AddCustomizedPresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCustomizedPresentConfigResp) Reset()         { *m = AddCustomizedPresentConfigResp{} }
func (m *AddCustomizedPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddCustomizedPresentConfigResp) ProtoMessage()    {}
func (*AddCustomizedPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{25}
}
func (m *AddCustomizedPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCustomizedPresentConfigResp.Unmarshal(m, b)
}
func (m *AddCustomizedPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCustomizedPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddCustomizedPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCustomizedPresentConfigResp.Merge(dst, src)
}
func (m *AddCustomizedPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddCustomizedPresentConfigResp.Size(m)
}
func (m *AddCustomizedPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCustomizedPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddCustomizedPresentConfigResp proto.InternalMessageInfo

type CustomizedPresentConfig struct {
	GiftId               uint32                     `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Name                 string                     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl              string                     `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price                uint32                     `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	Rank                 uint32                     `protobuf:"varint,5,opt,name=rank,proto3" json:"rank,omitempty"`
	PriceType            uint32                     `protobuf:"varint,6,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	Extend               *StPresentItemConfigExtend `protobuf:"bytes,7,opt,name=extend,proto3" json:"extend,omitempty"`
	CmdUrl               string                     `protobuf:"bytes,8,opt,name=cmd_url,json=cmdUrl,proto3" json:"cmd_url,omitempty"`
	MaxLevel             uint32                     `protobuf:"varint,9,opt,name=max_level,json=maxLevel,proto3" json:"max_level,omitempty"`
	EffectConfig         []*LevelEffectConfig       `protobuf:"bytes,10,rep,name=effect_config,json=effectConfig,proto3" json:"effect_config,omitempty"`
	CustomConfig         []*CustomConfig            `protobuf:"bytes,11,rep,name=custom_config,json=customConfig,proto3" json:"custom_config,omitempty"`
	LevelConfig          []*LevelConfig             `protobuf:"bytes,12,rep,name=level_config,json=levelConfig,proto3" json:"level_config,omitempty"`
	CustomMethod         []*CustomMethod            `protobuf:"bytes,13,rep,name=custom_method,json=customMethod,proto3" json:"custom_method,omitempty"`
	LevelText            string                     `protobuf:"bytes,14,opt,name=level_text,json=levelText,proto3" json:"level_text,omitempty"`
	UpdateTime           uint32                     `protobuf:"varint,15,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime           uint32                     `protobuf:"varint,16,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Score                uint32                     `protobuf:"varint,17,opt,name=score,proto3" json:"score,omitempty"`
	Charm                uint32                     `protobuf:"varint,18,opt,name=charm,proto3" json:"charm,omitempty"`
	RichValue            uint32                     `protobuf:"varint,19,opt,name=rich_value,json=richValue,proto3" json:"rich_value,omitempty"`
	RankFloat            float32                    `protobuf:"fixed32,20,opt,name=rank_float,json=rankFloat,proto3" json:"rank_float,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *CustomizedPresentConfig) Reset()         { *m = CustomizedPresentConfig{} }
func (m *CustomizedPresentConfig) String() string { return proto.CompactTextString(m) }
func (*CustomizedPresentConfig) ProtoMessage()    {}
func (*CustomizedPresentConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{26}
}
func (m *CustomizedPresentConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomizedPresentConfig.Unmarshal(m, b)
}
func (m *CustomizedPresentConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomizedPresentConfig.Marshal(b, m, deterministic)
}
func (dst *CustomizedPresentConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomizedPresentConfig.Merge(dst, src)
}
func (m *CustomizedPresentConfig) XXX_Size() int {
	return xxx_messageInfo_CustomizedPresentConfig.Size(m)
}
func (m *CustomizedPresentConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomizedPresentConfig.DiscardUnknown(m)
}

var xxx_messageInfo_CustomizedPresentConfig proto.InternalMessageInfo

func (m *CustomizedPresentConfig) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *CustomizedPresentConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CustomizedPresentConfig) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *CustomizedPresentConfig) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *CustomizedPresentConfig) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *CustomizedPresentConfig) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *CustomizedPresentConfig) GetExtend() *StPresentItemConfigExtend {
	if m != nil {
		return m.Extend
	}
	return nil
}

func (m *CustomizedPresentConfig) GetCmdUrl() string {
	if m != nil {
		return m.CmdUrl
	}
	return ""
}

func (m *CustomizedPresentConfig) GetMaxLevel() uint32 {
	if m != nil {
		return m.MaxLevel
	}
	return 0
}

func (m *CustomizedPresentConfig) GetEffectConfig() []*LevelEffectConfig {
	if m != nil {
		return m.EffectConfig
	}
	return nil
}

func (m *CustomizedPresentConfig) GetCustomConfig() []*CustomConfig {
	if m != nil {
		return m.CustomConfig
	}
	return nil
}

func (m *CustomizedPresentConfig) GetLevelConfig() []*LevelConfig {
	if m != nil {
		return m.LevelConfig
	}
	return nil
}

func (m *CustomizedPresentConfig) GetCustomMethod() []*CustomMethod {
	if m != nil {
		return m.CustomMethod
	}
	return nil
}

func (m *CustomizedPresentConfig) GetLevelText() string {
	if m != nil {
		return m.LevelText
	}
	return ""
}

func (m *CustomizedPresentConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *CustomizedPresentConfig) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *CustomizedPresentConfig) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *CustomizedPresentConfig) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *CustomizedPresentConfig) GetRichValue() uint32 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *CustomizedPresentConfig) GetRankFloat() float32 {
	if m != nil {
		return m.RankFloat
	}
	return 0
}

type CustomConfig struct {
	CustomId             uint32          `protobuf:"varint,1,opt,name=custom_id,json=customId,proto3" json:"custom_id,omitempty"`
	CustomName           string          `protobuf:"bytes,2,opt,name=custom_name,json=customName,proto3" json:"custom_name,omitempty"`
	CustomText           string          `protobuf:"bytes,3,opt,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	OptionConfig         []*OptionConfig `protobuf:"bytes,4,rep,name=OptionConfig,proto3" json:"OptionConfig,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CustomConfig) Reset()         { *m = CustomConfig{} }
func (m *CustomConfig) String() string { return proto.CompactTextString(m) }
func (*CustomConfig) ProtoMessage()    {}
func (*CustomConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{27}
}
func (m *CustomConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomConfig.Unmarshal(m, b)
}
func (m *CustomConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomConfig.Marshal(b, m, deterministic)
}
func (dst *CustomConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomConfig.Merge(dst, src)
}
func (m *CustomConfig) XXX_Size() int {
	return xxx_messageInfo_CustomConfig.Size(m)
}
func (m *CustomConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomConfig.DiscardUnknown(m)
}

var xxx_messageInfo_CustomConfig proto.InternalMessageInfo

func (m *CustomConfig) GetCustomId() uint32 {
	if m != nil {
		return m.CustomId
	}
	return 0
}

func (m *CustomConfig) GetCustomName() string {
	if m != nil {
		return m.CustomName
	}
	return ""
}

func (m *CustomConfig) GetCustomText() string {
	if m != nil {
		return m.CustomText
	}
	return ""
}

func (m *CustomConfig) GetOptionConfig() []*OptionConfig {
	if m != nil {
		return m.OptionConfig
	}
	return nil
}

type LevelConfig struct {
	Level                uint32          `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	CustomOption         []*CustomOption `protobuf:"bytes,2,rep,name=custom_option,json=customOption,proto3" json:"custom_option,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *LevelConfig) Reset()         { *m = LevelConfig{} }
func (m *LevelConfig) String() string { return proto.CompactTextString(m) }
func (*LevelConfig) ProtoMessage()    {}
func (*LevelConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{28}
}
func (m *LevelConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelConfig.Unmarshal(m, b)
}
func (m *LevelConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelConfig.Marshal(b, m, deterministic)
}
func (dst *LevelConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelConfig.Merge(dst, src)
}
func (m *LevelConfig) XXX_Size() int {
	return xxx_messageInfo_LevelConfig.Size(m)
}
func (m *LevelConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelConfig.DiscardUnknown(m)
}

var xxx_messageInfo_LevelConfig proto.InternalMessageInfo

func (m *LevelConfig) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *LevelConfig) GetCustomOption() []*CustomOption {
	if m != nil {
		return m.CustomOption
	}
	return nil
}

type OptionConfig struct {
	OptionId             uint32   `protobuf:"varint,1,opt,name=option_id,json=optionId,proto3" json:"option_id,omitempty"`
	OptionName           string   `protobuf:"bytes,2,opt,name=option_name,json=optionName,proto3" json:"option_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OptionConfig) Reset()         { *m = OptionConfig{} }
func (m *OptionConfig) String() string { return proto.CompactTextString(m) }
func (*OptionConfig) ProtoMessage()    {}
func (*OptionConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{29}
}
func (m *OptionConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OptionConfig.Unmarshal(m, b)
}
func (m *OptionConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OptionConfig.Marshal(b, m, deterministic)
}
func (dst *OptionConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OptionConfig.Merge(dst, src)
}
func (m *OptionConfig) XXX_Size() int {
	return xxx_messageInfo_OptionConfig.Size(m)
}
func (m *OptionConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_OptionConfig.DiscardUnknown(m)
}

var xxx_messageInfo_OptionConfig proto.InternalMessageInfo

func (m *OptionConfig) GetOptionId() uint32 {
	if m != nil {
		return m.OptionId
	}
	return 0
}

func (m *OptionConfig) GetOptionName() string {
	if m != nil {
		return m.OptionName
	}
	return ""
}

type LevelEffectConfig struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	EffectBegin          uint32   `protobuf:"varint,2,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32   `protobuf:"varint,3,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelEffectConfig) Reset()         { *m = LevelEffectConfig{} }
func (m *LevelEffectConfig) String() string { return proto.CompactTextString(m) }
func (*LevelEffectConfig) ProtoMessage()    {}
func (*LevelEffectConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{30}
}
func (m *LevelEffectConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelEffectConfig.Unmarshal(m, b)
}
func (m *LevelEffectConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelEffectConfig.Marshal(b, m, deterministic)
}
func (dst *LevelEffectConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelEffectConfig.Merge(dst, src)
}
func (m *LevelEffectConfig) XXX_Size() int {
	return xxx_messageInfo_LevelEffectConfig.Size(m)
}
func (m *LevelEffectConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelEffectConfig.DiscardUnknown(m)
}

var xxx_messageInfo_LevelEffectConfig proto.InternalMessageInfo

func (m *LevelEffectConfig) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *LevelEffectConfig) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *LevelEffectConfig) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

type CustomMethod struct {
	Options              []*CustomOption `protobuf:"bytes,1,rep,name=options,proto3" json:"options,omitempty"`
	EffectUrl            string          `protobuf:"bytes,2,opt,name=effect_url,json=effectUrl,proto3" json:"effect_url,omitempty"`
	EffectMd5            string          `protobuf:"bytes,3,opt,name=effect_md5,json=effectMd5,proto3" json:"effect_md5,omitempty"`
	PreviewType          uint32          `protobuf:"varint,4,opt,name=preview_type,json=previewType,proto3" json:"preview_type,omitempty"`
	PreviewUrl           string          `protobuf:"bytes,5,opt,name=preview_url,json=previewUrl,proto3" json:"preview_url,omitempty"`
	PreviewMd5           string          `protobuf:"bytes,6,opt,name=preview_md5,json=previewMd5,proto3" json:"preview_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CustomMethod) Reset()         { *m = CustomMethod{} }
func (m *CustomMethod) String() string { return proto.CompactTextString(m) }
func (*CustomMethod) ProtoMessage()    {}
func (*CustomMethod) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{31}
}
func (m *CustomMethod) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomMethod.Unmarshal(m, b)
}
func (m *CustomMethod) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomMethod.Marshal(b, m, deterministic)
}
func (dst *CustomMethod) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomMethod.Merge(dst, src)
}
func (m *CustomMethod) XXX_Size() int {
	return xxx_messageInfo_CustomMethod.Size(m)
}
func (m *CustomMethod) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomMethod.DiscardUnknown(m)
}

var xxx_messageInfo_CustomMethod proto.InternalMessageInfo

func (m *CustomMethod) GetOptions() []*CustomOption {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *CustomMethod) GetEffectUrl() string {
	if m != nil {
		return m.EffectUrl
	}
	return ""
}

func (m *CustomMethod) GetEffectMd5() string {
	if m != nil {
		return m.EffectMd5
	}
	return ""
}

func (m *CustomMethod) GetPreviewType() uint32 {
	if m != nil {
		return m.PreviewType
	}
	return 0
}

func (m *CustomMethod) GetPreviewUrl() string {
	if m != nil {
		return m.PreviewUrl
	}
	return ""
}

func (m *CustomMethod) GetPreviewMd5() string {
	if m != nil {
		return m.PreviewMd5
	}
	return ""
}

type CustomOption struct {
	CustomId             uint32   `protobuf:"varint,1,opt,name=custom_id,json=customId,proto3" json:"custom_id,omitempty"`
	OptionId             uint32   `protobuf:"varint,2,opt,name=option_id,json=optionId,proto3" json:"option_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomOption) Reset()         { *m = CustomOption{} }
func (m *CustomOption) String() string { return proto.CompactTextString(m) }
func (*CustomOption) ProtoMessage()    {}
func (*CustomOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{32}
}
func (m *CustomOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomOption.Unmarshal(m, b)
}
func (m *CustomOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomOption.Marshal(b, m, deterministic)
}
func (dst *CustomOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomOption.Merge(dst, src)
}
func (m *CustomOption) XXX_Size() int {
	return xxx_messageInfo_CustomOption.Size(m)
}
func (m *CustomOption) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomOption.DiscardUnknown(m)
}

var xxx_messageInfo_CustomOption proto.InternalMessageInfo

func (m *CustomOption) GetCustomId() uint32 {
	if m != nil {
		return m.CustomId
	}
	return 0
}

func (m *CustomOption) GetOptionId() uint32 {
	if m != nil {
		return m.OptionId
	}
	return 0
}

// 更新礼物配置
type UpdateCustomizedPresentConfigReq struct {
	Config               *CustomizedPresentConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *UpdateCustomizedPresentConfigReq) Reset()         { *m = UpdateCustomizedPresentConfigReq{} }
func (m *UpdateCustomizedPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCustomizedPresentConfigReq) ProtoMessage()    {}
func (*UpdateCustomizedPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{33}
}
func (m *UpdateCustomizedPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCustomizedPresentConfigReq.Unmarshal(m, b)
}
func (m *UpdateCustomizedPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCustomizedPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateCustomizedPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCustomizedPresentConfigReq.Merge(dst, src)
}
func (m *UpdateCustomizedPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateCustomizedPresentConfigReq.Size(m)
}
func (m *UpdateCustomizedPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCustomizedPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCustomizedPresentConfigReq proto.InternalMessageInfo

func (m *UpdateCustomizedPresentConfigReq) GetConfig() *CustomizedPresentConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

// 更新礼物配置
type UpdateCustomizedPresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCustomizedPresentConfigResp) Reset()         { *m = UpdateCustomizedPresentConfigResp{} }
func (m *UpdateCustomizedPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCustomizedPresentConfigResp) ProtoMessage()    {}
func (*UpdateCustomizedPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{34}
}
func (m *UpdateCustomizedPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCustomizedPresentConfigResp.Unmarshal(m, b)
}
func (m *UpdateCustomizedPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCustomizedPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateCustomizedPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCustomizedPresentConfigResp.Merge(dst, src)
}
func (m *UpdateCustomizedPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateCustomizedPresentConfigResp.Size(m)
}
func (m *UpdateCustomizedPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCustomizedPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCustomizedPresentConfigResp proto.InternalMessageInfo

// 获取礼物配置
type GetAllCustomizedPresentConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllCustomizedPresentConfigReq) Reset()         { *m = GetAllCustomizedPresentConfigReq{} }
func (m *GetAllCustomizedPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetAllCustomizedPresentConfigReq) ProtoMessage()    {}
func (*GetAllCustomizedPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{35}
}
func (m *GetAllCustomizedPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllCustomizedPresentConfigReq.Unmarshal(m, b)
}
func (m *GetAllCustomizedPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllCustomizedPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetAllCustomizedPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllCustomizedPresentConfigReq.Merge(dst, src)
}
func (m *GetAllCustomizedPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetAllCustomizedPresentConfigReq.Size(m)
}
func (m *GetAllCustomizedPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllCustomizedPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllCustomizedPresentConfigReq proto.InternalMessageInfo

type GetAllCustomizedPresentConfigResp struct {
	Config               []*CustomizedPresentConfig `protobuf:"bytes,1,rep,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetAllCustomizedPresentConfigResp) Reset()         { *m = GetAllCustomizedPresentConfigResp{} }
func (m *GetAllCustomizedPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetAllCustomizedPresentConfigResp) ProtoMessage()    {}
func (*GetAllCustomizedPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{36}
}
func (m *GetAllCustomizedPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllCustomizedPresentConfigResp.Unmarshal(m, b)
}
func (m *GetAllCustomizedPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllCustomizedPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetAllCustomizedPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllCustomizedPresentConfigResp.Merge(dst, src)
}
func (m *GetAllCustomizedPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetAllCustomizedPresentConfigResp.Size(m)
}
func (m *GetAllCustomizedPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllCustomizedPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllCustomizedPresentConfigResp proto.InternalMessageInfo

func (m *GetAllCustomizedPresentConfigResp) GetConfig() []*CustomizedPresentConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

// 删除礼物配置
type DelCustomizedPresentConfigReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCustomizedPresentConfigReq) Reset()         { *m = DelCustomizedPresentConfigReq{} }
func (m *DelCustomizedPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelCustomizedPresentConfigReq) ProtoMessage()    {}
func (*DelCustomizedPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{37}
}
func (m *DelCustomizedPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCustomizedPresentConfigReq.Unmarshal(m, b)
}
func (m *DelCustomizedPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCustomizedPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelCustomizedPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCustomizedPresentConfigReq.Merge(dst, src)
}
func (m *DelCustomizedPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelCustomizedPresentConfigReq.Size(m)
}
func (m *DelCustomizedPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCustomizedPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelCustomizedPresentConfigReq proto.InternalMessageInfo

func (m *DelCustomizedPresentConfigReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelCustomizedPresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCustomizedPresentConfigResp) Reset()         { *m = DelCustomizedPresentConfigResp{} }
func (m *DelCustomizedPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelCustomizedPresentConfigResp) ProtoMessage()    {}
func (*DelCustomizedPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{38}
}
func (m *DelCustomizedPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCustomizedPresentConfigResp.Unmarshal(m, b)
}
func (m *DelCustomizedPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCustomizedPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelCustomizedPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCustomizedPresentConfigResp.Merge(dst, src)
}
func (m *DelCustomizedPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelCustomizedPresentConfigResp.Size(m)
}
func (m *DelCustomizedPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCustomizedPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelCustomizedPresentConfigResp proto.InternalMessageInfo

type TicketConfig struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ChannelIdList        []uint32 `protobuf:"varint,2,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	BackpackTopText      string   `protobuf:"bytes,3,opt,name=backpack_top_text,json=backpackTopText,proto3" json:"backpack_top_text,omitempty"`
	HomepageText         string   `protobuf:"bytes,4,opt,name=homepage_text,json=homepageText,proto3" json:"homepage_text,omitempty"`
	LastOperatorTime     uint32   `protobuf:"varint,5,opt,name=last_operator_time,json=lastOperatorTime,proto3" json:"last_operator_time,omitempty"`
	ImPushMsg            string   `protobuf:"bytes,6,opt,name=im_push_msg,json=imPushMsg,proto3" json:"im_push_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TicketConfig) Reset()         { *m = TicketConfig{} }
func (m *TicketConfig) String() string { return proto.CompactTextString(m) }
func (*TicketConfig) ProtoMessage()    {}
func (*TicketConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{39}
}
func (m *TicketConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketConfig.Unmarshal(m, b)
}
func (m *TicketConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketConfig.Marshal(b, m, deterministic)
}
func (dst *TicketConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketConfig.Merge(dst, src)
}
func (m *TicketConfig) XXX_Size() int {
	return xxx_messageInfo_TicketConfig.Size(m)
}
func (m *TicketConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketConfig.DiscardUnknown(m)
}

var xxx_messageInfo_TicketConfig proto.InternalMessageInfo

func (m *TicketConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TicketConfig) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *TicketConfig) GetBackpackTopText() string {
	if m != nil {
		return m.BackpackTopText
	}
	return ""
}

func (m *TicketConfig) GetHomepageText() string {
	if m != nil {
		return m.HomepageText
	}
	return ""
}

func (m *TicketConfig) GetLastOperatorTime() uint32 {
	if m != nil {
		return m.LastOperatorTime
	}
	return 0
}

func (m *TicketConfig) GetImPushMsg() string {
	if m != nil {
		return m.ImPushMsg
	}
	return ""
}

type TicketPresentConfig struct {
	Config               *StPresentItemConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	TicketConfig         *TicketConfig        `protobuf:"bytes,2,opt,name=ticket_config,json=ticketConfig,proto3" json:"ticket_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *TicketPresentConfig) Reset()         { *m = TicketPresentConfig{} }
func (m *TicketPresentConfig) String() string { return proto.CompactTextString(m) }
func (*TicketPresentConfig) ProtoMessage()    {}
func (*TicketPresentConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{40}
}
func (m *TicketPresentConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketPresentConfig.Unmarshal(m, b)
}
func (m *TicketPresentConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketPresentConfig.Marshal(b, m, deterministic)
}
func (dst *TicketPresentConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketPresentConfig.Merge(dst, src)
}
func (m *TicketPresentConfig) XXX_Size() int {
	return xxx_messageInfo_TicketPresentConfig.Size(m)
}
func (m *TicketPresentConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketPresentConfig.DiscardUnknown(m)
}

var xxx_messageInfo_TicketPresentConfig proto.InternalMessageInfo

func (m *TicketPresentConfig) GetConfig() *StPresentItemConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

func (m *TicketPresentConfig) GetTicketConfig() *TicketConfig {
	if m != nil {
		return m.TicketConfig
	}
	return nil
}

// 增加礼物配置
type AddTicketPresentConfigReq struct {
	TicketPresentConfig  *TicketPresentConfig `protobuf:"bytes,1,opt,name=ticket_present_config,json=ticketPresentConfig,proto3" json:"ticket_present_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AddTicketPresentConfigReq) Reset()         { *m = AddTicketPresentConfigReq{} }
func (m *AddTicketPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddTicketPresentConfigReq) ProtoMessage()    {}
func (*AddTicketPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{41}
}
func (m *AddTicketPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTicketPresentConfigReq.Unmarshal(m, b)
}
func (m *AddTicketPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTicketPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddTicketPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTicketPresentConfigReq.Merge(dst, src)
}
func (m *AddTicketPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddTicketPresentConfigReq.Size(m)
}
func (m *AddTicketPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTicketPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddTicketPresentConfigReq proto.InternalMessageInfo

func (m *AddTicketPresentConfigReq) GetTicketPresentConfig() *TicketPresentConfig {
	if m != nil {
		return m.TicketPresentConfig
	}
	return nil
}

type AddTicketPresentConfigResp struct {
	TicketPresentConfig  *TicketPresentConfig `protobuf:"bytes,1,opt,name=ticket_present_config,json=ticketPresentConfig,proto3" json:"ticket_present_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AddTicketPresentConfigResp) Reset()         { *m = AddTicketPresentConfigResp{} }
func (m *AddTicketPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddTicketPresentConfigResp) ProtoMessage()    {}
func (*AddTicketPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{42}
}
func (m *AddTicketPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTicketPresentConfigResp.Unmarshal(m, b)
}
func (m *AddTicketPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTicketPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddTicketPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTicketPresentConfigResp.Merge(dst, src)
}
func (m *AddTicketPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddTicketPresentConfigResp.Size(m)
}
func (m *AddTicketPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTicketPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddTicketPresentConfigResp proto.InternalMessageInfo

func (m *AddTicketPresentConfigResp) GetTicketPresentConfig() *TicketPresentConfig {
	if m != nil {
		return m.TicketPresentConfig
	}
	return nil
}

// 更新礼物配置
type UpdateTicketPresentConfigReq struct {
	TicketPresentConfig  *TicketPresentConfig `protobuf:"bytes,1,opt,name=ticket_present_config,json=ticketPresentConfig,proto3" json:"ticket_present_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdateTicketPresentConfigReq) Reset()         { *m = UpdateTicketPresentConfigReq{} }
func (m *UpdateTicketPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateTicketPresentConfigReq) ProtoMessage()    {}
func (*UpdateTicketPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{43}
}
func (m *UpdateTicketPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTicketPresentConfigReq.Unmarshal(m, b)
}
func (m *UpdateTicketPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTicketPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateTicketPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTicketPresentConfigReq.Merge(dst, src)
}
func (m *UpdateTicketPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateTicketPresentConfigReq.Size(m)
}
func (m *UpdateTicketPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTicketPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTicketPresentConfigReq proto.InternalMessageInfo

func (m *UpdateTicketPresentConfigReq) GetTicketPresentConfig() *TicketPresentConfig {
	if m != nil {
		return m.TicketPresentConfig
	}
	return nil
}

type UpdateTicketPresentConfigResp struct {
	TicketPresentConfig  *TicketPresentConfig `protobuf:"bytes,1,opt,name=ticket_present_config,json=ticketPresentConfig,proto3" json:"ticket_present_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdateTicketPresentConfigResp) Reset()         { *m = UpdateTicketPresentConfigResp{} }
func (m *UpdateTicketPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateTicketPresentConfigResp) ProtoMessage()    {}
func (*UpdateTicketPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{44}
}
func (m *UpdateTicketPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTicketPresentConfigResp.Unmarshal(m, b)
}
func (m *UpdateTicketPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTicketPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateTicketPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTicketPresentConfigResp.Merge(dst, src)
}
func (m *UpdateTicketPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateTicketPresentConfigResp.Size(m)
}
func (m *UpdateTicketPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTicketPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTicketPresentConfigResp proto.InternalMessageInfo

func (m *UpdateTicketPresentConfigResp) GetTicketPresentConfig() *TicketPresentConfig {
	if m != nil {
		return m.TicketPresentConfig
	}
	return nil
}

// 获取礼物配置
type GetAllTicketPresentConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllTicketPresentConfigReq) Reset()         { *m = GetAllTicketPresentConfigReq{} }
func (m *GetAllTicketPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetAllTicketPresentConfigReq) ProtoMessage()    {}
func (*GetAllTicketPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{45}
}
func (m *GetAllTicketPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllTicketPresentConfigReq.Unmarshal(m, b)
}
func (m *GetAllTicketPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllTicketPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetAllTicketPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllTicketPresentConfigReq.Merge(dst, src)
}
func (m *GetAllTicketPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetAllTicketPresentConfigReq.Size(m)
}
func (m *GetAllTicketPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllTicketPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllTicketPresentConfigReq proto.InternalMessageInfo

type GetAllTicketPresentConfigResp struct {
	Config               []*TicketPresentConfig `protobuf:"bytes,1,rep,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetAllTicketPresentConfigResp) Reset()         { *m = GetAllTicketPresentConfigResp{} }
func (m *GetAllTicketPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetAllTicketPresentConfigResp) ProtoMessage()    {}
func (*GetAllTicketPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{46}
}
func (m *GetAllTicketPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllTicketPresentConfigResp.Unmarshal(m, b)
}
func (m *GetAllTicketPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllTicketPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetAllTicketPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllTicketPresentConfigResp.Merge(dst, src)
}
func (m *GetAllTicketPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetAllTicketPresentConfigResp.Size(m)
}
func (m *GetAllTicketPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllTicketPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllTicketPresentConfigResp proto.InternalMessageInfo

func (m *GetAllTicketPresentConfigResp) GetConfig() []*TicketPresentConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

// 删除礼物配置
type DelTicketPresentConfigReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTicketPresentConfigReq) Reset()         { *m = DelTicketPresentConfigReq{} }
func (m *DelTicketPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelTicketPresentConfigReq) ProtoMessage()    {}
func (*DelTicketPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{47}
}
func (m *DelTicketPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTicketPresentConfigReq.Unmarshal(m, b)
}
func (m *DelTicketPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTicketPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelTicketPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTicketPresentConfigReq.Merge(dst, src)
}
func (m *DelTicketPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelTicketPresentConfigReq.Size(m)
}
func (m *DelTicketPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTicketPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelTicketPresentConfigReq proto.InternalMessageInfo

func (m *DelTicketPresentConfigReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelTicketPresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTicketPresentConfigResp) Reset()         { *m = DelTicketPresentConfigResp{} }
func (m *DelTicketPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelTicketPresentConfigResp) ProtoMessage()    {}
func (*DelTicketPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_api_5cd2682293252952, []int{48}
}
func (m *DelTicketPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTicketPresentConfigResp.Unmarshal(m, b)
}
func (m *DelTicketPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTicketPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelTicketPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTicketPresentConfigResp.Merge(dst, src)
}
func (m *DelTicketPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelTicketPresentConfigResp.Size(m)
}
func (m *DelTicketPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTicketPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelTicketPresentConfigResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*StConfigIosExtend)(nil), "apicentergo.StConfigIosExtend")
	proto.RegisterType((*StPresentItemConfigExtend)(nil), "apicentergo.StPresentItemConfigExtend")
	proto.RegisterType((*CustomText)(nil), "apicentergo.CustomText")
	proto.RegisterType((*StPresentItemConfig)(nil), "apicentergo.StPresentItemConfig")
	proto.RegisterType((*FellowPresentConfig)(nil), "apicentergo.FellowPresentConfig")
	proto.RegisterType((*GetPresentConfigByIdReq)(nil), "apicentergo.GetPresentConfigByIdReq")
	proto.RegisterType((*GetPresentConfigByIdResp)(nil), "apicentergo.GetPresentConfigByIdResp")
	proto.RegisterType((*GetPresentConfigListReq)(nil), "apicentergo.GetPresentConfigListReq")
	proto.RegisterType((*GetPresentConfigListResp)(nil), "apicentergo.GetPresentConfigListResp")
	proto.RegisterType((*AddPresentConfigReq)(nil), "apicentergo.AddPresentConfigReq")
	proto.RegisterType((*EffectDelayLevelInfo)(nil), "apicentergo.EffectDelayLevelInfo")
	proto.RegisterType((*AddPresentConfigResp)(nil), "apicentergo.AddPresentConfigResp")
	proto.RegisterType((*DelPresentConfigReq)(nil), "apicentergo.DelPresentConfigReq")
	proto.RegisterType((*DelPresentConfigResp)(nil), "apicentergo.DelPresentConfigResp")
	proto.RegisterType((*UpdatePresentConfigReq)(nil), "apicentergo.UpdatePresentConfigReq")
	proto.RegisterType((*UpdatePresentConfigResp)(nil), "apicentergo.UpdatePresentConfigResp")
	proto.RegisterType((*UserPresentSend)(nil), "apicentergo.UserPresentSend")
	proto.RegisterType((*UserPresentReceive)(nil), "apicentergo.UserPresentReceive")
	proto.RegisterType((*GetUserPresentSendReq)(nil), "apicentergo.GetUserPresentSendReq")
	proto.RegisterType((*GetUserPresentSendResp)(nil), "apicentergo.GetUserPresentSendResp")
	proto.RegisterType((*GetUserPresentReceiveReq)(nil), "apicentergo.GetUserPresentReceiveReq")
	proto.RegisterType((*GetUserPresentReceiveResp)(nil), "apicentergo.GetUserPresentReceiveResp")
	proto.RegisterType((*GetAllFellowPresentReq)(nil), "apicentergo.GetAllFellowPresentReq")
	proto.RegisterType((*GetAllFellowPresentResp)(nil), "apicentergo.GetAllFellowPresentResp")
	proto.RegisterType((*AddCustomizedPresentConfigReq)(nil), "apicentergo.AddCustomizedPresentConfigReq")
	proto.RegisterType((*AddCustomizedPresentConfigResp)(nil), "apicentergo.AddCustomizedPresentConfigResp")
	proto.RegisterType((*CustomizedPresentConfig)(nil), "apicentergo.CustomizedPresentConfig")
	proto.RegisterType((*CustomConfig)(nil), "apicentergo.CustomConfig")
	proto.RegisterType((*LevelConfig)(nil), "apicentergo.LevelConfig")
	proto.RegisterType((*OptionConfig)(nil), "apicentergo.OptionConfig")
	proto.RegisterType((*LevelEffectConfig)(nil), "apicentergo.LevelEffectConfig")
	proto.RegisterType((*CustomMethod)(nil), "apicentergo.CustomMethod")
	proto.RegisterType((*CustomOption)(nil), "apicentergo.CustomOption")
	proto.RegisterType((*UpdateCustomizedPresentConfigReq)(nil), "apicentergo.UpdateCustomizedPresentConfigReq")
	proto.RegisterType((*UpdateCustomizedPresentConfigResp)(nil), "apicentergo.UpdateCustomizedPresentConfigResp")
	proto.RegisterType((*GetAllCustomizedPresentConfigReq)(nil), "apicentergo.GetAllCustomizedPresentConfigReq")
	proto.RegisterType((*GetAllCustomizedPresentConfigResp)(nil), "apicentergo.GetAllCustomizedPresentConfigResp")
	proto.RegisterType((*DelCustomizedPresentConfigReq)(nil), "apicentergo.DelCustomizedPresentConfigReq")
	proto.RegisterType((*DelCustomizedPresentConfigResp)(nil), "apicentergo.DelCustomizedPresentConfigResp")
	proto.RegisterType((*TicketConfig)(nil), "apicentergo.TicketConfig")
	proto.RegisterType((*TicketPresentConfig)(nil), "apicentergo.TicketPresentConfig")
	proto.RegisterType((*AddTicketPresentConfigReq)(nil), "apicentergo.AddTicketPresentConfigReq")
	proto.RegisterType((*AddTicketPresentConfigResp)(nil), "apicentergo.AddTicketPresentConfigResp")
	proto.RegisterType((*UpdateTicketPresentConfigReq)(nil), "apicentergo.UpdateTicketPresentConfigReq")
	proto.RegisterType((*UpdateTicketPresentConfigResp)(nil), "apicentergo.UpdateTicketPresentConfigResp")
	proto.RegisterType((*GetAllTicketPresentConfigReq)(nil), "apicentergo.GetAllTicketPresentConfigReq")
	proto.RegisterType((*GetAllTicketPresentConfigResp)(nil), "apicentergo.GetAllTicketPresentConfigResp")
	proto.RegisterType((*DelTicketPresentConfigReq)(nil), "apicentergo.DelTicketPresentConfigReq")
	proto.RegisterType((*DelTicketPresentConfigResp)(nil), "apicentergo.DelTicketPresentConfigResp")
	proto.RegisterEnum("apicentergo.PresentPriceType", PresentPriceType_name, PresentPriceType_value)
	proto.RegisterEnum("apicentergo.ConfigListTypeBitMap", ConfigListTypeBitMap_name, ConfigListTypeBitMap_value)
}

func init() {
	proto.RegisterFile("apicenter-go/userpresent-api.proto", fileDescriptor_userpresent_api_5cd2682293252952)
}

var fileDescriptor_userpresent_api_5cd2682293252952 = []byte{
	// 2527 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x19, 0x4b, 0x6f, 0x1b, 0xc7,
	0x39, 0x24, 0x25, 0x8a, 0xfb, 0x91, 0x94, 0xa8, 0xa5, 0x1e, 0x54, 0x6c, 0xd9, 0xd2, 0x26, 0x35,
	0x04, 0x37, 0x96, 0x02, 0x05, 0x06, 0x0c, 0xb4, 0x0e, 0x2a, 0x89, 0xb4, 0x4b, 0x54, 0x2f, 0xac,
	0xa8, 0xb4, 0x76, 0x90, 0x6e, 0x57, 0xbb, 0x43, 0x6a, 0xa0, 0x7d, 0x79, 0x67, 0x28, 0x4b, 0x39,
	0xf7, 0xde, 0x5b, 0xff, 0x40, 0x6f, 0x45, 0x7f, 0x42, 0xff, 0x4e, 0x7b, 0x2b, 0x7a, 0xee, 0xad,
	0x98, 0x6f, 0x66, 0xc9, 0x5d, 0x3e, 0xa4, 0x26, 0xb1, 0x7b, 0xe3, 0x7c, 0xef, 0xf7, 0x7c, 0xb3,
	0x04, 0xc3, 0x8e, 0xa8, 0x43, 0x02, 0x4e, 0xe2, 0x67, 0xbd, 0x70, 0xa7, 0xcf, 0x48, 0x1c, 0xc5,
	0x84, 0x91, 0x80, 0x3f, 0xb3, 0x23, 0xba, 0x1d, 0xc5, 0x21, 0x0f, 0xf5, 0xf2, 0x80, 0xa6, 0x17,
	0x1a, 0x2f, 0x61, 0xf1, 0x8c, 0x1f, 0x84, 0x41, 0x97, 0xf6, 0xda, 0x21, 0x6b, 0xdd, 0x70, 0x12,
	0xb8, 0xfa, 0x16, 0xd4, 0xae, 0xa9, 0x4b, 0x42, 0x8b, 0x74, 0xbb, 0xc4, 0xe1, 0x56, 0x3f, 0xf6,
	0x1a, 0xb9, 0x8d, 0xdc, 0x56, 0xc5, 0x9c, 0x47, 0x78, 0x0b, 0xc1, 0xe7, 0xb1, 0x67, 0xfc, 0xa7,
	0x08, 0x6b, 0x67, 0xfc, 0x54, 0xea, 0x68, 0x73, 0xe2, 0x4b, 0x51, 0x4a, 0xce, 0x2a, 0xcc, 0x51,
	0x4e, 0x7c, 0x8b, 0xba, 0xc8, 0x5e, 0x35, 0x8b, 0xe2, 0xd8, 0x9e, 0xac, 0x20, 0x3f, 0x49, 0x81,
	0xfe, 0x18, 0xca, 0xec, 0x32, 0x7c, 0xaf, 0x08, 0x1b, 0x05, 0x14, 0x03, 0x02, 0x24, 0x69, 0xf4,
	0x6d, 0xa8, 0xf7, 0x03, 0x24, 0xb9, 0xb0, 0xb9, 0x73, 0x69, 0x85, 0x11, 0xa7, 0x61, 0xd0, 0x98,
	0xd9, 0xc8, 0x6d, 0x95, 0xcc, 0x45, 0x89, 0xda, 0x17, 0x98, 0x13, 0x44, 0xa0, 0x4d, 0xcc, 0xe2,
	0x84, 0xf1, 0xc6, 0x2c, 0xd2, 0x14, 0x29, 0xeb, 0x10, 0xc6, 0x05, 0xa2, 0xeb, 0x85, 0xef, 0x85,
	0xb1, 0x45, 0x69, 0xac, 0x38, 0xb6, 0x5d, 0xfd, 0x25, 0x00, 0x0d, 0x99, 0x45, 0xd0, 0xa7, 0xc6,
	0xdc, 0x46, 0x6e, 0xab, 0xbc, 0xfb, 0x68, 0x3b, 0x15, 0xc4, 0xed, 0xb1, 0x08, 0x9a, 0x1a, 0x1d,
	0x04, 0x73, 0x1d, 0x20, 0x08, 0x39, 0xed, 0xde, 0x5a, 0xb6, 0xe7, 0x35, 0x4a, 0xa8, 0x53, 0x93,
	0x90, 0x3d, 0xcf, 0xd3, 0x6b, 0x50, 0xe0, 0x76, 0xaf, 0xa1, 0xa1, 0x4a, 0xf1, 0x53, 0xff, 0x19,
	0xcc, 0x77, 0xc3, 0xd8, 0x21, 0x16, 0x23, 0x81, 0x6b, 0x5f, 0x78, 0xa4, 0x01, 0xc8, 0x54, 0x45,
	0xe8, 0x99, 0x02, 0x0a, 0xb2, 0x20, 0xbc, 0xa0, 0x1e, 0xe5, 0xb7, 0x96, 0x47, 0xae, 0x89, 0xd7,
	0x28, 0xa3, 0x8c, 0x6a, 0x02, 0x3d, 0x14, 0x40, 0xfd, 0x4b, 0x58, 0x52, 0xf1, 0x51, 0x95, 0x60,
	0xb1, 0x4b, 0xe2, 0x75, 0x1b, 0x15, 0x94, 0xa9, 0x4b, 0x9c, 0x4a, 0xe0, 0x99, 0xc0, 0xe8, 0x4f,
	0x60, 0x21, 0x15, 0x72, 0x4b, 0x38, 0x5d, 0x95, 0x06, 0x0c, 0xc3, 0xde, 0x92, 0x55, 0x32, 0x24,
	0xb1, 0x5c, 0xe2, 0xd9, 0xb7, 0x8d, 0x79, 0x24, 0x9c, 0x27, 0x09, 0x51, 0x53, 0x40, 0xf5, 0x17,
	0x50, 0x76, 0xfa, 0x8c, 0x87, 0xbe, 0xc5, 0xc9, 0x0d, 0x6f, 0x2c, 0x6c, 0x14, 0xb6, 0xca, 0xbb,
	0xab, 0x99, 0x10, 0x1e, 0x20, 0xbe, 0x43, 0x6e, 0xb8, 0x09, 0xce, 0xe0, 0xb7, 0xfe, 0x39, 0xcc,
	0xfb, 0xd4, 0x49, 0x97, 0x49, 0x6d, 0x23, 0xb7, 0xa5, 0x99, 0x15, 0x9f, 0x3a, 0xc3, 0x22, 0xc9,
	0x52, 0xf9, 0xee, 0xf3, 0xc6, 0xe2, 0x08, 0xd5, 0x91, 0xfb, 0x5c, 0xdf, 0x81, 0x25, 0xe6, 0xdb,
	0x9e, 0x67, 0x5d, 0xdb, 0x51, 0x5a, 0xa2, 0x8e, 0xb4, 0x8b, 0x88, 0xfb, 0xc6, 0x8e, 0x86, 0x62,
	0x27, 0x31, 0x08, 0xe1, 0xf5, 0x49, 0x0c, 0x42, 0x83, 0xc8, 0x5c, 0x9f, 0xd1, 0x30, 0x48, 0x62,
	0xdd, 0x58, 0x52, 0x99, 0x43, 0xa8, 0x8a, 0xb2, 0x08, 0x30, 0x65, 0xd6, 0x45, 0x78, 0x63, 0x5d,
	0xc4, 0xc4, 0xbe, 0xa2, 0x41, 0xaf, 0xb1, 0x2c, 0xe9, 0x28, 0xdb, 0x0f, 0x6f, 0xf6, 0x15, 0x50,
	0x54, 0x4e, 0xd7, 0x0e, 0x98, 0xca, 0xee, 0x0a, 0x66, 0x57, 0x13, 0x10, 0xcc, 0xac, 0xb1, 0x0b,
	0x30, 0x8c, 0x9a, 0xa8, 0xa3, 0x2b, 0x72, 0x8b, 0x7d, 0xa6, 0x99, 0xe2, 0xa7, 0xae, 0xc3, 0x0c,
	0x86, 0x3b, 0xbf, 0x51, 0xd8, 0xd2, 0x4c, 0xfc, 0x6d, 0xfc, 0x71, 0x16, 0xea, 0x13, 0xfa, 0x75,
	0x7a, 0xa7, 0xea, 0x30, 0x13, 0xd8, 0x3e, 0xc1, 0xee, 0xd4, 0x4c, 0xfc, 0xad, 0xaf, 0x41, 0x89,
	0x3a, 0x61, 0x80, 0xc1, 0x2b, 0x20, 0x7c, 0x4e, 0x9c, 0x45, 0xc8, 0x96, 0x60, 0x36, 0x8a, 0xa9,
	0x43, 0xb0, 0xff, 0xaa, 0xa6, 0x3c, 0x08, 0x28, 0x73, 0xc2, 0x98, 0x60, 0xc7, 0x55, 0x4d, 0x79,
	0x10, 0x50, 0xe7, 0xd2, 0x8e, 0x7d, 0xd5, 0x6e, 0xf2, 0x20, 0x14, 0xc6, 0x76, 0x70, 0x85, 0x7d,
	0x56, 0x35, 0xf1, 0xb7, 0xbe, 0x09, 0x15, 0x15, 0xfe, 0x0b, 0xd2, 0xa3, 0x01, 0x36, 0x51, 0xd5,
	0x2c, 0x4b, 0xd8, 0xbe, 0x00, 0x89, 0x58, 0xa5, 0xea, 0x55, 0x76, 0x93, 0x36, 0x28, 0x43, 0x31,
	0x46, 0xfa, 0x91, 0x6b, 0x73, 0x62, 0x71, 0xea, 0xcb, 0x86, 0xaa, 0x9a, 0x20, 0x41, 0x1d, 0xea,
	0x13, 0x41, 0xe0, 0xc4, 0x64, 0x40, 0x20, 0x5b, 0x09, 0x24, 0x08, 0x09, 0x96, 0xa1, 0x48, 0x99,
	0xa8, 0x72, 0xd5, 0x39, 0xb3, 0x94, 0x35, 0x89, 0x27, 0xf4, 0xa2, 0x8f, 0x16, 0xbf, 0x8d, 0x08,
	0xf6, 0x49, 0xd5, 0xd4, 0x10, 0xd2, 0xb9, 0x8d, 0x88, 0x40, 0xc7, 0xd4, 0xb9, 0xb4, 0xae, 0x6d,
	0xaf, 0x4f, 0xb0, 0x3b, 0xaa, 0xa6, 0x26, 0x20, 0xdf, 0x08, 0x80, 0xfe, 0x35, 0x14, 0xd5, 0x58,
	0x59, 0xc0, 0xb1, 0xf2, 0x64, 0x64, 0xac, 0x4c, 0x19, 0xac, 0xa6, 0xe2, 0xd2, 0x5f, 0x40, 0xb1,
	0x4b, 0x3c, 0x2f, 0x7c, 0x8f, 0x6d, 0x51, 0xde, 0xdd, 0xc8, 0xf0, 0xbf, 0x42, 0x94, 0x92, 0x21,
	0xf9, 0x4d, 0x45, 0xaf, 0xff, 0x0a, 0x00, 0x3b, 0xd6, 0xa2, 0x41, 0x37, 0x6c, 0x2c, 0x62, 0x47,
	0x6e, 0x66, 0xb8, 0x65, 0x59, 0x63, 0x03, 0x63, 0xbd, 0xb5, 0x83, 0x6e, 0x68, 0x6a, 0xc8, 0x24,
	0x7e, 0xea, 0x0f, 0x40, 0x13, 0x55, 0x6c, 0x07, 0x01, 0x71, 0xb1, 0x87, 0x4a, 0x66, 0x89, 0xb2,
	0x7d, 0x3c, 0xa3, 0xdf, 0x76, 0x70, 0x65, 0x75, 0xbd, 0xd0, 0xe6, 0xd8, 0x30, 0x79, 0x53, 0x13,
	0x90, 0x57, 0x02, 0x60, 0xfc, 0xb9, 0x00, 0xf5, 0x09, 0xd6, 0xe9, 0xbb, 0xb0, 0xdc, 0x0f, 0xe8,
	0xbb, 0x3e, 0xb1, 0x2e, 0x6c, 0xe7, 0xaa, 0x17, 0x87, 0xfd, 0xc0, 0x1d, 0xdc, 0x3e, 0x9a, 0x59,
	0x97, 0xc8, 0xfd, 0x01, 0x4e, 0x94, 0xdc, 0x17, 0xa0, 0x2b, 0x1e, 0x16, 0xf6, 0xe3, 0x24, 0x13,
	0x79, 0x0c, 0x75, 0x4d, 0x62, 0xce, 0x10, 0x91, 0x24, 0x44, 0x51, 0x8b, 0x4e, 0x96, 0xd5, 0xab,
	0x49, 0x88, 0xe8, 0xe0, 0x2f, 0x61, 0xc9, 0xef, 0x7b, 0x9c, 0x8e, 0xea, 0x9f, 0x41, 0x42, 0x1d,
	0x71, 0x59, 0xf5, 0x4f, 0x61, 0x51, 0x72, 0xa4, 0xb5, 0xcb, 0x3a, 0x5f, 0x40, 0x44, 0x4a, 0xf9,
	0x03, 0xd0, 0x24, 0xad, 0xd0, 0x5d, 0x44, 0x91, 0x25, 0x04, 0x08, 0xd5, 0x13, 0x7d, 0xa7, 0x7e,
	0x0f, 0x3b, 0x61, 0x82, 0xef, 0x6d, 0xbf, 0x37, 0xd1, 0x5c, 0xc1, 0x52, 0x9a, 0x68, 0xae, 0xe0,
	0x58, 0x07, 0x50, 0x86, 0x7e, 0x4f, 0x23, 0xec, 0x13, 0xcd, 0xd4, 0x24, 0xe4, 0x2d, 0x8d, 0x8c,
	0x5d, 0x58, 0x7d, 0x4d, 0x78, 0x26, 0x29, 0xfb, 0xb7, 0x6d, 0xd7, 0x24, 0xef, 0xa6, 0x8e, 0x08,
	0xe3, 0x3b, 0x68, 0x4c, 0xe6, 0x61, 0x91, 0xbe, 0x07, 0x65, 0x64, 0x72, 0x10, 0x8c, 0x8c, 0xa3,
	0x55, 0x3a, 0xa1, 0xca, 0x4d, 0xa0, 0x83, 0xdf, 0xc6, 0xd9, 0xb8, 0x49, 0x87, 0x94, 0x71, 0x61,
	0xd2, 0x63, 0x28, 0x8b, 0x70, 0x5b, 0x17, 0x94, 0xfb, 0x76, 0xa4, 0xcc, 0x02, 0x01, 0xda, 0x47,
	0x88, 0xb0, 0xb9, 0x47, 0x38, 0x5e, 0xbc, 0x79, 0x79, 0xd9, 0xf7, 0x08, 0xdf, 0xf3, 0x3c, 0xe3,
	0xcd, 0xb8, 0xcd, 0x52, 0x28, 0x8b, 0xf4, 0x97, 0xa0, 0xa1, 0xcd, 0x1e, 0x65, 0xbc, 0x91, 0xc3,
	0xce, 0xb8, 0xdf, 0xe2, 0x92, 0x60, 0x11, 0x22, 0x8c, 0xbf, 0x16, 0xa0, 0xbe, 0xe7, 0xba, 0xd9,
	0xb6, 0x23, 0xef, 0x06, 0x93, 0x34, 0x37, 0x65, 0x92, 0xe6, 0xa7, 0x4c, 0xd2, 0x42, 0x7a, 0x92,
	0x8e, 0x4e, 0xc2, 0x99, 0xfb, 0x26, 0xe1, 0xec, 0xe8, 0x24, 0x4c, 0xe6, 0x6b, 0x31, 0x35, 0x5f,
	0xb3, 0x43, 0x6c, 0x6e, 0x74, 0x88, 0x0d, 0xa7, 0x54, 0xe9, 0x27, 0x4e, 0x29, 0xed, 0x27, 0x4d,
	0x29, 0xf8, 0x11, 0x53, 0x2a, 0x3b, 0x88, 0xca, 0xa3, 0x83, 0xe8, 0xef, 0x39, 0x58, 0x9a, 0x24,
	0x42, 0x84, 0x5f, 0x5e, 0xbb, 0xb2, 0xa8, 0xe4, 0x01, 0xbb, 0x47, 0x2c, 0x3b, 0x4e, 0xd8, 0x0f,
	0xb8, 0x9a, 0x31, 0x9a, 0x80, 0x1c, 0x08, 0x80, 0xe8, 0x6f, 0xd7, 0xbe, 0x55, 0x58, 0x99, 0xb7,
	0x92, 0x6b, 0xdf, 0x4a, 0xa4, 0x58, 0x97, 0x6e, 0x22, 0x1a, 0x13, 0x6b, 0x48, 0x23, 0xd3, 0x37,
	0x2f, 0xe1, 0xcd, 0x14, 0xa5, 0xd8, 0x0f, 0x9d, 0x34, 0xa5, 0xcc, 0xe3, 0xbc, 0x84, 0x27, 0x94,
	0xc6, 0x1b, 0x58, 0x1a, 0x2f, 0xb5, 0x0f, 0xd3, 0x76, 0xdb, 0x50, 0x6f, 0x12, 0x6f, 0xac, 0x8a,
	0xa7, 0x4e, 0x81, 0x15, 0x58, 0x1a, 0xa7, 0x67, 0x91, 0xf1, 0x2d, 0xac, 0x9c, 0xe3, 0x35, 0x3b,
	0x26, 0xea, 0x03, 0x18, 0xb9, 0x06, 0xab, 0x13, 0x85, 0xb3, 0xc8, 0xf8, 0x77, 0x0e, 0x16, 0xce,
	0x19, 0x89, 0x93, 0xd5, 0x56, 0x14, 0xe2, 0x32, 0x14, 0x79, 0x68, 0xf5, 0x07, 0xb6, 0xcf, 0xf2,
	0xf0, 0x9c, 0xe2, 0x33, 0x85, 0x87, 0x16, 0xe7, 0xd4, 0x55, 0x29, 0x2d, 0xf2, 0xb0, 0xc3, 0x29,
	0x6e, 0x0d, 0x3c, 0xb4, 0x02, 0xea, 0x5c, 0x61, 0xe7, 0xca, 0xdb, 0x02, 0x78, 0x78, 0xac, 0x20,
	0xe9, 0x68, 0xcc, 0x64, 0xd6, 0xa6, 0x07, 0x6a, 0x86, 0x20, 0xdf, 0xac, 0x9c, 0xf4, 0x02, 0x70,
	0x2c, 0xb8, 0xd6, 0x01, 0x94, 0xe3, 0x22, 0xb3, 0xb2, 0x11, 0x35, 0xe9, 0x95, 0x48, 0xff, 0x1a,
	0x94, 0x6c, 0xd7, 0xb5, 0xc4, 0x96, 0xa0, 0x7a, 0x71, 0xce, 0x76, 0x5d, 0x93, 0x3a, 0x97, 0x42,
	0x2c, 0xd6, 0x1f, 0xee, 0x28, 0x72, 0x0b, 0x2a, 0x09, 0x80, 0xd8, 0x50, 0x8c, 0xbf, 0xe4, 0x41,
	0x4f, 0x79, 0x6c, 0x12, 0x87, 0xd0, 0x6b, 0x9c, 0x31, 0xdd, 0x38, 0xf4, 0x53, 0x6e, 0xcf, 0x89,
	0xb3, 0x70, 0xfc, 0x01, 0x68, 0x88, 0x4a, 0xb9, 0x8e, 0xb4, 0xe8, 0xfc, 0x67, 0x50, 0x45, 0xe4,
	0x88, 0xfb, 0x15, 0x01, 0xfc, 0xb8, 0x01, 0x78, 0x00, 0x9a, 0x08, 0x80, 0x5c, 0x0e, 0x65, 0x04,
	0x44, 0x44, 0x0e, 0x70, 0x3f, 0x54, 0x48, 0xb9, 0x4f, 0x96, 0x06, 0xc8, 0x33, 0x5c, 0x29, 0x37,
	0xa1, 0x12, 0x4b, 0xb7, 0x65, 0x88, 0xe4, 0x1e, 0x58, 0x56, 0x30, 0x8c, 0xd2, 0xb7, 0xb0, 0xfc,
	0x9a, 0xf0, 0x91, 0xca, 0x10, 0xe5, 0x58, 0x83, 0xc2, 0x30, 0x44, 0xe2, 0xa7, 0x88, 0x1c, 0x4e,
	0x59, 0x8b, 0x33, 0x15, 0x9d, 0x39, 0x3c, 0x77, 0x98, 0xa8, 0x24, 0xcc, 0x03, 0x4b, 0xc6, 0xb3,
	0x48, 0x02, 0x33, 0xba, 0xb0, 0x32, 0x49, 0x38, 0x8b, 0xf4, 0x43, 0xa8, 0x0f, 0xde, 0x5f, 0xf2,
	0xb9, 0xc4, 0x6d, 0xea, 0xa9, 0xeb, 0xe5, 0x61, 0xa6, 0xe8, 0x47, 0xd9, 0x17, 0xa3, 0xe1, 0xa1,
	0x89, 0x6c, 0xc6, 0xef, 0xf1, 0xfa, 0x1a, 0x4f, 0xf6, 0x87, 0xf2, 0x23, 0x86, 0xb5, 0x29, 0xf2,
	0x59, 0xa4, 0x9f, 0xc3, 0x4a, 0xe2, 0x4a, 0x12, 0xec, 0x8c, 0x37, 0x8f, 0xa7, 0x79, 0x93, 0x08,
	0x59, 0x8a, 0x32, 0x67, 0xe5, 0x53, 0x03, 0x63, 0xb7, 0xe7, 0x79, 0x99, 0x0b, 0xc1, 0x24, 0xef,
	0x8c, 0x2b, 0xdc, 0x00, 0xc6, 0x31, 0x2c, 0xd2, 0x4f, 0xa1, 0x2e, 0xaf, 0x8a, 0xc1, 0xeb, 0xf6,
	0x07, 0xdd, 0xda, 0x8b, 0xdd, 0xb4, 0x44, 0xbc, 0xbe, 0xbf, 0x83, 0xf5, 0x3d, 0xd7, 0x95, 0x0f,
	0x2b, 0xfa, 0x3d, 0x19, 0xbf, 0xc7, 0x7f, 0x09, 0xc5, 0xcc, 0xc4, 0xfa, 0x7c, 0xc2, 0x3b, 0x76,
	0x9c, 0x51, 0xf1, 0x18, 0x1b, 0xf0, 0xe8, 0x2e, 0xf1, 0x2c, 0x32, 0xfe, 0x35, 0x0b, 0xab, 0x53,
	0xf0, 0xb8, 0xcf, 0xd0, 0x2e, 0x4f, 0x4d, 0x5f, 0x71, 0xfc, 0x50, 0xcf, 0xb4, 0x64, 0x35, 0x98,
	0x9d, 0xba, 0x1a, 0x14, 0xa7, 0xaf, 0x06, 0x73, 0x3f, 0x6a, 0x35, 0x58, 0x85, 0x39, 0xc7, 0x97,
	0x2b, 0xb6, 0xdc, 0x59, 0x8b, 0x8e, 0x8f, 0x6b, 0xb5, 0x58, 0x95, 0xed, 0x1b, 0xf5, 0xf4, 0x95,
	0x6d, 0x5c, 0xf2, 0xed, 0x1b, 0xf9, 0x4d, 0xe3, 0x00, 0xaa, 0x6a, 0xc5, 0x51, 0x99, 0x90, 0x9b,
	0x41, 0xf6, 0xa3, 0x0c, 0x92, 0xca, 0xbb, 0x5d, 0x45, 0x57, 0xad, 0x4e, 0x2a, 0x96, 0x5f, 0x43,
	0x55, 0x7d, 0x94, 0x50, 0x42, 0xca, 0x28, 0x64, 0x6d, 0x42, 0x3a, 0x13, 0x7e, 0x27, 0x75, 0xd2,
	0x7f, 0x01, 0x15, 0xb4, 0x2e, 0x61, 0xaf, 0x20, 0x7b, 0x63, 0xdc, 0x06, 0xc5, 0x5d, 0xf6, 0x86,
	0x87, 0x94, 0x72, 0x9f, 0xf0, 0xcb, 0xd0, 0x6d, 0x54, 0xa7, 0x2a, 0x3f, 0x42, 0x82, 0x44, 0xb9,
	0x3c, 0x89, 0xb4, 0x48, 0xe5, 0xf8, 0xc2, 0x9f, 0x97, 0x6b, 0x3c, 0x42, 0xf0, 0x63, 0xc0, 0xc8,
	0x73, 0x77, 0xe1, 0xbe, 0xe7, 0x6e, 0x6d, 0xec, 0xb9, 0x3b, 0x78, 0xb2, 0x2f, 0x4e, 0x7c, 0xb2,
	0xeb, 0xe9, 0x27, 0x7b, 0xf6, 0x91, 0x5b, 0x1f, 0x7d, 0xe4, 0x66, 0x57, 0xb0, 0xa5, 0xd1, 0x15,
	0xec, 0x6f, 0x39, 0xa8, 0xa4, 0xc3, 0x2c, 0x52, 0xaf, 0x62, 0x33, 0x28, 0xf3, 0x92, 0x04, 0xb4,
	0xf1, 0x4a, 0x56, 0xc8, 0x54, 0xbd, 0xab, 0x2f, 0x46, 0x78, 0xb7, 0x3c, 0xce, 0x7e, 0x6b, 0x2a,
	0xa4, 0x09, 0x30, 0x36, 0x2f, 0xa1, 0x22, 0x3f, 0x05, 0x4a, 0x75, 0x8d, 0x99, 0x09, 0x91, 0x4f,
	0x13, 0x98, 0x19, 0x72, 0xc3, 0x81, 0x72, 0x2a, 0xab, 0x53, 0xf6, 0xc4, 0x61, 0x7a, 0xd5, 0xe7,
	0xc8, 0xfc, 0xd4, 0xf4, 0x4a, 0xe1, 0x49, 0x7a, 0xe5, 0xc9, 0x38, 0xcc, 0xda, 0x28, 0x42, 0x22,
	0x05, 0xa5, 0x42, 0x22, 0x01, 0x32, 0x24, 0x0a, 0x99, 0x0e, 0x89, 0x04, 0x89, 0x90, 0x18, 0x57,
	0xb0, 0x38, 0xd6, 0x0c, 0x53, 0x0c, 0x1f, 0x7d, 0x5f, 0xe4, 0xef, 0x7b, 0x5f, 0x14, 0x46, 0xde,
	0x17, 0xc6, 0x3f, 0x07, 0xe9, 0x54, 0xa5, 0xfa, 0x15, 0xcc, 0x49, 0x5b, 0x98, 0x1a, 0xcb, 0x77,
	0x44, 0x21, 0xa1, 0x4c, 0x29, 0x19, 0x3e, 0x8d, 0x94, 0x12, 0x31, 0x1d, 0x86, 0xe8, 0xd4, 0x2b,
	0x9e, 0x0c, 0xbe, 0xc3, 0x6d, 0x42, 0x25, 0x8a, 0xc9, 0x35, 0x25, 0xef, 0xe5, 0xd8, 0x52, 0xaf,
	0x24, 0x05, 0xc3, 0xc1, 0xf5, 0x18, 0x92, 0x23, 0x6a, 0x90, 0x1b, 0x0a, 0x28, 0x90, 0xfa, 0xf0,
	0x9c, 0x10, 0x0c, 0x5f, 0xeb, 0x09, 0xc1, 0x91, 0xfb, 0xdc, 0xf8, 0x75, 0xe2, 0xa7, 0xfa, 0xb0,
	0x7c, 0x67, 0xd9, 0x66, 0x12, 0x98, 0xcf, 0x26, 0xd0, 0xf8, 0x03, 0x6c, 0xc8, 0x2d, 0xf6, 0xa3,
	0xdd, 0x3a, 0x9f, 0xc1, 0xe6, 0x3d, 0x1a, 0x58, 0x64, 0x18, 0xb0, 0x21, 0xaf, 0xd9, 0xe9, 0x66,
	0x18, 0x36, 0x6c, 0xde, 0x43, 0xc3, 0xa2, 0x8c, 0xad, 0x85, 0x1f, 0x6c, 0xeb, 0x0e, 0xac, 0x37,
	0xc9, 0x1d, 0x36, 0xe8, 0xf3, 0x90, 0x1f, 0x44, 0x38, 0x4f, 0x5d, 0x71, 0xa5, 0xde, 0xc5, 0xc0,
	0x22, 0xe3, 0x1f, 0x39, 0xa8, 0x74, 0xa8, 0x73, 0x45, 0x92, 0xe2, 0x1f, 0x11, 0xa1, 0x3f, 0x81,
	0x05, 0xe7, 0xd2, 0x0e, 0x02, 0xe2, 0x59, 0xd4, 0x95, 0x2b, 0x84, 0xe8, 0xd8, 0xaa, 0x59, 0x55,
	0xe0, 0xb6, 0x2b, 0x96, 0x03, 0xfd, 0x29, 0x2c, 0x5e, 0xd8, 0xce, 0x55, 0x64, 0x3b, 0x57, 0x16,
	0x0f, 0xa3, 0xf4, 0x88, 0x59, 0x48, 0x10, 0x9d, 0x30, 0xc2, 0x39, 0xf3, 0x19, 0x54, 0x2f, 0x43,
	0x9f, 0x44, 0x76, 0x8f, 0x48, 0x3a, 0xf9, 0x0d, 0xa9, 0x92, 0x00, 0x91, 0xe8, 0x0b, 0xd0, 0x3d,
	0x9b, 0x71, 0x2b, 0x8c, 0x48, 0x6c, 0xf3, 0x30, 0x96, 0xe3, 0x58, 0x5e, 0xc0, 0x35, 0x81, 0x39,
	0x51, 0x08, 0x1c, 0xca, 0x8f, 0xa0, 0x4c, 0x7d, 0x2b, 0xea, 0xb3, 0x4b, 0xcb, 0x67, 0x3d, 0x55,
	0x93, 0x1a, 0xf5, 0x4f, 0xfb, 0xec, 0xf2, 0x88, 0xf5, 0x8c, 0x3f, 0xe5, 0xa0, 0x2e, 0xfd, 0xcc,
	0xae, 0x0d, 0x2f, 0x46, 0x8a, 0xe7, 0xfe, 0xc5, 0x48, 0xd1, 0x8b, 0x41, 0xc6, 0x51, 0x60, 0x72,
	0xcb, 0xe5, 0x51, 0x40, 0xb6, 0x85, 0xd3, 0xa1, 0x35, 0x2b, 0x3c, 0x75, 0x32, 0xde, 0xc1, 0xda,
	0x9e, 0xeb, 0x4e, 0xb0, 0x49, 0x24, 0xb2, 0x03, 0xcb, 0x4a, 0x78, 0xb2, 0xbc, 0xdd, 0x61, 0xe5,
	0x24, 0x19, 0x75, 0x3e, 0x0e, 0x34, 0x62, 0xf8, 0x74, 0x9a, 0x4a, 0x16, 0x7d, 0x24, 0x9d, 0x1c,
	0x1e, 0x9e, 0xab, 0xcb, 0xf5, 0xff, 0xe8, 0x69, 0x1f, 0xd6, 0xef, 0xd0, 0xfa, 0xd1, 0x9c, 0x7d,
	0x04, 0x0f, 0xe5, 0x0c, 0x98, 0xec, 0xac, 0xf1, 0x06, 0xd6, 0xef, 0xc0, 0xb3, 0x28, 0x53, 0x8e,
	0x85, 0xff, 0xc9, 0x8e, 0x64, 0x36, 0xfc, 0x1c, 0xd6, 0x9a, 0x64, 0x8a, 0xde, 0xb1, 0xb9, 0xf0,
	0x10, 0x3e, 0x9d, 0x46, 0xcc, 0xa2, 0xa7, 0x04, 0x6a, 0x0a, 0x78, 0x3a, 0xd8, 0x66, 0xd7, 0x60,
	0xf9, 0xd4, 0x6c, 0x9d, 0xb5, 0x8e, 0x3b, 0xd6, 0xa9, 0xd9, 0x3e, 0x68, 0x59, 0xe7, 0xc7, 0xbf,
	0x39, 0x3e, 0xf9, 0xed, 0x71, 0xed, 0x13, 0x7d, 0x1d, 0xd6, 0xb2, 0x28, 0xb3, 0xd5, 0xb4, 0x9a,
	0xed, 0xbd, 0xa3, 0x93, 0xe3, 0x66, 0x2d, 0xa7, 0xaf, 0x42, 0x3d, 0x8b, 0xee, 0xec, 0xb7, 0xf6,
	0x8e, 0x6b, 0xf9, 0xa7, 0x6f, 0x61, 0x69, 0xf8, 0x79, 0xb1, 0x23, 0xbf, 0x4c, 0x1e, 0xd9, 0x91,
	0xae, 0xc3, 0xfc, 0xc1, 0xc9, 0xf1, 0xab, 0xf6, 0x6b, 0xeb, 0xfc, 0xf8, 0xb0, 0x7d, 0xd4, 0xee,
	0xd4, 0x3e, 0xd1, 0x57, 0x40, 0x57, 0xb0, 0xe3, 0x93, 0x8e, 0xd5, 0xfa, 0xdd, 0x69, 0xdb, 0x6c,
	0x09, 0xe1, 0x59, 0x78, 0xb3, 0x75, 0xd8, 0xea, 0xb4, 0x9a, 0xb5, 0xfc, 0xfe, 0xce, 0xdb, 0x67,
	0xbd, 0xd0, 0xb3, 0x83, 0xde, 0xf6, 0xf3, 0x5d, 0xce, 0xb7, 0x9d, 0xd0, 0xdf, 0xc1, 0x7f, 0x78,
	0x9d, 0xd0, 0xdb, 0x61, 0x24, 0xbe, 0xa6, 0x0e, 0x61, 0x3b, 0xa9, 0xd0, 0x5e, 0x14, 0x11, 0xfd,
	0xd5, 0x7f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x7b, 0x51, 0x2e, 0x30, 0x26, 0x1e, 0x00, 0x00,
}
