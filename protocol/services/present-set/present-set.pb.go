// Code generated by protoc-gen-go. DO NOT EDIT.
// source: present-set/present-set.proto

package present_set // import "golang.52tt.com/protocol/services/present-set"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import revenue_api_go "golang.52tt.com/protocol/services/revenue-api-go"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 礼物等级
type PresentLevel int32

const (
	PresentLevel_PRESENT_LEVEL_UNSPECIFIED PresentLevel = 0
	PresentLevel_PRESENT_LEVEL_R           PresentLevel = 10
	PresentLevel_PRESENT_LEVEL_SR          PresentLevel = 20
	PresentLevel_PRESENT_LEVEL_SSR         PresentLevel = 30
)

var PresentLevel_name = map[int32]string{
	0:  "PRESENT_LEVEL_UNSPECIFIED",
	10: "PRESENT_LEVEL_R",
	20: "PRESENT_LEVEL_SR",
	30: "PRESENT_LEVEL_SSR",
}
var PresentLevel_value = map[string]int32{
	"PRESENT_LEVEL_UNSPECIFIED": 0,
	"PRESENT_LEVEL_R":           10,
	"PRESENT_LEVEL_SR":          20,
	"PRESENT_LEVEL_SSR":         30,
}

func (x PresentLevel) String() string {
	return proto.EnumName(PresentLevel_name, int32(x))
}
func (PresentLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{0}
}

// 活动时效
type PresentSetPeriod int32

const (
	PresentSetPeriod_PRESENT_SET_PERIOD_UNSPECIFIED PresentSetPeriod = 0
	PresentSetPeriod_PRESENT_SET_PERIOD_LIMITED     PresentSetPeriod = 1
	PresentSetPeriod_PRESENT_SET_PERIOD_LONG        PresentSetPeriod = 2
)

var PresentSetPeriod_name = map[int32]string{
	0: "PRESENT_SET_PERIOD_UNSPECIFIED",
	1: "PRESENT_SET_PERIOD_LIMITED",
	2: "PRESENT_SET_PERIOD_LONG",
}
var PresentSetPeriod_value = map[string]int32{
	"PRESENT_SET_PERIOD_UNSPECIFIED": 0,
	"PRESENT_SET_PERIOD_LIMITED":     1,
	"PRESENT_SET_PERIOD_LONG":        2,
}

func (x PresentSetPeriod) String() string {
	return proto.EnumName(PresentSetPeriod_name, int32(x))
}
func (PresentSetPeriod) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{1}
}

// 活动状态
type PresentSetActiveStatus int32

const (
	PresentSetActiveStatus_PRESENT_SET_ACTIVE_STATUS_UNSPECIFIED PresentSetActiveStatus = 0
	PresentSetActiveStatus_PRESENT_SET_ACTIVE_STATUS_INACTIVE    PresentSetActiveStatus = 1
	PresentSetActiveStatus_PRESENT_SET_ACTIVE_STATUS_ACTIVE      PresentSetActiveStatus = 2
	PresentSetActiveStatus_PRESENT_SET_ACTIVE_STATUS_ENDED       PresentSetActiveStatus = 3
)

var PresentSetActiveStatus_name = map[int32]string{
	0: "PRESENT_SET_ACTIVE_STATUS_UNSPECIFIED",
	1: "PRESENT_SET_ACTIVE_STATUS_INACTIVE",
	2: "PRESENT_SET_ACTIVE_STATUS_ACTIVE",
	3: "PRESENT_SET_ACTIVE_STATUS_ENDED",
}
var PresentSetActiveStatus_value = map[string]int32{
	"PRESENT_SET_ACTIVE_STATUS_UNSPECIFIED": 0,
	"PRESENT_SET_ACTIVE_STATUS_INACTIVE":    1,
	"PRESENT_SET_ACTIVE_STATUS_ACTIVE":      2,
	"PRESENT_SET_ACTIVE_STATUS_ENDED":       3,
}

func (x PresentSetActiveStatus) String() string {
	return proto.EnumName(PresentSetActiveStatus_name, int32(x))
}
func (PresentSetActiveStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{2}
}

// 预览类型
type PresentSetPreviewType int32

const (
	PresentSetPreviewType_PRESENT_SET_PREVIEW_TYPE_UNSPECIFIED PresentSetPreviewType = 0
	PresentSetPreviewType_PRESENT_SET_PREVIEW_TYPE_IMAGE       PresentSetPreviewType = 1
	PresentSetPreviewType_PRESENT_SET_PREVIEW_TYPE_VIDEO       PresentSetPreviewType = 2
)

var PresentSetPreviewType_name = map[int32]string{
	0: "PRESENT_SET_PREVIEW_TYPE_UNSPECIFIED",
	1: "PRESENT_SET_PREVIEW_TYPE_IMAGE",
	2: "PRESENT_SET_PREVIEW_TYPE_VIDEO",
}
var PresentSetPreviewType_value = map[string]int32{
	"PRESENT_SET_PREVIEW_TYPE_UNSPECIFIED": 0,
	"PRESENT_SET_PREVIEW_TYPE_IMAGE":       1,
	"PRESENT_SET_PREVIEW_TYPE_VIDEO":       2,
}

func (x PresentSetPreviewType) String() string {
	return proto.EnumName(PresentSetPreviewType_name, int32(x))
}
func (PresentSetPreviewType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{3}
}

// 收集来源类型
type CollectSourceType int32

const (
	CollectSourceType_COLLECT_SOURCE_TYPE_UNSPECIFIED CollectSourceType = 0
	CollectSourceType_COLLECT_SOURCE_TYPE_GUARANTEED  CollectSourceType = 1
	CollectSourceType_COLLECT_SOURCE_TYPE_LOTTERY     CollectSourceType = 2
)

var CollectSourceType_name = map[int32]string{
	0: "COLLECT_SOURCE_TYPE_UNSPECIFIED",
	1: "COLLECT_SOURCE_TYPE_GUARANTEED",
	2: "COLLECT_SOURCE_TYPE_LOTTERY",
}
var CollectSourceType_value = map[string]int32{
	"COLLECT_SOURCE_TYPE_UNSPECIFIED": 0,
	"COLLECT_SOURCE_TYPE_GUARANTEED":  1,
	"COLLECT_SOURCE_TYPE_LOTTERY":     2,
}

func (x CollectSourceType) String() string {
	return proto.EnumName(CollectSourceType_name, int32(x))
}
func (CollectSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{4}
}

// 礼物套组活动链接类型
type ActivityLinkType int32

const (
	ActivityLinkType_ACTIVITY_LINK_TYPE_UNSPECIFIED ActivityLinkType = 0
	ActivityLinkType_ACTIVITY_LINK_TYPE_HALF_SCREEN ActivityLinkType = 1
	ActivityLinkType_ACTIVITY_LINK_TYPE_FULL_SCREEN ActivityLinkType = 2
)

var ActivityLinkType_name = map[int32]string{
	0: "ACTIVITY_LINK_TYPE_UNSPECIFIED",
	1: "ACTIVITY_LINK_TYPE_HALF_SCREEN",
	2: "ACTIVITY_LINK_TYPE_FULL_SCREEN",
}
var ActivityLinkType_value = map[string]int32{
	"ACTIVITY_LINK_TYPE_UNSPECIFIED": 0,
	"ACTIVITY_LINK_TYPE_HALF_SCREEN": 1,
	"ACTIVITY_LINK_TYPE_FULL_SCREEN": 2,
}

func (x ActivityLinkType) String() string {
	return proto.EnumName(ActivityLinkType_name, int32(x))
}
func (ActivityLinkType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{5}
}

// 帝王套横幅跳转类型
type EmperorSetBannerJumpType int32

const (
	EmperorSetBannerJumpType_EmperorSetBannerJumpTypeNone        EmperorSetBannerJumpType = 0
	EmperorSetBannerJumpType_EmperorSetBannerJumpTypeCMS         EmperorSetBannerJumpType = 1
	EmperorSetBannerJumpType_EmperorSetBannerJumpTypeActivityUrl EmperorSetBannerJumpType = 2
)

var EmperorSetBannerJumpType_name = map[int32]string{
	0: "EmperorSetBannerJumpTypeNone",
	1: "EmperorSetBannerJumpTypeCMS",
	2: "EmperorSetBannerJumpTypeActivityUrl",
}
var EmperorSetBannerJumpType_value = map[string]int32{
	"EmperorSetBannerJumpTypeNone":        0,
	"EmperorSetBannerJumpTypeCMS":         1,
	"EmperorSetBannerJumpTypeActivityUrl": 2,
}

func (x EmperorSetBannerJumpType) String() string {
	return proto.EnumName(EmperorSetBannerJumpType_name, int32(x))
}
func (EmperorSetBannerJumpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{6}
}

// 礼物套组配置
type PresentSetConfig struct {
	SetId        uint32                 `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	SetName      string                 `protobuf:"bytes,2,opt,name=set_name,json=setName,proto3" json:"set_name,omitempty"`
	ActiveStatus PresentSetActiveStatus `protobuf:"varint,3,opt,name=active_status,json=activeStatus,proto3,enum=present_set.PresentSetActiveStatus" json:"active_status,omitempty"`
	CreateTime   uint32                 `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Rank         float64                `protobuf:"fixed64,5,opt,name=rank,proto3" json:"rank,omitempty"`
	AwardText    string                 `protobuf:"bytes,11,opt,name=award_text,json=awardText,proto3" json:"award_text,omitempty"`
	// group
	Period    PresentSetPeriod `protobuf:"varint,6,opt,name=period,proto3,enum=present_set.PresentSetPeriod" json:"period,omitempty"`
	StartTime uint32           `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime   uint32           `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// group
	// bool enable_king_set = 9; // 是否配置帝王套（废弃）
	// uint32 king_set_id = 10; // 帝王套id（废弃）
	// group
	EnablePreview bool                  `protobuf:"varint,12,opt,name=enable_preview,json=enablePreview,proto3" json:"enable_preview,omitempty"`
	PreviewType   PresentSetPreviewType `protobuf:"varint,23,opt,name=preview_type,json=previewType,proto3,enum=present_set.PresentSetPreviewType" json:"preview_type,omitempty"`
	PreviewResUrl string                `protobuf:"bytes,13,opt,name=preview_res_url,json=previewResUrl,proto3" json:"preview_res_url,omitempty"`
	PreviewResMd5 string                `protobuf:"bytes,25,opt,name=preview_res_md5,json=previewResMd5,proto3" json:"preview_res_md5,omitempty"`
	// group
	EnableActivity         bool             `protobuf:"varint,14,opt,name=enable_activity,json=enableActivity,proto3" json:"enable_activity,omitempty"`
	ActivityEntranceResUrl string           `protobuf:"bytes,15,opt,name=activity_entrance_res_url,json=activityEntranceResUrl,proto3" json:"activity_entrance_res_url,omitempty"`
	ActivityLinkType       ActivityLinkType `protobuf:"varint,24,opt,name=activity_link_type,json=activityLinkType,proto3,enum=present_set.ActivityLinkType" json:"activity_link_type,omitempty"`
	ActivityJumpUrl        string           `protobuf:"bytes,16,opt,name=activity_jump_url,json=activityJumpUrl,proto3" json:"activity_jump_url,omitempty"`
	// end
	PresentList    []*SetPresentItem `protobuf:"bytes,17,rep,name=present_list,json=presentList,proto3" json:"present_list,omitempty"`
	AwardItemList  []*AwardItem      `protobuf:"bytes,18,rep,name=award_item_list,json=awardItemList,proto3" json:"award_item_list,omitempty"`
	UpdateTime     uint32            `protobuf:"varint,19,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	ChangeTime     uint32            `protobuf:"varint,20,opt,name=change_time,json=changeTime,proto3" json:"change_time,omitempty"`
	BreakingNewsId uint32            `protobuf:"varint,21,opt,name=breaking_news_id,json=breakingNewsId,proto3" json:"breaking_news_id,omitempty"`
	OriginPresent  *SetPresentItem   `protobuf:"bytes,22,opt,name=origin_present,json=originPresent,proto3" json:"origin_present,omitempty"`
	// group
	EnableEmperorSet     bool     `protobuf:"varint,26,opt,name=enable_emperor_set,json=enableEmperorSet,proto3" json:"enable_emperor_set,omitempty"`
	EmperorSetId         uint32   `protobuf:"varint,27,opt,name=emperor_set_id,json=emperorSetId,proto3" json:"emperor_set_id,omitempty"`
	IsDel                bool     `protobuf:"varint,28,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentSetConfig) Reset()         { *m = PresentSetConfig{} }
func (m *PresentSetConfig) String() string { return proto.CompactTextString(m) }
func (*PresentSetConfig) ProtoMessage()    {}
func (*PresentSetConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{0}
}
func (m *PresentSetConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSetConfig.Unmarshal(m, b)
}
func (m *PresentSetConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSetConfig.Marshal(b, m, deterministic)
}
func (dst *PresentSetConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSetConfig.Merge(dst, src)
}
func (m *PresentSetConfig) XXX_Size() int {
	return xxx_messageInfo_PresentSetConfig.Size(m)
}
func (m *PresentSetConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSetConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSetConfig proto.InternalMessageInfo

func (m *PresentSetConfig) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *PresentSetConfig) GetSetName() string {
	if m != nil {
		return m.SetName
	}
	return ""
}

func (m *PresentSetConfig) GetActiveStatus() PresentSetActiveStatus {
	if m != nil {
		return m.ActiveStatus
	}
	return PresentSetActiveStatus_PRESENT_SET_ACTIVE_STATUS_UNSPECIFIED
}

func (m *PresentSetConfig) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *PresentSetConfig) GetRank() float64 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *PresentSetConfig) GetAwardText() string {
	if m != nil {
		return m.AwardText
	}
	return ""
}

func (m *PresentSetConfig) GetPeriod() PresentSetPeriod {
	if m != nil {
		return m.Period
	}
	return PresentSetPeriod_PRESENT_SET_PERIOD_UNSPECIFIED
}

func (m *PresentSetConfig) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *PresentSetConfig) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *PresentSetConfig) GetEnablePreview() bool {
	if m != nil {
		return m.EnablePreview
	}
	return false
}

func (m *PresentSetConfig) GetPreviewType() PresentSetPreviewType {
	if m != nil {
		return m.PreviewType
	}
	return PresentSetPreviewType_PRESENT_SET_PREVIEW_TYPE_UNSPECIFIED
}

func (m *PresentSetConfig) GetPreviewResUrl() string {
	if m != nil {
		return m.PreviewResUrl
	}
	return ""
}

func (m *PresentSetConfig) GetPreviewResMd5() string {
	if m != nil {
		return m.PreviewResMd5
	}
	return ""
}

func (m *PresentSetConfig) GetEnableActivity() bool {
	if m != nil {
		return m.EnableActivity
	}
	return false
}

func (m *PresentSetConfig) GetActivityEntranceResUrl() string {
	if m != nil {
		return m.ActivityEntranceResUrl
	}
	return ""
}

func (m *PresentSetConfig) GetActivityLinkType() ActivityLinkType {
	if m != nil {
		return m.ActivityLinkType
	}
	return ActivityLinkType_ACTIVITY_LINK_TYPE_UNSPECIFIED
}

func (m *PresentSetConfig) GetActivityJumpUrl() string {
	if m != nil {
		return m.ActivityJumpUrl
	}
	return ""
}

func (m *PresentSetConfig) GetPresentList() []*SetPresentItem {
	if m != nil {
		return m.PresentList
	}
	return nil
}

func (m *PresentSetConfig) GetAwardItemList() []*AwardItem {
	if m != nil {
		return m.AwardItemList
	}
	return nil
}

func (m *PresentSetConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *PresentSetConfig) GetChangeTime() uint32 {
	if m != nil {
		return m.ChangeTime
	}
	return 0
}

func (m *PresentSetConfig) GetBreakingNewsId() uint32 {
	if m != nil {
		return m.BreakingNewsId
	}
	return 0
}

func (m *PresentSetConfig) GetOriginPresent() *SetPresentItem {
	if m != nil {
		return m.OriginPresent
	}
	return nil
}

func (m *PresentSetConfig) GetEnableEmperorSet() bool {
	if m != nil {
		return m.EnableEmperorSet
	}
	return false
}

func (m *PresentSetConfig) GetEmperorSetId() uint32 {
	if m != nil {
		return m.EmperorSetId
	}
	return 0
}

func (m *PresentSetConfig) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

// 礼物套组中的礼物配置
type SetPresentItem struct {
	SetId                uint32       `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	PresentId            uint32       `protobuf:"varint,2,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	PresentName          string       `protobuf:"bytes,3,opt,name=present_name,json=presentName,proto3" json:"present_name,omitempty"`
	PresentIcon          string       `protobuf:"bytes,4,opt,name=present_icon,json=presentIcon,proto3" json:"present_icon,omitempty"`
	PresentLevel         PresentLevel `protobuf:"varint,5,opt,name=present_level,json=presentLevel,proto3,enum=present_set.PresentLevel" json:"present_level,omitempty"`
	IsOrigin             bool         `protobuf:"varint,6,opt,name=is_origin,json=isOrigin,proto3" json:"is_origin,omitempty"`
	Probability          float64      `protobuf:"fixed64,7,opt,name=probability,proto3" json:"probability,omitempty"`
	Rank                 float64      `protobuf:"fixed64,8,opt,name=rank,proto3" json:"rank,omitempty"`
	Threshold            uint32       `protobuf:"varint,9,opt,name=threshold,proto3" json:"threshold,omitempty"`
	Guaranteed           int32        `protobuf:"varint,10,opt,name=guaranteed,proto3" json:"guaranteed,omitempty"`
	BackgroundUrl        string       `protobuf:"bytes,11,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	PresentPrice         uint32       `protobuf:"varint,12,opt,name=present_price,json=presentPrice,proto3" json:"present_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetPresentItem) Reset()         { *m = SetPresentItem{} }
func (m *SetPresentItem) String() string { return proto.CompactTextString(m) }
func (*SetPresentItem) ProtoMessage()    {}
func (*SetPresentItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{1}
}
func (m *SetPresentItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPresentItem.Unmarshal(m, b)
}
func (m *SetPresentItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPresentItem.Marshal(b, m, deterministic)
}
func (dst *SetPresentItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPresentItem.Merge(dst, src)
}
func (m *SetPresentItem) XXX_Size() int {
	return xxx_messageInfo_SetPresentItem.Size(m)
}
func (m *SetPresentItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPresentItem.DiscardUnknown(m)
}

var xxx_messageInfo_SetPresentItem proto.InternalMessageInfo

func (m *SetPresentItem) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *SetPresentItem) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *SetPresentItem) GetPresentName() string {
	if m != nil {
		return m.PresentName
	}
	return ""
}

func (m *SetPresentItem) GetPresentIcon() string {
	if m != nil {
		return m.PresentIcon
	}
	return ""
}

func (m *SetPresentItem) GetPresentLevel() PresentLevel {
	if m != nil {
		return m.PresentLevel
	}
	return PresentLevel_PRESENT_LEVEL_UNSPECIFIED
}

func (m *SetPresentItem) GetIsOrigin() bool {
	if m != nil {
		return m.IsOrigin
	}
	return false
}

func (m *SetPresentItem) GetProbability() float64 {
	if m != nil {
		return m.Probability
	}
	return 0
}

func (m *SetPresentItem) GetRank() float64 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *SetPresentItem) GetThreshold() uint32 {
	if m != nil {
		return m.Threshold
	}
	return 0
}

func (m *SetPresentItem) GetGuaranteed() int32 {
	if m != nil {
		return m.Guaranteed
	}
	return 0
}

func (m *SetPresentItem) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

func (m *SetPresentItem) GetPresentPrice() uint32 {
	if m != nil {
		return m.PresentPrice
	}
	return 0
}

// 礼物套组奖励配置
type SetAwardItem struct {
	SetId                uint32       `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	SetName              string       `protobuf:"bytes,2,opt,name=set_name,json=setName,proto3" json:"set_name,omitempty"`
	AwardItemList        []*AwardItem `protobuf:"bytes,3,rep,name=award_item_list,json=awardItemList,proto3" json:"award_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetAwardItem) Reset()         { *m = SetAwardItem{} }
func (m *SetAwardItem) String() string { return proto.CompactTextString(m) }
func (*SetAwardItem) ProtoMessage()    {}
func (*SetAwardItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{2}
}
func (m *SetAwardItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAwardItem.Unmarshal(m, b)
}
func (m *SetAwardItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAwardItem.Marshal(b, m, deterministic)
}
func (dst *SetAwardItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAwardItem.Merge(dst, src)
}
func (m *SetAwardItem) XXX_Size() int {
	return xxx_messageInfo_SetAwardItem.Size(m)
}
func (m *SetAwardItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAwardItem.DiscardUnknown(m)
}

var xxx_messageInfo_SetAwardItem proto.InternalMessageInfo

func (m *SetAwardItem) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *SetAwardItem) GetSetName() string {
	if m != nil {
		return m.SetName
	}
	return ""
}

func (m *SetAwardItem) GetAwardItemList() []*AwardItem {
	if m != nil {
		return m.AwardItemList
	}
	return nil
}

// 套组奖励
type AwardItem struct {
	AwardType            revenue_api_go.RevenueAwardType `protobuf:"varint,1,opt,name=award_type,json=awardType,proto3,enum=RevenueApiGo.RevenueAwardType" json:"award_type,omitempty"`
	AwardId              string                          `protobuf:"bytes,2,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	AwardCount           uint32                          `protobuf:"varint,3,opt,name=award_count,json=awardCount,proto3" json:"award_count,omitempty"`
	ActiveCount          uint32                          `protobuf:"varint,4,opt,name=active_count,json=activeCount,proto3" json:"active_count,omitempty"`
	Rank                 uint32                          `protobuf:"varint,5,opt,name=rank,proto3" json:"rank,omitempty"`
	AwardPic             string                          `protobuf:"bytes,6,opt,name=award_pic,json=awardPic,proto3" json:"award_pic,omitempty"`
	AwardPrice           uint32                          `protobuf:"varint,7,opt,name=award_price,json=awardPrice,proto3" json:"award_price,omitempty"`
	AwardCountInfo       string                          `protobuf:"bytes,8,opt,name=award_count_info,json=awardCountInfo,proto3" json:"award_count_info,omitempty"`
	AwardName            string                          `protobuf:"bytes,9,opt,name=award_name,json=awardName,proto3" json:"award_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *AwardItem) Reset()         { *m = AwardItem{} }
func (m *AwardItem) String() string { return proto.CompactTextString(m) }
func (*AwardItem) ProtoMessage()    {}
func (*AwardItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{3}
}
func (m *AwardItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardItem.Unmarshal(m, b)
}
func (m *AwardItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardItem.Marshal(b, m, deterministic)
}
func (dst *AwardItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardItem.Merge(dst, src)
}
func (m *AwardItem) XXX_Size() int {
	return xxx_messageInfo_AwardItem.Size(m)
}
func (m *AwardItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardItem.DiscardUnknown(m)
}

var xxx_messageInfo_AwardItem proto.InternalMessageInfo

func (m *AwardItem) GetAwardType() revenue_api_go.RevenueAwardType {
	if m != nil {
		return m.AwardType
	}
	return revenue_api_go.RevenueAwardType_RevenueAwardTypeNone
}

func (m *AwardItem) GetAwardId() string {
	if m != nil {
		return m.AwardId
	}
	return ""
}

func (m *AwardItem) GetAwardCount() uint32 {
	if m != nil {
		return m.AwardCount
	}
	return 0
}

func (m *AwardItem) GetActiveCount() uint32 {
	if m != nil {
		return m.ActiveCount
	}
	return 0
}

func (m *AwardItem) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *AwardItem) GetAwardPic() string {
	if m != nil {
		return m.AwardPic
	}
	return ""
}

func (m *AwardItem) GetAwardPrice() uint32 {
	if m != nil {
		return m.AwardPrice
	}
	return 0
}

func (m *AwardItem) GetAwardCountInfo() string {
	if m != nil {
		return m.AwardCountInfo
	}
	return ""
}

func (m *AwardItem) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

// 帝王套配置
type EmperorSetConfig struct {
	SetId          uint32                             `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	SetName        string                             `protobuf:"bytes,2,opt,name=set_name,json=setName,proto3" json:"set_name,omitempty"`
	ActiveStatus   PresentSetActiveStatus             `protobuf:"varint,3,opt,name=active_status,json=activeStatus,proto3,enum=present_set.PresentSetActiveStatus" json:"active_status,omitempty"`
	CreateTime     uint32                             `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime     uint32                             `protobuf:"varint,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	IconUrl        string                             `protobuf:"bytes,6,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	IsFuseUserInfo bool                               `protobuf:"varint,7,opt,name=is_fuse_user_info,json=isFuseUserInfo,proto3" json:"is_fuse_user_info,omitempty"`
	Effect         *EmperorSetConfig_EmperorSetEffect `protobuf:"bytes,8,opt,name=effect,proto3" json:"effect,omitempty"`
	// group
	ShowPresentShelf bool    `protobuf:"varint,10,opt,name=show_present_shelf,json=showPresentShelf,proto3" json:"show_present_shelf,omitempty"`
	Rank             float64 `protobuf:"fixed64,11,opt,name=rank,proto3" json:"rank,omitempty"`
	ShowEffectEnd    bool    `protobuf:"varint,12,opt,name=show_effect_end,json=showEffectEnd,proto3" json:"show_effect_end,omitempty"`
	// group
	StartTime uint32 `protobuf:"varint,15,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime   uint32 `protobuf:"varint,16,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// group
	BannerResUrl   string                   `protobuf:"bytes,20,opt,name=banner_res_url,json=bannerResUrl,proto3" json:"banner_res_url,omitempty"`
	BannerJumpType EmperorSetBannerJumpType `protobuf:"varint,21,opt,name=banner_jump_type,json=bannerJumpType,proto3,enum=present_set.EmperorSetBannerJumpType" json:"banner_jump_type,omitempty"`
	BannerJumpUrl  string                   `protobuf:"bytes,22,opt,name=banner_jump_url,json=bannerJumpUrl,proto3" json:"banner_jump_url,omitempty"`
	// group
	EnableBreakingNews bool   `protobuf:"varint,25,opt,name=enable_breaking_news,json=enableBreakingNews,proto3" json:"enable_breaking_news,omitempty"`
	BreakingNewsId     uint32 `protobuf:"varint,26,opt,name=breaking_news_id,json=breakingNewsId,proto3" json:"breaking_news_id,omitempty"`
	BreakingTimesLimit int32  `protobuf:"varint,27,opt,name=breaking_times_limit,json=breakingTimesLimit,proto3" json:"breaking_times_limit,omitempty"`
	// end
	PresentList            []*EmperorSetPresentItem `protobuf:"bytes,30,rep,name=present_list,json=presentList,proto3" json:"present_list,omitempty"`
	PresentsTotalPrice     uint32                   `protobuf:"varint,31,opt,name=presents_total_price,json=presentsTotalPrice,proto3" json:"presents_total_price,omitempty"`
	IsDel                  bool                     `protobuf:"varint,32,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	SendEmperorSetText     string                   `protobuf:"bytes,33,opt,name=send_emperor_set_text,json=sendEmperorSetText,proto3" json:"send_emperor_set_text,omitempty"`
	SendEmperorSetFrameUrl string                   `protobuf:"bytes,34,opt,name=send_emperor_set_frame_url,json=sendEmperorSetFrameUrl,proto3" json:"send_emperor_set_frame_url,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                 `json:"-"`
	XXX_unrecognized       []byte                   `json:"-"`
	XXX_sizecache          int32                    `json:"-"`
}

func (m *EmperorSetConfig) Reset()         { *m = EmperorSetConfig{} }
func (m *EmperorSetConfig) String() string { return proto.CompactTextString(m) }
func (*EmperorSetConfig) ProtoMessage()    {}
func (*EmperorSetConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{4}
}
func (m *EmperorSetConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmperorSetConfig.Unmarshal(m, b)
}
func (m *EmperorSetConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmperorSetConfig.Marshal(b, m, deterministic)
}
func (dst *EmperorSetConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmperorSetConfig.Merge(dst, src)
}
func (m *EmperorSetConfig) XXX_Size() int {
	return xxx_messageInfo_EmperorSetConfig.Size(m)
}
func (m *EmperorSetConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_EmperorSetConfig.DiscardUnknown(m)
}

var xxx_messageInfo_EmperorSetConfig proto.InternalMessageInfo

func (m *EmperorSetConfig) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *EmperorSetConfig) GetSetName() string {
	if m != nil {
		return m.SetName
	}
	return ""
}

func (m *EmperorSetConfig) GetActiveStatus() PresentSetActiveStatus {
	if m != nil {
		return m.ActiveStatus
	}
	return PresentSetActiveStatus_PRESENT_SET_ACTIVE_STATUS_UNSPECIFIED
}

func (m *EmperorSetConfig) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *EmperorSetConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *EmperorSetConfig) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *EmperorSetConfig) GetIsFuseUserInfo() bool {
	if m != nil {
		return m.IsFuseUserInfo
	}
	return false
}

func (m *EmperorSetConfig) GetEffect() *EmperorSetConfig_EmperorSetEffect {
	if m != nil {
		return m.Effect
	}
	return nil
}

func (m *EmperorSetConfig) GetShowPresentShelf() bool {
	if m != nil {
		return m.ShowPresentShelf
	}
	return false
}

func (m *EmperorSetConfig) GetRank() float64 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *EmperorSetConfig) GetShowEffectEnd() bool {
	if m != nil {
		return m.ShowEffectEnd
	}
	return false
}

func (m *EmperorSetConfig) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *EmperorSetConfig) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *EmperorSetConfig) GetBannerResUrl() string {
	if m != nil {
		return m.BannerResUrl
	}
	return ""
}

func (m *EmperorSetConfig) GetBannerJumpType() EmperorSetBannerJumpType {
	if m != nil {
		return m.BannerJumpType
	}
	return EmperorSetBannerJumpType_EmperorSetBannerJumpTypeNone
}

func (m *EmperorSetConfig) GetBannerJumpUrl() string {
	if m != nil {
		return m.BannerJumpUrl
	}
	return ""
}

func (m *EmperorSetConfig) GetEnableBreakingNews() bool {
	if m != nil {
		return m.EnableBreakingNews
	}
	return false
}

func (m *EmperorSetConfig) GetBreakingNewsId() uint32 {
	if m != nil {
		return m.BreakingNewsId
	}
	return 0
}

func (m *EmperorSetConfig) GetBreakingTimesLimit() int32 {
	if m != nil {
		return m.BreakingTimesLimit
	}
	return 0
}

func (m *EmperorSetConfig) GetPresentList() []*EmperorSetPresentItem {
	if m != nil {
		return m.PresentList
	}
	return nil
}

func (m *EmperorSetConfig) GetPresentsTotalPrice() uint32 {
	if m != nil {
		return m.PresentsTotalPrice
	}
	return 0
}

func (m *EmperorSetConfig) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *EmperorSetConfig) GetSendEmperorSetText() string {
	if m != nil {
		return m.SendEmperorSetText
	}
	return ""
}

func (m *EmperorSetConfig) GetSendEmperorSetFrameUrl() string {
	if m != nil {
		return m.SendEmperorSetFrameUrl
	}
	return ""
}

type EmperorSetConfig_EmperorSetEffect struct {
	AndroidEffectUrl        string   `protobuf:"bytes,1,opt,name=android_effect_url,json=androidEffectUrl,proto3" json:"android_effect_url,omitempty"`
	AndroidEffectMd5        string   `protobuf:"bytes,2,opt,name=android_effect_md5,json=androidEffectMd5,proto3" json:"android_effect_md5,omitempty"`
	IosEffectUrl            string   `protobuf:"bytes,3,opt,name=ios_effect_url,json=iosEffectUrl,proto3" json:"ios_effect_url,omitempty"`
	IosEffectMd5            string   `protobuf:"bytes,4,opt,name=ios_effect_md5,json=iosEffectMd5,proto3" json:"ios_effect_md5,omitempty"`
	IsBreakingBox           bool     `protobuf:"varint,5,opt,name=is_breaking_box,json=isBreakingBox,proto3" json:"is_breaking_box,omitempty"`
	EmperorViewingSourceUrl string   `protobuf:"bytes,6,opt,name=emperor_viewing_source_url,json=emperorViewingSourceUrl,proto3" json:"emperor_viewing_source_url,omitempty"`
	EmperorViewingSourceMd5 string   `protobuf:"bytes,7,opt,name=emperor_viewing_source_md5,json=emperorViewingSourceMd5,proto3" json:"emperor_viewing_source_md5,omitempty"`
	EmperorFlowSourceUrl    string   `protobuf:"bytes,8,opt,name=emperor_flow_source_url,json=emperorFlowSourceUrl,proto3" json:"emperor_flow_source_url,omitempty"`
	EmperorFlowSourceMd5    string   `protobuf:"bytes,9,opt,name=emperor_flow_source_md5,json=emperorFlowSourceMd5,proto3" json:"emperor_flow_source_md5,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *EmperorSetConfig_EmperorSetEffect) Reset()         { *m = EmperorSetConfig_EmperorSetEffect{} }
func (m *EmperorSetConfig_EmperorSetEffect) String() string { return proto.CompactTextString(m) }
func (*EmperorSetConfig_EmperorSetEffect) ProtoMessage()    {}
func (*EmperorSetConfig_EmperorSetEffect) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{4, 0}
}
func (m *EmperorSetConfig_EmperorSetEffect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmperorSetConfig_EmperorSetEffect.Unmarshal(m, b)
}
func (m *EmperorSetConfig_EmperorSetEffect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmperorSetConfig_EmperorSetEffect.Marshal(b, m, deterministic)
}
func (dst *EmperorSetConfig_EmperorSetEffect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmperorSetConfig_EmperorSetEffect.Merge(dst, src)
}
func (m *EmperorSetConfig_EmperorSetEffect) XXX_Size() int {
	return xxx_messageInfo_EmperorSetConfig_EmperorSetEffect.Size(m)
}
func (m *EmperorSetConfig_EmperorSetEffect) XXX_DiscardUnknown() {
	xxx_messageInfo_EmperorSetConfig_EmperorSetEffect.DiscardUnknown(m)
}

var xxx_messageInfo_EmperorSetConfig_EmperorSetEffect proto.InternalMessageInfo

func (m *EmperorSetConfig_EmperorSetEffect) GetAndroidEffectUrl() string {
	if m != nil {
		return m.AndroidEffectUrl
	}
	return ""
}

func (m *EmperorSetConfig_EmperorSetEffect) GetAndroidEffectMd5() string {
	if m != nil {
		return m.AndroidEffectMd5
	}
	return ""
}

func (m *EmperorSetConfig_EmperorSetEffect) GetIosEffectUrl() string {
	if m != nil {
		return m.IosEffectUrl
	}
	return ""
}

func (m *EmperorSetConfig_EmperorSetEffect) GetIosEffectMd5() string {
	if m != nil {
		return m.IosEffectMd5
	}
	return ""
}

func (m *EmperorSetConfig_EmperorSetEffect) GetIsBreakingBox() bool {
	if m != nil {
		return m.IsBreakingBox
	}
	return false
}

func (m *EmperorSetConfig_EmperorSetEffect) GetEmperorViewingSourceUrl() string {
	if m != nil {
		return m.EmperorViewingSourceUrl
	}
	return ""
}

func (m *EmperorSetConfig_EmperorSetEffect) GetEmperorViewingSourceMd5() string {
	if m != nil {
		return m.EmperorViewingSourceMd5
	}
	return ""
}

func (m *EmperorSetConfig_EmperorSetEffect) GetEmperorFlowSourceUrl() string {
	if m != nil {
		return m.EmperorFlowSourceUrl
	}
	return ""
}

func (m *EmperorSetConfig_EmperorSetEffect) GetEmperorFlowSourceMd5() string {
	if m != nil {
		return m.EmperorFlowSourceMd5
	}
	return ""
}

// 帝王套礼物配置
type EmperorSetPresentItem struct {
	SetId                uint32   `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	PresentId            uint32   `protobuf:"varint,2,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	Rank                 float64  `protobuf:"fixed64,6,opt,name=rank,proto3" json:"rank,omitempty"`
	PresentName          string   `protobuf:"bytes,3,opt,name=present_name,json=presentName,proto3" json:"present_name,omitempty"`
	PresentIcon          string   `protobuf:"bytes,4,opt,name=present_icon,json=presentIcon,proto3" json:"present_icon,omitempty"`
	PresentPrice         uint32   `protobuf:"varint,5,opt,name=present_price,json=presentPrice,proto3" json:"present_price,omitempty"`
	ShowPresentShelf     bool     `protobuf:"varint,7,opt,name=show_present_shelf,json=showPresentShelf,proto3" json:"show_present_shelf,omitempty"`
	EffectBegin          uint32   `protobuf:"varint,8,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32   `protobuf:"varint,9,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmperorSetPresentItem) Reset()         { *m = EmperorSetPresentItem{} }
func (m *EmperorSetPresentItem) String() string { return proto.CompactTextString(m) }
func (*EmperorSetPresentItem) ProtoMessage()    {}
func (*EmperorSetPresentItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{5}
}
func (m *EmperorSetPresentItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmperorSetPresentItem.Unmarshal(m, b)
}
func (m *EmperorSetPresentItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmperorSetPresentItem.Marshal(b, m, deterministic)
}
func (dst *EmperorSetPresentItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmperorSetPresentItem.Merge(dst, src)
}
func (m *EmperorSetPresentItem) XXX_Size() int {
	return xxx_messageInfo_EmperorSetPresentItem.Size(m)
}
func (m *EmperorSetPresentItem) XXX_DiscardUnknown() {
	xxx_messageInfo_EmperorSetPresentItem.DiscardUnknown(m)
}

var xxx_messageInfo_EmperorSetPresentItem proto.InternalMessageInfo

func (m *EmperorSetPresentItem) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *EmperorSetPresentItem) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *EmperorSetPresentItem) GetRank() float64 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *EmperorSetPresentItem) GetPresentName() string {
	if m != nil {
		return m.PresentName
	}
	return ""
}

func (m *EmperorSetPresentItem) GetPresentIcon() string {
	if m != nil {
		return m.PresentIcon
	}
	return ""
}

func (m *EmperorSetPresentItem) GetPresentPrice() uint32 {
	if m != nil {
		return m.PresentPrice
	}
	return 0
}

func (m *EmperorSetPresentItem) GetShowPresentShelf() bool {
	if m != nil {
		return m.ShowPresentShelf
	}
	return false
}

func (m *EmperorSetPresentItem) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *EmperorSetPresentItem) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

type CreatePresentSetReq struct {
	PresentSet           *PresentSetConfig `protobuf:"bytes,1,opt,name=present_set,json=presentSet,proto3" json:"present_set,omitempty"`
	IsValidate           bool              `protobuf:"varint,3,opt,name=is_validate,json=isValidate,proto3" json:"is_validate,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CreatePresentSetReq) Reset()         { *m = CreatePresentSetReq{} }
func (m *CreatePresentSetReq) String() string { return proto.CompactTextString(m) }
func (*CreatePresentSetReq) ProtoMessage()    {}
func (*CreatePresentSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{6}
}
func (m *CreatePresentSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePresentSetReq.Unmarshal(m, b)
}
func (m *CreatePresentSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePresentSetReq.Marshal(b, m, deterministic)
}
func (dst *CreatePresentSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePresentSetReq.Merge(dst, src)
}
func (m *CreatePresentSetReq) XXX_Size() int {
	return xxx_messageInfo_CreatePresentSetReq.Size(m)
}
func (m *CreatePresentSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePresentSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePresentSetReq proto.InternalMessageInfo

func (m *CreatePresentSetReq) GetPresentSet() *PresentSetConfig {
	if m != nil {
		return m.PresentSet
	}
	return nil
}

func (m *CreatePresentSetReq) GetIsValidate() bool {
	if m != nil {
		return m.IsValidate
	}
	return false
}

type CreatePresentSetResp struct {
	SetId                uint32   `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatePresentSetResp) Reset()         { *m = CreatePresentSetResp{} }
func (m *CreatePresentSetResp) String() string { return proto.CompactTextString(m) }
func (*CreatePresentSetResp) ProtoMessage()    {}
func (*CreatePresentSetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{7}
}
func (m *CreatePresentSetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePresentSetResp.Unmarshal(m, b)
}
func (m *CreatePresentSetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePresentSetResp.Marshal(b, m, deterministic)
}
func (dst *CreatePresentSetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePresentSetResp.Merge(dst, src)
}
func (m *CreatePresentSetResp) XXX_Size() int {
	return xxx_messageInfo_CreatePresentSetResp.Size(m)
}
func (m *CreatePresentSetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePresentSetResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePresentSetResp proto.InternalMessageInfo

func (m *CreatePresentSetResp) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

type GetPresentSetInfoReq struct {
	SetId                uint32   `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentSetInfoReq) Reset()         { *m = GetPresentSetInfoReq{} }
func (m *GetPresentSetInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentSetInfoReq) ProtoMessage()    {}
func (*GetPresentSetInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{8}
}
func (m *GetPresentSetInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentSetInfoReq.Unmarshal(m, b)
}
func (m *GetPresentSetInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentSetInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentSetInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentSetInfoReq.Merge(dst, src)
}
func (m *GetPresentSetInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentSetInfoReq.Size(m)
}
func (m *GetPresentSetInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentSetInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentSetInfoReq proto.InternalMessageInfo

func (m *GetPresentSetInfoReq) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

type GetPresentSetInfoResp struct {
	PresentSet           *PresentSetConfig `protobuf:"bytes,1,opt,name=present_set,json=presentSet,proto3" json:"present_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPresentSetInfoResp) Reset()         { *m = GetPresentSetInfoResp{} }
func (m *GetPresentSetInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentSetInfoResp) ProtoMessage()    {}
func (*GetPresentSetInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{9}
}
func (m *GetPresentSetInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentSetInfoResp.Unmarshal(m, b)
}
func (m *GetPresentSetInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentSetInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentSetInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentSetInfoResp.Merge(dst, src)
}
func (m *GetPresentSetInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentSetInfoResp.Size(m)
}
func (m *GetPresentSetInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentSetInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentSetInfoResp proto.InternalMessageInfo

func (m *GetPresentSetInfoResp) GetPresentSet() *PresentSetConfig {
	if m != nil {
		return m.PresentSet
	}
	return nil
}

type GetPresentSetListNoCacheReq struct {
	ActiveStatus         PresentSetActiveStatus `protobuf:"varint,1,opt,name=active_status,json=activeStatus,proto3,enum=present_set.PresentSetActiveStatus" json:"active_status,omitempty"`
	Offset               uint32                 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32                 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	SetId                uint32                 `protobuf:"varint,7,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	SetName              string                 `protobuf:"bytes,8,opt,name=set_name,json=setName,proto3" json:"set_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPresentSetListNoCacheReq) Reset()         { *m = GetPresentSetListNoCacheReq{} }
func (m *GetPresentSetListNoCacheReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentSetListNoCacheReq) ProtoMessage()    {}
func (*GetPresentSetListNoCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{10}
}
func (m *GetPresentSetListNoCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentSetListNoCacheReq.Unmarshal(m, b)
}
func (m *GetPresentSetListNoCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentSetListNoCacheReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentSetListNoCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentSetListNoCacheReq.Merge(dst, src)
}
func (m *GetPresentSetListNoCacheReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentSetListNoCacheReq.Size(m)
}
func (m *GetPresentSetListNoCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentSetListNoCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentSetListNoCacheReq proto.InternalMessageInfo

func (m *GetPresentSetListNoCacheReq) GetActiveStatus() PresentSetActiveStatus {
	if m != nil {
		return m.ActiveStatus
	}
	return PresentSetActiveStatus_PRESENT_SET_ACTIVE_STATUS_UNSPECIFIED
}

func (m *GetPresentSetListNoCacheReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetPresentSetListNoCacheReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetPresentSetListNoCacheReq) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *GetPresentSetListNoCacheReq) GetSetName() string {
	if m != nil {
		return m.SetName
	}
	return ""
}

type GetPresentSetListNoCacheResp struct {
	PresentSetList       []*PresentSetConfig `protobuf:"bytes,1,rep,name=present_set_list,json=presentSetList,proto3" json:"present_set_list,omitempty"`
	Total                uint32              `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetPresentSetListNoCacheResp) Reset()         { *m = GetPresentSetListNoCacheResp{} }
func (m *GetPresentSetListNoCacheResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentSetListNoCacheResp) ProtoMessage()    {}
func (*GetPresentSetListNoCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{11}
}
func (m *GetPresentSetListNoCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentSetListNoCacheResp.Unmarshal(m, b)
}
func (m *GetPresentSetListNoCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentSetListNoCacheResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentSetListNoCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentSetListNoCacheResp.Merge(dst, src)
}
func (m *GetPresentSetListNoCacheResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentSetListNoCacheResp.Size(m)
}
func (m *GetPresentSetListNoCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentSetListNoCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentSetListNoCacheResp proto.InternalMessageInfo

func (m *GetPresentSetListNoCacheResp) GetPresentSetList() []*PresentSetConfig {
	if m != nil {
		return m.PresentSetList
	}
	return nil
}

func (m *GetPresentSetListNoCacheResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetPresentSetSimpleListNoCacheReq struct {
	Keyword              string   `protobuf:"bytes,4,opt,name=keyword,proto3" json:"keyword,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentSetSimpleListNoCacheReq) Reset()         { *m = GetPresentSetSimpleListNoCacheReq{} }
func (m *GetPresentSetSimpleListNoCacheReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentSetSimpleListNoCacheReq) ProtoMessage()    {}
func (*GetPresentSetSimpleListNoCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{12}
}
func (m *GetPresentSetSimpleListNoCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentSetSimpleListNoCacheReq.Unmarshal(m, b)
}
func (m *GetPresentSetSimpleListNoCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentSetSimpleListNoCacheReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentSetSimpleListNoCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentSetSimpleListNoCacheReq.Merge(dst, src)
}
func (m *GetPresentSetSimpleListNoCacheReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentSetSimpleListNoCacheReq.Size(m)
}
func (m *GetPresentSetSimpleListNoCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentSetSimpleListNoCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentSetSimpleListNoCacheReq proto.InternalMessageInfo

func (m *GetPresentSetSimpleListNoCacheReq) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

type GetPresentSetSimpleListNoCacheResp struct {
	PresentSetList       []*PresentSetConfig `protobuf:"bytes,1,rep,name=present_set_list,json=presentSetList,proto3" json:"present_set_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetPresentSetSimpleListNoCacheResp) Reset()         { *m = GetPresentSetSimpleListNoCacheResp{} }
func (m *GetPresentSetSimpleListNoCacheResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentSetSimpleListNoCacheResp) ProtoMessage()    {}
func (*GetPresentSetSimpleListNoCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{13}
}
func (m *GetPresentSetSimpleListNoCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentSetSimpleListNoCacheResp.Unmarshal(m, b)
}
func (m *GetPresentSetSimpleListNoCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentSetSimpleListNoCacheResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentSetSimpleListNoCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentSetSimpleListNoCacheResp.Merge(dst, src)
}
func (m *GetPresentSetSimpleListNoCacheResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentSetSimpleListNoCacheResp.Size(m)
}
func (m *GetPresentSetSimpleListNoCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentSetSimpleListNoCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentSetSimpleListNoCacheResp proto.InternalMessageInfo

func (m *GetPresentSetSimpleListNoCacheResp) GetPresentSetList() []*PresentSetConfig {
	if m != nil {
		return m.PresentSetList
	}
	return nil
}

type DeletePresentSetReq struct {
	SetId                uint32   `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePresentSetReq) Reset()         { *m = DeletePresentSetReq{} }
func (m *DeletePresentSetReq) String() string { return proto.CompactTextString(m) }
func (*DeletePresentSetReq) ProtoMessage()    {}
func (*DeletePresentSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{14}
}
func (m *DeletePresentSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePresentSetReq.Unmarshal(m, b)
}
func (m *DeletePresentSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePresentSetReq.Marshal(b, m, deterministic)
}
func (dst *DeletePresentSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePresentSetReq.Merge(dst, src)
}
func (m *DeletePresentSetReq) XXX_Size() int {
	return xxx_messageInfo_DeletePresentSetReq.Size(m)
}
func (m *DeletePresentSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePresentSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePresentSetReq proto.InternalMessageInfo

func (m *DeletePresentSetReq) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

type DeletePresentSetResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePresentSetResp) Reset()         { *m = DeletePresentSetResp{} }
func (m *DeletePresentSetResp) String() string { return proto.CompactTextString(m) }
func (*DeletePresentSetResp) ProtoMessage()    {}
func (*DeletePresentSetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{15}
}
func (m *DeletePresentSetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePresentSetResp.Unmarshal(m, b)
}
func (m *DeletePresentSetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePresentSetResp.Marshal(b, m, deterministic)
}
func (dst *DeletePresentSetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePresentSetResp.Merge(dst, src)
}
func (m *DeletePresentSetResp) XXX_Size() int {
	return xxx_messageInfo_DeletePresentSetResp.Size(m)
}
func (m *DeletePresentSetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePresentSetResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePresentSetResp proto.InternalMessageInfo

type SetPresentsToSetReq struct {
	SetId                uint32            `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	PresentList          []*SetPresentItem `protobuf:"bytes,2,rep,name=present_list,json=presentList,proto3" json:"present_list,omitempty"`
	IsValidate           bool              `protobuf:"varint,3,opt,name=is_validate,json=isValidate,proto3" json:"is_validate,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SetPresentsToSetReq) Reset()         { *m = SetPresentsToSetReq{} }
func (m *SetPresentsToSetReq) String() string { return proto.CompactTextString(m) }
func (*SetPresentsToSetReq) ProtoMessage()    {}
func (*SetPresentsToSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{16}
}
func (m *SetPresentsToSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPresentsToSetReq.Unmarshal(m, b)
}
func (m *SetPresentsToSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPresentsToSetReq.Marshal(b, m, deterministic)
}
func (dst *SetPresentsToSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPresentsToSetReq.Merge(dst, src)
}
func (m *SetPresentsToSetReq) XXX_Size() int {
	return xxx_messageInfo_SetPresentsToSetReq.Size(m)
}
func (m *SetPresentsToSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPresentsToSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPresentsToSetReq proto.InternalMessageInfo

func (m *SetPresentsToSetReq) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *SetPresentsToSetReq) GetPresentList() []*SetPresentItem {
	if m != nil {
		return m.PresentList
	}
	return nil
}

func (m *SetPresentsToSetReq) GetIsValidate() bool {
	if m != nil {
		return m.IsValidate
	}
	return false
}

type SetPresentsToSetResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPresentsToSetResp) Reset()         { *m = SetPresentsToSetResp{} }
func (m *SetPresentsToSetResp) String() string { return proto.CompactTextString(m) }
func (*SetPresentsToSetResp) ProtoMessage()    {}
func (*SetPresentsToSetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{17}
}
func (m *SetPresentsToSetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPresentsToSetResp.Unmarshal(m, b)
}
func (m *SetPresentsToSetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPresentsToSetResp.Marshal(b, m, deterministic)
}
func (dst *SetPresentsToSetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPresentsToSetResp.Merge(dst, src)
}
func (m *SetPresentsToSetResp) XXX_Size() int {
	return xxx_messageInfo_SetPresentsToSetResp.Size(m)
}
func (m *SetPresentsToSetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPresentsToSetResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPresentsToSetResp proto.InternalMessageInfo

type GetUserSetsInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SetId                uint32   `protobuf:"varint,2,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSetsInfoReq) Reset()         { *m = GetUserSetsInfoReq{} }
func (m *GetUserSetsInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserSetsInfoReq) ProtoMessage()    {}
func (*GetUserSetsInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{18}
}
func (m *GetUserSetsInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSetsInfoReq.Unmarshal(m, b)
}
func (m *GetUserSetsInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSetsInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserSetsInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSetsInfoReq.Merge(dst, src)
}
func (m *GetUserSetsInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserSetsInfoReq.Size(m)
}
func (m *GetUserSetsInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSetsInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSetsInfoReq proto.InternalMessageInfo

func (m *GetUserSetsInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserSetsInfoReq) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

// 用户套组信息
type UserSetInfo struct {
	SetId                uint32                         `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	Uid                  uint32                         `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	LastPresentId        uint32                         `protobuf:"varint,3,opt,name=last_present_id,json=lastPresentId,proto3" json:"last_present_id,omitempty"`
	PresentList          []*UserSetInfo_UserPresentInfo `protobuf:"bytes,4,rep,name=present_list,json=presentList,proto3" json:"present_list,omitempty"`
	CollectTotal         uint32                         `protobuf:"varint,5,opt,name=collect_total,json=collectTotal,proto3" json:"collect_total,omitempty"`
	CollectProgress      uint32                         `protobuf:"varint,6,opt,name=collect_progress,json=collectProgress,proto3" json:"collect_progress,omitempty"`
	RarestPresentId      uint32                         `protobuf:"varint,7,opt,name=rarest_present_id,json=rarestPresentId,proto3" json:"rarest_present_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *UserSetInfo) Reset()         { *m = UserSetInfo{} }
func (m *UserSetInfo) String() string { return proto.CompactTextString(m) }
func (*UserSetInfo) ProtoMessage()    {}
func (*UserSetInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{19}
}
func (m *UserSetInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSetInfo.Unmarshal(m, b)
}
func (m *UserSetInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSetInfo.Marshal(b, m, deterministic)
}
func (dst *UserSetInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSetInfo.Merge(dst, src)
}
func (m *UserSetInfo) XXX_Size() int {
	return xxx_messageInfo_UserSetInfo.Size(m)
}
func (m *UserSetInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSetInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserSetInfo proto.InternalMessageInfo

func (m *UserSetInfo) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *UserSetInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserSetInfo) GetLastPresentId() uint32 {
	if m != nil {
		return m.LastPresentId
	}
	return 0
}

func (m *UserSetInfo) GetPresentList() []*UserSetInfo_UserPresentInfo {
	if m != nil {
		return m.PresentList
	}
	return nil
}

func (m *UserSetInfo) GetCollectTotal() uint32 {
	if m != nil {
		return m.CollectTotal
	}
	return 0
}

func (m *UserSetInfo) GetCollectProgress() uint32 {
	if m != nil {
		return m.CollectProgress
	}
	return 0
}

func (m *UserSetInfo) GetRarestPresentId() uint32 {
	if m != nil {
		return m.RarestPresentId
	}
	return 0
}

// 用户礼物信息
type UserSetInfo_UserPresentInfo struct {
	PresentId            uint32   `protobuf:"varint,1,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	IsCollected          bool     `protobuf:"varint,2,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty"`
	IsNew                bool     `protobuf:"varint,3,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSetInfo_UserPresentInfo) Reset()         { *m = UserSetInfo_UserPresentInfo{} }
func (m *UserSetInfo_UserPresentInfo) String() string { return proto.CompactTextString(m) }
func (*UserSetInfo_UserPresentInfo) ProtoMessage()    {}
func (*UserSetInfo_UserPresentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{19, 0}
}
func (m *UserSetInfo_UserPresentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSetInfo_UserPresentInfo.Unmarshal(m, b)
}
func (m *UserSetInfo_UserPresentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSetInfo_UserPresentInfo.Marshal(b, m, deterministic)
}
func (dst *UserSetInfo_UserPresentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSetInfo_UserPresentInfo.Merge(dst, src)
}
func (m *UserSetInfo_UserPresentInfo) XXX_Size() int {
	return xxx_messageInfo_UserSetInfo_UserPresentInfo.Size(m)
}
func (m *UserSetInfo_UserPresentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSetInfo_UserPresentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserSetInfo_UserPresentInfo proto.InternalMessageInfo

func (m *UserSetInfo_UserPresentInfo) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *UserSetInfo_UserPresentInfo) GetIsCollected() bool {
	if m != nil {
		return m.IsCollected
	}
	return false
}

func (m *UserSetInfo_UserPresentInfo) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

// 用户播报信息
type UserBroadcast struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SetId                uint32   `protobuf:"varint,2,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	PresentId            uint32   `protobuf:"varint,3,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBroadcast) Reset()         { *m = UserBroadcast{} }
func (m *UserBroadcast) String() string { return proto.CompactTextString(m) }
func (*UserBroadcast) ProtoMessage()    {}
func (*UserBroadcast) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{20}
}
func (m *UserBroadcast) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBroadcast.Unmarshal(m, b)
}
func (m *UserBroadcast) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBroadcast.Marshal(b, m, deterministic)
}
func (dst *UserBroadcast) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBroadcast.Merge(dst, src)
}
func (m *UserBroadcast) XXX_Size() int {
	return xxx_messageInfo_UserBroadcast.Size(m)
}
func (m *UserBroadcast) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBroadcast.DiscardUnknown(m)
}

var xxx_messageInfo_UserBroadcast proto.InternalMessageInfo

func (m *UserBroadcast) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserBroadcast) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *UserBroadcast) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

type GetUserSetsInfoResp struct {
	UserSetInfoList       []*UserSetInfo   `protobuf:"bytes,1,rep,name=user_set_info_list,json=userSetInfoList,proto3" json:"user_set_info_list,omitempty"`
	UserBroadcastList     []*UserBroadcast `protobuf:"bytes,2,rep,name=user_broadcast_list,json=userBroadcastList,proto3" json:"user_broadcast_list,omitempty"`
	LastUpdateTime        uint32           `protobuf:"varint,3,opt,name=last_update_time,json=lastUpdateTime,proto3" json:"last_update_time,omitempty"`
	SendAllEmperorSetText string           `protobuf:"bytes,4,opt,name=send_all_emperor_set_text,json=sendAllEmperorSetText,proto3" json:"send_all_emperor_set_text,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}         `json:"-"`
	XXX_unrecognized      []byte           `json:"-"`
	XXX_sizecache         int32            `json:"-"`
}

func (m *GetUserSetsInfoResp) Reset()         { *m = GetUserSetsInfoResp{} }
func (m *GetUserSetsInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserSetsInfoResp) ProtoMessage()    {}
func (*GetUserSetsInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{21}
}
func (m *GetUserSetsInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSetsInfoResp.Unmarshal(m, b)
}
func (m *GetUserSetsInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSetsInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserSetsInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSetsInfoResp.Merge(dst, src)
}
func (m *GetUserSetsInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserSetsInfoResp.Size(m)
}
func (m *GetUserSetsInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSetsInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSetsInfoResp proto.InternalMessageInfo

func (m *GetUserSetsInfoResp) GetUserSetInfoList() []*UserSetInfo {
	if m != nil {
		return m.UserSetInfoList
	}
	return nil
}

func (m *GetUserSetsInfoResp) GetUserBroadcastList() []*UserBroadcast {
	if m != nil {
		return m.UserBroadcastList
	}
	return nil
}

func (m *GetUserSetsInfoResp) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *GetUserSetsInfoResp) GetSendAllEmperorSetText() string {
	if m != nil {
		return m.SendAllEmperorSetText
	}
	return ""
}

type SetAwardsToSetReq struct {
	AwardItems           *SetAwardItem `protobuf:"bytes,1,opt,name=award_items,json=awardItems,proto3" json:"award_items,omitempty"`
	IsValidate           bool          `protobuf:"varint,2,opt,name=is_validate,json=isValidate,proto3" json:"is_validate,omitempty"`
	IsCreate             bool          `protobuf:"varint,3,opt,name=is_create,json=isCreate,proto3" json:"is_create,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetAwardsToSetReq) Reset()         { *m = SetAwardsToSetReq{} }
func (m *SetAwardsToSetReq) String() string { return proto.CompactTextString(m) }
func (*SetAwardsToSetReq) ProtoMessage()    {}
func (*SetAwardsToSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{22}
}
func (m *SetAwardsToSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAwardsToSetReq.Unmarshal(m, b)
}
func (m *SetAwardsToSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAwardsToSetReq.Marshal(b, m, deterministic)
}
func (dst *SetAwardsToSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAwardsToSetReq.Merge(dst, src)
}
func (m *SetAwardsToSetReq) XXX_Size() int {
	return xxx_messageInfo_SetAwardsToSetReq.Size(m)
}
func (m *SetAwardsToSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAwardsToSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetAwardsToSetReq proto.InternalMessageInfo

func (m *SetAwardsToSetReq) GetAwardItems() *SetAwardItem {
	if m != nil {
		return m.AwardItems
	}
	return nil
}

func (m *SetAwardsToSetReq) GetIsValidate() bool {
	if m != nil {
		return m.IsValidate
	}
	return false
}

func (m *SetAwardsToSetReq) GetIsCreate() bool {
	if m != nil {
		return m.IsCreate
	}
	return false
}

type SetAwardsToSetResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAwardsToSetResp) Reset()         { *m = SetAwardsToSetResp{} }
func (m *SetAwardsToSetResp) String() string { return proto.CompactTextString(m) }
func (*SetAwardsToSetResp) ProtoMessage()    {}
func (*SetAwardsToSetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{23}
}
func (m *SetAwardsToSetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAwardsToSetResp.Unmarshal(m, b)
}
func (m *SetAwardsToSetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAwardsToSetResp.Marshal(b, m, deterministic)
}
func (dst *SetAwardsToSetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAwardsToSetResp.Merge(dst, src)
}
func (m *SetAwardsToSetResp) XXX_Size() int {
	return xxx_messageInfo_SetAwardsToSetResp.Size(m)
}
func (m *SetAwardsToSetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAwardsToSetResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetAwardsToSetResp proto.InternalMessageInfo

type DeleteAwardsToSetReq struct {
	SetId                uint32   `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAwardsToSetReq) Reset()         { *m = DeleteAwardsToSetReq{} }
func (m *DeleteAwardsToSetReq) String() string { return proto.CompactTextString(m) }
func (*DeleteAwardsToSetReq) ProtoMessage()    {}
func (*DeleteAwardsToSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{24}
}
func (m *DeleteAwardsToSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAwardsToSetReq.Unmarshal(m, b)
}
func (m *DeleteAwardsToSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAwardsToSetReq.Marshal(b, m, deterministic)
}
func (dst *DeleteAwardsToSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAwardsToSetReq.Merge(dst, src)
}
func (m *DeleteAwardsToSetReq) XXX_Size() int {
	return xxx_messageInfo_DeleteAwardsToSetReq.Size(m)
}
func (m *DeleteAwardsToSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAwardsToSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAwardsToSetReq proto.InternalMessageInfo

func (m *DeleteAwardsToSetReq) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

type DeleteAwardsToSetResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAwardsToSetResp) Reset()         { *m = DeleteAwardsToSetResp{} }
func (m *DeleteAwardsToSetResp) String() string { return proto.CompactTextString(m) }
func (*DeleteAwardsToSetResp) ProtoMessage()    {}
func (*DeleteAwardsToSetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{25}
}
func (m *DeleteAwardsToSetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAwardsToSetResp.Unmarshal(m, b)
}
func (m *DeleteAwardsToSetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAwardsToSetResp.Marshal(b, m, deterministic)
}
func (dst *DeleteAwardsToSetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAwardsToSetResp.Merge(dst, src)
}
func (m *DeleteAwardsToSetResp) XXX_Size() int {
	return xxx_messageInfo_DeleteAwardsToSetResp.Size(m)
}
func (m *DeleteAwardsToSetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAwardsToSetResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAwardsToSetResp proto.InternalMessageInfo

type GetAwardsToSetListReq struct {
	SetId                string   `protobuf:"bytes,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	SetName              string   `protobuf:"bytes,2,opt,name=set_name,json=setName,proto3" json:"set_name,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAwardsToSetListReq) Reset()         { *m = GetAwardsToSetListReq{} }
func (m *GetAwardsToSetListReq) String() string { return proto.CompactTextString(m) }
func (*GetAwardsToSetListReq) ProtoMessage()    {}
func (*GetAwardsToSetListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{26}
}
func (m *GetAwardsToSetListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardsToSetListReq.Unmarshal(m, b)
}
func (m *GetAwardsToSetListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardsToSetListReq.Marshal(b, m, deterministic)
}
func (dst *GetAwardsToSetListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardsToSetListReq.Merge(dst, src)
}
func (m *GetAwardsToSetListReq) XXX_Size() int {
	return xxx_messageInfo_GetAwardsToSetListReq.Size(m)
}
func (m *GetAwardsToSetListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardsToSetListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardsToSetListReq proto.InternalMessageInfo

func (m *GetAwardsToSetListReq) GetSetId() string {
	if m != nil {
		return m.SetId
	}
	return ""
}

func (m *GetAwardsToSetListReq) GetSetName() string {
	if m != nil {
		return m.SetName
	}
	return ""
}

func (m *GetAwardsToSetListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAwardsToSetListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetAwardsToSetListResp struct {
	SetAwardItemList     []*SetAwardItem `protobuf:"bytes,1,rep,name=set_award_item_list,json=setAwardItemList,proto3" json:"set_award_item_list,omitempty"`
	Total                uint32          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAwardsToSetListResp) Reset()         { *m = GetAwardsToSetListResp{} }
func (m *GetAwardsToSetListResp) String() string { return proto.CompactTextString(m) }
func (*GetAwardsToSetListResp) ProtoMessage()    {}
func (*GetAwardsToSetListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{27}
}
func (m *GetAwardsToSetListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardsToSetListResp.Unmarshal(m, b)
}
func (m *GetAwardsToSetListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardsToSetListResp.Marshal(b, m, deterministic)
}
func (dst *GetAwardsToSetListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardsToSetListResp.Merge(dst, src)
}
func (m *GetAwardsToSetListResp) XXX_Size() int {
	return xxx_messageInfo_GetAwardsToSetListResp.Size(m)
}
func (m *GetAwardsToSetListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardsToSetListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardsToSetListResp proto.InternalMessageInfo

func (m *GetAwardsToSetListResp) GetSetAwardItemList() []*SetAwardItem {
	if m != nil {
		return m.SetAwardItemList
	}
	return nil
}

func (m *GetAwardsToSetListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetPresentSetListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentSetListReq) Reset()         { *m = GetPresentSetListReq{} }
func (m *GetPresentSetListReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentSetListReq) ProtoMessage()    {}
func (*GetPresentSetListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{28}
}
func (m *GetPresentSetListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentSetListReq.Unmarshal(m, b)
}
func (m *GetPresentSetListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentSetListReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentSetListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentSetListReq.Merge(dst, src)
}
func (m *GetPresentSetListReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentSetListReq.Size(m)
}
func (m *GetPresentSetListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentSetListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentSetListReq proto.InternalMessageInfo

type GetPresentSetListResp struct {
	PresentSetList       []*PresentSetConfig `protobuf:"bytes,1,rep,name=present_set_list,json=presentSetList,proto3" json:"present_set_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetPresentSetListResp) Reset()         { *m = GetPresentSetListResp{} }
func (m *GetPresentSetListResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentSetListResp) ProtoMessage()    {}
func (*GetPresentSetListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{29}
}
func (m *GetPresentSetListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentSetListResp.Unmarshal(m, b)
}
func (m *GetPresentSetListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentSetListResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentSetListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentSetListResp.Merge(dst, src)
}
func (m *GetPresentSetListResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentSetListResp.Size(m)
}
func (m *GetPresentSetListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentSetListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentSetListResp proto.InternalMessageInfo

func (m *GetPresentSetListResp) GetPresentSetList() []*PresentSetConfig {
	if m != nil {
		return m.PresentSetList
	}
	return nil
}

type UserSentSetPresentReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PresentId            uint32   `protobuf:"varint,2,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	PresentCount         uint32   `protobuf:"varint,3,opt,name=present_count,json=presentCount,proto3" json:"present_count,omitempty"`
	OrderId              []string `protobuf:"bytes,4,rep,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	SendTime             uint32   `protobuf:"varint,5,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSentSetPresentReq) Reset()         { *m = UserSentSetPresentReq{} }
func (m *UserSentSetPresentReq) String() string { return proto.CompactTextString(m) }
func (*UserSentSetPresentReq) ProtoMessage()    {}
func (*UserSentSetPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{30}
}
func (m *UserSentSetPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSentSetPresentReq.Unmarshal(m, b)
}
func (m *UserSentSetPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSentSetPresentReq.Marshal(b, m, deterministic)
}
func (dst *UserSentSetPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSentSetPresentReq.Merge(dst, src)
}
func (m *UserSentSetPresentReq) XXX_Size() int {
	return xxx_messageInfo_UserSentSetPresentReq.Size(m)
}
func (m *UserSentSetPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSentSetPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserSentSetPresentReq proto.InternalMessageInfo

func (m *UserSentSetPresentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserSentSetPresentReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *UserSentSetPresentReq) GetPresentCount() uint32 {
	if m != nil {
		return m.PresentCount
	}
	return 0
}

func (m *UserSentSetPresentReq) GetOrderId() []string {
	if m != nil {
		return m.OrderId
	}
	return nil
}

func (m *UserSentSetPresentReq) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

type UserSentSetPresentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSentSetPresentResp) Reset()         { *m = UserSentSetPresentResp{} }
func (m *UserSentSetPresentResp) String() string { return proto.CompactTextString(m) }
func (*UserSentSetPresentResp) ProtoMessage()    {}
func (*UserSentSetPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{31}
}
func (m *UserSentSetPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSentSetPresentResp.Unmarshal(m, b)
}
func (m *UserSentSetPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSentSetPresentResp.Marshal(b, m, deterministic)
}
func (dst *UserSentSetPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSentSetPresentResp.Merge(dst, src)
}
func (m *UserSentSetPresentResp) XXX_Size() int {
	return xxx_messageInfo_UserSentSetPresentResp.Size(m)
}
func (m *UserSentSetPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSentSetPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserSentSetPresentResp proto.InternalMessageInfo

type CheckPresentInActiveSetReq struct {
	PresentId            uint32   `protobuf:"varint,1,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckPresentInActiveSetReq) Reset()         { *m = CheckPresentInActiveSetReq{} }
func (m *CheckPresentInActiveSetReq) String() string { return proto.CompactTextString(m) }
func (*CheckPresentInActiveSetReq) ProtoMessage()    {}
func (*CheckPresentInActiveSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{32}
}
func (m *CheckPresentInActiveSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPresentInActiveSetReq.Unmarshal(m, b)
}
func (m *CheckPresentInActiveSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPresentInActiveSetReq.Marshal(b, m, deterministic)
}
func (dst *CheckPresentInActiveSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPresentInActiveSetReq.Merge(dst, src)
}
func (m *CheckPresentInActiveSetReq) XXX_Size() int {
	return xxx_messageInfo_CheckPresentInActiveSetReq.Size(m)
}
func (m *CheckPresentInActiveSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPresentInActiveSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPresentInActiveSetReq proto.InternalMessageInfo

func (m *CheckPresentInActiveSetReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

type CheckPresentInActiveSetResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckPresentInActiveSetResp) Reset()         { *m = CheckPresentInActiveSetResp{} }
func (m *CheckPresentInActiveSetResp) String() string { return proto.CompactTextString(m) }
func (*CheckPresentInActiveSetResp) ProtoMessage()    {}
func (*CheckPresentInActiveSetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{33}
}
func (m *CheckPresentInActiveSetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPresentInActiveSetResp.Unmarshal(m, b)
}
func (m *CheckPresentInActiveSetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPresentInActiveSetResp.Marshal(b, m, deterministic)
}
func (dst *CheckPresentInActiveSetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPresentInActiveSetResp.Merge(dst, src)
}
func (m *CheckPresentInActiveSetResp) XXX_Size() int {
	return xxx_messageInfo_CheckPresentInActiveSetResp.Size(m)
}
func (m *CheckPresentInActiveSetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPresentInActiveSetResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPresentInActiveSetResp proto.InternalMessageInfo

type CheckPresentInActiveSetWithCacheReq struct {
	PresentId            uint32   `protobuf:"varint,1,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckPresentInActiveSetWithCacheReq) Reset()         { *m = CheckPresentInActiveSetWithCacheReq{} }
func (m *CheckPresentInActiveSetWithCacheReq) String() string { return proto.CompactTextString(m) }
func (*CheckPresentInActiveSetWithCacheReq) ProtoMessage()    {}
func (*CheckPresentInActiveSetWithCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{34}
}
func (m *CheckPresentInActiveSetWithCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPresentInActiveSetWithCacheReq.Unmarshal(m, b)
}
func (m *CheckPresentInActiveSetWithCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPresentInActiveSetWithCacheReq.Marshal(b, m, deterministic)
}
func (dst *CheckPresentInActiveSetWithCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPresentInActiveSetWithCacheReq.Merge(dst, src)
}
func (m *CheckPresentInActiveSetWithCacheReq) XXX_Size() int {
	return xxx_messageInfo_CheckPresentInActiveSetWithCacheReq.Size(m)
}
func (m *CheckPresentInActiveSetWithCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPresentInActiveSetWithCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPresentInActiveSetWithCacheReq proto.InternalMessageInfo

func (m *CheckPresentInActiveSetWithCacheReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

type CheckPresentInActiveSetWithCacheResp struct {
	IsActive             bool     `protobuf:"varint,1,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckPresentInActiveSetWithCacheResp) Reset()         { *m = CheckPresentInActiveSetWithCacheResp{} }
func (m *CheckPresentInActiveSetWithCacheResp) String() string { return proto.CompactTextString(m) }
func (*CheckPresentInActiveSetWithCacheResp) ProtoMessage()    {}
func (*CheckPresentInActiveSetWithCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{35}
}
func (m *CheckPresentInActiveSetWithCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPresentInActiveSetWithCacheResp.Unmarshal(m, b)
}
func (m *CheckPresentInActiveSetWithCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPresentInActiveSetWithCacheResp.Marshal(b, m, deterministic)
}
func (dst *CheckPresentInActiveSetWithCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPresentInActiveSetWithCacheResp.Merge(dst, src)
}
func (m *CheckPresentInActiveSetWithCacheResp) XXX_Size() int {
	return xxx_messageInfo_CheckPresentInActiveSetWithCacheResp.Size(m)
}
func (m *CheckPresentInActiveSetWithCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPresentInActiveSetWithCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPresentInActiveSetWithCacheResp proto.InternalMessageInfo

func (m *CheckPresentInActiveSetWithCacheResp) GetIsActive() bool {
	if m != nil {
		return m.IsActive
	}
	return false
}

type TestResetUserInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestResetUserInfoReq) Reset()         { *m = TestResetUserInfoReq{} }
func (m *TestResetUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*TestResetUserInfoReq) ProtoMessage()    {}
func (*TestResetUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{36}
}
func (m *TestResetUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestResetUserInfoReq.Unmarshal(m, b)
}
func (m *TestResetUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestResetUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *TestResetUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestResetUserInfoReq.Merge(dst, src)
}
func (m *TestResetUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_TestResetUserInfoReq.Size(m)
}
func (m *TestResetUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestResetUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestResetUserInfoReq proto.InternalMessageInfo

func (m *TestResetUserInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type TestResetUserInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestResetUserInfoResp) Reset()         { *m = TestResetUserInfoResp{} }
func (m *TestResetUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*TestResetUserInfoResp) ProtoMessage()    {}
func (*TestResetUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{37}
}
func (m *TestResetUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestResetUserInfoResp.Unmarshal(m, b)
}
func (m *TestResetUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestResetUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *TestResetUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestResetUserInfoResp.Merge(dst, src)
}
func (m *TestResetUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_TestResetUserInfoResp.Size(m)
}
func (m *TestResetUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestResetUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestResetUserInfoResp proto.InternalMessageInfo

type GetSetPresentConfigListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSetPresentConfigListReq) Reset()         { *m = GetSetPresentConfigListReq{} }
func (m *GetSetPresentConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetSetPresentConfigListReq) ProtoMessage()    {}
func (*GetSetPresentConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{38}
}
func (m *GetSetPresentConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSetPresentConfigListReq.Unmarshal(m, b)
}
func (m *GetSetPresentConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSetPresentConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetSetPresentConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSetPresentConfigListReq.Merge(dst, src)
}
func (m *GetSetPresentConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetSetPresentConfigListReq.Size(m)
}
func (m *GetSetPresentConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSetPresentConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSetPresentConfigListReq proto.InternalMessageInfo

type SimpleStPresentItemConfig struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl              string   `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	Rank                 uint32   `protobuf:"varint,5,opt,name=rank,proto3" json:"rank,omitempty"`
	EffectBegin          uint32   `protobuf:"varint,6,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32   `protobuf:"varint,7,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	IsDel                bool     `protobuf:"varint,8,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	Tag                  uint32   `protobuf:"varint,9,opt,name=tag,proto3" json:"tag,omitempty"`
	IsTest               bool     `protobuf:"varint,10,opt,name=is_test,json=isTest,proto3" json:"is_test,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimpleStPresentItemConfig) Reset()         { *m = SimpleStPresentItemConfig{} }
func (m *SimpleStPresentItemConfig) String() string { return proto.CompactTextString(m) }
func (*SimpleStPresentItemConfig) ProtoMessage()    {}
func (*SimpleStPresentItemConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{39}
}
func (m *SimpleStPresentItemConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleStPresentItemConfig.Unmarshal(m, b)
}
func (m *SimpleStPresentItemConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleStPresentItemConfig.Marshal(b, m, deterministic)
}
func (dst *SimpleStPresentItemConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleStPresentItemConfig.Merge(dst, src)
}
func (m *SimpleStPresentItemConfig) XXX_Size() int {
	return xxx_messageInfo_SimpleStPresentItemConfig.Size(m)
}
func (m *SimpleStPresentItemConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleStPresentItemConfig.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleStPresentItemConfig proto.InternalMessageInfo

func (m *SimpleStPresentItemConfig) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *SimpleStPresentItemConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SimpleStPresentItemConfig) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *SimpleStPresentItemConfig) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *SimpleStPresentItemConfig) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *SimpleStPresentItemConfig) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *SimpleStPresentItemConfig) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *SimpleStPresentItemConfig) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *SimpleStPresentItemConfig) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *SimpleStPresentItemConfig) GetIsTest() bool {
	if m != nil {
		return m.IsTest
	}
	return false
}

type GetSetPresentConfigListResp struct {
	PresentList          []*SimpleStPresentItemConfig `protobuf:"bytes,1,rep,name=present_list,json=presentList,proto3" json:"present_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetSetPresentConfigListResp) Reset()         { *m = GetSetPresentConfigListResp{} }
func (m *GetSetPresentConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetSetPresentConfigListResp) ProtoMessage()    {}
func (*GetSetPresentConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{40}
}
func (m *GetSetPresentConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSetPresentConfigListResp.Unmarshal(m, b)
}
func (m *GetSetPresentConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSetPresentConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetSetPresentConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSetPresentConfigListResp.Merge(dst, src)
}
func (m *GetSetPresentConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetSetPresentConfigListResp.Size(m)
}
func (m *GetSetPresentConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSetPresentConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSetPresentConfigListResp proto.InternalMessageInfo

func (m *GetSetPresentConfigListResp) GetPresentList() []*SimpleStPresentItemConfig {
	if m != nil {
		return m.PresentList
	}
	return nil
}

type CheckPresentValidReq struct {
	PresentId            uint32   `protobuf:"varint,1,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckPresentValidReq) Reset()         { *m = CheckPresentValidReq{} }
func (m *CheckPresentValidReq) String() string { return proto.CompactTextString(m) }
func (*CheckPresentValidReq) ProtoMessage()    {}
func (*CheckPresentValidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{41}
}
func (m *CheckPresentValidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPresentValidReq.Unmarshal(m, b)
}
func (m *CheckPresentValidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPresentValidReq.Marshal(b, m, deterministic)
}
func (dst *CheckPresentValidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPresentValidReq.Merge(dst, src)
}
func (m *CheckPresentValidReq) XXX_Size() int {
	return xxx_messageInfo_CheckPresentValidReq.Size(m)
}
func (m *CheckPresentValidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPresentValidReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPresentValidReq proto.InternalMessageInfo

func (m *CheckPresentValidReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *CheckPresentValidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckPresentValidResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckPresentValidResp) Reset()         { *m = CheckPresentValidResp{} }
func (m *CheckPresentValidResp) String() string { return proto.CompactTextString(m) }
func (*CheckPresentValidResp) ProtoMessage()    {}
func (*CheckPresentValidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{42}
}
func (m *CheckPresentValidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPresentValidResp.Unmarshal(m, b)
}
func (m *CheckPresentValidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPresentValidResp.Marshal(b, m, deterministic)
}
func (dst *CheckPresentValidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPresentValidResp.Merge(dst, src)
}
func (m *CheckPresentValidResp) XXX_Size() int {
	return xxx_messageInfo_CheckPresentValidResp.Size(m)
}
func (m *CheckPresentValidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPresentValidResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPresentValidResp proto.InternalMessageInfo

type LotteryPresentInfo struct {
	PresentId            uint32   `protobuf:"varint,1,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	PresentName          string   `protobuf:"bytes,2,opt,name=present_name,json=presentName,proto3" json:"present_name,omitempty"`
	IsOrigin             bool     `protobuf:"varint,3,opt,name=is_origin,json=isOrigin,proto3" json:"is_origin,omitempty"`
	IsCollected          bool     `protobuf:"varint,4,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty"`
	Threshold            uint32   `protobuf:"varint,5,opt,name=threshold,proto3" json:"threshold,omitempty"`
	IsReachThreshold     bool     `protobuf:"varint,6,opt,name=is_reach_threshold,json=isReachThreshold,proto3" json:"is_reach_threshold,omitempty"`
	Guaranteed           int32    `protobuf:"varint,7,opt,name=guaranteed,proto3" json:"guaranteed,omitempty"`
	IsReachGuaranteed    bool     `protobuf:"varint,8,opt,name=is_reach_guaranteed,json=isReachGuaranteed,proto3" json:"is_reach_guaranteed,omitempty"`
	IsTriggerGuaranteed  bool     `protobuf:"varint,9,opt,name=is_trigger_guaranteed,json=isTriggerGuaranteed,proto3" json:"is_trigger_guaranteed,omitempty"`
	Probability          float64  `protobuf:"fixed64,10,opt,name=probability,proto3" json:"probability,omitempty"`
	IsLottery            bool     `protobuf:"varint,11,opt,name=is_lottery,json=isLottery,proto3" json:"is_lottery,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LotteryPresentInfo) Reset()         { *m = LotteryPresentInfo{} }
func (m *LotteryPresentInfo) String() string { return proto.CompactTextString(m) }
func (*LotteryPresentInfo) ProtoMessage()    {}
func (*LotteryPresentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{43}
}
func (m *LotteryPresentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryPresentInfo.Unmarshal(m, b)
}
func (m *LotteryPresentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryPresentInfo.Marshal(b, m, deterministic)
}
func (dst *LotteryPresentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryPresentInfo.Merge(dst, src)
}
func (m *LotteryPresentInfo) XXX_Size() int {
	return xxx_messageInfo_LotteryPresentInfo.Size(m)
}
func (m *LotteryPresentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryPresentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryPresentInfo proto.InternalMessageInfo

func (m *LotteryPresentInfo) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *LotteryPresentInfo) GetPresentName() string {
	if m != nil {
		return m.PresentName
	}
	return ""
}

func (m *LotteryPresentInfo) GetIsOrigin() bool {
	if m != nil {
		return m.IsOrigin
	}
	return false
}

func (m *LotteryPresentInfo) GetIsCollected() bool {
	if m != nil {
		return m.IsCollected
	}
	return false
}

func (m *LotteryPresentInfo) GetThreshold() uint32 {
	if m != nil {
		return m.Threshold
	}
	return 0
}

func (m *LotteryPresentInfo) GetIsReachThreshold() bool {
	if m != nil {
		return m.IsReachThreshold
	}
	return false
}

func (m *LotteryPresentInfo) GetGuaranteed() int32 {
	if m != nil {
		return m.Guaranteed
	}
	return 0
}

func (m *LotteryPresentInfo) GetIsReachGuaranteed() bool {
	if m != nil {
		return m.IsReachGuaranteed
	}
	return false
}

func (m *LotteryPresentInfo) GetIsTriggerGuaranteed() bool {
	if m != nil {
		return m.IsTriggerGuaranteed
	}
	return false
}

func (m *LotteryPresentInfo) GetProbability() float64 {
	if m != nil {
		return m.Probability
	}
	return 0
}

func (m *LotteryPresentInfo) GetIsLottery() bool {
	if m != nil {
		return m.IsLottery
	}
	return false
}

type TestGetUserSetLotteryInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SetId                uint32   `protobuf:"varint,2,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestGetUserSetLotteryInfoReq) Reset()         { *m = TestGetUserSetLotteryInfoReq{} }
func (m *TestGetUserSetLotteryInfoReq) String() string { return proto.CompactTextString(m) }
func (*TestGetUserSetLotteryInfoReq) ProtoMessage()    {}
func (*TestGetUserSetLotteryInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{44}
}
func (m *TestGetUserSetLotteryInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestGetUserSetLotteryInfoReq.Unmarshal(m, b)
}
func (m *TestGetUserSetLotteryInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestGetUserSetLotteryInfoReq.Marshal(b, m, deterministic)
}
func (dst *TestGetUserSetLotteryInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestGetUserSetLotteryInfoReq.Merge(dst, src)
}
func (m *TestGetUserSetLotteryInfoReq) XXX_Size() int {
	return xxx_messageInfo_TestGetUserSetLotteryInfoReq.Size(m)
}
func (m *TestGetUserSetLotteryInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestGetUserSetLotteryInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestGetUserSetLotteryInfoReq proto.InternalMessageInfo

func (m *TestGetUserSetLotteryInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestGetUserSetLotteryInfoReq) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

type TestGetUserSetLotteryInfoResp struct {
	Uid                    uint32                                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SetId                  uint32                                    `protobuf:"varint,2,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	LotteryPresentInfoList []*LotteryPresentInfo                     `protobuf:"bytes,4,rep,name=lottery_present_info_list,json=lotteryPresentInfoList,proto3" json:"lottery_present_info_list,omitempty"`
	SendPresentCounter     uint32                                    `protobuf:"varint,5,opt,name=send_present_counter,json=sendPresentCounter,proto3" json:"send_present_counter,omitempty"`
	CollectedPresentList   []uint32                                  `protobuf:"varint,6,rep,packed,name=collected_present_list,json=collectedPresentList,proto3" json:"collected_present_list,omitempty"`
	PoolInfoList           []*TestGetUserSetLotteryInfoResp_PoolInfo `protobuf:"bytes,7,rep,name=pool_info_list,json=poolInfoList,proto3" json:"pool_info_list,omitempty"`
	LotteryProbability     string                                    `protobuf:"bytes,8,opt,name=lottery_probability,json=lotteryProbability,proto3" json:"lottery_probability,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                                  `json:"-"`
	XXX_unrecognized       []byte                                    `json:"-"`
	XXX_sizecache          int32                                     `json:"-"`
}

func (m *TestGetUserSetLotteryInfoResp) Reset()         { *m = TestGetUserSetLotteryInfoResp{} }
func (m *TestGetUserSetLotteryInfoResp) String() string { return proto.CompactTextString(m) }
func (*TestGetUserSetLotteryInfoResp) ProtoMessage()    {}
func (*TestGetUserSetLotteryInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{45}
}
func (m *TestGetUserSetLotteryInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestGetUserSetLotteryInfoResp.Unmarshal(m, b)
}
func (m *TestGetUserSetLotteryInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestGetUserSetLotteryInfoResp.Marshal(b, m, deterministic)
}
func (dst *TestGetUserSetLotteryInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestGetUserSetLotteryInfoResp.Merge(dst, src)
}
func (m *TestGetUserSetLotteryInfoResp) XXX_Size() int {
	return xxx_messageInfo_TestGetUserSetLotteryInfoResp.Size(m)
}
func (m *TestGetUserSetLotteryInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestGetUserSetLotteryInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestGetUserSetLotteryInfoResp proto.InternalMessageInfo

func (m *TestGetUserSetLotteryInfoResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestGetUserSetLotteryInfoResp) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *TestGetUserSetLotteryInfoResp) GetLotteryPresentInfoList() []*LotteryPresentInfo {
	if m != nil {
		return m.LotteryPresentInfoList
	}
	return nil
}

func (m *TestGetUserSetLotteryInfoResp) GetSendPresentCounter() uint32 {
	if m != nil {
		return m.SendPresentCounter
	}
	return 0
}

func (m *TestGetUserSetLotteryInfoResp) GetCollectedPresentList() []uint32 {
	if m != nil {
		return m.CollectedPresentList
	}
	return nil
}

func (m *TestGetUserSetLotteryInfoResp) GetPoolInfoList() []*TestGetUserSetLotteryInfoResp_PoolInfo {
	if m != nil {
		return m.PoolInfoList
	}
	return nil
}

func (m *TestGetUserSetLotteryInfoResp) GetLotteryProbability() string {
	if m != nil {
		return m.LotteryProbability
	}
	return ""
}

type TestGetUserSetLotteryInfoResp_PoolInfo struct {
	PresentId            uint32   `protobuf:"varint,1,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	PresentName          string   `protobuf:"bytes,2,opt,name=present_name,json=presentName,proto3" json:"present_name,omitempty"`
	Guaranteed           int32    `protobuf:"varint,3,opt,name=guaranteed,proto3" json:"guaranteed,omitempty"`
	Probability          string   `protobuf:"bytes,4,opt,name=probability,proto3" json:"probability,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestGetUserSetLotteryInfoResp_PoolInfo) Reset() {
	*m = TestGetUserSetLotteryInfoResp_PoolInfo{}
}
func (m *TestGetUserSetLotteryInfoResp_PoolInfo) String() string { return proto.CompactTextString(m) }
func (*TestGetUserSetLotteryInfoResp_PoolInfo) ProtoMessage()    {}
func (*TestGetUserSetLotteryInfoResp_PoolInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{45, 0}
}
func (m *TestGetUserSetLotteryInfoResp_PoolInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestGetUserSetLotteryInfoResp_PoolInfo.Unmarshal(m, b)
}
func (m *TestGetUserSetLotteryInfoResp_PoolInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestGetUserSetLotteryInfoResp_PoolInfo.Marshal(b, m, deterministic)
}
func (dst *TestGetUserSetLotteryInfoResp_PoolInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestGetUserSetLotteryInfoResp_PoolInfo.Merge(dst, src)
}
func (m *TestGetUserSetLotteryInfoResp_PoolInfo) XXX_Size() int {
	return xxx_messageInfo_TestGetUserSetLotteryInfoResp_PoolInfo.Size(m)
}
func (m *TestGetUserSetLotteryInfoResp_PoolInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TestGetUserSetLotteryInfoResp_PoolInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TestGetUserSetLotteryInfoResp_PoolInfo proto.InternalMessageInfo

func (m *TestGetUserSetLotteryInfoResp_PoolInfo) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *TestGetUserSetLotteryInfoResp_PoolInfo) GetPresentName() string {
	if m != nil {
		return m.PresentName
	}
	return ""
}

func (m *TestGetUserSetLotteryInfoResp_PoolInfo) GetGuaranteed() int32 {
	if m != nil {
		return m.Guaranteed
	}
	return 0
}

func (m *TestGetUserSetLotteryInfoResp_PoolInfo) GetProbability() string {
	if m != nil {
		return m.Probability
	}
	return ""
}

type CreateEmperorSetReq struct {
	EmperorSet           *EmperorSetConfig `protobuf:"bytes,1,opt,name=emperor_set,json=emperorSet,proto3" json:"emperor_set,omitempty"`
	IsValidate           bool              `protobuf:"varint,2,opt,name=is_validate,json=isValidate,proto3" json:"is_validate,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CreateEmperorSetReq) Reset()         { *m = CreateEmperorSetReq{} }
func (m *CreateEmperorSetReq) String() string { return proto.CompactTextString(m) }
func (*CreateEmperorSetReq) ProtoMessage()    {}
func (*CreateEmperorSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{46}
}
func (m *CreateEmperorSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateEmperorSetReq.Unmarshal(m, b)
}
func (m *CreateEmperorSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateEmperorSetReq.Marshal(b, m, deterministic)
}
func (dst *CreateEmperorSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateEmperorSetReq.Merge(dst, src)
}
func (m *CreateEmperorSetReq) XXX_Size() int {
	return xxx_messageInfo_CreateEmperorSetReq.Size(m)
}
func (m *CreateEmperorSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateEmperorSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateEmperorSetReq proto.InternalMessageInfo

func (m *CreateEmperorSetReq) GetEmperorSet() *EmperorSetConfig {
	if m != nil {
		return m.EmperorSet
	}
	return nil
}

func (m *CreateEmperorSetReq) GetIsValidate() bool {
	if m != nil {
		return m.IsValidate
	}
	return false
}

type CreateEmperorSetResp struct {
	SetId                uint32   `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateEmperorSetResp) Reset()         { *m = CreateEmperorSetResp{} }
func (m *CreateEmperorSetResp) String() string { return proto.CompactTextString(m) }
func (*CreateEmperorSetResp) ProtoMessage()    {}
func (*CreateEmperorSetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{47}
}
func (m *CreateEmperorSetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateEmperorSetResp.Unmarshal(m, b)
}
func (m *CreateEmperorSetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateEmperorSetResp.Marshal(b, m, deterministic)
}
func (dst *CreateEmperorSetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateEmperorSetResp.Merge(dst, src)
}
func (m *CreateEmperorSetResp) XXX_Size() int {
	return xxx_messageInfo_CreateEmperorSetResp.Size(m)
}
func (m *CreateEmperorSetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateEmperorSetResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateEmperorSetResp proto.InternalMessageInfo

func (m *CreateEmperorSetResp) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

type GetEmperorSetListNoCacheReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	SetName              string   `protobuf:"bytes,3,opt,name=set_name,json=setName,proto3" json:"set_name,omitempty"`
	SetId                uint32   `protobuf:"varint,4,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	IncludeDel           bool     `protobuf:"varint,5,opt,name=include_del,json=includeDel,proto3" json:"include_del,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEmperorSetListNoCacheReq) Reset()         { *m = GetEmperorSetListNoCacheReq{} }
func (m *GetEmperorSetListNoCacheReq) String() string { return proto.CompactTextString(m) }
func (*GetEmperorSetListNoCacheReq) ProtoMessage()    {}
func (*GetEmperorSetListNoCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{48}
}
func (m *GetEmperorSetListNoCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEmperorSetListNoCacheReq.Unmarshal(m, b)
}
func (m *GetEmperorSetListNoCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEmperorSetListNoCacheReq.Marshal(b, m, deterministic)
}
func (dst *GetEmperorSetListNoCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEmperorSetListNoCacheReq.Merge(dst, src)
}
func (m *GetEmperorSetListNoCacheReq) XXX_Size() int {
	return xxx_messageInfo_GetEmperorSetListNoCacheReq.Size(m)
}
func (m *GetEmperorSetListNoCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEmperorSetListNoCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEmperorSetListNoCacheReq proto.InternalMessageInfo

func (m *GetEmperorSetListNoCacheReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetEmperorSetListNoCacheReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetEmperorSetListNoCacheReq) GetSetName() string {
	if m != nil {
		return m.SetName
	}
	return ""
}

func (m *GetEmperorSetListNoCacheReq) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *GetEmperorSetListNoCacheReq) GetIncludeDel() bool {
	if m != nil {
		return m.IncludeDel
	}
	return false
}

type GetEmperorSetListNoCacheResp struct {
	EmperorSetList       []*EmperorSetConfig `protobuf:"bytes,1,rep,name=emperor_set_list,json=emperorSetList,proto3" json:"emperor_set_list,omitempty"`
	Total                uint32              `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetEmperorSetListNoCacheResp) Reset()         { *m = GetEmperorSetListNoCacheResp{} }
func (m *GetEmperorSetListNoCacheResp) String() string { return proto.CompactTextString(m) }
func (*GetEmperorSetListNoCacheResp) ProtoMessage()    {}
func (*GetEmperorSetListNoCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{49}
}
func (m *GetEmperorSetListNoCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEmperorSetListNoCacheResp.Unmarshal(m, b)
}
func (m *GetEmperorSetListNoCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEmperorSetListNoCacheResp.Marshal(b, m, deterministic)
}
func (dst *GetEmperorSetListNoCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEmperorSetListNoCacheResp.Merge(dst, src)
}
func (m *GetEmperorSetListNoCacheResp) XXX_Size() int {
	return xxx_messageInfo_GetEmperorSetListNoCacheResp.Size(m)
}
func (m *GetEmperorSetListNoCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEmperorSetListNoCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEmperorSetListNoCacheResp proto.InternalMessageInfo

func (m *GetEmperorSetListNoCacheResp) GetEmperorSetList() []*EmperorSetConfig {
	if m != nil {
		return m.EmperorSetList
	}
	return nil
}

func (m *GetEmperorSetListNoCacheResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetEmperorSetListReq struct {
	IncludeDel           bool     `protobuf:"varint,5,opt,name=include_del,json=includeDel,proto3" json:"include_del,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEmperorSetListReq) Reset()         { *m = GetEmperorSetListReq{} }
func (m *GetEmperorSetListReq) String() string { return proto.CompactTextString(m) }
func (*GetEmperorSetListReq) ProtoMessage()    {}
func (*GetEmperorSetListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{50}
}
func (m *GetEmperorSetListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEmperorSetListReq.Unmarshal(m, b)
}
func (m *GetEmperorSetListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEmperorSetListReq.Marshal(b, m, deterministic)
}
func (dst *GetEmperorSetListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEmperorSetListReq.Merge(dst, src)
}
func (m *GetEmperorSetListReq) XXX_Size() int {
	return xxx_messageInfo_GetEmperorSetListReq.Size(m)
}
func (m *GetEmperorSetListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEmperorSetListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEmperorSetListReq proto.InternalMessageInfo

func (m *GetEmperorSetListReq) GetIncludeDel() bool {
	if m != nil {
		return m.IncludeDel
	}
	return false
}

type GetEmperorSetListResp struct {
	EmperorSetList       []*EmperorSetConfig `protobuf:"bytes,1,rep,name=emperor_set_list,json=emperorSetList,proto3" json:"emperor_set_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetEmperorSetListResp) Reset()         { *m = GetEmperorSetListResp{} }
func (m *GetEmperorSetListResp) String() string { return proto.CompactTextString(m) }
func (*GetEmperorSetListResp) ProtoMessage()    {}
func (*GetEmperorSetListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{51}
}
func (m *GetEmperorSetListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEmperorSetListResp.Unmarshal(m, b)
}
func (m *GetEmperorSetListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEmperorSetListResp.Marshal(b, m, deterministic)
}
func (dst *GetEmperorSetListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEmperorSetListResp.Merge(dst, src)
}
func (m *GetEmperorSetListResp) XXX_Size() int {
	return xxx_messageInfo_GetEmperorSetListResp.Size(m)
}
func (m *GetEmperorSetListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEmperorSetListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEmperorSetListResp proto.InternalMessageInfo

func (m *GetEmperorSetListResp) GetEmperorSetList() []*EmperorSetConfig {
	if m != nil {
		return m.EmperorSetList
	}
	return nil
}

type GetEmperorSetInfoReq struct {
	SetId                uint32   `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	IncludeDel           bool     `protobuf:"varint,5,opt,name=include_del,json=includeDel,proto3" json:"include_del,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEmperorSetInfoReq) Reset()         { *m = GetEmperorSetInfoReq{} }
func (m *GetEmperorSetInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetEmperorSetInfoReq) ProtoMessage()    {}
func (*GetEmperorSetInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{52}
}
func (m *GetEmperorSetInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEmperorSetInfoReq.Unmarshal(m, b)
}
func (m *GetEmperorSetInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEmperorSetInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetEmperorSetInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEmperorSetInfoReq.Merge(dst, src)
}
func (m *GetEmperorSetInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetEmperorSetInfoReq.Size(m)
}
func (m *GetEmperorSetInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEmperorSetInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEmperorSetInfoReq proto.InternalMessageInfo

func (m *GetEmperorSetInfoReq) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *GetEmperorSetInfoReq) GetIncludeDel() bool {
	if m != nil {
		return m.IncludeDel
	}
	return false
}

type GetEmperorSetInfoResp struct {
	EmperorSet           *EmperorSetConfig `protobuf:"bytes,1,opt,name=emperor_set,json=emperorSet,proto3" json:"emperor_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetEmperorSetInfoResp) Reset()         { *m = GetEmperorSetInfoResp{} }
func (m *GetEmperorSetInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetEmperorSetInfoResp) ProtoMessage()    {}
func (*GetEmperorSetInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{53}
}
func (m *GetEmperorSetInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEmperorSetInfoResp.Unmarshal(m, b)
}
func (m *GetEmperorSetInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEmperorSetInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetEmperorSetInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEmperorSetInfoResp.Merge(dst, src)
}
func (m *GetEmperorSetInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetEmperorSetInfoResp.Size(m)
}
func (m *GetEmperorSetInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEmperorSetInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEmperorSetInfoResp proto.InternalMessageInfo

func (m *GetEmperorSetInfoResp) GetEmperorSet() *EmperorSetConfig {
	if m != nil {
		return m.EmperorSet
	}
	return nil
}

type DeleteEmperorSetReq struct {
	SetId                uint32   `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteEmperorSetReq) Reset()         { *m = DeleteEmperorSetReq{} }
func (m *DeleteEmperorSetReq) String() string { return proto.CompactTextString(m) }
func (*DeleteEmperorSetReq) ProtoMessage()    {}
func (*DeleteEmperorSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{54}
}
func (m *DeleteEmperorSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteEmperorSetReq.Unmarshal(m, b)
}
func (m *DeleteEmperorSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteEmperorSetReq.Marshal(b, m, deterministic)
}
func (dst *DeleteEmperorSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteEmperorSetReq.Merge(dst, src)
}
func (m *DeleteEmperorSetReq) XXX_Size() int {
	return xxx_messageInfo_DeleteEmperorSetReq.Size(m)
}
func (m *DeleteEmperorSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteEmperorSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteEmperorSetReq proto.InternalMessageInfo

func (m *DeleteEmperorSetReq) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

type DeleteEmperorSetResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteEmperorSetResp) Reset()         { *m = DeleteEmperorSetResp{} }
func (m *DeleteEmperorSetResp) String() string { return proto.CompactTextString(m) }
func (*DeleteEmperorSetResp) ProtoMessage()    {}
func (*DeleteEmperorSetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{55}
}
func (m *DeleteEmperorSetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteEmperorSetResp.Unmarshal(m, b)
}
func (m *DeleteEmperorSetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteEmperorSetResp.Marshal(b, m, deterministic)
}
func (dst *DeleteEmperorSetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteEmperorSetResp.Merge(dst, src)
}
func (m *DeleteEmperorSetResp) XXX_Size() int {
	return xxx_messageInfo_DeleteEmperorSetResp.Size(m)
}
func (m *DeleteEmperorSetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteEmperorSetResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteEmperorSetResp proto.InternalMessageInfo

type CheckPresentInActiveEmperorSetWithCacheReq struct {
	PresentId            uint32   `protobuf:"varint,1,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckPresentInActiveEmperorSetWithCacheReq) Reset() {
	*m = CheckPresentInActiveEmperorSetWithCacheReq{}
}
func (m *CheckPresentInActiveEmperorSetWithCacheReq) String() string {
	return proto.CompactTextString(m)
}
func (*CheckPresentInActiveEmperorSetWithCacheReq) ProtoMessage() {}
func (*CheckPresentInActiveEmperorSetWithCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{56}
}
func (m *CheckPresentInActiveEmperorSetWithCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPresentInActiveEmperorSetWithCacheReq.Unmarshal(m, b)
}
func (m *CheckPresentInActiveEmperorSetWithCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPresentInActiveEmperorSetWithCacheReq.Marshal(b, m, deterministic)
}
func (dst *CheckPresentInActiveEmperorSetWithCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPresentInActiveEmperorSetWithCacheReq.Merge(dst, src)
}
func (m *CheckPresentInActiveEmperorSetWithCacheReq) XXX_Size() int {
	return xxx_messageInfo_CheckPresentInActiveEmperorSetWithCacheReq.Size(m)
}
func (m *CheckPresentInActiveEmperorSetWithCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPresentInActiveEmperorSetWithCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPresentInActiveEmperorSetWithCacheReq proto.InternalMessageInfo

func (m *CheckPresentInActiveEmperorSetWithCacheReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

type CheckPresentInActiveEmperorSetWithCacheResp struct {
	IsActive             bool     `protobuf:"varint,1,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckPresentInActiveEmperorSetWithCacheResp) Reset() {
	*m = CheckPresentInActiveEmperorSetWithCacheResp{}
}
func (m *CheckPresentInActiveEmperorSetWithCacheResp) String() string {
	return proto.CompactTextString(m)
}
func (*CheckPresentInActiveEmperorSetWithCacheResp) ProtoMessage() {}
func (*CheckPresentInActiveEmperorSetWithCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{57}
}
func (m *CheckPresentInActiveEmperorSetWithCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPresentInActiveEmperorSetWithCacheResp.Unmarshal(m, b)
}
func (m *CheckPresentInActiveEmperorSetWithCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPresentInActiveEmperorSetWithCacheResp.Marshal(b, m, deterministic)
}
func (dst *CheckPresentInActiveEmperorSetWithCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPresentInActiveEmperorSetWithCacheResp.Merge(dst, src)
}
func (m *CheckPresentInActiveEmperorSetWithCacheResp) XXX_Size() int {
	return xxx_messageInfo_CheckPresentInActiveEmperorSetWithCacheResp.Size(m)
}
func (m *CheckPresentInActiveEmperorSetWithCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPresentInActiveEmperorSetWithCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPresentInActiveEmperorSetWithCacheResp proto.InternalMessageInfo

func (m *CheckPresentInActiveEmperorSetWithCacheResp) GetIsActive() bool {
	if m != nil {
		return m.IsActive
	}
	return false
}

type ValidUserCanSendSetByEmperorSetIdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	EmperorSetId         uint32   `protobuf:"varint,2,opt,name=emperor_set_id,json=emperorSetId,proto3" json:"emperor_set_id,omitempty"`
	SendTime             int64    `protobuf:"varint,3,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ValidUserCanSendSetByEmperorSetIdReq) Reset()         { *m = ValidUserCanSendSetByEmperorSetIdReq{} }
func (m *ValidUserCanSendSetByEmperorSetIdReq) String() string { return proto.CompactTextString(m) }
func (*ValidUserCanSendSetByEmperorSetIdReq) ProtoMessage()    {}
func (*ValidUserCanSendSetByEmperorSetIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{58}
}
func (m *ValidUserCanSendSetByEmperorSetIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidUserCanSendSetByEmperorSetIdReq.Unmarshal(m, b)
}
func (m *ValidUserCanSendSetByEmperorSetIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidUserCanSendSetByEmperorSetIdReq.Marshal(b, m, deterministic)
}
func (dst *ValidUserCanSendSetByEmperorSetIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidUserCanSendSetByEmperorSetIdReq.Merge(dst, src)
}
func (m *ValidUserCanSendSetByEmperorSetIdReq) XXX_Size() int {
	return xxx_messageInfo_ValidUserCanSendSetByEmperorSetIdReq.Size(m)
}
func (m *ValidUserCanSendSetByEmperorSetIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidUserCanSendSetByEmperorSetIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_ValidUserCanSendSetByEmperorSetIdReq proto.InternalMessageInfo

func (m *ValidUserCanSendSetByEmperorSetIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ValidUserCanSendSetByEmperorSetIdReq) GetEmperorSetId() uint32 {
	if m != nil {
		return m.EmperorSetId
	}
	return 0
}

func (m *ValidUserCanSendSetByEmperorSetIdReq) GetSendTime() int64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

type ValidUserCanSendSetByEmperorSetIdResp struct {
	CanSend              bool     `protobuf:"varint,1,opt,name=can_send,json=canSend,proto3" json:"can_send,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ValidUserCanSendSetByEmperorSetIdResp) Reset()         { *m = ValidUserCanSendSetByEmperorSetIdResp{} }
func (m *ValidUserCanSendSetByEmperorSetIdResp) String() string { return proto.CompactTextString(m) }
func (*ValidUserCanSendSetByEmperorSetIdResp) ProtoMessage()    {}
func (*ValidUserCanSendSetByEmperorSetIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{59}
}
func (m *ValidUserCanSendSetByEmperorSetIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidUserCanSendSetByEmperorSetIdResp.Unmarshal(m, b)
}
func (m *ValidUserCanSendSetByEmperorSetIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidUserCanSendSetByEmperorSetIdResp.Marshal(b, m, deterministic)
}
func (dst *ValidUserCanSendSetByEmperorSetIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidUserCanSendSetByEmperorSetIdResp.Merge(dst, src)
}
func (m *ValidUserCanSendSetByEmperorSetIdResp) XXX_Size() int {
	return xxx_messageInfo_ValidUserCanSendSetByEmperorSetIdResp.Size(m)
}
func (m *ValidUserCanSendSetByEmperorSetIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidUserCanSendSetByEmperorSetIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_ValidUserCanSendSetByEmperorSetIdResp proto.InternalMessageInfo

func (m *ValidUserCanSendSetByEmperorSetIdResp) GetCanSend() bool {
	if m != nil {
		return m.CanSend
	}
	return false
}

type UserSentEmperorSetPresentReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	SetId                uint32   `protobuf:"varint,3,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	OrderId              string   `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	SendTime             uint32   `protobuf:"varint,5,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	FromUkwAccount       string   `protobuf:"bytes,6,opt,name=from_ukw_account,json=fromUkwAccount,proto3" json:"from_ukw_account,omitempty"`
	FromUkwNickname      string   `protobuf:"bytes,7,opt,name=from_ukw_nickname,json=fromUkwNickname,proto3" json:"from_ukw_nickname,omitempty"`
	ToUkwAccount         string   `protobuf:"bytes,8,opt,name=to_ukw_account,json=toUkwAccount,proto3" json:"to_ukw_account,omitempty"`
	ToUkwNickname        string   `protobuf:"bytes,9,opt,name=to_ukw_nickname,json=toUkwNickname,proto3" json:"to_ukw_nickname,omitempty"`
	ChannelId            uint32   `protobuf:"varint,10,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Score                uint32   `protobuf:"varint,11,opt,name=score,proto3" json:"score,omitempty"`
	AddRich              uint32   `protobuf:"varint,12,opt,name=add_rich,json=addRich,proto3" json:"add_rich,omitempty"`
	AddCharm             uint32   `protobuf:"varint,13,opt,name=add_charm,json=addCharm,proto3" json:"add_charm,omitempty"`
	GuildId              uint32   `protobuf:"varint,14,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSentEmperorSetPresentReq) Reset()         { *m = UserSentEmperorSetPresentReq{} }
func (m *UserSentEmperorSetPresentReq) String() string { return proto.CompactTextString(m) }
func (*UserSentEmperorSetPresentReq) ProtoMessage()    {}
func (*UserSentEmperorSetPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{60}
}
func (m *UserSentEmperorSetPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSentEmperorSetPresentReq.Unmarshal(m, b)
}
func (m *UserSentEmperorSetPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSentEmperorSetPresentReq.Marshal(b, m, deterministic)
}
func (dst *UserSentEmperorSetPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSentEmperorSetPresentReq.Merge(dst, src)
}
func (m *UserSentEmperorSetPresentReq) XXX_Size() int {
	return xxx_messageInfo_UserSentEmperorSetPresentReq.Size(m)
}
func (m *UserSentEmperorSetPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSentEmperorSetPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserSentEmperorSetPresentReq proto.InternalMessageInfo

func (m *UserSentEmperorSetPresentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserSentEmperorSetPresentReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *UserSentEmperorSetPresentReq) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *UserSentEmperorSetPresentReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UserSentEmperorSetPresentReq) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *UserSentEmperorSetPresentReq) GetFromUkwAccount() string {
	if m != nil {
		return m.FromUkwAccount
	}
	return ""
}

func (m *UserSentEmperorSetPresentReq) GetFromUkwNickname() string {
	if m != nil {
		return m.FromUkwNickname
	}
	return ""
}

func (m *UserSentEmperorSetPresentReq) GetToUkwAccount() string {
	if m != nil {
		return m.ToUkwAccount
	}
	return ""
}

func (m *UserSentEmperorSetPresentReq) GetToUkwNickname() string {
	if m != nil {
		return m.ToUkwNickname
	}
	return ""
}

func (m *UserSentEmperorSetPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserSentEmperorSetPresentReq) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *UserSentEmperorSetPresentReq) GetAddRich() uint32 {
	if m != nil {
		return m.AddRich
	}
	return 0
}

func (m *UserSentEmperorSetPresentReq) GetAddCharm() uint32 {
	if m != nil {
		return m.AddCharm
	}
	return 0
}

func (m *UserSentEmperorSetPresentReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type UserSentEmperorSetPresentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSentEmperorSetPresentResp) Reset()         { *m = UserSentEmperorSetPresentResp{} }
func (m *UserSentEmperorSetPresentResp) String() string { return proto.CompactTextString(m) }
func (*UserSentEmperorSetPresentResp) ProtoMessage()    {}
func (*UserSentEmperorSetPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{61}
}
func (m *UserSentEmperorSetPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSentEmperorSetPresentResp.Unmarshal(m, b)
}
func (m *UserSentEmperorSetPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSentEmperorSetPresentResp.Marshal(b, m, deterministic)
}
func (dst *UserSentEmperorSetPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSentEmperorSetPresentResp.Merge(dst, src)
}
func (m *UserSentEmperorSetPresentResp) XXX_Size() int {
	return xxx_messageInfo_UserSentEmperorSetPresentResp.Size(m)
}
func (m *UserSentEmperorSetPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSentEmperorSetPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserSentEmperorSetPresentResp proto.InternalMessageInfo

type GetUserEmperorSetHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserEmperorSetHistoryReq) Reset()         { *m = GetUserEmperorSetHistoryReq{} }
func (m *GetUserEmperorSetHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetUserEmperorSetHistoryReq) ProtoMessage()    {}
func (*GetUserEmperorSetHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{62}
}
func (m *GetUserEmperorSetHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserEmperorSetHistoryReq.Unmarshal(m, b)
}
func (m *GetUserEmperorSetHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserEmperorSetHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetUserEmperorSetHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserEmperorSetHistoryReq.Merge(dst, src)
}
func (m *GetUserEmperorSetHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetUserEmperorSetHistoryReq.Size(m)
}
func (m *GetUserEmperorSetHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserEmperorSetHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserEmperorSetHistoryReq proto.InternalMessageInfo

func (m *GetUserEmperorSetHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserEmperorSetHistoryResp struct {
	EmperorSetList       []*EmperorSetHistory `protobuf:"bytes,1,rep,name=emperor_set_list,json=emperorSetList,proto3" json:"emperor_set_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetUserEmperorSetHistoryResp) Reset()         { *m = GetUserEmperorSetHistoryResp{} }
func (m *GetUserEmperorSetHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetUserEmperorSetHistoryResp) ProtoMessage()    {}
func (*GetUserEmperorSetHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{63}
}
func (m *GetUserEmperorSetHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserEmperorSetHistoryResp.Unmarshal(m, b)
}
func (m *GetUserEmperorSetHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserEmperorSetHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetUserEmperorSetHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserEmperorSetHistoryResp.Merge(dst, src)
}
func (m *GetUserEmperorSetHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetUserEmperorSetHistoryResp.Size(m)
}
func (m *GetUserEmperorSetHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserEmperorSetHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserEmperorSetHistoryResp proto.InternalMessageInfo

func (m *GetUserEmperorSetHistoryResp) GetEmperorSetList() []*EmperorSetHistory {
	if m != nil {
		return m.EmperorSetList
	}
	return nil
}

type GetEmperorSetSummaryReq struct {
	SummaryId            uint32   `protobuf:"varint,1,opt,name=summary_id,json=summaryId,proto3" json:"summary_id,omitempty"`
	SummaryType          uint32   `protobuf:"varint,2,opt,name=summary_type,json=summaryType,proto3" json:"summary_type,omitempty"`
	SubId                uint32   `protobuf:"varint,3,opt,name=sub_id,json=subId,proto3" json:"sub_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEmperorSetSummaryReq) Reset()         { *m = GetEmperorSetSummaryReq{} }
func (m *GetEmperorSetSummaryReq) String() string { return proto.CompactTextString(m) }
func (*GetEmperorSetSummaryReq) ProtoMessage()    {}
func (*GetEmperorSetSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{64}
}
func (m *GetEmperorSetSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEmperorSetSummaryReq.Unmarshal(m, b)
}
func (m *GetEmperorSetSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEmperorSetSummaryReq.Marshal(b, m, deterministic)
}
func (dst *GetEmperorSetSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEmperorSetSummaryReq.Merge(dst, src)
}
func (m *GetEmperorSetSummaryReq) XXX_Size() int {
	return xxx_messageInfo_GetEmperorSetSummaryReq.Size(m)
}
func (m *GetEmperorSetSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEmperorSetSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEmperorSetSummaryReq proto.InternalMessageInfo

func (m *GetEmperorSetSummaryReq) GetSummaryId() uint32 {
	if m != nil {
		return m.SummaryId
	}
	return 0
}

func (m *GetEmperorSetSummaryReq) GetSummaryType() uint32 {
	if m != nil {
		return m.SummaryType
	}
	return 0
}

func (m *GetEmperorSetSummaryReq) GetSubId() uint32 {
	if m != nil {
		return m.SubId
	}
	return 0
}

type GetEmperorSetSummaryResp struct {
	EmperorSumList       []*EmperorSetSummary `protobuf:"bytes,1,rep,name=emperor_sum_list,json=emperorSumList,proto3" json:"emperor_sum_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetEmperorSetSummaryResp) Reset()         { *m = GetEmperorSetSummaryResp{} }
func (m *GetEmperorSetSummaryResp) String() string { return proto.CompactTextString(m) }
func (*GetEmperorSetSummaryResp) ProtoMessage()    {}
func (*GetEmperorSetSummaryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{65}
}
func (m *GetEmperorSetSummaryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEmperorSetSummaryResp.Unmarshal(m, b)
}
func (m *GetEmperorSetSummaryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEmperorSetSummaryResp.Marshal(b, m, deterministic)
}
func (dst *GetEmperorSetSummaryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEmperorSetSummaryResp.Merge(dst, src)
}
func (m *GetEmperorSetSummaryResp) XXX_Size() int {
	return xxx_messageInfo_GetEmperorSetSummaryResp.Size(m)
}
func (m *GetEmperorSetSummaryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEmperorSetSummaryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEmperorSetSummaryResp proto.InternalMessageInfo

func (m *GetEmperorSetSummaryResp) GetEmperorSumList() []*EmperorSetSummary {
	if m != nil {
		return m.EmperorSumList
	}
	return nil
}

type EmperorSetHistory struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	SetId                uint32   `protobuf:"varint,3,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	SendTime             uint32   `protobuf:"varint,4,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	IsUkw                bool     `protobuf:"varint,5,opt,name=is_ukw,json=isUkw,proto3" json:"is_ukw,omitempty"`
	ChannelId            uint32   `protobuf:"varint,6,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmperorSetHistory) Reset()         { *m = EmperorSetHistory{} }
func (m *EmperorSetHistory) String() string { return proto.CompactTextString(m) }
func (*EmperorSetHistory) ProtoMessage()    {}
func (*EmperorSetHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{66}
}
func (m *EmperorSetHistory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmperorSetHistory.Unmarshal(m, b)
}
func (m *EmperorSetHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmperorSetHistory.Marshal(b, m, deterministic)
}
func (dst *EmperorSetHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmperorSetHistory.Merge(dst, src)
}
func (m *EmperorSetHistory) XXX_Size() int {
	return xxx_messageInfo_EmperorSetHistory.Size(m)
}
func (m *EmperorSetHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_EmperorSetHistory.DiscardUnknown(m)
}

var xxx_messageInfo_EmperorSetHistory proto.InternalMessageInfo

func (m *EmperorSetHistory) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *EmperorSetHistory) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *EmperorSetHistory) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *EmperorSetHistory) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *EmperorSetHistory) GetIsUkw() bool {
	if m != nil {
		return m.IsUkw
	}
	return false
}

func (m *EmperorSetHistory) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type EmperorSetSummary struct {
	SetId                uint32   `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	SinglePrice          uint32   `protobuf:"varint,3,opt,name=single_price,json=singlePrice,proto3" json:"single_price,omitempty"`
	Rank                 float32  `protobuf:"fixed32,4,opt,name=rank,proto3" json:"rank,omitempty"`
	BeginTime            uint32   `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmperorSetSummary) Reset()         { *m = EmperorSetSummary{} }
func (m *EmperorSetSummary) String() string { return proto.CompactTextString(m) }
func (*EmperorSetSummary) ProtoMessage()    {}
func (*EmperorSetSummary) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_set_5d3f6fb74367b8ed, []int{67}
}
func (m *EmperorSetSummary) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmperorSetSummary.Unmarshal(m, b)
}
func (m *EmperorSetSummary) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmperorSetSummary.Marshal(b, m, deterministic)
}
func (dst *EmperorSetSummary) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmperorSetSummary.Merge(dst, src)
}
func (m *EmperorSetSummary) XXX_Size() int {
	return xxx_messageInfo_EmperorSetSummary.Size(m)
}
func (m *EmperorSetSummary) XXX_DiscardUnknown() {
	xxx_messageInfo_EmperorSetSummary.DiscardUnknown(m)
}

var xxx_messageInfo_EmperorSetSummary proto.InternalMessageInfo

func (m *EmperorSetSummary) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *EmperorSetSummary) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *EmperorSetSummary) GetSinglePrice() uint32 {
	if m != nil {
		return m.SinglePrice
	}
	return 0
}

func (m *EmperorSetSummary) GetRank() float32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *EmperorSetSummary) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *EmperorSetSummary) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func init() {
	proto.RegisterType((*PresentSetConfig)(nil), "present_set.PresentSetConfig")
	proto.RegisterType((*SetPresentItem)(nil), "present_set.SetPresentItem")
	proto.RegisterType((*SetAwardItem)(nil), "present_set.SetAwardItem")
	proto.RegisterType((*AwardItem)(nil), "present_set.AwardItem")
	proto.RegisterType((*EmperorSetConfig)(nil), "present_set.EmperorSetConfig")
	proto.RegisterType((*EmperorSetConfig_EmperorSetEffect)(nil), "present_set.EmperorSetConfig.EmperorSetEffect")
	proto.RegisterType((*EmperorSetPresentItem)(nil), "present_set.EmperorSetPresentItem")
	proto.RegisterType((*CreatePresentSetReq)(nil), "present_set.CreatePresentSetReq")
	proto.RegisterType((*CreatePresentSetResp)(nil), "present_set.CreatePresentSetResp")
	proto.RegisterType((*GetPresentSetInfoReq)(nil), "present_set.GetPresentSetInfoReq")
	proto.RegisterType((*GetPresentSetInfoResp)(nil), "present_set.GetPresentSetInfoResp")
	proto.RegisterType((*GetPresentSetListNoCacheReq)(nil), "present_set.GetPresentSetListNoCacheReq")
	proto.RegisterType((*GetPresentSetListNoCacheResp)(nil), "present_set.GetPresentSetListNoCacheResp")
	proto.RegisterType((*GetPresentSetSimpleListNoCacheReq)(nil), "present_set.GetPresentSetSimpleListNoCacheReq")
	proto.RegisterType((*GetPresentSetSimpleListNoCacheResp)(nil), "present_set.GetPresentSetSimpleListNoCacheResp")
	proto.RegisterType((*DeletePresentSetReq)(nil), "present_set.DeletePresentSetReq")
	proto.RegisterType((*DeletePresentSetResp)(nil), "present_set.DeletePresentSetResp")
	proto.RegisterType((*SetPresentsToSetReq)(nil), "present_set.SetPresentsToSetReq")
	proto.RegisterType((*SetPresentsToSetResp)(nil), "present_set.SetPresentsToSetResp")
	proto.RegisterType((*GetUserSetsInfoReq)(nil), "present_set.GetUserSetsInfoReq")
	proto.RegisterType((*UserSetInfo)(nil), "present_set.UserSetInfo")
	proto.RegisterType((*UserSetInfo_UserPresentInfo)(nil), "present_set.UserSetInfo.UserPresentInfo")
	proto.RegisterType((*UserBroadcast)(nil), "present_set.UserBroadcast")
	proto.RegisterType((*GetUserSetsInfoResp)(nil), "present_set.GetUserSetsInfoResp")
	proto.RegisterType((*SetAwardsToSetReq)(nil), "present_set.SetAwardsToSetReq")
	proto.RegisterType((*SetAwardsToSetResp)(nil), "present_set.SetAwardsToSetResp")
	proto.RegisterType((*DeleteAwardsToSetReq)(nil), "present_set.DeleteAwardsToSetReq")
	proto.RegisterType((*DeleteAwardsToSetResp)(nil), "present_set.DeleteAwardsToSetResp")
	proto.RegisterType((*GetAwardsToSetListReq)(nil), "present_set.GetAwardsToSetListReq")
	proto.RegisterType((*GetAwardsToSetListResp)(nil), "present_set.GetAwardsToSetListResp")
	proto.RegisterType((*GetPresentSetListReq)(nil), "present_set.GetPresentSetListReq")
	proto.RegisterType((*GetPresentSetListResp)(nil), "present_set.GetPresentSetListResp")
	proto.RegisterType((*UserSentSetPresentReq)(nil), "present_set.UserSentSetPresentReq")
	proto.RegisterType((*UserSentSetPresentResp)(nil), "present_set.UserSentSetPresentResp")
	proto.RegisterType((*CheckPresentInActiveSetReq)(nil), "present_set.CheckPresentInActiveSetReq")
	proto.RegisterType((*CheckPresentInActiveSetResp)(nil), "present_set.CheckPresentInActiveSetResp")
	proto.RegisterType((*CheckPresentInActiveSetWithCacheReq)(nil), "present_set.CheckPresentInActiveSetWithCacheReq")
	proto.RegisterType((*CheckPresentInActiveSetWithCacheResp)(nil), "present_set.CheckPresentInActiveSetWithCacheResp")
	proto.RegisterType((*TestResetUserInfoReq)(nil), "present_set.TestResetUserInfoReq")
	proto.RegisterType((*TestResetUserInfoResp)(nil), "present_set.TestResetUserInfoResp")
	proto.RegisterType((*GetSetPresentConfigListReq)(nil), "present_set.GetSetPresentConfigListReq")
	proto.RegisterType((*SimpleStPresentItemConfig)(nil), "present_set.SimpleStPresentItemConfig")
	proto.RegisterType((*GetSetPresentConfigListResp)(nil), "present_set.GetSetPresentConfigListResp")
	proto.RegisterType((*CheckPresentValidReq)(nil), "present_set.CheckPresentValidReq")
	proto.RegisterType((*CheckPresentValidResp)(nil), "present_set.CheckPresentValidResp")
	proto.RegisterType((*LotteryPresentInfo)(nil), "present_set.LotteryPresentInfo")
	proto.RegisterType((*TestGetUserSetLotteryInfoReq)(nil), "present_set.TestGetUserSetLotteryInfoReq")
	proto.RegisterType((*TestGetUserSetLotteryInfoResp)(nil), "present_set.TestGetUserSetLotteryInfoResp")
	proto.RegisterType((*TestGetUserSetLotteryInfoResp_PoolInfo)(nil), "present_set.TestGetUserSetLotteryInfoResp.PoolInfo")
	proto.RegisterType((*CreateEmperorSetReq)(nil), "present_set.CreateEmperorSetReq")
	proto.RegisterType((*CreateEmperorSetResp)(nil), "present_set.CreateEmperorSetResp")
	proto.RegisterType((*GetEmperorSetListNoCacheReq)(nil), "present_set.GetEmperorSetListNoCacheReq")
	proto.RegisterType((*GetEmperorSetListNoCacheResp)(nil), "present_set.GetEmperorSetListNoCacheResp")
	proto.RegisterType((*GetEmperorSetListReq)(nil), "present_set.GetEmperorSetListReq")
	proto.RegisterType((*GetEmperorSetListResp)(nil), "present_set.GetEmperorSetListResp")
	proto.RegisterType((*GetEmperorSetInfoReq)(nil), "present_set.GetEmperorSetInfoReq")
	proto.RegisterType((*GetEmperorSetInfoResp)(nil), "present_set.GetEmperorSetInfoResp")
	proto.RegisterType((*DeleteEmperorSetReq)(nil), "present_set.DeleteEmperorSetReq")
	proto.RegisterType((*DeleteEmperorSetResp)(nil), "present_set.DeleteEmperorSetResp")
	proto.RegisterType((*CheckPresentInActiveEmperorSetWithCacheReq)(nil), "present_set.CheckPresentInActiveEmperorSetWithCacheReq")
	proto.RegisterType((*CheckPresentInActiveEmperorSetWithCacheResp)(nil), "present_set.CheckPresentInActiveEmperorSetWithCacheResp")
	proto.RegisterType((*ValidUserCanSendSetByEmperorSetIdReq)(nil), "present_set.ValidUserCanSendSetByEmperorSetIdReq")
	proto.RegisterType((*ValidUserCanSendSetByEmperorSetIdResp)(nil), "present_set.ValidUserCanSendSetByEmperorSetIdResp")
	proto.RegisterType((*UserSentEmperorSetPresentReq)(nil), "present_set.UserSentEmperorSetPresentReq")
	proto.RegisterType((*UserSentEmperorSetPresentResp)(nil), "present_set.UserSentEmperorSetPresentResp")
	proto.RegisterType((*GetUserEmperorSetHistoryReq)(nil), "present_set.GetUserEmperorSetHistoryReq")
	proto.RegisterType((*GetUserEmperorSetHistoryResp)(nil), "present_set.GetUserEmperorSetHistoryResp")
	proto.RegisterType((*GetEmperorSetSummaryReq)(nil), "present_set.GetEmperorSetSummaryReq")
	proto.RegisterType((*GetEmperorSetSummaryResp)(nil), "present_set.GetEmperorSetSummaryResp")
	proto.RegisterType((*EmperorSetHistory)(nil), "present_set.EmperorSetHistory")
	proto.RegisterType((*EmperorSetSummary)(nil), "present_set.EmperorSetSummary")
	proto.RegisterEnum("present_set.PresentLevel", PresentLevel_name, PresentLevel_value)
	proto.RegisterEnum("present_set.PresentSetPeriod", PresentSetPeriod_name, PresentSetPeriod_value)
	proto.RegisterEnum("present_set.PresentSetActiveStatus", PresentSetActiveStatus_name, PresentSetActiveStatus_value)
	proto.RegisterEnum("present_set.PresentSetPreviewType", PresentSetPreviewType_name, PresentSetPreviewType_value)
	proto.RegisterEnum("present_set.CollectSourceType", CollectSourceType_name, CollectSourceType_value)
	proto.RegisterEnum("present_set.ActivityLinkType", ActivityLinkType_name, ActivityLinkType_value)
	proto.RegisterEnum("present_set.EmperorSetBannerJumpType", EmperorSetBannerJumpType_name, EmperorSetBannerJumpType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PresentSetClient is the client API for PresentSet service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PresentSetClient interface {
	// 创建礼物套组
	CreatePresentSet(ctx context.Context, in *CreatePresentSetReq, opts ...grpc.CallOption) (*CreatePresentSetResp, error)
	// 获取礼物套组信息
	GetPresentSetInfoNoCache(ctx context.Context, in *GetPresentSetInfoReq, opts ...grpc.CallOption) (*GetPresentSetInfoResp, error)
	// 获取礼物套组列表
	GetPresentSetListNoCache(ctx context.Context, in *GetPresentSetListNoCacheReq, opts ...grpc.CallOption) (*GetPresentSetListNoCacheResp, error)
	// 获取礼物套组列表
	GetPresentSetSimpleListNoCache(ctx context.Context, in *GetPresentSetSimpleListNoCacheReq, opts ...grpc.CallOption) (*GetPresentSetSimpleListNoCacheResp, error)
	// 设置套组内礼物
	SetPresentsToSet(ctx context.Context, in *SetPresentsToSetReq, opts ...grpc.CallOption) (*SetPresentsToSetResp, error)
	// 删除套组
	DeletePresentSet(ctx context.Context, in *DeletePresentSetReq, opts ...grpc.CallOption) (*DeletePresentSetResp, error)
	// 获取套组礼物信息列表
	GetAwardsToSetList(ctx context.Context, in *GetAwardsToSetListReq, opts ...grpc.CallOption) (*GetAwardsToSetListResp, error)
	// 设置套组内奖励
	SetAwardsToSet(ctx context.Context, in *SetAwardsToSetReq, opts ...grpc.CallOption) (*SetAwardsToSetResp, error)
	// 删除套组内奖励
	DeleteAwardsToSet(ctx context.Context, in *DeleteAwardsToSetReq, opts ...grpc.CallOption) (*DeleteAwardsToSetResp, error)
	// 检查礼物是否处于活跃套组中（管理后台接口）
	CheckPresentInActiveSet(ctx context.Context, in *CheckPresentInActiveSetReq, opts ...grpc.CallOption) (*CheckPresentInActiveSetResp, error)
	// 获取套组礼物配置
	GetSetPresentConfigList(ctx context.Context, in *GetSetPresentConfigListReq, opts ...grpc.CallOption) (*GetSetPresentConfigListResp, error)
	// 获取礼物套组信息
	GetPresentSetInfo(ctx context.Context, in *GetPresentSetInfoReq, opts ...grpc.CallOption) (*GetPresentSetInfoResp, error)
	// 获取用户礼物套组信息
	GetUserSetsInfo(ctx context.Context, in *GetUserSetsInfoReq, opts ...grpc.CallOption) (*GetUserSetsInfoResp, error)
	// 获取礼物套组列表
	GetPresentSetList(ctx context.Context, in *GetPresentSetListReq, opts ...grpc.CallOption) (*GetPresentSetListResp, error)
	// 用户送出套组礼物
	UserSentSetPresent(ctx context.Context, in *UserSentSetPresentReq, opts ...grpc.CallOption) (*UserSentSetPresentResp, error)
	// 检查礼物是否处于活跃套组中（查缓存接口）
	CheckPresentInActiveSetWithCache(ctx context.Context, in *CheckPresentInActiveSetWithCacheReq, opts ...grpc.CallOption) (*CheckPresentInActiveSetWithCacheResp, error)
	// 检查礼物是否可以赠送
	CheckPresentValid(ctx context.Context, in *CheckPresentValidReq, opts ...grpc.CallOption) (*CheckPresentValidResp, error)
	// 创建帝王套
	CreateEmperorSet(ctx context.Context, in *CreateEmperorSetReq, opts ...grpc.CallOption) (*CreateEmperorSetResp, error)
	// 获取帝王套列表
	GetEmperorSetListNoCache(ctx context.Context, in *GetEmperorSetListNoCacheReq, opts ...grpc.CallOption) (*GetEmperorSetListNoCacheResp, error)
	// 获取帝王套信息
	GetEmperorSetInfoNoCache(ctx context.Context, in *GetEmperorSetInfoReq, opts ...grpc.CallOption) (*GetEmperorSetInfoResp, error)
	// 删除帝王套
	DeleteEmperorSet(ctx context.Context, in *DeleteEmperorSetReq, opts ...grpc.CallOption) (*DeleteEmperorSetResp, error)
	// 获取帝王套列表
	GetEmperorSetList(ctx context.Context, in *GetEmperorSetListReq, opts ...grpc.CallOption) (*GetEmperorSetListResp, error)
	// 获取帝王套信息
	GetEmperorSetInfo(ctx context.Context, in *GetEmperorSetInfoReq, opts ...grpc.CallOption) (*GetEmperorSetInfoResp, error)
	// 校验礼物是否在生效的帝王套中
	CheckPresentInActiveEmperorSetWithCache(ctx context.Context, in *CheckPresentInActiveEmperorSetWithCacheReq, opts ...grpc.CallOption) (*CheckPresentInActiveEmperorSetWithCacheResp, error)
	// 判断用户是否可以根据礼物套组送帝王套
	ValidUserCanSendSetByEmperorSetId(ctx context.Context, in *ValidUserCanSendSetByEmperorSetIdReq, opts ...grpc.CallOption) (*ValidUserCanSendSetByEmperorSetIdResp, error)
	// 处理帝王套送出
	UserSentEmperorSetPresent(ctx context.Context, in *UserSentEmperorSetPresentReq, opts ...grpc.CallOption) (*UserSentEmperorSetPresentResp, error)
	// 获取用户帝王套历史
	GetUserEmperorSetHistory(ctx context.Context, in *GetUserEmperorSetHistoryReq, opts ...grpc.CallOption) (*GetUserEmperorSetHistoryResp, error)
	// 获取用户帝王套汇总
	GetEmperorSetSummary(ctx context.Context, in *GetEmperorSetSummaryReq, opts ...grpc.CallOption) (*GetEmperorSetSummaryResp, error)
	// 测试接口 重置用户信息
	TestResetUserInfo(ctx context.Context, in *TestResetUserInfoReq, opts ...grpc.CallOption) (*TestResetUserInfoResp, error)
	// 测试接口 获取用户套组当前抽奖信息
	TestGetUserSetLotteryInfo(ctx context.Context, in *TestGetUserSetLotteryInfoReq, opts ...grpc.CallOption) (*TestGetUserSetLotteryInfoResp, error)
}

type presentSetClient struct {
	cc *grpc.ClientConn
}

func NewPresentSetClient(cc *grpc.ClientConn) PresentSetClient {
	return &presentSetClient{cc}
}

func (c *presentSetClient) CreatePresentSet(ctx context.Context, in *CreatePresentSetReq, opts ...grpc.CallOption) (*CreatePresentSetResp, error) {
	out := new(CreatePresentSetResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/CreatePresentSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) GetPresentSetInfoNoCache(ctx context.Context, in *GetPresentSetInfoReq, opts ...grpc.CallOption) (*GetPresentSetInfoResp, error) {
	out := new(GetPresentSetInfoResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/GetPresentSetInfoNoCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) GetPresentSetListNoCache(ctx context.Context, in *GetPresentSetListNoCacheReq, opts ...grpc.CallOption) (*GetPresentSetListNoCacheResp, error) {
	out := new(GetPresentSetListNoCacheResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/GetPresentSetListNoCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) GetPresentSetSimpleListNoCache(ctx context.Context, in *GetPresentSetSimpleListNoCacheReq, opts ...grpc.CallOption) (*GetPresentSetSimpleListNoCacheResp, error) {
	out := new(GetPresentSetSimpleListNoCacheResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/GetPresentSetSimpleListNoCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) SetPresentsToSet(ctx context.Context, in *SetPresentsToSetReq, opts ...grpc.CallOption) (*SetPresentsToSetResp, error) {
	out := new(SetPresentsToSetResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/SetPresentsToSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) DeletePresentSet(ctx context.Context, in *DeletePresentSetReq, opts ...grpc.CallOption) (*DeletePresentSetResp, error) {
	out := new(DeletePresentSetResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/DeletePresentSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) GetAwardsToSetList(ctx context.Context, in *GetAwardsToSetListReq, opts ...grpc.CallOption) (*GetAwardsToSetListResp, error) {
	out := new(GetAwardsToSetListResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/GetAwardsToSetList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) SetAwardsToSet(ctx context.Context, in *SetAwardsToSetReq, opts ...grpc.CallOption) (*SetAwardsToSetResp, error) {
	out := new(SetAwardsToSetResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/SetAwardsToSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) DeleteAwardsToSet(ctx context.Context, in *DeleteAwardsToSetReq, opts ...grpc.CallOption) (*DeleteAwardsToSetResp, error) {
	out := new(DeleteAwardsToSetResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/DeleteAwardsToSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) CheckPresentInActiveSet(ctx context.Context, in *CheckPresentInActiveSetReq, opts ...grpc.CallOption) (*CheckPresentInActiveSetResp, error) {
	out := new(CheckPresentInActiveSetResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/CheckPresentInActiveSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) GetSetPresentConfigList(ctx context.Context, in *GetSetPresentConfigListReq, opts ...grpc.CallOption) (*GetSetPresentConfigListResp, error) {
	out := new(GetSetPresentConfigListResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/GetSetPresentConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) GetPresentSetInfo(ctx context.Context, in *GetPresentSetInfoReq, opts ...grpc.CallOption) (*GetPresentSetInfoResp, error) {
	out := new(GetPresentSetInfoResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/GetPresentSetInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) GetUserSetsInfo(ctx context.Context, in *GetUserSetsInfoReq, opts ...grpc.CallOption) (*GetUserSetsInfoResp, error) {
	out := new(GetUserSetsInfoResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/GetUserSetsInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) GetPresentSetList(ctx context.Context, in *GetPresentSetListReq, opts ...grpc.CallOption) (*GetPresentSetListResp, error) {
	out := new(GetPresentSetListResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/GetPresentSetList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) UserSentSetPresent(ctx context.Context, in *UserSentSetPresentReq, opts ...grpc.CallOption) (*UserSentSetPresentResp, error) {
	out := new(UserSentSetPresentResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/UserSentSetPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) CheckPresentInActiveSetWithCache(ctx context.Context, in *CheckPresentInActiveSetWithCacheReq, opts ...grpc.CallOption) (*CheckPresentInActiveSetWithCacheResp, error) {
	out := new(CheckPresentInActiveSetWithCacheResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/CheckPresentInActiveSetWithCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) CheckPresentValid(ctx context.Context, in *CheckPresentValidReq, opts ...grpc.CallOption) (*CheckPresentValidResp, error) {
	out := new(CheckPresentValidResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/CheckPresentValid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) CreateEmperorSet(ctx context.Context, in *CreateEmperorSetReq, opts ...grpc.CallOption) (*CreateEmperorSetResp, error) {
	out := new(CreateEmperorSetResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/CreateEmperorSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) GetEmperorSetListNoCache(ctx context.Context, in *GetEmperorSetListNoCacheReq, opts ...grpc.CallOption) (*GetEmperorSetListNoCacheResp, error) {
	out := new(GetEmperorSetListNoCacheResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/GetEmperorSetListNoCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) GetEmperorSetInfoNoCache(ctx context.Context, in *GetEmperorSetInfoReq, opts ...grpc.CallOption) (*GetEmperorSetInfoResp, error) {
	out := new(GetEmperorSetInfoResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/GetEmperorSetInfoNoCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) DeleteEmperorSet(ctx context.Context, in *DeleteEmperorSetReq, opts ...grpc.CallOption) (*DeleteEmperorSetResp, error) {
	out := new(DeleteEmperorSetResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/DeleteEmperorSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) GetEmperorSetList(ctx context.Context, in *GetEmperorSetListReq, opts ...grpc.CallOption) (*GetEmperorSetListResp, error) {
	out := new(GetEmperorSetListResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/GetEmperorSetList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) GetEmperorSetInfo(ctx context.Context, in *GetEmperorSetInfoReq, opts ...grpc.CallOption) (*GetEmperorSetInfoResp, error) {
	out := new(GetEmperorSetInfoResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/GetEmperorSetInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) CheckPresentInActiveEmperorSetWithCache(ctx context.Context, in *CheckPresentInActiveEmperorSetWithCacheReq, opts ...grpc.CallOption) (*CheckPresentInActiveEmperorSetWithCacheResp, error) {
	out := new(CheckPresentInActiveEmperorSetWithCacheResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/CheckPresentInActiveEmperorSetWithCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) ValidUserCanSendSetByEmperorSetId(ctx context.Context, in *ValidUserCanSendSetByEmperorSetIdReq, opts ...grpc.CallOption) (*ValidUserCanSendSetByEmperorSetIdResp, error) {
	out := new(ValidUserCanSendSetByEmperorSetIdResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/ValidUserCanSendSetByEmperorSetId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) UserSentEmperorSetPresent(ctx context.Context, in *UserSentEmperorSetPresentReq, opts ...grpc.CallOption) (*UserSentEmperorSetPresentResp, error) {
	out := new(UserSentEmperorSetPresentResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/UserSentEmperorSetPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) GetUserEmperorSetHistory(ctx context.Context, in *GetUserEmperorSetHistoryReq, opts ...grpc.CallOption) (*GetUserEmperorSetHistoryResp, error) {
	out := new(GetUserEmperorSetHistoryResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/GetUserEmperorSetHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) GetEmperorSetSummary(ctx context.Context, in *GetEmperorSetSummaryReq, opts ...grpc.CallOption) (*GetEmperorSetSummaryResp, error) {
	out := new(GetEmperorSetSummaryResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/GetEmperorSetSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) TestResetUserInfo(ctx context.Context, in *TestResetUserInfoReq, opts ...grpc.CallOption) (*TestResetUserInfoResp, error) {
	out := new(TestResetUserInfoResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/TestResetUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentSetClient) TestGetUserSetLotteryInfo(ctx context.Context, in *TestGetUserSetLotteryInfoReq, opts ...grpc.CallOption) (*TestGetUserSetLotteryInfoResp, error) {
	out := new(TestGetUserSetLotteryInfoResp)
	err := c.cc.Invoke(ctx, "/present_set.PresentSet/TestGetUserSetLotteryInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PresentSetServer is the server API for PresentSet service.
type PresentSetServer interface {
	// 创建礼物套组
	CreatePresentSet(context.Context, *CreatePresentSetReq) (*CreatePresentSetResp, error)
	// 获取礼物套组信息
	GetPresentSetInfoNoCache(context.Context, *GetPresentSetInfoReq) (*GetPresentSetInfoResp, error)
	// 获取礼物套组列表
	GetPresentSetListNoCache(context.Context, *GetPresentSetListNoCacheReq) (*GetPresentSetListNoCacheResp, error)
	// 获取礼物套组列表
	GetPresentSetSimpleListNoCache(context.Context, *GetPresentSetSimpleListNoCacheReq) (*GetPresentSetSimpleListNoCacheResp, error)
	// 设置套组内礼物
	SetPresentsToSet(context.Context, *SetPresentsToSetReq) (*SetPresentsToSetResp, error)
	// 删除套组
	DeletePresentSet(context.Context, *DeletePresentSetReq) (*DeletePresentSetResp, error)
	// 获取套组礼物信息列表
	GetAwardsToSetList(context.Context, *GetAwardsToSetListReq) (*GetAwardsToSetListResp, error)
	// 设置套组内奖励
	SetAwardsToSet(context.Context, *SetAwardsToSetReq) (*SetAwardsToSetResp, error)
	// 删除套组内奖励
	DeleteAwardsToSet(context.Context, *DeleteAwardsToSetReq) (*DeleteAwardsToSetResp, error)
	// 检查礼物是否处于活跃套组中（管理后台接口）
	CheckPresentInActiveSet(context.Context, *CheckPresentInActiveSetReq) (*CheckPresentInActiveSetResp, error)
	// 获取套组礼物配置
	GetSetPresentConfigList(context.Context, *GetSetPresentConfigListReq) (*GetSetPresentConfigListResp, error)
	// 获取礼物套组信息
	GetPresentSetInfo(context.Context, *GetPresentSetInfoReq) (*GetPresentSetInfoResp, error)
	// 获取用户礼物套组信息
	GetUserSetsInfo(context.Context, *GetUserSetsInfoReq) (*GetUserSetsInfoResp, error)
	// 获取礼物套组列表
	GetPresentSetList(context.Context, *GetPresentSetListReq) (*GetPresentSetListResp, error)
	// 用户送出套组礼物
	UserSentSetPresent(context.Context, *UserSentSetPresentReq) (*UserSentSetPresentResp, error)
	// 检查礼物是否处于活跃套组中（查缓存接口）
	CheckPresentInActiveSetWithCache(context.Context, *CheckPresentInActiveSetWithCacheReq) (*CheckPresentInActiveSetWithCacheResp, error)
	// 检查礼物是否可以赠送
	CheckPresentValid(context.Context, *CheckPresentValidReq) (*CheckPresentValidResp, error)
	// 创建帝王套
	CreateEmperorSet(context.Context, *CreateEmperorSetReq) (*CreateEmperorSetResp, error)
	// 获取帝王套列表
	GetEmperorSetListNoCache(context.Context, *GetEmperorSetListNoCacheReq) (*GetEmperorSetListNoCacheResp, error)
	// 获取帝王套信息
	GetEmperorSetInfoNoCache(context.Context, *GetEmperorSetInfoReq) (*GetEmperorSetInfoResp, error)
	// 删除帝王套
	DeleteEmperorSet(context.Context, *DeleteEmperorSetReq) (*DeleteEmperorSetResp, error)
	// 获取帝王套列表
	GetEmperorSetList(context.Context, *GetEmperorSetListReq) (*GetEmperorSetListResp, error)
	// 获取帝王套信息
	GetEmperorSetInfo(context.Context, *GetEmperorSetInfoReq) (*GetEmperorSetInfoResp, error)
	// 校验礼物是否在生效的帝王套中
	CheckPresentInActiveEmperorSetWithCache(context.Context, *CheckPresentInActiveEmperorSetWithCacheReq) (*CheckPresentInActiveEmperorSetWithCacheResp, error)
	// 判断用户是否可以根据礼物套组送帝王套
	ValidUserCanSendSetByEmperorSetId(context.Context, *ValidUserCanSendSetByEmperorSetIdReq) (*ValidUserCanSendSetByEmperorSetIdResp, error)
	// 处理帝王套送出
	UserSentEmperorSetPresent(context.Context, *UserSentEmperorSetPresentReq) (*UserSentEmperorSetPresentResp, error)
	// 获取用户帝王套历史
	GetUserEmperorSetHistory(context.Context, *GetUserEmperorSetHistoryReq) (*GetUserEmperorSetHistoryResp, error)
	// 获取用户帝王套汇总
	GetEmperorSetSummary(context.Context, *GetEmperorSetSummaryReq) (*GetEmperorSetSummaryResp, error)
	// 测试接口 重置用户信息
	TestResetUserInfo(context.Context, *TestResetUserInfoReq) (*TestResetUserInfoResp, error)
	// 测试接口 获取用户套组当前抽奖信息
	TestGetUserSetLotteryInfo(context.Context, *TestGetUserSetLotteryInfoReq) (*TestGetUserSetLotteryInfoResp, error)
}

func RegisterPresentSetServer(s *grpc.Server, srv PresentSetServer) {
	s.RegisterService(&_PresentSet_serviceDesc, srv)
}

func _PresentSet_CreatePresentSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePresentSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).CreatePresentSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/CreatePresentSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).CreatePresentSet(ctx, req.(*CreatePresentSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_GetPresentSetInfoNoCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentSetInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).GetPresentSetInfoNoCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/GetPresentSetInfoNoCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).GetPresentSetInfoNoCache(ctx, req.(*GetPresentSetInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_GetPresentSetListNoCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentSetListNoCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).GetPresentSetListNoCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/GetPresentSetListNoCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).GetPresentSetListNoCache(ctx, req.(*GetPresentSetListNoCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_GetPresentSetSimpleListNoCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentSetSimpleListNoCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).GetPresentSetSimpleListNoCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/GetPresentSetSimpleListNoCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).GetPresentSetSimpleListNoCache(ctx, req.(*GetPresentSetSimpleListNoCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_SetPresentsToSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPresentsToSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).SetPresentsToSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/SetPresentsToSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).SetPresentsToSet(ctx, req.(*SetPresentsToSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_DeletePresentSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePresentSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).DeletePresentSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/DeletePresentSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).DeletePresentSet(ctx, req.(*DeletePresentSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_GetAwardsToSetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAwardsToSetListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).GetAwardsToSetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/GetAwardsToSetList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).GetAwardsToSetList(ctx, req.(*GetAwardsToSetListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_SetAwardsToSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAwardsToSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).SetAwardsToSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/SetAwardsToSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).SetAwardsToSet(ctx, req.(*SetAwardsToSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_DeleteAwardsToSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAwardsToSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).DeleteAwardsToSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/DeleteAwardsToSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).DeleteAwardsToSet(ctx, req.(*DeleteAwardsToSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_CheckPresentInActiveSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPresentInActiveSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).CheckPresentInActiveSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/CheckPresentInActiveSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).CheckPresentInActiveSet(ctx, req.(*CheckPresentInActiveSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_GetSetPresentConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSetPresentConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).GetSetPresentConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/GetSetPresentConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).GetSetPresentConfigList(ctx, req.(*GetSetPresentConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_GetPresentSetInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentSetInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).GetPresentSetInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/GetPresentSetInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).GetPresentSetInfo(ctx, req.(*GetPresentSetInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_GetUserSetsInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSetsInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).GetUserSetsInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/GetUserSetsInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).GetUserSetsInfo(ctx, req.(*GetUserSetsInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_GetPresentSetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentSetListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).GetPresentSetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/GetPresentSetList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).GetPresentSetList(ctx, req.(*GetPresentSetListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_UserSentSetPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserSentSetPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).UserSentSetPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/UserSentSetPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).UserSentSetPresent(ctx, req.(*UserSentSetPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_CheckPresentInActiveSetWithCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPresentInActiveSetWithCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).CheckPresentInActiveSetWithCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/CheckPresentInActiveSetWithCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).CheckPresentInActiveSetWithCache(ctx, req.(*CheckPresentInActiveSetWithCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_CheckPresentValid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPresentValidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).CheckPresentValid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/CheckPresentValid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).CheckPresentValid(ctx, req.(*CheckPresentValidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_CreateEmperorSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEmperorSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).CreateEmperorSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/CreateEmperorSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).CreateEmperorSet(ctx, req.(*CreateEmperorSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_GetEmperorSetListNoCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEmperorSetListNoCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).GetEmperorSetListNoCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/GetEmperorSetListNoCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).GetEmperorSetListNoCache(ctx, req.(*GetEmperorSetListNoCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_GetEmperorSetInfoNoCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEmperorSetInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).GetEmperorSetInfoNoCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/GetEmperorSetInfoNoCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).GetEmperorSetInfoNoCache(ctx, req.(*GetEmperorSetInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_DeleteEmperorSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEmperorSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).DeleteEmperorSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/DeleteEmperorSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).DeleteEmperorSet(ctx, req.(*DeleteEmperorSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_GetEmperorSetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEmperorSetListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).GetEmperorSetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/GetEmperorSetList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).GetEmperorSetList(ctx, req.(*GetEmperorSetListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_GetEmperorSetInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEmperorSetInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).GetEmperorSetInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/GetEmperorSetInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).GetEmperorSetInfo(ctx, req.(*GetEmperorSetInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_CheckPresentInActiveEmperorSetWithCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPresentInActiveEmperorSetWithCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).CheckPresentInActiveEmperorSetWithCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/CheckPresentInActiveEmperorSetWithCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).CheckPresentInActiveEmperorSetWithCache(ctx, req.(*CheckPresentInActiveEmperorSetWithCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_ValidUserCanSendSetByEmperorSetId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidUserCanSendSetByEmperorSetIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).ValidUserCanSendSetByEmperorSetId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/ValidUserCanSendSetByEmperorSetId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).ValidUserCanSendSetByEmperorSetId(ctx, req.(*ValidUserCanSendSetByEmperorSetIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_UserSentEmperorSetPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserSentEmperorSetPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).UserSentEmperorSetPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/UserSentEmperorSetPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).UserSentEmperorSetPresent(ctx, req.(*UserSentEmperorSetPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_GetUserEmperorSetHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserEmperorSetHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).GetUserEmperorSetHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/GetUserEmperorSetHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).GetUserEmperorSetHistory(ctx, req.(*GetUserEmperorSetHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_GetEmperorSetSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEmperorSetSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).GetEmperorSetSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/GetEmperorSetSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).GetEmperorSetSummary(ctx, req.(*GetEmperorSetSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_TestResetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestResetUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).TestResetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/TestResetUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).TestResetUserInfo(ctx, req.(*TestResetUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentSet_TestGetUserSetLotteryInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestGetUserSetLotteryInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentSetServer).TestGetUserSetLotteryInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_set.PresentSet/TestGetUserSetLotteryInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentSetServer).TestGetUserSetLotteryInfo(ctx, req.(*TestGetUserSetLotteryInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PresentSet_serviceDesc = grpc.ServiceDesc{
	ServiceName: "present_set.PresentSet",
	HandlerType: (*PresentSetServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePresentSet",
			Handler:    _PresentSet_CreatePresentSet_Handler,
		},
		{
			MethodName: "GetPresentSetInfoNoCache",
			Handler:    _PresentSet_GetPresentSetInfoNoCache_Handler,
		},
		{
			MethodName: "GetPresentSetListNoCache",
			Handler:    _PresentSet_GetPresentSetListNoCache_Handler,
		},
		{
			MethodName: "GetPresentSetSimpleListNoCache",
			Handler:    _PresentSet_GetPresentSetSimpleListNoCache_Handler,
		},
		{
			MethodName: "SetPresentsToSet",
			Handler:    _PresentSet_SetPresentsToSet_Handler,
		},
		{
			MethodName: "DeletePresentSet",
			Handler:    _PresentSet_DeletePresentSet_Handler,
		},
		{
			MethodName: "GetAwardsToSetList",
			Handler:    _PresentSet_GetAwardsToSetList_Handler,
		},
		{
			MethodName: "SetAwardsToSet",
			Handler:    _PresentSet_SetAwardsToSet_Handler,
		},
		{
			MethodName: "DeleteAwardsToSet",
			Handler:    _PresentSet_DeleteAwardsToSet_Handler,
		},
		{
			MethodName: "CheckPresentInActiveSet",
			Handler:    _PresentSet_CheckPresentInActiveSet_Handler,
		},
		{
			MethodName: "GetSetPresentConfigList",
			Handler:    _PresentSet_GetSetPresentConfigList_Handler,
		},
		{
			MethodName: "GetPresentSetInfo",
			Handler:    _PresentSet_GetPresentSetInfo_Handler,
		},
		{
			MethodName: "GetUserSetsInfo",
			Handler:    _PresentSet_GetUserSetsInfo_Handler,
		},
		{
			MethodName: "GetPresentSetList",
			Handler:    _PresentSet_GetPresentSetList_Handler,
		},
		{
			MethodName: "UserSentSetPresent",
			Handler:    _PresentSet_UserSentSetPresent_Handler,
		},
		{
			MethodName: "CheckPresentInActiveSetWithCache",
			Handler:    _PresentSet_CheckPresentInActiveSetWithCache_Handler,
		},
		{
			MethodName: "CheckPresentValid",
			Handler:    _PresentSet_CheckPresentValid_Handler,
		},
		{
			MethodName: "CreateEmperorSet",
			Handler:    _PresentSet_CreateEmperorSet_Handler,
		},
		{
			MethodName: "GetEmperorSetListNoCache",
			Handler:    _PresentSet_GetEmperorSetListNoCache_Handler,
		},
		{
			MethodName: "GetEmperorSetInfoNoCache",
			Handler:    _PresentSet_GetEmperorSetInfoNoCache_Handler,
		},
		{
			MethodName: "DeleteEmperorSet",
			Handler:    _PresentSet_DeleteEmperorSet_Handler,
		},
		{
			MethodName: "GetEmperorSetList",
			Handler:    _PresentSet_GetEmperorSetList_Handler,
		},
		{
			MethodName: "GetEmperorSetInfo",
			Handler:    _PresentSet_GetEmperorSetInfo_Handler,
		},
		{
			MethodName: "CheckPresentInActiveEmperorSetWithCache",
			Handler:    _PresentSet_CheckPresentInActiveEmperorSetWithCache_Handler,
		},
		{
			MethodName: "ValidUserCanSendSetByEmperorSetId",
			Handler:    _PresentSet_ValidUserCanSendSetByEmperorSetId_Handler,
		},
		{
			MethodName: "UserSentEmperorSetPresent",
			Handler:    _PresentSet_UserSentEmperorSetPresent_Handler,
		},
		{
			MethodName: "GetUserEmperorSetHistory",
			Handler:    _PresentSet_GetUserEmperorSetHistory_Handler,
		},
		{
			MethodName: "GetEmperorSetSummary",
			Handler:    _PresentSet_GetEmperorSetSummary_Handler,
		},
		{
			MethodName: "TestResetUserInfo",
			Handler:    _PresentSet_TestResetUserInfo_Handler,
		},
		{
			MethodName: "TestGetUserSetLotteryInfo",
			Handler:    _PresentSet_TestGetUserSetLotteryInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "present-set/present-set.proto",
}

func init() {
	proto.RegisterFile("present-set/present-set.proto", fileDescriptor_present_set_5d3f6fb74367b8ed)
}

var fileDescriptor_present_set_5d3f6fb74367b8ed = []byte{
	// 4199 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x3b, 0x5d, 0x6f, 0x1b, 0x49,
	0x72, 0x26, 0x69, 0x49, 0x54, 0x49, 0x94, 0xa8, 0xd6, 0x87, 0x29, 0xda, 0xb2, 0xe4, 0xb1, 0xd7,
	0x2b, 0x2b, 0x6b, 0x79, 0xcf, 0x17, 0xe3, 0x2e, 0x77, 0xd8, 0x05, 0x64, 0x89, 0xd6, 0x72, 0x57,
	0x96, 0x85, 0x21, 0x65, 0xc3, 0x87, 0x00, 0x93, 0x11, 0xa7, 0x25, 0xcd, 0x69, 0x38, 0x33, 0x37,
	0x3d, 0xb4, 0x6c, 0x04, 0x17, 0x04, 0x08, 0x10, 0x60, 0x81, 0x4b, 0x80, 0x20, 0xef, 0xf7, 0x16,
	0x04, 0xf7, 0x12, 0x20, 0x2f, 0x79, 0xca, 0x63, 0x80, 0x00, 0xf7, 0x5b, 0xf2, 0x0f, 0xf2, 0x12,
	0x74, 0x75, 0xcf, 0x4c, 0xcf, 0x97, 0x28, 0xfb, 0xfc, 0x92, 0x37, 0x76, 0x75, 0x75, 0x75, 0x75,
	0x75, 0x7d, 0x75, 0xd5, 0x10, 0xd6, 0xfc, 0x80, 0x32, 0xea, 0x86, 0x8f, 0x19, 0x0d, 0x9f, 0x28,
	0xbf, 0xb7, 0xfd, 0xc0, 0x0b, 0x3d, 0x32, 0x23, 0x41, 0x06, 0xa3, 0x61, 0xfb, 0x7e, 0x40, 0xdf,
	0x51, 0x77, 0x44, 0x1f, 0x9b, 0xbe, 0xfd, 0xf8, 0xcc, 0x7b, 0x92, 0x1e, 0x8a, 0x15, 0xda, 0x8f,
	0xd3, 0xd0, 0x3c, 0x12, 0x8b, 0x7a, 0x34, 0xdc, 0xf5, 0xdc, 0x53, 0xfb, 0x8c, 0x2c, 0xc3, 0x24,
	0xa3, 0xa1, 0x61, 0x5b, 0xad, 0xca, 0x46, 0x65, 0xb3, 0xa1, 0x4f, 0x30, 0x1a, 0x76, 0x2d, 0xb2,
	0x0a, 0x75, 0x0e, 0x76, 0xcd, 0x21, 0x6d, 0x55, 0x37, 0x2a, 0x9b, 0xd3, 0xfa, 0x14, 0xa3, 0xe1,
	0xa1, 0x39, 0xa4, 0xe4, 0x3b, 0x68, 0x98, 0x83, 0xd0, 0x7e, 0x47, 0x0d, 0x16, 0x9a, 0xe1, 0x88,
	0xb5, 0x6a, 0x1b, 0x95, 0xcd, 0xb9, 0xa7, 0xf7, 0xb7, 0x15, 0x86, 0xb6, 0x93, 0x7d, 0x76, 0x10,
	0xb7, 0x87, 0xa8, 0xfa, 0xac, 0xa9, 0x8c, 0xc8, 0x3a, 0xcc, 0x0c, 0x02, 0x6a, 0x86, 0xd4, 0x08,
	0xed, 0x21, 0x6d, 0xdd, 0x44, 0x06, 0x40, 0x80, 0xfa, 0xf6, 0x90, 0x12, 0x02, 0x37, 0x03, 0xd3,
	0xbd, 0x68, 0x4d, 0x6c, 0x54, 0x36, 0x2b, 0x3a, 0xfe, 0x26, 0x6b, 0x00, 0xe6, 0xa5, 0x19, 0x58,
	0x46, 0x48, 0xdf, 0x87, 0xad, 0x19, 0xe4, 0x6d, 0x1a, 0x21, 0x7d, 0xfa, 0x3e, 0x24, 0xcf, 0x60,
	0xd2, 0xa7, 0x81, 0xed, 0x59, 0xad, 0x49, 0x64, 0x6b, 0xad, 0x84, 0xad, 0x23, 0x44, 0xd2, 0x25,
	0x32, 0xa7, 0xca, 0x42, 0x33, 0x08, 0x05, 0x27, 0x53, 0xc8, 0xc9, 0x34, 0x42, 0x90, 0x91, 0x55,
	0xa8, 0x53, 0xd7, 0x12, 0x93, 0x75, 0x9c, 0x9c, 0xa2, 0xae, 0x85, 0x53, 0x5f, 0xc0, 0x1c, 0x75,
	0xcd, 0x13, 0x87, 0x1a, 0x7e, 0x40, 0xdf, 0xd9, 0xf4, 0xb2, 0x35, 0xbb, 0x51, 0xd9, 0xac, 0xeb,
	0x0d, 0x01, 0x3d, 0x12, 0x40, 0xd2, 0x81, 0x59, 0x39, 0x6f, 0x84, 0x1f, 0x7c, 0xda, 0xba, 0x85,
	0xdc, 0x69, 0x65, 0xdc, 0x09, 0xd4, 0xfe, 0x07, 0x9f, 0xea, 0x33, 0x7e, 0x32, 0x20, 0x0f, 0x61,
	0x3e, 0x22, 0x13, 0x50, 0x66, 0x8c, 0x02, 0xa7, 0xd5, 0x40, 0x11, 0x34, 0x24, 0x58, 0xa7, 0xec,
	0x38, 0x70, 0xb2, 0x78, 0x43, 0xeb, 0x59, 0x6b, 0x35, 0x8b, 0xf7, 0xd2, 0x7a, 0x46, 0xbe, 0x84,
	0x79, 0xc9, 0x3d, 0xde, 0x8c, 0x1d, 0x7e, 0x68, 0xcd, 0x21, 0xfb, 0xf2, 0x50, 0x3b, 0x12, 0x4a,
	0xfe, 0x02, 0x56, 0x23, 0x0c, 0x83, 0xba, 0x61, 0x60, 0xba, 0x03, 0x1a, 0xb3, 0x30, 0x8f, 0xa4,
	0x57, 0x22, 0x84, 0x8e, 0x9c, 0x97, 0xbc, 0xfc, 0x00, 0x24, 0x5e, 0xea, 0xd8, 0xee, 0x85, 0x10,
	0x40, 0xab, 0xe0, 0x7a, 0xa2, 0xdd, 0x0e, 0x6c, 0xf7, 0x02, 0xcf, 0xde, 0x34, 0x33, 0x10, 0xb2,
	0x05, 0x0b, 0x31, 0xb1, 0x5f, 0x8f, 0x86, 0x3e, 0xee, 0xdf, 0xc4, 0xfd, 0xe7, 0xa3, 0x89, 0xef,
	0x47, 0x43, 0x9f, 0x6f, 0xfc, 0x2d, 0xca, 0x1c, 0xa9, 0x3b, 0x36, 0x0b, 0x5b, 0x0b, 0x1b, 0xb5,
	0xcd, 0x99, 0xa7, 0xb7, 0x53, 0x5b, 0x0a, 0x61, 0xf3, 0x61, 0x37, 0xa4, 0x43, 0x3d, 0xb2, 0xaa,
	0x03, 0x9b, 0x85, 0xe4, 0x5b, 0x98, 0x17, 0xaa, 0x66, 0x87, 0x74, 0x28, 0x48, 0x10, 0x24, 0xb1,
	0x92, 0xe6, 0x9a, 0xe3, 0xe0, 0xea, 0x86, 0x19, 0xfd, 0xc4, 0xf5, 0xeb, 0x30, 0x33, 0xf2, 0xad,
	0x58, 0xbf, 0x17, 0x85, 0x7e, 0x0b, 0x10, 0xea, 0x0e, 0x37, 0x80, 0x73, 0xd3, 0x3d, 0x93, 0x08,
	0x4b, 0xd2, 0x00, 0x10, 0x84, 0x08, 0x9b, 0xd0, 0x3c, 0x09, 0xa8, 0x79, 0x61, 0xbb, 0x67, 0x86,
	0x4b, 0x2f, 0x19, 0xb7, 0xd3, 0x65, 0xc4, 0x9a, 0x8b, 0xe0, 0x87, 0xf4, 0x92, 0x75, 0x2d, 0xf2,
	0x1c, 0xe6, 0xbc, 0xc0, 0x3e, 0xb3, 0x5d, 0x43, 0xb2, 0xd6, 0x5a, 0xd9, 0xa8, 0x8c, 0x3b, 0x6d,
	0x43, 0x2c, 0x91, 0x20, 0xf2, 0x15, 0x10, 0xa9, 0x0c, 0x74, 0xe8, 0xd3, 0xc0, 0x0b, 0xf8, 0x9a,
	0x56, 0x1b, 0xf5, 0xa1, 0x29, 0x66, 0x3a, 0x62, 0xa2, 0x47, 0x43, 0xf2, 0x00, 0xe6, 0x14, 0x34,
	0xce, 0xd9, 0x6d, 0xe4, 0x6c, 0x96, 0xc6, 0x38, 0x5d, 0x8b, 0xfb, 0x17, 0x9b, 0x19, 0x16, 0x75,
	0x5a, 0x77, 0x90, 0xce, 0x84, 0xcd, 0xf6, 0xa8, 0xa3, 0xfd, 0xa1, 0x06, 0x73, 0x69, 0x66, 0xca,
	0x3c, 0xd1, 0x1a, 0x40, 0x74, 0x02, 0xdb, 0x42, 0x5f, 0xd4, 0xd0, 0xa7, 0x25, 0xa4, 0x6b, 0x91,
	0x7b, 0xc9, 0x1d, 0xa3, 0xb3, 0xaa, 0xa1, 0x2a, 0x44, 0xd7, 0x88, 0x0e, 0x4b, 0x41, 0xb1, 0x07,
	0x9e, 0x8b, 0x7e, 0x26, 0x41, 0xe9, 0x0e, 0x3c, 0x97, 0x7c, 0x0b, 0x8d, 0x58, 0x53, 0xe8, 0x3b,
	0xea, 0xa0, 0xc7, 0x99, 0x7b, 0xba, 0x5a, 0x64, 0x9e, 0x07, 0x1c, 0x41, 0x8f, 0x48, 0xe2, 0x88,
	0xdc, 0x86, 0x69, 0x9b, 0x19, 0x42, 0x9a, 0xe8, 0x78, 0xea, 0x7a, 0xdd, 0x66, 0xaf, 0x70, 0x4c,
	0x36, 0x60, 0xc6, 0x0f, 0xbc, 0x13, 0xf3, 0xc4, 0x76, 0xb8, 0x7d, 0x4d, 0xa1, 0x33, 0x53, 0x41,
	0xb1, 0x9f, 0xab, 0x2b, 0x7e, 0xee, 0x0e, 0x4c, 0x87, 0xe7, 0x01, 0x65, 0xe7, 0x9e, 0x63, 0xb5,
	0xa6, 0xc5, 0xb1, 0x63, 0x00, 0xb9, 0x0b, 0x70, 0x36, 0x32, 0x03, 0xd3, 0x0d, 0x29, 0xb5, 0x5a,
	0xb0, 0x51, 0xd9, 0x9c, 0xd0, 0x15, 0x08, 0xf7, 0x4a, 0x27, 0xe6, 0xe0, 0xe2, 0x2c, 0xf0, 0x46,
	0xae, 0x85, 0x36, 0x22, 0x3c, 0x65, 0x23, 0x81, 0x72, 0x0b, 0xb9, 0x9f, 0x9c, 0xdb, 0x0f, 0xec,
	0x01, 0x45, 0xdf, 0xd5, 0x88, 0x0f, 0x77, 0xc4, 0x61, 0xda, 0xdf, 0x56, 0x60, 0x96, 0x3b, 0xf2,
	0x48, 0xb7, 0x3f, 0x21, 0x66, 0x14, 0x58, 0x52, 0xed, 0x23, 0x2c, 0x49, 0xfb, 0xaf, 0x2a, 0x4c,
	0x27, 0xfb, 0x7f, 0x13, 0x87, 0x00, 0xee, 0x48, 0x2a, 0x78, 0x55, 0x77, 0xb7, 0x75, 0x11, 0xf3,
	0x76, 0x7c, 0x7b, 0xdf, 0x8b, 0x07, 0x18, 0x17, 0xb8, 0x27, 0x91, 0x21, 0x82, 0xbb, 0x90, 0x55,
	0xa8, 0x4b, 0x66, 0xac, 0x88, 0x4f, 0xb1, 0x9b, 0xc5, 0x0d, 0x52, 0x4c, 0x0d, 0xbc, 0x91, 0x1b,
	0xa2, 0x32, 0x35, 0x74, 0xb1, 0xd9, 0x2e, 0x87, 0x70, 0x5d, 0x92, 0xc1, 0x4f, 0x60, 0x88, 0x98,
	0x35, 0x23, 0x60, 0x02, 0x45, 0x0d, 0x5a, 0x0d, 0x79, 0x99, 0xb7, 0x41, 0xec, 0x6f, 0xf8, 0xf6,
	0x00, 0xf5, 0x63, 0x5a, 0x17, 0x3c, 0x1c, 0xd9, 0x83, 0x64, 0x53, 0x71, 0x05, 0x53, 0xca, 0xa6,
	0x78, 0x01, 0xdc, 0x0b, 0x28, 0x5c, 0x19, 0xb6, 0x7b, 0xea, 0xa1, 0xaa, 0x4c, 0xeb, 0x73, 0x09,
	0x6b, 0x5d, 0xf7, 0xd4, 0x4b, 0x82, 0x23, 0x5e, 0xc2, 0xb4, 0x12, 0x1c, 0xf9, 0x35, 0x68, 0xff,
	0x3b, 0x03, 0xcd, 0xc4, 0x82, 0xff, 0x3f, 0x64, 0x00, 0x19, 0x17, 0x3a, 0x91, 0x73, 0xa1, 0xab,
	0x50, 0xe7, 0x46, 0x8d, 0x2a, 0x2e, 0x04, 0x3b, 0xc5, 0xc7, 0x5c, 0xb9, 0x1f, 0xc1, 0x82, 0xcd,
	0x8c, 0xd3, 0x11, 0xa3, 0xc6, 0x88, 0xd1, 0x40, 0xc8, 0x6d, 0x4a, 0x44, 0x37, 0x9b, 0xbd, 0x18,
	0x31, 0x7a, 0xcc, 0x68, 0x80, 0x72, 0x7b, 0x01, 0x93, 0xf4, 0xf4, 0x94, 0x0e, 0x42, 0x94, 0xeb,
	0xcc, 0xd3, 0xed, 0xd4, 0x51, 0xb2, 0x22, 0x53, 0x00, 0x1d, 0x5c, 0xa5, 0xcb, 0xd5, 0xdc, 0x83,
	0xb2, 0x73, 0xef, 0xd2, 0x88, 0x57, 0x9f, 0x53, 0xe7, 0x14, 0xcd, 0xb3, 0xae, 0x37, 0xf9, 0x4c,
	0x24, 0x15, 0x0e, 0x8f, 0x35, 0x65, 0x46, 0x31, 0xfb, 0x87, 0x30, 0x8f, 0x14, 0x04, 0x41, 0x83,
	0xba, 0x56, 0x94, 0x4f, 0x70, 0xb0, 0xd8, 0xae, 0xe3, 0x66, 0x13, 0x96, 0xf9, 0xab, 0x12, 0x96,
	0x66, 0x3a, 0x61, 0x79, 0xc0, 0x5d, 0x83, 0xeb, 0xd2, 0x20, 0x0e, 0xdf, 0x4b, 0x28, 0xb7, 0x59,
	0x01, 0x95, 0x41, 0xfb, 0x15, 0x34, 0x25, 0x16, 0x46, 0x59, 0xb4, 0xb4, 0x65, 0xbc, 0xe6, 0x2f,
	0x4a, 0x64, 0xf3, 0x1c, 0xd1, 0x79, 0xec, 0x45, 0x83, 0x93, 0x9b, 0x44, 0x63, 0x7e, 0x30, 0x95,
	0x20, 0xdf, 0x77, 0x25, 0x72, 0x49, 0x11, 0x22, 0xdf, 0xf8, 0x6b, 0x58, 0x92, 0x41, 0x28, 0x15,
	0xf9, 0x30, 0x7d, 0xa9, 0xeb, 0x32, 0x40, 0x3d, 0x57, 0x82, 0x5f, 0x61, 0x90, 0x6c, 0x17, 0x06,
	0xc9, 0xaf, 0x61, 0x29, 0xc6, 0xe4, 0xa2, 0x61, 0x86, 0x63, 0x0f, 0xed, 0x10, 0x03, 0xd7, 0x84,
	0x4e, 0xa2, 0x39, 0x2e, 0x26, 0x76, 0xc0, 0x67, 0x64, 0xda, 0x96, 0xa4, 0x10, 0x77, 0xd1, 0x6b,
	0x69, 0x25, 0x22, 0x28, 0xcd, 0x24, 0xbe, 0x86, 0x25, 0x39, 0x64, 0x46, 0xe8, 0x85, 0xa6, 0x23,
	0x6d, 0x7d, 0x1d, 0xd9, 0x24, 0xd1, 0x5c, 0x9f, 0x4f, 0x09, 0x9b, 0x4f, 0xe2, 0xe6, 0x86, 0x12,
	0x37, 0xc9, 0x4f, 0x60, 0x99, 0xf1, 0x8b, 0x55, 0x23, 0x2f, 0x26, 0xc2, 0xf7, 0x50, 0x96, 0x84,
	0x4f, 0x26, 0xfc, 0x60, 0x46, 0xfc, 0x0b, 0x68, 0xe7, 0x96, 0x9c, 0x06, 0xe6, 0x90, 0xe2, 0x1d,
	0x68, 0x22, 0x75, 0x4b, 0xaf, 0x7b, 0xc1, 0xa7, 0x8f, 0x03, 0xa7, 0xfd, 0xc7, 0x9a, 0xea, 0x30,
	0x3a, 0xb1, 0x92, 0x9b, 0xae, 0x15, 0x78, 0xb6, 0x15, 0x69, 0x29, 0x27, 0x54, 0x41, 0x42, 0x4d,
	0x39, 0x23, 0x50, 0xf9, 0x7d, 0xe6, 0xb1, 0x79, 0x32, 0x5a, 0x2d, 0xc0, 0xe6, 0xf9, 0xe8, 0x03,
	0x98, 0xb3, 0x3d, 0xa6, 0xd2, 0x15, 0x01, 0x7d, 0xd6, 0xf6, 0x58, 0x42, 0x33, 0x8d, 0xc5, 0xe9,
	0xdd, 0xcc, 0x60, 0x71, 0x5a, 0x0f, 0x61, 0xde, 0x66, 0x89, 0x16, 0x9d, 0x78, 0xef, 0xd1, 0x7f,
	0xd4, 0xf5, 0x86, 0xcd, 0x22, 0x05, 0x7a, 0xee, 0xbd, 0x27, 0xbf, 0x84, 0x76, 0x24, 0x1b, 0x9e,
	0x19, 0x73, 0x5c, 0xe6, 0x8d, 0x82, 0x01, 0x55, 0x9c, 0xca, 0x2d, 0x89, 0xf1, 0x5a, 0x20, 0xf4,
	0x70, 0x9e, 0xb3, 0x52, 0xbe, 0x98, 0xb3, 0x35, 0x55, 0xbe, 0x98, 0x73, 0xf8, 0x0c, 0xa2, 0x29,
	0xe3, 0xd4, 0xf1, 0x2e, 0xd5, 0x6d, 0x85, 0x7f, 0x5f, 0x92, 0xd3, 0x2f, 0x1c, 0xef, 0x32, 0xd9,
	0xb3, 0x64, 0x19, 0xdf, 0x70, 0xba, 0x64, 0xd9, 0x4b, 0xeb, 0x99, 0xf6, 0x9f, 0x55, 0x58, 0x2e,
	0xd4, 0xd5, 0x4f, 0x4c, 0xbd, 0x22, 0xf7, 0x35, 0xa9, 0xb8, 0xaf, 0xcf, 0x93, 0x8e, 0xe5, 0xd2,
	0x92, 0x89, 0x7c, 0x5a, 0x52, 0xe2, 0x6b, 0xa7, 0x4a, 0x7c, 0xed, 0x3d, 0x98, 0x95, 0xea, 0x72,
	0x42, 0x79, 0x92, 0x26, 0x5e, 0x71, 0x33, 0x02, 0xf6, 0x9c, 0x83, 0xf8, 0x71, 0x15, 0xaf, 0x2b,
	0x53, 0x2e, 0x1a, 0x79, 0x5c, 0xed, 0x1d, 0x2c, 0xee, 0x62, 0x60, 0x4a, 0x22, 0x9b, 0x4e, 0x7f,
	0x43, 0xbe, 0x05, 0xf5, 0x25, 0x8e, 0x02, 0x9c, 0x29, 0x7d, 0x75, 0x8a, 0xf8, 0xa1, 0x47, 0x62,
	0xe5, 0x69, 0xf4, 0x3a, 0xcc, 0xd8, 0xcc, 0x78, 0x67, 0x3a, 0x36, 0x8f, 0x69, 0x28, 0xb0, 0xba,
	0x0e, 0x36, 0x7b, 0x2d, 0x21, 0xda, 0x63, 0x58, 0xca, 0xef, 0xcb, 0xfc, 0x92, 0x4b, 0xe3, 0xe8,
	0xfb, 0xf1, 0xed, 0xf2, 0x1c, 0xdc, 0x3d, 0xf5, 0x38, 0x9f, 0x25, 0xe8, 0x6f, 0x60, 0xb9, 0x00,
	0x9d, 0xf9, 0x7f, 0xea, 0xb9, 0xb4, 0xff, 0xae, 0xc0, 0xed, 0x14, 0x65, 0xee, 0x08, 0x0f, 0xbd,
	0x5d, 0x73, 0x70, 0x4e, 0x39, 0x3f, 0xb9, 0x24, 0xa2, 0xf2, 0xa9, 0x49, 0xc4, 0x0a, 0x4c, 0x7a,
	0xa7, 0xa7, 0x9c, 0x49, 0xa1, 0xa2, 0x72, 0x44, 0x96, 0x60, 0x42, 0xb8, 0x77, 0x91, 0xc6, 0x89,
	0x81, 0x22, 0x87, 0xa9, 0xb2, 0x74, 0xa7, 0x9e, 0x4a, 0x77, 0xb4, 0xdf, 0xc2, 0x9d, 0xf2, 0x83,
	0x30, 0x9f, 0xec, 0x43, 0x53, 0xe1, 0x59, 0xc4, 0x89, 0x0a, 0xc6, 0x89, 0x31, 0xe2, 0x9a, 0xf3,
	0x53, 0x34, 0x39, 0xc3, 0x18, 0x1c, 0xe4, 0x39, 0xc4, 0x40, 0xfb, 0x06, 0xee, 0xa5, 0xb6, 0xef,
	0xd9, 0x43, 0xdf, 0xa1, 0x19, 0x69, 0xb6, 0x60, 0xea, 0x82, 0x7e, 0xb8, 0xf4, 0x02, 0x4b, 0xda,
	0x53, 0x34, 0xd4, 0x86, 0xa0, 0x8d, 0x5b, 0xfe, 0x19, 0xcf, 0xa0, 0x7d, 0x05, 0x8b, 0x7b, 0xd4,
	0xa1, 0x59, 0x2b, 0x29, 0xd1, 0xbe, 0x15, 0x58, 0xca, 0x63, 0x33, 0x5f, 0xfb, 0x87, 0x0a, 0x2c,
	0x26, 0x3e, 0x8a, 0xf5, 0xbd, 0x2b, 0xc9, 0xe4, 0x1e, 0xfa, 0xd5, 0x8f, 0x7c, 0xe8, 0x8f, 0xb5,
	0xc1, 0x15, 0x58, 0xca, 0xb3, 0xc3, 0x7c, 0xed, 0x1b, 0x20, 0xfb, 0x34, 0xe4, 0x69, 0x64, 0x8f,
	0x86, 0x2c, 0x32, 0xb5, 0x26, 0xd4, 0x46, 0x31, 0x8b, 0xfc, 0xa7, 0xc2, 0x77, 0x55, 0x3d, 0xfe,
	0x3f, 0xd7, 0x60, 0x46, 0x2e, 0xc6, 0x34, 0xb4, 0xe4, 0x78, 0x92, 0x5e, 0x35, 0xa1, 0xf7, 0x10,
	0xe6, 0x1d, 0x93, 0x85, 0x86, 0xe2, 0x9e, 0x85, 0x92, 0x37, 0x38, 0xf8, 0x28, 0x76, 0xd1, 0x3f,
	0x64, 0x04, 0x73, 0x13, 0x05, 0xb3, 0x99, 0x12, 0x8c, 0xc2, 0x00, 0xfe, 0x8e, 0x56, 0xf3, 0xc3,
	0xa4, 0xa4, 0x74, 0x1f, 0x1a, 0x03, 0xcf, 0x71, 0xb8, 0x83, 0x14, 0x6a, 0x2a, 0xbd, 0xb2, 0x04,
	0x62, 0xf2, 0x42, 0x1e, 0x41, 0x33, 0x42, 0xf2, 0x03, 0xef, 0x2c, 0xa0, 0x8c, 0x61, 0x80, 0x68,
	0xe8, 0xf3, 0x12, 0x7e, 0x24, 0xc1, 0x64, 0x0b, 0x16, 0x02, 0x33, 0xa0, 0xe9, 0x63, 0x08, 0xa3,
	0x9c, 0x17, 0x13, 0xf1, 0x41, 0xda, 0xbf, 0x86, 0xf9, 0x0c, 0x6f, 0x99, 0xe8, 0x54, 0x29, 0x28,
	0x0c, 0xd8, 0xcc, 0x90, 0x7b, 0x52, 0x21, 0xbd, 0xba, 0x3e, 0x63, 0xb3, 0xdd, 0x08, 0x24, 0x73,
	0x2c, 0x97, 0x5e, 0xca, 0x1b, 0x9f, 0xb0, 0xd9, 0x21, 0xbd, 0xd4, 0xde, 0x40, 0x83, 0xef, 0xf5,
	0x3c, 0xf0, 0x4c, 0x6b, 0x60, 0xb2, 0xf0, 0xda, 0xf7, 0x99, 0x61, 0xa9, 0x96, 0x61, 0x49, 0xfb,
	0x5d, 0x15, 0x16, 0x73, 0xea, 0xc2, 0x7c, 0xd2, 0x01, 0x82, 0x0f, 0x14, 0x24, 0xe9, 0x9e, 0x7a,
	0xaa, 0xf9, 0xb5, 0xca, 0xee, 0x4a, 0x9f, 0x1f, 0x25, 0x03, 0xbc, 0x9f, 0xef, 0x61, 0x11, 0xc9,
	0x9c, 0x44, 0x8c, 0xab, 0xc6, 0xd0, 0xce, 0xd1, 0x89, 0xcf, 0xa7, 0x2f, 0x8c, 0xd4, 0x21, 0xd2,
	0xda, 0x84, 0x26, 0x2a, 0x98, 0xfa, 0xf8, 0x12, 0xe7, 0x99, 0xe3, 0xf0, 0xe3, 0xe4, 0x01, 0xf6,
	0x73, 0x58, 0xc5, 0xf4, 0xd2, 0x74, 0x9c, 0x7c, 0x56, 0x2a, 0x7c, 0x11, 0xa6, 0xac, 0x3b, 0x8e,
	0x93, 0x4e, 0x4c, 0xb9, 0x91, 0x2f, 0x44, 0x75, 0x85, 0xc4, 0xc4, 0x7f, 0x11, 0xbd, 0x86, 0xed,
	0x90, 0x0e, 0x99, 0x8c, 0x3b, 0xab, 0x59, 0x53, 0x4e, 0x2a, 0x05, 0x10, 0x57, 0x0a, 0x58, 0xd6,
	0x8e, 0xab, 0x59, 0x3b, 0x96, 0x75, 0x1a, 0xf1, 0xbe, 0x94, 0x97, 0x5e, 0xb7, 0x99, 0x08, 0xaf,
	0xda, 0x12, 0x90, 0x2c, 0x3b, 0xcc, 0xe7, 0xf1, 0x54, 0xb8, 0xa8, 0x0c, 0x9f, 0x25, 0x1e, 0xed,
	0x16, 0x2c, 0x17, 0xa0, 0x33, 0x5f, 0xbb, 0xc4, 0x40, 0xab, 0x40, 0xb9, 0x9c, 0xf3, 0x84, 0xa6,
	0xaf, 0xf1, 0xfe, 0x4e, 0x02, 0x5e, 0xad, 0x38, 0xe0, 0xdd, 0x54, 0x02, 0x9e, 0xf6, 0x1e, 0x56,
	0x8a, 0x36, 0x66, 0x3e, 0xf9, 0x0e, 0x16, 0xf9, 0x16, 0xd9, 0xca, 0x8c, 0x50, 0xbc, 0x2b, 0x44,
	0xde, 0x64, 0xca, 0xe8, 0x8a, 0xc8, 0xb5, 0x92, 0x49, 0x45, 0xe4, 0x89, 0xb5, 0xbf, 0xca, 0xe4,
	0x1c, 0x31, 0x43, 0x9f, 0x2d, 0x0a, 0xfd, 0x4b, 0x05, 0x96, 0x85, 0xad, 0x44, 0xe5, 0x74, 0x3e,
	0x5b, 0xec, 0x9b, 0xc7, 0x64, 0xb9, 0x4a, 0x2e, 0xaa, 0x16, 0x85, 0x22, 0xbf, 0x2a, 0x6a, 0x3e,
	0xab, 0x50, 0xf7, 0x02, 0x8b, 0x06, 0x9c, 0x02, 0xf7, 0xb1, 0xd3, 0xfa, 0x14, 0x8e, 0xbb, 0x16,
	0x57, 0x39, 0x16, 0x3f, 0xc5, 0x85, 0xc7, 0xac, 0x33, 0xf9, 0x16, 0xd7, 0x5a, 0xb0, 0x52, 0xc4,
	0x26, 0xf3, 0xb5, 0x5f, 0x42, 0x7b, 0xf7, 0x9c, 0x0e, 0x2e, 0x62, 0x8f, 0x27, 0x13, 0x20, 0xa1,
	0x7c, 0x57, 0xfb, 0x3e, 0x6d, 0x0d, 0x6e, 0x97, 0x2e, 0x66, 0xbe, 0xb6, 0x07, 0xf7, 0x4b, 0xa6,
	0xdf, 0xd8, 0xe1, 0x79, 0x9c, 0x53, 0x8c, 0xd9, 0x64, 0x17, 0x1e, 0x8c, 0xa7, 0xc2, 0x7c, 0x69,
	0x73, 0x22, 0x63, 0x43, 0x2a, 0x68, 0x73, 0x02, 0x59, 0xdb, 0x84, 0xa5, 0x3e, 0xc5, 0xdb, 0x17,
	0x7e, 0xb1, 0x34, 0x84, 0x72, 0xc3, 0x2a, 0xc0, 0x64, 0xbe, 0x76, 0x07, 0xda, 0xfb, 0x54, 0x11,
	0x9f, 0x50, 0x89, 0x48, 0xd7, 0xfe, 0xa9, 0x0a, 0xab, 0x22, 0xe5, 0xe9, 0xa9, 0xf1, 0x5f, 0xd6,
	0xbe, 0x6e, 0xc1, 0x14, 0xea, 0x7d, 0xbc, 0xd5, 0x24, 0x1f, 0x8a, 0xb7, 0x8d, 0x62, 0x79, 0xf8,
	0x3b, 0x55, 0x6a, 0xaa, 0xa5, 0x4b, 0x4d, 0x4b, 0x30, 0x21, 0x1e, 0x2a, 0xd2, 0xf2, 0x70, 0x50,
	0x58, 0x09, 0xcc, 0xbe, 0x43, 0x26, 0xc7, 0xbd, 0x43, 0xa6, 0x32, 0xef, 0x10, 0xa5, 0x32, 0x50,
	0x57, 0x2b, 0x03, 0x4d, 0xa8, 0x85, 0xe6, 0x99, 0x7c, 0xb6, 0xf0, 0x9f, 0x78, 0x38, 0x66, 0x84,
	0x94, 0x85, 0xb2, 0x02, 0x35, 0x69, 0x33, 0x2e, 0x42, 0xed, 0x1c, 0x33, 0xf3, 0x62, 0x89, 0x31,
	0x9f, 0x74, 0x33, 0x49, 0x83, 0xb0, 0xc0, 0x87, 0x69, 0x7f, 0x50, 0x26, 0xd2, 0x54, 0xca, 0xa0,
	0xed, 0xc3, 0x92, 0xaa, 0x23, 0xe8, 0x87, 0xc7, 0xab, 0x56, 0x3e, 0xe1, 0xe1, 0xb7, 0x5f, 0x40,
	0x88, 0xf9, 0xda, 0xbf, 0xd6, 0x80, 0x1c, 0x78, 0x61, 0x48, 0x83, 0x0f, 0x1f, 0x97, 0x1c, 0xa4,
	0x9e, 0xa9, 0xd5, 0xfc, 0x33, 0x35, 0x55, 0xd2, 0xaf, 0x65, 0x4a, 0xfa, 0xd9, 0xe4, 0xe2, 0x66,
	0x3e, 0xb9, 0x48, 0xd5, 0xef, 0x27, 0xb2, 0xf5, 0xfb, 0xaf, 0x80, 0xd8, 0xcc, 0x08, 0xa8, 0x39,
	0x38, 0x37, 0x12, 0x34, 0xd1, 0x39, 0x68, 0xda, 0x4c, 0xe7, 0x13, 0xfd, 0x92, 0x6a, 0xff, 0x54,
	0xae, 0xda, 0xbf, 0x0d, 0x8b, 0x31, 0x35, 0x05, 0x51, 0xe8, 0xc7, 0x82, 0x24, 0xb7, 0x9f, 0xe0,
	0x3f, 0x85, 0x65, 0xae, 0x19, 0x81, 0x7d, 0x76, 0x46, 0x03, 0x75, 0xc5, 0x34, 0xae, 0x58, 0xb4,
	0x59, 0x5f, 0xcc, 0x29, 0x6b, 0x32, 0x5d, 0x0c, 0xc8, 0x77, 0x31, 0xd6, 0x00, 0x6c, 0x66, 0x38,
	0xe2, 0x32, 0xb0, 0xa8, 0x59, 0xd7, 0xa7, 0x6d, 0x26, 0x6f, 0x47, 0xdb, 0x87, 0x3b, 0x5c, 0xfb,
	0x92, 0x04, 0x48, 0x4e, 0x7c, 0x74, 0xd6, 0xfc, 0x87, 0x9b, 0xb0, 0x76, 0x05, 0x25, 0xe6, 0x5f,
	0x3f, 0x61, 0xfb, 0x15, 0xac, 0x4a, 0x7e, 0x93, 0x1c, 0x34, 0x4e, 0xc0, 0x44, 0xb2, 0xbc, 0x9e,
	0xd2, 0xfb, 0xbc, 0xaa, 0xe9, 0x2b, 0x4e, 0x0e, 0x16, 0xd5, 0xfc, 0xd0, 0xf1, 0xa7, 0xa2, 0x07,
	0x0d, 0xa4, 0x2e, 0x60, 0xa5, 0xee, 0x48, 0x89, 0x21, 0x34, 0x20, 0x7f, 0x0e, 0x2b, 0xb1, 0x4a,
	0x19, 0x29, 0x13, 0x9c, 0xdc, 0xa8, 0x6d, 0x36, 0xf4, 0xa5, 0x78, 0xf6, 0x48, 0x49, 0xcb, 0xdf,
	0xc2, 0x9c, 0xef, 0x79, 0x8e, 0xc2, 0xf8, 0x14, 0x32, 0xfe, 0xd3, 0x14, 0xe3, 0x57, 0x0a, 0x6c,
	0xfb, 0xc8, 0xf3, 0x1c, 0x1c, 0xcc, 0xfa, 0xf2, 0x17, 0x92, 0x7e, 0x02, 0x8b, 0x89, 0x78, 0x92,
	0xbb, 0x17, 0xef, 0x63, 0x12, 0x9f, 0x3b, 0x9e, 0x69, 0xff, 0x63, 0x05, 0xea, 0x11, 0xad, 0xcf,
	0x60, 0x83, 0x69, 0xbd, 0xaf, 0xe5, 0xf4, 0x3e, 0xa3, 0x93, 0x71, 0x25, 0x29, 0x06, 0x25, 0x45,
	0x9b, 0x24, 0xf7, 0x94, 0x45, 0x1b, 0xb5, 0xc5, 0x59, 0x54, 0xdc, 0xc8, 0x16, 0xfd, 0x75, 0x48,
	0xfa, 0x9a, 0x63, 0x13, 0xcd, 0xa4, 0x68, 0xa3, 0xee, 0x5b, 0x5e, 0xb4, 0xf9, 0xbd, 0x28, 0x96,
	0x24, 0xc8, 0x99, 0xe7, 0x7d, 0x92, 0xf1, 0x55, 0x8a, 0x33, 0xbe, 0xaa, 0x5a, 0xe2, 0x50, 0x53,
	0xc7, 0x5a, 0x3a, 0x75, 0x4c, 0xf6, 0xbf, 0xa9, 0xda, 0x01, 0x3f, 0x8f, 0x3b, 0x70, 0x46, 0x16,
	0xc5, 0xc0, 0x32, 0x21, 0xcf, 0x23, 0x40, 0x7b, 0xd4, 0x91, 0x35, 0x90, 0x12, 0xfe, 0x44, 0xe6,
	0xa6, 0x26, 0xff, 0xa5, 0x99, 0x5b, 0x4e, 0xaa, 0x73, 0x34, 0x45, 0xb3, 0x24, 0x93, 0xfc, 0x19,
	0x66, 0x92, 0xe9, 0xed, 0xb9, 0x5c, 0xc6, 0xf2, 0x2d, 0x52, 0xcd, 0xec, 0xc2, 0xcf, 0xc8, 0xb0,
	0x76, 0x98, 0x61, 0xed, 0xea, 0x7a, 0xdb, 0x78, 0x8e, 0xdf, 0x64, 0x38, 0x56, 0x0b, 0x72, 0x7f,
	0x8a, 0xce, 0x26, 0x95, 0x99, 0xb4, 0x29, 0x8c, 0xab, 0xcc, 0xa4, 0x15, 0x58, 0xfb, 0x01, 0xb6,
	0x8a, 0xb2, 0xbe, 0x04, 0xeb, 0x63, 0x52, 0xc8, 0xef, 0xe1, 0xcf, 0xae, 0x4d, 0x6c, 0x5c, 0x26,
	0xf9, 0xd7, 0xf0, 0x00, 0xad, 0x8f, 0x7b, 0xb8, 0x5d, 0xd3, 0xed, 0x51, 0xd7, 0xea, 0xd1, 0xf0,
	0xf9, 0x07, 0x45, 0x92, 0x56, 0x71, 0x98, 0xc9, 0x7f, 0xc8, 0x50, 0x2d, 0xf8, 0x90, 0x21, 0x95,
	0xc7, 0x73, 0xab, 0xaa, 0x29, 0x79, 0xfc, 0x73, 0xf8, 0xe2, 0x1a, 0x9b, 0x33, 0x9f, 0x9b, 0xe6,
	0xc0, 0x74, 0x0d, 0xbe, 0x50, 0x9e, 0x60, 0x6a, 0x20, 0xf0, 0xb5, 0x3f, 0xd6, 0xe0, 0x4e, 0xf4,
	0x18, 0xc8, 0x95, 0xe9, 0x4b, 0x9f, 0x2e, 0xa1, 0x19, 0x9c, 0xd1, 0xd0, 0x48, 0xd2, 0xa5, 0x69,
	0x01, 0x39, 0x4e, 0x05, 0xbd, 0x5a, 0xa6, 0xd4, 0xa9, 0x3c, 0x56, 0x2a, 0xd7, 0x7d, 0xac, 0x90,
	0x4d, 0x68, 0x9e, 0x06, 0xde, 0xd0, 0x18, 0x5d, 0x5c, 0x1a, 0xe6, 0x40, 0x3c, 0x86, 0x44, 0x77,
	0x64, 0x8e, 0xc3, 0x8f, 0x2f, 0x2e, 0x77, 0x04, 0x94, 0x6c, 0xc1, 0x42, 0x8c, 0xe9, 0xda, 0x83,
	0x0b, 0xf4, 0x44, 0xa2, 0x17, 0x32, 0x2f, 0x51, 0x0f, 0x25, 0x98, 0x4b, 0x3f, 0xf4, 0x52, 0x34,
	0x45, 0x78, 0x99, 0x0d, 0x3d, 0x85, 0xe2, 0x43, 0x98, 0x97, 0x58, 0x31, 0x3d, 0xd1, 0xea, 0x68,
	0x20, 0x5a, 0x4c, 0x6d, 0x0d, 0xf0, 0xf3, 0x19, 0x97, 0x3a, 0xfc, 0x74, 0x20, 0x24, 0x22, 0x21,
	0x5d, 0x8b, 0x7b, 0x17, 0x36, 0xf0, 0x02, 0x8a, 0xd9, 0x09, 0x17, 0x08, 0x1f, 0xe0, 0x07, 0x01,
	0x96, 0x65, 0x04, 0xf6, 0xe0, 0x5c, 0x7e, 0x00, 0x31, 0x65, 0x5a, 0x96, 0x6e, 0x0f, 0xce, 0xb1,
	0x71, 0x6f, 0x59, 0xc6, 0xe0, 0xdc, 0x0c, 0x86, 0xf8, 0xa5, 0x55, 0x43, 0xe7, 0xb8, 0xbb, 0x7c,
	0xcc, 0xd7, 0x9d, 0x8d, 0x6c, 0x07, 0x3f, 0x24, 0x98, 0x13, 0xeb, 0x70, 0xdc, 0xb5, 0xb4, 0x75,
	0x58, 0xbb, 0xe2, 0x2e, 0x99, 0xaf, 0x3d, 0x41, 0x87, 0xcf, 0x71, 0x92, 0xf9, 0xef, 0x6c, 0x16,
	0x7a, 0xc1, 0x87, 0xe2, 0xf7, 0xcf, 0x39, 0x7a, 0xe0, 0x92, 0x05, 0xf8, 0x98, 0x2f, 0x73, 0x68,
	0x77, 0x4b, 0x7c, 0x44, 0xb4, 0x3a, 0xeb, 0xd1, 0x02, 0xb8, 0x95, 0xf2, 0x40, 0xbd, 0xd1, 0x70,
	0x68, 0x0a, 0xb6, 0xd6, 0x00, 0x98, 0x18, 0x29, 0xf6, 0x2c, 0x21, 0x22, 0xa4, 0x47, 0xd3, 0xd8,
	0x30, 0x16, 0x1a, 0x39, 0x23, 0x61, 0xd8, 0x06, 0xe6, 0x3a, 0x39, 0x3a, 0x51, 0x75, 0x72, 0x74,
	0xd2, 0xb5, 0x34, 0x0b, 0x5a, 0xc5, 0x7b, 0x66, 0x4e, 0x36, 0x1a, 0x5e, 0xe7, 0x64, 0xd1, 0xea,
	0xf8, 0x64, 0x23, 0xf1, 0x19, 0xc9, 0xbf, 0x55, 0x60, 0x21, 0x77, 0xfe, 0xcf, 0x66, 0x57, 0x29,
	0xe3, 0xb9, 0x99, 0x31, 0x1e, 0xf1, 0x6a, 0x1b, 0x5d, 0x5c, 0x4a, 0x97, 0x3f, 0x61, 0xb3, 0xe3,
	0x8b, 0xcb, 0x8c, 0xbe, 0x4e, 0x66, 0xf4, 0x55, 0xfb, 0xf7, 0x14, 0xc3, 0xf2, 0x58, 0x65, 0xa1,
	0x65, 0x09, 0x26, 0x84, 0x01, 0xc9, 0xd0, 0x39, 0x88, 0xbe, 0x58, 0x61, 0xb6, 0x7b, 0x86, 0xdf,
	0x27, 0xf2, 0x17, 0x6a, 0x4d, 0xde, 0x09, 0xc2, 0x8e, 0x52, 0xef, 0x54, 0xce, 0x73, 0x35, 0xf9,
	0xcc, 0x12, 0x1f, 0xa8, 0xaa, 0x2b, 0x98, 0x46, 0x48, 0xee, 0xfb, 0x82, 0xc9, 0xd4, 0xf7, 0x05,
	0x5b, 0x1e, 0xcc, 0xaa, 0x5f, 0x4a, 0x91, 0x35, 0x58, 0x3d, 0xd2, 0x3b, 0xbd, 0xce, 0x61, 0xdf,
	0x38, 0xe8, 0xbc, 0xee, 0x1c, 0x18, 0xc7, 0x87, 0xbd, 0xa3, 0xce, 0x6e, 0xf7, 0x45, 0xb7, 0xb3,
	0xd7, 0xbc, 0x41, 0x16, 0x61, 0x3e, 0x3d, 0xad, 0x37, 0x81, 0x2c, 0x41, 0x33, 0x0d, 0xec, 0xe9,
	0xcd, 0x25, 0xb2, 0x0c, 0x0b, 0x19, 0x68, 0x4f, 0x6f, 0xde, 0xdd, 0x62, 0xea, 0x67, 0xad, 0xe2,
	0xbb, 0x4e, 0xa2, 0xc1, 0xdd, 0x08, 0xb5, 0xd7, 0xe9, 0x1b, 0x47, 0x1d, 0xbd, 0xfb, 0x6a, 0x2f,
	0xb3, 0xf3, 0x5d, 0x68, 0x17, 0xe0, 0x1c, 0x74, 0x5f, 0x76, 0xfb, 0x9d, 0xbd, 0x66, 0x85, 0xdc,
	0x86, 0x5b, 0x45, 0xf3, 0xaf, 0x0e, 0xf7, 0x9b, 0xd5, 0xad, 0xff, 0xa8, 0xc0, 0x4a, 0x71, 0x77,
	0x8a, 0x3c, 0x82, 0x2f, 0xd4, 0x75, 0x3b, 0xbb, 0xfd, 0xee, 0xeb, 0x8e, 0xd1, 0xeb, 0xef, 0xf4,
	0x8f, 0x7b, 0x19, 0x16, 0x1e, 0x82, 0x56, 0x8e, 0xda, 0x3d, 0x14, 0xe3, 0x66, 0x85, 0x3c, 0x80,
	0x8d, 0x72, 0x3c, 0x89, 0x55, 0x25, 0xf7, 0x61, 0xbd, 0x1c, 0xab, 0x73, 0xb8, 0xd7, 0xd9, 0x6b,
	0xd6, 0xb6, 0x7e, 0xac, 0xc0, 0x72, 0xe1, 0x87, 0xa6, 0x64, 0x13, 0x1e, 0xa4, 0xce, 0xab, 0x77,
	0x5e, 0x77, 0x3b, 0x6f, 0x8c, 0xfe, 0xdb, 0xa3, 0x4e, 0x86, 0xed, 0xac, 0x74, 0x55, 0xcc, 0xee,
	0xcb, 0x9d, 0x7d, 0xce, 0xf2, 0x55, 0x38, 0xaf, 0xbb, 0x7b, 0x9d, 0x57, 0xcd, 0xea, 0xd6, 0x6f,
	0x61, 0x41, 0x3e, 0x98, 0x45, 0x97, 0x1a, 0xd9, 0xb8, 0x0f, 0xeb, 0xbb, 0xaf, 0x0e, 0x0e, 0x3a,
	0xbb, 0x7d, 0xa3, 0xf7, 0xea, 0x58, 0xdf, 0xed, 0x94, 0x70, 0x50, 0x84, 0xb4, 0x7f, 0xbc, 0xa3,
	0xef, 0x1c, 0xf6, 0x3b, 0x78, 0x7f, 0xeb, 0x70, 0xbb, 0x08, 0xe7, 0xe0, 0x55, 0xbf, 0xdf, 0xd1,
	0xdf, 0x36, 0xab, 0x5b, 0x7f, 0x03, 0xcd, 0xec, 0x17, 0xa7, 0x9c, 0x30, 0xca, 0xad, 0xdb, 0x7f,
	0x6b, 0x1c, 0x74, 0x0f, 0x7f, 0x28, 0xd9, 0xbc, 0x00, 0xe7, 0xbb, 0x9d, 0x83, 0x17, 0x46, 0x6f,
	0x57, 0xef, 0x74, 0x0e, 0xc5, 0xf1, 0x0b, 0x70, 0x5e, 0x1c, 0x1f, 0x1c, 0x44, 0x38, 0xd5, 0xad,
	0xbf, 0xaf, 0x40, 0xab, 0xec, 0xfb, 0x19, 0xb2, 0x01, 0x77, 0xca, 0xe6, 0x0e, 0x3d, 0x97, 0x36,
	0x6f, 0xf0, 0xf3, 0x95, 0x61, 0xec, 0xbe, 0xec, 0x35, 0x2b, 0xe4, 0x4b, 0xb8, 0x5f, 0x86, 0x10,
	0x9d, 0xfb, 0x38, 0x70, 0x9a, 0xd5, 0xa7, 0xff, 0xd3, 0x02, 0x48, 0x74, 0x82, 0xbc, 0x85, 0x66,
	0xb6, 0xe3, 0x4c, 0x36, 0x52, 0xae, 0xb6, 0xa0, 0x11, 0xde, 0xbe, 0x37, 0x06, 0x83, 0xf9, 0xda,
	0x0d, 0x32, 0x40, 0x3f, 0x9f, 0x6e, 0x37, 0xcb, 0x77, 0x04, 0x49, 0x13, 0x28, 0x6a, 0x62, 0xb7,
	0xb5, 0x71, 0x28, 0xb8, 0xc9, 0x6f, 0x32, 0x9b, 0x28, 0x8f, 0x15, 0xb2, 0x59, 0x4e, 0x21, 0xfd,
	0xe6, 0x6a, 0x3f, 0xba, 0x26, 0x26, 0x6e, 0xf9, 0x77, 0x15, 0xb8, 0x7b, 0x75, 0x9b, 0x95, 0x6c,
	0x97, 0xd3, 0x2b, 0x6a, 0xe9, 0xb6, 0x9f, 0x7c, 0x14, 0x3e, 0x72, 0xf1, 0x16, 0x9a, 0xd9, 0x36,
	0x65, 0xe6, 0xe2, 0x0a, 0x9a, 0xaa, 0x99, 0x8b, 0x2b, 0xec, 0x73, 0x22, 0xe9, 0x6c, 0xa7, 0x36,
	0x43, 0xba, 0xa0, 0xed, 0x9b, 0x21, 0x5d, 0xd8, 0xea, 0xbd, 0x41, 0x0c, 0x6c, 0xa2, 0x66, 0x1a,
	0x14, 0x24, 0x77, 0xd5, 0xf9, 0xd6, 0x49, 0xfb, 0xfe, 0x58, 0x1c, 0xdc, 0xa0, 0x87, 0xdf, 0x1a,
	0x2b, 0x73, 0xe4, 0x6e, 0x61, 0x73, 0x23, 0x11, 0xc9, 0xfa, 0x95, 0xf3, 0x48, 0xf4, 0x2f, 0x61,
	0x21, 0xd7, 0xe8, 0x21, 0x45, 0xe7, 0xcd, 0x90, 0xd6, 0xc6, 0xa1, 0x20, 0x75, 0x17, 0x6e, 0x95,
	0x14, 0xd7, 0xc9, 0x97, 0x69, 0x3b, 0x2b, 0x6d, 0x12, 0xb4, 0x37, 0xaf, 0x87, 0x18, 0xed, 0x57,
	0x52, 0x12, 0xce, 0xec, 0x57, 0x5e, 0x6a, 0x6f, 0x6f, 0x5e, 0x0f, 0x31, 0x92, 0x5e, 0xce, 0x7a,
	0x3f, 0x9f, 0x03, 0x78, 0x0d, 0xf3, 0x99, 0x3e, 0x2b, 0x59, 0xcf, 0x2e, 0xcc, 0x34, 0xed, 0xdb,
	0x1b, 0x57, 0x23, 0x14, 0x72, 0x8d, 0xf2, 0xb9, 0x77, 0xb5, 0x9f, 0x18, 0xc3, 0xb5, 0x22, 0x13,
	0x03, 0x48, 0xbe, 0x19, 0x94, 0xb1, 0x83, 0xc2, 0xa6, 0x56, 0xc6, 0x0e, 0x4a, 0x3a, 0x4a, 0x37,
	0xc8, 0x8f, 0x15, 0xd8, 0x18, 0xd7, 0xb2, 0x21, 0x5f, 0x5f, 0x47, 0x6b, 0xd4, 0x47, 0x7e, 0xfb,
	0x27, 0x1f, 0xb9, 0x22, 0x12, 0x65, 0xae, 0xa0, 0x9f, 0x11, 0x65, 0x51, 0xe7, 0x20, 0x23, 0xca,
	0xe2, 0x9e, 0xc0, 0x8d, 0x24, 0x82, 0x29, 0xff, 0x57, 0x28, 0x8a, 0x60, 0xa9, 0x52, 0x48, 0x61,
	0x04, 0xcb, 0x94, 0x3f, 0xa2, 0xe0, 0x52, 0x58, 0x09, 0xcb, 0x07, 0x97, 0xb2, 0x82, 0x5e, 0x3e,
	0xb8, 0x94, 0x96, 0xd6, 0xe2, 0xa0, 0x99, 0x2e, 0x09, 0x95, 0x06, 0xcd, 0x5c, 0x25, 0x2a, 0xaf,
	0x7d, 0xf9, 0xe2, 0x92, 0xea, 0xe0, 0x4b, 0x45, 0x56, 0x50, 0x3d, 0x2a, 0x74, 0xf0, 0x39, 0x91,
	0x09, 0xb3, 0x49, 0x9f, 0xf0, 0x2a, 0xc6, 0x4b, 0xcd, 0x26, 0x5f, 0xc7, 0x2b, 0xa0, 0x5e, 0xec,
	0x4a, 0x3e, 0x55, 0x2c, 0xbf, 0xaf, 0xc0, 0x97, 0xd7, 0xac, 0x51, 0x91, 0x9f, 0x8d, 0x35, 0x84,
	0xe2, 0x32, 0x59, 0xfb, 0xe7, 0x9f, 0xb6, 0x10, 0x19, 0xfc, 0x5d, 0x05, 0xee, 0x8d, 0xad, 0x3d,
	0x91, 0xb4, 0x8d, 0x5e, 0xa7, 0x50, 0xd6, 0x7e, 0xfa, 0xb1, 0x4b, 0x90, 0x9d, 0x10, 0x56, 0x4b,
	0x0b, 0x1f, 0xe4, 0x51, 0xa1, 0x9f, 0x2a, 0x2a, 0x76, 0xb5, 0xb7, 0xae, 0x8b, 0xaa, 0x18, 0x65,
	0x61, 0x71, 0x24, 0x6f, 0x94, 0x65, 0x45, 0x97, 0xbc, 0x51, 0x96, 0x56, 0x5b, 0xb4, 0x1b, 0x84,
	0x66, 0xea, 0xbe, 0xd1, 0xe3, 0xfc, 0x41, 0xb9, 0x5a, 0x25, 0x85, 0x94, 0xf6, 0x17, 0xd7, 0xc0,
	0x8a, 0xb4, 0x3b, 0xd7, 0xf6, 0xce, 0x68, 0x77, 0x51, 0x03, 0x3d, 0xa3, 0xdd, 0xc5, 0x9d, 0x73,
	0xbc, 0xad, 0xd2, 0xc6, 0x50, 0xe6, 0xb6, 0xae, 0xea, 0xdd, 0x65, 0x6e, 0xeb, 0xca, 0x5e, 0x93,
	0x76, 0xe3, 0xf9, 0x93, 0x5f, 0x3d, 0x3e, 0xf3, 0x1c, 0xd3, 0x3d, 0xdb, 0x7e, 0xf6, 0x34, 0x0c,
	0xb7, 0x07, 0xde, 0xf0, 0x09, 0xfe, 0x43, 0x75, 0xe0, 0x39, 0x4f, 0x18, 0x0d, 0xde, 0xd9, 0x03,
	0xca, 0xd4, 0x7f, 0xbc, 0x9e, 0x4c, 0xe2, 0xf4, 0x4f, 0xff, 0x2f, 0x00, 0x00, 0xff, 0xff, 0x77,
	0x63, 0x56, 0xd8, 0x13, 0x3b, 0x00, 0x00,
}
