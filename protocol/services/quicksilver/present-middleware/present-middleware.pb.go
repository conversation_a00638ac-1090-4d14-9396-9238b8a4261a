// Code generated by protoc-gen-go. DO NOT EDIT.
// source: src/quicksilver/present-middleware/present-middleware.proto

package present_middleware

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 送礼来源类型
type PresentSendSourceType int32

const (
	PresentSendSourceType_E_SEND_SOURCE_DEFLAUTE         PresentSendSourceType = 0
	PresentSendSourceType_E_SEND_SOURCE_GIFT_TURNTABLE   PresentSendSourceType = 1
	PresentSendSourceType_E_SEND_SOURCE_GIFT_SHELF       PresentSendSourceType = 2
	PresentSendSourceType_E_SEND_SOURCE_SPEECH_BALL      PresentSendSourceType = 3
	PresentSendSourceType_E_SEND_SOURCE_DRAW_GIFT        PresentSendSourceType = 4
	PresentSendSourceType_E_SEND_SOURCE_MASKED_CALL      PresentSendSourceType = 5
	PresentSendSourceType_E_SEND_SOURCE_IM               PresentSendSourceType = 6
	PresentSendSourceType_E_SEND_SOURCE_OFFICIAL_CHANNEL PresentSendSourceType = 7
)

var PresentSendSourceType_name = map[int32]string{
	0: "E_SEND_SOURCE_DEFLAUTE",
	1: "E_SEND_SOURCE_GIFT_TURNTABLE",
	2: "E_SEND_SOURCE_GIFT_SHELF",
	3: "E_SEND_SOURCE_SPEECH_BALL",
	4: "E_SEND_SOURCE_DRAW_GIFT",
	5: "E_SEND_SOURCE_MASKED_CALL",
	6: "E_SEND_SOURCE_IM",
	7: "E_SEND_SOURCE_OFFICIAL_CHANNEL",
}
var PresentSendSourceType_value = map[string]int32{
	"E_SEND_SOURCE_DEFLAUTE":         0,
	"E_SEND_SOURCE_GIFT_TURNTABLE":   1,
	"E_SEND_SOURCE_GIFT_SHELF":       2,
	"E_SEND_SOURCE_SPEECH_BALL":      3,
	"E_SEND_SOURCE_DRAW_GIFT":        4,
	"E_SEND_SOURCE_MASKED_CALL":      5,
	"E_SEND_SOURCE_IM":               6,
	"E_SEND_SOURCE_OFFICIAL_CHANNEL": 7,
}

func (x PresentSendSourceType) String() string {
	return proto.EnumName(PresentSendSourceType_name, int32(x))
}
func (PresentSendSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{0}
}

// 购买还是背包
type PresentSourceType int32

const (
	PresentSourceType_PRESENT_SOURCE_BUY           PresentSourceType = 0
	PresentSourceType_PRESENT_SOURCE_PACKAGE       PresentSourceType = 1
	PresentSourceType_PRESENT_SOURCE_PACKAGE_FIRST PresentSourceType = 2
)

var PresentSourceType_name = map[int32]string{
	0: "PRESENT_SOURCE_BUY",
	1: "PRESENT_SOURCE_PACKAGE",
	2: "PRESENT_SOURCE_PACKAGE_FIRST",
}
var PresentSourceType_value = map[string]int32{
	"PRESENT_SOURCE_BUY":           0,
	"PRESENT_SOURCE_PACKAGE":       1,
	"PRESENT_SOURCE_PACKAGE_FIRST": 2,
}

func (x PresentSourceType) String() string {
	return proto.EnumName(PresentSourceType_name, int32(x))
}
func (PresentSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{1}
}

// 送礼类型
type PresentSendType int32

const (
	PresentSendType_PRESENT_SEND_NORMAL PresentSendType = 0
	PresentSendType_PRESENT_SEND_DRAW   PresentSendType = 1
)

var PresentSendType_name = map[int32]string{
	0: "PRESENT_SEND_NORMAL",
	1: "PRESENT_SEND_DRAW",
}
var PresentSendType_value = map[string]int32{
	"PRESENT_SEND_NORMAL": 0,
	"PRESENT_SEND_DRAW":   1,
}

func (x PresentSendType) String() string {
	return proto.EnumName(PresentSendType_name, int32(x))
}
func (PresentSendType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{2}
}

// 涂鸦礼物图
type DrawPresentPicture struct {
	LineList             []*PresentLine `protobuf:"bytes,1,rep,name=line_list,json=lineList,proto3" json:"line_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *DrawPresentPicture) Reset()         { *m = DrawPresentPicture{} }
func (m *DrawPresentPicture) String() string { return proto.CompactTextString(m) }
func (*DrawPresentPicture) ProtoMessage()    {}
func (*DrawPresentPicture) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{0}
}
func (m *DrawPresentPicture) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DrawPresentPicture.Unmarshal(m, b)
}
func (m *DrawPresentPicture) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DrawPresentPicture.Marshal(b, m, deterministic)
}
func (dst *DrawPresentPicture) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DrawPresentPicture.Merge(dst, src)
}
func (m *DrawPresentPicture) XXX_Size() int {
	return xxx_messageInfo_DrawPresentPicture.Size(m)
}
func (m *DrawPresentPicture) XXX_DiscardUnknown() {
	xxx_messageInfo_DrawPresentPicture.DiscardUnknown(m)
}

var xxx_messageInfo_DrawPresentPicture proto.InternalMessageInfo

func (m *DrawPresentPicture) GetLineList() []*PresentLine {
	if m != nil {
		return m.LineList
	}
	return nil
}

type PresentLine struct {
	ItemId               uint32          `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	PointList            []*PresentPoint `protobuf:"bytes,2,rep,name=point_list,json=pointList,proto3" json:"point_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PresentLine) Reset()         { *m = PresentLine{} }
func (m *PresentLine) String() string { return proto.CompactTextString(m) }
func (*PresentLine) ProtoMessage()    {}
func (*PresentLine) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{1}
}
func (m *PresentLine) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentLine.Unmarshal(m, b)
}
func (m *PresentLine) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentLine.Marshal(b, m, deterministic)
}
func (dst *PresentLine) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentLine.Merge(dst, src)
}
func (m *PresentLine) XXX_Size() int {
	return xxx_messageInfo_PresentLine.Size(m)
}
func (m *PresentLine) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentLine.DiscardUnknown(m)
}

var xxx_messageInfo_PresentLine proto.InternalMessageInfo

func (m *PresentLine) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentLine) GetPointList() []*PresentPoint {
	if m != nil {
		return m.PointList
	}
	return nil
}

type PresentPoint struct {
	X                    float32  `protobuf:"fixed32,1,opt,name=x,proto3" json:"x,omitempty"`
	Y                    float32  `protobuf:"fixed32,2,opt,name=y,proto3" json:"y,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentPoint) Reset()         { *m = PresentPoint{} }
func (m *PresentPoint) String() string { return proto.CompactTextString(m) }
func (*PresentPoint) ProtoMessage()    {}
func (*PresentPoint) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{2}
}
func (m *PresentPoint) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentPoint.Unmarshal(m, b)
}
func (m *PresentPoint) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentPoint.Marshal(b, m, deterministic)
}
func (dst *PresentPoint) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentPoint.Merge(dst, src)
}
func (m *PresentPoint) XXX_Size() int {
	return xxx_messageInfo_PresentPoint.Size(m)
}
func (m *PresentPoint) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentPoint.DiscardUnknown(m)
}

var xxx_messageInfo_PresentPoint proto.InternalMessageInfo

func (m *PresentPoint) GetX() float32 {
	if m != nil {
		return m.X
	}
	return 0
}

func (m *PresentPoint) GetY() float32 {
	if m != nil {
		return m.Y
	}
	return 0
}

// 赠送礼物
type PresentSendMsg struct {
	ItemInfo             *PresentSendItemInfo `protobuf:"bytes,1,opt,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`
	SendTime             uint64               `protobuf:"varint,2,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ChannelId            uint32               `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SendUid              uint32               `protobuf:"varint,4,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	SendAccount          string               `protobuf:"bytes,5,opt,name=send_account,json=sendAccount,proto3" json:"send_account,omitempty"`
	SendNickname         string               `protobuf:"bytes,6,opt,name=send_nickname,json=sendNickname,proto3" json:"send_nickname,omitempty"`
	TargetUid            uint32               `protobuf:"varint,7,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TargetAccount        string               `protobuf:"bytes,8,opt,name=target_account,json=targetAccount,proto3" json:"target_account,omitempty"`
	TargetNickname       string               `protobuf:"bytes,9,opt,name=target_nickname,json=targetNickname,proto3" json:"target_nickname,omitempty"`
	ExtendJson           string               `protobuf:"bytes,10,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *PresentSendMsg) Reset()         { *m = PresentSendMsg{} }
func (m *PresentSendMsg) String() string { return proto.CompactTextString(m) }
func (*PresentSendMsg) ProtoMessage()    {}
func (*PresentSendMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{3}
}
func (m *PresentSendMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSendMsg.Unmarshal(m, b)
}
func (m *PresentSendMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSendMsg.Marshal(b, m, deterministic)
}
func (dst *PresentSendMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSendMsg.Merge(dst, src)
}
func (m *PresentSendMsg) XXX_Size() int {
	return xxx_messageInfo_PresentSendMsg.Size(m)
}
func (m *PresentSendMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSendMsg.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSendMsg proto.InternalMessageInfo

func (m *PresentSendMsg) GetItemInfo() *PresentSendItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

func (m *PresentSendMsg) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *PresentSendMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PresentSendMsg) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *PresentSendMsg) GetSendAccount() string {
	if m != nil {
		return m.SendAccount
	}
	return ""
}

func (m *PresentSendMsg) GetSendNickname() string {
	if m != nil {
		return m.SendNickname
	}
	return ""
}

func (m *PresentSendMsg) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *PresentSendMsg) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *PresentSendMsg) GetTargetNickname() string {
	if m != nil {
		return m.TargetNickname
	}
	return ""
}

func (m *PresentSendMsg) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

type SendPresentReq struct {
	TargetUid            uint32              `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ItemId               uint32              `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ChannelId            uint32              `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32              `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	SendSource           uint32              `protobuf:"varint,5,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	ItemSource           uint32              `protobuf:"varint,6,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId             uint32              `protobuf:"varint,7,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SendType             uint32              `protobuf:"varint,8,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	DrawPresentPic       *DrawPresentPicture `protobuf:"bytes,9,opt,name=draw_present_pic,json=drawPresentPic,proto3" json:"draw_present_pic,omitempty"`
	AppId                uint32              `protobuf:"varint,10,opt,name=appId,proto3" json:"appId,omitempty"`
	MarketId             uint32              `protobuf:"varint,11,opt,name=marketId,proto3" json:"marketId,omitempty"`
	SendUid              uint32              `protobuf:"varint,12,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	ServiceInfo          *ServiceCtrlInfo    `protobuf:"bytes,13,opt,name=service_info,json=serviceInfo,proto3" json:"service_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SendPresentReq) Reset()         { *m = SendPresentReq{} }
func (m *SendPresentReq) String() string { return proto.CompactTextString(m) }
func (*SendPresentReq) ProtoMessage()    {}
func (*SendPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{4}
}
func (m *SendPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPresentReq.Unmarshal(m, b)
}
func (m *SendPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPresentReq.Marshal(b, m, deterministic)
}
func (dst *SendPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPresentReq.Merge(dst, src)
}
func (m *SendPresentReq) XXX_Size() int {
	return xxx_messageInfo_SendPresentReq.Size(m)
}
func (m *SendPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendPresentReq proto.InternalMessageInfo

func (m *SendPresentReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SendPresentReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *SendPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SendPresentReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *SendPresentReq) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *SendPresentReq) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *SendPresentReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *SendPresentReq) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *SendPresentReq) GetDrawPresentPic() *DrawPresentPicture {
	if m != nil {
		return m.DrawPresentPic
	}
	return nil
}

func (m *SendPresentReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *SendPresentReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *SendPresentReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *SendPresentReq) GetServiceInfo() *ServiceCtrlInfo {
	if m != nil {
		return m.ServiceInfo
	}
	return nil
}

type SendPresentResp struct {
	ItemId                  uint32          `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	MsgInfo                 *PresentSendMsg `protobuf:"bytes,2,opt,name=msg_info,json=msgInfo,proto3" json:"msg_info,omitempty"`
	MemberContributionAdded uint32          `protobuf:"varint,3,opt,name=member_contribution_added,json=memberContributionAdded,proto3" json:"member_contribution_added,omitempty"`
	Count                   uint32          `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	CurTbeans               uint64          `protobuf:"varint,5,opt,name=cur_tbeans,json=curTbeans,proto3" json:"cur_tbeans,omitempty"`
	ItemSource              uint32          `protobuf:"varint,6,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId                uint32          `protobuf:"varint,7,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SourceRemain            uint32          `protobuf:"varint,8,opt,name=source_remain,json=sourceRemain,proto3" json:"source_remain,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}        `json:"-"`
	XXX_unrecognized        []byte          `json:"-"`
	XXX_sizecache           int32           `json:"-"`
}

func (m *SendPresentResp) Reset()         { *m = SendPresentResp{} }
func (m *SendPresentResp) String() string { return proto.CompactTextString(m) }
func (*SendPresentResp) ProtoMessage()    {}
func (*SendPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{5}
}
func (m *SendPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPresentResp.Unmarshal(m, b)
}
func (m *SendPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPresentResp.Marshal(b, m, deterministic)
}
func (dst *SendPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPresentResp.Merge(dst, src)
}
func (m *SendPresentResp) XXX_Size() int {
	return xxx_messageInfo_SendPresentResp.Size(m)
}
func (m *SendPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendPresentResp proto.InternalMessageInfo

func (m *SendPresentResp) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *SendPresentResp) GetMsgInfo() *PresentSendMsg {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

func (m *SendPresentResp) GetMemberContributionAdded() uint32 {
	if m != nil {
		return m.MemberContributionAdded
	}
	return 0
}

func (m *SendPresentResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *SendPresentResp) GetCurTbeans() uint64 {
	if m != nil {
		return m.CurTbeans
	}
	return 0
}

func (m *SendPresentResp) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *SendPresentResp) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *SendPresentResp) GetSourceRemain() uint32 {
	if m != nil {
		return m.SourceRemain
	}
	return 0
}

type PresentTargetUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentTargetUserInfo) Reset()         { *m = PresentTargetUserInfo{} }
func (m *PresentTargetUserInfo) String() string { return proto.CompactTextString(m) }
func (*PresentTargetUserInfo) ProtoMessage()    {}
func (*PresentTargetUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{6}
}
func (m *PresentTargetUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentTargetUserInfo.Unmarshal(m, b)
}
func (m *PresentTargetUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentTargetUserInfo.Marshal(b, m, deterministic)
}
func (dst *PresentTargetUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentTargetUserInfo.Merge(dst, src)
}
func (m *PresentTargetUserInfo) XXX_Size() int {
	return xxx_messageInfo_PresentTargetUserInfo.Size(m)
}
func (m *PresentTargetUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentTargetUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentTargetUserInfo proto.InternalMessageInfo

func (m *PresentTargetUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PresentTargetUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *PresentTargetUserInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type PresentBatchTargetInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	ExtendJson           string   `protobuf:"bytes,4,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentBatchTargetInfo) Reset()         { *m = PresentBatchTargetInfo{} }
func (m *PresentBatchTargetInfo) String() string { return proto.CompactTextString(m) }
func (*PresentBatchTargetInfo) ProtoMessage()    {}
func (*PresentBatchTargetInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{7}
}
func (m *PresentBatchTargetInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentBatchTargetInfo.Unmarshal(m, b)
}
func (m *PresentBatchTargetInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentBatchTargetInfo.Marshal(b, m, deterministic)
}
func (dst *PresentBatchTargetInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentBatchTargetInfo.Merge(dst, src)
}
func (m *PresentBatchTargetInfo) XXX_Size() int {
	return xxx_messageInfo_PresentBatchTargetInfo.Size(m)
}
func (m *PresentBatchTargetInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentBatchTargetInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentBatchTargetInfo proto.InternalMessageInfo

func (m *PresentBatchTargetInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PresentBatchTargetInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *PresentBatchTargetInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *PresentBatchTargetInfo) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

type PresentSendItemInfo struct {
	ItemId               uint32              `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32              `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	ShowEffect           uint32              `protobuf:"varint,3,opt,name=show_effect,json=showEffect,proto3" json:"show_effect,omitempty"`
	ShowEffectV2         uint32              `protobuf:"varint,4,opt,name=show_effect_v2,json=showEffectV2,proto3" json:"show_effect_v2,omitempty"`
	FlowId               uint32              `protobuf:"varint,5,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	IsBatch              bool                `protobuf:"varint,6,opt,name=is_batch,json=isBatch,proto3" json:"is_batch,omitempty"`
	ShowBatchEffect      bool                `protobuf:"varint,7,opt,name=show_batch_effect,json=showBatchEffect,proto3" json:"show_batch_effect,omitempty"`
	SendType             uint32              `protobuf:"varint,8,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	DrawPresentPic       *DrawPresentPicture `protobuf:"bytes,9,opt,name=draw_present_pic,json=drawPresentPic,proto3" json:"draw_present_pic,omitempty"`
	DynamicTemplateId    uint32              `protobuf:"varint,10,opt,name=dynamic_template_id,json=dynamicTemplateId,proto3" json:"dynamic_template_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PresentSendItemInfo) Reset()         { *m = PresentSendItemInfo{} }
func (m *PresentSendItemInfo) String() string { return proto.CompactTextString(m) }
func (*PresentSendItemInfo) ProtoMessage()    {}
func (*PresentSendItemInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{8}
}
func (m *PresentSendItemInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSendItemInfo.Unmarshal(m, b)
}
func (m *PresentSendItemInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSendItemInfo.Marshal(b, m, deterministic)
}
func (dst *PresentSendItemInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSendItemInfo.Merge(dst, src)
}
func (m *PresentSendItemInfo) XXX_Size() int {
	return xxx_messageInfo_PresentSendItemInfo.Size(m)
}
func (m *PresentSendItemInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSendItemInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSendItemInfo proto.InternalMessageInfo

func (m *PresentSendItemInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentSendItemInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *PresentSendItemInfo) GetShowEffect() uint32 {
	if m != nil {
		return m.ShowEffect
	}
	return 0
}

func (m *PresentSendItemInfo) GetShowEffectV2() uint32 {
	if m != nil {
		return m.ShowEffectV2
	}
	return 0
}

func (m *PresentSendItemInfo) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

func (m *PresentSendItemInfo) GetIsBatch() bool {
	if m != nil {
		return m.IsBatch
	}
	return false
}

func (m *PresentSendItemInfo) GetShowBatchEffect() bool {
	if m != nil {
		return m.ShowBatchEffect
	}
	return false
}

func (m *PresentSendItemInfo) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *PresentSendItemInfo) GetDrawPresentPic() *DrawPresentPicture {
	if m != nil {
		return m.DrawPresentPic
	}
	return nil
}

func (m *PresentSendItemInfo) GetDynamicTemplateId() uint32 {
	if m != nil {
		return m.DynamicTemplateId
	}
	return 0
}

// 批量送礼信息
type PresentBatchInfoMsg struct {
	ItemId               uint32                    `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	TotalItemCount       uint32                    `protobuf:"varint,2,opt,name=total_item_count,json=totalItemCount,proto3" json:"total_item_count,omitempty"`
	BatchType            uint32                    `protobuf:"varint,3,opt,name=batch_type,json=batchType,proto3" json:"batch_type,omitempty"`
	SendTime             uint64                    `protobuf:"varint,4,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ChannelId            uint32                    `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SendUid              uint32                    `protobuf:"varint,6,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	SendAccount          string                    `protobuf:"bytes,7,opt,name=send_account,json=sendAccount,proto3" json:"send_account,omitempty"`
	SendNickname         string                    `protobuf:"bytes,8,opt,name=send_nickname,json=sendNickname,proto3" json:"send_nickname,omitempty"`
	ExtendJson           string                    `protobuf:"bytes,9,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	TargetList           []*PresentBatchTargetInfo `protobuf:"bytes,10,rep,name=target_list,json=targetList,proto3" json:"target_list,omitempty"`
	ItemInfo             *PresentSendItemInfo      `protobuf:"bytes,11,opt,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *PresentBatchInfoMsg) Reset()         { *m = PresentBatchInfoMsg{} }
func (m *PresentBatchInfoMsg) String() string { return proto.CompactTextString(m) }
func (*PresentBatchInfoMsg) ProtoMessage()    {}
func (*PresentBatchInfoMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{9}
}
func (m *PresentBatchInfoMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentBatchInfoMsg.Unmarshal(m, b)
}
func (m *PresentBatchInfoMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentBatchInfoMsg.Marshal(b, m, deterministic)
}
func (dst *PresentBatchInfoMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentBatchInfoMsg.Merge(dst, src)
}
func (m *PresentBatchInfoMsg) XXX_Size() int {
	return xxx_messageInfo_PresentBatchInfoMsg.Size(m)
}
func (m *PresentBatchInfoMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentBatchInfoMsg.DiscardUnknown(m)
}

var xxx_messageInfo_PresentBatchInfoMsg proto.InternalMessageInfo

func (m *PresentBatchInfoMsg) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetTotalItemCount() uint32 {
	if m != nil {
		return m.TotalItemCount
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetBatchType() uint32 {
	if m != nil {
		return m.BatchType
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetSendAccount() string {
	if m != nil {
		return m.SendAccount
	}
	return ""
}

func (m *PresentBatchInfoMsg) GetSendNickname() string {
	if m != nil {
		return m.SendNickname
	}
	return ""
}

func (m *PresentBatchInfoMsg) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *PresentBatchInfoMsg) GetTargetList() []*PresentBatchTargetInfo {
	if m != nil {
		return m.TargetList
	}
	return nil
}

func (m *PresentBatchInfoMsg) GetItemInfo() *PresentSendItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

type ServiceCtrlInfo struct {
	ClientIp             string   `protobuf:"bytes,1,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	ClientPort           uint32   `protobuf:"varint,2,opt,name=client_port,json=clientPort,proto3" json:"client_port,omitempty"`
	DeviceId             string   `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,4,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	TerminalType         uint32   `protobuf:"varint,5,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	ClientId             uint32   `protobuf:"varint,6,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,7,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ServiceCtrlInfo) Reset()         { *m = ServiceCtrlInfo{} }
func (m *ServiceCtrlInfo) String() string { return proto.CompactTextString(m) }
func (*ServiceCtrlInfo) ProtoMessage()    {}
func (*ServiceCtrlInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{10}
}
func (m *ServiceCtrlInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServiceCtrlInfo.Unmarshal(m, b)
}
func (m *ServiceCtrlInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServiceCtrlInfo.Marshal(b, m, deterministic)
}
func (dst *ServiceCtrlInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServiceCtrlInfo.Merge(dst, src)
}
func (m *ServiceCtrlInfo) XXX_Size() int {
	return xxx_messageInfo_ServiceCtrlInfo.Size(m)
}
func (m *ServiceCtrlInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ServiceCtrlInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ServiceCtrlInfo proto.InternalMessageInfo

func (m *ServiceCtrlInfo) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *ServiceCtrlInfo) GetClientPort() uint32 {
	if m != nil {
		return m.ClientPort
	}
	return 0
}

func (m *ServiceCtrlInfo) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *ServiceCtrlInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ServiceCtrlInfo) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *ServiceCtrlInfo) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

func (m *ServiceCtrlInfo) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

// 批量赠送礼物
type BatchSendPresentReq struct {
	ItemId               uint32              `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ChannelId            uint32              `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32              `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	SendSource           uint32              `protobuf:"varint,4,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	ItemSource           uint32              `protobuf:"varint,5,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId             uint32              `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	BatchType            uint32              `protobuf:"varint,7,opt,name=batch_type,json=batchType,proto3" json:"batch_type,omitempty"`
	SendType             uint32              `protobuf:"varint,8,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	DrawPresentPic       *DrawPresentPicture `protobuf:"bytes,9,opt,name=draw_present_pic,json=drawPresentPic,proto3" json:"draw_present_pic,omitempty"`
	AppId                uint32              `protobuf:"varint,10,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId             uint32              `protobuf:"varint,11,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	TargetUidList        []uint32            `protobuf:"varint,12,rep,packed,name=target_uid_list,json=targetUidList,proto3" json:"target_uid_list,omitempty"`
	SendUid              uint32              `protobuf:"varint,13,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	ServiceInfo          *ServiceCtrlInfo    `protobuf:"bytes,14,opt,name=service_info,json=serviceInfo,proto3" json:"service_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchSendPresentReq) Reset()         { *m = BatchSendPresentReq{} }
func (m *BatchSendPresentReq) String() string { return proto.CompactTextString(m) }
func (*BatchSendPresentReq) ProtoMessage()    {}
func (*BatchSendPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{11}
}
func (m *BatchSendPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSendPresentReq.Unmarshal(m, b)
}
func (m *BatchSendPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSendPresentReq.Marshal(b, m, deterministic)
}
func (dst *BatchSendPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSendPresentReq.Merge(dst, src)
}
func (m *BatchSendPresentReq) XXX_Size() int {
	return xxx_messageInfo_BatchSendPresentReq.Size(m)
}
func (m *BatchSendPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSendPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSendPresentReq proto.InternalMessageInfo

func (m *BatchSendPresentReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *BatchSendPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatchSendPresentReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *BatchSendPresentReq) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *BatchSendPresentReq) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *BatchSendPresentReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *BatchSendPresentReq) GetBatchType() uint32 {
	if m != nil {
		return m.BatchType
	}
	return 0
}

func (m *BatchSendPresentReq) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *BatchSendPresentReq) GetDrawPresentPic() *DrawPresentPicture {
	if m != nil {
		return m.DrawPresentPic
	}
	return nil
}

func (m *BatchSendPresentReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *BatchSendPresentReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *BatchSendPresentReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

func (m *BatchSendPresentReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *BatchSendPresentReq) GetServiceInfo() *ServiceCtrlInfo {
	if m != nil {
		return m.ServiceInfo
	}
	return nil
}

type BatchSendPresentResp struct {
	MsgInfo              *PresentBatchInfoMsg     `protobuf:"bytes,1,opt,name=msg_info,json=msgInfo,proto3" json:"msg_info,omitempty"`
	CurTbeans            uint64                   `protobuf:"varint,2,opt,name=cur_tbeans,json=curTbeans,proto3" json:"cur_tbeans,omitempty"`
	ItemSource           uint32                   `protobuf:"varint,3,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId             uint32                   `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SourceRemain         uint32                   `protobuf:"varint,5,opt,name=source_remain,json=sourceRemain,proto3" json:"source_remain,omitempty"`
	TargetList           []*PresentTargetUserInfo `protobuf:"bytes,6,rep,name=target_list,json=targetList,proto3" json:"target_list,omitempty"`
	ItemInfo             *PresentSendItemInfo     `protobuf:"bytes,7,opt,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchSendPresentResp) Reset()         { *m = BatchSendPresentResp{} }
func (m *BatchSendPresentResp) String() string { return proto.CompactTextString(m) }
func (*BatchSendPresentResp) ProtoMessage()    {}
func (*BatchSendPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_f665d130f07824ea, []int{12}
}
func (m *BatchSendPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSendPresentResp.Unmarshal(m, b)
}
func (m *BatchSendPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSendPresentResp.Marshal(b, m, deterministic)
}
func (dst *BatchSendPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSendPresentResp.Merge(dst, src)
}
func (m *BatchSendPresentResp) XXX_Size() int {
	return xxx_messageInfo_BatchSendPresentResp.Size(m)
}
func (m *BatchSendPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSendPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSendPresentResp proto.InternalMessageInfo

func (m *BatchSendPresentResp) GetMsgInfo() *PresentBatchInfoMsg {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

func (m *BatchSendPresentResp) GetCurTbeans() uint64 {
	if m != nil {
		return m.CurTbeans
	}
	return 0
}

func (m *BatchSendPresentResp) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *BatchSendPresentResp) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *BatchSendPresentResp) GetSourceRemain() uint32 {
	if m != nil {
		return m.SourceRemain
	}
	return 0
}

func (m *BatchSendPresentResp) GetTargetList() []*PresentTargetUserInfo {
	if m != nil {
		return m.TargetList
	}
	return nil
}

func (m *BatchSendPresentResp) GetItemInfo() *PresentSendItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

func init() {
	proto.RegisterType((*DrawPresentPicture)(nil), "DrawPresentPicture")
	proto.RegisterType((*PresentLine)(nil), "PresentLine")
	proto.RegisterType((*PresentPoint)(nil), "PresentPoint")
	proto.RegisterType((*PresentSendMsg)(nil), "PresentSendMsg")
	proto.RegisterType((*SendPresentReq)(nil), "SendPresentReq")
	proto.RegisterType((*SendPresentResp)(nil), "SendPresentResp")
	proto.RegisterType((*PresentTargetUserInfo)(nil), "PresentTargetUserInfo")
	proto.RegisterType((*PresentBatchTargetInfo)(nil), "PresentBatchTargetInfo")
	proto.RegisterType((*PresentSendItemInfo)(nil), "PresentSendItemInfo")
	proto.RegisterType((*PresentBatchInfoMsg)(nil), "PresentBatchInfoMsg")
	proto.RegisterType((*ServiceCtrlInfo)(nil), "ServiceCtrlInfo")
	proto.RegisterType((*BatchSendPresentReq)(nil), "BatchSendPresentReq")
	proto.RegisterType((*BatchSendPresentResp)(nil), "BatchSendPresentResp")
	proto.RegisterEnum("PresentSendSourceType", PresentSendSourceType_name, PresentSendSourceType_value)
	proto.RegisterEnum("PresentSourceType", PresentSourceType_name, PresentSourceType_value)
	proto.RegisterEnum("PresentSendType", PresentSendType_name, PresentSendType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PresentMiddlewareClient is the client API for PresentMiddleware service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PresentMiddlewareClient interface {
	SendPresent(ctx context.Context, in *SendPresentReq, opts ...grpc.CallOption) (*SendPresentResp, error)
	BatchSendPresent(ctx context.Context, in *BatchSendPresentReq, opts ...grpc.CallOption) (*BatchSendPresentResp, error)
}

type presentMiddlewareClient struct {
	cc *grpc.ClientConn
}

func NewPresentMiddlewareClient(cc *grpc.ClientConn) PresentMiddlewareClient {
	return &presentMiddlewareClient{cc}
}

func (c *presentMiddlewareClient) SendPresent(ctx context.Context, in *SendPresentReq, opts ...grpc.CallOption) (*SendPresentResp, error) {
	out := new(SendPresentResp)
	err := c.cc.Invoke(ctx, "/PresentMiddleware/SendPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentMiddlewareClient) BatchSendPresent(ctx context.Context, in *BatchSendPresentReq, opts ...grpc.CallOption) (*BatchSendPresentResp, error) {
	out := new(BatchSendPresentResp)
	err := c.cc.Invoke(ctx, "/PresentMiddleware/BatchSendPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PresentMiddlewareServer is the server API for PresentMiddleware service.
type PresentMiddlewareServer interface {
	SendPresent(context.Context, *SendPresentReq) (*SendPresentResp, error)
	BatchSendPresent(context.Context, *BatchSendPresentReq) (*BatchSendPresentResp, error)
}

func RegisterPresentMiddlewareServer(s *grpc.Server, srv PresentMiddlewareServer) {
	s.RegisterService(&_PresentMiddleware_serviceDesc, srv)
}

func _PresentMiddleware_SendPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentMiddlewareServer).SendPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PresentMiddleware/SendPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentMiddlewareServer).SendPresent(ctx, req.(*SendPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentMiddleware_BatchSendPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSendPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentMiddlewareServer).BatchSendPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PresentMiddleware/BatchSendPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentMiddlewareServer).BatchSendPresent(ctx, req.(*BatchSendPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PresentMiddleware_serviceDesc = grpc.ServiceDesc{
	ServiceName: "PresentMiddleware",
	HandlerType: (*PresentMiddlewareServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendPresent",
			Handler:    _PresentMiddleware_SendPresent_Handler,
		},
		{
			MethodName: "BatchSendPresent",
			Handler:    _PresentMiddleware_BatchSendPresent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/quicksilver/present-middleware/present-middleware.proto",
}

func init() {
	proto.RegisterFile("src/quicksilver/present-middleware/present-middleware.proto", fileDescriptor_present_middleware_f665d130f07824ea)
}

var fileDescriptor_present_middleware_f665d130f07824ea = []byte{
	// 1465 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x57, 0xcd, 0x6f, 0xdb, 0xc6,
	0x12, 0xb7, 0x28, 0x59, 0x1f, 0x23, 0xc9, 0xa6, 0xd7, 0x5f, 0x8a, 0x93, 0xbc, 0xe7, 0xa7, 0xbc,
	0xb6, 0xae, 0xd1, 0x2a, 0xa8, 0x73, 0x68, 0xd1, 0xa2, 0x28, 0x64, 0x59, 0x4e, 0xd8, 0xc8, 0x8e,
	0x41, 0xc9, 0x09, 0x7a, 0x22, 0x68, 0x72, 0xed, 0x6c, 0x23, 0x7e, 0x84, 0xa4, 0xec, 0xf8, 0xd8,
	0x5b, 0xd1, 0xbf, 0xa3, 0xa7, 0xde, 0x7b, 0xe9, 0x7f, 0xd4, 0x6b, 0x0f, 0xed, 0xb5, 0xd8, 0xd9,
	0x25, 0x4d, 0xd2, 0x92, 0xed, 0xa2, 0xc8, 0x8d, 0xfb, 0x9b, 0xd9, 0xd9, 0xd9, 0x99, 0xf9, 0xcd,
	0x2c, 0xe1, 0xab, 0x30, 0xb0, 0x1e, 0xbf, 0x9d, 0x30, 0xeb, 0x4d, 0xc8, 0xc6, 0xe7, 0x34, 0x78,
	0xec, 0x07, 0x34, 0xa4, 0x6e, 0xf4, 0xa9, 0xc3, 0x6c, 0x7b, 0x4c, 0x2f, 0xcc, 0x80, 0x4e, 0x81,
	0x3a, 0x7e, 0xe0, 0x45, 0x5e, 0xfb, 0x1b, 0x20, 0x7b, 0x81, 0x79, 0x71, 0x24, 0xe4, 0x47, 0xcc,
	0x8a, 0x26, 0x01, 0x25, 0x1f, 0x43, 0x6d, 0xcc, 0x5c, 0x6a, 0x8c, 0x59, 0x18, 0xb5, 0x0a, 0x9b,
	0xc5, 0xad, 0xfa, 0x4e, 0xa3, 0x23, 0x75, 0x06, 0xcc, 0xa5, 0x7a, 0x95, 0x8b, 0x07, 0x2c, 0x8c,
	0xda, 0x23, 0xa8, 0xa7, 0x04, 0x64, 0x1d, 0x2a, 0x2c, 0xa2, 0x8e, 0xc1, 0xec, 0x56, 0x61, 0xb3,
	0xb0, 0xd5, 0xd4, 0xcb, 0x7c, 0xa9, 0xd9, 0xe4, 0x13, 0x00, 0xdf, 0x63, 0x6e, 0x24, 0x6c, 0x2a,
	0x68, 0xb3, 0x19, 0xdb, 0x3c, 0xe2, 0x12, 0xbd, 0x86, 0x0a, 0x68, 0x75, 0x1b, 0x1a, 0x69, 0x11,
	0x69, 0x40, 0xe1, 0x1d, 0x1a, 0x54, 0xf4, 0xc2, 0x3b, 0xbe, 0xba, 0x6c, 0x29, 0x62, 0x75, 0xd9,
	0xfe, 0x43, 0x81, 0x05, 0xa9, 0x3c, 0xa4, 0xae, 0x7d, 0x10, 0x9e, 0x91, 0xcf, 0xa0, 0x26, 0xbc,
	0x70, 0x4f, 0x3d, 0xdc, 0x56, 0xdf, 0x59, 0xe9, 0xa4, 0x74, 0x34, 0xee, 0x93, 0x7b, 0xea, 0xe9,
	0x55, 0x26, 0xbf, 0xc8, 0x7d, 0xa8, 0x85, 0xd4, 0xb5, 0x8d, 0x88, 0x39, 0x14, 0x6d, 0x97, 0xf4,
	0x2a, 0x07, 0x46, 0xcc, 0xa1, 0xe4, 0x21, 0x80, 0xf5, 0xda, 0x74, 0x5d, 0x3a, 0xe6, 0x17, 0x2b,
	0xe2, 0xc5, 0x6a, 0x12, 0xd1, 0x6c, 0x72, 0x0f, 0x50, 0xd5, 0x98, 0x30, 0xbb, 0x55, 0x42, 0x61,
	0x85, 0xaf, 0x8f, 0x99, 0x4d, 0xfe, 0x07, 0x0d, 0x14, 0x99, 0x96, 0xe5, 0x4d, 0xdc, 0xa8, 0x35,
	0xbf, 0x59, 0xd8, 0xaa, 0xe9, 0x75, 0x8e, 0x75, 0x05, 0x44, 0x1e, 0x41, 0x13, 0x55, 0x5c, 0x66,
	0xbd, 0x71, 0x4d, 0x87, 0xb6, 0xca, 0xa8, 0x83, 0xfb, 0x0e, 0x25, 0xc6, 0x3d, 0x88, 0xcc, 0xe0,
	0x8c, 0x46, 0x78, 0x48, 0x45, 0x78, 0x20, 0x10, 0x7e, 0xcc, 0x07, 0xb0, 0x20, 0xc5, 0xf1, 0x41,
	0x55, 0x34, 0xd2, 0x14, 0x68, 0x7c, 0xd4, 0x47, 0xb0, 0x28, 0xd5, 0x92, 0xc3, 0x6a, 0xa8, 0x27,
	0x77, 0x27, 0xc7, 0xfd, 0x17, 0xea, 0xf4, 0x5d, 0xc4, 0xbd, 0xfa, 0x3e, 0xf4, 0xdc, 0x16, 0xa0,
	0x12, 0x08, 0xe8, 0xdb, 0xd0, 0x73, 0xdb, 0xbf, 0x15, 0x61, 0x81, 0x47, 0x52, 0x06, 0x55, 0xa7,
	0x6f, 0x73, 0x2e, 0x16, 0xf2, 0x2e, 0xa6, 0x2a, 0x43, 0xc9, 0x54, 0xc6, 0x2d, 0xc1, 0x5d, 0x81,
	0x79, 0x71, 0x23, 0x11, 0x59, 0xb1, 0xe0, 0x0e, 0x62, 0xd0, 0x42, 0x6f, 0x12, 0x58, 0x14, 0xc3,
	0xda, 0xd4, 0x81, 0x43, 0x43, 0x44, 0xb8, 0x02, 0x1e, 0x27, 0x15, 0xca, 0x42, 0x81, 0x43, 0x52,
	0x81, 0x27, 0x1c, 0xbf, 0x8c, 0x24, 0xa0, 0x55, 0x01, 0x68, 0xf6, 0x55, 0x35, 0x5c, 0xfa, 0x14,
	0x43, 0xd9, 0x94, 0xd5, 0x70, 0xe9, 0x53, 0xf2, 0x35, 0xa8, 0x76, 0x60, 0x5e, 0x18, 0x92, 0x54,
	0x86, 0xcf, 0x2c, 0x0c, 0x63, 0x7d, 0x67, 0xb9, 0x73, 0x9d, 0x4c, 0xfa, 0x82, 0x9d, 0xc1, 0xf8,
	0x85, 0x4c, 0xdf, 0xd7, 0x6c, 0x8c, 0x6a, 0x53, 0x17, 0x0b, 0xb2, 0x01, 0x55, 0xc7, 0x0c, 0xde,
	0xd0, 0x48, 0xb3, 0x5b, 0x75, 0x71, 0x60, 0xbc, 0xce, 0xd4, 0x57, 0x23, 0x5b, 0x5f, 0x4f, 0x78,
	0x7d, 0x05, 0xe7, 0x8c, 0x5f, 0x83, 0x17, 0x7b, 0x13, 0xfd, 0x50, 0x3b, 0x43, 0x01, 0xf6, 0xa2,
	0x60, 0x8c, 0x85, 0x5e, 0x97, 0x5a, 0x7c, 0xd1, 0xfe, 0x45, 0x81, 0xc5, 0x4c, 0xf2, 0x42, 0x7f,
	0x36, 0x71, 0xb7, 0xa1, 0xea, 0x84, 0x67, 0xc2, 0xba, 0x82, 0xd6, 0x17, 0x3b, 0x59, 0xba, 0xe9,
	0x15, 0x27, 0x3c, 0x43, 0x12, 0x7d, 0x09, 0xf7, 0x1c, 0xea, 0x9c, 0xd0, 0xc0, 0xb0, 0x3c, 0x37,
	0x0a, 0xd8, 0xc9, 0x24, 0x62, 0x9e, 0x6b, 0x98, 0xb6, 0x4d, 0xe3, 0xcc, 0xae, 0x0b, 0x85, 0x5e,
	0x4a, 0xde, 0xe5, 0xe2, 0x19, 0x79, 0xe6, 0xc5, 0x31, 0x09, 0x8c, 0xe8, 0x84, 0x9a, 0x6e, 0x88,
	0x69, 0x2e, 0xe9, 0x35, 0x6b, 0x12, 0x8c, 0x10, 0xf8, 0x97, 0x59, 0xe6, 0xcc, 0x13, 0xc2, 0x80,
	0x3a, 0x26, 0x73, 0x65, 0xa6, 0x1b, 0x02, 0xd4, 0x11, 0x6b, 0xbf, 0x82, 0x55, 0x79, 0xdd, 0x91,
	0xa8, 0xe5, 0x90, 0x06, 0x78, 0x59, 0x15, 0x8a, 0x57, 0x85, 0xce, 0x3f, 0x49, 0x0b, 0x2a, 0x31,
	0xfd, 0x14, 0x64, 0x4c, 0xbc, 0x24, 0x04, 0x4a, 0xc8, 0xb6, 0x22, 0xc2, 0xf8, 0xdd, 0xfe, 0xa1,
	0x00, 0x6b, 0xd2, 0xf2, 0xae, 0x19, 0x59, 0xaf, 0x85, 0xf9, 0x7f, 0x6c, 0x7a, 0x03, 0xaa, 0x09,
	0x99, 0x85, 0xf9, 0x64, 0x9d, 0xa7, 0x71, 0xe9, 0x1a, 0x8d, 0xff, 0x52, 0x60, 0x79, 0x4a, 0x5f,
	0x9c, 0x5d, 0x0d, 0x49, 0x96, 0x94, 0x3c, 0x1b, 0x5f, 0x7b, 0x17, 0x06, 0x3d, 0x3d, 0xa5, 0x56,
	0x24, 0x33, 0x0d, 0x1c, 0xea, 0x23, 0x42, 0xfe, 0x0f, 0x0b, 0x29, 0x05, 0xe3, 0x7c, 0x47, 0x66,
	0xb9, 0x71, 0xa5, 0xf3, 0x72, 0x87, 0x9f, 0x7a, 0x3a, 0xf6, 0x2e, 0xf8, 0xa9, 0x82, 0xd0, 0x65,
	0xbe, 0x14, 0x04, 0x60, 0xa1, 0x71, 0xc2, 0xa3, 0x84, 0x39, 0xae, 0xea, 0x15, 0x16, 0x62, 0xd0,
	0xc8, 0x36, 0x2c, 0xa1, 0x65, 0x14, 0xc6, 0x0e, 0x54, 0x50, 0x67, 0x91, 0x0b, 0x50, 0x4b, 0x7a,
	0xf1, 0x3e, 0x59, 0xdd, 0x81, 0x65, 0xfb, 0xd2, 0x35, 0x1d, 0x66, 0x19, 0x11, 0x75, 0xfc, 0xb1,
	0x19, 0x61, 0xc9, 0x09, 0x8e, 0x2f, 0x49, 0xd1, 0x48, 0x4a, 0x34, 0xbb, 0xfd, 0x73, 0x31, 0x89,
	0x3c, 0xba, 0xc8, 0xc3, 0xce, 0x47, 0xd7, 0xcc, 0xc8, 0x6f, 0x81, 0x1a, 0x79, 0x91, 0x39, 0x36,
	0x50, 0x9c, 0x4e, 0xc2, 0x02, 0xe2, 0x3c, 0x77, 0xbd, 0x98, 0x33, 0x22, 0x1a, 0x78, 0x4f, 0xd9,
	0x50, 0x11, 0xc1, 0x8b, 0x66, 0x26, 0x5d, 0xe9, 0xc6, 0x49, 0x37, 0x7f, 0xd3, 0xa4, 0x2b, 0xdf,
	0x3c, 0xe9, 0x2a, 0x77, 0x98, 0x74, 0xd5, 0x29, 0x93, 0x2e, 0x57, 0xb3, 0xb5, 0x7c, 0xcd, 0x92,
	0x2f, 0xa0, 0x2e, 0xe7, 0x0c, 0x3e, 0x25, 0x00, 0x9f, 0x12, 0xeb, 0x9d, 0xe9, 0x54, 0xd2, 0xe5,
	0x4c, 0xe2, 0xaf, 0x8a, 0xec, 0xb3, 0xa0, 0x7e, 0x97, 0x67, 0x41, 0xfb, 0xcf, 0x02, 0x6f, 0x95,
	0x99, 0x5e, 0xca, 0x03, 0x68, 0x8d, 0x19, 0xaf, 0x11, 0xe6, 0x63, 0x92, 0x6a, 0x7a, 0x55, 0x00,
	0x9a, 0xcf, 0xdd, 0x97, 0x42, 0xdf, 0x0b, 0xe2, 0x0c, 0x81, 0x80, 0x8e, 0xbc, 0x00, 0x8b, 0xd0,
	0xa6, 0xa2, 0x61, 0xdb, 0x31, 0x61, 0x05, 0xa0, 0xd9, 0xa9, 0xdd, 0x98, 0xbb, 0x52, 0x7a, 0x37,
	0x26, 0xef, 0x11, 0x34, 0x23, 0x1a, 0x38, 0xcc, 0x35, 0xc7, 0x42, 0x45, 0xa4, 0xa8, 0x11, 0x83,
	0x71, 0x86, 0x63, 0x07, 0xe3, 0x34, 0xc5, 0x0e, 0xe2, 0x53, 0x41, 0x0a, 0xcf, 0x69, 0x10, 0x32,
	0xcf, 0x95, 0x6d, 0xb1, 0x29, 0xd0, 0x97, 0x02, 0x6c, 0xff, 0x5e, 0x84, 0x65, 0x8c, 0x65, 0x6e,
	0xca, 0xcf, 0xac, 0xcf, 0x6c, 0xe5, 0x28, 0x33, 0xc7, 0x78, 0xf1, 0x86, 0x31, 0x5e, 0xba, 0x6d,
	0x8c, 0xcf, 0xdf, 0xdc, 0xe0, 0xcb, 0xb9, 0x06, 0x9f, 0x65, 0x42, 0x65, 0x26, 0x13, 0xde, 0x43,
	0x3f, 0x58, 0x85, 0xb2, 0xe9, 0xfb, 0x57, 0x2d, 0x40, 0x8e, 0xf9, 0xfb, 0x50, 0x13, 0x63, 0x9d,
	0x4b, 0xf2, 0x73, 0xfe, 0xc3, 0xe4, 0x79, 0x36, 0x61, 0xb6, 0xa8, 0xee, 0xc6, 0x66, 0x91, 0xe7,
	0x26, 0x79, 0x46, 0x61, 0x1d, 0xa7, 0x59, 0xd8, 0xbc, 0xf9, 0x3d, 0xb0, 0x70, 0x97, 0xf7, 0xc0,
	0xaf, 0x0a, 0xac, 0x5c, 0xcf, 0x75, 0xe8, 0x93, 0xc7, 0xa9, 0xd9, 0x9f, 0x7b, 0x46, 0xa7, 0x9b,
	0xd6, 0xd5, 0x03, 0x20, 0x3b, 0xae, 0x95, 0x5b, 0xc6, 0x75, 0xf1, 0xe6, 0x6c, 0x96, 0x6e, 0x1b,
	0xd7, 0xf3, 0xd7, 0xc7, 0x35, 0xf9, 0x3c, 0xdb, 0x1d, 0xca, 0xd8, 0x1d, 0xd6, 0x3a, 0x53, 0x47,
	0xf8, 0xec, 0xe6, 0x50, 0xb9, 0x4b, 0x73, 0xd8, 0xfe, 0x51, 0x49, 0xde, 0x06, 0xc3, 0xa4, 0x64,
	0xb1, 0x78, 0x36, 0x60, 0xad, 0x6f, 0x0c, 0xfb, 0x87, 0x7b, 0xc6, 0xf0, 0xc5, 0xb1, 0xde, 0xeb,
	0x1b, 0x7b, 0xfd, 0xfd, 0x41, 0xf7, 0x78, 0xd4, 0x57, 0xe7, 0xc8, 0x26, 0x3c, 0xc8, 0xca, 0x9e,
	0x6a, 0xfb, 0x23, 0x63, 0x74, 0xac, 0x1f, 0x8e, 0xba, 0xbb, 0x83, 0xbe, 0x5a, 0x20, 0x0f, 0xa0,
	0x35, 0x45, 0x63, 0xf8, 0xac, 0x3f, 0xd8, 0x57, 0x15, 0xf2, 0x10, 0xee, 0x65, 0xa5, 0xc3, 0xa3,
	0x7e, 0xbf, 0xf7, 0xcc, 0xd8, 0xed, 0x0e, 0x06, 0x6a, 0x91, 0xdc, 0x87, 0xf5, 0xdc, 0xd1, 0x7a,
	0xf7, 0x15, 0x5a, 0x50, 0x4b, 0xd7, 0xf7, 0x1e, 0x74, 0x87, 0xcf, 0xfb, 0x7b, 0x46, 0x8f, 0xef,
	0x9d, 0x27, 0x2b, 0xa0, 0x66, 0xc5, 0xda, 0x81, 0x5a, 0x26, 0x6d, 0xf8, 0x4f, 0x16, 0x7d, 0xb1,
	0xbf, 0xaf, 0xf5, 0xb4, 0xee, 0xc0, 0xe8, 0x3d, 0xeb, 0x1e, 0x1e, 0xf6, 0x07, 0x6a, 0x65, 0x9b,
	0xc1, 0x52, 0x1c, 0x89, 0xab, 0x28, 0xac, 0x01, 0x39, 0xd2, 0xfb, 0xc3, 0xfe, 0xe1, 0x28, 0xde,
	0xb9, 0x7b, 0xfc, 0x9d, 0x3a, 0xc7, 0xa3, 0x93, 0xc3, 0x8f, 0xba, 0xbd, 0xe7, 0xdd, 0xa7, 0xfc,
	0xee, 0x9b, 0xf0, 0x60, 0xba, 0xcc, 0xd8, 0xd7, 0xf4, 0xe1, 0x48, 0x55, 0xb6, 0xbb, 0xb0, 0x98,
	0x0a, 0x3a, 0x1e, 0xb4, 0x0e, 0xcb, 0xc9, 0x26, 0xee, 0xe7, 0xe1, 0x0b, 0xfd, 0xa0, 0x3b, 0x50,
	0xe7, 0xc8, 0x2a, 0x2c, 0x65, 0x04, 0x3c, 0x16, 0x6a, 0x61, 0xe7, 0xa7, 0x42, 0xe2, 0xee, 0x41,
	0xf2, 0x47, 0x4c, 0x76, 0xa0, 0x9e, 0x22, 0x00, 0x59, 0xec, 0x64, 0x5b, 0xdf, 0x86, 0xda, 0xc9,
	0xf1, 0xa3, 0x3d, 0x47, 0xba, 0xa0, 0xe6, 0x99, 0x43, 0x56, 0x3a, 0x53, 0x1a, 0xe7, 0xc6, 0x6a,
	0x67, 0x1a, 0xc5, 0xda, 0x73, 0x27, 0x65, 0xfc, 0x13, 0x7f, 0xf2, 0x77, 0x00, 0x00, 0x00, 0xff,
	0xff, 0x7d, 0x21, 0xa3, 0xbe, 0xc8, 0x0f, 0x00, 0x00,
}
