// Code generated by protoc-gen-gogo.
// source: services/userrecommend/userrecommend.proto
// DO NOT EDIT!

/*
	Package userrecommend is a generated protocol buffer package.

	It is generated from these files:
		services/userrecommend/userrecommend.proto

	It has these top-level messages:
		RecommendFromContacts
		AddOrUpdateFullContactsReq
		AddOrUpdateFullContactsResp
		AddIncrementContactsReq
		AddIncrementContactsResp
		DeleteIncrementContactsReq
		DeleteIncrementContactsResp
		SetRejectRecommendReq
		SetRejectRecommendResp
		ChangeRecommendStatusReq
		ChangeRecommendStatusResp
		GetRecommendStatusReq
		GetRecommendStatusResp
		GetRecommendFromContactsReq
		GetRecommendFromContactsResp
		GetNoFriendRecommendReq
		GetNoFriendRecommendResp
		AddFullReportGameReq
		AddFullReportGameResp
		UpdateIncreReportGameReq
		UpdateIncreReportGameResp
		RecommendFromAll
		GetRecommendFromAllReq
		GetRecommendFromAllResp
		GetUserInstallGameReq
		GetUserInstallGameResp
		UpdateUserRecommendAsync
*/
package userrecommend

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type RecommendFromAll_RecommendType int32

const (
	RecommendFromAll_RECOMMEND_FROM_CONTACTS RecommendFromAll_RecommendType = 1
	RecommendFromAll_RECOMMEND_FROM_GAME     RecommendFromAll_RecommendType = 2
)

var RecommendFromAll_RecommendType_name = map[int32]string{
	1: "RECOMMEND_FROM_CONTACTS",
	2: "RECOMMEND_FROM_GAME",
}
var RecommendFromAll_RecommendType_value = map[string]int32{
	"RECOMMEND_FROM_CONTACTS": 1,
	"RECOMMEND_FROM_GAME":     2,
}

func (x RecommendFromAll_RecommendType) Enum() *RecommendFromAll_RecommendType {
	p := new(RecommendFromAll_RecommendType)
	*p = x
	return p
}
func (x RecommendFromAll_RecommendType) String() string {
	return proto.EnumName(RecommendFromAll_RecommendType_name, int32(x))
}
func (x *RecommendFromAll_RecommendType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RecommendFromAll_RecommendType_value, data, "RecommendFromAll_RecommendType")
	if err != nil {
		return err
	}
	*x = RecommendFromAll_RecommendType(value)
	return nil
}
func (RecommendFromAll_RecommendType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{21, 0}
}

type RecommendFromContacts struct {
	Phone    string `protobuf:"bytes,1,req,name=phone" json:"phone"`
	PhoneUid uint32 `protobuf:"varint,2,req,name=phone_uid,json=phoneUid" json:"phone_uid"`
}

func (m *RecommendFromContacts) Reset()         { *m = RecommendFromContacts{} }
func (m *RecommendFromContacts) String() string { return proto.CompactTextString(m) }
func (*RecommendFromContacts) ProtoMessage()    {}
func (*RecommendFromContacts) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{0}
}

func (m *RecommendFromContacts) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *RecommendFromContacts) GetPhoneUid() uint32 {
	if m != nil {
		return m.PhoneUid
	}
	return 0
}

// 全量数据
type AddOrUpdateFullContactsReq struct {
	Uid            uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	PhoneList      []string `protobuf:"bytes,2,rep,name=phone_list,json=phoneList" json:"phone_list,omitempty"`
	ContactVersion uint32   `protobuf:"varint,3,req,name=contact_version,json=contactVersion" json:"contact_version"`
}

func (m *AddOrUpdateFullContactsReq) Reset()         { *m = AddOrUpdateFullContactsReq{} }
func (m *AddOrUpdateFullContactsReq) String() string { return proto.CompactTextString(m) }
func (*AddOrUpdateFullContactsReq) ProtoMessage()    {}
func (*AddOrUpdateFullContactsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{1}
}

func (m *AddOrUpdateFullContactsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddOrUpdateFullContactsReq) GetPhoneList() []string {
	if m != nil {
		return m.PhoneList
	}
	return nil
}

func (m *AddOrUpdateFullContactsReq) GetContactVersion() uint32 {
	if m != nil {
		return m.ContactVersion
	}
	return 0
}

type AddOrUpdateFullContactsResp struct {
}

func (m *AddOrUpdateFullContactsResp) Reset()         { *m = AddOrUpdateFullContactsResp{} }
func (m *AddOrUpdateFullContactsResp) String() string { return proto.CompactTextString(m) }
func (*AddOrUpdateFullContactsResp) ProtoMessage()    {}
func (*AddOrUpdateFullContactsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{2}
}

// 增加增量数据
type AddIncrementContactsReq struct {
	Uid       uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	PhoneList []string `protobuf:"bytes,2,rep,name=phone_list,json=phoneList" json:"phone_list,omitempty"`
}

func (m *AddIncrementContactsReq) Reset()         { *m = AddIncrementContactsReq{} }
func (m *AddIncrementContactsReq) String() string { return proto.CompactTextString(m) }
func (*AddIncrementContactsReq) ProtoMessage()    {}
func (*AddIncrementContactsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{3}
}

func (m *AddIncrementContactsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddIncrementContactsReq) GetPhoneList() []string {
	if m != nil {
		return m.PhoneList
	}
	return nil
}

type AddIncrementContactsResp struct {
}

func (m *AddIncrementContactsResp) Reset()         { *m = AddIncrementContactsResp{} }
func (m *AddIncrementContactsResp) String() string { return proto.CompactTextString(m) }
func (*AddIncrementContactsResp) ProtoMessage()    {}
func (*AddIncrementContactsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{4}
}

// 删除增量数据
type DeleteIncrementContactsReq struct {
	Uid       uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	PhoneList []string `protobuf:"bytes,2,rep,name=phone_list,json=phoneList" json:"phone_list,omitempty"`
}

func (m *DeleteIncrementContactsReq) Reset()         { *m = DeleteIncrementContactsReq{} }
func (m *DeleteIncrementContactsReq) String() string { return proto.CompactTextString(m) }
func (*DeleteIncrementContactsReq) ProtoMessage()    {}
func (*DeleteIncrementContactsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{5}
}

func (m *DeleteIncrementContactsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeleteIncrementContactsReq) GetPhoneList() []string {
	if m != nil {
		return m.PhoneList
	}
	return nil
}

type DeleteIncrementContactsResp struct {
}

func (m *DeleteIncrementContactsResp) Reset()         { *m = DeleteIncrementContactsResp{} }
func (m *DeleteIncrementContactsResp) String() string { return proto.CompactTextString(m) }
func (*DeleteIncrementContactsResp) ProtoMessage()    {}
func (*DeleteIncrementContactsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{6}
}

// 不再推荐别人
type SetRejectRecommendReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Phone      string `protobuf:"bytes,2,req,name=phone" json:"phone"`
	BRecommend uint32 `protobuf:"varint,3,req,name=bRecommend" json:"bRecommend"`
}

func (m *SetRejectRecommendReq) Reset()         { *m = SetRejectRecommendReq{} }
func (m *SetRejectRecommendReq) String() string { return proto.CompactTextString(m) }
func (*SetRejectRecommendReq) ProtoMessage()    {}
func (*SetRejectRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{7}
}

func (m *SetRejectRecommendReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetRejectRecommendReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *SetRejectRecommendReq) GetBRecommend() uint32 {
	if m != nil {
		return m.BRecommend
	}
	return 0
}

type SetRejectRecommendResp struct {
}

func (m *SetRejectRecommendResp) Reset()         { *m = SetRejectRecommendResp{} }
func (m *SetRejectRecommendResp) String() string { return proto.CompactTextString(m) }
func (*SetRejectRecommendResp) ProtoMessage()    {}
func (*SetRejectRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{8}
}

// 改变推荐状态（是否向别人推荐自己）
type ChangeRecommendStatusReq struct {
	Uid    uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Status uint32 `protobuf:"varint,2,req,name=status" json:"status"`
}

func (m *ChangeRecommendStatusReq) Reset()         { *m = ChangeRecommendStatusReq{} }
func (m *ChangeRecommendStatusReq) String() string { return proto.CompactTextString(m) }
func (*ChangeRecommendStatusReq) ProtoMessage()    {}
func (*ChangeRecommendStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{9}
}

func (m *ChangeRecommendStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChangeRecommendStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type ChangeRecommendStatusResp struct {
}

func (m *ChangeRecommendStatusResp) Reset()         { *m = ChangeRecommendStatusResp{} }
func (m *ChangeRecommendStatusResp) String() string { return proto.CompactTextString(m) }
func (*ChangeRecommendStatusResp) ProtoMessage()    {}
func (*ChangeRecommendStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{10}
}

// 获取推荐状态
type GetRecommendStatusReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetRecommendStatusReq) Reset()         { *m = GetRecommendStatusReq{} }
func (m *GetRecommendStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendStatusReq) ProtoMessage()    {}
func (*GetRecommendStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{11}
}

func (m *GetRecommendStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRecommendStatusResp struct {
	Status uint32 `protobuf:"varint,2,req,name=status" json:"status"`
}

func (m *GetRecommendStatusResp) Reset()         { *m = GetRecommendStatusResp{} }
func (m *GetRecommendStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendStatusResp) ProtoMessage()    {}
func (*GetRecommendStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{12}
}

func (m *GetRecommendStatusResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// 推荐已经在TT上注册
type GetRecommendFromContactsReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetRecommendFromContactsReq) Reset()         { *m = GetRecommendFromContactsReq{} }
func (m *GetRecommendFromContactsReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendFromContactsReq) ProtoMessage()    {}
func (*GetRecommendFromContactsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{13}
}

func (m *GetRecommendFromContactsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRecommendFromContactsResp struct {
	RecommendList  []*RecommendFromContacts `protobuf:"bytes,1,rep,name=recommend_list,json=recommendList" json:"recommend_list,omitempty"`
	ContactVersion uint32                   `protobuf:"varint,2,opt,name=contact_version,json=contactVersion" json:"contact_version"`
}

func (m *GetRecommendFromContactsResp) Reset()         { *m = GetRecommendFromContactsResp{} }
func (m *GetRecommendFromContactsResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendFromContactsResp) ProtoMessage()    {}
func (*GetRecommendFromContactsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{14}
}

func (m *GetRecommendFromContactsResp) GetRecommendList() []*RecommendFromContacts {
	if m != nil {
		return m.RecommendList
	}
	return nil
}

func (m *GetRecommendFromContactsResp) GetContactVersion() uint32 {
	if m != nil {
		return m.ContactVersion
	}
	return 0
}

// 推荐不是好友的用户
type GetNoFriendRecommendReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetNoFriendRecommendReq) Reset()         { *m = GetNoFriendRecommendReq{} }
func (m *GetNoFriendRecommendReq) String() string { return proto.CompactTextString(m) }
func (*GetNoFriendRecommendReq) ProtoMessage()    {}
func (*GetNoFriendRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{15}
}

func (m *GetNoFriendRecommendReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetNoFriendRecommendResp struct {
	RecommendList  []*RecommendFromContacts `protobuf:"bytes,1,rep,name=recommend_list,json=recommendList" json:"recommend_list,omitempty"`
	ContactVersion uint32                   `protobuf:"varint,2,opt,name=contact_version,json=contactVersion" json:"contact_version"`
}

func (m *GetNoFriendRecommendResp) Reset()         { *m = GetNoFriendRecommendResp{} }
func (m *GetNoFriendRecommendResp) String() string { return proto.CompactTextString(m) }
func (*GetNoFriendRecommendResp) ProtoMessage()    {}
func (*GetNoFriendRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{16}
}

func (m *GetNoFriendRecommendResp) GetRecommendList() []*RecommendFromContacts {
	if m != nil {
		return m.RecommendList
	}
	return nil
}

func (m *GetNoFriendRecommendResp) GetContactVersion() uint32 {
	if m != nil {
		return m.ContactVersion
	}
	return 0
}

// 全量添加
type AddFullReportGameReq struct {
	Uid        uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	GameIdList []uint32 `protobuf:"varint,2,rep,name=game_id_list,json=gameIdList" json:"game_id_list,omitempty"`
}

func (m *AddFullReportGameReq) Reset()         { *m = AddFullReportGameReq{} }
func (m *AddFullReportGameReq) String() string { return proto.CompactTextString(m) }
func (*AddFullReportGameReq) ProtoMessage()    {}
func (*AddFullReportGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{17}
}

func (m *AddFullReportGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddFullReportGameReq) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

type AddFullReportGameResp struct {
}

func (m *AddFullReportGameResp) Reset()         { *m = AddFullReportGameResp{} }
func (m *AddFullReportGameResp) String() string { return proto.CompactTextString(m) }
func (*AddFullReportGameResp) ProtoMessage()    {}
func (*AddFullReportGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{18}
}

// 增量更新
type UpdateIncreReportGameReq struct {
	Uid              uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	AddGameIdList    []uint32 `protobuf:"varint,2,rep,name=add_game_id_list,json=addGameIdList" json:"add_game_id_list,omitempty"`
	DeleteGameIdList []uint32 `protobuf:"varint,3,rep,name=delete_game_id_list,json=deleteGameIdList" json:"delete_game_id_list,omitempty"`
}

func (m *UpdateIncreReportGameReq) Reset()         { *m = UpdateIncreReportGameReq{} }
func (m *UpdateIncreReportGameReq) String() string { return proto.CompactTextString(m) }
func (*UpdateIncreReportGameReq) ProtoMessage()    {}
func (*UpdateIncreReportGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{19}
}

func (m *UpdateIncreReportGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateIncreReportGameReq) GetAddGameIdList() []uint32 {
	if m != nil {
		return m.AddGameIdList
	}
	return nil
}

func (m *UpdateIncreReportGameReq) GetDeleteGameIdList() []uint32 {
	if m != nil {
		return m.DeleteGameIdList
	}
	return nil
}

type UpdateIncreReportGameResp struct {
}

func (m *UpdateIncreReportGameResp) Reset()         { *m = UpdateIncreReportGameResp{} }
func (m *UpdateIncreReportGameResp) String() string { return proto.CompactTextString(m) }
func (*UpdateIncreReportGameResp) ProtoMessage()    {}
func (*UpdateIncreReportGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{20}
}

// 推荐全部信息
type RecommendFromAll struct {
	RecommendType uint32 `protobuf:"varint,1,req,name=recommend_type,json=recommendType" json:"recommend_type"`
	Phone         string `protobuf:"bytes,2,opt,name=phone" json:"phone"`
	PhoneUid      uint32 `protobuf:"varint,3,opt,name=phone_uid,json=phoneUid" json:"phone_uid"`
	GameId        uint32 `protobuf:"varint,4,opt,name=game_id,json=gameId" json:"game_id"`
	GameUid       uint32 `protobuf:"varint,5,opt,name=game_uid,json=gameUid" json:"game_uid"`
}

func (m *RecommendFromAll) Reset()                    { *m = RecommendFromAll{} }
func (m *RecommendFromAll) String() string            { return proto.CompactTextString(m) }
func (*RecommendFromAll) ProtoMessage()               {}
func (*RecommendFromAll) Descriptor() ([]byte, []int) { return fileDescriptorUserrecommend, []int{21} }

func (m *RecommendFromAll) GetRecommendType() uint32 {
	if m != nil {
		return m.RecommendType
	}
	return 0
}

func (m *RecommendFromAll) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *RecommendFromAll) GetPhoneUid() uint32 {
	if m != nil {
		return m.PhoneUid
	}
	return 0
}

func (m *RecommendFromAll) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *RecommendFromAll) GetGameUid() uint32 {
	if m != nil {
		return m.GameUid
	}
	return 0
}

// 获取全部推荐类型用户
type GetRecommendFromAllReq struct {
	Uid          uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	Count        uint32   `protobuf:"varint,2,req,name=count" json:"count"`
	ExUidList    []uint32 `protobuf:"varint,3,rep,name=ex_uid_list,json=exUidList" json:"ex_uid_list,omitempty"`
	PriRecommend uint32   `protobuf:"varint,4,opt,name=priRecommend" json:"priRecommend"`
	Sex          uint32   `protobuf:"varint,5,opt,name=sex" json:"sex"`
}

func (m *GetRecommendFromAllReq) Reset()         { *m = GetRecommendFromAllReq{} }
func (m *GetRecommendFromAllReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendFromAllReq) ProtoMessage()    {}
func (*GetRecommendFromAllReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{22}
}

func (m *GetRecommendFromAllReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecommendFromAllReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetRecommendFromAllReq) GetExUidList() []uint32 {
	if m != nil {
		return m.ExUidList
	}
	return nil
}

func (m *GetRecommendFromAllReq) GetPriRecommend() uint32 {
	if m != nil {
		return m.PriRecommend
	}
	return 0
}

func (m *GetRecommendFromAllReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type GetRecommendFromAllResp struct {
	RecommendAllList []*RecommendFromAll `protobuf:"bytes,1,rep,name=recommend_all_list,json=recommendAllList" json:"recommend_all_list,omitempty"`
	ContactVersion   uint32              `protobuf:"varint,2,opt,name=contact_version,json=contactVersion" json:"contact_version"`
}

func (m *GetRecommendFromAllResp) Reset()         { *m = GetRecommendFromAllResp{} }
func (m *GetRecommendFromAllResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendFromAllResp) ProtoMessage()    {}
func (*GetRecommendFromAllResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{23}
}

func (m *GetRecommendFromAllResp) GetRecommendAllList() []*RecommendFromAll {
	if m != nil {
		return m.RecommendAllList
	}
	return nil
}

func (m *GetRecommendFromAllResp) GetContactVersion() uint32 {
	if m != nil {
		return m.ContactVersion
	}
	return 0
}

// 获取在指定游戏集合中用户安装的游戏
type GetUserInstallGameReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserInstallGameReq) Reset()         { *m = GetUserInstallGameReq{} }
func (m *GetUserInstallGameReq) String() string { return proto.CompactTextString(m) }
func (*GetUserInstallGameReq) ProtoMessage()    {}
func (*GetUserInstallGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{24}
}

func (m *GetUserInstallGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserInstallGameResp struct {
	GameIdList []uint32 `protobuf:"varint,1,rep,name=game_id_list,json=gameIdList" json:"game_id_list,omitempty"`
}

func (m *GetUserInstallGameResp) Reset()         { *m = GetUserInstallGameResp{} }
func (m *GetUserInstallGameResp) String() string { return proto.CompactTextString(m) }
func (*GetUserInstallGameResp) ProtoMessage()    {}
func (*GetUserInstallGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{25}
}

func (m *GetUserInstallGameResp) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

// 异步队列
type UpdateUserRecommendAsync struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IsFullReport bool   `protobuf:"varint,2,req,name=is_full_report,json=isFullReport" json:"is_full_report"`
}

func (m *UpdateUserRecommendAsync) Reset()         { *m = UpdateUserRecommendAsync{} }
func (m *UpdateUserRecommendAsync) String() string { return proto.CompactTextString(m) }
func (*UpdateUserRecommendAsync) ProtoMessage()    {}
func (*UpdateUserRecommendAsync) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend, []int{26}
}

func (m *UpdateUserRecommendAsync) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserRecommendAsync) GetIsFullReport() bool {
	if m != nil {
		return m.IsFullReport
	}
	return false
}

func init() {
	proto.RegisterType((*RecommendFromContacts)(nil), "userrecommend.RecommendFromContacts")
	proto.RegisterType((*AddOrUpdateFullContactsReq)(nil), "userrecommend.AddOrUpdateFullContactsReq")
	proto.RegisterType((*AddOrUpdateFullContactsResp)(nil), "userrecommend.AddOrUpdateFullContactsResp")
	proto.RegisterType((*AddIncrementContactsReq)(nil), "userrecommend.AddIncrementContactsReq")
	proto.RegisterType((*AddIncrementContactsResp)(nil), "userrecommend.AddIncrementContactsResp")
	proto.RegisterType((*DeleteIncrementContactsReq)(nil), "userrecommend.DeleteIncrementContactsReq")
	proto.RegisterType((*DeleteIncrementContactsResp)(nil), "userrecommend.DeleteIncrementContactsResp")
	proto.RegisterType((*SetRejectRecommendReq)(nil), "userrecommend.SetRejectRecommendReq")
	proto.RegisterType((*SetRejectRecommendResp)(nil), "userrecommend.SetRejectRecommendResp")
	proto.RegisterType((*ChangeRecommendStatusReq)(nil), "userrecommend.ChangeRecommendStatusReq")
	proto.RegisterType((*ChangeRecommendStatusResp)(nil), "userrecommend.ChangeRecommendStatusResp")
	proto.RegisterType((*GetRecommendStatusReq)(nil), "userrecommend.GetRecommendStatusReq")
	proto.RegisterType((*GetRecommendStatusResp)(nil), "userrecommend.GetRecommendStatusResp")
	proto.RegisterType((*GetRecommendFromContactsReq)(nil), "userrecommend.GetRecommendFromContactsReq")
	proto.RegisterType((*GetRecommendFromContactsResp)(nil), "userrecommend.GetRecommendFromContactsResp")
	proto.RegisterType((*GetNoFriendRecommendReq)(nil), "userrecommend.GetNoFriendRecommendReq")
	proto.RegisterType((*GetNoFriendRecommendResp)(nil), "userrecommend.GetNoFriendRecommendResp")
	proto.RegisterType((*AddFullReportGameReq)(nil), "userrecommend.AddFullReportGameReq")
	proto.RegisterType((*AddFullReportGameResp)(nil), "userrecommend.AddFullReportGameResp")
	proto.RegisterType((*UpdateIncreReportGameReq)(nil), "userrecommend.UpdateIncreReportGameReq")
	proto.RegisterType((*UpdateIncreReportGameResp)(nil), "userrecommend.UpdateIncreReportGameResp")
	proto.RegisterType((*RecommendFromAll)(nil), "userrecommend.RecommendFromAll")
	proto.RegisterType((*GetRecommendFromAllReq)(nil), "userrecommend.GetRecommendFromAllReq")
	proto.RegisterType((*GetRecommendFromAllResp)(nil), "userrecommend.GetRecommendFromAllResp")
	proto.RegisterType((*GetUserInstallGameReq)(nil), "userrecommend.GetUserInstallGameReq")
	proto.RegisterType((*GetUserInstallGameResp)(nil), "userrecommend.GetUserInstallGameResp")
	proto.RegisterType((*UpdateUserRecommendAsync)(nil), "userrecommend.UpdateUserRecommendAsync")
	proto.RegisterEnum("userrecommend.RecommendFromAll_RecommendType", RecommendFromAll_RecommendType_name, RecommendFromAll_RecommendType_value)
}
func (m *RecommendFromContacts) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecommendFromContacts) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.PhoneUid))
	return i, nil
}

func (m *AddOrUpdateFullContactsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddOrUpdateFullContactsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Uid))
	if len(m.PhoneList) > 0 {
		for _, s := range m.PhoneList {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.ContactVersion))
	return i, nil
}

func (m *AddOrUpdateFullContactsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddOrUpdateFullContactsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *AddIncrementContactsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddIncrementContactsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Uid))
	if len(m.PhoneList) > 0 {
		for _, s := range m.PhoneList {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *AddIncrementContactsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddIncrementContactsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DeleteIncrementContactsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteIncrementContactsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Uid))
	if len(m.PhoneList) > 0 {
		for _, s := range m.PhoneList {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *DeleteIncrementContactsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteIncrementContactsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SetRejectRecommendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetRejectRecommendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.BRecommend))
	return i, nil
}

func (m *SetRejectRecommendResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetRejectRecommendResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ChangeRecommendStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangeRecommendStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *ChangeRecommendStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangeRecommendStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetRecommendStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetRecommendStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *GetRecommendFromContactsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendFromContactsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetRecommendFromContactsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendFromContactsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RecommendList) > 0 {
		for _, msg := range m.RecommendList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUserrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.ContactVersion))
	return i, nil
}

func (m *GetNoFriendRecommendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNoFriendRecommendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetNoFriendRecommendResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNoFriendRecommendResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RecommendList) > 0 {
		for _, msg := range m.RecommendList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUserrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.ContactVersion))
	return i, nil
}

func (m *AddFullReportGameReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFullReportGameReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Uid))
	if len(m.GameIdList) > 0 {
		for _, num := range m.GameIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintUserrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *AddFullReportGameResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFullReportGameResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UpdateIncreReportGameReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateIncreReportGameReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Uid))
	if len(m.AddGameIdList) > 0 {
		for _, num := range m.AddGameIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintUserrecommend(dAtA, i, uint64(num))
		}
	}
	if len(m.DeleteGameIdList) > 0 {
		for _, num := range m.DeleteGameIdList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintUserrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *UpdateIncreReportGameResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateIncreReportGameResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RecommendFromAll) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecommendFromAll) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.RecommendType))
	dAtA[i] = 0x12
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.PhoneUid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.GameUid))
	return i, nil
}

func (m *GetRecommendFromAllReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendFromAllReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Count))
	if len(m.ExUidList) > 0 {
		for _, num := range m.ExUidList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintUserrecommend(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.PriRecommend))
	dAtA[i] = 0x28
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Sex))
	return i, nil
}

func (m *GetRecommendFromAllResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendFromAllResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RecommendAllList) > 0 {
		for _, msg := range m.RecommendAllList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUserrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.ContactVersion))
	return i, nil
}

func (m *GetUserInstallGameReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserInstallGameReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserInstallGameResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserInstallGameResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GameIdList) > 0 {
		for _, num := range m.GameIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintUserrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *UpdateUserRecommendAsync) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserRecommendAsync) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.IsFullReport {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func encodeFixed64Userrecommend(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Userrecommend(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintUserrecommend(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *RecommendFromContacts) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovUserrecommend(uint64(l))
	n += 1 + sovUserrecommend(uint64(m.PhoneUid))
	return n
}

func (m *AddOrUpdateFullContactsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.Uid))
	if len(m.PhoneList) > 0 {
		for _, s := range m.PhoneList {
			l = len(s)
			n += 1 + l + sovUserrecommend(uint64(l))
		}
	}
	n += 1 + sovUserrecommend(uint64(m.ContactVersion))
	return n
}

func (m *AddOrUpdateFullContactsResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *AddIncrementContactsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.Uid))
	if len(m.PhoneList) > 0 {
		for _, s := range m.PhoneList {
			l = len(s)
			n += 1 + l + sovUserrecommend(uint64(l))
		}
	}
	return n
}

func (m *AddIncrementContactsResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DeleteIncrementContactsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.Uid))
	if len(m.PhoneList) > 0 {
		for _, s := range m.PhoneList {
			l = len(s)
			n += 1 + l + sovUserrecommend(uint64(l))
		}
	}
	return n
}

func (m *DeleteIncrementContactsResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SetRejectRecommendReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.Uid))
	l = len(m.Phone)
	n += 1 + l + sovUserrecommend(uint64(l))
	n += 1 + sovUserrecommend(uint64(m.BRecommend))
	return n
}

func (m *SetRejectRecommendResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ChangeRecommendStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.Uid))
	n += 1 + sovUserrecommend(uint64(m.Status))
	return n
}

func (m *ChangeRecommendStatusResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetRecommendStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.Uid))
	return n
}

func (m *GetRecommendStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.Status))
	return n
}

func (m *GetRecommendFromContactsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.Uid))
	return n
}

func (m *GetRecommendFromContactsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RecommendList) > 0 {
		for _, e := range m.RecommendList {
			l = e.Size()
			n += 1 + l + sovUserrecommend(uint64(l))
		}
	}
	n += 1 + sovUserrecommend(uint64(m.ContactVersion))
	return n
}

func (m *GetNoFriendRecommendReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.Uid))
	return n
}

func (m *GetNoFriendRecommendResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RecommendList) > 0 {
		for _, e := range m.RecommendList {
			l = e.Size()
			n += 1 + l + sovUserrecommend(uint64(l))
		}
	}
	n += 1 + sovUserrecommend(uint64(m.ContactVersion))
	return n
}

func (m *AddFullReportGameReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.Uid))
	if len(m.GameIdList) > 0 {
		for _, e := range m.GameIdList {
			n += 1 + sovUserrecommend(uint64(e))
		}
	}
	return n
}

func (m *AddFullReportGameResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UpdateIncreReportGameReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.Uid))
	if len(m.AddGameIdList) > 0 {
		for _, e := range m.AddGameIdList {
			n += 1 + sovUserrecommend(uint64(e))
		}
	}
	if len(m.DeleteGameIdList) > 0 {
		for _, e := range m.DeleteGameIdList {
			n += 1 + sovUserrecommend(uint64(e))
		}
	}
	return n
}

func (m *UpdateIncreReportGameResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RecommendFromAll) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.RecommendType))
	l = len(m.Phone)
	n += 1 + l + sovUserrecommend(uint64(l))
	n += 1 + sovUserrecommend(uint64(m.PhoneUid))
	n += 1 + sovUserrecommend(uint64(m.GameId))
	n += 1 + sovUserrecommend(uint64(m.GameUid))
	return n
}

func (m *GetRecommendFromAllReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.Uid))
	n += 1 + sovUserrecommend(uint64(m.Count))
	if len(m.ExUidList) > 0 {
		for _, e := range m.ExUidList {
			n += 1 + sovUserrecommend(uint64(e))
		}
	}
	n += 1 + sovUserrecommend(uint64(m.PriRecommend))
	n += 1 + sovUserrecommend(uint64(m.Sex))
	return n
}

func (m *GetRecommendFromAllResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RecommendAllList) > 0 {
		for _, e := range m.RecommendAllList {
			l = e.Size()
			n += 1 + l + sovUserrecommend(uint64(l))
		}
	}
	n += 1 + sovUserrecommend(uint64(m.ContactVersion))
	return n
}

func (m *GetUserInstallGameReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.Uid))
	return n
}

func (m *GetUserInstallGameResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GameIdList) > 0 {
		for _, e := range m.GameIdList {
			n += 1 + sovUserrecommend(uint64(e))
		}
	}
	return n
}

func (m *UpdateUserRecommendAsync) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend(uint64(m.Uid))
	n += 2
	return n
}

func sovUserrecommend(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozUserrecommend(x uint64) (n int) {
	return sovUserrecommend(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *RecommendFromContacts) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecommendFromContacts: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecommendFromContacts: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhoneUid", wireType)
			}
			m.PhoneUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhoneUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("phone")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("phone_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddOrUpdateFullContactsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddOrUpdateFullContactsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddOrUpdateFullContactsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhoneList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhoneList = append(m.PhoneList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContactVersion", wireType)
			}
			m.ContactVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContactVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("contact_version")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddOrUpdateFullContactsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddOrUpdateFullContactsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddOrUpdateFullContactsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddIncrementContactsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddIncrementContactsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddIncrementContactsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhoneList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhoneList = append(m.PhoneList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddIncrementContactsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddIncrementContactsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddIncrementContactsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteIncrementContactsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteIncrementContactsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteIncrementContactsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhoneList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhoneList = append(m.PhoneList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteIncrementContactsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteIncrementContactsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteIncrementContactsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetRejectRecommendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetRejectRecommendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetRejectRecommendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BRecommend", wireType)
			}
			m.BRecommend = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BRecommend |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("phone")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("bRecommend")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetRejectRecommendResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetRejectRecommendResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetRejectRecommendResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangeRecommendStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChangeRecommendStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChangeRecommendStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangeRecommendStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChangeRecommendStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChangeRecommendStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecommendStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecommendStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecommendStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecommendStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendFromContactsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecommendFromContactsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecommendFromContactsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendFromContactsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecommendFromContactsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecommendFromContactsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecommendList = append(m.RecommendList, &RecommendFromContacts{})
			if err := m.RecommendList[len(m.RecommendList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContactVersion", wireType)
			}
			m.ContactVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContactVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNoFriendRecommendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNoFriendRecommendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNoFriendRecommendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNoFriendRecommendResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNoFriendRecommendResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNoFriendRecommendResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecommendList = append(m.RecommendList, &RecommendFromContacts{})
			if err := m.RecommendList[len(m.RecommendList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContactVersion", wireType)
			}
			m.ContactVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContactVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFullReportGameReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFullReportGameReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFullReportGameReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameIdList = append(m.GameIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUserrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUserrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameIdList = append(m.GameIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFullReportGameResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFullReportGameResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFullReportGameResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateIncreReportGameReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateIncreReportGameReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateIncreReportGameReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.AddGameIdList = append(m.AddGameIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUserrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUserrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.AddGameIdList = append(m.AddGameIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddGameIdList", wireType)
			}
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.DeleteGameIdList = append(m.DeleteGameIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUserrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUserrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.DeleteGameIdList = append(m.DeleteGameIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeleteGameIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateIncreReportGameResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateIncreReportGameResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateIncreReportGameResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecommendFromAll) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecommendFromAll: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecommendFromAll: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendType", wireType)
			}
			m.RecommendType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecommendType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhoneUid", wireType)
			}
			m.PhoneUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhoneUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameUid", wireType)
			}
			m.GameUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("recommend_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendFromAllReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecommendFromAllReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecommendFromAllReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ExUidList = append(m.ExUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUserrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUserrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ExUidList = append(m.ExUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExUidList", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PriRecommend", wireType)
			}
			m.PriRecommend = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PriRecommend |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendFromAllResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecommendFromAllResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecommendFromAllResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendAllList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecommendAllList = append(m.RecommendAllList, &RecommendFromAll{})
			if err := m.RecommendAllList[len(m.RecommendAllList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContactVersion", wireType)
			}
			m.ContactVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContactVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserInstallGameReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserInstallGameReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserInstallGameReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserInstallGameResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserInstallGameResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserInstallGameResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameIdList = append(m.GameIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUserrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUserrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameIdList = append(m.GameIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserRecommendAsync) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserRecommendAsync: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserRecommendAsync: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsFullReport", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsFullReport = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_full_report")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipUserrecommend(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUserrecommend
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserrecommend
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthUserrecommend
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowUserrecommend
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipUserrecommend(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthUserrecommend = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUserrecommend   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/userrecommend/userrecommend.proto", fileDescriptorUserrecommend)
}

var fileDescriptorUserrecommend = []byte{
	// 1272 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x57, 0x4f, 0x6f, 0x1b, 0x45,
	0x14, 0xef, 0xd8, 0x69, 0x9a, 0xbc, 0xd6, 0xc5, 0x4c, 0x48, 0xed, 0x6e, 0x9a, 0xc4, 0x0c, 0x69,
	0x6b, 0xda, 0x3a, 0x51, 0xc2, 0x9f, 0x83, 0x65, 0x59, 0x72, 0xd3, 0x24, 0x54, 0x90, 0xa6, 0x72,
	0x9a, 0xde, 0xc0, 0xda, 0x78, 0x87, 0x74, 0xe9, 0x7a, 0x3d, 0xdd, 0xd9, 0xad, 0x12, 0x89, 0x03,
	0x42, 0x42, 0x42, 0xbd, 0x80, 0x8a, 0x44, 0x25, 0xb8, 0xe6, 0xc0, 0x89, 0x33, 0x1f, 0xa1, 0xdc,
	0xfa, 0x09, 0x10, 0x84, 0x4b, 0x3e, 0x06, 0x9a, 0x99, 0xf5, 0x7a, 0xd7, 0xbb, 0x6b, 0x9b, 0x3f,
	0x12, 0x27, 0xdb, 0xf3, 0xfe, 0xfd, 0xde, 0x7b, 0xf3, 0x9b, 0xf7, 0x0c, 0x37, 0x38, 0x75, 0x9e,
	0x9a, 0x6d, 0xca, 0x57, 0x3c, 0x4e, 0x1d, 0x87, 0xb6, 0xbb, 0x9d, 0x0e, 0xb5, 0x8d, 0xe8, 0xaf,
	0x65, 0xe6, 0x74, 0xdd, 0x2e, 0xce, 0x45, 0x0e, 0xb5, 0x25, 0xf1, 0xa5, 0x6b, 0xaf, 0xb8, 0xd6,
	0x53, 0x66, 0xb6, 0x1f, 0x5b, 0x74, 0x85, 0x3f, 0xde, 0xf7, 0x4c, 0xcb, 0x35, 0x6d, 0xf7, 0x88,
	0x51, 0x65, 0x44, 0x1e, 0xc2, 0x6c, 0xb3, 0x67, 0xb2, 0xe9, 0x74, 0x3b, 0xeb, 0x5d, 0xdb, 0xd5,
	0xdb, 0x2e, 0xc7, 0x1a, 0x9c, 0x65, 0x8f, 0xba, 0x36, 0x2d, 0xa2, 0x52, 0xa6, 0x3c, 0x7d, 0x7b,
	0xe2, 0xe5, 0x6f, 0x8b, 0x67, 0x9a, 0xea, 0x08, 0xbf, 0x09, 0xd3, 0xf2, 0x4b, 0xcb, 0x33, 0x8d,
	0x62, 0xa6, 0x94, 0x29, 0xe7, 0x7c, 0xf9, 0x94, 0x3c, 0xde, 0x33, 0x0d, 0xf2, 0x25, 0x02, 0xad,
	0x61, 0x18, 0x3b, 0xce, 0x1e, 0x33, 0x74, 0x97, 0x6e, 0x7a, 0x96, 0xd5, 0x73, 0xdd, 0xa4, 0x4f,
	0xf0, 0x25, 0xc8, 0x0a, 0x5b, 0x14, 0xb2, 0x15, 0x07, 0x78, 0x1e, 0x40, 0x79, 0xb6, 0x4c, 0xee,
	0x16, 0x33, 0xa5, 0x6c, 0x79, 0xba, 0xa9, 0x62, 0x7d, 0x64, 0x72, 0x17, 0x57, 0xe0, 0xb5, 0xb6,
	0xf2, 0xd2, 0x7a, 0x4a, 0x1d, 0x6e, 0x76, 0xed, 0x62, 0x36, 0xe4, 0xe2, 0xa2, 0x2f, 0x7c, 0xa8,
	0x64, 0x64, 0x1e, 0xe6, 0x52, 0x31, 0x70, 0x46, 0xee, 0x43, 0xa1, 0x61, 0x18, 0x77, 0xed, 0xb6,
	0x43, 0x3b, 0xd4, 0x76, 0xff, 0x3d, 0x3e, 0xa2, 0x41, 0x31, 0xd9, 0x23, 0x67, 0x64, 0x17, 0xb4,
	0x3b, 0xd4, 0xa2, 0x2e, 0xfd, 0x2f, 0x03, 0xce, 0xc3, 0x5c, 0xaa, 0x53, 0xce, 0xc8, 0x13, 0x98,
	0xdd, 0xa5, 0x6e, 0x93, 0x7e, 0x46, 0xdb, 0x6e, 0xd0, 0xe6, 0x61, 0xe1, 0x82, 0xae, 0x67, 0xe2,
	0x5d, 0x5f, 0x02, 0xd8, 0x0f, 0x9c, 0x44, 0xea, 0x1e, 0x3a, 0x27, 0x45, 0xb8, 0x94, 0x14, 0x52,
	0x96, 0xbb, 0xb8, 0xfe, 0x48, 0xb7, 0x0f, 0x68, 0x70, 0xbc, 0xeb, 0xea, 0xae, 0x37, 0x34, 0xfd,
	0x2b, 0x30, 0xc9, 0xa5, 0x52, 0xe4, 0x9a, 0xf9, 0x67, 0x64, 0x0e, 0x2e, 0xa7, 0x78, 0xe4, 0x8c,
	0xac, 0xc0, 0xec, 0x16, 0x75, 0xc7, 0x8f, 0x45, 0xde, 0x87, 0x4b, 0x49, 0x06, 0x9c, 0x8d, 0x40,
	0xf1, 0x1e, 0xcc, 0x85, 0xed, 0xc2, 0x2c, 0x1a, 0x16, 0xee, 0x07, 0x04, 0x57, 0xd2, 0xed, 0x38,
	0xc3, 0x1f, 0xc2, 0xc5, 0x80, 0xcd, 0xaa, 0xfd, 0xa8, 0x94, 0x2d, 0x9f, 0x5f, 0x5b, 0x5a, 0x8e,
	0xb2, 0x3f, 0xd9, 0x43, 0x2e, 0x50, 0x48, 0x63, 0x4e, 0xa6, 0x84, 0x52, 0x99, 0xb3, 0x0a, 0x85,
	0x2d, 0xea, 0xde, 0xeb, 0x6e, 0x3a, 0xa6, 0x6c, 0xdf, 0xe8, 0xab, 0x43, 0xbe, 0x47, 0x50, 0x4c,
	0xb6, 0xf9, 0x9f, 0x73, 0xb9, 0x0f, 0x6f, 0x34, 0x0c, 0x43, 0xb0, 0xbf, 0x49, 0x59, 0xd7, 0x71,
	0xb7, 0xf4, 0x0e, 0x1d, 0x76, 0xe7, 0x4a, 0x70, 0xe1, 0x40, 0xef, 0xd0, 0x96, 0x69, 0xf4, 0x49,
	0x97, 0x6b, 0x82, 0x38, 0xbb, 0x2b, 0x01, 0x90, 0x02, 0xcc, 0x26, 0x78, 0xe4, 0x8c, 0x3c, 0x43,
	0x50, 0x54, 0x8f, 0x8d, 0xe4, 0xe3, 0x78, 0xf1, 0xae, 0x43, 0x5e, 0x37, 0x8c, 0x56, 0x42, 0xcc,
	0x9c, 0x6e, 0x18, 0x5b, 0x41, 0x58, 0x5c, 0x81, 0x19, 0x43, 0x92, 0x3d, 0xaa, 0x9b, 0x95, 0xba,
	0x79, 0x25, 0xea, 0xab, 0x0b, 0x76, 0xa4, 0x60, 0xe1, 0x8c, 0x7c, 0x95, 0x81, 0x7c, 0xa4, 0xd8,
	0x0d, 0xcb, 0xc2, 0x37, 0xc3, 0x5d, 0x12, 0x43, 0x22, 0x02, 0xb6, 0xdf, 0x85, 0x07, 0x47, 0x8c,
	0x86, 0x9f, 0x0a, 0x34, 0x74, 0x40, 0x64, 0x43, 0xbd, 0x09, 0x06, 0x04, 0x9e, 0x87, 0x73, 0x7e,
	0x16, 0xc5, 0x89, 0x90, 0xc2, 0xa4, 0x2a, 0x33, 0x5e, 0x84, 0x29, 0x29, 0x16, 0x0e, 0xce, 0x86,
	0xe4, 0xd2, 0x48, 0x0c, 0x98, 0x0d, 0xc8, 0x35, 0x23, 0x78, 0xe6, 0xa0, 0xd0, 0xdc, 0x58, 0xdf,
	0xd9, 0xde, 0xde, 0xb8, 0x77, 0xa7, 0xb5, 0xd9, 0xdc, 0xd9, 0x6e, 0xad, 0xef, 0xdc, 0x7b, 0xd0,
	0x58, 0x7f, 0xb0, 0x9b, 0x47, 0xb8, 0x00, 0x33, 0x03, 0xc2, 0xad, 0xc6, 0xf6, 0x46, 0x3e, 0x43,
	0x7e, 0x46, 0x51, 0xd6, 0xfb, 0xa5, 0x18, 0xf1, 0x46, 0xb6, 0xbb, 0x9e, 0xed, 0x46, 0x1e, 0x03,
	0x75, 0x84, 0x17, 0xe0, 0x3c, 0x3d, 0x14, 0xa0, 0xc3, 0xad, 0x99, 0xa6, 0x87, 0x7b, 0xa6, 0x6a,
	0x61, 0x19, 0x2e, 0x30, 0xc7, 0xec, 0xbf, 0xa2, 0xe1, 0xd4, 0x23, 0x12, 0x11, 0x9d, 0xd3, 0xc3,
	0x48, 0xee, 0xe2, 0x80, 0xbc, 0x40, 0x92, 0x9a, 0x71, 0xc0, 0x9c, 0xe1, 0x6d, 0xc0, 0xfd, 0xfe,
	0xe9, 0x96, 0x15, 0x66, 0xda, 0xe2, 0x30, 0xa6, 0x09, 0x07, 0xf9, 0x40, 0xd6, 0xb0, 0xac, 0x7f,
	0xc2, 0x33, 0xf5, 0xe0, 0xee, 0x71, 0xea, 0xdc, 0xb5, 0xb9, 0xab, 0x5b, 0xd6, 0x88, 0x8b, 0x4f,
	0xaa, 0xb2, 0xf4, 0x31, 0x03, 0xce, 0x62, 0x14, 0x44, 0x31, 0x0a, 0x7e, 0xd2, 0x23, 0x9a, 0x30,
	0x0f, 0x72, 0x69, 0xf0, 0x23, 0xbb, 0x9d, 0xda, 0xb8, 0x1b, 0x70, 0xd1, 0xe4, 0xad, 0x4f, 0x3d,
	0xcb, 0x6a, 0x39, 0x92, 0x0d, 0xb2, 0x83, 0x53, 0xbd, 0xf2, 0x9b, 0xbc, 0xcf, 0xe8, 0xb5, 0x3f,
	0x72, 0x90, 0x8b, 0xb8, 0xc6, 0xbf, 0x20, 0xb9, 0x2e, 0x24, 0x6d, 0x13, 0xf8, 0xed, 0x81, 0xe2,
	0xa6, 0x6f, 0x3e, 0xda, 0x8d, 0x71, 0x55, 0x39, 0x23, 0x9b, 0x5f, 0x1c, 0x9f, 0x66, 0xd1, 0xb3,
	0xe3, 0xd3, 0xec, 0xa4, 0x57, 0x75, 0xab, 0xac, 0xfa, 0xfc, 0xf8, 0x34, 0xbb, 0x5a, 0xaa, 0x78,
	0x35, 0xcf, 0x34, 0xea, 0x15, 0xb7, 0x36, 0xd0, 0x9e, 0x7a, 0x85, 0xd5, 0x24, 0xc1, 0x56, 0x6f,
	0xc9, 0x8f, 0xb5, 0x5b, 0xcb, 0xcb, 0xcb, 0xf5, 0x12, 0xfe, 0x11, 0xc9, 0x27, 0x30, 0xb6, 0x23,
	0xe0, 0x6b, 0x71, 0x30, 0x49, 0xdb, 0x89, 0x76, 0x7d, 0x2c, 0x3d, 0xce, 0xc8, 0x9a, 0x40, 0x9c,
	0x11, 0x88, 0x27, 0x3c, 0x1f, 0xef, 0x62, 0x0f, 0x6f, 0x29, 0x05, 0xdd, 0x31, 0x82, 0x42, 0xca,
	0x12, 0x13, 0x2b, 0x6c, 0xfa, 0x06, 0x15, 0x2b, 0xec, 0xb0, 0xbd, 0x48, 0xc2, 0xcc, 0xfe, 0x3d,
	0x98, 0x2f, 0x10, 0xe0, 0xf8, 0x66, 0x83, 0x07, 0x27, 0x58, 0xe2, 0xbe, 0xa5, 0x5d, 0x1d, 0x43,
	0x8b, 0x33, 0xf2, 0xae, 0xc0, 0x35, 0xe1, 0x37, 0x9c, 0x55, 0xf7, 0xa3, 0xc8, 0x7a, 0xc0, 0xea,
	0x95, 0xfd, 0x5a, 0x7f, 0xe3, 0xaa, 0xe3, 0x6f, 0xd4, 0xe4, 0x4d, 0xde, 0xe3, 0x07, 0xcb, 0x32,
	0x64, 0x55, 0xd1, 0x6e, 0x8e, 0xad, 0xcb, 0x19, 0xd1, 0x04, 0xd6, 0xb3, 0x02, 0x6b, 0xc6, 0x93,
	0x38, 0xa7, 0x83, 0x0a, 0xe2, 0xef, 0x10, 0xbc, 0x1e, 0x9b, 0x90, 0xf8, 0xad, 0xf8, 0x2d, 0x8a,
	0x4d, 0x65, 0x6d, 0x69, 0xb4, 0x12, 0x67, 0xe4, 0x1d, 0x11, 0x7c, 0xd2, 0x6f, 0xe0, 0x81, 0x0c,
	0x5f, 0xea, 0x37, 0xf0, 0xa0, 0xa6, 0x5e, 0x8a, 0xd5, 0x5b, 0xea, 0x53, 0xb5, 0x10, 0xff, 0x8a,
	0x60, 0x36, 0x71, 0x22, 0xe2, 0xc1, 0xfb, 0x9d, 0x36, 0xc3, 0xb5, 0xf2, 0x78, 0x8a, 0x9c, 0x91,
	0x8f, 0x05, 0xc2, 0x73, 0x7e, 0x2b, 0x75, 0x1f, 0xe3, 0x07, 0x7d, 0x8c, 0x7a, 0x4d, 0x37, 0x8c,
	0x1e, 0x4c, 0xf5, 0xb5, 0x07, 0x54, 0x24, 0xa0, 0xa6, 0x79, 0x4f, 0x1e, 0xfc, 0x0a, 0x72, 0xf9,
	0x09, 0xc1, 0x4c, 0xc2, 0x18, 0xc0, 0x57, 0x47, 0xb4, 0x50, 0xcd, 0x36, 0xed, 0xda, 0x38, 0x6a,
	0x9c, 0x91, 0xdb, 0x22, 0x8b, 0x29, 0x91, 0xc5, 0x94, 0x57, 0xb5, 0xab, 0xac, 0xca, 0x65, 0x1e,
	0x37, 0xfb, 0x79, 0xd8, 0x35, 0x39, 0xf9, 0x14, 0x6d, 0x42, 0x03, 0xac, 0x5e, 0xaa, 0xf0, 0x1a,
	0xa7, 0x87, 0x75, 0xfc, 0x1c, 0xc1, 0x6c, 0xe2, 0x9a, 0x1e, 0x2b, 0x7b, 0xda, 0xdf, 0x83, 0x58,
	0xd9, 0xd3, 0xb7, 0xfe, 0x6b, 0x02, 0xf0, 0xb4, 0x7f, 0x31, 0x14, 0xd8, 0x99, 0x3e, 0x58, 0x5e,
	0x53, 0x3b, 0x7b, 0x1d, 0x7f, 0x0e, 0x38, 0xbe, 0xec, 0xc7, 0xc8, 0x9c, 0xf8, 0x07, 0x42, 0xbb,
	0x3a, 0x86, 0x16, 0x67, 0xe4, 0xb2, 0x80, 0x02, 0x21, 0x82, 0x4c, 0xf5, 0x80, 0xf8, 0xd1, 0x07,
	0x26, 0x5f, 0x52, 0xf4, 0xf8, 0x34, 0x4d, 0x8a, 0x9e, 0x30, 0x42, 0x55, 0xf4, 0xf3, 0x49, 0xd1,
	0xb5, 0xc9, 0xaf, 0x8f, 0x4f, 0xb3, 0xaf, 0x8e, 0x6e, 0xe7, 0x5f, 0x9e, 0x2c, 0xa0, 0x57, 0x27,
	0x0b, 0xe8, 0xf7, 0x93, 0x05, 0xf4, 0xed, 0x9f, 0x0b, 0x67, 0xfe, 0x0a, 0x00, 0x00, 0xff, 0xff,
	0x69, 0x8e, 0x5a, 0x34, 0x6f, 0x10, 0x00, 0x00,
}
