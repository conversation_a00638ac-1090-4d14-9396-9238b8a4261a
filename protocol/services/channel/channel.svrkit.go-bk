package Channel

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: services/channel/channel.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for Channel service
const ChannelMagic = uint16(15200)

// Client API for Channel service

type ChannelClientInterface interface {
	DissolveChannel(ctx context.Context, uin uint32, in *DissolveChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	HoldMicrSpace(ctx context.Context, uin uint32, in *HoldMicrSpaceReq, opts ...svrkit.CallOption) (*HoldMicrSpaceResp, error)
	ReleaseMicrSpace(ctx context.Context, uin uint32, in *ReleaseMicrSpaceReq, opts ...svrkit.CallOption) (*ReleaseMicrSpaceResp, error)
	// 获取房间主题的详情
	GetChannelTopicDetail(ctx context.Context, uin uint32, in *GetChannelTopicDetailReq, opts ...svrkit.CallOption) (*GetChannelTopicDetailResp, error)
	// 废弃 改用 UnmuteChannelMember
	UnsetChannelMute(ctx context.Context, uin uint32, in *UnsetChannelMuteReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChannelDetailInfo(ctx context.Context, uin uint32, in *GetChannelDetailInfoReq, opts ...svrkit.CallOption) (*GetChannelDetailInfoResp, error)
	GetChannelMemberSize(ctx context.Context, uin uint32, in *GetChannelMemberSizeReq, opts ...svrkit.CallOption) (*GetChannelMemberSizeResp, error)
	GetChannelMuteList(ctx context.Context, uin uint32, in *GetChannelMuteListReq, opts ...svrkit.CallOption) (*GetChannelMuteListResp, error)
	GetMicrList(ctx context.Context, uin uint32, in *GetMicrListReq, opts ...svrkit.CallOption) (*GetMicrListResp, error)
	GetChannelListByBindId(ctx context.Context, uin uint32, in *GetChannelListByBindIdReq, opts ...svrkit.CallOption) (*GetChannelListByBindIdResp, error)
	GetChannelBindInfo(ctx context.Context, uin uint32, in *GetChannelBindInfoReq, opts ...svrkit.CallOption) (*GetChannelBindInfoResp, error)
	ModifyChannel(ctx context.Context, uin uint32, in *ModifyChannelReq, opts ...svrkit.CallOption) (*ModifyChannelResp, error)
	// 检查并从指定的绑定上频道的麦列表上剔除某人
	// 比如从公会下所有频道踢某人下麦(当用户被撤销管理员时)
	CheckAndKickChannelMicrByBindId(ctx context.Context, uin uint32, in *CheckAndKickChannelMicrByBindIdReq, opts ...svrkit.CallOption) (*CheckAndKickChannelMicrByBindIdResp, error)
	CheckUserIsMute(ctx context.Context, uin uint32, in *CheckUserIsMuteReq, opts ...svrkit.CallOption) (*CheckUserIsMuteResp, error)
	// 将用户出公会下面的所在频道 比如用户移出公会时被调用
	// 废弃
	KickUserOutOfGuildChannel(ctx context.Context, uin uint32, in *KickUserOutOfGuildChannelReq, opts ...svrkit.CallOption) (*KickUserOutOfGuildChannelResp, error)
	// 关闭一个麦位入口
	// 过期接口 废弃
	DisableChannelMicEntry(ctx context.Context, uin uint32, in *DisableChannelMicEntryReq, opts ...svrkit.CallOption) (*DisableChannelMicEntryResp, error)
	// 开启一个麦位入口
	// 过期接口 废弃
	EnableChannelMicEntry(ctx context.Context, uin uint32, in *EnableChannelMicEntryReq, opts ...svrkit.CallOption) (*EnableChannelMicEntryResp, error)
	// 踢人下麦
	// 过期接口 废弃
	KickoutChannelMic(ctx context.Context, uin uint32, in *KickoutChannelMicReq, opts ...svrkit.CallOption) (*KickoutChannelMicResp, error)
	// 踢人出频道
	// 过期接口 废弃
	KickoutChannelMember(ctx context.Context, uin uint32, in *KickoutChannelMemberReq, opts ...svrkit.CallOption) (*KickoutChannelMemberResp, error)
	// 获取临时房的 分配状态
	CheckTmpChannelIsAlloced(ctx context.Context, uin uint32, in *CheckTmpChannelIsAllocedReq, opts ...svrkit.CallOption) (*CheckTmpChannelIsAllocedResp, error)
	// 禁言(如果有上麦的话 会被踢下麦)
	// 废弃 改用 MuteChannelMember接口
	SetChannelMuteAndKickMic(ctx context.Context, uin uint32, in *SetChannelMuteAndKickMicReq, opts ...svrkit.CallOption) (*SetChannelMuteAndKickMicResp, error)
	// 批量获取用户当前房间ID
	// rpc BatchGetUserCurrChannelID( BatchGetUserCurrChannelIDReq ) returns( BatchGetUserCurrChannelIDResp ){
	// 	option( tlvpickle.CmdID ) = 29;
	//    option( tlvpickle.OptString ) = "u:";
	//    option( tlvpickle.Usage ) = "-u <uid>";
	// }
	//
	// 根据displayId搜索channel
	GetChannelByDisplayId(ctx context.Context, uin uint32, in *GetChannelByDisplayIdReq, opts ...svrkit.CallOption) (*GetChannelByDisplayIdResp, error)
	// 根据uid获取权限列表
	GetUserChannelRoleList(ctx context.Context, uin uint32, in *GetUserChannelRoleListReq, opts ...svrkit.CallOption) (*GetUserChannelRoleListResp, error)
	// 改变mic模式
	// 过期接口 废弃 改为使用channelmic服务提供的接口
	SetChannelMicModel(ctx context.Context, uin uint32, in *SetChannelMicModelReq, opts ...svrkit.CallOption) (*SetChannelMicModelResp, error)
	// 通过displayId创建频道
	CreateChannelByDisplayID(ctx context.Context, uin uint32, in *CreateChannelByDisplayIDReq, opts ...svrkit.CallOption) (*CreateChannelByDisplayIDResp, error)
	// 根据channelID列表批量获取房间信息
	// 过期接口
	GetChannelDetailInfoBatch(ctx context.Context, uin uint32, in *GetChannelDetailInfoBatchReq, opts ...svrkit.CallOption) (*GetChannelDetailInfoBatchResp, error)
	// 分配临时房间
	AllocTempChannel(ctx context.Context, uin uint32, in *AllocTempChannelReq, opts ...svrkit.CallOption) (*AllocTempChannelResp, error)
	// 获取房间的欢迎语
	GetChannelWelcomeMsg(ctx context.Context, uin uint32, in *GetChannelWelcomeMsgReq, opts ...svrkit.CallOption) (*GetChannelWelcomeMsgResp, error)
	// 关闭房间内所有的空麦位
	// 过期接口 废弃 改为使用channelmic服务提供的接口
	DisableAllEmptyMicrSpace(ctx context.Context, uin uint32, in *DisableAllEmptyMicrSpaceReq, opts ...svrkit.CallOption) (*DisableAllEmptyMicrSpaceResp, error)
	// 添加一首配置歌曲
	AddConfigMusic(ctx context.Context, uin uint32, in *AddConfigMusicReq, opts ...svrkit.CallOption) (*AddConfigMusicResp, error)
	// 删除一首配置歌曲
	DelConfigMusic(ctx context.Context, uin uint32, in *DelConfigMusicReq, opts ...svrkit.CallOption) (*DelConfigMusicResp, error)
	// 获取配置歌曲列表
	GetConfigMusicList(ctx context.Context, uin uint32, in *GetConfigMusicListReq, opts ...svrkit.CallOption) (*GetConfigMusicListResp, error)
	// 获取频道创建时间
	GetChannelCreateTs(ctx context.Context, uin uint32, in *GetChannelCreateTsReq, opts ...svrkit.CallOption) (*GetChannelCreateTsResp, error)
	// 记录房间礼物信息
	RecordSendGiftEvent(ctx context.Context, uin uint32, in *RecordSendGiftEventReq, opts ...svrkit.CallOption) (*RecordSendGiftEventResp, error)
	// 获取房间消费土豪榜
	GetConsumeTopN(ctx context.Context, uin uint32, in *GetConsumeTopNReq, opts ...svrkit.CallOption) (*GetConsumeTopNResp, error)
	// 更新房间的displayID
	ChangeDisplayID(ctx context.Context, uin uint32, in *ChangeDisplayIDReq, opts ...svrkit.CallOption) (*ChangeDisplayIDResp, error)
	// 获取房间最近访客列表
	GetChannelHistoryList(ctx context.Context, uin uint32, in *GetChannelHistoryListReq, opts ...svrkit.CallOption) (*GetChannelHistoryListResp, error)
	// 获取房间统计信息(包括房间最大在线人数)
	GetChannelStat(ctx context.Context, uin uint32, in *GetChannelStatReq, opts ...svrkit.CallOption) (*GetChannelStatResp, error)
	// 获取房间管理员列表
	GetChannelAdmin(ctx context.Context, uin uint32, in *GetChannelAdminReq, opts ...svrkit.CallOption) (*GetChannelAdminResp, error)
	// 设置麦位状态 比如打开/关闭/禁言 麦位 (可以取代之前的麦位开启和麦位关闭命令)
	// 过期接口 废弃 改为使用channelmic服务提供的接口
	SetChannelMicSpaceStatus(ctx context.Context, uin uint32, in *SetChannelMicSpaceStatusReq, opts ...svrkit.CallOption) (*SetChannelMicSpaceStatusResp, error)
	// 换麦位
	// 过期接口 废弃 改为使用channelmic服务提供的接口
	ChangeMicrophone(ctx context.Context, uin uint32, in *ChangeMicrophoneReq, opts ...svrkit.CallOption) (*ChangeMicrophoneResp, error)
	// 批量判断房间是否属于临时分配房
	BatchCheckIsTempAllocChannel(ctx context.Context, uin uint32, in *BatchCheckIsTempAllocChannelReq, opts ...svrkit.CallOption) (*BatchCheckIsTempAllocChannelResp, error)
	// 获取房间简单信息
	GetChannelSimpleInfo(ctx context.Context, uin uint32, in *GetChannelSimpleInfoReq, opts ...svrkit.CallOption) (*GetChannelSimpleInfoResp, error)
	// 批量获取房间简单信息
	BatchGetChannelSimpleInfo(ctx context.Context, uin uint32, in *BatchGetChannelSimpleInfoReq, opts ...svrkit.CallOption) (*BatchGetChannelSimpleInfoResp, error)
	// 连麦请求
	// 过期接口 改为使用channellive服务提供的接口
	LiveConnectMicApply(ctx context.Context, uin uint32, in *LiveConnectMicApplyReq, opts ...svrkit.CallOption) (*LiveConnectMicApplyResp, error)
	// 回应连麦
	// 过期接口 改为使用channellive服务提供的接口
	LiveConnectMicHandle(ctx context.Context, uin uint32, in *LiveConnectMicHandleReq, opts ...svrkit.CallOption) (*LiveConnectMicHandleResp, error)
	// 获取连麦申请人列表
	// 过期接口 改为使用channellive服务提供的接口
	GetLiveConnectMicApplyUserList(ctx context.Context, uin uint32, in *GetLiveConnectMicApplyUserListReq, opts ...svrkit.CallOption) (*GetLiveConnectMicApplyUserListResp, error)
	// 结束直播
	// 过期接口 改为使用channellive服务提供的接口
	FinishLive(ctx context.Context, uin uint32, in *FinishLiveReq, opts ...svrkit.CallOption) (*FinishLiveResp, error)
	// 检查是否在直播中
	// 过期接口 改为使用channellive服务提供的接口
	CheckIsLiveStarting(ctx context.Context, uin uint32, in *CheckIsLiveStartingReq, opts ...svrkit.CallOption) (*CheckIsLiveStartingResp, error)
	// 标记直播开始
	// 过期接口 改为使用channellive服务提供的接口
	StartLive(ctx context.Context, uin uint32, in *StartLiveReq, opts ...svrkit.CallOption) (*StartLiveResp, error)
	// 检测用户是否被踢
	CheckUserKickoutFromChannel(ctx context.Context, uin uint32, in *CheckUserKickoutFromChannelReq, opts ...svrkit.CallOption) (*CheckUserKickoutFromChannelResp, error)
	// 获取用户房间内累计消费
	GetUserAccumulateConsume(ctx context.Context, uin uint32, in *GetUserAccumulateConsumeReq, opts ...svrkit.CallOption) (*GetUserAccumulateConsumeResp, error)
	// 过期接口 改为使用channellive服务提供的接口
	EnterLiveChannel(ctx context.Context, uin uint32, in *EnterLiveChannelReq, opts ...svrkit.CallOption) (*EnterLiveChannelResp, error)
	// 过期接口 改为使用channellive服务提供的接口
	QuitLiveChannel(ctx context.Context, uin uint32, in *QuitLiveChannelReq, opts ...svrkit.CallOption) (*QuitLiveChannelResp, error)
	// 释放临时房间
	ReleaseTempChannel(ctx context.Context, uin uint32, in *ReleaseTempChannelReq, opts ...svrkit.CallOption) (*ReleaseTempChannelResp, error)
	AddChannelAdmin(ctx context.Context, uin uint32, in *AddChannelAdminReq, opts ...svrkit.CallOption) (*AddChannelAdminResp, error)
	RemoveChannelAdmin(ctx context.Context, uin uint32, in *RemoveChannelAdminReq, opts ...svrkit.CallOption) (*RemoveChannelAdminResp, error)
	// 新建频道简化版: 只创建频道，不进入频道
	CreateChannelLite(ctx context.Context, uin uint32, in *CreateChannelLiteReq, opts ...svrkit.CallOption) (*CreateChannelLiteResp, error)
	BatchGetChannelBindType(ctx context.Context, uin uint32, in *BatchGetChannelBindTypeReq, opts ...svrkit.CallOption) (*BatchGetChannelBindTypeResp, error)
	// 新的简单禁言接口
	MuteChannelMember(ctx context.Context, uin uint32, in *MuteChannelMemberReq, opts ...svrkit.CallOption) (*MuteChannelMemberResp, error)
	UnmuteChannelMember(ctx context.Context, uin uint32, in *UnmuteChannelMemberReq, opts ...svrkit.CallOption) (*UnmuteChannelMemberResp, error)
	KickoutChannelMemberLite(ctx context.Context, uin uint32, in *KickoutChannelMemberLiteReq, opts ...svrkit.CallOption) (*KickoutChannelMemberLiteResp, error)
	// ============= bugfix 系列接口 ============
	ChackAndRefixChannelOwner(ctx context.Context, uin uint32, in *ChackAndRefixChannelOwnerReq, opts ...svrkit.CallOption) (*ChackAndRefixChannelOwnerResp, error)
	RefixChannelOwner(ctx context.Context, uin uint32, in *RefixChannelOwnerReq, opts ...svrkit.CallOption) (*RefixChannelOwnerResp, error)
	// ============ 白名单 系列接口 =======================
	AddChannelToWhiteList(ctx context.Context, uin uint32, in *AddChannelToWhiteListReq, opts ...svrkit.CallOption) (*AddChannelToWhiteListResp, error)
	RemoveFromWhiteList(ctx context.Context, uin uint32, in *RemoveFromWhiteListReq, opts ...svrkit.CallOption) (*RemoveFromWhiteListResp, error)
	GetChannelWhiteList(ctx context.Context, uin uint32, in *GetChannelWhiteListReq, opts ...svrkit.CallOption) (*GetChannelWhiteListResp, error)
}

type ChannelClient struct {
	cc *svrkit.ClientConn
}

func NewChannelClient(cc *svrkit.ClientConn) ChannelClientInterface {
	return &ChannelClient{cc}
}

const (
	commandChannelGetSelfSvnInfo                  = 9995
	commandChannelEcho                            = 9999
	commandChannelDissolveChannel                 = 2
	commandChannelHoldMicrSpace                   = 5
	commandChannelReleaseMicrSpace                = 6
	commandChannelGetChannelTopicDetail           = 7
	commandChannelUnsetChannelMute                = 8
	commandChannelGetChannelDetailInfo            = 9
	commandChannelGetChannelMemberSize            = 10
	commandChannelGetChannelMuteList              = 12
	commandChannelGetMicrList                     = 13
	commandChannelGetChannelListByBindId          = 14
	commandChannelGetChannelBindInfo              = 15
	commandChannelModifyChannel                   = 16
	commandChannelCheckAndKickChannelMicrByBindId = 19
	commandChannelCheckUserIsMute                 = 21
	commandChannelKickUserOutOfGuildChannel       = 22
	commandChannelDisableChannelMicEntry          = 23
	commandChannelEnableChannelMicEntry           = 24
	commandChannelKickoutChannelMic               = 25
	commandChannelKickoutChannelMember            = 26
	commandChannelCheckTmpChannelIsAlloced        = 27
	commandChannelSetChannelMuteAndKickMic        = 28
	commandChannelGetChannelByDisplayId           = 30
	commandChannelGetUserChannelRoleList          = 31
	commandChannelSetChannelMicModel              = 32
	commandChannelCreateChannelByDisplayID        = 33
	commandChannelGetChannelDetailInfoBatch       = 34
	commandChannelAllocTempChannel                = 36
	commandChannelGetChannelWelcomeMsg            = 37
	commandChannelDisableAllEmptyMicrSpace        = 38
	commandChannelAddConfigMusic                  = 39
	commandChannelDelConfigMusic                  = 40
	commandChannelGetConfigMusicList              = 41
	commandChannelGetChannelCreateTs              = 42
	commandChannelRecordSendGiftEvent             = 43
	commandChannelGetConsumeTopN                  = 44
	commandChannelChangeDisplayID                 = 45
	commandChannelGetChannelHistoryList           = 46
	commandChannelGetChannelStat                  = 47
	commandChannelGetChannelAdmin                 = 48
	commandChannelSetChannelMicSpaceStatus        = 49
	commandChannelChangeMicrophone                = 51
	commandChannelBatchCheckIsTempAllocChannel    = 52
	commandChannelGetChannelSimpleInfo            = 53
	commandChannelBatchGetChannelSimpleInfo       = 54
	commandChannelLiveConnectMicApply             = 55
	commandChannelLiveConnectMicHandle            = 56
	commandChannelGetLiveConnectMicApplyUserList  = 57
	commandChannelFinishLive                      = 58
	commandChannelCheckIsLiveStarting             = 59
	commandChannelStartLive                       = 60
	commandChannelCheckUserKickoutFromChannel     = 61
	commandChannelGetUserAccumulateConsume        = 62
	commandChannelEnterLiveChannel                = 63
	commandChannelQuitLiveChannel                 = 64
	commandChannelReleaseTempChannel              = 65
	commandChannelAddChannelAdmin                 = 66
	commandChannelRemoveChannelAdmin              = 67
	commandChannelCreateChannelLite               = 100
	commandChannelBatchGetChannelBindType         = 102
	commandChannelMuteChannelMember               = 103
	commandChannelUnmuteChannelMember             = 104
	commandChannelKickoutChannelMemberLite        = 105
	commandChannelChackAndRefixChannelOwner       = 200
	commandChannelRefixChannelOwner               = 201
	commandChannelAddChannelToWhiteList           = 202
	commandChannelRemoveFromWhiteList             = 203
	commandChannelGetChannelWhiteList             = 204
)

func (c *ChannelClient) DissolveChannel(ctx context.Context, uin uint32, in *DissolveChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelDissolveChannel, "/Channel.Channel/DissolveChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) HoldMicrSpace(ctx context.Context, uin uint32, in *HoldMicrSpaceReq, opts ...svrkit.CallOption) (*HoldMicrSpaceResp, error) {
	out := new(HoldMicrSpaceResp)
	err := c.cc.Invoke(ctx, uin, commandChannelHoldMicrSpace, "/Channel.Channel/HoldMicrSpace", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) ReleaseMicrSpace(ctx context.Context, uin uint32, in *ReleaseMicrSpaceReq, opts ...svrkit.CallOption) (*ReleaseMicrSpaceResp, error) {
	out := new(ReleaseMicrSpaceResp)
	err := c.cc.Invoke(ctx, uin, commandChannelReleaseMicrSpace, "/Channel.Channel/ReleaseMicrSpace", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelTopicDetail(ctx context.Context, uin uint32, in *GetChannelTopicDetailReq, opts ...svrkit.CallOption) (*GetChannelTopicDetailResp, error) {
	out := new(GetChannelTopicDetailResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelTopicDetail, "/Channel.Channel/GetChannelTopicDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) UnsetChannelMute(ctx context.Context, uin uint32, in *UnsetChannelMuteReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelUnsetChannelMute, "/Channel.Channel/UnsetChannelMute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelDetailInfo(ctx context.Context, uin uint32, in *GetChannelDetailInfoReq, opts ...svrkit.CallOption) (*GetChannelDetailInfoResp, error) {
	out := new(GetChannelDetailInfoResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelDetailInfo, "/Channel.Channel/GetChannelDetailInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelMemberSize(ctx context.Context, uin uint32, in *GetChannelMemberSizeReq, opts ...svrkit.CallOption) (*GetChannelMemberSizeResp, error) {
	out := new(GetChannelMemberSizeResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelMemberSize, "/Channel.Channel/GetChannelMemberSize", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelMuteList(ctx context.Context, uin uint32, in *GetChannelMuteListReq, opts ...svrkit.CallOption) (*GetChannelMuteListResp, error) {
	out := new(GetChannelMuteListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelMuteList, "/Channel.Channel/GetChannelMuteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetMicrList(ctx context.Context, uin uint32, in *GetMicrListReq, opts ...svrkit.CallOption) (*GetMicrListResp, error) {
	out := new(GetMicrListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetMicrList, "/Channel.Channel/GetMicrList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelListByBindId(ctx context.Context, uin uint32, in *GetChannelListByBindIdReq, opts ...svrkit.CallOption) (*GetChannelListByBindIdResp, error) {
	out := new(GetChannelListByBindIdResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelListByBindId, "/Channel.Channel/GetChannelListByBindId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelBindInfo(ctx context.Context, uin uint32, in *GetChannelBindInfoReq, opts ...svrkit.CallOption) (*GetChannelBindInfoResp, error) {
	out := new(GetChannelBindInfoResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelBindInfo, "/Channel.Channel/GetChannelBindInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) ModifyChannel(ctx context.Context, uin uint32, in *ModifyChannelReq, opts ...svrkit.CallOption) (*ModifyChannelResp, error) {
	out := new(ModifyChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelModifyChannel, "/Channel.Channel/ModifyChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) CheckAndKickChannelMicrByBindId(ctx context.Context, uin uint32, in *CheckAndKickChannelMicrByBindIdReq, opts ...svrkit.CallOption) (*CheckAndKickChannelMicrByBindIdResp, error) {
	out := new(CheckAndKickChannelMicrByBindIdResp)
	err := c.cc.Invoke(ctx, uin, commandChannelCheckAndKickChannelMicrByBindId, "/Channel.Channel/CheckAndKickChannelMicrByBindId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) CheckUserIsMute(ctx context.Context, uin uint32, in *CheckUserIsMuteReq, opts ...svrkit.CallOption) (*CheckUserIsMuteResp, error) {
	out := new(CheckUserIsMuteResp)
	err := c.cc.Invoke(ctx, uin, commandChannelCheckUserIsMute, "/Channel.Channel/CheckUserIsMute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) KickUserOutOfGuildChannel(ctx context.Context, uin uint32, in *KickUserOutOfGuildChannelReq, opts ...svrkit.CallOption) (*KickUserOutOfGuildChannelResp, error) {
	out := new(KickUserOutOfGuildChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelKickUserOutOfGuildChannel, "/Channel.Channel/KickUserOutOfGuildChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) DisableChannelMicEntry(ctx context.Context, uin uint32, in *DisableChannelMicEntryReq, opts ...svrkit.CallOption) (*DisableChannelMicEntryResp, error) {
	out := new(DisableChannelMicEntryResp)
	err := c.cc.Invoke(ctx, uin, commandChannelDisableChannelMicEntry, "/Channel.Channel/DisableChannelMicEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) EnableChannelMicEntry(ctx context.Context, uin uint32, in *EnableChannelMicEntryReq, opts ...svrkit.CallOption) (*EnableChannelMicEntryResp, error) {
	out := new(EnableChannelMicEntryResp)
	err := c.cc.Invoke(ctx, uin, commandChannelEnableChannelMicEntry, "/Channel.Channel/EnableChannelMicEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) KickoutChannelMic(ctx context.Context, uin uint32, in *KickoutChannelMicReq, opts ...svrkit.CallOption) (*KickoutChannelMicResp, error) {
	out := new(KickoutChannelMicResp)
	err := c.cc.Invoke(ctx, uin, commandChannelKickoutChannelMic, "/Channel.Channel/KickoutChannelMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) KickoutChannelMember(ctx context.Context, uin uint32, in *KickoutChannelMemberReq, opts ...svrkit.CallOption) (*KickoutChannelMemberResp, error) {
	out := new(KickoutChannelMemberResp)
	err := c.cc.Invoke(ctx, uin, commandChannelKickoutChannelMember, "/Channel.Channel/KickoutChannelMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) CheckTmpChannelIsAlloced(ctx context.Context, uin uint32, in *CheckTmpChannelIsAllocedReq, opts ...svrkit.CallOption) (*CheckTmpChannelIsAllocedResp, error) {
	out := new(CheckTmpChannelIsAllocedResp)
	err := c.cc.Invoke(ctx, uin, commandChannelCheckTmpChannelIsAlloced, "/Channel.Channel/CheckTmpChannelIsAlloced", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) SetChannelMuteAndKickMic(ctx context.Context, uin uint32, in *SetChannelMuteAndKickMicReq, opts ...svrkit.CallOption) (*SetChannelMuteAndKickMicResp, error) {
	out := new(SetChannelMuteAndKickMicResp)
	err := c.cc.Invoke(ctx, uin, commandChannelSetChannelMuteAndKickMic, "/Channel.Channel/SetChannelMuteAndKickMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelByDisplayId(ctx context.Context, uin uint32, in *GetChannelByDisplayIdReq, opts ...svrkit.CallOption) (*GetChannelByDisplayIdResp, error) {
	out := new(GetChannelByDisplayIdResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelByDisplayId, "/Channel.Channel/GetChannelByDisplayId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetUserChannelRoleList(ctx context.Context, uin uint32, in *GetUserChannelRoleListReq, opts ...svrkit.CallOption) (*GetUserChannelRoleListResp, error) {
	out := new(GetUserChannelRoleListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetUserChannelRoleList, "/Channel.Channel/GetUserChannelRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) SetChannelMicModel(ctx context.Context, uin uint32, in *SetChannelMicModelReq, opts ...svrkit.CallOption) (*SetChannelMicModelResp, error) {
	out := new(SetChannelMicModelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelSetChannelMicModel, "/Channel.Channel/SetChannelMicModel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) CreateChannelByDisplayID(ctx context.Context, uin uint32, in *CreateChannelByDisplayIDReq, opts ...svrkit.CallOption) (*CreateChannelByDisplayIDResp, error) {
	out := new(CreateChannelByDisplayIDResp)
	err := c.cc.Invoke(ctx, uin, commandChannelCreateChannelByDisplayID, "/Channel.Channel/CreateChannelByDisplayID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelDetailInfoBatch(ctx context.Context, uin uint32, in *GetChannelDetailInfoBatchReq, opts ...svrkit.CallOption) (*GetChannelDetailInfoBatchResp, error) {
	out := new(GetChannelDetailInfoBatchResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelDetailInfoBatch, "/Channel.Channel/GetChannelDetailInfoBatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) AllocTempChannel(ctx context.Context, uin uint32, in *AllocTempChannelReq, opts ...svrkit.CallOption) (*AllocTempChannelResp, error) {
	out := new(AllocTempChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelAllocTempChannel, "/Channel.Channel/AllocTempChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelWelcomeMsg(ctx context.Context, uin uint32, in *GetChannelWelcomeMsgReq, opts ...svrkit.CallOption) (*GetChannelWelcomeMsgResp, error) {
	out := new(GetChannelWelcomeMsgResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelWelcomeMsg, "/Channel.Channel/GetChannelWelcomeMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) DisableAllEmptyMicrSpace(ctx context.Context, uin uint32, in *DisableAllEmptyMicrSpaceReq, opts ...svrkit.CallOption) (*DisableAllEmptyMicrSpaceResp, error) {
	out := new(DisableAllEmptyMicrSpaceResp)
	err := c.cc.Invoke(ctx, uin, commandChannelDisableAllEmptyMicrSpace, "/Channel.Channel/DisableAllEmptyMicrSpace", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) AddConfigMusic(ctx context.Context, uin uint32, in *AddConfigMusicReq, opts ...svrkit.CallOption) (*AddConfigMusicResp, error) {
	out := new(AddConfigMusicResp)
	err := c.cc.Invoke(ctx, uin, commandChannelAddConfigMusic, "/Channel.Channel/AddConfigMusic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) DelConfigMusic(ctx context.Context, uin uint32, in *DelConfigMusicReq, opts ...svrkit.CallOption) (*DelConfigMusicResp, error) {
	out := new(DelConfigMusicResp)
	err := c.cc.Invoke(ctx, uin, commandChannelDelConfigMusic, "/Channel.Channel/DelConfigMusic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetConfigMusicList(ctx context.Context, uin uint32, in *GetConfigMusicListReq, opts ...svrkit.CallOption) (*GetConfigMusicListResp, error) {
	out := new(GetConfigMusicListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetConfigMusicList, "/Channel.Channel/GetConfigMusicList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelCreateTs(ctx context.Context, uin uint32, in *GetChannelCreateTsReq, opts ...svrkit.CallOption) (*GetChannelCreateTsResp, error) {
	out := new(GetChannelCreateTsResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelCreateTs, "/Channel.Channel/GetChannelCreateTs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) RecordSendGiftEvent(ctx context.Context, uin uint32, in *RecordSendGiftEventReq, opts ...svrkit.CallOption) (*RecordSendGiftEventResp, error) {
	out := new(RecordSendGiftEventResp)
	err := c.cc.Invoke(ctx, uin, commandChannelRecordSendGiftEvent, "/Channel.Channel/RecordSendGiftEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetConsumeTopN(ctx context.Context, uin uint32, in *GetConsumeTopNReq, opts ...svrkit.CallOption) (*GetConsumeTopNResp, error) {
	out := new(GetConsumeTopNResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetConsumeTopN, "/Channel.Channel/GetConsumeTopN", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) ChangeDisplayID(ctx context.Context, uin uint32, in *ChangeDisplayIDReq, opts ...svrkit.CallOption) (*ChangeDisplayIDResp, error) {
	out := new(ChangeDisplayIDResp)
	err := c.cc.Invoke(ctx, uin, commandChannelChangeDisplayID, "/Channel.Channel/ChangeDisplayID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelHistoryList(ctx context.Context, uin uint32, in *GetChannelHistoryListReq, opts ...svrkit.CallOption) (*GetChannelHistoryListResp, error) {
	out := new(GetChannelHistoryListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelHistoryList, "/Channel.Channel/GetChannelHistoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelStat(ctx context.Context, uin uint32, in *GetChannelStatReq, opts ...svrkit.CallOption) (*GetChannelStatResp, error) {
	out := new(GetChannelStatResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelStat, "/Channel.Channel/GetChannelStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelAdmin(ctx context.Context, uin uint32, in *GetChannelAdminReq, opts ...svrkit.CallOption) (*GetChannelAdminResp, error) {
	out := new(GetChannelAdminResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelAdmin, "/Channel.Channel/GetChannelAdmin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) SetChannelMicSpaceStatus(ctx context.Context, uin uint32, in *SetChannelMicSpaceStatusReq, opts ...svrkit.CallOption) (*SetChannelMicSpaceStatusResp, error) {
	out := new(SetChannelMicSpaceStatusResp)
	err := c.cc.Invoke(ctx, uin, commandChannelSetChannelMicSpaceStatus, "/Channel.Channel/SetChannelMicSpaceStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) ChangeMicrophone(ctx context.Context, uin uint32, in *ChangeMicrophoneReq, opts ...svrkit.CallOption) (*ChangeMicrophoneResp, error) {
	out := new(ChangeMicrophoneResp)
	err := c.cc.Invoke(ctx, uin, commandChannelChangeMicrophone, "/Channel.Channel/ChangeMicrophone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) BatchCheckIsTempAllocChannel(ctx context.Context, uin uint32, in *BatchCheckIsTempAllocChannelReq, opts ...svrkit.CallOption) (*BatchCheckIsTempAllocChannelResp, error) {
	out := new(BatchCheckIsTempAllocChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelBatchCheckIsTempAllocChannel, "/Channel.Channel/BatchCheckIsTempAllocChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelSimpleInfo(ctx context.Context, uin uint32, in *GetChannelSimpleInfoReq, opts ...svrkit.CallOption) (*GetChannelSimpleInfoResp, error) {
	out := new(GetChannelSimpleInfoResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelSimpleInfo, "/Channel.Channel/GetChannelSimpleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) BatchGetChannelSimpleInfo(ctx context.Context, uin uint32, in *BatchGetChannelSimpleInfoReq, opts ...svrkit.CallOption) (*BatchGetChannelSimpleInfoResp, error) {
	out := new(BatchGetChannelSimpleInfoResp)
	err := c.cc.Invoke(ctx, uin, commandChannelBatchGetChannelSimpleInfo, "/Channel.Channel/BatchGetChannelSimpleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) LiveConnectMicApply(ctx context.Context, uin uint32, in *LiveConnectMicApplyReq, opts ...svrkit.CallOption) (*LiveConnectMicApplyResp, error) {
	out := new(LiveConnectMicApplyResp)
	err := c.cc.Invoke(ctx, uin, commandChannelLiveConnectMicApply, "/Channel.Channel/LiveConnectMicApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) LiveConnectMicHandle(ctx context.Context, uin uint32, in *LiveConnectMicHandleReq, opts ...svrkit.CallOption) (*LiveConnectMicHandleResp, error) {
	out := new(LiveConnectMicHandleResp)
	err := c.cc.Invoke(ctx, uin, commandChannelLiveConnectMicHandle, "/Channel.Channel/LiveConnectMicHandle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetLiveConnectMicApplyUserList(ctx context.Context, uin uint32, in *GetLiveConnectMicApplyUserListReq, opts ...svrkit.CallOption) (*GetLiveConnectMicApplyUserListResp, error) {
	out := new(GetLiveConnectMicApplyUserListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetLiveConnectMicApplyUserList, "/Channel.Channel/GetLiveConnectMicApplyUserList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) FinishLive(ctx context.Context, uin uint32, in *FinishLiveReq, opts ...svrkit.CallOption) (*FinishLiveResp, error) {
	out := new(FinishLiveResp)
	err := c.cc.Invoke(ctx, uin, commandChannelFinishLive, "/Channel.Channel/FinishLive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) CheckIsLiveStarting(ctx context.Context, uin uint32, in *CheckIsLiveStartingReq, opts ...svrkit.CallOption) (*CheckIsLiveStartingResp, error) {
	out := new(CheckIsLiveStartingResp)
	err := c.cc.Invoke(ctx, uin, commandChannelCheckIsLiveStarting, "/Channel.Channel/CheckIsLiveStarting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) StartLive(ctx context.Context, uin uint32, in *StartLiveReq, opts ...svrkit.CallOption) (*StartLiveResp, error) {
	out := new(StartLiveResp)
	err := c.cc.Invoke(ctx, uin, commandChannelStartLive, "/Channel.Channel/StartLive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) CheckUserKickoutFromChannel(ctx context.Context, uin uint32, in *CheckUserKickoutFromChannelReq, opts ...svrkit.CallOption) (*CheckUserKickoutFromChannelResp, error) {
	out := new(CheckUserKickoutFromChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelCheckUserKickoutFromChannel, "/Channel.Channel/CheckUserKickoutFromChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetUserAccumulateConsume(ctx context.Context, uin uint32, in *GetUserAccumulateConsumeReq, opts ...svrkit.CallOption) (*GetUserAccumulateConsumeResp, error) {
	out := new(GetUserAccumulateConsumeResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetUserAccumulateConsume, "/Channel.Channel/GetUserAccumulateConsume", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) EnterLiveChannel(ctx context.Context, uin uint32, in *EnterLiveChannelReq, opts ...svrkit.CallOption) (*EnterLiveChannelResp, error) {
	out := new(EnterLiveChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelEnterLiveChannel, "/Channel.Channel/EnterLiveChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) QuitLiveChannel(ctx context.Context, uin uint32, in *QuitLiveChannelReq, opts ...svrkit.CallOption) (*QuitLiveChannelResp, error) {
	out := new(QuitLiveChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelQuitLiveChannel, "/Channel.Channel/QuitLiveChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) ReleaseTempChannel(ctx context.Context, uin uint32, in *ReleaseTempChannelReq, opts ...svrkit.CallOption) (*ReleaseTempChannelResp, error) {
	out := new(ReleaseTempChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelReleaseTempChannel, "/Channel.Channel/ReleaseTempChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) AddChannelAdmin(ctx context.Context, uin uint32, in *AddChannelAdminReq, opts ...svrkit.CallOption) (*AddChannelAdminResp, error) {
	out := new(AddChannelAdminResp)
	err := c.cc.Invoke(ctx, uin, commandChannelAddChannelAdmin, "/Channel.Channel/AddChannelAdmin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) RemoveChannelAdmin(ctx context.Context, uin uint32, in *RemoveChannelAdminReq, opts ...svrkit.CallOption) (*RemoveChannelAdminResp, error) {
	out := new(RemoveChannelAdminResp)
	err := c.cc.Invoke(ctx, uin, commandChannelRemoveChannelAdmin, "/Channel.Channel/RemoveChannelAdmin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) CreateChannelLite(ctx context.Context, uin uint32, in *CreateChannelLiteReq, opts ...svrkit.CallOption) (*CreateChannelLiteResp, error) {
	out := new(CreateChannelLiteResp)
	err := c.cc.Invoke(ctx, uin, commandChannelCreateChannelLite, "/Channel.Channel/CreateChannelLite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) BatchGetChannelBindType(ctx context.Context, uin uint32, in *BatchGetChannelBindTypeReq, opts ...svrkit.CallOption) (*BatchGetChannelBindTypeResp, error) {
	out := new(BatchGetChannelBindTypeResp)
	err := c.cc.Invoke(ctx, uin, commandChannelBatchGetChannelBindType, "/Channel.Channel/BatchGetChannelBindType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) MuteChannelMember(ctx context.Context, uin uint32, in *MuteChannelMemberReq, opts ...svrkit.CallOption) (*MuteChannelMemberResp, error) {
	out := new(MuteChannelMemberResp)
	err := c.cc.Invoke(ctx, uin, commandChannelMuteChannelMember, "/Channel.Channel/MuteChannelMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) UnmuteChannelMember(ctx context.Context, uin uint32, in *UnmuteChannelMemberReq, opts ...svrkit.CallOption) (*UnmuteChannelMemberResp, error) {
	out := new(UnmuteChannelMemberResp)
	err := c.cc.Invoke(ctx, uin, commandChannelUnmuteChannelMember, "/Channel.Channel/UnmuteChannelMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) KickoutChannelMemberLite(ctx context.Context, uin uint32, in *KickoutChannelMemberLiteReq, opts ...svrkit.CallOption) (*KickoutChannelMemberLiteResp, error) {
	out := new(KickoutChannelMemberLiteResp)
	err := c.cc.Invoke(ctx, uin, commandChannelKickoutChannelMemberLite, "/Channel.Channel/KickoutChannelMemberLite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) ChackAndRefixChannelOwner(ctx context.Context, uin uint32, in *ChackAndRefixChannelOwnerReq, opts ...svrkit.CallOption) (*ChackAndRefixChannelOwnerResp, error) {
	out := new(ChackAndRefixChannelOwnerResp)
	err := c.cc.Invoke(ctx, uin, commandChannelChackAndRefixChannelOwner, "/Channel.Channel/ChackAndRefixChannelOwner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) RefixChannelOwner(ctx context.Context, uin uint32, in *RefixChannelOwnerReq, opts ...svrkit.CallOption) (*RefixChannelOwnerResp, error) {
	out := new(RefixChannelOwnerResp)
	err := c.cc.Invoke(ctx, uin, commandChannelRefixChannelOwner, "/Channel.Channel/RefixChannelOwner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) AddChannelToWhiteList(ctx context.Context, uin uint32, in *AddChannelToWhiteListReq, opts ...svrkit.CallOption) (*AddChannelToWhiteListResp, error) {
	out := new(AddChannelToWhiteListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelAddChannelToWhiteList, "/Channel.Channel/AddChannelToWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) RemoveFromWhiteList(ctx context.Context, uin uint32, in *RemoveFromWhiteListReq, opts ...svrkit.CallOption) (*RemoveFromWhiteListResp, error) {
	out := new(RemoveFromWhiteListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelRemoveFromWhiteList, "/Channel.Channel/RemoveFromWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelClient) GetChannelWhiteList(ctx context.Context, uin uint32, in *GetChannelWhiteListReq, opts ...svrkit.CallOption) (*GetChannelWhiteListResp, error) {
	out := new(GetChannelWhiteListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGetChannelWhiteList, "/Channel.Channel/GetChannelWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
