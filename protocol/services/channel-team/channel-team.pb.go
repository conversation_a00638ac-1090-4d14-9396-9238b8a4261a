// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-team/channel-team.proto

package channel_team // import "golang.52tt.com/protocol/services/channel-team"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UpdateType int32

const (
	UpdateType_UPDATE UpdateType = 0
	UpdateType_CREATE UpdateType = 1
)

var UpdateType_name = map[int32]string{
	0: "UPDATE",
	1: "CREATE",
}
var UpdateType_value = map[string]int32{
	"UPDATE": 0,
	"CREATE": 1,
}

func (x UpdateType) String() string {
	return proto.EnumName(UpdateType_name, int32(x))
}
func (UpdateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{0}
}

type UiTypeType int32

const (
	UiTypeType_UNKOWN UiTypeType = 0
	UiTypeType_WZRY   UiTypeType = 1
	UiTypeType_HPJY   UiTypeType = 2
)

var UiTypeType_name = map[int32]string{
	0: "UNKOWN",
	1: "WZRY",
	2: "HPJY",
}
var UiTypeType_value = map[string]int32{
	"UNKOWN": 0,
	"WZRY":   1,
	"HPJY":   2,
}

func (x UiTypeType) String() string {
	return proto.EnumName(UiTypeType_name, int32(x))
}
func (UiTypeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{1}
}

type JoinChannelTeamReq_Scene int32

const (
	// 其他
	JoinChannelTeamReq_OTHER JoinChannelTeamReq_Scene = 0
	// 麦位抱上小队
	JoinChannelTeamReq_HOLD_ON JoinChannelTeamReq_Scene = 1
	// 选ta加入小队
	JoinChannelTeamReq_PICK_UP JoinChannelTeamReq_Scene = 2
)

var JoinChannelTeamReq_Scene_name = map[int32]string{
	0: "OTHER",
	1: "HOLD_ON",
	2: "PICK_UP",
}
var JoinChannelTeamReq_Scene_value = map[string]int32{
	"OTHER":   0,
	"HOLD_ON": 1,
	"PICK_UP": 2,
}

func (x JoinChannelTeamReq_Scene) String() string {
	return proto.EnumName(JoinChannelTeamReq_Scene_name, int32(x))
}
func (JoinChannelTeamReq_Scene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{0, 0}
}

// 用户进入小队
type JoinChannelTeamReq struct {
	Uid                  uint32                   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32                   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	JoinUid              uint32                   `protobuf:"varint,3,opt,name=join_uid,json=joinUid,proto3" json:"join_uid,omitempty"`
	LocationIndex        uint32                   `protobuf:"varint,4,opt,name=location_index,json=locationIndex,proto3" json:"location_index,omitempty"`
	LocationName         string                   `protobuf:"bytes,5,opt,name=location_name,json=locationName,proto3" json:"location_name,omitempty"`
	Type                 JoinChannelTeamReq_Scene `protobuf:"varint,6,opt,name=type,proto3,enum=channel_team.JoinChannelTeamReq_Scene" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *JoinChannelTeamReq) Reset()         { *m = JoinChannelTeamReq{} }
func (m *JoinChannelTeamReq) String() string { return proto.CompactTextString(m) }
func (*JoinChannelTeamReq) ProtoMessage()    {}
func (*JoinChannelTeamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{0}
}
func (m *JoinChannelTeamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelTeamReq.Unmarshal(m, b)
}
func (m *JoinChannelTeamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelTeamReq.Marshal(b, m, deterministic)
}
func (dst *JoinChannelTeamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelTeamReq.Merge(dst, src)
}
func (m *JoinChannelTeamReq) XXX_Size() int {
	return xxx_messageInfo_JoinChannelTeamReq.Size(m)
}
func (m *JoinChannelTeamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelTeamReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelTeamReq proto.InternalMessageInfo

func (m *JoinChannelTeamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *JoinChannelTeamReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *JoinChannelTeamReq) GetJoinUid() uint32 {
	if m != nil {
		return m.JoinUid
	}
	return 0
}

func (m *JoinChannelTeamReq) GetLocationIndex() uint32 {
	if m != nil {
		return m.LocationIndex
	}
	return 0
}

func (m *JoinChannelTeamReq) GetLocationName() string {
	if m != nil {
		return m.LocationName
	}
	return ""
}

func (m *JoinChannelTeamReq) GetType() JoinChannelTeamReq_Scene {
	if m != nil {
		return m.Type
	}
	return JoinChannelTeamReq_OTHER
}

type JoinChannelTeamResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinChannelTeamResp) Reset()         { *m = JoinChannelTeamResp{} }
func (m *JoinChannelTeamResp) String() string { return proto.CompactTextString(m) }
func (*JoinChannelTeamResp) ProtoMessage()    {}
func (*JoinChannelTeamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{1}
}
func (m *JoinChannelTeamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelTeamResp.Unmarshal(m, b)
}
func (m *JoinChannelTeamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelTeamResp.Marshal(b, m, deterministic)
}
func (dst *JoinChannelTeamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelTeamResp.Merge(dst, src)
}
func (m *JoinChannelTeamResp) XXX_Size() int {
	return xxx_messageInfo_JoinChannelTeamResp.Size(m)
}
func (m *JoinChannelTeamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelTeamResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelTeamResp proto.InternalMessageInfo

// 房主获取申请列表
type GetChannelTeamApplyListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelTeamApplyListReq) Reset()         { *m = GetChannelTeamApplyListReq{} }
func (m *GetChannelTeamApplyListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelTeamApplyListReq) ProtoMessage()    {}
func (*GetChannelTeamApplyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{2}
}
func (m *GetChannelTeamApplyListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTeamApplyListReq.Unmarshal(m, b)
}
func (m *GetChannelTeamApplyListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTeamApplyListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelTeamApplyListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTeamApplyListReq.Merge(dst, src)
}
func (m *GetChannelTeamApplyListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelTeamApplyListReq.Size(m)
}
func (m *GetChannelTeamApplyListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTeamApplyListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTeamApplyListReq proto.InternalMessageInfo

func (m *GetChannelTeamApplyListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelTeamApplyListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelTeamApplyListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetChannelTeamApplyListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetChannelTeamApplyListResp struct {
	ApplyList            []*ApplyMember `protobuf:"bytes,1,rep,name=apply_list,json=applyList,proto3" json:"apply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetChannelTeamApplyListResp) Reset()         { *m = GetChannelTeamApplyListResp{} }
func (m *GetChannelTeamApplyListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelTeamApplyListResp) ProtoMessage()    {}
func (*GetChannelTeamApplyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{3}
}
func (m *GetChannelTeamApplyListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTeamApplyListResp.Unmarshal(m, b)
}
func (m *GetChannelTeamApplyListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTeamApplyListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelTeamApplyListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTeamApplyListResp.Merge(dst, src)
}
func (m *GetChannelTeamApplyListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelTeamApplyListResp.Size(m)
}
func (m *GetChannelTeamApplyListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTeamApplyListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTeamApplyListResp proto.InternalMessageInfo

func (m *GetChannelTeamApplyListResp) GetApplyList() []*ApplyMember {
	if m != nil {
		return m.ApplyList
	}
	return nil
}

type ApplyMember struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Dan                  string   `protobuf:"bytes,5,opt,name=dan,proto3" json:"dan,omitempty"`
	DanUrl               string   `protobuf:"bytes,6,opt,name=dan_url,json=danUrl,proto3" json:"dan_url,omitempty"`
	Desc                 string   `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`
	LocationName         string   `protobuf:"bytes,8,opt,name=location_name,json=locationName,proto3" json:"location_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyMember) Reset()         { *m = ApplyMember{} }
func (m *ApplyMember) String() string { return proto.CompactTextString(m) }
func (*ApplyMember) ProtoMessage()    {}
func (*ApplyMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{4}
}
func (m *ApplyMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyMember.Unmarshal(m, b)
}
func (m *ApplyMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyMember.Marshal(b, m, deterministic)
}
func (dst *ApplyMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyMember.Merge(dst, src)
}
func (m *ApplyMember) XXX_Size() int {
	return xxx_messageInfo_ApplyMember.Size(m)
}
func (m *ApplyMember) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyMember.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyMember proto.InternalMessageInfo

func (m *ApplyMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyMember) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ApplyMember) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ApplyMember) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ApplyMember) GetDan() string {
	if m != nil {
		return m.Dan
	}
	return ""
}

func (m *ApplyMember) GetDanUrl() string {
	if m != nil {
		return m.DanUrl
	}
	return ""
}

func (m *ApplyMember) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ApplyMember) GetLocationName() string {
	if m != nil {
		return m.LocationName
	}
	return ""
}

// 房主同意用户申请入队
type AgreeChannelTeamApplyReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	JoinUid              uint32   `protobuf:"varint,3,opt,name=join_uid,json=joinUid,proto3" json:"join_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AgreeChannelTeamApplyReq) Reset()         { *m = AgreeChannelTeamApplyReq{} }
func (m *AgreeChannelTeamApplyReq) String() string { return proto.CompactTextString(m) }
func (*AgreeChannelTeamApplyReq) ProtoMessage()    {}
func (*AgreeChannelTeamApplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{5}
}
func (m *AgreeChannelTeamApplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AgreeChannelTeamApplyReq.Unmarshal(m, b)
}
func (m *AgreeChannelTeamApplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AgreeChannelTeamApplyReq.Marshal(b, m, deterministic)
}
func (dst *AgreeChannelTeamApplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AgreeChannelTeamApplyReq.Merge(dst, src)
}
func (m *AgreeChannelTeamApplyReq) XXX_Size() int {
	return xxx_messageInfo_AgreeChannelTeamApplyReq.Size(m)
}
func (m *AgreeChannelTeamApplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AgreeChannelTeamApplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_AgreeChannelTeamApplyReq proto.InternalMessageInfo

func (m *AgreeChannelTeamApplyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AgreeChannelTeamApplyReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AgreeChannelTeamApplyReq) GetJoinUid() uint32 {
	if m != nil {
		return m.JoinUid
	}
	return 0
}

type AgreeChannelTeamApplyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AgreeChannelTeamApplyResp) Reset()         { *m = AgreeChannelTeamApplyResp{} }
func (m *AgreeChannelTeamApplyResp) String() string { return proto.CompactTextString(m) }
func (*AgreeChannelTeamApplyResp) ProtoMessage()    {}
func (*AgreeChannelTeamApplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{6}
}
func (m *AgreeChannelTeamApplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AgreeChannelTeamApplyResp.Unmarshal(m, b)
}
func (m *AgreeChannelTeamApplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AgreeChannelTeamApplyResp.Marshal(b, m, deterministic)
}
func (dst *AgreeChannelTeamApplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AgreeChannelTeamApplyResp.Merge(dst, src)
}
func (m *AgreeChannelTeamApplyResp) XXX_Size() int {
	return xxx_messageInfo_AgreeChannelTeamApplyResp.Size(m)
}
func (m *AgreeChannelTeamApplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AgreeChannelTeamApplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_AgreeChannelTeamApplyResp proto.InternalMessageInfo

// 用户退出小队或者房主踢用户退出小队
type TickChannelTeamMemberReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TickUid              uint32   `protobuf:"varint,3,opt,name=tick_uid,json=tickUid,proto3" json:"tick_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TickChannelTeamMemberReq) Reset()         { *m = TickChannelTeamMemberReq{} }
func (m *TickChannelTeamMemberReq) String() string { return proto.CompactTextString(m) }
func (*TickChannelTeamMemberReq) ProtoMessage()    {}
func (*TickChannelTeamMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{7}
}
func (m *TickChannelTeamMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TickChannelTeamMemberReq.Unmarshal(m, b)
}
func (m *TickChannelTeamMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TickChannelTeamMemberReq.Marshal(b, m, deterministic)
}
func (dst *TickChannelTeamMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TickChannelTeamMemberReq.Merge(dst, src)
}
func (m *TickChannelTeamMemberReq) XXX_Size() int {
	return xxx_messageInfo_TickChannelTeamMemberReq.Size(m)
}
func (m *TickChannelTeamMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TickChannelTeamMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_TickChannelTeamMemberReq proto.InternalMessageInfo

func (m *TickChannelTeamMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TickChannelTeamMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TickChannelTeamMemberReq) GetTickUid() uint32 {
	if m != nil {
		return m.TickUid
	}
	return 0
}

type TickChannelTeamMemberResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TickChannelTeamMemberResp) Reset()         { *m = TickChannelTeamMemberResp{} }
func (m *TickChannelTeamMemberResp) String() string { return proto.CompactTextString(m) }
func (*TickChannelTeamMemberResp) ProtoMessage()    {}
func (*TickChannelTeamMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{8}
}
func (m *TickChannelTeamMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TickChannelTeamMemberResp.Unmarshal(m, b)
}
func (m *TickChannelTeamMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TickChannelTeamMemberResp.Marshal(b, m, deterministic)
}
func (dst *TickChannelTeamMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TickChannelTeamMemberResp.Merge(dst, src)
}
func (m *TickChannelTeamMemberResp) XXX_Size() int {
	return xxx_messageInfo_TickChannelTeamMemberResp.Size(m)
}
func (m *TickChannelTeamMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TickChannelTeamMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_TickChannelTeamMemberResp proto.InternalMessageInfo

// 获取小队成员信息
type GetChannelTeamMemberListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32   `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	LocationList         []string `protobuf:"bytes,4,rep,name=location_list,json=locationList,proto3" json:"location_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelTeamMemberListReq) Reset()         { *m = GetChannelTeamMemberListReq{} }
func (m *GetChannelTeamMemberListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelTeamMemberListReq) ProtoMessage()    {}
func (*GetChannelTeamMemberListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{9}
}
func (m *GetChannelTeamMemberListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTeamMemberListReq.Unmarshal(m, b)
}
func (m *GetChannelTeamMemberListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTeamMemberListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelTeamMemberListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTeamMemberListReq.Merge(dst, src)
}
func (m *GetChannelTeamMemberListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelTeamMemberListReq.Size(m)
}
func (m *GetChannelTeamMemberListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTeamMemberListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTeamMemberListReq proto.InternalMessageInfo

func (m *GetChannelTeamMemberListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelTeamMemberListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelTeamMemberListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetChannelTeamMemberListReq) GetLocationList() []string {
	if m != nil {
		return m.LocationList
	}
	return nil
}

type GetChannelTeamMemberListResp struct {
	// 房间成员信息
	Info                 *ChannelTeamInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetChannelTeamMemberListResp) Reset()         { *m = GetChannelTeamMemberListResp{} }
func (m *GetChannelTeamMemberListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelTeamMemberListResp) ProtoMessage()    {}
func (*GetChannelTeamMemberListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{10}
}
func (m *GetChannelTeamMemberListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTeamMemberListResp.Unmarshal(m, b)
}
func (m *GetChannelTeamMemberListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTeamMemberListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelTeamMemberListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTeamMemberListResp.Merge(dst, src)
}
func (m *GetChannelTeamMemberListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelTeamMemberListResp.Size(m)
}
func (m *GetChannelTeamMemberListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTeamMemberListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTeamMemberListResp proto.InternalMessageInfo

func (m *GetChannelTeamMemberListResp) GetInfo() *ChannelTeamInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 获取开黑过的用户列表
type GetGangUpHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGangUpHistoryReq) Reset()         { *m = GetGangUpHistoryReq{} }
func (m *GetGangUpHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetGangUpHistoryReq) ProtoMessage()    {}
func (*GetGangUpHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{11}
}
func (m *GetGangUpHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangUpHistoryReq.Unmarshal(m, b)
}
func (m *GetGangUpHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangUpHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetGangUpHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangUpHistoryReq.Merge(dst, src)
}
func (m *GetGangUpHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetGangUpHistoryReq.Size(m)
}
func (m *GetGangUpHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangUpHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangUpHistoryReq proto.InternalMessageInfo

func (m *GetGangUpHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGangUpHistoryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetGangUpHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGangUpHistoryReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetGangUpHistoryResp struct {
	Record               []*GangUpRecord `protobuf:"bytes,1,rep,name=record,proto3" json:"record,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetGangUpHistoryResp) Reset()         { *m = GetGangUpHistoryResp{} }
func (m *GetGangUpHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetGangUpHistoryResp) ProtoMessage()    {}
func (*GetGangUpHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{12}
}
func (m *GetGangUpHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangUpHistoryResp.Unmarshal(m, b)
}
func (m *GetGangUpHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangUpHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetGangUpHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangUpHistoryResp.Merge(dst, src)
}
func (m *GetGangUpHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetGangUpHistoryResp.Size(m)
}
func (m *GetGangUpHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangUpHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangUpHistoryResp proto.InternalMessageInfo

func (m *GetGangUpHistoryResp) GetRecord() []*GangUpRecord {
	if m != nil {
		return m.Record
	}
	return nil
}

type GangUpRecord struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UpdateTime           int64    `protobuf:"varint,2,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	GameNameList         []string `protobuf:"bytes,3,rep,name=game_name_list,json=gameNameList,proto3" json:"game_name_list,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Nickname             string   `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,6,opt,name=account,proto3" json:"account,omitempty"`
	IsOnline             bool     `protobuf:"varint,7,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GangUpRecord) Reset()         { *m = GangUpRecord{} }
func (m *GangUpRecord) String() string { return proto.CompactTextString(m) }
func (*GangUpRecord) ProtoMessage()    {}
func (*GangUpRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{13}
}
func (m *GangUpRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GangUpRecord.Unmarshal(m, b)
}
func (m *GangUpRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GangUpRecord.Marshal(b, m, deterministic)
}
func (dst *GangUpRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GangUpRecord.Merge(dst, src)
}
func (m *GangUpRecord) XXX_Size() int {
	return xxx_messageInfo_GangUpRecord.Size(m)
}
func (m *GangUpRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_GangUpRecord.DiscardUnknown(m)
}

var xxx_messageInfo_GangUpRecord proto.InternalMessageInfo

func (m *GangUpRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GangUpRecord) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *GangUpRecord) GetGameNameList() []string {
	if m != nil {
		return m.GameNameList
	}
	return nil
}

func (m *GangUpRecord) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GangUpRecord) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GangUpRecord) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GangUpRecord) GetIsOnline() bool {
	if m != nil {
		return m.IsOnline
	}
	return false
}

// 获取所有的房间用户
type GetAllChannelMemberReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllChannelMemberReq) Reset()         { *m = GetAllChannelMemberReq{} }
func (m *GetAllChannelMemberReq) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelMemberReq) ProtoMessage()    {}
func (*GetAllChannelMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{14}
}
func (m *GetAllChannelMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelMemberReq.Unmarshal(m, b)
}
func (m *GetAllChannelMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelMemberReq.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelMemberReq.Merge(dst, src)
}
func (m *GetAllChannelMemberReq) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelMemberReq.Size(m)
}
func (m *GetAllChannelMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelMemberReq proto.InternalMessageInfo

func (m *GetAllChannelMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAllChannelMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetAllChannelMemberReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAllChannelMemberReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetAllChannelMemberResp struct {
	MemberList           []*ChannelMember `protobuf:"bytes,1,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetAllChannelMemberResp) Reset()         { *m = GetAllChannelMemberResp{} }
func (m *GetAllChannelMemberResp) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelMemberResp) ProtoMessage()    {}
func (*GetAllChannelMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{15}
}
func (m *GetAllChannelMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelMemberResp.Unmarshal(m, b)
}
func (m *GetAllChannelMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelMemberResp.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelMemberResp.Merge(dst, src)
}
func (m *GetAllChannelMemberResp) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelMemberResp.Size(m)
}
func (m *GetAllChannelMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelMemberResp proto.InternalMessageInfo

func (m *GetAllChannelMemberResp) GetMemberList() []*ChannelMember {
	if m != nil {
		return m.MemberList
	}
	return nil
}

type ChannelMember struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Dan                  string   `protobuf:"bytes,5,opt,name=dan,proto3" json:"dan,omitempty"`
	DanUrl               string   `protobuf:"bytes,6,opt,name=dan_url,json=danUrl,proto3" json:"dan_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMember) Reset()         { *m = ChannelMember{} }
func (m *ChannelMember) String() string { return proto.CompactTextString(m) }
func (*ChannelMember) ProtoMessage()    {}
func (*ChannelMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{16}
}
func (m *ChannelMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMember.Unmarshal(m, b)
}
func (m *ChannelMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMember.Marshal(b, m, deterministic)
}
func (dst *ChannelMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMember.Merge(dst, src)
}
func (m *ChannelMember) XXX_Size() int {
	return xxx_messageInfo_ChannelMember.Size(m)
}
func (m *ChannelMember) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMember.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMember proto.InternalMessageInfo

func (m *ChannelMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMember) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ChannelMember) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ChannelMember) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ChannelMember) GetDan() string {
	if m != nil {
		return m.Dan
	}
	return ""
}

func (m *ChannelMember) GetDanUrl() string {
	if m != nil {
		return m.DanUrl
	}
	return ""
}

// 修改游戏
type ChangeTabIdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32   `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeTabIdReq) Reset()         { *m = ChangeTabIdReq{} }
func (m *ChangeTabIdReq) String() string { return proto.CompactTextString(m) }
func (*ChangeTabIdReq) ProtoMessage()    {}
func (*ChangeTabIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{17}
}
func (m *ChangeTabIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeTabIdReq.Unmarshal(m, b)
}
func (m *ChangeTabIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeTabIdReq.Marshal(b, m, deterministic)
}
func (dst *ChangeTabIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeTabIdReq.Merge(dst, src)
}
func (m *ChangeTabIdReq) XXX_Size() int {
	return xxx_messageInfo_ChangeTabIdReq.Size(m)
}
func (m *ChangeTabIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeTabIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeTabIdReq proto.InternalMessageInfo

func (m *ChangeTabIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChangeTabIdReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChangeTabIdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type ChangeTabIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeTabIdResp) Reset()         { *m = ChangeTabIdResp{} }
func (m *ChangeTabIdResp) String() string { return proto.CompactTextString(m) }
func (*ChangeTabIdResp) ProtoMessage()    {}
func (*ChangeTabIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{18}
}
func (m *ChangeTabIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeTabIdResp.Unmarshal(m, b)
}
func (m *ChangeTabIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeTabIdResp.Marshal(b, m, deterministic)
}
func (dst *ChangeTabIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeTabIdResp.Merge(dst, src)
}
func (m *ChangeTabIdResp) XXX_Size() int {
	return xxx_messageInfo_ChangeTabIdResp.Size(m)
}
func (m *ChangeTabIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeTabIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeTabIdResp proto.InternalMessageInfo

// 批量获取房间小队匹配要求信息，房主头像信息
type BatchGetChannelTeamInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelIdList        []uint32 `protobuf:"varint,2,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelTeamInfoReq) Reset()         { *m = BatchGetChannelTeamInfoReq{} }
func (m *BatchGetChannelTeamInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelTeamInfoReq) ProtoMessage()    {}
func (*BatchGetChannelTeamInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{19}
}
func (m *BatchGetChannelTeamInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelTeamInfoReq.Unmarshal(m, b)
}
func (m *BatchGetChannelTeamInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelTeamInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelTeamInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelTeamInfoReq.Merge(dst, src)
}
func (m *BatchGetChannelTeamInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelTeamInfoReq.Size(m)
}
func (m *BatchGetChannelTeamInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelTeamInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelTeamInfoReq proto.InternalMessageInfo

func (m *BatchGetChannelTeamInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetChannelTeamInfoReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatchGetChannelTeamInfoResp struct {
	InfoMap              map[uint32]*ChannelTeamInfo `protobuf:"bytes,1,rep,name=info_map,json=infoMap,proto3" json:"info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *BatchGetChannelTeamInfoResp) Reset()         { *m = BatchGetChannelTeamInfoResp{} }
func (m *BatchGetChannelTeamInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelTeamInfoResp) ProtoMessage()    {}
func (*BatchGetChannelTeamInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{20}
}
func (m *BatchGetChannelTeamInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelTeamInfoResp.Unmarshal(m, b)
}
func (m *BatchGetChannelTeamInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelTeamInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelTeamInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelTeamInfoResp.Merge(dst, src)
}
func (m *BatchGetChannelTeamInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelTeamInfoResp.Size(m)
}
func (m *BatchGetChannelTeamInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelTeamInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelTeamInfoResp proto.InternalMessageInfo

func (m *BatchGetChannelTeamInfoResp) GetInfoMap() map[uint32]*ChannelTeamInfo {
	if m != nil {
		return m.InfoMap
	}
	return nil
}

// 编辑小队详细匹配要求信息
type SetChannelTeamInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LocationList         []string `protobuf:"bytes,3,rep,name=location_list,json=locationList,proto3" json:"location_list,omitempty"`
	TeamName             string   `protobuf:"bytes,4,opt,name=team_name,json=teamName,proto3" json:"team_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelTeamInfoReq) Reset()         { *m = SetChannelTeamInfoReq{} }
func (m *SetChannelTeamInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelTeamInfoReq) ProtoMessage()    {}
func (*SetChannelTeamInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{21}
}
func (m *SetChannelTeamInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelTeamInfoReq.Unmarshal(m, b)
}
func (m *SetChannelTeamInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelTeamInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelTeamInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelTeamInfoReq.Merge(dst, src)
}
func (m *SetChannelTeamInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelTeamInfoReq.Size(m)
}
func (m *SetChannelTeamInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelTeamInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelTeamInfoReq proto.InternalMessageInfo

func (m *SetChannelTeamInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelTeamInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelTeamInfoReq) GetLocationList() []string {
	if m != nil {
		return m.LocationList
	}
	return nil
}

func (m *SetChannelTeamInfoReq) GetTeamName() string {
	if m != nil {
		return m.TeamName
	}
	return ""
}

type SetChannelTeamInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelTeamInfoResp) Reset()         { *m = SetChannelTeamInfoResp{} }
func (m *SetChannelTeamInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelTeamInfoResp) ProtoMessage()    {}
func (*SetChannelTeamInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{22}
}
func (m *SetChannelTeamInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelTeamInfoResp.Unmarshal(m, b)
}
func (m *SetChannelTeamInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelTeamInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelTeamInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelTeamInfoResp.Merge(dst, src)
}
func (m *SetChannelTeamInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelTeamInfoResp.Size(m)
}
func (m *SetChannelTeamInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelTeamInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelTeamInfoResp proto.InternalMessageInfo

// 获取位置配置
type GetLocationConfigReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tabId,proto3" json:"tabId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLocationConfigReq) Reset()         { *m = GetLocationConfigReq{} }
func (m *GetLocationConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetLocationConfigReq) ProtoMessage()    {}
func (*GetLocationConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{23}
}
func (m *GetLocationConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLocationConfigReq.Unmarshal(m, b)
}
func (m *GetLocationConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLocationConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetLocationConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLocationConfigReq.Merge(dst, src)
}
func (m *GetLocationConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetLocationConfigReq.Size(m)
}
func (m *GetLocationConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLocationConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLocationConfigReq proto.InternalMessageInfo

func (m *GetLocationConfigReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetLocationConfigResp struct {
	LocationConfigList   []*LocationConfig `protobuf:"bytes,1,rep,name=location_config_list,json=locationConfigList,proto3" json:"location_config_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetLocationConfigResp) Reset()         { *m = GetLocationConfigResp{} }
func (m *GetLocationConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetLocationConfigResp) ProtoMessage()    {}
func (*GetLocationConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{24}
}
func (m *GetLocationConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLocationConfigResp.Unmarshal(m, b)
}
func (m *GetLocationConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLocationConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetLocationConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLocationConfigResp.Merge(dst, src)
}
func (m *GetLocationConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetLocationConfigResp.Size(m)
}
func (m *GetLocationConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLocationConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLocationConfigResp proto.InternalMessageInfo

func (m *GetLocationConfigResp) GetLocationConfigList() []*LocationConfig {
	if m != nil {
		return m.LocationConfigList
	}
	return nil
}

type LocationConfig struct {
	LocationList         []*Location `protobuf:"bytes,1,rep,name=location_list,json=locationList,proto3" json:"location_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *LocationConfig) Reset()         { *m = LocationConfig{} }
func (m *LocationConfig) String() string { return proto.CompactTextString(m) }
func (*LocationConfig) ProtoMessage()    {}
func (*LocationConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{25}
}
func (m *LocationConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LocationConfig.Unmarshal(m, b)
}
func (m *LocationConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LocationConfig.Marshal(b, m, deterministic)
}
func (dst *LocationConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LocationConfig.Merge(dst, src)
}
func (m *LocationConfig) XXX_Size() int {
	return xxx_messageInfo_LocationConfig.Size(m)
}
func (m *LocationConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_LocationConfig.DiscardUnknown(m)
}

var xxx_messageInfo_LocationConfig proto.InternalMessageInfo

func (m *LocationConfig) GetLocationList() []*Location {
	if m != nil {
		return m.LocationList
	}
	return nil
}

type Location struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string   `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Location) Reset()         { *m = Location{} }
func (m *Location) String() string { return proto.CompactTextString(m) }
func (*Location) ProtoMessage()    {}
func (*Location) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{26}
}
func (m *Location) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Location.Unmarshal(m, b)
}
func (m *Location) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Location.Marshal(b, m, deterministic)
}
func (dst *Location) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Location.Merge(dst, src)
}
func (m *Location) XXX_Size() int {
	return xxx_messageInfo_Location.Size(m)
}
func (m *Location) XXX_DiscardUnknown() {
	xxx_messageInfo_Location.DiscardUnknown(m)
}

var xxx_messageInfo_Location proto.InternalMessageInfo

func (m *Location) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Location) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

// 设置房间小队昵称
type SetGameNicknameReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameNickname         string   `protobuf:"bytes,3,opt,name=game_nickname,json=gameNickname,proto3" json:"game_nickname,omitempty"`
	IpAddr               string   `protobuf:"bytes,4,opt,name=ip_addr,json=ipAddr,proto3" json:"ip_addr,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	SmDeviceId           []byte   `protobuf:"bytes,6,opt,name=sm_device_id,json=smDeviceId,proto3" json:"sm_device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameNicknameReq) Reset()         { *m = SetGameNicknameReq{} }
func (m *SetGameNicknameReq) String() string { return proto.CompactTextString(m) }
func (*SetGameNicknameReq) ProtoMessage()    {}
func (*SetGameNicknameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{27}
}
func (m *SetGameNicknameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameNicknameReq.Unmarshal(m, b)
}
func (m *SetGameNicknameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameNicknameReq.Marshal(b, m, deterministic)
}
func (dst *SetGameNicknameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameNicknameReq.Merge(dst, src)
}
func (m *SetGameNicknameReq) XXX_Size() int {
	return xxx_messageInfo_SetGameNicknameReq.Size(m)
}
func (m *SetGameNicknameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameNicknameReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameNicknameReq proto.InternalMessageInfo

func (m *SetGameNicknameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetGameNicknameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetGameNicknameReq) GetGameNickname() string {
	if m != nil {
		return m.GameNickname
	}
	return ""
}

func (m *SetGameNicknameReq) GetIpAddr() string {
	if m != nil {
		return m.IpAddr
	}
	return ""
}

func (m *SetGameNicknameReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *SetGameNicknameReq) GetSmDeviceId() []byte {
	if m != nil {
		return m.SmDeviceId
	}
	return nil
}

type SetGameNicknameResp struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameNicknameResp) Reset()         { *m = SetGameNicknameResp{} }
func (m *SetGameNicknameResp) String() string { return proto.CompactTextString(m) }
func (*SetGameNicknameResp) ProtoMessage()    {}
func (*SetGameNicknameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{28}
}
func (m *SetGameNicknameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameNicknameResp.Unmarshal(m, b)
}
func (m *SetGameNicknameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameNicknameResp.Marshal(b, m, deterministic)
}
func (dst *SetGameNicknameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameNicknameResp.Merge(dst, src)
}
func (m *SetGameNicknameResp) XXX_Size() int {
	return xxx_messageInfo_SetGameNicknameResp.Size(m)
}
func (m *SetGameNicknameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameNicknameResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameNicknameResp proto.InternalMessageInfo

func (m *SetGameNicknameResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

// 查询房间小队情况
type ChannelTeamInfo struct {
	// 房主uid
	OwnerUid uint32 `protobuf:"varint,1,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	// 房间成员信息
	MemberList []*MemberInfo `protobuf:"bytes,2,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	// 房间小队创建时间
	CreateTime int64 `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 房间小队ui类型，UiTypeType
	UiType               uint32   `protobuf:"varint,4,opt,name=ui_type,json=uiType,proto3" json:"ui_type,omitempty"`
	TeamName             string   `protobuf:"bytes,5,opt,name=team_name,json=teamName,proto3" json:"team_name,omitempty"`
	GameName             string   `protobuf:"bytes,6,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelTeamInfo) Reset()         { *m = ChannelTeamInfo{} }
func (m *ChannelTeamInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelTeamInfo) ProtoMessage()    {}
func (*ChannelTeamInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{29}
}
func (m *ChannelTeamInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTeamInfo.Unmarshal(m, b)
}
func (m *ChannelTeamInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTeamInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelTeamInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTeamInfo.Merge(dst, src)
}
func (m *ChannelTeamInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelTeamInfo.Size(m)
}
func (m *ChannelTeamInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTeamInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTeamInfo proto.InternalMessageInfo

func (m *ChannelTeamInfo) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

func (m *ChannelTeamInfo) GetMemberList() []*MemberInfo {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *ChannelTeamInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ChannelTeamInfo) GetUiType() uint32 {
	if m != nil {
		return m.UiType
	}
	return 0
}

func (m *ChannelTeamInfo) GetTeamName() string {
	if m != nil {
		return m.TeamName
	}
	return ""
}

func (m *ChannelTeamInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type MemberInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Dan                  string   `protobuf:"bytes,5,opt,name=dan,proto3" json:"dan,omitempty"`
	DanUrl               string   `protobuf:"bytes,6,opt,name=dan_url,json=danUrl,proto3" json:"dan_url,omitempty"`
	GameNickname         string   `protobuf:"bytes,7,opt,name=game_nickname,json=gameNickname,proto3" json:"game_nickname,omitempty"`
	LocationName         string   `protobuf:"bytes,8,opt,name=location_name,json=locationName,proto3" json:"location_name,omitempty"`
	LocationIcon         string   `protobuf:"bytes,9,opt,name=location_icon,json=locationIcon,proto3" json:"location_icon,omitempty"`
	Disabled             bool     `protobuf:"varint,10,opt,name=disabled,proto3" json:"disabled,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberInfo) Reset()         { *m = MemberInfo{} }
func (m *MemberInfo) String() string { return proto.CompactTextString(m) }
func (*MemberInfo) ProtoMessage()    {}
func (*MemberInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{30}
}
func (m *MemberInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberInfo.Unmarshal(m, b)
}
func (m *MemberInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberInfo.Marshal(b, m, deterministic)
}
func (dst *MemberInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberInfo.Merge(dst, src)
}
func (m *MemberInfo) XXX_Size() int {
	return xxx_messageInfo_MemberInfo.Size(m)
}
func (m *MemberInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MemberInfo proto.InternalMessageInfo

func (m *MemberInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MemberInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MemberInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *MemberInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *MemberInfo) GetDan() string {
	if m != nil {
		return m.Dan
	}
	return ""
}

func (m *MemberInfo) GetDanUrl() string {
	if m != nil {
		return m.DanUrl
	}
	return ""
}

func (m *MemberInfo) GetGameNickname() string {
	if m != nil {
		return m.GameNickname
	}
	return ""
}

func (m *MemberInfo) GetLocationName() string {
	if m != nil {
		return m.LocationName
	}
	return ""
}

func (m *MemberInfo) GetLocationIcon() string {
	if m != nil {
		return m.LocationIcon
	}
	return ""
}

func (m *MemberInfo) GetDisabled() bool {
	if m != nil {
		return m.Disabled
	}
	return false
}

type PushChannelTeamUpdate struct {
	ChannelId            uint32           `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelTeamInfo      *ChannelTeamInfo `protobuf:"bytes,2,opt,name=channel_team_info,json=channelTeamInfo,proto3" json:"channel_team_info,omitempty"`
	PushTime             int64            `protobuf:"varint,3,opt,name=push_time,json=pushTime,proto3" json:"push_time,omitempty"`
	Type                 uint32           `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PushChannelTeamUpdate) Reset()         { *m = PushChannelTeamUpdate{} }
func (m *PushChannelTeamUpdate) String() string { return proto.CompactTextString(m) }
func (*PushChannelTeamUpdate) ProtoMessage()    {}
func (*PushChannelTeamUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_ef60bd4ac55d7247, []int{31}
}
func (m *PushChannelTeamUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushChannelTeamUpdate.Unmarshal(m, b)
}
func (m *PushChannelTeamUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushChannelTeamUpdate.Marshal(b, m, deterministic)
}
func (dst *PushChannelTeamUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushChannelTeamUpdate.Merge(dst, src)
}
func (m *PushChannelTeamUpdate) XXX_Size() int {
	return xxx_messageInfo_PushChannelTeamUpdate.Size(m)
}
func (m *PushChannelTeamUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_PushChannelTeamUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_PushChannelTeamUpdate proto.InternalMessageInfo

func (m *PushChannelTeamUpdate) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PushChannelTeamUpdate) GetChannelTeamInfo() *ChannelTeamInfo {
	if m != nil {
		return m.ChannelTeamInfo
	}
	return nil
}

func (m *PushChannelTeamUpdate) GetPushTime() int64 {
	if m != nil {
		return m.PushTime
	}
	return 0
}

func (m *PushChannelTeamUpdate) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func init() {
	proto.RegisterType((*JoinChannelTeamReq)(nil), "channel_team.JoinChannelTeamReq")
	proto.RegisterType((*JoinChannelTeamResp)(nil), "channel_team.JoinChannelTeamResp")
	proto.RegisterType((*GetChannelTeamApplyListReq)(nil), "channel_team.GetChannelTeamApplyListReq")
	proto.RegisterType((*GetChannelTeamApplyListResp)(nil), "channel_team.GetChannelTeamApplyListResp")
	proto.RegisterType((*ApplyMember)(nil), "channel_team.ApplyMember")
	proto.RegisterType((*AgreeChannelTeamApplyReq)(nil), "channel_team.AgreeChannelTeamApplyReq")
	proto.RegisterType((*AgreeChannelTeamApplyResp)(nil), "channel_team.AgreeChannelTeamApplyResp")
	proto.RegisterType((*TickChannelTeamMemberReq)(nil), "channel_team.TickChannelTeamMemberReq")
	proto.RegisterType((*TickChannelTeamMemberResp)(nil), "channel_team.TickChannelTeamMemberResp")
	proto.RegisterType((*GetChannelTeamMemberListReq)(nil), "channel_team.GetChannelTeamMemberListReq")
	proto.RegisterType((*GetChannelTeamMemberListResp)(nil), "channel_team.GetChannelTeamMemberListResp")
	proto.RegisterType((*GetGangUpHistoryReq)(nil), "channel_team.GetGangUpHistoryReq")
	proto.RegisterType((*GetGangUpHistoryResp)(nil), "channel_team.GetGangUpHistoryResp")
	proto.RegisterType((*GangUpRecord)(nil), "channel_team.GangUpRecord")
	proto.RegisterType((*GetAllChannelMemberReq)(nil), "channel_team.GetAllChannelMemberReq")
	proto.RegisterType((*GetAllChannelMemberResp)(nil), "channel_team.GetAllChannelMemberResp")
	proto.RegisterType((*ChannelMember)(nil), "channel_team.ChannelMember")
	proto.RegisterType((*ChangeTabIdReq)(nil), "channel_team.ChangeTabIdReq")
	proto.RegisterType((*ChangeTabIdResp)(nil), "channel_team.ChangeTabIdResp")
	proto.RegisterType((*BatchGetChannelTeamInfoReq)(nil), "channel_team.BatchGetChannelTeamInfoReq")
	proto.RegisterType((*BatchGetChannelTeamInfoResp)(nil), "channel_team.BatchGetChannelTeamInfoResp")
	proto.RegisterMapType((map[uint32]*ChannelTeamInfo)(nil), "channel_team.BatchGetChannelTeamInfoResp.InfoMapEntry")
	proto.RegisterType((*SetChannelTeamInfoReq)(nil), "channel_team.SetChannelTeamInfoReq")
	proto.RegisterType((*SetChannelTeamInfoResp)(nil), "channel_team.SetChannelTeamInfoResp")
	proto.RegisterType((*GetLocationConfigReq)(nil), "channel_team.GetLocationConfigReq")
	proto.RegisterType((*GetLocationConfigResp)(nil), "channel_team.GetLocationConfigResp")
	proto.RegisterType((*LocationConfig)(nil), "channel_team.LocationConfig")
	proto.RegisterType((*Location)(nil), "channel_team.Location")
	proto.RegisterType((*SetGameNicknameReq)(nil), "channel_team.SetGameNicknameReq")
	proto.RegisterType((*SetGameNicknameResp)(nil), "channel_team.SetGameNicknameResp")
	proto.RegisterType((*ChannelTeamInfo)(nil), "channel_team.ChannelTeamInfo")
	proto.RegisterType((*MemberInfo)(nil), "channel_team.MemberInfo")
	proto.RegisterType((*PushChannelTeamUpdate)(nil), "channel_team.PushChannelTeamUpdate")
	proto.RegisterEnum("channel_team.UpdateType", UpdateType_name, UpdateType_value)
	proto.RegisterEnum("channel_team.UiTypeType", UiTypeType_name, UiTypeType_value)
	proto.RegisterEnum("channel_team.JoinChannelTeamReq_Scene", JoinChannelTeamReq_Scene_name, JoinChannelTeamReq_Scene_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelTeamClient is the client API for ChannelTeam service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelTeamClient interface {
	JoinChannelTeam(ctx context.Context, in *JoinChannelTeamReq, opts ...grpc.CallOption) (*JoinChannelTeamResp, error)
	GetChannelTeamApplyList(ctx context.Context, in *GetChannelTeamApplyListReq, opts ...grpc.CallOption) (*GetChannelTeamApplyListResp, error)
	AgreeChannelTeamApply(ctx context.Context, in *AgreeChannelTeamApplyReq, opts ...grpc.CallOption) (*AgreeChannelTeamApplyResp, error)
	TickChannelTeamMember(ctx context.Context, in *TickChannelTeamMemberReq, opts ...grpc.CallOption) (*TickChannelTeamMemberResp, error)
	GetChannelTeamMemberList(ctx context.Context, in *GetChannelTeamMemberListReq, opts ...grpc.CallOption) (*GetChannelTeamMemberListResp, error)
	GetGangUpHistory(ctx context.Context, in *GetGangUpHistoryReq, opts ...grpc.CallOption) (*GetGangUpHistoryResp, error)
	GetAllChannelMember(ctx context.Context, in *GetAllChannelMemberReq, opts ...grpc.CallOption) (*GetAllChannelMemberResp, error)
	ChangeTabId(ctx context.Context, in *ChangeTabIdReq, opts ...grpc.CallOption) (*ChangeTabIdResp, error)
	BatchGetChannelTeamInfo(ctx context.Context, in *BatchGetChannelTeamInfoReq, opts ...grpc.CallOption) (*BatchGetChannelTeamInfoResp, error)
	SetChannelTeamInfo(ctx context.Context, in *SetChannelTeamInfoReq, opts ...grpc.CallOption) (*SetChannelTeamInfoResp, error)
	GetLocationConfig(ctx context.Context, in *GetLocationConfigReq, opts ...grpc.CallOption) (*GetLocationConfigResp, error)
	SetGameNickname(ctx context.Context, in *SetGameNicknameReq, opts ...grpc.CallOption) (*SetGameNicknameResp, error)
}

type channelTeamClient struct {
	cc *grpc.ClientConn
}

func NewChannelTeamClient(cc *grpc.ClientConn) ChannelTeamClient {
	return &channelTeamClient{cc}
}

func (c *channelTeamClient) JoinChannelTeam(ctx context.Context, in *JoinChannelTeamReq, opts ...grpc.CallOption) (*JoinChannelTeamResp, error) {
	out := new(JoinChannelTeamResp)
	err := c.cc.Invoke(ctx, "/channel_team.ChannelTeam/JoinChannelTeam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamClient) GetChannelTeamApplyList(ctx context.Context, in *GetChannelTeamApplyListReq, opts ...grpc.CallOption) (*GetChannelTeamApplyListResp, error) {
	out := new(GetChannelTeamApplyListResp)
	err := c.cc.Invoke(ctx, "/channel_team.ChannelTeam/GetChannelTeamApplyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamClient) AgreeChannelTeamApply(ctx context.Context, in *AgreeChannelTeamApplyReq, opts ...grpc.CallOption) (*AgreeChannelTeamApplyResp, error) {
	out := new(AgreeChannelTeamApplyResp)
	err := c.cc.Invoke(ctx, "/channel_team.ChannelTeam/AgreeChannelTeamApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamClient) TickChannelTeamMember(ctx context.Context, in *TickChannelTeamMemberReq, opts ...grpc.CallOption) (*TickChannelTeamMemberResp, error) {
	out := new(TickChannelTeamMemberResp)
	err := c.cc.Invoke(ctx, "/channel_team.ChannelTeam/TickChannelTeamMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamClient) GetChannelTeamMemberList(ctx context.Context, in *GetChannelTeamMemberListReq, opts ...grpc.CallOption) (*GetChannelTeamMemberListResp, error) {
	out := new(GetChannelTeamMemberListResp)
	err := c.cc.Invoke(ctx, "/channel_team.ChannelTeam/GetChannelTeamMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamClient) GetGangUpHistory(ctx context.Context, in *GetGangUpHistoryReq, opts ...grpc.CallOption) (*GetGangUpHistoryResp, error) {
	out := new(GetGangUpHistoryResp)
	err := c.cc.Invoke(ctx, "/channel_team.ChannelTeam/GetGangUpHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamClient) GetAllChannelMember(ctx context.Context, in *GetAllChannelMemberReq, opts ...grpc.CallOption) (*GetAllChannelMemberResp, error) {
	out := new(GetAllChannelMemberResp)
	err := c.cc.Invoke(ctx, "/channel_team.ChannelTeam/GetAllChannelMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamClient) ChangeTabId(ctx context.Context, in *ChangeTabIdReq, opts ...grpc.CallOption) (*ChangeTabIdResp, error) {
	out := new(ChangeTabIdResp)
	err := c.cc.Invoke(ctx, "/channel_team.ChannelTeam/ChangeTabId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamClient) BatchGetChannelTeamInfo(ctx context.Context, in *BatchGetChannelTeamInfoReq, opts ...grpc.CallOption) (*BatchGetChannelTeamInfoResp, error) {
	out := new(BatchGetChannelTeamInfoResp)
	err := c.cc.Invoke(ctx, "/channel_team.ChannelTeam/BatchGetChannelTeamInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamClient) SetChannelTeamInfo(ctx context.Context, in *SetChannelTeamInfoReq, opts ...grpc.CallOption) (*SetChannelTeamInfoResp, error) {
	out := new(SetChannelTeamInfoResp)
	err := c.cc.Invoke(ctx, "/channel_team.ChannelTeam/SetChannelTeamInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamClient) GetLocationConfig(ctx context.Context, in *GetLocationConfigReq, opts ...grpc.CallOption) (*GetLocationConfigResp, error) {
	out := new(GetLocationConfigResp)
	err := c.cc.Invoke(ctx, "/channel_team.ChannelTeam/GetLocationConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamClient) SetGameNickname(ctx context.Context, in *SetGameNicknameReq, opts ...grpc.CallOption) (*SetGameNicknameResp, error) {
	out := new(SetGameNicknameResp)
	err := c.cc.Invoke(ctx, "/channel_team.ChannelTeam/SetGameNickname", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelTeamServer is the server API for ChannelTeam service.
type ChannelTeamServer interface {
	JoinChannelTeam(context.Context, *JoinChannelTeamReq) (*JoinChannelTeamResp, error)
	GetChannelTeamApplyList(context.Context, *GetChannelTeamApplyListReq) (*GetChannelTeamApplyListResp, error)
	AgreeChannelTeamApply(context.Context, *AgreeChannelTeamApplyReq) (*AgreeChannelTeamApplyResp, error)
	TickChannelTeamMember(context.Context, *TickChannelTeamMemberReq) (*TickChannelTeamMemberResp, error)
	GetChannelTeamMemberList(context.Context, *GetChannelTeamMemberListReq) (*GetChannelTeamMemberListResp, error)
	GetGangUpHistory(context.Context, *GetGangUpHistoryReq) (*GetGangUpHistoryResp, error)
	GetAllChannelMember(context.Context, *GetAllChannelMemberReq) (*GetAllChannelMemberResp, error)
	ChangeTabId(context.Context, *ChangeTabIdReq) (*ChangeTabIdResp, error)
	BatchGetChannelTeamInfo(context.Context, *BatchGetChannelTeamInfoReq) (*BatchGetChannelTeamInfoResp, error)
	SetChannelTeamInfo(context.Context, *SetChannelTeamInfoReq) (*SetChannelTeamInfoResp, error)
	GetLocationConfig(context.Context, *GetLocationConfigReq) (*GetLocationConfigResp, error)
	SetGameNickname(context.Context, *SetGameNicknameReq) (*SetGameNicknameResp, error)
}

func RegisterChannelTeamServer(s *grpc.Server, srv ChannelTeamServer) {
	s.RegisterService(&_ChannelTeam_serviceDesc, srv)
}

func _ChannelTeam_JoinChannelTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinChannelTeamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamServer).JoinChannelTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_team.ChannelTeam/JoinChannelTeam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamServer).JoinChannelTeam(ctx, req.(*JoinChannelTeamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeam_GetChannelTeamApplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelTeamApplyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamServer).GetChannelTeamApplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_team.ChannelTeam/GetChannelTeamApplyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamServer).GetChannelTeamApplyList(ctx, req.(*GetChannelTeamApplyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeam_AgreeChannelTeamApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AgreeChannelTeamApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamServer).AgreeChannelTeamApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_team.ChannelTeam/AgreeChannelTeamApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamServer).AgreeChannelTeamApply(ctx, req.(*AgreeChannelTeamApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeam_TickChannelTeamMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TickChannelTeamMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamServer).TickChannelTeamMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_team.ChannelTeam/TickChannelTeamMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamServer).TickChannelTeamMember(ctx, req.(*TickChannelTeamMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeam_GetChannelTeamMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelTeamMemberListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamServer).GetChannelTeamMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_team.ChannelTeam/GetChannelTeamMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamServer).GetChannelTeamMemberList(ctx, req.(*GetChannelTeamMemberListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeam_GetGangUpHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGangUpHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamServer).GetGangUpHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_team.ChannelTeam/GetGangUpHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamServer).GetGangUpHistory(ctx, req.(*GetGangUpHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeam_GetAllChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamServer).GetAllChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_team.ChannelTeam/GetAllChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamServer).GetAllChannelMember(ctx, req.(*GetAllChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeam_ChangeTabId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeTabIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamServer).ChangeTabId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_team.ChannelTeam/ChangeTabId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamServer).ChangeTabId(ctx, req.(*ChangeTabIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeam_BatchGetChannelTeamInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelTeamInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamServer).BatchGetChannelTeamInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_team.ChannelTeam/BatchGetChannelTeamInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamServer).BatchGetChannelTeamInfo(ctx, req.(*BatchGetChannelTeamInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeam_SetChannelTeamInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelTeamInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamServer).SetChannelTeamInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_team.ChannelTeam/SetChannelTeamInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamServer).SetChannelTeamInfo(ctx, req.(*SetChannelTeamInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeam_GetLocationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLocationConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamServer).GetLocationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_team.ChannelTeam/GetLocationConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamServer).GetLocationConfig(ctx, req.(*GetLocationConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeam_SetGameNickname_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGameNicknameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamServer).SetGameNickname(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_team.ChannelTeam/SetGameNickname",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamServer).SetGameNickname(ctx, req.(*SetGameNicknameReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelTeam_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_team.ChannelTeam",
	HandlerType: (*ChannelTeamServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "JoinChannelTeam",
			Handler:    _ChannelTeam_JoinChannelTeam_Handler,
		},
		{
			MethodName: "GetChannelTeamApplyList",
			Handler:    _ChannelTeam_GetChannelTeamApplyList_Handler,
		},
		{
			MethodName: "AgreeChannelTeamApply",
			Handler:    _ChannelTeam_AgreeChannelTeamApply_Handler,
		},
		{
			MethodName: "TickChannelTeamMember",
			Handler:    _ChannelTeam_TickChannelTeamMember_Handler,
		},
		{
			MethodName: "GetChannelTeamMemberList",
			Handler:    _ChannelTeam_GetChannelTeamMemberList_Handler,
		},
		{
			MethodName: "GetGangUpHistory",
			Handler:    _ChannelTeam_GetGangUpHistory_Handler,
		},
		{
			MethodName: "GetAllChannelMember",
			Handler:    _ChannelTeam_GetAllChannelMember_Handler,
		},
		{
			MethodName: "ChangeTabId",
			Handler:    _ChannelTeam_ChangeTabId_Handler,
		},
		{
			MethodName: "BatchGetChannelTeamInfo",
			Handler:    _ChannelTeam_BatchGetChannelTeamInfo_Handler,
		},
		{
			MethodName: "SetChannelTeamInfo",
			Handler:    _ChannelTeam_SetChannelTeamInfo_Handler,
		},
		{
			MethodName: "GetLocationConfig",
			Handler:    _ChannelTeam_GetLocationConfig_Handler,
		},
		{
			MethodName: "SetGameNickname",
			Handler:    _ChannelTeam_SetGameNickname_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-team/channel-team.proto",
}

func init() {
	proto.RegisterFile("channel-team/channel-team.proto", fileDescriptor_channel_team_ef60bd4ac55d7247)
}

var fileDescriptor_channel_team_ef60bd4ac55d7247 = []byte{
	// 1530 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x58, 0x5b, 0x73, 0xd4, 0xc6,
	0x12, 0xb6, 0xf6, 0xbe, 0x6d, 0x63, 0x2f, 0x83, 0x2f, 0x8b, 0x0c, 0xc5, 0x22, 0x0c, 0xf8, 0x50,
	0x9c, 0xe5, 0x1c, 0x53, 0xe7, 0x14, 0x21, 0x79, 0x31, 0xc6, 0x65, 0x2f, 0x18, 0xdb, 0xc8, 0xde,
	0x70, 0x49, 0x55, 0x54, 0xb2, 0x34, 0x5e, 0x0f, 0xd6, 0x2d, 0x1a, 0x2d, 0x64, 0x2b, 0xff, 0x20,
	0xcf, 0xa9, 0x3c, 0xe5, 0x57, 0xe4, 0x27, 0xe4, 0x21, 0x2f, 0xc9, 0x5f, 0xc8, 0x7f, 0x49, 0xcd,
	0x8c, 0xb4, 0xd6, 0x6d, 0xbd, 0x1b, 0x87, 0x4a, 0xf1, 0x36, 0xd3, 0xd3, 0x33, 0xdd, 0xea, 0xfe,
	0xfa, 0x26, 0xb8, 0x61, 0x9c, 0xe8, 0x8e, 0x83, 0xad, 0x7f, 0x07, 0x58, 0xb7, 0x1f, 0xc4, 0x37,
	0x6d, 0xcf, 0x77, 0x03, 0x17, 0xcd, 0x84, 0x34, 0x8d, 0xd1, 0x94, 0x1f, 0x0b, 0x80, 0x9e, 0xb9,
	0xc4, 0xd9, 0x10, 0xc4, 0x43, 0xac, 0xdb, 0x2a, 0xfe, 0x06, 0x35, 0xa0, 0xd8, 0x27, 0x66, 0x53,
	0x6a, 0x49, 0xab, 0x97, 0x54, 0xb6, 0x44, 0xd7, 0x01, 0xa2, 0x8b, 0xc4, 0x6c, 0x16, 0xf8, 0x41,
	0x3d, 0xa4, 0x74, 0x4c, 0x74, 0x15, 0x6a, 0xef, 0x5c, 0xe2, 0x68, 0xec, 0x56, 0x91, 0x1f, 0x56,
	0xd9, 0xbe, 0x4b, 0x4c, 0x74, 0x1b, 0x66, 0x2d, 0xd7, 0xd0, 0x03, 0xe2, 0x3a, 0x1a, 0x71, 0x4c,
	0xfc, 0x6d, 0xb3, 0xc4, 0x19, 0x2e, 0x45, 0xd4, 0x0e, 0x23, 0xa2, 0x5b, 0x30, 0x24, 0x68, 0x8e,
	0x6e, 0xe3, 0x66, 0xb9, 0x25, 0xad, 0xd6, 0xd5, 0x99, 0x88, 0xb8, 0xab, 0xdb, 0x18, 0x3d, 0x86,
	0x52, 0x30, 0xf0, 0x70, 0xb3, 0xd2, 0x92, 0x56, 0x67, 0xd7, 0xee, 0xb4, 0xe3, 0xdf, 0xd2, 0xce,
	0x7e, 0x47, 0xfb, 0xc0, 0xc0, 0x0e, 0x56, 0xf9, 0x1d, 0xe5, 0x3e, 0x94, 0xf9, 0x16, 0xd5, 0xa1,
	0xbc, 0x77, 0xb8, 0xbd, 0xa9, 0x36, 0xa6, 0xd0, 0x34, 0x54, 0xb7, 0xf7, 0x76, 0x9e, 0x6a, 0x7b,
	0xbb, 0x0d, 0x89, 0x6d, 0xf6, 0x3b, 0x1b, 0xcf, 0xb5, 0xee, 0x7e, 0xa3, 0xa0, 0x2c, 0xc0, 0x95,
	0xcc, 0x7b, 0xd4, 0x53, 0xbe, 0x03, 0x79, 0x0b, 0x07, 0x31, 0xea, 0xba, 0xe7, 0x59, 0x83, 0x1d,
	0x42, 0x83, 0x0b, 0x99, 0x6d, 0x11, 0x2a, 0xee, 0xf1, 0x31, 0xc5, 0x41, 0x68, 0xb4, 0x70, 0x87,
	0xe6, 0xa1, 0x6c, 0xb8, 0x7d, 0x27, 0x08, 0x4d, 0x25, 0x36, 0xca, 0x2b, 0x58, 0x1e, 0x29, 0x9c,
	0x7a, 0xe8, 0x11, 0x80, 0xce, 0x08, 0x9a, 0x45, 0x68, 0xd0, 0x94, 0x5a, 0xc5, 0xd5, 0xe9, 0xb5,
	0xab, 0x49, 0x13, 0xf1, 0x0b, 0x2f, 0xb0, 0x7d, 0x84, 0x7d, 0xb5, 0xae, 0x47, 0xb7, 0x95, 0xdf,
	0x24, 0x98, 0x8e, 0x1d, 0xe5, 0x7c, 0x47, 0x13, 0xaa, 0xba, 0x21, 0x54, 0x2a, 0x70, 0xbf, 0x44,
	0x5b, 0x24, 0x43, 0xcd, 0x21, 0xc6, 0x29, 0x77, 0x59, 0x91, 0x1f, 0x0d, 0xf7, 0xec, 0x1d, 0x1a,
	0xfa, 0xbb, 0xac, 0xb2, 0x25, 0xa3, 0x98, 0xba, 0x13, 0xfa, 0x96, 0x2d, 0xd1, 0x12, 0x54, 0x4d,
	0xdd, 0xd1, 0xfa, 0xbe, 0xc5, 0xbd, 0x5a, 0x57, 0x2b, 0xa6, 0xee, 0x74, 0x7d, 0x0b, 0x21, 0x28,
	0x99, 0x98, 0x1a, 0xcd, 0x2a, 0xa7, 0xf2, 0x75, 0x16, 0x24, 0xb5, 0x2c, 0x48, 0x94, 0x63, 0x68,
	0xae, 0xf7, 0x7c, 0x8c, 0xd3, 0x86, 0xfa, 0xc8, 0xc0, 0x56, 0x96, 0xe1, 0xea, 0x08, 0x39, 0xd4,
	0x63, 0x4a, 0x1c, 0x12, 0xe3, 0x34, 0x76, 0x16, 0x9a, 0xfd, 0x82, 0x4a, 0x04, 0xc4, 0x38, 0x8d,
	0x2b, 0xc1, 0xf6, 0xa1, 0x12, 0x23, 0xe4, 0x50, 0x4f, 0xf9, 0x5e, 0x4a, 0x23, 0x46, 0x1c, 0x5e,
	0x18, 0xaf, 0x0b, 0x50, 0x09, 0xf4, 0x23, 0x6d, 0xa8, 0x46, 0x39, 0xd0, 0x8f, 0x3a, 0x66, 0xc2,
	0x2d, 0x1c, 0x7c, 0xa5, 0x56, 0x31, 0xee, 0x16, 0x0e, 0xb2, 0x97, 0x70, 0x6d, 0xb4, 0x2e, 0xd4,
	0x43, 0xff, 0x85, 0x12, 0x71, 0x8e, 0x5d, 0xae, 0xcd, 0xf4, 0xda, 0xf5, 0x24, 0x70, 0x63, 0xd7,
	0x3a, 0xce, 0xb1, 0xab, 0x72, 0x56, 0x25, 0x80, 0x2b, 0x5b, 0x38, 0xd8, 0xd2, 0x9d, 0x5e, 0xd7,
	0xdb, 0x26, 0x34, 0x70, 0xfd, 0xc1, 0x3f, 0x10, 0x86, 0xcf, 0x60, 0x3e, 0x2b, 0x95, 0x7a, 0x68,
	0x0d, 0x2a, 0x3e, 0x36, 0x5c, 0xdf, 0x0c, 0x63, 0x4f, 0x4e, 0x7e, 0x82, 0xb8, 0xa0, 0x72, 0x0e,
	0x35, 0xe4, 0x54, 0x7e, 0x97, 0x60, 0x26, 0x7e, 0x90, 0xa3, 0xfb, 0x0d, 0x98, 0xee, 0x7b, 0xa6,
	0x1e, 0x60, 0x2d, 0x20, 0x36, 0xe6, 0xca, 0x17, 0x55, 0x10, 0xa4, 0x43, 0x62, 0x63, 0xb4, 0x02,
	0xb3, 0x3d, 0xdd, 0xc6, 0x3c, 0x20, 0x84, 0xf9, 0x8b, 0xc2, 0xfc, 0x8c, 0xca, 0x22, 0x82, 0x99,
	0x38, 0x27, 0x16, 0xe3, 0x91, 0x5b, 0x4e, 0x45, 0x6e, 0x2c, 0xde, 0x2b, 0xc9, 0x78, 0x5f, 0x86,
	0x3a, 0xa1, 0x9a, 0xeb, 0x58, 0xc4, 0xc1, 0x3c, 0x36, 0x6b, 0x6a, 0x8d, 0xd0, 0x3d, 0xbe, 0x57,
	0x3e, 0xc0, 0xe2, 0x16, 0x0e, 0xd6, 0x2d, 0x2b, 0xf4, 0xd7, 0xdf, 0xc0, 0xfc, 0x5f, 0x4d, 0x8d,
	0x4b, 0xb9, 0x82, 0xa9, 0x87, 0xbe, 0x80, 0x69, 0x9b, 0xef, 0xe2, 0x79, 0x71, 0x39, 0x17, 0x5e,
	0xe1, 0x2d, 0xb0, 0x87, 0xc8, 0x54, 0x7e, 0x90, 0xe0, 0x52, 0xe2, 0xf4, 0x93, 0x48, 0x8e, 0xca,
	0x6b, 0x98, 0x65, 0x5a, 0xf5, 0xf0, 0x21, 0x0b, 0xc0, 0x8f, 0x18, 0xcb, 0xca, 0x65, 0x98, 0x4b,
	0xbc, 0x4c, 0x3d, 0xe5, 0x4b, 0x90, 0x9f, 0xe8, 0x81, 0x71, 0x92, 0x0c, 0x5f, 0x1e, 0x87, 0xb9,
	0x82, 0xef, 0xc0, 0xdc, 0x99, 0x60, 0x61, 0xf5, 0x42, 0xab, 0xc8, 0x4a, 0xfe, 0x50, 0x7a, 0x54,
	0x76, 0x96, 0x47, 0x3e, 0x4c, 0x3d, 0xf4, 0x12, 0x6a, 0x2c, 0xcc, 0x35, 0x5b, 0xf7, 0x42, 0xb7,
	0xfd, 0x3f, 0xe9, 0xb6, 0x73, 0x2e, 0xb7, 0xd9, 0xe2, 0x85, 0xee, 0x6d, 0x3a, 0x81, 0x3f, 0x50,
	0xab, 0x44, 0xec, 0xe4, 0x37, 0x30, 0x13, 0x3f, 0x60, 0xca, 0x9f, 0xe2, 0x41, 0xa4, 0xfc, 0x29,
	0x1e, 0xa0, 0x87, 0x50, 0x7e, 0xaf, 0x5b, 0x7d, 0x11, 0x68, 0x63, 0xf3, 0x90, 0xe0, 0x7d, 0x5c,
	0x78, 0x24, 0xb1, 0x64, 0xbb, 0x70, 0x30, 0xa1, 0x85, 0xc6, 0xb8, 0x26, 0x93, 0x4f, 0x8b, 0xd9,
	0x7c, 0xca, 0x02, 0x91, 0xa9, 0x24, 0xea, 0x60, 0x49, 0x80, 0x8b, 0x11, 0x78, 0x0d, 0x6c, 0xc2,
	0xe2, 0x41, 0xae, 0x5d, 0x94, 0xfb, 0x3c, 0x7b, 0xed, 0x84, 0x2f, 0x6d, 0xb8, 0xce, 0x31, 0xe9,
	0x31, 0x25, 0xe7, 0x41, 0x00, 0x20, 0x54, 0x33, 0x44, 0x43, 0x0f, 0x16, 0x72, 0xb8, 0xa9, 0x87,
	0x76, 0x61, 0x7e, 0xa8, 0xa2, 0xc1, 0xc9, 0xf1, 0xf0, 0xba, 0x96, 0xb4, 0x5a, 0xea, 0x3e, 0xb2,
	0x12, 0x7b, 0x8e, 0x85, 0x17, 0x30, 0x9b, 0xe4, 0x42, 0x9f, 0xa7, 0x8d, 0x20, 0x9e, 0x5e, 0xcc,
	0x7f, 0x3a, 0x55, 0x6c, 0xd6, 0xa0, 0x16, 0x9d, 0xb0, 0x46, 0x82, 0xdb, 0x48, 0x12, 0x8d, 0x04,
	0x0f, 0x3e, 0x04, 0x25, 0x62, 0xb8, 0x4e, 0x18, 0xaf, 0x7c, 0xad, 0xfc, 0x22, 0x01, 0x3a, 0x60,
	0x89, 0xdd, 0xc6, 0xbb, 0x61, 0x90, 0x5e, 0xd4, 0x7b, 0x22, 0x1f, 0x27, 0x23, 0x5f, 0xa4, 0xe3,
	0x28, 0xfa, 0x97, 0xa0, 0x4a, 0x3c, 0x4d, 0x37, 0x4d, 0x3f, 0xf4, 0x5d, 0x85, 0x78, 0xeb, 0xa6,
	0xe9, 0x33, 0xb7, 0x9a, 0xf8, 0x3d, 0x31, 0x30, 0x7b, 0x9b, 0xa5, 0x82, 0x19, 0xb5, 0x26, 0x08,
	0x1d, 0x13, 0xb5, 0x60, 0x86, 0xda, 0xda, 0xd9, 0x79, 0x85, 0x9f, 0x03, 0xb5, 0x9f, 0x86, 0x1c,
	0xca, 0x5d, 0xb8, 0x92, 0xf9, 0x06, 0xea, 0xb1, 0x8f, 0xb0, 0x69, 0x2f, 0x34, 0x01, 0x5b, 0x2a,
	0x7f, 0x48, 0x22, 0xd0, 0x63, 0xf8, 0x60, 0xb2, 0xdd, 0x0f, 0x0e, 0xf6, 0xb5, 0xb3, 0x0f, 0xae,
	0x71, 0x02, 0xeb, 0xe3, 0x3f, 0x4b, 0xe6, 0xd1, 0x02, 0xf7, 0x46, 0x33, 0xe9, 0x0d, 0x91, 0x22,
	0x39, 0xd6, 0x62, 0x49, 0x94, 0x95, 0x30, 0xc3, 0xc7, 0xc3, 0x12, 0x56, 0x14, 0x25, 0x4c, 0x90,
	0x78, 0x09, 0x5b, 0x82, 0x6a, 0x9f, 0x68, 0xbc, 0xb5, 0x17, 0x69, 0xbd, 0xd2, 0x27, 0x87, 0x03,
	0x0f, 0x27, 0x41, 0x5e, 0x4e, 0x82, 0x9c, 0x1d, 0x0e, 0x0b, 0x5f, 0x98, 0x1f, 0x6b, 0x51, 0xcd,
	0x53, 0x7e, 0x2a, 0x00, 0x9c, 0xa9, 0xf3, 0x69, 0xb4, 0xb4, 0x19, 0x64, 0x54, 0x73, 0x90, 0x31,
	0x49, 0x8f, 0x9b, 0x60, 0xe2, 0x40, 0xae, 0x27, 0x99, 0x3a, 0x86, 0xeb, 0xb0, 0xef, 0x30, 0x09,
	0xd5, 0x8f, 0x2c, 0x6c, 0x36, 0x41, 0x54, 0xea, 0x68, 0xaf, 0xfc, 0x2c, 0xc1, 0xc2, 0x7e, 0x9f,
	0x9e, 0xc4, 0x20, 0xd0, 0xe5, 0x2d, 0x45, 0x0a, 0xdd, 0x52, 0x1a, 0xdd, 0x1d, 0xb8, 0x1c, 0x77,
	0xb9, 0xc6, 0x7b, 0xb6, 0x89, 0x72, 0x65, 0x54, 0x14, 0xe2, 0x70, 0xf3, 0xfa, 0xf4, 0x24, 0x0e,
	0x8a, 0x1a, 0x23, 0x70, 0x48, 0xa0, 0x70, 0xd4, 0x13, 0x78, 0xe0, 0xeb, 0x7b, 0x2b, 0x00, 0x42,
	0x49, 0x8e, 0x0d, 0x80, 0x4a, 0x77, 0xff, 0xe9, 0xfa, 0xe1, 0x66, 0x63, 0x8a, 0xad, 0x37, 0xd4,
	0x4d, 0xb6, 0x96, 0xee, 0xdd, 0x07, 0xe8, 0x72, 0xf4, 0x0c, 0xb9, 0x76, 0x9f, 0xef, 0xbd, 0xda,
	0x6d, 0x4c, 0xa1, 0x1a, 0x94, 0x5e, 0xbd, 0x55, 0xdf, 0x34, 0x24, 0xb6, 0xda, 0xde, 0x7f, 0xf6,
	0xa6, 0x51, 0x58, 0xfb, 0xb5, 0x0e, 0xd3, 0x31, 0x4d, 0xd1, 0x6b, 0x98, 0x4b, 0x0d, 0x7e, 0xa8,
	0x35, 0x6e, 0xce, 0x94, 0x6f, 0x8e, 0xe1, 0xa0, 0x9e, 0x32, 0x85, 0x3c, 0xde, 0xa3, 0xe4, 0x8d,
	0x6f, 0x68, 0x35, 0xd5, 0x2a, 0x8e, 0x1c, 0x31, 0xe5, 0x7f, 0x4d, 0xc8, 0xc9, 0x25, 0xbe, 0x83,
	0x85, 0xdc, 0x09, 0x05, 0xa5, 0x26, 0xe7, 0x51, 0xe3, 0x92, 0x7c, 0x77, 0x22, 0xbe, 0x48, 0x56,
	0xee, 0x20, 0x92, 0x96, 0x35, 0x6a, 0x2a, 0x4a, 0xcb, 0x1a, 0x3d, 0xd5, 0x4c, 0x21, 0x0a, 0xcd,
	0x51, 0xa3, 0x04, 0x3a, 0xd7, 0x40, 0x89, 0xf1, 0x47, 0xbe, 0x37, 0x29, 0x2b, 0x17, 0xfa, 0x15,
	0x34, 0xd2, 0x6d, 0x3f, 0xba, 0x99, 0x79, 0x21, 0x3d, 0x8c, 0xc8, 0xca, 0x38, 0x16, 0xfe, 0xb8,
	0xc9, 0x27, 0x99, 0x74, 0xff, 0x8a, 0x56, 0x32, 0x97, 0x73, 0x7a, 0x6b, 0xf9, 0xf6, 0x04, 0x5c,
	0x5c, 0xca, 0x8e, 0x80, 0x7a, 0xd8, 0xdb, 0xa1, 0x6b, 0xd9, 0x78, 0x3d, 0x6b, 0x28, 0xe5, 0xeb,
	0xe7, 0x9c, 0x46, 0x78, 0x1e, 0xd1, 0x80, 0xa5, 0xf1, 0x3c, 0xba, 0x7b, 0x4c, 0xe3, 0xf9, 0x9c,
	0x8e, 0x4e, 0x99, 0x42, 0x3a, 0x2f, 0xd0, 0x69, 0x61, 0xb7, 0x92, 0x4f, 0xe4, 0xf6, 0x60, 0xf2,
	0xca, 0x78, 0x26, 0x2e, 0xe2, 0x6b, 0xb8, 0x9c, 0x69, 0x78, 0x50, 0xd6, 0x87, 0x99, 0xfe, 0x49,
	0xbe, 0x35, 0x96, 0x87, 0xbf, 0xff, 0x1a, 0xe6, 0x52, 0xf5, 0x39, 0x9d, 0x5e, 0xb2, 0x2d, 0x48,
	0x3a, 0xbd, 0xe4, 0x14, 0x78, 0x65, 0xea, 0xc9, 0x7f, 0xde, 0xb6, 0x7b, 0xae, 0xa5, 0x3b, 0xbd,
	0xf6, 0xff, 0xd6, 0x82, 0xa0, 0x6d, 0xb8, 0xf6, 0x03, 0xfe, 0xc7, 0xcf, 0x70, 0xad, 0x07, 0x14,
	0xfb, 0xac, 0x3f, 0xa0, 0x89, 0x1f, 0x82, 0x47, 0x15, 0x7e, 0xfe, 0xf0, 0xcf, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xec, 0xe2, 0x95, 0xe5, 0x34, 0x14, 0x00, 0x00,
}
