// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/contract-http-logic/contract-http-logic.proto

package contract_http_logic // import "golang.52tt.com/protocol/services/contract-http-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ProofContent_ProofType int32

const (
	ProofContent_ProofType_Invalid ProofContent_ProofType = 0
	ProofContent_ProofType_Video   ProofContent_ProofType = 1
	ProofContent_ProofType_Image   ProofContent_ProofType = 2
)

var ProofContent_ProofType_name = map[int32]string{
	0: "ProofType_Invalid",
	1: "ProofType_Video",
	2: "ProofType_Image",
}
var ProofContent_ProofType_value = map[string]int32{
	"ProofType_Invalid": 0,
	"ProofType_Video":   1,
	"ProofType_Image":   2,
}

func (x ProofContent_ProofType) String() string {
	return proto.EnumName(ProofContent_ProofType_name, int32(x))
}
func (ProofContent_ProofType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{26, 0}
}

type ProofShowContent_ProofType int32

const (
	ProofShowContent_ProofType_Invalid ProofShowContent_ProofType = 0
	ProofShowContent_ProofType_Video   ProofShowContent_ProofType = 1
	ProofShowContent_ProofType_Image   ProofShowContent_ProofType = 2
)

var ProofShowContent_ProofType_name = map[int32]string{
	0: "ProofType_Invalid",
	1: "ProofType_Video",
	2: "ProofType_Image",
}
var ProofShowContent_ProofType_value = map[string]int32{
	"ProofType_Invalid": 0,
	"ProofType_Video":   1,
	"ProofType_Image":   2,
}

func (x ProofShowContent_ProofType) String() string {
	return proto.EnumName(ProofShowContent_ProofType_name, int32(x))
}
func (ProofShowContent_ProofType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{27, 0}
}

type CheckCanCancelContractResp_CancelStage int32

const (
	CheckCanCancelContractResp_CancelContractTypeDefault                         CheckCanCancelContractResp_CancelStage = 0
	CheckCanCancelContractResp_CancelContractTypeWaitingForMaster                CheckCanCancelContractResp_CancelStage = 1
	CheckCanCancelContractResp_CancelContractTypeWaitingForOfficial              CheckCanCancelContractResp_CancelStage = 2
	CheckCanCancelContractResp_CancelContractTypeWaitingForMasterQuiet           CheckCanCancelContractResp_CancelStage = 3
	CheckCanCancelContractResp_CancelContractTypeWaitingForMasterNoReason        CheckCanCancelContractResp_CancelStage = 4
	CheckCanCancelContractResp_CancelContractTypeWaitingForMasterLive            CheckCanCancelContractResp_CancelStage = 5
	CheckCanCancelContractResp_CancelContractTypeWaitingForOfficialPay           CheckCanCancelContractResp_CancelStage = 6
	CheckCanCancelContractResp_CancelContractTypeWaitingForMasterPayAccept       CheckCanCancelContractResp_CancelStage = 7
	CheckCanCancelContractResp_CancelContractTypeWaitingForMasterNegotiateAccept CheckCanCancelContractResp_CancelStage = 8
)

var CheckCanCancelContractResp_CancelStage_name = map[int32]string{
	0: "CancelContractTypeDefault",
	1: "CancelContractTypeWaitingForMaster",
	2: "CancelContractTypeWaitingForOfficial",
	3: "CancelContractTypeWaitingForMasterQuiet",
	4: "CancelContractTypeWaitingForMasterNoReason",
	5: "CancelContractTypeWaitingForMasterLive",
	6: "CancelContractTypeWaitingForOfficialPay",
	7: "CancelContractTypeWaitingForMasterPayAccept",
	8: "CancelContractTypeWaitingForMasterNegotiateAccept",
}
var CheckCanCancelContractResp_CancelStage_value = map[string]int32{
	"CancelContractTypeDefault":                         0,
	"CancelContractTypeWaitingForMaster":                1,
	"CancelContractTypeWaitingForOfficial":              2,
	"CancelContractTypeWaitingForMasterQuiet":           3,
	"CancelContractTypeWaitingForMasterNoReason":        4,
	"CancelContractTypeWaitingForMasterLive":            5,
	"CancelContractTypeWaitingForOfficialPay":           6,
	"CancelContractTypeWaitingForMasterPayAccept":       7,
	"CancelContractTypeWaitingForMasterNegotiateAccept": 8,
}

func (x CheckCanCancelContractResp_CancelStage) String() string {
	return proto.EnumName(CheckCanCancelContractResp_CancelStage_name, int32(x))
}
func (CheckCanCancelContractResp_CancelStage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{32, 0}
}

// 处理结果
type ProcPromoteInviteReq_ProcResType int32

const (
	ProcPromoteInviteReq_ProcResType_Invalid  ProcPromoteInviteReq_ProcResType = 0
	ProcPromoteInviteReq_ProcResType_Agree    ProcPromoteInviteReq_ProcResType = 1
	ProcPromoteInviteReq_ProcResType_NO_Agree ProcPromoteInviteReq_ProcResType = 2
)

var ProcPromoteInviteReq_ProcResType_name = map[int32]string{
	0: "ProcResType_Invalid",
	1: "ProcResType_Agree",
	2: "ProcResType_NO_Agree",
}
var ProcPromoteInviteReq_ProcResType_value = map[string]int32{
	"ProcResType_Invalid":  0,
	"ProcResType_Agree":    1,
	"ProcResType_NO_Agree": 2,
}

func (x ProcPromoteInviteReq_ProcResType) String() string {
	return proto.EnumName(ProcPromoteInviteReq_ProcResType_name, int32(x))
}
func (ProcPromoteInviteReq_ProcResType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{52, 0}
}

type GetHallTaskResp struct {
	DayTaskList          []*HallTaskDetial `protobuf:"bytes,1,rep,name=day_task_list,json=dayTaskList,proto3" json:"day_task_list,omitempty"`
	WeekTaskList         []*HallTaskDetial `protobuf:"bytes,2,rep,name=week_task_list,json=weekTaskList,proto3" json:"week_task_list,omitempty"`
	RewardMsg            string            `protobuf:"bytes,3,opt,name=reward_msg,json=rewardMsg,proto3" json:"reward_msg,omitempty"`
	IsShow               bool              `protobuf:"varint,4,opt,name=is_show,json=isShow,proto3" json:"is_show,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetHallTaskResp) Reset()         { *m = GetHallTaskResp{} }
func (m *GetHallTaskResp) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskResp) ProtoMessage()    {}
func (*GetHallTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{0}
}
func (m *GetHallTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskResp.Unmarshal(m, b)
}
func (m *GetHallTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskResp.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskResp.Merge(dst, src)
}
func (m *GetHallTaskResp) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskResp.Size(m)
}
func (m *GetHallTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskResp proto.InternalMessageInfo

func (m *GetHallTaskResp) GetDayTaskList() []*HallTaskDetial {
	if m != nil {
		return m.DayTaskList
	}
	return nil
}

func (m *GetHallTaskResp) GetWeekTaskList() []*HallTaskDetial {
	if m != nil {
		return m.WeekTaskList
	}
	return nil
}

func (m *GetHallTaskResp) GetRewardMsg() string {
	if m != nil {
		return m.RewardMsg
	}
	return ""
}

func (m *GetHallTaskResp) GetIsShow() bool {
	if m != nil {
		return m.IsShow
	}
	return false
}

type HallTaskDetial struct {
	TaskName             string   `protobuf:"bytes,1,opt,name=task_name,json=taskName,proto3" json:"task_name,omitempty"`
	TaskProgress         string   `protobuf:"bytes,2,opt,name=task_progress,json=taskProgress,proto3" json:"task_progress,omitempty"`
	Rate                 float32  `protobuf:"fixed32,3,opt,name=rate,proto3" json:"rate,omitempty"`
	TaskVal              uint32   `protobuf:"varint,4,opt,name=task_val,json=taskVal,proto3" json:"task_val,omitempty"`
	ValList              []string `protobuf:"bytes,5,rep,name=val_list,json=valList,proto3" json:"val_list,omitempty"`
	Date                 string   `protobuf:"bytes,6,opt,name=date,proto3" json:"date,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HallTaskDetial) Reset()         { *m = HallTaskDetial{} }
func (m *HallTaskDetial) String() string { return proto.CompactTextString(m) }
func (*HallTaskDetial) ProtoMessage()    {}
func (*HallTaskDetial) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{1}
}
func (m *HallTaskDetial) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HallTaskDetial.Unmarshal(m, b)
}
func (m *HallTaskDetial) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HallTaskDetial.Marshal(b, m, deterministic)
}
func (dst *HallTaskDetial) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HallTaskDetial.Merge(dst, src)
}
func (m *HallTaskDetial) XXX_Size() int {
	return xxx_messageInfo_HallTaskDetial.Size(m)
}
func (m *HallTaskDetial) XXX_DiscardUnknown() {
	xxx_messageInfo_HallTaskDetial.DiscardUnknown(m)
}

var xxx_messageInfo_HallTaskDetial proto.InternalMessageInfo

func (m *HallTaskDetial) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *HallTaskDetial) GetTaskProgress() string {
	if m != nil {
		return m.TaskProgress
	}
	return ""
}

func (m *HallTaskDetial) GetRate() float32 {
	if m != nil {
		return m.Rate
	}
	return 0
}

func (m *HallTaskDetial) GetTaskVal() uint32 {
	if m != nil {
		return m.TaskVal
	}
	return 0
}

func (m *HallTaskDetial) GetValList() []string {
	if m != nil {
		return m.ValList
	}
	return nil
}

func (m *HallTaskDetial) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

type GetHallTaskHistoryResp struct {
	List                 []*HallTaskHistoryDetial `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetHallTaskHistoryResp) Reset()         { *m = GetHallTaskHistoryResp{} }
func (m *GetHallTaskHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskHistoryResp) ProtoMessage()    {}
func (*GetHallTaskHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{2}
}
func (m *GetHallTaskHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskHistoryResp.Unmarshal(m, b)
}
func (m *GetHallTaskHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskHistoryResp.Merge(dst, src)
}
func (m *GetHallTaskHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskHistoryResp.Size(m)
}
func (m *GetHallTaskHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskHistoryResp proto.InternalMessageInfo

func (m *GetHallTaskHistoryResp) GetList() []*HallTaskHistoryDetial {
	if m != nil {
		return m.List
	}
	return nil
}

type HallTaskHistoryDetial struct {
	Date                 string            `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	DayTaskList          []*HallTaskDetial `protobuf:"bytes,2,rep,name=day_task_list,json=dayTaskList,proto3" json:"day_task_list,omitempty"`
	WeekTaskList         []*HallTaskDetial `protobuf:"bytes,3,rep,name=week_task_list,json=weekTaskList,proto3" json:"week_task_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *HallTaskHistoryDetial) Reset()         { *m = HallTaskHistoryDetial{} }
func (m *HallTaskHistoryDetial) String() string { return proto.CompactTextString(m) }
func (*HallTaskHistoryDetial) ProtoMessage()    {}
func (*HallTaskHistoryDetial) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{3}
}
func (m *HallTaskHistoryDetial) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HallTaskHistoryDetial.Unmarshal(m, b)
}
func (m *HallTaskHistoryDetial) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HallTaskHistoryDetial.Marshal(b, m, deterministic)
}
func (dst *HallTaskHistoryDetial) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HallTaskHistoryDetial.Merge(dst, src)
}
func (m *HallTaskHistoryDetial) XXX_Size() int {
	return xxx_messageInfo_HallTaskHistoryDetial.Size(m)
}
func (m *HallTaskHistoryDetial) XXX_DiscardUnknown() {
	xxx_messageInfo_HallTaskHistoryDetial.DiscardUnknown(m)
}

var xxx_messageInfo_HallTaskHistoryDetial proto.InternalMessageInfo

func (m *HallTaskHistoryDetial) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *HallTaskHistoryDetial) GetDayTaskList() []*HallTaskDetial {
	if m != nil {
		return m.DayTaskList
	}
	return nil
}

func (m *HallTaskHistoryDetial) GetWeekTaskList() []*HallTaskDetial {
	if m != nil {
		return m.WeekTaskList
	}
	return nil
}

type GetESportCoachInfoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetESportCoachInfoReq) Reset()         { *m = GetESportCoachInfoReq{} }
func (m *GetESportCoachInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetESportCoachInfoReq) ProtoMessage()    {}
func (*GetESportCoachInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{4}
}
func (m *GetESportCoachInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportCoachInfoReq.Unmarshal(m, b)
}
func (m *GetESportCoachInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportCoachInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetESportCoachInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportCoachInfoReq.Merge(dst, src)
}
func (m *GetESportCoachInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetESportCoachInfoReq.Size(m)
}
func (m *GetESportCoachInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportCoachInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportCoachInfoReq proto.InternalMessageInfo

func (m *GetESportCoachInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetESportCoachInfoReq) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

type UserSkillItem struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	GameIcon             string   `protobuf:"bytes,3,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon,omitempty"`
	GameType             uint32   `protobuf:"varint,4,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	TypeSort             uint32   `protobuf:"varint,5,opt,name=type_sort,json=typeSort,proto3" json:"type_sort,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSkillItem) Reset()         { *m = UserSkillItem{} }
func (m *UserSkillItem) String() string { return proto.CompactTextString(m) }
func (*UserSkillItem) ProtoMessage()    {}
func (*UserSkillItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{5}
}
func (m *UserSkillItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSkillItem.Unmarshal(m, b)
}
func (m *UserSkillItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSkillItem.Marshal(b, m, deterministic)
}
func (dst *UserSkillItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSkillItem.Merge(dst, src)
}
func (m *UserSkillItem) XXX_Size() int {
	return xxx_messageInfo_UserSkillItem.Size(m)
}
func (m *UserSkillItem) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSkillItem.DiscardUnknown(m)
}

var xxx_messageInfo_UserSkillItem proto.InternalMessageInfo

func (m *UserSkillItem) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserSkillItem) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *UserSkillItem) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

func (m *UserSkillItem) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *UserSkillItem) GetTypeSort() uint32 {
	if m != nil {
		return m.TypeSort
	}
	return 0
}

type ESportMonthData struct {
	OrderAmount          uint64   `protobuf:"varint,11,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount,omitempty"`
	TotalOrders          uint64   `protobuf:"varint,12,opt,name=total_orders,json=totalOrders,proto3" json:"total_orders,omitempty"`
	TakeOrderDays        uint32   `protobuf:"varint,13,opt,name=take_order_days,json=takeOrderDays,proto3" json:"take_order_days,omitempty"`
	ActiveDays           uint32   `protobuf:"varint,14,opt,name=active_days,json=activeDays,proto3" json:"active_days,omitempty"`
	PeopleServed         uint32   `protobuf:"varint,15,opt,name=people_served,json=peopleServed,proto3" json:"people_served,omitempty"`
	NewCustomers         uint32   `protobuf:"varint,16,opt,name=new_customers,json=newCustomers,proto3" json:"new_customers,omitempty"`
	Repurchases          uint32   `protobuf:"varint,17,opt,name=repurchases,proto3" json:"repurchases,omitempty"`
	ViolationsA          uint32   `protobuf:"varint,18,opt,name=violations_a,json=violationsA,proto3" json:"violations_a,omitempty"`
	ViolationsB          uint32   `protobuf:"varint,19,opt,name=violations_b,json=violationsB,proto3" json:"violations_b,omitempty"`
	ViolationsC          uint32   `protobuf:"varint,20,opt,name=violations_c,json=violationsC,proto3" json:"violations_c,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ESportMonthData) Reset()         { *m = ESportMonthData{} }
func (m *ESportMonthData) String() string { return proto.CompactTextString(m) }
func (*ESportMonthData) ProtoMessage()    {}
func (*ESportMonthData) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{6}
}
func (m *ESportMonthData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ESportMonthData.Unmarshal(m, b)
}
func (m *ESportMonthData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ESportMonthData.Marshal(b, m, deterministic)
}
func (dst *ESportMonthData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ESportMonthData.Merge(dst, src)
}
func (m *ESportMonthData) XXX_Size() int {
	return xxx_messageInfo_ESportMonthData.Size(m)
}
func (m *ESportMonthData) XXX_DiscardUnknown() {
	xxx_messageInfo_ESportMonthData.DiscardUnknown(m)
}

var xxx_messageInfo_ESportMonthData proto.InternalMessageInfo

func (m *ESportMonthData) GetOrderAmount() uint64 {
	if m != nil {
		return m.OrderAmount
	}
	return 0
}

func (m *ESportMonthData) GetTotalOrders() uint64 {
	if m != nil {
		return m.TotalOrders
	}
	return 0
}

func (m *ESportMonthData) GetTakeOrderDays() uint32 {
	if m != nil {
		return m.TakeOrderDays
	}
	return 0
}

func (m *ESportMonthData) GetActiveDays() uint32 {
	if m != nil {
		return m.ActiveDays
	}
	return 0
}

func (m *ESportMonthData) GetPeopleServed() uint32 {
	if m != nil {
		return m.PeopleServed
	}
	return 0
}

func (m *ESportMonthData) GetNewCustomers() uint32 {
	if m != nil {
		return m.NewCustomers
	}
	return 0
}

func (m *ESportMonthData) GetRepurchases() uint32 {
	if m != nil {
		return m.Repurchases
	}
	return 0
}

func (m *ESportMonthData) GetViolationsA() uint32 {
	if m != nil {
		return m.ViolationsA
	}
	return 0
}

func (m *ESportMonthData) GetViolationsB() uint32 {
	if m != nil {
		return m.ViolationsB
	}
	return 0
}

func (m *ESportMonthData) GetViolationsC() uint32 {
	if m != nil {
		return m.ViolationsC
	}
	return 0
}

type GetESportCoachInfoResp struct {
	GuildId           uint32 `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildShortId      uint32 `protobuf:"varint,2,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id,omitempty"`
	GuildName         string `protobuf:"bytes,3,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	CoachUid          uint32 `protobuf:"varint,4,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	CoachTtid         string `protobuf:"bytes,5,opt,name=coach_ttid,json=coachTtid,proto3" json:"coach_ttid,omitempty"`
	CoachNick         string `protobuf:"bytes,6,opt,name=coach_nick,json=coachNick,proto3" json:"coach_nick,omitempty"`
	ContractBeginTime int64  `protobuf:"varint,7,opt,name=contract_begin_time,json=contractBeginTime,proto3" json:"contract_begin_time,omitempty"`
	ContractEndTime   int64  `protobuf:"varint,8,opt,name=contract_end_time,json=contractEndTime,proto3" json:"contract_end_time,omitempty"`
	// 技能
	SkillList []*UserSkillItem `protobuf:"bytes,9,rep,name=skill_list,json=skillList,proto3" json:"skill_list,omitempty"`
	// 数据
	ThisMonthData        *ESportMonthData `protobuf:"bytes,21,opt,name=this_month_data,json=thisMonthData,proto3" json:"this_month_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetESportCoachInfoResp) Reset()         { *m = GetESportCoachInfoResp{} }
func (m *GetESportCoachInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetESportCoachInfoResp) ProtoMessage()    {}
func (*GetESportCoachInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{7}
}
func (m *GetESportCoachInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportCoachInfoResp.Unmarshal(m, b)
}
func (m *GetESportCoachInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportCoachInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetESportCoachInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportCoachInfoResp.Merge(dst, src)
}
func (m *GetESportCoachInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetESportCoachInfoResp.Size(m)
}
func (m *GetESportCoachInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportCoachInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportCoachInfoResp proto.InternalMessageInfo

func (m *GetESportCoachInfoResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetESportCoachInfoResp) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

func (m *GetESportCoachInfoResp) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *GetESportCoachInfoResp) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *GetESportCoachInfoResp) GetCoachTtid() string {
	if m != nil {
		return m.CoachTtid
	}
	return ""
}

func (m *GetESportCoachInfoResp) GetCoachNick() string {
	if m != nil {
		return m.CoachNick
	}
	return ""
}

func (m *GetESportCoachInfoResp) GetContractBeginTime() int64 {
	if m != nil {
		return m.ContractBeginTime
	}
	return 0
}

func (m *GetESportCoachInfoResp) GetContractEndTime() int64 {
	if m != nil {
		return m.ContractEndTime
	}
	return 0
}

func (m *GetESportCoachInfoResp) GetSkillList() []*UserSkillItem {
	if m != nil {
		return m.SkillList
	}
	return nil
}

func (m *GetESportCoachInfoResp) GetThisMonthData() *ESportMonthData {
	if m != nil {
		return m.ThisMonthData
	}
	return nil
}

// 段位等信息
type SectionInfo struct {
	SectionName          string   `protobuf:"bytes,1,opt,name=section_name,json=sectionName,proto3" json:"section_name,omitempty"`
	ItemList             []string `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	SectionId            uint32   `protobuf:"varint,3,opt,name=section_id,json=sectionId,proto3" json:"section_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SectionInfo) Reset()         { *m = SectionInfo{} }
func (m *SectionInfo) String() string { return proto.CompactTextString(m) }
func (*SectionInfo) ProtoMessage()    {}
func (*SectionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{8}
}
func (m *SectionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SectionInfo.Unmarshal(m, b)
}
func (m *SectionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SectionInfo.Marshal(b, m, deterministic)
}
func (dst *SectionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SectionInfo.Merge(dst, src)
}
func (m *SectionInfo) XXX_Size() int {
	return xxx_messageInfo_SectionInfo.Size(m)
}
func (m *SectionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SectionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SectionInfo proto.InternalMessageInfo

func (m *SectionInfo) GetSectionName() string {
	if m != nil {
		return m.SectionName
	}
	return ""
}

func (m *SectionInfo) GetItemList() []string {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *SectionInfo) GetSectionId() uint32 {
	if m != nil {
		return m.SectionId
	}
	return 0
}

// 用户游戏资料信息
type UserSkillInfo struct {
	GameId               uint32         `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string         `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	SkillEvidence        string         `protobuf:"bytes,3,opt,name=skill_evidence,json=skillEvidence,proto3" json:"skill_evidence,omitempty"`
	SkillDesc            string         `protobuf:"bytes,4,opt,name=skill_desc,json=skillDesc,proto3" json:"skill_desc,omitempty"`
	Audio                string         `protobuf:"bytes,5,opt,name=audio,proto3" json:"audio,omitempty"`
	AudioDuration        uint32         `protobuf:"varint,6,opt,name=audio_duration,json=audioDuration,proto3" json:"audio_duration,omitempty"`
	SectionList          []*SectionInfo `protobuf:"bytes,7,rep,name=section_list,json=sectionList,proto3" json:"section_list,omitempty"`
	TextDesc             string         `protobuf:"bytes,8,opt,name=text_desc,json=textDesc,proto3" json:"text_desc,omitempty"`
	GameRank             uint32         `protobuf:"varint,9,opt,name=game_rank,json=gameRank,proto3" json:"game_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserSkillInfo) Reset()         { *m = UserSkillInfo{} }
func (m *UserSkillInfo) String() string { return proto.CompactTextString(m) }
func (*UserSkillInfo) ProtoMessage()    {}
func (*UserSkillInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{9}
}
func (m *UserSkillInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSkillInfo.Unmarshal(m, b)
}
func (m *UserSkillInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSkillInfo.Marshal(b, m, deterministic)
}
func (dst *UserSkillInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSkillInfo.Merge(dst, src)
}
func (m *UserSkillInfo) XXX_Size() int {
	return xxx_messageInfo_UserSkillInfo.Size(m)
}
func (m *UserSkillInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSkillInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserSkillInfo proto.InternalMessageInfo

func (m *UserSkillInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserSkillInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *UserSkillInfo) GetSkillEvidence() string {
	if m != nil {
		return m.SkillEvidence
	}
	return ""
}

func (m *UserSkillInfo) GetSkillDesc() string {
	if m != nil {
		return m.SkillDesc
	}
	return ""
}

func (m *UserSkillInfo) GetAudio() string {
	if m != nil {
		return m.Audio
	}
	return ""
}

func (m *UserSkillInfo) GetAudioDuration() uint32 {
	if m != nil {
		return m.AudioDuration
	}
	return 0
}

func (m *UserSkillInfo) GetSectionList() []*SectionInfo {
	if m != nil {
		return m.SectionList
	}
	return nil
}

func (m *UserSkillInfo) GetTextDesc() string {
	if m != nil {
		return m.TextDesc
	}
	return ""
}

func (m *UserSkillInfo) GetGameRank() uint32 {
	if m != nil {
		return m.GameRank
	}
	return 0
}

type GetESportUserSkillInfoReq struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetESportUserSkillInfoReq) Reset()         { *m = GetESportUserSkillInfoReq{} }
func (m *GetESportUserSkillInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetESportUserSkillInfoReq) ProtoMessage()    {}
func (*GetESportUserSkillInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{10}
}
func (m *GetESportUserSkillInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportUserSkillInfoReq.Unmarshal(m, b)
}
func (m *GetESportUserSkillInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportUserSkillInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetESportUserSkillInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportUserSkillInfoReq.Merge(dst, src)
}
func (m *GetESportUserSkillInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetESportUserSkillInfoReq.Size(m)
}
func (m *GetESportUserSkillInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportUserSkillInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportUserSkillInfoReq proto.InternalMessageInfo

func (m *GetESportUserSkillInfoReq) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *GetESportUserSkillInfoReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetESportUserSkillInfoResp struct {
	CoachUid             uint32         `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	GameId               uint32         `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	SkillInfo            *UserSkillInfo `protobuf:"bytes,3,opt,name=skill_info,json=skillInfo,proto3" json:"skill_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetESportUserSkillInfoResp) Reset()         { *m = GetESportUserSkillInfoResp{} }
func (m *GetESportUserSkillInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetESportUserSkillInfoResp) ProtoMessage()    {}
func (*GetESportUserSkillInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{11}
}
func (m *GetESportUserSkillInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportUserSkillInfoResp.Unmarshal(m, b)
}
func (m *GetESportUserSkillInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportUserSkillInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetESportUserSkillInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportUserSkillInfoResp.Merge(dst, src)
}
func (m *GetESportUserSkillInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetESportUserSkillInfoResp.Size(m)
}
func (m *GetESportUserSkillInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportUserSkillInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportUserSkillInfoResp proto.InternalMessageInfo

func (m *GetESportUserSkillInfoResp) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *GetESportUserSkillInfoResp) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetESportUserSkillInfoResp) GetSkillInfo() *UserSkillInfo {
	if m != nil {
		return m.SkillInfo
	}
	return nil
}

// 申请签约身份前置检查
type SignIdentityPreCheckReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	IdentityType         uint32   `protobuf:"varint,2,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SignIdentityPreCheckReq) Reset()         { *m = SignIdentityPreCheckReq{} }
func (m *SignIdentityPreCheckReq) String() string { return proto.CompactTextString(m) }
func (*SignIdentityPreCheckReq) ProtoMessage()    {}
func (*SignIdentityPreCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{12}
}
func (m *SignIdentityPreCheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SignIdentityPreCheckReq.Unmarshal(m, b)
}
func (m *SignIdentityPreCheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SignIdentityPreCheckReq.Marshal(b, m, deterministic)
}
func (dst *SignIdentityPreCheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SignIdentityPreCheckReq.Merge(dst, src)
}
func (m *SignIdentityPreCheckReq) XXX_Size() int {
	return xxx_messageInfo_SignIdentityPreCheckReq.Size(m)
}
func (m *SignIdentityPreCheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SignIdentityPreCheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_SignIdentityPreCheckReq proto.InternalMessageInfo

func (m *SignIdentityPreCheckReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SignIdentityPreCheckReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

type SignIdentityPreCheckResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SignIdentityPreCheckResp) Reset()         { *m = SignIdentityPreCheckResp{} }
func (m *SignIdentityPreCheckResp) String() string { return proto.CompactTextString(m) }
func (*SignIdentityPreCheckResp) ProtoMessage()    {}
func (*SignIdentityPreCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{13}
}
func (m *SignIdentityPreCheckResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SignIdentityPreCheckResp.Unmarshal(m, b)
}
func (m *SignIdentityPreCheckResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SignIdentityPreCheckResp.Marshal(b, m, deterministic)
}
func (dst *SignIdentityPreCheckResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SignIdentityPreCheckResp.Merge(dst, src)
}
func (m *SignIdentityPreCheckResp) XXX_Size() int {
	return xxx_messageInfo_SignIdentityPreCheckResp.Size(m)
}
func (m *SignIdentityPreCheckResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SignIdentityPreCheckResp.DiscardUnknown(m)
}

var xxx_messageInfo_SignIdentityPreCheckResp proto.InternalMessageInfo

// 申请签约-触发风控事件检查
type ApplySignRiskCheckReq struct {
	IdentityType         uint32   `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	DeviceId             string   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	AppVersion           uint32   `protobuf:"varint,4,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	FaceAuthResultToken  string   `protobuf:"bytes,5,opt,name=face_auth_result_token,json=faceAuthResultToken,proto3" json:"face_auth_result_token,omitempty"`
	GuildId              uint32   `protobuf:"varint,6,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplySignRiskCheckReq) Reset()         { *m = ApplySignRiskCheckReq{} }
func (m *ApplySignRiskCheckReq) String() string { return proto.CompactTextString(m) }
func (*ApplySignRiskCheckReq) ProtoMessage()    {}
func (*ApplySignRiskCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{14}
}
func (m *ApplySignRiskCheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplySignRiskCheckReq.Unmarshal(m, b)
}
func (m *ApplySignRiskCheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplySignRiskCheckReq.Marshal(b, m, deterministic)
}
func (dst *ApplySignRiskCheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplySignRiskCheckReq.Merge(dst, src)
}
func (m *ApplySignRiskCheckReq) XXX_Size() int {
	return xxx_messageInfo_ApplySignRiskCheckReq.Size(m)
}
func (m *ApplySignRiskCheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplySignRiskCheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplySignRiskCheckReq proto.InternalMessageInfo

func (m *ApplySignRiskCheckReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *ApplySignRiskCheckReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *ApplySignRiskCheckReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ApplySignRiskCheckReq) GetAppVersion() uint32 {
	if m != nil {
		return m.AppVersion
	}
	return 0
}

func (m *ApplySignRiskCheckReq) GetFaceAuthResultToken() string {
	if m != nil {
		return m.FaceAuthResultToken
	}
	return ""
}

func (m *ApplySignRiskCheckReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type ApplySignRiskCheckResp struct {
	// 人脸context
	FaceAuthContextJson  string   `protobuf:"bytes,1,opt,name=face_auth_context_json,json=faceAuthContextJson,proto3" json:"face_auth_context_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplySignRiskCheckResp) Reset()         { *m = ApplySignRiskCheckResp{} }
func (m *ApplySignRiskCheckResp) String() string { return proto.CompactTextString(m) }
func (*ApplySignRiskCheckResp) ProtoMessage()    {}
func (*ApplySignRiskCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{15}
}
func (m *ApplySignRiskCheckResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplySignRiskCheckResp.Unmarshal(m, b)
}
func (m *ApplySignRiskCheckResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplySignRiskCheckResp.Marshal(b, m, deterministic)
}
func (dst *ApplySignRiskCheckResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplySignRiskCheckResp.Merge(dst, src)
}
func (m *ApplySignRiskCheckResp) XXX_Size() int {
	return xxx_messageInfo_ApplySignRiskCheckResp.Size(m)
}
func (m *ApplySignRiskCheckResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplySignRiskCheckResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplySignRiskCheckResp proto.InternalMessageInfo

func (m *ApplySignRiskCheckResp) GetFaceAuthContextJson() string {
	if m != nil {
		return m.FaceAuthContextJson
	}
	return ""
}

type ApplySignReq struct {
	IdentityType         uint32   `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	ContractNames        []string `protobuf:"bytes,2,rep,name=contract_names,json=contractNames,proto3" json:"contract_names,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplySignReq) Reset()         { *m = ApplySignReq{} }
func (m *ApplySignReq) String() string { return proto.CompactTextString(m) }
func (*ApplySignReq) ProtoMessage()    {}
func (*ApplySignReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{16}
}
func (m *ApplySignReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplySignReq.Unmarshal(m, b)
}
func (m *ApplySignReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplySignReq.Marshal(b, m, deterministic)
}
func (dst *ApplySignReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplySignReq.Merge(dst, src)
}
func (m *ApplySignReq) XXX_Size() int {
	return xxx_messageInfo_ApplySignReq.Size(m)
}
func (m *ApplySignReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplySignReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplySignReq proto.InternalMessageInfo

func (m *ApplySignReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *ApplySignReq) GetContractNames() []string {
	if m != nil {
		return m.ContractNames
	}
	return nil
}

type ApplySignResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplySignResp) Reset()         { *m = ApplySignResp{} }
func (m *ApplySignResp) String() string { return proto.CompactTextString(m) }
func (*ApplySignResp) ProtoMessage()    {}
func (*ApplySignResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{17}
}
func (m *ApplySignResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplySignResp.Unmarshal(m, b)
}
func (m *ApplySignResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplySignResp.Marshal(b, m, deterministic)
}
func (dst *ApplySignResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplySignResp.Merge(dst, src)
}
func (m *ApplySignResp) XXX_Size() int {
	return xxx_messageInfo_ApplySignResp.Size(m)
}
func (m *ApplySignResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplySignResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplySignResp proto.InternalMessageInfo

// 公会业务类型
type GuildBusiness struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Available            bool     `protobuf:"varint,3,opt,name=available,proto3" json:"available,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildBusiness) Reset()         { *m = GuildBusiness{} }
func (m *GuildBusiness) String() string { return proto.CompactTextString(m) }
func (*GuildBusiness) ProtoMessage()    {}
func (*GuildBusiness) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{18}
}
func (m *GuildBusiness) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildBusiness.Unmarshal(m, b)
}
func (m *GuildBusiness) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildBusiness.Marshal(b, m, deterministic)
}
func (dst *GuildBusiness) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildBusiness.Merge(dst, src)
}
func (m *GuildBusiness) XXX_Size() int {
	return xxx_messageInfo_GuildBusiness.Size(m)
}
func (m *GuildBusiness) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildBusiness.DiscardUnknown(m)
}

var xxx_messageInfo_GuildBusiness proto.InternalMessageInfo

func (m *GuildBusiness) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GuildBusiness) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GuildBusiness) GetAvailable() bool {
	if m != nil {
		return m.Available
	}
	return false
}

// 获取公会信息
type TopGuildInfo struct {
	GuildId      uint32           `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ShortId      uint32           `protobuf:"varint,2,opt,name=short_id,json=shortId,proto3" json:"short_id,omitempty"`
	Name         string           `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	GuildTag     string           `protobuf:"bytes,4,opt,name=guild_tag,json=guildTag,proto3" json:"guild_tag,omitempty"`
	OwnerName    string           `protobuf:"bytes,5,opt,name=owner_name,json=ownerName,proto3" json:"owner_name,omitempty"`
	OwnerAccount string           `protobuf:"bytes,6,opt,name=owner_account,json=ownerAccount,proto3" json:"owner_account,omitempty"`
	OwnerAlias   string           `protobuf:"bytes,7,opt,name=owner_alias,json=ownerAlias,proto3" json:"owner_alias,omitempty"`
	OwnerUid     uint32           `protobuf:"varint,8,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	BusinessList []*GuildBusiness `protobuf:"bytes,9,rep,name=business_list,json=businessList,proto3" json:"business_list,omitempty"`
	//
	Rank                 uint32   `protobuf:"varint,10,opt,name=rank,proto3" json:"rank,omitempty"`
	RecommendTag         string   `protobuf:"bytes,11,opt,name=recommend_tag,json=recommendTag,proto3" json:"recommend_tag,omitempty"`
	AbilityTag           string   `protobuf:"bytes,12,opt,name=ability_tag,json=abilityTag,proto3" json:"ability_tag,omitempty"` // Deprecated: Do not use.
	HonorTitle           string   `protobuf:"bytes,13,opt,name=honor_title,json=honorTitle,proto3" json:"honor_title,omitempty"`
	FromTime             int64    `protobuf:"varint,14,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
	ToTime               int64    `protobuf:"varint,15,opt,name=to_time,json=toTime,proto3" json:"to_time,omitempty"`
	AbilityTagList       []string `protobuf:"bytes,16,rep,name=ability_tag_list,json=abilityTagList,proto3" json:"ability_tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopGuildInfo) Reset()         { *m = TopGuildInfo{} }
func (m *TopGuildInfo) String() string { return proto.CompactTextString(m) }
func (*TopGuildInfo) ProtoMessage()    {}
func (*TopGuildInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{19}
}
func (m *TopGuildInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopGuildInfo.Unmarshal(m, b)
}
func (m *TopGuildInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopGuildInfo.Marshal(b, m, deterministic)
}
func (dst *TopGuildInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopGuildInfo.Merge(dst, src)
}
func (m *TopGuildInfo) XXX_Size() int {
	return xxx_messageInfo_TopGuildInfo.Size(m)
}
func (m *TopGuildInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopGuildInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopGuildInfo proto.InternalMessageInfo

func (m *TopGuildInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *TopGuildInfo) GetShortId() uint32 {
	if m != nil {
		return m.ShortId
	}
	return 0
}

func (m *TopGuildInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TopGuildInfo) GetGuildTag() string {
	if m != nil {
		return m.GuildTag
	}
	return ""
}

func (m *TopGuildInfo) GetOwnerName() string {
	if m != nil {
		return m.OwnerName
	}
	return ""
}

func (m *TopGuildInfo) GetOwnerAccount() string {
	if m != nil {
		return m.OwnerAccount
	}
	return ""
}

func (m *TopGuildInfo) GetOwnerAlias() string {
	if m != nil {
		return m.OwnerAlias
	}
	return ""
}

func (m *TopGuildInfo) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

func (m *TopGuildInfo) GetBusinessList() []*GuildBusiness {
	if m != nil {
		return m.BusinessList
	}
	return nil
}

func (m *TopGuildInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *TopGuildInfo) GetRecommendTag() string {
	if m != nil {
		return m.RecommendTag
	}
	return ""
}

// Deprecated: Do not use.
func (m *TopGuildInfo) GetAbilityTag() string {
	if m != nil {
		return m.AbilityTag
	}
	return ""
}

func (m *TopGuildInfo) GetHonorTitle() string {
	if m != nil {
		return m.HonorTitle
	}
	return ""
}

func (m *TopGuildInfo) GetFromTime() int64 {
	if m != nil {
		return m.FromTime
	}
	return 0
}

func (m *TopGuildInfo) GetToTime() int64 {
	if m != nil {
		return m.ToTime
	}
	return 0
}

func (m *TopGuildInfo) GetAbilityTagList() []string {
	if m != nil {
		return m.AbilityTagList
	}
	return nil
}

// 获取推荐置顶公会列表
type GetRecommendTopGuildListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendTopGuildListReq) Reset()         { *m = GetRecommendTopGuildListReq{} }
func (m *GetRecommendTopGuildListReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendTopGuildListReq) ProtoMessage()    {}
func (*GetRecommendTopGuildListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{20}
}
func (m *GetRecommendTopGuildListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendTopGuildListReq.Unmarshal(m, b)
}
func (m *GetRecommendTopGuildListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendTopGuildListReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendTopGuildListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendTopGuildListReq.Merge(dst, src)
}
func (m *GetRecommendTopGuildListReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendTopGuildListReq.Size(m)
}
func (m *GetRecommendTopGuildListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendTopGuildListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendTopGuildListReq proto.InternalMessageInfo

type GetRecommendTopGuildListResp struct {
	GuildList            []*TopGuildInfo `protobuf:"bytes,1,rep,name=guild_list,json=guildList,proto3" json:"guild_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetRecommendTopGuildListResp) Reset()         { *m = GetRecommendTopGuildListResp{} }
func (m *GetRecommendTopGuildListResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendTopGuildListResp) ProtoMessage()    {}
func (*GetRecommendTopGuildListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{21}
}
func (m *GetRecommendTopGuildListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendTopGuildListResp.Unmarshal(m, b)
}
func (m *GetRecommendTopGuildListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendTopGuildListResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendTopGuildListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendTopGuildListResp.Merge(dst, src)
}
func (m *GetRecommendTopGuildListResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendTopGuildListResp.Size(m)
}
func (m *GetRecommendTopGuildListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendTopGuildListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendTopGuildListResp proto.InternalMessageInfo

func (m *GetRecommendTopGuildListResp) GetGuildList() []*TopGuildInfo {
	if m != nil {
		return m.GuildList
	}
	return nil
}

// 获取解约方式
type GetCancelContractTypesReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Scene                uint32   `protobuf:"varint,2,opt,name=scene,proto3" json:"scene,omitempty"`
	WorkerType           uint32   `protobuf:"varint,3,opt,name=worker_type,json=workerType,proto3" json:"worker_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCancelContractTypesReq) Reset()         { *m = GetCancelContractTypesReq{} }
func (m *GetCancelContractTypesReq) String() string { return proto.CompactTextString(m) }
func (*GetCancelContractTypesReq) ProtoMessage()    {}
func (*GetCancelContractTypesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{22}
}
func (m *GetCancelContractTypesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCancelContractTypesReq.Unmarshal(m, b)
}
func (m *GetCancelContractTypesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCancelContractTypesReq.Marshal(b, m, deterministic)
}
func (dst *GetCancelContractTypesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCancelContractTypesReq.Merge(dst, src)
}
func (m *GetCancelContractTypesReq) XXX_Size() int {
	return xxx_messageInfo_GetCancelContractTypesReq.Size(m)
}
func (m *GetCancelContractTypesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCancelContractTypesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCancelContractTypesReq proto.InternalMessageInfo

func (m *GetCancelContractTypesReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetCancelContractTypesReq) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *GetCancelContractTypesReq) GetWorkerType() uint32 {
	if m != nil {
		return m.WorkerType
	}
	return 0
}

type CanCancelContract struct {
	CancelType           uint32   `protobuf:"varint,1,opt,name=cancel_type,json=cancelType,proto3" json:"cancel_type,omitempty"`
	Enabled              bool     `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	ShowButDisable       bool     `protobuf:"varint,5,opt,name=show_but_disable,json=showButDisable,proto3" json:"show_but_disable,omitempty"`
	DisableMsg           string   `protobuf:"bytes,6,opt,name=disable_msg,json=disableMsg,proto3" json:"disable_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CanCancelContract) Reset()         { *m = CanCancelContract{} }
func (m *CanCancelContract) String() string { return proto.CompactTextString(m) }
func (*CanCancelContract) ProtoMessage()    {}
func (*CanCancelContract) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{23}
}
func (m *CanCancelContract) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CanCancelContract.Unmarshal(m, b)
}
func (m *CanCancelContract) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CanCancelContract.Marshal(b, m, deterministic)
}
func (dst *CanCancelContract) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CanCancelContract.Merge(dst, src)
}
func (m *CanCancelContract) XXX_Size() int {
	return xxx_messageInfo_CanCancelContract.Size(m)
}
func (m *CanCancelContract) XXX_DiscardUnknown() {
	xxx_messageInfo_CanCancelContract.DiscardUnknown(m)
}

var xxx_messageInfo_CanCancelContract proto.InternalMessageInfo

func (m *CanCancelContract) GetCancelType() uint32 {
	if m != nil {
		return m.CancelType
	}
	return 0
}

func (m *CanCancelContract) GetEnabled() bool {
	if m != nil {
		return m.Enabled
	}
	return false
}

func (m *CanCancelContract) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CanCancelContract) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *CanCancelContract) GetShowButDisable() bool {
	if m != nil {
		return m.ShowButDisable
	}
	return false
}

func (m *CanCancelContract) GetDisableMsg() string {
	if m != nil {
		return m.DisableMsg
	}
	return ""
}

type GetCancelContractTypesResp struct {
	CancelTypes          []*CanCancelContract `protobuf:"bytes,1,rep,name=cancel_types,json=cancelTypes,proto3" json:"cancel_types,omitempty"`
	PayCost              int64                `protobuf:"varint,2,opt,name=pay_cost,json=payCost,proto3" json:"pay_cost,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetCancelContractTypesResp) Reset()         { *m = GetCancelContractTypesResp{} }
func (m *GetCancelContractTypesResp) String() string { return proto.CompactTextString(m) }
func (*GetCancelContractTypesResp) ProtoMessage()    {}
func (*GetCancelContractTypesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{24}
}
func (m *GetCancelContractTypesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCancelContractTypesResp.Unmarshal(m, b)
}
func (m *GetCancelContractTypesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCancelContractTypesResp.Marshal(b, m, deterministic)
}
func (dst *GetCancelContractTypesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCancelContractTypesResp.Merge(dst, src)
}
func (m *GetCancelContractTypesResp) XXX_Size() int {
	return xxx_messageInfo_GetCancelContractTypesResp.Size(m)
}
func (m *GetCancelContractTypesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCancelContractTypesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCancelContractTypesResp proto.InternalMessageInfo

func (m *GetCancelContractTypesResp) GetCancelTypes() []*CanCancelContract {
	if m != nil {
		return m.CancelTypes
	}
	return nil
}

func (m *GetCancelContractTypesResp) GetPayCost() int64 {
	if m != nil {
		return m.PayCost
	}
	return 0
}

// 申请解约
type ApplyCancelContractReq struct {
	GuildId              uint32          `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	CancelType           uint32          `protobuf:"varint,2,opt,name=cancel_type,json=cancelType,proto3" json:"cancel_type,omitempty"`
	ProofUrls            []string        `protobuf:"bytes,3,rep,name=proof_urls,json=proofUrls,proto3" json:"proof_urls,omitempty"`
	PayDesc              string          `protobuf:"bytes,4,opt,name=pay_desc,json=payDesc,proto3" json:"pay_desc,omitempty"`
	ProofList            []*ProofContent `protobuf:"bytes,5,rep,name=proof_list,json=proofList,proto3" json:"proof_list,omitempty"`
	CancelReason         string          `protobuf:"bytes,6,opt,name=cancel_reason,json=cancelReason,proto3" json:"cancel_reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ApplyCancelContractReq) Reset()         { *m = ApplyCancelContractReq{} }
func (m *ApplyCancelContractReq) String() string { return proto.CompactTextString(m) }
func (*ApplyCancelContractReq) ProtoMessage()    {}
func (*ApplyCancelContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{25}
}
func (m *ApplyCancelContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyCancelContractReq.Unmarshal(m, b)
}
func (m *ApplyCancelContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyCancelContractReq.Marshal(b, m, deterministic)
}
func (dst *ApplyCancelContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyCancelContractReq.Merge(dst, src)
}
func (m *ApplyCancelContractReq) XXX_Size() int {
	return xxx_messageInfo_ApplyCancelContractReq.Size(m)
}
func (m *ApplyCancelContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyCancelContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyCancelContractReq proto.InternalMessageInfo

func (m *ApplyCancelContractReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplyCancelContractReq) GetCancelType() uint32 {
	if m != nil {
		return m.CancelType
	}
	return 0
}

func (m *ApplyCancelContractReq) GetProofUrls() []string {
	if m != nil {
		return m.ProofUrls
	}
	return nil
}

func (m *ApplyCancelContractReq) GetPayDesc() string {
	if m != nil {
		return m.PayDesc
	}
	return ""
}

func (m *ApplyCancelContractReq) GetProofList() []*ProofContent {
	if m != nil {
		return m.ProofList
	}
	return nil
}

func (m *ApplyCancelContractReq) GetCancelReason() string {
	if m != nil {
		return m.CancelReason
	}
	return ""
}

type ProofContent struct {
	Key                  string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	CensorKey            string                 `protobuf:"bytes,2,opt,name=censor_key,json=censorKey,proto3" json:"censor_key,omitempty"`
	Type                 ProofContent_ProofType `protobuf:"varint,3,opt,name=type,proto3,enum=contract_http_logic.ProofContent_ProofType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ProofContent) Reset()         { *m = ProofContent{} }
func (m *ProofContent) String() string { return proto.CompactTextString(m) }
func (*ProofContent) ProtoMessage()    {}
func (*ProofContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{26}
}
func (m *ProofContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProofContent.Unmarshal(m, b)
}
func (m *ProofContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProofContent.Marshal(b, m, deterministic)
}
func (dst *ProofContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProofContent.Merge(dst, src)
}
func (m *ProofContent) XXX_Size() int {
	return xxx_messageInfo_ProofContent.Size(m)
}
func (m *ProofContent) XXX_DiscardUnknown() {
	xxx_messageInfo_ProofContent.DiscardUnknown(m)
}

var xxx_messageInfo_ProofContent proto.InternalMessageInfo

func (m *ProofContent) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ProofContent) GetCensorKey() string {
	if m != nil {
		return m.CensorKey
	}
	return ""
}

func (m *ProofContent) GetType() ProofContent_ProofType {
	if m != nil {
		return m.Type
	}
	return ProofContent_ProofType_Invalid
}

type ProofShowContent struct {
	Url                  string                     `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Type                 ProofShowContent_ProofType `protobuf:"varint,2,opt,name=type,proto3,enum=contract_http_logic.ProofShowContent_ProofType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *ProofShowContent) Reset()         { *m = ProofShowContent{} }
func (m *ProofShowContent) String() string { return proto.CompactTextString(m) }
func (*ProofShowContent) ProtoMessage()    {}
func (*ProofShowContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{27}
}
func (m *ProofShowContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProofShowContent.Unmarshal(m, b)
}
func (m *ProofShowContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProofShowContent.Marshal(b, m, deterministic)
}
func (dst *ProofShowContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProofShowContent.Merge(dst, src)
}
func (m *ProofShowContent) XXX_Size() int {
	return xxx_messageInfo_ProofShowContent.Size(m)
}
func (m *ProofShowContent) XXX_DiscardUnknown() {
	xxx_messageInfo_ProofShowContent.DiscardUnknown(m)
}

var xxx_messageInfo_ProofShowContent proto.InternalMessageInfo

func (m *ProofShowContent) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *ProofShowContent) GetType() ProofShowContent_ProofType {
	if m != nil {
		return m.Type
	}
	return ProofShowContent_ProofType_Invalid
}

type ApplyCancelContractResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyCancelContractResp) Reset()         { *m = ApplyCancelContractResp{} }
func (m *ApplyCancelContractResp) String() string { return proto.CompactTextString(m) }
func (*ApplyCancelContractResp) ProtoMessage()    {}
func (*ApplyCancelContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{28}
}
func (m *ApplyCancelContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyCancelContractResp.Unmarshal(m, b)
}
func (m *ApplyCancelContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyCancelContractResp.Marshal(b, m, deterministic)
}
func (dst *ApplyCancelContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyCancelContractResp.Merge(dst, src)
}
func (m *ApplyCancelContractResp) XXX_Size() int {
	return xxx_messageInfo_ApplyCancelContractResp.Size(m)
}
func (m *ApplyCancelContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyCancelContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyCancelContractResp proto.InternalMessageInfo

type LockPayCancelAmountReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LockPayCancelAmountReq) Reset()         { *m = LockPayCancelAmountReq{} }
func (m *LockPayCancelAmountReq) String() string { return proto.CompactTextString(m) }
func (*LockPayCancelAmountReq) ProtoMessage()    {}
func (*LockPayCancelAmountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{29}
}
func (m *LockPayCancelAmountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LockPayCancelAmountReq.Unmarshal(m, b)
}
func (m *LockPayCancelAmountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LockPayCancelAmountReq.Marshal(b, m, deterministic)
}
func (dst *LockPayCancelAmountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LockPayCancelAmountReq.Merge(dst, src)
}
func (m *LockPayCancelAmountReq) XXX_Size() int {
	return xxx_messageInfo_LockPayCancelAmountReq.Size(m)
}
func (m *LockPayCancelAmountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LockPayCancelAmountReq.DiscardUnknown(m)
}

var xxx_messageInfo_LockPayCancelAmountReq proto.InternalMessageInfo

func (m *LockPayCancelAmountReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type LockPayCancelAmountResp struct {
	PayCost              int64    `protobuf:"varint,1,opt,name=pay_cost,json=payCost,proto3" json:"pay_cost,omitempty"`
	ExpireTs             int64    `protobuf:"varint,2,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	StartTs              int64    `protobuf:"varint,3,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LockPayCancelAmountResp) Reset()         { *m = LockPayCancelAmountResp{} }
func (m *LockPayCancelAmountResp) String() string { return proto.CompactTextString(m) }
func (*LockPayCancelAmountResp) ProtoMessage()    {}
func (*LockPayCancelAmountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{30}
}
func (m *LockPayCancelAmountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LockPayCancelAmountResp.Unmarshal(m, b)
}
func (m *LockPayCancelAmountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LockPayCancelAmountResp.Marshal(b, m, deterministic)
}
func (dst *LockPayCancelAmountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LockPayCancelAmountResp.Merge(dst, src)
}
func (m *LockPayCancelAmountResp) XXX_Size() int {
	return xxx_messageInfo_LockPayCancelAmountResp.Size(m)
}
func (m *LockPayCancelAmountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LockPayCancelAmountResp.DiscardUnknown(m)
}

var xxx_messageInfo_LockPayCancelAmountResp proto.InternalMessageInfo

func (m *LockPayCancelAmountResp) GetPayCost() int64 {
	if m != nil {
		return m.PayCost
	}
	return 0
}

func (m *LockPayCancelAmountResp) GetExpireTs() int64 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

func (m *LockPayCancelAmountResp) GetStartTs() int64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

// 检查是否可以解约
type CheckCanCancelContractReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCanCancelContractReq) Reset()         { *m = CheckCanCancelContractReq{} }
func (m *CheckCanCancelContractReq) String() string { return proto.CompactTextString(m) }
func (*CheckCanCancelContractReq) ProtoMessage()    {}
func (*CheckCanCancelContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{31}
}
func (m *CheckCanCancelContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCanCancelContractReq.Unmarshal(m, b)
}
func (m *CheckCanCancelContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCanCancelContractReq.Marshal(b, m, deterministic)
}
func (dst *CheckCanCancelContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCanCancelContractReq.Merge(dst, src)
}
func (m *CheckCanCancelContractReq) XXX_Size() int {
	return xxx_messageInfo_CheckCanCancelContractReq.Size(m)
}
func (m *CheckCanCancelContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCanCancelContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCanCancelContractReq proto.InternalMessageInfo

func (m *CheckCanCancelContractReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type CheckCanCancelContractResp struct {
	LockPayCost          int64                                  `protobuf:"varint,1,opt,name=lock_pay_cost,json=lockPayCost,proto3" json:"lock_pay_cost,omitempty"`
	LockExpireTs         int64                                  `protobuf:"varint,2,opt,name=lock_expire_ts,json=lockExpireTs,proto3" json:"lock_expire_ts,omitempty"`
	LockStartTs          int64                                  `protobuf:"varint,3,opt,name=lock_start_ts,json=lockStartTs,proto3" json:"lock_start_ts,omitempty"`
	CancelStage          CheckCanCancelContractResp_CancelStage `protobuf:"varint,4,opt,name=cancel_stage,json=cancelStage,proto3,enum=contract_http_logic.CheckCanCancelContractResp_CancelStage" json:"cancel_stage,omitempty"`
	StageExpireTs        int64                                  `protobuf:"varint,5,opt,name=stage_expire_ts,json=stageExpireTs,proto3" json:"stage_expire_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *CheckCanCancelContractResp) Reset()         { *m = CheckCanCancelContractResp{} }
func (m *CheckCanCancelContractResp) String() string { return proto.CompactTextString(m) }
func (*CheckCanCancelContractResp) ProtoMessage()    {}
func (*CheckCanCancelContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{32}
}
func (m *CheckCanCancelContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCanCancelContractResp.Unmarshal(m, b)
}
func (m *CheckCanCancelContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCanCancelContractResp.Marshal(b, m, deterministic)
}
func (dst *CheckCanCancelContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCanCancelContractResp.Merge(dst, src)
}
func (m *CheckCanCancelContractResp) XXX_Size() int {
	return xxx_messageInfo_CheckCanCancelContractResp.Size(m)
}
func (m *CheckCanCancelContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCanCancelContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCanCancelContractResp proto.InternalMessageInfo

func (m *CheckCanCancelContractResp) GetLockPayCost() int64 {
	if m != nil {
		return m.LockPayCost
	}
	return 0
}

func (m *CheckCanCancelContractResp) GetLockExpireTs() int64 {
	if m != nil {
		return m.LockExpireTs
	}
	return 0
}

func (m *CheckCanCancelContractResp) GetLockStartTs() int64 {
	if m != nil {
		return m.LockStartTs
	}
	return 0
}

func (m *CheckCanCancelContractResp) GetCancelStage() CheckCanCancelContractResp_CancelStage {
	if m != nil {
		return m.CancelStage
	}
	return CheckCanCancelContractResp_CancelContractTypeDefault
}

func (m *CheckCanCancelContractResp) GetStageExpireTs() int64 {
	if m != nil {
		return m.StageExpireTs
	}
	return 0
}

// 获取解约申请列表
type GetApplyCancelContractListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApplyCancelContractListReq) Reset()         { *m = GetApplyCancelContractListReq{} }
func (m *GetApplyCancelContractListReq) String() string { return proto.CompactTextString(m) }
func (*GetApplyCancelContractListReq) ProtoMessage()    {}
func (*GetApplyCancelContractListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{33}
}
func (m *GetApplyCancelContractListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyCancelContractListReq.Unmarshal(m, b)
}
func (m *GetApplyCancelContractListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyCancelContractListReq.Marshal(b, m, deterministic)
}
func (dst *GetApplyCancelContractListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyCancelContractListReq.Merge(dst, src)
}
func (m *GetApplyCancelContractListReq) XXX_Size() int {
	return xxx_messageInfo_GetApplyCancelContractListReq.Size(m)
}
func (m *GetApplyCancelContractListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyCancelContractListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyCancelContractListReq proto.InternalMessageInfo

func (m *GetApplyCancelContractListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetApplyCancelContractListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetApplyCancelContractListReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type ApplyCancelContractItem struct {
	Uid                  uint32              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32              `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	CancelType           uint32              `protobuf:"varint,3,opt,name=cancel_type,json=cancelType,proto3" json:"cancel_type,omitempty"`
	Account              string              `protobuf:"bytes,4,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string              `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Alias                string              `protobuf:"bytes,6,opt,name=alias,proto3" json:"alias,omitempty"`
	ApplyTime            uint32              `protobuf:"varint,7,opt,name=apply_time,json=applyTime,proto3" json:"apply_time,omitempty"`
	EndTime              uint32              `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	AnchorIdentityList   []uint32            `protobuf:"varint,9,rep,packed,name=anchor_identity_list,json=anchorIdentityList,proto3" json:"anchor_identity_list,omitempty"`
	Reason               string              `protobuf:"bytes,10,opt,name=reason,proto3" json:"reason,omitempty"`
	ShowAcceptBtn        bool                `protobuf:"varint,11,opt,name=show_accept_btn,json=showAcceptBtn,proto3" json:"show_accept_btn,omitempty"`
	ShowRejectBtn        bool                `protobuf:"varint,12,opt,name=show_reject_btn,json=showRejectBtn,proto3" json:"show_reject_btn,omitempty"`
	ProofList            []*ProofShowContent `protobuf:"bytes,13,rep,name=proof_list,json=proofList,proto3" json:"proof_list,omitempty"`
	CancelReason         string              `protobuf:"bytes,14,opt,name=cancel_reason,json=cancelReason,proto3" json:"cancel_reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ApplyCancelContractItem) Reset()         { *m = ApplyCancelContractItem{} }
func (m *ApplyCancelContractItem) String() string { return proto.CompactTextString(m) }
func (*ApplyCancelContractItem) ProtoMessage()    {}
func (*ApplyCancelContractItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{34}
}
func (m *ApplyCancelContractItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyCancelContractItem.Unmarshal(m, b)
}
func (m *ApplyCancelContractItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyCancelContractItem.Marshal(b, m, deterministic)
}
func (dst *ApplyCancelContractItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyCancelContractItem.Merge(dst, src)
}
func (m *ApplyCancelContractItem) XXX_Size() int {
	return xxx_messageInfo_ApplyCancelContractItem.Size(m)
}
func (m *ApplyCancelContractItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyCancelContractItem.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyCancelContractItem proto.InternalMessageInfo

func (m *ApplyCancelContractItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyCancelContractItem) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplyCancelContractItem) GetCancelType() uint32 {
	if m != nil {
		return m.CancelType
	}
	return 0
}

func (m *ApplyCancelContractItem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ApplyCancelContractItem) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ApplyCancelContractItem) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *ApplyCancelContractItem) GetApplyTime() uint32 {
	if m != nil {
		return m.ApplyTime
	}
	return 0
}

func (m *ApplyCancelContractItem) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ApplyCancelContractItem) GetAnchorIdentityList() []uint32 {
	if m != nil {
		return m.AnchorIdentityList
	}
	return nil
}

func (m *ApplyCancelContractItem) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *ApplyCancelContractItem) GetShowAcceptBtn() bool {
	if m != nil {
		return m.ShowAcceptBtn
	}
	return false
}

func (m *ApplyCancelContractItem) GetShowRejectBtn() bool {
	if m != nil {
		return m.ShowRejectBtn
	}
	return false
}

func (m *ApplyCancelContractItem) GetProofList() []*ProofShowContent {
	if m != nil {
		return m.ProofList
	}
	return nil
}

func (m *ApplyCancelContractItem) GetCancelReason() string {
	if m != nil {
		return m.CancelReason
	}
	return ""
}

type GetApplyCancelContractListResp struct {
	List                 []*ApplyCancelContractItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	NextPage             uint32                     `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetApplyCancelContractListResp) Reset()         { *m = GetApplyCancelContractListResp{} }
func (m *GetApplyCancelContractListResp) String() string { return proto.CompactTextString(m) }
func (*GetApplyCancelContractListResp) ProtoMessage()    {}
func (*GetApplyCancelContractListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{35}
}
func (m *GetApplyCancelContractListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyCancelContractListResp.Unmarshal(m, b)
}
func (m *GetApplyCancelContractListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyCancelContractListResp.Marshal(b, m, deterministic)
}
func (dst *GetApplyCancelContractListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyCancelContractListResp.Merge(dst, src)
}
func (m *GetApplyCancelContractListResp) XXX_Size() int {
	return xxx_messageInfo_GetApplyCancelContractListResp.Size(m)
}
func (m *GetApplyCancelContractListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyCancelContractListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyCancelContractListResp proto.InternalMessageInfo

func (m *GetApplyCancelContractListResp) GetList() []*ApplyCancelContractItem {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetApplyCancelContractListResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

// 同意解约
type AcceptApplyCancelContractReq struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AcceptApplyCancelContractReq) Reset()         { *m = AcceptApplyCancelContractReq{} }
func (m *AcceptApplyCancelContractReq) String() string { return proto.CompactTextString(m) }
func (*AcceptApplyCancelContractReq) ProtoMessage()    {}
func (*AcceptApplyCancelContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{36}
}
func (m *AcceptApplyCancelContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceptApplyCancelContractReq.Unmarshal(m, b)
}
func (m *AcceptApplyCancelContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceptApplyCancelContractReq.Marshal(b, m, deterministic)
}
func (dst *AcceptApplyCancelContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptApplyCancelContractReq.Merge(dst, src)
}
func (m *AcceptApplyCancelContractReq) XXX_Size() int {
	return xxx_messageInfo_AcceptApplyCancelContractReq.Size(m)
}
func (m *AcceptApplyCancelContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptApplyCancelContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptApplyCancelContractReq proto.InternalMessageInfo

func (m *AcceptApplyCancelContractReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *AcceptApplyCancelContractReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type AcceptApplyCancelContractResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AcceptApplyCancelContractResp) Reset()         { *m = AcceptApplyCancelContractResp{} }
func (m *AcceptApplyCancelContractResp) String() string { return proto.CompactTextString(m) }
func (*AcceptApplyCancelContractResp) ProtoMessage()    {}
func (*AcceptApplyCancelContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{37}
}
func (m *AcceptApplyCancelContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceptApplyCancelContractResp.Unmarshal(m, b)
}
func (m *AcceptApplyCancelContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceptApplyCancelContractResp.Marshal(b, m, deterministic)
}
func (dst *AcceptApplyCancelContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptApplyCancelContractResp.Merge(dst, src)
}
func (m *AcceptApplyCancelContractResp) XXX_Size() int {
	return xxx_messageInfo_AcceptApplyCancelContractResp.Size(m)
}
func (m *AcceptApplyCancelContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptApplyCancelContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptApplyCancelContractResp proto.InternalMessageInfo

// 拒绝解约
type RejectApplyCancelContractReq struct {
	TargetUid            uint32          `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	GuildId              uint32          `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ProofUrls            []string        `protobuf:"bytes,3,rep,name=proof_urls,json=proofUrls,proto3" json:"proof_urls,omitempty"`
	RejectReason         string          `protobuf:"bytes,4,opt,name=reject_reason,json=rejectReason,proto3" json:"reject_reason,omitempty"`
	ProofList            []*ProofContent `protobuf:"bytes,5,rep,name=proof_list,json=proofList,proto3" json:"proof_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *RejectApplyCancelContractReq) Reset()         { *m = RejectApplyCancelContractReq{} }
func (m *RejectApplyCancelContractReq) String() string { return proto.CompactTextString(m) }
func (*RejectApplyCancelContractReq) ProtoMessage()    {}
func (*RejectApplyCancelContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{38}
}
func (m *RejectApplyCancelContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RejectApplyCancelContractReq.Unmarshal(m, b)
}
func (m *RejectApplyCancelContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RejectApplyCancelContractReq.Marshal(b, m, deterministic)
}
func (dst *RejectApplyCancelContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RejectApplyCancelContractReq.Merge(dst, src)
}
func (m *RejectApplyCancelContractReq) XXX_Size() int {
	return xxx_messageInfo_RejectApplyCancelContractReq.Size(m)
}
func (m *RejectApplyCancelContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RejectApplyCancelContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_RejectApplyCancelContractReq proto.InternalMessageInfo

func (m *RejectApplyCancelContractReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *RejectApplyCancelContractReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *RejectApplyCancelContractReq) GetProofUrls() []string {
	if m != nil {
		return m.ProofUrls
	}
	return nil
}

func (m *RejectApplyCancelContractReq) GetRejectReason() string {
	if m != nil {
		return m.RejectReason
	}
	return ""
}

func (m *RejectApplyCancelContractReq) GetProofList() []*ProofContent {
	if m != nil {
		return m.ProofList
	}
	return nil
}

type RejectApplyCancelContractResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RejectApplyCancelContractResp) Reset()         { *m = RejectApplyCancelContractResp{} }
func (m *RejectApplyCancelContractResp) String() string { return proto.CompactTextString(m) }
func (*RejectApplyCancelContractResp) ProtoMessage()    {}
func (*RejectApplyCancelContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{39}
}
func (m *RejectApplyCancelContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RejectApplyCancelContractResp.Unmarshal(m, b)
}
func (m *RejectApplyCancelContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RejectApplyCancelContractResp.Marshal(b, m, deterministic)
}
func (dst *RejectApplyCancelContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RejectApplyCancelContractResp.Merge(dst, src)
}
func (m *RejectApplyCancelContractResp) XXX_Size() int {
	return xxx_messageInfo_RejectApplyCancelContractResp.Size(m)
}
func (m *RejectApplyCancelContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RejectApplyCancelContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_RejectApplyCancelContractResp proto.InternalMessageInfo

type ContractWorker struct {
	WorkerType           uint32   `protobuf:"varint,1,opt,name=worker_type,json=workerType,proto3" json:"worker_type,omitempty"`
	WorkerName           string   `protobuf:"bytes,2,opt,name=worker_name,json=workerName,proto3" json:"worker_name,omitempty"`
	WorkerDesc           string   `protobuf:"bytes,3,opt,name=worker_desc,json=workerDesc,proto3" json:"worker_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContractWorker) Reset()         { *m = ContractWorker{} }
func (m *ContractWorker) String() string { return proto.CompactTextString(m) }
func (*ContractWorker) ProtoMessage()    {}
func (*ContractWorker) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{40}
}
func (m *ContractWorker) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContractWorker.Unmarshal(m, b)
}
func (m *ContractWorker) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContractWorker.Marshal(b, m, deterministic)
}
func (dst *ContractWorker) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContractWorker.Merge(dst, src)
}
func (m *ContractWorker) XXX_Size() int {
	return xxx_messageInfo_ContractWorker.Size(m)
}
func (m *ContractWorker) XXX_DiscardUnknown() {
	xxx_messageInfo_ContractWorker.DiscardUnknown(m)
}

var xxx_messageInfo_ContractWorker proto.InternalMessageInfo

func (m *ContractWorker) GetWorkerType() uint32 {
	if m != nil {
		return m.WorkerType
	}
	return 0
}

func (m *ContractWorker) GetWorkerName() string {
	if m != nil {
		return m.WorkerName
	}
	return ""
}

func (m *ContractWorker) GetWorkerDesc() string {
	if m != nil {
		return m.WorkerDesc
	}
	return ""
}

type ContractPrivilege struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string   `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	IsSelect             bool     `protobuf:"varint,3,opt,name=is_select,json=isSelect,proto3" json:"is_select,omitempty"`
	Note                 string   `protobuf:"bytes,4,opt,name=note,proto3" json:"note,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContractPrivilege) Reset()         { *m = ContractPrivilege{} }
func (m *ContractPrivilege) String() string { return proto.CompactTextString(m) }
func (*ContractPrivilege) ProtoMessage()    {}
func (*ContractPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{41}
}
func (m *ContractPrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContractPrivilege.Unmarshal(m, b)
}
func (m *ContractPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContractPrivilege.Marshal(b, m, deterministic)
}
func (dst *ContractPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContractPrivilege.Merge(dst, src)
}
func (m *ContractPrivilege) XXX_Size() int {
	return xxx_messageInfo_ContractPrivilege.Size(m)
}
func (m *ContractPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_ContractPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_ContractPrivilege proto.InternalMessageInfo

func (m *ContractPrivilege) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ContractPrivilege) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ContractPrivilege) GetIsSelect() bool {
	if m != nil {
		return m.IsSelect
	}
	return false
}

func (m *ContractPrivilege) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

type ContractPrivilegeGroup struct {
	Name                 string               `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	PrivilegeList        []*ContractPrivilege `protobuf:"bytes,2,rep,name=privilege_list,json=privilegeList,proto3" json:"privilege_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ContractPrivilegeGroup) Reset()         { *m = ContractPrivilegeGroup{} }
func (m *ContractPrivilegeGroup) String() string { return proto.CompactTextString(m) }
func (*ContractPrivilegeGroup) ProtoMessage()    {}
func (*ContractPrivilegeGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{42}
}
func (m *ContractPrivilegeGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContractPrivilegeGroup.Unmarshal(m, b)
}
func (m *ContractPrivilegeGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContractPrivilegeGroup.Marshal(b, m, deterministic)
}
func (dst *ContractPrivilegeGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContractPrivilegeGroup.Merge(dst, src)
}
func (m *ContractPrivilegeGroup) XXX_Size() int {
	return xxx_messageInfo_ContractPrivilegeGroup.Size(m)
}
func (m *ContractPrivilegeGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_ContractPrivilegeGroup.DiscardUnknown(m)
}

var xxx_messageInfo_ContractPrivilegeGroup proto.InternalMessageInfo

func (m *ContractPrivilegeGroup) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ContractPrivilegeGroup) GetPrivilegeList() []*ContractPrivilege {
	if m != nil {
		return m.PrivilegeList
	}
	return nil
}

type GetContractPrivilegeListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	WorkerType           uint32   `protobuf:"varint,2,opt,name=worker_type,json=workerType,proto3" json:"worker_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetContractPrivilegeListReq) Reset()         { *m = GetContractPrivilegeListReq{} }
func (m *GetContractPrivilegeListReq) String() string { return proto.CompactTextString(m) }
func (*GetContractPrivilegeListReq) ProtoMessage()    {}
func (*GetContractPrivilegeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{43}
}
func (m *GetContractPrivilegeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetContractPrivilegeListReq.Unmarshal(m, b)
}
func (m *GetContractPrivilegeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetContractPrivilegeListReq.Marshal(b, m, deterministic)
}
func (dst *GetContractPrivilegeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetContractPrivilegeListReq.Merge(dst, src)
}
func (m *GetContractPrivilegeListReq) XXX_Size() int {
	return xxx_messageInfo_GetContractPrivilegeListReq.Size(m)
}
func (m *GetContractPrivilegeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetContractPrivilegeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetContractPrivilegeListReq proto.InternalMessageInfo

func (m *GetContractPrivilegeListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetContractPrivilegeListReq) GetWorkerType() uint32 {
	if m != nil {
		return m.WorkerType
	}
	return 0
}

type GetContractPrivilegeListResp struct {
	GroupList            []*ContractPrivilegeGroup `protobuf:"bytes,1,rep,name=group_list,json=groupList,proto3" json:"group_list,omitempty"`
	WorkerList           []*ContractWorker         `protobuf:"bytes,2,rep,name=worker_list,json=workerList,proto3" json:"worker_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetContractPrivilegeListResp) Reset()         { *m = GetContractPrivilegeListResp{} }
func (m *GetContractPrivilegeListResp) String() string { return proto.CompactTextString(m) }
func (*GetContractPrivilegeListResp) ProtoMessage()    {}
func (*GetContractPrivilegeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{44}
}
func (m *GetContractPrivilegeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetContractPrivilegeListResp.Unmarshal(m, b)
}
func (m *GetContractPrivilegeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetContractPrivilegeListResp.Marshal(b, m, deterministic)
}
func (dst *GetContractPrivilegeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetContractPrivilegeListResp.Merge(dst, src)
}
func (m *GetContractPrivilegeListResp) XXX_Size() int {
	return xxx_messageInfo_GetContractPrivilegeListResp.Size(m)
}
func (m *GetContractPrivilegeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetContractPrivilegeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetContractPrivilegeListResp proto.InternalMessageInfo

func (m *GetContractPrivilegeListResp) GetGroupList() []*ContractPrivilegeGroup {
	if m != nil {
		return m.GroupList
	}
	return nil
}

func (m *GetContractPrivilegeListResp) GetWorkerList() []*ContractWorker {
	if m != nil {
		return m.WorkerList
	}
	return nil
}

type SignLiveSubmit struct {
	Tag                  uint32   `protobuf:"varint,1,opt,name=tag,proto3" json:"tag,omitempty"`
	Link                 string   `protobuf:"bytes,2,opt,name=link,proto3" json:"link,omitempty"`
	Contact              string   `protobuf:"bytes,3,opt,name=contact,proto3" json:"contact,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SignLiveSubmit) Reset()         { *m = SignLiveSubmit{} }
func (m *SignLiveSubmit) String() string { return proto.CompactTextString(m) }
func (*SignLiveSubmit) ProtoMessage()    {}
func (*SignLiveSubmit) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{45}
}
func (m *SignLiveSubmit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SignLiveSubmit.Unmarshal(m, b)
}
func (m *SignLiveSubmit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SignLiveSubmit.Marshal(b, m, deterministic)
}
func (dst *SignLiveSubmit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SignLiveSubmit.Merge(dst, src)
}
func (m *SignLiveSubmit) XXX_Size() int {
	return xxx_messageInfo_SignLiveSubmit.Size(m)
}
func (m *SignLiveSubmit) XXX_DiscardUnknown() {
	xxx_messageInfo_SignLiveSubmit.DiscardUnknown(m)
}

var xxx_messageInfo_SignLiveSubmit proto.InternalMessageInfo

func (m *SignLiveSubmit) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *SignLiveSubmit) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *SignLiveSubmit) GetContact() string {
	if m != nil {
		return m.Contact
	}
	return ""
}

// 申请签约
type ApplySignContractReq struct {
	GuildId              uint32          `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	IdentityType         uint32          `protobuf:"varint,2,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	Duration             uint32          `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`
	WorkerType           uint32          `protobuf:"varint,4,opt,name=worker_type,json=workerType,proto3" json:"worker_type,omitempty"`
	Submit               *SignLiveSubmit `protobuf:"bytes,5,opt,name=submit,proto3" json:"submit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ApplySignContractReq) Reset()         { *m = ApplySignContractReq{} }
func (m *ApplySignContractReq) String() string { return proto.CompactTextString(m) }
func (*ApplySignContractReq) ProtoMessage()    {}
func (*ApplySignContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{46}
}
func (m *ApplySignContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplySignContractReq.Unmarshal(m, b)
}
func (m *ApplySignContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplySignContractReq.Marshal(b, m, deterministic)
}
func (dst *ApplySignContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplySignContractReq.Merge(dst, src)
}
func (m *ApplySignContractReq) XXX_Size() int {
	return xxx_messageInfo_ApplySignContractReq.Size(m)
}
func (m *ApplySignContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplySignContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplySignContractReq proto.InternalMessageInfo

func (m *ApplySignContractReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplySignContractReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *ApplySignContractReq) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *ApplySignContractReq) GetWorkerType() uint32 {
	if m != nil {
		return m.WorkerType
	}
	return 0
}

func (m *ApplySignContractReq) GetSubmit() *SignLiveSubmit {
	if m != nil {
		return m.Submit
	}
	return nil
}

type ApplySignContractResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplySignContractResp) Reset()         { *m = ApplySignContractResp{} }
func (m *ApplySignContractResp) String() string { return proto.CompactTextString(m) }
func (*ApplySignContractResp) ProtoMessage()    {}
func (*ApplySignContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{47}
}
func (m *ApplySignContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplySignContractResp.Unmarshal(m, b)
}
func (m *ApplySignContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplySignContractResp.Marshal(b, m, deterministic)
}
func (dst *ApplySignContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplySignContractResp.Merge(dst, src)
}
func (m *ApplySignContractResp) XXX_Size() int {
	return xxx_messageInfo_ApplySignContractResp.Size(m)
}
func (m *ApplySignContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplySignContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplySignContractResp proto.InternalMessageInfo

// 晋升信息
type PromoteInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SignMonths           uint32   `protobuf:"varint,2,opt,name=sign_months,json=signMonths,proto3" json:"sign_months,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromoteInfo) Reset()         { *m = PromoteInfo{} }
func (m *PromoteInfo) String() string { return proto.CompactTextString(m) }
func (*PromoteInfo) ProtoMessage()    {}
func (*PromoteInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{48}
}
func (m *PromoteInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromoteInfo.Unmarshal(m, b)
}
func (m *PromoteInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromoteInfo.Marshal(b, m, deterministic)
}
func (dst *PromoteInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromoteInfo.Merge(dst, src)
}
func (m *PromoteInfo) XXX_Size() int {
	return xxx_messageInfo_PromoteInfo.Size(m)
}
func (m *PromoteInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PromoteInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PromoteInfo proto.InternalMessageInfo

func (m *PromoteInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PromoteInfo) GetSignMonths() uint32 {
	if m != nil {
		return m.SignMonths
	}
	return 0
}

// 签约信息
type UserContractInfo struct {
	WorkerType           uint32   `protobuf:"varint,1,opt,name=worker_type,json=workerType,proto3" json:"worker_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserContractInfo) Reset()         { *m = UserContractInfo{} }
func (m *UserContractInfo) String() string { return proto.CompactTextString(m) }
func (*UserContractInfo) ProtoMessage()    {}
func (*UserContractInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{49}
}
func (m *UserContractInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserContractInfo.Unmarshal(m, b)
}
func (m *UserContractInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserContractInfo.Marshal(b, m, deterministic)
}
func (dst *UserContractInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserContractInfo.Merge(dst, src)
}
func (m *UserContractInfo) XXX_Size() int {
	return xxx_messageInfo_UserContractInfo.Size(m)
}
func (m *UserContractInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserContractInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserContractInfo proto.InternalMessageInfo

func (m *UserContractInfo) GetWorkerType() uint32 {
	if m != nil {
		return m.WorkerType
	}
	return 0
}

// 签约管理， 获取用户的签约相关信息 /contract-http-logic/contract/GetUserContractInfo
type GetUserContractInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserContractInfoReq) Reset()         { *m = GetUserContractInfoReq{} }
func (m *GetUserContractInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserContractInfoReq) ProtoMessage()    {}
func (*GetUserContractInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{50}
}
func (m *GetUserContractInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserContractInfoReq.Unmarshal(m, b)
}
func (m *GetUserContractInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserContractInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserContractInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserContractInfoReq.Merge(dst, src)
}
func (m *GetUserContractInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserContractInfoReq.Size(m)
}
func (m *GetUserContractInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserContractInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserContractInfoReq proto.InternalMessageInfo

type GetUserContractInfoResp struct {
	PromoteInfo          *PromoteInfo      `protobuf:"bytes,1,opt,name=promote_info,json=promoteInfo,proto3" json:"promote_info,omitempty"`
	ContractInfo         *UserContractInfo `protobuf:"bytes,2,opt,name=contract_info,json=contractInfo,proto3" json:"contract_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetUserContractInfoResp) Reset()         { *m = GetUserContractInfoResp{} }
func (m *GetUserContractInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserContractInfoResp) ProtoMessage()    {}
func (*GetUserContractInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{51}
}
func (m *GetUserContractInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserContractInfoResp.Unmarshal(m, b)
}
func (m *GetUserContractInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserContractInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserContractInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserContractInfoResp.Merge(dst, src)
}
func (m *GetUserContractInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserContractInfoResp.Size(m)
}
func (m *GetUserContractInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserContractInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserContractInfoResp proto.InternalMessageInfo

func (m *GetUserContractInfoResp) GetPromoteInfo() *PromoteInfo {
	if m != nil {
		return m.PromoteInfo
	}
	return nil
}

func (m *GetUserContractInfoResp) GetContractInfo() *UserContractInfo {
	if m != nil {
		return m.ContractInfo
	}
	return nil
}

// 处理晋升邀请
type ProcPromoteInviteReq struct {
	ProcRes              uint32   `protobuf:"varint,1,opt,name=proc_res,json=procRes,proto3" json:"proc_res,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProcPromoteInviteReq) Reset()         { *m = ProcPromoteInviteReq{} }
func (m *ProcPromoteInviteReq) String() string { return proto.CompactTextString(m) }
func (*ProcPromoteInviteReq) ProtoMessage()    {}
func (*ProcPromoteInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{52}
}
func (m *ProcPromoteInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProcPromoteInviteReq.Unmarshal(m, b)
}
func (m *ProcPromoteInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProcPromoteInviteReq.Marshal(b, m, deterministic)
}
func (dst *ProcPromoteInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProcPromoteInviteReq.Merge(dst, src)
}
func (m *ProcPromoteInviteReq) XXX_Size() int {
	return xxx_messageInfo_ProcPromoteInviteReq.Size(m)
}
func (m *ProcPromoteInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ProcPromoteInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_ProcPromoteInviteReq proto.InternalMessageInfo

func (m *ProcPromoteInviteReq) GetProcRes() uint32 {
	if m != nil {
		return m.ProcRes
	}
	return 0
}

func (m *ProcPromoteInviteReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type ProcPromoteInviteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProcPromoteInviteResp) Reset()         { *m = ProcPromoteInviteResp{} }
func (m *ProcPromoteInviteResp) String() string { return proto.CompactTextString(m) }
func (*ProcPromoteInviteResp) ProtoMessage()    {}
func (*ProcPromoteInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{53}
}
func (m *ProcPromoteInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProcPromoteInviteResp.Unmarshal(m, b)
}
func (m *ProcPromoteInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProcPromoteInviteResp.Marshal(b, m, deterministic)
}
func (dst *ProcPromoteInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProcPromoteInviteResp.Merge(dst, src)
}
func (m *ProcPromoteInviteResp) XXX_Size() int {
	return xxx_messageInfo_ProcPromoteInviteResp.Size(m)
}
func (m *ProcPromoteInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ProcPromoteInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_ProcPromoteInviteResp proto.InternalMessageInfo

// 获取公会的管理员列表
type GetGuildMgrListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildMgrListReq) Reset()         { *m = GetGuildMgrListReq{} }
func (m *GetGuildMgrListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildMgrListReq) ProtoMessage()    {}
func (*GetGuildMgrListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{54}
}
func (m *GetGuildMgrListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMgrListReq.Unmarshal(m, b)
}
func (m *GetGuildMgrListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMgrListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildMgrListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMgrListReq.Merge(dst, src)
}
func (m *GetGuildMgrListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildMgrListReq.Size(m)
}
func (m *GetGuildMgrListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMgrListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMgrListReq proto.InternalMessageInfo

func (m *GetGuildMgrListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildMgrListResp struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildMgrListResp) Reset()         { *m = GetGuildMgrListResp{} }
func (m *GetGuildMgrListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildMgrListResp) ProtoMessage()    {}
func (*GetGuildMgrListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{55}
}
func (m *GetGuildMgrListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMgrListResp.Unmarshal(m, b)
}
func (m *GetGuildMgrListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMgrListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildMgrListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMgrListResp.Merge(dst, src)
}
func (m *GetGuildMgrListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildMgrListResp.Size(m)
}
func (m *GetGuildMgrListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMgrListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMgrListResp proto.InternalMessageInfo

func (m *GetGuildMgrListResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 审核上传的视频
type CensorVideoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	VideoKey             []string `protobuf:"bytes,3,rep,name=video_key,json=videoKey,proto3" json:"video_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CensorVideoReq) Reset()         { *m = CensorVideoReq{} }
func (m *CensorVideoReq) String() string { return proto.CompactTextString(m) }
func (*CensorVideoReq) ProtoMessage()    {}
func (*CensorVideoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{56}
}
func (m *CensorVideoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CensorVideoReq.Unmarshal(m, b)
}
func (m *CensorVideoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CensorVideoReq.Marshal(b, m, deterministic)
}
func (dst *CensorVideoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CensorVideoReq.Merge(dst, src)
}
func (m *CensorVideoReq) XXX_Size() int {
	return xxx_messageInfo_CensorVideoReq.Size(m)
}
func (m *CensorVideoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CensorVideoReq.DiscardUnknown(m)
}

var xxx_messageInfo_CensorVideoReq proto.InternalMessageInfo

func (m *CensorVideoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CensorVideoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CensorVideoReq) GetVideoKey() []string {
	if m != nil {
		return m.VideoKey
	}
	return nil
}

type CensorVideoResp struct {
	CensorResult         []*CensorResult `protobuf:"bytes,1,rep,name=censor_result,json=censorResult,proto3" json:"censor_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CensorVideoResp) Reset()         { *m = CensorVideoResp{} }
func (m *CensorVideoResp) String() string { return proto.CompactTextString(m) }
func (*CensorVideoResp) ProtoMessage()    {}
func (*CensorVideoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{57}
}
func (m *CensorVideoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CensorVideoResp.Unmarshal(m, b)
}
func (m *CensorVideoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CensorVideoResp.Marshal(b, m, deterministic)
}
func (dst *CensorVideoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CensorVideoResp.Merge(dst, src)
}
func (m *CensorVideoResp) XXX_Size() int {
	return xxx_messageInfo_CensorVideoResp.Size(m)
}
func (m *CensorVideoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CensorVideoResp.DiscardUnknown(m)
}

var xxx_messageInfo_CensorVideoResp proto.InternalMessageInfo

func (m *CensorVideoResp) GetCensorResult() []*CensorResult {
	if m != nil {
		return m.CensorResult
	}
	return nil
}

type CensorResult struct {
	VideoKey             string   `protobuf:"bytes,1,opt,name=video_key,json=videoKey,proto3" json:"video_key,omitempty"`
	CensorKey            string   `protobuf:"bytes,2,opt,name=censor_key,json=censorKey,proto3" json:"censor_key,omitempty"`
	TranscodeUrl         string   `protobuf:"bytes,3,opt,name=transcode_url,json=transcodeUrl,proto3" json:"transcode_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CensorResult) Reset()         { *m = CensorResult{} }
func (m *CensorResult) String() string { return proto.CompactTextString(m) }
func (*CensorResult) ProtoMessage()    {}
func (*CensorResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{58}
}
func (m *CensorResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CensorResult.Unmarshal(m, b)
}
func (m *CensorResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CensorResult.Marshal(b, m, deterministic)
}
func (dst *CensorResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CensorResult.Merge(dst, src)
}
func (m *CensorResult) XXX_Size() int {
	return xxx_messageInfo_CensorResult.Size(m)
}
func (m *CensorResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CensorResult.DiscardUnknown(m)
}

var xxx_messageInfo_CensorResult proto.InternalMessageInfo

func (m *CensorResult) GetVideoKey() string {
	if m != nil {
		return m.VideoKey
	}
	return ""
}

func (m *CensorResult) GetCensorKey() string {
	if m != nil {
		return m.CensorKey
	}
	return ""
}

func (m *CensorResult) GetTranscodeUrl() string {
	if m != nil {
		return m.TranscodeUrl
	}
	return ""
}

// 获取是否需要进行成员身份确认
type GetNeedConfirmWorkerTypeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNeedConfirmWorkerTypeReq) Reset()         { *m = GetNeedConfirmWorkerTypeReq{} }
func (m *GetNeedConfirmWorkerTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetNeedConfirmWorkerTypeReq) ProtoMessage()    {}
func (*GetNeedConfirmWorkerTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{59}
}
func (m *GetNeedConfirmWorkerTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNeedConfirmWorkerTypeReq.Unmarshal(m, b)
}
func (m *GetNeedConfirmWorkerTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNeedConfirmWorkerTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetNeedConfirmWorkerTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNeedConfirmWorkerTypeReq.Merge(dst, src)
}
func (m *GetNeedConfirmWorkerTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetNeedConfirmWorkerTypeReq.Size(m)
}
func (m *GetNeedConfirmWorkerTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNeedConfirmWorkerTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNeedConfirmWorkerTypeReq proto.InternalMessageInfo

func (m *GetNeedConfirmWorkerTypeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetNeedConfirmWorkerTypeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetNeedConfirmWorkerTypeResp struct {
	NeedConfirm          bool     `protobuf:"varint,1,opt,name=need_confirm,json=needConfirm,proto3" json:"need_confirm,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNeedConfirmWorkerTypeResp) Reset()         { *m = GetNeedConfirmWorkerTypeResp{} }
func (m *GetNeedConfirmWorkerTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetNeedConfirmWorkerTypeResp) ProtoMessage()    {}
func (*GetNeedConfirmWorkerTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{60}
}
func (m *GetNeedConfirmWorkerTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNeedConfirmWorkerTypeResp.Unmarshal(m, b)
}
func (m *GetNeedConfirmWorkerTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNeedConfirmWorkerTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetNeedConfirmWorkerTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNeedConfirmWorkerTypeResp.Merge(dst, src)
}
func (m *GetNeedConfirmWorkerTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetNeedConfirmWorkerTypeResp.Size(m)
}
func (m *GetNeedConfirmWorkerTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNeedConfirmWorkerTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNeedConfirmWorkerTypeResp proto.InternalMessageInfo

func (m *GetNeedConfirmWorkerTypeResp) GetNeedConfirm() bool {
	if m != nil {
		return m.NeedConfirm
	}
	return false
}

// 进行成员身份修改
type ModifyWorkerTypeReq struct {
	WorkerType           uint32   `protobuf:"varint,1,opt,name=worker_type,json=workerType,proto3" json:"worker_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyWorkerTypeReq) Reset()         { *m = ModifyWorkerTypeReq{} }
func (m *ModifyWorkerTypeReq) String() string { return proto.CompactTextString(m) }
func (*ModifyWorkerTypeReq) ProtoMessage()    {}
func (*ModifyWorkerTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{61}
}
func (m *ModifyWorkerTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyWorkerTypeReq.Unmarshal(m, b)
}
func (m *ModifyWorkerTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyWorkerTypeReq.Marshal(b, m, deterministic)
}
func (dst *ModifyWorkerTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyWorkerTypeReq.Merge(dst, src)
}
func (m *ModifyWorkerTypeReq) XXX_Size() int {
	return xxx_messageInfo_ModifyWorkerTypeReq.Size(m)
}
func (m *ModifyWorkerTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyWorkerTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyWorkerTypeReq proto.InternalMessageInfo

func (m *ModifyWorkerTypeReq) GetWorkerType() uint32 {
	if m != nil {
		return m.WorkerType
	}
	return 0
}

type ModifyWorkerTypeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyWorkerTypeResp) Reset()         { *m = ModifyWorkerTypeResp{} }
func (m *ModifyWorkerTypeResp) String() string { return proto.CompactTextString(m) }
func (*ModifyWorkerTypeResp) ProtoMessage()    {}
func (*ModifyWorkerTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{62}
}
func (m *ModifyWorkerTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyWorkerTypeResp.Unmarshal(m, b)
}
func (m *ModifyWorkerTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyWorkerTypeResp.Marshal(b, m, deterministic)
}
func (dst *ModifyWorkerTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyWorkerTypeResp.Merge(dst, src)
}
func (m *ModifyWorkerTypeResp) XXX_Size() int {
	return xxx_messageInfo_ModifyWorkerTypeResp.Size(m)
}
func (m *ModifyWorkerTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyWorkerTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyWorkerTypeResp proto.InternalMessageInfo

// 新主播任务
type AnchorLevelNewTask struct {
	RemainTime             uint32   `protobuf:"varint,1,opt,name=remain_time,json=remainTime,proto3" json:"remain_time,omitempty"`
	NeedWeekActiveDays     uint32   `protobuf:"varint,2,opt,name=need_week_active_days,json=needWeekActiveDays,proto3" json:"need_week_active_days,omitempty"`
	RemainWeekActiveDays   uint32   `protobuf:"varint,3,opt,name=remain_week_active_days,json=remainWeekActiveDays,proto3" json:"remain_week_active_days,omitempty"`
	NeedWeekPlatformDays   uint32   `protobuf:"varint,4,opt,name=need_week_platform_days,json=needWeekPlatformDays,proto3" json:"need_week_platform_days,omitempty"`
	RemainWeekPlatformDays uint32   `protobuf:"varint,5,opt,name=remain_week_platform_days,json=remainWeekPlatformDays,proto3" json:"remain_week_platform_days,omitempty"`
	WeekGiftValue          uint32   `protobuf:"varint,6,opt,name=week_gift_value,json=weekGiftValue,proto3" json:"week_gift_value,omitempty"`
	AnchorCheckLevel       string   `protobuf:"bytes,7,opt,name=anchor_check_level,json=anchorCheckLevel,proto3" json:"anchor_check_level,omitempty"`
	WeekNum                uint32   `protobuf:"varint,8,opt,name=week_num,json=weekNum,proto3" json:"week_num,omitempty"`
	TaskType               uint32   `protobuf:"varint,9,opt,name=task_type,json=taskType,proto3" json:"task_type,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *AnchorLevelNewTask) Reset()         { *m = AnchorLevelNewTask{} }
func (m *AnchorLevelNewTask) String() string { return proto.CompactTextString(m) }
func (*AnchorLevelNewTask) ProtoMessage()    {}
func (*AnchorLevelNewTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_55e2a9e7d2ade33b, []int{63}
}
func (m *AnchorLevelNewTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorLevelNewTask.Unmarshal(m, b)
}
func (m *AnchorLevelNewTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorLevelNewTask.Marshal(b, m, deterministic)
}
func (dst *AnchorLevelNewTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorLevelNewTask.Merge(dst, src)
}
func (m *AnchorLevelNewTask) XXX_Size() int {
	return xxx_messageInfo_AnchorLevelNewTask.Size(m)
}
func (m *AnchorLevelNewTask) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorLevelNewTask.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorLevelNewTask proto.InternalMessageInfo

func (m *AnchorLevelNewTask) GetRemainTime() uint32 {
	if m != nil {
		return m.RemainTime
	}
	return 0
}

func (m *AnchorLevelNewTask) GetNeedWeekActiveDays() uint32 {
	if m != nil {
		return m.NeedWeekActiveDays
	}
	return 0
}

func (m *AnchorLevelNewTask) GetRemainWeekActiveDays() uint32 {
	if m != nil {
		return m.RemainWeekActiveDays
	}
	return 0
}

func (m *AnchorLevelNewTask) GetNeedWeekPlatformDays() uint32 {
	if m != nil {
		return m.NeedWeekPlatformDays
	}
	return 0
}

func (m *AnchorLevelNewTask) GetRemainWeekPlatformDays() uint32 {
	if m != nil {
		return m.RemainWeekPlatformDays
	}
	return 0
}

func (m *AnchorLevelNewTask) GetWeekGiftValue() uint32 {
	if m != nil {
		return m.WeekGiftValue
	}
	return 0
}

func (m *AnchorLevelNewTask) GetAnchorCheckLevel() string {
	if m != nil {
		return m.AnchorCheckLevel
	}
	return ""
}

func (m *AnchorLevelNewTask) GetWeekNum() uint32 {
	if m != nil {
		return m.WeekNum
	}
	return 0
}

func (m *AnchorLevelNewTask) GetTaskType() uint32 {
	if m != nil {
		return m.TaskType
	}
	return 0
}

func init() {
	proto.RegisterType((*GetHallTaskResp)(nil), "contract_http_logic.GetHallTaskResp")
	proto.RegisterType((*HallTaskDetial)(nil), "contract_http_logic.HallTaskDetial")
	proto.RegisterType((*GetHallTaskHistoryResp)(nil), "contract_http_logic.GetHallTaskHistoryResp")
	proto.RegisterType((*HallTaskHistoryDetial)(nil), "contract_http_logic.HallTaskHistoryDetial")
	proto.RegisterType((*GetESportCoachInfoReq)(nil), "contract_http_logic.GetESportCoachInfoReq")
	proto.RegisterType((*UserSkillItem)(nil), "contract_http_logic.UserSkillItem")
	proto.RegisterType((*ESportMonthData)(nil), "contract_http_logic.ESportMonthData")
	proto.RegisterType((*GetESportCoachInfoResp)(nil), "contract_http_logic.GetESportCoachInfoResp")
	proto.RegisterType((*SectionInfo)(nil), "contract_http_logic.SectionInfo")
	proto.RegisterType((*UserSkillInfo)(nil), "contract_http_logic.UserSkillInfo")
	proto.RegisterType((*GetESportUserSkillInfoReq)(nil), "contract_http_logic.GetESportUserSkillInfoReq")
	proto.RegisterType((*GetESportUserSkillInfoResp)(nil), "contract_http_logic.GetESportUserSkillInfoResp")
	proto.RegisterType((*SignIdentityPreCheckReq)(nil), "contract_http_logic.SignIdentityPreCheckReq")
	proto.RegisterType((*SignIdentityPreCheckResp)(nil), "contract_http_logic.SignIdentityPreCheckResp")
	proto.RegisterType((*ApplySignRiskCheckReq)(nil), "contract_http_logic.ApplySignRiskCheckReq")
	proto.RegisterType((*ApplySignRiskCheckResp)(nil), "contract_http_logic.ApplySignRiskCheckResp")
	proto.RegisterType((*ApplySignReq)(nil), "contract_http_logic.ApplySignReq")
	proto.RegisterType((*ApplySignResp)(nil), "contract_http_logic.ApplySignResp")
	proto.RegisterType((*GuildBusiness)(nil), "contract_http_logic.GuildBusiness")
	proto.RegisterType((*TopGuildInfo)(nil), "contract_http_logic.TopGuildInfo")
	proto.RegisterType((*GetRecommendTopGuildListReq)(nil), "contract_http_logic.GetRecommendTopGuildListReq")
	proto.RegisterType((*GetRecommendTopGuildListResp)(nil), "contract_http_logic.GetRecommendTopGuildListResp")
	proto.RegisterType((*GetCancelContractTypesReq)(nil), "contract_http_logic.GetCancelContractTypesReq")
	proto.RegisterType((*CanCancelContract)(nil), "contract_http_logic.CanCancelContract")
	proto.RegisterType((*GetCancelContractTypesResp)(nil), "contract_http_logic.GetCancelContractTypesResp")
	proto.RegisterType((*ApplyCancelContractReq)(nil), "contract_http_logic.ApplyCancelContractReq")
	proto.RegisterType((*ProofContent)(nil), "contract_http_logic.ProofContent")
	proto.RegisterType((*ProofShowContent)(nil), "contract_http_logic.ProofShowContent")
	proto.RegisterType((*ApplyCancelContractResp)(nil), "contract_http_logic.ApplyCancelContractResp")
	proto.RegisterType((*LockPayCancelAmountReq)(nil), "contract_http_logic.LockPayCancelAmountReq")
	proto.RegisterType((*LockPayCancelAmountResp)(nil), "contract_http_logic.LockPayCancelAmountResp")
	proto.RegisterType((*CheckCanCancelContractReq)(nil), "contract_http_logic.CheckCanCancelContractReq")
	proto.RegisterType((*CheckCanCancelContractResp)(nil), "contract_http_logic.CheckCanCancelContractResp")
	proto.RegisterType((*GetApplyCancelContractListReq)(nil), "contract_http_logic.GetApplyCancelContractListReq")
	proto.RegisterType((*ApplyCancelContractItem)(nil), "contract_http_logic.ApplyCancelContractItem")
	proto.RegisterType((*GetApplyCancelContractListResp)(nil), "contract_http_logic.GetApplyCancelContractListResp")
	proto.RegisterType((*AcceptApplyCancelContractReq)(nil), "contract_http_logic.AcceptApplyCancelContractReq")
	proto.RegisterType((*AcceptApplyCancelContractResp)(nil), "contract_http_logic.AcceptApplyCancelContractResp")
	proto.RegisterType((*RejectApplyCancelContractReq)(nil), "contract_http_logic.RejectApplyCancelContractReq")
	proto.RegisterType((*RejectApplyCancelContractResp)(nil), "contract_http_logic.RejectApplyCancelContractResp")
	proto.RegisterType((*ContractWorker)(nil), "contract_http_logic.ContractWorker")
	proto.RegisterType((*ContractPrivilege)(nil), "contract_http_logic.ContractPrivilege")
	proto.RegisterType((*ContractPrivilegeGroup)(nil), "contract_http_logic.ContractPrivilegeGroup")
	proto.RegisterType((*GetContractPrivilegeListReq)(nil), "contract_http_logic.GetContractPrivilegeListReq")
	proto.RegisterType((*GetContractPrivilegeListResp)(nil), "contract_http_logic.GetContractPrivilegeListResp")
	proto.RegisterType((*SignLiveSubmit)(nil), "contract_http_logic.SignLiveSubmit")
	proto.RegisterType((*ApplySignContractReq)(nil), "contract_http_logic.ApplySignContractReq")
	proto.RegisterType((*ApplySignContractResp)(nil), "contract_http_logic.ApplySignContractResp")
	proto.RegisterType((*PromoteInfo)(nil), "contract_http_logic.PromoteInfo")
	proto.RegisterType((*UserContractInfo)(nil), "contract_http_logic.UserContractInfo")
	proto.RegisterType((*GetUserContractInfoReq)(nil), "contract_http_logic.GetUserContractInfoReq")
	proto.RegisterType((*GetUserContractInfoResp)(nil), "contract_http_logic.GetUserContractInfoResp")
	proto.RegisterType((*ProcPromoteInviteReq)(nil), "contract_http_logic.ProcPromoteInviteReq")
	proto.RegisterType((*ProcPromoteInviteResp)(nil), "contract_http_logic.ProcPromoteInviteResp")
	proto.RegisterType((*GetGuildMgrListReq)(nil), "contract_http_logic.GetGuildMgrListReq")
	proto.RegisterType((*GetGuildMgrListResp)(nil), "contract_http_logic.GetGuildMgrListResp")
	proto.RegisterType((*CensorVideoReq)(nil), "contract_http_logic.CensorVideoReq")
	proto.RegisterType((*CensorVideoResp)(nil), "contract_http_logic.CensorVideoResp")
	proto.RegisterType((*CensorResult)(nil), "contract_http_logic.CensorResult")
	proto.RegisterType((*GetNeedConfirmWorkerTypeReq)(nil), "contract_http_logic.GetNeedConfirmWorkerTypeReq")
	proto.RegisterType((*GetNeedConfirmWorkerTypeResp)(nil), "contract_http_logic.GetNeedConfirmWorkerTypeResp")
	proto.RegisterType((*ModifyWorkerTypeReq)(nil), "contract_http_logic.ModifyWorkerTypeReq")
	proto.RegisterType((*ModifyWorkerTypeResp)(nil), "contract_http_logic.ModifyWorkerTypeResp")
	proto.RegisterType((*AnchorLevelNewTask)(nil), "contract_http_logic.AnchorLevelNewTask")
	proto.RegisterEnum("contract_http_logic.ProofContent_ProofType", ProofContent_ProofType_name, ProofContent_ProofType_value)
	proto.RegisterEnum("contract_http_logic.ProofShowContent_ProofType", ProofShowContent_ProofType_name, ProofShowContent_ProofType_value)
	proto.RegisterEnum("contract_http_logic.CheckCanCancelContractResp_CancelStage", CheckCanCancelContractResp_CancelStage_name, CheckCanCancelContractResp_CancelStage_value)
	proto.RegisterEnum("contract_http_logic.ProcPromoteInviteReq_ProcResType", ProcPromoteInviteReq_ProcResType_name, ProcPromoteInviteReq_ProcResType_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/contract-http-logic/contract-http-logic.proto", fileDescriptor_contract_http_logic_55e2a9e7d2ade33b)
}

var fileDescriptor_contract_http_logic_55e2a9e7d2ade33b = []byte{
	// 3324 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x5a, 0x4f, 0x73, 0x23, 0xc7,
	0x75, 0xf7, 0x80, 0x5c, 0x12, 0x78, 0xf8, 0x43, 0xec, 0x2c, 0x97, 0x04, 0x25, 0x31, 0xa2, 0x66,
	0xa5, 0x0d, 0x4b, 0xb2, 0xb9, 0xf6, 0xaa, 0xe4, 0x54, 0xca, 0x29, 0xc7, 0x5c, 0x52, 0xa6, 0x29,
	0x2f, 0x29, 0x7a, 0xc8, 0xdd, 0xb5, 0x74, 0xc8, 0xa4, 0x39, 0xd3, 0x04, 0xdb, 0x18, 0x4c, 0xcf,
	0x4e, 0x37, 0x40, 0xa1, 0x72, 0x48, 0x55, 0xbe, 0x43, 0x2e, 0x29, 0x57, 0xf9, 0x9e, 0x93, 0x73,
	0xca, 0x31, 0xc9, 0x29, 0xb7, 0x54, 0xbe, 0x40, 0xf2, 0x05, 0x72, 0x4b, 0x95, 0xef, 0xa9, 0xf7,
	0xba, 0x67, 0x30, 0xf8, 0x43, 0x82, 0x51, 0x94, 0xdb, 0xf4, 0xef, 0xbd, 0x7e, 0xfd, 0xfa, 0xf5,
	0xeb, 0xf7, 0xa7, 0x01, 0xf8, 0x33, 0xad, 0x9f, 0xbd, 0x1d, 0x88, 0xb0, 0xa7, 0x44, 0x3c, 0xe4,
	0xd9, 0xb3, 0x50, 0x26, 0x3a, 0x63, 0xa1, 0xfe, 0xc1, 0xb5, 0xd6, 0xe9, 0x0f, 0x62, 0xd9, 0x15,
	0xe1, 0x3c, 0x6c, 0x2f, 0xcd, 0xa4, 0x96, 0xee, 0xa3, 0x9c, 0x14, 0x20, 0x29, 0x20, 0x92, 0xf7,
	0x1f, 0x0e, 0xac, 0x1d, 0x71, 0xfd, 0x0b, 0x16, 0xc7, 0x17, 0x4c, 0xf5, 0x7c, 0xae, 0x52, 0xf7,
	0x08, 0x9a, 0x11, 0x1b, 0x05, 0x9a, 0xa9, 0x5e, 0x10, 0x0b, 0xa5, 0x3b, 0xce, 0xce, 0xd2, 0x6e,
	0xfd, 0xf9, 0x93, 0xbd, 0x39, 0x02, 0xf6, 0xf2, 0x99, 0x87, 0x5c, 0x0b, 0x16, 0xfb, 0xf5, 0x88,
	0x8d, 0x70, 0xf8, 0x52, 0x28, 0xed, 0x1e, 0x43, 0xeb, 0x86, 0xf3, 0x5e, 0x49, 0x52, 0xe5, 0xfe,
	0x92, 0x1a, 0x38, 0xb5, 0x10, 0xb5, 0x0d, 0x90, 0xf1, 0x1b, 0x96, 0x45, 0x41, 0x5f, 0x75, 0x3b,
	0x4b, 0x3b, 0xce, 0x6e, 0xcd, 0xaf, 0x19, 0xe4, 0x44, 0x75, 0xdd, 0x4d, 0x58, 0x15, 0x2a, 0x50,
	0xd7, 0xf2, 0xa6, 0xb3, 0xbc, 0xe3, 0xec, 0x56, 0xfd, 0x15, 0xa1, 0xce, 0xaf, 0xe5, 0x8d, 0xf7,
	0x7b, 0x07, 0x5a, 0x93, 0x82, 0xdd, 0x77, 0xa1, 0x46, 0x0a, 0x25, 0xac, 0xcf, 0x3b, 0x0e, 0x49,
	0xaa, 0x22, 0x70, 0xca, 0xfa, 0xdc, 0x7d, 0x02, 0x4d, 0x22, 0xa6, 0x99, 0xec, 0x66, 0x5c, 0xa9,
	0x4e, 0x85, 0x18, 0x1a, 0x08, 0x9e, 0x59, 0xcc, 0x75, 0x61, 0x39, 0x63, 0x9a, 0x93, 0x1a, 0x15,
	0x9f, 0xbe, 0xdd, 0x2d, 0x20, 0x21, 0xc1, 0x90, 0xc5, 0xa4, 0x42, 0xd3, 0x5f, 0xc5, 0xf1, 0x6b,
	0x16, 0x23, 0x69, 0xc8, 0x62, 0x63, 0x80, 0x07, 0x3b, 0x4b, 0xbb, 0x35, 0x7f, 0x75, 0xc8, 0x62,
	0xda, 0x96, 0x0b, 0xcb, 0x11, 0x4a, 0x5a, 0xa1, 0x55, 0xe8, 0xdb, 0xfb, 0x35, 0x6c, 0x94, 0x4e,
	0xe4, 0x17, 0x42, 0x69, 0x99, 0x8d, 0xe8, 0x60, 0x7e, 0x0a, 0xcb, 0xa5, 0xf3, 0xf8, 0xf8, 0x4e,
	0x2b, 0xda, 0x79, 0xd6, 0x98, 0x34, 0xcf, 0xfb, 0x27, 0x07, 0x1e, 0xcf, 0xa5, 0x17, 0x7a, 0x38,
	0x63, 0x3d, 0x66, 0xdd, 0xa0, 0xf2, 0x9d, 0xb9, 0xc1, 0xd2, 0xb7, 0x74, 0x03, 0xef, 0x4b, 0x78,
	0x7c, 0xc4, 0xf5, 0xe7, 0xe7, 0xa9, 0xcc, 0xf4, 0x81, 0x64, 0xe1, 0xf5, 0x71, 0x72, 0x25, 0x7d,
	0xfe, 0x16, 0x6d, 0xdc, 0x1d, 0x88, 0x38, 0x0a, 0x44, 0x44, 0x9b, 0x68, 0xfa, 0xab, 0x34, 0x3e,
	0x8e, 0xf0, 0xbc, 0x43, 0x64, 0x0d, 0x06, 0x22, 0xa2, 0xe3, 0x6c, 0xfa, 0x55, 0x02, 0x5e, 0x89,
	0xc8, 0xfb, 0xad, 0x03, 0xcd, 0x57, 0x8a, 0x67, 0xe7, 0x3d, 0x11, 0xc7, 0xc7, 0x9a, 0xf7, 0xd1,
	0x95, 0xba, 0xac, 0xcf, 0xc7, 0x82, 0x56, 0x70, 0x68, 0xe4, 0x10, 0x81, 0xfc, 0xc6, 0xb8, 0x45,
	0x15, 0x01, 0xf2, 0x9b, 0x9c, 0x28, 0x42, 0x99, 0x58, 0xf7, 0x24, 0xe2, 0x71, 0x28, 0x93, 0x82,
	0xa8, 0x47, 0x29, 0xb7, 0xce, 0x41, 0xc4, 0x8b, 0x51, 0x4a, 0x33, 0x11, 0x0f, 0x94, 0xcc, 0xd0,
	0x3d, 0x88, 0x88, 0xc0, 0xb9, 0xcc, 0xb4, 0xf7, 0x87, 0x0a, 0xac, 0x99, 0xdd, 0x9e, 0xc8, 0x44,
	0x5f, 0x1f, 0x32, 0xcd, 0xdc, 0x0f, 0xa0, 0x21, 0xb3, 0x88, 0x67, 0x01, 0xeb, 0xcb, 0x41, 0xa2,
	0x3b, 0xf5, 0x1d, 0x67, 0x77, 0xd9, 0xaf, 0x13, 0xb6, 0x4f, 0x10, 0xb2, 0x68, 0xa9, 0x59, 0x1c,
	0x10, 0xa8, 0x3a, 0x0d, 0xc3, 0x42, 0xd8, 0x97, 0x04, 0xb9, 0x4f, 0x61, 0x4d, 0xb3, 0x1e, 0x37,
	0x1c, 0x41, 0xc4, 0x46, 0xaa, 0xd3, 0xa4, 0xc5, 0x9b, 0x08, 0x13, 0xd3, 0x21, 0x1b, 0x29, 0xf7,
	0x7d, 0xa8, 0xb3, 0x50, 0x8b, 0x21, 0x37, 0x3c, 0x2d, 0xe2, 0x01, 0x03, 0x11, 0xc3, 0x13, 0x68,
	0xa6, 0x5c, 0xa6, 0x31, 0x0f, 0x14, 0xcf, 0x86, 0x3c, 0xea, 0xac, 0x11, 0x4b, 0xc3, 0x80, 0xe7,
	0x84, 0x21, 0x53, 0xc2, 0x6f, 0x82, 0x70, 0xa0, 0xb4, 0xec, 0xa3, 0x46, 0x6d, 0xc3, 0x94, 0xf0,
	0x9b, 0x83, 0x1c, 0x73, 0x77, 0xa0, 0x9e, 0xf1, 0x74, 0x90, 0x85, 0xd7, 0x4c, 0x71, 0xd5, 0x79,
	0x48, 0x2c, 0x65, 0x08, 0xf7, 0x35, 0x14, 0x32, 0x66, 0x5a, 0xc8, 0x44, 0x05, 0xac, 0xe3, 0x1a,
	0x96, 0x31, 0xb6, 0x3f, 0xc5, 0x72, 0xd9, 0x79, 0x34, 0xcd, 0xf2, 0x62, 0x8a, 0x25, 0xec, 0xac,
	0x4f, 0xb3, 0x1c, 0x78, 0xff, 0xbc, 0x44, 0x97, 0x70, 0xc6, 0xd1, 0x54, 0x7a, 0x97, 0xa7, 0x7d,
	0x08, 0x2d, 0x43, 0x52, 0xd7, 0x32, 0xd3, 0x41, 0xe1, 0x6e, 0x0d, 0x42, 0xcf, 0x11, 0x3c, 0x8e,
	0x30, 0x94, 0x19, 0x2e, 0x72, 0x24, 0x1b, 0xca, 0x08, 0xc9, 0x3d, 0x69, 0xec, 0xae, 0xcb, 0x93,
	0xee, 0x8a, 0x73, 0x0d, 0x51, 0x6b, 0x11, 0x91, 0xb7, 0xd4, 0x7c, 0xc3, 0x7e, 0xa1, 0xcb, 0xe4,
	0x44, 0x84, 0x3d, 0x1b, 0x54, 0x0c, 0xf9, 0x54, 0x84, 0x3d, 0x77, 0x0f, 0xc6, 0x39, 0xe0, 0x92,
	0x77, 0x45, 0x12, 0x68, 0xd1, 0xe7, 0x9d, 0xd5, 0x1d, 0x67, 0x77, 0xc9, 0x7f, 0x98, 0x93, 0x5e,
	0x20, 0xe5, 0x42, 0xf4, 0xb9, 0xfb, 0x31, 0x14, 0x60, 0xc0, 0x93, 0xc8, 0x70, 0x57, 0x89, 0x7b,
	0x2d, 0x27, 0x7c, 0x9e, 0x44, 0xc4, 0xbb, 0x0f, 0xa0, 0xf0, 0x0e, 0x99, 0x0b, 0x5e, 0xa3, 0x0b,
	0xee, 0xcd, 0xbd, 0xe0, 0x13, 0xd7, 0xcd, 0xaf, 0xd1, 0x2c, 0x8a, 0x13, 0x2f, 0x61, 0x4d, 0x5f,
	0x0b, 0x15, 0xf4, 0xd1, 0xd5, 0x83, 0x88, 0x69, 0xd6, 0x79, 0xbc, 0xe3, 0xec, 0xd6, 0x9f, 0x7f,
	0x38, 0x57, 0xce, 0xd4, 0xbd, 0xf0, 0x9b, 0x38, 0xb9, 0x18, 0x7a, 0x31, 0xd4, 0xcf, 0x79, 0x88,
	0xe7, 0x89, 0x47, 0x87, 0x87, 0xae, 0xcc, 0xb0, 0x1c, 0xf8, 0xeb, 0x16, 0xcb, 0x2d, 0x2f, 0x34,
	0xef, 0x8f, 0x83, 0x5d, 0xcd, 0xaf, 0x22, 0x90, 0x27, 0xa0, 0x7c, 0xbe, 0x88, 0xe8, 0xd4, 0x9a,
	0x7e, 0xcd, 0x22, 0xc7, 0x91, 0xf7, 0xaf, 0x95, 0x72, 0x1c, 0xc1, 0x05, 0xbf, 0x5d, 0x1c, 0xf9,
	0x08, 0x5a, 0xc6, 0x8c, 0x7c, 0x28, 0x22, 0x9e, 0x84, 0xb9, 0x83, 0x34, 0x09, 0xfd, 0xdc, 0x82,
	0xa4, 0x0d, 0xb1, 0x45, 0x5c, 0x85, 0xe4, 0x25, 0x35, 0x6b, 0xc9, 0x43, 0xae, 0x42, 0x77, 0x1d,
	0x1e, 0xb0, 0x41, 0x24, 0xa4, 0xf5, 0x10, 0x33, 0x40, 0xd9, 0xf4, 0x11, 0x44, 0x83, 0x8c, 0x1c,
	0x9d, 0x3c, 0xa4, 0xe9, 0x37, 0x09, 0x3d, 0xb4, 0xa0, 0x7b, 0x30, 0xb6, 0x14, 0x59, 0x62, 0x95,
	0xce, 0x72, 0x67, 0xee, 0x19, 0x94, 0x2c, 0x5c, 0xd8, 0x92, 0xcc, 0x85, 0x51, 0x8d, 0x7f, 0xa3,
	0x8d, 0x7e, 0x55, 0x9b, 0x64, 0xf9, 0x37, 0x9a, 0xd4, 0xcb, 0x2d, 0x90, 0xb1, 0xa4, 0xd7, 0xa9,
	0x8d, 0xe3, 0xa1, 0xcf, 0x92, 0x9e, 0xf7, 0x2b, 0xd8, 0x2a, 0x6e, 0xde, 0x84, 0x45, 0x31, 0xcc,
	0x4f, 0x5c, 0x0e, 0x67, 0xea, 0x72, 0x94, 0x2c, 0x5e, 0x29, 0x5b, 0xdc, 0xfb, 0x5b, 0x07, 0xde,
	0xb9, 0x4d, 0xa6, 0x4a, 0xbf, 0x9d, 0xd0, 0xb1, 0xc3, 0x8b, 0xe4, 0x4a, 0xd2, 0x29, 0x2d, 0x76,
	0x78, 0x5c, 0xd1, 0x1c, 0x13, 0x7e, 0x7a, 0x5f, 0xc1, 0xe6, 0xb9, 0xe8, 0x26, 0xc7, 0x11, 0x4f,
	0xb4, 0xd0, 0xa3, 0xb3, 0x8c, 0x1f, 0x5c, 0xf3, 0xb0, 0xb7, 0x20, 0x9f, 0x3d, 0x81, 0xa6, 0xb0,
	0x33, 0x4c, 0x46, 0xb1, 0x41, 0x26, 0x07, 0x31, 0xab, 0x78, 0xef, 0x40, 0x67, 0xbe, 0x68, 0x95,
	0x7a, 0xff, 0xe5, 0xc0, 0xe3, 0xfd, 0x34, 0x8d, 0x47, 0xc8, 0xe1, 0x0b, 0xd5, 0x2b, 0x56, 0x9d,
	0x11, 0xed, 0xcc, 0x8a, 0x46, 0x73, 0x45, 0x7c, 0x28, 0xc2, 0xc2, 0x26, 0x35, 0xbf, 0x6a, 0x80,
	0xe3, 0x08, 0xd3, 0x45, 0x18, 0x0b, 0x9e, 0x68, 0x33, 0xdf, 0xdc, 0x13, 0x30, 0x10, 0xcd, 0xc6,
	0x7c, 0x92, 0xa6, 0xc1, 0x90, 0x67, 0x0a, 0x3d, 0x70, 0xd9, 0xe6, 0x93, 0x34, 0x7d, 0x6d, 0x10,
	0xf7, 0x53, 0xd8, 0xb8, 0x62, 0x21, 0x0f, 0xd8, 0x40, 0x5f, 0x07, 0x19, 0x57, 0x83, 0x58, 0x07,
	0x5a, 0xf6, 0x78, 0x62, 0x9d, 0xf9, 0x11, 0x52, 0xf7, 0x07, 0xfa, 0xda, 0x27, 0xda, 0x05, 0x92,
	0x26, 0xcc, 0xb5, 0x32, 0x61, 0x2e, 0xef, 0x04, 0x36, 0xe6, 0x6d, 0x56, 0xa5, 0x93, 0x2b, 0xe1,
	0xc1, 0xa1, 0xc3, 0xfe, 0x46, 0xc9, 0xc4, 0x06, 0x87, 0x62, 0xa5, 0x03, 0x43, 0xfb, 0x42, 0xc9,
	0xc4, 0xfb, 0x1a, 0x1a, 0x63, 0x71, 0xf7, 0x35, 0xd9, 0x47, 0xd0, 0x2a, 0x1c, 0x03, 0xaf, 0xbd,
	0xb2, 0xe1, 0xa5, 0x99, 0xa3, 0x78, 0xf7, 0x95, 0xb7, 0x06, 0xcd, 0x92, 0x6c, 0x95, 0x7a, 0xaf,
	0xa0, 0x79, 0x84, 0xdb, 0x78, 0x31, 0x50, 0x22, 0xb1, 0x95, 0x67, 0x29, 0x7a, 0xd1, 0x37, 0x62,
	0x25, 0x37, 0xa0, 0x6f, 0xf7, 0x3d, 0xa8, 0xb1, 0x21, 0x13, 0x31, 0xbb, 0x8c, 0xcd, 0x21, 0x54,
	0xfd, 0x31, 0xe0, 0xfd, 0x6e, 0x19, 0x1a, 0x17, 0x32, 0x25, 0xd1, 0x14, 0xab, 0xee, 0xf0, 0xb6,
	0x2d, 0xa8, 0x4e, 0x65, 0xb3, 0x55, 0x65, 0x13, 0x59, 0xae, 0xcc, 0x52, 0x49, 0x19, 0xbc, 0xda,
	0x24, 0x49, 0xb3, 0xae, 0x8d, 0x4b, 0x46, 0xf4, 0x05, 0xeb, 0x62, 0xd4, 0x92, 0x37, 0x09, 0xcf,
	0x4c, 0xe8, 0xb3, 0xd9, 0x8b, 0x90, 0xbc, 0xf6, 0x36, 0x64, 0x16, 0x86, 0x54, 0xd9, 0x98, 0x04,
	0xd6, 0x20, 0x70, 0xdf, 0x60, 0xe8, 0x3f, 0x96, 0x29, 0x16, 0x4c, 0x51, 0xee, 0xaa, 0xf9, 0x46,
	0xec, 0x3e, 0x22, 0xa8, 0x81, 0x61, 0xc0, 0xdb, 0x5c, 0x35, 0xb7, 0x99, 0x00, 0xbc, 0xcd, 0x47,
	0xd0, 0xbc, 0xb4, 0xb6, 0x5c, 0x9c, 0xa8, 0x26, 0x4c, 0xef, 0x37, 0xf2, 0x89, 0x79, 0xe1, 0x4e,
	0xd1, 0x0b, 0x8c, 0xd1, 0xf1, 0x1b, 0xf5, 0xcf, 0x78, 0x28, 0xfb, 0x7d, 0xca, 0x95, 0xac, 0x4b,
	0x95, 0x59, 0xcd, 0x6f, 0x14, 0x20, 0xda, 0xe0, 0x09, 0xd4, 0xd9, 0xa5, 0x88, 0xc9, 0x5d, 0x58,
	0x97, 0x2a, 0xb3, 0xda, 0x8b, 0x4a, 0xc7, 0xf1, 0xc1, 0xc2, 0xc8, 0xf4, 0x3e, 0xd4, 0xaf, 0x65,
	0x22, 0xb3, 0x40, 0x0b, 0x1d, 0x73, 0x2a, 0xcc, 0x6a, 0x3e, 0x10, 0x74, 0x81, 0x08, 0x6e, 0xf2,
	0x2a, 0x93, 0x7d, 0x93, 0x91, 0x5b, 0x94, 0x91, 0xab, 0x08, 0x50, 0x2a, 0xde, 0x84, 0x55, 0x2d,
	0x0d, 0x69, 0x8d, 0x48, 0x2b, 0x5a, 0x12, 0x61, 0x17, 0xda, 0xa5, 0xb5, 0x8d, 0x01, 0xda, 0xe4,
	0x88, 0xad, 0xf1, 0xe2, 0x54, 0x67, 0x6f, 0xc3, 0xbb, 0x47, 0x5c, 0xfb, 0x85, 0xe2, 0xd6, 0x59,
	0x90, 0xe6, 0xf3, 0xb7, 0xde, 0x5f, 0xc2, 0x7b, 0xb7, 0x93, 0x55, 0xea, 0xfe, 0x2c, 0x2f, 0x71,
	0x4a, 0xed, 0xca, 0x07, 0x73, 0x6d, 0x5c, 0x76, 0x43, 0x5b, 0x05, 0x91, 0x02, 0x7d, 0xca, 0x02,
	0x07, 0x2c, 0x09, 0x79, 0x7c, 0x60, 0xe7, 0xe1, 0x55, 0x52, 0x0b, 0x82, 0xe3, 0x3a, 0x3c, 0x50,
	0x21, 0x4f, 0xf2, 0xdb, 0x60, 0x06, 0x68, 0xcf, 0x1b, 0x99, 0xf5, 0x78, 0x36, 0x11, 0x95, 0x0c,
	0x44, 0xe1, 0xf2, 0x5f, 0x1c, 0x78, 0x78, 0xc0, 0x92, 0xc9, 0xf5, 0x28, 0x98, 0x11, 0x52, 0xbe,
	0xd9, 0x60, 0x20, 0xba, 0xd7, 0x1d, 0x58, 0xe5, 0x09, 0x5e, 0x29, 0x73, 0x37, 0xaa, 0x7e, 0x3e,
	0x9c, 0x7b, 0x37, 0xb0, 0xc9, 0x1a, 0xa7, 0x6b, 0xfa, 0xc6, 0x23, 0xc1, 0xae, 0x35, 0xb8, 0x1c,
	0xe8, 0x20, 0x12, 0x8a, 0xee, 0xeb, 0x03, 0x12, 0xd5, 0x42, 0xfc, 0xc5, 0x40, 0x1f, 0x1a, 0x14,
	0x95, 0xb1, 0x0c, 0xd4, 0x02, 0x9b, 0xbb, 0x01, 0x16, 0x3a, 0x51, 0x5d, 0xef, 0x6f, 0x4c, 0x96,
	0x9b, 0x6b, 0x33, 0x95, 0xba, 0xc7, 0xd0, 0x28, 0x6d, 0x46, 0xd9, 0x53, 0x79, 0x3a, 0xf7, 0x54,
	0x66, 0x4c, 0xe1, 0xd7, 0xc7, 0xbb, 0x56, 0x68, 0xff, 0x94, 0x8d, 0x82, 0x50, 0x52, 0x9d, 0x84,
	0x1e, 0xb6, 0x9a, 0xb2, 0xd1, 0x81, 0x54, 0xda, 0xfb, 0x83, 0x63, 0xc3, 0xed, 0xd4, 0xfc, 0xbb,
	0x4f, 0x6d, 0xca, 0xd0, 0x95, 0x19, 0x43, 0x6f, 0x03, 0xa4, 0x99, 0x94, 0x57, 0xc1, 0x20, 0x8b,
	0x15, 0xb5, 0x8f, 0x35, 0xbf, 0x46, 0xc8, 0xab, 0x2c, 0x2e, 0x14, 0x2a, 0x59, 0x17, 0x15, 0xa2,
	0x5a, 0xe3, 0x67, 0xf9, 0xcc, 0xa2, 0xfd, 0xbe, 0xcd, 0x15, 0xcf, 0x90, 0x8d, 0x42, 0x7d, 0xa2,
	0xad, 0x70, 0xba, 0xea, 0x4f, 0xa0, 0x69, 0x95, 0xcb, 0x38, 0x53, 0xb6, 0x6a, 0xaa, 0xf9, 0xd6,
	0x9a, 0x3e, 0x61, 0xde, 0xbf, 0x39, 0xd0, 0x28, 0x0b, 0x70, 0xdb, 0xb0, 0xd4, 0xe3, 0x23, 0x1b,
	0xa8, 0xf1, 0x93, 0x8a, 0x73, 0x9e, 0x28, 0x99, 0x05, 0x48, 0xa8, 0xd8, 0xe2, 0x9c, 0x90, 0x5f,
	0xf2, 0x91, 0xfb, 0xe7, 0x36, 0x8c, 0xa3, 0xc7, 0xb4, 0x9e, 0x7f, 0xb2, 0x50, 0x45, 0x33, 0x40,
	0xeb, 0x98, 0x98, 0xef, 0xbd, 0x84, 0x5a, 0x01, 0xb9, 0x8f, 0xe1, 0x61, 0x31, 0x08, 0x8e, 0x93,
	0x21, 0x8b, 0x45, 0xd4, 0xfe, 0x9e, 0xfb, 0x08, 0xd6, 0xc6, 0xf0, 0x6b, 0x11, 0x71, 0xd9, 0x76,
	0x26, 0xc1, 0xe3, 0x3e, 0xeb, 0xf2, 0x76, 0xc5, 0xfb, 0x47, 0x07, 0xda, 0x84, 0x9e, 0x5f, 0xcb,
	0x9b, 0xd2, 0xa6, 0x06, 0x59, 0x9c, 0x6f, 0x6a, 0x90, 0xc5, 0xee, 0x41, 0x29, 0xf9, 0xb4, 0x9e,
	0x3f, 0xbb, 0x5d, 0xeb, 0x92, 0x98, 0xff, 0x67, 0xcd, 0xb7, 0x60, 0x73, 0xae, 0x07, 0xaa, 0xd4,
	0xfb, 0x14, 0x36, 0x5e, 0xca, 0xb0, 0x77, 0xc6, 0x2c, 0xd1, 0xb4, 0xcb, 0x77, 0x3b, 0xa7, 0x17,
	0xc3, 0xe6, 0xdc, 0x49, 0xa6, 0x17, 0x2c, 0x2e, 0x82, 0x33, 0x71, 0x11, 0x30, 0x42, 0xf3, 0x6f,
	0x52, 0x91, 0xf1, 0x40, 0x2b, 0x7b, 0x49, 0xaa, 0x06, 0xb8, 0x20, 0x7f, 0x55, 0x9a, 0x65, 0x1a,
	0x69, 0x4b, 0x66, 0x1e, 0x8d, 0x2f, 0x94, 0xf7, 0x63, 0xd8, 0xa2, 0x0a, 0x65, 0xf6, 0x0a, 0xde,
	0xad, 0xe5, 0x3f, 0x3c, 0x80, 0x77, 0x6e, 0x9b, 0xa8, 0x52, 0xd7, 0x83, 0x66, 0x2c, 0xc3, 0x5e,
	0x30, 0xa5, 0x6e, 0x3d, 0xb6, 0x3b, 0x43, 0x95, 0x3f, 0x84, 0x16, 0xf1, 0x4c, 0xeb, 0xdd, 0x40,
	0xf4, 0xf3, 0x5c, 0xf7, 0x5c, 0xd2, 0xd4, 0x06, 0x48, 0xd2, 0xb9, 0xd9, 0x84, 0xfb, 0x17, 0x45,
	0xac, 0x51, 0x9a, 0x75, 0xcd, 0x9b, 0x47, 0xeb, 0xf9, 0x4f, 0xe6, 0xc7, 0x9a, 0x5b, 0x95, 0xde,
	0x33, 0xd0, 0x39, 0x8a, 0xc8, 0x03, 0x10, 0x0d, 0xdc, 0xa7, 0xb0, 0x46, 0x82, 0x4b, 0xaa, 0x3e,
	0x20, 0x2d, 0x9a, 0x04, 0xe7, 0xba, 0x7a, 0xbf, 0x5d, 0x82, 0x7a, 0x49, 0x88, 0xbb, 0x0d, 0x5b,
	0xb3, 0xe1, 0xf1, 0x90, 0x5f, 0xb1, 0x41, 0xac, 0xdb, 0xdf, 0x73, 0x9f, 0x82, 0x37, 0x4b, 0x7e,
	0xc3, 0x84, 0x16, 0x49, 0xf7, 0xe7, 0x32, 0x3b, 0x61, 0x4a, 0xf3, 0xac, 0xed, 0xb8, 0xbb, 0xf0,
	0xe1, 0x5d, 0x7c, 0x5f, 0x5e, 0x5d, 0x89, 0x50, 0xb0, 0xb8, 0x5d, 0x71, 0x3f, 0x81, 0x3f, 0x5e,
	0x2c, 0xf1, 0x57, 0x03, 0xc1, 0x75, 0x7b, 0xc9, 0xdd, 0x83, 0x8f, 0x17, 0x33, 0x9f, 0x4a, 0x13,
	0x71, 0xda, 0xcb, 0xee, 0xc7, 0xf0, 0x74, 0x31, 0xff, 0x4b, 0x31, 0xe4, 0xed, 0x07, 0x8b, 0x14,
	0xc9, 0x55, 0x3e, 0x63, 0xa3, 0xf6, 0x8a, 0xfb, 0x0c, 0x3e, 0x59, 0x2c, 0xf8, 0x8c, 0x8d, 0xf6,
	0xc3, 0x90, 0xa7, 0xba, 0xbd, 0xea, 0x7e, 0x06, 0x3f, 0xba, 0x87, 0xe6, 0xbc, 0x2b, 0xb5, 0x60,
	0x9a, 0xdb, 0x69, 0x55, 0x4f, 0xc0, 0xf6, 0x11, 0xd7, 0x73, 0x2e, 0xab, 0xad, 0x33, 0xee, 0x4a,
	0x19, 0x2e, 0x2c, 0xa7, 0xe8, 0x5a, 0xb6, 0xea, 0xc5, 0x6f, 0x73, 0x1d, 0xbb, 0x3c, 0x48, 0x06,
	0x7d, 0x9b, 0xe3, 0x57, 0x71, 0x7c, 0x3a, 0xe8, 0x7b, 0xff, 0xbd, 0x34, 0x37, 0x2a, 0xd0, 0x8b,
	0x1f, 0x46, 0xb5, 0x62, 0x01, 0xfc, 0x9c, 0x58, 0xb7, 0x72, 0x67, 0xaa, 0x5a, 0x9a, 0x57, 0x13,
	0xe4, 0xf5, 0xab, 0x4d, 0x45, 0x76, 0xe8, 0xbe, 0x03, 0xd5, 0x44, 0x84, 0xbd, 0x52, 0xf1, 0x5b,
	0x8c, 0xa9, 0x63, 0xa7, 0x82, 0x76, 0xc5, 0x76, 0xec, 0x54, 0xcb, 0x6e, 0x03, 0x76, 0x46, 0xf1,
	0x68, 0xfc, 0x4e, 0xd3, 0xf4, 0x6b, 0x84, 0x50, 0x3d, 0xb7, 0x05, 0xd5, 0x89, 0x67, 0x99, 0x26,
	0xd6, 0x1f, 0xe6, 0x39, 0xe6, 0x87, 0xb0, 0xce, 0x92, 0xf0, 0x5a, 0x66, 0x41, 0xd1, 0x9d, 0x14,
	0xf5, 0x6e, 0xd3, 0x77, 0x0d, 0x2d, 0xef, 0x0e, 0x29, 0xcd, 0x6d, 0xc0, 0x8a, 0xcd, 0x6f, 0x40,
	0x2a, 0xd8, 0x11, 0xdd, 0x35, 0xac, 0x50, 0x18, 0x9d, 0x5a, 0x70, 0xa9, 0x13, 0xaa, 0x6b, 0xab,
	0x7e, 0x13, 0x61, 0x73, 0x96, 0x2f, 0xf4, 0x98, 0x2f, 0xe3, 0xbf, 0xe1, 0xa1, 0xe1, 0x6b, 0x8c,
	0xf9, 0x7c, 0x42, 0x91, 0xef, 0x70, 0x22, 0x21, 0x37, 0x29, 0x21, 0x7f, 0x74, 0xaf, 0xbc, 0x71,
	0x67, 0x52, 0x6e, 0xcd, 0x49, 0xca, 0x7f, 0x0d, 0x7f, 0x74, 0x97, 0x7f, 0x51, 0xa1, 0x5a, 0x7e,
	0x51, 0xff, 0xfe, 0x5c, 0x35, 0x6e, 0x71, 0x1b, 0xf3, 0xa6, 0x8e, 0x71, 0x3e, 0xc1, 0xbe, 0xb1,
	0xe4, 0x8c, 0x55, 0x04, 0xce, 0x58, 0x97, 0x7b, 0xbf, 0x86, 0xf7, 0x8c, 0x81, 0x6e, 0x29, 0x89,
	0xb6, 0x01, 0x34, 0xcb, 0xba, 0x5c, 0x97, 0x9e, 0x1e, 0x6a, 0x06, 0x79, 0x75, 0xa7, 0x1b, 0x7a,
	0xef, 0xc3, 0xf6, 0x1d, 0x92, 0x55, 0xea, 0xfd, 0xa7, 0x03, 0xef, 0x19, 0xa3, 0x7f, 0xd7, 0x6b,
	0x2f, 0x2a, 0xc6, 0xa8, 0x0d, 0x22, 0x1f, 0xb0, 0x47, 0xb3, 0x9c, 0xb7, 0x41, 0x08, 0x9a, 0xa3,
	0xf9, 0xbf, 0x97, 0x65, 0x68, 0x81, 0x3b, 0xf6, 0xa7, 0x52, 0x4f, 0x41, 0x2b, 0x1f, 0xbf, 0xa1,
	0x4a, 0x7f, 0xba, 0x0d, 0x70, 0xa6, 0xdb, 0x80, 0x12, 0x43, 0xe9, 0x71, 0xce, 0x32, 0x50, 0x8b,
	0x3a, 0x66, 0xa0, 0x5a, 0x73, 0xa9, 0xcc, 0x80, 0xe5, 0xa6, 0x17, 0xc3, 0xc3, 0x7c, 0xd1, 0xb3,
	0x4c, 0x0c, 0x45, 0xcc, 0xbb, 0xfc, 0xb6, 0xae, 0x9d, 0x7e, 0x2b, 0x30, 0x6b, 0xd0, 0x37, 0x3d,
	0x40, 0xaa, 0x40, 0xf1, 0x98, 0x87, 0xda, 0x76, 0xed, 0x55, 0xa1, 0xce, 0x69, 0x4c, 0x42, 0xa4,
	0xe6, 0x79, 0xf7, 0x80, 0xdf, 0xde, 0x5f, 0xc1, 0xc6, 0xcc, 0x6a, 0x47, 0x99, 0x1c, 0xa4, 0x73,
	0x97, 0x3c, 0x81, 0x56, 0x9a, 0x73, 0x95, 0x7f, 0xd1, 0xb9, 0xa5, 0x07, 0x98, 0x16, 0xec, 0x37,
	0x8b, 0xd9, 0x74, 0x00, 0x5f, 0x51, 0x8f, 0x38, 0xc3, 0x76, 0x8f, 0xd8, 0x3d, 0x75, 0x0e, 0x95,
	0x99, 0x76, 0xec, 0xf7, 0x0e, 0x35, 0x98, 0xb7, 0xc8, 0x56, 0xa9, 0xfb, 0x05, 0x40, 0x17, 0xf7,
	0x59, 0x6e, 0x30, 0x3f, 0xb9, 0xdf, 0x36, 0xc8, 0x3e, 0x7e, 0x8d, 0xa6, 0x53, 0x28, 0x39, 0x2c,
	0xb4, 0x59, 0xf8, 0x2b, 0xd7, 0xa4, 0x3f, 0xe5, 0x2a, 0x93, 0x35, 0xce, 0xa0, 0x75, 0x2e, 0xba,
	0x09, 0xa6, 0xdb, 0xf3, 0xc1, 0x65, 0x5f, 0x50, 0xb1, 0x8c, 0x1d, 0xbe, 0x4d, 0x2b, 0x9a, 0x75,
	0xf1, 0x50, 0x62, 0x91, 0xf4, 0xf2, 0x33, 0xc7, 0x6f, 0x4c, 0x17, 0xb8, 0x12, 0xb3, 0x27, 0x5e,
	0xf3, 0xf3, 0xa1, 0xf7, 0xef, 0x0e, 0xac, 0x17, 0xcf, 0x41, 0xf7, 0x6c, 0xa4, 0xee, 0xf3, 0x36,
	0x88, 0x79, 0xa8, 0x78, 0x01, 0x36, 0xf9, 0xab, 0x18, 0x4f, 0x1f, 0xcd, 0xf2, 0xcc, 0x15, 0xf9,
	0x09, 0xac, 0x28, 0xda, 0x1f, 0xa5, 0xb0, 0xdb, 0x0c, 0x35, 0x69, 0x0a, 0xdf, 0x4e, 0xf1, 0x36,
	0x4b, 0x0f, 0x8f, 0x13, 0x77, 0xf5, 0xa7, 0x50, 0x3f, 0xcb, 0x64, 0x5f, 0x6a, 0x4e, 0xef, 0x51,
	0x2d, 0xa8, 0x14, 0x7b, 0xab, 0x08, 0x72, 0x18, 0x25, 0xba, 0x89, 0xf9, 0x65, 0x40, 0xe5, 0x0e,
	0x83, 0x10, 0xbd, 0xf7, 0x2b, 0xef, 0x53, 0x68, 0xbf, 0x52, 0x3c, 0x2b, 0xe2, 0x33, 0x0a, 0x59,
	0x74, 0xdb, 0xbd, 0x0e, 0xfd, 0xc6, 0x33, 0x3d, 0xcf, 0xe7, 0x6f, 0xbd, 0xbf, 0x77, 0x60, 0x73,
	0x2e, 0x49, 0xa5, 0xee, 0x01, 0x34, 0x52, 0xa3, 0xaa, 0x79, 0xf9, 0x75, 0xc8, 0x0c, 0x3b, 0xb7,
	0xc5, 0xae, 0x7c, 0x4f, 0x7e, 0x3d, 0x2d, 0x6d, 0xf0, 0x0b, 0x28, 0x9e, 0xfe, 0x8c, 0x94, 0x0a,
	0x49, 0xf9, 0xe8, 0xd6, 0xf7, 0xe3, 0x09, 0x35, 0x1a, 0x61, 0x69, 0xe4, 0xfd, 0x9d, 0x03, 0xeb,
	0x67, 0x99, 0x0c, 0x8b, 0xc5, 0x86, 0x42, 0x73, 0xeb, 0x27, 0x69, 0x26, 0xc3, 0x20, 0xa3, 0x6e,
	0xdf, 0x94, 0x43, 0x99, 0x0c, 0x7d, 0xae, 0xac, 0x81, 0x2b, 0xb9, 0x81, 0xbd, 0x37, 0x64, 0x7f,
	0x24, 0xd1, 0x21, 0x6f, 0xc2, 0xa3, 0xd2, 0xb0, 0xd4, 0x85, 0x99, 0xe6, 0xac, 0x20, 0xec, 0x77,
	0x33, 0xce, 0xdb, 0x8e, 0xdb, 0x31, 0x1a, 0xe4, 0xf0, 0xe9, 0x97, 0x96, 0x52, 0xc1, 0x13, 0x9f,
	0xa3, 0x9b, 0x4a, 0xbd, 0x67, 0xe0, 0x1e, 0x71, 0x4d, 0x6f, 0x3f, 0x27, 0xdd, 0x6c, 0x71, 0xd0,
	0xf0, 0x7e, 0x08, 0x8f, 0x66, 0x26, 0x98, 0x16, 0x6c, 0x20, 0x4a, 0x0f, 0x4d, 0x4d, 0x7f, 0x75,
	0x20, 0xcc, 0x1b, 0xd2, 0xd7, 0xd0, 0x3a, 0xa0, 0xf6, 0x9a, 0xda, 0xc5, 0x05, 0x37, 0xc7, 0x16,
	0x81, 0x95, 0x71, 0x11, 0xf8, 0x2e, 0xd4, 0x86, 0x38, 0x91, 0xda, 0x75, 0x93, 0xe5, 0xaa, 0x04,
	0xfc, 0x92, 0x8f, 0xbc, 0xaf, 0x60, 0x6d, 0x42, 0xb6, 0x4a, 0xdd, 0x9f, 0x43, 0xd3, 0xf6, 0xf7,
	0xe6, 0xd5, 0xfa, 0xce, 0x77, 0x2f, 0x33, 0xd9, 0x3c, 0x61, 0xfb, 0x8d, 0xb0, 0x34, 0xf2, 0x24,
	0x34, 0xca, 0xd4, 0x49, 0x3d, 0xec, 0xff, 0x15, 0x72, 0x3d, 0x16, 0x3d, 0x2a, 0x3c, 0x81, 0xa6,
	0xce, 0x58, 0xa2, 0x42, 0x19, 0x71, 0x4c, 0xd7, 0x36, 0xc6, 0x34, 0x0a, 0xf0, 0x55, 0x16, 0x7b,
	0x5f, 0x50, 0x20, 0x3f, 0xe5, 0x3c, 0x3a, 0x90, 0xc9, 0x95, 0xc8, 0xfa, 0x6f, 0x8a, 0x3b, 0x82,
	0x46, 0xfb, 0xdf, 0x94, 0xc7, 0xde, 0x3e, 0x05, 0xee, 0x5b, 0x64, 0xa9, 0xd4, 0xfd, 0x00, 0x1a,
	0x09, 0xe7, 0x51, 0x10, 0x1a, 0x2a, 0x49, 0xad, 0xfa, 0xf5, 0x64, 0x3c, 0xc1, 0xfb, 0x31, 0x3c,
	0x3a, 0x91, 0x91, 0xb8, 0x1a, 0x4d, 0xaa, 0xb1, 0xf0, 0x3a, 0x6f, 0xc0, 0xfa, 0xec, 0x3c, 0x95,
	0x7a, 0xbf, 0x5b, 0x02, 0x77, 0x9f, 0xea, 0xdd, 0x97, 0x7c, 0xc8, 0xe3, 0x53, 0x7e, 0x73, 0xc1,
	0x54, 0x0f, 0xe5, 0x65, 0xbc, 0xcf, 0xf2, 0x1f, 0x41, 0xad, 0x3c, 0x03, 0x51, 0x09, 0xfd, 0x23,
	0x78, 0x4c, 0xaa, 0xd2, 0x7f, 0x17, 0xca, 0xbf, 0x81, 0x9b, 0x2d, 0xbb, 0x48, 0x7c, 0xc3, 0x79,
	0x6f, 0x7f, 0xfc, 0x5b, 0xf8, 0x67, 0xb0, 0x69, 0x65, 0xce, 0x4c, 0x32, 0x81, 0x76, 0xdd, 0x90,
	0x67, 0xa7, 0x8d, 0x57, 0x4a, 0x63, 0xa6, 0xaf, 0x64, 0xd6, 0x37, 0xd3, 0x4c, 0x00, 0x5e, 0xcf,
	0xd7, 0x3a, 0xb3, 0x44, 0x9a, 0xf6, 0xa7, 0xb0, 0x55, 0x5e, 0x6d, 0x72, 0xa2, 0xf9, 0x27, 0xc1,
	0xc6, 0x78, 0xbd, 0x89, 0xa9, 0x4f, 0x61, 0x8d, 0xe6, 0x74, 0xc5, 0x95, 0x0e, 0x86, 0x2c, 0x1e,
	0xf0, 0xfc, 0xb7, 0x40, 0x84, 0x8f, 0xc4, 0x95, 0x7e, 0x8d, 0xa0, 0xfb, 0x7d, 0xb0, 0xad, 0x42,
	0x10, 0x62, 0x9b, 0x1e, 0xc4, 0x68, 0x41, 0xfb, 0xe8, 0xde, 0x36, 0x14, 0xea, 0xdf, 0xc9, 0xb2,
	0xe8, 0x17, 0x24, 0x15, 0xfb, 0x2f, 0xdb, 0x8f, 0xe0, 0xf8, 0x74, 0xd0, 0x2f, 0xfe, 0x74, 0x43,
	0x67, 0x67, 0x7f, 0xf2, 0x43, 0x00, 0x4f, 0xe9, 0xc5, 0x9f, 0x7c, 0xfd, 0x59, 0x57, 0xc6, 0x2c,
	0xe9, 0xee, 0x7d, 0xf6, 0x5c, 0xeb, 0xbd, 0x50, 0xf6, 0x9f, 0xd1, 0x5f, 0x96, 0x42, 0x19, 0x3f,
	0x53, 0x3c, 0x1b, 0x8a, 0x90, 0xab, 0x79, 0x7f, 0x6c, 0xba, 0x5c, 0x21, 0xb6, 0x4f, 0xff, 0x27,
	0x00, 0x00, 0xff, 0xff, 0xed, 0x1d, 0xce, 0xea, 0x19, 0x25, 0x00, 0x00,
}
