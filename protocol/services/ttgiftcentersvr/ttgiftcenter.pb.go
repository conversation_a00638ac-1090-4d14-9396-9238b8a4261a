// Code generated by protoc-gen-gogo.
// source: src/ttgiftcentersvr/ttgiftcenter.proto
// DO NOT EDIT!

/*
	Package TTGiftCenter is a generated protocol buffer package.

	It is generated from these files:
		src/ttgiftcentersvr/ttgiftcenter.proto

	It has these top-level messages:
		ProductItem
		CreateProductReq
		CreateProductResp
		ModifyProductReq
		Product
		SearchProductReq
		SearchProductResp
		BatchGetProductReq
		BatchGetProductResp
		DispatchItemReq
		AccountPwdItem
		ExchangeCardItem
		AddItemReq
		AddItemResp
		UpdateSubTTGiftReq
		UpdateSubGuildGiftReq
		AddSubYorActGiftReq
		GetSubGiftReq
		SubTTGift
		GetSubTTGiftResp
		SubGuildGift
		GetSubGuildGiftResp
		ActivitGift
		GetSubActivityGiftResp
		YGift
		GetSubYGiftResp
		OperateGift
		GetSubOperateGiftResp
		ModifyGiftRankReq
		DispatchSubTTGiftReq
		DispatchSubGiftResp
		DispatchSubGuildGiftReq
		GetGameGiftAmountReq
		GameGiftAmount
		GetGameGiftAmountResp
		GetTTGiftPurchaseAuthReq
		TTGiftPurchaseAuth
		GetTTGiftPurchaseAuthResp
		GetGuildGiftPurchaseAuthReq
		GetGuildGiftPurchaseAuthResp
		ExportSubGiftReq
		ExportSubGiftResp
		ClaimGiftReq
		ClaimGiftResp
		GetGiftDispatchRecordReq
		TTGiftDispatchInfo
		GetTTGiftDispatchRecordResp
		GuildGiftDispatchInfo
		GetGuildGiftDispatchRecordResp
		RecycleGiftReq
		AddGiftActivityReq
		AddGiftActivityResp
		DelGiftActivityReq
		ModifyGiftActivityRankReq
		GiftActivity
		GetGiftActivityReq
		GetGiftActivityResp
		ActivityBanner
		GetActivityBannerResp
		PurchaseTTGiftReq
		PurchaseTTGiftResp
		PurchaseGuildGiftReq
		PurchaseGuildGiftResp
		PickTTGiftReq
		GetTTGiftDayLimitReq
		GetTTGiftDayLimitResp
*/
package TTGiftCenter

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ActivityType int32

const (
	ActivityType_GIFT_BANNER   ActivityType = 1
	ActivityType_GIFT_ACTIVITY ActivityType = 2
)

var ActivityType_name = map[int32]string{
	1: "GIFT_BANNER",
	2: "GIFT_ACTIVITY",
}
var ActivityType_value = map[string]int32{
	"GIFT_BANNER":   1,
	"GIFT_ACTIVITY": 2,
}

func (x ActivityType) Enum() *ActivityType {
	p := new(ActivityType)
	*p = x
	return p
}
func (x ActivityType) String() string {
	return proto.EnumName(ActivityType_name, int32(x))
}
func (x *ActivityType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ActivityType_value, data, "ActivityType")
	if err != nil {
		return err
	}
	*x = ActivityType(value)
	return nil
}
func (ActivityType) EnumDescriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{0} }

type SubType int32

const (
	SubType_TT_GIFT       SubType = 1
	SubType_GUILD_GIFT    SubType = 2
	SubType_ACTIVITY_GIFT SubType = 3
	SubType_Y_GIFT        SubType = 4
	SubType_OPERATE_GIFT  SubType = 5
)

var SubType_name = map[int32]string{
	1: "TT_GIFT",
	2: "GUILD_GIFT",
	3: "ACTIVITY_GIFT",
	4: "Y_GIFT",
	5: "OPERATE_GIFT",
}
var SubType_value = map[string]int32{
	"TT_GIFT":       1,
	"GUILD_GIFT":    2,
	"ACTIVITY_GIFT": 3,
	"Y_GIFT":        4,
	"OPERATE_GIFT":  5,
}

func (x SubType) Enum() *SubType {
	p := new(SubType)
	*p = x
	return p
}
func (x SubType) String() string {
	return proto.EnumName(SubType_name, int32(x))
}
func (x *SubType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SubType_value, data, "SubType")
	if err != nil {
		return err
	}
	*x = SubType(value)
	return nil
}
func (SubType) EnumDescriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{1} }

type OrderByType int32

const (
	OrderByType_PRODUCT_ID OrderByType = 1
	OrderByType_RANK       OrderByType = 2
	OrderByType_STOCK      OrderByType = 3
	OrderByType_REMAIN     OrderByType = 4
	OrderByType_NAME       OrderByType = 5
)

var OrderByType_name = map[int32]string{
	1: "PRODUCT_ID",
	2: "RANK",
	3: "STOCK",
	4: "REMAIN",
	5: "NAME",
}
var OrderByType_value = map[string]int32{
	"PRODUCT_ID": 1,
	"RANK":       2,
	"STOCK":      3,
	"REMAIN":     4,
	"NAME":       5,
}

func (x OrderByType) Enum() *OrderByType {
	p := new(OrderByType)
	*p = x
	return p
}
func (x OrderByType) String() string {
	return proto.EnumName(OrderByType_name, int32(x))
}
func (x *OrderByType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(OrderByType_value, data, "OrderByType")
	if err != nil {
		return err
	}
	*x = OrderByType(value)
	return nil
}
func (OrderByType) EnumDescriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{2} }

type TTGiftPlatform int32

const (
	TTGiftPlatform_ALL_PLATFORM TTGiftPlatform = 255
	TTGiftPlatform_ANDROID      TTGiftPlatform = 1
	TTGiftPlatform_IOS          TTGiftPlatform = 2
)

var TTGiftPlatform_name = map[int32]string{
	255: "ALL_PLATFORM",
	1:   "ANDROID",
	2:   "IOS",
}
var TTGiftPlatform_value = map[string]int32{
	"ALL_PLATFORM": 255,
	"ANDROID":      1,
	"IOS":          2,
}

func (x TTGiftPlatform) Enum() *TTGiftPlatform {
	p := new(TTGiftPlatform)
	*p = x
	return p
}
func (x TTGiftPlatform) String() string {
	return proto.EnumName(TTGiftPlatform_name, int32(x))
}
func (x *TTGiftPlatform) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(TTGiftPlatform_value, data, "TTGiftPlatform")
	if err != nil {
		return err
	}
	*x = TTGiftPlatform(value)
	return nil
}
func (TTGiftPlatform) EnumDescriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{3} }

// TT子库商品的条件位
type ESubTTGiftPurchaseCond int32

const (
	ESubTTGiftPurchaseCond_SUB_TT_PURCHASE_COND_SDK_ALLOW ESubTTGiftPurchaseCond = 1
)

var ESubTTGiftPurchaseCond_name = map[int32]string{
	1: "SUB_TT_PURCHASE_COND_SDK_ALLOW",
}
var ESubTTGiftPurchaseCond_value = map[string]int32{
	"SUB_TT_PURCHASE_COND_SDK_ALLOW": 1,
}

func (x ESubTTGiftPurchaseCond) Enum() *ESubTTGiftPurchaseCond {
	p := new(ESubTTGiftPurchaseCond)
	*p = x
	return p
}
func (x ESubTTGiftPurchaseCond) String() string {
	return proto.EnumName(ESubTTGiftPurchaseCond_name, int32(x))
}
func (x *ESubTTGiftPurchaseCond) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ESubTTGiftPurchaseCond_value, data, "ESubTTGiftPurchaseCond")
	if err != nil {
		return err
	}
	*x = ESubTTGiftPurchaseCond(value)
	return nil
}
func (ESubTTGiftPurchaseCond) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{4}
}

type ProductItem struct {
	StorageId  uint32 `protobuf:"varint,1,req,name=storage_id,json=storageId" json:"storage_id"`
	ItemBinary []byte `protobuf:"bytes,2,req,name=item_binary,json=itemBinary" json:"item_binary"`
	Uid        uint32 `protobuf:"varint,3,opt,name=uid" json:"uid"`
}

func (m *ProductItem) Reset()                    { *m = ProductItem{} }
func (m *ProductItem) String() string            { return proto.CompactTextString(m) }
func (*ProductItem) ProtoMessage()               {}
func (*ProductItem) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{0} }

func (m *ProductItem) GetStorageId() uint32 {
	if m != nil {
		return m.StorageId
	}
	return 0
}

func (m *ProductItem) GetItemBinary() []byte {
	if m != nil {
		return m.ItemBinary
	}
	return nil
}

func (m *ProductItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CreateProductReq struct {
	Name          string `protobuf:"bytes,1,req,name=name" json:"name"`
	Description   string `protobuf:"bytes,2,req,name=description" json:"description"`
	UsageDesc     string `protobuf:"bytes,3,req,name=usage_desc,json=usageDesc" json:"usage_desc"`
	GameId        uint32 `protobuf:"varint,4,req,name=game_id,json=gameId" json:"game_id"`
	ItemType      uint32 `protobuf:"varint,5,req,name=item_type,json=itemType" json:"item_type"`
	GiftType      uint32 `protobuf:"varint,6,req,name=gift_type,json=giftType" json:"gift_type"`
	ExchangeSDate uint32 `protobuf:"varint,7,req,name=exchange_s_date,json=exchangeSDate" json:"exchange_s_date"`
	ExchangeEDate uint32 `protobuf:"varint,8,req,name=exchange_e_date,json=exchangeEDate" json:"exchange_e_date"`
	Operator      string `protobuf:"bytes,9,req,name=operator" json:"operator"`
	Platform      uint32 `protobuf:"varint,10,req,name=platform" json:"platform"`
}

func (m *CreateProductReq) Reset()                    { *m = CreateProductReq{} }
func (m *CreateProductReq) String() string            { return proto.CompactTextString(m) }
func (*CreateProductReq) ProtoMessage()               {}
func (*CreateProductReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{1} }

func (m *CreateProductReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateProductReq) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *CreateProductReq) GetUsageDesc() string {
	if m != nil {
		return m.UsageDesc
	}
	return ""
}

func (m *CreateProductReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CreateProductReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *CreateProductReq) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *CreateProductReq) GetExchangeSDate() uint32 {
	if m != nil {
		return m.ExchangeSDate
	}
	return 0
}

func (m *CreateProductReq) GetExchangeEDate() uint32 {
	if m != nil {
		return m.ExchangeEDate
	}
	return 0
}

func (m *CreateProductReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *CreateProductReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

type CreateProductResp struct {
	ProductId uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
}

func (m *CreateProductResp) Reset()                    { *m = CreateProductResp{} }
func (m *CreateProductResp) String() string            { return proto.CompactTextString(m) }
func (*CreateProductResp) ProtoMessage()               {}
func (*CreateProductResp) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{2} }

func (m *CreateProductResp) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type ModifyProductReq struct {
	ProductId     uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Name          string `protobuf:"bytes,2,req,name=name" json:"name"`
	Description   string `protobuf:"bytes,3,req,name=description" json:"description"`
	UsageDesc     string `protobuf:"bytes,4,req,name=usage_desc,json=usageDesc" json:"usage_desc"`
	GiftType      uint32 `protobuf:"varint,5,req,name=gift_type,json=giftType" json:"gift_type"`
	ExchangeSDate uint32 `protobuf:"varint,6,req,name=exchange_s_date,json=exchangeSDate" json:"exchange_s_date"`
	ExchangeEDate uint32 `protobuf:"varint,7,req,name=exchange_e_date,json=exchangeEDate" json:"exchange_e_date"`
	Operator      string `protobuf:"bytes,8,req,name=operator" json:"operator"`
	Platform      uint32 `protobuf:"varint,9,req,name=platform" json:"platform"`
}

func (m *ModifyProductReq) Reset()                    { *m = ModifyProductReq{} }
func (m *ModifyProductReq) String() string            { return proto.CompactTextString(m) }
func (*ModifyProductReq) ProtoMessage()               {}
func (*ModifyProductReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{3} }

func (m *ModifyProductReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ModifyProductReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ModifyProductReq) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *ModifyProductReq) GetUsageDesc() string {
	if m != nil {
		return m.UsageDesc
	}
	return ""
}

func (m *ModifyProductReq) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *ModifyProductReq) GetExchangeSDate() uint32 {
	if m != nil {
		return m.ExchangeSDate
	}
	return 0
}

func (m *ModifyProductReq) GetExchangeEDate() uint32 {
	if m != nil {
		return m.ExchangeEDate
	}
	return 0
}

func (m *ModifyProductReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *ModifyProductReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

// 商品
type Product struct {
	ProductId     uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Name          string `protobuf:"bytes,2,req,name=name" json:"name"`
	Description   string `protobuf:"bytes,3,req,name=description" json:"description"`
	UsageDesc     string `protobuf:"bytes,4,req,name=usage_desc,json=usageDesc" json:"usage_desc"`
	GameId        uint32 `protobuf:"varint,5,req,name=game_id,json=gameId" json:"game_id"`
	ItemType      uint32 `protobuf:"varint,6,req,name=item_type,json=itemType" json:"item_type"`
	GiftType      uint32 `protobuf:"varint,7,req,name=gift_type,json=giftType" json:"gift_type"`
	ExchangeSDate uint32 `protobuf:"varint,8,req,name=exchange_s_date,json=exchangeSDate" json:"exchange_s_date"`
	ExchangeEDate uint32 `protobuf:"varint,9,req,name=exchange_e_date,json=exchangeEDate" json:"exchange_e_date"`
	Left          uint32 `protobuf:"varint,10,req,name=left" json:"left"`
	Stock         uint32 `protobuf:"varint,11,req,name=stock" json:"stock"`
	CreateTime    uint32 `protobuf:"varint,12,req,name=create_time,json=createTime" json:"create_time"`
	Platform      uint32 `protobuf:"varint,13,req,name=platform" json:"platform"`
}

func (m *Product) Reset()                    { *m = Product{} }
func (m *Product) String() string            { return proto.CompactTextString(m) }
func (*Product) ProtoMessage()               {}
func (*Product) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{4} }

func (m *Product) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *Product) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Product) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *Product) GetUsageDesc() string {
	if m != nil {
		return m.UsageDesc
	}
	return ""
}

func (m *Product) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *Product) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *Product) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *Product) GetExchangeSDate() uint32 {
	if m != nil {
		return m.ExchangeSDate
	}
	return 0
}

func (m *Product) GetExchangeEDate() uint32 {
	if m != nil {
		return m.ExchangeEDate
	}
	return 0
}

func (m *Product) GetLeft() uint32 {
	if m != nil {
		return m.Left
	}
	return 0
}

func (m *Product) GetStock() uint32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *Product) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *Product) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

type SearchProductReq struct {
	ProductId        uint64 `protobuf:"varint,1,opt,name=product_id,json=productId" json:"product_id"`
	GameId           uint32 `protobuf:"varint,2,opt,name=game_id,json=gameId" json:"game_id"`
	GiftType         uint32 `protobuf:"varint,3,opt,name=gift_type,json=giftType" json:"gift_type"`
	Name             string `protobuf:"bytes,4,opt,name=name" json:"name"`
	CreateAfterTime  uint32 `protobuf:"varint,5,opt,name=create_after_time,json=createAfterTime" json:"create_after_time"`
	CreateBeforeTime uint32 `protobuf:"varint,6,opt,name=create_before_time,json=createBeforeTime" json:"create_before_time"`
	OrderByType      uint32 `protobuf:"varint,7,opt,name=order_by_type,json=orderByType" json:"order_by_type"`
	Desc             bool   `protobuf:"varint,8,opt,name=desc" json:"desc"`
	LimitStartIdx    uint32 `protobuf:"varint,9,req,name=limit_start_idx,json=limitStartIdx" json:"limit_start_idx"`
	LimitCount       uint32 `protobuf:"varint,10,req,name=limit_count,json=limitCount" json:"limit_count"`
	Platform         uint32 `protobuf:"varint,11,req,name=platform" json:"platform"`
}

func (m *SearchProductReq) Reset()                    { *m = SearchProductReq{} }
func (m *SearchProductReq) String() string            { return proto.CompactTextString(m) }
func (*SearchProductReq) ProtoMessage()               {}
func (*SearchProductReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{5} }

func (m *SearchProductReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *SearchProductReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SearchProductReq) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *SearchProductReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SearchProductReq) GetCreateAfterTime() uint32 {
	if m != nil {
		return m.CreateAfterTime
	}
	return 0
}

func (m *SearchProductReq) GetCreateBeforeTime() uint32 {
	if m != nil {
		return m.CreateBeforeTime
	}
	return 0
}

func (m *SearchProductReq) GetOrderByType() uint32 {
	if m != nil {
		return m.OrderByType
	}
	return 0
}

func (m *SearchProductReq) GetDesc() bool {
	if m != nil {
		return m.Desc
	}
	return false
}

func (m *SearchProductReq) GetLimitStartIdx() uint32 {
	if m != nil {
		return m.LimitStartIdx
	}
	return 0
}

func (m *SearchProductReq) GetLimitCount() uint32 {
	if m != nil {
		return m.LimitCount
	}
	return 0
}

func (m *SearchProductReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

type SearchProductResp struct {
	ProductList []*Product `protobuf:"bytes,1,rep,name=product_list,json=productList" json:"product_list,omitempty"`
	Total       uint32     `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *SearchProductResp) Reset()                    { *m = SearchProductResp{} }
func (m *SearchProductResp) String() string            { return proto.CompactTextString(m) }
func (*SearchProductResp) ProtoMessage()               {}
func (*SearchProductResp) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{6} }

func (m *SearchProductResp) GetProductList() []*Product {
	if m != nil {
		return m.ProductList
	}
	return nil
}

func (m *SearchProductResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 根据product id批量获取主库商品信息
type BatchGetProductReq struct {
	ProductidList []uint64 `protobuf:"varint,1,rep,name=productid_list,json=productidList" json:"productid_list,omitempty"`
}

func (m *BatchGetProductReq) Reset()                    { *m = BatchGetProductReq{} }
func (m *BatchGetProductReq) String() string            { return proto.CompactTextString(m) }
func (*BatchGetProductReq) ProtoMessage()               {}
func (*BatchGetProductReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{7} }

func (m *BatchGetProductReq) GetProductidList() []uint64 {
	if m != nil {
		return m.ProductidList
	}
	return nil
}

type BatchGetProductResp struct {
	ProductList []*Product `protobuf:"bytes,1,rep,name=product_list,json=productList" json:"product_list,omitempty"`
}

func (m *BatchGetProductResp) Reset()                    { *m = BatchGetProductResp{} }
func (m *BatchGetProductResp) String() string            { return proto.CompactTextString(m) }
func (*BatchGetProductResp) ProtoMessage()               {}
func (*BatchGetProductResp) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{8} }

func (m *BatchGetProductResp) GetProductList() []*Product {
	if m != nil {
		return m.ProductList
	}
	return nil
}

// 将主库商品的物品分配到子库
type DispatchItemReq struct {
	ProductId uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	SubType   uint32 `protobuf:"varint,2,req,name=sub_type,json=subType" json:"sub_type"`
	Amount    int32  `protobuf:"varint,3,req,name=amount" json:"amount"`
	Operator  string `protobuf:"bytes,4,req,name=operator" json:"operator"`
}

func (m *DispatchItemReq) Reset()                    { *m = DispatchItemReq{} }
func (m *DispatchItemReq) String() string            { return proto.CompactTextString(m) }
func (*DispatchItemReq) ProtoMessage()               {}
func (*DispatchItemReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{9} }

func (m *DispatchItemReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *DispatchItemReq) GetSubType() uint32 {
	if m != nil {
		return m.SubType
	}
	return 0
}

func (m *DispatchItemReq) GetAmount() int32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *DispatchItemReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type AccountPwdItem struct {
	Account string `protobuf:"bytes,1,req,name=account" json:"account"`
	Pwd     string `protobuf:"bytes,2,req,name=pwd" json:"pwd"`
}

func (m *AccountPwdItem) Reset()                    { *m = AccountPwdItem{} }
func (m *AccountPwdItem) String() string            { return proto.CompactTextString(m) }
func (*AccountPwdItem) ProtoMessage()               {}
func (*AccountPwdItem) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{10} }

func (m *AccountPwdItem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AccountPwdItem) GetPwd() string {
	if m != nil {
		return m.Pwd
	}
	return ""
}

type ExchangeCardItem struct {
	Code string `protobuf:"bytes,1,req,name=code" json:"code"`
}

func (m *ExchangeCardItem) Reset()                    { *m = ExchangeCardItem{} }
func (m *ExchangeCardItem) String() string            { return proto.CompactTextString(m) }
func (*ExchangeCardItem) ProtoMessage()               {}
func (*ExchangeCardItem) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{11} }

func (m *ExchangeCardItem) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

type AddItemReq struct {
	ProductId      uint64   `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	ItemBinaryList [][]byte `protobuf:"bytes,2,rep,name=item_binary_list,json=itemBinaryList" json:"item_binary_list,omitempty"`
	Operator       string   `protobuf:"bytes,3,req,name=operator" json:"operator"`
	ItemHashList   []string `protobuf:"bytes,4,rep,name=item_hash_list,json=itemHashList" json:"item_hash_list,omitempty"`
}

func (m *AddItemReq) Reset()                    { *m = AddItemReq{} }
func (m *AddItemReq) String() string            { return proto.CompactTextString(m) }
func (*AddItemReq) ProtoMessage()               {}
func (*AddItemReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{12} }

func (m *AddItemReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *AddItemReq) GetItemBinaryList() [][]byte {
	if m != nil {
		return m.ItemBinaryList
	}
	return nil
}

func (m *AddItemReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *AddItemReq) GetItemHashList() []string {
	if m != nil {
		return m.ItemHashList
	}
	return nil
}

type AddItemResp struct {
	SuccessItems [][]byte `protobuf:"bytes,1,rep,name=success_items,json=successItems" json:"success_items,omitempty"`
	FailedItems  [][]byte `protobuf:"bytes,2,rep,name=failed_items,json=failedItems" json:"failed_items,omitempty"`
}

func (m *AddItemResp) Reset()                    { *m = AddItemResp{} }
func (m *AddItemResp) String() string            { return proto.CompactTextString(m) }
func (*AddItemResp) ProtoMessage()               {}
func (*AddItemResp) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{13} }

func (m *AddItemResp) GetSuccessItems() [][]byte {
	if m != nil {
		return m.SuccessItems
	}
	return nil
}

func (m *AddItemResp) GetFailedItems() [][]byte {
	if m != nil {
		return m.FailedItems
	}
	return nil
}

type UpdateSubTTGiftReq struct {
	ProductId         uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Price             uint32 `protobuf:"varint,2,req,name=price" json:"price"`
	Status            uint32 `protobuf:"varint,3,req,name=status" json:"status"`
	LevelMin          uint32 `protobuf:"varint,4,req,name=level_min,json=levelMin" json:"level_min"`
	LevelMax          uint32 `protobuf:"varint,5,req,name=level_max,json=levelMax" json:"level_max"`
	PurchaseLimit     uint32 `protobuf:"varint,6,req,name=purchase_limit,json=purchaseLimit" json:"purchase_limit"`
	PurchaseCondition uint64 `protobuf:"varint,7,req,name=purchase_condition,json=purchaseCondition" json:"purchase_condition"`
	DayLimit          uint32 `protobuf:"varint,8,req,name=day_limit,json=dayLimit" json:"day_limit"`
	HourLimit         uint32 `protobuf:"varint,9,req,name=hour_limit,json=hourLimit" json:"hour_limit"`
	BuySTime          uint32 `protobuf:"varint,10,req,name=buy_s_time,json=buySTime" json:"buy_s_time"`
	BuyETime          uint32 `protobuf:"varint,11,req,name=buy_e_time,json=buyETime" json:"buy_e_time"`
	PickAble          uint32 `protobuf:"varint,12,req,name=pick_able,json=pickAble" json:"pick_able"`
	Operator          string `protobuf:"bytes,13,req,name=operator" json:"operator"`
	PickSTime         uint32 `protobuf:"varint,14,opt,name=pick_s_time,json=pickSTime" json:"pick_s_time"`
	PickETime         uint32 `protobuf:"varint,15,opt,name=pick_e_time,json=pickETime" json:"pick_e_time"`
	RegisterIn        uint32 `protobuf:"varint,16,opt,name=register_in,json=registerIn" json:"register_in"`
}

func (m *UpdateSubTTGiftReq) Reset()                    { *m = UpdateSubTTGiftReq{} }
func (m *UpdateSubTTGiftReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateSubTTGiftReq) ProtoMessage()               {}
func (*UpdateSubTTGiftReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{14} }

func (m *UpdateSubTTGiftReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *UpdateSubTTGiftReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *UpdateSubTTGiftReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UpdateSubTTGiftReq) GetLevelMin() uint32 {
	if m != nil {
		return m.LevelMin
	}
	return 0
}

func (m *UpdateSubTTGiftReq) GetLevelMax() uint32 {
	if m != nil {
		return m.LevelMax
	}
	return 0
}

func (m *UpdateSubTTGiftReq) GetPurchaseLimit() uint32 {
	if m != nil {
		return m.PurchaseLimit
	}
	return 0
}

func (m *UpdateSubTTGiftReq) GetPurchaseCondition() uint64 {
	if m != nil {
		return m.PurchaseCondition
	}
	return 0
}

func (m *UpdateSubTTGiftReq) GetDayLimit() uint32 {
	if m != nil {
		return m.DayLimit
	}
	return 0
}

func (m *UpdateSubTTGiftReq) GetHourLimit() uint32 {
	if m != nil {
		return m.HourLimit
	}
	return 0
}

func (m *UpdateSubTTGiftReq) GetBuySTime() uint32 {
	if m != nil {
		return m.BuySTime
	}
	return 0
}

func (m *UpdateSubTTGiftReq) GetBuyETime() uint32 {
	if m != nil {
		return m.BuyETime
	}
	return 0
}

func (m *UpdateSubTTGiftReq) GetPickAble() uint32 {
	if m != nil {
		return m.PickAble
	}
	return 0
}

func (m *UpdateSubTTGiftReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *UpdateSubTTGiftReq) GetPickSTime() uint32 {
	if m != nil {
		return m.PickSTime
	}
	return 0
}

func (m *UpdateSubTTGiftReq) GetPickETime() uint32 {
	if m != nil {
		return m.PickETime
	}
	return 0
}

func (m *UpdateSubTTGiftReq) GetRegisterIn() uint32 {
	if m != nil {
		return m.RegisterIn
	}
	return 0
}

type UpdateSubGuildGiftReq struct {
	ProductId     uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Price         uint32 `protobuf:"varint,2,req,name=price" json:"price"`
	Status        uint32 `protobuf:"varint,3,req,name=status" json:"status"`
	LevelMin      uint32 `protobuf:"varint,4,req,name=level_min,json=levelMin" json:"level_min"`
	LevelMax      uint32 `protobuf:"varint,5,req,name=level_max,json=levelMax" json:"level_max"`
	PurchaseLimit uint32 `protobuf:"varint,6,req,name=purchase_limit,json=purchaseLimit" json:"purchase_limit"`
	BuySTime      uint32 `protobuf:"varint,7,req,name=buy_s_time,json=buySTime" json:"buy_s_time"`
	BuyETime      uint32 `protobuf:"varint,8,req,name=buy_e_time,json=buyETime" json:"buy_e_time"`
	Operator      string `protobuf:"bytes,9,req,name=operator" json:"operator"`
}

func (m *UpdateSubGuildGiftReq) Reset()         { *m = UpdateSubGuildGiftReq{} }
func (m *UpdateSubGuildGiftReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSubGuildGiftReq) ProtoMessage()    {}
func (*UpdateSubGuildGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{15}
}

func (m *UpdateSubGuildGiftReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *UpdateSubGuildGiftReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *UpdateSubGuildGiftReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UpdateSubGuildGiftReq) GetLevelMin() uint32 {
	if m != nil {
		return m.LevelMin
	}
	return 0
}

func (m *UpdateSubGuildGiftReq) GetLevelMax() uint32 {
	if m != nil {
		return m.LevelMax
	}
	return 0
}

func (m *UpdateSubGuildGiftReq) GetPurchaseLimit() uint32 {
	if m != nil {
		return m.PurchaseLimit
	}
	return 0
}

func (m *UpdateSubGuildGiftReq) GetBuySTime() uint32 {
	if m != nil {
		return m.BuySTime
	}
	return 0
}

func (m *UpdateSubGuildGiftReq) GetBuyETime() uint32 {
	if m != nil {
		return m.BuyETime
	}
	return 0
}

func (m *UpdateSubGuildGiftReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type AddSubYorActGiftReq struct {
	ProductId uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Status    uint32 `protobuf:"varint,2,req,name=status" json:"status"`
	Operator  string `protobuf:"bytes,3,req,name=operator" json:"operator"`
	SubType   uint32 `protobuf:"varint,4,req,name=sub_type,json=subType" json:"sub_type"`
}

func (m *AddSubYorActGiftReq) Reset()                    { *m = AddSubYorActGiftReq{} }
func (m *AddSubYorActGiftReq) String() string            { return proto.CompactTextString(m) }
func (*AddSubYorActGiftReq) ProtoMessage()               {}
func (*AddSubYorActGiftReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{16} }

func (m *AddSubYorActGiftReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *AddSubYorActGiftReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *AddSubYorActGiftReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *AddSubYorActGiftReq) GetSubType() uint32 {
	if m != nil {
		return m.SubType
	}
	return 0
}

type GetSubGiftReq struct {
	ProductId        uint64 `protobuf:"varint,1,opt,name=product_id,json=productId" json:"product_id"`
	GameId           uint32 `protobuf:"varint,2,opt,name=game_id,json=gameId" json:"game_id"`
	Name             string `protobuf:"bytes,3,opt,name=name" json:"name"`
	CreateAfterTime  uint64 `protobuf:"varint,4,opt,name=create_after_time,json=createAfterTime" json:"create_after_time"`
	CreateBeforeTime uint64 `protobuf:"varint,5,opt,name=create_before_time,json=createBeforeTime" json:"create_before_time"`
	Status           uint32 `protobuf:"varint,6,opt,name=status" json:"status"`
	OrderByType      uint32 `protobuf:"varint,7,opt,name=order_by_type,json=orderByType" json:"order_by_type"`
	Desc             bool   `protobuf:"varint,8,opt,name=desc" json:"desc"`
	LimitStart       uint32 `protobuf:"varint,9,req,name=limit_start,json=limitStart" json:"limit_start"`
	LimitEnd         uint32 `protobuf:"varint,10,req,name=limit_end,json=limitEnd" json:"limit_end"`
	GiftType         uint32 `protobuf:"varint,11,opt,name=gift_type,json=giftType" json:"gift_type"`
	IsNeedExpire     bool   `protobuf:"varint,12,opt,name=is_need_expire,json=isNeedExpire" json:"is_need_expire"`
	Platform         uint32 `protobuf:"varint,13,req,name=platform" json:"platform"`
}

func (m *GetSubGiftReq) Reset()                    { *m = GetSubGiftReq{} }
func (m *GetSubGiftReq) String() string            { return proto.CompactTextString(m) }
func (*GetSubGiftReq) ProtoMessage()               {}
func (*GetSubGiftReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{17} }

func (m *GetSubGiftReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetSubGiftReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetSubGiftReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetSubGiftReq) GetCreateAfterTime() uint64 {
	if m != nil {
		return m.CreateAfterTime
	}
	return 0
}

func (m *GetSubGiftReq) GetCreateBeforeTime() uint64 {
	if m != nil {
		return m.CreateBeforeTime
	}
	return 0
}

func (m *GetSubGiftReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetSubGiftReq) GetOrderByType() uint32 {
	if m != nil {
		return m.OrderByType
	}
	return 0
}

func (m *GetSubGiftReq) GetDesc() bool {
	if m != nil {
		return m.Desc
	}
	return false
}

func (m *GetSubGiftReq) GetLimitStart() uint32 {
	if m != nil {
		return m.LimitStart
	}
	return 0
}

func (m *GetSubGiftReq) GetLimitEnd() uint32 {
	if m != nil {
		return m.LimitEnd
	}
	return 0
}

func (m *GetSubGiftReq) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *GetSubGiftReq) GetIsNeedExpire() bool {
	if m != nil {
		return m.IsNeedExpire
	}
	return false
}

func (m *GetSubGiftReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

type SubTTGift struct {
	ProductId         uint64   `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Price             uint32   `protobuf:"varint,2,req,name=price" json:"price"`
	Status            uint32   `protobuf:"varint,3,req,name=status" json:"status"`
	Rank              uint32   `protobuf:"varint,4,req,name=rank" json:"rank"`
	LevelMin          uint32   `protobuf:"varint,5,req,name=level_min,json=levelMin" json:"level_min"`
	LevelMax          uint32   `protobuf:"varint,6,req,name=level_max,json=levelMax" json:"level_max"`
	PurchaseLimit     uint32   `protobuf:"varint,7,req,name=purchase_limit,json=purchaseLimit" json:"purchase_limit"`
	PurchaseCondition uint64   `protobuf:"varint,8,req,name=purchase_condition,json=purchaseCondition" json:"purchase_condition"`
	DayLimit          uint32   `protobuf:"varint,9,req,name=day_limit,json=dayLimit" json:"day_limit"`
	HourLimit         uint32   `protobuf:"varint,10,req,name=hour_limit,json=hourLimit" json:"hour_limit"`
	BuySTime          uint32   `protobuf:"varint,11,req,name=buy_s_time,json=buySTime" json:"buy_s_time"`
	BuyETime          uint32   `protobuf:"varint,12,req,name=buy_e_time,json=buyETime" json:"buy_e_time"`
	PickAble          uint32   `protobuf:"varint,13,req,name=pick_able,json=pickAble" json:"pick_able"`
	PickTimes         uint32   `protobuf:"varint,14,req,name=pick_times,json=pickTimes" json:"pick_times"`
	Left              uint32   `protobuf:"varint,15,req,name=left" json:"left"`
	Stock             uint32   `protobuf:"varint,16,req,name=stock" json:"stock"`
	UpdateTime        uint32   `protobuf:"varint,17,req,name=update_time,json=updateTime" json:"update_time"`
	Product           *Product `protobuf:"bytes,18,req,name=product" json:"product,omitempty"`
	PickSTime         uint32   `protobuf:"varint,19,opt,name=pick_s_time,json=pickSTime" json:"pick_s_time"`
	PickETime         uint32   `protobuf:"varint,20,opt,name=pick_e_time,json=pickETime" json:"pick_e_time"`
	UnclaimedCount    uint32   `protobuf:"varint,21,opt,name=unclaimed_count,json=unclaimedCount" json:"unclaimed_count"`
	RegisterIn        uint32   `protobuf:"varint,22,opt,name=register_in,json=registerIn" json:"register_in"`
}

func (m *SubTTGift) Reset()                    { *m = SubTTGift{} }
func (m *SubTTGift) String() string            { return proto.CompactTextString(m) }
func (*SubTTGift) ProtoMessage()               {}
func (*SubTTGift) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{18} }

func (m *SubTTGift) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *SubTTGift) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *SubTTGift) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SubTTGift) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *SubTTGift) GetLevelMin() uint32 {
	if m != nil {
		return m.LevelMin
	}
	return 0
}

func (m *SubTTGift) GetLevelMax() uint32 {
	if m != nil {
		return m.LevelMax
	}
	return 0
}

func (m *SubTTGift) GetPurchaseLimit() uint32 {
	if m != nil {
		return m.PurchaseLimit
	}
	return 0
}

func (m *SubTTGift) GetPurchaseCondition() uint64 {
	if m != nil {
		return m.PurchaseCondition
	}
	return 0
}

func (m *SubTTGift) GetDayLimit() uint32 {
	if m != nil {
		return m.DayLimit
	}
	return 0
}

func (m *SubTTGift) GetHourLimit() uint32 {
	if m != nil {
		return m.HourLimit
	}
	return 0
}

func (m *SubTTGift) GetBuySTime() uint32 {
	if m != nil {
		return m.BuySTime
	}
	return 0
}

func (m *SubTTGift) GetBuyETime() uint32 {
	if m != nil {
		return m.BuyETime
	}
	return 0
}

func (m *SubTTGift) GetPickAble() uint32 {
	if m != nil {
		return m.PickAble
	}
	return 0
}

func (m *SubTTGift) GetPickTimes() uint32 {
	if m != nil {
		return m.PickTimes
	}
	return 0
}

func (m *SubTTGift) GetLeft() uint32 {
	if m != nil {
		return m.Left
	}
	return 0
}

func (m *SubTTGift) GetStock() uint32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *SubTTGift) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *SubTTGift) GetProduct() *Product {
	if m != nil {
		return m.Product
	}
	return nil
}

func (m *SubTTGift) GetPickSTime() uint32 {
	if m != nil {
		return m.PickSTime
	}
	return 0
}

func (m *SubTTGift) GetPickETime() uint32 {
	if m != nil {
		return m.PickETime
	}
	return 0
}

func (m *SubTTGift) GetUnclaimedCount() uint32 {
	if m != nil {
		return m.UnclaimedCount
	}
	return 0
}

func (m *SubTTGift) GetRegisterIn() uint32 {
	if m != nil {
		return m.RegisterIn
	}
	return 0
}

type GetSubTTGiftResp struct {
	TtGiftList []*SubTTGift `protobuf:"bytes,1,rep,name=tt_gift_list,json=ttGiftList" json:"tt_gift_list,omitempty"`
	Total      uint32       `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetSubTTGiftResp) Reset()                    { *m = GetSubTTGiftResp{} }
func (m *GetSubTTGiftResp) String() string            { return proto.CompactTextString(m) }
func (*GetSubTTGiftResp) ProtoMessage()               {}
func (*GetSubTTGiftResp) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{19} }

func (m *GetSubTTGiftResp) GetTtGiftList() []*SubTTGift {
	if m != nil {
		return m.TtGiftList
	}
	return nil
}

func (m *GetSubTTGiftResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type SubGuildGift struct {
	ProductId      uint64   `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Price          uint32   `protobuf:"varint,2,req,name=price" json:"price"`
	Status         uint32   `protobuf:"varint,3,req,name=status" json:"status"`
	Rank           uint32   `protobuf:"varint,4,req,name=rank" json:"rank"`
	LevelMin       uint32   `protobuf:"varint,5,req,name=level_min,json=levelMin" json:"level_min"`
	LevelMax       uint32   `protobuf:"varint,6,req,name=level_max,json=levelMax" json:"level_max"`
	PurchaseLimit  uint32   `protobuf:"varint,7,req,name=purchase_limit,json=purchaseLimit" json:"purchase_limit"`
	BuySTime       uint32   `protobuf:"varint,8,req,name=buy_s_time,json=buySTime" json:"buy_s_time"`
	BuyETime       uint32   `protobuf:"varint,9,req,name=buy_e_time,json=buyETime" json:"buy_e_time"`
	Left           uint32   `protobuf:"varint,10,req,name=left" json:"left"`
	Stock          uint32   `protobuf:"varint,11,req,name=stock" json:"stock"`
	UpdateTime     uint32   `protobuf:"varint,12,req,name=update_time,json=updateTime" json:"update_time"`
	Product        *Product `protobuf:"bytes,13,req,name=product" json:"product,omitempty"`
	UnclaimedCount uint32   `protobuf:"varint,14,opt,name=unclaimed_count,json=unclaimedCount" json:"unclaimed_count"`
}

func (m *SubGuildGift) Reset()                    { *m = SubGuildGift{} }
func (m *SubGuildGift) String() string            { return proto.CompactTextString(m) }
func (*SubGuildGift) ProtoMessage()               {}
func (*SubGuildGift) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{20} }

func (m *SubGuildGift) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *SubGuildGift) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *SubGuildGift) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SubGuildGift) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *SubGuildGift) GetLevelMin() uint32 {
	if m != nil {
		return m.LevelMin
	}
	return 0
}

func (m *SubGuildGift) GetLevelMax() uint32 {
	if m != nil {
		return m.LevelMax
	}
	return 0
}

func (m *SubGuildGift) GetPurchaseLimit() uint32 {
	if m != nil {
		return m.PurchaseLimit
	}
	return 0
}

func (m *SubGuildGift) GetBuySTime() uint32 {
	if m != nil {
		return m.BuySTime
	}
	return 0
}

func (m *SubGuildGift) GetBuyETime() uint32 {
	if m != nil {
		return m.BuyETime
	}
	return 0
}

func (m *SubGuildGift) GetLeft() uint32 {
	if m != nil {
		return m.Left
	}
	return 0
}

func (m *SubGuildGift) GetStock() uint32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *SubGuildGift) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *SubGuildGift) GetProduct() *Product {
	if m != nil {
		return m.Product
	}
	return nil
}

func (m *SubGuildGift) GetUnclaimedCount() uint32 {
	if m != nil {
		return m.UnclaimedCount
	}
	return 0
}

type GetSubGuildGiftResp struct {
	GuildGiftList []*SubGuildGift `protobuf:"bytes,1,rep,name=guild_gift_list,json=guildGiftList" json:"guild_gift_list,omitempty"`
	Total         uint32          `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetSubGuildGiftResp) Reset()                    { *m = GetSubGuildGiftResp{} }
func (m *GetSubGuildGiftResp) String() string            { return proto.CompactTextString(m) }
func (*GetSubGuildGiftResp) ProtoMessage()               {}
func (*GetSubGuildGiftResp) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{21} }

func (m *GetSubGuildGiftResp) GetGuildGiftList() []*SubGuildGift {
	if m != nil {
		return m.GuildGiftList
	}
	return nil
}

func (m *GetSubGuildGiftResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type ActivitGift struct {
	ProductId      uint64   `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Left           uint32   `protobuf:"varint,2,req,name=left" json:"left"`
	Stock          uint32   `protobuf:"varint,3,req,name=stock" json:"stock"`
	UpdateTime     uint32   `protobuf:"varint,4,req,name=update_time,json=updateTime" json:"update_time"`
	Product        *Product `protobuf:"bytes,5,req,name=product" json:"product,omitempty"`
	UnclaimedCount uint32   `protobuf:"varint,6,opt,name=unclaimed_count,json=unclaimedCount" json:"unclaimed_count"`
}

func (m *ActivitGift) Reset()                    { *m = ActivitGift{} }
func (m *ActivitGift) String() string            { return proto.CompactTextString(m) }
func (*ActivitGift) ProtoMessage()               {}
func (*ActivitGift) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{22} }

func (m *ActivitGift) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ActivitGift) GetLeft() uint32 {
	if m != nil {
		return m.Left
	}
	return 0
}

func (m *ActivitGift) GetStock() uint32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *ActivitGift) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *ActivitGift) GetProduct() *Product {
	if m != nil {
		return m.Product
	}
	return nil
}

func (m *ActivitGift) GetUnclaimedCount() uint32 {
	if m != nil {
		return m.UnclaimedCount
	}
	return 0
}

type GetSubActivityGiftResp struct {
	ActivityGiftList []*ActivitGift `protobuf:"bytes,1,rep,name=activity_gift_list,json=activityGiftList" json:"activity_gift_list,omitempty"`
	Total            uint32         `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetSubActivityGiftResp) Reset()         { *m = GetSubActivityGiftResp{} }
func (m *GetSubActivityGiftResp) String() string { return proto.CompactTextString(m) }
func (*GetSubActivityGiftResp) ProtoMessage()    {}
func (*GetSubActivityGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{23}
}

func (m *GetSubActivityGiftResp) GetActivityGiftList() []*ActivitGift {
	if m != nil {
		return m.ActivityGiftList
	}
	return nil
}

func (m *GetSubActivityGiftResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type YGift struct {
	ProductId  uint64   `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Left       uint32   `protobuf:"varint,2,req,name=left" json:"left"`
	Stock      uint32   `protobuf:"varint,3,req,name=stock" json:"stock"`
	UpdateTime uint32   `protobuf:"varint,4,req,name=update_time,json=updateTime" json:"update_time"`
	Product    *Product `protobuf:"bytes,5,req,name=product" json:"product,omitempty"`
}

func (m *YGift) Reset()                    { *m = YGift{} }
func (m *YGift) String() string            { return proto.CompactTextString(m) }
func (*YGift) ProtoMessage()               {}
func (*YGift) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{24} }

func (m *YGift) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *YGift) GetLeft() uint32 {
	if m != nil {
		return m.Left
	}
	return 0
}

func (m *YGift) GetStock() uint32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *YGift) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *YGift) GetProduct() *Product {
	if m != nil {
		return m.Product
	}
	return nil
}

type GetSubYGiftResp struct {
	YGiftList []*YGift `protobuf:"bytes,1,rep,name=y_gift_list,json=yGiftList" json:"y_gift_list,omitempty"`
	Total     uint32   `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetSubYGiftResp) Reset()                    { *m = GetSubYGiftResp{} }
func (m *GetSubYGiftResp) String() string            { return proto.CompactTextString(m) }
func (*GetSubYGiftResp) ProtoMessage()               {}
func (*GetSubYGiftResp) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{25} }

func (m *GetSubYGiftResp) GetYGiftList() []*YGift {
	if m != nil {
		return m.YGiftList
	}
	return nil
}

func (m *GetSubYGiftResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type OperateGift struct {
	ProductId  uint64   `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Left       uint32   `protobuf:"varint,2,req,name=left" json:"left"`
	Stock      uint32   `protobuf:"varint,3,req,name=stock" json:"stock"`
	UpdateTime uint32   `protobuf:"varint,4,req,name=update_time,json=updateTime" json:"update_time"`
	Product    *Product `protobuf:"bytes,5,req,name=product" json:"product,omitempty"`
}

func (m *OperateGift) Reset()                    { *m = OperateGift{} }
func (m *OperateGift) String() string            { return proto.CompactTextString(m) }
func (*OperateGift) ProtoMessage()               {}
func (*OperateGift) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{26} }

func (m *OperateGift) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *OperateGift) GetLeft() uint32 {
	if m != nil {
		return m.Left
	}
	return 0
}

func (m *OperateGift) GetStock() uint32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *OperateGift) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *OperateGift) GetProduct() *Product {
	if m != nil {
		return m.Product
	}
	return nil
}

type GetSubOperateGiftResp struct {
	OperateGiftList []*OperateGift `protobuf:"bytes,1,rep,name=operate_gift_list,json=operateGiftList" json:"operate_gift_list,omitempty"`
	Total           uint32         `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetSubOperateGiftResp) Reset()         { *m = GetSubOperateGiftResp{} }
func (m *GetSubOperateGiftResp) String() string { return proto.CompactTextString(m) }
func (*GetSubOperateGiftResp) ProtoMessage()    {}
func (*GetSubOperateGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{27}
}

func (m *GetSubOperateGiftResp) GetOperateGiftList() []*OperateGift {
	if m != nil {
		return m.OperateGiftList
	}
	return nil
}

func (m *GetSubOperateGiftResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type ModifyGiftRankReq struct {
	SubType    uint32   `protobuf:"varint,1,req,name=sub_type,json=subType" json:"sub_type"`
	ProductIds []uint64 `protobuf:"varint,2,rep,name=product_ids,json=productIds" json:"product_ids,omitempty"`
	GiftType   uint32   `protobuf:"varint,3,opt,name=gift_type,json=giftType" json:"gift_type"`
}

func (m *ModifyGiftRankReq) Reset()                    { *m = ModifyGiftRankReq{} }
func (m *ModifyGiftRankReq) String() string            { return proto.CompactTextString(m) }
func (*ModifyGiftRankReq) ProtoMessage()               {}
func (*ModifyGiftRankReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{28} }

func (m *ModifyGiftRankReq) GetSubType() uint32 {
	if m != nil {
		return m.SubType
	}
	return 0
}

func (m *ModifyGiftRankReq) GetProductIds() []uint64 {
	if m != nil {
		return m.ProductIds
	}
	return nil
}

func (m *ModifyGiftRankReq) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

type DispatchSubTTGiftReq struct {
	ProductId uint64   `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	UidList   []uint32 `protobuf:"varint,2,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	Operator  string   `protobuf:"bytes,3,req,name=operator" json:"operator"`
}

func (m *DispatchSubTTGiftReq) Reset()         { *m = DispatchSubTTGiftReq{} }
func (m *DispatchSubTTGiftReq) String() string { return proto.CompactTextString(m) }
func (*DispatchSubTTGiftReq) ProtoMessage()    {}
func (*DispatchSubTTGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{29}
}

func (m *DispatchSubTTGiftReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *DispatchSubTTGiftReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *DispatchSubTTGiftReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

// only for tt or guild subgift
type DispatchSubGiftResp struct {
	Product  *Product       `protobuf:"bytes,1,req,name=product" json:"product,omitempty"`
	ItemList []*ProductItem `protobuf:"bytes,2,rep,name=item_list,json=itemList" json:"item_list,omitempty"`
}

func (m *DispatchSubGiftResp) Reset()                    { *m = DispatchSubGiftResp{} }
func (m *DispatchSubGiftResp) String() string            { return proto.CompactTextString(m) }
func (*DispatchSubGiftResp) ProtoMessage()               {}
func (*DispatchSubGiftResp) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{30} }

func (m *DispatchSubGiftResp) GetProduct() *Product {
	if m != nil {
		return m.Product
	}
	return nil
}

func (m *DispatchSubGiftResp) GetItemList() []*ProductItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type DispatchSubGuildGiftReq struct {
	ProductId uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	GuildId   uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	Count     uint32 `protobuf:"varint,3,req,name=count" json:"count"`
	Operator  string `protobuf:"bytes,4,req,name=operator" json:"operator"`
}

func (m *DispatchSubGuildGiftReq) Reset()         { *m = DispatchSubGuildGiftReq{} }
func (m *DispatchSubGuildGiftReq) String() string { return proto.CompactTextString(m) }
func (*DispatchSubGuildGiftReq) ProtoMessage()    {}
func (*DispatchSubGuildGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{31}
}

func (m *DispatchSubGuildGiftReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *DispatchSubGuildGiftReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DispatchSubGuildGiftReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *DispatchSubGuildGiftReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type GetGameGiftAmountReq struct {
	GameIdList []uint32 `protobuf:"varint,1,rep,name=game_id_list,json=gameIdList" json:"game_id_list,omitempty"`
	SubType    uint32   `protobuf:"varint,2,req,name=sub_type,json=subType" json:"sub_type"`
	Platform   uint32   `protobuf:"varint,3,req,name=platform" json:"platform"`
}

func (m *GetGameGiftAmountReq) Reset()         { *m = GetGameGiftAmountReq{} }
func (m *GetGameGiftAmountReq) String() string { return proto.CompactTextString(m) }
func (*GetGameGiftAmountReq) ProtoMessage()    {}
func (*GetGameGiftAmountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{32}
}

func (m *GetGameGiftAmountReq) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

func (m *GetGameGiftAmountReq) GetSubType() uint32 {
	if m != nil {
		return m.SubType
	}
	return 0
}

func (m *GetGameGiftAmountReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

type GameGiftAmount struct {
	GameId     uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	GiftAmount uint32 `protobuf:"varint,2,req,name=gift_amount,json=giftAmount" json:"gift_amount"`
}

func (m *GameGiftAmount) Reset()                    { *m = GameGiftAmount{} }
func (m *GameGiftAmount) String() string            { return proto.CompactTextString(m) }
func (*GameGiftAmount) ProtoMessage()               {}
func (*GameGiftAmount) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{33} }

func (m *GameGiftAmount) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameGiftAmount) GetGiftAmount() uint32 {
	if m != nil {
		return m.GiftAmount
	}
	return 0
}

type GetGameGiftAmountResp struct {
	AmountList []*GameGiftAmount `protobuf:"bytes,1,rep,name=amount_list,json=amountList" json:"amount_list,omitempty"`
}

func (m *GetGameGiftAmountResp) Reset()         { *m = GetGameGiftAmountResp{} }
func (m *GetGameGiftAmountResp) String() string { return proto.CompactTextString(m) }
func (*GetGameGiftAmountResp) ProtoMessage()    {}
func (*GetGameGiftAmountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{34}
}

func (m *GetGameGiftAmountResp) GetAmountList() []*GameGiftAmount {
	if m != nil {
		return m.AmountList
	}
	return nil
}

type GetTTGiftPurchaseAuthReq struct {
	Uid           uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	ProductIdList []uint64 `protobuf:"varint,2,rep,name=product_id_list,json=productIdList" json:"product_id_list,omitempty"`
}

func (m *GetTTGiftPurchaseAuthReq) Reset()         { *m = GetTTGiftPurchaseAuthReq{} }
func (m *GetTTGiftPurchaseAuthReq) String() string { return proto.CompactTextString(m) }
func (*GetTTGiftPurchaseAuthReq) ProtoMessage()    {}
func (*GetTTGiftPurchaseAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{35}
}

func (m *GetTTGiftPurchaseAuthReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTTGiftPurchaseAuthReq) GetProductIdList() []uint64 {
	if m != nil {
		return m.ProductIdList
	}
	return nil
}

type TTGiftPurchaseAuth struct {
	ProductId       uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Auth            bool   `protobuf:"varint,2,req,name=auth" json:"auth"`
	AlreadyPurchase uint32 `protobuf:"varint,3,opt,name=already_purchase,json=alreadyPurchase" json:"already_purchase"`
}

func (m *TTGiftPurchaseAuth) Reset()                    { *m = TTGiftPurchaseAuth{} }
func (m *TTGiftPurchaseAuth) String() string            { return proto.CompactTextString(m) }
func (*TTGiftPurchaseAuth) ProtoMessage()               {}
func (*TTGiftPurchaseAuth) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{36} }

func (m *TTGiftPurchaseAuth) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *TTGiftPurchaseAuth) GetAuth() bool {
	if m != nil {
		return m.Auth
	}
	return false
}

func (m *TTGiftPurchaseAuth) GetAlreadyPurchase() uint32 {
	if m != nil {
		return m.AlreadyPurchase
	}
	return 0
}

type GetTTGiftPurchaseAuthResp struct {
	PurchaseAuthList []*TTGiftPurchaseAuth `protobuf:"bytes,1,rep,name=purchase_auth_list,json=purchaseAuthList" json:"purchase_auth_list,omitempty"`
}

func (m *GetTTGiftPurchaseAuthResp) Reset()         { *m = GetTTGiftPurchaseAuthResp{} }
func (m *GetTTGiftPurchaseAuthResp) String() string { return proto.CompactTextString(m) }
func (*GetTTGiftPurchaseAuthResp) ProtoMessage()    {}
func (*GetTTGiftPurchaseAuthResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{37}
}

func (m *GetTTGiftPurchaseAuthResp) GetPurchaseAuthList() []*TTGiftPurchaseAuth {
	if m != nil {
		return m.PurchaseAuthList
	}
	return nil
}

type GetGuildGiftPurchaseAuthReq struct {
	GuildId    uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductId  uint64 `protobuf:"varint,2,req,name=product_id,json=productId" json:"product_id"`
	GuildLevel uint32 `protobuf:"varint,3,req,name=guild_level,json=guildLevel" json:"guild_level"`
	BuyNumber  uint32 `protobuf:"varint,4,req,name=buy_number,json=buyNumber" json:"buy_number"`
}

func (m *GetGuildGiftPurchaseAuthReq) Reset()         { *m = GetGuildGiftPurchaseAuthReq{} }
func (m *GetGuildGiftPurchaseAuthReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildGiftPurchaseAuthReq) ProtoMessage()    {}
func (*GetGuildGiftPurchaseAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{38}
}

func (m *GetGuildGiftPurchaseAuthReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildGiftPurchaseAuthReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetGuildGiftPurchaseAuthReq) GetGuildLevel() uint32 {
	if m != nil {
		return m.GuildLevel
	}
	return 0
}

func (m *GetGuildGiftPurchaseAuthReq) GetBuyNumber() uint32 {
	if m != nil {
		return m.BuyNumber
	}
	return 0
}

type GetGuildGiftPurchaseAuthResp struct {
	BuyLimit  int32 `protobuf:"varint,1,req,name=buy_limit,json=buyLimit" json:"buy_limit"`
	LevelAuth bool  `protobuf:"varint,2,req,name=level_auth,json=levelAuth" json:"level_auth"`
}

func (m *GetGuildGiftPurchaseAuthResp) Reset()         { *m = GetGuildGiftPurchaseAuthResp{} }
func (m *GetGuildGiftPurchaseAuthResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildGiftPurchaseAuthResp) ProtoMessage()    {}
func (*GetGuildGiftPurchaseAuthResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{39}
}

func (m *GetGuildGiftPurchaseAuthResp) GetBuyLimit() int32 {
	if m != nil {
		return m.BuyLimit
	}
	return 0
}

func (m *GetGuildGiftPurchaseAuthResp) GetLevelAuth() bool {
	if m != nil {
		return m.LevelAuth
	}
	return false
}

// 导出 指定子库的礼物商品的指定数量的物品 并将该物品设置为已经发放
type ExportSubGiftReq struct {
	ProductId        uint64   `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Amount           uint32   `protobuf:"varint,2,req,name=amount" json:"amount"`
	SubType          uint32   `protobuf:"varint,3,req,name=sub_type,json=subType" json:"sub_type"`
	ExportToType     uint32   `protobuf:"varint,4,opt,name=export_to_type,json=exportToType" json:"export_to_type"`
	ExportToIdList   []uint32 `protobuf:"varint,5,rep,name=export_to_id_list,json=exportToIdList" json:"export_to_id_list,omitempty"`
	ExportToIdPerCnt uint32   `protobuf:"varint,6,opt,name=export_to_id_per_cnt,json=exportToIdPerCnt" json:"export_to_id_per_cnt"`
}

func (m *ExportSubGiftReq) Reset()                    { *m = ExportSubGiftReq{} }
func (m *ExportSubGiftReq) String() string            { return proto.CompactTextString(m) }
func (*ExportSubGiftReq) ProtoMessage()               {}
func (*ExportSubGiftReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{40} }

func (m *ExportSubGiftReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ExportSubGiftReq) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *ExportSubGiftReq) GetSubType() uint32 {
	if m != nil {
		return m.SubType
	}
	return 0
}

func (m *ExportSubGiftReq) GetExportToType() uint32 {
	if m != nil {
		return m.ExportToType
	}
	return 0
}

func (m *ExportSubGiftReq) GetExportToIdList() []uint32 {
	if m != nil {
		return m.ExportToIdList
	}
	return nil
}

func (m *ExportSubGiftReq) GetExportToIdPerCnt() uint32 {
	if m != nil {
		return m.ExportToIdPerCnt
	}
	return 0
}

type ExportSubGiftResp struct {
	ItemList []*ProductItem `protobuf:"bytes,1,rep,name=item_list,json=itemList" json:"item_list,omitempty"`
}

func (m *ExportSubGiftResp) Reset()                    { *m = ExportSubGiftResp{} }
func (m *ExportSubGiftResp) String() string            { return proto.CompactTextString(m) }
func (*ExportSubGiftResp) ProtoMessage()               {}
func (*ExportSubGiftResp) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{41} }

func (m *ExportSubGiftResp) GetItemList() []*ProductItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type ClaimGiftReq struct {
	Id uint32 `protobuf:"varint,1,req,name=id" json:"id"`
}

func (m *ClaimGiftReq) Reset()                    { *m = ClaimGiftReq{} }
func (m *ClaimGiftReq) String() string            { return proto.CompactTextString(m) }
func (*ClaimGiftReq) ProtoMessage()               {}
func (*ClaimGiftReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{42} }

func (m *ClaimGiftReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type ClaimGiftResp struct {
	GiftList []*DispatchSubGiftResp `protobuf:"bytes,1,rep,name=gift_list,json=giftList" json:"gift_list,omitempty"`
}

func (m *ClaimGiftResp) Reset()                    { *m = ClaimGiftResp{} }
func (m *ClaimGiftResp) String() string            { return proto.CompactTextString(m) }
func (*ClaimGiftResp) ProtoMessage()               {}
func (*ClaimGiftResp) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{43} }

func (m *ClaimGiftResp) GetGiftList() []*DispatchSubGiftResp {
	if m != nil {
		return m.GiftList
	}
	return nil
}

type GetGiftDispatchRecordReq struct {
	ProductId uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
}

func (m *GetGiftDispatchRecordReq) Reset()         { *m = GetGiftDispatchRecordReq{} }
func (m *GetGiftDispatchRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetGiftDispatchRecordReq) ProtoMessage()    {}
func (*GetGiftDispatchRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{44}
}

func (m *GetGiftDispatchRecordReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type TTGiftDispatchInfo struct {
	Uid           uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	DispatchTime  uint32 `protobuf:"varint,2,req,name=dispatch_time,json=dispatchTime" json:"dispatch_time"`
	DispatchCount uint32 `protobuf:"varint,3,req,name=dispatch_count,json=dispatchCount" json:"dispatch_count"`
	Status        uint32 `protobuf:"varint,4,req,name=status" json:"status"`
	ClaimTime     uint32 `protobuf:"varint,5,req,name=claim_time,json=claimTime" json:"claim_time"`
}

func (m *TTGiftDispatchInfo) Reset()                    { *m = TTGiftDispatchInfo{} }
func (m *TTGiftDispatchInfo) String() string            { return proto.CompactTextString(m) }
func (*TTGiftDispatchInfo) ProtoMessage()               {}
func (*TTGiftDispatchInfo) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{45} }

func (m *TTGiftDispatchInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TTGiftDispatchInfo) GetDispatchTime() uint32 {
	if m != nil {
		return m.DispatchTime
	}
	return 0
}

func (m *TTGiftDispatchInfo) GetDispatchCount() uint32 {
	if m != nil {
		return m.DispatchCount
	}
	return 0
}

func (m *TTGiftDispatchInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *TTGiftDispatchInfo) GetClaimTime() uint32 {
	if m != nil {
		return m.ClaimTime
	}
	return 0
}

type GetTTGiftDispatchRecordResp struct {
	DispatchList []*TTGiftDispatchInfo `protobuf:"bytes,1,rep,name=dispatch_list,json=dispatchList" json:"dispatch_list,omitempty"`
}

func (m *GetTTGiftDispatchRecordResp) Reset()         { *m = GetTTGiftDispatchRecordResp{} }
func (m *GetTTGiftDispatchRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetTTGiftDispatchRecordResp) ProtoMessage()    {}
func (*GetTTGiftDispatchRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{46}
}

func (m *GetTTGiftDispatchRecordResp) GetDispatchList() []*TTGiftDispatchInfo {
	if m != nil {
		return m.DispatchList
	}
	return nil
}

type GuildGiftDispatchInfo struct {
	GuildId       uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	DispatchTime  uint32 `protobuf:"varint,2,req,name=dispatch_time,json=dispatchTime" json:"dispatch_time"`
	DispatchCount uint32 `protobuf:"varint,3,req,name=dispatch_count,json=dispatchCount" json:"dispatch_count"`
}

func (m *GuildGiftDispatchInfo) Reset()         { *m = GuildGiftDispatchInfo{} }
func (m *GuildGiftDispatchInfo) String() string { return proto.CompactTextString(m) }
func (*GuildGiftDispatchInfo) ProtoMessage()    {}
func (*GuildGiftDispatchInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{47}
}

func (m *GuildGiftDispatchInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildGiftDispatchInfo) GetDispatchTime() uint32 {
	if m != nil {
		return m.DispatchTime
	}
	return 0
}

func (m *GuildGiftDispatchInfo) GetDispatchCount() uint32 {
	if m != nil {
		return m.DispatchCount
	}
	return 0
}

type GetGuildGiftDispatchRecordResp struct {
	DispatchList []*GuildGiftDispatchInfo `protobuf:"bytes,1,rep,name=dispatch_list,json=dispatchList" json:"dispatch_list,omitempty"`
}

func (m *GetGuildGiftDispatchRecordResp) Reset()         { *m = GetGuildGiftDispatchRecordResp{} }
func (m *GetGuildGiftDispatchRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildGiftDispatchRecordResp) ProtoMessage()    {}
func (*GetGuildGiftDispatchRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{48}
}

func (m *GetGuildGiftDispatchRecordResp) GetDispatchList() []*GuildGiftDispatchInfo {
	if m != nil {
		return m.DispatchList
	}
	return nil
}

type RecycleGiftReq struct {
	ProductId uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
}

func (m *RecycleGiftReq) Reset()                    { *m = RecycleGiftReq{} }
func (m *RecycleGiftReq) String() string            { return proto.CompactTextString(m) }
func (*RecycleGiftReq) ProtoMessage()               {}
func (*RecycleGiftReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{49} }

func (m *RecycleGiftReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type AddGiftActivityReq struct {
	Title        string `protobuf:"bytes,1,req,name=title" json:"title"`
	ActivityType uint32 `protobuf:"varint,2,req,name=activity_type,json=activityType" json:"activity_type"`
	PicUrl       string `protobuf:"bytes,3,req,name=pic_url,json=picUrl" json:"pic_url"`
	JumpUrl      string `protobuf:"bytes,4,req,name=jump_url,json=jumpUrl" json:"jump_url"`
	IconUrl      string `protobuf:"bytes,5,req,name=icon_url,json=iconUrl" json:"icon_url"`
	GameId       uint32 `protobuf:"varint,6,req,name=game_id,json=gameId" json:"game_id"`
	StartTime    uint32 `protobuf:"varint,7,req,name=start_time,json=startTime" json:"start_time"`
	EndTime      uint32 `protobuf:"varint,8,req,name=end_time,json=endTime" json:"end_time"`
	Operator     string `protobuf:"bytes,9,req,name=operator" json:"operator"`
}

func (m *AddGiftActivityReq) Reset()                    { *m = AddGiftActivityReq{} }
func (m *AddGiftActivityReq) String() string            { return proto.CompactTextString(m) }
func (*AddGiftActivityReq) ProtoMessage()               {}
func (*AddGiftActivityReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{50} }

func (m *AddGiftActivityReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AddGiftActivityReq) GetActivityType() uint32 {
	if m != nil {
		return m.ActivityType
	}
	return 0
}

func (m *AddGiftActivityReq) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *AddGiftActivityReq) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *AddGiftActivityReq) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *AddGiftActivityReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *AddGiftActivityReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *AddGiftActivityReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *AddGiftActivityReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type AddGiftActivityResp struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
}

func (m *AddGiftActivityResp) Reset()                    { *m = AddGiftActivityResp{} }
func (m *AddGiftActivityResp) String() string            { return proto.CompactTextString(m) }
func (*AddGiftActivityResp) ProtoMessage()               {}
func (*AddGiftActivityResp) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{51} }

func (m *AddGiftActivityResp) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type DelGiftActivityReq struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	Operator   string `protobuf:"bytes,2,req,name=operator" json:"operator"`
}

func (m *DelGiftActivityReq) Reset()                    { *m = DelGiftActivityReq{} }
func (m *DelGiftActivityReq) String() string            { return proto.CompactTextString(m) }
func (*DelGiftActivityReq) ProtoMessage()               {}
func (*DelGiftActivityReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{52} }

func (m *DelGiftActivityReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DelGiftActivityReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type ModifyGiftActivityRankReq struct {
	ActivityIdList []uint32 `protobuf:"varint,1,rep,name=activity_id_list,json=activityIdList" json:"activity_id_list,omitempty"`
}

func (m *ModifyGiftActivityRankReq) Reset()         { *m = ModifyGiftActivityRankReq{} }
func (m *ModifyGiftActivityRankReq) String() string { return proto.CompactTextString(m) }
func (*ModifyGiftActivityRankReq) ProtoMessage()    {}
func (*ModifyGiftActivityRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{53}
}

func (m *ModifyGiftActivityRankReq) GetActivityIdList() []uint32 {
	if m != nil {
		return m.ActivityIdList
	}
	return nil
}

type GiftActivity struct {
	ActivityId   uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	Title        string `protobuf:"bytes,2,req,name=title" json:"title"`
	ActivityType uint32 `protobuf:"varint,3,req,name=activity_type,json=activityType" json:"activity_type"`
	PicUrl       string `protobuf:"bytes,4,req,name=pic_url,json=picUrl" json:"pic_url"`
	IconUrl      string `protobuf:"bytes,5,req,name=icon_url,json=iconUrl" json:"icon_url"`
	JumpUrl      string `protobuf:"bytes,6,req,name=jump_url,json=jumpUrl" json:"jump_url"`
	GameId       uint32 `protobuf:"varint,7,req,name=game_id,json=gameId" json:"game_id"`
	StartTime    uint32 `protobuf:"varint,8,req,name=start_time,json=startTime" json:"start_time"`
	EndTime      uint32 `protobuf:"varint,9,req,name=end_time,json=endTime" json:"end_time"`
	Rank         uint32 `protobuf:"varint,10,req,name=rank" json:"rank"`
	CreateTime   uint32 `protobuf:"varint,11,req,name=create_time,json=createTime" json:"create_time"`
}

func (m *GiftActivity) Reset()                    { *m = GiftActivity{} }
func (m *GiftActivity) String() string            { return proto.CompactTextString(m) }
func (*GiftActivity) ProtoMessage()               {}
func (*GiftActivity) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{54} }

func (m *GiftActivity) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *GiftActivity) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GiftActivity) GetActivityType() uint32 {
	if m != nil {
		return m.ActivityType
	}
	return 0
}

func (m *GiftActivity) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *GiftActivity) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *GiftActivity) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *GiftActivity) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GiftActivity) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GiftActivity) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GiftActivity) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *GiftActivity) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type GetGiftActivityReq struct {
	ActivityId       uint32 `protobuf:"varint,1,opt,name=activity_id,json=activityId" json:"activity_id"`
	GameId           uint32 `protobuf:"varint,2,opt,name=game_id,json=gameId" json:"game_id"`
	CreateAfterTime  uint64 `protobuf:"varint,3,opt,name=create_after_time,json=createAfterTime" json:"create_after_time"`
	CreateBeforeTime uint64 `protobuf:"varint,4,opt,name=create_before_time,json=createBeforeTime" json:"create_before_time"`
	ActivityType     uint32 `protobuf:"varint,5,opt,name=activity_type,json=activityType" json:"activity_type"`
	OrderByType      uint32 `protobuf:"varint,6,req,name=order_by_type,json=orderByType" json:"order_by_type"`
	Desc             bool   `protobuf:"varint,7,req,name=desc" json:"desc"`
	LimitStart       uint32 `protobuf:"varint,8,req,name=limit_start,json=limitStart" json:"limit_start"`
	LimitEnd         uint32 `protobuf:"varint,9,req,name=limit_end,json=limitEnd" json:"limit_end"`
}

func (m *GetGiftActivityReq) Reset()                    { *m = GetGiftActivityReq{} }
func (m *GetGiftActivityReq) String() string            { return proto.CompactTextString(m) }
func (*GetGiftActivityReq) ProtoMessage()               {}
func (*GetGiftActivityReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{55} }

func (m *GetGiftActivityReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *GetGiftActivityReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGiftActivityReq) GetCreateAfterTime() uint64 {
	if m != nil {
		return m.CreateAfterTime
	}
	return 0
}

func (m *GetGiftActivityReq) GetCreateBeforeTime() uint64 {
	if m != nil {
		return m.CreateBeforeTime
	}
	return 0
}

func (m *GetGiftActivityReq) GetActivityType() uint32 {
	if m != nil {
		return m.ActivityType
	}
	return 0
}

func (m *GetGiftActivityReq) GetOrderByType() uint32 {
	if m != nil {
		return m.OrderByType
	}
	return 0
}

func (m *GetGiftActivityReq) GetDesc() bool {
	if m != nil {
		return m.Desc
	}
	return false
}

func (m *GetGiftActivityReq) GetLimitStart() uint32 {
	if m != nil {
		return m.LimitStart
	}
	return 0
}

func (m *GetGiftActivityReq) GetLimitEnd() uint32 {
	if m != nil {
		return m.LimitEnd
	}
	return 0
}

type GetGiftActivityResp struct {
	GiftActivityList []*GiftActivity `protobuf:"bytes,1,rep,name=gift_activity_list,json=giftActivityList" json:"gift_activity_list,omitempty"`
	Total            uint32          `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetGiftActivityResp) Reset()                    { *m = GetGiftActivityResp{} }
func (m *GetGiftActivityResp) String() string            { return proto.CompactTextString(m) }
func (*GetGiftActivityResp) ProtoMessage()               {}
func (*GetGiftActivityResp) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{56} }

func (m *GetGiftActivityResp) GetGiftActivityList() []*GiftActivity {
	if m != nil {
		return m.GiftActivityList
	}
	return nil
}

func (m *GetGiftActivityResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type ActivityBanner struct {
	PicUrl     string `protobuf:"bytes,1,req,name=pic_url,json=picUrl" json:"pic_url"`
	JumpUrl    string `protobuf:"bytes,2,req,name=jump_url,json=jumpUrl" json:"jump_url"`
	ActivityId uint32 `protobuf:"varint,3,req,name=activity_id,json=activityId" json:"activity_id"`
}

func (m *ActivityBanner) Reset()                    { *m = ActivityBanner{} }
func (m *ActivityBanner) String() string            { return proto.CompactTextString(m) }
func (*ActivityBanner) ProtoMessage()               {}
func (*ActivityBanner) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{57} }

func (m *ActivityBanner) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *ActivityBanner) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *ActivityBanner) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type GetActivityBannerResp struct {
	BannerList []*ActivityBanner `protobuf:"bytes,1,rep,name=banner_list,json=bannerList" json:"banner_list,omitempty"`
}

func (m *GetActivityBannerResp) Reset()         { *m = GetActivityBannerResp{} }
func (m *GetActivityBannerResp) String() string { return proto.CompactTextString(m) }
func (*GetActivityBannerResp) ProtoMessage()    {}
func (*GetActivityBannerResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{58}
}

func (m *GetActivityBannerResp) GetBannerList() []*ActivityBanner {
	if m != nil {
		return m.BannerList
	}
	return nil
}

type PurchaseTTGiftReq struct {
	ProductId uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Uid       uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *PurchaseTTGiftReq) Reset()                    { *m = PurchaseTTGiftReq{} }
func (m *PurchaseTTGiftReq) String() string            { return proto.CompactTextString(m) }
func (*PurchaseTTGiftReq) ProtoMessage()               {}
func (*PurchaseTTGiftReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{59} }

func (m *PurchaseTTGiftReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *PurchaseTTGiftReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type PurchaseTTGiftResp struct {
	GiftInfo *SubTTGift   `protobuf:"bytes,1,req,name=gift_info,json=giftInfo" json:"gift_info,omitempty"`
	ItemInfo *ProductItem `protobuf:"bytes,2,req,name=item_info,json=itemInfo" json:"item_info,omitempty"`
}

func (m *PurchaseTTGiftResp) Reset()                    { *m = PurchaseTTGiftResp{} }
func (m *PurchaseTTGiftResp) String() string            { return proto.CompactTextString(m) }
func (*PurchaseTTGiftResp) ProtoMessage()               {}
func (*PurchaseTTGiftResp) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{60} }

func (m *PurchaseTTGiftResp) GetGiftInfo() *SubTTGift {
	if m != nil {
		return m.GiftInfo
	}
	return nil
}

func (m *PurchaseTTGiftResp) GetItemInfo() *ProductItem {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

type PurchaseGuildGiftReq struct {
	ProductId uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	GuildId   uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	Count     uint32 `protobuf:"varint,3,req,name=count" json:"count"`
}

func (m *PurchaseGuildGiftReq) Reset()         { *m = PurchaseGuildGiftReq{} }
func (m *PurchaseGuildGiftReq) String() string { return proto.CompactTextString(m) }
func (*PurchaseGuildGiftReq) ProtoMessage()    {}
func (*PurchaseGuildGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{61}
}

func (m *PurchaseGuildGiftReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *PurchaseGuildGiftReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *PurchaseGuildGiftReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type PurchaseGuildGiftResp struct {
	GiftInfo *SubGuildGift  `protobuf:"bytes,1,req,name=gift_info,json=giftInfo" json:"gift_info,omitempty"`
	ItemList []*ProductItem `protobuf:"bytes,2,rep,name=item_list,json=itemList" json:"item_list,omitempty"`
}

func (m *PurchaseGuildGiftResp) Reset()         { *m = PurchaseGuildGiftResp{} }
func (m *PurchaseGuildGiftResp) String() string { return proto.CompactTextString(m) }
func (*PurchaseGuildGiftResp) ProtoMessage()    {}
func (*PurchaseGuildGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{62}
}

func (m *PurchaseGuildGiftResp) GetGiftInfo() *SubGuildGift {
	if m != nil {
		return m.GiftInfo
	}
	return nil
}

func (m *PurchaseGuildGiftResp) GetItemList() []*ProductItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type PickTTGiftReq struct {
	ProductId uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
}

func (m *PickTTGiftReq) Reset()                    { *m = PickTTGiftReq{} }
func (m *PickTTGiftReq) String() string            { return proto.CompactTextString(m) }
func (*PickTTGiftReq) ProtoMessage()               {}
func (*PickTTGiftReq) Descriptor() ([]byte, []int) { return fileDescriptorTtgiftcenter, []int{63} }

func (m *PickTTGiftReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type GetTTGiftDayLimitReq struct {
	ProductId uint64 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
}

func (m *GetTTGiftDayLimitReq) Reset()         { *m = GetTTGiftDayLimitReq{} }
func (m *GetTTGiftDayLimitReq) String() string { return proto.CompactTextString(m) }
func (*GetTTGiftDayLimitReq) ProtoMessage()    {}
func (*GetTTGiftDayLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{64}
}

func (m *GetTTGiftDayLimitReq) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type GetTTGiftDayLimitResp struct {
	Used       uint32 `protobuf:"varint,1,req,name=used" json:"used"`
	TotalLimit uint32 `protobuf:"varint,2,req,name=total_limit,json=totalLimit" json:"total_limit"`
}

func (m *GetTTGiftDayLimitResp) Reset()         { *m = GetTTGiftDayLimitResp{} }
func (m *GetTTGiftDayLimitResp) String() string { return proto.CompactTextString(m) }
func (*GetTTGiftDayLimitResp) ProtoMessage()    {}
func (*GetTTGiftDayLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTtgiftcenter, []int{65}
}

func (m *GetTTGiftDayLimitResp) GetUsed() uint32 {
	if m != nil {
		return m.Used
	}
	return 0
}

func (m *GetTTGiftDayLimitResp) GetTotalLimit() uint32 {
	if m != nil {
		return m.TotalLimit
	}
	return 0
}

func init() {
	proto.RegisterType((*ProductItem)(nil), "TTGiftCenter.ProductItem")
	proto.RegisterType((*CreateProductReq)(nil), "TTGiftCenter.CreateProductReq")
	proto.RegisterType((*CreateProductResp)(nil), "TTGiftCenter.CreateProductResp")
	proto.RegisterType((*ModifyProductReq)(nil), "TTGiftCenter.ModifyProductReq")
	proto.RegisterType((*Product)(nil), "TTGiftCenter.Product")
	proto.RegisterType((*SearchProductReq)(nil), "TTGiftCenter.SearchProductReq")
	proto.RegisterType((*SearchProductResp)(nil), "TTGiftCenter.SearchProductResp")
	proto.RegisterType((*BatchGetProductReq)(nil), "TTGiftCenter.BatchGetProductReq")
	proto.RegisterType((*BatchGetProductResp)(nil), "TTGiftCenter.BatchGetProductResp")
	proto.RegisterType((*DispatchItemReq)(nil), "TTGiftCenter.DispatchItemReq")
	proto.RegisterType((*AccountPwdItem)(nil), "TTGiftCenter.AccountPwdItem")
	proto.RegisterType((*ExchangeCardItem)(nil), "TTGiftCenter.ExchangeCardItem")
	proto.RegisterType((*AddItemReq)(nil), "TTGiftCenter.AddItemReq")
	proto.RegisterType((*AddItemResp)(nil), "TTGiftCenter.AddItemResp")
	proto.RegisterType((*UpdateSubTTGiftReq)(nil), "TTGiftCenter.UpdateSubTTGiftReq")
	proto.RegisterType((*UpdateSubGuildGiftReq)(nil), "TTGiftCenter.UpdateSubGuildGiftReq")
	proto.RegisterType((*AddSubYorActGiftReq)(nil), "TTGiftCenter.AddSubYorActGiftReq")
	proto.RegisterType((*GetSubGiftReq)(nil), "TTGiftCenter.GetSubGiftReq")
	proto.RegisterType((*SubTTGift)(nil), "TTGiftCenter.SubTTGift")
	proto.RegisterType((*GetSubTTGiftResp)(nil), "TTGiftCenter.GetSubTTGiftResp")
	proto.RegisterType((*SubGuildGift)(nil), "TTGiftCenter.SubGuildGift")
	proto.RegisterType((*GetSubGuildGiftResp)(nil), "TTGiftCenter.GetSubGuildGiftResp")
	proto.RegisterType((*ActivitGift)(nil), "TTGiftCenter.ActivitGift")
	proto.RegisterType((*GetSubActivityGiftResp)(nil), "TTGiftCenter.GetSubActivityGiftResp")
	proto.RegisterType((*YGift)(nil), "TTGiftCenter.YGift")
	proto.RegisterType((*GetSubYGiftResp)(nil), "TTGiftCenter.GetSubYGiftResp")
	proto.RegisterType((*OperateGift)(nil), "TTGiftCenter.OperateGift")
	proto.RegisterType((*GetSubOperateGiftResp)(nil), "TTGiftCenter.GetSubOperateGiftResp")
	proto.RegisterType((*ModifyGiftRankReq)(nil), "TTGiftCenter.ModifyGiftRankReq")
	proto.RegisterType((*DispatchSubTTGiftReq)(nil), "TTGiftCenter.DispatchSubTTGiftReq")
	proto.RegisterType((*DispatchSubGiftResp)(nil), "TTGiftCenter.DispatchSubGiftResp")
	proto.RegisterType((*DispatchSubGuildGiftReq)(nil), "TTGiftCenter.DispatchSubGuildGiftReq")
	proto.RegisterType((*GetGameGiftAmountReq)(nil), "TTGiftCenter.GetGameGiftAmountReq")
	proto.RegisterType((*GameGiftAmount)(nil), "TTGiftCenter.GameGiftAmount")
	proto.RegisterType((*GetGameGiftAmountResp)(nil), "TTGiftCenter.GetGameGiftAmountResp")
	proto.RegisterType((*GetTTGiftPurchaseAuthReq)(nil), "TTGiftCenter.GetTTGiftPurchaseAuthReq")
	proto.RegisterType((*TTGiftPurchaseAuth)(nil), "TTGiftCenter.TTGiftPurchaseAuth")
	proto.RegisterType((*GetTTGiftPurchaseAuthResp)(nil), "TTGiftCenter.GetTTGiftPurchaseAuthResp")
	proto.RegisterType((*GetGuildGiftPurchaseAuthReq)(nil), "TTGiftCenter.GetGuildGiftPurchaseAuthReq")
	proto.RegisterType((*GetGuildGiftPurchaseAuthResp)(nil), "TTGiftCenter.GetGuildGiftPurchaseAuthResp")
	proto.RegisterType((*ExportSubGiftReq)(nil), "TTGiftCenter.ExportSubGiftReq")
	proto.RegisterType((*ExportSubGiftResp)(nil), "TTGiftCenter.ExportSubGiftResp")
	proto.RegisterType((*ClaimGiftReq)(nil), "TTGiftCenter.ClaimGiftReq")
	proto.RegisterType((*ClaimGiftResp)(nil), "TTGiftCenter.ClaimGiftResp")
	proto.RegisterType((*GetGiftDispatchRecordReq)(nil), "TTGiftCenter.GetGiftDispatchRecordReq")
	proto.RegisterType((*TTGiftDispatchInfo)(nil), "TTGiftCenter.TTGiftDispatchInfo")
	proto.RegisterType((*GetTTGiftDispatchRecordResp)(nil), "TTGiftCenter.GetTTGiftDispatchRecordResp")
	proto.RegisterType((*GuildGiftDispatchInfo)(nil), "TTGiftCenter.GuildGiftDispatchInfo")
	proto.RegisterType((*GetGuildGiftDispatchRecordResp)(nil), "TTGiftCenter.GetGuildGiftDispatchRecordResp")
	proto.RegisterType((*RecycleGiftReq)(nil), "TTGiftCenter.RecycleGiftReq")
	proto.RegisterType((*AddGiftActivityReq)(nil), "TTGiftCenter.AddGiftActivityReq")
	proto.RegisterType((*AddGiftActivityResp)(nil), "TTGiftCenter.AddGiftActivityResp")
	proto.RegisterType((*DelGiftActivityReq)(nil), "TTGiftCenter.DelGiftActivityReq")
	proto.RegisterType((*ModifyGiftActivityRankReq)(nil), "TTGiftCenter.ModifyGiftActivityRankReq")
	proto.RegisterType((*GiftActivity)(nil), "TTGiftCenter.GiftActivity")
	proto.RegisterType((*GetGiftActivityReq)(nil), "TTGiftCenter.GetGiftActivityReq")
	proto.RegisterType((*GetGiftActivityResp)(nil), "TTGiftCenter.GetGiftActivityResp")
	proto.RegisterType((*ActivityBanner)(nil), "TTGiftCenter.ActivityBanner")
	proto.RegisterType((*GetActivityBannerResp)(nil), "TTGiftCenter.GetActivityBannerResp")
	proto.RegisterType((*PurchaseTTGiftReq)(nil), "TTGiftCenter.PurchaseTTGiftReq")
	proto.RegisterType((*PurchaseTTGiftResp)(nil), "TTGiftCenter.PurchaseTTGiftResp")
	proto.RegisterType((*PurchaseGuildGiftReq)(nil), "TTGiftCenter.PurchaseGuildGiftReq")
	proto.RegisterType((*PurchaseGuildGiftResp)(nil), "TTGiftCenter.PurchaseGuildGiftResp")
	proto.RegisterType((*PickTTGiftReq)(nil), "TTGiftCenter.PickTTGiftReq")
	proto.RegisterType((*GetTTGiftDayLimitReq)(nil), "TTGiftCenter.GetTTGiftDayLimitReq")
	proto.RegisterType((*GetTTGiftDayLimitResp)(nil), "TTGiftCenter.GetTTGiftDayLimitResp")
	proto.RegisterEnum("TTGiftCenter.ActivityType", ActivityType_name, ActivityType_value)
	proto.RegisterEnum("TTGiftCenter.SubType", SubType_name, SubType_value)
	proto.RegisterEnum("TTGiftCenter.OrderByType", OrderByType_name, OrderByType_value)
	proto.RegisterEnum("TTGiftCenter.TTGiftPlatform", TTGiftPlatform_name, TTGiftPlatform_value)
	proto.RegisterEnum("TTGiftCenter.ESubTTGiftPurchaseCond", ESubTTGiftPurchaseCond_name, ESubTTGiftPurchaseCond_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for TTGiftCenter service

type TTGiftCenterClient interface {
	// ----------------------------------------------//
	// major gift part
	// ----------------------------------------------//
	// 创建主库礼物商品
	CreateProduct(ctx context.Context, in *CreateProductReq, opts ...grpc.CallOption) (*CreateProductResp, error)
	// 修改主库的礼物商品
	ModifyProduct(ctx context.Context, in *ModifyProductReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取主库的礼物商品列表
	SearchProduct(ctx context.Context, in *SearchProductReq, opts ...grpc.CallOption) (*SearchProductResp, error)
	// 将主库的指定礼物商品对应的物品分配给子库(在此之前需要在子库建立该商品的信息)
	DispatchItem(ctx context.Context, in *DispatchItemReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 为主库的指定礼物商品增加物品库存
	AddItem(ctx context.Context, in *AddItemReq, opts ...grpc.CallOption) (*AddItemResp, error)
	// 建立或更新 TT个人子库的商品信息
	UpdateSubTTGift(ctx context.Context, in *UpdateSubTTGiftReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 建立或更新 TT公会子库的商品信息
	UpdateSubGuildGift(ctx context.Context, in *UpdateSubGuildGiftReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取 TT个人子库的商品信息列表
	GetSubTTGift(ctx context.Context, in *GetSubGiftReq, opts ...grpc.CallOption) (*GetSubTTGiftResp, error)
	// 获取 TT公会子库的商品信息列表
	GetSubGuildGift(ctx context.Context, in *GetSubGiftReq, opts ...grpc.CallOption) (*GetSubGuildGiftResp, error)
	// 获取 活动子库的商品信息列表
	GetSubActivityGift(ctx context.Context, in *GetSubGiftReq, opts ...grpc.CallOption) (*GetSubActivityGiftResp, error)
	// 获取 Y包子库的商品信息列表
	GetSubYGift(ctx context.Context, in *GetSubGiftReq, opts ...grpc.CallOption) (*GetSubYGiftResp, error)
	// 修改子库商品的排序值
	ModifyGiftRank(ctx context.Context, in *ModifyGiftRankReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 发放 TT个人子库的商品对应的物品给个人(记录分配信息 返回分配结果)
	DispatchSubTTGift(ctx context.Context, in *DispatchSubTTGiftReq, opts ...grpc.CallOption) (*DispatchSubGiftResp, error)
	// 发放 TT公会子库的商品对应的物品给公会(记录分配信息 返回分配结果)
	DispatchSubGuildGift(ctx context.Context, in *DispatchSubGuildGiftReq, opts ...grpc.CallOption) (*DispatchSubGiftResp, error)
	GetGameGiftAmount(ctx context.Context, in *GetGameGiftAmountReq, opts ...grpc.CallOption) (*GetGameGiftAmountResp, error)
	GetTTGiftPurchaseAuth(ctx context.Context, in *GetTTGiftPurchaseAuthReq, opts ...grpc.CallOption) (*GetTTGiftPurchaseAuthResp, error)
	// 导出 指定子库的礼物商品的指定数量的物品 并将该物品设置为已经发放
	ExportSubGift(ctx context.Context, in *ExportSubGiftReq, opts ...grpc.CallOption) (*ExportSubGiftResp, error)
	GetGuildGiftPurchaseAuth(ctx context.Context, in *GetGuildGiftPurchaseAuthReq, opts ...grpc.CallOption) (*GetGuildGiftPurchaseAuthResp, error)
	ClaimTTGift(ctx context.Context, in *ClaimGiftReq, opts ...grpc.CallOption) (*ClaimGiftResp, error)
	ClaimGuildGift(ctx context.Context, in *ClaimGiftReq, opts ...grpc.CallOption) (*ClaimGiftResp, error)
	GetTTGiftDispatchRecord(ctx context.Context, in *GetGiftDispatchRecordReq, opts ...grpc.CallOption) (*GetTTGiftDispatchRecordResp, error)
	GetGuildGiftDispatchRecord(ctx context.Context, in *GetGiftDispatchRecordReq, opts ...grpc.CallOption) (*GetGuildGiftDispatchRecordResp, error)
	RecycleGift(ctx context.Context, in *RecycleGiftReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 发放 TT个人子库的商品对应的物品给个人(记录分配信息 返回分配结果)
	DispatchSubTTGiftForActivity(ctx context.Context, in *DispatchSubTTGiftReq, opts ...grpc.CallOption) (*DispatchSubGiftResp, error)
	// 发放 TT公会子库的商品对应的物品给公会(记录分配信息 返回分配结果)
	DispatchSubGuildGiftForActivity(ctx context.Context, in *DispatchSubGuildGiftReq, opts ...grpc.CallOption) (*DispatchSubGiftResp, error)
	// 获取 运营子库的商品信息列表
	GetSubOperateGift(ctx context.Context, in *GetSubGiftReq, opts ...grpc.CallOption) (*GetSubOperateGiftResp, error)
	// ----------------------------------------------//
	// gift avtivity part 礼包活动相关设置
	// ----------------------------------------------//
	AddGiftActivity(ctx context.Context, in *AddGiftActivityReq, opts ...grpc.CallOption) (*AddGiftActivityResp, error)
	DelGiftActivity(ctx context.Context, in *DelGiftActivityReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ModifyGiftActivityRank(ctx context.Context, in *ModifyGiftActivityRankReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGiftActivity(ctx context.Context, in *GetGiftActivityReq, opts ...grpc.CallOption) (*GetGiftActivityResp, error)
	GetActivityBanner(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetActivityBannerResp, error)
	// ----------------------------------------------//
	// purchase and pick
	// ----------------------------------------------//
	// 购买TT子库的礼包
	PurchaseTTGift(ctx context.Context, in *PurchaseTTGiftReq, opts ...grpc.CallOption) (*PurchaseTTGiftResp, error)
	// 购买TT公会子库的礼包
	PurchaseGuildGift(ctx context.Context, in *PurchaseGuildGiftReq, opts ...grpc.CallOption) (*PurchaseGuildGiftResp, error)
	// 淘号
	PickTTGift(ctx context.Context, in *PickTTGiftReq, opts ...grpc.CallOption) (*PurchaseTTGiftResp, error)
	// 建立 Y包子库 或者 活动子库 的礼物商品信息
	AddSubYorActGiftInfo(ctx context.Context, in *AddSubYorActGiftReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 根据product id批量获取主库商品信息
	BatchGetProduct(ctx context.Context, in *BatchGetProductReq, opts ...grpc.CallOption) (*BatchGetProductResp, error)
	// 购买TT子库的礼包
	SDKApplyTTGift(ctx context.Context, in *PurchaseTTGiftReq, opts ...grpc.CallOption) (*PurchaseTTGiftResp, error)
	// 购买TT子库的礼包
	GetTTGiftDayLimit(ctx context.Context, in *GetTTGiftDayLimitReq, opts ...grpc.CallOption) (*GetTTGiftDayLimitResp, error)
}

type tTGiftCenterClient struct {
	cc *grpc.ClientConn
}

func NewTTGiftCenterClient(cc *grpc.ClientConn) TTGiftCenterClient {
	return &tTGiftCenterClient{cc}
}

func (c *tTGiftCenterClient) CreateProduct(ctx context.Context, in *CreateProductReq, opts ...grpc.CallOption) (*CreateProductResp, error) {
	out := new(CreateProductResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/CreateProduct", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) ModifyProduct(ctx context.Context, in *ModifyProductReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/ModifyProduct", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) SearchProduct(ctx context.Context, in *SearchProductReq, opts ...grpc.CallOption) (*SearchProductResp, error) {
	out := new(SearchProductResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/SearchProduct", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) DispatchItem(ctx context.Context, in *DispatchItemReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/DispatchItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) AddItem(ctx context.Context, in *AddItemReq, opts ...grpc.CallOption) (*AddItemResp, error) {
	out := new(AddItemResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/AddItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) UpdateSubTTGift(ctx context.Context, in *UpdateSubTTGiftReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/UpdateSubTTGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) UpdateSubGuildGift(ctx context.Context, in *UpdateSubGuildGiftReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/UpdateSubGuildGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) GetSubTTGift(ctx context.Context, in *GetSubGiftReq, opts ...grpc.CallOption) (*GetSubTTGiftResp, error) {
	out := new(GetSubTTGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/GetSubTTGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) GetSubGuildGift(ctx context.Context, in *GetSubGiftReq, opts ...grpc.CallOption) (*GetSubGuildGiftResp, error) {
	out := new(GetSubGuildGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/GetSubGuildGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) GetSubActivityGift(ctx context.Context, in *GetSubGiftReq, opts ...grpc.CallOption) (*GetSubActivityGiftResp, error) {
	out := new(GetSubActivityGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/GetSubActivityGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) GetSubYGift(ctx context.Context, in *GetSubGiftReq, opts ...grpc.CallOption) (*GetSubYGiftResp, error) {
	out := new(GetSubYGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/GetSubYGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) ModifyGiftRank(ctx context.Context, in *ModifyGiftRankReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/ModifyGiftRank", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) DispatchSubTTGift(ctx context.Context, in *DispatchSubTTGiftReq, opts ...grpc.CallOption) (*DispatchSubGiftResp, error) {
	out := new(DispatchSubGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/DispatchSubTTGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) DispatchSubGuildGift(ctx context.Context, in *DispatchSubGuildGiftReq, opts ...grpc.CallOption) (*DispatchSubGiftResp, error) {
	out := new(DispatchSubGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/DispatchSubGuildGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) GetGameGiftAmount(ctx context.Context, in *GetGameGiftAmountReq, opts ...grpc.CallOption) (*GetGameGiftAmountResp, error) {
	out := new(GetGameGiftAmountResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/GetGameGiftAmount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) GetTTGiftPurchaseAuth(ctx context.Context, in *GetTTGiftPurchaseAuthReq, opts ...grpc.CallOption) (*GetTTGiftPurchaseAuthResp, error) {
	out := new(GetTTGiftPurchaseAuthResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/GetTTGiftPurchaseAuth", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) ExportSubGift(ctx context.Context, in *ExportSubGiftReq, opts ...grpc.CallOption) (*ExportSubGiftResp, error) {
	out := new(ExportSubGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/ExportSubGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) GetGuildGiftPurchaseAuth(ctx context.Context, in *GetGuildGiftPurchaseAuthReq, opts ...grpc.CallOption) (*GetGuildGiftPurchaseAuthResp, error) {
	out := new(GetGuildGiftPurchaseAuthResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/GetGuildGiftPurchaseAuth", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) ClaimTTGift(ctx context.Context, in *ClaimGiftReq, opts ...grpc.CallOption) (*ClaimGiftResp, error) {
	out := new(ClaimGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/ClaimTTGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) ClaimGuildGift(ctx context.Context, in *ClaimGiftReq, opts ...grpc.CallOption) (*ClaimGiftResp, error) {
	out := new(ClaimGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/ClaimGuildGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) GetTTGiftDispatchRecord(ctx context.Context, in *GetGiftDispatchRecordReq, opts ...grpc.CallOption) (*GetTTGiftDispatchRecordResp, error) {
	out := new(GetTTGiftDispatchRecordResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/GetTTGiftDispatchRecord", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) GetGuildGiftDispatchRecord(ctx context.Context, in *GetGiftDispatchRecordReq, opts ...grpc.CallOption) (*GetGuildGiftDispatchRecordResp, error) {
	out := new(GetGuildGiftDispatchRecordResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/GetGuildGiftDispatchRecord", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) RecycleGift(ctx context.Context, in *RecycleGiftReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/RecycleGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) DispatchSubTTGiftForActivity(ctx context.Context, in *DispatchSubTTGiftReq, opts ...grpc.CallOption) (*DispatchSubGiftResp, error) {
	out := new(DispatchSubGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/DispatchSubTTGiftForActivity", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) DispatchSubGuildGiftForActivity(ctx context.Context, in *DispatchSubGuildGiftReq, opts ...grpc.CallOption) (*DispatchSubGiftResp, error) {
	out := new(DispatchSubGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/DispatchSubGuildGiftForActivity", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) GetSubOperateGift(ctx context.Context, in *GetSubGiftReq, opts ...grpc.CallOption) (*GetSubOperateGiftResp, error) {
	out := new(GetSubOperateGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/GetSubOperateGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) AddGiftActivity(ctx context.Context, in *AddGiftActivityReq, opts ...grpc.CallOption) (*AddGiftActivityResp, error) {
	out := new(AddGiftActivityResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/AddGiftActivity", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) DelGiftActivity(ctx context.Context, in *DelGiftActivityReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/DelGiftActivity", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) ModifyGiftActivityRank(ctx context.Context, in *ModifyGiftActivityRankReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/ModifyGiftActivityRank", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) GetGiftActivity(ctx context.Context, in *GetGiftActivityReq, opts ...grpc.CallOption) (*GetGiftActivityResp, error) {
	out := new(GetGiftActivityResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/GetGiftActivity", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) GetActivityBanner(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetActivityBannerResp, error) {
	out := new(GetActivityBannerResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/GetActivityBanner", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) PurchaseTTGift(ctx context.Context, in *PurchaseTTGiftReq, opts ...grpc.CallOption) (*PurchaseTTGiftResp, error) {
	out := new(PurchaseTTGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/PurchaseTTGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) PurchaseGuildGift(ctx context.Context, in *PurchaseGuildGiftReq, opts ...grpc.CallOption) (*PurchaseGuildGiftResp, error) {
	out := new(PurchaseGuildGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/PurchaseGuildGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) PickTTGift(ctx context.Context, in *PickTTGiftReq, opts ...grpc.CallOption) (*PurchaseTTGiftResp, error) {
	out := new(PurchaseTTGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/PickTTGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) AddSubYorActGiftInfo(ctx context.Context, in *AddSubYorActGiftReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/AddSubYorActGiftInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) BatchGetProduct(ctx context.Context, in *BatchGetProductReq, opts ...grpc.CallOption) (*BatchGetProductResp, error) {
	out := new(BatchGetProductResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/BatchGetProduct", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) SDKApplyTTGift(ctx context.Context, in *PurchaseTTGiftReq, opts ...grpc.CallOption) (*PurchaseTTGiftResp, error) {
	out := new(PurchaseTTGiftResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/SDKApplyTTGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTGiftCenterClient) GetTTGiftDayLimit(ctx context.Context, in *GetTTGiftDayLimitReq, opts ...grpc.CallOption) (*GetTTGiftDayLimitResp, error) {
	out := new(GetTTGiftDayLimitResp)
	err := grpc.Invoke(ctx, "/TTGiftCenter.TTGiftCenter/GetTTGiftDayLimit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for TTGiftCenter service

type TTGiftCenterServer interface {
	// ----------------------------------------------//
	// major gift part
	// ----------------------------------------------//
	// 创建主库礼物商品
	CreateProduct(context.Context, *CreateProductReq) (*CreateProductResp, error)
	// 修改主库的礼物商品
	ModifyProduct(context.Context, *ModifyProductReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取主库的礼物商品列表
	SearchProduct(context.Context, *SearchProductReq) (*SearchProductResp, error)
	// 将主库的指定礼物商品对应的物品分配给子库(在此之前需要在子库建立该商品的信息)
	DispatchItem(context.Context, *DispatchItemReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 为主库的指定礼物商品增加物品库存
	AddItem(context.Context, *AddItemReq) (*AddItemResp, error)
	// 建立或更新 TT个人子库的商品信息
	UpdateSubTTGift(context.Context, *UpdateSubTTGiftReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 建立或更新 TT公会子库的商品信息
	UpdateSubGuildGift(context.Context, *UpdateSubGuildGiftReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取 TT个人子库的商品信息列表
	GetSubTTGift(context.Context, *GetSubGiftReq) (*GetSubTTGiftResp, error)
	// 获取 TT公会子库的商品信息列表
	GetSubGuildGift(context.Context, *GetSubGiftReq) (*GetSubGuildGiftResp, error)
	// 获取 活动子库的商品信息列表
	GetSubActivityGift(context.Context, *GetSubGiftReq) (*GetSubActivityGiftResp, error)
	// 获取 Y包子库的商品信息列表
	GetSubYGift(context.Context, *GetSubGiftReq) (*GetSubYGiftResp, error)
	// 修改子库商品的排序值
	ModifyGiftRank(context.Context, *ModifyGiftRankReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 发放 TT个人子库的商品对应的物品给个人(记录分配信息 返回分配结果)
	DispatchSubTTGift(context.Context, *DispatchSubTTGiftReq) (*DispatchSubGiftResp, error)
	// 发放 TT公会子库的商品对应的物品给公会(记录分配信息 返回分配结果)
	DispatchSubGuildGift(context.Context, *DispatchSubGuildGiftReq) (*DispatchSubGiftResp, error)
	GetGameGiftAmount(context.Context, *GetGameGiftAmountReq) (*GetGameGiftAmountResp, error)
	GetTTGiftPurchaseAuth(context.Context, *GetTTGiftPurchaseAuthReq) (*GetTTGiftPurchaseAuthResp, error)
	// 导出 指定子库的礼物商品的指定数量的物品 并将该物品设置为已经发放
	ExportSubGift(context.Context, *ExportSubGiftReq) (*ExportSubGiftResp, error)
	GetGuildGiftPurchaseAuth(context.Context, *GetGuildGiftPurchaseAuthReq) (*GetGuildGiftPurchaseAuthResp, error)
	ClaimTTGift(context.Context, *ClaimGiftReq) (*ClaimGiftResp, error)
	ClaimGuildGift(context.Context, *ClaimGiftReq) (*ClaimGiftResp, error)
	GetTTGiftDispatchRecord(context.Context, *GetGiftDispatchRecordReq) (*GetTTGiftDispatchRecordResp, error)
	GetGuildGiftDispatchRecord(context.Context, *GetGiftDispatchRecordReq) (*GetGuildGiftDispatchRecordResp, error)
	RecycleGift(context.Context, *RecycleGiftReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 发放 TT个人子库的商品对应的物品给个人(记录分配信息 返回分配结果)
	DispatchSubTTGiftForActivity(context.Context, *DispatchSubTTGiftReq) (*DispatchSubGiftResp, error)
	// 发放 TT公会子库的商品对应的物品给公会(记录分配信息 返回分配结果)
	DispatchSubGuildGiftForActivity(context.Context, *DispatchSubGuildGiftReq) (*DispatchSubGiftResp, error)
	// 获取 运营子库的商品信息列表
	GetSubOperateGift(context.Context, *GetSubGiftReq) (*GetSubOperateGiftResp, error)
	// ----------------------------------------------//
	// gift avtivity part 礼包活动相关设置
	// ----------------------------------------------//
	AddGiftActivity(context.Context, *AddGiftActivityReq) (*AddGiftActivityResp, error)
	DelGiftActivity(context.Context, *DelGiftActivityReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ModifyGiftActivityRank(context.Context, *ModifyGiftActivityRankReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGiftActivity(context.Context, *GetGiftActivityReq) (*GetGiftActivityResp, error)
	GetActivityBanner(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetActivityBannerResp, error)
	// ----------------------------------------------//
	// purchase and pick
	// ----------------------------------------------//
	// 购买TT子库的礼包
	PurchaseTTGift(context.Context, *PurchaseTTGiftReq) (*PurchaseTTGiftResp, error)
	// 购买TT公会子库的礼包
	PurchaseGuildGift(context.Context, *PurchaseGuildGiftReq) (*PurchaseGuildGiftResp, error)
	// 淘号
	PickTTGift(context.Context, *PickTTGiftReq) (*PurchaseTTGiftResp, error)
	// 建立 Y包子库 或者 活动子库 的礼物商品信息
	AddSubYorActGiftInfo(context.Context, *AddSubYorActGiftReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 根据product id批量获取主库商品信息
	BatchGetProduct(context.Context, *BatchGetProductReq) (*BatchGetProductResp, error)
	// 购买TT子库的礼包
	SDKApplyTTGift(context.Context, *PurchaseTTGiftReq) (*PurchaseTTGiftResp, error)
	// 购买TT子库的礼包
	GetTTGiftDayLimit(context.Context, *GetTTGiftDayLimitReq) (*GetTTGiftDayLimitResp, error)
}

func RegisterTTGiftCenterServer(s *grpc.Server, srv TTGiftCenterServer) {
	s.RegisterService(&_TTGiftCenter_serviceDesc, srv)
}

func _TTGiftCenter_CreateProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).CreateProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/CreateProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).CreateProduct(ctx, req.(*CreateProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_ModifyProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).ModifyProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/ModifyProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).ModifyProduct(ctx, req.(*ModifyProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_SearchProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).SearchProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/SearchProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).SearchProduct(ctx, req.(*SearchProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_DispatchItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DispatchItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).DispatchItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/DispatchItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).DispatchItem(ctx, req.(*DispatchItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_AddItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).AddItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/AddItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).AddItem(ctx, req.(*AddItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_UpdateSubTTGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSubTTGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).UpdateSubTTGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/UpdateSubTTGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).UpdateSubTTGift(ctx, req.(*UpdateSubTTGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_UpdateSubGuildGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSubGuildGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).UpdateSubGuildGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/UpdateSubGuildGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).UpdateSubGuildGift(ctx, req.(*UpdateSubGuildGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_GetSubTTGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).GetSubTTGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/GetSubTTGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).GetSubTTGift(ctx, req.(*GetSubGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_GetSubGuildGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).GetSubGuildGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/GetSubGuildGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).GetSubGuildGift(ctx, req.(*GetSubGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_GetSubActivityGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).GetSubActivityGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/GetSubActivityGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).GetSubActivityGift(ctx, req.(*GetSubGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_GetSubYGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).GetSubYGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/GetSubYGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).GetSubYGift(ctx, req.(*GetSubGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_ModifyGiftRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyGiftRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).ModifyGiftRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/ModifyGiftRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).ModifyGiftRank(ctx, req.(*ModifyGiftRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_DispatchSubTTGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DispatchSubTTGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).DispatchSubTTGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/DispatchSubTTGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).DispatchSubTTGift(ctx, req.(*DispatchSubTTGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_DispatchSubGuildGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DispatchSubGuildGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).DispatchSubGuildGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/DispatchSubGuildGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).DispatchSubGuildGift(ctx, req.(*DispatchSubGuildGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_GetGameGiftAmount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameGiftAmountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).GetGameGiftAmount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/GetGameGiftAmount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).GetGameGiftAmount(ctx, req.(*GetGameGiftAmountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_GetTTGiftPurchaseAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTTGiftPurchaseAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).GetTTGiftPurchaseAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/GetTTGiftPurchaseAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).GetTTGiftPurchaseAuth(ctx, req.(*GetTTGiftPurchaseAuthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_ExportSubGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportSubGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).ExportSubGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/ExportSubGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).ExportSubGift(ctx, req.(*ExportSubGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_GetGuildGiftPurchaseAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildGiftPurchaseAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).GetGuildGiftPurchaseAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/GetGuildGiftPurchaseAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).GetGuildGiftPurchaseAuth(ctx, req.(*GetGuildGiftPurchaseAuthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_ClaimTTGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClaimGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).ClaimTTGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/ClaimTTGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).ClaimTTGift(ctx, req.(*ClaimGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_ClaimGuildGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClaimGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).ClaimGuildGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/ClaimGuildGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).ClaimGuildGift(ctx, req.(*ClaimGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_GetTTGiftDispatchRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGiftDispatchRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).GetTTGiftDispatchRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/GetTTGiftDispatchRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).GetTTGiftDispatchRecord(ctx, req.(*GetGiftDispatchRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_GetGuildGiftDispatchRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGiftDispatchRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).GetGuildGiftDispatchRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/GetGuildGiftDispatchRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).GetGuildGiftDispatchRecord(ctx, req.(*GetGiftDispatchRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_RecycleGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecycleGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).RecycleGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/RecycleGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).RecycleGift(ctx, req.(*RecycleGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_DispatchSubTTGiftForActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DispatchSubTTGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).DispatchSubTTGiftForActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/DispatchSubTTGiftForActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).DispatchSubTTGiftForActivity(ctx, req.(*DispatchSubTTGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_DispatchSubGuildGiftForActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DispatchSubGuildGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).DispatchSubGuildGiftForActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/DispatchSubGuildGiftForActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).DispatchSubGuildGiftForActivity(ctx, req.(*DispatchSubGuildGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_GetSubOperateGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).GetSubOperateGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/GetSubOperateGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).GetSubOperateGift(ctx, req.(*GetSubGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_AddGiftActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddGiftActivityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).AddGiftActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/AddGiftActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).AddGiftActivity(ctx, req.(*AddGiftActivityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_DelGiftActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelGiftActivityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).DelGiftActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/DelGiftActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).DelGiftActivity(ctx, req.(*DelGiftActivityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_ModifyGiftActivityRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyGiftActivityRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).ModifyGiftActivityRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/ModifyGiftActivityRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).ModifyGiftActivityRank(ctx, req.(*ModifyGiftActivityRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_GetGiftActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGiftActivityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).GetGiftActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/GetGiftActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).GetGiftActivity(ctx, req.(*GetGiftActivityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_GetActivityBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).GetActivityBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/GetActivityBanner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).GetActivityBanner(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_PurchaseTTGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PurchaseTTGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).PurchaseTTGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/PurchaseTTGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).PurchaseTTGift(ctx, req.(*PurchaseTTGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_PurchaseGuildGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PurchaseGuildGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).PurchaseGuildGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/PurchaseGuildGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).PurchaseGuildGift(ctx, req.(*PurchaseGuildGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_PickTTGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PickTTGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).PickTTGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/PickTTGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).PickTTGift(ctx, req.(*PickTTGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_AddSubYorActGiftInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSubYorActGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).AddSubYorActGiftInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/AddSubYorActGiftInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).AddSubYorActGiftInfo(ctx, req.(*AddSubYorActGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_BatchGetProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).BatchGetProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/BatchGetProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).BatchGetProduct(ctx, req.(*BatchGetProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_SDKApplyTTGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PurchaseTTGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).SDKApplyTTGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/SDKApplyTTGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).SDKApplyTTGift(ctx, req.(*PurchaseTTGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTGiftCenter_GetTTGiftDayLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTTGiftDayLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTGiftCenterServer).GetTTGiftDayLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/TTGiftCenter.TTGiftCenter/GetTTGiftDayLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTGiftCenterServer).GetTTGiftDayLimit(ctx, req.(*GetTTGiftDayLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _TTGiftCenter_serviceDesc = grpc.ServiceDesc{
	ServiceName: "TTGiftCenter.TTGiftCenter",
	HandlerType: (*TTGiftCenterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateProduct",
			Handler:    _TTGiftCenter_CreateProduct_Handler,
		},
		{
			MethodName: "ModifyProduct",
			Handler:    _TTGiftCenter_ModifyProduct_Handler,
		},
		{
			MethodName: "SearchProduct",
			Handler:    _TTGiftCenter_SearchProduct_Handler,
		},
		{
			MethodName: "DispatchItem",
			Handler:    _TTGiftCenter_DispatchItem_Handler,
		},
		{
			MethodName: "AddItem",
			Handler:    _TTGiftCenter_AddItem_Handler,
		},
		{
			MethodName: "UpdateSubTTGift",
			Handler:    _TTGiftCenter_UpdateSubTTGift_Handler,
		},
		{
			MethodName: "UpdateSubGuildGift",
			Handler:    _TTGiftCenter_UpdateSubGuildGift_Handler,
		},
		{
			MethodName: "GetSubTTGift",
			Handler:    _TTGiftCenter_GetSubTTGift_Handler,
		},
		{
			MethodName: "GetSubGuildGift",
			Handler:    _TTGiftCenter_GetSubGuildGift_Handler,
		},
		{
			MethodName: "GetSubActivityGift",
			Handler:    _TTGiftCenter_GetSubActivityGift_Handler,
		},
		{
			MethodName: "GetSubYGift",
			Handler:    _TTGiftCenter_GetSubYGift_Handler,
		},
		{
			MethodName: "ModifyGiftRank",
			Handler:    _TTGiftCenter_ModifyGiftRank_Handler,
		},
		{
			MethodName: "DispatchSubTTGift",
			Handler:    _TTGiftCenter_DispatchSubTTGift_Handler,
		},
		{
			MethodName: "DispatchSubGuildGift",
			Handler:    _TTGiftCenter_DispatchSubGuildGift_Handler,
		},
		{
			MethodName: "GetGameGiftAmount",
			Handler:    _TTGiftCenter_GetGameGiftAmount_Handler,
		},
		{
			MethodName: "GetTTGiftPurchaseAuth",
			Handler:    _TTGiftCenter_GetTTGiftPurchaseAuth_Handler,
		},
		{
			MethodName: "ExportSubGift",
			Handler:    _TTGiftCenter_ExportSubGift_Handler,
		},
		{
			MethodName: "GetGuildGiftPurchaseAuth",
			Handler:    _TTGiftCenter_GetGuildGiftPurchaseAuth_Handler,
		},
		{
			MethodName: "ClaimTTGift",
			Handler:    _TTGiftCenter_ClaimTTGift_Handler,
		},
		{
			MethodName: "ClaimGuildGift",
			Handler:    _TTGiftCenter_ClaimGuildGift_Handler,
		},
		{
			MethodName: "GetTTGiftDispatchRecord",
			Handler:    _TTGiftCenter_GetTTGiftDispatchRecord_Handler,
		},
		{
			MethodName: "GetGuildGiftDispatchRecord",
			Handler:    _TTGiftCenter_GetGuildGiftDispatchRecord_Handler,
		},
		{
			MethodName: "RecycleGift",
			Handler:    _TTGiftCenter_RecycleGift_Handler,
		},
		{
			MethodName: "DispatchSubTTGiftForActivity",
			Handler:    _TTGiftCenter_DispatchSubTTGiftForActivity_Handler,
		},
		{
			MethodName: "DispatchSubGuildGiftForActivity",
			Handler:    _TTGiftCenter_DispatchSubGuildGiftForActivity_Handler,
		},
		{
			MethodName: "GetSubOperateGift",
			Handler:    _TTGiftCenter_GetSubOperateGift_Handler,
		},
		{
			MethodName: "AddGiftActivity",
			Handler:    _TTGiftCenter_AddGiftActivity_Handler,
		},
		{
			MethodName: "DelGiftActivity",
			Handler:    _TTGiftCenter_DelGiftActivity_Handler,
		},
		{
			MethodName: "ModifyGiftActivityRank",
			Handler:    _TTGiftCenter_ModifyGiftActivityRank_Handler,
		},
		{
			MethodName: "GetGiftActivity",
			Handler:    _TTGiftCenter_GetGiftActivity_Handler,
		},
		{
			MethodName: "GetActivityBanner",
			Handler:    _TTGiftCenter_GetActivityBanner_Handler,
		},
		{
			MethodName: "PurchaseTTGift",
			Handler:    _TTGiftCenter_PurchaseTTGift_Handler,
		},
		{
			MethodName: "PurchaseGuildGift",
			Handler:    _TTGiftCenter_PurchaseGuildGift_Handler,
		},
		{
			MethodName: "PickTTGift",
			Handler:    _TTGiftCenter_PickTTGift_Handler,
		},
		{
			MethodName: "AddSubYorActGiftInfo",
			Handler:    _TTGiftCenter_AddSubYorActGiftInfo_Handler,
		},
		{
			MethodName: "BatchGetProduct",
			Handler:    _TTGiftCenter_BatchGetProduct_Handler,
		},
		{
			MethodName: "SDKApplyTTGift",
			Handler:    _TTGiftCenter_SDKApplyTTGift_Handler,
		},
		{
			MethodName: "GetTTGiftDayLimit",
			Handler:    _TTGiftCenter_GetTTGiftDayLimit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/ttgiftcentersvr/ttgiftcenter.proto",
}

func (m *ProductItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProductItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.StorageId))
	if m.ItemBinary != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.ItemBinary)))
		i += copy(dAtA[i:], m.ItemBinary)
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *CreateProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x12
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Description)))
	i += copy(dAtA[i:], m.Description)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.UsageDesc)))
	i += copy(dAtA[i:], m.UsageDesc)
	dAtA[i] = 0x20
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ItemType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GiftType))
	dAtA[i] = 0x38
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ExchangeSDate))
	dAtA[i] = 0x40
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ExchangeEDate))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Operator)))
	i += copy(dAtA[i:], m.Operator)
	dAtA[i] = 0x50
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *CreateProductResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateProductResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *ModifyProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Description)))
	i += copy(dAtA[i:], m.Description)
	dAtA[i] = 0x22
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.UsageDesc)))
	i += copy(dAtA[i:], m.UsageDesc)
	dAtA[i] = 0x28
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GiftType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ExchangeSDate))
	dAtA[i] = 0x38
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ExchangeEDate))
	dAtA[i] = 0x42
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Operator)))
	i += copy(dAtA[i:], m.Operator)
	dAtA[i] = 0x48
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *Product) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Product) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Description)))
	i += copy(dAtA[i:], m.Description)
	dAtA[i] = 0x22
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.UsageDesc)))
	i += copy(dAtA[i:], m.UsageDesc)
	dAtA[i] = 0x28
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ItemType))
	dAtA[i] = 0x38
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GiftType))
	dAtA[i] = 0x40
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ExchangeSDate))
	dAtA[i] = 0x48
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ExchangeEDate))
	dAtA[i] = 0x50
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Left))
	dAtA[i] = 0x58
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Stock))
	dAtA[i] = 0x60
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x68
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *SearchProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SearchProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GiftType))
	dAtA[i] = 0x22
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x28
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.CreateAfterTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.CreateBeforeTime))
	dAtA[i] = 0x38
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.OrderByType))
	dAtA[i] = 0x40
	i++
	if m.Desc {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x48
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.LimitStartIdx))
	dAtA[i] = 0x50
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.LimitCount))
	dAtA[i] = 0x58
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *SearchProductResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SearchProductResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductList) > 0 {
		for _, msg := range m.ProductList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *BatchGetProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductidList) > 0 {
		for _, num := range m.ProductidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetProductResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetProductResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductList) > 0 {
		for _, msg := range m.ProductList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DispatchItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DispatchItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.SubType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Amount))
	dAtA[i] = 0x22
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Operator)))
	i += copy(dAtA[i:], m.Operator)
	return i, nil
}

func (m *AccountPwdItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AccountPwdItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x12
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Pwd)))
	i += copy(dAtA[i:], m.Pwd)
	return i, nil
}

func (m *ExchangeCardItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExchangeCardItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Code)))
	i += copy(dAtA[i:], m.Code)
	return i, nil
}

func (m *AddItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	if len(m.ItemBinaryList) > 0 {
		for _, b := range m.ItemBinaryList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(b)))
			i += copy(dAtA[i:], b)
		}
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Operator)))
	i += copy(dAtA[i:], m.Operator)
	if len(m.ItemHashList) > 0 {
		for _, s := range m.ItemHashList {
			dAtA[i] = 0x22
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *AddItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SuccessItems) > 0 {
		for _, b := range m.SuccessItems {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(b)))
			i += copy(dAtA[i:], b)
		}
	}
	if len(m.FailedItems) > 0 {
		for _, b := range m.FailedItems {
			dAtA[i] = 0x12
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(b)))
			i += copy(dAtA[i:], b)
		}
	}
	return i, nil
}

func (m *UpdateSubTTGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateSubTTGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Price))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.LevelMin))
	dAtA[i] = 0x28
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.LevelMax))
	dAtA[i] = 0x30
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.PurchaseLimit))
	dAtA[i] = 0x38
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.PurchaseCondition))
	dAtA[i] = 0x40
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.DayLimit))
	dAtA[i] = 0x48
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.HourLimit))
	dAtA[i] = 0x50
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.BuySTime))
	dAtA[i] = 0x58
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.BuyETime))
	dAtA[i] = 0x60
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.PickAble))
	dAtA[i] = 0x6a
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Operator)))
	i += copy(dAtA[i:], m.Operator)
	dAtA[i] = 0x70
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.PickSTime))
	dAtA[i] = 0x78
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.PickETime))
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.RegisterIn))
	return i, nil
}

func (m *UpdateSubGuildGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateSubGuildGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Price))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.LevelMin))
	dAtA[i] = 0x28
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.LevelMax))
	dAtA[i] = 0x30
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.PurchaseLimit))
	dAtA[i] = 0x38
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.BuySTime))
	dAtA[i] = 0x40
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.BuyETime))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Operator)))
	i += copy(dAtA[i:], m.Operator)
	return i, nil
}

func (m *AddSubYorActGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddSubYorActGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Operator)))
	i += copy(dAtA[i:], m.Operator)
	dAtA[i] = 0x20
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.SubType))
	return i, nil
}

func (m *GetSubGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSubGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x20
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.CreateAfterTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.CreateBeforeTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x38
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.OrderByType))
	dAtA[i] = 0x40
	i++
	if m.Desc {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x48
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.LimitStart))
	dAtA[i] = 0x50
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.LimitEnd))
	dAtA[i] = 0x58
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GiftType))
	dAtA[i] = 0x60
	i++
	if m.IsNeedExpire {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x68
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *SubTTGift) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SubTTGift) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Price))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Rank))
	dAtA[i] = 0x28
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.LevelMin))
	dAtA[i] = 0x30
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.LevelMax))
	dAtA[i] = 0x38
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.PurchaseLimit))
	dAtA[i] = 0x40
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.PurchaseCondition))
	dAtA[i] = 0x48
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.DayLimit))
	dAtA[i] = 0x50
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.HourLimit))
	dAtA[i] = 0x58
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.BuySTime))
	dAtA[i] = 0x60
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.BuyETime))
	dAtA[i] = 0x68
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.PickAble))
	dAtA[i] = 0x70
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.PickTimes))
	dAtA[i] = 0x78
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Left))
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Stock))
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.UpdateTime))
	if m.Product == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("product")
	} else {
		dAtA[i] = 0x92
		i++
		dAtA[i] = 0x1
		i++
		i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Product.Size()))
		n1, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x98
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.PickSTime))
	dAtA[i] = 0xa0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.PickETime))
	dAtA[i] = 0xa8
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.UnclaimedCount))
	dAtA[i] = 0xb0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.RegisterIn))
	return i, nil
}

func (m *GetSubTTGiftResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSubTTGiftResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TtGiftList) > 0 {
		for _, msg := range m.TtGiftList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *SubGuildGift) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SubGuildGift) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Price))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Rank))
	dAtA[i] = 0x28
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.LevelMin))
	dAtA[i] = 0x30
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.LevelMax))
	dAtA[i] = 0x38
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.PurchaseLimit))
	dAtA[i] = 0x40
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.BuySTime))
	dAtA[i] = 0x48
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.BuyETime))
	dAtA[i] = 0x50
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Left))
	dAtA[i] = 0x58
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Stock))
	dAtA[i] = 0x60
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.UpdateTime))
	if m.Product == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("product")
	} else {
		dAtA[i] = 0x6a
		i++
		i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Product.Size()))
		n2, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x70
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.UnclaimedCount))
	return i, nil
}

func (m *GetSubGuildGiftResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSubGuildGiftResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GuildGiftList) > 0 {
		for _, msg := range m.GuildGiftList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *ActivitGift) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivitGift) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Left))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Stock))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.UpdateTime))
	if m.Product == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("product")
	} else {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Product.Size()))
		n3, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.UnclaimedCount))
	return i, nil
}

func (m *GetSubActivityGiftResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSubActivityGiftResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ActivityGiftList) > 0 {
		for _, msg := range m.ActivityGiftList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *YGift) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *YGift) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Left))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Stock))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.UpdateTime))
	if m.Product == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("product")
	} else {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Product.Size()))
		n4, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *GetSubYGiftResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSubYGiftResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.YGiftList) > 0 {
		for _, msg := range m.YGiftList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *OperateGift) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OperateGift) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Left))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Stock))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.UpdateTime))
	if m.Product == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("product")
	} else {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Product.Size()))
		n5, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *GetSubOperateGiftResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSubOperateGiftResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OperateGiftList) > 0 {
		for _, msg := range m.OperateGiftList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *ModifyGiftRankReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyGiftRankReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.SubType))
	if len(m.ProductIds) > 0 {
		for _, num := range m.ProductIds {
			dAtA[i] = 0x10
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GiftType))
	return i, nil
}

func (m *DispatchSubTTGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DispatchSubTTGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Operator)))
	i += copy(dAtA[i:], m.Operator)
	return i, nil
}

func (m *DispatchSubGiftResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DispatchSubGiftResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Product == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("product")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Product.Size()))
		n6, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if len(m.ItemList) > 0 {
		for _, msg := range m.ItemList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DispatchSubGuildGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DispatchSubGuildGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x22
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Operator)))
	i += copy(dAtA[i:], m.Operator)
	return i, nil
}

func (m *GetGameGiftAmountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameGiftAmountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GameIdList) > 0 {
		for _, num := range m.GameIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.SubType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *GameGiftAmount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameGiftAmount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GiftAmount))
	return i, nil
}

func (m *GetGameGiftAmountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameGiftAmountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AmountList) > 0 {
		for _, msg := range m.AmountList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetTTGiftPurchaseAuthReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTTGiftPurchaseAuthReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Uid))
	if len(m.ProductIdList) > 0 {
		for _, num := range m.ProductIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *TTGiftPurchaseAuth) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TTGiftPurchaseAuth) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	if m.Auth {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.AlreadyPurchase))
	return i, nil
}

func (m *GetTTGiftPurchaseAuthResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTTGiftPurchaseAuthResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PurchaseAuthList) > 0 {
		for _, msg := range m.PurchaseAuthList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGuildGiftPurchaseAuthReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftPurchaseAuthReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GuildLevel))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.BuyNumber))
	return i, nil
}

func (m *GetGuildGiftPurchaseAuthResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftPurchaseAuthResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.BuyLimit))
	dAtA[i] = 0x10
	i++
	if m.LevelAuth {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ExportSubGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExportSubGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Amount))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.SubType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ExportToType))
	if len(m.ExportToIdList) > 0 {
		for _, num := range m.ExportToIdList {
			dAtA[i] = 0x28
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ExportToIdPerCnt))
	return i, nil
}

func (m *ExportSubGiftResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExportSubGiftResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ItemList) > 0 {
		for _, msg := range m.ItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ClaimGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClaimGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *ClaimGiftResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClaimGiftResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GiftList) > 0 {
		for _, msg := range m.GiftList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGiftDispatchRecordReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftDispatchRecordReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *TTGiftDispatchInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TTGiftDispatchInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.DispatchTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.DispatchCount))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x28
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ClaimTime))
	return i, nil
}

func (m *GetTTGiftDispatchRecordResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTTGiftDispatchRecordResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DispatchList) > 0 {
		for _, msg := range m.DispatchList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GuildGiftDispatchInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildGiftDispatchInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.DispatchTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.DispatchCount))
	return i, nil
}

func (m *GetGuildGiftDispatchRecordResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftDispatchRecordResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DispatchList) > 0 {
		for _, msg := range m.DispatchList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RecycleGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecycleGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *AddGiftActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddGiftActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ActivityType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.PicUrl)))
	i += copy(dAtA[i:], m.PicUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.JumpUrl)))
	i += copy(dAtA[i:], m.JumpUrl)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.IconUrl)))
	i += copy(dAtA[i:], m.IconUrl)
	dAtA[i] = 0x30
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x38
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.StartTime))
	dAtA[i] = 0x40
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Operator)))
	i += copy(dAtA[i:], m.Operator)
	return i, nil
}

func (m *AddGiftActivityResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddGiftActivityResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ActivityId))
	return i, nil
}

func (m *DelGiftActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelGiftActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Operator)))
	i += copy(dAtA[i:], m.Operator)
	return i, nil
}

func (m *ModifyGiftActivityRankReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyGiftActivityRankReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ActivityIdList) > 0 {
		for _, num := range m.ActivityIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GiftActivity) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GiftActivity) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ActivityType))
	dAtA[i] = 0x22
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.PicUrl)))
	i += copy(dAtA[i:], m.PicUrl)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.IconUrl)))
	i += copy(dAtA[i:], m.IconUrl)
	dAtA[i] = 0x32
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.JumpUrl)))
	i += copy(dAtA[i:], m.JumpUrl)
	dAtA[i] = 0x38
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.StartTime))
	dAtA[i] = 0x48
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x50
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Rank))
	dAtA[i] = 0x58
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.CreateTime))
	return i, nil
}

func (m *GetGiftActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.CreateAfterTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.CreateBeforeTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ActivityType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.OrderByType))
	dAtA[i] = 0x38
	i++
	if m.Desc {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x40
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.LimitStart))
	dAtA[i] = 0x48
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.LimitEnd))
	return i, nil
}

func (m *GetGiftActivityResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftActivityResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GiftActivityList) > 0 {
		for _, msg := range m.GiftActivityList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *ActivityBanner) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivityBanner) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.PicUrl)))
	i += copy(dAtA[i:], m.PicUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(len(m.JumpUrl)))
	i += copy(dAtA[i:], m.JumpUrl)
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ActivityId))
	return i, nil
}

func (m *GetActivityBannerResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActivityBannerResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.BannerList) > 0 {
		for _, msg := range m.BannerList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *PurchaseTTGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PurchaseTTGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *PurchaseTTGiftResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PurchaseTTGiftResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GiftInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("gift_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GiftInfo.Size()))
		n7, err := m.GiftInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if m.ItemInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("item_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ItemInfo.Size()))
		n8, err := m.ItemInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *PurchaseGuildGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PurchaseGuildGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *PurchaseGuildGiftResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PurchaseGuildGiftResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GiftInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("gift_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.GiftInfo.Size()))
		n9, err := m.GiftInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	if len(m.ItemList) > 0 {
		for _, msg := range m.ItemList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintTtgiftcenter(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *PickTTGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PickTTGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *GetTTGiftDayLimitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTTGiftDayLimitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *GetTTGiftDayLimitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTTGiftDayLimitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.Used))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTtgiftcenter(dAtA, i, uint64(m.TotalLimit))
	return i, nil
}

func encodeFixed64Ttgiftcenter(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Ttgiftcenter(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintTtgiftcenter(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ProductItem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.StorageId))
	if m.ItemBinary != nil {
		l = len(m.ItemBinary)
		n += 1 + l + sovTtgiftcenter(uint64(l))
	}
	n += 1 + sovTtgiftcenter(uint64(m.Uid))
	return n
}

func (m *CreateProductReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Name)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	l = len(m.Description)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	l = len(m.UsageDesc)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	n += 1 + sovTtgiftcenter(uint64(m.GameId))
	n += 1 + sovTtgiftcenter(uint64(m.ItemType))
	n += 1 + sovTtgiftcenter(uint64(m.GiftType))
	n += 1 + sovTtgiftcenter(uint64(m.ExchangeSDate))
	n += 1 + sovTtgiftcenter(uint64(m.ExchangeEDate))
	l = len(m.Operator)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	n += 1 + sovTtgiftcenter(uint64(m.Platform))
	return n
}

func (m *CreateProductResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	return n
}

func (m *ModifyProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	l = len(m.Name)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	l = len(m.Description)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	l = len(m.UsageDesc)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	n += 1 + sovTtgiftcenter(uint64(m.GiftType))
	n += 1 + sovTtgiftcenter(uint64(m.ExchangeSDate))
	n += 1 + sovTtgiftcenter(uint64(m.ExchangeEDate))
	l = len(m.Operator)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	n += 1 + sovTtgiftcenter(uint64(m.Platform))
	return n
}

func (m *Product) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	l = len(m.Name)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	l = len(m.Description)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	l = len(m.UsageDesc)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	n += 1 + sovTtgiftcenter(uint64(m.GameId))
	n += 1 + sovTtgiftcenter(uint64(m.ItemType))
	n += 1 + sovTtgiftcenter(uint64(m.GiftType))
	n += 1 + sovTtgiftcenter(uint64(m.ExchangeSDate))
	n += 1 + sovTtgiftcenter(uint64(m.ExchangeEDate))
	n += 1 + sovTtgiftcenter(uint64(m.Left))
	n += 1 + sovTtgiftcenter(uint64(m.Stock))
	n += 1 + sovTtgiftcenter(uint64(m.CreateTime))
	n += 1 + sovTtgiftcenter(uint64(m.Platform))
	return n
}

func (m *SearchProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.GameId))
	n += 1 + sovTtgiftcenter(uint64(m.GiftType))
	l = len(m.Name)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	n += 1 + sovTtgiftcenter(uint64(m.CreateAfterTime))
	n += 1 + sovTtgiftcenter(uint64(m.CreateBeforeTime))
	n += 1 + sovTtgiftcenter(uint64(m.OrderByType))
	n += 2
	n += 1 + sovTtgiftcenter(uint64(m.LimitStartIdx))
	n += 1 + sovTtgiftcenter(uint64(m.LimitCount))
	n += 1 + sovTtgiftcenter(uint64(m.Platform))
	return n
}

func (m *SearchProductResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductList) > 0 {
		for _, e := range m.ProductList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	n += 1 + sovTtgiftcenter(uint64(m.Total))
	return n
}

func (m *BatchGetProductReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductidList) > 0 {
		for _, e := range m.ProductidList {
			n += 1 + sovTtgiftcenter(uint64(e))
		}
	}
	return n
}

func (m *BatchGetProductResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductList) > 0 {
		for _, e := range m.ProductList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	return n
}

func (m *DispatchItemReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.SubType))
	n += 1 + sovTtgiftcenter(uint64(m.Amount))
	l = len(m.Operator)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	return n
}

func (m *AccountPwdItem) Size() (n int) {
	var l int
	_ = l
	l = len(m.Account)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	l = len(m.Pwd)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	return n
}

func (m *ExchangeCardItem) Size() (n int) {
	var l int
	_ = l
	l = len(m.Code)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	return n
}

func (m *AddItemReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	if len(m.ItemBinaryList) > 0 {
		for _, b := range m.ItemBinaryList {
			l = len(b)
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	l = len(m.Operator)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	if len(m.ItemHashList) > 0 {
		for _, s := range m.ItemHashList {
			l = len(s)
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	return n
}

func (m *AddItemResp) Size() (n int) {
	var l int
	_ = l
	if len(m.SuccessItems) > 0 {
		for _, b := range m.SuccessItems {
			l = len(b)
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	if len(m.FailedItems) > 0 {
		for _, b := range m.FailedItems {
			l = len(b)
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	return n
}

func (m *UpdateSubTTGiftReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.Price))
	n += 1 + sovTtgiftcenter(uint64(m.Status))
	n += 1 + sovTtgiftcenter(uint64(m.LevelMin))
	n += 1 + sovTtgiftcenter(uint64(m.LevelMax))
	n += 1 + sovTtgiftcenter(uint64(m.PurchaseLimit))
	n += 1 + sovTtgiftcenter(uint64(m.PurchaseCondition))
	n += 1 + sovTtgiftcenter(uint64(m.DayLimit))
	n += 1 + sovTtgiftcenter(uint64(m.HourLimit))
	n += 1 + sovTtgiftcenter(uint64(m.BuySTime))
	n += 1 + sovTtgiftcenter(uint64(m.BuyETime))
	n += 1 + sovTtgiftcenter(uint64(m.PickAble))
	l = len(m.Operator)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	n += 1 + sovTtgiftcenter(uint64(m.PickSTime))
	n += 1 + sovTtgiftcenter(uint64(m.PickETime))
	n += 2 + sovTtgiftcenter(uint64(m.RegisterIn))
	return n
}

func (m *UpdateSubGuildGiftReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.Price))
	n += 1 + sovTtgiftcenter(uint64(m.Status))
	n += 1 + sovTtgiftcenter(uint64(m.LevelMin))
	n += 1 + sovTtgiftcenter(uint64(m.LevelMax))
	n += 1 + sovTtgiftcenter(uint64(m.PurchaseLimit))
	n += 1 + sovTtgiftcenter(uint64(m.BuySTime))
	n += 1 + sovTtgiftcenter(uint64(m.BuyETime))
	l = len(m.Operator)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	return n
}

func (m *AddSubYorActGiftReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.Status))
	l = len(m.Operator)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	n += 1 + sovTtgiftcenter(uint64(m.SubType))
	return n
}

func (m *GetSubGiftReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.GameId))
	l = len(m.Name)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	n += 1 + sovTtgiftcenter(uint64(m.CreateAfterTime))
	n += 1 + sovTtgiftcenter(uint64(m.CreateBeforeTime))
	n += 1 + sovTtgiftcenter(uint64(m.Status))
	n += 1 + sovTtgiftcenter(uint64(m.OrderByType))
	n += 2
	n += 1 + sovTtgiftcenter(uint64(m.LimitStart))
	n += 1 + sovTtgiftcenter(uint64(m.LimitEnd))
	n += 1 + sovTtgiftcenter(uint64(m.GiftType))
	n += 2
	n += 1 + sovTtgiftcenter(uint64(m.Platform))
	return n
}

func (m *SubTTGift) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.Price))
	n += 1 + sovTtgiftcenter(uint64(m.Status))
	n += 1 + sovTtgiftcenter(uint64(m.Rank))
	n += 1 + sovTtgiftcenter(uint64(m.LevelMin))
	n += 1 + sovTtgiftcenter(uint64(m.LevelMax))
	n += 1 + sovTtgiftcenter(uint64(m.PurchaseLimit))
	n += 1 + sovTtgiftcenter(uint64(m.PurchaseCondition))
	n += 1 + sovTtgiftcenter(uint64(m.DayLimit))
	n += 1 + sovTtgiftcenter(uint64(m.HourLimit))
	n += 1 + sovTtgiftcenter(uint64(m.BuySTime))
	n += 1 + sovTtgiftcenter(uint64(m.BuyETime))
	n += 1 + sovTtgiftcenter(uint64(m.PickAble))
	n += 1 + sovTtgiftcenter(uint64(m.PickTimes))
	n += 1 + sovTtgiftcenter(uint64(m.Left))
	n += 2 + sovTtgiftcenter(uint64(m.Stock))
	n += 2 + sovTtgiftcenter(uint64(m.UpdateTime))
	if m.Product != nil {
		l = m.Product.Size()
		n += 2 + l + sovTtgiftcenter(uint64(l))
	}
	n += 2 + sovTtgiftcenter(uint64(m.PickSTime))
	n += 2 + sovTtgiftcenter(uint64(m.PickETime))
	n += 2 + sovTtgiftcenter(uint64(m.UnclaimedCount))
	n += 2 + sovTtgiftcenter(uint64(m.RegisterIn))
	return n
}

func (m *GetSubTTGiftResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TtGiftList) > 0 {
		for _, e := range m.TtGiftList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	n += 1 + sovTtgiftcenter(uint64(m.Total))
	return n
}

func (m *SubGuildGift) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.Price))
	n += 1 + sovTtgiftcenter(uint64(m.Status))
	n += 1 + sovTtgiftcenter(uint64(m.Rank))
	n += 1 + sovTtgiftcenter(uint64(m.LevelMin))
	n += 1 + sovTtgiftcenter(uint64(m.LevelMax))
	n += 1 + sovTtgiftcenter(uint64(m.PurchaseLimit))
	n += 1 + sovTtgiftcenter(uint64(m.BuySTime))
	n += 1 + sovTtgiftcenter(uint64(m.BuyETime))
	n += 1 + sovTtgiftcenter(uint64(m.Left))
	n += 1 + sovTtgiftcenter(uint64(m.Stock))
	n += 1 + sovTtgiftcenter(uint64(m.UpdateTime))
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovTtgiftcenter(uint64(l))
	}
	n += 1 + sovTtgiftcenter(uint64(m.UnclaimedCount))
	return n
}

func (m *GetSubGuildGiftResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GuildGiftList) > 0 {
		for _, e := range m.GuildGiftList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	n += 1 + sovTtgiftcenter(uint64(m.Total))
	return n
}

func (m *ActivitGift) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.Left))
	n += 1 + sovTtgiftcenter(uint64(m.Stock))
	n += 1 + sovTtgiftcenter(uint64(m.UpdateTime))
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovTtgiftcenter(uint64(l))
	}
	n += 1 + sovTtgiftcenter(uint64(m.UnclaimedCount))
	return n
}

func (m *GetSubActivityGiftResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ActivityGiftList) > 0 {
		for _, e := range m.ActivityGiftList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	n += 1 + sovTtgiftcenter(uint64(m.Total))
	return n
}

func (m *YGift) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.Left))
	n += 1 + sovTtgiftcenter(uint64(m.Stock))
	n += 1 + sovTtgiftcenter(uint64(m.UpdateTime))
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovTtgiftcenter(uint64(l))
	}
	return n
}

func (m *GetSubYGiftResp) Size() (n int) {
	var l int
	_ = l
	if len(m.YGiftList) > 0 {
		for _, e := range m.YGiftList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	n += 1 + sovTtgiftcenter(uint64(m.Total))
	return n
}

func (m *OperateGift) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.Left))
	n += 1 + sovTtgiftcenter(uint64(m.Stock))
	n += 1 + sovTtgiftcenter(uint64(m.UpdateTime))
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovTtgiftcenter(uint64(l))
	}
	return n
}

func (m *GetSubOperateGiftResp) Size() (n int) {
	var l int
	_ = l
	if len(m.OperateGiftList) > 0 {
		for _, e := range m.OperateGiftList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	n += 1 + sovTtgiftcenter(uint64(m.Total))
	return n
}

func (m *ModifyGiftRankReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.SubType))
	if len(m.ProductIds) > 0 {
		for _, e := range m.ProductIds {
			n += 1 + sovTtgiftcenter(uint64(e))
		}
	}
	n += 1 + sovTtgiftcenter(uint64(m.GiftType))
	return n
}

func (m *DispatchSubTTGiftReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovTtgiftcenter(uint64(e))
		}
	}
	l = len(m.Operator)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	return n
}

func (m *DispatchSubGiftResp) Size() (n int) {
	var l int
	_ = l
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovTtgiftcenter(uint64(l))
	}
	if len(m.ItemList) > 0 {
		for _, e := range m.ItemList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	return n
}

func (m *DispatchSubGuildGiftReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.GuildId))
	n += 1 + sovTtgiftcenter(uint64(m.Count))
	l = len(m.Operator)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	return n
}

func (m *GetGameGiftAmountReq) Size() (n int) {
	var l int
	_ = l
	if len(m.GameIdList) > 0 {
		for _, e := range m.GameIdList {
			n += 1 + sovTtgiftcenter(uint64(e))
		}
	}
	n += 1 + sovTtgiftcenter(uint64(m.SubType))
	n += 1 + sovTtgiftcenter(uint64(m.Platform))
	return n
}

func (m *GameGiftAmount) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.GameId))
	n += 1 + sovTtgiftcenter(uint64(m.GiftAmount))
	return n
}

func (m *GetGameGiftAmountResp) Size() (n int) {
	var l int
	_ = l
	if len(m.AmountList) > 0 {
		for _, e := range m.AmountList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	return n
}

func (m *GetTTGiftPurchaseAuthReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.Uid))
	if len(m.ProductIdList) > 0 {
		for _, e := range m.ProductIdList {
			n += 1 + sovTtgiftcenter(uint64(e))
		}
	}
	return n
}

func (m *TTGiftPurchaseAuth) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 2
	n += 1 + sovTtgiftcenter(uint64(m.AlreadyPurchase))
	return n
}

func (m *GetTTGiftPurchaseAuthResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PurchaseAuthList) > 0 {
		for _, e := range m.PurchaseAuthList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	return n
}

func (m *GetGuildGiftPurchaseAuthReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.GuildId))
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.GuildLevel))
	n += 1 + sovTtgiftcenter(uint64(m.BuyNumber))
	return n
}

func (m *GetGuildGiftPurchaseAuthResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.BuyLimit))
	n += 2
	return n
}

func (m *ExportSubGiftReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.Amount))
	n += 1 + sovTtgiftcenter(uint64(m.SubType))
	n += 1 + sovTtgiftcenter(uint64(m.ExportToType))
	if len(m.ExportToIdList) > 0 {
		for _, e := range m.ExportToIdList {
			n += 1 + sovTtgiftcenter(uint64(e))
		}
	}
	n += 1 + sovTtgiftcenter(uint64(m.ExportToIdPerCnt))
	return n
}

func (m *ExportSubGiftResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ItemList) > 0 {
		for _, e := range m.ItemList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	return n
}

func (m *ClaimGiftReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.Id))
	return n
}

func (m *ClaimGiftResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GiftList) > 0 {
		for _, e := range m.GiftList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	return n
}

func (m *GetGiftDispatchRecordReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	return n
}

func (m *TTGiftDispatchInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.Uid))
	n += 1 + sovTtgiftcenter(uint64(m.DispatchTime))
	n += 1 + sovTtgiftcenter(uint64(m.DispatchCount))
	n += 1 + sovTtgiftcenter(uint64(m.Status))
	n += 1 + sovTtgiftcenter(uint64(m.ClaimTime))
	return n
}

func (m *GetTTGiftDispatchRecordResp) Size() (n int) {
	var l int
	_ = l
	if len(m.DispatchList) > 0 {
		for _, e := range m.DispatchList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	return n
}

func (m *GuildGiftDispatchInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.GuildId))
	n += 1 + sovTtgiftcenter(uint64(m.DispatchTime))
	n += 1 + sovTtgiftcenter(uint64(m.DispatchCount))
	return n
}

func (m *GetGuildGiftDispatchRecordResp) Size() (n int) {
	var l int
	_ = l
	if len(m.DispatchList) > 0 {
		for _, e := range m.DispatchList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	return n
}

func (m *RecycleGiftReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	return n
}

func (m *AddGiftActivityReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Title)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	n += 1 + sovTtgiftcenter(uint64(m.ActivityType))
	l = len(m.PicUrl)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	l = len(m.JumpUrl)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	l = len(m.IconUrl)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	n += 1 + sovTtgiftcenter(uint64(m.GameId))
	n += 1 + sovTtgiftcenter(uint64(m.StartTime))
	n += 1 + sovTtgiftcenter(uint64(m.EndTime))
	l = len(m.Operator)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	return n
}

func (m *AddGiftActivityResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ActivityId))
	return n
}

func (m *DelGiftActivityReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ActivityId))
	l = len(m.Operator)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	return n
}

func (m *ModifyGiftActivityRankReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ActivityIdList) > 0 {
		for _, e := range m.ActivityIdList {
			n += 1 + sovTtgiftcenter(uint64(e))
		}
	}
	return n
}

func (m *GiftActivity) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ActivityId))
	l = len(m.Title)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	n += 1 + sovTtgiftcenter(uint64(m.ActivityType))
	l = len(m.PicUrl)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	l = len(m.IconUrl)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	l = len(m.JumpUrl)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	n += 1 + sovTtgiftcenter(uint64(m.GameId))
	n += 1 + sovTtgiftcenter(uint64(m.StartTime))
	n += 1 + sovTtgiftcenter(uint64(m.EndTime))
	n += 1 + sovTtgiftcenter(uint64(m.Rank))
	n += 1 + sovTtgiftcenter(uint64(m.CreateTime))
	return n
}

func (m *GetGiftActivityReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ActivityId))
	n += 1 + sovTtgiftcenter(uint64(m.GameId))
	n += 1 + sovTtgiftcenter(uint64(m.CreateAfterTime))
	n += 1 + sovTtgiftcenter(uint64(m.CreateBeforeTime))
	n += 1 + sovTtgiftcenter(uint64(m.ActivityType))
	n += 1 + sovTtgiftcenter(uint64(m.OrderByType))
	n += 2
	n += 1 + sovTtgiftcenter(uint64(m.LimitStart))
	n += 1 + sovTtgiftcenter(uint64(m.LimitEnd))
	return n
}

func (m *GetGiftActivityResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GiftActivityList) > 0 {
		for _, e := range m.GiftActivityList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	n += 1 + sovTtgiftcenter(uint64(m.Total))
	return n
}

func (m *ActivityBanner) Size() (n int) {
	var l int
	_ = l
	l = len(m.PicUrl)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	l = len(m.JumpUrl)
	n += 1 + l + sovTtgiftcenter(uint64(l))
	n += 1 + sovTtgiftcenter(uint64(m.ActivityId))
	return n
}

func (m *GetActivityBannerResp) Size() (n int) {
	var l int
	_ = l
	if len(m.BannerList) > 0 {
		for _, e := range m.BannerList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	return n
}

func (m *PurchaseTTGiftReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.Uid))
	return n
}

func (m *PurchaseTTGiftResp) Size() (n int) {
	var l int
	_ = l
	if m.GiftInfo != nil {
		l = m.GiftInfo.Size()
		n += 1 + l + sovTtgiftcenter(uint64(l))
	}
	if m.ItemInfo != nil {
		l = m.ItemInfo.Size()
		n += 1 + l + sovTtgiftcenter(uint64(l))
	}
	return n
}

func (m *PurchaseGuildGiftReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	n += 1 + sovTtgiftcenter(uint64(m.GuildId))
	n += 1 + sovTtgiftcenter(uint64(m.Count))
	return n
}

func (m *PurchaseGuildGiftResp) Size() (n int) {
	var l int
	_ = l
	if m.GiftInfo != nil {
		l = m.GiftInfo.Size()
		n += 1 + l + sovTtgiftcenter(uint64(l))
	}
	if len(m.ItemList) > 0 {
		for _, e := range m.ItemList {
			l = e.Size()
			n += 1 + l + sovTtgiftcenter(uint64(l))
		}
	}
	return n
}

func (m *PickTTGiftReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	return n
}

func (m *GetTTGiftDayLimitReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.ProductId))
	return n
}

func (m *GetTTGiftDayLimitResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTtgiftcenter(uint64(m.Used))
	n += 1 + sovTtgiftcenter(uint64(m.TotalLimit))
	return n
}

func sovTtgiftcenter(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozTtgiftcenter(x uint64) (n int) {
	return sovTtgiftcenter(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *ProductItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ProductItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ProductItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StorageId", wireType)
			}
			m.StorageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StorageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemBinary", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemBinary = append(m.ItemBinary[:0], dAtA[iNdEx:postIndex]...)
			if m.ItemBinary == nil {
				m.ItemBinary = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("storage_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_binary")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UsageDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UsageDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftType", wireType)
			}
			m.GiftType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeSDate", wireType)
			}
			m.ExchangeSDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeSDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeEDate", wireType)
			}
			m.ExchangeEDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeEDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Operator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("description")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("usage_desc")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_type")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_type")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("exchange_s_date")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("exchange_e_date")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("operator")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("platform")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateProductResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateProductResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateProductResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UsageDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UsageDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftType", wireType)
			}
			m.GiftType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeSDate", wireType)
			}
			m.ExchangeSDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeSDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeEDate", wireType)
			}
			m.ExchangeEDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeEDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Operator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("description")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("usage_desc")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_type")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("exchange_s_date")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("exchange_e_date")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("operator")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("platform")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Product) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Product: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Product: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UsageDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UsageDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftType", wireType)
			}
			m.GiftType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeSDate", wireType)
			}
			m.ExchangeSDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeSDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeEDate", wireType)
			}
			m.ExchangeEDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeEDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Left", wireType)
			}
			m.Left = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Left |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Stock", wireType)
			}
			m.Stock = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Stock |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000800)
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00001000)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("description")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("usage_desc")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_type")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_type")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("exchange_s_date")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("exchange_e_date")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("left")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stock")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("platform")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SearchProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SearchProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SearchProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftType", wireType)
			}
			m.GiftType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateAfterTime", wireType)
			}
			m.CreateAfterTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateAfterTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateBeforeTime", wireType)
			}
			m.CreateBeforeTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateBeforeTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderByType", wireType)
			}
			m.OrderByType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OrderByType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Desc = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitStartIdx", wireType)
			}
			m.LimitStartIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitStartIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitCount", wireType)
			}
			m.LimitCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_start_idx")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_count")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("platform")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SearchProductResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SearchProductResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SearchProductResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductList = append(m.ProductList, &Product{})
			if err := m.ProductList[len(m.ProductList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetProductReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ProductidList = append(m.ProductidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTtgiftcenter
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTtgiftcenter
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint64(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ProductidList = append(m.ProductidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetProductResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetProductResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetProductResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductList = append(m.ProductList, &Product{})
			if err := m.ProductList[len(m.ProductList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DispatchItemReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DispatchItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DispatchItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubType", wireType)
			}
			m.SubType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SubType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Operator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sub_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("amount")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("operator")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AccountPwdItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AccountPwdItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AccountPwdItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Pwd", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Pwd = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pwd")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExchangeCardItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ExchangeCardItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ExchangeCardItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Code = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddItemReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemBinaryList", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemBinaryList = append(m.ItemBinaryList, make([]byte, postIndex-iNdEx))
			copy(m.ItemBinaryList[len(m.ItemBinaryList)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Operator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemHashList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemHashList = append(m.ItemHashList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("operator")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuccessItems", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SuccessItems = append(m.SuccessItems, make([]byte, postIndex-iNdEx))
			copy(m.SuccessItems[len(m.SuccessItems)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FailedItems", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FailedItems = append(m.FailedItems, make([]byte, postIndex-iNdEx))
			copy(m.FailedItems[len(m.FailedItems)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateSubTTGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateSubTTGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateSubTTGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelMin", wireType)
			}
			m.LevelMin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelMin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelMax", wireType)
			}
			m.LevelMax = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelMax |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PurchaseLimit", wireType)
			}
			m.PurchaseLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PurchaseLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PurchaseCondition", wireType)
			}
			m.PurchaseCondition = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PurchaseCondition |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayLimit", wireType)
			}
			m.DayLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DayLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HourLimit", wireType)
			}
			m.HourLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HourLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuySTime", wireType)
			}
			m.BuySTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuySTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuyETime", wireType)
			}
			m.BuyETime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuyETime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PickAble", wireType)
			}
			m.PickAble = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PickAble |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000800)
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Operator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00001000)
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PickSTime", wireType)
			}
			m.PickSTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PickSTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PickETime", wireType)
			}
			m.PickETime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PickETime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RegisterIn", wireType)
			}
			m.RegisterIn = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegisterIn |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("price")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level_min")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level_max")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("purchase_limit")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("purchase_condition")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("day_limit")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("hour_limit")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("buy_s_time")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("buy_e_time")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pick_able")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("operator")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateSubGuildGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateSubGuildGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateSubGuildGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelMin", wireType)
			}
			m.LevelMin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelMin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelMax", wireType)
			}
			m.LevelMax = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelMax |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PurchaseLimit", wireType)
			}
			m.PurchaseLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PurchaseLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuySTime", wireType)
			}
			m.BuySTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuySTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuyETime", wireType)
			}
			m.BuyETime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuyETime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Operator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("price")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level_min")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level_max")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("purchase_limit")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("buy_s_time")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("buy_e_time")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("operator")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddSubYorActGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddSubYorActGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddSubYorActGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Operator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubType", wireType)
			}
			m.SubType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SubType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("operator")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sub_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSubGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSubGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSubGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateAfterTime", wireType)
			}
			m.CreateAfterTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateAfterTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateBeforeTime", wireType)
			}
			m.CreateBeforeTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateBeforeTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderByType", wireType)
			}
			m.OrderByType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OrderByType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Desc = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitStart", wireType)
			}
			m.LimitStart = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitStart |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitEnd", wireType)
			}
			m.LimitEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftType", wireType)
			}
			m.GiftType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNeedExpire", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNeedExpire = bool(v != 0)
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_start")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_end")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("platform")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SubTTGift) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SubTTGift: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SubTTGift: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelMin", wireType)
			}
			m.LevelMin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelMin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelMax", wireType)
			}
			m.LevelMax = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelMax |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PurchaseLimit", wireType)
			}
			m.PurchaseLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PurchaseLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PurchaseCondition", wireType)
			}
			m.PurchaseCondition = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PurchaseCondition |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayLimit", wireType)
			}
			m.DayLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DayLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HourLimit", wireType)
			}
			m.HourLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HourLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuySTime", wireType)
			}
			m.BuySTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuySTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuyETime", wireType)
			}
			m.BuyETime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuyETime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000800)
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PickAble", wireType)
			}
			m.PickAble = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PickAble |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00001000)
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PickTimes", wireType)
			}
			m.PickTimes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PickTimes |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00002000)
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Left", wireType)
			}
			m.Left = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Left |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00004000)
		case 16:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Stock", wireType)
			}
			m.Stock = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Stock |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00008000)
		case 17:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00010000)
		case 18:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &Product{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00020000)
		case 19:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PickSTime", wireType)
			}
			m.PickSTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PickSTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 20:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PickETime", wireType)
			}
			m.PickETime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PickETime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 21:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UnclaimedCount", wireType)
			}
			m.UnclaimedCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UnclaimedCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 22:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RegisterIn", wireType)
			}
			m.RegisterIn = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegisterIn |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("price")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rank")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level_min")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level_max")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("purchase_limit")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("purchase_condition")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("day_limit")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("hour_limit")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("buy_s_time")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("buy_e_time")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pick_able")
	}
	if hasFields[0]&uint64(0x00002000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pick_times")
	}
	if hasFields[0]&uint64(0x00004000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("left")
	}
	if hasFields[0]&uint64(0x00008000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stock")
	}
	if hasFields[0]&uint64(0x00010000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("update_time")
	}
	if hasFields[0]&uint64(0x00020000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSubTTGiftResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSubTTGiftResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSubTTGiftResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TtGiftList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TtGiftList = append(m.TtGiftList, &SubTTGift{})
			if err := m.TtGiftList[len(m.TtGiftList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SubGuildGift) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SubGuildGift: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SubGuildGift: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelMin", wireType)
			}
			m.LevelMin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelMin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelMax", wireType)
			}
			m.LevelMax = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelMax |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PurchaseLimit", wireType)
			}
			m.PurchaseLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PurchaseLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuySTime", wireType)
			}
			m.BuySTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuySTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuyETime", wireType)
			}
			m.BuyETime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuyETime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Left", wireType)
			}
			m.Left = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Left |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Stock", wireType)
			}
			m.Stock = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Stock |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000800)
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &Product{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00001000)
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UnclaimedCount", wireType)
			}
			m.UnclaimedCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UnclaimedCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("price")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rank")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level_min")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level_max")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("purchase_limit")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("buy_s_time")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("buy_e_time")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("left")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stock")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("update_time")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSubGuildGiftResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSubGuildGiftResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSubGuildGiftResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildGiftList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildGiftList = append(m.GuildGiftList, &SubGuildGift{})
			if err := m.GuildGiftList[len(m.GuildGiftList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivitGift) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActivitGift: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActivitGift: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Left", wireType)
			}
			m.Left = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Left |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Stock", wireType)
			}
			m.Stock = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Stock |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &Product{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UnclaimedCount", wireType)
			}
			m.UnclaimedCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UnclaimedCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("left")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stock")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("update_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSubActivityGiftResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSubActivityGiftResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSubActivityGiftResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityGiftList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityGiftList = append(m.ActivityGiftList, &ActivitGift{})
			if err := m.ActivityGiftList[len(m.ActivityGiftList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *YGift) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: YGift: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: YGift: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Left", wireType)
			}
			m.Left = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Left |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Stock", wireType)
			}
			m.Stock = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Stock |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &Product{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("left")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stock")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("update_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSubYGiftResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSubYGiftResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSubYGiftResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field YGiftList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.YGiftList = append(m.YGiftList, &YGift{})
			if err := m.YGiftList[len(m.YGiftList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OperateGift) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OperateGift: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OperateGift: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Left", wireType)
			}
			m.Left = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Left |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Stock", wireType)
			}
			m.Stock = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Stock |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &Product{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("left")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stock")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("update_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSubOperateGiftResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSubOperateGiftResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSubOperateGiftResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperateGiftList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OperateGiftList = append(m.OperateGiftList, &OperateGift{})
			if err := m.OperateGiftList[len(m.OperateGiftList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyGiftRankReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyGiftRankReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyGiftRankReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubType", wireType)
			}
			m.SubType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SubType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ProductIds = append(m.ProductIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTtgiftcenter
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTtgiftcenter
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint64(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ProductIds = append(m.ProductIds, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductIds", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftType", wireType)
			}
			m.GiftType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sub_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DispatchSubTTGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DispatchSubTTGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DispatchSubTTGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTtgiftcenter
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTtgiftcenter
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Operator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("operator")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DispatchSubGiftResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DispatchSubGiftResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DispatchSubGiftResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &Product{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemList = append(m.ItemList, &ProductItem{})
			if err := m.ItemList[len(m.ItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DispatchSubGuildGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DispatchSubGuildGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DispatchSubGuildGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Operator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("operator")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameGiftAmountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameGiftAmountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameGiftAmountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameIdList = append(m.GameIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTtgiftcenter
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTtgiftcenter
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameIdList = append(m.GameIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameIdList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubType", wireType)
			}
			m.SubType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SubType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sub_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("platform")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameGiftAmount) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameGiftAmount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameGiftAmount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftAmount", wireType)
			}
			m.GiftAmount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftAmount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_amount")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameGiftAmountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameGiftAmountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameGiftAmountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AmountList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AmountList = append(m.AmountList, &GameGiftAmount{})
			if err := m.AmountList[len(m.AmountList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTTGiftPurchaseAuthReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTTGiftPurchaseAuthReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTTGiftPurchaseAuthReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ProductIdList = append(m.ProductIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTtgiftcenter
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTtgiftcenter
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint64(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ProductIdList = append(m.ProductIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TTGiftPurchaseAuth) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TTGiftPurchaseAuth: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TTGiftPurchaseAuth: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Auth", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Auth = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AlreadyPurchase", wireType)
			}
			m.AlreadyPurchase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AlreadyPurchase |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("auth")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTTGiftPurchaseAuthResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTTGiftPurchaseAuthResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTTGiftPurchaseAuthResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PurchaseAuthList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PurchaseAuthList = append(m.PurchaseAuthList, &TTGiftPurchaseAuth{})
			if err := m.PurchaseAuthList[len(m.PurchaseAuthList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftPurchaseAuthReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftPurchaseAuthReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftPurchaseAuthReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildLevel", wireType)
			}
			m.GuildLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuyNumber", wireType)
			}
			m.BuyNumber = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuyNumber |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_level")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("buy_number")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftPurchaseAuthResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftPurchaseAuthResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftPurchaseAuthResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuyLimit", wireType)
			}
			m.BuyLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuyLimit |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelAuth", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.LevelAuth = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("buy_limit")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level_auth")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExportSubGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ExportSubGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ExportSubGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubType", wireType)
			}
			m.SubType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SubType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExportToType", wireType)
			}
			m.ExportToType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExportToType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ExportToIdList = append(m.ExportToIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTtgiftcenter
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTtgiftcenter
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ExportToIdList = append(m.ExportToIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExportToIdList", wireType)
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExportToIdPerCnt", wireType)
			}
			m.ExportToIdPerCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExportToIdPerCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("amount")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sub_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExportSubGiftResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ExportSubGiftResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ExportSubGiftResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemList = append(m.ItemList, &ProductItem{})
			if err := m.ItemList[len(m.ItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClaimGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClaimGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClaimGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClaimGiftResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClaimGiftResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClaimGiftResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftList = append(m.GiftList, &DispatchSubGiftResp{})
			if err := m.GiftList[len(m.GiftList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftDispatchRecordReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftDispatchRecordReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftDispatchRecordReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TTGiftDispatchInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TTGiftDispatchInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TTGiftDispatchInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DispatchTime", wireType)
			}
			m.DispatchTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DispatchTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DispatchCount", wireType)
			}
			m.DispatchCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DispatchCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClaimTime", wireType)
			}
			m.ClaimTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClaimTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("dispatch_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("dispatch_count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("claim_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTTGiftDispatchRecordResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTTGiftDispatchRecordResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTTGiftDispatchRecordResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DispatchList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DispatchList = append(m.DispatchList, &TTGiftDispatchInfo{})
			if err := m.DispatchList[len(m.DispatchList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildGiftDispatchInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildGiftDispatchInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildGiftDispatchInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DispatchTime", wireType)
			}
			m.DispatchTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DispatchTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DispatchCount", wireType)
			}
			m.DispatchCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DispatchCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("dispatch_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("dispatch_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftDispatchRecordResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftDispatchRecordResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftDispatchRecordResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DispatchList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DispatchList = append(m.DispatchList, &GuildGiftDispatchInfo{})
			if err := m.DispatchList[len(m.DispatchList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecycleGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecycleGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecycleGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddGiftActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddGiftActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddGiftActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityType", wireType)
			}
			m.ActivityType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Operator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pic_url")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("jump_url")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("icon_url")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_time")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("operator")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddGiftActivityResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddGiftActivityResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddGiftActivityResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelGiftActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelGiftActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelGiftActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Operator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("operator")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyGiftActivityRankReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyGiftActivityRankReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyGiftActivityRankReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ActivityIdList = append(m.ActivityIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTtgiftcenter
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTtgiftcenter
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ActivityIdList = append(m.ActivityIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GiftActivity) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GiftActivity: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GiftActivity: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityType", wireType)
			}
			m.ActivityType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pic_url")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("icon_url")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("jump_url")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_time")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rank")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("create_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateAfterTime", wireType)
			}
			m.CreateAfterTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateAfterTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateBeforeTime", wireType)
			}
			m.CreateBeforeTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateBeforeTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityType", wireType)
			}
			m.ActivityType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderByType", wireType)
			}
			m.OrderByType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OrderByType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Desc = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitStart", wireType)
			}
			m.LimitStart = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitStart |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitEnd", wireType)
			}
			m.LimitEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_by_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("desc")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_start")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_end")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftActivityResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftActivityResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftActivityResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftActivityList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftActivityList = append(m.GiftActivityList, &GiftActivity{})
			if err := m.GiftActivityList[len(m.GiftActivityList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivityBanner) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActivityBanner: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActivityBanner: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pic_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("jump_url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActivityBannerResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActivityBannerResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActivityBannerResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannerList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BannerList = append(m.BannerList, &ActivityBanner{})
			if err := m.BannerList[len(m.BannerList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PurchaseTTGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PurchaseTTGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PurchaseTTGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PurchaseTTGiftResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PurchaseTTGiftResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PurchaseTTGiftResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GiftInfo == nil {
				m.GiftInfo = &SubTTGift{}
			}
			if err := m.GiftInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemInfo == nil {
				m.ItemInfo = &ProductItem{}
			}
			if err := m.ItemInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PurchaseGuildGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PurchaseGuildGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PurchaseGuildGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PurchaseGuildGiftResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PurchaseGuildGiftResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PurchaseGuildGiftResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GiftInfo == nil {
				m.GiftInfo = &SubGuildGift{}
			}
			if err := m.GiftInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemList = append(m.ItemList, &ProductItem{})
			if err := m.ItemList[len(m.ItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PickTTGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PickTTGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PickTTGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTTGiftDayLimitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTTGiftDayLimitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTTGiftDayLimitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTTGiftDayLimitResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTTGiftDayLimitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTTGiftDayLimitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Used", wireType)
			}
			m.Used = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Used |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalLimit", wireType)
			}
			m.TotalLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTtgiftcenter(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTtgiftcenter
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("used")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipTtgiftcenter(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowTtgiftcenter
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTtgiftcenter
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthTtgiftcenter
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowTtgiftcenter
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipTtgiftcenter(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthTtgiftcenter = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowTtgiftcenter   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/ttgiftcentersvr/ttgiftcenter.proto", fileDescriptorTtgiftcenter) }

var fileDescriptorTtgiftcenter = []byte{
	// 3904 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x5b, 0x4b, 0x70, 0x1b, 0x47,
	0x73, 0xf6, 0xe2, 0x41, 0x00, 0x8d, 0x07, 0xc1, 0xd5, 0xc3, 0x10, 0x2c, 0x91, 0xd0, 0xea, 0x61,
	0x8a, 0xb6, 0xa4, 0x94, 0x2c, 0xbf, 0x64, 0x59, 0x0e, 0x48, 0x42, 0x14, 0x4b, 0x14, 0xc9, 0x02,
	0x21, 0x3b, 0x72, 0x25, 0x41, 0x16, 0xbb, 0x4b, 0x72, 0x4d, 0x3c, 0xd6, 0xfb, 0x90, 0x09, 0xbf,
	0x2a, 0x65, 0xe7, 0xe0, 0xca, 0xc9, 0x95, 0x4b, 0x0e, 0xa9, 0x54, 0x0e, 0xf1, 0x3d, 0x97, 0x9c,
	0x72, 0x4a, 0x55, 0x0e, 0xf1, 0x31, 0x55, 0xa9, 0x5c, 0x53, 0x89, 0x7d, 0xf1, 0x2d, 0xb9, 0xe6,
	0x12, 0xa7, 0x66, 0x66, 0x1f, 0x33, 0xb3, 0x03, 0x60, 0xa9, 0xc8, 0xa9, 0xf8, 0xff, 0x2f, 0x2a,
	0xa1, 0xa7, 0xa7, 0x7b, 0xa6, 0xbb, 0xa7, 0xbb, 0xe7, 0x9b, 0x25, 0x5c, 0x75, 0x6c, 0xed, 0xa6,
	0xeb, 0x1e, 0x98, 0xfb, 0xae, 0x66, 0x0c, 0x5d, 0xc3, 0x76, 0x9e, 0xda, 0xcc, 0xef, 0x1b, 0x96,
	0x3d, 0x72, 0x47, 0x72, 0xa9, 0xd3, 0xd9, 0x30, 0xf7, 0xdd, 0x35, 0x4c, 0xab, 0x5f, 0xd6, 0x46,
	0x83, 0xc1, 0x68, 0x78, 0xd3, 0xed, 0x3f, 0xb5, 0x4c, 0xed, 0xa8, 0x6f, 0xdc, 0x74, 0x8e, 0x7a,
	0x9e, 0xd9, 0x77, 0xcd, 0xa1, 0x3b, 0xb6, 0x0c, 0x32, 0x47, 0xf9, 0x18, 0x8a, 0xbb, 0xf6, 0x48,
	0xf7, 0x34, 0x77, 0xd3, 0x35, 0x06, 0xf2, 0x25, 0x00, 0xc7, 0x1d, 0xd9, 0xea, 0x81, 0xd1, 0x35,
	0xf5, 0x9a, 0xd4, 0x48, 0x2d, 0x97, 0x57, 0x33, 0xdf, 0xff, 0xeb, 0xd2, 0x0b, 0xed, 0x82, 0x4f,
	0xdf, 0xd4, 0xe5, 0x2b, 0x50, 0x34, 0x5d, 0x63, 0xd0, 0xed, 0x99, 0x43, 0xd5, 0x1e, 0xd7, 0x52,
	0x8d, 0xd4, 0x72, 0xc9, 0xe7, 0x02, 0x34, 0xb0, 0x8a, 0xe9, 0xf2, 0x59, 0x48, 0x7b, 0xa6, 0x5e,
	0x4b, 0x37, 0xa4, 0x50, 0x08, 0x22, 0x28, 0xff, 0x9d, 0x82, 0xea, 0x9a, 0x6d, 0xa8, 0xae, 0xe1,
	0x6b, 0x6e, 0x1b, 0x1f, 0xcb, 0x35, 0xc8, 0x0c, 0xd5, 0x81, 0x81, 0x55, 0x16, 0x7c, 0x6e, 0x4c,
	0x91, 0xaf, 0x42, 0x51, 0x37, 0x1c, 0xcd, 0x36, 0x2d, 0xd7, 0x1c, 0x0d, 0xb1, 0xb6, 0x80, 0x81,
	0x1e, 0x40, 0x4b, 0xf7, 0x1c, 0xb4, 0x70, 0x44, 0xac, 0xa5, 0x29, 0xb6, 0x02, 0xa6, 0xaf, 0x1b,
	0x8e, 0x26, 0x5f, 0x80, 0xdc, 0x81, 0x3a, 0xc0, 0x9b, 0xcb, 0x50, 0x9b, 0x9b, 0x43, 0xc4, 0x4d,
	0x5d, 0xbe, 0x08, 0x05, 0xbc, 0x33, 0x64, 0xa0, 0x5a, 0x96, 0x62, 0xc8, 0x23, 0x72, 0x67, 0x6c,
	0x19, 0x88, 0x05, 0x19, 0x9e, 0xb0, 0xcc, 0xd1, 0x2c, 0x88, 0x8c, 0x59, 0x5e, 0x85, 0x79, 0xe3,
	0x58, 0x3b, 0x54, 0x87, 0x07, 0x46, 0xd7, 0xe9, 0xea, 0xaa, 0x6b, 0xd4, 0x72, 0x14, 0x63, 0x39,
	0x18, 0xdc, 0x5b, 0x57, 0x5d, 0x96, 0xdb, 0x20, 0xdc, 0x79, 0x11, 0x77, 0x0b, 0x73, 0x37, 0x20,
	0x3f, 0xb2, 0x0c, 0x5b, 0x75, 0x47, 0x76, 0xad, 0x40, 0xed, 0x31, 0xa4, 0x22, 0x0e, 0xab, 0xaf,
	0xba, 0xfb, 0x23, 0x7b, 0x50, 0x03, 0x7a, 0x7d, 0x01, 0x55, 0x79, 0x0b, 0x16, 0x38, 0xfb, 0x3b,
	0x16, 0x32, 0x9f, 0x45, 0x7e, 0x06, 0x9e, 0xcf, 0x04, 0xe6, 0xf3, 0xe9, 0x9b, 0xba, 0xf2, 0xef,
	0x29, 0xa8, 0x3e, 0x1a, 0xe9, 0xe6, 0xfe, 0x98, 0x72, 0x5d, 0x92, 0x99, 0xa1, 0x7f, 0x53, 0xb3,
	0xfc, 0x9b, 0x4e, 0xe6, 0xdf, 0x8c, 0xd8, 0xbf, 0x8c, 0x77, 0xb2, 0x49, 0xbd, 0x33, 0x77, 0x22,
	0xef, 0xe4, 0x92, 0x79, 0x27, 0x3f, 0xd3, 0x3b, 0x05, 0xa1, 0x77, 0xfe, 0x25, 0x0d, 0x39, 0xdf,
	0xba, 0xff, 0xaf, 0x4c, 0x4b, 0x1d, 0x9d, 0xec, 0xac, 0xa3, 0x33, 0x37, 0xfb, 0xe8, 0xe4, 0x92,
	0x3a, 0x27, 0x7f, 0x22, 0xe7, 0x14, 0x26, 0x3b, 0xa7, 0x06, 0x99, 0xbe, 0xb1, 0xef, 0x32, 0x87,
	0x02, 0x53, 0xe4, 0x3a, 0x64, 0x1d, 0x77, 0xa4, 0x1d, 0xd5, 0x8a, 0xd4, 0x10, 0x21, 0xa1, 0x64,
	0xa7, 0xe1, 0xc3, 0xd2, 0x75, 0xcd, 0x81, 0x51, 0x2b, 0x51, 0x1c, 0x40, 0x06, 0x3a, 0xe6, 0xc0,
	0x60, 0xfc, 0x5a, 0x16, 0xfa, 0xf5, 0x6f, 0xd3, 0x50, 0xdd, 0x33, 0x54, 0x5b, 0x3b, 0x9c, 0x72,
	0x76, 0x24, 0x91, 0x83, 0x29, 0xcb, 0xa7, 0xa8, 0x64, 0x4a, 0x59, 0x3e, 0x32, 0x2b, 0x9d, 0x6d,
	0x23, 0xb3, 0x06, 0x21, 0x92, 0x69, 0x48, 0x5c, 0x88, 0xfc, 0x0e, 0x2c, 0xf8, 0xdb, 0x53, 0xf7,
	0x5d, 0xc3, 0x26, 0x9b, 0xcc, 0x52, 0x42, 0xe6, 0xc9, 0x70, 0x13, 0x8d, 0xe2, 0x9d, 0xde, 0x02,
	0xd9, 0x9f, 0xd1, 0x33, 0xf6, 0x47, 0xb6, 0x6f, 0x97, 0x39, 0x6a, 0x4a, 0x95, 0x8c, 0xaf, 0xe2,
	0x61, 0x3c, 0x67, 0x19, 0xca, 0x23, 0x5b, 0x37, 0xec, 0x6e, 0x6f, 0x1c, 0x78, 0x3f, 0x62, 0x2f,
	0xe2, 0xa1, 0xd5, 0x71, 0xb0, 0x52, 0x1c, 0x84, 0xf9, 0x86, 0xb4, 0x9c, 0x0f, 0x56, 0x8a, 0x28,
	0xc8, 0xd9, 0x7d, 0x73, 0x60, 0xba, 0x5d, 0xc7, 0x55, 0x6d, 0x64, 0xae, 0x63, 0xd6, 0xd9, 0x78,
	0x70, 0x0f, 0x8d, 0x6d, 0xea, 0xc7, 0xc8, 0x6d, 0x84, 0x5b, 0x1b, 0x79, 0x43, 0xd6, 0xe7, 0x80,
	0x07, 0xd6, 0x10, 0x9d, 0x71, 0x5b, 0x51, 0xe8, 0x36, 0x13, 0x16, 0x38, 0xaf, 0x39, 0x96, 0xfc,
	0x16, 0x94, 0x02, 0xb7, 0xf5, 0x4d, 0xc7, 0xad, 0x49, 0x8d, 0xf4, 0x72, 0xf1, 0xd6, 0x99, 0x1b,
	0x74, 0x01, 0xbe, 0x11, 0x4c, 0x28, 0xfa, 0xac, 0x5b, 0xa6, 0x83, 0x43, 0xcd, 0x1d, 0xb9, 0x6a,
	0x1f, 0x9f, 0xd6, 0x30, 0xd4, 0x30, 0x49, 0x79, 0x07, 0xe4, 0x55, 0xd5, 0xd5, 0x0e, 0x37, 0x0c,
	0x97, 0x0a, 0x91, 0x2b, 0x50, 0xf1, 0x05, 0x98, 0x7a, 0xa4, 0x2d, 0xd3, 0x2e, 0x87, 0x54, 0x24,
	0x58, 0xd9, 0x81, 0x53, 0xb1, 0xc9, 0xff, 0x9b, 0x95, 0x2a, 0x7f, 0x2e, 0xc1, 0xfc, 0xba, 0xe9,
	0x58, 0x48, 0x28, 0xea, 0x0d, 0x12, 0xa7, 0xfa, 0x25, 0xc8, 0x3b, 0x5e, 0x8f, 0xf8, 0x99, 0xde,
	0x65, 0xce, 0xf1, 0x7a, 0xd8, 0xc7, 0xe7, 0x61, 0x4e, 0x1d, 0x60, 0xb7, 0xa0, 0x8c, 0x94, 0x0d,
	0xc2, 0x99, 0xd0, 0x98, 0x1c, 0x9a, 0x11, 0xe5, 0x50, 0xe5, 0x01, 0x54, 0x9a, 0x1a, 0xf6, 0xeb,
	0xee, 0x27, 0x3a, 0x6e, 0x5b, 0x16, 0x21, 0xa7, 0x12, 0x0a, 0xd3, 0x40, 0x04, 0x44, 0xd4, 0x8a,
	0x58, 0x9f, 0xe8, 0x4c, 0x86, 0x44, 0x04, 0xe5, 0x55, 0xa8, 0xb6, 0xfc, 0x1c, 0xb1, 0xa6, 0xda,
	0x44, 0x56, 0x0d, 0x32, 0xda, 0x48, 0xe7, 0x3a, 0x11, 0x44, 0x51, 0xfe, 0x5a, 0x02, 0x68, 0xea,
	0xfa, 0x89, 0x8c, 0xb1, 0x0c, 0x55, 0xaa, 0x57, 0x22, 0x3e, 0x48, 0x35, 0xd2, 0xcb, 0xa5, 0x76,
	0x25, 0x6a, 0x95, 0x70, 0x64, 0xd0, 0xfb, 0x4e, 0x0b, 0x6b, 0xc7, 0x65, 0xc0, 0x73, 0xba, 0x87,
	0xaa, 0x73, 0x48, 0x24, 0x65, 0x1a, 0xe9, 0xe5, 0x42, 0xbb, 0x84, 0xa8, 0x0f, 0x54, 0xe7, 0x10,
	0xfb, 0xed, 0x31, 0x14, 0xc3, 0x45, 0xe2, 0xba, 0x5e, 0x76, 0x3c, 0x4d, 0x33, 0x1c, 0xa7, 0x8b,
	0xd8, 0x1c, 0x1c, 0x01, 0xa5, 0x76, 0xc9, 0x27, 0x22, 0x3e, 0x47, 0xbe, 0x08, 0xa5, 0x7d, 0xd5,
	0xec, 0x1b, 0xba, 0xcf, 0x43, 0x56, 0x58, 0x24, 0x34, 0xcc, 0xa2, 0xfc, 0x98, 0x01, 0xf9, 0xb1,
	0x85, 0x72, 0xec, 0x9e, 0xd7, 0x23, 0xd1, 0x93, 0xd8, 0x08, 0x75, 0xc8, 0x5a, 0xb6, 0xa9, 0xb1,
	0xe1, 0x40, 0x48, 0x28, 0x18, 0x1c, 0x57, 0x75, 0x3d, 0x07, 0x6f, 0x3a, 0xcc, 0x6d, 0x84, 0x86,
	0x72, 0x5b, 0xdf, 0x78, 0x6a, 0xf4, 0xbb, 0x03, 0x73, 0xc8, 0x74, 0x6c, 0x79, 0x4c, 0x7e, 0x64,
	0x0e, 0x29, 0x16, 0xf5, 0x98, 0x2d, 0xf9, 0x84, 0x45, 0x3d, 0x96, 0x5f, 0x81, 0x8a, 0xe5, 0xd9,
	0xda, 0xa1, 0xea, 0x18, 0x5d, 0x7c, 0xf8, 0xd9, 0x8a, 0x1f, 0x8c, 0x6d, 0xa1, 0x21, 0xf9, 0x35,
	0x90, 0x43, 0x66, 0x6d, 0x34, 0xd4, 0x4d, 0x5c, 0x3b, 0x73, 0xd4, 0xce, 0x16, 0x82, 0xf1, 0xb5,
	0x60, 0x18, 0x2d, 0x42, 0x57, 0xc7, 0xbe, 0x70, 0xba, 0x62, 0xe5, 0x75, 0x75, 0x4c, 0xe4, 0x5e,
	0x02, 0x38, 0x1c, 0x79, 0xb6, 0xcf, 0x43, 0xa7, 0xae, 0x02, 0xa2, 0x13, 0x26, 0x05, 0xa0, 0xe7,
	0x8d, 0xbb, 0x0e, 0x49, 0xaa, 0x4c, 0xfb, 0xd6, 0xf3, 0xc6, 0x7b, 0x38, 0x99, 0xfa, 0x3c, 0x7e,
	0xe2, 0x2d, 0x72, 0x3c, 0x2d, 0xcc, 0x73, 0x11, 0x0a, 0xa8, 0xe9, 0xef, 0xaa, 0xbd, 0x3e, 0x5b,
	0xb3, 0xf2, 0x88, 0xdc, 0xec, 0xf5, 0xd9, 0x5e, 0xa5, 0x3c, 0x21, 0xde, 0x8a, 0x58, 0x88, 0xbf,
	0x9a, 0x0a, 0x95, 0xb3, 0xb1, 0x74, 0xb2, 0x9c, 0x80, 0xcb, 0x5f, 0xcf, 0x3c, 0xcf, 0x45, 0x16,
	0x74, 0x05, 0x8a, 0xb6, 0x71, 0x60, 0x3a, 0xa8, 0xc6, 0x98, 0xc3, 0x5a, 0x95, 0xe2, 0x82, 0x60,
	0x60, 0x73, 0xa8, 0xfc, 0x73, 0x0a, 0xce, 0x84, 0x51, 0xb6, 0xe1, 0x99, 0x7d, 0xfd, 0x37, 0x37,
	0xd0, 0x58, 0x5f, 0xe7, 0x12, 0xf8, 0x3a, 0x2f, 0xf4, 0xf5, 0xcc, 0x2b, 0x81, 0xf2, 0x17, 0x12,
	0x9c, 0x6a, 0xea, 0xfa, 0x9e, 0xd7, 0x7b, 0x32, 0xb2, 0x9b, 0x9a, 0x7b, 0x22, 0x9b, 0x46, 0x76,
	0x4b, 0x09, 0xec, 0x36, 0x3b, 0x6b, 0xd1, 0xe5, 0x20, 0x23, 0x28, 0x07, 0xca, 0x7f, 0xa6, 0xa1,
	0xbc, 0x61, 0xb8, 0xc8, 0xe1, 0x13, 0xd6, 0xf5, 0x2c, 0x5d, 0x51, 0xd0, 0xf2, 0xa4, 0x93, 0xb5,
	0x3c, 0x19, 0x4a, 0x49, 0xc2, 0x96, 0x27, 0x4b, 0x4d, 0x89, 0xb7, 0x3c, 0x91, 0xd9, 0xe8, 0xd6,
	0x28, 0x30, 0xdb, 0xf3, 0x68, 0x88, 0xc2, 0x16, 0x07, 0x37, 0x44, 0x4c, 0x46, 0x81, 0xa8, 0x19,
	0xc2, 0x61, 0x8b, 0xd9, 0x8c, 0xa1, 0xce, 0x66, 0x14, 0x4c, 0x6e, 0x0d, 0xb9, 0x0e, 0xb2, 0x28,
	0xec, 0x20, 0x57, 0xa0, 0x62, 0x3a, 0xdd, 0xa1, 0x61, 0xe8, 0x5d, 0xe3, 0xd8, 0x32, 0x6d, 0x94,
	0x55, 0xa2, 0x05, 0x95, 0x4c, 0x67, 0xdb, 0x30, 0xf4, 0x16, 0x1e, 0x49, 0xd2, 0x0b, 0xcf, 0x41,
	0x21, 0x2c, 0x23, 0xbf, 0xf4, 0xd1, 0xae, 0x41, 0xc6, 0x56, 0x87, 0x47, 0x4c, 0xf0, 0x61, 0x0a,
	0x7b, 0xe8, 0xb3, 0xb3, 0x0f, 0xfd, 0x5c, 0xc2, 0x43, 0x9f, 0x3b, 0x69, 0x75, 0xc9, 0x9f, 0xa0,
	0xba, 0x14, 0x12, 0x54, 0x17, 0x48, 0x52, 0x5d, 0x8a, 0x09, 0x32, 0x4e, 0x69, 0x76, 0x75, 0x29,
	0x0b, 0xab, 0x0b, 0xf2, 0x29, 0x62, 0x41, 0x52, 0x9c, 0x5a, 0x85, 0x5e, 0x0f, 0xa2, 0x23, 0x31,
	0x4e, 0x78, 0x23, 0x9b, 0x9f, 0x7c, 0x23, 0xab, 0x0a, 0x6f, 0x64, 0x1e, 0x2e, 0x11, 0x64, 0x89,
	0x0b, 0x74, 0xdc, 0x93, 0x01, 0xbc, 0xc8, 0x9b, 0x90, 0xf3, 0xa3, 0xa7, 0x26, 0x37, 0x52, 0x93,
	0x9b, 0xde, 0x80, 0x8b, 0x2f, 0x77, 0xa7, 0x12, 0x95, 0xbb, 0xd3, 0xe2, 0x72, 0x77, 0x1d, 0xe6,
	0xbd, 0xa1, 0xd6, 0x57, 0xcd, 0x81, 0xa1, 0xfb, 0x57, 0x90, 0x33, 0x14, 0x67, 0x25, 0x1c, 0x24,
	0xd7, 0x10, 0xae, 0x3a, 0x9e, 0x9d, 0x50, 0x1d, 0x4d, 0xa8, 0x92, 0x44, 0x19, 0xf4, 0x5f, 0x8e,
	0x25, 0xbf, 0x0d, 0x25, 0xd7, 0xed, 0xe2, 0xe3, 0x4b, 0x35, 0xf8, 0x2f, 0xb2, 0x7b, 0x8d, 0xa6,
	0x80, 0x8b, 0xb3, 0xff, 0xcc, 0xbb, 0xc8, 0xcf, 0x69, 0x28, 0xd1, 0x25, 0xf8, 0xb7, 0xea, 0x90,
	0xb2, 0xe7, 0x24, 0x9f, 0xe0, 0x9c, 0x14, 0x84, 0xe7, 0xe4, 0x99, 0x11, 0x07, 0x3a, 0xbe, 0x4b,
	0xb3, 0xe3, 0xbb, 0x9c, 0x28, 0xbe, 0x05, 0x31, 0x59, 0x99, 0x1c, 0x93, 0x8a, 0x07, 0xa7, 0xfc,
	0xaa, 0x1c, 0xb5, 0x61, 0x8e, 0x25, 0xaf, 0xc2, 0xfc, 0x01, 0x22, 0xc4, 0x42, 0xae, 0x1e, 0x0b,
	0xb9, 0x68, 0x62, 0xf9, 0x20, 0xf8, 0xef, 0xcc, 0xc0, 0xfb, 0x0f, 0x09, 0x8a, 0x4d, 0xcd, 0x35,
	0x9f, 0x9a, 0x6e, 0xf2, 0xb8, 0x0b, 0x0c, 0x9d, 0x9a, 0x6c, 0xe8, 0xf4, 0x4c, 0x43, 0x67, 0x66,
	0x1b, 0x3a, 0xfb, 0xac, 0x86, 0x9e, 0x9b, 0x62, 0xe8, 0x2f, 0xe0, 0x2c, 0x31, 0xb4, 0xbf, 0xed,
	0x71, 0x68, 0xeb, 0x0d, 0x90, 0x55, 0x9f, 0x16, 0x33, 0xf7, 0x39, 0x76, 0x11, 0x94, 0xc9, 0xda,
	0x55, 0x95, 0x12, 0x34, 0xd3, 0xe0, 0x7f, 0x27, 0x41, 0xf6, 0xc9, 0xaf, 0xd4, 0xd4, 0x4a, 0x0f,
	0xe6, 0x89, 0xed, 0x9e, 0x84, 0x46, 0x7b, 0x0d, 0x8a, 0x71, 0x6b, 0x9d, 0x62, 0xe5, 0x10, 0xee,
	0x42, 0x32, 0x03, 0xfd, 0xbd, 0x04, 0xc5, 0x1d, 0xdc, 0xcd, 0x1a, 0xbf, 0x56, 0x33, 0x7d, 0x0a,
	0x67, 0x88, 0x99, 0xa8, 0x7d, 0x60, 0x63, 0xb5, 0x60, 0x81, 0x34, 0xea, 0xc6, 0xac, 0x00, 0xa3,
	0x67, 0xce, 0x8f, 0xa2, 0x1f, 0x33, 0xcd, 0x77, 0x0c, 0x0b, 0xe4, 0xc9, 0x00, 0x4f, 0x55, 0x87,
	0x47, 0xa8, 0xc3, 0xa7, 0x2f, 0x05, 0x92, 0x08, 0x23, 0x5a, 0x82, 0x62, 0x64, 0x64, 0x02, 0x48,
	0x64, 0xda, 0x10, 0xda, 0xd7, 0x49, 0x80, 0x7a, 0x2a, 0xc7, 0x70, 0x3a, 0x00, 0xb0, 0x4e, 0x8e,
	0x59, 0x9c, 0x83, 0xbc, 0x17, 0x00, 0x6e, 0x48, 0x7b, 0xb9, 0x9d, 0xf3, 0x08, 0xd4, 0x36, 0xfb,
	0xce, 0xa3, 0x7c, 0x09, 0xa7, 0x28, 0xcd, 0xa1, 0xb5, 0x29, 0xbf, 0x49, 0x89, 0x32, 0xc9, 0x1b,
	0x3e, 0xa8, 0x1e, 0xae, 0x22, 0xe6, 0x16, 0xea, 0xf1, 0x8e, 0x20, 0xed, 0x18, 0x03, 0xfa, 0x4b,
	0x09, 0x5e, 0xa4, 0x17, 0x70, 0xe2, 0x8b, 0xf4, 0x12, 0xe4, 0x49, 0x96, 0x37, 0x75, 0x16, 0xc3,
	0xc3, 0x54, 0x52, 0xe9, 0xb5, 0x10, 0xc2, 0x0b, 0x3d, 0xae, 0x25, 0x44, 0xf0, 0x3e, 0x83, 0xd3,
	0x1b, 0x86, 0xbb, 0xa1, 0x0e, 0x70, 0x08, 0x35, 0x31, 0xf0, 0x87, 0xd6, 0xd6, 0x80, 0x92, 0x7f,
	0xa7, 0x8b, 0x22, 0xb1, 0xdc, 0x06, 0x72, 0xa5, 0xc3, 0xb6, 0x9f, 0x09, 0x2e, 0xd2, 0x97, 0x8f,
	0xb4, 0xf0, 0xf2, 0xf1, 0x3e, 0x54, 0x58, 0xcd, 0xf4, 0x55, 0x52, 0x12, 0x3c, 0x6d, 0x5c, 0x81,
	0x22, 0x0e, 0x35, 0x1f, 0xb4, 0xa4, 0xd5, 0xc2, 0x41, 0x28, 0x45, 0x79, 0x1f, 0x1f, 0x32, 0x7e,
	0x53, 0x8e, 0x25, 0xbf, 0x0b, 0x45, 0x32, 0x95, 0x3e, 0x5e, 0xe7, 0x59, 0x3f, 0x72, 0xd3, 0x80,
	0x4c, 0xc0, 0xce, 0xfc, 0x10, 0x6a, 0x1b, 0x86, 0x4b, 0xb8, 0x77, 0xfd, 0x3e, 0xa5, 0xe9, 0xb9,
	0x87, 0xc8, 0x60, 0xfe, 0x1b, 0x2b, 0xbd, 0x6a, 0x44, 0x90, 0xaf, 0xc2, 0x7c, 0xe4, 0xe4, 0x28,
	0x7c, 0x22, 0xd4, 0x98, 0x98, 0x53, 0xf9, 0x13, 0x09, 0xe4, 0xb8, 0xe4, 0xc4, 0x29, 0x4e, 0xf5,
	0xdc, 0x43, 0x6c, 0x8f, 0xf0, 0x66, 0x8a, 0x28, 0xf2, 0x4d, 0xa8, 0xaa, 0x7d, 0xdb, 0x50, 0xf5,
	0x71, 0x37, 0x68, 0xac, 0x98, 0x23, 0x3a, 0xef, 0x8f, 0x06, 0x3a, 0x95, 0x23, 0x38, 0x37, 0x61,
	0x8b, 0x8e, 0x25, 0x6f, 0x53, 0x57, 0x26, 0x24, 0x9e, 0xb6, 0x62, 0x83, 0xb5, 0xa2, 0x40, 0x42,
	0xd5, 0xa2, 0x7e, 0xe1, 0x3d, 0xff, 0x8d, 0x04, 0x2f, 0x21, 0x47, 0x05, 0x87, 0x82, 0xb7, 0x29,
	0x1d, 0xfb, 0x92, 0x28, 0xf6, 0x59, 0xeb, 0xa4, 0xc4, 0xd6, 0x41, 0x41, 0x83, 0xa5, 0xe0, 0x16,
	0x94, 0x09, 0x45, 0xc0, 0x03, 0x5b, 0x88, 0x8e, 0x64, 0xa1, 0x36, 0x72, 0xe8, 0x0d, 0x7a, 0x86,
	0xcd, 0x24, 0xfc, 0x42, 0xcf, 0x1b, 0x6f, 0x63, 0xb2, 0xb2, 0x0f, 0xe7, 0x27, 0x2f, 0xd8, 0xb1,
	0x50, 0x2e, 0x44, 0x42, 0x48, 0x5f, 0x2b, 0x51, 0x98, 0x3a, 0x6a, 0x45, 0xc3, 0xfb, 0x21, 0x69,
	0x91, 0x63, 0x2e, 0x23, 0xad, 0x33, 0x92, 0xa5, 0x7c, 0x9d, 0x82, 0x6a, 0xeb, 0xd8, 0x1a, 0xd9,
	0xd3, 0xc0, 0x98, 0x49, 0x20, 0x91, 0xe0, 0x74, 0x04, 0x90, 0x3e, 0x7d, 0x68, 0xd3, 0xa2, 0x43,
	0xbb, 0x02, 0x15, 0x03, 0xeb, 0xed, 0xba, 0xa3, 0x00, 0x29, 0x8a, 0xc2, 0xa5, 0x44, 0xc6, 0x3a,
	0x23, 0xcc, 0x7b, 0x0d, 0x16, 0x22, 0xde, 0x20, 0xb8, 0xb3, 0x38, 0x51, 0x54, 0x02, 0x46, 0x3f,
	0x59, 0xdc, 0x86, 0xd3, 0x0c, 0xab, 0x65, 0xd8, 0x5d, 0x8d, 0xeb, 0xc6, 0xaa, 0xd1, 0x9c, 0x5d,
	0xc3, 0x5e, 0x1b, 0xba, 0xca, 0x43, 0x58, 0xe0, 0x8c, 0xe0, 0x58, 0x6c, 0x26, 0x96, 0x92, 0x67,
	0xe2, 0xcb, 0x50, 0x5a, 0x43, 0xcd, 0x5e, 0x60, 0xcd, 0xd3, 0x90, 0xe2, 0xc2, 0x2a, 0x65, 0xea,
	0xca, 0x0e, 0x94, 0x29, 0x2e, 0xc7, 0x92, 0xef, 0xf9, 0xd5, 0x8d, 0x52, 0x77, 0x91, 0x55, 0x27,
	0xa8, 0x2f, 0xa4, 0xf4, 0x61, 0xb5, 0xef, 0xe1, 0x9c, 0x81, 0x06, 0x02, 0xbe, 0xb6, 0xa1, 0x8d,
	0x6c, 0x3d, 0xa9, 0x43, 0x95, 0x7f, 0x0c, 0x13, 0x43, 0xf8, 0x06, 0x34, 0xdc, 0x1f, 0x4d, 0xcc,
	0x37, 0xd7, 0xa0, 0xac, 0xfb, 0x7c, 0xa4, 0x75, 0xa1, 0xc3, 0xa0, 0x14, 0x0c, 0xe1, 0xe6, 0xe5,
	0x15, 0xa8, 0x84, 0xac, 0xf1, 0x12, 0x12, 0x8a, 0x21, 0x17, 0xe3, 0xe8, 0xd2, 0x98, 0x11, 0x5c,
	0x1a, 0x2f, 0x01, 0xe0, 0x4e, 0x3a, 0xc0, 0xe3, 0xa8, 0xc3, 0x83, 0xe9, 0x48, 0x9f, 0xa2, 0xe3,
	0xd3, 0xce, 0xee, 0x25, 0x30, 0x06, 0xee, 0x80, 0xa2, 0x95, 0xcf, 0x4a, 0x2c, 0xb4, 0x29, 0xa2,
	0x5d, 0x61, 0x83, 0x7f, 0x2b, 0xc1, 0x99, 0xf0, 0x80, 0x32, 0x26, 0x9b, 0x99, 0x4e, 0x7e, 0x21,
	0xdb, 0x29, 0x1f, 0xc1, 0x22, 0x9d, 0x35, 0x04, 0x7b, 0x7f, 0x20, 0xde, 0xfb, 0x25, 0xae, 0x34,
	0x89, 0xb6, 0xc5, 0x6d, 0xff, 0x75, 0xa8, 0xb4, 0x0d, 0x6d, 0xac, 0xf5, 0x8d, 0x93, 0xa4, 0x0d,
	0xe5, 0x1f, 0x52, 0x20, 0x37, 0x75, 0x2c, 0x3c, 0xb8, 0xfc, 0xa0, 0xb9, 0xa8, 0x9d, 0x34, 0xdd,
	0x3e, 0xfb, 0x06, 0x47, 0x48, 0xc8, 0x5a, 0xe1, 0x9d, 0x28, 0xd6, 0x05, 0x94, 0x82, 0x21, 0x9c,
	0x29, 0x2e, 0x40, 0xce, 0x32, 0xb5, 0xae, 0x67, 0xf7, 0x99, 0x36, 0x6d, 0xce, 0x32, 0xb5, 0xc7,
	0x76, 0x1f, 0x39, 0xe6, 0x23, 0x6f, 0x60, 0xe1, 0x71, 0xba, 0x4d, 0xc9, 0x21, 0xaa, 0xcf, 0x60,
	0x6a, 0xa3, 0x21, 0x66, 0xc8, 0xd2, 0x0c, 0x88, 0x8a, 0x18, 0xa8, 0xbe, 0x61, 0x4e, 0xd0, 0x37,
	0xe0, 0x8f, 0xa9, 0x54, 0x94, 0x7d, 0x78, 0x80, 0xbf, 0x80, 0xe9, 0xd8, 0xa5, 0x4b, 0x90, 0x37,
	0x86, 0x7a, 0x1c, 0x69, 0xc8, 0x19, 0x43, 0x3d, 0x21, 0xbc, 0x7f, 0x17, 0xa3, 0xfb, 0xac, 0x11,
	0x1d, 0x0b, 0x55, 0xa0, 0xd0, 0x52, 0x5c, 0xec, 0x41, 0x30, 0xb0, 0xa9, 0x2b, 0x7f, 0x00, 0xf2,
	0xba, 0xd1, 0xe7, 0x5d, 0x90, 0x6c, 0x32, 0xb3, 0xb8, 0x94, 0x70, 0x71, 0x2d, 0x38, 0x17, 0xb5,
	0xff, 0xa1, 0x06, 0xff, 0x1a, 0xb0, 0x0c, 0x55, 0x4a, 0x0b, 0xdd, 0xf3, 0x55, 0x22, 0x25, 0x38,
	0xc0, 0xbe, 0x4e, 0x43, 0x89, 0x96, 0x90, 0x74, 0x81, 0x61, 0x28, 0xa5, 0x12, 0x84, 0x52, 0x3a,
	0x49, 0x28, 0x65, 0xc4, 0xa1, 0x34, 0x3d, 0x52, 0xe8, 0x58, 0x9b, 0x13, 0xc5, 0x1a, 0x15, 0x4a,
	0xb9, 0x99, 0xa1, 0x94, 0x9f, 0x1d, 0x4a, 0x05, 0x51, 0x28, 0x05, 0x20, 0x1b, 0xc4, 0x40, 0x36,
	0xee, 0x2b, 0x97, 0xa2, 0xf8, 0x2b, 0x17, 0xe5, 0xbf, 0x52, 0x20, 0xfb, 0x75, 0x65, 0x6a, 0xb0,
	0x48, 0x42, 0x5f, 0xcc, 0x78, 0xb1, 0x11, 0xbe, 0xcb, 0xa4, 0x4f, 0xfe, 0x2e, 0x93, 0x99, 0xfa,
	0x2e, 0x13, 0x73, 0x3a, 0xfd, 0xb1, 0x0b, 0xeb, 0xf4, 0xd8, 0x23, 0x0d, 0x7d, 0xc8, 0x85, 0x8f,
	0x34, 0x39, 0xba, 0x15, 0x16, 0x3d, 0xd2, 0xe4, 0x93, 0x3c, 0xd2, 0x14, 0x44, 0x8f, 0x34, 0xca,
	0x67, 0x18, 0x8f, 0x8b, 0x9d, 0xf2, 0x07, 0x20, 0x93, 0xcb, 0x49, 0xb0, 0xa9, 0xc9, 0x90, 0x1c,
	0x33, 0xb7, 0x7a, 0x40, 0xfd, 0x9a, 0x79, 0x89, 0xff, 0x04, 0x2a, 0x01, 0xef, 0xaa, 0x3a, 0x1c,
	0x1a, 0x36, 0x7d, 0x22, 0xa4, 0x19, 0xc9, 0x35, 0x25, 0x0a, 0x78, 0x2e, 0x66, 0xd2, 0x13, 0xb2,
	0x13, 0xb9, 0x54, 0xb1, 0xba, 0x83, 0x4b, 0x55, 0x0f, 0xff, 0x9a, 0x72, 0xa9, 0xe2, 0xa6, 0x01,
	0x99, 0x80, 0xf3, 0xc9, 0x2e, 0x2c, 0x04, 0x6d, 0xf4, 0x09, 0x81, 0x01, 0xbf, 0x05, 0x4a, 0x71,
	0x2d, 0x90, 0xf2, 0x95, 0x04, 0x32, 0x2f, 0xd2, 0xb1, 0xe4, 0xdb, 0x7e, 0x27, 0x67, 0x0e, 0xf7,
	0x47, 0xfe, 0xad, 0x7f, 0x22, 0x38, 0x8f, 0xfb, 0x37, 0xdc, 0x34, 0x04, 0xed, 0x26, 0x9e, 0x95,
	0xc2, 0xb3, 0x66, 0xb5, 0x9b, 0x68, 0x9e, 0x72, 0x0c, 0xa7, 0x83, 0x35, 0xfc, 0xdf, 0x5e, 0xfa,
	0x95, 0x6f, 0x24, 0x38, 0x23, 0x50, 0xed, 0x58, 0xf2, 0x9b, 0x71, 0x0b, 0x4c, 0xc3, 0x8a, 0xe3,
	0x46, 0x38, 0x29, 0xfa, 0x71, 0x1b, 0xca, 0xbb, 0xa6, 0x76, 0x74, 0x32, 0xbf, 0x2a, 0xef, 0x60,
	0x4c, 0xc2, 0x6f, 0xf4, 0xfc, 0x67, 0xb5, 0xc4, 0x93, 0x7f, 0x0f, 0x87, 0x29, 0x3f, 0xd9, 0xb1,
	0x50, 0x66, 0xf0, 0x1c, 0x83, 0xad, 0x4f, 0x98, 0x82, 0x0e, 0x00, 0x3e, 0x5b, 0xfe, 0xb5, 0x8d,
	0x41, 0x15, 0xf0, 0x00, 0x16, 0xb2, 0x72, 0x0b, 0x4a, 0x4d, 0x3a, 0x29, 0xcd, 0x43, 0x71, 0x63,
	0xf3, 0x7e, 0xa7, 0xbb, 0xda, 0xdc, 0xde, 0x6e, 0xb5, 0xab, 0x92, 0xbc, 0x00, 0x65, 0x4c, 0x68,
	0xae, 0x75, 0x36, 0xdf, 0xdf, 0xec, 0x3c, 0xa9, 0xa6, 0x56, 0x3e, 0x80, 0xdc, 0x9e, 0x7f, 0xb3,
	0x2a, 0x42, 0xae, 0xd3, 0xe9, 0x22, 0x86, 0xaa, 0x24, 0x57, 0x00, 0x36, 0x1e, 0x6f, 0x6e, 0xad,
	0x93, 0xdf, 0x29, 0x34, 0x35, 0x98, 0x45, 0x48, 0x69, 0x19, 0x60, 0xce, 0xff, 0x7f, 0x46, 0xae,
	0x42, 0x69, 0x67, 0xb7, 0xd5, 0x6e, 0x76, 0x5a, 0x84, 0x92, 0x5d, 0x79, 0x00, 0xc5, 0x1d, 0x2a,
	0xed, 0x55, 0x00, 0x76, 0xdb, 0x3b, 0xeb, 0x8f, 0xd7, 0x3a, 0xdd, 0xcd, 0xf5, 0xaa, 0x24, 0xe7,
	0x21, 0xd3, 0x6e, 0x6e, 0x3f, 0xac, 0xa6, 0xe4, 0x02, 0x64, 0xf7, 0x3a, 0x3b, 0x6b, 0x0f, 0x89,
	0xc4, 0x76, 0xeb, 0x51, 0x73, 0x73, 0xbb, 0x9a, 0x41, 0x0c, 0xdb, 0xcd, 0x47, 0xad, 0x6a, 0x76,
	0xe5, 0x6d, 0xa8, 0xf8, 0x97, 0x75, 0x1f, 0x96, 0x91, 0x17, 0xa0, 0xd4, 0xdc, 0xda, 0xea, 0xee,
	0x6e, 0x35, 0x3b, 0xf7, 0x77, 0xda, 0x8f, 0xaa, 0x3f, 0x4b, 0x68, 0xf1, 0xcd, 0xed, 0xf5, 0xf6,
	0x0e, 0x16, 0x9e, 0x83, 0xf4, 0xe6, 0xce, 0x5e, 0x35, 0xb5, 0x72, 0x17, 0xce, 0xb6, 0xc2, 0x33,
	0xb3, 0x4b, 0x3d, 0x96, 0xca, 0x0a, 0x2c, 0xee, 0x3d, 0x5e, 0xed, 0x76, 0x3a, 0xdd, 0xdd, 0xc7,
	0xed, 0xb5, 0x07, 0xcd, 0xbd, 0x56, 0x77, 0x6d, 0x67, 0x7b, 0xbd, 0xbb, 0xb7, 0xfe, 0xb0, 0xdb,
	0xdc, 0xda, 0xda, 0xf9, 0xa0, 0x2a, 0xdd, 0xfa, 0xab, 0x06, 0x30, 0xdf, 0xc9, 0xcb, 0x2a, 0x94,
	0x99, 0xaf, 0xa1, 0xe5, 0x45, 0x36, 0xc6, 0xf8, 0x4f, 0xd5, 0xeb, 0x4b, 0x53, 0xc7, 0x1d, 0x4b,
	0x99, 0xff, 0xe3, 0xef, 0x7e, 0x4a, 0x4b, 0x7f, 0xfa, 0xdd, 0x4f, 0xe9, 0x17, 0xfe, 0x0c, 0xfd,
	0x23, 0xff, 0x21, 0x94, 0x99, 0xaf, 0xa6, 0x79, 0x15, 0xfc, 0x27, 0xd5, 0xf5, 0xf3, 0x37, 0xc2,
	0xaf, 0xf6, 0x6f, 0xec, 0x3d, 0x5c, 0x25, 0x5f, 0xed, 0xb7, 0x06, 0x96, 0x3b, 0xee, 0xee, 0xae,
	0x12, 0xf9, 0x29, 0x4a, 0xbe, 0x0a, 0x65, 0xe6, 0x1b, 0x45, 0x5e, 0x3e, 0xff, 0xd9, 0x29, 0xbf,
	0x85, 0xd8, 0x07, 0x8e, 0x44, 0x45, 0x9a, 0x52, 0xf1, 0xfb, 0x50, 0xa2, 0x3f, 0x06, 0x94, 0x2f,
	0x88, 0x6f, 0xa3, 0xfe, 0xb7, 0x71, 0x49, 0x36, 0x90, 0xa1, 0xa4, 0xef, 0x40, 0xce, 0xff, 0x66,
	0x4d, 0xae, 0x71, 0x29, 0x3c, 0xfc, 0xde, 0xae, 0x7e, 0x6e, 0xc2, 0x48, 0xb0, 0xdc, 0x2c, 0x25,
	0xb0, 0x07, 0xf3, 0xdc, 0xc7, 0x6a, 0x32, 0x77, 0xa3, 0x8b, 0x7f, 0xcb, 0x96, 0x64, 0xd1, 0xaf,
	0x51, 0x3a, 0x0e, 0xa9, 0x0f, 0xe2, 0xa8, 0x77, 0xd2, 0x09, 0x6a, 0xe8, 0x74, 0x9c, 0x44, 0xd3,
	0x6d, 0x4a, 0x53, 0x1f, 0x4a, 0xf4, 0xbb, 0xaf, 0xfc, 0x12, 0x57, 0xd7, 0xe9, 0x8f, 0x67, 0xea,
	0x8b, 0xa2, 0xc1, 0xa8, 0x20, 0x29, 0x4b, 0x48, 0xfa, 0xeb, 0x48, 0x7a, 0xca, 0xba, 0x83, 0xe4,
	0x57, 0xae, 0x5b, 0x77, 0xa3, 0x0c, 0x77, 0x4f, 0x0e, 0xdf, 0x54, 0xa2, 0x4d, 0x4d, 0x55, 0x78,
	0x51, 0x38, 0x48, 0x97, 0x00, 0xb2, 0xa3, 0x37, 0xa8, 0x1d, 0x1d, 0xe3, 0x3e, 0x92, 0x7b, 0xf3,
	0x9a, 0xae, 0xe6, 0xb2, 0x68, 0x90, 0x7f, 0x32, 0x23, 0xbb, 0x7b, 0x73, 0xca, 0xee, 0x3e, 0x84,
	0x22, 0xf5, 0x62, 0x34, 0x5d, 0xe5, 0x05, 0xd1, 0xe0, 0x13, 0x76, 0x57, 0x6f, 0x51, 0xbb, 0xfa,
	0x23, 0xa8, 0xb0, 0x4f, 0x1d, 0xf2, 0x92, 0xe8, 0xa0, 0x53, 0x0f, 0x21, 0x49, 0x22, 0xe1, 0x6d,
	0x4a, 0xc3, 0x11, 0x2c, 0xc4, 0x9e, 0x34, 0x64, 0x65, 0x22, 0x32, 0x14, 0xc5, 0xf6, 0x6c, 0xf4,
	0x88, 0x28, 0xbb, 0x43, 0x29, 0xfb, 0x98, 0x79, 0x3f, 0x89, 0xa2, 0xe1, 0xca, 0x64, 0x59, 0x74,
	0x90, 0x27, 0x55, 0xf9, 0x0e, 0xa5, 0x72, 0x00, 0x0b, 0x31, 0x0c, 0x9d, 0xdf, 0x9f, 0xe8, 0xe5,
	0xa0, 0x7e, 0x69, 0x26, 0x4f, 0xa0, 0xee, 0x2e, 0xa5, 0x6e, 0x4c, 0x95, 0x6d, 0x06, 0x00, 0xbf,
	0x1a, 0x13, 0x27, 0xc4, 0xdf, 0xeb, 0x2f, 0x27, 0xe2, 0x0b, 0x54, 0xbf, 0x4b, 0xa9, 0xfe, 0x56,
	0x82, 0x32, 0x03, 0x33, 0xf2, 0x49, 0x9b, 0x07, 0x62, 0xf9, 0xa4, 0x1d, 0xc3, 0x28, 0x95, 0x7b,
	0x48, 0xc7, 0x3d, 0xa4, 0x63, 0xce, 0xba, 0x33, 0xbc, 0xe3, 0xe2, 0xf8, 0xbf, 0x76, 0xdd, 0x6a,
	0xd0, 0x07, 0xa0, 0x71, 0x7d, 0xd8, 0xb8, 0xab, 0x0e, 0xb0, 0x51, 0xee, 0x35, 0xae, 0xbb, 0x8d,
	0xbb, 0x8e, 0xd7, 0x43, 0xb7, 0x9b, 0x7b, 0xf2, 0x57, 0x12, 0x41, 0x0d, 0x45, 0x38, 0xb3, 0x7c,
	0x2d, 0x6e, 0xe0, 0x09, 0x00, 0x7a, 0x7d, 0x25, 0x29, 0x6b, 0x60, 0x97, 0xf7, 0x98, 0x33, 0x54,
	0xc4, 0x50, 0xa8, 0x1f, 0xdb, 0x5c, 0xa7, 0x48, 0x63, 0xa9, 0xf5, 0x97, 0x26, 0x8e, 0x39, 0x96,
	0x52, 0x43, 0x82, 0x7f, 0x17, 0x27, 0x02, 0x0f, 0x1b, 0x22, 0x77, 0xdd, 0xbb, 0xeb, 0x91, 0x0c,
	0x50, 0x21, 0xac, 0x61, 0x40, 0x3f, 0xb3, 0x12, 0xbc, 0xfa, 0x26, 0xb5, 0xfa, 0x2f, 0xe0, 0xc5,
	0x09, 0x60, 0xa3, 0x20, 0xa4, 0x84, 0xf0, 0x6c, 0xfd, 0xda, 0x84, 0x90, 0x8a, 0xe3, 0x77, 0x44,
	0xfd, 0x2a, 0xa5, 0xfe, 0x6b, 0x09, 0xea, 0x93, 0x31, 0xbf, 0xc4, 0x4b, 0x78, 0x75, 0xb2, 0x03,
	0x27, 0xad, 0x62, 0x8d, 0x5a, 0xc5, 0x87, 0x50, 0xa4, 0xc0, 0x40, 0x99, 0xbb, 0x94, 0xb1, 0x38,
	0x61, 0x92, 0x04, 0xb8, 0x4e, 0xc9, 0x7e, 0x0a, 0xe7, 0x63, 0xf9, 0xed, 0x3e, 0xfe, 0xa8, 0x95,
	0xc0, 0x42, 0xcf, 0x33, 0x17, 0xb6, 0x28, 0xbd, 0x9f, 0xc3, 0x92, 0x28, 0xcf, 0xd1, 0xaa, 0x9f,
	0x73, 0x5a, 0xbc, 0x4f, 0x69, 0xdf, 0xc7, 0x69, 0x91, 0x7d, 0xbf, 0x9f, 0x5e, 0xba, 0x2e, 0x89,
	0x06, 0xb9, 0xd7, 0x7f, 0xa2, 0x67, 0x83, 0xd2, 0x73, 0x00, 0xf3, 0x1c, 0x92, 0xc8, 0xb7, 0x4d,
	0x71, 0xb4, 0x96, 0xdf, 0x90, 0x00, 0x8a, 0x24, 0x8a, 0x74, 0xb6, 0x3f, 0xe3, 0x40, 0x47, 0x5e,
	0x51, 0x1c, 0x93, 0x4c, 0x12, 0x2a, 0x06, 0xa5, 0xc3, 0x82, 0xb3, 0x62, 0xe4, 0x51, 0x7e, 0x79,
	0x52, 0x55, 0xe6, 0xf0, 0xc9, 0x24, 0x1a, 0xf7, 0x59, 0xf3, 0x71, 0x10, 0x0d, 0xbf, 0xab, 0x38,
	0x78, 0x26, 0x68, 0x9f, 0xc4, 0xe6, 0x3b, 0x60, 0x14, 0x2d, 0xc4, 0x50, 0x11, 0x79, 0xea, 0x62,
	0x05, 0x01, 0x11, 0x07, 0x55, 0x88, 0xa2, 0x43, 0x4a, 0x91, 0x0e, 0x15, 0x16, 0xd3, 0xe0, 0x3b,
	0x9a, 0x18, 0x88, 0x52, 0x6f, 0x4c, 0x67, 0x08, 0xb4, 0x1c, 0xb3, 0x55, 0x3f, 0x06, 0x1d, 0xf0,
	0x27, 0x59, 0x04, 0x6b, 0xf0, 0x9b, 0x12, 0xe2, 0x0f, 0x44, 0xdd, 0x98, 0xb9, 0xcb, 0x40, 0x84,
	0x0f, 0xf0, 0xc7, 0x88, 0x41, 0x0e, 0x92, 0x6e, 0xe6, 0x53, 0xe6, 0x5a, 0x70, 0x9a, 0xff, 0xd6,
	0x1e, 0xa3, 0x19, 0xf1, 0x63, 0xc2, 0x7f, 0x8f, 0x9f, 0x24, 0xdc, 0x3e, 0x63, 0xc3, 0x8d, 0xfb,
	0x93, 0x2f, 0x3e, 0xdc, 0xe2, 0x7f, 0x4e, 0xc6, 0x87, 0x9b, 0xe0, 0x6f, 0xc6, 0x88, 0xa2, 0xcf,
	0xd9, 0x28, 0xd8, 0x5b, 0x7f, 0xd8, 0xb4, 0xac, 0xfe, 0xf8, 0x39, 0x47, 0xc1, 0x17, 0x94, 0x96,
	0x2f, 0x71, 0x50, 0xb3, 0x18, 0x8a, 0xa0, 0xf7, 0x8b, 0x21, 0x34, 0x82, 0xd0, 0x8e, 0x03, 0x31,
	0x4a, 0x03, 0xa9, 0xfb, 0x4a, 0x9a, 0x7c, 0x33, 0xa8, 0xcf, 0x7d, 0x83, 0x38, 0xc6, 0xab, 0xd5,
	0xef, 0x7f, 0x58, 0x94, 0xfe, 0xe9, 0x87, 0x45, 0xe9, 0xdf, 0x7e, 0x58, 0x94, 0xbe, 0xfd, 0x71,
	0xf1, 0x85, 0xff, 0x09, 0x00, 0x00, 0xff, 0xff, 0xf9, 0x06, 0x1e, 0x19, 0x81, 0x3f, 0x00, 0x00,
}
