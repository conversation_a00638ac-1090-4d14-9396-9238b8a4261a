// Code generated by protoc-gen-go. DO NOT EDIT.
// source: exchange/exchange-glory.proto

package Exchange // import "golang.52tt.com/protocol/services/Exchange"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type FillAllItemReq struct {
	TimeNow              int64    `protobuf:"varint,1,opt,name=time_now,json=timeNow,proto3" json:"time_now,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FillAllItemReq) Reset()         { *m = FillAllItemReq{} }
func (m *FillAllItemReq) String() string { return proto.CompactTextString(m) }
func (*FillAllItemReq) ProtoMessage()    {}
func (*FillAllItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{0}
}
func (m *FillAllItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FillAllItemReq.Unmarshal(m, b)
}
func (m *FillAllItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FillAllItemReq.Marshal(b, m, deterministic)
}
func (dst *FillAllItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FillAllItemReq.Merge(dst, src)
}
func (m *FillAllItemReq) XXX_Size() int {
	return xxx_messageInfo_FillAllItemReq.Size(m)
}
func (m *FillAllItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FillAllItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_FillAllItemReq proto.InternalMessageInfo

func (m *FillAllItemReq) GetTimeNow() int64 {
	if m != nil {
		return m.TimeNow
	}
	return 0
}

type PushFeishuReq struct {
	TimeNow              int64    `protobuf:"varint,1,opt,name=time_now,json=timeNow,proto3" json:"time_now,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushFeishuReq) Reset()         { *m = PushFeishuReq{} }
func (m *PushFeishuReq) String() string { return proto.CompactTextString(m) }
func (*PushFeishuReq) ProtoMessage()    {}
func (*PushFeishuReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{1}
}
func (m *PushFeishuReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushFeishuReq.Unmarshal(m, b)
}
func (m *PushFeishuReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushFeishuReq.Marshal(b, m, deterministic)
}
func (dst *PushFeishuReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushFeishuReq.Merge(dst, src)
}
func (m *PushFeishuReq) XXX_Size() int {
	return xxx_messageInfo_PushFeishuReq.Size(m)
}
func (m *PushFeishuReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushFeishuReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushFeishuReq proto.InternalMessageInfo

func (m *PushFeishuReq) GetTimeNow() int64 {
	if m != nil {
		return m.TimeNow
	}
	return 0
}

type GetExchangeTransactionSumReq struct {
	BeginTime            int64    `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExchangeTransactionSumReq) Reset()         { *m = GetExchangeTransactionSumReq{} }
func (m *GetExchangeTransactionSumReq) String() string { return proto.CompactTextString(m) }
func (*GetExchangeTransactionSumReq) ProtoMessage()    {}
func (*GetExchangeTransactionSumReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{2}
}
func (m *GetExchangeTransactionSumReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExchangeTransactionSumReq.Unmarshal(m, b)
}
func (m *GetExchangeTransactionSumReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExchangeTransactionSumReq.Marshal(b, m, deterministic)
}
func (dst *GetExchangeTransactionSumReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExchangeTransactionSumReq.Merge(dst, src)
}
func (m *GetExchangeTransactionSumReq) XXX_Size() int {
	return xxx_messageInfo_GetExchangeTransactionSumReq.Size(m)
}
func (m *GetExchangeTransactionSumReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExchangeTransactionSumReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExchangeTransactionSumReq proto.InternalMessageInfo

func (m *GetExchangeTransactionSumReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetExchangeTransactionSumReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type ExchangeTransactionSumData struct {
	TargetType           uint32   `protobuf:"varint,1,opt,name=target_type,json=targetType,proto3" json:"target_type,omitempty"`
	TargetItemId         string   `protobuf:"bytes,2,opt,name=target_item_id,json=targetItemId,proto3" json:"target_item_id,omitempty"`
	ItemName             string   `protobuf:"bytes,3,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	TargetCount          uint32   `protobuf:"varint,4,opt,name=target_count,json=targetCount,proto3" json:"target_count,omitempty"`
	TargetUidCount       uint32   `protobuf:"varint,5,opt,name=target_uid_count,json=targetUidCount,proto3" json:"target_uid_count,omitempty"`
	TargetValue          uint64   `protobuf:"varint,6,opt,name=target_value,json=targetValue,proto3" json:"target_value,omitempty"`
	CostFragmentType     uint32   `protobuf:"varint,7,opt,name=cost_fragment_type,json=costFragmentType,proto3" json:"cost_fragment_type,omitempty"`
	CostFragmentCount    uint64   `protobuf:"varint,8,opt,name=cost_fragment_count,json=costFragmentCount,proto3" json:"cost_fragment_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExchangeTransactionSumData) Reset()         { *m = ExchangeTransactionSumData{} }
func (m *ExchangeTransactionSumData) String() string { return proto.CompactTextString(m) }
func (*ExchangeTransactionSumData) ProtoMessage()    {}
func (*ExchangeTransactionSumData) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{3}
}
func (m *ExchangeTransactionSumData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExchangeTransactionSumData.Unmarshal(m, b)
}
func (m *ExchangeTransactionSumData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExchangeTransactionSumData.Marshal(b, m, deterministic)
}
func (dst *ExchangeTransactionSumData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExchangeTransactionSumData.Merge(dst, src)
}
func (m *ExchangeTransactionSumData) XXX_Size() int {
	return xxx_messageInfo_ExchangeTransactionSumData.Size(m)
}
func (m *ExchangeTransactionSumData) XXX_DiscardUnknown() {
	xxx_messageInfo_ExchangeTransactionSumData.DiscardUnknown(m)
}

var xxx_messageInfo_ExchangeTransactionSumData proto.InternalMessageInfo

func (m *ExchangeTransactionSumData) GetTargetType() uint32 {
	if m != nil {
		return m.TargetType
	}
	return 0
}

func (m *ExchangeTransactionSumData) GetTargetItemId() string {
	if m != nil {
		return m.TargetItemId
	}
	return ""
}

func (m *ExchangeTransactionSumData) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *ExchangeTransactionSumData) GetTargetCount() uint32 {
	if m != nil {
		return m.TargetCount
	}
	return 0
}

func (m *ExchangeTransactionSumData) GetTargetUidCount() uint32 {
	if m != nil {
		return m.TargetUidCount
	}
	return 0
}

func (m *ExchangeTransactionSumData) GetTargetValue() uint64 {
	if m != nil {
		return m.TargetValue
	}
	return 0
}

func (m *ExchangeTransactionSumData) GetCostFragmentType() uint32 {
	if m != nil {
		return m.CostFragmentType
	}
	return 0
}

func (m *ExchangeTransactionSumData) GetCostFragmentCount() uint64 {
	if m != nil {
		return m.CostFragmentCount
	}
	return 0
}

type GetExchangeTransactionSumResp struct {
	List                 []*ExchangeTransactionSumData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetExchangeTransactionSumResp) Reset()         { *m = GetExchangeTransactionSumResp{} }
func (m *GetExchangeTransactionSumResp) String() string { return proto.CompactTextString(m) }
func (*GetExchangeTransactionSumResp) ProtoMessage()    {}
func (*GetExchangeTransactionSumResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{4}
}
func (m *GetExchangeTransactionSumResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExchangeTransactionSumResp.Unmarshal(m, b)
}
func (m *GetExchangeTransactionSumResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExchangeTransactionSumResp.Marshal(b, m, deterministic)
}
func (dst *GetExchangeTransactionSumResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExchangeTransactionSumResp.Merge(dst, src)
}
func (m *GetExchangeTransactionSumResp) XXX_Size() int {
	return xxx_messageInfo_GetExchangeTransactionSumResp.Size(m)
}
func (m *GetExchangeTransactionSumResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExchangeTransactionSumResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExchangeTransactionSumResp proto.InternalMessageInfo

func (m *GetExchangeTransactionSumResp) GetList() []*ExchangeTransactionSumData {
	if m != nil {
		return m.List
	}
	return nil
}

type GetExchangeHistoryTypeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExchangeHistoryTypeReq) Reset()         { *m = GetExchangeHistoryTypeReq{} }
func (m *GetExchangeHistoryTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetExchangeHistoryTypeReq) ProtoMessage()    {}
func (*GetExchangeHistoryTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{5}
}
func (m *GetExchangeHistoryTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExchangeHistoryTypeReq.Unmarshal(m, b)
}
func (m *GetExchangeHistoryTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExchangeHistoryTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetExchangeHistoryTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExchangeHistoryTypeReq.Merge(dst, src)
}
func (m *GetExchangeHistoryTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetExchangeHistoryTypeReq.Size(m)
}
func (m *GetExchangeHistoryTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExchangeHistoryTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExchangeHistoryTypeReq proto.InternalMessageInfo

func (m *GetExchangeHistoryTypeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetExchangeHistoryTypeResp struct {
	ExchangeTypeList     []uint32 `protobuf:"varint,1,rep,packed,name=exchange_type_list,json=exchangeTypeList,proto3" json:"exchange_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExchangeHistoryTypeResp) Reset()         { *m = GetExchangeHistoryTypeResp{} }
func (m *GetExchangeHistoryTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetExchangeHistoryTypeResp) ProtoMessage()    {}
func (*GetExchangeHistoryTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{6}
}
func (m *GetExchangeHistoryTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExchangeHistoryTypeResp.Unmarshal(m, b)
}
func (m *GetExchangeHistoryTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExchangeHistoryTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetExchangeHistoryTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExchangeHistoryTypeResp.Merge(dst, src)
}
func (m *GetExchangeHistoryTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetExchangeHistoryTypeResp.Size(m)
}
func (m *GetExchangeHistoryTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExchangeHistoryTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExchangeHistoryTypeResp proto.InternalMessageInfo

func (m *GetExchangeHistoryTypeResp) GetExchangeTypeList() []uint32 {
	if m != nil {
		return m.ExchangeTypeList
	}
	return nil
}

type GetCarouselResp struct {
	List                 []*CarouselData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetCarouselResp) Reset()         { *m = GetCarouselResp{} }
func (m *GetCarouselResp) String() string { return proto.CompactTextString(m) }
func (*GetCarouselResp) ProtoMessage()    {}
func (*GetCarouselResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{7}
}
func (m *GetCarouselResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCarouselResp.Unmarshal(m, b)
}
func (m *GetCarouselResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCarouselResp.Marshal(b, m, deterministic)
}
func (dst *GetCarouselResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCarouselResp.Merge(dst, src)
}
func (m *GetCarouselResp) XXX_Size() int {
	return xxx_messageInfo_GetCarouselResp.Size(m)
}
func (m *GetCarouselResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCarouselResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCarouselResp proto.InternalMessageInfo

func (m *GetCarouselResp) GetList() []*CarouselData {
	if m != nil {
		return m.List
	}
	return nil
}

type CarouselData struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nick                 string   `protobuf:"bytes,2,opt,name=nick,proto3" json:"nick,omitempty"`
	ItemName             string   `protobuf:"bytes,3,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	ItemIcon             string   `protobuf:"bytes,4,opt,name=item_icon,json=itemIcon,proto3" json:"item_icon,omitempty"`
	Count                uint32   `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	FakeUid              uint32   `protobuf:"varint,6,opt,name=fake_uid,json=fakeUid,proto3" json:"fake_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CarouselData) Reset()         { *m = CarouselData{} }
func (m *CarouselData) String() string { return proto.CompactTextString(m) }
func (*CarouselData) ProtoMessage()    {}
func (*CarouselData) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{8}
}
func (m *CarouselData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CarouselData.Unmarshal(m, b)
}
func (m *CarouselData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CarouselData.Marshal(b, m, deterministic)
}
func (dst *CarouselData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CarouselData.Merge(dst, src)
}
func (m *CarouselData) XXX_Size() int {
	return xxx_messageInfo_CarouselData.Size(m)
}
func (m *CarouselData) XXX_DiscardUnknown() {
	xxx_messageInfo_CarouselData.DiscardUnknown(m)
}

var xxx_messageInfo_CarouselData proto.InternalMessageInfo

func (m *CarouselData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CarouselData) GetNick() string {
	if m != nil {
		return m.Nick
	}
	return ""
}

func (m *CarouselData) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *CarouselData) GetItemIcon() string {
	if m != nil {
		return m.ItemIcon
	}
	return ""
}

func (m *CarouselData) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *CarouselData) GetFakeUid() uint32 {
	if m != nil {
		return m.FakeUid
	}
	return 0
}

type ExchangeHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ExchangeType         uint32   `protobuf:"varint,2,opt,name=exchange_type,json=exchangeType,proto3" json:"exchange_type,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExchangeHistoryReq) Reset()         { *m = ExchangeHistoryReq{} }
func (m *ExchangeHistoryReq) String() string { return proto.CompactTextString(m) }
func (*ExchangeHistoryReq) ProtoMessage()    {}
func (*ExchangeHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{9}
}
func (m *ExchangeHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExchangeHistoryReq.Unmarshal(m, b)
}
func (m *ExchangeHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExchangeHistoryReq.Marshal(b, m, deterministic)
}
func (dst *ExchangeHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExchangeHistoryReq.Merge(dst, src)
}
func (m *ExchangeHistoryReq) XXX_Size() int {
	return xxx_messageInfo_ExchangeHistoryReq.Size(m)
}
func (m *ExchangeHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExchangeHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExchangeHistoryReq proto.InternalMessageInfo

func (m *ExchangeHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExchangeHistoryReq) GetExchangeType() uint32 {
	if m != nil {
		return m.ExchangeType
	}
	return 0
}

func (m *ExchangeHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *ExchangeHistoryReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type ExchangeHistoryData struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	FragmentType         uint32   `protobuf:"varint,3,opt,name=fragment_type,json=fragmentType,proto3" json:"fragment_type,omitempty"`
	FragmentAmount       uint32   `protobuf:"varint,4,opt,name=fragment_amount,json=fragmentAmount,proto3" json:"fragment_amount,omitempty"`
	TargetType           uint32   `protobuf:"varint,5,opt,name=target_type,json=targetType,proto3" json:"target_type,omitempty"`
	TargetItemId         string   `protobuf:"bytes,6,opt,name=target_item_id,json=targetItemId,proto3" json:"target_item_id,omitempty"`
	TargetAmount         uint32   `protobuf:"varint,7,opt,name=target_amount,json=targetAmount,proto3" json:"target_amount,omitempty"`
	CreateTime           uint32   `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	TargetItemName       string   `protobuf:"bytes,9,opt,name=target_item_name,json=targetItemName,proto3" json:"target_item_name,omitempty"`
	TargetItemIcon       string   `protobuf:"bytes,10,opt,name=target_item_icon,json=targetItemIcon,proto3" json:"target_item_icon,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,11,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExchangeHistoryData) Reset()         { *m = ExchangeHistoryData{} }
func (m *ExchangeHistoryData) String() string { return proto.CompactTextString(m) }
func (*ExchangeHistoryData) ProtoMessage()    {}
func (*ExchangeHistoryData) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{10}
}
func (m *ExchangeHistoryData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExchangeHistoryData.Unmarshal(m, b)
}
func (m *ExchangeHistoryData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExchangeHistoryData.Marshal(b, m, deterministic)
}
func (dst *ExchangeHistoryData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExchangeHistoryData.Merge(dst, src)
}
func (m *ExchangeHistoryData) XXX_Size() int {
	return xxx_messageInfo_ExchangeHistoryData.Size(m)
}
func (m *ExchangeHistoryData) XXX_DiscardUnknown() {
	xxx_messageInfo_ExchangeHistoryData.DiscardUnknown(m)
}

var xxx_messageInfo_ExchangeHistoryData proto.InternalMessageInfo

func (m *ExchangeHistoryData) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ExchangeHistoryData) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ExchangeHistoryData) GetFragmentType() uint32 {
	if m != nil {
		return m.FragmentType
	}
	return 0
}

func (m *ExchangeHistoryData) GetFragmentAmount() uint32 {
	if m != nil {
		return m.FragmentAmount
	}
	return 0
}

func (m *ExchangeHistoryData) GetTargetType() uint32 {
	if m != nil {
		return m.TargetType
	}
	return 0
}

func (m *ExchangeHistoryData) GetTargetItemId() string {
	if m != nil {
		return m.TargetItemId
	}
	return ""
}

func (m *ExchangeHistoryData) GetTargetAmount() uint32 {
	if m != nil {
		return m.TargetAmount
	}
	return 0
}

func (m *ExchangeHistoryData) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ExchangeHistoryData) GetTargetItemName() string {
	if m != nil {
		return m.TargetItemName
	}
	return ""
}

func (m *ExchangeHistoryData) GetTargetItemIcon() string {
	if m != nil {
		return m.TargetItemIcon
	}
	return ""
}

func (m *ExchangeHistoryData) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type ExchangeHistoryResp struct {
	List                 []*ExchangeHistoryData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ExchangeHistoryResp) Reset()         { *m = ExchangeHistoryResp{} }
func (m *ExchangeHistoryResp) String() string { return proto.CompactTextString(m) }
func (*ExchangeHistoryResp) ProtoMessage()    {}
func (*ExchangeHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{11}
}
func (m *ExchangeHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExchangeHistoryResp.Unmarshal(m, b)
}
func (m *ExchangeHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExchangeHistoryResp.Marshal(b, m, deterministic)
}
func (dst *ExchangeHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExchangeHistoryResp.Merge(dst, src)
}
func (m *ExchangeHistoryResp) XXX_Size() int {
	return xxx_messageInfo_ExchangeHistoryResp.Size(m)
}
func (m *ExchangeHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExchangeHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExchangeHistoryResp proto.InternalMessageInfo

func (m *ExchangeHistoryResp) GetList() []*ExchangeHistoryData {
	if m != nil {
		return m.List
	}
	return nil
}

type ExchangeItemReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExchangeItemReq) Reset()         { *m = ExchangeItemReq{} }
func (m *ExchangeItemReq) String() string { return proto.CompactTextString(m) }
func (*ExchangeItemReq) ProtoMessage()    {}
func (*ExchangeItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{12}
}
func (m *ExchangeItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExchangeItemReq.Unmarshal(m, b)
}
func (m *ExchangeItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExchangeItemReq.Marshal(b, m, deterministic)
}
func (dst *ExchangeItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExchangeItemReq.Merge(dst, src)
}
func (m *ExchangeItemReq) XXX_Size() int {
	return xxx_messageInfo_ExchangeItemReq.Size(m)
}
func (m *ExchangeItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExchangeItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExchangeItemReq proto.InternalMessageInfo

func (m *ExchangeItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExchangeItemReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ExchangeItemReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type DelExchangeConfigReq struct {
	IdList               []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelExchangeConfigReq) Reset()         { *m = DelExchangeConfigReq{} }
func (m *DelExchangeConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelExchangeConfigReq) ProtoMessage()    {}
func (*DelExchangeConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{13}
}
func (m *DelExchangeConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelExchangeConfigReq.Unmarshal(m, b)
}
func (m *DelExchangeConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelExchangeConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelExchangeConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelExchangeConfigReq.Merge(dst, src)
}
func (m *DelExchangeConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelExchangeConfigReq.Size(m)
}
func (m *DelExchangeConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelExchangeConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelExchangeConfigReq proto.InternalMessageInfo

func (m *DelExchangeConfigReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type GetExchangeItemNameReq struct {
	ExchangeType         uint32   `protobuf:"varint,1,opt,name=exchange_type,json=exchangeType,proto3" json:"exchange_type,omitempty"`
	ItemId               string   `protobuf:"bytes,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Version              string   `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExchangeItemNameReq) Reset()         { *m = GetExchangeItemNameReq{} }
func (m *GetExchangeItemNameReq) String() string { return proto.CompactTextString(m) }
func (*GetExchangeItemNameReq) ProtoMessage()    {}
func (*GetExchangeItemNameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{14}
}
func (m *GetExchangeItemNameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExchangeItemNameReq.Unmarshal(m, b)
}
func (m *GetExchangeItemNameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExchangeItemNameReq.Marshal(b, m, deterministic)
}
func (dst *GetExchangeItemNameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExchangeItemNameReq.Merge(dst, src)
}
func (m *GetExchangeItemNameReq) XXX_Size() int {
	return xxx_messageInfo_GetExchangeItemNameReq.Size(m)
}
func (m *GetExchangeItemNameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExchangeItemNameReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExchangeItemNameReq proto.InternalMessageInfo

func (m *GetExchangeItemNameReq) GetExchangeType() uint32 {
	if m != nil {
		return m.ExchangeType
	}
	return 0
}

func (m *GetExchangeItemNameReq) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *GetExchangeItemNameReq) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type GetExchangeItemNameResp struct {
	ItemName             string   `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExchangeItemNameResp) Reset()         { *m = GetExchangeItemNameResp{} }
func (m *GetExchangeItemNameResp) String() string { return proto.CompactTextString(m) }
func (*GetExchangeItemNameResp) ProtoMessage()    {}
func (*GetExchangeItemNameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{15}
}
func (m *GetExchangeItemNameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExchangeItemNameResp.Unmarshal(m, b)
}
func (m *GetExchangeItemNameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExchangeItemNameResp.Marshal(b, m, deterministic)
}
func (dst *GetExchangeItemNameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExchangeItemNameResp.Merge(dst, src)
}
func (m *GetExchangeItemNameResp) XXX_Size() int {
	return xxx_messageInfo_GetExchangeItemNameResp.Size(m)
}
func (m *GetExchangeItemNameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExchangeItemNameResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExchangeItemNameResp proto.InternalMessageInfo

func (m *GetExchangeItemNameResp) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

type BatchSetExchangeConfigReq struct {
	List                 []*ExchangeConfig `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchSetExchangeConfigReq) Reset()         { *m = BatchSetExchangeConfigReq{} }
func (m *BatchSetExchangeConfigReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetExchangeConfigReq) ProtoMessage()    {}
func (*BatchSetExchangeConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{16}
}
func (m *BatchSetExchangeConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetExchangeConfigReq.Unmarshal(m, b)
}
func (m *BatchSetExchangeConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetExchangeConfigReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetExchangeConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetExchangeConfigReq.Merge(dst, src)
}
func (m *BatchSetExchangeConfigReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetExchangeConfigReq.Size(m)
}
func (m *BatchSetExchangeConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetExchangeConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetExchangeConfigReq proto.InternalMessageInfo

func (m *BatchSetExchangeConfigReq) GetList() []*ExchangeConfig {
	if m != nil {
		return m.List
	}
	return nil
}

type GetExchangeConfigReq struct {
	FragmentType         uint32   `protobuf:"varint,1,opt,name=fragment_type,json=fragmentType,proto3" json:"fragment_type,omitempty"`
	Valid                bool     `protobuf:"varint,2,opt,name=valid,proto3" json:"valid,omitempty"`
	FilterType           []uint32 `protobuf:"varint,3,rep,packed,name=filter_type,json=filterType,proto3" json:"filter_type,omitempty"`
	IncludeCollection    bool     `protobuf:"varint,4,opt,name=include_collection,json=includeCollection,proto3" json:"include_collection,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExchangeConfigReq) Reset()         { *m = GetExchangeConfigReq{} }
func (m *GetExchangeConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetExchangeConfigReq) ProtoMessage()    {}
func (*GetExchangeConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{17}
}
func (m *GetExchangeConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExchangeConfigReq.Unmarshal(m, b)
}
func (m *GetExchangeConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExchangeConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetExchangeConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExchangeConfigReq.Merge(dst, src)
}
func (m *GetExchangeConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetExchangeConfigReq.Size(m)
}
func (m *GetExchangeConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExchangeConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExchangeConfigReq proto.InternalMessageInfo

func (m *GetExchangeConfigReq) GetFragmentType() uint32 {
	if m != nil {
		return m.FragmentType
	}
	return 0
}

func (m *GetExchangeConfigReq) GetValid() bool {
	if m != nil {
		return m.Valid
	}
	return false
}

func (m *GetExchangeConfigReq) GetFilterType() []uint32 {
	if m != nil {
		return m.FilterType
	}
	return nil
}

func (m *GetExchangeConfigReq) GetIncludeCollection() bool {
	if m != nil {
		return m.IncludeCollection
	}
	return false
}

type ExchangeConfig struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FragmentType         uint32   `protobuf:"varint,2,opt,name=fragment_type,json=fragmentType,proto3" json:"fragment_type,omitempty"`
	ExchangeType         uint32   `protobuf:"varint,3,opt,name=exchange_type,json=exchangeType,proto3" json:"exchange_type,omitempty"`
	ItemId               string   `protobuf:"bytes,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName             string   `protobuf:"bytes,5,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	Intro                string   `protobuf:"bytes,6,opt,name=intro,proto3" json:"intro,omitempty"`
	Price                uint32   `protobuf:"varint,7,opt,name=price,proto3" json:"price,omitempty"`
	InventoryType        uint32   `protobuf:"varint,8,opt,name=inventory_type,json=inventoryType,proto3" json:"inventory_type,omitempty"`
	Remain               uint32   `protobuf:"varint,9,opt,name=remain,proto3" json:"remain,omitempty"`
	Tag                  string   `protobuf:"bytes,10,opt,name=tag,proto3" json:"tag,omitempty"`
	BuyLimitType         uint32   `protobuf:"varint,11,opt,name=buy_limit_type,json=buyLimitType,proto3" json:"buy_limit_type,omitempty"`
	BuyLimitCount        uint32   `protobuf:"varint,12,opt,name=buy_limit_count,json=buyLimitCount,proto3" json:"buy_limit_count,omitempty"`
	SortId               uint32   `protobuf:"varint,13,opt,name=sort_id,json=sortId,proto3" json:"sort_id,omitempty"`
	BeginTime            int64    `protobuf:"varint,14,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,15,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	CreateTime           int64    `protobuf:"varint,16,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Remark               string   `protobuf:"bytes,17,opt,name=remark,proto3" json:"remark,omitempty"`
	ItemIcon             string   `protobuf:"bytes,18,opt,name=item_icon,json=itemIcon,proto3" json:"item_icon,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,19,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	MaxCount             uint32   `protobuf:"varint,20,opt,name=max_count,json=maxCount,proto3" json:"max_count,omitempty"`
	FillType             uint32   `protobuf:"varint,21,opt,name=fill_type,json=fillType,proto3" json:"fill_type,omitempty"`
	ShowIcon             string   `protobuf:"bytes,22,opt,name=show_icon,json=showIcon,proto3" json:"show_icon,omitempty"`
	ExtraParams          string   `protobuf:"bytes,23,opt,name=extra_params,json=extraParams,proto3" json:"extra_params,omitempty"`
	Version              string   `protobuf:"bytes,24,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExchangeConfig) Reset()         { *m = ExchangeConfig{} }
func (m *ExchangeConfig) String() string { return proto.CompactTextString(m) }
func (*ExchangeConfig) ProtoMessage()    {}
func (*ExchangeConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{18}
}
func (m *ExchangeConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExchangeConfig.Unmarshal(m, b)
}
func (m *ExchangeConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExchangeConfig.Marshal(b, m, deterministic)
}
func (dst *ExchangeConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExchangeConfig.Merge(dst, src)
}
func (m *ExchangeConfig) XXX_Size() int {
	return xxx_messageInfo_ExchangeConfig.Size(m)
}
func (m *ExchangeConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ExchangeConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ExchangeConfig proto.InternalMessageInfo

func (m *ExchangeConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ExchangeConfig) GetFragmentType() uint32 {
	if m != nil {
		return m.FragmentType
	}
	return 0
}

func (m *ExchangeConfig) GetExchangeType() uint32 {
	if m != nil {
		return m.ExchangeType
	}
	return 0
}

func (m *ExchangeConfig) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *ExchangeConfig) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *ExchangeConfig) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *ExchangeConfig) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ExchangeConfig) GetInventoryType() uint32 {
	if m != nil {
		return m.InventoryType
	}
	return 0
}

func (m *ExchangeConfig) GetRemain() uint32 {
	if m != nil {
		return m.Remain
	}
	return 0
}

func (m *ExchangeConfig) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *ExchangeConfig) GetBuyLimitType() uint32 {
	if m != nil {
		return m.BuyLimitType
	}
	return 0
}

func (m *ExchangeConfig) GetBuyLimitCount() uint32 {
	if m != nil {
		return m.BuyLimitCount
	}
	return 0
}

func (m *ExchangeConfig) GetSortId() uint32 {
	if m != nil {
		return m.SortId
	}
	return 0
}

func (m *ExchangeConfig) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ExchangeConfig) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ExchangeConfig) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ExchangeConfig) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *ExchangeConfig) GetItemIcon() string {
	if m != nil {
		return m.ItemIcon
	}
	return ""
}

func (m *ExchangeConfig) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *ExchangeConfig) GetMaxCount() uint32 {
	if m != nil {
		return m.MaxCount
	}
	return 0
}

func (m *ExchangeConfig) GetFillType() uint32 {
	if m != nil {
		return m.FillType
	}
	return 0
}

func (m *ExchangeConfig) GetShowIcon() string {
	if m != nil {
		return m.ShowIcon
	}
	return ""
}

func (m *ExchangeConfig) GetExtraParams() string {
	if m != nil {
		return m.ExtraParams
	}
	return ""
}

func (m *ExchangeConfig) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type GetExchangeConfigResp struct {
	List                 []*ExchangeConfig `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetExchangeConfigResp) Reset()         { *m = GetExchangeConfigResp{} }
func (m *GetExchangeConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetExchangeConfigResp) ProtoMessage()    {}
func (*GetExchangeConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{19}
}
func (m *GetExchangeConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExchangeConfigResp.Unmarshal(m, b)
}
func (m *GetExchangeConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExchangeConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetExchangeConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExchangeConfigResp.Merge(dst, src)
}
func (m *GetExchangeConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetExchangeConfigResp.Size(m)
}
func (m *GetExchangeConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExchangeConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExchangeConfigResp proto.InternalMessageInfo

func (m *GetExchangeConfigResp) GetList() []*ExchangeConfig {
	if m != nil {
		return m.List
	}
	return nil
}

type DelBannerReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBannerReq) Reset()         { *m = DelBannerReq{} }
func (m *DelBannerReq) String() string { return proto.CompactTextString(m) }
func (*DelBannerReq) ProtoMessage()    {}
func (*DelBannerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{20}
}
func (m *DelBannerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBannerReq.Unmarshal(m, b)
}
func (m *DelBannerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBannerReq.Marshal(b, m, deterministic)
}
func (dst *DelBannerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBannerReq.Merge(dst, src)
}
func (m *DelBannerReq) XXX_Size() int {
	return xxx_messageInfo_DelBannerReq.Size(m)
}
func (m *DelBannerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBannerReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBannerReq proto.InternalMessageInfo

func (m *DelBannerReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type SortBannerReq struct {
	IdList               []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SortBannerReq) Reset()         { *m = SortBannerReq{} }
func (m *SortBannerReq) String() string { return proto.CompactTextString(m) }
func (*SortBannerReq) ProtoMessage()    {}
func (*SortBannerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{21}
}
func (m *SortBannerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortBannerReq.Unmarshal(m, b)
}
func (m *SortBannerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortBannerReq.Marshal(b, m, deterministic)
}
func (dst *SortBannerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortBannerReq.Merge(dst, src)
}
func (m *SortBannerReq) XXX_Size() int {
	return xxx_messageInfo_SortBannerReq.Size(m)
}
func (m *SortBannerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SortBannerReq.DiscardUnknown(m)
}

var xxx_messageInfo_SortBannerReq proto.InternalMessageInfo

func (m *SortBannerReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type GloryEmptyMsg struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GloryEmptyMsg) Reset()         { *m = GloryEmptyMsg{} }
func (m *GloryEmptyMsg) String() string { return proto.CompactTextString(m) }
func (*GloryEmptyMsg) ProtoMessage()    {}
func (*GloryEmptyMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{22}
}
func (m *GloryEmptyMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GloryEmptyMsg.Unmarshal(m, b)
}
func (m *GloryEmptyMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GloryEmptyMsg.Marshal(b, m, deterministic)
}
func (dst *GloryEmptyMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GloryEmptyMsg.Merge(dst, src)
}
func (m *GloryEmptyMsg) XXX_Size() int {
	return xxx_messageInfo_GloryEmptyMsg.Size(m)
}
func (m *GloryEmptyMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GloryEmptyMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GloryEmptyMsg proto.InternalMessageInfo

type GloryBannerData struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	OsType               uint32   `protobuf:"varint,2,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
	ImgUrl               string   `protobuf:"bytes,3,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	JumpUrl              string   `protobuf:"bytes,4,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	BeginTime            int64    `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	CreateTime           int64    `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	NobilityLevel        uint32   `protobuf:"varint,8,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	AccountLevel         uint32   `protobuf:"varint,9,opt,name=account_level,json=accountLevel,proto3" json:"account_level,omitempty"`
	RichLevel            uint64   `protobuf:"varint,10,opt,name=rich_level,json=richLevel,proto3" json:"rich_level,omitempty"`
	Remark               string   `protobuf:"bytes,11,opt,name=remark,proto3" json:"remark,omitempty"`
	Handler              string   `protobuf:"bytes,12,opt,name=handler,proto3" json:"handler,omitempty"`
	SortId               uint32   `protobuf:"varint,13,opt,name=sort_id,json=sortId,proto3" json:"sort_id,omitempty"`
	MarketId             []uint32 `protobuf:"varint,14,rep,packed,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GloryBannerData) Reset()         { *m = GloryBannerData{} }
func (m *GloryBannerData) String() string { return proto.CompactTextString(m) }
func (*GloryBannerData) ProtoMessage()    {}
func (*GloryBannerData) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{23}
}
func (m *GloryBannerData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GloryBannerData.Unmarshal(m, b)
}
func (m *GloryBannerData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GloryBannerData.Marshal(b, m, deterministic)
}
func (dst *GloryBannerData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GloryBannerData.Merge(dst, src)
}
func (m *GloryBannerData) XXX_Size() int {
	return xxx_messageInfo_GloryBannerData.Size(m)
}
func (m *GloryBannerData) XXX_DiscardUnknown() {
	xxx_messageInfo_GloryBannerData.DiscardUnknown(m)
}

var xxx_messageInfo_GloryBannerData proto.InternalMessageInfo

func (m *GloryBannerData) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GloryBannerData) GetOsType() uint32 {
	if m != nil {
		return m.OsType
	}
	return 0
}

func (m *GloryBannerData) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *GloryBannerData) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *GloryBannerData) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GloryBannerData) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GloryBannerData) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GloryBannerData) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *GloryBannerData) GetAccountLevel() uint32 {
	if m != nil {
		return m.AccountLevel
	}
	return 0
}

func (m *GloryBannerData) GetRichLevel() uint64 {
	if m != nil {
		return m.RichLevel
	}
	return 0
}

func (m *GloryBannerData) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *GloryBannerData) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

func (m *GloryBannerData) GetSortId() uint32 {
	if m != nil {
		return m.SortId
	}
	return 0
}

func (m *GloryBannerData) GetMarketId() []uint32 {
	if m != nil {
		return m.MarketId
	}
	return nil
}

type GetAllBannerResp struct {
	List                 []*GloryBannerData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetAllBannerResp) Reset()         { *m = GetAllBannerResp{} }
func (m *GetAllBannerResp) String() string { return proto.CompactTextString(m) }
func (*GetAllBannerResp) ProtoMessage()    {}
func (*GetAllBannerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_glory_279da6dbcd0ae6ad, []int{24}
}
func (m *GetAllBannerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllBannerResp.Unmarshal(m, b)
}
func (m *GetAllBannerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllBannerResp.Marshal(b, m, deterministic)
}
func (dst *GetAllBannerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllBannerResp.Merge(dst, src)
}
func (m *GetAllBannerResp) XXX_Size() int {
	return xxx_messageInfo_GetAllBannerResp.Size(m)
}
func (m *GetAllBannerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllBannerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllBannerResp proto.InternalMessageInfo

func (m *GetAllBannerResp) GetList() []*GloryBannerData {
	if m != nil {
		return m.List
	}
	return nil
}

func init() {
	proto.RegisterType((*FillAllItemReq)(nil), "exchange.FillAllItemReq")
	proto.RegisterType((*PushFeishuReq)(nil), "exchange.PushFeishuReq")
	proto.RegisterType((*GetExchangeTransactionSumReq)(nil), "exchange.GetExchangeTransactionSumReq")
	proto.RegisterType((*ExchangeTransactionSumData)(nil), "exchange.ExchangeTransactionSumData")
	proto.RegisterType((*GetExchangeTransactionSumResp)(nil), "exchange.GetExchangeTransactionSumResp")
	proto.RegisterType((*GetExchangeHistoryTypeReq)(nil), "exchange.GetExchangeHistoryTypeReq")
	proto.RegisterType((*GetExchangeHistoryTypeResp)(nil), "exchange.GetExchangeHistoryTypeResp")
	proto.RegisterType((*GetCarouselResp)(nil), "exchange.GetCarouselResp")
	proto.RegisterType((*CarouselData)(nil), "exchange.CarouselData")
	proto.RegisterType((*ExchangeHistoryReq)(nil), "exchange.ExchangeHistoryReq")
	proto.RegisterType((*ExchangeHistoryData)(nil), "exchange.ExchangeHistoryData")
	proto.RegisterType((*ExchangeHistoryResp)(nil), "exchange.ExchangeHistoryResp")
	proto.RegisterType((*ExchangeItemReq)(nil), "exchange.ExchangeItemReq")
	proto.RegisterType((*DelExchangeConfigReq)(nil), "exchange.DelExchangeConfigReq")
	proto.RegisterType((*GetExchangeItemNameReq)(nil), "exchange.GetExchangeItemNameReq")
	proto.RegisterType((*GetExchangeItemNameResp)(nil), "exchange.GetExchangeItemNameResp")
	proto.RegisterType((*BatchSetExchangeConfigReq)(nil), "exchange.BatchSetExchangeConfigReq")
	proto.RegisterType((*GetExchangeConfigReq)(nil), "exchange.GetExchangeConfigReq")
	proto.RegisterType((*ExchangeConfig)(nil), "exchange.ExchangeConfig")
	proto.RegisterType((*GetExchangeConfigResp)(nil), "exchange.GetExchangeConfigResp")
	proto.RegisterType((*DelBannerReq)(nil), "exchange.DelBannerReq")
	proto.RegisterType((*SortBannerReq)(nil), "exchange.SortBannerReq")
	proto.RegisterType((*GloryEmptyMsg)(nil), "exchange.GloryEmptyMsg")
	proto.RegisterType((*GloryBannerData)(nil), "exchange.GloryBannerData")
	proto.RegisterType((*GetAllBannerResp)(nil), "exchange.GetAllBannerResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ExchangeGloryClient is the client API for ExchangeGlory service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ExchangeGloryClient interface {
	// banner//////////////////////////////////////////////////////
	// !!!所有的时间戳单位为秒！！！
	// 运营后台获取所有banner配置
	GetAllBanner(ctx context.Context, in *GloryEmptyMsg, opts ...grpc.CallOption) (*GetAllBannerResp, error)
	// 创建或更新，id不传则为创建，id非0则为更新
	SetBanner(ctx context.Context, in *GloryBannerData, opts ...grpc.CallOption) (*GloryEmptyMsg, error)
	// 排序
	SortBanner(ctx context.Context, in *SortBannerReq, opts ...grpc.CallOption) (*GloryEmptyMsg, error)
	// 删除banner
	DelBanner(ctx context.Context, in *DelBannerReq, opts ...grpc.CallOption) (*GloryEmptyMsg, error)
	// 兑换运营后台//////////////////////////////////////////////////////
	// !!!所有的时间戳单位为秒！！！
	// 查看商城兑换配置
	GetExchangeConfig(ctx context.Context, in *GetExchangeConfigReq, opts ...grpc.CallOption) (*GetExchangeConfigResp, error)
	// 批量创建或更新，id不传则为创建，id非0则为更新。item_name不用传。
	BatchSetExchangeConfig(ctx context.Context, in *BatchSetExchangeConfigReq, opts ...grpc.CallOption) (*GloryEmptyMsg, error)
	// 根据兑换类型和物品ID查询物品名，只用作显示
	GetExchangeItemName(ctx context.Context, in *GetExchangeItemNameReq, opts ...grpc.CallOption) (*GetExchangeItemNameResp, error)
	// 批量删除兑换
	BatchDelExchangeConfig(ctx context.Context, in *DelExchangeConfigReq, opts ...grpc.CallOption) (*GloryEmptyMsg, error)
	// 兑换前端接口//////////////////////////////////////////////////////
	// 兑换
	ExchangeItem(ctx context.Context, in *ExchangeItemReq, opts ...grpc.CallOption) (*GloryEmptyMsg, error)
	// 获取半年内，用户所有的兑换类型
	GetExchangeHistoryType(ctx context.Context, in *GetExchangeHistoryTypeReq, opts ...grpc.CallOption) (*GetExchangeHistoryTypeResp, error)
	// 兑换记录
	ExchangeHistory(ctx context.Context, in *ExchangeHistoryReq, opts ...grpc.CallOption) (*ExchangeHistoryResp, error)
	// 荣耀兑换轮播
	GetCarousel(ctx context.Context, in *GloryEmptyMsg, opts ...grpc.CallOption) (*GetCarouselResp, error)
	// 兑换统计，按照兑换配置id，聚合获取一段时间的统计信息
	GetExchangeTransactionSum(ctx context.Context, in *GetExchangeTransactionSumReq, opts ...grpc.CallOption) (*GetExchangeTransactionSumResp, error)
	// 触发推送
	PushFeishu(ctx context.Context, in *PushFeishuReq, opts ...grpc.CallOption) (*GloryEmptyMsg, error)
	// 重新生成荣耀兑换轮播
	RegenerateCarousel(ctx context.Context, in *GloryEmptyMsg, opts ...grpc.CallOption) (*GloryEmptyMsg, error)
	// 触发补仓操作
	FillAllItem(ctx context.Context, in *FillAllItemReq, opts ...grpc.CallOption) (*GloryEmptyMsg, error)
}

type exchangeGloryClient struct {
	cc *grpc.ClientConn
}

func NewExchangeGloryClient(cc *grpc.ClientConn) ExchangeGloryClient {
	return &exchangeGloryClient{cc}
}

func (c *exchangeGloryClient) GetAllBanner(ctx context.Context, in *GloryEmptyMsg, opts ...grpc.CallOption) (*GetAllBannerResp, error) {
	out := new(GetAllBannerResp)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/GetAllBanner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) SetBanner(ctx context.Context, in *GloryBannerData, opts ...grpc.CallOption) (*GloryEmptyMsg, error) {
	out := new(GloryEmptyMsg)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/SetBanner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) SortBanner(ctx context.Context, in *SortBannerReq, opts ...grpc.CallOption) (*GloryEmptyMsg, error) {
	out := new(GloryEmptyMsg)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/SortBanner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) DelBanner(ctx context.Context, in *DelBannerReq, opts ...grpc.CallOption) (*GloryEmptyMsg, error) {
	out := new(GloryEmptyMsg)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/DelBanner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) GetExchangeConfig(ctx context.Context, in *GetExchangeConfigReq, opts ...grpc.CallOption) (*GetExchangeConfigResp, error) {
	out := new(GetExchangeConfigResp)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/GetExchangeConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) BatchSetExchangeConfig(ctx context.Context, in *BatchSetExchangeConfigReq, opts ...grpc.CallOption) (*GloryEmptyMsg, error) {
	out := new(GloryEmptyMsg)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/BatchSetExchangeConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) GetExchangeItemName(ctx context.Context, in *GetExchangeItemNameReq, opts ...grpc.CallOption) (*GetExchangeItemNameResp, error) {
	out := new(GetExchangeItemNameResp)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/GetExchangeItemName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) BatchDelExchangeConfig(ctx context.Context, in *DelExchangeConfigReq, opts ...grpc.CallOption) (*GloryEmptyMsg, error) {
	out := new(GloryEmptyMsg)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/BatchDelExchangeConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) ExchangeItem(ctx context.Context, in *ExchangeItemReq, opts ...grpc.CallOption) (*GloryEmptyMsg, error) {
	out := new(GloryEmptyMsg)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/ExchangeItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) GetExchangeHistoryType(ctx context.Context, in *GetExchangeHistoryTypeReq, opts ...grpc.CallOption) (*GetExchangeHistoryTypeResp, error) {
	out := new(GetExchangeHistoryTypeResp)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/GetExchangeHistoryType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) ExchangeHistory(ctx context.Context, in *ExchangeHistoryReq, opts ...grpc.CallOption) (*ExchangeHistoryResp, error) {
	out := new(ExchangeHistoryResp)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/ExchangeHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) GetCarousel(ctx context.Context, in *GloryEmptyMsg, opts ...grpc.CallOption) (*GetCarouselResp, error) {
	out := new(GetCarouselResp)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/GetCarousel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) GetExchangeTransactionSum(ctx context.Context, in *GetExchangeTransactionSumReq, opts ...grpc.CallOption) (*GetExchangeTransactionSumResp, error) {
	out := new(GetExchangeTransactionSumResp)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/GetExchangeTransactionSum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) PushFeishu(ctx context.Context, in *PushFeishuReq, opts ...grpc.CallOption) (*GloryEmptyMsg, error) {
	out := new(GloryEmptyMsg)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/PushFeishu", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) RegenerateCarousel(ctx context.Context, in *GloryEmptyMsg, opts ...grpc.CallOption) (*GloryEmptyMsg, error) {
	out := new(GloryEmptyMsg)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/RegenerateCarousel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeGloryClient) FillAllItem(ctx context.Context, in *FillAllItemReq, opts ...grpc.CallOption) (*GloryEmptyMsg, error) {
	out := new(GloryEmptyMsg)
	err := c.cc.Invoke(ctx, "/exchange.ExchangeGlory/FillAllItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ExchangeGloryServer is the server API for ExchangeGlory service.
type ExchangeGloryServer interface {
	// banner//////////////////////////////////////////////////////
	// !!!所有的时间戳单位为秒！！！
	// 运营后台获取所有banner配置
	GetAllBanner(context.Context, *GloryEmptyMsg) (*GetAllBannerResp, error)
	// 创建或更新，id不传则为创建，id非0则为更新
	SetBanner(context.Context, *GloryBannerData) (*GloryEmptyMsg, error)
	// 排序
	SortBanner(context.Context, *SortBannerReq) (*GloryEmptyMsg, error)
	// 删除banner
	DelBanner(context.Context, *DelBannerReq) (*GloryEmptyMsg, error)
	// 兑换运营后台//////////////////////////////////////////////////////
	// !!!所有的时间戳单位为秒！！！
	// 查看商城兑换配置
	GetExchangeConfig(context.Context, *GetExchangeConfigReq) (*GetExchangeConfigResp, error)
	// 批量创建或更新，id不传则为创建，id非0则为更新。item_name不用传。
	BatchSetExchangeConfig(context.Context, *BatchSetExchangeConfigReq) (*GloryEmptyMsg, error)
	// 根据兑换类型和物品ID查询物品名，只用作显示
	GetExchangeItemName(context.Context, *GetExchangeItemNameReq) (*GetExchangeItemNameResp, error)
	// 批量删除兑换
	BatchDelExchangeConfig(context.Context, *DelExchangeConfigReq) (*GloryEmptyMsg, error)
	// 兑换前端接口//////////////////////////////////////////////////////
	// 兑换
	ExchangeItem(context.Context, *ExchangeItemReq) (*GloryEmptyMsg, error)
	// 获取半年内，用户所有的兑换类型
	GetExchangeHistoryType(context.Context, *GetExchangeHistoryTypeReq) (*GetExchangeHistoryTypeResp, error)
	// 兑换记录
	ExchangeHistory(context.Context, *ExchangeHistoryReq) (*ExchangeHistoryResp, error)
	// 荣耀兑换轮播
	GetCarousel(context.Context, *GloryEmptyMsg) (*GetCarouselResp, error)
	// 兑换统计，按照兑换配置id，聚合获取一段时间的统计信息
	GetExchangeTransactionSum(context.Context, *GetExchangeTransactionSumReq) (*GetExchangeTransactionSumResp, error)
	// 触发推送
	PushFeishu(context.Context, *PushFeishuReq) (*GloryEmptyMsg, error)
	// 重新生成荣耀兑换轮播
	RegenerateCarousel(context.Context, *GloryEmptyMsg) (*GloryEmptyMsg, error)
	// 触发补仓操作
	FillAllItem(context.Context, *FillAllItemReq) (*GloryEmptyMsg, error)
}

func RegisterExchangeGloryServer(s *grpc.Server, srv ExchangeGloryServer) {
	s.RegisterService(&_ExchangeGlory_serviceDesc, srv)
}

func _ExchangeGlory_GetAllBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GloryEmptyMsg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).GetAllBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/GetAllBanner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).GetAllBanner(ctx, req.(*GloryEmptyMsg))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_SetBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GloryBannerData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).SetBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/SetBanner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).SetBanner(ctx, req.(*GloryBannerData))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_SortBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortBannerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).SortBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/SortBanner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).SortBanner(ctx, req.(*SortBannerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_DelBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelBannerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).DelBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/DelBanner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).DelBanner(ctx, req.(*DelBannerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_GetExchangeConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExchangeConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).GetExchangeConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/GetExchangeConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).GetExchangeConfig(ctx, req.(*GetExchangeConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_BatchSetExchangeConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetExchangeConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).BatchSetExchangeConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/BatchSetExchangeConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).BatchSetExchangeConfig(ctx, req.(*BatchSetExchangeConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_GetExchangeItemName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExchangeItemNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).GetExchangeItemName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/GetExchangeItemName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).GetExchangeItemName(ctx, req.(*GetExchangeItemNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_BatchDelExchangeConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelExchangeConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).BatchDelExchangeConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/BatchDelExchangeConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).BatchDelExchangeConfig(ctx, req.(*DelExchangeConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_ExchangeItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExchangeItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).ExchangeItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/ExchangeItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).ExchangeItem(ctx, req.(*ExchangeItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_GetExchangeHistoryType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExchangeHistoryTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).GetExchangeHistoryType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/GetExchangeHistoryType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).GetExchangeHistoryType(ctx, req.(*GetExchangeHistoryTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_ExchangeHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExchangeHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).ExchangeHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/ExchangeHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).ExchangeHistory(ctx, req.(*ExchangeHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_GetCarousel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GloryEmptyMsg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).GetCarousel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/GetCarousel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).GetCarousel(ctx, req.(*GloryEmptyMsg))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_GetExchangeTransactionSum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExchangeTransactionSumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).GetExchangeTransactionSum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/GetExchangeTransactionSum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).GetExchangeTransactionSum(ctx, req.(*GetExchangeTransactionSumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_PushFeishu_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushFeishuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).PushFeishu(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/PushFeishu",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).PushFeishu(ctx, req.(*PushFeishuReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_RegenerateCarousel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GloryEmptyMsg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).RegenerateCarousel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/RegenerateCarousel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).RegenerateCarousel(ctx, req.(*GloryEmptyMsg))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeGlory_FillAllItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FillAllItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeGloryServer).FillAllItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchange.ExchangeGlory/FillAllItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeGloryServer).FillAllItem(ctx, req.(*FillAllItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ExchangeGlory_serviceDesc = grpc.ServiceDesc{
	ServiceName: "exchange.ExchangeGlory",
	HandlerType: (*ExchangeGloryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAllBanner",
			Handler:    _ExchangeGlory_GetAllBanner_Handler,
		},
		{
			MethodName: "SetBanner",
			Handler:    _ExchangeGlory_SetBanner_Handler,
		},
		{
			MethodName: "SortBanner",
			Handler:    _ExchangeGlory_SortBanner_Handler,
		},
		{
			MethodName: "DelBanner",
			Handler:    _ExchangeGlory_DelBanner_Handler,
		},
		{
			MethodName: "GetExchangeConfig",
			Handler:    _ExchangeGlory_GetExchangeConfig_Handler,
		},
		{
			MethodName: "BatchSetExchangeConfig",
			Handler:    _ExchangeGlory_BatchSetExchangeConfig_Handler,
		},
		{
			MethodName: "GetExchangeItemName",
			Handler:    _ExchangeGlory_GetExchangeItemName_Handler,
		},
		{
			MethodName: "BatchDelExchangeConfig",
			Handler:    _ExchangeGlory_BatchDelExchangeConfig_Handler,
		},
		{
			MethodName: "ExchangeItem",
			Handler:    _ExchangeGlory_ExchangeItem_Handler,
		},
		{
			MethodName: "GetExchangeHistoryType",
			Handler:    _ExchangeGlory_GetExchangeHistoryType_Handler,
		},
		{
			MethodName: "ExchangeHistory",
			Handler:    _ExchangeGlory_ExchangeHistory_Handler,
		},
		{
			MethodName: "GetCarousel",
			Handler:    _ExchangeGlory_GetCarousel_Handler,
		},
		{
			MethodName: "GetExchangeTransactionSum",
			Handler:    _ExchangeGlory_GetExchangeTransactionSum_Handler,
		},
		{
			MethodName: "PushFeishu",
			Handler:    _ExchangeGlory_PushFeishu_Handler,
		},
		{
			MethodName: "RegenerateCarousel",
			Handler:    _ExchangeGlory_RegenerateCarousel_Handler,
		},
		{
			MethodName: "FillAllItem",
			Handler:    _ExchangeGlory_FillAllItem_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "exchange/exchange-glory.proto",
}

func init() {
	proto.RegisterFile("exchange/exchange-glory.proto", fileDescriptor_exchange_glory_279da6dbcd0ae6ad)
}

var fileDescriptor_exchange_glory_279da6dbcd0ae6ad = []byte{
	// 1631 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x58, 0x6d, 0x6f, 0xdb, 0x46,
	0x12, 0x8e, 0x2c, 0xeb, 0x6d, 0xf4, 0x62, 0x7b, 0xed, 0xd8, 0xb2, 0x12, 0xc7, 0x0e, 0xf3, 0x66,
	0xe4, 0x62, 0x1b, 0x97, 0xc3, 0x1d, 0xee, 0xcb, 0x1d, 0xe2, 0xd8, 0x8e, 0xa3, 0x22, 0x0d, 0x02,
	0x39, 0x09, 0xd2, 0xa2, 0x80, 0x40, 0x93, 0x2b, 0x79, 0x6b, 0xbe, 0xa8, 0xe4, 0xca, 0xb1, 0xfe,
	0x45, 0x7f, 0x40, 0x81, 0xb6, 0x3f, 0xa7, 0x5f, 0xfa, 0xa1, 0x7f, 0xa8, 0xc5, 0xce, 0x2e, 0xc5,
	0x25, 0x45, 0xd1, 0xe9, 0x27, 0x69, 0x67, 0x66, 0x67, 0x66, 0x67, 0x9e, 0x67, 0xb8, 0x24, 0x6c,
	0xd1, 0x6b, 0xeb, 0xc2, 0xf4, 0x86, 0xf4, 0x20, 0xfa, 0xb3, 0x37, 0x74, 0xfc, 0x60, 0xb2, 0x3f,
	0x0a, 0x7c, 0xee, 0x93, 0x6a, 0x24, 0xed, 0x6c, 0xd3, 0x6b, 0x4e, 0xbd, 0x90, 0xf9, 0xde, 0x81,
	0x3f, 0xe2, 0xcc, 0xf7, 0xc2, 0xe8, 0x57, 0x9a, 0x1a, 0xff, 0x80, 0xd6, 0x2b, 0xe6, 0x38, 0x87,
	0x8e, 0xd3, 0xe5, 0xd4, 0xed, 0xd1, 0x1f, 0xc8, 0x26, 0x54, 0x39, 0x73, 0x69, 0xdf, 0xf3, 0x3f,
	0xb7, 0x0b, 0x3b, 0x85, 0xdd, 0x62, 0xaf, 0x22, 0xd6, 0x6f, 0xfd, 0xcf, 0xc6, 0x53, 0x68, 0xbe,
	0x1b, 0x87, 0x17, 0xaf, 0x28, 0x0b, 0x2f, 0xc6, 0x37, 0xd8, 0x7e, 0x82, 0xbb, 0xa7, 0x94, 0x9f,
	0xa8, 0x44, 0xde, 0x07, 0xa6, 0x17, 0x9a, 0x96, 0x88, 0x7c, 0x36, 0xc6, 0x30, 0x5b, 0x00, 0xe7,
	0x74, 0xc8, 0xbc, 0xbe, 0xd8, 0xa0, 0x36, 0xd7, 0x50, 0xf2, 0x9e, 0xb9, 0x54, 0x78, 0xa6, 0x9e,
	0x2d, 0x95, 0x0b, 0xd2, 0x33, 0xf5, 0x6c, 0xa1, 0x32, 0x7e, 0x5f, 0x80, 0x4e, 0xb6, 0xdf, 0x63,
	0x93, 0x9b, 0x64, 0x1b, 0xea, 0xdc, 0x0c, 0x86, 0x94, 0xf7, 0xf9, 0x64, 0x24, 0x3d, 0x37, 0x7b,
	0x20, 0x45, 0xef, 0x27, 0x23, 0x4a, 0x1e, 0x42, 0x4b, 0x19, 0x30, 0x4e, 0xdd, 0x3e, 0xb3, 0x31,
	0x40, 0xad, 0xd7, 0x90, 0x52, 0x51, 0x87, 0xae, 0x4d, 0xee, 0x40, 0x0d, 0xd5, 0x9e, 0xe9, 0xd2,
	0x76, 0x11, 0x0d, 0xaa, 0x42, 0xf0, 0xd6, 0x74, 0x29, 0xb9, 0x0f, 0xca, 0xb8, 0x6f, 0xf9, 0x63,
	0x8f, 0xb7, 0x17, 0x31, 0x88, 0x8a, 0x7b, 0x24, 0x44, 0x64, 0x17, 0x96, 0x95, 0xc9, 0x98, 0xd9,
	0xca, 0xac, 0x84, 0x66, 0x2a, 0xfa, 0x07, 0x66, 0x4b, 0xcb, 0xd8, 0xd9, 0x95, 0xe9, 0x8c, 0x69,
	0xbb, 0xbc, 0x53, 0xd8, 0x5d, 0x8c, 0x9c, 0x7d, 0x14, 0x22, 0xf2, 0x0c, 0x88, 0xe5, 0x87, 0xbc,
	0x3f, 0x08, 0xcc, 0xa1, 0x4b, 0x3d, 0x75, 0xb4, 0x0a, 0xba, 0x5b, 0x16, 0x9a, 0x57, 0x4a, 0x81,
	0x07, 0xdc, 0x87, 0xd5, 0xa4, 0xb5, 0x8c, 0x5e, 0x45, 0xbf, 0x2b, 0xba, 0x39, 0x26, 0x60, 0x7c,
	0x03, 0x5b, 0x39, 0xad, 0x0a, 0x47, 0xe4, 0xbf, 0xb0, 0xe8, 0xb0, 0x90, 0xb7, 0x0b, 0x3b, 0xc5,
	0xdd, 0xfa, 0xf3, 0x87, 0xfb, 0x11, 0xbc, 0xf6, 0xe7, 0xb7, 0xa1, 0x87, 0x3b, 0x8c, 0x3d, 0xd8,
	0xd4, 0x5c, 0xbf, 0x66, 0x21, 0xf7, 0x83, 0x89, 0x48, 0x52, 0x40, 0x60, 0x19, 0x8a, 0x63, 0x66,
	0xab, 0x0e, 0x89, 0xbf, 0xc6, 0x57, 0xd0, 0x99, 0x67, 0x1e, 0x8e, 0x44, 0x15, 0xa2, 0xc8, 0x58,
	0x80, 0xfe, 0x34, 0xa9, 0x66, 0x6f, 0x39, 0xd2, 0x08, 0xeb, 0x37, 0x22, 0xf4, 0xff, 0x60, 0xe9,
	0x94, 0xf2, 0x23, 0x33, 0xf0, 0xc7, 0x21, 0x75, 0xd0, 0xc1, 0xd3, 0xc4, 0x39, 0xd6, 0xe3, 0x73,
	0x44, 0x56, 0x5a, 0xe6, 0x3f, 0x17, 0xa0, 0xa1, 0x8b, 0x67, 0xb3, 0x25, 0x04, 0x16, 0x3d, 0x66,
	0x5d, 0x2a, 0xf8, 0xe0, 0xff, 0x7c, 0xd8, 0x44, 0x4a, 0x66, 0xf9, 0x1e, 0x62, 0x46, 0x29, 0xbb,
	0x96, 0xef, 0x91, 0x35, 0x28, 0xe9, 0x28, 0x91, 0x0b, 0xc1, 0x83, 0x81, 0x79, 0x49, 0x05, 0x88,
	0x10, 0x18, 0xcd, 0x5e, 0x45, 0xac, 0x3f, 0x30, 0xdb, 0x98, 0x00, 0x49, 0x55, 0x2a, 0xb3, 0xa8,
	0xe4, 0x01, 0x34, 0x13, 0x65, 0xc3, 0x7c, 0x9b, 0xbd, 0x86, 0x5e, 0x31, 0xb2, 0x0e, 0x65, 0x7f,
	0x30, 0x08, 0x29, 0xc7, 0xa4, 0x9b, 0x3d, 0xb5, 0x12, 0x59, 0x39, 0xcc, 0x65, 0x11, 0xc4, 0xe5,
	0xc2, 0xf8, 0xb1, 0x08, 0xab, 0xa9, 0xd8, 0x58, 0xa3, 0x16, 0x2c, 0x4c, 0x63, 0x2f, 0x30, 0x5b,
	0x64, 0xef, 0x07, 0x36, 0x0d, 0x62, 0x92, 0x55, 0x70, 0xdd, 0xc5, 0xac, 0x92, 0x68, 0x96, 0x71,
	0x1b, 0x03, 0x1d, 0xc9, 0x4f, 0x60, 0x69, 0x6a, 0x64, 0xba, 0x1a, 0xd5, 0x5a, 0x91, 0xf8, 0x10,
	0xa5, 0x69, 0xd2, 0x97, 0xbe, 0x80, 0xf4, 0xe5, 0x0c, 0xd2, 0x3f, 0x80, 0xa6, 0xb2, 0x52, 0xd1,
	0x24, 0xc5, 0x94, 0x51, 0x1c, 0xcb, 0x0a, 0xa8, 0xc9, 0xa9, 0x9c, 0x4e, 0x55, 0x19, 0x4b, 0x8a,
	0x70, 0x76, 0xc5, 0xd4, 0x8f, 0xa1, 0x50, 0xc3, 0x68, 0xad, 0x38, 0x1a, 0x02, 0x22, 0x65, 0x89,
	0xb8, 0x80, 0xb4, 0x25, 0xa2, 0x63, 0x1b, 0xea, 0xf4, 0x7a, 0xc4, 0x02, 0x15, 0xb4, 0x2e, 0x83,
	0x4a, 0x11, 0x4e, 0xc5, 0xd7, 0x33, 0x1d, 0x41, 0xc8, 0xff, 0x33, 0x01, 0xf9, 0xad, 0x59, 0xea,
	0x6a, 0xed, 0x53, 0xc8, 0xef, 0xc2, 0x52, 0xa4, 0x8c, 0x9e, 0x09, 0xb3, 0xa0, 0x92, 0x9d, 0x5e,
	0x98, 0x76, 0x7a, 0x8a, 0xde, 0xa2, 0x86, 0x5e, 0xe3, 0x00, 0xd6, 0x8e, 0xa9, 0x13, 0x79, 0x3b,
	0xf2, 0xbd, 0x01, 0x1b, 0x0a, 0x7f, 0x1b, 0x50, 0x61, 0xb6, 0x4e, 0xdf, 0x32, 0xb3, 0x91, 0xb4,
	0x23, 0x58, 0xd7, 0x06, 0x40, 0x54, 0x27, 0xb1, 0x65, 0x06, 0xc5, 0x85, 0x0c, 0x14, 0x0b, 0xbf,
	0x89, 0x99, 0x5e, 0x66, 0xb2, 0xb1, 0x6d, 0xa8, 0x5c, 0xd1, 0x40, 0x3c, 0x07, 0x15, 0x29, 0xa3,
	0xa5, 0xf1, 0x1f, 0xd8, 0xc8, 0x8c, 0x18, 0x8e, 0x92, 0x5c, 0x2e, 0x24, 0xb9, 0x6c, 0x74, 0x61,
	0xf3, 0xa5, 0xc9, 0xad, 0x8b, 0xb3, 0x78, 0x73, 0x7c, 0xbe, 0x67, 0x89, 0xaa, 0xb7, 0x67, 0xab,
	0xae, 0x4c, 0x65, 0xc1, 0x7f, 0x2d, 0xc0, 0xda, 0x69, 0x96, 0x9b, 0x19, 0x8e, 0x14, 0x32, 0x38,
	0xb2, 0x06, 0xa5, 0x2b, 0xd3, 0x51, 0x27, 0xae, 0xf6, 0xe4, 0x42, 0xe0, 0x65, 0xc0, 0x1c, 0x4e,
	0x83, 0x88, 0x5c, 0xa2, 0xca, 0x20, 0x45, 0xb8, 0x6d, 0x0f, 0x08, 0xf3, 0x2c, 0x67, 0x6c, 0xd3,
	0xbe, 0xe5, 0x3b, 0x0e, 0xc5, 0xe1, 0x8d, 0xec, 0xaa, 0xf6, 0x56, 0x94, 0xe6, 0x68, 0xaa, 0x30,
	0x7e, 0x2b, 0x41, 0x2b, 0x99, 0xe0, 0x0c, 0xd9, 0x67, 0xb2, 0x5d, 0xc8, 0xc8, 0x76, 0xa6, 0x8d,
	0xc5, 0xfc, 0x36, 0x2e, 0x26, 0xda, 0x98, 0xe8, 0x48, 0x29, 0x35, 0x5d, 0xd7, 0xa0, 0xc4, 0x3c,
	0x1e, 0xf8, 0x8a, 0xd9, 0x72, 0x21, 0xa4, 0xa3, 0x80, 0x59, 0xd1, 0xd3, 0x52, 0x2e, 0xc8, 0x23,
	0x68, 0x31, 0xef, 0x8a, 0x7a, 0x02, 0xfa, 0x32, 0x0f, 0x49, 0xe3, 0xe6, 0x54, 0x1a, 0x4d, 0xc5,
	0x80, 0xba, 0x26, 0xf3, 0x90, 0xbf, 0xcd, 0x9e, 0x5a, 0x09, 0x3e, 0x70, 0x73, 0xa8, 0xa8, 0x2a,
	0xfe, 0x8a, 0xf9, 0x72, 0x3e, 0x9e, 0xf4, 0x71, 0x3c, 0x4a, 0x87, 0x92, 0xa2, 0x8d, 0xf3, 0xf1,
	0xe4, 0x8d, 0x10, 0xa2, 0xbf, 0xc7, 0xb0, 0x14, 0x5b, 0x49, 0xbe, 0x34, 0x64, 0xdc, 0xc8, 0x4c,
	0x5e, 0x09, 0x36, 0xa0, 0x12, 0xfa, 0x01, 0x17, 0x05, 0x68, 0xca, 0xc0, 0x62, 0xd9, 0xb5, 0x53,
	0xb7, 0xa6, 0x56, 0xde, 0xad, 0x69, 0x29, 0x71, 0x6b, 0x4a, 0x4f, 0xad, 0x65, 0xd4, 0xea, 0x53,
	0x4b, 0x9d, 0x35, 0xb8, 0x6c, 0xaf, 0xc8, 0x9a, 0xcb, 0x55, 0xf2, 0xa1, 0x45, 0x52, 0x0f, 0xad,
	0xd4, 0x58, 0x5a, 0x4d, 0x8f, 0x25, 0xb1, 0xdb, 0x35, 0xaf, 0xd5, 0x59, 0xd7, 0x50, 0x5d, 0x75,
	0xcd, 0x6b, 0x79, 0xcc, 0x3b, 0x50, 0x1b, 0x30, 0xc7, 0x91, 0xf5, 0xba, 0x2d, 0x95, 0x42, 0x80,
	0xb5, 0xba, 0x03, 0xb5, 0xf0, 0xc2, 0xff, 0x2c, 0xe3, 0xae, 0xcb, 0xb8, 0x42, 0x80, 0x71, 0xef,
	0x43, 0x83, 0x5e, 0xf3, 0xc0, 0xec, 0x8f, 0xcc, 0xc0, 0x74, 0xc3, 0xf6, 0x06, 0xea, 0xeb, 0x28,
	0x7b, 0x87, 0x22, 0x9d, 0xf2, 0xed, 0x24, 0xe5, 0x4f, 0xe0, 0x76, 0x06, 0xdd, 0xf0, 0x82, 0xf1,
	0x77, 0x68, 0x7b, 0x0f, 0x1a, 0xc7, 0xd4, 0x79, 0x69, 0x7a, 0x1e, 0x0d, 0x04, 0x5b, 0x53, 0x7c,
	0x30, 0x76, 0xa1, 0x79, 0xe6, 0x07, 0x3c, 0x36, 0x98, 0x3b, 0xf5, 0x96, 0xa0, 0x79, 0x2a, 0xae,
	0xef, 0x27, 0xee, 0x88, 0x4f, 0xbe, 0x0e, 0x87, 0xc6, 0x4f, 0x45, 0x58, 0x42, 0x89, 0xdc, 0x9c,
	0xf9, 0x6c, 0xdd, 0x80, 0x8a, 0x1f, 0xea, 0x44, 0x2b, 0xfb, 0xe1, 0x94, 0x3d, 0xee, 0xb0, 0x3f,
	0x0e, 0x1c, 0x35, 0xeb, 0xca, 0xcc, 0x1d, 0x7e, 0x08, 0x1c, 0x81, 0x8e, 0xef, 0xc7, 0xee, 0x08,
	0x35, 0x92, 0x57, 0x15, 0xb1, 0x16, 0xaa, 0x24, 0xae, 0x4a, 0x79, 0xb8, 0x2a, 0xe7, 0xe2, 0xaa,
	0x32, 0x83, 0xab, 0x47, 0xd0, 0xf2, 0xfc, 0x73, 0xe6, 0x30, 0x3e, 0xe9, 0x3b, 0xf4, 0x8a, 0x3a,
	0x11, 0xd5, 0x22, 0xe9, 0x1b, 0x21, 0x14, 0x83, 0xc1, 0xb4, 0x10, 0x26, 0xca, 0x4a, 0x32, 0xae,
	0xa1, 0x84, 0xd2, 0x68, 0x0b, 0x20, 0x60, 0xd6, 0x85, 0xb2, 0x00, 0xbc, 0xd0, 0xd6, 0x84, 0x44,
	0xaa, 0x63, 0x08, 0xd7, 0x13, 0x10, 0x6e, 0x43, 0xe5, 0xc2, 0xf4, 0x6c, 0x87, 0x06, 0x48, 0xb7,
	0x5a, 0x2f, 0x5a, 0xce, 0x27, 0x1a, 0xe2, 0x36, 0xb8, 0xa4, 0xa8, 0x6a, 0x61, 0xb7, 0xaa, 0x52,
	0xd0, 0xb5, 0x8d, 0x43, 0x58, 0x3e, 0xa5, 0xfc, 0xd0, 0x99, 0x36, 0x3f, 0x1c, 0x91, 0xbd, 0x04,
	0x76, 0x36, 0x63, 0xec, 0xa4, 0xfa, 0x28, 0xc1, 0xf3, 0xfc, 0x17, 0x80, 0x66, 0x84, 0x2a, 0xb4,
	0x20, 0x27, 0xd0, 0xd0, 0x9d, 0x92, 0x8d, 0x94, 0x8b, 0x08, 0x1c, 0x9d, 0x8e, 0xa6, 0x48, 0x65,
	0x61, 0xdc, 0x22, 0x87, 0x50, 0x3b, 0xa3, 0x0a, 0x74, 0x64, 0x7e, 0x1a, 0x9d, 0x79, 0xee, 0x8d,
	0x5b, 0xe4, 0x05, 0x40, 0x0c, 0x5c, 0x3d, 0x8f, 0x04, 0x9c, 0xf3, 0x3c, 0xfc, 0x1f, 0x6a, 0x53,
	0x6a, 0x10, 0xed, 0x9e, 0xad, 0xf3, 0x25, 0x6f, 0xff, 0x47, 0x58, 0x99, 0x61, 0x28, 0xb9, 0x97,
	0x38, 0xf7, 0xcc, 0xd3, 0xb2, 0xb3, 0x9d, 0xab, 0xc7, 0xe2, 0x7c, 0x82, 0xf5, 0xec, 0x87, 0x36,
	0x79, 0x10, 0x6f, 0x9e, 0xfb, 0x58, 0xcf, 0xcb, 0xf8, 0x3b, 0x58, 0xcd, 0xb8, 0x46, 0x90, 0x9d,
	0xcc, 0x9c, 0xb4, 0x7b, 0x4d, 0xe7, 0xfe, 0x0d, 0x16, 0x98, 0xf7, 0x99, 0xca, 0x7b, 0xe6, 0x32,
	0xa5, 0x17, 0x25, 0xeb, 0xa6, 0x95, 0x97, 0xf2, 0x31, 0x34, 0xf4, 0x70, 0x3a, 0x58, 0x52, 0xf7,
	0xbf, 0x3c, 0x2f, 0x34, 0x71, 0x63, 0xd3, 0x5e, 0xd9, 0xf4, 0x92, 0xce, 0x7d, 0x07, 0xec, 0x3c,
	0xbc, 0xd9, 0x08, 0x2b, 0xf0, 0x2e, 0xbe, 0x94, 0x2a, 0x25, 0xb9, 0x3b, 0xf7, 0x32, 0x2b, 0x1c,
	0x6f, 0xe5, 0x68, 0xd1, 0xe3, 0x11, 0xd4, 0xb5, 0xf7, 0xc3, 0xf9, 0x74, 0xdb, 0x4c, 0x64, 0xa8,
	0xbf, 0x4f, 0x1a, 0xb7, 0x88, 0x97, 0x78, 0xbf, 0x4d, 0xbe, 0x06, 0x93, 0xc7, 0x99, 0x67, 0x9b,
	0xf9, 0x14, 0xd2, 0x79, 0xf2, 0x45, 0x76, 0x18, 0xef, 0x05, 0x40, 0xfc, 0x05, 0x46, 0xcf, 0x39,
	0xf1, 0x5d, 0x26, 0xaf, 0x5f, 0xaf, 0x81, 0xf4, 0xe8, 0x90, 0x7a, 0x34, 0x30, 0x39, 0xbd, 0xf9,
	0xf4, 0x39, 0x9e, 0x5e, 0x42, 0x5d, 0xfb, 0x74, 0x44, 0xb4, 0xc7, 0x65, 0xf2, 0x8b, 0x52, 0x8e,
	0x8f, 0x4e, 0xe7, 0x8f, 0x3f, 0xb7, 0xd7, 0x4f, 0xd2, 0x8d, 0x92, 0xb3, 0xea, 0xd9, 0xb7, 0x4f,
	0x87, 0xbe, 0x63, 0x7a, 0xc3, 0xfd, 0x7f, 0x3f, 0xe7, 0x7c, 0xdf, 0xf2, 0xdd, 0x03, 0xfc, 0x66,
	0x65, 0xf9, 0xce, 0x41, 0x48, 0x83, 0x2b, 0x66, 0xd1, 0xf0, 0x20, 0xda, 0x72, 0x5e, 0x46, 0xdd,
	0xbf, 0xfe, 0x0a, 0x00, 0x00, 0xff, 0xff, 0xc8, 0x32, 0x4b, 0x10, 0x1b, 0x13, 0x00, 0x00,
}
