// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/exchange/exchange_svr.proto

package Exchange

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockExchangeClient is a mock of ExchangeClient interface.
type MockExchangeClient struct {
	ctrl     *gomock.Controller
	recorder *MockExchangeClientMockRecorder
}

// MockExchangeClientMockRecorder is the mock recorder for MockExchangeClient.
type MockExchangeClientMockRecorder struct {
	mock *MockExchangeClient
}

// NewMockExchangeClient creates a new mock instance.
func NewMockExchangeClient(ctrl *gomock.Controller) *MockExchangeClient {
	mock := &MockExchangeClient{ctrl: ctrl}
	mock.recorder = &MockExchangeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockExchangeClient) EXPECT() *MockExchangeClientMockRecorder {
	return m.recorder
}

// AddBlacklist mocks base method.
func (m *MockExchangeClient) AddBlacklist(ctx context.Context, in *AddBlacklistReq, opts ...grpc.CallOption) (*AddBlacklistResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddBlacklist", varargs...)
	ret0, _ := ret[0].(*AddBlacklistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddBlacklist indicates an expected call of AddBlacklist.
func (mr *MockExchangeClientMockRecorder) AddBlacklist(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBlacklist", reflect.TypeOf((*MockExchangeClient)(nil).AddBlacklist), varargs...)
}

// AddInnerWhite mocks base method.
func (m *MockExchangeClient) AddInnerWhite(ctx context.Context, in *AddInnerWhiteReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddInnerWhite", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddInnerWhite indicates an expected call of AddInnerWhite.
func (mr *MockExchangeClientMockRecorder) AddInnerWhite(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddInnerWhite", reflect.TypeOf((*MockExchangeClient)(nil).AddInnerWhite), varargs...)
}

// AddPrivateWithdrawLog mocks base method.
func (m *MockExchangeClient) AddPrivateWithdrawLog(ctx context.Context, in *AddPrivateWithdrawLogReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPrivateWithdrawLog", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPrivateWithdrawLog indicates an expected call of AddPrivateWithdrawLog.
func (mr *MockExchangeClientMockRecorder) AddPrivateWithdrawLog(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPrivateWithdrawLog", reflect.TypeOf((*MockExchangeClient)(nil).AddPrivateWithdrawLog), varargs...)
}

// BatchCheckRecycleScore mocks base method.
func (m *MockExchangeClient) BatchCheckRecycleScore(ctx context.Context, in *BatchCheckRecycleScoreReq, opts ...grpc.CallOption) (*BatchCheckRecycleScoreResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchCheckRecycleScore", varargs...)
	ret0, _ := ret[0].(*BatchCheckRecycleScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckRecycleScore indicates an expected call of BatchCheckRecycleScore.
func (mr *MockExchangeClientMockRecorder) BatchCheckRecycleScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckRecycleScore", reflect.TypeOf((*MockExchangeClient)(nil).BatchCheckRecycleScore), varargs...)
}

// BatchCreateScoreRecycleTask mocks base method.
func (m *MockExchangeClient) BatchCreateScoreRecycleTask(ctx context.Context, in *BatchCreateScoreRecycleTaskReq, opts ...grpc.CallOption) (*BatchCreateScoreRecycleTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchCreateScoreRecycleTask", varargs...)
	ret0, _ := ret[0].(*BatchCreateScoreRecycleTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCreateScoreRecycleTask indicates an expected call of BatchCreateScoreRecycleTask.
func (mr *MockExchangeClientMockRecorder) BatchCreateScoreRecycleTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateScoreRecycleTask", reflect.TypeOf((*MockExchangeClient)(nil).BatchCreateScoreRecycleTask), varargs...)
}

// BatchGetBlacklist mocks base method.
func (m *MockExchangeClient) BatchGetBlacklist(ctx context.Context, in *BatchGetBlacklistReq, opts ...grpc.CallOption) (*BatchGetBlacklistResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetBlacklist", varargs...)
	ret0, _ := ret[0].(*BatchGetBlacklistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBlacklist indicates an expected call of BatchGetBlacklist.
func (mr *MockExchangeClientMockRecorder) BatchGetBlacklist(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBlacklist", reflect.TypeOf((*MockExchangeClient)(nil).BatchGetBlacklist), varargs...)
}

// BatchGetCannotUnfreezeUid mocks base method.
func (m *MockExchangeClient) BatchGetCannotUnfreezeUid(ctx context.Context, in *BatchGetCannotUnfreezeUidReq, opts ...grpc.CallOption) (*BatchGetCannotUnfreezeUidResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetCannotUnfreezeUid", varargs...)
	ret0, _ := ret[0].(*BatchGetCannotUnfreezeUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCannotUnfreezeUid indicates an expected call of BatchGetCannotUnfreezeUid.
func (mr *MockExchangeClientMockRecorder) BatchGetCannotUnfreezeUid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCannotUnfreezeUid", reflect.TypeOf((*MockExchangeClient)(nil).BatchGetCannotUnfreezeUid), varargs...)
}

// BatchGetFreezeStatus mocks base method.
func (m *MockExchangeClient) BatchGetFreezeStatus(ctx context.Context, in *BatchGetFreezeStatusReq, opts ...grpc.CallOption) (*BatchGetFreezeStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetFreezeStatus", varargs...)
	ret0, _ := ret[0].(*BatchGetFreezeStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetFreezeStatus indicates an expected call of BatchGetFreezeStatus.
func (mr *MockExchangeClientMockRecorder) BatchGetFreezeStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetFreezeStatus", reflect.TypeOf((*MockExchangeClient)(nil).BatchGetFreezeStatus), varargs...)
}

// BatchRunScoreRecycleTask mocks base method.
func (m *MockExchangeClient) BatchRunScoreRecycleTask(ctx context.Context, in *BatchRunScoreRecycleTaskReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchRunScoreRecycleTask", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchRunScoreRecycleTask indicates an expected call of BatchRunScoreRecycleTask.
func (mr *MockExchangeClientMockRecorder) BatchRunScoreRecycleTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRunScoreRecycleTask", reflect.TypeOf((*MockExchangeClient)(nil).BatchRunScoreRecycleTask), varargs...)
}

// BeginTransaction mocks base method.
func (m *MockExchangeClient) BeginTransaction(ctx context.Context, in *BeginTransactionReq, opts ...grpc.CallOption) (*BeginTransactionResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BeginTransaction", varargs...)
	ret0, _ := ret[0].(*BeginTransactionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BeginTransaction indicates an expected call of BeginTransaction.
func (mr *MockExchangeClientMockRecorder) BeginTransaction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BeginTransaction", reflect.TypeOf((*MockExchangeClient)(nil).BeginTransaction), varargs...)
}

// CheckIfInBlacklist mocks base method.
func (m *MockExchangeClient) CheckIfInBlacklist(ctx context.Context, in *CheckIfInBlacklistReq, opts ...grpc.CallOption) (*CheckIfInBlacklistResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckIfInBlacklist", varargs...)
	ret0, _ := ret[0].(*CheckIfInBlacklistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfInBlacklist indicates an expected call of CheckIfInBlacklist.
func (mr *MockExchangeClientMockRecorder) CheckIfInBlacklist(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfInBlacklist", reflect.TypeOf((*MockExchangeClient)(nil).CheckIfInBlacklist), varargs...)
}

// CheckInnerWhite mocks base method.
func (m *MockExchangeClient) CheckInnerWhite(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*CheckInnerWhiteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckInnerWhite", varargs...)
	ret0, _ := ret[0].(*CheckInnerWhiteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckInnerWhite indicates an expected call of CheckInnerWhite.
func (mr *MockExchangeClientMockRecorder) CheckInnerWhite(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInnerWhite", reflect.TypeOf((*MockExchangeClient)(nil).CheckInnerWhite), varargs...)
}

// CheckPrivateWithdraw mocks base method.
func (m *MockExchangeClient) CheckPrivateWithdraw(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckPrivateWithdraw", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckPrivateWithdraw indicates an expected call of CheckPrivateWithdraw.
func (mr *MockExchangeClientMockRecorder) CheckPrivateWithdraw(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckPrivateWithdraw", reflect.TypeOf((*MockExchangeClient)(nil).CheckPrivateWithdraw), varargs...)
}

// ConfirmUserDistributor mocks base method.
func (m *MockExchangeClient) ConfirmUserDistributor(ctx context.Context, in *ConfirmUserDistributorReq, opts ...grpc.CallOption) (*ConfirmUserDistributorResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmUserDistributor", varargs...)
	ret0, _ := ret[0].(*ConfirmUserDistributorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmUserDistributor indicates an expected call of ConfirmUserDistributor.
func (mr *MockExchangeClientMockRecorder) ConfirmUserDistributor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmUserDistributor", reflect.TypeOf((*MockExchangeClient)(nil).ConfirmUserDistributor), varargs...)
}

// CreateRemitLog mocks base method.
func (m *MockExchangeClient) CreateRemitLog(ctx context.Context, in *RemitLogInputData, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRemitLog", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRemitLog indicates an expected call of CreateRemitLog.
func (mr *MockExchangeClientMockRecorder) CreateRemitLog(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRemitLog", reflect.TypeOf((*MockExchangeClient)(nil).CreateRemitLog), varargs...)
}

// FreezeESportScore mocks base method.
func (m *MockExchangeClient) FreezeESportScore(ctx context.Context, in *FreezeESportScoreReq, opts ...grpc.CallOption) (*FreezeESportScoreResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FreezeESportScore", varargs...)
	ret0, _ := ret[0].(*FreezeESportScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreezeESportScore indicates an expected call of FreezeESportScore.
func (mr *MockExchangeClientMockRecorder) FreezeESportScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreezeESportScore", reflect.TypeOf((*MockExchangeClient)(nil).FreezeESportScore), varargs...)
}

// FreezePartScore mocks base method.
func (m *MockExchangeClient) FreezePartScore(ctx context.Context, in *FreezePartScoreReq, opts ...grpc.CallOption) (*ErrorList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FreezePartScore", varargs...)
	ret0, _ := ret[0].(*ErrorList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreezePartScore indicates an expected call of FreezePartScore.
func (mr *MockExchangeClientMockRecorder) FreezePartScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreezePartScore", reflect.TypeOf((*MockExchangeClient)(nil).FreezePartScore), varargs...)
}

// FreezeScore mocks base method.
func (m *MockExchangeClient) FreezeScore(ctx context.Context, in *FreezeScoreReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FreezeScore", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreezeScore indicates an expected call of FreezeScore.
func (mr *MockExchangeClientMockRecorder) FreezeScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreezeScore", reflect.TypeOf((*MockExchangeClient)(nil).FreezeScore), varargs...)
}

// GenerateLastMonthDistributorPercent mocks base method.
func (m *MockExchangeClient) GenerateLastMonthDistributorPercent(ctx context.Context, in *EmptyMsg, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateLastMonthDistributorPercent", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateLastMonthDistributorPercent indicates an expected call of GenerateLastMonthDistributorPercent.
func (mr *MockExchangeClientMockRecorder) GenerateLastMonthDistributorPercent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateLastMonthDistributorPercent", reflect.TypeOf((*MockExchangeClient)(nil).GenerateLastMonthDistributorPercent), varargs...)
}

// GetCurrentMonthWithdrawRemain mocks base method.
func (m *MockExchangeClient) GetCurrentMonthWithdrawRemain(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*WithdrawRemain, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCurrentMonthWithdrawRemain", varargs...)
	ret0, _ := ret[0].(*WithdrawRemain)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentMonthWithdrawRemain indicates an expected call of GetCurrentMonthWithdrawRemain.
func (mr *MockExchangeClientMockRecorder) GetCurrentMonthWithdrawRemain(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentMonthWithdrawRemain", reflect.TypeOf((*MockExchangeClient)(nil).GetCurrentMonthWithdrawRemain), varargs...)
}

// GetESportScoreFreezeHistory mocks base method.
func (m *MockExchangeClient) GetESportScoreFreezeHistory(ctx context.Context, in *GetESportScoreFreezeHistoryReq, opts ...grpc.CallOption) (*GetESportScoreFreezeHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetESportScoreFreezeHistory", varargs...)
	ret0, _ := ret[0].(*GetESportScoreFreezeHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetESportScoreFreezeHistory indicates an expected call of GetESportScoreFreezeHistory.
func (mr *MockExchangeClientMockRecorder) GetESportScoreFreezeHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetESportScoreFreezeHistory", reflect.TypeOf((*MockExchangeClient)(nil).GetESportScoreFreezeHistory), varargs...)
}

// GetESportScoreFreezeList mocks base method.
func (m *MockExchangeClient) GetESportScoreFreezeList(ctx context.Context, in *GetESportScoreFreezeListReq, opts ...grpc.CallOption) (*GetESportScoreFreezeListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetESportScoreFreezeList", varargs...)
	ret0, _ := ret[0].(*GetESportScoreFreezeListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetESportScoreFreezeList indicates an expected call of GetESportScoreFreezeList.
func (mr *MockExchangeClientMockRecorder) GetESportScoreFreezeList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetESportScoreFreezeList", reflect.TypeOf((*MockExchangeClient)(nil).GetESportScoreFreezeList), varargs...)
}

// GetMonthRemitList mocks base method.
func (m *MockExchangeClient) GetMonthRemitList(ctx context.Context, in *GetMonthRemitListReq, opts ...grpc.CallOption) (*GetMonthRemitListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMonthRemitList", varargs...)
	ret0, _ := ret[0].(*GetMonthRemitListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMonthRemitList indicates an expected call of GetMonthRemitList.
func (mr *MockExchangeClientMockRecorder) GetMonthRemitList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMonthRemitList", reflect.TypeOf((*MockExchangeClient)(nil).GetMonthRemitList), varargs...)
}

// GetRemitDetailList mocks base method.
func (m *MockExchangeClient) GetRemitDetailList(ctx context.Context, in *GetRemitDetailListReq, opts ...grpc.CallOption) (*RemitDetailDataList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRemitDetailList", varargs...)
	ret0, _ := ret[0].(*RemitDetailDataList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRemitDetailList indicates an expected call of GetRemitDetailList.
func (mr *MockExchangeClientMockRecorder) GetRemitDetailList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRemitDetailList", reflect.TypeOf((*MockExchangeClient)(nil).GetRemitDetailList), varargs...)
}

// GetRemitDetailWithIdAndMonth mocks base method.
func (m *MockExchangeClient) GetRemitDetailWithIdAndMonth(ctx context.Context, in *GetRemitDetailWithIdAndMonthReq, opts ...grpc.CallOption) (*RemitDetailDataList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRemitDetailWithIdAndMonth", varargs...)
	ret0, _ := ret[0].(*RemitDetailDataList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRemitDetailWithIdAndMonth indicates an expected call of GetRemitDetailWithIdAndMonth.
func (mr *MockExchangeClientMockRecorder) GetRemitDetailWithIdAndMonth(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRemitDetailWithIdAndMonth", reflect.TypeOf((*MockExchangeClient)(nil).GetRemitDetailWithIdAndMonth), varargs...)
}

// GetRemitLog mocks base method.
func (m *MockExchangeClient) GetRemitLog(ctx context.Context, in *OffsetReq, opts ...grpc.CallOption) (*RemitLogList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRemitLog", varargs...)
	ret0, _ := ret[0].(*RemitLogList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRemitLog indicates an expected call of GetRemitLog.
func (mr *MockExchangeClientMockRecorder) GetRemitLog(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRemitLog", reflect.TypeOf((*MockExchangeClient)(nil).GetRemitLog), varargs...)
}

// GetScoreCanWithdraw mocks base method.
func (m *MockExchangeClient) GetScoreCanWithdraw(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*GetScoreCanWithdrawResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScoreCanWithdraw", varargs...)
	ret0, _ := ret[0].(*GetScoreCanWithdrawResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScoreCanWithdraw indicates an expected call of GetScoreCanWithdraw.
func (mr *MockExchangeClientMockRecorder) GetScoreCanWithdraw(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScoreCanWithdraw", reflect.TypeOf((*MockExchangeClient)(nil).GetScoreCanWithdraw), varargs...)
}

// GetScoreFreezeList mocks base method.
func (m *MockExchangeClient) GetScoreFreezeList(ctx context.Context, in *GetScoreFreezeListReq, opts ...grpc.CallOption) (*GetScoreFreezeListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScoreFreezeList", varargs...)
	ret0, _ := ret[0].(*GetScoreFreezeListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScoreFreezeList indicates an expected call of GetScoreFreezeList.
func (mr *MockExchangeClientMockRecorder) GetScoreFreezeList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScoreFreezeList", reflect.TypeOf((*MockExchangeClient)(nil).GetScoreFreezeList), varargs...)
}

// GetScorePartFreezeHistory mocks base method.
func (m *MockExchangeClient) GetScorePartFreezeHistory(ctx context.Context, in *GetScorePartFreezeHistoryReq, opts ...grpc.CallOption) (*GetScorePartFreezeHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScorePartFreezeHistory", varargs...)
	ret0, _ := ret[0].(*GetScorePartFreezeHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScorePartFreezeHistory indicates an expected call of GetScorePartFreezeHistory.
func (mr *MockExchangeClientMockRecorder) GetScorePartFreezeHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScorePartFreezeHistory", reflect.TypeOf((*MockExchangeClient)(nil).GetScorePartFreezeHistory), varargs...)
}

// GetScorePartFreezeList mocks base method.
func (m *MockExchangeClient) GetScorePartFreezeList(ctx context.Context, in *GetScorePartFreezeListReq, opts ...grpc.CallOption) (*GetScorePartFreezeListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScorePartFreezeList", varargs...)
	ret0, _ := ret[0].(*GetScorePartFreezeListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScorePartFreezeList indicates an expected call of GetScorePartFreezeList.
func (mr *MockExchangeClientMockRecorder) GetScorePartFreezeList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScorePartFreezeList", reflect.TypeOf((*MockExchangeClient)(nil).GetScorePartFreezeList), varargs...)
}

// GetScoreRecycleTaskList mocks base method.
func (m *MockExchangeClient) GetScoreRecycleTaskList(ctx context.Context, in *GetScoreRecycleTaskListReq, opts ...grpc.CallOption) (*GetScoreRecycleTaskListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScoreRecycleTaskList", varargs...)
	ret0, _ := ret[0].(*GetScoreRecycleTaskListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScoreRecycleTaskList indicates an expected call of GetScoreRecycleTaskList.
func (mr *MockExchangeClientMockRecorder) GetScoreRecycleTaskList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScoreRecycleTaskList", reflect.TypeOf((*MockExchangeClient)(nil).GetScoreRecycleTaskList), varargs...)
}

// GetSettlementHistory mocks base method.
func (m *MockExchangeClient) GetSettlementHistory(ctx context.Context, in *GetSettlementRecordReq, opts ...grpc.CallOption) (*GetTransactionHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSettlementHistory", varargs...)
	ret0, _ := ret[0].(*GetTransactionHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettlementHistory indicates an expected call of GetSettlementHistory.
func (mr *MockExchangeClientMockRecorder) GetSettlementHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettlementHistory", reflect.TypeOf((*MockExchangeClient)(nil).GetSettlementHistory), varargs...)
}

// GetTransactionHistory mocks base method.
func (m *MockExchangeClient) GetTransactionHistory(ctx context.Context, in *GetTransactionHistoryReq, opts ...grpc.CallOption) (*GetTransactionHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionHistory", varargs...)
	ret0, _ := ret[0].(*GetTransactionHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionHistory indicates an expected call of GetTransactionHistory.
func (mr *MockExchangeClientMockRecorder) GetTransactionHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionHistory", reflect.TypeOf((*MockExchangeClient)(nil).GetTransactionHistory), varargs...)
}

// GetUserScorePop mocks base method.
func (m *MockExchangeClient) GetUserScorePop(ctx context.Context, in *GetUserScorePopReq, opts ...grpc.CallOption) (*GetUserScorePopResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserScorePop", varargs...)
	ret0, _ := ret[0].(*GetUserScorePopResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserScorePop indicates an expected call of GetUserScorePop.
func (mr *MockExchangeClientMockRecorder) GetUserScorePop(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserScorePop", reflect.TypeOf((*MockExchangeClient)(nil).GetUserScorePop), varargs...)
}

// InsertRemitDetailData mocks base method.
func (m *MockExchangeClient) InsertRemitDetailData(ctx context.Context, in *InsertRemitDetailDataReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InsertRemitDetailData", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertRemitDetailData indicates an expected call of InsertRemitDetailData.
func (mr *MockExchangeClientMockRecorder) InsertRemitDetailData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertRemitDetailData", reflect.TypeOf((*MockExchangeClient)(nil).InsertRemitDetailData), varargs...)
}

// PrivateWithdraw mocks base method.
func (m *MockExchangeClient) PrivateWithdraw(ctx context.Context, in *PrivateWithdrawReq, opts ...grpc.CallOption) (*PrivateWithdrawResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PrivateWithdraw", varargs...)
	ret0, _ := ret[0].(*PrivateWithdrawResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PrivateWithdraw indicates an expected call of PrivateWithdraw.
func (mr *MockExchangeClientMockRecorder) PrivateWithdraw(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrivateWithdraw", reflect.TypeOf((*MockExchangeClient)(nil).PrivateWithdraw), varargs...)
}

// SetScoreCanWithdraw mocks base method.
func (m *MockExchangeClient) SetScoreCanWithdraw(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetScoreCanWithdraw", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetScoreCanWithdraw indicates an expected call of SetScoreCanWithdraw.
func (mr *MockExchangeClientMockRecorder) SetScoreCanWithdraw(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetScoreCanWithdraw", reflect.TypeOf((*MockExchangeClient)(nil).SetScoreCanWithdraw), varargs...)
}

// SetUserScoreNotPop mocks base method.
func (m *MockExchangeClient) SetUserScoreNotPop(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserScoreNotPop", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserScoreNotPop indicates an expected call of SetUserScoreNotPop.
func (mr *MockExchangeClientMockRecorder) SetUserScoreNotPop(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserScoreNotPop", reflect.TypeOf((*MockExchangeClient)(nil).SetUserScoreNotPop), varargs...)
}

// UnfreezeESportScore mocks base method.
func (m *MockExchangeClient) UnfreezeESportScore(ctx context.Context, in *UnfreezeESportScoreReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnfreezeESportScore", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnfreezeESportScore indicates an expected call of UnfreezeESportScore.
func (mr *MockExchangeClientMockRecorder) UnfreezeESportScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfreezeESportScore", reflect.TypeOf((*MockExchangeClient)(nil).UnfreezeESportScore), varargs...)
}

// UnfreezePartScore mocks base method.
func (m *MockExchangeClient) UnfreezePartScore(ctx context.Context, in *UnfreezePartScoreReq, opts ...grpc.CallOption) (*ErrorList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnfreezePartScore", varargs...)
	ret0, _ := ret[0].(*ErrorList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnfreezePartScore indicates an expected call of UnfreezePartScore.
func (mr *MockExchangeClientMockRecorder) UnfreezePartScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfreezePartScore", reflect.TypeOf((*MockExchangeClient)(nil).UnfreezePartScore), varargs...)
}

// UnfreezeScore mocks base method.
func (m *MockExchangeClient) UnfreezeScore(ctx context.Context, in *UnfreezeScoreReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnfreezeScore", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnfreezeScore indicates an expected call of UnfreezeScore.
func (mr *MockExchangeClientMockRecorder) UnfreezeScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfreezeScore", reflect.TypeOf((*MockExchangeClient)(nil).UnfreezeScore), varargs...)
}

// UpdateBlacklist mocks base method.
func (m *MockExchangeClient) UpdateBlacklist(ctx context.Context, in *UpdateBlacklistReq, opts ...grpc.CallOption) (*UpdateBlacklistResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateBlacklist", varargs...)
	ret0, _ := ret[0].(*UpdateBlacklistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBlacklist indicates an expected call of UpdateBlacklist.
func (mr *MockExchangeClientMockRecorder) UpdateBlacklist(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBlacklist", reflect.TypeOf((*MockExchangeClient)(nil).UpdateBlacklist), varargs...)
}

// MockExchangeServer is a mock of ExchangeServer interface.
type MockExchangeServer struct {
	ctrl     *gomock.Controller
	recorder *MockExchangeServerMockRecorder
}

// MockExchangeServerMockRecorder is the mock recorder for MockExchangeServer.
type MockExchangeServerMockRecorder struct {
	mock *MockExchangeServer
}

// NewMockExchangeServer creates a new mock instance.
func NewMockExchangeServer(ctrl *gomock.Controller) *MockExchangeServer {
	mock := &MockExchangeServer{ctrl: ctrl}
	mock.recorder = &MockExchangeServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockExchangeServer) EXPECT() *MockExchangeServerMockRecorder {
	return m.recorder
}

// AddBlacklist mocks base method.
func (m *MockExchangeServer) AddBlacklist(ctx context.Context, in *AddBlacklistReq) (*AddBlacklistResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBlacklist", ctx, in)
	ret0, _ := ret[0].(*AddBlacklistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddBlacklist indicates an expected call of AddBlacklist.
func (mr *MockExchangeServerMockRecorder) AddBlacklist(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBlacklist", reflect.TypeOf((*MockExchangeServer)(nil).AddBlacklist), ctx, in)
}

// AddInnerWhite mocks base method.
func (m *MockExchangeServer) AddInnerWhite(ctx context.Context, in *AddInnerWhiteReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddInnerWhite", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddInnerWhite indicates an expected call of AddInnerWhite.
func (mr *MockExchangeServerMockRecorder) AddInnerWhite(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddInnerWhite", reflect.TypeOf((*MockExchangeServer)(nil).AddInnerWhite), ctx, in)
}

// AddPrivateWithdrawLog mocks base method.
func (m *MockExchangeServer) AddPrivateWithdrawLog(ctx context.Context, in *AddPrivateWithdrawLogReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPrivateWithdrawLog", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPrivateWithdrawLog indicates an expected call of AddPrivateWithdrawLog.
func (mr *MockExchangeServerMockRecorder) AddPrivateWithdrawLog(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPrivateWithdrawLog", reflect.TypeOf((*MockExchangeServer)(nil).AddPrivateWithdrawLog), ctx, in)
}

// BatchCheckRecycleScore mocks base method.
func (m *MockExchangeServer) BatchCheckRecycleScore(ctx context.Context, in *BatchCheckRecycleScoreReq) (*BatchCheckRecycleScoreResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckRecycleScore", ctx, in)
	ret0, _ := ret[0].(*BatchCheckRecycleScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckRecycleScore indicates an expected call of BatchCheckRecycleScore.
func (mr *MockExchangeServerMockRecorder) BatchCheckRecycleScore(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckRecycleScore", reflect.TypeOf((*MockExchangeServer)(nil).BatchCheckRecycleScore), ctx, in)
}

// BatchCreateScoreRecycleTask mocks base method.
func (m *MockExchangeServer) BatchCreateScoreRecycleTask(ctx context.Context, in *BatchCreateScoreRecycleTaskReq) (*BatchCreateScoreRecycleTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateScoreRecycleTask", ctx, in)
	ret0, _ := ret[0].(*BatchCreateScoreRecycleTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCreateScoreRecycleTask indicates an expected call of BatchCreateScoreRecycleTask.
func (mr *MockExchangeServerMockRecorder) BatchCreateScoreRecycleTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateScoreRecycleTask", reflect.TypeOf((*MockExchangeServer)(nil).BatchCreateScoreRecycleTask), ctx, in)
}

// BatchGetBlacklist mocks base method.
func (m *MockExchangeServer) BatchGetBlacklist(ctx context.Context, in *BatchGetBlacklistReq) (*BatchGetBlacklistResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBlacklist", ctx, in)
	ret0, _ := ret[0].(*BatchGetBlacklistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBlacklist indicates an expected call of BatchGetBlacklist.
func (mr *MockExchangeServerMockRecorder) BatchGetBlacklist(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBlacklist", reflect.TypeOf((*MockExchangeServer)(nil).BatchGetBlacklist), ctx, in)
}

// BatchGetCannotUnfreezeUid mocks base method.
func (m *MockExchangeServer) BatchGetCannotUnfreezeUid(ctx context.Context, in *BatchGetCannotUnfreezeUidReq) (*BatchGetCannotUnfreezeUidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetCannotUnfreezeUid", ctx, in)
	ret0, _ := ret[0].(*BatchGetCannotUnfreezeUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCannotUnfreezeUid indicates an expected call of BatchGetCannotUnfreezeUid.
func (mr *MockExchangeServerMockRecorder) BatchGetCannotUnfreezeUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCannotUnfreezeUid", reflect.TypeOf((*MockExchangeServer)(nil).BatchGetCannotUnfreezeUid), ctx, in)
}

// BatchGetFreezeStatus mocks base method.
func (m *MockExchangeServer) BatchGetFreezeStatus(ctx context.Context, in *BatchGetFreezeStatusReq) (*BatchGetFreezeStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetFreezeStatus", ctx, in)
	ret0, _ := ret[0].(*BatchGetFreezeStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetFreezeStatus indicates an expected call of BatchGetFreezeStatus.
func (mr *MockExchangeServerMockRecorder) BatchGetFreezeStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetFreezeStatus", reflect.TypeOf((*MockExchangeServer)(nil).BatchGetFreezeStatus), ctx, in)
}

// BatchRunScoreRecycleTask mocks base method.
func (m *MockExchangeServer) BatchRunScoreRecycleTask(ctx context.Context, in *BatchRunScoreRecycleTaskReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchRunScoreRecycleTask", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchRunScoreRecycleTask indicates an expected call of BatchRunScoreRecycleTask.
func (mr *MockExchangeServerMockRecorder) BatchRunScoreRecycleTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRunScoreRecycleTask", reflect.TypeOf((*MockExchangeServer)(nil).BatchRunScoreRecycleTask), ctx, in)
}

// BeginTransaction mocks base method.
func (m *MockExchangeServer) BeginTransaction(ctx context.Context, in *BeginTransactionReq) (*BeginTransactionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BeginTransaction", ctx, in)
	ret0, _ := ret[0].(*BeginTransactionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BeginTransaction indicates an expected call of BeginTransaction.
func (mr *MockExchangeServerMockRecorder) BeginTransaction(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BeginTransaction", reflect.TypeOf((*MockExchangeServer)(nil).BeginTransaction), ctx, in)
}

// CheckIfInBlacklist mocks base method.
func (m *MockExchangeServer) CheckIfInBlacklist(ctx context.Context, in *CheckIfInBlacklistReq) (*CheckIfInBlacklistResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfInBlacklist", ctx, in)
	ret0, _ := ret[0].(*CheckIfInBlacklistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfInBlacklist indicates an expected call of CheckIfInBlacklist.
func (mr *MockExchangeServerMockRecorder) CheckIfInBlacklist(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfInBlacklist", reflect.TypeOf((*MockExchangeServer)(nil).CheckIfInBlacklist), ctx, in)
}

// CheckInnerWhite mocks base method.
func (m *MockExchangeServer) CheckInnerWhite(ctx context.Context, in *UidReq) (*CheckInnerWhiteResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckInnerWhite", ctx, in)
	ret0, _ := ret[0].(*CheckInnerWhiteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckInnerWhite indicates an expected call of CheckInnerWhite.
func (mr *MockExchangeServerMockRecorder) CheckInnerWhite(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInnerWhite", reflect.TypeOf((*MockExchangeServer)(nil).CheckInnerWhite), ctx, in)
}

// CheckPrivateWithdraw mocks base method.
func (m *MockExchangeServer) CheckPrivateWithdraw(ctx context.Context, in *UidReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckPrivateWithdraw", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckPrivateWithdraw indicates an expected call of CheckPrivateWithdraw.
func (mr *MockExchangeServerMockRecorder) CheckPrivateWithdraw(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckPrivateWithdraw", reflect.TypeOf((*MockExchangeServer)(nil).CheckPrivateWithdraw), ctx, in)
}

// ConfirmUserDistributor mocks base method.
func (m *MockExchangeServer) ConfirmUserDistributor(ctx context.Context, in *ConfirmUserDistributorReq) (*ConfirmUserDistributorResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmUserDistributor", ctx, in)
	ret0, _ := ret[0].(*ConfirmUserDistributorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmUserDistributor indicates an expected call of ConfirmUserDistributor.
func (mr *MockExchangeServerMockRecorder) ConfirmUserDistributor(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmUserDistributor", reflect.TypeOf((*MockExchangeServer)(nil).ConfirmUserDistributor), ctx, in)
}

// CreateRemitLog mocks base method.
func (m *MockExchangeServer) CreateRemitLog(ctx context.Context, in *RemitLogInputData) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRemitLog", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRemitLog indicates an expected call of CreateRemitLog.
func (mr *MockExchangeServerMockRecorder) CreateRemitLog(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRemitLog", reflect.TypeOf((*MockExchangeServer)(nil).CreateRemitLog), ctx, in)
}

// FreezeESportScore mocks base method.
func (m *MockExchangeServer) FreezeESportScore(ctx context.Context, in *FreezeESportScoreReq) (*FreezeESportScoreResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FreezeESportScore", ctx, in)
	ret0, _ := ret[0].(*FreezeESportScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreezeESportScore indicates an expected call of FreezeESportScore.
func (mr *MockExchangeServerMockRecorder) FreezeESportScore(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreezeESportScore", reflect.TypeOf((*MockExchangeServer)(nil).FreezeESportScore), ctx, in)
}

// FreezePartScore mocks base method.
func (m *MockExchangeServer) FreezePartScore(ctx context.Context, in *FreezePartScoreReq) (*ErrorList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FreezePartScore", ctx, in)
	ret0, _ := ret[0].(*ErrorList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreezePartScore indicates an expected call of FreezePartScore.
func (mr *MockExchangeServerMockRecorder) FreezePartScore(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreezePartScore", reflect.TypeOf((*MockExchangeServer)(nil).FreezePartScore), ctx, in)
}

// FreezeScore mocks base method.
func (m *MockExchangeServer) FreezeScore(ctx context.Context, in *FreezeScoreReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FreezeScore", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreezeScore indicates an expected call of FreezeScore.
func (mr *MockExchangeServerMockRecorder) FreezeScore(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreezeScore", reflect.TypeOf((*MockExchangeServer)(nil).FreezeScore), ctx, in)
}

// GenerateLastMonthDistributorPercent mocks base method.
func (m *MockExchangeServer) GenerateLastMonthDistributorPercent(ctx context.Context, in *EmptyMsg) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateLastMonthDistributorPercent", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateLastMonthDistributorPercent indicates an expected call of GenerateLastMonthDistributorPercent.
func (mr *MockExchangeServerMockRecorder) GenerateLastMonthDistributorPercent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateLastMonthDistributorPercent", reflect.TypeOf((*MockExchangeServer)(nil).GenerateLastMonthDistributorPercent), ctx, in)
}

// GetCurrentMonthWithdrawRemain mocks base method.
func (m *MockExchangeServer) GetCurrentMonthWithdrawRemain(ctx context.Context, in *UidReq) (*WithdrawRemain, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentMonthWithdrawRemain", ctx, in)
	ret0, _ := ret[0].(*WithdrawRemain)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentMonthWithdrawRemain indicates an expected call of GetCurrentMonthWithdrawRemain.
func (mr *MockExchangeServerMockRecorder) GetCurrentMonthWithdrawRemain(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentMonthWithdrawRemain", reflect.TypeOf((*MockExchangeServer)(nil).GetCurrentMonthWithdrawRemain), ctx, in)
}

// GetESportScoreFreezeHistory mocks base method.
func (m *MockExchangeServer) GetESportScoreFreezeHistory(ctx context.Context, in *GetESportScoreFreezeHistoryReq) (*GetESportScoreFreezeHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetESportScoreFreezeHistory", ctx, in)
	ret0, _ := ret[0].(*GetESportScoreFreezeHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetESportScoreFreezeHistory indicates an expected call of GetESportScoreFreezeHistory.
func (mr *MockExchangeServerMockRecorder) GetESportScoreFreezeHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetESportScoreFreezeHistory", reflect.TypeOf((*MockExchangeServer)(nil).GetESportScoreFreezeHistory), ctx, in)
}

// GetESportScoreFreezeList mocks base method.
func (m *MockExchangeServer) GetESportScoreFreezeList(ctx context.Context, in *GetESportScoreFreezeListReq) (*GetESportScoreFreezeListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetESportScoreFreezeList", ctx, in)
	ret0, _ := ret[0].(*GetESportScoreFreezeListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetESportScoreFreezeList indicates an expected call of GetESportScoreFreezeList.
func (mr *MockExchangeServerMockRecorder) GetESportScoreFreezeList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetESportScoreFreezeList", reflect.TypeOf((*MockExchangeServer)(nil).GetESportScoreFreezeList), ctx, in)
}

// GetMonthRemitList mocks base method.
func (m *MockExchangeServer) GetMonthRemitList(ctx context.Context, in *GetMonthRemitListReq) (*GetMonthRemitListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMonthRemitList", ctx, in)
	ret0, _ := ret[0].(*GetMonthRemitListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMonthRemitList indicates an expected call of GetMonthRemitList.
func (mr *MockExchangeServerMockRecorder) GetMonthRemitList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMonthRemitList", reflect.TypeOf((*MockExchangeServer)(nil).GetMonthRemitList), ctx, in)
}

// GetRemitDetailList mocks base method.
func (m *MockExchangeServer) GetRemitDetailList(ctx context.Context, in *GetRemitDetailListReq) (*RemitDetailDataList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRemitDetailList", ctx, in)
	ret0, _ := ret[0].(*RemitDetailDataList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRemitDetailList indicates an expected call of GetRemitDetailList.
func (mr *MockExchangeServerMockRecorder) GetRemitDetailList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRemitDetailList", reflect.TypeOf((*MockExchangeServer)(nil).GetRemitDetailList), ctx, in)
}

// GetRemitDetailWithIdAndMonth mocks base method.
func (m *MockExchangeServer) GetRemitDetailWithIdAndMonth(ctx context.Context, in *GetRemitDetailWithIdAndMonthReq) (*RemitDetailDataList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRemitDetailWithIdAndMonth", ctx, in)
	ret0, _ := ret[0].(*RemitDetailDataList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRemitDetailWithIdAndMonth indicates an expected call of GetRemitDetailWithIdAndMonth.
func (mr *MockExchangeServerMockRecorder) GetRemitDetailWithIdAndMonth(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRemitDetailWithIdAndMonth", reflect.TypeOf((*MockExchangeServer)(nil).GetRemitDetailWithIdAndMonth), ctx, in)
}

// GetRemitLog mocks base method.
func (m *MockExchangeServer) GetRemitLog(ctx context.Context, in *OffsetReq) (*RemitLogList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRemitLog", ctx, in)
	ret0, _ := ret[0].(*RemitLogList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRemitLog indicates an expected call of GetRemitLog.
func (mr *MockExchangeServerMockRecorder) GetRemitLog(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRemitLog", reflect.TypeOf((*MockExchangeServer)(nil).GetRemitLog), ctx, in)
}

// GetScoreCanWithdraw mocks base method.
func (m *MockExchangeServer) GetScoreCanWithdraw(ctx context.Context, in *UidReq) (*GetScoreCanWithdrawResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScoreCanWithdraw", ctx, in)
	ret0, _ := ret[0].(*GetScoreCanWithdrawResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScoreCanWithdraw indicates an expected call of GetScoreCanWithdraw.
func (mr *MockExchangeServerMockRecorder) GetScoreCanWithdraw(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScoreCanWithdraw", reflect.TypeOf((*MockExchangeServer)(nil).GetScoreCanWithdraw), ctx, in)
}

// GetScoreFreezeList mocks base method.
func (m *MockExchangeServer) GetScoreFreezeList(ctx context.Context, in *GetScoreFreezeListReq) (*GetScoreFreezeListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScoreFreezeList", ctx, in)
	ret0, _ := ret[0].(*GetScoreFreezeListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScoreFreezeList indicates an expected call of GetScoreFreezeList.
func (mr *MockExchangeServerMockRecorder) GetScoreFreezeList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScoreFreezeList", reflect.TypeOf((*MockExchangeServer)(nil).GetScoreFreezeList), ctx, in)
}

// GetScorePartFreezeHistory mocks base method.
func (m *MockExchangeServer) GetScorePartFreezeHistory(ctx context.Context, in *GetScorePartFreezeHistoryReq) (*GetScorePartFreezeHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScorePartFreezeHistory", ctx, in)
	ret0, _ := ret[0].(*GetScorePartFreezeHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScorePartFreezeHistory indicates an expected call of GetScorePartFreezeHistory.
func (mr *MockExchangeServerMockRecorder) GetScorePartFreezeHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScorePartFreezeHistory", reflect.TypeOf((*MockExchangeServer)(nil).GetScorePartFreezeHistory), ctx, in)
}

// GetScorePartFreezeList mocks base method.
func (m *MockExchangeServer) GetScorePartFreezeList(ctx context.Context, in *GetScorePartFreezeListReq) (*GetScorePartFreezeListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScorePartFreezeList", ctx, in)
	ret0, _ := ret[0].(*GetScorePartFreezeListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScorePartFreezeList indicates an expected call of GetScorePartFreezeList.
func (mr *MockExchangeServerMockRecorder) GetScorePartFreezeList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScorePartFreezeList", reflect.TypeOf((*MockExchangeServer)(nil).GetScorePartFreezeList), ctx, in)
}

// GetScoreRecycleTaskList mocks base method.
func (m *MockExchangeServer) GetScoreRecycleTaskList(ctx context.Context, in *GetScoreRecycleTaskListReq) (*GetScoreRecycleTaskListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScoreRecycleTaskList", ctx, in)
	ret0, _ := ret[0].(*GetScoreRecycleTaskListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScoreRecycleTaskList indicates an expected call of GetScoreRecycleTaskList.
func (mr *MockExchangeServerMockRecorder) GetScoreRecycleTaskList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScoreRecycleTaskList", reflect.TypeOf((*MockExchangeServer)(nil).GetScoreRecycleTaskList), ctx, in)
}

// GetSettlementHistory mocks base method.
func (m *MockExchangeServer) GetSettlementHistory(ctx context.Context, in *GetSettlementRecordReq) (*GetTransactionHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettlementHistory", ctx, in)
	ret0, _ := ret[0].(*GetTransactionHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettlementHistory indicates an expected call of GetSettlementHistory.
func (mr *MockExchangeServerMockRecorder) GetSettlementHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettlementHistory", reflect.TypeOf((*MockExchangeServer)(nil).GetSettlementHistory), ctx, in)
}

// GetTransactionHistory mocks base method.
func (m *MockExchangeServer) GetTransactionHistory(ctx context.Context, in *GetTransactionHistoryReq) (*GetTransactionHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionHistory", ctx, in)
	ret0, _ := ret[0].(*GetTransactionHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionHistory indicates an expected call of GetTransactionHistory.
func (mr *MockExchangeServerMockRecorder) GetTransactionHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionHistory", reflect.TypeOf((*MockExchangeServer)(nil).GetTransactionHistory), ctx, in)
}

// GetUserScorePop mocks base method.
func (m *MockExchangeServer) GetUserScorePop(ctx context.Context, in *GetUserScorePopReq) (*GetUserScorePopResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserScorePop", ctx, in)
	ret0, _ := ret[0].(*GetUserScorePopResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserScorePop indicates an expected call of GetUserScorePop.
func (mr *MockExchangeServerMockRecorder) GetUserScorePop(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserScorePop", reflect.TypeOf((*MockExchangeServer)(nil).GetUserScorePop), ctx, in)
}

// InsertRemitDetailData mocks base method.
func (m *MockExchangeServer) InsertRemitDetailData(ctx context.Context, in *InsertRemitDetailDataReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertRemitDetailData", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertRemitDetailData indicates an expected call of InsertRemitDetailData.
func (mr *MockExchangeServerMockRecorder) InsertRemitDetailData(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertRemitDetailData", reflect.TypeOf((*MockExchangeServer)(nil).InsertRemitDetailData), ctx, in)
}

// PrivateWithdraw mocks base method.
func (m *MockExchangeServer) PrivateWithdraw(ctx context.Context, in *PrivateWithdrawReq) (*PrivateWithdrawResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrivateWithdraw", ctx, in)
	ret0, _ := ret[0].(*PrivateWithdrawResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PrivateWithdraw indicates an expected call of PrivateWithdraw.
func (mr *MockExchangeServerMockRecorder) PrivateWithdraw(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrivateWithdraw", reflect.TypeOf((*MockExchangeServer)(nil).PrivateWithdraw), ctx, in)
}

// SetScoreCanWithdraw mocks base method.
func (m *MockExchangeServer) SetScoreCanWithdraw(ctx context.Context, in *UidReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetScoreCanWithdraw", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetScoreCanWithdraw indicates an expected call of SetScoreCanWithdraw.
func (mr *MockExchangeServerMockRecorder) SetScoreCanWithdraw(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetScoreCanWithdraw", reflect.TypeOf((*MockExchangeServer)(nil).SetScoreCanWithdraw), ctx, in)
}

// SetUserScoreNotPop mocks base method.
func (m *MockExchangeServer) SetUserScoreNotPop(ctx context.Context, in *UidReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserScoreNotPop", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserScoreNotPop indicates an expected call of SetUserScoreNotPop.
func (mr *MockExchangeServerMockRecorder) SetUserScoreNotPop(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserScoreNotPop", reflect.TypeOf((*MockExchangeServer)(nil).SetUserScoreNotPop), ctx, in)
}

// UnfreezeESportScore mocks base method.
func (m *MockExchangeServer) UnfreezeESportScore(ctx context.Context, in *UnfreezeESportScoreReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnfreezeESportScore", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnfreezeESportScore indicates an expected call of UnfreezeESportScore.
func (mr *MockExchangeServerMockRecorder) UnfreezeESportScore(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfreezeESportScore", reflect.TypeOf((*MockExchangeServer)(nil).UnfreezeESportScore), ctx, in)
}

// UnfreezePartScore mocks base method.
func (m *MockExchangeServer) UnfreezePartScore(ctx context.Context, in *UnfreezePartScoreReq) (*ErrorList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnfreezePartScore", ctx, in)
	ret0, _ := ret[0].(*ErrorList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnfreezePartScore indicates an expected call of UnfreezePartScore.
func (mr *MockExchangeServerMockRecorder) UnfreezePartScore(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfreezePartScore", reflect.TypeOf((*MockExchangeServer)(nil).UnfreezePartScore), ctx, in)
}

// UnfreezeScore mocks base method.
func (m *MockExchangeServer) UnfreezeScore(ctx context.Context, in *UnfreezeScoreReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnfreezeScore", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnfreezeScore indicates an expected call of UnfreezeScore.
func (mr *MockExchangeServerMockRecorder) UnfreezeScore(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfreezeScore", reflect.TypeOf((*MockExchangeServer)(nil).UnfreezeScore), ctx, in)
}

// UpdateBlacklist mocks base method.
func (m *MockExchangeServer) UpdateBlacklist(ctx context.Context, in *UpdateBlacklistReq) (*UpdateBlacklistResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBlacklist", ctx, in)
	ret0, _ := ret[0].(*UpdateBlacklistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBlacklist indicates an expected call of UpdateBlacklist.
func (mr *MockExchangeServerMockRecorder) UpdateBlacklist(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBlacklist", reflect.TypeOf((*MockExchangeServer)(nil).UpdateBlacklist), ctx, in)
}

// MockGuildExchangeClient is a mock of GuildExchangeClient interface.
type MockGuildExchangeClient struct {
	ctrl     *gomock.Controller
	recorder *MockGuildExchangeClientMockRecorder
}

// MockGuildExchangeClientMockRecorder is the mock recorder for MockGuildExchangeClient.
type MockGuildExchangeClientMockRecorder struct {
	mock *MockGuildExchangeClient
}

// NewMockGuildExchangeClient creates a new mock instance.
func NewMockGuildExchangeClient(ctrl *gomock.Controller) *MockGuildExchangeClient {
	mock := &MockGuildExchangeClient{ctrl: ctrl}
	mock.recorder = &MockGuildExchangeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGuildExchangeClient) EXPECT() *MockGuildExchangeClientMockRecorder {
	return m.recorder
}

// AnchorApplyPrivate mocks base method.
func (m *MockGuildExchangeClient) AnchorApplyPrivate(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AnchorApplyPrivate", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorApplyPrivate indicates an expected call of AnchorApplyPrivate.
func (mr *MockGuildExchangeClientMockRecorder) AnchorApplyPrivate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorApplyPrivate", reflect.TypeOf((*MockGuildExchangeClient)(nil).AnchorApplyPrivate), varargs...)
}

// AnchorApplyPublic mocks base method.
func (m *MockGuildExchangeClient) AnchorApplyPublic(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AnchorApplyPublic", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorApplyPublic indicates an expected call of AnchorApplyPublic.
func (mr *MockGuildExchangeClientMockRecorder) AnchorApplyPublic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorApplyPublic", reflect.TypeOf((*MockGuildExchangeClient)(nil).AnchorApplyPublic), varargs...)
}

// AnchorCheckAuth mocks base method.
func (m *MockGuildExchangeClient) AnchorCheckAuth(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*AuthRes, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AnchorCheckAuth", varargs...)
	ret0, _ := ret[0].(*AuthRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorCheckAuth indicates an expected call of AnchorCheckAuth.
func (mr *MockGuildExchangeClientMockRecorder) AnchorCheckAuth(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorCheckAuth", reflect.TypeOf((*MockGuildExchangeClient)(nil).AnchorCheckAuth), varargs...)
}

// AnchorCheckPrivate mocks base method.
func (m *MockGuildExchangeClient) AnchorCheckPrivate(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*AuthRes, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AnchorCheckPrivate", varargs...)
	ret0, _ := ret[0].(*AuthRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorCheckPrivate indicates an expected call of AnchorCheckPrivate.
func (mr *MockGuildExchangeClientMockRecorder) AnchorCheckPrivate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorCheckPrivate", reflect.TypeOf((*MockGuildExchangeClient)(nil).AnchorCheckPrivate), varargs...)
}

// AnchorGetContractList mocks base method.
func (m *MockGuildExchangeClient) AnchorGetContractList(ctx context.Context, in *UidOffsetReq, opts ...grpc.CallOption) (*ContractList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AnchorGetContractList", varargs...)
	ret0, _ := ret[0].(*ContractList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorGetContractList indicates an expected call of AnchorGetContractList.
func (mr *MockGuildExchangeClientMockRecorder) AnchorGetContractList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorGetContractList", reflect.TypeOf((*MockGuildExchangeClient)(nil).AnchorGetContractList), varargs...)
}

// AnchorGetExchangeHis mocks base method.
func (m *MockGuildExchangeClient) AnchorGetExchangeHis(ctx context.Context, in *AnchorExchangeHisReq, opts ...grpc.CallOption) (*AnchorExchangeHisRes, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AnchorGetExchangeHis", varargs...)
	ret0, _ := ret[0].(*AnchorExchangeHisRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorGetExchangeHis indicates an expected call of AnchorGetExchangeHis.
func (mr *MockGuildExchangeClientMockRecorder) AnchorGetExchangeHis(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorGetExchangeHis", reflect.TypeOf((*MockGuildExchangeClient)(nil).AnchorGetExchangeHis), varargs...)
}

// AnchorGetSignUrl mocks base method.
func (m *MockGuildExchangeClient) AnchorGetSignUrl(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*ApplyStatus, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AnchorGetSignUrl", varargs...)
	ret0, _ := ret[0].(*ApplyStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorGetSignUrl indicates an expected call of AnchorGetSignUrl.
func (mr *MockGuildExchangeClientMockRecorder) AnchorGetSignUrl(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorGetSignUrl", reflect.TypeOf((*MockGuildExchangeClient)(nil).AnchorGetSignUrl), varargs...)
}

// AnchorStartSign mocks base method.
func (m *MockGuildExchangeClient) AnchorStartSign(ctx context.Context, in *ApplyIdReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AnchorStartSign", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorStartSign indicates an expected call of AnchorStartSign.
func (mr *MockGuildExchangeClientMockRecorder) AnchorStartSign(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorStartSign", reflect.TypeOf((*MockGuildExchangeClient)(nil).AnchorStartSign), varargs...)
}

// BatchGetAnchorInfo mocks base method.
func (m *MockGuildExchangeClient) BatchGetAnchorInfo(ctx context.Context, in *UidsList, opts ...grpc.CallOption) (*AnchorInfoList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetAnchorInfo", varargs...)
	ret0, _ := ret[0].(*AnchorInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAnchorInfo indicates an expected call of BatchGetAnchorInfo.
func (mr *MockGuildExchangeClientMockRecorder) BatchGetAnchorInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorInfo", reflect.TypeOf((*MockGuildExchangeClient)(nil).BatchGetAnchorInfo), varargs...)
}

// BatchGetMasterUid mocks base method.
func (m *MockGuildExchangeClient) BatchGetMasterUid(ctx context.Context, in *BatchGetMasterUidReq, opts ...grpc.CallOption) (*BatchGetMasterUidResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetMasterUid", varargs...)
	ret0, _ := ret[0].(*BatchGetMasterUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetMasterUid indicates an expected call of BatchGetMasterUid.
func (mr *MockGuildExchangeClientMockRecorder) BatchGetMasterUid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetMasterUid", reflect.TypeOf((*MockGuildExchangeClient)(nil).BatchGetMasterUid), varargs...)
}

// BindMainAccountId mocks base method.
func (m *MockGuildExchangeClient) BindMainAccountId(ctx context.Context, in *BindOaReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BindMainAccountId", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BindMainAccountId indicates an expected call of BindMainAccountId.
func (mr *MockGuildExchangeClientMockRecorder) BindMainAccountId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindMainAccountId", reflect.TypeOf((*MockGuildExchangeClient)(nil).BindMainAccountId), varargs...)
}

// CheckMismatchSign mocks base method.
func (m *MockGuildExchangeClient) CheckMismatchSign(ctx context.Context, in *EmptyMsg, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckMismatchSign", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckMismatchSign indicates an expected call of CheckMismatchSign.
func (mr *MockGuildExchangeClientMockRecorder) CheckMismatchSign(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckMismatchSign", reflect.TypeOf((*MockGuildExchangeClient)(nil).CheckMismatchSign), varargs...)
}

// CheckSumAndSettlementStatus mocks base method.
func (m *MockGuildExchangeClient) CheckSumAndSettlementStatus(ctx context.Context, in *ManualSettlementReq, opts ...grpc.CallOption) (*CheckSumAndSettlementStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckSumAndSettlementStatus", varargs...)
	ret0, _ := ret[0].(*CheckSumAndSettlementStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckSumAndSettlementStatus indicates an expected call of CheckSumAndSettlementStatus.
func (mr *MockGuildExchangeClientMockRecorder) CheckSumAndSettlementStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckSumAndSettlementStatus", reflect.TypeOf((*MockGuildExchangeClient)(nil).CheckSumAndSettlementStatus), varargs...)
}

// DelMain mocks base method.
func (m *MockGuildExchangeClient) DelMain(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelMain", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelMain indicates an expected call of DelMain.
func (mr *MockGuildExchangeClientMockRecorder) DelMain(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMain", reflect.TypeOf((*MockGuildExchangeClient)(nil).DelMain), varargs...)
}

// DelMainPlaceCount mocks base method.
func (m *MockGuildExchangeClient) DelMainPlaceCount(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelMainPlaceCount", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelMainPlaceCount indicates an expected call of DelMainPlaceCount.
func (mr *MockGuildExchangeClientMockRecorder) DelMainPlaceCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMainPlaceCount", reflect.TypeOf((*MockGuildExchangeClient)(nil).DelMainPlaceCount), varargs...)
}

// DelTransWhiteList mocks base method.
func (m *MockGuildExchangeClient) DelTransWhiteList(ctx context.Context, in *WhiteList, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelTransWhiteList", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelTransWhiteList indicates an expected call of DelTransWhiteList.
func (mr *MockGuildExchangeClientMockRecorder) DelTransWhiteList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelTransWhiteList", reflect.TypeOf((*MockGuildExchangeClient)(nil).DelTransWhiteList), varargs...)
}

// ExchangeV2 mocks base method.
func (m *MockGuildExchangeClient) ExchangeV2(ctx context.Context, in *GuildExchangeTypeReq, opts ...grpc.CallOption) (*AllScore, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExchangeV2", varargs...)
	ret0, _ := ret[0].(*AllScore)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExchangeV2 indicates an expected call of ExchangeV2.
func (mr *MockGuildExchangeClientMockRecorder) ExchangeV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExchangeV2", reflect.TypeOf((*MockGuildExchangeClient)(nil).ExchangeV2), varargs...)
}

// GetAnchorInfo mocks base method.
func (m *MockGuildExchangeClient) GetAnchorInfo(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*AnchorInfo, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorInfo", varargs...)
	ret0, _ := ret[0].(*AnchorInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorInfo indicates an expected call of GetAnchorInfo.
func (mr *MockGuildExchangeClientMockRecorder) GetAnchorInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorInfo", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetAnchorInfo), varargs...)
}

// GetApplyStatus mocks base method.
func (m *MockGuildExchangeClient) GetApplyStatus(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*ApplyStatus, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetApplyStatus", varargs...)
	ret0, _ := ret[0].(*ApplyStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplyStatus indicates an expected call of GetApplyStatus.
func (mr *MockGuildExchangeClientMockRecorder) GetApplyStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplyStatus", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetApplyStatus), varargs...)
}

// GetExchangeAmount mocks base method.
func (m *MockGuildExchangeClient) GetExchangeAmount(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*UserAllScoreResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangeAmount", varargs...)
	ret0, _ := ret[0].(*UserAllScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangeAmount indicates an expected call of GetExchangeAmount.
func (mr *MockGuildExchangeClientMockRecorder) GetExchangeAmount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangeAmount", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetExchangeAmount), varargs...)
}

// GetExchangeAmountList mocks base method.
func (m *MockGuildExchangeClient) GetExchangeAmountList(ctx context.Context, in *UidOffsetReq, opts ...grpc.CallOption) (*UserAllScoreList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangeAmountList", varargs...)
	ret0, _ := ret[0].(*UserAllScoreList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangeAmountList indicates an expected call of GetExchangeAmountList.
func (mr *MockGuildExchangeClientMockRecorder) GetExchangeAmountList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangeAmountList", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetExchangeAmountList), varargs...)
}

// GetExchangeAmountListType mocks base method.
func (m *MockGuildExchangeClient) GetExchangeAmountListType(ctx context.Context, in *UidOffsetTypeReq, opts ...grpc.CallOption) (*UserAllScoreList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangeAmountListType", varargs...)
	ret0, _ := ret[0].(*UserAllScoreList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangeAmountListType indicates an expected call of GetExchangeAmountListType.
func (mr *MockGuildExchangeClientMockRecorder) GetExchangeAmountListType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangeAmountListType", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetExchangeAmountListType), varargs...)
}

// GetExchangeDetail mocks base method.
func (m *MockGuildExchangeClient) GetExchangeDetail(ctx context.Context, in *GetExchangeDetailReq, opts ...grpc.CallOption) (*GetExchangeListRes, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangeDetail", varargs...)
	ret0, _ := ret[0].(*GetExchangeListRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangeDetail indicates an expected call of GetExchangeDetail.
func (mr *MockGuildExchangeClientMockRecorder) GetExchangeDetail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangeDetail", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetExchangeDetail), varargs...)
}

// GetExchangeHis mocks base method.
func (m *MockGuildExchangeClient) GetExchangeHis(ctx context.Context, in *GetExchangeListReq, opts ...grpc.CallOption) (*GetExchangeListRes, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangeHis", varargs...)
	ret0, _ := ret[0].(*GetExchangeListRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangeHis indicates an expected call of GetExchangeHis.
func (mr *MockGuildExchangeClientMockRecorder) GetExchangeHis(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangeHis", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetExchangeHis), varargs...)
}

// GetMainAccountInfoList mocks base method.
func (m *MockGuildExchangeClient) GetMainAccountInfoList(ctx context.Context, in *OaInfoReq, opts ...grpc.CallOption) (*OaInfoList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMainAccountInfoList", varargs...)
	ret0, _ := ret[0].(*OaInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainAccountInfoList indicates an expected call of GetMainAccountInfoList.
func (mr *MockGuildExchangeClientMockRecorder) GetMainAccountInfoList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainAccountInfoList", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetMainAccountInfoList), varargs...)
}

// GetMainApplyList mocks base method.
func (m *MockGuildExchangeClient) GetMainApplyList(ctx context.Context, in *GetMainApplyListReq, opts ...grpc.CallOption) (*GetMainApplyListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMainApplyList", varargs...)
	ret0, _ := ret[0].(*GetMainApplyListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainApplyList indicates an expected call of GetMainApplyList.
func (mr *MockGuildExchangeClientMockRecorder) GetMainApplyList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainApplyList", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetMainApplyList), varargs...)
}

// GetMainData mocks base method.
func (m *MockGuildExchangeClient) GetMainData(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*GetMain, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMainData", varargs...)
	ret0, _ := ret[0].(*GetMain)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainData indicates an expected call of GetMainData.
func (mr *MockGuildExchangeClientMockRecorder) GetMainData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainData", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetMainData), varargs...)
}

// GetMainGuilds mocks base method.
func (m *MockGuildExchangeClient) GetMainGuilds(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*GuildList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMainGuilds", varargs...)
	ret0, _ := ret[0].(*GuildList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainGuilds indicates an expected call of GetMainGuilds.
func (mr *MockGuildExchangeClientMockRecorder) GetMainGuilds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainGuilds", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetMainGuilds), varargs...)
}

// GetMainHasPlaceCount mocks base method.
func (m *MockGuildExchangeClient) GetMainHasPlaceCount(ctx context.Context, in *UidOffsetReq, opts ...grpc.CallOption) (*GetMainListRes, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMainHasPlaceCount", varargs...)
	ret0, _ := ret[0].(*GetMainListRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainHasPlaceCount indicates an expected call of GetMainHasPlaceCount.
func (mr *MockGuildExchangeClientMockRecorder) GetMainHasPlaceCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainHasPlaceCount", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetMainHasPlaceCount), varargs...)
}

// GetMainList mocks base method.
func (m *MockGuildExchangeClient) GetMainList(ctx context.Context, in *UidOffsetReq, opts ...grpc.CallOption) (*GetMainListRes, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMainList", varargs...)
	ret0, _ := ret[0].(*GetMainListRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainList indicates an expected call of GetMainList.
func (mr *MockGuildExchangeClientMockRecorder) GetMainList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainList", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetMainList), varargs...)
}

// GetMainOnly mocks base method.
func (m *MockGuildExchangeClient) GetMainOnly(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*GetMain, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMainOnly", varargs...)
	ret0, _ := ret[0].(*GetMain)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainOnly indicates an expected call of GetMainOnly.
func (mr *MockGuildExchangeClientMockRecorder) GetMainOnly(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainOnly", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetMainOnly), varargs...)
}

// GetMainPlaceCount mocks base method.
func (m *MockGuildExchangeClient) GetMainPlaceCount(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*GetMain, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMainPlaceCount", varargs...)
	ret0, _ := ret[0].(*GetMain)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainPlaceCount indicates an expected call of GetMainPlaceCount.
func (mr *MockGuildExchangeClientMockRecorder) GetMainPlaceCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainPlaceCount", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetMainPlaceCount), varargs...)
}

// GetMasterApplyCount mocks base method.
func (m *MockGuildExchangeClient) GetMasterApplyCount(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*ApplyCount, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMasterApplyCount", varargs...)
	ret0, _ := ret[0].(*ApplyCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterApplyCount indicates an expected call of GetMasterApplyCount.
func (mr *MockGuildExchangeClientMockRecorder) GetMasterApplyCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterApplyCount", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetMasterApplyCount), varargs...)
}

// GetMasterApplyList mocks base method.
func (m *MockGuildExchangeClient) GetMasterApplyList(ctx context.Context, in *ApplyListReq, opts ...grpc.CallOption) (*AnchorApplyList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMasterApplyList", varargs...)
	ret0, _ := ret[0].(*AnchorApplyList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterApplyList indicates an expected call of GetMasterApplyList.
func (mr *MockGuildExchangeClientMockRecorder) GetMasterApplyList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterApplyList", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetMasterApplyList), varargs...)
}

// GetMasterFreezeTotal mocks base method.
func (m *MockGuildExchangeClient) GetMasterFreezeTotal(ctx context.Context, in *ManualSettlementReq, opts ...grpc.CallOption) (*GetMasterFreezeTotalResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMasterFreezeTotal", varargs...)
	ret0, _ := ret[0].(*GetMasterFreezeTotalResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterFreezeTotal indicates an expected call of GetMasterFreezeTotal.
func (mr *MockGuildExchangeClientMockRecorder) GetMasterFreezeTotal(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterFreezeTotal", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetMasterFreezeTotal), varargs...)
}

// GetMasterFreezeUser mocks base method.
func (m *MockGuildExchangeClient) GetMasterFreezeUser(ctx context.Context, in *ManualSettlementReq, opts ...grpc.CallOption) (*GetMasterFreezeUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMasterFreezeUser", varargs...)
	ret0, _ := ret[0].(*GetMasterFreezeUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterFreezeUser indicates an expected call of GetMasterFreezeUser.
func (mr *MockGuildExchangeClientMockRecorder) GetMasterFreezeUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterFreezeUser", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetMasterFreezeUser), varargs...)
}

// GetMasterUid mocks base method.
func (m *MockGuildExchangeClient) GetMasterUid(ctx context.Context, in *GuildReq, opts ...grpc.CallOption) (*UidRes, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMasterUid", varargs...)
	ret0, _ := ret[0].(*UidRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterUid indicates an expected call of GetMasterUid.
func (mr *MockGuildExchangeClientMockRecorder) GetMasterUid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterUid", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetMasterUid), varargs...)
}

// GetSettlementAttribute mocks base method.
func (m *MockGuildExchangeClient) GetSettlementAttribute(ctx context.Context, in *UidTimeRangeReq, opts ...grpc.CallOption) (*UserAllScoreList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSettlementAttribute", varargs...)
	ret0, _ := ret[0].(*UserAllScoreList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettlementAttribute indicates an expected call of GetSettlementAttribute.
func (mr *MockGuildExchangeClientMockRecorder) GetSettlementAttribute(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettlementAttribute", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetSettlementAttribute), varargs...)
}

// GetSettlementBalance mocks base method.
func (m *MockGuildExchangeClient) GetSettlementBalance(ctx context.Context, in *GuildExchangeTypeReq, opts ...grpc.CallOption) (*Balance, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSettlementBalance", varargs...)
	ret0, _ := ret[0].(*Balance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettlementBalance indicates an expected call of GetSettlementBalance.
func (mr *MockGuildExchangeClientMockRecorder) GetSettlementBalance(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettlementBalance", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetSettlementBalance), varargs...)
}

// GetUidGuildExchangeData mocks base method.
func (m *MockGuildExchangeClient) GetUidGuildExchangeData(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*UserGuildExchangeData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUidGuildExchangeData", varargs...)
	ret0, _ := ret[0].(*UserGuildExchangeData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUidGuildExchangeData indicates an expected call of GetUidGuildExchangeData.
func (mr *MockGuildExchangeClientMockRecorder) GetUidGuildExchangeData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUidGuildExchangeData", reflect.TypeOf((*MockGuildExchangeClient)(nil).GetUidGuildExchangeData), varargs...)
}

// HandleMainApply mocks base method.
func (m *MockGuildExchangeClient) HandleMainApply(ctx context.Context, in *HandleMainApplyReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleMainApply", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleMainApply indicates an expected call of HandleMainApply.
func (mr *MockGuildExchangeClientMockRecorder) HandleMainApply(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleMainApply", reflect.TypeOf((*MockGuildExchangeClient)(nil).HandleMainApply), varargs...)
}

// InsertMain mocks base method.
func (m *MockGuildExchangeClient) InsertMain(ctx context.Context, in *SetGuildMainListReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InsertMain", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertMain indicates an expected call of InsertMain.
func (mr *MockGuildExchangeClientMockRecorder) InsertMain(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertMain", reflect.TypeOf((*MockGuildExchangeClient)(nil).InsertMain), varargs...)
}

// ManualSettlement mocks base method.
func (m *MockGuildExchangeClient) ManualSettlement(ctx context.Context, in *ManualSettlementReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ManualSettlement", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ManualSettlement indicates an expected call of ManualSettlement.
func (mr *MockGuildExchangeClientMockRecorder) ManualSettlement(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ManualSettlement", reflect.TypeOf((*MockGuildExchangeClient)(nil).ManualSettlement), varargs...)
}

// ManualSum mocks base method.
func (m *MockGuildExchangeClient) ManualSum(ctx context.Context, in *ManualSumReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ManualSum", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ManualSum indicates an expected call of ManualSum.
func (mr *MockGuildExchangeClientMockRecorder) ManualSum(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ManualSum", reflect.TypeOf((*MockGuildExchangeClient)(nil).ManualSum), varargs...)
}

// MasterBatchExaApply mocks base method.
func (m *MockGuildExchangeClient) MasterBatchExaApply(ctx context.Context, in *MasterBatchExaApplyReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MasterBatchExaApply", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MasterBatchExaApply indicates an expected call of MasterBatchExaApply.
func (mr *MockGuildExchangeClientMockRecorder) MasterBatchExaApply(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterBatchExaApply", reflect.TypeOf((*MockGuildExchangeClient)(nil).MasterBatchExaApply), varargs...)
}

// MasterGetContractFinishedList mocks base method.
func (m *MockGuildExchangeClient) MasterGetContractFinishedList(ctx context.Context, in *OffsetValidReq, opts ...grpc.CallOption) (*ContractList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MasterGetContractFinishedList", varargs...)
	ret0, _ := ret[0].(*ContractList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MasterGetContractFinishedList indicates an expected call of MasterGetContractFinishedList.
func (mr *MockGuildExchangeClientMockRecorder) MasterGetContractFinishedList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterGetContractFinishedList", reflect.TypeOf((*MockGuildExchangeClient)(nil).MasterGetContractFinishedList), varargs...)
}

// MasterGetContractList mocks base method.
func (m *MockGuildExchangeClient) MasterGetContractList(ctx context.Context, in *UidOffsetReq, opts ...grpc.CallOption) (*AnchorApplyList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MasterGetContractList", varargs...)
	ret0, _ := ret[0].(*AnchorApplyList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MasterGetContractList indicates an expected call of MasterGetContractList.
func (mr *MockGuildExchangeClientMockRecorder) MasterGetContractList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterGetContractList", reflect.TypeOf((*MockGuildExchangeClient)(nil).MasterGetContractList), varargs...)
}

// MasterGetMainApplyList mocks base method.
func (m *MockGuildExchangeClient) MasterGetMainApplyList(ctx context.Context, in *MasterGetMainApplyListReq, opts ...grpc.CallOption) (*MasterGetMainApplyListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MasterGetMainApplyList", varargs...)
	ret0, _ := ret[0].(*MasterGetMainApplyListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MasterGetMainApplyList indicates an expected call of MasterGetMainApplyList.
func (mr *MockGuildExchangeClientMockRecorder) MasterGetMainApplyList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterGetMainApplyList", reflect.TypeOf((*MockGuildExchangeClient)(nil).MasterGetMainApplyList), varargs...)
}

// MasterGetSignUrl mocks base method.
func (m *MockGuildExchangeClient) MasterGetSignUrl(ctx context.Context, in *ApplyIdReq, opts ...grpc.CallOption) (*SignUrl, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MasterGetSignUrl", varargs...)
	ret0, _ := ret[0].(*SignUrl)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MasterGetSignUrl indicates an expected call of MasterGetSignUrl.
func (mr *MockGuildExchangeClientMockRecorder) MasterGetSignUrl(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterGetSignUrl", reflect.TypeOf((*MockGuildExchangeClient)(nil).MasterGetSignUrl), varargs...)
}

// MasterInsertMainApply mocks base method.
func (m *MockGuildExchangeClient) MasterInsertMainApply(ctx context.Context, in *ExchangeGuildMainApplyData, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MasterInsertMainApply", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MasterInsertMainApply indicates an expected call of MasterInsertMainApply.
func (mr *MockGuildExchangeClientMockRecorder) MasterInsertMainApply(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterInsertMainApply", reflect.TypeOf((*MockGuildExchangeClient)(nil).MasterInsertMainApply), varargs...)
}

// MasterStartSign mocks base method.
func (m *MockGuildExchangeClient) MasterStartSign(ctx context.Context, in *ApplyIdReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MasterStartSign", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MasterStartSign indicates an expected call of MasterStartSign.
func (mr *MockGuildExchangeClientMockRecorder) MasterStartSign(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterStartSign", reflect.TypeOf((*MockGuildExchangeClient)(nil).MasterStartSign), varargs...)
}

// OfficalBatchExaToPrivate mocks base method.
func (m *MockGuildExchangeClient) OfficalBatchExaToPrivate(ctx context.Context, in *BatchExaApplyReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OfficalBatchExaToPrivate", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficalBatchExaToPrivate indicates an expected call of OfficalBatchExaToPrivate.
func (mr *MockGuildExchangeClientMockRecorder) OfficalBatchExaToPrivate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficalBatchExaToPrivate", reflect.TypeOf((*MockGuildExchangeClient)(nil).OfficalBatchExaToPrivate), varargs...)
}

// OfficalGetMainContractData mocks base method.
func (m *MockGuildExchangeClient) OfficalGetMainContractData(ctx context.Context, in *OffsetValidReq, opts ...grpc.CallOption) (*ContractListT, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OfficalGetMainContractData", varargs...)
	ret0, _ := ret[0].(*ContractListT)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficalGetMainContractData indicates an expected call of OfficalGetMainContractData.
func (mr *MockGuildExchangeClientMockRecorder) OfficalGetMainContractData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficalGetMainContractData", reflect.TypeOf((*MockGuildExchangeClient)(nil).OfficalGetMainContractData), varargs...)
}

// OfficalGetMainContractList mocks base method.
func (m *MockGuildExchangeClient) OfficalGetMainContractList(ctx context.Context, in *UidOffsetReq, opts ...grpc.CallOption) (*MainContractList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OfficalGetMainContractList", varargs...)
	ret0, _ := ret[0].(*MainContractList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficalGetMainContractList indicates an expected call of OfficalGetMainContractList.
func (mr *MockGuildExchangeClientMockRecorder) OfficalGetMainContractList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficalGetMainContractList", reflect.TypeOf((*MockGuildExchangeClient)(nil).OfficalGetMainContractList), varargs...)
}

// OfficalGetToPrivateList mocks base method.
func (m *MockGuildExchangeClient) OfficalGetToPrivateList(ctx context.Context, in *UidOffsetHandleReq, opts ...grpc.CallOption) (*AnchorApplyListT, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OfficalGetToPrivateList", varargs...)
	ret0, _ := ret[0].(*AnchorApplyListT)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficalGetToPrivateList indicates an expected call of OfficalGetToPrivateList.
func (mr *MockGuildExchangeClientMockRecorder) OfficalGetToPrivateList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficalGetToPrivateList", reflect.TypeOf((*MockGuildExchangeClient)(nil).OfficalGetToPrivateList), varargs...)
}

// OnSignDocExpire mocks base method.
func (m *MockGuildExchangeClient) OnSignDocExpire(ctx context.Context, in *OnSignFlowExpireReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OnSignDocExpire", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnSignDocExpire indicates an expected call of OnSignDocExpire.
func (mr *MockGuildExchangeClientMockRecorder) OnSignDocExpire(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSignDocExpire", reflect.TypeOf((*MockGuildExchangeClient)(nil).OnSignDocExpire), varargs...)
}

// OnSignFlowFinish mocks base method.
func (m *MockGuildExchangeClient) OnSignFlowFinish(ctx context.Context, in *OnSignFlowFinishReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OnSignFlowFinish", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnSignFlowFinish indicates an expected call of OnSignFlowFinish.
func (mr *MockGuildExchangeClientMockRecorder) OnSignFlowFinish(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSignFlowFinish", reflect.TypeOf((*MockGuildExchangeClient)(nil).OnSignFlowFinish), varargs...)
}

// OnSignFlowUpdate mocks base method.
func (m *MockGuildExchangeClient) OnSignFlowUpdate(ctx context.Context, in *OnSignFlowUpdateReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OnSignFlowUpdate", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnSignFlowUpdate indicates an expected call of OnSignFlowUpdate.
func (mr *MockGuildExchangeClientMockRecorder) OnSignFlowUpdate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSignFlowUpdate", reflect.TypeOf((*MockGuildExchangeClient)(nil).OnSignFlowUpdate), varargs...)
}

// OnSignMasterDocExpire mocks base method.
func (m *MockGuildExchangeClient) OnSignMasterDocExpire(ctx context.Context, in *OnSignFlowExpireReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OnSignMasterDocExpire", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnSignMasterDocExpire indicates an expected call of OnSignMasterDocExpire.
func (mr *MockGuildExchangeClientMockRecorder) OnSignMasterDocExpire(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSignMasterDocExpire", reflect.TypeOf((*MockGuildExchangeClient)(nil).OnSignMasterDocExpire), varargs...)
}

// OnSignMasterFlowFinish mocks base method.
func (m *MockGuildExchangeClient) OnSignMasterFlowFinish(ctx context.Context, in *OnSignFlowFinishReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OnSignMasterFlowFinish", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnSignMasterFlowFinish indicates an expected call of OnSignMasterFlowFinish.
func (mr *MockGuildExchangeClientMockRecorder) OnSignMasterFlowFinish(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSignMasterFlowFinish", reflect.TypeOf((*MockGuildExchangeClient)(nil).OnSignMasterFlowFinish), varargs...)
}

// OnSignMasterFlowUpdate mocks base method.
func (m *MockGuildExchangeClient) OnSignMasterFlowUpdate(ctx context.Context, in *OnSignFlowUpdateReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OnSignMasterFlowUpdate", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnSignMasterFlowUpdate indicates an expected call of OnSignMasterFlowUpdate.
func (mr *MockGuildExchangeClientMockRecorder) OnSignMasterFlowUpdate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSignMasterFlowUpdate", reflect.TypeOf((*MockGuildExchangeClient)(nil).OnSignMasterFlowUpdate), varargs...)
}

// QueryEncashmentList mocks base method.
func (m *MockGuildExchangeClient) QueryEncashmentList(ctx context.Context, in *QueryEncashmentListReq, opts ...grpc.CallOption) (*QueryEncashmentListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryEncashmentList", varargs...)
	ret0, _ := ret[0].(*QueryEncashmentListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryEncashmentList indicates an expected call of QueryEncashmentList.
func (mr *MockGuildExchangeClientMockRecorder) QueryEncashmentList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryEncashmentList", reflect.TypeOf((*MockGuildExchangeClient)(nil).QueryEncashmentList), varargs...)
}

// QueryUidAutoExchangeScore mocks base method.
func (m *MockGuildExchangeClient) QueryUidAutoExchangeScore(ctx context.Context, in *QueryUidAutoExchangeScoreReq, opts ...grpc.CallOption) (*QueryUidAutoExchangeScoreResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryUidAutoExchangeScore", varargs...)
	ret0, _ := ret[0].(*QueryUidAutoExchangeScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUidAutoExchangeScore indicates an expected call of QueryUidAutoExchangeScore.
func (mr *MockGuildExchangeClientMockRecorder) QueryUidAutoExchangeScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUidAutoExchangeScore", reflect.TypeOf((*MockGuildExchangeClient)(nil).QueryUidAutoExchangeScore), varargs...)
}

// RecycleGuildExchange mocks base method.
func (m *MockGuildExchangeClient) RecycleGuildExchange(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecycleGuildExchange", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecycleGuildExchange indicates an expected call of RecycleGuildExchange.
func (mr *MockGuildExchangeClientMockRecorder) RecycleGuildExchange(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecycleGuildExchange", reflect.TypeOf((*MockGuildExchangeClient)(nil).RecycleGuildExchange), varargs...)
}

// SearchApplyListTTid mocks base method.
func (m *MockGuildExchangeClient) SearchApplyListTTid(ctx context.Context, in *SearchApplyListTTidReq, opts ...grpc.CallOption) (*AnchorApplyData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchApplyListTTid", varargs...)
	ret0, _ := ret[0].(*AnchorApplyData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchApplyListTTid indicates an expected call of SearchApplyListTTid.
func (mr *MockGuildExchangeClientMockRecorder) SearchApplyListTTid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchApplyListTTid", reflect.TypeOf((*MockGuildExchangeClient)(nil).SearchApplyListTTid), varargs...)
}

// SetMainGuild mocks base method.
func (m *MockGuildExchangeClient) SetMainGuild(ctx context.Context, in *MainGuild, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetMainGuild", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetMainGuild indicates an expected call of SetMainGuild.
func (mr *MockGuildExchangeClientMockRecorder) SetMainGuild(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMainGuild", reflect.TypeOf((*MockGuildExchangeClient)(nil).SetMainGuild), varargs...)
}

// SetMainPlaceCount mocks base method.
func (m *MockGuildExchangeClient) SetMainPlaceCount(ctx context.Context, in *SetMainPlaceCountReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetMainPlaceCount", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetMainPlaceCount indicates an expected call of SetMainPlaceCount.
func (mr *MockGuildExchangeClientMockRecorder) SetMainPlaceCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMainPlaceCount", reflect.TypeOf((*MockGuildExchangeClient)(nil).SetMainPlaceCount), varargs...)
}

// SetTransWhiteList mocks base method.
func (m *MockGuildExchangeClient) SetTransWhiteList(ctx context.Context, in *WhiteList, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetTransWhiteList", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetTransWhiteList indicates an expected call of SetTransWhiteList.
func (mr *MockGuildExchangeClientMockRecorder) SetTransWhiteList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTransWhiteList", reflect.TypeOf((*MockGuildExchangeClient)(nil).SetTransWhiteList), varargs...)
}

// SumAndSettlement mocks base method.
func (m *MockGuildExchangeClient) SumAndSettlement(ctx context.Context, in *ManualSettlementReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SumAndSettlement", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SumAndSettlement indicates an expected call of SumAndSettlement.
func (mr *MockGuildExchangeClientMockRecorder) SumAndSettlement(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SumAndSettlement", reflect.TypeOf((*MockGuildExchangeClient)(nil).SumAndSettlement), varargs...)
}

// UpdateMain mocks base method.
func (m *MockGuildExchangeClient) UpdateMain(ctx context.Context, in *SetGuildMainListReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateMain", varargs...)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateMain indicates an expected call of UpdateMain.
func (mr *MockGuildExchangeClientMockRecorder) UpdateMain(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMain", reflect.TypeOf((*MockGuildExchangeClient)(nil).UpdateMain), varargs...)
}

// MockGuildExchangeServer is a mock of GuildExchangeServer interface.
type MockGuildExchangeServer struct {
	ctrl     *gomock.Controller
	recorder *MockGuildExchangeServerMockRecorder
}

// MockGuildExchangeServerMockRecorder is the mock recorder for MockGuildExchangeServer.
type MockGuildExchangeServerMockRecorder struct {
	mock *MockGuildExchangeServer
}

// NewMockGuildExchangeServer creates a new mock instance.
func NewMockGuildExchangeServer(ctrl *gomock.Controller) *MockGuildExchangeServer {
	mock := &MockGuildExchangeServer{ctrl: ctrl}
	mock.recorder = &MockGuildExchangeServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGuildExchangeServer) EXPECT() *MockGuildExchangeServerMockRecorder {
	return m.recorder
}

// AnchorApplyPrivate mocks base method.
func (m *MockGuildExchangeServer) AnchorApplyPrivate(ctx context.Context, in *UidReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnchorApplyPrivate", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorApplyPrivate indicates an expected call of AnchorApplyPrivate.
func (mr *MockGuildExchangeServerMockRecorder) AnchorApplyPrivate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorApplyPrivate", reflect.TypeOf((*MockGuildExchangeServer)(nil).AnchorApplyPrivate), ctx, in)
}

// AnchorApplyPublic mocks base method.
func (m *MockGuildExchangeServer) AnchorApplyPublic(ctx context.Context, in *UidReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnchorApplyPublic", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorApplyPublic indicates an expected call of AnchorApplyPublic.
func (mr *MockGuildExchangeServerMockRecorder) AnchorApplyPublic(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorApplyPublic", reflect.TypeOf((*MockGuildExchangeServer)(nil).AnchorApplyPublic), ctx, in)
}

// AnchorCheckAuth mocks base method.
func (m *MockGuildExchangeServer) AnchorCheckAuth(ctx context.Context, in *UidReq) (*AuthRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnchorCheckAuth", ctx, in)
	ret0, _ := ret[0].(*AuthRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorCheckAuth indicates an expected call of AnchorCheckAuth.
func (mr *MockGuildExchangeServerMockRecorder) AnchorCheckAuth(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorCheckAuth", reflect.TypeOf((*MockGuildExchangeServer)(nil).AnchorCheckAuth), ctx, in)
}

// AnchorCheckPrivate mocks base method.
func (m *MockGuildExchangeServer) AnchorCheckPrivate(ctx context.Context, in *UidReq) (*AuthRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnchorCheckPrivate", ctx, in)
	ret0, _ := ret[0].(*AuthRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorCheckPrivate indicates an expected call of AnchorCheckPrivate.
func (mr *MockGuildExchangeServerMockRecorder) AnchorCheckPrivate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorCheckPrivate", reflect.TypeOf((*MockGuildExchangeServer)(nil).AnchorCheckPrivate), ctx, in)
}

// AnchorGetContractList mocks base method.
func (m *MockGuildExchangeServer) AnchorGetContractList(ctx context.Context, in *UidOffsetReq) (*ContractList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnchorGetContractList", ctx, in)
	ret0, _ := ret[0].(*ContractList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorGetContractList indicates an expected call of AnchorGetContractList.
func (mr *MockGuildExchangeServerMockRecorder) AnchorGetContractList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorGetContractList", reflect.TypeOf((*MockGuildExchangeServer)(nil).AnchorGetContractList), ctx, in)
}

// AnchorGetExchangeHis mocks base method.
func (m *MockGuildExchangeServer) AnchorGetExchangeHis(ctx context.Context, in *AnchorExchangeHisReq) (*AnchorExchangeHisRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnchorGetExchangeHis", ctx, in)
	ret0, _ := ret[0].(*AnchorExchangeHisRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorGetExchangeHis indicates an expected call of AnchorGetExchangeHis.
func (mr *MockGuildExchangeServerMockRecorder) AnchorGetExchangeHis(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorGetExchangeHis", reflect.TypeOf((*MockGuildExchangeServer)(nil).AnchorGetExchangeHis), ctx, in)
}

// AnchorGetSignUrl mocks base method.
func (m *MockGuildExchangeServer) AnchorGetSignUrl(ctx context.Context, in *UidReq) (*ApplyStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnchorGetSignUrl", ctx, in)
	ret0, _ := ret[0].(*ApplyStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorGetSignUrl indicates an expected call of AnchorGetSignUrl.
func (mr *MockGuildExchangeServerMockRecorder) AnchorGetSignUrl(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorGetSignUrl", reflect.TypeOf((*MockGuildExchangeServer)(nil).AnchorGetSignUrl), ctx, in)
}

// AnchorStartSign mocks base method.
func (m *MockGuildExchangeServer) AnchorStartSign(ctx context.Context, in *ApplyIdReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnchorStartSign", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnchorStartSign indicates an expected call of AnchorStartSign.
func (mr *MockGuildExchangeServerMockRecorder) AnchorStartSign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnchorStartSign", reflect.TypeOf((*MockGuildExchangeServer)(nil).AnchorStartSign), ctx, in)
}

// BatchGetAnchorInfo mocks base method.
func (m *MockGuildExchangeServer) BatchGetAnchorInfo(ctx context.Context, in *UidsList) (*AnchorInfoList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorInfo", ctx, in)
	ret0, _ := ret[0].(*AnchorInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAnchorInfo indicates an expected call of BatchGetAnchorInfo.
func (mr *MockGuildExchangeServerMockRecorder) BatchGetAnchorInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorInfo", reflect.TypeOf((*MockGuildExchangeServer)(nil).BatchGetAnchorInfo), ctx, in)
}

// BatchGetMasterUid mocks base method.
func (m *MockGuildExchangeServer) BatchGetMasterUid(ctx context.Context, in *BatchGetMasterUidReq) (*BatchGetMasterUidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetMasterUid", ctx, in)
	ret0, _ := ret[0].(*BatchGetMasterUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetMasterUid indicates an expected call of BatchGetMasterUid.
func (mr *MockGuildExchangeServerMockRecorder) BatchGetMasterUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetMasterUid", reflect.TypeOf((*MockGuildExchangeServer)(nil).BatchGetMasterUid), ctx, in)
}

// BindMainAccountId mocks base method.
func (m *MockGuildExchangeServer) BindMainAccountId(ctx context.Context, in *BindOaReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindMainAccountId", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BindMainAccountId indicates an expected call of BindMainAccountId.
func (mr *MockGuildExchangeServerMockRecorder) BindMainAccountId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindMainAccountId", reflect.TypeOf((*MockGuildExchangeServer)(nil).BindMainAccountId), ctx, in)
}

// CheckMismatchSign mocks base method.
func (m *MockGuildExchangeServer) CheckMismatchSign(ctx context.Context, in *EmptyMsg) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckMismatchSign", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckMismatchSign indicates an expected call of CheckMismatchSign.
func (mr *MockGuildExchangeServerMockRecorder) CheckMismatchSign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckMismatchSign", reflect.TypeOf((*MockGuildExchangeServer)(nil).CheckMismatchSign), ctx, in)
}

// CheckSumAndSettlementStatus mocks base method.
func (m *MockGuildExchangeServer) CheckSumAndSettlementStatus(ctx context.Context, in *ManualSettlementReq) (*CheckSumAndSettlementStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckSumAndSettlementStatus", ctx, in)
	ret0, _ := ret[0].(*CheckSumAndSettlementStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckSumAndSettlementStatus indicates an expected call of CheckSumAndSettlementStatus.
func (mr *MockGuildExchangeServerMockRecorder) CheckSumAndSettlementStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckSumAndSettlementStatus", reflect.TypeOf((*MockGuildExchangeServer)(nil).CheckSumAndSettlementStatus), ctx, in)
}

// DelMain mocks base method.
func (m *MockGuildExchangeServer) DelMain(ctx context.Context, in *UidReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMain", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelMain indicates an expected call of DelMain.
func (mr *MockGuildExchangeServerMockRecorder) DelMain(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMain", reflect.TypeOf((*MockGuildExchangeServer)(nil).DelMain), ctx, in)
}

// DelMainPlaceCount mocks base method.
func (m *MockGuildExchangeServer) DelMainPlaceCount(ctx context.Context, in *UidReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMainPlaceCount", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelMainPlaceCount indicates an expected call of DelMainPlaceCount.
func (mr *MockGuildExchangeServerMockRecorder) DelMainPlaceCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMainPlaceCount", reflect.TypeOf((*MockGuildExchangeServer)(nil).DelMainPlaceCount), ctx, in)
}

// DelTransWhiteList mocks base method.
func (m *MockGuildExchangeServer) DelTransWhiteList(ctx context.Context, in *WhiteList) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelTransWhiteList", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelTransWhiteList indicates an expected call of DelTransWhiteList.
func (mr *MockGuildExchangeServerMockRecorder) DelTransWhiteList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelTransWhiteList", reflect.TypeOf((*MockGuildExchangeServer)(nil).DelTransWhiteList), ctx, in)
}

// ExchangeV2 mocks base method.
func (m *MockGuildExchangeServer) ExchangeV2(ctx context.Context, in *GuildExchangeTypeReq) (*AllScore, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExchangeV2", ctx, in)
	ret0, _ := ret[0].(*AllScore)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExchangeV2 indicates an expected call of ExchangeV2.
func (mr *MockGuildExchangeServerMockRecorder) ExchangeV2(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExchangeV2", reflect.TypeOf((*MockGuildExchangeServer)(nil).ExchangeV2), ctx, in)
}

// GetAnchorInfo mocks base method.
func (m *MockGuildExchangeServer) GetAnchorInfo(ctx context.Context, in *UidReq) (*AnchorInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorInfo", ctx, in)
	ret0, _ := ret[0].(*AnchorInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorInfo indicates an expected call of GetAnchorInfo.
func (mr *MockGuildExchangeServerMockRecorder) GetAnchorInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorInfo", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetAnchorInfo), ctx, in)
}

// GetApplyStatus mocks base method.
func (m *MockGuildExchangeServer) GetApplyStatus(ctx context.Context, in *UidReq) (*ApplyStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplyStatus", ctx, in)
	ret0, _ := ret[0].(*ApplyStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplyStatus indicates an expected call of GetApplyStatus.
func (mr *MockGuildExchangeServerMockRecorder) GetApplyStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplyStatus", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetApplyStatus), ctx, in)
}

// GetExchangeAmount mocks base method.
func (m *MockGuildExchangeServer) GetExchangeAmount(ctx context.Context, in *UidReq) (*UserAllScoreResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangeAmount", ctx, in)
	ret0, _ := ret[0].(*UserAllScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangeAmount indicates an expected call of GetExchangeAmount.
func (mr *MockGuildExchangeServerMockRecorder) GetExchangeAmount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangeAmount", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetExchangeAmount), ctx, in)
}

// GetExchangeAmountList mocks base method.
func (m *MockGuildExchangeServer) GetExchangeAmountList(ctx context.Context, in *UidOffsetReq) (*UserAllScoreList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangeAmountList", ctx, in)
	ret0, _ := ret[0].(*UserAllScoreList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangeAmountList indicates an expected call of GetExchangeAmountList.
func (mr *MockGuildExchangeServerMockRecorder) GetExchangeAmountList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangeAmountList", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetExchangeAmountList), ctx, in)
}

// GetExchangeAmountListType mocks base method.
func (m *MockGuildExchangeServer) GetExchangeAmountListType(ctx context.Context, in *UidOffsetTypeReq) (*UserAllScoreList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangeAmountListType", ctx, in)
	ret0, _ := ret[0].(*UserAllScoreList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangeAmountListType indicates an expected call of GetExchangeAmountListType.
func (mr *MockGuildExchangeServerMockRecorder) GetExchangeAmountListType(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangeAmountListType", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetExchangeAmountListType), ctx, in)
}

// GetExchangeDetail mocks base method.
func (m *MockGuildExchangeServer) GetExchangeDetail(ctx context.Context, in *GetExchangeDetailReq) (*GetExchangeListRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangeDetail", ctx, in)
	ret0, _ := ret[0].(*GetExchangeListRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangeDetail indicates an expected call of GetExchangeDetail.
func (mr *MockGuildExchangeServerMockRecorder) GetExchangeDetail(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangeDetail", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetExchangeDetail), ctx, in)
}

// GetExchangeHis mocks base method.
func (m *MockGuildExchangeServer) GetExchangeHis(ctx context.Context, in *GetExchangeListReq) (*GetExchangeListRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangeHis", ctx, in)
	ret0, _ := ret[0].(*GetExchangeListRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangeHis indicates an expected call of GetExchangeHis.
func (mr *MockGuildExchangeServerMockRecorder) GetExchangeHis(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangeHis", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetExchangeHis), ctx, in)
}

// GetMainAccountInfoList mocks base method.
func (m *MockGuildExchangeServer) GetMainAccountInfoList(ctx context.Context, in *OaInfoReq) (*OaInfoList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMainAccountInfoList", ctx, in)
	ret0, _ := ret[0].(*OaInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainAccountInfoList indicates an expected call of GetMainAccountInfoList.
func (mr *MockGuildExchangeServerMockRecorder) GetMainAccountInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainAccountInfoList", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetMainAccountInfoList), ctx, in)
}

// GetMainApplyList mocks base method.
func (m *MockGuildExchangeServer) GetMainApplyList(ctx context.Context, in *GetMainApplyListReq) (*GetMainApplyListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMainApplyList", ctx, in)
	ret0, _ := ret[0].(*GetMainApplyListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainApplyList indicates an expected call of GetMainApplyList.
func (mr *MockGuildExchangeServerMockRecorder) GetMainApplyList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainApplyList", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetMainApplyList), ctx, in)
}

// GetMainData mocks base method.
func (m *MockGuildExchangeServer) GetMainData(ctx context.Context, in *UidReq) (*GetMain, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMainData", ctx, in)
	ret0, _ := ret[0].(*GetMain)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainData indicates an expected call of GetMainData.
func (mr *MockGuildExchangeServerMockRecorder) GetMainData(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainData", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetMainData), ctx, in)
}

// GetMainGuilds mocks base method.
func (m *MockGuildExchangeServer) GetMainGuilds(ctx context.Context, in *UidReq) (*GuildList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMainGuilds", ctx, in)
	ret0, _ := ret[0].(*GuildList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainGuilds indicates an expected call of GetMainGuilds.
func (mr *MockGuildExchangeServerMockRecorder) GetMainGuilds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainGuilds", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetMainGuilds), ctx, in)
}

// GetMainHasPlaceCount mocks base method.
func (m *MockGuildExchangeServer) GetMainHasPlaceCount(ctx context.Context, in *UidOffsetReq) (*GetMainListRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMainHasPlaceCount", ctx, in)
	ret0, _ := ret[0].(*GetMainListRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainHasPlaceCount indicates an expected call of GetMainHasPlaceCount.
func (mr *MockGuildExchangeServerMockRecorder) GetMainHasPlaceCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainHasPlaceCount", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetMainHasPlaceCount), ctx, in)
}

// GetMainList mocks base method.
func (m *MockGuildExchangeServer) GetMainList(ctx context.Context, in *UidOffsetReq) (*GetMainListRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMainList", ctx, in)
	ret0, _ := ret[0].(*GetMainListRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainList indicates an expected call of GetMainList.
func (mr *MockGuildExchangeServerMockRecorder) GetMainList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainList", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetMainList), ctx, in)
}

// GetMainOnly mocks base method.
func (m *MockGuildExchangeServer) GetMainOnly(ctx context.Context, in *UidReq) (*GetMain, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMainOnly", ctx, in)
	ret0, _ := ret[0].(*GetMain)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainOnly indicates an expected call of GetMainOnly.
func (mr *MockGuildExchangeServerMockRecorder) GetMainOnly(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainOnly", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetMainOnly), ctx, in)
}

// GetMainPlaceCount mocks base method.
func (m *MockGuildExchangeServer) GetMainPlaceCount(ctx context.Context, in *UidReq) (*GetMain, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMainPlaceCount", ctx, in)
	ret0, _ := ret[0].(*GetMain)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainPlaceCount indicates an expected call of GetMainPlaceCount.
func (mr *MockGuildExchangeServerMockRecorder) GetMainPlaceCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainPlaceCount", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetMainPlaceCount), ctx, in)
}

// GetMasterApplyCount mocks base method.
func (m *MockGuildExchangeServer) GetMasterApplyCount(ctx context.Context, in *UidReq) (*ApplyCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMasterApplyCount", ctx, in)
	ret0, _ := ret[0].(*ApplyCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterApplyCount indicates an expected call of GetMasterApplyCount.
func (mr *MockGuildExchangeServerMockRecorder) GetMasterApplyCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterApplyCount", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetMasterApplyCount), ctx, in)
}

// GetMasterApplyList mocks base method.
func (m *MockGuildExchangeServer) GetMasterApplyList(ctx context.Context, in *ApplyListReq) (*AnchorApplyList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMasterApplyList", ctx, in)
	ret0, _ := ret[0].(*AnchorApplyList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterApplyList indicates an expected call of GetMasterApplyList.
func (mr *MockGuildExchangeServerMockRecorder) GetMasterApplyList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterApplyList", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetMasterApplyList), ctx, in)
}

// GetMasterFreezeTotal mocks base method.
func (m *MockGuildExchangeServer) GetMasterFreezeTotal(ctx context.Context, in *ManualSettlementReq) (*GetMasterFreezeTotalResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMasterFreezeTotal", ctx, in)
	ret0, _ := ret[0].(*GetMasterFreezeTotalResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterFreezeTotal indicates an expected call of GetMasterFreezeTotal.
func (mr *MockGuildExchangeServerMockRecorder) GetMasterFreezeTotal(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterFreezeTotal", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetMasterFreezeTotal), ctx, in)
}

// GetMasterFreezeUser mocks base method.
func (m *MockGuildExchangeServer) GetMasterFreezeUser(ctx context.Context, in *ManualSettlementReq) (*GetMasterFreezeUserResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMasterFreezeUser", ctx, in)
	ret0, _ := ret[0].(*GetMasterFreezeUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterFreezeUser indicates an expected call of GetMasterFreezeUser.
func (mr *MockGuildExchangeServerMockRecorder) GetMasterFreezeUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterFreezeUser", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetMasterFreezeUser), ctx, in)
}

// GetMasterUid mocks base method.
func (m *MockGuildExchangeServer) GetMasterUid(ctx context.Context, in *GuildReq) (*UidRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMasterUid", ctx, in)
	ret0, _ := ret[0].(*UidRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterUid indicates an expected call of GetMasterUid.
func (mr *MockGuildExchangeServerMockRecorder) GetMasterUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterUid", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetMasterUid), ctx, in)
}

// GetSettlementAttribute mocks base method.
func (m *MockGuildExchangeServer) GetSettlementAttribute(ctx context.Context, in *UidTimeRangeReq) (*UserAllScoreList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettlementAttribute", ctx, in)
	ret0, _ := ret[0].(*UserAllScoreList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettlementAttribute indicates an expected call of GetSettlementAttribute.
func (mr *MockGuildExchangeServerMockRecorder) GetSettlementAttribute(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettlementAttribute", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetSettlementAttribute), ctx, in)
}

// GetSettlementBalance mocks base method.
func (m *MockGuildExchangeServer) GetSettlementBalance(ctx context.Context, in *GuildExchangeTypeReq) (*Balance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettlementBalance", ctx, in)
	ret0, _ := ret[0].(*Balance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettlementBalance indicates an expected call of GetSettlementBalance.
func (mr *MockGuildExchangeServerMockRecorder) GetSettlementBalance(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettlementBalance", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetSettlementBalance), ctx, in)
}

// GetUidGuildExchangeData mocks base method.
func (m *MockGuildExchangeServer) GetUidGuildExchangeData(ctx context.Context, in *UidReq) (*UserGuildExchangeData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUidGuildExchangeData", ctx, in)
	ret0, _ := ret[0].(*UserGuildExchangeData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUidGuildExchangeData indicates an expected call of GetUidGuildExchangeData.
func (mr *MockGuildExchangeServerMockRecorder) GetUidGuildExchangeData(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUidGuildExchangeData", reflect.TypeOf((*MockGuildExchangeServer)(nil).GetUidGuildExchangeData), ctx, in)
}

// HandleMainApply mocks base method.
func (m *MockGuildExchangeServer) HandleMainApply(ctx context.Context, in *HandleMainApplyReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleMainApply", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleMainApply indicates an expected call of HandleMainApply.
func (mr *MockGuildExchangeServerMockRecorder) HandleMainApply(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleMainApply", reflect.TypeOf((*MockGuildExchangeServer)(nil).HandleMainApply), ctx, in)
}

// InsertMain mocks base method.
func (m *MockGuildExchangeServer) InsertMain(ctx context.Context, in *SetGuildMainListReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertMain", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertMain indicates an expected call of InsertMain.
func (mr *MockGuildExchangeServerMockRecorder) InsertMain(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertMain", reflect.TypeOf((*MockGuildExchangeServer)(nil).InsertMain), ctx, in)
}

// ManualSettlement mocks base method.
func (m *MockGuildExchangeServer) ManualSettlement(ctx context.Context, in *ManualSettlementReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ManualSettlement", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ManualSettlement indicates an expected call of ManualSettlement.
func (mr *MockGuildExchangeServerMockRecorder) ManualSettlement(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ManualSettlement", reflect.TypeOf((*MockGuildExchangeServer)(nil).ManualSettlement), ctx, in)
}

// ManualSum mocks base method.
func (m *MockGuildExchangeServer) ManualSum(ctx context.Context, in *ManualSumReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ManualSum", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ManualSum indicates an expected call of ManualSum.
func (mr *MockGuildExchangeServerMockRecorder) ManualSum(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ManualSum", reflect.TypeOf((*MockGuildExchangeServer)(nil).ManualSum), ctx, in)
}

// MasterBatchExaApply mocks base method.
func (m *MockGuildExchangeServer) MasterBatchExaApply(ctx context.Context, in *MasterBatchExaApplyReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MasterBatchExaApply", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MasterBatchExaApply indicates an expected call of MasterBatchExaApply.
func (mr *MockGuildExchangeServerMockRecorder) MasterBatchExaApply(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterBatchExaApply", reflect.TypeOf((*MockGuildExchangeServer)(nil).MasterBatchExaApply), ctx, in)
}

// MasterGetContractFinishedList mocks base method.
func (m *MockGuildExchangeServer) MasterGetContractFinishedList(ctx context.Context, in *OffsetValidReq) (*ContractList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MasterGetContractFinishedList", ctx, in)
	ret0, _ := ret[0].(*ContractList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MasterGetContractFinishedList indicates an expected call of MasterGetContractFinishedList.
func (mr *MockGuildExchangeServerMockRecorder) MasterGetContractFinishedList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterGetContractFinishedList", reflect.TypeOf((*MockGuildExchangeServer)(nil).MasterGetContractFinishedList), ctx, in)
}

// MasterGetContractList mocks base method.
func (m *MockGuildExchangeServer) MasterGetContractList(ctx context.Context, in *UidOffsetReq) (*AnchorApplyList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MasterGetContractList", ctx, in)
	ret0, _ := ret[0].(*AnchorApplyList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MasterGetContractList indicates an expected call of MasterGetContractList.
func (mr *MockGuildExchangeServerMockRecorder) MasterGetContractList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterGetContractList", reflect.TypeOf((*MockGuildExchangeServer)(nil).MasterGetContractList), ctx, in)
}

// MasterGetMainApplyList mocks base method.
func (m *MockGuildExchangeServer) MasterGetMainApplyList(ctx context.Context, in *MasterGetMainApplyListReq) (*MasterGetMainApplyListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MasterGetMainApplyList", ctx, in)
	ret0, _ := ret[0].(*MasterGetMainApplyListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MasterGetMainApplyList indicates an expected call of MasterGetMainApplyList.
func (mr *MockGuildExchangeServerMockRecorder) MasterGetMainApplyList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterGetMainApplyList", reflect.TypeOf((*MockGuildExchangeServer)(nil).MasterGetMainApplyList), ctx, in)
}

// MasterGetSignUrl mocks base method.
func (m *MockGuildExchangeServer) MasterGetSignUrl(ctx context.Context, in *ApplyIdReq) (*SignUrl, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MasterGetSignUrl", ctx, in)
	ret0, _ := ret[0].(*SignUrl)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MasterGetSignUrl indicates an expected call of MasterGetSignUrl.
func (mr *MockGuildExchangeServerMockRecorder) MasterGetSignUrl(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterGetSignUrl", reflect.TypeOf((*MockGuildExchangeServer)(nil).MasterGetSignUrl), ctx, in)
}

// MasterInsertMainApply mocks base method.
func (m *MockGuildExchangeServer) MasterInsertMainApply(ctx context.Context, in *ExchangeGuildMainApplyData) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MasterInsertMainApply", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MasterInsertMainApply indicates an expected call of MasterInsertMainApply.
func (mr *MockGuildExchangeServerMockRecorder) MasterInsertMainApply(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterInsertMainApply", reflect.TypeOf((*MockGuildExchangeServer)(nil).MasterInsertMainApply), ctx, in)
}

// MasterStartSign mocks base method.
func (m *MockGuildExchangeServer) MasterStartSign(ctx context.Context, in *ApplyIdReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MasterStartSign", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MasterStartSign indicates an expected call of MasterStartSign.
func (mr *MockGuildExchangeServerMockRecorder) MasterStartSign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterStartSign", reflect.TypeOf((*MockGuildExchangeServer)(nil).MasterStartSign), ctx, in)
}

// OfficalBatchExaToPrivate mocks base method.
func (m *MockGuildExchangeServer) OfficalBatchExaToPrivate(ctx context.Context, in *BatchExaApplyReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficalBatchExaToPrivate", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficalBatchExaToPrivate indicates an expected call of OfficalBatchExaToPrivate.
func (mr *MockGuildExchangeServerMockRecorder) OfficalBatchExaToPrivate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficalBatchExaToPrivate", reflect.TypeOf((*MockGuildExchangeServer)(nil).OfficalBatchExaToPrivate), ctx, in)
}

// OfficalGetMainContractData mocks base method.
func (m *MockGuildExchangeServer) OfficalGetMainContractData(ctx context.Context, in *OffsetValidReq) (*ContractListT, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficalGetMainContractData", ctx, in)
	ret0, _ := ret[0].(*ContractListT)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficalGetMainContractData indicates an expected call of OfficalGetMainContractData.
func (mr *MockGuildExchangeServerMockRecorder) OfficalGetMainContractData(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficalGetMainContractData", reflect.TypeOf((*MockGuildExchangeServer)(nil).OfficalGetMainContractData), ctx, in)
}

// OfficalGetMainContractList mocks base method.
func (m *MockGuildExchangeServer) OfficalGetMainContractList(ctx context.Context, in *UidOffsetReq) (*MainContractList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficalGetMainContractList", ctx, in)
	ret0, _ := ret[0].(*MainContractList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficalGetMainContractList indicates an expected call of OfficalGetMainContractList.
func (mr *MockGuildExchangeServerMockRecorder) OfficalGetMainContractList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficalGetMainContractList", reflect.TypeOf((*MockGuildExchangeServer)(nil).OfficalGetMainContractList), ctx, in)
}

// OfficalGetToPrivateList mocks base method.
func (m *MockGuildExchangeServer) OfficalGetToPrivateList(ctx context.Context, in *UidOffsetHandleReq) (*AnchorApplyListT, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficalGetToPrivateList", ctx, in)
	ret0, _ := ret[0].(*AnchorApplyListT)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficalGetToPrivateList indicates an expected call of OfficalGetToPrivateList.
func (mr *MockGuildExchangeServerMockRecorder) OfficalGetToPrivateList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficalGetToPrivateList", reflect.TypeOf((*MockGuildExchangeServer)(nil).OfficalGetToPrivateList), ctx, in)
}

// OnSignDocExpire mocks base method.
func (m *MockGuildExchangeServer) OnSignDocExpire(ctx context.Context, in *OnSignFlowExpireReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnSignDocExpire", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnSignDocExpire indicates an expected call of OnSignDocExpire.
func (mr *MockGuildExchangeServerMockRecorder) OnSignDocExpire(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSignDocExpire", reflect.TypeOf((*MockGuildExchangeServer)(nil).OnSignDocExpire), ctx, in)
}

// OnSignFlowFinish mocks base method.
func (m *MockGuildExchangeServer) OnSignFlowFinish(ctx context.Context, in *OnSignFlowFinishReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnSignFlowFinish", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnSignFlowFinish indicates an expected call of OnSignFlowFinish.
func (mr *MockGuildExchangeServerMockRecorder) OnSignFlowFinish(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSignFlowFinish", reflect.TypeOf((*MockGuildExchangeServer)(nil).OnSignFlowFinish), ctx, in)
}

// OnSignFlowUpdate mocks base method.
func (m *MockGuildExchangeServer) OnSignFlowUpdate(ctx context.Context, in *OnSignFlowUpdateReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnSignFlowUpdate", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnSignFlowUpdate indicates an expected call of OnSignFlowUpdate.
func (mr *MockGuildExchangeServerMockRecorder) OnSignFlowUpdate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSignFlowUpdate", reflect.TypeOf((*MockGuildExchangeServer)(nil).OnSignFlowUpdate), ctx, in)
}

// OnSignMasterDocExpire mocks base method.
func (m *MockGuildExchangeServer) OnSignMasterDocExpire(ctx context.Context, in *OnSignFlowExpireReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnSignMasterDocExpire", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnSignMasterDocExpire indicates an expected call of OnSignMasterDocExpire.
func (mr *MockGuildExchangeServerMockRecorder) OnSignMasterDocExpire(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSignMasterDocExpire", reflect.TypeOf((*MockGuildExchangeServer)(nil).OnSignMasterDocExpire), ctx, in)
}

// OnSignMasterFlowFinish mocks base method.
func (m *MockGuildExchangeServer) OnSignMasterFlowFinish(ctx context.Context, in *OnSignFlowFinishReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnSignMasterFlowFinish", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnSignMasterFlowFinish indicates an expected call of OnSignMasterFlowFinish.
func (mr *MockGuildExchangeServerMockRecorder) OnSignMasterFlowFinish(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSignMasterFlowFinish", reflect.TypeOf((*MockGuildExchangeServer)(nil).OnSignMasterFlowFinish), ctx, in)
}

// OnSignMasterFlowUpdate mocks base method.
func (m *MockGuildExchangeServer) OnSignMasterFlowUpdate(ctx context.Context, in *OnSignFlowUpdateReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnSignMasterFlowUpdate", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnSignMasterFlowUpdate indicates an expected call of OnSignMasterFlowUpdate.
func (mr *MockGuildExchangeServerMockRecorder) OnSignMasterFlowUpdate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSignMasterFlowUpdate", reflect.TypeOf((*MockGuildExchangeServer)(nil).OnSignMasterFlowUpdate), ctx, in)
}

// QueryEncashmentList mocks base method.
func (m *MockGuildExchangeServer) QueryEncashmentList(ctx context.Context, in *QueryEncashmentListReq) (*QueryEncashmentListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryEncashmentList", ctx, in)
	ret0, _ := ret[0].(*QueryEncashmentListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryEncashmentList indicates an expected call of QueryEncashmentList.
func (mr *MockGuildExchangeServerMockRecorder) QueryEncashmentList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryEncashmentList", reflect.TypeOf((*MockGuildExchangeServer)(nil).QueryEncashmentList), ctx, in)
}

// QueryUidAutoExchangeScore mocks base method.
func (m *MockGuildExchangeServer) QueryUidAutoExchangeScore(ctx context.Context, in *QueryUidAutoExchangeScoreReq) (*QueryUidAutoExchangeScoreResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUidAutoExchangeScore", ctx, in)
	ret0, _ := ret[0].(*QueryUidAutoExchangeScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUidAutoExchangeScore indicates an expected call of QueryUidAutoExchangeScore.
func (mr *MockGuildExchangeServerMockRecorder) QueryUidAutoExchangeScore(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUidAutoExchangeScore", reflect.TypeOf((*MockGuildExchangeServer)(nil).QueryUidAutoExchangeScore), ctx, in)
}

// RecycleGuildExchange mocks base method.
func (m *MockGuildExchangeServer) RecycleGuildExchange(ctx context.Context, in *UidReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecycleGuildExchange", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecycleGuildExchange indicates an expected call of RecycleGuildExchange.
func (mr *MockGuildExchangeServerMockRecorder) RecycleGuildExchange(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecycleGuildExchange", reflect.TypeOf((*MockGuildExchangeServer)(nil).RecycleGuildExchange), ctx, in)
}

// SearchApplyListTTid mocks base method.
func (m *MockGuildExchangeServer) SearchApplyListTTid(ctx context.Context, in *SearchApplyListTTidReq) (*AnchorApplyData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchApplyListTTid", ctx, in)
	ret0, _ := ret[0].(*AnchorApplyData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchApplyListTTid indicates an expected call of SearchApplyListTTid.
func (mr *MockGuildExchangeServerMockRecorder) SearchApplyListTTid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchApplyListTTid", reflect.TypeOf((*MockGuildExchangeServer)(nil).SearchApplyListTTid), ctx, in)
}

// SetMainGuild mocks base method.
func (m *MockGuildExchangeServer) SetMainGuild(ctx context.Context, in *MainGuild) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMainGuild", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetMainGuild indicates an expected call of SetMainGuild.
func (mr *MockGuildExchangeServerMockRecorder) SetMainGuild(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMainGuild", reflect.TypeOf((*MockGuildExchangeServer)(nil).SetMainGuild), ctx, in)
}

// SetMainPlaceCount mocks base method.
func (m *MockGuildExchangeServer) SetMainPlaceCount(ctx context.Context, in *SetMainPlaceCountReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMainPlaceCount", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetMainPlaceCount indicates an expected call of SetMainPlaceCount.
func (mr *MockGuildExchangeServerMockRecorder) SetMainPlaceCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMainPlaceCount", reflect.TypeOf((*MockGuildExchangeServer)(nil).SetMainPlaceCount), ctx, in)
}

// SetTransWhiteList mocks base method.
func (m *MockGuildExchangeServer) SetTransWhiteList(ctx context.Context, in *WhiteList) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetTransWhiteList", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetTransWhiteList indicates an expected call of SetTransWhiteList.
func (mr *MockGuildExchangeServerMockRecorder) SetTransWhiteList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTransWhiteList", reflect.TypeOf((*MockGuildExchangeServer)(nil).SetTransWhiteList), ctx, in)
}

// SumAndSettlement mocks base method.
func (m *MockGuildExchangeServer) SumAndSettlement(ctx context.Context, in *ManualSettlementReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SumAndSettlement", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SumAndSettlement indicates an expected call of SumAndSettlement.
func (mr *MockGuildExchangeServerMockRecorder) SumAndSettlement(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SumAndSettlement", reflect.TypeOf((*MockGuildExchangeServer)(nil).SumAndSettlement), ctx, in)
}

// UpdateMain mocks base method.
func (m *MockGuildExchangeServer) UpdateMain(ctx context.Context, in *SetGuildMainListReq) (*EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMain", ctx, in)
	ret0, _ := ret[0].(*EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateMain indicates an expected call of UpdateMain.
func (mr *MockGuildExchangeServerMockRecorder) UpdateMain(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMain", reflect.TypeOf((*MockGuildExchangeServer)(nil).UpdateMain), ctx, in)
}
